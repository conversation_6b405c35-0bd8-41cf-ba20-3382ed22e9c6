import{z as t}from"./index-r0dFAfgr.js";const i=a=>t({url:"/api/sm/maintainTask",method:"get",params:a}),e=a=>t({url:"/api/sm/maintainTask",method:"post",data:a}),m=a=>t({url:"/api/sm/maintainTask",method:"delete",data:a}),r=(a,s)=>t({url:`/api/sm/maintainTask/${a}/assign`,method:"post",data:s}),o=a=>t({url:"/api/sm/maintainTaskItem",method:"get",params:a});export{e as A,m as D,i as G,r as a,o as b};
