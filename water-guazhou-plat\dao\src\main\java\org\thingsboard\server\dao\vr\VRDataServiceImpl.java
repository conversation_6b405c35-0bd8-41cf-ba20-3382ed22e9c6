/**
 * Copyright © 2016-2019 The Thingsboard Authors
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.vr;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.VRData;
import org.thingsboard.server.dao.model.sql.VideoEntity;
import org.thingsboard.server.dao.sql.VRDataMapper;

@Slf4j
@Service
public class VRDataServiceImpl implements VRDataService {


    @Autowired
    private VRDataMapper vrDataMapper;

    @Override
    public VRData save(VRData vrData) {
        vrDataMapper.updateById(vrData);

        return vrData;
    }

    @Override
    public VRData getById(String id) {
        return vrDataMapper.selectList(new QueryWrapper<>()).get(0);
    }
}
