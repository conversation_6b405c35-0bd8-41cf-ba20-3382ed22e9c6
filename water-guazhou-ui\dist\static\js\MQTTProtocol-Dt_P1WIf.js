import{_ as U}from"./CardTable-rdWOL4_6.js";import{_ as J}from"./CardSearch-CB_HNR-Q.js";import{d as N,g as m,h as v,F as p,q as r,G as d,J as w,bK as P,C as L,M as E,b7 as A,bM as j,bq as Q,ca as G,ak as _,cb as O,s as g,j as R,u as z,b as q,cc as H,ay as y,n as D,p as l,aB as K,aJ as X,aw as W,bh as Y,bw as T,an as x,al as Z,H as $}from"./index-r0dFAfgr.js";import{x as b}from"./xlsx-rVJkW9yq.js";import{s as tt,f as et,i as ot,j as I,k as at,l as lt}from"./index-BggOjNGp.js";import it from"./dialog-DYfJ1HfJ.js";import{v as st,a as nt}from"./variableDialog-BU6B0mQZ.js";import{i as rt}from"./importControl-BvALbT5Z.js";/* empty css                                                                     */import{l as ct}from"./lodash.default-B3JdLn1L.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";import"./formValidate-U0WTqY4Y.js";import"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import"./KeyValueTable-yppKjcF3.js";import"./reduce-BbPixnH6.js";import"./padStart-BKfyZZDO.js";import"./_baseExtremum-UssVWohW.js";import"./_baseLt-svgXHEqw.js";import"./sortBy-DDhdj0i5.js";import"./max-CCqK09y5.js";import"./minBy-DBQvPu-j.js";import"./_baseSum-Cz9yialR.js";import"./min-ks0CS-3r.js";import"./sumBy-Dpy7mNiE.js";const pt=N({__name:"ImportJsonButton",props:{config:{}},setup(t){const e=t,i=(s,o)=>{const a=new FileReader;a.readAsText(s.raw,"UTF-8"),a.onload=async function(c){const h=JSON.parse(c.target.result);new window.FormData().append("file",h),e.config.click&&e.config.click(h)}};return(s,o)=>{const a=w,c=P;return m(),v(c,{ref:"uploadFile",class:"deviceImportBtn",action:"https://run.mocky.io/v3/9d059bf9-4660-45f2-925d-ce80ad6c4d15","show-file-list":!1,"on-change":i,"auto-upload":!1,accept:".json"},{default:p(()=>[r(a,{class:"ml-3",type:"default",plain:""},{default:p(()=>o[0]||(o[0]=[d(" 导入JSON ")])),_:1})]),_:1},512)}}}),dt=L(pt,[["__scopeId","data-v-6d044523"]]),{$btnPerms:k,$message:f,$confirm:mt}=E(),ft={name:"DTUProtocol",components:{tpDialog:it,variableDialog:st},setup(){return{Refresh:A,Edit:j,Delete:Q,Upload:G,Plus:_,DocumentAdd:O}},data(){return{cardSearchConfig:{filters:[{label:"搜索",field:"name",type:"input"},{type:"btn-group",btns:[{perm:!0,text:"查询",svgIcon:g(Z),click:t=>this.clickFilterData(t.name)},{text:"当前页变量增改",perm:k("MQTTVariableAdd"),svgIcon:g(_),click:()=>this.editProtocolVariable()},{text:"导入",perm:k("MQTTVariableImport"),click:t=>{console.log(t),this.cardTableConfig.dataList=t[0].protocol},component:g(dt)},{text:"保存",perm:!0,svgIcon:g(_),click:()=>{const t=this.currentTemplate;t.protocolList=this.cardTableConfig.dataList,tt(t).then(()=>{f.success("保存成功")})}}]}]},cardTableConfig:{loading:!1,dataList:[],indexVisible:!0,columns:[{prop:"name",label:"变量名称",width:80},{prop:"propertyCategory",label:"实际变量名称(非中文)",width:180},{prop:"statType",label:"统计类型",formatter:t=>this.valueTextMap.statType[t.statType]},{prop:"propertyType",label:"变量类型",width:140,formatter:t=>this.valueTextMap.propertyType[t.propertyType]},{prop:"unit",label:"单位"},{prop:"sampleDeviation",label:"每小时数据偏差值",width:140},{prop:"dataOffset",label:"数据偏移量",width:100},{prop:"samplingMax",label:"采样最大值",width:120,textColor:"#FF5722"},{prop:"samplingMin",label:"采样最小值",width:120,textColor:"#33AB9F"},{prop:"sampleCoef",label:"采样系数",width:100,textColor:"#FFB800"},{prop:"unitCoef",label:"数据小数位",width:100},{prop:"range",label:"量程"},{prop:"formulaProperty",label:"公式"}],pagination:{page:1,limit:15,total:0,layout:"total, prev, pager, next, jumper",handleSize:()=>{},handlePage:t=>this.handleCurrentChange(t)}},actionUrl:"",filterTemplate:"",templateList:[],dialogInfo:{currentTitle:"添加协议模板",visible:!1,template:{},type:"MQTT",data:[],close:()=>this.dialogInfo.visible=!1},uploadDis:!1,headers:{},currentTemplate:{},varDialogInfo:{currentTitle:"添加变量",visible:!1,template:{},protocolList:[],prototypeData1:[],prototypeData2:[],filtered:[],close:()=>this.varDialogInfo.visible=!1},protocolData:[],copyInfo:null,currentItem:{},valueTextMap:nt}},computed:{fTemplateList(){return this.templateList.filter(t=>t.name.toLowerCase().includes(this.filterTemplate.toLowerCase()))}},created(){this.actionUrl=R().actionUrl+"/api/deviceTemplate/import/MQTT",this.getTemlate(),this.headers["X-Authorization"]="Bearer "+z().token},methods:{getTemlate(){et("MQTT").then(t=>{if(this.templateList=t.data,this.dialogInfo.data=t.data,this.templateList.length>0){const e=this.currentItem.id?this.currentItem:this.templateList[0];this.clickTemplate(e)}else this.filterProtocolData=[],this.cardTableConfig.pagination.total=this.filterProtocolData.length,this.protocolData=[],this.cardTableConfig.dataList=[],q.warning("暂无数据"),this.$forceUpdate()})},clickTemplate(t){this.currentItem=t,ot(t.id).then(e=>{if(this.currentTemplate=e.data,this.protocolData=[],this.cardTableConfig.pagination.page=1,this.currentTemplate.protocolList)for(const[i,s]of this.currentTemplate.protocolList.entries())s.indexNumber=i+1,this.protocolData.push(s);this.filterProtocolData=this.protocolData,this.cardTableConfig.pagination.total=this.filterProtocolData.length,this.cardTableConfig.dataList=this.filterProtocolData.slice(0,15)})},exportTemplate(){I(this.currentTemplate.id).then(t=>{const e=t.data;e[0].protocol=JSON.parse(t.data[0].protocol||"[]");const i=JSON.stringify(e);H(i,"export.json")})},exportExcel(){I(this.currentTemplate.id).then(t=>{const e=b.utils.json_to_sheet(JSON.parse(t.data[0].protocol||"[]")),i=b.utils.book_new();b.utils.book_append_sheet(i,e,"data"),b.writeFile(i,"test.xlsx")})},uploadSuccess(t){f(t.message),this.getTemlate()},handleChange(){},addTemplate(){this.dialogInfo.visible=!0,this.dialogInfo.currentTitle="添加协议模板";const t={name:"",type:"",remark:"",additionalInfo:""};this.dialogInfo.template=t},editTemplate(t){this.dialogInfo.visible=!0,this.dialogInfo.currentTitle="编辑协议模板";for(const e in t)this.dialogInfo.template[e]=t[e]},copyTemplate(t){t&&(this.currentTemplate=t),at(this.currentTemplate.id).then(e=>{f(e.data.result),this.getTemlate()})},async refreshData(){this.getTemlate()},deleteTemplate(t){const e="确定要删除"+t.name+"吗?";mt(e,"删除提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{lt(t.id).then(()=>{f({type:"success",message:"删除成功"}),this.currentItem={},this.getTemlate()}).catch(i=>{const s=i.data.message;f(s)})}).catch(i=>{console.log(i)})},editProtocolVariable(){if(this.varDialogInfo.template=this.currentTemplate,this.varDialogInfo.protocolList=this.cardTableConfig.dataList,this.protocolData.length!==this.filterProtocolData.length){const t=ct.pullAll(this.protocolData,this.filterProtocolData);this.varDialogInfo.filtered=t}if(this.filterProtocolData.length>15)if(this.cardTableConfig.pagination.page===1)this.varDialogInfo.prototypeData1=[],this.varDialogInfo.prototypeData2=this.filterProtocolData.slice(15,this.filterProtocolData.length);else{const t=this.cardTableConfig.pagination.page*15-15;this.varDialogInfo.prototypeData1=this.filterProtocolData.slice(0,t),this.varDialogInfo.prototypeData2=this.filterProtocolData.slice(this.cardTableConfig.pagination.page*15,this.filterProtocolData.length)}else this.varDialogInfo.prototypeData1=[],this.varDialogInfo.prototypeData2=[];this.varDialogInfo.visible=!0,this.varDialogInfo.currentTitle="协议变量操作"},clickFilterData(t){this.filterProtocolData=this.protocolData.filter(e=>e.name.toLowerCase().includes(t.toLowerCase())),this.cardTableConfig.pagination.total=this.filterProtocolData.length,this.cardTableConfig.dataList=this.filterProtocolData.slice(0,15)},handleVarChange(){},async uploadFile(t){this.uploadDis=!0,rt(this,t,"MQTT",this.currentTemplate)},handleCurrentChange(t){this.cardTableConfig.pagination.page=t,this.cardTableConfig.dataList=this.filterProtocolData.slice(t*15-15,[t*15])}}},ht={class:"template-protocol-container"},ut={class:"left-template-box custom-bg-box-shadow1"},gt={class:"operation-btns"},Tt={class:"template-list-box"},bt=["onClick"],_t={class:"t-item-name"},Dt={class:"hover-button"},vt=["onClick"],Ct=["onClick"],yt=["onClick"],xt=["onClick"],It={class:"right-protocol-box custom-main-bg-box-shadow1"};function kt(t,e,i,s,o,a){const c=w,h=$,C=P,F=J,M=U,S=y("tpDialog"),B=y("variableDialog");return m(),D("div",ht,[l("div",ut,[e[10]||(e[10]=l("div",{class:"title-box custom-bottom-box-shadow"},[l("p",{class:"title-text top-title"},[l("i",{class:"iconfont icon-shuangjiantouyou"}),d("模板列表 ")])],-1)),r(h,{modelValue:o.filterTemplate,"onUpdate:modelValue":e[1]||(e[1]=n=>o.filterTemplate=n),placeholder:"输入模板名称搜索",class:"tree-filter-create-box"},{append:p(()=>[r(c,{icon:s.Refresh,onClick:e[0]||(e[0]=n=>o.filterTemplate="")},{default:p(()=>e[4]||(e[4]=[d(" 重置 ")])),_:1},8,["icon"])]),_:1},8,["modelValue"]),r(c,{class:"tree-o-btn add-blue-green",icon:s.DocumentAdd,onClick:a.addTemplate},{default:p(()=>e[5]||(e[5]=[d(" 新建模板 ")])),_:1},8,["icon","onClick"]),l("div",gt,[r(c,{icon:s.Edit,class:"node-o-btn edit-primary-blue",onClick:e[2]||(e[2]=n=>a.editTemplate(o.currentItem))},{default:p(()=>e[6]||(e[6]=[d(" 编辑 ")])),_:1},8,["icon"]),r(C,{class:"upload-demo",action:o.actionUrl,"on-change":a.handleChange,headers:o.headers,"show-file-list":!1,"on-success":a.uploadSuccess},{default:p(()=>[r(c,{class:"node-o-btn add-child-blue",icon:s.Upload},{default:p(()=>e[7]||(e[7]=[d(" 导入 ")])),_:1},8,["icon"])]),_:1},8,["action","on-change","headers","on-success"]),r(c,{icon:s.Delete,class:"node-o-btn delete-orange",onClick:e[3]||(e[3]=n=>a.deleteTemplate(o.currentItem))},{default:p(()=>e[8]||(e[8]=[d(" 删除 ")])),_:1},8,["icon"])]),e[11]||(e[11]=l("span",{class:"alltemp"},"所有模板",-1)),l("div",Tt,[(m(!0),D(K,null,X(a.fTemplateList,(n,V)=>(m(),D("div",{key:V,class:W(["t-item",{"active-item":n.id===o.currentTemplate.id}]),onClick:u=>a.clickTemplate(n)},[l("p",_t,[e[9]||(e[9]=l("i",{class:"iconfont icon-moban"},null,-1)),d(" "+Y(n.name),1)]),l("span",Dt,[l("i",{class:"el-icon-edit",style:{color:"#0a81ff"},onClick:T(u=>a.editTemplate(n),["stop"])},null,8,vt),l("i",{class:"iconfont icon-daochu1",style:{color:"#32d1db"},onClick:T(u=>a.exportTemplate(n),["stop"])},null,8,Ct),l("i",{class:"iconfont icon-download",style:{color:"#32d1db"},onClick:T(u=>a.exportExcel(n),["stop"])},null,8,yt),l("i",{class:"el-icon-delete",style:{color:"#f56c6c"},onClick:T(u=>a.deleteTemplate(n),["stop"])},null,8,xt)])],10,bt))),128))])]),l("div",It,[r(F,{ref:"cardSearch",config:o.cardSearchConfig},null,8,["config"]),r(M,{config:o.cardTableConfig,class:"card-table"},null,8,["config"])]),o.dialogInfo.visible?(m(),v(S,{key:0,"dialog-info":o.dialogInfo,onGetTemlate:a.getTemlate},null,8,["dialog-info","onGetTemlate"])):x("",!0),o.varDialogInfo.visible?(m(),v(B,{key:1,"var-dialog-info":o.varDialogInfo,onGetTemlate:a.getTemlate},null,8,["var-dialog-info","onGetTemlate"])):x("",!0)])}const Zt=L(ft,[["render",kt],["__scopeId","data-v-bc5fcc96"]]);export{Zt as default};
