<!--

    Copyright © 2016-2019 The Thingsboard Authors

    Licensed under the Apache License, Version 2.0 (the "License");
    you may not use this file except in compliance with the License.
    You may obtain a copy of the License at

        http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.

-->
<assembly xmlns="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.3"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.3 http://maven.apache.org/xsd/assembly-1.1.3.xsd">
    <id>windows</id>

    <formats>
        <format>zip</format>
    </formats>

    <!-- Workaround to create logs directory -->
    <fileSets>
        <fileSet>
            <directory>${pkg.win.dist}</directory>
            <outputDirectory>logs</outputDirectory>
            <excludes>
                <exclude>*/**</exclude>
            </excludes>
        </fileSet>
        <fileSet>
            <directory>${pkg.win.dist}/conf</directory>
            <outputDirectory>conf</outputDirectory>
            <lineEnding>windows</lineEnding>
        </fileSet>
    </fileSets>

    <files>
        <file>
            <source>${project.build.directory}/${project.build.finalName}-boot.${project.packaging}</source>
            <outputDirectory>lib</outputDirectory>
            <destName>${pkg.name}.jar</destName>
        </file>
        <file>
            <source>${pkg.win.dist}/service.exe</source>
            <outputDirectory/>
            <destName>${pkg.name}.exe</destName>
        </file>
        <file>
            <source>${pkg.win.dist}/service.xml</source>
            <outputDirectory/>
            <destName>${pkg.name}.xml</destName>
            <lineEnding>windows</lineEnding>
        </file>
        <file>
            <source>${pkg.win.dist}/install.bat</source>
            <outputDirectory/>
            <lineEnding>windows</lineEnding>
        </file>
        <file>
            <source>${pkg.win.dist}/uninstall.bat</source>
            <outputDirectory/>
            <lineEnding>windows</lineEnding>
        </file>
    </files>
</assembly>
