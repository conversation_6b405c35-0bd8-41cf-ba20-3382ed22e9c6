package org.thingsboard.server.dao.model.sql.maintainCircuit;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 班组主表
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-09-07
 */
@TableName("tb_maintain_circuit_team_m")
@Data
public class MaintainCircuitTeamM {

    @TableId
    private String id;

    private String type;

    private String name;

    private String remark;

    private String creator;

    private transient String creatorName;

    private Date createTime;

    private String tenantId;

    private transient List<MaintainCircuitTeamC> maintainCircuitTeamCList;
}
