"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[5235],{15235:(e,t,i)=>{i.r(t),i.d(t,{default:()=>P});var r=i(43697),o=i(99880),a=i(20102),s=i(96674),n=i(22974),l=i(83379),p=i(70586),c=i(17452),d=i(5600),u=i(75215),h=i(71715),m=i(52011),g=i(6570),y=i(65587),b=i(15923),v=i(92604),w=i(90578);i(67676);let f=class extends b.Z{constructor(e){super(e),this.portalItem=null}normalizeCtorArgs(e){return e&&e.portalItem&&e.path?{...e,path:this._normalizePath(e.path,e.portalItem)}:e}set path(e){(0,p.pC)(e)&&(0,c.YP)(e)?v.Z.getLogger(this.declaredClass).error("portalitemresource:invalid-path","A portal item resource path must be relative"):this._set("path",e)}_castPath(e){return this._normalizePath(e,this.portalItem)}get url(){return this.portalItem&&this.path?`${this.portalItem.itemUrl}/resources/${this.path}`:null}get itemRelativeUrl(){return this.portalItem&&this.path?`./resources/${this.path}`:null}fetch(e="json",t){const i=this.url;if((0,p.Wi)(i))throw new a.Z("portal-item-resource:fetch","Portal item resource does not refer to a valid item or path");return this.portalItem.portal.request(i,{responseType:e,query:{token:this.portalItem.apiKey},signal:(0,p.U2)(t,"signal")})}async update(e,t){return(await i.e(7873).then(i.bind(i,97873))).addOrUpdateResource(this,"update",e,t)}hasPath(){return(0,p.pC)(this.path)}_normalizePath(e,t){return(0,p.Wi)(e)?e:(e=e.replace(/^\/+/,""),(0,p.pC)(t)&&(0,c.YP)(e)&&(e=(0,c.PF)(e,t.itemUrl)),e?.replace(/^\/+/,"").replace(/^(\.\/)?resources\//,""))}};(0,r._)([(0,d.Cb)()],f.prototype,"portalItem",void 0),(0,r._)([(0,d.Cb)({type:String,value:null})],f.prototype,"path",null),(0,r._)([(0,w.p)("path")],f.prototype,"_castPath",null),(0,r._)([(0,d.Cb)({type:String,readOnly:!0})],f.prototype,"url",null),(0,r._)([(0,d.Cb)({type:String,readOnly:!0})],f.prototype,"itemRelativeUrl",null),f=(0,r._)([(0,m.j)("esri.portal.PortalItemResource")],f);const k=f;let C=class extends b.Z{constructor(e){super(e),this.created=null,this.rating=null}};(0,r._)([(0,d.Cb)()],C.prototype,"created",void 0),(0,r._)([(0,d.Cb)()],C.prototype,"rating",void 0),C=(0,r._)([(0,m.j)("esri.portal.PortalRating")],C);const S=C;var _;const I=new Set(["Map Service","Feature Service","Feature Collection","Scene Service","Image Service","Stream Service","Vector Tile Service","GeoJson","CSV","KML","WFS","WMTS","WMS","Feed"]),x=new Set(["KML","GeoJson","CSV"]);let U=_=class extends((0,s.eC)(l.Z)){static from(e){return(0,u.TJ)(_,e)}constructor(e){super(e),this.access=null,this.accessInformation=null,this.apiKey=null,this.applicationProxies=null,this.avgRating=null,this.categories=null,this.created=null,this.culture=null,this.description=null,this.extent=null,this.groupCategories=null,this.id=null,this.isOrgItem=!1,this.itemControl=null,this.licenseInfo=null,this.modified=null,this.name=null,this.numComments=null,this.numRatings=null,this.numViews=null,this.owner=null,this.ownerFolder=null,this.portal=null,this.screenshots=null,this.size=null,this.snippet=null,this.sourceJSON=null,this.sourceUrl=null,this.spatialReference=null,this.tags=null,this.title=null,this.type=null,this.typeKeywords=null,this.url=null}destroy(){this.portal=null}get displayName(){const e=this.type,t=this.typeKeywords||[];let i=e;return"Feature Service"===e||"Feature Collection"===e?i=t.includes("Table")?"Table":t.includes("Route Layer")?"Route Layer":t.includes("Markup")?"Markup":"Feature Layer":"Image Service"===e?i=t.includes("Elevation 3D Layer")?"Elevation Layer":t.includes("Tiled Imagery")?"Tiled Imagery Layer":"Imagery Layer":"Scene Service"===e?i="Scene Layer":"Video Service"===e?i="Video Layer":"Scene Package"===e?i="Scene Layer Package":"Stream Service"===e?i="Feature Layer":"Geoprocessing Service"===e&&this.portal&&this.portal.isPortal?i=t.includes("Web Tool")?"Tool":"Geoprocessing Service":"Geocoding Service"===e?i="Locator":"Geoenrichment Service"===e?i="GeoEnrichment Service":"Microsoft Powerpoint"===e?i="Microsoft PowerPoint":"GeoJson"===e?i="GeoJSON":"Globe Service"===e?i="Globe Layer":"Vector Tile Service"===e?i="Tile Layer":"netCDF"===e?i="NetCDF":"Map Service"===e?i=t.includes("Spatiotemporal")||!t.includes("Hosted Service")&&!t.includes("Tiled")||t.includes("Relational")?"Map Image Layer":"Tile Layer":e&&e.toLowerCase().includes("add in")?i=e.replace(/(add in)/gi,"Add-In"):"datastore catalog service"===e?i="Big Data File Share":"Compact Tile Package"===e?i="Tile Package (tpkx)":"OGCFeatureServer"===e?i="OGC Feature Layer":"web mapping application"===e&&t.includes("configurableApp")?i="Instant App":"Insights Page"===e&&(i="Insights Report"),i}readExtent(e){return e&&e.length?new g.Z(e[0][0],e[0][1],e[1][0],e[1][1]):null}get iconUrl(){const e=this.type&&this.type.toLowerCase()||"",t=this.typeKeywords||[];let i,r=!1,a=!1,s=!1,n=!1,l=!1,p=!1;return e.indexOf("service")>0||"feature collection"===e||"kml"===e||"wms"===e||"wmts"===e||"wfs"===e?(r=t.includes("Hosted Service"),"feature service"===e||"feature collection"===e||"kml"===e||"wfs"===e?(a=t.includes("Table"),s=t.includes("Route Layer"),n=t.includes("Markup"),l=t.includes("Spatiotemporal"),p=t.includes("UtilityNetwork"),i=l&&a?"spatiotemporaltable":a?"table":s?"routelayer":n?"markup":l?"spatiotemporal":r?"featureshosted":p?"utilitynetwork":"features"):i="map service"===e||"wms"===e||"wmts"===e?r||t.includes("Tiled")||"wmts"===e?"maptiles":"mapimages":"scene service"===e?t.includes("Line")?"sceneweblayerline":t.includes("3DObject")?"sceneweblayermultipatch":t.includes("Point")?"sceneweblayerpoint":t.includes("IntegratedMesh")?"sceneweblayermesh":t.includes("PointCloud")?"sceneweblayerpointcloud":t.includes("Polygon")?"sceneweblayerpolygon":t.includes("Building")?"sceneweblayerbuilding":t.includes("Voxel")?"sceneweblayervoxel":"sceneweblayer":"image service"===e?t.includes("Elevation 3D Layer")?"elevationlayer":t.includes("Tiled Imagery")?"tiledimagerylayer":"imagery":"stream service"===e?"streamlayer":"video service"===e?"mediaservice":"vector tile service"===e?"vectortile":"datastore catalog service"===e?"datastorecollection":"geocoding service"===e?"geocodeservice":"geoprocessing service"===e?t.includes("Web Tool")&&this.portal&&this.portal.isPortal?"tool":"layers":"geodata service"===e?"geodataservice":"layers"):i="web map"===e||"cityengine web scene"===e?"maps":"web scene"===e?t.includes("ViewingMode-Local")?"webscenelocal":"websceneglobal":"web mapping application"===e&&t.includes("configurableApp")?"instantapps":"web mapping application"===e||"mobile application"===e||"application"===e||"operation view"===e||"desktop application"===e?"apps":"map document"===e||"map package"===e||"published map"===e||"scene document"===e||"globe document"===e||"basemap package"===e||"mobile basemap package"===e||"mobile map package"===e||"project package"===e||"project template"===e||"pro map"===e||"layout"===e||"layer"===e&&t.includes("ArcGIS Pro")||"explorer map"===e&&t.indexOf("Explorer Document")?"mapsgray":"service definition"===e||"csv"===e||"shapefile"===e||"cad drawing"===e||"geojson"===e||"360 vr experience"===e||"netcdf"===e||"administrative report"===e?"datafiles":"explorer add in"===e||"desktop add in"===e||"windows viewer add in"===e||"windows viewer configuration"===e?"appsgray":"arcgis pro add in"===e||"arcgis pro configuration"===e?"addindesktop":"rule package"===e||"file geodatabase"===e||"sqlite geodatabase"===e||"csv collection"===e||"kml collection"===e||"windows mobile package"===e||"map template"===e||"desktop application template"===e||"gml"===e||"arcpad package"===e||"code sample"===e||"form"===e||"document link"===e||"earth configuration"===e||"operations dashboard add in"===e||"rules package"===e||"image"===e||"workflow manager package"===e||"explorer map"===e&&t.includes("Explorer Mapping Application")||t.includes("Document")?"datafilesgray":"network analysis service"===e||"geoprocessing service"===e||"geodata service"===e||"geometry service"===e||"geoprocessing package"===e||"locator package"===e||"geoprocessing sample"===e||"workflow manager service"===e?"toolsgray":"layer"===e||"layer package"===e||"explorer layer"===e?"layersgray":"scene package"===e?"scenepackage":"mobile scene package"===e?"mobilescenepackage":"tile package"===e||"compact tile package"===e?"tilepackage":"task file"===e?"taskfile":"report template"===e?"report-template":"statistical data collection"===e?"statisticaldatacollection":"insights workbook"===e?"workbook":"insights model"===e?"insightsmodel":"insights page"===e?"insightspage":"insights theme"===e?"insightstheme":"hub initiative"===e?"hubinitiative":"hubpage"===e?"hubpage":"hub event"===e?"hubevent":"hub site application"===e?"hubsite":"hub project"===e?"hubproject":"relational database connection"===e?"relationaldatabaseconnection":"big data file share"===e?"datastorecollection":"image collection"===e?"imagecollection":"style"===e?"style":"desktop style"===e?"desktopstyle":"dashboard"===e?"dashboard":"raster function template"===e?"rasterprocessingtemplate":"vector tile package"===e?"vectortilepackage":"ortho mapping project"===e?"orthomappingproject":"ortho mapping template"===e?"orthomappingtemplate":"solution"===e?"solutions":"geopackage"===e?"geopackage":"deep learning package"===e?"deeplearningpackage":"real time analytic"===e?"realtimeanalytics":"big data analytic"===e?"bigdataanalytics":"feed"===e?"feed":"excalibur imagery project"===e?"excaliburimageryproject":"notebook"===e?"notebook":"storymap"===e?"storymap":"survey123 add in"===e?"survey123addin":"mission"===e?"mission":"mission report"===e?"missionreport":"quickcapture project"===e?"quickcaptureproject":"pro report"===e?"proreport":"pro report template"===e?"proreporttemplate":"urban model"===e?"urbanmodel":"web experience"===e?"experiencebuilder":"web experience template"===e?"webexperiencetemplate":"experience builder widget"===e?"experiencebuilderwidget":"experience builder widget package"===e?"experiencebuilderwidgetpackage":"workflow"===e?"workflow":"insights script"===e?"insightsscript":"kernel gateway connection"===e?"kernelgatewayconnection":"hub initiative template"===e?"hubinitiativetemplate":"storymap theme"===e?"storymaptheme":"knowledge graph"===e?"knowledgegraph":"native application"===e?"nativeapp":"native application installer"===e?"nativeappinstaller":"link chart"===e?"linkchart":"investigation"===e?"investigation":"ogcfeatureserver"===e?"features":"pro project"===e?"proproject":"insights workbook package"===e?"insightsworkbookpackage":"apache parquet"===e?"apacheparquet":"notebook code snippets"===e||"notebook code snippet library"===e?"notebookcodesnippets":"suitability model"===e?"suitabilitymodel":"esri classifier definition"===e?"classifierdefinition":"esri classification schema"===e?"classificationschema":"insights data engineering workbook"===e?"dataengineeringworkbook":"insights data engineering model"===e?"dataengineeringmodel":"deep learning studio project"===e?"deeplearningproject":"discussion"===e?"discussion":"allsource project"===e?"allsourceproject":"api key"===e?"apikey":"maps",i?(0,o.V)("esri/images/portal/"+i+"16.png"):null}get isLayer(){return null!=this.type&&I.has(this.type)}get itemPageUrl(){const e=this.portal?.itemPageUrl;return e&&this.id?`${e}?id=${this.id}`:null}get itemUrl(){const e=this.portal?.restUrl;return e&&this.id?`${e}/content/items/${this.id}`:null}get thumbnailUrl(){const e=this.itemUrl,t=this.thumbnail;return e&&t?this.portal?.normalizeUrl(`${e}/info/${t}?f=json`)??null:null}get userItemUrl(){const e=this.get("portal.restUrl");if(!e)return null;const t=this.owner||this.get("portal.user.username");return t?`${e}/content/users/${this.ownerFolder?`${t}/${this.ownerFolder}`:t}/items/${this.id}`:null}load(e){const t=this.portal??(this.portal=y.Z.getDefault()),i=t.load(e).then((()=>this.sourceJSON?this.sourceJSON:this.id&&this.itemUrl?t.request(this.itemUrl,{signal:(0,p.pC)(e)?e.signal:null,query:{token:this.apiKey}}):{})).then((e=>{this.sourceJSON=e,this.read(e)}));return this.addResolvingPromise(i),Promise.resolve(this)}async addRating(e){const t={method:"post",query:{}};return e instanceof S&&(e=e.rating),null==e||isNaN(e)||"number"!=typeof e||(t.query.rating=e),this.portal?(await this.portal.request(this.itemUrl+"/addRating",t),new S({rating:e,created:new Date})):null}clone(){const e={access:this.access,accessInformation:this.accessInformation,applicationProxies:(0,n.d9)(this.applicationProxies),avgRating:this.avgRating,categories:(0,n.d9)(this.categories),created:(0,n.d9)(this.created),culture:this.culture,description:this.description,extent:(0,n.d9)(this.extent),groupCategories:(0,n.d9)(this.groupCategories),id:this.id,itemControl:this.itemControl,licenseInfo:this.licenseInfo,modified:(0,n.d9)(this.modified),name:this.name,numComments:this.numComments,numRatings:this.numRatings,numViews:this.numViews,owner:this.owner,ownerFolder:this.ownerFolder,portal:this.portal,screenshots:(0,n.d9)(this.screenshots),size:this.size,snippet:this.snippet,sourceUrl:this.sourceUrl,spatialReference:this.spatialReference,tags:(0,n.d9)(this.tags),thumbnail:this.thumbnail,title:this.title,type:this.type,typeKeywords:(0,n.d9)(this.typeKeywords),url:this.url};this.loaded&&(e.loadStatus="loaded");const t=new _({sourceJSON:this.sourceJSON}).set(e);return t._set("isOrgItem",this.isOrgItem),t}createPostQuery(){const e=this.toJSON();for(const t of["tags","typeKeywords","categories"])e[t]&&(e[t]=e[t].join(", "));const{extent:t}=e;return t&&(e.extent=JSON.stringify(t)),e}async deleteRating(){await(0,p.s3)(this.portal).request(this.itemUrl+"/deleteRating",{method:"post"})}fetchData(e="json",t){return(0,p.s3)(this.portal).request(this.itemUrl+"/data",{responseType:e,...t,query:{token:this.apiKey}})}async fetchRating(e){const t=await(0,p.s3)(this.portal).request(this.itemUrl+"/rating",{query:{token:this.apiKey},...e});return null!=t.rating?(t.created=new Date(t.created),new S(t)):null}fetchRelatedItems(e,t){return(0,p.s3)(this.portal).requestToTypedArray(this.itemUrl+"/relatedItems",{query:{...e,token:this.apiKey},...t},_)}getThumbnailUrl(e){let t=this.thumbnailUrl;return t&&e&&(t+=`&w=${e}`),t}reload(){return(0,p.s3)(this.portal).request(this.itemUrl??"",{cacheBust:!0,query:{token:this.apiKey}}).then((e=>(this.sourceJSON=e,this.read(e),this)))}update(e){return this.id?this.load().then((()=>(0,p.s3)(this.portal).signIn())).then((()=>{const t=e&&e.data,i={method:"post"};i.query=this.createPostQuery();for(const e in i.query)null===i.query[e]&&(i.query[e]="");return i.query.clearEmptyFields=!0,null!=t&&("string"==typeof t?i.query.text=t:"object"==typeof t&&(i.query.text=JSON.stringify(t))),this.portal.request(`${this.userItemUrl}/update`,i).then((()=>this.reload()))})):Promise.reject(new a.Z("portal:item-does-not-exist","The item does not exist yet and cannot be updated"))}async copy(e){if(!this.id)throw new a.Z("portal:item-does-not-exist","The item does not exist yet");await this.load();const{portal:t,itemUrl:i}=this;await(0,p.s3)(t).signIn();const{copyResources:r,folder:o,tags:s,title:n}=e||{},l={method:"post",query:{copyPrivateResources:"all"===r,folder:"string"==typeof o?o:o?.id,includeResources:!!r,tags:s?.join(","),title:n}},{itemId:c}=await t.request(`${i}/copy`,l);return new _({id:c,portal:t})}updateThumbnail(e){return this.id?this.load().then((()=>this.portal.signIn())).then((()=>{const t=e.thumbnail,i=e.filename,r={method:"post"};if("string"==typeof t)(0,c.HK)(t)?r.query={data:t}:r.query={url:(0,c.hF)(t)},(0,p.pC)(i)&&(r.query.filename=i);else{const e=new FormData;(0,p.pC)(i)?e.append("file",t,i):e.append("file",t),r.body=e}return this.portal.request(`${this.userItemUrl}/updateThumbnail`,r).then((()=>this.reload()))})):Promise.reject(new a.Z("portal:item-does-not-exist","The item does not exist yet and cannot be updated"))}async fetchResources(e={},t){return(await i.e(7873).then(i.bind(i,97873))).fetchResources(this,e,t)}async addResource(e,t,r){const o=await i.e(7873).then(i.bind(i,97873));return e.portalItem=this,o.addOrUpdateResource(e,"add",t,r)}async removeResource(e,t){const r=await i.e(7873).then(i.bind(i,97873));if(e.portalItem&&e.portalItem.itemUrl!==this.itemUrl)throw new a.Z("removeresource:portal-item-mismatch","The portal item associated with the provided resource does not match the item");return r.removeResource(this,e,t)}async removeAllResources(e){return(await i.e(7873).then(i.bind(i,97873))).removeAllResources(this,e)}resourceFromPath(e){return new k({portalItem:this,path:e})}toJSON(){const e=this.extent,t={accessInformation:this.accessInformation,categories:(0,n.d9)(this.categories),created:this.created&&this.created.getTime(),description:this.description,extent:e&&[[e.xmin,e.ymin],[e.xmax,e.ymax]],id:this.id,isOrgItem:this.isOrgItem,licenseInfo:this.licenseInfo,modified:this.modified&&this.modified.getTime(),name:this.name,owner:this.owner,ownerFolder:this.ownerFolder,snippet:this.snippet,sourceUrl:this.sourceUrl,spatialReference:this.spatialReference,tags:(0,n.d9)(this.tags),thumbnail:this.thumbnail,title:this.title,type:this.type,typeKeywords:(0,n.d9)(this.typeKeywords),url:this.url};return(0,n.yd)(t)}static fromJSON(e){if(!e)return null;if(e.declaredClass)throw new Error("JSON object is already hydrated");return new _({sourceJSON:e})}_getPostQuery(){const e=this.toJSON();for(const t in e)"tags"===t&&null!==e[t]&&(e[t]=e[t].join(", ")),"typeKeywords"===t&&null!==e[t]&&(e[t]=e[t].join(", ")),"extent"===t&&e[t]&&(e[t]=JSON.stringify(e[t]));return e}};(0,r._)([(0,d.Cb)({type:["private","shared","org","public"]})],U.prototype,"access",void 0),(0,r._)([(0,d.Cb)()],U.prototype,"accessInformation",void 0),(0,r._)([(0,d.Cb)({type:String})],U.prototype,"apiKey",void 0),(0,r._)([(0,d.Cb)({json:{read:{source:"appProxies"}}})],U.prototype,"applicationProxies",void 0),(0,r._)([(0,d.Cb)()],U.prototype,"avgRating",void 0),(0,r._)([(0,d.Cb)()],U.prototype,"categories",void 0),(0,r._)([(0,d.Cb)({type:Date})],U.prototype,"created",void 0),(0,r._)([(0,d.Cb)()],U.prototype,"culture",void 0),(0,r._)([(0,d.Cb)()],U.prototype,"description",void 0),(0,r._)([(0,d.Cb)({readOnly:!0})],U.prototype,"displayName",null),(0,r._)([(0,d.Cb)({type:g.Z})],U.prototype,"extent",void 0),(0,r._)([(0,h.r)("extent")],U.prototype,"readExtent",null),(0,r._)([(0,d.Cb)()],U.prototype,"groupCategories",void 0),(0,r._)([(0,d.Cb)({readOnly:!0})],U.prototype,"iconUrl",null),(0,r._)([(0,d.Cb)()],U.prototype,"id",void 0),(0,r._)([(0,d.Cb)({readOnly:!0})],U.prototype,"isLayer",null),(0,r._)([(0,d.Cb)({type:Boolean,readOnly:!0})],U.prototype,"isOrgItem",void 0),(0,r._)([(0,d.Cb)()],U.prototype,"itemControl",void 0),(0,r._)([(0,d.Cb)({readOnly:!0})],U.prototype,"itemPageUrl",null),(0,r._)([(0,d.Cb)({readOnly:!0})],U.prototype,"itemUrl",null),(0,r._)([(0,d.Cb)()],U.prototype,"licenseInfo",void 0),(0,r._)([(0,d.Cb)({type:Date})],U.prototype,"modified",void 0),(0,r._)([(0,d.Cb)()],U.prototype,"name",void 0),(0,r._)([(0,d.Cb)()],U.prototype,"numComments",void 0),(0,r._)([(0,d.Cb)()],U.prototype,"numRatings",void 0),(0,r._)([(0,d.Cb)()],U.prototype,"numViews",void 0),(0,r._)([(0,d.Cb)()],U.prototype,"owner",void 0),(0,r._)([(0,d.Cb)()],U.prototype,"ownerFolder",void 0),(0,r._)([(0,d.Cb)({type:y.Z})],U.prototype,"portal",void 0),(0,r._)([(0,d.Cb)()],U.prototype,"screenshots",void 0),(0,r._)([(0,d.Cb)()],U.prototype,"size",void 0),(0,r._)([(0,d.Cb)()],U.prototype,"snippet",void 0),(0,r._)([(0,d.Cb)()],U.prototype,"sourceJSON",void 0),(0,r._)([(0,d.Cb)({type:String})],U.prototype,"sourceUrl",void 0),(0,r._)([(0,d.Cb)({type:String})],U.prototype,"spatialReference",void 0),(0,r._)([(0,d.Cb)()],U.prototype,"tags",void 0),(0,r._)([(0,d.Cb)()],U.prototype,"thumbnail",void 0),(0,r._)([(0,d.Cb)({readOnly:!0})],U.prototype,"thumbnailUrl",null),(0,r._)([(0,d.Cb)()],U.prototype,"title",void 0),(0,r._)([(0,d.Cb)()],U.prototype,"type",void 0),(0,r._)([(0,d.Cb)()],U.prototype,"typeKeywords",void 0),(0,r._)([(0,d.Cb)({type:String,json:{read(e,t){if(x.has(t.type)){const t=this.portal?.restUrl;e||(e=t&&this.id?`${t}/content/items/${this.id}/data`:null)}return e}}})],U.prototype,"url",void 0),(0,r._)([(0,d.Cb)({readOnly:!0})],U.prototype,"userItemUrl",null),U=_=(0,r._)([(0,m.j)("esri.portal.PortalItem")],U);const P=U}}]);