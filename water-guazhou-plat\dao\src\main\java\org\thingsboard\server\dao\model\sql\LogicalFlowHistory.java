package org.thingsboard.server.dao.model.sql;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.GenericGenerator;
import org.thingsboard.server.dao.model.ModelConstants;

import javax.persistence.*;

@Data
@Entity
@EqualsAndHashCode
@Table(name = ModelConstants.TABLE_LOGICAL_FLOW_HISTORY)
public class LogicalFlowHistory {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.LOGICAL_FLOW_ID)
    private String logicalFlowId;

    @Column(name = ModelConstants.LOGICAL_FLOW_NAME)
    private String logicalFlowName;

    @Column(name = ModelConstants.CREATED_TIME)
    private Long createdTime;

    @Column(name = ModelConstants.START_TIME)
    private Long startTime;
}
