package org.thingsboard.server.dao.util.imodel.query.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.thingsboard.server.dao.model.sql.base.BaseTemplateFile;
import org.thingsboard.server.dao.util.imodel.query.PageableQueryEntity;

/**
 * 平台管理-模型文件对象 base_template_file
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
@ApiModel(value = "模型文件", description = "平台管理-模型文件实体类")
@Data
public class BaseTemplateFilePageRequest extends PageableQueryEntity<BaseTemplateFile> {
    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 模板名称
     */
    @ApiModelProperty(value = "模板名称")
    private String name;

    /**
     * 模板编码
     */
    @ApiModelProperty(value = "模板编码")
    private String code;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;

    /**
     * 分类
     */
    @ApiModelProperty(value = "分类")
    private String type;

    /**
     * 文件类型
     */
    @ApiModelProperty(value = "文件类型")
    private String fileType;

    /**
     * 文件路径
     */
    @ApiModelProperty(value = "文件路径")
    private String fileUrl;
}
