package org.thingsboard.server.dao.sql.workOrder;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.thingsboard.server.dao.model.sql.TaskTrackHistoryEntity;

import java.util.List;

@Deprecated
public interface TaskTrackHistoryRepository extends JpaRepository<TaskTrackHistoryEntity, String> {

    @Query("SELECT tth FROM TaskTrackHistoryEntity tth " +
            "WHERE tth.contentId = ?1 " +
            "ORDER BY tth.userId, tth.ts")
    List<TaskTrackHistoryEntity> findByContentIdOrderByTs(String contentId);
}
