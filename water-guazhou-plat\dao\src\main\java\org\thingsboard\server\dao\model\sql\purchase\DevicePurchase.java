package org.thingsboard.server.dao.model.sql.purchase;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseTenantName;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseUsername;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.util.Date;

@Getter
@Setter
@ResponseEntity
public class DevicePurchase {
    @TableId(type = IdType.ASSIGN_UUID)
    // id
    private String id;

    // 采购单编码
    private String code;

    // 采购单标题
    private String title;

    // 采购人
    @ParseUsername(withDepartment = true)
    private String userId;

    // 使用方式/用途
    private String useWay;

    // 申请时间
    private Date uploadTime;

    // 预计采购日期
    private Date preTime;

    // 预算
    private Double budget;

    // 是否为补录
    private Boolean addRecord;

    // 创建人
    @ParseUsername
    private String creator;

    // 创建时间
    private Date createTime;

    // 租户ID
    @ParseTenantName
    private String tenantId;

}
