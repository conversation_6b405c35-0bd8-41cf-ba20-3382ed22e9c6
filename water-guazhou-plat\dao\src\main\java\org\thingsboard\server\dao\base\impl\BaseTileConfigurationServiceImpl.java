package org.thingsboard.server.dao.base.impl;

import java.util.Collections;
import java.util.List;
import java.util.UUID;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.base.IBaseTileConfigurationService;
import org.thingsboard.server.dao.model.sql.base.BaseTileConfiguration;
import org.thingsboard.server.dao.sql.base.BaseTileConfigurationMapper;
import org.thingsboard.server.dao.util.imodel.query.base.BaseTileConfigurationPageRequest;

/**
 * 公共管理平台-瓦片数据配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-06
 */
@Service
public class BaseTileConfigurationServiceImpl implements IBaseTileConfigurationService {
    @Autowired
    private BaseTileConfigurationMapper baseTileConfigurationMapper;

    /**
     * 查询公共管理平台-瓦片数据配置
     *
     * @param id 公共管理平台-瓦片数据配置主键
     * @return 公共管理平台-瓦片数据配置
     */
    @Override
    public BaseTileConfiguration selectBaseTileConfigurationById(String id) {
        return baseTileConfigurationMapper.selectBaseTileConfigurationById(id);
    }

    /**
     * 查询公共管理平台-瓦片数据配置列表
     *
     * @param baseTileConfiguration 公共管理平台-瓦片数据配置
     * @return 公共管理平台-瓦片数据配置
     */
    @Override
    public IPage<BaseTileConfiguration> selectBaseTileConfigurationList(BaseTileConfigurationPageRequest baseTileConfiguration) {
        return baseTileConfigurationMapper.selectBaseTileConfigurationList(baseTileConfiguration);
    }

    /**
     * 新增公共管理平台-瓦片数据配置
     *
     * @param baseTileConfiguration 公共管理平台-瓦片数据配置
     * @return 结果
     */
    @Override
    public int insertBaseTileConfiguration(BaseTileConfiguration baseTileConfiguration) {
        baseTileConfiguration.setId(UUID.randomUUID().toString().replace("-", ""));
        return baseTileConfigurationMapper.insertBaseTileConfiguration(baseTileConfiguration);
    }

    /**
     * 修改公共管理平台-瓦片数据配置
     *
     * @param baseTileConfiguration 公共管理平台-瓦片数据配置
     * @return 结果
     */
    @Override
    public int updateBaseTileConfiguration(BaseTileConfiguration baseTileConfiguration) {
        return baseTileConfigurationMapper.updateBaseTileConfiguration(baseTileConfiguration);
    }

    /**
     * 批量删除公共管理平台-瓦片数据配置
     *
     * @param ids 需要删除的公共管理平台-瓦片数据配置主键
     * @return 结果
     */
    @Override
    public int deleteBaseTileConfigurationByIds(List<String> ids) {
        return baseTileConfigurationMapper.deleteBaseTileConfigurationByIds(ids);
    }

    /**
     * 删除公共管理平台-瓦片数据配置信息
     *
     * @param id 公共管理平台-瓦片数据配置主键
     * @return 结果
     */
    @Override
    public int deleteBaseTileConfigurationById(String id) {
        return baseTileConfigurationMapper.deleteBaseTileConfigurationById(id);
    }

    /**
     * 获取所有的瓦片数据配置数据
     * @return
     */
    @Override
    public List<BaseTileConfiguration> selectAllBaseTileConfiguration() {
        return baseTileConfigurationMapper.selectAllBaseTileConfiguration();
    }
}
