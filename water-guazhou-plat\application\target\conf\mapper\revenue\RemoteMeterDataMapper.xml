<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.revenue.RemoteMeterDataMapper">

    <select id="selectAllByTime" resultType="org.thingsboard.server.dao.model.sql.revenue.RemoteMeterData">
        select meter.cust_code,a.*,
        (select b.id from revenue.tb_remote_meter_data b left join revenue.tb_water_meter wm on b.meter_code = wm.meter_code left join revenue.tb_cust_info ci on wm.cust_code = ci.code  where a.meter_code = b.meter_code and b.data_time &lt; a.data_time order by b.data_time desc offset 0 limit 1) as last_id
        from revenue.tb_remote_meter_data a
        left join revenue.tb_water_meter meter on a.meter_code = meter.meter_code
        left join revenue.tb_cust_info ci on meter.cust_code = ci.code
        where a.upload_time between #{start} and #{end}
    </select>

</mapper>