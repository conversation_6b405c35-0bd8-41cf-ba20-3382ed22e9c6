package org.thingsboard.server.dao.model.sql.smartPipe.dma;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 核算周期配置
 */
@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@TableName(ModelConstants.PIPE_PARTITION_TOTAL_FLOW_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class PipePartitionTotalFlow {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @TableField(exist = false)
    private String partitionId;

    @TableField(exist = false)
    private String partitionName;

    @TableField(exist = false)
    private String deviceName;

    @TableField(ModelConstants.PIPE_PARTITION_TOTAL_FLOW_DEVICE_ID)
    private String deviceId;

    @TableField(ModelConstants.PIPE_PARTITION_TOTAL_FLOW_VALUE)
    private BigDecimal value;

    @TableField(ModelConstants.PIPE_PARTITION_TOTAL_FLOW_CORRECT_WATER)
    private BigDecimal correctWater;

    @TableField(ModelConstants.PIPE_PARTITION_TOTAL_ORIGIN_VALUE)
    private BigDecimal originWater;

    @TableField(ModelConstants.PIPE_PARTITION_TOTAL_FLOW_COLLECT_TIME)
    private Date collectTime;

    @TableField(exist = false)
    private String collectTimeStr;

    @TableField(ModelConstants.PIPE_PARTITION_TOTAL_FLOW_TYPE)
    private String type;

    @TableField(ModelConstants.COPY_DATA_READ_METER_DATA_UPDATE_USER)
    private String updateUser;

    @TableField(ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;
}
