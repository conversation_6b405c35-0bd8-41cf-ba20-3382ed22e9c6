package org.thingsboard.server.dao.sql.dataSourceRelation;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.data_source_relation.DataSourceRelationDao;
import org.thingsboard.server.dao.model.sql.DataSourceRelationEntity;

import javax.transaction.Transactional;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/2/26 15:05
 */
@Service
@Transactional
public class DataSourceRelationDaoImpl implements DataSourceRelationDao {

    @Autowired
    private DataSourceRelationRepository dataSourceRelationRepository;


    @Override
    public boolean mountRelation(String originateId, List<String> dataSourceId, String entityType) {
        dataSourceId.forEach(id -> {
            DataSourceRelationEntity dataSourceRelationEntity = new DataSourceRelationEntity();
            dataSourceRelationEntity.setDataSourceId(id);
            dataSourceRelationEntity.setOriginateId(originateId);
            dataSourceRelationEntity.setEntityType(entityType);
            dataSourceRelationRepository.save(dataSourceRelationEntity);
        });
        return true;
    }

    @Override
    public boolean mountRelation(String originateId, String dataSourceId, String type) {
        DataSourceRelationEntity dataSourceRelationEntity = new DataSourceRelationEntity();
        dataSourceRelationEntity.setDataSourceId(dataSourceId);
        dataSourceRelationEntity.setOriginateId(originateId);
        dataSourceRelationEntity.setEntityType(type);
        dataSourceRelationRepository.save(dataSourceRelationEntity);
        return true;
    }

    @Override
    public DataSourceRelationEntity findByDataSourceId(String dataSourceId) {
        return dataSourceRelationRepository.findByDataSourceId(dataSourceId);
    }
}
