package org.thingsboard.server.dao.util.imodel.query.smartProduction.guard;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.dao.model.sql.smartProduction.guard.GuardArrangePartner;
import org.thingsboard.server.dao.smartProduction.guard.GuardRearrangeRecordService;
import org.thingsboard.server.dao.sql.smartProduction.guard.GuardArrangePartnerMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;

import java.util.Collections;
import java.util.List;

@Service
public class GuardArrangePartnerServiceImpl implements GuardArrangePartnerService {
    @Autowired
    private GuardArrangePartnerMapper mapper;

    @Autowired
    private GuardRearrangeRecordService guardRearrangeRecordService;

    @Override
    public IPage<GuardArrangePartner> findAllConditional(GuardArrangePartnerPageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    public GuardArrangePartner save(GuardArrangePartnerSaveRequest entity) {
        return QueryUtil.saveOrUpdateOneByRequest(entity, mapper::insert, null);
    }

    @Override
    public List<GuardArrangePartner> replaceAll(String arrangeId, List<GuardArrangePartnerSaveRequest> partners) {
        mapper.deleteAllByArrangeId(arrangeId);
        if (partners.size() == 0) {
            return Collections.emptyList();
        }

        return QueryUtil.saveOrUpdateBatchByRequest(partners, mapper::saveAll, mapper::saveAll);
    }

    @Override
    @Transactional
    public boolean switchArrangement(GuardArrangeSwitchRequest req) {
        String id = req.getId();
        GuardArrangePartner partnerInfo = mapper.selectById(id);
        List<GuardArrangePartner> rearrangeInfoList = req.buildArrangeList(partnerInfo.getArrangeId());

        guardRearrangeRecordService.record(req);

        // 移除当前排班成员
        mapper.deleteById(req.getId());
        if (rearrangeInfoList.size() == 0) {
            return true;
        }

        return mapper.saveAll(rearrangeInfoList) > 0;
    }

    @Override
    @Transactional
    public List<GuardArrangePartner> autoCompletePendingArrange() {
        List<GuardArrangePartnerSaveRequest> templates = mapper.selectPendingArrangePartnerSaveTemplate();
        return QueryUtil.saveOrUpdateBatchByRequest(templates, mapper::saveAll, null);
    }

    @Override
    public boolean removeOnArrangeIdIn(List<String> removedArrangeInfoIdList) {
        if (removedArrangeInfoIdList.size() == 0) {
            return true;
        }
        return mapper.removeByArrangeIdIn(removedArrangeInfoIdList) > 0;
    }

}
