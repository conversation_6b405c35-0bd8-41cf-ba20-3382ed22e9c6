import{d as W,c as P,r as x,s as Q,o as $,am as z,j as E,g as G,n as O,q as F,i as y,t as Y,p as H,_ as J,aq as K,C as X}from"./index-r0dFAfgr.js";import{w as Z}from"./Point-WxyopZva.js";import{d as aa}from"./onemap-CEunQziB.js";import{P as S,C as ta}from"./index-CcDafpIP.js";import{r as U}from"./chart-wy3NEK2T.js";import{g as ea}from"./URLHelper-B9aplt5w.js";const sa={class:"onemap-panel-wrapper"},na={class:"table-box"},la=W({__name:"waterQualityMonitoring",props:{view:{},menu:{}},emits:["highlightMark","addMarks"],setup(j,{emit:q}){const I=q,C=j,c=P([{label:"0 个",value:"水质监测点总数"},{label:"0 %",value:"报警率"}]),w=P(),f=[{name:"offline",label:"离线"},{name:"alarm",label:"报警"},{name:"online",label:"正常"}],k=x({group:[{id:"chart",fieldset:{desc:"监测状态统计",type:"underline",style:{marginTop:0}},fields:[{type:"vchart",option:U(),style:{height:"150px"}}]},{id:"tab",fields:[{type:"input",field:"name",appendBtns:[{perm:!0,text:"刷新",click:()=>r(!0)}],onChange:()=>r()},{type:"tabs",field:"type",tabs:[{label:"全部",value:"all"},...f.map(t=>({...t,value:t.name}))],tabType:"simple",onChange:()=>r()}]}],labelPosition:"top",gutter:12,defaultValue:{type:"all"}}),i=x({indexVisible:!0,dataList:[],pagination:{hide:!0,refreshData:({page:t,size:n})=>{i.pagination.page=t,i.pagination.limit=n},layout:"total,sizes, jumper"},handleRowClick:t=>A(t),columns:[{width:200,label:"名称",prop:"name",sortable:!0},{width:80,label:"状态",prop:"status",formatter:t=>{var n;return((n=f.find(u=>u.name===t.status))==null?void 0:n.label)||t.status}},{width:160,label:"更新时间",prop:"lastTime",sortable:!0}]}),D=x({dataList:[],columns:[],pagination:{refreshData:({page:t,size:n})=>{D.pagination.page=t,D.pagination.limit=n,r()}}}),r=async t=>{var n,u,g,b,M,T,V,L,R;i.loading=!0;try{const h=(n=w.value)==null?void 0:n.dataForm.type,v=await aa({name:(u=w.value)==null?void 0:u.dataForm.name,status:h==="all"?"":h});i.dataList=((g=v.data)==null?void 0:g.data)||[];const B=k.group[0].fields[0],_=((M=(b=v.data)==null?void 0:b.data)==null?void 0:M.length)||0,N=[],p=[];if((V=(T=v.data)==null?void 0:T.data)!=null&&V.map(a=>{var d,m;const e=(d=a.location)==null?void 0:d.split(",");if((e==null?void 0:e.length)===2){const l=new Z({longitude:e[0],latitude:e[1],spatialReference:(m=C.view)==null?void 0:m.spatialReference});N.push({id:a.stationId,visible:!1,x:l.x,y:l.y,offsetY:-40,title:a.name,customComponent:Q(S),customConfig:{info:{type:"attrs",imageUrl:a.imgs,stationId:a.stationId}},attributes:{id:a.stationId,row:a,path:C.menu.path},symbolConfig:{url:ea("水质监测站.png")}})}let s=p.find(l=>l.name===a.status);const{label:o}=f.find(l=>l.name===a.status)||{};s?s.value++:(s={name:a.status,nameAlias:o,value:1,scale:"0%"},p.push(s))}),p.map(a=>{var e,s,o;return a.scale=_===0?"0%":(Number(a.value)/_*100).toFixed(2)+"%",a.value=((o=(s=(e=v.data)==null?void 0:e.data)==null?void 0:s.filter(d=>d.status===a.name))==null?void 0:o.length)||0,a}),t){const a=(L=k.group.find(e=>e.id==="tab"))==null?void 0:L.fields[1];if(a){a.tabs=a.tabs.map(s=>{var m;const o=p.find(l=>l.name===s.value),d=((m=f.find(l=>l.name===s.value))==null?void 0:m.label)||"";return s.label=d+"("+((o==null?void 0:o.value)||0)+")",s});const e=a.tabs.find(s=>s.value==="all");e&&(e.label="全部("+_+")"),B&&(B.option=U(p,"个","",0))}c.value[0].label=_+"个",c.value[1].label=((R=p.find(e=>e.name==="alarm"))==null?void 0:R.scale)||"0 %"}I("addMarks",{windows:N,customWinComp:Q(S)})}catch(h){console.dir(h)}i.loading=!1},A=async t=>{I("highlightMark",C.menu,t==null?void 0:t.stationId)};return $(()=>{r(!0)}),z(()=>E().isDark,()=>r(!0)),(t,n)=>{const u=J,g=K;return G(),O("div",sa,[F(y(ta),{modelValue:y(c),"onUpdate:modelValue":n[0]||(n[0]=b=>Y(c)?c.value=b:null),span:12},null,8,["modelValue"]),F(u,{ref_key:"refForm",ref:w,config:y(k)},null,8,["config"]),H("div",na,[F(g,{config:y(i)},null,8,["config"])])])}}}),ca=X(la,[["__scopeId","data-v-d6475a81"]]);export{ca as default};
