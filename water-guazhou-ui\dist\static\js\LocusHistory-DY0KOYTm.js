import{c as H,Q as j,dc as O,d as Z,r as N,l as U,s as P,D as tt,bE as q,bT as et,g as z,h as ot,F as D,p as f,q as C,i as r,cs as X,G as $,bh as E,n as J,aJ as at,aB as it,_ as st,H as rt,c2 as nt,dg as lt,aq as pt,dh as mt,al as dt,b7 as ct,X as ut,ar as ft,a1 as gt,C as ht}from"./index-r0dFAfgr.js";/* empty css                         */import{w as Q}from"./Point-WxyopZva.js";import{g as _t,q as W,c as K,s as vt,t as yt}from"./FeatureHelper-Da16o0mu.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import{n as bt,b as wt}from"./MapView-DaoQedLH.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import{g as Y,a as Vt}from"./LayerHelper-Cn-iiqxI.js";import"./project-DUuzYgGl.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./geometryEngineBase-BhsKaODW.js";import{a as kt}from"./locas-Cxm3ID_S.js";import It from"./RightDrawerMap-D5PhmGFO.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./pipe-nogVzCHG.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./ArcView-DpMnCY82.js";import"./ViewHelper-BGCZjxXH.js";import"./fieldconfig-Bk3o1wi7.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./ArcStationWarning-BF9YrSzF.js";/* empty css                         */import"./useWaterPoint-Bv0z6ym6.js";import"./DateFormatter-Bm9a68Ax.js";import"./zhandian-YaGuQZe6.js";import"./useStation-DJgnSZIA.js";import"./DrawerBox-CLde5xC8.js";import"./SideDrawer-CBntChyn.js";import"./PipeDetail-CTBPYFJW.js";import"./Search-NSrhrIa_.js";import"./FormTableColumnFilter-BT7pLXIC.js";import"./QueryHelper-ILO3qZqg.js";import"./config-fy91bijz.js";import"./ArcBRTools-BO92yznB.js";import"./PipeLength.vue_vue_type_script_setup_true_lang-BZfUsvDf.js";import"./arcWidgetButton-0glIxrt7.js";import"./StatisticsHelper-D-s_6AyQ.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./wfsUtils-DXofo3da.js";import"./usePipelineGroup-Ba1pmWz_.js";import"./GroupLayer-DhhN3FyN.js";import"./lazyLayerLoader-DbM9sT1W.js";import"./WFSLayer-1TtJp3nx.js";import"./clientSideDefaults-VQhQaYxh.js";import"./QueryEngineCapabilities-Dk3NJwmm.js";import"./wfsUtils-Du031XaC.js";import"./geojson-MBFu2-HZ.js";import"./xmlUtils-CtUoQO7q.js";import"./ListWindow-WS05QqV0.js";import"./PopLayout-BP55MvL7.js";import"./PoiSearchV2-D7yNeLuv.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./URLHelper-B9aplt5w.js";import"./useLayerList-DmEwJ-ws.js";import"./useScaleBar-Beed-z91.js";const Ct=()=>{const _=H(!1);let a,A,n,y=[];const l=[];let s,m=0,d=0,I=3.3;const F=33,g=async(t,e,i,p=0,v)=>{if(d=p||0,m>=y.length&&(_.value=!1),!!_.value){if(p===t.length){if(t.length>1){const u=(v||0)/I*F;await g([y[m+1]],u,i,0);return}d=0,m++,R();return}await O(e),S(t[p]),L(t[p]),await g(t,e,i,++p,v)}},L=t=>{if(!t)return;l.push(t);const e=K({geometry:new wt({paths:[l],spatialReference:a==null?void 0:a.spatialReference}),symbol:vt("polyline",{color:"#ff0000",width:2})});s==null||s.removeAll(),s==null||s.add(e)},S=t=>{if(n==null||n.removeAll(),!t)return;const e=K({geometry:new Q({x:t[0],y:t[1],spatialReference:a==null?void 0:a.spatialReference}),symbol:A});return n==null||n.add(e),e},R=async()=>{if(!_.value)return;if(m>=y.length-1){_.value=!1,m=0,l.length=0;return}const t=y[m],e=y[m+1],i=W([t,e],"meters",a==null?void 0:a.spatialReference),p=c([[t[0],t[1]+10],[t[0],t[1]],[e[0],e[1]]]);if(i<I){const v=i/I*F;await g([e],v,p,0)}else{const v=i/I,u=Math.floor(v),T=v-u,M=yt(I,p),x=Array.from({length:u}).map((B,G)=>[t[0]+G*M.x,t[1]+G*M.y]);await g(x,F,p,d,T)}},c=t=>{if((t==null?void 0:t.length)!==3)return 0;const e=[];e.push(t[0][0]-t[1][0]),e.push(t[0][1]-t[1][1]);const i=[];i.push(t[2][0]-t[1][0]),i.push(t[2][1]-t[1][1]);let p;if(e[0]*i[1]===e[1]*i[0])p=180;else{const v=e[0]*i[0]+e[1]*i[1];p=Math.acos(v/Math.sqrt(e[0]**2+e[1]**2)/Math.sqrt(i[0]**2+i[1]**2))*180/Math.PI,i[0]<0&&(p=360-p)}return p},o=(t,e)=>{m=t===void 0?m||0:t,I=(e||100)*.033,_.value=!0,m===0&&d===0&&(s==null||s.removeAll()),R()},h=async t=>{I=(t||100)*.033,_.value===!0&&(_.value=!1,d=0,await O(F),_.value=!0,R())},b=()=>{_.value=!1},V=async()=>{_.value=!1,await O(F),m=0,d=0,l.length=0,s==null||s.removeAll(),n==null||n.removeAll(),l.length=0;const t=S(y[0]);_t(a,t,{zoom:15,duration:500,avoidHighlight:!0})},w=t=>{a=t.mapView||a,y=t.path||[],s=Y(a,{id:"locus",title:"移动轨迹"}),n=Y(a,{id:"person",title:"人员"}),V(),A=new bt(t.pic)},k=()=>{var t,e;n&&((t=a==null?void 0:a.map)==null||t.remove(n)),s&&((e=a==null?void 0:a.map)==null||e.remove(s))};return j(()=>{k()}),{init:w,pause:b,stop:V,start:o,destroy:k,setSpeed:h,running:_}},Ft="/static/png/user_location-D6v63UKh.png",Tt={class:"locusdisplay"},Lt={class:"audioplay"},Mt={class:"userinfo"},xt={class:"user-task-item"},Dt={class:"item-content"},Rt={class:"user-task-item"},Nt={class:"item-content"},Ut={class:"person-info-tablebox"},At={class:"locusinfo"},St={class:"circle"},Bt={class:"circle_title"},qt={class:"unit"},Et=Z({__name:"LocusHistory",setup(_){const a=H(),A=H(),n={},y=N({tabs:[],loading:!1,layerIds:[],layerInfos:[]}),l=N({speed:100,activeNames:[],slideVal:0,dateVal:U().format("YYYY-MM-DD")}),s=N([{title:"总里程",unit:"km",value:0},{title:"总时长",unit:"h",value:0},{title:"平均速度",unit:"km/h",value:0}]),m=N({group:[{fields:[{type:"select-tree",field:"pid",options:[],defaultExpandAll:!0,checkStrictly:!0,onChange:()=>L(),extraFormItem:[{type:"input",field:"name",prefixIcon:P(dt),appendBtns:[{perm:!0,svgIcon:P(ct),click:()=>L()}],onChange:()=>L()}]},{type:"tree",field:"person",options:[],onChange:()=>F()}]}],labelPosition:"top",defaultValue:{}}),d=N({dataList:[],pagination:{hide:!0},columns:[{label:"日期",prop:"createTime"},{label:"X坐标",prop:"lon"},{label:"Y坐标",prop:"lat"}]}),I=async()=>{var o,h;y.layerIds=Vt(n.view);const c=await ut(y.layerIds);y.layerInfos=((h=(o=c.data)==null?void 0:o.result)==null?void 0:h.rows)||[]},F=()=>{var b,V;const c=(V=(b=a.value)==null?void 0:b.dataForm.person)==null?void 0:V[0];if(d.dataList=[],!c)return;const h=m.group[0].fields[1].options.find(w=>w.value===c);l.curUser=h,g.stop(),kt({userId:tt(c),page:1,size:999999,fromTime:U(l.dateVal,q).startOf("D").valueOf(),toTime:U(l.dateVal,q).endOf("D").valueOf()}).then(w=>{var i,p,v;d.dataList=(p=(i=w.data.data)==null?void 0:i.data)==null?void 0:p.map(u=>{var x;const[T,M]=((x=u.coordinate)==null?void 0:x.split(","))||[];return{...u,lon:parseFloat(T),lat:parseFloat(M)}});const t=d.dataList.map(u=>{var T;return new Q({longitude:u.lon,latitude:u.lat,spatialReference:(T=n.view)==null?void 0:T.spatialReference})}).map(u=>[u.x,u.y]);console.log(t);const e=W(t,"kilometers",(v=n.view)==null?void 0:v.spatialReference);if(s[0].value=Number(e.toFixed(2)||"0"),d.dataList.length>1){const u=d.dataList[d.dataList.length-1].createTime,T=U(u,q).valueOf(),M=d.dataList[0].createTime,x=U(M,q).valueOf(),B=T-x;s[1].value=Number((B/1e3/60/60).toFixed(2)),s[2].value=Number(B===0?0:(e/B).toFixed(2))}else s[1].value=0,s[2].value=0;g.init({mapView:n.view,path:t,pic:{width:20,height:20,yoffset:10,url:Ft}})}).catch(()=>{d.dataList=[]})},g=Ct(),L=()=>{var o;const c=((o=a.value)==null?void 0:o.dataForm)||{};et({page:1,size:999999,pid:c.pid,name:c.name}).then(h=>{var V,w;const b=m.group[0].fields[1];b.options=(w=(V=h.data.data)==null?void 0:V.data)==null?void 0:w.map(k=>({id:k.id.id,value:k.id.id,label:k.firstName,data:k}))})},S=()=>{ft(2).then(c=>{var h,b;const o=m.group[0].fields[0];o.options=gt(c.data.data||[]),a.value&&(a.value.dataForm.pid=(b=(h=o.options)==null?void 0:h[0])==null?void 0:b.value),L()})},R=async c=>{n.graphicsLayer=Y(c,{id:"locus-line",title:"轨迹"}),n.view=c,S(),await I()};return(c,o)=>{const h=st,b=rt,V=nt,w=lt,k=pt,t=mt;return z(),ot(It,{ref_key:"refMap",ref:A,title:"历史轨迹","hide-coords":!1,"hide-search":!0,"hide-layer-list":!0,onMapLoaded:R},{"map-bars":D(()=>[f("div",Tt,[f("div",Lt,[o[6]||(o[6]=f("span",null,"轨迹播放",-1)),C(b,{modelValue:r(l).speed,"onUpdate:modelValue":o[0]||(o[0]=e=>r(l).speed=e),size:"small",style:{width:"100px","margin-left":"auto"},onChange:o[1]||(o[1]=e=>r(g).setSpeed(r(l).speed))},{append:D(()=>o[5]||(o[5]=[f("span",null,"m/s",-1)])),_:1},8,["modelValue"]),C(r(X),{icon:r(g).running.value?"ep:video-pause":"ep:video-play",onClick:o[2]||(o[2]=()=>r(g).running.value?r(g).pause():r(g).start(0,r(l).speed))},null,8,["icon"]),C(r(X),{icon:"mdi:stop-circle-outline",onClick:r(g).stop},null,8,["onClick"])]),f("div",Mt,[C(t,{modelValue:r(l).activeNames,"onUpdate:modelValue":o[4]||(o[4]=e=>r(l).activeNames=e)},{default:D(()=>[C(w,{title:"人员任务信息"},{default:D(()=>{var e;return[f("div",xt,[o[7]||(o[7]=f("span",null,"姓名",-1)),o[8]||(o[8]=$(": ")),f("div",Dt,E((e=r(l).curUser)==null?void 0:e.label),1)]),f("div",Rt,[o[9]||(o[9]=f("span",null,"日期",-1)),o[10]||(o[10]=$(": ")),f("div",Nt,[C(V,{modelValue:r(l).dateVal,"onUpdate:modelValue":o[3]||(o[3]=i=>r(l).dateVal=i),type:"date",style:{width:"100%"},placeholder:"选择日期",size:"small",onChange:F},null,8,["modelValue"])])])]}),_:1}),C(w,{title:"人员信息"},{default:D(()=>[f("div",Ut,[C(k,{config:r(d)},null,8,["config"])])]),_:1})]),_:1},8,["modelValue"])])]),f("div",At,[(z(!0),J(it,null,at(r(s),(e,i)=>(z(),J("div",{key:i,class:"locusinfo-item"},[f("div",St,E(e.value===void 0?"--":e.value),1),f("div",Bt,E(e.title),1),f("div",qt,E(e.unit),1)]))),128))])]),default:D(()=>[C(h,{ref_key:"refForm",ref:a,config:r(m)},null,8,["config"])]),_:1},512)}}}),Po=ht(Et,[["__scopeId","data-v-aaec5db8"]]);export{Po as default};
