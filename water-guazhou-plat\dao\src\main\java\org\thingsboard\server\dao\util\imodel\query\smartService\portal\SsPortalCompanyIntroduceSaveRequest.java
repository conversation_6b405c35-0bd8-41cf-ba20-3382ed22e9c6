package org.thingsboard.server.dao.util.imodel.query.smartService.portal;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartService.portal.SsPortalCompanyIntroduce;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;

@Getter
@Setter
public class SsPortalCompanyIntroduceSaveRequest extends SaveRequest<SsPortalCompanyIntroduce> {
    // 简介
    private String introduce;

    // 描述
    private String detail;


    @Override
    protected SsPortalCompanyIntroduce build() {
        SsPortalCompanyIntroduce entity = new SsPortalCompanyIntroduce();
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    @Override
    protected SsPortalCompanyIntroduce update(String id) {
        SsPortalCompanyIntroduce entity = new SsPortalCompanyIntroduce();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(SsPortalCompanyIntroduce entity) {
        entity.setIntroduce(introduce);
        entity.setDetail(detail);
    }

}