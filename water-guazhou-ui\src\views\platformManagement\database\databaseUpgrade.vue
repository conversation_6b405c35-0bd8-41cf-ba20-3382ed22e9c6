<template>
  <div class="wrapper">
    <CardSearch
      ref="refSearch"
      :config="SearchConfig"
    ></CardSearch>
    <CardTable
      class="card-table"
      :config="TableConfig"
    ></CardTable>
    <DialogForm
      ref="refDialogForm"
      :config="DialogFormConfig"
    ></DialogForm>
  </div>
</template>

<script setup>
import { onMounted, reactive, ref } from 'vue'
import { 
  getBaseDatabaseUpgradeList,
  addBaseDatabaseUpgrade,
  editBaseDatabaseUpgrade,
  deleteBaseDatabaseUpgrade,
  getBaseDatabaseUpgradeDetail 
} from '@/api/platformManagement/baseDatabaseUpgrade'
import { SLConfirm, SLMessage } from '@/utils/Message'
import dayjs from 'dayjs'

const refSearch = ref()
const refDialogForm = ref()

const SearchConfig = reactive({
  labelWidth: '100px',
  filters: [
    { 
      type: 'input', 
      label: '升级前版本', 
      field: 'versionFrom', 
      placeholder: '请输入起始版本号',
      onChange: () => refreshData() 
    },
    { 
      type: 'input', 
      label: '升级后版本', 
      field: 'versionTo', 
      placeholder: '请输入目标版本号',
      onChange: () => refreshData() 
    },
    {
      type: 'select',
      label: '执行状态',
      field: 'status',
      options: [
        { label: '全部', value: '' },
        { label: '成功', value: 'SUCCESS' },
        { label: '失败', value: 'FAILED' },
        { label: '执行中', value: 'PROCESSING' }
      ],
      onChange: () => refreshData()
    },
    {
      type: 'btn-group',
      btns: [
        { type: 'primary', perm: true, text: '查询', click: () => refreshData() },
        { perm: true, type: 'primary', text: '新增', click: () => handleAdd() },
        { perm: true, type: 'danger', text: '批量删除', click: () => handleDelete() }
      ]
    }
  ],
  defaultParams: {}
})

const TableConfig = reactive({
  columns: [
    { label: '升级前版本', prop: 'versionFrom' },
    { label: '升级后版本', prop: 'versionTo' },
    { 
      label: '执行状态', 
      prop: 'status', 
      formatter: (row) => ({
        SUCCESS: '成功',
        FAILED: '失败',
        PROCESSING: '执行中'
      })[row.status],
      tagType: (row) => ({
        SUCCESS: 'success',
        FAILED: 'danger',
        PROCESSING: 'warning'
      })[row.status]
    },
    { 
      label: '执行时间', 
      prop: 'executionTime'
    },
    { label: '操作者ID', prop: 'operatorId' },
    { label: 'SQL文件路径', prop: 'sqlFilePath' }
  ],
  dataList: [],
  operations: [
    {
      perm: true,
      type: 'primary',
      isTextBtn: true,
      text: '查看详情',
      click: (row) => handleDetail(row)
    },
    {
      perm: true,
      type: 'primary',
      isTextBtn: true,
      text: '编辑',
      click: (row) => handleAdd(row)
    },
    {
      perm: true,
      type: 'danger',
      isTextBtn: true,
      text: '删除',
      click: (row) => handleDelete(row)
    }
  ],
  pagination: {
    total: 0,
    page: 1,
    align: 'right',
    limit: 20,
    handlePage: (page) => {
      TableConfig.pagination.page = page
      refreshData()
    },
    handleSize: (size) => {
      TableConfig.pagination.limit = size
      refreshData()
    }
  },
  handleSelectChange: (rows) => {
    TableConfig.selectList = rows || []
  }
})

const DialogFormConfig = reactive({
  title: '数据库版本升级',
  group: [
    {
      fields: [
        {
          type: 'input',
          label: '升级前版本',
          field: 'versionFrom',
          rules: [{ required: true, message: '请输入起始版本号' }]
        },
        {
          type: 'input',
          label: '升级后版本',
          field: 'versionTo',
          rules: [{ required: true, message: '请输入目标版本号' }]
        },
        {
          type: 'select',
          label: '执行状态',
          field: 'status',
          options: [
            { label: '成功', value: 'SUCCESS' },
            { label: '失败', value: 'FAILED' },
            { label: '执行中', value: 'PROCESSING' }
          ],
          rules: [{ required: true, message: '请选择执行状态' }]
        },
        {
          type: 'input',
          label: 'SQL文件路径',
          field: 'sqlFilePath',
          rules: [{ required: true, message: '请输入SQL文件路径' }]
        },
        {
          type: 'datetime',
          label: '执行时间',
          field: 'executionTime',
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          rules: [{ required: true, message: '请选择执行时间' }]
        },
        {
          type: 'input',
          label: '操作者ID',
          field: 'operatorId',
          rules: [{ required: false }]
        }
      ]
    }
  ],
  labelPosition: 'top',
  defaultValue: {},
  dialogWidth: 600,
  draggable: true,
  showSubmit: true,
  showCancel: true,
  cancelText: '取消',
  submitText: '提交',
  submit: async (params) => {
    try {
      if (params.id) {
        await editBaseDatabaseUpgrade(params)
        SLMessage.success('修改成功')
      } else {
        await addBaseDatabaseUpgrade(params)
        SLMessage.success('新增成功')
      }
      refDialogForm.value?.closeDialog()
      refreshData()
    } catch (error) {
      SLMessage.error('操作失败')
    }
  }
})

const resetDialogConfig = () => {
  DialogFormConfig.group[0].fields.forEach(field => {
    field.disabled = false
    field.readonly = false
  })
  DialogFormConfig.showSubmit = true
  DialogFormConfig.showCancel = true
  DialogFormConfig.cancelText = '取消'
  DialogFormConfig.submitText = '提交'
}

const handleAdd = (row) => {
  resetDialogConfig()
  DialogFormConfig.title = row ? '编辑升级记录' : '新增升级记录'
  DialogFormConfig.defaultValue = { 
    ...(row || {}),
    executionTime: row?.executionTime ? dayjs(row.executionTime).format('YYYY-MM-DD HH:mm:ss') : ''
  }
  refDialogForm.value?.openDialog()
}

const handleDetail = async (row) => {
  try {
    const res = await getBaseDatabaseUpgradeDetail(row.id)
    const detailData = res.data?.data || res
    
    resetDialogConfig()
    DialogFormConfig.title = '升级记录详情'
    DialogFormConfig.defaultValue = { 
      ...detailData,
      executionTime: detailData.executionTime ? dayjs(detailData.executionTime).format('YYYY-MM-DD HH:mm:ss') : ''
    }
    DialogFormConfig.group[0].fields.forEach(field => {
      field.disabled = true
      field.readonly = true
    })
    DialogFormConfig.showSubmit = false
    DialogFormConfig.cancelText = '关闭'
    refDialogForm.value?.openDialog()
  } catch (error) {
    SLMessage.error('获取详情失败')
  }
}

const handleDelete = (row) => {
  SLConfirm('确定删除？', '删除提示')
    .then(async () => {
      const ids = row ? [row.id] : TableConfig.selectList?.map(item => item.id) || []
      if (!ids.length) {
        SLMessage.warning('请选择要删除的数据')
        return
      }
      await deleteBaseDatabaseUpgrade(ids)
      SLMessage.success('删除成功')
      refreshData()
    })
    .catch(() => {})
}

const refreshData = async () => {
  try {
    const res = await getBaseDatabaseUpgradeList({
      page: TableConfig.pagination.page,
      size: TableConfig.pagination.limit,
      ...(refSearch.value?.queryParams || {})
    })
    const responseData = res.data?.data || res
    TableConfig.dataList = responseData.records || responseData
    TableConfig.pagination.total = responseData.total || responseData.length || 0
  } catch (error) {
    SLMessage.error('数据加载失败')
  }
}

onMounted(() => {
  refreshData()
})
</script>

<style lang="scss" scoped>
.wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.card-table {
  flex: 1;
  margin-top: 16px;
}
</style>