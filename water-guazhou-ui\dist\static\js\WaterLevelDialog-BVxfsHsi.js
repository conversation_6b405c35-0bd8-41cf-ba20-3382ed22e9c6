import{d as H,c as y,r as G,a8 as T,am as J,o as K,x as w,g as m,h as g,F as t,p as s,q as l,G as v,an as U,n as O,aJ as j,aB as z,aK as P,aL as $,I as Q,bU as X,c2 as Z,bW as h,e4 as ee,dz as le,dA as te,H as ae,K as oe,J as re,L as ne,C as de}from"./index-r0dFAfgr.js";import{s as se,u as ue}from"./conservationWaterLevel-BIi1yWt3.js";import{g as ie}from"./index-C7go6VEC.js";const pe={class:"dialog-footer"},me=H({__name:"WaterLevelDialog",props:{visible:{type:Boolean,default:!1},formData:{default:()=>({})},dialogType:{default:"add"}},emits:["update:visible","confirm"],setup(C,{emit:A}){const p=C,V=A,_=y(!1),f=y(),c=y([]),o=G({stationId:"",rawWaterLevel:0,groundwaterLevel:0,recordTime:"",dataSource:1}),x=T({get:()=>p.visible,set:r=>V("update:visible",r)}),I=T(()=>({add:"新增涵养水位数据",edit:"编辑涵养水位数据",view:"查看涵养水位数据"})[p.dialogType]),W={stationId:[{required:!0,message:"请选择测点",trigger:"change"}],rawWaterLevel:[{required:!0,message:"请输入原水液位",trigger:"blur"}],groundwaterLevel:[{required:!0,message:"请输入地下水位",trigger:"blur"}],recordTime:[{required:!0,message:"请选择记录时间",trigger:"change"}]};J(()=>p.formData,r=>{Object.assign(o,r)},{deep:!0,immediate:!0}),K(()=>{q()});const q=async()=>{try{const r=await ie({type:"水源地"});r.status===200&&(c.value=r.data.data||[])}catch(r){console.error("加载测点列表失败:",r),w.error("加载测点列表失败")}},D=r=>{if(r==null)return"";const e=Number(r).toFixed(3);return r>0?`+${e}`:e},R=async()=>{if(f.value)try{await f.value.validate(),_.value=!0;const r={...o};let e;p.dialogType==="add"?e=await se(r):e=await ue(r),e.data.code===200&&(w.success(p.dialogType==="add"?"新增成功":"更新成功"),V("confirm"),b())}catch(r){console.error("保存失败:",r),w.error("保存失败")}finally{_.value=!1}},b=()=>{var r;(r=f.value)==null||r.resetFields(),V("update:visible",!1)};return(r,e)=>{const B=P,M=$,n=Q,d=X,S=Z,i=h,u=ee,L=le,F=te,k=ae,N=oe,E=re,Y=ne;return m(),g(Y,{modelValue:x.value,"onUpdate:modelValue":e[12]||(e[12]=a=>x.value=a),title:I.value,width:"800px","close-on-click-modal":!1,onClose:b},{footer:t(()=>[s("div",pe,[l(E,{onClick:b},{default:t(()=>e[23]||(e[23]=[v("取消")])),_:1}),r.dialogType!=="view"?(m(),g(E,{key:0,type:"primary",onClick:R,loading:_.value},{default:t(()=>e[24]||(e[24]=[v(" 确定 ")])),_:1},8,["loading"])):U("",!0)])]),default:t(()=>[l(N,{ref_key:"formRef",ref:f,model:o,rules:W,"label-width":"120px",disabled:r.dialogType==="view"},{default:t(()=>[l(i,{gutter:20},{default:t(()=>[l(d,{span:12},{default:t(()=>[l(n,{label:"测点",prop:"stationId",required:""},{default:t(()=>[l(M,{modelValue:o.stationId,"onUpdate:modelValue":e[0]||(e[0]=a=>o.stationId=a),placeholder:"请选择测点",filterable:"",style:{width:"100%"}},{default:t(()=>[(m(!0),O(z,null,j(c.value,a=>(m(),g(B,{key:a.id,label:a.name,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),l(d,{span:12},{default:t(()=>[l(n,{label:"记录时间",prop:"recordTime",required:""},{default:t(()=>[l(S,{modelValue:o.recordTime,"onUpdate:modelValue":e[1]||(e[1]=a=>o.recordTime=a),type:"datetime",placeholder:"选择记录时间",format:"YYYY-MM-DD HH:mm:ss","value-format":"x",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),l(i,{gutter:20},{default:t(()=>[l(d,{span:12},{default:t(()=>[l(n,{label:"原水液位",prop:"rawWaterLevel",required:""},{default:t(()=>[l(u,{modelValue:o.rawWaterLevel,"onUpdate:modelValue":e[2]||(e[2]=a=>o.rawWaterLevel=a),precision:3,step:.001,min:0,placeholder:"请输入原水液位",style:{width:"100%"}},null,8,["modelValue"]),e[13]||(e[13]=s("span",{class:"unit-text"},"米",-1))]),_:1})]),_:1}),l(d,{span:12},{default:t(()=>[l(n,{label:"地下水位",prop:"groundwaterLevel",required:""},{default:t(()=>[l(u,{modelValue:o.groundwaterLevel,"onUpdate:modelValue":e[3]||(e[3]=a=>o.groundwaterLevel=a),precision:3,step:.001,min:0,placeholder:"请输入地下水位",style:{width:"100%"}},null,8,["modelValue"]),e[14]||(e[14]=s("span",{class:"unit-text"},"米",-1))]),_:1})]),_:1})]),_:1}),l(i,{gutter:20},{default:t(()=>[l(d,{span:12},{default:t(()=>[l(n,{label:"降雨量",prop:"rainfallAmount"},{default:t(()=>[l(u,{modelValue:o.rainfallAmount,"onUpdate:modelValue":e[4]||(e[4]=a=>o.rainfallAmount=a),precision:2,step:.1,min:0,placeholder:"请输入降雨量",style:{width:"100%"}},null,8,["modelValue"]),e[15]||(e[15]=s("span",{class:"unit-text"},"毫米",-1))]),_:1})]),_:1}),l(d,{span:12},{default:t(()=>[l(n,{label:"蒸发量",prop:"evaporationAmount"},{default:t(()=>[l(u,{modelValue:o.evaporationAmount,"onUpdate:modelValue":e[5]||(e[5]=a=>o.evaporationAmount=a),precision:2,step:.1,min:0,placeholder:"请输入蒸发量",style:{width:"100%"}},null,8,["modelValue"]),e[16]||(e[16]=s("span",{class:"unit-text"},"毫米",-1))]),_:1})]),_:1})]),_:1}),l(i,{gutter:20},{default:t(()=>[l(d,{span:12},{default:t(()=>[l(n,{label:"地表径流量",prop:"surfaceRunoff"},{default:t(()=>[l(u,{modelValue:o.surfaceRunoff,"onUpdate:modelValue":e[6]||(e[6]=a=>o.surfaceRunoff=a),precision:2,step:1,min:0,placeholder:"请输入地表径流量",style:{width:"100%"}},null,8,["modelValue"]),e[17]||(e[17]=s("span",{class:"unit-text"},"立方米",-1))]),_:1})]),_:1}),l(d,{span:12},{default:t(()=>[l(n,{label:"地下水开采量",prop:"extractionAmount"},{default:t(()=>[l(u,{modelValue:o.extractionAmount,"onUpdate:modelValue":e[7]||(e[7]=a=>o.extractionAmount=a),precision:2,step:1,min:0,placeholder:"请输入地下水开采量",style:{width:"100%"}},null,8,["modelValue"]),e[18]||(e[18]=s("span",{class:"unit-text"},"立方米",-1))]),_:1})]),_:1})]),_:1}),l(i,{gutter:20},{default:t(()=>[l(d,{span:12},{default:t(()=>[l(n,{label:"土壤含水率",prop:"soilMoisture"},{default:t(()=>[l(u,{modelValue:o.soilMoisture,"onUpdate:modelValue":e[8]||(e[8]=a=>o.soilMoisture=a),precision:2,step:.1,min:0,max:100,placeholder:"请输入土壤含水率",style:{width:"100%"}},null,8,["modelValue"]),e[19]||(e[19]=s("span",{class:"unit-text"},"%",-1))]),_:1})]),_:1}),l(d,{span:12},{default:t(()=>[l(n,{label:"渗透系数",prop:"permeabilityCoefficient"},{default:t(()=>[l(u,{modelValue:o.permeabilityCoefficient,"onUpdate:modelValue":e[9]||(e[9]=a=>o.permeabilityCoefficient=a),precision:4,step:1e-4,min:0,placeholder:"请输入渗透系数",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),l(i,{gutter:20},{default:t(()=>[l(d,{span:12},{default:t(()=>[l(n,{label:"数据来源",prop:"dataSource"},{default:t(()=>[l(F,{modelValue:o.dataSource,"onUpdate:modelValue":e[10]||(e[10]=a=>o.dataSource=a)},{default:t(()=>[l(L,{label:1},{default:t(()=>e[20]||(e[20]=[v("手动录入")])),_:1}),l(L,{label:2},{default:t(()=>e[21]||(e[21]=[v("设备采集")])),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1}),r.dialogType==="view"?(m(),g(d,{key:0,span:12},{default:t(()=>[l(n,{label:"液位变化",prop:"levelChange"},{default:t(()=>[l(k,{"model-value":D(o.levelChange),readonly:"",style:{width:"100%"}},null,8,["model-value"]),e[22]||(e[22]=s("span",{class:"unit-text"},"米",-1))]),_:1})]),_:1})):U("",!0)]),_:1}),l(n,{label:"备注",prop:"remark"},{default:t(()=>[l(k,{modelValue:o.remark,"onUpdate:modelValue":e[11]||(e[11]=a=>o.remark=a),type:"textarea",rows:3,placeholder:"请输入备注信息"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","disabled"])]),_:1},8,["modelValue","title"])}}}),Ve=de(me,[["__scopeId","data-v-5373a192"]]);export{Ve as default};
