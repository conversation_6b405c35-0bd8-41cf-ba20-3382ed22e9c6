import{z as e}from"./index-r0dFAfgr.js";const s=t=>e({url:"/api/store",method:"get",params:t}),a=t=>e({url:"/api/store",method:"post",data:t}),c=(t,o)=>e({url:`/api/store/${t}`,method:"patch",data:o}),d=t=>e({url:`/api/store/${t}`,method:"delete"}),h=t=>e({url:"/api/goodsShelf",method:"get",params:t}),l=t=>e({url:"/api/goodsShelf",method:"post",data:t}),p=(t,o)=>e({url:`/api/goodsShelf/${t}`,method:"patch",data:o}),S=t=>e({url:`/api/goodsShelf/${t}`,method:"delete"}),n=t=>e({url:"/api/constructionProject",method:"get",params:t}),u=t=>e({url:"/api/StoreInRecord",method:"get",params:t}),i=t=>e({url:"/api/StoreInRecord",method:"post",data:t}),g=t=>e({url:"/api/StoreInRecordDetail",method:"get",params:t}),m=t=>e({url:"/api/StoreOutRecord",method:"get",params:t}),R=t=>e({url:"/api/StoreOutRecord",method:"post",data:t}),f=t=>e({url:"/api/StoreOutRecordDetail",method:"get",params:t}),I=t=>e({url:"/api/deviceStorageJournal/checkout",method:"post",data:[t]});export{n as a,h as b,f as c,m as d,i as e,g as f,s as g,u as h,I as i,c as j,a as k,d as l,p as m,l as n,S as o,R as p};
