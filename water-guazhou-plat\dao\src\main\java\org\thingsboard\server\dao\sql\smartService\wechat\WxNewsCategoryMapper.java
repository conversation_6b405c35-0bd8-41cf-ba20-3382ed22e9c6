package org.thingsboard.server.dao.sql.smartService.wechat;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.thingsboard.server.dao.model.sql.smartService.wechat.WxNewsCategory;
import org.thingsboard.server.dao.util.imodel.query.smartService.wechat.WxNewsCategoryPageRequest;

@Mapper
public interface WxNewsCategoryMapper extends BaseMapper<WxNewsCategory> {
    IPage<WxNewsCategory> findByPage(WxNewsCategoryPageRequest request);

    boolean updateFully(WxNewsCategory entity);

}
