package org.thingsboard.server.dao.smartService.portal;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.smartService.portal.SsPortalSystemInfo;
import org.thingsboard.server.dao.sql.smartService.portal.SsPortalSystemInfoMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.smartService.portal.SsPortalSystemInfoSaveRequest;

@Service
public class SsPortalSystemInfoServiceImpl implements SsPortalSystemInfoService {
    @Autowired
    private SsPortalSystemInfoMapper mapper;

    @Override
    public SsPortalSystemInfo get(String tenantId) {
        return mapper.getByTenantId(tenantId);
    }

    @Override
    public SsPortalSystemInfo save(SsPortalSystemInfoSaveRequest entity) {
        return QueryUtil.saveOrUpdateOneByRequest(entity, mapper::save, mapper::updateFully);
    }

    @Override
    public boolean update(SsPortalSystemInfo entity) {
        return mapper.update(entity);
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }

}
