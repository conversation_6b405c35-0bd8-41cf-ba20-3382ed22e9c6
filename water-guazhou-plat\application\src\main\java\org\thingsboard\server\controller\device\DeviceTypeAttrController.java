package org.thingsboard.server.controller.device;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.deviceType.DeviceTypeAttrService;
import org.thingsboard.server.dao.deviceType.DeviceTypeService;
import org.thingsboard.server.dao.model.sql.deviceManage.DeviceTypeAttr;
import org.thingsboard.server.dao.util.imodel.query.device.DeviceTypeAttrPageRequest;
import org.thingsboard.server.dao.util.imodel.query.device.DeviceTypeAttrSaveRequest;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;
import org.thingsboard.server.utils.imodel.annotations.IStarController;

import static org.thingsboard.server.common.data.exception.ThingsboardErrorCode.GENERAL;

@IStarController
@RequestMapping("/api/deviceTypeAttr")
public class DeviceTypeAttrController extends BaseController {
    @Autowired
    private DeviceTypeAttrService deviceAttrService;

    @Autowired
    private DeviceTypeService deviceTypeService;


    @GetMapping
    public IstarResponse findAllConditional(DeviceTypeAttrPageRequest request) {
        return IstarResponse.ok(deviceAttrService.findAllConditional(request));
    }

    @GetMapping("list/{serialId}")
    public IstarResponse getListBySerialId(@PathVariable String serialId, String type) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return IstarResponse.ok(deviceAttrService.getListBySerialId(serialId, tenantId, type));
    }

    @PostMapping
    public DeviceTypeAttr save(@RequestBody DeviceTypeAttrSaveRequest req) throws ThingsboardException {
        //验证创建规则，确保类别存在性
        checkSerialId(req.getSerialId(), req.tenantId());
        String userId = UUIDConverter.fromTimeUUID(getCurrentUser().getUuidId());
        req.setCreator(userId);
        return deviceAttrService.save(req);
    }

    @PatchMapping("/{id}")
    public boolean edit(@RequestBody DeviceTypeAttrSaveRequest req, @PathVariable String id) throws ThingsboardException {
        checkSerialId(req.getSerialId(), req.tenantId());
        return deviceAttrService.update(req.unwrap(id));
    }

    private void checkSerialId(String serialId, String tenantId) throws ThingsboardException {
        if (serialId.equals("00000000000000")) {
            throw new ThingsboardException("只有类别末级可添加类别属性", GENERAL);
        }

        if (!deviceTypeService.existsBySerialId(serialId, tenantId)) {
            throw new ThingsboardException("类别不存在", GENERAL);
        } else if(deviceTypeService.getDepthBySerialId(serialId, tenantId) != 3) {
            throw new ThingsboardException("只有类别末级可添加类别属性", GENERAL);
        }
    }

    @DeleteMapping("/{id}")
    public IstarResponse delete(@PathVariable String id) {
        return IstarResponse.ok(deviceAttrService.delete(id));
    }
}
