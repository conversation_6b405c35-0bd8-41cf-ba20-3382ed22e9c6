import{d as U,c as p,r as f,s as w,l as I,bH as x,D as C,u as k,o as D,g as z,n as F,q as c,F as L,b6 as q}from"./index-r0dFAfgr.js";import{_ as B}from"./CardTable-rdWOL4_6.js";import{_ as E}from"./CardSearch-CB_HNR-Q.js";import N from"./OrderStepTags-CClNfq4j.js";import W from"./detail-CU6-qhMl.js";import{h as V,b as j,g as A,a as G,j as H}from"./config-DqqM5K5L.js";import{c as M}from"./index-CpGhZCTT.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";/* empty css                         */import"./detailSteps-BqRp_Y4m.js";/* empty css                */import"./index-CCFuhOrs.js";/* empty css                             */import"./DPlayer-Be2AurQX.js";import"./DPlayer.min-_HMH7IVX.js";import"./DateFormatter-Bm9a68Ax.js";import"./OutsideWorkOrder-C6s8joBt.js";const R={class:"wrapper"},me=U({__name:"OrderSearch",setup($){const d=p(),m=p(),u=p(),g=f({title:"流程明细",cancel:!1,className:"lightColor",group:[]}),_=p(""),O=f({filters:[{field:"stepProcessUserId",label:"类别",labelWidth:40,type:"radio-button",options:[{label:"全部",value:""},{label:"由我处理",value:"stepProcessUserId"},{label:"由我创建",value:"organizerId"}],onChange:()=>s()},{field:"title",label:"标题",type:"input",onChange:()=>s()},{xl:16,itemContainerStyle:{width:"100%"},field:"status",label:"工单状态",type:"radio-button",options:V(!0)},{field:"date",label:"发起时间",type:"daterange"},{field:"source",label:"来源",type:"select",options:j()},{field:"level",label:"紧急程度",type:"select",options:A()},{field:"type",label:"类型",type:"select-tree",options:G()}],operations:[{type:"btn-group",btns:[{perm:!0,type:"primary",text:"查询",iconifyIcon:"ep:search",click:()=>s()},{perm:!0,type:"default",text:"重置",iconifyIcon:"ep:refresh",click:()=>S()},{perm:!0,type:"warning",text:"导出",iconifyIcon:"ep:download",click:()=>T()}]}],handleSearch:()=>s(),defaultParams:{stepProcessUserId:"",status:""}}),t=f({expandable:!0,expandComponent:w(N),columns:H(),defaultExpandAll:!0,dataList:[],pagination:{refreshData:({page:e,size:a})=>{t.pagination.page=e,t.pagination.limit=a,s()}},operations:[{perm:!0,text:"详情",isTextBtn:!0,click:e=>P(e)}]}),P=e=>{var a;_.value=e.id||"",g.title=e.serialNo,(a=u.value)==null||a.openDrawer()},s=async()=>{var e,a,i,n,l,b,y,h;t.loading=!0;try{const r=((e=d.value)==null?void 0:e.queryParams)||{},o={page:t.pagination.page,size:t.pagination.limit||20,...r,fromTime:(a=r.date)!=null&&a[0]?I(r.date[0],x).valueOf():void 0,toTime:(i=r.date)!=null&&i[1]?I(r.date[1],x).endOf("D").valueOf():void 0};r.stepProcessUserId==="stepProcessUserId"?(o.stepProcessUserId=C(((l=(n=k().user)==null?void 0:n.id)==null?void 0:l.id)||""),o.organizerId=""):r.stepProcessUserId==="organizerId"?(o.organizerId=C(((y=(b=k().user)==null?void 0:b.id)==null?void 0:y.id)||""),o.stepProcessUserId=""):(o.organizerId="",o.stepProcessUserId=""),delete o.date;const v=(h=(await M(o)).data)==null?void 0:h.data;t.dataList=v.data,t.pagination.total=v.total}catch{}t.loading=!1},S=()=>{var e;(e=d.value)==null||e.resetForm(),s()},T=()=>{var e;(e=m.value)==null||e.exportTable()};return D(()=>{s()}),(e,a)=>{const i=E,n=B,l=q;return z(),F("div",R,[c(i,{ref_key:"refSearch",ref:d,config:O},null,8,["config"]),c(n,{ref_key:"refTable",ref:m,class:"card-table",config:t},null,8,["config"]),c(l,{ref_key:"refdetail",ref:u,config:g},{default:L(()=>[c(W,{id:_.value},null,8,["id"])]),_:1},8,["config"])])}}});export{me as default};
