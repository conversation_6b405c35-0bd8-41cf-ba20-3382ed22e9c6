import{d as h,c as s,r as i,o as b,g as k,n as v,q as t,i as a,t as y,p as C,_ as x,aq as V,C as F}from"./index-r0dFAfgr.js";import{C as w}from"./index-CcDafpIP.js";import{r as M}from"./chart-wy3NEK2T.js";const B={class:"onemap-panel-wrapper"},R={class:"table-box"},q=h({__name:"complaint",props:{view:{},menu:{}},emits:["highlightMark","addMarks"],setup(r,{emit:p}){const c=p,d=r,m=s(),o=s([{label:"0 件",value:"月度受理量"},{label:"0 件",value:"今日已办量"},{label:"0 件",value:"今日待办量"}]),n=i({indexVisible:!0,dataList:[],pagination:{hide:!0},columns:[{label:"热线编号",prop:"key1",width:90},{label:"来电号码",prop:"key2",width:90},{label:"电话来源",prop:"key3",width:90},{label:"工单类型",prop:"key4",width:90}],handleRowClick:e=>{n.currentRow=e,c("highlightMark",d.menu,e==null?void 0:e.id)}}),u=i({group:[{fieldset:{type:"underline",desc:"热线类型占比"},fields:[{type:"vchart",option:M(),style:{width:"100%",height:"150px"}}]},{fields:[{type:"input",field:"layer",append:"刷新"}]}],labelPosition:"top",gutter:12});return b(()=>{}),(e,l)=>{const f=x,_=V;return k(),v("div",B,[t(a(w),{modelValue:a(o),"onUpdate:modelValue":l[0]||(l[0]=g=>y(o)?o.value=g:null),span:8},null,8,["modelValue"]),t(f,{ref_key:"refForm",ref:m,config:a(u)},null,8,["config"]),C("div",R,[t(_,{config:a(n)},null,8,["config"])])])}}}),I=F(q,[["__scopeId","data-v-94e553a1"]]);export{I as default};
