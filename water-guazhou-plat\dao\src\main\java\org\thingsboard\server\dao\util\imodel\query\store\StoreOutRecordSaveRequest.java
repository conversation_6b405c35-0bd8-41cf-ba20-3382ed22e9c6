package org.thingsboard.server.dao.util.imodel.query.store;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.store.StoreOutRecord;
import org.thingsboard.server.dao.util.imodel.query.ComplexSaveRequest;
import org.thingsboard.server.dao.util.imodel.query.IStarHttpRequest;
import org.thingsboard.server.dao.util.imodel.query.StringSetter;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

import java.util.Date;

@Getter
@Setter
public class StoreOutRecordSaveRequest extends ComplexSaveRequest<StoreOutRecord, StoreOutRecordDetailSaveRequest> {
    // 出库单编号
    @NotNullOrEmpty
    private String code;

    // 出库单标题
    @NotNullOrEmpty
    private String title;

    // 是否报账
    private Boolean reimbursement;

    // 仓库ID
    @NotNullOrEmpty
    private String storehouseId;

    // 领用人ID
    @NotNullOrEmpty
    private String receiveUserId;

    // 经办人
    private String manager;

    // 出库类型
    private String type;

    // 施工项目ID
    private String constructionProjectId;

    // 是否为补录
    private Boolean addRecord;

    // 备注
    private String remark;

    @Override
    public String valid(IStarHttpRequest request) {
        return checkItemExistence("至少需要出库一项设备");
    }

    protected StoreOutRecord build() {
        StoreOutRecord entity = new StoreOutRecord();
        entity.setCreator(currentUserUUID());
        entity.setCreateTime(new Date());
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    protected StoreOutRecord update(String id) {
        StoreOutRecord entity = new StoreOutRecord();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(StoreOutRecord entity) {
        entity.setCode(code);
        entity.setTitle(title);
        entity.setReimbursement(reimbursement);
        entity.setStorehouseId(storehouseId);
        entity.setReceiveUserId(receiveUserId);
        entity.setManager(manager);
        entity.setType(type);
        entity.setConstructionProjectId(constructionProjectId);
        entity.setAddRecord(addRecord);
        entity.setRemark(remark);
    }

    @Override
    protected StringSetter<StoreOutRecordDetailSaveRequest> parentSetter() {
        return StoreOutRecordDetailSaveRequest::setMainId;
    }
}