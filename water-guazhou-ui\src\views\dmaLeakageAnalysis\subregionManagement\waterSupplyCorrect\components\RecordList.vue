<template>
  <DialogForm
    ref="refRecord"
    :config="RecordConfig"
  ></DialogForm>
</template>
<script lang="ts" setup>
import { GetDMASupplyTotalFlowCorrectRecords, ExportDMASupplyTotalFlowCorrectRecords } from '@/api/mapservice/dma'
import { IDialogFormIns } from '@/components/type'
import { saveAs } from '@/utils/printUtils'

const refRecord = ref<IDialogFormIns>()
const TableConfig_Records = reactive<ITable>({
  dataList: [],
  columns: [
    { label: '分区名称', prop: 'partitionName' },
    { label: '水表名称', prop: 'partitionName' },
    { label: '日期', prop: 'collectTime' },
    { label: '追加水量', prop: 'correctWater' },
    { label: '修正日期', prop: 'createTime' },
    { label: '操作人', prop: 'updateUser' }
  ],
  height: 400,
  pagination: {
    refreshData: ({ page, size }) => {
      TableConfig_Records.pagination.page = page
      TableConfig_Records.pagination.limit = size
      refreshRecords()
    }
  }
})
const RecordConfig = reactive<IDialogFormConfig>({
  title: '修正记录',
  labelWidth: 70,
  group: [
    {
      fields: [
        {
          type: 'input',
          label: '分区名称',
          field: 'partitionName',
          inputStyle: {
            width: '200px'
          },
          extraFormItem: [
            {
              type: 'btn-group',
              btns: [
                {
                  perm: true,
                  text: '查询',
                  styles: {
                    marginLeft: '20px'
                  },
                  iconifyIcon: 'ep:search',
                  click: () => refreshRecords()
                },
                {
                  perm: true,
                  text: '导出',
                  type: 'success',
                  iconifyIcon: 'ep:download',
                  click: () => refreshRecords(true)
                }
              ]
            }
          ]
        }
      ]
    },
    {
      fields: [{ type: 'table', config: TableConfig_Records }]
    }
  ]
})

const refreshRecords = async (isExport?: boolean) => {
  try {
    const params = {
      page: TableConfig_Records.pagination.page || 1,
      size: TableConfig_Records.pagination.limit || 20,
      partitionName: refRecord.value?.refForm?.dataForm.partitionName
    }
    if (isExport) {
      const res = await ExportDMASupplyTotalFlowCorrectRecords(params)
      saveAs(res.data, '供水量修正记录')
    } else {
      TableConfig_Records.loading = true
      const res = await GetDMASupplyTotalFlowCorrectRecords(params)
      const data = res.data?.data
      TableConfig_Records.dataList = data?.data || []
      TableConfig_Records.pagination.total = data?.total || 0
    }
  } catch (error) {
    //
  }
  TableConfig_Records.loading = false
}
onMounted(() => {
  refreshRecords()
})
defineExpose({
  refRecord
})
</script>
<style lang="scss" scoped></style>
