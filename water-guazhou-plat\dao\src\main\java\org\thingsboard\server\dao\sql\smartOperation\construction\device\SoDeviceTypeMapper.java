package org.thingsboard.server.dao.sql.smartOperation.construction.device;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.smartOperation.device.SoDeviceType;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.device.SoDeviceTypePageRequest;

import java.util.Collections;
import java.util.List;
import java.util.Map;

@Mapper
public interface SoDeviceTypeMapper extends BaseMapper<SoDeviceType> {
    IPage<SoDeviceType> findByPage(SoDeviceTypePageRequest request);

    boolean update(SoDeviceType entity);

    boolean updateFully(SoDeviceType entity);

    String getParentId(String id);

    Integer getDepth(String id);

    String getSerialId(String id);

    // region serial id检查
    default boolean isSerialIdExists(String serialId, String id, String tenantId) {
        boolean serialIdExists = selectCountBySerialIdAndTenantId(serialId, tenantId) > 0;
        if (id == null) {
            return serialIdExists;
        }
        return serialIdExists && selectCountByIdAndSerialId(id, serialId) == 0;
    }

    int selectCountByIdAndSerialId(@Param("id") String id, @Param("serialId") String serialId);

    int selectCountBySerialIdAndTenantId(@Param("serialId") String serialId, @Param("tenantId") String tenantId);

    // endregion

    int save(SoDeviceType entity);

    boolean canBeDelete(@Param("id") String id, @Param("tenantId") String tenantId);

    List<SoDeviceType> findRoots(String tenantId);

    List<SoDeviceType> findChildren(String parentId);

    @SuppressWarnings("unused")
    default String getTreePath(String id) {
        return (String) getTreePathMap().getOrDefault(id, Collections.emptyMap()).get("path");
    }

    @MapKey("id")
    Map<String, Map<String, Object>> getTreePathMap();

    boolean existsBySerialId(@Param("serialId") String serialId, @Param("tenantId") String tenantId);

    int getDepthBySerialId(@Param("serialId") String serialId, @Param("tenantId") String tenantId);

    String getIdBySerialId(@Param("serialId") String serialId, @Param("tenantId") String tenantId);


}
