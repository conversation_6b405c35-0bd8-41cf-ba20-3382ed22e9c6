package org.thingsboard.server.controller.smartOperation.construction.project;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.construction.project.SoDeviceItemService;
import org.thingsboard.server.dao.model.sql.smartOperation.device.SoDeviceItem;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoDeviceItemPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoDeviceItemSaveRequest;
import org.thingsboard.server.dao.util.reflection.ExceptionUtils;
import org.thingsboard.server.utils.imodel.annotations.IStarController2;

@IStarController2
@RequestMapping("/api/so/deviceItem")
public class SoDeviceItemController extends BaseController {
    @Autowired
    private SoDeviceItemService service;


    @GetMapping
    public IPage<SoDeviceItem> findAllConditional(SoDeviceItemPageRequest request) {
        request.withCode();
        return service.findAllConditional(request);
    }

    // @PostMapping
    public SoDeviceItem save(@RequestBody SoDeviceItemSaveRequest req) {
        return service.save(req);
    }

    // @PatchMapping("/{id}")
    public boolean edit(@RequestBody SoDeviceItemSaveRequest req, @PathVariable String id) {
        return service.update(req.unwrap(id));
    }

    @DeleteMapping("/{id}")
    public boolean delete(@PathVariable String id) {
        if (!service.canBeDelete(id)) {
            ExceptionUtils.silentThrow("设备已被使用，无法删除。");
        }

        return service.delete(id);
    }

}