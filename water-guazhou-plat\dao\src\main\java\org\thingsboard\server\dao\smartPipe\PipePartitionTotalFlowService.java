package org.thingsboard.server.dao.smartPipe;

import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.DTO.PartitionFlowAnalysisDTO;
import org.thingsboard.server.dao.model.DTO.PartitionSupplyCorrectRecordsDTO;
import org.thingsboard.server.dao.model.request.PartitionCustRequest;
import org.thingsboard.server.dao.model.request.PartitionTotalFlowRequest;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.PipePartitionTotalFlow;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-04-25
 */
public interface PipePartitionTotalFlowService {

    PageData<PipePartitionTotalFlow> getList(PartitionTotalFlowRequest request);

    void correct(PipePartitionTotalFlow pipePartitionTotalFlow, String userId);

    PageData<PartitionSupplyCorrectRecordsDTO> getCorrectRecords(PartitionCustRequest partitionCustRequest);

    PageData<PartitionFlowAnalysisDTO> getFlowAnalysis(PartitionTotalFlowRequest request);

    /**
     * 获取分区供水量和修正水量
     * @param partitionIdList
     * @param startTime
     * @param endTime
     * @param supplyWater
     * @param correctSupplyWater
     */
    void getSupplyAndCorrectWater(List<String> partitionIdList, Long startTime, Long endTime, Map<String, BigDecimal> supplyWater, Map<String, BigDecimal> correctSupplyWater);
}
