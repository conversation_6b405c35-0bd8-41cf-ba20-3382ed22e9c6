import{两 as h}from"./charts-CdlVhAbT.js";import p from"./SupplyItem-Ba6JtLer.js";import{G as I}from"./shuichangzonglan-HwbtusbI.js";import{z as g,d as y,r as b,o as C,ay as x,g as n,n as m,p as r,aB as d,aJ as f,h as v,i,aw as B,q as k,a0 as F,C as G}from"./index-r0dFAfgr.js";const L=()=>g({url:"/istar/api/customer/report/waterPlantMonthTotal",method:"get"}),S={class:"gsl-wrapper"},T={class:"supply-items left"},w={class:"chart"},M=y({__name:"GSL_beibei",setup(N){const t=b({leftItems:[{label:"本月供水量",unit:"万m³",value:"0.00"},{label:"上月供水量",unit:"万m³",value:"0.00"},{label:"年累计供水量",unit:"万m³",value:"0.00"}],rightItems:[],centerOption:null}),_=()=>{var u;I({stationType:"水厂",projectId:(u=F().selectedProject)==null?void 0:u.value}).then(e=>{var o,a,s;t.leftItems[0].value=((((o=e.data)==null?void 0:o.lastMonthTotal)||0)/1e4).toFixed(2),t.leftItems[1].value=((((a=e.data)==null?void 0:a.monthTotal)||0)/1e4).toFixed(2),t.leftItems[2].value=((((s=e.data)==null?void 0:s.total)||0)/1e4).toFixed(2)}).catch(()=>{t.leftItems[0].value=0,t.leftItems[1].value=0,t.leftItems[2].value=0}),L().then(e=>{var a,s,c;console.log((a=e.data)==null?void 0:a.data),t.rightItems=((c=(s=e.data)==null?void 0:s.data)==null?void 0:c.map(l=>({label:l.stationName,unit:"万m³",value:((l.value??0)/1e4).toFixed(2)})))||[];const o=t.rightItems.map(l=>({name:l.label,value:Number(l.value)}));t.centerOption=h(o)})};return C(()=>{_()}),(u,e)=>{const o=x("VChart");return n(),m("div",S,[r("div",T,[(n(!0),m(d,null,f(i(t).leftItems,(a,s)=>(n(),v(p,{key:s,class:"supply-item",config:a},null,8,["config"]))),128))]),r("div",{class:B(["supply-items right overlay-y",{"only-one":i(t).rightItems.length===1}])},[(n(!0),m(d,null,f(i(t).rightItems,(a,s)=>(n(),v(p,{key:s,class:"supply-item",config:a},null,8,["config"]))),128))],2),e[0]||(e[0]=r("div",{class:"center_item total"}," 总供水量 ",-1)),e[1]||(e[1]=r("div",{class:"center_item plant"}," 水厂供水量 ",-1)),r("div",w,[k(o,{ref:"refChart",option:i(t).centerOption},null,8,["option"])])])}}}),q=G(M,[["__scopeId","data-v-b5eed7b2"]]);export{q as default};
