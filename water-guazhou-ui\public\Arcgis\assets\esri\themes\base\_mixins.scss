////////////////////////////////////////
// Widget Common Mixins
@use "sass:math";

@mixin boxShadow($arguments) {
  box-shadow: #{$arguments};
}

@mixin defaultBoxShadow() {
  @include boxShadow("0 1px 2px rgba(0, 0, 0, 0.3)");
}

@mixin borderBoxShadow($color: $border-color) {
  box-shadow: 0 0 0 1px $color;
}

@mixin cardBoxShadow($color: $border-color) {
  box-shadow: 0 1px 0 $color;
}

@mixin pointerStyle($width, $height) {
  content: "";
  position: absolute;
  width: $width + px;
  height: $height + px;
}

@mixin outlineStyle() {
  outline: 2px solid $interactive-font-color;
  outline-offset: 2px;
  z-index: 1;
}

@function sqrt($r) {
  $x0: 1;
  $x1: $x0;
  @for $i from 1 through 10 {
    $x1: $x0 - math.div($x0 * $x0 - abs($r), 2 * $x0);
    $x0: $x1;
  }
  @return $x1;
}

@mixin contentMaxHeightDockedSide($contentHeight) {
  $pageMenuHeight: $contentHeight - 60;
  .esri-popup--is-docked {
    &-top-left,
    &-top-right,
    &-bottom-left,
    &-bottom-right {
      .esri-popup__content {
        max-height: #{$contentHeight}px;
      }
    }
  }
}

@mixin popupHeight_BasedOnViewSize($height) {
  $pageMenuHeight: $height - 50;
  .esri-popup__main-container {
    max-height: $height;
  }
  &.esri-popup--feature-menu-open {
    .esri-popup__feature-menu-viewport {
      max-height: $pageMenuHeight;
    }
  }
}

@mixin wordbreak() {
  /* For Firefox */
  white-space: pre-wrap;
  word-break: break-all;
  /**
  * IE should ignore these.
  * Modern browsers should override break-all.
  */
  word-wrap: break-word;
  word-break: break-word;
}

@mixin measurementWidgetStyles($selector) {
  .#{$selector} {
    &__container {
      position: relative;
      padding: $cap-spacing 0;
      overflow-y: auto;
      a {
        text-decoration: none;
      }
    }

    &__header {
      position: relative;
      font-size: $font-size__body;
      align-items: flex-start;
      justify-content: space-between;
      display: flex;
      flex: 0 0 auto;
    }

    &__header-title,
    h1 &__header-title {
      font: {
        size: $font-size__header-text;
        weight: $font-weight--bold;
      }
      padding: 8px 0;
      margin: 0;
      display: block;
      flex: 1;
      word-break: break-word;
      text-align: left;
    }

    &__panel--error {
      color: $font-color--error;
      padding: 0 $side-spacing;
      animation: esri-fade-in 250ms ease-in-out;
    }

    &__hint {
      padding: 0 $side-spacing;
      animation: esri-fade-in 250ms ease-in-out;

      &-text {
        margin: $cap-spacing 0;
        padding: 0;
      }
    }

    &__measurement {
      padding: $cap-spacing $side-spacing;
      margin: $cap-spacing 0;
      background-color: $background-color--offset;
      animation: esri-fade-in 250ms ease-in-out;

      &-item {
        display: flex;
        padding-bottom: $cap-spacing;
        flex-flow: column;

        &--disabled {
          display: flex;
          color: rgba($font-color, $opacity--disabled);
        }

        &-title {
          padding-bottom: $cap-spacing--quarter;
        }

        &-value {
          font-weight: $font-weight--bold;
        }
      }
    }

    &__settings {
      display: flex;
      justify-content: space-between;
      padding: $cap-spacing--half $side-spacing;
    }

    &__units {
      display: flex;
      flex: 0 1 48%;
      flex-flow: column;
      padding: 0;
      animation: esri-fade-in 250ms ease-in-out;
      &:only-child {
        flex: 1 0 100%;
      }
    }

    &__units-select {
      width: 100%;
      padding: {
        left: 0.5em;
        right: 2.7em;
      }

      &-wrapper {
        width: 100%;
      }
    }

    &__actions {
      display: flex;
      flex-flow: column;
      justify-content: center;
      padding: 0 $side-spacing;
    }
  }

  [dir="rtl"] {
    .#{$selector}__units-select {
      padding: {
        left: 2.7em;
        right: 0.5em;
      }
    }
  }
}

// Smart Mapping Sliders
@mixin smartMappingSlider($class) {
  .#{$class} {
    direction: ltr;
    min-width: $smartmapping-slider__width;

    .esri-slider {
      font-size: $font-size--small;
      position: relative;
      z-index: 1;

      .esri-slider__content {
        flex-direction: row;
        height: $smartmapping-slider__base-height;
        margin: 0 auto 0 40%;
      }

      .esri-slider__track {
        background-color: transparent;
        display: flex;
        flex: 0 0 auto;
      }

      .esri-slider__anchor {
        border-bottom: 1px solid $interactive-font-color;
        border-top: 1px solid $background-color;
        width: $smartmapping-slider__ramp-width;

        &:hover,
        &:focus {
          .esri-slider__label {
            text-decoration: underline;
          }

          .esri-slider__thumb {
            background-color: $interactive-font-color--hover;
            border: none;
            transform: none;

            &:after {
              border-left-color: $interactive-font-color--hover;
            }
            &:before {
              background-color: $button-color--bright;
              transform: translate3d(-$smartmapping-slider__thumb-size--offset * 0.25, 0, 0);
            }
          }
        }
      }

      .esri-slider__thumb {
        background-color: $smartmapping-slider__thumb-background-color;
        border-radius: 0;
        border: none;
        height: $smartmapping-slider__thumb-size;
        left: -$smartmapping-slider__thumb-size;
        top: -$smartmapping-slider__thumb-size--offset;
        width: $smartmapping-slider__thumb-size--offset;

        &:before {
          position: absolute;
          top: 0;
          left: -$smartmapping-slider__thumb-size--offset * 0.25;
          width: $smartmapping-slider__thumb-size--offset * 0.5;
          content: "";
          height: $smartmapping-slider__thumb-size;
          background-color: $interactive-font-color;
          transition: transform 125ms ease-in-out, background-color 125ms ease-in-out;
        }
        &:after {
          position: absolute;
          top: 0;
          left: $smartmapping-slider__thumb-size--offset;
          content: "";
          border-bottom: $smartmapping-slider__thumb-pointer-size solid #0000;
          border-left: $smartmapping-slider__thumb-pointer-size solid $interactive-font-color;
          border-top: $smartmapping-slider__thumb-pointer-size solid #0000;
          height: 0;
          width: 0;
        }
      }

      .esri-slider__label {
        left: auto;
        line-height: 20px;
        min-width: 50px;
        right: 50px;
        text-align: right;

        &:hover {
          background-color: $background-color--hover;
        }
      }

      .esri-slider__segment {
        &:hover {
          cursor: default;
        }
      }

      .esri-slider__range-input {
        margin: auto;
        text-align: center;
        width: 50%;
      }

      .esri-slider__label-input {
        text-align: right;
        width: 70px;
      }

      .esri-slider__max,
      .esri-slider__min {
        flex: none;
        margin: $cap-spacing--three-quarters auto;
        padding: $cap-spacing--three-quarters $cap-spacing;
        position: relative;
        width: auto;
        z-index: 0;

        &:before {
          content: "";
          position: absolute;
          left: 0;
          top: 0;
          height: 100%;
          width: 100%;
          z-index: -1;
        }

        &.esri-slider__range--active {
          background-color: $background-color;
        }

        .esri-slider__range-input {
          margin: 0 auto;
        }
      }

      .esri-slider__extra-content {
        display: flex;
        height: 100%;
      }

      .esri-histogram__svg {
        overflow: visible;
      }

      .esri-histogram__label {
        fill: $font-color;
        stroke-width: 2;
      }

      .esri-histogram__average-line,
      .esri-histogram__label {
        paint-order: stroke;
        stroke: $background-color;
      }

      .zoom-cap--max {
        position: absolute;
        top: 0;
      }

      .zoom-cap--min {
        position: absolute;
        bottom: 0;
      }

      .zoom-cap {
        height: 11px;
        width: $smartmapping-slider__ramp-width;
        stroke-width: 0;

        .zoom-cap--mask {
          fill: #fff;
        }
        .zoom-cap--line {
          fill: #fff;
        }
        .zoom-cap--underline {
          fill: #323232;
        }

        &:hover {
          cursor: pointer;

          .zoom-cap--mask {
            fill: #fff;
          }
          .zoom-cap--line {
            fill: #0079c1;
          }
          .zoom-cap--underline {
            fill: #fff;
          }
        }
      }
    }
  }

  .#{$class}__ramp {
    display: flex;
    height: 100%;
    width: $smartmapping-slider__ramp-width;
    position: relative;

    svg {
      height: 100%;
      width: 100%;
      position: absolute;
      stroke: $smartmapping-slider__ramp-stroke-color;
      stroke-width: $smartmapping-slider__ramp-stroke-width;
      left: 0;

      rect {
        height: 100%;
        width: 100%;
      }

      path {
        stroke-width: $smartmapping-slider__ramp-path-stroke-width;
      }
    }
  }

  .#{$class}__histogram-container {
    display: flex;
    flex: 1 1 0;
    height: 100%;
    width: $smartmapping-slider__histogram-width;
  }

  .#{$class}.#{$class}--interactive-track {
    .esri-slider {
      .esri-slider__content {
        margin: 0 auto 0 35%;
      }

      .esri-slider__track {
        background-color: transparent;
        width: 12px;
      }
    }

    .esri-slider__anchor--active {
      .esri-slider__label {
        text-decoration: underline;
      }
      .esri-slider__thumb {
        background-color: $interactive-font-color--hover;
        border: none;
        transform: none;
        &:after {
          border-left-color: $interactive-font-color--hover;
        }
        &:before {
          background-color: $button-color--bright;
          transform: translate3d(-$smartmapping-slider__thumb-size--offset * 0.25, 0, 0);
        }
      }
    }

    .esri-slider__segment--interactive {
      background-size: 12px 100%;
      background-repeat: no-repeat;
      background-position: 50% 50%;
      background-image: url(../base/images/SM-Slider_TrackBackground_idle.svg);

      &.esri-slider__segment--active {
        background-color: #eee;
        background-image: url(../base/images/SM-Slider_TrackBackground_hover.svg);
      }

      &:hover,
      &:focus {
        cursor: pointer;
        background-color: #eee;
        background-image: url(../base/images/SM-Slider_TrackBackground_hover.svg);
      }
    }
  }
}

@mixin loopingProgressBar($selector) {
  #{$selector}:before,
  #{$selector}:after {
    content: "";
    opacity: 1;
    position: absolute;
    height: 1px;
    top: 0;
    transition: opacity 500ms ease-in-out;
  }
  #{$selector}:before {
    background-color: $border-color;
    width: 100%;
    z-index: 0;
  }
  #{$selector}:after {
    background-color: $interactive-font-color;
    width: $looping-progress-bar-width;
    z-index: 0;
    animation: looping-progresss-bar-ani $looping-progress-bar-params;
  }
}

/**
 * Adds the base styles for an arrow facing down.
 */
@mixin arrowButton() {
  $size: 18px;
  $icon-size: 10px;
  $padding: ($size - $icon-size) * 0.5;

  font-size: $icon-size;
  line-height: $icon-size;

  width: $size;
  height: $size;
  padding: $padding;

  cursor: pointer;
  transition: all 0.1s ease-in-out;
  text-align: center;

  border: none;
  background: none;

  appearance: none;

  @each $icomoon-selector in $icomoon-selectors {
    @extend #{$icomoon-selector};
  }

  @extend .esri-icon-left;

  &:hover:not(:disabled) {
    background: $background-color--hover;
  }

  &:disabled {
    opacity: 0.4;
    cursor: default;
  }

  &.esri-arrow-down {
    transform: rotate(-90deg);
  }

  &.esri-arrow-up {
    transform: rotate(90deg);
  }

  &.esri-arrow-left {
    transform: rotate(0deg);
  }

  &.esri-arrow-right {
    transform: rotate(180deg);
  }
}

/**
* Styles for Sortable
*/
@mixin sortableChosen($selector) {
  transition: background-color 125ms ease-in-out;
  &.#{$selector} {
    background-color: $background-color--active;
    opacity: $opacity--sortable;
  }
}

@mixin timeSlider() {
  $tick-width: 1px;
  $focus-ring-width: 2px;
  $thumb-size: 16px;
  $tick-padding: 3px;
  $tick-height: 3px;
  $tick-height-primary: 6px;

  .esri-slider.esri-slider--horizontal {
    width: inherit;
    flex-grow: 1;
    padding: 30px 13px 28px 13px;

    .esri-widget__anchor,
    .esri-slider__anchor {
      outline: none !important;
      color: inherit;
    }

    .esri-slider__anchor:focus .esri-slider__thumb {
      outline: solid $focus-ring-width $border-color--active;
      outline-offset: $focus-ring-width;
      overflow: visible;
    }

    .esri-slider__segment.esri-slider__segment-0 {
      background-color: unset; // Reset blue first segment
    }

    .esri-slider__thumb {
      width: $thumb-size;
      height: $thumb-size;
      left: -$thumb-size * 0.5;
      top: -$thumb-size * 0.5;
    }

    .esri-slider__tick {
      width: $tick-width + ($tick-padding * 2);
      height: 20px;
      padding: $tick-padding; // Make ticks slightly easier to click on
      margin: -$tick-padding;
      background: none;

      &:after {
        content: "";
        display: block;
        width: $tick-width;
        background: #6e6e6e66;
      }

      &.secondary-tick:after {
        height: $tick-height;
      }

      &.primary-tick {
        &:after {
          height: $tick-height-primary;
        }

        .primary-tick__ampm {
          font-size: $font-size--tiny;
        }
      }
    }

    .esri-slider__ticks {
      margin: 0;
      margin-top: $cap-spacing--half;
      width: calc(100% - #{$tick-width});
    }

    .esri-slider__tick-label {
      font-size: $font-size--tiny-time-slider;
      line-height: normal;
      margin-top: $cap-spacing;
      text-align: center;
    }

    .esri-slider__label {
      font-size: $font-size--small;
    }

    .esri-slider__label-input {
      font-size: $font-size--tiny-time-slider;
    }
  }
}
