<template>
  <div>
    <Form
      ref="refForm"
      :config="FormConfig"
    ></Form>
  </div>
</template>
<script lang="ts" setup>
import { Printer } from '@element-plus/icons-vue'
import { v4 as uuidv4 } from 'uuid'
import PrintTemplate from '@arcgis/core/rest/support/PrintTemplate.js'
import { IFormIns } from '@/components/type'
import { getPrintFormat, getPrintTemplate } from '../../config'
import { SLMessage } from '@/utils/Message'
import {
  createPolygon,
  excutePrintTask,
  extentTo,
  getGraphicLayer,
  getScaleOnExtent,
  setSymbol
} from '@/utils/MapHelper'
import { downloadFile } from '@/utils/fileHelper'

const props = defineProps<{
  view?: __esri.MapView
}>()
const staticState: {
  graphicsLayer?: __esri.GraphicsLayer
  graphic?: __esri.Graphic
  curTemplate?: any
} = {}
const refForm = ref<IFormIns>()
const FormConfig = reactive<IFormConfig>({
  gutter: 12,
  labelPosition: 'top',
  group: [
    {
      fieldset: {
        desc: '标题'
      },
      fields: [
        {
          type: 'input',
          field: 'title',
          rules: [{ required: true, message: '请输入标题' }]
        }
      ]
    },
    {
      fieldset: {
        desc: '格式'
      },
      fields: [
        {
          type: 'select',
          field: 'type',
          clearable: false,
          options: getPrintFormat()
        }
      ]
    },
    {
      id: 'print-template',
      fieldset: {
        desc: '模板'
      },
      fields: [
        {
          type: 'select',
          clearable: false,
          field: 'template',
          options: getPrintTemplate()
        }
      ]
    },
    {
      fieldset: {
        desc: '比例尺'
      },
      fields: [
        {
          type: 'input-number',
          field: 'scale',
          placeholder: '比如：500',
          prepend: '1:',
          rules: [{ required: true, message: '请输入比例尺' }]
        }
      ]
    },
    {
      fieldset: {
        desc: '选取范围'
      },
      fields: [
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: '点击生成范围',
              styles: {
                width: '100%'
              },
              click: async () => {
                try {
                  const res = await refForm.value?.Submit()
                  if (res !== false) {
                    drawRect()
                    staticState.graphic
                      && extentTo(
                        props.view,
                        staticState.graphic.geometry.extent,
                        true
                      )
                  }
                } catch (error) {
                  console.log(error)
                }
              },
              type: 'success'
            }
          ]
        }
      ]
    },
    {
      fieldset: {
        desc: '高级设置'
      },
      fields: [
        { type: 'input', label: '出图人员', field: 'username' },
        { type: 'input', label: '出图单位', field: 'depart' },
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              svgIcon: shallowRef(Printer),
              text: '打印',
              click: () => handlePrint()
            }
          ]
        }
      ]
    },
    {
      id: 'print-result',
      fieldset: {
        desc: '打印结果'
      },
      fields: [
        {
          type: 'list',
          data: [],
          style: { height: '100px' },
          itemStyle: row => {
            return {
              textDecoration: row.url ? 'underline' : 'none',
              color: '#00ff00'
            }
          },
          formatter: (val, row) => {
            if (!val) return val
            return (
              val
              + (row.status === 'success'
                ? '（打印完成,点击下载！）'
                : row.status === 'printing'
                  ? '（正在打印...）'
                  : row.status === 'failed'
                    ? '打印失败!'
                    : '')
            )
          },
          displayField: 'title',
          nodeClick: item => {
            item.url && downloadFile(item.url, item.title)
          }
        }
      ]
    }
  ],
  defaultValue: {
    type: 'pdf',
    template: 'a3-landscape',
    scale: 500
  }
})
const drawRect = (type: 'update' | 'add' = 'add') => {
  if (!props.view) return
  const scale = refForm.value?.dataForm.scale
  if (!scale) {
    type === 'add' && SLMessage.warning('请输入比例尺')
    return
  }
  const template = refForm.value?.dataForm.template
  if (!template) {
    type === 'add' && SLMessage.warning('请选择模板')
    return
  }
  staticState.graphicsLayer?.removeAll()
  staticState.graphic = undefined
  const radio = 1 // 投影坐标系下值取1,地理坐标系下为:180 / (Math.PI * 6378137);//实际距离换算到地图距离的参数
  const tempField = FormConfig.group.find(item => item.id === 'print-template')
    ?.fields[0] as IFormSelect
  const templateObj = tempField?.options?.find(
    item => item.value === template
  )?.data
  if (!templateObj) return
  staticState.curTemplate = templateObj
  const width = ((templateObj.pageSize[0] * scale) / 100) * radio
  const height = ((templateObj.pageSize[1] * scale) / 100) * radio
  const vertices: number[][] = []
  const mapCenter = props.view.center
  vertices.push([mapCenter.x - width / 2, mapCenter.y + height / 2])
  vertices.push([mapCenter.x + width / 2, mapCenter.y + height / 2])
  vertices.push([mapCenter.x + width / 2, mapCenter.y - height / 2])
  vertices.push([mapCenter.x - width / 2, mapCenter.y - height / 2])
  const graphic = createPolygon(
    vertices,
    props.view.spatialReference,
    setSymbol('polygon', {
      outlineColor: [255, 0, 0],
      color: [0, 0, 0, 0.3],
      outlineWidth: 1
    }),
    {
      id: 'template-rect'
    }
  )
  staticState.graphic = graphic
  graphic && staticState.graphicsLayer?.add(graphic)
}
const onMapDrag = () => {
  // if (!staticState.isDragging) return
  drawRect('update')
}
const bindViewEvent = () => {
  // props.view?.on('pointer-down', onPointerDown)
  props.view?.on('drag', onMapDrag)
}
const destroy = () => {
  staticState.graphicsLayer && props.view?.map.remove(staticState.graphicsLayer)
}
const handlePrint = async () => {
  if (!props.view) return
  const res = await refForm.value?.Submit()
  if (res === false) return
  if (!staticState.graphic) {
    SLMessage.warning('请先生成打印范围')
    return
  }
  const result = {
    id: uuidv4(),
    url: '',
    status: 'printing',
    title: refForm.value?.dataForm.title
  }
  const printResultList = FormConfig.group.find(
    item => item.id === 'print-result'
  )?.fields[0] as IFormList
  printResultList.data.unshift(result)
  try {
    // const mapjson = toJson(props.view, '0')
    // const dataForm = refForm.value?.dataForm || {}
    // mapjson.layoutOptions.authorText = dataForm.username
    // mapjson.layoutOptions.copyrightText = dataForm.depart

    // mapjson.layoutOptions.titleText = dataForm.title
    // mapjson.mapOptions.scale = getScaleOnExtent(staticState.graphic?.geometry?.extent)
    // mapjson.mapOptions.extent = staticState.graphic?.geometry.extent.toJSON()

    // mapjson.layoutOptions.customTextElements.push({ company: dataForm.depart })
    // const url = window.SITE_CONFIG.GIS_CONFIG.gisService + window.SITE_CONFIG.GIS_CONFIG.gisPrintGPService
    // const jobinfo = await submitGPJob(url, {
    //   Web_Map_as_JSON: JSON.stringify(mapjson),
    //   Format: refForm.value?.dataForm.type, // "PDF"
    //   Layout_Template: refForm.value?.dataForm.template, // that.domObj.find(".layout").val(),//"DefaultA0L"
    //   Georef_info: 'False',
    //   Template_Folder: window.SITE_CONFIG.GIS_CONFIG.gisPrintTemplatePath + '/0'
    // })
    // await jobinfo.waitForJobCompletion()
    // if (jobinfo.jobStatus === 'job-succeeded') {
    //   // const res = await jobinfo.fetchResultData('summary')
    //   result.title = (refForm.value?.dataForm.title || '') + '（打印完成）'
    // } else if (jobinfo.jobStatus === 'job-cancelled') {
    //   result.title = (refForm.value?.dataForm.title || '') + '（取消打印）'
    // } else if (jobinfo.jobStatus === 'job-cancelling') {
    //   result.title = (refForm.value?.dataForm.title || '') + '（正在取消）'
    // } else if (jobinfo.jobStatus === 'job-failed') {
    //   result.title = (refForm.value?.dataForm.title || '') + '（打印失败）'
    // }
    const dataForm = refForm.value?.dataForm || {}
    const scale = getScaleOnExtent(staticState.graphic?.geometry?.extent)
    console.log('scale:' + scale)

    const res = await excutePrintTask(
      props.view,
      new PrintTemplate({
        format: dataForm.type,
        exportOptions: {
          dpi: 96
          // width: staticState.curTemplate?.width,
          // height: staticState.curTemplate?.height
        },
        outScale: dataForm.scale || 500,
        layout: dataForm.template,
        layoutOptions: {
          titleText: dataForm.title,
          authorText: dataForm.username,
          copyrightText: dataForm.depart,
          scalebarUnit: 'Meters',
          elementOverrides: {
            'North Arrow': {
              visible: true
            }
          }
        },
        showLabels: true
      })
    )
    result.status = 'success'
    result.url = res.url
  } catch (error) {
    result.status = 'failed'
  }
  const index = printResultList.data.findIndex(item => item.id === result.id)
  index !== -1 && printResultList.data.splice(index, 1)
  printResultList.data.unshift(result)
}
onMounted(() => {
  if (!props.view) {
    SLMessage.error('地图未加载，请稍候再试')
    return
  }
  staticState.graphicsLayer = getGraphicLayer(props.view, {
    id: 'template-print',
    title: '模板打印绘制图层'
  })
  bindViewEvent()
})
onBeforeUnmount(() => {
  destroy()
})
</script>
<style lang="scss" scoped></style>
