import{_ as V}from"./Panel-DyoxrWMd.js";import{d as H,c,r as I,b as u,W,bB as $,j as C,am as j,Q as q,ay as U,g as A,n as X,q as D,F as L,i as f,h as Y,p as Q,an as J,_ as K,C as tt}from"./index-r0dFAfgr.js";import{g as et}from"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./geometryEngineBase-BhsKaODW.js";import{g as rt}from"./ToolHelper-BiiInOzB.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import{g as ot}from"./LayerHelper-Cn-iiqxI.js";import"./project-DUuzYgGl.js";import{u as it}from"./arcWidgetButton-0glIxrt7.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import{H as at}from"./pipeAnalys-BoQOC96l.js";import pt from"./RightDrawerMap-D5PhmGFO.js";import{m as nt}from"./max-CCqK09y5.js";import{m as st}from"./min-ks0CS-3r.js";import"./v4-SoommWqA.js";import"./widget-BcWKanF2.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./pipe-nogVzCHG.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./ArcView-DpMnCY82.js";import"./ViewHelper-BGCZjxXH.js";import"./fieldconfig-Bk3o1wi7.js";import"./ArcStationWarning-BF9YrSzF.js";/* empty css                         */import"./useWaterPoint-Bv0z6ym6.js";import"./DateFormatter-Bm9a68Ax.js";import"./zhandian-YaGuQZe6.js";import"./useStation-DJgnSZIA.js";import"./DrawerBox-CLde5xC8.js";import"./SideDrawer-CBntChyn.js";import"./PipeDetail-CTBPYFJW.js";import"./Search-NSrhrIa_.js";import"./FormTableColumnFilter-BT7pLXIC.js";import"./QueryHelper-ILO3qZqg.js";import"./config-fy91bijz.js";import"./ArcBRTools-BO92yznB.js";import"./PipeLength.vue_vue_type_script_setup_true_lang-BZfUsvDf.js";import"./StatisticsHelper-D-s_6AyQ.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./wfsUtils-DXofo3da.js";import"./usePipelineGroup-Ba1pmWz_.js";import"./GroupLayer-DhhN3FyN.js";import"./lazyLayerLoader-DbM9sT1W.js";import"./WFSLayer-1TtJp3nx.js";import"./clientSideDefaults-VQhQaYxh.js";import"./QueryEngineCapabilities-Dk3NJwmm.js";import"./wfsUtils-Du031XaC.js";import"./geojson-MBFu2-HZ.js";import"./xmlUtils-CtUoQO7q.js";import"./ListWindow-WS05QqV0.js";import"./PopLayout-BP55MvL7.js";import"./PoiSearchV2-D7yNeLuv.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./URLHelper-B9aplt5w.js";import"./useLayerList-DmEwJ-ws.js";import"./useScaleBar-Beed-z91.js";import"./_baseExtremum-UssVWohW.js";import"./_baseLt-svgXHEqw.js";const lt={class:"chart-box"},mt=H({__name:"AcrossAnalys",setup(ct){const k=c(),d=c(),h=c(),g=c(),e=I({mounted:!1,curOperate:"",tabs:[],chartOption:null}),t={soeResult:[],pipeInfo:{ID:[],ZGround:[],Distance:[],Depth:[],Diameter:[],Z:[],Material:[]}},F=c(),_=I({columns:[{label:"设备类型",prop:"name"},{label:"数量",prop:"count",formatter:r=>{var o;return(o=r.data)==null?void 0:o.length}}],dataList:[],pagination:{hide:!0}}),G=I({group:[{fieldset:{desc:"绘制剖面线"},fields:[{type:"btn-group",btns:[{perm:!0,type:"warning",loading:()=>e.curOperate==="analysing",disabled:()=>e.curOperate==="analysing",text:()=>e.curOperate==="picking"?"正在绘制剖面线":e.curOperate==="analysing"?"分析中":"点击绘制剖面线",click:()=>M(),styles:{width:"100%"}}]}]},{fieldset:{desc:"分析结果"},fields:[{type:"table",style:{height:"250px"},config:_},{type:"btn-group",itemContainerStyle:{marginTop:"20px",marginBottom:"8px"},btns:[{perm:!0,type:"success",text:()=>e.curOperate==="analysing"?"正在分析...":"开始分析",loading:()=>e.curOperate==="analysing",disabled:()=>e.curOperate==="picking",click:()=>B(),styles:{width:"100%"}}]},{type:"btn-group",btns:[{perm:!0,text:"详细信息",disabled:()=>e.curOperate!=="viewingDetail"&&e.curOperate!=="analysed",loading:()=>e.curOperate==="detailing",click:()=>N(),styles:{width:"100%"}}]}]}],labelPosition:"top",gutter:12}),M=async()=>{var r,o;t.view&&(Z(),(r=d.value)==null||r.Close(),e.curOperate="picking",(o=t.sketch)==null||o.create("polyline"))},Z=()=>{var r,o;(r=t.graphicsLayer)==null||r.removeAll(),(o=g.value)==null||o.clearDetailData()},B=async()=>{var r,o,l,n,s;if(!t.view){u.error("地图服务未就绪，请稍候再试");return}if(!((o=(r=t.geometry)==null?void 0:r.paths)!=null&&o.length)){u.warning("请先绘制剖面线");return}e.curOperate="analysing";try{const m=new et({geometry:t.geometry}),i=(window.SITE_CONFIG.SITENAME==="qingyang"?rt(m):m).geometry,p=await at({UserToken:W().gToken||"",X1:i.paths[0][0][0],X2:i.paths[0][1][0],Y1:i.paths[0][0][1],Y2:i.paths[0][1][1],f:"pjson"});if(t.soeResult=((l=p.data)==null?void 0:l.Values)||[],p.data.Status!=="successed"){u.error("分析失败"),e.curOperate="";return}if(e.tabs=[],t.pipeInfo.ID=[],t.pipeInfo.ZGround=[],t.pipeInfo.Distance=[],t.pipeInfo.Depth=[],t.pipeInfo.Diameter=[],t.pipeInfo.Z=[],t.pipeInfo.Material=[],t.soeResult.map(a=>{var S;const b=(S=a.PipeLineNameDefinition||a.PipePointNameDefinition)==null?void 0:S.split(":");let y="",x="";if(b.length===2){y=b[0],x=b[1];const O=e.tabs.find(R=>R.label===y);O?O.data.push(x):e.tabs.push({label:y,name:y,data:[x]})}t.pipeInfo.ID.push(a.ID),t.pipeInfo.ZGround.push(a.ZGround),t.pipeInfo.Distance.push(a.Distance),t.pipeInfo.Depth.push(a.Depth),t.pipeInfo.Diameter.push(a.Diameter),t.pipeInfo.Z.push(a.Z),t.pipeInfo.Material.push(a.Material)}),!e.tabs.length){e.curOperate="analysed",u.warning("无相关数据");return}_.dataList=e.tabs,(n=h.value)==null||n.clear(),e.chartOption=null,(s=d.value)==null||s.Open(),e.chartOption=w(),$(()=>{var a;(a=h.value)==null||a.resize()})}catch{u.error("系统错误"),e.curOperate=""}e.curOperate="analysed"},w=()=>{const r=[],o=t.pipeInfo.ZGround.map((i,p)=>{const a=[t.pipeInfo.Distance[p],t.pipeInfo.ZGround[p]];return r.push(a),[t.pipeInfo.Distance[p],t.pipeInfo.Z[p]]}),l=nt(t.pipeInfo.Diameter)||0,n=st(t.pipeInfo.Diameter)||0,s=5,m=10;return{legend:{textStyle:{color:C().isDark?"#fff":""}},toolbox:{show:!0,feature:{saveAsImage:{show:!0,title:"保存为图片"}}},dataZoom:[{show:!0,type:"inside",start:0,end:100,textStyle:{color:"#666"}},{start:0,end:100}],tooltip:{trigger:"axis",formatter:i=>{if(i.seriesName==="地面")return"地面点高程："+t.pipeInfo.ZGround[i.dataIndex].toFixed(2);if(i[0].seriesName==="管点")return"管径："+t.pipeInfo.Diameter[i[0].dataIndex]+"<br/>材质: "+t.pipeInfo.Material[i[0].dataIndex]+"<br/>埋深: "+Number(t.pipeInfo.Depth[i[0].dataIndex].toFixed(2))/8}},xAxis:{type:"value",show:!0,position:"bottom",name:"距离",nameLocation:"end",nameTextStyle:{},boundaryGap:[0,0],min:null,max:null,color:"#A9D2E1",axisLabel:{formatter:"{value}m",textStyle:{color:"#666"}},splitLine:{show:!1,color:"#00ff00"},splitArea:{show:!1}},yAxis:{name:"高程",type:"value",scale:!0,color:"#A9D2E1",axisLabel:{formatter:"{value}m",textStyle:{color:"#666"}},splitArea:{show:!1},splitLine:{lineStyle:{color:"#FFFFFF",opacity:.2,type:"dashed"}}},grid:{left:100},series:[{name:"管点",type:"scatter",tooltip:{trigger:"axis"},legendHoverLink:!0,symbol:"emptyCircle",symbolSize(i){let p;for(let a=0;a<o.length;a++)o[a][1]===i[1]&&(p=a);return l===n?s:s+(t.pipeInfo.Diameter[p]-n)/(l-n)*(m-s)},label:{show:!0,formatter:i=>t.pipeInfo.ID[i.dataIndex],position:"bottom",textStyle:{color:"#fff",align:"right",baseline:"bottom",fontSize:"10px"}},itemStyle:{color:"red",borderWidth:2,borderColor:"#070707"},emphasis:{utenStyle:{color:"#aa0000",borderWidth:2,borderColor:"#070707"}},data:o},{name:"地面",type:"line",clickable:!0,data:r,markePoint:{},itemStyle:{lineStyle:{width:2,color:"#aaaaaa"},areaStyle:{color:"#AE6F39",type:"default"}},label:{show:!0,formatter:i=>i.data[0]<t.pipeInfo.Distance[i.dataIndex-1]+5&&i.dataIndex>0?"":i.data[1].toFixed(1)},markeLine:{data:[{type:"average",name:"平均高程"}]},stack:null,xAxisIndexs:0,yAxisIndex:0,symbol:null,symbolSize:6,symbolRotate:null,showAllSymbol:!1,dataFilter:"nearst",legendHoverLink:!0}]}},N=()=>{var r;e.curOperate="detailing",(r=g.value)==null||r.refreshDetail(e.tabs)},{initSketch:P,destroySketch:E}=it(),v=r=>{var o;r.state==="complete"&&(t.geometry=(o=r==null?void 0:r.graphics[0])==null?void 0:o.geometry,e.curOperate="picked")},T=r=>{e.mounted=!0,t.view=r,t.graphicsLayer=ot(t.view,{id:"pipe-analys-across",title:"横剖面分析"}),t.sketch=P(t.view,t.graphicsLayer,{updateCallBack:v,createCallBack:v})};return j(()=>C().isDark,()=>{w()}),q(()=>{var r,o;(r=d.value)==null||r.Close(),(o=t.graphicsLayer)==null||o.removeAll(),E()}),(r,o)=>{const l=K,n=U("VChart"),s=V;return A(),X("div",{ref_key:"refBox",ref:k,class:"across-page"},[D(pt,{ref_key:"refMap",ref:g,title:"横剖面分析","full-content":!0,onMapLoaded:T,onDetailRefreshed:o[0]||(o[0]=m=>f(e).curOperate="analysed")},{default:L(()=>[D(l,{ref_key:"refForm",ref:F,config:f(G)},null,8,["config"]),f(e).mounted?(A(),Y(s,{key:0,ref_key:"refChartPanel",ref:d,telport:f(k),"custom-class":"gis-across-analys-panel",title:"横剖面分析结果"},{default:L(()=>[Q("div",lt,[D(n,{ref_key:"refChart",ref:h,option:f(e).chartOption},null,8,["option"])])]),_:1},8,["telport"])):J("",!0)]),_:1},512)],512)}}}),Ir=tt(mt,[["__scopeId","data-v-81d9df23"]]);export{Ir as default};
