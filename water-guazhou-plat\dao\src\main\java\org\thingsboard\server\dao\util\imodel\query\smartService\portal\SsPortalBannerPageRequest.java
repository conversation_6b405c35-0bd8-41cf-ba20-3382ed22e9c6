package org.thingsboard.server.dao.util.imodel.query.smartService.portal;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartService.portal.SsPortalBanner;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;

@Getter
@Setter
public class SsPortalBannerPageRequest extends AdvancedPageableQueryEntity<SsPortalBanner, SsPortalBannerPageRequest> {
    // 是否启用
    private Boolean active;

}
