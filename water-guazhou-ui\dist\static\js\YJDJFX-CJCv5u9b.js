import{G as f}from"./index-CpGhZCTT.js";import{d as m,r as d,o as y,ay as u,g as b,n as h,q as v,i as _,l,C as x}from"./index-r0dFAfgr.js";const L={class:"chart-wrapper"},w=m({__name:"YJDJFX",setup(O){const c=s=>{const r=s.map(a=>a.name),e=s.map(a=>a.value),t=20,o="#0099FF",n="事件类型";return{tooltip:{trigger:"item"},grid:{left:20,right:20,bottom:10,top:40,containLabel:!0},xAxis:[{boundaryGap:t/2,type:"category",data:r,axisLine:{show:!1},splitLine:{show:!1},axisLabel:{color:"#fff",margin:12},axisTick:{show:!1}}],yAxis:[{type:"value",splitLine:{show:!1},axisLine:{show:!1},axisTick:{show:!1},axisLabel:{show:!1}}],series:[{name1:"bar0-0",color:o,silent:!0,symbol:"diamond",type:"pictorialBar",symbolSize:[t,t/2],symbolOffset:[0,-t/4],data:e.map(a=>({value:a,symbolPosition:"end"}))},{name:n,color:o,type:"bar",data:e,barGap:0,hoverAnimation:!1,barWidth:t/2,label:{show:!0,position:"top",distance:10,color:"#fff",offset:[t/4,0]},itemStyle:{opacity:.8}},{name:n,color:o,type:"bar",data:e,barGap:0,barWidth:t/2,hoverAnimation:!1,label:{show:!1,position:"top",distance:10,color:"#fff"},itemStyle:{opacity:.8}},{name1:"bar0-1",color:o,symbol:"diamond",type:"pictorialBar",silent:!0,symbolSize:[t,t/2],symbolOffset:[0,t/4],data:e}]}},i=d({eventLevenOption:null}),p=()=>{f({fromTime:l().startOf("y").valueOf(),toTime:l().valueOf(),statisticType:!0}).then(s=>{var t,o,n;const e=(((n=(o=(t=s.data)==null?void 0:t.data)==null?void 0:o.types)==null?void 0:n.data)||{}).map(a=>({name:a.key,value:a.value}));i.eventLevenOption=c(e)})};return y(()=>{p()}),(s,r)=>{const e=u("VChart");return b(),h("div",L,[v(e,{option:_(i).eventLevenOption},null,8,["option"])])}}}),C=x(w,[["__scopeId","data-v-127874de"]]);export{C as default};
