import{Y as T,l as A}from"./index-r0dFAfgr.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./GraphicsLayer-DTrBRwJQ.js";import{e as I,i as D}from"./QueryHelper-ILO3qZqg.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";const y=["ENABLED","LINKID","AUDITEDDATE","AUDITEDISPASS","REPAIRNUMBER","PROJECTNAME","COMPLETIONDATE","HASAT<PERSON>CHFI<PERSON>","UPDATEDBY","UPDATEDDATE","PERCENTOPEN","CURRENTLYOPEN","OPENCLOSE","OUTFILE","所属水厂","所属片区","ANCILLARYROLE","USERCOUNT","CREATEDDATE","ATTACH_SID","BYPASSVALVE","NORMALLYOPEN","OPERABLE","HYDRANTVALVE","WATERTYPE","LIFECYCLESTATUS","ADMINISTRATIVEAREA","SHAPE.LEN"],V=async(d,u)=>{if(d===void 0)return[];const a=["BURYTYPE","SID","DIAMETER","MATERIAL","PIPELENGTH","LANEWAY"],i=[...a],r=(await T(d)).data,o=[],s=[];return r.fields.map(e=>{var t,p;if(y.indexOf(e.name)===-1){if(e.type==="esriFieldTypeOID"){const l={type:"input",label:e.alias,field:e.name,disabled:!0,placeholder:"自动填充"};s.push(l)}if(e.type==="esriFieldTypeString"){const l={type:"input",label:e.alias,field:e.name,disabled:e.editable!==!0,placeholder:"请输入"+e.alias,maxlength:e.length,rules:!e.nullable||a.includes(e.name)?[{required:!0,message:"请输入"+e.alias}]:[]};s.push(l)}if(e.type==="esriFieldTypeDouble"){const l={type:"input-number",label:e.alias,field:e.name,readonly:e.editable!==!0,placeholder:"请输入"+e.alias,rules:!e.nullable||a.includes(e.name)?[{required:!0,message:"请输入"+e.alias}]:[]};s.push(l)}if(e.type==="esriFieldTypeDate"){const l={type:"date",label:e.alias,field:e.name,readonly:e.editable!==!0,placeholder:"请选择"+e.alias,disabledDate:n=>Date.now()<n.valueOf(),formatter:(n,c,f)=>(console.log(n,c,f),A(n,"YYYY-MM-DD").format("YYYYMMDD")),rules:!e.nullable||a.includes(e.name)?[{required:!0,message:"请选择"+e.alias}]:[]};s.push(l)}if(e.type==="esriFieldTypeInteger"){const l={type:"number",label:e.alias,field:e.name,controlPosition:"right",min:0,readonly:e.editable!==!0,placeholder:"请输入"+e.alias,rules:!e.nullable||a.includes(e.name)?[{required:!0,message:"请输入"+e.alias}]:[]};s.push(l)}if(e.type==="esriFieldTypeSmallInteger"){const l={type:"radio",label:e.alias,field:e.name,readonly:e.editable!==!0,placeholder:"请选择"+e.alias,rules:!e.nullable||a.includes(e.name)?[{required:!0,message:"请选择"+e.alias}]:[],options:[]};(p=(t=e.domain)==null?void 0:t.codedValues)!=null&&p.length&&e.domain.type==="codeValue"?l.options=e.domain.codeValues.map(n=>({label:n.name,value:n.code})):l.options=[{label:"是",value:1},{label:"否",value:0}],s.push(l)}}}),s.map(e=>{if(!e.field)return;e.field.toLowerCase()==="sid"&&(e.rules=[{required:!0,message:"请输入编号"},{asyncValidator:(p,l,n)=>{b(l,d,u,n)}}]),i.indexOf(e.field)!==-1?o.splice(1,0,e):o.push(e)}),o},b=async(d,u,a,i)=>{const E=window.SITE_CONFIG.GIS_CONFIG.gisService+window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService;let r;try{r=await I(`${E}/${u}`,D({returnGeometry:!1,where:"SID='"+d+"'"}))}catch{r=null}if(r!=null&&r.length){const o=(a==null?void 0:a.OBJECTID)!==void 0&&(a==null?void 0:a.OBJECTID)!==null,s=o&&r.includes(a.OBJECTID);return o?s?r.length>1?i(new Error("当前编号重复")):i():i("当前编号已存在"):i(new Error("当前编号已存在"))}return i()};export{V as i,y as s};
