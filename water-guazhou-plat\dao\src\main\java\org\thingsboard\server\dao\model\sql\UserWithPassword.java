package org.thingsboard.server.dao.model.sql;

import com.baomidou.mybatisplus.annotation.TableName;
import com.datastax.driver.core.utils.UUIDs;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.User;
import org.thingsboard.server.common.data.id.CustomerId;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.id.UserId;
import org.thingsboard.server.common.data.security.Authority;
import org.thingsboard.server.dao.model.BaseSqlEntity;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.model.SearchTextEntity;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;

import java.util.UUID;

import static org.thingsboard.server.common.data.UUIDConverter.fromString;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.USER_PG_HIBERNATE_COLUMN_FAMILY_NAME)
@TableName("tb_user")
public class UserWithPassword extends BaseSqlEntity<User> implements SearchTextEntity<User> {

    @Column(name = ModelConstants.USER_TENANT_ID_PROPERTY)
    private String tenantId;

    @Column(name = ModelConstants.USER_CUSTOMER_ID_PROPERTY)
    private String customerId;

    @Enumerated(EnumType.STRING)
    @Column(name = ModelConstants.USER_AUTHORITY_PROPERTY)
    private Authority authority;

    @Column(name = ModelConstants.USER_EMAIL_PROPERTY, unique = true)
    private String email;

    @Column(name = ModelConstants.SEARCH_TEXT_PROPERTY)
    private String searchText;

    @Column(name = ModelConstants.USER_FIRST_NAME_PROPERTY)
    private String firstName;

    @Column(name = ModelConstants.USER_LAST_NAME_PROPERTY)
    private String lastName;

    @Column(name = ModelConstants.USER_DEPARTMENT_ID)
    private String departmentId;

    private String password;

    private transient boolean status;

    private transient String departmentName;

    private transient String organizationName;


    private transient String roleName;

    @Type(type = "json")
    @Column(name = ModelConstants.USER_ADDITIONAL_INFO_PROPERTY)
    private JsonNode additionalInfo;

    @Column
    private String phone;

    @Column(name = ModelConstants.USER_SERIAL_NO)
    private String serialNo;

    @Column(name = ModelConstants.USER_SERIAL_LOGIN_NAME)
    private String loginName;


    private transient String UserId;

    public UserWithPassword() {

    }
    @Override
    public String getSearchTextSource() {
        return email;
    }

    @Override
    public void setSearchText(String searchText) {
        this.searchText = searchText;
    }

    @Override
    public User toData() {
        User user = new User(new UserId(super.getId()));
        user.setCreatedTime(UUIDs.unixTimestamp(super.getId()));
        user.setAuthority(authority);
        if (tenantId != null) {
            user.setTenantId(new TenantId(fromString(tenantId)));
        }
        if (customerId != null) {
            user.setCustomerId(new CustomerId(fromString(customerId)));
        }
        user.setPassword(password);
        user.setEmail(email);
        user.setFirstName(firstName);
        user.setLastName(lastName);
        user.setAdditionalInfo(additionalInfo);
        user.setPhone(phone);
        user.setSerialNo(serialNo);
        user.setLoginName(loginName);
        user.setDepartmentName(departmentName);
        user.setDepartmentId(departmentId);
        user.setOrganizationName(organizationName);
        user.setRoleName(roleName);
        user.setStatus(status);
        return user;
    }
}