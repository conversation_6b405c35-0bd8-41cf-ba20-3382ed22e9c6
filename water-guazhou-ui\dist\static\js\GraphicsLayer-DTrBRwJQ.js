import{e as r,y as i,a}from"./Point-WxyopZva.js";import"./index-r0dFAfgr.js";import{c as p,t as h,i as s,x as n,d as c,e as d}from"./MapView-DaoQedLH.js";let t=class extends p(h(d)){constructor(e){super(e),this.elevationInfo=null,this.graphics=new s,this.screenSizePerspectiveEnabled=!0,this.type="graphics",this.internal=!1}destroy(){this.removeAll(),this.graphics.destroy()}add(e){return this.graphics.add(e),this}addMany(e){return this.graphics.addMany(e),this}removeAll(){return this.graphics.removeAll(),this}remove(e){this.graphics.remove(e)}removeMany(e){this.graphics.removeMany(e)}on(e,o){return super.on(e,o)}graphicChanged(e){this.emit("graphic-update",e)}};r([i({type:n})],t.prototype,"elevationInfo",void 0),r([i(c(s,"graphics"))],t.prototype,"graphics",void 0),r([i({type:["show","hide"]})],t.prototype,"listMode",void 0),r([i()],t.prototype,"screenSizePerspectiveEnabled",void 0),r([i({readOnly:!0})],t.prototype,"type",void 0),r([i({constructOnly:!0})],t.prototype,"internal",void 0),t=r([a("esri.layers.GraphicsLayer")],t);const g=t;export{g as h};
