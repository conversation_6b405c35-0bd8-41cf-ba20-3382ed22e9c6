package org.thingsboard.server.dao.feignService;

import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.dao.model.sql.AppRole;

import java.util.List;

@Component
@FeignClient("base-service")
public interface AppRoleApi {

    @GetMapping("api/app/role/{id}")
    AppRole findById(@PathVariable String id);

    /**
     * 查询所有
     *
     * @return
     */
    @GetMapping("api/app/role/list/all")
    List<AppRole> findAll();

    /**
     * 按应用分类id查询
     *
     * @param appTypeId
     * @return
     */
    @GetMapping("api/app/role/appTypeId/{appTypeId}")
    List<AppRole> findByAppTypeId(@PathVariable String appTypeId);

    @PostMapping("api/app/role")
    AppRole save(@RequestBody AppRole appRole);

    @PutMapping("api/app/role")
    AppRole update(@RequestBody AppRole appRole);

    @DeleteMapping("api/app/role/{id}")
    AppRole deleteById(@PathVariable String id);

}
