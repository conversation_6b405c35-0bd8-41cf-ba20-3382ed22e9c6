import{_ as S}from"./index-C9hz-UZb.js";import{d as L,a6 as q,c as u,r as b,am as B,j as v,l as n,o as H,ay as V,g as w,n as A,q as p,F as E,p as s,aq as N,C as G}from"./index-r0dFAfgr.js";import{_ as P}from"./CardSearch-CB_HNR-Q.js";import{z as T,a as x}from"./echart-BoVIcYbV.js";import{G as z}from"./index-CpGhZCTT.js";import"./Search-NSrhrIa_.js";import"./config-DqqM5K5L.js";import"./DateFormatter-Bm9a68Ax.js";import"./OutsideWorkOrder-C6s8joBt.js";const R={class:"wrapper"},U={class:"card-content"},j={class:"top"},I={class:"table-box"},W={class:"bottom"},J=L({__name:"statistics",setup(K){const Y=q(),_=u(),O=u(),f=u(),o=b({barOption:null,lineOption:null});B(()=>v().isDark,()=>{o.lineOption.backgroundColor=v().isDark?"#131624":"#F4F7FA",o.barOption.backgroundColor=v().isDark?"#131624":"#F4F7FA"});const g=u(),F=b({filters:[{type:"radio-button",label:"发起时间",options:[{label:"日",value:"date"},{label:"月",value:"month"},{label:"年",value:"year"}],field:"type",onChange:()=>y()},{clearable:!1,handleHidden:(a,e,t)=>{t.hidden=a.type!=="date"},type:"date",label:"",field:"date"},{clearable:!1,handleHidden:(a,e,t)=>{t.hidden=a.type!=="month"},type:"month",label:"",field:"month"},{clearable:!1,handleHidden:(a,e,t)=>{t.hidden=a.type!=="year"},type:"year",label:"",field:"year"},{type:"btn-group",btns:[{perm:!0,text:"统计",click:()=>y()}]}],defaultParams:{type:"date",month:n().format("YYYY-MM"),year:n().format("YYYY"),date:n().format("YYYY-MM-DD")}}),h=b({border:!1,columns:[{label:"发起人员名称",prop:"key"},{label:"发起事件数量",prop:"value"}],indexVisible:!0,pagination:{hide:!0},dataList:[]}),y=async()=>{var m,i;const a=((m=g.value)==null?void 0:m.queryParams)||{};(i=_.value)==null||i.clear();const e=(a==null?void 0:a.type)||"date";let t;switch(e){case"month":t=n(a[e],"YYYY-MM");break;case"year":t=n(a[e],"YYYY");break;default:t=n(a[e],"YYYY-MM-DD");break}z({fromTime:t.startOf(e==="year"?"y":e==="month"?"M":"D").valueOf(),toTime:t.endOf(e==="year"?"y":e==="month"?"M":"D").valueOf(),statisticOrganizer:!0,statisticType:!0}).then(r=>{var l,c,d;h.dataList=((d=(c=(l=r.data)==null?void 0:l.data)==null?void 0:c.organizers)==null?void 0:d.data)||[],Y.listenTo(f.value,()=>{var k,C,D,M;o.barOption=T(((D=(C=(k=r.data)==null?void 0:k.data)==null?void 0:C.types)==null?void 0:D.data)||[]),(M=O.value)==null||M.resize()})}).catch(r=>{console.dir(r),h.dataList=[],o.barOption=T([])}),z({timeUnit:e==="year"?"MONTH":e==="month"?"DAY":"HOUR",fromTime:t.startOf(e==="year"?"y":e==="month"?"M":"D").valueOf(),toTime:t.endOf(e==="year"?"y":e==="month"?"M":"D").valueOf(),statisticOrganizer:!0,statisticType:!0}).then(r=>{Y.listenTo(f.value,()=>{var l,c,d;o.lineOption=x(((c=(l=r.data)==null?void 0:l.data)==null?void 0:c.types)||[],e),(d=_.value)==null||d.resize()})}).catch(()=>{o.lineOption=x([])})};return H(()=>{y()}),(a,e)=>{const t=P,m=N,i=V("VChart"),r=S;return w(),A("div",R,[p(t,{ref_key:"refSearch",ref:g,config:F},null,8,["config"]),p(r,{class:"card-table"},{default:E(()=>[s("div",U,[s("div",j,[s("div",I,[p(m,{config:h},null,8,["config"])]),s("div",{ref_key:"agriEcoDev",ref:f,class:"echart-box"},[p(i,{ref_key:"refChartBar",ref:O,option:o.barOption},null,8,["option"])],512)]),s("div",W,[s("div",{ref_key:"agriEcoDev",ref:f,class:"chart-box"},[p(i,{ref_key:"refChartLine",ref:_,option:o.lineOption},null,8,["option"])],512)])])]),_:1})])}}}),ne=G(J,[["__scopeId","data-v-72392fa4"]]);export{ne as default};
