import{m as a}from"./index-r0dFAfgr.js";function e(t){return a({url:"/api/install/project/m",method:"get",params:t})}function n(t){return a({url:"/api/install/project/m/manage",method:"get",params:t})}function r(t){return a({url:`/api/install/project/m/${t}`,method:"get"})}function s(t){return a({url:"/api/install/project/m",method:"delete",data:t})}function i(t){return a({url:"/api/install/project/m",method:"post",data:t})}function o(t){return a({url:"/api/install/project/c",method:"post",data:t})}function p(t){return a({url:"/api/install/project/attachment/list",method:"post",data:t})}function u(t){return a({url:"/api/install/project/m/audit",method:"post",data:t})}export{e as a,u as b,r as c,s as d,i as e,o as f,n as p,p as s};
