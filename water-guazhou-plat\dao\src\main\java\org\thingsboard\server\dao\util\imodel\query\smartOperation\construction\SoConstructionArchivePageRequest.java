package org.thingsboard.server.dao.util.imodel.query.smartOperation.construction;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartOperation.SoGeneralTaskStatus;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionArchive;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;

import java.util.Date;

@Getter
@Setter
public class SoConstructionArchivePageRequest extends AdvancedPageableQueryEntity<SoConstructionArchive, SoConstructionArchivePageRequest> {
    // 所属工程编号
    private String constructionCode;

    // 所属工程名称
    private String constructionName;

    // 所属工程类别Id
    private String constructionTypeId;

    // 所属项目启动时间开始
    private Date projectStartTimeFrom;

    // 所属项目启动时间截止
    private Date projectStartTimeTo;

    public SoGeneralTaskStatus getProcessingStatus() {
        return SoGeneralTaskStatus.PROCESSING;
    }

}
