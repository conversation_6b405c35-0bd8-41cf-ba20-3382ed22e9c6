<!-- gis水量热点图 -->
<template>
  <div class="onemap-panel-wrapper">
    <Cards
      v-model="cardsvalue"
      :span="12"
    ></Cards>
    <Form :config="FormConfig"></Form>
  </div>
</template>
<script lang="ts" setup>
import { Cards } from '../../components'
import { ring } from '../../components/components/chart'

defineEmits(['highlightMark', 'addMarks'])
defineProps<{
  view?: __esri.MapView
  menu: IMenuItem
}>()

const cardsvalue = ref([
  { label: '0 户', value: '用户数' },
  { label: '0 m³', value: '用水量' }
])
const FormConfig = reactive<IFormConfig>({
  labelPosition: 'top',
  group: [
    {
      fieldset: {
        type: 'underline',
        desc: '用水量占比'
      },
      fields: [
        {
          type: 'vchart',
          option: ring(),
          style: {
            width: '100%',
            height: '150px'
          }
        }
      ]
    },
    {
      fieldset: {
        type: 'underline',
        desc: '用水性质占比'
      },
      fields: [
        {
          type: 'vchart',
          option: ring(),
          style: {
            width: '100%',
            height: '150px'
          }
        }
      ]
    },
    {
      fieldset: {
        type: 'underline',
        desc: '各营业所水量占比'
      },
      fields: [
        {
          type: 'vchart',
          option: ring(),
          style: {
            width: '100%',
            height: '150px'
          }
        }
      ]
    }
  ]
})
</script>

<style lang="scss" scoped>
.onemap-panel-wrapper {
  min-height: 610px;
  height: 100%;
}
</style>
