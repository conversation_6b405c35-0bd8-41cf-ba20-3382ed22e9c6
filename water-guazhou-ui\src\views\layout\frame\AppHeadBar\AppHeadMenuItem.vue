<template>
  <template
    v-if="!props.item?.hidden && !props.item?.meta?.hidden && hasAuthority"
  >
    <div
      v-if="props.level === 1"
      :class="[
        'menu-item',
        'menu-level-' + props.level,
        useAppStore().subMenuParentRoute?.path === props.item.path
          ? 'is-active'
          : ''
      ]"
      role="menuitem"
      @click="() => handleSubMenuClick(props.item)"
    >
      <span :class="'title-text'">{{ props.item.meta.title }}</span>
      <Icon
        v-if="props.item.children?.length"
        icon="ep:caret-right"
      ></Icon>
    </div>
    <div
      v-if="props.level < 1"
      :class="'menu-item menu-level-' + props.level"
    >
      <div class="menu-item-main">
        <span :class="'title-text' + ' ' + props.item.meta.icon">
          {{ props.item.meta.title }}
        </span>
      </div>
      <ul
        v-if="props.item.children?.length"
        class="menu-list"
      >
        <li
          v-for="child in props.item.children"
          :key="child.path"
        >
          <app-head-menu-item
            :level="props.level + 1"
            :item="child"
            :icon="child.meta?.icon || props.icon"
            @submenu-click="cMenu => handleSubMenuClick(cMenu)"
          >
          </app-head-menu-item>
        </li>
      </ul>
    </div>
  </template>
</template>

<script lang="ts" setup>
import { Icon } from '@iconify/vue'
import { useAppStore } from '@/store'
import { hasPermission } from '@/utils/RouterHelper'

const props = defineProps<{
  item: any
  icon?: string
  level: number
}>()
const emit = defineEmits(['submenu-click'])
const handleSubMenuClick = (menu: any) => {
  emit('submenu-click', menu)
}
const hasAuthority = computed(() => {
  return hasPermission(props.item?.meta?.roles)
})
</script>

<style lang="scss" scoped>
.menu-item {
  display: inline-block;
  vertical-align: top;
  margin-right: 20px;
  &.menu-level-0 {
    min-width: 160px;
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    line-height: 22px;
    margin-bottom: 20px;
    padding-left: 37px;
    color: var(--el-text-color-primary);
    .menu-item-main {
      margin-bottom: 20px;
    }
  }
  &.menu-level-1 {
    width: 100%;
    display: flex;
    align-items: center;
    color: var(--el-text-color-secondary);
    cursor: pointer;
    padding: 4px 6px;
    .title-text {
      word-break: keep-all;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      margin-right: auto;
    }

    &.is-active {
      background-color: var(--el-color-primary);
      color: #fff;
    }
    &:hover {
      color: var(--el-color-primary);
      &.is-active {
        background-color: var(--el-color-primary);
        color: #fff;
      }
    }
  }
}
.menu-list {
  list-style-type: none;
  margin: 0;
  padding: 0;
  padding-left: 15px;
  height: 350px;
  overflow-y: auto;
  &::-webkit-scrollbar{
    display: none;
  }
  & > li {
    padding: 2px 0;
  }
}
</style>
