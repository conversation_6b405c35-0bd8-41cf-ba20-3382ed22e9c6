function C(e,i,r){r===void 0&&(r={});var t={type:"Feature"};return(r.id===0||r.id)&&(t.id=r.id),r.bbox&&(t.bbox=r.bbox),t.properties=i||{},t.geometry=e,t}function E(e,i,r){r===void 0&&(r={});for(var t=0,a=e;t<a.length;t++){var f=a[t];if(f.length<4)throw new Error("Each LinearRing of a Polygon must have 4 or more Positions.");for(var o=0;o<f[f.length-1].length;o++)if(f[f.length-1][o]!==f[0][o])throw new Error("First and last Position are not equivalent.")}var u={type:"Polygon",coordinates:e};return C(u,i,r)}function L(e,i,r){if(r===void 0&&(r={}),e.length<2)throw new Error("coordinates must be an array of two or more positions");var t={type:"LineString",coordinates:e};return C(t,i,r)}function d(e,i){i===void 0&&(i={});var r={type:"FeatureCollection"};return i.id&&(r.id=i.id),i.bbox&&(r.bbox=i.bbox),r.features=e,r}function k(e,i,r){r===void 0&&(r={});var t={type:"MultiPolygon",coordinates:e};return C(t,i,r)}function F(e){return!!e&&e.constructor===Object}function O(e){if(Array.isArray(e))return e;if(e.type==="Feature"){if(e.geometry!==null)return e.geometry.coordinates}else if(e.coordinates)return e.coordinates;throw new Error("coords must be GeoJSON Feature, Geometry Object or an Array")}function A(e){return e.type==="Feature"?e.geometry:e}function G(e,i,r){if(e!==null)for(var t,a,f,o,u,n,l,c=0,s=0,h,g=e.type,b=g==="FeatureCollection",p=g==="Feature",m=b?e.features.length:1,v=0;v<m;v++){l=b?e.features[v].geometry:p?e.geometry:e,h=l?l.type==="GeometryCollection":!1,u=h?l.geometries.length:1;for(var M=0;M<u;M++){var y=0,P=0;if(o=h?l.geometries[M]:l,o!==null){n=o.coordinates;var w=o.type;switch(c=0,w){case null:break;case"Point":if(i(n,s,v,y,P)===!1)return!1;s++,y++;break;case"LineString":case"MultiPoint":for(t=0;t<n.length;t++){if(i(n[t],s,v,y,P)===!1)return!1;s++,w==="MultiPoint"&&y++}w==="LineString"&&y++;break;case"Polygon":case"MultiLineString":for(t=0;t<n.length;t++){for(a=0;a<n[t].length-c;a++){if(i(n[t][a],s,v,y,P)===!1)return!1;s++}w==="MultiLineString"&&y++,w==="Polygon"&&P++}w==="Polygon"&&y++;break;case"MultiPolygon":for(t=0;t<n.length;t++){for(P=0,a=0;a<n[t].length;a++){for(f=0;f<n[t][a].length-c;f++){if(i(n[t][a][f],s,v,y,P)===!1)return!1;s++}P++}y++}break;case"GeometryCollection":for(t=0;t<o.geometries.length;t++)if(G(o.geometries[t],i)===!1)return!1;break;default:throw new Error("Unknown Geometry Type")}}}}}function B(e,i){if(e.type==="Feature")i(e,0);else if(e.type==="FeatureCollection")for(var r=0;r<e.features.length&&i(e.features[r],r)!==!1;r++);}function S(e,i){var r,t,a,f,o,u,n,l,c,s,h=0,g=e.type==="FeatureCollection",b=e.type==="Feature",p=g?e.features.length:1;for(r=0;r<p;r++){for(u=g?e.features[r].geometry:b?e.geometry:e,l=g?e.features[r].properties:b?e.properties:{},c=g?e.features[r].bbox:b?e.bbox:void 0,s=g?e.features[r].id:b?e.id:void 0,n=u?u.type==="GeometryCollection":!1,o=n?u.geometries.length:1,a=0;a<o;a++){if(f=n?u.geometries[a]:u,f===null){if(i(null,h,l,c,s)===!1)return!1;continue}switch(f.type){case"Point":case"LineString":case"MultiPoint":case"Polygon":case"MultiLineString":case"MultiPolygon":{if(i(f,h,l,c,s)===!1)return!1;break}case"GeometryCollection":{for(t=0;t<f.geometries.length;t++)if(i(f.geometries[t],h,l,c,s)===!1)return!1;break}default:throw new Error("Unknown Geometry Type")}}h++}}function U(e,i){S(e,function(r,t,a,f,o){var u=r===null?null:r.type;switch(u){case null:case"Point":case"LineString":case"Polygon":return i(C(r,a,{bbox:f,id:o}),t,0)===!1?!1:void 0}var n;switch(u){case"MultiPoint":n="Point";break;case"MultiLineString":n="LineString";break;case"MultiPolygon":n="Polygon";break}for(var l=0;l<r.coordinates.length;l++){var c=r.coordinates[l],s={type:n,coordinates:c};if(i(C(s,a),t,l)===!1)return!1}})}export{d as a,S as b,G as c,U as d,A as e,B as f,O as g,F as i,L as l,k as m,E as p};
