import{m as P,d as de,c,r as he,o as fe,g as V,n as Q,q as a,F as s,p as v,aB as ge,aJ as ye,h as ve,G as b,bh as be,x as m,y as Ce,aK as _e,aL as Se,I as xe,H as De,J as ke,K as we,bz as Ee,N as Ne,c6 as Ie,O as $e,bU as Fe,bW as Ge,bB as j,c4 as M,C as Te}from"./index-r0dFAfgr.js";import Re from"./AddSchemeDialog-CfI2157J.js";import We from"./ViewSchemeDialog-SI8wgcMk.js";import ze from"./EditSchemeDialog-C28El-B5.js";import{h as Ae}from"./pumpRoomInfo-DV75B9MO.js";function Ve(C){return P({url:`/api/sp/pumpGroupScheme/byPumpRoom/${C}`,method:"get"})}function Me(C){return P({url:"/api/sp/pumpGroupScheme",method:"post",data:C})}function Pe(C,k){return P({url:`/api/sp/pumpGroupScheme/${C}`,method:"patch",data:k})}const Le={class:"pump-station-dispatch"},Be={class:"filter-panel"},He={class:"content-container"},Oe={class:"card-header"},Ue=de({__name:"index",setup(C){const k=c(!1),L=c([]),w=c(!1),E=c(!1),_=c(!1),N=c({}),i=he({stationId:"",schemeName:""}),d=c([]),G=c(),T=c(),R=c();let f=null,g=null,I=null;const X=async()=>{try{const e=await Ae({page:1,size:1e3});let t=[];e&&e.data&&e.data.code===200&&e.data.data&&Array.isArray(e.data.data.data)&&(t=e.data.data.data),L.value=t.map(o=>({label:o.name,value:o.id}))}catch(e){console.error("获取泵房列表失败:",e),m.error("获取泵房列表失败")}},$=async()=>{if(!i.stationId){d.value=[];return}k.value=!0;try{const e=await Ve(i.stationId);console.log("泵组方案接口返回数据:",e),console.log("查询泵房ID:",i.stationId);let t=[];e&&e.data&&(e.data.code===200?Array.isArray(e.data.data)&&(t=e.data.data):Array.isArray(e.data)&&(t=e.data)),console.log("解析后的泵组方案数据:",t),console.log("泵组方案数据数量:",t.length),d.value=t.map(o=>({id:o.id,pumpCode:o.schemeCode,pumpName:o.schemeName,pumpHouse:o.pumpRoomName||"泵房",pumpStatus:o.status===1?"启用":"禁用",schemeDescription:o.schemeDescription,pumpGroupConfig:o.pumpGroupConfig,createTime:o.createTime})),d.value.length===0&&m.info("该泵房暂无泵组方案数据"),await j(),B()}catch(e){console.error("获取泵组方案数据失败:",e),m.error("获取泵组方案数据失败"),await j(),B()}finally{k.value=!1}},Y=()=>{$()},Z=()=>{$()},ee=()=>{i.stationId="",i.schemeName="",d.value=[],me()},te=e=>{console.log("查看方案详情:",e),N.value={id:e.id,schemeName:e.pumpName,schemeCode:e.pumpCode,pumpRoomName:e.pumpHouse,schemeDescription:e.schemeDescription,schemeRemark:e.schemeRemark,pumpGroupConfig:e.pumpGroupConfig,status:e.pumpStatus==="启用"?1:0,createTime:e.createTime,creator:e.creator},E.value=!0},ae=e=>{console.log("编辑:",e),N.value={id:e.id,schemeName:e.pumpName,schemeCode:e.pumpCode,pumpRoomName:e.pumpHouse,schemeDescription:e.schemeDescription,schemeRemark:e.schemeRemark,pumpGroupConfig:e.pumpGroupConfig,status:e.pumpStatus==="启用"?1:0,createTime:e.createTime,creator:e.creator},_.value=!0},oe=e=>{console.log("从查看对话框编辑:",e),E.value=!1,_.value=!0},se=e=>{Ce.confirm(`确定要删除方案"${e.pumpName}"吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{const t=d.value.findIndex(o=>o.id===e.id);t>-1&&(d.value.splice(t,1),m.success("删除成功"))}).catch(()=>{})},ne=()=>{if(!i.stationId){m.warning("请先选择泵房");return}w.value=!0},le=async e=>{console.log("添加方案:",e);try{const t={pumpRoomId:i.stationId,schemeName:e.schemeName,schemeCode:ie(),schemeDescription:e.schemeDescription,schemeRemark:e.schemeRemark,pumpGroupConfig:JSON.stringify(e.pumpConfigs),status:1};console.log("保存数据:",t);const o=await Me(t);console.log("保存结果:",o),m.success("方案添加成功"),w.value=!1,await $()}catch(t){console.error("保存方案失败:",t),m.error("保存方案失败，请重试")}},ie=()=>{const e=Date.now(),t=Math.floor(Math.random()*1e3).toString().padStart(3,"0");return`SCHEME_${e}_${t}`},re=async e=>{console.log("编辑方案:",e);try{const t={schemeName:e.schemeName,schemeDescription:e.schemeDescription,schemeRemark:e.schemeRemark,pumpGroupConfig:JSON.stringify(e.pumpConfigs),status:1};console.log("更新数据:",t);const o=await Pe(e.id,t);console.log("更新结果:",o),m.success("方案更新成功"),_.value=!1,await $()}catch(t){console.error("更新方案失败:",t),m.error("更新方案失败，请重试")}},W=()=>{const e=[],t=[],o=[],r=[],l=[],h=[];return d.value.forEach(u=>{try{const S=JSON.parse(u.pumpGroupConfig||"[]"),y=u.pumpName||"未知方案";e.push(y);let p=0,x=0,z=0;S.forEach(D=>{var q,K;const H=(q=D.fixedFlow)==null?void 0:q.match(/(\d+(?:\.\d+)?)/),O=H?parseFloat(H[1]):0,U=(K=D.fixedPower)==null?void 0:K.match(/(\d+(?:\.\d+)?)/),J=U?parseFloat(U[1]):0;O>0&&J>0&&(p+=O,x+=J,z++)}),t.push(p),o.push(x);const F=Math.round(p*(.7+Math.random()*.2));r.push(F);const A=Math.round(16+Math.random()*8);l.push(A);const n=p>0?Math.round(x/p*(200+Math.random()*300)):0;h.push(n)}catch(S){console.error("解析泵组配置失败:",S)}}),e.length===0?{categories:["方案1","方案2","方案3","方案4","方案5"],supplyFlowData:[750,600,800,700,650],powerConsumptionData:[450,360,480,420,390],avgFlowData:[600,480,640,560,520],runTimeData:[20,22,18,24,19],pumpElectricityData:[250,240,300,280,260]}:{categories:e,supplyFlowData:t,powerConsumptionData:o,avgFlowData:r,runTimeData:l,pumpElectricityData:h}},pe=()=>{if(!R.value)return;I&&I.dispose(),I=M(R.value);const e=W(),t={title:{text:"泵组吨水电耗",left:"left",textStyle:{fontSize:14,fontWeight:"bold"}},tooltip:{trigger:"axis",axisPointer:{type:"shadow"},formatter:function(o){const r=o[0];return`${r.name}（方案汇总）<br/>吨水电耗: ${r.value}kWh/t`}},legend:{data:["吨水电耗"],top:30,itemWidth:10,itemHeight:10},grid:{left:"3%",right:"4%",bottom:"3%",top:"20%",containLabel:!0},xAxis:{type:"category",data:e.categories,axisLabel:{fontSize:12}},yAxis:{type:"value",name:"单位：kWh/t",axisLabel:{fontSize:12}},series:[{name:"吨水电耗",type:"bar",data:e.pumpElectricityData,itemStyle:{color:"#fac858"},barWidth:"50%"}]};I.setOption(t)},B=()=>{ce(),ue(),pe()},ce=()=>{if(!G.value)return;f&&f.dispose(),f=M(G.value);const e=W(),t={title:{text:"供水量与耗电量",left:"left",textStyle:{fontSize:14,fontWeight:"bold"}},tooltip:{trigger:"axis",axisPointer:{type:"shadow"},formatter:function(o){let r=`${o[0].name}（方案汇总）<br/>`;return o.forEach(l=>{const h=l.seriesName==="供水量"?"m³/h":"kW";r+=`${l.marker}${l.seriesName}: ${l.value}${h}<br/>`}),r}},legend:{data:["供水量","耗电量"],top:30,itemWidth:10,itemHeight:10},grid:{left:"3%",right:"4%",bottom:"3%",top:"20%",containLabel:!0},xAxis:{type:"category",data:e.categories,axisLabel:{fontSize:12}},yAxis:{type:"value",axisLabel:{fontSize:12}},series:[{name:"供水量",type:"bar",data:e.supplyFlowData,itemStyle:{color:"#5470c6"},barWidth:"30%"},{name:"耗电量",type:"bar",data:e.powerConsumptionData,itemStyle:{color:"#91cc75"},barWidth:"30%"}]};f.setOption(t)},ue=()=>{if(!T.value)return;g&&g.dispose(),g=M(T.value);const e=W(),t={title:{text:"运行时长与平均流量",left:"left",textStyle:{fontSize:14,fontWeight:"bold"}},tooltip:{trigger:"axis",axisPointer:{type:"shadow"},formatter:function(o){let r=`${o[0].name}（方案汇总）<br/>`;return o.forEach(l=>{const h=l.seriesName==="平均流量"?"m³/h":"小时";r+=`${l.marker}${l.seriesName}: ${l.value}${h}<br/>`}),r}},legend:{data:["平均流量","运行时长"],top:30,itemWidth:10,itemHeight:10},grid:{left:"3%",right:"4%",bottom:"3%",top:"20%",containLabel:!0},xAxis:{type:"category",data:e.categories,axisLabel:{fontSize:12}},yAxis:{type:"value",axisLabel:{fontSize:12}},series:[{name:"平均流量",type:"bar",data:e.avgFlowData,itemStyle:{color:"#5470c6"},barWidth:"30%"},{name:"运行时长",type:"bar",data:e.runTimeData,itemStyle:{color:"#73c0de"},barWidth:"30%"}]};g.setOption(t)},me=()=>{f&&(f.dispose(),f=null),g&&(g.dispose(),g=null)};return fe(()=>{X()}),(e,t)=>{const o=_e,r=Se,l=xe,h=De,u=ke,S=we,y=Ee,p=Ne,x=Ie,z=$e,F=Fe,A=Ge;return V(),Q("div",Le,[a(y,{class:"filter-card"},{default:s(()=>[v("div",Be,[a(S,{model:i,inline:""},{default:s(()=>[a(l,{label:"选择泵房:"},{default:s(()=>[a(r,{modelValue:i.stationId,"onUpdate:modelValue":t[0]||(t[0]=n=>i.stationId=n),placeholder:"请选择",style:{width:"200px"},onChange:Y},{default:s(()=>[(V(!0),Q(ge,null,ye(L.value,n=>(V(),ve(o,{key:n.value,label:n.label,value:n.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(l,{label:"方案名称:"},{default:s(()=>[a(h,{modelValue:i.schemeName,"onUpdate:modelValue":t[1]||(t[1]=n=>i.schemeName=n),placeholder:"输入方案",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),a(l,null,{default:s(()=>[a(u,{type:"primary",onClick:Z},{default:s(()=>t[5]||(t[5]=[b("查询")])),_:1}),a(u,{onClick:ee},{default:s(()=>t[6]||(t[6]=[b("重置")])),_:1})]),_:1})]),_:1},8,["model"])])]),_:1}),v("div",He,[a(A,{gutter:16},{default:s(()=>[a(F,{span:14},{default:s(()=>[a(y,{class:"pump-list-card"},{header:s(()=>[v("div",Oe,[t[8]||(t[8]=v("span",null,"泵组方案",-1)),a(u,{type:"primary",size:"small",onClick:ne},{default:s(()=>t[7]||(t[7]=[b("添加")])),_:1})])]),default:s(()=>[a(z,{data:d.value,border:"",stripe:"",height:"400"},{default:s(()=>[a(p,{type:"selection",width:"55"}),a(p,{prop:"pumpName",label:"方案名称","min-width":"140"}),a(p,{prop:"pumpHouse",label:"所属泵房","min-width":"120"}),a(p,{prop:"schemeDescription",label:"方案描述","min-width":"180","show-overflow-tooltip":""}),a(p,{prop:"pumpStatus",label:"方案状态",width:"100"},{default:s(({row:n})=>[a(x,{type:n.pumpStatus==="启用"?"success":"info"},{default:s(()=>[b(be(n.pumpStatus),1)]),_:2},1032,["type"])]),_:1}),a(p,{label:"操作",width:"180"},{default:s(({row:n})=>[a(u,{type:"primary",link:"",size:"small",onClick:D=>te(n)},{default:s(()=>t[9]||(t[9]=[b("查看")])),_:2},1032,["onClick"]),a(u,{type:"primary",link:"",size:"small",onClick:D=>ae(n)},{default:s(()=>t[10]||(t[10]=[b("编辑")])),_:2},1032,["onClick"]),a(u,{type:"danger",link:"",size:"small",onClick:D=>se(n)},{default:s(()=>t[11]||(t[11]=[b("删除")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1})]),_:1}),a(F,{span:10},{default:s(()=>[a(y,{class:"chart-card",style:{"margin-bottom":"16px"}},{default:s(()=>[v("div",{ref_key:"waterSupplyChart",ref:G,style:{height:"200px"}},null,512)]),_:1}),a(y,{class:"chart-card",style:{"margin-bottom":"16px"}},{default:s(()=>[v("div",{ref_key:"powerConsumptionChart",ref:T,style:{height:"200px"}},null,512)]),_:1}),a(y,{class:"chart-card"},{default:s(()=>[v("div",{ref_key:"pumpElectricityChart",ref:R,style:{height:"200px"}},null,512)]),_:1})]),_:1})]),_:1})]),a(Re,{visible:w.value,"onUpdate:visible":t[2]||(t[2]=n=>w.value=n),"pump-room-id":i.stationId,onConfirm:le},null,8,["visible","pump-room-id"]),a(We,{visible:E.value,"onUpdate:visible":t[3]||(t[3]=n=>E.value=n),"scheme-data":N.value,onEdit:oe},null,8,["visible","scheme-data"]),a(ze,{visible:_.value,"onUpdate:visible":t[4]||(t[4]=n=>_.value=n),"pump-room-id":i.stationId,"scheme-data":N.value,onConfirm:re},null,8,["visible","pump-room-id","scheme-data"])])}}}),Xe=Te(Ue,[["__scopeId","data-v-3953596d"]]);export{Xe as default};
