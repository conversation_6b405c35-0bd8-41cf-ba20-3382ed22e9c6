<template>
  <div class="item">
    <div class="item_title">
      {{ title }}
    </div>
    <div class="item_content">
      <slot></slot>
    </div>
  </div>
</template>

<script lang="ts" setup>
defineProps<{ title: string }>()
</script>

<style lang="scss" scoped>
.item {
  position: relative;
  background: url('../imgs/smartdecisionMaking_item.png') 0 0 / 100% 100%
    no-repeat;
  height: 480px;
  width: 462px;
  padding: 0 5px;
  .item_title {
    text-align: center;
    line-height: 40px;
    font-size: 24px;
    color: #65aaf3;
  }
  .item_content {
    height: calc(100% - 40px);
  }
}
</style>
