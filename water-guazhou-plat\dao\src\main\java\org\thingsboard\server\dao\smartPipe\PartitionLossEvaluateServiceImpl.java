package org.thingsboard.server.dao.smartPipe;

import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.dao.model.DTO.PartitionNormalLossEvaluateDTO;
import org.thingsboard.server.dao.model.DTO.PartitionNotNormalLossEvaluateDTO;
import org.thingsboard.server.dao.model.DTO.PartitionTotalDifferenceDTO;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.Partition;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.PartitionMount;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.PipeMinFlowConfig;
import org.thingsboard.server.dao.sql.smartPipe.PartitionMountMapper;
import org.thingsboard.server.dao.util.InfluxUtil;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2023-05-11
 */
@Service
public class PartitionLossEvaluateServiceImpl implements PartitionLossEvaluateService {

    @Autowired
    private PartitionService partitionService;

    @Autowired
    private PartitionMountMapper partitionMountMapper;

    @Autowired
    private InfluxUtil influxUtil;

    @Autowired
    private PipeMinFlowConfigService pipeMinFlowConfigService;

    @Autowired
    private PipePartitionTotalFlowService pipePartitionTotalFlowService;

    @Autowired
    private PipeCopyDataReadMeterDataService pipeCopyDataReadMeterDataService;


    @Override
    public List<PartitionNormalLossEvaluateDTO> getNormalList(String date, String name, String partitionId, String tenantId) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH");
        // 查询所有运营中的DMA分区
        Partition partition = partitionService.getById(partitionId);
        List<Partition> partitionList = new ArrayList<>();
        partitionService.getDMAChild(partition, partitionList);
        partitionList = partitionList.stream().filter(a -> a.getName().contains(name) && a.getStatus().equals(DataConstants.PARTITION_STATUS.RUN.getValue())).collect(Collectors.toList());

        List<String> partitionIdList = partitionList.stream().map(a -> a.getId()).collect(Collectors.toList());
        Map<String, Partition> partitionMap = new HashMap<>();
        Map<String, PartitionNormalLossEvaluateDTO> resultMap = new HashMap<>();
        PartitionNormalLossEvaluateDTO partitionNormalLossEvaluateDTO;
        if (partitionIdList.size() == 0) {
            return new ArrayList<>();
        }
        // 用户数
        Map<String, Integer> userNumMap = partitionService.getUserNum(partitionIdList);
        // 最小流配置
        Map<String, PipeMinFlowConfig> pipeMinFlowConfigMap = pipeMinFlowConfigService.getByPartitionIdList(partitionIdList);
        PipeMinFlowConfig pipeMinFlowConfig;

        for (Partition partition1 : partitionList) {
            partitionMap.put(partition1.getId(), partition1);
            partitionNormalLossEvaluateDTO = new PartitionNormalLossEvaluateDTO();
            partitionNormalLossEvaluateDTO.setPartitionId(partition1.getId());
            partitionNormalLossEvaluateDTO.setPartitionName(partition1.getName());
            partitionNormalLossEvaluateDTO.setStatus(partition1.getStatus());
            partitionNormalLossEvaluateDTO.setStatusName(DataConstants.PARTITION_STATUS.getByValue(partition1.getStatus()).getName());
            partitionNormalLossEvaluateDTO.setUserNum(userNumMap.get(partition1.getId()));
            partitionNormalLossEvaluateDTO.setInlet(partition1.getInletNum());
            partitionNormalLossEvaluateDTO.setMainLineLength(partition1.getMainLineLength());

            pipeMinFlowConfig = pipeMinFlowConfigMap.get(partition1.getId());
            if (pipeMinFlowConfig != null) {
                partitionNormalLossEvaluateDTO.setIncrTime(pipeMinFlowConfig.getIncrTime());
                partitionNormalLossEvaluateDTO.setIncrBase(pipeMinFlowConfig.getIncrBase());
                partitionNormalLossEvaluateDTO.setIncrWarn(pipeMinFlowConfig.getIncrWarn());
                partitionNormalLossEvaluateDTO.setIncrError(pipeMinFlowConfig.getIncrError());
            }
            resultMap.put(partition1.getId(), partitionNormalLossEvaluateDTO);
        }

        List<PartitionMount> partitionMountList = partitionMountMapper.getAllByPartitionId(partitionIdList, DataConstants.PARTITION_TYPE.METERING.getValue(), "in", tenantId);
        Map<String, String> devicePartitionIdMap = new HashMap<>();
        for (PartitionMount partitionMount : partitionMountList) {
            devicePartitionIdMap.put(partitionMount.getDeviceId(), partitionMount.getPartitionId());
        }

        List<String> deviceIdListOrigin = partitionMountList.stream().map(a -> a.getDeviceId()).distinct().collect(Collectors.toList());
        if (deviceIdListOrigin.size() == 0) {
            deviceIdListOrigin.add("-");
        }
        List<String> deviceIdList = new ArrayList<>();
        for (String deviceId : deviceIdListOrigin) {
            deviceIdList.add(deviceId + ".total_flow");
            deviceIdList.add(deviceId + ".Instantaneous_flow");
        }
        try {
            Long start = format.parse(date + " 00").getTime();
            Long end = format.parse(date + " 23").getTime();
            JSONObject data = influxUtil.getData(deviceIdList, start, end, "15m");
            List<String> timeList = data.keySet().stream().sorted().collect(Collectors.toList());
            // 夜间最小流时间点
            int startHour = 2;
            int endHour = 4;
            if (partition.getMinFlowEndHour() != null) {
                startHour = partition.getMinFlowStartHour();
            }
            if (partition.getMinFlowEndHour() != null) {
                endHour = partition.getMinFlowEndHour();
            }
            Long startLong = start + (startHour * 60 * 60 * 1000);
            Long endLong = start + (endHour * 60 * 60 * 1000);


            Map<String, Integer> totalValueSizeMap = new HashMap<>();
            Map<String, Map<String, String>> partitionTimeMap = new HashMap<>();
            Map<String, BigDecimal> tempTotalFlowMap = new HashMap<>();
            Map<String, BigDecimal> tempNightTotalMap;
            Map<String, BigDecimal> tempFlowValueMap;
            BigDecimal instanceFlow;
            BigDecimal totalFlow;
            for (String time : timeList) {
                tempFlowValueMap = new HashMap<>();
                tempNightTotalMap = new HashMap<>();
                for (String deviceId : deviceIdListOrigin) {
                    if (data.getJSONObject(time).get(deviceId + ".Instantaneous_flow") != null) {
                        try {
                            instanceFlow = data.getJSONObject(time).getBigDecimal(deviceId + ".Instantaneous_flow");
                            totalFlow = data.getJSONObject(time).getBigDecimal(deviceId + ".total_flow");

                            partitionNormalLossEvaluateDTO = resultMap.get(devicePartitionIdMap.get(deviceId));
                            if (partitionNormalLossEvaluateDTO.getSupplyTotal() == null) {
                                partitionNormalLossEvaluateDTO.setSupplyTotal(BigDecimal.ZERO);
                            }
                            partitionNormalLossEvaluateDTO.setSupplyTotal(partitionNormalLossEvaluateDTO.getSupplyTotal().add(totalFlow));

                            if (totalValueSizeMap.get(devicePartitionIdMap.get(deviceId)) == null) {
                                totalValueSizeMap.put(devicePartitionIdMap.get(deviceId), 0);
                            }
                            if (tempTotalFlowMap.get(devicePartitionIdMap.get(deviceId)) == null) {
                                tempTotalFlowMap.put(devicePartitionIdMap.get(deviceId), BigDecimal.ZERO);
                            }
                            tempTotalFlowMap.put(devicePartitionIdMap.get(deviceId), tempTotalFlowMap.get(devicePartitionIdMap.get(deviceId)).add(instanceFlow));

                            // 流量数
                            if (partitionTimeMap.get(devicePartitionIdMap.get(deviceId)) == null) {
                                partitionTimeMap.put(devicePartitionIdMap.get(deviceId), new HashMap<>());
                            }
                            if (partitionTimeMap.get(devicePartitionIdMap.get(deviceId)).get(time) == null) {
                                partitionTimeMap.get(devicePartitionIdMap.get(deviceId)).put(time, time);
                                totalValueSizeMap.put(devicePartitionIdMap.get(deviceId), totalValueSizeMap.get(devicePartitionIdMap.get(deviceId)) + 1);
                            }

                            if (format.parse(time).getTime() >= startLong && format.parse(time).getTime() <= endLong) {
                                if (tempFlowValueMap.get(devicePartitionIdMap.get(deviceId)) == null) {
                                    tempFlowValueMap.put(devicePartitionIdMap.get(deviceId), BigDecimal.ZERO);
                                }
                                tempFlowValueMap.put(devicePartitionIdMap.get(deviceId), tempFlowValueMap.get(devicePartitionIdMap.get(deviceId)).add(instanceFlow));
                                if (tempNightTotalMap.get(devicePartitionIdMap.get(deviceId)) == null) {
                                    tempNightTotalMap.put(devicePartitionIdMap.get(deviceId), BigDecimal.ZERO);
                                }
                                tempNightTotalMap.put(devicePartitionIdMap.get(deviceId), tempNightTotalMap.get(devicePartitionIdMap.get(deviceId)).add(totalFlow));
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                }
                partitionIdList = tempTotalFlowMap.keySet().stream().collect(Collectors.toList());
                for (String partId : partitionIdList) {
                    partitionNormalLossEvaluateDTO = resultMap.get(partId);
                    if (tempFlowValueMap.get(partId) != null) {
                        if (partitionNormalLossEvaluateDTO.getMinFlow() == null) {
                            partitionNormalLossEvaluateDTO.setMinFlow(tempFlowValueMap.get(partId));
                            partitionNormalLossEvaluateDTO.setMinTime(format.parse(time));
                            partitionNormalLossEvaluateDTO.setMinFlowTimeHour(partitionNormalLossEvaluateDTO.getMinTime().getHours());
                        }
                        if (partitionNormalLossEvaluateDTO.getMinFlow().compareTo(tempFlowValueMap.get(partId)) > 0) {
                            partitionNormalLossEvaluateDTO.setMinFlow(tempFlowValueMap.get(partId));
                            partitionNormalLossEvaluateDTO.setMinTime(format.parse(time));
                            partitionNormalLossEvaluateDTO.setMinFlowTimeHour(partitionNormalLossEvaluateDTO.getMinTime().getHours());
                        }
                    }

                    if (tempNightTotalMap.get(partId) != null) {
                        if (partitionNormalLossEvaluateDTO.getMinValue() == null) {
                            partitionNormalLossEvaluateDTO.setMinValue(tempNightTotalMap.get(partId));
                            partitionNormalLossEvaluateDTO.setMinValueTimeHour(format.parse(time).getHours());
                        }
                        if (partitionNormalLossEvaluateDTO.getMinValue().compareTo(tempNightTotalMap.get(partId)) > 0) {
                            partitionNormalLossEvaluateDTO.setMinValue(tempNightTotalMap.get(partId));
                            partitionNormalLossEvaluateDTO.setMinValueTimeHour(format.parse(time).getHours());
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        List<PartitionNormalLossEvaluateDTO> result = resultMap.values().stream().collect(Collectors.toList());

        return result;
    }

    @Override
    public List<PartitionNotNormalLossEvaluateDTO> getNotNormalList(String date, String name, String partitionId, String tenantId, String type) {


        Map<String, PartitionNotNormalLossEvaluateDTO> resultMap = this.getNotNormalMap(date, name, partitionId, tenantId, type);

        List<PartitionNotNormalLossEvaluateDTO> result = resultMap.values().stream().collect(Collectors.toList());

        return result;
    }

    private Map<String, PartitionNotNormalLossEvaluateDTO> getNotNormalMap(String date, String name, String partitionId, String tenantId, String type) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH");
        // 查询所有运营中的DMA分区
        Partition partition = partitionService.getById(partitionId);
        List<Partition> partitionList = new ArrayList<>();
        partitionService.getDMAChild(partition, partitionList);
        partitionList = partitionList.stream().filter(a -> a.getName().contains(name) && !a.getStatus().equals(DataConstants.PARTITION_STATUS.RUN.getValue())).collect(Collectors.toList());
        if ("all".equals(type)) {
            partitionList = partitionService.getAll("", "", "", tenantId);
        }
        //分区id
        List<String> partitionIdList = partitionList.stream().map(a -> a.getId()).collect(Collectors.toList());
        Map<String, Partition> partitionMap = new HashMap<>();
        Map<String, PartitionNotNormalLossEvaluateDTO> resultMap = new HashMap<>();
        PartitionNotNormalLossEvaluateDTO partitionNormalLossEvaluateDTO;
        if (partitionIdList.size() == 0) {
            return new HashMap();
        }
        // 用户数，分区id和对应的用户数
        Map<String, Integer> userNumMap = partitionService.getUserNum(partitionIdList);
        // 最小流配置，分区id和对应的最小流配置
        Map<String, PipeMinFlowConfig> pipeMinFlowConfigMap = pipeMinFlowConfigService.getByPartitionIdList(partitionIdList);
        PipeMinFlowConfig pipeMinFlowConfig;

        //遍历分区，组装Map<String, PartitionNotNormalLossEvaluateDTO>增量漏失评估
        for (Partition partition1 : partitionList) {
            partitionMap.put(partition1.getId(), partition1);
            partitionNormalLossEvaluateDTO = new PartitionNotNormalLossEvaluateDTO();
            partitionNormalLossEvaluateDTO.setPartitionId(partition1.getId());
            partitionNormalLossEvaluateDTO.setPartitionName(partition1.getName());
            partitionNormalLossEvaluateDTO.setStatus(partition1.getStatus());
            partitionNormalLossEvaluateDTO.setStatusName(DataConstants.PARTITION_STATUS.getByValue(partition1.getStatus()).getName());
            partitionNormalLossEvaluateDTO.setUserNum(userNumMap.get(partition1.getId()));
            partitionNormalLossEvaluateDTO.setInlet(partition1.getInletNum());
            partitionNormalLossEvaluateDTO.setMainLineLength(partition1.getMainLineLength());

            pipeMinFlowConfig = pipeMinFlowConfigMap.get(partition1.getId());
            if (pipeMinFlowConfig != null) {
                partitionNormalLossEvaluateDTO.setIncrTime(pipeMinFlowConfig.getIncrTime());
                partitionNormalLossEvaluateDTO.setIncrBase(pipeMinFlowConfig.getIncrBase());
                partitionNormalLossEvaluateDTO.setIncrWarn(pipeMinFlowConfig.getIncrWarn());
                partitionNormalLossEvaluateDTO.setIncrError(pipeMinFlowConfig.getIncrError());
            }
            resultMap.put(partition1.getId(), partitionNormalLossEvaluateDTO);
        }

        //分区挂接数据
        List<PartitionMount> partitionMountList = partitionMountMapper.getAllByPartitionId (partitionIdList, DataConstants.PARTITION_TYPE.METERING.getValue(), "in", tenantId);
        //组装map，设备id和分区id
        Map<String, String> devicePartitionIdMap = new HashMap<>();
        for (PartitionMount partitionMount : partitionMountList) {
            devicePartitionIdMap.put(partitionMount.getDeviceId(), partitionMount.getPartitionId());
        }

        //TODO 2025/3/25  下边两个值属于无用代码，后续可删除
        BigDecimal minNightFlow;
        BigDecimal mnfDivDayAvgFlow;

        //过滤重复设备
        List<String> deviceIdListOrigin = partitionMountList.stream().map(a -> a.getDeviceId()).distinct().collect(Collectors.toList());
        if (deviceIdListOrigin.size() == 0) {
            deviceIdListOrigin.add("-");
        }
        //拼接一个设备设备关联的总流量和瞬时流量的数据list
        List<String> deviceIdList = new ArrayList<>();
        for (String deviceId : deviceIdListOrigin) {
            deviceIdList.add(deviceId + ".total_flow");
            deviceIdList.add(deviceId + ".Instantaneous_flow");
        }
        try {
            //构建查询参数，起止时间为00：00-23：00，范围15m
            Long start = format.parse(date + " 00").getTime();
            Long end = format.parse(date + " 23").getTime();
            //通过influxdb，查询设备数据，然后排序
            JSONObject data = influxUtil.getData(deviceIdList, start, end, "15m");
            List<String> timeList = data.keySet().stream().sorted().collect(Collectors.toList());
            // 夜间最小流时间点
            int startHour = 2;
            int endHour = 4;
            //该分区最小流时间设置
            if (partition.getMinFlowEndHour() != null) {
                startHour = partition.getMinFlowStartHour();
            }
            if (partition.getMinFlowEndHour() != null) {
                endHour = partition.getMinFlowEndHour();
            }
            Long startLong = start + (startHour * 60 * 60 * 1000);
            Long endLong = start + (endHour * 60 * 60 * 1000);


            Map<String, Integer> totalValueSizeMap = new HashMap<>();
            Map<String, Map<String, String>> partitionTimeMap = new HashMap<>();
            Map<String, BigDecimal> tempTotalFlowMap = new HashMap<>();
            Map<String, BigDecimal> tempNightTotalMap;
            Map<String, BigDecimal> tempFlowValueMap;
            BigDecimal instanceFlow;
            BigDecimal totalFlow;

            for (String time : timeList) {
                tempFlowValueMap = new HashMap<>();
                tempNightTotalMap = new HashMap<>();
                //遍历去重设备id，根据influxdb查询到的数据进行拼装
                for (String deviceId : deviceIdListOrigin) {
                    if (data.getJSONObject(time).get(deviceId + ".Instantaneous_flow") != null) {
                        try {
                            instanceFlow = data.getJSONObject(time).getBigDecimal(deviceId + ".Instantaneous_flow");
                            totalFlow = data.getJSONObject(time).getBigDecimal(deviceId + ".total_flow");

                            partitionNormalLossEvaluateDTO = resultMap.get(devicePartitionIdMap.get(deviceId));
                            if (partitionNormalLossEvaluateDTO.getSupplyTotal() == null) {
                                partitionNormalLossEvaluateDTO.setSupplyTotal(BigDecimal.ZERO);
                            }
                            partitionNormalLossEvaluateDTO.setSupplyTotal(partitionNormalLossEvaluateDTO.getSupplyTotal().add(totalFlow));

                            if (totalValueSizeMap.get(devicePartitionIdMap.get(deviceId)) == null) {
                                totalValueSizeMap.put(devicePartitionIdMap.get(deviceId), 0);
                            }
                            if (tempTotalFlowMap.get(devicePartitionIdMap.get(deviceId)) == null) {
                                tempTotalFlowMap.put(devicePartitionIdMap.get(deviceId), BigDecimal.ZERO);
                            }
                            tempTotalFlowMap.put(devicePartitionIdMap.get(deviceId), tempTotalFlowMap.get(devicePartitionIdMap.get(deviceId)).add(instanceFlow));

                            // 流量数
                            if (partitionTimeMap.get(devicePartitionIdMap.get(deviceId)) == null) {
                                partitionTimeMap.put(devicePartitionIdMap.get(deviceId), new HashMap<>());
                            }
                            if (partitionTimeMap.get(devicePartitionIdMap.get(deviceId)).get(time) == null) {
                                partitionTimeMap.get(devicePartitionIdMap.get(deviceId)).put(time, time);
                                totalValueSizeMap.put(devicePartitionIdMap.get(deviceId), totalValueSizeMap.get(devicePartitionIdMap.get(deviceId)) + 1);
                            }

                            if (format.parse(time).getTime() >= startLong && format.parse(time).getTime() <= endLong) {
                                if (tempFlowValueMap.get(devicePartitionIdMap.get(deviceId)) == null) {
                                    tempFlowValueMap.put(devicePartitionIdMap.get(deviceId), BigDecimal.ZERO);
                                }
                                tempFlowValueMap.put(devicePartitionIdMap.get(deviceId), tempFlowValueMap.get(devicePartitionIdMap.get(deviceId)).add(instanceFlow));
                                if (tempNightTotalMap.get(devicePartitionIdMap.get(deviceId)) == null) {
                                    tempNightTotalMap.put(devicePartitionIdMap.get(deviceId), BigDecimal.ZERO);
                                }
                                tempNightTotalMap.put(devicePartitionIdMap.get(deviceId), tempNightTotalMap.get(devicePartitionIdMap.get(deviceId)).add(totalFlow));
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                }
                partitionIdList = tempTotalFlowMap.keySet().stream().collect(Collectors.toList());
                for (String partId : partitionIdList) {
                    partitionNormalLossEvaluateDTO = resultMap.get(partId);
                    if (tempFlowValueMap.get(partId) != null) {
                        if (partitionNormalLossEvaluateDTO.getMinFlow() == null) {
                            partitionNormalLossEvaluateDTO.setMinFlow(tempFlowValueMap.get(partId));
                            partitionNormalLossEvaluateDTO.setMinTime(format.parse(time));
                            partitionNormalLossEvaluateDTO.setMinFlowTimeHour(partitionNormalLossEvaluateDTO.getMinTime().getHours());
                        }
                        if (partitionNormalLossEvaluateDTO.getMinFlow().compareTo(tempFlowValueMap.get(partId)) > 0) {
                            partitionNormalLossEvaluateDTO.setMinFlow(tempFlowValueMap.get(partId));
                            partitionNormalLossEvaluateDTO.setMinTime(format.parse(time));
                            partitionNormalLossEvaluateDTO.setMinFlowTimeHour(partitionNormalLossEvaluateDTO.getMinTime().getHours());
                        }
                    }

                    if (tempNightTotalMap.get(partId) != null) {
                        if (partitionNormalLossEvaluateDTO.getMinValue() == null) {
                            partitionNormalLossEvaluateDTO.setMinValue(tempNightTotalMap.get(partId));
                            partitionNormalLossEvaluateDTO.setMinValueTimeHour(format.parse(time).getHours());
                        }
                        if (partitionNormalLossEvaluateDTO.getMinValue().compareTo(tempNightTotalMap.get(partId)) > 0) {
                            partitionNormalLossEvaluateDTO.setMinValue(tempNightTotalMap.get(partId));
                            partitionNormalLossEvaluateDTO.setMinValueTimeHour(format.parse(time).getHours());
                        }
                    }
                }
            }

            // MNF / 日均流量
            for (String partId : partitionIdList) {
                partitionNormalLossEvaluateDTO = resultMap.get(partId);
                getEvaluate(partitionMap, partitionNormalLossEvaluateDTO, pipeMinFlowConfigMap);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return resultMap;
    }

    private void getEvaluate(Map<String, Partition> partitionMap, PartitionNotNormalLossEvaluateDTO partitionNormalLossEvaluateDTO, Map<String, PipeMinFlowConfig> pipeMinFlowConfigMap) {
        String lossLevel = "一般";
        BigDecimal mnfDivDayAvgFlow;
        PipeMinFlowConfig pipeMinFlowConfig;
        BigDecimal minNightFlow = partitionNormalLossEvaluateDTO.getMinFlow();

        // 日均流量
        try {
            partitionNormalLossEvaluateDTO.setAvgDayFlow(partitionNormalLossEvaluateDTO.getSupplyTotal().divide(BigDecimal.valueOf(24), 2, RoundingMode.HALF_UP));
        } catch (Exception e) {
            partitionNormalLossEvaluateDTO.setAvgDayFlow(BigDecimal.ZERO);
        }

        // mnf / 日均流量
        try {
            mnfDivDayAvgFlow = minNightFlow.multiply(BigDecimal.valueOf(100)).divide(partitionNormalLossEvaluateDTO.getAvgDayFlow(), 2, RoundingMode.HALF_UP);

            partitionNormalLossEvaluateDTO.setMnfDivideAvgDayFlow(mnfDivDayAvgFlow);
        } catch (Exception e) {
            partitionNormalLossEvaluateDTO.setMnfDivideAvgDayFlow(BigDecimal.ZERO);
        }

        // 单位管线夜间最小流
        try {
            partitionNormalLossEvaluateDTO.setUnitPipeNightFlowMin(partitionNormalLossEvaluateDTO.getMinFlow().divide(partitionNormalLossEvaluateDTO.getMainLineLength(), 2, RoundingMode.HALF_UP));
        } catch (Exception e) {
            partitionNormalLossEvaluateDTO.setUnitPipeNightFlowMin(BigDecimal.ZERO);
        }

        // 用户合法用水量  用水定额*注册用户数量
        try {
            partitionNormalLossEvaluateDTO.setLegalUseWater(partitionMap.get(partitionNormalLossEvaluateDTO.getPartitionId()).getLegalUseWater().multiply(BigDecimal.valueOf(partitionNormalLossEvaluateDTO.getUserNum())));
        } catch (Exception e) {
            partitionNormalLossEvaluateDTO.setLegalUseWater(BigDecimal.ZERO);
        }

        // 净夜间流量 MNF-用户合法用水量-大用户用水量
        try {
            partitionNormalLossEvaluateDTO.setNetNightFlow(partitionNormalLossEvaluateDTO.getMinFlow().subtract(partitionNormalLossEvaluateDTO.getLegalUseWater()));
        } catch (Exception e) {
            partitionNormalLossEvaluateDTO.setNetNightFlow(BigDecimal.ZERO);
        }
        // 漏失水量
        try {
            partitionNormalLossEvaluateDTO.setLossWater(partitionNormalLossEvaluateDTO.getNetNightFlow().multiply(BigDecimal.valueOf(24)));
        } catch (Exception e) {
            partitionNormalLossEvaluateDTO.setLossWater(BigDecimal.ZERO);
        }

        // 漏损指数 漏失水量 / 用户合法用水量
        try {
            partitionNormalLossEvaluateDTO.setLossIndex(partitionNormalLossEvaluateDTO.getLossWater().divide(partitionNormalLossEvaluateDTO.getLegalUseWater(), 2, RoundingMode.HALF_UP));
        } catch (Exception e) {
            partitionNormalLossEvaluateDTO.setLossIndex(BigDecimal.ZERO);
        }

        // 漏损评估
        partitionNormalLossEvaluateDTO.setLossValuation("一般");
        try {
            pipeMinFlowConfig = pipeMinFlowConfigMap.get(partitionNormalLossEvaluateDTO.getPartitionId());
            int ANum = 0; // 较好
            int BNum = 0; // 一般
            int CNum = 0; // 较差
            if (pipeMinFlowConfig.getNightFlowMin() == null || pipeMinFlowConfig.getNightFlowMax() == null) {
                BNum++;
            } else {
                if (minNightFlow.compareTo(pipeMinFlowConfig.getNightFlowMin()) < 0) {
                    ANum++;
                } else if (minNightFlow.compareTo(pipeMinFlowConfig.getNightFlowMax()) > 0) {
                    CNum++;
                } else {
                    BNum++;
                }
            }
            if (pipeMinFlowConfig.getUnitPipeNightFlowMin() == null || pipeMinFlowConfig.getUnitPipeNightFlowMax() == null) {
                BNum++;
            } else {
                if (partitionNormalLossEvaluateDTO.getUnitPipeNightFlowMin().compareTo(pipeMinFlowConfig.getUnitPipeNightFlowMin()) < 0) {
                    ANum++;
                } else if (partitionNormalLossEvaluateDTO.getUnitPipeNightFlowMin().compareTo(pipeMinFlowConfig.getUnitPipeNightFlowMax()) > 0) {
                    CNum++;
                } else {
                    BNum++;
                }
            }
            if (pipeMinFlowConfig.getMnfDivDayAvgHourFlowMin() == null || pipeMinFlowConfig.getMnfDivDayAvgHourFlowMax() == null) {
                BNum++;
            } else {
                if (partitionNormalLossEvaluateDTO.getMnfDivideAvgDayFlow().compareTo(pipeMinFlowConfig.getMnfDivDayAvgHourFlowMin()) < 0) {
                    ANum++;
                } else if (partitionNormalLossEvaluateDTO.getMnfDivideAvgDayFlow().compareTo(pipeMinFlowConfig.getMnfDivDayAvgHourFlowMax()) > 0) {
                    CNum++;
                } else {
                    BNum++;
                }
            }

            if (ANum > 1) {
                lossLevel = "较好";
            } else if (BNum > 1) {
                lossLevel = "较差";
            } else {
                lossLevel = "一般";
            }

        } catch (Exception e) {
            partitionNormalLossEvaluateDTO.setLossValuation(lossLevel);
        }
    }

    @Override
    public List<PartitionTotalDifferenceDTO> getTotalDifference(String partitionId, String type, String date, String start, String end) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        Long startTime;
        Long endTime;
        List<String> ymList = new ArrayList<>();
        if ("month".equals(type)) {
            ymList.add(date.replaceAll("-", "").substring(0, 6));
            start = date + "-01 00:00:00";
            startTime = LocalDate.parse(start, formatter).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
            endTime = LocalDate.parse(start, formatter).plusMonths(1L).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
        } else {
            start = start + " 00:00:00";
            end = end + " 23:59:59";
            LocalDate startDate = LocalDate.parse(start, formatter);
            LocalDate endDate = LocalDate.parse(end, formatter);
            startTime = startDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
            endTime = endDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
            while (startDate.isBefore(endDate.plusDays(1L))) {
                ymList.add(DateTimeFormatter.ofPattern("yyyy-MM-dd").format(startDate).replaceFirst("-", "").substring(0, 6));
                startDate = startDate.plusMonths(1L);
            }
            startDate = startDate.minusMonths(1L);
        }
        // 查询所有运营中的DMA分区
        Partition partition = partitionService.getById(partitionId);
        List<Partition> partitionList = new ArrayList<>();
        partitionService.getDMAChild(partition, partitionList);
        Map<String, Partition> partitionMap = new HashMap<>();
        List<String> partitionIdList = new ArrayList<>();

        // 最小流时间
        Integer minHour = null;
        Integer maxHour = null;
        for (Partition partitionTemp : partitionList) {
            partitionMap.put(partitionTemp.getId(), partitionTemp);
            partitionIdList.add(partitionTemp.getId());
            if (partitionTemp.getMinFlowStartHour() != null) {
                if (minHour == null) {
                    minHour = partitionTemp.getMinFlowStartHour();
                }
                if (minHour > partitionTemp.getMinFlowStartHour()) {
                    minHour = partitionTemp.getMinFlowStartHour();
                }
            }
            if (partitionTemp.getMinFlowEndHour() != null) {
                if (maxHour == null) {
                    maxHour = partitionTemp.getMinFlowEndHour();
                }
                if (maxHour < partitionTemp.getMinFlowEndHour()) {
                    minHour = partitionTemp.getMinFlowEndHour();
                }
            }
        }
        if (maxHour == null) {
            maxHour = 4;
        }
        if (minHour == null) {
            minHour = 2;
        }
        if (partitionIdList.size() == 0) {
            return new ArrayList<>();
        }
        // 用户数
        Map<String, Integer> userNumMap = partitionService.getUserNum(partitionIdList);
        // 该月抄表数
        Map<String, Integer> copiedNum = partitionService.getCopiedNum(partitionIdList, ymList);
        // 供水量
        Map<String, BigDecimal> supplyWater = new HashMap<>();
        // 校准供水量
        Map<String, BigDecimal> correctSupplyWater = new HashMap<>();
        pipePartitionTotalFlowService.getSupplyAndCorrectWater(partitionIdList, startTime, endTime, supplyWater, correctSupplyWater);
        // 实抄水量
        Map<String, BigDecimal> copyWater = new HashMap<>();
        // 校准用水量
        Map<String, BigDecimal> correctCopyWater = new HashMap<>();
        pipeCopyDataReadMeterDataService.getUseAndCorrectWater(partitionIdList, startTime, endTime, copyWater, correctCopyWater);

        List<PartitionTotalDifferenceDTO> result = new ArrayList<>();
        PartitionTotalDifferenceDTO partitionTotalDifferenceDTO;

        LocalDate startDate;
        LocalDate endDate = Instant.ofEpochMilli(endTime).atZone(ZoneId.systemDefault()).toLocalDate();
        Map<String, PartitionNotNormalLossEvaluateDTO> notNormalMap;

        Map<String, PartitionNotNormalLossEvaluateDTO> lossWaterMap = this.getLossWater(partitionIdList, partitionMap, startTime, endTime, userNumMap, partition.getTenantId(), minHour, maxHour);
        for (Partition tempPartition : partitionList) {
            partitionId = tempPartition.getId();
            partitionTotalDifferenceDTO = new PartitionTotalDifferenceDTO();
            partitionTotalDifferenceDTO.setPartitionId(partitionId);
            partitionTotalDifferenceDTO.setPartitionName(tempPartition.getName());

            // 抄表情况
            partitionTotalDifferenceDTO.setNeedCopyNum(userNumMap.get(partitionId));
            if (copiedNum.get(partitionId) != null) {
                partitionTotalDifferenceDTO.setRealCopyNum(copiedNum.get(partitionId));
            }
            partitionTotalDifferenceDTO.setNotCopyNum(partitionTotalDifferenceDTO.getNeedCopyNum() - partitionTotalDifferenceDTO.getRealCopyNum());
            try {
                partitionTotalDifferenceDTO.setCopyRate(BigDecimal.valueOf(partitionTotalDifferenceDTO.getRealCopyNum()).multiply(BigDecimal.valueOf(100)).divide(BigDecimal.valueOf(partitionTotalDifferenceDTO.getNeedCopyNum()), 2, RoundingMode.HALF_UP));
            } catch (Exception e) {
                partitionTotalDifferenceDTO.setCopyRate(BigDecimal.ZERO);
            }

            // 实抄水量分析
            if (supplyWater.get(partitionId) != null) {
                partitionTotalDifferenceDTO.setSupplyTotal(supplyWater.get(partitionId));
            }
            if (copyWater.get(partitionId) != null) {
                partitionTotalDifferenceDTO.setRealCopyWater(copyWater.get(partitionId));
            }
            partitionTotalDifferenceDTO.setNrwWater(partitionTotalDifferenceDTO.getSupplyTotal().subtract(partitionTotalDifferenceDTO.getRealCopyWater()));
            try {
                partitionTotalDifferenceDTO.setNrwRate((partitionTotalDifferenceDTO.getSupplyTotal().subtract(partitionTotalDifferenceDTO.getRealCopyWater()).multiply(BigDecimal.valueOf(100)).divide(partitionTotalDifferenceDTO.getSupplyTotal(), 2, RoundingMode.HALF_UP)));
            } catch (Exception e) {
            }

            // 校准水量分析
            if (correctSupplyWater.get(partitionId) != null) {
                partitionTotalDifferenceDTO.setCorrectSupplyTotal(correctSupplyWater.get(partitionId));
            }
            if (correctCopyWater.get(partitionId) != null) {
                partitionTotalDifferenceDTO.setCorrectCopyWater(correctCopyWater.get(partitionId));
            }
            partitionTotalDifferenceDTO.setCorrectNrwWater(partitionTotalDifferenceDTO.getCorrectSupplyTotal().subtract(partitionTotalDifferenceDTO.getCorrectCopyWater()));
            try {
                partitionTotalDifferenceDTO.setCorrectNrwRate((partitionTotalDifferenceDTO.getCorrectSupplyTotal().subtract(partitionTotalDifferenceDTO.getCorrectCopyWater()).multiply(BigDecimal.valueOf(100).divide(partitionTotalDifferenceDTO.getCorrectSupplyTotal(), 2, RoundingMode.HALF_UP))).setScale(2, RoundingMode.HALF_UP));
            } catch (Exception e) {
            }

            // 参考漏失水量
            try {
                partitionTotalDifferenceDTO.setReferenceLossWater(lossWaterMap.get(partitionId).getLossWater());
                if (partitionTotalDifferenceDTO.getReferenceLossWater().compareTo(BigDecimal.ZERO) < 0) {
                    partitionTotalDifferenceDTO.setReferenceLossWater(BigDecimal.ZERO);
                }
            } catch (Exception e) {
                partitionTotalDifferenceDTO.setReferenceLossWater(BigDecimal.ZERO);
            }
            // 表观漏失水量
            try {
                partitionTotalDifferenceDTO.setFaceLossWater(partitionTotalDifferenceDTO.getCorrectNrwWater().subtract(partitionTotalDifferenceDTO.getReferenceLossWater()));
            } catch (Exception e) {
                partitionTotalDifferenceDTO.setFaceLossWater(BigDecimal.ZERO);
            }

            result.add(partitionTotalDifferenceDTO);
        }

        return result;
    }

    @Override
    public List<PartitionNotNormalLossEvaluateDTO> getNormalListDetail(String date, String type, String start, String end, String partitionId, String tenantId) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        Long startTime;
        Long endTime;
        List<String> ymList = new ArrayList<>();
        if ("month".equals(type)) {
            ymList.add(date.replaceAll("-", "").substring(0, 6));
            start = date + "-01";
            startTime = LocalDate.parse(start, formatter).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
            endTime = LocalDate.parse(start, formatter).plusMonths(1L).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
        } else if ("year".equals(type)) {
            LocalDate startDate = LocalDate.of(Integer.valueOf(date), 1, 1);
            LocalDate endDate = startDate.plusYears(1L);
            startTime = startDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
            endTime = endDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli() - 1L;
            startDate = startDate.minusMonths(1L);
        } else {
            startTime = LocalDate.parse(start, formatter).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
            endTime = LocalDate.parse(end, formatter).plusDays(1L).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli() - 1L;
        }
        Partition partition = partitionService.getById(partitionId);
        if (partition == null) {
            return new ArrayList<>();
        }

        if (endTime > System.currentTimeMillis()) {
            endTime = LocalDate.now().plusDays(1L).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli() - 1L;
        }

        // 用户数
        Map<String, Integer> userNumMap = partitionService.getUserNum(Collections.singletonList(partitionId));
        int userNum = 0;
        if (userNumMap.get(partitionId) != null) {
            userNum = userNumMap.get(partitionId);
        }

        // 分区下的设备
        List<PartitionMount> partitionMountList = partitionMountMapper.getAllByPartitionId(Collections.singletonList(partitionId), DataConstants.PARTITION_TYPE.METERING.getValue(), "in", tenantId);
        List<String> deviceIdList = new ArrayList<>();
        for (PartitionMount partitionMount : partitionMountList) {
            deviceIdList.add(partitionMount.getDeviceId() + ".Instantaneous_flow");
            deviceIdList.add(partitionMount.getDeviceId() + ".total_flow");
        }
        if (deviceIdList.size() == 0) {
            deviceIdList.add("-");
        }
        // 查询区间数据
        JSONObject data = influxUtil.getData(deviceIdList, startTime, endTime, "1h");

        PartitionNotNormalLossEvaluateDTO partitionNotNormalLossEvaluateDTO;

        List<String> timeList = data.keySet().stream().sorted().collect(Collectors.toList());
        Map<String, PartitionNotNormalLossEvaluateDTO> resultMap = new LinkedHashMap<>();

        // 最小流时间
        int startHour = 2;
        int endHour = 4;
        if (partition.getMinFlowStartHour() != null) {
            startHour = partition.getMinFlowStartHour();
        }
        if (partition.getMinFlowEndHour() != null) {
            endHour = partition.getMinFlowEndHour();
        }

        // 最小流设置
        List<PipeMinFlowConfig> listByPartition = pipeMinFlowConfigService.getListByPartition(partitionId);
        PipeMinFlowConfig pipeMinFlowConfig = null;
        Map minFlowConfigMap = new HashMap<>();
        if (listByPartition.size() > 0) {
            pipeMinFlowConfig = listByPartition.get(0);
            minFlowConfigMap.put(partitionId, pipeMinFlowConfig);
        }

        int hour;
        for (String time : timeList) {
            date = time.substring(0, 10);
            hour = Integer.valueOf(time.substring(11, 13));
            partitionNotNormalLossEvaluateDTO = resultMap.get(date);
            if (partitionNotNormalLossEvaluateDTO == null) {
                partitionNotNormalLossEvaluateDTO = new PartitionNotNormalLossEvaluateDTO();
                partitionNotNormalLossEvaluateDTO.setPartitionName(date);
                partitionNotNormalLossEvaluateDTO.setPartitionId(partitionId);
                // 用户数
                partitionNotNormalLossEvaluateDTO.setUserNum(userNum);

                partitionNotNormalLossEvaluateDTO.setMainLineLength(partition.getMainLineLength());
                if (pipeMinFlowConfig != null) {
                    partitionNotNormalLossEvaluateDTO.setIncrBase(pipeMinFlowConfig.getIncrBase());
                    partitionNotNormalLossEvaluateDTO.setIncrTime(pipeMinFlowConfig.getIncrTime());
                    partitionNotNormalLossEvaluateDTO.setIncrWarn(pipeMinFlowConfig.getIncrWarn());
                    partitionNotNormalLossEvaluateDTO.setIncrError(pipeMinFlowConfig.getIncrError());
                }
            }
            if (partitionNotNormalLossEvaluateDTO.getSupplyTotal() == null) {
                partitionNotNormalLossEvaluateDTO.setSupplyTotal(BigDecimal.ZERO);
            }
            for (String deviceId : deviceIdList) {
                if (deviceId.endsWith("total_flow") && data.getJSONObject(time).getBigDecimal(deviceId) != null) {
                    partitionNotNormalLossEvaluateDTO.setSupplyTotal(partitionNotNormalLossEvaluateDTO.getSupplyTotal().add(data.getJSONObject(time).getBigDecimal(deviceId)));

                    // 最小水量
                    if (startHour <= hour && hour <= endHour) {
                        if (partitionNotNormalLossEvaluateDTO.getMinValue() == null) {
                            partitionNotNormalLossEvaluateDTO.setMinValue(data.getJSONObject(time).getBigDecimal(deviceId));
                            partitionNotNormalLossEvaluateDTO.setMinValueTimeHour(hour);
                        }
                        if (partitionNotNormalLossEvaluateDTO.getMinValue().compareTo(data.getJSONObject(time).getBigDecimal(deviceId)) > 0) {
                            partitionNotNormalLossEvaluateDTO.setMinValue(data.getJSONObject(time).getBigDecimal(deviceId));
                            partitionNotNormalLossEvaluateDTO.setMinValueTimeHour(hour);
                        }
                    }
                }

                // 最小流
                if (deviceId.endsWith("Instantaneous_flow") && data.getJSONObject(time).getBigDecimal(deviceId) != null) {
                    if (startHour <= hour && hour <= endHour) {
                        if (partitionNotNormalLossEvaluateDTO.getMinFlow() == null) {
                            partitionNotNormalLossEvaluateDTO.setMinFlow(data.getJSONObject(time).getBigDecimal(deviceId));
                            partitionNotNormalLossEvaluateDTO.setMinFlowTimeHour(hour);
                        }
                        if (partitionNotNormalLossEvaluateDTO.getMinFlow().compareTo(data.getJSONObject(time).getBigDecimal(deviceId)) > 0) {
                            partitionNotNormalLossEvaluateDTO.setMinFlow(data.getJSONObject(time).getBigDecimal(deviceId));
                            partitionNotNormalLossEvaluateDTO.setMinFlowTimeHour(hour);
                        }
                    }
                }
            }

            // 评估
            Map<String, Partition> partitionMap = new HashMap<>();
            partitionMap.put(partitionId, partition);

            this.getEvaluate(partitionMap, partitionNotNormalLossEvaluateDTO, minFlowConfigMap);

            resultMap.put(date, partitionNotNormalLossEvaluateDTO);
        }

        return resultMap.values().stream().collect(Collectors.toList());
    }

    @Override
    public List<JSONObject> getDayFlowAnalysis(String partitionId, String date, String tenantId) {
        LocalDate startDate = LocalDate.parse(date, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        LocalDate endDate = startDate.plusDays(1L);
        Long startTime = startDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
        Long endTime = endDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli() - 1L;

        // 分区下的设备
        List<PartitionMount> in = partitionMountMapper.getAllByPartitionId(Collections.singletonList(partitionId), DataConstants.PARTITION_TYPE.METERING.getValue(), "in", tenantId);
        List<String> deviceIdList = new ArrayList<>();
        for (PartitionMount partitionMount : in) {
            deviceIdList.add(partitionMount.getDeviceId() + ".Instantaneous_flow");
            deviceIdList.add(partitionMount.getDeviceId() + ".total_flow");
        }
        if (deviceIdList.size() == 0) {
            deviceIdList.add("-");
        }
        // 查询区间数据
        JSONObject data = influxUtil.getData(deviceIdList, startTime, endTime, "1h");
        Map<Integer, JSONObject> resultMap = new HashMap<>();
        List<String> timeList = data.keySet().stream().collect(Collectors.toList());
        JSONObject tempObject;
        int hour;
        for (String time : timeList) {
            hour = Integer.valueOf(time.substring(11, 13));
            tempObject = resultMap.get(hour);
            if (tempObject == null) {
                tempObject = new JSONObject();
                tempObject.put("time", hour);
                tempObject.put("supply", BigDecimal.ZERO);
                tempObject.put("flow", BigDecimal.ZERO);
            }
            for (String deviceId : deviceIdList) {
                if (deviceId.endsWith("total_flow") && data.getJSONObject(time).getBigDecimal(deviceId) != null) {
                    tempObject.put("supply", tempObject.getBigDecimal("supply").add(data.getJSONObject(time).getBigDecimal(deviceId)));
                }
                if (deviceId.endsWith("Instantaneous_flow") && data.getJSONObject(time).getBigDecimal(deviceId) != null) {
                    tempObject.put("flow", tempObject.getBigDecimal("flow").add(data.getJSONObject(time).getBigDecimal(deviceId)));
                }
            }

            resultMap.put(hour, tempObject);
        }

        for (int i = 0; i < 24; i++) {
            if (resultMap.get(i) == null) {
                tempObject = new JSONObject();
                tempObject.put("time", i);
                tempObject.put("supply", BigDecimal.ZERO);
                tempObject.put("flow", BigDecimal.ZERO);
                resultMap.put(i, tempObject);
            }
        }

        return resultMap.values().stream().sorted(Comparator.comparingInt(a -> a.getInteger("time"))).collect(Collectors.toList());

    }

    @Override
    public Map<String, PartitionNotNormalLossEvaluateDTO> getLossWater(List<String> partitionIdList, Map<String, Partition> partitionMap, Long startTime, Long endTime, Map<String, Integer> userNumMap, String tenantId, int minHour, int maxHour) {
        List<PartitionMount> partitionMountList = partitionMountMapper.getAllByPartitionId(partitionIdList, DataConstants.PARTITION_TYPE.METERING.getValue(), "in", tenantId);
        if (partitionMountList.size() == 0) {
            return new HashMap<>();
        }
        List<String> deviceIdList = new ArrayList<>();
        Map<String, Map<String, PartitionNotNormalLossEvaluateDTO>> partitionNotNormalLossEvaluateDTOMap = new HashMap<>();
        Map<String, String> deviceIdPartitionMap = new HashMap<>();
        for (PartitionMount partitionMount : partitionMountList) {
            deviceIdList.add(partitionMount.getDeviceId() + ".Instantaneous_flow");

            deviceIdPartitionMap.put(partitionMount.getDeviceId() + ".Instantaneous_flow", partitionMount.getPartitionId());
        }
        if (deviceIdList.size() == 0) {
            deviceIdList.add("-");
        }
        // 查询区间数据
        JSONObject data = influxUtil.getData(deviceIdList, startTime, endTime, "1h");

        PartitionNotNormalLossEvaluateDTO partitionNotNormalLossEvaluateDTO = new PartitionNotNormalLossEvaluateDTO();
        List<String> timeList = data.keySet().stream().collect(Collectors.toList());

        int startHour = 2;
        int endHour = 4;

        int hour;
        String day;
        Partition partition;
        Map<String, PartitionNotNormalLossEvaluateDTO> subMap;
        for (String time : timeList) {
            day = time.substring(0, 10);
            hour = Integer.valueOf(time.substring(11, 13));
            if (hour < minHour || hour > maxHour) {
                continue;
            }

            if (data.getJSONObject(time) != null) {
                for (String deviceId : deviceIdList) {
                    if (data.getJSONObject(time).get(deviceId) == null) {
                        continue;
                    }
                    subMap = partitionNotNormalLossEvaluateDTOMap.get(deviceIdPartitionMap.get(deviceId));
                    if (subMap == null) {
                        subMap = new HashMap<>();
                        partitionNotNormalLossEvaluateDTOMap.put(deviceIdPartitionMap.get(deviceId), subMap);
                    }
                    partitionNotNormalLossEvaluateDTO = subMap.get(day);
                    if (partitionNotNormalLossEvaluateDTO == null) {
                        partitionNotNormalLossEvaluateDTO = new PartitionNotNormalLossEvaluateDTO();
                        partitionNotNormalLossEvaluateDTO.setMinFlow(data.getJSONObject(time).getBigDecimal(deviceId));

                        subMap.put(day, partitionNotNormalLossEvaluateDTO);
                    }
                    partition = partitionMap.get(deviceIdPartitionMap.get(deviceId));
                    if (partition.getMinFlowStartHour() != null) {
                        startHour = partition.getMinFlowStartHour();
                    }
                    if (partition.getMinFlowEndHour() != null) {
                        endHour = partition.getMinFlowEndHour();
                    }
                    if (hour >= startHour && hour <= endHour) {
                        if (partitionNotNormalLossEvaluateDTO.getMinFlow().compareTo(data.getJSONObject(time).getBigDecimal(deviceId)) > 0) {
                            partitionNotNormalLossEvaluateDTO.setMinFlow(data.getJSONObject(time).getBigDecimal(deviceId));
                        }
                    }

                }
            }
        }

        Map<String, PartitionNotNormalLossEvaluateDTO> resultMap = new HashMap<>();
        partitionIdList = partitionNotNormalLossEvaluateDTOMap.keySet().stream().collect(Collectors.toList());
        List<PartitionNotNormalLossEvaluateDTO> partitionNotNormalLossEvaluateDTOList;
        for (String partitionId : partitionIdList) {
            PartitionNotNormalLossEvaluateDTO result = resultMap.get(partitionId);
            if (result == null) {
                result = new PartitionNotNormalLossEvaluateDTO();
                result.setLossWater(BigDecimal.ZERO);
                resultMap.put(partitionId, result);
            }
            subMap = partitionNotNormalLossEvaluateDTOMap.get(partitionId);
            if (subMap == null) {
                continue;
            }
            partitionNotNormalLossEvaluateDTOList = subMap.values().stream().collect(Collectors.toList());
            for (PartitionNotNormalLossEvaluateDTO partitionNotNormalLossEvaluateDTOTemp : partitionNotNormalLossEvaluateDTOList) {
                if (partitionNotNormalLossEvaluateDTOTemp.getMinFlow() != null) {
                    // 用户合法用水量  用水定额*注册用户数量
                    try {
                        partitionNotNormalLossEvaluateDTOTemp.setLegalUseWater(partitionMap.get(partitionId).getLegalUseWater().multiply(BigDecimal.valueOf(userNumMap.get(partitionId))));
                    } catch (Exception e) {
                        partitionNotNormalLossEvaluateDTOTemp.setLegalUseWater(BigDecimal.ZERO);
                    }

                    // 净夜间流量 MNF-用户合法用水量-大用户用水量
                    try {
                        partitionNotNormalLossEvaluateDTOTemp.setNetNightFlow(partitionNotNormalLossEvaluateDTOTemp.getMinFlow().subtract(partitionNotNormalLossEvaluateDTOTemp.getLegalUseWater()));
                    } catch (Exception e) {
                        partitionNotNormalLossEvaluateDTOTemp.setNetNightFlow(BigDecimal.ZERO);
                    }
                    // 漏失水量
                    try {
                        partitionNotNormalLossEvaluateDTOTemp.setLossWater(partitionNotNormalLossEvaluateDTOTemp.getNetNightFlow().multiply(BigDecimal.valueOf(24)));
                        if (partitionNotNormalLossEvaluateDTOTemp.getLossWater().compareTo(BigDecimal.ZERO) < 0) {
                            partitionNotNormalLossEvaluateDTOTemp.setLossWater(BigDecimal.ZERO);
                        }
                    } catch (Exception e) {
                        partitionNotNormalLossEvaluateDTOTemp.setLossWater(BigDecimal.ZERO);
                    }
                    result.setLossWater(result.getLossWater().add(partitionNotNormalLossEvaluateDTOTemp.getLossWater()));
                }
            }
        }
        return resultMap;
    }

}
