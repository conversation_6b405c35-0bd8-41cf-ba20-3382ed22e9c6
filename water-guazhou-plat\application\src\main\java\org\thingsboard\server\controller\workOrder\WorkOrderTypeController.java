package org.thingsboard.server.controller.workOrder;

import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrderType;
import org.thingsboard.server.dao.orderWork.WorkOrderTypeService;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.List;

/**
 * 工单类型信息
 */
@RestController
@RequestMapping("api/workOrderType")
public class WorkOrderTypeController extends BaseController {

    @Autowired
    private WorkOrderTypeService workOrderTypeService;

    @GetMapping("list")
    public IstarResponse findList(@RequestParam(required = false, defaultValue = "") String status) throws ThingsboardException {
        return IstarResponse.ok(workOrderTypeService.findList(status, getTenantId()));
    }


    @PostMapping("save")
    public IstarResponse save(@RequestBody WorkOrderType entity) throws ThingsboardException {
        entity.setTenantId(UUIDConverter.fromTimeUUID(getTenantId().getId()));
        workOrderTypeService.save(entity);
        return IstarResponse.ok();
    }

    @PostMapping("restore")
    public IstarResponse restore(@RequestBody JSONObject param) {
        String id = param.getString("id");
        workOrderTypeService.changeStatus("1", id);

        return IstarResponse.ok();
    }

    @PostMapping("remove")
    public IstarResponse remove(@RequestBody List<String> ids) {
        for (String id : ids) {
            workOrderTypeService.changeStatus("2", id);
        }
        return IstarResponse.ok();
    }

    @GetMapping("findByResourceId")
    public IstarResponse findByResourceId(@RequestParam String resourceId) {
        return IstarResponse.ok(workOrderTypeService.findByResourceId(resourceId));
    }

    @GetMapping("findTypeIdByResourceId")
    public IstarResponse findTypeIdByResourceId(@RequestParam String resourceId) {
        return IstarResponse.ok(workOrderTypeService.findTypeIdByResourceId(resourceId));
    }


}
