<!-- 新增管网 -->
<!-- toDo: 绘制后进行属性输入与提交 -->
<!-- toDo: 坐标输入返回后没清除绘制 -->
<template>
  <RightDrawerMap
    ref="refMap"
    title="新增管网"
    @map-loaded="onMaploaded"
  >
    <div
      v-if="state.curPage === 'add'"
      class="form-wrapper"
    >
      <div class="add-form overlay-y">
        <Form
          ref="refFormAdd"
          :config="FormConfigAdd"
        ></Form>
      </div>

      <div class="add-footer">
        <el-button
          type="default"
          @click="handleBack"
        >
          取消
        </el-button>
        <el-button
          :loading="FormConfigAdd.submitting"
          type="primary"
          @click="() => refFormAdd?.Submit()"
        >
          确定
        </el-button>
      </div>
    </div>
    <Form
      v-show="state.curPage === 'index'"
      ref="refForm"
      :config="FormConfig"
    ></Form>
  </RightDrawerMap>
</template>
<script lang="ts" setup>
import { queryLayerClassName } from '@/api/mapservice'
import { PostGisOperateLog } from '@/api/system/gisSetting'
import { formatterDate } from '@/utils/GlobalHelper'
import {
  calcLength,
  createPoint,
  createPolyline,
  excuteIdentify,
  generate4548Graphic,
  getGraphicLayer,
  getNeerestPoint,
  getSubLayerIds,
  gotoAndHighLight,
  initDrawer,
  initIdentifyParams,
  setMapCursor,
  setSymbol
} from '@/utils/MapHelper'
import { SLConfirm, SLMessage } from '@/utils/Message'
import Graphic from '@arcgis/core/Graphic.js'
import Point from '@arcgis/core/geometry/Point.js'
import Polyline from '@arcgis/core/geometry/Polyline.js'
import * as coordinateFormatter from '@arcgis/core/geometry/coordinateFormatter.js'
import FeatureLayer from '@arcgis/core/layers/FeatureLayer.js'
import RightDrawerMap from '../../components/common/RightDrawerMap.vue'
import { EGigLogFunc, EGisLogApp, EGisLogOperateType } from '../../config'
import { initPipeEditForm } from './config'

const state = reactive<{
  layerIds: number[]
  layerInfos: any[]
  curLayerInfo?: any
  curPage: 'index' | 'add'
  timer: any
}>({
  layerIds: [],
  layerInfos: [],
  curPage: 'index',
  timer: null
})
const staticState: {
  view?: __esri.MapView
  drawer?: __esri.Draw
  drawAction?: __esri.DrawAction
  graphic?: __esri.Graphic
  submitGraphic?: __esri.Graphic
  vertices: number[][]
  graphicsLayer?: __esri.GraphicsLayer
  textLayer?: __esri.GraphicsLayer
  moveEvent?: any
  identifyResult?: any
} = {
  vertices: []
}
const refForm = ref<IFormIns>()
const TableConfig = reactive<ITable>({
  height: 198,
  pagination: {
    hide: true
  },
  columns: [
    { minWidth: 60, label: '', prop: 'name' },
    {
      minWidth: 100,
      label: '经度(x)',
      prop: 'x',
      tableDataName: 'tableList',
      formItemConfig: {
        type: 'input-number',
        rules: [{ required: true, message: '请输入坐标' }]
      }
    },
    {
      minWidth: 100,
      label: '纬度(y)',
      prop: 'y',
      tableDataName: 'tableList',
      formItemConfig: {
        type: 'input-number',
        rules: [{ required: true, message: '请输入坐标' }]
      }
    }
  ],
  dataList: [{ name: '坐标' }]
})
const FormConfig = reactive<IFormConfig>({
  labelPosition: 'top',
  group: [
    {
      id: 'layer',
      fieldset: {
        desc: '选择图层'
      },
      fields: [
        {
          type: 'tree',
          checkStrictly: true,
          showCheckbox: true,
          field: 'layerid',
          nodeKey: 'value',
          options: [],
          handleCheckChange: (data, isChecked) => {
            if (isChecked) {
              refForm.value && (refForm.value.dataForm.layerid = [data.value])

              state.curLayerInfo = state.layerInfos.find(item => item.layerid === data.value)
              if (state.curLayerInfo.geometrytype === 'esriGeometryPolyline') {
                TableConfig.dataList = [{ name: '起点' }, { name: '终点' }]
              } else if (state.curLayerInfo.geometrytype === 'esriGeometryPoint') {
                TableConfig.dataList = [{ name: '坐标' }]
              }
            }
          }
        }
      ]
    },
    {
      fieldset: {
        desc: '录入方式'
      },
      fields: [
        {
          type: 'radio',
          field: 'inputType',
          options: [
            { label: '地图绘制', value: 'map' },
            { label: '坐标录入', value: 'input' }
          ]
        },
        {
          handleHidden: (params, query, config) => {
            config.hidden = params.inputType !== 'map'
          },
          label: '启用捕捉',
          width: 60,
          type: 'switch',
          field: 'capture',
          activeText: '启用',
          inActiveText: '关闭'
          // onChange: val => {
          //   if (val === true) {
          //     initSnapping()
          //   } else {
          //     state.timer && clearTimeout(state.timer)
          //     staticState.moveEvent?.remove()
          //   }
          // }
        },
        {
          type: 'select',
          field: 'captureLayerId',
          label: '捕捉图层',
          multiple: true,
          options: [],
          handleHidden: (params, query, config) => {
            config.hidden = params.capture !== true || params.inputType !== 'map'
          }
        },
        {
          type: 'btn-group',
          btns: [{ perm: true, text: '绘制', click: () => initDraw() }],
          handleHidden: (params, query, config) => {
            config.hidden = params.inputType !== 'map'
          }
        },
        {
          type: 'table',
          hidden: true,
          config: TableConfig,
          field: 'tableList',
          handleHidden: (params, query, config) => {
            config.hidden = params.inputType !== 'input'
          }
        },
        {
          type: 'btn-group',
          hidden: true,
          itemContainerStyle: {
            marginTop: '12px'
          },
          btns: [
            {
              perm: true,
              text: '确定',
              styles: {
                marginLeft: 'auto'
              },
              click: () => handleTableConfirm()
            }
          ],
          handleHidden: (params, query, config) => {
            config.hidden = params.inputType !== 'input'
          }
        }
      ]
    }
  ],
  defaultValue: {
    capture: false,
    inputType: 'map'
  }
})
const handleTableConfirm = () => {
  if (state.curLayerInfo.geometrytype === 'esriGeometryPolyline') {
    const paths = TableConfig.dataList
      .filter(item => item.x && item.y)
      .map(item => {
        const point = new Point({
          longitude: item.x,
          latitude: item.y,
          spatialReference: staticState.view?.spatialReference
        })
        return [point.x, point.y]
      })
    if (paths.length < 2) {
      SLMessage.warning('请完整输入坐标信息')
      return
    }
    staticState.graphic = new Graphic({
      geometry: new Polyline({
        paths: [paths],
        spatialReference: staticState.view?.spatialReference
      }),
      symbol: setSymbol('polyline')
    }) as __esri.Graphic
    staticState.vertices = paths
    staticState.graphicsLayer?.removeAll()
    staticState.graphicsLayer?.add(staticState.graphic)
  } else if (state.curLayerInfo.geometrytype === 'esriGeometryPoint') {
    const location = TableConfig.dataList.filter(item => item.x && item.y)[0]
    if (!location) {
      SLMessage.warning('请完整输入坐标信息')
      return
    }
    staticState.graphic = new Graphic({
      geometry: new Point({
        longitude: location.x,
        latitude: location.y,
        spatialReference: staticState.view?.spatialReference
      }),
      symbol: setSymbol('point')
    }) as __esri.Graphic
    staticState.graphicsLayer?.removeAll()
    staticState.graphicsLayer?.add(staticState.graphic)
  }
  gotoAndHighLight(staticState.view, staticState.graphic, {
    avoidHighlight: true
  })
  resetFormConfig()
  state.curPage = 'add'
}
const refFormAdd = ref<IFormIns>()
const FormConfigAdd = reactive<IFormConfig>({
  labelPosition: 'top',
  group: [
    {
      fieldset: {
        desc: computed<string>(() => state.curLayerInfo?.layername) as any
      },
      fields: []
    }
  ],
  defaultValue: {},
  submit: params => {
    SLConfirm('该操作变更空间数据，是否继续？', '提示信息')
      .then(() => {
        if (!staticState.submitGraphic) {
          SLMessage.error('绘制图形丢失，请重新绘制图形')
          return
        }
        const layerid = refForm.value?.dataForm.layerid
        if (layerid === undefined) {
          SLMessage.error('未找到图层信息，请重新选择图层和绘制')
          return
        }
        FormConfigAdd.submitting = true
        const layer = new FeatureLayer({
          url:
            window.SITE_CONFIG.GIS_CONFIG.gisService
            + window.SITE_CONFIG.GIS_CONFIG.gisPipeFeatureServiceFeatureServer
            + '/'
            + layerid,
          id: 'feature-layer'
        })
        const layerName = state.curLayerInfo.layername
        const submitParams = {
          ...params,
          CREATEDDATE: moment().format(formatterDate)
        }
        staticState.submitGraphic.attributes = submitParams
        layer
          ?.applyEdits({
            addFeatures: [staticState.submitGraphic]
          })
          .then(res => {
            SLMessage.success('操作成功')
            PostGisOperateLog({
              optionName: EGigLogFunc.XINZENGGW,
              type: EGisLogApp.BASICGIS,
              content: `${EGisLogOperateType.ADD}${layerName},OBJECTID:${res.addFeatureResults[0].objectId}`,
              optionType: EGisLogOperateType.ADD
            }).catch(() => {
              console.log('生成gis操作日志失败')
            })
            const pipelayer = staticState.view?.map.findLayerById('pipelayer') as __esri.MapImageLayer
            pipelayer && pipelayer.refresh()
            refFormAdd.value?.resetForm()
            state.curPage = 'index'
            staticState.graphicsLayer?.removeAll()
            staticState.graphic = undefined
          })
          .catch(error => {
            console.log(error)
            SLMessage.error('操作失败')
          })
          .finally(() => {
            FormConfigAdd.submitting = false
          })
      })
      .catch(() => {
        //
      })
  }
})
const handleBack = () => {
  refFormAdd.value?.resetForm()
  state.curPage = 'index'
}
const getLayerInfo = async () => {
  state.layerIds = getSubLayerIds(staticState.view)
  const layerInfo = await queryLayerClassName(state.layerIds)
  state.layerInfos = layerInfo.data?.result?.rows || []
  const field = FormConfig.group[0].fields[0] as IFormTree
  const field1 = FormConfig.group[1].fields[2] as IFormSelect
  const points = state.layerInfos
    .filter(item => item.geometrytype === 'esriGeometryPoint')
    .map(item => {
      return {
        label: item.layername,
        value: item.layerid,
        data: item
      }
    })
  const lines = state.layerInfos
    .filter(item => item.geometrytype === 'esriGeometryPolyline')
    .map(item => {
      return {
        label: item.layername,
        value: item.layerid,
        data: item
      }
    })
  field
    && (field.options = [
      { label: '管线类', value: -2, children: lines, disabled: true },
      { label: '管点类', value: -1, children: points, disabled: true }
    ])
  field1.options = [...points, ...lines]
  if (!refForm.value) return
  refForm.value.dataForm.layerid = state.layerIds.slice(0, 1)
  // refForm.value.dataForm.captureLayerId = state.layerIds[0]
  state.curLayerInfo = state.layerInfos.find(item => item.layerid === state.layerIds[0])
}
const initDraw = () => {
  if (!state.curLayerInfo || !staticState.view) return
  const geometryType = state.curLayerInfo?.geometrytype === 'esriGeometryPolyline'
    ? 'polyline'
    : state.curLayerInfo?.geometrytype === 'esriGeometryPoint'
      ? 'point'
      : ''
  if (!geometryType) return
  setMapCursor('crosshair')
  const captureAble = refForm.value?.dataForm.capture
  if (captureAble) {
    initSnapping()
  } else {
    state.timer && clearTimeout(state.timer)
    staticState.moveEvent?.remove()
  }
  staticState.vertices.length = 0
  staticState.drawer?.destroy()
  staticState.drawer = initDrawer(staticState.view)
  staticState.drawAction?.destroy()
  staticState.drawAction = staticState.drawer?.create(geometryType)
  geometryType === 'polyline'
    && staticState.drawAction?.on(['vertex-add'], e => {
      updateVertices(e, geometryType, 'add')
    })
  staticState.drawAction?.on(['cursor-update'], e => {
    updateVertices(e, geometryType, 'update')
  })
  staticState.drawAction?.on('draw-complete', async e => {
    updateVertices(e, geometryType)
    setMapCursor('')
    state.curPage = 'add'
    resetFormConfig()
  })
}
const resetFormConfig = async () => {
  FormConfigAdd.submitting = true
  try {
    const layerid = refForm.value?.dataForm.layerid?.[0]
    if (layerid === undefined) {
      SLMessage.warning('请先选择图层')
      return
    }
    const fields = await initPipeEditForm(layerid)
    if (!refFormAdd.value) return
    FormConfigAdd.group[0].fields = [...fields]
    if (!staticState.graphic) {
      SLMessage.error('请先绘制图形')
      return
    }
    // 不需要特定站点的坐标转换
    staticState.submitGraphic = staticState.graphic
    if (staticState.graphic?.geometry.type === 'polyline') {
      const pipelength = calcLength(staticState.vertices, 'meters', staticState.view?.spatialReference)
      refFormAdd.value.dataForm.PIPELENGTH = pipelength
      // if (window.SITE_CONFIG.SITENAME === 'qingyang') {
      //   const submitCoords = staticState.vertices.map(to4548)
      //   staticState.submitGraphic = new Graphic({
      //     geometry: new Polyline({
      //       paths: [submitCoords],
      //       spatialReference: new SpatialReference({ wkid: 4548 })
      //     })
      //   })
      // } else {
      //   staticState.submitGraphic = staticState.graphic
      // }
    } else {
      const point = staticState.submitGraphic?.geometry as Point
      refFormAdd.value.dataForm.X = point.x
      refFormAdd.value.dataForm.Y = point.y
      // if (window.SITE_CONFIG.SITENAME === 'qingyang') {
      //   coordinateFormatter.load().then(() => {
      //     const coords = to4548([point.x, point.y])
      //     staticState.submitGraphic = new Graphic({
      //       geometry: new Point({
      //         x: coords[0],
      //         y: coords[1],
      //         spatialReference: new SpatialReference({ wkid: 4548 })
      //       })
      //     })
      //     // 可以在这里修改点的坐标
      //     if (refFormAdd.value) {
      //       refFormAdd.value.dataForm.X = coords[0]
      //       refFormAdd.value.dataForm.Y = coords[1]
      //     }
      //   })
      // } else {
      //   staticState.submitGraphic = staticState.graphic
      //   // 可以在这里修改点的坐标
      //   refFormAdd.value.dataForm.X = point.x
      //   refFormAdd.value.dataForm.Y = point.y
      // }
    }
  } catch (error) {
    state.curPage = 'index'
    SLMessage.error('暂时无法查询到管网服务详情，请稍候再试')
  }
  FormConfigAdd.submitting = false
}
const updateVertices = (e, type: 'polyline' | 'point', eventType?: 'add' | 'update') => {
  staticState.graphicsLayer?.removeAll()
  const vertices = e.vertices
  if (type === 'point') {
    if (staticState.identifyResult) {
      const point = getNeerestPoint(
        staticState.identifyResult.feature?.geometry,
        new Point({
          x: vertices[vertices.length - 1][0],
          y: vertices[vertices.length - 1][1],
          spatialReference: staticState.view?.spatialReference
        })
      )

      staticState.graphic = new Graphic({
        geometry: point,
        symbol: setSymbol('point')
      }) as __esri.Graphic
      staticState.vertices = vertices
      point && staticState.graphicsLayer?.add(staticState.graphic)
    } else {
      staticState.graphic = createPoint(vertices, staticState.view?.spatialReference, setSymbol('point'))
      staticState.vertices = vertices
      staticState.graphic && staticState.graphicsLayer?.add(staticState.graphic)
    }
  } else if (type === 'polyline') {
    if (staticState.identifyResult) {
      if (eventType === 'add') {
        const point = getNeerestPoint(
          staticState.identifyResult.feature?.geometry,
          new Point({
            x: vertices[vertices.length - 1][0],
            y: vertices[vertices.length - 1][1],
            spatialReference: staticState.view?.spatialReference
          })
        )
        if (point) {
          vertices[vertices.length - 1][0] = point.x
          vertices[vertices.length - 1][1] = point.y
        }
      }
    }
    const polyline = createPolyline(vertices, staticState.view?.spatialReference, setSymbol('polyline'))
    staticState.graphic = polyline
    staticState.vertices = vertices
    polyline && staticState.graphicsLayer?.add(polyline)
    if (eventType === 'add' && vertices.length === 2) {
      staticState.drawAction?.destroy()
      setMapCursor('')
      state.curPage = 'add'
      resetFormConfig()
    }
  }
}
const initSnapping = () => {
  staticState.moveEvent?.remove()
  staticState.moveEvent = staticState.view?.on('pointer-move', e => {
    state.timer && clearTimeout(state.timer)
    staticState.textLayer?.removeAll()
    staticState.identifyResult = undefined
    state.timer = setTimeout(() => {
      const isSnapping = refForm.value?.dataForm.capture
      if (!isSnapping) return
      const mapPoint = staticState.view?.toMap(e)
      doIdentify(mapPoint, state.timer)
    }, 200)
  })
}

const doIdentify = async (mapPoint?: __esri.Point, timer?: any) => {
  if (!staticState.view || !mapPoint) return
  try {
    const layerIds = refForm.value?.dataForm.captureLayerId
    if (!layerIds?.length) return
    const identityParams = initIdentifyParams({
      geometry: mapPoint,
      mapExtent: staticState.view.extent,
      layerIds
    })
    const res = await excuteIdentify(
      window.SITE_CONFIG.GIS_CONFIG.gisService + window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService,
      identityParams
    )
    if (timer !== state.timer) return
    staticState.textLayer?.removeAll()
    staticState.identifyResult = res.results?.[0]
    if (!staticState.identifyResult) return
    const point = getNeerestPoint(staticState.identifyResult.feature?.geometry, mapPoint)
    if (!point) return
    const textGraphic = new Graphic({
      geometry: point,
      symbol: setSymbol('text', {
        yOffset: 15,
        text: staticState.identifyResult.layerName + ':' + staticState.identifyResult.feature.attributes['新编号']
      })
    })
    staticState.textLayer?.add(textGraphic)
  } catch (error) {
    staticState.textLayer?.removeAll()
  }
}

const onMaploaded = async view => {
  coordinateFormatter.load()
  staticState.view = view
  staticState.graphicsLayer = getGraphicLayer(staticState.view, {
    id: 'pipe-add',
    title: '新增管网'
  })
  staticState.textLayer = getGraphicLayer(staticState.view, {
    id: 'pipe-add-text',
    title: '新增管网-临时标注'
  })
  await getLayerInfo()
}
onBeforeUnmount(() => {
  staticState.drawAction?.destroy()
  staticState.drawer?.destroy()
  staticState.graphicsLayer?.removeAll()
  staticState.moveEvent?.remove()
  staticState.textLayer?.removeAll()
})
</script>
<style lang="scss" scoped>
.form-wrapper {
  height: 100%;
  .add-form {
    padding-right: 8px;
    height: calc(100% - 50px);
  }
  .add-footer {
    margin-top: 12px;
    height: 50px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
}
</style>
