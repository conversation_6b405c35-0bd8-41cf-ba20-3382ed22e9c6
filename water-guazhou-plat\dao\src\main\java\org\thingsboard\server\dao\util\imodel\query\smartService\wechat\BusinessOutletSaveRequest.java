package org.thingsboard.server.dao.util.imodel.query.smartService.wechat;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartService.wechat.BusinessOutlet;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

@Getter
@Setter
public class BusinessOutletSaveRequest extends SaveRequest<BusinessOutlet> {
    // 图片
    private String image;

    // 名称
    @NotNullOrEmpty
    private String name;

    // 联系电话
    @NotNullOrEmpty
    private String phone;

    // 经度
    @NotNullOrEmpty
    private String lat;

    // 纬度
    @NotNullOrEmpty
    private String lon;

    // 地址
    @NotNullOrEmpty
    private String address;

    // 上班时间
    @NotNullOrEmpty
    private String beginTime;

    // 下班时间
    @NotNullOrEmpty
    private String endTime;

    // 备注
    private String remark;

    @Override
    protected BusinessOutlet build() {
        BusinessOutlet entity = new BusinessOutlet();
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    @Override
    protected BusinessOutlet update(String id) {
        BusinessOutlet entity = new BusinessOutlet();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(BusinessOutlet entity) {
        entity.setImage(image);
        entity.setName(name);
        entity.setPhone(phone);
        entity.setLat(lat);
        entity.setLon(lon);
        entity.setAddress(address);
        entity.setBeginTime(beginTime);
        entity.setEndTime(endTime);
        entity.setRemark(remark);
    }

}