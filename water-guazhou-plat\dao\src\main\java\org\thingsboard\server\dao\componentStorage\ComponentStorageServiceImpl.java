package org.thingsboard.server.dao.componentStorage;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.User;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.ComponentStorageEntity;
import org.thingsboard.server.dao.sql.componentStorage.ComponentStorageRepository;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class ComponentStorageServiceImpl implements ComponentStorageService {

    @Autowired
    private ComponentStorageRepository componentStorageRepository;

    @Override
    public ComponentStorageEntity get(String id) {
        return componentStorageRepository.findOne(id);
    }

    @Override
    public PageData<ComponentStorageEntity> findList(int page, int size, String code, String name, String type, TenantId tenantId) {
        // 分页参数
        PageRequest pageable = new PageRequest(page - 1, size, Sort.Direction.DESC, "createTime");
        // 查询
        if (StringUtils.isBlank(type)) {
            type = "%%";
        }
        Page<ComponentStorageEntity> pageResult = componentStorageRepository.findList(code, name, type, UUIDConverter.fromTimeUUID(tenantId.getId()), pageable);

        return new PageData<>(pageResult.getTotalElements(), pageResult.getContent());
    }

    @Override
    public List<ComponentStorageEntity> all(TenantId tenantId) {
        return componentStorageRepository.findAll(UUIDConverter.fromTimeUUID(tenantId.getId()));
    }

    @Override
    public List<String> specificationList(TenantId tenantId) {
        return componentStorageRepository.specificationList(UUIDConverter.fromTimeUUID(tenantId.getId()));
    }

    @Override
    public Object save(ComponentStorageEntity entity, User currentUser) {
        JSONObject result = new JSONObject();
        // 校验code是否已存在
        ComponentStorageEntity componentStorageEntity = componentStorageRepository.findByCodeAndIsDel(entity.getCode(), "0");
        if (componentStorageEntity != null && !componentStorageEntity.getId().equals(entity.getId())) {
            result.put("code", 400);
            result.put("message", "备件编号已存在");

            return result;
        }

        // 新增
        if (StringUtils.isBlank(entity.getId())) {
            entity.setCreator(currentUser.getFirstName());
            entity.setCreateTime(new Date());
            entity.setNumber(0L);
            entity.setTenantId(UUIDConverter.fromTimeUUID(currentUser.getTenantId().getId()));
            entity.setIsDel("0");
        }

        return componentStorageRepository.save(entity);
    }

    @Override
    @Transactional
    public void remove(List<String> ids) {
        List<ComponentStorageEntity> updateList = new ArrayList<>();
        for (String id : ids) {
            ComponentStorageEntity entity = componentStorageRepository.findOne(id);
            if (entity != null) {
                entity.setIsDel("1");
                updateList.add(entity);
            }
        }
        componentStorageRepository.save(updateList);
    }

    @Override
    public void save(ComponentStorageEntity entity) {
        componentStorageRepository.save(entity);
    }

    @Override
    public void save(List<ComponentStorageEntity> updateList) {
        componentStorageRepository.save(updateList);
    }
}
