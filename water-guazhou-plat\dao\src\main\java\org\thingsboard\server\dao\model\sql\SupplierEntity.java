package org.thingsboard.server.dao.model.sql;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2021-06-01
 */
@TableName("tb_supplier")
@Data
public class SupplierEntity {

    @TableId
    private String id;

    private String name;

    private String address;

    private String contact;

    private String contactPhone;

    private String website;

    private String importance;

    private String companySize;

    private String email;

    private String deviceTypeId;

    private String status;

    private String creator;

    private String remark;

    private String invoice;

    private Date createTime;

    private Date updateTime;

    private String tenantId;

    private transient String creatorName;

    private transient String deviceTypeName;

    private transient List<SupplierGoodsEntity> supplierGoodsList;

    private transient List<SupplierQualificationsEntity> supplierQualificationsList;
}
