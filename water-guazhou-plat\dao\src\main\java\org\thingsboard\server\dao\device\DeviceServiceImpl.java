/**
 * Copyright © 2016-2019 The Thingsboard Authors
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.device;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Function;
import com.google.common.util.concurrent.Futures;
import com.google.common.util.concurrent.ListenableFuture;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.codehaus.jackson.map.ObjectMapper;
import org.codehaus.jackson.type.TypeReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.thingsboard.server.common.data.*;
import org.thingsboard.server.common.data.constantsAttribute.DeviceAttribute;
import org.thingsboard.server.common.data.constantsAttribute.DisplayObj;
import org.thingsboard.server.common.data.constantsAttribute.PropAttribute;
import org.thingsboard.server.common.data.device.DeviceControl;
import org.thingsboard.server.common.data.device.DeviceFullData;
import org.thingsboard.server.common.data.device.DeviceSearchQuery;
import org.thingsboard.server.common.data.id.CustomerId;
import org.thingsboard.server.common.data.id.DeviceId;
import org.thingsboard.server.common.data.id.EntityId;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.kv.*;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.common.data.page.TextPageData;
import org.thingsboard.server.common.data.page.TextPageLink;
import org.thingsboard.server.common.data.relation.EntityRelation;
import org.thingsboard.server.common.data.relation.EntitySearchDirection;
import org.thingsboard.server.common.data.security.DeviceCredentials;
import org.thingsboard.server.common.data.security.DeviceCredentialsType;
import org.thingsboard.server.common.data.utils.DateUtils;
import org.thingsboard.server.dao.alarm.AlarmDao;
import org.thingsboard.server.dao.alarm.AlarmJsonDao;
import org.thingsboard.server.dao.alarm.AlarmService;
import org.thingsboard.server.dao.attributes.AttributesDao;
import org.thingsboard.server.dao.customer.CustomerDao;
import org.thingsboard.server.dao.dataSource.JobService;
import org.thingsboard.server.dao.entity.AbstractEntityService;
import org.thingsboard.server.dao.entityview.EntityViewService;
import org.thingsboard.server.dao.exception.DataValidationException;
import org.thingsboard.server.dao.model.DTO.DevicePartitionDTO;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.model.request.PartitionMountRequest;
import org.thingsboard.server.dao.model.sql.ProjectEntity;
import org.thingsboard.server.dao.model.sql.ProjectRelationEntity;
import org.thingsboard.server.dao.model.sql.shuiwu.assets.AssetsAccountEntity;
import org.thingsboard.server.dao.msgLog.DeviceLogService;
import org.thingsboard.server.dao.project.ProjectRelationService;
import org.thingsboard.server.dao.project.ProjectService;
import org.thingsboard.server.dao.service.DataValidator;
import org.thingsboard.server.dao.service.PaginatedRemover;
import org.thingsboard.server.dao.shuiwu.assets.AssetsAccountService;
import org.thingsboard.server.dao.sql.device.DevicePartitionMapper;
import org.thingsboard.server.dao.tenant.TenantDao;
import org.thingsboard.server.dao.timeseries.TimeseriesService;
import org.thingsboard.server.dao.util.HttpClientFactory;
import org.thingsboard.server.dao.util.OracleUtil;

import javax.annotation.Nullable;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static org.thingsboard.server.common.data.CacheConstants.DEVICE_CACHE;
import static org.thingsboard.server.dao.DaoUtil.toUUIDs;
import static org.thingsboard.server.dao.model.ModelConstants.NULL_UUID;
import static org.thingsboard.server.dao.service.Validator.*;

@Service
@Slf4j
public class DeviceServiceImpl extends AbstractEntityService implements DeviceService {

    public static final String INCORRECT_TENANT_ID = "Incorrect tenantId ";
    public static final String INCORRECT_PAGE_LINK = "Incorrect page link ";
    public static final String INCORRECT_CUSTOMER_ID = "Incorrect customerId ";
    public static final String INCORRECT_DEVICE_ID = "Incorrect deviceId ";
    @Autowired
    private DeviceDao deviceDao;

    @Autowired
    private TenantDao tenantDao;

    @Autowired
    private CustomerDao customerDao;

    @Autowired
    private AlarmDao alarmDao;

    @Autowired
    private DeviceCredentialsService deviceCredentialsService;

    @Autowired
    private EntityViewService entityViewService;

    @Autowired
    private CacheManager cacheManager;

    @Autowired
    private AttributesDao attributesDao;

    @Autowired
    private ProjectRelationService projectRelationService;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private DeviceLogService deviceLogService;

    @Autowired
    private JobService jobService;

    @Autowired
    private AssetsAccountService assetsAccountService;
    @Autowired
    private OracleUtil oracleUtil;
    @Autowired
    private AlarmJsonDao alarmJsonDao;
    @Autowired
    private TimeseriesService timeseriesService;
    @Autowired
    private AlarmService alarmService;

    @Autowired
    private DevicePartitionMapper devicePartitionMapper;

    @Override
    public Device findDeviceById(TenantId tenantId, DeviceId deviceId) {
        log.trace("Executing findDeviceById [{}]", deviceId);
        validateId(deviceId, INCORRECT_DEVICE_ID + deviceId);
        return deviceDao.findById(tenantId, deviceId.getId());
    }

    @Override
    @Cacheable(cacheNames = DEVICE_CACHE, key = "{#deviceId}")
    public Device findDeviceById(DeviceId deviceId) {
        log.trace("Executing findDeviceById [{}]", deviceId);
        validateId(deviceId, INCORRECT_DEVICE_ID + deviceId);
        return deviceDao.findById(deviceId.getId());
    }

    @Override
    public ListenableFuture<Device> findDeviceByIdAsync(TenantId tenantId, DeviceId deviceId) {
        log.trace("Executing findDeviceById [{}]", deviceId);
        validateId(deviceId, INCORRECT_DEVICE_ID + deviceId);
        return deviceDao.findByIdAsync(tenantId, deviceId.getId());
    }

    @Override
    public ListenableFuture<Device> findDeviceByIdAsync(DeviceId deviceId) {
        return deviceDao.findDeviceByIdAsync(deviceId);
    }

    @Cacheable(cacheNames = DEVICE_CACHE, key = "{#tenantId, #name}")
    @Override
    public Device findDeviceByTenantIdAndName(TenantId tenantId, String name) {
        log.trace("Executing findDeviceByTenantIdAndName [{}][{}]", tenantId, name);
        validateId(tenantId, INCORRECT_TENANT_ID + tenantId);
        Optional<Device> deviceOpt = deviceDao.findDeviceByTenantIdAndName(tenantId.getId(), name);
        return deviceOpt.orElse(null);
    }

    @Caching(evict = {@CacheEvict(cacheNames = DEVICE_CACHE, allEntries = true)})
    @Override
    public Device saveDevice(Device device) {
        //device添加创建时间
        device.setCreatedTime(System.currentTimeMillis());
        log.trace("Executing saveDevice [{}]", device);
        deviceValidator.validate(device, Device::getTenantId);
        Device savedDevice = null;
        if (device.getId() == null) {
            savedDevice = deviceDao.save(device.getTenantId(), device);
            DeviceCredentials deviceCredentials = new DeviceCredentials();
            deviceCredentials.setDeviceId(new DeviceId(savedDevice.getUuidId()));
            deviceCredentials.setCredentialsType(DeviceCredentialsType.ACCESS_TOKEN);
            deviceCredentials.setCredentialsId(RandomStringUtils.randomAlphanumeric(20));
            deviceCredentialsService.createDeviceCredentials(device.getTenantId(), deviceCredentials);

            // 新增项目和设备关系
            projectRelationService.mountEntityToProject(DataConstants.ProjectRelationEntityType.DEVICE.name(),
                    device.getProjectId(), Collections.singletonList(UUIDConverter.fromTimeUUID(savedDevice.getUuidId())));

        } else {
            try {
                Device findDevice = deviceDao.findDeviceByIdAsync(device.getId()).get();
                List<ProjectEntity> projectEntities = projectRelationService.findProjectRelationByEntityTypeAndEntityId(
                        DataConstants.ProjectRelationEntityType.DEVICE.name(), UUIDConverter.fromTimeUUID(device.getUuidId()));
                if (projectEntities != null && !projectEntities.isEmpty()) {
                    device.setProjectId(projectEntities.get(0).getId());
                }
                savedDevice = updateDevice(device, findDevice);
            } catch (Exception e) {
                e.printStackTrace();
                log.error("查询设备的数据失败");
            }
        }

        // 同步到设备资产
        try {
            syncDeviceAccount(device, UUIDConverter.fromTimeUUID(savedDevice.getUuidId()));
        } catch (Exception e) {
            log.error("同步设备到设备资产错误, e = ", e);
        }

        return savedDevice;
    }

    /**
     * 同步设备到设备资产
     *
     * @param device
     * @param deviceId
     */
    private void syncDeviceAccount(Device device, String deviceId) {
        AssetsAccountEntity assetsAccountEntity = new AssetsAccountEntity();
        if (device.getId() != null) {
            assetsAccountEntity = assetsAccountService.findByDeviceId(UUIDConverter.fromTimeUUID(device.getUuidId()));
        } else {
            assetsAccountEntity.setDeviceId(deviceId);
        }

        // 更新名称
        if (!StringUtils.isEmpty(device.getName())) {
            assetsAccountEntity.setDeviceName(device.getName());
        }
        // 更新项目ID
        if (!StringUtils.isEmpty(device.getProjectId())) {
            assetsAccountEntity.setProjectId(device.getProjectId());
        }
        // 更新企业ID
        if (device.getTenantId() != null) {
            assetsAccountEntity.setTenantId(UUIDConverter.fromTimeUUID(device.getTenantId().getId()));
        }
        // 设备类型
        if (!StringUtils.isEmpty(device.getDeviceTypeName())) {
            assetsAccountEntity.setDeviceType(device.getDeviceTypeName());
        }
        // TODO 设置其父设备/子设备

        assetsAccountService.save(assetsAccountEntity);
    }

    @Override
    public List<Device> findAll() {
        return deviceDao.find().stream().filter(device -> device.getIsDelete().equalsIgnoreCase(DataConstants.IS_DELETE_NO)).collect(Collectors.toList());
    }

    private Device updateDevice(Device newDevice, Device oldDevice) {
        if (newDevice.getAdditionalInfo() != null) {
            oldDevice.setAdditionalInfo(newDevice.getAdditionalInfo());
        }
        if (newDevice.getType() != null) {
            oldDevice.setType(newDevice.getType());
        }
        if (newDevice.getIsDelete() != null) {
            oldDevice.setIsDelete(newDevice.getIsDelete());
        }
        if (newDevice.getTenantId() != null) {
            oldDevice.setTenantId(newDevice.getTenantId());
        }
        if (newDevice.getName() != null) {
            oldDevice.setName(newDevice.getName());
        }
        if (newDevice.getCustomerId() != null) {
            oldDevice.setCustomerId(newDevice.getCustomerId());
        }
        if (newDevice.getHardwareId() != null) {
            oldDevice.setHardwareId(newDevice.getHardwareId());
        }
        if (newDevice.getAepDeviceId() != null) {
            oldDevice.setAepDeviceId(newDevice.getAepDeviceId());
        }
        if (newDevice.getForeignKey() != null) {
            oldDevice.setForeignKey(newDevice.getForeignKey());
        }
        if (newDevice.getDeviceTypeName() != null) {
            oldDevice.setDeviceTypeName(newDevice.getDeviceTypeName());
        }
        if (newDevice.getLocation() != null) {
            oldDevice.setLocation(newDevice.getLocation());
        }
        oldDevice.setGateWayId(newDevice.getGateWayId());
        deviceDao.save(oldDevice.getTenantId(), oldDevice);
        // 修改项目和设备的关系
        projectRelationService.mountEntityToProject(DataConstants.ProjectRelationEntityType.DEVICE.name(),
                newDevice.getProjectId(), Collections.singletonList(UUIDConverter.fromTimeUUID(UUID.fromString(newDevice.getId().getId().toString()))));
        return oldDevice;
    }

    @Override
    public Device assignDeviceToCustomer(TenantId tenantId, DeviceId deviceId, CustomerId customerId) {
        Device device = findDeviceById(tenantId, deviceId);
        device.setCustomerId(customerId);
        return saveDevice(device);
    }

    @Override
    public Device unassignDeviceFromCustomer(TenantId tenantId, DeviceId deviceId) {
        Device device = findDeviceById(tenantId, deviceId);
        device.setCustomerId(null);
        return saveDevice(device);
    }

    @Override
    @Transactional
    @Caching(evict = {@CacheEvict(cacheNames = DEVICE_CACHE, allEntries = true)})
    public void deleteDevice(TenantId tenantId, DeviceId deviceId) {
        log.trace("Executing deleteDevice [{}]", deviceId);
        Cache cache = cacheManager.getCache(DEVICE_CACHE);
        validateId(deviceId, INCORRECT_DEVICE_ID + deviceId);
        DeviceCredentials deviceCredentials = deviceCredentialsService.findDeviceCredentialsByDeviceId(tenantId, deviceId);
        if (deviceCredentials != null) {
            deviceCredentialsService.deleteDeviceCredentials(tenantId, deviceCredentials);
        }
        deleteEntityRelations(tenantId, deviceId);
        Device device = deviceDao.findById(deviceId.getId());
        List<Object> list = new ArrayList<>();
        list.add(device.getTenantId());
        list.add(device.getName());
        cache.evict(list);
//        // 修改删除标记
//        device.setIsDelete(DataConstants.IS_DELETE_YES);
//        // 设置设备名字为ID
//        device.setName(device.getId().getId().toString());
        // 删除attr_kv
        try {
            attributesDao.deleteByEntityId(device.getId());
        } catch (Exception e) {
            e.printStackTrace();
        }
        // 删除设备上下线
        deviceLogService.deleteByDeviceId(UUIDConverter.fromTimeUUID(device.getUuidId()));
        // 删除设备
        deviceDao.removeById(device.getUuidId());

        // 删除Oracle关联
        try {
            oracleUtil.deleteDeviceOrVedioById(UUIDConverter.fromTimeUUID(device.getUuidId()));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public TextPageData<Device> findDevicesByTenantId(TenantId tenantId, TextPageLink pageLink) {
        log.trace("Executing findDevicesByTenantId, tenantId [{}], pageLink [{}]", tenantId, pageLink);
        validateId(tenantId, INCORRECT_TENANT_ID + tenantId);
        validatePageLink(pageLink, INCORRECT_PAGE_LINK + pageLink);
        //List<Device> devices = deviceDao.findDevicesByTenantId(tenantId.getId(), pageLink);
        List<Device> devices = deviceDao.findAllByTenantId(tenantId);
        devices = devices.stream()
                .filter(d -> d.getIsDelete().equals(DataConstants.IS_DELETE_NO)
                        && !d.getType().equalsIgnoreCase(DataConstants.GATEWAY_NAME)
                        && !d.getType().equalsIgnoreCase(DataConstants.PROTOCOL_TYPE_MODBUS)
                        && !d.getType().equalsIgnoreCase(DataConstants.DEVICE_TYPE_DTU))
                .collect(Collectors.toList());

        return new TextPageData<>(getDeviceStatus(devices, attributesDao.findAll(ModelConstants.LAST_UPDATE_TIME)), pageLink);
    }

    @Override
    public TextPageData<Device> findDevicesByTenantIdAndType(TenantId tenantId, String type, TextPageLink pageLink) {
        log.trace("Executing findDevicesByTenantIdAndType, tenantId [{}], type [{}], pageLink [{}]", tenantId, type, pageLink);
        validateId(tenantId, INCORRECT_TENANT_ID + tenantId);
        validateString(type, "Incorrect type " + type);
        validatePageLink(pageLink, INCORRECT_PAGE_LINK + pageLink);
        List<Device> devices = deviceDao.findDevicesByTenantIdAndType(tenantId.getId(), type, pageLink);
        devices = devices.stream()
                .filter(d -> d.getIsDelete().equals(DataConstants.IS_DELETE_NO))
                .collect(Collectors.toList());
        // 若不是查询网关设备，过滤掉网关设备再返回给前端
        if (!type.equalsIgnoreCase(DataConstants.GATEWAY_NAME)) {
            devices = devices.stream()
                    .filter(d -> !d.getType().equalsIgnoreCase(DataConstants.GATEWAY_NAME)
                            && !d.getType().equalsIgnoreCase(DataConstants.PROTOCOL_TYPE_MODBUS)
                            && !d.getType().equalsIgnoreCase(DataConstants.DEVICE_TYPE_DTU))
                    .collect(Collectors.toList());
        }
        return new TextPageData<>(devices, pageLink);
    }

    @Override
    public ListenableFuture<List<Device>> findDevicesByTenantIdAndIdsAsync(TenantId tenantId, List<DeviceId> deviceIds) {
        log.trace("Executing findDevicesByTenantIdAndIdsAsync, tenantId [{}], deviceIds [{}]", tenantId, deviceIds);
        validateId(tenantId, INCORRECT_TENANT_ID + tenantId);
        validateIds(deviceIds, "Incorrect deviceIds " + deviceIds);
        return deviceDao.findDevicesByTenantIdAndIdsAsync(tenantId.getId(), toUUIDs(deviceIds));
    }


    @Override
    public void deleteDevicesByTenantId(TenantId tenantId) {
        log.trace("Executing deleteDevicesByTenantId, tenantId [{}]", tenantId);
        validateId(tenantId, INCORRECT_TENANT_ID + tenantId);
        tenantDevicesRemover.removeEntities(tenantId, tenantId);
    }

    @Override
    public TextPageData<Device> findDevicesByTenantIdAndCustomerId(TenantId tenantId, CustomerId customerId, TextPageLink pageLink) {
        log.trace("Executing findDevicesByTenantIdAndCustomerId, tenantId [{}], customerId [{}], pageLink [{}]", tenantId, customerId, pageLink);
        validateId(tenantId, INCORRECT_TENANT_ID + tenantId);
        validateId(customerId, INCORRECT_CUSTOMER_ID + customerId);
        validatePageLink(pageLink, INCORRECT_PAGE_LINK + pageLink);
        List<Device> devices = deviceDao.findDevicesByTenantIdAndCustomerId(tenantId.getId(), customerId.getId(), pageLink);
        return new TextPageData<>(devices, pageLink);
    }

    @Override
    public TextPageData<Device> findDevicesByTenantIdAndCustomerIdAndType(TenantId tenantId, CustomerId customerId, String type, TextPageLink pageLink) {
        log.trace("Executing findDevicesByTenantIdAndCustomerIdAndType, tenantId [{}], customerId [{}], type [{}], pageLink [{}]", tenantId, customerId, type, pageLink);
        validateId(tenantId, INCORRECT_TENANT_ID + tenantId);
        validateId(customerId, INCORRECT_CUSTOMER_ID + customerId);
        validateString(type, "Incorrect type " + type);
        validatePageLink(pageLink, INCORRECT_PAGE_LINK + pageLink);
        List<Device> devices = deviceDao.findDevicesByTenantIdAndCustomerIdAndType(tenantId.getId(), customerId.getId(), type, pageLink);
        return new TextPageData<>(devices, pageLink);
    }

    @Override
    public ListenableFuture<List<Device>> findDevicesByTenantIdCustomerIdAndIdsAsync(TenantId tenantId, CustomerId customerId, List<DeviceId> deviceIds) {
        log.trace("Executing findDevicesByTenantIdCustomerIdAndIdsAsync, tenantId [{}], customerId [{}], deviceIds [{}]", tenantId, customerId, deviceIds);
        validateId(tenantId, INCORRECT_TENANT_ID + tenantId);
        validateId(customerId, INCORRECT_CUSTOMER_ID + customerId);
        validateIds(deviceIds, "Incorrect deviceIds " + deviceIds);
        return deviceDao.findDevicesByTenantIdCustomerIdAndIdsAsync(tenantId.getId(),
                customerId.getId(), toUUIDs(deviceIds));
    }

    @Override
    public void unassignCustomerDevices(TenantId tenantId, CustomerId customerId) {
        log.trace("Executing unassignCustomerDevices, tenantId [{}], customerId [{}]", tenantId, customerId);
        validateId(tenantId, INCORRECT_TENANT_ID + tenantId);
        validateId(customerId, INCORRECT_CUSTOMER_ID + customerId);
        customerDeviceUnasigner.removeEntities(tenantId, customerId);
    }

    @Override
    public ListenableFuture<List<Device>> findDevicesByQuery(TenantId tenantId, DeviceSearchQuery query) {
        ListenableFuture<List<EntityRelation>> relations = relationService.findByQuery(tenantId, query.toEntitySearchQuery());
        ListenableFuture<List<Device>> devices = Futures.transformAsync(relations, r -> {
            EntitySearchDirection direction = query.toEntitySearchQuery().getParameters().getDirection();
            List<ListenableFuture<Device>> futures = new ArrayList<>();
            for (EntityRelation relation : r) {
                EntityId entityId = direction == EntitySearchDirection.FROM ? relation.getTo() : relation.getFrom();
                if (entityId.getEntityType() == EntityType.DEVICE) {
                    futures.add(findDeviceByIdAsync(tenantId, new DeviceId(entityId.getId())));
                }
            }
            return Futures.successfulAsList(futures);
        });

        devices = Futures.transform(devices, new Function<List<Device>, List<Device>>() {
            @Nullable
            @Override
            public List<Device> apply(@Nullable List<Device> deviceList) {
                return deviceList == null ? Collections.emptyList() : deviceList.stream().filter(device -> query.getDeviceTypes().contains(device.getType())).collect(Collectors.toList());
            }
        });

        return devices;
    }

    @Override
    public List<Device> findAllByTenantId(TenantId tenantId, String key) {
        List<Device> devices = deviceDao.findAllByTenantId(tenantId, key)/*.stream()
                .filter(device -> !device.getType().equalsIgnoreCase(DataConstants.DEVICE_TYPE_PORT)
                        && !device.getType().equalsIgnoreCase(DataConstants.DEVICE_TYPE_DTU)
                        && !device.getType().equalsIgnoreCase(DataConstants.GATEWAY_NAME)
                        && device.getIsDelete().equalsIgnoreCase(DataConstants.IS_DELETE_NO)).collect(Collectors.toList())*/;
        return getDeviceStatus(devices, attributesDao.findAll(ModelConstants.LAST_UPDATE_TIME));
    }


    @Override
    public List<Device> findAllByTenantId(TenantId tenantId) {
        List<Device> devices = deviceDao.findAllByTenantId(tenantId).stream()
                .filter(device -> !device.getType().equalsIgnoreCase(DataConstants.DEVICE_TYPE_PORT)
                        && !device.getType().equalsIgnoreCase(DataConstants.GATEWAY_NAME)
                        && device.getIsDelete().equalsIgnoreCase(DataConstants.IS_DELETE_NO)).collect(Collectors.toList());
        return getDeviceStatus(devices, attributesDao.findAll(ModelConstants.LAST_UPDATE_TIME));
    }

    /**
     * 根据tenantId获取gateWay列表，兼容之前默认网关类型为gateWay，新的网关类型为PORT-串口型网关，DTU-DTU型网关
     * todo 前端需要一次性获取最后一次上线时间？
     *
     * @param tenantId
     * @return
     */
    @Override
    public List<Device> findGateWayByTenantId(TenantId tenantId) {
        List<Device> devices = deviceDao.findAllByTenantId(tenantId)
                .stream()
                .filter(device -> (DataConstants.DeviceTypeParam.contains(device.getType()))
                        && device.getIsDelete().equalsIgnoreCase(DataConstants.IS_DELETE_NO)
                        && device.getGateWayId() == null)
                .collect(Collectors.toList());
        return getDeviceStatus(devices, attributesDao.findAll(ModelConstants.LAST_UPDATE_TIME));
    }

    /**
     * 获取gateWay下面挂载的设备
     *
     * @param gateWayId
     * @return
     */
    @Override
    public List<Device> findDeviceByGateWayId(DeviceId gateWayId) {
        List<Device> devices = deviceDao.findByGateway(gateWayId).stream().filter(device -> device.getIsDelete().equalsIgnoreCase(DataConstants.IS_DELETE_NO)).collect(Collectors.toList());
        return getDeviceStatus(devices, attributesDao.findAll(ModelConstants.LAST_UPDATE_TIME));
    }

    @Override
    public ListenableFuture<List<EntitySubtype>> findDeviceTypesByTenantId(TenantId tenantId) {
        log.trace("Executing findDeviceTypesByTenantId, tenantId [{}]", tenantId);
        validateId(tenantId, INCORRECT_TENANT_ID + tenantId);
        ListenableFuture<List<EntitySubtype>> tenantDeviceTypes = deviceDao.findTenantDeviceTypesAsync(tenantId.getId());
        return Futures.transform(tenantDeviceTypes,
                deviceTypes -> {
                    deviceTypes.sort(Comparator.comparing(EntitySubtype::getType));
                    return deviceTypes;
                });
    }

    private DataValidator<Device> deviceValidator =
            new DataValidator<Device>() {

                @Override
                protected void validateCreate(TenantId tenantId, Device device) {
                }

                @Override
                protected void validateUpdate(TenantId tenantId, Device device) {
                }

                @Override
                protected void validateDataImpl(TenantId tenantId, Device device) {
                    if (StringUtils.isEmpty(device.getType())) {
                        throw new DataValidationException("Device type should be specified!");
                    }
                    if (StringUtils.isEmpty(device.getName())) {
                        throw new DataValidationException("Device name should be specified!");
                    }
                    if (device.getTenantId() == null) {
                        throw new DataValidationException("Device should be assigned to tenant!");
                    } else {
                        Tenant tenant = tenantDao.findById(device.getTenantId(), device.getTenantId().getId());
                        if (tenant == null) {
                            throw new DataValidationException("Device is referencing to non-existent tenant!");
                        }
                    }
                    if (device.getCustomerId() == null) {
                        device.setCustomerId(new CustomerId(NULL_UUID));
                    } else if (!device.getCustomerId().getId().equals(NULL_UUID)) {
                        Customer customer = customerDao.findById(device.getTenantId(), device.getCustomerId().getId());
                        if (customer == null) {
                            throw new DataValidationException("Can't assign device to non-existent customer!");
                        }
                        if (!customer.getTenantId().getId().equals(device.getTenantId().getId())) {
                            throw new DataValidationException("Can't assign device to customer from different tenant!");
                        }
                    }
                }
            };

    private PaginatedRemover<TenantId, Device> tenantDevicesRemover =
            new PaginatedRemover<TenantId, Device>() {

                @Override
                protected List<Device> findEntities(TenantId tenantId, TenantId id, TextPageLink pageLink) {
                    return deviceDao.findDevicesByTenantId(id.getId(), pageLink);
                }

                @Override
                protected void removeEntity(TenantId tenantId, Device entity) {
                    deleteDevice(tenantId, new DeviceId(entity.getUuidId()));
                }
            };

    private PaginatedRemover<CustomerId, Device> customerDeviceUnasigner = new PaginatedRemover<CustomerId, Device>() {

        @Override
        protected List<Device> findEntities(TenantId tenantId, CustomerId id, TextPageLink pageLink) {
            return deviceDao.findDevicesByTenantIdAndCustomerId(tenantId.getId(), id.getId(), pageLink);
        }

        @Override
        protected void removeEntity(TenantId tenantId, Device entity) {
            unassignDeviceFromCustomer(tenantId, new DeviceId(entity.getUuidId()));
        }
    };

    @Override
    public void updateDeviceOnline(DeviceId deviceId) {
        Device device = deviceDao.findById(deviceId.getId());
        if (device != null && device.getGateWayId() != null) {
            attributesDao.save(device.getGateWayId(), DataConstants.SERVER_SCOPE, new BaseAttributeKvEntry(new LongDataEntry(ModelConstants.LAST_UPDATE_TIME, System.currentTimeMillis()), System.currentTimeMillis()));
        }
        attributesDao.save(deviceId, DataConstants.SERVER_SCOPE, new BaseAttributeKvEntry(new LongDataEntry(ModelConstants.LAST_UPDATE_TIME, System.currentTimeMillis()), System.currentTimeMillis()));
    }

    @Override
    public Device findDeviceByAdditionalInfo(String gatewayId) {
        StringBuilder builder = new StringBuilder();
        builder.append("{").append("\"description\":\"").append(gatewayId).append("\"}");
        List<Device> deviceList = deviceDao.findDeviceByAdditionalInfo(builder.toString());

        if (deviceList.size() == 1) {
            return deviceList.get(0);
        }

        return null;
    }

    /**
     * 查询指定项目的设备列表
     * 不包含网关设备
     *
     * @param projectId 项目ID
     * @param tenantId
     * @return List 设备列表
     */
    @Override
    public List<Device> findByProjectId(String projectId, TenantId tenantId) {
        List<ProjectEntity> list = projectService.findAllChild(tenantId, projectId);
        List<String> projectIdList = list.stream().map(ProjectEntity::getId).collect(Collectors.toList());
        projectIdList.add(projectId);
        List<Device> devices = deviceDao.findDeviceByProjectId(projectIdList);
        return getDeviceStatus(devices, attributesDao.findAll(ModelConstants.LAST_UPDATE_TIME));
    }

    @Override
    public List<Device> findByTemplateId(String templateId) {
        return deviceDao.findByTemplateId(templateId);
    }

    @Override
    public List<Device> findGateWayByProjectId(String projectId) {
        List<Device> devices = deviceDao.findGateWayByProjectId(projectId);
        return getDeviceStatus(devices, attributesDao.findAll(ModelConstants.LAST_UPDATE_TIME));
    }

    @Override
    public List<Device> findAllGateway() {
        // 查询所有未被删除的主机
        List<Device> devices = deviceDao.find()
                .stream()
                .filter(device -> (DataConstants.DeviceTypeParam.contains(device.getType()))
                        && device.getIsDelete().equalsIgnoreCase(DataConstants.IS_DELETE_NO)).collect(Collectors.toList());

        return getDeviceStatus(devices, attributesDao.findAll(ModelConstants.LAST_UPDATE_TIME));
    }

    @Override
    public List<Device> findAllGatewayByTenantId(String tenantId) {
        // 查询所有未被删除的主机

        return deviceDao.findByTenantId(new TenantId(UUIDConverter.fromString(tenantId)))
                .stream()
                .filter(device -> (DataConstants.DeviceTypeParam.contains(device.getType()))
                        && device.getIsDelete().equalsIgnoreCase(DataConstants.IS_DELETE_NO)).collect(Collectors.toList());
    }

    @Override
    public List<Device> findAllGatewayByProjectId(String projectId) {
        // 查询所有未被删除的主机
        List<Device> devices = deviceDao.findDevicesByProject(projectId)
                .stream()
                .filter(device -> (DataConstants.DeviceTypeParam.contains(device.getType()))
                        && device.getIsDelete().equalsIgnoreCase(DataConstants.IS_DELETE_NO)).collect(Collectors.toList());

        return devices;
    }

    @Override
    public long findAllDevice() {
        List<Device> devices = deviceDao.find()
                .stream()
                .filter(device -> (!DataConstants.DeviceTypeParam.contains(device.getType()))
                        && device.getIsDelete().equalsIgnoreCase(DataConstants.IS_DELETE_NO)).collect(Collectors.toList());
        return devices.size();
    }

    @Override
    public long findAllDeviceByProjectId(String projectId) {
        List<Device> devices = deviceDao.findDevicesByProject(projectId)
                .stream()
                .filter(device -> (!DataConstants.DeviceTypeParam.contains(device.getType()))
                        && device.getIsDelete().equalsIgnoreCase(DataConstants.IS_DELETE_NO)).collect(Collectors.toList());
        return devices.size();
    }


    @Override
    public long findAllDeviceByTenantId(TenantId tenantId) {
        List<Device> devices = deviceDao.findByTenantId(tenantId)
                .stream()
                .filter(device -> (!DataConstants.DeviceTypeParam.contains(device.getType()))
                        && device.getIsDelete().equalsIgnoreCase(DataConstants.IS_DELETE_NO)).collect(Collectors.toList());
        return devices.size();
    }

    @Override
    public long findAllModbusDevice() {
        return deviceDao.findAllModbusDevice();
    }

    @Override
    public long findAllModbusDeviceByTenantId(TenantId tenantId) {
        return deviceDao.findAllModbusDeviceByTenantId(tenantId);
    }

    @Override
    public long findAllModbusDeviceByProjectId(String projectId) {
        return deviceDao.findAllModbusDeviceByProjectId(projectId);
    }

    @Override
    public long findAllDtuDevice() {
        return deviceDao.findAllDtuDevice();
    }

    @Override
    public long findAllDtuDeviceByTenantId(String tenantId) {
        return deviceDao.findAllDtuDeviceByTenantId(tenantId);
    }

    @Override
    public long findAllDtuDeviceByProjectId(String projectId) {
        return deviceDao.findAllDtuDeviceByProjectId(projectId);
    }

    @Override
    public long findAllOnlineDevice() {
        List<Device> devices = deviceDao.find()
                .stream()
                .filter(device -> (!DataConstants.DeviceTypeParam.contains(device.getType()))
                        && device.getIsDelete().equalsIgnoreCase(DataConstants.IS_DELETE_NO)).collect(Collectors.toList());
        List<Device> result = getDeviceStatus(devices, attributesDao.findAll(ModelConstants.LAST_UPDATE_TIME)).stream().filter(Device::isStatus).collect(Collectors.toList());
        return result.size();
    }

    @Override
    public long findAllOnlineGateway() {
        List<Device> devices = deviceDao.find()
                .stream()
                .filter(device -> (DataConstants.DeviceTypeParam.contains(device.getType()))
                        && device.getIsDelete().equalsIgnoreCase(DataConstants.IS_DELETE_NO)).collect(Collectors.toList());
        List<Device> result = getDeviceStatus(devices, attributesDao.findAll(ModelConstants.LAST_UPDATE_TIME)).stream().filter(Device::isStatus).collect(Collectors.toList());
        return result.size();
    }

    @Override
    public long findAllOfflineGateway() {
        List<Device> devices = deviceDao.find()
                .stream()
                .filter(device -> (DataConstants.DeviceTypeParam.contains(device.getType()))
                        && device.getIsDelete().equalsIgnoreCase(DataConstants.IS_DELETE_NO)).collect(Collectors.toList());
        List<Device> result = getDeviceStatus(devices, attributesDao.findAll(ModelConstants.LAST_UPDATE_TIME)).stream().filter(Device::isStatus).collect(Collectors.toList());
        return devices.size() - result.size();
    }

    @Override
    public long findAllOnlineDeviceByTenantId(TenantId tenantId) {
        List<Device> devices = deviceDao.findByTenantId(tenantId)
                .stream()
                .filter(device -> (!DataConstants.DeviceTypeParam.contains(device.getType()))
                        && device.getIsDelete().equalsIgnoreCase(DataConstants.IS_DELETE_NO)).collect(Collectors.toList());
        List<Device> result = getDeviceStatus(devices, attributesDao.findAll(ModelConstants.LAST_UPDATE_TIME)).stream().filter(Device::isStatus).collect(Collectors.toList());
        return result.size();
    }

    @Override
    public long findAllOfflineGatewayByTenantId(TenantId tenantId) {
        List<Device> devices = deviceDao.findByTenantId(tenantId)
                .stream()
                .filter(device -> (DataConstants.DeviceTypeParam.contains(device.getType()))
                        && device.getIsDelete().equalsIgnoreCase(DataConstants.IS_DELETE_NO)).collect(Collectors.toList());
        List<Device> result = getDeviceStatus(devices, attributesDao.findAll(ModelConstants.LAST_UPDATE_TIME)).stream().filter(Device::isStatus).collect(Collectors.toList());
        return devices.size() - result.size();
    }

    @Override
    public long findAllOnlineGatewayByTenantId(TenantId tenantId) {
        List<Device> devices = deviceDao.findByTenantId(tenantId)
                .stream()
                .filter(device -> (DataConstants.DeviceTypeParam.contains(device.getType()))
                        && device.getIsDelete().equalsIgnoreCase(DataConstants.IS_DELETE_NO)).collect(Collectors.toList());
        List<Device> result = getDeviceStatus(devices, attributesDao.findAll(ModelConstants.LAST_UPDATE_TIME)).stream().filter(Device::isStatus).collect(Collectors.toList());
        return result.size();
    }

    @Override
    public long findAllGatewayByTenantId(TenantId tenantId) {
        List<Device> devices = deviceDao.findByTenantId(tenantId)
                .stream()
                .filter(device -> (DataConstants.DeviceTypeParam.contains(device.getType()))
                        && device.getIsDelete().equalsIgnoreCase(DataConstants.IS_DELETE_NO)).collect(Collectors.toList());
        return devices.size();
    }

    @Override
    public long findAllOnlineDeviceByProjectId(String projectId) {
        List<Device> devices = deviceDao.findDeviceByProjectId(projectId)
                .stream()
                .filter(device -> (!DataConstants.DeviceTypeParam.contains(device.getType()))
                        && device.getIsDelete().equalsIgnoreCase(DataConstants.IS_DELETE_NO)).collect(Collectors.toList());
        List<Device> result = getDeviceStatus(devices, attributesDao.findAll(ModelConstants.LAST_UPDATE_TIME)).stream().filter(Device::isStatus).collect(Collectors.toList());
        return result.size();
    }

    @Override
    public long findAllOfflineGatewayByProject(String projectId) {
        List<Device> devices = deviceDao.findDevicesByProject(projectId)
                .stream()
                .filter(device -> (DataConstants.DeviceTypeParam.contains(device.getType()))
                        && device.getIsDelete().equalsIgnoreCase(DataConstants.IS_DELETE_NO)).collect(Collectors.toList());
        List<Device> result = getDeviceStatus(devices, attributesDao.findAll(ModelConstants.LAST_UPDATE_TIME)).stream().filter(Device::isStatus).collect(Collectors.toList());
        return devices.size() - result.size();
    }

    @Override
    public long findAllOnlineGatewayByProject(String projectId) {
        List<Device> devices = deviceDao.findDevicesByProject(projectId)
                .stream()
                .filter(device -> (DataConstants.DeviceTypeParam.contains(device.getType()))
                        && device.getIsDelete().equalsIgnoreCase(DataConstants.IS_DELETE_NO)).collect(Collectors.toList());
        List<Device> result = getDeviceStatus(devices, attributesDao.findAll(ModelConstants.LAST_UPDATE_TIME)).stream().filter(Device::isStatus).collect(Collectors.toList());
        return result.size();
    }

    @Override
    public long findAllOffLineDevice() {
        List<Device> devices = deviceDao.find()
                .stream()
                .filter(device -> (!DataConstants.DeviceTypeParam.contains(device.getType()))
                        && device.getIsDelete().equalsIgnoreCase(DataConstants.IS_DELETE_NO)).collect(Collectors.toList());
        List<Device> result = getDeviceStatus(devices, attributesDao.findAll(ModelConstants.LAST_UPDATE_TIME)).stream().filter(Device::isStatus).collect(Collectors.toList());
        return devices.size() - result.size();
    }

    @Override
    public long findAllOffLineDeviceByTenantId(TenantId tenantId) {
        List<Device> devices = deviceDao.findByTenantId(tenantId)
                .stream()
                .filter(device -> (!DataConstants.DeviceTypeParam.contains(device.getType()))
                        && device.getIsDelete().equalsIgnoreCase(DataConstants.IS_DELETE_NO)).collect(Collectors.toList());
        List<Device> result = getDeviceStatus(devices, attributesDao.findAll(ModelConstants.LAST_UPDATE_TIME)).stream().filter(Device::isStatus).collect(Collectors.toList());
        return devices.size() - result.size();
    }

    @Override
    public long findAllOffLineDeviceByProjectId(String projectId) {
        List<Device> devices = deviceDao.findDevicesByProject(projectId)
                .stream()
                .filter(device -> (!DataConstants.DeviceTypeParam.contains(device.getType()))
                        && device.getIsDelete().equalsIgnoreCase(DataConstants.IS_DELETE_NO)).collect(Collectors.toList());
        List<Device> result = getDeviceStatus(devices, attributesDao.findAll(ModelConstants.LAST_UPDATE_TIME)).stream().filter(Device::isStatus).collect(Collectors.toList());
        return devices.size() - result.size();
    }

    @Override
    public List<Device> findAllByProjectId(String projectId, String key) {
        List<Device> devices = deviceDao.findAllByProjectId(projectId, key).stream()
                .filter(device -> /*!device.getType().equalsIgnoreCase(DataConstants.DEVICE_TYPE_PORT)
                        && */!device.getType().equalsIgnoreCase(DataConstants.DEVICE_TYPE_DTU)
                        && !device.getType().equalsIgnoreCase(DataConstants.GATEWAY_NAME)
                        && device.getIsDelete().equalsIgnoreCase(DataConstants.IS_DELETE_NO)).collect(Collectors.toList());
        return getDeviceStatus(devices, attributesDao.findAll(ModelConstants.LAST_UPDATE_TIME));
    }

    @Override
    @Transactional
    @Caching(evict = {@CacheEvict(cacheNames = DEVICE_CACHE, allEntries = true)})
    public void deleteGateway(String gatewayId) {
        Device gateway = deviceDao.findById(UUIDConverter.fromString(gatewayId));
        if (gateway != null) {
            // 删除主机
            boolean b = deviceDao.removeById(gateway.getUuidId());
            // 删除主机密钥
            deviceCredentialsService.deleteByDeviceId(new DeviceId(UUIDConverter.fromString(gatewayId)));
            // 删除attr_kv
            attributesDao.deleteByEntityId(gateway.getId());
            // 删除设备上下线日志
            deviceLogService.deleteByDeviceId(UUIDConverter.fromTimeUUID(gateway.getUuidId()));
            //删除设备报警数据
            alarmDao.deleteAlarmByDevice(gateway.getId());

            jobService.deleteOffline(gateway);
            // 删除主机挂载的从机
            List<Device> deviceList = deviceDao.findByGateway(new DeviceId(UUIDConverter.fromString(gatewayId)));
            // 遍历删除相关数据
            deviceList.forEach(device -> {
                // 删除设备密钥
                deviceCredentialsService.deleteByDeviceId(device.getId());
                // 删除设备attr_kv
                attributesDao.deleteByEntityId(device.getId());
                // 删除设备上下线日志
                deviceLogService.deleteByDeviceId(UUIDConverter.fromTimeUUID(device.getUuidId()));
                //删除设备报警数据
                alarmDao.deleteAlarmByDevice(device.getId());
                deviceDao.removeById(device.getUuidId());
                jobService.deleteOffline(device);
                // 删除Oracle同步
                oracleUtil.deleteDeviceOrVedioById(UUIDConverter.fromTimeUUID(device.getUuidId()));
            });
        }

    }

    @Override
    public List<Device> findByProjectIdAndType(String projectId, String type) {
        List<Device> devices = deviceDao.findDeviceByProjectIdAndType(projectId, type);
        return getDeviceStatus(devices, attributesDao.findAll(ModelConstants.LAST_UPDATE_TIME));
    }

    @Override
    public List<Device> findAllByGateway(String gatewayId, String key) {
        List<Device> devices = deviceDao.findAllByGatewayId(gatewayId, key).stream()
                .filter(device -> device.getIsDelete().equalsIgnoreCase(DataConstants.IS_DELETE_NO)).collect(Collectors.toList());
        // 合并结果
        Map<DeviceId, Device> deviceMap = new HashMap<>();
        devices.forEach(device -> {
            if (deviceMap.containsKey(device.getId())) {
                Device d = deviceMap.get(device.getId());
                if (device.getAttrKey().equals("info")) {
                    d.setAttribute(device.getInfo());
                }
                if (device.getAttrKey().equals("prop")) {
                    d.setProp(device.getInfo());
                }
                deviceMap.put(d.getId(), d);
            } else {
                deviceMap.put(device.getId(), device);
            }
        });
        devices = new ArrayList<>(deviceMap.values());

        return getDeviceStatus(devices, attributesDao.findAll(ModelConstants.LAST_UPDATE_TIME));
    }

    @Override
    public List<Device> findByTenantId(TenantId tenantId) {
        return deviceDao.findByTenantId(tenantId);
    }

    @Override
    public List<Device> getMqttDeviceAndDevices(TenantId tenantId) {
        return deviceDao.findByTenantId(tenantId).stream().filter(device -> {
            JSONObject obj = JSONObject.parseObject(device.getAdditionalInfo().asText());
            if (!DataConstants.IS_DELETE_NO.equals(device.getIsDelete())) {
                return false;
            }
            if ("MQTT".equals(device.getType()) && obj != null && obj.getBoolean("gateway") != null ? obj.getBoolean("gateway") : false) {
                return true;
            }
            if (!"MQTT".equals(device.getType()) && (obj == null || !(obj.getBoolean("gateway") != null ? obj.getBoolean("gateway") : false))) {
                return true;
            }
            return false;
        }).collect(Collectors.toList());
    }

    @Override
    public void deleteByProjectId(String projectId) {
        List<Device> devices = deviceDao.findDevicesByProject(projectId);
        devices.forEach(device -> {
            deviceCredentialsService.deleteByDeviceId(device.getId());
            // 删除设备attr_kv
            attributesDao.deleteByEntityId(device.getId());
            // 删除设备上下线日志
            deviceLogService.deleteByDeviceId(UUIDConverter.fromTimeUUID(device.getUuidId()));
            //删除设备报警数据
            alarmDao.deleteAlarmByDevice(device.getId());
            deviceDao.removeById(device.getUuidId());
            jobService.deleteOffline(device);
        });
    }

    @Override
    public List<Device> findByTemplateId(String id, String attribute) {
        return deviceDao.findByTemplateId(id, attribute);
    }

    @Override
    public List<Device> findCloudDeviceList() {
        return deviceDao.findAll();
    }

    @Override
    public List<Device> getGatewayByTenantId(TenantId id) {
        List<String> param = Arrays.asList("MQTT", "NBMQTT", "DTU", "NBDTU", "MODBUS");
        return deviceDao.findByType(param, id)
                .stream()
                .filter(device -> DataConstants.IS_DELETE_NO.equals(device.getIsDelete()))
                .collect(Collectors.toList());
    }

    /**
     * 从设备列表中筛选出从机并设置对应的项目名称
     *
     * @param deviceList
     * @param tenantId
     * @return
     */
    @Override
    public List<Device> getCloudDeviceList(List<Device> deviceList, TenantId tenantId) {
        // 筛选出从机
        Map<String, Device> gatewayDeviceMap = new HashMap<>();
        deviceList = deviceList.stream().filter(device -> {
            if (device.getGateWayId() != null) {
                return true;
            }
            gatewayDeviceMap.put(device.getUuidId().toString(), device);
            return false;
        }).collect(Collectors.toList());

        // 查询项目列表
        List<ProjectEntity> projectList = projectService.findByTenantId(tenantId);
        List<ProjectRelationEntity> relations = projectRelationService
                .findByProjectIdInAndEntityType(projectList.stream().map(ProjectEntity::getId).collect(Collectors.toList()), DataConstants.ProjectRelationEntityType.DEVICE.name());
        List<Device> gatewayList = this.findGateWayByTenantId(tenantId);

        Map<String, Device> gatewayMap = new HashMap<>();
        gatewayList.forEach(gateway -> gatewayMap.put(gateway.getId().getId().toString(), gateway));
        Map<String, ProjectEntity> projectMap = new HashMap<>();
        projectList.forEach(project -> projectMap.put(project.getId(), project));
        Map<String, String> deviceProjectRelationMap = new HashMap<>();
        relations.forEach(relation -> deviceProjectRelationMap.put(relation.getEntityId(), relation.getProjectId()));

        for (Device device : deviceList) {
            String deviceId = UUIDConverter.fromTimeUUID(device.getId().getId());
            String projectId = deviceProjectRelationMap.get(deviceId);
            if (projectId != null) {
                ProjectEntity project = projectMap.get(projectId);
                if (project != null) {
                    device.setProjectId(projectId);
                    device.setProjectName(project.getName());
                    Device gateway = gatewayDeviceMap.get(device.getGateWayId().getId().toString());
                    if (gateway != null) {
                        JSONObject obj = JSON.parseObject(gateway.getAdditionalInfo().asText());
                        device.setVoltage(obj.getString("voltage"));
                    }
                }
            }

            Device gateway = gatewayMap.get(device.getGateWayId().getId().toString());
            if (gateway != null) {
                device.setGatewayName(gateway.getName());
            }

        }

        return getDeviceStatus(deviceList, attributesDao.findAll(ModelConstants.LAST_UPDATE_TIME));
    }

    @Override
    public List<Device> findByIdIn(List<String> deviceIdList, String tenantId) {
        return deviceDao.findByIdIn(tenantId, deviceIdList);
    }

    @Override
    public List<Device> findByProjectIdAndTypeAndName(String projectId, String type, String name) {
        List<Device> devices = deviceDao.findDeviceByProjectIdAndTypeAndName(projectId, type, name);
        return getDeviceStatus(devices, attributesDao.findAll(ModelConstants.LAST_UPDATE_TIME));
    }

    @Override
    public List<Device> findByForeignKey(String foreignKey) {
        return deviceDao.findByForeignKey(foreignKey);
    }

    @Override
    public List<Device> findByForeignKeyIn(List<String> deviceCodeList) {
        List<Device> devices = deviceDao.findByForeignKeyIn(deviceCodeList);
        return getDeviceStatus(devices, attributesDao.findAll(ModelConstants.LAST_UPDATE_TIME));
    }

    @Override
    public List<Device> findAll(TenantId tenantId) {
        return getDeviceStatus(deviceDao.findAllByTenantId(tenantId), attributesDao.findAll(ModelConstants.LAST_UPDATE_TIME));
    }

    @Override
    public List<DeviceFullData> getDeviceFullData(TenantId tenantId, DeviceId deviceId, String group) {
        List<DeviceFullData> result = new ArrayList<>();
        try {
            AttributeKvEntry attributeKvEntry = attributesDao.find(deviceId, DataConstants.SHARED_SCOPE, DataConstants.ATTRIBUTE_PROP).get();
            if (attributeKvEntry != null) {
                List<PropAttribute> props = new ObjectMapper().readValue(attributeKvEntry.getValueAsString(), new TypeReference<List<PropAttribute>>() {
                });
                if (!StringUtils.isEmpty(group)) {
                    props = props.stream().filter(prop -> group.equals(prop.getGroup())).collect(Collectors.toList());
                }

                //最后一次数据
                List<TsKvEntry> tsKvEntries = timeseriesService.findAllLatest(tenantId, deviceId).get();
                Map<String, TsKvEntry> tsKvEntryMap = new HashMap<>();
                tsKvEntries.forEach(tsKvEntry -> {
                    tsKvEntryMap.put(tsKvEntry.getKey(), tsKvEntry);
                });
                props.forEach(propAttribute -> {
//                    if (propAttribute.getPropertyType().equals("1")) {
                    DeviceFullData deviceFullData = new DeviceFullData();
                    deviceFullData.setProperty(propAttribute.getPropertyCategory());
                    deviceFullData.setPropertyName(propAttribute.getName());
                    deviceFullData.setUnit(propAttribute.getUnit());
                    deviceFullData.setPropertyType(propAttribute.getPropertyType());
                    deviceFullData.setEntityType("String");
                    deviceFullData.setSerialNumber(propAttribute.getSerialNumber());
                    deviceFullData.setPointAddress(propAttribute.getPointAddress());
                    deviceFullData.setGroup(propAttribute.getGroup());
                    deviceFullData.setControlOptions(propAttribute.getControlOptions());
                    //获取最后一次数据
                    if (tsKvEntryMap.containsKey(propAttribute.getPropertyCategory())) {
                        deviceFullData.setValue(tsKvEntryMap.get(propAttribute.getPropertyCategory()).getValueAsString());
                        deviceFullData.setCollectionTime(tsKvEntryMap.get(propAttribute.getPropertyCategory()).getTs());
                        if (System.currentTimeMillis() - tsKvEntryMap.get(propAttribute.getPropertyCategory()).getTs() > (1000 * 60 * 120)) {
                            deviceFullData.setStatus(false);
                        } else {
                            deviceFullData.setStatus(true);
                        }
                    }

                    // 数据展示处理
                    if (deviceFullData.getValue() != null) {
                        try {
                            BigDecimal data = new BigDecimal(deviceFullData.getValue());
                            List<DisplayObj> displayList = propAttribute.getDisplay();
                            if (displayList != null) {
                                for (DisplayObj display : displayList) {
                                    BigDecimal displayData = new BigDecimal(display.getValue());
                                    if (displayData.compareTo(data) == 0) {
                                        deviceFullData.setValue(display.getLabel());
                                        break;
                                    }
                                }
                            }

                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }

                    result.add(deviceFullData);
//                    }
                });
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    /**
     * 下发控制指令
     */
    @Override
    public Object deviceControl(DeviceControl control, User currentUser) {
        JSONObject result = new JSONObject();
        // 校验参数
        if (StringUtils.isEmpty(control.getDeviceId())) {
            result.put("code", 500);
            result.put("message", "请选择要下发的设备!");
            return result;
        }
        if (StringUtils.isEmpty(control.getValue())) {
            result.put("code", 500);
            result.put("message", "请输入要下发的值!");
            return result;
        }
        if (StringUtils.isEmpty(control.getProperty())) {
            result.put("code", 500);
            result.put("message", "请选择要下发的变量!");
            return result;
        }
        /*
         * 1. 查询设备、设备密钥以及变量列表
         * 2. 判断设备类型并选择采用的下发方式（目前支持：MODBUS DTU透传）
         * 3. 查询要下发的设备的地址
         * 4. 查询要下发的变量的寄存器地址以及下发的位数
         * 5. 构造下发的信息
         * 6. 远程调用接口执行控制指令
         * 7. 返回执行结果（注：透传仅能感知是否发送到DTU并不能感知DTU是否将指令正确发送给了设备）
         *
         * 从第三步开始不同的设备，采用的代码会不同，根据具体设备下发要求来
         */
        try {
            // 查询设备以及设备密钥
            Device device = this.findDeviceById(new DeviceId(UUIDConverter.fromString(control.getDeviceId())));
            if (device == null) {
                result.put("code", 500);
                result.put("message", "该设备不存在或已被删除!");
                return result;
            }
            DeviceCredentials deviceCredentials = deviceCredentialsService.findDeviceCredentialsByDeviceId(device.getTenantId(), device.getId());
            if (deviceCredentials == null) {
                result.put("code", 500);
                result.put("message", "该设备不存在或已被删除!");
                return result;
            }
            control.setToken(deviceCredentials.getCredentialsId());

            String type = device.getType();
            if (!type.equals("MQTT")) {// DTU MODBUS透传方式
                result = dtuModbusControlProcess(control, device);
            } else {
                result.put("code", 200);
                result.put("message", "暂不支持该设备类型的控制下发!");
            }

            return result;
        } catch (Exception e) {
            e.printStackTrace();
            result.put("code", 500);
            result.put("message", "下发控制指令失败, 未知异常!");
            return result;
        }
    }

    @Override
    public List<Device> findByDeviceTypeName(String deviceTypeName, TenantId tenantId) {
        List<Device> deviceList = deviceDao.findByDeviceTypeName(deviceTypeName, tenantId);
        return getDeviceStatus(deviceList, attributesDao.findAll(ModelConstants.LAST_UPDATE_TIME));
    }

    @Override
    public Device getDeviceInfo(DeviceId id) {
        // 查询设备信息
        Device device = deviceDao.findById(id.getId());
        // 查询所属项目
        List<ProjectEntity> relations = projectRelationService
                .findProjectRelationByEntityTypeAndEntityId(DataConstants.ProjectRelationEntityType.DEVICE.name(), UUIDConverter.fromTimeUUID(id.getId()));

        if (relations != null && relations.size() > 0) {
            ProjectEntity projectEntity = relations.get(0);
            device.setProjectName(projectEntity.getName());
        }

        return device;
    }

    @Override
    public PageData<DevicePartitionDTO> getPartitionDevice(PartitionMountRequest request) {
        if (request.getName() == null) {
            request.setName("");
        }
        request.setName("%" + request.getName() + "%");
        List<DevicePartitionDTO> deviceList = devicePartitionMapper.getPartitionDevice("%%", request.getName(), request.getTenantId(), request.getType(), request.getPage() - 1, request.getSize(), request.getPartitionId());
        int size = devicePartitionMapper.getPartitionDeviceCount("%%", request.getName(), request.getTenantId(), request.getType(), request.getPartitionId());

        return new PageData<>(size, deviceList);
    }

    @Override
    public PageData<DevicePartitionDTO> getWaterFactoryDevice(PartitionMountRequest request) {

        if (request.getName() == null) {
            request.setName("");
        }
        request.setName("%" + request.getName() + "%");
        List<DevicePartitionDTO> deviceList = devicePartitionMapper.getWaterFactoryDevice("%流量%", request.getName(), request.getTenantId(), request.getPage() - 1, request.getSize());
        int size = devicePartitionMapper.getWaterFactoryDeviceCount("%流量%", request.getName(), request.getTenantId());

        return new PageData<>(size, deviceList);

    }

    @Value("${control.dtu-modbus.url}")
    private String CONTROL_DTU_MODBUS_URL;

    /**
     * DTU设备 MODBUS透传控制下发
     * <p>
     * 1. 查询要下发的变量
     * 2. 构造要下发的信息
     * 3. 远程调用接口执行下发
     * 4. 返回调用结果
     */
    private JSONObject dtuModbusControlProcess(DeviceControl control, Device device) throws Exception {
        JSONObject result = new JSONObject();

        // 查询父设备
        Device gatewayDevice = this.findDeviceById(device.getGateWayId());
        if (gatewayDevice == null) {
            result.put("code", 500);
            result.put("message", "该设备不存在或已被删除!");
            return result;
        }

        // 查询父设备密钥
        DeviceCredentials gatewayCredentials = deviceCredentialsService.findDeviceCredentialsByDeviceId(gatewayDevice.getTenantId(), gatewayDevice.getId());
        if (gatewayCredentials == null) {
            result.put("code", 500);
            result.put("message", "该设备不存在或已被删除!");
            return result;
        }
        String token = gatewayCredentials.getCredentialsId();

        // 查询设备基本信息
        AttributeKvEntry infoKvEntry = attributesDao.find(device.getId(), DataConstants.SHARED_SCOPE, DataConstants.ATTRIBUTE_INFO).get();
        DeviceAttribute info = new ObjectMapper().readValue(infoKvEntry.getValueAsString(), new TypeReference<DeviceAttribute>() {
        });
        if (info == null) {
            result.put("code", 500);
            result.put("message", "要下发的设备配置不完整!");
            return result;
        }

        // 查询设备变量列表
        AttributeKvEntry propKvEntry = attributesDao.find(device.getId(), DataConstants.SHARED_SCOPE, DataConstants.ATTRIBUTE_PROP).get();
        List<PropAttribute> props = new ObjectMapper().readValue(propKvEntry.getValueAsString(), new TypeReference<List<PropAttribute>>() {
        });
        // 遍历找出要发送的变量
        PropAttribute controlThis = null;
        for (PropAttribute p : props) {
            if (control.getProperty().equals(p.getPropertyCategory())) {
                controlThis = p;
                break;
            }
        }
        if (controlThis == null) {
            result.put("code", 500);
            result.put("message", "要下发的变量不存在或已被删除!");
            return result;
        }

        // 获取要发送的modbus设备地址
        String unitId = info.getUnitId();
        // 获取要发送的变量寄存器地址
        String registerAddress = controlThis.getRegisterAddress();
        // 获取要发送的变量位数
        String byteCount = controlThis.getByteCount();

        CloseableHttpClient httpClient = HttpClientFactory.getHttpClient();
        HttpPost httpPost = new HttpPost(CONTROL_DTU_MODBUS_URL);

        JSONObject param = new JSONObject();
        param.put("token", token);
        param.put("unitId", Integer.valueOf(unitId));
        param.put("functionCode", "05");// 单个寄存器
        param.put("address", registerAddress);
        param.put("data", Integer.valueOf(control.getValue()));

        StringEntity paramEntity = new StringEntity(param.toString());
        httpPost.setEntity(paramEntity);
        httpPost.setHeader("Content-Type", "application/json");
        CloseableHttpResponse execute = httpClient.execute(httpPost);
        int statusCode = execute.getStatusLine().getStatusCode();
        if (statusCode != 200) {
            result.put("code", 500);
            result.put("message", "调用远程下发控制接口异常!");
            return result;
        }
        String resultString = EntityUtils.toString(execute.getEntity());

        result.put("code", 200);
        result.put("message", "下发成功!");
        return result;
    }


    /**
     * 获取设备的在线状态
     *
     * @return
     */
    public List<Device> getDeviceStatus(List<Device> devices, List<AttributeKeyKvEntry> attrs) {
        try {
            //获取网关的最后一次连接时间
            Map<String, Long> map = new HashMap<>();
            attrs.forEach(at -> {
                if (at.getValueAsString() != null)
                    map.put(at.getAttributeBaseKey().getEntityId(), Long.valueOf(at.getValueAsString()));
            });
            for (Device d : devices) {
                if (map.containsKey(UUIDConverter.fromTimeUUID(d.getId().getId()))) {
                    d.setStatus(System.currentTimeMillis() - map.get(UUIDConverter.fromTimeUUID(d.getId().getId())) <= DateUtils.DAY_TIME);
                } else {
                    d.setStatus(false);
                }
                d.setNodeId(UUIDConverter.fromTimeUUID(d.getUuidId()));
            }
            return devices;
        } catch (Exception e) {
            e.printStackTrace();
            return devices;
        }
    }
}
