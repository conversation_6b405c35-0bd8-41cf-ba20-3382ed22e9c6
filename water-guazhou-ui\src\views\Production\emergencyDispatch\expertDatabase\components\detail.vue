<!-- 专家库-详情 -->
<template>
  <div :class="{ isDark: useAppStore().isDark }">
    <SLCard
      style="
        height: calc(100% - 80px);
        padding: 20px;
        margin: auto;
        margin-top: 20px;
        width: 100%;
        overflow: auto;
      "
    >
      <el-descriptions title="基础信息">
        <el-descriptions-item label="姓名">
          {{ config?.name || '' }}
        </el-descriptions-item>
        <el-descriptions-item label="性别">
          <el-tag size="small"> {{ config.gender }} </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="联系电话">
          {{ config.phone }}
        </el-descriptions-item>
        <el-descriptions-item label="联系邮箱">
          {{ config.email }}
        </el-descriptions-item>
        <el-descriptions-item label="最高学历">
          {{ config.educationLevel }}
        </el-descriptions-item>
      </el-descriptions>
      <el-divider content-position="left"> 行业领域 </el-divider>
      <el-descriptions title="">
        <el-descriptions-item label="职称">
          {{ config.professionalTitle }}
        </el-descriptions-item>
        <el-descriptions-item label="职称评定时间">
          {{ config.professionalTitleTime }}
        </el-descriptions-item>
        <el-descriptions-item label="行业领域">
          {{ config.industrySector }}
        </el-descriptions-item>
        <el-descriptions-item label="行业从事时间">
          {{ config.industryTime }}
        </el-descriptions-item>
        <el-descriptions-item label="工作单位">
          {{ config.deptName }}
        </el-descriptions-item>
        <el-descriptions-item label="单位电话">
          {{ config.deptPhone }}
        </el-descriptions-item>
      </el-descriptions>
      <el-divider content-position="left"> 工作经历 </el-divider>
      <div
        class="view w-e-text-container"
        v-html="config.jobHistory"
        style="margin: 0px 40px"
      ></div>
      <el-divider content-position="left"> 所获荣誉 </el-divider>
      <div
        class="view w-e-text-container"
        v-html="config.honorHistory"
        style="margin: 0px 40px"
      ></div>
      <el-divider content-position="left"> 学术成就 </el-divider>
      <div
        class="view w-e-text-container"
        v-html="config.academicHistory"
        style="margin: 0px 40px"
      ></div>
    </SLCard>
  </div>
</template>

<script lang="ts" setup>
import { useAppStore } from '@/store';
import { expertList } from '@/api/server/index';

const props = defineProps<{ config: any }>();

const refreshData = () => {
  expertList().then((res) => {
    console.log(res);
  });
};

onMounted(() => {
  refreshData();
});
</script>

<style lang="scss" scoped>
.content {
  margin: auto;
  width: 100%;
}

.view {
  min-height: 100px;
}
</style>
