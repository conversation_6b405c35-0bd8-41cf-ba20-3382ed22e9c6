<template>
  <div class="light-plat">
    <div class="info">
      <div class="sync-ratio">
        <span
          v-if="data.delta !== undefined"
          class="sync-ratio__label"
        >同比</span>
        <div
          v-if="data.delta !== undefined"
          class="sync-ratio__info"
          :class="data.type"
        >
          <Icon
            :icon="
              data.type === 'up'
                ? 'material-symbols:arrow-drop-up'
                : 'material-symbols:arrow-drop-down'
            "
          ></Icon>
          <span>{{ data.delta }}%</span>
        </div>
      </div>
      <div class="value">
        <span>{{ data.value }}</span><span class="unit">{{ data.unit || '%' }}</span>
      </div>
    </div>
    <div class="footer">
      <span class="title">
        {{ data.title }}
      </span>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { Icon } from '@iconify/vue'

defineProps<{
  data: ILightPlatData
}>()
</script>
<style lang="scss" scoped>
.light-plat {
  width: 100%;
  height: 114px;
  .info {
    width: 100%;
    height: 92px;
    padding: 14px 24px;
    background: url(../../imgs/light_plat.png) 0 0 / 100% 100% no-repeat;
    .sync-ratio {
      font-size: 12px;
      height: 22px;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      word-break: keep-all;
      line-height: 20px;
      .sync-ratio__label {
        color: #b8d2ff;
      }
      .sync-ratio__info {
        display: flex;
        align-items: center;
        justify-content: space-around;
        border-radius: 20px;
        &.up {
          background: linear-gradient(270deg, #ed5571 0%, #ed8355 100%);
        }
        &.down {
          background: linear-gradient(270deg, #43b530 0%, #b8cc41 100%);
        }
      }
    }
    .value {
      vertical-align: bottom;
      font-style: normal;
      font-weight: 400;
      font-size: 24px;
      line-height: 30px;
      text-align: center;
      word-break: keep-all;
      display: flex;
      align-items: baseline;
      justify-content: center;
      .unit {
        font-size: 12px;
        word-break: keep-all;
      }
    }
  }
  .footer .title {
    display: grid;
    place-items: center;
    font-family: 'PingFang SC';
    font-style: normal;
    font-weight: 600;
    font-size: 14px;
    line-height: 20px;
    /* identical to box height */

    color: #b8d2ff;
  }
}
</style>
