package org.thingsboard.server.dao.sql.smartService.portal;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.thingsboard.server.dao.model.sql.smartService.portal.SsPortalNews;
import org.thingsboard.server.dao.util.imodel.query.smartService.portal.SsPortalActiveRequest;
import org.thingsboard.server.dao.util.imodel.query.smartService.portal.SsPortalNewsPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartService.portal.SsPortalNewsSaveRequest;

@Mapper
public interface SsPortalNewsMapper extends BaseMapper<SsPortalNews> {
    IPage<SsPortalNews> findByPage(SsPortalNewsPageRequest request);

    @SuppressWarnings("methodNotInXmlInspection")
    boolean update(SsPortalNews entity);

    boolean updateFully(SsPortalNews entity);

    boolean active(SsPortalActiveRequest request);

    boolean setHot(SsPortalActiveRequest request);

    boolean setRecommend(SsPortalActiveRequest request);

    boolean canSave(SsPortalNewsSaveRequest request);

}
