import{d as N,c as p,r as d,l as n,bH as v,bI as L,bJ as w,s as T,bE as k,o as O,g as I,n as P,q as l,i as s,F,b6 as q}from"./index-r0dFAfgr.js";import{_ as M}from"./CardTable-rdWOL4_6.js";import{_ as z}from"./CardSearch-CB_HNR-Q.js";import B from"./OrderStepTags-CClNfq4j.js";import G from"./detail-CU6-qhMl.js";import{E as D,b as R,g as H,c as U,d as V,e as $}from"./config-DqqM5K5L.js";import"./index-0NlGN6gS.js";import{_ as A}from"./NewOrder.vue_vue_type_script_setup_true_lang-DAnrmOVe.js";import{f as W}from"./DateFormatter-Bm9a68Ax.js";import{G as J}from"./lossControl-DNefZk8I.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";/* empty css                         */import"./index-CpGhZCTT.js";import"./detailSteps-BqRp_Y4m.js";/* empty css                */import"./index-CCFuhOrs.js";/* empty css                             */import"./DPlayer-Be2AurQX.js";import"./DPlayer.min-_HMH7IVX.js";import"./OutsideWorkOrder-C6s8joBt.js";import"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import"./FormMap-BGaXSqQF.js";import"./ArcView-DpMnCY82.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./widget-BcWKanF2.js";import"./pe-B8dP0-Ut.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./TileLayer-B5vQ99gG.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./commonProperties-DqNQ4F00.js";import"./project-DUuzYgGl.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./IdentifyResult-4DxLVhTm.js";import"./ViewHelper-BGCZjxXH.js";import"./fieldconfig-Bk3o1wi7.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./LayerHelper-Cn-iiqxI.js";import"./pipe-nogVzCHG.js";/* empty css                                                                      */import"./URLHelper-B9aplt5w.js";import"./ArcPipe.vue_vue_type_script_setup_true_lang-ClsVvV2P.js";import"./utils-D5nxoMq3.js";import"./usePartition-DkcY9fQ2.js";import"./dma-SMxrzG7b.js";import"./hookupDevice-Bcbk7s68.js";import"./useUser-Blb5V02j.js";const Y={class:"wrapper"},Rt=N({__name:"index",setup(j){const u=p(),c=p(),S=p(),b=p(),y=d({title:"流程明细",cancel:!1,className:"lightColor",group:[]}),h=p(""),f=(e,t,o)=>o.hidden=e.type!==o.field,x=d({filters:[{field:"status",label:"工单状态",type:"select",options:[{label:"全部",value:""},{label:"待处理",value:D.待接单},{label:"处理中",value:"PIPE_PROCESSING"},{label:"已处理",value:"PIPE_COMPLETE"},{label:"已终止",value:D.已终止}],clearable:!1},{type:"select",label:"选择方式",field:"type",options:[{label:"按年",value:"year"},{label:"按月",value:"month"},{label:"按时间段",value:"day"}],clearable:!1},{field:"day",label:"发起时间",type:"daterange",handleHidden:f,clearable:!1},{field:"month",label:"发起时间",type:"month",handleHidden:f,clearable:!1},{field:"year",label:"发起时间",type:"year",handleHidden:f,clearable:!1},{field:"organizerName",label:"发起人",type:"input",clearable:!1},{field:"partitionName",label:"分区名称",type:"input",clearable:!1},{field:"processUserName",label:"接收人",type:"input",clearable:!1},{field:"receiveDepartmentName",label:"部门名称",type:"input",clearable:!1},{field:"source",label:"来源",type:"select",options:R()},{field:"level",label:"紧急程度",type:"select",options:H()}],operations:[{type:"btn-group",btns:[{perm:!0,type:"primary",text:"查询",iconifyIcon:"ep:search",click:()=>a()},{perm:!0,type:"default",text:"重置",iconifyIcon:"ep:refresh",click:()=>E()},{perm:!0,type:"success",text:"新增工单",iconifyIcon:"ep:download",click:()=>{var e;(e=u.value)==null||e.openDialog()}}]}],handleSearch:()=>a(),defaultParams:{status:"",type:"month",day:[n().subtract(1,"M").format(v),n().format(v)],month:n().format(L),year:n().format(w)}}),r=d({expandable:!0,expandComponent:T(B),columns:[{minWidth:160,label:"标题",prop:"title"},{minWidth:160,label:"分区名称",prop:"partitionName"},{minWidth:160,label:"事件描述",prop:"remark"},{minWidth:160,label:"添加人",prop:"organizerName"},{minWidth:160,label:"接收人",prop:"processUserName"},{minWidth:160,label:"部门",prop:"receiveDepartmentName"},{minWidth:160,label:"处理级别",prop:"processLevelLabel"},{minWidth:160,label:"紧急程度",prop:"level",iconifyIcon:"ep:clock",formatter:(e,t)=>U(t),cellStyle:e=>({color:V(e.level)})},{minWidth:160,label:"添加日期",prop:"createTime",formatter(e,t){return W(t,k)}},{minWidth:160,label:"预计完成日期",prop:"estimatedFinishTime",formatter(e,t){return W(t,k)}},{minWidth:160,label:"处理进度",prop:"status",formatter(e,t){return $[t]||t}}],defaultExpandAll:!0,dataList:[],pagination:{refreshData:({page:e,size:t})=>{r.pagination.page=e,r.pagination.limit=t,a()}},operations:[{perm:!0,text:"详情",isTextBtn:!0,click:e=>C(e)}]}),C=e=>{var t;h.value=e.id||"",y.title=e.serialNo,(t=b.value)==null||t.openDrawer()},a=async()=>{var e,t,o,m;r.loading=!0;try{const i=((e=c.value)==null?void 0:e.queryParams)||{},g={page:r.pagination.page,size:r.pagination.limit||20,...i,start:(t=i.day)==null?void 0:t[0],end:(o=i.day)==null?void 0:o[1]};delete g.day;const _=(m=(await J(g)).data)==null?void 0:m.data;r.dataList=_.data,r.pagination.total=_.total}catch{}r.loading=!1},E=()=>{var e;(e=c.value)==null||e.resetForm(),a()};return O(()=>{a()}),(e,t)=>{const o=z,m=M,i=q;return I(),P("div",Y,[l(o,{ref_key:"refSearch",ref:c,config:s(x)},null,8,["config"]),l(m,{ref_key:"refTable",ref:S,class:"card-table",config:s(r)},null,8,["config"]),l(A,{ref_key:"refDialog",ref:u,"need-partition":!0,"default-values":{isDirectDispatch:!0},onSuccess:a},null,512),l(i,{ref_key:"refdetail",ref:b,config:s(y)},{default:F(()=>[l(G,{id:s(h)},null,8,["id"])]),_:1},8,["config"])])}}});export{Rt as default};
