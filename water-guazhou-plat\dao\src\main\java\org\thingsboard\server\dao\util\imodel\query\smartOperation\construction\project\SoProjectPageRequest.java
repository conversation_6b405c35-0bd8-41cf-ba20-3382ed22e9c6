package org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoProject;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;

import java.util.Date;

@Getter
@Setter
public class SoProjectPageRequest extends AdvancedPageableQueryEntity<SoProject, SoProjectPageRequest> {
    // 编号
    private String code;

    // 名称
    private String name;

    // 类别id
    private String typeId;

    // 启动时间开始
    private String startTimeFrom;

    // 启动时间截止
    private String startTimeTo;

    public Date getStartTimeFrom() {
        return toDate(startTimeFrom);
    }

    public Date getStartTimeTo() {
        return toDate(startTimeTo);
    }
}
