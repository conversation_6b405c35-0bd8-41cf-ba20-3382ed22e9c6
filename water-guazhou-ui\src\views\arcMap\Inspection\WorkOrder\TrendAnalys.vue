<template>
  <div class="wrapper">
    <SLCard
      class="card"
      title=" "
      overlay
    >
      <template #title>
        <Search
          ref="refSearch"
          :config="SearchConfig"
        ></Search>
      </template>
      <div class="top">
        <FormTable :config="TableConfig"></FormTable>
      </div>
      <div class="bottom">
        <div class="chart-box">
          <VChart
            ref="refChart"
            :option="state.lineOption"
          ></VChart>
        </div>
      </div>
    </SLCard>
  </div>
</template>
<script lang="ts" setup>
import { ref, reactive, shallowRef, onMounted, onBeforeUnmount } from 'vue'
import { Search as SearchIcon } from '@element-plus/icons-vue'
import moment from 'moment'
import { ISearchIns } from '@/components/type'
import { IECharts } from '@/plugins/echart'
import { formatterDate } from '@/utils/GlobalHelper'
import {
  EventTrendTableColumns,
  LineOption
} from '@/views/workorder/statistics/echart'
import { WorkOrderTrend } from '@/api/patrol'

const refSearch = ref<ISearchIns>()
const refChart = ref<IECharts>()
const SearchConfig = reactive<ISearch>({
  filters: [{ type: 'year', label: '时间范围', field: 'date' }],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          svgIcon: shallowRef(SearchIcon),
          click: () => refreshData()
        }
      ]
    }
  ],
  defaultParams: {
    date: moment().format(formatterDate)
  }
})
const TableConfig = reactive<ITable>({
  indexVisible: true,
  columns: EventTrendTableColumns,
  dataList: [],
  pagination: {
    hide: true,
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page
      TableConfig.pagination.limit = size
      refreshData()
    }
  }
})
const state = reactive<{
  lineOption: any
}>({
  lineOption: null
})
const refreshData = () => {
  const { date } = refSearch.value?.queryParams || {}
  const [fromTime, toTime] = date
    ? [
      moment(date, 'YYYY').startOf('y').valueOf(),
      moment(date, 'YYYY').endOf('y').valueOf()
    ]
    : [moment().startOf('y').valueOf(), moment().valueOf()]
  refChart.value?.clear()
  WorkOrderTrend({
    timeUnit: 'MONTH',
    fromTime,
    toTime
    // statisticType: true
  })
    .then(res => {
      const data = res.data?.data || []
      TableConfig.dataList = data.map(item => {
        const obj: any = {
          eventType: item.key
        }
        TableConfig.columns
          .filter(item => item.prop !== 'eventType')
          .map((column, i) => {
            obj[column.prop] = item.data?.[i]?.value
          })
        return obj
      })
      state.lineOption = LineOption(TableConfig.dataList)
      // TableConfig.dataList = res.data?.data?.types?.map(item => {
      //   const row: any = {
      //     eventType: item.key,
      //     total: item.total
      //   }
      //   item.data.map(o => {
      //     const key = moment(o.from, formatterDate).format('MM')
      //     row[key] = o.value
      //   })
      //   return row
      // }) || []
      // TableConfig.pagination.total = res.data?.data?.types?.length || 0
      // state.lineOption = LineOption(TableConfig.dataList)
    })
    .catch(() => {
      TableConfig.dataList = []
      state.lineOption = LineOption([])
    })
}
const resizeChart = () => {
  refChart.value?.resize()
}
onMounted(() => {
  refreshData()
  window.addEventListener('resize', resizeChart)
})
onBeforeUnmount(() => {
  window.removeEventListener('resize', resizeChart)
})
</script>
<style lang="scss" scoped>
.card {
  height: 100%;
}

.top {
  height: 50%;
}

.bottom {
  height: 50%;
  padding: 8px;
  .chart-box {
    height: 100%;
    width: 100%;
  }
}
</style>
