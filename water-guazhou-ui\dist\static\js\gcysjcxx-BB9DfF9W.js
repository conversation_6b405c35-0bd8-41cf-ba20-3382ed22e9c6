import{d as r,r as s,a8 as l,g as i,h as n,F as d,q as p,i as f,bz as m,C as _}from"./index-r0dFAfgr.js";import{d as u}from"./index-CCFuhOrs.js";/* empty css                             */import"./DPlayer-Be2AurQX.js";import"./DPlayer.min-_HMH7IVX.js";const x=r({__name:"gcysjcxx",props:{config:{}},setup(e){const t=e,a=s({defaultValue:l(()=>t.config),border:!0,direction:"horizontal",column:2,title:"工程预算基础信息",fields:[{type:"text",label:"预算人:",field:"budgeter"},{type:"text",label:"预算金额:",field:"cost"},{type:"text",label:"预算备注:",field:"remark"},{type:"text",label:"创建人:",field:"creatorName"},{type:"text",label:"创建时间:",field:"createTimeName"},{type:"text",label:"最后更新人:",field:"updateUserName"},{type:"text",label:"最后更新时间:",field:"updateTimeName"}]});return(b,g)=>{const o=u,c=m;return i(),n(c,{shadow:"hover",class:"card"},{default:d(()=>[p(o,{config:f(a)},null,8,["config"])]),_:1})}}}),v=_(x,[["__scopeId","data-v-1b929f7c"]]);export{v as default};
