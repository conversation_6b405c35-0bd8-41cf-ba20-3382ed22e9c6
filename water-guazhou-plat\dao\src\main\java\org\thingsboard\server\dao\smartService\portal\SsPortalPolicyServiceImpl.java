package org.thingsboard.server.dao.smartService.portal;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.smartService.portal.SsPortalPolicy;
import org.thingsboard.server.dao.sql.smartService.portal.SsPortalPolicyMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.smartService.portal.SsPortalPolicyPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartService.portal.SsPortalPolicySaveRequest;

@Service
public class SsPortalPolicyServiceImpl implements SsPortalPolicyService {
    @Autowired
    private SsPortalPolicyMapper mapper;

    @Override
    public SsPortalPolicy findById(String id) {
        return mapper.selectById(id);
    }

    @Override
    public IPage<SsPortalPolicy> findAllConditional(SsPortalPolicyPageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    public SsPortalPolicy save(SsPortalPolicySaveRequest entity) {
        return QueryUtil.saveOrUpdateOneByRequest(entity, mapper::insert, mapper::updateFully);
    }

    @Override
    public boolean update(SsPortalPolicy entity) {
        return mapper.update(entity);
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }

    @Override
    public boolean canBeDelete(String id) {
        return mapper.canBeDelete(id);
    }

}
