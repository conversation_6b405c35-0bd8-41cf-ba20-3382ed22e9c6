package org.thingsboard.server.dao.smartService.system;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.dao.model.sql.smartService.system.SystemUrgency;
import org.thingsboard.server.dao.sql.smartService.system.SystemUrgencyMapper;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.Date;
import java.util.List;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-10-27
 */
@Slf4j
@Service
@Transactional
public class SystemUrgencyServiceImpl implements SystemUrgencyService {
    @Autowired
    private SystemUrgencyMapper systemUrgencyMapper;


    @Override
    public SystemUrgency save(SystemUrgency systemUrgency) {
        systemUrgency.setUpdateTime(new Date());
        if (StringUtils.isBlank(systemUrgency.getId())) {
            systemUrgency.setIsDel("0");
            systemUrgency.setCreateTime(new Date());
            systemUrgencyMapper.insert(systemUrgency);
        } else {
            systemUrgencyMapper.updateById(systemUrgency);
        }

        return systemUrgency;
    }

    @Override
    @Transactional
    public IstarResponse delete(List<String> ids) {
        // 软删除
        QueryWrapper<SystemUrgency> updateWrapper = new QueryWrapper<>();
        updateWrapper.in("id", ids);
        SystemUrgency systemUrgency = new SystemUrgency();
        systemUrgency.setIsDel("1");
        systemUrgencyMapper.update(systemUrgency, updateWrapper);
        return IstarResponse.ok("删除成功");
    }

    @Override
    public List getList(String isDel, String tenantId) {
        QueryWrapper<SystemUrgency> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("tenant_id", tenantId);
        if (StringUtils.isNotBlank(isDel)) {
            queryWrapper.eq("is_del", isDel);
        }
        queryWrapper.orderByDesc("create_time");
        List<SystemUrgency> systemUrgencies = systemUrgencyMapper.selectList(queryWrapper);

        return systemUrgencies;
    }

    @Override
    public IstarResponse deleteHard(List<String> ids) {
        int i = systemUrgencyMapper.deleteBatchIds(ids);
        if (i > 0) {
            return IstarResponse.ok("删除成功");
        }
        return IstarResponse.error("删除失败");
    }
}
