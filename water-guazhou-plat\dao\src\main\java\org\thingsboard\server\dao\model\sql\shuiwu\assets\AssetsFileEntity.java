package org.thingsboard.server.dao.model.sql.shuiwu.assets;

import lombok.Data;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;

/**
 * 设备台账
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2021-10-19
 */
@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.ASSETS_FILE_TABLE)
public class AssetsFileEntity {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.ASSETS_FILE_NAME)
    private String name;

    @Column(name = ModelConstants.ASSETS_FILE_CREATE_TIME)
    private Long createTime;

    @Column(name = ModelConstants.ASSETS_FILE_PROJECT_ID)
    private String projectId;

    @Column(name = ModelConstants.ASSETS_FILE_TENANT_ID)
    private String tenantId;

    private transient String url;

}
