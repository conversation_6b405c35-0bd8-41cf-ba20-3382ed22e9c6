package org.thingsboard.server.dao.model.sql.dma;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;
import java.util.Date;

/**
 * DMA分析
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-04-24
 */
@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.DMA_WARN_LIST_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class DmaWarnListEntity {
    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.DMA_WARN_LIST_PARTITION_ID)
    private String partitionId;

    @Column(name = ModelConstants.DMA_WARN_LIST_TYPE)
    private String type;

    @Column(name = ModelConstants.DMA_WARN_LIST_DETAIL)
    private String detail;

    @Column(name = ModelConstants.DMA_WARN_LIST_WARN_VALUE)
    private Float warnValue;

    @Column(name = ModelConstants.DMA_WARN_LIST_THRESHOLD_VALUE)
    private Float thresholdValue;

    @Column(name = ModelConstants.DMA_WARN_LIST_STATUS)
    private String status;

    @Column(name = ModelConstants.DMA_WARN_LIST_OP_TIME)
    private Date opTime;

    @Column(name = ModelConstants.DMA_WARN_LIST_OPERATOR)
    private String operator;

    @Column(name = ModelConstants.DMA_WARN_LIST_REMARK)
    private String remark;

    @Column(name = ModelConstants.DMA_WARN_LIST_CREATE_TIME)
    private Date createTime;

    private transient String partitionName;

    private transient String operatorName;

}
