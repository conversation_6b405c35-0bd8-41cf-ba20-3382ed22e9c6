package org.thingsboard.server.dao.construction;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionDesignAmend;
import org.thingsboard.server.dao.sql.smartOperation.construction.SoConstructionDesignAmendMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionDesignAmendPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionDesignAmendSaveRequest;

@Service
public class SoConstructionDesignAmendServiceImpl implements SoConstructionDesignAmendService {
    @Autowired
    private SoConstructionDesignAmendMapper mapper;

    @Override
    public IPage<SoConstructionDesignAmend> findAllConditional(SoConstructionDesignAmendPageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    public SoConstructionDesignAmend save(SoConstructionDesignAmendSaveRequest entity) {
        return QueryUtil.saveOrUpdateOneByRequest(entity, mapper::insert, mapper::updateFully);
    }

    @Override
    public boolean update(SoConstructionDesignAmend entity) {
        return mapper.update(entity);
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }
}
