package org.thingsboard.server.common.data.VO;

import lombok.Data;

/**
 * 关联户表信息
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-04-26
 */
@Data
public class CustMeterVO {
    /**
     * 区域
     */
    private Object partitionName;

    /**
     * 表册
     */
    private Object bookName;

    /**
     * 客户编号
     */
    private Object custCode;

    /**
     * 客户名称
     */
    private Object custName;

    /**
     * 水表编号
     */
    private Object meterCode;

    /**
     * 小区名称
     */
    private Object organizationName;

    /**
     * 安装位置
     */
    private Object installPosition;

    /**
     * 水表口径
     */
    private Object caliber;

    /**
     * 水表厂家
     */
    private Object meterBrand;

    /**
     * 水表类型
     */
    private Object meterType;

    /**
     * 用水性质
     */
    private Object useNature;

}
