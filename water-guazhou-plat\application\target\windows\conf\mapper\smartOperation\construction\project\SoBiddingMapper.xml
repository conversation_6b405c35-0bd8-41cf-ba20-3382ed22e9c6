<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartOperation.construction.project.SoBiddingMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->
        bidding.id,
        bidding.project_code,
        bidding.proxy_bidding_company,
        bidding.prefer_company_id,
        bidding.attachments,
        bidding.creator,
        bidding.create_time,
        bidding.update_user,
        bidding.update_time,
        bidding.tenant_id,
        project.name                                                  as project_name,
        project.type_id                                               as project_type_id,
        (select name from so_general_type where id = project.type_id) as project_type_name,
        project.estimate                                              as project_estimate,
        project.start_time                                            as project_start_time,
        project.expect_end_time                                       as project_expect_end_time,
        project.principal                                             as project_principal

        <!--@sql from so_bidding bidding, so_project project -->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.smartOperation.project.SoBidding">
        <result column="id" property="id"/>
        <result column="project_code" property="projectCode"/>
        <result column="project_name" property="projectName"/>
        <result column="project_type_id" property="projectTypeId"/>
        <result column="project_type_name" property="projectTypeName"/>
        <result column="project_estimate" property="projectEstimate"/>
        <result column="project_start_time" property="projectStartTime"/>
        <result column="project_expect_end_time" property="projectExpectEndTime"/>
        <result column="project_principal" property="projectPrincipal"/>
        <result column="proxy_bidding_company" property="proxyBiddingCompany"/>
        <result column="prefer_company_id" property="preferCompanyId"/>
        <result column="attachments" property="attachments"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from so_bidding bidding
                 left join so_project project
                           on project.code = bidding.project_code and project.tenant_id = bidding.tenant_id
        <where>
            bidding.project_code = project.code
              and bidding.tenant_id = project.tenant_id
            <if test="projectCode != null and projectCode != ''">
                and project_code like '%' || #{projectCode} || '%'
            </if>
            <if test="projectName != null and projectName != ''">
                and project.name like '%' || #{projectName} || '%'
            </if>
            <if test="projectTypeId != null and projectTypeId != ''">
                and project.type_id = #{projectTypeId}
            </if>
            <if test="startTimeFrom != null">
                and project.start_time >= #{startTimeFrom}
            </if>
            <if test="startTimeTo != null">
                <!--@formatter:off-->
                and (select so_project.start_time &lt;= #{startTimeTo} from so_project
                    where project_code = #{projectCode} and bidding.tenant_id = #{tenantId})
                <!--@formatter:on-->
            </if>
            and bidding.tenant_id = #{tenantId}
        </where>
        order by project.create_time desc
    </select>

    <update id="update">
        update so_bidding
        <set>
            <if test="proxyBiddingCompany != null and proxyBiddingCompany != ''">
                proxy_bidding_company = #{proxyBiddingCompany},
            </if>
            <if test="preferCompanyId != null and preferCompanyId != ''">
                prefer_company_id = #{preferCompanyId},
            </if>
            <if test="attachments != null and attachments != ''">
                attachments = #{attachments},
            </if>
        </set>
        where id = #{id}
    </update>

    <update id="updateFully">
        update so_bidding
        set proxy_bidding_company = #{proxyBiddingCompany},
            prefer_company_id     = #{preferCompanyId},
            attachments           = #{attachments}
        where id = #{id}
    </update>

    <insert id="save">
        INSERT INTO so_bidding(id,
                               project_code,
                               proxy_bidding_company,
                               prefer_company_id,
                               attachments,
                               creator,
                               create_time,
                               update_user,
                               update_time,
                               tenant_id)
        VALUES (#{id},
                #{projectCode},
                #{proxyBiddingCompany},
                #{preferCompanyId},
                #{attachments},
                #{creator},
                #{createTime},
                #{updateUser},
                #{updateTime},
                #{tenantId})
    </insert>

    <select id="getIdByProjectCodeAndTenantId" resultType="java.lang.String">
        select id
        from so_bidding
        where project_code = #{projectCode}
          and tenant_id = #{tenantId}
    </select>
</mapper>