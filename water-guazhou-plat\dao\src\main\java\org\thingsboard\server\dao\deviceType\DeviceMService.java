package org.thingsboard.server.dao.deviceType;

import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.deviceManage.Device;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.List;

public interface DeviceMService {
    PageData getList(String typeId, String serialId, String name, String model, int page, int size, String tenantId);

    Device save(Device device);

    IstarResponse delete(List<String> ids);

    boolean checkSerialId(Device device, String tenantId);
}
