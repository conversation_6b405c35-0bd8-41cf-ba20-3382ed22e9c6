import{_ as H}from"./index-C9hz-UZb.js";import{d as U,a6 as X,r as w,bF as l,c as f,am as $,bB as G,s as M,b as J,o as Q,ah as Z,bA as ee,ay as te,g as _,n as ae,q as c,i as n,F as m,cs as B,p,h as k,j as S,bo as I,bR as q,dF as oe,dA as re,bU as ne,bW as ie,al as se,aj as le,bD as ce,C as pe}from"./index-r0dFAfgr.js";import{_ as de}from"./CardTable-rdWOL4_6.js";import{_ as me}from"./CardSearch-CB_HNR-Q.js";import{a as he}from"./statisticalAnalysis-D5JxC4wJ.js";import{u as ue}from"./useStation-DJgnSZIA.js";import{p as fe}from"./printUtils-C-AxhDcd.js";import{r as ye}from"./data-D3PIONJl.js";import"./Search-NSrhrIa_.js";import"./zhandian-YaGuQZe6.js";function ge(){return{title:{text:"",textStyle:{color:"#5470C6",fontSize:"14px"},top:10},grid:{left:90,right:90,top:70,bottom:80},legend:{top:20,type:"scroll",width:"500",textStyle:{fontSize:12}},tooltip:{trigger:"axis",axisPointer:{type:"shadow"},formatter:function(b){let a=b[0].name+"<br/>";return b.forEach(u=>{a+=u.marker+u.seriesName+": "+u.value+"<br/>"}),a}},xAxis:{type:"category",data:[],axisLabel:{rotate:30,interval:0,fontSize:12,margin:10},axisTick:{alignWithLabel:!0}},yAxis:[{position:"left",type:"value",name:"数值",axisLine:{show:!0,lineStyle:{types:"solid"}},axisLabel:{show:!0},splitLine:{lineStyle:{type:[5,10],dashOffset:5}}}],series:[]}}const _e={class:"wrapper"},be={class:"pie-charts"},Ce={class:"pie-chart-item"},ve={class:"pie-chart-item"},xe={class:"pie-chart-item"},we={class:"right-content"},ke={class:"right-content"},Se={class:"main-chart"},Ye=U({__name:"index",setup(R){const{getStationTree:b}=ue();X();const a=w({type:"date",treeDataType:"Station",stationId:"",sumsRow:{},title:"",activeName:"list",chartOption:null,pieChart1Option:null,pieChart2Option:null,pieChart3Option:null,dataList:{},chartKey:0});l().date();const u=f(),Y=f(),D=f(),O=f(),z=f(),A=f();$(()=>a.activeName,()=>{a.chartKey++,a.activeName==="echarts"&&setTimeout(()=>{L(d.dataList),G(()=>{var t,e,o,r;(t=D.value)==null||t.resize(),(e=O.value)==null||e.resize(),(o=z.value)==null||o.resize(),(r=A.value)==null||r.resize()})},100)});const C=w({data:[],currentProject:{}}),W=w({defaultParams:{type:"day",year:[l().format("YYYY"),l().format("YYYY")],month:[l().format("YYYY-MM"),l().format("YYYY-MM")],day:[l().format("YYYY-MM-DD"),l().format("YYYY-MM-DD")]},filters:[{type:"radio-button",field:"type",options:[{label:"日报",value:"day"},{label:"月报",value:"month"},{label:"年报",value:"year"}],label:"报告类型"},{type:"daterange",label:"选择时间",field:"day",clearable:!1,handleHidden:(t,e,o)=>{o.hidden=t.type==="month"||t.type==="year"}},{type:"monthrange",label:"选择时间",field:"month",clearable:!1,handleHidden:(t,e,o)=>{o.hidden=t.type==="day"||t.type==="year"}},{type:"yearrange",label:"选择时间",field:"year",clearable:!1,handleHidden:(t,e,o)=>{o.hidden=t.type==="month"||t.type==="day"}}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",click:()=>T(),svgIcon:M(se)},{text:"导出",perm:!0,type:"warning",svgIcon:M(le),click:()=>V()},{perm:!0,text:"打印",type:"success",svgIcon:M(ce),click:()=>j()}]}]}),d=w({loading:!0,dataList:[],columns:[],operations:[],operationWidth:"150px",pagination:{hide:!1},border:!0,stripe:!0,size:"default"}),T=()=>{var s;d.loading=!0;const t=((s=Y.value)==null?void 0:s.queryParams)||{},e=ye.find(h=>h.value===t.type),o=t[(e==null?void 0:e.value)||"day"];if(!o||!o[0]||!o[1]){d.loading=!1;return}a.title="水源地取水总量、耗电总量和吨水电耗("+(e==null?void 0:e.label)+l(o[0]).format((e==null?void 0:e.data)||"YYYY-MM-DD")+"至"+l(o[1]).format((e==null?void 0:e.data)||"YYYY-MM-DD")+")";const[r,y]=o,i={queryType:t.type,start:l(r).startOf("day").valueOf(),end:l(y).endOf("day").valueOf(),name:""};he(i).then(h=>{var N;const v=((N=h.data)==null?void 0:N.data)||[];a.dataList=v;const P=[{prop:"index",label:"序号",minWidth:120,align:"center"},{prop:"name",label:"设备名称",minWidth:200,align:"center"},{prop:"unitConsumption",label:"吨水电耗",unit:"(kWh)",minWidth:180,align:"center"},{prop:"totalFlow",label:"取水量",unit:"(m³)",minWidth:180,align:"center"},{prop:"energy",label:"用电量",unit:"(kW)",minWidth:180,align:"center"}];d.columns=P;const g=v.map((x,E)=>({index:E+1,name:x.name||`水源地${E+1}`,unitConsumption:x.unitConsumption||0,totalFlow:x.totalFlow||0,energy:x.energy||0}));d.dataList=g,d.loading=!1,setTimeout(()=>{K(g),a.activeName==="echarts"&&L(g)},200)}).catch(h=>{console.error("获取数据失败:",h),d.loading=!1,J.error("获取数据失败，请稍后重试")})},V=()=>{var t;(t=u.value)==null||t.exportTable()},j=()=>{fe({title:a.title,data:d.dataList,titleList:d.columns})},F=()=>{var t,e,o,r;(t=D.value)==null||t.resize(),(e=O.value)==null||e.resize(),(o=z.value)==null||o.resize(),(r=A.value)==null||r.resize()},K=t=>{const e=["#5470C6","#91CC75","#FAC858","#EE6666","#73C0DE","#3BA272","#FC8452","#9A60B4","#EA7CCC"],o=t.map((i,s)=>({name:i.name,value:i.unitConsumption||0,itemStyle:{color:e[s%e.length]}}));a.pieChart1Option={tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} kWh ({d}%)"},series:[{name:"吨水电耗",type:"pie",radius:["40%","70%"],center:["50%","50%"],data:o,emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}},label:{show:!0,formatter:`{b}
{c} kWh`}}]};const r=t.map((i,s)=>({name:i.name,value:i.totalFlow||0,itemStyle:{color:e[s%e.length]}}));a.pieChart2Option={tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} m³ ({d}%)"},series:[{name:"取水量",type:"pie",radius:["40%","70%"],center:["50%","50%"],data:r,emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}},label:{show:!0,formatter:`{b}
{c} m³`}}]};const y=t.map((i,s)=>({name:i.name,value:i.energy||0,itemStyle:{color:e[s%e.length]}}));a.pieChart3Option={tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} kW ({d}%)"},series:[{name:"用电量",type:"pie",radius:["40%","70%"],center:["50%","50%"],data:y,emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}},label:{show:!0,formatter:`{b}
{c} kW`}}]}},L=t=>{const e=ge();e.xAxis.data=t.map(r=>r.name||`设备${r.index}`),e.legend={top:20,type:"scroll",width:"500",textStyle:{fontSize:12}},e.yAxis[0].name="数值";const o=[{name:"吨水电耗(kWh)",type:"line",data:t.map(r=>r.unitConsumption||0),lineStyle:{color:"#5470C6",width:3},itemStyle:{color:"#5470C6"},symbol:"circle",symbolSize:6,smooth:!0,yAxisIndex:0},{name:"取水量(m³)",type:"bar",data:t.map(r=>r.totalFlow||0),itemStyle:{color:"#91CC75"},yAxisIndex:0},{name:"用电量(kW)",type:"bar",data:t.map(r=>r.energy||0),itemStyle:{color:"#FAC858"},yAxisIndex:1}];e.yAxis.push({position:"right",type:"value",name:"用电量(kW)",axisLine:{show:!0,lineStyle:{color:"#FAC858"}},axisLabel:{show:!0,formatter:"{value}"},splitLine:{show:!1}}),e.series=o,a.chartOption=e};return Q(async()=>{var e;const t=await b("水源地");C.data=t,C.currentProject=Z(C.data),W.defaultParams={...W.defaultParams,treeData:C.currentProject},(e=Y.value)==null||e.resetForm(),T(),window.addEventListener("resize",()=>{setTimeout(()=>{F()},100)})}),ee(()=>{window.removeEventListener("resize",F)}),(t,e)=>{const o=me,r=oe,y=re,i=te("VChart"),s=ne,h=de,v=ie,P=H;return _(),ae("div",_e,[c(o,{ref_key:"cardSearch",ref:Y,config:n(W)},null,8,["config"]),c(P,{class:"card",title:n(a).activeName==="list"?"水源地取水总量、耗电总量和吨水电耗":"水源地能耗图表"},{query:m(()=>[c(y,{modelValue:n(a).activeName,"onUpdate:modelValue":e[0]||(e[0]=g=>n(a).activeName=g)},{default:m(()=>[c(r,{label:"echarts"},{default:m(()=>[c(n(B),{style:{"margin-right":"1px","font-size":"16px"},icon:"clarity:bar-chart-line"})]),_:1}),c(r,{label:"list"},{default:m(()=>[c(n(B),{style:{"margin-right":"1px","font-size":"16px"},icon:"material-symbols:table"})]),_:1})]),_:1},8,["modelValue"])]),default:m(()=>[c(v,{gutter:20},{default:m(()=>[c(s,{span:8},{default:m(()=>[p("div",be,[p("div",Ce,[e[1]||(e[1]=p("h4",{class:"chart-title"},"取水总量",-1)),(_(),k(i,{key:n(a).chartKey,ref_key:"refPieChart2",ref:z,theme:n(S)().isDark?"dark":"light",option:n(a).pieChart2Option,class:"pie-chart"},null,8,["theme","option"]))]),p("div",ve,[e[2]||(e[2]=p("h4",{class:"chart-title"},"耗电总量",-1)),(_(),k(i,{key:n(a).chartKey,ref_key:"refPieChart3",ref:A,theme:n(S)().isDark?"dark":"light",option:n(a).pieChart3Option,class:"pie-chart"},null,8,["theme","option"]))]),p("div",xe,[e[3]||(e[3]=p("h4",{class:"chart-title"},"吨水电量",-1)),(_(),k(i,{key:n(a).chartKey,ref_key:"refPieChart1",ref:O,theme:n(S)().isDark?"dark":"light",option:n(a).pieChart1Option,class:"pie-chart"},null,8,["theme","option"]))])])]),_:1}),c(s,{span:16},{default:m(()=>[I(p("div",we,[c(h,{id:"print",ref_key:"refTable",ref:u,class:"card-table",config:n(d)},null,8,["config"])],512),[[q,n(a).activeName==="list"]]),I(p("div",ke,[p("div",Se,[e[4]||(e[4]=p("h4",{class:"chart-title"},"综合对比图表",-1)),(_(),k(i,{key:n(a).chartKey,ref_key:"refChart",ref:D,theme:n(S)().isDark?"dark":"light",option:n(a).chartOption,class:"mixed-chart"},null,8,["theme","option"]))])],512),[[q,n(a).activeName==="echarts"]])]),_:1})]),_:1})]),_:1},8,["title"])])}}}),Ne=pe(Ye,[["__scopeId","data-v-3649e62a"]]);export{Ne as default};
