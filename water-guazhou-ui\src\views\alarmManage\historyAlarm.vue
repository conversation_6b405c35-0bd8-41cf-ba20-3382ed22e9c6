<template>
  <!-- 历史告警 -->
  <TreeBox v-loading="!!TreeData.loading">
    <template #tree>
      <SLTree :tree-data="TreeData" />
    </template>
    <!-- 历史告警 -->
    <CardSearch
      ref="refSearch"
      :config="SearchConfig"
    />
    <CardTable
      :config="TableConfig"
      class="card-table"
    />
    <DialogForm
      ref="refDialogFormReport"
      :config="DialogFormConfigReport"
    >
      <infoTable
        v-if="state.detailsInfo.visible"
        :dialog-info="state.detailsInfo"
        :device-name="state.deviceName"
      ></infoTable>
    </DialogForm>
  </TreeBox>
</template>

<script lang="ts" setup>
import { getHistoryAlarmList, getAlarmList } from '@/api/alarm' // getAlarmReal, , getDevicesAlarm, getAlarmRealByProjectId
import { getDevice } from '@/api/device' // , getGateway
import { removeSlash } from '@/utils/removeIdSlash' // 处理id, idRemoveSlash
import infoTable from './components/infoTable.vue'
import { useBusinessStore } from '@/store'

const businessStore = useBusinessStore()
const refDialogFormReport = ref<IDialogFormIns>()
const refSearch = ref<ISearchIns>()
const state = reactive<{
  deviceName: Map<string, any>
  alarmSetValue: Map<string, any>
  alarmInfo: Map<string, any>
  detailsInfo: {
    visible: boolean
    isStatistics: boolean
    row: any
    close:() => any
      }
  pickerOptions: {
    disabledDate: (item) => boolean
  }
  severityColor: Record<string, any>
      }>({
        deviceName: new Map(),
        alarmSetValue: new Map(),
        alarmInfo: new Map(),
        detailsInfo: {
          visible: false,
          isStatistics: true,
          row: {},
          close: () => {
            state.detailsInfo.visible = false
          }
        },
        pickerOptions: {
          disabledDate(time) {
            return time.getTime() > Date.now()
          }
        },
        severityColor: {
          提示: 'rgb(85,204,244)',
          次要: 'rgb(255,216,0)',
          重要: '#f58717',
          紧急: 'rgb(245,75,23)',
          严重: '#FF0000'
        }
      })
const TreeData = reactive<SLTreeConfig>({
  title: '区域划分',
  data: businessStore.projectList,
  currentProject: businessStore.selectedProject,
  isFilterTree: true,
  treeNodeHandleClick: data => {
    // 设置当前选中项目信息
    TreeData.currentProject = data
    businessStore.SET_selectedProject(data)
    refreshData()
  }
})
const SearchConfig = reactive<ISearch>({
  filters: [
    { label: '搜索', field: 'name', type: 'input', labelWidth: 60 },
    {
      label: '设备',
      field: 'deviceId',
      type: 'select',
      multiple: true,
      labelWidth: 40,
      options: []
    },
    { label: '日期', field: 'daterange', type: 'daterange', labelWidth: 40 },
    {
      label: '告警类型',
      field: 'type',
      type: 'select',
      options: [
        { value: 'scope', label: '范围告警' },
        { value: 'change', label: '变动告警' },
        { value: 'offline', label: '掉线告警' }
      ],
      formatter: (val, row, filter: any) => {
        return filter?.options?.find(item => item.value === val)?.label || val
      }
    },
    {
      label: '解除类型',
      field: 'alarmTypes',
      type: 'select',
      options: [
        { value: 'CLEAR_FORCED', label: '强制解除' },
        { value: 'CLEARED_ACK', label: '自动恢复' },
        { value: 'offline', label: '掉线告警' }
      ],
      formatter: (val, filter) => {
        return filter?.options?.find(item => item.value === val)?.label || val
      }
    }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          icon: 'iconfont icon-chaxun',
          text: '查询',
          click: () => refreshData()
        }
      ]
    }
  ],
  defaultParams: {
    daterange: [
      moment().subtract(1, 'months').format('YYYY-MM-DD'),
      moment().format('YYYY-MM-DD')
    ]
  }
})
const TableConfig = reactive<ICardTable>({
  loading: false,
  dataList: [],
  columns: [
    { prop: 'name', label: '告警名称', width: 200 },
    { prop: 'showDeviceN', label: '告警设备', width: 200 },
    { minWidth: 100, prop: 'alarmType', label: '告警类型' },
    { prop: 'cycleName', label: '周期' },
    {
      prop: 'createdTime',
      label: '告警时间',
      icon: 'iconfont icon-shijian',
      iconStyle: {
        color: '#69e850',
        display: 'inline-block',
        'font-size': '16px'
      },
      width: 160
    },
    {
      minWidth: 100,
      prop: 'severity',
      label: '告警级别',
      cellStyle: row => ({
        'border-radius': '14px',
        color: '#fff',
        'line-height': '20px',
        height: '20px',
        padding: '4px 13px',
        'font-size': '12px',
        backgroundColor: row.severityColor
      })
    },
    { prop: 'alarmValue', label: '告警触发值', width: 120 },
    { prop: 'recoverSet', label: '恢复触发值', width: 120 },
    { minWidth: 100, prop: 'recoverType', label: '解除类型' },
    { prop: 'dismissal', label: '确认/解除人', width: 150 },
    {
      prop: 'clearTime',
      label: '解除时间',
      icon: 'iconfont icon-shijian',
      iconStyle: {
        color: '#69e850'
      },
      width: 160
    },
    {
      prop: 'clearRemarks',
      label: '操作备注',
      width: 160,
      formatter: row => row.details.clearRemarks
    }
  ],
  operations: [
    {
      text: '详情',
      perm: true,
      isTextBtn: true,
      icon: 'iconfont icon-xiangqing',
      click: row => rowInfo(row)
    }
  ],
  operationWidth: '80px',
  operationFixed: 'right',
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page
      TableConfig.pagination.limit = size
      refreshData()
    }
  }
})

const DialogFormConfigReport = reactive<IDialogFormConfig>({
  title: '详情',
  group: []
})

const refreshData = async () => {
  if (!TreeData.currentProject.disabled) {
    // 获取项目设备
    const devices = await getDevice(TreeData.currentProject.id)
    const filter = SearchConfig.filters?.find(
      item => item.field === 'deviceId'
    ) as IFormSelect
    filter
      && (filter.options = devices.data.map(item => ({
        label: item.name,
        value: removeSlash(item.id.id)
      })))
    devices.data.forEach(item => {
      state.deviceName.set(item.id.id, item.name)
    })
    const aData = await getAlarmList()
    for (const item of aData.data) {
      state.alarmSetValue.set(removeSlash(item.id.id), item.details)
    }

    const query = refSearch.value?.queryParams || {}
    const params: any = {
      projectId: TreeData.currentProject.id,
      page: TableConfig.pagination.page,
      size: TableConfig.pagination.limit,
      ...query
    }
    const [start, end] = params.daterange
    params.start = moment(start).valueOf()
    params.end = moment(end).add(1, 'd').add(-1, 's').valueOf()
    delete params.daterange

    const res = await getHistoryAlarmList(params)
    handleAlarm(res.data)
  }
}

const handleAlarm = val => {
  const data = val.data
  const alarmList: any[] = []
  const date = {
    day: '日',
    month: '月',
    year: '年'
  }
  data.forEach(item => {
    const dName = state.deviceName.get(item.originator.id)
      ? state.deviceName.get(item.originator.id)
      : '设备已删除'
    item.name = item.alarmJsonName ? item.alarmJsonName : '掉线 - ' + dName
    item.showDeviceN = dName
    const infoV = state.alarmSetValue.get(item.alarmJsonId)
    item.severityColor = state.severityColor[item.severity]

    if (infoV) {
      item.alarmValue = infoV.attributeName + ': ' + infoV.alarmSetValue
      item.recoverSet = infoV.recoverSetValue
    } else {
      item.alarmValue = '此条设置已删除'
      item.recoverSet = '此条设置已删除'
      if (item.type === 'offline') {
        item.alarmValue = '-'
        item.recoverSet = '-'
      }
    }
    if (item.alarmCycle) {
      item.cycleName = date[item.alarmCycle]
      item.recoverSet = '-'
    } else {
      item.cycleName = ''
      item.cycle = null
    }
    item.alarmType = '范围告警'
    item.alarmType = item.type === 'change' ? '变动告警' : item.alarmType
    item.alarmType = item.type === 'offline' ? '掉线告警' : item.alarmType
    // item.showStatus = '强制解除'
    // item.showStatus = item.status === ''
    item.clearTime = moment(item.clearTs).format('YYYY-MM-DD HH:mm')
    item.removeRemark = item.details.removeRemark
    item.createdTime = moment(item.createdTime).format('YYYY-MM-DD HH:mm')
    if (item.status === 'CLEAR_FORCED') {
      item.recoverType = '强制解除'
      item.clearRemarks = item.details.clearRemarks
        ? item.details.clearRemarks
        : ''
      item.dismissal = item.details.dismissal ? item.details.dismissal : ''
    } else {
      item.recoverType = '自动恢复'
      item.clearRemarks = item.details.confirmRemarks
        ? item.details.confirmRemarks
        : ''
      item.dismissal = item.details.confirm ? item.details.confirm : ''
    }
    item.recordList = [] // details.record 详细告警
    if (item.details.record) {
      for (const i of item.details.record) {
        const infoItem = {
          time: moment(parseInt(i.ts)).format('YYYY-MM-DD HH:mm'),
          infoValue: i.info,
          status: i.status.toUpperCase() === 'ALARM' ? '触发报警' : '恢复'
        }
        item.recordList.push(infoItem)
      }
    }

    alarmList.push(item)
  })
  TableConfig.dataList = alarmList
  TableConfig.pagination.total = val.total
}

const rowInfo = row => {
  refDialogFormReport.value?.openDialog()
  state.detailsInfo.row = row
  state.detailsInfo.visible = true
}
onMounted(() => {
  refreshData()
})
</script>

<style lang="scss" scoped>
.content-box-container {
  .filter-box {
    // margin-bottom: 10px;
    .box-btns {
      // height: 30px;
      margin-bottom: 10px;
      .top-filter {
        width: 600px;
        margin-left: 5px;
        margin-bottom: 10px;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      .filter_btns {
        width: 100%;
      }
    }
    .filter-label {
      margin-left: 40px;
    }
    .line {
      width: 15px;
      height: 32px;
      border-right: 2px solid gray;
      margin-right: 10px;
    }
    .filter-input {
      width: 240px;
      margin-right: 5px;
    }
    .filter-inputs {
      width: 360px !important;
    }
    .filter-select {
      width: 130px;
      margin: 0 5px 0 5px;
    }
    .time-type {
      width: 70px;
    }
    .data-time {
      width: 130px;
      margin: 0 10px;
    }
  }
  .alarm-s-table {
    .severity_span_color {
      font-size: 12px;
      color: #fff;
      padding: 4px 13px;
      border-radius: 14px;
      line-height: 20px;
    }
    .icon-time-c {
      color: #69e850;
    }
    .alarm-row-btn {
      padding: 7px 15px;
      border-radius: 20px;
    }
  }
}
.card-table {
  height: calc(100% - 80px);
}
</style>
