package org.thingsboard.server.dao.sql.assay;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.assay.AssayReportAddress;

import java.util.List;

@Mapper
public interface AssayReportAddressMapper extends BaseMapper<AssayReportAddress> {
    void batchInsert(List<AssayReportAddress> entityList);

    List<AssayReportAddress> getList(@Param("reportId") String reportId);
}
