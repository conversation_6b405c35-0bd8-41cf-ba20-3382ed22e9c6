package org.thingsboard.server.dao.assay;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.User;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.request.AssayItemListRequest;
import org.thingsboard.server.dao.model.sql.assay.AssayItem;
import org.thingsboard.server.dao.sql.assay.AssayItemMapper;

import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class AssayItemServiceImpl implements AssayItemService {

    @Autowired
    private AssayItemMapper assayItemMapper;

    @Override
    public PageData<AssayItem> findList(AssayItemListRequest request, TenantId tenantId) {
        Page<AssayItem> pageRequest = new Page<>(request.getPage(), request.getSize());
        request.setTenantId(UUIDConverter.fromTimeUUID(tenantId.getId()));

        IPage<AssayItem> pageResult = assayItemMapper.findList(pageRequest, request);

        return new PageData<>(pageResult.getTotal(), pageResult.getRecords());
    }

    @Override
    public void save(AssayItem entity, User currentUser) {
        Date now = new Date();
        if (StringUtils.isNotBlank(entity.getId())) {
            entity.setUpdateTime(now);
            assayItemMapper.updateById(entity);
        } else {
            entity.setCreateTime(now);
            entity.setUpdateTime(now);
            entity.setCreateUser(UUIDConverter.fromTimeUUID(currentUser.getUuidId()));
            entity.setTenantId(UUIDConverter.fromTimeUUID(currentUser.getTenantId().getId()));
            assayItemMapper.insert(entity);
        }
    }

    @Override
    public void remove(List<String> ids) {
        for (String id : ids) {
            assayItemMapper.deleteById(id);
        }
    }
}
