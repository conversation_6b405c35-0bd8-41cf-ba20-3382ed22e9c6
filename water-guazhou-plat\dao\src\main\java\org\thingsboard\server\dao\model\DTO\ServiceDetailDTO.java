package org.thingsboard.server.dao.model.DTO;

import lombok.Data;

/**
 * 呼叫来源报表
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-01-03
 */
@Data
public class ServiceDetailDTO {
    private Integer no;

    private String time;

    private Integer all = 0;

    private Integer connect = 0;

    private Integer queue = 0;

    private Integer abandon = 0;

    private Integer inMiss = 0;

    private String inMissRate = "-";

    private Integer ivrIn = 0;

    private Integer ivrMiss = 0;

    private Integer ivrAnswer = 0;

    private String ivrAnswerRate = "-";

    private Integer personAnswer = 0;

    private String personAnswerRate = "-";

    private Integer voiceUse = 0;

    private String queueWaitAvg = "0";

    private String personCallDurationAvg = "0";

    private String endDurationAvg = "0";

    private String connectRate = "-";

    private String callCenterSolveRate = "-";

    private String callAnswerVelocityAvg = "0";

    private String callOccupyRate = "-";

    private String ringTimeAvg = "0";

    private String serviceLevel = "0/0";


    public ServiceDetailDTO() {
    }

    public ServiceDetailDTO(String time) {
        this.time = time;
    }
}
