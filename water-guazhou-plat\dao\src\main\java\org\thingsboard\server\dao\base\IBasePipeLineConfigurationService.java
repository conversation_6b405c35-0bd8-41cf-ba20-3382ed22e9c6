package org.thingsboard.server.dao.base;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.base.BasePipeLineConfiguration;
import org.thingsboard.server.dao.util.imodel.query.base.BasePipeLineConfigurationPageRequest;

import java.util.List;


/**
 * 平台管理-管网配置Service接口
 * 
 * <AUTHOR>
 * @date 2025-05-22
 */
public interface IBasePipeLineConfigurationService 
{
    /**
     * 查询平台管理-管网配置
     * 
     * @param id 平台管理-管网配置主键
     * @return 平台管理-管网配置
     */
    public BasePipeLineConfiguration selectBasePipeLineConfigurationById(String id);

    /**
     * 查询平台管理-管网配置列表
     * 
     * @param basePipeLineConfiguration 平台管理-管网配置
     * @return 平台管理-管网配置集合
     */
    public IPage<BasePipeLineConfiguration> selectBasePipeLineConfigurationList(BasePipeLineConfigurationPageRequest basePipeLineConfiguration);

    /**
     * 新增平台管理-管网配置
     * 
     * @param basePipeLineConfiguration 平台管理-管网配置
     * @return 结果
     */
    public int insertBasePipeLineConfiguration(BasePipeLineConfiguration basePipeLineConfiguration);

    /**
     * 修改平台管理-管网配置
     * 
     * @param basePipeLineConfiguration 平台管理-管网配置
     * @return 结果
     */
    public int updateBasePipeLineConfiguration(BasePipeLineConfiguration basePipeLineConfiguration);

    /**
     * 批量删除平台管理-管网配置
     * 
     * @param ids 需要删除的平台管理-管网配置主键集合
     * @return 结果
     */
    public int deleteBasePipeLineConfigurationByIds(List<String> ids);

    /**
     * 删除平台管理-管网配置信息
     * 
     * @param id 平台管理-管网配置主键
     * @return 结果
     */
    public int deleteBasePipeLineConfigurationById(String id);
}
