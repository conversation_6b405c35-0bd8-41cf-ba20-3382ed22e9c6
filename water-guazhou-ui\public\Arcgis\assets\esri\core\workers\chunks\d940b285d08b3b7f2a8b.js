"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[2472],{5732:(t,e,n)=>{n.d(e,{c:()=>i});var i="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{}},46521:(t,e,n)=>{function i(){return[1,0,0,0,1,0,0,0,1]}function s(t,e,n,i,s,r,o,c,a){return[t,e,n,i,s,r,o,c,a]}function r(t,e){return new Float64Array(t,e,9)}n.d(e,{a:()=>r,c:()=>i,f:()=>s}),Object.freeze(Object.defineProperty({__proto__:null,clone:function(t){return[t[0],t[1],t[2],t[3],t[4],t[5],t[6],t[7],t[8]]},create:i,createView:r,fromValues:s},Symbol.toStringTag,{value:"Module"}))},13598:(t,e,n)=>{function i(){return[1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1]}function s(t){return[t[0],t[1],t[2],t[3],t[4],t[5],t[6],t[7],t[8],t[9],t[10],t[11],t[12],t[13],t[14],t[15]]}function r(t,e){return new Float64Array(t,e,16)}n.d(e,{I:()=>o,a:()=>r,b:()=>s,c:()=>i});const o=[1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1];Object.freeze(Object.defineProperty({__proto__:null,IDENTITY:o,clone:s,create:i,createView:r,fromValues:function(t,e,n,i,s,r,o,c,a,h,u,f,l,d,_,m){return[t,e,n,i,s,r,o,c,a,h,u,f,l,d,_,m]}},Symbol.toStringTag,{value:"Module"}))},94961:(t,e,n)=>{function i(){return[0,0,0,1]}function s(t){return[t[0],t[1],t[2],t[3]]}function r(t,e){return new Float64Array(t,e,4)}n.d(e,{I:()=>o,a:()=>i,b:()=>s,c:()=>r});const o=[0,0,0,1];Object.freeze(Object.defineProperty({__proto__:null,IDENTITY:o,clone:s,create:i,createView:r,fromValues:function(t,e,n,i){return[t,e,n,i]}},Symbol.toStringTag,{value:"Module"}))},29268:(t,e,n)=>{n.d(e,{a:()=>T,c:()=>g,g:()=>b,h:()=>p,j:()=>S,m:()=>z}),n(80442);var i,s,r=n(92604),o=n(22021),c=n(70586),a=n(52138),h=n(17896),u=n(65617),f=n(98766),l=n(88669);(s=i||(i={}))[s.X=0]="X",s[s.Y=1]="Y",s[s.Z=2]="Z";var d=n(78341),_=n(61277),m=n(12981);function g(){return(0,l.c)()}function p(t,e=g()){return(0,f.c)(e,t)}function T(t){return t[3]}function b(t){return t}function O(t,e,n){if((0,c.Wi)(e))return!1;const{origin:i,direction:s}=e,r=y;r[0]=i[0]-t[0],r[1]=i[1]-t[1],r[2]=i[2]-t[2];const o=s[0]*s[0]+s[1]*s[1]+s[2]*s[2];if(0===o)return!1;const a=2*(s[0]*r[0]+s[1]*r[1]+s[2]*r[2]),h=a*a-4*o*(r[0]*r[0]+r[1]*r[1]+r[2]*r[2]-t[3]*t[3]);if(h<0)return!1;const u=Math.sqrt(h);let f=(-a-u)/(2*o);const l=(-a+u)/(2*o);return(f<0||l<f&&l>0)&&(f=l),!(f<0||(n&&(n[0]=i[0]+s[0]*f,n[1]=i[1]+s[1]*f,n[2]=i[2]+s[2]*f),0))}const y=(0,u.c)();function S(t,e){return O(t,e,null)}function R(t,e,n){const i=m.WM.get(),s=m.MP.get();(0,h.f)(i,e.origin,e.direction);const r=T(t);(0,h.f)(n,i,e.origin),(0,h.g)(n,n,1/(0,h.l)(n)*r);const o=E(t,e.origin),c=(0,_.EU)(e.origin,n);return(0,a.d)(s,c+o,i),(0,h.m)(n,n,s),n}function M(t,e,n){const i=(0,h.b)(m.WM.get(),e,t),s=(0,h.g)(m.WM.get(),i,t[3]/(0,h.l)(i));return(0,h.a)(n,s,t)}function E(t,e){const n=(0,h.b)(m.WM.get(),e,t),i=(0,h.l)(n),s=T(t),r=s+Math.abs(s-i);return(0,o.ZF)(s/r)}const A=(0,u.c)();function N(t,e,n,s){const r=(0,h.b)(A,e,t);switch(n){case i.X:{const t=(0,o.jE)(r,A)[2];return(0,h.s)(s,-Math.sin(t),Math.cos(t),0)}case i.Y:{const t=(0,o.jE)(r,A),e=t[1],n=t[2],i=Math.sin(e);return(0,h.s)(s,-i*Math.cos(n),-i*Math.sin(n),Math.cos(e))}case i.Z:return(0,h.n)(s,r);default:return}}function x(t,e){const n=(0,h.b)(F,e,t);return(0,h.l)(n)-t[3]}function z(t,e){const n=(0,h.d)(t,e),i=T(t);return n<=i*i}const F=(0,u.c)(),v=g();Object.freeze(Object.defineProperty({__proto__:null,altitudeAt:x,angleToSilhouette:E,axisAt:N,clear:function(t){t[0]=t[1]=t[2]=t[3]=0},closestPoint:function(t,e,n){return O(t,e,n)?n:((0,d.JI)(e,t,n),M(t,n,n))},closestPointOnSilhouette:R,containsPoint:z,copy:p,create:g,distanceToSilhouette:function(t,e){const n=(0,h.b)(m.WM.get(),e,t),i=(0,h.p)(n),s=t[3]*t[3];return Math.sqrt(Math.abs(i-s))},elevate:function(t,e,n){return t!==n&&(0,h.c)(n,t),n[3]=t[3]+e,n},fromCenterAndRadius:function(t,e){return(0,l.f)(t[0],t[1],t[2],e)},fromRadius:function(t,e){return t[0]=t[1]=t[2]=0,t[3]=e,t},fromValues:function(t,e,n,i){return(0,l.f)(t,e,n,i)},getCenter:b,getRadius:T,intersectRay:O,intersectRayClosestSilhouette:function(t,e,n){if(O(t,e,n))return n;const i=R(t,e,m.WM.get());return(0,h.a)(n,e.origin,(0,h.g)(m.WM.get(),e.direction,(0,h.i)(e.origin,i)/(0,h.l)(e.direction))),n},intersectsRay:S,projectPoint:M,setAltitudeAt:function(t,e,n,s){const r=x(t,e),o=N(t,e,i.Z,F),c=(0,h.g)(F,o,n-r);return(0,h.a)(s,e,c)},setExtent:function(t,e,n){return r.Z.getLogger("esri.geometry.support.sphere").error("sphere.setExtent is not yet supported"),t===n?n:p(t,n)},tmpSphere:v,wrap:function(t){return t}},Symbol.toStringTag,{value:"Module"}))},97323:(t,e,n)=>{function i(){return[0,0]}function s(t,e){return[t,e]}function r(t,e){return new Float64Array(t,e,2)}function o(){return s(1,1)}function c(){return s(1,0)}function a(){return s(0,1)}n.d(e,{Z:()=>h,a:()=>i,c:()=>r,f:()=>s});const h=[0,0],u=o(),f=c(),l=a();Object.freeze(Object.defineProperty({__proto__:null,ONES:u,UNIT_X:f,UNIT_Y:l,ZEROS:h,clone:function(t){return[t[0],t[1]]},create:i,createView:r,fromArray:function(t){const e=[0,0],n=Math.min(2,t.length);for(let i=0;i<n;++i)e[i]=t[i];return e},fromValues:s,ones:o,unitX:c,unitY:a,zeros:function(){return[0,0]}},Symbol.toStringTag,{value:"Module"}))},88669:(t,e,n)=>{function i(){return[0,0,0,0]}function s(t,e,n,i){return[t,e,n,i]}function r(t,e){return new Float64Array(t,e,4)}function o(){return s(1,1,1,1)}function c(){return s(1,0,0,0)}function a(){return s(0,1,0,0)}function h(){return s(0,0,1,0)}function u(){return s(0,0,0,1)}n.d(e,{a:()=>r,c:()=>i,f:()=>s});const f=o(),l=c(),d=a(),_=h(),m=u();Object.freeze(Object.defineProperty({__proto__:null,ONES:f,UNIT_W:m,UNIT_X:l,UNIT_Y:d,UNIT_Z:_,ZEROS:[0,0,0,0],clone:function(t){return[t[0],t[1],t[2],t[3]]},create:i,createView:r,fromArray:function(t){const e=[0,0,0,0],n=Math.min(4,t.length);for(let i=0;i<n;++i)e[i]=t[i];return e},fromValues:s,ones:o,unitW:u,unitX:c,unitY:a,unitZ:h,zeros:function(){return[0,0,0,0]}},Symbol.toStringTag,{value:"Module"}))},69801:(t,e,n)=>{n.d(e,{WJ:()=>a,Xq:()=>c});var i,s,r=n(70586),o=n(44553);(s=i||(i={}))[s.ALL=0]="ALL",s[s.SOME=1]="SOME";class c{constructor(t,e,n){this._namespace=t,this._storage=e,this._removeFunc=!1,this._hit=0,this._miss=0,this._storage.register(this),this._namespace+=":",n&&(this._storage.registerRemoveFunc(this._namespace,n),this._removeFunc=!0)}destroy(){this._storage.clear(this._namespace),this._removeFunc&&this._storage.deregisterRemoveFunc(this._namespace),this._storage.deregister(this),this._storage=null}get namespace(){return this._namespace.slice(0,-1)}get hitRate(){return this._hit/(this._hit+this._miss)}get size(){return this._storage.size}get maxSize(){return this._storage.maxSize}resetHitRate(){this._hit=this._miss=0}put(t,e,n,i=0){this._storage.put(this._namespace+t,e,n,i)}get(t){const e=this._storage.get(this._namespace+t);return void 0===e?++this._miss:++this._hit,e}pop(t){const e=this._storage.pop(this._namespace+t);return void 0===e?++this._miss:++this._hit,e}updateSize(t,e,n){this._storage.updateSize(this._namespace+t,e,n)}clear(){this._storage.clear(this._namespace)}clearAll(){this._storage.clearAll()}getStats(){return this._storage.getStats()}resetStats(){this._storage.resetStats()}}class a{constructor(t=10485760){this._maxSize=t,this._db=new Map,this._size=0,this._hit=0,this._miss=0,this._removeFuncs=new o.Z,this._users=new o.Z}destroy(){this.clearAll(),this._removeFuncs.clear(),this._users.clear(),this._db=null}register(t){this._users.push(t)}deregister(t){this._users.removeUnordered(t)}registerRemoveFunc(t,e){this._removeFuncs.push([t,e])}deregisterRemoveFunc(t){this._removeFuncs.filterInPlace((e=>e[0]!==t))}get size(){return this._size}get maxSize(){return this._maxSize}set maxSize(t){this._maxSize=Math.max(t,0),this._checkSizeLimit()}put(t,e,n,s){const r=this._db.get(t);if(r&&(this._size-=r.size,this._db.delete(t),r.entry!==e&&this._notifyRemove(t,r.entry,i.ALL)),n>this._maxSize)return void this._notifyRemove(t,e,i.ALL);if(void 0===e)return void console.warn("Refusing to cache undefined entry ");if(!n||n<0)return void console.warn("Refusing to cache entry with invalid size "+n);const o=1+Math.max(s,-3)- -3;this._db.set(t,{entry:e,size:n,lifetime:o,lives:o}),this._size+=n,this._checkSizeLimit()}updateSize(t,e,n){const s=this._db.get(t);if(s&&s.entry===e){for(this._size-=s.size;n>this._maxSize;){const s=this._notifyRemove(t,e,i.SOME);if(!((0,r.pC)(s)&&s>0))return void this._db.delete(t);n=s}s.size=n,this._size+=n,this._checkSizeLimit()}}pop(t){const e=this._db.get(t);if(e)return this._size-=e.size,this._db.delete(t),++this._hit,e.entry;++this._miss}get(t){const e=this._db.get(t);if(void 0!==e)return this._db.delete(t),e.lives=e.lifetime,this._db.set(t,e),++this._hit,e.entry;++this._miss}getStats(){const t={Size:Math.round(this._size/1048576)+"/"+Math.round(this._maxSize/1048576)+"MB","Hit rate":Math.round(100*this._getHitRate())+"%",Entries:this._db.size.toString()},e={},n=new Array;this._db.forEach(((t,i)=>{const s=t.lifetime;n[s]=(n[s]||0)+t.size,this._users.forAll((n=>{const s=n.namespace;if(i.startsWith(s)){const n=e[s]||0;e[s]=n+t.size}}))}));const i={};this._users.forAll((t=>{const n=t.namespace;if(!isNaN(t.hitRate)&&t.hitRate>0){const s=e[n]||0;e[n]=s,i[n]=Math.round(100*t.hitRate)+"%"}else i[n]="0%"}));const s=Object.keys(e);s.sort(((t,n)=>e[n]-e[t])),s.forEach((n=>t[n]=Math.round(e[n]/2**20)+"MB / "+i[n]));for(let e=n.length-1;e>=0;--e){const i=n[e];i&&(t["Priority "+(e+-3-1)]=Math.round(i/this.size*100)+"%")}return t}resetStats(){this._hit=this._miss=0,this._users.forAll((t=>t.resetHitRate()))}clear(t){this._db.forEach(((e,n)=>{n.startsWith(t)&&(this._size-=e.size,this._db.delete(n),this._notifyRemove(n,e.entry,i.ALL))}))}clearAll(){this._db.forEach(((t,e)=>this._notifyRemove(e,t.entry,i.ALL))),this._size=0,this._db.clear()}_getHitRate(){return this._hit/(this._hit+this._miss)}_notifyRemove(t,e,n){let i;return this._removeFuncs.some((s=>{if(t.startsWith(s[0])){const t=s[1](e,n);return"number"==typeof t&&(i=t),!0}return!1})),i}_checkSizeLimit(){if(!(this._size<=this._maxSize))for(const[t,e]of this._db){if(this._db.delete(t),e.lives<=1){this._size-=e.size;const n=this._notifyRemove(t,e.entry,i.SOME);(0,r.pC)(n)&&n>0&&(this._size+=n,e.lives=e.lifetime,e.size=n,this._db.set(t,e))}else--e.lives,this._db.set(t,e);if(this._size<=.9*this.maxSize)return}}}},22807:(t,e,n)=>{n.d(e,{x:()=>s});var i=n(41213);class s{constructor(t){this._allocator=t,this._items=[],this._itemsPtr=0,this._grow()}get(){return 0===this._itemsPtr&&(0,i.Y)((()=>this._reset())),this._itemsPtr===this._items.length&&this._grow(),this._items[this._itemsPtr++]}_reset(){const t=Math.min(3*Math.max(8,this._itemsPtr),this._itemsPtr+3*r);this._items.length=Math.min(t,this._items.length),this._itemsPtr=0}_grow(){for(let t=0;t<Math.max(8,Math.min(this._items.length,r));t++)this._items.push(this._allocator())}}const r=1024},43090:(t,e,n)=>{function i(t){return 32+t.length}function s(t){if(!t)return 0;let e=a;for(const n in t)if(t.hasOwnProperty(n)){const s=t[n];switch(typeof s){case"string":e+=i(s);break;case"number":e+=16;break;case"boolean":e+=4}}return e}function r(t){if(!t)return 0;if(Array.isArray(t))return function(t){const e=t.length;if(0===e||"number"==typeof t[0])return 32+8*e;let n=h;for(let i=0;i<e;i++)n+=o(t[i]);return n}(t);let e=a;for(const n in t)t.hasOwnProperty(n)&&(e+=o(t[n]));return e}function o(t){switch(typeof t){case"object":return r(t);case"string":return i(t);case"number":return 16;case"boolean":return 4;default:return 8}}function c(t,e){return h+t.length*e}n.d(e,{Ul:()=>r,Y8:()=>u,do:()=>c,f2:()=>s});const a=32,h=32;var u;!function(t){t[t.KILOBYTES=1024]="KILOBYTES",t[t.MEGABYTES=1048576]="MEGABYTES",t[t.GIGABYTES=1073741824]="GIGABYTES"}(u||(u={}))},16996:(t,e,n)=>{n.d(e,{Ue:()=>a,nF:()=>u,zk:()=>h});var i=n(22021),s=n(22807),r=n(17896),o=n(65617),c=n(12981);function a(t){return t?{origin:(0,o.a)(t.origin),vector:(0,o.a)(t.vector)}:{origin:(0,o.c)(),vector:(0,o.c)()}}function h(t,e,n=a()){return(0,r.c)(n.origin,t),(0,r.b)(n.vector,e,t),n}function u(t,e,n){return function(t,e,n,s,o){const{vector:a,origin:h}=t,u=(0,r.b)(c.WM.get(),e,h),f=(0,r.e)(a,u)/(0,r.p)(a);return(0,r.g)(o,a,(0,i.uZ)(f,0,1)),(0,r.a)(o,o,t.origin)}(t,e,0,0,n)}(0,o.c)(),(0,o.c)(),new s.x((()=>a()))},16306:(t,e,n)=>{n.d(e,{aX:()=>R});var i=n(68773),s=n(20102),r=n(92604),o=n(70586),c=n(38913),a=n(58901),h=n(73913),u=n(8744),f=n(40488),l=(n(66577),n(3172)),d=n(33955),_=n(11282),m=n(17452);async function g(t,e,n){const i="string"==typeof t?(0,m.mN)(t):t,s=e[0].spatialReference,r=(0,d.Ji)(e[0]),o={...n,query:{...i.query,f:"json",sr:s.wkid?s.wkid:JSON.stringify(s),geometries:JSON.stringify((a=e,{geometryType:(0,d.Ji)(a[0]),geometries:a.map((t=>t.toJSON()))}))}},{data:c}=await(0,l.default)(i.path+"/simplify",o);var a;return function(t,e,n){const i=(0,d.q9)(e);return t.map((t=>{const e=i.fromJSON(t);return e.spatialReference=n,e}))}(c.geometries,r,s)}const p=r.Z.getLogger("esri.geometry.support.normalizeUtils");function T(t){return"polygon"===t[0].type}function b(t){return"polyline"===t[0].type}function O(t,e,n){if(e){const e=function(t,e){if(!(t instanceof a.Z||t instanceof c.Z)){const t="straightLineDensify: the input geometry is neither polyline nor polygon";throw p.error(t),new s.Z(t)}const n=(0,h.x3)(t),i=[];for(const t of n){const n=[];i.push(n),n.push([t[0][0],t[0][1]]);for(let i=0;i<t.length-1;i++){const s=t[i][0],r=t[i][1],o=t[i+1][0],c=t[i+1][1],a=Math.sqrt((o-s)*(o-s)+(c-r)*(c-r)),h=(c-r)/a,u=(o-s)/a,f=a/e;if(f>1){for(let t=1;t<=f-1;t++){const i=t*e,o=u*i+s,c=h*i+r;n.push([o,c])}const t=(a+Math.floor(f-1)*e)/2,i=u*t+s,o=h*t+r;n.push([i,o])}n.push([o,c])}}return function(t){return"polygon"===t.type}(t)?new c.Z({rings:i,spatialReference:t.spatialReference}):new a.Z({paths:i,spatialReference:t.spatialReference})}(t,1e6);t=(0,f.Sx)(e,!0)}return n&&(t=(0,h.Sy)(t,n)),t}function y(t,e,n){if(Array.isArray(t)){const i=t[0];if(i>e){const n=(0,h.XZ)(i,e);t[0]=i+n*(-2*e)}else if(i<n){const e=(0,h.XZ)(i,n);t[0]=i+e*(-2*n)}}else{const i=t.x;if(i>e){const n=(0,h.XZ)(i,e);t=t.clone().offset(n*(-2*e),0)}else if(i<n){const e=(0,h.XZ)(i,n);t=t.clone().offset(e*(-2*n),0)}}return t}function S(t,e){let n=-1;for(let i=0;i<e.cutIndexes.length;i++){const s=e.cutIndexes[i],r=e.geometries[i],o=(0,h.x3)(r);for(let t=0;t<o.length;t++){const e=o[t];e.some((n=>{if(n[0]<180)return!0;{let n=0;for(let t=0;t<e.length;t++){const i=e[t][0];n=i>n?i:n}n=Number(n.toFixed(9));const i=-360*(0,h.XZ)(n,180);for(let n=0;n<e.length;n++){const e=r.getPoint(t,n);r.setPoint(t,n,e.clone().offset(i,0))}return!0}}))}if(s===n){if(T(t))for(const e of(0,h.x3)(r))t[s]=t[s].addRing(e);else if(b(t))for(const e of(0,h.x3)(r))t[s]=t[s].addPath(e)}else n=s,t[s]=r}return t}async function R(t,e,n){if(!Array.isArray(t))return R([t],e);e&&"string"!=typeof e&&p.warn("normalizeCentralMeridian()","The url object is deprecated, use the url string instead");const s="string"==typeof e?e:e?.url??i.Z.geometryServiceUrl;let r,m,T,b,M,E,A,N,x=0;const z=[],F=[];for(const e of t)if((0,o.Wi)(e))F.push(e);else if(r||(r=e.spatialReference,m=(0,u.C5)(r),T=r.isWebMercator,E=T?102100:4326,b=h.UZ[E].maxX,M=h.UZ[E].minX,A=h.UZ[E].plus180Line,N=h.UZ[E].minus180Line),m)if("mesh"===e.type)F.push(e);else if("point"===e.type)F.push(y(e.clone(),b,M));else if("multipoint"===e.type){const t=e.clone();t.points=t.points.map((t=>y(t,b,M))),F.push(t)}else if("extent"===e.type){const t=e.clone()._normalize(!1,!1,m);F.push(t.rings?new c.Z(t):t)}else if(e.extent){const t=e.extent,n=(0,h.XZ)(t.xmin,M)*(2*b);let i=0===n?e.clone():(0,h.Sy)(e.clone(),n);t.offset(n,0),t.intersects(A)&&t.xmax!==b?(x=t.xmax>x?t.xmax:x,i=O(i,T),z.push(i),F.push("cut")):t.intersects(N)&&t.xmin!==M?(x=t.xmax*(2*b)>x?t.xmax*(2*b):x,i=O(i,T,360),z.push(i),F.push("cut")):F.push(i)}else F.push(e.clone());else F.push(e);let v=(0,h.XZ)(x,b),w=-90;const I=v,P=new a.Z;for(;v>0;){const t=360*v-180;P.addPath([[t,w],[t,-1*w]]),w*=-1,v--}if(z.length>0&&I>0){const e=S(z,await async function(t,e,n,i){const s=(0,_.en)(t),r=e[0].spatialReference,o={...i,query:{...s.query,f:"json",sr:JSON.stringify(r),target:JSON.stringify({geometryType:(0,d.Ji)(e[0]),geometries:e}),cutter:JSON.stringify(n)}},c=await(0,l.default)(s.path+"/cut",o),{cutIndexes:a,geometries:h=[]}=c.data;return{cutIndexes:a,geometries:h.map((t=>{const e=(0,d.im)(t);return e.spatialReference=r,e}))}}(s,z,P,n)),i=[],r=[];for(let n=0;n<F.length;n++){const s=F[n];if("cut"!==s)r.push(s);else{const s=e.shift(),c=t[n];(0,o.pC)(c)&&"polygon"===c.type&&c.rings&&c.rings.length>1&&s.rings.length>=c.rings.length?(i.push(s),r.push("simplify")):r.push(T?(0,f.$)(s):s)}}if(!i.length)return r;const c=await g(s,i,n),a=[];for(let t=0;t<r.length;t++){const e=r[t];"simplify"!==e?a.push(e):a.push(T?(0,f.$)(c.shift()):c.shift())}return a}const L=[];for(let t=0;t<F.length;t++){const e=F[t];if("cut"!==e)L.push(e);else{const t=z.shift();L.push(!0===T?(0,f.$)(t):t)}}return L}},73913:(t,e,n)=>{n.d(e,{Sy:()=>a,UZ:()=>o,XZ:()=>c,x3:()=>h});var i=n(58901),s=n(82971),r=n(33955);const o={102100:{maxX:20037508.342788905,minX:-20037508.342788905,plus180Line:new i.Z({paths:[[[20037508.342788905,-20037508.342788905],[20037508.342788905,20037508.342788905]]],spatialReference:s.Z.WebMercator}),minus180Line:new i.Z({paths:[[[-20037508.342788905,-20037508.342788905],[-20037508.342788905,20037508.342788905]]],spatialReference:s.Z.WebMercator})},4326:{maxX:180,minX:-180,plus180Line:new i.Z({paths:[[[180,-180],[180,180]]],spatialReference:s.Z.WGS84}),minus180Line:new i.Z({paths:[[[-180,-180],[-180,180]]],spatialReference:s.Z.WGS84})}};function c(t,e){return Math.ceil((t-e)/(2*e))}function a(t,e){const n=h(t);for(const t of n)for(const n of t)n[0]+=e;return t}function h(t){return(0,r.oU)(t)?t.rings:t.paths}},62073:(t,e,n)=>{n(22021),n(17896);var i,s,r=n(65617);n(29268),n(61277),n(12981),(0,r.c)(),(0,r.c)(),(0,r.c)(),(0,r.c)(),(0,r.c)(),(0,r.c)(),(0,r.c)(),(0,r.c)(),(0,r.c)(),(0,r.c)(),(s=i||(i={}))[s.NONE=0]="NONE",s[s.CLAMP=1]="CLAMP",s[s.INFINITE_MIN=4]="INFINITE_MIN",s[s.INFINITE_MAX=8]="INFINITE_MAX",i.INFINITE_MIN,i.INFINITE_MAX,i.INFINITE_MAX},78341:(t,e,n)=>{n.d(e,{JI:()=>h,Ue:()=>o,re:()=>a}),n(67676);var i=n(22807),s=n(17896),r=n(65617);function o(t){return t?c((0,r.a)(t.origin),(0,r.a)(t.direction)):c((0,r.c)(),(0,r.c)())}function c(t,e){return{origin:t,direction:e}}function a(t,e){const n=u.get();return n.origin=t,n.direction=e,n}function h(t,e,n){const i=(0,s.e)(t.direction,(0,s.b)(n,e,t.origin));return(0,s.a)(n,t.origin,(0,s.g)(n,t.direction,i)),n}n(12981);const u=new i.x((()=>o()))},61277:(t,e,n)=>{n.d(e,{EU:()=>o});var i=n(22021),s=n(17896),r=n(65617);function o(t,e){const n=(0,s.e)(t,e)/((0,s.l)(t)*(0,s.l)(e));return-(0,i.ZF)(n)}(0,r.c)(),(0,r.c)()},12981:(t,e,n)=>{n.d(e,{MP:()=>_,WM:()=>d});var i=n(43090),s=n(41213),r=n(46521),o=n(13598),c=n(94961),a=n(97323),h=n(65617),u=n(88669);class f{constructor(t,e,n){this._itemByteSize=t,this._itemCreate=e,this._buffers=new Array,this._items=new Array,this._itemsPtr=0,this._itemsPerBuffer=Math.ceil(n/this._itemByteSize)}get(){0===this._itemsPtr&&(0,s.Y)((()=>this._reset()));const t=Math.floor(this._itemsPtr/this._itemsPerBuffer);for(;this._buffers.length<=t;){const t=new ArrayBuffer(this._itemsPerBuffer*this._itemByteSize);for(let e=0;e<this._itemsPerBuffer;++e)this._items.push(this._itemCreate(t,e*this._itemByteSize));this._buffers.push(t)}return this._items[this._itemsPtr++]}_reset(){const t=2*(Math.floor(this._itemsPtr/this._itemsPerBuffer)+1);for(;this._buffers.length>t;)this._buffers.pop(),this._items.length=this._buffers.length*this._itemsPerBuffer;this._itemsPtr=0}static createVec2f64(t=l){return new f(16,a.c,t)}static createVec3f64(t=l){return new f(24,h.b,t)}static createVec4f64(t=l){return new f(32,u.a,t)}static createMat3f64(t=l){return new f(72,r.a,t)}static createMat4f64(t=l){return new f(128,o.a,t)}static createQuatf64(t=l){return new f(32,c.c,t)}get test(){return{size:this._buffers.length*this._itemsPerBuffer*this._itemByteSize}}}const l=4*i.Y8.KILOBYTES,d=(f.createVec2f64(),f.createVec3f64()),_=(f.createVec4f64(),f.createMat3f64(),f.createMat4f64());f.createQuatf64()},11282:(t,e,n)=>{n.d(e,{cv:()=>c,en:()=>o,lA:()=>r}),n(68773),n(40330);var i=n(22974),s=n(17452);function r(t,e){return e?{...e,query:{...t??{},...e.query}}:{query:t}}function o(t){return"string"==typeof t?(0,s.mN)(t):(0,i.d9)(t)}function c(t,e,n){const i={};for(const s in t){if("declaredClass"===s)continue;const r=t[s];if(null!=r&&"function"!=typeof r)if(Array.isArray(r)){i[s]=[];for(let t=0;t<r.length;t++)i[s][t]=c(r[t])}else if("object"==typeof r)if(r.toJSON){const t=r.toJSON(n&&n[s]);i[s]=e?t:JSON.stringify(t)}else i[s]=e?r:JSON.stringify(r);else i[s]=r}return i}n(71058)},11726:(t,e,n)=>{n.d(e,{hu:()=>s,yK:()=>r}),n(97323),n(98766),(0,n(88669).c)();class i{constructor(t){this.message=t}toString(){return`AssertException: ${this.message}`}}function s(t,e){if(!t){e=e||"Assertion";const t=new Error(e).stack;throw new i(`${e} at ${t}`)}}function r(t,e,n,i){let s,r=(n[0]-t[0])/e[0],o=(i[0]-t[0])/e[0];r>o&&(s=r,r=o,o=s);let c=(n[1]-t[1])/e[1],a=(i[1]-t[1])/e[1];if(c>a&&(s=c,c=a,a=s),r>a||c>o)return!1;c>r&&(r=c),a<o&&(o=a);let h=(n[2]-t[2])/e[2],u=(i[2]-t[2])/e[2];return h>u&&(s=h,h=u,u=s),!(r>u||h>o||(u<o&&(o=u),o<0))}},73248:(t,e,n)=>{n.r(e),n.d(e,{default:()=>nt});var i,s,r,o,c,a=n(43697),h=n(70586),u=n(95330),f=(n(92604),n(75215),n(67676),n(20102),n(80442),n(52011)),l=n(17896),d=n(65617),_=n(16996),m=n(29268),g=n(26993),p=n(71143),T=n(44553),b=n(22807),O=(n(52138),n(98766),n(88669)),y=n(78341);function S(t){return t?{ray:(0,y.Ue)(t.ray),c0:t.c0,c1:t.c1}:{ray:(0,y.Ue)(),c0:0,c1:Number.MAX_VALUE}}function R(t,e){for(let n=0;n<o.NUM;n++){const i=t[n];if(i[0]*e[0]+i[1]*e[1]+i[2]*e[2]+i[3]>=e[3])return!1}return!0}new b.x((()=>S())),n(62073),n(12981),(r=i||(i={}))[r.LEFT=0]="LEFT",r[r.RIGHT=1]="RIGHT",r[r.BOTTOM=2]="BOTTOM",r[r.TOP=3]="TOP",r[r.NEAR=4]="NEAR",r[r.FAR=5]="FAR",function(t){t[t.NEAR_BOTTOM_LEFT=0]="NEAR_BOTTOM_LEFT",t[t.NEAR_BOTTOM_RIGHT=1]="NEAR_BOTTOM_RIGHT",t[t.NEAR_TOP_RIGHT=2]="NEAR_TOP_RIGHT",t[t.NEAR_TOP_LEFT=3]="NEAR_TOP_LEFT",t[t.FAR_BOTTOM_LEFT=4]="FAR_BOTTOM_LEFT",t[t.FAR_BOTTOM_RIGHT=5]="FAR_BOTTOM_RIGHT",t[t.FAR_TOP_RIGHT=6]="FAR_TOP_RIGHT",t[t.FAR_TOP_LEFT=7]="FAR_TOP_LEFT"}(s||(s={})),s.FAR_BOTTOM_RIGHT,s.NEAR_BOTTOM_RIGHT,s.NEAR_BOTTOM_LEFT,s.FAR_BOTTOM_LEFT,s.NEAR_BOTTOM_LEFT,s.NEAR_BOTTOM_RIGHT,s.NEAR_TOP_RIGHT,s.NEAR_TOP_LEFT,s.FAR_BOTTOM_RIGHT,s.FAR_BOTTOM_LEFT,s.FAR_TOP_LEFT,s.FAR_TOP_RIGHT,s.NEAR_BOTTOM_RIGHT,s.FAR_BOTTOM_RIGHT,s.FAR_TOP_RIGHT,s.NEAR_TOP_RIGHT,s.FAR_BOTTOM_LEFT,s.NEAR_BOTTOM_LEFT,s.NEAR_TOP_LEFT,s.FAR_TOP_LEFT,s.FAR_TOP_LEFT,s.NEAR_TOP_LEFT,s.NEAR_TOP_RIGHT,s.FAR_TOP_RIGHT,function(t){t[t.NUM=6]="NUM"}(o||(o={})),function(t){t[t.NUM=8]="NUM"}(c||(c={})),(0,O.f)(-1,-1,-1,1),(0,O.f)(1,-1,-1,1),(0,O.f)(1,1,-1,1),(0,O.f)(-1,1,-1,1),(0,O.f)(-1,-1,1,1),(0,O.f)(1,-1,1,1),(0,O.f)(1,1,1,1),(0,O.f)(-1,1,1,1),new b.x(S),(0,d.c)(),(0,d.c)(),(0,d.c)(),(0,d.c)(),(0,d.c)(),(0,d.c)(),(0,d.c)(),(0,d.c)();var M,E,A=n(11726);class N{get bounds(){return this._root.bounds}get halfSize(){return this._root.halfSize}get root(){return this._root.node}get maximumObjectsPerNode(){return this._maximumObjectsPerNode}get maximumDepth(){return this._maximumDepth}get objectCount(){return this._objectCount}constructor(t,e){this._objectToBoundingSphere=t,this._maximumObjectsPerNode=10,this._maximumDepth=20,this._degenerateObjects=new Set,this._root=new x,this._objectCount=0,e&&(void 0!==e.maximumObjectsPerNode&&(this._maximumObjectsPerNode=e.maximumObjectsPerNode),void 0!==e.maximumDepth&&(this._maximumDepth=e.maximumDepth))}destroy(){this._degenerateObjects.clear(),x.clearPool(),C[0]=null,V.prune(),J.prune()}add(t,e=t.length){this._objectCount+=e,this._grow(t,e);const n=x.acquire();for(let i=0;i<e;i++){const e=t[i];this._isDegenerate(e)?this._degenerateObjects.add(e):(n.init(this._root),this._add(e,n))}x.release(n)}remove(t,e=null){this._objectCount-=t.length;const n=x.acquire();for(const i of t){const t=(0,h.pC)(e)?e:(0,m.h)(this._objectToBoundingSphere(i),X);L(t[3])?(n.init(this._root),this._remove(i,t,n)):this._degenerateObjects.delete(i)}x.release(n),this._shrink()}update(t,e){if(!L(e[3])&&this._isDegenerate(t))return;const n=function(t){return C[0]=t,C}(t);this.remove(n,e),this.add(n)}forEachAlongRay(t,e,n){const i=(0,y.re)(t,e);this._forEachNode(this._root,(t=>{if(!this._intersectsNode(i,t))return!1;const e=t.node;return e.terminals.forAll((t=>{this._intersectsObject(i,t)&&n(t)})),null!==e.residents&&e.residents.forAll((t=>{this._intersectsObject(i,t)&&n(t)})),!0}))}forEachAlongRayWithVerticalOffset(t,e,n,i){const s=(0,y.re)(t,e);this._forEachNode(this._root,(t=>{if(!this._intersectsNodeWithOffset(s,t,i))return!1;const e=t.node;return e.terminals.forAll((t=>{this._intersectsObjectWithOffset(s,t,i)&&n(t)})),null!==e.residents&&e.residents.forAll((t=>{this._intersectsObjectWithOffset(s,t,i)&&n(t)})),!0}))}forEach(t){this._forEachNode(this._root,(e=>{const n=e.node;return n.terminals.forAll(t),null!==n.residents&&n.residents.forAll(t),!0})),this._degenerateObjects.forEach(t)}forEachDegenerateObject(t){this._degenerateObjects.forEach(t)}findClosest(t,e,n,i=(()=>!0),s=1/0){let r=1/0,o=1/0,c=null;const a=I(t,e),h=a=>{if(--s,!i(a))return;const h=this._objectToBoundingSphere(a);if(!R(n,h))return;const u=P(t,e,(0,m.g)(h)),f=u-h[3],l=u+h[3];f<r&&(r=f,o=l,c=a)};return this._forEachNodeDepthOrdered(this._root,(i=>{if(s<=0||!R(n,i.bounds))return!1;if((0,l.g)(H,a,i.halfSize),(0,l.a)(H,H,i.bounds),P(t,e,H)>o)return!1;const r=i.node;return r.terminals.forAll((t=>h(t))),null!==r.residents&&r.residents.forAll((t=>h(t))),!0}),t,e),c}forEachInDepthRange(t,e,n,i,s,r,o){let c=-1/0,a=1/0;const h={setRange:t=>{n===N.DepthOrder.FRONT_TO_BACK?(c=Math.max(c,t.near),a=Math.min(a,t.far)):(c=Math.max(c,-t.far),a=Math.min(a,-t.near))}};h.setRange(i);const u=P(e,n,t),f=I(e,n),d=I(e,-n),_=t=>{if(!o(t))return;const i=this._objectToBoundingSphere(t),f=(0,m.g)(i),l=P(e,n,f)-u,d=l-i[3],_=l+i[3];d>a||_<c||!R(r,i)||s(t,h)};this._forEachNodeDepthOrdered(this._root,(t=>{if(!R(r,t.bounds))return!1;if((0,l.g)(H,f,t.halfSize),(0,l.a)(H,H,t.bounds),P(e,n,H)-u>a)return!1;if((0,l.g)(H,d,t.halfSize),(0,l.a)(H,H,t.bounds),P(e,n,H)-u<c)return!1;const i=t.node;return i.terminals.forAll((t=>_(t))),null!==i.residents&&i.residents.forAll((t=>_(t))),!0}),e,n)}forEachNode(t){this._forEachNode(this._root,(e=>t(e.node,e.bounds,e.halfSize)))}forEachNeighbor(t,e){const n=(0,m.a)(e),i=(0,m.g)(e),s=e=>{const s=this._objectToBoundingSphere(e),r=(0,m.a)(s),o=n+r;return!((0,l.d)((0,m.g)(s),i)-o*o<=0)||t(e)};let r=!0;const o=t=>{r&&(r=s(t))};this._forEachNode(this._root,(t=>{const e=(0,m.a)(t.bounds),s=n+e;if((0,l.d)((0,m.g)(t.bounds),i)-s*s>0)return!1;const c=t.node;return c.terminals.forAll(o),r&&null!==c.residents&&c.residents.forAll(o),r})),r&&this.forEachDegenerateObject(o)}_intersectsNode(t,e){return v(e.bounds,2*-e.halfSize,W),v(e.bounds,2*e.halfSize,U),(0,A.yK)(t.origin,t.direction,W,U)}_intersectsNodeWithOffset(t,e,n){return v(e.bounds,2*-e.halfSize,W),v(e.bounds,2*e.halfSize,U),n.applyToMinMax(W,U),(0,A.yK)(t.origin,t.direction,W,U)}_intersectsObject(t,e){const n=this._objectToBoundingSphere(e);return!(n[3]>0)||(0,m.j)(n,t)}_intersectsObjectWithOffset(t,e,n){const i=this._objectToBoundingSphere(e);return!(i[3]>0)||(0,m.j)(n.applyToBoundingSphere(i),t)}_forEachNode(t,e){let n=x.acquire().init(t);const i=[n];for(;0!==i.length;){if(n=i.pop(),e(n)&&!n.isLeaf())for(let t=0;t<n.node.children.length;t++)n.node.children[t]&&i.push(x.acquire().init(n).advance(t));x.release(n)}}_forEachNodeDepthOrdered(t,e,n,i=N.DepthOrder.FRONT_TO_BACK){let s=x.acquire().init(t);const r=[s];for(function(t,e,n){if(!J.length)for(let t=0;t<8;++t)J.push({index:0,distance:0});for(let n=0;n<8;++n){const i=B[n];J.data[n].index=n,J.data[n].distance=P(t,e,i)}J.sort(((t,e)=>t.distance-e.distance));for(let t=0;t<8;++t)n[t]=J.data[t].index}(n,i,K);0!==r.length;){if(s=r.pop(),e(s)&&!s.isLeaf())for(let t=7;t>=0;--t){const e=K[t];s.node.children[e]&&r.push(x.acquire().init(s).advance(e))}x.release(s)}}_remove(t,e,n){V.clear();const i=n.advanceTo(e,((t,e)=>{V.push(t.node),V.push(e)}))?n.node.terminals:n.node.residents;if(i.removeUnordered(t),0===i.length)for(let t=V.length-2;t>=0;t-=2){const e=V.data[t],n=V.data[t+1];if(!this._purge(e,n))break}}_nodeIsEmpty(t){if(0!==t.terminals.length)return!1;if(null!==t.residents)return 0===t.residents.length;for(let e=0;e<t.children.length;e++)if(t.children[e])return!1;return!0}_purge(t,e){return e>=0&&(t.children[e]=null),!!this._nodeIsEmpty(t)&&(null===t.residents&&(t.residents=new T.Z({shrink:!0})),!0)}_add(t,e){e.advanceTo(this._objectToBoundingSphere(t))?e.node.terminals.push(t):(e.node.residents.push(t),e.node.residents.length>this._maximumObjectsPerNode&&e.depth<this._maximumDepth&&this._split(e))}_split(t){const e=t.node.residents;t.node.residents=null;for(let n=0;n<e.length;n++){const i=x.acquire().init(t);this._add(e.getItemAt(n),i),x.release(i)}}_grow(t,e){if(0!==e&&(w(t,e,(t=>this._objectToBoundingSphere(t)),k),L(k[3])&&!this._fitsInsideTree(k)))if(this._nodeIsEmpty(this._root.node))(0,m.h)(k,this._root.bounds),this._root.halfSize=1.25*this._root.bounds[3],this._root.updateBoundsRadiusFromHalfSize();else{const t=this._rootBoundsForRootAsSubNode(k);this._placingRootViolatesMaxDepth(t)?this._rebuildTree(k,t):this._growRootAsSubNode(t),x.release(t)}}_rebuildTree(t,e){(0,l.c)(D,e.bounds),D[3]=e.halfSize,w([t,D],2,(t=>t),q);const n=x.acquire().init(this._root);this._root.initFrom(null,q,q[3]),this._root.increaseHalfSize(1.25),this._forEachNode(n,(t=>(this.add(t.node.terminals.data,t.node.terminals.length),null!==t.node.residents&&this.add(t.node.residents.data,t.node.residents.length),!0))),x.release(n)}_placingRootViolatesMaxDepth(t){const e=Math.log(t.halfSize/this._root.halfSize)*Math.LOG2E;let n=0;return this._forEachNode(this._root,(t=>(n=Math.max(n,t.depth),n+e<=this._maximumDepth))),n+e>this._maximumDepth}_rootBoundsForRootAsSubNode(t){const e=t[3],n=t;let i=-1/0;const s=this._root.bounds,r=this._root.halfSize;for(let t=0;t<3;t++){const o=s[t]-r-(n[t]-e),c=n[t]+e-(s[t]+r),a=Math.max(0,Math.ceil(o/(2*r))),h=Math.max(0,Math.ceil(c/(2*r)))+1,u=2**Math.ceil(Math.log(a+h)*Math.LOG2E);i=Math.max(i,u),Y[t].min=a,Y[t].max=h}for(let t=0;t<3;t++){let e=Y[t].min,n=Y[t].max;const o=(i-(e+n))/2;e+=Math.ceil(o),n+=Math.floor(o);const c=s[t]-r-e*r*2;G[t]=c+(n+e)*r}const o=i*r;return G[3]=o*Z,x.acquire().initFrom(null,G,o,0)}_growRootAsSubNode(t){const e=this._root.node;(0,l.c)(k,this._root.bounds),k[3]=this._root.halfSize,this._root.init(t),t.advanceTo(k,null,!0),t.node.children=e.children,t.node.residents=e.residents,t.node.terminals=e.terminals}_shrink(){for(;;){const t=this._findShrinkIndex();if(-1===t)break;this._root.advance(t),this._root.depth=0}}_findShrinkIndex(){if(0!==this._root.node.terminals.length||this._root.isLeaf())return-1;let t=null;const e=this._root.node.children;let n=0,i=0;for(;i<e.length&&null==t;)n=i++,t=e[n];for(;i<e.length;)if(e[i++])return-1;return n}_isDegenerate(t){return!L(this._objectToBoundingSphere(t)[3])}_fitsInsideTree(t){const e=this._root.bounds,n=this._root.halfSize;return t[3]<=n&&t[0]>=e[0]-n&&t[0]<=e[0]+n&&t[1]>=e[1]-n&&t[1]<=e[1]+n&&t[2]>=e[2]-n&&t[2]<=e[2]+n}}class x{constructor(){this.bounds=(0,m.c)(),this.halfSize=0,this.initFrom(null,null,0,0)}init(t){return this.initFrom(t.node,t.bounds,t.halfSize,t.depth)}initFrom(t,e,n,i=this.depth){return this.node=(0,h.pC)(t)?t:x.createEmptyNode(),(0,h.pC)(e)&&(0,m.h)(e,this.bounds),this.halfSize=n,this.depth=i,this}increaseHalfSize(t){this.halfSize*=t,this.updateBoundsRadiusFromHalfSize()}updateBoundsRadiusFromHalfSize(){this.bounds[3]=this.halfSize*Z}advance(t){let e=this.node.children[t];e||(e=x.createEmptyNode(),this.node.children[t]=e),this.node=e,this.halfSize/=2,this.depth++;const n=B[t];return this.bounds[0]+=n[0]*this.halfSize,this.bounds[1]+=n[1]*this.halfSize,this.bounds[2]+=n[2]*this.halfSize,this.updateBoundsRadiusFromHalfSize(),this}advanceTo(t,e,n=!1){for(;;){if(this.isTerminalFor(t))return e&&e(this,-1),!0;if(this.isLeaf()){if(!n)return e&&e(this,-1),!1;this.node.residents=null}const i=this._childIndex(t);e&&e(this,i),this.advance(i)}}isLeaf(){return null!=this.node.residents}isTerminalFor(t){return t[3]>this.halfSize/2}_childIndex(t){const e=this.bounds;return(e[0]<t[0]?1:0)+(e[1]<t[1]?2:0)+(e[2]<t[2]?4:0)}static createEmptyNode(){return{children:[null,null,null,null,null,null,null,null],terminals:new T.Z({shrink:!0}),residents:new T.Z({shrink:!0})}}static acquire(){return x._pool.acquire()}static release(t){x._pool.release(t)}static clearPool(){x._pool.prune()}}function z(t,e){t[0]=Math.min(t[0],e[0]-e[3]),t[1]=Math.min(t[1],e[1]-e[3]),t[2]=Math.min(t[2],e[2]-e[3])}function F(t,e){t[0]=Math.max(t[0],e[0]+e[3]),t[1]=Math.max(t[1],e[1]+e[3]),t[2]=Math.max(t[2],e[2]+e[3])}function v(t,e,n){n[0]=t[0]+e,n[1]=t[1]+e,n[2]=t[2]+e}function w(t,e,n,i){if(1===e){const e=n(t[0]);(0,m.h)(e,i)}else{W[0]=1/0,W[1]=1/0,W[2]=1/0,U[0]=-1/0,U[1]=-1/0,U[2]=-1/0;for(let i=0;i<e;i++){const e=n(t[i]);L(e[3])&&(z(W,e),F(U,e))}(0,l.h)(i,W,U,.5),i[3]=Math.max(U[0]-W[0],U[1]-W[1],U[2]-W[2])/2}}function I(t,e){let n,i=1/0;for(let s=0;s<8;++s){const r=P(t,e,j[s]);r<i&&(i=r,n=j[s])}return n}function P(t,e,n){return e*(t[0]*n[0]+t[1]*n[1]+t[2]*n[2])}function L(t){return!isNaN(t)&&t!==-1/0&&t!==1/0&&t>0}x._pool=new p.Z(x),M=N||(N={}),(E=M.DepthOrder||(M.DepthOrder={}))[E.FRONT_TO_BACK=1]="FRONT_TO_BACK",E[E.BACK_TO_FRONT=-1]="BACK_TO_FRONT";const B=[(0,d.f)(-1,-1,-1),(0,d.f)(1,-1,-1),(0,d.f)(-1,1,-1),(0,d.f)(1,1,-1),(0,d.f)(-1,-1,1),(0,d.f)(1,-1,1),(0,d.f)(-1,1,1),(0,d.f)(1,1,1)],j=[(0,d.f)(-1,-1,-1),(0,d.f)(-1,-1,1),(0,d.f)(-1,1,-1),(0,d.f)(-1,1,1),(0,d.f)(1,-1,-1),(0,d.f)(1,-1,1),(0,d.f)(1,1,-1),(0,d.f)(1,1,1)],Z=Math.sqrt(3),C=[null],G=(0,m.c)(),H=(0,d.c)(),W=(0,d.c)(),U=(0,d.c)(),V=new T.Z,X=(0,m.c)(),k=(0,m.c)(),D=(0,m.c)(),q=(0,m.c)(),Y=[{min:0,max:0},{min:0,max:0},{min:0,max:0}],J=new T.Z,K=[0,0,0,0,0,0,0,0],$=N;var Q=n(212);function tt(t,e,n){const i=(0,m.c)(),s=(0,m.g)(i);return(0,l.z)(s,s,t,.5),(0,l.z)(s,s,e,.5),i[3]=(0,l.i)(s,t),(0,l.a)(s,s,n),i}let et=class{constructor(){this._idToComponent=new Map,this._components=new $((t=>t.bounds)),this._edges=new $((t=>t.bounds)),this._tmpLineSegment=(0,_.Ue)(),this._tmpP1=(0,d.c)(),this._tmpP2=(0,d.c)(),this._tmpP3=(0,d.c)(),this.remoteClient=null}async fetchCandidates(t,e){await Promise.resolve(),(0,u.k_)(e),await this._ensureEdgeLocations(t,e);const n=[];return this._edges.forEachNeighbor((e=>(this._addCandidates(t,e,n),n.length<1e3)),t.bounds),{result:{candidates:n}}}async _ensureEdgeLocations(t,e){const n=[];if(this._components.forEachNeighbor((t=>{if((0,h.Wi)(t.info)){const{id:e,uid:i}=t;n.push({id:e,uid:i})}return!0}),t.bounds),!n.length)return;const i={components:n},s=await this.remoteClient.invoke("fetchAllEdgeLocations",i,(0,h.Pt)(e,{}));for(const t of s.components)this._setFetchEdgeLocations(t)}async add(t){const e=new it(t.id,t.bounds);return this._idToComponent.set(e.id,e),this._components.add([e]),{result:{}}}async remove(t){const e=this._idToComponent.get(t.id);if(e){const t=[];this._edges.forEachNeighbor((n=>(n.component===e&&t.push(n),!0)),e.bounds),this._edges.remove(t),this._components.remove([e]),this._idToComponent.delete(e.id)}return{result:{}}}_setFetchEdgeLocations(t){const e=this._idToComponent.get(t.id);if((0,h.Wi)(e)||t.uid!==e.uid)return;const n=Q.n_.createView(t.locations),i=new Array(n.count),s=(0,d.c)(),r=(0,d.c)();for(let o=0;o<n.count;o++){n.position0.getVec(o,s),n.position1.getVec(o,r);const c=tt(s,r,t.origin),a=new st(e,o,c);i[o]=a}this._edges.add(i);const{objectIds:o,origin:c}=t;e.info={locations:n,objectIds:o,origin:c}}_addCandidates(t,e,n){const{info:i}=e.component,{origin:s,objectIds:r}=i,o=i.locations,c=o.position0.getVec(e.index,this._tmpP1),a=o.position1.getVec(e.index,this._tmpP2);(0,l.a)(c,c,s),(0,l.a)(a,a,s);const h=r[o.componentIndex.get(e.index)];this._addEdgeCandidate(t,h,c,a,n),this._addVertexCandidate(t,h,c,n),this._addVertexCandidate(t,h,a,n)}_addEdgeCandidate(t,e,n,i,s){if(!(t.types&g.r.EDGE))return;const r=(0,m.g)(t.bounds),o=(0,_.zk)(n,i,this._tmpLineSegment),c=(0,_.nF)(o,r,this._tmpP3);(0,m.m)(t.bounds,c)&&s.push({type:"edge",objectId:e,target:(0,d.a)(c),distance:(0,l.i)(r,c),start:(0,d.a)(n),end:(0,d.a)(i)})}_addVertexCandidate(t,e,n,i){if(!(t.types&g.r.VERTEX))return;const s=(0,m.g)(t.bounds);(0,m.m)(t.bounds,n)&&i.push({type:"vertex",objectId:e,target:(0,d.a)(n),distance:(0,l.i)(s,n)})}};et=(0,a._)([(0,f.j)("esri.views.interactive.snapping.featureSources.sceneLayerSource.SceneLayerSnappingSourceWorker")],et);const nt=et;class it{constructor(t,e){this.id=t,this.bounds=e,this.info=null,this.uid=++it.uid}}it.uid=0;class st{constructor(t,e,n){this.component=t,this.index=e,this.bounds=n}}}}]);