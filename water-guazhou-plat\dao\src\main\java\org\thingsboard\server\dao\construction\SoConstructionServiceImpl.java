package org.thingsboard.server.dao.construction;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.dao.construction.project.SoDeviceItemService;
import org.thingsboard.server.dao.construction.project.SoProjectOperateRecordService;
import org.thingsboard.server.dao.model.sql.smartOperation.ConstructionWorkflow;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstruction;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionCompositeProject;
import org.thingsboard.server.dao.model.sql.smartOperation.device.SoDeviceItem;
import org.thingsboard.server.dao.sql.smartOperation.construction.SoConstructionMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionSaveRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoDeviceItemPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoDeviceItemSaveRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoProjectOperateRecordSaveRequest;

import java.util.List;

import static org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemScope.SO_CONSTRUCTION;

@Service
public class SoConstructionServiceImpl implements SoConstructionService {
    @Autowired
    private SoConstructionMapper mapper;

    @Autowired
    private SoDeviceItemService deviceItemService;

    @Autowired
    private SoProjectOperateRecordService recordService;

    @Override
    public String generateCode(String tenantId) {
        return mapper.generateCode(tenantId);
    }

    @Override
    public IPage<SoConstruction> findAllConditional(SoConstructionPageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    @Transactional
    public SoConstruction save(SoConstructionSaveRequest entity) {
        return QueryUtil.saveOrUpdateOneByRequest(entity, e -> {
            int count = mapper.insert(e);
            recordService.save(SoProjectOperateRecordSaveRequest.of(entity, mapper.getCodeById(e.getId()), "实施期%s工程已受理", "受理"));
            return count;
        }, mapper::updateFully);
    }

    @Override
    public boolean isCodeExists(String code, String tenantId, String id) {
        return mapper.isCodeExists(code, tenantId, id);
    }

    @Override
    public boolean update(SoConstruction entity) {
        return mapper.update(entity);
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }


    // region 设备项管理
    @Override
    public IPage<SoDeviceItem> getDevices(SoDeviceItemPageRequest request) {
        return deviceItemService.findAllConditional(request);
    }

    @Override
    public List<SoDeviceItem> saveDevice(List<SoDeviceItemSaveRequest> request) {
        for (SoDeviceItemSaveRequest req : request) {
            req.setScope(SO_CONSTRUCTION);
        }
        return deviceItemService.saveAll(request, SO_CONSTRUCTION);
    }

    // endregion
    @Override
    public List<ConstructionWorkflow> completionInfo(String constructionCode, String tenantId) {
        return mapper.completionInfo(constructionCode, tenantId);
    }

    @Override
    public List<String> getAllCodeByProject(String projectCode, String tenantId) {
        return mapper.getAllCodeByProject(projectCode, tenantId);
    }

    @Override
    public SoConstructionCompositeProject getCompositeByConstructionCode(String constructionCode, String tenantId) {
        return mapper.getCompositeByConstructionCode(constructionCode, tenantId);
    }

    @Override
    public boolean canBeDelete(String id) {
        return mapper.canBeDelete(id);
    }

}
