<template>
  <div class="app-container">
    <div class="device-auth-container">
      <!-- 左侧组织树 -->
      <div class="org-tree-container">
        <div class="org-tree-title">水厂选择</div>
        <div class="search-box">
          <el-input v-model="searchOrgName" placeholder="搜索水厂名称" clearable @clear="filterOrgNode"></el-input>
        </div>
        <div class="org-tree-content">
          <el-tree
            ref="orgTree"
            :data="orgTreeData"
            :props="orgTreeProps"
            node-key="id"
            highlight-current
            default-expand-all
            :filter-node-method="filterOrgNode"
            @node-click="handleOrgNodeClick"
          ></el-tree>
        </div>
      </div>

      <!-- 右侧设备列表 -->
      <div class="device-list-container">
        <!-- 搜索表单 -->
        <el-form :model="queryParams" ref="queryForm" :inline="true" class="search-form">
          <el-form-item label="设备名称：">
            <el-input v-model="queryParams.deviceName" placeholder="请输入" clearable></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">查询</el-button>
            <el-button @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <!-- 数据表格 -->
        <el-table v-loading="loading" :data="deviceList" border @expand-change="handleExpandChange">
          <el-table-column type="expand">
            <template #default="props">
              <div v-loading="props.row.loadingUserAuth">
                <el-table :data="props.row.userAuthList || []" border class="sub-table">
                  <el-table-column label="用户名称" prop="userName" align="center"></el-table-column>
                  <el-table-column label="权限类型" align="center">
                    <template #default="scope">
                      <div class="auth-type-tags">
                        <el-tag v-if="scope.row.authType === 1" type="danger" class="ml-5">完全控制</el-tag>
                        <el-tag v-if="scope.row.authType === 2" type="info" class="ml-5">只读访问</el-tag>
                        <el-tag v-if="scope.row.authType === 3" type="warning" class="ml-5">操作后台</el-tag>
                        <el-tag v-if="scope.row.authType === 4" type="success" class="ml-5">参数设置</el-tag>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" align="center" width="120">
                    <template #default="scope">
                      <el-button type="danger" size="mini" @click="handleDeleteUserAuth(props.row, scope.row)">删除</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="设备名称" prop="name" align="center"></el-table-column>
          <el-table-column label="设备归属" prop="typeName" align="center"></el-table-column>
          <el-table-column label="绑定用户" align="center">
            <template #default="scope">
              <span v-if="scope.row.userAuthList && scope.row.userAuthList.length > 0">
                {{ scope.row.userAuthList.map(item => item.userName).join(', ') }}
              </span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column label="管理范围" align="center">
            <template #default="scope">
              <span v-if="scope.row.userAuthList && scope.row.userAuthList.length > 0">
                <el-tag v-if="hasAuthType(scope.row, 1)" type="danger" class="ml-5">完全控制</el-tag>
                <el-tag v-if="hasAuthType(scope.row, 2)" type="info" class="ml-5">只读访问</el-tag>
                <el-tag v-if="hasAuthType(scope.row, 3)" type="warning" class="ml-5">操作后台</el-tag>
                <el-tag v-if="hasAuthType(scope.row, 4)" type="success" class="ml-5">参数设置</el-tag>
              </span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="240">
            <template #default="scope">
              <div style="display: flex; justify-content: center; gap: 10px;">
                <el-button type="primary" size="mini" @click="handleUserBind(scope.row)">用户绑定</el-button>
                <el-button type="success" size="mini" @click="handleAuthManage(scope.row)">权限管理</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="queryParams.page"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="queryParams.size"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          ></el-pagination>
        </div>
      </div>
    </div>

    <!-- 用户绑定对话框 -->
    <el-dialog :title="'用户绑定 - ' + (currentDevice?.name || '')" v-model="userBindDialogVisible" width="600px" append-to-body>
      <el-form ref="userBindForm" :model="userBindForm" label-width="100px">
        <el-form-item label="选择用户" prop="userIds">
          <choose-user-by-role
            :users="selectedUsers"
            @checkUsers="handleUserSelect"
            multiple
          ></choose-user-by-role>
        </el-form-item>
        <el-form-item label="权限类型" prop="authType">
          <div class="auth-type-buttons">
            <el-button
              :type="userBindForm.authType === 1 ? 'danger' : 'default'"
              @click="handleAuthTypeChange(1, 'userBindForm')"
              size="mini"
            >完全控制</el-button>
            <el-button
              :type="userBindForm.authType === 2 ? 'info' : 'default'"
              @click="handleAuthTypeChange(2, 'userBindForm')"
              size="mini"
            >只读访问</el-button>
            <el-button
              :type="userBindForm.authType === 3 ? 'warning' : 'default'"
              @click="handleAuthTypeChange(3, 'userBindForm')"
              size="mini"
            >操作后台</el-button>
            <el-button
              :type="userBindForm.authType === 4 ? 'success' : 'default'"
              @click="handleAuthTypeChange(4, 'userBindForm')"
              size="mini"
            >参数设置</el-button>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="userBindDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitUserBind">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 权限管理对话框 -->
    <el-dialog :title="'权限管理 - ' + (currentDevice?.name || '')" v-model="authManageDialogVisible" width="800px" append-to-body>
      <div v-if="currentDeviceUserAuthList.length === 0" class="empty-data">
        <p>暂无用户权限数据</p>
        <p style="color: #999; font-size: 12px;">API返回的数据结构:</p>
        <pre style="text-align: left; max-height: 200px; overflow: auto; background: #f5f5f5; padding: 10px; border-radius: 4px;">{{ JSON.stringify(apiResponseData, null, 2) }}</pre>
      </div>
      <el-table v-else :data="currentDeviceUserAuthList" border>
        <el-table-column label="用户名称" align="center">
          <template #default="scope">
            {{ scope.row.userName || '未命名用户' }}
            <div v-if="!scope.row.userName" style="color: #999; font-size: 12px;">
              用户ID: {{ scope.row.userId }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="权限类型" align="center">
          <template #default="scope">
            <div class="auth-type-buttons">
              <el-button
                :type="scope.row.authType === 1 ? 'danger' : 'default'"
                @click="handleRowAuthTypeChange(scope.row, 1)"
                size="mini"
              >完全控制</el-button>
              <el-button
                :type="scope.row.authType === 2 ? 'info' : 'default'"
                @click="handleRowAuthTypeChange(scope.row, 2)"
                size="mini"
              >只读访问</el-button>
              <el-button
                :type="scope.row.authType === 3 ? 'warning' : 'default'"
                @click="handleRowAuthTypeChange(scope.row, 3)"
                size="mini"
              >操作后台</el-button>
              <el-button
                :type="scope.row.authType === 4 ? 'success' : 'default'"
                @click="handleRowAuthTypeChange(scope.row, 4)"
                size="mini"
              >参数设置</el-button>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="120">
          <template #default="scope">
            <el-button type="danger" size="mini" @click="handleRemoveUserAuth(scope.$index)">移除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="auth-manage-footer">
        <el-button type="primary" @click="handleAddUserAuth">添加用户</el-button>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="authManageDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitAuthManage">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 添加用户对话框 -->
    <el-dialog title="添加用户" v-model="addUserDialogVisible" width="500px" append-to-body>
      <el-form ref="addUserForm" :model="addUserForm" label-width="100px">
        <el-form-item label="选择用户" prop="userId">
          <choose-user-by-role
            :users="addSelectedUsers"
            @checkUsers="handleAddUserSelect"
            :multiple="false"
          ></choose-user-by-role>
        </el-form-item>
        <el-form-item label="权限类型" prop="authType">
          <div class="auth-type-buttons">
            <el-button
              :type="addUserForm.authType === 1 ? 'danger' : 'default'"
              @click="handleAuthTypeChange(1, 'addUserForm')"
              size="mini"
            >完全控制</el-button>
            <el-button
              :type="addUserForm.authType === 2 ? 'info' : 'default'"
              @click="handleAuthTypeChange(2, 'addUserForm')"
              size="mini"
            >只读访问</el-button>
            <el-button
              :type="addUserForm.authType === 3 ? 'warning' : 'default'"
              @click="handleAuthTypeChange(3, 'addUserForm')"
              size="mini"
            >操作后台</el-button>
            <el-button
              :type="addUserForm.authType === 4 ? 'success' : 'default'"
              @click="handleAuthTypeChange(4, 'addUserForm')"
              size="mini"
            >参数设置</el-button>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="addUserDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitAddUser">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, reactive, onMounted, toRefs, watch, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import ChooseUserByRole from '@/components/chooseUserByRole/index.vue';
import {
  getDeviceUserAuthList,
  getDeviceUserAuthByDeviceId,
  batchSaveDeviceUserAuth,
  deleteDeviceUserAuth,
  getDeviceList,
  getUserList
} from '@/api/deviceAuth';
import { getWaterSupplyTree } from '@/api/company_org';

export default defineComponent({
  name: 'DeviceAuth',
  components: {
    ChooseUserByRole
  },
  setup() {
    const queryForm = ref(null);
    const orgTree = ref(null);

    const state = reactive({
      // 组织树数据
      orgTreeData: [],
      // 组织树配置
      orgTreeProps: {
        label: 'name',
        children: 'children'
      },
      // 搜索组织名称
      searchOrgName: '',
      // 当前选中的组织ID
      currentOrgId: '',
      // 查询参数
      queryParams: {
        page: 1,
        size: 10,
        orgId: '',
        deviceName: '',
        sortBy: ''
      },
      // 加载状态
      loading: false,
      // 设备列表
      deviceList: [],
      // 总记录数
      total: 0,
      // 当前设备
      currentDevice: null,
      // 用户绑定对话框可见性
      userBindDialogVisible: false,
      // 用户绑定表单
      userBindForm: {
        userIds: [],
        authType: 0 // 0表示未选择，1-4表示不同的权限类型
      },
      // 用户选项
      userOptions: [],
      // 已选择的用户（用于用户绑定）
      selectedUsers: [],
      // 已选择的用户（用于添加用户）
      addSelectedUsers: [],
      // 权限管理对话框可见性
      authManageDialogVisible: false,
      // 当前设备用户权限列表
      currentDeviceUserAuthList: [],
      // API返回的原始数据
      apiResponseData: null,
      // 添加用户对话框可见性
      addUserDialogVisible: false,
      // 添加用户表单
      addUserForm: {
        userId: '',
        userName: '',
        authType: 0 // 0表示未选择，1-4表示不同的权限类型
      }
    });

    // 监听搜索组织名称变化
    watch(() => state.searchOrgName, (val) => {
      orgTree.value?.filter(val);
    });

    // 过滤组织节点
    const filterOrgNode = (value, data) => {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    };

    // 获取组织树
    const getOrgTreeData = async () => {
      try {
        // 获取水厂树，深度为1（只查询供水单位，不包括部门）
        const res = await getWaterSupplyTree(1);
        // 正确获取API返回的data字段中的数据
        state.orgTreeData = res.data?.data || [];
      } catch (error) {
        ElMessage.error('获取组织树失败');
      }
    };

    // 获取设备列表
    const getList = async () => {
      state.loading = true;
      try {
        const params = { ...state.queryParams };

        const res = await getDeviceList(params);

        // 获取设备列表数据
        let deviceList = [];

        // 统一处理设备列表接口返回的数据结构
        if (res.data?.data?.data) {
          // 标准分页数据结构
          deviceList = res.data.data.data || [];
          state.total = res.data.data.total || 0;
        } else if (Array.isArray(res.data)) {
          // 直接返回数组的情况
          deviceList = res.data;
          state.total = res.data.length || 0;
        } else if (res.data?.data && Array.isArray(res.data.data)) {
          // 嵌套在data字段中的数组
          deviceList = res.data.data;
          state.total = res.data.data.length || 0;
        } else {
          // 其他情况，尝试从res.data中获取数据
          deviceList = res.data || [];
          state.total = deviceList.length || 0;
        }



        // 过滤设备名称
        if (params.deviceName) {
          deviceList = deviceList.filter(device =>
            device.name && device.name.includes(params.deviceName)
          );
          state.total = deviceList.length;
        }

        // 初始化设备列表，添加用户权限列表相关属性
        state.deviceList = deviceList.map(device => ({
          ...device,
          userAuthList: [],
          loadingUserAuth: false,
          userAuthLoaded: false
        }));
      } catch (error) {

        ElMessage.error('获取设备列表失败');
      } finally {
        state.loading = false;
      }
    };

    // 获取设备用户权限列表
    const getDeviceUserAuth = async (device) => {
      if (device.userAuthLoaded) {
        return;
      }

      device.loadingUserAuth = true;
      try {
        const res = await getDeviceUserAuthByDeviceId(device.id);

        // 保存API返回的原始数据，用于调试
        state.apiResponseData = res;

        // 检查API返回的数据格式
        if (!res.data) {
          device.userAuthList = [];
          return;
        }

        // 确保res.data是一个数组
        let dataArray = res.data;
        if (!Array.isArray(dataArray)) {
          // 尝试从不同的位置获取数组数据
          if (res.data && Array.isArray(res.data.data)) {
            dataArray = res.data.data;
          } else if (res.data && typeof res.data === 'object') {
            // 如果是对象，尝试找到其中的数组属性
            for (const key in res.data) {
              if (Array.isArray(res.data[key])) {
                dataArray = res.data[key];
                break;
              }
            }
          }

          // 如果仍然不是数组，则创建一个空数组
          if (!Array.isArray(dataArray)) {
            dataArray = [];
          }
        }

        // 将后端返回的整数值转换为前端的布尔值
        const userAuthList = dataArray.map(item => {


          // 确保authType是数字
          const authType = typeof item.authType === 'number' ? item.authType : parseInt(item.authType);

          // 尝试查找用户信息
          let userName = item.userName;
          if (!userName) {
            // 如果API返回的userName为空，尝试从用户列表中查找
            const user = state.userOptions.find(user => {
              // 确保ID是字符串类型，避免调用replace方法时出错
              const userId = user.id ? String(user.id) : '';
              const itemUserId = item.userId ? String(item.userId) : '';

              // 尝试不同的匹配方式
              return userId === itemUserId ||
                     userId === itemUserId.replace(/-/g, '') ||
                     itemUserId === userId.replace(/-/g, '');
            });
            if (user) {
              userName = user.firstName || user.email || '未命名用户';
            }
          }

          // 根据authType设置对应的布尔值
          const fullControl = authType === 1;
          const readOnly = authType === 2;
          const operateBackend = authType === 3;
          const paramSetting = authType === 4;

          return {
            ...item,
            authType, // 确保authType是数字
            userName: userName || '未命名用户', // 确保userName不为空
            fullControl,
            readOnly,
            operateBackend,
            paramSetting
          };
        });


        device.userAuthList = userAuthList;
        device.userAuthLoaded = true;
      } catch (error) {

        device.userAuthList = [];
        state.apiResponseData = { error: error.message };
      } finally {
        device.loadingUserAuth = false;
      }
    };

    // 处理展开行事件
    const handleExpandChange = (row, expandedRows) => {
      if (expandedRows.length > 0) {
        getDeviceUserAuth(row);
      }
    };

    // 获取用户列表
    const getUserOptions = async () => {
      try {
        const res = await getUserList({});
        state.userOptions = res.data || [];

      } catch (error) {

        ElMessage.error('获取用户列表失败');
      }
    };

    // 组织节点点击事件
    const handleOrgNodeClick = (data) => {
      state.currentOrgId = data.id;
      state.queryParams.orgId = data.id;
      state.queryParams.page = 1;
      getList();
    };

    // 查询按钮点击事件
    const handleQuery = () => {
      state.queryParams.page = 1;
      getList();
    };

    // 重置按钮点击事件
    const resetQuery = () => {
      queryForm.value?.resetFields();
      state.queryParams.deviceName = '';
      state.queryParams.sortBy = '';
      handleQuery();
    };

    // 每页条数变化事件
    const handleSizeChange = (val: number) => {
      state.queryParams.size = val;
      getList();
    };

    // 当前页变化事件
    const handleCurrentChange = (val: number) => {
      state.queryParams.page = val;
      getList();
    };

    // 用户绑定按钮点击事件
    const handleUserBind = (row) => {
      state.currentDevice = row;
      state.userBindForm = {
        userIds: [],
        authType: 0 // 0表示未选择，1-4表示不同的权限类型
      };
      state.selectedUsers = []; // 清空已选择的用户
      state.userBindDialogVisible = true;
    };

    // 处理用户选择事件（用户绑定）
    const handleUserSelect = (users) => {
      state.userBindForm.userIds = users.map(user => user.id);
      // 保存选择的用户信息，包括用户名
      state.selectedUsers = users.map(user => ({
        id: user.id,
        name: user.firstName || user.email || '未命名用户'
      }));
    };

    // 处理用户选择事件（添加用户）
    const handleAddUserSelect = (users) => {
      if (users && users.length > 0) {
        const user = users[0]; // 单选模式只有一个用户
        state.addUserForm.userId = user.id;
        state.addUserForm.userName = user.firstName || user.email || '未命名用户';
        // 保存选择的用户信息
        state.addSelectedUsers = [{
          id: user.id,
          name: user.firstName || user.email || '未命名用户'
        }];
      }
    };

    // 处理权限类型变化（用于用户绑定和添加用户）
    const handleAuthTypeChange = (type, formName) => {

      // 如果当前类型已经选中，则取消选中
      if (state[formName].authType === type) {
        state[formName].authType = 0;
      } else {
        // 否则选中当前类型
        state[formName].authType = type;
      }

      // 强制更新表单，确保UI更新
      state[formName] = { ...state[formName] };

    };

    // 处理行权限类型变化（用于权限管理）
    const handleRowAuthTypeChange = (row, type) => {

      // 如果当前类型已经选中，则取消选中
      if (row.authType === type) {
        // 使用Vue.set确保响应式更新
        row.authType = 0;
      } else {
        // 否则选中当前类型
        row.authType = type;
      }

      // 强制更新currentDeviceUserAuthList，确保UI更新
      state.currentDeviceUserAuthList = [...state.currentDeviceUserAuthList];
    };

    // 提交用户绑定
    const submitUserBind = async () => {
      if (!state.userBindForm.userIds || state.userBindForm.userIds.length === 0) {
        ElMessage.warning('请选择用户');
        return;
      }

      if (!state.userBindForm.authType || state.userBindForm.authType === 0) {
        ElMessage.warning('请选择权限类型');
        return;
      }

      try {
        const userAuthList = state.userBindForm.userIds.map(userId => {
          // 从保存的用户信息中查找用户名
          const savedUser = state.selectedUsers.find(item => item.id === userId);
          // 如果在保存的用户信息中找不到，则从用户选项中查找
          const user = savedUser || state.userOptions.find(item => item.id === userId);
          return {
            userId,
            userName: user ? (user.name || user.firstName || user.email || '未命名用户') : '未命名用户',
            authType: state.userBindForm.authType
          };
        });

        // 合并现有的用户权限列表
        const existingUserIds = state.currentDevice.userAuthList.map(item => item.userId);
        for (const item of state.currentDevice.userAuthList) {
          if (!userAuthList.some(auth => auth.userId === item.userId)) {
            userAuthList.push(item);
          }
        }

        await batchSaveDeviceUserAuth({
          deviceId: state.currentDevice.id,
          deviceName: state.currentDevice.name,
          deviceSerial: state.currentDevice.serialId,
          userAuthList
        });

        ElMessage.success('用户绑定成功');
        state.userBindDialogVisible = false;
        getList();
      } catch (error) {
        ElMessage.error('用户绑定失败');
      }
    };

    // 权限管理按钮点击事件
    const handleAuthManage = async (row) => {
      state.currentDevice = row;


      // 确保用户列表已加载
      if (state.userOptions.length === 0) {
        await getUserOptions();
      }

      try {
        // 直接调用API获取设备用户权限列表

        const res = await getDeviceUserAuthByDeviceId(row.id);




        // 获取数据数组
        let dataArray = [];

        if (res.data) {
          if (Array.isArray(res.data)) {
            dataArray = res.data;
          } else if (res.data.data && Array.isArray(res.data.data)) {
            dataArray = res.data.data;
          } else if (typeof res.data === 'object') {
            // 如果是对象，尝试找到其中的数组属性
            for (const key in res.data) {
              if (Array.isArray(res.data[key])) {
                dataArray = res.data[key];
                break;
              }
            }
          }
        }

        // 处理用户权限列表
        const userAuthList = dataArray.map(item => {
          // 确保authType是数字
          const authType = typeof item.authType === 'number' ? item.authType : parseInt(item.authType) || 0;

          // 获取用户名
          let userName = item.userName || '';
          if (!userName && item.userId) {
            // 尝试从用户列表中查找
            const user = state.userOptions.find(user => {
              const userId = String(user.id || '');
              const itemUserId = String(item.userId || '');
              return userId === itemUserId ||
                     userId === itemUserId.replace(/-/g, '') ||
                     itemUserId === userId.replace(/-/g, '');
            });
            if (user) {
              userName = user.firstName || user.email || '未命名用户';
            }
          }

          return {
            userId: item.userId,
            userName: userName || '未命名用户',
            authType
          };
        });



        // 直接设置当前设备用户权限列表
        state.currentDeviceUserAuthList = userAuthList;

        // 更新设备的用户权限列表
        row.userAuthList = [...userAuthList];
        row.userAuthLoaded = true;

      } catch (error) {
        state.currentDeviceUserAuthList = [];
        ElMessage.error('获取设备用户权限列表失败');
      }

      state.authManageDialogVisible = true;
    };

    // 添加用户按钮点击事件
    const handleAddUserAuth = () => {
      state.addUserForm = {
        userId: '',
        userName: '',
        authType: 0 // 0表示未选择，1-4表示不同的权限类型
      };
      state.addSelectedUsers = []; // 清空已选择的用户
      state.addUserDialogVisible = true;
    };

    // 判断用户是否已添加
    const isUserAlreadyAdded = (userId) => {
      // 用户ID可能有不同的格式，需要进行兼容处理
      return state.currentDeviceUserAuthList.some(item => {
        // 确保ID是字符串类型，避免调用replace方法时出错
        const itemUserId = item.userId ? String(item.userId) : '';
        const userIdStr = userId ? String(userId) : '';

        // 尝试不同的匹配方式
        return itemUserId === userIdStr ||
               itemUserId === userIdStr.replace(/-/g, '') ||
               userIdStr === itemUserId.replace(/-/g, '');
      });
    };

    // 提交添加用户
    const submitAddUser = () => {
      if (!state.addUserForm.userId) {
        ElMessage.warning('请选择用户');
        return;
      }

      if (!state.addUserForm.authType || state.addUserForm.authType === 0) {
        ElMessage.warning('请选择权限类型');
        return;
      }

      // 从保存的用户信息中查找用户名
      const savedUser = state.addSelectedUsers.length > 0 ? state.addSelectedUsers[0] : null;
      // 如果在保存的用户信息中找不到，则从用户选项中查找
      const user = savedUser || state.userOptions.find(item => item.id === state.addUserForm.userId);

      if (user) {
        state.currentDeviceUserAuthList.push({
          userId: state.addUserForm.userId,
          userName: user.name || user.firstName || user.email || state.addUserForm.userName || '未命名用户',
          authType: state.addUserForm.authType
        });
        state.addUserDialogVisible = false;
      } else {
        // 如果找不到用户信息，但有用户ID，也添加
        state.currentDeviceUserAuthList.push({
          userId: state.addUserForm.userId,
          userName: state.addUserForm.userName || '未命名用户',
          authType: state.addUserForm.authType
        });
        state.addUserDialogVisible = false;
      }
    };

    // 移除用户权限
    const handleRemoveUserAuth = (index) => {
      state.currentDeviceUserAuthList.splice(index, 1);
    };

    // 提交权限管理
    const submitAuthManage = async () => {
      // 检查是否有权限类型为0的用户
      const invalidUsers = state.currentDeviceUserAuthList.filter(item => !item.authType || item.authType === 0);
      if (invalidUsers.length > 0) {
        ElMessage.warning(`有${invalidUsers.length}个用户未选择权限类型，请为所有用户选择权限类型`);
        return;
      }

      try {
        await batchSaveDeviceUserAuth({
          deviceId: state.currentDevice.id,
          deviceName: state.currentDevice.name,
          deviceSerial: state.currentDevice.serialId,
          userAuthList: state.currentDeviceUserAuthList
        });

        ElMessage.success('权限管理成功');
        state.authManageDialogVisible = false;
        getList();
      } catch (error) {
        console.error('权限管理失败', error);
        ElMessage.error('权限管理失败');
      }
    };

    // 删除用户权限
    const handleDeleteUserAuth = (device, userAuth) => {
      ElMessageBox.confirm('确认删除该用户权限吗？', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          // 过滤掉要删除的用户权限
          const userAuthList = device.userAuthList.filter(item => item.userId !== userAuth.userId);

          // 检查是否有权限类型为0的用户
          const invalidUsers = userAuthList.filter(item => !item.authType || item.authType === 0);
          if (invalidUsers.length > 0) {
            ElMessage.warning(`有${invalidUsers.length}个用户未选择权限类型，无法提交`);
            return;
          }

          await batchSaveDeviceUserAuth({
            deviceId: device.id,
            deviceName: device.name,
            deviceSerial: device.serialId,
            userAuthList
          });

          ElMessage.success('删除成功');
          getList();
        } catch (error) {
          console.error('删除失败', error);
          ElMessage.error('删除失败');
        }
      }).catch(() => {});
    };

    // 判断是否有指定权限类型
    const hasAuthType = (device, authType) => {
      if (!device.userAuthList) return false;

      // 根据authType的类型进行不同的判断
      if (typeof authType === 'string') {
        // 如果是字符串，则判断是否有对应的布尔值为true
        return device.userAuthList.some(item => item[authType] === true);
      } else if (typeof authType === 'number') {
        // 如果是数字，则判断是否有对应的authType值
        return device.userAuthList.some(item => item.authType === authType);
      }

      return false;
    };

    onMounted(() => {
      getOrgTreeData();
      getUserOptions();
      getList();
    });

    return {
      queryForm,
      orgTree,
      ...toRefs(state),
      filterOrgNode,
      handleOrgNodeClick,
      handleQuery,
      resetQuery,
      handleSizeChange,
      handleCurrentChange,
      handleUserBind,
      handleUserSelect,
      handleAddUserSelect,
      handleAuthTypeChange,
      handleRowAuthTypeChange,
      submitUserBind,
      handleAuthManage,
      handleAddUserAuth,
      isUserAlreadyAdded,
      submitAddUser,
      handleRemoveUserAuth,
      submitAuthManage,
      handleDeleteUserAuth,
      hasAuthType,
      handleExpandChange
    };
  }
});
</script>

<style scoped>
.app-container {
  padding: 20px;
}
.device-auth-container {
  display: flex;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
.org-tree-container {
  width: 250px;
  border-right: 1px solid #e6e6e6;
  padding: 20px 0;
}
.org-tree-title {
  font-size: 16px;
  font-weight: bold;
  padding: 0 20px 10px;
  border-bottom: 1px solid #e6e6e6;
}
.search-box {
  padding: 10px 20px;
}
.org-tree-content {
  padding: 10px 20px;
  height: calc(100vh - 200px);
  overflow-y: auto;
}
.device-list-container {
  flex: 1;
  padding: 20px;
}
.search-form {
  margin-bottom: 20px;
}
.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
.sub-table {
  margin: 10px 0;
}
.ml-5 {
  margin-left: 5px;
}
.auth-manage-footer {
  margin-top: 20px;
  text-align: right;
}
.auth-type-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: center;
}
.auth-type-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}
.user-checkbox-group, .user-radio-group {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
}

.empty-data {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 20px;
  text-align: center;
}
.empty-data p {
  margin: 10px 0;
}

</style>
