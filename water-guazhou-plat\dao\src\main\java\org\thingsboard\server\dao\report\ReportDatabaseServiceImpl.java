package org.thingsboard.server.dao.report;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.report.Report;
import org.thingsboard.server.dao.model.sql.report.ReportDatabase;
import org.thingsboard.server.dao.model.sql.report.ReportQuery;
import org.thingsboard.server.dao.model.sql.report.ReportTable;
import org.thingsboard.server.dao.sql.report.ReportDatabaseMapper;
import org.thingsboard.server.dao.sql.report.ReportMapper;
import org.thingsboard.server.dao.sql.report.ReportQueryMapper;
import org.thingsboard.server.dao.sql.report.ReportTableMapper;
import org.thingsboard.server.dao.util.DatabaseUtil;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.sql.*;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.*;

/**
 *
 */
@Service
public class ReportDatabaseServiceImpl implements ReportDatabaseService {

    @Autowired
    private ReportDatabaseMapper reportDatabaseMapper;

    @Autowired
    private DatabaseUtil databaseUtil;

    @Autowired
    private ReportQueryMapper reportQueryMapper;

    @Autowired
    private ReportTableMapper reportTableMapper;

    @Autowired
    private ReportMapper reportMapper;

    @Override
    public ReportDatabase save(ReportDatabase reportDatabase) {
        if (StringUtils.isBlank(reportDatabase.getId())) {
            reportDatabase.setCreateTime(new Date());
            reportDatabaseMapper.insert(reportDatabase);
        } else {
            reportDatabaseMapper.updateById(reportDatabase);
        }
        return reportDatabase;
    }


    @Override
    public PageData<ReportDatabase> getList(int page, int size, String name, String tenantId) {
        IPage<ReportDatabase> iPage = new Page<>(page, size);
        IPage<ReportDatabase> result = reportDatabaseMapper.getList(iPage, name, tenantId);
        return new PageData<>(result.getTotal(), result.getRecords());
    }

    @Override
    public void delete(List<String> ids) {
        reportDatabaseMapper.deleteBatchIds(ids);
    }

    @Override
    public boolean testConnect(ReportDatabase reportDatabase) {
        if (StringUtils.isNotBlank(reportDatabase.getId()) && StringUtils.isBlank(reportDatabase.getPassword())) {
            ReportDatabase reportDatabase1 = reportDatabaseMapper.selectById(reportDatabase.getId());
            if (StringUtils.isNotBlank(reportDatabase1.getPassword())) {
                reportDatabase.setPassword(reportDatabase1.getPassword());
            }
        }
        Connection connection = this.getConnection(reportDatabase);
        if (connection != null) {
            databaseUtil.close(connection, null, null);

            return true;
        }

        return false;
    }

    @Override
    public IstarResponse testSql(String databaseId, String sql, JSONObject param) {
        JSONObject result = new JSONObject();
        ReportDatabase reportDatabase = reportDatabaseMapper.selectById(databaseId);
        if (reportDatabase == null) {
            return IstarResponse.error("该数据库不存在");
        }
        Connection connection = this.getConnection(reportDatabase);
        if (connection == null) {
            return IstarResponse.error("数据库连接失败");
        }
        PreparedStatement preparedStatement = null;
        sql = sql.replace("\n", " ");
        try {
            // 查询条件
            Set<String> strings = param.keySet();
            Iterator<String> iterator = strings.iterator();
            String key;
            Object object;
            String searchStr = "";
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String[] skipKeyArr = {"page", "size", "notTotalList", "totalList", "orderByList", "tableName", "headerMap", "filter"};
            List<String> skipKeyList = Arrays.asList(skipKeyArr);
            while (iterator.hasNext()) {
                key = iterator.next();
                if (skipKeyList.contains(key)) {
                    continue;
                }
                object = param.getObject(key, Object.class);
                if (object.getClass().equals(ArrayList.class)) {
                    try {
                        List<Long> paramList = JSONObject.parseObject(JSONObject.toJSONString(object), List.class);
                        if (paramList.size() == 0) {
                            paramList.add(1374312743000L);
                            paramList.add(2952149543000L);
                        }
                        searchStr = searchStr + "and " + key + " between '" + format.format(new Date(paramList.get(0))) + "' and '" + format.format(new Date(paramList.get(1))) + "' ";
                    } catch (Exception e) {
                        List<Long> paramList = new ArrayList<>();
                        paramList.add(1374312743000L);
                        paramList.add(2952149543000L);
                        searchStr = searchStr + "and " + key + " between '" + format.format(new Date(paramList.get(0))) + "' and '" + format.format(new Date(paramList.get(1))) + "'' ";
                    }

                } else if (String.class.equals(object.getClass()) && StringUtils.isNotBlank(String.valueOf(object))) {
                    searchStr = searchStr + "and " + key + " like '%" + object + "%' ";
                } else {
                    searchStr = searchStr + "and " + key + "=" + object + " ";
                }
            }
            if (searchStr.startsWith("and")) {
                searchStr = searchStr.substring(3);
                sql = sql + " where " + searchStr;
            }

            // 排序
            if (param.get("orderByList") != null) {
                List<String> orderByList = param.getObject("orderByList", List.class);
                if (orderByList.size() > 0) {
                    String order = " order by";
                    for (String s : orderByList) {
                        order = order + " " + s + ",";
                    }

                    order = order.substring(0, order.length() - 1);

                    sql = sql + order;
                }
            }

            // 分页参数
            try {
                if (param.containsKey("page")) {
                    int page = param.getInteger("page");
                    int size = param.getInteger("size");
                    sql = sql + " offset " + ((page - 1) * size) + " limit " + size;
                }
            } catch (Exception e) {
                e.printStackTrace();
            }

            preparedStatement = connection.prepareStatement(sql);
            ResultSet resultSet = preparedStatement.executeQuery();
            JSONArray list = new JSONArray();
            ResultSetMetaData metaData = resultSet.getMetaData();
            int columnCount = metaData.getColumnCount();
            JSONObject jsonObject;
            while (resultSet.next()) {
                jsonObject = new JSONObject();
                for (int i = 1; i <= columnCount; i++) {
                    jsonObject.put(metaData.getColumnName(i), resultSet.getObject(i));
                }
                list.add(jsonObject);

            }
            if (param.isEmpty()) {
                return IstarResponse.ok(list);
            }
            result.put("data", list);

            // 记录总数
            if (sql.contains("order")) {
                sql = "select count(*) " + sql.substring(sql.indexOf(" from"), sql.indexOf(" order"));
            } else {
                sql = "select count(*) " + sql.substring(sql.indexOf(" from"), sql.indexOf(" offset"));
            }
            preparedStatement = connection.prepareStatement(sql);
            resultSet = preparedStatement.executeQuery();
            int total = 0;
            try {
                resultSet.next();
                total = resultSet.getInt(1);
            } catch (Exception e) {
                e.printStackTrace();
            }
            result.put("total", total);

            // 合计
            result.put("count", null);
            List<String> totalList = param.getObject("totalList", List.class);
            if (totalList != null && totalList.size() > 0) {
                String prefix = "select";
                String suffix = "";
                sql = sql.substring(sql.indexOf(" from"));

                for (String totals : totalList) {
                    prefix = prefix + " sum(" + totals + ") as " + totals + ",";
                }

                prefix = prefix.substring(0, prefix.length() - 1);

                sql = prefix + sql + suffix;

                preparedStatement = connection.prepareStatement(sql);
                resultSet = preparedStatement.executeQuery();

                metaData = resultSet.getMetaData();
                columnCount = metaData.getColumnCount();
                resultSet.next();
                jsonObject = new JSONObject();
                for (int i = 1; i <= columnCount; i++) {
                    jsonObject.put(metaData.getColumnName(i), resultSet.getObject(i));
                }
                result.put("count", jsonObject);
            }

            result.put("tableName", param.getString("tableName"));
            result.put("headerMap", param.get("headerMap"));

            return IstarResponse.ok(result);
        } catch (SQLException e) {
            e.printStackTrace();
            return IstarResponse.error("sql格式错误：" + e.getMessage());
        }

    }

    @Override
    public IstarResponse testSqlById(String id, JSONObject param) {
        ReportQuery reportQueryC = reportQueryMapper.selectById(id);
        if (reportQueryC == null) {
            return IstarResponse.error("该数据不存在");
        }
        String databaseId = reportQueryC.getDatabaseId();
        String content = reportQueryC.getContent();
        if (!param.isEmpty()) {
            List<String> notTotalList = new ArrayList<>();
            List<String> totalList = new ArrayList<>();
            List<String> orderByList = new ArrayList<>();
            Map headerMap = new LinkedHashMap();
            // 是否合计
            try {
                ReportTable reportTable = reportTableMapper.selectById(reportQueryC.getPid());
                Report report = reportMapper.selectById(reportTable.getPid());
                if (report != null) {
                    param.put("tableName", report.getName());
                }
                JSONObject object = JSONObject.parseObject(reportTable.getContent(), JSONObject.class);
                JSONArray jsonArray = object.getJSONObject("table").getJSONArray("columns");
                for (Object o : jsonArray) {
                    if (((JSONObject) o).getBoolean("total")) {
                        totalList.add(((JSONObject) o).getString("prop"));
                    } else {
                        notTotalList.add(((JSONObject) o).getString("prop"));
                    }
                    headerMap.put(((JSONObject) o).get("prop"), ((JSONObject) o).get("label"));

                    // 排序字段
                    if (StringUtils.isNotBlank(((JSONObject) o).getString("orderBy"))) {
                        orderByList.add(((JSONObject) o).getString("prop") + " " + ((JSONObject) o).getString("orderBy"));
                    }
                }
                param.put("orderByList", orderByList);
                param.put("totalList", totalList);
                param.put("notTotalList", notTotalList);
                param.put("headerMap", headerMap);
            } catch (Exception e) {
                e.printStackTrace();
            }

        }
        return this.testSql(databaseId, content, param);
    }

    public Connection getConnection(ReportDatabase reportDatabase) {
        String password = new String(Base64.getDecoder().decode(reportDatabase.getPassword()));

        Connection connection = databaseUtil.getConnection(reportDatabase.getHost(), reportDatabase.getPort(), reportDatabase.getServer(), reportDatabase.getUsername(), password, DataConstants.REPORT_DATABASE_TYPE.getByValue(reportDatabase.getType()));

        return connection;
    }

}
