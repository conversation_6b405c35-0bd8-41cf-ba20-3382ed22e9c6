package org.thingsboard.server.dao.sql.workOrder;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.request.WorkOrderReminderRequest;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrderReminder;

@Mapper
public interface WorkOrderReminderMapper extends BaseMapper<WorkOrderReminder> {
    IPage<WorkOrderReminder> getList(@Param("param") WorkOrderReminderRequest workOrderReminderRequest);
}
