import s from"./TitleHeader-CBWfLOPA.js";import{d as a,g as l,n as o,q as r,F as i,ax as e,p as d,aw as n,C as p}from"./index-r0dFAfgr.js";const c={class:"title-card__main"},m=a({__name:"TitleCard",props:{title:{},titleWidth:{},size:{}},setup(_){return(t,h)=>(l(),o("div",{class:n(["title-card",t.size])},[r(s,{title:t.title,"title-width":t.titleWidth,size:t.size},{title:i(()=>[e(t.$slots,"title",{},void 0,!0)]),right:i(()=>[e(t.$slots,"title-right",{},void 0,!0)]),_:3},8,["title","title-width","size"]),d("div",c,[e(t.$slots,"default",{},void 0,!0)])],2))}}),v=p(m,[["__scopeId","data-v-d08944d0"]]);export{v as default};
