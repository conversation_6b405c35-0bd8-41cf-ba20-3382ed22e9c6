package org.thingsboard.server.dao.model.sql;

import com.alibaba.fastjson.JSONObject;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class SoStatistic {
    // 分类设备统计
    private JSONObject deviceByTypeStatistic;

    // 分部门用户统计
    private JSONObject departmentStatistic;

    // 设备巡检完成统计
    private JSONObject circuitCompletionStatistic;

    // 工单及时完成统计
    private final JSONObject onTimeWorkOrderCompleteStatistic;

    // 分类工单统计
    private final JSONObject workOrderTypeStatistic;

    public SoStatistic(JSONObject deviceByTypeStatistic, JSONObject departmentStatistic, JSONObject circuitCompletionStatistic, JSONObject onTimeWorkOrderCompleteStatistic, JSONObject workOrderTypeStatistic) {
        this.deviceByTypeStatistic = deviceByTypeStatistic;
        this.departmentStatistic = departmentStatistic;
        this.circuitCompletionStatistic = circuitCompletionStatistic;
        this.onTimeWorkOrderCompleteStatistic = onTimeWorkOrderCompleteStatistic;
        this.workOrderTypeStatistic = workOrderTypeStatistic;
    }

}
