<template>
  <div class="station-detail">
    <div class="righ-header">
      <span class="title">{{ props.stationInfo?.label ?? '添加站点' }}</span>
      <el-button
        v-if="props.stationInfo"
        type="danger"
        size="small"
        @click="handleDelete"
      >
        删除
      </el-button>
    </div>
    <Form v-if="props.stationInfo" ref="refForm" :config="FormConfig">
      <template #fieldSlot="{ config, row }">
        <div
          v-if="config.field === 'location'"
          style="width: 100%; height: 450px"
        >
          <FormMap
            v-model="state.location"
            :row="row"
            :show-input="(config as IFormMap).showInput"
            :disabled="(config as IFormMap).disabled"
            :readonly="config.readonly"
            :handle-inverse-geocodeing="handleInverseGeocodeing"
            @change="handleMapChange"
          ></FormMap>
        </div>
        <div
          v-else-if="config.type === 'table'"
          style="width: 100%; height: 400px"
        >
          <FormTable ref="refTable" :config="config.config"></FormTable>
        </div>
      </template>
    </Form>
    <Form v-else ref="refForm" :config="FormConfig">
      <template #fieldSlot="{ config, row }">
        <div
          v-if="config.field === 'location'"
          style="width: 100%; height: 450px"
        >
          <FormMap
            v-model="state.location"
            :row="row"
            :show-input="(config as IFormMap).showInput"
            :disabled="(config as IFormMap).disabled"
            :readonly="config.readonly"
            :handle-inverse-geocodeing="handleInverseGeocodeing"
            @change="handleMapChange"
          ></FormMap>
        </div>
        <div
          v-else-if="config.type === 'table'"
          style="width: 100%; height: 400px"
        >
          <FormTable ref="refTable" :config="config.config"></FormTable>
        </div>
      </template>
    </Form>
    <DialogForm
      ref="refDialogForm"
      :config="DialogFormConfig_addGroup"
    ></DialogForm>
    <DialogForm
      ref="refVideoDialog"
      :config="VideoDialogFormConfig"
    ></DialogForm>
  </div>
</template>
<script lang="ts" setup>
import { DeleteStation, PostStation } from '@/api/shuiwureports/zhandian';
import { useBusinessStore } from '@/store';
import { SLConfirm, SLMessage } from '@/utils/Message';
import { useAttrTable } from '../hooks';
import { AttrGroupName_none, initStationType } from '../data';
import {
  SuYuan_GetVideos,
  SuYuan_SaveVideo,
  getVideosName,
  SuYuan_DeleteVideo
} from '@/api/video';
import { traverse } from '@/utils/GlobalHelper';

const refForm = ref<IFormIns>();
const refVideoDialog = ref<IDialogFormIns>();
const emit = defineEmits(['deleted', 'submited']);
const props = defineProps<{
  stationInfo?: NormalOption;
  projectId?: string;
}>();
const state = reactive<{
  location: any;
  activeTab: string;
  videoList: any[];
}>({
  location: undefined,
  activeTab: '',
  videoList: []
});
const inputData = ref<any>([
  {
    name: '',
    unit: '',
    type: 'shuzhi'
  }
]);
const attrTable = useAttrTable();

const addVideo = () => {
  VideoDialogFormConfig.defaultValue = { orderNumber: 1 };
  refVideoDialog.value?.openDialog();
};

// 视频绑定
const VideoDialogFormConfig = reactive<IDialogFormConfig>({
  title: '视频绑定',
  dialogWidth: 500,
  group: [
    {
      fields: [
        {
          type: 'select-tree',
          options: useBusinessStore().projectList,
          label: '视频分组',
          field: 'videoGrouping',
          checkStrictly: true,
          rules: [{ required: true, message: '请选择视频分组' }],
          onChange(value) {
            const params = {
              page: 1,
              size: 999
            };
            SuYuan_GetVideos(value, params).then((res) => {
              state.videoList = traverse(res.data || []);
            });
          }
        },
        {
          type: 'select',
          label: '视频',
          field: 'videoList',
          multiple: true,
          options: computed(() => state.videoList) as any,
          rules: [{ required: true, message: '请选择视频' }]
        }
      ]
    }
  ],
  labelPosition: 'right',
  labelWidth: '100px',
  defaultValue: { orderNumber: 1 },
  submit: (params) => {
    SLConfirm('确定提交？', '提示信息').then(() => {
      try {
        const data = params.videoList.map((key: any) => {
          return state.videoList.find((item: any) => item.id === key);
        });
        VideoTableConfig.dataList = [...VideoTableConfig.dataList, ...data];
        SLMessage.success('添加成功');
        refVideoDialog.value?.closeDialog();
      } catch (error) {
        SLMessage.error('添加失败');
        console.log(error);
      }
    });
  }
});

// 摄像头列表
const VideoTableConfig = reactive<ITable>({
  titleRight: [
    {
      style: {
        marginLeft: 'auto'
      },
      items: [
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              type: 'primary',
              text: '绑定视频',
              icon: 'iconfont icon-jia',
              click: () => addVideo()
            }
          ]
        }
      ]
    }
  ],
  height: 'none',
  indexVisible: true,
  columns: [
    { label: '名称', prop: 'name', minWidth: '250px' },
    {
      label: '类型',
      prop: 'videoType',
      formatter: (row) => {}
    }
  ],
  dataList: [],
  operationWidth: 200,
  operations: [
    {
      perm: true,
      text: '取消绑定',
      click: (row) => {
        SLConfirm('确定取消绑定？', '提示信息').then(async () => {
          try {
            VideoTableConfig.dataList = VideoTableConfig.dataList.filter(
              (item) => item.id !== row.id
            );
          } catch (error) {
            console.log(error);
            SLMessage.error('操作失败');
          }
        });
      }
    }
  ],
  pagination: {
    hide: true
  }
});

const FormConfig = reactive<IFormConfig>({
  labelPosition: 'top',
  defaultValue: {},
  group: [
    {
      fieldset: {
        type: 'underline',
        desc: '静态属性'
      },
      fields: [
        {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          type: 'input',
          label: '名称：',
          field: 'name',
          rules: [{ required: true, message: '请输入名称' }]
        },
        {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          type: 'select',
          label: '类型：',
          field: 'type',
          options: initStationType(),
          rules: [{ required: true, message: '请输入类型' }]
        },
        {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          type: 'number',
          min: 0,
          label: '排序',
          field: 'orderNum'
        },
        {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          type: 'select-tree',
          options: computed(() => useBusinessStore().projectList) as any,
          label: '所属区域',
          field: 'projectId'
        },
        {
          xs: 24,
          sm: 12,
          type: 'input',
          label: '地址：',
          field: 'address'
        },
        // {
        //   xs: 24,
        //   sm: 12,
        //   md: 8,
        //   lg: 8,
        //   xl: 8,
        //   type: 'input-number',
        //   label: 'X坐标：',
        //   field: 'x'
        // },
        // {
        //   xs: 24,
        //   sm: 12,
        //   md: 8,
        //   lg: 8,
        //   xl: 8,
        //   type: 'input',
        //   label: 'Y坐标：',
        //   field: 'y'
        // },
        // { type: 'amap', field: 'location', resultType: 'str' },
        {
          type: 'form-map',
          showInput: true,
          field: 'location'
        },
        {
          xs: 24,
          type: 'image',
          label: '图片：',
          field: 'imgs'
        },
        {
          xs: 24,
          sm: 16,
          md: 16,
          lg: 16,
          xl: 16,
          type: 'textarea',
          maxRow: 6,
          minRow: 6,
          label: '备注：',
          field: 'remark'
        }
      ]
    },
    {
      fieldset: {
        desc: '动态属性'
      },
      fields: [
        {
          type: 'tabs',
          label: '',
          field: 'stationAttrInfo_type',
          tabs: computed(
            () =>
              attrTable.attrGroup.group.value.map((item) => {
                return {
                  label: item.type,
                  value: item.type
                };
              }) ?? []
          ) as any,
          closable: true,
          handleTabRemove: async (tagName: any, nextTabName?: any) => {
            if (tagName && tagName !== AttrGroupName_none) {
              await SLConfirm('确定删除？', '提示信息');
            }
            attrTable.attrGroup.removeAttrGroup(tagName);
            // attrGroups.value = attrGroups.value.filter(item => item !== tagName)
            // 当删除的是最后一个标签是，则添加一个替代标签
            if (!attrTable.attrGroup.group.value.length) {
              attrTable.attrGroup.group.value.push({
                type: AttrGroupName_none,
                attrList: []
              });
              nextTabName = AttrGroupName_none;
            }
            if (refForm.value?.dataForm) {
              if (nextTabName) {
                refForm.value.dataForm.stationAttrInfo_type = nextTabName;
              } else {
                refForm.value.dataForm.stationAttrInfo_type =
                  refForm.value?.dataForm?.stationAttrInfo_type ||
                  AttrGroupName_none;
              }
            }
            // 重置属性表数据
            attrTable.TableConfig.value.dataList =
              attrTable.attrGroup.group.value.find(
                (item) =>
                  item.type === refForm.value?.dataForm?.stationAttrInfo_type
              )?.attrList || [];
          },
          handleTabClick(tabObj) {
            const tab = tabObj.paneName;
            handleTabClick(tab);
            // if (state.activeTab) {
            //   // 保存之前点击的tab
            //   const oldGroup = attrTable.attrGroup.group.value.find(item => item.type === state.activeTab)
            //   if (oldGroup) {
            //     oldGroup.attrList = attrTable.TableConfig.value.dataList || []
            //   }
            // }
            // // 把本次点击的tab保存起来，下次点击可以用这个值来查上次上一次点击的tab，目的是用来同步table数据到attrGroup,attrGroup存的数据就是提交的时候对应的stationAttrInfo字段的数据
            // state.activeTab = tab
            // // 点击的是添加标签时
            // if (tab === AttrGroupName_none) {
            //   if (attrTable.attrGroup.group.value.length === 0) {
            //     attrTable.attrGroup.group.value.push({
            //       type: '请添加分组',
            //       attrList: []
            //     })
            //   }
            //   // options.refDialogForm.value?.openDialog()
            // }
            // //  else {
            // //   // 否则刷新table
            // //   attrTable.refreshData(tab)
            // //   // attrTable.TableConfig.value.dataList = attrTable.attrGroup.group.value.find(item => item.type === tab)
            // //   //   ?.attrList || []
            // // }
            // attrTable.refreshData(tab)
          },
          // onChange: (tab: string) => {
          //   debugger
          //   console.log(tab)
          //   if (state.activeTab) {
          //     // 保存之前点击的tab
          //     const oldGroup = attrTable.attrGroup.group.value.find(item => item.type === state.activeTab)
          //     if (oldGroup) {
          //       oldGroup.attrList = attrTable.TableConfig.value.dataList || []
          //     }
          //   }
          //   // 把本次点击的tab保存起来，下次点击可以用这个值来查上次上一次点击的tab，目的是用来同步table数据到attrGroup,attrGroup存的数据就是提交的时候对应的stationAttrInfo字段的数据
          //   state.activeTab = tab
          //   // 点击的是添加标签时
          //   if (tab === AttrGroupName_none) {
          //     if (attrTable.attrGroup.group.value.length === 0) {
          //       attrTable.attrGroup.group.value.push({
          //         type: '请添加分组',
          //         attrList: []
          //       })
          //     }
          //     // options.refDialogForm.value?.openDialog()
          //   }
          //   //  else {
          //   //   // 否则刷新table
          //   //   attrTable.refreshData(tab)
          //   //   // attrTable.TableConfig.value.dataList = attrTable.attrGroup.group.value.find(item => item.type === tab)
          //   //   //   ?.attrList || []
          //   // }
          //   attrTable.refreshData(tab)
          // },
          btns: [
            {
              type: 'primary',
              size: 'small',
              perm: true,
              text: '添加分组',
              click: () => refDialogForm.value?.openDialog()
            },
            {
              type: 'primary',
              size: 'small',
              perm: true,
              text: '添加变量',
              click: () => {
                if (
                  refForm.value?.dataForm?.stationAttrInfo_type ===
                  AttrGroupName_none
                ) {
                  SLMessage.warning('请先添加分组');
                  return;
                }
                attrTable.addAttrRow(
                  refForm.value?.dataForm?.stationAttrInfo_type,
                  refForm.value?.dataForm?.id
                );
              }
            }
          ]
        },
        {
          type: 'table',
          label: '',
          config: attrTable.TableConfig.value
        },
        {
          type: 'tabs',
          label: '',
          field: 'extraInfo',
          tabs: [
            {
              value: 'scada',
              label: '组态配置'
            },
            {
              value: 'formVal',
              label: '填报配置'
            },
            {
              value: '视频',
              label: '视频'
            }
          ]
          // btns: [
          //   {
          //     type: 'primary',
          //     size: 'small',
          //     text: '添加组态',
          //     perm: true,
          //     click: scadaTable.addScadaRow
          //   }
          // ]
        },
        {
          type: 'textarea',
          label: '组态路径',
          field: 'scadaUrl',
          handleHidden: (params, query, config) => {
            config.hidden = params.extraInfo !== 'scada';
          }
          // config: scadaTable.TableConfig.value
        },
        {
          xs: 24,
          label: '',
          type: 'table',
          field: 'users',
          handleHidden: (params, query, config) => {
            config.hidden = params.extraInfo !== 'formVal';
          },
          config: {
            indexVisible: true,
            height: '380px',
            titleRight: [
              {
                style: {
                  marginLeft: 'auto'
                },
                items: [
                  {
                    type: 'btn-group',
                    btns: [
                      {
                        perm: true,
                        type: 'primary',
                        text: '新增',
                        icon: 'iconfont icon-jia',
                        click: () => {
                          inputData.value.push({
                            name: '',
                            unit: '',
                            type: 'shuzhi'
                          });
                        }
                      }
                    ]
                  }
                ]
              }
            ],
            dataList: computed(() => inputData.value) as any,
            columns: [
              {
                label: '名称',
                prop: 'name',
                formItemConfig: { type: 'input' }
              },
              {
                label: '单位',
                prop: 'unit',
                formItemConfig: { type: 'input' }
              },
              {
                label: '类型',
                prop: 'type',
                formItemConfig: {
                  type: 'select',
                  options: [
                    {
                      label: '数值',
                      value: 'shuzhi'
                    },
                    {
                      label: '开关',
                      value: 'kaiguan'
                    }
                  ]
                }
              }
            ],
            operationWidth: 100,
            operations: [
              {
                text: '删除',
                isTextBtn: true,
                perm: true,
                type: 'danger',
                iconifyIcon: 'ep:delete'
              }
            ],
            pagination: {
              hide: true
            }
          }
        },
        {
          type: 'table',
          handleHidden: (params, query, config) => {
            config.hidden = params.extraInfo !== '视频';
          },
          config: VideoTableConfig
        }
      ],
      groupBtns: {
        styles: {
          paddingTop: '8px',
          textAlign: 'right'
        },
        btns: [
          {
            perm: true,
            text: '确定',
            loading: (): boolean => !!FormConfig.submitting,
            click: () => {
              refForm.value?.Submit();
            }
            //  async (params: any) => {
            //   try {
            //     // 把当前标签对应的table数据映射到attrGroup中
            //     const group = attrTable.attrGroup.group.value.find(
            //       o => o.type === activeTab
            //     )
            //     if (group) {
            //       group.attrList = attrTable.TableConfig.value.dataList || []
            //     }
            //     const submitParams = {
            //       ...(params || {}),
            //       stationAttrInfo: [...attrTable.attrGroup.group.value],
            //       //  attrTable.TableConfig.value.dataList || [],
            //       additionalInfo: '',
            //       orderNum: 999,
            //       createTime: new Date(),
            //       info: {}
            //     }
            //     const res = await PostStation(submitParams)
            //     if (res.data) {
            //       SLMessage.success('提交成功')
            //       options.submitSuccessCallBack?.()
            //     }
            //   } catch (error) {
            //     //
            //   }
            // }
          }
        ]
      }
    }
  ],
  submit: async (params) => {
    FormConfig.submitting = true;

    try {
      const group = attrTable.attrGroup.group.value.find(
        (o) => o.type === state.activeTab
      );
      if (group) {
        group.attrList = attrTable.TableConfig.value.dataList || [];
      }
      const submitParams = {
        orderNum: 999,
        ...(params || {}),
        id: props.stationInfo?.value,
        projectId: params.projectId ?? props.projectId,
        location: state.location?.join(','),
        stationAttrInfo: [...attrTable.attrGroup.group.value],
        videoList: VideoTableConfig.dataList
          .map((item) => {
            return item.id;
          })
          .join(','),
        //  attrTable.TableConfig.value.dataList || [],
        additionalInfo: '',
        createTime: new Date(),
        info: {}
      };
      const res = await PostStation(submitParams);
      if (res.data) {
        SLMessage.success('提交成功');
        emit('submited');
        resetForm();
      }
    } catch (error) {
      SLMessage.error('保存失败');
    }
    FormConfig.submitting = false;
  }
});

const handleMapChange = (val) => {
  if (!refForm.value) return;
  state.location = val;
};
const handleInverseGeocodeing = (res, row) => {
  row.address = res.data.result.formatted_address || '';
};
const resetForm = async () => {
  if (!refForm.value) return;
  attrTable.refreshTableColumns(props.projectId);
  await attrTable.attrGroup.initAttrGroupData(props.stationInfo?.value);
  const group = attrTable.attrGroup.group.value?.[0]?.type;
  // 单独提到slot逻辑中，这段无用了
  // const location = defaultParams?.location?.split(',')
  //   || window.SITE_CONFIG.GIS_CONFIG.gisDefaultCenter || [116, 29]
  // location.map(item => parseInt(item) || item)

  FormConfig.defaultValue = {
    orderNum: 999,
    ...(props.stationInfo?.data || {}),
    projectId: props.stationInfo?.data?.projectId ?? props.projectId,
    stationAttrInfo_type: group || AttrGroupName_none,
    extraInfo: 'scada'
  };
  refForm.value?.resetForm();
  // attrTable.refreshData(group)
  handleTabClick(group);
  // scadaTable.refreshData();
};

const handleDelete = () => {
  SLConfirm('确定删除？', '提示信息')
    .then(async () => {
      try {
        const stationId = props.stationInfo?.value;
        if (!stationId) {
          SLMessage.warning('请选择一个站点');
          return;
        }
        const res = await DeleteStation([stationId]);
        if (res.data?.length) {
          SLMessage.success('删除成功');
          emit('deleted');
          resetForm();
        } else {
          SLMessage.error('删除失败');
        }
      } catch (error) {
        SLMessage.error('删除失败');
      }
    })
    .catch(() => {
      //
    });
};

const refDialogForm = ref<IDialogFormIns>();
const DialogFormConfig_addGroup = reactive<IDialogFormConfig>({
  title: '请输入分组名称',
  dialogWidth: 450,
  group: [
    {
      fields: [{ field: 'name', label: '', type: 'input' }]
    }
  ],
  labelPosition: 'top',
  defaultValue: {
    stationAttrInfo_type: AttrGroupName_none
  },
  cancel: true,
  submit: (params: any) => {
    const groups = attrTable.attrGroup.group;
    const group = groups.value.find((item) => item.type === params.name);
    if (group) {
      SLMessage.warning('请不要重复添加相同的分组');
      return;
    }
    // 将新分组名称添加到分组数组中
    groups.value = groups.value.filter(
      (item) => item.type !== AttrGroupName_none
    );
    groups.value.push({
      type: params.name,
      attrList: []
    });
    // 修改tab会触发相应的onChange函数从而更新table数据
    refForm.value &&
      (refForm.value.dataForm.stationAttrInfo_type = params.name);
    handleTabClick(params.name);
    // 重新表单结构
    // stationForm.initDynamicAttrConfig()
    // stationForm.resetForm({
    //   ...(refForm.value?.dataForm
    //     || stationForm.FormConfig.value.defaultValue
    //     || {}),
    //   stationAttrInfo_type: params.name
    // })
    // 重置属性表数据
    // stationForm.attrTable.refreshData(params.name)
    // 关闭弹窗
    refDialogForm.value?.closeDialog();
  }
});
const handleTabClick = (tab: string) => {
  if (state.activeTab) {
    // 保存之前点击的tab
    const oldGroup = attrTable.attrGroup.group.value.find(
      (item) => item.type === state.activeTab
    );
    if (oldGroup) {
      oldGroup.attrList = attrTable.TableConfig.value.dataList || [];
    }
  }
  // 把本次点击的tab保存起来，下次点击可以用这个值来查上次上一次点击的tab，目的是用来同步table数据到attrGroup,attrGroup存的数据就是提交的时候对应的stationAttrInfo字段的数据
  state.activeTab = tab;
  // 点击的是添加标签时
  if (tab === AttrGroupName_none) {
    if (attrTable.attrGroup.group.value.length === 0) {
      attrTable.attrGroup.group.value.push({
        type: '请添加分组',
        attrList: []
      });
    }
    // options.refDialogForm.value?.openDialog()
  }
  //  else {
  //   // 否则刷新table
  //   attrTable.refreshData(tab)
  //   // attrTable.TableConfig.value.dataList = attrTable.attrGroup.group.value.find(item => item.type === tab)
  //   //   ?.attrList || []
  // }
  attrTable.refreshData(tab);
};
watch(
  () => props.stationInfo,
  () => {
    attrTable.TableConfig.value.dataList = [];
    attrTable.attrGroup.group.value = [];
    state.activeTab = AttrGroupName_none;
    state.location = props.stationInfo?.data?.location
      ?.split(',')
      ?.map((item) => parseFloat(item) || item) ||
      window.SITE_CONFIG.GIS_CONFIG.gisDefaultCenter || [116, 29];
    resetForm();
  }
);
onMounted(() => {
  resetForm();
});
</script>
<style lang="scss">
.simple-input {
  .el-input__wrapper {
    border-radius: 0;
    box-shadow: 0 -1px 0 0 var(--el-input-border-color, var(--el-border-color)) inset;

    &:hover {
      box-shadow: 0 -1px 0 0 var(--el-input-hover-border-color) inset;
    }
  }
}
</style>
<style lang="scss" scoped>
.station-detail {
  width: 100%;
  padding: 20px;
  overflow-y: auto;

  .righ-header {
    width: 100%;
    height: 32px;
    display: flex;
    align-items: center;
  }

  .title {
    margin-right: auto;
    word-break: keep-all;
  }

  .title-right {
  }
}
</style>
