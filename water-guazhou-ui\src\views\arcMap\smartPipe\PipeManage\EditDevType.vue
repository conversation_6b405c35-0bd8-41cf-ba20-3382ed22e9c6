<!-- 修改设备类型 -->
<!-- 批量的把指定图层的数据修改成另一图层数据 -->
<!-- 需要确认字段匹配 -->
<!-- 需要筛选刷新设备 -->
<template>
  <RightDrawerMap
    ref="refMap"
    :title="'修改设备类型'"
    :detail-max-min="true"
    :full-content="true"
    @map-loaded="onMapLoaded"
    @detail-refreshed="state.loading = false"
  >
    <Form
      ref="refForm"
      :config="FormConfig"
    ></Form>
    <template #detail-header>
      <span>修改设备类型查询结果{{ state.curLayerName && ' - ' + state.curLayerName }}</span>
    </template>
    <template #detail-default>
      <div class="detail-wrapper">
        <div class="left">
          <div class="title">
            <div class="title-left">
              <span> 目标设备图层 </span>
              <el-select
                v-model="state.targetLayer"
                size="small"
                style="width: 90px; margin-left: 8px"
                @change="handleDetailLayerChange"
              >
                <el-option
                  v-for="item in state.layerInfos"
                  :key="item.layerid"
                  :value="item.layerid"
                  :label="item.layername"
                >
                </el-option>
              </el-select>
            </div>

            <div class="title-btns">
              <el-button
                v-if="false"
                type="warning"
                size="small"
                :icon="RefreshLeft"
                @click="handleUndo"
              >
                撤回
              </el-button>
              <el-button
                type="success"
                size="small"
                :icon="Refresh"
                :disabled="!TableConfig.dataList.length"
                :loading="state.submitting"
                @click="handleRefreshAttrs"
              >
                刷新
              </el-button>
            </div>
          </div>
          <div class="table-box">
            <FormTable :config="TableConfig"></FormTable>
          </div>
        </div>
        <div class="right">
          <div class="title">
            <span>待刷新要素</span>
            <FormTableColumnFilter
              v-if="refDetailTable?.TableConfig_Detail.columns"
              :show-tooltip="true"
              :columns="refDetailTable.TableConfig_Detail.columns"
            ></FormTableColumnFilter>
          </div>
          <div class="table-box">
            <DetailTable
              ref="refDetailTable"
              @row-click="handleRowClick"
              @refresh-data="startQuery"
            ></DetailTable>
          </div>
        </div>
      </div>
    </template>
  </RightDrawerMap>
</template>
<script lang="ts" setup>
import SketchViewModel from '@arcgis/core/widgets/Sketch/SketchViewModel.js'
import { Refresh, RefreshLeft } from '@element-plus/icons-vue'
import {
  applyEdits,
  generate4548Graphic,
  getGraphicLayer,
  getLayerOids,
  getSubLayerIds,
  refreshPipeLayer,
  setSymbol
} from '@/utils/MapHelper'
import { queryLayerClassName } from '@/api/mapservice'
import { SLConfirm, SLMessage } from '@/utils/Message'
import RightDrawerMap from '../../components/common/RightDrawerMap.vue'
import DetailTable from '../../components/common/DetailTable.vue'
import { GetFieldConfig, GetFieldUniqueValue } from '@/api/mapservice/fieldconfig'
import { PostGisOperateLog } from '@/api/system/gisSetting'
import { EGigLogFunc, EGisLogApp, EGisLogOperateType } from '../../config'

const refForm = ref<IFormIns>()
const refMap = ref<InstanceType<typeof RightDrawerMap>>()
const state = reactive<{
  tabs: any[]
  targetLayer?: number
  loading: boolean
  layerInfos: any[]
  layerIds: any[]
  curType: 'ellipse' | 'rectangle' | 'polygon' | ''
  curLayerName: string
  timer?: any
  uniqueing: boolean
  curFieldNode?: any
  curLayerFields: any[]
  submitting: boolean
}>({
  tabs: [],
  curType: '',
  layerInfos: [],
  layerIds: [],
  loading: false,
  curLayerName: '',
  uniqueing: false,
  curLayerFields: [],
  submitting: false
})
const staticState: {
  view?: __esri.MapView
  sketch?: __esri.SketchViewModel
  graphic?: __esri.Graphic
  graphicsLayer?: __esri.GraphicsLayer
  textLayer?: __esri.GraphicsLayer
  sketchCompHandler?: any
  sketchUpdateHandler?: any
  moveEvent?: any
  clickEvent?: any
  identifyResult?: any
} = {}
const TableConfig = reactive<ITable>({
  dataList: [],
  columns: [
    { width: 100, label: '目标字段', prop: 'alias' },
    {
      label: '选择对应字段',
      prop: 'name',
      formItemConfig: {
        type: 'select',
        readonly: (value): boolean => {
          return value === 'OBJECTID'
        },
        options: computed(
          () => state.curLayerFields.map(item => ({
            label: item.alias,
            value: item.name,
            id: item.name
          })) || []
        ) as any
      }
    }
  ],
  pagination: {
    hide: true
  }
})
const handleDetailLayerChange = async () => {
  const curLayerId = refForm.value?.dataForm?.layerid[0]
  const targetLayerId = state.targetLayer
  if (curLayerId === undefined || targetLayerId === undefined || curLayerId === targetLayerId) {
    TableConfig.dataList = []
    return
  }
  const targetLayerInfo = state.layerInfos.find(item => item.layerid === state.targetLayer)
  if (!targetLayerInfo) return
  const targetLayerName = targetLayerInfo.layername
  const target = await GetFieldConfig(targetLayerName)
  const cur = state.curLayerFields
  const fieldCompare: any = {}
  cur.map(item => {
    fieldCompare[item.name] = item.name
  })
  TableConfig.dataList = target.data?.result?.rows.map(item => {
    return {
      alias: item.alias,
      targetName: item.name,
      name: fieldCompare[item.name]
    }
  }) || []
}
const FormConfig = reactive<IFormConfig>({
  group: [
    {
      fieldset: {
        desc: '绘制工具'
      },
      fields: [
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              title: '绘制多边形',
              disabled: () => state.loading,
              iconifyIcon: 'mdi:shape-polygon-plus',
              click: () => initDraw('polygon')
            },
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              title: '绘制矩形',
              disabled: () => state.loading,
              iconifyIcon: 'ep:crop',
              click: () => initDraw('rectangle')
            },
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              title: '绘制椭圆',
              disabled: () => state.loading,
              iconifyIcon: 'mdi:ellipse-outline',
              click: () => initDraw('circle')
            },
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              title: '清除图形',
              disabled: () => state.loading,
              iconifyIcon: 'ep:delete',
              click: () => clearGraphicsLayer()
            }
          ]
        }
      ]
    },
    {
      id: 'layer',
      fieldset: {
        desc: '选择图层'
      },
      fields: [
        {
          type: 'tree',
          options: [],
          checkStrictly: true,
          showCheckbox: true,
          field: 'layerid',
          nodeKey: 'value',
          handleCheckChange: (data, isChecked) => {
            if (isChecked) {
              refForm.value && (refForm.value.dataForm.layerid = [data.value])
              handleDetailLayerChange()
            }
          }
        }
      ]
    },
    {
      id: 'layer',
      fieldset: {
        desc: '图层字段'
      },
      fields: [
        {
          type: 'list',
          data: [],
          className: 'sql-list-wrapper',
          setData: async (config: IFormList, row) => {
            if (!row.layerid?.length) {
              state.curLayerFields.length = 0
              return
            }
            const layerid = row.layerid[0]
            const layerName = state.layerInfos.find(item => item.layerid === layerid)?.layername
            if (!layerName) return
            const fields = await GetFieldConfig(layerName)
            config.data = fields.data?.result?.rows
            state.curLayerFields = fields.data?.result?.rows || []
            state.curLayerName = state.layerInfos.find(item => item.layerid === row.layerid[0])?.layername
          },
          setDataBy: 'layerid',
          displayField: 'alias',
          valueField: 'name',
          highlightCurrentRow: true,
          nodeClick: node => {
            state.curFieldNode = node
            appendSQL(node.name)
          }
        }
      ]
    },
    {
      id: 'field-construct',
      fieldset: {
        desc: '构建查询语句'
      },
      fields: [
        {
          type: 'btn-group',
          size: 'small',
          style: {
            width: '40%',
            display: 'flex',
            flexWrap: 'wrap'
          },
          className: 'sql-btns-wrapper',
          btns: [
            {
              perm: true,
              text: '=',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('=')
              }
            },
            {
              perm: true,
              text: '模糊',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL("like '%替换此处%'")
              }
            },
            {
              perm: true,
              text: '>',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('>')
              }
            },
            {
              perm: true,
              text: '<',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('<')
              }
            },
            {
              perm: true,
              text: '非',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('<>')
              }
            },
            {
              perm: true,
              text: '并且',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('and')
              }
            },
            {
              perm: true,
              text: '或者',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('or')
              }
            },
            {
              perm: true,
              text: '%',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('%')
              }
            }
          ],
          extraFormItem: [
            {
              type: 'list',
              wrapperStyle: {
                width: '60%',
                height: '144px'
              },
              className: 'sql-list-wrapper',
              field: 'uniqueValue',
              data: [],
              nodeClick: node => {
                appendSQL("'" + node + "'")
              },
              filters: [
                {
                  type: 'btn-group',
                  btns: [
                    {
                      perm: true,
                      text: () => (state.uniqueing ? '正在获取唯一值' : '获取唯一值'),
                      loading: () => state.uniqueing,
                      disabled: () => state.loading,
                      styles: {
                        width: '100%',
                        borderRadius: '0'
                      },
                      click: () => getUniqueValue()
                    }
                  ]
                }
              ]
            }
          ]
        }
      ]
    },
    {
      fieldset: {
        desc: '组合查询条件'
      },
      fields: [
        {
          type: 'textarea',
          field: 'sql',
          placeholder: 'OBJECTID > 0'
        },
        {
          type: 'btn-group',
          itemContainerStyle: {
            marginBottom: '8px'
          },
          btns: [
            {
              perm: true,
              text: '清除组合条件',
              type: 'danger',
              disabled: () => state.loading,
              click: () => clear(),
              styles: {
                width: '100%'
              }
            }
          ]
        }
      ]
    },
    {
      fields: [
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: '查询',
              styles: {
                width: '100%'
              },
              click: () => startQuery()
            },
            {
              perm: true,
              text: '重置',
              type: 'default',
              styles: {
                width: '100%'
              },
              click: () => resetForm()
            }
          ]
        }
      ]
    }
  ],
  labelPosition: 'top',
  gutter: 12,
  defaultValue: {
    length: 1
  }
})
const appendSQL = val => {
  if (!refForm.value) return
  if (!refForm.value?.dataForm) refForm.value.dataForm = {}
  const sql = refForm.value.dataForm.sql || ' '
  refForm.value.dataForm.sql = sql + val + ' '
}

const getUniqueValue = async () => {
  if (!state.curFieldNode) return
  const layerid = refForm.value?.dataForm.layerid
  if (!layerid?.length) {
    SLMessage.warning('请先选择一个图层')
    return
  }
  state.uniqueing = true
  try {
    const res = await GetFieldUniqueValue({
      layerid: layerid[0],
      field_name: state.curFieldNode.name
    })
    const extraFormItem = FormConfig.group.find(item => item.id === 'field-construct')?.fields[0].extraFormItem
    const field = extraFormItem && (extraFormItem[0] as IFormList)
    field && (field.data = res.data.result.rows)
  } catch (error) {
    SLMessage.error('获取唯一值失败')
  }
  state.uniqueing = false
}
const clear = () => {
  refForm.value?.dataForm && (refForm.value.dataForm.sql = '')
}

const resetForm = () => {
  refForm.value?.resetForm()
}
const clearGraphicsLayer = () => {
  staticState.graphicsLayer?.removeAll()
}
const initDraw = (type: 'polygon' | 'point' | 'rectangle' | 'circle') => {
  staticState.sketchCompHandler?.remove()
  staticState.sketchUpdateHandler?.remove()
  staticState.sketch?.destroy()
  staticState.graphicsLayer?.removeAll()
  staticState.sketch = new SketchViewModel({
    view: staticState.view,
    layer: staticState.graphicsLayer,
    polygonSymbol: setSymbol('polygon', {
      color: [255, 0, 0, 0.3],
      outlineColor: [255, 0, 0, 1]
    }) as any,
    polylineSymbol: setSymbol('polyline') as any,
    pointSymbol: setSymbol('point') as any
  })
  staticState.sketch?.create(type)
  staticState.sketchUpdateHandler = staticState.sketch?.on('update', (result: any) => {
    if (result.state === 'complete') {
      console.log(result.graphics[0])

      staticState.graphic = result.graphics[0]
    }
  })
  staticState.sketchCompHandler = staticState.sketch?.on('create', (result: any) => {
    if (result.state === 'complete') {
      console.log(result.graphic)

      staticState.graphic = result.graphic
    }
  })
}
const getLayerInfo = async () => {
  state.layerIds = getSubLayerIds(staticState.view)
  const layerInfo = await queryLayerClassName(state.layerIds)
  state.layerInfos = layerInfo.data?.result?.rows?.filter(item => item.geometrytype === 'esriGeometryPoint') || []
  const field = FormConfig.group.find(item => item.id === 'layer')?.fields[0] as IFormTree
  const points = state.layerInfos.map(item => {
    return {
      label: item.layername,
      value: item.layerid,
      data: item
    }
  })
  field && (field.options = [{ label: '管点类', value: -1, disabled: true, children: points }])
  refForm.value && (refForm.value.dataForm.layerid = [points[0]?.value])
  state.targetLayer = points[1]?.value
}

/** *************详情table*************** */
const refDetailTable = ref<InstanceType<typeof DetailTable>>()
const handleRowClick = row => {
  refDetailTable.value?.extentTo(staticState.view, row.OBJECTID)
}
const startQuery = async () => {
  handleDetailLayerChange()
  refMap.value?.toggleCustomDetail(true)
  const layerIds = refForm.value?.dataForm.layerid || []
  const queryParams = {
    where: refForm.value?.dataForm.sql || '1=1',
    geometry: staticState.graphic?.geometry
  }
  const tabs = await getLayerOids(layerIds, state.layerInfos, queryParams)
  refDetailTable.value?.refreshDetail(
    staticState.view,
    {
      layerid: layerIds[0],
      layername: tabs[0]?.name,
      oids: tabs[0]?.data
    },
    {
      queryParams,
      allAttributes: true
    }
  )
}
const handleUndo = () => {
  //
}
const handleRefreshAttrs = () => {
  const layername = state.curLayerName
  const targetLayerId = state.targetLayer
  const targetLayerName = state.layerInfos.find(item => item.layerid === targetLayerId)?.layername
  SLConfirm('应用到空间数据？', '提示信息')
    .then(async () => {
      const features = refDetailTable.value?.staticState.tabFeatures
      if (!features?.length) return
      const fields = TableConfig.dataList.filter(item => !!(item.name ?? ''))
      const newFeatures = features.map(item => {
        const g = window.SITE_CONFIG.SITENAME === 'qingyang' ? generate4548Graphic(item) : item
        const attributes = {}
        fields.map(o => {
          attributes[o.targetName] = item.attributes[o.name]
        })
        g.attributes = attributes
        return g
      })
      const curLayerId = refForm.value?.dataForm.layerid?.[0]
      if (curLayerId === undefined || targetLayerId === undefined || curLayerId === targetLayerId) {
        return
      }
      state.submitting = true
      try {
        await applyEdits(targetLayerId, {
          addFeatures: newFeatures
        })
        await applyEdits(curLayerId, { deleteFeatures: features })
        PostGisOperateLog({
          optionName: EGigLogFunc.XIUGAISBSS,
          type: EGisLogApp.BASICGIS,
          content: `将OBJECTID为${features
            .map(item => item.attributes.OBJECTID)
            ?.join('、')}的【${layername}】修改为【${targetLayerName}】`,
          optionType: EGisLogOperateType.UPDATE
        }).catch(() => {
          console.log('生成gis操作日志失败')
        })
        startQuery()
        refreshPipeLayer(staticState.view)
      } catch (error) {
        SLMessage.error('更新数据失败！')
      }
      state.submitting = false
    })
    .catch(() => {
      //
    })
}
const onMapLoaded = view => {
  staticState.view = view
  staticState.graphicsLayer = getGraphicLayer(staticState.view, {
    id: 'pipe-brush',
    title: '属性刷'
  })

  staticState.textLayer = getGraphicLayer(staticState.view, {
    id: 'pipe-brush-text',
    title: '属性刷-标注'
  })
  getLayerInfo()
}
onBeforeUnmount(() => {
  staticState.sketchCompHandler?.remove()
  staticState.sketchUpdateHandler?.remove()
  staticState.sketch?.destroy()
  staticState.graphicsLayer?.removeAll()
  staticState.moveEvent?.remove()
  staticState.clickEvent?.remove()
  staticState.textLayer?.removeAll()
})
</script>
<style lang="scss" scoped>
.darkblue {
  .detail-wrapper {
    .title {
      background-color: rgba(21, 45, 68, 1);
    }
  }
}
.detail-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  .left {
    width: 350px;
  }
  .right {
    width: calc(100% - 350px);
    border-left: 1px solid var(--el-border-color);
  }
  .title {
    background-color: #ddd;
    height: 40px;
    font-size: 14px;
    display: flex;
    align-items: center;
    padding: 0 10px;
    justify-content: space-between;
    margin: 0;
    word-break: keep-all;
    .title-left {
      display: flex;
      align-items: center;
    }
  }
  .table-box {
    padding: 8px;
    height: calc(100% - 40px);
    width: 100%;
  }
}
</style>
