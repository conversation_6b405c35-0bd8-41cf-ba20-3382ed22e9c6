/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.model;

import com.datastax.driver.core.utils.UUIDs;
import org.apache.commons.lang3.ArrayUtils;
import org.omg.CORBA.PUBLIC_MEMBER;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.kv.Aggregation;

import java.util.UUID;

public class ModelConstants {

    private ModelConstants() {
    }

    public static final UUID NULL_UUID = UUIDs.startOf(0);
    public static final String NULL_UUID_STR = UUIDConverter.fromTimeUUID(NULL_UUID);
    public static final TenantId SYSTEM_TENANT = new TenantId(ModelConstants.NULL_UUID);
    public static final String GATEWAY = "gateWay";
    public static final String CONFIGURATION = "configuration";
    public static final String LAST_UPDATE_TIME = "lastUpdateTime";
    public static final String LAST_CONNECT_TIME = "lastConnectTime";
    public static final String ATTRIBUTE = "info";
    public static final String PROR = "prop";
    public static final String ENERGY_TYPE = "energyType";
    public static final String ENERGY_ATTRIBUTE = "attribute";

    public static final String DEVICE_ALARM = "deviceAlarm";
    public static final String TIME_SHARING = "timeSharing";
    public static final String TIME_SHARING_WEEK = "week";
    public static final String TIME_SHARING_TIME = "time";
    public static final String ENERGY_PRICE = "energyPrice";
    public static final String ALARM_TIME = "alarmTime";
    public static final String ALARM_TYPE_OFFLINE = "offline";
    public static final String ALARM_FORM_EMAIL = "alarmFromEmail";
    public static final String ALARM_FORM_SMS = "alarmFromSms";
    public static final String USER_PHONE = "phone";
    public static final String ALARM_RELEASED = "false";
    public static final Boolean ALARM_RELEASE_NOT = true;
    public static final String ALARM_TYPE_UPPER_LIMIT = "upperLimit";
    public static final String ALARM_TYPE_LOWER_LIMIT = "lowerLimit";
    public static final String ALARM_TYPE_EQUIVALENCE = "equivalence";
    public static final String ALARM_LEVEL_ONE = "一级";
    public static final String ALARM_LEVEL_TWO = "二级";
    public static final String ALARM_LEVEL_THREE = "三级";


    /**
     * common Field
     */
    public static final String NAME = "name";
    public static final String TYPE = "type";
    public static final String UPDATE_TIME = "update_time";
    public static final String CREATE_TIME = "create_time";
    public static final String CREATED_TIME = "created_time";
    public static final String START_TIME = "start_time";
    public static final String END_TIME = "end_time";
    public static final String ADDITIONAL_INFO = "additional_info";
    public static final String IS_DEL = "is_del";
    public static final String CREATOR = "creator";
    public static final String REMARK = "remark";


    /**
     * energy
     */
    public static final String ENERGY = "energy";
    public static final String ENERGY_UNIT = "unit";
    public static final String ENERGY_TABLE_TYPE = "energy_type";
    public static final String ENERGY_NAME = "energy_name";
    public static final String ENERGY_ENABLE = "enable";

    /**
     * Generic constants.
     */
    public static final String ID_PROPERTY = "id";
    public static final String USER_ID_PROPERTY = "user_id";
    public static final String TENANT_ID_PROPERTY = "tenant_id";
    public static final String CUSTOMER_ID_PROPERTY = "customer_id";
    public static final String GATEWAY_ID_PROPERTY = "gateway_id";
    public static final String DEVICE_ID_PROPERTY = "device_id";
    public static final String TITLE_PROPERTY = "title";
    public static final String ALIAS_PROPERTY = "alias";
    public static final String SEARCH_TEXT_PROPERTY = "search_text";
    public static final String ADDITIONAL_INFO_PROPERTY = "additional_info";
    public static final String ENTITY_TYPE_PROPERTY = "entity_type";
    public static final String ENTITY_SEQUENCE="sequence";

    public static final String ENTITY_TYPE_COLUMN = ENTITY_TYPE_PROPERTY;
    public static final String ENTITY_ID_COLUMN = "entity_id";
    public static final String ATTRIBUTE_TYPE_COLUMN = "attribute_type";
    public static final String ATTRIBUTE_KEY_COLUMN = "attribute_key";
    public static final String LAST_UPDATE_TS_COLUMN = "last_update_ts";


    /**
     * Cassandra user constants.
     */
    public static final String USER_COLUMN_FAMILY_NAME = "user";
    public static final String USER_PG_HIBERNATE_COLUMN_FAMILY_NAME = "tb_user";
    public static final String USER_TENANT_ID_PROPERTY = TENANT_ID_PROPERTY;
    public static final String USER_CUSTOMER_ID_PROPERTY = CUSTOMER_ID_PROPERTY;
    public static final String USER_EMAIL_PROPERTY = "email";
    public static final String USER_AUTHORITY_PROPERTY = "authority";
    public static final String USER_FIRST_NAME_PROPERTY = "first_name";
    public static final String USER_LAST_NAME_PROPERTY = "last_name";
    public static final String USER_ADDITIONAL_INFO_PROPERTY = ADDITIONAL_INFO_PROPERTY;

    public static final String USER_BY_EMAIL_COLUMN_FAMILY_NAME = "user_by_email";
    public static final String USER_BY_TENANT_AND_SEARCH_TEXT_COLUMN_FAMILY_NAME = "user_by_tenant_and_search_text";
    public static final String USER_BY_CUSTOMER_AND_SEARCH_TEXT_COLUMN_FAMILY_NAME = "user_by_customer_and_search_text";

    /**
     * Cassandra user_credentials constants.
     */
    public static final String USER_CREDENTIALS_COLUMN_FAMILY_NAME = "user_credentials";
    public static final String USER_CREDENTIALS_USER_ID_PROPERTY = USER_ID_PROPERTY;
    public static final String USER_CREDENTIALS_ENABLED_PROPERTY = "enabled";
    public static final String USER_CREDENTIALS_PASSWORD_PROPERTY = "password"; //NOSONAR, the constant used to identify password column name (not password value itself)
    public static final String USER_CREDENTIALS_ACTIVATE_TOKEN_PROPERTY = "activate_token";
    public static final String USER_CREDENTIALS_RESET_TOKEN_PROPERTY = "reset_token";

    public static final String USER_CREDENTIALS_BY_USER_COLUMN_FAMILY_NAME = "user_credentials_by_user";
    public static final String USER_CREDENTIALS_BY_ACTIVATE_TOKEN_COLUMN_FAMILY_NAME = "user_credentials_by_activate_token";
    public static final String USER_CREDENTIALS_BY_RESET_TOKEN_COLUMN_FAMILY_NAME = "user_credentials_by_reset_token";

    /**
     * Cassandra admin_settings constants.
     */
    public static final String ADMIN_SETTINGS_COLUMN_FAMILY_NAME = "admin_settings";
    public static final String ADMIN_SETTINGS_KEY_PROPERTY = "key";
    public static final String ADMIN_SETTINGS_JSON_VALUE_PROPERTY = "json_value";

    public static final String ADMIN_SETTINGS_BY_KEY_COLUMN_FAMILY_NAME = "admin_settings_by_key";

    /**
     * Cassandra contact constants.
     */
    public static final String COUNTRY_PROPERTY = "country";
    public static final String STATE_PROPERTY = "state";
    public static final String CITY_PROPERTY = "city";
    public static final String ADDRESS_PROPERTY = "address";
    public static final String ADDRESS2_PROPERTY = "address2";
    public static final String ZIP_PROPERTY = "zip";
    public static final String PHONE_PROPERTY = "phone";
    public static final String EMAIL_PROPERTY = "email";

    /**
     * Cassandra tenant constants.
     */
    public static final String TENANT_COLUMN_FAMILY_NAME = "tenant";
    public static final String TENANT_TITLE_PROPERTY = TITLE_PROPERTY;
    public static final String TENANT_REGION_PROPERTY = "region";
    public static final String TENANT_ADDITIONAL_INFO_PROPERTY = ADDITIONAL_INFO_PROPERTY;

    public static final String TENANT_BY_REGION_AND_SEARCH_TEXT_COLUMN_FAMILY_NAME = "tenant_by_region_and_search_text";

    /**
     * Cassandra customer constants.
     */
    public static final String CUSTOMER_COLUMN_FAMILY_NAME = "customer";
    public static final String CUSTOMER_TENANT_ID_PROPERTY = TENANT_ID_PROPERTY;
    public static final String CUSTOMER_TITLE_PROPERTY = TITLE_PROPERTY;
    public static final String CUSTOMER_ADDITIONAL_INFO_PROPERTY = ADDITIONAL_INFO_PROPERTY;

    public static final String CUSTOMER_BY_TENANT_AND_SEARCH_TEXT_COLUMN_FAMILY_NAME = "customer_by_tenant_and_search_text";
    public static final String CUSTOMER_BY_TENANT_AND_TITLE_VIEW_NAME = "customer_by_tenant_and_title";


    public static final String ENTITY_VIEW_TABLE_FAMILY_NAME = "entity_view";
    public static final String ENTITY_VIEW_ENTITY_ID_PROPERTY = ENTITY_ID_COLUMN;
    public static final String ENTITY_VIEW_TENANT_ID_PROPERTY = TENANT_ID_PROPERTY;
    public static final String ENTITY_VIEW_CUSTOMER_ID_PROPERTY = CUSTOMER_ID_PROPERTY;
    public static final String ENTITY_VIEW_NAME_PROPERTY = "name";
    public static final String ENTITY_VIEW_BY_TENANT_AND_CUSTOMER_CF = "entity_view_by_tenant_and_customer";
    public static final String ENTITY_VIEW_BY_TENANT_AND_CUSTOMER_AND_TYPE_CF = "entity_view_by_tenant_and_customer_and_type";
    public static final String ENTITY_VIEW_BY_TENANT_AND_ENTITY_ID_CF = "entity_view_by_tenant_and_entity_id";
    public static final String ENTITY_VIEW_KEYS_PROPERTY = "keys";
    public static final String ENTITY_VIEW_TYPE_PROPERTY = "type";
    public static final String ENTITY_VIEW_START_TS_PROPERTY = "start_ts";
    public static final String ENTITY_VIEW_END_TS_PROPERTY = "end_ts";
    public static final String ENTITY_VIEW_ADDITIONAL_INFO_PROPERTY = ADDITIONAL_INFO_PROPERTY;
    public static final String ENTITY_VIEW_BY_TENANT_AND_SEARCH_TEXT_CF = "entity_view_by_tenant_and_search_text";
    public static final String ENTITY_VIEW_BY_TENANT_BY_TYPE_AND_SEARCH_TEXT_CF = "entity_view_by_tenant_by_type_and_search_text";
    public static final String ENTITY_VIEW_BY_TENANT_AND_NAME = "entity_view_by_tenant_and_name";

    /**
     * Cassandra device constants.
     */
    public static final String DEVICE_COLUMN_FAMILY_NAME = "device";
    public static final String DEVICE_TENANT_ID_PROPERTY = TENANT_ID_PROPERTY;
    public static final String DEVICE_CUSTOMER_ID_PROPERTY = CUSTOMER_ID_PROPERTY;
    public static final String DEVICE_NAME_PROPERTY = "name";
    public static final String DEVICE_TYPE_PROPERTY = "type";
    public static final String DEVICE_IS_DELETE_PROPERTY = "is_delete";
    public static final String DEVICE_TYPE_NAME = "device_type_name";
    public static final String DEVICE_FOREIGN_KEY = "foreign_key";
    public static final String DEVICE_ADDITIONAL_INFO_PROPERTY = ADDITIONAL_INFO_PROPERTY;

    public static final String DEVICE_BY_TENANT_AND_SEARCH_TEXT_COLUMN_FAMILY_NAME = "device_by_tenant_and_search_text";
    public static final String DEVICE_BY_TENANT_BY_TYPE_AND_SEARCH_TEXT_COLUMN_FAMILY_NAME = "device_by_tenant_by_type_and_search_text";
    public static final String DEVICE_BY_CUSTOMER_AND_SEARCH_TEXT_COLUMN_FAMILY_NAME = "device_by_customer_and_search_text";
    public static final String DEVICE_BY_CUSTOMER_BY_TYPE_AND_SEARCH_TEXT_COLUMN_FAMILY_NAME = "device_by_customer_by_type_and_search_text";
    public static final String DEVICE_BY_TENANT_AND_NAME_VIEW_NAME = "device_by_tenant_and_name";
    public static final String DEVICE_TYPES_BY_TENANT_VIEW_NAME = "device_types_by_tenant";

    /**
     * Cassandra audit log constants.
     */
    public static final String AUDIT_LOG_COLUMN_FAMILY_NAME = "audit_log";

    public static final String AUDIT_LOG_BY_ENTITY_ID_CF = "audit_log_by_entity_id";
    public static final String AUDIT_LOG_BY_CUSTOMER_ID_CF = "audit_log_by_customer_id";
    public static final String AUDIT_LOG_BY_USER_ID_CF = "audit_log_by_user_id";
    public static final String AUDIT_LOG_BY_TENANT_ID_CF = "audit_log_by_tenant_id";
    public static final String AUDIT_LOG_BY_TENANT_ID_PARTITIONS_CF = "audit_log_by_tenant_id_partitions";

    public static final String AUDIT_LOG_ID_PROPERTY = ID_PROPERTY;
    public static final String AUDIT_LOG_TENANT_ID_PROPERTY = TENANT_ID_PROPERTY;
    public static final String AUDIT_LOG_CUSTOMER_ID_PROPERTY = CUSTOMER_ID_PROPERTY;
    public static final String AUDIT_LOG_ENTITY_TYPE_PROPERTY = ENTITY_TYPE_PROPERTY;
    public static final String AUDIT_LOG_ENTITY_ID_PROPERTY = ENTITY_ID_COLUMN;
    public static final String AUDIT_LOG_ENTITY_NAME_PROPERTY = "entity_name";
    public static final String AUDIT_LOG_USER_ID_PROPERTY = USER_ID_PROPERTY;
    public static final String AUDIT_LOG_PARTITION_PROPERTY = "partition";
    public static final String AUDIT_LOG_USER_NAME_PROPERTY = "user_name";
    public static final String AUDIT_LOG_ACTION_TYPE_PROPERTY = "action_type";
    public static final String AUDIT_LOG_ACTION_DATA_PROPERTY = "action_data";
    public static final String AUDIT_LOG_ACTION_STATUS_PROPERTY = "action_status";
    public static final String AUDIT_LOG_ACTION_FAILURE_DETAILS_PROPERTY = "action_failure_details";

    /**
     * Cassandra asset constants.
     */
    public static final String ASSET_COLUMN_FAMILY_NAME = "asset";
    public static final String ASSET_TENANT_ID_PROPERTY = TENANT_ID_PROPERTY;
    public static final String ASSET_CUSTOMER_ID_PROPERTY = CUSTOMER_ID_PROPERTY;
    public static final String ASSET_NAME_PROPERTY = "name";
    public static final String ASSET_TYPE_PROPERTY = "type";
    public static final String ASSET_ADDITIONAL_INFO_PROPERTY = ADDITIONAL_INFO_PROPERTY;

    public static final String ASSET_BY_TENANT_AND_SEARCH_TEXT_COLUMN_FAMILY_NAME = "asset_by_tenant_and_search_text";
    public static final String ASSET_BY_TENANT_BY_TYPE_AND_SEARCH_TEXT_COLUMN_FAMILY_NAME = "asset_by_tenant_by_type_and_search_text";
    public static final String ASSET_BY_CUSTOMER_AND_SEARCH_TEXT_COLUMN_FAMILY_NAME = "asset_by_customer_and_search_text";
    public static final String ASSET_BY_CUSTOMER_BY_TYPE_AND_SEARCH_TEXT_COLUMN_FAMILY_NAME = "asset_by_customer_by_type_and_search_text";
    public static final String ASSET_BY_TENANT_AND_NAME_VIEW_NAME = "asset_by_tenant_and_name";
    public static final String ASSET_TYPES_BY_TENANT_VIEW_NAME = "asset_types_by_tenant";

    /**
     * Cassandra entity_subtype constants.
     */
    public static final String ENTITY_SUBTYPE_COLUMN_FAMILY_NAME = "entity_subtype";
    public static final String ENTITY_SUBTYPE_TENANT_ID_PROPERTY = TENANT_ID_PROPERTY;
    public static final String ENTITY_SUBTYPE_ENTITY_TYPE_PROPERTY = ENTITY_TYPE_PROPERTY;
    public static final String ENTITY_SUBTYPE_TYPE_PROPERTY = "type";

    /**
     * Cassandra alarm constants.
     */
    public static final String ALARM_COLUMN_FAMILY_NAME = "alarm";
    public static final String ALARM_TENANT_ID_PROPERTY = TENANT_ID_PROPERTY;
    public static final String ALARM_TYPE_PROPERTY = "type";
    public static final String ALARM_DETAILS_PROPERTY = "details";
    public static final String ALARM_ORIGINATOR_ID_PROPERTY = "originator_id";
    public static final String ALARM_ORIGINATOR_TYPE_PROPERTY = "originator_type";
    public static final String ALARM_SEVERITY_PROPERTY = "severity";
    public static final String ALARM_STATUS_PROPERTY = "status";
    public static final String ALARM_START_TS_PROPERTY = "start_ts";
    public static final String ALARM_END_TS_PROPERTY = "end_ts";
    public static final String ALARM_ACK_TS_PROPERTY = "ack_ts";
    public static final String ALARM_CLEAR_TS_PROPERTY = "clear_ts";
    public static final String ALARM_PROPAGATE_PROPERTY = "propagate";
    public static final String ALARM_VALUE = "value";
    public static final String ALARM_LEVEL = "level";
    public static final String ALARM_JSON = "alarm_json";
    public static final String ALARM_ATTRIBUTE = "attribute";
    public static final String ALARM_TYPE = "alarm_type";
    public static final String ALARM_PARAMS = "params";
    public static final String ALARM_SCRIPT = "alarm_script";
    public static final String ALARM_RESTORE_SCRIPT = "restore_script";
    public static final String ALARM_ALARM_NAME = "alarm_name";
    public static final String ALARM_CREATE_TIME = "create_time";
    public static final String ALARM_JSON_ID = "alarm_json_id";
    public static final String ALARM_JSON_NAME = "alarm_json_name";
    public static final String ALARM_PERIOD = "period";
    public static final String ALARM_RESTORE_TYPE = "restore_type";
    public static final String ALARM_ALARM_TYPE = "alarm_type";
    public static final String ALARM_CYCLE = "alarm_cycle";
    public static final String ALARM_IS_CYCLE="alarm_is_cycle";

    public static final String ALARM_BY_ID_VIEW_NAME = "alarm_by_id";




    public static final String DATA_MONITOR="dataMonitor";


    /**
     * Cassandra entity relation constants.
     */
    public static final String RELATION_COLUMN_FAMILY_NAME = "relation";
    public static final String RELATION_FROM_ID_PROPERTY = "from_id";
    public static final String RELATION_FROM_TYPE_PROPERTY = "from_type";
    public static final String RELATION_TO_ID_PROPERTY = "to_id";
    public static final String RELATION_TO_TYPE_PROPERTY = "to_type";
    public static final String RELATION_TYPE_PROPERTY = "relation_type";
    public static final String RELATION_TYPE_GROUP_PROPERTY = "relation_type_group";

    public static final String RELATION_BY_TYPE_AND_CHILD_TYPE_VIEW_NAME = "relation_by_type_and_child_type";
    public static final String RELATION_REVERSE_VIEW_NAME = "reverse_relation";


    /**
     * Cassandra device_credentials constants.
     */
    public static final String DEVICE_CREDENTIALS_COLUMN_FAMILY_NAME = "device_credentials";
    public static final String DEVICE_CREDENTIALS_DEVICE_ID_PROPERTY = DEVICE_ID_PROPERTY;
    public static final String DEVICE_CREDENTIALS_CREDENTIALS_TYPE_PROPERTY = "credentials_type";
    public static final String DEVICE_CREDENTIALS_CREDENTIALS_ID_PROPERTY = "credentials_id";
    public static final String DEVICE_CREDENTIALS_CREDENTIALS_VALUE_PROPERTY = "credentials_value";

    public static final String DEVICE_CREDENTIALS_BY_DEVICE_COLUMN_FAMILY_NAME = "device_credentials_by_device";
    public static final String DEVICE_CREDENTIALS_BY_CREDENTIALS_ID_COLUMN_FAMILY_NAME = "device_credentials_by_credentials_id";

    /**
     * Cassandra widgets_bundle constants.
     */
    public static final String WIDGETS_BUNDLE_COLUMN_FAMILY_NAME = "widgets_bundle";
    public static final String WIDGETS_BUNDLE_TENANT_ID_PROPERTY = TENANT_ID_PROPERTY;
    public static final String WIDGETS_BUNDLE_ALIAS_PROPERTY = ALIAS_PROPERTY;
    public static final String WIDGETS_BUNDLE_TITLE_PROPERTY = TITLE_PROPERTY;
    public static final String WIDGETS_BUNDLE_IMAGE_PROPERTY = "image";

    public static final String WIDGETS_BUNDLE_BY_TENANT_AND_SEARCH_TEXT_COLUMN_FAMILY_NAME = "widgets_bundle_by_tenant_and_search_text";
    public static final String WIDGETS_BUNDLE_BY_TENANT_AND_ALIAS_COLUMN_FAMILY_NAME = "widgets_bundle_by_tenant_and_alias";

    /**
     * Cassandra widget_type constants.
     */
    public static final String WIDGET_TYPE_COLUMN_FAMILY_NAME = "widget_type";
    public static final String WIDGET_TYPE_TENANT_ID_PROPERTY = TENANT_ID_PROPERTY;
    public static final String WIDGET_TYPE_BUNDLE_ALIAS_PROPERTY = "bundle_alias";
    public static final String WIDGET_TYPE_ALIAS_PROPERTY = ALIAS_PROPERTY;
    public static final String WIDGET_TYPE_NAME_PROPERTY = "name";
    public static final String WIDGET_TYPE_DESCRIPTOR_PROPERTY = "descriptor";

    public static final String WIDGET_TYPE_BY_TENANT_AND_ALIASES_COLUMN_FAMILY_NAME = "widget_type_by_tenant_and_aliases";

    /**
     * Cassandra dashboard constants.
     */
    public static final String DASHBOARD_COLUMN_FAMILY_NAME = "dashboard";
    public static final String DASHBOARD_TENANT_ID_PROPERTY = TENANT_ID_PROPERTY;
    public static final String DASHBOARD_TITLE_PROPERTY = TITLE_PROPERTY;
    public static final String DASHBOARD_CONFIGURATION_PROPERTY = "configuration";
    public static final String DASHBOARD_ASSIGNED_CUSTOMERS_PROPERTY = "assigned_customers";

    public static final String DASHBOARD_BY_TENANT_AND_SEARCH_TEXT_COLUMN_FAMILY_NAME = "dashboard_by_tenant_and_search_text";

    /**
     * Cassandra plugin component metadata constants.
     */
    public static final String COMPONENT_DESCRIPTOR_COLUMN_FAMILY_NAME = "component_descriptor";
    public static final String COMPONENT_DESCRIPTOR_TYPE_PROPERTY = "type";
    public static final String COMPONENT_DESCRIPTOR_SCOPE_PROPERTY = "scope";
    public static final String COMPONENT_DESCRIPTOR_NAME_PROPERTY = "name";
    public static final String COMPONENT_DESCRIPTOR_CLASS_PROPERTY = "clazz";
    public static final String COMPONENT_DESCRIPTOR_CONFIGURATION_DESCRIPTOR_PROPERTY = "configuration_descriptor";
    public static final String COMPONENT_DESCRIPTOR_ACTIONS_PROPERTY = "actions";

    public static final String COMPONENT_DESCRIPTOR_BY_TYPE_AND_SEARCH_TEXT_COLUMN_FAMILY_NAME = "component_desc_by_type_search_text";
    public static final String COMPONENT_DESCRIPTOR_BY_SCOPE_TYPE_AND_SEARCH_TEXT_COLUMN_FAMILY_NAME = "component_desc_by_scope_type_search_text";
    public static final String COMPONENT_DESCRIPTOR_BY_ID = "component_desc_by_id";

    /**
     * Cassandra event constants.
     */
    public static final String EVENT_COLUMN_FAMILY_NAME = "event";
    public static final String EVENT_TENANT_ID_PROPERTY = TENANT_ID_PROPERTY;
    public static final String EVENT_TYPE_PROPERTY = "event_type";
    public static final String EVENT_UID_PROPERTY = "event_uid";
    public static final String EVENT_ENTITY_TYPE_PROPERTY = ENTITY_TYPE_PROPERTY;
    public static final String EVENT_ENTITY_ID_PROPERTY = "entity_id";
    public static final String EVENT_BODY_PROPERTY = "body";

    public static final String EVENT_BY_TYPE_AND_ID_VIEW_NAME = "event_by_type_and_id";
    public static final String EVENT_BY_ID_VIEW_NAME = "event_by_id";

    public static final String DEBUG_MODE = "debug_mode";

    /**
     * Cassandra rule chain constants.
     */
    public static final String RULE_CHAIN_COLUMN_FAMILY_NAME = "rule_chain";
    public static final String RULE_CHAIN_TENANT_ID_PROPERTY = TENANT_ID_PROPERTY;
    public static final String RULE_CHAIN_NAME_PROPERTY = "name";
    public static final String RULE_CHAIN_FIRST_RULE_NODE_ID_PROPERTY = "first_rule_node_id";
    public static final String RULE_CHAIN_ROOT_PROPERTY = "root";
    public static final String RULE_CHAIN_CONFIGURATION_PROPERTY = "configuration";

    public static final String RULE_CHAIN_BY_TENANT_AND_SEARCH_TEXT_COLUMN_FAMILY_NAME = "rule_chain_by_tenant_and_search_text";

    /**
     * Cassandra rule node constants.
     */
    public static final String RULE_NODE_COLUMN_FAMILY_NAME = "rule_node";
    public static final String RULE_NODE_CHAIN_ID_PROPERTY = "rule_chain_id";
    public static final String RULE_NODE_TYPE_PROPERTY = "type";
    public static final String RULE_NODE_NAME_PROPERTY = "name";
    public static final String RULE_NODE_CONFIGURATION_PROPERTY = "configuration";

    /**
     * Cassandra attributes and timeseries constants.
     */
    public static final String ATTRIBUTES_KV_CF = "attributes_kv_cf";
    public static final String TS_KV_CF = "ts_kv_cf";
    public static final String TS_KV_PARTITIONS_CF = "ts_kv_partitions_cf";
    public static final String TS_KV_LATEST_CF = "ts_kv_latest_cf";

    public static final String PARTITION_COLUMN = "partition";
    public static final String KEY_COLUMN = "key";
    public static final String TS_COLUMN = "ts";
    public static final String VALUE_COLUMN = "value";

    /**
     * cassandra telemetryAttribute constants
     */
    public static final String TELEMETRY_ATTRIBUTE_COLIMN_FAMILY_NAME = "telemetry_attribute";
    public static final String TELEMETRY_ATTRIBUTE_DEVICE_ID = "device_id";
    public static final String TELEMETRY_ATTRIBUTE_ATTRIBUTE_TYPE = "attribute_type";
    public static final String TELEMETRY_ATTRIBUTE_AGREEMENT_NAME = "agreement_name";
    public static final String TELEMETRY_ATTRIBUTE_HARMONIC_NUMBER = "harmonic_number";
    public static final String TELEMETRY_ATTRIBUTE_SHOW_NAME = "show_name";
    public static final String TELEMETRY_ATTRIBUTE_DATA_TYPE = "data_type";
    public static final String TELEMETRY_ATTRIBUTE_DATA_OFFSET = "data_offset";
    public static final String TELEMETRY_ATTRIBUTE_COEFFICIENT = "coefficient";
    public static final String TELEMETRY_ATTRIBUTE_MIN_VALUE = "min_value";
    public static final String TELEMETRY_ATTRIBUTE_MAX_VALUE = "max_value";
    public static final String TELEMETRY_ATTRIBUTE_FORMULA = "formula";
    public static final String TELEMETRY_ATTRIBUTE_STATISTICS = "statistics";
    public static final String TELEMETRY_ATTRIBUTE_UNIT = "unit";
    public static final String TELEMETRY_ATTRIBUTE_UNIT_COEFFICIENT = "unit_coefficient";
    public static final String TELEMETRY_ATTRIBUTE_BYTE_ORDER = "byte_order";
    public static final String TELEMETRY_ATTRIBUTE_CONFIGURATION_PROPERTY = "configuration_property";

    /**
     * virtual
     */
    public static final String VIRTUAL_COLUMN_FAMILY_NAME = "virtual";
    public static final String VIRTUAL_TENANT_ID = "tenant_id";
    public static final String VIRTUAL_NAME = "name";
    public static final String VIRTUAL_GROUP = "virtual_group";
    public static final String VIRTUAL_TYPE = "type";
    public static final String VIRTUAL_UNIT = "unit";
    public static final String VIRTUAL_FORULA = "formula";
    public static final String VIRTUAL_SERIAL_NUMBER = "serial_number";


    public static final String CONSTANTS_ATTRIBUTE_NAME = "constants_attribute";

    /**
     * menu pool
     */
    public static final String MENU_POOL_NAME = "menu_pool";
    public static final String MENU_POOL_PARENT_ID = "parent_id";
    public static final String MENU_POOL_DEFAULT_NAME = "default_name";
    public static final String MENU_POOL_TYPE = "type";
    public static final String MENU_POOL_ORDER_NUM = "order_num";
    public static final String MENU_POOL_PARAMS = "params";
    public static final String MENU_POOL_STATUS = "status";
    public static final String MENU_POOL_FLAG_DELETE = "flag_delete";
    public static final String MENU_POOL_ADDITIONAL_INFO = "additional_info";


    public static final UUID MENU_POOL_ROOT = UUIDs.startOf(9527);
    public static final Integer MENU_POOL_STATUS_DISPLAY = 0;
    public static final Integer MENU_POOL_STATUS_HIDE = 1;
    public static final Integer MENU_POOL_FLAG_DELETE_FALSE = 0;
    public static final Integer MENU_POOL_FLAG_DELETE_TRUE = 1;

    /**
     * menu tenant
     */
    public static final String MENU_TENANT_NAME = "menu_tenant";
    public static final String MENU_TENANT_TENANT_ID = "tenant_id";
    public static final String MENU_TENANT_MENU_POOL_ID = "menu_pool_id";
    public static final String MENU_TENANT_IS_EXTENSION_MENU = "is_extension_menu";


    /**
     * menu customer
     */
    public static final String MENU_CUSTOMER_NAME = "menu_customer";
    public static final String MENU_CUSTOMER_NAME_PROPERTY = "name";
    public static final String MENU_CUSTOMER_DEFAULT_NAME = "default_name";
    public static final String MENU_CUSTOMER_PARENT_ID = "parent_id";
    public static final String MENU_CUSTOMER_PERMS = "perms";
    public static final String MENU_CUSTOMER_ICON = "icon";
    public static final String MENU_CUSTOMER_ORDER_NUM = "order_num";
    public static final String MENU_CUSTOMER_URL = "url";
    public static final String MENU_CUSTOMER_STATUS = "status";
    public static final String MENU_CUSTOMER_FLAG_DELETE = "flag_delete";
    public static final String MENU_CUSTOMER_MENT_TENANT_ID = "menu_tenant_id";
    public static final String MENU_CUSTOMER_ADDITIONAL_INFO = "additional_info";
    public static final UUID MENU_CUSTOMER_ROOT = UUIDs.startOf(8888);
    public static final UUID MENU_CUSTOMER_UUID = UUIDs.startOf(6666);

    public static final Integer MENU_CUSTOMER_STATUS_DISPLAY = 0;
    public static final Integer MENU_CUSTOMER_STATUS_HIDE = 1;
    public static final Integer MENU_CUSTOMER_FLAG_DELETE_FALSE = 0;
    public static final Integer MENU_CUSTOMER_FLAG_DELETE_TRUE = 1;

    /**
     * role
     */
    public static final String CUSTOMER_ROLE_NAME = "customer_role";
    public static final String CUSTOMER_ROLE_NAME_PROPERTY = "name";
    public static final String CUSTOMER_ROLE_STATUS = "status";
    public static final String CUSTOMER_ROLE_FLAG_DELETE = "flag_delete";
    public static final String CUSTOMER_ROLE_ADDITIONAL_INFO = "additional_info";
    public static final String CUSTOMER_ROLE_TENANT_ID = "tenant_id";

    public static final Integer CUSTOMER_ROLE_STATUS_DISPLAY = 0;
    public static final Integer CUSTOMER_ROLE_STATUS_HIDE = 1;
    public static final Integer CUSTOMER_ROLE_FLAG_DELETE_FALSE = 0;
    public static final Integer CUSTOMER_ROLE_FLAG_DELETE_TRUE = 1;

    /**
     * menu role
     */
    public static final String CUSTOMER_MENU_ROLE = "customer_menu_role";
    public static final String CUSTOMER_MENU_ROLE_MENU_ID = "menu_id";
    public static final String CUSTOMER_MENU_ROLE_ROLE_ID = "role_id";

    /**
     * user role
     */
    public static final String CUSTOMER_USER_ROLE = "customer_user_role";
    public static final String CUSTOMER_USER_ROLE_MENU_ID = "user_id";
    public static final String CUSTOMER_USER_ROLE_ROLE_ID = "role_id";

    /**
     * repair
     */
    public static final String REPAIR_NAME = "repair";
    public static final String REPAIR_DEVICE_ID = "device_id";
    public static final String REPAIR_CREATE_TIME = "created_time";
    public static final String REPAIR_REPAIR_START_TIME = "repair_start_time";
    public static final String REPAIR_REPAIR_END_TIME = "repair_end_time";
    public static final String REPAIR_STATUS = "status";
    public static final String REPAIR_REMARK = "remark";
    public static final String REPAIR_TENANT_ID = "tenant_id";


    /**
     * maintain
     */

    public static final String MAINTAIN_NAME = "maintain";
    public static final String MAINTAIN_JSON_NAME = "name";
    public static final String MAINTAIN_JSON = "maintain_json";
    public static final String MAINTAIN_RECORD_NAME = "maintain_record";
    public static final String MAINTAIN_RECORD_START = "start_time";
    public static final String MAINTAIN_RECORD_END = "end_time";
    public static final String MAINTAIN_ID = "maintain_id";
    public static final String MAINTAIN_USER = "maintain_user";
    public static final String MAINTAIN_PARAMS = "params";
    public static final String MAINTAIN_TYPE = "type";
    public static final String MAINTAIN_PERIOD = "period";

    /**
     * lotCard
     */
    public static final String LOT_CARD_NAME = "lot_card";
    public static final String LOT_CARD_ID = "card_id";
    public static final String NUMBER = "number";
    public static final String OPERATOR = "operator";
    public static final String DATA_FLOW = "data_flow";


    /**
     * option log
     */
    public static final String OPTION_LOG_NAME = "option_log";
    public static final String OPTION_LOG_USER_ID = "user_id";
    public static final String OPTION_LOG_TENANT_ID = "tenant_id";
    public static final String OPTION_LOG_FIRST_NAME = "first_name";
    public static final String OPTION_LOG_AUTHORITY = "authority";
    public static final String OPTION_LOG_OPTIONS = "options";
    public static final String OPTION_LOG_CREATE_TIME = "create_time";
    public static final String OPTION_LOG_ADDITIONAL_INFO = "additional_info";

    /**
     * 水泵
     */
    public static final String WATER_PUMP_TABLE = "tb_water_pump";
    public static final String WATER_PUMP_NAME = "name";
    public static final String WATER_PUMP_LOCATION = "location";

    /**
     * 水泵关联设备
     */
    public static final String WATER_PUMP_RELATION_TABLE = "tb_water_pump_relation";
    public static final String WATER_PUMP_RELATION_WATER_PUMP_ID = "water_pump_id";
    public static final String WATER_PUMP_RELATION_DEVICE_ID = "device_id";
    public static final String WATER_PUMP_RELATION_ATTR = "attr";

    /**
     * 加药管理
     */
    public static final String TB_MEDICINE_MANAGE_TABLE = "tb_medicine_manage";
    public static final String TB_MEDICINE_MANAGE_STATION_ID = "station_id";
    public static final String TB_MEDICINE_MANAGE_MEDICINE_TYPE = "medicine_type";
    public static final String TB_MEDICINE_MANAGE_TIME = "time";
    public static final String TB_MEDICINE_MANAGE_NUM = "num";
    public static final String TB_MEDICINE_MANAGE_UNIT_PRICE = "unit_price";
    public static final String TB_MEDICINE_MANAGE_PRICE = "price";

    /**
     * 化验记录
     */
    public static final String TB_ASSAY_RECORD_TABLE = "tb_assay_record";
    public static final  String TB_ASSAY_RECORD_STATION_ID = "station_id";
    public static final  String TB_ASSAY_RECORD_DISSOLVED_OXYGEN = "dissolved_oxygen";
    public static final  String TB_ASSAY_RECORD_CONDUCTIVITY = "conductivity";
    public static final  String TB_ASSAY_RECORD_TEMPERATURE = "temperature";
    public static final  String TB_ASSAY_RECORD_CHLOROPHYL = "chlorophyl";
    public static final  String TB_ASSAY_RECORD_AMMONIA_NITROGEN = "ammonia_nitrogen";
    public static final  String TB_ASSAY_RECORD_NITRATE = "nitrate";
    public static final  String TB_ASSAY_RECORD_DISSOLVED_ORGANIC_CARBON = "dissolved_organic_carbon";
    public static final  String TB_ASSAY_RECORD_ORGANIC_CARBON = "organic_carbon";
    public static final  String TB_ASSAY_RECORD_TURBIDITY = "turbidity";
    public static final  String TB_ASSAY_RECORD_PH = "ph";
    public static final  String TB_ASSAY_RECORD_CHROMATICITY = "chromaticity";
    public static final  String TB_ASSAY_RECORD_UV254 = "uv254";
    public static final  String TB_ASSAY_RECORD_STATUS = "status";
    public static final  String TB_ASSAY_RECORD_STATION_NAME = "station_name";

    /**
     * 营收数据录入
     */
    public static final String TB_OPERATING_INCOME_INPUT_TABLE = "tb_operating_income_input";
    public static final String TB_OPERATING_INCOME_INPUT_TS = "ts";
    public static final String TB_OPERATING_INCOME_INPUT_STATION_ID = "station_id";
    public static final String TB_OPERATING_INCOME_INPUT_WATER_SALES = "water_sales";
    public static final String TB_OPERATING_INCOME_INPUT_MONEY = "money";
    public static final String TB_OPERATING_INCOME_INPUT_UPDATE_USER = "update_user";
    public static final String TB_OPERATING_INCOME_INPUT_UPDATE_TIME = "update_time";


    /**
     * Main names of cassandra key-value columns storage.
     */
    public static final String BOOLEAN_VALUE_COLUMN = "bool_v";
    public static final String STRING_VALUE_COLUMN = "str_v";
    public static final String LONG_VALUE_COLUMN = "long_v";
    public static final String DOUBLE_VALUE_COLUMN = "dbl_v";

    protected static final String[] NONE_AGGREGATION_COLUMNS = new String[]{LONG_VALUE_COLUMN, DOUBLE_VALUE_COLUMN, BOOLEAN_VALUE_COLUMN, STRING_VALUE_COLUMN, KEY_COLUMN, TS_COLUMN};

    protected static final String[] COUNT_AGGREGATION_COLUMNS = new String[]{count(LONG_VALUE_COLUMN), count(DOUBLE_VALUE_COLUMN), count(BOOLEAN_VALUE_COLUMN), count(STRING_VALUE_COLUMN)};

    protected static final String[] MIN_AGGREGATION_COLUMNS = ArrayUtils.addAll(COUNT_AGGREGATION_COLUMNS,
            new String[]{min(LONG_VALUE_COLUMN), min(DOUBLE_VALUE_COLUMN), min(BOOLEAN_VALUE_COLUMN), min(STRING_VALUE_COLUMN)});
    protected static final String[] MAX_AGGREGATION_COLUMNS = ArrayUtils.addAll(COUNT_AGGREGATION_COLUMNS,
            new String[]{max(LONG_VALUE_COLUMN), max(DOUBLE_VALUE_COLUMN), max(BOOLEAN_VALUE_COLUMN), max(STRING_VALUE_COLUMN)});
    protected static final String[] SUM_AGGREGATION_COLUMNS = ArrayUtils.addAll(COUNT_AGGREGATION_COLUMNS,
            new String[]{sum(LONG_VALUE_COLUMN), sum(DOUBLE_VALUE_COLUMN)});
    protected static final String[] AVG_AGGREGATION_COLUMNS = SUM_AGGREGATION_COLUMNS;

    public static String min(String s) {
        return "min(" + s + ")";
    }

    public static String max(String s) {
        return "max(" + s + ")";
    }

    public static String sum(String s) {
        return "sum(" + s + ")";
    }

    public static String count(String s) {
        return "count(" + s + ")";
    }

    public static String[] getFetchColumnNames(Aggregation aggregation) {
        switch (aggregation) {
            case NONE:
                return NONE_AGGREGATION_COLUMNS;
            case MIN:
                return MIN_AGGREGATION_COLUMNS;
            case MAX:
                return MAX_AGGREGATION_COLUMNS;
            case SUM:
                return SUM_AGGREGATION_COLUMNS;
            case COUNT:
                return COUNT_AGGREGATION_COLUMNS;
            case AVG:
                return AVG_AGGREGATION_COLUMNS;
            default:
                throw new RuntimeException("Aggregation type: " + aggregation + " is not supported!");
        }
    }

    /**
     * project
     */
    public static final String PROJECT_TABLE = "project";
    public static final String PROJECT_PARENT_ID = "parent_id";
    public static final String PROJECT_CREATE_TIME = "create_time";
    public static final String PROJECT_TENANT_ID = "tenant_id";
    public static final String PROJECT_NAME = "name";
    public static final String PROJECT_ADDITIONAL_INFO = "additional_info";

    /**
     * project relation
     */
    public static final String PROJECT_RELATION_TABLE = "project_relation";
    public static final String PROJECT_RELATION_PROJECT_ID = "project_id";
    public static final String PROJECT_RELATION_ENTITY_ID = "entity_id";
    public static final String PROJECT_RELATION_ENTITY_TYPE = "entity_type";
    public static final String PROJECT_RELATION_CREATE_TIME = "create_time";

    /**
     * app type
     */
    public static final String APP_TYPE_TABLE = "app_type";
    public static final String APP_TYPE_CREATE_TIME = "create_time";
    public static final String APP_TYPE_NAME = "app_name";
    public static final String APP_TYPE_ADDITIONAL_INFO = "additional_info";

    /**
     * app role
     */
    public static final String APP_ROLE_TABLE = "app_role";
    public static final String APP_ROLE_CREATE_TIME = "create_time";
    public static final String APP_ROLE_NAME = "name";
    public static final String APP_ROLE_ADDITIONAL_INFO = "additional_info";
    public static final String APP_ROLE_TYPE_ID = "app_type_id";

    /**
     * app type relation
     */
    public static final String APP_TYPE_RELATION_TABLE = "app_type_relation";
    public static final String APP_TYPE_RELATION_CREATE_TIME = "create_time";
    public static final String APP_TYPE_RELATION_MENU_POOL_ID = "menu_pool_id";
    public static final String APP_TYPE_RELATION_APP_TYPE_ID = "appTypeId";

    /**
     * 站点表
     */
    public static final String TB_STATION_TABLE = "tb_station";
    public static final String TB_STATION_NAME = "name";
    public static final String TB_STATION_TYPE = "type";
    public static final String TB_STATION_ADDRESS = "address";
    public static final String TB_STATION_LOCATION = "location";
    public static final String TB_STATION_ADDITIONAL_INFO = "additional_info";
    public static final String TB_STATION_REMARK = "remark";
    public static final String TB_STATION_IMGS = "imgs";
    public static final String TB_STATION_ORDER_NUM = "order_num";
    public static final String TB_STATION_SCADA_URL = "scada_url";

    /**
     * 站点设备数据点表
     */
    public static final String TB_STATION_ATTR_TABLE = "tb_station_attr";
    public static final String TB_STATION_ATTR_STATION_ID = "station_id";
    public static final String TB_STATION_ATTR_DEVICE_ID = "device_id";
    public static final String TB_STATION_ATTR_RANGE = "range";
    public static final String TB_STATION_ATTR_ATTR = "attr";
    public static final String TB_STATION_ATTR_TYPE = "type";
    public static final String TB_STATION_ATTR_UNIT = "unit";

    /**
     *
     */
    public static final String TB_FLOW_RATE_REPORT_TABLE = "tb_flow_rate_report";
    public static final String TB_FLOW_RATE_REPORT_STATION_ID = "station_id";
    public static final String TB_FLOW_RATE_REPORT_TIME = "time";
    public static final String TB_FLOW_RATE_REPORT_FLOW_RATE_AVG = "flow_rate_avg";
    public static final String TB_FLOW_RATE_REPORT_FLOW_RATE_MIN = "flow_rate_min";
    public static final String TB_FLOW_RATE_REPORT_FLOW_RATE_MIN_TS = "flow_rate_min_ts";
    public static final String TB_FLOW_RATE_REPORT_FLOW_RATE_MAX = "flow_rate_max";
    public static final String TB_FLOW_RATE_REPORT_FLOW_RATE_MAX_TS = "flow_rate_max_ts";
    public static final String TB_FLOW_RATE_REPORT_LAST_TOTAL_FLOW = "last_total_flow";
    public static final String TB_FLOW_RATE_REPORT_LAST_REVERSE_TOTAL_FLOW = "last_reverse_total_flow";
    public static final String TB_FLOW_RATE_REPORT_LAST_TOTAL_FLOW_TS = "last_total_flow_ts";
    public static final String TB_FLOW_RATE_REPORT_LAST_REVERSE_TOTAL_FLOW_TS = "last_reverse_total_flow_ts";
    public static final String TB_FLOW_RATE_REPORT_TOTAL_FLOW = "total_flow";
    public static final String TB_FLOW_RATE_REPORT_TOTAL_RUN_TIME = "total_run_time";
    public static final String TB_FLOW_RATE_REPORT_TOTAL_STOP_TIME = "total_stop_time";
    public static final String TB_FLOW_RATE_REPORT_ENERGY_IN = "energy_in";
    public static final String TB_FLOW_RATE_REPORT_LAST_ENERGY_IN = "last_energy_in";
    public static final String TB_FLOW_RATE_REPORT_LAST_ENERGY_IN_TS = "last_energy_in_ts";


    /**
     *
     */
    public static final String TB_PRESSURE_REPORT_TABLE = "tb_pressure_report";
    public static final String TB_PRESSURE_REPORT_STATION_ID = "station_id";
    public static final String TB_PRESSURE_REPORT_TIME = "time";
    public static final String TB_PRESSURE_REPORT_PRESSURE_AVG = "pressure_avg";
    public static final String TB_PRESSURE_REPORT_PRESSURE_MIN = "pressure_min";
    public static final String TB_PRESSURE_REPORT_PRESSURE_MIN_TS = "pressure_min_ts";
    public static final String TB_PRESSURE_REPORT_PRESSURE_MAX = "pressure_max";
    public static final String TB_PRESSURE_REPORT_PRESSURE_MAX_TS = "pressure_max_ts";

    /**
     * 水质报表填报
     */
    public static final String TB_WATER_REPORT_TABLE = "tb_water_report";
    public static final String TB_WATER_REPORT_TYPE = "type";
    public static final String TB_WATER_REPORT_STATION_ID = "station_id";
    public static final String TB_WATER_REPORT_COD = "cod";
    public static final String TB_WATER_REPORT_BOD = "bod";
    public static final String TB_WATER_REPORT_NH3 = "nh3";
    public static final String TB_WATER_REPORT_PH = "ph";
    public static final String TB_WATER_REPORT_TP = "tp";
    public static final String TB_WATER_REPORT_TN = "tn";
    public static final String TB_WATER_REPORT_TIME = "time";
    public static final String TB_WATER_REPORT_TENANT_ID = "tenant_id";
    public static final String TB_WATER_REPORT_TIME_TYPE = "time_type";

}
