package org.thingsboard.server.utils.imodel.aop;

import org.springframework.core.Ordered;
import org.thingsboard.server.dao.util.reflection.BeanWrapper;
import org.thingsboard.server.dao.util.reflection.FieldWrapper;

public interface ValidatePerformer extends Ordered {
    boolean match(FieldWrapper field);

    String resolveString(BeanWrapper bean, FieldWrapper field, String value, boolean hasParent);

    String resolveDestType(BeanWrapper bean, FieldWrapper field, Class<?> type, Object value, boolean hasParent);

    default boolean callConditionalGetter(BeanWrapper wrapper, String condition) {
        if (condition.length() > 0) {
            if (condition.startsWith("!")) {
                return wrapper.callConditionalGetter(condition.substring(1), true);
            }
            return !wrapper.callConditionalGetter(condition, true);
        }

        return false;
    }
}
