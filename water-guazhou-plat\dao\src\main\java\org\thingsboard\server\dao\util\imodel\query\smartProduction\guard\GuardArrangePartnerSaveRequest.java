package org.thingsboard.server.dao.util.imodel.query.smartProduction.guard;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartProduction.guard.GuardArrangePartner;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;

@Getter
@Setter
public class GuardArrangePartnerSaveRequest extends SaveRequest<GuardArrangePartner> {
    // 排班id
    private String arrangeId;

    // 用户id
    private String userId;


    @Override
    protected GuardArrangePartner build() {
        GuardArrangePartner entity = new GuardArrangePartner();
        commonSet(entity);
        return entity;
    }

    @Override
    protected GuardArrangePartner update(String id) {
        GuardArrangePartner entity = new GuardArrangePartner();
        commonSet(entity);
        return entity;
    }

    private void commonSet(GuardArrangePartner entity) {
        entity.setArrangeId(arrangeId);
        entity.setUserId(userId);
    }

}