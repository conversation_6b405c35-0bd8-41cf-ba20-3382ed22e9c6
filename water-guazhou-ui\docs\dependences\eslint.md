[toc]

# eslint配置解读

## 相关依赖

- [eslint](https://zh-hans.eslint.org/docs/latest/use/getting-started)：eslint必备，基础
- [eslint-config-airbnb-base](https://www.npmjs.com/package/eslint-config-airbnb-base)：使用Airbnb编码规则配置eslint
- [eslint-plugin-import](http://www.npmdoc.org/eslint-plugin-importzhongwenwendangzhongwenshili.html)： 支持 ES2015+ (ES6+) 导入/导出语法的 linting
- [eslint-plugin-react](https://www.npmjs.com/package/eslint-plugin-react)：React specific linting rules for eslint，用于处理jsx、tsx语法检验
- [eslint-plugin-vue](https://www.npmjs.com/package/eslint-plugin-vue)：Official ESLint plugin for Vue.js，用于处理vue的语法检验

## 配置文件

- .eslintrc eslint配置文件

```js
module.exports = {
  // 对全局API的支持
  globals: {
    // vue3 的全局API支持
    defineEmits: 'readonly',
    defineProps: 'readonly'
  },
  parser:'vue-eslint-parser', // 对vue文件的校验
  
  parserOptions: {
    parser: '@typescript-eslint/parser', // Specifies the ESLint parser
    ecmaVersion: 'latest', // Allows for the parsing of modern ECMAScript features
    sourceType: 'module', // Allows for the use of imports
    ecmaFeatures: {
      // Allows for the parsing of JSX
      jsx: true
    }
  },
  // eslint的插件
  extends: [
    'plugin:vue/vue3-recommended', // vue3推荐配置
    'plugin:react/recommended', // react推荐配置，主要用于处理tsx和jsx
    'plugin:@typescript-eslint/recommended', // ts的推荐配置
    'airbnb-base',  // 基于Airbnb编码规则的配置
    './.eslintrc-auto-import.json' // 对unplugin的自动引入的检验配置
  ],
  rules: {
    // 具体的与插件配置相冲突的规则写在这里
  }
}
```

- .eslintignore eslint忽略的目录配置

在这个文件里写下的目录，将会被eslint忽略而不进行代码校验
