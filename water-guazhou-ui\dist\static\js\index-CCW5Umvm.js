import{d as j,a6 as E,r as C,am as H,j as u,c as f,bF as F,s as K,bB as U,o as X,ay as $,g as T,n as S,q as p,i as s,p as g,F as v,bo as R,br as G,b7 as Z,C as ee}from"./index-r0dFAfgr.js";import{_ as te}from"./index-C9hz-UZb.js";import{_ as ae}from"./CardTable-rdWOL4_6.js";import{_ as oe}from"./InlineForm.vue_vue_type_style_index_0_lang-s-ANlzyw.js";import{_ as le}from"./CardSearch-CB_HNR-Q.js";import{l as A}from"./echart-DkOzaNjN.js";import{u as re}from"./useStation-DJgnSZIA.js";import{c as ne,d as se}from"./waterIndicators-BJSzKLY_.js";import"./Search-NSrhrIa_.js";import"./zhandian-YaGuQZe6.js";const ie={class:"wrapper"},ce={class:"main"},pe={class:"left"},ue={class:"table1"},fe={class:"table2"},me={class:"right"},de=j({__name:"index",setup(be){const{getAllStationOption:I}=re(),Y=E(),r=C({compareType:2,chartOption1:null,chartOption2:null,chartOption3:null,storedStationName:"",storedStationList:[],attrTypeList:[{label:"浊度",value:"turbidity",unit:"(NTU)"},{label:"余氯",value:"remainder",unit:"(mg/L)"},{label:"PH",value:"ph",unit:"(pH)"},{label:"BOD5",value:"BOD5",unit:"(mg/L)"},{label:"COD",value:"COD",unit:"(mg/L)"},{label:"总磷",value:"CSK_TP",unit:"(mg/L)"},{label:"总氮",value:"CSK_TN",unit:"(mg/L)"},{label:"溶解氧",value:"DO",unit:"(mg/L)"},{label:"氨氮",value:"CSK_NH3_N",unit:"(mg/L)"}]});H(()=>u().isDark,()=>{r.chartOption1.backgroundColor=u().isDark?"#131624":"#F4F7FA",r.chartOption2.backgroundColor=u().isDark?"#131624":"#F4F7FA",r.chartOption3.backgroundColor=u().isDark?"#131624":"#F4F7FA"});const d=f(!1),b=f(),z=f(),M=f(),D=f(),O=f(),L=f(),h=f(),k=C({defaultParams:{attrType:"turbidity",time:F().format("YYYY-MM-DD"),queryType:"15m"},filters:[{type:"select",label:"水厂",field:"stationId",options:[],clearable:!1,onChange:e=>{r.storedStationName=r.storedStationList.find(o=>(o==null?void 0:o.id)===e).label}},{type:"select",field:"attrType",options:r.attrTypeList,clearable:!1,label:"水质项"},{type:"date",label:"日期",field:"time",clearable:!1},{type:"select",width:"100px",field:"queryType",clearable:!1,options:[{label:"1 m",value:"1m"},{label:"5 m",value:"5m"},{label:"10 m",value:"10m"},{label:"15 m",value:"15m"},{label:"小时",value:"60m"}],label:"时间间隔"},{type:"btn-group",btns:[{perm:!0,text:"查询",click:()=>w(),icon:"iconfont icon-chaxun"},{perm:!0,type:"default",text:"重置",svgIcon:K(Z),click:()=>{var e;(e=b.value)==null||e.resetForm()}},{text:"导出",perm:!0,type:"warning",icon:"iconfont icon-xiazai",click:()=>Q()}]}]}),V=f({group:[{fields:[{type:"radio-button",field:"compareType",options:[{label:"环比",value:2},{label:"同比",value:1},{label:"定基比",value:3}],label:"",clearable:!1,onChange:e=>{r.compareType=e,w()}}]}],defaultValue:{compareType:2}}),_=C({loading:!0,dataList:[],highlightCurrentRow:!0,columns:[{prop:"name1",label:"时间"},{prop:"name2",label:"差值率",unit:"(%)"},{prop:"name3",label:"变化系数"}],operations:[],showSummary:!1,operationWidth:"150px",pagination:{hide:!0}}),x=C({loading:!0,dataList:[],highlightCurrentRow:!0,columns:[{prop:"ts",label:"时间"},{prop:"max",label:"最大值"},{prop:"maxTs",label:"最大值发生时间"},{prop:"min",label:"最小值"},{prop:"minTs",label:"最小值发生时间"},{prop:"avg",label:"平均值"}],operations:[],showSummary:!1,operationWidth:"150px",pagination:{hide:!0}}),w=async()=>{var i,l;d.value=!0,_.loading=!0,x.loading=!0;const e=((i=b.value)==null?void 0:i.queryParams)||{},o={stationId:e.stationId,attrType:e.attrType,queryType:e.queryType,time:F(e.time).format("YYYY-MM-DD"),compareType:r.compareType},a=(l=(await ne(o)).data)==null?void 0:l.data;_.columns=a==null?void 0:a.baseTable.tableInfo.map(n=>({prop:n.columnValue,label:n.columnName,unit:n.unit?"("+n.unit+")":""})),_.dataList=a==null?void 0:a.baseTable.tableDataList,_.loading=!1,x.dataList=a==null?void 0:a.countTable,x.loading=!1,q(a==null?void 0:a.baseTable,o)},q=(e,o)=>{const t=r.attrTypeList.find(c=>(o==null?void 0:o.attrType)===c.value);J();const a={name:"",smooth:!0,data:[],type:"line",markPoint:{data:[{type:"max",name:"最大值",label:{fontSize:12,color:u().isDark?"#ffffff":"#000000"}},{type:"min",name:"最小值",label:{color:u().isDark?"#ffffff":"#000000"}}]}},i=e==null?void 0:e.tableDataList.map(c=>c.ts),l=A();l.series=[],l.yAxis[0].name=t==null?void 0:t.label.concat(t==null?void 0:t.unit),l.xAxis.data=i,e==null||e.tableInfo.filter(c=>{if(!["differenceRate","ts","changeRate"].includes(c.columnValue)){const N=JSON.parse(JSON.stringify(a));N.name=c.columnName,N.data=e==null?void 0:e.tableDataList.map(W=>W[c.columnValue]),l.series.push(N)}});const n=A();n.series=[],n.yAxis[0].name=t==null?void 0:t.label.concat(t==null?void 0:t.unit),n.xAxis.data=i;const m=JSON.parse(JSON.stringify(a));m.data=e==null?void 0:e.tableDataList.map(c=>c[o.time]),n.series.push(m);const y=A();y.series=[],y.yAxis[0].name="系数(%)",y.xAxis.data=i;const B=JSON.parse(JSON.stringify(a));B.data=e==null?void 0:e.tableDataList.map(c=>c.changeRate),y.series.push(B),U(()=>{h.value&&Y.listenTo(h.value,()=>{r.chartOption1=l,r.chartOption2=n,r.chartOption3=y,P()})})},J=()=>{var e,o,t;(e=D.value)==null||e.clear(),(o=O.value)==null||o.clear(),(t=L.value)==null||t.clear()},P=()=>{var e,o,t;(e=D.value)==null||e.resize(),(o=O.value)==null||o.resize(),(t=L.value)==null||t.resize(),d.value=!1},Q=()=>{var t;const e=((t=b.value)==null?void 0:t.queryParams)||{},o={stationId:e.stationId,attrType:e.attrType,queryType:e.queryType,time:F(e.time).format("YYYY-MM-DD"),compareType:r.compareType};se(o).then(a=>{const i=window.URL.createObjectURL(a.data),l=document.createElement("a");l.style.display="none",l.href=i,l.setAttribute("download","出水水质报表.xlsx"),document.body.appendChild(l),l.click()})};return X(async()=>{var t,a,i,l;const e=await I("水厂");r.storedStationList=e,console.log("state.storedStations",e);const o=(t=k.filters)==null?void 0:t.find(n=>(n==null?void 0:n.field)==="stationId");o.options=e,k.defaultParams={...k.defaultParams,stationId:(a=e[0])==null?void 0:a.id},r.storedStationName=(i=e[0])==null?void 0:i.label,(l=b.value)==null||l.resetForm(),await w()}),(e,o)=>{const t=le,a=oe,i=ae,l=te,n=$("VChart"),m=G;return T(),S("div",ie,[p(t,{ref_key:"refSearch",ref:b,config:s(k)},null,8,["config"]),g("div",ce,[g("div",pe,[p(l,{class:"left-card",title:" "},{right:v(()=>[p(a,{ref:"refForm",class:"left",config:s(V)},null,8,["config"])]),default:v(()=>[g("div",ue,[p(i,{ref_key:"refCardTable",ref:z,class:"card-table",config:s(_)},null,8,["config"])]),g("div",fe,[p(i,{ref_key:"refCardTable2",ref:M,class:"card-table",config:s(x)},null,8,["config"])])]),_:1})]),g("div",me,[p(l,{title:s(r).compareType===1?" 同比":s(r).compareType===2?" 环比":" 定基比分析",class:"card-chart"},{default:v(()=>[R((T(),S("div",{ref_key:"zxDiv",ref:h,class:"chart-box"},[p(n,{ref_key:"refChart1",ref:D,theme:s(u)().isDark?"blackBackground":"whiteBackground",option:s(r).chartOption1},null,8,["theme","option"])])),[[m,s(d)]])]),_:1},8,["title"]),p(l,{title:"阈值分析",class:"card-chart"},{default:v(()=>[R((T(),S("div",{ref_key:"zxDiv",ref:h,class:"chart-box"},[p(n,{ref_key:"refChart2",ref:O,theme:s(u)().isDark?"blackBackground":"whiteBackground",option:s(r).chartOption2},null,8,["theme","option"])])),[[m,s(d)]])]),_:1}),p(l,{title:"系数分析",class:"card-chart"},{default:v(()=>[R((T(),S("div",{ref_key:"zxDiv",ref:h,class:"chart-box"},[p(n,{ref_key:"refChart3",ref:L,theme:s(u)().isDark?"blackBackground":"whiteBackground",option:s(r).chartOption3},null,8,["theme","option"])])),[[m,s(d)]])]),_:1})])])])}}}),De=ee(de,[["__scopeId","data-v-bd09786a"]]);export{De as default};
