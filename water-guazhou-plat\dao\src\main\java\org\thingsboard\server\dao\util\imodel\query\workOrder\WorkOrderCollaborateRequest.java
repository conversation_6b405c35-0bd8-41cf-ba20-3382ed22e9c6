package org.thingsboard.server.dao.util.imodel.query.workOrder;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.workOrder.*;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

import java.util.Date;

@Getter
@Setter
public class WorkOrderCollaborateRequest extends WorkOrderCollaborateVerifyRequest {
    // 是否直接派发
    private String isDirectDispatch;

    // 协作审核人
    @NotNullOrEmpty
    private String nextProcessUserId;

    // 额外信息
    private String additionalInfo;

    // 备注
    private String remark;

    // 工单类型
    @NotNullOrEmpty
    private String type;

    // 协作备注
    private String collaborationRemark;

    @Override
    public boolean validateProcessLevel() {
        return Boolean.parseBoolean(isDirectDispatch);
    }

    public ReturnWrapper build(WorkOrder order, String tenantId) {
        Date now = new Date();

        WorkOrderCollaboration collaboration = new WorkOrderCollaboration();
        WorkOrderDetail detail = new WorkOrderDetail();
        WorkOrder subWorkOrder = null;
        WorkOrderDetail subWorkStage = null;

        detail.setProcessTime(now);
        detail.setType(WorkOrderStatus.COLLABORATION);
        detail.setProcessUserId(currentUserUUID());
        detail.setNextProcessUserId(nextProcessUserId);
        detail.setProcessAdditionalInfo(additionalInfo);
        detail.setProcessRemark(remark);

        collaboration.setOrderId(order.getId());
        collaboration.setTime(now);
        collaboration.setUserId(nextProcessUserId);
        collaboration.setTenantId(tenantId);
        collaboration.setType(type);
        collaboration.setRemark(collaborationRemark);
        collaboration.setStatus(WorkOrderCollaborationStatus.PENDING);
        if (parseBoolean(isDirectDispatch)) {
            setApproved("true");
            subWorkOrder = new WorkOrder();
            subWorkStage = process(subWorkOrder, collaboration, now, tenantId);
        }


        return new ReturnWrapper(detail, collaboration, subWorkOrder, subWorkStage);
    }
    public static class ReturnWrapper {
        private final WorkOrderDetail workOrderDetail;

        private final WorkOrderCollaboration workOrderCollaboration;

        private final WorkOrder subWorkOrder;

        private final WorkOrderDetail subWorkStage;

        public ReturnWrapper(WorkOrderDetail workOrderDetail, WorkOrderCollaboration workOrderCollaboration, WorkOrder subWorkOrder, WorkOrderDetail subWorkStage) {
            this.workOrderDetail = workOrderDetail;
            this.workOrderCollaboration = workOrderCollaboration;
            this.subWorkOrder = subWorkOrder;
            this.subWorkStage = subWorkStage;
        }

        public WorkOrderDetail getWorkOrderDetail() {
            return workOrderDetail;
        }

        public WorkOrderCollaboration getWorkOrderCollaboration() {
            return workOrderCollaboration;
        }

        public WorkOrder getSubWorkOrder() {
            return subWorkOrder;
        }

        public WorkOrderDetail getSubWorkStage() {
            return subWorkStage;
        }

    }

}
