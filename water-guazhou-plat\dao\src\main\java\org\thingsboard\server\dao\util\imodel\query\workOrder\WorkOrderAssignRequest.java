package org.thingsboard.server.dao.util.imodel.query.workOrder;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrder;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrderDetail;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrderLevel;
import org.thingsboard.server.dao.util.imodel.query.IStarHttpRequest;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;

import java.util.Date;

import static org.thingsboard.server.dao.model.sql.workOrder.WorkOrderStatus.ASSIGN;

@Getter
@Setter
public class WorkOrderAssignRequest extends SaveRequest<WorkOrderDetail> {
    // 下一步的处理人
    private String stepProcessUserId;

    // 处理级别
//    private WorkOrderLevel processLevel;
    private Integer processLevel;

    private String processLevelLabel;

    // endregion

    @Override
    public String valid(IStarHttpRequest request) {
        if (stepProcessUserId == null) {
            return "派发的指定用户" + stepProcessUserId + "为空";
        }

        stepProcessUserId = parseUUID(stepProcessUserId);

        return null;
    }

    public WorkOrderDetail process(WorkOrder order) {
        Date now = new Date();
        // region order
        order.setEstimatedFinishTime(new Date(now.getTime() + (order.getProcessLevel() * 60 * 1000)));
        // 下一步处理人
        order.setStepProcessUserId(stepProcessUserId);
        order.setProcessUserId(stepProcessUserId);
        order.setStatus(ASSIGN);
        order.setUpdateTime(new Date());
        // endregion

        // region detail
        WorkOrderDetail detail = new WorkOrderDetail();
        detail.setMainId(order.getId());
        detail.setType(ASSIGN);
        // 记录当前处理用户为处理人
        detail.setProcessUserId(currentUserUUID());
        detail.setProcessTime(new Date());
        // 下一步处理人需要传入
        detail.setNextProcessUserId(stepProcessUserId);
        // endregion
        return detail;
    }

    @Override
    public WorkOrderDetail build() {
        return null;
    }

    @Override
    public WorkOrderDetail update(String id) {
        return null;
    }
}
