<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.maintainCircuit.maintain.MaintainTaskMMapper">

    <resultMap id="statisticsMap" type="java.util.HashMap">
        <result property="id" column="id"/>
        <result property="userName" column="userName"/>
        <result property="totalTask" column="totalTask"/>
        <result property="noReceiveTask" column="noReceiveTask"/>
        <result property="execTask" column="execTask"/>
        <result property="prepAudit" column="prepAudit"/>
        <result property="inTimeComplete" column="inTimeComplete"/>
        <result property="outTimeComplete" column="outTimeComplete"/>
        <result property="planTotal" column="planTotal"/>
        <result property="tempTotal" column="tempTotal"/>
    </resultMap>

    <select id="getList" resultType="org.thingsboard.server.dao.model.sql.maintainCircuit.maintain.MaintainTaskM">
        select a.*, b.first_name as creatorName, b1.first_name as auditorName, td.id auditorDepartment, td.name
        auditorDepartmentName, b2.first_name
        as userName, tctm.name as teamName
        from tb_device_maintain_task_m a
        left join tb_user b on a.creator = b.id
        left join tb_user b1 on a.auditor = b1.id
        left join tb_department td on b1.department_id =td.id
        left join tb_user b2 on a.user_id = b2.id
        left join tb_maintain_circuit_team_m tctm on a.team_id = tctm.id
        where a.code like '%' || #{code} || '%'
        and a.name like '%' || #{planName} || '%'
        and a.audit_status like '%' || #{auditStatus} || '%'
        and b2.first_name like '%' || #{userName} || '%'
        and b2.id like '%' || #{userId} || '%'
        and tctm.name like '%' || #{teamName} || '%'
        and a.tenant_id = #{tenantId}

        <if test="status != ''">
            and a.status in
            <foreach collection="status.split(',')" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>


        <if test="startStartTime != null">
            and a.start_time &gt;= #{startStartTime}
        </if>

        <if test="startEndTime != null">
            and a.start_time &lt;= #{startEndTime}
        </if>

        <if test="endStartTime != null">
            and a.end_time &gt;= #{endStartTime}
        </if>

        <if test="endEndTime != null">
            and a.end_time &lt;= #{endEndTime}
        </if>

        order by a.create_time desc
        offset (#{page} - 1) * #{size} limit #{size}
    </select>

    <select id="getListCount" resultType="int">

        select count(*)
        from tb_device_maintain_task_m a
        left join tb_user b on a.creator = b.id
        left join tb_user b1 on a.auditor = b1.id
        left join tb_department td on b1.department_id =td.id
        left join tb_user b2 on a.user_id = b2.id
        left join tb_maintain_circuit_team_m tctm on a.team_id = tctm.id
        where a.code like '%' || #{code} || '%'
        and a.name like '%' || #{planName} || '%'
        and a.status like '%' || #{status} || '%'
        and a.audit_status like '%' || #{auditStatus} || '%'
        and b2.first_name like '%' || #{userName} || '%'
        and b2.id like '%' || #{userId} || '%'
        and tctm.name like '%' || #{teamName} || '%'
        and a.tenant_id = #{tenantId}

        <if test="status != ''">
            and a.status in
            <foreach collection="status.split(',')" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>


        <if test="startStartTime != null">
            and a.start_time &gt;= #{startStartTime}
        </if>

        <if test="startEndTime != null">
            and a.start_time &lt;= #{startEndTime}
        </if>

        <if test="endStartTime != null">
            and a.end_time &gt;= #{endStartTime}
        </if>

        <if test="endEndTime != null">
            and a.end_time &lt;= #{endEndTime}
        </if>
    </select>

    <select id="statistics" resultMap="statisticsMap">

        select a.id,
        a.first_name as userName,
        b.total as totalTask,
        c.total as noReceiveTask,
        d.total as execTask,
        e.total prepAudit,
        f.total as inTimeComplete,
        g.total as outTimeComplete,
        h.total as planTotal,
        i.total as tempTotal
        from tb_user a

        left join (
        select a.id, count(b.id) as total
        from tb_user a
        left join tb_device_maintain_task_m b on b.user_id = a.id
        <if test="startTime != null">
            and b.create_time &gt;= #{startTime}
        </if>

        <if test="endTime != null">
            and b.create_time &lt;= #{endTime}
        </if>
        where a.tenant_id = #{tenantId}
        group by a.id
        ) b on a.id = b.id
        left join (
        select a.id, count(b.id) as total
        from tb_user a
        left join tb_device_maintain_task_m b on b.user_id = a.id
        and b.status = '0'
        <if test="startTime != null">
            and b.create_time &gt;= #{startTime}
        </if>

        <if test="endTime != null">
            and b.create_time &lt;= #{endTime}
        </if>
        where a.tenant_id = #{tenantId}
        group by a.id
        ) c on a.id = c.id

        left join (
        select a.id, count(b.id) as total
        from tb_user a
        left join tb_device_maintain_task_m b on b.user_id = a.id
        and b.status = '1'
        <if test="startTime != null">
            and b.create_time &gt;= #{startTime}
        </if>

        <if test="endTime != null">
            and b.create_time &lt;= #{endTime}
        </if>
        where a.tenant_id = #{tenantId}
        group by a.id
        ) d on a.id = d.id

        left join (
        select a.id, count(b.id) as total
        from tb_user a
        left join tb_device_maintain_task_m b on b.user_id = a.id
        and (b.status = '2' or b.status = '3') and b.audit_status = '0'
        <if test="startTime != null">
            and b.create_time &gt;= #{startTime}
        </if>

        <if test="endTime != null">
            and b.create_time &lt;= #{endTime}
        </if>
        where a.tenant_id = #{tenantId}
        group by a.id
        ) e on a.id = e.id

        left join (
        select a.id, count(b.id) as total
        from tb_user a
        left join tb_device_maintain_task_m b on b.user_id = a.id
        and b.status = '2'
        <if test="startTime != null">
            and b.create_time &gt;= #{startTime}
        </if>

        <if test="endTime != null">
            and b.create_time &lt;= #{endTime}
        </if>
        where a.tenant_id = #{tenantId}
        group by a.id
        ) f on a.id = f.id

        left join (
        select a.id, count(b.id) as total
        from tb_user a
        left join tb_device_maintain_task_m b on b.user_id = a.id
        and b.status = '3'
        <if test="startTime != null">
            and b.create_time &gt;= #{startTime}
        </if>

        <if test="endTime != null">
            and b.create_time &lt;= #{endTime}
        </if>
        where a.tenant_id = #{tenantId}
        group by a.id
        ) g on a.id = g.id

        left join (
        select a.id, count(b.id) as total
        from tb_user a
        left join tb_device_maintain_task_m b on b.user_id = a.id
        and b.type = '计划任务'
        <if test="startTime != null">
            and b.create_time &gt;= #{startTime}
        </if>

        <if test="endTime != null">
            and b.create_time &lt;= #{endTime}
        </if>
        where a.tenant_id = #{tenantId}
        group by a.id
        ) h on a.id = h.id

        left join (
        select a.id, count(b.id) as total
        from tb_user a
        left join tb_device_maintain_task_m b on b.user_id = a.id
        and b.type = '临时任务'
        <if test="startTime != null">
            and b.create_time &gt;= #{startTime}
        </if>

        <if test="endTime != null">
            and b.create_time &lt;= #{endTime}
        </if>
        where a.tenant_id = #{tenantId}
        group by a.id
        ) i on a.id = i.id

        where a.tenant_id = #{tenantId}
        order by b.total desc

    </select>


    <select id="getNotCompleteNum" resultType="java.lang.Integer">
        select ifnull(count(*), 0) as num
        from tb_device_maintain_task_m
        where user_id = #{userId} and (status = '0' or status = '1') and tenant_id = #{tenantId}
    </select>

    <select id="getById" resultType="org.thingsboard.server.dao.model.sql.maintainCircuit.maintain.MaintainTaskM">
        select a.*,
               b.first_name  as creatorName,
               b1.first_name as auditorName,
               td.id            auditorDepartment,
               td.name          auditorDepartmentName,
               b2.first_name
                             as userName,
               tctm.name     as teamName
        from tb_device_maintain_task_m a
                 left join tb_user b on a.creator = b.id
                 left join tb_user b1 on a.auditor = b1.id
                 left join tb_department td on b1.department_id = td.id
                 left join tb_user b2 on a.user_id = b2.id
                 left join tb_maintain_circuit_team_m tctm on a.team_id = tctm.id
        where a.id = #{id}
    </select>



    <select id="coutDaijieshou" resultType="java.lang.Integer">
        select ifnull(count(*), 0) as num
        from tb_device_maintain_task_m
        where user_id = #{userId} and (status = '0' or status = '1')
    </select>

</mapper>