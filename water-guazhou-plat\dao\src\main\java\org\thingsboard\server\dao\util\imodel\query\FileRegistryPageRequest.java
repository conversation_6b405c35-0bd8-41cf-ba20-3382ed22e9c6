package org.thingsboard.server.dao.util.imodel.query;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.fileRegistry.FileRegistry;

@Getter
@Setter
public class FileRegistryPageRequest extends AdvancedPageableQueryEntity<FileRegistry, FileRegistryPageRequest> {
    // 文件名
    private String fileName;

    // 文件标签
    private String label;

    // 文件宿主
    private String host;

}
