package org.thingsboard.server.dao.util.imodel.query.store;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.store.ConstructionProject;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;

import java.util.Date;

@Getter
@Setter
public class ConstructionProjectPageRequest extends AdvancedPageableQueryEntity<ConstructionProject, ConstructionProjectPageRequest> {
    // 项目编码
    private String code;

    // 项目名称
    private String name;

    // 项目地址
    private String address;

    // 施工部门ID
    private String orgId;

    // 施工方
    private String constructionSide;

    // 创建时间
    private String createTime;

    public Date getCreateTime() {
        return toDate(createTime);
    }
}
