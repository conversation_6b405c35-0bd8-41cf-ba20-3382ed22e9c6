<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.report.ReportComponentMapper">

    <select id="getList" resultType="org.thingsboard.server.dao.model.sql.report.ReportComponent">
        select a.*, b.name as databaseName from tb_report_component a
        left join tb_report_database b on content::json->>'databaseId' = b.id
        where a.tenant_id = #{param.tenantId}
         <if test="param.name != null and param.name != ''">
             and content::json->>'name' like '%'||#{param.name}||'%'
         </if>
         <if test="param.label != null and param.label != ''">
             and content::json->>'label' like '%'||#{param.label}||'%'
         </if>
         order by a.create_time desc
    </select>
</mapper>