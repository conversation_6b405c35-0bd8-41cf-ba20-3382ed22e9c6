import{u as h}from"./useDetector-BRcb7GRN.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import{d as g,r as y,c as b,o as C,ay as T,g as w,n as x,q as k,i as s,p as o,bh as m,G as f,C as D}from"./index-r0dFAfgr.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./project-DUuzYgGl.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import{u as V}from"./usePartition-DkcY9fQ2.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./index-0NlGN6gS.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./LayerHelper-Cn-iiqxI.js";import"./pipe-nogVzCHG.js";import"./dma-SMxrzG7b.js";import"./hookupDevice-Bcbk7s68.js";const z={class:"chart"},S={class:"area area-top"},B={class:"circle"},F={class:"info"},M={class:"flow"},N={class:"area area-bottom"},O={class:"circle"},A={class:"info"},E={class:"flow"},J=g({__name:"FQTJ",setup(P){const e=y({curType:"level",level:{count:0,flow:0},dma:{count:0,flow:0},cxcOption:{}}),a=V(),c=(r,t,i)=>{var p;r.path.length<=2&&(t.push({name:r.label}),r.path.length===2?e.level.count++:(p=r.children)==null||p.map(n=>{var d;const _=((d=n.children)==null?void 0:d.length)||0;i.push({source:r.label,target:n.label,value:_}),c(n,t,i)}))},l=b(),u=h(),v=()=>{const r=[],t=[];a.Tree.value.map(i=>c(i,r,t)),e.cxcOption={tooltip:{trigger:"item"},series:[{type:"sankey",data:r,links:t,left:50,top:60,right:100,bottom:60,lineStyle:{color:"source",curveness:.5},itemStyle:{color:"#1f77b4",borderColor:"#1f77b4"},label:{color:"#fff",fontFamily:"Arial",fontSize:10}}]}};return C(async()=>{var i;const r=a.getTree(),t=a.getDevices({page:1,size:9999,type:"1"});await Promise.all([r,t]),v(),(i=l.value)==null||i.resize(),u.listenToMush(document.documentElement,()=>{var p;(p=l.value)==null||p.resize()})}),(r,t)=>{const i=T("VChart");return w(),x("div",z,[k(i,{ref_key:"refChart",ref:l,option:s(e).cxcOption},null,8,["option"]),o("div",S,[o("div",B,m(s(e).level.count),1),o("div",F,[t[1]||(t[1]=o("div",{class:"title"}," 一级分区 ",-1)),o("div",M,[t[0]||(t[0]=f(" 流量计数")),o("span",null,m(s(e).level.flow),1)])])]),o("div",N,[o("div",O,m(s(e).dma.count),1),o("div",A,[t[3]||(t[3]=o("div",{class:"title"}," DMA分区 ",-1)),o("div",E,[t[2]||(t[2]=f(" 流量计数")),o("span",null,m(s(e).dma.flow),1)])])])])}}}),ro=D(J,[["__scopeId","data-v-1c816130"]]);export{ro as default};
