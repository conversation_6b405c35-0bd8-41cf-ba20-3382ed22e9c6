package org.thingsboard.server.dao.model.sql.smartProduction.safeProduction;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@TableName("sp_production_task")
@NoArgsConstructor
@AllArgsConstructor
public class ProductionTask {

    private String id;

    private String code;

    private String name;

    private String type;

    private String execUser;

    @TableField(exist = false)
    private String execUserName;

    private Date planStartTime;

    private Date planEndTime;

    private String level;

    @TableField(exist = false)
    private String levelName;

    private Integer processLevel;

    private String processLevelLabel;

    private String status;

    private String coordinate;

    private String address;

    private String remark;

    private String reviewer;

    @TableField(exist = false)
    private String reviewerName;

    private String reviewRemark;

    private Date reviewTime;

    private String files;

    private String creator;

    @TableField(exist = false)
    private String creatorName;

    private Date createTime;

    private String tenantId;

    private String processRemark;

    private String processFiles;

    private Date realStartTime;

    private Date realEndTime;



}
