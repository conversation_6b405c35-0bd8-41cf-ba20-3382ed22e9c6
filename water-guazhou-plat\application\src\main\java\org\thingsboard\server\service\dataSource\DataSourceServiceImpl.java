package org.thingsboard.server.service.dataSource;

import com.datastax.driver.core.utils.UUIDs;
import org.codehaus.jackson.map.ObjectMapper;
import org.codehaus.jackson.type.TypeReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.Device;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.VO.DefaultMapObject;
import org.thingsboard.server.common.data.constantsAttribute.PropAttribute;
import org.thingsboard.server.common.data.dataSource.*;
import org.thingsboard.server.common.data.exception.ThingsboardErrorCode;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.DeviceId;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.kv.TsKvEntry;
import org.thingsboard.server.common.data.utils.DateUtils;
import org.thingsboard.server.common.data.utils.StringUtils;
import org.thingsboard.server.dao.dataSource.*;
import org.thingsboard.server.dao.data_source_relation.DataSourceRelationDao;
import org.thingsboard.server.dao.device.DeviceService;
import org.thingsboard.server.dao.model.sql.DataSourceEntity;
import org.thingsboard.server.dao.model.sql.DataSourceRelationEntity;
import org.thingsboard.server.dao.model.sql.OriginDataEntity;
import org.thingsboard.server.dao.model.sql.RestApiEntity;
import org.thingsboard.server.dao.origin.OriginDataService;
import org.thingsboard.server.dao.timeseries.TimeseriesService;
import org.thingsboard.server.service.rpc.DeviceRpcService;

import javax.transaction.Transactional;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.ExecutionException;

/**
 * <AUTHOR>
 * @date 2020/2/26 15:00
 */
@Service
public class DataSourceServiceImpl implements DataSourceService {


    @Autowired
    private DataSourceDao dataSourceDao;

    @Autowired
    private RestApiDao restApiDao;

    @Autowired
    private DeviceDataSourceProcess deviceDataSourceProcess;

    @Autowired
    private MidDataSourceProcess midDataSourceProcess;

    @Autowired
    private DataSourceRelationDao dataSourceRelationDao;

    @Autowired
    private OriginDataService originDataService;

    @Autowired
    private TimeseriesService timeseriesService;

    @Autowired
    private JobService jobService;

    @Autowired
    private DeviceRpcService deviceRpcService;

    @Autowired
    private DeviceService deviceService;

//    @Autowired
    //private Scheduler scheduler;

    @Override
    public List<DataSourceEntity> findByProjectIdAndType(String projectId, DataSourceType type) {
        List<DataSourceEntity> dataSourceEntities = dataSourceDao.findByProjectIdAndType(projectId, type);
        List<BaseDataSource> baseDataSourceList = new ArrayList<>();
        dataSourceEntities.forEach(dataSourceEntity -> baseDataSourceList.add(convertToDataSource(dataSourceEntity)));
        return dataSourceEntities;
    }

    @Override
    public List<DataSourceEntity> findAllByOriginatorId(String originatorId, DataSourceType type) {
        return dataSourceDao.findAllByOriginatorIdAndType(originatorId, type);
    }

    /**
     * 将DataSourceEntity转换为dataSource
     *
     * @param dataSourceEntity 数据源实体
     * @return datasource
     */
    private BaseDataSource convertToDataSource(DataSourceEntity dataSourceEntity) {
        BaseDataSource baseDataSource = BaseDataSource.builder()
                .id(dataSourceEntity.getId())
                .name(dataSourceEntity.getSourceName())
                .type(DataSourceType.MID_SOURCE)
                .enable(dataSourceEntity.getEnable())
                .format(dataSourceEntity.getFormat())
                .updateTime(dataSourceEntity.getUpdateTime())
                .build();
        switch (DataSourceType.valueOf(dataSourceEntity.getType())) {
            case DEVICE_SOURCE: {
                baseDataSource = new DeviceBaseDataSource(baseDataSource, dataSourceEntity.getDeviceId(), dataSourceEntity.getProperty());
                break;
            }
            case MID_SOURCE: {
                baseDataSource.setType(DataSourceType.MID_SOURCE);
                break;
            }
            case SYSTEM_SOURCE: {
                baseDataSource.setType(DataSourceType.SYSTEM_SOURCE);
                break;
            }
            case RESTAPI_SOURCE: {
                baseDataSource = new RestApiBaseDataSource(baseDataSource, dataSourceEntity.getURL(), dataSourceEntity.getMethod(), dataSourceEntity.getParams(), dataSourceEntity.getParsingAttribute(), dataSourceEntity.getParsingPath());
                break;
            }
            case STATISTICS_SOURCE:
            default: {
                baseDataSource = new StatisticsBaseDataSource(baseDataSource, dataSourceEntity.getFormula(), dataSourceEntity.getTemplate(), dataSourceEntity.getFrequency());
                break;
            }
        }
        return baseDataSource;
    }

    @Override
    @Transactional
    public List<DataSourceEntity> save(TenantId tenantId, List<DataSourceEntity> baseDataSources, String projectId, String type) throws ThingsboardException {
        List<DataSourceEntity> baseDataSourceList = new ArrayList<>();
        try {
            baseDataSources.forEach(dataSource -> {
                        if (dataSource.getType().equalsIgnoreCase(DataSourceType.STATISTICS_SOURCE.name()) && dataSource.getProperty() == null) {
                            dataSource.setProperty(UUIDConverter.fromTimeUUID(UUIDs.timeBased()));
                        }
                        dataSource.setUpdateTime(System.currentTimeMillis());
                        if (dataSource.getId() == null) {
                            dataSource.setId(UUIDConverter.fromTimeUUID(UUIDs.timeBased()));
                            //当新增数据源时，进行数据机源与发起者的绑定
                            dataSource.setTenantId(UUIDConverter.fromTimeUUID(tenantId.getId()));
                            DataSourceEntity saveEntity = dataSourceDao.save(dataSource);
                            baseDataSourceList.add(saveEntity);
                            dataSourceRelationDao.mountRelation(projectId, saveEntity.getId(), type);
                        } else {
                            baseDataSourceList.add(dataSourceDao.save(dataSource));
                        }
                        //数据更新完成，更新定时任务配置
                        if (dataSource.getEnable() == 0) {
                            jobService.deleteTrigger(dataSource, projectId);
                        } else {
                            jobService.modifyTrigger(dataSource, projectId);
                        }
                    }
            );
//            deviceRpcService.sendDataToMqttDevice(projectId, Collections.singleton(saveEntity), DataConstants.DATA_TYPE_DATA_SOURCE);
        } catch (Exception e) {
            throw new ThingsboardException(ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }
        return baseDataSourceList;
    }

    @Override
    public boolean deleteDataSource(String dataSourceId) {
        DataSourceEntity dataSourceEntity = dataSourceDao.findById(dataSourceId);
        DataSourceRelationEntity dataSourceRelationEntity = dataSourceRelationDao.findByDataSourceId(dataSourceId);
        if (dataSourceRelationEntity != null) {
            jobService.deleteTrigger(dataSourceEntity, dataSourceRelationEntity.getOriginateId());
        }
        return dataSourceDao.deleteDataSource(dataSourceId);
    }


    @Override
    public List<DataFromDataSource> findDataByDataSource(DataSourceRequest dataSourceRequest) {
        List<DataFromDataSource> dataFromDataSources = new ArrayList<>();
        List<String> dataSourceIds = dataSourceRequest.getDataSourceIds();
        List<DataSourceEntity> dataSourceEntityList = dataSourceDao.findByIdIn(dataSourceIds);
        for (DataSourceEntity dataSourceEntity : dataSourceEntityList) {
            switch (DataSourceType.valueOf(dataSourceEntity.getType())) {
                case DEVICE_SOURCE: {
                    dataFromDataSources.add(deviceDataSourceProcess.processDeviceDataSource(dataSourceEntity, dataSourceRequest));
                    break;
                }
                case PREPARATION_SOURCE:
                case RESTAPI_SOURCE:
                case STATISTICS_SOURCE:
                    dataFromDataSources.add(deviceDataSourceProcess.processRestApiDataSource(dataSourceEntity, dataSourceRequest));
                    break;
                case MID_SOURCE:
                    dataFromDataSources.add(midDataSourceProcess.processMidDataSource(dataSourceEntity, dataSourceRequest));
                    break;
                default:
            }
        }
        return dataFromDataSources;
    }

    @Override
    public List<DataFromDataSource> findLastDataByDataSource(DataSourceRequest dataSourceRequest) {
        List<DataFromDataSource> dataFromDataSources = new ArrayList<>();
        List<String> dataSourceIds = dataSourceRequest.getDataSourceIds();
        List<DataSourceEntity> dataSourceEntityList = dataSourceDao.findByIdIn(dataSourceIds);
        for (DataSourceEntity dataSourceEntity : dataSourceEntityList) {
            switch (DataSourceType.valueOf(dataSourceEntity.getType())) {
                case DEVICE_SOURCE:
                    dataFromDataSources.add(deviceDataSourceProcess.processDeviceLastData(dataSourceEntity));
                    break;
                case SYSTEM_SOURCE:
                    dataFromDataSources.add(midDataSourceProcess.processSystemLastDataSource(dataSourceEntity));
                    break;
                case RESTAPI_SOURCE:
                case STATISTICS_SOURCE:
                    dataFromDataSources.add(midDataSourceProcess.processLastDataSource(dataSourceEntity));
                    break;
                case PREPARATION_SOURCE:
                    dataFromDataSources.add(midDataSourceProcess.processPreparationLastDataSource(dataSourceEntity));
                    break;
                case MID_SOURCE:
                    dataFromDataSources.add(midDataSourceProcess.processLastMidDataSource(dataSourceEntity));
                    break;
                default:
            }
        }
        return dataFromDataSources;
    }

    @Override
    public String findDataByDataSourceById(String dataSourceId) {
        DataSourceEntity dataSourceEntity = dataSourceDao.findById(dataSourceId);
        if (dataSourceEntity == null) {
            return "0";
        }
        String result = "0";
        switch (DataSourceType.valueOf(dataSourceEntity.getType())) {
            case DEVICE_SOURCE: {
                TsKvEntry deviceLastData = deviceDataSourceProcess.getDeviceLastData(dataSourceEntity);
                if (deviceLastData == null) {
                    return null;
                }
                return deviceLastData.getValueAsString();
            }
            case SYSTEM_SOURCE:
                return String.valueOf(DateUtils.getSysDataSource(System.currentTimeMillis(), dataSourceEntity.getProperty()));
            case RESTAPI_SOURCE:
            case STATISTICS_SOURCE: {
                TsKvEntry tsKvEntry = timeseriesService.findLatestByKey(new DeviceId(UUIDConverter.fromString(dataSourceEntity.getId())), dataSourceEntity.getProperty());
                if (tsKvEntry != null) {
                    return tsKvEntry.getValueAsString();
                }
                break;
            }
            case PREPARATION_SOURCE: {
                TsKvEntry tsKvEntry = timeseriesService.findLatestByKey(new DeviceId(UUIDConverter.fromString(dataSourceEntity.getId())), dataSourceEntity.getPreparation());
                if (tsKvEntry != null) {
                    return tsKvEntry.getValueAsString();
                }
                break;
            }
            case MID_SOURCE: {
                OriginDataEntity dataEntity = originDataService.getLastOriginDataFormId(dataSourceEntity.getId());
                if (dataEntity != null) {
                    return dataEntity.getValue();
                }
            }
            break;
            default:
        }
        return result;
    }

    @Override
    public DataFromDataSource findDataFromDataSourceByDataSourceById(String dataSourceId) {
        DataSourceEntity dataSourceEntity = dataSourceDao.findById(dataSourceId);
        DataFromDataSource dataFromDataSource = null;
        String result = "0";
        if (dataSourceEntity.getType().equals(DataSourceType.DEVICE_SOURCE.name())) {
            TsKvEntry tsKvEntry = timeseriesService.findLatest(new DeviceId(UUIDConverter.fromString(dataSourceEntity.getDeviceId())), dataSourceEntity.getProperty());
            dataFromDataSource = new DataFromDataSource(dataSourceId, dataSourceEntity.getSourceName(), Collections.singletonList(DefaultMapObject.builder()
                    .ts(String.valueOf(tsKvEntry.getTs()))
                    .value(tsKvEntry.getValueAsString())
                    .build()));
        } else {
            OriginDataEntity originDataEntity = originDataService.getLastOriginDataFormId(dataSourceId);
            dataFromDataSource = new DataFromDataSource(dataSourceId, dataSourceEntity.getSourceName(),
                    Collections.singletonList(DefaultMapObject.builder()
                            .ts(String.valueOf(originDataEntity.getUpdateTime()))
                            .value(originDataEntity.getValue())
                            .build()));
        }
        return dataFromDataSource;
    }

    @Override
    public boolean setOriginData(OriginDataEntity originDataEntity) {
        DataSourceRelationEntity relationEntity = dataSourceRelationDao.findByDataSourceId(originDataEntity.getDataSourceId());
        originDataEntity.setUpdateTime(System.currentTimeMillis());
        OriginDataEntity save = originDataService.saveOriginData(originDataEntity);
        deviceRpcService.sendDataToMqttDevice(relationEntity.getOriginateId(), Collections.singleton(save), DataConstants.DATA_TYPE_ORIGIN_DATA);
        return true;
    }

    @Override
    public boolean mountPreparation(TenantId tenantId, List<PreparationVO> preparations, String originatorId, String entityType) {
        //查询该发起者的所有预设值记录
        List<DataSourceEntity> dataSourceEntities = dataSourceDao.findAllByOriginatorIdAndType(originatorId, DataSourceType.PREPARATION_SOURCE);
        Map<String, DataSourceEntity> map = new HashMap<>();
        dataSourceEntities.forEach(dataSourceEntity -> {
            dataSourceEntity.setEnable(0);
            map.put(dataSourceEntity.getPreparation(), dataSourceEntity);
        });
        preparations.forEach(preparation -> {
            if (map.containsKey(preparation.getPreparation())) {
                map.get(preparation.getPreparation()).setEnable(1);
                map.get(preparation.getPreparation()).setFrequency(preparation.getFrequency());
            } else {
                map.put(preparation.getPreparation(), DataSourceEntity.builder().sourceName(preparation.getSourceName())
                        .enable(1)
                        .updateTime(System.currentTimeMillis())
                        .preparation(preparation.getPreparation())
                        .type(DataSourceType.PREPARATION_SOURCE.name())
                        .tenantId(UUIDConverter.fromTimeUUID(tenantId.getId()))
                        .frequency(preparation.getFrequency()).build());

            }
        });
        return saveMaps(tenantId, originatorId, map, entityType);
    }

    @Override
    public boolean saveDeviceDataSource(TenantId tenantId, String deviceId, String prop) {
        //查询该发起者的所有预设值记录
        List<DataSourceEntity> dataSourceEntities = dataSourceDao.findAllByOriginatorIdAndType(deviceId, DataSourceType.DEVICE_SOURCE);
        Map<String, DataSourceEntity> map = new HashMap<>();
        dataSourceEntities.forEach(dataSourceEntity -> {
            dataSourceEntity.setEnable(0);
            map.put(dataSourceEntity.getProperty(), dataSourceEntity);
        });
        try {
            List<PropAttribute> propAttributes = new ObjectMapper().readValue(prop, new TypeReference<List<PropAttribute>>() {
            });
            propAttributes.forEach(preparation -> {
                if (map.containsKey(preparation.getPropertyCategory())) {
                    map.get(preparation.getPropertyCategory()).setEnable(1);
                    //更新该条数据的单位
                    map.get(preparation.getPropertyCategory()).setUnit(preparation.getUnit());
                    map.get(preparation.getPropertyCategory()).setSourceName(preparation.getName());
                    map.get(preparation.getPropertyCategory()).setStatisticsType(preparation.getStatType());
                } else {
                    map.put(preparation.getPropertyCategory(), DataSourceEntity.builder().sourceName(preparation.getName())
                            .enable(1)
                            .updateTime(System.currentTimeMillis())
                            .deviceId(deviceId)
                            .unit(preparation.getUnit())
                            .dataType(preparation.getPropertyType() == null ? "read" : preparation.getPropertyType().equals("3") ? "write" : "read")
                            .type(DataSourceType.DEVICE_SOURCE.name())
                            .property(preparation.getPropertyCategory())
                            .build());
                }
            });
            return saveMaps(tenantId, deviceId, map, DataConstants.DEVICE);

        } catch (IOException e) {
            e.printStackTrace();
            return false;
        }
    }

    @Override
    public List<ImmediateData> getDeviceSourceImmediateData(String originatorId) {
        List<ImmediateData> dataList = new ArrayList<>();
        List<DataSourceEntity> list = findByProjectIdAndType(originatorId, DataSourceType.DEVICE_SOURCE);
        try {
            List<TsKvEntry> tsKvEntities = timeseriesService.findAllLatest(null, new DeviceId(UUIDConverter.fromString(originatorId))).get();
            if (tsKvEntities != null && tsKvEntities.size() > 0) {
                Map<String, TsKvEntry> tsKvEntryMap = new LinkedHashMap<>();
                tsKvEntities.forEach(tsKvEntry -> tsKvEntryMap.put(tsKvEntry.getKey(), tsKvEntry));
                list.forEach(dataSourceEntity -> {
                    if (tsKvEntryMap.containsKey(dataSourceEntity.getProperty())) {
                        dataList.add(ImmediateData.builder()
                                .name(dataSourceEntity.getSourceName())
                                .value(tsKvEntryMap.get(dataSourceEntity.getProperty()).getValueAsString())
                                .unit(dataSourceEntity.getUnit())
                                .ts(tsKvEntryMap.get(dataSourceEntity.getProperty()).getTs()).build());
                    } else {
                        dataList.add(ImmediateData.builder()
                                .name(dataSourceEntity.getSourceName())
                                .value(null)
                                .unit(dataSourceEntity.getUnit())
                                .build());
                    }
                });
            } else {
                list.forEach(dataSourceEntity -> {
                    dataList.add(ImmediateData.builder()
                            .name(dataSourceEntity.getSourceName())
                            .unit(dataSourceEntity.getUnit())
                            .build());
                });
            }
        } catch (InterruptedException e) {
            e.printStackTrace();
        } catch (ExecutionException e) {
            e.printStackTrace();
        }
        return dataList;
    }

    @Override
    public List<DataSourceEntity> findByTypeAndTenantId(String type, String tenantId) {
        return dataSourceDao.findByTypeAndTenantId(type, tenantId);
    }

    @Override
    public int countByOriginatorIdAndName(String originatorId, String name) {
        return dataSourceDao.countByOriginatorIdAndName(originatorId, name);
    }

    @Override
    public RestApiEntity saveRestApi(RestApiEntity restApiEntity) {
        if (!StringUtils.checkNotNull(restApiEntity.getId())) {
            restApiEntity.setCreateTime(System.currentTimeMillis());
        }
        RestApiEntity save = restApiDao.save(restApiEntity);
        //更新数据源设置
        List<DataSourceEntity> dataSourceEntities = findAllByOriginatorId(save.getId(), DataSourceType.RESTAPI_SOURCE);
        Map<String, DataSourceEntity> map = new HashMap<>();
        dataSourceEntities.forEach(dataSourceEntity -> {
            dataSourceEntity.setEnable(0);
            map.put(dataSourceEntity.getProperty(), dataSourceEntity);
        });
        try {
            List<RestApiAttribute> propAttributes = new ObjectMapper().readValue(save.getParsingAttribute(), new TypeReference<List<RestApiAttribute>>() {
            });
            propAttributes.forEach(preparation -> {
                if (map.containsKey(preparation.getAttrProperty())) {
                    map.get(preparation.getAttrProperty()).setEnable(1);
                    //更新该条数据的单位
                    map.get(preparation.getAttrProperty()).setUnit(preparation.getUnit());
                    map.get(preparation.getAttrProperty()).setSourceName(preparation.getAttrName());
                    map.get(preparation.getAttrProperty()).setParsingPath(preparation.getAttrPath());
                } else {
                    map.put(preparation.getAttrProperty(), DataSourceEntity.builder().sourceName(preparation.getAttrName())
                            .enable(1)
                            .updateTime(System.currentTimeMillis())
                            .unit(preparation.getUnit())
                            .dataType(preparation.getDataType())
                            .type(DataSourceType.RESTAPI_SOURCE.name())
                            .property(preparation.getAttrProperty())
                            .parsingPath(preparation.getAttrPath())
                            .build());
                }
            });
            saveMaps(new TenantId(UUIDConverter.fromString(save.getTenantId())), save.getId(), map, DataSourceType.RESTAPI_SOURCE.name());
            jobService.modifyRestApi(restApiEntity);
        } catch (Exception e) {
            e.getMessage();
        }
        return save;
    }

    @Override
    public List<RestApiEntity> getRestApiByProjectId(String projectId) {
        return restApiDao.findByProjectId(projectId);
    }

    @Override
    public boolean deleteRestApi(String id) {
        return restApiDao.deleteById(id);
    }

    private boolean saveMaps(TenantId tenantId, String deviceId, Map<String, DataSourceEntity> map, String type) {
        List<DataSourceEntity> result = new ArrayList<>();
        map.forEach((key, value) -> result.add(value));
        try {
            save(tenantId, result, deviceId, type);
        } catch (ThingsboardException e) {
            e.printStackTrace();
            return false;
        }
        return true;
    }

    @Override
    public DataSourceEntity findById(String id) {
        DataSourceEntity datasource = dataSourceDao.findById(id);
        if (datasource != null) {
            Device device = deviceService.findDeviceById(new DeviceId(UUIDConverter.fromString(datasource.getDeviceId())));
            if (device != null) {
                datasource.setDeviceName(device.getName());
            }
        }
        return datasource;
    }

    @Override
    public List<DataSourceGroup> findDataSourceGroupByOriginatorId(String originatorId) {
        List<DataSourceEntity> dataSourceEntities = dataSourceDao.findByOriginatorId(originatorId);
        Map<String, DataSourceGroup> dataSourceGroups = new HashMap<>();
        dataSourceEntities.forEach(dataSourceEntity -> {
            if (dataSourceGroups.containsKey(dataSourceEntity.getType())) {
                dataSourceGroups.get(dataSourceEntity.getType()).getDataSourceList().add(dataSourceEntity);
            } else {
                List<DataSourceEntity> list = new ArrayList<>();
                list.add(dataSourceEntity);
                dataSourceGroups.put(dataSourceEntity.getType(), DataSourceGroup.builder().type(dataSourceEntity.getType()).dataSourceList(list).build());
            }
        });
        List<DataSourceGroup> list = new ArrayList<>();
        dataSourceGroups.forEach((key, value) -> list.add(value));
        return list;
    }

    @Override
    public List<RestApiEntity> findAll() {
        return restApiDao.findAll();
    }

    @Override
    public int countRestApiName(TenantId tenantId, String name) {
        return restApiDao.countRestApiByName(UUIDConverter.fromTimeUUID(tenantId.getId()), name);
    }

    @Override
    public boolean deleteDeviceDataSource(String deviceId) {
        return false;
    }

    @Override
    public List<RestApiEntity> findByTenantId(TenantId tenantId) {
        return restApiDao.findByTenantId(tenantId);
    }

    @Override
    public List<DataSourceEntity> findDataSourceByOriginator(String originator) {
        return dataSourceDao.findByOriginatorId(originator);
    }

    @Override
    public List<DataSourceEntity> findAllByOriginatorIdInAndProperty(List<String> deviceIdList, String property) {
        return dataSourceDao.findAllByOriginatorIdInAndProperty(deviceIdList, DataSourceType.DEVICE_SOURCE.name(), property);
    }


}
