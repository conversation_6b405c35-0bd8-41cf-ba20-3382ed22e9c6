package org.thingsboard.server.dao.sql.gis;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.thingsboard.server.dao.model.sql.gis.GisPlan;

import java.util.List;

public interface GisPlanRepository extends JpaRepository<GisPlan, String> {

    Page<GisPlan> findByNameLikeAndUserIdIn(String name, List<String> userIds, Pageable pageable);

    Page<GisPlan> findByNameLikeAndTypeAndUserIdIn(String name, String type, List<String> userIds, Pageable pageable);
}
