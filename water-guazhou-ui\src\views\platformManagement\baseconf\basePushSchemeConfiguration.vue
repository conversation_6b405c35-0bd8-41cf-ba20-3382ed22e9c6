<template>
  <div class="wrapper">
    <CardSearch
      ref="refSearch"
      :config="SearchConfig"
    ></CardSearch>
    <CardTable
      class="card-table"
      :config="TableConfig"
    ></CardTable>
    <DialogForm
      ref="refDialogForm"
      :config="DialogFormConfig"
    ></DialogForm>
  </div>
</template>

<script setup>
import { onMounted, reactive, ref } from 'vue'
import { 
  getPushSchemeConfigList,
  addPushSchemeConfig,
  editPushSchemeConfig,
  deletePushSchemeConfig,
  getPushSchemeConfigDetail
} from '@/api/platformManagement/basePushSchemeConfiguration'
import { SLConfirm, SLMessage } from '@/utils/Message'

const refSearch = ref()
const refDialogForm = ref()

const SearchConfig = reactive({
  labelWidth: '100px',
  filters: [
    { 
      type: 'input', 
      label: '方案名称', 
      field: 'name', 
      placeholder: '请输入方案名称',
      onChange: () => refreshData() 
    },
    {
      type: 'btn-group',
      btns: [
        { type: 'primary', perm: true, text: '查询', click: () => refreshData() },
        { perm: true, type: 'primary', text: '新增', click: () => handleAdd() },
        { perm: true, type: 'danger', text: '批量删除', click: () => handleDelete() }
      ]
    }
  ],
  defaultParams: {}
})

const TableConfig = reactive({
  columns: [
    { label: '方案名称', prop: 'name' },
    { label: '关联模板', prop: 'templateId' },
    { label: '目标用户类型', prop: 'targetType' },
    { label: '触发条件', prop: 'triggerType' },
    { label: '发送策略', prop: 'sendStrategy' },
    { 
      label: '状态', 
      prop: 'status',
      formatter: (row) => row.status === 'true' ? '启用' : '禁用'
    },
    { label: '创建时间', prop: 'createTime' }
  ],
  dataList: [],
  operations: [
    {
      perm: true,
      type: 'primary',
      isTextBtn: true,
      text: '查看详情',
      click: (row) => handleDetail(row)
    },
    {
      perm: true,
      type: 'primary',
      isTextBtn: true,
      text: '编辑',
      click: (row) => handleAdd(row)
    },
    {
      perm: true,
      type: 'danger',
      isTextBtn: true,
      text: '删除',
      click: (row) => handleDelete(row)
    }
  ],
  pagination: {
    total: 0,
    page: 1,
    align: 'right',
    limit: 20,
    handlePage: (page) => {
      TableConfig.pagination.page = page
      refreshData()
    },
    handleSize: (size) => {
      TableConfig.pagination.limit = size
      refreshData()
    }
  },
  handleSelectChange: (rows) => {
    TableConfig.selectList = rows || []
  }
})

const DialogFormConfig = reactive({
  title: '新增推送方案配置',
  group: [
    {
      fields: [
        {
          type: 'input',
          label: '方案名称',
          field: 'name',
          rules: [{ required: true, message: '请输入方案名称' }]
        },
        {
          type: 'input',
          label: '关联模板',
          field: 'templateId',
          rules: [{ required: true, message: '请输入关联模板' }]
        },
        {
          type: 'input',
          label: '目标用户类型',
          field: 'targetType',
          rules: [{ required: true, message: '请输入目标用户类型' }]
        },
        {
          type: 'select',
          label: '触发条件',
          field: 'triggerType',
          options: [
            { label: '事件触发', value: '事件触发' },
            { label: '定时任务', value: '定时任务' },
            { label: '手动触发', value: '手动触发' }
          ],
          rules: [{ required: true, message: '请选择触发条件' }]
        },
        {
          type: 'select',
          label: '发送策略',
          field: 'sendStrategy',
          options: [
            { label: '立即发送', value: '立即发送' },
            { label: '延迟发送', value: '延迟发送' },
            { label: '指定事件发送', value: '指定事件发送' }
          ],
          rules: [{ required: true, message: '请选择发送策略' }]
        },
        {
          type: 'switch',
          label: '状态',
          field: 'status',
          activeValue: 'true',
          inactiveValue: 'false',
          rules: [{ required: true, message: '请选择状态' }]
        },
        {
          type: 'date',
          label: '创建时间',
          field: 'createTime',
          rules: [{ required: true, message: '请选择创建时间' }]
        }
      ]
    }
  ],
  labelPosition: 'top',
  defaultValue: {},
  dialogWidth: 600,
  draggable: true,
  showSubmit: true,
  showCancel: true,
  cancelText: '取消',
  submitText: '确定',
  submit: async (params) => {
    try {
      if (params.id) {
        await editPushSchemeConfig(params)
        SLMessage.success('修改成功')
      } else {
        await addPushSchemeConfig(params)
        SLMessage.success('新增成功')
      }
      refDialogForm.value?.closeDialog()
      refreshData()
    } catch (error) {
      SLMessage.error('操作失败')
    }
  }
})

// 重置对话框配置
const resetDialogConfig = () => {
  DialogFormConfig.group[0].fields.forEach(field => {
    field.disabled = false
    field.readonly = false
  })
  
  DialogFormConfig.showSubmit = true
  DialogFormConfig.showCancel = true
  DialogFormConfig.cancelText = '取消'
  DialogFormConfig.submitText = '确定'
  
  DialogFormConfig.submit = async (params) => {
    try {
      if (params.id) {
        await editPushSchemeConfig(params)
        SLMessage.success('修改成功')
      } else {
        await addPushSchemeConfig(params)
        SLMessage.success('新增成功')
      }
      refDialogForm.value?.closeDialog()
      refreshData()
    } catch (error) {
      SLMessage.error('操作失败')
    }
  }
}

// 查看详情
const handleDetail = async (row) => {
  try {
    const res = await getPushSchemeConfigDetail(row.id)
    const detailData = res.data?.data || res
    
    resetDialogConfig()
    DialogFormConfig.title = '推送方案配置详情'
    DialogFormConfig.defaultValue = { ...detailData }
    DialogFormConfig.group[0].fields.forEach(field => {
      field.disabled = true
      field.readonly = true
    })
    DialogFormConfig.showSubmit = false
    DialogFormConfig.showCancel = true
    DialogFormConfig.cancelText = '关闭'
    refDialogForm.value?.openDialog()
  } catch (error) {
    SLMessage.error('获取详情失败')
  }
}

// 新增/编辑
const handleAdd = (row) => {
  resetDialogConfig()
  
  if (row) {
    DialogFormConfig.title = '编辑推送方案配置'
    DialogFormConfig.defaultValue = { ...row }
  } else {
    DialogFormConfig.title = '新增推送方案配置'
    DialogFormConfig.defaultValue = {}
  }
  
  refDialogForm.value?.openDialog()
}

// 删除
const handleDelete = async (row) => {
  try {
    const ids = row ? [row.id] : TableConfig.selectList.map(item => item.id)
    if (!ids.length) {
      SLMessage.warning('请选择要删除的数据')
      return
    }
    
    await SLConfirm('确定要删除选中的数据吗？')
    await deletePushSchemeConfig(ids)
    SLMessage.success('删除成功')
    refreshData()
  } catch (error) {
    if (error !== 'cancel') {
      SLMessage.error('删除失败')
    }
  }
}

// 刷新数据
const refreshData = async () => {
  try {
    const res = await getPushSchemeConfigList({
      page: TableConfig.pagination.page,
      size: TableConfig.pagination.limit,
      ...(refSearch.value?.queryParams || {})
    })
    const responseData = res.data?.data || res
    TableConfig.dataList = responseData.records || responseData
    TableConfig.pagination.total = responseData.total || responseData.length || 0
  } catch (error) {
    SLMessage.error('数据加载失败')
  }
}

onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.wrapper {
  padding: 20px;
}

.card-table {
  margin-top: 20px;
}
</style>