package org.thingsboard.server.dao.util.imodel.response.annotations;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.lang.annotation.*;

@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
@Documented
@Deprecated
public @interface ParseViaMapper {

    /**
     * 使用指定Mapper来获取Id字段对应的名字
     *
     * @return 目标Mapper
     */
    Class<? extends BaseMapper<?>> value();

    String method() default "getNameById";

    /**
     * 目标字段名前缀，会转换为 前缀+name 的形式
     */
    String prefix() default "";

    boolean withoutSuffix() default false;
}
