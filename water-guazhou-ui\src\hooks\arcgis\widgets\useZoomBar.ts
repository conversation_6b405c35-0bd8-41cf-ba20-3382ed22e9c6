import Zoom from '@arcgis/core/widgets/Zoom.js'

export const useZoomBar = () => {
  let zoomBar: __esri.Zoom | undefined
  const init = (view: __esri.MapView, widgetPosition?: string) => {
    zoomBar = new Zoom({
      view
    }) as __esri.Zoom
    view.ui?.add(zoomBar, widgetPosition || 'top-left')
    return zoomBar
  }
  const destroy = () => {
    zoomBar?.destroy()
  }
  onBeforeUnmount(() => {
    destroy()
  })
  return {
    init
  }
}
export default useZoomBar
