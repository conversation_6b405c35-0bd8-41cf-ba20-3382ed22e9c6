package org.thingsboard.server.dao.sql.smartService.portal;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.thingsboard.server.dao.model.sql.smartService.portal.SsPortalPolicyContent;
import org.thingsboard.server.dao.util.imodel.query.smartService.portal.SsPortalPolicyContentPageRequest;

@Mapper
public interface SsPortalPolicyContentMapper extends BaseMapper<SsPortalPolicyContent> {
    IPage<SsPortalPolicyContent> findByPage(SsPortalPolicyContentPageRequest request);

    @SuppressWarnings("methodNotInXmlInspection")
    boolean update(SsPortalPolicyContent entity);

    boolean updateFully(SsPortalPolicyContent entity);

}
