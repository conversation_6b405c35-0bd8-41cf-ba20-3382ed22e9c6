package org.thingsboard.server.dao.notify;

import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.request.SystemNotifyListRequest;
import org.thingsboard.server.dao.model.sql.notify.SystemNotify;

import java.util.Map;

public interface SystemNotifyService {

    PageData<SystemNotify> findList(SystemNotifyListRequest request, TenantId tenantId);

    /**
     * 一键已读通知
     *
     * @param type     通知类型
     * @param to       一键已读的用户
     * @param tenantId 租户ID
     */
    void readAll(String type, String to, TenantId tenantId);

    /**
     * 统计用户各个类型的通知数量
     *
     * @param to       用户ID
     * @param status   通知的状态
     * @param tenantId 租户ID
     * @return 数据
     */
    Map<String, Integer> notifyCount(String to, String status, TenantId tenantId);

    /**
     * 发送通知消息
     * @param type  消息类型
     * @param topic 消息主题
     * @param from  消息发送人
     * @param to    消息接收人
     * @param tenantId
     */
    void sendNotify(String type, DataConstants.SYSTEM_NOTIFY_TOPIC topic, String from, String to, String tenantId);

    void readOne(String id, TenantId tenantId);
}
