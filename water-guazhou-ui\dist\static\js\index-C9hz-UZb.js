import{d as i,n as o,ax as t,an as a,p as l,aw as n,av as r,i as d,g as s,bh as p,j as h,C as y}from"./index-r0dFAfgr.js";const u={key:0,class:"sl-card-title"},m={class:"title"},c={key:1,class:"sl-card-bottom"},v=i({__name:"index",props:{overlay:{type:Boolean},title:{},height:{},bottom:{type:Boolean},noBorder:{type:<PERSON>olean},contentStyle:{},padding:{}},setup(g){return(e,B)=>(s(),o("div",{class:n(["sl-card",{hastitle:!!e.title,dark:d(h)().isDark}]),style:r({height:e.height?e.height+"px":"","box-shadow":e.noBorder?"none":"var(--el-box-shadow-lighter)",padding:e.padding})},[e.title?(s(),o("div",u,[t(e.$slots,"title",{},()=>[l("span",m,p(typeof e.title=="string"?e.title:""),1)],!0),t(e.$slots,"query",{},void 0,!0),t(e.$slots,"right",{},void 0,!0)])):a("",!0),l("div",{class:n(["sl-card-content",{"overlay-y":e.overlay}]),style:r(e.contentStyle)},[t(e.$slots,"default",{},void 0,!0)],6),e.bottom?(s(),o("div",c,[t(e.$slots,"bottom",{},void 0,!0)])):a("",!0)],6))}}),f=y(v,[["__scopeId","data-v-3c969122"]]);export{f as _};
