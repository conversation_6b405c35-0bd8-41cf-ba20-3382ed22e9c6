<template>
  <div class="table-wrapper">
    <Search
      ref="refSearch"
      :config="SearchConfig"
      class="search"
    ></Search>
    <FormTable
      :config="TableConfig"
      class="table-box"
    ></FormTable>
    <DialogForm
      ref="refDialog"
      :config="DialogConfig"
    ></DialogForm>
  </div>
</template>
<script lang="ts" setup>
import {
  AddDmaPartitionFlowMeter,
  DeleteDmaPartitionFlowMeter,
  GetDmaPartitionFlowMeter
} from '@/api/mapservice/dma/partitionResources'
import { FlowMeterTypeOptions, OFlowMeterTypeOptions } from '@/common/constants/options'
import { IDialogFormIns, ISearchIns } from '@/components/type'
import { SLConfirm, SLMessage } from '@/utils/Message'

const props = defineProps<{ partition?: NormalOption }>()
const refSearch = ref<ISearchIns>()
const refDialog = ref<IDialogFormIns>()
const SearchConfig = reactive<ISearch>({
  filters: [
    {
      type: 'select',
      label: '类型',
      field: 'type',
      labelWidth: 40,
      options: FlowMeterTypeOptions
    },
    {
      type: 'input',
      label: '水表编号',
      field: 'code',
      clearable: false
    },
    {
      type: 'input',
      label: '水表类型',
      field: 'meterType',
      clearable: false
    },
    {
      type: 'input',
      label: '水表厂家',
      field: 'brand',
      clearable: false
    }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          iconifyIcon: 'ep:search',
          text: '查询',
          type: 'primary',
          click: () => refreshData()
        },
        {
          perm: true,
          iconifyIcon: 'ep:refresh',
          text: '重置',
          type: 'default',
          click: () => {
            refSearch.value?.resetForm()
          }
        },
        {
          perm: true,
          iconifyIcon: 'ep:circle-plus',
          text: '新增',
          type: 'success',
          click: () => handleAou()
        },
        {
          perm: false,
          iconifyIcon: 'ep:circle-plus',
          text: '自动生成',
          type: 'success',
          click: () => refreshData()
        }
      ]
    }
  ]
})
const TableConfig = reactive<ITable>({
  dataList: [],
  columns: [
    {
      minWidth: 120,
      label: '类型',
      prop: 'type',
      formatter(row, value) {
        return OFlowMeterTypeOptions[value] ?? value
      }
    },
    { minWidth: 120, label: '水表编号', prop: 'code' },
    { minWidth: 120, label: '安装位置', prop: 'position' },
    { minWidth: 120, label: '水表类型', prop: 'meterType' },
    { minWidth: 120, label: '水表厂家', prop: 'brand' },
    { minWidth: 120, label: '管径', prop: 'caliber' },
    { minWidth: 120, label: '管材', prop: 'pipe' },
    {
      minWidth: 120,
      label: '是否远传',
      prop: 'isRemote',
      formatter(row, value) {
        return value === '1' ? '是' : value === '0' ? '否' : ''
      }
    },
    { minWidth: 120, label: '安装年份', prop: 'year' },
    { minWidth: 120, label: '备注', prop: 'remark' },
    { minWidth: 130, label: '现场图片', prop: 'img', image: true }
  ],
  pagination: {
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page
      TableConfig.pagination.limit = size
      refreshData()
    }
  },
  operations: [
    {
      perm: true,
      text: '编辑',
      iconifyIcon: 'ep:edit',
      click: row => handleAou(row)
    },
    {
      perm: true,
      text: '删除',
      iconifyIcon: 'ep:delete',
      type: 'danger',
      click: row => handleDelete(row)
    }
  ]
})
const refreshData = async () => {
  try {
    TableConfig.loading = true
    const query = refSearch.value?.queryParams || {}
    const res = await GetDmaPartitionFlowMeter({
      ...query,
      partitionId: props.partition?.value,
      page: TableConfig.pagination.page || 1,
      size: TableConfig.pagination.limit || 20
    })
    const data = res.data.data || {}
    TableConfig.dataList = data.data || []
    TableConfig.pagination.total = data.total || 0
  } catch (error) {
    //
  }
  TableConfig.loading = false
}
const handleAou = (row?: any) => {
  DialogConfig.defaultValue = {
    ...(row || {})
  }
  DialogConfig.title = row ? '编辑流量表' : '添加流量表'
  refDialog.value?.openDialog()
}
const handleDelete = (row?: any) => {
  const ids = row ? [row.id] : []
  if (!ids.length) {
    SLMessage.error('请选择要删除的数据')
    return
  }
  SLConfirm('确定删除?', '提示信息')
    .then(async () => {
      try {
        const res = await DeleteDmaPartitionFlowMeter(ids)
        if (res.data.code === 200) {
          SLMessage.success('删除成功')
          refreshData()
        } else {
          SLMessage.error(res.data.message)
        }
      } catch (error) {
        SLMessage.error('删除失败')
      }
    })
    .catch(() => {
      //
    })
}
const DialogConfig = reactive<IDialogFormConfig>({
  title: '添加流量表',
  dialogWidth: 600,
  labelPosition: 'right',
  group: [
    {
      fields: [
        {
          lg: 24,
          xl: 12,
          type: 'select',
          label: '类型',
          field: 'type',
          options: FlowMeterTypeOptions
        },
        {
          lg: 24,
          xl: 12,
          type: 'input',
          label: '水表编号',
          field: 'code'
        },
        {
          type: 'textarea',
          label: '水表位置',
          field: 'position'
        },
        {
          lg: 24,
          xl: 12,
          type: 'input',
          label: '水表类型',
          field: 'meterType'
        },
        {
          lg: 24,
          xl: 12,
          type: 'input',
          label: '水表厂家',
          field: 'brand'
        },
        {
          lg: 24,
          xl: 12,
          type: 'input-number',
          label: '管径',
          field: 'caliber'
        },
        {
          lg: 24,
          xl: 12,
          type: 'input',
          label: '管材',
          field: 'pipe'
        },
        {
          lg: 24,
          xl: 12,
          type: 'radio',
          label: '是否远传',
          field: 'isRemote',
          options: [
            { label: '是', value: '1' },
            { label: '否', value: '0' }
          ]
        },
        {
          lg: 24,
          xl: 12,
          type: 'year',
          label: '安装年份',
          field: 'year'
        },
        {
          type: 'textarea',
          label: '备注',
          field: 'remark'
        },
        {
          type: 'image',
          label: '现场图片',
          field: 'img'
        }
      ]
    }
  ],
  submit: async params => {
    DialogConfig.submitting = true
    if (!props.partition?.value) {
      SLMessage.warning('请先选择分区')
      return
    }
    try {
      const res = await AddDmaPartitionFlowMeter({
        ...params,
        partitionId: props.partition.value
      })
      if (res.data.code === 200) {
        SLMessage.success('添加成功')
        refreshData()
        refDialog.value?.closeDialog()
      } else {
        SLMessage.error(res.data.message)
      }
    } catch (error) {
      SLMessage.error('操作失败')
    }
    DialogConfig.submitting = false
  }
})
onMounted(() => {
  refreshData()
})
</script>
<style lang="scss" scoped>
.table-wrapper {
  height: 100%;
}
.search {
  margin-bottom: 10px;
  margin-left: -20px;
  margin-right: -20px;
}
.table-box {
  height: calc(100% - 40px);
}
</style>
