package org.thingsboard.server.dao.maintain;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.model.sql.smartManagement.maintaince.SMMaintainTask;
import org.thingsboard.server.dao.model.sql.smartProduction.circuit.CircuitTask;
import org.thingsboard.server.dao.model.sql.smartProduction.circuit.GeneralTaskStatus;
import org.thingsboard.server.dao.model.sql.statistic.GeneralTaskStatusStatistic;
import org.thingsboard.server.dao.sql.smartManagement.maintain.SMMaintainTaskMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.maintain.*;

import java.util.Collections;
import java.util.List;

import static org.thingsboard.server.dao.model.sql.smartProduction.circuit.GeneralTaskStatus.*;

@Service
public class SMMaintainTaskServiceImpl implements SMMaintainTaskService {
    @Autowired
    private SMMaintainTaskMapper mapper;

    @Autowired
    private SMMaintainTaskItemService itemService;


    @Override
    public IPage<SMMaintainTask> findAllConditional(SMMaintainTaskPageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    public SMMaintainTask save(SMMaintainTaskSaveRequest entity) {
        SMMaintainTask task = QueryUtil.saveOrUpdateOneByRequest(entity, mapper);
        List<SMMaintainTaskItemSaveRequest> items = entity.getItems(task.getId());
        // 因为禁止了更新操作，所以无需删除任务下的所有子任务
        itemService.saveAll(items);
        return task;
    }

    @Override
    public boolean update(SMMaintainTask entity) {
        return mapper.update(entity);
    }

    @Override
    @Transactional
    public boolean delete(String id) {
        itemService.removeAllByTaskId(id);
        return mapper.deleteById(id) > 0;
    }

    @Override
    public boolean assign(SMMaintainTaskAssignRequest req) {
        return mapper.assign(req);
    }

    @Override
    public boolean complete(SMMaintainTaskCompleteRequest req) {
        return mapper.complete(req);
    }

    @Override
    public GeneralTaskStatusStatistic countStatusByUser(String userId, GeneralTaskStatus status) {
        return countStatusByUser(userId, Collections.singletonList(status));
    }
    @Override
    public GeneralTaskStatusStatistic countStatusByUser(String userId, List<GeneralTaskStatus> status) {
        return new GeneralTaskStatusStatistic(
                mapper.totalStatusOfUser(userId, status),
                mapper.totalOfUser(userId)
        );
        // return mapper.countStatus(userId, status);
    }

    @Override
    public boolean deleteAll(List<String> idList) {
        return mapper.deleteBatchIds(idList) > 0;
    }

    @Override
    public JSONObject statusCount(TenantId tenantId) {
        QueryWrapper<SMMaintainTask> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("tenant_id", UUIDConverter.fromTimeUUID(tenantId.getId()));
        List<SMMaintainTask> list = mapper.selectList(queryWrapper);
        JSONObject result = new JSONObject();
        int pending = 0;
        int received = 0;
        int complete = 0;
        if (list != null && !list.isEmpty()) {
            for (SMMaintainTask maintainTask : list) {
                GeneralTaskStatus status = maintainTask.getStatus();
                if (GeneralTaskStatus.PENDING.equals(status)) {
                    pending = pending + 1;
                }
                if (GeneralTaskStatus.RECEIVED.equals(status)) {
                    received = received + 1;
                }
                if (GeneralTaskStatus.APPROVED.equals(status)) {
                    complete = complete + 1;
                }
            }
        }
        result.put("pending", pending);
        result.put("received", received);
        result.put("complete", complete);

        return result;
    }
}
