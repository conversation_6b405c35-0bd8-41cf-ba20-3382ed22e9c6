.filter-box {
  display: flex;
  margin-bottom: 10px;
  justify-content: space-between;



  .left-btn {
    display: flex;
    align-items: center;

    .msg-text {
      margin-left: 15px;
      font-size: small;
      color: rgb(41, 173, 39);
    }
  }

  .right-box {
    display: flex;
    align-items: center;

    .el-icon-question {
      font-size: 30px;
      color: #ff5722;
      margin-right: 10px;
    }
  }

  .filter-input {
    width: 200px;
    margin-right: 10px;
  }
}

.data-display-area {
  display: flex;
  flex-wrap: wrap;
  overflow-y: auto;
  align-content: flex-start;

  .var-card-item {
    margin: 10px;
    padding: 5px;
    color: #fff;
    border-radius: 5px;
    box-shadow: 0 0 10px 1px rgba(0, 147, 255, 0.3);

    &:hover {
      box-shadow: 0 0 10px 1px rgba(0, 199, 255, 0.7);
    }

    p {
      margin: 0 0;
    }
  }

  .data-source-msg {
    text-align: center;
    padding: 50px;
    width: 100%;
  }
}

.data-display-list-table {
  .col-name-box {
    display: flex;
    align-items: center;
  }

  .text-icon-box {
    width: 35px;
    height: 35px;
    text-align: center;
    line-height: 35px;
    margin: 0 10px 0 0;
    border-radius: 5px;
    background-color: #3DA8F6;

    .name-icon {
      color: #fff;
    }
  }

  .icon-clock {
    color: #69E850;
  }

  .table-btn {
    padding: 7px 12px;
    // border-radius: 20px;
  }
}

.ds-value-style {
  font-weight: 700;
}

.ds-unit-style {
  font-size: 16px;
  margin-left: 5px;
  font-weight: 500;
}