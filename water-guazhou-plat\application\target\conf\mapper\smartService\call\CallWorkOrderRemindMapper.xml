<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.smartService.call.CallWorkOrderRemindMapper">
    <select id="getList" resultType="org.thingsboard.server.dao.model.sql.smartService.call.CallWorkOrderRemind">
        select a.*, b.first_name as creatorName, c.name as sourceName
        from tb_service_work_order_remind a
        left join tb_user b on a.creator = b.id
        left join tb_service_system_dict c on a.source = c.id
        where a.work_order_id = #{workOrderId}
        order by a.create_time desc
    </select>

</mapper>