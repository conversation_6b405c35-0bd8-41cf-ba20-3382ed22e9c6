package org.thingsboard.server.dao.sql.shuiwu.assets;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.dao.model.sql.shuiwu.assets.AssetsDepreciationEntity;

/**
 * 设备转移
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2021-10-19
 */
public interface AssetsDepreciationRepository extends CrudRepository<AssetsDepreciationEntity, String> {
    @Query("select new AssetsDepreciationEntity(a, b.deviceNo, b.deviceName, b.depreciationMethod, b.purchaseAmount, b.useLife, b.netResidualRate, b.originValue, b.netValue) from AssetsDepreciationEntity  a, AssetsAccountEntity b " +
            "where a.pid = b.id and b.deviceNo like ?1 and b.deviceName like ?2 and b.depreciationMethod like ?3 and a.tenantId like ?4 and a.projectId like ?5 and a.createTime between ?6 and ?7")
    Page<AssetsDepreciationEntity> findPage(String deviceNo, String deviceName, String depreciationMethod, String tenantId, String projectId, Long start, Long end, Pageable pageable);


    AssetsDepreciationEntity findTopByPidOrderByCreateTimeDesc(String pif);

    @Transactional
    @Modifying
    void deleteByPid(String pid);
}
