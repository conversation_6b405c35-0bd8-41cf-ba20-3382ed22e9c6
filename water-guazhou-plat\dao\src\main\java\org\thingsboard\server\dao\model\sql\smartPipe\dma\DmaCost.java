package org.thingsboard.server.dao.model.sql.smartPipe.dma;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * DMA分区设置
 */
@Data
@TableName("tb_pipe_dma_cost")
public class DmaCost {
    private String id;

    private String code;

    private String partitionId;

    private String type;

    @TableField(exist = false)
    private String typeName;

    private String value;

    private String base;

    private String remark;

    private String creator;

    @TableField(exist = false)
    private String creatorName;

    private Date createTime;

    private String tenantId;


}
