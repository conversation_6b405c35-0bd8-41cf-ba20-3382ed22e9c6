package org.thingsboard.server.dao.sql.base;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.thingsboard.server.dao.model.sql.base.OptionLogList;
import org.thingsboard.server.dao.util.imodel.query.base.OptionLogListPageRequest;

/**
 * 平台管理-日志Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
@Mapper
public interface OptionLogMapper {

    IPage<OptionLogList> selectLogList(OptionLogListPageRequest optionLogListPageRequest);

    IPage<OptionLogList> selectOperationLogList(OptionLogListPageRequest optionLogListPageRequest);
}
