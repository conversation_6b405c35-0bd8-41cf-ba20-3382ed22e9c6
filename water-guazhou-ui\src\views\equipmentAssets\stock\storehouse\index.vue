<!-- 仓库管理 -->
<template>
  <div class="wrapper">
    <CardSearch
      ref="refSearch"
      :config="cardSearchConfig"
    />
    <CardTable
      :config="TableConfig"
      class="card-table"
    ></CardTable>
    <DialogForm
      ref="refForm"
      :config="addOrUpdateConfig"
    ></DialogForm>
  </div>
</template>

<script lang="ts" setup>
import { Refresh } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { ICONS } from '@/common/constans/common'
import { ICardSearchIns, IDialogFormIns } from '@/components/type'
import { getstoreSerch, postStore, patchStore, deleteStore } from '@/api/equipment_assets/equipmentOutStock'
import useGlobal from '@/hooks/global/useGlobal'
import { SLConfirm } from '@/utils/Message'

const { $btnPerms } = useGlobal()
const refSearch = ref<ICardSearchIns>()
const refForm = ref<IDialogFormIns>()

const cardSearchConfig = ref<ISearch>({
  filters: [
    { label: '仓库编码', field: 'code', type: 'input' },
    { label: '仓库名称', field: 'name', type: 'input' },
    { label: '管理员', field: 'managerId', type: 'department-user' }

  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          icon: ICONS.QUERY,
          click: () => refreshData()
        },
        {
          type: 'default',
          perm: true,
          text: '重置',
          svgIcon: shallowRef(Refresh),
          click: () => {
            refSearch.value?.resetForm()
            refreshData()
          }
        },
        {
          perm: $btnPerms('RoleManageAdd'),
          text: '新增',
          type: 'success',
          icon: ICONS.ADD,
          click: () => clickCreatedRole()
        }
      ]
    }
  ]
})

const TableConfig = reactive<ICardTable>({
  defaultExpandAll: true,
  indexVisible: true,
  columns: [
    { label: '仓库编码', prop: 'code' },
    { label: '仓库名称', prop: 'name' },
    { label: '仓库地址', prop: 'address' },
    { label: '排序编号', prop: 'orderNum' },
    { label: '管理员', prop: 'managerName' },
    { label: '联系电话', prop: 'tel' },
    { label: '备注/说明', prop: 'remark' },
    { label: '创建人', prop: 'creatorName' },
    { label: '创建时间', prop: 'createTime' }
  ],
  operationWidth: '200px',
  operations: [
    {
      type: 'primary',
      isTextBtn: true,
      color: '#4195f0',
      text: '编辑',
      perm: $btnPerms('RoleManageEdit'),
      icon: 'iconfont icon-xiangqing',
      click: row => clickEdit(row)
    },
    {
      isTextBtn: true,
      type: 'danger',
      text: '删除',
      icon: 'iconfont icon-shanchu',
      perm: $btnPerms('RoleManageDelete'),
      click: row => haneleDelete(row)
    }
  ],
  dataList: [],
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page
      TableConfig.pagination.limit = size
      refreshData()
    }
  }
})

const addOrUpdateConfig = reactive<IDialogFormConfig>({
  title: '新增',
  labelWidth: '100px',
  dialogWidth: '500px',
  submitting: false,
  submit: (params:any) => {
    delete params.department
    addOrUpdateConfig.submitting = true
    if (params.id) {
      patchStore(params.id, params).then(() => {
        refreshData()
        ElMessage.success('修改成功')
        refForm.value?.closeDialog()
        addOrUpdateConfig.submitting = false
      }).catch(error => {
        ElMessage.warning(error)
        addOrUpdateConfig.submitting = false
      })
    } else {
      postStore(params).then(() => {
        refreshData()
        ElMessage.success('添加成功')
        refForm.value?.closeDialog()
        addOrUpdateConfig.submitting = false
      }).catch(error => {
        ElMessage.warning(error)
        addOrUpdateConfig.submitting = false
      })
    }
  },

  defaultValue: {},
  group: [
    {
      fields: [
        {
          type: 'input',
          label: '仓库编码',
          field: 'code',
          rules: [{ required: true, message: '请输入仓库编码' }]
        },

        {
          type: 'input',
          label: '仓库名称',
          field: 'name',
          rules: [{ required: true, message: '请输入仓库名称' }]
        },
        {
          type: 'textarea',
          label: '仓库地址',
          field: 'address',
          rules: [{ required: true, message: '请输入仓库地址' }]
        },
        {
          type: 'input-number',
          label: '排序编号',
          field: 'orderNum'
        },
        {
          type: 'department-user',
          label: '管理员',
          field: 'managerId',
          rules: [{ required: true, message: '请输入管理员' }]
        },
        {
          type: 'input-number',
          label: '联系电话',
          field: 'tel',
          rules: [{ required: true, message: '请输入联系电话' }]
        },
        {
          type: 'textarea',
          label: '备注',
          field: 'remark'
        }
      ]
    }
  ]
})

const clickCreatedRole = () => {
  addOrUpdateConfig.title = '新增'
  addOrUpdateConfig.defaultValue = { }
  refForm.value?.openDialog()
}

const clickEdit = (row: { [x: string]: any }) => {
  addOrUpdateConfig.title = '编辑'
  addOrUpdateConfig.defaultValue = { ...(row) || {} }
  refForm.value?.openDialog()
}

const haneleDelete = (row: { id: string }) => {
  SLConfirm('确定删除该仓库, 是否继续?', '删除提示').then(() => {
    deleteStore(row.id).then(() => {
      refreshData()
      ElMessage.success('删除成功')
    }).catch(error => {
      ElMessage.warning(error)
    })
  })
}

const refreshData = async () => {
  const params:{
    size:number|undefined
    page:number|undefined
    department?:string
  } = {
    size: TableConfig.pagination.limit,
    page: TableConfig.pagination.page,
    ...(refSearch.value?.queryParams || {})
  }
  delete params.department
  getstoreSerch(params).then(res => {
    TableConfig.dataList = res.data.data.data || []
    TableConfig.pagination.total = res.data.data.total || 0
  })
}

function init() {
  refreshData()
}

onMounted(() => {
  init()
})
</script>

<style lang="scss">
.el-table__placeholder {
  display: none;
}
</style>
