import{aA as l}from"./MapView-DaoQedLH.js";class h{constructor(e){this.message=e}toString(){return`AssertException: ${this.message}`}}function w(r,e){if(!r){e=e||"Assertion";const t=new Error(e).stack;throw new h(`${e} at ${t}`)}}function m(r,e){r||(e=e||"",console.warn("Verify failed: "+e+`
`+new Error("verify").stack))}function p(r,e,t,n){let i,o=(t[0]-r[0])/e[0],c=(n[0]-r[0])/e[0];o>c&&(i=o,o=c,c=i);let u=(t[1]-r[1])/e[1],f=(n[1]-r[1])/e[1];if(u>f&&(i=u,u=f,f=i),o>f||u>c)return!1;u>o&&(o=u),f<c&&(c=f);let s=(t[2]-r[2])/e[2],a=(n[2]-r[2])/e[2];return s>a&&(i=s,s=a,a=i),!(o>a||s>c)&&(a<c&&(c=a),!(c<0))}function A(r,e,t,n,i,o=l()){const c=(n[i]-t[i])*(e[0]-r[0])-(n[0]-t[0])*(e[i]-r[i]),u=(n[0]-t[0])*(r[i]-t[i])-(n[i]-t[i])*(r[0]-t[0]);if(c===0)return!1;const f=u/c;return o[0]=r[0]+f*(e[0]-r[0]),o[1]=r[i]+f*(e[i]-r[i]),!0}function E(r,e){return Math.log(r)/Math.log(e)}function k(r,e,t,n){r[12]=e,r[13]=t,r[14]=n}function x(r){return r[0]===1&&r[1]===0&&r[2]===0&&r[3]===0&&r[4]===0&&r[5]===1&&r[6]===0&&r[7]===0&&r[8]===0&&r[9]===0&&r[10]===1&&r[11]===0&&r[15]===1}export{E as a,m as c,A as f,k as h,p as i,x as l,w as s};
