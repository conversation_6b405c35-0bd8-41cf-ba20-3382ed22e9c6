package org.thingsboard.server.dao.util.imodel.query.smartService.portal;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartService.portal.SsPortalNews;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;

@Getter
@Setter
public class SsPortalVideoPageRequest extends AdvancedPageableQueryEntity<SsPortalNews, SsPortalVideoPageRequest> {

    // 标题
    private String title;

    // 是否简介
    private String isIntroduce;

    // 是否发布
    private String isPublicize;

    // 视频格式
    private String vformat;

    // 视频格式
    private String tenantId;

}
