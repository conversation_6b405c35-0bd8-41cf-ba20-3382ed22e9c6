package org.thingsboard.server.dao.util.imodel.query.base;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.thingsboard.server.dao.model.sql.base.BasePipeLineConfiguration;
import org.thingsboard.server.dao.util.imodel.query.PageableQueryEntity;

/**
 * 平台管理-管网配置对象 base_pipe_line_configuration
 *
 * <AUTHOR>
 * @date 2025-05-22
 */
@ApiModel(value = "管网配置", description = "管网配置实体类")
@Data
public class BasePipeLineConfigurationPageRequest extends PageableQueryEntity<BasePipeLineConfiguration> {

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 管网id
     */
    @ApiModelProperty(value = "管网id")
    private String pipeId;

    /**
     * 运行规则
     */
    @ApiModelProperty(value = "运行规则")
    private String rule;

    /**
     * 报警阈值
     */
    @ApiModelProperty(value = "报警阈值")
    private String alarmThreshold;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

}
