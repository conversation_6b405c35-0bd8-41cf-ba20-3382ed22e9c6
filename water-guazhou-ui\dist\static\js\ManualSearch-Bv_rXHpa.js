import{d as A,c as E,r as N,b as v,W as J,Q as P,g as U,h as Q,F as O,q as w,i as S,_ as j,X as K,C as X}from"./index-r0dFAfgr.js";import{g as Y}from"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import{g as Z,a as ee}from"./LayerHelper-Cn-iiqxI.js";import{g as te}from"./QueryHelper-ILO3qZqg.js";import{GetFieldConfig as re,GetFieldUniqueValue as ae}from"./fieldconfig-Bk3o1wi7.js";import{u as ie}from"./arcWidgetButton-0glIxrt7.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import{u as oe}from"./useScheme-DcjSAE44.js";import{GetFieldConfig as le,GetFieldValueByGeoserver as se,QueryByPolygon as pe}from"./wfsUtils-DXofo3da.js";import me from"./RightDrawerMap-D5PhmGFO.js";import ne from"./SchemeHeader-BLYQTCg3.js";import{_ as ce}from"./SchemeManage.vue_vue_type_script_setup_true_lang-fv9Irhyi.js";import{_ as de}from"./SaveScheme.vue_vue_type_script_setup_true_lang-Bt-6iBz5.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./project-DUuzYgGl.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./pipe-nogVzCHG.js";import"./executeForIds-BLdIsxvI.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./ArcView-DpMnCY82.js";import"./ViewHelper-BGCZjxXH.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./ArcStationWarning-BF9YrSzF.js";/* empty css                         */import"./useWaterPoint-Bv0z6ym6.js";import"./DateFormatter-Bm9a68Ax.js";import"./zhandian-YaGuQZe6.js";import"./useStation-DJgnSZIA.js";import"./DrawerBox-CLde5xC8.js";import"./SideDrawer-CBntChyn.js";import"./PipeDetail-CTBPYFJW.js";import"./Search-NSrhrIa_.js";import"./FormTableColumnFilter-BT7pLXIC.js";import"./config-fy91bijz.js";import"./ArcBRTools-BO92yznB.js";import"./PipeLength.vue_vue_type_script_setup_true_lang-BZfUsvDf.js";import"./StatisticsHelper-D-s_6AyQ.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./usePipelineGroup-Ba1pmWz_.js";import"./GroupLayer-DhhN3FyN.js";import"./lazyLayerLoader-DbM9sT1W.js";import"./WFSLayer-1TtJp3nx.js";import"./clientSideDefaults-VQhQaYxh.js";import"./QueryEngineCapabilities-Dk3NJwmm.js";import"./wfsUtils-Du031XaC.js";import"./geojson-MBFu2-HZ.js";import"./xmlUtils-CtUoQO7q.js";import"./ListWindow-WS05QqV0.js";import"./PopLayout-BP55MvL7.js";import"./PoiSearchV2-D7yNeLuv.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./URLHelper-B9aplt5w.js";import"./useLayerList-DmEwJ-ws.js";import"./useScaleBar-Beed-z91.js";import"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";const ue=A({__name:"ManualSearch",setup(fe){const x=E(),i=E(),r=N({tabs:[],curOperate:"",layerInfos:[],layerIds:[]}),o={},g=N({group:[{fieldset:{desc:"绘制工具"},fields:[{type:"btn-group",btns:[{perm:!0,text:"",type:"default",size:"large",title:"绘制多边形",iconifyIcon:"mdi:shape-polygon-plus",click:()=>F("polygon")},{perm:!0,text:"",type:"default",size:"large",title:"绘制矩形",iconifyIcon:"ep:crop",click:()=>F("rectangle")},{perm:!0,text:"",type:"default",size:"large",title:"绘制圆形",iconifyIcon:"mdi:ellipse-outline",click:()=>F("circle")},{perm:!0,text:"",type:"default",size:"large",title:"清除图形",iconifyIcon:"ep:delete",click:()=>R()}]}]},{fieldset:{desc:"选择图层"},fields:[{type:"tree",options:[],checkStrictly:!0,showCheckbox:!0,field:"layerid",nodeKey:"value",handleCheckChange:(e,t)=>{t&&i.value&&(i.value.dataForm.layerid=[e.value])}}]},{fieldset:{desc:"图层字段"},fields:[{type:"list",data:[],className:"sql-list-wrapper",setData:async(e,t)=>{var p,s,m,n;if(!((p=t.layerid)!=null&&p.length))return;const a=t.layerid[0];if(GIS_SERVER_SWITCH)le(a).then(l=>{e.data=l.data.featureTypes[0].properties});else{const l=(s=r.layerInfos.find(c=>c.layerid===a))==null?void 0:s.layername;if(!l)return;const f=await re(l);e.data=(n=(m=f.data)==null?void 0:m.result)==null?void 0:n.rows}},setDataBy:"layerid",displayField:GIS_SERVER_SWITCH?"name":"alias",valueField:"name",highlightCurrentRow:!0,nodeClick:e=>{r.curFieldNode=e,GIS_SERVER_SWITCH?d(`"${e.name}"`):d(e.name)}}]},{id:"field-construct",fieldset:{desc:"构建查询语句"},fields:[{type:"btn-group",size:"small",style:{width:"40%",display:"flex",flexWrap:"wrap"},className:"sql-btns-wrapper",btns:[{perm:!0,text:"=",styles:{margin:"6px",width:"50px"},click:()=>{d("=")}},{perm:!0,text:"模糊",styles:{margin:"6px",width:"50px"},click:()=>{d("like '%替换此处%'")}},{perm:!0,text:">",styles:{margin:"6px",width:"50px"},click:()=>{d(">")}},{perm:!0,text:"<",styles:{margin:"6px",width:"50px"},click:()=>{d("<")}},{perm:!0,text:"非",styles:{margin:"6px",width:"50px"},click:()=>{d("<>")}},{perm:!0,text:"并且",styles:{margin:"6px",width:"50px"},click:()=>{d("and")}},{perm:!0,text:"或者",styles:{margin:"6px",width:"50px"},click:()=>{d("or")}},{perm:!0,text:"%",styles:{margin:"6px",width:"50px"},click:()=>{d("%")}}],extraFormItem:[{type:"list",wrapperStyle:{width:"60%",height:"144px"},className:"sql-list-wrapper",field:"uniqueValue",data:[],nodeClick:e=>{d("'"+e+"'")},filters:[{type:"btn-group",btns:[{perm:!0,text:()=>r.curOperate==="uniqueing"?"正在获取唯一值":"获取唯一值",loading:()=>r.curOperate==="uniqueing",styles:{width:"100%",borderRadius:"0"},click:()=>V()}]}]}]}]},{fieldset:{desc:"组合查询条件"},fields:[{type:"textarea",field:"sql",placeholder:"OBJECTID > 0"},{type:"btn-group",btns:[{perm:!0,text:"清除",type:"danger",disabled:()=>r.curOperate==="detailing",click:()=>T(),styles:{width:"100%"}},{perm:!0,text:()=>r.curOperate==="detailing"?"正在查询":"查询",disabled:()=>r.curOperate==="detailing",loading:()=>r.curOperate==="detailing",click:()=>_(),styles:{width:"100%"}},{perm:window.SITE_CONFIG.GIS_CONFIG.gisSaveScheme,text:"保存方案",click:()=>M(),styles:{width:"100%"}}]}]}],labelPosition:"top",gutter:12}),F=e=>{var t,a;o.view&&((t=I.value)==null||t.create(e),(a=o.graphicsLayer)==null||a.removeAll())},R=()=>{var e;(e=o.graphicsLayer)==null||e.removeAll(),o.graphics=void 0},L=()=>{var e;if(GIS_SERVER_SWITCH){const t=g.group[1].fields[0];let p=((e=o.view)==null?void 0:e.layerViews.items[0].layer.sublayers).items.map(s=>({label:s.name,value:s.name}));t.options=p,i.value&&(i.value.dataForm.layerid=r.layerIds)}else r.layerIds=ee(o.view),K(r.layerIds).then(t=>{var m,n;r.layerInfos=((n=(m=t.data)==null?void 0:m.result)==null?void 0:n.rows)||[];const a=g.group[1].fields[0],p=r.layerInfos.filter(l=>l.geometrytype==="esriGeometryPoint").map(l=>({label:l.layername,value:l.layerid,data:l})),s=r.layerInfos.filter(l=>l.geometrytype==="esriGeometryPolyline").map(l=>({label:l.layername,value:l.layerid,data:l}));a&&(a.options=[{label:"管点类",value:-1,children:p,disabled:!0},{label:"管线类",value:-2,children:s,disabled:!0}]),i.value&&(i.value.dataForm.layerid=[])})},T=()=>{var e;(e=i.value)!=null&&e.dataForm&&(i.value.dataForm.sql="")},V=async()=>{var t,a;if(!r.curFieldNode)return;const e=(t=i.value)==null?void 0:t.dataForm.layerid;if(!(e!=null&&e.length)){v.warning("请先选择一个图层");return}r.curOperate="uniqueing";try{if(GIS_SERVER_SWITCH)se({layerName:e[0],fiedName:r.curFieldNode.name}).then(p=>{var f;let s=p.data;const m=new Set;s.features.forEach(c=>{let h=c.properties[r.curFieldNode.name];h===null&&(h=""),m.add(h)});const n=(f=g.group.find(c=>c.id==="field-construct"))==null?void 0:f.fields[0].extraFormItem,l=n&&n[0];l&&(l.data=m)});else{const p=await ae({usertoken:J().gToken,layerid:e[0],f:"pjson",field_name:r.curFieldNode.name}),s=(a=g.group.find(n=>n.id==="field-construct"))==null?void 0:a.fields[0].extraFormItem,m=s&&s[0];m&&(m.data=p.data.result.rows)}}catch{v.error("获取唯一值失败")}r.curOperate=""},d=e=>{var a;if(!i.value)return;(a=i.value)!=null&&a.dataForm||(i.value.dataForm={});const t=i.value.dataForm.sql||" ";i.value.dataForm.sql=t+e+" "},_=async()=>{var e,t,a,p,s,m,n,l,f;try{r.tabs.length=0,r.curOperate="detailing";const c=(e=i.value)==null?void 0:e.dataForm.layerid;c!=null&&c.length?GIS_SERVER_SWITCH?pe(c,(a=(t=o.graphics)==null?void 0:t.geometry)==null?void 0:a.rings[0],(s=(p=i.value)==null?void 0:p.dataForm)==null?void 0:s.sql).then(h=>{var q;let b=h.data.features;const C=new Set;b.forEach(y=>{y.id.split(".")[0]!==void 0&&C.add(y.id.split(".")[0])}),Array.from(C).forEach(y=>{let G=b.filter(z=>z.id.split(".")[0]===y);r.tabs.push({name:y,label:`${y}(${G.length})`,data:G})}),(q=x.value)==null||q.refreshDetail(r.tabs)}):(r.tabs=await te(c,r.layerInfos,{where:((n=(m=i.value)==null?void 0:m.dataForm)==null?void 0:n.sql)||"1=1",geometry:(l=o.graphics)==null?void 0:l.geometry}),(f=x.value)==null||f.refreshDetail(r.tabs)):v.warning("请选择一个要查询的图层")}catch(c){v.error(c.message)}r.curOperate=""},u=oe("manual"),M=()=>{var e,t;if(!((t=(e=i.value)==null?void 0:e.dataForm.layerid)!=null&&t.length)){v.warning("请选择一个要查询的图层");return}u.openSaveDialog()},D=async e=>{var a,p,s,m;const t=u.parseScheme(e);(a=i.value)!=null&&a.dataForm&&(i.value.dataForm.layerid=t.layerid||[],i.value.dataForm.sql=t.sql),t.graphic&&(o.graphics=Y.fromJSON(t.graphic),(p=I.value)==null||p.cancel(),(s=o.graphicsLayer)==null||s.removeAll(),(m=o.graphicsLayer)==null||m.add(o.graphics)),_()},B=e=>{var t,a;u.submitScheme({...e,type:u.schemeType.value,detail:JSON.stringify({layerid:((t=i.value)==null?void 0:t.dataForm.layerid)||[],graphic:o.graphics,sql:(a=i.value)==null?void 0:a.dataForm.sql})})},{initSketch:W,destroySketch:$,sketch:I}=ie(),k=e=>{e.state==="complete"&&(o.graphics=e.graphics[0],console.log(JSON.stringify(o.graphics)))},H=async e=>{o.view=e,o.graphicsLayer=Z(o.view,{id:"search-manual",title:"高级查询"}),W(o.view,o.graphicsLayer,{updateCallBack:k,createCallBack:k}),setTimeout(()=>{L()},1e3)};return P(()=>{var e,t;(e=o.graphicsLayer)==null||e.removeAll(),(t=o.graphicsLayer)==null||t.destroy(),$()}),(e,t)=>{const a=j;return U(),Q(me,{ref_key:"refMap",ref:x,title:"高级查询",onMapLoaded:H},{"right-title":O(()=>[w(ne,{title:"高级查询",onSchemeClick:S(u).openManagerDialog},null,8,["onSchemeClick"])]),default:O(()=>[w(a,{ref_key:"refForm",ref:i,config:S(g)},null,8,["config"]),w(ce,{ref:S(u).getSchemeManageRef,type:S(u).schemeType.value,onRowClick:D},null,8,["type"]),w(de,{ref:S(u).getSaveSchemeRef,onSubmit:B},null,512)]),_:1},512)}}}),_r=X(ue,[["__scopeId","data-v-190d01a4"]]);export{_r as default};
