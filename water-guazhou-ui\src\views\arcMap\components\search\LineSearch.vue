<!-- 沿线查询 -->
<template>
  <RightDrawerMap
    ref="refMap"
    :title="'沿线查询'"
    @map-loaded="onMapLoaded"
    @detail-refreshed="state.curOperate = ''"
  >
    <template #right-title>
      <SchemeHeader
        :title="'沿线查询'"
        @scheme-click="scheme.openManagerDialog"
      ></SchemeHeader>
    </template>
    <Form
      ref="refForm"
      :config="FormConfig"
    ></Form>
    <SchemeManage
      :ref="scheme.getSchemeManageRef"
      :type="scheme.schemeType.value"
      @row-click="handleUseScheme"
    ></SchemeManage>
    <SaveScheme
      :ref="scheme.getSaveSchemeRef"
      @submit="handleSchemeSubmit"
    ></SaveScheme>
  </RightDrawerMap>
</template>
<script lang="ts" setup>
import Graphic from '@arcgis/core/Graphic'
import {
  excuteIdentify,
  getGraphicLayer,
  getSubLayerIds,
  initIdentifyParams,
  setMapCursor,
  setSymbol
} from '@/utils/MapHelper'
import {
  excuteIdentifyByGeoserver,
  convertGeoJSONToArcGIS,
  getPipeAttachmentByGeoserver
} from '@/utils/geoserver/geoserverUtils.js'

// 定义getPipeAttachmentByGeoserver函数的参数类型
interface GetPipeAttachmentByGeoserverParams {
  nodeGeometries?: any[];
  flagOIDs?: string;
  nodeLayerName?: string;
  outputLayers: string;
}
import { IFormIns } from '@/components/type'
import { queryLayerClassName } from '@/api/mapservice'
import { SLMessage } from '@/utils/Message'
import RightDrawerMap from '../common/RightDrawerMap.vue'
import { GetPipeAttachment } from '@/api/mapservice/pipe'
import SchemeHeader from './Scheme/SchemeHeader.vue'
import SchemeManage from './Scheme/SchemeManage.vue'
import SaveScheme from './Scheme/SaveScheme.vue'
import { useScheme } from '@/hooks/arcgis/useScheme'

const refMap = ref<InstanceType<typeof RightDrawerMap>>()
const refForm = ref<IFormIns>()

const state = reactive<{
  tabs: any[]
  curOperate: 'picking' | 'searching' | ''
  layerInfos: any[]
  layerIds: any[]
}>({
  tabs: [],
  curOperate: '',
  layerInfos: [],
  layerIds: []
})
// 声明GIS_SERVER_SWITCH全局变量
declare global {
  interface Window {
    GIS_SERVER_SWITCH: boolean;
    SITE_CONFIG: any;
  }
}

// 定义GeoServer图层类型
interface GeoServerLayer {
  name: string;
  title?: string;
}

// 定义WMS图层类型
interface WMSLayer extends __esri.Layer {
  sublayers?: GeoServerLayer[];
}

const staticState: {
  view?: __esri.MapView
  graphicsLayer?: __esri.GraphicsLayer
  queryParams: {
    geometry?: __esri.Geometry
    where?: string
  }
  mapClick?: {
    remove(): void
  }
  identifyResults: __esri.Graphic[]
} = {
  queryParams: {
    geometry: undefined,
    where: undefined
  },
  identifyResults: []
}
const FormConfig = reactive<IFormConfig>({
  group: [
    {
      fieldset: {
        desc: '依次点选两个节点后进行查询'
      },
      fields: [
        {
          type: 'tree',
          options: [],
          checkStrictly: false,
          label: '选择图层',
          showCheckbox: true,
          field: 'layerid',
          nodeKey: 'value'
        },
        {
          type: 'btn-group',
          itemContainerStyle: {
            marginBottom: '8px'
          },
          btns: [
            {
              perm: true,
              type: 'warning',
              text: () => (state.curOperate === 'picking' ? '拾取节点中...' : '拾取节点'),
              styles: {
                width: '100%'
              },
              loading: () => state.curOperate === 'picking',
              click: () => startPick()
            }
          ]
        },
        {
          type: 'btn-group',
          itemContainerStyle: {
            marginBottom: '8px'
          },
          btns: [
            {
              perm: true,
              text: () => (state.curOperate === 'searching' ? '查询中，请稍候...' : '查询'),
              styles: {
                width: '50%'
              },
              loading: () => state.curOperate === 'searching',
              disabled: () => state.curOperate === 'picking',
              click: () => doQuery()
            },
            {
              perm: window.SITE_CONFIG.GIS_CONFIG.gisSaveScheme,
              text: '保存方案',
              styles: {
                width: '100%'
              },
              click: () => handleSaveScheme()
            },
            {
              perm: true,
              text: '清除',
              styles: {
                width: '50%'
              },
              type: 'danger',
              disabled: () => state.curOperate === 'searching',
              click: () => stopIdentify()
            }
          ]
        }
      ]
    }
  ],
  labelPosition: 'top',
  gutter: 12
})

const startPick = () => {
  if (!staticState.view) return
  stopIdentify()
  state.curOperate = 'picking'
  setMapCursor('crosshair')

  staticState.graphicsLayer?.removeAll()
  staticState.identifyResults = []
  staticState.mapClick = staticState.view?.on('click', async e => {
    await doIdentify(e)
  })
}

/**
 * 节点识别函数 - 用于沿线查询中识别用户点击的节点
 * 在沿线查询中，用户需要依次点选两个节点，然后查询这两个节点之间的管线
 */
const doIdentify = async (e: any) => {
  if (!staticState.view) return
  try {
    let feature: __esri.Graphic;

    if (window.GIS_SERVER_SWITCH) {
      // GeoServer模式 - 使用WMS服务查询节点
      const res = await excuteIdentifyByGeoserver(
        staticState.view,
        '/geoserver/guazhou/wms',
        '节点', // 使用选中的图层ID
        e
      )

      // 检查是否查询到节点
      if (!res || !res.data || !res.data.features || !res.data.features.length) {
        SLMessage.warning('没有查询到节点')
        return
      }

      // 将GeoJSON格式的几何对象转换为ArcGIS可以使用的几何对象
      const geoJsonFeature = res.data.features[0]
      const geometry = convertGeoJSONToArcGIS(geoJsonFeature.geometry)
      feature = new Graphic({
        geometry: geometry,
        attributes: geoJsonFeature.properties || {},
        symbol: setSymbol(geometry.type, { color: [255, 0, 0, 1] })
      })
    } else {
      // ArcGIS模式
      const queryParams = initIdentifyParams({
        layerIds: [1], // 使用默认图层ID
        geometry: e.mapPoint,
        mapExtent: staticState.view.extent
      })

      const res = await excuteIdentify(
        window.SITE_CONFIG.GIS_CONFIG.gisService + window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService,
        queryParams
      )

      if (!res.results?.length) {
        SLMessage.warning('没有查询到节点')
        return
      }

      feature = res.results[0].feature
      feature.symbol = setSymbol(feature.geometry.type, { color: [255, 0, 0, 1] })
    }

    staticState.identifyResults.push(feature)
    staticState.graphicsLayer?.add(feature)

    if (staticState.identifyResults.length >= 2) {
      staticState.mapClick?.remove()
      state.curOperate = ''
      setMapCursor('')
    }
  } catch (error) {
    console.error('节点查询失败:', error)
    staticState.view?.graphics.removeAll()
  }
}
/**
 * 执行沿线查询 - 查询两个节点之间的管线和设施
 * 在用户选择两个节点后，执行此函数查询节点之间的管线和设施信息
 */
const doQuery = async () => {
  // 检查是否已选择两个节点
  if (staticState.identifyResults.length !== 2) {
    SLMessage.warning('请依次选取两个节点后再试')
    return
  }

  // 获取用户在树状组件中选中的图层ID
  const layerIds = refForm.value?.dataForm.layerid || []
  if (!layerIds?.length) {
    SLMessage.warning('请选择要查询的图层')
    return
  }

  // 设置查询状态
  state.curOperate = 'searching'
  setMapCursor('progress') // 设置鼠标光标为进度状态

  try {
    // 获取选中的图层信息
    const layerInfos = state.layerInfos.filter(item => layerIds.indexOf(item.layerid) !== -1) || []
    // const layerInfos = refForm.value?.dataForm.layerid || [];
    let res: any;

    if (window.GIS_SERVER_SWITCH) {
      // GeoServer模式 - 使用空间查询
      // 获取节点几何信息
      const nodeGeometries = staticState.identifyResults.map(item => {
        // 提取节点的几何信息
        return item.geometry.toJSON();
      });

      // 同时也获取节点ID，作为备用
      const nodeIds = staticState.identifyResults.map(item => {
        // 尝试获取节点ID，优先使用id字段，如果没有则使用OBJECTID
        return item.attributes.id || item.attributes.OBJECTID || (item as any).id || '';
      }).join(',');

      // 获取节点图层名称
      const nodeLayerName = layerInfos.find(item =>
        item.geometrytype === 'esriGeometryPoint' ||
        item.layername.includes('点')
      )?.layername;

      console.log('节点几何信息:', nodeGeometries);
      console.log('节点ID:', nodeIds);
      console.log('节点图层名称:', nodeLayerName);

      // 调用GeoServer沿线查询API，使用空间查询
      res = await getPipeAttachmentByGeoserver({
        nodeGeometries: nodeGeometries, // 节点几何信息
        flagOIDs: nodeIds, // 节点ID，作为备用
        nodeLayerName: nodeLayerName, // 节点图层名称，用于查询节点几何信息
        outputLayers: layerIds.join(',') // 输出图层名称，逗号分隔
      } as any);
    } else {
      // ArcGIS模式
      res = await GetPipeAttachment({
        flagOIDs: staticState.identifyResults.map(item => item.attributes.OBJECTID).join(','),
        outputLayers: layerInfos.map(item => item.layername).join(',')
      });
    }

    if (!res.data.result) {
      if (res.data.code === 10000) {
        SLMessage.warning(res.data.message)
      } else {
        SLMessage.error(res.data.message || '查询失败')
      }
    } else {
      const tabs = layerInfos.map(layerInfo => {
        const item = res.data.result.find((o: any) => o.layeralias === layerInfo.layername)
        return {
          label: layerInfo.layername + '(' + (item?.queryresult?.length || 0) + ')',
          name: layerInfo.layername,
          data: item?.queryresult || []
        }
      })
      refMap.value?.refreshDetail(tabs)
    }
  } catch (error) {
    SLMessage.error('查询失败')
    console.error('沿线查询失败:', error)
  }
  setMapCursor('')
  state.curOperate = ''
}

/**
 * 获取图层信息 - 用于填充树状组件的选项
 * 在沿线查询中，用户需要选择要查询的图层，此函数获取可用的图层信息
 */
const getLayerInfo = () => {
  if (window.GIS_SERVER_SWITCH) {
    // GeoServer模式 - 从WMS图层获取子图层信息
    const field = FormConfig.group[0].fields[0] as IFormTree

    // 使用类型断言解决类型错误
    const layerInfo = (staticState.view?.layerViews as any)?.items?.[0]?.layer?.sublayers;

    if (layerInfo && layerInfo.items) {
      // 将子图层转换为树状组件的选项
      let layers = layerInfo.items.map((item: any) => {
        return {
          label: item.name, // 图层名称
          value: item.name, // 图层ID
          // data: item    // 图层数据（已注释）
        }
      });

      // 设置树状组件的选项
      field.options = layers; // 直接使用图层列表，不分类

      // 设置默认选中所有图层
      refForm.value && (refForm.value.dataForm.layerid = state.layerIds)
    }
  } else {
    // ArcGIS模式 - 使用ArcGIS Server的服务获取图层信息

    // 获取地图中的子图层ID
    state.layerIds = getSubLayerIds(staticState.view)

    // 查询图层名称和类型
    queryLayerClassName(state.layerIds).then(layerInfo => {
      // 保存图层信息
      state.layerInfos = layerInfo.data?.result?.rows || []

      // 构建表单树
      const field = FormConfig.group[0].fields[0] as IFormTree

      // 筛选点类型图层（用于节点查询）
      const points = state.layerInfos
        .filter(item => item.geometrytype === 'esriGeometryPoint')
        .map(item => {
          return {
            label: item.layername, // 图层名称
            value: item.layerid,   // 图层ID
            data: item             // 图层数据
          }
        })

      // 筛选线类型图层（用于管线查询）
      const lines = state.layerInfos
        .filter(item => item.geometrytype === 'esriGeometryPolyline')
        .map(item => {
          return {
            label: item.layername, // 图层名称
            value: item.layerid,   // 图层ID
            data: item             // 图层数据
          }
        })

      // 设置树状组件的选项，分为管点类和管线类
      field && (field.options = [
        { label: '管点类', value: -1, children: points },
        { label: '管线类', value: -2, children: lines }
      ])

      // 设置默认选中所有图层
      refForm.value && (refForm.value.dataForm.layerid = state.layerIds)
    })
  }

}
const stopIdentify = () => {
  staticState.mapClick?.remove()
  staticState.identifyResults = []
  staticState.graphicsLayer?.removeAll()
  refMap.value?.clearDetailData()
  state.curOperate = ''
  setMapCursor('')
}

const scheme = useScheme('line')
const handleSaveScheme = () => {
  if (staticState.identifyResults.length < 2) {
    SLMessage.warning('请先在地图上选择两个节点')
    return
  }
  scheme.openSaveDialog()
}
const handleUseScheme = async (row: any) => {
  stopIdentify()
  const detail = scheme.parseScheme(row)
  if (refForm.value?.dataForm) {
    refForm.value.dataForm.layerid = detail.layerid || []
  }
  if (detail.identifyResults.length) {
    staticState.identifyResults = detail.identifyResults.map((item: any) => {
      return Graphic.fromJSON(item)
    })
    staticState.graphicsLayer?.removeAll()
    staticState.graphicsLayer?.addMany(staticState.identifyResults)
  }
  doQuery()
}
const handleSchemeSubmit = (params: any) => {
  scheme.submitScheme({
    ...params,
    type: scheme.schemeType.value,
    detail: JSON.stringify({
      layerid: refForm.value?.dataForm.layerid || [],
      identifyResults: staticState.identifyResults
    })
  })
}
const onMapLoaded = (view: __esri.MapView) => {
  staticState.view = view
  staticState.graphicsLayer = getGraphicLayer(staticState.view, {
    id: 'search-line',
    title: '沿线查询'
  })
  setTimeout(()=>{
    getLayerInfo()
  },1000)
  // window.addEventListener('keydown', stopIdentify)
}
onBeforeUnmount(() => {
  staticState.mapClick?.remove()
  staticState.graphicsLayer?.removeAll()
  // window.removeEventListener('keydown', stopIdentify)
})
</script>
<style lang="scss" scoped></style>
