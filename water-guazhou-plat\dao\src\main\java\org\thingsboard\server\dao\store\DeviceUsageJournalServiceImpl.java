package org.thingsboard.server.dao.store;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.dao.model.sql.store.DeviceUsageJournal;
import org.thingsboard.server.dao.sql.department.DeviceUsageJournalMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.store.DeviceUsageJournalPageRequest;
import org.thingsboard.server.dao.util.imodel.query.store.DeviceUsageJournalSaveRequest;

@Service
public class DeviceUsageJournalServiceImpl implements DeviceUsageJournalService {
    @Autowired
    private DeviceUsageJournalMapper mapper;


    @Override
    public IPage<DeviceUsageJournal> findAllConditional(DeviceUsageJournalPageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    @Transactional
    public DeviceUsageJournal save(DeviceUsageJournalSaveRequest entity) {
        DeviceUsageJournal deviceUsageJournal = QueryUtil.saveOrUpdateOneByRequest(entity, mapper::insert, mapper::update);
        // storageService.updateScrappedTime(deviceUsageJournal.getDeviceLabelCode(),
        //         deviceUsageJournal.getReceiveTime(),
        //         entity.tenantId());
        return deviceUsageJournal;
    }

    @Override
    @Transactional
    public boolean update(DeviceUsageJournal entity) {
        boolean update = mapper.update(entity);
        // DeviceUsageJournal deviceUsageJournal = mapper.selectById(attr.getId());
        // storageService.updateScrappedTime(deviceUsageJournal.getDeviceLabelCode(),
        //         deviceUsageJournal.getReceiveTime(),
        //         deviceUsageJournal.getTenantId());
        return update;
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }

}
