package org.thingsboard.server.dao.model.sql.smartOperation.construction;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartOperation.SoGeneralTaskStatus;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.math.BigDecimal;
import java.util.List;


@Getter
@Setter
@ResponseEntity
public class SoConstructionExpenseContainer {
    // 所属工程编号
    private String constructionCode;

    // 所属工程名称
    private String constructionName;

    // 合同总金额，万元
    private BigDecimal contractTotalCost;

    // 结算总金额，万元
    private BigDecimal finalReportCost;

    // 已付总金额，万元
    private BigDecimal totalCost;

    // 当前状态
    @TableField(exist = false)
    private SoGeneralTaskStatus status;

    private List<SoConstructionExpense> items;

}
