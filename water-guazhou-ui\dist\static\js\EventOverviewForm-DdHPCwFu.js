import{d as G,c,r as J,am as I,m as K,x as _,g as v,h as b,F as i,p as h,q as r,G as F,bh as O,an as C,n as W,eL as z,I as Q,bU as X,H as Z,bW as ee,c2 as te,K as ae,J as oe,L as re,C as ie}from"./index-r0dFAfgr.js";import{_ as le}from"./FormMap-BGaXSqQF.js";import{U as se,C as de}from"./eventOverview-CiZ_uCa9.js";import"./ArcView-DpMnCY82.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./widget-BcWKanF2.js";import"./pe-B8dP0-Ut.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./TileLayer-B5vQ99gG.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./commonProperties-DqNQ4F00.js";import"./project-DUuzYgGl.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./IdentifyResult-4DxLVhTm.js";import"./ViewHelper-BGCZjxXH.js";import"./fieldconfig-Bk3o1wi7.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./LayerHelper-Cn-iiqxI.js";import"./pipe-nogVzCHG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./URLHelper-B9aplt5w.js";import"./ArcPipe.vue_vue_type_script_setup_true_lang-ClsVvV2P.js";import"./utils-D5nxoMq3.js";const pe={class:"map-selector"},ne={key:0,class:"map-selector-header"},me={class:"map-container"},ue={class:"dialog-footer"},ce=G({__name:"EventOverviewForm",props:{modelValue:{type:Boolean},isEdit:{type:Boolean},isView:{type:Boolean},editData:{}},emits:["update:modelValue","submit"],setup(U,{emit:B}){const t=U,T=B,n=c(!1),g=c(),w=c(!1),E=c([]),f=c([]),Y={value:"id",label:"name",children:"children",emitPath:!0,checkStrictly:!1},y=c([95.787329,40.516879]),a=J({typeCategory:[],type:"",typeName:"",title:"",createTime:"",address:"",remark:""}),R={typeCategory:[{required:!0,message:"请选择事件类型",trigger:"change"}],title:[{required:!0,message:"请输入事件名称",trigger:"blur"}],createTime:[{required:!0,message:"请选择创建时间",trigger:"change"}],address:[{required:!0,message:"请输入地址",trigger:"blur"}]};I(()=>t.modelValue,async e=>{n.value=e,e&&(await $(),M())}),I(n,e=>{T("update:modelValue",e)});const M=()=>{if((t.isEdit||t.isView)&&t.editData){if(Object.assign(a,t.editData),t.editData.coordinate){const e=t.editData.coordinate.split(",");y.value=[parseFloat(e[0]),parseFloat(e[1])]}f.value.length>0&&D(t.editData.type)}else{const e=new Date,s=new Date(e.getTime()+8*60*60*1e3).toISOString().slice(0,19).replace("T"," ");Object.assign(a,{typeCategory:[],type:"",typeName:"",title:"",createTime:s,address:"",remark:""}),y.value=[95.787329,40.516879]}},D=e=>{if(!(!e||f.value.length===0)){for(const o of f.value)if(o.children){const s=o.children.find(d=>d.id===e);if(s){a.typeCategory=[o.id,s.id],a.typeName=`${o.name} / ${s.name}`;break}}}},$=async()=>{var e,o;try{const s=await K({url:"/api/workOrderType/list",method:"get",params:{status:1}});if((e=s.data)!=null&&e.data){f.value=s.data.data;const d=s.data.data.filter(l=>l.parentId==="0"||l.parentId===0||!l.parentId);E.value=d.map(l=>({id:l.id,name:l.name,children:l.children||[]})),(t.isEdit||t.isView)&&((o=t.editData)!=null&&o.type)&&D(t.editData.type)}}catch(s){console.error("获取事件类型失败:",s),_.error("获取事件类型失败")}},q=e=>{if(e&&Array.isArray(e)&&e.length>=2){const o=e[e.length-1];a.type=o;const s=e[0],d=f.value.find(l=>l.id===s);if(d&&d.children){const l=d.children.find(m=>m.id===o);l&&(a.typeName=`${d.name} / ${l.name}`)}}else a.type="",a.typeName=""},H=e=>{var o,s;(s=(o=e.data)==null?void 0:o.result)!=null&&s.formatted_address&&(a.address=e.data.result.formatted_address)},L=e=>{e&&e.length===2&&(a.coordinate=`${e[0]},${e[1]}`,_.success("位置选择成功"))},S=async()=>{if(!t.isView&&g.value)try{await g.value.validate(),w.value=!0;const e={title:a.title,type:a.type,typeName:a.typeName,address:a.address,remark:a.remark,coordinate:a.coordinate,coordinateName:a.address};t.isEdit&&a.id?(await se({id:a.id,...e}),_.success("事件更新成功")):(await de(e),_.success("事件创建成功")),n.value=!1,T("submit",a)}catch{_.error(t.isEdit?"事件更新失败":"事件创建失败")}finally{w.value=!1}},k=()=>{var e;n.value=!1,(e=g.value)==null||e.resetFields(),y.value=[95.787329,40.516879]};return(e,o)=>{const s=z,d=Q,l=X,m=Z,u=ee,j=te,P=ae,x=oe,A=re;return v(),b(A,{modelValue:n.value,"onUpdate:modelValue":o[6]||(o[6]=V=>n.value=V),title:t.isView?"查看事件":t.isEdit?"编辑事件":"新增事件",width:"1000px","before-close":k},{footer:i(()=>[h("span",ue,[r(x,{onClick:k},{default:i(()=>[F(O(t.isView?"关闭":"取消"),1)]),_:1}),t.isView?C("",!0):(v(),b(x,{key:0,type:"primary",onClick:S,loading:w.value},{default:i(()=>[F(O(t.isEdit?"更新":"创建"),1)]),_:1},8,["loading"]))])]),default:i(()=>[r(P,{ref_key:"formRef",ref:g,model:a,rules:R,"label-width":"100px",class:"event-form"},{default:i(()=>{var V,N;return[r(u,{gutter:20},{default:i(()=>[r(l,{span:12},{default:i(()=>[r(d,{label:"事件类型",prop:"typeCategory"},{default:i(()=>[r(s,{modelValue:a.typeCategory,"onUpdate:modelValue":o[0]||(o[0]=p=>a.typeCategory=p),options:E.value,props:Y,placeholder:"请选择事件类型",style:{width:"100%"},disabled:t.isView,onChange:q},null,8,["modelValue","options","disabled"])]),_:1})]),_:1}),r(l,{span:12},{default:i(()=>[r(d,{label:"事件名称",prop:"title"},{default:i(()=>[r(m,{modelValue:a.title,"onUpdate:modelValue":o[1]||(o[1]=p=>a.title=p),placeholder:"请输入事件名称",disabled:t.isView},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),r(u,{gutter:20},{default:i(()=>[r(l,{span:12},{default:i(()=>[r(d,{label:"创建时间",prop:"createTime"},{default:i(()=>[r(j,{modelValue:a.createTime,"onUpdate:modelValue":o[2]||(o[2]=p=>a.createTime=p),type:"datetime",placeholder:"请选择创建时间",style:{width:"100%"},format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss",disabled:t.isView},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),r(u,{gutter:20},{default:i(()=>[r(l,{span:24},{default:i(()=>[r(d,{label:"地址",prop:"address"},{default:i(()=>[r(m,{modelValue:a.address,"onUpdate:modelValue":o[3]||(o[3]=p=>a.address=p),placeholder:"请输入地址",disabled:t.isView},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),r(u,{gutter:20},{default:i(()=>[r(l,{span:24},{default:i(()=>[r(d,{label:"备注",prop:"remark"},{default:i(()=>[r(m,{modelValue:a.remark,"onUpdate:modelValue":o[4]||(o[4]=p=>a.remark=p),type:"textarea",rows:3,placeholder:"请输入备注信息",disabled:t.isView},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),t.isView&&((V=t.editData)==null?void 0:V.status)==="REJECTED"&&((N=t.editData)!=null&&N.rejectReason)?(v(),b(u,{key:0,gutter:20},{default:i(()=>[r(l,{span:24},{default:i(()=>[r(d,{label:"驳回原因"},{default:i(()=>[r(m,{value:t.editData.rejectReason,type:"textarea",rows:3,readonly:"",style:{"background-color":"#f5f7fa"}},null,8,["value"])]),_:1})]),_:1})]),_:1})):C("",!0),r(u,{gutter:20},{default:i(()=>[r(l,{span:24},{default:i(()=>[r(d,{label:t.isView?"事件位置":"选择位置"},{default:i(()=>[h("div",pe,[t.isView?C("",!0):(v(),W("div",ne,o[7]||(o[7]=[h("span",null,"请在地图上点击选择事件发生位置",-1)]))),h("div",me,[r(le,{modelValue:y.value,"onUpdate:modelValue":o[5]||(o[5]=p=>y.value=p),"show-input":!0,"handle-inverse-geocodeing":H,style:{height:"350px"},onChange:L},null,8,["modelValue"])])])]),_:1},8,["label"])]),_:1})]),_:1})]}),_:1},8,["model"])]),_:1},8,["modelValue","title"])}}}),Yt=ie(ce,[["__scopeId","data-v-d5bef318"]]);export{Yt as default};
