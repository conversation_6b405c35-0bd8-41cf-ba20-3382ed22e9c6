import{d as s,c as t,r as c,g as r,h as i,i as p,_ as f,C as _}from"./index-r0dFAfgr.js";const u=s({__name:"MultiScreen",props:{view:{}},setup(d){const o=t(),l=c({labelPosition:"top",group:[{fieldset:{type:"simple",desc:"分屏数量"},fields:[{type:"radio",label:"",field:"count",options:[{label:"1屏",value:1},{label:"2屏",value:2},{label:"4屏",value:4},{label:"9屏",value:9}],onChange:e=>a(e)}]},{fieldset:{desc:"模式选择",type:"simple"},fields:[{type:"radio",label:"",field:"type",options:[{label:"中心同步",value:"center-sync"},{label:"缩放同步",value:"zoom-sync"}]}]}]}),a=e=>{console.log(e)};return(e,m)=>{const n=f;return r(),i(n,{ref_key:"refForm",ref:o,config:p(l)},null,8,["config"])}}}),v=_(u,[["__scopeId","data-v-7b4eb7de"]]);export{v as default};
