<template>
  <div class="one-map-detail">
    <div class="row1">
      <OrderStepTags
        :config="OrderStepTagsVale"
        style="margin-bottom: 10px; padding: 0"
      />
      <el-descriptions
        title=""
        :border="true"
        :column="2"
      >
        <el-descriptions-item label="工单编号">
          {{ state.curRow?.serialNo }}
        </el-descriptions-item>
        <el-descriptions-item label="事件名称">
          {{ state.curRow?.title }}
        </el-descriptions-item>
        <el-descriptions-item label="事件来源">
          {{ state.curRow?.source }}
        </el-descriptions-item>
        <el-descriptions-item label="上报时间">
          {{ state.curRow?.createTime }}
        </el-descriptions-item>
        <el-descriptions-item label="上报人">
          {{ state.curRow?.uploadUserName }}
        </el-descriptions-item>
        <el-descriptions-item label="所属部门">
          {{ state.curRow?.uploadUserDepartmentName }}
        </el-descriptions-item>
        <el-descriptions-item label="事件类型">
          {{ state.curRow?.type }}
        </el-descriptions-item>
        <el-descriptions-item label="事件内容">
          {{ state.curRow?.remark }}
        </el-descriptions-item>
        <el-descriptions-item label="紧急程度">
          {{ state.curRow?.level }}
        </el-descriptions-item>
        <el-descriptions-item label="处理级别">
          {{ state.curRow?.processLevel }}
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <div class="row2">
      <el-tabs
        type="border-card"
        class="source-tabs"
        :class="useAppStore().isDark ? 'darkblue' : ''"
      >
        <el-tab-pane label="图片">
          <div class="panel-centent overlay-y flex-row-wrap">
            <el-image
              v-for="(img, i) in state.imgUrls"
              :key="i"
              :style="{
                width: '100px',
                height: '100px',
                padding: '8px'
              }"
              alt="图片"
              :fit="'cover'"
              :preview-src-list="state.imgUrls"
              :initial-index="i"
              :close-on-press-escape="true"
              :hide-on-click-modal="true"
              :preview-teleported="true"
              :src="img"
            ></el-image>
          </div>
        </el-tab-pane>
        <el-tab-pane label="视频">
          <div class="panel-centent overlay-y flex-row-wrap">
            <Videor
              v-for="(video, i) in state.videoUrls"
              :key="i"
              :url="video"
              :height="164"
              :width="291"
            />
          </div>
        </el-tab-pane>
        <el-tab-pane label="音频">
          <div class="panel-centent overlay-y">
            <Voicer
              v-for="(audio, i) in state.audioUrls"
              :key="i"
              :url="audio"
              :size="'large'"
              :show-url="true"
              :download="true"
              class="voicer"
            />
          </div>
        </el-tab-pane>
        <el-tab-pane label="附件">
          <div class="panel-centent overlay-y">
            <div
              v-for="(file, i) in state.otherFiles"
              :key="i"
              class="downloadable file-url"
              @click="downloadFile(file)"
            >
              {{ file }}
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>
<script lang="ts" setup>
import OrderStepTags from '../../components/components/OrderStepTags.vue'
import { useAppStore } from '@/store'
import { downloadFile } from '@/utils/fileHelper'
import { formatWorkOrderStatus } from '../../components/components/config'

const emit = defineEmits(['refresh', 'mounted'])
const state = reactive<{
  imgUrls: string[]
  otherFiles: string[]
  audioUrls: string[]
  videoUrls: string[]
  curRow?: any
}>({
  otherFiles: [],
  audioUrls: [],
  imgUrls: [],
  videoUrls: []
})
const OrderStepTagsVale = reactive<any>({
  status: 'SUBMIT',
  statusName: '审核中',
  colum: true
})

const refreshDetail = row => {
  emit('refresh', { title: row.title })
  state.curRow = row
  state.otherFiles = (row.otherFileUrl && row.otherFileUrl?.split(',')) || []
  state.audioUrls = (row.audioUrl && row.audioUrl?.split(',')) || []
  state.videoUrls = (row.videoUrl && row.videoUrl?.split(',')) || []
  state.imgUrls = (row.imgUrl && row.imgUrl?.split(',')) || []
  OrderStepTagsVale.colum = true
  OrderStepTagsVale.status = row.status
  OrderStepTagsVale.statusName = formatWorkOrderStatus(row.status) || ''
}
defineExpose({
  refreshDetail
})
onMounted(() => {
  emit('mounted')
})
</script>
<style lang="scss" scoped>
.one-map-detail {
  .row1 {
    margin-bottom: 20px;
    padding: 8px;
    padding-bottom: 0;
  }
  .row2 {
    padding: 8px;
    height: 480px;
  }
}

.source-tabs {
  height: 100%;
  :deep(.el-tabs__content) {
    height: calc(100% - 40px);
    .el-tab-pane {
      height: 100%;
      overflow-y: auto;
    }
  }
}
.voicer {
  margin-bottom: 8px;
  &:last-child {
    margin-bottom: 0;
  }
}
.panel-centent {
  width: 100%;
  height: 100%;
}
.flex-row-wrap {
  display: flex;
  flex-wrap: wrap;
}
.downloadable {
  text-decoration: underline;
  color: cadetblue;
  cursor: pointer;
  text-overflow: ellipsis;
  overflow: hidden;
  &:hover {
    color: var(--el-color-primary);
  }
}
.file-url {
  line-height: 25px;
}
</style>
