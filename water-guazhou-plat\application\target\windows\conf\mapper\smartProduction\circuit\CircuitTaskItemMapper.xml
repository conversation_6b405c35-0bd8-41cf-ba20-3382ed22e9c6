<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartProduction.circuit.CircuitTaskItemMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->id,
                           main_id,
                           type,
                           item_type,
                           item_name,
                           item_method,
                           item_require,
                           result,
                           result_remark,
                           file,
                           work_order_id,
                           execution_time,
                           execution_user_id,
                           tenant_id<!--@sql from sp_circuit_task_item -->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.smartProduction.circuit.CircuitTaskItem">
        <result column="id" property="id"/>
        <result column="main_id" property="mainId"/>
        <result column="type" property="type"/>
        <result column="item_type" property="itemType"/>
        <result column="item_name" property="itemName"/>
        <result column="item_method" property="itemMethod"/>
        <result column="item_require" property="itemRequire"/>
        <result column="result" property="result"/>
        <result column="result_remark" property="resultRemark"/>
        <result column="file" property="file"/>
        <result column="work_order_id" property="workOrderId"/>
        <result column="execution_time" property="executionTime"/>
        <result column="execution_user_id" property="executionUserId"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sp_circuit_task_item
        <where>
            <if test="mainId != null and mainId != ''">
                and main_id = #{mainId}
            </if>
            <if test="type != null and type != ''">
                and type = #{type}
            </if>
            <if test="itemType != null and itemType != ''">
                and item_type = #{itemType}
            </if>
            <if test="itemName != null and itemName != ''">
                and item_name = #{itemName}
            </if>
            <if test="itemMethod != null and itemMethod != ''">
                and item_method = #{itemMethod}
            </if>
            <if test="itemRequire != null and itemRequire != ''">
                and item_require = #{itemRequire}
            </if>
            <if test="result != null and result != ''">
                and result = #{result}
            </if>
            <if test="resultRemark != null and resultRemark != ''">
                and result_remark = #{resultRemark}
            </if>
            <if test="workOrderId != null and workOrderId != ''">
                and work_order_id = #{workOrderId}
            </if>
            <if test="executionTime != null">
                and onday(execution_time, #{executionTime})
            </if>
            <if test="executionUserId != null and executionUserId != ''">
                and execution_user_id = #{executionUserId}
            </if>
            and tenant_id = #{tenantId}
        </where>
    </select>

    <update id="update">
        update sp_circuit_task_item
        <set>
            <if test="mainId != null">
                main_id = #{mainId},
            </if>
            <if test="type != null">
                type = #{type},
            </if>
            <if test="itemType != null">
                item_type = #{itemType},
            </if>
            <if test="itemName != null">
                item_name = #{itemName},
            </if>
            <if test="itemMethod != null">
                item_method = #{itemMethod},
            </if>
            <if test="itemRequire != null">
                item_require = #{itemRequire},
            </if>
            <if test="result != null">
                result = #{result},
            </if>
            <if test="resultRemark != null">
                result_remark = #{resultRemark},
            </if>
            <if test="file != null">
                file = #{file},
            </if>
            <if test="workOrderId != null">
                work_order_id = #{workOrderId},
            </if>
            <if test="executionTime != null">
                execution_time = #{executionTime},
            </if>
            <if test="executionUserId != null">
                execution_user_id = #{executionUserId},
            </if>
        </set>
        where id = #{id}
    </update>

    <insert id="saveAll">
        INSERT INTO sp_circuit_task_item(id,
                                         main_id,
                                         type,
                                         item_type,
                                         item_name,
                                         item_method,
                                         item_require,
                                         result,
                                         result_remark,
                                         file,
                                         work_order_id,
                                         execution_time,
                                         execution_user_id,
                                         tenant_id)VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (#{element.id},
             #{element.mainId},
             #{element.type},
             #{element.itemType},
             #{element.itemName},
             #{element.itemMethod},
             #{element.itemRequire},
             #{element.result},
             #{element.resultRemark},
             #{element.file},
             #{element.workOrderId},
             #{element.executionTime},
             #{element.executionUserId},
             #{element.tenantId})
        </foreach>
    </insert>

    <update id="updateAll">
        update sp_circuit_task_item
        <set>
            main_id           = valueTable.mainId,
            type              = valueTable.type,
            item_type         = valueTable.itemType,
            item_name         = valueTable.itemName,
            item_method       = valueTable.itemMethod,
            item_require      = valueTable.itemRequire,
            result            = valueTable.result,
            result_remark     = valueTable.resultRemark,
            file              = valueTable.file,
            work_order_id     = valueTable.workOrderId,
            execution_time    = valueTable.executionTime,
            execution_user_id = valueTable.executionUserId
        </set>
        FROM (
        VALUES
        <foreach collection="list" item="element" separator=",">
            (#{element.id},
             #{element.mainId},
             #{element.type},
             #{element.itemType},
             #{element.itemName},
             #{element.itemMethod},
             #{element.itemRequire},
             #{element.result},
             #{element.resultRemark},
             #{element.file},
             #{element.workOrderId},
             #{element.executionTime},
             #{element.executionUserId})
        </foreach>
        ) as valueTable(id, mainId, type, itemType, itemName, itemMethod, itemRequire, result, resultRemark, file,
                        workOrderId, executionTime, executionUserId)
        where id = valueTable.id
    </update>

    <update id="complete">
        update sp_circuit_task_item
        <set>
            execution_user_id = #{userId},
            result            = #{result},
            work_order_id     = #{workOrderId},
            result_remark     = #{resultRemark},
            file              = #{file},
        </set>
        where id = #{id}
          and (select count(1) > 0
               from sp_circuit_task
               where sp_circuit_task.id = main_id
                 and sp_circuit_task.execution_user_id like #{userId})
    </update>

    <select id="buildBySetting" resultMap="BaseResultMap">
        select #{mainId}   main_id,
               cfg.type,
               cfg.item_type,
               cfg.name    item_name,
               cfg.method  item_method,
               cfg.require item_require,
               cfg.tenant_id
        from sp_circuit_config cfg
        where cfg.id = #{setting}
    </select>

    <select id="isComplete" resultType="boolean">
        select count(1)
        from sp_circuit_task_item item
        where (select count(1) = 0
               from sp_circuit_task_item iitem
               where iitem.main_id = item.main_id
                 and iitem.result is null)
    </select>

    <update id="markParentComplete">
        update sp_circuit_task task
        set real_end_time = now()
        where task.id = (select item.main_id from sp_circuit_task_item item where item.id = #{id})
    </update>
</mapper>