/* 转移接口 */
import request from '@/plugins/axios';

/**
 * 新增
 * @param params
 * @returns
 */
export function saveData(params) {
  return request({
    url: '/api/assets/transfer',
    method: 'post',
    data: params
  });
}
/**
 * 审核
 * @param params
 * @returns
 */
export function review(params) {
  return request({
    url: '/api/assets/transfer/review',
    method: 'post',
    data: params
  });
}

/**
 * 分页查询列表
 * @param params
 * @returns
 */
export const getTableData = (params: any) =>
  request({
    url: '/api/assets/transfer/page',
    method: 'get',
    params
  });
