package org.thingsboard.server.dao.sql.user;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.thingsboard.server.dao.model.sql.UserSchedule;

import java.util.Date;
import java.util.List;

public interface UserScheduleRepository extends JpaRepository<UserSchedule, String> {

    @Query("SELECT us FROM UserSchedule us " +
            "WHERE us.userId = ?3 AND us.tenantId = ?4 AND us.time >= ?1 AND us.time <= ?2 " +
            "ORDER BY us.time, us.createTime")
    List<UserSchedule> findList(Date beginTime, Date endTime, String userId, String tenantId);
}
