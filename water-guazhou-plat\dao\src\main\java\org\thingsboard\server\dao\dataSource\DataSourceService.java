package org.thingsboard.server.dao.dataSource;

import org.thingsboard.server.common.data.Tenant;
import org.thingsboard.server.common.data.dataSource.*;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.model.sql.DataSourceEntity;
import org.thingsboard.server.dao.model.sql.OriginDataEntity;
import org.thingsboard.server.dao.model.sql.RestApiEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/2/26 14:35
 */
public interface DataSourceService {

    /**
     * 根据发起者ID和数据源类别查找数据源列表
     *
     * @param projectId 发起者ID
     * @param type      数据源类别
     * @return List<DataSource>
     */
    List<DataSourceEntity> findByProjectIdAndType(String projectId, DataSourceType type);

    /**
     * 根据ID获取数据源详情
     *
     * @param id 数据源ID
     * @return 数据源详情
     */
    DataSourceEntity findById(String id);


    /**
     * 根据发起者ID获取数据源列表并根据数据源类别进行分组
     *
     * @param originatorId 发起者id
     * @return 数据源分组
     */
    List<DataSourceGroup> findDataSourceGroupByOriginatorId(String originatorId);


    /**
     * 根据发起者ID和数据源类别查询所有数据源列表(包括不可用)
     *
     * @param originatorId 发起者ID
     * @param type         数据源类别
     * @return 数据源列表
     */
    List<DataSourceEntity> findAllByOriginatorId(String originatorId, DataSourceType type);

    /**
     * 保存数据源
     *
     * @param baseDataSources 数据源组
     * @return List<DataSource>
     */
    List<DataSourceEntity> save(TenantId tenantId, List<DataSourceEntity> baseDataSources, String projectId, String type) throws ThingsboardException;

    /**
     * 删除数据源
     *
     * @param dataSourceId 数据源ID
     * @return boolean
     */
    boolean deleteDataSource(String dataSourceId);

    /**
     * 根据数据源ID列表来获取对应数据源的数据
     *
     * @param dataSourceRequest 数据源获取数据的标准请求，包含数据源ID，起始时间和结束时间，以及数据间隔
     * @return 数据
     */
    List<DataFromDataSource> findDataByDataSource(DataSourceRequest dataSourceRequest);

    /**
     * 根据数据源ID列表来获取对应数据源的数据
     *
     * @param dataSourceRequest 数据源获取数据的标准请求，包含数据源ID，起始时间和结束时间，以及数据间隔
     * @return 数据
     */
    List<DataFromDataSource> findLastDataByDataSource(DataSourceRequest dataSourceRequest);


    /**
     * 根据数据源ID获取最后一次数据
     *
     * @param dataSourceId 数据源ID
     * @return 数值
     */
    String findDataByDataSourceById(String dataSourceId);

    /**
     * 根据数据源ID获取最后一次数据
     *
     * @param dataSourceId 数据源ID
     * @return 数值
     */
    DataFromDataSource findDataFromDataSourceByDataSourceById(String dataSourceId);

    /**
     * 保存中间变量数据源
     *
     * @param originDataEntity 数据源数据
     * @return 保存结果
     */
    boolean setOriginData(OriginDataEntity originDataEntity);

    /**
     * 绑定发起者的预设统计变量
     *
     * @param preparations 预设统计变量
     * @param originatorId 发起者ID
     * @return 绑定结果
     */
    boolean mountPreparation(TenantId tenantId, List<PreparationVO> preparations, String originatorId, String entityType);

    /**
     * 新建设备时保存设备数据源
     *
     * @param deviceId 设备ID
     * @param prop     设备属性
     * @param tenantId 企业ID
     * @return 是否保存成功
     */
    boolean saveDeviceDataSource(TenantId tenantId, String deviceId, String prop);


    /**
     * 获取设备数据源的最后一次数据列表
     *
     * @param dataSourceId 数据源ID
     * @return 数据列表
     */
    List<ImmediateData> getDeviceSourceImmediateData(String dataSourceId);


    /**
     * 根据类别和企业id进行查询
     *
     * @param name     类别
     * @param tenantId 企业ID
     * @return 数据源列表
     */
    List<DataSourceEntity> findByTypeAndTenantId(String name, String tenantId);

    /**
     * 校验发起者下相同名称的数据源数量
     *
     * @param originatorId 发起者ID
     * @param name         数据源名称
     * @return 数据源数量
     */
    int countByOriginatorIdAndName(String originatorId, String name);

    /**
     * 保存restApi
     * @param restApiEntity restApi
     * @return restApi
     */
    RestApiEntity saveRestApi(RestApiEntity restApiEntity);

    /**
     * 根据项目ID查询restapi
     * @param projectId
     * @return
     */
    List<RestApiEntity> getRestApiByProjectId(String projectId);

    /**
     * 删除RestApi配置
     * @param id
     * @return
     */
    boolean deleteRestApi(String id);

    List<RestApiEntity> findAll();

    /**
     * 校验发起者下相同名称的API数据源数量
     *
     * @param tenantId 企业ID
     * @param name         数据源名称
     * @return 数据源数量
     */
    int countRestApiName(TenantId tenantId, String name);

    /**
     * 删除数据源
     *
     * @param deviceId 数据源ID
     * @return boolean
     */
    boolean deleteDeviceDataSource(String deviceId);
    
    List<RestApiEntity> findByTenantId(TenantId tenantId);

    List<DataSourceEntity> findDataSourceByOriginator(String originator);

    List<DataSourceEntity> findAllByOriginatorIdInAndProperty(List<String> deviceIdList, String property);
}
