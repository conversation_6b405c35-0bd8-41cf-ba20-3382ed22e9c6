import{d as p,r as _,g as a,n as u,p as s,ax as c,i as t,h as n,cW as f,cX as m,aw as r,j as S,C as k}from"./index-r0dFAfgr.js";const h=p({__name:"TreeBox",emits:["collapse","collapsed"],setup(v,{emit:i}){const o=i,e=_({treeState:!1}),d=()=>{e.treeState=!e.treeState,o("collapse",e.treeState),setTimeout(()=>{o("collapsed",e.treeState)},600)};return(l,B)=>(a(),u("div",{class:r(["layout-tree-detail-container",{dark:t(S)().isDark}])},[s("div",{class:r(["left-project-tree-list",{"tree-hidden":t(e).treeState}])},[c(l.$slots,"tree",{},void 0,!0),s("p",{class:"control-fold-btn",onClick:d},[t(e).treeState?(a(),n(t(f),{key:0})):(a(),n(t(m),{key:1}))])],2),s("div",{class:r(["right-detail-box",{"fill-width":t(e).treeState}])},[c(l.$slots,"default",{},void 0,!0)],2)],2))}}),g=k(h,[["__scopeId","data-v-54e53769"]]);export{g as _};
