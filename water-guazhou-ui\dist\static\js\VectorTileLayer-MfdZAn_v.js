import{i as J,s as I,S as C,j as X,N as Y,ak as H,am as Z,w as ee,Z as R,e as c,y as h,b as te,m as re,o as se,a as ie}from"./Point-WxyopZva.js";import{L as _,A as oe,U as A,Y as q,V as P,d as v,X as le,i as Q,u as V,K as F,C as ae,w as z,y as ne}from"./pe-B8dP0-Ut.js";import{bs as K,w as W,cM as ue,z as O,c as pe,t as ce,eg as he,Q as ye,R as de,de as fe,ef as me,V as ge,gR as Ae,dO as M,bp as Se,e as we}from"./MapView-DaoQedLH.js";import{cm as ve,fL as xe,a5 as x,aS as E,aO as _e}from"./index-r0dFAfgr.js";import{s as Ue}from"./ArcGISCachedService-CQM8IwuM.js";import{n as be,z as Ie}from"./TilemapCache-BPMaYmR0.js";import{e as Re}from"./jsonContext-C-xrKYgv.js";import{l as $e}from"./StyleRepository-CdCHyVhB.js";import"./widget-BcWKanF2.js";import"./StyleDefinition-Bnnz5uyC.js";import"./enums-B5k73o5q.js";import"./enums-BRzLM11V.js";import"./enums-BDQrMlcz.js";import"./VertexElementDescriptor-BOD-G50G.js";import"./GeometryUtils-B7ExOJII.js";import"./definitions-826PWLuy.js";let U=null;function Te(e){if(U)return U;const t={lossy:"UklGRiIAAABXRUJQVlA4IBYAAAAwAQCdASoBAAEADsD+JaQAA3AAAAAA",lossless:"UklGRhoAAABXRUJQVlA4TA0AAAAvAAAAEAcQERGIiP4HAA==",alpha:"UklGRkoAAABXRUJQVlA4WAoAAAAQAAAAAAAAAAAAQUxQSAwAAAARBxAR/Q9ERP8DAABWUDggGAAAABQBAJ0BKgEAAQAAAP4AAA3AAP7mtQAAAA==",animation:"UklGRlIAAABXRUJQVlA4WAoAAAASAAAAAAAAAAAAQU5JTQYAAAD/////AABBTk1GJgAAAAAAAAAAAAAAAAAAAGQAAABWUDhMDQAAAC8AAAAQBxAREYiI/gcA"};return U=new Promise(r=>{const s=new Image;s.onload=()=>{s.onload=s.onerror=null,r(s.width>0&&s.height>0)},s.onerror=()=>{s.onload=s.onerror=null,r(!1)},s.src="data:image/webp;base64,"+t[e]}),U}const j=1.15;let B=class{constructor(t,r){this._spriteSource=t,this._maxTextureSize=r,this.devicePixelRatio=1,this._spriteImageFormat="png",this._isRetina=!1,this._spritesData={},this.image=null,this.width=null,this.height=null,this.loadStatus="not-loaded",t.type==="url"&&t.spriteFormat&&(this._spriteImageFormat=t.spriteFormat),t.pixelRatio&&(this.devicePixelRatio=t.pixelRatio),this.baseURL=t.spriteUrl}get spriteNames(){const t=[];for(const r in this._spritesData)t.push(r);return t.sort(),t}getSpriteInfo(t){return this._spritesData?this._spritesData[t]:null}async load(t){if(this.baseURL){this.loadStatus="loading";try{await this._loadSprites(t),this.loadStatus="loaded"}catch{this.loadStatus="failed"}}else this.loadStatus="failed"}async _loadSprites(t){this._isRetina=this.devicePixelRatio>j;const{width:r,height:s,data:i,json:o}=await this._getSpriteData(this._spriteSource,t),l=Object.keys(o);if(!l||l.length===0||!i)return this._spritesData=this.image=null,void(this.width=this.height=0);this._spritesData=o,this.width=r,this.height=s;const n=Math.max(this._maxTextureSize,4096);if(r>n||s>n){const a=`Sprite resource for style ${this.baseURL} is bigger than the maximum allowed of ${n} pixels}`;throw J.getLogger("esri.layers.support.SpriteSource").error(a),new I("SpriteSource",a)}let u;for(let a=0;a<i.length;a+=4)u=i[a+3]/255,i[a]=i[a]*u,i[a+1]=i[a+1]*u,i[a+2]=i[a+2]*u;this.image=i}async _getSpriteData(t,r){if(t.type==="image"){let y,d;if(this.devicePixelRatio<j){if(!t.spriteSource1x)throw new I("SpriteSource","no image data provided for low resolution sprites!");y=t.spriteSource1x.image,d=t.spriteSource1x.json}else{if(!t.spriteSource2x)throw new I("SpriteSource","no image data provided for high resolution sprites!");y=t.spriteSource2x.image,d=t.spriteSource2x.json}return"width"in y&&"height"in y&&"data"in y&&(ve(y.data)||xe(y.data))?{width:y.width,height:y.height,data:new Uint8Array(y.data),json:d}:{...N(y),json:d}}const s=_(this.baseURL),i=s.query?"?"+oe(s.query):"",o=this._isRetina?"@2x":"",l=`${s.path}${o}.${this._spriteImageFormat}${i}`,n=`${s.path}${o}.json${i}`,[u,a]=await Promise.all([A(n,r),A(l,{responseType:"image",...r})]);return{...N(a.data),json:u.data}}};function N(e){const t=document.createElement("canvas"),r=t.getContext("2d");t.width=e.width,t.height=e.height,r.drawImage(e,0,0,e.width,e.height);const s=r.getImageData(0,0,e.width,e.height);return{width:e.width,height:e.height,data:new Uint8Array(s.data)}}let Pe=class{constructor(t){this.url=t}async fetchTileIndex(){return this._tileIndexPromise||(this._tileIndexPromise=A(this.url).then(t=>t.data.index)),this._tileIndexPromise}async dataKey(t,r){const s=await this.fetchTileIndex();return C(r),this._getIndexedDataKey(s,t)}_getIndexedDataKey(t,r){const s=[r];if(r.level<0||r.row<0||r.col<0||r.row>>r.level>0||r.col>>r.level>0)return null;let i=r;for(;i.level!==0;)i=new K(i.level-1,i.row>>1,i.col>>1,i.world),s.push(i);let o,l,n=t,u=s.pop();if(n===1)return u;for(;s.length;)if(o=s.pop(),l=(1&o.col)+((1&o.row)<<1),n){if(n[l]===0){u=null;break}if(n[l]===1){u=o;break}u=o,n=n[l]}return u}},Oe=class{constructor(t,r){this._tilemap=t,this._tileIndexUrl=r}async fetchTileIndex(t){return this._tileIndexPromise||(this._tileIndexPromise=A(this._tileIndexUrl,{query:{...t==null?void 0:t.query}}).then(r=>r.data.index)),this._tileIndexPromise}dataKey(t,r){const{level:s,row:i,col:o}=t,l=new K(t);return this._tilemap.fetchAvailabilityUpsample(s,i,o,l,r).then(()=>(l.world=t.world,l)).catch(n=>{if(X(n))throw n;return null})}};class Le{constructor(t){this._tileUrl=t,this._promise=null,this._abortController=null,this._abortOptions=[]}getData(t){this._promise===null&&(this._abortController=new AbortController,this._promise=this._makeRequest(this._tileUrl,this._abortController.signal));const r=this._abortOptions;return r.push(t),H(t,()=>{r.every(s=>Z(s))&&this._abortController.abort()}),this._promise.then(s=>x(s))}async _makeRequest(t,r){const{data:s}=await A(t,{responseType:"array-buffer",signal:r});return s}}const $=new Map;function De(e,t,r,s,i){return Me(e.replace(/\{z\}/gi,t.toString()).replace(/\{y\}/gi,r.toString()).replace(/\{x\}/gi,s.toString()),i)}function Me(e,t){return Y($,e,()=>new Le(e)).getData(t).then(r=>($.delete(e),r)).catch(r=>{throw $.delete(e),r})}let Ee=class{constructor(t,r,s){this.tilemap=null,this.tileInfo=null,this.capabilities=null,this.fullExtent=null,this.name=t,this.sourceUrl=r;const i=_(this.sourceUrl),o=x(s),l=o.tiles;if(i)for(let m=0;m<l.length;m++){const f=_(l[m]);f&&(q(f.path)||(f.path=P(i.path,f.path)),l[m]=v(f.path,{...i.query,...f.query}))}this.tileServers=l;const n=s.capabilities&&s.capabilities.split(",").map(m=>m.toLowerCase().trim()),u=(s==null?void 0:s.exportTilesAllowed)===!0,a=(n==null?void 0:n.includes("tilemap"))===!0,y=u&&s.hasOwnProperty("maxExportTilesCount")?s.maxExportTilesCount:0;this.capabilities={operations:{supportsExportTiles:u,supportsTileMap:a},exportTiles:u?{maxExportTilesCount:+y}:null},this.tileInfo=be(o.tileInfo,o,null,{ignoreMinMaxLOD:!0});const d=s.tileMap?v(P(i.path,s.tileMap),i.query??{}):null;a?(this.type="vector-tile",this.tilemap=new Oe(new Ie({layer:{parsedUrl:i,tileInfo:this.tileInfo,type:"vector-tile",tileServers:this.tileServers}}),d)):d&&(this.tilemap=new Pe(d)),this.fullExtent=W.fromJSON(s.fullExtent)}destroy(){}async getRefKey(t,r){var s;return await((s=this.tilemap)==null?void 0:s.dataKey(t,r))??t}requestTile(t,r,s,i){const o=this.tileServers[r%this.tileServers.length];return De(o,t,r,s,i)}isCompatibleWith(t){const r=this.tileInfo,s=t.tileInfo;if(!r.spatialReference.equals(s.spatialReference)||!r.origin.equals(s.origin)||Math.round(r.dpi)!==Math.round(s.dpi))return!1;const i=r.lods,o=s.lods,l=Math.min(i.length,o.length);for(let n=0;n<l;n++){const u=i[n],a=o[n];if(u.level!==a.level||Math.round(u.scale)!==Math.round(a.scale))return!1}return!0}};const T=E.defaults&&E.defaults.io.corsEnabledServers;async function je(e,t){const r={source:null,sourceBase:null,sourceUrl:null,validatedSource:null,style:null,styleBase:null,styleUrl:null,sourceNameToSource:{},primarySourceName:"",spriteFormat:"png"},[s,i]=typeof e=="string"?[e,null]:[null,e.jsonUrl];await g(r,"esri",e,i,t);const o={layerDefinition:r.validatedSource,url:s,serviceUrl:r.sourceUrl,style:r.style,styleUrl:r.styleUrl,spriteUrl:r.style.sprite&&w(r.styleBase,r.style.sprite),spriteFormat:r.spriteFormat,glyphsUrl:r.style.glyphs&&w(r.styleBase,r.style.glyphs),sourceNameToSource:r.sourceNameToSource,primarySourceName:r.primarySourceName};return S(o.spriteUrl),S(o.glyphsUrl),o}function S(e){if(!e)return;const t=le(e);T&&!T.includes(t)&&T.push(t)}function w(...e){let t;for(const r of e)r!=null&&(Q(r)?t&&(t=t.split("://")[0]+":"+r.trim()):t=q(r)?r:P(t,r));return t?V(t):void 0}async function g(e,t,r,s,i){let o,l,n;if(C(i),typeof r=="string"){const a=F(r);S(a),n=await A(a,{...i,responseType:"json",query:{f:"json",...i==null?void 0:i.query}}),n.ssl&&(o&&(o=o.replace(/^http:/i,"https:")),l&&(l=l.replace(/^http:/i,"https:"))),o=a,l=a}else r!=null&&(n={data:r},o=r.jsonUrl||null,l=s);const u=n==null?void 0:n.data;if(G(u))return e.styleUrl=o||null,Ne(e,u,l,i);if(Be(u))return e.sourceUrl?k(e,u,l,!1,t,i):(e.sourceUrl=o||null,k(e,u,l,!0,t,i));throw new Error("You must specify the URL or the JSON for a service or for a style.")}function G(e){return!!(e!=null&&e.sources)}function Be(e){return!G(e)}async function Ne(e,t,r,s){const i=r?ae(r):z();e.styleBase=i,e.style=t,e.styleUrl&&S(e.styleUrl),t["sprite-format"]&&t["sprite-format"].toLowerCase()==="webp"&&(e.spriteFormat="webp");const o=[];if(t.sources&&t.sources.esri){const l=t.sources.esri;l.url?await g(e,"esri",w(i,l.url),void 0,s):o.push(g(e,"esri",l,i,s))}for(const l of Object.keys(t.sources))l!=="esri"&&t.sources[l].type==="vector"&&(t.sources[l].url?o.push(g(e,l,w(i,t.sources[l].url),void 0,s)):t.sources[l].tiles&&o.push(g(e,l,t.sources[l],i,s)));await Promise.all(o)}async function k(e,t,r,s,i,o){const l=r?V(r)+"/":z(),n=ke(t,l),u=new Ee(i,v(l,(o==null?void 0:o.query)??{}),n);if(!s&&e.primarySourceName in e.sourceNameToSource){const a=e.sourceNameToSource[e.primarySourceName];if(!a.isCompatibleWith(u))return;u.fullExtent!=null&&(a.fullExtent!=null?a.fullExtent.union(u.fullExtent):a.fullExtent=u.fullExtent.clone()),a.tileInfo&&u.tileInfo&&a.tileInfo.lods.length<u.tileInfo.lods.length&&(a.tileInfo=u.tileInfo)}if(s?(e.sourceBase=l,e.source=t,e.validatedSource=n,e.primarySourceName=i,e.sourceUrl&&S(e.sourceUrl)):S(l),e.sourceNameToSource[i]=u,!e.style){if(t.defaultStyles==null)throw new Error;return typeof t.defaultStyles=="string"?g(e,"",w(l,t.defaultStyles,"root.json"),void 0,o):g(e,"",t.defaultStyles,w(l,"root.json"),o)}}function ke(e,t){if(e.hasOwnProperty("tileInfo"))return e;const r={xmin:-20037507067161843e-9,ymin:-20037507067161843e-9,xmax:20037507067161843e-9,ymax:20037507067161843e-9,spatialReference:{wkid:102100}},s=512;let i=78271.51696400007,o=2958287637957775e-7;const l=[],n=e.hasOwnProperty("minzoom")?+e.minzoom:0,u=e.hasOwnProperty("maxzoom")?+e.maxzoom:22;for(let a=0;a<=u;a++)a>=n&&l.push({level:a,scale:o,resolution:i}),i/=2,o/=2;for(const a of e.tiles??[])S(w(t,a));return{capabilities:"TilesOnly",initialExtent:r,fullExtent:r,minScale:0,maxScale:0,tiles:e.tiles,tileInfo:{rows:s,cols:s,dpi:96,format:"pbf",origin:{x:-20037508342787e-6,y:20037508342787e-6},lods:l,spatialReference:{wkid:102100}}}}const b=1e-6;function Ce(e,t){if(e===t)return!0;if(e==null&&t!=null||e!=null&&t==null||e==null||t==null||!e.spatialReference.equals(t.spatialReference)||e.dpi!==t.dpi)return!1;const r=e.origin,s=t.origin;if(Math.abs(r.x-s.x)>=b||Math.abs(r.y-s.y)>=b)return!1;let i,o;e.lods[0].scale>t.lods[0].scale?(i=e,o=t):(o=e,i=t);for(let l=i.lods[0].scale;l>=o.lods[o.lods.length-1].scale-b;l/=2)if(Math.abs(l-o.lods[0].scale)<b)return!0;return!1}function qe(e,t){if(e===t)return e;if(e==null&&t!=null)return t;if(e!=null&&t==null)return e;if(e==null||t==null)return null;const r=e.size[0],s=e.format,i=e.dpi,o=new ee({x:e.origin.x,y:e.origin.y}),l=e.spatialReference,n=e.lods[0].scale>t.lods[0].scale?e.lods[0]:t.lods[0],u=e.lods[e.lods.length-1].scale<=t.lods[t.lods.length-1].scale?e.lods[e.lods.length-1]:t.lods[t.lods.length-1],a=n.scale,y=n.resolution,d=u.scale,m=[];let f=a,L=y,D=0;for(;f>d;)m.push(new ue({level:D,resolution:L,scale:f})),D++,f/=2,L/=2;return new O({size:[r,r],dpi:i,format:s||"pbf",origin:o,lods:m,spatialReference:l})}let p=class extends pe(ce(Ue(he(ye(de(fe(me(ge(we))))))))){constructor(...e){super(...e),this._spriteSourceMap=new Map,this.currentStyleInfo=null,this.style=null,this.isReference=null,this.operationalLayerType="VectorTileLayer",this.type="vector-tile",this.url=null,this.showCollisionBoxes="none",this.path=null}normalizeCtorArgs(e,t){return typeof e=="string"?{url:e,...t}:e}destroy(){if(this.sourceNameToSource)for(const e of Object.values(this.sourceNameToSource))e==null||e.destroy();this._spriteSourceMap.clear()}async prefetchResources(e){await this.loadSpriteSource(globalThis.devicePixelRatio||1,e)}load(e){const t=this.loadFromPortal({supportedTypes:["Vector Tile Service"],supportsData:!1},e).catch(R).then(async()=>{if(!this.portalItem||!this.portalItem.id)return;const r=`${this.portalItem.itemUrl}/resources/styles/root.json`;(await A(r,{...e,query:{f:"json",...this.customParameters,token:this.apiKey}})).data&&this.read({url:r},Re(this.portalItem))}).catch(R).then(()=>this._loadStyle(e));return this.addResolvingPromise(t),Promise.resolve(this)}get attributionDataUrl(){const e=this.currentStyleInfo,t=e&&e.serviceUrl&&_(e.serviceUrl);if(!t)return null;const r=this._getDefaultAttribution(t.path);return r?v(r,{...this.customParameters,token:this.apiKey}):null}get capabilities(){const e=this.primarySource;return e?e.capabilities:{operations:{supportsExportTiles:!1,supportsTileMap:!1},exportTiles:null}}get fullExtent(){var e;return((e=this.primarySource)==null?void 0:e.fullExtent)||null}get parsedUrl(){return this.serviceUrl?_(this.serviceUrl):null}get serviceUrl(){return this.currentStyleInfo&&this.currentStyleInfo.serviceUrl||null}get spatialReference(){var e;return((e=this.tileInfo)==null?void 0:e.spatialReference)??null}get styleUrl(){return this.currentStyleInfo&&this.currentStyleInfo.styleUrl||null}writeStyleUrl(e,t){e&&Q(e)&&(e=`https:${e}`);const r=_e(ne(e));t.styleUrl=Ae(e,r)}get tileInfo(){var r;const e=[];for(const s in this.sourceNameToSource)e.push(this.sourceNameToSource[s]);let t=((r=this.primarySource)==null?void 0:r.tileInfo)||new O;if(e.length>1)for(let s=0;s<e.length;s++)Ce(t,e[s].tileInfo)&&(t=qe(t,e[s].tileInfo));return t}readVersion(e,t){return t.version?parseFloat(t.version):parseFloat(t.currentVersion)}async loadSpriteSource(e=1,t){var r,s;if(!this._spriteSourceMap.has(e)){const i=M("2d").maxTextureSize,o=(r=this.currentStyleInfo)!=null&&r.spriteUrl?v(this.currentStyleInfo.spriteUrl,{...this.customParameters,token:this.apiKey}):null,l=new B({type:"url",spriteUrl:o,pixelRatio:e,spriteFormat:(s=this.currentStyleInfo)==null?void 0:s.spriteFormat},i);await l.load(t),this._spriteSourceMap.set(e,l)}return this._spriteSourceMap.get(e)}async setSpriteSource(e,t){if(!e)return null;const r=M("2d").maxTextureSize,s=e.spriteUrl,i=s?v(s,{...this.customParameters,token:this.apiKey}):null;if(!i&&e.type==="url")return null;const o=new B(e,r);try{await o.load(t);const l=e.pixelRatio||1;return this._spriteSourceMap.clear(),this._spriteSourceMap.set(l,o),i&&this.currentStyleInfo&&(this.currentStyleInfo.spriteUrl=i),this.emit("spriteSource-change",{spriteSource:o}),o}catch(l){R(l)}return null}async loadStyle(e,t){var s;const r=e||this.style||this.url;return this._loadingTask&&typeof r=="string"&&this.url===r||((s=this._loadingTask)==null||s.abort(),this._loadingTask=Se(i=>(this._spriteSourceMap.clear(),this._getSourceAndStyle(r,{signal:i})),t)),this._loadingTask.promise}getStyleLayerId(e){return this.styleRepository.getStyleLayerId(e)}getStyleLayerIndex(e){return this.styleRepository.getStyleLayerIndex(e)}getPaintProperties(e){return x(this.styleRepository.getPaintProperties(e))}setPaintProperties(e,t){const r=this.styleRepository.isPainterDataDriven(e);this.styleRepository.setPaintProperties(e,t);const s=this.styleRepository.isPainterDataDriven(e);this.emit("paint-change",{layer:e,paint:t,isDataDriven:r||s})}getStyleLayer(e){return x(this.styleRepository.getStyleLayer(e))}setStyleLayer(e,t){this.styleRepository.setStyleLayer(e,t),this.emit("style-layer-change",{layer:e,index:t})}deleteStyleLayer(e){this.styleRepository.deleteStyleLayer(e),this.emit("delete-style-layer",{layer:e})}getLayoutProperties(e){return x(this.styleRepository.getLayoutProperties(e))}setLayoutProperties(e,t){this.styleRepository.setLayoutProperties(e,t),this.emit("layout-change",{layer:e,layout:t})}setStyleLayerVisibility(e,t){this.styleRepository.setStyleLayerVisibility(e,t),this.emit("style-layer-visibility-change",{layer:e,visibility:t})}getStyleLayerVisibility(e){return this.styleRepository.getStyleLayerVisibility(e)}write(e,t){return t!=null&&t.origin&&!this.styleUrl?(t.messages&&t.messages.push(new I("vectortilelayer:unsupported",`VectorTileLayer (${this.title}, ${this.id}) with style defined by JSON only are not supported`,{layer:this})),null):super.write(e,t)}getTileUrl(e,t,r){return null}async _getSourceAndStyle(e,t){if(!e)throw new Error("invalid style!");const r=await je(e,{...t,query:{...this.customParameters,token:this.apiKey}});r.spriteFormat==="webp"&&(await Te("lossy")||(r.spriteFormat="png")),this._set("currentStyleInfo",{...r}),typeof e=="string"?(this.url=e,this.style=null):(this.url=null,this.style=e),this._set("sourceNameToSource",r.sourceNameToSource),this._set("primarySource",r.sourceNameToSource[r.primarySourceName]),this._set("styleRepository",new $e(r.style)),this.read(r.layerDefinition,{origin:"service"}),this.emit("load-style")}_getDefaultAttribution(e){const t=e.match(/^https?:\/\/(?:basemaps|basemapsbeta|basemapsdev)(?:-api)?\.arcgis\.com(\/[^\/]+)?\/arcgis\/rest\/services\/([^\/]+(\/[^\/]+)*)\/vectortileserver/i),r=["OpenStreetMap_v2","OpenStreetMap_Daylight_v2","OpenStreetMap_Export_v2","OpenStreetMap_FTS_v2","OpenStreetMap_GCS_v2","World_Basemap","World_Basemap_v2","World_Basemap_Export_v2","World_Basemap_GCS_v2","World_Basemap_WGS84","World_Contours_v2"];if(!t)return;const s=t[2]&&t[2].toLowerCase();if(!s)return;const i=t[1]||"";for(const o of r)if(o.toLowerCase().includes(s))return F(`//static.arcgis.com/attribution/Vector${i}/${o}`)}async _loadStyle(e){var t;return((t=this._loadingTask)==null?void 0:t.promise)??this.loadStyle(null,e)}};c([h({readOnly:!0})],p.prototype,"attributionDataUrl",null),c([h({type:["show","hide"]})],p.prototype,"listMode",void 0),c([h({json:{read:!0,write:!0}})],p.prototype,"blendMode",void 0),c([h({readOnly:!0,json:{read:!1}})],p.prototype,"capabilities",null),c([h({readOnly:!0})],p.prototype,"currentStyleInfo",void 0),c([h({json:{read:!1},readOnly:!0,type:W})],p.prototype,"fullExtent",null),c([h()],p.prototype,"style",void 0),c([h({type:Boolean,json:{read:!1,write:{enabled:!0,overridePolicy:()=>({enabled:!1})}}})],p.prototype,"isReference",void 0),c([h({type:["VectorTileLayer"]})],p.prototype,"operationalLayerType",void 0),c([h({readOnly:!0})],p.prototype,"parsedUrl",null),c([h({readOnly:!0})],p.prototype,"serviceUrl",null),c([h({type:te,readOnly:!0})],p.prototype,"spatialReference",null),c([h({readOnly:!0})],p.prototype,"styleRepository",void 0),c([h({readOnly:!0})],p.prototype,"sourceNameToSource",void 0),c([h({readOnly:!0})],p.prototype,"primarySource",void 0),c([h({type:String,readOnly:!0,json:{write:{ignoreOrigin:!0},origins:{"web-document":{write:{ignoreOrigin:!0,isRequired:!0}}}}})],p.prototype,"styleUrl",null),c([re(["portal-item","web-document"],"styleUrl")],p.prototype,"writeStyleUrl",null),c([h({json:{read:!1,origins:{service:{read:!1}}},readOnly:!0,type:O})],p.prototype,"tileInfo",null),c([h({json:{read:!1},readOnly:!0,value:"vector-tile"})],p.prototype,"type",void 0),c([h({json:{origins:{"web-document":{read:{source:"styleUrl"}},"portal-item":{read:{source:"url"}}},write:!1,read:!1}})],p.prototype,"url",void 0),c([h({readOnly:!0})],p.prototype,"version",void 0),c([se("version",["version","currentVersion"])],p.prototype,"readVersion",null),c([h({type:String})],p.prototype,"showCollisionBoxes",void 0),c([h({type:String,json:{origins:{"web-scene":{read:!0,write:!0}},read:!1}})],p.prototype,"path",void 0),p=c([ie("esri.layers.VectorTileLayer")],p);const nt=p;export{nt as default};
