import{_ as C}from"./index-C9hz-UZb.js";import{d as O,c as d,r as f,s as k,l as n,o as M,Q as w,ay as D,g as z,n as x,q as s,F as v,p as c,aq as B,al as S,C as q}from"./index-r0dFAfgr.js";import{_ as L}from"./Search-NSrhrIa_.js";import{B as V,P}from"./echart-BoVIcYbV.js";import{G as T}from"./index-CpGhZCTT.js";import"./config-DqqM5K5L.js";import"./DateFormatter-Bm9a68Ax.js";import"./OutsideWorkOrder-C6s8joBt.js";const E={class:"wrapper"},H={class:"top"},R={class:"top-left"},A={class:"top-right"},F={class:"bottom"},G=O({__name:"OrderReportAnalys",setup(I){const _=d(),m=d(),h=d(),g=f({filters:[{type:"radio-button",label:"时间范围",options:[{label:"日",value:"date"},{label:"月",value:"month"},{label:"年",value:"year"}],field:"type",onChange:()=>l()},{clearable:!1,handleHidden:(e,t,a)=>{a.hidden=e.type!=="date"},type:"date",label:"",field:"date"},{clearable:!1,handleHidden:(e,t,a)=>{a.hidden=e.type!=="month"},type:"month",label:"",field:"month"},{clearable:!1,handleHidden:(e,t,a)=>{a.hidden=e.type!=="year"},type:"year",label:"",field:"year"},{type:"btn-group",btns:[{perm:!0,text:"查询",svgIcon:k(S),click:()=>l()}]}],defaultParams:{type:"date",month:n().format("YYYY-MM"),year:n().format("YYYY"),date:n().format("YYYY-MM-DD")}}),r=f({indexVisible:!0,columns:[{label:"发起人员",prop:"key"},{label:"发起事件数",prop:"value"}],dataList:[],pagination:{refreshData:({page:e,size:t})=>{r.pagination.page=e,r.pagination.limit=t,l()}}}),i=f({pieOption:null,barOption:null}),l=async()=>{var p,b,Y;const e=((p=_.value)==null?void 0:p.queryParams)||{},t=(e==null?void 0:e.type)||"date";let a;switch(t){case"month":a=n(e[t],"YYYY-MM");break;case"year":a=n(e[t],"YYYY");break;default:a=n(e[t],"YYYY-MM-DD");break}const o=((Y=(b=(await T({fromTime:a.startOf(t==="year"?"y":t==="month"?"M":"D").valueOf(),toTime:a.endOf(t==="year"?"y":t==="month"?"M":"D").valueOf(),statisticOrganizer:!0})).data)==null?void 0:b.data)==null?void 0:Y.organizers)||{};i.barOption=V(o.data||[]),i.pieOption=P(o.data||[],"人员上报分析"),r.dataList=o.data||[],r.pagination.total=o.total||0},u=()=>{var e,t;(e=m.value)==null||e.resize(),(t=h.value)==null||t.resize()};return M(()=>{l(),window.addEventListener("resize",u)}),w(()=>{window.removeEventListener("resize",u)}),(e,t)=>{const a=L,y=B,o=D("VChart"),p=C;return z(),x("div",E,[s(p,{class:"card",title:" ",overlay:""},{title:v(()=>[s(a,{ref_key:"refSearch",ref:_,config:g},null,8,["config"])]),default:v(()=>[c("div",H,[c("div",R,[s(y,{config:r},null,8,["config"])]),c("div",A,[s(o,{ref_key:"refChart1",ref:h,option:i.pieOption},null,8,["option"])])]),c("div",F,[s(o,{ref_key:"refChart",ref:m,option:i.barOption},null,8,["option"])])]),_:1})])}}}),Z=q(G,[["__scopeId","data-v-2741dbda"]]);export{Z as default};
