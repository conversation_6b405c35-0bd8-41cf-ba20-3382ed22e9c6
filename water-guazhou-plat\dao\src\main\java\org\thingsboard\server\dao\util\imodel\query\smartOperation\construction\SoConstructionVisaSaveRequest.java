package org.thingsboard.server.dao.util.imodel.query.smartOperation.construction;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionVisa;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

import java.util.Date;

@Getter
@Setter
public class SoConstructionVisaSaveRequest extends SaveRequest<SoConstructionVisa> {
    // 编号
    @NotNullOrEmpty
    private String code;

    // 所属工程编号
    private String constructionCode;

    // 预算人
    private String budgeter;

    // 施工单位
    @NotNullOrEmpty
    private String constructOrganization;

    // 施工地点
    @NotNullOrEmpty
    private String address;

    // 施工时间
    private Date constructTime;

    // 建设单位
    @NotNullOrEmpty
    private String buildOrganization;

    // 监理单位
    @NotNullOrEmpty
    private String supervisorOrganization;

    // 审计单位
    @NotNullOrEmpty
    private String auditOrganization;

    // 原因
    private String remark;

    // 附件信息
    private String attachments;

    @Override
    protected SoConstructionVisa build() {
        SoConstructionVisa entity = new SoConstructionVisa();
        entity.setConstructionCode(constructionCode);
        entity.setCreateTime(createTime());
        entity.setCreator(currentUserUUID());
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    @Override
    protected SoConstructionVisa update(String id) {
        SoConstructionVisa entity = new SoConstructionVisa();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(SoConstructionVisa entity) {
        entity.setCode(code);
        entity.setBudgeter(budgeter);
        entity.setConstructOrganization(constructOrganization);
        entity.setAddress(address);
        entity.setConstructTime(constructTime);
        entity.setBuildOrganization(buildOrganization);
        entity.setSupervisorOrganization(supervisorOrganization);
        entity.setAuditOrganization(auditOrganization);
        entity.setRemark(remark);
        entity.setAttachments(attachments);
    }
}