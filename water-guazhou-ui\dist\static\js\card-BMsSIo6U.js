import{_ as h}from"./index-C9hz-UZb.js";import{d as j,r as f,a8 as g,l as x,c as k,f8 as V,o as J,am as _,D as Y,dW as Z,M as K,C as M,ay as Q,g as B,n as w,q as a,F as n,p as t,bh as i,aB as E,aJ as R,h as T,G as b,ct as U,bU as L,bW as q,cK as H,cL as I,cM as P,bV as N}from"./index-r0dFAfgr.js";import{u as O}from"./index-t9hHXNDZ.js";import z from"./add-CFWDlVLF.js";import{a as W}from"./URLHelper-B9aplt5w.js";import"./index-BI1vGJja.js";import"./index-CaaU9niG.js";const{$messageInfo:X,$confirm:S}=K(),$=j({name:"企业详情",components:{configsialog:z,SLCard:h},props:{config:{type:Object,default(){return{}}}},setup(e){const s=f({enterprisevalue:g(()=>e.config.value),additionalInfo:g(()=>{const o=e.config.value.additionalInfo;return o?typeof o=="object"?o:JSON.parse(o):[{platformName:"",companyProfiles:""}]})}),F=g(()=>x(e.config.value.createdTime).format("YYYY-MM-DD HH:mm:ss")),l=k({visible:!1,title:"添加应用",type:!0,close:()=>{l.value.visible=!1,c()},value:g(()=>e.config.value),appform:{}}),C=f({addApp:()=>{l.value.title="添加应用",l.value.type=!0,l.value.appform={id:null,name:"",type:"1",applicationUrl:"",orderNum:0,remark:"",img:"",createTime:null,menuIdList:null},l.value.visible=!0},editApp:o=>{l.value.title="修改应用",l.value.appform=o,l.value.type=!1,l.value.visible=!0},deleteApp:o=>{S("确认要删除该应用吗？","删除提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{V([o.id]).then(()=>{X("删除成功"),c()})}).catch(()=>{})}}),d=f({dataForm:{location:[116,39]}}),m=f({items:[],time:o=>x(o).format("YYYY-MM-DD HH:mm:ss")});J(async()=>{u()});const u=async()=>{const o={events:{mapmove:p=>{const v=p.getCenter();d.dataForm.location=v}},center:d.dataForm.location},{setMarker:r}=await O("innerAmap",o);r(d.dataForm.location,{icon:W()}),_(()=>d.dataForm.location,()=>{})},c=()=>{var r;const o=Y((r=e.config.value)==null?void 0:r.id.id);Z(o).then(p=>{m.items=p.data})};return _(s,()=>{c(),d.dataForm.location=[e.config.value.latd,e.config.value.lgtd],u()}),{configs:l,appoperate:C,enterprise:s,time:F,state:d,apps:m}}}),tt="data:image/png;base64,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",st="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABEAAAAFCAYAAACjKgd3AAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAEaADAAQAAAABAAAABQAAAAAb86JDAAAAdUlEQVQYGaWQyw1AQBRFxUIXFjpQjA40J2FPGyxUg3GOGHl7Nzl5930zmaJAKaUaGn0UtQpaY6zrnYf6qWM6OOCEPg/jPbCCMn6H8D04717nxQGylnDEF0S1oTeHxlDSmOCEC0bI2jHbmxjNs9xx3j39/z+5AX/qlRVZj8VgAAAAAElFTkSuQmCC",et={class:"introduce"},ot={class:"tenantInfoBox"},at={class:"tenantName infoText"},nt={class:"textBox"},lt={class:"infoText"},At={class:"textBox"},it={class:"infoText"},ut={class:"textBox"},dt={class:"infoText"},rt={class:"textBox"},ct={class:"infoText bottom"},pt={class:"apps"},mt={class:"manage"},ft={class:"cs"},gt={class:"cards"},vt={class:"card"},Bt={class:"title"},Ft={class:"title-text"},Ct={class:"title-buttom"},xt={class:"introduce"},_t={class:"application-icon"},wt={class:"icon"},Et=["src"],bt={class:"type"},ht={class:"update-time"};function Gt(e,s,F,l,C,d){const m=U,u=L,c=q,o=H,r=I,p=P,v=N,G=h,y=Q("configsialog");return B(),w(E,null,[a(G,{title:"企业详情",class:"card-box"},{default:n(()=>[t("div",et,[a(c,null,{default:n(()=>[a(u,{xs:24,sm:24,md:11,lg:11,xl:11},{default:n(()=>[t("div",ot,[t("h1",at,i(e.enterprise.enterprisevalue.name),1),t("div",nt,[s[1]||(s[1]=t("label",null,"平台名称：",-1)),a(m,null,{default:n(()=>[t("span",lt,i(e.enterprise.additionalInfo.platformName||""),1)]),_:1})]),t("div",At,[s[2]||(s[2]=t("label",null,"企业地址：",-1)),t("span",it,i(e.enterprise.enterprisevalue.address),1)]),t("div",ut,[s[3]||(s[3]=t("label",null,"创建时间：",-1)),t("span",dt,i(e.time),1)]),t("div",rt,[s[4]||(s[4]=t("label",null,"企业简介：",-1)),t("div",ct,i(e.enterprise.additionalInfo.companyProfiles||""),1)])])]),_:1}),a(u,{xs:24,sm:24,md:2,lg:2,xl:2}),a(u,{xs:24,sm:24,md:11,lg:11,xl:11},{default:n(()=>s[5]||(s[5]=[t("div",{class:"messageContainer"},[t("div",{class:"amap-container"},[t("div",{id:"innerAmap",style:{height:"100%",width:"100%"}})])],-1)])),_:1})]),_:1})]),t("div",pt,[s[11]||(s[11]=t("div",{class:"manage-title"},[t("span",null,"应用管理")],-1)),t("div",mt,[a(v,null,{default:n(()=>[t("div",ft,[t("div",gt,[a(c,{gutter:30},{default:n(()=>[a(u,{xs:24,sm:12,md:8,lg:8,xl:6},{default:n(()=>[t("div",{class:"cardinput",onClick:s[0]||(s[0]=(...A)=>e.appoperate.addApp&&e.appoperate.addApp(...A))},s[6]||(s[6]=[t("div",{class:"icon"},[t("img",{src:tt})],-1),t("div",{class:"text"}," 添加应用 ",-1)]))]),_:1}),(B(!0),w(E,null,R(e.apps.items,A=>(B(),T(u,{key:A.id,xs:24,sm:12,md:8,lg:8,xl:6},{default:n(()=>[t("div",vt,[t("div",Bt,[t("span",Ft,i(A.name),1),t("div",Ct,[a(p,{trigger:"click"},{dropdown:n(()=>[a(r,null,{default:n(()=>[a(o,{onClick:D=>e.appoperate.editApp(A)},{default:n(()=>s[7]||(s[7]=[b(" 编辑 ")])),_:2},1032,["onClick"]),a(o,{onClick:D=>e.appoperate.deleteApp(A)},{default:n(()=>s[8]||(s[8]=[b(" 删除 ")])),_:2},1032,["onClick"])]),_:2},1024)]),default:n(()=>[s[9]||(s[9]=t("span",{class:"el-dropdown-link css"},[t("img",{src:st})],-1))]),_:2},1024)])]),t("div",xt,[t("span",null,i(A.remark),1)]),t("div",_t,[s[10]||(s[10]=t("span",null,"应用图标：",-1)),t("div",wt,[t("img",{src:A.img},null,8,Et)])]),t("div",bt,[t("span",null,"应用类型："+i(A.type==="1"?"基本类型":"其他类型"),1)]),t("div",ht,[t("span",null,"更新时间："+i(e.apps.time(A.createTime)),1)])])]),_:2},1024))),128))]),_:1})])])]),_:1})])])]),_:1}),a(y,{config:e.configs},null,8,["config"])],64)}const Zt=M($,[["render",Gt],["__scopeId","data-v-c4befac0"]]);export{Zt as default};
