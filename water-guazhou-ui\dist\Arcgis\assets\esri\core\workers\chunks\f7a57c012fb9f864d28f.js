"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[4547],{52138:(t,e,n)=>{n.d(e,{A:()=>M,a:()=>u,d:()=>f,e:()=>P,f:()=>E,g:()=>C,h:()=>A,i:()=>i,j:()=>R,k:()=>c,m:()=>_,s:()=>s,t:()=>a,w:()=>l,y:()=>N,z:()=>O});var r=n(65617),o=n(46851);function s(t,e,n,r,o,s,i,a,u,_,l,c,P,E,f,M,S){return t[0]=e,t[1]=n,t[2]=r,t[3]=o,t[4]=s,t[5]=i,t[6]=a,t[7]=u,t[8]=_,t[9]=l,t[10]=c,t[11]=P,t[12]=E,t[13]=f,t[14]=M,t[15]=S,t}function i(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=1,t[6]=0,t[7]=0,t[8]=0,t[9]=0,t[10]=1,t[11]=0,t[12]=0,t[13]=0,t[14]=0,t[15]=1,t}function a(t,e){if(t===e){const n=e[1],r=e[2],o=e[3],s=e[6],i=e[7],a=e[11];t[1]=e[4],t[2]=e[8],t[3]=e[12],t[4]=n,t[6]=e[9],t[7]=e[13],t[8]=r,t[9]=s,t[11]=e[14],t[12]=o,t[13]=i,t[14]=a}else t[0]=e[0],t[1]=e[4],t[2]=e[8],t[3]=e[12],t[4]=e[1],t[5]=e[5],t[6]=e[9],t[7]=e[13],t[8]=e[2],t[9]=e[6],t[10]=e[10],t[11]=e[14],t[12]=e[3],t[13]=e[7],t[14]=e[11],t[15]=e[15];return t}function u(t,e){const n=e[0],r=e[1],o=e[2],s=e[3],i=e[4],a=e[5],u=e[6],_=e[7],l=e[8],c=e[9],P=e[10],E=e[11],f=e[12],M=e[13],S=e[14],p=e[15],C=n*a-r*i,h=n*u-o*i,R=n*_-s*i,A=r*u-o*a,O=r*_-s*a,N=o*_-s*u,T=l*M-c*f,g=l*S-P*f,d=l*p-E*f,L=c*S-P*M,G=c*p-E*M,y=P*p-E*S;let m=C*y-h*G+R*L+A*d-O*g+N*T;return m?(m=1/m,t[0]=(a*y-u*G+_*L)*m,t[1]=(o*G-r*y-s*L)*m,t[2]=(M*N-S*O+p*A)*m,t[3]=(P*O-c*N-E*A)*m,t[4]=(u*d-i*y-_*g)*m,t[5]=(n*y-o*d+s*g)*m,t[6]=(S*R-f*N-p*h)*m,t[7]=(l*N-P*R+E*h)*m,t[8]=(i*G-a*d+_*T)*m,t[9]=(r*d-n*G-s*T)*m,t[10]=(f*O-M*R+p*C)*m,t[11]=(c*R-l*O-E*C)*m,t[12]=(a*g-i*L-u*T)*m,t[13]=(n*L-r*g+o*T)*m,t[14]=(M*h-f*A-S*C)*m,t[15]=(l*A-c*h+P*C)*m,t):null}function _(t,e,n){const r=e[0],o=e[1],s=e[2],i=e[3],a=e[4],u=e[5],_=e[6],l=e[7],c=e[8],P=e[9],E=e[10],f=e[11],M=e[12],S=e[13],p=e[14],C=e[15];let h=n[0],R=n[1],A=n[2],O=n[3];return t[0]=h*r+R*a+A*c+O*M,t[1]=h*o+R*u+A*P+O*S,t[2]=h*s+R*_+A*E+O*p,t[3]=h*i+R*l+A*f+O*C,h=n[4],R=n[5],A=n[6],O=n[7],t[4]=h*r+R*a+A*c+O*M,t[5]=h*o+R*u+A*P+O*S,t[6]=h*s+R*_+A*E+O*p,t[7]=h*i+R*l+A*f+O*C,h=n[8],R=n[9],A=n[10],O=n[11],t[8]=h*r+R*a+A*c+O*M,t[9]=h*o+R*u+A*P+O*S,t[10]=h*s+R*_+A*E+O*p,t[11]=h*i+R*l+A*f+O*C,h=n[12],R=n[13],A=n[14],O=n[15],t[12]=h*r+R*a+A*c+O*M,t[13]=h*o+R*u+A*P+O*S,t[14]=h*s+R*_+A*E+O*p,t[15]=h*i+R*l+A*f+O*C,t}function l(t,e,n){const r=n[0],o=n[1],s=n[2];if(e===t)t[12]=e[0]*r+e[4]*o+e[8]*s+e[12],t[13]=e[1]*r+e[5]*o+e[9]*s+e[13],t[14]=e[2]*r+e[6]*o+e[10]*s+e[14],t[15]=e[3]*r+e[7]*o+e[11]*s+e[15];else{const n=e[0],i=e[1],a=e[2],u=e[3],_=e[4],l=e[5],c=e[6],P=e[7],E=e[8],f=e[9],M=e[10],S=e[11];t[0]=n,t[1]=i,t[2]=a,t[3]=u,t[4]=_,t[5]=l,t[6]=c,t[7]=P,t[8]=E,t[9]=f,t[10]=M,t[11]=S,t[12]=n*r+_*o+E*s+e[12],t[13]=i*r+l*o+f*s+e[13],t[14]=a*r+c*o+M*s+e[14],t[15]=u*r+P*o+S*s+e[15]}return t}function c(t,e,n){const r=n[0],o=n[1],s=n[2];return t[0]=e[0]*r,t[1]=e[1]*r,t[2]=e[2]*r,t[3]=e[3]*r,t[4]=e[4]*o,t[5]=e[5]*o,t[6]=e[6]*o,t[7]=e[7]*o,t[8]=e[8]*s,t[9]=e[9]*s,t[10]=e[10]*s,t[11]=e[11]*s,t[12]=e[12],t[13]=e[13],t[14]=e[14],t[15]=e[15],t}function P(t,e,n,r){let s,i,a,u,_,l,c,P,E,f,M,S,p,C,h,R,A,O,N,T,g,d,L,G,y=r[0],m=r[1],I=r[2],W=Math.sqrt(y*y+m*m+I*I);return W<(0,o.g)()?null:(W=1/W,y*=W,m*=W,I*=W,s=Math.sin(n),i=Math.cos(n),a=1-i,u=e[0],_=e[1],l=e[2],c=e[3],P=e[4],E=e[5],f=e[6],M=e[7],S=e[8],p=e[9],C=e[10],h=e[11],R=y*y*a+i,A=m*y*a+I*s,O=I*y*a-m*s,N=y*m*a-I*s,T=m*m*a+i,g=I*m*a+y*s,d=y*I*a+m*s,L=m*I*a-y*s,G=I*I*a+i,t[0]=u*R+P*A+S*O,t[1]=_*R+E*A+p*O,t[2]=l*R+f*A+C*O,t[3]=c*R+M*A+h*O,t[4]=u*N+P*T+S*g,t[5]=_*N+E*T+p*g,t[6]=l*N+f*T+C*g,t[7]=c*N+M*T+h*g,t[8]=u*d+P*L+S*G,t[9]=_*d+E*L+p*G,t[10]=l*d+f*L+C*G,t[11]=c*d+M*L+h*G,e!==t&&(t[12]=e[12],t[13]=e[13],t[14]=e[14],t[15]=e[15]),t)}function E(t,e){return t[0]=1,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=1,t[6]=0,t[7]=0,t[8]=0,t[9]=0,t[10]=1,t[11]=0,t[12]=e[0],t[13]=e[1],t[14]=e[2],t[15]=1,t}function f(t,e,n){if(0===e)return i(t);let r,s,a,u=n[0],_=n[1],l=n[2],c=Math.sqrt(u*u+_*_+l*l);return c<(0,o.g)()?null:(c=1/c,u*=c,_*=c,l*=c,r=Math.sin(e),s=Math.cos(e),a=1-s,t[0]=u*u*a+s,t[1]=_*u*a+l*r,t[2]=l*u*a-_*r,t[3]=0,t[4]=u*_*a-l*r,t[5]=_*_*a+s,t[6]=l*_*a+u*r,t[7]=0,t[8]=u*l*a+_*r,t[9]=_*l*a-u*r,t[10]=l*l*a+s,t[11]=0,t[12]=0,t[13]=0,t[14]=0,t[15]=1,t)}function M(t,e){const n=Math.sin(e),r=Math.cos(e);return t[0]=1,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=r,t[6]=n,t[7]=0,t[8]=0,t[9]=-n,t[10]=r,t[11]=0,t[12]=0,t[13]=0,t[14]=0,t[15]=1,t}function S(t,e,n){const r=e[0],o=e[1],s=e[2],i=e[3],a=r+r,u=o+o,_=s+s,l=r*a,c=r*u,P=r*_,E=o*u,f=o*_,M=s*_,S=i*a,p=i*u,C=i*_;return t[0]=1-(E+M),t[1]=c+C,t[2]=P-p,t[3]=0,t[4]=c-C,t[5]=1-(l+M),t[6]=f+S,t[7]=0,t[8]=P+p,t[9]=f-S,t[10]=1-(l+E),t[11]=0,t[12]=n[0],t[13]=n[1],t[14]=n[2],t[15]=1,t}const p=(0,r.c)();function C(t,e,n,r){const o=e[0],s=e[1],i=e[2],a=e[3],u=o+o,_=s+s,l=i+i,c=o*u,P=o*_,E=o*l,f=s*_,M=s*l,S=i*l,p=a*u,C=a*_,h=a*l,R=r[0],A=r[1],O=r[2];return t[0]=(1-(f+S))*R,t[1]=(P+h)*R,t[2]=(E-C)*R,t[3]=0,t[4]=(P-h)*A,t[5]=(1-(c+S))*A,t[6]=(M+p)*A,t[7]=0,t[8]=(E+C)*O,t[9]=(M-p)*O,t[10]=(1-(c+f))*O,t[11]=0,t[12]=n[0],t[13]=n[1],t[14]=n[2],t[15]=1,t}function h(t,e,n){return t[0]=e[0]-n[0],t[1]=e[1]-n[1],t[2]=e[2]-n[2],t[3]=e[3]-n[3],t[4]=e[4]-n[4],t[5]=e[5]-n[5],t[6]=e[6]-n[6],t[7]=e[7]-n[7],t[8]=e[8]-n[8],t[9]=e[9]-n[9],t[10]=e[10]-n[10],t[11]=e[11]-n[11],t[12]=e[12]-n[12],t[13]=e[13]-n[13],t[14]=e[14]-n[14],t[15]=e[15]-n[15],t}function R(t,e){return t[0]===e[0]&&t[1]===e[1]&&t[2]===e[2]&&t[3]===e[3]&&t[4]===e[4]&&t[5]===e[5]&&t[6]===e[6]&&t[7]===e[7]&&t[8]===e[8]&&t[9]===e[9]&&t[10]===e[10]&&t[11]===e[11]&&t[12]===e[12]&&t[13]===e[13]&&t[14]===e[14]&&t[15]===e[15]}function A(t,e){if(t===e)return!0;const n=t[0],r=t[1],s=t[2],i=t[3],a=t[4],u=t[5],_=t[6],l=t[7],c=t[8],P=t[9],E=t[10],f=t[11],M=t[12],S=t[13],p=t[14],C=t[15],h=e[0],R=e[1],A=e[2],O=e[3],N=e[4],T=e[5],g=e[6],d=e[7],L=e[8],G=e[9],y=e[10],m=e[11],I=e[12],W=e[13],F=e[14],w=e[15],b=(0,o.g)();return Math.abs(n-h)<=b*Math.max(1,Math.abs(n),Math.abs(h))&&Math.abs(r-R)<=b*Math.max(1,Math.abs(r),Math.abs(R))&&Math.abs(s-A)<=b*Math.max(1,Math.abs(s),Math.abs(A))&&Math.abs(i-O)<=b*Math.max(1,Math.abs(i),Math.abs(O))&&Math.abs(a-N)<=b*Math.max(1,Math.abs(a),Math.abs(N))&&Math.abs(u-T)<=b*Math.max(1,Math.abs(u),Math.abs(T))&&Math.abs(_-g)<=b*Math.max(1,Math.abs(_),Math.abs(g))&&Math.abs(l-d)<=b*Math.max(1,Math.abs(l),Math.abs(d))&&Math.abs(c-L)<=b*Math.max(1,Math.abs(c),Math.abs(L))&&Math.abs(P-G)<=b*Math.max(1,Math.abs(P),Math.abs(G))&&Math.abs(E-y)<=b*Math.max(1,Math.abs(E),Math.abs(y))&&Math.abs(f-m)<=b*Math.max(1,Math.abs(f),Math.abs(m))&&Math.abs(M-I)<=b*Math.max(1,Math.abs(M),Math.abs(I))&&Math.abs(S-W)<=b*Math.max(1,Math.abs(S),Math.abs(W))&&Math.abs(p-F)<=b*Math.max(1,Math.abs(p),Math.abs(F))&&Math.abs(C-w)<=b*Math.max(1,Math.abs(C),Math.abs(w))}function O(t){const e=(0,o.g)(),n=t[0],r=t[1],s=t[2],i=t[4],a=t[5],u=t[6],_=t[8],l=t[9],c=t[10];return Math.abs(1-(n*n+i*i+_*_))<=e&&Math.abs(1-(r*r+a*a+l*l))<=e&&Math.abs(1-(s*s+u*u+c*c))<=e}function N(t){return 1===t[0]&&0===t[1]&&0===t[2]&&0===t[4]&&1===t[5]&&0===t[6]&&0===t[8]&&0===t[9]&&1===t[10]}const T=_,g=h;Object.freeze(Object.defineProperty({__proto__:null,add:function(t,e,n){return t[0]=e[0]+n[0],t[1]=e[1]+n[1],t[2]=e[2]+n[2],t[3]=e[3]+n[3],t[4]=e[4]+n[4],t[5]=e[5]+n[5],t[6]=e[6]+n[6],t[7]=e[7]+n[7],t[8]=e[8]+n[8],t[9]=e[9]+n[9],t[10]=e[10]+n[10],t[11]=e[11]+n[11],t[12]=e[12]+n[12],t[13]=e[13]+n[13],t[14]=e[14]+n[14],t[15]=e[15]+n[15],t},adjoint:function(t,e){const n=e[0],r=e[1],o=e[2],s=e[3],i=e[4],a=e[5],u=e[6],_=e[7],l=e[8],c=e[9],P=e[10],E=e[11],f=e[12],M=e[13],S=e[14],p=e[15];return t[0]=a*(P*p-E*S)-c*(u*p-_*S)+M*(u*E-_*P),t[1]=-(r*(P*p-E*S)-c*(o*p-s*S)+M*(o*E-s*P)),t[2]=r*(u*p-_*S)-a*(o*p-s*S)+M*(o*_-s*u),t[3]=-(r*(u*E-_*P)-a*(o*E-s*P)+c*(o*_-s*u)),t[4]=-(i*(P*p-E*S)-l*(u*p-_*S)+f*(u*E-_*P)),t[5]=n*(P*p-E*S)-l*(o*p-s*S)+f*(o*E-s*P),t[6]=-(n*(u*p-_*S)-i*(o*p-s*S)+f*(o*_-s*u)),t[7]=n*(u*E-_*P)-i*(o*E-s*P)+l*(o*_-s*u),t[8]=i*(c*p-E*M)-l*(a*p-_*M)+f*(a*E-_*c),t[9]=-(n*(c*p-E*M)-l*(r*p-s*M)+f*(r*E-s*c)),t[10]=n*(a*p-_*M)-i*(r*p-s*M)+f*(r*_-s*a),t[11]=-(n*(a*E-_*c)-i*(r*E-s*c)+l*(r*_-s*a)),t[12]=-(i*(c*S-P*M)-l*(a*S-u*M)+f*(a*P-u*c)),t[13]=n*(c*S-P*M)-l*(r*S-o*M)+f*(r*P-o*c),t[14]=-(n*(a*S-u*M)-i*(r*S-o*M)+f*(r*u-o*a)),t[15]=n*(a*P-u*c)-i*(r*P-o*c)+l*(r*u-o*a),t},copy:function(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4],t[5]=e[5],t[6]=e[6],t[7]=e[7],t[8]=e[8],t[9]=e[9],t[10]=e[10],t[11]=e[11],t[12]=e[12],t[13]=e[13],t[14]=e[14],t[15]=e[15],t},determinant:function(t){const e=t[0],n=t[1],r=t[2],o=t[3],s=t[4],i=t[5],a=t[6],u=t[7],_=t[8],l=t[9],c=t[10],P=t[11],E=t[12],f=t[13],M=t[14],S=t[15];return(e*i-n*s)*(c*S-P*M)-(e*a-r*s)*(l*S-P*f)+(e*u-o*s)*(l*M-c*f)+(n*a-r*i)*(_*S-P*E)-(n*u-o*i)*(_*M-c*E)+(r*u-o*a)*(_*f-l*E)},equals:A,exactEquals:R,frob:function(t){return Math.sqrt(t[0]**2+t[1]**2+t[2]**2+t[3]**2+t[4]**2+t[5]**2+t[6]**2+t[7]**2+t[8]**2+t[9]**2+t[10]**2+t[11]**2+t[12]**2+t[13]**2+t[14]**2+t[15]**2)},fromQuat:function(t,e){const n=e[0],r=e[1],o=e[2],s=e[3],i=n+n,a=r+r,u=o+o,_=n*i,l=r*i,c=r*a,P=o*i,E=o*a,f=o*u,M=s*i,S=s*a,p=s*u;return t[0]=1-c-f,t[1]=l+p,t[2]=P-S,t[3]=0,t[4]=l-p,t[5]=1-_-f,t[6]=E+M,t[7]=0,t[8]=P+S,t[9]=E-M,t[10]=1-_-c,t[11]=0,t[12]=0,t[13]=0,t[14]=0,t[15]=1,t},fromQuat2:function(t,e){const n=p,r=-e[0],o=-e[1],s=-e[2],i=e[3],a=e[4],u=e[5],_=e[6],l=e[7],c=r*r+o*o+s*s+i*i;return c>0?(n[0]=2*(a*i+l*r+u*s-_*o)/c,n[1]=2*(u*i+l*o+_*r-a*s)/c,n[2]=2*(_*i+l*s+a*o-u*r)/c):(n[0]=2*(a*i+l*r+u*s-_*o),n[1]=2*(u*i+l*o+_*r-a*s),n[2]=2*(_*i+l*s+a*o-u*r)),S(t,e,n),t},fromRotation:f,fromRotationTranslation:S,fromRotationTranslationScale:C,fromRotationTranslationScaleOrigin:function(t,e,n,r,o){const s=e[0],i=e[1],a=e[2],u=e[3],_=s+s,l=i+i,c=a+a,P=s*_,E=s*l,f=s*c,M=i*l,S=i*c,p=a*c,C=u*_,h=u*l,R=u*c,A=r[0],O=r[1],N=r[2],T=o[0],g=o[1],d=o[2],L=(1-(M+p))*A,G=(E+R)*A,y=(f-h)*A,m=(E-R)*O,I=(1-(P+p))*O,W=(S+C)*O,F=(f+h)*N,w=(S-C)*N,b=(1-(P+M))*N;return t[0]=L,t[1]=G,t[2]=y,t[3]=0,t[4]=m,t[5]=I,t[6]=W,t[7]=0,t[8]=F,t[9]=w,t[10]=b,t[11]=0,t[12]=n[0]+T-(L*T+m*g+F*d),t[13]=n[1]+g-(G*T+I*g+w*d),t[14]=n[2]+d-(y*T+W*g+b*d),t[15]=1,t},fromScaling:function(t,e){return t[0]=e[0],t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=e[1],t[6]=0,t[7]=0,t[8]=0,t[9]=0,t[10]=e[2],t[11]=0,t[12]=0,t[13]=0,t[14]=0,t[15]=1,t},fromTranslation:E,fromXRotation:M,fromYRotation:function(t,e){const n=Math.sin(e),r=Math.cos(e);return t[0]=r,t[1]=0,t[2]=-n,t[3]=0,t[4]=0,t[5]=1,t[6]=0,t[7]=0,t[8]=n,t[9]=0,t[10]=r,t[11]=0,t[12]=0,t[13]=0,t[14]=0,t[15]=1,t},fromZRotation:function(t,e){const n=Math.sin(e),r=Math.cos(e);return t[0]=r,t[1]=n,t[2]=0,t[3]=0,t[4]=-n,t[5]=r,t[6]=0,t[7]=0,t[8]=0,t[9]=0,t[10]=1,t[11]=0,t[12]=0,t[13]=0,t[14]=0,t[15]=1,t},frustum:function(t,e,n,r,o,s,i){const a=1/(n-e),u=1/(o-r),_=1/(s-i);return t[0]=2*s*a,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=2*s*u,t[6]=0,t[7]=0,t[8]=(n+e)*a,t[9]=(o+r)*u,t[10]=(i+s)*_,t[11]=-1,t[12]=0,t[13]=0,t[14]=i*s*2*_,t[15]=0,t},getRotation:function(t,e){const n=e[0]+e[5]+e[10];let r=0;return n>0?(r=2*Math.sqrt(n+1),t[3]=.25*r,t[0]=(e[6]-e[9])/r,t[1]=(e[8]-e[2])/r,t[2]=(e[1]-e[4])/r):e[0]>e[5]&&e[0]>e[10]?(r=2*Math.sqrt(1+e[0]-e[5]-e[10]),t[3]=(e[6]-e[9])/r,t[0]=.25*r,t[1]=(e[1]+e[4])/r,t[2]=(e[8]+e[2])/r):e[5]>e[10]?(r=2*Math.sqrt(1+e[5]-e[0]-e[10]),t[3]=(e[8]-e[2])/r,t[0]=(e[1]+e[4])/r,t[1]=.25*r,t[2]=(e[6]+e[9])/r):(r=2*Math.sqrt(1+e[10]-e[0]-e[5]),t[3]=(e[1]-e[4])/r,t[0]=(e[8]+e[2])/r,t[1]=(e[6]+e[9])/r,t[2]=.25*r),t},getScaling:function(t,e){const n=e[0],r=e[1],o=e[2],s=e[4],i=e[5],a=e[6],u=e[8],_=e[9],l=e[10];return t[0]=Math.sqrt(n*n+r*r+o*o),t[1]=Math.sqrt(s*s+i*i+a*a),t[2]=Math.sqrt(u*u+_*_+l*l),t},getTranslation:function(t,e){return t[0]=e[12],t[1]=e[13],t[2]=e[14],t},hasIdentityRotation:N,identity:i,invert:u,invertOrIdentity:function(t,e){return u(t,e)||i(t),t},isOrthoNormal:O,lookAt:function(t,e,n,r){let s,a,u,_,l,c,P,E,f,M;const S=e[0],p=e[1],C=e[2],h=r[0],R=r[1],A=r[2],O=n[0],N=n[1],T=n[2],g=(0,o.g)();return Math.abs(S-O)<g&&Math.abs(p-N)<g&&Math.abs(C-T)<g?i(t):(P=S-O,E=p-N,f=C-T,M=1/Math.sqrt(P*P+E*E+f*f),P*=M,E*=M,f*=M,s=R*f-A*E,a=A*P-h*f,u=h*E-R*P,M=Math.sqrt(s*s+a*a+u*u),M?(M=1/M,s*=M,a*=M,u*=M):(s=0,a=0,u=0),_=E*u-f*a,l=f*s-P*u,c=P*a-E*s,M=Math.sqrt(_*_+l*l+c*c),M?(M=1/M,_*=M,l*=M,c*=M):(_=0,l=0,c=0),t[0]=s,t[1]=_,t[2]=P,t[3]=0,t[4]=a,t[5]=l,t[6]=E,t[7]=0,t[8]=u,t[9]=c,t[10]=f,t[11]=0,t[12]=-(s*S+a*p+u*C),t[13]=-(_*S+l*p+c*C),t[14]=-(P*S+E*p+f*C),t[15]=1,t)},mul:T,multiply:_,multiplyScalar:function(t,e,n){return t[0]=e[0]*n,t[1]=e[1]*n,t[2]=e[2]*n,t[3]=e[3]*n,t[4]=e[4]*n,t[5]=e[5]*n,t[6]=e[6]*n,t[7]=e[7]*n,t[8]=e[8]*n,t[9]=e[9]*n,t[10]=e[10]*n,t[11]=e[11]*n,t[12]=e[12]*n,t[13]=e[13]*n,t[14]=e[14]*n,t[15]=e[15]*n,t},multiplyScalarAndAdd:function(t,e,n,r){return t[0]=e[0]+n[0]*r,t[1]=e[1]+n[1]*r,t[2]=e[2]+n[2]*r,t[3]=e[3]+n[3]*r,t[4]=e[4]+n[4]*r,t[5]=e[5]+n[5]*r,t[6]=e[6]+n[6]*r,t[7]=e[7]+n[7]*r,t[8]=e[8]+n[8]*r,t[9]=e[9]+n[9]*r,t[10]=e[10]+n[10]*r,t[11]=e[11]+n[11]*r,t[12]=e[12]+n[12]*r,t[13]=e[13]+n[13]*r,t[14]=e[14]+n[14]*r,t[15]=e[15]+n[15]*r,t},ortho:function(t,e,n,r,o,s,i){const a=1/(e-n),u=1/(r-o),_=1/(s-i);return t[0]=-2*a,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=-2*u,t[6]=0,t[7]=0,t[8]=0,t[9]=0,t[10]=2*_,t[11]=0,t[12]=(e+n)*a,t[13]=(o+r)*u,t[14]=(i+s)*_,t[15]=1,t},perspective:function(t,e,n,r,o){const s=1/Math.tan(e/2);let i;return t[0]=s/n,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=s,t[6]=0,t[7]=0,t[8]=0,t[9]=0,t[11]=-1,t[12]=0,t[13]=0,t[15]=0,null!=o&&o!==1/0?(i=1/(r-o),t[10]=(o+r)*i,t[14]=2*o*r*i):(t[10]=-1,t[14]=-2*r),t},perspectiveFromFieldOfView:function(t,e,n,r){const o=Math.tan(e.upDegrees*Math.PI/180),s=Math.tan(e.downDegrees*Math.PI/180),i=Math.tan(e.leftDegrees*Math.PI/180),a=Math.tan(e.rightDegrees*Math.PI/180),u=2/(i+a),_=2/(o+s);return t[0]=u,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=_,t[6]=0,t[7]=0,t[8]=-(i-a)*u*.5,t[9]=(o-s)*_*.5,t[10]=r/(n-r),t[11]=-1,t[12]=0,t[13]=0,t[14]=r*n/(n-r),t[15]=0,t},rotate:P,rotateX:function(t,e,n){const r=Math.sin(n),o=Math.cos(n),s=e[4],i=e[5],a=e[6],u=e[7],_=e[8],l=e[9],c=e[10],P=e[11];return e!==t&&(t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[12]=e[12],t[13]=e[13],t[14]=e[14],t[15]=e[15]),t[4]=s*o+_*r,t[5]=i*o+l*r,t[6]=a*o+c*r,t[7]=u*o+P*r,t[8]=_*o-s*r,t[9]=l*o-i*r,t[10]=c*o-a*r,t[11]=P*o-u*r,t},rotateY:function(t,e,n){const r=Math.sin(n),o=Math.cos(n),s=e[0],i=e[1],a=e[2],u=e[3],_=e[8],l=e[9],c=e[10],P=e[11];return e!==t&&(t[4]=e[4],t[5]=e[5],t[6]=e[6],t[7]=e[7],t[12]=e[12],t[13]=e[13],t[14]=e[14],t[15]=e[15]),t[0]=s*o-_*r,t[1]=i*o-l*r,t[2]=a*o-c*r,t[3]=u*o-P*r,t[8]=s*r+_*o,t[9]=i*r+l*o,t[10]=a*r+c*o,t[11]=u*r+P*o,t},rotateZ:function(t,e,n){const r=Math.sin(n),o=Math.cos(n),s=e[0],i=e[1],a=e[2],u=e[3],_=e[4],l=e[5],c=e[6],P=e[7];return e!==t&&(t[8]=e[8],t[9]=e[9],t[10]=e[10],t[11]=e[11],t[12]=e[12],t[13]=e[13],t[14]=e[14],t[15]=e[15]),t[0]=s*o+_*r,t[1]=i*o+l*r,t[2]=a*o+c*r,t[3]=u*o+P*r,t[4]=_*o-s*r,t[5]=l*o-i*r,t[6]=c*o-a*r,t[7]=P*o-u*r,t},scale:c,set:s,str:function(t){return"mat4("+t[0]+", "+t[1]+", "+t[2]+", "+t[3]+", "+t[4]+", "+t[5]+", "+t[6]+", "+t[7]+", "+t[8]+", "+t[9]+", "+t[10]+", "+t[11]+", "+t[12]+", "+t[13]+", "+t[14]+", "+t[15]+")"},sub:g,subtract:h,targetTo:function(t,e,n,r){const o=e[0],s=e[1],i=e[2],a=r[0],u=r[1],_=r[2];let l=o-n[0],c=s-n[1],P=i-n[2],E=l*l+c*c+P*P;E>0&&(E=1/Math.sqrt(E),l*=E,c*=E,P*=E);let f=u*P-_*c,M=_*l-a*P,S=a*c-u*l;return E=f*f+M*M+S*S,E>0&&(E=1/Math.sqrt(E),f*=E,M*=E,S*=E),t[0]=f,t[1]=M,t[2]=S,t[3]=0,t[4]=c*S-P*M,t[5]=P*f-l*S,t[6]=l*M-c*f,t[7]=0,t[8]=l,t[9]=c,t[10]=P,t[11]=0,t[12]=o,t[13]=s,t[14]=i,t[15]=1,t},translate:l,transpose:a},Symbol.toStringTag,{value:"Module"}))},83305:(t,e,n)=>{n.d(e,{e:()=>c,f:()=>l,g:()=>_,h:()=>S,i:()=>a,j:()=>A,l:()=>u,p:()=>G});var r=n(99880),o=n(80442);let s,i=null;function a(){return!!i}function u(){return s||(s=n.e(6774).then(n.bind(n,66774)).then((t=>t.p)).then((({default:t})=>t({locateFile:t=>(0,r.V)(`esri/geometry/support/${t}`)}))).then((t=>{N(t)})),s)}var _,l,c,P;!function(t){function e(t,e,n){i.ensureCache.prepare();const r=d(n),o=n===r,s=i.ensureFloat64(r),a=i._pe_geog_to_proj(i.getPointer(t),e,s);return a&&L(n,e,s,o),a}function n(t,e,n){return r(t,e,n,0)}function r(t,e,n,r){i.ensureCache.prepare();const o=d(n),s=n===o,a=i.ensureFloat64(o),u=i._pe_proj_to_geog_center(i.getPointer(t),e,a,r);return u&&L(n,e,a,s),u}t.geogToProj=e,t.projGeog=function(t,r,o,s){switch(s){case l.PE_TRANSFORM_P_TO_G:return n(t,r,o);case l.PE_TRANSFORM_G_TO_P:return e(t,r,o)}return 0},t.projToGeog=n,t.projToGeogCenter=r}(_||(_={})),(P=l||(l={})).init=function(){P.PE_BUFFER_MAX=i.PeDefs.prototype.PE_BUFFER_MAX,P.PE_NAME_MAX=i.PeDefs.prototype.PE_NAME_MAX,P.PE_MGRS_MAX=i.PeDefs.prototype.PE_MGRS_MAX,P.PE_USNG_MAX=i.PeDefs.prototype.PE_USNG_MAX,P.PE_DD_MAX=i.PeDefs.prototype.PE_DD_MAX,P.PE_DDM_MAX=i.PeDefs.prototype.PE_DDM_MAX,P.PE_DMS_MAX=i.PeDefs.prototype.PE_DMS_MAX,P.PE_UTM_MAX=i.PeDefs.prototype.PE_UTM_MAX,P.PE_PARM_MAX=i.PeDefs.prototype.PE_PARM_MAX,P.PE_TYPE_NONE=i.PeDefs.prototype.PE_TYPE_NONE,P.PE_TYPE_GEOGCS=i.PeDefs.prototype.PE_TYPE_GEOGCS,P.PE_TYPE_PROJCS=i.PeDefs.prototype.PE_TYPE_PROJCS,P.PE_TYPE_GEOGTRAN=i.PeDefs.prototype.PE_TYPE_GEOGTRAN,P.PE_TYPE_COORDSYS=i.PeDefs.prototype.PE_TYPE_COORDSYS,P.PE_TYPE_UNIT=i.PeDefs.prototype.PE_TYPE_UNIT,P.PE_TYPE_LINUNIT=i.PeDefs.prototype.PE_TYPE_LINUNIT,P.PE_STR_OPTS_NONE=i.PeDefs.prototype.PE_STR_OPTS_NONE,P.PE_STR_AUTH_NONE=i.PeDefs.prototype.PE_STR_AUTH_NONE,P.PE_STR_AUTH_TOP=i.PeDefs.prototype.PE_STR_AUTH_TOP,P.PE_STR_NAME_CANON=i.PeDefs.prototype.PE_STR_NAME_CANON,P.PE_PARM_X0=i.PeDefs.prototype.PE_PARM_X0,P.PE_PARM_ND=i.PeDefs.prototype.PE_PARM_ND,P.PE_TRANSFORM_1_TO_2=i.PeDefs.prototype.PE_TRANSFORM_1_TO_2,P.PE_TRANSFORM_2_TO_1=i.PeDefs.prototype.PE_TRANSFORM_2_TO_1,P.PE_TRANSFORM_P_TO_G=i.PeDefs.prototype.PE_TRANSFORM_P_TO_G,P.PE_TRANSFORM_G_TO_P=i.PeDefs.prototype.PE_TRANSFORM_G_TO_P,P.PE_HORIZON_RECT=i.PeDefs.prototype.PE_HORIZON_RECT,P.PE_HORIZON_POLY=i.PeDefs.prototype.PE_HORIZON_POLY,P.PE_HORIZON_LINE=i.PeDefs.prototype.PE_HORIZON_LINE,P.PE_HORIZON_DELTA=i.PeDefs.prototype.PE_HORIZON_DELTA},function(t){const e={},n={},r=t=>{if(t){const e=t.getType();switch(e){case l.PE_TYPE_GEOGCS:t=i.castObject(t,i.PeGeogcs);break;case l.PE_TYPE_PROJCS:t=i.castObject(t,i.PeProjcs);break;case l.PE_TYPE_GEOGTRAN:t=i.castObject(t,i.PeGeogtran);break;default:e&l.PE_TYPE_UNIT&&(t=i.castObject(t,i.PeUnit))}}return t};function o(t,n){let o=null,s=e[t];if(s||(s={},e[t]=s),s.hasOwnProperty(String(n)))o=s[n];else{const e=i.PeFactory.prototype.factoryByType(t,n);i.compare(e,i.NULL)||(o=e,s[n]=o)}return o=r(o),o}t.initialize=function(){i.PeFactory.prototype.initialize(null)},t.coordsys=function(t){return o(l.PE_TYPE_COORDSYS,t)},t.factoryByType=o,t.fromString=function(t,e){let o=null,s=n[t];if(s||(s={},n[t]=s),s.hasOwnProperty(e))o=s[e];else{const n=i.PeFactory.prototype.fromString(t,e);i.compare(n,i.NULL)||(o=n,s[e]=o)}return o=r(o),o},t.geogcs=function(t){return o(l.PE_TYPE_GEOGCS,t)},t.geogtran=function(t){return o(l.PE_TYPE_GEOGTRAN,t)},t.getCode=function(t){return i.PeFactory.prototype.getCode(t)},t.projcs=function(t){return o(l.PE_TYPE_PROJCS,t)},t.unit=function(t){return o(l.PE_TYPE_UNIT,t)}}(c||(c={}));let E=null;var f,M,S,p,C,h,R,A,O;function N(t){function e(t,e,n){t[e]=n(t[e])}i=t,l.init(),f.init(),C.init(),R.init(),A.init(),E=class extends i.PeGCSExtent{destroy(){i.destroy(this)}};const n=[i.PeDatum,i.PeGeogcs,i.PeGeogtran,i.PeObject,i.PeParameter,i.PePrimem,i.PeProjcs,i.PeSpheroid,i.PeUnit];for(const t of n)e(t.prototype,"getName",(t=>function(){return t.call(this,new Array(l.PE_NAME_MAX))}));for(const t of[i.PeGeogtran,i.PeProjcs])e(t.prototype,"getParameters",(t=>function(){const e=new Array(l.PE_PARM_MAX);let n=t.call(this);for(let t=0;t<e.length;t++){const r=i.getValue(n,"*");e[t]=r?i.wrapPointer(r,i.PeParameter):null,n+=Int32Array.BYTES_PER_ELEMENT}return e}));e(i.PeHorizon.prototype,"getCoord",(t=>function(){const e=this.getSize();if(!e)return null;const n=[];return L(n,e,t.call(this)),n})),e(i.PeGTlistExtendedEntry.prototype,"getEntries",(t=>{const e=i._pe_getPeGTlistExtendedGTsSize();return function(){let n=null;const r=t.call(this);if(!i.compare(r,i.NULL)){n=[r];const t=this.getSteps();if(t>1){const o=i.getPointer(r);for(let r=1;r<t;r++)n.push(i.wrapPointer(o+e*r,i.PeGTlistExtendedGTs))}}return n}}));const r=i._pe_getPeHorizonSize(),o=t=>function(){let e=this._cache;if(e||(e=new Map,this._cache=e),e.has(t))return e.get(t);let n=null;const o=t.call(this);if(!i.compare(o,i.NULL)){n=[o];const t=o.getNump();if(t>1){const e=i.getPointer(o);for(let o=1;o<t;o++)n.push(i.wrapPointer(e+r*o,i.PeHorizon))}}return e.set(t,n),n};e(i.PeProjcs.prototype,"horizonGcsGenerate",o),e(i.PeProjcs.prototype,"horizonPcsGenerate",o),i.PeObject.prototype.toString=function(t=l.PE_STR_OPTS_NONE){i.ensureCache.prepare();const e=i.getPointer(this),n=i.ensureInt8(new Array(l.PE_BUFFER_MAX));return i.UTF8ToString(i._pe_object_to_string_ext(e,t,n))}}function T(t){if(!t)return;const e=i.getClass(t);if(!e)return;const n=i.getCache(e);if(!n)return;const r=i.getPointer(t);r&&delete n[r]}function g(t,e){const n=[],r=new Array(e);for(let e=0;e<t;e++)n.push(i.ensureInt8(r));return n}function d(t){let e;return Array.isArray(t[0])?(e=[],t.forEach((t=>{e.push(t[0],t[1])}))):e=t,e}function L(t,e,n,r=!1){if(r)for(let r=0;r<2*e;r++)t[r]=i.getValue(n+r*Float64Array.BYTES_PER_ELEMENT,"double");else{const r=0===t.length;for(let o=0;o<e;o++)r&&(t[o]=new Array(2)),t[o][0]=i.getValue(n,"double"),t[o][1]=i.getValue(n+Float64Array.BYTES_PER_ELEMENT,"double"),n+=2*Float64Array.BYTES_PER_ELEMENT}}!function(t){let e;t.init=function(){t.PE_GTLIST_OPTS_COMMON=i.PeGTlistExtended.prototype.PE_GTLIST_OPTS_COMMON,e=i._pe_getPeGTlistExtendedEntrySize()},t.getGTlist=function(t,n,r,o,s,a){let u=null;const _=new i.PeInteger(a);try{const l=i.PeGTlistExtended.prototype.getGTlist(t,n,r,o,s,_);if((a=_.val)&&(u=[l],a>1)){const t=i.getPointer(l);for(let n=1;n<a;n++)u.push(i.wrapPointer(t+e*n,i.PeGTlistExtendedEntry))}}finally{i.destroy(_)}return u}}(f||(f={})),function(t){t.destroy=function(t){if(t&&t.length){for(const e of t)T(e),e.getEntries().forEach((t=>{T(t);const e=t.getGeogtran();T(e),e.getParameters().forEach(T),[e.getGeogcs1(),e.getGeogcs2()].forEach((t=>{T(t);const e=t.getDatum();T(e),T(e.getSpheroid()),T(t.getPrimem()),T(t.getUnit())}))}));i.PeGTlistExtendedEntry.prototype.Delete(t[0])}}}(M||(M={})),function(t){t.geogToGeog=function(t,e,n,r,o){i.ensureCache.prepare();const s=d(n),a=n===s,u=i.ensureFloat64(s);let _=0;r&&(_=i.ensureFloat64(r));const l=i._pe_geog_to_geog(i.getPointer(t),e,u,_,o);return l&&L(n,e,u,a),l}}(S||(S={})),function(t){const e=(t,e,n,r,o,s)=>{let a,u;switch(i.ensureCache.prepare(),t){case"dd":a=i._pe_geog_to_dd,u=l.PE_DD_MAX;break;case"ddm":a=i._pe_geog_to_ddm,u=l.PE_DDM_MAX;break;case"dms":a=i._pe_geog_to_dms,u=l.PE_DMS_MAX}let _=0;e&&(_=i.getPointer(e));const c=d(r),P=i.ensureFloat64(c),E=g(n,u),f=a(_,n,P,o,i.ensureInt32(E));if(f)for(let t=0;t<n;t++)s[t]=i.UTF8ToString(E[t]);return f},n=(t,e,n,r,o)=>{let s;switch(i.ensureCache.prepare(),t){case"dd":s=i._pe_dd_to_geog;break;case"ddm":s=i._pe_ddm_to_geog;break;case"dms":s=i._pe_dms_to_geog}let a=0;e&&(a=i.getPointer(e));const u=r.map((t=>i.ensureString(t))),_=i.ensureInt32(u),l=i.ensureFloat64(new Array(2*n)),c=s(a,n,_,l);return c&&L(o,n,l),c};t.geogToDms=function(t,n,r,o,s){return e("dms",t,n,r,o,s)},t.dmsToGeog=function(t,e,r,o){return n("dms",t,e,r,o)},t.geogToDdm=function(t,n,r,o,s){return e("ddm",t,n,r,o,s)},t.ddmToGeog=function(t,e,r,o){return n("ddm",t,e,r,o)},t.geogToDd=function(t,n,r,o,s){return e("dd",t,n,r,o,s)},t.ddToGeog=function(t,e,r,o){return n("dd",t,e,r,o)}}(p||(p={})),function(t){t.init=function(){t.PE_MGRS_STYLE_NEW=i.PeNotationMgrs.prototype.PE_MGRS_STYLE_NEW,t.PE_MGRS_STYLE_OLD=i.PeNotationMgrs.prototype.PE_MGRS_STYLE_OLD,t.PE_MGRS_STYLE_AUTO=i.PeNotationMgrs.prototype.PE_MGRS_STYLE_AUTO,t.PE_MGRS_180_ZONE_1_PLUS=i.PeNotationMgrs.prototype.PE_MGRS_180_ZONE_1_PLUS,t.PE_MGRS_ADD_SPACES=i.PeNotationMgrs.prototype.PE_MGRS_ADD_SPACES},t.geogToMgrsExtended=function(t,e,n,r,o,s,a){i.ensureCache.prepare();let u=0;t&&(u=i.getPointer(t));const _=d(n),c=i.ensureFloat64(_),P=g(e,l.PE_MGRS_MAX),E=i.ensureInt32(P),f=i._pe_geog_to_mgrs_extended(u,e,c,r,o,s,E);if(f)for(let t=0;t<e;t++)a[t]=i.UTF8ToString(P[t]);return f},t.mgrsToGeogExtended=function(t,e,n,r,o){i.ensureCache.prepare();let s=0;t&&(s=i.getPointer(t));const a=n.map((t=>i.ensureString(t))),u=i.ensureInt32(a),_=i.ensureFloat64(new Array(2*e)),l=i._pe_mgrs_to_geog_extended(s,e,u,r,_);return l&&L(o,e,_),l}}(C||(C={})),function(t){t.geogToUsng=function(t,e,n,r,o,s,a){i.ensureCache.prepare();let u=0;t&&(u=i.getPointer(t));const _=d(n),c=i.ensureFloat64(_),P=g(e,l.PE_MGRS_MAX),E=i.ensureInt32(P),f=i._pe_geog_to_usng(u,e,c,r,o,s,E);if(f)for(let t=0;t<e;t++)a[t]=i.UTF8ToString(P[t]);return f},t.usngToGeog=function(t,e,n,r){i.ensureCache.prepare();let o=0;t&&(o=i.getPointer(t));const s=n.map((t=>i.ensureString(t))),a=i.ensureInt32(s),u=i.ensureFloat64(new Array(2*e)),_=i._pe_usng_to_geog(o,e,a,u);return _&&L(r,e,u),_}}(h||(h={})),function(t){t.init=function(){t.PE_UTM_OPTS_NONE=i.PeNotationUtm.prototype.PE_UTM_OPTS_NONE,t.PE_UTM_OPTS_ADD_SPACES=i.PeNotationUtm.prototype.PE_UTM_OPTS_ADD_SPACES,t.PE_UTM_OPTS_NS=i.PeNotationUtm.prototype.PE_UTM_OPTS_NS},t.geogToUtm=function(t,e,n,r,o){i.ensureCache.prepare();let s=0;t&&(s=i.getPointer(t));const a=d(n),u=i.ensureFloat64(a),_=g(e,l.PE_UTM_MAX),c=i.ensureInt32(_),P=i._pe_geog_to_utm(s,e,u,r,c);if(P)for(let t=0;t<e;t++)o[t]=i.UTF8ToString(_[t]);return P},t.utmToGeog=function(t,e,n,r,o){i.ensureCache.prepare();let s=0;t&&(s=i.getPointer(t));const a=n.map((t=>i.ensureString(t))),u=i.ensureInt32(a),_=i.ensureFloat64(new Array(2*e)),l=i._pe_utm_to_geog(s,e,u,r,_);return l&&L(o,e,_),l}}(R||(R={})),function(t){const e=new Map;t.init=function(){t.PE_PCSINFO_OPTION_NONE=i.PePCSInfo.prototype.PE_PCSINFO_OPTION_NONE,t.PE_PCSINFO_OPTION_DOMAIN=i.PePCSInfo.prototype.PE_PCSINFO_OPTION_DOMAIN,t.PE_POLE_OUTSIDE_BOUNDARY=i.PePCSInfo.prototype.PE_POLE_OUTSIDE_BOUNDARY,t.PE_POLE_POINT=i.PePCSInfo.prototype.PE_POLE_POINT},t.generate=function(n,r=t.PE_PCSINFO_OPTION_DOMAIN){let o=null,s=null;return e.has(n)&&(s=e.get(n),s[r]&&(o=s[r])),o||(o=i.PePCSInfo.prototype.generate(n,r),s||(s=[],e.set(n,s)),s[r]=o),o}}(A||(A={})),function(t){t.versionString=function(){return i.PeVersion.prototype.version_string()}}(O||(O={}));const G=Object.freeze(Object.defineProperty({__proto__:null,get PeCSTransformations(){return _},get PeDefs(){return l},get PeFactory(){return c},get PeGCSExtent(){return E},get PeGTTransformations(){return S},get PeGTlistExtended(){return f},get PeGTlistExtendedEntry(){return M},get PeNotationDms(){return p},get PeNotationMgrs(){return C},get PeNotationUsng(){return h},get PeNotationUtm(){return R},get PePCSInfo(){return A},get PeVersion(){return O},_init:N,get _pe(){return i},isLoaded:a,isSupported:function(){return!!(0,o.Z)("esri-wasm")},load:u},Symbol.toStringTag,{value:"Module"}))},44547:(t,e,n)=>{n.d(e,{Up:()=>x,Bm:()=>X,iQ:()=>v,kR:()=>W,zD:()=>F,iV:()=>w,CM:()=>z,oj:()=>b,dz:()=>D,JY:()=>U,KC:()=>j,Wt:()=>Y});var r=n(20102),o=n(22021),s=n(70586),i=n(95330),a=n(67900),u=n(58971),_=n(10661),l=n(52138),c=(n(17896),n(65617)),P=n(2109),E=n(6570),f=n(65091),M=n(83305),S=n(94139),p=n(38913),C=n(58901),h=(n(24470),n(68441));Math.PI;const R=h.sv.radius,A=h.sv.eccentricitySquared,O={a1:R*A,a2:R*A*R*A,a3:R*A*A/2,a4:R*A*R*A*2.5,a5:R*A+R*A*A/2,a6:1-A};h.sv.radius,h.sv.flattening,h.Z1.radius,h.Z1.flattening,h.yr.radius,h.yr.flattening,h.yr.radius;var N=n(18571),T=n(8744),g=n(56726),d=n(69285);let L=null,G=null,y=null,m={};const I=new _.s;function W(){return!!L&&(0,M.i)()}function F(t){return(0,s.Wi)(y)&&(y=Promise.all([(0,M.l)(),n.e(5837).then(n.bind(n,45837)).then((t=>t.g)),n.e(819).then(n.bind(n,80819))])),y.then((([,e,{hydratedAdapter:n}])=>{(0,i.k_)(t),G=n,L=e.default,L._enableProjection(M.p),I.notify()}))}function w(t,e,n=null,r=null){return Array.isArray(t)?0===t.length?[]:b(G,t,t[0].spatialReference,e,n,r):b(G,[t],t.spatialReference,e,n,r)[0]}function b(t,e,n,r,o=null,i=null){if((0,s.Wi)(n)||(0,s.Wi)(r))return e;if(x(n,r,o))return e.map((t=>(0,s.Wg)(function(t,e,n){return t?"x"in t?function(t,e,n,r,o){dt[0]=t.x,dt[1]=t.y;const s=t.z;return dt[2]=void 0!==s?s:o,z(dt,e,0,dt,r,0,1)?(n.x=dt[0],n.y=dt[1],n.spatialReference=r,void 0===s?(n.z=void 0,n.hasZ=!1):(n.z=dt[2],n.hasZ=!0),void 0===t.m?(n.m=void 0,n.hasM=!1):(n.m=t.m,n.hasM=!0),n):null}(t,e,new S.Z,n,0):"xmin"in t?function(t,e,n,r,o){const{xmin:s,ymin:i,xmax:a,ymax:u,hasZ:_,hasM:l}=t;return Z(s,i,_?t.zmin:o,e,dt,r)?(n.xmin=dt[0],n.ymin=dt[1],_&&(n.zmin=dt[2]),Z(a,u,_?t.zmax:o,e,dt,r)?(n.xmax=dt[0],n.ymax=dt[1],_&&(n.zmax=dt[2]),l&&(n.mmin=t.mmin,n.mmax=t.mmax),n.spatialReference=r,n):null):null}(t,e,new E.Z,n,0):"rings"in t?B(t,e,new p.Z,n,0):"paths"in t?function(t,e,n,r,o){const{paths:s,hasZ:i,hasM:a}=t,u=[];return K(s,i??!1,a??!1,e,u,r,o)?(n.paths=u,n.spatialReference=r,n.hasZ=i,n.hasM=a,n):null}(t,e,new C.Z,n,0):"points"in t?function(t,e,n,r,o){const{points:s,hasZ:i,hasM:a}=t,u=[],_=s.length,l=[];for(const t of s)l.push(t[0],t[1],i?t[2]:o);if(!z(l,e,0,l,r,0,_))return null;for(let t=0;t<_;++t){const e=3*t,n=l[e],r=l[e+1];i&&a?u.push([n,r,l[e+2],s[t][3]]):i?u.push([n,r,l[e+2]]):a?u.push([n,r,s[t][2]]):u.push([n,r])}return n.points=u,n.spatialReference=r,n.hasZ=i,n.hasM=a,n}(t,e,new f.Z,n,0):null:null}(t,n,r))));if((0,s.Wi)(o)){const t=N.Z.cacheKey(n,r);void 0!==m[t]?o=m[t]:(o=function(t,e,n=null){if((0,s.Wi)(t)||(0,s.Wi)(e))return null;if((0,s.Wi)(L)||(0,s.Wi)(G))throw new H;const r=L._getTransformation(G,t,e,n,n?.spatialReference);return null!==r?N.Z.fromGE(r):null}(n,r,void 0),(0,s.Wi)(o)&&(o=new N.Z),m[t]=o)}if((0,s.Wi)(L)||(0,s.Wi)(t))throw new H;return(0,s.pC)(i)?L._project(t,e,n,r,o,i):L._project(t,e,n,r,o)}function D(t,e){const n=U([t],e);return(0,s.pC)(n.pending)?{pending:n.pending,geometry:null}:(0,s.pC)(n.geometries)?{pending:null,geometry:n.geometries[0]}:{pending:null,geometry:null}}function U(t,e){if(!W())for(const n of t)if((0,s.pC)(n)&&!(0,T.fS)(n.spatialReference,e)&&(0,T.JY)(n.spatialReference)&&(0,T.JY)(e)&&!x(n.spatialReference,e))return(0,u.it)(I),{pending:F(),geometries:null};return{pending:null,geometries:t.map((t=>(0,s.Wi)(t)?null:(0,T.fS)(t.spatialReference,e)?t:(0,T.JY)(t.spatialReference)&&(0,T.JY)(e)?function(t,e){try{const n=w(t,e);if(null==n)return null;"xmin"in t&&"xmin"in n&&(n.zmin=t.zmin,n.zmax=t.zmax);const r=(0,d.k)(n.type,t.spatialReference,e);return(0,s.pC)(r)&&r(n),n}catch(t){if(!(t instanceof H))throw t;return null}}(t,e):null))}}class H extends r.Z{constructor(){super("projection:not-loaded","projection engine not fully loaded yet, please call load()")}}var k;function x(t,e,n){return!n&&(!!(0,T.fS)(t,e)||(0,T.JY)(t)&&(0,T.JY)(e)&&!!St(t,e,Rt))}async function v(t,e,n,r){if(W())return(0,i.Yn)(r);if(Array.isArray(t)){for(const{source:e,dest:n,geographicTransformation:o}of t)if(!x(e,n,o))return F(r)}else if(!x(t,e,n))return F(r);return(0,i.Yn)(r)}function Y(t,e,n=e.spatialReference,r=0){return(0,s.pC)(t.spatialReference)&&(0,s.pC)(n)&&(0,s.pC)(B(t,t.spatialReference,e,n,r))}function B(t,e,n,r,o){const{rings:s,hasZ:i,hasM:a}=t,u=[];return K(s,i??!1,a??!1,e,u,r,o)?(n.rings=u,n.spatialReference=r,n.hasZ=i,n.hasM=a,n):null}function j(t,e,n,r=0){dt[0]=t.x,dt[1]=t.y;const o=t.z;return dt[2]=void 0!==o?o:r,z(dt,t.spatialReference,0,e,n,0,1)}function Z(t,e,n,r,o,s){return Nt[0]=t,Nt[1]=e,Nt[2]=n,z(Nt,r,0,o,s,0,1)}function z(t,e,n,r,o,i,a=1){const u=St(e,o,Rt);if((0,s.Wi)(u))return!1;if(u===Q){if(t===r&&n===i)return!0;const e=n+3*a;for(let o=n,s=i;o<e;o++,s++)r[s]=t[o];return!0}const _=n+3*a;for(let e=n,o=i;e<_;e+=3,o+=3)u(t,e,r,o);return!0}function K(t,e,n,r,o,s,i=0){const a=new Array;for(const n of t)for(const t of n)a.push(t[0],t[1],e?t[2]:i);if(!z(a,r,0,a,s,0,a.length/3))return!1;let u=0;o.length=0;for(const r of t){const t=new Array;for(const o of r)e&&n?t.push([a[u++],a[u++],a[u++],o[3]]):e?t.push([a[u++],a[u++],a[u++]]):n?(t.push([a[u++],a[u++],o[2]]),u++):(t.push([a[u++],a[u++]]),u++);o.push(t)}return!0}function X(t,e,n,r){if((0,s.Wi)(t)||(0,s.Wi)(r))return!1;const o=V(t,Ct),i=V(r,ht);if(o===i&&!q(i)&&(o!==k.UNKNOWN||(0,T.fS)(t,r)))return(0,l.f)(n,e),!0;if(q(i)){const t=Mt[o][k.LON_LAT],r=Mt[k.LON_LAT][i];return!(0,s.Wi)(t)&&!(0,s.Wi)(r)&&(t(e,0,Tt,0),r(Tt,0,gt,0),J(At*Tt[0],At*Tt[1],n),n[12]=gt[0],n[13]=gt[1],n[14]=gt[2],!0)}if((i===k.WEB_MERCATOR||i===k.PLATE_CARREE)&&(o===k.WGS84||o===k.CGCS2000&&i===k.PLATE_CARREE||o===k.SPHERICAL_ECEF||o===k.WEB_MERCATOR)){const t=Mt[o][k.LON_LAT],r=Mt[k.LON_LAT][i];return!(0,s.Wi)(t)&&!(0,s.Wi)(r)&&(t(e,0,Tt,0),r(Tt,0,gt,0),o===k.SPHERICAL_ECEF?function(t,e,n){J(t,e,n),(0,l.t)(n,n)}(At*Tt[0],At*Tt[1],n):(0,l.i)(n),n[12]=gt[0],n[13]=gt[1],n[14]=gt[2],!0)}return!1}function q(t){return t===k.SPHERICAL_ECEF||t===k.SPHERICAL_MARS_PCPF||t===k.SPHERICAL_MOON_PCPF}function J(t,e,n){const r=Math.sin(t),o=Math.cos(t),s=Math.sin(e),i=Math.cos(e),a=n;return a[0]=-r,a[4]=-s*o,a[8]=i*o,a[12]=0,a[1]=o,a[5]=-s*r,a[9]=i*r,a[13]=0,a[2]=0,a[6]=i,a[10]=s,a[14]=0,a[3]=0,a[7]=0,a[11]=0,a[15]=1,a}function V(t,e){return t?e.spatialReference===t?e.spatialReferenceId:(e.spatialReference=t,"metersPerUnit"in e&&(e.metersPerUnit=(0,a.c9)(t,1)),t.wkt===P.kU.wkt?e.spatialReferenceId=k.SPHERICAL_ECEF:(0,T.oR)(t)?e.spatialReferenceId=k.WGS84:(0,T.sS)(t)?e.spatialReferenceId=k.WEB_MERCATOR:(0,T.QM)(t)?e.spatialReferenceId=k.PLATE_CARREE:t.wkt===P.pn.wkt?e.spatialReferenceId=k.WGS84_ECEF:t.wkid===g.W.CGCS2000?e.spatialReferenceId=k.CGCS2000:t.wkt===P.JL.wkt?e.spatialReferenceId=k.SPHERICAL_MARS_PCPF:t.wkt===P.mM.wkt?e.spatialReferenceId=k.SPHERICAL_MOON_PCPF:(0,T.BZ)(t)?e.spatialReferenceId=k.GCSMARS2000:(0,T.V2)(t)?e.spatialReferenceId=k.GCSMOON2000:e.spatialReferenceId=k.UNKNOWN):k.UNKNOWN}function Q(t,e,n,r){t!==n&&(n[r++]=t[e++],n[r++]=t[e++],n[r]=t[e])}function $(t,e,n,r){n[r++]=Ot*(t[e++]/h.sv.radius),n[r++]=Ot*(Math.PI/2-2*Math.atan(Math.exp(-t[e++]/h.sv.radius))),n[r]=t[e]}function tt(t,e,n,r){!function(t,e,n,r,s){const i=.4999999*Math.PI,a=(0,o.uZ)(At*t[e+1],-i,i),u=Math.sin(a);n[r++]=At*t[e]*s.radius,n[r++]=s.halfSemiMajorAxis*Math.log((1+u)/(1-u)),n[r]=t[e+2]}(t,e,n,r,h.sv)}!function(t){t[t.UNKNOWN=0]="UNKNOWN",t[t.SPHERICAL_ECEF=1]="SPHERICAL_ECEF",t[t.WGS84=2]="WGS84",t[t.WEB_MERCATOR=3]="WEB_MERCATOR",t[t.WGS84_ECEF=4]="WGS84_ECEF",t[t.CGCS2000=5]="CGCS2000",t[t.WGS84_COMPARABLE_LON_LAT=6]="WGS84_COMPARABLE_LON_LAT",t[t.SPHERICAL_MARS_PCPF=7]="SPHERICAL_MARS_PCPF",t[t.GCSMARS2000=8]="GCSMARS2000",t[t.SPHERICAL_MOON_PCPF=9]="SPHERICAL_MOON_PCPF",t[t.GCSMOON2000=10]="GCSMOON2000",t[t.LON_LAT=11]="LON_LAT",t[t.PLATE_CARREE=12]="PLATE_CARREE"}(k||(k={}));const et=h.sv.radius*Math.PI/180,nt=180/(h.sv.radius*Math.PI);function rt(t,e,n,r){n[r]=t[e]*et,n[r+1]=t[e+1]*et,n[r+2]=t[e+2]}function ot(t,e,n,r){n[r]=t[e]*nt,n[r+1]=t[e+1]*nt,n[r+2]=t[e+2]}function st(t,e,n,r,o){const s=o+t[e+2],i=At*t[e+1],a=At*t[e],u=Math.cos(i);n[r++]=Math.cos(a)*u*s,n[r++]=Math.sin(a)*u*s,n[r]=Math.sin(i)*s}function it(t,e,n,r){st(t,e,n,r,h.Z1.radius)}function at(t,e,n,r){st(t,e,n,r,h.yr.radius)}function ut(t,e,n,r){st(t,e,n,r,h.sv.radius)}function _t(t,e,n,r,s){const i=t[e],a=t[e+1],u=t[e+2],_=Math.sqrt(i*i+a*a+u*u),l=(0,o.Kt)(u/(0===_?1:_)),c=Math.atan2(a,i);n[r++]=Ot*c,n[r++]=Ot*l,n[r]=_-s}function lt(t,e,n,r){_t(t,e,n,r,h.Z1.radius)}function ct(t,e,n,r){_t(t,e,n,r,h.yr.radius)}function Pt(t,e,n,r){_t(t,e,n,r,h.sv.radius)}function Et(t,e,n,r){!function(t,e,n,r,o){const s=At*t[e],i=At*t[e+1],a=t[e+2],u=Math.sin(i),_=Math.cos(i),l=o.radius/Math.sqrt(1-o.eccentricitySquared*u*u);n[r++]=(l+a)*_*Math.cos(s),n[r++]=(l+a)*_*Math.sin(s),n[r++]=(l*(1-o.eccentricitySquared)+a)*u}(t,e,n,r,h.sv)}function ft(t,e,n,r){const o=O,s=t[e],i=t[e+1],a=t[e+2];let u,_,l,c,P,E,f,M,S,p,C,R,A,N,T,g,d,L,G,y,m;u=Math.abs(a),_=s*s+i*i,l=Math.sqrt(_),c=_+a*a,P=Math.sqrt(c),y=Math.atan2(i,s),E=a*a/c,f=_/c,N=o.a2/P,T=o.a3-o.a4/P,f>.3?(M=u/P*(1+f*(o.a1+N+E*T)/P),G=Math.asin(M),p=M*M,S=Math.sqrt(1-p)):(S=l/P*(1-E*(o.a5-N-f*T)/P),G=Math.acos(S),p=1-S*S,M=Math.sqrt(p)),C=1-h.sv.eccentricitySquared*p,R=h.sv.radius/Math.sqrt(C),A=o.a6*R,N=l-R*S,T=u-A*M,d=S*N+M*T,g=S*T-M*N,L=g/(A/C+d),G+=L,m=d+g*L/2,a<0&&(G=-G),n[r++]=Ot*y,n[r++]=Ot*G,n[r]=m}const Mt={[k.WGS84]:{[k.CGCS2000]:null,[k.GCSMARS2000]:null,[k.GCSMOON2000]:null,[k.LON_LAT]:Q,[k.WGS84_COMPARABLE_LON_LAT]:Q,[k.SPHERICAL_ECEF]:ut,[k.SPHERICAL_MARS_PCPF]:null,[k.SPHERICAL_MOON_PCPF]:null,[k.UNKNOWN]:null,[k.WEB_MERCATOR]:tt,[k.PLATE_CARREE]:rt,[k.WGS84]:Q,[k.WGS84_ECEF]:Et},[k.CGCS2000]:{[k.CGCS2000]:Q,[k.GCSMARS2000]:null,[k.GCSMOON2000]:null,[k.LON_LAT]:Q,[k.WGS84_COMPARABLE_LON_LAT]:Q,[k.SPHERICAL_ECEF]:ut,[k.SPHERICAL_MARS_PCPF]:null,[k.SPHERICAL_MOON_PCPF]:null,[k.UNKNOWN]:null,[k.WEB_MERCATOR]:null,[k.PLATE_CARREE]:rt,[k.WGS84]:null,[k.WGS84_ECEF]:Et},[k.GCSMARS2000]:{[k.CGCS2000]:null,[k.GCSMARS2000]:Q,[k.GCSMOON2000]:null,[k.LON_LAT]:Q,[k.WGS84_COMPARABLE_LON_LAT]:null,[k.SPHERICAL_ECEF]:null,[k.SPHERICAL_MARS_PCPF]:at,[k.SPHERICAL_MOON_PCPF]:null,[k.UNKNOWN]:null,[k.WEB_MERCATOR]:null,[k.PLATE_CARREE]:null,[k.WGS84]:null,[k.WGS84_ECEF]:null},[k.GCSMOON2000]:{[k.CGCS2000]:null,[k.GCSMARS2000]:null,[k.GCSMOON2000]:Q,[k.LON_LAT]:Q,[k.WGS84_COMPARABLE_LON_LAT]:null,[k.SPHERICAL_ECEF]:null,[k.SPHERICAL_MARS_PCPF]:null,[k.SPHERICAL_MOON_PCPF]:it,[k.UNKNOWN]:null,[k.WEB_MERCATOR]:null,[k.PLATE_CARREE]:null,[k.WGS84]:null,[k.WGS84_ECEF]:null},[k.WEB_MERCATOR]:{[k.CGCS2000]:null,[k.GCSMARS2000]:null,[k.GCSMOON2000]:null,[k.LON_LAT]:$,[k.WGS84_COMPARABLE_LON_LAT]:$,[k.SPHERICAL_ECEF]:function(t,e,n,r){$(t,e,n,r),ut(n,r,n,r)},[k.SPHERICAL_MARS_PCPF]:null,[k.SPHERICAL_MOON_PCPF]:null,[k.UNKNOWN]:null,[k.WEB_MERCATOR]:Q,[k.PLATE_CARREE]:function(t,e,n,r){$(t,e,n,r),rt(n,r,n,r)},[k.WGS84]:$,[k.WGS84_ECEF]:function(t,e,n,r){$(t,e,n,r),Et(n,r,n,r)}},[k.WGS84_ECEF]:{[k.CGCS2000]:ft,[k.GCSMARS2000]:null,[k.GCSMOON2000]:null,[k.LON_LAT]:ft,[k.WGS84_COMPARABLE_LON_LAT]:ft,[k.SPHERICAL_ECEF]:function(t,e,n,r){ft(t,e,n,r),ut(n,r,n,r)},[k.SPHERICAL_MARS_PCPF]:null,[k.SPHERICAL_MOON_PCPF]:null,[k.UNKNOWN]:null,[k.WEB_MERCATOR]:function(t,e,n,r){ft(t,e,n,r),tt(n,r,n,r)},[k.PLATE_CARREE]:function(t,e,n,r){ft(t,e,n,r),rt(n,r,n,r)},[k.WGS84]:ft,[k.WGS84_ECEF]:Q},[k.SPHERICAL_ECEF]:{[k.CGCS2000]:Pt,[k.GCSMARS2000]:null,[k.GCSMOON2000]:null,[k.LON_LAT]:Pt,[k.WGS84_COMPARABLE_LON_LAT]:Pt,[k.SPHERICAL_ECEF]:Q,[k.SPHERICAL_MARS_PCPF]:null,[k.SPHERICAL_MOON_PCPF]:null,[k.UNKNOWN]:null,[k.WEB_MERCATOR]:function(t,e,n,r){Pt(t,e,n,r),tt(n,r,n,r)},[k.PLATE_CARREE]:function(t,e,n,r){Pt(t,e,n,r),rt(n,r,n,r)},[k.WGS84]:Pt,[k.WGS84_ECEF]:function(t,e,n,r){Pt(t,e,n,r),Et(n,r,n,r)}},[k.SPHERICAL_MARS_PCPF]:{[k.CGCS2000]:null,[k.GCSMARS2000]:ct,[k.GCSMOON2000]:null,[k.LON_LAT]:ct,[k.WGS84_COMPARABLE_LON_LAT]:null,[k.SPHERICAL_ECEF]:null,[k.SPHERICAL_MARS_PCPF]:Q,[k.SPHERICAL_MOON_PCPF]:null,[k.UNKNOWN]:null,[k.WEB_MERCATOR]:null,[k.PLATE_CARREE]:null,[k.WGS84]:null,[k.WGS84_ECEF]:null},[k.SPHERICAL_MOON_PCPF]:{[k.CGCS2000]:null,[k.GCSMARS2000]:null,[k.GCSMOON2000]:lt,[k.LON_LAT]:lt,[k.WGS84_COMPARABLE_LON_LAT]:null,[k.SPHERICAL_ECEF]:null,[k.SPHERICAL_MARS_PCPF]:null,[k.SPHERICAL_MOON_PCPF]:Q,[k.UNKNOWN]:null,[k.WEB_MERCATOR]:null,[k.PLATE_CARREE]:null,[k.WGS84]:null,[k.WGS84_ECEF]:null},[k.UNKNOWN]:{[k.CGCS2000]:null,[k.GCSMARS2000]:null,[k.GCSMOON2000]:null,[k.LON_LAT]:null,[k.WGS84_COMPARABLE_LON_LAT]:null,[k.SPHERICAL_ECEF]:null,[k.SPHERICAL_MARS_PCPF]:null,[k.SPHERICAL_MOON_PCPF]:null,[k.UNKNOWN]:Q,[k.WEB_MERCATOR]:null,[k.PLATE_CARREE]:null,[k.WGS84]:null,[k.WGS84_ECEF]:null},[k.LON_LAT]:{[k.CGCS2000]:Q,[k.GCSMARS2000]:Q,[k.GCSMOON2000]:Q,[k.LON_LAT]:Q,[k.WGS84_COMPARABLE_LON_LAT]:Q,[k.SPHERICAL_ECEF]:ut,[k.SPHERICAL_MARS_PCPF]:at,[k.SPHERICAL_MOON_PCPF]:it,[k.UNKNOWN]:null,[k.WEB_MERCATOR]:tt,[k.PLATE_CARREE]:rt,[k.WGS84]:Q,[k.WGS84_ECEF]:Et},[k.WGS84_COMPARABLE_LON_LAT]:{[k.CGCS2000]:null,[k.GCSMARS2000]:null,[k.GCSMOON2000]:null,[k.LON_LAT]:Q,[k.WGS84_COMPARABLE_LON_LAT]:Q,[k.SPHERICAL_ECEF]:ut,[k.SPHERICAL_MARS_PCPF]:null,[k.SPHERICAL_MOON_PCPF]:null,[k.UNKNOWN]:null,[k.WEB_MERCATOR]:null,[k.PLATE_CARREE]:rt,[k.WGS84]:Q,[k.WGS84_ECEF]:Et},[k.PLATE_CARREE]:{[k.CGCS2000]:ot,[k.GCSMARS2000]:null,[k.GCSMOON2000]:null,[k.LON_LAT]:ot,[k.WGS84_COMPARABLE_LON_LAT]:ot,[k.SPHERICAL_ECEF]:function(t,e,n,r){ot(t,e,n,r),ut(n,r,n,r)},[k.SPHERICAL_MARS_PCPF]:null,[k.SPHERICAL_MOON_PCPF]:null,[k.UNKNOWN]:null,[k.WEB_MERCATOR]:function(t,e,n,r){ot(t,e,n,r),tt(n,r,n,r)},[k.PLATE_CARREE]:Q,[k.WGS84]:ot,[k.WGS84_ECEF]:function(t,e,n,r){ot(t,e,n,r),Et(n,r,n,r)}}};function St(t,e,n=pt()){return(0,s.Wi)(t)||(0,s.Wi)(e)?null:function(t,e,n){if((0,s.Wi)(t)||(0,s.Wi)(e)||n.source.spatialReference===t&&n.dest.spatialReference===e)return n;const r=V(t,n.source),o=V(e,n.dest);return r===k.UNKNOWN&&o===k.UNKNOWN?(0,T.fS)(t,e)?n.projector=Q:n.projector=null:n.projector=Mt[r][o],n}(t,e,n).projector}function pt(){return{source:{spatialReference:null,spatialReferenceId:k.UNKNOWN,metersPerUnit:1},dest:{spatialReference:null,spatialReferenceId:k.UNKNOWN,metersPerUnit:1},projector:Q}}const Ct={spatialReference:null,spatialReferenceId:k.UNKNOWN},ht={spatialReference:null,spatialReferenceId:k.UNKNOWN},Rt=pt(),At=(pt(),(0,o.Vl)(1)),Ot=(0,o.BV)(1),Nt=(0,c.c)(),Tt=(0,c.c)(),gt=(0,c.c)(),dt=(0,c.c)();(0,c.c)()},18571:(t,e,n)=>{n.d(e,{Z:()=>s});let r=0;class o{static fromGE(t){const e=new o;return e._wkt=t.wkt,e._wkid=t.wkid,e._isInverse=t.isInverse,e}constructor(t){this.uid=r++,t?(this._wkt=null!=t.wkt?t.wkt:null,this._wkid=null!=t.wkid?t.wkid:-1,this._isInverse=null!=t.isInverse&&!0===t.isInverse):(this._wkt=null,this._wkid=-1,this._isInverse=!1)}get wkt(){return this._wkt}set wkt(t){this._wkt=t,this.uid=r++}get wkid(){return this._wkid}set wkid(t){this._wkid=t,this.uid=r++}get isInverse(){return this._isInverse}set isInverse(t){this._isInverse=t,this.uid=r++}getInverse(){const t=new o;return t._wkt=this.wkt,t._wkid=this._wkid,t._isInverse=!this.isInverse,t}}class s{static cacheKey(t,e){return[void 0!==t.wkid&&null!==t.wkid?t.wkid.toString():"-1",void 0!==t.wkt&&null!==t.wkt?t.wkt.toString():"",void 0!==e.wkid&&null!==e.wkid?e.wkid.toString():"-1",void 0!==e.wkt&&null!==e.wkt?e.wkt.toString():""].join(",")}static fromGE(t){const e=new s;let n="";for(const r of t.steps){const t=o.fromGE(r);e.steps.push(t),n+=t.uid.toString()+","}return e._cachedProjection={},e._gtlistentry=null,e._chain=n,e}constructor(t){if(this.steps=[],this._cachedProjection={},this._chain="",this._gtlistentry=null,t&&t.steps)for(const e of t.steps)e instanceof o?this.steps.push(e):this.steps.push(new o({wkid:e.wkid,wkt:e.wkt,isInverse:e.isInverse}))}getInverse(){const t=new s;t.steps=[];for(let e=this.steps.length-1;e>=0;e--){const n=this.steps[e];t.steps.push(n.getInverse())}return t}getGTListEntry(){let t="";for(const e of this.steps)t+=e.uid.toString()+",";return t!==this._chain&&(this._gtlistentry=null,this._cachedProjection={},this._chain=t),this._gtlistentry}assignCachedGe(t,e,n){this._cachedProjection[s.cacheKey(t,e)]=n}getCachedGeTransformation(t,e){let n="";for(const t of this.steps)n+=t.uid.toString()+",";n!==this._chain&&(this._gtlistentry=null,this._cachedProjection={},this._chain=n);const r=this._cachedProjection[s.cacheKey(t,e)];return void 0===r?null:r}}},69285:(t,e,n)=>{n.d(e,{k:()=>i});var r=n(70586),o=n(67900),s=n(8744);function i(t,e,n){if((0,r.Wi)(e)||(0,r.Wi)(n)||n.vcsWkid||(0,s.fS)(e,n))return null;const i=(0,o._R)(e)/(0,o._R)(n);if(1===i)return null;switch(t){case"point":case"esriGeometryPoint":return t=>function(t,e){t&&null!=t.z&&(t.z*=e)}(t,i);case"polyline":case"esriGeometryPolyline":return t=>function(t,e){if(t)for(const n of t.paths)for(const t of n)t.length>2&&(t[2]*=e)}(t,i);case"polygon":case"esriGeometryPolygon":return t=>function(t,e){if(t)for(const n of t.rings)for(const t of n)t.length>2&&(t[2]*=e)}(t,i);case"multipoint":case"esriGeometryMultipoint":return t=>function(t,e){if(t)for(const n of t.points)n.length>2&&(n[2]*=e)}(t,i);case"extent":case"esriGeometryExtent":return t=>function(t,e){t&&null!=t.zmin&&null!=t.zmax&&(t.zmin*=e,t.zmax*=e)}(t,i);default:return null}}}}]);