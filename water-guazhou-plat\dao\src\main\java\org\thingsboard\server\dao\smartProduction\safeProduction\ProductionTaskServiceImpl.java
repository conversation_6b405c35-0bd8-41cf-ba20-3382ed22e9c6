package org.thingsboard.server.dao.smartProduction.safeProduction;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.request.SafeProductionRequest;
import org.thingsboard.server.dao.model.sql.smartProduction.safeProduction.ProductionTask;
import org.thingsboard.server.dao.orderWork.NewlyWorkOrderService;
import org.thingsboard.server.dao.sql.smartProduction.safeProduction.ProductionTaskMapper;
import org.thingsboard.server.dao.util.RedisUtil;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class ProductionTaskServiceImpl implements ProductionTaskService {

    @Autowired
    private ProductionTaskMapper productionTaskMapper;

    @Autowired
    private NewlyWorkOrderService workOrderService;

    @Override
    public ProductionTask save(ProductionTask productionTask) {
        if (StringUtils.isBlank(productionTask.getId())) {
            productionTask.setCode(RedisUtil.nextId(DataConstants.REDIS_KEY.PRODUCTION_TASK, ""));
            productionTask.setCreateTime(new Date());
            productionTask.setStatus("0");
            productionTaskMapper.insert(productionTask);
        } else {
            productionTaskMapper.updateById(productionTask);
        }
        return productionTask;
    }

    @Override
    public PageData<ProductionTask> findList(SafeProductionRequest request) {
        IPage<ProductionTask> ipage = new Page<>(request.getPage(), request.getSize());
        ipage = productionTaskMapper.findList(ipage, request);
        return new PageData<>(ipage.getTotal(), ipage.getRecords());
    }

    @Override
    public void delete(List<String> id) {
        productionTaskMapper.deleteBatchIds(id);
    }

    @Override
    public ProductionTask review(ProductionTask productionTask) {
        productionTaskMapper.updateById(productionTask);
        return productionTask;
    }

    @Override
    public IstarResponse process(ProductionTask productionTask, String userId) {
        ProductionTask productionTask1 = productionTaskMapper.selectById(productionTask.getId());

        if (productionTask1 == null) {
            return IstarResponse.error("任务不存在");
        }
        if (!productionTask1.getExecUser().equals(userId)) {
            return IstarResponse.error("您没有处理权限");
        }
        productionTask.setRealEndTime(new Date());
        productionTask.setStatus("4");
        productionTaskMapper.updateById(productionTask);
        return IstarResponse.ok();
    }

    @Override
    public IstarResponse receive(String id, String userId) {
        ProductionTask productionTask = productionTaskMapper.selectById(id);

        if (productionTask == null) {
            return IstarResponse.error("任务不存在");
        }
        if (!productionTask.getExecUser().equals(userId)) {
            return IstarResponse.error("您没有处理权限");
        }
        productionTask.setRealStartTime(new Date());
        productionTask.setStatus("3");

        productionTaskMapper.updateById(productionTask);
        return IstarResponse.ok();
    }
}

