import{_ as Z}from"./Panel-DyoxrWMd.js";import{_ as j,a as J}from"./PipeLength.vue_vue_type_script_setup_true_lang-BZfUsvDf.js";import{a as K,b as Q,c as Y,d as ee,e as te,_ as oe}from"./arcWidgetButton-0glIxrt7.js";import{d as re,c as ie,cN as ae,r as se,g as A,n as ne,p as P,q as C,i as m,cs as pe,bo as le,bR as me,F as ce,h as R,an as q,aB as ue,X as de,C as we}from"./index-r0dFAfgr.js";import{u as ve}from"./useWidgets-BRE-VQU9.js";import{G as fe}from"./AnimatedLinesLayer-B2VbV4jv.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./geometryEngineBase-BhsKaODW.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./project-DUuzYgGl.js";import{GetFieldUniqueValue as L}from"./fieldconfig-Bk3o1wi7.js";/* empty css                                                                      */import{u as ge}from"./useBasemapGallary-Bk4zNUJL.js";import"./index-0NlGN6gS.js";import{GetFieldValueByGeoserver as N}from"./wfsUtils-DXofo3da.js";import{u as V}from"./usePipelineGroup-Ba1pmWz_.js";import"./v4-SoommWqA.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./widget-BcWKanF2.js";import"./LayerHelper-Cn-iiqxI.js";import"./pipe-nogVzCHG.js";import"./TileLayer-B5vQ99gG.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./StatisticsHelper-D-s_6AyQ.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./GroupLayer-DhhN3FyN.js";import"./lazyLayerLoader-DbM9sT1W.js";import"./WFSLayer-1TtJp3nx.js";import"./clientSideDefaults-VQhQaYxh.js";import"./QueryEngineCapabilities-Dk3NJwmm.js";import"./wfsUtils-Du031XaC.js";import"./geojson-MBFu2-HZ.js";import"./xmlUtils-CtUoQO7q.js";const ye={class:"tools-temp-wrapper"},_e={id:"overviewmap",class:"overviewmap"},he=re({__name:"ArcBRTools",props:{view:{},basemapChange:{type:Function}},setup(W,{expose:k}){const b=ie(),{proxy:T}=ae(),{createServiceLayer:E}=fe(),e=W,s=se({showOverViewMap:!1,toolPanelTitle:"",toolPanelOperate:""}),{addCustomWidget:M}=ve(),F=K(),x=Q(),D=Y(),$=ge(e.basemapChange),H=ee(),z=te(),B=(n,o,l,h)=>{var c,u;l?(u=b.value)==null||u.Close():(s.toolPanelOperate=n,s.toolPanelTitle=o,(c=b.value)==null||c.Open(),G.map(p=>{var S;p!==s.toolPanelOperate&&((S=T.$refs["refArcWidgetButton-tool-"+p])==null||S.toggle(!0))}))},G=["areameasure","pipelength"],U=()=>{G.map(n=>{var o;(o=T.$refs["refArcWidgetButton-tool-"+n])==null||o.toggle(!0)})};return k({init:async(n,o)=>{var c,u,p,S,O;if(!e.view)return;H.init(e.view,"bottom-right"),M(e.view,"tool-pipelength","bottom-right"),M(e.view,"tool-areameasure","bottom-right"),F.init(e.view,"","bottom-right"),z.init(e.view,"bottom-right"),$.init(e.view),D.init(e.view,"overviewmap"),M(e.view,"gis-overview","bottom-right");const l=e.view&&x.init(e.view);if((n==null?void 0:n.hidePipe)!==!0){let r;GIS_SERVER_SWITCH?r=E(e.view,"WMSLayer",{id:"pipelayer",title:"给水管网",url:"/geoserver/guazhou/wms",sublayers:[{name:"管线",type:"MultiLineString",spatialReferences:"EPSG:3857"},{name:"测点",type:"Point",spatialReferences:"EPSG:3857"}],visible:!0,version:"1.1.0",format:"png",transparent:!1}):r=E(e.view,"MapImage",{id:"pipelayer",title:"给水管网",url:window.SITE_CONFIG.GIS_CONFIG.gisService+window.SITE_CONFIG.GIS_CONFIG.gisPipeDynamicService}),e.view.map.add(r),await(r==null?void 0:r.when());const d=V("管线材质分组"),w=V("管线口径分组"),X=r==null?void 0:r.allSublayers.map(i=>i.id).toArray(),I=(p=(((u=(c=(await de(X)).data)==null?void 0:c.result)==null?void 0:u.rows)||[]).find(i=>i.geometrytype==="esriGeometryPolyline"))==null?void 0:p.layerid;GIS_SERVER_SWITCH?(N({layerName:"给水管线",fiedName:"材质"}).then(i=>{let v=i.data;const a=new Set;v.features.forEach(t=>{a.add(t.properties.材质)}),d.init(e.view),Array.from(a).map((t,g)=>{const y="材质 = '"+t+"'",_=d.genColor(g);d.addSubLayerByGeoServer(t,v,_,y)})}),N({layerName:"给水管线",fiedName:"材质"}).then(i=>{let v=i.data;const a=new Set;v.features.forEach(t=>{a.add(t.properties.DIAMETER)}),w.init(e.view),Array.from(a).map((t,g)=>{const y="DIAMETER = "+t,_=w.genColor(g);w.addSubLayerByGeoServer(t,v,_,y)})})):(L({layerid:I,field_name:"MATERIAL"}).then(i=>{var a,f;d.init(e.view),(((f=(a=i.data)==null?void 0:a.result)==null?void 0:f.rows)||[]).map((t,g)=>{const y=d.genSql("MATERIAL",t),_=d.genColor(g);d.addSubLayer(e.view,I,t,y,_)})}),L({layerid:I,field_name:"DIAMETER"}).then(i=>{var a,f;w.init(e.view),(((f=(a=i.data)==null?void 0:a.result)==null?void 0:f.rows)||[]).map((t,g)=>{const y=w.genSql("DIAMETER",t),_=w.genColor(g);w.addSubLayer(e.view,I,"DN"+t,y,_)})}))}const h=(O=(S=e.view)==null?void 0:S.extent)==null?void 0:O.clone();return h&&l&&(l.goToOverride=r=>{r.goTo(h)}),o&&o(),{homeButton:l}}}),(n,o)=>{const l=oe,h=j,c=J,u=Z;return A(),ne(ue,null,[P("div",ye,[C(l,{id:"tool-pipelength",ref:"refArcWidgetButton-tool-pipelength",icon:"mdi:ruler",title:"管线长度",onClick:o[0]||(o[0]=p=>B("pipelength","管线长度测量",p))},null,512),C(l,{id:"tool-areameasure",ref:"refArcWidgetButton-tool-areameasure",icon:"gis:measure-area-alt",title:"面积测量",onClick:o[1]||(o[1]=p=>B("areameasure","面积测量",p))},null,512),P("div",{id:"gis-overview",title:"鹰眼图",class:"esri-widget esri-expand esri-component esri-widget--button custom-toolbar",onClick:o[2]||(o[2]=()=>m(s).showOverViewMap=!m(s).showOverViewMap)},[C(m(pe),{class:"tool-icon",icon:m(s).showOverViewMap===!1?"gis:earth":"ep:d-arrow-right"},null,8,["icon"])])]),le(P("div",_e,null,512),[[me,m(s).showOverViewMap]]),C(u,{ref_key:"refToolPanel",ref:b,"custom-class":"tool-panel",telport:"#arcmap-wrapper",title:m(s).toolPanelTitle,"destroy-by-close":!0,"before-close":()=>U()},{default:ce(()=>[m(s).toolPanelOperate==="pipelength"?(A(),R(h,{key:0,view:n.view},null,8,["view"])):q("",!0),m(s).toolPanelOperate==="areameasure"?(A(),R(c,{key:1,view:n.view},null,8,["view"])):q("",!0)]),_:1},8,["title","before-close"])],64)}}}),to=we(he,[["__scopeId","data-v-880a9803"]]);export{to as default};
