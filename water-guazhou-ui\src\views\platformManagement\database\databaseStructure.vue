<template>
  <div class="wrapper">
    <CardSearch
      ref="refSearch"
      :config="SearchConfig"
    ></CardSearch>
    <CardTable
      class="card-table"
      :config="TableConfig"
    ></CardTable>
    <DialogForm
      ref="refDialogForm"
      :config="DialogFormConfig"
    ></DialogForm>
  </div>
</template>

<script setup>
import { onMounted, reactive, ref } from 'vue'
import { 
  getBaseDatabaseStructureList,
  addBaseDatabaseStructure,
  editBaseDatabaseStructure,
  deleteBaseDatabaseStructure,
  getBaseDatabaseStructureDetail 
} from '@/api/platformManagement/baseDatabaseStructure'
import { SLConfirm, SLMessage } from '@/utils/Message'
import dayjs from 'dayjs'

const refSearch = ref()
const refDialogForm = ref()

const SearchConfig = reactive({
  labelWidth: '100px',
  filters: [
    { 
      type: 'input', 
      label: '对象名称', 
      field: 'objectName', 
      placeholder: '请输入操作对象名称',
      onChange: () => refreshData() 
    },
    {
      type: 'select',
      label: '执行状态',
      field: 'status',
      options: [
        { label: '全部', value: '' },
        { label: '成功', value: 'SUCCESS' },
        { label: '失败', value: 'FAILED' },
        { label: '执行中', value: 'PROCESSING' }
      ],
      onChange: () => refreshData()
    },
    {
      type: 'btn-group',
      btns: [
        { type: 'primary', perm: true, text: '查询', click: () => refreshData() },
        { perm: true, type: 'primary', text: '新增', click: () => handleAdd() },
        { perm: true, type: 'danger', text: '批量删除', click: () => handleDelete() }
      ]
    }
  ],
  defaultParams: {}
})

const TableConfig = reactive({
  columns: [
    { label: '版本号', prop: 'version' },
    { 
      label: '变更类型', 
      prop: 'changeType',
      formatter: (row) => {
        const types = {
          CREATE: '新建',
          ALTER: '修改',
          DROP: '删除'
        }
        return types[row.changeType] || row.changeType
      }
    },
    { label: '操作对象名', prop: 'objectName' },
    { 
      label: '对象类型', 
      prop: 'objectType',
      formatter: (row) => {
        const types = {
          TABLE: '数据表',
          VIEW: '视图',
          PROCEDURE: '存储过程'
        }
        return types[row.objectType] || row.objectType
      }
    },
    { label: '变更描述', prop: 'changeDescription' },
    { label: '执行状态', prop: 'status', formatter: (row) => {
      const statusMap = {
        SUCCESS: '成功',
        FAILED: '失败',
        PROCESSING: '执行中'
      }
      return statusMap[row.status] || row.status
    }, tagType: (row) => {
      return {
        SUCCESS: 'success',
        FAILED: 'danger',
        PROCESSING: 'warning'
      }[row.status]
    }},
    { 
      label: '执行时间', 
      prop: 'executedAt',
      formatter: (row) => dayjs(row.executedAt).format('YYYY-MM-DD HH:mm:ss') 
    },
    { label: '执行者', prop: 'executedBy' }
  ],
  dataList: [],
  operations: [
    {
      perm: true,
      type: 'primary',
      isTextBtn: true,
      text: '查看详情',
      click: (row) => handleDetail(row)
    },
    {
      perm: true,
      type: 'primary',
      isTextBtn: true,
      text: '编辑',
      click: (row) => handleAdd(row)
    },
    {
      perm: true,
      type: 'danger',
      isTextBtn: true,
      text: '删除',
      click: (row) => handleDelete(row)
    }
  ],
  pagination: {
    total: 0,
    page: 1,
    align: 'right',
    limit: 20,
    handlePage: (page) => {
      TableConfig.pagination.page = page
      refreshData()
    },
    handleSize: (size) => {
      TableConfig.pagination.limit = size
      refreshData()
    }
  },
  handleSelectChange: (rows) => {
    TableConfig.selectList = rows || []
  }
})

const DialogFormConfig = reactive({
  title: 'SQL变更管理',
  group: [
    {
      fields: [
        {
          type: 'input',
          label: '版本号',
          field: 'version',
          rules: [{ required: true, message: '请输入版本号' }]
        },
        {
          type: 'select',
          label: '变更类型',
          field: 'changeType',
          options: [
            { label: '新建', value: 'CREATE' },
            { label: '修改', value: 'ALTER' },
            { label: '删除', value: 'DROP' }
          ],
          rules: [{ required: true, message: '请选择变更类型' }]
        },
        {
          type: 'select',
          label: '对象类型',
          field: 'objectType',
          options: [
            { label: '数据表', value: 'TABLE' },
            { label: '视图', value: 'VIEW' },
            { label: '存储过程', value: 'PROCEDURE' }
          ],
          rules: [{ required: true, message: '请选择对象类型' }]
        },
        {
          type: 'input',
          label: '操作对象名',
          field: 'objectName',
          rules: [{ required: true, message: '请输入操作对象名称' }]
        },
        {
          type: 'textarea',
          label: '变更描述',
          field: 'changeDescription',
          rows: 3,
          rules: [{ required: true, message: '请输入变更描述' }]
        },
        {
          type: 'textarea',
          label: 'SQL语句',
          field: 'sqlStatement',
          rows: 6,
          rules: [{ required: true, message: '请输入SQL语句' }]
        },
        {
          type: 'select',
          label: '执行状态',
          field: 'status',
          options: [
            { label: '成功', value: 'SUCCESS' },
            { label: '失败', value: 'FAILED' },
            { label: '执行中', value: 'PROCESSING' }
          ],
          rules: [{ required: true, message: '请选择执行状态' }]
        },
        {
          type: 'input',
          label: '执行者',
          field: 'executedBy',
          rules: [{ required: true, message: '请输入执行者' }]
        },
        {
          type: 'datetime',
          label: '执行时间',
          field: 'executedAt',
          rules: [{ required: true, message: '请选择执行时间' }]
        }
      ]
    }
  ],
  labelPosition: 'top',
  defaultValue: {},
  dialogWidth: 800,
  draggable: true,
  showSubmit: true,
  showCancel: true,
  cancelText: '取消',
  submitText: '提交',
  submit: async (params) => {
    try {
      if (params.id) {
        await editBaseDatabaseStructure(params)
        SLMessage.success('修改成功')
      } else {
        await addBaseDatabaseStructure(params)
        SLMessage.success('新增成功')
      }
      refDialogForm.value?.closeDialog()
      refreshData()
    } catch (error) {
      SLMessage.error('操作失败')
    }
  }
})

const resetDialogConfig = () => {
  DialogFormConfig.group[0].fields.forEach(field => {
    field.disabled = false
    field.readonly = false
  })
  DialogFormConfig.showSubmit = true
  DialogFormConfig.showCancel = true
  DialogFormConfig.cancelText = '取消'
  DialogFormConfig.submitText = '提交'
}

const handleAdd = (row) => {
  resetDialogConfig()
  DialogFormConfig.title = row ? '编辑变更记录' : '新增变更记录'
  DialogFormConfig.defaultValue = { 
    ...(row || {}),
    executedAt: row?.executedAt ? dayjs(row.executedAt).format('YYYY-MM-DD HH:mm:ss') : ''
  }
  refDialogForm.value?.openDialog()
}

const handleDetail = async (row) => {
  try {
    const res = await getBaseDatabaseStructureDetail(row.id)
    const detailData = res.data?.data || res
    
    resetDialogConfig()
    DialogFormConfig.title = '变更记录详情'
    DialogFormConfig.defaultValue = { 
      ...detailData,
      executedAt: detailData.executedAt ? dayjs(detailData.executedAt).format('YYYY-MM-DD HH:mm:ss') : ''
    }
    DialogFormConfig.group[0].fields.forEach(field => {
      field.disabled = true
      field.readonly = true
    })
    DialogFormConfig.showSubmit = false
    DialogFormConfig.cancelText = '关闭'
    refDialogForm.value?.openDialog()
  } catch (error) {
    SLMessage.error('获取详情失败')
  }
}

const handleDelete = (row) => {
  SLConfirm('确定删除？', '删除提示')
    .then(async () => {
      const ids = row ? [row.id] : TableConfig.selectList?.map(item => item.id) || []
      if (!ids.length) {
        SLMessage.warning('请选择要删除的数据')
        return
      }
      await deleteBaseDatabaseStructure(ids)
      SLMessage.success('删除成功')
      refreshData()
    })
    .catch(() => {})
}

const refreshData = async () => {
  try {
    const res = await getBaseDatabaseStructureList({
      page: TableConfig.pagination.page,
      size: TableConfig.pagination.limit,
      ...(refSearch.value?.queryParams || {})
    })
    const responseData = res.data?.data || res
    TableConfig.dataList = responseData.records || responseData
    TableConfig.pagination.total = responseData.total || responseData.length || 0
  } catch (error) {
    SLMessage.error('数据加载失败')
  }
}

onMounted(() => {
  refreshData()
})
</script>

<style lang="scss" scoped>
.wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.card-table {
  flex: 1;
  margin-top: 16px;
}
</style>