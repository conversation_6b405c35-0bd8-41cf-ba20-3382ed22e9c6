package org.thingsboard.server.dao.sql.smartProduction.dispatch;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.thingsboard.server.dao.model.sql.smartProduction.dispatch.EmergencyVehicle;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch.EmergencyVehiclePageRequest;

@Mapper
public interface EmergencyVehicleMapper extends BaseMapper<EmergencyVehicle> {
    IPage<EmergencyVehicle> findByPage(EmergencyVehiclePageRequest request);


    boolean update(EmergencyVehicle entity);

}
