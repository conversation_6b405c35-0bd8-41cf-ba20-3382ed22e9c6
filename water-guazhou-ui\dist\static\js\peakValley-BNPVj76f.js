import{d as E,M as q,a6 as z,c as f,r as x,am as B,bF as c,s as L,o as F,ay as P,bo as R,i as s,g as k,n as C,q as n,F as u,cs as O,an as N,j,bB as M,dF as G,dA as W,aq as U,br as $,al as J,aj as K,C as Q}from"./index-r0dFAfgr.js";import{_ as X}from"./index-C9hz-UZb.js";import{_ as Y}from"./Search-NSrhrIa_.js";import{l as Z}from"./echart-DxEZmJvB.js";import{b as aa}from"./flowMonitoring-DtJlPj0G.js";const ea={class:"view"},ta={key:0,style:{height:"100%"}},oa=E({__name:"peakValley",props:{stationName:{},stationId:{}},setup(V){const{$messageWarning:w}=q(),T=z(),h=f(!1),D=f(),g=f(),d=V,I=f(),b=f(),o=x({chartOption:null,tableDataList:[],activeName:"echarts"});B(()=>d.stationId,()=>{console.log(d.stationId),v()});const S=x({defaultParams:{date:[c().add(-7,"day").format(),c().format()]},filters:[{type:"daterange",label:"日期",field:"date"},{type:"btn-group",btns:[{perm:!0,text:"查询",svgIcon:L(J),click:()=>{d.stationId?v():w("请选择监测点")}},{perm:!0,text:"导出",type:"warning",svgIcon:L(K),hide:()=>o.activeName!=="list",click:()=>{var a;d.stationId?(a=D.value)==null||a.exportTable():w("请选择监测点")}}]}]}),y=x({loading:!1,dataList:[],columns:[{prop:"ts",label:"时间",width:"130px"},{prop:"max",label:"最大流量",unit:"(m³)"},{prop:"maxTs",label:"最大流量读取时间",formatter:(a,t)=>t?c(t).format("HH:00"):"-"},{prop:"min",label:"最小流量",unit:"(m³)"},{prop:"minTs",label:"最小流量读取时间",formatter:(a,t)=>t?c(t).format("HH:00"):"-"},{prop:"avg",label:"平均流量",unit:"(m³)"}],operations:[],showSummary:!1,operationWidth:"150px",pagination:{hide:!0}}),H=()=>{var r,i;(r=g.value)==null||r.clear();const a=Z();a.series=[],y.columns.filter(e=>e.prop!=="ts").map(e=>{var l;const p=(l=o.tableDataList)==null?void 0:l.map(_=>e.prop.indexOf("Ts")!==-1?c(_[e.prop]).format("HH:00"):_[e.prop]),m={name:e.label,smooth:!0,data:p,type:"line",markPoint:{data:[{type:"max",name:"最大值"},{type:"min",name:"最小值"}]},markLine:{data:[{type:"average",name:"平均值"}]}};a.series.push(m),a.name="流量数据(m³)"}),console.log("state.tableDataList",o.tableDataList),a.yAxis[0].name="时间",a.yAxis[1].name="流量(m³)",a.xAxis.data=(i=o.tableDataList)==null?void 0:i.map(e=>e.ts),M(()=>{b.value&&T.listenTo(b.value,()=>{var e;o.chartOption=a,(e=g.value)==null||e.resize()})})},v=async()=>{var m,l;h.value=!0;const a=((m=I.value)==null?void 0:m.queryParams)||{},[t,r]=a.date,i={start:t?c(t).startOf("day").valueOf():null,end:r?c(r).endOf("day").valueOf():null,stationId:d.stationId},p=(l=(await aa(i)).data)==null?void 0:l.data;y.dataList=p,o.tableDataList=p,H(),h.value=!1};return F(()=>{d.stationId&&v()}),(a,t)=>{const r=Y,i=G,e=W,p=U,m=P("VChart"),l=X,_=$;return R((k(),C("div",ea,[n(r,{ref_key:"cardSearch",ref:I,config:s(S)},null,8,["config"]),n(l,{class:"card-table",title:" "},{right:u(()=>[n(e,{modelValue:s(o).activeName,"onUpdate:modelValue":t[0]||(t[0]=A=>s(o).activeName=A)},{default:u(()=>[n(i,{label:"echarts"},{default:u(()=>[n(s(O),{style:{"margin-right":"1px","font-size":"16px"},icon:"clarity:line-chart-line"})]),_:1}),n(i,{label:"list"},{default:u(()=>[n(s(O),{style:{"margin-right":"1px","font-size":"16px"},icon:"material-symbols:table"})]),_:1})]),_:1},8,["modelValue"])]),default:u(()=>[s(o).activeName==="list"?(k(),C("div",ta,[n(p,{ref_key:"refCard",ref:D,config:s(y)},null,8,["config"])])):N("",!0),s(o).activeName==="echarts"?(k(),C("div",{key:1,ref_key:"agriEcoDev",ref:b,class:"card-ehcarts"},[n(m,{ref_key:"refChart",ref:g,theme:s(j)().isDark?"dark":"",class:"card-ehcarts",option:s(o).chartOption},null,8,["theme","option"])],512)):N("",!0)]),_:1})])),[[_,s(h)]])}}}),ca=Q(oa,[["__scopeId","data-v-7df586d5"]]);export{ca as default};
