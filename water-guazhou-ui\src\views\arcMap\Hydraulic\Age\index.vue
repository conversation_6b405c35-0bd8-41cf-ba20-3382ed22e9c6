<!--
  水龄
 -->
<template>
  <RightDrawerMap :title="'水齡分布'">
    <SliderBar></SliderBar>
    <el-divider></el-divider>
    <HydraulicPanel
      :header="['水齡分组(h)', '图层控制', '定位']"
      :legends="[
        { label: '>45h', value: 14, checked: true },
        { label: '40~45h', value: 124, checked: true },
        { label: '35~40h', value: 441, checked: true },
        { label: '30~35h', value: 231, checked: true },
        { label: '25~30h', value: 321, checked: true },
        { label: '20~25h', value: 1321, checked: true },
        { label: '0~20h', value: 4121, checked: true }
      ]"
      :unit="'h'"
    ></HydraulicPanel>
  </RightDrawerMap>
</template>
<script lang="ts" setup>
import RightDrawerMap from '../../components/common/RightDrawerMap.vue'
import HydraulicPanel from '../components/HydraulicPanel.vue'
import SliderBar from '../components/SliderBar.vue'
</script>
<style lang="scss" scoped></style>
