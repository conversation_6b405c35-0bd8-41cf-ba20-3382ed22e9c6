import { IFormItemRule } from '@/common/types/element-plus'
import { ISLOperation } from '../SLCardSearch/type'
import { ISLPaginationConfig } from '../SLPagination/type'
import { ISLTableConfig } from '../SLTable/type'

export interface ISLFormItemBase {
  type: string
  field: string
  label?: string
  hidden?: boolean
  style?: string
  placeholder?: string
  readonly?: boolean
  disabled?: boolean
  rules?: IFormItemRule
  aInfo?: boolean
  itemContainerStyle?: string
  xs?: number
  sm?: number
  md?: number
  lg?: number
  xl?: number
  size?: 'small' | 'default' | 'large'
  /**
   * 单位
   *
   * 当设置此项时，会附加在表单元素的尾端
   */
  unit?: string
  /**
   * 格式化表单值,仅当类型为text时有用
   */
  formatter?: (...args: any) => any
  /**
   *
   * 为表单元素指定列宽
   *
   * 值为每行容纳几个元素
   */
  cols?: number
  onChange?: (...args: any[]) => void
}

interface ISLFormInput extends ISLFormItemBase {
  type: 'input'
  trim?: boolean
  onKeyUp?: (...args: any[]) => void
}
interface ISLFormInputNumber extends ISLFormItemBase {
  type: 'input-number'
  onKeyUp?: (...args: any[]) => void
}
interface ISLFormText extends ISLFormItemBase {
  type: 'text'
}
interface ISLFormPassword extends ISLFormItemBase {
  type: 'password'
}
interface ISLFormTextArea extends ISLFormItemBase {
  type: 'textarea'
  minRow?: number
  maxRow?: number
}
interface ISLFormNumber extends ISLFormItemBase {
  type: 'number'
  min?: number
  max?: number
  controlPosition?: 'right'
}
export interface ISLFormSelect extends ISLFormItemBase {
  type: 'select'
  multiple?: boolean
  allowCreate?: boolean
  options?: NormalOption[]
  remote?: boolean
  returnType?: 'str' | 'arr'
  remoteMethod?: (val: any) => NormalOption[]
}
interface ICascaderProps {
  checkStrictly?: boolean
  label?: string
  value?: string
  children?: string
}
export interface ISLFormCascader extends ISLFormItemBase {
  type: 'cascader'
  props?: ICascaderProps
  multiple?: boolean
  options?: NormalOption[]
}
export interface ISLFormCheckbox extends ISLFormItemBase {
  type: 'checkbox'
  options?: NormalOption[]
}
interface ISLFormRadio extends ISLFormItemBase {
  type: 'radio' | 'radio-button'
  options?: NormalOption[]
}

interface ISLFormSwitch extends ISLFormItemBase {
  type: 'switch'
  activeText?: string
  inActiveText?: string
  activeColor?: string
  inActiveColor?: string
  activeValue?: string | boolean
  inActiveValue?: string | boolean
}
export interface ISLFormDate extends ISLFormItemBase {
  type: 'date' | 'datetime' | 'month' | 'year'
  min?: string
  max?: string
  format?: string
  textFormat?: string
}
export interface ISLFormDateRange extends ISLFormItemBase {
  type: 'daterange' | 'datetimerange'
  min?: string
  max?: string
  format?: string
  rangeSeparator?: string
}
interface ISLFormTime extends ISLFormItemBase {
  type: 'time'
  min?: string
  max?: string
  format?: string
  isRange?: boolean
}
interface ISLFormEditor extends ISLFormItemBase {
  type: 'editor'
}
interface ISLFormUploader extends ISLFormItemBase {
  type: 'uploader'
  desc?: string
  multiple?: boolean
  maxFile?: number
  maxSize?: number
  autoUpload?: boolean
  fileTypes?: string[]
  downLoad?: boolean
  uploadBefore?: (...args: any[]) => void
  uploadAfter?: (...args: any[]) => void
  removeBefore?: (...args: any[]) => void
  fileClick?: (...args: any[]) => void
}
interface ISLFormImgUploader extends ISLFormItemBase {
  type: 'image'
  url?: string
  limit?: number
  multiple?: boolean
  returnType?: 'arrStr' | 'comma'
}
interface ISLFormFileUploader extends ISLFormItemBase {
  type: 'file'
  url?: string
  limit?: number
  multiple?: boolean
  returnType?: 'arrStr' | 'comma'
}
interface ISLFormAMap extends ISLFormItemBase {
  type: 'amap'
  required?: boolean
  resultType?: 'str' | 'arrStr'
}
export interface ISLFormUser extends ISLFormItemBase {
  type: 'user'
  height?: string
  width?: string
  /**
   * 多选返回的是用逗号分隔的字符串
   */
  multiple?: boolean
  checkUsers?: (users: any) => void
}

export interface ISLTagGroup extends ISLFormItemBase {
  type: 'tags'
  cancelable?: boolean
  options: TagNormalOption[]
  width?: string | number
  height?: string | number
  color?: string
  border?: string
  className?: string
  /** 激活时的背景色 */
  activeColor?: string
  /** 激活时的文本颜色 */
  activeTextColor?: string
  btns?: IButton[]
}
/**
 * 表格
 * 暂不支持浮动布局，只能是100% 宽度
 *
 * 高度只能通过config.height来控制，
 *
 * 当只想显示一个table的时候请直接使用SLTable
 */
export interface ISLFormTable extends Partial<ISLFormItemBase> {
  type: 'table'
  hide?: boolean
  config: ISLTableConfig
}

export interface IDialogFormTableColumn {
  /**
   * 表头label
   */
  label: string
  /**
   * 字段
   */
  prop: string
  /**
   * 单位
   */
  unit?: string
  /**
   * 表格项图标
   */
  icon?: string
  /**
   * 支持string|function
   */
  iconColor?: any
  /**
   * 支持number 最小支持12
   */
  iconSize?: number
  /**
   * 表格项 图片
   */
  image?: boolean
  /**
   * 表格列 是否必填，需要配全formItemConfig的rules来正确的进行表单验证
   */
  required?: boolean
  /**
   * 列宽
   */
  width?: string | number
  /**
   * 表头的提示信息
   */
  tooltip?: string
  /**
   * 表头的提示图标
   */
  toolTipIcon?: string
  /**
   * 列的布局
   */
  align?: 'left' | 'center' | 'right'
  /**
   * 最小宽度
   */
  minWidth?: string | number
  /**
   * 是否是固定列/固定位置
   */
  fixed?: 'right' | 'left' | boolean
  /**
   * 当配置此项时为表单元素显示，表单元素的配置
   */
  formItemConfig?: ISLFormItem
  /**
   * 单元格的样式
   */
  cellStyle?: Record<string, string> | ((row: any, val: any, field: string) => any)
  /**
   * 格式化文本内容
   */
  formatter?: (row: any, value: any, field: string) => any
}
export interface IDialogFormTable extends ISLFormItemBase {
  type: 'formtable'
  /**
   * 表格标题
   */
  title?: any
  /**
   * 标题右侧按钮
   */
  headerBtns?: ISLOperation[]
  /**
   * 标题右侧的搜索项
   * @deprecated 暂时还未开发完成
   */
  headerQuery?: ISLFormItem[]
  /**
   * 标题右侧搜索项的默认值
   */
  headerQueryDefault?: any
  /**
   * 表格搜索
   */
  handleQuery?: (queryParams: any) => any
  /**
   * 表格列配置
   */
  columns: IDialogFormTableColumn[]
  /**
   * 表格高度
   * @deprecated 表单中的表格都不再对高度进行限制
   */
  height?: string | number
  operations?: ISLOperation[]
  operationFixed?: boolean | string
  operationWidth?: string | number
  pagination?: ISLPaginationConfig
}
export interface ISLFormInlineBtn extends ISLFormItemBase {
  type: 'btn'
  config: ISLOperation
}
export type ISLFormItem =
  | ISLFormSelect
  | ISLFormInput
  | ISLFormText
  | ISLFormPassword
  | ISLFormTextArea
  | ISLFormNumber
  | ISLFormEditor
  | ISLFormUploader
  | ISLFormCascader
  | ISLFormCheckbox
  | ISLFormRadio
  | ISLFormSwitch
  | ISLFormDate
  | ISLFormDateRange
  | ISLFormTime
  | ISLFormImgUploader
  | ISLFormAMap
  | ISLFormTable
  | IDialogFormTable
  | ISLFormUser
  | ISLFormInputNumber
  | ISLFormFileUploader
  | ISLTagGroup
  | ISLFormInlineBtn
export type IBtnType =
  | ''
  | 'default'
  | 'text'
  | 'success'
  | 'warning'
  | 'info'
  | 'primary'
  | 'danger'
export type ISLFormBtn = {
  text: string
  disabled?: boolean
  icon?: string
  click?: (...args: any[]) => void
  style?: string
  type?: IBtnType
  class?: string
}
