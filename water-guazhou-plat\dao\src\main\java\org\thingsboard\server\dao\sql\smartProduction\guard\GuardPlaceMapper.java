package org.thingsboard.server.dao.sql.smartProduction.guard;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.thingsboard.server.dao.model.sql.smartProduction.guard.GuardPlace;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.guard.GuardPlacePageRequest;

@Mapper
public interface GuardPlaceMapper extends BaseMapper<GuardPlace> {
    IPage<GuardPlace> findByPage(GuardPlacePageRequest request);

    boolean updateFully(GuardPlace entity);

}
