import{d as F,r as E,c as L,W as g,o as M,am as x,j as D,g as G,n as J,q as C,i as w,t as B,_ as R,C as q}from"./index-r0dFAfgr.js";import{C as Q}from"./index-CcDafpIP.js";import{r as S,o as v}from"./chart-wy3NEK2T.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import{a as m,c as U}from"./LayerHelper-Cn-iiqxI.js";import{b as H,i as $}from"./QueryHelper-ILO3qZqg.js";import{P as h}from"./pipe-nogVzCHG.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./project-DUuzYgGl.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./executeForIds-BLdIsxvI.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";const z=F({__name:"valve",props:{view:{},menu:{}},emits:["highlightMark","addMarks"],setup(k,{emit:b}){const c=b,i=k,V=E({pipeLayerOption:[]}),A=L(),u=L([{label:"0 个",value:"阀门总数"}]),O=E({height:250,dataList:[],pagination:{hide:!0},columns:[{label:"名称",prop:"LANEWAY",sortable:!0,formatter:t=>t.LANEWAY||"未知"},{label:"数量",prop:"ObjectId"}],handleRowClick:t=>{const e=t.LANEWAY?" LANEWAY like '%"+t.LANEWAY+"%' ":" LANEWAY is null ";c("highlightMark",i.menu,e,t.LANEWAY)}}),f=E({group:[{id:"valvestatus",fieldset:{type:"underline",desc:"阀门状态占比图"},fields:[{type:"vchart",option:S(),style:{width:"100%",height:"150px"},itemContainerStyle:{marginBottom:0},handleClick:t=>{c("highlightMark",i.menu,t.data.name==="未知"?"":" OPENCLOSE="+(t.data.name||"")+" ",t.data.nameAlias)}}]},{id:"diameter",fieldset:{type:"underline",desc:"按口径统计阀门数量"},fields:[{type:"vchart",option:v(),style:{width:"100%",height:"150px"},itemContainerStyle:{marginBottom:0},handleClick:t=>{c("highlightMark",i.menu,t.data.name==="未知"?"":" DIAMETER="+(t.data.name||"")+" ",t.data.nameAlias)}}]},{id:"VALVETYPE",fieldset:{type:"underline",desc:"按类型统计阀门数量"},fields:[{type:"vchart",option:v(),style:{width:"100%",height:"150px"},handleClick:t=>{c("highlightMark",i.menu,t.data.name==="未知"?"":" SUBTYPE = '"+(t.data.name||"")+"' ",t.data.nameAlias)}}]},{id:"LANEWAY",fieldset:{type:"underline",desc:"按所在道路统计阀门数量"},fields:[{type:"input",field:"LANEWAY",appendBtns:[{perm:!0,text:"刷新",click:()=>y()}],onChange:()=>y()},{type:"table",config:O}]}],labelPosition:"top",gutter:12}),T=async t=>{if(!i.view)return;const e=m(i.view,void 0,void 0,"阀门");return e.length?await H(window.SITE_CONFIG.GIS_CONFIG.gisService+window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService+"/"+e[0],$({returnGeometry:!1,where:""})):void 0},Y=async()=>{var l,d,r,N,_;if(!i.view)return;const t=(l=f.group.find(n=>n.id==="valvestatus"))==null?void 0:l.fields,e=(t==null?void 0:t.length)&&t[0],o=m(i.view,void 0,void 0,"阀门"),p=await h({usertoken:g().gToken,layerids:JSON.stringify(o),group_fields:JSON.stringify(["OPENCLOSE"]),statistic_field:"ObjectId",statistic_type:"1",where:"",f:"pjson"}),s=(r=(d=p.data.result.rows[0])==null?void 0:d.rows)==null?void 0:r.reduce((n,W)=>n+(parseFloat(W.ObjectId)||0)*1,0),a=(_=(N=p.data.result.rows[0])==null?void 0:N.rows)==null?void 0:_.map(n=>({name:n.OPENCLOSE||"未知",nameAlias:n.OPENCLOSE||"未知",value:n.ObjectId||0,valueAlias:n.ObjectId||0,scale:(s===0?0:n.ObjectId/s*100).toFixed(2)+"%"}));e&&(e.option=S(a,"个","",0))},j=async()=>{var a,l,d;if(!i.view)return;const t=(a=f.group.find(r=>r.id==="diameter"))==null?void 0:a.fields,e=(t==null?void 0:t.length)&&t[0],o=m(i.view,void 0,void 0,"阀门"),s=(d=(l=(await h({usertoken:g().gToken,layerids:JSON.stringify(o),group_fields:JSON.stringify(["DIAMETER"]),statistic_field:"ObjectId",statistic_type:"1",where:"",f:"pjson"})).data.result.rows[0])==null?void 0:l.rows)==null?void 0:d.map(r=>({name:r.DIAMETER||"未知",nameAlias:"DN"+r.DIAMETER||"未知",value:r.ObjectId||0}));e&&(e.option=v(s,"个"))},P=async()=>{var a,l,d;if(!i.view)return;const t=(a=f.group.find(r=>r.id==="VALVETYPE"))==null?void 0:a.fields,e=(t==null?void 0:t.length)&&t[0],o=m(i.view,void 0,void 0,"阀门"),s=(d=(l=(await h({usertoken:g().gToken,layerids:JSON.stringify(o),group_fields:JSON.stringify(["VALVETYPE"]),statistic_field:"ObjectId",statistic_type:"1",where:"",f:"pjson"})).data.result.rows[0])==null?void 0:l.rows)==null?void 0:d.map(r=>({name:r.VALVETYPE||"未知",nameAlias:r.VALVETYPE||"未知",value:r.ObjectId||0}));e&&(e.option=v(s,"个"))},y=async()=>{var s,a;if(!i.view)return;const t=(s=A.value)==null?void 0:s.dataForm.LANEWAY,e=t?" LANEWAY like '%"+t+"%' ":"",o=m(i.view,void 0,void 0,"阀门"),p=await h({usertoken:g().gToken,layerids:JSON.stringify(o),group_fields:JSON.stringify(["LANEWAY"]),statistic_field:"ObjectId",statistic_type:"1",where:e||"",f:"pjson"});O.dataList=(a=p.data.result.rows[0])==null?void 0:a.rows},I=()=>{Y(),j(),P(),y()};return M(async()=>{I(),V.pipeLayerOption=await U(i.view);const t=await T();u.value[0].label=(t??"0")+"个"}),x(()=>D().isDark,()=>I()),(t,e)=>{const o=R;return G(),J("div",null,[C(w(Q),{modelValue:w(u),"onUpdate:modelValue":e[0]||(e[0]=p=>B(u)?u.value=p:null),span:24},null,8,["modelValue"]),C(o,{ref_key:"refForm",ref:A,config:w(f)},null,8,["config"])])}}}),de=q(z,[["__scopeId","data-v-90354de0"]]);export{de as default};
