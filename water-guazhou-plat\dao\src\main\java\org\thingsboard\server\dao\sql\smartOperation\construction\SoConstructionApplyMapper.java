package org.thingsboard.server.dao.sql.smartOperation.construction;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionApply;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionApplyContainer;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionApplyPageRequest;

import java.util.List;

@Mapper
public interface SoConstructionApplyMapper extends BaseMapper<SoConstructionApply> {
    List<SoConstructionApplyContainer> findByPage(SoConstructionApplyPageRequest request);

    long countByPage(SoConstructionApplyPageRequest request);

    boolean update(SoConstructionApply entity);

    boolean updateFully(SoConstructionApply entity);

    boolean isCodeExists(@Param("code") String code, @Param("tenantId") String tenantId, @Param("id") String id);

    boolean markAsComplete(String id);

    String getConstructionCodeById(String id);

    boolean canMarkGlobalAsComplete(String id);
}
