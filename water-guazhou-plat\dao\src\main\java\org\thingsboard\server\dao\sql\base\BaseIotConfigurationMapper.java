package org.thingsboard.server.dao.sql.base;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.base.BaseIotConfiguration;
import org.thingsboard.server.dao.util.imodel.query.base.BaseIotConfigurationPageRequest;

import java.util.List;

/**
 * 平台管理-物联配置Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@Mapper
public interface BaseIotConfigurationMapper {
    /**
     * 查询平台管理-物联配置
     *
     * @param id 平台管理-物联配置主键
     * @return 平台管理-物联配置
     */
    public BaseIotConfiguration selectBaseIotConfigurationById(String id);

    /**
     * 查询平台管理-物联配置列表
     *
     * @param baseIotConfiguration 平台管理-物联配置
     * @return 平台管理-物联配置集合
     */
    public IPage<BaseIotConfiguration> selectBaseIotConfigurationList(BaseIotConfigurationPageRequest baseIotConfiguration);

    /**
     * 新增平台管理-物联配置
     *
     * @param baseIotConfiguration 平台管理-物联配置
     * @return 结果
     */
    public int insertBaseIotConfiguration(BaseIotConfiguration baseIotConfiguration);

    /**
     * 修改平台管理-物联配置
     *
     * @param baseIotConfiguration 平台管理-物联配置
     * @return 结果
     */
    public int updateBaseIotConfiguration(BaseIotConfiguration baseIotConfiguration);

    /**
     * 删除平台管理-物联配置
     *
     * @param id 平台管理-物联配置主键
     * @return 结果
     */
    public int deleteBaseIotConfigurationById(String id);

    /**
     * 批量删除平台管理-物联配置
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBaseIotConfigurationByIds(@Param("array") List<String> ids);
}
