package org.thingsboard.server.dao.util.imodel.response.cache;

public class MybatisCacheAdapter<TKey, TVal> implements Cache<TKey, TVal> {
    private final org.apache.ibatis.cache.Cache delegate;

    public MybatisCacheAdapter(org.apache.ibatis.cache.Cache delegate) {
        this.delegate = delegate;
    }

    @Override
    public void put(T<PERSON>ey key, TVal val) {
        delegate.putObject(key, val);
    }

    @Override
    @SuppressWarnings("unchecked")
    public TVal get(TKey key) {
        return (TVal) delegate.getObject(key);
    }

    @Override
    @SuppressWarnings("unchecked")
    public TVal invalidate(TK<PERSON> key) {
        return (TVal) delegate.removeObject(key);
    }

    @Override
    public boolean contains(TK<PERSON> key) {
        return get(key) != null;
    }
}
