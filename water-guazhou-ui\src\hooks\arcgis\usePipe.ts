import { PipeStatistics } from '@/api/mapservice/pipe';
import { useGisStore } from '@/store';
import {
  EStatisticField,
  EStatisticGroup,
  staticPipe
} from '@/utils/MapHelper';

export const usePipe = () => {
  /**
   * 线总长
   */
  const totalLineLength = ref<number>(0);
  const lineLengths = ref<
    {
      layerid: number;
      layername: string;
      /**图层总长，单位m */ total: number;
    }[]
  >([]);
  /**
   * 获取管线长度（需要先确定已经登录管线系统并获取到管线图层信息）
   */
  const getLineLength = async () => {
    const gisStore = useGisStore();
    try {
      await gisStore.SET_gLayerInfoesAsync();
      const ids = gisStore.gLayerInfos
        ?.filter((item) => item.geometrytype === 'esriGeometryPolyline')
        .map((item) => item.layerid);
      const res = await staticPipe('length', {
        layerIds: ids
      });
      lineLengths.value = res.map((item) => {
        return {
          ...item,
          total: item.rows.reduce((prev, cur) => {
            return prev + (cur[EStatisticField.ShapeLen] ?? 0);
          }, 0)
        };
      });
      res.map((item) => {
        totalLineLength.value += item.rows[0]?.[EStatisticField.ShapeLen] ?? 0;
      });
    } catch (error) {
      console.log(error);
    }
    return lineLengths.value;
  };
  const totalPointCount = ref<number>(0);
  const pointCounts = ref<
    { layerid: number; layername: string; total: number }[]
  >([]);
  const getPointCount = async () => {
    const gisStore = useGisStore();
    try {
      await gisStore.SET_gLayerInfoesAsync();
      const ids = gisStore.gLayerInfos
        ?.filter((item) => item.geometrytype === 'esriGeometryPoint')
        .map((item) => item.layerid);

      const countRes = await staticPipe('count', {
        layerIds: ids
      });
      pointCounts.value = countRes.map((item) => {
        return {
          ...item,
          total: item.rows.reduce((prev, cur) => {
            return prev + (cur[EStatisticField.OBJECTID] ?? 0);
          }, 0)
        };
      });
      totalPointCount.value = pointCounts.value.reduce((prev, cur) => {
        return prev + cur.total;
      }, 0);
    } catch (error) {
      console.log(error);
    }
    return pointCounts.value;
  };

  return {
    /**
     * 获取管网长度
     */
    getLineLength,
    /**
     * 所有图层管线总长，单位m
     */
    totalLineLength,
    /**
     * 各图层管线长度，单位m
     */
    lineLengths,
    /**
     * 获取管点数量
     */
    getPointCount,
    /**
     * 管点总数
     */
    totalPointCount,
    /**
     * 各图层管点数量
     */
    pointCounts
  };
};
type IStatisticResult = {
  layerid: number;
  layername: string;
  rows: { value: number; name: string }[];
};
export const useStatistic = () => {
  const statisticResults = ref<IStatisticResult[]>([]);
  const statistic = async (params: {
    /**
     * JSON.stringify([1,2])
     */
    layerids: string;
    /**
     * 分组字段
     */
    group_field: EStatisticGroup;
    /**
     * 统计字段
     */
    statistic_field: EStatisticField;
    where?: string;
    geometry?: any;
  }): Promise<IStatisticResult[]> => {
    try {
      const res = await PipeStatistics({
        usertoken: useGisStore().gToken,
        statistic_type:
          params.statistic_field === EStatisticField.OBJECTID ? '1' : '2',
        where: params.where || '1=1',
        f: 'pjson',
        ...params,
        group_fields: JSON.stringify([params.group_field])
      });
      const rows = res.data?.result?.rows || [];
      const result = rows.map((item) => {
        return {
          ...item,
          rows:
            item.rows?.map((o) => {
              return {
                name: o[params.group_field],
                value: o[params.statistic_field]
              };
            }) || []
        };
      });
      statisticResults.value = result;
      return result;
    } catch (error) {
      console.log(error);
      statisticResults.value = [];
    }
    return statisticResults.value;
  };
  return {
    statistic,
    statisticResults
  };
};
