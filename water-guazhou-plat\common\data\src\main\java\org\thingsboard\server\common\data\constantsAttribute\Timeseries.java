/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.common.data.constantsAttribute;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
public class Timeseries {
    private String tag;
    private String type;
    private String pollPeriod;
    private String functionCode;
    private String address;
    private String registerCount;
    private String bit;
    private String byteOrder;
    private String order;

    /**
     * 每小时偏差上限
     */
    private String sampleDeviation;
    /**
     * 遥测数据最大值
     */
    private String samplingMax;
    /**
     * 遥测数据最小值
     */
    private String samplingMin;
    /**
     * 公式计算
     */
    private String formulaProperty;
    /**
     * 采样系数
     */
    private String sampleCoef;
    /**
     * 量程
     */
    private String range;
    /**
     * 统计类型
     */
    private String statType;

    /**
     * 变量类型
     */
    private String propertyType;

    /**
     * 分组
     */
    private String group;

    /**
     * 传感器ID号
     */
    private String serialNumber;

    /**
     * 监测点位
     */
    private String pointAddress;

    /**
     * 无效值, 针对特定值为无效值的情况
     */
    private String invalidValue;


}
