package org.thingsboard.server.dao.sql.bulletin;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.model.sql.BulletinDataEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/2 18:31
 */
@Service
public class JpaBulletinDao {

    @Autowired
    private BulletinRepository bulletinRepository;


    public BulletinDataEntity save(BulletinDataEntity bulletinDataEntity) {
        return bulletinRepository.save(bulletinDataEntity);
    }


    public BulletinDataEntity findLastByTenantId(TenantId tenantId) {
        return bulletinRepository.findFirstByTenantIdOrderByUpdateTimeDesc(UUIDConverter.fromTimeUUID(tenantId.getId()));
    }


    public List<BulletinDataEntity> findByTenant(TenantId tenantId) {
        return bulletinRepository.findByTenantId(UUIDConverter.fromTimeUUID(tenantId.getId()));
    }

}
