package org.thingsboard.server.dao.sql.repair;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.thingsboard.server.dao.model.sql.MaintenancePlanEntity;
import org.thingsboard.server.dao.model.sql.RepairPlanEntity;

import java.util.List;

public interface MaintenancePlanRepository extends JpaRepository<MaintenancePlanEntity, String> {

    @Query("SELECT rp FROM MaintenancePlanEntity rp " +
            "WHERE rp.tenantId = ?2 AND rp.name LIKE %?1%")
    Page<MaintenancePlanEntity> findList(String name, String tenantId, Pageable pageable);

    List<MaintenancePlanEntity> findByType(String type);
}
