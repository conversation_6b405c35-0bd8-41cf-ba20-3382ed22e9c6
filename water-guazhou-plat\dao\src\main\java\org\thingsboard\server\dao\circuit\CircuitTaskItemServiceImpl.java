package org.thingsboard.server.dao.circuit;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.dao.model.sql.smartProduction.circuit.CircuitTaskItem;
import org.thingsboard.server.dao.sql.smartProduction.circuit.CircuitTaskItemMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.circuit.CircuitTaskItemCompleteRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.circuit.CircuitTaskItemPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.circuit.CircuitTaskItemSaveRequest;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class CircuitTaskItemServiceImpl implements CircuitTaskItemService {
    @Autowired
    private CircuitTaskItemMapper mapper;


    @Override
    public IPage<CircuitTaskItem> findAllConditional(CircuitTaskItemPageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    public List<CircuitTaskItem> save(List<CircuitTaskItemSaveRequest> entity) {
        return QueryUtil.saveOrUpdateBatchByRequest(entity, mapper::saveAll, mapper::updateAll);
    }

    @Override
    public boolean update(CircuitTaskItem entity) {
        return mapper.update(entity);
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }

    @Override
    public boolean complete(String id, CircuitTaskItemCompleteRequest req) {
        boolean complete = mapper.complete(req);
        if (mapper.isComplete(id)) {
            mapper.markParentComplete(id);
        }
        return complete;
    }

    @Override
    public boolean deleteAll(List<String> idList) {
        return QueryUtil.deleteBatch(idList, mapper);
    }

    @Override
    @Transactional(propagation = Propagation.SUPPORTS)
    public boolean saveAllToSettings(List<String> settings, String mainId) {
        if (settings != null) {
            List<CircuitTaskItem> generatedItems = settings.stream()
                    .map(setting -> mapper.buildBySetting(setting, mainId))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            mapper.saveAll(generatedItems);
        }
        return true;
    }

}
