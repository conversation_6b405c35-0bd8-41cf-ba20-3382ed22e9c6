package org.thingsboard.server.dao.sql.shuiwu;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.thingsboard.server.dao.model.sql.shuiwu.ValveEntity;

public interface ValveRepository extends JpaRepository<ValveEntity, String> {
    @Query("SELECT v FROM ValveEntity v " +
            "WHERE v.tenantId = ?2 AND v.name LIKE %?1%")
    Page<ValveEntity> findList(String name, String tenantId, Pageable pageable);
}
