import{r as u,u as P,a8 as b,a9 as g,d as Y,c as n,s as q,x as m,S as F,o as R,bu as H,f as N,g as B,n as j,q as d,i as c,F as V,e6 as z,D as $,b6 as K,b7 as Q}from"./index-r0dFAfgr.js";import{_ as G}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as J}from"./CardTable-rdWOL4_6.js";import{_ as X}from"./CardSearch-CB_HNR-Q.js";import{p as Z,m as ee,k as te}from"./zhandian-YaGuQZe6.js";import{I as re}from"./common-CvK_P_ao.js";import{u as ae}from"./useStation-DJgnSZIA.js";import{f as T}from"./DateFormatter-Bm9a68Ax.js";import{g as ie}from"./OutsideWorkOrder-C6s8joBt.js";import{g as oe,a as le,b as se}from"./index-CpGhZCTT.js";import pe from"./detail-CoG_alkC.js";import{a as D}from"./data-quisimke.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";import"./FormMap-BGaXSqQF.js";import"./ArcView-DpMnCY82.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./widget-BcWKanF2.js";import"./pe-B8dP0-Ut.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./TileLayer-B5vQ99gG.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./commonProperties-DqNQ4F00.js";import"./project-DUuzYgGl.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./IdentifyResult-4DxLVhTm.js";import"./ViewHelper-BGCZjxXH.js";import"./fieldconfig-Bk3o1wi7.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./LayerHelper-Cn-iiqxI.js";import"./pipe-nogVzCHG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./URLHelper-B9aplt5w.js";import"./ArcPipe.vue_vue_type_script_setup_true_lang-ClsVvV2P.js";import"./utils-D5nxoMq3.js";/* empty css                         */import"./meterReading-a_0Q9Mjn.js";import"./WSPlayer-B40t_qqh.js";import"./DPlayer-Be2AurQX.js";import"./DPlayer.min-_HMH7IVX.js";import"./chart-DG9cJCI0.js";import"./index-D9ERhRP6.js";const s=u({workOrderResourceList:[],workOrderTypeList:[],WorkOrderEmergencyLevelList:[],WorkOrderProcessLevelList:[]});function ne(){ie({status:"1",page:1,size:999}).then(o=>{s.workOrderResourceList=g(o.data.data.data||[],"children",{label:"name",value:"name"})}),oe("1").then(o=>{s.workOrderTypeList=g(o.data.data||[],"children",{label:"name",value:"name"})}),le("1").then(o=>{s.WorkOrderEmergencyLevelList=g(o.data.data||[],"children",{label:"name",value:"id"})}),se("1").then(o=>{s.WorkOrderProcessLevelList=g(o.data.data||[],"children",{label:"name",value:"name"})})}ne();var C;(C=P().user)!=null&&C.name;const me=()=>b(()=>s.workOrderResourceList),de=()=>b(()=>s.WorkOrderEmergencyLevelList),ce=()=>b(()=>s.WorkOrderProcessLevelList),ue={class:"wrapper"},er=Y({__name:"statistics",setup(o){const{getStationTree:I,getStationTreeByDisabledType:W}=ae(),h=n(),v=[{label:"提醒报警",value:"1"},{label:"重要报警",value:"2"},{label:"紧急报警",value:"3"}],L=[{label:"报警中",value:"1"},{label:"已恢复",value:"2"},{label:"已解除",value:"3"}],_=[{label:"未处理",value:"1"},{label:"处理中",value:"2"},{label:"已处理",value:"3"}],y=n(),U=n(),M=u({title:"报警详情",cancel:!1,className:"lightColor",group:[]}),k=n({}),E=n({defaultParams:{processStatus:"1",alarmStatus:"1"},filters:[{label:"站点",field:"stationId",type:"select-tree",multiple:!0,options:b(()=>x.stationTree)},{label:"处理状态",field:"processStatus",type:"select",options:_},{label:"报警类型",field:"alarmType",type:"select",options:D},{label:"报警等级",field:"alarmLevel",type:"select",options:v},{label:"报警状态",field:"alarmStatus",type:"select",options:L}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",icon:re.QUERY,click:()=>l()},{type:"default",perm:!0,text:"重置",svgIcon:q(Q),click:()=>{var e;(e=y.value)==null||e.resetForm(),l()}},{perm:!0,text:"解除告警",click:()=>{var e;if(!((e=a.selectList)!=null&&e.length)){m.warning("请选择需要解除的数据");return}O()}}]}]}),a=u({defaultExpandAll:!0,indexVisible:!0,rowKey:"id",handleSelectChange(e){a.selectList=e},selectList:[],columns:[{label:"站点名称",prop:"stationName"},{label:"报警标题",prop:"title"},{label:"首次报警时间",prop:"time",formatter:e=>T(e.time,"YYYY-MM-DD HH:mm:ss")},{label:"报警结束时间",prop:"endTime",formatter:e=>T(e.endTime,"YYYY-MM-DD HH:mm:ss")},{label:"报警等级",prop:"alarmLevel",width:"110px",formatter:e=>{var t;return(t=v.find(r=>r.value===e.alarmLevel))==null?void 0:t.label}},{label:"报警类型",prop:"alarmType",width:"110px",formatter:e=>{var t;return(t=D.find(r=>r.value===e.alarmType))==null?void 0:t.label}},{label:"报警描述",prop:"alarmInfo"},{label:"处理建议",prop:"processMethod"},{label:"报警状态",prop:"alarmStatus",width:"110px",formatter:e=>{var t;return(t=L.find(r=>r.value===e.alarmStatus))==null?void 0:t.label}},{label:"处理状态",prop:"processStatus",width:"110px",formatter:e=>{var t;return(t=_.find(r=>r.value===e.processStatus))==null?void 0:t.label}},{label:"操作人",prop:"optionUserName",width:"100px"}],operationWidth:"170px",operations:[{perm:!0,text:"解除告警",click:e=>{O(e)}},{perm:!0,text:"详情",click:e=>{var t;k.value=e,(t=U.value)==null||t.openDrawer()}}],dataList:[],pagination:{page:1,limit:20,total:0,refreshData:({page:e,size:t})=>{a.pagination.page=e,a.pagination.limit=t,l()}}}),S=u({title:"新增",dialogWidth:"1000px",labelWidth:"100px",submitting:!1,submit:e=>{const t=e.id;delete e.id,e.ccUserId=e.ccUserId.join(","),Z(t,e).then(r=>{var i;r.data.code===200?(m.success("创建完成"),l(),(i=h.value)==null||i.closeDialog()):m.warning("创建失败")})},defaultValue:{},group:[{fields:[{type:"input",label:"标题",field:"title",rules:[{required:!0,message:"请输入标题"}]},{xs:6,type:"select",label:"来源",field:"source",options:me(),rules:[{required:!0,message:"请选择来源"}]},{xs:6,type:"select",label:"紧急程度",field:"level",options:de(),rules:[{required:!0,message:"请选择紧急程度"}]},{field:"address",label:"地址",type:"textarea",rules:[{required:!0,message:"请输入地址"}]},{field:"remark",label:"描述",type:"textarea"}]},{fieldset:{desc:"分派信息",type:"underline"},fields:[{type:"switch",label:"是否直接派发",field:"isDirectDispatch"},{handleHidden:(e,t,r)=>{r.hidden=!e.isDirectDispatch},label:"处理人",xs:6,field:"stepProcessUserId",type:"select",options:[]},{handleHidden:(e,t,r)=>{r.hidden=!e.isDirectDispatch},xs:6,field:"processLevel",label:"处理级别",type:"select",options:ce()}]},{fields:[{field:"ccUserId",label:"抄送人",type:"select",multiple:!0,options:[]}],fieldset:{desc:"抄送",type:"underline"}},{fields:[{field:"imgUrl",label:"现场图片",type:"image",returnType:"comma",limit:2,multiple:!0,accept:"image/*"},{field:"videoUrl",label:"现场视频",type:"file",limit:2,returnType:"comma",accept:"video/*",tips:"只能上传视频文件,最多上传2个，大小不能超过100M"},{field:"audioUrl",label:"现场录音",type:"file",limit:2,returnType:"comma",accept:"audio/*",tips:"只能上传音频文件,最多上传2个，大小不能超过4M"},{field:"otherFileUrl",label:"其它附件",type:"file",limit:2,returnType:"comma",tips:"只能上传文件,最多上传2个，大小不能超过4M"}]}]}),x=u({stationTree:[]});function O(e){F("确定解除选中的告警, 是否继续?","解除提示").then(async()=>{var r;let t=[];e?t=[e.id]:t=(r=a.selectList)==null?void 0:r.map(i=>i.id),ee(t).then(i=>{i.data.code===200?(m.success("解除告警成功"),a.selectList=[],l()):m.warning("解除告警失败")})})}function w(e){return e.map(r=>(r.children&&r.children.length!==0?w(r.children):r.disabled=!1,r))}const A=async()=>{const e=await z({authType:"CUSTOMER_USER"}),t=["uploadUserId","stepProcessUserId","ccUserId"];S.group.map(r=>{r.fields.filter(p=>p.field&&t.indexOf(p.field)!==-1).map(p=>{p.options=e.data.map(f=>({label:f.firstName,value:$(f.id.id)}))})})},l=async()=>{var t;a.dataList=[];const e={size:a.pagination.limit||20,page:a.pagination.page||1,...((t=y.value)==null?void 0:t.queryParams)||{}};e.stationId&&(e.stationId=e.stationId.join(",")),te(e).then(r=>{a.dataList=r.data.data.data||[],a.pagination.total=r.data.data.total||0})};return R(()=>{l(),A()}),H(async()=>{const e=await I();await W(e,["Project","Station"],!1,"Station"),x.stationTree=w(e||[])}),N(()=>{l()}),(e,t)=>{const r=X,i=J,p=G,f=K;return B(),j("div",ue,[d(r,{ref_key:"refSearch",ref:y,config:c(E)},null,8,["config"]),d(i,{class:"card-table",config:c(a)},null,8,["config"]),d(p,{ref_key:"refForm",ref:h,config:c(S)},null,8,["config"]),d(f,{ref_key:"refdetail",ref:U,config:c(M)},{default:V(()=>[d(pe,{config:c(k)},null,8,["config"])]),_:1},8,["config"])])}}});export{er as default};
