<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.base.BaseVideoConfigurationMapper">
    
    <resultMap type="org.thingsboard.server.dao.model.sql.base.BaseVideoConfiguration" id="BaseVideoConfigurationResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="type"    column="type"    />
        <result property="brand"    column="brand"    />
        <result property="model"    column="model"    />
        <result property="ipAddress"    column="ip_address"    />
        <result property="streamType"    column="stream_type"    />
        <result property="resolution"    column="resolution"    />
        <result property="frameRate"    column="frame_rate"    />
        <result property="videoCodec"    column="video_codec"    />
    </resultMap>

    <sql id="selectBaseVideoConfigurationVo">
        select id, name, type, brand, model, ip_address, stream_type, resolution, frame_rate, video_codec from base_video_configuration
    </sql>

    <select id="selectBaseVideoConfigurationList" parameterType="org.thingsboard.server.dao.model.sql.base.BaseVideoConfiguration" resultMap="BaseVideoConfigurationResult">
        <include refid="selectBaseVideoConfigurationVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="brand != null  and brand != ''"> and brand = #{brand}</if>
            <if test="model != null  and model != ''"> and model = #{model}</if>
            <if test="ipAddress != null  and ipAddress != ''"> and ip_address = #{ipAddress}</if>
            <if test="streamType != null  and streamType != ''"> and stream_type = #{streamType}</if>
            <if test="resolution != null  and resolution != ''"> and resolution = #{resolution}</if>
            <if test="frameRate != null  and frameRate != ''"> and frame_rate = #{frameRate}</if>
            <if test="videoCodec != null  and videoCodec != ''"> and video_codec = #{videoCodec}</if>
        </where>
    </select>
    
    <select id="selectBaseVideoConfigurationById" parameterType="String" resultMap="BaseVideoConfigurationResult">
        <include refid="selectBaseVideoConfigurationVo"/>
        where id = #{id}
    </select>

    <insert id="insertBaseVideoConfiguration" parameterType="org.thingsboard.server.dao.model.sql.base.BaseVideoConfiguration">
        insert into base_video_configuration
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="name != null">name,</if>
            <if test="type != null">type,</if>
            <if test="brand != null">brand,</if>
            <if test="model != null">model,</if>
            <if test="ipAddress != null">ip_address,</if>
            <if test="streamType != null">stream_type,</if>
            <if test="resolution != null">resolution,</if>
            <if test="frameRate != null">frame_rate,</if>
            <if test="videoCodec != null">video_codec,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="name != null">#{name},</if>
            <if test="type != null">#{type},</if>
            <if test="brand != null">#{brand},</if>
            <if test="model != null">#{model},</if>
            <if test="ipAddress != null">#{ipAddress},</if>
            <if test="streamType != null">#{streamType},</if>
            <if test="resolution != null">#{resolution},</if>
            <if test="frameRate != null">#{frameRate},</if>
            <if test="videoCodec != null">#{videoCodec},</if>
         </trim>
    </insert>

    <update id="updateBaseVideoConfiguration" parameterType="org.thingsboard.server.dao.model.sql.base.BaseVideoConfiguration">
        update base_video_configuration
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="type != null">type = #{type},</if>
            <if test="brand != null">brand = #{brand},</if>
            <if test="model != null">model = #{model},</if>
            <if test="ipAddress != null">ip_address = #{ipAddress},</if>
            <if test="streamType != null">stream_type = #{streamType},</if>
            <if test="resolution != null">resolution = #{resolution},</if>
            <if test="frameRate != null">frame_rate = #{frameRate},</if>
            <if test="videoCodec != null">video_codec = #{videoCodec},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBaseVideoConfigurationById" parameterType="String">
        delete from base_video_configuration where id = #{id}
    </delete>

    <delete id="deleteBaseVideoConfigurationByIds" parameterType="String">
        delete from base_video_configuration where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>