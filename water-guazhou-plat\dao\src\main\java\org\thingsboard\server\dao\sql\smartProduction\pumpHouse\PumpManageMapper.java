package org.thingsboard.server.dao.sql.smartProduction.pumpHouse;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.thingsboard.server.dao.model.sql.smartProduction.pumpHouse.PumpManage;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.pumpHouse.PumpManagePageRequest;

import java.util.List;

@Mapper
public interface PumpManageMapper extends BaseMapper<PumpManage> {
    IPage<PumpManage> findByPage(PumpManagePageRequest request);

    boolean update(PumpManage entity);

    int saveAll(List<PumpManage> list);

    int updateAll(List<PumpManage> ts);
}
