<template>
  <el-upload
    class="deviceImportBtn"
    action="action"
    :show-file-list="false"
    :http-request="UploadFile"
    :on-change="(file: any, fileList: any)=>handleChange(file, fileList)"
  >
    <el-button :icon="Upload">
      {{ props.operation?.text || '导 入' }}
    </el-button>
  </el-upload>
</template>

<script lang="ts" setup>
import { Upload } from '@element-plus/icons-vue'

const props = defineProps<{
  operation: any
}>()

const handleChange = (file, fileList) => {
  console.log(file, fileList)
}
const UploadFile = async (res: any) => {
  console.log(res, 'UploadFileUploadFile')
  const file = res.file
  const formData = new window.FormData()
  formData.append('file', file)
  props.operation.handle(formData)
}
</script>
<style lang="scss" scoped>
.deviceImportBtn {
  margin: 0 12px;
  font-size: 16px !important;
  display: inline-block;
}
</style>
