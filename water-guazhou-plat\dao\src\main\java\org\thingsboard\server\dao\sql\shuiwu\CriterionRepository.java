package org.thingsboard.server.dao.sql.shuiwu;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.thingsboard.server.dao.model.sql.shuiwu.CriterionEntity;

import java.util.List;

public interface CriterionRepository extends JpaRepository<CriterionEntity, String> {

    @Query("SELECT c FROM CriterionEntity c " +
            "WHERE c.tenantId = ?3 AND c.name LIKE %?1% AND c.deviceType IN ?2 AND c.isDel = '0'")
    Page<CriterionEntity> findList(String name, List<String> deviceTypeList, String s, Pageable pageable);

    @Query("SELECT c FROM CriterionEntity c " +
            "WHERE c.tenantId = ?3 AND c.name LIKE %?1% AND c.deviceType LIKE %?2% AND c.isDel = '0'")
    Page<CriterionEntity> findList(String name, String deviceType, String s, Pageable pageable);

    List<CriterionEntity> findByTenantIdAndIsDel(String tenantId, String isDel);
}
