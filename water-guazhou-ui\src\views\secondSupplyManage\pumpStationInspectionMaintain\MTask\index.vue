<!-- 养护任务 -->
<template>
  <div class="wrapper">
    <CardSearch ref="refSearch" :config="SearchConfig" />
    <CardTable :config="TableConfig" class="card-table" />
    <DialogForm ref="refForm" :config="FormConfig"></DialogForm>
    <DialogForm ref="refAuditForm" :config="FormAuditConfig"></DialogForm>
  </div>
</template>

<script lang="ts" setup>
import { Plus, Search } from '@element-plus/icons-vue';
import { SLConfirm } from '@/utils/Message';
import useGlobal from '@/hooks/global/useGlobal';
import useStation from '@/hooks/station/useStation';
import { formatDate } from '@/utils/DateFormatter';
import {
  getTaskList,
  delTask,
  saveTask,
  receiveTask,
  completeTask,
  auditTask
} from '@/api/secondSupplyManage/stationCircuit';
import useDepartment from '@/hooks/department/useDepartment';
import { getUserList } from '@/api/user/index';
import { removeSlash } from '@/utils/removeIdSlash';

const { getAllStationOption } = useStation();
const { getDepartmentTree } = useDepartment();
const { $messageSuccess, $messageError, $messageWarning, $confirm } =
  useGlobal();
const refForm = ref<IDialogFormIns>();
const refAuditForm = ref<IDialogFormIns>();
const refSearch = ref<ISearchIns>();

const state = reactive<{
  optionUsers: any;
  auditUsers: any;
  searchAuditUsers: any;
  searchOptionUsers: any;
  stationOptionList: any;
  departmentTree: any;
  currentTask: any;
  code: string;
  counter: any;
}>({
  optionUsers: [],
  auditUsers: [],
  searchOptionUsers: [],
  searchAuditUsers: [],
  stationOptionList: [],
  departmentTree: [],
  currentTask: {},
  code: '',
  counter: '000100'
});

const SearchConfig = reactive<ISearch>({
  filters: [
    {
      label: '任务编号',
      field: 'code',
      type: 'input',
      placeholder: '请输入任务编号'
    },
    {
      label: '业务类型',
      field: 'type',
      type: 'select',
      placeholder: '请选择业务类型',
      options: [
        { label: '维修业务', value: '维修业务' },
        { label: '保养业务', value: '保养业务' },
        { label: '清洗业务', value: '清洗业务' }
      ]
    },
    {
      label: '维保部门',
      field: 'optionDep',
      type: 'select-tree',
      options: computed(() => state.departmentTree) as any,
      placeholder: '请选择维保部门',
      onChange: async (val) => {
        state.searchOptionUsers = await userList(val);
      }
    },
    {
      label: '维保人员',
      field: 'optionUserId',
      type: 'select',
      placeholder: '请选择维保人员',
      options: computed(() => state.searchOptionUsers) as any
    },
    {
      label: '审核部门',
      field: 'auditDep',
      type: 'select-tree',
      options: computed(() => state.departmentTree) as any,
      placeholder: '请选审核部门',
      onChange: async (val) => {
        console.log(val);
        state.searchAuditUsers = await userList(val);
      }
    },
    {
      label: '审核人员',
      field: 'auditUserId',
      type: 'select',
      placeholder: '请选择审核人',
      options: computed(() => state.searchAuditUsers) as any
    },
    {
      label: '预计开始时间',
      field: 'startTime',
      type: 'date',
      format: 'YYYY-MM-DD',
      placeholder: '请选择预计开始时间',
      onChange: (val) => {
        const field = SearchConfig.filters?.find(
          (item) => item.field === 'endTime'
        ) as any;
        field.disabledDate = function (date: any) {
          // const endDate = val[1];
          return date < new Date(val);
        };
      }
    },
    {
      label: '预计结束时间',
      field: 'endTime',
      type: 'date',
      format: 'YYYY-MM-DD',
      placeholder: '请选择预计结束时间',
      onChange: (val) => {
        const field = SearchConfig.filters?.find(
          (item) => item.field === 'startTime'
        ) as any;
        field.disabledDate = function (date: any) {
          // const endDate = val[0];
          return date > new Date(val);
        };
      }
    },
    {
      label: '实际开始时间',
      field: 'realStartTime',
      type: 'date',
      format: 'YYYY-MM-DD',
      placeholder: '请选择实际开始时间',
      onChange: (val) => {
        const field = SearchConfig.filters?.find(
          (item) => item.field === 'realEndTime'
        ) as any;
        field.disabledDate = function (date: any) {
          // const endDate = val[1];
          return date < new Date(val);
        };
      }
    },
    {
      label: '实际结束时间',
      field: 'realEndTime',
      type: 'date',
      format: 'YYYY-MM-DD',
      placeholder: '请选择实际结束时间',
      onChange: (val) => {
        const field = SearchConfig.filters?.find(
          (item) => item.field === 'realStartTime'
        ) as any;
        field.disabledDate = function (date: any) {
          // const endDate = val[0];
          return date > new Date(val);
        };
      }
    },
    {
      label: '状态',
      field: 'status',
      type: 'select',
      placeholder: '请选择状态',
      options: [
        { label: '待接收', value: '待接收' },
        { label: '执行中', value: '执行中' },
        { label: '待审核', value: '待审核' },
        { label: '完成', value: '完成' },
        { label: '按时完成', value: '按时完成' },
        { label: '超期完成', value: '超期完成' },
        { label: '已作废', value: '已作废' }
      ]
    },
    {
      label: '审核结果',
      field: 'auditResult',
      type: 'select',
      placeholder: '请选择审核结果',
      options: [
        { label: '未审核', value: '未审核' },
        { label: '合格', value: '合格' },
        { label: '不合格', value: '不合格' }
      ]
    }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          svgIcon: shallowRef(Search),
          click: () => refreshData()
        },
        {
          text: '新增',
          perm: true,
          svgIcon: shallowRef(Plus),
          type: 'success',
          click: () => {
            FormConfig.defaultValue = {};
            FormConfig.title = '新增';
            refForm.value?.openDialog();
          }
        }
      ]
    }
  ]
});

const TableConfig = reactive<ITable>({
  loading: false,
  dataList: [],
  indexVisible: true,
  columns: [
    { prop: 'name', label: '任务名称', minWidth: '200px' },
    { prop: 'type', label: '业务类型', minWidth: '200px' },
    { prop: 'optionDepName', label: '维保部门', minWidth: '200px' },
    { prop: 'optionUserName', label: '维保人员', minWidth: '200px' },
    {
      prop: 'days',
      label: '消耗天数',
      minWidth: '200px',
      formatter: (row: any, val: any) =>
        row.endTime
          ? Math.floor((row.endTime - row.startTime) / (1000 * 60 * 60 * 24))
          : '-'
    },
    {
      prop: 'realStartTime',
      label: '任务开始时间',
      minWidth: '200px',
      formatter: (row: any, val: any) => formatDate(val, 'YYYY-MM-DD')
    },
    {
      prop: 'realEndTime',
      label: '任务结束时间',
      minWidth: '200px',
      formatter: (row: any, val: any) => formatDate(val, 'YYYY-MM-DD')
    },
    {
      prop: 'startTime',
      label: '预计开始时间',
      minWidth: '200px',
      formatter: (row: any, val: any) => formatDate(val, 'YYYY-MM-DD')
    },
    {
      prop: 'endTime',
      label: '预计结束时间',
      minWidth: '200px',
      formatter: (row: any, val: any) => formatDate(val, 'YYYY-MM-DD')
    },
    { prop: 'auditDepName', label: '审核部门', minWidth: '200px' },
    { prop: 'auditUserName', label: '审核人员', minWidth: '200px' },
    { prop: 'auditResult', label: '审核结果', minWidth: '200px' },
    { prop: 'auditRemark', label: '审核备注', minWidth: '200px' },
    { prop: 'status', label: '状态', minWidth: '200px' },
    {
      prop: 'createTime',
      label: '添加时间',
      minWidth: '200px',
      formatter: (row: any, val: any) => formatDate(val, 'YYYY-MM-DD HH:mm:ss')
    }
  ],
  operationFixed: 'right',
  operationWidth: 300,
  operations: [
    {
      text: '接收',
      isTextBtn: true,
      perm: true,
      type: 'warning',
      disabled: (row: any) => row.status !== '未开始',
      click: (row) => {
        $confirm('确定接收此任务?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          receiveTask({ id: row.id }).then((res) => {
            if (res.data.code === 200) {
              $messageSuccess('接收成功');
              refreshData();
            } else {
              $messageError(res.data.message);
            }
          });
        });
      }
    },
    {
      text: '完成',
      isTextBtn: true,
      perm: true,
      type: 'success',
      disabled: (row: any) => row.status !== '处理中',
      click: (row) => {
        $confirm('确定完成此任务?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          completeTask({ id: row.id }).then((res) => {
            if (res.data.code === 200) {
              $messageSuccess('提交成功');
              refreshData();
            } else {
              $messageError(res.data.message);
            }
          });
        });
      }
    },
    {
      text: '审核',
      isTextBtn: true,
      perm: true,
      disabled: (row: any) => row.status !== '处理完成',
      click: (row) => {
        state.currentTask = row;
        refAuditForm.value?.openDialog();
      }
    },
    {
      text: '编辑',
      isTextBtn: true,
      perm: true,

      disabled: (row: any) => !!row.auditResult,
      click: (row) => {
        FormConfig.title = '编辑';
        FormConfig.defaultValue = {
          ...row,
          startTime: row.startTime
            ? formatDate(row.startTime, 'YYYY-MM-DD')
            : '',
          endTime: row.endTime ? formatDate(row.endTime, 'YYYY-MM-DD') : ''
        };
        refForm.value?.openDialog();
      }
    },
    {
      perm: true,
      text: '删除',
      isTextBtn: true,
      type: 'danger',
      icon: 'iconfont icon-shanchu',
      click: (row) => handleDelete(row)
    }
  ],
  pagination: {
    refreshData: ({ page, size }) => {
      TableConfig.pagination.limit = size;
      TableConfig.pagination.page = page;
      refreshData();
    }
  }
});

const FormConfig = reactive<IDialogFormConfig>({
  title: '新增',
  defaultValue: {},
  dialogWidth: 900,
  group: [
    {
      fields: [
        {
          xs: 16,
          type: 'input',
          label: '任务编号',
          readonly: true,
          clearable: false,
          field: 'code',
          rules: [{ required: true, message: '请输入任务名称' }]
        },
        {
          xs: 8,
          type: 'btn-group',
          field: 'codeButton',
          btns: [
            {
              text: '获取编号',
              perm: true,
              click: () => {
                const code = getCode();
                const data = refForm.value?.refForm?.dataForm;
                FormConfig.defaultValue = {
                  ...data,
                  code
                };
                refForm.value?.refForm?.resetForm();
              }
            }
          ]
        },
        {
          xs: 12,
          type: 'input',
          label: '任务名称',
          field: 'name',
          rules: [{ required: true, message: '请填写任务名称' }]
        },
        {
          xs: 12,
          label: '业务类型',
          field: 'type',
          type: 'select',
          placeholder: '请选择业务类型',
          options: [
            { label: '维修业务', value: '维修业务' },
            { label: '保养业务', value: '保养业务' },
            { label: '清洗业务', value: '清洗业务' }
          ]
        },
        {
          xs: 12,
          type: 'date',
          label: '开始时间',
          field: 'startTime',
          format: 'YYYY-MM-DD',
          rules: [{ required: true, message: '请选择开始时间' }],
          disabledDate: (date: any) => {
            return new Date() > date;
          },
          onChange: (val) => {
            const field = FormConfig.group[0].fields?.find(
              (item) => item.field === 'endTime'
            ) as any;
            field.disabledDate = function (date: any) {
              const endDate = val;
              return date < new Date(endDate);
            };
          }
        },
        {
          xs: 12,
          type: 'date',
          label: '结束时间',
          field: 'endTime',
          format: 'YYYY-MM-DD',
          rules: [{ required: true, message: '请选择结束时间' }],
          onChange: (val) => {
            const field = FormConfig.group[0].fields?.find(
              (item) => item.field === 'startTime'
            ) as any;
            field.disabledDate = function (date: any) {
              const endDate = val;
              return date > new Date(endDate);
            };
          }
        },
        {
          xs: 12,
          type: 'select-tree',
          label: '维保部门',
          field: 'optionDep',
          rules: [{ required: true, message: '请选择维保部门' }],
          autoFillOptions: async (config) => {
            config.options = await getDepartmentTree(2);
          },
          onChange: async (val) => {
            state.optionUsers = await userList(val);
          }
        },
        {
          xs: 12,
          type: 'department-user',
          label: '维保人员',
          field: 'optionUserId',
          rules: [{ required: true, message: '请选择维保人员' }]
        },
        {
          xs: 12,
          type: 'select-tree',
          label: '审核部门',
          field: 'auditDep',
          rules: [{ required: true, message: '请选择审核部门' }],
          autoFillOptions: async (config) => {
            config.options = await getDepartmentTree(2);
          },
          onChange: async (val) => {
            state.auditUsers = await userList(val);
          }
        },
        {
          xs: 12,
          type: 'department-user',
          label: '审核人员',
          field: 'auditUserId',
          rules: [{ required: true, message: '请选择审核人员' }]
        },
        {
          xs: 12,
          type: 'select',
          field: 'stationId',
          label: '养护站点',
          rules: [{ required: true, message: '请选择站点' }],
          options: computed(() => state.stationOptionList) as any
        }
      ]
    }
  ],
  submit: (params: any) => {
    saveTask({
      ...params,
      stationType: '泵站'
    })
      .then((res) => {
        if (res.data?.code === 200) {
          $messageSuccess('保存成功');
        } else {
          $messageError('保存失败');
        }
        refreshData();
        refForm.value?.closeDialog();
      })
      .catch((err) => {
        $messageError(err);
      });
  }
});

const FormAuditConfig = reactive<IDialogFormConfig>({
  title: '审核',
  defaultValue: {},
  dialogWidth: 900,
  group: [
    {
      fields: [
        {
          xs: 24,
          type: 'select',
          label: '审核结果',
          field: 'auditResult',
          rules: [{ required: true, message: '请选择维保人员' }],
          options: [
            { label: '合格', value: '合格' },
            { label: '不合格', value: '不合格' }
          ]
        },
        {
          xs: 24,
          type: 'textarea',
          field: 'auditRemark',
          label: '审核备注'
        }
      ]
    }
  ],
  submit: (params: any) => {
    auditTask({
      id: state.currentTask.id,
      ...params
    })
      .then((res) => {
        if (res.data?.code === 200) {
          $messageSuccess('保存成功');
        } else {
          $messageError('保存失败');
        }
        refreshData();
        refAuditForm.value?.closeDialog();
      })
      .catch((err) => {
        $messageError(err);
      });
  }
});
const getCode = () => {
  const now = new Date();
  const datePart =
    now.getFullYear().toString() +
    (now.getMonth() + 1).toString().padStart(2, '0') +
    now.getDate().toString().padStart(2, '0');

  // 存储和维护生成的编号计数器
  if (!state.counter) {
    state.counter = 100; // 从000100开始
  }

  // 获取当前计数器并加1
  const numberPart = state.counter.toString().padStart(6, '0');
  state.counter++;

  // 拼接成最终的生产编号
  return datePart + numberPart;
};
// 获取部门用户
const userList = async (val: any) => {
  const res = await getUserList({ pid: val });
  const value = res.data.data.data || [];
  return value.map((item) => {
    return { label: item.firstName, value: removeSlash(item.id.id) };
  });
};
const refreshData = async () => {
  TableConfig.loading = true;
  const query = refSearch.value?.queryParams || {};
  // const [bStart, bEnd] = query.beginStartTime || [];
  // const [eStart, eEnd] = query.endStartTime || [];
  const newParams: any = {
    ...query,
    // beginStartTime: bStart,
    // beginEndTime: bEnd,
    // endStartTime: eStart,
    // endEndTime: eEnd,
    size: TableConfig.pagination.limit || 20,
    page: TableConfig.pagination.page || 1
  };
  getTaskList(newParams)
    .then((res) => {
      TableConfig.dataList = res.data?.data?.data || [];
      TableConfig.pagination.total = res.data?.data?.total || 0;
      TableConfig.loading = false;
    })
    .catch((err) => {
      $messageError(err);
      TableConfig.loading = false;
    });
};

const handleDelete = (row?: any) => {
  SLConfirm('确定删除指定养护任务', '删除提示').then(() => {
    delTask([row.id])
      .then((res) => {
        if (res.data?.code === 200) {
          $messageSuccess('删除成功');
          refreshData();
        } else {
          $messageError('删除失败');
        }
      })
      .catch((err) => {
        $messageError(err);
      });
  });
};

onMounted(async () => {
  state.stationOptionList = await getAllStationOption('泵站');
  state.departmentTree = await getDepartmentTree(2);

  refreshData();
});
</script>
<style lang="scss" scoped></style>
