#!/bin/bash
#
# Copyright © 2016-2019 The Thingsboard Authors
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

firstlaunch=${DATA_FOLDER}/.firstlaunch

if [ ! -d ${PGDATA} ]; then
    mkdir -p ${PGDATA}
    chown -R postgres:postgres ${PGDATA}
    su postgres -c '/usr/lib/postgresql/9.6/bin/pg_ctl initdb -U postgres'
fi

su postgres -c '/usr/lib/postgresql/9.6/bin/pg_ctl -l /var/log/postgres/postgres.log -w start'

if [ ! -f ${firstlaunch} ]; then
    su postgres -c 'psql -U postgres -d postgres -c "CREATE DATABASE thingsboard"'
fi
