<template>
  <div class="theme-bar">
    <span>夜间模式：</span>
    <el-switch
      v-model="isDark"
      inline-prompt
      :size="'small'"
      :active-text="'是'"
      :inactive-text="'否'"
      :active-value="true"
      :inactive-value="false"
      @change="toggle"
    />
  </div>
</template>
<script lang="ts" setup>
import { useAppStore } from '@/store'

const isDark = ref<boolean>(useAppStore().isDark)
const toggle = value => {
  useAppStore().SET_isDark(value)
}
</script>
<style lang="scss" scoped></style>
