import request from '@/plugins/axios';

// 预案管理
// 获取预案列表
export function getPlanList(params: {
  name?: string;
  stationId?: string;
  page: number | undefined;
  size: number | undefined;
  fromTime?: string;
  toTime?: string;
}) {
  return request({
    url: `/api/sp/emergencyPlan`,
    method: 'GET',
    params
  });
}

// 新增修改预案
export function postPlan(params: {
  id?: string;
  name: string;
  stationId: string;
  remark?: string;
  content: string;
}) {
  return request({
    url: `/api/sp/emergencyPlan`,
    method: 'POST',
    data: params
  });
}

// 删除预案
export function deletePlan(id: string) {
  return request({
    url: `/api/sp/emergencyPlan/${id}`,
    method: 'delete'
  });
}

// 应急人员

// 获取应急人员树
// depth:2 单位->部门
// depth:3 单位->部门->应急用户
export function getEmergencyUserTree(depth: string | number) {
  return request({
    url: `/api/sp/emergencyUser/tree/${depth}`,
    method: 'GET'
  });
}

// 获取应急人员列表
export function getEmergencyUserList(params: {
  name?: string;
  deptId?: string;
  page: number | undefined;
  size: number | undefined;
  fromTime?: string;
  toTime?: string;
}) {
  return request({
    url: `/api/sp/emergencyUser`,
    method: 'GET',
    params
  });
}

// 新增修改应急人员
export function postEmergencyUserUpdate() {
  return request({
    url: `/api/sp/emergencyUser/sync`,
    method: 'POST'
  });
}

// 新增修改预案
export function postEmergencyUser(params: {
  id?: string;
  name: string;
  phone: string;
  deptId: string;
}) {
  return request({
    url: `/api/sp/emergencyUser`,
    method: 'POST',
    data: params
  });
}

// 删除预案
export function deleteEmergencyUser(id: string) {
  return request({
    url: `/api/sp/emergencyUser/${id}`,
    method: 'delete'
  });
}

// 应急车辆
// 获取应急车辆列表
export function getMergencyVehicleList(params: {
  numberPlate?: string;
  page: number | undefined;
  size: number | undefined;
  fromTime?: string;
  toTime?: string;
}) {
  return request({
    url: `/api/sp/mergencyVehicle`,
    method: 'GET',
    params
  });
}

// 新增修改应急车辆
export function postMergencyVehicle(params: {
  id?: string;
  numberPlate: string;
  carBrand: string;
  usePeriod?: string;
  carUserId?: string;
  simNum: string;
}) {
  return request({
    url: `/api/sp/mergencyVehicle`,
    method: 'POST',
    data: params
  });
}

// 删除应急车辆
export function deleteMergencyVehicle(id: string) {
  return request({
    url: `/api/sp/mergencyVehicle/${id}`,
    method: 'delete'
  });
}

// 消息群发
// 获取短信模板
export function getSMS_templateList(params: {
  keyword?: string;
  page: number | undefined;
  size: number | undefined;
  fromTime?: string;
  toTime?: string;
}) {
  return request({
    url: `/api/sp/messageTemplate`,
    method: 'GET',
    params
  });
}

// 短信记录
// 获取短信列表
export function getMessageRecordList(params: {
  content?: string;
  page: number | undefined;
  size: number | undefined;
  fromTime?: string;
  toTime?: string;
}) {
  return request({
    url: `/api/sp/messageRecord`,
    method: 'GET',
    params
  });
}

/** 群发短信 */
export function sendSms(params: {
  receiveUserIdList: string[];
  receiveUserPhoneList: string[];
  templateId: string;
  sendTime?: string;
}) {
  return request({
    url: `/api/sp/messageRecord/send`,
    method: 'POST',
    data: params
  });
}
