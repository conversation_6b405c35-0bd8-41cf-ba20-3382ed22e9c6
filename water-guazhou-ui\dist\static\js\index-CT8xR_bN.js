import{d as j,M as B,c as h,r as p,s as m,bO as N,a8 as S,S as T,a0 as V,D,o as $,a1 as O,bo as z,i as f,g as A,h as E,F as k,q as b,br as G,ak as M,al as U,bM as H,bq as R,bP as J}from"./index-r0dFAfgr.js";import{_ as K}from"./TreeBox-DDD2iwoR.js";import{_ as Q}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as X}from"./CardTable-rdWOL4_6.js";import{_ as Y}from"./CardSearch-CB_HNR-Q.js";import{_ as Z}from"./index-BJ-QPYom.js";import{b as ee,g as te,c as ae,a as re,p as se,f as ne}from"./process-DWVjEFpZ.js";import{u as le}from"./useDepartment-BkP08hh6.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";const he=j({__name:"index",setup(oe){const{getDepartmentTree:q}=le(),{$messageError:y,$messageSuccess:v,$messageWarning:P}=B(),W=h(),x=h(),u=h(),g=p({departmentTree:[],formData:[]}),C=p({filters:[{type:"input",label:"类型名称",field:"name"}],operations:[{type:"btn-group",btns:[{perm:!0,text:"新增",svgIcon:m(M),click:()=>{var e;l.defaultValue={departmentId:[]},(e=u.value)==null||e.openDialog()}},{perm:!0,text:"查询",svgIcon:m(U),click:()=>d()}]}]}),_=p({loading:!0,indexVisible:!0,columns:[{label:"编号",prop:"code",minWidth:120,align:"center"},{label:"工程类型",prop:"mainName",minWidth:120,align:"center"},{label:"节点序号",prop:"orderNum",minWidth:120,align:"center"},{label:"节点位置",prop:"type",minWidth:120,align:"center"},{label:"表单",prop:"formName",minWidth:120,align:"center"},{label:"节点名称",prop:"name",minWidth:120,align:"center"},{label:"预计执行天数",prop:"executionDay",minWidth:120,align:"center"},{label:"显示步骤",prop:"title",minWidth:120,align:"center"},{label:"操作人员",prop:"userNames",minWidth:120,align:"center"}],dataList:[],operationFixed:"right",operationWidth:260,operations:[{perm:!0,text:"编辑",isTextBtn:!1,svgIcon:m(H),click:e=>F(e)},{perm:!0,text:"删除",isTextBtn:!1,type:"danger",svgIcon:m(R),click:e=>L([e.id])},{perm:!0,text:"表单",isTextBtn:!1,type:"warning",svgIcon:m(J),click:e=>{N.push({name:"GCGL010104",query:{formId:e.formId}})}}],pagination:{hide:!0}}),l=p({title:"新增",labelWidth:120,dialogWidth:500,group:[{fields:[{type:"select",label:"节点位置",field:"type",options:[{label:"开始",value:"开始"},{label:"过程",value:"过程"},{label:"结束",value:"结束"}],rules:[{required:!0,message:"请选择节点位置"}]},{type:"input-number",label:"节点序号",field:"orderNum",rules:[{required:!0,message:"请填写节点序号"}],placeholder:"请填写节点序号"},{type:"input",label:"节点名称",field:"name",rules:[{required:!0,message:"请填写节点名称"}],placeholder:"请填写节点名称"},{type:"select-tree",label:"操作部门",field:"departmentId",multiple:!0,showCheckbox:!0,rules:[{required:!0,message:"请选择操作部门"}],checkStrictly:!1,options:S(()=>g.departmentTree),onChange:async e=>{await I(e)}},{type:"select",multiple:!0,label:"操作人员",field:"users",options:[],rules:[{required:!0,message:"请填写操作人员"}],placeholder:"请填写操作人员"},{type:"select",label:"表单名称",field:"formId",options:S(()=>g.formData),rules:[{required:!0,message:"请选择表单名称"}],placeholder:"请选择表单名称"},{type:"input",label:"显示步骤",field:"title",rules:[{required:!0,message:"请填写显示步骤"}],placeholder:"请填写显示步骤"},{type:"input-number",label:"预计执行天数",field:"executionDay",rules:[{required:!0,message:"请填写预计执行天数"}],placeholder:"请填写预计执行天数"}]}],submit:e=>{T("确定提交？","提示信息").then(()=>{var r;l.submitting=!0,e={...e,users:e.users.join(","),mainId:(r=c.currentProject)==null?void 0:r.value,departmentId:e.departmentId.join(",")},ee(e).then(t=>{var s,n,a;((s=t.data)==null?void 0:s.code)===200?((n=u.value)==null||n.closeDialog(),l.submitting=!1,v("保存成功"),d()):(y((a=t.data)==null?void 0:a.message),l.submitting=!1)}).catch(t=>{y(t),l.submitting=!1})})}}),I=async e=>{var t,s,n,a,o;const r=(t=l.group[0].fields)==null?void 0:t.find(i=>i.field==="users");if(e){const i=await w(e);console.log(i),r.options=i}else r.options=[];l.defaultValue={...l.defaultValue,...(n=(s=u.value)==null?void 0:s.refForm)==null?void 0:n.dataForm,users:""},(o=(a=u.value)==null?void 0:a.refForm)==null||o.resetForm()},c=p({title:"工程类型",data:[],currentProject:{},isFilterTree:!0,treeNodeHandleClick:e=>{c.currentProject=e,V().SET_selectedProject(e),d()}}),w=async e=>{var n;const t=(n=(await te({pid:e.join(",")})).data)==null?void 0:n.data;return t==null?void 0:t.map(a=>({id:D(a.id.id),label:a.firstName,value:D(a.id.id)}))},F=async e=>{var t;let r={};if(e){const s=e.departmentId.split(",");await I(s),r={...e,departmentId:s,users:e.users.split(",")}}l.defaultValue=r,(t=u.value)==null||t.openDialog()},L=e=>{T("确定删除？","提示信息").then(()=>{ae(e).then(()=>{v("删除成功"),d()}).catch(r=>{P(r)})})},d=async()=>{var n,a,o,i;_.loading=!0;const r={...((n=x.value)==null?void 0:n.queryParams)||{},page:1,size:9999,mainId:(a=c.currentProject)==null?void 0:a.value},s=(i=(o=(await re(r)).data)==null?void 0:o.data)==null?void 0:i.data;_.dataList=s,_.loading=!1};return $(async()=>{var s,n,a;g.departmentTree=await q(2);const r=(s=(await se({page:1,size:9999})).data)==null?void 0:s.data.data;c.data=O(r||[]),c.currentProject=c.data[0],await d();const t=await ne({page:1,size:9999});g.formData=(a=(n=t.data)==null?void 0:n.data)==null?void 0:a.data.map(o=>({label:o.name,value:o.id}))}),(e,r)=>{const t=Z,s=Y,n=X,a=Q,o=K,i=G;return z((A(),E(o,null,{tree:k(()=>[b(t,{"tree-data":f(c)},null,8,["tree-data"])]),default:k(()=>[b(s,{ref_key:"refSearch",ref:x,config:f(C)},null,8,["config"]),b(n,{ref_key:"refTable",ref:W,config:f(_),class:"card-table"},null,8,["config"]),b(a,{ref_key:"refForm",ref:u,config:f(l)},null,8,["config"])]),_:1})),[[i,!!f(c).loading]])}}});export{he as default};
