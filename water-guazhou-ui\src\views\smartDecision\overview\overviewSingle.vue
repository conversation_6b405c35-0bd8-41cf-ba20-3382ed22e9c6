<template>
  <Layout :show-bars="false" :show-headers="true">
    <OverView_admin v-if="isAdmin"></OverView_admin>
    <OverView v-else></OverView>
  </Layout>
</template>
<script lang="ts" setup>
import Layout from '../layout/index.vue';
import OverView from './overview_zilianda.vue';
import OverView_admin from './overview_yanting.vue';
import { useUserStore } from '@/store';

const isAdmin = ref<boolean>(useUserStore().roles.includes('TENANT_ADMIN'));
</script>
<style lang="scss" scoped>
:deep(.smart-decision) {
  background: url('../imgs/bg.png') 0 0 /100% 100% no-repeat;
}
</style>
