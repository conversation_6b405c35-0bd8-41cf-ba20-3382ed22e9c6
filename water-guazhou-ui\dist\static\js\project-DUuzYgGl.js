import{U as l}from"./pe-B8dP0-Ut.js";import{e as s,y as i,a as d,W as S,a5 as h}from"./Point-WxyopZva.js";import{c2 as f,b$ as g,d8 as y,d9 as R}from"./MapView-DaoQedLH.js";import"./index-r0dFAfgr.js";let o=class extends S{constructor(t){super(t),this.geometries=[],this.outSpatialReference=null,this.transformation=null,this.transformForward=null}toJSON(){const t=this.geometries.map(a=>a.toJSON()),e=this.geometries[0],r={};return r.outSR=this.outSpatialReference.wkid||JSON.stringify(this.outSpatialReference.toJSON()),r.inSR=e.spatialReference.wkid||JSON.stringify(e.spatialReference.toJSON()),r.geometries=JSON.stringify({geometryType:f(e),geometries:t}),this.transformation&&(r.transformation=this.transformation.wkid||JSON.stringify(this.transformation)),this.transformForward!=null&&(r.transformForward=this.transformForward),r}};s([i()],o.prototype,"geometries",void 0),s([i({json:{read:{source:"outSR"}}})],o.prototype,"outSpatialReference",void 0),s([i()],o.prototype,"transformation",void 0),s([i()],o.prototype,"transformForward",void 0),o=s([d("esri.rest.support.ProjectParameters")],o);const J=o,N=h(J);async function v(n,t,e){t=N(t);const r=g(n),a={...r.query,f:"json",...t.toJSON()},p=t.outSpatialReference,m=f(t.geometries[0]),c=R(a,e);return l(r.path+"/project",c).then(({data:{geometries:u}})=>y(u,m,p))}export{J as a,v as n};
