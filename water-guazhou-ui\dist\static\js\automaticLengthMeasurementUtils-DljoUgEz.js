import{aS as P,at as Ct,e as r,y as g,a as v,v as Rt,aT as jt,p as Ft,k as It,E as pt,aU as Gt,aV as Et}from"./Point-WxyopZva.js";import{T,$ as ht,R as f,b2 as bt,aW as yt}from"./index-r0dFAfgr.js";import{M as wt,W as ft,x as W,N as a,T as qt,l as Z,w as A,V as Bt}from"./widget-BcWKanF2.js";import{is as Xt,it as Yt,iu as Jt,iv as Kt,iw as Qt,ix as te,iy as ee,iz as ne,iA as se,iB as ie,iC as ae,iD as oe,iE as re,iF as le,aI as ce,as as ue,ev as j,aS as F,aR as pe,af as w,iG as Tt,iH as he}from"./MapView-DaoQedLH.js";import{c as fe,f as ve,a as ge}from"./spatialReferenceEllipsoidUtils-j_kxMN-4.js";import{geodesicLength as kt}from"./geometryEngine-OGzB5MRq.js";import{ab as me,ac as vt,ad as $e,ae as de}from"./AnimatedLinesLayer-B2VbV4jv.js";function _e(e,t){return{type:P(t),value:e,unit:t}}function _(e,t){return{type:P(t),value:e,unit:t}}function be(e,t){return{type:P(t),value:e,unit:t}}function ye(e,t,n="arithmetic"){return{type:P(t),value:e,unit:t,rotationType:n}}function Ht(e,t){return _e(Ct(e.value,e.unit,t),t)}function xn(e,t){return T(e)?t:T(t)||e.value>Ct(t.value,t.unit,e.unit)?e:t}function Mn(e,t){return T(e)?null:{...e,value:e.value*t}}const Ln=_(0,"meters"),zn=be(0,"square-meters");ye(0,"radians");let V=class extends Rt{constructor(t){super(t),this.helpMessage=void 0}};r([g()],V.prototype,"tooltipOptions",void 0),r([g()],V.prototype,"helpMessage",void 0),V=r([v("esri.views.interactive.tooltip.SketchTooltipInfo")],V);const R="esri-tooltip",u=`${R}-content`,we=`${R}-table`,Te=`${R}-help-message`;function gt(e,t,n,s=2,i="abbr"){return Jt(e,Ht(t,n).value,n,s,i)}function Vt(e,t,n,s=2,i="abbr"){return Kt(e,Ht(t,n).value,n,s,i)}function xe(e,t,n=2,s="abbr"){return Qt(e,t.value,t.unit,n,s)}function Me(e,t,n=2,s="abbr"){return te(e,t.value,t.unit,n,s)}function Le(e,t,n=2,s="abbr"){return ee(e,t.value,t.unit,n,s)}function ze(e,t,n=2,s="abbr"){return ne(e,t.value,t.unit,n,s)}function Ce(e,t,n=2,s="abbr"){return se(e,t.value,t.unit,n,s)}function Re(e,t,n=2,s="abbr"){return ie(e,t.value,t.unit,n,s)}function Ee(e,t,n=2,s="abbr"){return ae(e,t.value,t.unit,n,s)}function ke(e,t,n=2,s="abbr"){return oe(e,t.value,t.unit,n,s)}function He(e,t,n=2,s="abbr"){return re(e,t.value,t.unit,n,s)}function Ve(e,t,n=2,s="abbr"){return le(e,t.value,t.unit,n,s)}function Ot(e,t,n){return Xt(e.value,e.unit,e.rotationType,t,n)}function Oe(e,t,n){return Yt(e.value,e.unit,e.rotationType,t,n)}function xt(e,t,n,s,i="abbr"){switch(s=ht(s,2),n){case"imperial":return Re(e,t,s,i);case"metric":return xe(e,t,s,i);default:return gt(e,t,n,s,i)}}function Ne(e,t,n,s=2,i="abbr"){switch(n){case"imperial":return Ee(e,t,s,i);case"metric":return Me(e,t,s,i);default:return Vt(e,t,n,s,i)}}function De(e,t,n,s=2,i="abbr"){switch(n){case"imperial":return ke(e,t,s,i);case"metric":return Le(e,t,s,i);default:return gt(e,t,n,s,i)}}function Se(e,t,n,s=2,i="abbr"){switch(n){case"imperial":return He(e,t,s,i);case"metric":return ze(e,t,s,i);default:return Vt(e,t,n,s,i)}}function Ue(e,t,n,s=2,i="abbr"){switch(n){case"imperial":return Ve(e,t,s,i);case"metric":return Ce(e,t,s,i);default:return gt(e,t,n,s,i)}}function Pe(e){var i,o;const t="metric";if(T(e))return t;const n=e.map,s=(n&&"portalItem"in n?(i=n.portalItem)==null?void 0:i.portal:null)??ce.getDefault();switch(((o=s.user)==null?void 0:o.units)??s.units){case t:return t;case"english":return"imperial"}return ht(jt(e.spatialReference),t)}let h=class extends ft{get _units(){const t=Pe(this.tooltip.view);return{length:t,verticalLength:t,area:t}}_getHelpMessage(t){var H,x,z,C;const{info:n}=this,{tooltipOptions:s,helpMessage:i,viewType:o}=n,c=(H=s==null?void 0:s.visibleElements)==null?void 0:H.helpMessage,p=t??i,L=o==="3d"?"helpMessages3d":"helpMessages2d";return c&&p?(C=(z=(x=this._messagesTooltip)==null?void 0:x.sketch)==null?void 0:z[L])==null?void 0:C[p]:void 0}_formatScale(t){return W(t,{style:"percent",maximumFractionDigits:0})}_formatRelativeOrientation(t){return W(t,{maximumFractionDigits:2,minimumFractionDigits:2,signDisplay:"exceptZero"})}_formatLength(t,n,s){return xt(this._messagesUnits,t,ht(n,this._units.length),s)}_formatRelativeLength(t){return Ne(this._messagesUnits,t,this._units.length)}_formatVerticalLength(t){return De(this._messagesUnits,t,this._units.verticalLength)}_formatRelativeVerticalLength(t){return Se(this._messagesUnits,t,this._units.verticalLength)}_formatTotalLength(t){return xt(this._messagesUnits,t,this._units.length)}_formatArea(t){return Ue(this._messagesUnits,t,this._units.area)}_formatPercentage(t){return W(t.value,{style:"percent"})}};r([g()],h.prototype,"info",void 0),r([g()],h.prototype,"tooltip",void 0),r([g()],h.prototype,"_units",null),r([wt("esri/core/t9n/Units"),g()],h.prototype,"_messagesUnits",void 0),r([wt("esri/views/interactive/tooltip/t9n/Tooltip"),g()],h.prototype,"_messagesTooltip",void 0),h=r([v("esri.views.interactive.tooltip.content.TooltipContent")],h);function m({className:e,helpMessage:t},...n){const s=n.filter(i=>!!i);return a("div",{class:qt(e,u)},s.length>0?a("div",{class:we},...s):null,t?a("div",{key:"help-message",class:Te},t):null)}const I=`${R}-field`,G={base:I,title:`${I}__title`,value:`${I}__value`};let l=class extends ft{render(){return a("div",{class:G.base},a("div",{class:G.title},this.title),a("div",{class:G.value},this.value))}};r([g()],l.prototype,"title",void 0),r([g()],l.prototype,"value",void 0),l=r([v("esri.views.interactive.tooltip.support.TooltipField")],l);const We={base:`${R}-value-by-value`};let y=class extends ft{constructor(){super(...arguments),this.divider="×"}render(){return a("div",{class:We.base},a("span",null,this.left),a("span",null,this.divider),a("span",null,this.right))}};r([g()],y.prototype,"left",void 0),r([g()],y.prototype,"divider",void 0),r([g()],y.prototype,"right",void 0),y=r([v("esri.views.interactive.tooltip.support.ValueByValue")],y);const Ze={base:`${u} ${`${u}--draw-rectangle`}`};let B=class extends h{render(){const{area:t,radius:n,xSize:s,ySize:i,tooltipOptions:o}=this.info,{visibleElements:c}=o,p=this._messagesTooltip.sketch;return a(m,{className:Ze.base,helpMessage:this._getHelpMessage()},c.radius&&f(n)&&a(l,{title:p.radius,value:this._formatLength(n)}),c.size&&f(s)&&f(i)&&a(l,{title:p.size,value:a(y,{left:this._formatLength(s),right:this._formatLength(i)})}),c.area&&a(l,{title:p.area,value:this._formatArea(t)}))}};B=r([v("esri.views.interactive.tooltip.content.TooltipContentDrawCircle")],B);const Ae={base:`${u}--draw-point`};let X=class extends h{render(){const{elevation:t,tooltipOptions:n}=this.info,{visibleElements:s}=n,i=this._messagesTooltip.sketch;return a(m,{className:Ae.base,helpMessage:this._getHelpMessage()},s.elevation&&a(l,{title:i.elevation,value:this._formatVerticalLength(t)}))}};X=r([v("esri.views.interactive.tooltip.content.TooltipContentDrawPoint")],X);const je={base:`${u} ${`${u}--draw-polygon`}`};let Y=class extends h{render(){const{area:t,elevation:n,tooltipOptions:s,viewType:i}=this.info,{visibleElements:o}=s,c=this._messagesTooltip.sketch;return a(m,{className:je.base,helpMessage:this._getHelpMessage()},o.elevation&&i!=="2d"&&a(l,{title:c.elevation,value:this._formatVerticalLength(n)}),o.area&&a(l,{title:c.area,value:this._formatArea(t)}))}};Y=r([v("esri.views.interactive.tooltip.content.TooltipContentDrawPolygon")],Y);const Fe={base:`${u} ${`${u}--draw-polyline`}`};let J=class extends h{render(){const{elevation:t,totalLength:n,tooltipOptions:s,viewType:i}=this.info,{visibleElements:o}=s,c=this._messagesTooltip.sketch;return a(m,{className:Fe.base,helpMessage:this._getHelpMessage()},o.elevation&&i!=="2d"&&a(l,{title:c.elevation,value:this._formatVerticalLength(t)}),o.totalLength&&a(l,{title:c.totalLength,value:this._formatLength(n)}))}};J=r([v("esri.views.interactive.tooltip.content.TooltipContentDrawPolyline")],J);const Ie={base:`${u} ${`${u}--draw-rectangle`}`};let K=class extends h{render(){const{area:t,xSize:n,ySize:s,tooltipOptions:i}=this.info,{visibleElements:o}=i,c=this._messagesTooltip.sketch;return a(m,{className:Ie.base,helpMessage:this._getHelpMessage()},o.size&&f(n)&&f(s)&&a(l,{title:c.size,value:a(y,{left:this._formatLength(n),right:this._formatLength(s)})}),o.area&&a(l,{title:c.area,value:this._formatArea(t)}))}};K=r([v("esri.views.interactive.tooltip.content.TooltipContentDrawRectangle")],K);const Ge={base:`${u} ${`${u}--extent-rotate`}`};let Q=class extends h{render(){const{angle:t,tooltipOptions:n}=this.info,{visibleElements:s}=n,i=this._messagesTooltip.sketch;return a(m,{className:Ge.base,helpMessage:this._getHelpMessage()},s.rotation&&a(l,{title:i.rotation,value:this._formatRelativeOrientation(t)}))}};Q=r([v("esri.views.interactive.tooltip.content.TooltipContentExtentRotate")],Q);const qe={base:`${u} ${`${u}--extent-scale`}`};let tt=class extends h{render(){const t=this.info,{visibleElements:n}=t.tooltipOptions,s=this._messagesTooltip.sketch;return a(m,{className:qe.base,helpMessage:this._getHelpMessage()},n.size&&a(l,{title:s.size,value:a(y,{left:this._formatLength(t.xSize),right:this._formatLength(t.ySize)})}),n.scale&&a(l,{title:s.scale,value:a(y,{left:this._formatScale(t.xScale),right:this._formatScale(t.yScale)})}))}};tt=r([v("esri.views.interactive.tooltip.content.TooltipContentExtentScale")],tt);const Be={base:`${u} ${`${u}--reshape-edge-offset`}`};let et=class extends h{render(){const{area:t,distance:n,totalLength:s,tooltipOptions:i}=this.info,{visibleElements:o}=i,c=this._messagesTooltip.sketch;return a(m,{className:Be.base,helpMessage:this._getHelpMessage()},o.distance&&a(l,{title:c.distance,value:this._formatRelativeLength(n)}),o.area&&f(t)&&a(l,{title:c.area,value:this._formatArea(t)}),o.totalLength&&f(s)&&a(l,{title:c.totalLength,value:this._formatLength(s)}))}};et=r([v("esri.views.interactive.tooltip.content.TooltipContentReshapeEdgeOffset")],et);const Xe={base:`${u} ${`${u}--transform-absolute`}`};let nt=class extends h{render(){const{info:t}=this,{visibleElements:n}=t.tooltipOptions,s=this._messagesTooltip.sketch;return a(m,{className:Xe.base,helpMessage:this._getHelpMessage()},n.orientation&&t.orientationEnabled&&a(l,{title:s.orientation,value:Ot(t.orientation,t.rotationType,t.orientationPrecision)}),n.size&&t.sizeEnabled&&a(l,{title:s.size,value:this._formatLength(t.size,t.sizeUnit,t.sizePrecision)}))}};nt=r([v("esri.views.interactive.tooltip.content.TooltipContentTransformAbsolute")],nt);const Ye={base:`${u} ${`${u}--transform-rotate`}`};let st=class extends h{render(){const{info:t}=this,{visibleElements:n}=t.tooltipOptions,s=this._messagesTooltip.sketch;return a(m,{className:Ye.base,helpMessage:this._getHelpMessage()},n.rotation&&a(l,{title:s.rotation,value:Oe(t.rotation,t.rotationType,t.rotationPrecision)}),n.orientation&&a(l,{title:s.orientation,value:Ot(t.orientation,t.rotationType,t.orientationPrecision)}))}};st=r([v("esri.views.interactive.tooltip.content.TooltipContentTransformRotate")],st);const Je={base:`${u} ${`${u}--transform-scale`}`};let it=class extends h{render(){const{scale:t,size:n,sizePrecision:s,sizeUnit:i,tooltipOptions:o}=this.info,{visibleElements:c}=o,p=this._messagesTooltip.sketch;return a(m,{className:Je.base,helpMessage:this._getHelpMessage()},c.scale&&a(l,{title:p.scale,value:this._formatPercentage(t)}),c.size&&a(l,{title:p.size,value:this._formatLength(n,i,s)}))}};it=r([v("esri.views.interactive.tooltip.content.TooltipContentTransformScale")],it);const Ke={base:`${u} ${`${u}--translate-graphic`}`};let at=class extends h{render(){const{info:t}=this,{visibleElements:n}=t.tooltipOptions,s=this._messagesTooltip.sketch;return a(m,{className:Ke.base,helpMessage:this._getHelpMessage()},n.distance&&a(l,{title:s.distance,value:this._formatLength(t.distance)}))}};at=r([v("esri.views.interactive.tooltip.content.TooltipContentTranslateGraphic")],at);const Qe={base:`${u} ${`${u}--translate-graphic-xy`}`};let ot=class extends h{render(){const{info:t}=this,{visibleElements:n}=t.tooltipOptions,s=this._messagesTooltip.sketch;return a(m,{className:Qe.base,helpMessage:this._getHelpMessage()},n.distance&&a(l,{title:s.distance,value:this._formatRelativeLength(t.distance)}))}};ot=r([v("esri.views.interactive.tooltip.content.TooltipContentTranslateGraphicXY")],ot);const tn={base:`${u} ${`${u}--translate-graphic-z`}`};let rt=class extends h{render(){const{info:e}=this,{visibleElements:t}=e.tooltipOptions,n=this._messagesTooltip.sketch;return a(m,{className:tn.base,helpMessage:this._getHelpMessage()},t.distance&&a(l,{title:n.distance,value:this._formatRelativeVerticalLength(e.distance)}))}};rt=r([v("esri.views.interactive.tooltip.content.TooltipContentTranslateGraphicZ")],rt);const en={base:`${u} ${`${u}--translate-vertex`}`};let lt=class extends h{render(){const{distance:t,elevation:n,area:s,totalLength:i,tooltipOptions:o}=this.info,{visibleElements:c}=o,p=this._messagesTooltip.sketch;return a(m,{className:en.base,helpMessage:this._getHelpMessage()},c.distance&&a(l,{title:p.distance,value:this._formatLength(t)}),c.elevation&&f(n)&&a(l,{title:p.elevation,value:this._formatVerticalLength(n)}),c.area&&f(s)&&a(l,{title:p.area,value:this._formatArea(s)}),c.totalLength&&f(i)&&a(l,{title:p.totalLength,value:this._formatLength(i)}))}};lt=r([v("esri.views.interactive.tooltip.content.TooltipContentTranslateVertex")],lt);const nn={base:`${u} ${`${u}--translate-vertex`}`};let ct=class extends h{render(){const{area:t,distance:n,elevation:s,totalLength:i,tooltipOptions:o}=this.info,{visibleElements:c}=o,p=this._messagesTooltip.sketch;return a(m,{className:nn.base,helpMessage:this._getHelpMessage()},c.distance&&a(l,{title:p.distance,value:this._formatRelativeLength(n)}),c.elevation&&f(s)&&a(l,{title:p.elevation,value:this._formatVerticalLength(s)}),c.area&&f(t)&&a(l,{title:p.area,value:this._formatArea(t)}),c.totalLength&&f(i)&&a(l,{title:p.totalLength,value:this._formatLength(i)}))}};ct=r([v("esri.views.interactive.tooltip.content.TooltipContentTranslateVertexXY")],ct);const sn={base:`${u} ${`${u}--translate-vertex`}`};let ut=class extends h{render(){const{distance:e,elevation:t,tooltipOptions:n}=this.info,{visibleElements:s}=n,i=this._messagesTooltip.sketch;return a(m,{className:sn.base,helpMessage:this._getHelpMessage()},s.distance&&a(l,{title:i.distance,value:this._formatRelativeVerticalLength(e)}),s.elevation&&f(t)&&a(l,{title:i.elevation,value:this._formatVerticalLength(t)}))}};ut=r([v("esri.views.interactive.tooltip.content.TooltipContentTranslateVertexZ")],ut);function an(e,t){if(T(t))return null;const n=document.createElement("div");switch(t.type){case"draw-point":return new X({tooltip:e,info:t,container:n});case"draw-polygon":return new Y({tooltip:e,info:t,container:n});case"draw-polyline":return new J({tooltip:e,info:t,container:n});case"draw-rectangle":return new K({tooltip:e,info:t,container:n});case"draw-circle":return new B({tooltip:e,info:t,container:n});case"extent-rotate":return new Q({tooltip:e,info:t,container:n});case"extent-scale":return new tt({tooltip:e,info:t,container:n});case"transform-absolute":return new nt({tooltip:e,info:t,container:n});case"transform-rotate":return new st({tooltip:e,info:t,container:n});case"transform-scale":return new it({tooltip:e,info:t,container:n});case"translate-graphic":return new at({tooltip:e,info:t,container:n});case"translate-graphic-z":return new rt({tooltip:e,info:t,container:n});case"translate-graphic-xy":return new ot({tooltip:e,info:t,container:n});case"translate-vertex":return new lt({tooltip:e,info:t,container:n});case"translate-vertex-z":return new ut({tooltip:e,info:t,container:n});case"translate-vertex-xy":return new ct({tooltip:e,info:t,container:n});case"reshape-edge-offset":return new et({tooltip:e,info:t,container:n})}}const on={base:`${R}`};let M=class extends Rt{constructor(t){super(t),this.info=null,this._contentContainer=(()=>{const n=document.createElement("div");return n.classList.add(on.base),n})(),this._contentWidget=null}initialize(){const t=this._contentContainer;this.addHandles([Z(()=>{var n;return(n=this.view.overlay)==null?void 0:n.surface},n=>{t.remove(),bt(n,s=>s.appendChild(t))},A),Z(()=>this.info,(n,s)=>{f(this._contentWidget)&&f(n)&&f(s)&&n.type===s.type?this._contentWidget.info=n:(this._contentWidget=yt(this._contentWidget),bt(an(this,n),i=>{this._contentWidget=i,i.container&&t.appendChild(i.container)}))},A),Z(()=>({container:this._contentContainer,contentWidget:this._contentWidget,screenPoint:this._screenPoint}),rn,A)])}destroy(){this._contentWidget=yt(this._contentWidget),this._contentContainer.remove()}clear(){this.info=null}get _screenPoint(){const t=this.view.inputManager;return t!=null&&t.multiTouchActive?null:t==null?void 0:t.latestPointerLocation}get test(){var t;return{contentContainer:this._contentContainer,visible:((t=this._contentContainer)==null?void 0:t.style.display)!=="none"}}};function rn({container:e,contentWidget:t,screenPoint:n}){const{style:s}=e;if(f(n)&&f(t)){s.display="block";const i=Bt(e),o=`translate(${Math.round(n.x)+Mt[0]*(i?-1:1)}px, ${Math.round(n.y)+Mt[1]}px)`;s.transform=i?`translate(-100%, 0) ${o}`:o}else s.display="none"}r([g({nonNullable:!0})],M.prototype,"view",void 0),r([g()],M.prototype,"info",void 0),r([g()],M.prototype,"_contentContainer",void 0),r([g()],M.prototype,"_contentWidget",void 0),r([g()],M.prototype,"_screenPoint",null),M=r([v("esri.views.interactive.tooltip.Tooltip")],M);const Mt=[20,20];function Nt(e){const t=fe(e),n=t===ve?ge:t;return ue(e,n)?n:e}function Dt(e,t,n,s,...i){return Ft(e)&&me(e)?t.apply(void 0,i):It(e)?n.apply(void 0,i):s.apply(void 0,i)}var b;function St(e){return Pt(e,b.Direct)}function Ut(e){return Pt(e,b.Horizontal)}function Pt(e,t){const{hasZ:n,spatialReference:s}=e,i=Nt(s);let o=0;const c=Et(i);if(T(c))return null;const p=t===b.Direct?Wt:k;for(const L of e.paths){if(L.length<2)continue;const H=L.length-1;for(let x=0;x<H;++x){const z=L[x];$[0]=z[0],$[1]=z[1],$[2]=n?z[2]:0;const C=L[x+1];d[0]=C[0],d[1]=C[1],d[2]=n?C[2]:0;const _t=p($,d,s);if(T(_t))return null;o+=_t.value}}return _(o,c)}function U(e,t){const{spatialReference:n}=e;return pt(n,t.spatialReference)?($[0]=e.x,$[1]=e.y,$[2]=e.hasZ?e.z:0,d[0]=t.x,d[1]=t.y,d[2]=t.hasZ?t.z:0,k($,d,n)):null}function Bn(e,t){const{spatialReference:n}=e;return pt(n,t.spatialReference)?($[0]=e.x,$[1]=e.y,$[2]=e.hasZ?e.z:0,d[0]=t.x,d[1]=t.y,d[2]=t.hasZ?t.z:0,ln($,d,n)):null}function Xn(e){return $[0]=e.x,$[1]=e.y,$[2]=e.hasZ?e.z:0,cn($,e.spatialReference)}function Wt(e,t,n){const s=mt(e,t,n,b.Direct);return f(s)?_(s.direct,s.unit):null}function k(e,t,n){const s=mt(e,t,n,b.Horizontal);return f(s)?_(s.horizontal,s.unit):null}function ln(e,t,n){const s=mt(e,t,n,b.Vertical);return f(s)?_(s.verticalSigned,s.unit):null}function cn(e,t){const n=Gt(t);return f(n)?_(e[2],n):null}function mt(e,t,n,s){const i=Nt(n),o=Et(i);if(T(o))return null;const c=t[2]-e[2];if(s===b.Vertical)return{verticalSigned:c,unit:o};if(!j(e,n,q,i)||!j(t,n,O,i))return null;if(s===b.Direct)return{direct:F(O,q),unit:o};if(pe(N,e[0],e[1],t[2]),!j(N,n,N,i))return null;const p=F(N,O);return s===b.Horizontal?{horizontal:p,unit:o}:{direct:F(O,q),horizontal:p,vertical:Math.abs(c),unit:o}}(function(e){e[e.Direct=0]="Direct",e[e.Horizontal=1]="Horizontal",e[e.Vertical=2]="Vertical"})(b||(b={}));const $=w(),d=w(),q=w(),O=w(),N=w();function Zt(e){const{spatialReference:t}=e;return Dt(t,fn,vn,gn,e)}function At(e,t){if(!pt(e.spatialReference,t.spatialReference))return null;const{spatialReference:n}=e;return D[0]=e.x,D[1]=e.y,D[2]=e.hasZ?e.z:0,S[0]=t.x,S[1]=t.y,S[2]=t.hasZ?t.z:0,$t(D,S,n)}function $t(e,t,n){return Dt(n,un,pn,hn,e,t,n)}function un(e,t,n){return _(vt(dt,e,t,n).distance,"meters")}function pn(e,t,n){return _(kt(mn(e,t,n),"meters"),"meters")}function hn(e,t,n){return Tt(e,n,Lt)&&Tt(t,n,zt)?_(vt(dt,Lt,zt).distance,"meters"):null}function fn(e){return _($e([e],"meters")[0],"meters")}function vn(e){return _(kt(e,"meters"),"meters")}function gn(e){const t=[];if(!he(e,t))return null;let n=0;for(const s of t){let i=0;for(let o=1;o<s.length;++o)i+=vt(dt,s[o-1],s[o]).distance;n+=i}return _(n,"meters")}function mn(e,t,n){return{type:"polyline",spatialReference:n,paths:[[[...e],[...t]]]}}const dt=new de,D=w(),S=w(),Lt=w(),zt=w();function Yn(e,t){return E(St,Zt,Ut,t,e)}function Jn(e,t,n,s){return E(Wt,$t,k,s,e,t,n)}function Kn(e,t,n){return E(U,At,U,n,e,t)}function Qn(e,t,n,s){return E(k,$t,k,s,e,t,n)}function ts(e){return E(St,Zt,Ut,"on-the-ground",e)}function es(e,t){return E(U,At,U,"on-the-ground",e,t)}function E(e,t,n,s,...i){if(s==="on-the-ground"){const o=t.apply(void 0,i);return f(o)?o:n.apply(void 0,i)}return e.apply(void 0,i)}export{ln as D,zn as U,_ as a,Yn as b,Kn as c,Jn as d,Pe as e,Xn as f,Qn as g,es as h,Nt as i,Ln as j,$t as k,ts as l,M as m,ye as n,be as o,xn as p,Dt as r,V as t,Bn as v,xt as x,Mn as y};
