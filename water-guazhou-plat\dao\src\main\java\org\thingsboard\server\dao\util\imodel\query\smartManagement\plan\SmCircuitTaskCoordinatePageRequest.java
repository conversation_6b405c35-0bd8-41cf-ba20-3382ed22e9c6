package org.thingsboard.server.dao.util.imodel.query.smartManagement.plan;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartManagement.plan.SmCircuitTaskCoordinate;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

@Getter
@Setter
public class SmCircuitTaskCoordinatePageRequest extends AdvancedPageableQueryEntity<SmCircuitTaskCoordinate, SmCircuitTaskCoordinatePageRequest> {
    @NotNullOrEmpty
    private String taskCode;

}
