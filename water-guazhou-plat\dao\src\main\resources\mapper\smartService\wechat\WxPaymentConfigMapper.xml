<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartService.wechat.WxPaymentConfigMapper">
    <sql id="Base_Column_List">
        <!--@sql select-->
        id,
        merchant_no,
        payment_key,
        tenant_id
        <!--@sql from wx_payment_config -->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.smartService.wechat.WxPaymentConfig">
        <result column="id" property="id"/>
        <result column="merchant_no" property="merchantNo"/>
        <result column="payment_key" property="paymentKey"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>
    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wx_payment_config
        <where>
            and tenant_id = #{tenantId}
        </where>
    </select>

    <update id="updateFully">
        update wx_payment_config
        set merchant_no = #{merchantNo},
            payment_key = #{paymentKey}
        where id = #{id}
    </update>

    <select id="getIdByTenantId" resultType="java.lang.String">
        select id
        from wx_payment_config
        where tenant_id = #{tenantId}
    </select>

    <select id="selectByTenantId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wx_payment_config
        where tenant_id=#{tenantId}
    </select>
</mapper>