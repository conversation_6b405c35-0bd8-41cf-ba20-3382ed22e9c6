package org.thingsboard.server.dao.guard;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.smartProduction.guard.GuardPlace;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.guard.GuardPlacePageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.guard.GuardPlaceSaveRequest;

public interface GuardPlaceService {
    GuardPlace findById(String id);

    IPage<GuardPlace> findAllConditional(GuardPlacePageRequest request);

    GuardPlace save(GuardPlaceSaveRequest entity);

    boolean delete(String id);

}
