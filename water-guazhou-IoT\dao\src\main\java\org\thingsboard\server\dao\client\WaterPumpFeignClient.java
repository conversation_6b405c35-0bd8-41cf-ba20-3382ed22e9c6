package org.thingsboard.server.dao.client;

import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.thingsboard.server.dao.model.sql.WaterPumpEntity;

import java.util.List;

@FeignClient(name = "base-service", configuration = {FeignConfig.class})
public interface WaterPumpFeignClient {

    @GetMapping("api/waterPump/findAll")
    List<WaterPumpEntity> findAll();

}
