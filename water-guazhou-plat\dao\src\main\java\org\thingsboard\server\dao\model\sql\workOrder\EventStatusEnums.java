package org.thingsboard.server.dao.model.sql.workOrder;

public enum EventStatusEnums {


    PENDING_REVIEW("待派单", "新建事件之后"),

    ASSIGNED("待接单", "指派事件之后"),

    PROCESSING("处理中", "用户接收工单之后"),

    COMPLETED("已完成", "用户处理完工单之后"),

    REJECTED("已撤回", "撤回");

    private final String stageName;

    private final String detailName;

    private EventStatusEnums(String stageName, String detailName) {
        this.stageName = stageName;
        this.detailName = detailName;
    }

    public String getStageName() {
        return stageName;
    }

    public String getDetailName() {
        return detailName;
    }

}
