package org.thingsboard.server.dao.util.imodel.query;

public class AdvancedPageableQueryEntity<T, S extends AdvancedPageableQueryEntity<T, S>> extends PageableQueryEntity<T> {
    private static final long MIN_PAGE = 1;

    private static final long MAX_SIZE = -1;

    @SuppressWarnings("unchecked")
    public S ignorePage() {
        setPage(MIN_PAGE);
        setSize(MAX_SIZE);
        return (S) this;
    }

}
