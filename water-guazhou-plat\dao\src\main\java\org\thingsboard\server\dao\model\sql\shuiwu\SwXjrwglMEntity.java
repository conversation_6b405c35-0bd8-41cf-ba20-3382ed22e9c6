package org.thingsboard.server.dao.model.sql.shuiwu;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;
import java.util.Date;
import java.util.List;

/**
 * 水务-巡检管理主表
 */
@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.SHUIWU_XJRWGL_M_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class SwXjrwglMEntity {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.SHUIWU_XJRWGL_M_CONTENT)
    private String content;

    @Column(name = ModelConstants.SHUIWU_XJRWGL_M_STATUS)
    private String status;

    @JsonFormat(timezone = "GMT+8",pattern="yyyy-MM-dd HH:mm:ss")
    @Column(name = ModelConstants.SHUIWU_XJRWGL_M_EXECUTE_TIME)
    private Date executeTime;

    @Column(name = ModelConstants.SHUIWU_XJRWGL_M_LIMIT_TIME)
    private Integer limitTime;

    @Column(name = ModelConstants.SHUIWU_XJRWGL_M_PERIOD_TIME)
    private Integer periodTime;

    @Column(name = ModelConstants.SHUIWU_XJRWGL_M_USERS)
    private String users;

    @Column(name = ModelConstants.CREATOR)
    private String creator;

    @Column(name = ModelConstants.IS_DEL)
    private String isDel;

    @Column(name = ModelConstants.CREATE_TIME)
    private Date createTime;

    @Column(name = ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;

    @Transient
    private List<SwXjrwglCEntity> jobList;

    @Transient
    private boolean create = false;

    @Transient
    private String userNames;

}
