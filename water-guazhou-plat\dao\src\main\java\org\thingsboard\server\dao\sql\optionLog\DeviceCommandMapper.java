package org.thingsboard.server.dao.sql.optionLog;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.request.DeviceCommandRequest;
import org.thingsboard.server.dao.model.sql.DeviceCommand;

@Mapper
public interface DeviceCommandMapper extends BaseMapper<DeviceCommand> {

    IPage<DeviceCommand> findList(IPage<DeviceCommand> page, @Param("param") DeviceCommandRequest request);

}
