import{d as k,r as v,a8 as S,g as p,n as d,p as u,aB as B,aJ as x,aw as c,i as l,bh as w,q as h,F as z,dk as y,an as E,cE as I,C as N}from"./index-r0dFAfgr.js";const P={key:0,class:"horizontal-menu"},V=["onClick"],F=k({__name:"HorizontalBar",props:{menu:{}},emits:["click"],setup(f,{emit:_}){const m=_,r=f,a=v({current:[],isCollapsed:!1}),C=S(()=>{var s,e;return(s=r.menu)!=null&&s.path?((e=a.current.find(n=>{var t;return n.pPath===((t=r.menu)==null?void 0:t.path)}))==null?void 0:e.selectedSubmenus.map(n=>n.path))||[]:[]}),g=s=>{if(!r.menu)return;let e=a.current.find(t=>{var o;return t.pPath===((o=r.menu)==null?void 0:o.path)});e||(e={pPath:r.menu.path,selectedSubmenus:[]},a.current.push(e));const n=e.selectedSubmenus.findIndex(t=>t.path===s.path);n!==-1?(e==null||e.selectedSubmenus.splice(n,1),m("click",s,!1)):(e==null||e.selectedSubmenus.push(s),m("click",s,!0))};return(s,e)=>{var t,o;const n=I;return(o=(t=s.menu)==null?void 0:t.children)!=null&&o.length?(p(),d("div",P,[u("div",{class:c(["menu-wrapper",[l(a).isCollapsed?"collapsed":""]])},[(p(!0),d(B,null,x(s.menu.children,(i,b)=>(p(),d("div",{key:b,class:c(["menu-tag",[l(C).indexOf(i.path)!==-1?"active":""]]),onClick:()=>g(i)},[u("span",null,w(i.meta.title),1)],10,V))),128))],2),u("div",{class:c(["right-bar",[l(a).isCollapsed?"right-bar-collapsed":""]]),onClick:e[0]||(e[0]=i=>l(a).isCollapsed=!l(a).isCollapsed)},[h(n,{class:c(["right-bar-icon",[l(a).isCollapsed?"right":"left"]])},{default:z(()=>[h(l(y))]),_:1},8,["class"])],2)])):E("",!0)}}}),q=N(F,[["__scopeId","data-v-d7952861"]]);export{q as default};
