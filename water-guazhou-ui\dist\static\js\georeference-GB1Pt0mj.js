import{R as T,T as f}from"./index-r0dFAfgr.js";import{e as u,y as g,a as q,W as G,w as S,$ as k,as as V,at as C}from"./Point-WxyopZva.js";import{e as W}from"./mat3f64-BVJGbF0t.js";import{af as R,ag as D,eL as H,aY as z,a_ as b,be as O,ex as J,bd as U,eM as X,aX as Z,eN as K,aN as w,bi as P,eO as Q}from"./MapView-DaoQedLH.js";import{e as m,o as tt}from"./mat4f64-BCm7QTSd.js";import{c as _}from"./spatialReferenceEllipsoidUtils-j_kxMN-4.js";import{v as rt}from"./quat-CM9ioDFt.js";import{e as nt}from"./quatf64-QCogZAoR.js";import{a as N,x as et,g as ot}from"./axisAngleDegrees-CVgmQKGQ.js";import{T as h,i as y}from"./BufferView-BcX1hwIm.js";import{t as A,e as at,r as $}from"./vec33-BEptSvzS.js";import{R as it,h as lt,L as st,M as ct,j as pt,k as ut,O as gt,v as ft,V as ht}from"./projection-oyk5Uk7v.js";var M;let p=M=class extends G{constructor(t){super(t),this.origin=R(),this.translation=R(),this.rotation=N(),this.scale=D(1,1,1),this.geographic=!0}get localMatrix(){const t=m();return rt(E,ot(this.rotation),et(this.rotation)),H(t,E,this.translation,this.scale),t}get localMatrixInverse(){return z(m(),this.localMatrix)}applyLocal(t,r){return b(r,t,this.localMatrix)}applyLocalInverse(t,r){return b(r,t,this.localMatrixInverse)}project(t,r){const n=new Float64Array(t.length),e=h.fromTypedArray(n),o=h.fromTypedArray(t);if(this.geographic){const l=_(r),c=m();return O(r,this.origin,c,l),J(c,c,this.localMatrix),A(e,o,c),U(n,l,0,n,r,0,n.length/3),n}const{localMatrix:a,origin:i}=this;X(a,tt)?at(e,o):A(e,o,a);for(let l=0;l<n.length;l+=3)n[l+0]+=i[0],n[l+1]+=i[1],n[l+2]+=i[2];return n}getOriginPoint(t){const[r,n,e]=this.origin;return new S({x:r,y:n,z:e,spatialReference:t})}equals(t){return T(t)&&this.geographic===t.geographic&&Z(this.origin,t.origin)&&K(this.localMatrix,t.localMatrix)}clone(){const t={origin:w(this.origin),translation:w(this.translation),rotation:N(this.rotation),scale:w(this.scale),geographic:this.geographic};return new M(t)}};u([g({type:[Number],nonNullable:!0,json:{write:!0}})],p.prototype,"origin",void 0),u([g({type:[Number],nonNullable:!0,json:{write:!0}})],p.prototype,"translation",void 0),u([g({type:[Number],nonNullable:!0,json:{write:!0}})],p.prototype,"rotation",void 0),u([g({type:[Number],nonNullable:!0,json:{write:!0}})],p.prototype,"scale",void 0),u([g({type:Boolean,nonNullable:!0,json:{write:!0}})],p.prototype,"geographic",void 0),u([g()],p.prototype,"localMatrix",null),u([g()],p.prototype,"localMatrixInverse",null),p=M=u([q("esri.geometry.support.MeshTransform")],p);const E=nt(),yt=p;function d(t,r){return t.isGeographic||t.isWebMercator&&((r==null?void 0:r.geographic)??!0)}function j(t,r,n){return d(r.spatialReference,n)?$t(t,r,n):At(t,r,n)}function mt(t,r,n){const{position:e,normal:o,tangent:a}=t;if(f(r))return{position:e,normal:o,tangent:a};const i=r.localMatrix;return j({position:gt(e,new Float64Array(e.length),i),normal:T(o)?ft(o,new Float32Array(o.length),i):null,tangent:T(a)?ht(a,new Float32Array(a.length),i):null},r.getOriginPoint(n),{geographic:r.geographic})}function Gt(t,r,n){if(n!=null&&n.useTransform){const{position:e,normal:o,tangent:a}=t;return{vertexAttributes:{position:e,normal:o,tangent:a},transform:new yt({origin:[r.x,r.y,r.z??0],geographic:d(r.spatialReference,n)})}}return{vertexAttributes:j(t,r,n),transform:null}}function L(t,r,n){return d(r.spatialReference,n)?Mt(t,r,n):B(t,r,n)}function St(t,r,n,e){if(f(r))return L(t,n,e);const o=mt(t,r,n.spatialReference);return n.equals(r.getOriginPoint(n.spatialReference))?B(o,n,e):L(o,n,e)}function At(t,r,n){const e=new Float64Array(t.position.length),o=t.position,a=r.x,i=r.y,l=r.z||0,{horizontal:c,vertical:x}=F(n?n.unit:null,r.spatialReference);for(let s=0;s<o.length;s+=3)e[s+0]=o[s+0]*c+a,e[s+1]=o[s+1]*c+i,e[s+2]=o[s+2]*x+l;return{position:e,normal:t.normal,tangent:t.tangent}}function $t(t,r,n){const e=r.spatialReference,o=I(r,n,v),a=new Float64Array(t.position.length),i=xt(t.position,o,e,a),l=P(Y,o);return{position:i,normal:wt(i,a,t.normal,l,e),tangent:Tt(i,a,t.tangent,l,e)}}function xt(t,r,n,e){A(h.fromTypedArray(e),h.fromTypedArray(t),r);const o=new Float64Array(t.length);return it(e,o,n)}function wt(t,r,n,e,o){if(f(n))return null;const a=new Float32Array(n.length);return $(y.fromTypedArray(a),y.fromTypedArray(n),e),lt(a,t,r,o,a),a}function Tt(t,r,n,e,o){if(f(n))return null;const a=new Float32Array(n.length);$(y.fromTypedArray(a,4*Float32Array.BYTES_PER_ELEMENT),y.fromTypedArray(n,4*Float32Array.BYTES_PER_ELEMENT),e);for(let i=3;i<a.length;i+=4)a[i]=n[i];return st(a,t,r,o,a),a}function B(t,r,n){const e=new Float64Array(t.position.length),o=t.position,a=r.x,i=r.y,l=r.z||0,{horizontal:c,vertical:x}=F(n?n.unit:null,r.spatialReference);for(let s=0;s<o.length;s+=3)e[s+0]=(o[s+0]-a)/c,e[s+1]=(o[s+1]-i)/c,e[s+2]=(o[s+2]-l)/x;return{position:e,normal:t.normal,tangent:t.tangent}}function Mt(t,r,n){const e=r.spatialReference;I(r,n,v);const o=z(Rt,v),a=new Float64Array(t.position.length),i=vt(t.position,e,o,a),l=P(Y,o);return{position:i,normal:dt(t.normal,t.position,a,e,l),tangent:Ft(t.tangent,t.position,a,e,l)}}function I(t,r,n){O(t.spatialReference,[t.x,t.y,t.z||0],n,_(t.spatialReference));const{horizontal:e,vertical:o}=F(r?r.unit:null,t.spatialReference);return Q(n,n,[e,e,o]),n}function vt(t,r,n,e){const o=ct(t,r,e),a=h.fromTypedArray(o),i=new Float64Array(o.length),l=h.fromTypedArray(i);return A(l,a,n),i}function dt(t,r,n,e,o){if(f(t))return null;const a=pt(t,r,n,e,new Float32Array(t.length)),i=y.fromTypedArray(a);return $(i,i,o),a}function Ft(t,r,n,e,o){if(f(t))return null;const a=ut(t,r,n,e,new Float32Array(t.length)),i=y.fromTypedArray(a,4*Float32Array.BYTES_PER_ELEMENT);return $(i,i,o),a}function F(t,r){if(f(t))return bt;const n=r.isGeographic?1:k(r),e=r.isGeographic?1:V(r),o=C(1,t,"meters");return{horizontal:o*n,vertical:o*e}}const v=m(),Rt=m(),Y=W(),bt={horizontal:1,vertical:1};export{yt as L,St as M,Gt as _,L as b,mt as k,d as r,j as x};
