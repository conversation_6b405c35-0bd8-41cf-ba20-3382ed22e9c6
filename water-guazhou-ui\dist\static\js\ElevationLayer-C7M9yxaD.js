const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["static/js/ElevationQuery-lpOV_-MV.js","static/js/MapView-DaoQedLH.js","static/js/index-r0dFAfgr.js","static/css/index-ByiGXvnH.css","static/js/Point-WxyopZva.js","static/js/widget-BcWKanF2.js","static/js/pe-B8dP0-Ut.js"])))=>i.map(i=>d[i]);
import{T as f,aO as c,R as p,eS as g,a3 as y}from"./index-r0dFAfgr.js";import{s as w,Z as V,S as v,e as s,y as l,o as D,a as S}from"./Point-WxyopZva.js";import{U as m,A as T}from"./pe-B8dP0-Ut.js";import{eg as $,Q as x,R as b,V as I,eK as O,cS as E,e as L}from"./MapView-DaoQedLH.js";import{s as N}from"./ArcGISCachedService-CQM8IwuM.js";import{h as A}from"./WorkerHandle-3vEm1fum.js";import"./widget-BcWKanF2.js";import"./TilemapCache-BPMaYmR0.js";class U{constructor(e,t,i,o){this._hasNoDataValues=null,this._minValue=null,this._maxValue=null,"pixelData"in e?(this.values=e.pixelData,this.width=e.width,this.height=e.height,this.noDataValue=e.noDataValue):(this.values=e,this.width=t,this.height=i,this.noDataValue=o)}get hasNoDataValues(){if(f(this._hasNoDataValues)){const e=this.noDataValue;this._hasNoDataValues=this.values.includes(e)}return this._hasNoDataValues}get minValue(){return this._ensureBounds(),c(this._minValue)}get maxValue(){return this._ensureBounds(),c(this._maxValue)}_ensureBounds(){if(p(this._minValue))return;const{noDataValue:e,values:t}=this;let i=1/0,o=-1/0,u=!0;for(const n of t)n===e?this._hasNoDataValues=!0:(i=n<i?n:i,o=n>o?n:o,u=!1);u?(this._minValue=0,this._maxValue=0):(this._minValue=i,this._maxValue=o>-3e38?o:0)}}class _ extends A{constructor(e=null){super("LercWorker","_decode",{_decode:t=>[t.buffer]},e,{strategy:"dedicated"}),this.schedule=e,this.ref=0}decode(e,t,i){return e&&e.byteLength!==0?this.invoke({buffer:e,options:t},i):Promise.resolve(null)}release(){--this.ref<=0&&(h.forEach((e,t)=>{e===this&&h.delete(t)}),this.destroy())}}const h=new Map;function j(r=null){let e=h.get(c(r));return e||(p(r)?(e=new _(t=>r.immediate.schedule(t)),h.set(r,e)):(e=new _,h.set(null,e))),++e.ref,e}let a=class extends N($(x(b(I(L))))){constructor(...r){super(...r),this.copyright=null,this.heightModelInfo=null,this.path=null,this.minScale=void 0,this.maxScale=void 0,this.opacity=1,this.operationalLayerType="ArcGISTiledElevationServiceLayer",this.sourceJSON=null,this.type="elevation",this.url=null,this.version=null,this._lercDecoder=j()}normalizeCtorArgs(r,e){return typeof r=="string"?{url:r,...e}:r}destroy(){this._lercDecoder=g(this._lercDecoder)}readVersion(r,e){let t=e.currentVersion;return t||(t=9.3),t}load(r){const e=p(r)?r.signal:null;return this.addResolvingPromise(this.loadFromPortal({supportedTypes:["Image Service"],supportsData:!1,validateItem:t=>{for(let i=0;i<t.typeKeywords.length;i++)if(t.typeKeywords[i].toLowerCase()==="elevation 3d layer")return!0;throw new w("portal:invalid-layer-item-type","Invalid layer item type '${type}', expected '${expectedType}' ",{type:"Image Service",expectedType:"Image Service Elevation 3D Layer"})}},r).catch(V).then(()=>this._fetchImageService(e))),Promise.resolve(this)}fetchTile(r,e,t,i){const o=p((i=i||{signal:null}).signal)?i.signal:i.signal=new AbortController().signal,u={responseType:"array-buffer",signal:o},n={noDataValue:i.noDataValue,returnFileInfo:!0};return this.load().then(()=>this._fetchTileAvailability(r,e,t,i)).then(()=>m(this.getTileUrl(r,e,t),u)).then(d=>this._lercDecoder.decode(d.data,n,o)).then(d=>new U(d))}getTileUrl(r,e,t){const i=!this.tilemapCache&&this.supportsBlankTile,o=T({...this.parsedUrl.query,blankTile:!i&&null});return`${this.parsedUrl.path}/tile/${r}/${e}/${t}${o?"?"+o:""}`}async queryElevation(r,e){const{ElevationQuery:t}=await y(()=>import("./ElevationQuery-lpOV_-MV.js"),__vite__mapDeps([0,1,2,3,4,5,6]));return v(e),new t().query(this,r,e)}async createElevationSampler(r,e){const{ElevationQuery:t}=await y(()=>import("./ElevationQuery-lpOV_-MV.js"),__vite__mapDeps([0,1,2,3,4,5,6]));return v(e),new t().createSampler(this,r,e)}_fetchTileAvailability(r,e,t,i){return this.tilemapCache?this.tilemapCache.fetchAvailability(r,e,t,i):Promise.resolve("unknown")}async _fetchImageService(r){var i;if(this.sourceJSON)return this.sourceJSON;const e={query:{f:"json",...this.parsedUrl.query},responseType:"json",signal:r},t=await m(this.parsedUrl.path,e);t.ssl&&(this.url=(i=this.url)==null?void 0:i.replace(/^http:/i,"https:")),this.sourceJSON=t.data,this.read(t.data,{origin:"service",url:this.parsedUrl})}get hasOverriddenFetchTile(){return!this.fetchTile.__isDefault__}};s([l({json:{read:{source:"copyrightText"}}})],a.prototype,"copyright",void 0),s([l({readOnly:!0,type:O})],a.prototype,"heightModelInfo",void 0),s([l({type:String,json:{origins:{"web-scene":{read:!0,write:!0}},read:!1}})],a.prototype,"path",void 0),s([l({type:["show","hide"]})],a.prototype,"listMode",void 0),s([l({json:{read:!1,write:!1,origins:{service:{read:!1,write:!1},"portal-item":{read:!1,write:!1},"web-document":{read:!1,write:!1}}},readOnly:!0})],a.prototype,"minScale",void 0),s([l({json:{read:!1,write:!1,origins:{service:{read:!1,write:!1},"portal-item":{read:!1,write:!1},"web-document":{read:!1,write:!1}}},readOnly:!0})],a.prototype,"maxScale",void 0),s([l({json:{read:!1,write:!1,origins:{"web-document":{read:!1,write:!1}}}})],a.prototype,"opacity",void 0),s([l({type:["ArcGISTiledElevationServiceLayer"]})],a.prototype,"operationalLayerType",void 0),s([l()],a.prototype,"sourceJSON",void 0),s([l({json:{read:!1},value:"elevation",readOnly:!0})],a.prototype,"type",void 0),s([l(E)],a.prototype,"url",void 0),s([l()],a.prototype,"version",void 0),s([D("version",["currentVersion"])],a.prototype,"readVersion",null),a=s([S("esri.layers.ElevationLayer")],a),a.prototype.fetchTile.__isDefault__=!0;const F=a;export{F as default};
