import{d as H,j as M,b3 as V,c as z,l as C,r as B,a8 as S,D as G,eX as O,dW as W,a0 as X,fE as j,cO as q,cP as K,cQ as Q,cR as Y,ch as Z,cS as x,bu as ss,cF as ts,u as b,ei as as,C as es,n as d,q as a,F as e,ay as I,fF as os,g as p,p as s,bh as u,G as A,aB as _,aJ as v,h as E,an as w,dX as ns,cE as ls,cK as is,J as ps,cL as ds,cM as rs,fG as us,bU as cs,bz as ms,bW as _s,fH as gs}from"./index-r0dFAfgr.js";/* empty css                  */const hs=H({name:"Portal",setup(){const t=M(),o=V(),h=z(C().format()),k=B({year:S(()=>h.value.slice(0,4)),month:S(()=>h.value.slice(5,7)),day:S(()=>h.value.slice(8,10)),nowtime:C().format("HH:mm:ss").slice(0,8)}),r=B({applist:[],length:0,showBigCard:localStorage.getItem("showBigPortalCard")!=="false",getapplist:async()=>{const i=G(O.get("tenantId")||"");try{const n=await W(i);n.status===200&&(r.applist=n.data,r.length=r.applist.length,r.applist.length===1&&r.Jump(r.applist[0]))}catch{r.applist=[],r.length=0}},Jump:async i=>{if(i)if(X().curNavs.length||await j(),t.SET_appname(i.name),t.SET_appid(i.id),i.type==="2"){const n=q(i.applicationUrl,"type");let m="";if(t.SET_isFrameApp(!0),n){if(n.value==="yingshou"){const y=await K();m=i.applicationUrl.split("?")[0]+"?username="+y.data.username+"&password="+y.data.password}else n.value==="yingshou2"?m=i.applicationUrl.split("?")[0]+"?o="+Q():m=i.applicationUrl;window.open(m)}else t.SET_frameAppUrl(i.applicationUrl),o.push({name:"InsideFrame",params:{url:i.applicationUrl}})}else{t.SET_isFrameApp(!1),await Y();const n=Z().addRouters||[];o.push({path:x(n[0])||"/"})}}});function U(){b().LogOut()}ss(()=>{setInterval(()=>{h.value=C().format(),k.nowtime=C().format("HH:mm:ss").slice(0,8)},1e3),r.getapplist(),f.getTenants()});const f=B({name:"",getTenants:()=>{const i=b().tenantId;as(i).then(n=>{console.log(n),n.data&&(f.name=n.data.additionalInfo.apptitle)}).catch(n=>{console.log(n)})}});return{time:k,apps:r,userLogo:ts,logout:U,apptitle:f,useUserStore:b,appStore:t}}}),fs={class:"bg"},vs={class:"left"},ws={class:"logo"},ys=["src"],Cs={class:"title"},Es={class:"right"},ks=["src"],Fs={class:"el-dropdown-link"},Bs={class:"content-left"},Ss={class:"content-left-top"},bs={class:"content-left-bottom"},Us={class:"content-right"},Is={class:"content-right-bottom"},As={class:"date year"},Ds={class:"date mothen"},Ts={class:"date day"},Ls={class:"date"},Js={class:"time"},Ns={key:0,class:"card-content"},Ps={class:"img"},Rs=["src"],$s={class:"card-title"},Hs={class:"card-content"},Ms={class:"img"},Vs=["src"],zs={class:"card-title"},Gs={class:"card-content"},Os={class:"img"},Ws=["src"],Xs={class:"card-title"};function js(t,o,h,k,r,U){const f=ns,i=I("arrow-down"),n=ls,m=is,y=I("router-link"),D=ps,T=ds,L=rs,J=us,c=cs,F=ms,N=_s,P=gs,R=os;return p(),d("div",fs,[a(R,null,{default:e(()=>[a(J,null,{default:e(()=>[s("div",vs,[s("div",ws,[s("img",{src:t.appStore.logo,alt:""},null,8,ys)]),s("div",Cs,u(t.apptitle.name),1)]),s("div",Es,[a(f,{size:45,src:"https://empty"},{default:e(()=>[s("img",{class:"user-avatar",src:t.userLogo},null,8,ks)]),_:1}),a(L,null,{dropdown:e(()=>[a(T,null,{default:e(()=>[a(y,{class:"inlineBlock",to:"/accountManage/index"},{default:e(()=>[a(m,null,{default:e(()=>o[1]||(o[1]=[s("span",null,"账户管理",-1)])),_:1})]),_:1}),a(m,{divided:""},{default:e(()=>[a(D,{type:"default",text:!0,size:"small",class:"logout-btn",onClick:t.logout},{default:e(()=>o[2]||(o[2]=[A(" 登出 ")])),_:1},8,["onClick"])]),_:1})]),_:1})]),default:e(()=>[s("span",Fs,[A(u(t.useUserStore().name)+" ",1),a(n,{class:"el-icon--right"},{default:e(()=>[a(i)]),_:1})])]),_:1})])]),_:1}),a(P,null,{default:e(()=>[a(N,{gutter:20},{default:e(()=>[a(c,{xs:24,sm:24,md:2,lg:2,xl:2}),a(c,{xs:24,sm:24,md:10,lg:10,xl:10},{default:e(()=>[s("div",Bs,[s("div",Ss,[o[3]||(o[3]=s("span",null,"WELCOME!",-1)),s("span",null,"欢迎使用"+u(t.apptitle.name),1)]),s("div",bs," 你好，"+u(t.useUserStore().firstName)+"，开始您一天的工作吧！ ",1)])]),_:1}),a(c,{xs:24,sm:24,md:10,lg:10,xl:10,class:"while"},{default:e(()=>[s("div",Us,[o[7]||(o[7]=s("span",{class:"content-right-top"},"当前时间",-1)),s("div",Is,[s("div",As,[(p(!0),d(_,null,v(t.time.year,l=>(p(),d("div",{key:l},[s("span",null,u(l),1)]))),128)),o[4]||(o[4]=s("span",null,"年",-1))]),s("div",Ds,[(p(!0),d(_,null,v(t.time.month,l=>(p(),d("div",{key:l},[s("span",null,u(l),1)]))),128)),o[5]||(o[5]=s("span",null,"月",-1))]),s("div",Ts,[(p(!0),d(_,null,v(t.time.day,l=>(p(),d("div",{key:l},[s("span",null,u(l),1)]))),128)),o[6]||(o[6]=s("span",null,"日",-1))]),s("div",Ls,[s("div",Js,[s("span",null,u(t.time.nowtime),1)])])])])]),_:1}),a(c,{xs:24,sm:24,md:2,lg:2,xl:2}),a(c,{xs:24,sm:24,md:2,lg:2,xl:2}),t.apps.showBigCard?(p(),E(c,{key:0,xs:24,sm:24,md:5,lg:5,xl:5},{default:e(()=>[a(F,{class:"bigcard",shadow:"always",onClick:o[0]||(o[0]=l=>t.apps.Jump(t.apps.applist[0]))},{default:e(()=>[t.apps.applist[0]?(p(),d("div",Ns,[s("div",Ps,[s("img",{src:t.apps.applist[0].img},null,8,Rs)]),s("div",$s,u(t.apps.applist[0].name),1)])):w("",!0)]),_:1})]),_:1})):w("",!0),a(c,{class:"cards",xs:24,sm:24,md:t.apps.showBigCard?15:20},{default:e(()=>[(p(!0),d(_,null,v(t.apps.applist,(l,g)=>(p(),d(_,null,[g>=(t.apps.showBigCard?1:0)&&g<(t.apps.showBigCard?7:t.apps.applist.length)?(p(),E(F,{key:g,class:"card",shadow:"always",onClick:$=>t.apps.Jump(l)},{default:e(()=>[s("div",Hs,[s("div",Ms,[s("img",{src:l.img},null,8,Vs)]),s("div",zs,u(l.name),1)])]),_:2},1032,["onClick"])):w("",!0)],64))),256))]),_:1},8,["md"]),a(c,{xs:24,sm:24,md:2,lg:2,xl:2}),a(c,{xs:24,sm:24,md:2,lg:2,xl:2}),t.apps.showBigCard?(p(),E(c,{key:1,class:"othercards",xs:24,sm:24,md:20,lg:20,xl:20},{default:e(()=>[(p(!0),d(_,null,v(t.apps.applist,(l,g)=>(p(),d(_,null,[g>=7?(p(),E(F,{key:g,class:"card",shadow:"always",onClick:$=>t.apps.Jump(l)},{default:e(()=>[s("div",Gs,[s("div",Os,[s("img",{src:l.img},null,8,Ws)]),s("div",Xs,u(l.name),1)])]),_:2},1032,["onClick"])):w("",!0)],64))),256))]),_:1})):w("",!0)]),_:1})]),_:1})]),_:1})])}const Qs=es(hs,[["render",js],["__scopeId","data-v-ba1aef78"]]);export{Qs as default};
