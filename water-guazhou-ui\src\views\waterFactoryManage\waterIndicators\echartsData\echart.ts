export function lineOption(datex?: any) {
  const option = {
    title: {
      text: '',
      textStyle: {
        fontSize: '14px'
      },
      top: 10
    },
    grid: {
      left: 80,
      right: 80,
      top: 50,
      bottom: 20
    },
    legend: {
      top: 0,
      width: '500',
      type: 'scroll',
      textStyle: {
        fontSize: 12
      }
    },
    tooltip: { trigger: 'axis' },
    xAxis: {
      type: 'category',
      data: datex
    },
    yAxis: [{
      position: 'left',
      alignTicks: true,
      type: 'value',
      name: '流量',
      offset: 0,
      axisLine: {
        show: true,
        lineStyle: {
          types: 'solid'
        }
      },
      axisLabel: {
        show: true
      },
      splitLine: {
        lineStyle: {
          type: [5, 10],
          dashOffset: 5
        }
      }
    }],
    series: [{}]
  }
  return option
}
