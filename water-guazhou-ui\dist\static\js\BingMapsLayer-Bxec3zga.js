import{h as w,w as v,b as y,s as n,e as o,y as s,a as M}from"./Point-WxyopZva.js";import{c as f,Q as S,V as j,z as m,cM as e}from"./MapView-DaoQedLH.js";import{U as h}from"./pe-B8dP0-Ut.js";import{R as c,T as b}from"./index-r0dFAfgr.js";import{j as U}from"./BaseTileLayer-DM38cky_.js";import"./widget-BcWKanF2.js";import"./imageBitmapUtils-Db1drMDc.js";const u=new w({BingMapsAerial:"aerial",BingMapsRoad:"road",BingMapsHybrid:"hybrid"}),k="https://dev.virtualearth.net";let r=class extends f(S(j(U))){constructor(t){super(t),this.type="bing-maps",this.tileInfo=new m({size:[256,256],dpi:96,origin:new v({x:-20037508342787e-6,y:20037508342787e-6,spatialReference:y.WebMercator}),spatialReference:y.WebMercator,lods:[new e({level:1,resolution:78271.5169639999,scale:295828763795777e-6}),new e({level:2,resolution:39135.7584820001,scale:147914381897889e-6}),new e({level:3,resolution:19567.8792409999,scale:73957190948944e-6}),new e({level:4,resolution:9783.93962049996,scale:36978595474472e-6}),new e({level:5,resolution:4891.96981024998,scale:18489297737236e-6}),new e({level:6,resolution:2445.98490512499,scale:9244648868618e-6}),new e({level:7,resolution:1222.99245256249,scale:4622324434309e-6}),new e({level:8,resolution:611.49622628138,scale:2311162217155e-6}),new e({level:9,resolution:305.748113140558,scale:1155581108577e-6}),new e({level:10,resolution:152.874056570411,scale:577790.554289}),new e({level:11,resolution:76.4370282850732,scale:288895.277144}),new e({level:12,resolution:38.2185141425366,scale:144447.638572}),new e({level:13,resolution:19.1092570712683,scale:72223.819286}),new e({level:14,resolution:9.55462853563415,scale:36111.909643}),new e({level:15,resolution:4.77731426794937,scale:18055.954822}),new e({level:16,resolution:2.38865713397468,scale:9027.977411}),new e({level:17,resolution:1.19432856685505,scale:4513.988705}),new e({level:18,resolution:.597164283559817,scale:2256.994353}),new e({level:19,resolution:.298582141647617,scale:1128.497176}),new e({level:20,resolution:.1492910708238085,scale:564.248588})]}),this.key=null,this.style="road",this.culture="en-US",this.region=null,this.portalUrl=null,this.hasAttributionData=!0}get bingMetadata(){return this._get("bingMetadata")}set bingMetadata(t){this._set("bingMetadata",t)}get copyright(){return c(this.bingMetadata)?this.bingMetadata.copyright:null}get operationalLayerType(){return u.toJSON(this.style)}get bingLogo(){return c(this.bingMetadata)?this.bingMetadata.brandLogoUri:null}load(t){return this.key?this.addResolvingPromise(this._getMetadata()):this.portalUrl?this.addResolvingPromise(this._getPortalBingKey().then(()=>this._getMetadata())):this.addResolvingPromise(Promise.reject(new n("bingmapslayer:load","Bing layer must have bing key."))),Promise.resolve(this)}getTileUrl(t,a,i){if(!this.loaded||b(this.bingMetadata))return null;const l=this.bingMetadata.resourceSets[0].resources[0],g=l.imageUrlSubdomains[a%l.imageUrlSubdomains.length],p=this._getQuadKey(t,a,i);return l.imageUrl.replace("{subdomain}",g).replace("{quadkey}",p)}async fetchAttributionData(){return this.load().then(()=>b(this.bingMetadata)?null:{contributors:this.bingMetadata.resourceSets[0].resources[0].imageryProviders.map(t=>({attribution:t.attribution,coverageAreas:t.coverageAreas.map(a=>({zoomMin:a.zoomMin,zoomMax:a.zoomMax,score:1,bbox:[a.bbox[0],a.bbox[1],a.bbox[2],a.bbox[3]]}))}))})}_getMetadata(){const t={road:"roadOnDemand",aerial:"aerial",hybrid:"aerialWithLabelsOnDemand"}[this.style];return h(`${k}/REST/v1/Imagery/Metadata/${t}`,{responseType:"json",query:{include:"ImageryProviders",uriScheme:"https",key:this.key,suppressStatus:!0,output:"json",culture:this.culture,userRegion:this.region}}).then(a=>{const i=a.data;if(i.statusCode!==200)throw new n("bingmapslayer:getmetadata",i.statusDescription);if(this.bingMetadata=i,this.bingMetadata.resourceSets.length===0)throw new n("bingmapslayer:getmetadata","no bing resourcesets");if(this.bingMetadata.resourceSets[0].resources.length===0)throw new n("bingmapslayer:getmetadata","no bing resources")}).catch(a=>{throw new n("bingmapslayer:getmetadata",a.message)})}_getPortalBingKey(){return h(this.portalUrl??"",{responseType:"json",authMode:"no-prompt",query:{f:"json"}}).then(t=>{if(!t.data.bingKey)throw new n("bingmapslayer:getportalbingkey","The referenced Portal does not contain a valid bing key");this.key=t.data.bingKey}).catch(t=>{throw new n("bingmapslayer:getportalbingkey",t.message)})}_getQuadKey(t,a,i){let l="";for(let g=t;g>0;g--){let p=0;const d=1<<g-1;i&d&&(p+=1),a&d&&(p+=2),l+=p.toString()}return l}};o([s({json:{read:!1,write:!1},value:null})],r.prototype,"bingMetadata",null),o([s({json:{read:!1,write:!1},value:"bing-maps",readOnly:!0})],r.prototype,"type",void 0),o([s({type:m})],r.prototype,"tileInfo",void 0),o([s({type:String,readOnly:!0,json:{read:!1,write:!1}})],r.prototype,"copyright",null),o([s({type:String,json:{write:!1,read:!1}})],r.prototype,"key",void 0),o([s({type:u.apiValues,nonNullable:!0,json:{read:{source:"layerType",reader:u.read}}})],r.prototype,"style",void 0),o([s({type:["BingMapsAerial","BingMapsHybrid","BingMapsRoad"]})],r.prototype,"operationalLayerType",null),o([s({type:String,json:{write:!1,read:!1}})],r.prototype,"culture",void 0),o([s({type:String,json:{write:!1,read:!1}})],r.prototype,"region",void 0),o([s({type:String,json:{write:!0,read:!0}})],r.prototype,"portalUrl",void 0),o([s({type:Boolean,json:{write:!1,read:!1}})],r.prototype,"hasAttributionData",void 0),o([s({type:String,readOnly:!0})],r.prototype,"bingLogo",null),r=o([M("esri.layers.BingMapsLayer")],r);const A=r;export{A as default};
