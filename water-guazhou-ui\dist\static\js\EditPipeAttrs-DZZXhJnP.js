const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["static/js/wfsTransactionUtils-YwEioX_z.js","static/js/AnimatedLinesLayer-B2VbV4jv.js","static/js/Point-WxyopZva.js","static/js/index-r0dFAfgr.js","static/css/index-ByiGXvnH.css","static/js/MapView-DaoQedLH.js","static/js/widget-BcWKanF2.js","static/js/pe-B8dP0-Ut.js","static/js/GraphicsLayer-DTrBRwJQ.js","static/js/dehydratedFeatures-CEuswj7y.js","static/js/enums-B5k73o5q.js","static/js/plane-BhzlJB-C.js","static/js/sphere-NgXH-gLx.js","static/js/mat3f64-BVJGbF0t.js","static/js/mat4f64-BCm7QTSd.js","static/js/quatf64-QCogZAoR.js","static/js/elevationInfoUtils-5B4aSzEU.js","static/js/quat-CM9ioDFt.js","static/js/TileLayer-B5vQ99gG.js","static/js/ArcGISCachedService-CQM8IwuM.js","static/js/TilemapCache-BPMaYmR0.js","static/js/Version-Q4YOKegY.js","static/js/QueryTask-B4og_2RG.js","static/js/executeForIds-BLdIsxvI.js","static/js/sublayerUtils-bmirCD0I.js","static/js/imageBitmapUtils-Db1drMDc.js","static/js/scaleUtils-DgkF6NQH.js","static/js/ExportImageParameters-BiedgHNY.js","static/js/floorFilterUtils-DZ5C6FQv.js","static/js/WMSLayer-mTaW758E.js","static/js/crsUtils-DAndLU68.js","static/js/ExportWMSImageParameters-CGwvCiFd.js","static/js/BaseTileLayer-DM38cky_.js","static/js/commonProperties-DqNQ4F00.js","static/js/project-DUuzYgGl.js","static/js/QueryEngineResult-D2Huf9Bb.js","static/js/quantizationUtils-DtI9CsYu.js","static/js/WhereClause-CNjGNHY9.js","static/js/executionError-BOo4jP8A.js","static/js/utils-DcsZ6Otn.js","static/js/generateRendererUtils-Bt0vqUD2.js","static/js/projectionSupport-BDUl30tr.js","static/js/json-Wa8cmqdu.js","static/js/utils-dKbgHYZY.js","static/js/LayerView-BSt9B8Gh.js","static/js/Container-BwXq1a-x.js","static/js/definitions-826PWLuy.js","static/js/enums-BDQrMlcz.js","static/js/Texture-BYqObwfn.js","static/js/Util-sSNWzwlq.js","static/js/pixelRangeUtils-Dr0gmLDH.js","static/js/number-Q7BpbuNy.js","static/js/coordinateFormatter-C2XOyrWt.js","static/js/earcut-BJup91r2.js","static/js/normalizeUtilsSync-NMksarRY.js","static/js/TurboLine-CDscS66C.js","static/js/enums-L38xj_2E.js","static/js/util-DPgA-H2V.js","static/js/RefreshableLayerView-DUeNHzrW.js","static/js/vec2-Fy2J07i2.js"])))=>i.map(i=>d[i]);
import{d as pe,c as A,r as O,a1 as z,b as c,a8 as de,s as ce,S as W,a3 as me,u as ue,l as fe,bH as ye,Q as ge,g as X,h as Q,F as B,p as j,i as P,an as he,q as R,_ as ve,da as be,X as Ie,C as we}from"./index-r0dFAfgr.js";import{_ as Fe}from"./InlineForm.vue_vue_type_style_index_0_lang-s-ANlzyw.js";import{_ as xe}from"./FormTableColumnFilter-BT7pLXIC.js";import{p as Se}from"./AnimatedLinesLayer-B2VbV4jv.js";import{s as U}from"./FeatureHelper-Da16o0mu.js";import"./pe-B8dP0-Ut.js";import"./MapView-DaoQedLH.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./Point-WxyopZva.js";import{f as _e,g as ke,a as Ce}from"./LayerHelper-Cn-iiqxI.js";import{q as Te,c as De,g as Le}from"./QueryHelper-ILO3qZqg.js";import{GetFieldConfig as Ge,GetFieldUniqueValue as Ee}from"./fieldconfig-Bk3o1wi7.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./geometryEngineBase-BhsKaODW.js";import{GetFieldConfig as qe,GetFieldValueByGeoserver as Ne,QueryByPolygon as Ae}from"./wfsUtils-DXofo3da.js";import Be from"./RightDrawerMap-D5PhmGFO.js";import Pe from"./DetailTable-Dc-xAY7v.js";import{i as Oe}from"./config-fy91bijz.js";import{P as Y}from"./gisSetting-CQEP-Q3N.js";import{E as K,c as Z,d as ee}from"./config-DncLSA-r.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./project-DUuzYgGl.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./pipe-nogVzCHG.js";import"./executeForIds-BLdIsxvI.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./ArcView-DpMnCY82.js";import"./ViewHelper-BGCZjxXH.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./ArcStationWarning-BF9YrSzF.js";/* empty css                         */import"./useWaterPoint-Bv0z6ym6.js";import"./DateFormatter-Bm9a68Ax.js";import"./zhandian-YaGuQZe6.js";import"./useStation-DJgnSZIA.js";import"./DrawerBox-CLde5xC8.js";import"./SideDrawer-CBntChyn.js";import"./PipeDetail-CTBPYFJW.js";import"./Search-NSrhrIa_.js";import"./ArcBRTools-BO92yznB.js";import"./PipeLength.vue_vue_type_script_setup_true_lang-BZfUsvDf.js";import"./arcWidgetButton-0glIxrt7.js";import"./StatisticsHelper-D-s_6AyQ.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./usePipelineGroup-Ba1pmWz_.js";import"./GroupLayer-DhhN3FyN.js";import"./lazyLayerLoader-DbM9sT1W.js";import"./WFSLayer-1TtJp3nx.js";import"./clientSideDefaults-VQhQaYxh.js";import"./QueryEngineCapabilities-Dk3NJwmm.js";import"./wfsUtils-Du031XaC.js";import"./geojson-MBFu2-HZ.js";import"./xmlUtils-CtUoQO7q.js";import"./ListWindow-WS05QqV0.js";import"./PopLayout-BP55MvL7.js";import"./PoiSearchV2-D7yNeLuv.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./URLHelper-B9aplt5w.js";import"./useLayerList-DmEwJ-ws.js";import"./useScaleBar-Beed-z91.js";import"./index-DeAQQ1ej.js";const Re={class:"table-box"},Ue=pe({__name:"EditPipeAttrs",setup($e){const x=window.GIS_SERVER_SWITCH===!0,T=A(),i=O({layerIds:[],layerInfos:[],loading:!1,uniqueing:!1,curLayerFields:[]}),s={},m=A(),E=O({labelPosition:"top",group:[{fieldset:{desc:"绘制工具"},fields:[{type:"btn-group",btns:[{perm:!0,text:"",type:"default",size:"large",title:"绘制点",disabled:()=>i.loading,iconifyIcon:"gis:circle",click:()=>q("point")},{perm:!0,text:"",type:"default",size:"large",title:"绘制多边形",disabled:()=>i.loading,iconifyIcon:"mdi:shape-polygon-plus",click:()=>q("polygon")},{perm:!0,text:"",type:"default",size:"large",title:"绘制矩形",disabled:()=>i.loading,iconifyIcon:"ep:crop",click:()=>q("rectangle")},{perm:!0,text:"",type:"default",size:"large",title:"绘制椭圆",disabled:()=>i.loading,iconifyIcon:"mdi:ellipse-outline",click:()=>q("circle")},{perm:!0,text:"",type:"default",size:"large",title:"清除图形",disabled:()=>i.loading,iconifyIcon:"ep:delete",click:()=>oe()}]}]},{id:"layer",fieldset:{desc:"选择图层"},fields:[{type:"tree",checkStrictly:!0,showCheckbox:!0,field:"layerid",nodeKey:"value",options:[],handleCheckChange:(e,r)=>{var t,o;r&&(m.value&&(m.value.dataForm.layerid=[e.value]),x?i.curLayerInfo=i.layerInfos.find(n=>n.value===e.value||n.label===e.value):i.curLayerInfo=i.layerInfos.find(n=>n.layerid===e.value),(t=D.value)!=null&&t.dataForm&&(D.value.dataForm.field=""),(o=T.value)==null||o.refreshDetail())}}]},{id:"layer",fieldset:{desc:"图层字段"},fields:[{type:"list",data:[],className:"sql-list-wrapper",setDataBy:"layerid",setData:async(e,r)=>{var o,n,u,f;if(!((o=r.layerid)!=null&&o.length)){i.curLayerFields.length=0;return}const t=r.layerid[0];if(x)try{console.log("使用GeoServer模式获取字段信息:",t);const a=await qe(t);if(a&&a.data&&a.data.featureTypes&&a.data.featureTypes[0]){const p=a.data.featureTypes[0].properties||[];e.data=p;const g=["START_SID","END_SID","SID","OBJECTID","PIPELENGTH","X","Y","the_geom","geom"],y=p.filter(d=>{var l;return g.indexOf(d.name)===-1&&d.type!=="gml:GeometryPropertyType"&&["int","long","double","float","string","date"].indexOf(((l=d.type.split(":")[1])==null?void 0:l.toLowerCase())||"")!==-1}).map((d,l)=>{var b;const I=((b=d.type.split(":")[1])==null?void 0:b.toLowerCase())||"";let v="input";return["int","long","double","float"].includes(I)?v="input-number":I==="date"&&(v="date"),{id:l,field:d.name,label:d.name,type:v,disabled:!1,readonly:!1}}).sort((d,l)=>d.id-l.id);i.curLayerFields=z(y||[],{id:"field",label:"label",value:"field"}),H(y)}else console.error("无法获取GeoServer字段信息")}catch(a){console.error("获取GeoServer字段信息时出错:",a)}else{const a=(n=i.layerInfos.find(l=>l.layerid===t))==null?void 0:n.layername;if(!a)return;const p=await Ge(a);e.data=(f=(u=p.data)==null?void 0:u.result)==null?void 0:f.rows;const g=await Oe(t),y=["START_SID","END_SID","SID","OBJECTID","PIPELENGTH","X","Y"],d=g.filter(l=>y.indexOf(l.field)===-1&&l.disabled!==!0&&l.readonly!==!0&&["input","input-number","number","date"].indexOf(l.type)!==-1).map(l=>(l.id=e.data.findIndex(I=>I.name===l.field),l)).sort((l,I)=>l.id-I.id);i.curLayerFields=z(d||[],{id:"field",label:"label",value:"field"}),H(d)}},displayField:x?"name":"alias",valueField:"name",highlightCurrentRow:!0,nodeClick:e=>{i.curFieldNode=e,S(x?`"${e.name}"`:e.name)}}]},{id:"field-construct",fieldset:{desc:"构建查询语句"},fields:[{type:"btn-group",size:"small",style:{width:"40%",display:"flex",flexWrap:"wrap"},className:"sql-btns-wrapper",btns:[{perm:!0,text:"=",styles:{margin:"6px",width:"50px"},click:()=>{S("=")}},{perm:!0,text:"模糊",styles:{margin:"6px",width:"50px"},click:()=>{S("like '%替换此处%'")}},{perm:!0,text:">",styles:{margin:"6px",width:"50px"},click:()=>{S(">")}},{perm:!0,text:"<",styles:{margin:"6px",width:"50px"},click:()=>{S("<")}},{perm:!0,text:"非",styles:{margin:"6px",width:"50px"},click:()=>{S("<>")}},{perm:!0,text:"并且",styles:{margin:"6px",width:"50px"},click:()=>{S("and")}},{perm:!0,text:"或者",styles:{margin:"6px",width:"50px"},click:()=>{S("or")}},{perm:!0,text:"%",styles:{margin:"6px",width:"50px"},click:()=>{S("%")}}],extraFormItem:[{type:"list",wrapperStyle:{width:"60%",height:"144px"},className:"sql-list-wrapper",field:"uniqueValue",data:[],nodeClick:e=>{S("'"+e+"'")},filters:[{type:"btn-group",btns:[{perm:!0,text:()=>i.uniqueing?"正在获取唯一值":"获取唯一值",loading:()=>i.uniqueing,disabled:()=>i.loading,styles:{width:"100%",borderRadius:"0"},click:()=>re()}]}]}]}]},{fieldset:{desc:"组合查询条件"},fields:[{type:"textarea",field:"sql",placeholder:"OBJECTID > 0"},{type:"btn-group",itemContainerStyle:{marginBottom:"8px"},btns:[{perm:!0,text:"清除组合条件",type:"danger",disabled:()=>i.loading,click:()=>te(),styles:{width:"100%"}}]}]},{fields:[{type:"btn-group",btns:[{perm:!0,text:"查询",styles:{width:"100%"},click:()=>N()},{perm:!0,text:"重置",type:"default",styles:{width:"100%"},click:()=>ae()}]}]}]}),S=e=>{var t;if(!m.value)return;(t=m.value)!=null&&t.dataForm||(m.value.dataForm={});const r=m.value.dataForm.sql||" ";m.value.dataForm.sql=r+e+" "},re=async()=>{var e,r;try{if(!i.curFieldNode){c.warning("请先选择字段");return}const t=i.curFieldNode.name||i.curFieldNode.field;if(!t){c.warning("无法获取字段名称");return}if(console.log("获取字段唯一值:",t),!m.value||!m.value.dataForm){c.warning("表单对象不存在");return}const o=m.value.dataForm.layerid;if(!(o!=null&&o.length)){c.warning("请先选择一个图层");return}i.uniqueing=!0;const n=E.group.find(a=>a.id==="field-construct");if(!n||!n.fields||!n.fields[0]){console.error("extraFormItemGroup不存在或为空"),c.error("无法获取唯一值列表组件"),i.uniqueing=!1;return}const u=n.fields[0].extraFormItem;if(!u||!u[0]){console.error("extraFormItem不存在或为空"),c.error("无法获取唯一值列表组件"),i.uniqueing=!1;return}const f=u[0];if(!f){console.error("无法获取列表字段"),c.error("无法获取唯一值列表组件"),i.uniqueing=!1;return}if(x)try{const a=((e=i.curLayerInfo)==null?void 0:e.layerTitle)||((r=i.curLayerInfo)==null?void 0:r.label)||o[0];console.log("使用GeoServer模式获取唯一值:",a,t);const p=await Ne({layerName:a,fiedName:t});if(!p||!p.data){console.error("获取GeoServer唯一值响应无效:",p),c.error("获取唯一值失败"),i.uniqueing=!1;return}const g=p.data;if(console.log("获取到的GeoServer数据:",g),!g.features||!Array.isArray(g.features)){console.error("无效的GeoServer数据格式:",g),c.warning("无法获取唯一值"),i.uniqueing=!1;return}const y=new Set;g.features.forEach(l=>{l&&l.properties&&y.add(l.properties[t])});const d=Array.from(y).filter(l=>l!=null);console.log("提取的唯一值:",d),f.data=d}catch(a){console.error("获取GeoServer字段唯一值时出错:",a),c.error("获取唯一值失败")}else try{console.log("使用ArcGIS模式获取唯一值:",o[0],t);const a=await Ee({layerid:o[0],field_name:t});if(!a||!a.data||!a.data.result||!a.data.result.rows){console.error("获取ArcGIS唯一值响应无效:",a),c.error("获取唯一值失败"),i.uniqueing=!1;return}f.data=a.data.result.rows}catch(a){console.error("获取ArcGIS字段唯一值时出错:",a),c.error("获取唯一值失败")}}catch(t){console.error("获取唯一值时出错:",t),c.error("获取唯一值失败")}finally{i.uniqueing=!1}},te=()=>{var e;(e=m.value)!=null&&e.dataForm&&(m.value.dataForm.sql="")},oe=()=>{var e;(e=s.graphicsLayer)==null||e.removeAll(),s.graphic=void 0},q=e=>{var r,t,o,n,u,f,a;(r=s.sketchCompHandler)==null||r.remove(),(t=s.sketchUpdateHandler)==null||t.remove(),(o=s.sketch)==null||o.destroy(),(n=s.graphicsLayer)==null||n.removeAll(),s.sketch=new Se({view:s.view,layer:s.graphicsLayer,polygonSymbol:U("polygon",{color:[255,0,0,.3],outlineColor:[255,0,0,1]}),polylineSymbol:U("polyline"),pointSymbol:U("point")}),(u=s.sketch)==null||u.create(e),s.sketchUpdateHandler=(f=s.sketch)==null?void 0:f.on("update",p=>{p.state==="complete"&&(console.log(p.graphics[0]),s.graphic=p.graphics[0])}),s.sketchCompHandler=(a=s.sketch)==null?void 0:a.on("create",p=>{p.state==="complete"&&(console.log(p.graphic),s.graphic=p.graphic)})},$=A(),N=async()=>{var t,o,n,u,f,a,p,g,y,d,l,I,v,b;(t=$.value)==null||t.toggleCustomDetail(!0);const e=((o=m.value)==null?void 0:o.dataForm.layerid)||[];let r=(n=s.graphic)==null?void 0:n.geometry;if((r==null?void 0:r.type)==="point"&&(r=(await Te(De({bufferSpatialReference:(u=s.view)==null?void 0:u.spatialReference,distances:[12],geometries:[r],outSpatialReference:(f=s.view)==null?void 0:f.spatialReference,geodesic:!0,unit:"meters",unionResults:!1})))[0]),x)try{console.log("使用GeoServer模式查询:",e,r==null?void 0:r.rings),r!=null&&r.rings&&r.rings[0]&&r.rings[0][0]!==r.rings[0][r.rings[0].length-1]&&r.rings[0].push([...r.rings[0][0]]);const F=await Ae(e,r==null?void 0:r.rings[0],(p=(a=m.value)==null?void 0:a.dataForm)==null?void 0:p.sql);console.log("查询响应:",F);let _=F.data.features;if(console.log("查询到的要素:",_),_&&_.length>0){console.log("查询结果:",_);const L=new Set;_.forEach(h=>{if(h.id){const w=h.id.split(".")[0];L.add(w)}});const C=[];L.forEach(h=>{const w=_.filter(G=>G.id&&G.id.split(".")[0]===h);w.length>0&&C.push({name:h,label:`${h}(${w.length})`,data:w})}),C.length>0?(d=T.value)==null||d.refreshDetail(s.view,{layerid:e[0],layername:(g=C[0])==null?void 0:g.name,oids:(y=C[0])==null?void 0:y.data}):c.info("查询结果为空")}else c.info("查询结果为空")}catch(F){console.error("执行GeoServer查询时出错:",F),c.error("查询失败，请检查查询条件是否正确")}else{const F={where:((l=m.value)==null?void 0:l.dataForm.sql)||"1=1",geometry:r},_=await Le(e,i.layerInfos,F);(b=T.value)==null||b.refreshDetail(s.view,{layerid:e[0],layername:(I=_[0])==null?void 0:I.name,oids:(v=_[0])==null?void 0:v.data},{queryParams:F})}},ae=()=>{var e;(e=m.value)==null||e.resetForm()},k=O({labelPosition:"right",labelWidth:150,group:[{fields:[{labelWidth:150,type:"select",field:"field",label:"属性批量编辑： 字段",options:de(()=>i.curLayerFields)},{type:"btn-group",btns:[{perm:!0,text:"更新",loading:()=>!!k.submitting,disabled:e=>!e.field,svgIcon:ce(be),click:()=>ie()}]}]}]}),D=A(),H=e=>{if(console.log("重置内联表单配置:",e,x),!e||!e.length){console.warn("没有可用的字段");return}const r=e.map(t=>{const o={...t};return x&&(!o.field&&o.name&&(o.field=o.name),o.label||(o.label=o.field||o.name||"")),o.hidden=!0,o.handleHidden=(n,u,f)=>{f.hidden=!n.field||n.field!==o.field},o});try{k.group[0].fields.splice(1,k.group[0].fields.length-2,...r),console.log("内联表单字段更新成功")}catch(t){console.error("更新内联表单字段时出错:",t)}},ie=async()=>{var n,u,f,a,p;const e=(u=(n=m.value)==null?void 0:n.dataForm.layerid)==null?void 0:u[0];if(e===void 0)return;const r=(f=T.value)==null?void 0:f.staticState.tabFeatures;if(!(r!=null&&r.length))return;const t=(a=D.value)==null?void 0:a.dataForm.field,o=(p=D.value)==null?void 0:p.dataForm[t];if(!t){c.warning("请选择字段");return}if(o!==0&&!o)try{await W("当前字段值为空，确定提交？","提示信息")}catch{return}W("应用到空间数据库？","提示信息").then(()=>{var g;if(x)try{console.log("使用GeoServer模式更新属性:",e,t,o),k.submitting=!0,me(()=>import("./wfsTransactionUtils-YwEioX_z.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59])).then(async({updateFeatureAttributes:y})=>{var d,l,I,v;try{const b={[t]:o};console.log("要更新的属性:",b),console.log("要素对象结构:",r[0]),console.log("要素对象属性:",Object.keys(r[0]));const F=r.map(h=>{var G,V,J;const w=h.id||((G=h.attributes)==null?void 0:G.OBJECTID)||((V=h.properties)==null?void 0:V.id)||((J=h.properties)==null?void 0:J.OBJECTID);if(console.log("要素ID:",w),typeof w=="string"){const M=w.split(".");return M.length>1?M[1]:w}else return w}),_=((d=i.curLayerFields.find(h=>h.value===t))==null?void 0:d.label)||t,L=((l=i.curLayerInfo)==null?void 0:l.layerTitle)||((I=i.curLayerInfo)==null?void 0:I.label)||e;console.log("使用图层名称进行更新:",L);const C=await y(L,r,b);if(console.log("GeoServer更新响应:",C),C.data&&C.data.includes("<wfs:totalUpdated>")){const h=((v=C.data.match(/<wfs:totalUpdated>(\d+)<\/wfs:totalUpdated>/))==null?void 0:v[1])||"0";parseInt(h)>0?(c.success(`属性更新成功，共更新${h}个要素`),Y({optionName:K.SHUXINBIANJI,type:Z.BASICGIS,content:`对ID为${F.join("、")}的${e}，统一将属性【${_}】修改为【${o}】`,optionType:ee.UPDATE}).catch(()=>{console.log("生成gis操作日志失败")}),r.forEach(w=>{w.properties&&(w.properties[t]=o)}),N()):c.warning("没有要素被更新")}else c.error("属性更新失败"),console.error("更新响应不符合预期:",C.data)}catch(b){console.error("WFS-T更新操作失败:",b),c.error("属性更新失败")}finally{k.submitting=!1}}).catch(y=>{console.error("加载WFS-T工具函数失败:",y),c.error("加载属性编辑功能失败"),k.submitting=!1})}catch(y){console.error("GeoServer模式下更新属性时出错:",y),c.error("属性更新失败"),k.submitting=!1}else{const y=r.map(v=>{var F;const b=v;return b.attributes[t]=o,b.attributes.UPDATEDBY=(F=ue().user)==null?void 0:F.firstName,b.attributes.UPDATEDDATE=fe().format(ye),b}),d=(g=i.curLayerFields.find(v=>v.value===t))==null?void 0:g.label,l=y.map(v=>v.attributes.OBJECTID),I=i.curLayerInfo.layername;k.submitting=!0,_e(e,{updateFeatures:y||[]}).then(()=>{Y({optionName:K.SHUXINBIANJI,type:Z.BASICGIS,content:`对OBJECTID为${l.join("、")}的${I}，统一将属性【${d}】修改为【${o}】`,optionType:ee.UPDATE}).catch(()=>{console.log("生成gis操作日志失败")}),N()}).finally(()=>{k.submitting=!1})}}).catch(()=>{})},le=e=>{var r,t;x?(r=T.value)==null||r.extentTo(s.view,e.OBJECTID):(t=T.value)==null||t.extentTo(s.view,e.OBJECTID)},se=()=>{var e;if(x){const r=E.group[1].fields[0];let o=((e=s.view)==null?void 0:e.layerViews.items[0].layer.sublayers).items.map(n=>({label:n.name,value:n.name}));r.options=o,m.value&&(m.value.dataForm.layerid=i.layerIds)}else i.layerIds=Ce(s.view),Ie(i.layerIds).then(r=>{var u,f;i.layerInfos=((f=(u=r.data)==null?void 0:u.result)==null?void 0:f.rows)||[];const t=E.group[1].fields[0],o=i.layerInfos.filter(a=>a.geometrytype==="esriGeometryPoint").map(a=>({label:a.layername,value:a.layerid,data:a})),n=i.layerInfos.filter(a=>a.geometrytype==="esriGeometryPolyline").map(a=>({label:a.layername,value:a.layerid,data:a}));t&&(t.options=[{label:"管线类",value:-2,children:n,disabled:!0},{label:"管点类",value:-1,children:o,disabled:!0}]),m.value&&(m.value.dataForm.layerid=i.layerIds.slice(0,1),i.curLayerInfo=i.layerInfos.find(a=>a.layerid===i.layerIds[0]))})},ne=async e=>{s.view=e,s.graphicsLayer=ke(s.view,{id:"pipe-add",title:"属性编辑"}),setTimeout(()=>{se()},1e3)};return ge(()=>{var e,r,t,o;(e=s.sketchCompHandler)==null||e.remove(),(r=s.sketchUpdateHandler)==null||r.remove(),(t=s.sketch)==null||t.destroy(),(o=s.graphicsLayer)==null||o.removeAll()}),(e,r)=>{const t=ve,o=xe,n=Fe;return X(),Q(Be,{ref_key:"refMap",ref:$,title:"属性编辑","detail-max-min":!0,"detail-extra":!0,onMapLoaded:ne},{"detail-header":B(()=>r[0]||(r[0]=[j("span",null,"查询结果",-1)])),"detail-extra":B(()=>{var u;return[(u=P(T))!=null&&u.TableConfig_Detail.columns?(X(),Q(o,{key:0,columns:P(T).TableConfig_Detail.columns,"show-tooltip":!0},null,8,["columns"])):he("",!0)]}),"detail-default":B(()=>[R(n,{ref_key:"refFormInline",ref:D,config:P(k)},null,8,["config"]),j("div",Re,[R(Pe,{ref_key:"refDetailTable",ref:T,onRowClick:le,onRefreshData:N},null,512)])]),default:B(()=>[R(t,{ref_key:"refForm",ref:m,config:P(E)},null,8,["config"])]),_:1},512)}}}),Qt=we(Ue,[["__scopeId","data-v-783c1435"]]);export{Qt as default};
