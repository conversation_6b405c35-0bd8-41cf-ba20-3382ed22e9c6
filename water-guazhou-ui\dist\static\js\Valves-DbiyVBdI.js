import{d as I,c as g,r as x,b as s,g as _,h as C,F as N,q as O,i as w,_ as L,X as D}from"./index-r0dFAfgr.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import{a as M}from"./LayerHelper-Cn-iiqxI.js";import{g as S}from"./QueryHelper-ILO3qZqg.js";import{GetFieldConfig as V,GetFieldUniqueValue as B}from"./fieldconfig-Bk3o1wi7.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import R from"./RightDrawerMap-D5PhmGFO.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./project-DUuzYgGl.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./pipe-nogVzCHG.js";import"./executeForIds-BLdIsxvI.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./ArcView-DpMnCY82.js";import"./ViewHelper-BGCZjxXH.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./ArcStationWarning-BF9YrSzF.js";/* empty css                         */import"./useWaterPoint-Bv0z6ym6.js";import"./DateFormatter-Bm9a68Ax.js";import"./zhandian-YaGuQZe6.js";import"./useStation-DJgnSZIA.js";import"./DrawerBox-CLde5xC8.js";import"./SideDrawer-CBntChyn.js";import"./PipeDetail-CTBPYFJW.js";import"./Search-NSrhrIa_.js";import"./FormTableColumnFilter-BT7pLXIC.js";import"./config-fy91bijz.js";import"./ArcBRTools-BO92yznB.js";import"./PipeLength.vue_vue_type_script_setup_true_lang-BZfUsvDf.js";import"./arcWidgetButton-0glIxrt7.js";import"./StatisticsHelper-D-s_6AyQ.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./wfsUtils-DXofo3da.js";import"./usePipelineGroup-Ba1pmWz_.js";import"./GroupLayer-DhhN3FyN.js";import"./lazyLayerLoader-DbM9sT1W.js";import"./WFSLayer-1TtJp3nx.js";import"./clientSideDefaults-VQhQaYxh.js";import"./QueryEngineCapabilities-Dk3NJwmm.js";import"./wfsUtils-Du031XaC.js";import"./geojson-MBFu2-HZ.js";import"./xmlUtils-CtUoQO7q.js";import"./ListWindow-WS05QqV0.js";import"./PopLayout-BP55MvL7.js";import"./PoiSearchV2-D7yNeLuv.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./URLHelper-B9aplt5w.js";import"./useLayerList-DmEwJ-ws.js";import"./useScaleBar-Beed-z91.js";const $r=I({__name:"Valves",setup(G){const o=g(),d=g(),c={},r=x({loading:!1,tabs:[],layerIds:[],layerInfos:[]}),u=x({group:[{fieldset:{desc:"选择字段"},fields:[{type:"list",data:[],className:"sql-list-wrapper",setData:async(t,e)=>{var l,n,f,y;if(!((l=e.layerid)!=null&&l.length))return;const i=e.layerid[0],p=(n=r.layerInfos.find(q=>q.layerid===i))==null?void 0:n.layername;if(!p)return;const m=await V(p);t.data=(y=(f=m.data)==null?void 0:f.result)==null?void 0:y.rows},setDataBy:"layerid",displayField:"alias",valueField:"name",highlightCurrentRow:!0,nodeClick:t=>{r.curFieldNode=t,a(t.name)}}]},{id:"field-construct",fieldset:{desc:"属性过滤"},fields:[{type:"btn-group",size:"small",style:{width:"40%",display:"flex",flexWrap:"wrap"},className:"sql-btns-wrapper",btns:[{perm:!0,text:"=",styles:{margin:"6px",width:"50px"},click:()=>{a("=")}},{perm:!0,text:"模糊",styles:{margin:"6px",width:"50px"},click:()=>{a("like '%替换此处%'")}},{perm:!0,text:">",styles:{margin:"6px",width:"50px"},click:()=>{a(">")}},{perm:!0,text:"<",styles:{margin:"6px",width:"50px"},click:()=>{a("<")}},{perm:!0,text:"非",styles:{margin:"6px",width:"50px"},click:()=>{a("<>")}},{perm:!0,text:"并且",styles:{margin:"6px",width:"50px"},click:()=>{a("and")}},{perm:!0,text:"或者",styles:{margin:"6px",width:"50px"},click:()=>{a("or")}},{perm:!0,text:"%",styles:{margin:"6px",width:"50px"},click:()=>{a("%")}}],extraFormItem:[{type:"list",wrapperStyle:{width:"60%",height:"144px"},className:"sql-list-wrapper",field:"uniqueValue",data:[],nodeClick:t=>{a("'"+t+"'")},filters:[{type:"btn-group",btns:[{perm:!0,text:()=>r.curOperate==="uniqueing"?"正在获取唯一值":"获取唯一值",loading:()=>r.curOperate==="uniqueing",disabled:()=>r.curOperate==="detailing",styles:{width:"100%",borderRadius:"0"},click:()=>F()}]}]}]}]},{fieldset:{desc:"组合查询条件"},fields:[{type:"textarea",field:"sql",placeholder:"OBJECTID > 0"},{type:"btn-group",itemContainerStyle:{marginBottom:"8px"},btns:[{perm:!0,text:"清除组合条件",type:"danger",disabled:()=>r.curOperate==="detailing",click:()=>h(),styles:{width:"100%"}}]},{type:"btn-group",btns:[{perm:!0,text:()=>r.loading?"正在查询":"查询",loading:()=>r.loading,disabled:()=>r.layerIds.length===0,click:()=>b(),styles:{width:"100%"}}]}]}],labelPosition:"top",gutter:12,defaultValue:{layerid:[]}}),a=t=>{var i;if(!o.value)return;(i=o.value)!=null&&i.dataForm||(o.value.dataForm={});const e=o.value.dataForm.sql||"";o.value.dataForm.sql=e+t+""},h=()=>{var t;(t=o.value)!=null&&t.dataForm&&(o.value.dataForm.sql="")},F=async()=>{var e,i;if(!r.curFieldNode)return;const t=(e=o.value)==null?void 0:e.dataForm.layerid;if(!(t!=null&&t.length)){s.warning("请先选择一个图层");return}r.curOperate="uniqueing";try{const p=await B({layerid:t[0],field_name:r.curFieldNode.name}),m=(i=u.group.find(n=>n.id==="field-construct"))==null?void 0:i.fields[0].extraFormItem,l=m&&m[0];l&&(l.data=p.data.result.rows)}catch{s.error("获取唯一值失败")}r.curOperate=""},v=async()=>{var e,i;if(r.layerIds=M(c.view,void 0,void 0,"阀门"),!r.layerIds.length){s.warning("当前没有阀门");return}const t=await D(r.layerIds);r.layerInfos=((i=(e=t.data)==null?void 0:e.result)==null?void 0:i.rows)||[],o.value&&(o.value.dataForm.layerid=r.layerIds)},b=async()=>{var e,i,p;r.loading=!0;const{layerid:t}=((e=o.value)==null?void 0:e.dataForm)||{};if(!(t!=null&&t.length)){s.warning("阀门服务不存在"),r.loading=!1;return}try{r.tabs=await S(t||[],r.layerInfos,{where:((i=o.value)==null?void 0:i.dataForm.sql)||"1=1"}),(p=d.value)==null||p.refreshDetail(r.tabs)}catch(m){s.error(m.message),r.loading=!1}},k=async t=>{c.view=t,await v()};return(t,e)=>{const i=L;return _(),C(R,{ref_key:"refMap",ref:d,title:"阀门展示",onDetailRefreshed:e[0]||(e[0]=p=>w(r).loading=!1),onMapLoaded:k},{default:N(()=>[O(i,{ref_key:"refForm",ref:o,config:w(u)},null,8,["config"])]),_:1},512)}}});export{$r as default};
