/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.controller.maintenance;

import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.repository.query.Param;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.VO.AlarmLinkedUser;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.MaintainId;
import org.thingsboard.server.common.data.maintain.Maintain;
import org.thingsboard.server.common.data.relation.RelationTypeGroup;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.maintain.MaintainService;
import org.thingsboard.server.service.aspect.annotation.SysLog;

import java.util.List;

/**
 * 保养相关API，包含保养条件的创建，以及保养订单的修改，查询
 */

@RestController
@RequestMapping("/api/maintain")
public class MaintainController extends BaseController {

    @Autowired
    private MaintainService maintainService;

    /**
     * 新增保养设定
     *
     * @param
     * @return
     */
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS')")
    @PostMapping("/add")
    @SysLog(options = DataConstants.OPERATING_TYPE.OPERATING_TYPE_OPTIONS_MAINTAIN_ADD)
    public ResponseEntity<Object> add(@RequestBody Maintain maintain) throws ThingsboardException {
        JSONObject result = new JSONObject();
        HttpStatus httpStatus;
        // 校验参数
        if (maintain.getDeviceId() == null) {
            httpStatus = HttpStatus.BAD_REQUEST;
            result.put("err", "请指定保养设备");
            return ResponseEntity.status(httpStatus).body(result);
        }
        // 保存维修记录
        maintain.setTenantId(getTenantId());
        maintain = maintainService.addMaintain(maintain);
        return ResponseEntity.status(HttpStatus.OK).body(maintain);
    }

    /**
     * 根据tenantId查询保养设定
     *
     * @return
     */
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS')")
    @GetMapping("/findByTenant")
    @SysLog(detail = DataConstants.OPERATING_TYPE_MAINTAIN_GET)
    public ResponseEntity<Object> findByTenantId() throws ThingsboardException {
        List<Maintain> maintainList = maintainService.findByTenantId(getTenantId());
        return ResponseEntity.ok(maintainList);
    }


    /**
     * 根据tenantId查询保养设定
     *
     * @return
     */
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS')")
    @GetMapping("/findByProject")
    @SysLog(detail = DataConstants.OPERATING_TYPE_MAINTAIN_GET)
    public ResponseEntity<Object> findByProjectId(@Param("projectId")String projectId) throws ThingsboardException {
        List<Maintain> maintainList = maintainService.findByProjectId(projectId);
        return ResponseEntity.ok(maintainList);
    }

    /**
     * 删除设定
     *
     * @return
     */
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS')")
    @GetMapping("/delete/{id}")
    @SysLog(options = DataConstants.OPERATING_TYPE.OPERATING_TYPE_OPTIONS_MAINTAIN_DELETE)
    public ResponseEntity<Object> delete(@PathVariable("id") String maintainId) throws ThingsboardException {
        return ResponseEntity.ok(maintainService.delete(new MaintainId(UUIDConverter.fromString(maintainId))));
    }


    /**
     * 报警关联联系人
     * @param alarmLinkedUsers 报警关联人
     * @return 报警设置内容
     */
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN',  'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE', 'TENANT_SYS', 'CUSTOMER_USER')")
    @RequestMapping(value = "/maintain/linkedUser/{maintainId}", method = RequestMethod.POST)
    @ResponseBody
    @SysLog(options = DataConstants.OPERATING_TYPE.OPERATING_TYPE_OPTIONS_SETTING_ALARM_SAVE)
    public boolean saveAlarmJson(@PathVariable("maintainId")String maintainId, @RequestBody List<AlarmLinkedUser> alarmLinkedUsers) throws ThingsboardException {
        return alarmJsonService.linkedUser(alarmLinkedUsers,maintainId,getTenantId(), RelationTypeGroup.USER);
    }

    /**
     * 报警关联外部联系人
     * @param alarmLinkedUsers 报警关联人
     * @return 报警设置内容
     */
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN',  'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE', 'TENANT_SYS', 'CUSTOMER_USER')")
    @RequestMapping(value = "/maintain/linkedExtraUser/{maintainId}", method = RequestMethod.POST)
    @ResponseBody
    @SysLog(options = DataConstants.OPERATING_TYPE.OPERATING_TYPE_OPTIONS_SETTING_ALARM_SAVE)
    public boolean linkedAlarmJson(@PathVariable("maintainId")String alarmJsonId, @RequestBody List<AlarmLinkedUser> alarmLinkedUsers) throws ThingsboardException {
        return alarmJsonService.linkedUser(alarmLinkedUsers,alarmJsonId,getTenantId(),RelationTypeGroup.EXTAR_USER);
    }


    /**
     * 获取报警关联联系人
     * @return 报警设置内容
     */
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN',  'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE', 'TENANT_SYS', 'CUSTOMER_USER')")
    @GetMapping(value = "/maintain/linkedUser/{maintainId}")
    @ResponseBody
    @SysLog(options = DataConstants.OPERATING_TYPE.OPERATING_TYPE_OPTIONS_SETTING_ALARM_SAVE)
    public List<AlarmLinkedUser> getAlarmLinkedUser(@PathVariable("maintainId") String attrAlarmJsonId) {
        try {
            checkNotNull(attrAlarmJsonId);
            return alarmJsonService.getAllLinkedUser(getTenantId(),attrAlarmJsonId);
        } catch (ThingsboardException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 获取报警外部关联人
     * @return 报警设置内容
     */
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN',  'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE', 'TENANT_SYS', 'CUSTOMER_USER')")
    @GetMapping(value = "/maintain/linkedExtraUser/{maintainId}")
    @ResponseBody
    @SysLog(options = DataConstants.OPERATING_TYPE.OPERATING_TYPE_OPTIONS_SETTING_ALARM_SAVE)
    public List<AlarmLinkedUser> getAlarmLinkedExtraUser(@PathVariable("maintainId") String attrAlarmJsonId) {
        try {
            checkNotNull(attrAlarmJsonId);
            return alarmJsonService.getAllLinkedExtraUser(getTenantId(),attrAlarmJsonId);
        } catch (ThingsboardException e) {
            e.printStackTrace();
        }
        return null;
    }


}
