package org.thingsboard.server.dao.alarm;

import org.thingsboard.server.common.data.alarm.AlarmV2;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;

import java.util.List;

public interface AlarmV2Service {

    /**
     * 查询报警列表
     *
     * @param stationType 站点类型
     * @param projectId   所属项目
     * @param start       开始时间
     * @param end         结束时间
     * @param status      报警状态
     * @param level       报警等级
     * @param page        当前页
     * @param size        每页记录数
     * @param tenantId    租户ID
     * @return 数据
     */
    PageData<AlarmV2> findList(String stationType, String projectId, Long start, Long end, String status, String level, int page, int size, TenantId tenantId) throws Exception;

    /**
     * 查询报警列表
     *
     * @param stationType 站点类型
     * @param projectId   所属项目
     * @param start       开始时间
     * @param end         结束时间
     * @param status      报警状态
     * @param level       报警等级
     * @param tenantId    租户ID
     * @return 数据
     */
    List<AlarmV2> findList(String stationType, String projectId, Long start, Long end, String status, String level, TenantId tenantId) throws Exception;

    /**
     * 按报警类型查询统计曲线
     *
     * @param stationType 站点类型
     * @param projectId   所属项目
     * @param start       开始时间
     * @param end         结束时间
     * @param status      报警状态
     * @param level       报警等级
     * @param tenantId    租户ID
     * @return 数据
     */
    Object trendByType(String stationType, String projectId, Long start, Long end, String status, String level, TenantId tenantId) throws Exception;
}
