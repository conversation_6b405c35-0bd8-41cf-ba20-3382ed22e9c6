package org.thingsboard.server.dao.smartPipe;

import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.DmaDict;

import java.util.List;
import java.util.Map;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-04-25
 */
public interface DmaDictService {

    Object save(DmaDict dmaDict);

    void delete(List<String> idList);

    PageData<DmaDict> getList(Map<String, Object> params);

}
