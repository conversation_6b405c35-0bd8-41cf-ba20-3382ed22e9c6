package org.thingsboard.server.dao.app;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.Tenant;
import org.thingsboard.server.common.data.exception.ThingsboardErrorCode;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.dao.model.sql.AppType;
import org.thingsboard.server.dao.sql.app.AppTypeRepository;
import org.thingsboard.server.dao.tenant.TenantService;

import java.util.Arrays;
import java.util.List;

@Slf4j
@Service
@Transactional
public class AppTypeServiceImpl implements AppTypeService {

    @Autowired
    private AppTypeRepository appTypeRepository;

    @Autowired
    private TenantService tenantService;


    @Override
    public AppType findById(String id) {
        return appTypeRepository.findOne(id);
    }

    @Override
    public AppType save(AppType appType) {
        appType.setCreateTime(System.currentTimeMillis());
        return appTypeRepository.save(appType);
    }

    @Override
    public AppType update(AppType appType) {
        if (appType == null) {
            return null;
        }
        AppType _appType = appTypeRepository.findOne(appType.getId());
        _appType.setAppName(appType.getAppName());
        _appType.setAdditionalInfo(appType.getAdditionalInfo());
        return appTypeRepository.save(_appType);
    }

    @Override
    public AppType deleteById(String id) throws ThingsboardException {
        AppType appType = appTypeRepository.findOne(id);
        // 校验是否允许删除
        List<Tenant> tenantList = tenantService.findByAppTypeId(id);
        if (tenantList.size() > 0) {
            throw new ThingsboardException("存在引用该应用的企业, 不允许删除!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }

        if (appType != null) {
            appTypeRepository.delete(id);
            return appType;
        }
        return null;
    }

    @Override
    public List<AppType> findList() {
        Sort orders = new Sort(new Sort.Order(Sort.Direction.DESC, "createTime"));
        return appTypeRepository.findAll(orders);
    }
}
