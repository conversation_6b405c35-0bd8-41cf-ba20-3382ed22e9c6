package org.thingsboard.server.dao.model.DTO;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 呼叫来源报表
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-01-03
 */
@Data
public class CustPriceDTO {

    private String typeName;

    private String code;

    private String name;

    private BigDecimal basePrice;

    private BigDecimal totalSignPrice;

    private BigDecimal totalPrice;

    private String tenantId;
}
