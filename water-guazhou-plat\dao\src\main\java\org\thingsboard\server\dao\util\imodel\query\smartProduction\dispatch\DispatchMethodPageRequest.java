package org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartProduction.dispatch.DispatchMethod;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;

import java.util.Date;

@Getter
@Setter
public class DispatchMethodPageRequest extends AdvancedPageableQueryEntity<DispatchMethod, DispatchMethodPageRequest> {
    // 方案名称
    private String name;

    // 方案类型
    private String type;

    // 日期标签。工作日/节假日
    private String dateLabel;

    // 方案日期
    private String timeFrom;

    // 方案日期
    private String timeTo;

    // 供水量
    private Double waterSupplyFrom;

    // 供水量
    private Double waterSupplyTo;

    // 天气类型
    private String weatherType;

    // 耗电量
    private Double powerConsumptionFrom;

    // 耗电量
    private Double powerConsumptionTo;

    public Date getTimeTo() {
        return toDate(timeTo);
    }

    public Date getTimeFrom() {
        return toDate(timeFrom);
    }

}
