<template>
  <DialogForm
    ref="refDialog"
    :config="dialogConfig"
  >
    <Tabs
      v-model="state.activeName"
      :config="tabsConfig"
    >
      <template #content="scope">
        <el-form
          :ref="'refForm' + scope.data?.value"
          class="form"
          :model="formData"
        >
          <el-row :gutter="20">
            <template v-if="state.activeName === '存量设置'">
              <el-col :span="24">
                <el-radio-group v-model="state.stockType">
                  <el-radio label="夜间最小流">
                    夜间最小流量
                  </el-radio>
                  <el-radio label="夜间最小值">
                    夜间最小值
                  </el-radio>
                </el-radio-group>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  label="流量最小值(m³/h)"
                  :prop="'nightFlowMin'"
                  :rules="[{ required: true, message: '请输入流量最小值' }]"
                >
                  <el-input
                    v-model="formData.nightFlowMin"
                    type="input-number"
                    @change="handleNightFlowChange"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  label="流量最大值(m³/h)"
                  :prop="'nightFlowMax'"
                  :rules="[{ required: true, message: '请输入流量最大值' }]"
                >
                  <el-input
                    v-model="formData.nightFlowMax"
                    type="input-number"
                    @change="handleNightFlowChange"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="20">
                <span>备注:夜间最小流初始值设定,夜间最小值(小于{{
                  formData.nightFlowMin
                }}较好 / {{ formData.nightFlowMin }}-{{
                  formData.nightFlowMax
                }}一般 / 大于{{ formData.nightFlowMax }}较差)</span>
              </el-col>
              <template v-if="state.stockType === '夜间最小值'">
                <el-col :span="24">
                  <el-form-item
                    label="采集频率"
                    :rules="[{ required: true, message: '请输入采集频率' }]"
                    :prop="'collectRate'"
                  >
                    <el-radio-group
                      v-model="formData.collectRate"
                      @change="handleNightFlowChange"
                    >
                      <el-radio
                        v-for="item in CollectRates"
                        :key="item"
                        :label="item"
                      >
                        {{ item }}
                      </el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item
                    label="最小值(m³/h)"
                    :rules="[{ required: true, message: '请输入最小值' }]"
                    :prop="'nightValueMin'"
                  >
                    <el-input
                      v-model="formData.nightValueMin"
                      disabled
                      type="input-number"
                    ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item
                    label="最大值(m³/h)"
                    :prop="'nightValueMax'"
                    :rules="[{ required: true, message: '请输入最大值' }]"
                  >
                    <el-input
                      v-model="formData.nightValueMax"
                      disabled
                      type="input-number"
                    ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="20">
                  <span>备注:最小（大）值根据流量最小（大）值自动计算</span>
                </el-col>
              </template>
              <el-col :span="20">
                <div class="title">
                  单位管长夜间净流量:
                </div>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  label="最小值(m³/h)"
                  :prop="'unitPipeNightFlowMin'"
                  :rules="[{ required: true, message: '请输入最小值' }]"
                >
                  <el-input
                    v-model="formData.unitPipeNightFlowMin"
                    type="input-number"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  label="最大值(m³/h)"
                  :prop="'unitPipeNightFlowMax'"
                  :rules="[{ required: true, message: '请输入最大值' }]"
                >
                  <el-input
                    v-model="formData.unitPipeNightFlowMax"
                    type="input-number"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="20">
                <span>备注:单位管长初始值设定,夜间最小值(小于{{
                  formData.unitPipeNightFlowMin
                }}较好 / {{ formData.unitPipeNightFlowMin }}-{{
                  formData.unitPipeNightFlowMax
                }}一般 / 大于{{ formData.unitPipeNightFlowMax }}较差)</span>
              </el-col>
              <el-col :span="20">
                <div class="title">
                  MNF/日均小时流量(%):
                </div>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  label="最小值(%)"
                  :prop="'mnfDivDayAvgHourFlowMin'"
                  :rules="[{ required: true, message: '请输入最小值' }]"
                >
                  <el-input
                    v-model="formData.mnfDivDayAvgHourFlowMin"
                    type="input-number"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  label="最大值(%)"
                  :prop="'mnfDivDayAvgHourFlowMax'"
                  :rules="[{ required: true, message: '请输入最大值' }]"
                >
                  <el-input
                    v-model="formData.mnfDivDayAvgHourFlowMax"
                    type="input-number"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="20">
                <span>备注:MNF/日均小时流量初始值设定,夜间最小流(小于{{
                  formData.mnfDivDayAvgHourFlowMin
                }}%较好 / {{ formData.mnfDivDayAvgHourFlowMin }}%-{{
                  formData.mnfDivDayAvgHourFlowMax
                }}%一般 / 大于{{
                  formData.mnfDivDayAvgmnfDivDayAvgHourFlowMaxHourFlowMin
                }}%较差)</span>
              </el-col>
              <el-col
                :span="20"
                style="margin-top: 20px"
              >
                <span>规则:满足两个及以上较差评为较差.满足三个较好评为较好,其它评价为一般</span>
              </el-col>
            </template>
            <template v-else>
              <el-col :span="24">
                <el-radio-group
                  v-model="formData.incrType"
                  @change="handleIncrTypeChange"
                >
                  <el-radio label="1">
                    手动选择
                  </el-radio>
                  <el-radio label="2">
                    自动选择
                  </el-radio>
                </el-radio-group>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  label="日期"
                  :prop="'incrTime'"
                  :rules="[{ required: true, message: '请选择日期' }]"
                >
                  <el-date-picker
                    v-model="formData.incrTime"
                    type="date"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  label="基准值(m³/h)"
                  :prop="'incrBase'"
                  :rules="[{ required: true, message: '请输入基准值' }]"
                >
                  <el-input
                    v-model="formData.incrBase"
                    type="input-number"
                    :disabled="formData.incrType === '2'"
                    @change="handleIncrChange"
                  >
                    <template #suffix>
                      m³/h
                    </template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="20">
                <span>备注:历史测量最小夜间流量最小值为基准值</span>
              </el-col>
              <el-col :span="20">
                <el-form-item
                  label="黄色预警值"
                  :prop="'incrWarn'"
                  :rules="[{ required: true, message: '请输入预警值' }]"
                >
                  <el-input
                    v-model="formData.incrWarn"
                    type="input-number"
                    :disabled="formData.incrType === '2'"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="20">
                <span>备注:黄色预警值=基准值+0.9936*管线长度+0.1408</span>
              </el-col>
              <el-col :span="20">
                <el-form-item
                  label="红色预警值"
                  :prop="'incrError'"
                  :rules="[{ required: true, message: '请输入预警值' }]"
                >
                  <el-input
                    v-model="formData.incrError"
                    type="input-number"
                    :disabled="formData.incrType === '2'"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="20">
                <span>备注:红色预警值=基准值+0.9336*管线长度+0.1408+0.5*管线长度</span>
              </el-col>
            </template>
          </el-row>
        </el-form>
      </template>
    </Tabs>
  </DialogForm>
</template>
<script lang="ts" setup>
import { IDialogFormIns } from '@/components/type'
import { SLConfirm, SLMessage } from '@/utils/Message'
import { CollectRates, PostDMAMinFlowConfig } from '@/api/mapservice/dma'

const emit = defineEmits(['success'])
const { proxy }: any = getCurrentInstance()
const props = defineProps<{
  defaultValue?: Record<string, any>
}>()
const refDialog = ref<IDialogFormIns>()
const state = reactive<{
  tabsList: any[]
  stationTree: any
  stationId: string
  type: string
  activeName: string
  stockType: string
  increaseType: string
}>({
  tabsList: [],
  stationTree: [],
  stationId: '',
  type: 'month',
  activeName: '存量设置',
  stockType: '夜间最小流',
  increaseType: '手动选择'
})

const formData = ref<Record<string, any>>({
  ...(props.defaultValue || {}),
  incrType: props.defaultValue?.incrType || '1'
})
watch(
  () => props.defaultValue,
  () => {
    formData.value = {
      ...(props.defaultValue || {}),
      incrType: props.defaultValue?.incrType || '1'
    }
  }
)
const tabsConfig = reactive<ITabs>({
  type: 'tabs',
  tabType: 'border-card',
  width: '100%',
  tabs: [
    { label: '存量漏失指标设置', value: '存量设置' },
    { label: '增量漏失指标设置', value: '增量设置' }
  ],
  handleTabClick: (tab: any) => {
    //
    state.activeName = tab.props.name
  }
})
// 对话框配置
const dialogConfig = reactive<IDialogFormConfig>({
  dialogWidth: 800,
  title: '修改',
  group: [],
  desTroyOnClose: true,
  submit: () => {
    const refForm = proxy.$refs['refForm' + state.activeName]
    refForm?.validate((isValid, invalidFields) => {
      if (!isValid) {
        const obj = Object.keys(invalidFields)
        refForm?.scrollToField(obj[0])
        return
      }
      SLConfirm('确定提交？', '提示信息')
        .then(async () => {
          try {
            dialogConfig.submitting = true
            const res = await PostDMAMinFlowConfig(formData.value as any)
            if (res.data.code === 200) {
              SLMessage.success('操作成功')
              emit('success')
              refDialog.value?.closeDialog()
            } else {
              SLMessage.error(res.data.message)
            }
          } catch (error) {
            SLMessage.error('操作失败')
          }
          dialogConfig.submitting = false
        })
        .catch(() => {
          //
        })
    })
  }
})
const handleNightFlowChange = () => {
  if (formData.value.collectRate) {
    formData.value.nightValueMax = (
      ((formData.value.nightFlowMax || 0) * formData.value.collectRate)
      / 60
    ).toFixed(2)
    formData.value.nightValueMin = (
      ((formData.value.nightFlowMin || 0) * formData.value.collectRate)
      / 60
    ).toFixed(2)
  }
}
const handleIncrTypeChange = () => {
  const type = formData.value.incrType || '1'
  formData.value.incrBase = (type === '1' ? props.defaultValue?.incrBase : 0) || 0
  if (type === '1') {
    formData.value.incrWarn = props.defaultValue?.incrWarn || 0
    formData.value.incrError = props.defaultValue?.incrError || 0
  } else {
    handleIncrChange()
  }
}
const handleIncrChange = () => {
  const type = formData.value.incrType
  const base = Number(type === '1' ? formData.value.incrBase : 0) || 0
  const pipeLength = Number(formData.value.mainLineLength || 0)
  formData.value.incrWarn = (base + 0.9936 * pipeLength + 0.1408).toFixed(2)
  formData.value.incrError = (
    base
    + 0.9936 * pipeLength
    + 0.1408
    + 0.5 * pipeLength
  ).toFixed(2)
}
const openDialog = () => {
  refDialog.value?.openDialog()
}

defineExpose({
  openDialog
})
</script>
<style lang="scss" scoped>
.form {
  margin-top: 20px;
  span {
    color: grey;
    font-size: 13px;
    padding-bottom: 20px;
    display: block;
  }
  .title {
    font-size: 14px;
    font-weight: 800;
    padding: 20px 0;
  }
}
</style>
29°45′37.570678″N,106°26′04.257814″E 29°46′06.922083″N,106°25′51.360388″E
29°46′24.211200″N,106°25′53.007131″E 29°45′34.674693″N,106°26′09.682413″E
29°45′19.319970″N,106°25′59.841238″E 29°45′24.520659″N,106°25′46.376523″E
29°45′21.363982″N,106°25′49.557151″E 29°45′39.684254″N,106°25′50.217164″E
29°45′44.890595″N,106°25′50.329243″E 29°45′42.234696″N,106°25′50.249254″E
29°45′42.685273″N,106°25′47.535688″E 29°45′43.184956″N,106°25′46.844146″E
29°45′43.271269″N,106°25′44.141951″E 29°45′50.628099″N,106°25′27.318865″E
29°45′51.038249″N,106°25′25.269482″E 29°45′37.645001″N,106°25′18.735667″E
29°45′49.891671″N,106°25′22.592524″E 29°45′50.783031″N,106°25′22.903971″E
