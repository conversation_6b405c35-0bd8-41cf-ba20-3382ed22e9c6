package org.thingsboard.server.dao.model.sql.store;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.Compute;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.util.List;

@Getter
@Setter
@ResponseEntity
public class MainRestDeviceStorageJournal {

    // 设备编码
    private String serialId;

    // 仓库
    // @ParseViaMapper(StoreMapper.class)
    // @InfoViaMapper(name = "code", mapper = StoreMapper.class, prefix = "storehouse")
    private String storehouseId;

    // 仓库编码
    private String storehouseCode;

    // 仓库名称
    private String storehouseName;

    // 货架
    // @ParseViaMapper(GoodsShelfMapper.class)
    // @InfoViaMapper(name = "code", mapper = GoodsShelfMapper.class, prefix = "shelves")
    private String shelvesId;

    // 货架编码
    private String shelvesCode;

    // 货架名称
    private String shelvesName;

    // 设备名称
    @Compute("name")
    private String name;

    // 设备型号
    @Compute("model")
    private String model;

    // 所属大类
    @Compute("topType")
    private String topType;

    // 所属类别
    @Compute("type")
    private String type;

    // 剩余数量
    private Integer count;

    // 单位
    @Compute("unit")
    private String unit;

    // 台账信息
    List<RestDeviceStorageJournal> restDeviceInfos;

    // 租户ID
    // @ParseTenantName
    // private String tenantId;

    private void name() {
        restDeviceInfos.stream().findFirst().ifPresent(x -> {
            if (x.getDeviceInfoResponse() != null)
                name = x.getDeviceInfoResponse().getName();
        });
    }

    private void model() {
        restDeviceInfos.stream().findFirst().ifPresent(x -> {
            if (x.getDeviceInfoResponse() != null)
                model = x.getDeviceInfoResponse().getModel();
        });
    }

    private void topType() {
        restDeviceInfos.stream().findFirst().ifPresent(x -> {
            if (x.getDeviceInfoResponse() != null)
                topType = x.getDeviceInfoResponse().getTopType();
        });
    }

    private void type() {
        restDeviceInfos.stream().findFirst().ifPresent(x -> {
            if (x.getDeviceInfoResponse() != null)
                type = x.getDeviceInfoResponse().getType();
        });
    }

    private void unit() {
        restDeviceInfos.stream().findFirst().ifPresent(x -> {
            if (x.getDeviceInfoResponse() != null)
                unit = x.getDeviceInfoResponse().getUnit();
        });
    }
}
