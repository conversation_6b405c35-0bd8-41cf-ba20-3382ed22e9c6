import{d as o,g as l,n as c,p as s,bh as a,q as i,i as p,cs as r,C as _}from"./index-r0dFAfgr.js";const u={class:"value-item"},d={class:"label"},m={class:"value"},f={class:"unit"},v={class:"rate"},h={class:"rate-value"},b=o({__name:"ValueItem",props:{label:{},value:{},rate:{},unit:{},icon:{}},setup(n){const e=n;return(x,t)=>(l(),c("div",u,[s("p",d,[s("span",null,a(e.label),1)]),s("p",null,[s("span",m,a(e.value),1),s("span",f,a(e.unit),1)]),s("p",v,[i(p(r),{icon:e.icon},null,8,["icon"]),s("span",h,a(e.rate)+"%",1),t[0]||(t[0]=s("span",{class:"rate-suffix"},"较上个时刻增加",-1))])]))}}),V=_(b,[["__scopeId","data-v-38252c81"]]);export{V as default};
