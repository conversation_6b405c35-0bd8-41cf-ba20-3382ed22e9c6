package org.thingsboard.server.dao.model.DTO;

import lombok.Data;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.PipeUseWaterReport;

import java.math.BigDecimal;

/**
 * 水量填报
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2023-04-23
 */
@Data
public class PipeUseWaterReportDTO extends PipeUseWaterReport {

    private BigDecimal incomeWater;

    private BigDecimal noIncomeWater;

    private BigDecimal leakRate;

    private BigDecimal lossRate;

    private BigDecimal nrwRate;

}
