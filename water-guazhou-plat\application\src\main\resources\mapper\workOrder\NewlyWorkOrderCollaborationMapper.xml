<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.workOrder.NewlyWorkOrderCollaborationMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->id,
                           order_id,
                           type,
                           remark,
                           user_id,
                           time,
                           status,
                           additional_info,
                           tenant_id<!--@sql from work_order_collaboration -->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.workOrder.WorkOrderCollaboration">
        <result column="id" property="id"/>
        <result column="order_id" property="orderId"/>
        <result column="type" property="type"/>
        <result column="remark" property="remark"/>
        <result column="user_id" property="userId"/>
        <result column="time" property="time"/>
        <result column="status" property="status"/>
        <result column="additional_info" property="additionalInfo"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="process_remark" property="processRemark"/>
    </resultMap>

    <select id="findByPage" resultMap="BaseResultMap">
        select
        a.id,
        a.order_id,
        a.type,
        a.remark,
        a.user_id,
        a.time,
        a.status,
        a.additional_info,
        a.tenant_id,
        b.process_remark
        from work_order_collaboration a
        left join work_order_details b on  a.order_id = b.main_id and b.type = 'COLLABORATION'
        <where>
            <if test="orderId != null">
                and a.order_id like '%' || #{orderId} || '%'
            </if>
            <if test="type != null">
                and a.type = #{type}
            </if>
            <if test="userId != null">
                and a.user_id = #{userId}
            </if>
            <if test="fromTime != null">
                and a.time >= #{fromTime}
            </if>
            <if test="toTime != null">
                and a.time &lt;= #{toTime}
            </if>
            <if test="status != null and status != ''">
                <choose>
                    <when test="status == 'COMPLETED'">
                        and a.status != 'PENDING'
                    </when>
                    <otherwise>
                        and a.status = #{status}
                    </otherwise>
                </choose>
            </if>
            and a.tenant_id = #{tenantId}
        </where>
    </select>
</mapper>