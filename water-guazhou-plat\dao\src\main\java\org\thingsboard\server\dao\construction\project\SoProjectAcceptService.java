package org.thingsboard.server.dao.construction.project;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoProjectAccept;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoProjectAcceptPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoProjectAcceptSaveRequest;

public interface SoProjectAcceptService {
    /**
     * 分页条件查询项目验收信息
     *
     * @param request 分页查询条件
     * @return 分页查询结果
     */
    IPage<SoProjectAccept> findAllConditional(SoProjectAcceptPageRequest request);

    /**
     * 保存项目验收信息
     *
     * @param entity 实体信息
     * @return 保存好的实体
     */
    SoProjectAccept save(SoProjectAcceptSaveRequest entity);

    /**
     * 增量修改
     *
     * @param entity 实体信息
     * @return 是否修改成功
     */
    boolean update(SoProjectAccept entity);

    /**
     * 删除
     *
     * @param id 唯一标识
     * @return 是否删除成功
     */
    boolean delete(String id);

}
