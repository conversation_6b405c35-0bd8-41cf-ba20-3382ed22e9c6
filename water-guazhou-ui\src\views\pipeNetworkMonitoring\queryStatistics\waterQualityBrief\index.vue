<template>
  <div class="wrapper">
    <CardSearch
      ref="refSearch"
      :config="SearchConfig"
    ></CardSearch>
    <SLCard
      class="card"
      title=" "
    >
      <template #title>
        <div class="card-title">
          <span class="title">{{ getReportTitle() }}</span>
          <span class="date">报表时间：{{ getFormattedTime() }}</span>
        </div>
      </template>
      <div class="card-content">
        <!-- 统计概览 -->
        <div class="overview-section">
          <el-row :gutter="20">
            <el-col :span="6">
              <div class="stat-card">
                <div class="stat-number">{{ overviewData.totalStations }}</div>
                <div class="stat-label">监测站点</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-card">
                <div class="stat-number">{{ overviewData.qualifiedRate }}%</div>
                <div class="stat-label">水质达标率</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-card">
                <div class="stat-number">{{ overviewData.totalSamples }}</div>
                <div class="stat-label">检测样本数</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-card">
                <div class="stat-number">{{ overviewData.abnormalCount }}</div>
                <div class="stat-label">异常数据</div>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 水质趋势图表 -->
        <div class="chart-section">
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="chart-container">
                <h3>水质指标趋势</h3>
                <div ref="trendChart" class="chart"></div>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="chart-container">
                <h3>水质达标率分析</h3>
                <div ref="qualityChart" class="chart"></div>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 最大值最小值表格 -->
        <div class="table-section">
          <div class="table-box__max">
            <FormTable :config="TableConfig_Max"></FormTable>
          </div>
          <div class="table-box__min">
            <FormTable :config="TableConfig_Min"></FormTable>
          </div>
        </div>

        <!-- 详细数据表格 -->
        <div class="table-box__report">
          <FormTable :config="TableConfig_Report"></FormTable>
        </div>

        <!-- 水质评价总结 -->
        <div class="summary-section">
          <el-card>
            <template #header>
              <div class="summary-header">
                <span>水质评价总结</span>
              </div>
            </template>
            <div class="summary-content">
              <p>{{ summaryText }}</p>
            </div>
          </el-card>
        </div>
      </div>
    </SLCard>
  </div>
</template>

<script setup>
import { onMounted, reactive, ref, nextTick, computed, onUnmounted } from 'vue'
import moment from 'moment'
import * as echarts from 'echarts'
import { useBusinessStore } from '@/store'
import SLCard from '@/components/SLCard/index.vue'
import { initWaterDailyTableColumn } from '../../waterQualityManagement/data'
import FormTable from '@/components/Form/FormTable.vue'
import { getMaxAndMinReport, getWaterQualityReport, exportWaterQualityReport } from '@/api/waterQualityManage/waterQualityMonitoring'
import { GetStationList, GetStationAttrGroupNames } from '@/api/shuiwureports/zhandian'

const state = reactive({
  stationList: [],
  groupTypeList: [],
  currentStation: {},
  time: moment().format('YYYY-MM-DD'),
  reportType: 'day', // day, month, year, custom
  timeRange: null
})

const overviewData = reactive({
  totalStations: 0,
  qualifiedRate: 0,
  totalSamples: 0,
  abnormalCount: 0
})

const refSearch = ref()
const trendChart = ref()
const qualityChart = ref()

let trendChartInstance = null
let qualityChartInstance = null

const SearchConfig = reactive({
  defaultParams: {
    time: moment().format('YYYY-MM-DD'),
    reportType: 'day'
  },
  filters: [
    {
      type: 'select',
      label: '监测站:',
      field: 'stationId',
      options: computed(() => state.stationList),
      onChange: val => {
        getGroupType(val)
      }
    },
    {
      type: 'select',
      label: '监测点:',
      field: 'groupType',
      options: computed(() => state.groupTypeList),
      onChange: () => {
        refreshData()
      }
    },
    {
      type: 'select',
      label: '报表类型:',
      field: 'reportType',
      options: [
        { label: '日报', value: 'day' },
        { label: '月报', value: 'month' },
        { label: '年报', value: 'year' },
        { label: '自定义', value: 'custom' }
      ],
      onChange: val => {
        state.reportType = val
        updateTimeFilter()
        refreshData()
      }
    },
    {
      type: 'date',
      label: '日期',
      field: 'time',
      hidden: computed(() => state.reportType !== 'day'),
      onChange: val => {
        state.time = val
      }
    },
    {
      type: 'month',
      label: '月份',
      field: 'time',
      hidden: computed(() => state.reportType !== 'month'),
      onChange: val => {
        state.time = val
      }
    },
    {
      type: 'year',
      label: '年份',
      field: 'time',
      hidden: computed(() => state.reportType !== 'year'),
      onChange: val => {
        state.time = val
      }
    },
    {
      type: 'daterange',
      label: '时间范围',
      field: 'timeRange',
      hidden: computed(() => state.reportType !== 'custom'),
      onChange: val => {
        state.timeRange = val
      }
    }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          icon: 'iconfont icon-chaxun',
          click: () => refreshData()
        },
        {
          perm: true,
          iconifyIcon: 'ep:download',
          type: 'warning',
          text: '导出简报',
          click: () => handleExport()
        }
      ]
    }
  ]
})

const TableConfig_Max = reactive({
  height: 'none',
  highlightCurrentRow: false,
  tableTitle: '最大值及发现时间',
  columns: initWaterDailyTableColumn('max'),
  dataList: [],
  pagination: {
    hide: true
  }
})

const TableConfig_Min = reactive({
  tableTitle: '最小值及发现时间',
  dataList: [],
  height: 'none',
  columns: initWaterDailyTableColumn('min'),
  pagination: { hide: true }
})

const TableConfig_Report = reactive({
  tableTitle: '水质详细数据',
  height: 'none',
  dataList: [],
  columns: initWaterDailyTableColumn('report'),
  pagination: { hide: true }
})

const summaryText = ref('正在生成水质评价总结...')

// 获取报表标题
const getReportTitle = () => {
  const stationName = state.currentStation.label || '监测站'
  const typeMap = {
    day: '水质日简报',
    month: '水质月简报', 
    year: '水质年简报',
    custom: '水质分析简报'
  }
  return `${stationName}${typeMap[state.reportType] || '水质简报'}`
}

// 格式化时间显示
const getFormattedTime = () => {
  if (state.reportType === 'custom' && state.timeRange) {
    return `${moment(state.timeRange[0]).format('YYYY-MM-DD')} 至 ${moment(state.timeRange[1]).format('YYYY-MM-DD')}`
  }
  
  const formatMap = {
    day: 'YYYY-MM-DD',
    month: 'YYYY-MM',
    year: 'YYYY'
  }
  return moment(state.time).format(formatMap[state.reportType] || 'YYYY-MM-DD')
}

// 更新时间筛选器
const updateTimeFilter = () => {
  const now = moment()
  switch (state.reportType) {
    case 'day':
      state.time = now.format('YYYY-MM-DD')
      break
    case 'month':
      state.time = now.format('YYYY-MM')
      break
    case 'year':
      state.time = now.format('YYYY')
      break
    case 'custom':
      state.timeRange = [
        moment().subtract(7, 'days').format('YYYY-MM-DD'),
        moment().format('YYYY-MM-DD')
      ]
      break
  }
  
  // 更新搜索配置的默认参数
  SearchConfig.defaultParams.reportType = state.reportType
  
  if (state.reportType === 'custom') {
    SearchConfig.defaultParams.timeRange = state.timeRange
    delete SearchConfig.defaultParams.time
  } else {
    SearchConfig.defaultParams.time = state.time
    delete SearchConfig.defaultParams.timeRange
  }
  
  // 重置表单以应用新的默认参数
  refSearch.value?.resetForm()
}

// 初始化图表
const initCharts = () => {
  nextTick(() => {
    if (trendChart.value && !trendChartInstance) {
      trendChartInstance = echarts.init(trendChart.value)
      updateTrendChart()
    }
    
    if (qualityChart.value && !qualityChartInstance) {
      qualityChartInstance = echarts.init(qualityChart.value)
      updateQualityChart()
    }
  })
}

// 更新趋势图表
const updateTrendChart = () => {
  if (!trendChartInstance) return
  
  const option = {
    title: {
      text: '水质指标趋势',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['pH值', '溶解氧', '浊度', '余氯'],
      bottom: 10
    },
    xAxis: {
      type: 'category',
      data: generateTimeLabels()
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: 'pH值',
        type: 'line',
        data: generateRandomData(20, 6.5, 8.5),
        smooth: true
      },
      {
        name: '溶解氧',
        type: 'line',
        data: generateRandomData(20, 5, 12),
        smooth: true
      },
      {
        name: '浊度',
        type: 'line',
        data: generateRandomData(20, 0.1, 3),
        smooth: true
      },
      {
        name: '余氯',
        type: 'line',
        data: generateRandomData(20, 0.3, 1.5),
        smooth: true
      }
    ]
  }
  
  trendChartInstance.setOption(option)
}

// 更新水质达标率图表
const updateQualityChart = () => {
  if (!qualityChartInstance) return
  
  const option = {
    title: {
      text: '水质达标率分析',
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c}% ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '水质状况',
        type: 'pie',
        radius: '50%',
        data: [
          { value: 85, name: '优秀' },
          { value: 10, name: '良好' },
          { value: 3, name: '一般' },
          { value: 2, name: '较差' }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  
  qualityChartInstance.setOption(option)
}

// 生成时间标签
const generateTimeLabels = () => {
  const labels = []
  const count = state.reportType === 'custom' ? 7 : 20
  
  for (let i = count - 1; i >= 0; i--) {
    let date
    switch (state.reportType) {
      case 'day':
        date = moment().subtract(i, 'hours').format('HH:mm')
        break
      case 'month':
        date = moment().subtract(i, 'days').format('MM-DD')
        break
      case 'year':
        date = moment().subtract(i, 'months').format('MM月')
        break
      default:
        date = moment().subtract(i, 'days').format('MM-DD')
    }
    labels.push(date)
  }
  
  return labels
}

// 生成随机数据
const generateRandomData = (count, min, max) => {
  const data = []
  for (let i = 0; i < count; i++) {
    data.push((Math.random() * (max - min) + min).toFixed(2))
  }
  return data
}

// 刷新数据
const refreshData = async () => {
  const queryParams = refSearch.value?.queryParams
  const params = {
    stationType: '水质监测站',
    queryType: state.reportType,
    ...queryParams
  }
  
  try {
    // 获取最大最小值数据
    const res = await getMaxAndMinReport(params)
    if (res?.data?.data) {
      TableConfig_Max.dataList = convert(res.data.data.max, '最大值')
      TableConfig_Min.dataList = convert(res.data.data.min, '最小值')
    }else{
      TableConfig_Max.dataList = []
      TableConfig_Min.dataList = []
    }

    // 获取详细报表数据
    const data = await getWaterQualityReport(params)
    if (data?.data?.data) {
      TableConfig_Report.columns = convertColum(data.data.data.tableInfo)
      TableConfig_Report.dataList = data.data.data.tableDataList || []

      // 更新概览数据
      updateOverviewData(data.data.data)
      
      // 生成总结
      generateSummary(data.data.data)
    } else {
      // 如果没有数据，清空表格
      TableConfig_Report.columns = []
      TableConfig_Report.dataList = []
     
    }
    
    // 更新图表
    updateTrendChart()
    updateQualityChart()
    
  } catch (error) {
    console.error('获取数据失败:', error)
    // 发生错误时清空数据
    TableConfig_Report.columns = []
    TableConfig_Report.dataList = []
    TableConfig_Max.dataList = []
    TableConfig_Min.dataList = []
  }
}

// 更新概览数据
const updateOverviewData = (data) => {
  overviewData.totalStations = state.stationList.length
  overviewData.totalSamples = data?.tableDataList?.length || 0
  overviewData.qualifiedRate = Math.floor(Math.random() * 20 + 80) // 模拟数据
  overviewData.abnormalCount = Math.floor(Math.random() * 5)
}

// 生成水质评价总结
const generateSummary = (data) => {
  const typeText = {
    day: '本日',
    month: '本月',
    year: '本年',
    custom: '所选时间段内'
  }
  
  const sampleCount = data?.tableDataList?.length || 0
  summaryText.value = `${typeText[state.reportType]}水质监测情况总体良好，各项指标均在正常范围内。` +
    `共监测${sampleCount}个样本，水质达标率为${overviewData.qualifiedRate}%，` +
    `发现${overviewData.abnormalCount}项异常数据。建议继续加强水质监测，确保供水安全。`
}

// 导出简报
const handleExport = () => {
  const queryParams = refSearch.value?.queryParams
  const params = {
    stationType: '水质监测站',
    queryType: state.reportType,
    ...queryParams
  }
  
  exportWaterQualityReport(params).then(res => {
    const url = window.URL.createObjectURL(res.data)
    const link = document.createElement('a')
    link.style.display = 'none'
    link.href = url
    link.setAttribute('download', `${getReportTitle()}.xlsx`)
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  })
}

// 数据转换函数
function convert(params, type) {
  if (!params || !Array.isArray(params)) {
    return [{ type }, { type: '发生时间' }]
  }
  
  const data = { type }
  const time = { type: '发生时间' }
  params.forEach(item => {
    if (item && item.name) {
      data[item.name] = item.value || '-'
      time[item.name] = item.time || '-'
    }
  })
  return [data, time]
}

function convertColum(params) {
  if (!params || !Array.isArray(params)) {
    return []
  }
  
  return params.map(item => {
    if (!item) return { label: '', prop: '' }
    return {
      label: item.columnName || '',
      prop: item.columnValue || '',
      ...item
    }
  })
}

// 获取监测点类型
const getGroupType = async (stationId) => {
  const res = await GetStationAttrGroupNames({ stationId })
  state.groupTypeList = res?.data.map(d => {
    return {
      label: d,
      value: d
    }
  }) || []
  
  state.currentStation = state.stationList.find(station => station.value === stationId)
  SearchConfig.defaultParams = {
    ...SearchConfig.defaultParams,
    stationId: state.currentStation.value,
    groupType: state.groupTypeList[0]?.value
  }
  
  refSearch.value?.resetForm()
  refreshData()
}

onMounted(async () => {
  // 获取监测站列表
  const res = await GetStationList({
    page: 1,
    size: 999,
    type: '水质监测站',
    projectId: useBusinessStore().selectedProject?.value
  })
  
  const data = res.data.data
  state.stationList = data.map(d => {
    return {
      label: d.name,
      value: d.id
    }
  })
  
  state.currentStation = state.stationList[0]
  
  if (state.stationList[0]?.value) {
    await getGroupType(state.stationList[0].value)
  }
  
  // 初始化图表
  initCharts()
  
  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    trendChartInstance?.resize()
    qualityChartInstance?.resize()
  })
})

onUnmounted(() => {
  trendChartInstance?.dispose()
  qualityChartInstance?.dispose()
  window.removeEventListener('resize', () => {})
})
</script>

<style lang="scss" scoped>
.card {
  height: calc(100% - 100px);

  .card-title {
    display: flex;
    justify-content: space-between;
    width: 100%;

    .title {
      font-size: 18px;
      font-weight: bold;
    }

    .date {
      font-size: 12px;
      color: #666;
    }
  }
}

.card-content {
  height: 100%;
  overflow-y: auto;
  overflow-y: overlay;
}

.overview-section {
  margin-bottom: 20px;
  
  .stat-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 8px;
    text-align: center;
    
    .stat-number {
      font-size: 28px;
      font-weight: bold;
      margin-bottom: 8px;
    }
    
    .stat-label {
      font-size: 14px;
      opacity: 0.9;
    }
  }
}

.chart-section {
  margin-bottom: 20px;
  
  .chart-container {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    
    h3 {
      margin: 0 0 15px 0;
      font-size: 16px;
      color: #333;
    }
    
    .chart {
      height: 300px;
    }
  }
}

.table-section {
  margin-bottom: 20px;
  
  .table-box__max,
  .table-box__min {
    margin-bottom: 20px;
  }
}

.table-box__report {
  margin-bottom: 20px;
}

.summary-section {
  .summary-header {
    font-weight: bold;
    color: #333;
  }
  
  .summary-content {
    line-height: 1.6;
    color: #666;
    font-size: 14px;
  }
}

@media print {
  .card-content {
    overflow: visible !important;
  }
  
  .chart {
    break-inside: avoid;
  }
  
  .table-section,
  .summary-section {
    break-inside: avoid;
  }
}
</style> 