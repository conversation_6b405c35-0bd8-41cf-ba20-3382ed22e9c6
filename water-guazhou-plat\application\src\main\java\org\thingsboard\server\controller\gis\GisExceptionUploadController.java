package org.thingsboard.server.controller.gis;

import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.gis.GisExceptionUploadService;
import org.thingsboard.server.dao.model.request.GisExceptionUploadListRequest;
import org.thingsboard.server.dao.model.sql.gis.GisExceptionUpload;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.List;

/**
 * 异常上传
 */
@RestController
@RequestMapping("api/gis/exceptionUpload")
public class GisExceptionUploadController extends BaseController {

    @Autowired
    private GisExceptionUploadService gisExceptionUploadService;

    @GetMapping("list")
    public IstarResponse findList(GisExceptionUploadListRequest request) throws ThingsboardException {
        return IstarResponse.ok(gisExceptionUploadService.findList(request, getTenantId()));
    }

    @PostMapping("save")
    public IstarResponse save(@RequestBody GisExceptionUpload entity) throws ThingsboardException {
        gisExceptionUploadService.save(entity, getCurrentUser());
        return IstarResponse.ok();
    }

    @PostMapping("approval")
    public IstarResponse approval(@RequestBody JSONObject param) throws ThingsboardException {
        if (!param.containsKey("status")) {
            return IstarResponse.error("上传的状态不能为空!");
        }
        if (!param.containsKey("id")) {
            return IstarResponse.error("上传的ID不能为空!");
        }
        gisExceptionUploadService.approval(param.getString("id"), param.getString("status"), param.getString("remark"), getCurrentUser());

        return IstarResponse.ok();
    }

    @DeleteMapping("remove")
    public IstarResponse remove(@RequestBody List<String> ids) {
        gisExceptionUploadService.remove(ids);
        return IstarResponse.ok();
    }


}
