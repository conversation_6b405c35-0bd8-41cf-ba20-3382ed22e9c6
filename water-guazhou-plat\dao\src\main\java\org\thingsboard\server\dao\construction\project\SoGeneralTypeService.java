package org.thingsboard.server.dao.construction.project;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralType;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoGeneralTypePageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoGeneralTypeSaveRequest;

public interface SoGeneralTypeService {
    /**
     * 分页条件查询工程类型
     *
     * @param request 分页查询条件
     * @return 分页查询结果
     */
    IPage<SoGeneralType> findAllConditional(SoGeneralTypePageRequest request);

    /**
     * 保存工程类型
     *
     * @param entity 实体信息
     * @return 保存好的实体
     */
    SoGeneralType save(SoGeneralTypeSaveRequest entity);

    /**
     * 增量修改
     *
     * @param entity 实体信息
     * @return 是否修改成功
     */
    boolean update(SoGeneralType entity);

    /**
     * 删除
     *
     * @param id 唯一标识
     * @return 是否删除成功
     */
    boolean delete(String id);

}
