<template>
  <div class="one-map-detail">
    <div class="chart-wrapper">
      <Charts
        ref="refChart2"
        autoresize
        theme="dark"
        :option="option2"
      ></Charts>
      <Charts
        ref="refChart3"
        autoresize
        theme="dark"
        :option="option3"
      ></Charts>
      <Charts
        ref="refChart4"
        autoresize
        theme="dark"
        :option="option4"
      ></Charts>
    </div>
  </div>
</template>
<script lang="ts" setup>
import {
  horizontalHistogram,
  ring,
  hourlyLine
} from '../../components/components/chart';
import { Charts } from '../../components';
import { IECharts } from '@/plugins/echart';

const emit = defineEmits(['refresh', 'mounted']);

const refChart2 = ref<IECharts>();
const refChart3 = ref<IECharts>();
const refChart4 = ref<IECharts>();
const option2 = ring();
const option3 = horizontalHistogram('人员维修工单数量排行');
const option4 = hourlyLine();

const refreshDetail = () => {
  resizeChart();
};
const resizeChart = () => {
  refChart2.value?.resize();
  refChart3.value?.resize();
  refChart4.value?.resize();
};
defineExpose({
  refreshDetail
});
onMounted(() => {
  emit('mounted');
  window.addEventListener('resize', resizeChart);
});
onBeforeUnmount(() => {
  window.removeEventListener('resize', resizeChart);
});
</script>
<style lang="scss" scoped>
.one-map-detail {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: row;

  .chart-wrapper {
    display: flex;
    height: 100%;
    width: 100%;
    overflow: hidden;
    flex-direction: row;
    .tabs {
      flex: 1;
    }
  }
}
</style>
