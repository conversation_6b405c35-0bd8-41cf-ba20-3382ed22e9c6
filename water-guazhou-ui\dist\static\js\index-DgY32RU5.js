import{d as L,r as i,c as m,bF as o,a8 as Y,bX as I,s as M,o as w,ah as N,g as T,n as P,bo as O,i as _,p as B,q as S,br as E,bD as F,C as H}from"./index-r0dFAfgr.js";import{_ as V}from"./CardTable-rdWOL4_6.js";import{_ as R}from"./CardSearch-CB_HNR-Q.js";import{r as W}from"./data-D3PIONJl.js";import{g as z}from"./statisticalAnalysis-BoRmiv4A.js";import{u as A}from"./useStation-DJgnSZIA.js";import{p as J}from"./printUtils-C-AxhDcd.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";import"./zhandian-YaGuQZe6.js";const Q={class:"wrapper"},X={class:"main"},G={class:"right"},K=L({__name:"index",setup(U){const{getStationTree:k}=A(),s=i({queryType:"day",treeDataType:"Station",stationId:"",title:""}),j=m(!1),h=o().date(),b=m(),d=m(),r=i({data:[],currentProject:{}}),p=i({defaultParams:{type:"day",year:[o().format(),o().format()],month:[o().format(),o().format()],day:[o().date(h-6).format("YYYY-MM-DD"),o().date(h).format("YYYY-MM-DD")]},filters:[{type:"select-tree",field:"treeData",checkStrictly:!0,defaultExpandAll:!0,options:Y(()=>r.data),label:"站点选择",onChange:e=>{const t=I(r.data,"children","id",e);r.currentProject=t,s.treeDataType=t.data.type,s.treeDataType==="Station"&&(s.stationId=t.id,u())}},{type:"radio-button",field:"type",options:[{label:"日报",value:"day"},{label:"月报",value:"month"},{label:"年报",value:"year"}],label:"报告类型"},{type:"daterange",label:"选择时间",field:"day",clearable:!1,handleHidden:(e,t,a)=>{a.hidden=e.type==="month"||e.type==="year"}},{type:"monthrange",label:"选择时间",field:"month",clearable:!1,handleHidden:(e,t,a)=>{a.hidden=e.type==="day"||e.type==="year"}},{type:"yearrange",label:"选择时间",field:"year",clearable:!1,handleHidden:(e,t,a)=>{a.hidden=e.type==="month"||e.type==="day"}},{type:"btn-group",btns:[{perm:!0,text:"查询",click:()=>u(),icon:"iconfont icon-chaxun"},{text:"导出",perm:!0,type:"warning",icon:"iconfont icon-xiazai",click:()=>q()},{perm:!0,text:"打印",type:"success",svgIcon:M(F),click:()=>C()}]}]}),n=i({loading:!1,dataList:[],columns:[],operations:[],operationWidth:"150px",pagination:{hide:!0}}),u=()=>{var g;n.loading=!0;const e=r.currentProject.id,t=((g=d.value)==null?void 0:g.queryParams)||{},a=t[t.type],l=W.find(y=>y.value===t.type);s.title=r.currentProject.label+"能耗报表("+l.label+o(a[0]).format(l.data)+"至"+o(a[1]).format(l.data)+")",n.title=s.title;const f={stationId:e,start:o(a[0]).startOf(t.type).valueOf(),end:o(a[1]).endOf(t.type).valueOf(),queryType:t.type};z(f).then(y=>{var x;const v=y.data.data,D=(x=v.tableInfo)==null?void 0:x.map(c=>({prop:c.columnValue,label:c.columnName,unit:c.unit?"("+c.unit+")":""}));console.log(D),n.columns=D,n.dataList=v.tableDataList,n.loading=!1})},q=()=>{var e;(e=b.value)==null||e.exportTable()},C=()=>{J({title:s.title,titleList:n.columns,data:n.dataList})};return w(async()=>{var t;const e=await k("污水处理厂");r.data=e,r.currentProject=N(r.data),p.defaultParams={...p.defaultParams,treeData:r.currentProject},(t=d.value)==null||t.resetForm(),u()}),(e,t)=>{const a=R,l=V,f=E;return T(),P("div",Q,[O((T(),P("div",X,[B("div",G,[S(a,{ref_key:"cardSearch",ref:d,config:_(p)},null,8,["config"]),S(l,{id:"print",ref_key:"refTable",ref:b,class:"card-table",config:_(n)},null,8,["config"])])])),[[f,_(j)]])])}}}),ce=H(K,[["__scopeId","data-v-21551f20"]]);export{ce as default};
