package org.thingsboard.server.dao.model.sql.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 平台管理-产品授权对象 base_product_authorization
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
@ApiModel(value = "产品授权", description = "平台管理-产品授权实体类")
@Data
public class BaseProductAuthorization {

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;

    /**
     * 产品名称
     */
    @ApiModelProperty(value = "产品名称")
    private String name;

    /**
     * 产品类型
     */
    @ApiModelProperty(value = "产品类型")
    private String type;

    /**
     * 产品描述
     */
    @ApiModelProperty(value = "产品描述")
    private String description;

    /**
     * 路由配置（json）
     */
    @ApiModelProperty(value = "路由配置（json）")
    private String routeConfig;

    /**
     * 配置信息（json）
     */
    @ApiModelProperty(value = "配置信息（json）")
    private String config;

    /**
     * 是否开启
     */
    @ApiModelProperty(value = "是否开启")
    private String enabled;
}
