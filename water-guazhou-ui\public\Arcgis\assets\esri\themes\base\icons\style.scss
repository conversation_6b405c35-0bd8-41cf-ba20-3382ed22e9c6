@import "variables";

$icomoon-selectors: '[class^="esri-icon-"]', '[class*=" esri-icon-"]';

@mixin icomoonIconSelector($selectorPrefix:"", $selectorSuffix:"") {
  @each $icomoon-selector in $icomoon-selectors {
    #{$selectorPrefix}#{$icomoon-selector}#{$selectorSuffix} {
      @content;
    }
  }
}

@font-face {
  font-family: "#{$icomoon-font-family}";
  src: url("#{$icomoon-font-path}/#{$icomoon-font-family}.ttf?#{$icomoon-font-version}")
      format("truetype"),
    url("#{$icomoon-font-path}/#{$icomoon-font-family}.woff?#{$icomoon-font-version}")
      format("woff"),
    url("#{$icomoon-font-path}/#{$icomoon-font-family}.svg?#{$icomoon-font-version}##{$icomoon-font-family}")
      format("svg");
  font-weight: normal;
  font-style: normal;
}

@include icomoonIconSelector() {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: "#{$icomoon-font-family}" !important;
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

@function unicode($str) {
  @return unquote('"') + $str + unquote('"');
}

.esri-icon-close {
  &:before {
    content: unicode($esri-icon-close);
    color: $fill-color;
  }
}
.esri-icon-drag-horizontal {
  &:before {
    content: unicode($esri-icon-drag-horizontal);
    color: $fill-color;
  }
}
.esri-icon-drag-vertical {
  &:before {
    content: unicode($esri-icon-drag-vertical);
    color: $fill-color;
  }
}
.esri-icon-handle-horizontal {
  &:before {
    content: unicode($esri-icon-handle-horizontal);
    color: $fill-color;
  }
}
.esri-icon-handle-vertical {
  &:before {
    content: unicode($esri-icon-handle-vertical);
    color: $fill-color;
  }
}
.esri-icon-check-mark {
  &:before {
    content: unicode($esri-icon-check-mark);
    color: $fill-color;
  }
}
.esri-icon-left-triangle-arrow {
  &:before {
    content: unicode($esri-icon-left-triangle-arrow);
    color: $fill-color;
  }
}
.esri-icon-right-triangle-arrow {
  &:before {
    content: unicode($esri-icon-right-triangle-arrow);
    color: $fill-color;
  }
}
.esri-icon-down-arrow {
  &:before {
    content: unicode($esri-icon-down-arrow);
    color: $fill-color;
  }
}
.esri-icon-up-arrow {
  &:before {
    content: unicode($esri-icon-up-arrow);
    color: $fill-color;
  }
}
.esri-icon-overview-arrow-bottom-left {
  &:before {
    content: unicode($esri-icon-overview-arrow-bottom-left);
    color: $fill-color;
  }
}
.esri-icon-overview-arrow-bottom-right {
  &:before {
    content: unicode($esri-icon-overview-arrow-bottom-right);
    color: $fill-color;
  }
}
.esri-icon-overview-arrow-top-left {
  &:before {
    content: unicode($esri-icon-overview-arrow-top-left);
    color: $fill-color;
  }
}
.esri-icon-overview-arrow-top-right {
  &:before {
    content: unicode($esri-icon-overview-arrow-top-right);
    color: $fill-color;
  }
}
.esri-icon-maximize {
  &:before {
    content: unicode($esri-icon-maximize);
    color: $fill-color;
  }
}
.esri-icon-minimize {
  &:before {
    content: unicode($esri-icon-minimize);
    color: $fill-color;
  }
}
.esri-icon-checkbox-unchecked {
  &:before {
    content: unicode($esri-icon-checkbox-unchecked);
    color: $fill-color;
  }
}
.esri-icon-checkbox-checked {
  &:before {
    content: unicode($esri-icon-checkbox-checked);
    color: $fill-color;
  }
}
.esri-icon-radio-unchecked {
  &:before {
    content: unicode($esri-icon-radio-unchecked);
    color: $fill-color;
  }
}
.esri-icon-radio-checked {
  &:before {
    content: unicode($esri-icon-radio-checked);
    color: $fill-color;
  }
}
.esri-icon-up-arrow-circled {
  &:before {
    content: unicode($esri-icon-up-arrow-circled);
    color: $fill-color;
  }
}
.esri-icon-down-arrow-circled {
  &:before {
    content: unicode($esri-icon-down-arrow-circled);
    color: $fill-color;
  }
}
.esri-icon-left-arrow-circled {
  &:before {
    content: unicode($esri-icon-left-arrow-circled);
    color: $fill-color;
  }
}
.esri-icon-right-arrow-circled {
  &:before {
    content: unicode($esri-icon-right-arrow-circled);
    color: $fill-color;
  }
}
.esri-icon-zoom-out-fixed {
  &:before {
    content: unicode($esri-icon-zoom-out-fixed);
    color: $fill-color;
  }
}
.esri-icon-zoom-in-fixed {
  &:before {
    content: unicode($esri-icon-zoom-in-fixed);
    color: $fill-color;
  }
}
.esri-icon-refresh {
  &:before {
    content: unicode($esri-icon-refresh);
    color: $fill-color;
  }
}
.esri-icon-edit {
  &:before {
    content: unicode($esri-icon-edit);
    color: $fill-color;
  }
}
.esri-icon-authorize {
  &:before {
    content: unicode($esri-icon-authorize);
    color: $fill-color;
  }
}
.esri-icon-map-pin {
  &:before {
    content: unicode($esri-icon-map-pin);
    color: $fill-color;
  }
}
.esri-icon-blank-map-pin {
  &:before {
    content: unicode($esri-icon-blank-map-pin);
    color: $fill-color;
  }
}
.esri-icon-table {
  &:before {
    content: unicode($esri-icon-table);
    color: $fill-color;
  }
}
.esri-icon-plus {
  &:before {
    content: unicode($esri-icon-plus);
    color: $fill-color;
  }
}
.esri-icon-minus {
  &:before {
    content: unicode($esri-icon-minus);
    color: $fill-color;
  }
}
.esri-icon-beginning {
  &:before {
    content: unicode($esri-icon-beginning);
    color: $fill-color;
  }
}
.esri-icon-reverse {
  &:before {
    content: unicode($esri-icon-reverse);
    color: $fill-color;
  }
}
.esri-icon-pause {
  &:before {
    content: unicode($esri-icon-pause);
    color: $fill-color;
  }
}
.esri-icon-play {
  &:before {
    content: unicode($esri-icon-play);
    color: $fill-color;
  }
}
.esri-icon-forward {
  &:before {
    content: unicode($esri-icon-forward);
    color: $fill-color;
  }
}
.esri-icon-end {
  &:before {
    content: unicode($esri-icon-end);
    color: $fill-color;
  }
}
.esri-icon-erase {
  &:before {
    content: unicode($esri-icon-erase);
    color: $fill-color;
  }
}
.esri-icon-up-down-arrows {
  &:before {
    content: unicode($esri-icon-up-down-arrows);
    color: $fill-color;
  }
}
.esri-icon-left {
  &:before {
    content: unicode($esri-icon-left);
    color: $fill-color;
  }
}
.esri-icon-right {
  &:before {
    content: unicode($esri-icon-right);
    color: $fill-color;
  }
}
.esri-icon-announcement {
  &:before {
    content: unicode($esri-icon-announcement);
    color: $fill-color;
  }
}
.esri-icon-notice-round {
  &:before {
    content: unicode($esri-icon-notice-round);
    color: $fill-color;
  }
}
.esri-icon-notice-triangle {
  &:before {
    content: unicode($esri-icon-notice-triangle);
    color: $fill-color;
  }
}
.esri-icon-home {
  &:before {
    content: unicode($esri-icon-home);
    color: $fill-color;
  }
}
.esri-icon-locate {
  &:before {
    content: unicode($esri-icon-locate);
    color: $fill-color;
  }
}
.esri-icon-expand {
  &:before {
    content: unicode($esri-icon-expand);
    color: $fill-color;
  }
}
.esri-icon-collapse {
  &:before {
    content: unicode($esri-icon-collapse);
    color: $fill-color;
  }
}
.esri-icon-layer-list {
  &:before {
    content: unicode($esri-icon-layer-list);
    color: $fill-color;
  }
}
.esri-icon-basemap {
  &:before {
    content: unicode($esri-icon-basemap);
    color: $fill-color;
  }
}
.esri-icon-globe {
  &:before {
    content: unicode($esri-icon-globe);
    color: $fill-color;
  }
}
.esri-icon-applications {
  &:before {
    content: unicode($esri-icon-applications);
    color: $fill-color;
  }
}
.esri-icon-arrow-up-circled {
  &:before {
    content: unicode($esri-icon-arrow-up-circled);
    color: $fill-color;
  }
}
.esri-icon-arrow-down-circled {
  &:before {
    content: unicode($esri-icon-arrow-down-circled);
    color: $fill-color;
  }
}
.esri-icon-arrow-left-circled {
  &:before {
    content: unicode($esri-icon-arrow-left-circled);
    color: $fill-color;
  }
}
.esri-icon-arrow-right-circled {
  &:before {
    content: unicode($esri-icon-arrow-right-circled);
    color: $fill-color;
  }
}
.esri-icon-minus-circled {
  &:before {
    content: unicode($esri-icon-minus-circled);
    color: $fill-color;
  }
}
.esri-icon-plus-circled {
  &:before {
    content: unicode($esri-icon-plus-circled);
    color: $fill-color;
  }
}
.esri-icon-add-attachment {
  &:before {
    content: unicode($esri-icon-add-attachment);
    color: $fill-color;
  }
}
.esri-icon-attachment {
  &:before {
    content: unicode($esri-icon-attachment);
    color: $fill-color;
  }
}
.esri-icon-calendar {
  &:before {
    content: unicode($esri-icon-calendar);
    color: $fill-color;
  }
}
.esri-icon-close-circled {
  &:before {
    content: unicode($esri-icon-close-circled);
    color: $fill-color;
  }
}
.esri-icon-browser {
  &:before {
    content: unicode($esri-icon-browser);
    color: $fill-color;
  }
}
.esri-icon-collection {
  &:before {
    content: unicode($esri-icon-collection);
    color: $fill-color;
  }
}
.esri-icon-comment {
  &:before {
    content: unicode($esri-icon-comment);
    color: $fill-color;
  }
}
.esri-icon-configure-popup {
  &:before {
    content: unicode($esri-icon-configure-popup);
    color: $fill-color;
  }
}
.esri-icon-contact {
  &:before {
    content: unicode($esri-icon-contact);
    color: $fill-color;
  }
}
.esri-icon-dashboard {
  &:before {
    content: unicode($esri-icon-dashboard);
    color: $fill-color;
  }
}
.esri-icon-deny {
  &:before {
    content: unicode($esri-icon-deny);
    color: $fill-color;
  }
}
.esri-icon-description {
  &:before {
    content: unicode($esri-icon-description);
    color: $fill-color;
  }
}
.esri-icon-directions {
  &:before {
    content: unicode($esri-icon-directions);
    color: $fill-color;
  }
}
.esri-icon-directions2 {
  &:before {
    content: unicode($esri-icon-directions2);
    color: $fill-color;
  }
}
.esri-icon-documentation {
  &:before {
    content: unicode($esri-icon-documentation);
    color: $fill-color;
  }
}
.esri-icon-duplicate {
  &:before {
    content: unicode($esri-icon-duplicate);
    color: $fill-color;
  }
}
.esri-icon-review {
  &:before {
    content: unicode($esri-icon-review);
    color: $fill-color;
  }
}
.esri-icon-environment-settings {
  &:before {
    content: unicode($esri-icon-environment-settings);
    color: $fill-color;
  }
}
.esri-icon-error {
  &:before {
    content: unicode($esri-icon-error);
    color: $fill-color;
  }
}
.esri-icon-error2 {
  &:before {
    content: unicode($esri-icon-error2);
    color: $fill-color;
  }
}
.esri-icon-experimental {
  &:before {
    content: unicode($esri-icon-experimental);
    color: $fill-color;
  }
}
.esri-icon-feature-layer {
  &:before {
    content: unicode($esri-icon-feature-layer);
    color: $fill-color;
  }
}
.esri-icon-filter {
  &:before {
    content: unicode($esri-icon-filter);
    color: $fill-color;
  }
}
.esri-icon-grant {
  &:before {
    content: unicode($esri-icon-grant);
    color: $fill-color;
  }
}
.esri-icon-group {
  &:before {
    content: unicode($esri-icon-group);
    color: $fill-color;
  }
}
.esri-icon-key {
  &:before {
    content: unicode($esri-icon-key);
    color: $fill-color;
  }
}
.esri-icon-labels {
  &:before {
    content: unicode($esri-icon-labels);
    color: $fill-color;
  }
}
.esri-icon-tag {
  &:before {
    content: unicode($esri-icon-tag);
    color: $fill-color;
  }
}
.esri-icon-layers {
  &:before {
    content: unicode($esri-icon-layers);
    color: $fill-color;
  }
}
.esri-icon-left-arrow {
  &:before {
    content: unicode($esri-icon-left-arrow);
    color: $fill-color;
  }
}
.esri-icon-right-arrow {
  &:before {
    content: unicode($esri-icon-right-arrow);
    color: $fill-color;
  }
}
.esri-icon-link-external {
  &:before {
    content: unicode($esri-icon-link-external);
    color: $fill-color;
  }
}
.esri-icon-link {
  &:before {
    content: unicode($esri-icon-link);
    color: $fill-color;
  }
}
.esri-icon-loading-indicator {
  &:before {
    content: unicode($esri-icon-loading-indicator);
    color: $fill-color;
  }
}
.esri-icon-maps {
  &:before {
    content: unicode($esri-icon-maps);
    color: $fill-color;
  }
}
.esri-icon-marketplace {
  &:before {
    content: unicode($esri-icon-marketplace);
    color: $fill-color;
  }
}
.esri-icon-media {
  &:before {
    content: unicode($esri-icon-media);
    color: $fill-color;
  }
}
.esri-icon-media2 {
  &:before {
    content: unicode($esri-icon-media2);
    color: $fill-color;
  }
}
.esri-icon-menu {
  &:before {
    content: unicode($esri-icon-menu);
    color: $fill-color;
  }
}
.esri-icon-mobile {
  &:before {
    content: unicode($esri-icon-mobile);
    color: $fill-color;
  }
}
.esri-icon-phone {
  &:before {
    content: unicode($esri-icon-phone);
    color: $fill-color;
  }
}
.esri-icon-navigation {
  &:before {
    content: unicode($esri-icon-navigation);
    color: $fill-color;
  }
}
.esri-icon-pan {
  &:before {
    content: unicode($esri-icon-pan);
    color: $fill-color;
  }
}
.esri-icon-printer {
  &:before {
    content: unicode($esri-icon-printer);
    color: $fill-color;
  }
}
.esri-icon-pie-chart {
  &:before {
    content: unicode($esri-icon-pie-chart);
    color: $fill-color;
  }
}
.esri-icon-chart {
  &:before {
    content: unicode($esri-icon-chart);
    color: $fill-color;
  }
}
.esri-icon-line-chart {
  &:before {
    content: unicode($esri-icon-line-chart);
    color: $fill-color;
  }
}
.esri-icon-question {
  &:before {
    content: unicode($esri-icon-question);
    color: $fill-color;
  }
}
.esri-icon-resend-invitation {
  &:before {
    content: unicode($esri-icon-resend-invitation);
    color: $fill-color;
  }
}
.esri-icon-rotate {
  &:before {
    content: unicode($esri-icon-rotate);
    color: $fill-color;
  }
}
.esri-icon-save {
  &:before {
    content: unicode($esri-icon-save);
    color: $fill-color;
  }
}
.esri-icon-settings {
  &:before {
    content: unicode($esri-icon-settings);
    color: $fill-color;
  }
}
.esri-icon-settings2 {
  &:before {
    content: unicode($esri-icon-settings2);
    color: $fill-color;
  }
}
.esri-icon-share {
  &:before {
    content: unicode($esri-icon-share);
    color: $fill-color;
  }
}
.esri-icon-sign-out {
  &:before {
    content: unicode($esri-icon-sign-out);
    color: $fill-color;
  }
}
.esri-icon-support {
  &:before {
    content: unicode($esri-icon-support);
    color: $fill-color;
  }
}
.esri-icon-user {
  &:before {
    content: unicode($esri-icon-user);
    color: $fill-color;
  }
}
.esri-icon-time-clock {
  &:before {
    content: unicode($esri-icon-time-clock);
    color: $fill-color;
  }
}
.esri-icon-trash {
  &:before {
    content: unicode($esri-icon-trash);
    color: $fill-color;
  }
}
.esri-icon-upload {
  &:before {
    content: unicode($esri-icon-upload);
    color: $fill-color;
  }
}
.esri-icon-download {
  &:before {
    content: unicode($esri-icon-download);
    color: $fill-color;
  }
}
.esri-icon-zoom-in-magnifying-glass {
  &:before {
    content: unicode($esri-icon-zoom-in-magnifying-glass);
    color: $fill-color;
  }
}
.esri-icon-search {
  &:before {
    content: unicode($esri-icon-search);
    color: $fill-color;
  }
}
.esri-icon-zoom-out-magnifying-glass {
  &:before {
    content: unicode($esri-icon-zoom-out-magnifying-glass);
    color: $fill-color;
  }
}
.esri-icon-locked {
  &:before {
    content: unicode($esri-icon-locked);
    color: $fill-color;
  }
}
.esri-icon-unlocked {
  &:before {
    content: unicode($esri-icon-unlocked);
    color: $fill-color;
  }
}
.esri-icon-favorites {
  &:before {
    content: unicode($esri-icon-favorites);
    color: $fill-color;
  }
}
.esri-icon-compass {
  &:before {
    content: unicode($esri-icon-compass);
    color: $fill-color;
  }
}
.esri-icon-down {
  &:before {
    content: unicode($esri-icon-down);
    color: $fill-color;
  }
}
.esri-icon-up {
  &:before {
    content: unicode($esri-icon-up);
    color: $fill-color;
  }
}
.esri-icon-chat {
  &:before {
    content: unicode($esri-icon-chat);
    color: $fill-color;
  }
}
.esri-icon-dock-bottom {
  &:before {
    content: unicode($esri-icon-dock-bottom);
    color: $fill-color;
  }
}
.esri-icon-dock-left {
  &:before {
    content: unicode($esri-icon-dock-left);
    color: $fill-color;
  }
}
.esri-icon-dock-right {
  &:before {
    content: unicode($esri-icon-dock-right);
    color: $fill-color;
  }
}
.esri-icon-organization {
  &:before {
    content: unicode($esri-icon-organization);
    color: $fill-color;
  }
}
.esri-icon-north-navigation {
  &:before {
    content: unicode($esri-icon-north-navigation);
    color: $fill-color;
  }
}
.esri-icon-locate-circled {
  &:before {
    content: unicode($esri-icon-locate-circled);
    color: $fill-color;
  }
}
.esri-icon-dial {
  &:before {
    content: unicode($esri-icon-dial);
    color: $fill-color;
  }
}
.esri-icon-polygon {
  &:before {
    content: unicode($esri-icon-polygon);
    color: $fill-color;
  }
}
.esri-icon-polyline {
  &:before {
    content: unicode($esri-icon-polyline);
    color: $fill-color;
  }
}
.esri-icon-visible {
  &:before {
    content: unicode($esri-icon-visible);
    color: $fill-color;
  }
}
.esri-icon-non-visible {
  &:before {
    content: unicode($esri-icon-non-visible);
    color: $fill-color;
  }
}
.esri-icon-link-vertical {
  &:before {
    content: unicode($esri-icon-link-vertical);
    color: $fill-color;
  }
}
.esri-icon-unlocked-link-vertical {
  &:before {
    content: unicode($esri-icon-unlocked-link-vertical);
    color: $fill-color;
  }
}
.esri-icon-link-horizontal {
  &:before {
    content: unicode($esri-icon-link-horizontal);
    color: $fill-color;
  }
}
.esri-icon-unlocked-link-horizontal {
  &:before {
    content: unicode($esri-icon-unlocked-link-horizontal);
    color: $fill-color;
  }
}
.esri-icon-swap {
  &:before {
    content: unicode($esri-icon-swap);
    color: $fill-color;
  }
}
.esri-icon-cta-link-external {
  &:before {
    content: unicode($esri-icon-cta-link-external);
    color: $fill-color;
  }
}
.esri-icon-reply {
  &:before {
    content: unicode($esri-icon-reply);
    color: $fill-color;
  }
}
.esri-icon-public {
  &:before {
    content: unicode($esri-icon-public);
    color: $fill-color;
  }
}
.esri-icon-share2 {
  &:before {
    content: unicode($esri-icon-share2);
    color: $fill-color;
  }
}
.esri-icon-launch-link-external {
  &:before {
    content: unicode($esri-icon-launch-link-external);
    color: $fill-color;
  }
}
.esri-icon-rotate-back {
  &:before {
    content: unicode($esri-icon-rotate-back);
    color: $fill-color;
  }
}
.esri-icon-pan2 {
  &:before {
    content: unicode($esri-icon-pan2);
    color: $fill-color;
  }
}
.esri-icon-tracking {
  &:before {
    content: unicode($esri-icon-tracking);
    color: $fill-color;
  }
}
.esri-icon-expand2 {
  &:before {
    content: unicode($esri-icon-expand2);
    color: $fill-color;
  }
}
.esri-icon-arrow-down {
  &:before {
    content: unicode($esri-icon-arrow-down);
    color: $fill-color;
  }
}
.esri-icon-arrow-up {
  &:before {
    content: unicode($esri-icon-arrow-up);
    color: $fill-color;
  }
}
.esri-icon-hollow-eye {
  &:before {
    content: unicode($esri-icon-hollow-eye);
    color: $fill-color;
  }
}
.esri-icon-play-circled {
  &:before {
    content: unicode($esri-icon-play-circled);
    color: $fill-color;
  }
}
.esri-icon-volume-off {
  &:before {
    content: unicode($esri-icon-volume-off);
    color: $fill-color;
  }
}
.esri-icon-volume-on {
  &:before {
    content: unicode($esri-icon-volume-on);
    color: $fill-color;
  }
}
.esri-icon-bookmark {
  &:before {
    content: unicode($esri-icon-bookmark);
    color: $fill-color;
  }
}
.esri-icon-lightbulb {
  &:before {
    content: unicode($esri-icon-lightbulb);
    color: $fill-color;
  }
}
.esri-icon-sketch-rectangle {
  &:before {
    content: unicode($esri-icon-sketch-rectangle);
    color: $fill-color;
  }
}
.esri-icon-north-navigation-filled {
  &:before {
    content: unicode($esri-icon-north-navigation-filled);
    color: $fill-color;
  }
}
.esri-icon-default-action {
  &:before {
    content: unicode($esri-icon-default-action);
    color: $fill-color;
  }
}
.esri-icon-undo {
  &:before {
    content: unicode($esri-icon-undo);
    color: $fill-color;
  }
}
.esri-icon-redo {
  &:before {
    content: unicode($esri-icon-redo);
    color: $fill-color;
  }
}
.esri-icon-cursor {
  &:before {
    content: unicode($esri-icon-cursor);
    color: $fill-color;
  }
}
.esri-icon-cursor-filled {
  &:before {
    content: unicode($esri-icon-cursor-filled);
    color: $fill-color;
  }
}
.esri-icon-measure {
  &:before {
    content: unicode($esri-icon-measure);
    color: $fill-color;
  }
}
.esri-icon-measure-line {
  &:before {
    content: unicode($esri-icon-measure-line);
    color: $fill-color;
  }
}
.esri-icon-measure-area {
  &:before {
    content: unicode($esri-icon-measure-area);
    color: $fill-color;
  }
}
.esri-icon-legend {
  &:before {
    content: unicode($esri-icon-legend);
    color: $fill-color;
  }
}
.esri-icon-sliders {
  &:before {
    content: unicode($esri-icon-sliders);
    color: $fill-color;
  }
}
.esri-icon-sliders-horizontal {
  &:before {
    content: unicode($esri-icon-sliders-horizontal);
    color: $fill-color;
  }
}
.esri-icon-cursor-marquee {
  &:before {
    content: unicode($esri-icon-cursor-marquee);
    color: $fill-color;
  }
}
.esri-icon-lasso {
  &:before {
    content: unicode($esri-icon-lasso);
    color: $fill-color;
  }
}

.esri-icon-elevation-profile {
  &:before {
    content: unicode($esri-icon-elevation-profile);
    color: $fill-color;
  }
}
.esri-icon-slice {
  &:before {
    content: unicode($esri-icon-slice);
    color: $fill-color;
  }
}
.esri-icon-line-of-sight {
  &:before {
    content: unicode($esri-icon-line-of-sight);
    color: $fill-color;
  }
}
.esri-icon-zoom-to-object {
  &:before {
    content: unicode($esri-icon-zoom-to-object);
    color: $fill-color;
  }
}
.esri-icon-urban-model {
  &:before {
    content: unicode($esri-icon-urban-model);
    color: $fill-color;
  }
}
.esri-icon-measure-building-height-shadow {
  &:before {
    content: unicode($esri-icon-measure-building-height-shadow);
    color: $fill-color;
  }
}
.esri-icon-partly-cloudy {
  &:before {
    content: unicode($esri-icon-partly-cloudy);
    color: $fill-color;
  }
}
