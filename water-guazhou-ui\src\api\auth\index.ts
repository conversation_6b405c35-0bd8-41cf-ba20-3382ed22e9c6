import request from '@/plugins/axios';

export function changePassword(currentPassword, newPassword) {
  return request({
    url: '/api/auth/changePassword',
    method: 'post',
    data: {
      currentPassword,
      newPassword
    }
  });
}

export function resetPassword(token, password) {
  return request({
    url: `/api/noauth/resetPassword?istarLoginUrl=${encodeURIComponent(
      'https://ems.istarscloud.com/#'
    )}`,
    method: 'post',
    data: {
      resetToken: token,
      password
    }
  });
}
