<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.optionLog.CommandLogMapper">
    <select id="findList" resultType="org.thingsboard.server.dao.model.DTO.CommandLogDTO">
        SELECT a.*, b.name AS "deviceName", c.name AS "stationName"
        FROM tb_command_log a
        LEFT JOIN device b ON b.id = a.device_id
        LEFT JOIN tb_station c ON c.id = a.station_id
        <where>
            b.name IS NOT NULL
            <if test="param.tenantId != null and param.tenantId != ''">
                AND a.tenant_id = #{param.tenantId}
            </if>
            <if test="param.optionUserId != null and param.optionUserId != ''">
                AND a.option_user_id = #{param.optionUserId}
            </if>
            <if test="param.deviceId != null and param.deviceId != ''">
                AND a.device_id = #{param.deviceId}
            </if>
            <if test="param.stationId != null and param.stationId != ''">
                AND a.station_id = #{param.stationId}
            </if>
            <if test="param.beginTime != null">
                AND a.time <![CDATA[ >= ]]> #{param.beginTime}
            </if>
            <if test="param.endTime != null">
                AND a.time <![CDATA[ <= ]]> #{param.endTime}
            </if>
        </where>
    </select>

</mapper>