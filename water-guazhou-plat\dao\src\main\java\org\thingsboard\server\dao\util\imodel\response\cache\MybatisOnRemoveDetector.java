package org.thingsboard.server.dao.util.imodel.response.cache;

import org.apache.ibatis.cache.Cache;

import java.lang.ref.WeakReference;
import java.util.function.BiConsumer;

public class MybatisOnRemoveDetector implements Cache {
    private final Cache delegate;
    @SuppressWarnings("rawtypes")
    private final BiConsumer detector;


    @SuppressWarnings("rawtypes")
    public MybatisOnRemoveDetector(Cache delegate, BiConsumer detector) {
        this.delegate = delegate;
        this.detector = detector;
    }


    @Override
    public String getId() {
        return delegate.getId();
    }

    @Override
    public void putObject(Object key, Object value) {
        delegate.putObject(key, value);
    }

    @Override
    public Object getObject(Object key) {
        return delegate.getObject(key);
    }

    @Override
    public Object removeObject(Object key) {
        Object o = delegate.removeObject(key);
        if (o instanceof WeakReference) {
            o = ((WeakReference<?>) o).get();
        }
        if (detector != null && o != null) {
            //noinspection unchecked
            detector.accept(key, o);
        }
        return o;
    }

    @Override
    public void clear() {
        delegate.clear();
    }

    @Override
    public int getSize() {
        return delegate.getSize();
    }
}
