package org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartProduction.dispatch.DispatchMethod;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

import java.util.Date;

@Getter
@Setter
public class DispatchMethodSaveRequest extends SaveRequest<DispatchMethod> {
    // 方案名称
    @NotNullOrEmpty
    private String name;

    // 方案类型
    @NotNullOrEmpty
    private String type;

    // 日期标签。工作日/节假日
    @NotNullOrEmpty
    private String dateLabel;

    // 站点ID
    private String stationId;

    // 方案日期
    @NotNullOrEmpty
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date time;

    // 方案描述
    @NotNullOrEmpty
    private String remark;

    // 天气类型
    private String weatherType;

    // 最高温度
    private Double maxTemperature;

    // 最低温度
    private Double minTemperature;

    // 降雨量
    private Double rainfall;

    // 相对湿度
    private Double relativeHumidity;

    // 供水量
    private Double waterSupply;

    // 耗电量
    private Double powerConsumption;

    // 清水池液位
    private Double waterLevel;

    @Override
    protected DispatchMethod build() {
        DispatchMethod entity = new DispatchMethod();
        entity.setCreateTime(createTime());
        entity.setTenantId(tenantId());
        entity.setCreator(currentUserUUID());
        commonSet(entity);
        return entity;
    }

    @Override
    protected DispatchMethod update(String id) {
        DispatchMethod entity = new DispatchMethod();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(DispatchMethod entity) {
        entity.setName(name);
        entity.setType(type);
        entity.setDateLabel(dateLabel);
        entity.setStationId(stationId);
        entity.setTime(time);
        entity.setRemark(remark);
        entity.setWeatherType(weatherType);
        entity.setMaxTemperature(maxTemperature);
        entity.setMinTemperature(minTemperature);
        entity.setRainfall(rainfall);
        entity.setRelativeHumidity(relativeHumidity);
        entity.setWaterSupply(waterSupply);
        entity.setPowerConsumption(powerConsumption);
        entity.setWaterLevel(waterLevel);
        entity.setEditUser(currentUserUUID());
        entity.setEditTime(createTime());
        entity.setIsEnabled(false);
    }
}