<!-- 污水厂管理-水质分布 -->
<template>
  <div class="water-quality-distribution">
    <!-- 左侧地图 -->
    <div class="map-container">
      <ArcView ref="refArcView" @loaded="onMaploaded">
        <!-- 地图弹窗 -->
        <div v-if="state.windows?.length" class="infowindow-container">
          <template v-for="pop in state.windows" :key="pop.id">
            <ListWindow
              v-show="pop.visible"
              :ref="'refPop' + pop.id"
              :view="staticState.view"
              :config="pop"
              @toggled="(flag) => handlePopToggle(pop, flag)"
            >
              <div class="popup-content">
                <div
                  v-for="(item, index) in pop.attributes.values"
                  :key="index"
                  class="popup-item"
                >
                  <span class="popup-label">{{ item.label }}:</span>
                  <span class="popup-value">
                    {{ item.value }}
                    <span v-if="item.unit" class="popup-unit">{{ item.unit }}</span>
                  </span>
                </div>
              </div>
            </ListWindow>
          </template>
        </div>
      </ArcView>
    </div>

    <!-- 右侧数据面板 -->
    <div class="data-panel">
      <SLCard class="panel-content" title="水质分布" overlay>

   

        <!-- 水质数据列表 -->
        <div class="water-quality-list">
          <el-table
            :data="state.waterQualityData"
            :loading="state.loading"
            style="width: 100%"
            max-height="calc(100vh - 400px)"
            :show-header="true"
            size="small"
          >
            <el-table-column prop="sampleTime" label="采样时间" min-width="100">
              <template #default="{ row }">
                <div class="sample-time">{{ formatSampleTime(row.sampleTime) }}</div>
              </template>
            </el-table-column>
            <el-table-column prop="stationName" label="采样点位" min-width="100" />
            <el-table-column prop="waterQualityLevel" label="水质等级" min-width="80">
              <template #default="{ row }">
                <span :class="getQualityLevelClass(row.waterQualityLevel)">
                  {{ row.waterQualityLevel }}
                </span>
              </template>
            </el-table-column>
            <el-table-column label="检测报告" min-width="80">
              <template #default="{ row }">
                <el-button type="primary" link size="small" @click="handleViewReport(row)">
                  查看
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </SLCard>
    </div>

    <!-- 水质监控弹窗 -->
    <WaterQualityMonitorDialog
      v-model="state.showReportDialog"
      :data="state.currentReportData"
      @close="handleCloseReport"
    />
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref, onMounted, nextTick, getCurrentInstance } from 'vue'
import ArcView from '@/components/arcMap/ArcView.vue'
import ListWindow from '@/views/arcMap/components/popup/ListWindow.vue'
import WaterQualityMonitorDialog from '@/components/WaterQualityMonitorDialog/index.vue'
import { useBusinessStore } from '@/store'
import Point from '@arcgis/core/geometry/Point'
import Graphic from '@arcgis/core/Graphic'
import PictureMarkerSymbol from '@arcgis/core/symbols/PictureMarkerSymbol'
import { bindViewClick } from '@/utils/MapHelper'
import { getStationImageUrl } from '@/utils/URLHelper'
import { GetWaterPlantSupply, GetWaterPlanySupplyInfo } from '@/api/mapservice/onemap'
import type { MapPopupData } from './types'

const businessStore = useBusinessStore()
const refArcView = ref<any>()

const { proxy }: any = getCurrentInstance()

// 地图相关状态
const staticState: {
  view?: __esri.MapView
} = {}

const state = reactive<{
  statistics: {
    title: string
    count: number
    unit: string
    className: string
  }[]
  stationList: any[]
  waterQualityData: any[]
  loading: boolean
  windows: MapPopupData[]
  startDate: string
  endDate: string
  selectedStation: string
  selectedQualityLevel: string
  showReportDialog: boolean
  currentReportData: any
}>({
  statistics: [
    { className: 'text-blue', title: '监测站点', count: 0, unit: '个' },
    { className: 'text-green', title: '达标数据', count: 0, unit: '条' },
    { className: 'text-orange', title: '超标数据', count: 0, unit: '条' },
    { className: 'text-red', title: '异常数据', count: 0, unit: '条' }
  ],
  stationList: [],
  waterQualityData: [],
  loading: false,
  windows: [],
  startDate: '2025-06',
  endDate: '2025-06',
  selectedStation: '',
  selectedQualityLevel: '',
  showReportDialog: false,
  currentReportData: null
})



// 计算属性和方法
const getQualityLevelClass = (level: string) => {
  switch (level) {
    case 'I类':
      return 'quality-level-1'
    case 'II类':
      return 'quality-level-2'
    case 'III类':
      return 'quality-level-3'
    case 'IV类':
      return 'quality-level-4'
    case 'V类':
      return 'quality-level-5'
    default:
      return 'quality-level-unknown'
  }
}

// 格式化采样时间
const formatSampleTime = (time?: string) => {
  if (!time) return 'yyyy-MM-dd'
  const date = new Date(time)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}



// 生成水质数据
const generateWaterQualityData = (sewagePlants: any[]) => {
  const waterQualityData: any[] = []
  const qualityLevels = ['II类', 'III类', 'IV类']

  sewagePlants.forEach((station: any) => {
    // 为每个站点生成一条水质数据记录
    const sampleDate = new Date(Date.now())
    const randomLevel = qualityLevels[Math.floor(Math.random() * qualityLevels.length)]

    // 生成模拟的水质指标数据
    const indicators = {
      dissolvedOxygen: 4.5 + Math.random() * 3, // 4.5-7.5
      permanganate: 2 + Math.random() * 8, // 2-10
      cod: 15 + Math.random() * 20, // 15-35
      ammoniaNitrogen: 0.2 + Math.random() * 1.5, // 0.2-1.7
      totalPhosphorus: 0.05 + Math.random() * 0.3, // 0.05-0.35
      totalNitrogen: 0.3 + Math.random() * 1.5, // 0.3-1.8
      fecalColiform: 500 + Math.random() * 15000, // 500-15500
      ph: 6.5 + Math.random() * 2, // 6.5-8.5
      inflow: 80 + Math.random() * 40, // 进水流量 80-120
      outflow: 75 + Math.random() * 35, // 出水流量 75-110
      bod5: 5 + Math.random() * 15 // BOD5 5-20
    }

    waterQualityData.push({
      id: `${station.stationId || station.id}`,
      stationId: station.stationId || station.id,
      stationName: `${station.name || station.stationName}`,
      sampleTime: sampleDate.toISOString(),
      waterQualityLevel: randomLevel,
      reportId: `report-${station.stationId || station.id}`,
      indicators: indicators
    })
  })

  state.waterQualityData = waterQualityData

  // 更新统计信息
  const totalStations = sewagePlants.length
  const level2Data = waterQualityData.filter(d => d.waterQualityLevel === 'II类').length
  const level3Data = waterQualityData.filter(d => d.waterQualityLevel === 'III类').length
  const level4Data = waterQualityData.filter(d => d.waterQualityLevel === 'IV类').length

  state.statistics[0].count = totalStations
  state.statistics[1].count = level2Data
  state.statistics[2].count = level3Data
  state.statistics[3].count = level4Data
}



// 查看报告处理
const handleViewReport = (row: any) => {
  console.log('查看检测报告:', row)
  state.currentReportData = row
  state.showReportDialog = true
}

// 关闭报告弹窗
const handleCloseReport = () => {
  state.showReportDialog = false
  state.currentReportData = null
}

// 地图加载完成事件
const onMaploaded = async (view: __esri.MapView) => {
  console.log('地图加载完成，开始初始化')
  staticState.view = view

  // 等待地图完全准备好
  await new Promise(resolve => setTimeout(resolve, 1000))

  // 使用bindViewClick处理点击事件
  bindViewClick(staticState.view, (res) => {
    const result = res.results?.[0]
    if (!result) return
    if (result.type === 'graphic') {
      const row = result.graphic?.attributes?.row
      if (row?.fromWaterQuality) {
        handleMarkClick(row)
      }
    }
  })

  // 地图加载完成后直接初始化标记点（与lookBoard相同）
  console.log('开始调用initMapMarkers')
  await initMapMarkers()
}

// 初始化地图标记点
const initMapMarkers = async () => {
  try {
    if (!staticState.view) {
      console.log('地图视图未准备好')
      return
    }

    // 清除现有的图形
    staticState.view.graphics.removeAll()

    // 获取真实的污水厂数据（与lookBoard完全相同的逻辑）
    try {
      const response = await GetWaterPlantSupply({
        projectId: businessStore.selectedProject?.value
      })

      const dataList = response.data?.data || response.data || []
      console.log('获取到的原始数据:', dataList)

      if (Array.isArray(dataList) && dataList.length > 0) {
        // 过滤出污水厂
        const sewagePlants = dataList.filter((item: any) => {
          const name = item.name || item.stationName || ''
          return name.includes('污水') || name.includes('污水厂') || name.includes('污水处理厂')
        })

        console.log('过滤后的污水厂数据:', sewagePlants)
        state.stationList = sewagePlants

        sewagePlants.forEach((item: any, index: number) => {
          const location = item.location?.split(',')
          if (location?.length === 2 && location[0] && location[1]) {
            const longitude = parseFloat(location[0])
            const latitude = parseFloat(location[1])

            console.log(`站点坐标: ${longitude}, ${latitude}`)

            if (!isNaN(longitude) && !isNaN(latitude)) {
              const point = new Point({
                longitude: longitude,
                latitude: latitude,
                spatialReference: staticState.view!.spatialReference
              })

              const graphic = new Graphic({
                geometry: point,
                symbol: new PictureMarkerSymbol({
                  width: 40,
                  height: 40,
                  yoffset: 20,
                  url: getStationImageUrl('污水处理厂.png')
                }),
                attributes: {
                  row: {
                    ...item,
                    id: item.stationId || item.id || `sewage-plant-${index}`,
                    stationId: item.stationId || item.id,
                    name: item.name || item.stationName || `污水厂${index + 1}`,
                    fromWaterQuality: true
                  }
                }
              })

              staticState.view!.graphics.add(graphic)
              console.log(`成功添加标记点: ${item.name || item.stationName}`)
            } else {
              console.log('坐标解析失败:', location)
            }
          } else {
            console.log('位置信息无效:', item.location)
          }
        })

        // 生成水质数据
        generateWaterQualityData(sewagePlants)
      }
    } catch (apiError) {
      console.error('API调用失败:', apiError)
    }

    console.log('地图标记初始化完成，总共添加了', staticState.view.graphics.length, '个标记点')
  } catch (error) {
    console.error('初始化地图标记失败:', error)
  }
}

// 处理标记点点击事件
const handleMarkClick = async (row: any) => {
  if (!row || !staticState.view) return

  // 清空现有弹窗
  state.windows.length = 0

  // 找到对应的图形
  const graphic = staticState.view.graphics.find(
    (item) => item.attributes?.row?.id === row.id || item.attributes?.row?.stationId === row.stationId
  )

  if (!graphic) return

  const point = graphic.geometry as __esri.Point

  try {
    // 调用API获取污水厂汇总数据（与lookBoard相同）
    const response = await GetWaterPlanySupplyInfo({
      stationId: row.stationId || row.id
    })

    const detailData = response.data?.data || {}

    // 创建弹窗数据，包含API返回的详细信息
    const values = [
      { label: '污水厂名称', value: row.name || row.stationName || '污水厂' }
    ]

    // 添加今日处理量
    if (detailData.todayWaterSupply !== undefined && detailData.todayWaterSupply !== null) {
      values.push({
        label: '今日处理量',
        value: `${Number(detailData.todayWaterSupply).toFixed(1)} m³`
      })
    }

    // 添加昨日处理量
    if (detailData.yesterdayWaterSupply !== undefined && detailData.yesterdayWaterSupply !== null) {
      values.push({
        label: '昨日处理量',
        value: `${Number(detailData.yesterdayWaterSupply).toFixed(1)} m³`
      })
    }

    // 添加本月处理量
    if (detailData.monthWaterSupply !== undefined && detailData.monthWaterSupply !== null) {
      values.push({
        label: '本月处理量',
        value: `${Number(detailData.monthWaterSupply).toFixed(1)} m³`
      })
    }

    // 添加运行状态
    if (detailData.status !== undefined) {
      values.push({
        label: '运行状态',
        value: detailData.status === 1 ? '正常' : '异常'
      })
    }

    // 添加模拟的水质数据
    const mockWaterQuality = [
      { label: 'COD', value: '15.2 mg/L' },
      { label: 'BOD5', value: '8.5 mg/L' },
      { label: '氨氮', value: '2.1 mg/L' },
      { label: 'pH值', value: '7.2' }
    ]

    mockWaterQuality.forEach(item => {
      values.push(item)
    })

    const windowData: MapPopupData = {
      id: row.id || row.stationId,
      title: row.name || row.stationName || '污水厂',
      visible: false,
      x: point.x,
      y: point.y,
      offsetY: -30,
      attributes: {
        values: values,
        id: row.id || row.stationId
      }
    }

    state.windows.push(windowData)
    await nextTick()

    // 显示弹窗
    openPop(row.id || row.stationId)

  } catch (error) {
    // API调用失败时，显示基本信息
    const windowData: MapPopupData = {
      id: row.id || row.stationId,
      title: row.name || row.stationName || '污水厂',
      visible: false,
      x: point.x,
      y: point.y,
      offsetY: -30,
      attributes: {
        values: [
          { label: '污水厂名称', value: row.name || row.stationName || '污水厂' },
          { label: '数据获取', value: '暂时无法获取详细数据' }
        ],
        id: row.id || row.stationId
      }
    }

    state.windows.push(windowData)
    await nextTick()

    // 显示弹窗
    openPop(row.id || row.stationId)
  }
}

// 打开弹窗
const openPop = (id: string) => {
  const targetWindow = state.windows.find(w => w.id === id)
  if (targetWindow) {
    // 关闭其他弹窗
    state.windows.forEach(w => {
      w.visible = w.id === id
    })

    // 显示目标弹窗
    targetWindow.visible = true
  }
}

// 处理弹窗切换事件
const handlePopToggle = (pop: any, flag: boolean) => {
  // 弹窗切换事件处理
}

// 初始化
onMounted(() => {
  // 数据获取在地图加载完成后进行
})
</script>

<style lang="scss" scoped>
.water-quality-distribution {
  display: flex;
  height: 100vh;
  width: 100%;
}

.map-container {
  flex: 1;
  position: relative;

  .infowindow-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1000;

    :deep(.list-window) {
      pointer-events: auto;
    }
  }
}

.data-panel {
  width: 400px;
  background-color: var(--el-bg-color-page);
  border-left: 1px solid var(--el-border-color);
  display: flex;
  flex-direction: column;

  .panel-content {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding: 16px;
    }
  }
}

.statistics {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-bottom: 16px;
}

.statistics-item {
  .item-inner {
    background-color: var(--el-bg-color);
    border: 1px solid var(--el-border-color);
    border-radius: 8px;
    padding: 12px;
    display: flex;
    align-items: center;
    gap: 8px;

    .total {
      display: flex;
      align-items: baseline;
      margin-left: auto;
    }

    .title {
      font-size: 14px;
      color: var(--el-text-color-regular);
    }

    .count {
      font-size: 18px;
      font-weight: bold;
    }

    .unit {
      font-size: 12px;
      color: var(--el-text-color-regular);
      margin-left: 2px;
    }
  }
}

.text-blue {
  color: #409eff;
}

.text-green {
  color: #67c23a;
}

.text-red {
  color: #f56c6c;
}

.text-orange {
  color: #e6a23c;
}

.search-section {
  margin-bottom: 16px;

  .search-item {
    margin-bottom: 12px;

    .search-label {
      font-size: 14px;
      color: var(--el-text-color-regular);
      margin-bottom: 8px;
      display: block;
    }

    .date-range {
      display: flex;
      align-items: center;
      gap: 8px;

      .date-separator {
        font-size: 14px;
        color: var(--el-text-color-regular);
      }
    }

    .station-select {
      display: flex;
      align-items: center;
      gap: 8px;

      .station-avatar {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        background-color: var(--el-color-primary-light-9);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--el-color-primary);
      }

      .station-label {
        font-size: 14px;
        color: var(--el-text-color-regular);
      }
    }
  }
}

.water-quality-list {
  flex: 1;
  display: flex;
  flex-direction: column;

  .sample-time {
    font-size: 12px;
    line-height: 1.2;
    white-space: pre-line;
    color: var(--el-text-color-regular);
  }

  .quality-level-1 {
    color: #67c23a;
    font-weight: bold;
  }

  .quality-level-2 {
    color: #409eff;
    font-weight: bold;
  }

  .quality-level-3 {
    color: #e6a23c;
    font-weight: bold;
  }

  .quality-level-4 {
    color: #f56c6c;
    font-weight: bold;
  }

  .quality-level-5 {
    color: #909399;
    font-weight: bold;
  }

  .quality-level-unknown {
    color: var(--el-text-color-placeholder);
  }

  :deep(.el-table) {
    font-size: 12px;
    border: none;

    .el-table__header th {
      background-color: var(--el-bg-color);
      font-size: 12px;
      font-weight: bold;
      color: var(--el-text-color-primary);
      border-bottom: 1px solid var(--el-border-color);
    }

    .el-table__body td {
      padding: 12px 8px;
      border-bottom: 1px solid var(--el-border-color-lighter);
    }

    .el-table__row:hover {
      background-color: var(--el-bg-color-page);
    }
  }
}

.popup-content {
  padding: 8px;

  .popup-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 4px 0;
    border-bottom: 1px solid var(--el-border-color-lighter);

    &:last-child {
      border-bottom: none;
    }

    .popup-label {
      font-weight: bold;
      color: var(--el-text-color-primary);
      min-width: 80px;
    }

    .popup-value {
      color: var(--el-text-color-regular);

      .popup-unit {
        color: var(--el-text-color-placeholder);
        margin-left: 2px;
      }
    }
  }
}



// 响应式适配
@media (max-width: 1200px) {
  .data-panel {
    width: 350px;
  }

  .report-content .info-section .info-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .report-content .chart-section .chart-container {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .water-quality-distribution {
    flex-direction: column;
  }

  .map-container {
    height: 50vh;
  }

  .data-panel {
    width: 100%;
    height: 50vh;
  }

  .report-content .info-section .info-grid {
    grid-template-columns: 1fr;
  }
}
</style>
