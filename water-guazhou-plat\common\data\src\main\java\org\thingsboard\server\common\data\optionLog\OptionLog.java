/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.common.data.optionLog;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.EqualsAndHashCode;
import org.thingsboard.server.common.data.BaseData;
import org.thingsboard.server.common.data.id.OptionLogId;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.id.UserId;

@EqualsAndHashCode(callSuper = true)
public class OptionLog extends BaseData<OptionLogId> {

    private UserId userId;
    private TenantId tenantId;
    private String firstName;
    private String authority;
    private String options;
    private Long createTime;
    private JsonNode additionalInfo;
    private String type;
    private String info;
    private String typeDescription;
    private String status;
    private String examineId;
    private String examineName;
    private String examineTenantId;
    private Long examineTime;

    public OptionLog() {
        super();
    }

    public OptionLog(OptionLogId id) {
        super(id);
    }

    public OptionLog(OptionLog log){
        super();
        this.userId = log.getUserId();
        this.tenantId = log.getTenantId();
        this.firstName = log.getFirstName();
        this.authority = log.getAuthority();
        this.options = log.getOptions();
        this.createTime = log.getCreateTime();
        this.additionalInfo = log.getAdditionalInfo();
        this.status = log.getStatus();
        this.examineId = log.getExamineId();
        this.examineName = log.getExamineName();
        this.examineTenantId = log.getExamineTenantId();
        this.examineTime = log.getExamineTime();
    }

    public String getExamineId() {
        return examineId;
    }

    public void setExamineId(String examineId) {
        this.examineId = examineId;
    }

    public String getExamineName() {
        return examineName;
    }

    public void setExamineName(String examineName) {
        this.examineName = examineName;
    }

    public String getExamineTenantId() {
        return examineTenantId;
    }

    public void setExamineTenantId(String examineTenantId) {
        this.examineTenantId = examineTenantId;
    }

    public Long getExamineTime() {
        return examineTime;
    }

    public void setExamineTime(Long examineTime) {
        this.examineTime = examineTime;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public UserId getUserId() {
        return userId;
    }

    public void setUserId(UserId userId) {
        this.userId = userId;
    }

    public TenantId getTenantId() {
        return tenantId;
    }

    public void setTenantId(TenantId tenantId) {
        this.tenantId = tenantId;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getAuthority() {
        return authority;
    }

    public void setAuthority(String authority) {
        this.authority = authority;
    }

    public String getOptions() {
        return options;
    }

    public void setOptions(String options) {
        this.options = options;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public JsonNode getAdditionalInfo() {
        return additionalInfo;
    }

    public void setAdditionalInfo(JsonNode additionalInfo) {
        this.additionalInfo = additionalInfo;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getInfo() {
        return info;
    }

    public void setInfo(String info) {
        this.info = info;
    }

    public String getTypeDescription() {
        return typeDescription;
    }

    public void setTypeDescription(String typeDescription) {
        this.typeDescription = typeDescription;
    }
}
