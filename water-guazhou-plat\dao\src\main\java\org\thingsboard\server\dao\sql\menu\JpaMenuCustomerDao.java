/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.sql.menu;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Component;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.id.MenuCustomerId;
import org.thingsboard.server.common.data.id.MenuTenantId;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.menu.MenuCustomer;
import org.thingsboard.server.common.data.menu.MenuTypeVO;
import org.thingsboard.server.dao.DaoUtil;
import org.thingsboard.server.dao.menu.MenuCustomerDao;
import org.thingsboard.server.dao.model.sql.MenuCustomerEntity;
import org.thingsboard.server.dao.sql.JpaAbstractDao;
import org.thingsboard.server.dao.util.SqlDao;
import org.thingsboard.server.dao.util.mapping.JacksonUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Component
@SqlDao
@Slf4j
public class JpaMenuCustomerDao extends JpaAbstractDao<MenuCustomerEntity, MenuCustomer> implements MenuCustomerDao {

    @Autowired
    private MenuCustomerRepository menuCustomerRepository;

    @Override
    protected Class<MenuCustomerEntity> getEntityClass() {
        return MenuCustomerEntity.class;
    }

    @Override
    protected CrudRepository<MenuCustomerEntity, String> getCrudRepository() {
        return menuCustomerRepository;
    }

    @Override
    public void deleteByTenantId(TenantId tenantId) {
        menuCustomerRepository.deleteByTenantId();
    }

    @Override
    public List<MenuCustomer> findByTenantId(TenantId tenantId, MenuCustomerId root) {
        return DaoUtil.convertDataList(
                menuCustomerRepository
                        .findMenuByTenantId(
                                UUIDConverter.fromTimeUUID(tenantId.getId()),
                                UUIDConverter.fromTimeUUID(root.getId())));
    }

    @Override
    public List<MenuCustomer> findByTenantId(TenantId tenantId) {
        return DaoUtil.convertDataList(
                menuCustomerRepository
                        .findMenuByTenantId(UUIDConverter.fromTimeUUID(tenantId.getId())));
    }

    @Override
    public List<Integer> getTypesByTenantId(TenantId tenantId) {
        return menuCustomerRepository.getTypesByTenantId(UUIDConverter.fromTimeUUID(tenantId.getId()));
    }

    @Override
    public List<MenuTypeVO> getTypes(TenantId tenantId) {
        List types = menuCustomerRepository.getTypes(UUIDConverter.fromTimeUUID(tenantId.getId()));
        List<MenuTypeVO> menuTypeVOList = new ArrayList<>();
        for (Object o : types) {
            Object[] data = (Object[]) o;
            MenuTypeVO vo = new MenuTypeVO();
            vo.setType((Integer) data[0]);
            vo.setName((String) data[1]);
            // 获取component
            Map map = JacksonUtil.fromString((String) data[2], Map.class);
            vo.setComponent((String) map.get("component"));

            menuTypeVOList.add(vo);
        }

        return menuTypeVOList;
    }

    @Override
    public List<MenuCustomer> findByMenuTenantIdIn(List<String> menuIdList) {
        return DaoUtil.convertDataList(menuCustomerRepository.findByIdInOrderByOrderNumDesc(menuIdList.toArray()));
    }

    @Override
    public Integer getTypeByMenuTenantId(MenuTenantId menuTenantId) {
        return menuCustomerRepository.getTypeByMenuTenantId(UUIDConverter.fromTimeUUID(menuTenantId.getId()));
    }

    @Override
    public MenuCustomer findByMenuTenantId(MenuTenantId menuTenantId, TenantId tenantId) {
        return DaoUtil.getData(
                menuCustomerRepository.findByMenuTenantId(UUIDConverter.fromTimeUUID(menuTenantId.getId()), UUIDConverter.fromTimeUUID(tenantId.getId())));
    }

    @Override
    public void deleteByParentId(MenuCustomerId id) {
        menuCustomerRepository.deleteByParentId(UUIDConverter.fromTimeUUID(id.getId()));
    }

    @Override
    public List<MenuCustomer> findByMenuTenantIdInAndTenantId(List<String> menuTenantIdList, TenantId tenantId) {
        return DaoUtil.convertDataList(
                menuCustomerRepository.findByMenuTenantId(menuTenantIdList, UUIDConverter.fromTimeUUID(tenantId.getId())));
    }
}
