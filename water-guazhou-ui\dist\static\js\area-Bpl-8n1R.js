import{z as i}from"./index-r0dFAfgr.js";const e=t=>i({url:"/api/sm/circuitDistrictPoint",method:"get",params:t}),s=t=>i({url:"/api/sm/circuitDistrictPoint",method:"post",data:t}),c=t=>i({url:`/api/sm/circuitDistrictPoint/${t}`,method:"delete"}),o=t=>i({url:`/api/sm/circuitDistrictArea/${t}/points`,method:"get"}),a=t=>i({url:"/api/sm/circuitDistrictArea",method:"post",data:t}),u=t=>i({url:`/api/sm/circuitDistrictArea/${t}`,method:"delete"}),n=t=>i({url:"/api/sm/circuitDistrict",method:"post",data:t}),d=t=>i({url:`/api/sm/circuitDistrict/${t}`,method:"delete"}),m=t=>i({url:"/api/sm/circuitDistrict/tree",method:"get",params:t});export{n as A,c as D,o as G,e as a,m as b,d as c,u as d,a as e,s as f};
