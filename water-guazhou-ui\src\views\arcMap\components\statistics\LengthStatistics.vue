<template>
  <div>
    <Form ref="refForm" :config="FormConfig"> </Form>
    <Panel
      ref="refChartPanel"
      :telport="telport"
      custom-class="gis-length-statistics-panel"
      title="长度统计结果"
    >
      <Search
        ref="refChartTab"
        style="padding: 0"
        :config="ChartTabConfig"
      ></Search>
      <div class="chart-box">
        <VChart ref="refChart" :option="state.chartOption"></VChart>
      </div>
    </Panel>
    <PipeDetail
      ref="refDetail"
      :tabs="state.tabs"
      :telport="props.telport"
      @close="() => (state.curOperate = '')"
      @refreshed="() => (state.curOperate = '')"
      @refreshing="() => (state.curOperate = 'detailing')"
      @rowdblclick="handleLocate"
    ></PipeDetail>
  </div>
</template>
<script lang="ts" setup>
import { queryLayerClassName } from '@/api/mapservice';
import {
  GetFieldConfig,
  GetFieldUniqueValue
} from '@/api/mapservice/fieldconfig';
import { PipeStatistics } from '@/api/mapservice/pipe';
import Search from '@/components/Form/Search.vue';
import { IECharts } from '@/plugins/echart';
import { useGisStore } from '@/store';
import { formatTree } from '@/utils/GlobalHelper';
import {
  EStatisticField,
  createGeometry,
  createPolygon,
  createPolyline,
  excuteQueryForIds,
  getGraphicLayer,
  getSubLayerIds,
  initDrawer,
  initQueryParams,
  setMapCursor
} from '@/utils/MapHelper';
import { SLMessage } from '@/utils/Message';
import PipeDetail from '../common/PipeDetail.vue';

const props = defineProps<{
  view?: __esri.MapView;
  telport?: string;
}>();

const refForm = ref<IFormIns>();
const refChart = ref<IECharts>();
const refChartPanel = ref<IPanelIns>();
const refChartTab = ref<ISearchIns>();
const refDetail = ref<InstanceType<typeof PipeDetail>>();
const state = reactive<{
  curNode?: any;
  curLayer?: NormalOption;
  curOperate: 'uniqueing' | 'statistising' | 'detailing' | 'drawing' | '';
  pipeLayerOption: NormalOption[];
  tabs: IPipeDetailTab[];
  staticFields: string[];
  layerFields: Record<string, NormalOption[]>;
  chartOption: any;
  alllayersbardata: any[];
  chartFields: {
    layerName: string;
    fields: NormalOption[];
  }[];
}>({
  curNode: undefined,
  curOperate: '',
  pipeLayerOption: [],
  tabs: [],
  staticFields: [
    'SUBTYPE ',
    'DISTRICT',
    'MATERIAL',
    'DIAMETER',
    'JUNCTION',
    'OCCUPYSURF',
    'GASCHAR',
    'ANTIMATERIAL',
    'DATATYPE',
    'VENDER',
    'OWNERDEPT',
    'MANAGEDEPT'
  ],
  layerFields: {},
  chartOption: null,
  alllayersbardata: [],
  chartFields: []
});
const staticState: {
  drawer?: __esri.Draw;
  drawAction?: __esri.DrawAction;
  graphicsLayer?: __esri.GraphicsLayer;
  queryGeometry?: __esri.Geometry;
  chartTabs: {
    layerid: number;
    layername: string;
    rows: Record<string, any>[];
    // {
    //   'DIAMETER': number,
    //   'MATERIAL': string,
    //   'DISTRICT': string,
    //   [EStatisticField.ShapeLen]: number
    // }[]
  }[];
  staticField: string;
} = {
  chartTabs: [],
  staticField: EStatisticField.ShapeLen
};
const FormConfig = reactive<IFormConfig>({
  group: [
    {
      fieldset: {
        desc: '统计字段'
      },
      fields: [
        {
          type: 'tree',
          options: [],
          style: {
            width: '50%',
            height: '200px',
            padding: '8px'
          },
          field: 'layer',
          nodeKey: 'label',
          showCheckbox: true,
          // multiple: true,
          className: 'sql-list-wrapper',
          handleCheckChange: async (data: NormalOption, isChecked: boolean) => {
            state.curLayer = data;
            await handleLayerChange(data.label, isChecked);
          },
          extraFormItem: [
            {
              type: 'tree',
              style: {
                width: '50%',
                height: '200px',
                padding: '8px'
              },
              showCheckbox: true,
              multiple: true,
              className: 'sql-list-wrapper',
              field: 'staticFields',
              options: [],
              handleCheckChange: async (
                data: NormalOption,
                isChecked: boolean
              ) => {
                if (!state.curLayer) return;
                const chartFields = state.chartFields.find(
                  (item) => item.layerName === state.curLayer?.label
                );
                if (chartFields) {
                  const index = chartFields.fields.findIndex(
                    (item) => item.value === data.value
                  );
                  index !== -1 && chartFields.fields.splice(index, 1);
                  isChecked && chartFields.fields.push(data);
                }
              }
            }
          ]
        }
      ]
    },
    {
      id: 'field-construct',
      fieldset: {
        desc: '过滤条件（可选）'
      },
      fields: [
        {
          type: 'select',
          label: '图层名称',
          field: 'layer1',
          options: []
        },
        {
          type: 'list',
          label: '图层字段',
          data: [],
          className: 'sql-list-wrapper',
          setData: async (config: IFormList, row) => {
            if (!row.layer1) return;
            const fields = await GetFieldConfig(row.layer1);
            config.data = fields.data?.result?.rows;
          },
          setDataBy: 'layer1',
          displayField: 'alias',
          valueField: 'name',
          highlightCurrentRow: true,
          nodeClick: (node) => {
            state.curNode = node;
            appendSQL(node.name);
          }
        },
        {
          type: 'btn-group',
          size: 'small',
          style: {
            width: '40%',
            height: '144px',
            display: 'flex',
            flexWrap: 'wrap'
          },
          className: 'sql-btns-wrapper',
          btns: [
            {
              perm: true,
              text: '=',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('=');
              }
            },
            {
              perm: true,
              text: '模糊',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL("like '%替换此处%'");
              }
            },
            {
              perm: true,
              text: '>',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('>');
              }
            },
            {
              perm: true,
              text: '<',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('<');
              }
            },
            {
              perm: true,
              text: '非',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('<>');
              }
            },
            {
              perm: true,
              text: '并且',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('and');
              }
            },
            {
              perm: true,
              text: '或者',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('or');
              }
            },
            {
              perm: true,
              text: '%',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('%');
              }
            }
          ],
          extraFormItem: [
            {
              type: 'list',
              wrapperStyle: {
                width: '60%',
                height: '144px'
              },
              className: 'sql-list-wrapper',
              field: 'uniqueValue',
              data: [],
              nodeClick: (node) => {
                appendSQL("'" + node + "'");
              },
              filters: [
                {
                  type: 'btn-group',
                  btns: [
                    {
                      perm: true,
                      text: () =>
                        state.curOperate === 'uniqueing'
                          ? '正在获取唯一值'
                          : '获取唯一值',
                      loading: () => state.curOperate === 'uniqueing',
                      disabled: () => state.curOperate === 'detailing',
                      styles: {
                        width: '100%',
                        borderRadius: '0'
                      },
                      click: () => getUniqueValue()
                    }
                  ]
                }
              ]
            }
          ]
        },
        {
          type: 'textarea',
          label: '属性条件',
          field: 'sql',
          placeholder: 'OBJECTID > 0'
        },
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: '清除',
              size: 'small',
              type: 'danger',
              click: () => handleDelete()
            }
          ]
        },
        {
          type: 'btn-group',
          label: '几何条件：',
          size: 'small',
          btns: [
            {
              perm: true,
              text: '绘制范围',
              type: 'success',
              click: () => startDraw()
            },
            {
              perm: true,
              text: '清空范围',
              type: 'danger',
              click: () => clearRange()
            }
          ]
        }
      ]
    },
    {
      fields: [
        {
          type: 'btn-group',
          itemContainerStyle: {
            marginBottom: '5px'
          },
          btns: [
            {
              perm: true,
              text: () =>
                state.curOperate === 'statistising' ? '正在统计' : '统计',
              loading: () => state.curOperate === 'statistising',
              disabled: () =>
                ['statistising', 'detailing', 'drawing'].indexOf(
                  state.curOperate
                ) !== -1,
              type: 'warning',
              click: () => startStatistics(),
              styles: {
                width: '100%'
              }
            }
          ]
        }
      ]
    },
    {
      fields: [
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: () =>
                state.curOperate === 'detailing' ? '正在查询' : '查看详情',
              loading: () => state.curOperate === 'detailing',
              disabled: () =>
                ['statistising', 'detailing', 'drawing'].indexOf(
                  state.curOperate
                ) !== -1,
              click: () => {
                state.curOperate = 'detailing';
                refDetail.value?.openDialog();
              },
              styles: {
                width: '100%'
              }
            }
          ]
        }
      ]
    }
  ],
  labelPosition: 'top',
  gutter: 12
});
const ChartTabConfig = reactive<ISearch>({
  filters: [
    {
      type: 'tabs',
      field: 'type',
      itemContainerStyle: {
        width: '100%'
      },
      tabs: [],
      onChange: () => resetChartData()
    }
  ]
});

const initBaseLayer = async () => {
  if (!props.view) return;
  const sublayers = getSubLayerIds(props.view);
  const layersres = await queryLayerClassName(sublayers);
  const layers = layersres.data?.result?.rows || [];
  state.pipeLayerOption = [];
  layers.map((item) => {
    if (
      item.geometrytype === 'esriGeometryPolyline' ||
      item.layername.indexOf('立管') > -1
    ) {
      state.pipeLayerOption?.push({
        label: item.layername,
        value: item.layername,
        id: item.layerid,
        data: item
      });
    }
  });

  const layerField = FormConfig.group[0].fields[0] as IFormTree;
  layerField && (layerField.options = state.pipeLayerOption);
  state.curLayer = layerField.options[0];
  const layerField1 = FormConfig.group[1].fields[0] as IFormTree;
  layerField1 && (layerField1.options = state.pipeLayerOption);
  if (refForm.value?.dataForm) {
    refForm.value.dataForm.layer = state.pipeLayerOption.map(
      (item) => item.value
    );
  }

  await handleLayerChange(state.pipeLayerOption[0].label, true);
};
const getFields = async (layername): Promise<NormalOption[]> => {
  if (state.layerFields[layername]) return state.layerFields[layername];

  const res = await GetFieldConfig(layername);
  const result = formatTree(res.data?.result?.rows || [], {
    id: 'name',
    value: 'name',
    label: 'alias'
  });
  state.layerFields[layername] = result;
  return result;
};
const handleLayerChange = async (layername, isChecked) => {
  const extraFormItem = FormConfig.group[0].fields[0].extraFormItem;
  const fields = extraFormItem && (extraFormItem[0] as IFormTree);
  const options = isChecked ? await getFields(layername) : [];
  fields &&
    (fields.options = options.filter((item) => {
      return state.staticFields.indexOf(item.value) !== -1;
    }));
  state.chartFields = [];
  if (isChecked) {
    state.chartFields.unshift({
      layerName: layername,
      // 解构，不然修改state.chartFields[x].fields时会导致fields?.options也跟着改变
      fields: [...(fields?.options || [])]
    });
  } else {
    state.chartFields = state.chartFields.filter(
      (item) => item.layerName !== layername
    );
  }
  refForm.value &&
    (refForm.value.dataForm.staticFields = fields?.options?.map(
      (item) => item.value
    ));
};
const getUniqueValue = async () => {
  if (!state.curNode) return;
  state.curOperate = 'uniqueing';
  try {
    const layerid = state.pipeLayerOption.find(
      (item) => item.label === refForm.value?.dataForm.layer1
    )?.id;
    const res = await GetFieldUniqueValue({
      usertoken: useGisStore().gToken,
      layerid,
      f: 'pjson',
      field_name: state.curNode.name
    });
    const extraFormItem = FormConfig.group.find(
      (item) => item.id === 'field-construct'
    )?.fields[2].extraFormItem;
    const field = extraFormItem && (extraFormItem[0] as IFormList);
    field && (field.data = res.data.result.rows);
  } catch (error) {
    SLMessage.error('获取唯一值失败');
  }
  state.curOperate = '';
};
const appendSQL = (val) => {
  if (!refForm.value) return;
  if (!refForm.value?.dataForm) refForm.value.dataForm = {};
  const sql = refForm.value.dataForm.sql || ' ';
  refForm.value.dataForm.sql = sql + val + ' ';
};
const startDraw = () => {
  if (!props.view) return;
  // 绘制前清空sql,因为sql和绘制查询不兼容
  handleDelete();
  refDetail.value?.closeDialog();
  refChartPanel.value?.Close();
  state.curOperate = 'drawing';
  staticState.graphicsLayer = getGraphicLayer(props.view, {
    id: 'length-statistics',
    title: '长度统计绘制图层'
  });
  staticState.queryGeometry = undefined;
  setMapCursor('crosshair');
  staticState.drawer = initDrawer(props.view);
  staticState.drawAction = staticState.drawer.create('polygon');
  staticState.drawAction.on(['vertex-add', 'cursor-update'], updateVertices);
  staticState.drawAction.on('draw-complete', (e) => {
    updateVertices(e);
    setMapCursor('');
    const geometry = createGeometry(
      'polygon',
      e.vertices,
      props.view?.spatialReference
    );
    staticState.queryGeometry = geometry;
    state.curOperate = '';
  });
};

const updateVertices = (e) => {
  const graphic =
    e.vertices.length < 3
      ? createPolyline(e.vertices, props.view?.spatialReference)
      : createPolygon(e.vertices, props.view?.spatialReference);
  staticState.graphicsLayer?.removeAll();
  graphic && staticState.graphicsLayer?.add(graphic);
};
const handleLocate = async () => {
  props.view && refDetail.value?.extentTo(props.view);
};
const startStatistics = async () => {
  const layerIds = refForm.value?.dataForm.layer;
  if (!layerIds?.length) {
    SLMessage.warning('请选择要统计的图层');
    return;
  }
  const fields = refForm.value?.dataForm?.staticFields;
  if (!fields?.length) {
    SLMessage.warning('请选择要统计的属性');
    return;
  }
  state.curOperate = 'statistising';
  try {
    const checkedLayers: any[] = refForm.value?.dataForm.layer || [];
    const layers = state.pipeLayerOption.filter((item) => {
      return checkedLayers.indexOf(item.value) !== -1;
    });
    const sql = refForm.value?.dataForm?.sql;
    const layerIds = layers.map((item) => item.id);
    const res = await PipeStatistics({
      usertoken: useGisStore().gToken,
      layerids: JSON.stringify(layerIds),
      group_fields: JSON.stringify(refForm.value?.dataForm.staticFields || []),
      statistic_field: staticState.staticField,
      statistic_type: '2',
      where: sql,
      geometry: staticState.queryGeometry,
      f: 'pjson'
    });
    console.log(res);
    if (res.data.code === 10000) {
      staticState.chartTabs = res.data?.result?.rows || [];
      setChartTabs();
      refChartPanel.value?.Open();
      nextTick(() => {
        resetChartData();
      });
    } else {
      SLMessage.error('统计失败');
      state.curOperate = '';
      return;
    }
    state.tabs = [];
    await getTabOids(layers, 0, sql);
    if (!state.tabs.length) {
      SLMessage.info('查询结果为空');
    }
  } catch (error: any) {
    SLMessage.error(error.message || '统计失败');
  }
  state.curOperate = '';
};
const setChartTabs = () => {
  const field = ChartTabConfig.filters?.find(
    (item) => item.field === 'type'
  ) as ITabs;
  const tabs = staticState.chartTabs.map((item) => {
    return {
      label: item.layername,
      name: item.layerid
    };
  });
  field &&
    (field.tabs = tabs.map((item) => {
      return {
        ...item,
        value: item.name
      };
    }));
  refChartTab.value?.queryParams &&
    (refChartTab.value.queryParams.type = tabs[0]?.name);
};
const resetChartData = () => {
  const xData: string[] = [];
  const Data: number[] = [];
  const layerId = refChartTab.value?.queryParams.type;
  const chartTab = staticState.chartTabs.find(
    (item) => item.layerid === layerId
  );
  const name = chartTab?.layername;
  const fields =
    state.chartFields.find((item) => item.layerName === name)?.fields || [];

  chartTab?.rows.map((item) => {
    let xStr = '';
    fields.map((field) => {
      const val = item[field.value];
      const part = val === null || val === '' || val === ' ' ? '<空>' : val;
      if (xStr === '') {
        xStr = part;
      } else {
        xStr += ' ' + part;
      }
    });
    xStr && Data.push(item[staticState.staticField]);
    xStr && xData.push(xStr?.toString());
  });
  refChart.value?.resize();
  refChart.value?.clear();
  state.chartOption = {
    title: {},
    tooltip: {
      trigger: 'axis'
    },
    calculable: true,
    xAxis: [
      {
        type: 'category',
        data: xData.map((str) => {
          if (str?.indexOf && str.indexOf(' ') > 0) {
            return str.replace(/\s+/g, '\n');
          }
          return str;
        }),
        axisLabel: {
          interval: 0, /// /横轴信息全部显示
          rotate: 0 // 60度角倾斜显示
        }
      }
    ],
    grid: {
      // 控制图的大小，调整下面这些值就可以，
      left: 60,
      right: 72,
      top: 40,
      bottom: 96 // y2可以控制 X轴跟Zoom控件之间的间隔，避免以为倾斜后造成 label重叠到zoom上
    },
    yAxis: [
      {
        type: 'value',
        name: '单位(m)',
        splitLine: {
          lineStyle: {
            color: '#fff',
            opacity: 0.2
          }
        }
      }
    ],
    dataZoom: [
      { show: true, start: 0, end: 100 },
      { type: 'inside', start: 0, end: 100 },
      {
        show: true,
        yAxisIndex: 0,
        filterMode: 'empty',
        width: 30,
        height: '80%',
        showDataShadow: false,
        left: '93%'
      }
    ],
    series: [
      {
        name: chartTab?.layername,
        type: 'bar',
        data: Data || [],
        barWidth: 30,
        label: {
          show: true,
          position: 'top'
        },
        itemStyle: {
          normal: {
            color: '#337AB7'
          }
        }
      }
    ]
  };
};
const getTabOids = async (
  layers: NormalOption[],
  index: number,
  sql: string
) => {
  try {
    const tab = layers[index];
    const res = await excuteQueryForIds(
      window.SITE_CONFIG.GIS_CONFIG.gisService +
        window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService +
        '/' +
        tab.id,
      initQueryParams({
        returnGeometry: false,
        where: sql || '',
        geometry: staticState.queryGeometry,
        orderByFields: ['OBJECTID asc']
      })
    );
    if (res !== null) {
      state.tabs.push({ label: tab.label, name: tab.label, data: res });
    }
    if (index < layers.length - 1) {
      await getTabOids(layers, ++index, sql);
    }
  } catch (error) {
    console.log('发生错误，获取详情ids失败');
    throw new Error('发生错误，获取详情ids失败');
  }
};
const handleDelete = () => {
  refForm.value?.dataForm && (refForm.value.dataForm.sql = '');
};
const clear = () => {
  state.curOperate = '';
  setMapCursor('');
  staticState.drawAction?.destroy();
  staticState.drawer?.destroy();
  staticState.drawAction = undefined;
  staticState.drawer = undefined;
  staticState.graphicsLayer &&
    props.view?.map.remove(staticState.graphicsLayer);
};
const clearRange = () => {
  staticState.graphicsLayer?.removeAll();
  staticState.drawAction?.destroy();
  staticState.drawer?.destroy();
};
onMounted(() => {
  initBaseLayer();
});
onBeforeUnmount(() => {
  clear();
});
</script>
<style lang="scss" scoped>
:deep(.el-table__empty-block) {
  min-height: 40px;
  .el-table__empty-text {
    line-height: 40px;
  }
}
.chart-box {
  width: 100%;
  height: calc(100% - 52px);
}
</style>
<style>
.sql-btns-wrapper,
.sql-list-wrapper {
  box-shadow: 0 0 0 1px var(--el-border-color);
}
.gis-length-statistics-panel {
  height: 400px;
  width: 600px;
  position: absolute;
  left: calc(calc(50% - 300px));
  top: 150px;
  overflow: hidden;
}
</style>
