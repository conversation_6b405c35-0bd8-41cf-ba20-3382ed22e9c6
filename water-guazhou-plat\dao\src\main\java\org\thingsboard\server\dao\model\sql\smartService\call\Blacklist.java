package org.thingsboard.server.dao.model.sql.smartService.call;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 号码归属地
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2021-11-01
 */
@TableName("tb_service_call_blacklist")
@Data
public class Blacklist {

    @TableId
    private String id;

    private String phone;

    private String remark;

    private Date createTime;

    private Date updateTime;

    private String tenantId;

}
