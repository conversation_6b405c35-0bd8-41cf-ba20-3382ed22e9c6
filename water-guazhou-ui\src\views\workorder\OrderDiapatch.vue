<!-- 统一工单-工单中心-工单分派 -->
<template>
  <div class="wrapper">
    <CardSearch ref="refSearch" :config="SearchConfig"></CardSearch>
    <CardTable ref="refTable" :config="TableConfig" class="card-table"></CardTable>
    <SLDrawer ref="refFormDiapatch" :config="DialogFormConfigDiapatch"></SLDrawer>
    <SLDrawer ref="refFormTransform" :config="DialogFormConfigTransform"></SLDrawer>
    <SLDrawer ref="refFormReport" :config="DialogFormConfigReport"></SLDrawer>
    <SLDrawer ref="refFormApply" :config="DialogFormConfigApply"></SLDrawer>
    <SLDrawer ref="refFormInvalid" :config="DialogFormConfigInvalid"></SLDrawer>
    <SLDrawer ref="refdetail" :config="detailConfig">
      <detail :id="selectedId"></detail>
    </SLDrawer>
  </div>
</template>
<script lang="ts" setup>
import { dayjs } from 'element-plus'
import { Download, Refresh, Search } from '@element-plus/icons-vue'
import { onMounted, reactive, ref, shallowRef } from 'vue'
// import { useRouter } from 'vue-router'
import { formatDate } from '@/utils/DateFormatter'
import { SLConfirm, SLMessage } from '@/utils/Message'
import OrderStepTagsVue from './components/OrderStepTags.vue'
import detail from './components/detail.vue'
import {
  getFromOptions,
  // getOrderList,
  getOrderTypeOptions,
  WorkOrderDealLevel
} from './config'
import { DispatchWorkOrder, GetWorkOrderPage, getWorkOrderEmergencyLevelList, getWorkOrderProcessLevelList } from '@/api/workorder'
import useUser from '@/hooks/user/useUser'
import { formatterDate, traverse } from '@/utils/GlobalHelper'

// const router = useRouter()
const { getUserOptions } = useUser()
const refFormTransform = ref<ISLDrawerIns>()
const refFormDiapatch = ref<ISLDrawerIns>()
const refFormReport = ref<ISLDrawerIns>()
const refFormApply = ref<ISLDrawerIns>()
const refFormInvalid = ref<ISLDrawerIns>()
const refSearch = ref<ICardSearchIns>()
const refTable = ref<ICardTableIns>()
const refdetail = ref<ISLDrawerIns>()

const state = reactive<{
  WorkOrderEmergencyLevelList: any[],
  WorkOrderProcessLevelList: any[]
}>({
  WorkOrderEmergencyLevelList: [],
  WorkOrderProcessLevelList: []
})

function initOptions() {
  // 紧急程度
  getWorkOrderEmergencyLevelList('1').then(res => {
    state.WorkOrderEmergencyLevelList = traverse(res.data.data || [], 'children', { label: 'name', value: 'id' })
  })
  // 处理级别
  getWorkOrderProcessLevelList('1').then(res => {
    state.WorkOrderProcessLevelList = traverse(res.data.data || [], 'children', { label: 'name', value: 'name' })
  })
}

// 明细弹框
const detailConfig = reactive<IDrawerConfig>({
  title: '流程明细',
  cancel: false,
  className: 'lightColor',
  group: []
})
const selectedId = ref<string>('')

const SearchConfig = reactive<ISearch>({
  filters: [
    { type: 'daterange', label: '发起时间', field: 'date' },
    { type: 'input', label: '标题', field: 'title' },
    { type: 'select', label: '来源', field: 'source', options: getFromOptions() },
    {
      type: 'select-tree',
      label: '类型',
      field: 'type',
      options: getOrderTypeOptions()
    }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          svgIcon: shallowRef(Search),
          click: () => refreshData()
        },
        {
          perm: true,
          text: '重置',
          type: 'default',
          svgIcon: shallowRef(Refresh),
          click: () => {
            // SearchConfig.defaultParams = {}
            refSearch.value?.resetForm()
            refreshData()
          }
        },
        {
          perm: true,
          text: '导出',
          type: 'warning',
          svgIcon: shallowRef(Download),
          click: () => {
            refTable.value?.exportTable()
          }
        }
      ]
    }
  ],
  defaultParams: {
    date: [dayjs().subtract(1, 'M').format(formatterDate), dayjs().format(formatterDate)]
  },
  handleSearch: () => refreshData()
})
const TableConfig = reactive<ICardTable>({
  expandable: true,
  expandComponent: shallowRef(OrderStepTagsVue),
  defaultExpandAll: true,
  columns: [
    { label: '工单编号', prop: 'serialNo' },
    { label: '来源', prop: 'source' },
    { label: '类型', prop: 'type' },
    { label: '标题', prop: 'title' },
    { label: '地址', prop: 'address' },
    { label: '创建人', prop: 'organizerName' },
    {
      label: '发起时间',
      prop: 'createTime',
      formatter: row => formatDate(row.createTime)
    },
    {
      minWidth: 120,
      prop: 'level',
      label: '紧急程度',
      tag: true,
      tagColor: (row): string => state.WorkOrderEmergencyLevelList.find(item => item.value === row.level)?.color || '',
      formatter: row => state.WorkOrderEmergencyLevelList.find(item => item.value === row.level)?.label
    }
  ],
  dataList: [],
  pagination: {
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page
      TableConfig.pagination.limit = size
      refreshData()
    }
  },
  operationWidth: 140,
  operations: [
    {
      perm: true,
      isTextBtn: true,
      text: '详情',
      click: row => handleDetail(row)
    },
    {
      perm: true,
      isTextBtn: true,
      text: '分派',
      click: row => {
        TableConfig.currentRow = row
        DialogFormConfigDiapatch.defaultValue = {}
        refFormDiapatch.value?.openDrawer()
      }
    }
    // {
    //   perm: true,
    //   isTextBtn: true,
    //   text: '无效',
    //   click: row => {
    //     TableConfig.currentRow = row
    //     refFormInvalid.value?.openDrawer()
    //   }
    // }
    // {
    //   perm: true,
    //   isTextBtn: true,
    //   text: '转发',
    //   click: row => {
    //     TableConfig.currentRow = row
    //     refFormTransform.value?.openDrawer()
    //   }
    // },
    // {
    //   perm: true,
    //   isTextBtn: true,
    //   text: '上报',
    //   click: row => {
    //     TableConfig.currentRow = row
    //     refFormReport.value?.openDrawer()
    //   }
    // },
    // {
    //   perm: true,
    //   isTextBtn: true,
    //   text: '批示',
    //   click: row => {
    //     TableConfig.currentRow = row
    //     refFormApply.value?.openDrawer()
    //   }
    // }
  ]
})
const DialogFormConfigTransform = reactive<IDrawerConfig>({
  title: '转发',
  width: 500,
  labelPosition: 'top',
  group: [
    {
      fields: [
        {
          type: 'select',
          label: '转发至：',
          field: 'to',
          options: []
        },
        { type: 'textarea', label: '备注：', field: 'remark' }
      ]
    }
  ],
  submit: (params: any) => {
    SLConfirm('确定转发？', '提示信息')
      .then(() => {
        console.log(params)
        refFormTransform.value?.closeDrawer()
      })
      .catch(() => {
        //
      })
  }
})
const DialogFormConfigDiapatch = reactive<IDrawerConfig>({
  title: '分派',
  width: 500,
  labelPosition: 'top',
  group: [
    {
      fields: [
        {
          type: 'select',
          label: '分派至：',
          field: 'stepProcessUserId',
          rules: [{ required: true, message: '请选择用户' }],
          options: []
        },
        {
          field: 'processLevelLabel',
          label: '处理级别：',
          type: 'select',
          rules: [{ required: true, message: '请选择处理级别' }],
          options: WorkOrderDealLevel(),
          onChange: val => {
            const key = state.WorkOrderProcessLevelList.find(item => item.value === val)
            if (refFormDiapatch?.value?.refForm?.dataForm) {
              refFormDiapatch.value.refForm.dataForm = {
                ...refFormDiapatch.value.refForm.dataForm,
                processLevel: key.dayTime * 1440 + key.hourTime * 60 + key.minuteTime
              }
            }
          }
        }
      ]
    }
  ],
  submit: (params: any) => {
    SLConfirm('确定分派？', '提示信息')
      .then(async () => {
        DialogFormConfigDiapatch.submitting = true
        try {
          const res = await DispatchWorkOrder(TableConfig.currentRow.id, params)
          if (res.data?.code === 200) {
            SLMessage.success('操作成功')
            refreshData()
          } else {
            SLMessage.error(res?.data?.err || '操作失败')
          }
        } catch (error) {
          SLMessage.error('系统错误')
        }
        DialogFormConfigDiapatch.submitting = false
        refFormDiapatch.value?.closeDrawer()
      })
      .catch(() => {
        //
      })
  }
})
const DialogFormConfigInvalid = reactive<IDrawerConfig>({
  title: '无效',
  width: 500,
  labelPosition: 'top',
  group: [
    {
      fields: [
        {
          type: 'textarea',
          label: '原因：',
          field: 'remark'
        }
      ]
    }
  ],
  submit: (params: any) => {
    SLConfirm('确定提交？', '提示信息')
      .then(() => {
        console.log(params)
        refFormReport.value?.closeDrawer()
      })
      .catch(() => {
        //
      })
  }
})
const DialogFormConfigReport = reactive<IDrawerConfig>({
  title: '上报',
  width: 500,
  labelPosition: 'top',
  group: [
    {
      fields: [
        {
          type: 'select',
          label: '上报至：',
          field: 'to',
          options: []
        },
        { type: 'textarea', label: '备注：', field: 'remark' }
      ]
    }
  ],
  submit: (params: any) => {
    SLConfirm('确定上报？', '提示信息')
      .then(() => {
        console.log(params)
        refFormReport.value?.closeDrawer()
      })
      .catch(() => {
        //
      })
  }
})
const DialogFormConfigApply = reactive<IDrawerConfig>({
  title: '批示',
  width: 500,
  group: [
    {
      fields: [
        { type: 'textarea', label: '备注', field: 'remark' },
        {
          type: 'select',
          label: '发送至',
          field: 'to',
          options: []
        }
      ]
    }
  ],
  submit: (params: any) => {
    SLConfirm('确定发送？', '提示信息')
      .then(() => {
        console.log(params)
        refFormApply.value?.closeDrawer()
      })
      .catch(() => {
        //
      })
  }
})
const handleDetail = (row: any) => {
  selectedId.value = row.id || ''
  detailConfig.title = row.serialNo
  refdetail.value?.openDrawer()
  // router.push({
  //   name: 'WorkOrderDetail',
  //   query: {
  //     id: row.id
  //   }
  // })
}
const refreshData = async () => {
  TableConfig.loading = true
  try {
    const query = refSearch.value?.queryParams || {}
    const [fromTime, toTime] = query.date?.length === 2 ? [dayjs(query.date[0], formatterDate).format('YYYY-MM-DD'), dayjs(query.date[1], formatterDate).endOf('D').format('YYYY-MM-DD')] : [dayjs().subtract(1, 'M').startOf('D').format('YYYY-MM-DD'), dayjs().endOf('D').format('YYYY-MM-DD')]
    const params: any = {
      page: TableConfig.pagination.page || 1,
      size: TableConfig.pagination.limit || 20,
      ...query,
      fromTime,
      toTime,
      status: 'PENDING'
    }
    delete params.date
    const res = await GetWorkOrderPage(params)
    const data = res.data?.data
    TableConfig.dataList = data.data
    TableConfig.pagination.total = data.total
  } catch (error) {
    //
  }
  TableConfig.loading = false
}
const initUserOption = async () => {
  const options = await getUserOptions(false, { authority: 'CUSTOMER_USER' })
  const field = DialogFormConfigDiapatch.group[0].fields[0] as IFormSelect
  const transUser = DialogFormConfigTransform.group[0].fields[0] as IFormSelect
  const applyUser = DialogFormConfigApply.group[0].fields[1] as IFormSelect
  const reportUser = DialogFormConfigReport.group[0].fields[0] as IFormSelect
  reportUser.options = options
  applyUser.options = options
  transUser.options = options
  field.options = options
}
onMounted(() => {
  initOptions()
  initUserOption()
  refreshData()
})
</script>
<style lang="scss" scoped></style>
