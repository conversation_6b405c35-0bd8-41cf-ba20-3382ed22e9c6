package org.thingsboard.server.controller.smartPipe;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.DTO.PartitionNotNormalLossEvaluateDTO;
import org.thingsboard.server.dao.model.DTO.PartitionTotalDifferenceDTO;
import org.thingsboard.server.dao.smartPipe.PartitionLossEvaluateService;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;
import org.thingsboard.server.utils.ExcelUtil;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 智慧管网-漏失评估
 */
@RestController
@RequestMapping("api/spp/dma/partitionEvaluate")
public class PartitionLossEvaluateController extends BaseController {
    @Autowired
    private PartitionLossEvaluateService partitionLossEvaluateService;

    @GetMapping("normalList")
    public IstarResponse getNormalList(String date, @RequestParam(required = false, defaultValue = "") String name, String partitionId) throws ThingsboardException {
        if (StringUtils.isBlank(date)) {
            return IstarResponse.error("请输入查询时间");
        }

        if (StringUtils.isBlank(partitionId)) {
            return IstarResponse.error("请选择分区");
        }
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());

        return IstarResponse.ok(partitionLossEvaluateService.getNormalList(date, name, partitionId, tenantId));
    }

    @GetMapping("notNormalList")
    public IstarResponse getNotNormalList(String date, @RequestParam(required = false, defaultValue = "") String name, String partitionId) throws ThingsboardException {
        if (StringUtils.isBlank(date)) {
            return IstarResponse.error("请输入查询时间");
        }

        if (StringUtils.isBlank(partitionId)) {
            return IstarResponse.error("请选择分区");
        }
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());

        return IstarResponse.ok(partitionLossEvaluateService.getNotNormalList(date, name, partitionId, tenantId, ""));
    }

    @GetMapping("normalListForApp")
    public IstarResponse getNotNormalListForApp() throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());

        // 漏损评估
        List<PartitionNotNormalLossEvaluateDTO> normalList = partitionLossEvaluateService.getNotNormalList(LocalDate.now().minusDays(1L).format(DateTimeFormatter.ofPattern("yyyy-MM-dd")), "", "", tenantId, "all");
        JSONObject lossEvaluate = new JSONObject();
        lossEvaluate.put("较好", 0);
        lossEvaluate.put("一般", 0);
        lossEvaluate.put("较差", 0);
        normalList.forEach(a -> {
            if (lossEvaluate.get(a.getLossValuation()) != null) {
                lossEvaluate.put(a.getLossValuation(), lossEvaluate.getInteger(a.getLossValuation()) + 1);
            } else {
                lossEvaluate.put("一般", lossEvaluate.getInteger("一般") + 1);
            }
        });

        JSONObject result = new JSONObject();
        result.put("lossEvaluate", lossEvaluate);

        return IstarResponse.ok(result);
    }

    @GetMapping("normalListDetail")
    public IstarResponse getNotNormalList(String date, String type, String start, String end, String partitionId) throws ThingsboardException {
        if (StringUtils.isBlank(date) && StringUtils.isBlank(start)) {
            return IstarResponse.error("请输入查询时间");
        }

        if (StringUtils.isBlank(type)) {
            return IstarResponse.error("请选择时间类型");
        }

        if (StringUtils.isBlank(partitionId)) {
            return IstarResponse.error("请选择分区");
        }
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());

        return IstarResponse.ok(partitionLossEvaluateService.getNormalListDetail(date, type, start, end, partitionId, tenantId));
    }

    @GetMapping("totalDifference")
    public IstarResponse getTotalDifference(String partitionId, String type, String date, String start, String end) throws ThingsboardException {
        if (StringUtils.isBlank(date) && StringUtils.isBlank(start)) {
            return IstarResponse.error("请输入查询时间");
        }

        if (StringUtils.isBlank(partitionId)) {
            return IstarResponse.error("请选择分区");
        }

        if (StringUtils.isBlank(type)) {
            return IstarResponse.error("请选择查询类型");
        }
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());

        return IstarResponse.ok(partitionLossEvaluateService.getTotalDifference(partitionId, type, date, start, end));
    }

    @GetMapping("totalDifferenceExport")
    public IstarResponse getTotalDifferenceExport(String partitionId, String type, String date, String start, String end, HttpServletResponse response) throws ThingsboardException, IOException {
        if (StringUtils.isBlank(date) && StringUtils.isBlank(start)) {
            return IstarResponse.error("请输入查询时间");
        }

        if (StringUtils.isBlank(partitionId)) {
            return IstarResponse.error("请选择分区");
        }

        if (StringUtils.isBlank(type)) {
            return IstarResponse.error("请选择查询类型");
        }
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        List<PartitionTotalDifferenceDTO> totalDifferenceList = partitionLossEvaluateService.getTotalDifference(partitionId, type, date, start, end);


        JSONArray jsonArray = JSONObject.parseArray(JSONObject.toJSONString(totalDifferenceList));

        ExcelUtil.totalDifferenceExport(jsonArray, "yyyyMMddHHmmss", 20, response);

        return IstarResponse.ok();
    }

    @GetMapping("dayFlowAnalysis")
    public IstarResponse getDayFlowAnalysis(String partitionId, String date) throws ThingsboardException {
        if (StringUtils.isBlank(partitionId)) {
            return IstarResponse.error("请选择分区");
        }
        if (StringUtils.isBlank(date)) {
            return IstarResponse.error("请选择日期");
        }
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return IstarResponse.ok(partitionLossEvaluateService.getDayFlowAnalysis(partitionId, date, tenantId));
    }

}
