<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.smartService.duty.DutyTeamMMapper">
    <resultMap id="dutyTeamMap" type="org.thingsboard.server.dao.model.sql.smartService.duty.DutyTeamM">
        <result column="id" property="id"/>
        <result column="code" property="code"/>
        <result column="name" property="name"/>
        <result column="persons" property="persons"/>
        <result column="remark" property="remark"/>
        <result column="creator" property="creator"/>
        <result column="creatorName" property="creatorName"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="tenant_id" property="tenantId"/>
        <collection property="dutyTeamCList" ofType="org.thingsboard.server.dao.model.sql.smartService.duty.DutyTeamC">
            <result column="id1" property="id"/>
            <result column="main_id" property="mainId"/>
            <result column="person" property="person"/>
            <result column="tenant_id1" property="tenantId"/>
        </collection>
    </resultMap>
    <select id="getList" resultMap="dutyTeamMap">
        select a.*, b.id id1, b.main_id, b.person, b.tenant_id as tenant_id1,d.persons, e.first_name as creatorName
        from tb_service_duty_team_m a
        left join tb_service_duty_team_c b on a. id = b.main_id
        left join tb_user c on b.person = c.id
        left join (select b.main_id, string_agg(first_name||'', ',') as persons from tb_user a LEFT JOIN tb_service_duty_team_c b on a.id = person group by b.main_id) d on a.id = d.main_id
        left join tb_user e on a.creator = e.id
        where (a.code like '%'||#{keywords}||'%' or a.name like '%'||#{keywords}||'%')
        and a.tenant_id like #{tenantId}
        order by a.create_time desc
    </select>

</mapper>