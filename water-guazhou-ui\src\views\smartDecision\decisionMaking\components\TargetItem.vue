<template>
  <div class="item">
    <div
      v-if="config.rows?.indexOf(1) !== -1"
      class="row"
    >
      <span class="title">{{ props.config.label }}</span>
    </div>
    <div
      v-if="config.rows?.indexOf(2) !== -1"
      class="row"
    >
      <span class="value blue">{{ props.config.value }} </span>
      <span class="unit">{{ props.config.unit }}</span>
    </div>
    <div
      v-if="config.rows?.indexOf(3) !== -1"
      class="row"
    >
      <span class="text">{{ props.config.text }} </span>
      <Icon
        v-if="config.status"
        :icon="
          config.status === 'up'
            ? 'material-symbols:arrow-drop-up'
            : 'material-symbols:arrow-drop-down'
        "
        :class="config.status"
      ></Icon>
      <span
        class="value blue"
        :class="config.status"
      >
        {{ props.config.scale }}
      </span>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { Icon } from '@iconify/vue'

const props = defineProps<{ config: ITargetItem }>()
</script>

<style lang="scss" scoped>
.item {
  width: 180px;
  display: flex;
  flex-direction: column;
  align-items: center;
  line-height: 24px;
  .row {
    display: flex;
    align-items: center;
  }
  .title {
    font-size: 14px;
  }
  .text {
    font-size: 12px;
    margin-right: 8px;
  }
  .value {
    font-size: 18px;
  }
  .unit {
    font-size: 12px;
  }
  // :nth-child(1) {
  //   font-size: 14px;
  // }

  // :nth-child(2) {
  //   font-size: 20px;
  //   color: #2b70e6;

  //   font {
  //     color: #fff;
  //   }
  // }
}
.blue {
  color: #2b70e6;
}
.up {
  color: red;
}

.down {
  color: green;
}

.white {
  color: white;
}
</style>
