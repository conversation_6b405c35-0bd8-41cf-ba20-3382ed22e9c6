<template>
  <template v-if="!props.item?.hidden && !props.item?.meta?.hidden && hasAuthority">
    <!-- 有子级 -->
    <template v-if="props.item.children?.length">
      <!-- 只有一个子级并且隐藏层级 -->
      <template v-if="props.item.children.length === 1 && !props.item.alwaysShow">
        <sidebar-item
          :is-nest="true"
          :item="props.item.children[0]"
          :icon="props.item.children[0].meta?.icon || props.icon"
        ></sidebar-item>
      </template>
      <!-- 多个子级 -->
      <template v-else>
        <el-sub-menu :index="props.item.path">
          <template
            v-if="props.item.meta?.title"
            #title
          >
            <span :class="'title-text' + ' ' + props.item.meta.icon">{{ props.item.meta.title }}
            </span>
          </template>
          <template v-for="(child, k) in props.item.children">
            <template v-if="!child.hidden && !child?.meta?.hidden">
              <sidebar-item
                :key="k"
                :is-nest="true"
                :item="child"
                :icon="child.meta?.icon || props.icon"
              >
              </sidebar-item>
            </template>
          </template>
        </el-sub-menu>
      </template>
    </template>
    <!-- 无子级 -->
    <template v-else>
      <el-menu-item
        :index="props.item.path"
        :class="{ 'nest-menu': props.isNest }"
        :route="item.component ? item : { name: 'NotFound' }"
      >
        <template
          v-if="props.item?.meta?.title"
          #title
        >
          <span :class="'title-text' + ' ' + props.icon">{{ props.item.meta.title }}</span>
        </template>
      </el-menu-item>
    </template>
  </template>
</template>

<script lang="ts" setup>
import { hasPermission } from '@/utils/RouterHelper'

// import path from 'path'
const props = defineProps<{
  item: any
  isNest: boolean
  // path: string
  icon?: string
}>()
// const resolvePath = (cPath?: string) => {
//   if (props.path && cPath?.startsWith('/')) {
//     cPath = cPath
//       .split('/')
//       .filter(item => !!item)
//       .join('/')
//   }
//   return path.resolve(props.path, cPath || '')
// }
const hasAuthority = computed(() => {
  return hasPermission(props.item?.meta?.roles)
})
</script>

<style lang="scss">
.title-text {
  &.iconfont::before {
    margin-right: 4px !important;
  }
}
</style>
