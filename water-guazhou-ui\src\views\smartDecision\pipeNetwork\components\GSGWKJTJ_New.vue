<!-- 供水管网口径统计 -->
<template>
  <div class="chart">
    <VChart
      ref="refChart"
      :option="state.cxcOption"
    ></VChart>
  </div>
</template>
<script lang="ts" setup>
import { graphic } from 'echarts'
import { useDetector } from '@/hooks/echarts'
import { staticPipe } from '@/utils/MapHelper'
import { useGisStore } from '@/store'
import { hexToRgba } from '@/utils/GlobalHelper'

const state = reactive<{
  cxcOption: any
}>({
  cxcOption: {}
})
const initChartOption = (xData, yData) => {
  return {
    tooltip: {
      trigger: 'axis',
      formatter(params) {
        let relVal = params[0].name
        for (let i = 0, l = params.length; i < l; i++) {
          relVal += '<br/>' + params[i].marker + params[i].value + ' 个'
        }
        return relVal
      }
    },
    grid: {
      top: 40,
      left: 20,
      right: 20,
      bottom: 20,
      containLabel: true
    },
    legend: {
      show: false
    },
    yAxis: {
      name: '单位(个)',
      nameTextStyle: {
        color: '#B8D2FF'
      },
      type: 'value',
      splitLine: {
        lineStyle: {
          type: 'dashed',
          color: hexToRgba('#B8D2FF', 0.1)
        }
      },
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        textStyle: {
          color: '#B8D2FF',
          fontSize: 12
        }
      },
      position: 'top'
    },
    xAxis: {
      type: 'category',
      boundaryGap: true,
      data: xData,
      splitLine: {
        show: false
      },
      axisLine: {
        lineStyle: {
          color: '#548BD2'
        }
      },
      axisLabel: {
        textStyle: {
          color: '#B8D2FF',
          fontSize: 12
        },
        rotate: 45
      },
      axisTick: {
        show: false
      }
    },
    series: [
      {
        name: '管线',
        type: 'bar',
        barWidth: 10,
        label: {
          show: false
        },
        emphasis: {
          focus: 'series'
        },
        itemStyle: {
          color: new graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: 'rgba(93, 176, 252,1)'
            },
            {
              offset: 1,
              color: 'rgba(93, 176, 252,0.3)'
            }
          ])
        },
        data: yData
      }
    ]
  }
}

const refreshChart = async () => {
  const xData: any[] = []
  const yData: any[] = []
  try {
    const countRes = await staticPipe('count', {
      layerIds: useGisStore().gLayerInfos
        ?.filter(item => item.geometrytype === 'esriGeometryPolyline')
        .map(item => item.layerid),
      group_fields: ['DIAMETER']
    })
    countRes?.[0]?.rows?.map(item => {
      xData.push('DN ' + (item.DIAMETER ?? '--') + 'mm')
      yData.push(item.OBJECTID)
    })
  } catch (error) {
    console.log('管网口径统计失败')
  }
  state.cxcOption = initChartOption(xData, yData)
}
const refChart = ref()
const detector = useDetector()
onMounted(() => {
  refreshChart()
  detector.listenToMush(document.documentElement, () => {
    refChart.value?.resize()
  })
})
defineExpose({
  refreshChart
})
</script>
<style lang="scss" scoped>
.chart {
  width: 100%;
  height: 100%;
}
</style>
