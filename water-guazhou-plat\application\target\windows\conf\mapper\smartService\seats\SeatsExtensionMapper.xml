<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.smartService.seats.SeatsExtensionMapper">
    <select id="getList" resultType="org.thingsboard.server.dao.model.sql.smartService.seats.SeatsExtension">
        select a.*, b.name as departmentName
        from tb_service_seats_extension a
        left join tb_department b on a.department_id = b.id
    </select>

    <select id="getNotBindList" resultType="org.thingsboard.server.dao.model.sql.smartService.seats.SeatsExtension">
        select a.*
        from tb_service_seats_extension a
        where a.id not in (
            select extension_id from tb_service_seats_user
            )
    </select>
</mapper>