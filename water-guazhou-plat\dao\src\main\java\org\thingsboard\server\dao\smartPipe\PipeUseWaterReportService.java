package org.thingsboard.server.dao.smartPipe;

import org.thingsboard.server.dao.model.DTO.PipeUseWaterReportDTO;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.PipeUseWaterReport;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.List;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2023-04-25
 */
public interface PipeUseWaterReportService {

    IstarResponse save(PipeUseWaterReport pipeUseWaterReport);

    PipeUseWaterReportDTO getWaterBalance(String partitionId, String ym);

    List<PipeUseWaterReport> getYearDetail(String partitionId, String year);
}
