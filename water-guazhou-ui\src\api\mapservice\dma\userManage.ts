import { request } from '@/plugins/axios';
import { SLMessage } from '@/utils/Message';
import { saveAs } from '@/utils/printUtils';

/**
 * 获取导入DMA营收用户模板
 * @param params
 * @returns
 */
export const GetImportDMAUserTemplate = () => {
  return request({
    url: `/api/spp/partitionCust/template`,
    method: 'get',
    responseType: 'blob'
  });
};
/**
 * 将模板保存到本地
 */
export const DownloadDMAUserTemplate = async () => {
  try {
    SLMessage.info('正在下载模板...');
    const res = await GetImportDMAUserTemplate();
    saveAs(res.data, '分区用户挂接模板');
  } catch (error) {
    SLMessage.error('下载模板失败');
  }
};
/**
 * 导入DMA营收用户
 * @param params
 * @returns
 */
export const ImportDMAUsers = (file?: any) => {
  return request({
    url: `/api/spp/partitionCust/importSave`,
    method: 'post',
    data: file
  });
};

type IQueryDMAUserParams = IQueryPagerParams & {
  custName?: string;
  custCode?: string;
  waterCategory?: string;
  copyMeterUser?: string;
  businessHall?: string;
  partitionId?: string;
  isMount?: string;
  address?: string;
};
/**
 * 查询DMA营收用户列表
 * @param params
 * @returns
 */
export const GetDMAUsers = (params: IQueryDMAUserParams) => {
  return request({
    url: '/api/spp/partitionCust/list',
    method: 'get',
    params
  });
};
/**
 * 导出DMA营收用户
 * @param params
 * @returns
 */
export const ExportDMAUsers = (params?: IQueryDMAUserParams) => {
  return request({
    url: `/api/spp/partitionCust/export`,
    method: 'get',
    params,
    responseType: 'blob'
  });
};
/**
 * 添加DMA营收用户
 * @param params
 * @returns
 */
export const PostDMAUser = (params: {
  partitionId: string;
  custCode: string;
  copyMeterUser: string;
  businessHall: string;
}) => {
  return request({
    url: '/api/spp/partitionCust',
    method: 'post',
    data: params
  });
};

/**
 * 删除DMA 营收用户
 * @param ids
 * @returns
 */
export const DeleteDMAUsers = (ids: string[]) => {
  return request({
    url: '/api/spp/partitionCust',
    method: 'delete',
    data: ids
  });
};

/**
 * 批量取消挂接营收用户
 * @param ids
 * @returns
 */
export const MultiHookDownDMAUser = (ids: string[]) => {
  return request({
    url: '/api/spp/partitionCust/batchRemove',
    method: 'post',
    data: ids
  });
};

/**
 * 挂接营收用户
 * @param params
 * @returns
 */
export const MultiHookUpDMAUser = (
  params: {
    partitionId: string;
    id: string;
  }[]
) => {
  return request({
    url: '/api/spp/partitionCust/batchSave',
    method: 'post',
    data: params
  });
};

// 查询锦州DMA小区表册列表
export const GetDMAUserList = () => {
  return request({
    url: '/api/jinzhou/dmaBook/getBookList',
    method: 'get'
  });
};

// 获取当前分区挂接的营收表册
export const GetDMAUserPartitionList = (id: string) => {
  return request({
    url: `/api/jinzhou/dmaBook/${id}`,
    method: 'get'
  });
};

// 保存DMA小区表册列表
export const postDMAUserList = (params: { id: string; bookId: string }) => {
  return request({
    url: '/api/jinzhou/dmaBook/save',
    method: 'post',
    data: params
  });
};
