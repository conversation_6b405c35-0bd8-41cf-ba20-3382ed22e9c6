<template>
  <RightDrawerMap
    ref="refMap"
    :title="'爆管分析'"
    :full-content="true"
    @map-loaded="onMapLoaded"
    @detail-refreshed="state.viewingDetail = false"
    @detail-refreshing="state.viewingDetail = true"
  >
    <Form
      ref="refForm"
      :config="FormConfig"
    ></Form>
    <Panel
      ref="refPanel_User"
      custom-class="gis-detail-panel"
      :draggable="false"
      :max-min="true"
      title="受影响用户详情"
    >
      <div class="table-box">
        <FormTable :config="userInfo.TableConfig_User.value"></FormTable>
      </div>
    </Panel>
  </RightDrawerMap>
</template>
<script lang="ts" setup>
import { setMapCursor } from '@/utils/MapHelper'
import { IFormIns, IPanelIns } from '@/components/type'
import RightDrawerMap from '../../components/common/RightDrawerMap.vue'
import { useIdentify, useAnalys, useUserInfo } from './hooks/BurstAnalys'

const refForm = ref<IFormIns>()
const refPanel_User = ref<IPanelIns>()
const refMap = ref<InstanceType<typeof RightDrawerMap>>()

const identify = useIdentify()
const analys = useAnalys()
const userInfo = useUserInfo()
const state = reactive<{
  detailUrl: string
  viewingDetail: boolean
  curOperate: 'userDetailing' | ''
}>({
  viewingDetail: false,
  detailUrl: '',
  curOperate: ''
})
const staticState: {
  view?: __esri.MapView
  deviceids: any[]
  devicelayers: string[]
} = {
  deviceids: [],
  devicelayers: ['阀门', '计量装置']
}
const TableConfig_Detail = reactive<ITable>({
  dataList: [],
  columns: [],
  pagination: {
    hide: true,
    refreshData: ({ page, size }) => {
      TableConfig_Detail.pagination.page = page
      TableConfig_Detail.pagination.limit = size
      handleDetail()
    }
  }
})
const FormConfig = reactive<IFormConfig>({
  gutter: 12,
  labelPosition: 'top',
  group: [
    {
      fieldset: {
        desc: '选取管线'
      },
      fields: [
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              styles: {
                width: '100%'
              },
              type: 'warning',
              text: () => {
                return identify.isPicking.value === true
                  ? '正在选取管线'
                  : '点击选取管线'
              },
              loading: () => identify.isPicking.value === true,
              click: () => identify.init(staticState.view)
            }
          ]
        },
        {
          type: 'table',
          style: {
            height: '80px'
          },
          config: identify.TableConfig.value
        }
      ]
    },
    {
      fieldset: {
        desc: '执行分析'
      },
      fields: [
        {
          type: 'btn-group',
          itemContainerStyle: {
            marginBottom: 0
          },
          btns: [
            {
              perm: true,
              styles: {
                width: '100%'
              },
              loading: () => analys.isAnalys.value === true,
              text: () => (analys.isAnalys.value === true ? '正在分析' : '开始分析'),
              disabled: () => !identify.TableConfig.value.dataList.length,
              click: () => analys.init(
                staticState.view,
                false,
                identify.TableConfig.value.dataList[0].layerId,
                identify.staticState.identifyResult?.feature.attributes[
                  'OBJECTID'
                ]
              )
            }
          ]
        }
      ]
    },
    {
      fieldset: {
        desc: '分析结果'
      },
      fields: [
        {
          label: '影响范围概览',
          type: 'checkbox',
          field: 'showInMap',
          options: [{ label: '地图显示', value: 'show' }],
          onChange: val => {
            analys.staticState.resultLayer
              && (analys.staticState.resultLayer.visible = !!val.length)
          }
        },
        {
          type: 'attr-table',
          style: {
            minHeight: '50px'
          },
          config: analys.TableConfig_EffectRange.value
        },
        {
          type: 'table',
          label: '必关阀',
          style: {
            height: '250px'
          },
          config: analys.TableConfig_MustShut.value
        },
        {
          type: 'btn-group',
          itemContainerStyle: {
            marginBottom: '5px',
            marginTop: '15px'
          },
          btns: [
            {
              perm: true,
              styles: {
                width: '100%'
              },
              disabled: () => !analys.TableConfig_EffectRange.value.columns?.length,
              loading: () => state.viewingDetail === true,
              text: () => (state.viewingDetail === true ? '正在查询...' : '查看详细结果'),
              click: () => handleDetail()
            }
          ]
        },
        {
          type: 'btn-group',
          itemContainerStyle: {
            marginBottom: '5px'
          },
          btns: [
            {
              perm: false,
              styles: {
                width: '100%'
              },
              text: () => (state.curOperate === 'userDetailing'
                ? '正在查询'
                : '查看受影响用户'),
              loading: () => state.curOperate === 'userDetailing',
              disabled: () => !analys.TableConfig_EffectRange.value.columns?.length,
              click: () => userInfo.init(staticState.view, analys.tabs.value, () => {
                refPanel_User.value?.Open()
              })
            }
          ]
        },
        {
          type: 'btn-group',
          itemContainerStyle: {
            marginBottom: '5px'
          },
          btns: [
            {
              perm: true,
              styles: { width: '100%' },
              loading: () => analys.isExtendAnalys.value,
              disabled: () => !analys.TableConfig_MustShut.value.selectList?.length,
              text: () => (analys.isExtendAnalys.value === true
                ? '正在分析'
                : '二次关阀分析'),
              click: () => analys.init(
                staticState.view,
                true,
                undefined,
                analys.TableConfig_MustShut.value.selectList?.map(
                  item => item.OBJECTID
                ) || []
              )
            }
          ]
        },
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              styles: {
                width: '100%'
              },
              type: 'danger',
              disabled: () => [
                'picking',
                'analysing',
                'extendAnalysing',
                'detailing',
                'userDetailing'
              ].indexOf(state.curOperate) !== -1,
              text: '清除所有',
              click: () => clear()
            }
          ]
        }
      ]
    }
  ],
  defaultValue: {
    showInMap: ['show']
  }
})
const handleDetail = async () => {
  refMap.value?.refreshDetail(analys.tabs.value)
}

const clear = () => {
  setMapCursor('')
  analys.destroy()
  identify.destroy()
  userInfo.destroy()
}
const destroy = () => {
  clear()
  staticState.view?.destroy()
  staticState.view = undefined
}
const onMapLoaded = view => {
  staticState.view = view
}
onBeforeUnmount(() => {
  destroy()
})
</script>
<style lang="scss" scoped>
:deep(.el-table__empty-block) {
  min-height: 40px;

  .el-table__empty-text {
    line-height: 40px;
  }
}

.table-box {
  height: 100%;
}
</style>
