package org.thingsboard.server.dao.logicalFlow.BO;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@ToString
@NoArgsConstructor
public class LogicalFlowMessage {

    /**
     * 执行的节点
     */
    private LogicalFlowNodeBO logicalFlowNodeBo;

    /**
     * 延迟执行的时间(单位: 毫秒)
     */
    private Long sleepOrContinuousTime;

    /**
     * 执行类型
     */
    private String type;
}
