package org.thingsboard.server.dao.model.request;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class StationCircuitTaskListRequest {

    private int page;

    private int size;

    private String code;

    private String type;

    // 维保部门
    private String optionDep;

    // 维保人员
    private String optionUserId;

    // 审核部门
    private String auditDep;

    // 审核人员
    private String auditUserId;

    // 预计开始时间范围
    private String startTime;
    private Date beginStartTime;
    private Date endStartTime;

    // 预计结束时间范围
    private String endTime;
    private Date beginEndTime;
    private Date endEndTime;

    // 实际开始时间范围
    private String realStartTime;
    private Date beginRealStartTime;
    private Date endRealStartTime;

    // 实际结束时间范围
    private String realEndTime;
    private Date beginRealEndTime;
    private Date endRealEndTime;

    // 计划名称
    private String keyword;

    private String status;

    private String auditResult;

    private String stationType;

    private String tenantId;

}
