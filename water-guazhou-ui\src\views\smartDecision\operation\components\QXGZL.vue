<template>
  <VChart :option="state.repaireOption"></VChart>
</template>
<script lang="ts" setup>
import { GetWorkOrderCountStatistic } from '@/api/workorder'

const state = reactive<{ repaireOption: any }>({
  repaireOption: null
})

const generateRepareOption = (data: number[]) => {
  const xData = Array.from({ length: 12 }).map((item, i) => i + 1 + '月')
  const yData = data
  const barWidth = 20
  const title = '抢修工作量'
  return {
    tooltip: {
      trigger: 'item'
    },
    grid: {
      left: 20,
      right: 20,
      bottom: 0,
      top: 40,
      containLabel: true
    },
    xAxis: [
      {
        boundaryGap: barWidth,
        type: 'category',
        data: xData,
        axisLine: {
          show: false
        },
        splitLine: {
          show: false
        },
        axisLabel: {
          color: '#fff',
          rotate: 45,
          margin: 12
        },
        axisTick: {
          show: false
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        splitLine: {
          show: false
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          show: false
        }
      }
    ],
    series: [
      {
        name: title,
        color: '#0099FF',
        type: 'bar',
        data: yData,
        hoverAnimation: false,
        barWidth,
        label: {
          show: true,
          position: 'top',
          distance: 10,
          color: '#fff'
        },
        itemStyle: {
          opacity: 0.8
        }
      },
      {
        name1: 'bar0-1',
        color: '#0099FF',
        type: 'pictorialBar',
        silent: true,
        symbolSize: [barWidth, barWidth / 2],
        symbolOffset: [0, barWidth / 4],
        data: yData
      },
      {
        name1: 'bar0-0',
        color: '#0099FF',
        silent: true,
        type: 'pictorialBar',
        symbolSize: [barWidth, barWidth / 2],
        symbolOffset: [0, -barWidth / 4],
        data: yData.map(item => {
          return {
            value: item,
            symbolPosition: 'end'
          }
        })
      }
    ]
  }
}
const refreshData = () => {
  GetWorkOrderCountStatistic({
    fromTime: moment().startOf('y').valueOf(),
    toTime: moment().valueOf(),
    statisticType: true,
    timeUnit: 'MONTH'
  }).then(res => {
    const data = res.data?.data?.types || []
    const totalData: number[] = []
    data.map(item => {
      if (item.data?.length) {
        item.data.map((o, i) => {
          if (totalData[i] === undefined) {
            totalData[i] = o.value ?? 0
          } else {
            totalData[i] += o.value ?? 0
          }
        })
      }
    })
    state.repaireOption = generateRepareOption(totalData)
  })
}
onMounted(async () => {
  refreshData()
})
</script>
<style lang="scss" scoped></style>
