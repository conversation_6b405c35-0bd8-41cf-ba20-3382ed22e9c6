<template>
  <el-table-column
    :key="config.prop"
    :prop="config.prop"
    :label="(config.preUnit || '') + config.label + ((config.unit && ' ' + config.unit) || '')"
    :width="config.width"
    :align="config.align || 'left'"
    :min-width="config.minWidth"
    :fixed="config.fixed"
    show-overflow-tooltip
  >
    <template #default="scope">
      <template v-if="config.subColumns?.length">
        <template
          v-for="(subColumn, i) in config.subColumns"
          :key="i"
        >
          <table-column :config="subColumn"></table-column>
        </template>
      </template>
      <template v-else>
        <template v-if="config.prop">
          <SLFormItem
            v-if="config.slFormItemConfig"
            v-model="scope.row[config.prop]"
            :config="config.slFormItemConfig"
            @change="val => config.handleChange && config.handleChange(scope.row, val)"
          ></SLFormItem>
          <template v-else>
            <!-- 表格项 图标 -->
            <el-icon
              v-if="config.icon || config.svgIcon"
              :style="config.iconStyle"
            >
              <i
                v-if="config.icon"
                :class="config.icon"
              ></i>
              <component
                :is="config.svgIcon"
                v-else
              ></component>
            </el-icon>
            <el-image
              v-if="config.image"
              style="width: 100px; height: 100px; margin-top: 5px"
              :style="config.cellStyle"
              :src="resolveElImageData(scope.row[config.prop], false)"
              :preview-src-list="resolveElImageData(scope.row[config.prop], true)"
            />
            <span
              v-else
              class="table-cell"
              :style="
                typeof config.cellStyle === 'function'
                  ? config.cellStyle(scope.row)
                  : config.cellStyle
              "
              :class="config.className"
              @click="config.handleClick && config.handleClick(scope.row)"
            >
              {{
                config.formatter
                  ? config.formatter(scope.row, scope.row[config.prop], config.prop)
                  : scope.row[config.prop]
              }}
            </span>
          </template>
        </template>
      </template>
    </template>
  </el-table-column>
</template>
<script lang="ts" setup>
import { ISLTableColumn } from './type'
import { resolveElImageData } from '@/utils/GlobalHelper'

defineProps<{ config: ISLTableColumn }>()
</script>
<style lang="scss" scoped></style>
