package org.thingsboard.server.dao.model.sql.fileRegistry;

import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class FileRegistry {
    @TableId
    // id
    private String id;

    // 文件名
    private String fileName;

    // 文件地址
    private String fileAddress;

    // 文件宿主，顶级分类，通常使用UUID
    private String host;

    // 文件标签，二级分类
    private String label;

    // 上传时间
    private Date uploadTime;

    // 客户Id
    private String tenantId;
}
