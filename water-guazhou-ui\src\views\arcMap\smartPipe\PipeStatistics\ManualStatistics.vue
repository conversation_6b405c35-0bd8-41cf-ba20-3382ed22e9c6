<!-- 高级统计 -->
<template>
  <RightDrawerMap
    ref="refMap"
    :title="'高级统计'"
    :full-content="true"
    @map-loaded="onMapLoaded"
  >
    <Form ref="refForm" :config="FormConfig"></Form>
    <template #detail-header>
      <span>统计结果</span>
    </template>
    <template #detail-default>
      <StatisticsCharts
        :view="staticState.view"
        :layer-ids="state.layerIds"
        :query-params="staticState.queryParams"
        :percision="0"
        :statistics-params="{
          group_fields: [refForm?.dataForm.group_fields],
          statistic_field:
            refForm?.dataForm.statistic_type === '1'
              ? refForm?.dataForm.statistic_field
              : EStatisticField.ShapeLen,
          statistic_type: refForm?.dataForm.statistic_type
        }"
        :tabs="state.tabs"
        :prefix="refForm?.dataForm.group_fields === 'DIAMETER' ? 'DN' : ''"
        :unit="refForm?.dataForm.statistic_type === '1' ? '个' : 'm'"
        @detail-refreshed="state.loading = false"
        @attr-row-click="handleAttrRowClick"
      ></StatisticsCharts>
    </template>
  </RightDrawerMap>
</template>
<script lang="ts" setup>
import { queryLayerClassName } from '@/api/mapservice';
import {
  GetFieldConfig,
  GetFieldUniqueValue
} from '@/api/mapservice/fieldconfig';
import { useSketch } from '@/hooks/arcgis';
import { useGisStore } from '@/store';
import {
  EStatisticField,
  // extentTo,
  getGraphicLayer,
  getLayerOids,
  getSubLayerIds
} from '@/utils/MapHelper';
import { SLMessage } from '@/utils/Message';
import {
  GetFieldConfig as GetFieldConfigByGeoserver,
  GetFieldValueByGeoserver,
} from '@/utils/geoserver/wfsUtils';
import RightDrawerMap from '../../components/common/RightDrawerMap.vue';
import StatisticsCharts from '../../components/components/StatisticsCharts.vue';

const refMap = ref<InstanceType<typeof RightDrawerMap>>();
const refForm = ref<IFormIns>();

const state = reactive<{
  tabs: any[];
  loading: boolean;
  layerInfos: any[];
  layerIds: any[];
  curType: 'ellipse' | 'rectangle' | 'polygon' | '';
  curOperate: string;
  curFieldNode?: any;
}>({
  tabs: [],
  curType: '',
  layerInfos: [],
  layerIds: [],
  loading: false,
  curOperate: ''
});
const staticState: {
  view?: __esri.MapView;
  graphicsLayer?: __esri.GraphicsLayer;
  sketch?: __esri.SketchViewModel;
  // drawer?: Draw
  // drawAction?: DrawAction
  queryParams: {
    geometry?: __esri.Geometry;
    where?: string;
  };
} = {
  queryParams: {
    geometry: undefined,
    where: '1=1'
  }
};
const FormConfig = reactive<IFormConfig>({
  group: [
    {
      fieldset: {
        desc: '绘制工具'
      },
      fields: [
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              title: '绘制多边形',
              disabled: () => state.loading,
              iconifyIcon: 'mdi:shape-polygon-plus',
              click: () => initDraw('polygon')
            },
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              title: '绘制矩形',
              disabled: () => state.loading,
              iconifyIcon: 'ep:crop',
              click: () => initDraw('rectangle')
            },
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              title: '绘制圆形',
              disabled: () => state.loading,
              iconifyIcon: 'mdi:ellipse-outline',
              click: () => initDraw('circle')
            },
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              title: '清除图形',
              disabled: () => state.loading,
              iconifyIcon: 'ep:delete',
              click: () => clearGraphicsLayer()
            }
          ]
        }
      ]
    },
    {
      id: 'layerid',
      fieldset: {
        desc: '选择图层'
      },
      fields: [
        {
          type: 'tree',
          options: [],
          checkStrictly: true,
          showCheckbox: true,
          field: 'layerid',
          nodeKey: 'value',
          handleCheckChange: (data, isChecked) => {
            if (isChecked) {
              refForm.value && (refForm.value.dataForm.layerid = [data.value]);
            }
          }
        }
      ]
    },
    {
      id: 'layer',
      fieldset: {
        desc: '图层字段'
      },
      fields: [
        {
          type: 'list',
          data: [],
          className: 'sql-list-wrapper',
          setData: async (config: IFormList, row) => {
            if(true){
              const layerid = row.layerid[0];
              const fields = await GetFieldConfigByGeoserver(layerid);
              if (fields && fields.data && fields.data.featureTypes && fields.data.featureTypes[0]) {
                // 获取字段列表
                const properties = fields.data.featureTypes[0].properties || [];
                config.data = properties;
                // 过滤字段
                const ignoredFields = [
                  'START_SID',
                  'END_SID',
                  'SID',
                  'OBJECTID',
                  'PIPELENGTH',
                  'X',
                  'Y',
                  'the_geom',  // GeoServer特有的几何字段
                  'geom'       // GeoServer特有的几何字段
                ];

                // 创建字段列表
                const curLayerFields = properties
                  .filter((item: any) => {
                    // 过滤出可编辑的字段
                    return (
                      ignoredFields.indexOf(item.name) === -1 &&
                      item.type !== 'gml:GeometryPropertyType' && // 排除几何字段
                      ['int', 'long', 'double', 'float', 'string', 'date'].indexOf(
                        item.type.split(':')[1]?.toLowerCase() || ''
                      ) !== -1
                    );
                  })
                  .map((item: any, index: number) => {
                    // 将GeoServer字段转换为表单项
                    const type = item.type.split(':')[1]?.toLowerCase() || '';
                    let fieldType = 'input';

                    if (['int', 'long', 'double', 'float'].includes(type)) {
                      fieldType = 'input-number';
                    } else if (type === 'date') {
                      fieldType = 'date';
                    }

                    return {
                      id: index,
                      field: item.name,
                      label: item.name,
                      type: fieldType,
                      disabled: false,
                      readonly: false
                    };
                  })
                  .sort((a: any, b: any) => {
                    return a.id - b.id;
                  });

                state.curLayerFields = formatTree(curLayerFields || [], {
                  id: 'field',
                  label: 'label',
                  value: 'field'
                });
                resetInlineFromConfig(curLayerFields);
              } else {
                console.error('无法获取GeoServer字段信息');
              }
            }else{
              if (!row.layerid?.length) return;
              const layerid = row.layerid[0];
              const layerName = state.layerInfos.find(
                (item) => item.layerid === layerid
              )?.layername;
              if (!layerName) return;
              const fields = await GetFieldConfig(layerName);
              config.data = fields.data?.result?.rows;
            }
          },
          setDataBy: 'layerid',
          displayField: 'name',
          valueField: 'name',
          highlightCurrentRow: true,
          nodeClick: (node) => {
            state.curFieldNode = node;
            appendSQL(node.name);
          }
        }
      ]
    },
    {
      id: 'field-construct',
      fieldset: {
        desc: '构建查询语句'
      },
      fields: [
        {
          type: 'btn-group',
          size: 'small',
          style: {
            width: '40%',
            display: 'flex',
            flexWrap: 'wrap'
          },
          className: 'sql-btns-wrapper',
          btns: [
            {
              perm: true,
              text: '=',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('=');
              }
            },
            {
              perm: true,
              text: '模糊',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL("like '%替换此处%'");
              }
            },
            {
              perm: true,
              text: '>',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('>');
              }
            },
            {
              perm: true,
              text: '<',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('<');
              }
            },
            {
              perm: true,
              text: '非',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('<>');
              }
            },
            {
              perm: true,
              text: '并且',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('and');
              }
            },
            {
              perm: true,
              text: '或者',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('or');
              }
            },
            {
              perm: true,
              text: '%',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('%');
              }
            }
          ],
          extraFormItem: [
            {
              type: 'list',
              wrapperStyle: {
                width: '60%',
                height: '144px'
              },
              className: 'sql-list-wrapper',
              field: 'uniqueValue',
              data: [],
              nodeClick: (node) => {
                appendSQL("'" + node + "'");
              },
              filters: [
                {
                  type: 'btn-group',
                  btns: [
                    {
                      perm: true,
                      text: () =>
                        state.curOperate === 'uniqueing'
                          ? '正在获取唯一值'
                          : '获取唯一值',
                      loading: () => state.curOperate === 'uniqueing',
                      disabled: () => state.curOperate === 'detailing',
                      styles: {
                        width: '100%',
                        borderRadius: '0'
                      },
                      click: () => getUniqueValue()
                    }
                  ]
                }
              ]
            }
          ]
        }
      ]
    },
    {
      fieldset: {
        desc: '组合查询条件'
      },
      fields: [
        {
          type: 'textarea',
          field: 'sql',
          placeholder: 'OBJECTID > 0'
        },
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: '清除组合条件',
              type: 'danger',
              disabled: () => state.curOperate === 'detailing',
              click: () => clear(),
              styles: {
                width: '100%'
              }
            }
          ]
        }
      ]
    },
    {
      fieldset: {
        desc: '统计参数'
      },
      fields: [
        {
          type: 'select',
          label: '分组字段',
          field: 'group_fields',
          clearable: false,
          options: [
            {
              label: '口径',
              value: 'DIAMETER'
            },
            { label: '材质', value: 'MATERIAL' },
            { label: '所在道路', value: 'LANEWAY' }
            // { label: '权属单位', value: 'OWNERUNIT' }
          ],
          setOptionBy: 'layerid',
          setOptionMethod: (config) => {
            config.option = [
              { label: '材质', value: 'MATERIAL' },
              {
                label: '口径',
                value: 'DIAMETER'
                // disabled: () => {
                //   const field = FormConfig.group[2]?.fields[0] as IFormList
                //   return (
                //     field?.data?.findIndex(
                //       item => item.value === 'DIAMETER'
                //     ) === -1
                //   )
                // }
              },
              { label: '所在道路', value: 'LANEWAY' },
              { label: '权属单位', value: 'OWNERUNIT' }
            ];
          }
        },
        {
          type: 'select',
          label: '统计字段',
          clearable: false,
          field: 'statistic_field',
          options: [],
          setOptionMethod: async (config: IFormSelect, row) => {
            if (!row.layerid?.length) return;
            const layerid = row.layerid[0];
            const layerName = state.layerInfos.find(
              (item) => item.layerid === layerid
            )?.layername;
            if (!layerName) return;
            const fields = await GetFieldConfig(layerName);
            config.options = fields.data?.result?.rows.map((item) => {
              return {
                id: item.name,
                label: item.alias,
                value: item.name
              };
            });
          },
          setOptionBy: 'layerid'
        },
        {
          type: 'select',
          label: '统计类型',
          clearable: false,
          field: 'statistic_type',
          options: [
            { label: '数量', value: '1' },
            {
              label: '长度',
              value: '2',
              disabled: (value, row) => {
                const layerIds = row.layerid || [];
                const layerInfo = state.layerInfos.find(
                  (item) => item.layerid === layerIds[0]
                );
                return layerInfo?.geometrytype !== 'esriGeometryPolyline';
              }
            }
          ]
        },
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: () =>
                state.curOperate === 'detailing' ? '正在统计' : '统计',
              disabled: () => state.curOperate === 'detailing',
              loading: () => state.curOperate === 'detailing',
              click: () => startQuery(),
              styles: {
                width: '100%'
              }
            }
          ]
        }
      ]
    }
  ],
  labelPosition: 'top',
  gutter: 12,
  defaultValue: {
    group_fields: 'DIAMETER',
    statistic_field: 'OBJECTID',
    statistic_type: '1'
  }
});

const handleAttrRowClick = (row?: any, tab?: string) => {
  console.log(row, tab);
  // const diameter = row.label
  // refreshDetailTable(diameter, tab)
};
const { initSketch, destroySketch } = useSketch();
const initDraw = (type: any) => {
  clearGraphicsLayer();
  staticState.sketch?.create(type);
};
const clearGraphicsLayer = () => {
  staticState.graphicsLayer?.removeAll();
  staticState.queryParams.geometry = undefined;
};

const appendSQL = (val) => {
  if (!refForm.value) return;
  if (!refForm.value?.dataForm) refForm.value.dataForm = {};
  const sql = refForm.value.dataForm.sql || ' ';
  refForm.value.dataForm.sql = sql + val + ' ';
};

const clear = () => {
  refForm.value?.dataForm && (refForm.value.dataForm.sql = '');
};

const getUniqueValue = async () => {
  // 获取图层名称
  const layerName = refForm.value?.dataForm.layerid;
  const fieldName = state.curFieldNode.name;
  // 调用GeoServer API获取唯一值
  const response = await GetFieldValueByGeoserver({
    layerName: layerName,
    fiedName: fieldName
  });

  // 检查响应是否有效
  if (!response || !response.data) {
    console.error('获取GeoServer唯一值响应无效:', response);
    SLMessage.error('获取唯一值失败');
    state.uniqueing = false;
    return;
  }

  const data = response.data;
  console.log('获取到的GeoServer数据:', data);

  // 检查数据是否有效
  if (!data.features || !Array.isArray(data.features)) {
    console.error('无效的GeoServer数据格式:', data);
    SLMessage.warning('无法获取唯一值');
    state.uniqueing = false;
    return;
  }

  // 创建一个空集合来存储唯一的字段值
  const uniqueValues = new Set();

  // 遍历特征数组
  data.features.forEach(feature => {
    if (feature && feature.properties) {
      // 添加字段值到集合中
      uniqueValues.add(feature.properties[fieldName]);
    }
  });

  // 转换为数组并过滤空值
  const uniqueValuesArray = Array.from(uniqueValues).filter(value => value !== null && value !== undefined);
  console.log('提取的唯一值:', uniqueValuesArray);

  // 设置列表数据
  const extraFormItem = FormConfig.group.find(
  (item) => item.id === 'field-construct'
  )?.fields[0].extraFormItem;
  const field = extraFormItem && (extraFormItem[0] as IFormList);
  field.data = uniqueValuesArray;

};
const getLayerInfo = async () => {
  const field = FormConfig.group.find((item) => item.id === 'layerid')
  ?.fields[0] as IFormTree;
  const layerInfo = staticState.view?.layerViews.items[0].layer.sublayers;
  let layers = layerInfo.items.map(item => {
    return {
      label: item.name,
      value: item.name,
      layername: item.name,
      type: item.type,
      spatialReferences: item.spatialReferences
    }
  });
  field.options = layers;// [{ label: '管线类', value: -2, children: layers }]
};

const startQuery = async () => {
  SLMessage.info('正在统计，请稍候...');
  try {
    state.tabs.length = 0;
    const layerIds = refForm.value?.dataForm.layerid;
    if (!layerIds?.length) {
      SLMessage.warning('请选择一个要统计的图层');
      return;
    }
    staticState.queryParams.where = refForm.value?.dataForm?.sql || '1=1';
    state.loading = true;
    state.tabs.length = 0;
    state.tabs = await getLayerOids(
      layerIds,
      state.layerInfos,
      staticState.queryParams
    );
    refMap.value?.toggleCustomDetail(true);
    // await refMap.value?.refreshDetail(tabs)
    let extent = staticState.queryParams.geometry?.extent;
    if (!extent) {
      const pipeLayer = staticState.view?.map.findLayerById('pipelayer');
      if (pipeLayer) {
        extent = pipeLayer.fullExtent;
      } else {
        extent = staticState.view?.extent;
      }
    }
    // extent && extentTo(staticState.view, extent, true)
  } catch (error) {
    console.log(error);
    state.loading = false;
    SLMessage.error('统计失败');
  }
};
const resolveDrawEnd = (result: ISketchHandlerParameter) => {
  staticState.queryParams.geometry = result.graphics[0]?.geometry;
};
const onMapLoaded = (view) => {
  staticState.view = view;
  staticState.graphicsLayer = getGraphicLayer(staticState.view, {
    id: 'search-manual',
    title: '高级统计'
  });
  staticState.sketch = initSketch(staticState.view, staticState.graphicsLayer, {
    createCallBack: resolveDrawEnd,
    updateCallBack: resolveDrawEnd
  });
  setTimeout(()=>{
    getLayerInfo()
  },1000)
  
};
onBeforeUnmount(() => {
  destroySketch();
  staticState.sketch = undefined;
});
</script>
<style lang="scss" scoped></style>
