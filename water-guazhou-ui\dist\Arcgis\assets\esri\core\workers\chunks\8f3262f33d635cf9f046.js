"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[4242],{84552:(e,t,r)=>{r.d(t,{Z:()=>m});var n=r(43697),i=r(2368),a=r(96674),s=r(35463),o=r(5600),l=(r(75215),r(67676),r(36030)),c=r(52011),u=r(78981);let d=class extends((0,i.J)(a.wq)){constructor(e){super(e),this.unit="milliseconds",this.value=0}toMilliseconds(){return(0,s.rJ)(this.value,this.unit,"milliseconds")}};(0,n._)([(0,l.J)(u.v,{nonNullable:!0})],d.prototype,"unit",void 0),(0,n._)([(0,o.Cb)({type:Number,json:{write:!0},nonNullable:!0})],d.prototype,"value",void 0),d=(0,n._)([(0,c.j)("esri.TimeInterval")],d);const m=d},74669:(e,t,r)=>{r.d(t,{Z:()=>i});var n=r(69801);class i{constructor(e,t){this._storage=new n.WJ,this._storage.maxSize=e,t&&this._storage.registerRemoveFunc("",t)}put(e,t){this._storage.put(e,t,1,1)}pop(e){return this._storage.pop(e)}get(e){return this._storage.get(e)}clear(){this._storage.clearAll()}destroy(){this._storage.destroy()}}},64830:(e,t,r)=>{r.d(t,{Z:()=>i});var n=r(70586);class i{constructor(e=(e=>e.values().next().value)){this._peeker=e,this._items=new Set}get length(){return this._items.size}clear(){this._items.clear()}last(){if(0===this._items.size)return;let e;for(e of this._items);return e}peek(){if(0!==this._items.size)return this._peeker(this._items)}push(e){this.contains(e)||this._items.add(e)}contains(e){return this._items.has(e)}pop(){if(0===this.length)return;const e=this.peek();return this._items.delete((0,n.j0)(e)),e}popLast(){if(0===this.length)return;const e=this.last();return this._items.delete((0,n.j0)(e)),e}remove(e){this._items.delete(e)}filter(e){return this._items.forEach((t=>{e(t)||this._items.delete(t)})),this}}},80903:(e,t,r)=>{r.d(t,{Z:()=>l});var n=r(50758),i=r(92604),a=r(95330),s=r(64830),o=r(25045);class l{constructor(){this._inUseClients=new Array,this._clients=new Array,this._clientPromises=new Array,this._ongoingJobsQueue=new s.Z}destroy(){this.close()}get closed(){return!this._clients||!this._clients.length}open(e,t){return new Promise(((r,n)=>{let i=!0;const s=e=>{(0,a.k_)(t.signal),i&&(i=!1,e())};this._clients.length=e.length,this._clientPromises.length=e.length,this._inUseClients.length=e.length;for(let i=0;i<e.length;++i){const l=e[i];(0,a.y8)(l)?this._clientPromises[i]=l.then((e=>(this._clients[i]=new o.default(e,t,(()=>this._ongoingJobsQueue.pop()??null)),s(r),this._clients[i])),(()=>(s(n),null))):(this._clients[i]=new o.default(l,t,(()=>this._ongoingJobsQueue.pop()??null)),this._clientPromises[i]=Promise.resolve(this._clients[i]),s(r))}}))}broadcast(e,t,r){const n=new Array(this._clientPromises.length);for(let i=0;i<this._clientPromises.length;++i){const a=this._clientPromises[i];n[i]=a.then((n=>n?.invoke(e,t,r)))}return n}close(){let e;for(;e=this._ongoingJobsQueue.pop();)e.deferred.reject((0,a.zE)(`Worker closing, aborting job calling '${e.methodName}'`));for(const e of this._clientPromises)e.then((e=>e?.close()));this._clients.length=0,this._clientPromises.length=0}invoke(e,t,r){let n;Array.isArray(r)?(i.Z.getLogger("esri.core.workers.Connection").warn("invoke()","The transferList parameter is deprecated, use the options object instead"),n={transferList:r}):n=r;const s=(0,a.dD)();this._ongoingJobsQueue.push({methodName:e,data:t,invokeOptions:n,deferred:s});for(let e=0;e<this._clientPromises.length;e++){const t=this._clients[e];t?t.jobAdded():this._clientPromises[e].then((e=>e?.jobAdded()))}return s.promise}on(e,t){return Promise.all(this._clientPromises).then((()=>(0,n.AL)(this._clients.map((r=>r.on(e,t))))))}openPorts(){return new Promise((e=>{const t=new Array(this._clientPromises.length);let r=t.length;for(let n=0;n<this._clientPromises.length;++n)this._clientPromises[n].then((i=>{i&&(t[n]=i.openPort()),0==--r&&e(t)}))}))}get test(){return{numClients:this._clients.length}}}},78346:(e,t,r)=>{r.d(t,{bA:()=>R});var n=r(20102),i=r(80442),a=r(95330),s=r(80903),o=r(25045),l=r(40330),c=r(92604),u=r(70586),d=r(94362),m=r(99880),p=r(68773),g=(r(2587),r(17452));const h={};function y(e){const t={async:e.async,isDebug:e.isDebug,locale:e.locale,baseUrl:e.baseUrl,has:{...e.has},map:{...e.map},packages:e.packages&&e.packages.concat()||[],paths:{...e.paths}};return e.hasOwnProperty("async")||(t.async=!0),e.hasOwnProperty("isDebug")||(t.isDebug=!1),e.baseUrl||(t.baseUrl=h.baseUrl),t}var f=r(41213);class w{constructor(){const e=document.createDocumentFragment();["addEventListener","dispatchEvent","removeEventListener"].forEach((t=>{this[t]=(...r)=>e[t](...r)}))}}class M{constructor(){this._dispatcher=new w,this._workerPostMessage({type:d.Cs.HANDSHAKE})}terminate(){}get onmessage(){return this._onmessageHandler}set onmessage(e){this._onmessageHandler&&this.removeEventListener("message",this._onmessageHandler),this._onmessageHandler=e,e&&this.addEventListener("message",e)}get onmessageerror(){return this._onmessageerrorHandler}set onmessageerror(e){this._onmessageerrorHandler&&this.removeEventListener("messageerror",this._onmessageerrorHandler),this._onmessageerrorHandler=e,e&&this.addEventListener("messageerror",e)}get onerror(){return this._onerrorHandler}set onerror(e){this._onerrorHandler&&this.removeEventListener("error",this._onerrorHandler),this._onerrorHandler=e,e&&this.addEventListener("error",e)}postMessage(e){(0,f.Y)((()=>{this._workerMessageHandler(new MessageEvent("message",{data:e}))}))}dispatchEvent(e){return this._dispatcher.dispatchEvent(e)}addEventListener(e,t,r){this._dispatcher.addEventListener(e,t,r)}removeEventListener(e,t,r){this._dispatcher.removeEventListener(e,t,r)}_workerPostMessage(e){(0,f.Y)((()=>{this.dispatchEvent(new MessageEvent("message",{data:e}))}))}async _workerMessageHandler(e){const t=(0,d.QM)(e);if(t&&t.type===d.Cs.OPEN){const{modulePath:e,jobId:r}=t;let n=await o.default.loadWorker(e);n||(n=await import(e));const i=o.default.connect(n);this._workerPostMessage({type:d.Cs.OPENED,jobId:r,data:i})}}}var I=r(70171),b=r(17202);const v=c.Z.getLogger("esri.core.workers.workerFactory"),{HANDSHAKE:A}=d.Cs;let C,D;const N="Failed to create Worker. Fallback to execute module in main thread";async function T(e){return new Promise((t=>{function r(i){const a=(0,d.QM)(i);a&&a.type===A&&(e.removeEventListener("message",r),e.removeEventListener("error",n),t(e))}function n(t){t.preventDefault(),e.removeEventListener("message",r),e.removeEventListener("error",n),v.warn("Failed to create Worker. Fallback to execute module in main thread",t),(e=new M).addEventListener("message",r),e.addEventListener("error",n)}e.addEventListener("message",r),e.addEventListener("error",n)}))}function S(){let e;if(null!=p.Z.default){const t={...p.Z};delete t.default,e=JSON.parse(JSON.stringify(t))}else e=JSON.parse(JSON.stringify(p.Z));e.assetsPath=(0,g.hF)(e.assetsPath),e.defaultAssetsPath=e.defaultAssetsPath?(0,g.hF)(e.defaultAssetsPath):void 0,e.request.interceptors=[],e.log.interceptors=[],e.locale=(0,I.Kd)(),e.has={"esri-csp-restrictions":(0,i.Z)("esri-csp-restrictions"),"esri-2d-debug":!1,"esri-2d-update-debug":(0,i.Z)("esri-2d-update-debug"),"featurelayer-pbf":(0,i.Z)("featurelayer-pbf"),"featurelayer-simplify-thresholds":(0,i.Z)("featurelayer-simplify-thresholds"),"featurelayer-simplify-payload-size-factors":(0,i.Z)("featurelayer-simplify-payload-size-factors"),"featurelayer-simplify-mobile-factor":(0,i.Z)("featurelayer-simplify-mobile-factor"),"esri-atomics":(0,i.Z)("esri-atomics"),"esri-shared-array-buffer":(0,i.Z)("esri-shared-array-buffer"),"esri-tiles-debug":(0,i.Z)("esri-tiles-debug"),"esri-workers-arraybuffer-transfer":(0,i.Z)("esri-workers-arraybuffer-transfer"),"feature-polyline-generalization-factor":(0,i.Z)("feature-polyline-generalization-factor"),"host-webworker":1,"polylabel-placement-enabled":(0,i.Z)("polylabel-placement-enabled")},e.workers.loaderUrl&&(e.workers.loaderUrl=(0,g.hF)(e.workers.loaderUrl)),e.workers.workerPath?e.workers.workerPath=(0,g.hF)(e.workers.workerPath):e.workers.workerPath=(0,g.hF)((0,m.V)("esri/core/workers/RemoteClient.js")),e.workers.useDynamicImport=!1;const t=p.Z.workers.loaderConfig,r=y({baseUrl:t?.baseUrl,locale:(0,I.Kd)(),has:{"csp-restrictions":1,"dojo-test-sniff":0,"host-webworker":1,...t?.has},map:{...t?.map},paths:{...t?.paths},packages:t?.packages||[]}),n={version:l.i8,buildDate:b.r,revision:b.$};return JSON.stringify({esriConfig:e,loaderConfig:r,kernelInfo:n})}let j=0;const{ABORT:L,INVOKE:Z,OPEN:x,OPENED:k,RESPONSE:U}=d.Cs;class _{static async create(e){const t=await async function(){if(!(0,i.Z)("esri-workers")||((0,i.Z)("mozilla"),0))return T(new M);if(!C&&!D)try{const e='let globalId=0;const outgoing=new Map,configuration=JSON.parse("{CONFIGURATION}");self.esriConfig=configuration.esriConfig;const workerPath=self.esriConfig.workers.workerPath,HANDSHAKE=0,OPEN=1,OPENED=2,RESPONSE=3,INVOKE=4,ABORT=5;function createAbortError(){const e=new Error("Aborted");return e.name="AbortError",e}function receiveMessage(e){return e&&e.data?"string"==typeof e.data?JSON.parse(e.data):e.data:null}function invokeStaticMessage(e,o,r){const t=r&&r.signal,n=globalId++;return new Promise(((r,i)=>{if(t){if(t.aborted)return i(createAbortError());t.addEventListener("abort",(()=>{outgoing.get(n)&&(outgoing.delete(n),self.postMessage({type:5,jobId:n}),i(createAbortError()))}))}outgoing.set(n,{resolve:r,reject:i}),self.postMessage({type:4,jobId:n,methodName:e,abortable:null!=t,data:o})}))}let workerRevisionChecked=!1;function checkWorkerRevision(e){if(!workerRevisionChecked&&e.kernelInfo){workerRevisionChecked=!0;const{revision:o,version:r}=configuration.kernelInfo,{revision:t,version:n}=e.kernelInfo;esriConfig.assetsPath!==esriConfig.defaultAssetsPath&&o!==t&&console.warn(`Version mismatch detected between ArcGIS API for JavaScript modules and assets. For more information visit https://bit.ly/3QnsuSo.\\nModules version: ${r}\\nAssets version: ${n}`)}}function messageHandler(e){const o=receiveMessage(e);if(!o)return;const r=o.jobId;switch(o.type){case 1:let n;function t(e){const o=n.connect(e);self.postMessage({type:2,jobId:r,data:o},[o])}"function"==typeof define&&define.amd?require([workerPath],(e=>{n=e.default||e,checkWorkerRevision(n),n.loadWorker(o.modulePath).then((e=>e||new Promise((e=>{require([o.modulePath],e)})))).then(t)})):"System"in self&&"function"==typeof System.import?System.import(workerPath).then((e=>(n=e.default,checkWorkerRevision(n),n.loadWorker(o.modulePath)))).then((e=>e||System.import(o.modulePath))).then(t):esriConfig.workers.useDynamicImport?import(workerPath).then((e=>{n=e.default||e,checkWorkerRevision(n),n.loadWorker(o.modulePath).then((e=>e||import(o.modulePath))).then(t)})):(self.RemoteClient||importScripts(workerPath),n=self.RemoteClient.default||self.RemoteClient,checkWorkerRevision(n),n.loadWorker(o.modulePath).then(t));break;case 3:if(outgoing.has(r)){const i=outgoing.get(r);outgoing.delete(r),o.error?i.reject(JSON.parse(o.error)):i.resolve(o.data)}}}self.dojoConfig=configuration.loaderConfig,esriConfig.workers.loaderUrl&&(self.importScripts(esriConfig.workers.loaderUrl),"function"==typeof require&&"function"==typeof require.config&&require.config(configuration.loaderConfig)),self.addEventListener("message",messageHandler),self.postMessage({type:0});'.split('"{CONFIGURATION}"').join(`'${S()}'`);C=URL.createObjectURL(new Blob([e],{type:"text/javascript"}))}catch(e){D=e||{}}let e;if(C)try{e=new Worker(C,{name:"esri-worker-"+j++})}catch(t){v.warn(N,D),e=new M}else v.warn(N,D),e=new M;return T(e)}();return new _(t,e)}constructor(e,t){this._outJobs=new Map,this._inJobs=new Map,this.worker=e,this.id=t,e.addEventListener("message",this._onMessage.bind(this)),e.addEventListener("error",(e=>{e.preventDefault(),c.Z.getLogger("esri.core.workers.WorkerOwner").error(e)}))}terminate(){this.worker.terminate()}async open(e,t={}){const{signal:r}=t,n=(0,d.jt)();return new Promise(((t,i)=>{const s={resolve:t,reject:i,abortHandle:(0,a.$F)(r,(()=>{this._outJobs.delete(n),this._post({type:L,jobId:n})}))};this._outJobs.set(n,s),this._post({type:x,jobId:n,modulePath:e})}))}_onMessage(e){const t=(0,d.QM)(e);if(t)switch(t.type){case k:this._onOpenedMessage(t);break;case U:this._onResponseMessage(t);break;case L:this._onAbortMessage(t);break;case Z:this._onInvokeMessage(t)}}_onAbortMessage(e){const t=this._inJobs,r=e.jobId,n=t.get(r);n&&(n.controller&&n.controller.abort(),t.delete(r))}_onInvokeMessage(e){const{methodName:t,jobId:r,data:n,abortable:i}=e,s=i?new AbortController:null,o=this._inJobs,c=l.Nv[t];let u;try{if("function"!=typeof c)throw new TypeError(`${t} is not a function`);u=c.call(null,n,{signal:s?s.signal:null})}catch(e){return void this._post({type:U,jobId:r,error:(0,d.AB)(e)})}(0,a.y8)(u)?(o.set(r,{controller:s,promise:u}),u.then((e=>{o.has(r)&&(o.delete(r),this._post({type:U,jobId:r},e))}),(e=>{o.has(r)&&(o.delete(r),e||(e={message:"Error encountered at method"+t}),(0,a.D_)(e)||this._post({type:U,jobId:r,error:(0,d.AB)(e||{message:`Error encountered at method ${t}`})}))}))):this._post({type:U,jobId:r},u)}_onOpenedMessage(e){const{jobId:t,data:r}=e,n=this._outJobs.get(t);n&&(this._outJobs.delete(t),(0,u.hw)(n.abortHandle),n.resolve(r))}_onResponseMessage(e){const{jobId:t,error:r,data:i}=e,a=this._outJobs.get(t);a&&(this._outJobs.delete(t),(0,u.hw)(a.abortHandle),r?a.reject(n.Z.fromJSON(JSON.parse(r))):a.resolve(i))}_post(e,t,r){return(0,d.oi)(this.worker,e,t,r)}}let z=(0,i.Z)("esri-workers-debug")?1:(0,i.Z)("esri-mobile")?Math.min(navigator.hardwareConcurrency-1,3):(0,i.Z)("host-browser")?navigator.hardwareConcurrency-1:0;z||(z=(0,i.Z)("safari")&&(0,i.Z)("mac")?7:2);let B=0;const E=[];async function H(e,t){const r=new s.Z;return await r.open(e,t),r}async function R(e,t={}){if("string"!=typeof e)throw new n.Z("workers:undefined-module","modulePath is missing");let r=t.strategy||"distributed";if((0,i.Z)("host-webworker")&&!(0,i.Z)("esri-workers")&&(r="local"),"local"===r){let r=await o.default.loadWorker(e);r||(r=await import(e)),(0,a.k_)(t.signal);const n=t.client||r;return H([o.default.connect(r)],{...t,client:n})}if(await async function(){if(O)return O;P=new AbortController;const e=[];for(let t=0;t<z;t++){const r=_.create(t).then((e=>(E[t]=e,e)));e.push(r)}return O=Promise.all(e),O}(),(0,a.k_)(t.signal),"dedicated"===r){const r=B++%z;return H([await E[r].open(e,t)],t)}if(t.maxNumWorkers&&t.maxNumWorkers>0){const r=Math.min(t.maxNumWorkers,z);if(r<z){const n=new Array(r);for(let i=0;i<r;++i){const r=B++%z;n[i]=E[r].open(e,t)}return H(n,t)}}return H(E.map((r=>r.open(e,t))),t)}let P,O=null},2587:(e,t,r)=>{r(90344),r(18848),r(940),r(70171);var n=r(94443),i=r(3172),a=r(20102),s=r(70586);async function o(e){if((0,s.pC)(c.fetchBundleAsset))return c.fetchBundleAsset(e);const t=await(0,i.default)(e,{responseType:"text"});return JSON.parse(t.data)}class l{constructor({base:e="",pattern:t,location:r=new URL(window.location.href)}){let n;n="string"==typeof r?e=>new URL(e,new URL(r,window.location.href)).href:r instanceof URL?e=>new URL(e,r).href:r,this.pattern="string"==typeof t?new RegExp(`^${t}`):t,this.getAssetUrl=n,e=e?e.endsWith("/")?e:e+"/":"",this.matcher=new RegExp(`^${e}(?:(.*)/)?(.*)$`)}fetchMessageBundle(e,t){return async function(e,t,r,i){const s=t.exec(r);if(!s)throw new a.Z("esri-intl:invalid-bundle",`Bundle id "${r}" is not compatible with the pattern "${t}"`);const l=s[1]?`${s[1]}/`:"",c=s[2],u=(0,n.Su)(i),d=`${l}${c}.json`,m=u?`${l}${c}_${u}.json`:d;let p;try{p=await o(e(m))}catch(t){if(m===d)throw new a.Z("intl:unknown-bundle",`Bundle "${r}" cannot be loaded`,{error:t});try{p=await o(e(d))}catch(e){throw new a.Z("intl:unknown-bundle",`Bundle "${r}" cannot be loaded`,{error:e})}}return p}(this.getAssetUrl,this.matcher,e,t)}}const c={};var u,d=r(99880);(0,n.tz)((u={pattern:"esri/",location:d.V},new l(u)))},940:(e,t,r)=>{r.d(t,{n:()=>c});var n=r(92604),i=r(78286),a=r(19153),s=r(90344),o=r(18848);const l=n.Z.getLogger("esri.intl.substitute");function c(e,t,r={}){const{format:n={}}=r;return(0,a.gx)(e,(e=>function(e,t,r){let n,a;const s=e.indexOf(":");if(-1===s?n=e.trim():(n=e.slice(0,s).trim(),a=e.slice(s+1).trim()),!n)return"";const o=(0,i.hS)(n,t);if(null==o)return"";const l=(a?r?.[a]:null)??r?.[n];return l?u(o,l):a?d(o,a):m(o)}(e,t,n)))}function u(e,t){switch(t.type){case"date":return(0,s.p6)(e,t.intlOptions);case"number":return(0,o.uf)(e,t.intlOptions);default:return l.warn("missing format descriptor for key {key}"),m(e)}}function d(e,t){switch(t.toLowerCase()){case"dateformat":return(0,s.p6)(e);case"numberformat":return(0,o.uf)(e);default:return l.warn(`inline format is unsupported since 4.12: ${t}`),/^(dateformat|datestring)/i.test(t)?(0,s.p6)(e):/^numberformat/i.test(t)?(0,o.uf)(e):m(e)}}function m(e){switch(typeof e){case"string":return e;case"number":return(0,o.uf)(e);case"boolean":return""+e;default:return e instanceof Date?(0,s.p6)(e):""}}},17017:(e,t,r)=>{r.d(t,{N:()=>s});var n=r(43697),i=r(5600),a=(r(75215),r(67676),r(52011));const s=e=>{let t=class extends e{constructor(){super(...arguments),this.customParameters=null}};return(0,n._)([(0,i.Cb)({type:Object,json:{write:{overridePolicy:e=>({enabled:!!(e&&Object.keys(e).length>0)})}}})],t.prototype,"customParameters",void 0),t=(0,n._)([(0,a.j)("esri.layers.mixins.CustomParametersMixin")],t),t}},34760:(e,t,r)=>{r.d(t,{Q:()=>f});var n=r(43697),i=r(92604),a=r(95330),s=r(5600),o=(r(75215),r(67676),r(52011)),l=r(46791),c=(r(80442),r(20102),r(26258),r(87538));const u=new l.Z,d=new WeakMap;function m(e){return null!=e&&"object"==typeof e&&"refreshInterval"in e&&"refresh"in e}function p(e,t){return Number.isFinite(e)&&Number.isFinite(t)?t<=0?e:p(t,e%t):0}let g=0,h=0;function y(){const e=Date.now();for(const t of u)t.refreshInterval&&e-(d.get(t)??0)+5>=6e4*t.refreshInterval&&(d.set(t,e),t.refresh(e))}(0,c.EH)((()=>{const e=Date.now();let t=0;for(const r of u)t=p(Math.round(6e4*r.refreshInterval),t),r.refreshInterval?d.get(r)||d.set(r,e):d.delete(r);if(t!==h){if(h=t,clearInterval(g),0===h)return void(g=0);g=setInterval(y,h)}}));const f=e=>{let t=class extends e{constructor(...e){super(...e),this.refreshInterval=0,this.refreshTimestamp=0,this._debounceHasDataChanged=(0,a.Ds)((()=>this.hasDataChanged())),this.when().then((()=>{!function(e){m(e)&&u.push(e)}(this)}),(()=>{}))}destroy(){var e;m(e=this)&&u.includes(e)&&u.remove(e)}get refreshParameters(){return{_ts:this.refreshTimestamp||null}}refresh(e=Date.now()){(0,a.R8)(this._debounceHasDataChanged()).then((t=>{t&&this._set("refreshTimestamp",e),this.emit("refresh",{dataChanged:t})}),(e=>{i.Z.getLogger(this.declaredClass).error(e),this.emit("refresh",{dataChanged:!1,error:e})}))}async hasDataChanged(){return!0}};return(0,n._)([(0,s.Cb)({type:Number,cast:e=>e>=.1?e:e<=0?0:.1,json:{write:!0}})],t.prototype,"refreshInterval",void 0),(0,n._)([(0,s.Cb)({readOnly:!0})],t.prototype,"refreshTimestamp",void 0),(0,n._)([(0,s.Cb)()],t.prototype,"refreshParameters",null),t=(0,n._)([(0,o.j)("esri.layers.mixins.RefreshableLayer")],t),t}},28294:(e,t,r)=>{r.d(t,{n:()=>m});var n=r(43697),i=r(92835),a=r(84552),s=r(5600),o=(r(75215),r(67676),r(71715)),l=r(52011),c=r(35671),u=r(76259),d=r(78981);const m=e=>{let t=class extends e{constructor(){super(...arguments),this.timeExtent=null,this.timeOffset=null,this.useViewTime=!0}readOffset(e,t){const r=t.timeInfo.exportOptions;if(!r)return null;const n=r.timeOffset,i=d.v.fromJSON(r.timeOffsetUnits);return n&&i?new a.Z({value:n,unit:i}):null}set timeInfo(e){(0,c.UF)(e,this.fieldsIndex),this._set("timeInfo",e)}};return(0,n._)([(0,s.Cb)({type:i.Z,json:{write:!1}})],t.prototype,"timeExtent",void 0),(0,n._)([(0,s.Cb)({type:a.Z})],t.prototype,"timeOffset",void 0),(0,n._)([(0,o.r)("service","timeOffset",["timeInfo.exportOptions"])],t.prototype,"readOffset",null),(0,n._)([(0,s.Cb)({value:null,type:u.Z,json:{write:!0,origins:{"web-document":{read:!1,write:!1},"portal-item":{read:!1,write:!1}}}})],t.prototype,"timeInfo",null),(0,n._)([(0,s.Cb)({type:Boolean,json:{read:{source:"timeAnimation"},write:{target:"timeAnimation"},origins:{"web-scene":{read:!1,write:!1}}}})],t.prototype,"useViewTime",void 0),t=(0,n._)([(0,l.j)("esri.layers.mixins.TemporalLayer")],t),t}},35956:(e,t,r)=>{r.d(t,{Z:()=>d});var n,i=r(43697),a=r(96674),s=r(22974),o=r(5600),l=r(75215),c=r(52011);let u=n=class extends a.wq{constructor(e){super(e),this.variableName=null,this.dimensionName=null,this.values=[],this.isSlice=!1}clone(){return new n({variableName:this.variableName,dimensionName:this.dimensionName,values:(0,s.d9)(this.values),isSlice:this.isSlice})}};(0,i._)([(0,o.Cb)({type:String,json:{write:!0}})],u.prototype,"variableName",void 0),(0,i._)([(0,o.Cb)({type:String,json:{write:!0}})],u.prototype,"dimensionName",void 0),(0,i._)([(0,o.Cb)({type:l.V5.array(l.V5.oneOf([l.V5.native(Number),l.V5.array(l.V5.native(Number))])),json:{write:!0}})],u.prototype,"values",void 0),(0,i._)([(0,o.Cb)({type:Boolean,json:{write:!0}})],u.prototype,"isSlice",void 0),u=n=(0,i._)([(0,c.j)("esri.layers.support.DimensionalDefinition")],u);const d=u},29876:(e,t,r)=>{r.d(t,{Z:()=>g});var n,i=r(43697),a=(r(66577),r(96674)),s=r(5600),o=(r(75215),r(67676),r(52011)),l=r(6570),c=r(38913),u=r(33955),d=r(35956);const m={base:r(9361).Z,key:"type",typeMap:{extent:l.Z,polygon:c.Z}};let p=n=class extends a.wq{constructor(e){super(e),this.areaOfInterest=null,this.subsetDefinitions=null}get dimensions(){const{subsetDefinitions:e}=this;if(null==e||0===e.length)return[];const t=new Map;e.forEach((e=>{if(!e.dimensionName)return;let r,n;if(Array.isArray(e.values[0])){const t=e.values;r=t[0][0],n=t[e.values.length-1][1]}else{const t=e.values;r=t[0],n=t[e.values.length-1]}if(t.has(e.dimensionName)){const i=t.get(e.dimensionName);i[0]=Math.min(r,i[0]),i[1]=Math.max(n,i[1])}else t.set(e.dimensionName,[r,n])}));const r=[];for(const e of t)r.push({name:e[0],extent:e[1]});return r}get variables(){const{subsetDefinitions:e}=this;if(null==e||0===e.length)return[];const t=new Set;return e.forEach((e=>{e.variableName&&t.add(e.variableName)})),[...t]}clone(){const e=this.subsetDefinitions?.map((e=>e.clone())),t=this.areaOfInterest?this.areaOfInterest.clone():this.areaOfInterest;return new n({areaOfInterest:t,subsetDefinitions:e})}};(0,i._)([(0,s.Cb)({types:m,json:{read:u.im,write:!0}})],p.prototype,"areaOfInterest",void 0),(0,i._)([(0,s.Cb)({readOnly:!0})],p.prototype,"dimensions",null),(0,i._)([(0,s.Cb)({readOnly:!0})],p.prototype,"variables",null),(0,i._)([(0,s.Cb)({type:[d.Z],json:{write:!0}})],p.prototype,"subsetDefinitions",void 0),p=n=(0,i._)([(0,o.j)("esri.layers.support.MultidimensionalSubset")],p);const g=p},40297:(e,t,r)=>{r.d(t,{Z:()=>M});var n,i=r(43697),a=r(96674),s=r(22974),o=r(5600),l=(r(75215),r(36030)),c=r(71715),u=r(52011),d=r(30556);const m=new Set(["raster","raster2","dem","fillraster"]),p=new Set(["rasters"]),g=e=>e&&e.rasterFunction?w.fromJSON(e):e,h=e=>e&&e instanceof w?e.toJSON():e,y=e=>e?.functionName&&!e.declaredClass,f=e=>y(e)?new w(e):e;let w=n=class extends a.wq{constructor(e){super(e),this.functionName=null,this.outputPixelType="unknown",this.variableName=null,this.rasterFunctionDefinition=null}set functionArguments(e){if(e){const t=Object.keys(e);if(t.some((t=>m.has(t.toLowerCase())&&y(e[t])))||t.some((t=>p.has(t.toLowerCase())&&Array.isArray(e[t])&&e[t].some((e=>y(e)))))){e=(0,s.d9)(e);for(const r of t)m.has(r.toLowerCase())?e[r]=f(e[r]):p.has(r.toLowerCase())&&Array.isArray(e[r])&&(e[r]=e[r].map((e=>f(e))))}}this._set("functionArguments",e)}readFunctionArguments(e){return(e=>{if(null==e)return null;e=(0,s.d9)(e);const t={};for(const r of Object.keys(e))m.has(r.toLowerCase())?t[r]=g(e[r]):p.has(r.toLowerCase())&&Array.isArray(e[r])?t[r]=e[r].map(g):t[r]=e[r];return t})(e)}writeFunctionArguments(e,t,r){const n={};for(const t of Object.keys(e))m.has(t.toLowerCase())?n[t]=h(e[t]):p.has(t.toLowerCase())&&Array.isArray(e[t])?n[t]=e[t].map(h):n[t]=h(e[t]);t[r]=n}readFunctionName(e,t){const r=t.rasterFunctionInfos;return t.name||(r&&r.length&&"None"!==r[0].name?r[0].name:t.rasterFunctionDefinition?t.rasterFunctionDefinition.name:t.rasterFunction)}clone(){return new n({functionName:this.functionName,functionArguments:(0,s.d9)(this.functionArguments),outputPixelType:this.outputPixelType,variableName:this.variableName,rasterFunctionDefinition:(0,s.d9)(this.rasterFunctionDefinition)})}};(0,i._)([(0,o.Cb)({json:{type:Object,name:"rasterFunctionArguments"}})],w.prototype,"functionArguments",null),(0,i._)([(0,c.r)("functionArguments")],w.prototype,"readFunctionArguments",null),(0,i._)([(0,d.c)("functionArguments")],w.prototype,"writeFunctionArguments",null),(0,i._)([(0,o.Cb)({json:{type:String,write:{target:"rasterFunction"}}})],w.prototype,"functionName",void 0),(0,i._)([(0,c.r)("functionName",["rasterFunction","rasterFunctionInfos","rasterFunctionDefinition"])],w.prototype,"readFunctionName",null),(0,i._)([(0,l.J)({C128:"c128",C64:"c64",F32:"f32",F64:"f64",S16:"s16",S32:"s32",S8:"s8",U1:"u1",U16:"u16",U2:"u2",U32:"u32",U4:"u4",U8:"u8",UNKNOWN:"unknown"},{ignoreUnknown:!1}),(0,o.Cb)({json:{default:"unknown"}})],w.prototype,"outputPixelType",void 0),(0,i._)([(0,o.Cb)({type:String,json:{read:!0,write:!0}})],w.prototype,"variableName",void 0),(0,i._)([(0,o.Cb)({type:Object,json:{name:"rasterFunctionDefinition"}})],w.prototype,"rasterFunctionDefinition",void 0),w=n=(0,i._)([(0,u.j)("esri.layers.support.RasterFunction")],w);const M=w},88281:(e,t,r)=>{r.d(t,{Z:()=>o});var n=r(20102),i=r(70586),a=r(78346),s=r(5847);class o{constructor(){this._workerThread=null,this._destroyed=!1}async initialize(){const e=await(0,a.bA)("RasterWorker");this._destroyed?e.close():this._workerThread=e}destroy(){this._destroyed=!0,this._workerThread&&(this._workerThread.close(),this._workerThread=null)}async convertVectorFieldData(e,t){if(!this._workerThread)throw new n.Z("raster-jobhandler:no-connection","no available worker connection");const r=await this._workerThread.invoke("convertVectorFieldData",{pixelBlock:e.pixelBlock.toJSON(),type:e.dataType},t);return r?new s.Z(r):null}async decode(e,t){if(!this._workerThread)throw new n.Z("raster-jobhandler:no-connection","no available worker connection");const r=await this._workerThread.invoke("decode",e,t);return r?new s.Z(r):null}async symbolize(e,t){if(!this._workerThread)throw new n.Z("raster-jobhandler:no-connection","no available worker connection");const r={extent:e.extent&&e.extent.toJSON(),pixelBlock:(0,i.pC)(e.pixelBlock)&&e.pixelBlock.toJSON(),simpleStretchParams:e.simpleStretchParams,bandIds:e.bandIds},a=await this._workerThread.invoke("symbolize",r,t);return a?new s.Z(a):null}async updateSymbolizer(e,t){if(!this._workerThread)throw new n.Z("raster-jobhandler:no-connection","no available worker connection");const r=e?.rendererJSON?.histograms;await Promise.all(this._workerThread.broadcast("updateSymbolizer",{symbolizerJSON:e.toJSON(),histograms:r},t))}async updateRasterFunction(e,t){if(!this._workerThread)throw new n.Z("raster-jobhandler:no-connection","no available worker connection");await Promise.all(this._workerThread.broadcast("updateRasterFunction",{rasterFunctionJSON:e.toJSON()},t))}async process(e,t){if(!this._workerThread)throw new n.Z("raster-jobhandler:no-connection","no available worker connection");const r=await this._workerThread.invoke("process",{extent:e.extent?.toJSON(),primaryPixelBlocks:e.primaryPixelBlocks.map((e=>(0,i.pC)(e)?e.toJSON():null)),primaryRasterIds:e.primaryRasterIds},t);return r?new s.Z(r):null}async stretch(e,t){if(!this._workerThread)throw new n.Z("raster-jobhandler:no-connection","no available worker connection");if(!e?.pixelBlock)return null;const r={srcPixelBlock:e.pixelBlock.toJSON(),stretchParams:e.stretchParams},i=await this._workerThread.invoke("stretch",r,t);return i?new s.Z(i):null}async split(e,t){if(!this._workerThread)throw new n.Z("raster-jobhandler:no-connection","no available worker connection");if(!e?.pixelBlock)return null;const r={srcPixelBlock:e.pixelBlock.toJSON(),tileSize:e.tileSize,maximumPyramidLevel:e.maximumPyramidLevel},i=await this._workerThread.invoke("split",r,t);return i&&i.forEach(((e,t)=>{i.set(t,e?s.Z.fromJSON(e):null)})),i}async estimateStatisticsHistograms(e,t){if(!this._workerThread)throw new n.Z("raster-jobhandler:no-connection","no available worker connection");if(!e?.pixelBlock)return null;const r={srcPixelBlock:e.pixelBlock.toJSON()};return await this._workerThread.invoke("estimateStatisticsHistograms",r,t)}async mosaicAndTransform(e,t){if(!this._workerThread)throw new n.Z("raster-jobhandler:no-connection","no available worker connection");if(!e?.srcPixelBlocks?.length)return{pixelBlock:null};const r={...e,srcPixelBlocks:e.srcPixelBlocks.map((e=>(0,i.pC)(e)?e.toJSON():null))},a=await this._workerThread.invoke("mosaicAndTransform",r,t);return{pixelBlock:a.pixelBlock?new s.Z(a.pixelBlock):null,localNorthDirections:a.localNorthDirections}}async createFlowMesh(e,t){if(!this._workerThread)throw new n.Z("raster-jobhandler:no-connection","no available worker connection");const r={buffer:e.flowData.data.buffer,maskBuffer:e.flowData.mask.buffer,width:e.flowData.width,height:e.flowData.height},{meshType:i,simulationSettings:a}=e,s=await this._workerThread.invoke("createFlowMesh",{meshType:i,flowData:r,simulationSettings:a},{...t,transferList:[r.buffer,r.maskBuffer]});return{vertexData:new Float32Array(s.vertexBuffer),indexData:new Uint32Array(s.indexBuffer)}}getProjectionOffsetGrid(e,t){if(!this._workerThread)throw new n.Z("raster-jobhandler:no-connection","no available worker connection");const r=(0,i.pC)(e.datumTransformation)?e.datumTransformation.steps.map((e=>({wkid:e.wkid,wkt:e.wkt,isInverse:e.isInverse}))):null,a=(0,i.pC)(e.rasterTransform)?e.rasterTransform.toJSON():null,s={projectedExtent:e.projectedExtent.toJSON(),srcBufferExtent:e.srcBufferExtent.toJSON(),pixelSize:e.pixelSize,hasWrapAround:e.hasWrapAround,spacing:e.spacing,datumTransformationSteps:r,rasterTransform:a,isAdaptive:e.isAdaptive,includeGCSGrid:e.includeGCSGrid};return this._workerThread.invoke("getProjectionOffsetGrid",s,t)}}},76259:(e,t,r)=>{r.d(t,{Z:()=>y});var n=r(43697),i=r(92835),a=r(84552),s=r(2368),o=r(96674),l=r(70586),c=r(5600),u=(r(75215),r(67676),r(71715)),d=r(52011),m=r(30556),p=r(80216);function g(e,t){return a.Z.fromJSON({value:e,unit:t})}let h=class extends((0,s.J)(o.wq)){constructor(e){super(e),this.cumulative=!1,this.endField=null,this.fullTimeExtent=null,this.hasLiveData=!1,this.interval=null,this.startField=null,this.timeReference=null,this.trackIdField=null,this.useTime=!0}readFullTimeExtent(e,t){if(!t.timeExtent||!Array.isArray(t.timeExtent)||2!==t.timeExtent.length)return null;const r=new Date(t.timeExtent[0]),n=new Date(t.timeExtent[1]);return new i.Z({start:r,end:n})}writeFullTimeExtent(e,t){e&&(0,l.pC)(e.start)&&(0,l.pC)(e.end)?t.timeExtent=[e.start.getTime(),e.end.getTime()]:t.timeExtent=null}readInterval(e,t){return t.timeInterval&&t.timeIntervalUnits?g(t.timeInterval,t.timeIntervalUnits):t.defaultTimeInterval&&t.defaultTimeIntervalUnits?g(t.defaultTimeInterval,t.defaultTimeIntervalUnits):null}writeInterval(e,t){t.timeInterval=e?.toJSON().value??null,t.timeIntervalUnits=e?.toJSON().unit??null}};(0,n._)([(0,c.Cb)({type:Boolean,json:{name:"exportOptions.timeDataCumulative",write:!0}})],h.prototype,"cumulative",void 0),(0,n._)([(0,c.Cb)({type:String,json:{name:"endTimeField",write:{enabled:!0,allowNull:!0}}})],h.prototype,"endField",void 0),(0,n._)([(0,c.Cb)({type:i.Z,json:{write:{enabled:!0,allowNull:!0}}})],h.prototype,"fullTimeExtent",void 0),(0,n._)([(0,u.r)("fullTimeExtent",["timeExtent"])],h.prototype,"readFullTimeExtent",null),(0,n._)([(0,m.c)("fullTimeExtent")],h.prototype,"writeFullTimeExtent",null),(0,n._)([(0,c.Cb)({type:Boolean,json:{write:!0}})],h.prototype,"hasLiveData",void 0),(0,n._)([(0,c.Cb)({type:a.Z,json:{write:{enabled:!0,allowNull:!0}}})],h.prototype,"interval",void 0),(0,n._)([(0,u.r)("interval",["timeInterval","timeIntervalUnits","defaultTimeInterval","defaultTimeIntervalUnits"])],h.prototype,"readInterval",null),(0,n._)([(0,m.c)("interval")],h.prototype,"writeInterval",null),(0,n._)([(0,c.Cb)({type:String,json:{name:"startTimeField",write:{enabled:!0,allowNull:!0}}})],h.prototype,"startField",void 0),(0,n._)([(0,c.Cb)({type:p.Z,json:{write:{enabled:!0,allowNull:!0}}})],h.prototype,"timeReference",void 0),(0,n._)([(0,c.Cb)({type:String,json:{write:{enabled:!0,allowNull:!0}}})],h.prototype,"trackIdField",void 0),(0,n._)([(0,c.Cb)({type:Boolean,json:{name:"exportOptions.useTime",write:!0}})],h.prototype,"useTime",void 0),h=(0,n._)([(0,d.j)("esri.layers.support.TimeInfo")],h);const y=h},99815:(e,t,r)=>{r.d(t,{MO:()=>s,Tj:()=>h,Ur:()=>o,W2:()=>p,WU:()=>y,WY:()=>g,gk:()=>I,jj:()=>f,nb:()=>m});var n=r(70586),i=r(35956);function a(e,t,r){const n=t.shift();if(0===r.length){const e=[];r.push({sliceId:-1,multidimensionalDefinition:e})}const i=r.length;for(let t=0;t<i;t++){const t=r.shift().multidimensionalDefinition;n.values?.forEach((i=>{r.push({sliceId:-1,multidimensionalDefinition:[...t,{variableName:e,dimensionName:n.name,values:[i]}]})}))}t.length&&a(e,t,r)}function s(e,t){const r=[];let n=0;return(t?e.variables.filter((e=>e.name.toLowerCase()===t.toLowerCase())):[...e.variables].sort(((e,t)=>e.name>t.name?1:-1))).forEach((e=>{const t=[],i=[...e.dimensions].sort(((e,t)=>e.name>t.name?-1:1));a(e.name,i,t),t.forEach((e=>{r.push({...e,sliceId:n++})}))})),r}function o(e,t,r){let i=e;if(t&&(t=[...t].sort(((e,t)=>e.dimensionName<t.dimensionName?-1:1))).forEach((({dimensionName:e,values:t,isSlice:r})=>{t.length&&(i=i.filter((n=>{const i=n.multidimensionalDefinition.find((t=>t.dimensionName===e));if(null==i)return!1;const a=i.values[0];return"number"==typeof a?"number"==typeof t[0]?t.includes(a):t.some((e=>e[0]<=a&&e[1]>=a)):"number"==typeof t[0]?t.some((e=>a[0]<=e&&a[1]>=e)):r?t.some((e=>e[0]===a[0]&&e[0]===a[1])):t.some((e=>e[0]>=a[0]&&e[0]<=a[1]||e[1]>=a[0]&&e[1]<=a[1]||e[0]<a[0]&&e[1]>a[1]))})))})),i.length&&r&&(0,n.pC)(r.start)&&(0,n.pC)(r.end)){const e=r.start.getTime(),t=r.end.getTime(),n=i[0].multidimensionalDefinition.findIndex((e=>"StdTime"===e.dimensionName));n>-1&&(i=i.filter((r=>{const i=r.multidimensionalDefinition[n].values[0];return e<=i&&t>=i})))}return i.map((e=>e.sliceId))}function l(e,t){return Array.isArray(e)?t[0]===t[1]?e[0]===t[0]||e[1]===t[0]:e[0]>=t[0]&&e[0]<=t[1]&&e[1]>=t[0]&&e[1]<=t[1]:e>=t[0]&&e<=t[1]}function c(e,t){return e[0]<=t[0]&&e[1]>=t[0]||e[0]<=t[1]&&e[1]>=t[1]||e[0]>=t[0]&&e[1]<=t[1]}function u(e){return 1===e.length?[e[0],e[0]]:[e[0],e[e.length-1]]}function d(e,t,r){if(!t?.subsetDefinitions?.length)return e;let n;if(r){const{variables:i}=t;if(i.length&&!i.includes(r))return null;const a=t.subsetDefinitions.find((t=>t.dimensionName===e.name&&t.variableName===r));if(!a?.values?.length)return e;n=u(a.values)}else n=t.dimensions.find((({name:t})=>t===e.name))?.extent;const i=n;if(!i||!i?.length)return e;const a=e.values.filter((e=>l(e,i)));return{...e,extent:[...i],values:a}}function m(e,t,r){if(!t?.subsetDefinitions?.length)return!1;const{variables:n}=t;if(n.length&&e.some((({variableName:e})=>e&&!n.includes(e))))return!0;for(let n=0;n<e.length;n++){const i=e[n],a=t.subsetDefinitions.find((e=>(""===i.variableName||e.variableName===i.variableName)&&e.dimensionName===i.dimensionName));if(a?.values.length){const e=u(a.values);if(i.isSlice||2!==i.values.length||Array.isArray(i.values[0])||i.values[0]===i.values[1]||!r){if(i.values.some((t=>!l(t,e))))return!0}else if(!c(i.values,e))return!0}}return!1}function p(e,t){if((0,n.Wi)(e))return{isOutside:!1};const{geometry:r,timeExtent:a,multidimensionalDefinition:s}=t;let o=null;if((0,n.pC)(a)&&(o=function(e,t){const r=e.dimensions.find((({name:e})=>"StdTime"===e));if(null==r||(0,n.Wi)(t.start)&&(0,n.Wi)(t.end))return t;t=t.clone();const{start:a,end:s}=t.toJSON(),o=a===s?[a]:null!=a&&null!=s?[a,s]:[a??s];return 2===o.length&&r?.extent.length&&(o[0]=Math.max(o[0],r.extent[0]),o[1]=Math.min(o[1],r.extent[1]??r.extent[0]),o[1]<o[0])||m([new i.Z({variableName:"",dimensionName:"StdTime",isSlice:1===o.length,values:o})],e,!0)?null:(t.start=new Date(o[0]),t.end=new Date(o[1]??o[0]),t)}(e,a),(0,n.Wi)(o)))return{isOutside:!0};const{areaOfInterest:l}=e;if(l&&r){const e="point"===r.type?r:"extent"===r.type?r.center:"polygon"===r.type?r.centroid:null;if(e&&!l.contains(e))return{isOutside:!0}}return(0,n.pC)(s)&&s.length&&m(s,e,!0)?{isOutside:!0}:{isOutside:!1,intersection:{geometry:r,timeExtent:o,multidimensionalDefinition:s}}}function g(e,t={}){const{multidimensionalInfo:r,keyProperties:i}=e;if((0,n.Wi)(r))return null;const{variableName:a,multidimensionalSubset:s,multidimensionalDefinition:o}=t,l=(0,n.pC)(o)?o[0]?.variableName:null,c=a||l||i?.DefaultVariable;let{variables:u}=r;return s?.variables?.length&&(u=u.filter((({name:e})=>s.variables.includes(e)))),c?u.find((({name:e})=>e===c))??u[0]:u[0]}function h(e,t={}){const r=g(e,t);if(!r)return null;const n=[],{dimensions:a,name:s}=r;if(0===a.length)return[new i.Z({variableName:s,dimensionName:"",values:[],isSlice:!0})];for(let e=0;e<a.length;e++){const r=d(a[e],t.multidimensionalSubset,s);if(!r)return null;const{values:o,extent:l}=r;let c=o?.[0]??l[0];"stdz"===r.name.toLowerCase()&&!r.hasRanges&&Math.abs(l[1])<=Math.abs(l[0])&&(c=o?.length?o[o.length-1]:l[1]),n.push(new i.Z({variableName:s,dimensionName:r.name,values:[c],isSlice:!t.useRangeForRangedDimensionInfo||!!r.hasRanges}))}return n}function y(e){return!((0,n.Wi)(e)||!e.length)&&e.some((e=>{if(null==e.values)return!0;const t=e.values.length;return 0===t||t>1||!e.isSlice&&Array.isArray(e.values[0])}))}function f(e,t){if((0,n.Wi)(t)||(0,n.Wi)(e))return null;let r=t.variables.map((e=>({...e})));return e?.variables?.length&&(r=r.filter((({name:t})=>e.variables.includes(t))),r.forEach((t=>{t.dimensions=t.dimensions.map((r=>d(r,e,t.name))).filter(n.pC)}))),r}function w(e,t){const{values:r}=t;if(r?.length)return Array.isArray(r[0])!==Array.isArray(e)?-1:Array.isArray(r[0])?r.findIndex((t=>t[0]===e[0]&&t[1]===e[1])):r.indexOf(e);const{extent:n}=t;if(Array.isArray(e)||e<n[0]||e>n[1])return-1;const i=t.interval||1;if("ISO8601"!==t.unit)return Math.round((e-n[0])/i);const a=n[0];let s=-1;switch(t.intervalUnit?.toLowerCase()||"seconds"){case"seconds":s=Math.round((e-a)/1e3/i);break;case"minutes":s=Math.round((e-a)/6e4/i);break;case"hours":s=Math.round((e-a)/36e5/i);break;case"days":s=Math.round((e-a)/864e5/i);break;case"months":{const t=new Date(e).getUTCFullYear()-new Date(a).getUTCFullYear(),r=new Date(a).getUTCMonth(),n=new Date(e).getUTCMonth();s=0===t?n-r:n+11-r+12*(t-1)}break;case"years":s=Math.round((new Date(e).getUTCFullYear()-new Date(a).getUTCFullYear())/i);break;case"decades":s=Math.round((new Date(e).getUTCFullYear()-new Date(a).getUTCFullYear())/10/i)}return s}function M(e){let t=e.values?.length;if(t)return t;const{extent:r,unit:n}=e,i=e.interval||1,a=r?r[1]-r[0]:0;if("ISO8601"!==n)return Math.round(a/i);switch(e.intervalUnit?.toLowerCase()??"seconds"){case"seconds":t=Math.round(a/1e3/i);break;case"minutes":t=Math.round(a/6e4/i);break;case"hours":t=Math.round(a/36e5/i);break;case"days":t=Math.round(a/864e5/i);break;case"months":{const e=new Date(r[1]).getUTCFullYear()-new Date(r[0]).getUTCFullYear(),n=new Date(r[1][0]).getUTCMonth(),i=new Date(r[1][1]).getUTCMonth();t=0===e?i-n+1:i+11-n+12*(e-1)+1}break;case"years":t=Math.round((new Date(r[1]).getUTCFullYear()-new Date(r[0]).getUTCFullYear())/i);break;case"decades":t=Math.round((new Date(r[1]).getUTCFullYear()-new Date(r[0]).getUTCFullYear())/10/i);break;default:t=0}return t}function I(e,t){let r=0;const n=e[0].variableName,i=[...t.variables].sort(((e,t)=>e.name>t.name?1:-1));for(let t=0;t<i.length;t++){const a=i[t],s=[...a.dimensions].sort(((e,t)=>e.name>t.name?-1:1));if(a.name!==n){r+=s.map((e=>M(e))).reduce(((e,t)=>e*t));continue}const o=s.map((e=>M(e))),l=s.length;for(let t=0;t<l;t++){const n=e.find((e=>e.dimensionName===s[t].name));if(null==n)return null;const i=w(n.values[0],s[t]);if(-1===i)return null;o.shift(),r+=t===l-1?i:i*o.reduce(((e,t)=>e*t))}break}return r}},69608:(e,t,r)=>{r.d(t,{c:()=>i,k:()=>a});var n=r(35454);const i=(0,n.w)()({RSP_NearestNeighbor:"nearest",RSP_BilinearInterpolation:"bilinear",RSP_CubicConvolution:"cubic",RSP_Majority:"majority"}),a=(0,n.w)()({esriNoDataMatchAny:"any",esriNoDataMatchAll:"all"})},78981:(e,t,r)=>{r.d(t,{v:()=>n});const n=(0,r(35454).w)()({esriTimeUnitsMilliseconds:"milliseconds",esriTimeUnitsSeconds:"seconds",esriTimeUnitsMinutes:"minutes",esriTimeUnitsHours:"hours",esriTimeUnitsDays:"days",esriTimeUnitsWeeks:"weeks",esriTimeUnitsMonths:"months",esriTimeUnitsYears:"years",esriTimeUnitsDecades:"decades",esriTimeUnitsCenturies:"centuries",esriTimeUnitsUnknown:void 0})},30030:(e,t,r)=>{r.d(t,{FK:()=>m,dr:()=>d,ij:()=>g});var n=r(20941),i=r(16050),a=r(24705),s=r(28092),o=r(73622),l=r(60849),c=r(81571),u=r(3943);const d={key:"type",base:null,typeMap:{"unique-value":c.Z,"class-breaks":i.Z,"raster-colormap":s.Z,"raster-stretch":l.Z,"vector-field":u.Z,"raster-shaded-relief":o.Z,flow:a.Z}},m={...d,typeMap:{...d.typeMap}};delete m.typeMap["vector-field"],delete m.typeMap.flow;const p={uniqueValue:c.Z,classBreaks:i.Z,rasterStretch:l.Z,rasterColormap:s.Z,vectorField:u.Z,rasterShadedRelief:o.Z,flowRenderer:a.Z};function g(e,t){if(!e)return null;if("classBreaks"===e.type&&e.classificationMethod){const t=e.authoringInfo||{classificationMethod:""};t.classificationMethod=e.classificationMethod,e.authoringInfo=t}"vectorField"===e.type&&e.visualVariables&&!Array.isArray(e.visualVariables)&&(e.visualVariables=[e.visualVariables]);const r=(i=e)&&p[i.type]||null;var i;if(r){const n=new r;return n.read(e,t),n}return t&&t.messages&&e&&t.messages.push(new n.Z("renderer:unsupported","Renderers of type '"+(e.type||"unknown")+"' are not supported",{definition:e,context:t})),null}},24705:(e,t,r)=>{r.d(t,{Z:()=>f});var n,i=r(43697),a=r(22303),s=r(35454),o=r(96674),l=r(62357),c=r(5600),u=(r(75215),r(67676),r(36030)),d=r(52011),m=r(41733),p=r(69237),g=r(66338);const h=new s.X({flow_from:"flow-from",flow_to:"flow-to"});let y=n=class extends((0,m.W)(o.wq)){constructor(e){super(e),this.density=.8,this.color=new a.Z([255,255,255,1]),this.maxPathLength=200,this.trailWidth=1.5,this.flowSpeed=10,this.trailLength=100,this.smoothing=0,this.flowRepresentation="flow-from",this.type="flow",this.authoringInfo=null,this.legendOptions=null,this.trailCap="butt",this.background="none"}clone(){const{density:e,maxPathLength:t,trailWidth:r,flowSpeed:i,trailLength:a,smoothing:s,flowRepresentation:o,trailCap:l,background:c}=this,u=this.color.clone(),d=(this.visualVariables||[]).map((e=>e.clone())),m=this.authoringInfo?.clone(),p=this.legendOptions?.clone();return new n({density:e,color:u,maxPathLength:t,trailWidth:r,flowSpeed:i,trailLength:a,trailCap:l,background:c,smoothing:s,flowRepresentation:o,visualVariables:d,authoringInfo:m,legendOptions:p})}getSymbol(e,t){}async getSymbolAsync(e,t){}getSymbols(){return[]}};(0,i._)([(0,c.Cb)({type:Number,json:{write:!0}})],y.prototype,"density",void 0),(0,i._)([(0,c.Cb)({type:a.Z,json:{write:{allowNull:!0}}})],y.prototype,"color",void 0),(0,i._)([(0,c.Cb)({type:Number,cast:l.t_,json:{write:!0}})],y.prototype,"maxPathLength",void 0),(0,i._)([(0,c.Cb)({type:Number,cast:l.t_,json:{write:!0}})],y.prototype,"trailWidth",void 0),(0,i._)([(0,c.Cb)({type:Number,json:{write:!0}})],y.prototype,"flowSpeed",void 0),(0,i._)([(0,c.Cb)({type:Number,json:{write:!0}})],y.prototype,"trailLength",void 0),(0,i._)([(0,c.Cb)({type:Number,cast:l.t_,json:{write:!1}})],y.prototype,"smoothing",void 0),(0,i._)([(0,c.Cb)({type:h.apiValues,json:{type:h.jsonValues,read:{reader:h.read},write:{writer:h.write}}})],y.prototype,"flowRepresentation",void 0),(0,i._)([(0,u.J)({flowRenderer:"flow"})],y.prototype,"type",void 0),(0,i._)([(0,c.Cb)({type:p.Z,json:{write:!0}})],y.prototype,"authoringInfo",void 0),(0,i._)([(0,c.Cb)({type:g.I,json:{write:!0}})],y.prototype,"legendOptions",void 0),(0,i._)([(0,c.Cb)({type:String,json:{write:!0}})],y.prototype,"trailCap",void 0),(0,i._)([(0,c.Cb)({type:String,json:{write:!0}})],y.prototype,"background",void 0),y=n=(0,i._)([(0,d.j)("esri.renderers.FlowRenderer")],y);const f=y},28092:(e,t,r)=>{r.d(t,{Z:()=>h});var n=r(43697),i=r(96674),a=r(5600),s=r(75215),o=(r(67676),r(36030)),l=r(52011),c=r(22303);let u=class extends i.wq{constructor(){super(...arguments),this.value=null,this.label=null,this.color=null}};(0,n._)([(0,a.Cb)({type:Number,json:{write:!0}})],u.prototype,"value",void 0),(0,n._)([(0,a.Cb)({type:String,json:{write:!0}})],u.prototype,"label",void 0),(0,n._)([(0,a.Cb)({type:c.Z,json:{type:[s.z8],write:!0}})],u.prototype,"color",void 0),u=(0,n._)([(0,l.j)("esri.renderers.support.ColormapInfo")],u);const d=u;var m,p=r(93010);let g=m=class extends i.wq{constructor(e){super(e),this.colormapInfos=null,this.type="raster-colormap"}static createFromColormap(e,t){if(!e)return null;const r=5===e[0].length,n=[...e].sort((e=>e[0][0]-e[1][0])).map((e=>d.fromJSON({value:e[0],color:r?e.slice(1,5):e.slice(1,4).concat([255]),label:t?t[e[0]]??"":e[0]})));return new m({colormapInfos:n})}static createFromColorramp(e){const t=(0,p.Jw)(e);return m.createFromColormap(t)}clone(){return new m({colormapInfos:this.colormapInfos.map((e=>e.toJSON()))})}extractColormap(){return this.colormapInfos.map((({value:e,color:t})=>[e,t.r,t.g,t.b,t.a>1?t.a:255*t.a&255])).sort(((e,t)=>e[0]-t[0]))}};(0,n._)([(0,a.Cb)({type:[d],json:{write:!0}})],g.prototype,"colormapInfos",void 0),(0,n._)([(0,o.J)({rasterColormap:"raster-colormap"})],g.prototype,"type",void 0),g=m=(0,n._)([(0,l.j)("esri.renderers.RasterColormapRenderer")],g);const h=g},73622:(e,t,r)=>{r.d(t,{Z:()=>p});var n,i=r(43697),a=r(96674),s=r(22974),o=r(5600),l=(r(75215),r(36030)),c=r(71715),u=r(52011),d=r(94593);let m=n=class extends a.wq{constructor(e){super(e),this.altitude=45,this.azimuth=315,this.colorRamp=null,this.hillshadeType="traditional",this.pixelSizePower=.664,this.pixelSizeFactor=.024,this.scalingType="none",this.type="raster-shaded-relief",this.zFactor=1}readColorRamp(e){return(0,d.i)(e)}clone(){return new n({hillshadeType:this.hillshadeType,altitude:this.altitude,azimuth:this.azimuth,zFactor:this.zFactor,scalingType:this.scalingType,pixelSizeFactor:this.pixelSizeFactor,pixelSizePower:this.pixelSizePower,colorRamp:(0,s.d9)(this.colorRamp)})}};(0,i._)([(0,o.Cb)({type:Number,json:{write:!0}})],m.prototype,"altitude",void 0),(0,i._)([(0,o.Cb)({type:Number,json:{write:!0}})],m.prototype,"azimuth",void 0),(0,i._)([(0,o.Cb)({types:d.V,json:{write:!0}})],m.prototype,"colorRamp",void 0),(0,i._)([(0,c.r)("colorRamp")],m.prototype,"readColorRamp",null),(0,i._)([(0,o.Cb)({type:["traditional","multi-directional"],json:{write:!0}})],m.prototype,"hillshadeType",void 0),(0,i._)([(0,o.Cb)({type:Number,json:{write:!0}})],m.prototype,"pixelSizePower",void 0),(0,i._)([(0,o.Cb)({type:Number,json:{write:!0}})],m.prototype,"pixelSizeFactor",void 0),(0,i._)([(0,o.Cb)({type:["none","adjusted"],json:{write:!0}})],m.prototype,"scalingType",void 0),(0,i._)([(0,l.J)({rasterShadedRelief:"raster-shaded-relief"})],m.prototype,"type",void 0),(0,i._)([(0,o.Cb)({type:Number,json:{write:!0}})],m.prototype,"zFactor",void 0),m=n=(0,i._)([(0,u.j)("esri.renderers.RasterShadedReliefRenderer")],m);const p=m},60849:(e,t,r)=>{r.d(t,{Z:()=>h});var n,i=r(43697),a=r(96674),s=r(22974),o=r(5600),l=(r(75215),r(36030)),c=r(71715),u=r(52011),d=r(30556),m=r(75509),p=r(94593);let g=n=class extends a.wq{constructor(e){super(e),this.colorRamp=null,this.computeGamma=!1,this.dynamicRangeAdjustment=!1,this.gamma=[],this.maxPercent=null,this.minPercent=null,this.numberOfStandardDeviations=null,this.outputMax=null,this.outputMin=null,this.sigmoidStrengthLevel=null,this.statistics=[],this.histograms=null,this.useGamma=!1,this.stretchType="none",this.type="raster-stretch"}readColorRamp(e){if(e)return(0,p.i)(e)}writeStatistics(e,t,r){e?.length&&(Array.isArray(e[0])||(e=e.map((e=>[e.min,e.max,e.avg,e.stddev]))),t[r]=e)}readStretchType(e,t){let r=t.stretchType;return"number"==typeof r&&(r=m.J[r]),m.v.read(r)}clone(){return new n({stretchType:this.stretchType,outputMin:this.outputMin,outputMax:this.outputMax,useGamma:this.useGamma,computeGamma:this.computeGamma,statistics:(0,s.d9)(this.statistics),gamma:(0,s.d9)(this.gamma),sigmoidStrengthLevel:this.sigmoidStrengthLevel,numberOfStandardDeviations:this.numberOfStandardDeviations,minPercent:this.minPercent,maxPercent:this.maxPercent,colorRamp:(0,s.d9)(this.colorRamp),histograms:(0,s.d9)(this.histograms),dynamicRangeAdjustment:this.dynamicRangeAdjustment})}};(0,i._)([(0,o.Cb)({types:p.V,json:{write:!0}})],g.prototype,"colorRamp",void 0),(0,i._)([(0,c.r)("colorRamp")],g.prototype,"readColorRamp",null),(0,i._)([(0,o.Cb)({type:Boolean,json:{write:!0}})],g.prototype,"computeGamma",void 0),(0,i._)([(0,o.Cb)({type:Boolean,json:{write:{target:"dra"},read:{source:"dra"}}})],g.prototype,"dynamicRangeAdjustment",void 0),(0,i._)([(0,o.Cb)({type:[Number],json:{write:!0}})],g.prototype,"gamma",void 0),(0,i._)([(0,o.Cb)({type:Number,json:{write:!0}})],g.prototype,"maxPercent",void 0),(0,i._)([(0,o.Cb)({type:Number,json:{write:!0}})],g.prototype,"minPercent",void 0),(0,i._)([(0,o.Cb)({type:Number,json:{write:!0}})],g.prototype,"numberOfStandardDeviations",void 0),(0,i._)([(0,o.Cb)({type:Number,json:{read:{source:"max"},write:{target:"max"}}})],g.prototype,"outputMax",void 0),(0,i._)([(0,o.Cb)({type:Number,json:{read:{source:"min"},write:{target:"min"}}})],g.prototype,"outputMin",void 0),(0,i._)([(0,o.Cb)({type:Number,json:{write:!0}})],g.prototype,"sigmoidStrengthLevel",void 0),(0,i._)([(0,o.Cb)({json:{type:[[Number]],write:!0}})],g.prototype,"statistics",void 0),(0,i._)([(0,o.Cb)()],g.prototype,"histograms",void 0),(0,i._)([(0,d.c)("statistics")],g.prototype,"writeStatistics",null),(0,i._)([(0,o.Cb)({type:Boolean,json:{write:!0}})],g.prototype,"useGamma",void 0),(0,i._)([(0,o.Cb)({type:m.v.apiValues,json:{type:m.v.jsonValues,write:m.v.write}})],g.prototype,"stretchType",void 0),(0,i._)([(0,c.r)("stretchType",["stretchType"])],g.prototype,"readStretchType",null),(0,i._)([(0,l.J)({rasterStretch:"raster-stretch"})],g.prototype,"type",void 0),g=n=(0,i._)([(0,u.j)("esri.renderers.RasterStretchRenderer")],g);const h=g},3943:(e,t,r)=>{r.d(t,{Z:()=>E});var n=r(43697),i=r(22303),a=(r(66577),r(38171)),s=r(9790),o=r(35454),l=r(96674),c=r(22974),u=r(70586),d=r(5600),m=(r(75215),r(36030)),p=r(71715),g=r(52011),h=r(16306),y=r(80676),f=r(16050),w=r(41733),M=r(32984),I=r(89513),b=r(23847),v=r(28101),A=r(4095),C=r(77987),D=r(66643),N=(r(80442),r(65617));function T(e){for(const t of e)if("number"==typeof t)return t;return null}function S(e,t,r){for(let n=0;n<3;n++){const i=e[n];switch(i){case"symbol-value":{const e=r[n];return null!=e?e/t[n]:1}case"proportional":break;default:if(i&&t[n])return i/t[n]}}return 1}function j(e,t,r,n){switch(e){case"proportional":return r*n;case"symbol-value":return null!=t?t:r;default:return e}}async function L(e,t){if(e&&t)return(0,s.dU)(e)?async function(e,t){const n=e.symbolLayers;n&&await(0,D.Ed)(n,(async e=>async function(e,t){switch(e.type){case"extrude":!function(e,t){e.size="number"==typeof t[2]?t[2]:0}(e,t);break;case"icon":case"line":case"text":!function(e,t){const r=T(t);(0,u.pC)(r)&&(e.size=r)}(e,t);break;case"path":!function(e,t){const r=S(t,N.O,[e.width,void 0,e.height]);e.width=j(t[0],e.width,1,r),e.height=j(t[2],e.height,1,r)}(e,t);break;case"object":await async function(e,t){const{resourceSize:n,symbolSize:i}=await async function(e){const t=await r.e(2134).then(r.bind(r,42134)),n=await t.computeObjectLayerResourceSize(e,10),{width:i,height:a,depth:s}=e,o=[i,s,a];let l=1;for(let e=0;e<3;e++){const t=o[e];if(null!=t){l=t/n[e];break}}for(let e=0;e<3;e++)null==o[e]&&(o[e]=n[e]*l);return{resourceSize:n,symbolSize:o}}(e),a=S(t,n,i);e.width=j(t[0],i[0],n[0],a),e.depth=j(t[1],i[1],n[1],a),e.height=j(t[2],i[2],n[2],a)}(e,t)}}(e,t)))}(e,t):void((0,s.cT)(e)&&function(e,t){const r=T(t);if(!(0,u.Wi)(r))switch(e.type){case"simple-marker":e.size=r;break;case"picture-marker":{const t=e.width/e.height;t>1?(e.width=r,e.height=r*t):(e.width=r*t,e.height=r);break}case"simple-line":e.width=r;break;case"text":e.font.size=r}}(e,t))}new((r(57520),r(99880),r(3172),r(74669)).Z)(1e3),new i.Z([128,128,128]),r(52879),new i.Z("white");var Z,x=r(94139),k=r(37898);const U=new Set(["esriMetersPerSecond","esriKilometersPerHour","esriKnots","esriFeetPerSecond","esriMilesPerHour"]),_=new o.X({beaufort_ft:"beaufort-ft",beaufort_km:"beaufort-km",beaufort_kn:"beaufort-kn",beaufort_m:"beaufort-m",beaufort_mi:"beaufort-mi",classified_arrow:"classified-arrow",ocean_current_kn:"ocean-current-kn",ocean_current_m:"ocean-current-m",simple_scalar:"simple-scalar",single_arrow:"single-arrow",wind_speed:"wind-barb"}),z=new o.X({flow_from:"flow-from",flow_to:"flow-to"});let B=Z=class extends((0,w.W)(l.wq)){constructor(e){super(e),this.attributeField="Magnitude",this.flowRepresentation="flow-from",this.rotationType="arithmetic",this.style="single-arrow",this.symbolTileSize=50,this.type="vector-field"}readInputUnit(e,t){return U.has(e)?y.NL.fromJSON(e):null}readOutputUnit(e,t){return U.has(e)?y.NL.fromJSON(e):null}get styleRenderer(){const e=this.style,t=this.attributeField,r=this._createStyleRenderer(e);return r.field=t,r}get sizeVariables(){const e=[];if(this.visualVariables)for(const t of this.visualVariables)"size"===t.type&&e.push(t);if(0===e.length){const t=new b.Z({field:"Magnitude",minSize:.2*this.symbolTileSize,maxSize:.8*this.symbolTileSize});this.visualVariables?this.visualVariables.push(t):this._set("visualVariables",[t]),e.push(t)}return e}get rotationVariables(){const e=[];if(this.visualVariables)for(const t of this.visualVariables)"rotation"===t.type&&e.push(t);if(0===e.length){const t=new I.Z({field:"Direction",rotationType:this.rotationType});this.visualVariables?this.visualVariables.push(t):this._set("visualVariables",[t]),e.push(t)}return e}clone(){return new Z({attributeField:this.attributeField,flowRepresentation:this.flowRepresentation,rotationType:this.rotationType,symbolTileSize:this.symbolTileSize,style:this.style,visualVariables:(0,c.d9)(this.visualVariables),inputUnit:this.inputUnit,outputUnit:this.outputUnit})}async getGraphicsFromPixelData(e,t=!1,r=[]){const n=new Array,i=(0,y.Yx)(this.inputUnit,this.outputUnit),s=this.rotationVariables[0]?.rotationType||this.rotationType,o=t?(0,y.KC)(e.pixelBlock,"vector-uv",s,i):(0,y.QI)(e.pixelBlock,"vector-magdir",i);if((0,u.Wi)(o))return n;const l=e.extent,c=(0,u.pC)(o.mask)&&o.mask.length>0;let d=0;const m=(l.xmax-l.xmin)/o.width,p=(l.ymax-l.ymin)/o.height;for(let e=0;e<o.height;e++)for(let t=0;t<o.width;t++,d++){let i=new x.Z({x:l.xmin+t*m+m/2,y:l.ymax-e*p-p/2,spatialReference:l.spatialReference});i=(await(0,h.aX)(i))[0];const s=r.some((e=>e.intersects(i)));if((!c||o.mask[d])&&!s){const e={Magnitude:o.pixels[0][d],Direction:o.pixels[1][d]},t=new a.Z({geometry:{type:"point",x:i.x,y:i.y,spatialReference:l.spatialReference},attributes:e});t.symbol=this._getVisualVariablesAppliedSymbol(t),n.push(t)}}return n}getSymbol(e,t){}async getSymbolAsync(e,t){}getSymbols(){return[]}getClassBreakInfos(){return this.styleRenderer?.classBreakInfos}getDefaultSymbol(){return this.styleRenderer?.defaultSymbol}_getDefaultSymbol(e){return new C.Z({path:"M14,32 14,18 9,23 16,3 22,23 17,18 17,32 z",outline:new A.Z({width:0}),size:20,color:e||new i.Z([0,92,230])})}_getVisualVariablesAppliedSymbol(e){if(!e)return;const t=this.styleRenderer?.getSymbol(e)?.clone(),r=this.sizeVariables,n=this.rotationVariables;if(r&&r.length&&this.sizeVariables.forEach((r=>L(t,(0,v.PR)([r],e)))),n&&n.length){const r="flow-to"===this.flowRepresentation==("ocean-current-kn"===this.style||"ocean-current-m"===this.style)?0:180;e.attributes.Direction=e.attributes.Direction+r,this.rotationVariables.forEach((r=>function(e,t,r){if(e&&null!=t)if((0,s.dU)(e)){const n=e.symbolLayers;n&&n.forEach((e=>{if(e&&"object"===e.type)switch(r){case"tilt":e.tilt=t;break;case"roll":e.roll=t;break;default:e.heading=t}}))}else(0,s.cT)(e)&&("simple-marker"!==e.type&&"picture-marker"!==e.type&&"text"!==e.type||(e.angle=t))}(t,(0,v.cM)(r,e),r.axis)))}return t}_createStyleRenderer(e){let t={defaultSymbol:this._getDefaultSymbol(),classBreakInfos:[]};switch(e){case"single-arrow":t=this._createSingleArrowRenderer();break;case"beaufort-kn":t=this._createBeaufortKnotsRenderer();break;case"beaufort-m":t=this._createBeaufortMeterRenderer();break;case"beaufort-ft":t=this._createBeaufortFeetRenderer();break;case"beaufort-mi":t=this._createBeaufortMilesRenderer();break;case"beaufort-km":t=this._createBeaufortKilometersRenderer();break;case"ocean-current-m":t=this._createCurrentMeterRenderer();break;case"ocean-current-kn":t=this._createCurrentKnotsRenderer();break;case"simple-scalar":t=this._createSimpleScalarRenderer();break;case"wind-barb":t=this._createWindBarbsRenderer();break;case"classified-arrow":t=this._createClassifiedArrowRenderer()}return new f.Z(t)}_createSingleArrowRenderer(){return{defaultSymbol:this._getDefaultSymbol()}}_createBeaufortKnotsRenderer(){return{defaultSymbol:this._getDefaultSymbol(new i.Z([214,47,39])),classBreakInfos:this._getClassBreaks([0,1,3,6,10,16,21,27,33,40,47,55,63],[[40,146,199],[89,162,186],[129,179,171],[160,194,155],[191,212,138],[218,230,119],[250,250,100],[252,213,83],[252,179,102],[250,141,52],[247,110,42],[240,71,29]])}}_createBeaufortMeterRenderer(){return{defaultSymbol:this._getDefaultSymbol(new i.Z([214,47,39])),classBreakInfos:this._getClassBreaks([0,.2,1.8,3.3,5.4,8.5,11,14.1,17.2,20.8,24.4,28.6,32.7],[[69,117,181],[101,137,184],[132,158,186],[162,180,189],[192,204,190],[222,227,191],[255,255,191],[255,220,161],[250,185,132],[245,152,105],[237,117,81],[232,21,21]])}}_createBeaufortFeetRenderer(){const e=this._getDefaultSymbol(new i.Z([214,47,39]));let t=[0,.2,1.8,3.3,5.4,8.5,11,14.1,17.2,20.8,24.4,28.6,32.7];return t=t.map((e=>3.28084*e)),{defaultSymbol:e,classBreakInfos:this._getClassBreaks(t,[[69,117,181],[101,137,184],[132,158,186],[162,180,189],[192,204,190],[222,227,191],[255,255,191],[255,220,161],[250,185,132],[245,152,105],[237,117,81],[232,21,21]])}}_createBeaufortMilesRenderer(){const e=this._getDefaultSymbol(new i.Z([214,47,39]));let t=[0,.2,1.8,3.3,5.4,8.5,11,14.1,17.2,20.8,24.4,28.6,32.7];return t=t.map((e=>2.23694*e)),{defaultSymbol:e,classBreakInfos:this._getClassBreaks(t,[[69,117,181],[101,137,184],[132,158,186],[162,180,189],[192,204,190],[222,227,191],[255,255,191],[255,220,161],[250,185,132],[245,152,105],[237,117,81],[232,21,21]])}}_createBeaufortKilometersRenderer(){const e=this._getDefaultSymbol(new i.Z([214,47,39]));let t=[0,.2,1.8,3.3,5.4,8.5,11,14.1,17.2,20.8,24.4,28.6,32.7];return t=t.map((e=>3.6*e)),{defaultSymbol:e,classBreakInfos:this._getClassBreaks(t,[[69,117,181],[101,137,184],[132,158,186],[162,180,189],[192,204,190],[222,227,191],[255,255,191],[255,220,161],[250,185,132],[245,152,105],[237,117,81],[232,21,21]])}}_createCurrentMeterRenderer(){return{defaultSymbol:this._getDefaultSymbol(new i.Z([177,177,177])),classBreakInfos:this._getClassBreaks([0,.5,1,1.5,2],[[78,26,153],[179,27,26],[202,128,26],[177,177,177]])}}_createCurrentKnotsRenderer(){return{defaultSymbol:this._getDefaultSymbol(new i.Z([177,177,177])),classBreakInfos:this._getClassBreaks([0,.25,.5,1,1.5,2,2.5,3,3.5,4],[[0,0,0],[0,37,100],[78,26,153],[151,0,100],[179,27,26],[177,78,26],[202,128,26],[177,179,52],[177,177,177]])}}_createClassifiedArrowRenderer(){const e=this._getDefaultSymbol(new i.Z([56,168,0]));let t=[0,1e-6,3.5,7,10.5,14];if(this.sizeVariables?.length){const e=this.sizeVariables[0].minDataValue,r=this.sizeVariables[0].maxDataValue;if(e&&r){const n=(r-e)/5;t=Array.from(Array(6).keys()).map((t=>e+n*t))}}return{defaultSymbol:e,classBreakInfos:this._getClassBreaks(t,[[56,168,0],[139,309,0],[255,255,0],[255,128,0],[255,0,0]])}}_createSimpleScalarRenderer(){return{defaultSymbol:k.Z.fromJSON({imageData:"iVBORw0KGgoAAAANSUhEUgAAACsAAAArCAQAAABLVLlLAAAABGdBTUEAAYagMeiWXwAAAAJiS0dEAACqjSMyAAAACXBIWXMAAABIAAAASABGyWs+AAAC3ElEQVRIx9XXvW4cVRQH8N982btpsIREJECyiCXsxX4DKh6AliqGKCBBE2SlwlHgAbBD/AKmyEYUeQ1KahPZSZQvBCkQLTHZ7KGY8Xodz4w3a1NwbzVzz/znfJ//zbStVC5q3icKak9GAs2QIdDx3PtW/S011NW3p+M5Eomh11ipTIKe6+4LQzHaQ+G+63pIZNJJQXMpljwTwj1brpgx5w1zZlyx5Z4QnllEIm2xeeSUHBf0hV0bejo1Uh09G3aFvgXk7cCJFBc9EdaRVuHJJaOdKyTV2TVhYLMduNR0Q9gxL5GaaTDw8GzejrDRBpxWoGsySRW0dttKuattwNkIlFw2YXgzOdYq4Ox49PlM+JrKd5OusjTWhBuVxUfMX/KXXZ3WEmkuqa67wspR4BTbwtKr/5u4fFgStse/T7EifFPnnYl9zPq4vmUOPrRndgoHjDti1gOPqlyXoifcRNGQzUd31lDyfHmob1Gp35vSr+P6vilcQ5Egtyd8YF/ySg9NhPM+9M/IOaHwp5+PSZayXTvCogEUwlatC3J8LLwYtcWB8EuDXQVuCkV5/B4eNHb7wGBs87LBDS+xjdVSn09wq1G8dFM+9tSUhIGneLvUdniKxKpTYljCpu3j7rVWlHj/P23v4NPGUEyeCQnexe9lJjzEQqMjJs+EzNAX6B98dBZVRmroJx95x/A/6gln18EyfCUsl+qdXb/tjvfbw+mwforpUOBz4XLVoBwAn3aWnfeH246NyBXhrq7TTN5lNSP9RkU+puUJm3W2Tsdq0nZWM07srk7MwQrZSRysjjGWBLRJNsNbfj2JMR4AbxpU1XLAb9Mxfpsq5EjMuuiR8L0JiHOOBX3hiUvOmavN0nMueSzcceFk0BK4pMqLo7vDD1Z0qrtDx7Itt4Xwm9UqbMmk8S0Dtuzb2pvOU99Z1nLTOfleNmvfZfP2pYZmPfajwosKdDBNpacNpVGGsWX9CyDI8Xq/Sj6QAAAAJXRFWHRkYXRlOmNyZWF0ZQAyMDE0LTExLTEwVDAzOjE3OjU4LTA1OjAwF+tHyQAAACV0RVh0ZGF0ZTptb2RpZnkAMjAxNC0xMS0xMFQwMzoxNzo1OC0wNTowMGa2/3UAAAAASUVORK5CYII=",height:20,width:20,type:"esriPMS",angle:0})}}_createWindBarbsRenderer(){const e=Array.from(Array(31).keys()).map((e=>5*e)),t=[{range:"0-5",path:"M20 20 M5 20 A15 15 0 1 0 35 20 A15 15 0 1 0 5 20 M20 20 M10 20 A10 10 0 1 0 30 20 A10 10 0 1 0 10 20",imageData:"PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGhlaWdodD0iMzJweCIgd2lkdGg9IjMycHgiIHZpZXdCb3g9IjkgMCAyNiA0MSI+CiAgPHBhdGggZD0iTTIwIDIwIE01IDIwIEExNSAxNSAwIDEgMCAzNSAyMCBBMTUgMTUgMCAxIDAgNSAyMCBNMjAgMjAgTTEwIDIwIEExMCAxMCAwIDEgMCAzMCAyMCBBMTAgMTAgMCAxIDAgMTAgMjAiIHN0eWxlPSJzdHJva2U6cmdiKDAsMCwwKTtzdHJva2Utd2lkdGg6MS41Ii8+CiA8L3N2Zz4="},{range:"5-10",path:"M25 0 L25 40 M25 35 L17.5 37.5",imageData:"PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjkgMCAyNyA0NiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cGF0aCBkPSJNMjUgMCBMMjUgNDAgTTI1IDM1IEwxNy41IDM3LjUiIHN0eWxlPSJzdHJva2U6cmdiKDAsMCwwKTtzdHJva2Utd2lkdGg6MS41Ii8+CiA8L3N2Zz4="},{range:"10-15",path:"M25 0 L25 40 L10 45 L25 40",imageData:"PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjkgMCAyNyA0NiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cGF0aCBkPSJNMjUgMCBMMjUgNDAgTDEwIDQ1IEwyNSA0MCIgc3R5bGU9InN0cm9rZTpyZ2IoMCwwLDApO3N0cm9rZS13aWR0aDoxLjUiLz4KIDwvc3ZnPg=="},{range:"15-20",path:"M25 0 L25 40 L10 45 L25 40 M25 35 L17.5 37.5",imageData:"PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjEyIDAgMTUgNDUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CiAgPHBhdGggZD0iTTI1IDAgTDI1IDQwIEwxMCA0NSBMMjUgNDAgTTI1IDM1IEwxNy41IDM3LjUiIHN0eWxlPSJzdHJva2U6cmdiKDAsMCwwKTtzdHJva2Utd2lkdGg6MS41Ii8+CiA8L3N2Zz4="},{range:"20-25",path:"M25 0 L25 40 L10 45 L25 40 M25 35 L10 40",imageData:"PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjkgMCAyNiA0NiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cGF0aCBkPSJNMjUgMCBMMjUgNDAgTDEwIDQ1IEwyNSA0MCBNMjUgMzUgTDEwIDQwIiBzdHlsZT0ic3Ryb2tlOnJnYigwLDAsMCk7c3Ryb2tlLXdpZHRoOjEuNSIvPgogPC9zdmc+"},{range:"25-30",path:"M25 0 L25 40 L10 45 L25 40 M25 35 L10 40 L25 35 M25 30 L17.5 32.5",imageData:"PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjkgMCAyNiA0NiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cGF0aCBkPSJNMjUgMCBMMjUgNDAgTDEwIDQ1IEwyNSA0MCBNMjUgMzUgTDEwIDQwIEwyNSAzNSBNMjUgMzAgTDE3LjUgMzIuNSIgc3R5bGU9InN0cm9rZTpyZ2IoMCwwLDApO3N0cm9rZS13aWR0aDoxLjUiLz4KIDwvc3ZnPg=="},{range:"30-35",path:"M25 0 L25 40 L10 45 L25 40 M25 35 L10 40 L25 35 M25 30 L10 35",imageData:"PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMHB4IiBoZWlnaHQ9IjIwcHgiIHZpZXdCb3g9IjkgMCAyNiA0NiI+CiAgPHBhdGggZD0iTTI1IDAgTDI1IDQwIEwxMCA0NSBMMjUgNDAgTTI1IDM1IEwxMCA0MCBMMjUgMzUgTTI1IDMwIEwxMCAzNSIgc3R5bGU9InN0cm9rZTpyZ2IoMCwwLDApO3N0cm9rZS13aWR0aDoxLjUiLz4KIDwvc3ZnPg=="},{range:"35-40",path:"M25 0 L25 40 L10 45 L25 40 M25 35 L10 40 L25 35 M25 30 L10 35 L25 30 M25 25 L17.5 27.5",imageData:"PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMHB4IiBoZWlnaHQ9IjIwcHgiIHZpZXdCb3g9IjkgMCAyNiA0NiI+CiAgPHBhdGggZD0iTTI1IDAgTDI1IDQwIEwxMCA0NSBMMjUgNDAgTTI1IDM1IEwxMCA0MCBMMjUgMzUgTTI1IDMwIEwxMCAzNSBMMjUgMzAgTTI1IDI1IEwxNy41IDI3LjUiIHN0eWxlPSJzdHJva2U6cmdiKDAsMCwwKTtzdHJva2Utd2lkdGg6MS41Ii8+CiA8L3N2Zz4="},{range:"40-45",path:"M25 0 L25 40 L10 45 L25 40 M25 35 L10 40 L25 35 M25 30 L10 35 L25 30 M25 25 L10 30",imageData:"PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMHB4IiBoZWlnaHQ9IjIwcHgiIHZpZXdCb3g9IjkgMCAyNiA0NiI+CiAgPHBhdGggZD0iTTI1IDAgTDI1IDQwIEwxMCA0NSBMMjUgNDAgTTI1IDM1IEwxMCA0MCBMMjUgMzUgTTI1IDMwIEwxMCAzNSBMMjUgMzAgTTI1IDI1IEwxMCAzMCIgc3R5bGU9InN0cm9rZTpyZ2IoMCwwLDApO3N0cm9rZS13aWR0aDoxLjUiLz4KIDwvc3ZnPg=="},{range:"45-50",path:"M25 0 L25 40 L10 45 L25 40 M25 35 L10 40 L25 35 M25 30 L10 35 L25 30 M25 25 L10 30 L25 25 M25 20 L17.5 22.5",imageData:"PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMHB4IiBoZWlnaHQ9IjIwcHgiIHZpZXdCb3g9IjkgMCAyNiA0NiI+CiAgPHBhdGggZD0iTTI1IDAgTDI1IDQwIEwxMCA0NSBMMjUgNDAgTTI1IDM1IEwxMCA0MCBMMjUgMzUgTTI1IDMwIEwxMCAzNSBMMjUgMzAgTTI1IDI1IEwxMCAzMCBMMjUgMjUgTTI1IDIwIEwxNy41IDIyLjUiIHN0eWxlPSJzdHJva2U6cmdiKDAsMCwwKTtzdHJva2Utd2lkdGg6MS41Ii8+CiA8L3N2Zz4="},{range:"50-55",path:"M25 0 L25 40 L10 40 L25 35",imageData:"PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMHB4IiBoZWlnaHQ9IjIwcHgiIHZpZXdCb3g9IjkgMCAyNiA0MSI+CiAgPHBhdGggZD0iTTI1IDAgTDI1IDQwIEwxMCA0MCBMMjUgMzUiIHN0eWxlPSJzdHJva2U6cmdiKDAsMCwwKTtzdHJva2Utd2lkdGg6MS41Ii8+CiA8L3N2Zz4="},{range:"55-60",path:"M25 0 L25 40 L10 40 L25 35 M25 30 L17.5 32.5",imageData:"PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMHB4IiBoZWlnaHQ9IjIwcHgiIHZpZXdCb3g9IjkgMCAyNiA0MSI+CiAgPHBhdGggZD0iTTI1IDAgTDI1IDQwIEwxMCA0MCBMMjUgMzUgTTI1IDMwIEwxNy41IDMyLjUiIHN0eWxlPSJzdHJva2U6cmdiKDAsMCwwKTtzdHJva2Utd2lkdGg6MS41Ii8+CiA8L3N2Zz4="},{range:"60-65",path:"M25 0 L25 40 L10 40 L25 35 M25 30 L10 35",imageData:"PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMnB4IiBoZWlnaHQ9IjMycHgiIHZpZXdCb3g9IjkgMCAyNiA0MSI+CiAgPHBhdGggZD0iTTI1IDAgTDI1IDQwIEwxMCA0MCBMMjUgMzUgTTI1IDMwIEwxMCAzNSIgc3R5bGU9InN0cm9rZTpyZ2IoMCwwLDApO3N0cm9rZS13aWR0aDoxLjUiLz4KIDwvc3ZnPg=="},{range:"65-70",path:"M25 0 L25 40 L10 40 L25 35 M25 30 L10 35 L25 30 M25 25 L17.5 27.5",imageData:"PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMnB4IiBoZWlnaHQ9IjMycHgiIHZpZXdCb3g9IjkgMCAyNiA0MSI+CiAgPHBhdGggZD0iTTI1IDAgTDI1IDQwIEwxMCA0MCBMMjUgMzUgTTI1IDMwIEwxMCAzNSBMMjUgMzAgTTI1IDI1IEwxNy41IDI3LjUiIHN0eWxlPSJzdHJva2U6cmdiKDAsMCwwKTtzdHJva2Utd2lkdGg6MS41Ii8+CiA8L3N2Zz4="},{range:"70-75",path:"M25 0 L25 40 L10 40 L25 35 M25 30 L10 35 L25 30 M25 25 L10 30",imageData:"PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMnB4IiBoZWlnaHQ9IjMycHgiIHZpZXdCb3g9IjkgMCAyNiA0MSI+CiAgPHBhdGggZD0iTTI1IDAgTDI1IDQwIEwxMCA0MCBMMjUgMzUgTTI1IDMwIEwxMCAzNSBMMjUgMzAgTTI1IDI1IEwxMCAzMCIgc3R5bGU9InN0cm9rZTpyZ2IoMCwwLDApO3N0cm9rZS13aWR0aDoxLjUiLz4KIDwvc3ZnPg=="},{range:"75-80",path:"M25 0 L25 40 L10 40 L25 35 M25 30 L10 35 L25 30 M25 25 L10 30 L25 25 M25 20 L17.5 22.5",imageData:"PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMnB4IiBoZWlnaHQ9IjMycHgiIHZpZXdCb3g9IjkgMCAyNiA0MSI+CiAgPHBhdGggZD0iTTI1IDAgTDI1IDQwIEwxMCA0MCBMMjUgMzUgTTI1IDMwIEwxMCAzNSBMMjUgMzAgTTI1IDI1IEwxMCAzMCBMMjUgMjUgTTI1IDIwIEwxNy41IDIyLjUiIHN0eWxlPSJzdHJva2U6cmdiKDAsMCwwKTtzdHJva2Utd2lkdGg6MS41Ii8+CiA8L3N2Zz4="},{range:"80-85",path:"M25 0 L25 40 L10 40 L25 35 M25 30 L10 35 L25 30 M25 25 L10 30 L25 25 M25 20 L10 25",imageData:"PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMnB4IiBoZWlnaHQ9IjMycHgiIHZpZXdCb3g9IjkgMCAyNiA0MSI+CiAgPHBhdGggZD0iTTI1IDAgTDI1IDQwIEwxMCA0MCBMMjUgMzUgTTI1IDMwIEwxMCAzNSBMMjUgMzAgTTI1IDI1IEwxMCAzMCBMMjUgMjUgTTI1IDIwIEwxMCAyNSIgc3R5bGU9InN0cm9rZTpyZ2IoMCwwLDApO3N0cm9rZS13aWR0aDoxLjUiLz4KIDwvc3ZnPg=="},{range:"85-90",path:"M25 0 L25 40 L10 40 L25 35 M25 30 L10 35 L25 30 M25 25 L10 30 L25 25 M25 20 L10 25 L25 20 M25 15 L17.5 17.5",imageData:"PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMnB4IiBoZWlnaHQ9IjMycHgiIHZpZXdCb3g9IjkgMCAyNiA0MSI+CiAgPHBhdGggZD0iTTI1IDAgTDI1IDQwIEwxMCA0MCBMMjUgMzUgTTI1IDMwIEwxMCAzNSBMMjUgMzAgTTI1IDI1IEwxMCAzMCBMMjUgMjUgTTI1IDIwIEwxMCAyNSBMMjUgMjAgTTI1IDE1IEwxNy41IDE3LjUiIHN0eWxlPSJzdHJva2U6cmdiKDAsMCwwKTtzdHJva2Utd2lkdGg6MS41Ii8+CiA8L3N2Zz4="},{range:"90-95",path:"M25 0 L25 40 L10 40 L25 35 M25 30 L10 35 L25 30 M25 25 L10 30 L25 25 M25 20 L10 25 L25 20 M25 15 L10 20",imageData:"PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMnB4IiBoZWlnaHQ9IjMycHgiIHZpZXdCb3g9IjkgMCAyNiA0MSI+CiAgPHBhdGggZD0iTTI1IDAgTDI1IDQwIEwxMCA0MCBMMjUgMzUgTTI1IDMwIEwxMCAzNSBMMjUgMzAgTTI1IDI1IEwxMCAzMCBMMjUgMjUgTTI1IDIwIEwxMCAyNSBMMjUgMjAgTTI1IDE1IEwxMCAyMCIgc3R5bGU9InN0cm9rZTpyZ2IoMCwwLDApO3N0cm9rZS13aWR0aDoxLjUiLz4KIDwvc3ZnPg=="},{range:"95-100",path:"M25 0 L25 40 L10 40 L25 35 M25 30 L10 35 L25 30 M25 25 L10 30 L25 25 M25 20 L10 25 L25 20 M25 15 L10 20 L25 15 M25 10 L17.5 12.5",imageData:"PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMnB4IiBoZWlnaHQ9IjMycHgiIHZpZXdCb3g9IjkgMCAyNiA0MSI+CiAgPHBhdGggZD0iTTI1IDAgTDI1IDQwIEwxMCA0MCBMMjUgMzUgTTI1IDMwIEwxMCAzNSBMMjUgMzAgTTI1IDI1IEwxMCAzMCBMMjUgMjUgTTI1IDIwIEwxMCAyNSBMMjUgMjAgTTI1IDE1IEwxMCAyMCBMMjUgMTUgTTI1IDEwIEwxNy41IDEyLjUiIHN0eWxlPSJzdHJva2U6cmdiKDAsMCwwKTtzdHJva2Utd2lkdGg6MS41Ii8+CiA8L3N2Zz4="},{range:"100-105",path:"M25 0 L25 40 L10 40 L25 35 L10 35 L25 30",imageData:"PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMnB4IiBoZWlnaHQ9IjMycHgiIHZpZXdCb3g9IjkgMCAyNiA0MSI+CiAgPHBhdGggZD0iTTI1IDAgTDI1IDQwIEwxMCA0MCBMMjUgMzUgTDEwIDM1IEwyNSAzMCIgc3R5bGU9InN0cm9rZTpyZ2IoMCwwLDApO3N0cm9rZS13aWR0aDoxLjUiLz4KIDwvc3ZnPg=="},{range:"105-110",path:"M25 0 L25 40 L10 40 L25 35 L10 35 L25 30 M25 25 L17.5 27.5",imageData:"PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGhlaWdodD0iMzJweCIgd2lkdGg9IjMycHgiIHZpZXdCb3g9IjkgMCAyNiA0MSI+CiAgPHBhdGggZD0iTTI1IDAgTDI1IDQwIEwxMCA0MCBMMjUgMzUgTDEwIDM1IEwyNSAzMCBNMjUgMjUgTDE3LjUgMjcuNSIgc3R5bGU9InN0cm9rZTpyZ2IoMCwwLDApO3N0cm9rZS13aWR0aDoxLjUiLz4KIDwvc3ZnPg=="},{range:"110-115",path:"M25 0 L25 40 L10 40 L25 35 L10 35 L25 30 M25 25 L10 30",imageData:"PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGhlaWdodD0iMzJweCIgd2lkdGg9IjMycHgiIHZpZXdCb3g9IjkgMCAyNiA0MSI+CiAgPHBhdGggZD0iTTI1IDAgTDI1IDQwIEwxMCA0MCBMMjUgMzUgTDEwIDM1IEwyNSAzMCBNMjUgMjUgTDEwIDMwIiBzdHlsZT0ic3Ryb2tlOnJnYigwLDAsMCk7c3Ryb2tlLXdpZHRoOjEuNSIvPgogPC9zdmc+"},{range:"115-120",path:"M25 0 L25 40 L10 40 L25 35 L10 35 L25 30 M25 25 L10 30 M25 25 M25 20 L17.5 22.5",imageData:"PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGhlaWdodD0iMzJweCIgd2lkdGg9IjMycHgiIHZpZXdCb3g9IjkgMCAyNiA0MSI+CiAgPHBhdGggZD0iTTI1IDAgTDI1IDQwIEwxMCA0MCBMMjUgMzUgTDEwIDM1IEwyNSAzMCBNMjUgMjUgTDEwIDMwIE0yNSAyNSBNMjUgMjAgTDE3LjUgMjIuNSIgc3R5bGU9InN0cm9rZTpyZ2IoMCwwLDApO3N0cm9rZS13aWR0aDoxLjUiLz4KIDwvc3ZnPg=="},{range:"120-125",path:"M25 0 L25 40 L10 40 L25 35 L10 35 L25 30 M25 25 L10 30 M25 25 M25 20 L10 25",imageData:"PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGhlaWdodD0iMzJweCIgd2lkdGg9IjMycHgiIHZpZXdCb3g9IjkgMCAyNiA0MSI+CiAgPHBhdGggZD0iTTI1IDAgTDI1IDQwIEwxMCA0MCBMMjUgMzUgTDEwIDM1IEwyNSAzMCBNMjUgMjUgTDEwIDMwIE0yNSAyNSBNMjUgMjAgTDEwIDI1IiBzdHlsZT0ic3Ryb2tlOnJnYigwLDAsMCk7c3Ryb2tlLXdpZHRoOjEuNSIvPgogPC9zdmc+"},{range:"125-130",path:"M25 0 L25 40 L10 40 L25 35 L10 35 L25 30 M25 25 L10 30 M25 25 M25 20 L10 25 M25 20 M25 15 L17.5 17.5",imageData:"PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGhlaWdodD0iMzJweCIgd2lkdGg9IjMycHgiIHZpZXdCb3g9IjkgMCAyNiA0MSI+CiAgPHBhdGggZD0iTTI1IDAgTDI1IDQwIEwxMCA0MCBMMjUgMzUgTDEwIDM1IEwyNSAzMCBNMjUgMjUgTDEwIDMwIE0yNSAyNSBNMjUgMjAgTDEwIDI1IE0yNSAyMCBNMjUgMTUgTDE3LjUgMTcuNSIgc3R5bGU9InN0cm9rZTpyZ2IoMCwwLDApO3N0cm9rZS13aWR0aDoxLjUiLz4KIDwvc3ZnPg=="},{range:"130-135",path:"M25 0 L25 40 L10 40 L25 35 L10 35 L25 30 M25 25 L10 30 M25 25 M25 20 L10 25 M25 20 M25 15 L10 20",imageData:"PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGhlaWdodD0iMzJweCIgd2lkdGg9IjMycHgiIHZpZXdCb3g9IjkgMCAyNiA0MSI+CiAgPHBhdGggZD0iTTI1IDAgTDI1IDQwIEwxMCA0MCBMMjUgMzUgTDEwIDM1IEwyNSAzMCBNMjUgMjUgTDEwIDMwIE0yNSAyNSBNMjUgMjAgTDEwIDI1IE0yNSAyMCBNMjUgMTUgTDEwIDIwIiBzdHlsZT0ic3Ryb2tlOnJnYigwLDAsMCk7c3Ryb2tlLXdpZHRoOjEuNSIvPgogPC9zdmc+"},{range:"135-140",path:"M25 0 L25 40 L10 40 L25 35 L10 35 L25 30 M25 25 L10 30 M25 25 M25 20 L10 25 M25 20 M25 15 L10 20 M25 15 M25 10 L17.5 12.5",imageData:"PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGhlaWdodD0iMzJweCIgd2lkdGg9IjMycHgiIHZpZXdCb3g9IjkgMCAyNiA0MSI+CiAgPHBhdGggZD0iTTI1IDAgTDI1IDQwIEwxMCA0MCBMMjUgMzUgTDEwIDM1IEwyNSAzMCBNMjUgMjUgTDEwIDMwIE0yNSAyNSBNMjUgMjAgTDEwIDI1IE0yNSAyMCBNMjUgMTUgTDEwIDIwIE0yNSAxNSBNMjUgMTAgTDE3LjUgMTIuNSIgc3R5bGU9InN0cm9rZTpyZ2IoMCwwLDApO3N0cm9rZS13aWR0aDoxLjUiLz4KIDwvc3ZnPg=="},{range:"140-145",path:"M25 0 L25 40 L10 40 L25 35 L10 35 L25 30 M25 25 L10 30 M25 25 M25 20 L10 25 M25 20 M25 15 L10 20 M25 15 M25 10 L17.5 12.5",imageData:"PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGhlaWdodD0iMzJweCIgd2lkdGg9IjMycHgiIHZpZXdCb3g9IjkgMCAyNiA0MSI+CiAgPHBhdGggZD0iTTI1IDAgTDI1IDQwIEwxMCA0MCBMMjUgMzUgTDEwIDM1IEwyNSAzMCBNMjUgMjUgTDEwIDMwIE0yNSAyNSBNMjUgMjAgTDEwIDI1IE0yNSAyMCBNMjUgMTUgTDEwIDIwIE0yNSAxNSBNMjUgMTAgTDEwIDE1IiBzdHlsZT0ic3Ryb2tlOnJnYigwLDAsMCk7c3Ryb2tlLXdpZHRoOjEuNSIvPgogPC9zdmc+"},{range:"145-150",path:"M25 0 L25 40 L10 40 L25 35 L10 35 L25 30 M25 25 L10 30 M25 25 M25 20 L10 25 M25 20 M25 15 L10 20 M25 15 M25 10 L17.5 12.5",imageData:"PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGhlaWdodD0iMzJweCIgd2lkdGg9IjMycHgiIHZpZXdCb3g9IjkgMCAyNiA0MSI+CiAgPHBhdGggZD0iTTI1IDAgTDI1IDQwIEwxMCA0MCBMMjUgMzUgTDEwIDM1IEwyNSAzMCBNMjUgMjUgTDEwIDMwIE0yNSAyNSBNMjUgMjAgTDEwIDI1IE0yNSAyMCBNMjUgMTUgTDEwIDIwIE0yNSAxNSBNMjUgMTAgTDEwIDE1IE0yNSAxMCBNMjUgNSBMMTcuNSA3LjUiIHN0eWxlPSJzdHJva2U6cmdiKDAsMCwwKTtzdHJva2Utd2lkdGg6MS41Ii8+CiA8L3N2Zz4="}],r=k.Z.fromJSON({imageData:"iVBORw0KGgoAAAANSUhEUgAAACgAAAApCAQAAADtq6NDAAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAAgY0hSTQAAeiYAAICEAAD6AAAAgOgAAHUwAADqYAAAOpgAABdwnLpRPAAAAAJiS0dEAP+Hj8y/AAAACXBIWXMAAA7DAAAOwwHHb6hkAAAEY0lEQVRIx5XXWWxWRRQH8N+d+31tUdGAVjGglYJABFEBY91jfDAg7piYaFTccA++uMQEFRcSXlATtxiXqMQt4G4iisYl0ai4sIQYtVFZ1KIFKdTS0l4f7vRCS5fPebozc+bM/2z/Mzcx0AgSiUxXnKfIdMn875FIhX53U2n/B/s+kKM4UINTjTBZImixxnrv+9a2iL6zEoUBXcoudrWj/OtHm3wt02lfU9Qao9OnHvIhgmww84MEl1qnxfNmGrqHxAizLdPpC6chGcAxKGGcL+30gOERf1BSpUqVslQSV8d5ReZFe8VQ9avufJn31cWwlJV7iafKStGOE/1qvfH9qUxxu8ydUdmuSKbGO8YUdT2inKLG69pM70tliktl5qIkCAJGmusDG7Vqsc0WjZa4UBlBiA5YZIcjYzB7qDtH5kaUJFLs7RGZTZ42W4PRRmtwvbdt1+wGiaS4drEtDttdZYIDNVuAclR3vA3+dI3qHqmVSy7U6Tv1MScCPvPR7nIpFlsdCy3FdTLPGhK92e2CUITjMJ9ocwKxnsZqc3O3JwMma3d6UVLnyVxB4aXemZqvPqLdpJhW3KVVbY4yYImPo6M5Urv50fj+0z/FG9YaEiENs8UtMfXUaTeTePNHlhXfA1UU+2lyD1Il3Gtt9+adfpNG7dNlpg2U/T3KYLZ2dUWFdTgp3/rQ4sK973qnInV5TIf40x3dhvrJPBiqyWUo4wAtLqhQYS71qK+QKOFRywmGK/kpikzV6WMKhh58vGWs4TIJNjiEYLIuP8Tt4/zmLyqk+AyrJSbF+Qq1DgqRUPMxyl+9q3IQhX/rMCJ6tEunriDs1oSyQZKlr9AkhT2ZIARbJfaJS1vtVbHB+Rgi0RK/y1q1BWsEEyLoz40xtGKcARPVWB1BTPO7f4LNtpkUl1aoMbViLyZo0GRjPD3BxnxjqXeLYlvhqYrzMMG3HoyJXa3JjfnGlbYYFlP7Jh3qKsKY4hQ7TY0nG+xwRL61n63mxHtqNHosigyMLmClNwvuecFnOZB88nNBDzNkzhxEZaKMBVoKapggMzvHHXBEpNSSFAvtcFRsVn0bW8LlMmcXs+c0Kne3gRR32+zg4uXwjC6zit6Wt4a8LXVfcp/MtQXHn2ynGbuCmb8GvvFeJLEE82ReU9/n6+dkq2x3buG9Wn94smcgAw631RPR7BTH+kbmHReZoEpOdEe7zWqZl40s0JWs9Hmv7hjBHqPDwsjGKVJnWWqjbdZp1KhJi0aPmxYZsIRhlttgeF+Jlke41QcOQKoqilSb6HJzSvNG3G/UoWnxwsmt+sVaYwd63dRbqdnMyCPVeyRPvpYgdavM22oGKoMUVRbJfOWMwidJ8Zzb1UvmWK/VVUXzHaTjjrVYh1897HT7xxYEVUaa5SWb/WO+YUWa9SrwvigzM8YlzlYv2GSdVCYxxlBtVnnFq5olwp5/BEk/OLsf5LUmG2+inRJdVvjZ97ZH9/zP34ug1O91pf4p+D+JYBpvrKxfbwAAACV0RVh0ZGF0ZTpjcmVhdGUAMjAxNC0xMS0xMFQwMzoxMjowOS0wNTowMB9ViV0AAAAldEVYdGRhdGU6bW9kaWZ5ADIwMTQtMTEtMTBUMDM6MTI6MDktMDU6MDBuCDHhAAAAAElFTkSuQmCC",height:20,width:20,type:"esriPMS",angle:0}),n=e.map(((n,i)=>{let a;if(i!==e.length-1)if(0===i)a={minValue:n,maxValue:e[i+1],symbol:r};else{const r=k.Z.fromJSON({type:"esriPMS",imageData:t[i].imageData,contentType:"image/svg+xml",height:32,width:32,angle:0});a={minValue:n,maxValue:e[i+1],symbol:r}}return new M.Z(a)}));return{defaultSymbol:r,classBreakInfos:n}}_getClassBreaks(e,t){return t.map(((t,r)=>new M.Z({minValue:e[r],maxValue:e[r+1],symbol:this._getDefaultSymbol(new i.Z(t))})))}};(0,n._)([(0,d.Cb)({type:String,json:{write:!0}})],B.prototype,"attributeField",void 0),(0,n._)([(0,d.Cb)({type:z.apiValues,json:{type:z.jsonValues,read:{reader:z.read},write:{writer:z.write}}})],B.prototype,"flowRepresentation",void 0),(0,n._)([(0,d.Cb)({type:["geographic","arithmetic"],json:{write:!0}})],B.prototype,"rotationType",void 0),(0,n._)([(0,d.Cb)({type:_.apiValues,json:{type:_.jsonValues,read:{reader:_.read},write:{writer:_.write}}})],B.prototype,"style",void 0),(0,n._)([(0,d.Cb)({json:{write:!0}})],B.prototype,"symbolTileSize",void 0),(0,n._)([(0,d.Cb)({type:y.NL.apiValues,json:{type:y.NL.jsonValues,write:{writer:y.NL.write}}})],B.prototype,"inputUnit",void 0),(0,n._)([(0,p.r)("inputUnit")],B.prototype,"readInputUnit",null),(0,n._)([(0,d.Cb)({type:y.NL.apiValues,json:{type:y.NL.jsonValues,read:{reader:y.NL.read},write:{writer:y.NL.write}}})],B.prototype,"outputUnit",void 0),(0,n._)([(0,p.r)("outputUnit")],B.prototype,"readOutputUnit",null),(0,n._)([(0,m.J)({vectorField:"vector-field"})],B.prototype,"type",void 0),(0,n._)([(0,d.Cb)({type:f.Z})],B.prototype,"styleRenderer",null),(0,n._)([(0,d.Cb)({type:b.Z})],B.prototype,"sizeVariables",null),(0,n._)([(0,d.Cb)({type:I.Z})],B.prototype,"rotationVariables",null),B=Z=(0,n._)([(0,g.j)("esri.renderers.VectorFieldRenderer")],B);const E=B},67058:(e,t,r)=>{r.d(t,{FI:()=>b,In:()=>I,Ob:()=>w,U0:()=>M,YD:()=>v,ol:()=>L});var n=r(22303),i=(r(30030),r(70586)),a=(r(67900),r(1231)),s=r(48526),o=(r(24705),r(69237)),l=(r(32984),r(93010)),c=r(44262),u=(r(10278),r(622),r(1515)),d=r(60849),m=r(81571),p=r(28092),g=(r(73622),r(16050),r(3943));const h=u.Z.fromJSON({type:"multipart",colorRamps:[{fromColor:[0,0,255],toColor:[0,255,255]},{fromColor:[0,255,255],toColor:[255,255,0]},{fromColor:[255,255,0],toColor:[255,0,0]}]}),y=u.Z.fromJSON(l.Ms[0]),f=new Set(["scientific","standard-time","vector-uv","vector-magdir","vector-u","vector-v","vector-magnitude","vector-direction"]);function w(e,t){const{attributeTable:r,colormap:a}=e;if(T(e)){const t=function(e){if(!T(e))return null;let t;if((0,i.pC)(e.statistics)&&e.statistics.length&&("vector-magdir"===e.dataType||"vector-uv"===e.dataType)){const{minMagnitude:r,maxMagnitude:n}=function(e,t){let r,n;if("vector-magdir"===e)r=t[0].min,n=t[0].max;else{const e=t[0].min,i=t[0].max,a=t[1].min,s=t[1].max;r=0,n=Math.max(Math.abs(e),Math.abs(a),Math.abs(i),Math.abs(s))}return{minMagnitude:r,maxMagnitude:n}}(e.dataType,e.statistics);t=[{type:"size",field:"Magnitude",minSize:10,maxSize:40,minDataValue:r,maxDataValue:n}]}const r=(0,i.pC)(e.multidimensionalInfo)?S.get(e.multidimensionalInfo.variables[0].unit):null,n=new g.Z({visualVariables:t,inputUnit:r,rotationType:"geographic"});return n.visualVariables=[...n.sizeVariables,...n.rotationVariables],n}(e);if((0,i.pC)(t))return t}if((0,i.pC)(a)){const t=function(e){if(!N(e))return null;let t;const{attributeTable:r,colormap:n}=e;if((0,i.pC)(r)){const e=C(r,"value"),n=A(r,null,!0);"string"===n.type&&(t={},r.features.forEach((r=>{const i=r.attributes;t[i[e.name]]=n?i[n.name]:i[e.name]})))}return p.Z.createFromColormap((0,i.Wg)(n),t)}(e);if((0,i.pC)(t))return t}if((0,i.pC)(r)){const t=function(e,t,r,a){if(!D(e,t))return null;const{attributeTable:s,statistics:u}=e,d=A(s,t),p=C(s,"red"),g=C(s,"green"),h=C(s,"blue"),f=new o.Z,w=[],M=new Set,I=!!(p&&g&&h);if((0,i.pC)(s))s.features.forEach((e=>{const t=e.attributes[d.name];if(!M.has(e.attributes[d.name])&&null!=t){M.add(t);const r=I&&("single"===p.type||"double"===p.type)&&("single"===g.type||"double"===g.type)&&("single"===h.type||"double"===h.type)&&!s.features.some((e=>e.attributes[p.name]>1||e.attributes[g.name]>1||e.attributes[h.name]>1)),i=r?255:1;w.push(new c.Z({value:e.attributes[d.name],label:e.attributes[d.name]+"",symbol:{type:"simple-fill",style:"solid",outline:null,color:new n.Z(I?[e.attributes[p.name]*i,e.attributes[g.name]*i,e.attributes[h.name]*i,1]:[0,0,0,0])}}))}}));else if(u?.[0])for(let e=u[0].min;e<=u[0].max;e++)w.push(new c.Z({value:e,label:e.toString(),symbol:{type:"simple-fill",style:"solid",outline:null,color:new n.Z([0,0,0,0])}}));if(w.sort(((e,t)=>e.value&&"string"==typeof e.value.valueOf()?0:e.value>t.value?1:-1)),!I){const e=(0,l.Jw)(y,{numColors:w.length});w.forEach(((t,r)=>t.symbol.color=new n.Z(e[r].slice(1,4)))),f.colorRamp=y}return new m.Z({field:d.name,uniqueValueInfos:w,authoringInfo:f})}(e);if((0,i.pC)(t))return t}return function(e,t){e=b(e,t?.variableName);const{bandCount:r}=e;let{bandIds:n,stretchType:a}=t||{};n?.some((e=>e>=r))&&(n=null);let s=(0,i.Wg)(e.statistics),o=(0,i.Wg)(e.histograms);r>1?(n=n?.length?n:v(e),s=null==s?null:n?.map((e=>s[e])),o=null==o?null:n?.map((e=>o[e]))):n=[0],null==a&&(a=function(e){let t="percent-clip";const{pixelType:r,dataType:n,histograms:a,statistics:s,multidimensionalInfo:o}=e,l=f.has(n)||"generic"===n&&(0,i.pC)(o);return"u8"!==r||"processed"!==n&&(0,i.pC)(a)&&(0,i.pC)(s)?"u8"===r||"elevation"===n||l?t="min-max":(0,i.pC)(a)?t="percent-clip":(0,i.pC)(s)&&(t="min-max"):t="none",t}(e));let l=!1;switch(a){case"none":l=!1;break;case"percent-clip":l=!o?.length;break;default:l=!s?.length}const{dataType:c}=e,u=1===n?.length&&f.has(c)?h:null,m=new d.Z({stretchType:a,dynamicRangeAdjustment:l,colorRamp:u,outputMin:0,outputMax:255,gamma:1===n?.length?[1]:[1,1,1],useGamma:!1});return"percent-clip"===a?m.maxPercent=m.minPercent=.25:"standard-deviation"===a&&(m.numberOfStandardDeviations=2),l||!(0,i.pC)(e.multidimensionalInfo)&&!t?.includeStatisticsInStretch||("percent-clip"===a?m.histograms=o:"min-max"!==a&&"standard-deviation"!==a||(m.statistics=s)),m}(e,t)}function M(e,t=!1){const r=["raster-stretch"];return N(e)&&r.push("raster-colormap"),D(e)&&r.push("unique-value"),function(e,t=!1){const{attributeTable:r,bandCount:n}=e;return 1===n&&(!t||(0,i.pC)(r)||(0,i.pC)(e.histograms))}(e,t)&&r.push("class-breaks"),function(e){const{bandCount:t,dataType:r,pixelType:n}=e;return"elevation"===r||"generic"===r&&1===t&&("s16"===n||"s32"===n||"f32"===n||"f64"===n)}(e)&&r.push("raster-shaded-relief"),T(e)&&r.push("vector-field"),function(e){const{dataType:t}=e;return"vector-uv"===t||"vector-magdir"===t}(e)&&r.push("flow"),r}function I(e,t,r){const n=["nearest","bilinear","cubic","majority"].find((e=>e===r?.toLowerCase()));return"Map"===t?n??"bilinear":"standard-time"===e.dataType?n??"nearest":"thematic"===e.dataType||e.attributeTable||e.colormap?"nearest"===n||"majority"===n?n:"nearest":n??"bilinear"}function b(e,t){if(!t)return e;let r=(0,i.Wg)(e.statistics),n=(0,i.Wg)(e.histograms);const{multidimensionalInfo:a}=e;if(t&&(0,i.pC)(a)){const e=a.variables.find((e=>e.name===t));if(e){const{statistics:t,histograms:i}=e;t?.length&&(r=t),i?.length&&(n=i)}}return s.Z.fromJSON({...e.toJSON(),statistics:r,histograms:n})}function v(e){const t=e.bandCount;if(1===t)return null;if(2===t)return[0];const r=e.keyProperties&&e.keyProperties.BandProperties;let n;if(r&&r.length===t){const{red:e,green:t,blue:i,nir:a}=function(e){const t={};for(let r=0;r<e.length;r++){const n=e[r],i=n.BandName?.toLowerCase();if("red"===i)t.red=r;else if("green"===i)t.green=r;else if("blue"===i)t.blue=r;else if("nearinfrared"===i||"nearinfrared_1"===i||"nir"===i)t.nir=r;else if(n.WavelengthMax&&n.WavelengthMin){const e=n.WavelengthMin,i=n.WavelengthMax;null==t.blue&&e>=410&&e<=480&&i>=480&&i<=540?t.blue=r:null==t.green&&e>=490&&e<=560&&i>=560&&i<=610?t.green=r:null==t.red&&e>=595&&e<=670&&i>=660&&i<=730?t.red=r:null==t.nir&&e>=700&&e<=860&&i>=800&&i<=950&&(t.nir=r)}}return t}(r);null!=e&&null!=t&&null!=i?n=[e,t,i]:null!=a&&null!=e&&null!=t&&(n=[a,e,t])}return!n&&t>=3&&(n=[0,1,2]),n}function A(e,t,r){let n;return(0,i.pC)(e)?(n=t?e.fields.find((e=>t.toLowerCase()===e.name.toLowerCase())):function(e){let t;for(let r=0;r<e.length;r++){const n=e[r].name.toLowerCase();if("string"===e[r].type){if(n.startsWith("class")){t=e[r];break}null==t&&(n.endsWith("name")||n.endsWith("type"))&&(t=e[r])}}return t}(e.fields),n||(r||(n=e.fields.find((e=>"string"===e.type))),n||(n=C(e,"value")))):n=new a.Z({name:"value"}),n}function C(e,t){return(0,i.Wi)(e)?null:e.fields.find((e=>e.name.toLowerCase()===t))}function D(e,t){const{attributeTable:r,bandCount:n}=e;return!((!(0,i.Wi)(r)||!function(e){return["u8","s8"].includes(e.pixelType)&&null!=e.statistics?.[0]?.min&&null!=e.statistics[0]?.max&&1===e.bandCount}(e))&&((0,i.Wi)(r)||n>1||t&&null==r.fields.find((e=>e.name.toLowerCase()===t.toLowerCase()))))}function N(e){const{bandCount:t,colormap:r}=e;return(0,i.pC)(r)&&r.length>0&&1===t}function T(e){const{dataType:t}=e;return"vector-uv"===t||"vector-magdir"===t}const S=new Map([["m/s","meter-per-second"],["km/h","kilometer-per-hour"],["knots","knots"],["ft/s","feet-per-second"],["mph","mile-per-hour"]]);function j(e){return{color:e.symbolLayers[0].material?.color,type:"esriSFS",style:"esriSFSSolid"}}function L(e){if("uniqueValue"===e.type){const t=e.uniqueValueInfos,r=t?.[0].symbol;return r?.symbolLayers?.length&&(e.uniqueValueInfos=t?.map((e=>({value:e.value,label:e.label,symbol:e.symbol?j(e.symbol):null})))),e}if("classBreaks"===e.type){const t=e.classBreakInfos;return t[0].symbol?.symbolLayers?.length&&(e.classBreakInfos=t.map((e=>({classMinValue:e.classMinValue,classMaxValue:e.classMaxValue,label:e.label,symbol:e.symbol?j(e.symbol):null})))),e}return e}},51706:(e,t,r)=>{var n,i;function a(e){return e&&"esri.renderers.visualVariables.SizeVariable"===e.declaredClass}function s(e){return null!=e&&!isNaN(e)&&isFinite(e)}function o(e){return e.valueExpression?n.Expression:e.field&&"string"==typeof e.field?n.Field:n.Unknown}function l(e,t){const r=t||o(e),a=e.valueUnit||"unknown";return r===n.Unknown?i.Constant:e.stops?i.Stops:null!=e.minSize&&null!=e.maxSize&&null!=e.minDataValue&&null!=e.maxDataValue?i.ClampedLinear:"unknown"===a?null!=e.minSize&&null!=e.minDataValue?e.minSize&&e.minDataValue?i.Proportional:i.Additive:i.Identity:i.RealWorldSize}r.d(t,{PS:()=>o,QW:()=>l,RY:()=>n,hL:()=>i,iY:()=>a,qh:()=>s}),function(e){e.Unknown="unknown",e.Expression="expression",e.Field="field"}(n||(n={})),function(e){e.Unknown="unknown",e.Stops="stops",e.ClampedLinear="clamped-linear",e.Proportional="proportional",e.Additive="additive",e.Constant="constant",e.Identity="identity",e.RealWorldSize="real-world-size"}(i||(i={}))},26059:(e,t,r)=>{r.d(t,{N:()=>m,g:()=>d});var n=r(3172),i=r(6570),a=r(94139),s=r(82971),o=r(48526),l=r(11282),c=r(74889);async function u(e,t,r){const u=(0,l.en)(e),{renderingRule:d,sourceJSON:m}=t||{},p=d?JSON.stringify(d.rasterFunctionDefinition||d):null,g=(0,l.cv)({...u.query,renderingRule:p,f:"json"}),h=(0,l.lA)(g,r);e=u.path;const y=m||await(0,n.default)(e,h).then((e=>e.data)),f=y.hasRasterAttributeTable?(0,n.default)(`${e}/rasterAttributeTable`,h):null,w=y.hasColormap?(0,n.default)(`${e}/colormap`,h):null,M=y.hasHistograms?(0,n.default)(`${e}/histograms`,h):null,I=y.currentVersion>=10.3?(0,n.default)(`${e}/keyProperties`,h):null,b=y.hasMultidimensions?(0,n.default)(`${e}/multidimensionalInfo`,h):null,v=await Promise.allSettled([f,w,M,I,b]);let A=null;if(y.minValues&&y.minValues.length===y.bandCount){A=[];for(let e=0;e<y.minValues.length;e++)A.push({min:y.minValues[e],max:y.maxValues[e],avg:y.meanValues[e],stddev:y.stdvValues[e]})}const C=i.Z.fromJSON(y.extent),D=Math.ceil(C.width/y.pixelSizeX-.1),N=Math.ceil(C.height/y.pixelSizeY-.1),T=s.Z.fromJSON(y.spatialReference||y.extent.spatialReference),S="fulfilled"===v[0].status&&v[0].value?c.Z.fromJSON(v[0].value.data):null,j="fulfilled"===v[1].status?v[1].value?.data.colormap:null,L="fulfilled"===v[2].status?v[2].value?.data.histograms:null,Z="fulfilled"===v[3].status?v[3].value?.data??{}:{},x="fulfilled"===v[4].status?v[4].value?.data.multidimensionalInfo:null;x?.variables?.length&&x.variables.forEach((e=>{e.statistics?.length&&e.statistics.forEach((e=>{e.avg=e.mean,e.stddev=e.standardDeviation}))}));const{defaultVariable:k,serviceDataType:U}=y;k&&k!==Z.DefaultVariable&&(Z.DefaultVariable=k),U&&U.includes("esriImageServiceDataTypeVector")&&!U.includes(Z.DataType)&&(Z.DataType=U.replace("esriImageServiceDataType",""));let _=y.noDataValue;return y.noDataValues?.length&&y.noDataValues.some((e=>e!==_))&&(_=y.noDataValues),new o.Z({width:D,height:N,bandCount:y.bandCount,extent:i.Z.fromJSON(y.extent),spatialReference:T,pixelSize:new a.Z({x:y.pixelSizeX,y:y.pixelSizeY,spatialReference:T}),pixelType:y.pixelType.toLowerCase(),statistics:A,attributeTable:S,colormap:j,histograms:L,keyProperties:Z,noDataValue:_,multidimensionalInfo:x})}function d(e,t,r){return u(e,{sourceJSON:t},r)}function m(e,t,r){return u(e,{renderingRule:t},r)}},74889:(e,t,r)=>{r.d(t,{Z:()=>I});var n,i=r(43697),a=r(66577),s=r(38171),o=r(35454),l=r(96674),c=r(22974),u=r(70586),d=r(5600),m=(r(75215),r(71715)),p=r(52011),g=r(30556),h=r(82971),y=r(33955),f=r(1231);const w=new o.X({esriGeometryPoint:"point",esriGeometryMultipoint:"multipoint",esriGeometryPolyline:"polyline",esriGeometryPolygon:"polygon",esriGeometryEnvelope:"extent",mesh:"mesh","":null});let M=n=class extends l.wq{constructor(e){super(e),this.displayFieldName=null,this.exceededTransferLimit=!1,this.features=[],this.fields=null,this.geometryType=null,this.hasM=!1,this.hasZ=!1,this.queryGeometry=null,this.spatialReference=null}readFeatures(e,t){const r=h.Z.fromJSON(t.spatialReference),n=[];for(let t=0;t<e.length;t++){const i=e[t],a=s.Z.fromJSON(i),o=i.geometry&&i.geometry.spatialReference;(0,u.pC)(a.geometry)&&!o&&(a.geometry.spatialReference=r);const l=i.aggregateGeometries,c=a.aggregateGeometries;if(l&&(0,u.pC)(c))for(const e in c){const t=c[e],n=l[e]?.spatialReference;(0,u.pC)(t)&&!n&&(t.spatialReference=r)}n.push(a)}return n}writeGeometryType(e,t,r,n){if(e)return void w.write(e,t,r,n);const{features:i}=this;if(i)for(const e of i)if(e&&(0,u.pC)(e.geometry))return void w.write(e.geometry.type,t,r,n)}readQueryGeometry(e,t){if(!e)return null;const r=!!e.spatialReference,n=(0,y.im)(e);return n&&!r&&t.spatialReference&&(n.spatialReference=h.Z.fromJSON(t.spatialReference)),n}writeSpatialReference(e,t){if(e)return void(t.spatialReference=e.toJSON());const{features:r}=this;if(r)for(const e of r)if(e&&(0,u.pC)(e.geometry)&&e.geometry.spatialReference)return void(t.spatialReference=e.geometry.spatialReference.toJSON())}clone(){return new n(this.cloneProperties())}cloneProperties(){return(0,c.d9)({displayFieldName:this.displayFieldName,exceededTransferLimit:this.exceededTransferLimit,features:this.features,fields:this.fields,geometryType:this.geometryType,hasM:this.hasM,hasZ:this.hasZ,queryGeometry:this.queryGeometry,spatialReference:this.spatialReference,transform:this.transform})}toJSON(e){const t=this.write();if(t.features&&Array.isArray(e)&&e.length>0)for(let r=0;r<t.features.length;r++){const n=t.features[r];if(n.geometry){const t=e&&e[r];n.geometry=t&&t.toJSON()||n.geometry}}return t}quantize(e){const{scale:[t,r],translate:[n,i]}=e,a=this.features,s=this._getQuantizationFunction(this.geometryType,(e=>Math.round((e-n)/t)),(e=>Math.round((i-e)/r)));for(let e=0,t=a.length;e<t;e++)s?.((0,u.Wg)(a[e].geometry))||(a.splice(e,1),e--,t--);return this.transform=e,this}unquantize(){const{geometryType:e,features:t,transform:r}=this;if(!r)return this;const{translate:[n,i],scale:[a,s]}=r,o=this._getHydrationFunction(e,(e=>e*a+n),(e=>i-e*s));for(const{geometry:e}of t)(0,u.pC)(e)&&o&&o(e);return this.transform=null,this}_quantizePoints(e,t,r){let n,i;const a=[];for(let s=0,o=e.length;s<o;s++){const o=e[s];if(s>0){const e=t(o[0]),s=r(o[1]);e===n&&s===i||(a.push([e-n,s-i]),n=e,i=s)}else n=t(o[0]),i=r(o[1]),a.push([n,i])}return a.length>0?a:null}_getQuantizationFunction(e,t,r){return"point"===e?e=>(e.x=t(e.x),e.y=r(e.y),e):"polyline"===e||"polygon"===e?e=>{const n=(0,y.oU)(e)?e.rings:e.paths,i=[];for(let e=0,a=n.length;e<a;e++){const a=n[e],s=this._quantizePoints(a,t,r);s&&i.push(s)}return i.length>0?((0,y.oU)(e)?e.rings=i:e.paths=i,e):null}:"multipoint"===e?e=>{const n=this._quantizePoints(e.points,t,r);return n&&n.length>0?(e.points=n,e):null}:"extent"===e?e=>e:null}_getHydrationFunction(e,t,r){return"point"===e?e=>{e.x=t(e.x),e.y=r(e.y)}:"polyline"===e||"polygon"===e?e=>{const n=(0,y.oU)(e)?e.rings:e.paths;let i,a;for(let e=0,s=n.length;e<s;e++){const s=n[e];for(let e=0,n=s.length;e<n;e++){const n=s[e];e>0?(i+=n[0],a+=n[1]):(i=n[0],a=n[1]),n[0]=t(i),n[1]=r(a)}}}:"extent"===e?e=>{e.xmin=t(e.xmin),e.ymin=r(e.ymin),e.xmax=t(e.xmax),e.ymax=r(e.ymax)}:"multipoint"===e?e=>{const n=e.points;let i,a;for(let e=0,s=n.length;e<s;e++){const s=n[e];e>0?(i+=s[0],a+=s[1]):(i=s[0],a=s[1]),s[0]=t(i),s[1]=r(a)}}:null}};(0,i._)([(0,d.Cb)({type:String,json:{write:!0}})],M.prototype,"displayFieldName",void 0),(0,i._)([(0,d.Cb)({type:Boolean,json:{write:{overridePolicy:e=>({enabled:e})}}})],M.prototype,"exceededTransferLimit",void 0),(0,i._)([(0,d.Cb)({type:[s.Z],json:{write:!0}})],M.prototype,"features",void 0),(0,i._)([(0,m.r)("features")],M.prototype,"readFeatures",null),(0,i._)([(0,d.Cb)({type:[f.Z],json:{write:!0}})],M.prototype,"fields",void 0),(0,i._)([(0,d.Cb)({type:["point","multipoint","polyline","polygon","extent","mesh"],json:{read:{reader:w.read}}})],M.prototype,"geometryType",void 0),(0,i._)([(0,g.c)("geometryType")],M.prototype,"writeGeometryType",null),(0,i._)([(0,d.Cb)({type:Boolean,json:{write:{overridePolicy:e=>({enabled:e})}}})],M.prototype,"hasM",void 0),(0,i._)([(0,d.Cb)({type:Boolean,json:{write:{overridePolicy:e=>({enabled:e})}}})],M.prototype,"hasZ",void 0),(0,i._)([(0,d.Cb)({types:a.qM,json:{write:!0}})],M.prototype,"queryGeometry",void 0),(0,i._)([(0,m.r)("queryGeometry")],M.prototype,"readQueryGeometry",null),(0,i._)([(0,d.Cb)({type:h.Z,json:{write:!0}})],M.prototype,"spatialReference",void 0),(0,i._)([(0,g.c)("spatialReference")],M.prototype,"writeSpatialReference",null),(0,i._)([(0,d.Cb)({json:{write:!0}})],M.prototype,"transform",void 0),M=n=(0,i._)([(0,p.j)("esri.rest.support.FeatureSet")],M),M.prototype.toJSON.isDefaultToJSON=!0;const I=M},32163:(e,t,r)=>{r.d(t,{eZ:()=>d});var n=r(51773),i=r(35671),a=r(84649),s=(r(63801),r(48074),r(38745),r(9190)),o=(r(10214),r(71423),r(44951),r(422)),l=r(63061);const c=["oid","global-id"],u=["oid","global-id","guid"];function d({displayField:e,editFieldsInfo:t,fields:r,objectIdField:l,title:c},u){if(!r)return null;const d=function({editFieldsInfo:e,fields:t,objectIdField:r},n){return function(e,t){const r=e;return t&&(e=e.filter((e=>!t.includes(e.type)))),e===r&&(e=e.slice()),e.sort(g),e}(t??[],n?.ignoreFieldTypes||M).map((t=>new o.Z({fieldName:t.name,isEditable:y(t,e),label:t.alias,format:f(t),visible:p(t,{editFieldsInfo:e,objectIdField:r,visibleFieldNames:n?.visibleFieldNames})})))}({editFieldsInfo:t,fields:r,objectIdField:l},u);if(!d.length)return null;const m=function(e){const t=(0,i.O5)(e),{titleBase:r}=e;return t?`${r}: {${t.trim()}}`:r??""}({titleBase:c,fields:r,displayField:e}),h=[new s.Z,new a.Z];return new n.Z({title:m,content:h,fieldInfos:d})}const m=[/^fnode_$/i,/^tnode_$/i,/^lpoly_$/i,/^rpoly_$/i,/^poly_$/i,/^subclass$/i,/^subclass_$/i,/^rings_ok$/i,/^rings_nok$/i,/shape/i,/perimeter/i,/objectid/i,/_i$/i],p=(e,{editFieldsInfo:t,objectIdField:r,visibleFieldNames:n})=>n?n.has(e.name):!(h(e.name,t)||r&&e.name===r||c.includes(e.type)||m.some((t=>t.test(e.name))));function g(e,t){return"oid"===e.type?-1:"oid"===t.type?1:w(e)?-1:w(t)?1:(e.alias||e.name).toLocaleLowerCase().localeCompare((t.alias||t.name).toLocaleLowerCase())}function h(e,t){if(!e||!t)return!1;const{creationDateField:r,creatorField:n,editDateField:i,editorField:a}=t;return[r&&r.toLowerCase(),n&&n.toLowerCase(),i&&i.toLowerCase(),a&&a.toLowerCase()].includes(e.toLowerCase())}function y(e,t){return e.editable&&!u.includes(e.type)&&!h(e.name,t)}function f(e){switch(e.type){case"small-integer":case"integer":case"single":return new l.Z({digitSeparator:!0,places:0});case"double":return new l.Z({digitSeparator:!0,places:2});case"date":return new l.Z({dateFormat:"long-month-day-year"});default:return"string"===e.type&&(0,i.Ec)(e.name)?new l.Z({digitSeparator:!0,places:0}):null}}function w(e){return"name"===(e.name&&e.name.toLowerCase())||"name"===(e.alias&&e.alias.toLowerCase())}const M=["geometry","blob","raster","guid","xml"]}}]);