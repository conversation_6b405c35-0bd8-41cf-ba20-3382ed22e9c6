package org.thingsboard.server.dao.msg;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.dao.util.RedisUtil;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

/**
 * 锦州短信发送
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2023-11-09
 */
@Service
@Slf4j
public class JinzhouMsgSendService {

    public IstarResponse sendMsg(String phone, String content) {
        String url = "http://47.96.169.216:8513/sms/Api/ReturnJson/Send.do";
        HttpPost post = new HttpPost(url);

        //供水量
        List list = new ArrayList();
        NameValuePair nameValuePair = new BasicNameValuePair("SpCode", "910074");
        list.add(nameValuePair);
        nameValuePair = new BasicNameValuePair("LoginName", "泉林三网通知");
        list.add(nameValuePair);
        nameValuePair = new BasicNameValuePair("Password", "@J1Pl7tGCm");
        list.add(nameValuePair);
        nameValuePair = new BasicNameValuePair("MessageContent", "【义县自来水】" + content);
        list.add(nameValuePair);
        nameValuePair = new BasicNameValuePair("UserNumber", phone);
        list.add(nameValuePair);
        String serialNumber = RedisUtil.nextId(DataConstants.REDIS_KEY.YIXIAN_MSG, "");
        while (serialNumber.length() < 20) {
            serialNumber = serialNumber + "0";
        }
        nameValuePair = new BasicNameValuePair("SerialNumber", serialNumber);
        list.add(nameValuePair);
        HttpEntity httpEntity = new UrlEncodedFormEntity(list, StandardCharsets.UTF_8);
        post.setHeader("Content-Type", "application/x-www-form-urlencoded");
        post.setEntity(httpEntity);
        CloseableHttpResponse execute = null;
        try {
            CloseableHttpClient httpClient = HttpClients.createDefault();
            execute = httpClient.execute(post);
        } catch (IOException e) {
            e.printStackTrace();
        }
        HttpEntity entity = execute.getEntity();

        String s = null;
        try {
            s = EntityUtils.toString(entity);
            JSONObject object = JSONObject.parseObject(s);
            if (object.getString("result").equals("0")) {
                return IstarResponse.ok(object.getString("description"));
            } else {
                return IstarResponse.error(object.getString("description"));
            }
        } catch (IOException e) {
            log.error("义县短信发送失败：{}", s);
            e.printStackTrace();
            return IstarResponse.error(s);
        }
    }
}
