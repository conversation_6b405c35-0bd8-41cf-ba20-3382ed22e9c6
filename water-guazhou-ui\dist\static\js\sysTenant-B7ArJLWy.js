import{_ as S}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as T}from"./CardTable-rdWOL4_6.js";import{_ as k}from"./CardSearch-CB_HNR-Q.js";import{_ as I}from"./tenantManager.vue_vue_type_style_index_0_lang-COPQr3KK.js";import{d as D,c as b,r as u,ek as L,b as p,c8 as q,S as F,c7 as B,o as P,c9 as z,g as M,n as N,q as g,i as m,C as V}from"./index-r0dFAfgr.js";import{v as A,p as W}from"./formValidate-U0WTqY4Y.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";import"./index-DyRO7tCT.js";import"./index-qoWsDjz-.js";import"./index-B69llYYW.js";import"./useAmap-D6DJ1T90.js";import"./index-BI1vGJja.js";import"./URLHelper-B9aplt5w.js";import"./DateFormatter-Bm9a68Ax.js";const j=y=>[{fields:[{type:"input",label:"企业名称",field:"title",rules:[{required:!0,message:"请填写企业名称"}]},{type:"input",label:"邮箱",field:"email",rules:[{type:"email",message:"请输入正确邮箱地址",trigger:"blur"}]},{type:"select",label:"应用",field:"appTypeId",options:y,rules:[{required:!0,message:"请选择应用",trigger:"change"}]},{type:"input",label:"联系手机",field:"phone",rules:[{validator:A,trigger:"blur"}]},{type:"input",label:"国家",field:"country",rules:[{max:20,message:"输入不可超过20位",trigger:"blur"}]},{type:"input",label:"省",field:"state",rules:[{max:20,message:"输入不可超过20位",trigger:"blur"}]},{type:"input",label:"城市",field:"city",rules:[{max:20,message:"输入不可超过20位",trigger:"blur"}]},{type:"input",label:"地址",field:"address",rules:[{required:!0,message:"请输入企业地址",trigger:"blur"},{max:40,message:"输入不可超过40位",trigger:"blur"}]},{type:"input-number",label:"经度",field:"lgtd",rules:[{required:!0,message:"请输入企业经度",trigger:"blur"}]},{type:"input-number",label:"纬度",field:"latd",rules:[{required:!0,message:"请输入企业纬度",trigger:"blur"}]},{type:"input",label:"邮编",field:"zip",rules:[{validator:W,trigger:"blur"},{max:20,message:"名称不超过20位",trigger:"blur"}]},{type:"input",label:"地区",field:"region",rules:[{max:40,message:"输入不可超过40位",trigger:"blur"}]},{type:"input",label:"平台名称",field:"platformName",aInfo:!0,rules:[{max:40,message:"输入不可超过40位",trigger:"blur"}]},{type:"textarea",label:"企业简介",field:"companyProfiles",aInfo:!0,rules:[{max:200,message:"输入不可超过200位",trigger:"blur"}]},{type:"input",label:"App标题",field:"apptitle",aInfo:!0,rules:[{max:40,message:"输入不可超过200位",trigger:"blur"}]}]}],O={class:"wrapper"},$=D({__name:"sysTenant",setup(y){const _=b(),o=b(),c=b(),v=u({labelWidth:"60px",filters:[{type:"input",field:"name",label:"搜索",onChange:()=>s()},{type:"btn-group",btns:[{perm:!0,type:"primary",text:"查询",click:()=>s()},{perm:!0,type:"primary",text:"添加",click:()=>h()},{perm:!0,type:"warning",text:"批量删除",click:()=>x()}]}],defaultParams:{}}),r=u({columns:[{prop:"name",label:"名称"}],dataList:[],operationWidth:400,operations:[{text:"管理企业管理员",perm:!0,icon:"iconfont icon-icon_fuzhi",click:e=>{var t;d.currentId=e.id,(t=c.value)==null||t.open()}},{text:"企业授权管理",perm:!0,isTextBtn:!0,icon:"iconfont icon-icon_fuzhi",click:e=>{var t;d.currentId=e.id,(t=c.value)==null||t.open()}},{text:"编辑",perm:!0,isTextBtn:!0,icon:"iconfont icon-bianji",click:e=>h(e)},{text:"删除",perm:!0,isTextBtn:!0,icon:"iconfont icon-shanchu",click:e=>x(e)}],pagination:{total:0,page:1,align:"right",limit:20,handlePage:e=>{r.pagination.page=e,s()},handleSize:e=>{r.pagination.limit=e,s()}},handleSelectChange:e=>{r.selectList=e}}),l=u({title:"添加企业",dialogWidth:500,submit:async e=>{var t;try{(await L(e)).data?(p.success("操作成功"),(t=o.value)==null||t.closeDialog()):p.error("操作失败"),l.submitting=!1}catch{l.submitting=!1}},submitting:!1,defaultValue:{},group:[]}),h=e=>{var t,a,n;try{if(l.title=e?"编辑企业":"添加企业",e){const i=typeof e.additionalInfo=="string"?JSON.parse(e.additionalInfo):e.additionalInfo;l.defaultValue={...e??{},...i}}else l.defaultValue={};(a=(t=o.value)==null?void 0:t.refForm)==null||a.resetForm(),(n=o.value)==null||n.openDialog()}catch(i){console.log(i)}},s=async()=>{var a,n,i;r.loading=!0;const e=(a=_.value)==null?void 0:a.queryParams,t={page:r.pagination.page,limit:r.pagination.limit,...e||{}};try{const f=await q(t);r.dataList=((n=f.data)==null?void 0:n.data)||[],r.pagination.total=((i=f.data)==null?void 0:i.total)||0,r.loading=!1}catch{r.loading=!1}},d=u({title:"企业管理员列表",visible:!1,currentId:"",close:()=>{var e;(e=c.value)==null||e.close()}}),x=e=>{F("确定删除指定企业?","删除提示").then(()=>{var a;const t=e?[e.id.id]:(a=r.selectList)==null?void 0:a.map(n=>{var i;return(i=n.id)==null?void 0:i.id});if(!(t!=null&&t.length)){p.warning("请先选择要删除的企业");return}B(t).then(()=>{p.success("操作成功"),s()})})},C=e=>{const t=(e==null?void 0:e.map(a=>({label:a.appName,value:a.id})))||[];l.group=j(t)};return P(async()=>{s();const e=await z();C(e.data)}),(e,t)=>{const a=k,n=T,i=S;return M(),N("div",O,[g(a,{ref_key:"cardSearch",ref:_,class:"cardSearch",config:m(v)},null,8,["config"]),g(n,{class:"cardtable",config:m(r)},null,8,["config"]),g(i,{ref_key:"refDialogForm",ref:o,config:m(l),onRefreshData:s},null,8,["config"]),g(I,{ref_key:"refTenantDialog",ref:c,config:m(d)},null,8,["config"])])}}}),re=V($,[["__scopeId","data-v-884b68f4"]]);export{re as default};
