import{d as g,r as u,o as C,g as i,n as l,an as y,p as n,aw as I,i as c,bh as p,Z as _,C as E}from"./index-r0dFAfgr.js";const x={class:"weather"},f=["src"],N={class:"weather-item text"},G={class:"weather-item"},k=g({__name:"Weather",props:{logo:{}},setup(h){const r=h,t=u({icon:"qi-301",text:"多云",temp:"24"}),w=async()=>{var e,o,s;try{const a=window.SITE_CONFIG.GIS_CONFIG.gisDefaultCenter.join(","),m=`https://geoapi.qweather.com/v2/city/lookup?key=${window.SITE_CONFIG.WEATHER_CONFIG.KEY}&location=${a}&lang=zh`;return(s=(o=(e=(await _.get(m)).data)==null?void 0:e.location)==null?void 0:o[0])==null?void 0:s.id}catch{console.log("获取当前城市信息失败")}},d=async()=>{const e=await w();if(e)try{const o=`https://devapi.qweather.com/v7/weather/now?key=${window.SITE_CONFIG.WEATHER_CONFIG.KEY}&location=${e}`,a=(await _.get(o)).data.now||{};t.icon="qi-"+a.icon,t.text=a.text,t.temp=a.temp}catch{console.log("获取当前城市天气信息失败")}};return C(()=>{d()}),(e,o)=>(i(),l("div",x,[r.logo?(i(),l("img",{key:0,src:r.logo,alt:"",class:"weather-item logo"},null,8,f)):y("",!0),n("i",{class:I(["weather-item",c(t).icon])},null,2),n("span",N,p(c(t).text),1),n("span",G,p(c(t).temp)+"℃",1)]))}}),O=E(k,[["__scopeId","data-v-c955d8b3"]]);export{O as default};
