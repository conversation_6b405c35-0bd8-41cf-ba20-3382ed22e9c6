package org.thingsboard.server.dao.model.sql.smartPipe.dma;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import java.util.Date;

/**
 * 水厂监测点
 */
@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@TableName(ModelConstants.PIPE_WATER_FACTORY_MONITOR_POINT_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class PipeWaterFactoryMonitorPoint {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @TableField(ModelConstants.PIPE_WATER_FACTORY_MONITOR_POINT_FACTORY_NAME)
    private String factoryName;

    @TableField(ModelConstants.PIPE_WATER_FACTORY_MONITOR_POINT_ORDER_NUM)
    private Integer orderNum;

    @TableField(ModelConstants.PIPE_WATER_FACTORY_MONITOR_POINT_DEVICE_ID)
    private String deviceId;

    @TableField(exist = false)
    private String deviceName;

    @TableField(ModelConstants.PIPE_WATER_FACTORY_MONITOR_POINT_DIRECTION)
    private String direction;

    @TableField(ModelConstants.CREATE_TIME)
    private Date createTime;

    @TableField(ModelConstants.CREATOR)
    private String creator;

    @TableField(ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;

}
