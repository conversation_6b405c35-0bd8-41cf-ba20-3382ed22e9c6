<template>
  <div class="main">
    <div class="card zutai-card">
      <!-- <FramePage
        :url="''"
        :token-field="'token'"
        :disable-full-screen="true"
        ></FramePage> -->
      <div class="card-content" style="bottom: 47%; right: 31%; width: 130px">
        <div class="card-title" style="width: 110px">
          <span style="color: #d8feff; text-align: center">PAC制备装置</span>
        </div>
        <div class="row">
          <div class="label">计量泵1#：</div>
          <div class="status">
            <img :src="run" style="width: 15px;height: 15px;"/>
          </div> 
          <!-- <div
            :class="[
              'status',
              realTimeValue['PAC/PAM']?.pac1_status == '运行'
                ? 'online'
                : 'unline'
            ]"
          ></div> -->
        </div>
        <div class="row">
          <div class="label">计量泵2#：</div>
          <div class="status">
            <img :src="run" style="width: 15px;height: 15px;"/>
          </div> 
          <!-- <div
            :class="[
              'status',
              realTimeValue['PAC/PAM']?.pac2_status == '运行'
                ? 'online'
                : 'unline'
            ]"
          ></div> -->
        </div>
      </div>

      <div class="card-content" style="bottom: 47%; left: 36%; width: 130px">
        <div class="card-title" style="width: 110px">
          <span style="color: #d8feff; text-align: center">PAM制备装置</span>
        </div>
        <div class="row">
          <div class="label">计量泵1#：</div>
          <div class="status">
            <img :src="run" style="width: 15px;height: 15px;"/>
          </div> 
          <!-- <div
            :class="[
              'status',
              realTimeValue['PAC/PAM']?.pam1_status == '运行'
                ? 'online'
                : 'unline'
            ]"
          ></div> -->
        </div>
        <div class="row">
          <div class="label">计量泵2#：</div>
          <div class="status">
            <img :src="run" style="width: 15px;height: 15px;"/>
          </div> 
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { getDatasList } from '@/api/device';
import { removeSlash } from '@/utils/removeIdSlash';

import { useBusinessStore } from '@/store';
import stop from '../../imgs/水泵_停止.png'
import run from '../../imgs/水泵_在线.gif'
const businessStore = useBusinessStore();
const realTimeValue = ref<any>({});
const Interval = ref<any>();
const refreshData = (devices: any) => {
  for (const i in devices) {
    getDatasList(removeSlash(devices[i].id.id), {
      page: 1,
      size: 9999999
    }).then((res) => {
      const data = res.data.data;
      realTimeValue.value[devices[i].name] = {};
      for (const t in data) {
        realTimeValue.value[devices[i].name][data[t].property] =
          `${data[t].value}${data[t].unit || ''}`;
      }
    });
    // realTimeValue.value[devices[i].name] = res.data.data?.map(item => {
    //   return {
    //     [item.property]: `${item.value}${item.unit}`,
    //   }
    // })
  }
  console.log(realTimeValue.value);
};

const getValue = (property, deviceId) => {
  return realTimeValue.value.find(
    (item) => item.property === property && item.deviceId === deviceId
  );
};

const initData = () => {
  console.log(businessStore.projectList);
  const projectId = businessStore.projectList[0].id as string;
  
};

onMounted(() => {
  initData();
  Interval.value = setInterval(() => {
    initData();
  }, 30000);
});

onBeforeUnmount(() => {
  clearInterval(Interval.value);
});
</script>
<style lang="scss" scoped>

.main {
  width: 100%;
  height: 100%;
  position: relative;
  background: rgb(3, 17, 45);
}

.content {
  position: relative;
  width: 100%;
  height: 100%;
  padding: 20px;
  padding-top: 81px;
  color: rgba(184, 210, 255, 1);
}

.card {
  position: absolute;
}

.zutai-card {
  width: 100%;
  height: 100%;
  border: 1px solid rgba(0, 191, 252, 0.4);
  background: url(../../imgs/加药间.png) 0 0 /100% 100% no-repeat;
}

.cqjj-card {
  width: 745px;
  height: 315px;
  top: 746px;
  left: 26px;
}

.szjc-card {
  width: 994px;
  height: 315px;
  top: 746px;
  left: 783px;
}

.ssqx-card {
  width: 738px;
  height: 315px;
  top: 105px;
  left: 1790px;
}

.nhjc-card {
  width: 738px;
  height: 315px;
  top: 436px;
  left: 1790px;
}

.rgsl-card {
  width: 738px;
  height: 294px;
  top: 767px;
  left: 1790px;
}

.card-content {
  position: absolute;
  width: 190px;
  border-radius: 4px;
  padding: 0 5px 10px 5px;
  box-shadow: 0px 0px 8px 0px #00d9ff inset;
  background: linear-gradient(
    rgba(30, 105, 158, 0.1) 0%,
    rgba(30, 105, 158, 0.6) 100%
  );

  .card-title {
    // transform: translateX(-50%);
    font-size: 12px;
    line-height: 18px;
    width: 93px;
    height: 18px;
    margin: 0 auto;
    text-align: center;
    border-radius: 0 0 10px 10px;
    background: linear-gradient(
      180deg,
      rgba(116, 229, 255, 0.2) 0%,
      rgba(16, 229, 255, 0.6) 100%
    );
  }

  .row {
    display: flex;
    font-size: 12px;
    color: #d8feff;
    padding: 10px 10px 0px 10px;
    align-items: center;
    .value {
      padding-left: 4px;
    }
  }
}
.ysc-zjs {
  top: 4%;
  left: 7%;
  width: 170px;
}
.ysc-tsb1 {
  bottom: 32%;
  right: 34%;
  width: 170px;
}
.ysc-tsb3 {
  bottom: 12%;
  right: 34%;
  width: 170px;
}
.ysc-ddf1 {
  bottom: 32%;
  right: 20%;
  width: 170px;
}
.ysc-ddf3 {
  bottom: 12%;
  right: 20%;
  width: 170px;
}
.status {
  padding-left: 4px;
  width: 15px;
  height: 15px;
  border-radius: 50%;
}
.online {
  background-color: rgb(42, 241, 15);
}

.unline {
  background-color: red;
}
// .card-content::before {
//   content: '';
//   transform: translateX(-50%);
//   width: 100px;
//   height: 10px;
//   background-color: rgba(173, 216, 230, 0.8); /* 顶部的浅蓝色区域 */
//   border-radius: 0 0 10px 10px;
// }
</style>
