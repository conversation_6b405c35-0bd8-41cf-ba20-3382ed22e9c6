package org.thingsboard.server.dao.sql.logicalFlow;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.thingsboard.server.dao.model.sql.LogicalFlowAlarmNodeHistory;

import java.util.Date;
import java.util.List;

public interface LogicalFlowAlarmNodeHistoryRepository extends JpaRepository<LogicalFlowAlarmNodeHistory, String>,
        JpaSpecificationExecutor<LogicalFlowAlarmNodeHistory> {
    List<LogicalFlowAlarmNodeHistory> findByLogicalFlowNodeId(String logicalFlowNodeId, Pageable pageable);

    List<LogicalFlowAlarmNodeHistory> findByLogicalFlowNodeIdAndSendFlag(String id, String sendFlag, Pageable pageable);

    Page<LogicalFlowAlarmNodeHistory> findByLogicalFlowNodeIdInAndCreatedTimeIsGreaterThanEqualAndCreatedTimeIsLessThanEqualOrderByCreatedTimeDesc(List<String> nodeIds, Long start, Long end, Pageable pageable);

    void deleteByLogicalFlowId(String logicalFlowId);
}
