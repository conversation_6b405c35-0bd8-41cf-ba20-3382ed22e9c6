import{m as w,d as S,r as k,c as m,o as L,ay as I,g as D,h as O,F as y,p as o,q as T,i as x,j as B,l,C as A}from"./index-r0dFAfgr.js";import{a as V}from"./factoryReport-9H96f-eB.js";import{u as N}from"./useDetector-BRcb7GRN.js";import{u as W}from"./useStation-DJgnSZIA.js";import q from"./TitleCard-BgReUNwX.js";import"./zhandian-YaGuQZe6.js";import"./TitleHeader-CBWfLOPA.js";function z(p){return w({url:"/api/operatingIncomeInput/list",method:"get",params:p})}const F=S({__name:"CXCTJ",setup(p){const c=k({barChartOption:null}),u=W(),g=(t=[],e=[],a=[])=>{const n=e.map((s,r)=>s?Number(((s-(a[r]??0))/s*100).toFixed(2)):0);c.barChartOption={backgroundColor:"transparent",legend:{left:"right",top:"top",type:"scroll",textStyle:{color:"#fff",fontSize:12}},tooltip:{trigger:"axis"},grid:{left:10,right:10,top:50,bottom:10,containLabel:!0},xAxis:{type:"category",data:t||[],axisLabel:{show:!0,textStyle:{}},axisTick:{show:!1},splitLine:{show:!1}},yAxis:[{position:"left",type:"value",name:"m³",axisLabel:{show:!0,textStyle:{}},axisTick:{show:!1},splitLine:{lineStyle:{type:"dashed",color:"#666"}}},{type:"value",name:"产销差（%）",axisLabel:{show:!0,textStyle:{}},axisTick:{show:!1},splitLine:{show:!1}}],series:[{name:"供水量",type:"bar",barWidth:5,data:e||[]},{name:"售水量",type:"bar",barWidth:5,data:a||[]},{name:"产销差",type:"line",yAxisIndex:1,data:n||[]}]}},C=async()=>{var t;return await u.getStationOption("水厂",void 0,!0),(t=u.StationList.value[0])==null?void 0:t.value},b=async()=>{var r,h;const t=[],e=[],a=[],n=l().get("y").toString(),s=await C();s&&((await z({stationId:s,year:n})).data.data.map(i=>{a.push(i.ts),e.push(i.waterSales)}),(h=(r=(await V({stationIdList:s,queryType:"year",start:l().startOf("y").valueOf(),end:l().endOf("y").valueOf()})).data.data)==null?void 0:r.tableDataList)==null||h.map((i,_)=>{_>11||t.push(i.total)})),g(a,t,e)},v=N(),d=m(),f=m();return L(()=>{b(),v.listenToMush(f.value,()=>{var t;(t=d.value)==null||t.resize()})}),(t,e)=>{const a=I("VChart");return D(),O(q,{title:"产销差统计"},{title:y(()=>e[0]||(e[0]=[o("div",{class:"card-header"},[o("span",{class:"title-text"},[o("i",null,"产销差统计")]),o("div",{class:"left"})],-1)])),default:y(()=>[o("div",{ref_key:"refDiv",ref:f,class:"chart-box"},[T(a,{ref_key:"refChart",ref:d,option:x(c).barChartOption,theme:x(B)().isDark?"blackBackground":"whiteBackground"},null,8,["option","theme"])],512)]),_:1})}}}),P=A(F,[["__scopeId","data-v-da5f8637"]]);export{P as default};
