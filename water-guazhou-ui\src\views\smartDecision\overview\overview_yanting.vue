<!-- 智慧生产 -->
<template>
  <div class="content">
    <!-- left -->
    <div class="content_left">
      <TitleCard :title="'原水供应'" class="ysgy m-b-12">
        <YSGY @changeFactory="changeFactory"></YSGY>
      </TitleCard>
      <TitleCard :title="'生产调度'" class="scdd m-b-12">
        <SCDD
          :water-supply="waterSupply"
          @showWaterSupply="showWaterSupply"
        ></SCDD>
      </TitleCard>
      <TitleCard :title="'总供水'" class="zgs">
        <ZGS :water-supply="waterSupply"></ZGS>
      </TitleCard>
    </div>
    <!-- center -->
    <div class="content_center">
      <ThreeMap></ThreeMap>
      <TotalStatistic
        class="total-statistic"
        :water-supply="waterSupply"
        :water-supply-num="waterSupplyNum"
      ></TotalStatistic>
      <StationStatistic class="station-statistic"></StationStatistic>
      <Legend class="legend"></Legend>
    </div>
    <!-- right -->
    <div class="content_left content_right">
      <!-- <TitleCard :title="'营销服务'" class="yxfw m-b-12">
        <YXFW></YXFW>
      </TitleCard>
      <TitleCard :title="'漏损分析'" class="lsfx m-b-12">
        <LSPH></LSPH>
      </TitleCard> -->
      <TitleCard :title="'管网监控'" class="egzl">
        <GWJK :height="870"></GWJK>
      </TitleCard>
    </div>
  </div>
</template>

<script lang="ts" setup>
import TitleCard from '../components/TitleCard.vue';
import LSPH from './components/LSFX.vue';
import GWJK from './components/GWJK.vue';
import SCDD from './components/SCDD_zilianda.vue';
import YSGY from './components/YSGY_zilianda.vue';
import YXFW from './components/YXFW.vue';
import ZGS from './components/ZGS.vue';
import ThreeMap from './components/ThreeMap.vue';
import Legend from './components/Legend.vue';
import StationStatistic from './components/StationStatistic.vue';
import TotalStatistic from './components/TotalStatistic_zilianda.vue';

const waterSupply = ref<any>(null);
const waterSupplyNum = ref<any>(null);

// 选择水厂
const changeFactory = (item: any) => {
  console.log(item);
  waterSupply.value = item;
};
const showWaterSupply = (item: any) => {
  console.log(item);
  waterSupplyNum.value = item;
};
</script>

<style lang="scss" scoped>
.content {
  height: 100%;
  width: 100%;
  padding: 100px 50px 30px;
  display: flex;
  justify-content: space-between;
  color: #fff;
  padding-top: 92px;

  .content_left {
    width: 479px;
    height: 100%;
    z-index: 2;
    padding: 10px;
  }
  .content_center {
    flex: 1;
    padding: 10px;
    position: relative;
    display: flex;
    flex-direction: column;
    height: 100%;
    justify-content: space-between;
    background: url('../imgs/map_circle.png') 0 40% / 100% 60% no-repeat;
    .total-statistic {
      position: absolute;
      left: 0;
      top: 0;
    }
    .station-statistic {
      position: absolute;
      left: 20px;
      top: 100px;
    }
    .legend {
      position: absolute;
      right: 20px;
      bottom: 80px;
    }
  }
}
.ysgy {
  height: 230px;
}
.scdd {
  width: 100%;
  height: 370px;
}
.gwjk {
  width: 100%;
  height: 278px;
}
.zgs {
  width: 100%;
  height: 278px;
}
.yxfw {
  width: 100%;
  height: 313px;
}
.lsfx {
  width: 100%;
  height: 264px;
}
.egzl {
  width: 100%;
  height: 915px;
}

.m-b-12 {
  margin-bottom: 12px;
}
</style>
