import{_}from"./index-C9hz-UZb.js";import{d as m,o as g,g as b,n as v,q as e,F as t,G as o,bh as a,p as r,aw as T,i as w,j as y,cZ as H,c6 as L,c_ as D,bs as M,C as h}from"./index-r0dFAfgr.js";/* empty css                             */import{e as C}from"./index-CjYkj_FN.js";const E=["innerHTML"],k=["innerHTML"],x=["innerHTML"],N=m({__name:"detail",props:{config:{}},setup(S){const p=()=>{C().then(n=>{console.log(n)})};return g(()=>{p()}),(n,l)=>{const i=H,c=L,d=D,s=M,u=_;return b(),v("div",{class:T({isDark:w(y)().isDark})},[e(u,{style:{height:"calc(100% - 80px)",padding:"20px",margin:"auto","margin-top":"20px",width:"100%",overflow:"auto"}},{default:t(()=>[e(d,{title:"基础信息"},{default:t(()=>[e(i,{label:"姓名"},{default:t(()=>{var f;return[o(a(((f=n.config)==null?void 0:f.name)||""),1)]}),_:1}),e(i,{label:"性别"},{default:t(()=>[e(c,{size:"small"},{default:t(()=>[o(a(n.config.gender),1)]),_:1})]),_:1}),e(i,{label:"联系电话"},{default:t(()=>[o(a(n.config.phone),1)]),_:1}),e(i,{label:"联系邮箱"},{default:t(()=>[o(a(n.config.email),1)]),_:1}),e(i,{label:"最高学历"},{default:t(()=>[o(a(n.config.educationLevel),1)]),_:1})]),_:1}),e(s,{"content-position":"left"},{default:t(()=>l[0]||(l[0]=[o(" 行业领域 ")])),_:1}),e(d,{title:""},{default:t(()=>[e(i,{label:"职称"},{default:t(()=>[o(a(n.config.professionalTitle),1)]),_:1}),e(i,{label:"职称评定时间"},{default:t(()=>[o(a(n.config.professionalTitleTime),1)]),_:1}),e(i,{label:"行业领域"},{default:t(()=>[o(a(n.config.industrySector),1)]),_:1}),e(i,{label:"行业从事时间"},{default:t(()=>[o(a(n.config.industryTime),1)]),_:1}),e(i,{label:"工作单位"},{default:t(()=>[o(a(n.config.deptName),1)]),_:1}),e(i,{label:"单位电话"},{default:t(()=>[o(a(n.config.deptPhone),1)]),_:1})]),_:1}),e(s,{"content-position":"left"},{default:t(()=>l[1]||(l[1]=[o(" 工作经历 ")])),_:1}),r("div",{class:"view w-e-text-container",innerHTML:n.config.jobHistory,style:{margin:"0px 40px"}},null,8,E),e(s,{"content-position":"left"},{default:t(()=>l[2]||(l[2]=[o(" 所获荣誉 ")])),_:1}),r("div",{class:"view w-e-text-container",innerHTML:n.config.honorHistory,style:{margin:"0px 40px"}},null,8,k),e(s,{"content-position":"left"},{default:t(()=>l[3]||(l[3]=[o(" 学术成就 ")])),_:1}),r("div",{class:"view w-e-text-container",innerHTML:n.config.academicHistory,style:{margin:"0px 40px"}},null,8,x)]),_:1})],2)}}}),I=h(N,[["__scopeId","data-v-743066a8"]]);export{I as default};
