<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.conservationWaterLevel.ConservationWaterLevelMapper">

    <sql id="Base_Column_List">
        c.id,
        c.tenant_id as tenantId,
        c.station_id as stationId,
        c.raw_water_level as rawWaterLevel,
        c.groundwater_level as groundwaterLevel,
        c.level_change as levelChange,
        c.rainfall_amount as rainfallAmount,
        c.evaporation_amount as evaporationAmount,
        c.surface_runoff as surfaceRunoff,
        c.extraction_amount as extractionAmount,
        c.soil_moisture as soilMoisture,
        c.permeability_coefficient as permeabilityCoefficient,
        c.record_time as recordTime,
        c.create_time as createTime,
        c.update_time as updateTime,
        c.remark,
        c.data_source as dataSource,
        c.creator,
        s.name as stationName,
        s.location as stationLocation,
        u.first_name as creatorName
    </sql>

    <select id="getList" resultType="org.thingsboard.server.dao.model.sql.conservationWaterLevel.ConservationWaterLevel">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_conservation_water_level c
        LEFT JOIN tb_station s ON c.station_id = s.id
        LEFT JOIN tb_user u ON c.creator = u.id
        <where>
            <if test="params.tenantId != null and params.tenantId != ''">
                AND c.tenant_id = #{params.tenantId}
            </if>
            <if test="params.stationId != null and params.stationId != ''">
                AND c.station_id = #{params.stationId}
            </if>
            <if test="params.stationName != null and params.stationName != ''">
                AND s.name LIKE CONCAT('%', #{params.stationName}, '%')
            </if>
            <if test="params.dataSource != null">
                AND c.data_source = #{params.dataSource}
            </if>
            <if test="params.startTime != null">
                AND c.record_time >= #{params.startTime}
            </if>
            <if test="params.endTime != null">
                AND c.record_time &lt;= #{params.endTime}
            </if>
            <if test="params.creator != null and params.creator != ''">
                AND c.creator = #{params.creator}
            </if>
        </where>
        ORDER BY c.record_time DESC
        <if test="params.pageNum != null and params.pageSize != null">
            LIMIT #{params.pageSize} OFFSET #{params.offset}
        </if>
    </select>

    <select id="getCount" resultType="int">
        SELECT COUNT(1)
        FROM tb_conservation_water_level c
        LEFT JOIN tb_station s ON c.station_id = s.id
        <where>
            <if test="params.tenantId != null and params.tenantId != ''">
                AND c.tenant_id = #{params.tenantId}
            </if>
            <if test="params.stationId != null and params.stationId != ''">
                AND c.station_id = #{params.stationId}
            </if>
            <if test="params.stationName != null and params.stationName != ''">
                AND s.name LIKE CONCAT('%', #{params.stationName}, '%')
            </if>
            <if test="params.dataSource != null">
                AND c.data_source = #{params.dataSource}
            </if>
            <if test="params.startTime != null">
                AND c.record_time >= #{params.startTime}
            </if>
            <if test="params.endTime != null">
                AND c.record_time &lt;= #{params.endTime}
            </if>
            <if test="params.creator != null and params.creator != ''">
                AND c.creator = #{params.creator}
            </if>
        </where>
    </select>

    <select id="getWaterLevelChangeData" resultType="org.thingsboard.server.dao.model.sql.conservationWaterLevel.ConservationWaterLevel">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_conservation_water_level c
        LEFT JOIN tb_station s ON c.station_id = s.id
        LEFT JOIN tb_user u ON c.creator = u.id
        WHERE c.station_id = #{stationId}
        AND c.record_time BETWEEN #{startTime} AND #{endTime}
        ORDER BY c.record_time ASC
    </select>

    <select id="getLatestWaterLevel" resultType="org.thingsboard.server.dao.model.sql.conservationWaterLevel.ConservationWaterLevel">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_conservation_water_level c
        LEFT JOIN tb_station s ON c.station_id = s.id
        LEFT JOIN tb_user u ON c.creator = u.id
        WHERE c.station_id = #{stationId}
        ORDER BY c.record_time DESC
        LIMIT 1
    </select>

    <select id="getStatisticsData" resultType="map">
        SELECT
            COUNT(*) as totalRecords,
            AVG(c.raw_water_level) as avgRawWaterLevel,
            AVG(c.groundwater_level) as avgGroundwaterLevel,
            AVG(c.level_change) as avgLevelChange,
            SUM(c.rainfall_amount) as totalRainfall,
            SUM(c.evaporation_amount) as totalEvaporation,
            SUM(c.extraction_amount) as totalExtraction,
            MAX(c.raw_water_level) as maxRawWaterLevel,
            MIN(c.raw_water_level) as minRawWaterLevel,
            MAX(c.groundwater_level) as maxGroundwaterLevel,
            MIN(c.groundwater_level) as minGroundwaterLevel
        FROM tb_conservation_water_level c
        WHERE c.station_id = #{stationId}
        AND c.record_time BETWEEN #{startTime} AND #{endTime}
    </select>

    <insert id="batchInsert">
        INSERT INTO tb_conservation_water_level (
            id, tenant_id, station_id, raw_water_level, groundwater_level, level_change,
            rainfall_amount, evaporation_amount, surface_runoff, extraction_amount,
            soil_moisture, permeability_coefficient, record_time, create_time, update_time,
            remark, data_source, creator
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.id}, #{item.tenantId}, #{item.stationId}, #{item.rawWaterLevel},
                #{item.groundwaterLevel}, #{item.levelChange}, #{item.rainfallAmount},
                #{item.evaporationAmount}, #{item.surfaceRunoff}, #{item.extractionAmount},
                #{item.soilMoisture}, #{item.permeabilityCoefficient}, #{item.recordTime},
                #{item.createTime}, #{item.updateTime}, #{item.remark}, #{item.dataSource}, #{item.creator}
            )
        </foreach>
    </insert>

</mapper>
