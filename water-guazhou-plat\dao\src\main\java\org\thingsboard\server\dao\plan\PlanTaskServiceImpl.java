package org.thingsboard.server.dao.plan;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.dao.model.sql.plan.PlanTask;
import org.thingsboard.server.dao.sql.plan.PlanTaskMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.plan.PlanTaskCompleteRequest;
import org.thingsboard.server.dao.util.imodel.query.plan.PlanTaskPageRequest;
import org.thingsboard.server.dao.util.imodel.query.plan.PlanTaskSaveRequest;
import org.thingsboard.server.dao.util.imodel.query.plan.PlanTaskStartRequest;

import static org.thingsboard.server.dao.model.sql.smartProduction.circuit.GeneralTaskStatus.APPROVED;
import static org.thingsboard.server.dao.model.sql.smartProduction.circuit.GeneralTaskStatus.RECEIVED;

@Service
public class PlanTaskServiceImpl implements PlanTaskService {
    @Autowired
    private PlanTaskMapper mapper;

    @Autowired
    private PlanTaskDetailService service;


    @Override
    public IPage<PlanTask> findAllConditional(PlanTaskPageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    @Transactional
    public PlanTask save(PlanTaskSaveRequest entity) {
        // service.removeAllByMainId(entity.getId()); 已禁止更新，在需要更新的时候再说
        PlanTask planTask = QueryUtil.saveOrUpdateOneByRequest(entity, mapper::insert, mapper::update);
        service.saveAll(entity.getItems(planTask.getId()));
        return planTask;
    }

    @Override
    public PlanTask save(PlanTask entity) {
        return QueryUtil.saveOrUpdateOne(entity, mapper);
    }

    @Override
    public boolean update(PlanTask entity) {
        return mapper.update(entity);
    }

    @Override
    @Transactional
    public boolean delete(String id) {
        service.deleteByMainId(id);
        return mapper.deleteById(id) > 0;
    }

    @Override
    public boolean complete(PlanTaskCompleteRequest req) {
        return mapper.complete(req, APPROVED);
    }

    @Override
    public boolean reset(String id) {
        return mapper.resetChildren(id) > 0 && mapper.reset(id);
    }

    @Override
    public boolean start(PlanTaskStartRequest req) {
        return mapper.start(req, RECEIVED);
    }

    @Override
    public boolean canOperate(String id, String currentUser) {
        return mapper.canOperate(id, currentUser);
    }

}
