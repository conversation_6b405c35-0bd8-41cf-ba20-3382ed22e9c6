package org.thingsboard.server.dao.captcha;

import org.thingsboard.server.dao.model.sql.CaptchaEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/5/28 14:19
 */
public interface CaptchaService {

    List<CaptchaEntity> getCaptchaByTenant(String tenantId);

    List<CaptchaEntity> getCaptchaByPhone(String phone);

    CaptchaEntity saveCaptcha(CaptchaEntity captchaEntity);

    CaptchaEntity getCaptchaByPhoneAndInvalid(String phone,String invalid);

    CaptchaEntity getCaptchaByCaptchaAndInvalid(String captcha,String invalid);

    CaptchaEntity getCaptchaByCaptchaAndInvalidAndPhone(String captcha,String invalid,String phone);
}
