package org.thingsboard.server.dao.sql.supplier;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.SupplierEntity;

import java.util.List;

/**
 * userMybatis
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-08-23
 */
@Mapper
public interface SupplierMapper extends BaseMapper<SupplierEntity> {

    List<SupplierEntity> getList(@Param("name") String name, @Param("address") String address, @Param("status") String status, @Param("importance") String importance, @Param("page") int page, @Param("size") int size, @Param("tenantId") String tenantId);

    int getListCount(@Param("name") String name, @Param("address") String address, @Param("status") String status, @Param("importance") String importance, @Param("tenantId") String tenantId);

    String getNameById(String id);
}
