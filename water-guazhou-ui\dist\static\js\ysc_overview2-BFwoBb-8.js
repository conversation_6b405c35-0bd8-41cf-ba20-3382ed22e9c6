import{d as n,a0 as o,c as d,o as c,Q as r,g as i,n as l,dy as v,C as f}from"./index-r0dFAfgr.js";const p={class:"main"},_=n({__name:"ysc_overview2",setup(u){const a=o();d({});const t=d(),e=()=>{console.log(a.projectList),a.projectList[0].id};return c(()=>{e(),t.value=setInterval(()=>{e()},3e4)}),r(()=>{clearInterval(t.value)}),(m,s)=>(i(),l("div",p,s[0]||(s[0]=[v('<div class="card zutai-card" data-v-a7905f3d><div class="card-content" style="top:33%;left:28%;width:140px;" data-v-a7905f3d><div class="card-title" data-v-a7905f3d><span style="color:#d8feff;text-align:center;" data-v-a7905f3d>原水池2</span></div></div><div class="card-content ysc-zjs" style="" data-v-a7905f3d><div class="card-title" data-v-a7905f3d><span style="color:#d8feff;text-align:center;" data-v-a7905f3d>总进水</span></div></div></div>',1)])))}}),x=f(_,[["__scopeId","data-v-a7905f3d"]]);export{x as default};
