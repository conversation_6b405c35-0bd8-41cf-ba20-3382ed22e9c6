package org.thingsboard.server.dao.model.sql.statistic;

import org.thingsboard.server.dao.util.imodel.response.Responsible;
import org.thingsboard.server.dao.util.imodel.response.model.ReturnHelper;

import java.util.HashMap;

public class StatisticLong implements Responsible, Statisticable {
    private String key;
    private long value;

    public StatisticLong() {
    }

    public StatisticLong(String key, Long value) {
        this.key = key;
        this.value = value;
    }

    @Override
    public long getValue() {
        return value;
    }

    public String getKey() {
        return key;
    }

    @Override
    public Object postProcess(ReturnHelper returnHelper, Object arg) {
        return new HashMap<String, Object>() {{
            put("key", key);
            put("value", value);
            put("percentage", value * 1f / (long) arg);
        }};
    }
}
