/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.common.data.constantsAttribute;

import lombok.Data;

@Data
public class Transport {
    private String type;
    private String timeout;
    private String host;
    private String port;
    private String portName;
    private String encoding;
    private String baudRate;
    private String dataBits;
    private String stopBits;
    private String parity;
    private boolean useRtuTcp = true;

    public Transport() {
    }

    public Transport(String type, String timeout, String host, String port) {
        this.type = type;
        this.timeout = timeout;
        this.host = host;
        this.port = port;
    }

    public Transport(String type, String timeout, String portName, String encoding, String baudRate, String dataBits, String stopBits, String parity) {
        this.type = type;
        this.timeout = timeout;
        this.portName = portName;
        this.encoding = encoding;
        this.baudRate = baudRate;
        this.dataBits = dataBits;
        this.stopBits = stopBits;
        this.parity = parity;
    }
}
