package org.thingsboard.server.dao.model.sql.smartProduction.dispatch;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseUsername;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.util.Date;

@Getter
@Setter
@ResponseEntity
@TableName("sp_emergency_user")
public class EmergencyUser {
    // id
    @TableId
    private String id;

    // 人员名称
    private String name;

    // 联系电话
    private String phone;

    // 所属部门
    private String deptId;

    // 所属部门
    @TableField(exist = false)
    private String deptName;

    // 创建人
    @ParseUsername
    private String creator;

    // 创建时间
    private Date createTime;

    // 租户ID
    private String tenantId;

}
