package org.thingsboard.server.dao.model.request;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrderReminder;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;

@EqualsAndHashCode(callSuper = true)
@Data
public class WorkOrderReminderRequest extends AdvancedPageableQueryEntity<WorkOrderReminder, WorkOrderReminderRequest> {

    private String content;

    private String isDel;

    private String tenantId;

}
