import{d as V,r as x,bu as L,am as T,g as o,n,q as F,p as t,aB as I,aJ as B,bh as m,i as N,bt as W,C}from"./index-r0dFAfgr.js";const M={class:"basic-info overlay-y"},S={class:"list waterinfo"},q={class:"label",for:""},A={class:"value"},E={class:"list flow"},J={class:"label",for:""},P={class:"value"},R=V({__name:"BasicInfo",props:{dataDma:{}},setup(k){const a=k,i=x({waterInfo:[],minFlowInfo:[]}),d=()=>{var r,u,s,e,l,c,v,_,b,p,D,f,h,w,$,g,y;i.waterInfo=[{label:"用户类型",value:(r=a.dataDma)==null?void 0:r.userType},{label:"供水量",value:`${((u=a.dataDma)==null?void 0:u.supply)||0} m³`},{label:"售水量",value:`${((s=a.dataDma)==null?void 0:s.sale)||0} m³`},{label:"产销差水量",value:`${((e=a.dataDma)==null?void 0:e.nrw)||0} m³`},{label:"产销差",value:(l=a.dataDma)==null?void 0:l.nrwRate},{label:"用户数",value:`${((c=a.dataDma)==null?void 0:c.userNum)||0} 户`},{label:"大用户用水量",value:`${((v=a.dataDma)==null?void 0:v.bigUserWater)||0} m³`},{label:"其它用水量",value:`${((_=a.dataDma)==null?void 0:_.otherWater)||0} m³`}],i.minFlowInfo=[{label:"夜间最小流量",value:`${((b=a.dataDma)==null?void 0:b.minNightFlow)||0} m³/h`},{label:"夜间最小流时间",value:(p=a.dataDma)==null?void 0:p.minNightFlowTime},{label:"最小水量",value:`${((D=a.dataDma)==null?void 0:D.minValue)||0} m³`},{label:"最小水量时间",value:(f=a.dataDma)==null?void 0:f.minValueTime},{label:"预估漏损水量",value:`${((h=a.dataDma)==null?void 0:h.lossWater)||0} m³`},{label:"管道长度",value:`${((w=a.dataDma)==null?void 0:w.pipeLength)||0} km`},{label:"MNF/日均流量",value:($=a.dataDma)==null?void 0:$.mnfDivDayAvgFlow},{label:"单位管长净夜间流量",value:`${((g=a.dataDma)==null?void 0:g.unitPipeNightFlow)||0} m³/h/km`},{label:"漏失等级",value:(y=a.dataDma)==null?void 0:y.lossLevel}]};return L(()=>{d()}),T(()=>a.dataDma,()=>d()),(r,u)=>{const s=W;return o(),n("div",M,[F(s,{title:"昨日水量信息",type:"simple"}),t("div",S,[(o(!0),n(I,null,B(N(i).waterInfo,(e,l)=>(o(),n("div",{key:l,class:"list-item"},[t("label",q,m(e.label+":"),1),t("span",A,m(e.value),1)]))),128))]),F(s,{title:"昨日小流信息",type:"simple"}),t("div",E,[(o(!0),n(I,null,B(N(i).minFlowInfo,(e,l)=>(o(),n("div",{key:l,class:"list-item"},[t("label",J,m(e.label+":"),1),t("span",P,m(e.value),1)]))),128))])])}}}),j=C(R,[["__scopeId","data-v-d013f0b9"]]);export{j as default};
