<!-- 标签展示 -->
<template>
  <RightDrawerMap
    ref="refMap"
    title="标签展示"
    :detail-max-min="true"
    @map-loaded="onMaploaded"
  >
    <Form ref="refForm" :config="FormConfig"></Form>
    <template #detail-header> 标签列表 </template>
    <template #detail-default>
      <InlineForm ref="refFormDetail" :config="FormConfigDetail"></InlineForm>
      <div class="table-box">
        <FormTable :config="TableConfig"></FormTable>
      </div>
    </template>
  </RightDrawerMap>
</template>
<script lang="ts" setup>
import { Delete, Location, Refresh, Search } from '@element-plus/icons-vue';
import Graphic from '@arcgis/core/Graphic';
import Polygon from '@arcgis/core/geometry/Polygon';
import Polyline from '@arcgis/core/geometry/Polyline';
import Point from '@arcgis/core/geometry/Point';
import PictureMarkerSymbol from '@arcgis/core/symbols/PictureMarkerSymbol';
import SketchViewModel from '@arcgis/core/widgets/Sketch/SketchViewModel';
import {
  createPoint,
  createPolygon,
  createPolyline,
  getGraphicLayer,
  // getSubLayerIds,
  gotoAndHighLight,
  createPictureMarker,
  initDrawer,
  RgbaToArr,
  setMapCursor,
  setSymbol
} from '@/utils/MapHelper';
// import { queryLayerClassName } from '@/api/mapservice'
import { SLConfirm, SLMessage } from '@/utils/Message';
import RightDrawerMap from '../../components/common/RightDrawerMap.vue';
import {
  SymbolFillStyle,
  SymbolLineStyle,
  SymbolPointStyle
} from '../../config';
import { useUserStore } from '@/store';
import {
  AddMapLabel,
  DeleteMapLabels,
  GetMapLables
  // UpdateMapLabel
} from '@/api/mapservice/questionmark';
import { delay, formatterDateTime } from '@/utils/GlobalHelper';
import { removeSlash } from '@/utils/removeIdSlash';
import { formatDate } from '@/utils/DateFormatter';

const refMap = ref<InstanceType<typeof RightDrawerMap>>();
const refForm = ref<IFormIns>();
const refFormDetail = ref<IInlineFormIns>();
const state = reactive<{
  tabs: any[];
  loading: boolean;
  // layerInfos: any[]
  // layerIds: any[]
  curType: 'point' | 'polyline' | 'polygon';
}>({
  tabs: [],
  loading: false,
  curType: 'point'
  // layerInfos: [],
  // layerIds: []
});
const staticState: {
  view?: __esri.MapView;
  sketch?: __esri.SketchViewModel;
  sketchUpdateHnalder?: any;
  tempSketch?: __esri.SketchViewModel;
  temSketchUpdateHnalder?: any;
  graphics?: __esri.Graphic;
  graphicsLayer?: __esri.GraphicsLayer;
  graphicsTempLayer?: __esri.GraphicsLayer;
  drawer?: __esri.Draw;
  drawAction?: __esri.DrawAction;
  // tagCacheData?: any[]
} = {
  // tagCacheData: []
};
const FormConfig = reactive<IFormConfig>({
  group: [
    {
      fieldset: {
        desc: '绘制范围'
      },
      fields: [
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              isTextBtn: true,
              title: '点要素',
              iconifyIcon: 'mdi:circle-slice-8',
              click: () => initDraw('point')
            },
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              title: '线要素',
              isTextBtn: true,
              iconifyIcon: 'mdi:chart-timeline-variant',
              click: () => initDraw('polyline')
            },
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              isTextBtn: true,
              title: '面要素',
              iconifyIcon: 'mdi:shape-polygon-plus',
              click: () => initDraw('polygon')
            },
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              title: '清除要素',
              isTextBtn: true,
              iconifyIcon: 'ep:delete',
              click: () => clearGraphicsLayer()
            }
          ]
        }
      ]
    },
    {
      id: 'symbol',
      fieldset: {
        desc: '渲染样式配置'
      },
      fields: [
        {
          handleHidden: (params, query, config) => {
            config.hidden = state.curType !== 'point';
          },
          type: 'radio',
          field: 'type',
          options: [
            { label: '符号', value: 'simple' },
            { label: '图片', value: 'picture' }
          ]
        },
        {
          handleHidden: (params, query, config) => {
            config.hidden =
              params.type !== 'simple' || state.curType !== 'point';
          },
          type: 'select',
          field: 'pointStyle',
          label: '样式',
          clearable: false,
          options: SymbolPointStyle
        },

        {
          handleHidden: (params, query, config) => {
            config.hidden =
              params.type !== 'simple' || state.curType !== 'point';
          },
          type: 'input-number',
          field: 'size',
          label: '符号大小'
        },

        {
          handleHidden: (params, query, config) => {
            config.hidden =
              params.type !== 'picture' || state.curType !== 'point';
          },
          type: 'avatar',
          label: '图标',
          field: 'img'
        },
        {
          handleHidden: (params, query, config) => {
            config.hidden =
              params.type !== 'picture' || state.curType !== 'point';
          },
          type: 'input-number',
          field: 'width',
          label: '宽'
        },
        {
          handleHidden: (params, query, config) => {
            config.hidden =
              params.type !== 'picture' || state.curType !== 'point';
          },
          type: 'input-number',
          field: 'height',
          label: '高'
        },
        {
          handleHidden: (params, query, config) => {
            config.hidden =
              params.type !== 'picture' || state.curType !== 'point';
          },
          type: 'input-number',
          field: 'offsetX',
          label: '偏移-x'
        },
        {
          handleHidden: (params, query, config) => {
            config.hidden =
              params.type !== 'picture' || state.curType !== 'point';
          },
          type: 'input-number',
          field: 'offsetY',
          label: '偏移-y'
        },
        {
          handleHidden: (params, query, config) => {
            config.hidden = state.curType !== 'polyline';
          },
          type: 'select',
          field: 'polylineStyle',
          label: '样式',
          clearable: false,
          options: SymbolLineStyle
        },
        {
          handleHidden: (params, query, config) => {
            config.hidden = state.curType !== 'polyline';
          },
          type: 'input-number',
          field: 'polylineWidth',
          label: '线宽'
        },
        {
          handleHidden: (params, query, config) => {
            config.hidden = state.curType !== 'polygon';
          },
          type: 'select',
          field: 'polygonFillStyle',
          label: '填充样式',
          clearable: false,
          options: SymbolFillStyle
        },
        {
          handleHidden: (params, query, config) => {
            config.hidden = state.curType !== 'polygon';
          },
          type: 'select',
          field: 'polygonLineStyle',
          label: '边界样式',
          clearable: false,
          options: SymbolLineStyle
        },
        {
          handleHidden: (params, query, config) => {
            config.hidden = state.curType !== 'polygon';
          },
          type: 'input-number',
          field: 'polygonLineWidth',
          label: '边界宽度'
        },
        {
          handleHidden: (params, query, config) => {
            config.hidden = state.curType !== 'polygon';
          },
          type: 'color-picker',
          field: 'polygonFillColor',
          label: '填充颜色'
        },
        {
          handleHidden: (params, query, config) => {
            config.hidden = state.curType !== 'polygon';
          },
          type: 'color-picker',
          field: 'polygonLineColor',
          label: '边界颜色'
        },
        {
          handleHidden: (params, query, config) => {
            config.hidden =
              params.type !== 'simple' || state.curType !== 'point';
          },
          type: 'color-picker',
          field: 'pointColor',
          label: '颜色'
        },
        {
          handleHidden: (params, query, config) => {
            config.hidden =
              params.type !== 'simple' || state.curType !== 'point';
          },
          type: 'color-picker',
          field: 'pointOutlineColor',
          label: '边线颜色'
        },

        {
          handleHidden: (params, query, config) => {
            config.hidden =
              params.type !== 'simple' || state.curType !== 'point';
          },
          type: 'input-number',
          field: 'pointOutlineWidth',
          label: '边线宽'
        },
        {
          handleHidden: (params, query, config) => {
            config.hidden = state.curType !== 'polyline';
          },
          type: 'color-picker',
          field: 'polylineColor',
          label: '线条颜色'
        }
      ]
    },
    {
      id: 'params',
      fieldset: {
        desc: '文本属性'
      },
      fields: [
        {
          type: 'input',
          field: 'tag',
          label: '标签名称',
          rules: [{ required: true, message: '请输入标签名称' }]
        },
        {
          type: 'textarea',
          field: 'remark',
          label: '描述'
        },
        {
          type: 'radio',
          field: 'auth',
          label: '权限',
          options: [
            { label: '仅自己可见', value: 'selfonly' },
            { label: '所有人可见', value: 'all' }
          ]
        },
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: '添加',
              styles: {
                width: '50%'
              },
              click: () => handleAdd()
            },
            {
              perm: true,
              text: '重置',
              type: 'default',
              styles: {
                width: '50%'
              },
              click: () => handleReset()
            }
          ]
        }
      ]
    }
  ],
  labelPosition: 'top',
  gutter: 12,
  defaultValue: {
    type: 'simple',
    auth: 'selfonly',
    size: 12,
    width: 100,
    height: 100,
    offsetX: 0,
    offsetY: 0,
    pointStyle: 'circle',
    pointOutlineWidth: 1,
    pointOutlineColor: 'rgba(0,255,255,1)',
    polylineStyle: 'solid',
    polylineWidth: 2,
    polygonLineStyle: 'solid',
    polygonFillStyle: 'solid',
    polygonLineWidth: 2,
    pointColor: 'rgba(0,255,255,1)',
    polylineColor: 'rgba(0,255,255,1)',
    polygonFillColor: 'rgba(0,255,255,0.2)',
    polygonLineColor: 'rgba(0,255,255,1)'
  },
  submit: async (params?: any) => {
    if (!staticState.graphics) {
      SLMessage.warning('请先绘制标签图形');
      return;
    }
    state.loading = true;
    try {
      const geometry: __esri.Geometry = staticState.graphics.geometry;
      const picAttrs =
        params.geometryType === 'point' && params.type === 'picture'
          ? {
              img: params.img,
              imgWidth: params.width,
              imgHeight: params.height,
              imgOffsetX: params.offsetX,
              imgOffsetY: params.offsetY
            }
          : {};
      const geom =
        geometry?.type === 'point'
          ? { ...geometry.toJSON(), ...picAttrs }
          : geometry?.toJSON();
      const res = await AddMapLabel({
        name: params.tag,
        description: params.remark,
        available: params.auth === 'selfonly' ? '0' : '1',
        geomtype: params.geometryType,
        geom: JSON.stringify(geom),
        style:
          params.geometryType === 'point'
            ? params.pointStyle
            : params.geometryType === 'polyline'
              ? params.polylineStyle
              : params.geometryType === 'polygon'
                ? params.polygonFillStyle + ',' + params.polygonLineStyle
                : '',
        pointcolor:
          (params.geometryType === 'point' && params.pointColor) || '',
        pointsize: (params.geometryType === 'point' && params.size) || 0,
        linecolor:
          params.geometryType === 'point'
            ? params.pointOutlineColor
            : params.geometryType === 'polyline'
              ? params.polylineColor
              : params.geometryType === 'polygon'
                ? params.polygonLineColor
                : '',
        linewidth:
          params.geometryType === 'point'
            ? params.pointOutlineWidth
            : params.geometryType === 'polyline'
              ? params.polylineWidth
              : params.geometryType === 'polygon'
                ? params.polygonLineWidth
                : '0',
        fillcolor:
          (params.geometryType === 'polygon' && params.polygonFillColor) || ''
      });
      if (res.data.code === 200) {
        SLMessage.success('添加成功');
        refreshData();
        handleReset();
      } else {
        SLMessage.error(res.data.message || '添加失败');
      }
    } catch (error) {
      SLMessage.error('添加失败');
    }
    state.loading = false;
    refMap.value?.toggleCustomDetail(true);
  }
});

const initDraw = (type: any) => {
  if (!staticState.view) return;
  state.curType = type;
  refForm.value && (refForm.value.dataForm.geometryType = type);
  setMapCursor('crosshair');

  staticState.drawer?.destroy();
  staticState.drawer = initDrawer(staticState.view);
  staticState.drawAction?.destroy();
  staticState.drawAction = staticState.drawer?.create(type);

  staticState.graphicsTempLayer?.removeAll();
  staticState.graphicsLayer?.removeAll();
  staticState.sketchUpdateHnalder?.remove();
  staticState.sketch?.destroy();
  staticState.temSketchUpdateHnalder?.remove();
  staticState.tempSketch?.destroy();

  type !== 'point' &&
    staticState.drawAction?.on(['vertex-add', 'cursor-update'], updateVertices);
  staticState.drawAction?.on('draw-complete', async (e) => {
    updateVertices(e);
    staticState.tempSketch = new SketchViewModel({
      view: staticState.view,
      layer: staticState.graphicsTempLayer
    });
    staticState.temSketchUpdateHnalder = staticState.tempSketch?.on(
      'update',
      (result: any) => {
        if (result.state === 'complete') {
          staticState.graphics = result.graphics[0];
        }
      }
    );
    setMapCursor('');
  });
};
const updateVertices = (e) => {
  const type = state.curType;
  const dataForm = refForm.value?.dataForm || {};
  if (!type) return;
  let graphic: any;
  switch (type) {
    case 'polyline':
      graphic = createPolyline(
        e.vertices,
        staticState.view?.spatialReference,
        setSymbol(type, {
          style: dataForm.polylineStyle,
          color: dataForm.polylineColor,
          width: dataForm.polylineWidth
        })
      );
      break;
    case 'polygon':
      graphic =
        e.vertices.length < 3
          ? createPolyline(
              e.vertices,
              staticState.view?.spatialReference,
              setSymbol('polyline', {
                color: dataForm.polygonLineColor,
                style: dataForm.polygonLineStyle,
                width: dataForm.polygonLineWidth
              })
            )
          : createPolygon(
              e.vertices,
              staticState.view?.spatialReference,
              setSymbol(type, {
                style: dataForm.polygonFillStyle,
                color: dataForm.polygonFillColor,
                outlineColor: dataForm.polygonLineColor,
                outlineWidth: dataForm.polygonLineWidth,
                outlineStyle: dataForm.polygonLineStyle
              })
            );
      break;
    case 'point':
      graphic =
        dataForm.type === 'picture'
          ? createPictureMarker(e.vertices[0][0], e.vertices[0][1], {
              spatialReference: staticState.view?.spatialReference,
              picUrl: dataForm.img,
              picSize: [dataForm.width || 100, dataForm.height || 100],
              xOffset: dataForm.offsetX,
              yOffset: dataForm.offsetY
            })
          : createPoint(
              e.vertices,
              staticState.view?.spatialReference,
              setSymbol('point', {
                size: dataForm.size,
                style: dataForm.pointStyle,
                color: dataForm.pointColor,
                outlineColor: dataForm.pointOutlineColor,
                outlineWidth: dataForm.pointOutlineWidth
              })
            );
      break;
    default:
      break;
  }

  staticState.graphicsTempLayer?.removeAll();
  graphic && staticState.graphicsTempLayer?.add(graphic);
  staticState.graphics = graphic;
};

const handleAdd = async () => {
  if (!staticState.graphics) {
    SLMessage.warning('请先绘制图形');
    return;
  }
  refForm.value?.Submit();
};
const handleReset = () => {
  refForm.value?.resetForm();
  clearGraphicsLayer();
};
const clearGraphicsLayer = () => {
  staticState.graphicsTempLayer?.removeAll();
  staticState.graphics?.destroy();
  staticState.graphics = undefined;
};

const FormConfigDetail = reactive<IFormConfig>({
  group: [
    {
      fields: [
        {
          type: 'radio-button',
          field: 'createuser',
          options: [
            { label: '全部', value: 'all' },
            { label: '我的', value: 'selfonly' }
          ],
          onChange: () => refreshData()
        },
        {
          type: 'input',
          label: '名称',
          field: 'name',
          labelWidth: 'auto',
          onChange: () => refreshData()
        },
        {
          type: 'daterange',
          field: 'createtime',
          label: '创建时间',
          labelWidth: 'auto',
          onChange: () => refreshData()
        },
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: '查询',
              svgIcon: shallowRef(Search),
              click: () => refreshData()
            },
            {
              perm: true,
              text: '重置',
              type: 'default',
              svgIcon: shallowRef(Refresh),
              click: () => resetInlineForm()
            },
            {
              perm: (row) => row.createuser === 'selfonly',
              text: '删除',
              type: 'danger',
              disabled: () => !TableConfig.selectList?.length,
              svgIcon: shallowRef(Delete),
              click: () => handleDeleteTags()
            }
          ]
        }
      ]
    }
  ],
  defaultValue: {
    createuser: 'all'
  }
});
const TableConfig = reactive<ITable>({
  handleSelectChange: (rows) => {
    TableConfig.selectList = rows;
  },
  // indexVisible: true,
  rowKey: 'id',
  selectable: () => {
    return refFormDetail.value?.dataForm.createuser === 'selfonly';
  },
  columns: [
    {
      width: 60,
      label: '编号',
      prop: 'id',
      align: 'center'
    },
    {
      minWidth: 120,
      label: '标签名称',
      prop: 'name',
      formItemConfig: {
        type: 'input',
        placeholder: ' ',
        disabled: () => refFormDetail.value?.dataForm.createuser !== 'selfonly'
      }
    },
    {
      width: 120,
      label: '权限',
      align: 'center',
      prop: 'available',
      formItemConfig: {
        type: 'switch',
        readonly: () => refFormDetail.value?.dataForm.createuser !== 'selfonly',
        width: 70,
        activeValue: '1',
        activeText: '所有人',
        inActiveValue: '0',
        inActiveText: '仅自己',
        inActiveColor: '#00ffff'
      }
    },
    { minWidth: 120, label: '添加人', prop: 'createuserName' },
    {
      minWidth: 160,
      label: '创建时间',
      prop: 'createtime',
      formatter(row, value) {
        return formatDate(value, formatterDateTime);
      }
    },
    {
      minWidth: 160,
      label: '最后更新时间',
      prop: 'updatetime',
      formatter(row, value) {
        return formatDate(value, formatterDateTime);
      }
    },
    {
      minWidth: 120,
      label: '描述',
      prop: 'description',
      formItemConfig: {
        type: 'input',
        placeholder: ' ',
        disabled: () => refFormDetail.value?.dataForm.createuser !== 'selfonly'
      }
    }
  ],
  dataList: [],
  pagination: {
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page;
      TableConfig.pagination.limit = size;
      refreshData();
    }
  },
  operations: [
    {
      perm: true,
      text: '更新',
      disabled: () => refFormDetail.value?.dataForm.createuser !== 'selfonly',
      svgIcon: shallowRef(Refresh),
      click: (row) => updateRow(row)
    },
    {
      perm: true,
      text: '定位',
      svgIcon: shallowRef(Location),
      type: 'success',
      click: (row) => handleLocation(row)
    }
  ]
});
const updateRow = (row) => {
  // const cacheRow = staticState.tagCacheData?.find(item => item.id === row.id)
  const submitRow: Record<string, any> = { ...row };
  // cacheRow.name === row.name && delete submitRow.name

  SLConfirm('确定提交？', '提示信息')
    .then(() => {
      AddMapLabel(submitRow)
        .then((res) => {
          if (res.data.code === 200) {
            SLMessage.success('更新成功');
            refreshData();
          } else {
            SLMessage.warning('更新失败');
          }
        })
        .catch(() => {
          SLMessage.warning('更新失败');
        });
    })
    .catch(() => {
      //
    });
};
const handleLocation = async (row) => {
  if (!row.geom) {
    SLMessage.error('当前没有位置信息，无法定位');
    return;
  }
  try {
    refMap.value?.toggleCustomDetailMaxmin('normal');
    staticState.sketchUpdateHnalder?.remove();
    staticState.sketch?.destroy();
    staticState.temSketchUpdateHnalder?.remove();
    staticState.tempSketch?.destroy();
    if (refFormDetail.value?.dataForm.createuser === 'selfonly') {
      staticState.sketch = new SketchViewModel({
        view: staticState.view,
        layer: staticState.graphicsLayer
      });
      staticState.sketch?.on('update', (result: any) => {
        if (result.state === 'complete') {
          const graphics: __esri.Graphic | undefined = result.graphics[0];
          const geometry: any = graphics?.geometry;
          const geoJson = (row.geom && JSON.parse(row.geom)) || {};
          if (geometry?.type === 'point') {
            geoJson.x = geometry.x;
            geoJson.y = geometry.y;
          } else if (geometry?.type === 'polyline') {
            geoJson.paths = geometry.paths;
          } else if (geometry?.type === 'polygon') {
            geoJson.rings = geometry.rings;
          }
          row.geom = JSON.stringify(geoJson);
        }
      });
    }
    const geometryData = JSON.parse(row.geom);
    const graphic = new Graphic({
      attributes: { id: row.id }
    });
    if (geometryData.rings?.length) {
      graphic.geometry = new Polygon({
        rings: geometryData.rings,
        spatialReference:
          geometryData.spatialReference || staticState.view?.spatialReference
      });
      graphic.symbol = setSymbol('polygon', {
        color: RgbaToArr(row.fillcolor, [0, 255, 255, 0.2]),
        outlineWidth: row.linewidth || 1,
        outlineColor: RgbaToArr(row.linecolor, [0, 255, 255, 1]),
        style: row.style?.split(',')[0] || 'solid',
        outlineStyle: row.style?.split(',')[1] || 'solid'
      });
    } else if (geometryData.paths?.length) {
      graphic.geometry = new Polyline({
        paths: geometryData.paths,
        spatialReference:
          geometryData.spatialReference || staticState.view?.spatialReference
      });
      graphic.symbol = setSymbol('polyline', {
        color: RgbaToArr(row.linecolor, [0, 255, 255, 1]),
        width: row.linewidth || 1,
        style: row.style || 'solid'
      });
    } else if (geometryData.x) {
      graphic.geometry = new Point({
        x: geometryData.x,
        y: geometryData.y,
        spatialReference:
          geometryData.spatialReference || staticState.view?.spatialReference
      });
      if (geometryData.img !== undefined) {
        graphic.symbol = new PictureMarkerSymbol({
          url: geometryData.img,
          height: geometryData.imgHeight || 100,
          width: geometryData.imgWidth || 100,
          xoffset: geometryData.imgOffsetX || 0,
          yoffset: geometryData.imgOffsetY || 0
        });
      } else {
        graphic.symbol = setSymbol('point', {
          color: RgbaToArr(row.pointcolor, [0, 255, 255, 1]),
          width: row.pointsize || 12,
          outlineColor: RgbaToArr(row.linecolor, [0, 255, 255, 1]),
          outlineWidth: row.linewidth || 1,
          style: row.style || 'circle'
        });
      }
    }
    // const oldG = staticState.graphicsLayer?.graphics.find(
    //   item => item.attributes.id === row.id
    // )
    // if (oldG) {
    //   oldG.geometry = graphic.geometry
    //   oldG.symbol = graphic.symbol
    // } else {
    //   staticState.graphicsLayer
    // }
    staticState.graphicsLayer?.removeAll();
    staticState.graphicsTempLayer?.removeAll();
    staticState.graphicsLayer?.add(graphic);
    await delay(500);
    gotoAndHighLight(staticState.view, graphic, {
      avoidHighlight: true
    });
  } catch (error) {
    SLMessage.error('位置信息错误，无法定位');
  }
};
const handleDeleteTags = () => {
  SLConfirm('确定删除？', '提示信息')
    .then(() => {
      const ids = TableConfig.selectList?.map((item) => item.id);
      DeleteMapLabels(ids)
        .then((res) => {
          if (res.data.code === 200) {
            SLMessage.success('删除成功');
            refreshData();
          } else {
            SLMessage.error('删除失败');
          }
        })
        .catch(() => {
          SLMessage.error('删除失败');
        });
    })
    .catch(() => {
      //
    });
};
const refreshData = async () => {
  TableConfig.loading = true;
  const userStore = useUserStore();
  const query = refFormDetail.value?.dataForm || {};
  try {
    const res = await GetMapLables({
      page: TableConfig.pagination.page || 1,
      size: TableConfig.pagination.limit || 20,
      createuser:
        query.createuser === 'selfonly'
          ? userStore.user?.id?.id && removeSlash(userStore.user.id.id)
          : undefined,
      name: query.name,
      // available: query.createuser === 'all' ? '1' : undefined,
      beginTime:
        query.createtime?.length === 2 ? query.createtime[0] : undefined,
      endTime: query.createtime?.length === 2 ? query.createtime[1] : undefined
    });
    const data = res.data?.data;
    TableConfig.dataList = data?.data || [];
    // staticState.tagCacheData = res.data?.result?.rows || []
    TableConfig.pagination.total = data?.total || 0;
  } catch (error: any) {
    SLMessage.error(error.message);
  }
  TableConfig.loading = false;
};
const resetInlineForm = () => {
  refFormDetail.value?.resetForm();
};
const onMaploaded = async (view) => {
  staticState.view = view;
  refMap.value?.toggleCustomDetail(true);
  staticState.graphicsTempLayer = getGraphicLayer(staticState.view, {
    id: 'display-tags-temp',
    title: '标签临时绘制'
  });
  staticState.graphicsLayer = getGraphicLayer(staticState.view, {
    id: 'display-tags',
    title: '标签'
  });
  refreshData();
};
onBeforeUnmount(() => {
  staticState.drawAction?.destroy();
  staticState.drawer?.destroy();
  staticState.sketch?.destroy();
  staticState.tempSketch?.destroy();
  staticState.graphicsLayer?.destroy();
  staticState.graphicsTempLayer?.destroy();
  staticState.temSketchUpdateHnalder?.remove();
  staticState.sketchUpdateHnalder?.remove();
});
</script>
<style lang="scss" scoped>
.table-box {
  height: calc(100% - 50px);
}
</style>
