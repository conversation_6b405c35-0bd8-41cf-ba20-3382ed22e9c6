import{j as $,e as F,a as K,S as b,b as E}from"./Point-WxyopZva.js";import{b as P}from"./cimAnalyzer-CMgqZsaO.js";import{b2 as I,T as v,R as S,a$ as L,aO as A}from"./index-r0dFAfgr.js";import{d1 as O,eo as x}from"./MapView-DaoQedLH.js";import{p as U}from"./visualVariablesUtils-0WgcmuMn.js";import{S as z}from"./enums-L38xj_2E.js";import{x as B,o as k,n as j,E as H,a as J}from"./Matcher-v9ErZwmD.js";import{p as N}from"./BaseProcessor-CfOnUN16.js";import"./fontUtils-BuXIMW9g.js";import"./BidiEngine-CsUYIMdL.js";import"./GeometryUtils-B7ExOJII.js";import"./enums-B5k73o5q.js";import"./alignmentUtils-CkNI7z7C.js";import"./definitions-826PWLuy.js";import"./number-CoJp78Rz.js";import"./Rect-CUzevAry.js";import"./callExpressionWithFeature-DgtD4TSq.js";import"./quantizationUtils-DtI9CsYu.js";import"./floatRGBA-PQQNbO39.js";import"./widget-BcWKanF2.js";import"./pe-B8dP0-Ut.js";import"./color-DAS1c3my.js";import"./enums-BDQrMlcz.js";import"./VertexElementDescriptor-BOD-G50G.js";import"./visualVariablesUtils-7_6yXvXo.js";import"./tileUtils-B7X19rIS.js";import"./libtess-lH4Jrtkh.js";import"./MaterialKey-BYd7cMLJ.js";import"./GeometryUtils-BRRfazic.js";import"./earcut-BJup91r2.js";import"./TurboLine-CDscS66C.js";import"./ExpandedCIM-C1laM-_7.js";class V{constructor(e){this._remoteClient=e,this._resourceMap=new Map,this._inFlightResourceMap=new Map,this.geometryEngine=null,this.geometryEnginePromise=null}destroy(){}async fetchResource(e,s){const r=this._resourceMap,i=r.get(e);if(i)return i;let a=this._inFlightResourceMap.get(e);if(a)return a;try{a=this._remoteClient.invoke("tileRenderer.fetchResource",{url:e},{...s}),this._inFlightResourceMap.set(e,a),a.then(o=>(this._inFlightResourceMap.delete(e),r.set(e,o),o))}catch(o){return $(o)?null:{width:0,height:0}}return a}getResource(e){return this._resourceMap.get(e)??null}}function D(t,e){return(!t.minScale||t.minScale>=e)&&(!t.maxScale||t.maxScale<=e)}function R(t){const e=t.message,s={message:{data:{},tileKey:e.tileKey,tileKeyOrigin:e.tileKeyOrigin,version:e.version},transferList:new Array};for(const r in e.data){const i=e.data[r];if(s.message.data[r]=null,S(i)){const a=i.stride,o=i.indices.slice(0),n=i.vertices.slice(0),c=i.records.slice(0),d={stride:a,indices:o,vertices:n,records:c,metrics:I(i.metrics,h=>h.slice(0))};s.transferList.push(o,n,c),s.message.data[r]=d}}return s}let w=class extends N{constructor(){super(...arguments),this.type="symbol",this._matchers={feature:null,aggregate:null},this._bufferData=new Map,this._bufferIds=new Map}initialize(){this.handles.add([this.tileStore.on("update",this.onTileUpdate.bind(this))]),this._resourceManagerProxy=new V(this.remoteClient)}destroy(){this._resourceManagerProxy.destroy()}get supportsTileUpdates(){return!0}forEachBufferId(t){this._bufferIds.forEach(e=>{e.forEach(t)})}async update(t,e){var i;const s=e.schema.processors[0];if(s.type!=="symbol")return;const r=O(this._schema,s);(x(r,"mesh")||x(r,"target"))&&(t.mesh=!0,(i=t.why)==null||i.mesh.push("Symbology changed"),this._schema=s,this._factory=this._createFactory(s),this._factory.update(s,this.tileStore.tileScheme.tileInfo))}onTileMessage(t,e,s,r){return b(r),this._onTileData(t,e,s,r)}onTileClear(t){const e={clear:!0};return this._bufferData.delete(t.key.id),this._bufferIds.delete(t.key.id),this.remoteClient.invoke("tileRenderer.onTileData",{tileKey:t.id,data:e})}onTileError(t,e,s){const r=s.signal,i={tileKey:t.id,error:e};return this.remoteClient.invoke("tileRenderer.onTileError",i,{signal:r})}onTileUpdate(t){for(const e of t.removed)this._bufferData.has(e.key.id)&&this._bufferData.delete(e.key.id),this._bufferIds.has(e.key.id)&&this._bufferIds.delete(e.key.id);for(const e of t.added)this._bufferData.forEach(s=>{for(const r of s)r.message.tileKey===e.id&&this._updateTileMesh("append",e,R(r),[],!1,!1,null)})}_addBufferData(t,e){var s;this._bufferData.has(t)||this._bufferData.set(t,[]),(s=this._bufferData.get(t))==null||s.push(R(e))}_createFactory(t){const{geometryType:e,objectIdField:s,fields:r}=this.service,i=(d,h)=>this.remoteClient.invoke("tileRenderer.getMaterialItems",d,h),a={geometryType:e,fields:r,spatialReference:E.fromJSON(this.spatialReference)},o=new B(i,this.tileStore.tileScheme.tileInfo),{matcher:n,aggregateMatcher:c}=t.mesh;return this._store=o,this._matchers.feature=k(n,o,a,this._resourceManagerProxy),this._matchers.aggregate=I(c,d=>k(d,o,a,this._resourceManagerProxy)),new j(e,s,o)}async _onTileData(t,e,s,r){var m;b(r);const{type:i,addOrUpdate:a,remove:o,clear:n,end:c}=e,d=!!this._schema.mesh.sortKey;if(!a){const u={type:i,addOrUpdate:null,remove:o,clear:n,end:c,sort:d};return this.remoteClient.invoke("tileRenderer.onTileData",{tileKey:t.id,data:u},r)}const h=this._processFeatures(t,a,s,r,(m=e.status)==null?void 0:m.version);try{const u=await h;if(v(u)){const l={type:i,addOrUpdate:null,remove:o,clear:n,end:c,sort:d};return this.remoteClient.invoke("tileRenderer.onTileData",{tileKey:t.id,data:l},r)}const f=[];for(const l of u){let p=!1;const g=l.message.bufferIds,y=t.key.id,M=l.message.tileKey;if(y!==M&&S(g)){if(!this.tileStore.get(M)){this._addBufferData(y,l),f.push(l);continue}let _=this._bufferIds.get(M);_||(_=new Set,this._bufferIds.set(M,_));const C=Array.from(g);for(const T of C){if(_.has(T)){p=!0;break}_.add(T)}}p||(this._addBufferData(y,l),f.push(l))}await Promise.all(f.map(l=>{const p=t.key.id===l.message.tileKey,g=p?e.remove:[],y=p&&e.end;return this._updateTileMesh(i,t,l,g,y,!!e.clear,r.signal)}))}catch(u){this._handleError(t,u,r)}}async _updateTileMesh(t,e,s,r,i,a,o){const n=t,c=s.message.tileKey,d=!!this._schema.mesh.sortKey;c!==e.key.id&&(i=!1);const h=I(s,l=>l.message),m=I(s,l=>l.transferList)||[],u={type:n,addOrUpdate:h,remove:r,clear:a,end:i,sort:d},f={transferList:A(m)||[],signal:o};return b(f),this.remoteClient.invoke("tileRenderer.onTileData",{tileKey:c,data:u},f)}async _processFeatures(t,e,s,r,i){if(v(e)||!e.hasFeatures)return null;const a={transform:t.transform,hasZ:!1,hasM:!1},o=this._factory,n={viewingMode:"",scale:t.scale},c=await this._matchers.feature,d=await this._matchers.aggregate;b(r);const h=this._getLabelInfos(t,e);return await o.analyze(e.getCursor(),this._resourceManagerProxy,c,d,a,n),b(r),this._writeFeatureSet(t,e,a,h,o,s,i)}_writeFeatureSet(t,e,s,r,i,a,o){const n=e.getSize(),c=this._schema.mesh.matcher.symbologyType,d=new H(t.key.id,{features:n,records:n,metrics:0},c,a,c!==z.HEATMAP,o),h={viewingMode:"",scale:t.scale},m=e.getCursor();for(;m.next();)try{const f=m.getDisplayId(),l=S(r)?r.get(f):null;i.writeCursor(d,m,s,h,t.level,l,this._resourceManagerProxy)}catch{}const u=t.tileInfoView.tileInfo.isWrappable;return d.serialize(u)}_handleError(t,e,s){if(!$(e)){const r={tileKey:t.id,error:e.message};return this.remoteClient.invoke("tileRenderer.onTileError",r,{signal:s.signal})}return Promise.resolve()}_getLabelingSchemaForScale(t){const e=this._schema.mesh.labels;if(v(e))return null;if(e.type==="subtype"){const r={type:"subtype",classes:{}};let i=!1;for(const a in e.classes){const o=e.classes[a].filter(n=>D(n,t.scale));i=i||!!o.length,r.classes[a]=o}return i?r:null}const s=e.classes.filter(r=>D(r,t.scale));return s.length?{type:"simple",classes:s}:null}_getLabels(t,e){if(e.type==="subtype"){const s=this.service.subtypeField,r=L(s,"Expected to find subtype Field"),i=t.readAttribute(r);return i==null?[]:e.classes[i]??[]}return e.classes}_getLabelInfos(t,e){const s=this._getLabelingSchemaForScale(t);if(v(s))return null;const r=new Map,i=e.getCursor();for(;i.next();){const a=i.getDisplayId(),o=[],n=U(a),c=n&&i.readAttribute("cluster_count")!==1?"aggregate":"feature",d=this._getLabels(i,s);for(const h of d){if(h.target!==c)continue;const m=i.getStorage(),u=n&&c==="feature"?m.getComputedStringAtIndex(i.readAttribute("referenceId"),h.fieldIndex):m.getComputedStringAtIndex(a,h.fieldIndex);if(!u)continue;const f=P(u.toString()),l=f[0],p=f[1];this._store.getMosaicItem(h.symbol,J(l)).then(g=>{o[h.index]={glyphs:g.glyphMosaicItems??[],rtl:p,index:h.index}})}r.set(a,o)}return r}};w=F([K("esri.views.2d.layers.features.processors.SymbolProcessor")],w);const Te=w;export{Te as default};
