package org.thingsboard.server.dao.sql.alarmV2;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.DTO.AlarmRuleDTO;
import org.thingsboard.server.dao.model.request.AlarmRuleListRequest;
import org.thingsboard.server.dao.model.sql.alarmV2.AlarmRule;

import java.util.List;

@Mapper
public interface AlarmRuleMapper extends BaseMapper<AlarmRule> {
    IPage<AlarmRuleDTO> findList(IPage<AlarmRule> pageRequest, @Param("param") AlarmRuleListRequest request);

    void batchInsert(@Param("list") List<AlarmRule> alarmRuleList);

}
