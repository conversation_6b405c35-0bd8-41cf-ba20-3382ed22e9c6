<template>
  <span class="linear-title" :style="computedStyleVars"> <slot></slot> </span>
</template>
<script lang="ts" setup>
const props = defineProps<{
  size?: number;
  colorStart?: any;
  colorEnd?: any;
}>();
const computedStyleVars = computed(() => {
  return {
    '--linear-color-start': props.colorStart || '#ffffff',
    '--linear-color-end': props.colorEnd || '#adc2e0',
    '--linear-text-size': (props.size || 32) + 'px'
  };
});
</script>
<style lang="scss" scoped>
.linear-title {
  font-family: 'FZLanTingHeiS-H-GB';
  font-style: normal;
  font-weight: 600;
  font-size: var(--linear-text-size);
  background: linear-gradient(
    180deg,
    var(--linear-color-start) 24.19%,
    var(--linear-color-end) 79.05%
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
  // text-fill-color: transparent;
  word-break: keep-all;
  &::before {
    content: var(--shadow-text, attr(data-text));
    position: absolute;
    z-index: -1;
    text-shadow: 0 0 0.2em var(--shadow-color, rgba(0, 183, 255, 0.6));
  }
}
</style>
