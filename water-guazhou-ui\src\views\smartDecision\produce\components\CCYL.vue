<template>
  <div class="chart-wrapper">
    <VChart :option="state.option"></VChart>
  </div>
</template>
<script lang="ts" setup>
import { graphic } from 'echarts'
// import { padStart } from 'lodash-es'
import { dayjs } from 'element-plus'
import { GetFactoryPressureList } from '@/api/mapservice/dma/statistics'

// const generateXData = () => {
//   return Array.from({ length: 24 }).map((item, i) => {
//     return padStart(i.toString(), 2, '0') + ':00'
//   })
// }

const state = reactive<{
  option: any
}>({
  option: {
    tooltip: {
      trigger: 'axis',

      axisPointer: {
        lineStyle: {
          color: '#57617B'
        }
      },
      formatter(params) {
        let relVal = params[0].name
        for (let i = 0; i < params.length; i++) {
          relVal
            += '<br/>'
            + params[i].marker
            + params[i].seriesName
            + ' : '
            + params[i].value
            + '(MPa)'
        }
        return relVal
      }
    },
    legend: {
      textStyle: {
        color: '#fff'
      }
    },
    grid: {
      left: 20,
      right: 20,
      top: 40,
      bottom: 20,
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        boundaryGap: false,
        axisLine: {
          lineStyle: {
            color: '#57617B'
          }
        },
        data: []
      }
    ],
    yAxis: [
      {
        name: '万m³',
        type: 'value',
        axisTick: {
          show: false
        },
        axisLine: {
          lineStyle: {
            color: '#57617B'
          }
        },
        axisLabel: {
          margin: 10,
          fontSize: 14,
          color: '#57617B'
        },
        splitLine: {
          lineStyle: {
            color: '#57617B'
          }
        }
      }
    ],
    series: []
  }
})

const refreshData = () => {
  const params = {
    start: dayjs().startOf('day').format('x'),
    end: dayjs().endOf('day').format('x'),
    queryType: 'day'
  }
  GetFactoryPressureList(params)
    .then(res => {
      const series: any[] = []
      res.data.data.tableInfo.forEach(element => {
        // x坐标处理
        if (element.columnName === '数据时间') {
          state.option.xAxis[0].data = res.data.data.tableDataList.map(
            i => i[element.columnValue]
          )
          return
        }
        const item = {
          name: element.columnName,
          unit: 'cs',
          type: 'line',
          symbol: 'none',
          smooth: true,
          symbolSize: 10,
          lineStyle: {
            width: 1
          },
          areaStyle: {
            color: new graphic.LinearGradient(
              0,
              0,
              0,
              1,
              [
                {
                  offset: 0,
                  color: 'rgba(137, 189, 27, 0.3)'
                },
                {
                  offset: 0.8,
                  color: 'rgba(137, 189, 27, 0)'
                }
              ],
              false
            ),
            shadowColor: 'rgba(0, 0, 0, 0.1)',
            shadowBlur: 10
          },
          itemStyle: {
            color: 'rgb(137,189,27)'
          },
          data: res.data.data.tableDataList.map(i => i[element.columnValue])
        }
        series.push(item)
      })

      state.option.series = series
    })
    .catch(() => {
      //
    })
}

onMounted(() => {
  refreshData()
})
</script>
<style lang="scss" scoped>
.chart-wrapper {
  width: 100%;
  height: 100%;
}
</style>
