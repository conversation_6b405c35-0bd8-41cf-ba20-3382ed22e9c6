{"title": "Temperature & Humidity Demo Dashboard", "configuration": {"description": "Demo dashboard for sample applications that upload temperature and humidity received from DHT11 or DHT22 sensors", "widgets": {"03e06986-1c50-e9e4-267c-2bae930ad9a2": {"isSystemType": true, "bundleAlias": "digital_gauges", "typeAlias": "digital_thermometer", "type": "latest", "title": "New widget", "sizeX": 5, "sizeY": 5, "config": {"datasources": [{"type": "entity", "dataKeys": [{"name": "temperature", "type": "timeseries", "label": "temperature", "color": "#2196f3", "settings": {}, "_hash": 0.3720839051412099}], "name": "DHT11", "entityAliasId": "63a93238-c13f-4403-4bcc-9ccc86bd6a62"}], "timewindow": {"realtime": {"timewindowMs": 60000}}, "showTitle": false, "backgroundColor": "#000000", "color": "rgba(0, 0, 0, 0.87)", "padding": "0px", "settings": {"maxValue": 50, "donutStartAngle": 90, "showValue": true, "showMinMax": true, "gaugeWidthScale": 1, "levelColors": ["#304ffe", "#7e57c2", "#ff4081", "#d32f2f"], "refreshAnimationType": "<>", "refreshAnimationTime": 700, "startAnimationType": "<>", "startAnimationTime": 700, "titleFont": {"family": "RobotoDraft", "size": 12, "style": "normal", "weight": "500"}, "labelFont": {"family": "RobotoDraft", "size": 8, "style": "normal", "weight": "500"}, "valueFont": {"family": "Segment7Standard", "style": "normal", "weight": "500", "size": 18}, "minMaxFont": {"family": "Segment7Standard", "size": 12, "style": "normal", "weight": "500"}, "dashThickness": 1.5, "decimals": 0, "minValue": 0, "units": "°C", "gaugeColor": "#333333", "neonGlowBrightness": 35, "gaugeType": "donut", "showTitle": false}, "title": "Temperature"}, "row": 0, "col": 0, "id": "03e06986-1c50-e9e4-267c-2bae930ad9a2"}, "88808eb1-d381-9970-c852-e3499df68bd8": {"isSystemType": true, "bundleAlias": "digital_gauges", "typeAlias": "digital_vertical_bar", "type": "latest", "title": "New widget", "sizeX": 3, "sizeY": 5, "config": {"datasources": [{"type": "entity", "dataKeys": [{"name": "humidity", "type": "timeseries", "label": "humidity", "color": "#2196f3", "settings": {}, "_hash": 0.9492802776509441}], "name": "DHT11", "entityAliasId": "63a93238-c13f-4403-4bcc-9ccc86bd6a62"}], "timewindow": {"realtime": {"timewindowMs": 60000}}, "showTitle": false, "backgroundColor": "#000000", "color": "rgba(0, 0, 0, 0.87)", "padding": "0px", "settings": {"maxValue": 100, "donutStartAngle": 90, "showValue": true, "showMinMax": true, "gaugeWidthScale": 0.75, "levelColors": ["#3d5afe", "#f44336"], "refreshAnimationType": "<>", "refreshAnimationTime": 700, "startAnimationType": "<>", "startAnimationTime": 700, "titleFont": {"family": "RobotoDraft", "size": 12, "style": "normal", "weight": "500"}, "labelFont": {"family": "RobotoDraft", "size": 8, "style": "normal", "weight": "500"}, "valueFont": {"family": "Segment7Standard", "style": "normal", "weight": "500", "size": 14}, "minMaxFont": {"family": "Segment7Standard", "size": 8, "style": "normal", "weight": "normal", "color": "#cccccc"}, "neonGlowBrightness": 20, "decimals": 0, "showUnitTitle": true, "gaugeColor": "#171a1c", "gaugeType": "verticalBar", "showTitle": false, "minValue": 0, "dashThickness": 1.2}, "title": "<PERSON><PERSON><PERSON><PERSON>"}, "row": 0, "col": 5, "id": "88808eb1-d381-9970-c852-e3499df68bd8"}}, "states": {"default": {"name": "<PERSON><PERSON><PERSON>", "root": true, "layouts": {"main": {"widgets": {"03e06986-1c50-e9e4-267c-2bae930ad9a2": {"sizeX": 5, "sizeY": 5, "row": 0, "col": 0}, "88808eb1-d381-9970-c852-e3499df68bd8": {"sizeX": 3, "sizeY": 5, "row": 0, "col": 5}}, "gridSettings": {"backgroundColor": "#eeeeee", "color": "rgba(0,0,0,0.870588)", "columns": 24, "margins": [10, 10], "backgroundSizeMode": "100%"}}}}}, "entityAliases": {"63a93238-c13f-4403-4bcc-9ccc86bd6a62": {"id": "63a93238-c13f-4403-4bcc-9ccc86bd6a62", "alias": "DHT11", "filter": {"type": "entityName", "resolveMultiple": false, "entityType": "DEVICE", "entityNameFilter": "DHT11 Demo Device"}}}, "timewindow": {"displayValue": "", "selectedTab": 0, "realtime": {"interval": 1000, "timewindowMs": 60000}, "history": {"historyType": 0, "interval": 1000, "timewindowMs": 60000, "fixedTimewindow": {"startTimeMs": 1498653790019, "endTimeMs": 1498740190019}}, "aggregation": {"type": "AVG", "limit": 200}}, "settings": {"stateControllerId": "default", "showTitle": true, "showDashboardsSelect": true, "showEntitiesSelect": true, "showDashboardTimewindow": true, "showDashboardExport": true, "toolbarAlwaysOpen": false}}, "name": "Temperature & Humidity Demo Dashboard"}