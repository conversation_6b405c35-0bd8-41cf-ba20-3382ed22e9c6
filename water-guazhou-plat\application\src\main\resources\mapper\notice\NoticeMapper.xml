<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.notice.NoticeMapper">

    <select id="getNoticeByid" resultType="org.thingsboard.server.dao.model.sql.notice.NoticeDomain">
        SELECT n.id,n.content,n.type,n.sub_user_name,n.sub_time from tb_notice as n WHERE id = #{id}
    </select>
    <delete id="deleteNotice">
        DELETE from tb_notice WHERE id = #{noticeId}
    </delete>

    <insert id="savaNotice">
        INSERT INTO tb_notice(id,type,other_file,content,tenant_id,sub_time,sub_user_name)
        values (#{notice.id},#{notice.type},#{notice.other_file},#{notice.content},#{notice.sub_user_name},now(),Now())
    </insert>

    <select id="getList" resultType="org.thingsboard.server.dao.model.sql.notice.NoticeDomain">
        SELECT n.id,n.content,n.type,n.sub_user_name,n.sub_time from tb_notice as n
        <where>
            <if test="type != null">
                type = #{type},
            </if>
            <if test="beginTime != or and endTime != null">
                time between #{beginTime} and #{endTime}
            </if>

        </where>
        ORDER BY n.sub_time DESC
        offset (#{page} - 1) * #{size} limit #{size}


    </select>

    <select id="getListCount" resultType="int">

    </select>

    <update id="updateNotice">
        update tb_notice
        <set>
            <if test="notice.type != null">
                type = #{notice.type},
            </if>
            <if test="notice.content != null">
                content = #{notice.content},
            </if>
            <if test="notice.other_file != null">
                other_file = #{notice.other_file},
            </if>
            <if test="notice.tenant_id != null">
                tenant_id = #{notice.tenant_id},
            </if>
        </set>
        where id = #{notice.id}
    </update>
</mapper>