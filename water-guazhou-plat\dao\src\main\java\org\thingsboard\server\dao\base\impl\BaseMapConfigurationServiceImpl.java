package org.thingsboard.server.dao.base.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.base.BaseMapConfiguration;
import org.thingsboard.server.dao.base.IBaseMapConfigurationService;
import org.thingsboard.server.dao.sql.base.BaseMapConfigurationMapper;
import org.thingsboard.server.dao.util.imodel.query.base.BaseMapConfigurationPageRequest;

import java.util.List;
import java.util.UUID;

/**
 * 平台管理-底图配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-22
 */
@Service
public class BaseMapConfigurationServiceImpl implements IBaseMapConfigurationService {

    @Autowired
    private BaseMapConfigurationMapper baseMapConfigurationMapper;

    /**
     * 查询平台管理-底图配置
     *
     * @param id 平台管理-底图配置主键
     * @return 平台管理-底图配置
     */
    @Override
    public BaseMapConfiguration selectBaseMapConfigurationById(String id) {
        return baseMapConfigurationMapper.selectBaseMapConfigurationById(id);
    }

    /**
     * 查询平台管理-底图配置列表
     *
     * @param baseMapConfiguration 平台管理-底图配置
     * @return 平台管理-底图配置
     */
    @Override
    public IPage<BaseMapConfiguration> selectBaseMapConfigurationList(BaseMapConfigurationPageRequest baseMapConfiguration) {
        return baseMapConfigurationMapper.selectBaseMapConfigurationList(baseMapConfiguration);
    }

    /**
     * 新增平台管理-底图配置
     *
     * @param baseMapConfiguration 平台管理-底图配置
     * @return 结果
     */
    @Override
    public int insertBaseMapConfiguration(BaseMapConfiguration baseMapConfiguration) {
        baseMapConfiguration.setId(UUID.randomUUID().toString().replace("-", ""));
        return baseMapConfigurationMapper.insertBaseMapConfiguration(baseMapConfiguration);
    }

    /**
     * 修改平台管理-底图配置
     *
     * @param baseMapConfiguration 平台管理-底图配置
     * @return 结果
     */
    @Override
    public int updateBaseMapConfiguration(BaseMapConfiguration baseMapConfiguration) {
        return baseMapConfigurationMapper.updateBaseMapConfiguration(baseMapConfiguration);
    }

    /**
     * 批量删除平台管理-底图配置
     *
     * @param ids 需要删除的平台管理-底图配置主键
     * @return 结果
     */
    @Override
    public int deleteBaseMapConfigurationByIds(List<String> ids) {
        return baseMapConfigurationMapper.deleteBaseMapConfigurationByIds(ids);
    }

    /**
     * 删除平台管理-底图配置信息
     *
     * @param id 平台管理-底图配置主键
     * @return 结果
     */
    @Override
    public int deleteBaseMapConfigurationById(String id) {
        return baseMapConfigurationMapper.deleteBaseMapConfigurationById(id);
    }
}
