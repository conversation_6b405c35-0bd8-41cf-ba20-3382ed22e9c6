package org.thingsboard.server.dao.feignService;

import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.project.ProjectTreeVO;
import org.thingsboard.server.dao.model.sql.ProjectEntity;

import java.util.List;

@Component
@FeignClient("base-service")
public interface ProjectApi {
    @GetMapping("api/project/{id}")
    ProjectEntity findById(@PathVariable String id);

    @PostMapping("api/project")
    ProjectEntity save(@RequestBody ProjectEntity entity) throws ThingsboardException;

    @PutMapping("api/project")
    ProjectEntity update(@RequestBody ProjectEntity entity);

    @DeleteMapping("api/project/{id}")
    ProjectEntity delete(@PathVariable String id);

    @GetMapping("api/project/children/{id}")
    List<ProjectTreeVO> findChildrenById(@PathVariable String id);

    @GetMapping("api/project/root")
    List<ProjectTreeVO> findByRootProject() throws ThingsboardException;

}
