//package org.thingsboard.server.controller.maintainCircuit.circuit;
//
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.*;
//import org.thingsboard.server.common.data.UUIDConverter;
//import org.thingsboard.server.common.data.exception.ThingsboardException;
//import org.thingsboard.server.controller.base.BaseController;
//import org.thingsboard.server.dao.maintainCircuit.circuit.StationCircuitTaskService;
//import org.thingsboard.server.dao.model.request.StationCircuitTaskListRequest;
//import org.thingsboard.server.dao.model.sql.maintainCircuit.circuit.StationCircuitTask;
//import org.thingsboard.server.dao.util.imodel.response.IstarResponse;
//
//import java.util.List;
//
//@RestController
//@RequestMapping("api/stationCircuitTask")
//public class StationCircuitTaskController extends BaseController {
//
//    @Autowired
//    private StationCircuitTaskService stationCircuitTaskService;
//
//    @GetMapping("list")
//    public IstarResponse findList(StationCircuitTaskListRequest request) throws ThingsboardException {
//        request.setTenantId(UUIDConverter.fromTimeUUID(getTenantId().getId()));
//        return IstarResponse.ok(stationCircuitTaskService.findList(request));
//    }
//
//    @PostMapping
//    public IstarResponse save(@RequestBody StationCircuitTask entity) throws ThingsboardException {
//        entity.setTenantId(UUIDConverter.fromTimeUUID(getTenantId().getId()));
//        stationCircuitTaskService.save(entity);
//
//        return IstarResponse.ok();
//    }
//
//    @DeleteMapping("remove")
//    public IstarResponse remove(@RequestBody List<String> ids) {
//        stationCircuitTaskService.remove(ids);
//        return IstarResponse.ok();
//    }
//
//    @PostMapping("receive")
//    public IstarResponse taskReceive(@RequestBody StationCircuitTask task) throws ThingsboardException {
//        try {
//            stationCircuitTaskService.receive(task.getId(), getCurrentUser());
//        } catch (ThingsboardException e) {
//            return IstarResponse.error(e.getMessage());
//        }
//
//        return IstarResponse.ok();
//    }
//
//    @PostMapping("complete")
//    public IstarResponse taskComplete(@RequestBody StationCircuitTask task) throws ThingsboardException {
//        try {
//            stationCircuitTaskService.complete(task.getId(), getCurrentUser());
//        } catch (ThingsboardException e) {
//            return IstarResponse.error(e.getMessage());
//        }
//        return IstarResponse.ok();
//    }
//
//    @PostMapping("audit")
//    public IstarResponse taskAudit(@RequestBody StationCircuitTask task) throws ThingsboardException {
//        try {
//            stationCircuitTaskService.audit(task, getCurrentUser());
//        } catch (ThingsboardException e) {
//            return IstarResponse.error(e.getMessage());
//        }
//        return IstarResponse.ok();
//    }
//
//    @PostMapping("resend")
//    public IstarResponse taskResend(@RequestBody StationCircuitTask task) throws ThingsboardException {
//        stationCircuitTaskService.resend(task, getCurrentUser());
//        return IstarResponse.ok();
//    }
//
//
//}
