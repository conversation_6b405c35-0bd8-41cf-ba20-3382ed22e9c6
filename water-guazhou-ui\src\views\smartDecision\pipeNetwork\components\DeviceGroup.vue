<template>
  <ul class="device-group">
    <li
      v-for="(item, i) in state.listData"
      :key="i"
      class="device-group-item"
    >
      <div class="img">
        <el-image
          :fit="'contain'"
          :src="item.img"
        ></el-image>
      </div>
      <div class="info">
        <div class="count">
          {{ item.value }}
        </div>
        <div class="text">
          {{ item.name }}
        </div>
      </div>
    </li>
  </ul>
</template>
<script lang="ts" setup>
import { transNumberUnit } from '@/utils/GlobalHelper'

const getDeviceGroupImage = (name: string | URL) => {
  const href = new URL(`../imgs/${name}`, import.meta.url)?.href
  return href
}
const props = defineProps<{
  devieData: {
    pipeLength: number
    waterQuality: number
    pressure: number
    bigUser: number
    secondary: number
    flow: number
  }
}>()
const state = computed(() => {
  const pipeLength = transNumberUnit(props.devieData?.pipeLength ?? 0)
  return {
    listData: [
      {
        img: getDeviceGroupImage('7.png'),
        value: pipeLength.value.toFixed(2) + pipeLength.unit,
        name: '管线长度(米)'
      },
      {
        img: getDeviceGroupImage('8.png'),
        value: props.devieData.waterQuality || 0,
        name: '水质监测点(个)'
      },
      {
        img: getDeviceGroupImage('9.png'),
        value: props.devieData.pressure || 0,
        name: '测压点(个)'
      },
      {
        img: getDeviceGroupImage('10.png'),
        value: props.devieData.bigUser || 0,
        name: '大用户远传(个)'
      },
      {
        img: getDeviceGroupImage('11.png'),
        value: props.devieData.secondary || 0,
        name: '泵站(个)'
      },
      {
        img: getDeviceGroupImage('12.png'),
        value: props.devieData.flow || 0,
        name: '测流点(个)'
      }
    ]
  }
})
</script>
<style lang="scss" scoped>
.device-group {
  user-select: none;
  list-style-type: none;
  display: flex;
  flex-direction: column;
  margin: 0;
  padding: 0;
  .device-group-item {
    width: 204px;
    height: 80px;
    margin-bottom: 16px;
    background-color: #153b80;
    background: url(../../imgs/tech_rect.png) 0 0 / 100% 100% no-repeat;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    &:last-child {
      margin-bottom: 0;
    }
    .img {
      width: 80px;
      height: 80px;
      padding: 10px;
      .el-image {
        width: 100%;
        height: 100%;
      }
    }
    .info {
      height: 100%;
      padding-left: 10px;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      justify-content: center;
      .count {
        font-family: 'DIN';
        font-style: normal;
        font-weight: 700;
        font-size: 22px;
        line-height: 27px;
        color: #65ffbe;
      }
      .text {
        font-family: 'PingFang SC';
        font-style: normal;
        font-weight: 600;
        font-size: 12px;
        line-height: 17px;
        /* identical to box height */

        color: #ffffff;
      }
    }
  }
}
</style>
