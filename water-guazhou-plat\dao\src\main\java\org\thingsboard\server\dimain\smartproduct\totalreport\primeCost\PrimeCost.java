package org.thingsboard.server.dimain.smartproduct.totalreport.primeCost;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

/**
 * CREATE TABLE prime_cost
 * (
 *     id             varchar(50) PRIMARY KEY,
 *     factory        varchar(50),
 *     product        varchar(50),
 *     power_cost     numeric(13, 4),
 *     water_amount     numeric(13, 4),
 *     medical_amount decimal(13, 4),
 *     medical_price  decimal(13, 4),
 *     ym             varchar(50)
 * );
 *
 *
 * COMMENT ON COLUMN prime_cost.id IS 'id';
 * COMMENT ON COLUMN prime_cost.factory IS '工厂名称';
 * COMMENT ON COLUMN prime_cost.product IS '药品名称';
 * COMMENT ON COLUMN prime_cost.power_cost IS '电费，元';
 * COMMENT ON COLUMN prime_cost.water_cost IS '用水量，立方米';
 * COMMENT ON COLUMN prime_cost.medical_amount IS '用药量，吨';
 * COMMENT ON COLUMN prime_cost.medical_price IS '药价，元/吨';
 * COMMENT ON COLUMN prime_cost.ym IS '年月信息';
 *
 *
 * create table factory_month_water_price
 * (
 *     ym           varchar(5) not null
 *         primary key,
 *     factory_name varchar(63),
 *     price        numeric(10, 2)
 * );
 *
 *
 */
@SuppressWarnings("ALL")
@Getter
@Setter
public class PrimeCost {
    // id
    private String id;

    // 工厂名称
    private String factory;

    // 药品名称
    private String product;

    // 电费，元
    private BigDecimal powerCost;

    // 用水量，立方米
    private BigDecimal waterAmount;

    // 用药量，吨
    private BigDecimal medicalAmount;

    // 药价，元/吨
    private BigDecimal medicalPrice;

    // 前端传入的记录时间
    private String ym;

    // 记录时间
    private Date recordTime;

    private BigDecimal price;



}