import{m as r}from"./index-r0dFAfgr.js";function i(e){const t={page:e.page||1,size:e.size||10};return e.waterPlantName&&(t.waterPlantName=e.waterPlantName),e.startTime&&(t.startTime=e.startTime),e.endTime&&(t.endTime=e.endTime),e.dataSource&&(t.dataSource=e.dataSource),r({url:"/api/waterPlant/pressure",method:"get",params:t})}function n(e){return r({url:"/api/waterPlant/pressure",method:"post",data:e})}function s(e){return r({url:"/api/waterPlant/pressure/import",method:"post",data:e})}function u(e){const t={page:e.page||1,size:e.size||-1};return e.waterPlantName&&(t.waterPlantName=e.waterPlantName),e.startTime&&(t.startTime=e.startTime),e.endTime&&(t.endTime=e.endTime),e.dataSource&&(t.dataSource=e.dataSource),r({url:"/api/waterPlant/pressure/export",method:"get",params:t,responseType:"blob"})}function o(e){return r({url:`/api/waterPlant/pressure/${e}`,method:"delete"})}function l(e){return r({url:"/api/station/list",method:"get",params:{...e,type:"水厂"}})}export{l as a,o as d,u as e,i as g,s as i,n as s};
