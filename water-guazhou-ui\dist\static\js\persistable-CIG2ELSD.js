const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["static/js/pe-B8dP0-Ut.js","static/js/index-r0dFAfgr.js","static/css/index-ByiGXvnH.css","static/js/Point-WxyopZva.js"])))=>i.map(i=>d[i]);
import{a3 as _}from"./index-r0dFAfgr.js";import{i as b}from"./multiOriginJSONSupportUtils-C0wm8_Yw.js";import{Y as P,Z as y,V as N,J as x}from"./pe-B8dP0-Ut.js";import{n as O}from"./widget-BcWKanF2.js";import{F as R,G as $,H as S}from"./Point-WxyopZva.js";import{t as f}from"./resourceExtension-DfSw5lpL.js";import{b5 as j,b6 as d,b7 as F,b8 as J,b9 as A,ba as E}from"./MapView-DaoQedLH.js";function M(r){const o=(r==null?void 0:r.origins)??[void 0];return(t,n)=>{const s=T(r,t,n);for(const a of o){const i=R(t,a,n);for(const e in s)i[e]=s[e]}}}function T(r,o,t){if((r==null?void 0:r.type)==="resource")return V(r,o,t);switch((r==null?void 0:r.type)??"other"){case"other":return{read:!0,write:!0};case"url":{const{read:n,write:s}=E;return{read:n,write:s}}}}function V(r,o,t){const n=$(o,t);return{type:String,read:(s,a,i)=>{const e=j(s,a,i);return n.type===String?e:typeof n.type=="function"?new n.type({url:e}):void 0},write:{writer(s,a,i,e){if(!e||!e.resources)return typeof s=="string"?void(a[i]=d(s,e)):void(a[i]=s.write({},e));const m=H(s),p=d(m,{...e,verifyItemRelativeUrls:e&&e.verifyItemRelativeUrls?{writtenUrls:e.verifyItemRelativeUrls.writtenUrls,rootPath:void 0}:void 0},F.NO),c=n.type!==String&&(!b(this)||e&&e.origin&&this.originIdOf(t)>S(e.origin)),u={object:this,propertyName:t,value:s,targetUrl:p,dest:a,targetPropertyName:i,context:e,params:r};e&&e.portalItem&&p&&!P(p)?c?B(u):D(u):e&&e.portalItem&&(p==null||J(p)!=null||y(p)||c)?g(u):a[i]=p}}}}function g(r){const{targetUrl:o,params:t,value:n,context:s,dest:a,targetPropertyName:i}=r;if(!s.portalItem)return;const e=A(o),m=(e==null?void 0:e.filename)??O(),p=(t==null?void 0:t.prefix)??(e==null?void 0:e.prefix),c=v(n,o,s),u=N(p,m),w=`${u}.${f(c)}`,l=s.portalItem.resourceFromPath(w);y(o)&&s.resources&&s.resources.pendingOperations.push(G(o).then(I=>{l.path=`${u}.${f(I)}`,a[i]=l.itemRelativeUrl}).catch(()=>{}));const U=(t==null?void 0:t.compress)??!1;s.resources&&h({...r,resource:l,content:c,compress:U,updates:s.resources.toAdd}),a[i]=l.itemRelativeUrl}function B(r){const{context:o,targetUrl:t,params:n,value:s,dest:a,targetPropertyName:i}=r;if(!o.portalItem)return;const e=o.portalItem.resourceFromPath(t),m=v(s,t,o),p=f(m),c=x(e.path),u=(n==null?void 0:n.compress)??!1;p===c?(o.resources&&h({...r,resource:e,content:m,compress:u,updates:o.resources.toUpdate}),a[i]=t):g(r)}function D({context:r,targetUrl:o,dest:t,targetPropertyName:n}){r.portalItem&&r.resources&&(r.resources.toKeep.push({resource:r.portalItem.resourceFromPath(o),compress:!1}),t[n]=o)}function h({object:r,propertyName:o,updates:t,resource:n,content:s,compress:a}){t.push({resource:n,content:s,compress:a,finish:i=>{K(r,o,i)}})}function v(r,o,t){return typeof r=="string"?{url:o}:new Blob([JSON.stringify(r.toJSON(t))],{type:"application/json"})}async function G(r){const o=(await _(async()=>{const{default:n}=await import("./pe-B8dP0-Ut.js").then(s=>s.a6);return{default:n}},__vite__mapDeps([0,1,2,3]))).default,{data:t}=await o(r,{responseType:"blob"});return t}function H(r){return r==null?null:typeof r=="string"?r:r.url}function K(r,o,t){typeof r[o]=="string"?r[o]=t.url:r[o].url=t.url}export{M as g};
