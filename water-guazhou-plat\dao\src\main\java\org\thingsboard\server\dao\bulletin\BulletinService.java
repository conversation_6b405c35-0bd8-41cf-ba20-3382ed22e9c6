package org.thingsboard.server.dao.bulletin;

import org.thingsboard.server.common.data.Tenant;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.model.sql.BulletinDataEntity;
import org.thingsboard.server.dao.model.sql.OriginDataEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/3 14:01
 */
public interface BulletinService {


    BulletinDataEntity save(BulletinDataEntity bulletinDataEntity);

    BulletinDataEntity findLastByTenantId(TenantId tenantId);

    List<BulletinDataEntity> findByTenant(TenantId tenantId);

}
