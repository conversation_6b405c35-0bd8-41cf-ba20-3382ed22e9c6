package org.thingsboard.server.dao.sql.operateLog;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.model.sql.OperateLogEntity;

import javax.transaction.Transactional;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/5/28 14:33
 */
@Service
@Transactional
public class OperateLogDao {

    @Autowired
    private OperateLogRepository operateLogRepository;


    public List<OperateLogEntity> findByTenantId(TenantId tenantId) {
        return operateLogRepository.findByTenantId(UUIDConverter.fromTimeUUID(tenantId.getId()));
    }


    public List<OperateLogEntity> findByPhone(String phone) {
        return operateLogRepository.findByPhone(phone);
    }


    public List<OperateLogEntity> findByCaptcha(String captcha) {
        return operateLogRepository.findByCaptcha(captcha);
    }


    public OperateLogEntity findByCaptchaAndInvalid(String captcha, String invalid) {
        return operateLogRepository.findByCaptchaAndInvalid(captcha, invalid);
    }

    public OperateLogEntity save(OperateLogEntity operateLogEntity) {
        return operateLogRepository.save(operateLogEntity);
    }

    public OperateLogEntity findByPhoneAndInvalidAndCaptcha(String phone, String invalid,String captcha){
        return operateLogRepository.findByPhoneAndInvalidAndCaptcha(phone, invalid, captcha);
    }

    public OperateLogEntity findByPhoneAndInvalid(String phone, String invalid) {
        return operateLogRepository.findByPhoneAndInvalid(phone, invalid);
    }

    public List<OperateLogEntity> findByTenantIdAndTime(TenantId tenantId, long start, long end) {
        return operateLogRepository.findByTenantIdAndCreateTime(UUIDConverter.fromTimeUUID(tenantId.getId()), start, end);
    }
}
