<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.smartPipe.PartitionFlowMeterMapper">

    <select id="getList" resultType="org.thingsboard.server.dao.model.sql.smartPipe.dma.PartitionFlowMeter">
        select a.*
        from tb_pipe_partition_flow_meter a
        <where>
            <if test="param.partitionId != null and param.partitionId != ''">
                and a.partition_id = #{param.partitionId}
            </if>
            <if test="param.type != null and param.type != ''">
                and a.type like '%' || #{param.type} || '%'
            </if>
            <if test="param.code != null and param.code != ''">
                and a.code like '%' || #{param.code} || '%'
            </if>
            <if test="param.meterType != null and param.meterType != ''">
                and a.meter_type like '%' || #{param.meterType} || '%'
            </if>
            <if test="param.brand != null and param.brand != ''">
                and a.brand like '%' || #{param.brand} || '%'
            </if>
            and a.tenant_id = #{param.tenantId}
        </where>
        order by a.create_time desc

    </select>
</mapper>