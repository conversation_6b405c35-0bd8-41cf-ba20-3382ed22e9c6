import{d as B,c as x,r as h,bC as G,S as q,b as u,o as W,Q as H,g as O,h as P,F as k,p as C,q as w,i as I,_ as R,aq as U,X as j,C as A}from"./index-r0dFAfgr.js";import{_ as J}from"./InlineForm.vue_vue_type_style_index_0_lang-s-ANlzyw.js";import{p as V}from"./AnimatedLinesLayer-B2VbV4jv.js";import $ from"./RightDrawerMap-D5PhmGFO.js";import{s as v}from"./FeatureHelper-Da16o0mu.js";import"./pe-B8dP0-Ut.js";import"./MapView-DaoQedLH.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./Point-WxyopZva.js";import{g as K,a as Q}from"./LayerHelper-Cn-iiqxI.js";import"./project-DUuzYgGl.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./geometryEngineBase-BhsKaODW.js";import{L as F,E as X}from"./tools-DUuxC1oj.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./ArcView-DpMnCY82.js";import"./ViewHelper-BGCZjxXH.js";import"./fieldconfig-Bk3o1wi7.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./ArcStationWarning-BF9YrSzF.js";/* empty css                         */import"./useWaterPoint-Bv0z6ym6.js";import"./DateFormatter-Bm9a68Ax.js";import"./zhandian-YaGuQZe6.js";import"./useStation-DJgnSZIA.js";import"./DrawerBox-CLde5xC8.js";import"./SideDrawer-CBntChyn.js";import"./PipeDetail-CTBPYFJW.js";import"./Search-NSrhrIa_.js";import"./FormTableColumnFilter-BT7pLXIC.js";import"./QueryHelper-ILO3qZqg.js";import"./config-fy91bijz.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./pipe-nogVzCHG.js";import"./ArcBRTools-BO92yznB.js";import"./PipeLength.vue_vue_type_script_setup_true_lang-BZfUsvDf.js";import"./arcWidgetButton-0glIxrt7.js";import"./StatisticsHelper-D-s_6AyQ.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./wfsUtils-DXofo3da.js";import"./usePipelineGroup-Ba1pmWz_.js";import"./GroupLayer-DhhN3FyN.js";import"./lazyLayerLoader-DbM9sT1W.js";import"./WFSLayer-1TtJp3nx.js";import"./clientSideDefaults-VQhQaYxh.js";import"./QueryEngineCapabilities-Dk3NJwmm.js";import"./wfsUtils-Du031XaC.js";import"./geojson-MBFu2-HZ.js";import"./xmlUtils-CtUoQO7q.js";import"./ListWindow-WS05QqV0.js";import"./PopLayout-BP55MvL7.js";import"./PoiSearchV2-D7yNeLuv.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./URLHelper-B9aplt5w.js";import"./useLayerList-DmEwJ-ws.js";import"./useScaleBar-Beed-z91.js";const Y={class:"table-box"},Z=B({__name:"ExportShapefile",setup(tt){const _=x(),S=x(),l=x(),o=h({layerIds:[],layerInfos:[],refreshCount:0}),i={},n=h({labelPosition:"top",group:[{fieldset:{desc:"绘制工具"},fields:[{type:"btn-group",btns:[{perm:!0,text:"",type:"default",size:"large",title:"绘制多边形",disabled:()=>n.submitting===!0,iconifyIcon:"mdi:shape-polygon-plus",click:()=>b("polygon")},{perm:!0,text:"",type:"default",size:"large",title:"绘制矩形",disabled:()=>n.submitting===!0,iconifyIcon:"ep:crop",click:()=>b("rectangle")},{perm:!0,text:"",type:"default",size:"large",title:"绘制圆形",disabled:()=>n.submitting===!0,iconifyIcon:"mdi:ellipse-outline",click:()=>b("circle")},{perm:!0,text:"",type:"default",size:"large",title:"清除图形",disabled:()=>n.submitting===!0,iconifyIcon:"ep:delete",click:()=>g()}]}]},{id:"layerid",fieldset:{desc:"选择图层"},fields:[{type:"tree",options:[],showCheckbox:!0,field:"layerid",rules:[{required:!0,message:"请选择图层"}],nodeKey:"value"}]},{fieldset:{desc:"任务描述"},fields:[{type:"input",field:"taskname",label:"名称",rules:[{required:!0,message:"请输入名称"}]},{type:"textarea",field:"description",label:"描述"},{type:"btn-group",btns:[{perm:!0,text:"导出",styles:{width:"50%"},loading:()=>n.submitting===!0,iconifyIcon:"ep:download",click:()=>{var t;return(t=l.value)==null?void 0:t.Submit()}},{styles:{width:"50%"},perm:!0,text:"重置",type:"default",iconifyIcon:"ep:refresh",click:()=>{var t;g(),(t=l.value)==null||t.resetForm()}}]}]}],submit:()=>E()}),m=h({dataList:[],columns:[{minWidth:160,label:"任务名称",prop:"taskname"},{minWidth:160,label:"任务描述",prop:"description"},{minWidth:160,label:"创建时间",prop:"start_time"},{minWidth:160,label:"结束时间",prop:"end_time"},{width:100,align:"center",label:"导出状态",prop:"exp_status",tag:!0,tagColor:t=>t.exp_status==="导出失败"?"#990000":t.exp_status==="导出成功"?"#009900":"#409eff"}],operations:[{perm:t=>t.exp_status==="导出成功",text:"下载",iconifyIcon:"ep:download",click:t=>L(t)},{perm:t=>t.exp_status!=="导出成功",text:t=>t.exp_status==="导出中"?"导出中":"刷新",loading:t=>t.exp_status==="导出中",iconifyIcon:"ep:refresh",click:t=>{clearTimeout(o.timer),o.refreshCount=0,y(t.task_id)}},{perm:!1,text:"删除",type:"danger",disabled:t=>t.del_status==="不可用",iconifyIcon:"ep:delete",click:()=>D()}],pagination:{refreshData:({page:t,size:e})=>{m.pagination.page=t,m.pagination.limit=e,f()}}}),L=t=>{G(window.SITE_CONFIG.GIS_CONFIG.gisApi+"/ExportResult/"+t.task_id+".zip",t.taskname)},D=()=>{q("确定删除吗？","删除提示").then(()=>{}).catch(()=>{})},M=h({labelPosition:"right",group:[{fields:[{itemContainerStyle:{marginBottom:"8px"},labelWidth:60,type:"input",label:"任务名",field:"taskname"},{itemContainerStyle:{marginBottom:"8px"},type:"btn-group",btns:[{perm:!0,text:"搜索",click:()=>f()}]}]}]}),f=async()=>{var e,r,p;const t=await F({pageindex:m.pagination.page||1,pagesize:m.pagination.limit||20,exporttype:"1",taskname:(e=l.value)==null?void 0:e.dataForm.taskname});m.dataList=((r=t.data.result)==null?void 0:r.rows)||[],m.pagination.total=((p=t.data.result)==null?void 0:p.total)||0},y=async t=>{if(!t)return;const e=m.dataList.find(r=>r.task_id===t);try{const r=await F({pageindex:1,pagesize:1,exporttype:"1",taskid:e.task_id});if(r.data.result.rows.length){const p=r.data.result.rows[0];e.exp_status=p.exp_status,e.end_time=p.end_time,e.del_status=p.del_status,e.exp_status==="导出中"?o.timer=setTimeout(()=>{y(t)},1e3):e.exp_status==="未开始"&&o.refreshCount<10&&(o.timer=setTimeout(()=>{o.refreshCount++,y(t)},1e3))}}catch{e.exp_status="导出失败"}},g=()=>{var t,e;(t=i.graphicsLayer)==null||t.removeAll(),i.graphic=void 0,(e=i.sketch)==null||e.cancel()},b=t=>{var e;g(),(e=i.sketch)==null||e.create(t)},E=async()=>{var e,r,p;if(!i.graphic){u.warning("请先绘制图形");return}const t=((e=l.value)==null?void 0:e.dataForm)||{};if(!((r=t.layerid)!=null&&r.length)){u.warning("请先选择图层");return}n.submitting=!0;try{const s=await X({exporttype:1,geomjson:JSON.stringify(i.graphic.geometry.toJSON()),taskname:t.taskname,description:t.description,outputlayers:(p=t.layerid)==null?void 0:p.map(c=>{var d;return(d=o.layerInfos.find(a=>a.layerid===c))==null?void 0:d.layername}).filter(c=>c).join(",")});s.data.code===1e4?(console.log(s.data.message),u.success("导出成功"),_.value&&(_.value.dataForm.taskname=t.taskname),await f(),clearTimeout(o.timer),o.refreshCount=0,y(s.data.result)):u.error(s.data.message)}catch{u.error("导出失败")}n.submitting=!1},T=()=>{var t,e;i.graphicsLayer=K(i.view,{id:"export-map",title:"导出Shapefile"}),i.sketch=new V({view:i.view,layer:i.graphicsLayer,polygonSymbol:v("polygon"),pointSymbol:v("point"),polylineSymbol:v("polyline")}),i.sketchCreateHandler=(t=i.sketch)==null?void 0:t.on("create",r=>{r.state==="complete"&&(i.graphic=r.graphic)}),i.sketchUpdateHandler=(e=i.sketch)==null?void 0:e.on("update",r=>{r.state==="complete"&&(i.graphic=r.graphics[0])})},z=async()=>{var s,c,d;o.layerIds=Q(i.view);const t=await j(o.layerIds);o.layerInfos=((c=(s=t.data)==null?void 0:s.result)==null?void 0:c.rows)||[];const e=(d=n.group.find(a=>a.id==="layerid"))==null?void 0:d.fields[0],r=o.layerInfos.filter(a=>a.geometrytype==="esriGeometryPoint").map(a=>({label:a.layername,value:a.layerid,data:a})),p=o.layerInfos.filter(a=>a.geometrytype==="esriGeometryPolyline").map(a=>({label:a.layername,value:a.layerid,data:a}));e&&(e.options=[{label:"管点类",value:-1,children:r},{label:"管线类",value:-2,children:p}]),l.value&&(l.value.dataForm.layerid=o.layerIds||[])},N=t=>{var e;i.view=t,z(),T(),(e=S.value)==null||e.toggleCustomDetail(!0)};return W(()=>{f()}),H(()=>{var t,e,r;(t=i.sketchCreateHandler)==null||t.remove(),(e=i.sketchUpdateHandler)==null||e.remove(),g(),(r=i.sketch)==null||r.destroy()}),(t,e)=>{const r=R,p=J,s=U;return O(),P($,{ref_key:"refMap",ref:S,title:"导出Shapefile","detail-max-min":!0,"hide-detail-close":!0,onMapLoaded:N},{"detail-header":k(()=>e[0]||(e[0]=[C("span",null,"导出Shapefile任务列表",-1)])),"detail-default":k(()=>[w(p,{ref_key:"refSearch",ref:_,config:I(M)},null,8,["config"]),C("div",Y,[w(s,{config:I(m)},null,8,["config"])])]),default:k(()=>[w(r,{ref_key:"refForm",ref:l,config:I(n)},null,8,["config"])]),_:1},512)}}}),ar=A(Z,[["__scopeId","data-v-ac735289"]]);export{ar as default};
