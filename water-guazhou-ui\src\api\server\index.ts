// 智慧生产=二供管理-泵房信息 /api
import request from '@/plugins/axios';

// 泵机管理列表
export function pumpManageList(params?: any) {
  return request({
    url: '/api/sp/pumpManage',
    method: 'get',
    params
  });
}

// 新增泵机管理
export function addPumpManage(params?: any) {
  return request({
    url: '/api/sp/pumpManage',
    method: 'post',
    data: params
  });
}

// 修改泵机管理
export function editPumpManage(params?: any) {
  return request({
    url: `/api/sp/pumpManage/${params.id}`,
    method: 'patch',
    data: params
  });
}

// 删除泵泵机管理
export function delPumpManage(id: string) {
  return request({
    url: `/api/sp/pumpManage/${id}`,
    method: 'delete'
  });
}

// 批量添加泵泵机管理
export function batchAddPumpManage(params?: any) {
  return request({
    url: `/api/sp/pumpManage/batch`,
    method: 'post',
    data: params
  });
}

// 泵机管理模板
export function pumpManageTemplate() {
  return request({
    url: '/api/sp/pumpManage/excel/template',
    responseType: 'blob',
    method: 'get'
  });
}
// 泵机管理导出
export function pumpManageExport(params?: any) {
  return request({
    url: '/api/sp/pumpManage/excel/export',
    method: 'get',
    responseType: 'blob',
    params
  });
}

// xxxxxx

// 设备台账管理列表
export function deviceManageList(params?: any) {
  return request({
    url: '/api/sp/pipeLineDeviceManage',
    method: 'get',
    params
  });
}

// 新增设备台账管理
export function adddeviceManage(params?: any) {
  return request({
    url: '/api/sp/pipeLineDeviceManage',
    method: 'post',
    data: params
  });
}

// 修改设备台账管理
export function editdeviceManage(params?: any) {
  return request({
    url: `/api/sp/pipeLineDeviceManage/${params.id}`,
    method: 'patch',
    data: params
  });
}

// 删除设备台账管理
export function deldeviceManage(id: string) {
  return request({
    url: `/api/sp/pipeLineDeviceManage/${id}`,
    method: 'delete'
  });
}

// 批量添加设备台账管理
export function batchAdddeviceManage(params?: any) {
  return request({
    url: `/api/sp/pipeLineDeviceManage/batch`,
    method: 'post',
    data: params
  });
}

// 设备台账管理模板
export function deviceManageTemplate() {
  return request({
    url: '/api/sp/pipeLineDeviceManage/excel/template',
    responseType: 'blob',
    method: 'get'
  });
}
// 设备台账管理导出
export function deviceManageExport(params?: any) {
  return request({
    url: '/api/sp/pipeLineDeviceManage/excel/export',
    method: 'get',
    responseType: 'blob',
    params
  });
}
// 专家库
// 专家列表
export function expertList(params?: any) {
  return request({
    url: '/api/sp/expertInfo',
    method: 'get',
    params
  });
}

/**
 * 新增专家信息
 *
 * @param params 包含专家详细信息的对象，包括但不限于：
 * - id: 唯一标识符
 * - name: 专家名称
 * - gender: 性别
 * - phone: 联系电话
 * - email: 邮箱
 * - educationLevel: 学历
 * - professionalTitle: 职称
 * - professionalTitleTime: 职称评定时间
 * - industrySector: 行业领域
 * - industryTime: 入行时间
 * - deptName: 工作单位
 * - deptPhone: 单位电话
 * - jobHistory: 工作历史
 * - honorHistory: 荣誉历史
 * - academicHistory: 学术历史
 * - createTime: 创建时间
 * - tenantId: 租户ID
 *
 * @returns 返回请求结果，通常包含新增操作的成功与否以及相关提示信息
 */
export function addExpert(params?: any) {
  return request({
    url: '/api/sp/expertInfo',
    method: 'post',
    data: params
  });
}

// 修改专家信息
export function editExpert(params?: any) {
  return request({
    url: `/api/sp/expertInfo/save`,
    method: 'post',
    data: params
  });
}

// 删除专家信息
export function delExpert(id: string) {
  return request({
    url: `/api/sp/expertInfo/${id}`,
    method: 'delete'
  });
}
