package org.thingsboard.server.dao.util.imodel.query.smartService;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.DTO.WorkOrderVisitMsgDTO;

import java.util.List;

@Getter
@Setter
public class SendVisitMsgRequest {
    private WorkOrderListMsgRequest workOrderListMsgRequest;

    private List<WorkOrderVisitMsgDTO> workOrderMsgList;

    private String templateId;

    private String sendUser;

    private String exportType;

}
