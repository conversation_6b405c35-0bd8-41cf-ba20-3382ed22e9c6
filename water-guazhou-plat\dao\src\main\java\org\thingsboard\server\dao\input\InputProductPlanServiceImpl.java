package org.thingsboard.server.dao.input;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.utils.DateUtils;
import org.thingsboard.server.dao.model.sql.input.InputProductPlan;
import org.thingsboard.server.dao.model.sql.input.InputWuliao;
import org.thingsboard.server.dao.sql.input.InputProductPlanRepository;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class InputProductPlanServiceImpl implements InputProductPlanService {

    @Autowired
    private InputProductPlanRepository inputProductPlanRepository;

    @Override
    public void save(InputProductPlan inputProductPlan) {
        inputProductPlanRepository.save(inputProductPlan);
    }

    @Override
    public List<InputProductPlan> findMonthReport(String month) {
        Date monthStart = DateUtils.str2Date(month, "yyyy-MM");
        Calendar instance = Calendar.getInstance();
        instance.setTime(monthStart);
        instance.set(Calendar.DAY_OF_MONTH, instance.getActualMaximum(Calendar.DAY_OF_MONTH));

        Date monthEnd = instance.getTime();

        List<InputProductPlan> productPlanList = inputProductPlanRepository.findByTimeBetweenOrderByTimeAsc(monthStart, monthEnd);

        if (productPlanList != null && productPlanList.size() > 0) {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            for (InputProductPlan productPlan : productPlanList) {
                productPlan.setTimeStr(dateFormat.format(productPlan.getTime()));
            }
        }

        return productPlanList;
    }

    @Override
    public void remove(List<String> ids) {
        for (String id : ids) {
            inputProductPlanRepository.delete(id);
        }
    }
}
