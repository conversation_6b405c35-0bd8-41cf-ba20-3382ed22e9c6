package org.thingsboard.server.controller.base;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.common.data.security.Authority;
import org.thingsboard.server.dao.model.sql.TenantApplicationEntity;
import org.thingsboard.server.dao.tenant.TenantApplicationService;
import org.thingsboard.server.service.security.model.SecurityUser;
import org.thingsboard.server.service.utils.Aes;
import org.thingsboard.server.service.utils.CryptoUtil;

import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("api/tenantApplication")
public class TenantApplicationController extends BaseController {

    @Autowired
    private TenantApplicationService tenantApplicationService;


    @GetMapping("list")
    public PageData<TenantApplicationEntity> list(@RequestParam int page, @RequestParam int size, @RequestParam String tenantId) throws ThingsboardException {
        return tenantApplicationService.findList(tenantId, page, size);
    }

    @GetMapping("all")
    public List<TenantApplicationEntity> all(@RequestParam String tenantId,
                                             @RequestParam(required = false, defaultValue = "PC") String resourceType) throws ThingsboardException {
        SecurityUser currentUser = getCurrentUser();
        Authority authority = currentUser.getAuthority();
        if ("ALL".equals(resourceType)) {
            if (authority.equals(Authority.CUSTOMER_USER)) {
                // 查询当前用户有权限的企业应用
                return tenantApplicationService.findListByUser(currentUser.getId());
            } else {
                return tenantApplicationService.findAll(tenantId);
            }
        }
        if (authority.equals(Authority.CUSTOMER_USER)) {
            // 查询当前用户有权限的企业应用
            return tenantApplicationService.findListByUser(resourceType, currentUser.getId());
        } else {
            return tenantApplicationService.findAll(resourceType, tenantId);
        }
    }

    @PostMapping("save")
    public void save(@RequestBody TenantApplicationEntity entity) throws ThingsboardException {
        if (StringUtils.isBlank(entity.getId())) {
            entity.setCreateTime(new Date());
        }

        tenantApplicationService.saveOrUpdate(entity);
    }

    @DeleteMapping("delete")
    public void remove(@RequestBody List<String> ids) {
        tenantApplicationService.delete(ids);
    }

    @GetMapping("selectedMenuList")
    public List<String> selectedMenuList(@RequestParam String tenantApplicationId) {
        return tenantApplicationService.selectedMenuList(tenantApplicationId);
    }

    @PostMapping("getLoginParams")
    public Object getLoginParams(@RequestBody JSONObject params) {
        JSONObject result = new JSONObject();

        String key = params.getString("key");
        JSONObject data = params.getJSONObject("data");
        if (key.equals("yingshou")) {
            String username = data.getString("username");
            String password = data.getString("password");

            // AES解密
            username = Aes.aesDecrypt(username);
            password = Aes.aesDecrypt(password);

            // DES加密
            result.put("username", CryptoUtil.encode(username));
            result.put("password", CryptoUtil.encode(password));

            return result;
        }

        return null;
    }




}
