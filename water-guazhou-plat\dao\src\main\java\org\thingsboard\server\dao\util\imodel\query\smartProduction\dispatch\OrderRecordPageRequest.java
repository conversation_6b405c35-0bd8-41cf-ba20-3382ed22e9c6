package org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartProduction.dispatch.OrderRecord;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;

import java.util.Date;

@Getter
@Setter
public class OrderRecordPageRequest extends AdvancedPageableQueryEntity<OrderRecord, OrderRecordPageRequest> {

    // 指令状态。待发送/待接收/待回复/已回复/已拒绝
    private String commandStatus;

    // 接收站点/接收部门
    private String receiveDeptId;

    // 发送人
    private String sendUserId;

    // 接收人
    private String receiveUserId;

    // 指令类型
    private String type;

    // 发送的指令内容
    private String sendContent;

    // 发送时间起始
    private String sendTimeFrom;

    // 发送时间结束
    private String sendTimeTo;

    public Date getSendTimeFrom() {
        return toDate(sendTimeFrom);
    }

    public Date getSendTimeTo() {
        return toDate(sendTimeTo);
    }


}
