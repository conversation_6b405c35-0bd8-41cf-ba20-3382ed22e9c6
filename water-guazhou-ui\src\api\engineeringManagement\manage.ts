// 工程管理-工程管理
import request from '@/plugins/axios';

/**
 * 分页条件获取项目类型
 * @returns
 */
export function getProjectType(params: {
  page: number;
  size: number;
  fromTime?: string;
  toTime?: string;
  code?: string;
  name?: string;
}) {
  return request({
    url: `/api/so/project/type`,
    method: 'get',
    params
  });
}

/**
 * 添加项目类型
 * @returns
 */
export function postProjectType(params: {
  id?: number;
  name: number;
  orderNum?: string;
}) {
  return request({
    url: `/api/so/project/type`,
    method: 'post',
    data: params
  });
}

/**
 * 分页条件获取项目类型
 * @returns
 */
export function getSingleProjectList(params: {
  page: number;
  size: number;
  fromTime?: string;
  toTime?: string;
  code?: string;
  name?: string;
  projectCode?: string;
  address?: string;
}) {
  return request({
    url: `/api/so/construction`,
    method: 'get',
    params
  });
}

/**
 * 添加单项工程
 * @returns
 */
export function postConstruction(params: {
  id?: string;
  code: string;
  projectCode: string;
  name: string;
  address: string;
  typeId?: string;
  createTime?: string;
  firstpartName: string;
  firstpartPhone?: string;
  detailAddress?: string;
  remark?: string;
  estimate: number;
  attachments?: string;
}) {
  return request({
    url: `/api/so/construction`,
    method: 'post',
    data: params
  });
}

/**
 * 获取工程下的设备
 * @returns
 */
export function getConstructionDevice(
  code: string,
  params: {
    page: number;
    size: number;
    fromTime?: string;
    toTime?: string;
    serialId?: string;
  }
) {
  return request({
    url: `/api/so/construction/${code}/device`,
    method: 'get',
    params
  });
}

/**
 * 工程添加设备
 * @returns
 */
export function postConstructionDevice(
  code: string,
  params: {
    serialId: string;
    amount: string;
  }[]
) {
  return request({
    url: `/api/so/construction/${code}/device`,
    method: 'post',
    data: params
  });
}

/**
 * 获取设计管理
 * @returns
 */
export function getConstructionDesign(params: {
  page: number;
  size: number;
  fromTime?: string;
  toTime?: string;
  constructionCode?: string;
  constructionName?: string;
  constructionTypeId?: string;
}) {
  return request({
    url: `/api/so/constructionDesign`,
    method: 'get',
    params
  });
}

/**
 * 导出单项工程
 * @returns
 */
export function getConstructionExport() {
  return request({
    url: `/api/so/construction/export/excel`,
    method: 'get',
    responseType: 'blob'
  });
}

/**
 * 添加设计管理/编辑
 * @returns
 */
export function postConstructionDesign(params: {
  id?: string;
  constructionCode: string;
  type: string;
  cost: string;
  pipLengthDesign: string;
  remark: string;
  attachments: string;
}) {
  return request({
    url: `/api/so/constructionDesign`,
    method: 'post',
    data: params
  });
}

/**
 * 设计管理完成
 * @returns
 */
export function postConstructionDesignComplete(constructionCode: string) {
  return request({
    url: `/api/so/constructionDesign/${constructionCode}/complete`,
    method: 'post'
  });
}

/**
 * 导出设计管理
 * @returns
 */
export function getConstructionDesignExport() {
  return request({
    url: `/api/so/constructionDesign/export/excel`,
    method: 'get',
    responseType: 'blob'
  });
}

/**
 * 获取工程预算
 * @returns
 */
export function getConstructionEstimateList(params: {
  page: number;
  size: number;
  fromTime?: string;
  toTime?: string;
  constructionCode?: string;
  constructionName?: string;
  constructionTypeId?: string;
}) {
  return request({
    url: `/api/so/constructionEstimate`,
    method: 'get',
    params
  });
}

/**
 * 添加工程预算/编辑
 * @returns
 */
export function postConstructionEstimate(params: {
  id?: string;
  constructionCode: string;
  budgeter: string;
  cost: string;
  address?: string;
  remark?: string;
  attachments?: string;
}) {
  return request({
    url: `/api/so/constructionEstimate`,
    method: 'post',
    data: params
  });
}

/**
 * 工程预算完成
 * @returns
 */
export function postConstructionEstimateComplete(constructionCode: string) {
  return request({
    url: `/api/so/constructionEstimate/${constructionCode}/complete`,
    method: 'post'
  });
}

/**
 * 导出工程预算
 * @returns
 */
export function getConstructionEstimateExport() {
  return request({
    url: `/api/so/constructionEstimate/export/excel`,
    method: 'get',
    responseType: 'blob'
  });
}

/**
 * 获取验收管理
 * @returns
 */
export function getConstructionAcceptList(params: {
  page: number;
  size: number;
  fromTime?: string;
  toTime?: string;
  constructionCode?: string;
  constructionName?: string;
  constructionTypeId?: string;
  projectStartTimeFrom?: string;
  projectStartTimeTo?: string;
}) {
  return request({
    url: `/api/so/constructionAccept`,
    method: 'get',
    params
  });
}

/**
 * 添加验收管理/编辑
 * @returns
 */
export function postConstructionAccept(params: {
  id?: '';
  constructionCode: '';
  beginTime: '';
  endTime: '';
  applicantOrganization: '';
  applicant: '';
  applicantPhone: '';
  constructOrganization: '';
  supervisorOrganization: '';
  auditOrganization: '';
  designOrganization: '';
  remark: '';
  attachments: '';
}) {
  return request({
    url: `/api/so/constructionAccept`,
    method: 'post',
    data: params
  });
}

/**
 * 验收管理完成
 * @returns
 */
export function postConstructionAcceptComplete(constructionCode: string) {
  return request({
    url: `/api/so/constructionAccept/${constructionCode}/complete`,
    method: 'post'
  });
}

/**
 * 导出验收管理
 * @returns
 */
export function getConstructionAcceptExport() {
  return request({
    url: `/api/so/constructionAccept/export/excel`,
    method: 'get',
    responseType: 'blob'
  });
}

/**
 * 获取工程结算
 * @returns
 */
export function getConstructionSettlementList(params: {
  page: number;
  size: number;
  fromTime?: string;
  toTime?: string;
  constructionCode?: string;
  constructionName?: string;
  constructionTypeId?: string;
}) {
  return request({
    url: `/api/so/constructionSettlement`,
    method: 'get',
    params
  });
}

/**
 * 添加工程结算/编辑
 * @returns
 */
export function postConstructionSettlement(params: {
  id?: '';
  constructionCode: '';
  processUser: '';
  cost: '';
  address: '';
  remark: '';
  attachments: '';
}) {
  return request({
    url: `/api/so/constructionSettlement`,
    method: 'post',
    data: params
  });
}

/**
 * 工程结算完成
 * @returns
 */
export function postConstructionSettlementComplete(constructionCode: string) {
  return request({
    url: `/api/so/constructionSettlement/${constructionCode}/complete`,
    method: 'post'
  });
}

/**
 * 导出工程结算
 * @returns
 */
export function getConstructionSettlementExport() {
  return request({
    url: `/api/so/constructionSettlement/export/excel`,
    method: 'get',
    responseType: 'blob'
  });
}

/**
 * 获取归档管理
 * @returns
 */
export function getConstructionArchiveList(params: {
  page: number;
  size: number;
  fromTime?: string;
  toTime?: string;
  constructionCode?: string;
  constructionName?: string;
  constructionTypeId?: string;
  projectStartTimeFrom?: string;
  projectStartTimeTo?: string;
}) {
  return request({
    url: `/api/so/constructionArchive`,
    method: 'get',
    params
  });
}

/**
 * 添加归档管理/编辑
 * @returns
 */
export function postConstructionArchive(params: {
  id?: '';
  constructionCode: '';
  remark: '';
  attachments: '';
}) {
  return request({
    url: `/api/so/constructionArchive`,
    method: 'post',
    data: params
  });
}

/**
 * 导出归档管理
 * @returns
 */
export function getConstructionArchiveExport() {
  return request({
    url: `/api/so/constructionArchive/export/excel`,
    method: 'get',
    responseType: 'blob'
  });
}

/**
 * 获取签证单
 * @returns
 */
export function getConstructionVisaList(params: {
  page: number;
  size: number;
  fromTime?: string;
  toTime?: string;
  constructionCode?: string;
  constructionName?: string;
  constructionTypeId?: string;
  firstPartOrganization?: string;
  supervisorOrganization?: string;
}) {
  return request({
    url: `/api/so/constructionVisa`,
    method: 'get',
    params
  });
}

/**
 * 添加签证单/编辑
 * @returns
 */
export function postConstructionVisa(params: {
  id?: string;
  code: string;
  constructionCode: string;
  budgeter: string;
  constructOrganization: string;
  address: string;
  constructTime: string;
  buildOrganization: string;
  supervisorOrganization: string;
  auditOrganization: string;
  remark: string;
  attachments: string;
}) {
  return request({
    url: `/api/so/constructionVisa`,
    method: 'post',
    data: params
  });
}

/**
 * 签证单完成
 * @returns
 */
export function postConstructionVisaComplete(constructionCode: string) {
  return request({
    url: `/api/so/constructionVisa/${constructionCode}/complete`,
    method: 'post'
  });
}

/**
 * 导出签证单详情
 * @returns
 */
export function getConstructionVisaGlobalExport() {
  return request({
    url: `/api/so/constructionVisa/export/global/excel`,
    method: 'get',
    responseType: 'blob'
  });
}

/**
 * 导出签证单
 * @returns
 */
export function getConstructionVisaExport(constructionCode: string) {
  return request({
    url: `/api/so/constructionVisa/export/excel`,
    method: 'get',
    params: { constructionCode },
    responseType: 'blob'
  });
}

/**
 * 获取合同管理
 * @returns
 */
export function getConstructionContractList(params: {
  page: number;
  size: number;
  fromTime?: string;
  toTime?: string;
  constructionCode?: string;
  constructionName?: string;
  constructionTypeId?: string;
  firstPartOrganization?: string;
  supervisorOrganization?: string;
}) {
  return request({
    url: `/api/so/constructionContract`,
    method: 'get',
    params
  });
}

/**
 * 添加签证单/编辑
 * @returns
 */
export function postConstructionContract(params: {
  id?: string;
  code: string;
  name: string;
  type?: string;
  constructionCode: string;
  firstpartOrganization: string;
  firstpartRepresentative: string;
  firstpartPhone?: string;
  secondpartOrganization: string;
  secondpartRepresentative?: string;
  secondpartPhone?: string;
  cost: number;
  workTimeBegin?: string;
  workTimeEnd?: string;
  signTime?: string;
  remark?: string;
  attachments?: string;
}) {
  return request({
    url: `/api/so/constructionContract`,
    method: 'post',
    data: params
  });
}

/**
 * 分页条件获取合同分类
 * @returns
 */
export function getConstructionContractType(params: {
  page: number;
  size: number;
  fromTime?: string;
  toTime?: string;
  code?: string;
  name?: string;
}) {
  return request({
    url: `/api/so/constructionContract/type`,
    method: 'get',
    params
  });
}

/**
 * 添加合同分类
 * @returns
 */
export function postConstructionContractType(params: {
  id?: number;
  name: number;
  orderNum?: string;
}) {
  return request({
    url: `/api/so/constructionContract/type`,
    method: 'post',
    data: params
  });
}

/**
 * 导出合同管理详情
 * @returns
 */
export function getConstructionContractExport(constructionCode: string) {
  return request({
    url: `/api/so/constructionContract/export/global/excel`,
    method: 'get',
    params: { constructionCode },
    responseType: 'blob'
  });
}

/**
 * 导出合同管理
 * @returns
 */
export function getConstructionContractGlobalExport() {
  return request({
    url: `/api/so/constructionContract/export/global/excel`,
    method: 'get',
    responseType: 'blob'
  });
}

/**
 * 合同管理完成
 * @returns
 */
export function postConstructionContractComplete(constructionCode: string) {
  return request({
    url: `/api/so/constructionContract/${constructionCode}/complete`,
    method: 'post'
  });
}

/**
 * 获取费用管理
 * @returns
 */
export function getConstructionExpenseList(params: {
  page: number;
  size: number;
  fromTime?: string;
  toTime?: string;
  constructionCode?: string;
  constructionName?: string;
  constructionTypeId?: string;
}) {
  return request({
    url: `/api/so/constructionExpense`,
    method: 'get',
    params
  });
}

/**
 * 添加费用管理/编辑
 * @returns
 */
export function postConstructionExpense(params: {
  id?: string;
  code: string;
  constructionCode: string;
  contractCode: string;
  type: string;
  cost: string;
  paymentType?: string;
  approvalTime: string;
  submitFinanceTime: string;
  firstVerifyCost?: number;
  firstVerifyOrganization?: string;
  secondVerifyCost?: number;
  secondVerifyOrganization?: string;
  payeeInfo?: string;
  payeeOrganization?: string;
  remark?: string;
  attachments?: string;
}) {
  return request({
    url: `/api/so/constructionExpense`,
    method: 'post',
    data: params
  });
}

/**
 * 导出费用管理详情
 * @returns
 */
export function getconstructionExpenseExport(constructionCode: string) {
  return request({
    url: `/api/so/constructionExpense/export/excel`,
    method: 'get',
    params: { constructionCode },
    responseType: 'blob'
  });
}

/**
 * 导出费用管理
 * @returns
 */
export function getConstructionExpenseGlobalExport() {
  return request({
    url: `/api/so/constructionExpense/export/global/excel`,
    method: 'get',
    responseType: 'blob'
  });
}

/**
 * 合同管理完成
 * @returns
 */
export function postConstructionExpenseComplete(constructionCode: string) {
  return request({
    url: `/api/so/constructionExpense/${constructionCode}/complete`,
    method: 'post'
  });
}

/**
 * 获取合同管理
 * @returns
 */
export function getConstructionApplyList(params: {
  page: number;
  size: number;
  fromTime?: string;
  toTime?: string;
  constructionId?: string;
  constructionName?: string;
  constructionTypeId?: string;
}) {
  return request({
    url: `/api/so/constructionApply`,
    method: 'get',
    params
  });
}

/**
 * 实施管理完成
 * @returns
 */
export function postConstructionApplyComplete(id: string) {
  return request({
    url: `/api/so/constructionApply/${id}/complete`,
    method: 'post'
  });
}

/**
 * 添加实施管理/编辑
 * @returns
 */
export function postConstructionApply(params: {
  id?: string;
  code: string;
  constructionCode: string;
  contractCode: string;
  beginTime: string;
  endTime: string;
  principal: string;
  phone?: string;
  constructClass?: string;
  remark?: string;
}) {
  return request({
    url: `/api/so/constructionApply`,
    method: 'post',
    data: params
  });
}

/**
 * 实施管理删除
 * @returns
 */
export function deleteConstructionApply(id: string) {
  return request({
    url: `/api/so/constructionApply/${id}`,
    method: 'delete'
  });
}

/**
 * 导出实施管理
 * @returns
 */
export function getConstructionApplyGlobalExport() {
  return request({
    url: `/api/so/constructionApply/export/global/excel`,
    method: 'get',
    responseType: 'blob'
  });
}

/**
 * 导出实施管理详情
 * @returns
 */
export function getConstructionApplyExport(constructionCode: string) {
  return request({
    url: `/api/so/constructionApply/export/excel`,
    method: 'get',
    params: { constructionCode },
    responseType: 'blob'
  });
}

/**
 * 工程完成信息获取
 * @param params
 * @returns
 */
export function getConstructioncompletionInfo(constructionCode: string) {
  return request({
    url: `/api/so/construction/completionInfo/${constructionCode}`,
    method: 'get'
  });
}

/**
 * 添加设计变更/编辑
 * @returns
 */
export function postConstructionDesignAmend(params: {
  id?: string;
  code: string;
  designCode: string;
  constructionCode: string;
  type?: string;
  remark?: string;
  attachments?: string;
}) {
  return request({
    url: `/api/so/constructionDesignAmend`,
    method: 'post',
    data: params
  });
}

/**
 * 获取设计变更
 * @returns
 */
export function getConstructionDesignAmendList(params: {
  page: number;
  size: number;
  fromTime?: string;
  toTime?: string;
  designCode?: string;
  constructionCode?: string;
}) {
  return request({
    url: `/api/so/constructionDesignAmend`,
    method: 'get',
    params
  });
}

/**
 * 设计变更删除
 * @returns
 */
export function deleteConstructionDesign(id: string) {
  return request({
    url: `/api/so/constructionDesignAmend/${id}`,
    method: 'delete'
  });
}

/**
 * 获取合同变更
 * @returns
 */
export function getConstructionContractAmendList(params: {
  page: number;
  size: number;
  fromTime?: string;
  toTime?: string;
  contractCode?: string;
  constructionCode?: string;
}) {
  return request({
    url: `/api/so/constructionContractAmend`,
    method: 'get',
    params
  });
}

/**
 * 添加合同变更/编辑
 * @returns
 */
export function postConstructionContractAmend(params: {
  id?: string;
  contractCode: string;
  constructionCode?: string;
  amendDate?: string;
  remark?: string;
}) {
  return request({
    url: `/api/so/constructionContractAmend`,
    method: 'post',
    data: params
  });
}

/**
 * 合同变更删除
 * @returns
 */
export function deleteConstructionContractAmend(id: string) {
  return request({
    url: `/api/so/constructionContractAmend/${id}`,
    method: 'delete'
  });
}

/**
 * 获取简单合同信息
 * @returns
 */
export function getConstructionContractOptions(constructionCode: string) {
  return request({
    url: `/api/so/constructionContract/simple`,
    method: 'get',
    params: { constructionCode }
  });
}

/**
 * 添加设计变更/编辑
 * @returns
 */
export function postConstructionApplyFlow(params: {
  code: string;
  constructionApplyCode: string;
  workName: string;
  workStage: string;
  beginTime: string;
  endTime: string;
  headUser?: string;
  headUserPhone?: string;
  workContent?: string;
  remark?: string;
}) {
  return request({
    url: `/api/so/constructionApplyFlow`,
    method: 'post',
    data: params
  });
}

/**
 * 获取设计变更
 * @returns
 */
export function getConstructionApplyFlowList(params: {
  page: number;
  size: number;
  fromTime?: string;
  toTime?: string;
  constructionCode?: string;
  constructionName?: string;
  constructionTypeId?: string;
  constructionApplyCode?: string;
}) {
  return request({
    url: `/api/so/constructionApplyFlow`,
    method: 'get',
    params
  });
}

/**
 * 实施变更删除
 * @returns
 */
export function deleteConstructionApplyFlow(id: string) {
  return request({
    url: `/api/so/constructionApplyFlow/${id}`,
    method: 'delete'
  });
}
