package org.thingsboard.server.dao.shuiwu;

import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.shuiwu.CriterionEntity;

import java.util.List;

public interface CriterionService {
    CriterionEntity findById(String id);

    PageData<CriterionEntity> findList(int page, int size, String name, String deviceType, TenantId tenantId);

    void save(CriterionEntity entity);

    void deleteById(String id);

    List<CriterionEntity> findAll(String tenantId);

    void deleteById(List<String> ids);
}
