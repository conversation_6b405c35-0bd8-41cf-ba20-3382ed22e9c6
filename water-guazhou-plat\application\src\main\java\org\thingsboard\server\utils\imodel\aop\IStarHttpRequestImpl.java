package org.thingsboard.server.utils.imodel.aop;

import com.amazonaws.http.HttpMethodName;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.dao.util.imodel.query.IStarHttpRequest;
import org.thingsboard.server.service.security.model.SecurityUser;

import javax.servlet.http.HttpServletRequest;

public class IStarHttpRequestImpl implements IStarHttpRequest {

    private final HttpServletRequest request;

    private final String tenantId;

    private final String userId;

    private final boolean isPatch;

    public IStarHttpRequestImpl(HttpServletRequest request, SecurityUser user) {
        this.request = request;
        if (user != null) {
            tenantId = UUIDConverter.fromTimeUUID(user.getTenantId().getId());
            userId = UUIDConverter.fromTimeUUID(user.getUuidId());
        } else {
            tenantId = null;
            userId = null;
        }
        isPatch = HttpMethodName.PATCH.name().equals(request.getMethod());
    }

    @Override
    public String getUserId() {
        return userId;
    }

    @Override
    public String getTenantId() {
        return tenantId;
    }

    @Override
    public HttpServletRequest getRequest() {
        return request;
    }


    @Override
    public boolean isPatch() {
        return isPatch;
    }

}
