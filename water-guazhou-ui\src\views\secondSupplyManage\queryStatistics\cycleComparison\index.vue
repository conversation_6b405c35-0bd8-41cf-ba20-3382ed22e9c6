<!--周期对比 -->
<template>
  <div class="wrapper">
    <CardSearch
      ref="cardSearch"
      :config="cardSearchConfig"
    />
    <SLCard
      class="card"
      :title="state.chartName + (state.activeName === 'list' ? '周期对比列表' : '周期对比曲线')"
    >
      <template #right>
        <el-radio-group v-model="state.activeName">
          <el-radio-button label="echarts">
            <Icon
              style="margin-right: 1px; font-size: 16px"
              icon="clarity:line-chart-line"
            />
          </el-radio-button>
          <el-radio-button label="list">
            <Icon
              style="margin-right: 1px; font-size: 16px"
              icon="material-symbols:table"
            />
          </el-radio-button>
        </el-radio-group>
      </template>
      <div
        v-show="state.activeName === 'echarts'"
        ref="echartsDiv"
        class="chart-box"
      >
        <!-- 图表模式 -->
        <VChart
          ref="refChart"
          :theme="useAppStore().isDark ? 'dark' : 'light'"
          :option="state.chartOption"
        ></VChart>
      </div>
      <!-- 列表模式 -->
      <div v-show="state.activeName === 'list'">
        <FormTable
          ref="refCardTable"
          class="card-table"
          :config="cardTableConfig"
        />
      </div>
    </SLCard>
  </div>
</template>
<script lang="ts" setup>
import dayjs from 'dayjs'
import elementResizeDetectorMaker from 'element-resize-detector'
import { Download, Refresh } from '@element-plus/icons-vue'
import { Icon } from '@iconify/vue'
import { lineOption } from '../echartsData/echart'
import { IECharts } from '@/plugins/echart'
import { getCycleCompare } from '@/api/headwatersManage/queryStatistics'
import { GetStationAttrs } from '@/api/shuiwureports/zhandian'
import useStation from '@/hooks/station/useStation'
import { formatColumn } from '@/utils/formartColumn'
import { useAppStore } from '@/store'
import useGlobal from '@/hooks/global/useGlobal'

const { $messageWarning } = useGlobal()
const { getStationTree } = useStation()
const today = dayjs().date()
const state = reactive<{
  type: 'date' | 'week' | 'year'
  chartOption: any
  activeName: string
  chartName: string
  data: any
  stationTree: any
}>({
  type: 'date',
  chartOption: null,
  chartName: '',
  activeName: 'echarts',
  data: null,
  stationTree: []
})

watch(
  () => state.activeName,
  () => {
    if (state.activeName === 'echarts') {
      refuseChart()
    }
  }
)
const cardSearch = ref<ICardSearchIns>()
const refChart = ref<IECharts>()
const echartsDiv = ref<any>()
const refCardTable = ref<ICardTableIns>()
let tableData = reactive<any[]>([])

// 搜索栏初始化配置
const cardSearchConfig = reactive<ISearch>({
  defaultParams: {
    queryType: 'day',
    day: [dayjs().format(), dayjs().format()],
    month: [dayjs().subtract(1, 'month'), dayjs().format()]
  },

  filters: [
    {
      type: 'select-tree',
      label: '监测点:',
      field: 'attributeId',
      clearable: false,
      lazy: true,
      options: computed(() => state.stationTree) as any,
      lazyLoad: (node, resolve) => {
        if (node.level === 0) return resolve([])
        if (node.data.children?.length > 0) {
          return resolve(node.data.children)
        }

        if (node.isLeaf) return resolve([])
        if (node.data?.isLeaf) return resolve([])
        GetStationAttrs({ stationId: node.data.id }).then(res => {
          const newAttrs = res.data?.map(attr => {
            return {
              label: attr.type,
              value: '',
              id: '',
              children: attr.attrList.map(attr => {
                return {
                  label: attr.name,
                  value: attr.id,
                  id: attr.id,
                  isLeaf: true
                }
              })
            }
          })
          return resolve(newAttrs)
        })
      },
      nodeClick: (data, node) => {
        if (node.isLeaf) {
          state.chartName = data.label
          nextTick(() => {
            refreshData()
          })
        }
      }
    },
    {
      type: 'select',
      label: '比较类型:',
      field: 'queryType',
      clearable: false,
      width: '200px',
      options: [
        { label: '日分时(时间段)', value: 'day' },
        { label: '月分日(时间段)', value: 'month' }
        // { label: '日分时(时间点)', value: 'v3' },
        // { label: '月分日(时间点)', value: 'v4' }
      ],
      itemContainerStyle: {
        width: '240px'
      }
    },
    {
      type: 'daterange',
      label: '日期',
      field: 'day',
      clearable: false,
      handleHidden: (params: any, query: any, formItem: IFormItem) => {
        formItem.hidden = params.queryType === 'month'
      }
    },
    {
      type: 'monthrange',
      label: '日期',
      field: 'month',
      clearable: false,
      handleHidden: (params: any, query: any, formItem: IFormItem) => {
        formItem.hidden = params.queryType === 'day'
      }
    }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        // {
        //   perm: true,
        //   type: 'success',
        //   text: '添加',
        //   hide: (row?: any) => { return ['v3', 'v4'].indexOf(compareType.value) === -1 },
        //   click: () => refreshData(),
        //   icon: ICONS.ADD
        // },
        {
          perm: true,
          text: '查询',
          click: () => {
            cardTableConfig.pagination.page = 1
            const queryParams = (cardSearch.value?.queryParams as any) || {}
            if (queryParams.attributeId) {
              refreshData()
            } else $messageWarning('选择监测点')
          },
          icon: 'iconfont icon-chaxun'
        },
        {
          type: 'default',
          perm: true,
          text: '重置',
          svgIcon: shallowRef(Refresh),
          click: () => {
            cardSearch.value?.resetForm()
          }
        },
        {
          perm: true,
          type: 'warning',
          text: '导出',
          hide: () => {
            return state.activeName !== 'list'
          },
          svgIcon: shallowRef(Download),
          click: () => handleExport()
        }
      ]
    }
  ]
})

// 初始化列表配置数据
const cardTableConfig = reactive<ITable>({
  loading: false,
  dataList: [],
  columns: [],
  operations: [],
  pagination: {
    layout: 'total, prev, pager, next, jumper',
    refreshData: ({ page, size }) => {
      cardTableConfig.pagination.page = page
      cardTableConfig.pagination.limit = size
      cardTableConfig.dataList = tableData.slice((page - 1) * size, page * size)
    }
  }
})

// 刷新列表 模拟数据
const refreshData = () => {
  cardTableConfig.loading = true
  const queryParams = (cardSearch.value?.queryParams as any) || {}
  const [start, end] = queryParams[queryParams.queryType] || []
  const params: any = {
    attributeId: queryParams.attributeId,
    queryType: queryParams.queryType,
    start: start ? dayjs(start).startOf(queryParams.queryType).valueOf() : '',
    end: end ? dayjs(end).endOf(queryParams.queryType).valueOf() : ''
  }

  getCycleCompare(params).then(res => {
    const data = res.data?.data
    state.data = data
    // const columns = data?.tableInfo.map((item: any) => {
    //   // item.columnName.split('--').map((column: any, index: number) =>{

    //   // })
    //   return {
    //     prop: item.columnValue,
    //     label: item.columnName,
    //     unit: item.unit ? '(' + item.unit + ')' : ''
    //   }
    // })
    const columns = formatColumn(data?.tableInfo)
    tableData = data?.tableDataList as any[]
    cardTableConfig.columns = columns
    cardTableConfig.dataList = tableData?.slice(0, 20)
    cardTableConfig.pagination.total = data?.tableDataList.length
    refuseChart()
    cardTableConfig.loading = false
  })
}

const resizeChart = () => {
  refChart.value?.resize()
}
const refuseChart = () => {
  const chartOption = lineOption()
  chartOption.series = []
  const serie = {
    name: '',
    smooth: true,
    data: [],
    type: 'line',
    markPoint: {
      data: [
        {
          type: 'max',
          name: '最大值',
          label: {
            fontSize: 12,
            color: useAppStore().isDark ? '#ffffff' : '#000000'
          }
        },
        {
          type: 'min',
          name: '最小值',
          label: {
            color: useAppStore().isDark ? '#ffffff' : '#000000'
          }
        }
      ]
    },
    markLine: {
      data: [{ type: 'average', name: '平均值' }]
    }
  }
  chartOption.xAxis.data = state.data?.tableDataList.map(table => table.ts)
  state.data?.tableInfo.map(info => {
    if (info.columnValue !== 'ts') {
      const newSerie = JSON.parse(JSON.stringify(serie))
      newSerie.name = info.columnName
      chartOption.yAxis[0].name = state.chartName + (info.unit ? '(' + info.unit + ')' : '')
      newSerie.data = state.data?.tableDataList.map(table => table[info.columnValue])
      chartOption.series.push(newSerie)
    }
  })
  refChart.value?.clear()
  const options: any = { callOnAdd: true }
  const erd = elementResizeDetectorMaker(options)
  nextTick(() => {
    if (echartsDiv.value) {
      erd.listenTo(echartsDiv.value, () => {
        state.chartOption = chartOption
        resizeChart()
      })
    }
  })
}
// 导出
const handleExport = () => {
  refCardTable.value?.exportTable()
}
onBeforeMount(async () => {
  const treeData = (await getStationTree('泵站')) as any[]
  // await getStationTreeByDisabledType(treeData, ['Project', 'Station'], true, 'Station')
  state.stationTree = treeData
  console.log(' state.stationTree ', state.stationTree)
})
// onMounted(async () => {
//   const treeData = await getStationTree('泵站') as any[]
//   getStationTreeByDisabledType(treeData, ['Project', 'Station'], true, 'Station')
//   console.log('newTreeData', treeData)
//   TreeData.data = treeData as any
//   TreeData.expandNodeId = [treeData[0].id]
// })
</script>

<style lang="scss" scoped>
.card {
  height: calc(100% - 100px);
}

.card-table {
  height: calc(100vh - 270px);
  width: 100%;
}

.chart-box {
  width: 100%;
  height: calc(100vh - 280px);
}
</style>
