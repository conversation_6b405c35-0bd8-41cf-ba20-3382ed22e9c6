package org.thingsboard.server.dao.sql.smartManagement.district;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.smartManagement.district.CircuitDistrictPoint;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.district.CircuitDistrictPointPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.plan.CircuitTaskReportSaveRequest;

import java.util.List;

@Mapper
public interface CircuitDistrictPointMapper extends BaseMapper<CircuitDistrictPoint> {
    IPage<CircuitDistrictPoint> findByPage(CircuitDistrictPointPageRequest request);

    boolean update(CircuitDistrictPoint entity);

    List<CircuitTaskReportSaveRequest> selectReportTemplate(@Param("idList") List<String> idList);

    boolean hasPoint(String districtAreaId);

}
