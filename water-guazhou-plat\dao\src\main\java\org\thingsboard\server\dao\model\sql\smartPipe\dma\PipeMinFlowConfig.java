package org.thingsboard.server.dao.model.sql.smartPipe.dma;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 小流指标配置
 */
@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@TableName(ModelConstants.PIPE_MIN_FLOW_CONFIG_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class PipeMinFlowConfig {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @TableField(ModelConstants.PIPE_MIN_FLOW_CONFIG_PARTITION_ID)
    private String partitionId;

    @TableField(exist = false)
    private String partitionName;

    @TableField(exist = false)
    private String copyMeterType;

    @TableField(exist = false)
    private BigDecimal mainLineLength;

    @TableField(ModelConstants.PIPE_MIN_FLOW_CONFIG_NIGHT_FLOW_MIN)
    private BigDecimal nightFlowMin;

    @TableField(ModelConstants.PIPE_MIN_FLOW_CONFIG_NIGHT_FLOW_MAX)
    private BigDecimal nightFlowMax;

    @TableField(ModelConstants.PIPE_MIN_FLOW_CONFIG_NIGHT_VALUE_MIN)
    private BigDecimal nightValueMin;

    @TableField(ModelConstants.PIPE_MIN_FLOW_CONFIG_NIGHT_VALUE_MAX)
    private BigDecimal nightValueMax;

    @TableField(ModelConstants.PIPE_MIN_FLOW_CONFIG_UNIT_PIPE_NIGHT_FLOW_MIN)
    private BigDecimal unitPipeNightFlowMin;

    @TableField(ModelConstants.PIPE_MIN_FLOW_CONFIG_UNIT_PIPE_NIGHT_FLOW_MAX)
    private BigDecimal unitPipeNightFlowMax;

    @TableField(ModelConstants.PIPE_MIN_FLOW_CONFIG_MNF_DIV_DAY_AVG_HOUR_FLOW_MIN)
    private BigDecimal mnfDivDayAvgHourFlowMin;

    @TableField(ModelConstants.PIPE_MIN_FLOW_CONFIG_MNF_DIV_DAY_AVG_HOUR_FLOW_MAX)
    private BigDecimal mnfDivDayAvgHourFlowMax;

    @TableField(ModelConstants.PIPE_MIN_FLOW_CONFIG_COLLECT_RATE)
    private Integer collectRate;

    @TableField(ModelConstants.PIPE_MIN_FLOW_CONFIG_STOCK_TYPE)
    private String stockType;

    @TableField(ModelConstants.PIPE_MIN_FLOW_CONFIG_INCR_TIME)
    private Date incrTime;

    @TableField(ModelConstants.PIPE_MIN_FLOW_CONFIG_INCR_BASE)
    private BigDecimal incrBase;

    @TableField(ModelConstants.PIPE_MIN_FLOW_CONFIG_INCR_WARN)
    private BigDecimal incrWarn;

    @TableField(ModelConstants.PIPE_MIN_FLOW_CONFIG_INCR_ERROR)
    private BigDecimal incrError;

    @TableField(ModelConstants.PIPE_MIN_FLOW_CONFIG_INCR_TYPE)
    private String incrType;

    @TableField(ModelConstants.CREATOR)
    private String creator;

    @TableField(ModelConstants.CREATE_TIME)
    private Date createTime;

    @TableField(ModelConstants.UPDATE_TIME)
    private Date updateTime;

    @TableField(ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;

}
