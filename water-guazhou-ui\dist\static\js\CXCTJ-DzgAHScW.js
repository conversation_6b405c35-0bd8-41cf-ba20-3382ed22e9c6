import{_ as C}from"./index-C9hz-UZb.js";import{_ as g}from"./InlineForm.vue_vue_type_style_index_0_lang-s-ANlzyw.js";import{d as v,r as m,c as i,j as h,o as w,ay as k,g as S,h as L,F as _,p as o,q as l,i as s,cs as T,C as D}from"./index-r0dFAfgr.js";import"./index-0NlGN6gS.js";import{u as B}from"./useDetector-BRcb7GRN.js";import{G as V}from"./statistics-CeyexT_5.js";const A={class:"card-header"},F={class:"left"},P=v({__name:"CXCTJ",setup(q){const p=m({barChartOption:null}),c=i(),y=m({defaultValue:{dateType:"quarter"},group:[{fields:[{type:"radio-button",field:"dateType",options:[{label:"季度",value:"quarter"},{label:"年",value:"year"}],onChange:()=>f()}]}]}),f=()=>{var a;const t=(a=c.value)==null?void 0:a.dataForm.dateType;V({type:t}).then(r=>{const e=r.data.data||{},n=t==="year"?e.x.map(b=>b+"月"):e.x;p.barChartOption={legend:{left:"right",top:"top",type:"scroll",textStyle:{color:h().isDark?"#fff":"#333",fontSize:12}},tooltip:{trigger:"axis"},grid:{left:10,right:30,top:50,bottom:20,containLabel:!0},xAxis:{type:"category",data:n||[],axisLabel:{show:!0,textStyle:{}},axisTick:{show:!1},splitLine:{show:!1}},yAxis:[{position:"left",type:"value",name:"m³",axisLine:{show:!1},axisLabel:{show:!0,textStyle:{}},axisTick:{show:!1},splitLine:{lineStyle:{type:"dashed"}}},{type:"value",name:"产销差（%）",axisLabel:{show:!0,textStyle:{}},axisTick:{show:!1},splitLine:{show:!1}}],series:[{name:"供水量",type:"bar",barWidth:15,data:e.supply||[]},{name:"售水量",type:"bar",barWidth:15,data:e.sale||[]},{name:"产销差",type:"line",yAxisIndex:1,data:e.nrw||[]}]}}).catch(()=>{})},x=B(),d=i(),u=i();return w(()=>{f(),x.listenToMush(u.value,()=>{var t;(t=d.value)==null||t.resize()})}),(t,a)=>{const r=g,e=k("VChart"),n=C;return S(),L(n,{title:"产销差统计"},{title:_(()=>[o("div",A,[o("div",F,[l(s(T),{icon:"material-symbols:water-drop-outline"}),a[0]||(a[0]=o("span",null,"产销差统计",-1))]),l(r,{ref_key:"refProSaleForm",ref:c,style:{width:"auto"},config:s(y)},null,8,["config"])])]),default:_(()=>[o("div",{ref_key:"refDiv",ref:u,class:"chart-box"},[l(e,{ref_key:"refChart",ref:d,option:s(p).barChartOption,theme:s(h)().isDark?"blackBackground":"whiteBackground"},null,8,["option","theme"])],512)]),_:1})}}}),N=D(P,[["__scopeId","data-v-07416518"]]);export{N as default};
