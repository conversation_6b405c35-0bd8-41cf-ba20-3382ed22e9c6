/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.model.sql;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.common.data.id.MenuPoolId;
import org.thingsboard.server.common.data.menu.MenuPool;
import org.thingsboard.server.dao.model.BaseSqlEntity;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.MENU_POOL_NAME)
public class MenuPoolEntity extends BaseSqlEntity<MenuPool> {

    @Column(name = ModelConstants.MENU_POOL_PARENT_ID)
    private String parentId;

    @Column(name = ModelConstants.MENU_POOL_DEFAULT_NAME)
    private String defaultName;

    @Column(name = ModelConstants.MENU_POOL_TYPE)
    private Integer type;

    @Column(name = ModelConstants.MENU_POOL_ORDER_NUM)
    private Integer orderNum;

    @Column(name = ModelConstants.MENU_POOL_PARAMS)
    private String params;

    @Column(name = ModelConstants.MENU_POOL_STATUS)
    private Integer status;

    @Column(name = ModelConstants.MENU_POOL_FLAG_DELETE)
    private Integer flagDelete;

    @Column(name = ModelConstants.MENU_POOL_ADDITIONAL_INFO)
    private String additionalInfo;

    @Column(name = ModelConstants.MENU_CUSTOMER_ICON)
    private String icon;

    public MenuPoolEntity() {
        super();
    }

    public MenuPoolEntity(MenuPool menuPool) {
        if (menuPool.getId() != null) {
            this.setId(menuPool.getId().getId());
        }
        if (menuPool.getParentId() != null) {
            this.parentId = toString(menuPool.getParentId().getId());
        }
        this.defaultName = menuPool.getDefaultName();
        this.type = menuPool.getType();
        this.orderNum = menuPool.getOrderNum();
        this.params = menuPool.getParams();
        this.status = menuPool.getStatus();
        this.flagDelete = menuPool.getFlagDelete();
        this.additionalInfo = menuPool.getAdditionalInfo();
        this.icon = menuPool.getIcon();
    }


    @Override
    public MenuPool toData() {
        MenuPool menuPool = new MenuPool(new MenuPoolId(getId()));
        menuPool.setParentId(new MenuPoolId(toUUID(parentId)));
        menuPool.setDefaultName(defaultName);
        menuPool.setType(type);
        menuPool.setStatus(status);
        menuPool.setFlagDelete(flagDelete);
        menuPool.setAdditionalInfo(additionalInfo);
        menuPool.setOrderNum(orderNum);
        menuPool.setIcon(icon);
        return menuPool;
    }


}
