package org.thingsboard.server.controller.base;

import java.util.List;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.thingsboard.server.dao.base.IBaseTemplateFileService;
import org.thingsboard.server.dao.model.sql.base.BaseTemplateFile;
import org.thingsboard.server.dao.util.imodel.query.base.BaseTemplateFilePageRequest;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;
import org.thingsboard.server.service.aspect.annotation.MonitorPerformance;

/**
 * 平台管理-模型文件Controller
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
@Api(tags = "平台管理-模型文件")
@RestController
@RequestMapping("api/base/template/file")
public class BaseTemplateFileController extends BaseController {

    @Autowired
    private IBaseTemplateFileService baseTemplateFileService;

    /**
     * 查询平台管理-模型文件列表
     */
    @MonitorPerformance(description = "平台管理-查询模板文件列表")
    @ApiOperation(value = "查询模板文件列表")
    @GetMapping("/list")
    public IstarResponse list(BaseTemplateFilePageRequest baseTemplateFile) {
        return IstarResponse.ok(baseTemplateFileService.selectBaseTemplateFileList(baseTemplateFile));
    }

//    /**
//     * 导出平台管理-模型文件列表
//     */
//    @PreAuthorize("@ss.hasPermi('system:file:export')")
//    @Log(title = "平台管理-模型文件", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(HttpServletResponse response, BaseTemplateFile baseTemplateFile) {
//        List<BaseTemplateFile> list = baseTemplateFileService.selectBaseTemplateFileList(baseTemplateFile);
//        ExcelUtil<BaseTemplateFile> util = new ExcelUtil<BaseTemplateFile>(BaseTemplateFile.class);
//        util.exportExcel(response, list, "平台管理-模型文件数据");
//    }

    /**
     * 获取平台管理-模型文件详细信息
     */
    @MonitorPerformance(description = "平台管理-查询模板文件详情")
    @ApiOperation(value = "查询模板文件详情")
    @GetMapping(value = "/getDetail")
    public IstarResponse getInfo(String id) {
        return IstarResponse.ok(baseTemplateFileService.selectBaseTemplateFileById(id));
    }

    /**
     * 新增平台管理-模型文件
     */
    @MonitorPerformance(description = "平台管理-新增模板文件")
    @ApiOperation(value = "新增模板文件")
    @PostMapping("/add")
    public IstarResponse add(@RequestBody BaseTemplateFile baseTemplateFile) {
        return IstarResponse.ok(baseTemplateFileService.insertBaseTemplateFile(baseTemplateFile));
    }

    /**
     * 修改平台管理-模型文件
     */
    @MonitorPerformance(description = "平台管理-修改模板文件")
    @ApiOperation(value = "修改模板文件")
    @PostMapping("/edit")
    public IstarResponse edit(@RequestBody BaseTemplateFile baseTemplateFile) {
        return IstarResponse.ok(baseTemplateFileService.updateBaseTemplateFile(baseTemplateFile));
    }

    /**
     * 删除平台管理-模型文件
     */
    @MonitorPerformance(description = "平台管理-删除模板文件")
    @ApiOperation(value = "删除模板文件")
    @DeleteMapping("/deleteIds")
    public IstarResponse remove(@RequestBody List<String> ids) {
        return IstarResponse.ok(baseTemplateFileService.deleteBaseTemplateFileByIds(ids));
    }
}
