package org.thingsboard.server.dao.smartPipe;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.request.PartitionMountRequest;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.PartitionLossPoint;
import org.thingsboard.server.dao.sql.smartPipe.PartitionLossPointMapper;

import java.util.Date;
import java.util.List;

/**
 *
 */
@Service
public class PartitionLossPointServiceImpl implements PartitionLossPointService {

    @Autowired
    private PartitionLossPointMapper partitionLossPointMapper;

    @Override
    public PartitionLossPoint save(PartitionLossPoint partitionLossPoint) {
        if (StringUtils.isBlank(partitionLossPoint.getId())) {
            partitionLossPoint.setCreateTime(new Date());
            partitionLossPointMapper.insert(partitionLossPoint);
        } else {
            partitionLossPointMapper.updateById(partitionLossPoint);
        }
        return partitionLossPoint;
    }


    @Override
    public PageData<PartitionLossPoint> getList(PartitionMountRequest request) {
        IPage<PartitionLossPoint> page = new Page<>(request.getPage(), request.getSize());
        IPage<PartitionLossPoint> result = partitionLossPointMapper.getList(page, request);
        result.getRecords().stream().forEach(a -> {
            try {
                a.setStatusName(DataConstants.PARTITION_STATUS.getByValue(a.getStatus()).getName());
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
        return new PageData<>(result.getTotal(), result.getRecords());
    }

    @Override
    public void delete(List<String> ids) {
        partitionLossPointMapper.deleteBatchIds(ids);
    }

    @Override
    public List<JSONObject> getLossPointReport(String name, String tenantId) {
        List<JSONObject> lossPointReport = partitionLossPointMapper.getLossPointReport(name, tenantId);
        lossPointReport.stream().forEach(a -> {
            try {
                a.put("statusName", DataConstants.PARTITION_STATUS.getByValue(a.getString("status")).getName());
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
        return lossPointReport;
    }
}
