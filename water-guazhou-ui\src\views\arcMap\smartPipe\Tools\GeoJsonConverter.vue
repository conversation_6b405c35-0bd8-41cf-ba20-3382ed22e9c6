<template>
  <ArcLayout
    ref="refArcLayout"
    :panel-default-visible="true"
    :panel-max-min="true"
    :hide-panel-close="true"
    :panel-dragable="false"
    :panel-default-maxmin="'max'"
    :panel-title="'Geojson几何转换 —— 经纬度坐标转换成xy坐标'"
  >
    <CoordinateConverter ref="refConverter"></CoordinateConverter>
    <template #detail-default>
      <el-upload
        ref="refUpload"
        action=""
        :limit="1"
        :on-exceed="handleExceed"
        :auto-upload="false"
        :before-upload="handleBeforeUpload"
        :on-change="handleChange"
      >
        <template #trigger>
          <el-button
            :size="'small'"
            type="primary"
          >
            上传geojson文件
          </el-button>
        </template>
      </el-upload>
      <pre
        style="height: 200px;"
        class="code code-bg code-font"
      >
        <code>{{ inputG??'上传文件预览' }}</code>
      </pre>

      <el-button
        :loading="loading"
        :type="'primary'"
        style="margin: 10px 0"
        :size="'small'"
        @click="convert"
      >
        转换
      </el-button>
      <el-button
        :loading="loading"
        :type="'success'"
        :size="'small'"
        @click="copy"
      >
        复制结果
      </el-button>
      <pre
        ref="refPre"
        class="code code-bg code-font"
      >
        <code>{{ outputG }}</code>
      </pre>
    </template>
  </ArcLayout>
</template>
<script lang="ts" setup>
import { useClipboard } from '@vueuse/core'
import { UploadProps, UploadInstance, UploadRawFile } from 'element-plus'
import { SLMessage } from '@/utils/Message'
import CoordinateConverter from './CoordinateConverter.vue'

const refConverter = ref<InstanceType<typeof CoordinateConverter>>()
const refArcLayout = ref<IArcLayoutIns>()
const inputG = ref()
const outputG = ref()
const loading = ref<boolean>(false)
const convert = () => {
  if (!refConverter.value) return
  if (!inputG.value) {
    SLMessage.warning('请先填入geojson数据')
    return
  }
  try {
    const geojson = JSON.parse(inputG.value)
    const features = geojson?.features
    features?.map(feature => {
      if (!feature || !refConverter.value) return
      feature.properties?.center && (feature.properties.center = refConverter.value.convert(feature.properties.center))
      feature.properties?.centroid && (feature.properties.centroid = refConverter.value.convert(feature.properties.centroid))
      feature.geometry?.coordinates && (feature.geometry.coordinates = convertGeometryCoords(feature.geometry))
      feature.bbox && (feature.bbox = [...refConverter.value.convert([feature.bbox[0], feature.bbox[1]]), ...refConverter.value.convert([feature.bbox[0], feature.bbox[1]])])
    })
    outputG.value = geojson
  } catch (error) {
    SLMessage.error('转换失败')
  }
}
const convertGeometryCoords = geometry => {
  if (geometry.type === 'MultiPolygon') {
    return geometry.coordinates.map(polygonCoods => {
      return polygonCoods.map(pathCoords => {
        return pathCoords.map(pointCoords => refConverter.value?.convert(pointCoords))
      })
    })
  } if (geometry.type === 'LineString') {
    return geometry.coordinates.map(point => {
      return refConverter.value?.convert(point)
    })
  } if (geometry.type === 'Point') {
    return refConverter.value?.convert(geometry.coordinates)
  }
}
const refPre = ref<HTMLPreElement>()
const clipboard = useClipboard()
const copy = () => {
  refPre.value
    && clipboard
      .copy(refPre.value.innerText)
      .then(() => {
        SLMessage.success('复制成功')
      })
      .catch(e => {
        console.log(e)
        SLMessage.error('复制失败')
      })
}
const refUpload = ref<UploadInstance>()
const handleExceed: UploadProps['onExceed'] = files => {
  if (!refUpload.value) return
  refUpload.value.clearFiles()
  const file = files[0] as UploadRawFile
  refUpload.value.handleStart(file)
}
const handleBeforeUpload = file => {
  const reader = new FileReader()
  reader.readAsText(file, 'UTF-8')
  reader.onload = e => {
    const result: any = e.target?.result
    inputG.value = JSON.stringify(JSON.parse(result))
  }
  return false
}
const handleChange = (uploadFile, uploadFiles) => {
  console.log(uploadFile, uploadFiles)
  refUpload.value?.submit()
}
onMounted(() => {
  refArcLayout.value?.refPanel?.toggleMaxMin('max')
})
</script>
<style lang="scss" scoped>
.code {
  padding: 20px;
  margin: 0;
  height: 300px;
  overflow: auto;
}
.code-bg {
  background-color: var(--el-bg-color);
}
.code-font {
  font-size: 12px;
}
</style>
