<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartProduction.circuit.CircuitTemplateMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->id,
                           type,
                           name,
                           settings,
                           remark,
                           creator,
                           create_time,
                           tenant_id<!--@sql from sp_circuit_template -->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.smartProduction.circuit.CircuitTemplate">
        <result column="id" property="id"/>
        <result column="type" property="type"/>
        <result column="name" property="name"/>
        <result column="settings" property="settings"/>
        <result column="remark" property="remark"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sp_circuit_template
        <where>
            <if test="type != null and type != ''">
                and type = #{type}
            </if>
            <if test="name != null and name != ''">
                and name like '%' || #{name} || '%'
            </if>
            <if test="fromTime != null">
                and create_time >= #{fromTime}
            </if>
            <if test="creator != null and creator != ''">
                and creator = #{creator}
            </if>
            <if test="keyword != null and keyword != ''">
                and remark like '%' || #{keyword} || '%'
            </if>
            <if test="toTime != null">
                and create_time &lt;= #{toTime}
            </if>
            and tenant_id = #{tenantId}
        </where>
        order by create_time desc
    </select>

    <update id="update">
        update sp_circuit_template
        <set>
            <if test="type != null">
                type = #{type},
            </if>
            <if test="name != null">
                name = #{name},
            </if>
            <if test="settings != null">
                settings = #{settings},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
        </set>
        where id = #{id}
    </update>

    <select id="getSettings" resultType="java.lang.String">
        select settings
        from sp_circuit_template
        where id = #{templateId}
    </select>

    <select id="getNameById" resultType="java.lang.String">
        select name
        from sp_circuit_template
        where id = #{id}
    </select>
</mapper>