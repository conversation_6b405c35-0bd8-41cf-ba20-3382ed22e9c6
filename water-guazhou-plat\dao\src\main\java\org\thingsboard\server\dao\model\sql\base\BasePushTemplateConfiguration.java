package org.thingsboard.server.dao.model.sql.base;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 平台管理-推送模板配置对象 base_push_template_configuration
 *
 * <AUTHOR>
 * @date 2025-06-18
 */
@ApiModel(value = "推送模板配置", description = "推送模板配置实体类")
@Data
public class BasePushTemplateConfiguration {

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 模板名称
     */
    @ApiModelProperty(value = "模板名称")
    private String name;

    /**
     * 模板类型
     */
    @ApiModelProperty(value = "模板类型")
    private String type;

    /**
     * 消息标题
     */
    @ApiModelProperty(value = "消息标题")
    private String title;

    /**
     * 消息内容
     */
    @ApiModelProperty(value = "消息内容")
    private String content;

    /**
     * 状态（0-禁用，1-启用）
     */
    @ApiModelProperty(value = "状态（0-禁用，1-启用）")
    private String status;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createTime;

}
