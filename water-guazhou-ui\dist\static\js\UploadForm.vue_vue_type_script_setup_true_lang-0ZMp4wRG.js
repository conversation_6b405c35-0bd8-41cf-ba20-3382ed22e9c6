import{_ as F}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{P as k}from"./engineeringDocuments-DYprVB7x.js";import{d as w,c as x,r as u,ad as T,b as m,S as j,g as M,h as P,i as C}from"./index-r0dFAfgr.js";import{E as B}from"./config-B_00vVdd.js";const O=w({__name:"UploadForm",props:{objectid:{},layerid:{},layername:{},geo:{}},emits:["uploaded"],setup(y,{expose:v,emit:b}){const _=b,o=y,c=x(),t=u({image:[],video:[],audio:[],file:[]}),f=T("pipeMediasRef",null),h=(e,...i)=>f&&f.value&&typeof f.value[e]=="function"?f.value[e](...i):(console.warn(`PipeMedias组件的${e}方法不可用`),null),g=u({title:"图档上传",dialogWidth:500,group:[{fields:[{type:"image",label:"图片",field:"image",accept:".png,.jpg",handleSuccess:e=>p("image",e)},{type:"file",label:"录音",field:"audio",accept:".avi,.wav,.weba,.webm,.oga,.ogx,.opus,mpeg,.mp4,.mp3,.aac,.3gp,.3gp2",handleSuccess:e=>p("audio",e)},{type:"file",label:"视频",field:"video",accept:".avi,.webm,.oga,.ogx,.opus,.mpeg,.mp4",handleSuccess:e=>p("video",e)},{type:"file",label:"文档",field:"file",handleSuccess:e=>p("file",e)},{type:"textarea",label:"备注",field:"remark"}]}],labelPosition:"right",labelWidth:"100px",defaultValue:{},submit:e=>{var i;if(!o.layername){m.error("上传失败：缺少图层信息");return}(i=c.value)==null||i.closeDialog(),j("确定上传?","提示").then(async()=>{var l;try{const n={layerid:o.layerid,layername:o.layername},r=[...d("image",n,e),...d("video",n,e),...d("audio",n,e),...d("file",n,e)];debugger;const s=await k(r);if(s.data.code===200){m.success("上传成功"),(l=c.value)==null||l.closeDialog();const S={image:r.filter(a=>a.fileType==="image").map(a=>({name:a.name,file:a.file})),video:r.filter(a=>a.fileType==="video").map(a=>({name:a.name,file:a.file})),audio:r.filter(a=>a.fileType==="audio").map(a=>({name:a.name,file:a.file})),file:r.filter(a=>a.fileType==="file").map(a=>({name:a.name,file:a.file}))};h("addMediaFiles",S),_("uploaded")}else m.error("上传失败"),console.log(s.data.message)}catch(n){m.error("上传失败"),console.log(n)}}).catch(()=>{})}}),d=(e,i,l)=>{var r;const n=i.layername||"unknown";return((r=t[e])==null?void 0:r.map(s=>({name:s.name,fileType:e,deviceType:n,deviceCode:o.objectid,remark:l.remark,geo:JSON.stringify(o.geo),file:s.url,status:B.正常})))||[]},D=()=>{var e;g.title=`图档上传(${o.objectid||""})`,t.image=[],t.video=[],t.audio=[],t.file=[],(e=c.value)==null||e.openDialog()},p=(e,i)=>{console.log(e,i),o.geo&&(i=i.map(l=>({...l,geoInfo:o.geo}))),t[e]=i||[]};return v({openDialog:D}),(e,i)=>{const l=F;return M(),P(l,{ref_key:"refDialog",ref:c,config:C(g)},null,8,["config"])}}});export{O as _};
