package org.thingsboard.server.dao.groundwater;

import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.groundwater.GroundwaterLevel;
import org.thingsboard.server.dao.model.sql.groundwater.GroundwaterRecharge;
import org.thingsboard.server.dao.model.request.GroundwaterLevelRequest;
import org.thingsboard.server.dao.model.request.GroundwaterRechargeRequest;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 地下水涵养水位服务接口
 */
public interface GroundwaterService {

    /**
     * 保存地下水水位数据
     * @param entity 水位数据实体
     * @return 保存后的实体
     */
    GroundwaterLevel saveWaterLevel(GroundwaterLevel entity);

    /**
     * 分页查询地下水水位数据
     * @param request 查询参数
     * @param tenantId 租户ID
     * @return 分页数据
     */
    PageData<GroundwaterLevel> findWaterLevelList(GroundwaterLevelRequest request, TenantId tenantId);

    /**
     * 分页查询地下水水位数据（使用Map参数）
     * @param params 查询参数
     * @return 分页数据
     */
    PageData<GroundwaterLevel> findWaterLevelList(Map<String, Object> params);

    /**
     * 删除地下水水位数据
     * @param ids ID列表
     */
    void removeWaterLevel(List<String> ids);

    /**
     * 根据ID获取地下水水位详情
     * @param id 水位ID
     * @return 水位详情
     */
    GroundwaterLevel getWaterLevelById(String id);

    /**
     * 更新地下水水位数据
     * @param entity 水位数据实体
     * @return 更新后的实体
     */
    GroundwaterLevel updateWaterLevel(GroundwaterLevel entity);

    /**
     * 根据时间段获取地下水水位变化
     * @param stationId 站点ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 水位变化数据
     */
    List<GroundwaterLevel> getWaterLevelChangeData(String stationId, Date startTime, Date endTime);

    /**
     * 保存地下水涵养分析结果
     * @param entity 涵养分析结果实体
     * @return 保存后的实体
     */
    GroundwaterRecharge saveRechargeAnalysis(GroundwaterRecharge entity);

    /**
     * 分页查询地下水涵养分析结果
     * @param request 查询参数
     * @param tenantId 租户ID
     * @return 分页数据
     */
    PageData<GroundwaterRecharge> findRechargeList(GroundwaterRechargeRequest request, String tenantId);

    /**
     * 删除地下水涵养分析结果
     * @param ids ID列表
     */
    void removeRechargeAnalysis(List<String> ids);

    /**
     * 分析地下水涵养水位并给出建议
     * @param areaId 区域ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 涵养水位分析结果
     */
    GroundwaterRecharge analyzeGroundwaterRecharge(String areaId, Date startTime, Date endTime);

    /**
     * 获取地下水水位统计数据
     * @return 统计数据
     */
    Map<String, Object> getWaterLevelStatistics();
}