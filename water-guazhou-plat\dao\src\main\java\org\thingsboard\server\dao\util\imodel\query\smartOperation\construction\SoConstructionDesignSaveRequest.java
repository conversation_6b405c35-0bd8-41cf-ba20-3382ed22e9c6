package org.thingsboard.server.dao.util.imodel.query.smartOperation.construction;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionDesign;
import org.thingsboard.server.dao.util.imodel.query.IStarHttpRequest;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

import java.math.BigDecimal;

import static org.thingsboard.server.dao.model.sql.smartOperation.SoGeneralTaskStatus.PROCESSING;

@Getter
@Setter
public class SoConstructionDesignSaveRequest extends SaveRequest<SoConstructionDesign> {
    // 所属项目编号
    @NotNullOrEmpty
    private String constructionCode;

    // 设计分类
    private String type;

    // 设计费用，万元
    private BigDecimal cost;

    // 设计管长信息JSON
    private String pipLengthDesign;

    // 备注
    private String remark;

    // 附件信息
    private String attachments;

    @Override
    public String valid(IStarHttpRequest request) {
        if (BigDecimal.ZERO.compareTo(cost) > 0) {
            return "费用不能小于0";
        }
        return super.valid(request);
    }

    @Override
    protected SoConstructionDesign build() {
        SoConstructionDesign entity = new SoConstructionDesign();
        entity.setConstructionCode(constructionCode);
        entity.setCode("D" + constructionCode.substring(1));
        entity.setStatus(PROCESSING);
        entity.setTenantId(tenantId());
        entity.setCreator(currentUserUUID());
        entity.setCreateTime(createTime());
        commonSet(entity);
        return entity;
    }

    @Override
    protected SoConstructionDesign update(String id) {
        SoConstructionDesign entity = new SoConstructionDesign();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(SoConstructionDesign entity) {
        entity.setType(type);
        entity.setCost(cost);
        entity.setPipLengthDesign(pipLengthDesign);
        entity.setRemark(remark);
        entity.setAttachments(attachments);
        entity.setUpdateUser(currentUserUUID());
        entity.setUpdateTime(createTime());
    }
}