package org.thingsboard.server.dao.assay;

import org.thingsboard.server.common.data.User;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.request.AssayItemListRequest;
import org.thingsboard.server.dao.model.sql.assay.AssayItem;

import java.util.List;

public interface AssayItemService {
    /**
     * 分页查询化验项内容列表
     *
     * @param request  请求参数
     * @param tenantId 租户ID
     * @return 数据
     */
    PageData<AssayItem> findList(AssayItemListRequest request, TenantId tenantId);

    void save(AssayItem entity, User currentUser);

    void remove(List<String> ids);
}
