package org.thingsboard.server.controller.maintenance;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.User;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.sql.MaintenancePlanEntity;
import org.thingsboard.server.dao.repair.MaintenancePlanService;

import java.util.List;

@RestController
@RequestMapping("api/maintenance/plan")
public class MaintenancePlanController extends BaseController {

    @Autowired
    private MaintenancePlanService maintenancePlanService;

    @GetMapping("detail/{id}")
    public MaintenancePlanEntity get(@PathVariable String id) throws ThingsboardException {
        User currentUser = getCurrentUser();
        return maintenancePlanService.detail(id, currentUser);
    }

    @GetMapping("list")
    public PageData<MaintenancePlanEntity> findList(@RequestParam int page, @RequestParam int size,
                                               @RequestParam(required = false, defaultValue = "") String name) throws ThingsboardException {
        User currentUser = getCurrentUser();
        return maintenancePlanService.findList(page, size, name, currentUser);
    }

    @PostMapping("save")
    public MaintenancePlanEntity save(@RequestBody MaintenancePlanEntity entity) throws ThingsboardException {
        User currentUser = getCurrentUser();
        return maintenancePlanService.savePlan(entity, currentUser);
    }

    @DeleteMapping
    public void delete(@RequestBody List<String> ids) {
        maintenancePlanService.remove(ids);
    }

    @GetMapping("changeStatus/{id}")
    public void changeStatus(@PathVariable String id) {
        maintenancePlanService.changeStatus(id);
    }

}
