package org.thingsboard.server.dao.dataSource;

import org.thingsboard.server.common.data.dataSource.DataSourceType;
import org.thingsboard.server.dao.model.sql.DataSourceEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/2/26 15:03
 */
public interface DataSourceDao {

    /**
     * 根据项目ID和数据源类别查询
     *
     * @param projectId 项目ID
     * @param type      数据源类别
     * @return List<DataSourceEntity>
     */
    List<DataSourceEntity> findByProjectIdAndType(String projectId, DataSourceType type);

    /**
     * 保存数据源
     *
     * @param dataSourceEntity 数据源实体
     * @return DataSourceEntity
     */
    DataSourceEntity save(DataSourceEntity dataSourceEntity);

    /**
     * 删除数据源
     *
     * @param dataSourceId 数据源ID
     * @return boolean
     */
    boolean deleteDataSource(String dataSourceId);

    /**
     * 根据ID获取数据源实体
     *
     * @param dataSourceId 数据源ID
     * @return DataSourceEntity
     */
    DataSourceEntity findById(String dataSourceId);

    List<DataSourceEntity> findAllByOriginatorIdAndType(String originatorId, DataSourceType type);

    List<DataSourceEntity> findByTypeAndTenantId(String type, String tenantId);

    List<DataSourceEntity> findByOriginatorId(String originatorId);

    int countByOriginatorIdAndName(String originatorId, String name);

    List<DataSourceEntity> findAllByOriginatorIdInAndProperty(List<String> deviceIdList, String type, String property);

    List<DataSourceEntity> findByIdIn(List<String> dataSourceIds);
}
