/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.service.lotCard;

import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.EntityType;
import org.thingsboard.server.common.data.lot.LotCard;

public class LotCardUtil {

//    public static Object getCarIdMsg(LotCard lotCard) {
//        switch (lotCard.getType()) {
//            //腾讯物联网卡
//            case TENCENT:
//        }
//    }
//
//
//    private void getTencentLotCardMsg() {
//        DataConstants.OPERATING_TYPE_DEVICE_DATA
//    }
}
