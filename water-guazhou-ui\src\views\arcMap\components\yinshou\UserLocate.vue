<template>
  <Search
    ref="refSearch"
    style="padding: 0"
    :config="SearchConfig"
  ></Search>
  <div class="table-box">
    <FormTable :config="TableConfig">
    </FormTable>
  </div>
</template>
<script lang="ts" setup>
import { ISearchIns } from '@/components/type'
import { getUserInfoByYhbh } from '@/api/mapservice/gisUser'

defineProps<{
  view?: __esri.MapView
}>()
const refSearch = ref<ISearchIns>()
const TableConfig = reactive<ITable>({
  dataList: [],
  columns: [
    { label: '用户编号', prop: 'count' },
    { label: '用户姓名', prop: 'fee' },
    { label: '用户地址', prop: 'time' }
  ],
  pagination: {
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page
      TableConfig.pagination.limit = size
      refreshData()
    },
    layout: 'total,sizes, jumper'
  }
})

const SearchConfig = reactive<ISearch>({
  filters: [
    { type: 'input', label: '用户编号', field: 'yhbh', labelWidth: 70 },
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          type: 'default',
          loading: () => TableConfig.loading === true,
          click: () => refreshData()
        },
        {
          perm: true,
          type: 'danger',
          text: '导出',
          disabled: () => TableConfig.loading === true,
          click: () => {
            //
          }
        }
      ]
    }
  ]
})
const refreshData = async () => {
  TableConfig.loading = true
  try {
    const { yhbh } = refSearch.value?.queryParams || {}
    const res = await getUserInfoByYhbh(yhbh)
    TableConfig.dataList = res.data
  } catch (error) {
    console.dir(error)
  }
  TableConfig.loading = false
}
</script>
<style lang="scss" scoped>
.table-box {
  height: calc(100% - 50px);
}
</style>
