import{d as V,cN as z,c as k,r as B,o as I,ay as N,g as p,n as h,bo as O,i as v,dy as S,p as u,q as w,br as A,C as E}from"./index-r0dFAfgr.js";import{h as R,b as i}from"./chart-wy3NEK2T.js";import{e as T}from"./onemap-CEunQziB.js";import{p as _}from"./padStart-BKfyZZDO.js";const W={class:"one-map-detail"},X={class:"left"},q={class:"right"},G={style:{width:"100%",height:"100%"}},M=V({__name:"WaterVolumeDetail",emits:["refresh","mounted"],setup(U,{expose:x,emit:b}){const d=b,{proxy:y}=z(),g=R(),r=k(),o=B({lineChartOption1:i(Array.from({length:24}).map((t,a)=>_(a.toString(),2,"0")),[],{}),lineChartOption2:i(Array.from({length:30}).map((t,a)=>_(a.toString(),2,"0")),[],{}),stationRealTimeData:[],detailLoading:!1});x({refreshDetail:async t=>{d("refresh",{title:t.name}),o.detailLoading=!0,o.curRow=t,Array.from({length:2}).map((a,n)=>{var s;(s=y.$refs["refChart"+n])==null||s.resize()}),T({stationId:t.stationId}).then(a=>{var l,f,m,c;const n=((l=a.data.data.todayData)==null?void 0:l.map(e=>e.value))||[],s=((f=a.data.data.todayData)==null?void 0:f.map(e=>e.ts))||[];o.lineChartOption1=i(s,n,{unit:"m³",name:"日供水量",color1:"#ff0000"});const D=((m=a.data.data.monthData)==null?void 0:m.map(e=>e.value))||[],L=((c=a.data.data.monthData)==null?void 0:c.map(e=>e.ts))||[];o.lineChartOption2=i(D,L,{unit:"m³",name:"月供水量",color1:"#ff0000"})}).finally(()=>{o.detailLoading=!1})}}),I(()=>{d("mounted"),setTimeout(()=>{var t;window.addEventListener("resize",C),(t=r.value)==null||t.resize()},3e3)});function C(){var t;(t=r.value)==null||t.resize()}return(t,a)=>{const n=N("VChart"),s=A;return p(),h("div",W,[O((p(),h("div",X,a[0]||(a[0]=[S('<div class="flex" data-v-497bf8e5><span data-v-497bf8e5>区域总水量<text style="color:#42a0ff;" data-v-497bf8e5>0</text>m³</span><span data-v-497bf8e5>区域售水量<text style="color:#63c63a;" data-v-497bf8e5>0</text>m³/h</span></div><div class="flex" data-v-497bf8e5><span data-v-497bf8e5>夜间最小流量<text data-v-497bf8e5>0</text>m³/h</span><span data-v-497bf8e5>挂接用户<text data-v-497bf8e5>0</text>户</span><span data-v-497bf8e5>大用户数<text data-v-497bf8e5>0</text>户</span><span data-v-497bf8e5>大用户水量占比数<text data-v-497bf8e5>0</text>%</span></div>',2)]))),[[s,v(o).detailLoading]]),u("div",q,[u("div",G,[w(n,{ref_key:"refChart2",ref:r,autoresize:"",theme:"dark",option:v(g)},null,8,["option"])])])])}}}),K=E(M,[["__scopeId","data-v-497bf8e5"]]);export{K as default};
