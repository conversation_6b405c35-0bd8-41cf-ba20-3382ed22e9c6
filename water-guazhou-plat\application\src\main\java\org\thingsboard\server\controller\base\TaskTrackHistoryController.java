package org.thingsboard.server.controller.base;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.dao.model.sql.TaskTrackHistoryEntity;
import org.thingsboard.server.dao.orderWork.TaskTrackHistoryService;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("api/taskTrackHistory")
public class TaskTrackHistoryController extends BaseController {

    @Autowired
    private TaskTrackHistoryService taskTrackHistoryService;

    @PostMapping("saveLocation")
    public TaskTrackHistoryEntity saveLocation(@RequestBody TaskTrackHistoryEntity entity) throws ThingsboardException {
        log.info("data = {}", JSON.toJSONString(entity));
        if (StringUtils.isBlank(entity.getUserId())) {
            entity.setUserId(UUIDConverter.fromTimeUUID(getCurrentUser().getUuidId()));
        }
        return taskTrackHistoryService.save(entity);
    }

    @GetMapping("getTrack")
    public Object task(@RequestParam String contentId) throws JSONException {
        List<TaskTrackHistoryEntity> taskTrackHistoryList = taskTrackHistoryService.findByContentId(contentId);

        // 按用户分离数据
        Map<String, List<TaskTrackHistoryEntity>> map = new HashMap<>();
        for (TaskTrackHistoryEntity entity : taskTrackHistoryList) {
            String userId = entity.getUserId();
            List<TaskTrackHistoryEntity> list = new ArrayList<>();
            if (map.containsKey(userId)) {
                list = map.get(userId);
            }

            list.add(entity);
            map.put(userId, list);
        }

        List<JSONObject> resultList = new ArrayList<>();
        for (Map.Entry<String, List<TaskTrackHistoryEntity>> entry : map.entrySet()) {
            String key = entry.getKey();
            List<TaskTrackHistoryEntity> value = entry.getValue();
//            List<String> trackList = value.stream().map(TaskTrackHistoryEntity::getLocation).collect(Collectors.toList());

            JSONObject obj = new JSONObject();
            obj.put("userId", key);
            obj.put("track", value);

            resultList.add(obj);
        }

        return resultList;
    }
}
