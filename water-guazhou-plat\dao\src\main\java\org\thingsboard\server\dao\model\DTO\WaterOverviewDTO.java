package org.thingsboard.server.dao.model.DTO;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 智慧管网-指标总览供水量
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-12-19
 */
@Data
public class WaterOverviewDTO {

    private BigDecimal nowFlow = BigDecimal.ZERO;

    private BigDecimal nowSupply = BigDecimal.ZERO;

    private BigDecimal quarterSupply = BigDecimal.ZERO;

    private BigDecimal quarterSale = BigDecimal.ZERO;

    private String quarterNRW = "0%";

    private BigDecimal thisYearSupply = BigDecimal.ZERO;

    private BigDecimal thisYearSale = BigDecimal.ZERO;

    private String thisYearNRW = "0%";

    private BigDecimal lastMonthSupply = BigDecimal.ZERO;

    private BigDecimal lastMonthSale = BigDecimal.ZERO;

    private String lastMonthNRW = "0%";

    private BigDecimal lastQuarterSupply = BigDecimal.ZERO;

    private BigDecimal lastQuarterSale = BigDecimal.ZERO;

    private String lastQuarterNRW = "0%";

    private BigDecimal lastYearSupply = BigDecimal.ZERO;

    private BigDecimal lastYearSale = BigDecimal.ZERO;

    private String lastYearNRW = "0%";

}
