<template>
  <div class="pop-image">
    <el-tabs
      :tab-position="'right'"
      @tab-change="handleTabChange"
    >
      <!-- <el-tab-pane label="组态">
        <div class="content scada overlay">
          <iframe
            ref="refIframe"
            frameborder="0"
            scrolling="auto"
            :src="scadaUrl"
            width="100%"
            height="100%"
            allowfullscreen="true"
          ></iframe>
        </div>
      </el-tab-pane> -->
      <el-tab-pane label="信息">
        <div class="content attrs overlay-y">
          <AttrTable
            v-if="config.info.type === 'custom-attrs'"
            :columns="config.info.columns"
            :attributes="config.info.attributes"
            :data="config.info.attrData"
            :rows="config.info.rows"
          ></AttrTable>
          <AttrTable
            v-else-if="config.info.type === 'attrs'"
            :attributes="state.attributes"
          ></AttrTable>
          <el-image
            v-else
            :preview-teleported="true"
            :close-on-press-escape="true"
            :hide-on-click-modal="true"
            style="width: 100%; height: 100%"
            :src="images[0]"
            :preview-src-list="images"
            fit="cover"
          />
        </div>
      </el-tab-pane>
      <el-tab-pane
        label="报警"
        :lazy="true"
      >
        <div class="content alarms overlay-y">
          <FormTable :config="TableConfig"></FormTable>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script lang="ts" setup>
import {
  GetStationAlarmList,
  GetStationRealTimeDetail
} from '@/api/shuiwureports/zhandian'
import { formatDate } from '@/utils/DateFormatter'

const props = defineProps<{
  visible: boolean
  config: {
    info: {
      type?: 'image' | 'attrs' | 'custom-attrs'
      imageUrl?: string
      stationId: string
      columns?: IAttrTableRow[][]
      attributes?: { label: string; value: any }[]
      attrData?: any
      rows?: IAttrTableRow[][]
      scadaUrl?: string
    }
    warnings: {}
  }
}>()
const state = reactive<{
  attributes: { label: string; value: string; data?: any }[]
}>({
  attributes: []
})

const images = computed(() => {
  return props.config.info?.imageUrl?.split(',') || []
})
const TableConfig = reactive<ITable>({
  maxHeight: 300,
  dataList: [],
  columns: [
    {
      label: '报警时间',
      prop: 'startTs',
      formatter: row => formatDate(row.startTs)
    },
    {
      label: '状态',
      prop: 'type'
    }
  ],
  pagination: {
    hide: true
  }
})
const handleTabChange = async (index: any) => {
  try {
    if (index === '1') {
      await getAlarmList()
    } else if (index === '0') {
      if (props.config.info.type === 'attrs') {
        await getAttrList()
      }
    }
  } catch (error) {
    console.log(error)
  }
}
const getAlarmList = async () => {
  try {
    const res = await GetStationAlarmList(props.config.info.stationId)
    TableConfig.dataList = res.data || []
  } catch (error) {
    console.log(error)
  }
}
const getAttrList = async () => {
  const real = await GetStationRealTimeDetail(props.config.info.stationId)
  state.attributes = real.data?.map(item => {
    return {
      label: item.propertyName,
      value: (item.value || '--') + ' ' + (item.unit || ''),
      data: item
    }
  })
}
watch(
  () => props.visible,
  () => {
    handleTabChange('0')
  }
)
onMounted(() => {
  handleTabChange('0')
})
</script>
<style lang="scss" scoped>
.ppop-image{
  width: 350px;
  height: 300px;
}
.content {
  width: 290px;
  max-height: 290px;
  padding-right: 8px;
  // &.scada {
  //   width: 1000px;
  //   height: 675px;
  // }
  // &.attrs,
  // &.alarms {
  //   max-height: 320px;
  //   width: 280px;
  // }
}
.el-tabs--right{
  // min-width: 400px;
}
</style>
