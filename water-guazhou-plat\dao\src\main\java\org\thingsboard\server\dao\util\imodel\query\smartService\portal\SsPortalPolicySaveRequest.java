package org.thingsboard.server.dao.util.imodel.query.smartService.portal;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartService.portal.SsPortalPolicy;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

@Getter
@Setter
public class SsPortalPolicySaveRequest extends SaveRequest<SsPortalPolicy> {
    // 名称
    @NotNullOrEmpty
    private String name;

    // 图标
    private String icon;

    // 效果图标
    private String effectIcon;


    @Override
    protected SsPortalPolicy build() {
        SsPortalPolicy entity = new SsPortalPolicy();
        entity.setCreateTime(createTime());
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    @Override
    protected SsPortalPolicy update(String id) {
        SsPortalPolicy entity = new SsPortalPolicy();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(SsPortalPolicy entity) {
        entity.setName(name);
        entity.setIcon(icon);
        entity.setEffectIcon(effectIcon);
    }

}