package org.thingsboard.server.dao.station;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.StationAttrEntity;
import org.thingsboard.server.dao.sql.station.StationAttrRepository;

import java.util.List;

import static org.thingsboard.server.common.data.CacheConstants.STATION_ATTR_CACHE;

@Slf4j
@Service
public class StationAttrServiceImpl implements StationAttrService{

    @Autowired
    private StationAttrRepository stationAttrRepository;

    @Override
    @CacheEvict(cacheNames = STATION_ATTR_CACHE, allEntries = true)
    public void save(StationAttrEntity stationAttr) {
        stationAttrRepository.save(stationAttr);
    }

    @Override
    public List<StationAttrEntity> getList(String stationId, String type) {
        return stationAttrRepository.findByStationIdAndTypeOrderByOrderNum(stationId, type);
    }

    @Override
    public List<StationAttrEntity> getList(List<String> stationIdList, String type) {
        return stationAttrRepository.findByStationIdInAndTypeOrderByOrderNum(stationIdList, type);
    }

    @Override
    public void deleteByStationId(String id) {
        stationAttrRepository.deleteByStationId(id);
    }

    @Override
    @CacheEvict(cacheNames = STATION_ATTR_CACHE, allEntries = true)
    public void deleteByStationIdAndType(String stationId, String type) {
        stationAttrRepository.deleteByStationIdAndType(stationId, type);
    }

    @Override
    public List<StationAttrEntity> findByStation(String stationId) {
        return stationAttrRepository.findByStationIdOrderByOrderNum(stationId);
    }

    @Override
    public List<String> groupByStationId(String stationId) {
        return stationAttrRepository.groupByType(stationId);
    }

    @Override
    public StationAttrEntity findById(String attributeId) {
        return stationAttrRepository.findOne(attributeId);
    }

    @Override
    public List<StationAttrEntity> findByIdIn(List<String> ids) {
        return stationAttrRepository.findByIdIn(ids);
    }

    @Override
    @Cacheable(cacheNames = STATION_ATTR_CACHE, key = "{#deviceId, #attrList}")
    public List<StationAttrEntity> findByDeviceIdAndAttr(String deviceId, List<String> attrList) {
        return stationAttrRepository.findByDeviceIdAndAttrIn(deviceId, attrList);
    }

    @Override
    @Cacheable(cacheNames = STATION_ATTR_CACHE, key = "{#deviceId}")
    public List<StationAttrEntity> findByDeviceId(String deviceId) {
        return stationAttrRepository.findByDeviceId(deviceId);
    }

    @Override
    public List<StationAttrEntity> findByStationIdInAndAttr(List<String> id, String attr) {
        return stationAttrRepository.findByStationIdInAndAttr(id, attr);
    }

}
