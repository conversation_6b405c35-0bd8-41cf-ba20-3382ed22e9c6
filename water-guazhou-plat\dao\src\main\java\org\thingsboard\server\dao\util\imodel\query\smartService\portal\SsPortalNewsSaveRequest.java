package org.thingsboard.server.dao.util.imodel.query.smartService.portal;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartService.portal.SsPortalNews;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

@Getter
@Setter
public class SsPortalNewsSaveRequest extends SaveRequest<SsPortalNews> {
    // 标题
    private String title;

    // 摘要
    private String summary;

    // 封面
    private String cover;

    // 内容
    private String content;

    // 所属目录id
    @NotNullOrEmpty
    private String packageId;

    // 附件
    private String attachment;


    @Override
    protected SsPortalNews build() {
        SsPortalNews entity = new SsPortalNews();
        entity.setCreateTime(createTime());
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    @Override
    protected SsPortalNews update(String id) {
        SsPortalNews entity = new SsPortalNews();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(SsPortalNews entity) {
        entity.setTitle(title);
        entity.setSummary(summary);
        entity.setCover(cover);
        entity.setContent(content);
        entity.setActive(false);
        entity.setIsRecommend(false);
        entity.setIsHot(false);
        entity.setPackageId(packageId);
        entity.setAttachment(attachment);
    }

}