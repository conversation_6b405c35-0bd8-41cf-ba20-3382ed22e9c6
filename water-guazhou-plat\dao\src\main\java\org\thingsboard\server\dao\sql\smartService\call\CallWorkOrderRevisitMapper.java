package org.thingsboard.server.dao.sql.smartService.call;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.smartService.call.CallWorkOrderRevisit;

import java.util.List;

/**
 * userMybatis
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-08-27
 */
@Mapper
public interface CallWorkOrderRevisitMapper extends BaseMapper<CallWorkOrderRevisit> {

    List<CallWorkOrderRevisit> getList(@Param("workOrderId") String workOrderId);
}
