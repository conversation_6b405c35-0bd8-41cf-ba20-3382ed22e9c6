import{d as D,M as x,c as d,s as S,r as c,a8 as V,x as g,ar as L,a9 as T,bT as U,D as N,o as k,g as E,n as W,q as u,i as m,b6 as C,b7 as P}from"./index-r0dFAfgr.js";import{_ as I}from"./CardTable-rdWOL4_6.js";import{_ as M}from"./CardSearch-CB_HNR-Q.js";import{I as f}from"./common-CvK_P_ao.js";import{b as z,g as Y,a as R}from"./equipmentScrapped-Gar_DpYq.js";import{g as q}from"./equipmentOutStock-BiNkB8x8.js";import{b as A}from"./equipmentPurchase-KOqzaoYr.js";import{a as B}from"./ledgerManagement-CkhtRd8m.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";const H={class:"wrapper"},ee=D({__name:"index",setup($){const{$btnPerms:b}=x(),o=d(),n=d(),h=d({filters:[{label:"报废单编码",field:"code",type:"input",labelWidth:"90px"},{label:"报废单标题",field:"name",type:"input",labelWidth:"90px"},{label:"申请人员",field:"uploadUserId",type:"department-user"},{label:"经办人",field:"handleUserId",type:"department-user"},{type:"date",label:"报废单创建时间",field:"createTime",labelWidth:"120px",format:"YYYY-MM-DD HH:mm:ss",onChange:()=>p()}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",icon:f.QUERY,click:()=>p()},{type:"default",perm:!0,text:"重置",svgIcon:S(P),click:()=>{var e;(e=o.value)==null||e.resetForm(),p()}}]}]}),i=c({defaultExpandAll:!0,indexVisible:!0,columns:[{label:"报废单标题",prop:"name"},{label:"报废单编码",prop:"code"},{label:"报废申请时间",prop:"uploadTime"},{label:"申请部门",prop:"uploadUserDepartmentName"},{label:"申请人",prop:"uploadUserName"},{label:"经办人",prop:"handleUserName"},{label:"创建人",prop:"creatorName"},{label:"创建时间",prop:"createTime"},{label:"报废状态",prop:"isDump",formatter:e=>e.isDump?"已报废":"待报废"}],operationWidth:"160px",operations:[{hide:e=>e.isDump,type:"success",text:"报废",perm:b("RoleManageEdit"),icon:f.DELETE,click:e=>_(e)},{hide:e=>!e.isDump,type:"primary",text:"详情",perm:b("RoleManageEdit"),icon:f.DETAIL,click:e=>y(e)}],dataList:[],pagination:{page:1,limit:20,total:0,refreshData:({page:e,size:a})=>{i.pagination.page=e,i.pagination.limit=a,p()}}}),s=c({title:"设备选择",labelWidth:"130px",defaultValue:{},submitting:!1,btns:[],group:[{fields:[{xl:8,type:"input",label:"报废单标题",field:"name",disabled:!0},{xl:8,type:"input",label:"报废单编码",field:"code",disabled:!0},{xl:8,type:"input",label:"申请部门",field:"uploadUserDepartmentName",disabled:!0},{xl:8,type:"input",label:"申请人",field:"uploadUserName",disabled:!0},{xl:8,type:"input",label:"经办人",field:"handleUserName",disabled:!0},{xl:8,type:"input",label:"创建人",field:"creatorName",disabled:!0},{xl:8,type:"date",label:"创建时间",field:"createTime",readonly:!0},{type:"table",field:"items",config:{indexVisible:!0,height:"350px",dataList:V(()=>l.selectList),columns:[{label:"标签编码",prop:"deviceLabelCode"},{label:"设备名称",prop:"name"},{label:"规格型号",prop:"model"},{label:"所属大类",prop:"topType"},{label:"所属类别",prop:"type"},{label:"货架",prop:"shelvesName"},{label:"设备状态",prop:"isDumped",formatter:e=>e.isDumped?"已报废":"未报废"}],pagination:{hide:!0}}}]}]}),y=e=>{var a;for(const t in e)(e[t]===void 0||e[t]===null)&&(e[t]=" ");s.defaultValue={...e||{}},s.btns=[],(a=n.value)==null||a.openDrawer(),l.getSelectValue(e)},_=e=>{var a;s.defaultValue={...e||{}},s.btns=[{type:"primary",perm:!0,text:"报废",click:()=>{s.submitting=!0,z(e.id).then(()=>{var t;p(),g.success("报废完成"),(t=n.value)==null||t.closeDrawer(),s.submitting=!1}).catch(t=>{g.warning("您不是该报废任务执行人"),s.submitting=!1})}}],l.getSelectValue(e),(a=n.value)==null||a.openDrawer()},l=c({WaterSupplyTree:[],UserList:[],DevicePurchase:[],storeList:[],deviceValue:[],total:0,selectList:[],getDevice:e=>{const a={size:99999,page:1,...e};B(a).then(t=>{l.deviceValue=t.data.data.data||[],l.total=t.data.data.total||0})},getWaterSupplyTreeValue:()=>{L(2).then(a=>{l.WaterSupplyTree=T(a.data.data||[])})},getUserListValue:e=>{U({pid:e}).then(a=>{const t=a.data.data.data||[];l.UserList=t.map(r=>({label:r.firstName,value:N(r.id.id)}))})},getDevicePurchaseValue:()=>{A({page:1,size:99999}).then(a=>{const t=a.data.data.data||[];l.DevicePurchase=t.map(r=>({label:r.title,value:r.id}))})},getstoreSerchValue:()=>{q({page:1,size:99999}).then(a=>{const t=a.data.data.data||[];l.storeList=t.map(r=>({label:r.name,value:r.id}))})},getSelectValue:e=>{const a={page:1,size:99999,mainId:e.id};Y(a).then(t=>{l.selectList=t.data.data.data||[]})},init:()=>{l.getWaterSupplyTreeValue(),l.getDevicePurchaseValue(),l.getstoreSerchValue()}}),p=async()=>{var a;const e={size:i.pagination.limit,page:i.pagination.page,...((a=o.value)==null?void 0:a.queryParams)||{}};R(e).then(t=>{i.dataList=t.data.data.data||[],i.pagination.total=t.data.data.total||0})};return k(()=>{p(),l.init()}),(e,a)=>{const t=M,r=I,v=C;return E(),W("div",H,[u(t,{ref_key:"refSearch",ref:o,config:m(h)},null,8,["config"]),u(r,{config:m(i),class:"card-table"},null,8,["config"]),u(v,{ref_key:"scrappedref",ref:n,config:m(s)},null,8,["config"])])}}});export{ee as default};
