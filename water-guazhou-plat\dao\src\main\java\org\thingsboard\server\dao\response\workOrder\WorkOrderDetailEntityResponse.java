package org.thingsboard.server.dao.response.workOrder;

import lombok.Data;

@Data
@Deprecated
public class WorkOrderDetailEntityResponse {
    // 子表记录ID
    private String id;

    // 主表工单ID
    private String mainId;

    // 当前步骤类型
    private String type;

    // 当前步骤类型显示名
    private String typeName;

    // 处理人
    private String processUserId;

    // 处理时间
    private String processTime;

    // 处理备注
    private String processRemark;

    // 处理详情。json格式的处理详情，用于保存处理流转中的除备注外的字段的数据
    private String processAdditionalInfo;

    // 下一步处理人
    private String nextProcessUserId;

}
