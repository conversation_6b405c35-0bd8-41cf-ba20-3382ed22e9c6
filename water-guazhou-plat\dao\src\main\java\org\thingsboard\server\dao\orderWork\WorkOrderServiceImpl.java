package org.thingsboard.server.dao.orderWork;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.BoundValueOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.User;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.componentStorage.ComponentStorageService;
import org.thingsboard.server.dao.model.DTO.ComponentOptionDTO;
import org.thingsboard.server.dao.model.sql.*;
import org.thingsboard.server.dao.repair.MaintenanceJobService;
import org.thingsboard.server.dao.repair.RepairJobService;
import org.thingsboard.server.dao.sql.workOrder.WorkOrderRepository;
import org.thingsboard.server.dao.user.UserService;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@Deprecated
public class WorkOrderServiceImpl implements WorkOrderService {

    @Autowired
    private WorkOrderRepository workOrderRepository;
    @Autowired
    private RepairJobService repairJobService;
    @Autowired
    private MaintenanceJobService maintenanceJobService;
    @Autowired
    private ComponentStorageService componentStorageService;
    @Autowired
    private UserService userService;
    @Autowired
    private OrderFollowService orderFollowService;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 新增工单
     */
    @Override
    public WorkOrderEntity saveWorkOrder(WorkOrderEntity entity, User currentUser) {
        Date now = new Date();
        // 处理数据
        entity.setStatus("1");// 待接单
        entity.setCreateTime(now);
        entity.setCreator(UUIDConverter.fromTimeUUID(currentUser.getUuidId()));
        entity.setTenantId(UUIDConverter.fromTimeUUID(currentUser.getTenantId().getId()));

        // 生成工单编号
        BoundValueOperations<String, String> workOrderCode = stringRedisTemplate.boundValueOps("workOrderCode");
        String s = workOrderCode.get();
        if (StringUtils.isBlank(s)) {
            s = "1";
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
        String format = simpleDateFormat.format(entity.getCreateTime());
        String prefix = "";
        if (entity.getType().equals("1")) {
            prefix = "WX" + format;
        } else if (entity.getType().equals("2")) {
            prefix = "BY" + format;
        } else {
            prefix = "QT" + format;
        }

        entity.setCode(prefix + String.format("%04d", workOrderCode.increment(Long.parseLong(s))));

        // 关联维修保养
        String type = entity.getType();
        // 维修
        if ("1".equals(type)) {
            RepairJobEntity content = repairJobService.findById(entity.getContentId());
            content.setStatus("2");
            repairJobService.save(content);
        }
        // 保养
        if ("2".equals(type)) {
            MaintenanceJobEntity content = maintenanceJobService.findById(entity.getContentId());
            content.setStatus("2");
            maintenanceJobService.save(content);
        }

        // 保存工单数据
        return workOrderRepository.save(entity);
    }

    @Override
    public PageData<WorkOrderEntity> findList(String type, String code, boolean complete, int page, int size,
                                              User currentUser, Long createTimeStart, Long createTimeEnd, String priority,
                                              String status, String orderType, String creator, String executor) {
        // 分页参数
//        PageRequest pageable = new PageRequest(page - 1, size, Sort.Direction.DESC, "createTime");

        // 查询
        List<String> statusList = new ArrayList<>();

        if (StringUtils.isNotBlank(status)) {
            statusList.add(status);
        } else {
            statusList.add("-1");// 被退单
            statusList.add("1");// 待处理
            statusList.add("2");// 处理中
            statusList.add("3");// 待验收
            if (complete) {
                statusList.add("4");// 已完成的
            }
        }

        List<WorkOrderEntity> dataList = workOrderRepository.findByTenantIdOrderByCreateTimeDesc(UUIDConverter.fromTimeUUID(currentUser.getTenantId().getId()));

        // 我关注的
        String userId = UUIDConverter.fromTimeUUID(currentUser.getUuidId());
        List<OrderFollowEntity> followList = new ArrayList<>();
        Map<String, Boolean> followMap = new HashMap<>();
        if ("4".equals(type)) {
            followList = orderFollowService.findByUserId(userId);
            if (followList != null) {
                followList.forEach(follow -> followMap.put(follow.getOrderId(), true));
            }
        }


        // 条件查询
        dataList = dataList.stream().filter(entity -> {
            // 状态筛选
            String statusValue = entity.getStatus();
            if (!statusList.contains(statusValue)) {
                return false;
            }

            // code筛选
            if (StringUtils.isNotBlank(code)) {
                if (entity.getCode() != null && !entity.getCode().contains(code)) {
                    return false;
                }
            }

            // 时间筛选
            if (createTimeStart != null && createTimeEnd != null) {
                Date createTime = entity.getCreateTime();
                if (!(createTimeStart < createTime.getTime() && createTimeEnd > createTime.getTime())) {
                    return false;
                }
            }

            // 优先级筛选
            if (StringUtils.isNotBlank(priority)) {
                if (!priority.equals(entity.getPriority())) {
                    return false;
                }
            }

            // 用户筛选
            if (StringUtils.isNotBlank(creator)) {
                if (!creator.equals(entity.getCreator())) {
                    return false;
                }
            }
            if (StringUtils.isNotBlank(executor)) {
                if (!executor.equals(entity.getExecutor())) {
                    return false;
                }
            }

            // 工单类型
            if (StringUtils.isNotBlank(orderType)) {
                if (!orderType.equals(entity.getType())) {
                    return false;
                }
            }

            // 我关注的
            if ("4".equals(type)) {
                if (!followMap.containsKey(entity.getId())) {
                    return false;
                }
            }
            // 我发布的
            if ("3".equals(type)) {
                if (!userId.equals(entity.getCreator())) {
                    return false;
                }
            }
            // 我处理的
            if ("2".equals(type)) {
                if (!userId.equals(entity.getExecutor())) {
                    return false;
                }
            }

            return true;
        }).sorted((o1, o2) -> {
            Date createTime1 = o1.getCreateTime();
            Date createTime2 = o2.getCreateTime();
            return createTime2.compareTo(createTime1);
        }).collect(Collectors.toList());

        PageData<WorkOrderEntity> pageResult = PageData.page(dataList, page, size);

        // 查询用户列表
        List<User> userList = userService.findUserByTenant(currentUser.getTenantId());
        Map<String, User> userMap = new HashMap<>();
        if (userList != null) {
            userList.forEach(user -> userMap.put(UUIDConverter.fromTimeUUID(user.getUuidId()), user));
        }
        List<WorkOrderEntity> content = pageResult.getData();
        for (WorkOrderEntity order : content) {
            String executorValue = order.getExecutor();
            if (StringUtils.isNotBlank(executorValue)) {
                User user = userMap.get(executorValue);
                if (user != null) {
                    order.setExecutorName(user.getFirstName());
                }
            }
            String creatorValue = order.getCreator();
            if (StringUtils.isNotBlank(creatorValue)) {
                User user = userMap.get(creatorValue);
                if (user != null) {
                    order.setCreatorName(user.getFirstName());
                }
            }
        }

        return pageResult;
    }

    @Override
    public WorkOrderEntity getDetail(String id, TenantId tenantId, User currentUser) {
        // 查询工单
        WorkOrderEntity entity = workOrderRepository.findOne(id);
        if (entity != null) {
            // 查询工单内容
            String type = entity.getType();
            if ("1".equals(type)) {
                RepairJobEntity detail = repairJobService.detail(entity.getContentId(), tenantId);
                entity.setContentDetail(detail);
            }
            if ("2".equals(type)) {
                MaintenanceJobEntity detail = maintenanceJobService.detail(entity.getContentId(), tenantId);
                entity.setContentDetail(detail);
            }

            // 是否已关注
            String userId = UUIDConverter.fromTimeUUID(currentUser.getUuidId());
            OrderFollowEntity orderFollow = orderFollowService.findByUserIdAndOrderId(userId, entity.getId());

            entity.setFollow(orderFollow != null);

            // 相关人员名称
            List<User> userList = userService.findUserByTenant(currentUser.getTenantId());
            Map<String, User> userMap = new HashMap<>();
            if (userList != null) {
                userList.forEach(user -> userMap.put(UUIDConverter.fromTimeUUID(user.getUuidId()), user));
            }
            String executorValue = entity.getExecutor();
            if (StringUtils.isNotBlank(executorValue)) {
                User user = userMap.get(executorValue);
                if (user != null) {
                    entity.setExecutorName(user.getFirstName());
                }
            }
            String creatorValue = entity.getCreator();
            if (StringUtils.isNotBlank(creatorValue)) {
                User user = userMap.get(creatorValue);
                if (user != null) {
                    entity.setCreatorName(user.getFirstName());
                }
            }
        }

        return entity;
    }

    @Override
    @Transactional
    public void process(WorkOrderEntity entity, User currentUser) {
        /*
         * 1. 保存工单信息以及变更工单状态
         * 2. 保存检修或者保养的信息
         * 3. 变更备件库数量
         */
        Object detailObj = entity.getContentDetail();

        if (StringUtils.isNotBlank(entity.getId())) {
            entity.setStatus("3");// 待验收
            workOrderRepository.save(entity);

            String type = entity.getType();
            if ("1".equals(type)) {// 检修工单
                RepairJobEntity contentDetail = JSON.parseObject(JSON.toJSONString(detailObj), RepairJobEntity.class);
                List<RepairJobCEntity> jobList = contentDetail.getJobList();

                contentDetail.setStatus("3");// 待验收
                repairJobService.save(contentDetail);

                List<ComponentStorageEntity> componentList = componentStorageService.all(new TenantId(UUIDConverter.fromString(contentDetail.getTenantId())));
                Map<String, ComponentStorageEntity> componentMap = new HashMap<>();
                if (componentList != null && !componentList.isEmpty()) {
                    componentList.forEach(component -> componentMap.put(component.getId(), component));
                }
                for (RepairJobCEntity repairJobCEntity : jobList) {
                    List<ComponentOptionDTO> componentOptions = repairJobCEntity.getComponentOptions();
                    // TODO 记录操作单？
                    List<ComponentStorageEntity> updateList = new ArrayList<>();
                    for (ComponentOptionDTO componentOption : componentOptions) {
                        ComponentStorageEntity component = componentMap.get(componentOption.getComponentId());
                        if (component != null) {
                            component.setNumber(component.getNumber() - componentOption.getNumber());
                            updateList.add(component);
                        }
                    }
                    // 保存数量变化
                    componentStorageService.save(updateList);
                }

                // 保存明细
                repairJobService.save(jobList);
            }
            if ("2".equals(type)) {// 保养工单
                MaintenanceJobEntity contentDetail = JSON.parseObject(JSON.toJSONString(detailObj), MaintenanceJobEntity.class);
                List<MaintenanceJobCEntity> jobList = contentDetail.getJobList();

                contentDetail.setStatus("3");// 待验收
                maintenanceJobService.save(contentDetail);

                List<ComponentStorageEntity> componentList = componentStorageService.all(new TenantId(UUIDConverter.fromString(contentDetail.getTenantId())));
                Map<String, ComponentStorageEntity> componentMap = new HashMap<>();
                if (componentList != null && !componentList.isEmpty()) {
                    componentList.forEach(component -> componentMap.put(component.getId(), component));
                }
                for (MaintenanceJobCEntity maintenanceJobCEntity : jobList) {
                    List<ComponentOptionDTO> componentOptions = maintenanceJobCEntity.getComponentOptions();
                    // TODO 记录操作单？
                    List<ComponentStorageEntity> updateList = new ArrayList<>();
                    for (ComponentOptionDTO componentOption : componentOptions) {
                        ComponentStorageEntity component = componentMap.get(componentOption.getComponentId());
                        if (component != null) {
                            component.setNumber(component.getNumber() - componentOption.getNumber());
                            updateList.add(component);
                        }
                    }
                    // 保存数量变化
                    componentStorageService.save(updateList);
                }

                // 保存明细
                maintenanceJobService.save(jobList);
            }
        }

    }

    @Override
    public void confirm(JSONObject params, User currentUser) {
        /*
         * 1. 查询工单信息
         * 2. 记录工单实际开始时间
         * 3. 变更状态
         */
        String id = params.getString("id");
        boolean confirm = params.getBooleanValue("confirm");
        WorkOrderEntity entity = workOrderRepository.findOne(id);
        if (entity != null) {
            if (confirm) {
                entity.setRealStartTime(new Date());
                entity.setStatus("2");// 待处理

            } else {
                entity.setStatus("-1");
            }
            workOrderRepository.save(entity);
        }
    }

    @Override
    public void acceptance(WorkOrderEntity entity, boolean acceptance, User currentUser) {
        /*
         * 1. 记录验收时间并变更状态
         * 2. 保存工单数据
         */
        if (acceptance) {
            entity.setAcceptanceTime(new Date());
            entity.setStatus("4");// 完成

            workOrderRepository.save(entity);

            // TODO 变更相关维修或巡检任务的状态
        }

    }

    @Override
    public WorkOrderEntity findById(String id) {
        return workOrderRepository.findOne(id);
    }

    @Override
    public List<WorkOrderEntity> findByTenantIdAndType(String tenantId, String type) {
        return workOrderRepository.findByTenantIdAndType(tenantId, type);
    }

    @Override
    public JSONObject follow(JSONObject params) {
        String orderId = params.getString("orderId");
        String userId = params.getString("userId");

        OrderFollowEntity follow = orderFollowService.findByUserIdAndOrderId(userId, orderId);
        if (follow != null) {
            // 取消关注
            orderFollowService.deleteById(follow.getId());
            JSONObject result = new JSONObject();
            result.put("message", "取消关注");
            result.put("result", false);

            return result;
        } else {
            follow = new OrderFollowEntity();
            follow.setOrderId(orderId);
            follow.setUserId(userId);
            follow.setTime(new Date());

            orderFollowService.save(follow);

            JSONObject result = new JSONObject();
            result.put("message", "关注成功");
            result.put("result", true);

            return result;
        }
    }

    @Override
    public Object countOrder(String countType, String queryType, Boolean complete, User currentUser) {
        // 查询
        List<String> statusList = new ArrayList<>();
        statusList.add("-1");// 被退单
        statusList.add("1");// 待处理
        statusList.add("2");// 处理中
        statusList.add("3");// 待验收
        statusList.add("4");// 已完成的
        List<WorkOrderEntity> dataList = workOrderRepository.findByTenantIdOrderByCreateTimeDesc(UUIDConverter.fromTimeUUID(currentUser.getTenantId().getId()));

        // 我关注的
        String userId = UUIDConverter.fromTimeUUID(currentUser.getUuidId());
        List<OrderFollowEntity> followList = new ArrayList<>();
        Map<String, Boolean> followMap = new HashMap<>();
        if ("4".equals(queryType)) {
            followList = orderFollowService.findByUserId(userId);
            if (followList != null) {
                followList.forEach(follow -> followMap.put(follow.getOrderId(), true));
            }
        }
        // 条件查询
        dataList = dataList.stream().filter(entity -> {
            // 状态筛选
            String statusValue = entity.getStatus();
            if (!statusList.contains(statusValue)) {
                return false;
            }

            // 我关注的
            if ("4".equals(queryType)) {
                if (!followMap.containsKey(entity.getId())) {
                    return false;
                }
            }
            // 我发布的
            if ("3".equals(queryType)) {
                if (!userId.equals(entity.getCreator())) {
                    return false;
                }
            }
            // 我处理的
            if ("2".equals(queryType)) {
                if (!userId.equals(entity.getExecutor())) {
                    return false;
                }
            }

            return true;
        }).collect(Collectors.toList());

        // 统计
        Map<String, Long> countObj = new HashMap<>();
        if ("1".equals(countType)) {
            countObj.put("0", 0L);// 其他
            countObj.put("1", 0L);// 维修
            countObj.put("2", 0L);// 保养
        }
        if ("2".equals(countType)) {
            countObj.put("1", 0L);// 低优先级
            countObj.put("2", 0L);// 一般优先级
            countObj.put("3", 0L);// 高优先级
            countObj.put("4", 0L);// 紧急
        }
        for (WorkOrderEntity entity : dataList) {
            if ("1".equals(countType)) {
                long countValue = countObj.get(entity.getType());
                countObj.put(entity.getType(), countValue + 1);
            }
            if ("2".equals(countType)) {
                long countValue = countObj.get(entity.getPriority());
                countObj.put(entity.getPriority(), countValue + 1);
            }
        }

        // 处理数据
        List<JSONObject> result = new ArrayList<>();
        for (Map.Entry<String, Long> entry : countObj.entrySet()) {
            String key = entry.getKey();
            Long value = entry.getValue();

            JSONObject obj = new JSONObject();
            if ("1".equals(countType)) {
                if (key.equals("0")) {
                    obj.put("key", "其他");
                }
                if (key.equals("1")) {
                    obj.put("key", "维修工单");
                }
                if (key.equals("2")) {
                    obj.put("key", "保养工单");
                }
            }
            if ("2".equals(countType)) {
                if (key.equals("1")) {
                    obj.put("key", "低");
                }
                if (key.equals("2")) {
                    obj.put("key", "一般");
                }
                if (key.equals("3")) {
                    obj.put("key", "高");
                }
                if (key.equals("4")) {
                    obj.put("key", "紧急");
                }
            }
            obj.put("value", value);
            result.add(obj);
        }

        return result;
    }

    @Override
    public List<WorkOrderEntity> findByTenantId(TenantId tenantId) {
        return workOrderRepository.findByTenantIdOrderByCreateTimeDesc(UUIDConverter.fromTimeUUID(tenantId.getId()));
    }

    @Override
    public List<WorkOrderEntity> findByTenantIdAndTime(TenantId tenantId, Date startTime, Date endTime) {
        return workOrderRepository.findByTenantIdAndCreateTimeBetweenOrderByCreateTime(UUIDConverter.fromTimeUUID(tenantId.getId()), startTime, endTime);
    }
}
