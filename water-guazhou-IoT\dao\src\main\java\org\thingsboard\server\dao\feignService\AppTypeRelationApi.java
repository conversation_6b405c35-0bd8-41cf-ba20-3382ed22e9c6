package org.thingsboard.server.dao.feignService;

import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.thingsboard.server.dao.model.sql.AppTypeRelation;

import java.util.List;

@Component
@FeignClient("base-service")
public interface AppTypeRelationApi {

    @GetMapping("api/app/type/relation/{id}")
    AppTypeRelation findById(@PathVariable String id);

    @PutMapping("api/app/type/relation/{appTypeId}")
    Boolean assignMenuToAppType(@PathVariable String appTypeId, @RequestBody List<String> menuPoolId);

    @GetMapping("api/app/type/relation/apptypeid/{id}")
    List<AppTypeRelation> findByAppTypeId(@PathVariable String id);
}
