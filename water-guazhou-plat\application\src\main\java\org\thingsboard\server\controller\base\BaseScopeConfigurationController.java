package org.thingsboard.server.controller.base;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.thingsboard.server.dao.base.IBaseScopeConfigurationService;
import org.thingsboard.server.dao.model.sql.base.BaseScopeConfiguration;
import org.thingsboard.server.dao.util.imodel.query.base.BaseScopeConfigurationPageRequest;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;
import org.thingsboard.server.service.aspect.annotation.MonitorPerformance;

import java.util.List;

/**
 * 公共管理平台-范围设置Controller
 *
 * <AUTHOR>
 * @date 2025-06-06
 */
@Api(tags = "公共管理平台-范围设置")
@RestController
@RequestMapping("api/base/scope/configuration")
public class BaseScopeConfigurationController extends BaseController {

    @Autowired
    private IBaseScopeConfigurationService baseScopeConfigurationService;

    /**
     * 查询公共管理平台-范围设置列表
     */
    @MonitorPerformance(description = "平台管理-查询范围设置列表")
    @ApiOperation(value = "查询范围设置列表")
    @GetMapping("/list")
    public IstarResponse list(BaseScopeConfigurationPageRequest baseScopeConfiguration) {
        return IstarResponse.ok(baseScopeConfigurationService.selectBaseScopeConfigurationList(baseScopeConfiguration));
    }

    /**
     * 获取公共管理平台-范围设置详细信息
     */
    @MonitorPerformance(description = "平台管理-查询范围设置详情")
    @ApiOperation(value = "查询范围设置详情")
    @GetMapping(value = "/getDetail")
    public IstarResponse getInfo(String id) {
        return IstarResponse.ok(baseScopeConfigurationService.selectBaseScopeConfigurationById(id));
    }

    /**
     * 新增公共管理平台-范围设置
     */
    @MonitorPerformance(description = "平台管理-新增范围设置")
    @ApiOperation(value = "新增范围设置")
    @PostMapping("/add")
    public IstarResponse add(@RequestBody BaseScopeConfiguration baseScopeConfiguration) {
        return IstarResponse.ok(baseScopeConfigurationService.insertBaseScopeConfiguration(baseScopeConfiguration));
    }

    /**
     * 修改公共管理平台-范围设置
     */
    @MonitorPerformance(description = "平台管理-修改范围设置")
    @ApiOperation(value = "修改范围设置")
    @PostMapping("/edit")
    public IstarResponse edit(@RequestBody BaseScopeConfiguration baseScopeConfiguration) {
        return IstarResponse.ok(baseScopeConfigurationService.updateBaseScopeConfiguration(baseScopeConfiguration));
    }

    /**
     * 删除公共管理平台-范围设置
     */
    @MonitorPerformance(description = "平台管理-删除范围设置")
    @ApiOperation(value = "删除范围设置")
    @DeleteMapping("/deleteIds")
    public IstarResponse remove(@RequestBody List<String> ids) {
        return IstarResponse.ok(baseScopeConfigurationService.deleteBaseScopeConfigurationByIds(ids));
    }
}
