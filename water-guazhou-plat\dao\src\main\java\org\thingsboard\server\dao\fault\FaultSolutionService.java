package org.thingsboard.server.dao.fault;

import org.thingsboard.server.dao.model.sql.fault.FaultSolution;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.List;

/**
 * 班组
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-08-24
 */
public interface FaultSolutionService {

    List<FaultSolution> getListByMainId(String mainId, String name, String tenantId);

    FaultSolution save(FaultSolution faultSolution);

    IstarResponse delete(List<String> ids);
}
