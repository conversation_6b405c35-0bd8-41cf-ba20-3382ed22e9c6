import _ from 'lodash-es'
import moment from 'moment'

export const attr = [
  { label: '余氯', value: 'remainder', unit: 'mg/L' },
  { label: '浊度', value: 'turbidity', unit: 'NTU' },
  { label: 'PH', value: 'ph', unit: '' },
  { label: '溶氧', value: 'oxygen', unit: 'DO' },
  { label: '电导率', value: 'conductance', unit: 'us/cm' }
]

export const attrList: NormalOption[] = [
  { label: '浊度', value: 6.88, data: { unit: 'NTU', status: 'normal' } },
  { label: '余氯', value: 0.153, data: { unit: 'mg/L', status: 'normal' } },
  { label: 'PH', value: 8.27, data: { unit: '', status: 'normal' } },
  { label: '电导率', value: 3.142, data: { unit: 'us/cm', status: 'warning' } },
  { label: '温度', value: 14.25, data: { unit: '℃', status: 'normal' } }
]
export const initAttrOption = (): NormalOption[] => attrList.map(item => ({ label: item.label, value: item.label }))
export const initTreeDasta = (): NormalOption[] => [
  {
    label: '城东水司',
    value: '城东水司',
    id: '城东水司',
    data: { type: '水司' },
    children: [
      {
        label: '水厂一',
        value: '水厂一',
        id: '水厂一',
        data: {
          type: '水厂'
        }
      },

      {
        label: '水厂二',
        value: '水厂二',
        id: '水厂二',
        data: {
          type: '水厂'
        },
        children: [
          {
            label: '水质监测点',
            value: '水质监测点',
            id: '水质监测点',
            data: {
              type: '监测点'
            },
            children: [
              {
                label: '水厂二入水水质',
                value: '水厂二入水水质',
                id: '水厂二入水水质',
                data: { type: '属性', basicVal: 0.6 }
              },
              {
                label: '水厂二出水水质',
                value: '水厂二出水水质',
                id: '水厂二出水水质',
                data: { type: '属性', basicVal: 0.1 }
              },
              {
                label: '水质监测点1',
                value: '水质监测点1',
                id: '水质监测点1',
                data: { type: '属性', basicVal: 0.4 }
              },
              {
                label: '水质监测点2',
                value: '水质监测点2',
                id: '水质监测点2',
                data: { type: '属性', basicVal: 0.6 }
              },
              {
                label: '水质监测点3',
                value: '水质监测点3',
                id: '水质监测点3',
                data: { type: '属性', basicVal: 0.5 }
              },
              {
                label: '水质监测点4',
                value: '水质监测点4',
                id: '水质监测点4',
                data: { type: '属性', basicVal: 0.2 }
              }
            ]
          },
          {
            label: '子项目2',
            value: '子项目2',
            id: '子项目2',

            data: {
              type: '监测点'
            }
          }
        ]
      }
    ]
  },
  {
    label: '城西水司',
    value: '城西水司',
    id: '城西水司',
    data: { type: '水司' },
    children: [
      {
        data: { type: '水厂' },
        label: '西城水厂',
        value: '西城水厂',
        id: '西城水厂',
        children: []
      },
      {
        data: { type: '水厂' },
        label: '城西水厂二',
        value: '城西水厂二',
        id: '城西水厂二'
      },
      {
        data: { type: '水厂' },
        label: '水厂三',
        value: '水厂三',
        id: '水厂三',
        children: []
      }
    ]
  }
]
export const initAttrTreeDasta = (): NormalOption[] => [
  {
    label: '城东水司',
    value: '城东水司',
    id: '城东水司',
    data: { type: '水司' },
    children: [
      {
        label: '水厂一',
        value: '水厂一',
        id: '水厂一',
        data: {
          type: '水厂'
        }
      },

      {
        label: '水厂二',
        value: '水厂二',
        id: '水厂二',
        data: {
          type: '水厂'
        },
        children: [
          {
            label: '水质监测点',
            value: '水质监测点',
            id: '水质监测点',
            data: {
              type: '监测点'
            },
            children: [
              {
                label: '水厂二入水水质',
                value: '水厂二入水水质',
                id: '水厂二入水水质',
                data: { type: '属性', basicVal: 0.6 },
                children: [
                  {
                    value: '水厂二入水水质-浊度(NTU)',
                    label: '浊度',
                    id: '浊度',
                    data: { type: '变量', basicVal: 0.8 }
                  },
                  {
                    value: '水厂二入水水质-PH',
                    label: 'PH',
                    id: 'PH',
                    data: { type: '变量', basicVal: 8 }
                  }
                ]
              },
              {
                label: '水厂二出水水质',
                value: '水厂二出水水质',
                id: '水厂二出水水质',
                data: { type: '属性', basicVal: 0.1 },

                children: [
                  {
                    value: '水厂二出水水质-浊度(NTU)',
                    label: '浊度',
                    id: '浊度',
                    data: { type: '变量', basicVal: 0.1 }
                  },
                  {
                    value: '水厂二出水水质-PH',
                    label: 'PH',
                    id: 'PH',
                    data: { type: '变量', basicVal: 7.5 }
                  }
                ]
              },
              {
                label: '水质监测点1',
                value: '水质监测点1',
                id: '水质监测点1',
                data: { type: '属性', basicVal: 0.4 }
              },
              {
                label: '水质监测点2',
                value: '水质监测点2',
                id: '水质监测点2',
                data: { type: '属性', basicVal: 0.6 }
              },
              {
                label: '水质监测点3',
                value: '水质监测点3',
                id: '水质监测点3',
                data: { type: '属性', basicVal: 0.5 }
              },
              {
                label: '水质监测点4',
                value: '水质监测点4',
                id: '水质监测点4',
                data: { type: '属性', basicVal: 0.2 }
              }
            ]
          },
          {
            label: '子项目2',
            value: '子项目2',
            id: '子项目2',

            data: {
              type: '监测点'
            }
          }
        ]
      }
    ]
  },
  {
    label: '城西水司',
    value: '城西水司',
    id: '城西水司',
    data: { type: '水司' },
    children: [
      {
        data: { type: '水厂' },
        label: '西城水厂',
        value: '西城水厂',
        id: '西城水厂',
        children: []
      },
      {
        data: { type: '水厂' },
        label: '城西水厂二',
        value: '城西水厂二',
        id: '城西水厂二'
      },
      {
        data: { type: '水厂' },
        label: '水厂三',
        value: '水厂三',
        id: '水厂三',
        children: []
      }
    ]
  }
]
export const initWaterLineTable = (datas: NormalOption[] = []) => {
  const column: IFormTableColumn[] = [{ label: '时间', prop: 'time' }]
  datas
    .filter(item => item.data?.type === '属性')
    .map(item => {
      column.push({
        label: item.label,
        prop: item.value?.toString()
      })
    })
  return column
}
export const initWaterLineTableData = (datas: NormalOption[]) => {
  // const basic = (attrList.find(item => item.label === type)?.value as number) || 0
  const data: any[] = []
  const items = datas.filter(item => item.data?.type === '属性')
  for (let i = 0; i < 24; i++) {
    const x = i < 10 ? `0${i}` : i
    const Data: any = {
      time: `${x}:00`
    }
    items.map(item => {
      const basic = item.data?.basicVal
      const type = item.value as string
      if (!Data[type]) {
        Data[type] = Number((Math.random() * basic * 0.1 + basic).toFixed(3))
      }
    })
    data.push(Data)
  }
  const topData: any[] = [
    { time: '最大值' },
    { time: '最大值时间' },
    { time: '最小值' },
    { time: '最小值时间' },
    { time: '平均值' }
  ]
  datas.map(item => {
    topData.map(obj => {
      const field = item.value as string
      let time = 0
      switch (obj.time) {
        case '最大值':
          obj[field] = _.maxBy(data, o => o[field])[field]

          break
        case '最大值时间':
          time = Number((Math.random() * 24).toFixed(0))
          obj[field] = `${time < 10 ? `0${time}` : time} :00`
          break
        case '最小值':
          obj[field] = _.minBy(data, o => o[field])[field]
          break
        case '最小值时间':
          time = Number((Math.random() * 24).toFixed(0))
          obj[field] = `${time < 10 ? `0${time}` : time} :00`
          break
        case '平均值':
          obj[field] = _.meanBy(data, o => o[field]).toFixed(2)
          break
        default:
          break
      }
    })
  })
  return [...topData, ...data]
}

export const initWaterCompareTableColumn = (datas: NormalOption[] = []) => {
  const column: IFormTableColumn[] = [{ label: '时间', prop: 'time' }]
  datas
    .filter(item => item.data?.type === '变量')
    .map(item => {
      column.push({
        label: item.value?.toString(),
        prop: item.value?.toString()
      })
    })
  return column
}
export const initWaterCompareTableData = (datas: NormalOption[] = []) => {
  const Data: any[] = []
  const items = datas.filter(item => item.data?.type === '变量')
  for (let i = 24; i > 0; i--) {
    const data = {
      time: moment().add(i, 'h').format('YYYY-MM-DD HH:mm:ss')
    }
    items.map(item => {
      const basic = item.data?.basicVal || 0
      const type = item.value as string
      data[type] = Number((Math.random() * basic * 0.1 + basic).toFixed(3))
    })
    Data.push(data)
  }
  return Data
}
export const initSearchTableColumn = (): IFormTableColumn[] => [
  { label: '监测点名称', prop: 'name' },
  { label: '数据更新时间', prop: 'updateTime' },
  { label: '浊度(NTU)', prop: 'zhuodu' },
  { label: 'PH', prop: 'PH' },
  { label: '余氯(mg/L)', prop: 'yulv' }
]

export const initSearchTableData = (attr: NormalOption) => {
  const Data: any[] = []
  if (attr) {
    const basic = attr.data?.basicVal
    for (let i = 60; i > 0; i--) {
      const data: any = {
        name: attr.label,
        updateTime: moment().add(i, 'minute').format('YYYY-MM-DD HH:mm:ss'),
        zhuodu: Number((Math.random() * basic * 0.1 + basic).toFixed(3)),
        PH: Number((Math.random() + 7.5).toFixed(3)),
        yulv: Number((Math.random() * 0.5 + 0.3).toFixed(3))
      }
      Data.push(data)
    }
  }

  return Data
}
export const initWaterDailyTableColumn = (type: string): IFormTableColumn[] => {
  let firstfield
  if (type === 'max' || type === 'min') {
    firstfield = { label: '类别', prop: 'type' }
  } else {
    firstfield = { label: '时间', prop: 'time' }
  }
  return [
    firstfield,
    { label: '浊度(NTU)', prop: '浊度' },
    { label: 'PH', prop: 'PH' },
    { label: '余氯(mg/L)', prop: '余氯' },
    { label: '电导率(us/cm)', prop: '电导率' },
    { label: '溶解氧DO', prop: '溶氧' }
  ]
}
export const initWaterDailyMaxTableData = (
  type: 'min' | 'max' | 'report',
  date?: string,
  dateType: 'daily' | 'monthly' | 'yearly' = 'daily'
) => {
  const basic_zhuodu = type === 'max' ? 10 : 5
  const basic_PH = type === 'max' ? 9 : 7.5
  const basic_yulv = type === 'max' ? 0.5 : 0.3
  const basic_diaodaolv = type === 'max' ? 3 : 1.5
  const basic_wendu = type === 'max' ? 15 : 13
  if (type === 'max' || type === 'min') {
    return [
      {
        type: '最大值',
        zhuodu: (Math.random() * basic_zhuodu * 0.2 + basic_zhuodu).toFixed(3),
        PH: (Math.random() * basic_PH * 0.2 + basic_PH).toFixed(3),
        yulv: (Math.random() * basic_yulv * 0.2 + basic_yulv).toFixed(3),
        diandaolv: (
          Math.random() * basic_diaodaolv * 0.2
          + basic_diaodaolv
        ).toFixed(3),
        wendu: (Math.random() * basic_wendu * 0.2 + basic_wendu).toFixed(2)
      },
      {
        type: '发生时间',
        zhuodu: moment(date)
          .add(Number((Math.random() * 24).toFixed(0)))
          .format('YYYY-MM-DD HH:mm:ss'),
        PH: moment(date)
          .add(Number((Math.random() * 24).toFixed(0)))
          .format('YYYY-MM-DD HH:mm:ss'),
        yulv: moment(date)
          .add(Number((Math.random() * 24).toFixed(0)))
          .format('YYYY-MM-DD HH:mm:ss'),
        diandaolv: moment(date)
          .add(Number((Math.random() * 24).toFixed(0)))
          .format('YYYY-MM-DD HH:mm:ss'),
        wendu: moment(date)
          .add(Number((Math.random() * 24).toFixed(0)))
          .format('YYYY-MM-DD HH:mm:ss')
      }
    ]
  }
  const Data: any[] = []
  if (dateType === 'daily') {
    for (let i = 24; i > 0; i--) {
      const data = {
        time: moment(date, 'YYYY-MM-DD')
          .add(-i, 'h')
          .format('YYYY-MM-DD HH:mm:ss'),
        zhuodu: (Math.random() * basic_zhuodu * 0.2 + basic_zhuodu).toFixed(
          3
        ),
        PH: (Math.random() * basic_PH * 0.2 + basic_PH).toFixed(3),
        yulv: (Math.random() * basic_yulv * 0.2 + basic_yulv).toFixed(3),
        diandaolv: (
          Math.random() * basic_diaodaolv * 0.2
            + basic_diaodaolv
        ).toFixed(3),
        wendu: (Math.random() * basic_wendu * 0.2 + basic_wendu).toFixed(2)
      }
      Data.push(data)
    }
  } else if (dateType === 'monthly') {
    for (let i = 0; i < 30; i++) {
      const data = {
        time: moment(date, 'YYYY-MM')
          .startOf('M')
          .add(i, 'd')
          .format('YYYY-MM-DD'),
        zhuodu: (Math.random() * basic_zhuodu * 0.2 + basic_zhuodu).toFixed(
          3
        ),
        PH: (Math.random() * basic_PH * 0.2 + basic_PH).toFixed(3),
        yulv: (Math.random() * basic_yulv * 0.2 + basic_yulv).toFixed(3),
        diandaolv: (
          Math.random() * basic_diaodaolv * 0.2
            + basic_diaodaolv
        ).toFixed(3),
        wendu: (Math.random() * basic_wendu * 0.2 + basic_wendu).toFixed(2)
      }
      Data.push(data)
    }
  } else if (dateType === 'yearly') {
    for (let i = 0; i < 12; i++) {
      const data = {
        time: moment(date, 'YYYY').startOf('y').add(i, 'M').format('YYYY-MM'),
        zhuodu: (Math.random() * basic_zhuodu * 0.2 + basic_zhuodu).toFixed(
          3
        ),
        PH: (Math.random() * basic_PH * 0.2 + basic_PH).toFixed(3),
        yulv: (Math.random() * basic_yulv * 0.2 + basic_yulv).toFixed(3),
        diandaolv: (
          Math.random() * basic_diaodaolv * 0.2
            + basic_diaodaolv
        ).toFixed(3),
        wendu: (Math.random() * basic_wendu * 0.2 + basic_wendu).toFixed(2)
      }
      Data.push(data)
    }
  }
  return Data
}

export const initWaterMonitorReportTableData = () => {
  const types: any[] = [
    { label: '溶解氧(mg/L)', value: 6, data: { max: 9.99, min: 0.01 } },
    { label: '电导率(us/cm)', value: 3, data: { max: 3, min: 1.01 } },
    { label: '温度(℃)', value: 2, data: { max: 31.94, min: 0.01 } },
    { label: '叶绿素(ug/L)', value: 3.33, data: { max: 4, min: 1.0 } },
    { label: '氨氮(mg/L)', value: 2, data: { max: 3, min: 1 } },
    { label: '硝酸盐(mg/L)', value: 2, data: { max: 12, min: 1 } },
    { label: '溶解性有机碳(mg/L)', value: 2, data: { max: 0.5, min: 0.01 } },
    { label: '总有机碳(mg/L)', value: 2, data: { max: 0.5, min: 0.01 } },
    { label: '浊度(NTU)', value: 2, data: { max: 0.5, min: 0.01 } },
    { label: 'PH', value: 2, data: { max: 10, min: 0.01 } },
    { label: '色度(Hazen)', value: 2, data: { max: 0.5, min: 0.01 } },
    { label: 'UV254(Abs/m)', value: 2, data: { max: 10, min: 5 } }
  ]
  const data: any[] = []

  types.map(item => {
    const total = Math.random() * 100 + 600
    const ridb = Math.random() * 100 + 300
    const ricb = total - ridb
    const obj = {
      attr: item.label,
      standard: item.value,
      count: total.toFixed(0),
      max: item.data.max.toFixed(2),
      min: item.data.min.toFixed(2),
      average: ((item.data.max + item.data.min) / 2).toFixed(2),
      dabiao: ridb.toFixed(0),
      chaobiao: ricb.toFixed(0),
      dabiaorate: ((ridb / total) * 100).toFixed(0)
    }
    data.push(obj)
  })
  return data
}
