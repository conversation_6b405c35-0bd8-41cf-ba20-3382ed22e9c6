import { requestPipe } from '@/plugins/axios/gisService'

/**
 * 横剖面分析
 */
export const HorizentalSectionAnalysis = (params: {
  UserToken: string
  X1: number
  Y1: number
  X2: number
  Y2: number
  f: 'pjson'
}) => {
  return requestPipe({
    url: window.SITE_CONFIG.GIS_CONFIG.gisPipeDynamicService + '/exts/TFGeoAPISOE/HorizentalSectionAnalysis',
    method: 'get',
    params
  })
}
/**
 * 纵剖面分析
 */
export const VerticalSectionAnalysis = (params: {
  UserToken: string
  X1: number
  Y1: number
  X2: number
  Y2: number
  Buffer: any
  f: 'pjson'
}) => {
  return requestPipe({
    url: window.SITE_CONFIG.GIS_CONFIG.gisPipeDynamicService + '/exts/TFGeoAPISOE/VerticalSectionAnalysis',
    method: 'get',
    params
  })
}
