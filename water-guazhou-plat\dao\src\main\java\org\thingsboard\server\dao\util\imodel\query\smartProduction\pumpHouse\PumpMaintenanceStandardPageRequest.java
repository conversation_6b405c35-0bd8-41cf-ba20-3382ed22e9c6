package org.thingsboard.server.dao.util.imodel.query.smartProduction.pumpHouse;

import org.thingsboard.server.dao.model.sql.smartProduction.pumpHouse.PumpMaintenanceStandard;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class PumpMaintenanceStandardPageRequest extends AdvancedPageableQueryEntity<PumpMaintenanceStandard, PumpMaintenanceStandardPageRequest> {
    // 文件类型
    private String type;

    // 文件名称
    private String name;

    // 录入人
    private String inputUserName;



}
