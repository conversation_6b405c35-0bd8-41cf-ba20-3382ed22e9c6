/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.service.aspect;


import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.optionLog.OperatingLog;
import org.thingsboard.server.common.data.optionLog.OptionLog;
import org.thingsboard.server.dao.optionLog.OptionLogService;
import org.thingsboard.server.dao.util.mapping.JacksonUtil;
import org.thingsboard.server.service.aspect.annotation.SysLog;
import org.thingsboard.server.service.security.model.SecurityUser;
import org.thingsboard.server.service.utils.IPUtils;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.Date;


/**
 * 系统日志，切面处理类
 * <p>
 * jerry
 */
@Aspect
@Component
@Slf4j
public class SysLogAspect {
    @Autowired
    private OptionLogService logService;


    @Pointcut("@annotation(org.thingsboard.server.service.aspect.annotation.SysLog)")
    public void logPointCut() {

    }

    @Around("logPointCut()")
    public Object around(ProceedingJoinPoint point) throws Throwable {
        long beginTime = System.currentTimeMillis();
        //执行方法
        Object result = point.proceed();
        //执行时长(毫秒)
        long time = System.currentTimeMillis() - beginTime;

        //保存日志
        saveSysLog(point, time);

        return result;
    }

    private void saveSysLog(ProceedingJoinPoint joinPoint, long time) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();

        OptionLog optionLog = new OptionLog();
        SysLog syslog = method.getAnnotation(SysLog.class);
        if (DataConstants.OPERATING_TYPE.OPERATING_TYPE_OTHER.name().equals(syslog.options().name())) {
            return;
        }
        try {
            if (syslog != null) {
                //由于登陆时系统用户和企业用户都使用同一个接口，故此处根据登陆成功后的角色ID来区分是系统用户还是企业用户
                Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
                if (authentication != null && authentication.getPrincipal() instanceof SecurityUser) {
                    SecurityUser user = (SecurityUser) authentication.getPrincipal();
                    OperatingLog operatingLog = new OperatingLog();
                    //请求的方法名
                    String className = joinPoint.getTarget().getClass().getName();
                    String methodName = signature.getName();
                    operatingLog.setMethod(className + "." + methodName + "()");


                    //请求的参数
                    Object[] args = joinPoint.getArgs();
                    try {
                        String params = new ObjectMapper().writeValueAsString(args[0]);
                        operatingLog.setParams(params);
                    } catch (Exception e) {

                    }

                    //获取request
                    HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
                    //设置IP地址
                    operatingLog.setIp(IPUtils.getIpAddr(request));

                    //用户名
                    if (user == null) {
                        return;
                    }
                    optionLog.setFirstName(user.getFirstName() + " (" + user.getName() + ")");
                    optionLog.setUserId(user.getId());
                    optionLog.setTenantId(user.getTenantId());
                    optionLog.setAuthority(user.getAuthority().name());
                    optionLog.setOptions(syslog.options().name());
                    optionLog.setInfo(syslog.options().getDescription());
                    optionLog.setType(String.valueOf(syslog.type()));
                    operatingLog.setContinuedTime(String.valueOf(time));
                    optionLog.setCreateTime(System.currentTimeMillis());
                    optionLog.setAdditionalInfo(JacksonUtil.toJsonNode(operatingLog));
                    //保存系统日志
                    logService.save(optionLog);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("记录操作日志失败！" + e.getMessage());
        }

    }
}
