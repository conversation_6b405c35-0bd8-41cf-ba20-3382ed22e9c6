<!-- 安防监控-门禁管理 -->
<template>
  <div class="wrapper">
    <CardSearch ref="cardSearch" :config="cardSearchConfig" />
    <CardTable
      ref="refTable"
      class="card-table"
      title=" "
      :config="TableConfig"
    ></CardTable>
    <DialogForm ref="refDialogForm" :config="DialogFormConfig">
      <template #fieldSlot="{ config, row }">
        <VideoSelector
          v-if="config.field === 'videos'"
          v-model="row.videos"
          @change="handleVideoChange"
        />
      </template>
    </DialogForm>
  </div>
</template>
<script lang="ts" setup>
import moment from 'moment';
import { onMounted, reactive, ref, computed } from 'vue';
import {
  DeleteAccessControl,
  GetAccessControlPage,
  PostAccessControl,
  BatchSaveAccessControlVideo,
  GetAccessControlVideos
} from '@/api/shuiwureports/menjinguanli';
import { useBusinessStore } from '@/store';
import { objectLookup } from '@/utils/GlobalHelper';
import { formatDate } from '@/utils/DateFormatter';
import { SLConfirm, SLMessage } from '@/utils/Message';
import VideoSelector from '@/components/VideoSelector/VideoSelector.vue';

const businessStore = useBusinessStore();
const cardSearch = ref<ISearchIns>();
const refTable = ref<ICardTableIns>();
const refDialogForm = ref<IDialogFormIns>();
const TreeData = reactive<SLTreeConfig>({
  data: businessStore.projectList,
  currentProject: businessStore.selectedProject
});

// 搜索栏初始化配置
const cardSearchConfig = reactive<ISearch>({
  defaultParams: {},
  filters: [
    {
      type: 'select-tree',
      field: 'treeData',
      checkStrictly: true,
      defaultExpandAll: true,
      options: computed(() => TreeData.data) as any,
      label: '站点选择',
      onChange: (key) => {
        const val = objectLookup(TreeData.data, 'children', 'id', key);
        businessStore.SET_selectedProject(val);
        TreeData.currentProject = val;
        refreshData();
      }
    },
    {
      label: '门禁名称',
      type: 'input',
      field: 'name'
    },
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          click: () => refreshData(),
          icon: 'iconfont icon-chaxun'
        }
      ]
    }
  ]
});

const TableConfig = reactive<ICardTable>({
  title: '门禁监控',
  titleRight: [
    {
      style: {
        marginLeft: 'auto'
      },
      items: [
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              type: 'primary',
              text: '新增',
              icon: 'iconfont icon-jia',
              click: () => handleAdd()
            },
            {
              perm: true,
              type: 'danger',
              text: '批量删除',
              disabled: (): boolean => !TableConfig.selectList?.length,
              icon: 'iconfont icon-shanchu',
              click: () => {
                handleDelete();
              }
            }
          ]
        }
      ]
    }
  ],
  columns: [
    { label: '门禁名称', prop: 'name' },
    { label: '门名称', prop: 'doorName' },
    { label: '设备号', prop: 'deviceId' },
    { label: '安装位置', prop: 'installAddress' },
    { label: '安装人员', prop: 'installerName' },
    {
      label: '安装时间',
      prop: 'installTime',
      formatter: (row) => formatDate(row.installTime)
    },
    {
      label: '关联视频',
      prop: 'videoCount',
      formatter: (row) => row.videoCount ? `${row.videoCount}个` : '无'
    },
    { label: '安装说明', prop: 'remark' }
  ],
  dataList: [],
  operationWidth: 250,
  operations: [
    // {
    //   perm: true,
    //   type: 'primary',
    //   isTextBtn: true,
    //   text: '远程开门',
    //   size: 'small',
    //   click: (row: any) => {console.log(row)}
    // },
    {
      perm: true,
      type: 'success',
      isTextBtn: true,
      text: '查看视频',
      size: 'small',
      disabled: (row: any) => !row.videoCount,
      click: (row: any) => handleViewVideos(row)
    },
    {
      perm: true,
      type: 'primary',
      isTextBtn: true,
      text: '编辑',
      size: 'small',
      click: (row: any) => handleAdd(row)
    },
    {
      perm: true,
      type: 'danger',
      isTextBtn: true,
      text: '删除',
      size: 'small',
      icon: 'iconfont icon-shanchu',
      click: (row: any) => handleDelete(row)
    }
  ],
  pagination: {
    total: 0,
    page: 1,
    align: 'right',
    limit: 20,
    handlePage: (page: number) => {
      TableConfig.pagination.page = page;
      refreshData();
    },
    handleSize: (size: number) => {
      TableConfig.pagination.limit = size;
      refreshData();
    }
  },
  handleSelectChange: (rows?: any[]) => {
    TableConfig.selectList = rows || [];
  }
});
const DialogFormConfig = reactive<IDialogFormConfig>({
  title: '添加门禁监控',
  dialogWidth: 800,
  group: [
    {
      fields: [
        {
          type: 'input',
          field: 'name',
          label: '门禁名称',
          rules: [{ required: true, message: '请输入门禁名称' }]
        },
        {
          type: 'input',
          field: 'deviceId',
          label: '设备号',
          rules: [{ required: true, message: '请输入设备号' }]
        },
        { type: 'input', field: 'doorName', label: '所在门' },
        { type: 'input', field: 'installAddress', label: '安装位置' },
        { type: 'input', field: 'installerName', label: '安装人' },
        { type: 'datetime', field: 'installTime', label: '安装时间' },
        { type: 'textarea', field: 'remark', label: '安装说明' },
        {
          type: 'slot',
          field: 'videos',
          label: '关联视频',
          span: 24
        }
        // { type: 'input', field: 'createTme', label: '创建时间' },
        // { type: 'input', field: 'projectId', label: '项目ID' },
        // { type: 'input', field: 'tenantId', label: '租户ID' }
      ]
    }
  ],
  submit: (params: any) => {
    SLConfirm('确定提交', '提示信息')
      .then(async () => {
        try {
          DialogFormConfig.submitting = true;
          const submitData = { ...(params || {}) };

          // 处理安装时间
          if (submitData.installTime) {
            submitData.installTime = moment(
              submitData.installTime,
              'YYYY-MM-DD hh:mm:ss'
            ).valueOf();
          }

          // 提取视频数据，不包含在门禁数据中
          const selectedVideos = submitData.videos || [];
          delete submitData.videos;

          // 保存门禁信息
          const res = await PostAccessControl(submitData);
          if (res.data?.id) {
            // 如果有选择视频，保存视频关联
            if (selectedVideos.length > 0) {
              const videoIds = selectedVideos.map((video: any) => video.id);
              await BatchSaveAccessControlVideo({
                accessControlId: res.data.id,
                videoIds
              });
            }

            SLMessage.success('操作成功');
            refDialogForm.value?.closeDialog();
            refreshData();
          } else {
            SLMessage.error('操作失败');
          }
        } catch (error) {
          SLMessage.error('操作失败：' + (error as any)?.message || '未知错误');
        }
        DialogFormConfig.submitting = false;
      })
      .catch(() => {
        //
      });
  }
});
const handleAdd = async (row?: any) => {
  const projectId = TreeData.currentProject?.data?.id;
  if (!projectId) return SLMessage.warning('范围信息不存在，无法进行添加操作');

  let defaultValue: any = {
    ...(row || { projectId }),
    installTime: formatDate(row?.installTime),
    videos: []
  };

  // 如果是编辑模式，加载关联的视频
  if (row?.id) {
    try {
      const videoRes = await GetAccessControlVideos(row.id);
      defaultValue.videos = videoRes.data || [];
    } catch (error) {
      // 加载关联视频失败，使用默认值
    }
  }

  DialogFormConfig.defaultValue = defaultValue;
  DialogFormConfig.title = row ? '编辑门禁监控' : '添加门禁监控';
  refDialogForm.value?.openDialog();
};
const handleDelete = (row?: any) => {
  SLConfirm('确定删除？', '提示信息')
    .then(async () => {
      try {
        const ids = row?.id
          ? [row.id]
          : TableConfig.selectList!.map((item) => item.id);
        const res = await DeleteAccessControl(ids);
        if (res.data?.length) {
          SLMessage.success('删除成功');
          refreshData();
        } else {
          SLMessage.error('删除失败');
        }
      } catch (error) {
        SLMessage.error('系统错误');
      }
    })
    .catch(() => {
      //
    });
};
const refreshData = async () => {
  const queryParams = (cardSearch.value?.queryParams as any) || {};
  const res = await GetAccessControlPage({
    page: TableConfig.pagination.page || 1,
    size: TableConfig.pagination.limit || 20,
    projectId: TreeData.currentProject?.value
  });

  let dataList = (res.data?.data || []).filter(
    (item) => item.name.indexOf(queryParams.name || '') !== -1
  );

  // 为每个门禁加载关联的视频数量
  for (const item of dataList) {
    try {
      const videoRes = await GetAccessControlVideos(item.id);
      item.videoCount = (videoRes.data || []).length;
    } catch (error) {
      item.videoCount = 0;
    }
  }

  TableConfig.dataList = dataList;
  TableConfig.pagination.total = res.data?.total || 0;
};

// 处理视频选择变化
const handleVideoChange = (videos: any[]) => {
  // 视频选择变化处理
};

// 查看关联视频
const handleViewVideos = async (row: any) => {
  try {
    const videoRes = await GetAccessControlVideos(row.id);
    const videos = videoRes.data || [];

    if (videos.length === 0) {
      SLMessage.info('该门禁暂无关联视频');
      return;
    }

    const videoNames = videos.map((video: any) => video.name).join('、');
    SLMessage.info(`关联视频：${videoNames}`);
  } catch (error) {
    SLMessage.error('获取关联视频失败');
  }
};

onMounted(async () => {
  refreshData();
  cardSearchConfig.defaultParams = {
    ...cardSearchConfig.defaultParams,
    treeData: TreeData.currentProject
  };
  cardSearch.value?.resetForm();
});
</script>
<style lang="scss" scoped>
.card-table {
  height: 100%;
}
</style>
