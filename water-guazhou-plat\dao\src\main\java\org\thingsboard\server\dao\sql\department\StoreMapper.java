package org.thingsboard.server.dao.sql.department;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.thingsboard.server.dao.model.sql.store.Store;
import org.thingsboard.server.dao.util.imodel.query.store.StorePageRequest;

@Mapper
public interface StoreMapper extends BaseMapper<Store> {
    IPage<Store> findByPage(StorePageRequest request);

    boolean update(Store store);

    String getNameById(String id);

    String getCode(String id);

    boolean canBeDelete(String id);

}
