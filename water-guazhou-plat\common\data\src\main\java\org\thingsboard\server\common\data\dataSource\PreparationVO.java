package org.thingsboard.server.common.data.dataSource;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2020/4/8 15:10
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PreparationVO {
    /**
     * 绑定预设值名称
     */
    private String sourceName;
    /**
     * 绑定预设变量
     */
    private String preparation;
    /**
     * 统计间隔
     */
    private String frequency;
}
