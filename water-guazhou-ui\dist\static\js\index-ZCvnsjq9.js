import{_ as I}from"./CardTable-rdWOL4_6.js";import{_ as D}from"./CardSearch-CB_HNR-Q.js";import{d as P,M as j,r as f,c as T,bF as l,a8 as k,a0 as A,s as h,bu as N,g as E,n as K,q,i as x,al as B,b7 as H,aj as G,C as R}from"./index-r0dFAfgr.js";import{e as F,a as M}from"./queryStatistics-CQ9DBM08.js";import{f as U}from"./formartColumn-D5r7JJ2G.js";import{b as W}from"./zhandian-YaGuQZe6.js";import{d as Q,a as V}from"./useStation-DJgnSZIA.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";const $={class:"wrapper"},z=P({__name:"index",setup(J){const{$messageWarning:b}=j(),{getStationAttrGroups:C,getStationTree:X}=Q(),m=f({type:"date",chartOption:null,stationTree:[],activeName:"echarts",data:null,checkedKeys:[]}),O=[{label:"1 m",value:"1m"},{label:"5 m",value:"5m"},{label:"10 m",value:"10m"},{label:"15 m",value:"15m"},{label:"1小时",value:"hour"}],p=T(),w=T();let y=f([]);const g=f({data:[],title:"区域划分",showCheckbox:!0,defaultExpandAll:!0,accordion:!1,checkedKeys:[],handleCheck:(e,t)=>{console.log(t.checkedNodes,t.checkedKeys),g.checkedKeys=t.checkedKeys||[],g.checkedNodes=t.checkedNodes||[],_()},nodeExpand:async(e,t)=>{var a;if(((a=e.data)==null?void 0:a.type)==="Station"&&e.children[0].id===0){const n=await C(e.id,!0);t.data.children=n}}}),L=f({defaultParams:{queryType:"15m",type:"day",year:[l().format(),l().format()],month:[l().format(),l().format()],day:[l().startOf("day").format(),l().format()]},filters:[{type:"select-tree",field:"treeData",defaultExpandAll:!0,options:k(()=>A().projectList),label:"站点选择",onChange:async e=>{const t=await V().getStations("水厂",e);m.stationTree=t.map(a=>({...a,label:a.name}))}},{type:"select-tree",label:"监测点:",multiple:!0,field:"attributeId",clearable:!1,showCheckbox:!0,lazy:!0,options:k(()=>m.stationTree),lazyLoad:(e,t)=>{var a,n;if(e.level===0)return t([]);if(((a=e.data.children)==null?void 0:a.length)>0)return t(e.data.children);if(e.isLeaf)return t([]);if((n=e.data)!=null&&n.isLeaf)return t([]);W({stationId:e.data.id}).then(s=>{var d;const i=(d=s.data)==null?void 0:d.map(u=>({label:u.type,value:"",id:"",children:u.attrList.map(r=>({label:r.name,value:r.id,id:r.id,isLeaf:!0}))}));return t(i)})}},{type:"select",field:"type",options:[{label:"日报",value:"day"},{label:"月报",value:"month"},{label:"年报",value:"year"}],label:"报表"},{type:"datetimerange",label:"选择日期",field:"day",clearable:!1,handleHidden:(e,t,a)=>{a.hidden=e.type==="month"||e.type==="year"}},{type:"monthrange",label:"选择日期",field:"month",clearable:!1,handleHidden:(e,t,a)=>{a.hidden=e.type==="day"||e.type==="year"}},{type:"yearrange",label:"选择日期",field:"year",clearable:!1,handleHidden:(e,t,a)=>{a.hidden=e.type==="month"||e.type==="day"}},{type:"select",label:"时间间隔:",field:"queryType",clearable:!1,allowCreate:!0,handleHidden:(e,t,a)=>{a.hidden=e.type==="month"||e.type==="year"},options:O,itemContainerStyle:{width:"180px"}}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",click:()=>{var t;o.pagination.page=1;const e=((t=p.value)==null?void 0:t.queryParams)||{};e.attributeId&&e.attributeId.length>0?_():b("选择监测点")},svgIcon:h(B)},{type:"default",perm:!0,text:"重置",svgIcon:h(H),click:()=>{var e;(e=p.value)==null||e.resetForm()}},{text:"导出",perm:!0,type:"warning",svgIcon:h(G),hide:()=>m.activeName!=="list",click:()=>S()}]}]}),o=f({loading:!1,dataList:[],columns:[],operations:[],pagination:{refreshData:({page:e,size:t})=>{o.pagination.page=e,o.pagination.limit=t,o.dataList=y==null?void 0:y.slice((e-1)*t,e*t)}}}),S=()=>{var e;if(o.dataList.length>0){const t=((e=p.value)==null?void 0:e.queryParams)||{};console.log(t);const[a,n]=t[t.type]||[];let s=0,i=0;t.type==="day"?(s=a?l(a).valueOf():"",i=n?l(n).valueOf():""):(s=a?l(a).startOf(t.type).valueOf():"",i=n?l(n).endOf(t.type).valueOf():"");const d={attributes:t.attributeId.join(","),queryType:t.type==="month"?"day":t.type==="year"?"month":t.queryType,start:s,end:i};F(d).then(u=>{const r=window.URL.createObjectURL(u.data),c=document.createElement("a");c.style.display="none",c.href=r,c.setAttribute("download","数据对比表.xlsx"),document.body.appendChild(c),c.click()})}else b("无数据导出")},_=()=>{var d;o.loading=!0;const e=((d=p.value)==null?void 0:d.queryParams)||{};console.log(e);let t=0,a=0;const[n,s]=e[e.type]||[];e.type==="day"?(t=n?l(n).valueOf():"",a=s?l(s).valueOf():""):(t=n?l(n).startOf(e.type).valueOf():"",a=s?l(s).endOf(e.type).valueOf():"");const i={attributes:e.attributeId.join(","),queryType:e.type==="month"?"day":e.type==="year"?"month":e.queryType,start:t,end:a};M(i).then(u=>{var v;const r=(v=u.data)==null?void 0:v.data;m.data=r,y=r==null?void 0:r.tableDataList;const c=U(r==null?void 0:r.tableInfo);o.columns=c,o.dataList=y.slice(0*20,1*20),o.pagination.total=r==null?void 0:r.tableDataList.length,o.loading=!1})};return N(async()=>{}),(e,t)=>{const a=D,n=I;return E(),K("div",$,[q(a,{ref_key:"cardSearch",ref:p,config:x(L)},null,8,["config"]),q(n,{ref_key:"refCardTable",ref:w,class:"card-table",config:x(o)},null,8,["config"])])}}}),se=R(z,[["__scopeId","data-v-6e823689"]]);export{se as default};
