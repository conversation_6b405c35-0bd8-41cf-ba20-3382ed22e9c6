package org.thingsboard.server.dao.smartService.call;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.smartService.call.CallLogC;
import org.thingsboard.server.dao.sql.smartService.call.CallLogCMapper;

import java.util.Date;
import java.util.List;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-10-27
 */
@Slf4j
@Service
@Transactional
public class CallLogCServiceImpl implements CallLogCService {
    @Autowired
    private CallLogCMapper callLogCMapper;


    @Override
    public PageData getList(String phone, Long startTime, Long endTime, String status, int page, int size) {
        List<CallLogC> knowledgeNotices = callLogCMapper.getList(phone, startTime, endTime, status, page, size);

        int total = callLogCMapper.getListCount(phone, startTime, endTime, status);

        return new PageData(total, knowledgeNotices);
    }

    @Override
    public CallLogC process(CallLogC callLogC) {
        callLogC.setProcessTime(new Date());
        callLogCMapper.updateById(callLogC);

        return callLogC;
    }

    @Override
    public CallLogC getById(String id) {
        return callLogCMapper.selectById(id);
    }
}
