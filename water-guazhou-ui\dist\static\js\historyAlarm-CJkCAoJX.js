import{d as I,a0 as M,c as C,r as u,l as f,D as w,o as L,bo as V,i as c,g as S,h as k,F as h,q as _,an as j,br as A,C as F}from"./index-r0dFAfgr.js";import{_ as E}from"./TreeBox-DDD2iwoR.js";import{_ as P}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as H}from"./CardTable-rdWOL4_6.js";import{_ as W}from"./CardSearch-CB_HNR-Q.js";import{_ as B}from"./index-BJ-QPYom.js";import{h as O,l as q}from"./index-Bj5d3Vsu.js";import{a as z}from"./index-BggOjNGp.js";import J from"./infoTable-CW0u6ryD.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";const $=I({__name:"historyAlarm",setup(K){const b=M(),y=C(),D=C(),t=u({deviceName:new Map,alarmSetValue:new Map,alarmInfo:new Map,detailsInfo:{visible:!1,isStatistics:!0,row:{},close:()=>{t.detailsInfo.visible=!1}},pickerOptions:{disabledDate(a){return a.getTime()>Date.now()}},severityColor:{提示:"rgb(85,204,244)",次要:"rgb(255,216,0)",重要:"#f58717",紧急:"rgb(245,75,23)",严重:"#FF0000"}}),m=u({title:"区域划分",data:b.projectList,currentProject:b.selectedProject,isFilterTree:!0,treeNodeHandleClick:a=>{m.currentProject=a,b.SET_selectedProject(a),g()}}),T=u({filters:[{label:"搜索",field:"name",type:"input",labelWidth:60},{label:"设备",field:"deviceId",type:"select",multiple:!0,labelWidth:40,options:[]},{label:"日期",field:"daterange",type:"daterange",labelWidth:40},{label:"告警类型",field:"type",type:"select",options:[{value:"scope",label:"范围告警"},{value:"change",label:"变动告警"},{value:"offline",label:"掉线告警"}],formatter:(a,o,r)=>{var l,e;return((e=(l=r==null?void 0:r.options)==null?void 0:l.find(i=>i.value===a))==null?void 0:e.label)||a}},{label:"解除类型",field:"alarmTypes",type:"select",options:[{value:"CLEAR_FORCED",label:"强制解除"},{value:"CLEARED_ACK",label:"自动恢复"},{value:"offline",label:"掉线告警"}],formatter:(a,o)=>{var r,l;return((l=(r=o==null?void 0:o.options)==null?void 0:r.find(e=>e.value===a))==null?void 0:l.label)||a}}],operations:[{type:"btn-group",btns:[{perm:!0,icon:"iconfont icon-chaxun",text:"查询",click:()=>g()}]}],defaultParams:{daterange:[f().subtract(1,"months").format("YYYY-MM-DD"),f().format("YYYY-MM-DD")]}}),d=u({loading:!1,dataList:[],columns:[{prop:"name",label:"告警名称",width:200},{prop:"showDeviceN",label:"告警设备",width:200},{minWidth:100,prop:"alarmType",label:"告警类型"},{prop:"cycleName",label:"周期"},{prop:"createdTime",label:"告警时间",icon:"iconfont icon-shijian",iconStyle:{color:"#69e850",display:"inline-block","font-size":"16px"},width:160},{minWidth:100,prop:"severity",label:"告警级别",cellStyle:a=>({"border-radius":"14px",color:"#fff","line-height":"20px",height:"20px",padding:"4px 13px","font-size":"12px",backgroundColor:a.severityColor})},{prop:"alarmValue",label:"告警触发值",width:120},{prop:"recoverSet",label:"恢复触发值",width:120},{minWidth:100,prop:"recoverType",label:"解除类型"},{prop:"dismissal",label:"确认/解除人",width:150},{prop:"clearTime",label:"解除时间",icon:"iconfont icon-shijian",iconStyle:{color:"#69e850"},width:160},{prop:"clearRemarks",label:"操作备注",width:160,formatter:a=>a.details.clearRemarks}],operations:[{text:"详情",perm:!0,isTextBtn:!0,icon:"iconfont icon-xiangqing",click:a=>R(a)}],operationWidth:"80px",operationFixed:"right",pagination:{page:1,limit:20,total:0,refreshData:({page:a,size:o})=>{d.pagination.page=a,d.pagination.limit=o,g()}}}),Y=u({title:"详情",group:[]}),g=async()=>{var a,o;if(!m.currentProject.disabled){const r=await z(m.currentProject.id),l=(a=T.filters)==null?void 0:a.find(s=>s.field==="deviceId");l&&(l.options=r.data.map(s=>({label:s.name,value:w(s.id.id)}))),r.data.forEach(s=>{t.deviceName.set(s.id.id,s.name)});const e=await O();for(const s of e.data)t.alarmSetValue.set(w(s.id.id),s.details);const i=((o=D.value)==null?void 0:o.queryParams)||{},n={projectId:m.currentProject.id,page:d.pagination.page,size:d.pagination.limit,...i},[p,v]=n.daterange;n.start=f(p).valueOf(),n.end=f(v).add(1,"d").add(-1,"s").valueOf(),delete n.daterange;const N=await q(n);x(N.data)}},x=a=>{const o=a.data,r=[],l={day:"日",month:"月",year:"年"};o.forEach(e=>{const i=t.deviceName.get(e.originator.id)?t.deviceName.get(e.originator.id):"设备已删除";e.name=e.alarmJsonName?e.alarmJsonName:"掉线 - "+i,e.showDeviceN=i;const n=t.alarmSetValue.get(e.alarmJsonId);if(e.severityColor=t.severityColor[e.severity],n?(e.alarmValue=n.attributeName+": "+n.alarmSetValue,e.recoverSet=n.recoverSetValue):(e.alarmValue="此条设置已删除",e.recoverSet="此条设置已删除",e.type==="offline"&&(e.alarmValue="-",e.recoverSet="-")),e.alarmCycle?(e.cycleName=l[e.alarmCycle],e.recoverSet="-"):(e.cycleName="",e.cycle=null),e.alarmType="范围告警",e.alarmType=e.type==="change"?"变动告警":e.alarmType,e.alarmType=e.type==="offline"?"掉线告警":e.alarmType,e.clearTime=f(e.clearTs).format("YYYY-MM-DD HH:mm"),e.removeRemark=e.details.removeRemark,e.createdTime=f(e.createdTime).format("YYYY-MM-DD HH:mm"),e.status==="CLEAR_FORCED"?(e.recoverType="强制解除",e.clearRemarks=e.details.clearRemarks?e.details.clearRemarks:"",e.dismissal=e.details.dismissal?e.details.dismissal:""):(e.recoverType="自动恢复",e.clearRemarks=e.details.confirmRemarks?e.details.confirmRemarks:"",e.dismissal=e.details.confirm?e.details.confirm:""),e.recordList=[],e.details.record)for(const p of e.details.record){const v={time:f(parseInt(p.ts)).format("YYYY-MM-DD HH:mm"),infoValue:p.info,status:p.status.toUpperCase()==="ALARM"?"触发报警":"恢复"};e.recordList.push(v)}r.push(e)}),d.dataList=r,d.pagination.total=a.total},R=a=>{var o;(o=y.value)==null||o.openDialog(),t.detailsInfo.row=a,t.detailsInfo.visible=!0};return L(()=>{g()}),(a,o)=>{const r=B,l=W,e=H,i=P,n=E,p=A;return V((S(),k(n,null,{tree:h(()=>[_(r,{"tree-data":c(m)},null,8,["tree-data"])]),default:h(()=>[_(l,{ref_key:"refSearch",ref:D,config:c(T)},null,8,["config"]),_(e,{config:c(d),class:"card-table"},null,8,["config"]),_(i,{ref_key:"refDialogFormReport",ref:y,config:c(Y)},{default:h(()=>[c(t).detailsInfo.visible?(S(),k(J,{key:0,"dialog-info":c(t).detailsInfo,"device-name":c(t).deviceName},null,8,["dialog-info","device-name"])):j("",!0)]),_:1},8,["config"])]),_:1})),[[p,!!c(m).loading]])}}}),ne=F($,[["__scopeId","data-v-18394a98"]]);export{ne as default};
