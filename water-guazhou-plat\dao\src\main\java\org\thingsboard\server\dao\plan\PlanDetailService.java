package org.thingsboard.server.dao.plan;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.plan.PlanDetail;
import org.thingsboard.server.dao.util.imodel.query.plan.PlanDetailPageRequest;
import org.thingsboard.server.dao.util.imodel.query.plan.PlanDetailSaveRequest;

import java.util.List;

public interface PlanDetailService {
    /**
     * 分页条件查询盘点计划条目
     *
     * @param request 分页查询条件
     * @return 分页查询结果
     */
    IPage<PlanDetail> findAllConditional(PlanDetailPageRequest request);

    List<PlanDetail> saveAll(List<PlanDetailSaveRequest> entity);

    /**
     * 增量修改
     *
     * @param entity 实体信息
     * @return 是否修改成功
     */
    boolean update(PlanDetail entity);

    /**
     * 删除
     *
     * @param id 唯一标识
     * @return 是否删除成功
     */
    boolean delete(String id);

    /**
     * 批量删除
     *
     * @param idList id列表
     * @return 是否成功
     */
    boolean deleteAll(List<String> idList);

    /**
     * 通过父级id删除所有
     *
     * @param id     唯一标识
     * @param idList 实体的id列表，为空列表或null时删除所有
     * @return 是否成功
     */
    boolean deleteAllByMainId(String id, List<String> idList);

    /**
     * 通过父级id删除所有
     *
     * @param id     唯一标识
     * @param idList 实体的id列表，为空列表或null时删除所有
     * @return 是否成功
     */
    boolean removeAllByMainOnIdNotIn(String id, List<String> idList);

}
