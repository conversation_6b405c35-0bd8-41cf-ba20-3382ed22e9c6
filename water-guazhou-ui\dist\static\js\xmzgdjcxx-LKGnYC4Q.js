import{_ as f}from"./CardTable-rdWOL4_6.js";import{d as u,r,a8 as _,o as b,g,n as x,q as e,F as C,i as n,aB as y,bz as N,C as h}from"./index-r0dFAfgr.js";import{d as j}from"./index-CCFuhOrs.js";import{c as z}from"./manage-BReaEVJk.js";import"./index-C9hz-UZb.js";/* empty css                             */import"./DPlayer-Be2AurQX.js";import"./DPlayer.min-_HMH7IVX.js";const T=u({__name:"xmzgdjcxx",props:{config:{}},setup(i){const t=i,s=r({defaultValue:_(()=>t.config),border:!0,direction:"horizontal",column:2,title:"项目总归档基础信息",fields:[{type:"text",label:"总归档时间:",field:"archiveTimeName"},{type:"text",label:"总归档说明:",field:"remark"},{type:"text",label:"创建人:",field:"creatorName"},{type:"text",label:"创建时间:",field:"createTimeName"},{type:"text",label:"最后更新人:",field:"updateUserName"},{type:"text",label:"最后更新时间:",field:"updateTimeName"}]}),a=r({defaultExpandAll:!0,indexVisible:!0,columns:[{label:"工程编号",prop:"code"},{label:"工程名称",prop:"name"},{label:"工程地址",prop:"address"},{label:"工程类别",prop:"typeName"},{label:"工程预算(万元)",prop:"estimate"},{label:"申请单位",prop:"fitstpartName"}],dataList:[],pagination:{hide:!0}}),c=async()=>{const o={page:-1,size:20,projectCode:t.config.projectCode};z(o).then(l=>{a.dataList=l.data.data.data||[]})};return b(()=>{c()}),(o,l)=>{const p=j,d=N,m=f;return g(),x(y,null,[e(d,{class:"card"},{default:C(()=>[e(p,{config:n(s)},null,8,["config"])]),_:1}),e(m,{title:"项目工程",config:n(a),class:"card-table"},null,8,["config"])],64)}}}),w=h(T,[["__scopeId","data-v-34971dd9"]]);export{w as default};
