import { traverse } from '@/utils/GlobalHelper'

export function 饼图(data, name) {
  const option = {
    tooltip: {
      trigger: 'item'
    },
    legend: {
      top: 'center',
      orient: 'vertical',
      left: 'right'
    },
    series: [
      {
        name,
        type: 'pie',
        center: ['30%', '50%'],
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 5,
          borderWidth: 1
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: false
          }
        },
        labelLine: {
          show: false
        },
        data: traverse(data, 'children', { name: 'level', value: 'count' })
      }
    ]
  }

  return option
}

// 折线图
export function 折线图(data, name) {
  const date:any[] = []
  const values:any[] = []
  data.forEach((item:any) => {
    date.push(item.month)
    values.push(item.count)
  })
  const option = {
    tooltip: {
      trigger: 'axis'
    },

    calculable: true,
    xAxis: [
      {
        type: 'category',
        boundaryGap: false,
        data: date
      }
    ],
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name,
        type: 'line',
        data: values,
        lineStyle: {
          normal: {
            width: 4,
            color: {
              type: 'linear',

              colorStops: [
                {
                  offset: 0,
                  color: '#57BD9F' // 0% 处的颜色
                },
                {
                  offset: 1,
                  color: '#19BC8C' // 100% 处的颜色
                }
              ],
              globalCoord: false // 缺省为 false
            },
            shadowColor: 'rgba(72,216,191, 0.3)',
            shadowBlur: 6,
            shadowOffsetY: 10
          }
        },
        itemStyle: {
          normal: {
            color: '#57BD9F',
            borderWidth: 6,
            borderColor: '#57BD9F'
          }
        }
      }
    ]
  }

  return option
}
