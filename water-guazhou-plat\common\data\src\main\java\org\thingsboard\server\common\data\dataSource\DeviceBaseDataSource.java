package org.thingsboard.server.common.data.dataSource;

import lombok.*;

/**
 * <AUTHOR>
 * @date 2020/2/26 15:49
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DeviceBaseDataSource extends BaseDataSource {

    private String deviceId;

    private String property;


    public void setType() {
        super.setType(DataSourceType.DEVICE_SOURCE);
    }


    public DeviceBaseDataSource(BaseDataSource dataSource,String deviceId, String property){
        this.setId(dataSource.getId());
        this.setType(DataSourceType.DEVICE_SOURCE);
        this.setName(dataSource.getName());
        this.setEnable(dataSource.getEnable());
        this.setFormat(dataSource.getFormat());
        this.setUpdateTime(dataSource.getUpdateTime());
        this.setOrder(dataSource.getOrder());
        this.setDeviceId(deviceId);
        this.setProperty(property);
    }

}
