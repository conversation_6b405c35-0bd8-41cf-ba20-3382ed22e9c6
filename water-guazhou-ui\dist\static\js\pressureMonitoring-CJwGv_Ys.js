import{d as z,c as N,r as x,s as S,o as E,am as G,j as O,g as W,n as Y,q as F,i as y,t as H,p as J,_ as K,aq as Q,C as X}from"./index-r0dFAfgr.js";import{w as Z}from"./Point-WxyopZva.js";import{a as aa}from"./onemap-CEunQziB.js";import{P as U,C as ea}from"./index-CcDafpIP.js";import{r as j}from"./chart-wy3NEK2T.js";import{g as ta}from"./URLHelper-B9aplt5w.js";const sa={class:"onemap-panel-wrapper"},na={class:"table-box"},la=z({__name:"pressureMonitoring",props:{view:{},menu:{}},emits:["highlightMark","addMarks"],setup(q,{emit:A}){const I=A,C=q,c=N([{label:"0 个",value:"压力监测点总数"},{label:"0 %",value:"报警率"}]),w=N(),f=[{name:"offline",label:"离线"},{name:"alarm",label:"报警"},{name:"online",label:"正常"}],k=x({group:[{id:"chart",fieldset:{desc:"监测状态统计",type:"underline",style:{marginTop:0}},fields:[{type:"vchart",option:j(),style:{height:"150px"}}]},{id:"tab",fields:[{type:"input",field:"name",appendBtns:[{perm:!0,text:"刷新",click:()=>r(!0)}],onChange:()=>r()},{type:"tabs",field:"type",tabs:[{label:"全部",value:"all"},...f.map(e=>({...e,value:e.name}))],tabType:"simple",onChange:()=>r()}]}],labelPosition:"top",gutter:12,defaultValue:{type:"all"}}),i=x({indexVisible:!0,dataList:[],pagination:{hide:!0,refreshData:({page:e,size:n})=>{i.pagination.page=e,i.pagination.limit=n},layout:"total,sizes, jumper"},handleRowClick:e=>$(e),columns:[{width:200,label:"名称",prop:"name",sortable:!0},{width:80,label:"状态",prop:"status",formatter:e=>{var n;return((n=f.find(u=>u.name===e.status))==null?void 0:n.label)||e.status}},{width:160,label:"更新时间",prop:"lastTime",sortable:!0}]}),D=x({dataList:[],columns:[],pagination:{refreshData:({page:e,size:n})=>{D.pagination.page=e,D.pagination.limit=n,r()}}}),r=async e=>{var n,u,g,b,M,T,V,L,R;i.loading=!0;try{const h=(n=w.value)==null?void 0:n.dataForm.type,v=await aa({name:(u=w.value)==null?void 0:u.dataForm.name,status:h==="all"?"":h});i.dataList=((g=v.data)==null?void 0:g.data)||[];const B=k.group[0].fields[0],_=((M=(b=v.data)==null?void 0:b.data)==null?void 0:M.length)||0,P=[],p=[];if((V=(T=v.data)==null?void 0:T.data)!=null&&V.map(a=>{var d,m;const t=(d=a.location)==null?void 0:d.split(",");if((t==null?void 0:t.length)===2){const l=new Z({longitude:t[0],latitude:t[1],spatialReference:(m=C.view)==null?void 0:m.spatialReference});P.push({visible:!1,x:l.x,y:l.y,offsetY:-40,id:a.stationId,title:a.name,customComponent:S(U),customConfig:{info:{type:"attrs",imageUrl:a.imgs,stationId:a.stationId}},attributes:{path:C.menu.path,id:a.stationId,row:a},symbolConfig:{url:ta("测压点.png")}})}let s=p.find(l=>l.name===a.status);const{label:o}=f.find(l=>l.name===a.status)||{};s?s.value++:(s={name:a.status,nameAlias:o,value:1,scale:"0%"},p.push(s))}),p.map(a=>{var t,s,o;return a.scale=_===0?"0%":(Number(a.value)/_*100).toFixed(2)+"%",a.value=((o=(s=(t=v.data)==null?void 0:t.data)==null?void 0:s.filter(d=>d.status===a.name))==null?void 0:o.length)||0,a}),e){const a=(L=k.group.find(t=>t.id==="tab"))==null?void 0:L.fields[1];if(a){a.tabs=a.tabs.map(s=>{var m;const o=p.find(l=>l.name===s.value),d=((m=f.find(l=>l.name===s.value))==null?void 0:m.label)||"";return s.label=d+"("+((o==null?void 0:o.value)||0)+")",s});const t=a.tabs.find(s=>s.value==="all");t&&(t.label="全部("+_+")"),B&&(B.option=j(p,"个","",0))}c.value[0].label=_+"个",c.value[1].label=((R=p.find(t=>t.name==="alarm"))==null?void 0:R.scale)||"0 %"}I("addMarks",{windows:P,customWinComp:S(U)})}catch(h){console.dir(h)}i.loading=!1},$=async e=>{I("highlightMark",C.menu,e==null?void 0:e.stationId)};return E(()=>{r(!0)}),G(()=>O().isDark,()=>r(!0)),(e,n)=>{const u=K,g=Q;return W(),Y("div",sa,[F(y(ea),{modelValue:y(c),"onUpdate:modelValue":n[0]||(n[0]=b=>H(c)?c.value=b:null),span:12},null,8,["modelValue"]),F(u,{ref_key:"refForm",ref:w,config:y(k)},null,8,["config"]),J("div",na,[F(g,{config:y(i)},null,8,["config"])])])}}}),ca=X(la,[["__scopeId","data-v-8aba86ea"]]);export{ca as default};
