<!-- 云台 -->
<template>
  <div class="PTZ">
    <div class="PTZ_slider">
      <span class="demonstration">慢</span>
      <el-slider size="small" input-size="small" v-model="state.slider" />
      <span class="demonstration">快</span>
    </div>
    <div class="PTZ-btns">
      <template v-for="(items, index) in btns" :key="index">
        <div class="PTZ-btn">
          <template v-for="(item, index) in items" :key="index">
            <el-button
              bg
              :icon="item.icon"
              text
              @click="item.onclick && item.onclick(item)"
            >
            </el-button>
          </template>
        </div>
      </template>
    </div>
    <div class="PTZ_btns_bottoms">
      <template v-for="(items, index) in btns2" :key="index">
        <el-button-group class="ml-4">
          <template v-for="(item, i) in items" :key="i">
            <el-button
              type=""
              size="small"
              @click="item.onclick && item.onclick(item)"
            >
              <Icon :icon="item.icon" :size="20" />
            </el-button>
          </template>
        </el-button-group>
      </template>
    </div>
    <!-- <el-tabs type="border-card" class="PTZ_tabs">
            <el-tab-pane label="预置点">
                <el-select v-model="state.PresetPoint.value" placeholder="Select" style="width: 100%" size="small">
                    <el-option v-for="item in state.PresetPoint.options" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
                <el-button-group class="tabs_btns">
                    <el-button type="" size="small" :icon="Edit" />
                    <el-button type="" size="small" :icon="VideoPlay" />
                    <el-button type="" size="small" :icon="Delete" />
                </el-button-group>
            </el-tab-pane>
            <el-tab-pane label="巡航">
                <el-select v-model="state.PresetPoint.value" placeholder="Select" style="width: 100%" size="small">
                    <el-option v-for="item in state.PresetPoint.options" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
                <el-button-group class="tabs_btns">
                    <el-button type="" size="small" :icon="Edit" />
                    <el-button type="" size="small" :icon="VideoPlay" />
                    <el-button type="" size="small" :icon="Delete" />
                </el-button-group>
            </el-tab-pane>
            <el-tab-pane label="轨迹">
                <el-button-group class="tabs_btns">
                    <el-button type="" size="small">开始录制</el-button>
                    <el-button type="" size="small">开始播放</el-button>
                </el-button-group>
            </el-tab-pane>
        </el-tabs> -->
  </div>
</template>

<script lang="ts" setup>
import {
  TopLeft,
  ArrowUp,
  TopRight,
  Back,
  CircleCloseFilled,
  Right,
  BottomLeft,
  ArrowDown,
  BottomRight
} from '@element-plus/icons-vue';
import { Icon } from '@iconify/vue';
import { ctrlNewChangeZoom } from '@/api/video';
import { ElMessage } from 'element-plus';

const props = defineProps<{ config: any }>();

const state = reactive({
  slider: 50,
  PresetPoint: {
    value: '',
    options: [{ value: '', label: '' }]
  }
});

const btns = ref<
  { title?: string; onclick?: Function; icon: any; type?: string }[][]
>([
  [
    {
      title: '',
      onclick: (val) => {
        PTZControl('LEFT_UP');
      },
      icon: TopLeft
    },
    {
      title: '',
      onclick: (val) => {
        PTZControl('UP');
      },
      icon: ArrowUp
    },
    {
      title: '',
      onclick: (val) => {
        PTZControl('RIGHT_UP');
      },
      icon: TopRight
    }
  ],
  [
    {
      title: '',
      onclick: (val) => {
        PTZControl('LEFT');
      },
      icon: Back
    },
    {
      title: '',
      onclick: (val) => {},
      icon: CircleCloseFilled
    },
    {
      title: '',
      onclick: (val) => {
        PTZControl('RIGHT');
      },
      icon: Right
    }
  ],
  [
    {
      title: '',
      onclick: (val) => {
        PTZControl('LEFT_DOWN');
      },
      icon: BottomLeft
    },
    {
      title: '',
      onclick: (val) => {
        PTZControl('DOWN');
      },
      icon: ArrowDown
    },
    {
      title: '',
      onclick: (val) => {
        PTZControl('RIGHT_DOWN');
      },
      icon: BottomRight
    }
  ]
]);

const btns2 = ref<
  { title?: string; onclick?: Function; icon: string; type?: string }[][]
>([
  [
    {
      title: '',
      onclick: (val) => {
        PTZControl('ZOOM_OUT');
      },
      icon: 'gravity-ui:magnifier-minus'
    },
    {
      title: '',
      onclick: (val) => {
        PTZControl('ZOOM_IN');
      },
      icon: 'gravity-ui:magnifier-plus'
    }
  ],
  [
    {
      title: '',
      onclick: (val) => {
        PTZControl('FOCUS_NEAR');
      },
      icon: 'ph:copy-duotone'
    },
    {
      title: '',
      onclick: (val) => {
        PTZControl('FOCUS_FAR');
      },
      icon: 'ph:copy-fill'
    }
  ],
  [
    {
      title: '',
      onclick: (val) => {
        PTZControl('IRIS_ENLARGE');
      },
      icon: 'ri:camera-lens-line'
    },
    {
      title: '',
      onclick: (val) => {
        PTZControl('IRIS_REDUCE');
      },
      icon: 'ri:camera-lens-fill'
    }
  ]
]);

// 云台控制
const PTZControl = (key) => {
  if (!props.config.url) {
    ElMessage.error('请先选中正常播放窗口');
  }
  const params = {
    cameraIndexCode: props.config?.serialNumber,
    id: props.config.id,
    action: '0',
    command: key,
    speed: state.slider
  };

  ctrlNewChangeZoom(params).then((res) => {
    //
  });
  setTimeout(() => {
    ctrlNewChangeZoom({ ...params, action: '1' }).then((res) => {
      //
    });
  }, 500);
};
</script>

<style lang="scss" scoped>
.PTZ {
  margin-bottom: 15px;
}

.PTZ_slider {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 10px;

  .el-slider {
    width: 120px;
    margin: 0 15px;
  }

  span {
    font-size: 14px;
  }
}

.PTZ-btns {
  .el-button + .el-button {
    margin-left: 30px;
  }

  .PTZ-btn {
    width: 100%;
    display: flex;
    justify-content: center;
    margin-bottom: 30px;
  }
}

.PTZ_btns_bottoms {
  border-top: 1px solid #e1e1e1;
  display: flex;
  justify-content: space-between;
  padding: 0 20px;
  padding-top: 10px;
}

.PTZ_tabs {
  margin-top: 10px;
}

.tabs_btns {
  width: 100%;
  margin-top: 5px;
  display: flex;
  justify-content: space-between;

  .el-button {
    flex: 1;
    width: auto;
  }
}
</style>
