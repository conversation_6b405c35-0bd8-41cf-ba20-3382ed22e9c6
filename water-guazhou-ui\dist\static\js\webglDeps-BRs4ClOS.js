import{E as O,x as F,s as v,f as C}from"./FramebufferObject-8j9PRuxE.js";import{h as c}from"./ProgramTemplate-tdUBoAol.js";import{e as P,a as w}from"./ProgramTemplate-tdUBoAol.js";import{R as p}from"./index-r0dFAfgr.js";import{t as m}from"./NestedMap-DgiGbX8E.js";import{E as M}from"./Texture-BYqObwfn.js";import{hY as T}from"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./enums-BDQrMlcz.js";import"./widget-BcWKanF2.js";import"./pe-B8dP0-Ut.js";class E{constructor(e){this._rctx=e,this._store=new m}dispose(){this._store.forEach(e=>e.forEach(r=>r.dispose())),this._store.clear()}acquire(e,r,t,n){const o=this._store.get(e,r);if(p(o))return o.ref(),o;const s=new c(this._rctx,e,r,t,n);return s.ref(),this._store.set(e,r,s),s}get test(){let e=0;return this._store.forEach(r=>r.forEach(t=>e+=t.hasGLName?2:1)),{cachedWebGLObjects:e}}}function h(i){const{options:e,value:r}=i;return typeof e[r]=="number"}function g(i){let e="";for(const r in i){const t=i[r];if(typeof t=="boolean")t&&(e+=`#define ${r}
`);else if(typeof t=="number")e+=`#define ${r} ${t.toFixed()}
`;else if(typeof t=="object")if(h(t)){const{value:n,options:o,namespace:s}=t,f=s?`${s}_`:"";for(const a in o)e+=`#define ${f}${a} ${o[a].toFixed()}
`;e+=`#define ${r} ${f}${n}
`}else{const n=t.options;let o=0;for(const s in n)e+=`#define ${n[s]} ${(o++).toFixed()}
`;e+=`#define ${r} ${n[t.value]}
`}}return e}export{O as BufferObject,F as FramebufferObject,c as Program,E as ProgramCache,v as Renderbuffer,P as ShaderCompiler,M as Texture,C as VertexArrayObject,T as createContextOrErrorHTML,w as createProgram,g as glslifyDefineMap};
