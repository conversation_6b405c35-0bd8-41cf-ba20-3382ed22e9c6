<!-- 视频参数 -->
<template>
  <div class="Parameters">
    <div class="slider-demo-block">
      <span class="demonstration">亮度</span>
      <el-slider v-model="state.brightness" />
    </div>
    <div class="slider-demo-block">
      <span class="demonstration">色度</span>
      <el-slider v-model="state.chroma" />
    </div>
    <div class="slider-demo-block">
      <span class="demonstration">饱和度</span>
      <el-slider v-model="state.saturation" />
    </div>
    <div class="slider-demo-block">
      <span class="demonstration">对比度</span>
      <el-slider v-model="state.contrast" />
    </div>
  </div>
</template>

<script lang="ts" setup>
const state = reactive({
  // 亮度
  brightness: 50,
  // 色度
  chroma: 50,
  // 饱和度
  saturation: 50,
  // 对比度
  contrast: 50
});
</script>

<style lang="scss" scoped>
.el-slider {
  margin-left: 10px;
  width: calc(100% - 10px);
}
</style>
