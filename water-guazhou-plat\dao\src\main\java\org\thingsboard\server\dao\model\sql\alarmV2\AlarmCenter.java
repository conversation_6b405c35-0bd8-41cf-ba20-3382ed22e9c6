package org.thingsboard.server.dao.model.sql.alarmV2;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;
import java.util.Date;

/**
 * 报警Version2 报警中心
 */
@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.V2_TB_ALARM_CENTER_TABLE)
@TableName(ModelConstants.V2_TB_ALARM_CENTER_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class AlarmCenter {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.V2_TB_ALARM_CENTER_STATION_ID)
    private String stationId;

    @Column(name = ModelConstants.V2_TB_ALARM_CENTER_TITLE)
    private String title;

    @Column(name = ModelConstants.V2_TB_ALARM_CENTER_TIME)
    private Date time;

    @Column(name = ModelConstants.V2_TB_ALARM_CENTER_END_TIME)
    private Date endTime;

    @Column(name = ModelConstants.V2_TB_ALARM_CENTER_ALARM_TYPE)
    private String alarmType;

    @Column(name = ModelConstants.V2_TB_ALARM_CENTER_ALARM_LEVEL)
    private String alarmLevel;

    @Column(name = ModelConstants.V2_TB_ALARM_CENTER_ALARM_INFO)
    private String alarmInfo;

    @Column(name = ModelConstants.V2_TB_ALARM_CENTER_PROCESS_METHOD)
    private String processMethod;

    @Column(name = ModelConstants.V2_TB_ALARM_CENTER_ALARM_STATUS)
    private String alarmStatus;

    @Column(name = ModelConstants.V2_TB_ALARM_CENTER_PROCESS_STATUS)
    private String processStatus;

    @Column(name = ModelConstants.V2_TB_ALARM_CENTER_OPTION_USER)
    private String optionUser;

    @Column(name = ModelConstants.V2_TB_ALARM_CENTER_WORK_ORDER_ID)
    private String workOrderId;

    @Column(name = ModelConstants.V2_TB_ALARM_CENTER_ALARM_RULE_ID)
    private String alarmRuleId;

    @Column(name = ModelConstants.V2_TB_ALARM_CENTER_DEVICE_ID)
    private String deviceId;

    @Column(name = ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;

    @Transient
    @TableField(exist = false)
    private String stationName;

    @Transient
    @TableField(exist = false)
    private String stationType;

    @Transient
    @TableField(exist = false)
    private String stationAttrId;

    @Transient
    @TableField(exist = false)
    private String attrName;

    @Transient
    @TableField(exist = false)
    private String attr;

}
