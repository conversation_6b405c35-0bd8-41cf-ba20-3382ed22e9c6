package org.thingsboard.server.dao.sql.department;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.thingsboard.server.dao.model.sql.store.StoreInRecordDetail;
import org.thingsboard.server.dao.util.imodel.query.store.StoreInRecordDetailPageRequest;

import java.util.List;

@Mapper
public interface StoreInRecordDetailMapper extends BaseMapper<StoreInRecordDetail> {
    IPage<StoreInRecordDetail> findByPage(StoreInRecordDetailPageRequest request);

    boolean update(StoreInRecordDetail attr);

    boolean saveAll(List<StoreInRecordDetail> list);

    boolean updateAll(List<StoreInRecordDetail> list);

    int removeAllByMainId(String id);
}
