<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.groundwater.GroundwaterLevelMapper">

    <sql id="Base_Column_List">
        id, tenant_id, station_id, water_level, level_change, recharge_amount,
        rainfall_amount, record_time, create_time, update_time, remark, status, data_source
    </sql>

    <select id="findList" resultType="org.thingsboard.server.dao.model.sql.groundwater.GroundwaterLevel">
        SELECT
        gl.id,
        gl.tenant_id,
        gl.station_id,
        gl.water_level,
        gl.level_change,
        gl.recharge_amount,
        gl.rainfall_amount,
        gl.record_time,
        gl.create_time,
        gl.update_time,
        gl.remark,
        gl.status,
        gl.data_source,
        s.name AS station_name,
        s.address as stationLocation
        FROM
        tb_groundwater_level gl
        LEFT JOIN
        tb_station s ON gl.station_id = s.id
        <where>
            <if test="tenantId != null and tenantId != ''">
                AND gl.tenant_id = #{tenantId}
            </if>
            <if test="stationId != null and stationId != ''">
                AND gl.station_id = #{stationId}
            </if>
            <if test="stationName != null and stationName != ''">
                AND s.name LIKE CONCAT('%', #{stationName}, '%')
            </if>
            <if test="status != null">
                AND gl.status = #{status}
            </if>
            <if test="startTime != null">
                AND gl.record_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND gl.record_time &lt;= #{endTime}
            </if>
        </where>
        ORDER BY gl.record_time DESC
    </select>

    <select id="findListCount" resultType="java.lang.Integer">
        SELECT
        COUNT(1)
        FROM
        tb_groundwater_level gl
        <where>
            <if test="tenantId != null and tenantId != ''">
                AND gl.tenant_id = #{tenantId}
            </if>
            <if test="stationId != null and stationId != ''">
                AND gl.station_id = #{stationId}
            </if>
            <if test="stationName != null and stationName != ''">
                AND s.name LIKE CONCAT('%', #{stationName}, '%')
            </if>
            <if test="status != null">
                AND gl.status = #{status}
            </if>
            <if test="startTime != null">
                AND gl.record_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND gl.record_time &lt;= #{endTime}
            </if>
        </where>
    </select>

    <select id="findByStationIdAndTimeBetween"
            resultType="org.thingsboard.server.dao.model.sql.groundwater.GroundwaterLevel">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        tb_groundwater_level
        WHERE
        station_id = #{stationId}
        <if test="startTime != null">
            AND record_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND record_time &lt;= #{endTime}
        </if>
        ORDER BY
        record_time ASC
    </select>

    <select id="findDetailById" resultType="org.thingsboard.server.dao.model.sql.groundwater.GroundwaterLevel">
        SELECT
        gl.id,
        gl.tenant_id,
        gl.station_id,
        gl.water_level,
        gl.level_change,
        gl.recharge_amount,
        gl.rainfall_amount,
        gl.record_time,
        gl.create_time,
        gl.update_time,
        gl.remark,
        gl.status,
        gl.data_source,
        s.name AS station_name,
        s.address as stationLocation
        FROM
        tb_groundwater_level gl
        LEFT JOIN
        tb_station s ON gl.station_id = s.id
        WHERE
        gl.id = #{id}
    </select>

    <select id="getList" resultType="org.thingsboard.server.dao.model.sql.groundwater.GroundwaterLevel">
        SELECT
            gl.id,
            gl.tenant_id,
            gl.station_id,
            gl.water_level,
            gl.level_change,
            gl.recharge_amount,
            gl.rainfall_amount,
            gl.record_time,
            gl.create_time,
            gl.update_time,
            gl.remark,
            gl.status,
            gl.data_source,
            s.name AS station_name,
            s.address as stationLocation
        FROM
            tb_groundwater_level gl
        LEFT JOIN
            tb_station s ON gl.station_id = s.id
        <where>
            <if test="params.tenantId != null and params.tenantId != ''">
                AND gl.tenant_id = #{params.tenantId}
            </if>
            <if test="params.stationId != null and params.stationId != ''">
                AND gl.station_id = #{params.stationId}
            </if>
            <if test="params.stationName != null and params.stationName != ''">
                AND s.name LIKE CONCAT('%', #{params.stationName}, '%')
            </if>
            <if test="params.status != null">
                AND gl.status = #{params.status}
            </if>
            <if test="params.recordTime != null">
                AND Date(gl.record_time) = #{params.recordTime}
            </if>

        </where>
        <choose>
            <when test="params.sortField != null and params.sortField != '' and params.sortOrder != null and params.sortOrder != ''">
                <choose>
                    <!-- 处理可能的驼峰命名转换为下划线命名 -->
                    <when test="params.sortField == 'createTime' or params.sortField == 'createtime'">
                        ORDER BY gl.create_time ${params.sortOrder}
                    </when>
                    <when test="params.sortField == 'updateTime' or params.sortField == 'updatetime'">
                        ORDER BY gl.update_time ${params.sortOrder}
                    </when>
                    <when test="params.sortField == 'recordTime' or params.sortField == 'recordtime'">
                        ORDER BY gl.record_time ${params.sortOrder}
                    </when>
                    <when test="params.sortField == 'waterLevel' or params.sortField == 'waterlevel'">
                        ORDER BY gl.water_level ${params.sortOrder}
                    </when>
                    <when test="params.sortField == 'levelChange' or params.sortField == 'levelchange'">
                        ORDER BY gl.level_change ${params.sortOrder}
                    </when>
                    <when test="params.sortField == 'stationName' or params.sortField == 'stationname'">
                        ORDER BY s.name ${params.sortOrder}
                    </when>
                    <otherwise>
                        <!-- 如果是其他字段，尝试直接使用 -->
                        ORDER BY gl.${params.sortField} ${params.sortOrder}
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                ORDER BY gl.record_time DESC
            </otherwise>
        </choose>
    </select>

</mapper>