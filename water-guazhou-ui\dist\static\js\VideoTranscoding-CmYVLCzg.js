import{d as o,c as s,bu as a,g as n,n as c,p as r,i as t,C as d}from"./index-r0dFAfgr.js";const _={class:"video_transcoding"},i=["src"],f=o({__name:"VideoTranscoding",setup(l){const e=s("");return a(()=>{e.value=window.SITE_CONFIG.videoUploadUrl}),(p,u)=>(n(),c("div",_,[r("iframe",{src:t(e),frameborder:"0",class:"video_transcoding"},null,8,i)]))}}),v=d(f,[["__scopeId","data-v-4344c04f"]]);export{v as default};
