/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.common.data.constantsAttribute;

import lombok.Data;
import org.thingsboard.server.common.data.HasName;
import org.thingsboard.server.common.data.SearchTextBasedWithAdditionalInfo;
import org.thingsboard.server.common.data.id.ConstantsAttributeId;

@Data
public class ConstantsAttribute extends SearchTextBasedWithAdditionalInfo<ConstantsAttributeId> implements HasName {
    private ConstantsAttributeId constantsAttributeId;
    private String type;
    private String key;
    private String value;

    public ConstantsAttribute(ConstantsAttributeId constantsAttributeId) {
        super(constantsAttributeId);
    }

    public ConstantsAttribute() {
    }

    @Override
    public String getName() {
        return key;
    }

    @Override
    public String getSearchText() {
        return key;
    }
}
