package org.thingsboard.server.dao.sql.smartService.system;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.thingsboard.server.dao.model.sql.smartService.system.SystemWorkOrderType;

/**
 * userMybatis
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-08-27
 */
@Mapper
public interface SystemWorkOrderTypeMapper extends BaseMapper<SystemWorkOrderType> {

    @Select("select * from postgres.public.tb_service_system_work_order_type where name = #{name} order by create_time desc offset 0 limit 1")
    SystemWorkOrderType selectByName(@Param("name") String name);
}
