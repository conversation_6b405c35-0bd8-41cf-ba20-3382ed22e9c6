package org.thingsboard.server.dao.logicalFlow.BO;

import lombok.Builder;
import lombok.Data;
import org.thingsboard.server.dao.model.sql.LogicalFlow;
import org.thingsboard.server.dao.model.sql.LogicalFlowNode;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/7/24 16:51
 */
@Data
@Builder
public class LogicFlowGateway {

    private LogicalFlow logicalFlow;

    private List<LogicalFlowNode> logicalFlowNodes;
}
