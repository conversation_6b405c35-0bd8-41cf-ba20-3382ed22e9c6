package org.thingsboard.server.dao.model.sql.smartService.call;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 发信箱
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2021-11-10
 */
@TableName("tb_service_call_msg_send")
@Data
public class MsgSend {

    @TableId
    private String id;

    private String receivePerson;

    private String receivePhone;

    private String content;

    private String status;

    private String remark;

    private String creator;

    private transient String creatorName;

    private Date createTime;

    private Date updateTime;

    private String tenantId;

}
