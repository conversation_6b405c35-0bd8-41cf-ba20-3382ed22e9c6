package org.thingsboard.server.dao.sql.smartProduction.dispatch;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.thingsboard.server.dao.model.sql.smartProduction.dispatch.MessageTemplate;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch.MessageTemplatePageRequest;

@Mapper
public interface MessageTemplateMapper extends BaseMapper<MessageTemplate> {
    IPage<MessageTemplate> findByPage(MessageTemplatePageRequest request);

    boolean update(MessageTemplate entity);

}
