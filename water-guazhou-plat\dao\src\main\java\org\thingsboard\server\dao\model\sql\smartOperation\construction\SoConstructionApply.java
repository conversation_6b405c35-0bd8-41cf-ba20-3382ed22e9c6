package org.thingsboard.server.dao.model.sql.smartOperation.construction;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartOperation.SoGeneralTaskStatus;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseUsername;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.util.Date;


@Getter
@Setter
@ResponseEntity
public class SoConstructionApply implements SoConstructionRelatedEntity {
    // id
    private String id;

    // 编号
    private String code;

    // 所属工程编号
    private String constructionCode;

    // 所属合同编号
    private String contractCode;

    // 所属合同名称
    @TableField(exist = false)
    private String contractName;

    // 所属合同类型
    @TableField(exist = false)
    private String contractType;

    // 所属合同类型
    @TableField(exist = false)
    private String contractTypeName;

    // 工期开始时间
    private Date beginTime;

    // 工期结束时间
    private Date endTime;

    // 工程负责人
    private String principal;

    // 联系电话
    private String phone;

    // 施工班组
    private String constructClass;

    // 当前状态
    private SoGeneralTaskStatus status;

    // 备注
    private String remark;

    // 创建人
    @ParseUsername
    private String creator;

    // 创建时间
    private Date createTime;

    // 客户id
    private String tenantId;

}
