package org.thingsboard.server.dao.sql.role;

import org.springframework.data.jpa.repository.JpaRepository;
import org.thingsboard.server.dao.model.sql.RoleApplication;

import java.util.List;

public interface RoleApplicationRepository extends JpaRepository<RoleApplication, String> {
    List<RoleApplication> findByRoleId(String strRoleId);

    RoleApplication findByRoleIdAndTenantApplicationId(String roleId, String tenantApplicationId);
}
