package org.thingsboard.server.dao.sql.menu2;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.dao.model.sql.TenantMenusRole;

import java.util.List;

public interface TenantMenusRoleRepository extends JpaRepository<TenantMenusRole, String> {

    @Query("SELECT tmr.menuId FROM TenantMenusRole tmr " +
            "WHERE tmr.roleId = ?1 AND tmr.tenantApplicationId = ?2 AND tmr.tenantId = ?3")
    List<String> findByRoleIdAndTenantApplicationIdAndTenantId(String roleId, String tenantApplicationId, String tenantId);

    @Modifying
    @Transactional
    void deleteByRoleIdAndTenantApplicationId(String roleId, String tenantApplicationId);
}
