<!-- 供水单位 -->
<template>
  <div class="wrapper">
    <CardSearch
      ref="refSearch"
      :config="cardSearchConfig"
    />
    <CardTable
      :config="TableConfig"
      class="card-table"
    ></CardTable>
    <!-- 编辑供水单位 -->
    <DialogForm
      ref="refForm"
      :config="addOrUpdateConfig"
    ></DialogForm>
    <!-- 添加下级 -->
    <DialogForm
      ref="refForm1"
      :config="addLowerLevel"
    ></DialogForm>
  </div>
</template>

<script lang="ts" setup>
import { ICONS } from '@/common/constans/common'
import { ICardSearchIns, IDialogFormIns } from '@/components/type'
import useGlobal from '@/hooks/global/useGlobal'
import { SLConfirm, SLMessage } from '@/utils/Message'
import { getWaterSupplyUnit, postWaterSupplyUnit, getWaterSupplyTree, deleteWaterSupply, patchWaterSupplyUnit } from '@/api/company_org'

const { $btnPerms } = useGlobal()

const refSearch = ref<ICardSearchIns>()

const refForm = ref<IDialogFormIns>()

const refForm1 = ref<IDialogFormIns>()

const cardSearchConfig = ref<ISearch>({
  filters: [
    { label: '单位名称', field: 'name', type: 'input' },
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          icon: ICONS.QUERY,
          click: () => refreshData()
        },
        {
          perm: $btnPerms('RoleManageAdd'),
          text: '编辑供水单位',
          icon: ICONS.EDIT,
          click: () => clickCreatedRole()
        }
      ]
    }
  ]
})

const TableConfig = reactive<ICardTable>({
  defaultExpandAll: true,
  rowKey: 'id',
  columns: [
    { label: '单位ID', prop: 'id' },
    { label: '供水单位名称', prop: 'name' },
    { label: '单位类别', prop: 'type' },
    { label: '排序编号', prop: 'orderNum' },
    { label: '联系方式', prop: 'phone' },
    { label: '位置', prop: 'location' },
    { label: '风貌信息', prop: 'styleInformation' },
    {
      label: '水厂类型',
      prop: 'orgType',
      formatter: (row) => {
        const typeMap = {
          '0': '水源地',
          '1': '供水厂',
          '2': '污水厂',
          '3': '其他'
        };
        return typeMap[row.orgType] || row.orgType;
      }
    }
  ],
  operationWidth: '200px',
  operations: [
    {
      type: 'success',
      isTextBtn: true,
      color: '#4195f0',
      text: '增下级',
      perm: $btnPerms('RoleManageEdit'),
      icon: ICONS.ADD,
      click: row => clickaddLowerLevel(row)
    },
    {
      type: 'primary',
      isTextBtn: true,
      color: '#4195f0',
      text: '编辑',
      perm: $btnPerms('RoleManageEdit'),
      icon: 'iconfont icon-xiangqing',
      click: row => clickaddLowerLevel(row, true)
    },
    {
      isTextBtn: true,
      type: 'danger',
      text: '删除',
      icon: 'iconfont icon-shanchu',
      perm: $btnPerms('RoleManageDelete'),
      click: row => haneleDelete(row)
    }
  ],
  dataList: [],
  pagination: {
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page
      TableConfig.pagination.limit = size
      // refreshData()
    }
  }
})

// 编辑供水单位
const addOrUpdateConfig = reactive<IDialogFormConfig>({
  title: '修改',
  labelWidth: '130px',
  dialogWidth: '500px',
  submit: (params: any) => {
    patchWaterSupplyUnit(params.id, params).then(res => {
      if (res.data.code === 200) {
        refreshData()
        refForm1.value?.closeDialog()
        return
      }
      SLMessage.error(res.data.message)
    }).catch(error => {
      SLMessage.warning(error)
    })
  },

  defaultValue: {},
  group: [
    {
      fields: [
        {
          type: 'input',
          label: '单位名称',
          field: 'name',
          rules: [{ required: true, message: '请输入单位名称' }]
        },
        {
          type: 'select',
          label: '单位类别',
          field: 'type',
          options: [
            { label: '国家级集团公司', value: '国家级集团公司' },
            { label: '省级集团公司', value: '省级集团公司' },
            { label: '地市公司', value: '地市公司' },
            { label: '区县公司', value: '区县公司' },
            { label: '供水营业厅', value: '供水营业厅' },
            { label: '末级区域', value: '末级区域' }
          ],
          rules: [{ required: true, message: '请选择单位类别' }]
        },
        {
          type: 'select',
          label: '水厂类型',
          field: 'orgType',
          options: [
            { label: '水源地', value: '0' },
            { label: '供水厂', value: '1' },
            { label: '污水厂', value: '2' },
            { label: '其他', value: '3' }
          ]
        },
        {
          type: 'input',
          label: '位置',
          field: 'location'
        },
        {
          type: 'input',
          label: '风貌信息',
          field: 'styleInformation'
        }
        // {
        //   type: 'select',
        //   label: '公司属性',
        //   field: 'key3',
        //   rules: [{ required: true, message: '请输入公司属性' }]
        // },
        // {
        //   type: 'input',
        //   label: '联系方式',
        //   field: 'key4'
        // },
        // {
        //   type: 'select',
        //   label: '增值税银行',
        //   field: 'key5'
        // },
        // {
        //   type: 'input',
        //   label: '增值税名称',
        //   field: 'key6'
        // },
        // {
        //   type: 'input',
        //   label: '增值税税号',
        //   field: 'key7'
        // },
        // {
        //   type: 'input',
        //   label: '银行账号',
        //   field: 'key8'
        // }, {
        //   type: 'input',
        //   label: '注册地址',
        //   field: 'key9'
        // }

      ]
    }
  ]
})

// 添加下级
const addLowerLevel = reactive<IDialogFormConfig>({
  title: '供水单位信息',
  dialogWidth: '500px',
  submit: (params: any) => {
    if (params.id) {
      patchWaterSupplyUnit(params.id, params).then(res => {
        if (res.data.code === 200) {
          refreshData()
          refForm1.value?.closeDialog()
          return
        }
        SLMessage.error(res.data.message)
      }).catch(error => {
        SLMessage.warning(error)
      })
    } else {
      postWaterSupplyUnit(params).then(res => {
        if (res.data.code === 200) {
          refreshData()
          refForm1.value?.closeDialog()
          return
        }
        SLMessage.error(res.data.message)
      }).catch(error => {
        SLMessage.warning(error)
      })
    }
  },

  defaultValue: { },
  group: [
    {
      fields: [
        {
          type: 'input',
          label: '单位名称',
          field: 'name',
          rules: [{ required: true, message: '请输入单位名称' }]
        }, {
          type: 'select',
          label: '上级供水单位',
          field: 'parentId',
          options: [],
          readonly: true
        }, {
          type: 'select',
          label: '单位类别',
          field: 'type',
          options: [
            { label: '国家级集团公司', value: '国家级集团公司' },
            { label: '省级集团公司', value: '省级集团公司' },
            { label: '地市公司', value: '地市公司' },
            { label: '区县公司', value: '区县公司' },
            { label: '供水营业厅', value: '供水营业厅' },
            { label: '末级区域', value: '末级区域' }
          ],
          rules: [{ required: true, message: '请选择单位类别' }]
        }, {
          type: 'input-number',
          label: '联系电话',
          field: 'phone',
          rules: [{ validator: phone }]
        }, {
          type: 'number',
          label: '排序',
          field: 'orderNum',
          min: 0
        }, {
          type: 'select',
          label: '水厂类型',
          field: 'orgType',
          options: [
            { label: '水源地', value: '0' },
            { label: '供水厂', value: '1' },
            { label: '污水厂', value: '2' },
            { label: '其他', value: '3' }
          ]
        }, {
          type: 'input',
          label: '位置',
          field: 'location'
        }, {
          type: 'input',
          label: '风貌信息',
          field: 'styleInformation'
        }
      ]
    }
  ]
})

function phone(rule: any, value: any, callback: any) {
  const key = /^1\d{10}$/
  if (value === undefined || value === '') callback()
  else if (key.test(value)) {
    callback()
  } else {
    callback(new Error('电话号码格式错误'))
  }
}

const clickCreatedRole = () => {
  addOrUpdateConfig.defaultValue = { ...TableConfig.dataList[0] }
  refForm.value?.openDialog()
}

const clickaddLowerLevel = (row: { [x: string]: any }, state = false) => {
  if (state) {
    addLowerLevel.defaultValue = { ...(row) || {} };
    (addLowerLevel.group[0].fields[1] as any).options = [{ label: row.parentName || '' || '', value: row.parentId }]
  } else {
    addLowerLevel.defaultValue = { orderNum: '0', parentId: row.id || '' || {} };
    (addLowerLevel.group[0].fields[1] as any).options = [{ label: row.name || '', value: row.id }]
  }
  refForm1.value?.openDialog()
}

const haneleDelete = (row: { id: any }) => {
  SLConfirm('确定删除该供水单位, 是否继续?', '删除提示').then(() => {
    deleteWaterSupply(row.id)
      .then(res => {
        if (res.data.code !== 200) {
          SLMessage.warning(res.data.err)
          return
        }
        refreshData()
        SLMessage.success('删除成功')
      })
      .catch(err => {
        SLMessage.error(err.data.message)
      })
  })
}

const refreshData = async () => {
  const depth = 1
  let res: any
  if (refSearch.value?.queryParams?.name) {
    res = await getWaterSupplyUnit({ name: refSearch.value?.queryParams?.name || '' })
  } else {
    res = await getWaterSupplyTree(depth)
  }
  console.log(res.data)
  TableConfig.dataList = res.data.data || []
  TableConfig.pagination.total = res.data.total || 0
}

onMounted(() => {
  refreshData()
})
</script>

<style lang="scss">
.el-table__placeholder {
  display: none;
}
</style>
