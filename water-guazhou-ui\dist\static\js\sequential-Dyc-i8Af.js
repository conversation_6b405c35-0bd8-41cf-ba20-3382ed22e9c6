import{d as w,M as q,a6 as S,c as i,r as g,am as B,bF as u,s as V,o as A,ay as E,bo as M,i as l,g as N,n as P,q as y,p as R,j,bB as z,br as L,al as T,C as F}from"./index-r0dFAfgr.js";import{_ as G}from"./Search-NSrhrIa_.js";import{l as W}from"./echart-DxEZmJvB.js";import{g as X}from"./flowMonitoring-DtJlPj0G.js";const $={class:""},H=w({__name:"sequential",props:{stationName:{},stationId:{}},setup(k){const{$messageWarning:x}=q(),b=S(),d=i(!1),m=i(),r=k,h=i(),_=i(),v=g({chartOption:null});B(()=>r.stationId,()=>{console.log(r.stationId),p()});const C=g({defaultParams:{date:u().format()},filters:[{type:"month",label:"日期",field:"date",width:"300px"},{type:"btn-group",btns:[{perm:!0,text:"查询",svgIcon:V(T),click:()=>{r.stationId?p():x("请选择监测点")}}]}]}),I=a=>{var n,s,c;const e=W();e.series=[];let t=[];for(const o in a){const O=(n=a[o])==null?void 0:n.map(f=>f.value);t=t.length===0?(s=a[o])==null?void 0:s.map(f=>f.ts):t;const D={name:o,smooth:!0,data:O,type:"line",markPoint:{data:[{type:"max",name:"最大值"},{type:"min",name:"最小值"}]},markLine:{data:[{type:"average",name:"平均值"}]}};e.series.push(D),e.yAxis[0].name="流量(m³)",e.xAxis.data=t}(c=m.value)==null||c.clear(),z(()=>{b.listenTo(_.value,()=>{var o;v.chartOption=e,(o=m.value)==null||o.resize()})})},p=async()=>{var s,c;d.value=!0;const a=((s=h.value)==null?void 0:s.queryParams)||{},e={start:u(a.date).startOf("month").valueOf(),end:u(a.date).endOf("month").valueOf(),stationId:r.stationId,type:"2"},n=(c=(await X(e)).data)==null?void 0:c.data;I(n),d.value=!1};return A(()=>{r.stationId&&p()}),(a,e)=>{const t=G,n=E("VChart"),s=L;return M((N(),P("div",$,[y(t,{ref_key:"cardSearch",ref:h,config:l(C)},null,8,["config"]),R("div",{ref_key:"agriEcoDev",ref:_,class:"card-ehcarts"},[y(n,{ref_key:"refChart",ref:m,theme:l(j)().isDark?"dark":"",class:"card-ehcarts",option:l(v).chartOption},null,8,["theme","option"])],512)])),[[s,l(d)]])}}}),Y=F(H,[["__scopeId","data-v-bcfede92"]]);export{Y as default};
