package org.thingsboard.server.controller.smartPipe;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.request.PartitionMountRequest;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.PartitionFireHydrant;
import org.thingsboard.server.dao.smartPipe.PartitionFireHydrantService;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.List;

/**
 * 智慧管网-消火栓信息
 */
@RestController
@RequestMapping("api/spp/dma/partition/fireHydrant")
public class PartitionFireHydrantController extends BaseController {

    @Autowired
    private PartitionFireHydrantService partitionFireHydrantService;

    @PostMapping
    public IstarResponse save(@RequestBody PartitionFireHydrant partitionFireHydrant) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        partitionFireHydrant.setTenantId(tenantId);

        return IstarResponse.ok(partitionFireHydrantService.save(partitionFireHydrant));
    }

    @GetMapping("list")
    public IstarResponse getList(PartitionMountRequest request) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        request.setTenantId(tenantId);
        return IstarResponse.ok(partitionFireHydrantService.getList(request));
    }

    @DeleteMapping
    public IstarResponse delete(@RequestBody List<String> ids) {
        partitionFireHydrantService.delete(ids);
        return IstarResponse.ok("删除成功");
    }
}
