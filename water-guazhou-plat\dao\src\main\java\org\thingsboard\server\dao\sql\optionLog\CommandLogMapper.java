package org.thingsboard.server.dao.sql.optionLog;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.DTO.CommandLogDTO;
import org.thingsboard.server.dao.model.request.CommandListRequest;
import org.thingsboard.server.dao.model.sql.CommandLog;

@Mapper
public interface CommandLogMapper extends BaseMapper<CommandLog> {
    IPage<CommandLogDTO> findList(IPage<CommandLogDTO> page, @Param("param") CommandListRequest request);
}
