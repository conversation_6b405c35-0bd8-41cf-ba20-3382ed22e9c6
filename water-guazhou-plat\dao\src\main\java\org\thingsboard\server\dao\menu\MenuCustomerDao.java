/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.menu;

import com.google.common.util.concurrent.ListenableFuture;
import org.thingsboard.server.common.data.id.MenuCustomerId;
import org.thingsboard.server.common.data.id.MenuTenantId;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.menu.MenuCustomer;
import org.thingsboard.server.common.data.menu.MenuTenant;
import org.thingsboard.server.common.data.menu.MenuTypeVO;
import org.thingsboard.server.dao.Dao;

import java.util.List;
import java.util.UUID;

public interface MenuCustomerDao extends Dao<MenuCustomer> {
    @Override
    List<MenuCustomer> find();

    @Override
    MenuCustomer findById(UUID id);

//    @Override
//    ListenableFuture<MenuCustomer> findByIdAsync(UUID id);

    @Override
    MenuCustomer save(MenuCustomer menuCustomer);

    @Override
    boolean removeById(UUID id);

    void deleteByTenantId(TenantId tenantId);

    List<MenuCustomer> findByTenantId(TenantId tenantId, MenuCustomerId root);

    List<MenuCustomer> findByTenantId(TenantId tenantId);


    List<Integer> getTypesByTenantId(TenantId tenantId);

    List<MenuTypeVO> getTypes(TenantId tenantId);

    List<MenuCustomer> findByMenuTenantIdIn(List<String> menuIdList);

    Integer getTypeByMenuTenantId(MenuTenantId menuTenantId);

    MenuCustomer findByMenuTenantId(MenuTenantId menuTenantId, TenantId tenantId);

    void deleteByParentId(MenuCustomerId id);

    List<MenuCustomer> findByMenuTenantIdInAndTenantId(List<String> menuTenantIdList, TenantId tenantId);
}
