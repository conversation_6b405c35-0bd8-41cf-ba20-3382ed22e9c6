import AnimatedLinesLayer from '@/utils/arcgis/Layers/AnimatedLinesLayer';
import * as query from '@arcgis/core/rest/query';
import * as geometryEngine from '@arcgis/core/geometry/geometryEngine';

export const useAnimatedLinesLayer = () => {
  const aminatedLinesLayer = new AnimatedLinesLayer({
    id: 'animatedlineslayer',
    title: '管网流动光线'
  });
  const addTo = (view: __esri.MapView) => {
    view?.map.add(aminatedLinesLayer);
    if (!window.SITE_CONFIG.GIS_CONFIG.gisDissolvedService) {
      console.log('未配置流动光线服务地址');
    } else {
      query
        .executeQueryJSON(
          window.SITE_CONFIG.GIS_CONFIG.gisService +
            '/' +
            window.SITE_CONFIG.GIS_CONFIG.gisDissolvedService +
            '/0',
          {
            where: '1=1',
            geometry: view?.extent,
            returnGeometry: true,
            outFields: ['OBJECTID', 'SHAPE.LEN']
          }
        )
        .then((res: any) => {
          const graphics = res.features
            .filter((item) => item.attributes['SHAPE.LEN'] >= 150)
            .map((trip: __esri.Graphic) => {
              const geometry = trip.geometry as __esri.Polyline;
              geometry.paths = geometry.paths.filter((path) => {
                // 过滤小于150长度的管线
                const length = geometryEngine.geodesicLength(
                  {
                    paths: [path],
                    spatialReference: view?.spatialReference
                  } as __esri.Polyline,
                  'meters'
                );
                return length > 150;
              });
              // trip.attributes.color = [238, 161, 7]
              trip.attributes.color = [11, 255, 140];
              return {
                attributes: trip.attributes,
                geometry: geometry,
                spatialReference: view?.spatialReference
              };
            });
          aminatedLinesLayer.removeAll();
          aminatedLinesLayer.addMany(graphics);
        });
    }
  };
  return {
    addTo
  };
};
