package org.thingsboard.server.dao.util.imodel.query.store;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.store.RestDeviceStorageInfo;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;

@Getter
@Setter
public class RestDeviceInfoPageRequest extends AdvancedPageableQueryEntity<RestDeviceStorageInfo, RestDeviceInfoPageRequest> {
    // 设备编码
    private String serialId;

    // 设备名称
    private String name;

    // 规格型号
    private String model;

    // 设备类型
    private String deviceTypeId;
}
