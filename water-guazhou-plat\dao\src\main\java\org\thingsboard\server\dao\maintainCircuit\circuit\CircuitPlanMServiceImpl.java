package org.thingsboard.server.dao.maintainCircuit.circuit;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.common.data.utils.DateUtils;
import org.thingsboard.server.dao.model.sql.UserEntity;
import org.thingsboard.server.dao.model.sql.maintainCircuit.circuit.*;
import org.thingsboard.server.dao.sql.deviceType.DeviceTypeMapper;
import org.thingsboard.server.dao.sql.maintainCircuit.circuit.*;
import org.thingsboard.server.dao.sql.user.UserMapper;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-08-24
 */
@Slf4j
@Service
@Transactional
public class CircuitPlanMServiceImpl implements CircuitPlanMService {

    @Autowired
    private CircuitPlanMMapper circuitPlanMMapper;

    @Autowired
    private CircuitPlanCMapper circuitPlanCMapper;

    @Autowired
    private DeviceTypeMapper deviceTypeMapper;

    @Autowired
    private CircuitPointMService circuitPointMService;

    @Autowired
    private CircuitTaskMMapper circuitTaskMMapper;

    @Autowired
    private CircuitTaskCMapper circuitTaskCMapper;

    @Autowired
    private CircuitTaskPointMapper circuitTaskPointMapper;

    @Autowired
    private UserMapper userMapper;

    @Override
    public PageData getList(String planName, String teamName, String userName, Date startStartTime, Date startEndTime, Date endStartTime, Date endEndTime, int page, int size, String tenantId) {

        List<CircuitPlanM> circuitPlanMList = circuitPlanMMapper.getList(planName, teamName, userName, startStartTime, startEndTime, endStartTime, endEndTime, page, size, tenantId);

        int total = circuitPlanMMapper.getListCount(planName, teamName, userName, startStartTime, startEndTime, endStartTime, endEndTime, tenantId);

        return new PageData(total, circuitPlanMList);
    }


    @Override
    public CircuitPlanM getDetail(String mainId) {
        CircuitPlanM circuitPlanM = circuitPlanMMapper.getById(mainId);
        List<CircuitPlanC> circuitPlanCList = circuitPlanCMapper.getList(circuitPlanM.getId());
        // 获取巡检点信息
        for (CircuitPlanC circuitPlanC : circuitPlanCList) {
            CircuitPointM circuitPointM = circuitPointMService.getById(circuitPlanC.getPointId());
            circuitPlanC.setCircuitPointM(circuitPointM);
        }

        circuitPlanM.setCircuitPlanCList(circuitPlanCList);

        return circuitPlanM;
    }

    @Override
    public CircuitPlanM save(CircuitPlanM circuitPlanM) {

        try {
            if (circuitPlanM.getEndTime() == null) {
                int executeDays = circuitPlanM.getExecutionDays();
                int intervalDays = circuitPlanM.getIntervalDays();
                int executionNum = circuitPlanM.getExecutionNum();
                long intervalTime = ((executeDays + intervalDays) * executionNum) * (24 * 3600 * 1000l);
                circuitPlanM.setEndTime(new Date(circuitPlanM.getStartTime().getTime() + intervalTime));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (StringUtils.isBlank(circuitPlanM.getId())) {
            circuitPlanM.setStatus("0");
            circuitPlanM.setCreateTime(new Date());

            circuitPlanMMapper.insert(circuitPlanM);
        } else {
            circuitPlanMMapper.updateById(circuitPlanM);
        }

        Map deleteMap = new HashMap<>();
        deleteMap.put("main_id", circuitPlanM.getId());
        circuitPlanCMapper.deleteByMap(deleteMap);

        if (circuitPlanM.getCircuitPlanCList() != null) {
            for (CircuitPlanC circuitPlanC : circuitPlanM.getCircuitPlanCList()) {
                circuitPlanC.setMainId(circuitPlanM.getId());
                circuitPlanC.setTenantId(circuitPlanM.getTenantId());
                circuitPlanC.setCreateTime(new Date());

                circuitPlanCMapper.insert(circuitPlanC);
            }
        }
        // 保存是生成任务，后续需要审核删除该步骤
        try {
            this.execute(circuitPlanM);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return circuitPlanM;
    }

    @Override
    public IstarResponse delete(List<String> ids) {
        circuitPlanMMapper.deleteBatchIds(ids);

        // 删除子表
        QueryWrapper<CircuitPlanC> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("main_id", ids);
        circuitPlanCMapper.delete(queryWrapper);

        return IstarResponse.ok("删除成功");
    }

    @Override
    public void reviewer(CircuitPlanM circuitPlanM) {
        CircuitPlanM circuitPlanM1 = new CircuitPlanM();
        circuitPlanM1.setId(circuitPlanM.getId());
        circuitPlanM1.setStatus(circuitPlanM.getStatus());
        circuitPlanM1.setReviewer(circuitPlanM.getReviewer());

        circuitPlanMMapper.updateById(circuitPlanM1);

        // 通过则生成任务
        if ("2".equals(circuitPlanM.getStatus())) {
            try {
                circuitPlanM = circuitPlanMMapper.selectById(circuitPlanM.getId());
                this.execute(circuitPlanM);
            } catch (ParseException e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public List<CircuitPlanM> findAll() {
        return circuitPlanMMapper.selectByMap(new HashMap<>());
    }

    @Override
    public void execute(CircuitPlanM circuitPlanM) throws ParseException {


        Integer periodTime = circuitPlanM.getIntervalDays(); // 间隔天数
        Integer executionDays = circuitPlanM.getExecutionDays(); // 执行天数
        Integer executionNum = circuitPlanM.getExecutionNum(); // 执行次数
        Date executeTime = circuitPlanM.getStartTime(); // 开始时间

        Date nextExecuteTime = null;

        if (executeTime == null) {
            return;
        }

        String type = "";

        if (periodTime == null || periodTime == 0) {
            nextExecuteTime = executeTime;
            type = "临时任务";
        } else {
            type = "计划任务";
            // 是否超过执行时间
            // if (new Date().getTime() > (executeTime.getTime() + (executionNum * periodTime * 4 * 60 * 60 * 1000l))) {
            //     return;
            // }
            boolean result = false;
            for (int i = 0; i < executionNum; i++) {
                nextExecuteTime = new Date(executeTime.getTime() + (periodTime * i * 24 * 60 * 60 * 1000));
                result = checkTimeIsToday(nextExecuteTime);
                if (result) {// 不在今天
                    break;
                }
            }
            if (!result) {// 不在今天
                return;
            }
        }

        // 查询保养设置子表内容
        List<CircuitPlanC> circuitPlanCList = circuitPlanCMapper.getList(circuitPlanM.getId());
        circuitPlanM.setStartTime(nextExecuteTime);
        if (executionDays != null) {
            circuitPlanM.setEndTime(new Date(nextExecuteTime.getTime() + (executionDays * 24 * 60 * 60 * 1000)));
        } else {
            if (circuitPlanM.getEndTime() != null) {
                circuitPlanM.setExecutionDays(Long.valueOf((circuitPlanM.getEndTime().getTime() - circuitPlanM.getStartTime().getTime()) / (24 * 60 * 60 * 1000)).intValue());
            }
        }

        // 生成保养任务
        CircuitTaskM circuitTaskM = saveCircuit(circuitPlanM, circuitPlanCList, type);
    }

    private CircuitTaskM saveCircuit(CircuitPlanM circuitPlanM, List<CircuitPlanC> circuitPlanCList, String type) {
        // 保养任务主表
        CircuitTaskM circuitTaskM = new CircuitTaskM();
        BeanUtils.copyProperties(circuitPlanM, circuitTaskM);
        circuitTaskM.setId(null);
        // 新建
        circuitTaskM.setCode("XJ" + new SimpleDateFormat("yyyyMMddHHmmssSSS").format(new Date()));
        circuitTaskM.setStatus("0");
        circuitTaskM.setType(type);
        circuitTaskM.setAuditStatus("0");
        circuitTaskM.setAuditor(circuitPlanM.getReviewer());
        circuitTaskM.setCreateTime(new Date());
        circuitTaskM.setTenantId(circuitPlanM.getTenantId());
        circuitTaskM.setStartTime(circuitPlanM.getStartTime());
        circuitTaskM.setEndTime(circuitPlanM.getEndTime());

        circuitTaskMMapper.insert(circuitTaskM);
        // 巡检任务子表
        Map<String, CircuitPointC> circuitTaskCMap = new HashMap<>();
        if (circuitPlanCList != null && !circuitPlanCList.isEmpty()) {
            for (CircuitPlanC circuitPlanC : circuitPlanCList) {
                // 巡检任务巡检点
                CircuitTaskPoint circuitTaskPoint = new CircuitTaskPoint();
                circuitTaskPoint.setMainId(circuitTaskM.getId());
                circuitTaskPoint.setPointId(circuitPlanC.getPointId());
                circuitTaskPoint.setTenantId(circuitTaskM.getTenantId());
                circuitTaskPoint.setCreateTime(new Date());
                circuitTaskPointMapper.insert(circuitTaskPoint);

                // 获取设备
                CircuitPointM circuitPointM = circuitPointMService.getById(circuitPlanC.getPointId());
                if (circuitPointM != null) {
                    for (CircuitPointC circuitPointC : circuitPointM.getCircuitPointCList()) {
                        circuitTaskCMap.put(circuitPointC.getDeviceLabelCode(), circuitPointC);
                    }
                }
            }

            List<CircuitPointC> circuitPointCS = circuitTaskCMap.values().stream().collect(Collectors.toList());
            for (CircuitPointC circuitPointC : circuitPointCS) {
                CircuitTaskC circuitTaskC = new CircuitTaskC();
                circuitTaskC.setId(null);
                circuitTaskC.setDeviceLabelCode(circuitPointC.getDeviceLabelCode());
                circuitTaskC.setMainId(circuitTaskM.getId());
                circuitTaskC.setStatus("0");
                circuitTaskC.setTenantId(circuitPointC.getTenantId());
                circuitTaskC.setCreateTime(new Date());
                circuitTaskCMapper.insert(circuitTaskC);
            }
        }

        return circuitTaskM;
    }


    private boolean checkTimeIsToday(Date nextExecuteTime) throws ParseException {
        String string = DateUtils.date2Str(new Date(), DateUtils.DATE_FORMATE_DAY);
        SimpleDateFormat dateFormat = new SimpleDateFormat(DateUtils.DATE_FORMATE_DEFAULT);
//        Date todayStart = dateFormat.parse(string + " 00:00:00");
        Date todayEnd = dateFormat.parse(string + " 23:59:59");

        if (/*nextExecuteTime.getTime() > todayStart.getTime() && */nextExecuteTime.getTime() < todayEnd.getTime()) {
            return true;
        }

        return false;
    }


    @Override
    public PageData<Map> getMaintainList(String deviceLabelCode, int page, int size) {
        List<Map> maintainList = circuitPlanCMapper.getCircuitList(deviceLabelCode, page, size);

        for (Map map : maintainList) {
            // 任务人
            String userIds = (String) map.get("userId");
            String userName = "";
            if (StringUtils.isNotBlank(userIds)) {
                String[] userIdStr = userIds.split(",");
                UserEntity userEntity;
                for (String userId : userIdStr) {
                    userEntity = userMapper.selectById(userId);
                    if (userEntity != null) {
                        userName = userName + "," + userEntity.getFirstName();
                    }
                }
                if (userName.contains(",")) {
                    userName = userName.substring(1);
                }
            }
            map.put("userName", userName);

            // 下一次巡检时间
            Date startTime = (Date) map.get("startTime");
            Integer intervalDays = (Integer) map.get("intervalDays");
            Integer executionNum = (Integer) map.get("executionNum");
            Date nextTime;
            map.put("nextTime", "-");
            if (executionNum != null && intervalDays != null) {
                for (int i = 0; i < executionNum; i++) {
                    nextTime = new Date(startTime.getTime() + (intervalDays * i * 24 * 60 * 60 * 1000));
                    if (nextTime.getTime() > System.currentTimeMillis()) {
                        map.put("nextTime", nextTime);
                        break;
                    }
                }
            }

        }
        int total = circuitPlanCMapper.getCircuitListCount(deviceLabelCode);

        return new PageData<>(total, maintainList);
    }
}
