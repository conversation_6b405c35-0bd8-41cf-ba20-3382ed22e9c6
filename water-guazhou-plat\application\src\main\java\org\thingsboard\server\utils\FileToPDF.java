//package org.thingsboard.server.utils;
//
//import com.itextpdf.text.Document;
//import com.itextpdf.text.Image;
//import com.itextpdf.text.PageSize;
//import com.itextpdf.text.pdf.PdfWriter;
//
//import java.io.File;
//import java.io.FileOutputStream;
//
///**
// * 文件转pdf
// *
// * @<NAME_EMAIL>
// * @since v1.0.0 2021-02-07
// */
//public class FileToPDF {
//    public static File fileTOPDF(File file) {
//        String originFileName = file.getAbsolutePath();
//        String targetFileName = originFileName.substring(0, originFileName.lastIndexOf(".")) + ".pdf";
//        // 是pdf文件直接返回
//        if (originFileName.endsWith("pdf")) {
//            return file;
//        }
//        File pdfFile = new File(targetFileName);
//        Document document = new Document(PageSize.A4);
//        try {
//            PdfWriter.getInstance(document, new FileOutputStream(pdfFile));
//            document.open();
//            if (originFileName.endsWith("jpg") || originFileName.endsWith("jpeg") || originFileName.endsWith("png")) {
//                document.add(Image.getInstance(originFileName));
//            }
//            if (originFileName.endsWith("docx") || originFileName.endsWith("doc")) {
//                WordToPDF.WordToPdf(originFileName, targetFileName);
//                return new File(targetFileName);
//            }
//            // todo excel转pdf
//            if (originFileName.endsWith("xlsx") || originFileName.endsWith("xls")) {
//                ExcelToPDF.excel2pdf(originFileName, targetFileName);
//                return new File(targetFileName);
//            }
//
//            document.close();
//
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return pdfFile;
//    }
//
//}
