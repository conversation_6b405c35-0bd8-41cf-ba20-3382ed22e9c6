package org.thingsboard.server.dao.alarmV2;

import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.DTO.AlarmRuleDTO;
import org.thingsboard.server.dao.model.request.AlarmRuleListRequest;
import org.thingsboard.server.dao.model.sql.alarmV2.AlarmRule;

import java.util.List;

public interface AlarmRuleService {
    PageData<AlarmRuleDTO> findList(AlarmRuleListRequest request, TenantId tenantId);

    void save(AlarmRuleDTO entity, TenantId tenantId) throws ThingsboardException;

    void remove(List<String> ids);

    List<AlarmRule> findByStationAttrList(List<String> stationAttrList);
}
