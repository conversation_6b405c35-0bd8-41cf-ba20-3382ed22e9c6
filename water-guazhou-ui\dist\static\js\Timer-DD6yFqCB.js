import{d as _,c as d,bu as p,l as n,g as t,n as a,aB as r,aJ as m,p as f,bh as c,an as g,i as b,C as k}from"./index-r0dFAfgr.js";import{p as s}from"./padStart-BKfyZZDO.js";const y={class:"timer"},h={class:"value"},D={key:0,class:"unit"},w=_({__name:"Timer",setup(B){const o=d([]),i=()=>["星期日","星期一","星期二","星期三","星期四","星期五","星期六"][new Date().getDay()];return p(()=>{o.value=[{label:s(n().get("year").toString(),4,"0"),unit:"年"},{label:s((n().get("M")+1).toString(),2,"0"),unit:"月"},{label:s(n().get("D").toString(),2,"0"),unit:"日"},{label:i(),unit:""}]}),(l,u)=>(t(),a("div",y,[(t(!0),a(r,null,m(b(o),e=>(t(),a(r,{key:e},[f("span",h,c(e.label),1),e.unit?(t(),a("span",D,c(e.unit),1)):g("",!0)],64))),128))]))}}),x=k(w,[["__scopeId","data-v-6c3cdb93"]]);export{x as default};
