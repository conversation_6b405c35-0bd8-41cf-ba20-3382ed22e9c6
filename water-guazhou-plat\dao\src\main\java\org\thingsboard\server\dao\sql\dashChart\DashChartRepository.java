/**
 * Copyright © 2017 The Thingsboard Authors
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.sql.dashChart;

import com.datastax.driver.mapping.annotations.Param;
import org.springframework.data.repository.CrudRepository;
import org.thingsboard.server.dao.model.sql.DashChartEntity;
import org.thingsboard.server.dao.util.SqlDao;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/3/12 17:46
 */

@SqlDao
public interface DashChartRepository extends CrudRepository<DashChartEntity, String> {


    List<DashChartEntity> findByTenantId(@Param("tenantId") String tenantId);

    List<DashChartEntity> findByOriginatorId(@Param("originatorId") String originatorId);

    void  deleteByDashBoardId(@Param("dashboardId") String dashboardId);

    DashChartEntity findByDashBoardJsonId(@Param("dashBoardJsonId") String jsonId);

    List<DashChartEntity> findByOriginatorIdAndChartType(@Param("originatorId") String originatorId,
                                                             @Param("chartType") String type);

    List<DashChartEntity> findByOriginatorIdAndNameLike(String originatorId, String name);
}
