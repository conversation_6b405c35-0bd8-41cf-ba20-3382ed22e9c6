package org.thingsboard.server.dao.model.sql.smartOperation.project;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class SoProjectFiles {
    // 项目立项
    private String project;

    // 招投标
    private String bidding;

    // 单项工程
    private String construction;

    // 设计管理
    private String constructionDesign;

    // 设计变更
    private String constructionDesignAmend;

    // 工程预算
    private String constructionEstimate;

    // 合同管理
    private String constructionContract;

    // 合同变更
    private String constructionContractAmend;

    // 签证单
    private String constructionVisa;

    // 费用管理
    private String constructionExpense;

    // 实施管理
    private String constructionApply;

    // 验收管理
    private String constructionAccept;

    // 工程结算
    private String constructionSettlement;

    // 项目归档
    private String constructionArchive;

    // 总验收
    private String projectAccept;

    // 总结算
    private String projectSettlement;

}
