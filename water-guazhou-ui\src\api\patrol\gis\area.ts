import { request } from '@/plugins/axios';

/**
 * 查询区域/路线下的关键点
 * @param params
 * @returns
 */
export const GetKeyPoint = (params: { areaId: string }) => {
  return request({
    url: '/api/sm/circuitDistrictPoint',
    method: 'get',
    params
  });
};
/**
 * 添加关键点
 * @param params
 * @returns
 */
export const AddKeyPoint = (params: {
  name: string;
  areaId: string;
  lat: string;
  lon: string;
  remark?: string;
}) => {
  return request({
    url: '/api/sm/circuitDistrictPoint',
    method: 'post',
    data: params
  });
};
/**
 * 删除关键点
 * @param id
 * @returns
 */
export const DeleteKeyPoint = (id: string) => {
  return request({
    url: `/api/sm/circuitDistrictPoint/${id}`,
    method: 'delete'
  });
};

/**
 * 查询区域/路径点JSON
 * @param id
 * @returns
 */
export const GetDistrictPointsJson = (id: string) => {
  return request({
    url: `/api/sm/circuitDistrictArea/${id}/points`,
    method: 'get'
  });
};

/**
 *  添加区域/路线
 * @param params
 * @returns
 */
export const AddDistrict = (params: {
  id: string;
  type: string;
  name: string;
  districtId: string;
  points: string;
}) => {
  return request({
    url: '/api/sm/circuitDistrictArea',
    method: 'post',
    data: params
  });
};

/**
 * 删除区域/路线
 * @param id
 * @returns
 */
export const DeleteDistrict = (id: string) => {
  return request({
    url: `/api/sm/circuitDistrictArea/${id}`,
    method: 'delete'
  });
};
/**
 * 添加片区
 * @param params
 * @returns
 */
export const AddArea = (params: {
  /** 全量更新使用 */
  id?: string;
  /** 片区名字 */
  name: string;
  /** 父级Id */
  parentId: string;
  /** 备注 */
  remark?: string;
}) => {
  return request({
    url: '/api/sm/circuitDistrict',
    method: 'post',
    data: params
  });
};

/**
 * 删除片区
 * @param id
 * @returns
 */
export const DeleteArea = (id: string) => {
  return request({
    url: `/api/sm/circuitDistrict/${id}`,
    method: 'delete'
  });
};
/**
 * 查询片区树
 * @param params
 * @returns
 */
export const GetAreaTree = (params?: any) => {
  return request({
    url: '/api/sm/circuitDistrict/tree',
    method: 'get',
    params
  });
};
