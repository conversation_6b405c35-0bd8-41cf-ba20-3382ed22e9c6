package org.thingsboard.server.dao.guard;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.smartProduction.guard.GuardPlace;
import org.thingsboard.server.dao.sql.smartProduction.guard.GuardPlaceMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.guard.GuardPlacePageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.guard.GuardPlaceSaveRequest;

@Service
public class GuardPlaceServiceImpl implements GuardPlaceService {
    @Autowired
    private GuardPlaceMapper mapper;

    @Override
    public GuardPlace findById(String id) {
        return mapper.selectById(id);
    }

    @Override
    public IPage<GuardPlace> findAllConditional(GuardPlacePageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    public GuardPlace save(GuardPlaceSaveRequest entity) {
        return QueryUtil.saveOrUpdateOneByRequest(entity, mapper::insert, mapper::updateFully);
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }

}
