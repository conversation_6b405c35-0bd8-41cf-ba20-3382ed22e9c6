/**
 * 报警类型
 */
export const alarmStrType = [
  { label: '液位异常', value: '液位异常' },
  { label: '水质异常', value: '水质异常' },
  { label: '设备故障', value: '设备故障' },
  { label: '通讯异常', value: '通讯异常' },
  { label: '流量异常', value: '流量异常' },
  { label: '控制异常', value: '控制异常' },
  { label: '设备健康', value: '设备健康' },
  { label: '机组电压异常', value: '机组电压异常' },
  { label: '压力异常', value: '压力异常' },
  { label: '余氯异常', value: '余氯异常' },
  { label: '浊度异常', value: '浊度异常' },
  { label: 'PH异常', value: 'PH异常' },
  { label: '其他', value: '其他' }
];

export const alarmNumType = [
  { label: '液位异常', value: '1' },
  { label: '水质异常', value: '2' },
  { label: '设备故障', value: '3' },
  { label: '通讯异常', value: '4' },
  { label: '流量异常', value: '5' },
  { label: '控制异常', value: '6' },
  { label: '设备健康', value: '7' },
  { label: '机组电压异常', value: '9' },
  { label: '压力异常', value: '10' },
  { label: '其他', value: '8' }
];

const yantingAlarmNumType = [
  { label: '液位异常', value: '1' },
  { label: '水质异常', value: '2' },
  { label: '设备故障', value: '3' },
  { label: '通讯异常', value: '4' },
  { label: '流量异常', value: '5' },
  { label: '控制异常', value: '6' },
  { label: '设备健康', value: '7' },
  { label: '机组电压异常', value: '9' },
  { label: '压力异常', value: '10' },
  { label: '其他', value: '8' },
  { label: '日用量报警', value: '日用量报警' },
  { label: '月用量报警', value: '月用量报警' }
];

export const alarmType = (() => {
  switch (window.SITE_CONFIG.SITENAME) {
    case 'yanting':
      return yantingAlarmNumType;
    case 'jingzhou':
      return alarmStrType;
    default:
      return alarmNumType;
  }
})();
