<template>
  <div class="app-tools">
    <el-popover
      v-if="state.showBell"
      :width="300"
      popper-style="box-shadow: rgb(14 18 22 / 35%) 0px 10px 38px -10px, rgb(14 18 22 / 20%) 0px 10px 20px -15px; padding: 20px;"
      style="margin-right: 10px"
    >
      <template #reference>
        <el-badge class="alarm" :value="state.alarm">
          <Bell
            style="color: white; width: 22px; height: 22px"
            @click="jumpToAlarm"
          />
        </el-badge>
      </template>
      <template #default>
        <information
          :config="state.data"
          @refreshData="refreshData"
        ></information>
      </template>
    </el-popover>
    <el-dropdown class="avatar-container" trigger="click">
      <div class="avatar-wrapper">
        <span>{{ userStore.user?.name }}</span>
        <img class="user-avatar" :src="userLog" />
      </div>
      <template #dropdown>
        <el-dropdown-menu class="user-dropdown">
          <el-dropdown-item>
            <span @click="goto('accountManage')">个人中心</span>
          </el-dropdown-item>
          <!-- <el-dropdown-item divided>
            <ChangeTheme></ChangeTheme>
          </el-dropdown-item>
          <el-dropdown-item v-if="alarmView">
            <ChangeMenuType></ChangeMenuType>
          </el-dropdown-item>
          <el-dropdown-item v-if="alarmView">
            <ToggleTags v-if="alarmView"></ToggleTags>
          </el-dropdown-item> -->
          <el-dropdown-item divided>
            <el-button
              class="long-btn"
              size="small"
              :text="true"
              @click="logout"
            >
              登出
            </el-button>
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
    <!-- 返回门户 -->
    <!-- <div style="padding:0 10px;color:#fff;cos">
      <span @click="goto('accountManage')">个人中心</span>
    </div>
    <div
      style="color:#fff;margin: 0 20px;"
      @click="toBack"
    >
      <el-button
        type="primary"
        :icon="HomeFilled"
      >
        返回门户
      </el-button>
    </div> -->
  </div>
</template>
<script lang="ts" setup>
import { ElNotification } from 'element-plus';
import { Bell, HomeFilled } from '@element-plus/icons-vue';
// import { hasPermission } from '@/utils/RouterHelper'
import { useUserStore, useAppStore } from '@/store';
import userLog from '@/assets/images/user-log.png';
// import ChangeTheme from '../components/ChangeTheme.vue'
// import ChangeMenuType from '../components/ChangeMenuType.vue'
// import ToggleTags from '../components/ToggleTags.vue'
import information from './information.vue';
import { getNotifyCount } from '@/api/admin';
import { removeSlash } from '@/utils/removeIdSlash';
import voicePlay from '@/utils/Voice_broadcast';

const appStore = useAppStore();
const userStore = useUserStore();
const router = useRouter();
const state = reactive<{
  alarm: number;
  userLog: string;
  showBell: boolean;
  time: any;
  data: any;
  sound: any;
}>({
  alarm: 0,
  userLog,
  showBell: !!window.SITE_CONFIG.LAYOUT?.SHOWBELL,
  time: null,
  data: {},
  sound: true
});
console.log(state.showBell, window.SITE_CONFIG.LAYOUT?.SHOWBELL);
// const alarmView = computed(() => !hasPermission(['SYS_ADMIN']))
// const alarmShow = computed(() => {
//   const isSysAdmin = userStore.roles.includes('SYS_ADMIN') && userStore.roles.includes('TENANT_SYS')
//   if (isSysAdmin) return false
//   return store.permission.addRouters.some(item => item.label === '告警管理')
// })

// 个人中心
const jumpToAlarm = () => {
  console.log('clicked');
  // router.push({
  //   path: '/HVACPage/realTimeAlarm/index/ah'
  // })
};

const goto = (name: string) => {
  router.push({ name });
};

// 退出
const logout = async () => {
  userStore.LogOut();
};

const openElNotification = () => {
  if (state.alarm !== 0 && appStore.MessageNotification) {
    ElNotification({
      title: '提示',
      message: `您有${state.alarm}条未处理信息，请及时处理！`,
      duration: 0,
      offset: 80,
      onClose: () => {
        state.sound = false;
      }
    });
    if (window.SITE_CONFIG.SITENAME === 'meixian') {
      appStore.VoiceBroadcast &&
        voicePlay.handleSpeak(`您有${state.alarm}条未处理信息，请及时处理！`);
      voicePlay.msg.onend = () => {
        if (state.sound) window.speechSynthesis.speak(voicePlay.msg);
      };
    } else {
      appStore.VoiceBroadcast &&
        voicePlay.handleSpeak(`您有${state.alarm}条未处理信息，请及时处理！`);
    }
  }
};

onMounted(() => {
  if (state.showBell) {
    const params = {
      to: removeSlash(userStore.id),
      status: 0
    };
    getNotifyCount(params).then((res) => {
      let sum = 0;
      for (const i in res.data.data) {
        sum += res.data.data[i];
      }
      state.data = res.data.data || {};
      state.alarm = sum;
      openElNotification();
    });
    if (
      window.SITE_CONFIG.SITENAME === 'meixian' &&
      appStore.MessageNotification
    ) {
      state.time = setInterval(() => {
        state.sound = true;
        openElNotification();
      }, 60000);
    } else if (appStore.MessageNotification) {
      state.time = setInterval(() => {
        getNotifyCount(params).then((res) => {
          let sum = 0;
          for (const i in res.data.data) {
            sum += res.data.data[i];
          }
          state.data = res.data.data || {};
          if (state.alarm !== sum && sum !== 0) {
            appStore.VoiceBroadcast &&
              voicePlay.handleSpeak(`您有新的未处理信息，请及时处理！`);
          }
          state.alarm = sum;
        });
      }, 120000);
    }
  }
});

const refreshData = () => {
  const params = {
    to: removeSlash(userStore.id),
    status: 0
  };
  getNotifyCount(params).then((res) => {
    let sum = 0;
    for (const i in res.data.data) {
      sum += res.data.data[i];
    }
    state.data = res.data.data || {};
    state.alarm = sum;
  });
};

onUnmounted(() => {
  clearInterval(state.time);
});
</script>

<style lang="scss" scoped>
.app-tools {
  display: flex;
  align-items: center;

  .alarm {
    margin-right: 12px;
    width: 1em;
    height: 1em;

    sup {
      top: 15px;
    }
  }

  .avatar-container {
    margin-left: 10px;

    .avatar-wrapper {
      margin-right: 30px;
      color: #fff;
      cursor: pointer;
      // margin-top: 5px;
      position: relative;
      display: flex;
      align-items: center;

      .user-avatar {
        width: 30px;
        height: 30px;
        margin-left: 10px;
        border-radius: 50%;
      }

      .el-icon-caret-bottom {
        position: absolute;
        right: -20px;
        top: 20px;
        font-size: 12px;
      }
    }
  }
}

.long-btn {
  width: 100%;
}
</style>
