package org.thingsboard.server.controller.video;

import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.ny.NyVideoBO;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.DTO.VideoSaveDTO;
import org.thingsboard.server.dao.model.sql.VideoEntity;
import org.thingsboard.server.dao.util.imodel.query.video.VideoMonitoringPageRequest;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;
import org.thingsboard.server.dao.video.VideoService;
import org.thingsboard.server.dao.util.CameraUtil;

import java.io.IOException;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/video/")
public class VideoController extends BaseController {
    @Autowired
    private CameraUtil cameraUtil;

    @Autowired
    private VideoService videoService;

    @GetMapping(value = "findByProject/{projectId}")
    public List<VideoEntity> findByProject(@PathVariable("projectId") String projectId,
                                           @RequestParam(required = false, defaultValue = "") String name,
                                           @RequestParam(required = false, defaultValue = "") String groupId,
                                           @RequestParam(required = false, defaultValue = "") String isBind) {
        return videoService.findByProject(projectId, name, isBind, groupId);
    }

    @GetMapping(value = "findList")
    public List<VideoEntity> findList(@RequestParam(required = false, defaultValue = "") String name,
                                           @RequestParam(required = false, defaultValue = "") String projectId,
                                           @RequestParam(required = false, defaultValue = "") String groupId,
                                           @RequestParam(required = false, defaultValue = "") String isBind) {
        return videoService.findByProject(projectId, name, isBind, groupId);
    }

    @GetMapping(value = "findAll")
    public List<VideoEntity> findAll(@RequestParam(required = false, defaultValue = "") String name) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return videoService.findAll(name, tenantId);
    }

    @GetMapping("tree")
    public IstarResponse getTree(@RequestParam(required = false, defaultValue = "") String projectId) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return IstarResponse.ok(videoService.getTreeByProjectId(projectId, tenantId));
    }

    @PostMapping(value = "save")
    public VideoEntity save(@RequestBody VideoEntity videoEntity) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        videoEntity.setTenantId(tenantId);
        return videoService.save(videoEntity);
    }

    @PostMapping(value = "batchSave")
    public IstarResponse batchSave(@RequestBody VideoSaveDTO videoSaveDTO) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        videoService.batchSave(videoSaveDTO, tenantId);
        return IstarResponse.ok();
    }


    @DeleteMapping(value = "delete/{id}")
    public boolean delete(@PathVariable("id") String id) {
        return videoService.delete(id);
    }


    @PostMapping("/delete/all")
    public boolean deleteAll(@RequestBody List<String> ids) {
        return videoService.deleteAll(ids);
    }

    @PostMapping("/ny/video/save")
    public VideoEntity saveNyVideo(@RequestBody NyVideoBO nyVideoBO) throws ThingsboardException {
        return videoService.save(nyVideoBO, getTenantId());
    }

    @GetMapping("findByProjectAndType/{projectId}/{type}")
    public List<VideoEntity> findByProjectAndType(@PathVariable String projectId, @PathVariable String type) {
        return videoService.findByProjectAndType(projectId, type);
    }

    @GetMapping("findByTenantIdAndType/{type}")
    public List<VideoEntity> findByType(@PathVariable String type) throws ThingsboardException {
        return videoService.findByType(type, getTenantId());
    }

    /**
     * 获取海康摄像头列表
     *
     * @param name
     * @return
     * @throws ThingsboardException
     */
    @GetMapping("camaraList")
    public List getCamaraList(@RequestParam(required = false, defaultValue = "") String name) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return cameraUtil.getCameraList(name, tenantId);
    }

    /**
     * 获取大华摄像头列表
     * @param name
     * @return
     * @throws ThingsboardException
     */
    @GetMapping("dahuaCamaraList")
    public List getDahuaCamaraList(@RequestParam(required = false, defaultValue = "") String name) throws ThingsboardException {
        return cameraUtil.getDahuaCamaraList(name);
    }

    /**
     * 根据cameraIndexCode获取播放连接
     */
    @GetMapping("getPreviewUrl/{cameraIndexCode}")
    public String getPreviewURL(@PathVariable String cameraIndexCode) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return cameraUtil.getPreviewURL(cameraIndexCode, tenantId);
    }

    /**
     * 根据id获取播放连接
     */
    @GetMapping("getPreviewUrlById/{id}")
    public String getPreviewURLById(@PathVariable String id) throws ThingsboardException, IOException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return cameraUtil.getPreviewURLById(id, tenantId);
    }


    /**
     * 根据cameraIndexCode获取播放连接
     */
    @GetMapping("getPreviewUrlByCode/{cameraIndexCode}")
    public String getPreviewUrlByCode(@PathVariable String cameraIndexCode) throws Exception {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return cameraUtil.getPreviewUrlByCode(cameraIndexCode, tenantId);
    }

    /**
     * 云台操作
     */
    @PostMapping("controlling")
    public IstarResponse controlling(@RequestBody JSONObject params) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return cameraUtil.controlling(params, tenantId);
    }

    /**
     * 视频回放
     */
    @GetMapping("getPlayback")
    public IstarResponse getPlaybackURL(@RequestParam Map params) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return cameraUtil.getPlaybackURL(JSONObject.parseObject(JSONObject.toJSONString(params)), tenantId);
    }

    /**
     * 开始录像
     */
    @GetMapping("recordStart")
    public IstarResponse recordStart(@RequestParam Map params) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return cameraUtil.recordStart(JSONObject.parseObject(JSONObject.toJSONString(params)), tenantId);
    }

    /**
     * 停止录像
     */
    @GetMapping("recordStop")
    public IstarResponse recordEnd(@RequestParam Map params) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return cameraUtil.recordStop(JSONObject.parseObject(JSONObject.toJSONString(params)), tenantId);
    }

    /**
     * 分页查询视频列表
     * @param request 分页查询条件
     * @return 分页数据
     * @throws ThingsboardException
     */
    @GetMapping("findByPage")
    public Page<VideoEntity> findByPage(VideoMonitoringPageRequest request) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        request.setTenantId(tenantId);
        return videoService.findByPage(request);
    }
}
