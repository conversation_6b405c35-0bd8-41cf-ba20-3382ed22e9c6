package org.thingsboard.server.dao.model.sql.smartProduction.guard;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseUsername;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

@Getter
@Setter
@ResponseEntity
@TableName("guard_arrange_partner")
public class GuardArrangePartner {
    // id
    private String id;

    // 排班id
    private String arrangeId;

    // 班次名称
    @TableField(exist = false)
    private String className;

    // 排班开始时间
    @TableField(exist = false)
    private String beginTime;

    // 排班结束时间
    @TableField(exist = false)
    private String endTime;

    // 用户id
    @ParseUsername(withDepartment = true)
    private String userId;

    // 岗位
    @TableField(exist = false)
    private String roleName;

    // 联系电话
    @TableField(exist = false)
    private String phone;

    public GuardArrangePartner() {

    }

    public GuardArrangePartner(String arrangeId, String userId) {
        this.arrangeId = arrangeId;
        this.userId = userId;
    }

}
