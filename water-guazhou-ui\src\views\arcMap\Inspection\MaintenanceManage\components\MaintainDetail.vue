<template>
  <div>
    <div class="detail-taskinfo">
      <div class="detail-group-title">
        任务详情
      </div>
      <FormTable :config="TableConfigTaskInfo"></FormTable>
    </div>
    <div class="detail-response">
      <p class="detail-group-title">
        设备信息
      </p>
      <Tabs
        v-model="state.curResponse"
        :config="{
          type: 'tabs',
          tabs: [
            { label: '已完成', value: '已完成' },
            { label: '未完成', value: '未完成' }
          ]
        }"
        @change="refreshResponse"
      ></Tabs>
      <div class="detail-response-tabcontent">
        <div class="left">
          <Tabs
            v-model="state.curDeviceType"
            :config="{
              type: 'tabs',
              tabs: state.deviceTabs,
              tabType: 'simple'
            }"
            @change="refreshResponse"
          ></Tabs>
          <DetailTable
            ref="refDetail"
            @row-click="handleRowClick"
            @refresh-data="refreshResponse"
          ></DetailTable>
        </div>
        <div class="right">
          <div class="right-header">
            <span>{{ state.currentDevice?.SID }} - 详情</span>
          </div>
          <div class="right-item">
            <span>图片</span>
            <div class="box img">
              <el-image
                v-for="(item, i) in curRowInfo.img"
                :key="i"
                :src="item"
              ></el-image>
            </div>
          </div>
          <div class="right-item video">
            <span>视频</span>
            <div class="box video">
              <Videor
                v-for="(item, i) in curRowInfo.video"
                :key="i"
                :url="item"
                style="height: 180px;"
              ></Videor>
            </div>
          </div>
          <div class="right-item">
            <span>音频</span>
            <div class="box audio">
              <Voicer
                v-for="(item, i) in curRowInfo.audio"
                :key="i"
                :show-url="true"
                :url="item"
                style="height: 25px;"
              ></Voicer>
            </div>
          </div>
          <div class="right-item attac">
            <span>附件</span>
            <div class="box">
              <el-button
                v-for="(item, i) in curRowInfo.file"
                :key="i"
                :text="true"
                :size="'small'"
                style="width: 100%;text-align: left;justify-content: flex-start;margin-left: 12px;"
                @click="downloadFile(item)"
              >
                {{ item }}
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { GetMaintainTaskItems } from '@/api/patrol'
import { downloadFile } from '@/utils/fileHelper'
import { PatrolTaskStatusConfig } from '../../config'
import DetailTable from '@/views/arcMap/components/common/DetailTable.vue'
import { delay } from '@/utils/GlobalHelper'

const emit = defineEmits(['row-click'])
const props = defineProps<{
  row?: any
  view?: __esri.MapView
}>()
const state = reactive<{
  curResponse: string
  responses: any[]
  curDeviceType: string
  deviceTabs: NormalOption[]
  currentRow: any
  currentDevice: any
  tableRows: any[]
}>({
  curResponse: '已完成',
  responses: [],
  curDeviceType: props.row.deviceName,
  deviceTabs: [{ label: props.row.deviceName, value: props.row.deviceName }],
  currentRow: undefined,
  currentDevice: undefined,
  tableRows: []
})

const TableConfigTaskInfo = reactive<ITable>({
  dataList: [],
  columns: [
    // { minWidth: 120, label: '任务编号', prop: 'code' },
    { minWidth: 120, label: '任务名称', prop: 'name' },
    { minWidth: 120, label: '养护设备类型', prop: 'deviceName' },
    { minWidth: 120, label: '开始时间', prop: 'beginTime' },
    { minWidth: 120, label: '结束时间', prop: 'endTime' },
    { minWidth: 120, label: '创建时间', prop: 'createTime' },
    { minWidth: 120, label: '养护人员', prop: 'maintainUserName' },
    {
      minWidth: 120,
      label: '任务状态',
      prop: 'status',
      formatter: (row, val) => PatrolTaskStatusConfig[val]?.text
    }
  ],
  pagination: {
    hide: true
  }
})
const curRowInfo = computed(() => {
  return {
    img: state.currentRow?.img?.split(',')?.filter(item => !!item) || [],
    video: state.currentRow?.video?.split(',')?.filter(item => !!item) || [],
    audio: state.currentRow?.audio?.split(',')?.filter(item => !!item) || [],
    file: state.currentRow?.file?.split(',')?.filter(item => !!item) || []
  }
})
const refreshResponse = () => {
  GetMaintainTaskItems({
    taskId: props.row.id,
    isComplete: state.curResponse !== '未完成',
    page: 1,
    size: 9999
  }).then(res => {
    const data = res.data?.data?.data || []
    state.tableRows = data
    const tab = {
      layername: props.row.deviceName,
      layerid: props.row.device,
      oids: data.map(item => item.objectId)
    }
    refDetail.value?.refreshDetail(props.view, tab)
  })
}
const refDetail = ref<InstanceType<typeof DetailTable>>()
const handleRowClick = async row => {
  state.currentRow = state.tableRows.find(
    item => item.objectId === row.OBJECTID?.toString()
  )
  state.currentDevice = row
  emit('row-click')
  await delay(500)
  refDetail.value?.extentTo(props.view, row.OBJECTID)
}
// watch(
//   () => props.row,
//   newVal => {
//     if (!newVal) return
//     refreshResponse()
//     TableConfigTaskInfo.dataList = [props.row]
//   }
// )
onMounted(() => {
  refreshResponse()
  TableConfigTaskInfo.dataList = [props.row || {}]
})
</script>
<style lang="scss" scoped>
.detail-taskinfo {
  padding-bottom: 30px;
}
.detail-group-title {
  font-size: 16px;
  line-height: 32px;
  font-weight: bold;
}
.detail-response-tabcontent {
  height: 100%;
  display: flex;
  flex-direction: row;
  .left,
  .right {
    padding: 8px;
    height: 600px;
  }
  .left {
    width: 60%;
    // display: flex;
    // flex-direction: column;
  }
  .right {
    width: 40%;
    .right-header {
      display: flex;
      align-items: center;
      height: 40px;
      font-size: 16px;
      font-weight: bold;
    }
    .right-item {
      & > span {
        line-height: 36px;
        font-size: 14px;
      }
      .box {
        height: 100px;
        border: 1px solid var(--el-border-color);
        padding: 8px;
        overflow: auto;
        &.video {
          height: auto;
          min-height: 100px;
        }
        .el-image {
          height: 79px;
        }
      }
    }
  }
}
</style>
