<!-- 统一工单-工单中心-完工审核 -->
<template>
  <div class="wrapper">
    <CardSearch
      ref="refSearch"
      :config="SearchConfig"
    ></CardSearch>
    <CardTable
      class="card-table"
      :config="TableConfig"
    ></CardTable>
    <DialogForm
      ref="refFormAudit"
      :config="FormConfig_Audit"
    ></DialogForm>
    <SLDrawer
      ref="refdetail"
      :config="detailConfig"
    >
      <detail :id="selectedId"></detail>
    </SLDrawer>
  </div>
</template>
<script lang="ts" setup>
import { Refresh, Search } from '@element-plus/icons-vue'
import { useUserStore } from '@/store'
import { removeSlash } from '@/utils/removeIdSlash' // 处理id, idRemoveSlash
// import { formatDate } from '@/utils/DateFormatter'
import { SLConfirm, SLMessage } from '@/utils/Message'
import { GetWorkOrderPage, VerifyWorkOrder, getWorkOrderEmergencyLevelList } from '@/api/workorder'
import detail from './components/detail.vue'
import { traverse } from '@/utils/GlobalHelper'

const refSearch = ref<ICardSearchIns>()
const refFormAudit = ref<IDialogFormIns>()
const refdetail = ref<ISLDrawerIns>()

const state = reactive<{
  WorkOrderEmergencyLevelList: any[],
}>({
  WorkOrderEmergencyLevelList: []
})

function initOptions() {
  // 紧急程度
  getWorkOrderEmergencyLevelList('1').then(res => {
    state.WorkOrderEmergencyLevelList = traverse(res.data.data || [], 'children', { label: 'name', value: 'id' })
  })
}

// 明细弹框
const detailConfig = reactive<IDrawerConfig>({
  title: '流程明细',
  cancel: false,
  className: 'lightColor',
  group: []
})
const selectedId = ref<string>('')

const SearchConfig = reactive<ISearch>({
  filters: [
    {
      type: 'radio-button',
      label: '类别',
      labelWidth: 40,
      field: 'stageBetween',
      options: [
        { label: '待审核', value: 'SUBMIT' },
        { label: '已审核', value: 'APPROVED' }
      ],
      onChange: val => {
        const perms = val === 'SUBMIT' ? ['详情', '审核', '复审'] : ['详情']
        const permWidth = val === 'SUBMIT' ? 140 : 100
        TableConfig.operationWidth = permWidth
        TableConfig.operations?.map(item => {
          const text:string = item.text as string
          item.perm = perms.indexOf(text) !== -1
        })
        refreshData()
      }
    },
    {
      type: 'input',
      label: '标题',
      field: 'title',
      onChange: () => refreshData()
    },
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          svgIcon: shallowRef(Search),
          click: () => refreshData()
        },
        {
          perm: true,
          text: '重置',
          type: 'default',
          svgIcon: shallowRef(Refresh),
          click: () => {
            // SearchConfig.defaultParams = { type: '待审核' }
            refSearch.value?.resetForm()
            refreshData()
          }
        }
      ]
    }
  ],
  defaultParams: { stageBetween: 'SUBMIT' },
  handleSearch: () => refreshData()
})
const TableConfig = reactive<ITable>({
  columns: [
    { minWidth: 180, prop: 'serialNo', label: '工单编号' },
    { minWidth: 120,
      prop: 'level',
      label: '紧急程度',
      tag: true,
      tagColor: (row):string => state.WorkOrderEmergencyLevelList.find(item => item.value === row.level)?.color || '',
      formatter: row => state.WorkOrderEmergencyLevelList.find(item => item.value === row.level)?.label
    },
    { minWidth: 120, prop: 'type', label: '类型' },
    { minWidth: 120, prop: 'title', label: '标题' },
    { minWidth: 200, prop: 'address', label: '地址' },
    {
      minWidth: 160,
      prop: 'createTime',
      label: '分派时间',
      formatter: row => dayjs(row.createTime).format('YYYY-MM-DD HH:mm:ss')
    },
    {
      minWidth: 160,
      prop: 'completeTime',
      label: '完成时间',
      formatter: row => dayjs(row.completeTime).format('YYYY-MM-DD HH:mm:ss')
    },
    { minWidth: 120, prop: 'processUserName', label: '处理人' },
    { minWidth: 120,
      prop: 'status',
      label: '状态',
      formatter: row => {
        switch (row.status) {
          case 'PENDING':
          case 'ASSIGN':
            return '待处理'
          case 'RESOLVING':
          case 'ARRIVING':
          case 'PROCESSING':
          case 'SUBMIT':
          case 'REVIEW':
          case 'CHARGEBACK_REVIEW':
          case 'HANDOVER_REVIEW':
          case 'REASSIGN':
          case 'COLLABORATION':
            return '处理中'
          case 'APPROVED':
          case 'CHARGEBACK':
          case 'TERMINATED':
            return '已结束'
          default:
            break
        }
      } }
  ],
  dataList: [],
  pagination: {
    refreshData: ({ page, size }) => {
      TableConfig.pagination.limit = size
      TableConfig.pagination.page = page
      refreshData()
    }
  },
  operations: [
    {
      perm: true,
      isTextBtn: true,
      text: '详情',
      click: row => {
        selectedId.value = row.id || ''
        detailConfig.title = row.serialNo
        refdetail.value?.openDrawer()
      }
    },
    {
      perm: true,
      text: '审核',
      isTextBtn: true,
      click: row => handleCharge(row)
    }
  ]
})
const FormConfig_Audit = reactive<IDialogFormConfig>({
  dialogWidth: 500,
  title: '完工审核',
  labelPosition: 'top',
  group: [
    {
      fields: [
        {
          type: 'radio',
          label: '审核结果：',
          field: 'stage',
          options: [
            { label: '通过', value: 'APPROVED' },
            { label: '退回', value: 'REJECTED' }
          ],
          rules: [{ required: true, message: '请选择结果' }]
        },
        { type: 'textarea', label: '备注：', field: 'processRemark' }
      ]
    }
  ],
  defaultValue: {},
  submit: (params: any) => {
    SLConfirm('确定提交？', '提示信息').then(async () => {
      FormConfig_Audit.submitting = true
      try {
        const res = await VerifyWorkOrder(TableConfig.currentRow?.id, {
          ...params,
          processAdditionalInfo: JSON.stringify(params)
        })
        if (res.data?.code === 200) {
          SLMessage.success('操作成功')
          refFormAudit.value?.closeDialog()
          refreshData()
        } else {
          SLMessage.error(res.data?.err || '操作失败')
        }
      } catch (error) {
        SLMessage.error('系统错误')
      }
      FormConfig_Audit.submitting = false
    }).catch(() => {
      //
    })
  }
})
const handleCharge = row => {
  TableConfig.currentRow = row
  refFormAudit.value?.openDialog()
}
const refreshData = async () => {
  TableConfig.loading = true
  try {
    const query = refSearch.value?.queryParams || {}
    const params:any = {
      page: TableConfig.pagination.page || 1,
      size: TableConfig.pagination.limit || 20,
      ...query,
      stageBetween: query.stageBetween === 'APPROVED' ? 'APPROVED' : 'SUBMIT,REVIEW',
      stepProcessUserId: removeSlash(useUserStore().user?.id?.id || '')
    }
    console.log()
    const res = await GetWorkOrderPage(params)
    TableConfig.dataList = res.data?.data?.data || []
    TableConfig.pagination.total = res.data?.data?.total || 0
  } catch (error) {
    //
  }

  TableConfig.loading = false
}
onMounted(() => {
  initOptions()
  refreshData()
})
</script>
<style lang="scss" scoped></style>
