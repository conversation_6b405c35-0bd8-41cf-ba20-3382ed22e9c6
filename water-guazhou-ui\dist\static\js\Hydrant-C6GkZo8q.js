import{d as q,c as x,r as h,b as c,Q as k,g as N,h as I,F as S,q as C,i as B,_ as M}from"./index-r0dFAfgr.js";import O from"./RightDrawerMap-D5PhmGFO.js";import{GetFieldConfig as R,QueryByPolygon as V,GetFieldValueByGeoserver as L}from"./wfsUtils-DXofo3da.js";import"./ArcView-DpMnCY82.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./widget-BcWKanF2.js";import"./pe-B8dP0-Ut.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./TileLayer-B5vQ99gG.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./commonProperties-DqNQ4F00.js";import"./project-DUuzYgGl.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./IdentifyResult-4DxLVhTm.js";import"./ViewHelper-BGCZjxXH.js";import"./fieldconfig-Bk3o1wi7.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./LayerHelper-Cn-iiqxI.js";import"./pipe-nogVzCHG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./ArcStationWarning-BF9YrSzF.js";/* empty css                         */import"./useWaterPoint-Bv0z6ym6.js";import"./DateFormatter-Bm9a68Ax.js";import"./zhandian-YaGuQZe6.js";import"./useStation-DJgnSZIA.js";import"./DrawerBox-CLde5xC8.js";import"./SideDrawer-CBntChyn.js";import"./PipeDetail-CTBPYFJW.js";import"./Search-NSrhrIa_.js";import"./FormTableColumnFilter-BT7pLXIC.js";import"./QueryHelper-ILO3qZqg.js";import"./config-fy91bijz.js";import"./ArcBRTools-BO92yznB.js";import"./PipeLength.vue_vue_type_script_setup_true_lang-BZfUsvDf.js";import"./arcWidgetButton-0glIxrt7.js";import"./StatisticsHelper-D-s_6AyQ.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./usePipelineGroup-Ba1pmWz_.js";import"./GroupLayer-DhhN3FyN.js";import"./lazyLayerLoader-DbM9sT1W.js";import"./WFSLayer-1TtJp3nx.js";import"./clientSideDefaults-VQhQaYxh.js";import"./QueryEngineCapabilities-Dk3NJwmm.js";import"./wfsUtils-Du031XaC.js";import"./geojson-MBFu2-HZ.js";import"./xmlUtils-CtUoQO7q.js";import"./ListWindow-WS05QqV0.js";import"./PopLayout-BP55MvL7.js";import"./PoiSearchV2-D7yNeLuv.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./URLHelper-B9aplt5w.js";import"./useLayerList-DmEwJ-ws.js";import"./useScaleBar-Beed-z91.js";const u="测点",Ar=q({__name:"Hydrant",setup(D){const r=x(),y=x(),f={},i=h({loading:!1,tabs:[],layerIds:[],layerInfos:[]}),g=h({group:[{fieldset:{desc:"选择字段"},fields:[{type:"list",data:[],className:"sql-list-wrapper",setData:async(t,o)=>{var p,a,d,s;if(!((p=o.layerid)!=null&&p.length))return;const n=await R(u);(s=(d=(a=n.data)==null?void 0:a.featureTypes)==null?void 0:d[0])!=null&&s.properties&&(t.data=n.data.featureTypes[0].properties.map(l=>({name:l.name,alias:l.name,type:l.localType||l.type})).filter(l=>l.name!=="geometry"&&l.name!=="geom"))},setDataBy:"layerid",displayField:"alias",valueField:"name",highlightCurrentRow:!0,nodeClick:t=>{i.curFieldNode=t,m(t.name)}}]},{id:"field-construct",fieldset:{desc:"属性过滤"},fields:[{type:"btn-group",size:"small",style:{width:"40%",display:"flex",flexWrap:"wrap"},className:"sql-btns-wrapper",btns:[{perm:!0,text:"=",styles:{margin:"6px",width:"50px"},click:()=>{m("=")}},{perm:!0,text:"模糊",styles:{margin:"6px",width:"50px"},click:()=>{m(" ILIKE '%替换此处%'")}},{perm:!0,text:">",styles:{margin:"6px",width:"50px"},click:()=>{m(">")}},{perm:!0,text:"<",styles:{margin:"6px",width:"50px"},click:()=>{m("<")}},{perm:!0,text:"非",styles:{margin:"6px",width:"50px"},click:()=>{m("<>")}},{perm:!0,text:"并且",styles:{margin:"6px",width:"50px"},click:()=>{m("AND")}},{perm:!0,text:"或者",styles:{margin:"6px",width:"50px"},click:()=>{m("OR")}}],extraFormItem:[{type:"list",wrapperStyle:{width:"60%",height:"144px"},className:"sql-list-wrapper",field:"uniqueValue",data:[],nodeClick:t=>{m(`'${t}'`)},filters:[{type:"btn-group",btns:[{perm:!0,text:()=>i.curOperate==="uniqueing"?"正在获取唯一值":"获取唯一值",loading:()=>i.curOperate==="uniqueing",disabled:()=>i.curOperate==="detailing",styles:{width:"100%",borderRadius:"0"},click:()=>v()}]}]}]}]},{fieldset:{desc:"组合查询条件"},fields:[{type:"textarea",field:"sql",placeholder:"id > 0"},{type:"btn-group",itemContainerStyle:{marginBottom:"8px"},btns:[{perm:!0,text:"清除组合条件",type:"danger",disabled:()=>i.curOperate==="detailing",click:()=>w(),styles:{width:"100%"}}]},{type:"btn-group",btns:[{perm:!0,text:"查询",disabled:()=>{var t,o,e;return!((e=(o=(t=r.value)==null?void 0:t.dataForm)==null?void 0:o.layerid)!=null&&e.length)},click:()=>F(),styles:{width:"100%"}}]}]}],labelPosition:"top",gutter:12,defaultValue:{layerid:[]}}),m=t=>{var e;if(!r.value)return;(e=r.value)!=null&&e.dataForm||(r.value.dataForm={});const o=r.value.dataForm.sql||" ";r.value.dataForm.sql=o+t+" "},w=()=>{var t;(t=r.value)!=null&&t.dataForm&&(r.value.dataForm.sql="")},F=async()=>{var e,n,p;const{layerid:t}=((e=r.value)==null?void 0:e.dataForm)||{};if(!(t!=null&&t.length)){c.warning("请先选择消防栓图层");return}const o=((n=r.value)==null?void 0:n.dataForm.sql)||"1=1";try{const a=await V(u,null,o);if(!a.data||!a.data.features){c.warning("未查询到消防栓数据");return}const d=a.data.features;i.tabs=d.map(s=>({...s.properties,layername:u,geometry:s.geometry})),(p=y.value)==null||p.refreshDetail(i.tabs)}catch(a){console.error("查询失败:",a),c.error("查询失败")}},v=async()=>{var o,e,n;if(!i.curFieldNode)return;const t=(o=r.value)==null?void 0:o.dataForm.layerid;if(!(t!=null&&t.length)){c.warning("请先选择一个图层");return}i.curOperate="uniqueing";try{const p=await L({layerName:"hydrant",fiedName:i.curFieldNode.name}),a=(e=g.group.find(s=>s.id==="field-construct"))==null?void 0:e.fields[0].extraFormItem,d=a&&a[0];if(d&&((n=p.data)!=null&&n.features)){const s=[...new Set(p.data.features.map(l=>l.properties[i.curFieldNode.name]))];d.data=s}}catch(p){console.error("获取唯一值失败:",p),c.error("获取唯一值失败")}i.curOperate=""},_=async()=>{if(!window.GIS_SERVER_SWITCH){c.warning("请启用GeoServer服务");return}i.layerInfos=[{layerid:"hydrant",layername:"hydrant",alias:"消防栓"}],r.value&&(r.value.dataForm.layerid=["hydrant"])},b=async t=>{f.view=t,await _()};return k(()=>{var t;(t=f.view)==null||t.map.removeAll()}),(t,o)=>{const e=M;return N(),I(O,{ref_key:"refMap",ref:y,title:"消防栓展示",onMapLoaded:b},{default:S(()=>[C(e,{ref_key:"refForm",ref:r,config:B(g)},null,8,["config"])]),_:1},512)}}});export{Ar as default};
