import{_ as v}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as S}from"./CardTable-rdWOL4_6.js";import{_ as C}from"./CardSearch-CB_HNR-Q.js";import{d as D,M as E,c,s as q,r as m,x as l,bN as M,S as R,o as B,g as F,n as G,q as p,i as u,b7 as I,C as N}from"./index-r0dFAfgr.js";import{I as T}from"./common-CvK_P_ao.js";import{m as V,n as W,o as w,b as L}from"./equipmentOutStock-BiNkB8x8.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";const O={class:"wrapper"},P=D({__name:"index",setup(U){const{$btnPerms:s}=E(),d=c(),n=c(),f=c(""),g=c({filters:[{label:"所属仓库编码",field:"code",type:"input",labelWidth:"120px"},{label:"所属仓库名称",field:"name",type:"input",labelWidth:"120px"}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",icon:T.QUERY,click:()=>o()},{type:"default",perm:!0,text:"重置",svgIcon:q(I),click:()=>{var e;(e=d.value)==null||e.resetForm(),o()}}]}]}),a=m({defaultExpandAll:!0,indexVisible:!0,rowKey:"id",columns:[{label:"仓库名称",prop:"name"},{label:"排序",prop:"orderNum"},{label:"备注",prop:"remark"},{label:"编号",prop:"code"}],operationWidth:"240px",operations:[{type:"success",isTextBtn:!0,color:"#4195f0",text:"新增子集",perm:s("RoleManageEdit"),icon:"iconfont icon-xiangqing",click:e=>h(e)},{hide:e=>e.layer===1,type:"primary",isTextBtn:!0,color:"#4195f0",text:"编辑",perm:s("RoleManageEdit"),icon:"iconfont icon-xiangqing",click:e=>b(e)},{hide:e=>e.layer===1,isTextBtn:!0,type:"danger",text:"删除",icon:"iconfont icon-shanchu",perm:s("RoleManageDelete"),click:e=>x(e)}],dataList:[],pagination:{page:1,limit:20,total:0,refreshData:({page:e,size:t})=>{a.pagination.page=e,a.pagination.limit=t,o()}}}),i=m({title:"",labelWidth:"100px",dialogWidth:"500px",submit:e=>{_(e)||(e.id?V(e.id,e).then(()=>{var t;l.success("修改成功"),o(),(t=n.value)==null||t.closeDialog()}):W(e).then(()=>{var t;l.success("添加成功"),o(),(t=n.value)==null||t.closeDialog()}))},defaultValue:{},group:[{fields:[{type:"input-number",label:"编码",field:"code",rules:[{required:!0,message:"请输入编码"}]},{type:"input",label:"名称",field:"name",rules:[{required:!0,message:"请输入名称"}]},{type:"input-number",label:"排序",field:"orderNum"},{type:"textarea",label:"备注",field:"remark"}]}]});function _(e){if(e.code===f.value)return!1;if(M(a.dataList||[],"children","code").includes(e.code))return l.warning("编码重复"),!0}const h=e=>{var t;i.title="新增",i.defaultValue={parentId:e.id},(t=n.value)==null||t.openDialog()},b=e=>{var t;i.title="编辑",f.value=e.code,i.defaultValue={...e||{}},(t=n.value)==null||t.openDialog()},x=e=>{R("确定删除该货架, 是否继续?","删除提示").then(()=>{w(e.id).then(()=>{l.success("删除成功"),o()}).catch(t=>{l.warning(t)})})},o=async()=>{var t;const e={size:a.pagination.limit,page:a.pagination.page,...((t=d.value)==null?void 0:t.queryParams)||{}};L(e).then(r=>{a.dataList=r.data.data.data||[],a.pagination.total=r.data.data.total||0})};return B(()=>{o()}),(e,t)=>{const r=C,y=S,k=v;return F(),G("div",O,[p(r,{ref_key:"refSearch",ref:d,config:u(g)},null,8,["config"]),p(y,{config:u(a),class:"card-table"},null,8,["config"]),p(k,{ref_key:"refForm",ref:n,config:u(i)},null,8,["config"])])}}}),J=N(P,[["__scopeId","data-v-e00ba156"]]);export{J as default};
