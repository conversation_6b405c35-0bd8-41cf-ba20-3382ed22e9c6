package org.thingsboard.server.dao.supplier;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.SupplierEntity;
import org.thingsboard.server.dao.model.sql.SupplierGoodsEntity;
import org.thingsboard.server.dao.model.sql.SupplierQualificationsEntity;
import org.thingsboard.server.dao.sql.supplier.SupplierMapper;
import org.thingsboard.server.dao.sql.supplier.goods.SupplierGoodsMapper;
import org.thingsboard.server.dao.sql.supplier.qualifications.SupplierQualificationsMapper;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-08-24
 */
@Slf4j
@Service
@Transactional
public class SupplierServiceImpl implements SupplierService {
    @Autowired
    private SupplierMapper supplierMapper;

    @Autowired
    private SupplierGoodsMapper supplierGoodsMapper;

    @Autowired
    private SupplierQualificationsMapper supplierQualificationsMapper;

    @Override
    public PageData getList(String name, String address, String status, String importance, int page, int size, String tenantId) {
        List<SupplierEntity> supplierEntities = supplierMapper.getList(name, address, status, importance, page, size, tenantId);
        int total = supplierMapper.getListCount(name, address, status, importance, tenantId);

        return new PageData(total, supplierEntities);

    }

    @Override
    @Transactional
    public SupplierEntity save(SupplierEntity supplierEntity) {
        supplierEntity.setUpdateTime(new Date());
        if (StringUtils.isBlank(supplierEntity.getId())) {
            supplierEntity.setCreateTime(new Date());
            supplierMapper.insert(supplierEntity);
        } else {
            supplierMapper.updateById(supplierEntity);
        }

        // 保存物资信息
        Map deleteMap = new HashMap();
        deleteMap.put("main_id", supplierEntity.getId());
        supplierGoodsMapper.deleteByMap(deleteMap);
        if (supplierEntity.getSupplierGoodsList() != null) {
            for (SupplierGoodsEntity supplierGoodsEntity : supplierEntity.getSupplierGoodsList()) {
                supplierGoodsEntity.setMainId(supplierEntity.getId());
                supplierGoodsEntity.setCreateTime(new Date());
                supplierGoodsEntity.setTenantId(supplierEntity.getTenantId());
                supplierGoodsMapper.insert(supplierGoodsEntity);
            }
        }

        // 保存资质信息
        supplierQualificationsMapper.deleteByMap(deleteMap);
        if (supplierEntity.getSupplierQualificationsList() != null) {
            for (SupplierQualificationsEntity supplierQualificationsEntity : supplierEntity.getSupplierQualificationsList()) {
                supplierQualificationsEntity.setMainId(supplierEntity.getId());
                supplierQualificationsEntity.setCreateTime(new Date());
                supplierQualificationsEntity.setTenantId(supplierEntity.getTenantId());

                supplierQualificationsMapper.insert(supplierQualificationsEntity);
            }
        }

        return supplierEntity;
    }

    @Override
    @Transactional
    public IstarResponse delete(List<String> ids) {
        supplierMapper.deleteBatchIds(ids);
        QueryWrapper<SupplierGoodsEntity> wrapper = new QueryWrapper<>();
        wrapper.in("main_id", ids);
        supplierGoodsMapper.delete(wrapper);

        QueryWrapper<SupplierQualificationsEntity> qualificationsEntityQueryWrapper = new QueryWrapper<>();
        supplierQualificationsMapper.delete(qualificationsEntityQueryWrapper);
        return IstarResponse.ok("删除成功");
    }
}
