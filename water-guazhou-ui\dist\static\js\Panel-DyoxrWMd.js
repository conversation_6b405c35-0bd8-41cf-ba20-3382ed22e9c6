import{d as N,c as q,a8 as I,r as X,am as Y,o as z,bB as P,Q as A,bv as Q,g as i,h as f,p as u,i as s,bo as _,bR as O,n as p,aw as L,an as r,q as c,F as v,ax as y,bh as U,aB as j,cs as h,d2 as G,cq as J,d3 as K,d4 as g,ct as Z,C as ee}from"./index-r0dFAfgr.js";import{v as te}from"./v4-SoommWqA.js";const oe=["id"],ae={class:"panel-left-wrapper"},ne={class:"panel-wrapper"},se={key:0,class:"panel-title"},le={key:2,class:"panel-close"},ie={class:"panel-content"},re={class:"panel-right-wrapper"},de={inheritAttrs:!1},ue=N({...de,__name:"Panel",props:{telport:{default:"body"},customClass:{default:""},modal:{type:Boolean,default:!1},modalClose:{type:Boolean,default:!1},showClose:{type:Boolean,default:!0},maxMin:{type:Boolean},extra:{type:Boolean},title:{default:""},draggable:{type:Boolean,default:!0},dragout:{type:Boolean,default:!1},destroyByClose:{type:Boolean,default:!1},fade:{default:"panel-fade"},position:{default:void 0},fullContent:{type:Boolean},defaultVisible:{type:Boolean},defaultMaxmin:{default:"normal"},beforeClose:{type:Function,default:void 0},beforeOpen:{type:Function,default:void 0},afterOpen:{type:Function,default:void 0},maxminChanged:{type:Function,default:void 0}},setup($,{expose:F}){const n=$,a=q(),B=I(()=>n.destroyByClose?t.visible:!0),t=X({id:te(),visible:n.defaultVisible??!1,isMoving:!1,lastRect:{left:0,top:0,width:0,height:0},lastEvenPos:{x:0,y:0},bundary:{left:0,right:0,top:0,bottom:0},maxmin:n.defaultMaxmin??"normal"}),R=()=>{n.modalClose&&m()},k=()=>{n.beforeOpen&&n.beforeOpen(),t.visible=!0,P(()=>{n.afterOpen&&n.afterOpen()})},m=async()=>{t.isMoving=!1,n.beforeClose&&await n.beforeClose(),t.visible=!1},S=e=>{e===void 0?t.visible?m():k():e?k():m()},b=e=>{var o;t.maxmin=e,(o=n.maxminChanged)==null||o.call(n,e)},T=e=>{!n.draggable||!a.value||(C(),x(),H(e),t.isMoving=!0)},w=e=>{if(!a.value||!n.draggable)return;let o=t.lastRect.top+(e.clientY-t.lastEvenPos.y),l=t.lastRect.left+(e.clientX-t.lastEvenPos.x);o<t.bundary.top&&(o=t.bundary.top),l<=t.bundary.left&&(l=t.bundary.left),o>t.bundary.bottom&&(o=t.bundary.bottom),l>t.bundary.right&&(l=t.bundary.right),V(o,l)},E=()=>{x(),t.isMoving=!1},x=()=>{var e,o,l,d;a.value&&(t.lastRect={top:(e=a.value)==null?void 0:e.offsetTop,left:(o=a.value)==null?void 0:o.offsetLeft,width:(l=a.value)==null?void 0:l.offsetWidth,height:(d=a.value)==null?void 0:d.offsetHeight})},H=e=>{t.lastEvenPos.x=e.clientX,t.lastEvenPos.y=e.clientY},C=()=>{if(!a.value)return;const e=g("height",n.telport),o=g("width",n.telport),l=g("width",a.value),d=g("height",a.value);t.bundary.bottom=n.dragout?e:e-d,t.bundary.right=n.dragout?o:o-l,t.bundary.left=n.dragout?-l:0},V=(e,o)=>{if(a.value)switch(n.position){case"bottom":a.value.style.bottom=e-a.value.offsetHeight+"px",a.value.style.left=o+"px";break;case"left":a.value.style.top=e+"px",a.value.style.left=o+"px";break;case"right":a.value.style.top=e+"px",a.value.style.right=o+a.value.offsetWidth+"px";break;case"top":a.value.style.top=e+"px",a.value.style.left=o+"px";break;default:a.value.style.left=o+"px",a.value.style.top=e+"px";break}},M=e=>e.stopPropagation&&e.stopPropagation(),W=()=>{document.addEventListener("mouseup",()=>{t.isMoving&&E()}),document.addEventListener("mousemove",e=>{t.isMoving&&w(e)}),document.addEventListener("click",M)},D=()=>{document.removeEventListener("mouseup",E),document.removeEventListener("mousemove",w),document.removeEventListener("click",M)};return Y(()=>{var e,o;return[(e=a.value)==null?void 0:e.offsetHeight,(o=a.value)==null?void 0:o.offsetWidth]},()=>{C()}),z(()=>{C(),x(),W(),P(()=>{n.afterOpen&&n.afterOpen()})}),A(()=>{D()}),F({Open:k,Close:m,Toggle:S,toggleMaxMin:b,...Q(t)}),(e,o)=>{const l=Z;return i(),f(K,{to:n.telport||"body"},[u("div",{id:s(t).id,class:"panel-container"},[s(B)&&e.modal?_((i(),p("div",{key:0,class:L(["panel-modal",{modal:e.modal}]),onClick:R},null,2)),[[O,s(t).visible&&e.modal]]):r("",!0),c(J,{name:e.fade},{default:v(()=>[s(B)?_((i(),p("div",G({key:0},e.$attrs,{ref_key:"refPanel",ref:a,class:["panel",[e.customClass,e.$attrs.class,s(t).maxmin,e.position]]}),[u("div",ae,[y(e.$slots,"left",{},void 0,!0)]),u("div",ne,[u("div",{class:"panel-header",onMousedown:T},[e.title?(i(),p("div",se,U(e.title),1)):y(e.$slots,"header",{key:1},void 0,!0),e.showClose||e.maxMin||e.extra?(i(),p("div",le,[e.extra?y(e.$slots,"extra",{key:0},void 0,!0):r("",!0),e.maxMin?(i(),p(j,{key:1},[s(t).maxmin==="normal"||s(t).maxmin==="max"?(i(),f(l,{key:0,content:"最小化",placement:s(t).maxmin==="max"?"bottom":"top"},{default:v(()=>[c(s(h),{icon:"ep:minus",onClick:o[0]||(o[0]=d=>b("min"))})]),_:1},8,["placement"])):r("",!0),s(t).maxmin==="min"||s(t).maxmin==="normal"?(i(),f(l,{key:1,content:"最大化",placement:"top"},{default:v(()=>[c(s(h),{icon:"material-symbols:fullscreen",onClick:o[1]||(o[1]=d=>b("max"))})]),_:1})):r("",!0),s(t).maxmin==="min"||s(t).maxmin==="max"?(i(),f(l,{key:2,content:"还原",placement:s(t).maxmin==="max"?"bottom":"top"},{default:v(()=>[c(s(h),{icon:"material-symbols:fullscreen-exit",onClick:o[2]||(o[2]=d=>b("normal"))})]),_:1},8,["placement"])):r("",!0)],64)):r("",!0),e.showClose?(i(),f(l,{key:2,content:"关闭",placement:s(t).maxmin==="max"?"bottom":"top"},{default:v(()=>[c(s(h),{icon:"ep:close",onClick:m})]),_:1},8,["placement"])):r("",!0)])):r("",!0)],32),u("div",ie,[u("div",{class:L(["panel-content-wrapper",[e.fullContent?"full-content":""]])},[y(e.$slots,"default",{},void 0,!0)],2)])]),u("div",re,[y(e.$slots,"right",{},void 0,!0)])],16)),[[O,s(t).visible]]):r("",!0)]),_:3},8,["name"])],8,oe)],8,["to"])}}}),pe=ee(ue,[["__scopeId","data-v-ff3d9124"]]);export{pe as _};
