package org.thingsboard.server.dao.model.sql.zutai;

import lombok.Data;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;
import java.util.Date;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2021-06-15
 */
@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.ZUTAI_ASSETS_TABLE)
public class AssetsEntity {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.ZUTAI_ASSETS_URI)
    private String uri;

    @Column(name = ModelConstants.ZUTAI_ASSETS_SIZE)
    private String size;

    @Column(name = ModelConstants.ZUTAI_ASSETS_TYPE)
    private String type;

    @Column(name = ModelConstants.ZUTAI_ASSETS_CREATE_TIME)
    private Date createTime;

    @Column(name = ModelConstants.ZUTAI_ASSETS_PROJECT_ID)
    private String projectId;

    @Column(name = ModelConstants.ZUTAI_ASSETS_TENANT_ID)
    private String tenantId;
}
