package org.thingsboard.server.dao.smartService.system;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.dao.model.sql.smartService.system.SystemWorkOrderType;
import org.thingsboard.server.dao.sql.smartService.system.SystemWorkOrderTypeMapper;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-10-27
 */
@Slf4j
@Service
@Transactional
public class SystemWorkOrderTypeServiceImpl implements SystemWorkOrderTypeService {
    @Autowired
    private SystemWorkOrderTypeMapper systemWorkOrderTypeMapper;


    @Override
    public SystemWorkOrderType save(SystemWorkOrderType systemWorkOrderType) {
        systemWorkOrderType.setUpdateTime(new Date());
        if (StringUtils.isBlank(systemWorkOrderType.getId())) {
            systemWorkOrderType.setIsDel("0");
            systemWorkOrderType.setCreateTime(new Date());
            systemWorkOrderTypeMapper.insert(systemWorkOrderType);
        } else {
            systemWorkOrderTypeMapper.updateById(systemWorkOrderType);
        }

        return systemWorkOrderType;
    }

    @Override
    @Transactional
    public IstarResponse delete(List<String> ids) {
        // 软删除
        QueryWrapper<SystemWorkOrderType> updateWrapper = new QueryWrapper<>();
        updateWrapper.in("id", ids);
        SystemWorkOrderType systemWorkOrderType = new SystemWorkOrderType();
        systemWorkOrderType.setIsDel("1");
        systemWorkOrderTypeMapper.update(systemWorkOrderType, updateWrapper);

        // 删除子分组
        QueryWrapper<SystemWorkOrderType> wrapper = new QueryWrapper<>();
        wrapper.in("pid", ids);
        List<SystemWorkOrderType> list = systemWorkOrderTypeMapper.selectList(wrapper);
        if (list.size() > 0) {
            this.delete(list.stream().map(SystemWorkOrderType::getId).collect(Collectors.toList()));
        }
        return IstarResponse.ok("删除成功");
    }

    @Override
    public List getTree(String isDel, String tenantId) {
        QueryWrapper<SystemWorkOrderType> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("tenant_id", tenantId);
        if (StringUtils.isNotBlank(isDel)) {

            queryWrapper.eq("is_del", isDel);
        }
        queryWrapper.orderByDesc("create_time");
        List<SystemWorkOrderType> systemWorkOrderTypes = systemWorkOrderTypeMapper.selectList(queryWrapper);

        // 获取根节点
        List<SystemWorkOrderType> root = systemWorkOrderTypes.stream().filter(a -> StringUtils.isBlank(a.getPid())).collect(Collectors.toList());

        for (SystemWorkOrderType systemWorkOrderType : root) {
            this.buildTree(systemWorkOrderType, systemWorkOrderTypes);
        }
        return root;
    }

    @Override
    public IstarResponse deleteHard(List<String> ids) {
        int i = systemWorkOrderTypeMapper.deleteBatchIds(ids);
        if (i > 0) {
            return IstarResponse.ok("删除成功");
        }
        return IstarResponse.error("删除失败");
    }

    private void buildTree(SystemWorkOrderType parent, List<SystemWorkOrderType> systemWorkOrderTypes) {
        for (SystemWorkOrderType systemWorkOrderType : systemWorkOrderTypes) {
            if (parent.getId().equals(systemWorkOrderType.getPid())) {
                buildTree(systemWorkOrderType, systemWorkOrderTypes);
                parent.getChildren().add(systemWorkOrderType);
            }
        }
    }
}
