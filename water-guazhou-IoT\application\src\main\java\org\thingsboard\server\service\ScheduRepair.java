/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.repair.Repair;
import org.thingsboard.server.common.data.repair.RepairType;
import org.thingsboard.server.dao.repair.RepairService;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class ScheduRepair {

    @Autowired
    private RepairService repairService;

    @Scheduled(fixedRate = 60000)
    public void updateRepair() {
        // 查询所有状态为待维修的记录
        List<Repair> repairList = repairService.findByStatus(RepairType.WAITING);
        
        // 校验时间
        List<String> needUpdateRepairIdList = repairList.stream()
                .filter(repair -> repair.getRepairStartTime() < System.currentTimeMillis())
                .map(repair -> UUIDConverter.fromTimeUUID(repair.getId().getId()))
                .collect(Collectors.toList());
        if (needUpdateRepairIdList.isEmpty()) {
            return;
        }
        repairService.updateStatus(needUpdateRepairIdList, RepairType.LOADING.getCode());
    }
}
