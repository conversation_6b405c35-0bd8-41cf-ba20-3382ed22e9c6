import{d as J,a8 as L,bY as P,ay as Y,i as d,g as n,n as r,aB as s,h as l,bg as C,F as y,p as M,bh as I,aw as b,aJ as A,an as h,fz as Z,bZ as K,b_ as O,j as Q,r as R,cV as U,C as W}from"./index-r0dFAfgr.js";import{i as X}from"./config-DncLSA-r.js";import"./index-DeAQQ1ej.js";const _=J({__name:"MenuItem",props:{item:{},icon:{},authority:{type:<PERSON>olean},popperAppendToBody:{type:Boolean}},emits:["click"],setup($){const u=$,f=L(()=>{var e,o;return u.authority===!1?!0:P((o=(e=u.item)==null?void 0:e.meta)==null?void 0:o.roles)});return(e,o)=>{var c,z,S,w,E,T,D,F,N,V,G,H,j,q;const k=Y("MenuItem",!0),g=Z,B=K,v=O;return!((c=e.item)!=null&&c.hidden)&&!((S=(z=e.item)==null?void 0:z.meta)!=null&&S.hidden)&&d(f)?(n(),r(s,{key:0},[(w=e.item.children)!=null&&w.length?(n(),r(s,{key:0},[e.item.children.length===1&&!e.item.alwaysShow?(n(),l(k,{key:0,item:e.item.children[0],icon:((E=e.item.children[0].meta)==null?void 0:E.icon)||e.icon,"popper-append-to-body":e.popperAppendToBody,onClick:o[0]||(o[0]=t=>e.$emit("click",t))},null,8,["item","icon","popper-append-to-body"])):(n(),r(s,{key:1},[e.item.isGroup?(n(),l(g,{key:0},C({default:y(()=>[(n(!0),r(s,null,A(e.item.children,(t,p)=>{var i,a;return n(),r(s,null,[!t.hidden&&!((i=t==null?void 0:t.meta)!=null&&i.hidden)?(n(),l(k,{key:p,item:t,icon:((a=t.meta)==null?void 0:a.icon)||e.icon,"popper-append-to-body":e.popperAppendToBody,onClick:o[1]||(o[1]=m=>e.$emit("click",m))},null,8,["item","icon","popper-append-to-body"])):h("",!0)],64)}),256))]),_:2},[(T=e.item)!=null&&T.title||(F=(D=e.item)==null?void 0:D.meta)!=null&&F.title?{name:"title",fn:y(()=>{var t,p,i,a,m;return[M("span",{class:b("title-text "+(((p=(t=e.item)==null?void 0:t.meta)==null?void 0:p.icon)||e.icon||""))},I(((i=e.item)==null?void 0:i.title)||((m=(a=e.item)==null?void 0:a.meta)==null?void 0:m.title)),3)]}),key:"0"}:void 0]),1024)):(n(),l(B,{key:1,index:e.item.path,"popper-append-to-body":e.popperAppendToBody},C({default:y(()=>[(n(!0),r(s,null,A(e.item.children,(t,p)=>{var i,a;return n(),r(s,null,[!t.hidden&&!((i=t==null?void 0:t.meta)!=null&&i.hidden)?(n(),l(k,{key:p,item:t,icon:((a=t.meta)==null?void 0:a.icon)||e.icon,"popper-append-to-body":e.popperAppendToBody,onClick:o[2]||(o[2]=m=>e.$emit("click",m))},null,8,["item","icon","popper-append-to-body"])):h("",!0)],64)}),256))]),_:2},[(N=e.item)!=null&&N.title||(G=(V=e.item)==null?void 0:V.meta)!=null&&G.title?{name:"title",fn:y(()=>{var t,p,i,a,m;return[M("span",{class:b("title-text "+(((p=(t=e.item)==null?void 0:t.meta)==null?void 0:p.icon)||e.icon||""))},I(((i=e.item)==null?void 0:i.title)||((m=(a=e.item)==null?void 0:a.meta)==null?void 0:m.title)),3)]}),key:"0"}:void 0]),1032,["index","popper-append-to-body"]))],64))],64)):(n(),l(v,{key:1,index:e.item.path,route:e.item.component?e.item:{name:"NotFound"},onClick:o[3]||(o[3]=t=>e.$emit("click",e.item))},C({_:2},[(H=e.item)!=null&&H.title||(q=(j=e.item)==null?void 0:j.meta)!=null&&q.title?{name:"title",fn:y(()=>{var t;return[M("span",{class:b("title-text "+(e.item.meta.icon||e.icon||""))},I(((t=e.item)==null?void 0:t.title)||e.item.meta.title),3)]}),key:"0"}:void 0]),1032,["index","route"]))],64)):h("",!0)}}}),x=J({__name:"HorizontalMenu",emits:["click"],setup($){const u=Q(),f=R({activeIndex:"1",menus:X()});return(e,o)=>{const k=_,g=U;return n(),l(g,{"default-active":d(f).activeIndex,class:b(["horizontal-menu",d(u).isDark?"darkblue":""]),mode:"horizontal","show-timeout":30,"menu-trigger":"click","unique-opened":!0,"background-color":d(u).isDark?"transparent":"#fff","text-color":d(u).isDark?"#fff":"#000"},{default:y(()=>[(n(!0),r(s,null,A(d(f).menus,(B,v)=>(n(),l(k,{key:v,authority:!1,item:B,"popper-append-to-body":!1,onClick:o[0]||(o[0]=c=>e.$emit("click",c))},null,8,["item"]))),128))]),_:1},8,["default-active","class","background-color","text-color"])}}}),oe=W(x,[["__scopeId","data-v-d9daa166"]]);export{oe as default};
