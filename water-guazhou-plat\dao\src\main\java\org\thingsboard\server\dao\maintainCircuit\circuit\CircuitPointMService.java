package org.thingsboard.server.dao.maintainCircuit.circuit;

import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.maintainCircuit.circuit.CircuitPointM;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.List;

/**
 * 班组
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-08-24
 */
public interface CircuitPointMService {
    PageData getList(String name, int page, int size, String tenantId);

    CircuitPointM getDetail(String mainId);

    CircuitPointM save(CircuitPointM circuitPointM);

    IstarResponse delete(List<String> ids);

    List<CircuitPointM> findAll();

    CircuitPointM getById(String pointId);
}
