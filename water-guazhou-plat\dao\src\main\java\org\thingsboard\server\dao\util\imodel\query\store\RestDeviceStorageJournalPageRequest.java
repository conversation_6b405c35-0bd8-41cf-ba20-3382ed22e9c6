package org.thingsboard.server.dao.util.imodel.query.store;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.store.MainRestDeviceStorageJournal;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;

import java.util.Date;

@Getter
@Setter
public class RestDeviceStorageJournalPageRequest extends AdvancedPageableQueryEntity<MainRestDeviceStorageJournal, RestDeviceStorageJournalPageRequest> {
    // 设备编码
    private String serialId;

    // 设备标签
    private String deviceLabelCode;

    // 设备名称
    private String name;

    // 规格型号
    private String model;

    // 所在货架
    private String shelvesId;

    // 设备类型
    private String deviceTypeId;

    // [经过逻辑修改已与原字段意义相悖]是否在库
    private Boolean inStoreOnly;

    // 安装区域Id
    private String areaId;

    // 安装位置
    private String address;

    // 仓库Id
    private String storeId;

    // 最后保养时间
    private String lastMaintainanceTimeFrom;

    // 最后保养时间
    private String lastMaintainanceTimeTo;

    public Date getLastMaintainanceTimeFrom() {
        return toDate(lastMaintainanceTimeFrom);
    }

    public Date getLastMaintainanceTimeTo() {
        return toDate(lastMaintainanceTimeTo);
    }

}
