import{w as k}from"./Point-WxyopZva.js";import"./index-r0dFAfgr.js";import{G as A,a as x}from"./area-Bpl-8n1R.js";import{a as L,c as m,s as l,g as w,b as H}from"./FeatureHelper-Da16o0mu.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./MapView-DaoQedLH.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import{g as b}from"./LayerHelper-Cn-iiqxI.js";import"./project-DUuzYgGl.js";import{u as R}from"./useWaterPoint-Bv0z6ym6.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./geometryEngineBase-BhsKaODW.js";const X=P=>{const e={},p=R(P),f=()=>{var i,r;(i=e.graphicsLayer)==null||i.removeAll(),(r=e.keyPointLayer)==null||r.removeAll()},G=async(i,r,t)=>{var d,u,v;if(!i||r===void 0)return;e.view=i,e.graphicsLayer=b(e.view,{id:"district-area",title:"区域/路线"}),f();const a=await A(r);if(!a.data.data)return;const o=JSON.parse(a.data.data);if(!o.geometry)return;const s=o.geometry.rings?"polygon":o.geometry.paths?"polyline":"";if(!s)return;const y=L(s,s==="polygon"?o.geometry.rings:o.geometry.paths,o.geometry.spatialReference),c=m({geometry:y,symbol:l(s),attributes:{areaId:r}});if((d=e.graphicsLayer)==null||d.add(c),t!=null&&t.goto&&w(e.view,c,{avoidHighlight:!t.highlight,ratio:t.ratio}),t!=null&&t.showKeyPoint&&h(r),!((u=o.bufferGeometry)!=null&&u.rings))return;const n=L("polygon",o.bufferGeometry.rings,o.bufferGeometry.spatialReference),g=m({geometry:n,symbol:l((n==null?void 0:n.type)||"polygon",{color:[0,255,0,.1],outlineWidth:1,outlineColor:"#00ff00"})});(v=e.graphicsLayer)==null||v.add(g)},h=i=>{e.keyPointLayer=b(e.view,{id:"key-point",title:"关键点"}),x({areaId:i}).then(r=>{var a;const t=(a=r.data.data)==null?void 0:a.data;t==null||t.map(o=>{var n,g;const s=new k({longitude:o.lon,latitude:o.lat,spatialReference:(n=e.view)==null?void 0:n.spatialReference}),y=m({geometry:s,symbol:l("point",{outlineWidth:1,outlineColor:"#00ffff",color:"#ff0000"}),attributes:{...o}}),c=m({geometry:s,symbol:l("text",{text:o.name,color:"#ff0000",yOffset:-25}),attributes:{...o}});(g=e.keyPointLayer)==null||g.addMany([y,c])})}).catch(r=>{console.log(r.message)})};return{removeAll:f,add:G,destroy:()=>{var i,r,t,a;e.graphicsLayer&&((r=(i=e.view)==null?void 0:i.map)==null||r.remove(e.graphicsLayer)),e.keyPointLayer&&((a=(t=e.view)==null?void 0:t.map)==null||a.remove(e.keyPointLayer))},refreshKeyPoint:h,extentTo:(i,r)=>{var a,o,s,y;const t=i==="area"?(o=(a=e.graphicsLayer)==null?void 0:a.graphics)==null?void 0:o[0]:(y=(s=e.keyPointLayer)==null?void 0:s.graphics)==null?void 0:y.find(c=>{var n;return((n=c.attributes)==null?void 0:n.id)===r});t&&(w(e.view,t,{avoidHighlight:!0}),r!==void 0&&(p.removeAll(),p.add(e.view,{id:r,point:H(t.geometry)})))},removeHighlight:()=>{p.removeAll()}}};export{X as u};
