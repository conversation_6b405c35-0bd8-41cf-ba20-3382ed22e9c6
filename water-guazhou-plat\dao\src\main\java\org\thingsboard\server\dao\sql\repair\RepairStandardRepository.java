package org.thingsboard.server.dao.sql.repair;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.thingsboard.server.dao.model.sql.RepairStandardEntity;

import java.util.List;

public interface RepairStandardRepository extends JpaRepository<RepairStandardEntity, String> {

    @Query("SELECT new RepairStandardEntity(rs.id, rs.name, rs.deviceType, rs.remark, rs.creator, rs.createTime, rs.tenantId) " +
            "FROM RepairStandardEntity rs " +
            "WHERE rs.tenantId = ?2 AND rs.name LIKE %?1% AND rs.deviceType LIKE %?3%")
    Page<RepairStandardEntity> findList(String name, String tenantId, String deviceType, Pageable pageable);

    @Query("SELECT new RepairStandardEntity(rs.id, rs.name, rs.deviceType, rs.remark, rs.creator, rs.createTime, rs.tenantId) " +
            "FROM RepairStandardEntity rs " +
            "WHERE rs.tenantId = ?2 AND rs.name LIKE %?1%")
    List<RepairStandardEntity> findAll(String name, String tenantId);
}
