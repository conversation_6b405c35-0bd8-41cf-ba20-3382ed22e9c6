import CoordinateConversion from '@arcgis/core/widgets/CoordinateConversion'

export const useCoordinate = () => {
  let ccWidget: __esri.CoordinateConversion | undefined
  const init = (view: __esri.MapView, widgetPosition?: string) => {
    ccWidget = new CoordinateConversion({
      view
    })
    ccWidget && view.ui?.add(ccWidget, widgetPosition || 'bottom-left')
    return ccWidget
  }
  const destroy = () => {
    ccWidget?.destroy()
  }
  onBeforeUnmount(() => {
    destroy()
  })
  return {
    init
  }
}
export default useCoordinate
