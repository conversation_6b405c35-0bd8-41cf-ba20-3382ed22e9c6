package org.thingsboard.server.dao.construction.device;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.smartOperation.device.SoDeviceType;
import org.thingsboard.server.dao.util.imodel.query.GeneralDeviceTypeService;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.device.SoDeviceTypePageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.device.SoDeviceTypeSaveRequest;

import java.util.List;

public interface SoDeviceTypeService extends GeneralDeviceTypeService {
    /**
     * 分页条件查询工程设备类型
     *
     * @param request 分页查询条件
     * @return 分页查询结果
     */
    IPage<SoDeviceType> findAllConditional(SoDeviceTypePageRequest request);

    /**
     * 保存工程设备类型
     *
     * @param entity 实体信息
     * @return 保存好的实体
     */
    SoDeviceType save(SoDeviceTypeSaveRequest entity);

    /**
     * 增量修改
     *
     * @param entity 实体信息
     * @return 是否修改成功
     */
    boolean update(SoDeviceType entity);

    /**
     * 删除
     *
     * @param id 唯一标识
     * @return 是否删除成功
     */
    boolean delete(String id);

    /**
     * 获取父级id
     *
     * @param id 唯一标识
     * @return 父级id
     */
    String getParentId(String id);

    /**
     * 是否允许删除
     *
     * @param id       唯一标识
     * @param tenantId 客户id
     * @return 是否允许删除
     */
    boolean canBeDelete(String id, String tenantId);

    /**
     * 查询指定客户的设备类型树
     *
     * @param tenantId 客户id
     * @return 设备类型树
     */
    List<SoDeviceType> findAllStructure(String tenantId);

    /**
     * 通过serialId判断是否已存在
     *
     * @param serialId 序列号
     * @param tenantId 客户id
     * @return 是否已存在
     */
    boolean existsBySerialId(String serialId, String tenantId);

    /**
     * 获取设备类型的当前深度
     *
     * @param serialId 序列号
     * @param tenantId 客户id
     * @return 当前深度
     */
    int getDepthBySerialId(String serialId, String tenantId);

    /**
     * 通过序列号获取id
     *
     * @param serialId 序列号
     * @param tenantId 客户id
     * @return id
     */
    String getIdBySerialId(String serialId, String tenantId);

}
