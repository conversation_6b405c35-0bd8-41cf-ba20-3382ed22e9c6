package org.thingsboard.server.controller.maintainCircuit.maintain;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.maintainCircuit.maintain.MaintainStandardService;
import org.thingsboard.server.dao.model.sql.maintainCircuit.maintain.MaintainStandard;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.List;

/**
 * 班组
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-09-07
 */
@RestController
@RequestMapping("api/maintain/standard/m")
public class MaintainStandardController extends BaseController {

    @Autowired
    private MaintainStandardService maintainStandardService;

    @GetMapping
    public IstarResponse getList(@RequestParam(required = false, defaultValue = "") String deviceTypeId, @RequestParam(required = false, defaultValue = "") String keywords,
                                 int page, int size) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());

        return IstarResponse.ok(maintainStandardService.getList(deviceTypeId, keywords, page, size, tenantId));
    }

    @PostMapping
    public IstarResponse save(@RequestBody MaintainStandard maintainStandard) throws ThingsboardException {
        maintainStandard.setCreator(UUIDConverter.fromTimeUUID(getCurrentUser().getUuidId()));
        maintainStandard.setTenantId(UUIDConverter.fromTimeUUID(getTenantId().getId()));
        return IstarResponse.ok(maintainStandardService.save(maintainStandard));
    }

    @DeleteMapping
    public IstarResponse delete(@RequestBody List<String> ids) {
        return maintainStandardService.delete(ids);
    }
}
