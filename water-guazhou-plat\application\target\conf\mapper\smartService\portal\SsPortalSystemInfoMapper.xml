<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartService.portal.SsPortalSystemInfoMapper">
    <sql id="Base_Column_List">
        <!--@sql select-->
        id,
        name,
        domain,
        record_no,
        tenant_id
        <!--@sql from ss_portal_system_info -->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.smartService.portal.SsPortalSystemInfo">
        <result column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="domain" property="domain"/>
        <result column="record_no" property="recordNo"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <select id="getByTenantId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ss_portal_system_info
        where tenant_id = #{tenantId}
    </select>

    <update id="updateFully">
        update ss_portal_system_info
        set name      = #{name},
            domain    = #{domain},
            record_no = #{recordNo}
        where id = #{id}
    </update>

    <insert id="save">
        INSERT INTO ss_portal_system_info(id,
                                          name,
                                          domain,
                                          record_no,
                                          tenant_id)
        VALUES (#{id},
                #{name},
                #{domain},
                #{recordNo},
                #{tenantId})
        on conflict(tenant_id) do update
            set name      = #{name},
                domain    = #{domain},
                record_no = #{recordNo}
    </insert>
</mapper>