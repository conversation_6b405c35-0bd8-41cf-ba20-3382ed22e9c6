<template>
  <SLDialog
    v-if="slDialogConfig.visible"
    :config="slDialogConfig"
    @close="$emit('close')"
  >
    <el-form
      ref="ruleForm"
      :rules="rules"
      :label-width="config.labelWidth || '120px'"
      label-position="top"
      :model="dataForm"
      class="dialogform addOrUpdateDialog"
    >
      <template v-for="item in config.columns">
        <!-- 数据项（可自定义新增属性，可能需要改typescript） -->
        <el-form-item
          v-if="item.type !== 'none'"
          :key="item.key"
          :label="item.label"
          :prop="item.key"
        >
          <!-- 输入框 -->
          <el-input
            v-if="item.type === 'input'"
            v-model="dataForm[item.key]"
            :disabled="item.disabled"
            :placeholder="'请输入' + item.label"
          >
            <template #suffix>
              <i
                v-if="item.unit"
                class="inputUnit"
                style="margin-right: 10px"
                >{{ item.unit }}</i
              >
            </template>
          </el-input>
          <!-- 密码输入框 -->
          <el-input
            v-if="item.type === 'password'"
            v-model="dataForm[item.key]"
            type="password"
            :disabled="item.disabled"
            :placeholder="'请输入' + item.label"
          ></el-input>
          <!-- 数字输入框 -->
          <el-input-number
            v-if="item.type === 'input-number'"
            v-model="dataForm[item.key]"
            :disabled="item.disabled"
            :placeholder="'请输入'"
          >
            <template #suffix>
              <i
                v-if="item.unit"
                class="inputUnit"
                style="margin-right: 10px"
                >{{ item.unit }}</i
              >
            </template>
          </el-input-number>
          <!-- 输入框 只能输入数字 -->
          <el-input
            v-else-if="item.type === 'number'"
            v-model="dataForm[item.key]"
            :disabled="item.disabled"
            :placeholder="item.label"
            onkeyup="value=value.replace(/[^\d]/g,'')"
            style="width: 100%"
          >
            <template #suffix>
              <i
                v-if="item.unit"
                class="inputUnit"
                style="margin-right: 10px"
                >{{ item.unit }}</i
              >
            </template>
          </el-input>
          <!-- 多行文本框 -->
          <el-input
            v-else-if="item.type === 'textarea'"
            v-model="dataForm[item.key]"
            :disabled="item.disabled"
            type="textarea"
            resize="none"
            :rows="item.rows as any"
            :placeholder="'请输入' + item.label"
          ></el-input>
          <!-- 下拉框 -->
          <el-select
            v-else-if="item.type === 'select'"
            v-model="dataForm[item.key]"
            :disabled="item.disabled"
            :multiple="item.multiple"
            :multiple-limit="item.multipleLimit"
            :default-first-option="item.defaultFirst"
            collapse-tags
            :allow-create="item.allowCreate"
            :placeholder="'请选择' + item.label"
            style="width: 100%"
            :filterable="item.search"
            @change="item.handleChange"
          >
            <el-option
              v-for="option of item.options"
              :key="option.value as any"
              :value="option.value"
              :label="option.label"
            ></el-option>
          </el-select>
          <!-- 单选框 -->
          <el-radio-group
            v-if="item.type === 'radio'"
            v-model="dataForm[item.key]"
            :disabled="item.disabled"
            @change="item.handleChange"
          >
            <el-radio
              v-for="option in item.options"
              :key="option.value as any"
              style="margin-bottom: 0"
              :label="option.value as any"
            >
              {{ option.label }}
            </el-radio>
          </el-radio-group>
          <el-switch
            v-if="item.type === 'switch'"
            v-model="dataForm[item.key]"
            inline-prompt
            :disabled="item.disabled"
            :active-color="item.activeColor || '#1DCF8E'"
            :inactive-color="item.inActiveColor || '#3A3E56'"
            :active-text="item.activeText || '是'"
            :inactive-text="item.inActiveText || '否'"
            :active-value="item.activeValue || true"
            :inactive-value="item.inActiveValue || false"
          ></el-switch>
          <!-- 日期选择器 -->
          <el-date-picker
            v-if="item.type === 'date'"
            v-model="dataForm[item.key]"
            :disabled="item.disabled"
            :disabled-date="item.disabledDate"
            :type="item.dateType as any"
            class="date-picker-input"
            range-separator="至"
            start-placeholder="开始日期"
            placeholder="请选择"
            end-placeholder="结束日期"
            style="width: 100%"
          ></el-date-picker>
          <!-- 时间选择器 -->
          <el-time-picker
            v-else-if="item.type === 'time'"
            v-model="dataForm[item.key]"
            :disabled="item.disabled"
            :is-range="item.range"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            :format="item.format"
            :value-format="item.valFormat"
            placeholder="请选择"
            style="width: 100%"
            @change="item.handleChange"
          ></el-time-picker>
          <!-- 联级选择器 -->
          <el-cascader
            v-if="item.type === 'cascader'"
            v-model="dataForm[item.key]"
            :disabled="item.disabled"
            :options="item.cascaderOptions as any"
            clearable
            collapse-tags
            filterable
            style="width: 100%"
            :props="item.props"
            @change="item.handleChange"
          ></el-cascader>
          <!-- 图片上传 -->
          <el-upload
            v-if="item.type === 'image'"
            class="avatar-uploader"
            :disabled="item.disabled"
            :action="item.imgActionUrl || imgActionUrl"
            :headers="item.headers || headers"
            :show-file-list="false"
            :on-success="
              (res, file) => handleUploadSuccess(res, file, item.key)
            "
            :before-upload="beforeAvatarUpload"
          >
            <img
              v-if="dataForm[item.key]"
              :src="dataForm[item.key]"
              class="avatar"
            />
            <el-icon v-else class="el-icon-plus avatar-uploader-icon">
              <Plus />
            </el-icon>
            <!-- <i  class="el-icon-plus avatar-uploader-icon"></i> -->
          </el-upload>
          <!-- 文件上传 -->
          <div v-if="item.type === 'file'" class="fileUpload">
            <el-upload
              class="upload-demo"
              :disabled="item.disabled"
              :action="item.fileActionUrl || fileActionUrl"
              :headers="item.headers || headers"
              :show-file-list="false"
              :on-success="
                (res, file) => handleUploadSuccess(res, file, item.key)
              "
              :before-upload="beforeFileUpload"
            >
              <el-button size="small" type="primary"> 点击上传 </el-button>
            </el-upload>
            <div v-if="dataForm[item.key]" class="fileBox">
              {{ uploadFileName }}
              <span @click="dataForm[item.key] = ''">×</span>
            </div>
          </div>
          <SLUploader
            v-if="item.type === 'image_sl'"
            v-model="dataForm[item.key]"
            :limit="item.limit"
            :disabled="item.disabled"
            :url="item.url || fileActionUrl"
            :multiple="item.multiple"
          >
          </SLUploader>
          <!-- 用户选择 -->
          <ChooseUserByRole
            v-if="item.type === 'userByRole'"
            width="100%"
            height="48px"
            @checkUsers="(users) => checkUsers(users, item.key)"
          />
          <!-- 优先过的Amap组件 -->
          <!-- 不再限制key的名称 -->
          <!-- 传入值为字符串，可以为序列化（JSON.stringify）后的经纬度数组，也可以是用','分隔的字符串 -->
          <!-- 输入框修改值后enter跳转位置 -->
          <!-- 拖拽地图同步更新表单绑定值 -->
          <div v-if="item.type === 'location'" class="amap-wrapper">
            <SLAmap
              v-model="dataForm[item.key]"
              :disabled="item.disabled"
              :init-center-mark="true"
              :required="item.required"
              :result-type="item.returnType"
            />
          </div>

          <!-- 表单附加信息  选择框下面的提示文字 -->
          <p v-if="item.message" :style="item.messageStyle">
            {{ item.message }}
          </p>
        </el-form-item>
      </template>
    </el-form>
  </SLDialog>
</template>

<script lang="ts">
import {
  reactive,
  toRefs,
  onBeforeMount,
  onMounted,
  ref,
  computed,
  defineComponent,
  PropType,
  nextTick
} from 'vue';
import { cloneDeep } from 'lodash-es';
import { Plus } from '@element-plus/icons-vue';
import request from '@/plugins/axios';
import { useAppStore, useUserStore } from '@/store';
import useSLDialog from '../SLDialog/useSLDialog';
import { DateFormatter } from '@/utils/DateFormatter';
import { SLMessage } from '@/utils/Message';
import { IElForm } from '@/common/types/element-plus';

export default defineComponent({
  name: 'AddUpdateDialog',
  components: { Plus },
  props: {
    config: {
      type: Object as PropType<AOUConfig>,
      default: () => {
        //
      }
    }
  },
  emits: ['refreshData', 'close'],
  setup(props, ctx) {
    const appStore = useAppStore()
    const userStore = useUserStore()
    const { slDialogConfig } = useSLDialog(
      async () => await submit(),
      async () => await props.config.close()
    );
    slDialogConfig.value.title = props.config.title || '';
    slDialogConfig.value.width = props.config.width || '30%';
    slDialogConfig.value.contentHeight = props.config.height || '600px';
    slDialogConfig.value.contentMaxHeight = props.config.maxHeight || '600px';
    slDialogConfig.value.visible = true;
    // 定义状态
    const state = reactive<{
      imgActionUrl: string;
      fileActionUrl: string;
      uploadFileName: string;
      headers: any;
      dataForm: any;
    }>({
      imgActionUrl: '',
      fileActionUrl: '',
      uploadFileName: '',
      headers: {},
      dataForm: { id: null }
    });

    // ruleForm组建实例
    const ruleForm = ref<IElForm>();

    // 定义标点rule  根据每个column.rule
    const rules = computed(() => {
      const rules: any = {};
      (props.config.columns || []).forEach((column) => {
        rules[column.key] = column.rules;
      });
      return rules;
    });

    // 保存 save
    const submit = () => {
      ruleForm.value?.validate(async (valid: any) => {
        if (!valid) {
          return false;
        }

        // 默认设置为新增
        let successText = '新增成功';
        let submitUrl = props.config.addUrl;
        // 提交数据中若有 ID  则判断为修改
        if (state.dataForm.id) {
          if (!props.config.submit) {
            if (!props.config.editUrl) {
              // 没有配置提交方法也没有配置路径则返回
              console.log('没有配置请求路径或方法');
              props.config.close();
              return;
            }
          }

          successText = '修改成功';
          submitUrl = props.config.editUrl;
        } else if (!props.config.submit) {
          if (!props.config.addUrl) {
            props.config.close();
            return;
          }
        }

        // additionalInfo JSON对象字符串初始化
        const aInfo = {};

        // 提取提交参数对象 （深克隆 不影响表单正常显示）
        let queryParams = cloneDeep({ ...state.dataForm });
        // 遍历每个Key
        for (const item of props.config.columns) {
          // 格式化日期选择器值(这个改一下比较好)
          if (item.type === 'date') {
            let formatter: string | undefined = '';
            switch (item.dateType) {
              case 'date':
                formatter = 'Y-M-D';
                break;
              default:
                formatter = undefined;
                break;
            }
            if (queryParams[item.key] instanceof Array) {
              queryParams[item.key] = queryParams[item.key].map((item) => {
                return DateFormatter(item, formatter);
              });
            } else {
              queryParams[item.key] = DateFormatter(
                queryParams[item.key],
                formatter
              );
            }
          }

          // 判断column.aInfo是否为true
          // 将aInfo的选项添加到aInfo对象中
          if (item.aInfo) {
            aInfo[item.key] = queryParams[item.key];
            delete queryParams[item.key];
          }
        }

        // 参数对象结合外部额外参数
        queryParams = {
          ...queryParams,
          ...(props.config.externalParams || {})
        };
        // 如果aInfo中有任意值 设置additionalInfo
        if (Object.values(aInfo).length > 0) {
          queryParams.additionalInfo = JSON.stringify(aInfo);
        }
        // 在表单提交前 执行外部最后的参数自定义修改函数
        if (props.config.setSubmitParams) {
          queryParams = props.config.setSubmitParams(queryParams);
        }

        if (props.config.submit) {
          const res = await props.config.submit(queryParams);
          if (res.status === 200) {
            SLMessage.success(successText);

            // 请求完成关闭窗口
            ctx.emit('refreshData');
            props.config.close();
          } else {
            SLMessage.error('请求失败');
          }
        } else {
          // post请求提交
          try {
            const res = await request({
              url: submitUrl,
              method: 'post',
              data: queryParams
            });
            if (res.status === 200) {
              SLMessage.success(successText);

              // 请求完成关闭窗口
              ctx.emit('refreshData');
              props.config.close();
            } else {
              SLMessage.error('请求失败');
            }
          } catch (err: any) {
            SLMessage.error(err.message || err.data.message);
          }
        }
      });
    };

    // 图片上传成功，保存图片url
    const handleUploadSuccess = (res: string, file: any, key: string) => {
      console.log(res, 'handleUploadSuccess');
      state.dataForm[key] = res;

      // dataForm重新赋值 刷新图片
      // state.dataForm = { ...state.dataForm }
    };

    const beforeAvatarUpload = (file: any) => {
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png';
      const isLt2M = file.size / 1024 / 1024 < 2;

      if (!isJPG) {
        SLMessage.error('上传图片只能是 JPG/PNG 格式!');
      }
      if (!isLt2M) {
        SLMessage.error('上传图片大小不能超过 2MB!');
      }
      return isJPG && isLt2M;
    };

    // 暂存文件名
    const beforeFileUpload = (file: any) => {
      state.uploadFileName = file.name;
      return true;
    };

    onMounted(async () => {
      nextTick(() => {
        ruleForm.value?.clearValidate();
      });
    });

    // 接收选择的用户数组 并赋给dataForm (字符串。‘，’分割)
    const checkUsers = (users: any[], key: string) => {
      state.dataForm[key] = users.map((user) => user.id).join(',');
      ruleForm.value?.validate(async (valid: any) => {
        if (!valid) {
          return false;
        }
      });
    };

    onBeforeMount(() => {
      // 初始化图片 文件上传url
      state.imgActionUrl = appStore.actionUrl + 'file/api/upload/image';
      state.fileActionUrl = appStore.actionUrl + 'file/api/upload/file';
      state.headers['X-Authorization'] = 'Bearer ' + userStore.token;
      init();
    });

    // 初始化默认值
    const init = () => {
      props.config.defaultValue &&
        (state.dataForm = cloneDeep(props.config.defaultValue));
      // 如果有open属性 则调用自定义初始化函数
      if (props.config.open) props.config.open();
    };

    return {
      ...toRefs(state),
      rules,
      submit,
      handleUploadSuccess,
      beforeAvatarUpload,
      beforeFileUpload,
      checkUsers,
      ruleForm,
      slDialogConfig
    };
  }
});
</script>
<style lang="scss" scoped>
.amap-wrapper {
  width: 100%;
  height: 250px;
}
</style>

<style lang="scss">
.addOrUpdateDialog {
  .avatar-uploader .el-upload {
    border: 1px dashed #404354 !important;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #3d4050 !important;
    background: #eee;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 256px;
    height: 156px;
    line-height: 156px;
    text-align: center;
  }
  .avatar {
    width: 256px;
    height: 156px;
    display: block;
  }
  // .el-input-number__decrease {
  //   width: 46px !important;
  //   height: 46px !important;
  //   line-height: 46px !important;
  // }

  // .el-input-number__increase {
  //   width: 46px !important;
  //   height: 46px !important;
  //   line-height: 46px !important;
  // }
}
</style>
>

<style lang="scss" scoped>
// @import 'src/assets/css/amapSearchStyle.scss';
.searchInput {
  z-index: 1000;
  position: relative;
}
.location-label {
  color: #9097c0;
}
.location-input {
  width: 170px;
}
.message-text {
  margin: 10px 0 12px 20px;
  color: #39b01c;
  line-height: initial;
}
.get-location-box {
  width: 100%;
  height: 250px;
}

.fileUpload {
  display: flex;
  .upload-demo {
    margin-right: 16px;
  }
  .fileBox {
    padding: 0 16px;
    display: inline-block;
    overflow: hidden; //超出的文本隐藏
    text-overflow: ellipsis; //溢出用省略号显示
    white-space: nowrap; //溢出不换行
    max-width: 240px;
    height: 40px;
    line-height: 40px;
    background-color: #fdf6ec;
    border-color: #faecd8;
    color: #e6a23c;
    position: relative;
    > span {
      display: block;
      position: absolute;
      width: 20px;
      height: 20px;
      line-height: 20px;
      text-align: center;
      top: 0;
      right: 0;
      cursor: pointer;
    }
  }
}
</style>
