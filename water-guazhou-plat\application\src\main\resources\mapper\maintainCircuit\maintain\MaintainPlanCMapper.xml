<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.maintainCircuit.maintain.MaintainPlanCMapper">

    <select id="getList" resultType="org.thingsboard.server.dao.model.sql.maintainCircuit.maintain.MaintainPlanC">

        select a.*, b.model as model, b.serial_id as serialId, b.name, b.type_id as typeId, area.name as installAddressName, settle.address detailInstallAddressName
        from tb_device_maintain_plan_c a
                 left join device_storage_journal journal on a.device_label_code = journal.device_label_code and journal.tenant_id = a.tenant_id
                 left join m_device b on journal.serial_id = b.serial_id and b.tenant_id = a.tenant_id
                 left join device_settle_journal settle on a.device_label_code = settle.device_label_code and settle.tenant_id = a.tenant_id
                 left join tb_area area on settle.install_address_id = area.id and area.tenant_id = a.tenant_id
        where a.main_id = #{mainId}
        order by a.create_time desc

    </select>


    <select id="getMaintainList" resultType="java.util.Map">
        select c.name, c.execution_days as "limitDays", c.interval_days as "cycleDays", c.user_id as "userId", c.start_time as "startTime", c.execution_num as "executionNum"
        from tb_device_maintain_plan_c a
                 left join device_storage_journal b on a.device_label_code = b.device_label_code
                 left join tb_device_maintain_plan_m c on a.main_id = c.id
        where a.device_label_code = #{deviceLabelCode}
        offset (#{page} - 1) * #{size} limit #{size}
    </select>

    <select id="getMaintainListCount" resultType="int">
        select count(*)
        from tb_device_maintain_plan_c a
                 left join device_storage_journal b on a.device_label_code = b.device_label_code
                 left join tb_device_maintain_plan_m c on a.main_id = c.id

        where a.device_label_code = #{deviceLabelCode}
    </select>

</mapper>