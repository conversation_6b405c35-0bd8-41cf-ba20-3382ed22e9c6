<!-- 设备报废 -->
<template>
  <div class="wrapper">
    <CardSearch
      ref="refSearch"
      :config="cardSearchConfig"
    />
    <CardTable
      :config="TableConfig"
      class="card-table"
    ></CardTable>
    <!-- 报废 -->
    <SLDrawer
      ref="scrappedref"
      :config="scrapped"
    ></SLDrawer>
  </div>
</template>

<script lang="ts" setup>
import { Refresh } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { ICONS } from '@/common/constans/common'
import { ICardSearchIns, ISLDrawerIns } from '@/components/type'
import { getdeviceDumpSerch, getPlanTaskDetail, postdump } from '@/api/equipment_assets/equipmentScrapped'

import { getstoreSerch } from '@/api/equipment_assets/equipmentOutStock'
import { getWaterSupplyTree } from '@/api/company_org'
import { getUserList } from '@/api/user/index'
import { removeSlash } from '@/utils/removeIdSlash'
import { getDevicePurchaseSearch } from '@/api/equipment_assets/equipmentPurchase'
import { getDeviceStorageJournalSerch } from '@/api/equipment_assets/ledgerManagement'
import { traverse } from '@/utils/GlobalHelper'
import useGlobal from '@/hooks/global/useGlobal'

const { $btnPerms } = useGlobal()

const refSearch = ref<ICardSearchIns>()

const scrappedref = ref<ISLDrawerIns>()

const cardSearchConfig = ref<ISearch>({
  filters: [
    { label: '报废单编码', field: 'code', type: 'input', labelWidth: '90px' },
    { label: '报废单标题', field: 'name', type: 'input', labelWidth: '90px' },
    { label: '申请人员', field: 'uploadUserId', type: 'department-user' },

    { label: '经办人', field: 'handleUserId', type: 'department-user' },
    {
      type: 'date',
      label: '报废单创建时间',
      field: 'createTime',
      labelWidth: '120px',
      format: 'YYYY-MM-DD HH:mm:ss',
      onChange: () => refreshData()
    }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          icon: ICONS.QUERY,
          click: () => refreshData()
        },
        {
          type: 'default',
          perm: true,
          text: '重置',
          svgIcon: shallowRef(Refresh),
          click: () => {
            refSearch.value?.resetForm()
            refreshData()
          }
        }
      ]
    }
  ]
})

const TableConfig = reactive<ICardTable>({
  defaultExpandAll: true,
  indexVisible: true,
  columns: [
    { label: '报废单标题', prop: 'name' },
    { label: '报废单编码', prop: 'code' },
    { label: '报废申请时间', prop: 'uploadTime' },
    { label: '申请部门', prop: 'uploadUserDepartmentName' },
    { label: '申请人', prop: 'uploadUserName' },
    { label: '经办人', prop: 'handleUserName' },
    { label: '创建人', prop: 'creatorName' },
    { label: '创建时间', prop: 'createTime' },
    { label: '报废状态', prop: 'isDump', formatter: row => (row.isDump ? '已报废' : '待报废') }
  ],
  operationWidth: '160px',
  operations: [
    {
      hide: row => row.isDump,
      type: 'success',
      text: '报废',
      perm: $btnPerms('RoleManageEdit'),
      icon: ICONS.DELETE,
      click: row => clickEdit(row)
    },
    {
      hide: row => !row.isDump,
      type: 'primary',
      text: '详情',
      perm: $btnPerms('RoleManageEdit'),
      icon: ICONS.DETAIL,
      click: row => openDetails(row)
    }
  ],
  dataList: [],
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page
      TableConfig.pagination.limit = size
      refreshData()
    }
  }
})

// 设备选择
const scrapped = reactive<IDrawerConfig>({
  title: '设备选择',
  labelWidth: '130px',
  defaultValue: {},
  submitting: false,
  btns: [],
  group: [
    {
      fields: [
        {
          xl: 8,
          type: 'input',
          label: '报废单标题',
          field: 'name',
          disabled: true
        }, {
          xl: 8,
          type: 'input',
          label: '报废单编码',
          field: 'code',
          disabled: true
        }, {
          xl: 8,
          type: 'input',
          label: '申请部门',
          field: 'uploadUserDepartmentName',
          disabled: true
        }, {
          xl: 8,
          type: 'input',
          label: '申请人',
          field: 'uploadUserName',
          disabled: true
        }, {
          xl: 8,
          type: 'input',
          label: '经办人',
          field: 'handleUserName',
          disabled: true
        }, {
          xl: 8,
          type: 'input',
          label: '创建人',
          field: 'creatorName',
          disabled: true
        }, {
          xl: 8,
          type: 'date',
          label: '创建时间',
          field: 'createTime',
          readonly: true
        }, {
          type: 'table',
          field: 'items',
          config: {
            indexVisible: true,
            height: '350px',
            dataList: computed(() => data.selectList) as any,
            columns: [
              { label: '标签编码',
                prop: 'deviceLabelCode'
              }, { label: '设备名称',
                prop: 'name'
              }, { label: '规格型号',
                prop: 'model'
              }, { label: '所属大类',
                prop: 'topType'
              }, { label: '所属类别',
                prop: 'type'
              }, { label: '货架',
                prop: 'shelvesName'
              }, { label: '设备状态',
                prop: 'isDumped',
                formatter: row => (row.isDumped ? '已报废' : '未报废')
              }
            ],
            pagination: {
              hide: true
            }
          }
        }
      ]
    }
  ]
})

const openDetails = (row: { [x: string]: any }) => {
  for (const i in row) {
    if (row[i] === undefined || row[i] === null) row[i] = ' '
  }
  scrapped.defaultValue = { ...(row) || {} }
  scrapped.btns = []
  scrappedref.value?.openDrawer()
  data.getSelectValue(row)
}

const clickEdit = (row: { [x: string]: any }) => {
  scrapped.defaultValue = { ...(row) || {} }
  scrapped.btns = [{
    type: 'primary',
    perm: true,
    text: '报废',
    click: () => {
      scrapped.submitting = true
      postdump(row.id).then(() => {
        refreshData()
        ElMessage.success('报废完成')
        scrappedref.value?.closeDrawer()
        scrapped.submitting = false
      }).catch(error => {
        ElMessage.warning('您不是该报废任务执行人')
        scrapped.submitting = false
      })
    }
  }]
  data.getSelectValue(row)
  scrappedref.value?.openDrawer()
}

const data = reactive({
  // 部门
  WaterSupplyTree: [] as any,
  // 用户列表
  UserList: [],
  // 采购单
  DevicePurchase: [],
  // 仓库
  storeList: [],
  // 设备列表
  deviceValue: [] as any,
  // 设备数量
  total: 0,
  // 选中的设备
  selectList: [] as any[],

  getDevice: (param?: any) => {
    const params = {
      size: 99999,
      page: 1,
      ...param
    }
    getDeviceStorageJournalSerch(params).then(res => {
      data.deviceValue = res.data.data.data || []
      data.total = res.data.data.total || 0
    })
  },
  getWaterSupplyTreeValue: () => {
    const depth = 2
    getWaterSupplyTree(depth).then(res => {
      data.WaterSupplyTree = traverse(res.data.data || [])
    })
  },
  getUserListValue: (pid: string) => {
    getUserList({ pid }).then(res => {
      const value = res.data.data.data || []
      data.UserList = value.map(item => {
        return { label: item.firstName, value: removeSlash(item.id.id) }
      })
    })
  },
  getDevicePurchaseValue: () => {
    const params = { page: 1, size: 99999 }
    getDevicePurchaseSearch(params).then(res => {
      const value = res.data.data.data || []
      data.DevicePurchase = value.map(item => {
        return { label: item.title, value: item.id }
      })
    })
  },
  getstoreSerchValue: () => {
    const params = { page: 1, size: 99999 }
    getstoreSerch(params).then(res => {
      const value = res.data.data.data || []
      data.storeList = value.map(item => {
        return { label: item.name, value: item.id }
      })
    })
  },
  getSelectValue: (row:any) => {
    const params = { page: 1, size: 99999, mainId: row.id }
    getPlanTaskDetail(params).then(res => {
      data.selectList = res.data.data.data || []
    })
  },

  init: () => {
    data.getWaterSupplyTreeValue()
    data.getDevicePurchaseValue()
    data.getstoreSerchValue()
  }
})

const refreshData = async () => {
  const params = {
    size: TableConfig.pagination.limit,
    page: TableConfig.pagination.page,
    ...(refSearch.value?.queryParams || {})
  }
  getdeviceDumpSerch(params).then(res => {
    TableConfig.dataList = res.data.data.data || []
    TableConfig.pagination.total = res.data.data.total || 0
  })
}

onMounted(() => {
  refreshData()
  data.init()
})
</script>

<style lang="scss">
.el-table__placeholder {
  display: none;
}
</style>
