import{d as S,r as W,Q as K,g as j,n as q,p as c,q as U,aw as v,i as n,aB as N,bs as T,C as H}from"./index-r0dFAfgr.js";import{m as z,n as J,e as Z,c as d,a as y,s as u,q as X,u as _}from"./FeatureHelper-Da16o0mu.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./MapView-DaoQedLH.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./Point-WxyopZva.js";import{g as w}from"./LayerHelper-Cn-iiqxI.js";import"./project-DUuzYgGl.js";import{s as D,i as $}from"./ToolHelper-BiiInOzB.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./geometryEngineBase-BhsKaODW.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./pipe-nogVzCHG.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";const ee="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAABO0lEQVRYhe2SPUsDQRCGR7CRCHYSiIVYmsLedCI2+hv8aE0vEsHaP2ChlQRMbgYiyuXmnSXF9elt9J/YrE0uJmeQILim2Ade2J1l2YedIYpEIpEIEQlwymY3QQOcjAUY6DPgA+fl6weyrNrr97dCRrKs+h/dnk3HrM5m+yGTmO2MBRh4CD0DAjxPCSSqj8VeVLcZ8D3VjdH5R+Lc0Wg9FNUrIiI2uxdARutmovpGRMSqDQZ827mK5PkqA55VGxPv3f4o0DGrL5SAAJdPzq2XBUR1T8wOywIyGKyJ6jUR0a8EihZImtbKA8vAkM1a5XoCnBcCk7SdqzDgu87tzi2Q5/lyYnbmvV/69pDqQRfYLNclTWtFm2bcORaRlbkF/pqZAgy8J8BdiDDwOi1g1hRAQobNLkL9eCQSWWw+AZZVJPHoS+wkAAAAAElFTkSuQmCC",te="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAB9UlEQVRYhe3VPWhUQRSG4ZsIWqiFooUBC/8KEWxESGFIKWIliJ1W0cZGLCRWroI/KBai2IhFjO7ON5hiyXLeczdIBK2tIkQhaBFEEUFEkkLj2KwhBE0I7A4IOXCqOcPz3TswUxSrtVptrFqz2SMYCzAl9xPZAwS4LrgXzHoFn1NKXVkDyH1I0JD7WcFkVjy4DwhShAm5D1Xd92XDo9lxwVyEV/V6fWM2uCiKoubeJ5gVvBtx35YVr8J+wVfBp+i+OyteK8vtgmnB9wAHs+JPGo1NESYEP6L70ay4ma0TPBek4D6QFa9UKt3BPQiS4FJWvCiKIsBtQQpmd7PjEc63LpqRGOOa3PgxwU/BhwiDufFDgplg9jaabQ1mp2R2NQseGo29gi8y+zjSaOxcEOqMzE53FK81mz0B3gu+xbI8sHhdMFwdHd3SETy6bxa8FsxGs/6/zQSz3mB2rv14jGsFzwS/ovvJf82llLrkPtRWvFKpdAuqgiT3C8vNC4bbAsfx8Q2CW3+uWMGd5faklLoEj1fklOWO4H4/wM1HZbl+fkHuN1pfflEw1zqCsaU6wnjrNVxyblFPRxgU1ATXFgZ4GNwvB7MjgpkAU51owYzgsMyuCB7MB3jabO4RvBRM1tz7VvJbV3QEZv2CN4IXwWxXp5zV+r/qN5DLzblC4gwsAAAAAElFTkSuQmCC",re="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAADR0lEQVRYhe2WT4hVdRTHzzhJTtPQpoVYGGiuIizKLN20qjGh2iTSQgRrEy7clRt54NJFQRZIycCQvt85+Mjbm3u+575n3gKVkol2hRtFLBCncDLQKWV+LTx3uD7fe/PH92xRP/hx4b5z7ud7/vx+5xH9v3q4xvJ8RRV4uVKpLLvv8CRJRgQ4zUBkgPM8f+Dfgsf7KqJerz/EwNcMxACMBbP1ApxgILLZ4b6Wowxn4G8G0rE8X9Ei6vO+iLgDonokqB5xIVaIECDvi4iWyK9VG41VIjLIwBcMRAEyERkaz7JhBr5xu896IsIjK2r8oz9/kjRdKSKDARgXoHZocnI5EdF4lg2z2bc9ESEiQwVcgOMe9V5P88/jWTYsIoOt3S/N5iMM3HC/XfcM9z0lafo0ERGrvi/AB518g9kbnqmrR82eWjQ8SZIRBk45+JOguoWBGTa7Wk3T57v5BmA7A7cYmApm6xcNF5EhBppzkau+SUTEZtsYuMmql2sTE2va+pptLYQGYMO9wo2BPxmYCapbHPA2m32pqg+2+jLwWgEXsxeWBldtOPyMiAyy6mYGrjFwQ8ye7OQ7VyJguhfwGQZmA/AeEVE1yzZ1azgxG/WOXxqciEiAmh+ZE0F1LQMXGJgVs91d/UrwappuXBLcb69fGYiiWo8xDtTS9AkGzruIrQuBB7N3BBABhAFmYL/k+cPzCmDggKf+gj8PxhgHpNlczWYfFjdci8+rrZEfrdcfrU1MrAnABhcQg+pH8wtQvcjALWk2VzNw1h0/jjEOtLMPqq/Ml3a/R24ycKnTd4iIqKr6bHHHE90uRzHR2s12AV5n4C8GfpM0faZrYMAPDETJsuc6GonqPgZiMPu0eFcWUR6ri4G7gINe0kpnAWbfeffvvOP97WHy/dy/HtU9DFxn4I+g+uJ8cCKioLrDBZxtD6/XH2NgloF4rNFY1/q7n46TpWG0oMhLAta632xN9fG7BQC73OD3To3imTjDwLQALy0UTkQUYxxgYMrnybvtBBx3Ab+0O2rFqlQqy5IkGVkMnIjo0OTkcgYuOeOruwxY9XIpved8CPVynyt9/0o7AcdKBn3dAtTapkjMRgPwVj+3mI12K/F/b/0DkZsqT+CQScYAAAAASUVORK5CYII=",ie={class:"measure-tool"},oe=["src"],ae=["src"],se=["src"],me=S({__name:"Measure",props:{view:{}},setup(P){const r=P,s=W({curOperate:""}),e={},g=t=>{var i,m,o,a,p;r.view&&(s.curOperate=t,D("crosshair"),(i=e.drawAction)==null||i.destroy(),(m=e.drawer)==null||m.destroy(),e.graphicsLayer=w(r.view,{id:"measure-layer",title:"测量绘制图层"}),e.textLayer=w(r.view,{id:"measure-text",title:"测量结果图层"}),e.moveLayer=w(r.view,{id:"measure-move",title:"移动测量图层"}),(o=e.graphicsLayer)==null||o.removeAll(),(a=e.textLayer)==null||a.removeAll(),(p=e.moveLayer)==null||p.removeAll(),e.drawer=$(r.view),e.drawAction=e.drawer.create(t==="angle"||t==="length"?"polyline":"polygon"),e.drawAction.on(["vertex-add","cursor-update"],f),e.drawAction.on("draw-complete",A=>{f(A),D("")}))},f=t=>{var A,x,h,R,B,L,E,C,O,V,I,Q,k,G,F,M,b;console.log(t);const i=t.type==="cursor-update",m=s.curOperate==="area"||s.curOperate==="angle"?t.vertices.length>=3:s.curOperate==="length"?t.vertices.length>1:!1,o=s.curOperate;let a,p;if(o==="area"){if(a=t.vertices.length<3?z(t.vertices,(A=r.view)==null?void 0:A.spatialReference):J(t.vertices,(x=r.view)==null?void 0:x.spatialReference),((h=t.vertices)==null?void 0:h.length)>=3){const l=Z(t.vertices,void 0,(R=r.view)==null?void 0:R.spatialReference);p=d({geometry:y("polygon",t.vertices,(B=r.view)==null?void 0:B.spatialReference),symbol:u("text",{text:"总面积："+((l==null?void 0:l.toFixed(2))||"0")+" ㎡",yOffset:10})})}}else if(a=z(t.vertices,(L=r.view)==null?void 0:L.spatialReference),t.vertices.length){if(o==="length")p=d({geometry:y("point",[t.vertices[t.vertices.length-1]],(E=r.view)==null?void 0:E.spatialReference),symbol:u("text",{text:((C=t.vertices)==null?void 0:C.length)===1?"0 m":X(t.vertices,void 0,(O=r.view)==null?void 0:O.spatialReference).toFixed(2)+" m",yOffset:10})});else if(o==="angle"&&t.vertices.length>=3){const l=t.vertices||[];p=d({geometry:y("point",[l[l.length-2]],(V=r.view)==null?void 0:V.spatialReference),symbol:u("text",{text:((I=t.vertices)==null?void 0:I.length)===1?"0 度":_(l.slice(l.length-3)).toFixed(2)+" 度",yOffset:10})})}}(Q=e.moveLayer)==null||Q.removeAll(),(k=e.graphicsLayer)==null||k.removeAll(),s.curOperate==="area"&&((G=e.textLayer)==null||G.removeAll()),a&&((F=e.graphicsLayer)==null||F.add(a)),p&&(i?m&&((M=e.moveLayer)==null||M.add(p)):(b=e.textLayer)==null||b.add(p))},Y=()=>{var t,i,m,o,a;(t=e.drawAction)==null||t.destroy(),(i=e.drawer)==null||i.destroy(),e.graphicsLayer&&((m=r.view)==null||m.map.remove(e.graphicsLayer)),e.textLayer&&((o=r.view)==null||o.map.remove(e.textLayer)),e.moveLayer&&((a=r.view)==null||a.map.remove(e.moveLayer))};return K(()=>{Y()}),(t,i)=>{const m=T;return j(),q(N,null,[i[3]||(i[3]=c("div",{class:"panel-title"},[c("span",null,"请选择测量工具：")],-1)),U(m),c("div",ie,[c("div",{class:v(["measure-tool-item length",{active:n(s).curOperate==="length"}]),onClick:i[0]||(i[0]=o=>g("length"))},[c("img",{class:"img",src:n(ee),alt:""},null,8,oe)],2),c("div",{class:v(["measure-tool-item area",{active:n(s).curOperate==="area"}]),onClick:i[1]||(i[1]=o=>g("area"))},[c("img",{class:"img",src:n(re),alt:""},null,8,ae)],2),c("div",{class:v(["measure-tool-item angle",{active:n(s).curOperate==="angle"}]),onClick:i[2]||(i[2]=o=>g("angle"))},[c("img",{class:"img",src:n(te),alt:""},null,8,se)],2)])],64)}}}),Rt=H(me,[["__scopeId","data-v-5bd5cb49"]]);export{Rt as default};
