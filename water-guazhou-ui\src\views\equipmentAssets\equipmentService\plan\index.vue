<!-- 保养计划 -->
<template>
  <div class="wrapper">
    <CardSearch
      ref="refSearch"
      :config="cardSearchConfig"
    />
    <CardTable
      :config="TableConfig"
      class="card-table"
    ></CardTable>
    <SLDrawer
      ref="refForm"
      :config="addOrUpdateConfig"
    ></SLDrawer>
    <SLDrawer
      ref="detailForm"
      :config="detailConfig"
    ></SLDrawer>
    <SLDrawer
      ref="refForm1"
      :config="addEquipment"
    ></SLDrawer>
  </div>
</template>

<script lang="ts" setup>
import { Refresh } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { ICONS } from '@/common/constans/common'
import { ICardSearchIns, ISLDrawerIns } from '@/components/type'
import useGlobal from '@/hooks/global/useGlobal'
import { SLConfirm } from '@/utils/Message'
import {
  getAreaTreeSearch
} from '@/api/equipment_assets/equipmentManage'
import { getMaintenancePlanSerch, postMaintenancePlan, deleteMaintenancePlan, getMaintenancePlanSerchDetail } from '@/api/equipment_assets/equipmentService'
import { getInspectionTeamSerch } from '@/api/equipment_assets/equipmentInspection'
import { formatDate } from '@/utils/DateFormatter'
import { getWaterSupplyTree } from '@/api/company_org'
import { traverse, uniqueFunc } from '@/utils/GlobalHelper'
import { getUserList } from '@/api/user/index'
import { removeSlash } from '@/utils/removeIdSlash'
import { getDeviceStorageJournalEq } from '@/api/equipment_assets/ledgerManagement'

const { $btnPerms } = useGlobal()

const refSearch = ref<ICardSearchIns>()

const refForm = ref<ISLDrawerIns>()

const detailForm = ref<ISLDrawerIns>()

const refForm1 = ref<ISLDrawerIns>()

const chosen = ref([])

const cardSearchConfig = ref<ISearch>({
  filters: [
    { label: '计划名称', field: 'planName', type: 'input' },
    { label: '班组名称', field: 'teamName', type: 'input' },
    { label: '保养人员名称', field: 'userName', type: 'input', labelWidth: '100px' },
    { label: '开始时间', field: 'startEndTime', type: 'daterange', format: 'x' },
    { label: '结束时间', field: 'endEndTime', type: 'daterange', format: 'x' }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          icon: ICONS.QUERY,
          click: () => refreshData()
        },
        {
          type: 'default',
          perm: true,
          text: '重置',
          svgIcon: shallowRef(Refresh),
          click: () => {
            refSearch.value?.resetForm()
            refreshData()
          }
        },
        {
          perm: $btnPerms('RoleManageAdd'),
          text: '新增',
          type: 'success',
          icon: ICONS.ADD,
          click: () => clickCreatedRole()
        },
        {
          perm: $btnPerms('RoleManageAdd'),
          text: '批量删除',
          type: 'danger',
          icon: ICONS.DELETE,
          click: () => {
            if (TableConfig.selectList?.length === 0) {
              ElMessage.warning('请选中至少一条数据')
              return
            }
            haneleDelete()
          }
        }
      ]
    }
  ]
})

const TableConfig = reactive<ICardTable>({
  defaultExpandAll: true,
  indexVisible: true,
  selectList: [],
  handleSelectChange: (val:any) => {
    TableConfig.selectList = val
  },
  columns: [
    { label: '计划名称', prop: 'name' },
    { label: '保养班组', prop: 'teamName' },
    { label: '保养人员', prop: 'userName' },
    { label: '开始时间', prop: 'startTime', formatter: row => formatDate(row.startTime, 'YYYY-MM-DD') },
    { label: '结束时间', prop: 'endTime', formatter: row => formatDate(row.endTime, 'YYYY-MM-DD') },
    { label: '消耗天数', prop: 'executionDays' },
    { label: '间隔天数', prop: 'intervalDays' },
    { label: '执行次数', prop: 'executionNum' },
    { label: '备注', prop: 'remark' },
    { label: '审核人姓名', prop: 'reviewerName' },
    { label: '审核部门', prop: 'reviewerDepartment' },
    { label: '添加人', prop: 'creatorName' },
    { label: '添加时间', prop: 'createTime', formatter: row => formatDate(row.createTime, 'YYYY-MM-DD') }
  ],
  operations: [
    {
      type: 'primary',
      isTextBtn: true,
      color: '#4195f0',
      text: '详情',
      perm: $btnPerms('RoleManageEdit'),
      icon: 'iconfont icon-xiangqing',
      click: row => openDetailDialog(row)
    },
    {
      isTextBtn: true,
      type: 'danger',
      text: '删除',
      icon: 'iconfont icon-shanchu',
      perm: $btnPerms('RoleManageDelete'),
      click: row => haneleDelete(row)
    }
  ],
  dataList: [],
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page
      TableConfig.pagination.limit = size
      refreshData()
    }
  }
})

const addOrUpdateConfig = reactive<IDrawerConfig>({
  title: '新增',
  labelWidth: '130px',
  submit: (params:any) => {
    if (!params.maintainPlanCList || !params.maintainPlanCList.length) {
      ElMessage.warning('请添加设备')
      return
    }
    params.maintainPlanCList.map(item => item.mmDeviceId = item.serialId)
    postMaintenancePlan(params).then(() => {
      refreshData()
      ElMessage.success('添加成功')
      refForm.value?.closeDrawer()
    })
  },

  defaultValue: {},
  group: [
    {
      fields: [
        {
          xl: 8,
          type: 'input',
          label: '计划名称',
          field: 'name',
          rules: [{ required: true, message: '请输入计划名称' }]
        }, {
          xl: 8,
          type: 'select',
          label: '班组',
          field: 'teamId',
          rules: [{ required: true, message: '请选择班组' }],
          options: computed(() => data.teamList) as any,
          onChange: val => {
            const value:any = data.teamList.find((item:any) => item.value === val) || {}
            data.userList = value.maintainCircuitTeamCList.map(item => {
              return { label: item.userName, value: item.userId }
            })
          }
        }, {
          xl: 8,
          type: 'select',
          label: '人员名称',
          field: 'userId',
          rules: [{ required: true, message: '请选择人员名称' }],
          options: computed(() => data.userList) as any
        }, {
          xl: 8,
          type: 'date',
          label: '计划开始时间',
          field: 'startTime',
          rules: [{ required: true, message: '请输入计划开始时间' }],
          format: 'YYYY-MM-DD'
        }, {
          xl: 8,
          type: 'number',
          label: '执行天数',
          field: 'executionDays',
          min: 0,
          rules: [{ required: true, message: '请输入执行天数' }]
        }, {
          xl: 8,
          type: 'number',
          label: '间隔时间',
          field: 'intervalDays',
          min: 0,
          rules: [{ required: true, message: '请输入间隔时间' }]
        }, {
          xl: 8,
          type: 'number',
          label: '执行次数',
          field: 'executionNum',
          min: 0,
          rules: [{ required: true, message: '请输入执行次数' }]
        },
        {
          xl: 8,
          type: 'department-user',
          label: '审核人员',
          field: 'reviewer',
          rules: [{ required: true, message: '请选择审核人员' }]
        },
        {
          xl: 16,
          type: 'textarea',
          label: '备注',
          field: 'remark'
        }, {
          type: 'table',
          field: 'maintainPlanCList',
          config: {
            indexVisible: true,
            height: '350px',
            titleRight: [
              {
                style: {
                  justifyContent: 'flex-end'
                },
                items: [
                  {
                    type: 'btn-group',
                    btns: [
                      {
                        text: '添加设备',
                        perm: true,
                        click: () => {
                          refForm1.value?.openDrawer()
                        }
                      }
                    ]
                  }
                ]
              }
            ],
            dataList: computed(() => data.selectList) as any,
            columns: [
              { label: '标签编码',
                prop: 'deviceLabelCode'
              }, { label: '设备名称',
                prop: 'name'
              }, { label: '设备款式',
                prop: 'model'
              }, { label: '所属类别',
                prop: 'type'
              }, { label: '安装区域',
                prop: 'installAddressName'
              }, { label: '安装位置',
                prop: 'actualAddress'
              }
            ],
            operations: [
              {
                type: 'danger',
                text: '移除',
                icon: ICONS.DELETE,
                perm: $btnPerms('RoleManageDelete'),
                click: row => data.selectList = data.selectList.filter(item => item.id !== row.id)
              }
            ],
            pagination: {
              hide: true
            }
          }
        }
      ]
    }
  ]
})

// 详情
const detailConfig = reactive<IDrawerConfig>({
  title: '详情',
  labelWidth: '130px',
  defaultValue: {},
  group: [
    {
      fields: [
        {
          disabled: true,
          xl: 8,
          type: 'input',
          label: '计划名称',
          field: 'name',
          rules: [{ required: true, message: '请输入计划名称' }]
        }, {
          readonly: true,
          xl: 8,
          type: 'select',
          label: '班组',
          field: 'teamId',
          rules: [{ required: true, message: '请选择班组' }],
          options: computed(() => data.teamList) as any
        }, {
          readonly: true,
          xl: 8,
          type: 'select',
          label: '人员名称',
          field: 'userName',
          rules: [{ required: true, message: '请选择人员名称' }],
          options: computed(() => data.userList) as any
        }, {
          readonly: true,
          xl: 8,
          type: 'date',
          label: '计划开始时间',
          field: 'startTime',
          rules: [{ required: true, message: '请输入计划开始时间' }],
          format: 'x'
        }, {
          readonly: true,
          xl: 8,
          type: 'input-number',
          label: '执行天数',
          field: 'executionDays',
          rules: [{ required: true, message: '请输入执行天数' }]
        }, {
          readonly: true,
          xl: 8,
          type: 'input-number',
          label: '间隔时间',
          field: 'intervalDays',
          rules: [{ required: true, message: '请输入间隔时间' }]
        }, {
          readonly: true,
          xl: 8,
          type: 'input-number',
          label: '执行次数',
          field: 'executionNum',
          rules: [{ required: true, message: '请输入执行次数' }]
        },
        {
          readonly: true,
          xl: 8,
          type: 'select-tree',
          label: '审核部门',
          field: 'reviewerDepartment',
          checkStrictly: true,
          options: computed(() => data.WaterSupplyTree) as any,
          rules: [{ required: true, message: '请选择审核部门' }]
        },
        {
          disabled: true,
          xl: 8,
          type: 'input',
          label: '审核人员',
          field: 'reviewerName',
          rules: [{ required: true, message: '请选择审核人员' }]
        },
        {
          disabled: true,
          xl: 16,
          type: 'textarea',
          label: '备注',
          field: 'remark'
        }, {
          type: 'table',
          field: 'maintainPlanCList',
          config: {
            indexVisible: true,
            height: '350px',
            dataList: computed(() => data.selectList) as any,
            columns: [
              { label: '标签编码',
                prop: 'deviceLabelCode'
              }, { label: '设备名称',
                prop: 'name'
              }, { label: '设备款式',
                prop: 'model'
              }, { label: '所属类别',
                prop: 'type'
              }, { label: '安装区域',
                prop: 'installAddressName'
              }, { label: '安装位置',
                prop: 'actualAddress'
              }
            ],
            pagination: {
              hide: true
            }
          }
        }
      ]
    }
  ]
})

// 设备选择
const addEquipment = reactive<IDrawerConfig>({
  title: '设备选择',
  labelWidth: '130px',
  submit: (params:any, status:boolean) => {
    if (status) {
      delete params.drive
      data.getDevice(params)
      return
    }
    data.selectList = [...data.selectList, ...chosen.value]
    data.selectList = uniqueFunc(data.selectList, 'id')
    refForm1.value?.closeDrawer()
  },
  defaultValue: {},
  group: [
    {
      fields: [
        {
          xl: 8,
          type: 'input',
          label: '标签编码',
          field: 'deviceLabelCode'
        }, {
          xl: 8,
          type: 'input',
          label: '设备名称',
          field: 'name'
        }, {
          xl: 8,
          type: 'input',
          label: '设备型号',
          field: 'model'
        }, {
          xl: 8,
          label: '安装区域',
          field: 'areaId',
          type: 'select-tree',
          checkStrictly: true,
          options: computed(() => data.installationArea) as any
        },
        //  {
        //   xl: 8,
        //   type: 'input',
        //   label: '安装位置',
        //   field: 'detailInstallAddressName'
        // },
        {
          xl: 12,
          type: 'daterange',
          label: '上次保养时间',
          field: 'lastMaintainanceTime'
        },
        {
          type: 'table',
          field: 'drive',
          config: {
            indexVisible: true,
            height: '350px',
            selectList: [],
            handleSelectChange: val => {
              chosen.value = val
            },
            dataList: computed(() => data.deviceValue) as any,
            titleRight: [
              {
                style: {
                  justifyContent: 'flex-end'
                },
                items: [
                  {
                    type: 'btn-group',
                    btns: [
                      {
                        text: '查询',
                        type: 'success',
                        perm: true,
                        click: () => {
                          refForm1.value?.Submit(true)
                        }
                      }, {
                        text: '重置',
                        type: 'primary',
                        perm: true,
                        click: () => {
                          refForm1.value?.resetForm()
                          refForm1.value?.Submit(true)
                        }
                      }
                    ]
                  }
                ]
              }
            ],
            columns: [
              { label: '标签编码',
                prop: 'deviceLabelCode'
              }, { label: '设备名称',
                prop: 'name'
              }, { label: '规格型号',
                prop: 'model'
              }, { label: '所属大类',
                prop: 'topType'
              }, { label: '所属类别',
                prop: 'type'
              }, { label: '安装区域',
                prop: 'installAddressName'
              }, { label: '安装位置',
                prop: 'actualAddress'
              }, { label: '最后保养时间',
                prop: 'lastMaintainanceTime'
              }
            ],
            pagination: {
              hide: true
            }
          }
        }
      ]
    }
  ]
})

const clickCreatedRole = () => {
  addOrUpdateConfig.title = '新增'
  addOrUpdateConfig.defaultValue = { }
  data.selectList = []
  refForm.value?.openDrawer()
}

// 打开详情
const openDetailDialog = (row: { [x: string]: any }) => {
  detailConfig.title = '详情'
  getMaintenancePlanSerchDetail(row.id).then(res => {
    const value = res.data.data || {}
    detailConfig.defaultValue = { ...(value) || {} }
    data.selectList = value.maintainPlanCList || []
    detailForm.value?.openDrawer()
  }).catch(error => {
    ElMessage.warning(error)
  })
}

const haneleDelete = (row?: { id: string }) => {
  SLConfirm('确定删除该保养计划, 是否继续?', '删除提示').then(() => {
    let ids: string[] = []
    if (row) {
      ids = [row.id]
    } else {
      ids = TableConfig?.selectList?.map(node => node.id) ?? []
    }
    deleteMaintenancePlan(ids).then(() => {
      refreshData()
      ElMessage.success('删除成功')
    })
  })
}

const data = reactive({
  // 班组
  teamList: [],
  // 班组用户
  userList: [] as any[],
  // 部门
  WaterSupplyTree: [],
  // 部门用户
  departmentUser: [],
  // 设备列表
  deviceValue: [] as any[],
  // 安装区域
  installationArea: [],
  // 选中的设备
  selectList: [] as any[],
  getTeam: () => {
    const params = {
      size: 99999,
      page: 1,
      type: '保养班组',
      name: ''
    }
    getInspectionTeamSerch(params).then(res => {
      const value = res.data.data.data || []
      data.teamList = value.map(item => {
        return { label: item.name, value: item.id, maintainCircuitTeamCList: item.maintainCircuitTeamCList }
      })
    })
  },
  getWaterSupplyTreeValue: () => {
    const depth = 2
    getWaterSupplyTree(depth).then(res => {
      data.WaterSupplyTree = traverse(res.data.data || [])
    })
  },
  getUserListValue: (pid: string) => {
    getUserList({ pid }).then(res => {
      const value = res.data.data.data || []
      data.departmentUser = value.map(item => {
        return { label: item.firstName, value: removeSlash(item.id.id) }
      })
    })
  },
  getDevice: (param?: any) => {
    const params = {
      size: 99999,
      page: 1,
      ...param
    }
    if (params.lastMaintainanceTime && params.lastMaintainanceTime.length > 1) {
      params.lastMaintainanceTimeFrom = params.lastMaintainanceTime[0]
      params.lastMaintainanceTimeTo = params.lastMaintainanceTime[1]
    }
    delete params.lastMaintainanceTime
    getDeviceStorageJournalEq(params).then(res => {
      const value = res.data.data.data || []
      let value2: any[] = []
      value.forEach((element: any) => {
        value2 = [...value2, ...element.restDeviceInfos]
      })
      data.deviceValue = value2
    })
  },
  // 获取安装区域
  getAreaTreeValue: () => {
    const params = {
      page: 1,
      size: 99999,
      shortName: ''
    }
    getAreaTreeSearch(params).then(res => {
      data.installationArea = traverse(res.data.data.data || [])
    })
  }
})

const refreshData = async () => {
  const params = {
    size: TableConfig.pagination.limit,
    page: TableConfig.pagination.page,
    startStartTime: '',
    startEndTime: [] as any,
    endStartTime: '',
    endEndTime: [] as any,
    ...(refSearch.value?.queryParams || {})
  }
  if (refSearch.value?.queryParams && refSearch.value?.queryParams.startEndTime && refSearch.value?.queryParams.startEndTime.length > 1) {
    params.startStartTime = refSearch.value?.queryParams.startEndTime[0]
    params.startEndTime = refSearch.value?.queryParams.startEndTime[1]
  }
  if (refSearch.value?.queryParams && refSearch.value?.queryParams.endEndTime && refSearch.value?.queryParams.endEndTime.length > 1) {
    params.endStartTime = refSearch.value?.queryParams.endEndTime[0]
    params.endEndTime = refSearch.value?.queryParams.endEndTime[1]
  }
  getMaintenancePlanSerch(params).then(res => {
    TableConfig.dataList = res.data.data.data || []
    TableConfig.pagination.total = res.data.data.total || 0
  })
}

onMounted(() => {
  refreshData()
  data.getTeam()
  data.getWaterSupplyTreeValue()
  data.getDevice()
  data.getAreaTreeValue()
})
</script>

<style lang="scss">
.el-table__placeholder {
  display: none;
}
</style>
