package org.thingsboard.server.dao.util.imodel.query.plan;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.query.AwareCurrentUserUUID;

@Getter
@Setter
public class PlanTaskStartRequest implements AwareCurrentUserUUID {
    // id
    private String id;




    private String currentUser;

    @Override
    public void currentUserId(String uuid) {
        currentUser = uuid;
    }
}
