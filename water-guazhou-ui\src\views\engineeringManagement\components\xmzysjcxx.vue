<!-- 工程管理-详情-项目总验收基础信息 -->
<template>
  <el-card class="card">
    <descriptions :config="basicConfig"></descriptions>
  </el-card>
  <CardTable
    title="项目工程"
    :config="TableConfig"
    class="card-table"
  ></CardTable>
</template>

<script lang="ts" setup>
import { getSingleProjectList } from '@/api/engineeringManagement/manage';

const props = defineProps<{ config: any }>();

const basicConfig = reactive<IDescriptionsConfig>({
  defaultValue: computed(() => props.config) as any,
  border: true,
  direction: 'horizontal',
  column: 2,
  title: '项目总验收基础信息',
  fields: [
    {
      type: 'text',
      label: '总验时间:',
      field: 'acceptTime',
      formatter: (row) => dayjs(row).format('YYYY-MM-DD')
    },
    { type: 'text', label: '总验说明:', field: 'remark' },
    { type: 'text', label: '创建人:', field: 'creatorName' },
    { type: 'text', label: '创建时间:', field: 'createTimeName' },
    { type: 'text', label: '最后更新人:', field: 'updateUserName' },
    { type: 'text', label: '最后更新时间:', field: 'updateTimeName' }
  ]
});

const TableConfig = reactive<ICardTable>({
  defaultExpandAll: true,
  indexVisible: true,
  columns: [
    { label: '工程编号', prop: 'code' },
    { label: '工程名称', prop: 'name' },
    { label: '工程地址', prop: 'address' },
    { label: '工程类别', prop: 'typeName' },
    { label: '工程预算(万元)', prop: 'estimate' },
    { label: '申请单位', prop: 'fitstpartName' }
  ],
  dataList: [],
  pagination: {
    hide: true
  }
});

const refreshData = async () => {
  const params = {
    page: -1,
    size: 20,
    projectCode: props.config.projectCode
  };
  getSingleProjectList(params).then((res) => {
    TableConfig.dataList = res.data.data.data || [];
  });
};

onMounted(() => {
  refreshData();
});
</script>

<style lang="scss" scoped>
.card {
  margin-bottom: 20px;
}

.card-table {
  height: 300px;
  margin-bottom: 20px;
}
</style>
