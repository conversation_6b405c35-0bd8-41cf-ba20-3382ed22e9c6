import { videoTest } from './constantRoutes/video';

const testRouter = () => {
  if (import.meta.env.MODE === 'development') {
    return [
      {
        path: '/Test',
        component: () => import('@/views/layout/frame/Layout.vue'),
        hidden: false,
        alwaysShow: true,
        name: 'Test',
        meta: {
          title: '开发测试',
          icon: 'iconfont icon-camera',
          roles: ['CUSTOMER_USER', 'TENANT_ADMIN', 'TENANT_SUPPORT']
        },
        children: [videoTest]
      }
    ];
  }
  return [];
};
export default testRouter();
