package org.thingsboard.server.dao.sql.smartOperation.construction;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionContract;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionContractContainer;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoSimpleConstructionContract;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionContractPageRequest;

import java.util.List;

@Mapper
public interface SoConstructionContractMapper extends BaseMapper<SoConstructionContract> {
    List<SoConstructionContractContainer> findByPage(SoConstructionContractPageRequest request);

    long countByPage(SoConstructionContractPageRequest request);

    boolean update(SoConstructionContract entity);

    boolean updateFully(SoConstructionContract entity);

    boolean isCodeExists(@Param("code") String code, @Param("tenantId") String tenantId, @Param("id") String id);

    String getConstructionCodeById(String id);

    IPage<SoSimpleConstructionContract> findSimple(SoConstructionContractPageRequest request);

    boolean isCompleteByContractCode(@Param("code") String code, @Param("tenantId") String tenantId);

}
