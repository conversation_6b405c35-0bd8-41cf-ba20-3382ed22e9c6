package org.thingsboard.server.dao.model.sql.smartProduction.circuit;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.sql.smartProduction.circuit.CircuitConfigMapper;
import org.thingsboard.server.dao.util.imodel.response.annotations.*;

import java.util.Date;

@Getter
@Setter
@ResponseEntity
@TableName("sp_circuit_template")
public class CircuitTemplate {
    // id
    @TableId
    private String id;

    // 配置类型，用于数据隔离。三种类型：水源、水厂、二供泵房。
    private String type;

    // 巡检模板名称
    private String name;

    // 巡检项目，巡检配置的ID。多个用逗号分隔
    @InfoViaMapper(mapper = CircuitConfigMapper.class, name = "itemTypes")
    private String settings;

    // 备注
    private String remark;

    // 创建人
    @ParseUsername
    private String creator;

    // 创建时间
    private Date createTime;

    // 租户ID
    @ParseTenantName
    private String tenantId;

}
