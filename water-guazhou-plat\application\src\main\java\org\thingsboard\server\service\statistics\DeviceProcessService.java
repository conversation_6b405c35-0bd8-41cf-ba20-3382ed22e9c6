package org.thingsboard.server.service.statistics;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.thingsboard.rule.engine.api.MailService;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.Device;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.User;
import org.thingsboard.server.common.data.VO.AlarmLinkedUser;
import org.thingsboard.server.common.data.alarm.*;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.kv.AttributeKvEntry;
import org.thingsboard.server.common.data.utils.AlarmUtils;
import org.thingsboard.server.common.data.utils.DateUtils;
import org.thingsboard.server.dao.alarm.AlarmService;
import org.thingsboard.server.dao.alarmV2.AlarmCenterService;
import org.thingsboard.server.dao.attributes.AttributesService;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.model.sql.DeviceLogEntity;
import org.thingsboard.server.dao.model.sql.ProjectEntity;
import org.thingsboard.server.dao.model.sql.StationAttrEntity;
import org.thingsboard.server.dao.model.sql.StationEntity;
import org.thingsboard.server.dao.model.sql.alarmV2.AlarmCenter;
import org.thingsboard.server.dao.msg.JinzhouMsgSendService;
import org.thingsboard.server.dao.msgLog.DeviceLogService;
import org.thingsboard.server.dao.project.ProjectRelationService;
import org.thingsboard.server.dao.station.StationAttrService;
import org.thingsboard.server.dao.station.StationService;
import org.thingsboard.server.dao.user.UserCredentialsDao;
import org.thingsboard.server.dao.user.UserService;
import org.thingsboard.server.dao.util.mapping.JacksonUtil;
import org.thingsboard.server.service.mail.MailExecutorService;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/4/15 11:33
 */
@Service
@Slf4j
public class DeviceProcessService {

    @Autowired
    private UserService userService;

    @Autowired
    private AlarmService alarmService;

    @Autowired
    private AttributesService attributesService;

    @Autowired
    private UserCredentialsDao userCredentialsDao;

    @Autowired
    private MailExecutorService mailExecutorService;

    @Autowired
    private MailService mailService;

    @Autowired
    private JinzhouMsgSendService jinzhouMsgSendService;

    @Autowired
    private DeviceLogService deviceLogService;

    @Autowired
    private ProjectRelationService projectRelationService;

    @Autowired
    private StationService stationService;

    @Autowired
    private AlarmCenterService alarmCenterService;

    @Autowired
    private StationAttrService stationAttrService;

    @Value("${msg.version}")
    private String msgVersion;


   public void processDeviceOffline(Device device){
       try {
           AlarmReport alarmReport = new AlarmReport();
           alarmReport.setDeviceName(device.getName());
           alarmReport.setTime(DateUtils.date2Str(System.currentTimeMillis(), DateUtils.DATE_FORMATE_DEFAULT_2));
           Optional<AttributeKvEntry> attributeKvEntry = attributesService.find(device.getTenantId(), device.getId(), DataConstants.SERVER_SCOPE, ModelConstants.LAST_UPDATE_TIME).get();
           long lastUpDateTime = attributeKvEntry.isPresent() ? attributeKvEntry.get().getLongValue().get() : System.currentTimeMillis() - DateUtils.DAY_TIME;
           //当设备上次上线时间距离现在超过15分组，触发掉线报警
           int time = -1;
           if (device.getOfflineInterval().endsWith("m")) {
               time = Integer.parseInt(device.getOfflineInterval().split("m")[0]) * 1000 * 60;
           }
           if (device.getOfflineInterval().endsWith("h")) {
               time = Integer.parseInt(device.getOfflineInterval().split("h")[0]) * 60 * 1000 * 60;
           }
           if (time == -1) {
               return;
           }
           if (System.currentTimeMillis() - lastUpDateTime > time) {
               List<StationEntity> stationList = stationService.findByTenantId(device.getTenantId());
               Map<String, StationEntity> stationMap = stationList.stream().collect(Collectors.toMap(StationEntity::getId, station -> station));
               //确认当前是否有未解除的掉线报警
               List<Alarm> alarms = alarmService.findOnlineByTypeAndDevice(device.getId(), ModelConstants.ALARM_TYPE_OFFLINE).get();
               if (alarms != null && alarms.size() > 0) {
                   try {
                       Alarm alarm = alarms.get(0);
                       if (alarm.getStatus() == AlarmStatus.RESTORE_ACK) {
                           alarm.setStatus(AlarmStatus.CONFIRM_UNACK);
                           ArrayNode objectNode = (ArrayNode) alarm.getDetails().get(DataConstants.ALARM_RECORDING);
                           objectNode.add(JacksonUtil.toJsonNode(new AlarmConf(System.currentTimeMillis(), "设备掉线", DataConstants.ALARM)));
                           ObjectNode details = (ObjectNode) alarm.getDetails();
                           details.put(DataConstants.ALARM_RECORDING, AlarmUtils.convertAlarm(objectNode));
                           alarm.setDetails(details);
                           alarmService.createOrUpdateAlarm(alarm);
                           sendEmailAndSms(alarm, device, alarmReport);
                       }
                   } catch (Exception e) {
                       e.printStackTrace();
                   }
               } else {
                   Alarm alm = new Alarm();
                   alm.setStartTs(System.currentTimeMillis());
                   alm.setType(ModelConstants.ALARM_TYPE_OFFLINE);
                   alm.setOriginator(device.getId());
                   alm.setStatus(AlarmStatus.CONFIRM_UNACK);
                   alm.setTenantId(device.getTenantId());
                   JsonNode jsonNode = JacksonUtil.toJsonNode(new AlarmType(ModelConstants.ALARM_TYPE_OFFLINE));
                   ObjectNode objectNode = (ObjectNode) jsonNode;
                   List<AlarmConf> confs = new ArrayList<>();
                   confs.add(new AlarmConf(System.currentTimeMillis(), "设备掉线", DataConstants.ALARM));
                   objectNode.put(DataConstants.ALARM_RECORDING, JacksonUtil.toJsonNode(confs));
                   alm.setDetails(objectNode);
                   alm.setSeverity("紧急");
                   Alarm result = alarmService.createOrUpdateAlarm(alm);
                   sendEmailAndSms(result, device, alarmReport);
               }

               // V2报警
               // 检查是否已存在报警
               Integer count = alarmCenterService.countAlarmByAlarmRuleId(UUIDConverter.fromTimeUUID(device.getUuidId()));
               if (count == 0) {// 未有离线告警
                   // 查询该设备相关站点
                   List<StationAttrEntity> stationAttrList = stationAttrService.findByDeviceId(device.getUuidId().toString());
                   List<String> stationIdList = stationAttrList.stream()
                           .map(StationAttrEntity::getStationId)
                           .distinct()
                           .collect(Collectors.toList());

                   Date now = new Date();
                   SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                   if (stationIdList.size() > 0) {
                       for (String stationId : stationIdList) {
                           StationEntity station = stationMap.get(stationId);
                           if (station == null) {
                               continue;
                           }
                           AlarmCenter alarmCenter = new AlarmCenter();
                           alarmCenter.setStationId(stationId);
                           alarmCenter.setTitle(station.getName() + "的[" + device.getName() + "]" + "-离线");
                           alarmCenter.setTime(now);
                           alarmCenter.setAlarmType("离线告警");
                           alarmCenter.setAlarmLevel("3");// 紧急告警
                           alarmCenter.setAlarmInfo(station.getName() + "的[" + device.getName() + "]设备在" + dateFormat.format(now) + "监测到离线, 请及时查看处理!");
                           alarmCenter.setAlarmStatus(DataConstants.ALARMV2_ALARM_STATUS.NEW.getValue());// 报警中
                           alarmCenter.setProcessStatus(DataConstants.ALARMV2_PROCESS_STATUS.NEW.getValue());// 未处理
                           alarmCenter.setTenantId(UUIDConverter.fromTimeUUID(device.getTenantId().getId()));
                           alarmCenter.setAlarmRuleId(UUIDConverter.fromTimeUUID(device.getUuidId()));
                           alarmCenter.setDeviceId(UUIDConverter.fromTimeUUID(device.getUuidId()));

                           alarmCenterService.save(alarmCenter);
                           // send sms
                           sendSms(alarmCenter, device.getTenantId());
                       }
                   }

               }
           }
       } catch (InterruptedException e) {
           e.printStackTrace();
       } catch (ExecutionException e) {
           e.printStackTrace();
       }
   }

    private void sendSms(AlarmCenter alarmCenter, TenantId tenantId) {
        List<User> users = userService.findUserByTenant(tenantId);
        List<User> sendSmsUser = new ArrayList<>();
        users.forEach(user -> {
            //新增在进行报警时，先判断该用户是否已经激活
            if (userCredentialsDao.findByUserId(user.getTenantId(), user.getUuidId()) == null
                    || !userCredentialsDao.findByUserId(user.getTenantId(), user.getUuidId()).isEnabled()) {
                return;
            }
            try {
                JSONObject info = new JSONObject();
                if (user.getAdditionalInfo() != null) {
                    info = JSONObject.parseObject(user.getAdditionalInfo().toString());
                }
                if (info.getString(ModelConstants.ALARM_FORM_SMS) != null && info.getString(ModelConstants.ALARM_FORM_SMS).equals(ModelConstants.ALARM_RELEASE_NOT)) {
                    sendSmsUser.add(user);
                }
            } catch (Exception e) {
                log.error("解析用户数据失败");
            }

        });
        if ("jinzhou".equals(msgVersion)) {// 锦州短信
            mailExecutorService.execute(() -> {
                sendSmsUser.forEach(user -> {
                    if (StringUtils.isNotBlank(user.getPhone())) {
                        jinzhouMsgSendService.sendMsg(user.getPhone(), alarmCenter.getAlarmInfo());
                    }
                });
            });
        }

    }


    private void sendEmailAndSms(Alarm alarm, Device device, AlarmReport alarmReport) {
        List<User> users = userService.findUserByTenant(device.getTenantId());
        List<User> sendEmailUser = new ArrayList<>();
        List<User> sendSmsUser = new ArrayList<>();
        users.forEach(user -> {
            //新增在进行报警时，先判断该用户是否已经激活
            if (userCredentialsDao.findByUserId(user.getTenantId(), user.getUuidId()) == null
                    || !userCredentialsDao.findByUserId(user.getTenantId(), user.getUuidId()).isEnabled()) {
                return;
            }
            try {
                Map info = JacksonUtil.fromString(user.getAdditionalInfo().asText(), Map.class);
                if (info.get(ModelConstants.ALARM_FORM_EMAIL) != null && info.get(ModelConstants.ALARM_FORM_EMAIL).equals(ModelConstants.ALARM_RELEASE_NOT)) {
                    sendEmailUser.add(user);
                }
                if (info.get(ModelConstants.ALARM_FORM_SMS) != null && info.get(ModelConstants.ALARM_FORM_SMS).equals(ModelConstants.ALARM_RELEASE_NOT)) {
                    sendSmsUser.add(user);
                }
            } catch (Exception e) {
                e.getLocalizedMessage();
            }

        });
        String subject = "掉线报警提醒";
        String emailBody = getEmailBody(device, alarm);
        createDeviceLog(device);
        mailExecutorService.execute(() -> {
            sendEmailUser.forEach(user -> {
                if (user.getEmail() != null) {
                    AlarmLinkedUser alarmLinkedUser = AlarmLinkedUser.builder()
                            .userName(user.getName() + "(" + user.getName() + ")")
                            .email(user.getEmail())
                            .phone(user.getPhone())
                            .tenantId(user.getTenantId())
                            .build();
                    mailService.sendEmail(alarmLinkedUser, subject, emailBody, DataConstants.LOG_TYPE_DEVICE_OFFLINE);
                }
            });
        });
        mailExecutorService.execute(() -> {
            sendSmsUser.forEach(user -> {
                if (user.getPhone()!=null) {
                    mailService.sendOfflineSMS(user, alarmReport);
                }
            });
        });
    }


    public String getEmailBody(Device device, Alarm alarm) {
        String body = "您的设备" + device.getName() + "于" + DateUtils.date2Str(new Date(alarm.getStartTs()), DateUtils.DATE_FORMATE_DEFAULT) + "触发掉线报警，请尽快前往解除！";
        return body;
    }


    private void createDeviceLog(Device device) {
        List<ProjectEntity> projectEntity = projectRelationService.findProjectRelationByEntityTypeAndEntityId(DataConstants.ProjectRelationEntityType.DEVICE.name(), UUIDConverter.fromTimeUUID(device.getUuidId()));
        if (projectEntity != null && projectEntity.size() > 0) {
            DeviceLogEntity deviceLogEntity = DeviceLogEntity.builder()
                    .deviceName(device.getName())
                    .status(DataConstants.DEVICE_LOG_OFFLINE)
                    .updateTime(System.currentTimeMillis())
                    .projectId(projectEntity.get(0).getId())
                    .tenantId(UUIDConverter.fromTimeUUID(device.getTenantId().getId()))
                    .deviceId(UUIDConverter.fromTimeUUID(device.getUuidId()))
                    .projectName(projectEntity.get(0).getName())
                    .build();
            deviceLogService.save(deviceLogEntity);
        }

    }


}
