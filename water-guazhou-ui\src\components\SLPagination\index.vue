<template>
  <el-pagination
    :current-page="config.page || 1"
    :page-size="config.limit || 20"
    :class="config.align || 'left'"
    :layout="config.layout || 'total,sizes, prev, pager, next, jumper'"
    :total="config.total || 0"
    :page-sizes="config.pageSize || [5, 10, 20, 50, 100]"
    @size-change="config.handleSize"
    @current-change="config.handlePage"
    @prev-click="config.handlePage"
    @next-click="config.handlePage"
  />
</template>

<script lang="ts">
import { defineComponent, PropType } from 'vue'
import { ISLPaginationConfig } from './type'

export default defineComponent({
  name: 'SLPagination',
  props: {
    config: {
      type: Object as PropType<ISLPaginationConfig>,
      default: () => {
        //
      }
    }
  },
  setup() {
    return {}
  }
})
</script>

<style lang="scss" scoped>
.left {
  text-align: left;
}
.right {
  text-align: right;
}
</style>
