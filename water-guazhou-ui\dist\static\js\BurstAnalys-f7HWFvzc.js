import{_ as de}from"./Panel-DyoxrWMd.js";import{d as ye,c as N,r as _,b as x,X as ge,W as fe,Q as be,g as ve,n as we,q as j,i as G,F as he,p as xe,_ as Se,aq as Ie,C as Oe}from"./index-r0dFAfgr.js";import{w as Y}from"./MapView-DaoQedLH.js";import{s as M,d as Le,h as Ce}from"./FeatureHelper-Da16o0mu.js";import{s as _e,a as Ge}from"./GPHelper-fLrvVD-A.js";import{i as ke,e as Fe}from"./IdentifyHelper-RJWmLn49.js";import{g as B,a as U,e as Z}from"./LayerHelper-Cn-iiqxI.js";import{e as De,i as J,f as Te,h as Pe,a as ee}from"./QueryHelper-ILO3qZqg.js";import{s as q}from"./ToolHelper-BiiInOzB.js";import{GetFieldConfig as Ne}from"./fieldconfig-Bk3o1wi7.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";import"./Point-WxyopZva.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./pe-B8dP0-Ut.js";import"./geometryEngineBase-BhsKaODW.js";import{g as je}from"./gisUser-Ba96nctf.js";import{b as Ae,g as Ve}from"./poi_burst-B3btyDkV.js";import Ee from"./PipeDetail-CTBPYFJW.js";import"./v4-SoommWqA.js";import"./widget-BcWKanF2.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./identify-4SBo5EZk.js";import"./scaleUtils-DgkF6NQH.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./pipe-nogVzCHG.js";import"./executeForIds-BLdIsxvI.js";import"./project-DUuzYgGl.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./imageBitmapUtils-Db1drMDc.js";import"./ExportImageParameters-BiedgHNY.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./Search-NSrhrIa_.js";import"./FormTableColumnFilter-BT7pLXIC.js";import"./DateFormatter-Bm9a68Ax.js";import"./config-fy91bijz.js";const Re={class:"table-box"},Me=ye({__name:"BurstAnalys",props:{view:{},telport:{}},setup(te){const ae=N(),z=N(),A=N(),s=te,i=_({tabs:[],curOperate:""}),e={identifyResult:void 0,markLayer:void 0,mapClick:void 0,queryParams:ke(),jobid:"",valveFlag:!1,deviceids:[],devicelayers:["阀门","计量装置"],mustShutVolveFeatures:[]},F=_({columns:[{label:"管线类型",prop:"layerName"},{label:"管线编号",prop:"value"}],dataList:[],pagination:{hide:!0}}),D=_({data:[],columns:[]}),I=_({handleRowDbClick:t=>{var r;const a=e.mustShutVolveFeatures.find(n=>n.attributes.SID===t.SID);a&&((r=s.view)==null||r.goTo(a))},dataList:[],columns:[{label:"编号",prop:"SID"},{label:"阀门级别",prop:"VALVECLASS"}],pagination:{hide:!0}}),O=_({handleSelectChange:t=>{var n,o;if(!s.view)return;O.selectList=t||[];const a=t.map(m=>m.SID),r=e.mustShutVolveFeatures.filter(m=>a.indexOf(m.attributes.SID)!==-1).map(m=>(m.symbol=M("point",{color:[0,255,255],outlineColor:[255,0,255],outlineWidth:2}),m));e.extentMustShutValveLayer=B(s.view,{id:"extentMustShutValveLayer",title:"二次关阀"}),(n=e.extentMustShutValveLayer)==null||n.removeAll(),(o=e.extentMustShutValveLayer)==null||o.addMany(r)},dataList:[],columns:[{label:"编号",prop:"SID"},{label:"阀门级别",prop:"VALVECLASS"}],pagination:{hide:!0}}),V=_({dataList:[],columns:[],pagination:{hide:!0,refreshData:({page:t,size:a})=>{V.pagination.page=t,V.pagination.limit=a,X()}}}),E=_({columns:[{prop:"yhbh",label:"用户编号"},{prop:"yhxm",label:"用户姓名"},{prop:"yhdz",label:"用户地址"},{prop:"lxdh",label:"联系电话"},{prop:"dxdh",label:"短信电话"},{prop:"ysxz",label:"用水性质"},{prop:"vnum",label:"阀门编号"},{prop:"sbbh",label:"水表编号"}],dataList:[],pagination:{hide:!0}}),re=_({gutter:12,labelPosition:"top",group:[{fieldset:{desc:"选取管线"},fields:[{type:"btn-group",btns:[{perm:!0,styles:{width:"100%"},type:"warning",text:()=>i.curOperate==="picking"?"正在选取管线":"点击选取管线",loading:()=>i.curOperate==="picking",disabled:()=>["picking","analysing","extendAnalysing","detailing","userDetailing"].indexOf(i.curOperate)!==-1,click:()=>ie()}]},{type:"table",style:{height:"80px"},config:F}]},{fieldset:{desc:"执行分析"},fields:[{type:"btn-group",itemContainerStyle:{marginBottom:0},btns:[{perm:!0,styles:{width:"100%"},loading:()=>i.curOperate==="analysing",text:()=>i.curOperate==="analysing"?"正在分析":"开始分析",disabled:()=>["picking","analysing","extendAnalysing","detailing","userDetailing"].indexOf(i.curOperate)!==-1||!F.dataList.length,click:()=>ne()}]}]},{fieldset:{desc:"分析结果"},fields:[{label:"影响范围概览",type:"checkbox",field:"showInMap",options:[{label:"地图显示",value:"show"}],onChange:t=>{e.resultLayer&&(e.resultLayer.visible=!!t.length)}},{type:"attr-table",style:{minHeight:"50px"},config:D},{type:"table",label:"必关阀",style:{height:"250px"},config:I},{type:"btn-group",itemContainerStyle:{marginBottom:"5px",marginTop:"15px"},btns:[{perm:!0,styles:{width:"100%"},disabled:()=>{var t;return["picking","analysing","extendAnalysing","detailing","userDetailing"].indexOf(i.curOperate)!==-1||!((t=D.columns)!=null&&t.length)},loading:()=>i.curOperate==="detailing",text:()=>i.curOperate==="detailing"?"正在查询":"查看详细结果",click:()=>X()}]},{type:"btn-group",itemContainerStyle:{marginBottom:"0"},btns:[{perm:!0,styles:{width:"100%"},text:()=>i.curOperate==="userDetailing"?"正在查询":"查看受影响用户",loading:()=>i.curOperate==="userDetailing",disabled:()=>["picking","analysing","extendAnalysing","detailing","userDetailing"].indexOf(i.curOperate)!==-1,click:()=>pe()}]}]},{fieldset:{desc:"必关阀拓展分析"},fields:[{type:"table",style:{height:"250px"},label:"选择必关阀",config:O},{type:"btn-group",itemContainerStyle:{marginBottom:"5px",marginTop:"15px"},btns:[{perm:!0,styles:{width:"100%"},loading:()=>i.curOperate==="extendAnalysing",text:()=>i.curOperate==="extendAnalysing"?"正在分析":"二次关阀分析",disabled:()=>{var t;return["picking","analysing","extendAnalysing","detailing","userDetailing"].indexOf(i.curOperate)!==-1||!((t=O.selectList)!=null&&t.length)},click:()=>le()}]},{type:"btn-group",btns:[{perm:!0,styles:{width:"100%"},type:"danger",disabled:()=>["picking","analysing","extendAnalysing","detailing","userDetailing"].indexOf(i.curOperate)!==-1,text:"清除所有",click:()=>ue()}]}]}],defaultValue:{showInMap:["show"]}}),ie=()=>{s.view&&(q("crosshair"),i.curOperate="picking",e.markLayer=B(s.view,{id:"burst-analys",title:"爆管标注"}),e.mapClick=s.view.on("click",async t=>{var a;(a=e.markLayer)==null||a.removeAll(),await se(t)}))},se=async t=>{var a,r,n,o,m,y,h,g,b,L,C;if(s.view){try{e.queryParams.layerIds=U(s.view,!0),e.queryParams.geometry=t.mapPoint,e.queryParams.mapExtent=s.view.extent;const d=await Fe(window.SITE_CONFIG.GIS_CONFIG.gisService+window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService,e.queryParams);(a=s.view)==null||a.graphics.removeAll();const c=(r=d.results)==null?void 0:r.filter(f=>{var l,p;return((p=(l=f.feature)==null?void 0:l.geometry)==null?void 0:p.type)==="polyline"});if(!(c!=null&&c.length)){x.warning("没有查询到管线"),i.curOperate="";return}e.identifyResult=c&&c[0],e.burstPoint=c&&Le((o=(n=c[0])==null?void 0:n.feature)==null?void 0:o.geometry,t.mapPoint);const w=e.burstPoint&&Ce(e.burstPoint.x,e.burstPoint.y,{picUrl:Ae,spatialReference:(m=s.view)==null?void 0:m.spatialReference,yOffset:8}),u=c[0].feature;u&&(u.symbol=M(u.geometry.type)),c.length&&((y=e.markLayer)==null||y.add(u)),(h=e.markLayer)==null||h.add(w);const S=c.map(f=>{var l,p;return{layerName:f.layerName,layerId:f.layerId,value:(l=f.feature.attributes)==null?void 0:l.OBJECTID,attributes:(p=f.feature)==null?void 0:p.attributes}})||[];F.dataList=S.length&&[S[0]]||[];const v=u==null?void 0:u.geometry.extent;if(v){const f=v.xmax-v.xmin,l=v.ymax-v.ymin,p=v.xmin-f/2,T=v.xmax+f/2,P=v.ymin-l/2,k=v.ymax+l/2;(g=s.view)==null||g.goTo(new Y({xmin:p,ymin:P,xmax:T,ymax:k,spatialReference:s.view.spatialReference})),q(""),(b=e.mapClick)!=null&&b.remove&&((L=e.mapClick)==null||L.remove())}}catch{(C=s.view)==null||C.graphics.removeAll(),i.curOperate=""}i.curOperate="picked"}},ne=async()=>{var t,a,r,n,o,m,y,h;if(s.view){i.curOperate="analysing";try{e.resultLayer&&((t=s.view)==null||t.map.remove(e.resultLayer)),e.extendResultLayer&&((a=s.view)==null||a.map.remove(e.extendResultLayer));const b=(n=(r=(await ge(F.dataList[0].layerId)).data)==null?void 0:r.result)==null?void 0:n.rows,L=(b==null?void 0:b.length)&&b[0].layerdbname,C=(o=e.identifyResult)==null?void 0:o.feature.attributes.OBJECTID,d=await _e(L,C);if(await d.waitForJobCompletion(),d.jobStatus==="job-succeeded"){e.jobid=d.jobId,e.resultLayer=await d.fetchResultMapImageLayer(d.jobId),e.resultLayer.title="爆管分析结果";const c=Z(s.view);(m=s.view)==null||m.map.add(e.resultLayer,c);const u=(await d.fetchResultData("summary")).value;(u==null?void 0:u.code)!==1e4?x.error(u.error):(e.resultSummary=(y=u==null?void 0:u.result)==null?void 0:y.summary,H()),i.tabs=e.resultSummary.layersummary.map(S=>({label:S.layername,name:S.layername,data:[]})),await R(i.tabs,0),await W(),e.mustShutValveLayer=B(s.view,{id:"mustShutValveLayer",title:"必关阀"}),(h=e.mustShutValveLayer)==null||h.addMany(e.mustShutVolveFeatures)}else d.jobStatus==="job-cancelled"?x.info("已取消分析"):d.jobStatus==="job-cancelling"?x.info("任务正在取消"):d.jobStatus==="job-failed"&&x.info("分析失败，请联系管理员")}catch{x.info("分析失败，请联系管理员"),i.curOperate="picked"}i.curOperate="analysed"}},R=async(t,a)=>{if(a<t.length){const r=t[a];r.data=await Q(r.name,0),a<t.length-1&&await R(t,++a)}},Q=async(t,a)=>{var r,n;try{let o=await De(((i.curOperate==="extendAnalysing"?(r=e.extendResultLayer)==null?void 0:r.url:(n=e.resultLayer)==null?void 0:n.url)||"")+"/"+a,J({where:"layername='"+t+"'",orderByFields:["OBJECTID asc"],returnGeometry:!1}));return o===null&&(o=await Q(t,++a)),o}catch{return[]}},W=async()=>{var t,a,r,n,o,m,y;if(s.view){try{I.loading=!0,O.loading=!0;const g=oe(i.curOperate==="extendAnalysing"?window.SITE_CONFIG.GIS_CONFIG.gisService+window.SITE_CONFIG.GIS_CONFIG.gisExtendShutValveAnalysGPService:window.SITE_CONFIG.GIS_CONFIG.gisService+window.SITE_CONFIG.GIS_CONFIG.gisShutValveAnalysGPService)+e.jobid,L=((a=(t=(await Ve(e.jobid)).data)==null?void 0:t.layers)==null?void 0:a.filter(l=>l.geometryType==="esriGeometryPoint").map(l=>l.id))||[],C=await Te(g,Pe({layerIds:L||[0,1],searchFields:["mustshut"],returnGeometry:!0,searchText:"1",contains:!1})),d=[],c=[];(r=C.results)==null||r.map(l=>{var P;if(!((P=l.feature)!=null&&P.attributes))return;const p=l.feature.attributes,T=d.find(k=>k.name===p.layername);if(T)T.data.push(p==null?void 0:p.OBJECTID);else{const k=p.layername;d.push({label:k,name:k,id:l.layerId,data:[p.OBJECTID]})}});const w=d.find(l=>l.name==="阀门");if(!w){I.dataList=[],O.dataList=[],I.loading=!1,O.loading=!1;return}const u=await U(s.view,void 0,void 0,"阀门"),S=w==null?void 0:w.id,v=await Ne("阀门"),f=await ee(window.SITE_CONFIG.GIS_CONFIG.gisService+window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService+"/"+u[0],J({objectIds:w==null?void 0:w.data,outFields:((m=(o=(n=v.data)==null?void 0:n.result)==null?void 0:o.rows)==null?void 0:m.filter(l=>l.visible).map(l=>l.name))||[],returnGeometry:!0}));(y=f.features)==null||y.map(l=>{c.push({...l.attributes||{},layerId:S}),l.symbol=M("point",{color:[0,255,255],outlineColor:[255,0,255],outlineWidth:2})}),e.mustShutVolveFeatures=f.features,I.dataList=c,O.dataList=c,I.selectList=c}catch(h){x.error("必关阀查询失败"),console.dir(h)}I.loading=!1,O.loading=!1}},H=()=>{var h,g,b,L,C,d,c,w,u,S,v;const t=e.resultSummary;if((h=t==null?void 0:t.layersummary)!=null&&h.length){const f={},l=[];t.layersummary.forEach(p=>{f[p.layerdbname]=p.geometrytype==="esriGeometryPoint"?p.count+"个":p.length+"米",l.push([{label:p.layername,prop:p.layerdbname}])}),D.data=f,D.columns=l}let a=t.xmin||((b=(g=s.view)==null?void 0:g.extent)==null?void 0:b.xmin),r=t.xmax||((C=(L=s.view)==null?void 0:L.extent)==null?void 0:C.xmax),n=t.ymin||((c=(d=s.view)==null?void 0:d.extent)==null?void 0:c.ymin),o=t.ymax||((u=(w=s.view)==null?void 0:w.extent)==null?void 0:u.ymax);const m=r-a,y=o-n;a-=m/2,r+=m/2,n-=y/2,o+=y/2,(v=s.view)==null||v.goTo(new Y({xmin:a,ymin:n,xmax:r,ymax:o,spatialReference:(S=s.view)==null?void 0:S.spatialReference}))},oe=t=>{t=t||window.SITE_CONFIG.GIS_CONFIG.gisService+window.SITE_CONFIG.GIS_CONFIG.gisBurstGPService;const a=/GPServer.+/;return t.replace(a,"MapServer/jobs/")},X=async()=>{var t;i.curOperate="detailing",(t=A.value)==null||t.openDialog()},le=async()=>{var t,a;i.curOperate="extendAnalysing";try{e.resultLayer&&((t=s.view)==null||t.map.remove(e.resultLayer));const r=((a=I.selectList)==null?void 0:a.map(n=>n.OBJECTID))||[];await me("Valve",r),i.curOperate="extendAnalysed"}catch{i.curOperate="analysed"}},me=async(t,a)=>{var n,o;const r=await Ge({bysource:!0,usertoken:fe().gToken,valves:t+":"+a.join(",")});if(await r.waitForJobCompletion(),r.jobStatus==="job-succeeded"){e.jobid=r.jobId;const m=await r.fetchResultMapImageLayer(r.jobId);e.extendResultLayer=m,e.extendResultLayer.title="二次关阀分析结果";const y=Z(s.view);(n=s.view)==null||n.map.add(e.extendResultLayer,y);const g=(await r.fetchResultData("summary")).value;(g==null?void 0:g.code)!==1e4?x.error(g.error):(e.resultSummary=(o=g==null?void 0:g.result)==null?void 0:o.summary,H()),i.tabs=e.resultSummary.layersummary.map(b=>({label:b.layername,name:b.layername,data:[]})),await R(i.tabs,0),await W()}else r.jobStatus==="job-cancelled"?x.info("已取消分析"):r.jobStatus==="job-cancelling"?x.info("任务正在取消"):r.jobStatus==="job-failed"&&(x.info("分析失败，请联系管理员"),i.curOperate="analysed")},pe=async()=>{var t;i.curOperate="userDetailing";try{e.deviceids=[],await $(0);const a=await je(e.deviceids);E.dataList=a.data.Data.rows,N}catch{x.error("获取用户信息失败"),i.curOperate="analysed";return}i.curOperate="viewingUserDetail",(t=z.value)==null||t.Open()},$=async t=>{var r;const a=e.devicelayers[t];if(!(!s.view||!a))try{const n=await U(s.view,void 0,void 0,a),o=(r=i.tabs.find(y=>y.name===a))==null?void 0:r.data;(await ee(window.SITE_CONFIG.GIS_CONFIG.gisService+window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService+"/"+n,J({outFields:["SID"],objectIds:o,returnGeometry:!1}))).features.map(y=>{e.deviceids.push(y.attributes.SID)}),t<e.devicelayers.length-1&&await $(++t)}catch(n){console.dir(n)}},ce=async()=>{var t;s.view&&((t=A.value)==null||t.extentTo(s.view))},ue=()=>{K(),V.dataList=[],D.data=[],O.dataList=[],I.dataList=[],F.dataList=[],E.dataList=[]},K=()=>{var t,a,r,n,o,m;q(""),e.resultLayer&&((t=s.view)==null||t.map.remove(e.resultLayer)),e.extendResultLayer&&((a=s.view)==null||a.map.remove(e.extendResultLayer)),e.markLayer&&((r=s.view)==null||r.map.remove(e.markLayer)),e.mustShutValveLayer&&((n=s.view)==null||n.map.remove(e.mustShutValveLayer)),e.extentMustShutValveLayer&&((o=s.view)==null||o.map.remove(e.extentMustShutValveLayer)),(m=e.mapClick)!=null&&m.remove&&e.mapClick.remove()};return be(()=>{var t,a,r;K(),(t=e.mapClick)!=null&&t.remove&&((a=e.mapClick)==null||a.remove()),e.markLayer&&((r=s.view)==null||r.map.remove(e.markLayer))}),(t,a)=>{const r=Se,n=Ie,o=de;return ve(),we("div",null,[j(r,{ref_key:"refForm",ref:ae,config:G(re)},null,8,["config"]),j(Ee,{ref_key:"refDetail",ref:A,tabs:G(i).tabs,telport:t.telport,onRefreshed:a[0]||(a[0]=()=>G(i).curOperate="viewingDetail"),onRefreshing:a[1]||(a[1]=()=>G(i).curOperate="detailing"),onClose:a[2]||(a[2]=()=>G(i).curOperate==="analysed"),onRowdblclick:ce},null,8,["tabs","telport"]),j(o,{ref_key:"refPanel_User",ref:z,"custom-class":"gis-detail-panel",draggable:!1,"max-min":!0,title:"受影响用户详情","before-close":()=>G(i).curOperate="analysed"},{default:he(()=>[xe("div",Re,[j(n,{config:G(E)},null,8,["config"])])]),_:1},8,["before-close"])])}}}),ga=Oe(Me,[["__scopeId","data-v-b3ada187"]]);export{ga as default};
