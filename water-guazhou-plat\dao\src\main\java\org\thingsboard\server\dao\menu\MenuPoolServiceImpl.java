/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.menu;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.id.MenuPoolId;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.menu.Menu;
import org.thingsboard.server.common.data.menu.MenuPool;
import org.thingsboard.server.common.data.menu.MenuPoolVO;
import org.thingsboard.server.dao.exception.DataValidationException;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.service.DataValidator;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;

import static org.thingsboard.server.dao.service.Validator.validateId;

@Service
@Transactional
@Slf4j
public class MenuPoolServiceImpl implements MenuPoolService {

    public static final String INCORRECT_MENU_POOL_ID = "Incorrect tenantId ";

    @Autowired
    private MenuPoolDao menuPoolDao;

    private DataValidator<MenuPool> menuPoolDataValidator = new DataValidator<MenuPool>() {
        @Override
        public void validate(MenuPool data, Function<MenuPool, TenantId> menuPoolTenantIdFunction) {
            if (StringUtils.isEmpty(data.getDefaultName())) {
                throw new DataValidationException("MenuPool default should be specified!");
            }
            if (data.getType() == null) {
                throw new DataValidationException("type should be specified!");
            }
            if (data.getStatus() == null) {
                data.setStatus(ModelConstants.MENU_POOL_STATUS_DISPLAY);
            }
            if (data.getFlagDelete() == null) {
                data.setFlagDelete(ModelConstants.MENU_POOL_FLAG_DELETE_FALSE);
            }
        }

    };
//
//    private DataValidator<MenuPool> menuPoolDataValidator = new DataValidator<MenuPool>() {
//        @Override
//        protected void validateDataImpl(MenuPool data) {
//            if (StringUtils.isEmpty(data.getDefaultName())) {
//                throw new DataValidationException("MenuPool default should be specified!");
//            }
//            if (data.getType() == null) {
//                throw new DataValidationException("type should be specified!");
//            }
//            if (data.getStatus() == null) {
//                data.setStatus(ModelConstants.MENU_POOL_STATUS_DISPLAY);
//            }
//            if (data.getFlagDelete() == null) {
//                data.setFlagDelete(ModelConstants.MENU_POOL_FLAG_DELETE_FALSE);
//            }
//        }
//    };

    @Override
    public List<Menu> findMenuByParentId(MenuPoolId parentId) {
        log.trace("Executing findMenuPoolByParentId [{}]", parentId);
        validateId(parentId, INCORRECT_MENU_POOL_ID + parentId);
        List<MenuPool> menuPoolList = menuPoolDao.findByParentId(parentId);

        return MenuPoolListToMenuList(menuPoolList);
    }

    @Override
    public List<Menu> findAll(MenuPoolId rootId) {
        log.trace("Executing findAll");
        List<Menu> menuList = new ArrayList<>();
        List<MenuPool> menus = menuPoolDao.find();
        // 筛选出所有的一级菜单
        List<MenuPool> pMenuList = menus.stream()
                .filter(menuPool -> menuPool.getParentId().equals(rootId))
                .collect(Collectors.toList());
        // 筛选出所有的二级菜单
        List<MenuPool> cMenuList = menus.stream()
                .filter(menuPool -> !menuPool.getParentId().equals(rootId))
                .collect(Collectors.toList());

        // 遍历一级菜单,设置其二级菜单
        for (MenuPool parent : pMenuList) {
            Menu menu = MenuPoolToMenu(parent);
            // 筛选该一级菜单的二级菜单
            List<MenuPool> child = cMenuList.stream()
                    .filter(menuPool -> menuPool.getParentId().equals(parent.getId()))
                    .collect(Collectors.toList());
            if (child.size() > 0) {
                List<Menu> childMenus = MenuPoolListToMenuList(child);
                menu.setChildren(childMenus);
            }
            menuList.add(menu);
        }

        return menuList;
    }


    @Override
    public Menu findById(MenuPoolId id) {
        log.trace("Executing findById [{}]", id);
        return MenuPoolToMenu(menuPoolDao.findById(id.getId()));
    }

    @Override
    public MenuPool saveMenu(Menu menu, MenuPoolId parentId) {
        MenuPool menuPool = MenuToMenuPool(menu);
        menuPool.setParentId(parentId);

        if (menuPool.getId() == null) {
            log.trace("Executing saveMenu [menu = {}, parent = {}]", menuPool);
        } else {
            log.trace("Executing updateMenu [menu = {}, parent = {}]", menuPool);
        }
        if (menuPool.getParentId() == null) {
            menuPool.setParentId(new MenuPoolId(ModelConstants.MENU_POOL_ROOT));
        }

        return menuPoolDao.save(menuPool);
    }

    @Override
    public List<MenuPoolVO> getSelectableTree() {
//        List<MenuPoolVO> tree = new ArrayList<>();
        log.trace("Executing getSelectableTree");
        // 获取根节点下的一级菜单
        List<MenuPool> MenuPoolList = menuPoolDao.getSelectableTree(new MenuPoolId(ModelConstants.MENU_POOL_ROOT));
        /*for (MenuPool menuPool : MenuPoolList) {
            // 获取每个一级菜单的子菜单
            MenuPoolVO vo = MenuPoolVO.toMenuPoolVO(menuPool);
            List<MenuPool> childrenTree = menuPoolDao.getSelectableTree(menuPool.getId());
            if (childrenTree != null && childrenTree.size() > 0) {
                vo.setChildren(MenuPoolListToMenuPoolVOList(childrenTree));
            }

            tree.add(vo);
        }*/
        List<MenuPoolVO> collect = MenuPoolList.stream()
                .map(MenuPoolVO::toMenuPoolVO)
                .peek(menu -> menu.setChildren(getChildren(menu)))
                .collect(Collectors.toList());
        return collect;
    }

    private List<MenuPoolVO> getChildren(MenuPoolVO menu) {
        List<MenuPool> childrenTree = menuPoolDao.getSelectableTree(new MenuPoolId(UUID.fromString(menu.getId())));

        if (childrenTree != null) {
            List<MenuPoolVO> menuPoolVOS = MenuPoolVO.toMenuPoolVOList(childrenTree);
            for (MenuPoolVO menuPoolVO : menuPoolVOS) {
                menuPoolVO.setChildren(getChildren(menuPoolVO));
            }

            return menuPoolVOS;
        }

        return null;
    }

    @Override
    public List<MenuPool> findAll() {
        return menuPoolDao.findAll();
    }

    @Override
    public List<MenuPool> findByParentId(MenuPoolId id) {
        return menuPoolDao.findByParentId(id);
    }

    private List<MenuPoolVO> MenuPoolListToMenuPoolVOList(List<MenuPool> childrenTree) {
        List<MenuPoolVO> result = new ArrayList<>();
        for (MenuPool menuPool : childrenTree) {
            MenuPoolVO vo = MenuPoolVO.toMenuPoolVO(menuPool);
            result.add(vo);
        }
        return result;
    }

}
