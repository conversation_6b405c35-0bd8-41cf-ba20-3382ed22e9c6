import{d as R,ch as z,a0 as C,j as b,cN as x,c as S,am as B,bB as u,o as N,g as p,n as g,q as f,F as d,i as y,aB as P,aJ as T,h as U,p as L,bh as A,bw as D,cO as H,cP as I,cQ as $,cR as F,bO as r,cS as V,cT as O,cU as W,b_ as q,cV as G,bV as Y,C as j}from"./index-r0dFAfgr.js";const J={class:"menu-title"},Q=R({__name:"index",setup(K){const m=z(),i=C(),a=b(),{proxy:_}=x(),c=S(),v=async e=>{if(!i.curNavs.length||!e)return;const n=a.appid&&e.id===a.appid;if(a.SET_appname(e.name),a.SET_appid(e.id),e.type==="2"){const t=H(e.applicationUrl,"type");let s="";if(a.SET_isFrameApp(!0),t){if(t.value==="yingshou"){const l=await I();s=e.applicationUrl.split("?")[0]+"?username="+l.data.username+"&password="+l.data.password}else t.value==="yingshou2"?s=e.applicationUrl.split("?")[0]+"?o="+$():s=e.applicationUrl;window.open(s)}else window.open(e.applicationUrl)}else if(a.SET_isFrameApp(!1),await F(),n){const t=r.currentRoute.value.fullPath==="/"?V(m.addRouters[0]):r.currentRoute.value.fullPath;r.push({path:t||"/"})}else{const t=O(m.addRouters[0]);r.push(t)}};B(()=>i.curNavs,async()=>{await u(),w(),E()});const k=e=>{const n=e.wheelDelta||-e.deltaY*40;_.$refs.elMenuHorizontalScrollRef.wrap$.scrollLeft-=n/4},E=async()=>{await u();const e=document.querySelector(".el-menu.el-menu--horizontal li.is-active");if(!e)return!1;_.$refs.elMenuHorizontalScrollRef.wrap$.scrollLeft=e.offsetLeft},h=S(),w=()=>{if(!h.value||!c.value)return;const e=document.getElementsByClassName("horizontal-menu-item");let n=0;for(let t=0;t<e.length;t++)n+=e[t].clientWidth;c.value.$el.style.width=n+"px"};return N(async()=>{await u(),w()}),(e,n)=>{const t=W,s=q,l=G,M=Y;return p(),g("div",{ref_key:"refContainer",ref:h,class:"el-menu-horizontal-warp"},[f(M,{ref:"elMenuHorizontalScrollRef","min-size":8,onWheel:D(k,["prevent"])},{default:d(()=>[f(l,{ref_key:"refMenu",ref:c,"default-active":y(a).appid,"background-color":"transparent",mode:"horizontal",ellipsis:!1},{default:d(()=>[(p(!0),g(P,null,T(y(i).curNavs,o=>(p(),U(s,{key:o.id,class:"horizontal-menu-item",index:o.id,onClick:()=>v(o)},{title:d(()=>[f(t,{class:"image-icon",src:o.img,fit:"fill",alt:""},null,8,["src"]),L("span",J,A(o.name),1)]),_:2},1032,["index","onClick"]))),128))]),_:1},8,["default-active"])]),_:1},512)],512)}}}),Z=j(Q,[["__scopeId","data-v-3305fce4"]]);export{Z as default};
