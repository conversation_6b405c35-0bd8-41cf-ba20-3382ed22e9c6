package org.thingsboard.server.controller.leakDetectionManagement;

import lombok.Data;

/**
 * 用水分析数据传输对象
 */
@Data
public class WaterAnalysisDto {
    /**
     * 分区ID
     */
    private String partitionId;
    
    /**
     * 分区名称
     */
    private String partitionName;
    
    /**
     * 总用户数
     */
    private int totalUsers;
    
    /**
     * 正常用户数
     */
    private int normalUsers;
    
    /**
     * 用水异常用户数
     */
    private int abnormalWaterUsers;
    
    /**
     * 流量异常数
     */
    private int abnormalFlow;
    
    /**
     * 表计异常数
     */
    private int abnormalMeter;
} 