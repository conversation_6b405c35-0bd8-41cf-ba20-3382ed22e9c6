package org.thingsboard.server.dao.supplier.qualifications;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.dao.model.sql.SupplierQualificationsEntity;
import org.thingsboard.server.dao.sql.supplier.qualifications.SupplierQualificationsMapper;

import java.util.List;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-08-24
 */
@Slf4j
@Service
@Transactional
public class SupplierQualificationsServiceImpl implements SupplierQualificationsService {
    @Autowired
    private SupplierQualificationsMapper supplierQualificationsMapper;

    @Override
    public List<SupplierQualificationsEntity> getByMainId(String mainId) {
        QueryWrapper<SupplierQualificationsEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("main_id", mainId);
        queryWrapper.orderByDesc("create_time");

        return supplierQualificationsMapper.selectList(queryWrapper);
    }
}
