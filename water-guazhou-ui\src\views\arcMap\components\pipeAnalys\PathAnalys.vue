<template>
  <div>
    <Form ref="refForm" :config="FormConfig"> </Form>

    <PipeDetail
      ref="refDetail"
      :tabs="state.tabs"
      :telport="telport"
      @refreshed="() => (state.curOperate = 'viewingDetail')"
      @refreshing="() => (state.curOperate = 'detailing')"
      @close="() => state.curOperate === 'analysed'"
      @rowdblclick="handleLocate"
    ></PipeDetail>
  </div>
</template>
<script lang="ts" setup>
import Extent from '@arcgis/core/geometry/Extent.js';
import {
  excuteIdentify,
  excuteQueryForIds,
  getGraphicLayer,
  getPipeMapLayerMinIndex,
  getSubLayerIds,
  initIdentifyParams,
  initQueryParams,
  setMapCursor,
  setSymbol,
  submitGPJob
} from '@/utils/MapHelper';
import { SLMessage } from '@/utils/Message';
import { queryLayerClassName } from '@/api/mapservice';
import { useGisStore } from '@/store';
import PipeDetail from '../common/PipeDetail.vue';

const refDetail = ref<InstanceType<typeof PipeDetail>>();
const props = defineProps<{
  view?: __esri.MapView;
  telport?: string;
}>();
const state = reactive<{
  curOperate:
    | 'picking'
    | 'picked'
    | 'analysing'
    | 'analysed'
    | 'detailing'
    | 'viewingDetail'
    | '';
  tabs: IPipeDetailTab[];
}>({
  curOperate: '',
  tabs: []
});
const staticState: {
  pickedFeatures: any[];
  graphicsLayer?: __esri.GraphicsLayer;
  resultLayer?: __esri.MapImageLayer;
  mapClick?: any;
  resultSummary?: any;
  jobId?: string;
} = {
  pickedFeatures: [],
  graphicsLayer: undefined,
  resultLayer: undefined,
  mapClick: undefined
};
const refForm = ref<IFormIns>();
const TableConfig_Pipe = reactive<ITable>({
  columns: [
    { label: '管线类型', prop: 'layerName' },
    { label: '管线编号', prop: 'OBJECTID' }
  ],
  dataList: [],
  pagination: {
    hide: true
  }
});
const TableConfig_AnalysResut = reactive<ITable>({
  columns: [
    { label: '图层名称', prop: 'layername' },
    { label: '管线数量', prop: 'count' },
    {
      label: '管线长度',
      prop: 'length',
      formatter: (row, val) => {
        return val + ' 米';
      }
    }
  ],
  dataList: [],
  pagination: {
    hide: true
  }
});
const FormConfig = reactive<IFormConfig>({
  group: [
    {
      fieldset: {
        desc: '选取管线'
      },
      fields: [
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              type: 'warning',
              text: () =>
                state.curOperate === 'picking'
                  ? '正在选取管线'
                  : '点击并依次选取两条管线',
              click: () => startPick(),
              loading: () => state.curOperate === 'picking',
              disabled: () => state.curOperate === 'analysing',
              styles: {
                width: '100%'
              }
            }
          ]
        },
        {
          type: 'table',
          style: {
            height: '120px'
          },
          label: '所选管线概览',
          config: TableConfig_Pipe
        }
      ]
    },
    {
      fieldset: {
        desc: '执行分析'
      },
      fields: [
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: () =>
                state.curOperate === 'analysing' ? '正在分析' : '开始分析',
              click: () => startAnalys(),
              disabled: () =>
                state.curOperate === 'analysing' ||
                TableConfig_Pipe.dataList.length !== 2,
              loading: () => state.curOperate === 'analysing',
              styles: {
                width: '100%'
              }
            }
          ]
        }
      ]
    },
    {
      fieldset: {
        desc: '分析结果'
      },
      fields: [
        {
          type: 'checkbox',
          field: 'viewInMap',
          options: [{ label: '地图显示', value: 'viewInMap' }],
          onChange: (val) => {
            staticState.resultLayer &&
              (staticState.resultLayer.visible = !!val.length);
          }
        },
        {
          type: 'table',
          style: {
            height: '80'
          },
          config: TableConfig_AnalysResut
        },
        {
          type: 'btn-group',
          itemContainerStyle: {
            marginTop: '20px',
            marginBottom: '8px'
          },
          btns: [
            {
              perm: true,
              text: () =>
                state.curOperate === 'detailing' ? '正在查询' : '查看详细结果',
              loading: () => state.curOperate === 'detailing',
              disabled: () =>
                state.curOperate === 'analysing' ||
                state.curOperate === 'detailing',
              click: () => handleDetail(),
              styles: {
                width: '100%'
              }
            }
          ]
        },
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: '清除所有',
              type: 'danger',
              disabled: () => state.curOperate === 'analysing',
              click: () => clear(),
              styles: {
                width: '100%'
              }
            }
          ]
        }
      ]
    }
  ],
  labelPosition: 'top',
  gutter: 12,
  defaultValue: {
    viewInMap: ['viewInMap']
  }
});
const handleLocate = async () => {
  props.view && refDetail.value?.extentTo(props.view);
};
const handleDetail = () => {
  state.curOperate = 'detailing';
  refDetail.value?.openDialog();
};
const startPick = () => {
  if (props.view) {
    setMapCursor('crosshair');
    TableConfig_Pipe.dataList = [];
    staticState.pickedFeatures = [];
    state.curOperate = 'picking';
    staticState.graphicsLayer = getGraphicLayer(props.view, {
      id: 'path-analys',
      title: '最短路径分析标注'
    });
    staticState.graphicsLayer?.removeAll();
    staticState.resultLayer && props.view.map.remove(staticState.resultLayer);
    staticState.mapClick = props.view?.on('click', async (e) => {
      try {
        await doIdentify(e);
        if (TableConfig_Pipe.dataList.length >= 2) {
          TableConfig_Pipe.dataList.length = 2;
          staticState.mapClick?.remove();
          setMapCursor('');
          staticState.mapClick = undefined;
          state.curOperate = 'picked';
        }
      } catch (error) {
        SLMessage.error('范围内没有管线');
      }
    });
  }
};

const startAnalys = async () => {
  try {
    staticState.resultLayer && props.view?.map.remove(staticState.resultLayer);
    state.curOperate = 'analysing';
    const res = await queryLayerClassName(
      staticState.pickedFeatures[0].layerId
    );
    const datas = res.data?.result?.rows;
    const jobinfo = await submitGPJob(
      window.SITE_CONFIG.GIS_CONFIG.gisService +
        window.SITE_CONFIG.GIS_CONFIG.gisPathAnalysGPService,
      {
        startclassname: datas?.length && datas[0].layerdbname,
        startid: staticState.pickedFeatures[0]?.feature.attributes['OBJECTID'],
        endclassname: datas?.length && datas[0].layerdbname,
        endid: staticState.pickedFeatures[1]?.feature.attributes['OBJECTID'],
        usertoken: useGisStore().gToken
      }
    );
    await jobinfo.waitForJobCompletion();
    if (jobinfo.jobStatus === 'job-succeeded') {
      staticState.jobId = jobinfo.jobId;
      staticState.resultLayer = await jobinfo.fetchResultMapImageLayer(
        jobinfo.jobId
      );
      staticState.resultLayer.title = '最短路径分析结果';
      const pipeMapIndex = getPipeMapLayerMinIndex(props.view);
      props.view?.map.add(staticState.resultLayer, pipeMapIndex);

      const res = await jobinfo.fetchResultData('summary');

      const value: any = res.value;
      if (value?.code !== 10000) {
        SLMessage.error(value.error);
      } else {
        staticState.resultSummary = value?.result?.summary;
        showAnalyzsSummary(value?.result?.summary);
      }
      // 查询详情ids
      state.tabs = staticState.resultSummary.layersummary.map((item) => {
        return {
          label: item.layername,
          name: item.layername,
          data: []
        };
      });
      await setTabOids(state.tabs, 0);
    } else if (jobinfo.jobStatus === 'job-cancelled') {
      SLMessage.info('已取消分析');
    } else if (jobinfo.jobStatus === 'job-cancelling') {
      SLMessage.info('任务正在取消');
    } else if (jobinfo.jobStatus === 'job-failed') {
      SLMessage.info('分析失败，请联系管理员');
    }
  } catch (error) {
    state.curOperate = 'picked';
    console.dir(error);

    SLMessage.info('分析失败，请联系管理员');
    return;
  }
  state.curOperate = 'analysed';
};

const setTabOids = async (tabs, index) => {
  if (index < tabs.length) {
    const tab = tabs[index];
    tab.data = await getTempOids(tab.name, 0);
    index < tabs.length - 1 && (await setTabOids(tabs, ++index));
  }
};
const getTempOids = async (tab: string, layerIndex: number) => {
  try {
    let alloids = await excuteQueryForIds(
      (staticState.resultLayer?.url || '') + '/' + layerIndex,
      initQueryParams({
        where: "layername='" + tab + "'",
        orderByFields: ['OBJECTID asc'],
        returnGeometry: false
      })
    );
    if (alloids === null) {
      alloids = await getTempOids(tab, ++layerIndex);
    }
    return alloids;
  } catch (error) {
    return [];
  }
};
const doIdentify = async (e: any) => {
  if (!props.view) return;
  try {
    const res = await excuteIdentify(
      window.SITE_CONFIG.GIS_CONFIG.gisService +
        window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService,
      initIdentifyParams({
        tolerance: 3,
        geometry: e.mapPoint,
        mapExtent: props.view.extent,
        width: props.view.width,
        layerIds: getSubLayerIds(props.view)
      })
    );
    const result = res.results?.filter(
      (item) => item.feature?.geometry?.type === 'polyline'
    )[0];
    if (result) {
      const data = (result && [result]) || [];
      const tableRow = data.map((item) => {
        return {
          ...(item.feature.attributes || {}),
          layerId: item.layerId,
          layerName: item.layerName
        };
      });
      TableConfig_Pipe.dataList.push(...tableRow);
      staticState.pickedFeatures.push(result);
      if (result) {
        const graphic = result.feature;
        graphic && (graphic.symbol = setSymbol('polyline'));
        staticState.graphicsLayer?.add(graphic);
      }
    } else {
      SLMessage.warning('没有查询到管线');
    }
  } catch (error) {
    console.log('拾取失败');
  }
};

const showAnalyzsSummary = (summary: any) => {
  TableConfig_AnalysResut.dataList = summary.layersummary || [];
  let xmin: number = summary.xmin || props.view?.extent?.xmin;
  let xmax: number = summary.xmax || props.view?.extent?.xmax;
  let ymin: number = summary.ymin || props.view?.extent?.ymin;
  let ymax: number = summary.ymax || props.view?.extent?.ymax;
  const width = xmax - xmin;
  const height = ymax - ymin;
  xmin -= width / 2;
  xmax += width / 2;
  ymin -= height / 2;
  ymax += height / 2;
  props.view?.goTo(
    new Extent({
      xmin,
      ymin,
      xmax,
      ymax,
      spatialReference: props.view?.spatialReference
    })
  );
};
const clear = () => {
  staticState.graphicsLayer &&
    props.view?.map.remove(staticState.graphicsLayer);
  staticState.resultLayer && props.view?.map.remove(staticState.resultLayer);
  staticState.mapClick?.remove();
  staticState.mapClick = undefined;
  TableConfig_Pipe.dataList = [];
  staticState.pickedFeatures = [];
  TableConfig_AnalysResut.dataList = [];
  staticState.resultSummary = undefined;

  setMapCursor('');
};
onBeforeUnmount(() => {
  clear();
});
</script>
<style lang="scss" scoped>
:deep(.el-table__empty-block) {
  min-height: 40px;
  .el-table__empty-text {
    line-height: 40px;
  }
}
</style>
