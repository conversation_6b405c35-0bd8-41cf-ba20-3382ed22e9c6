"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[3975,7873],{65845:(e,t,r)=>{r.d(t,{D:()=>o});var a=r(81153);function o(e){e&&e.writtenProperties&&e.writtenProperties.forEach((({target:e,propName:t,newOrigin:r})=>{(0,a.l)(e)&&r&&e.originOf(t)!==r&&e.updateOrigin(t,r)}))}},81153:(e,t,r)=>{function a(e){return e&&"getAtOrigin"in e&&"originOf"in e}r.d(t,{l:()=>a})},41123:(e,t,r)=>{r.d(t,{D:()=>o});const a="randomUUID"in crypto;function o(){if(a)return crypto.randomUUID();const e=crypto.getRandomValues(new Uint16Array(8));e[3]=4095&e[3]|16384,e[4]=16383&e[4]|32768;const t=t=>e[t].toString(16).padStart(4,"0");return t(0)+t(1)+"-"+t(2)+"-"+t(3)+"-"+t(4)+"-"+t(5)+t(6)+t(7)}},54295:(e,t,r)=>{r.d(t,{V:()=>i});var a=r(43697),o=r(5600),s=(r(75215),r(67676),r(52011));const i=e=>{let t=class extends e{get apiKey(){return this._isOverridden("apiKey")?this._get("apiKey"):"portalItem"in this?this.portalItem?.apiKey:null}set apiKey(e){null!=e?this._override("apiKey",e):(this._clearOverride("apiKey"),this.clear("apiKey","user"))}};return(0,a._)([(0,o.Cb)({type:String})],t.prototype,"apiKey",null),t=(0,a._)([(0,s.j)("esri.layers.mixins.APIKeyMixin")],t),t}},17287:(e,t,r)=>{r.d(t,{Y:()=>p});var a=r(43697),o=r(92604),s=r(70586),i=r(5600),n=(r(75215),r(67676),r(52011)),l=r(66677);const p=e=>{let t=class extends e{get title(){if(this._get("title")&&"defaults"!==this.originOf("title"))return this._get("title");if(this.url){const e=(0,l.Qc)(this.url);if((0,s.pC)(e)&&e.title)return e.title}return this._get("title")||""}set title(e){this._set("title",e)}set url(e){this._set("url",(0,l.Nm)(e,o.Z.getLogger(this.declaredClass)))}};return(0,a._)([(0,i.Cb)()],t.prototype,"title",null),(0,a._)([(0,i.Cb)({type:String})],t.prototype,"url",null),t=(0,a._)([(0,n.j)("esri.layers.mixins.ArcGISService")],t),t}},20559:(e,t,r)=>{r.d(t,{xp:()=>q,Vt:()=>P});var a=r(43697),o=r(3172),s=r(20102),i=r(92604),n=r(70586),l=r(95330),p=r(17452),c=r(5600),u=(r(75215),r(67676),r(71715)),d=r(52011),h=r(30556),y=r(65845),m=r(6570),f=r(79235),g=r(82971),v=r(66677),w=r(21506),S=r(61064);var I=r(65587),_=r(15235),x=r(66643),b=r(41123),R=r(97873);async function C(e,t,r){if(!t||!t.resources)return;const a=t.portalItem===e.portalItem?new Set(e.paths):new Set;e.paths.length=0,e.portalItem=t.portalItem;const o=new Set(t.resources.toKeep.map((e=>e.resource.path))),i=new Set,n=[];o.forEach((t=>{a.delete(t),e.paths.push(t)}));for(const s of t.resources.toUpdate)if(a.delete(s.resource.path),o.has(s.resource.path)||i.has(s.resource.path)){const{resource:t,content:a,finish:o,error:i}=s,l=(0,R.getSiblingOfSameTypeI)(t,(0,b.D)());e.paths.push(l.path),n.push(O({resource:l,content:a,compress:s.compress,finish:o,error:i},r))}else e.paths.push(s.resource.path),n.push(A(s,r)),i.add(s.resource.path);for(const a of t.resources.toAdd)n.push(O(a,r)),e.paths.push(a.resource.path);if(a.forEach((e=>{if(t.portalItem){const r=t.portalItem.resourceFromPath(e);n.push(r.portalItem.removeResource(r).catch((()=>{})))}})),0===n.length)return;const p=await(0,l.as)(n);(0,l.k_)(r);const c=p.filter((e=>"error"in e)).map((e=>e.error));if(c.length>0)throw new s.Z("save:resources","Failed to save one or more resources",{errors:c})}async function O(e,t){const r={...(0,n.pC)(t)?t:{},compress:e.compress},a=await(0,x.q6)(e.resource.portalItem.addResource(e.resource,e.content,r));if(!0!==a.ok)throw e.error?.(a.error),a.error;e.finish?.(e.resource)}async function A(e,t){const r=await(0,x.q6)(e.resource.update(e.content,t));if(!0!==r.ok)throw e.error?.(r.error),r.error;e.finish?.(e.resource)}const N="esri.layers.mixins.SceneService",U=i.Z.getLogger(N),P=e=>{let t=class extends e{constructor(){super(...arguments),this.spatialReference=null,this.fullExtent=null,this.heightModelInfo=null,this.minScale=0,this.maxScale=0,this.version={major:Number.NaN,minor:Number.NaN,versionString:""},this.copyright=null,this.sublayerTitleMode="item-title",this.title=null,this.layerId=null,this.indexInfo=null,this._debouncedSaveOperations=(0,l.Ds)((async(e,t,r)=>{switch(e){case q.SAVE:return this._save(t);case q.SAVE_AS:return this._saveAs(r,t)}}))}readSpatialReference(e,t){return this._readSpatialReference(t)}_readSpatialReference(e){if(null!=e.spatialReference)return g.Z.fromJSON(e.spatialReference);{const t=e.store,r=t.indexCRS||t.geographicCRS,a=r&&parseInt(r.substring(r.lastIndexOf("/")+1,r.length),10);return null!=a?new g.Z(a):null}}readFullExtent(e,t,r){if(null!=e&&"object"==typeof e){const a=null==e.spatialReference?{...e,spatialReference:this._readSpatialReference(t)}:e;return m.Z.fromJSON(a,r)}const a=t.store,o=this._readSpatialReference(t);return null==o||null==a||null==a.extent||!Array.isArray(a.extent)||a.extent.some((e=>e<T))?null:new m.Z({xmin:a.extent[0],ymin:a.extent[1],xmax:a.extent[2],ymax:a.extent[3],spatialReference:o})}parseVersionString(e){const t={major:Number.NaN,minor:Number.NaN,versionString:e},r=e.split(".");return r.length>=2&&(t.major=parseInt(r[0],10),t.minor=parseInt(r[1],10)),t}readVersion(e,t){const r=t.store,a=null!=r.version?r.version.toString():"";return this.parseVersionString(a)}readTitlePortalItem(e){return"item-title"!==this.sublayerTitleMode?void 0:e}readTitleService(e,t){const r=this.portalItem&&this.portalItem.title;if("item-title"===this.sublayerTitleMode)return(0,v.a7)(this.url,t.name);let a=t.name;if(!a&&this.url){const e=(0,v.Qc)(this.url);(0,n.pC)(e)&&(a=e.title)}return"item-title-and-service-name"===this.sublayerTitleMode&&r&&(a=r+" - "+a),(0,v.ld)(a)}set url(e){const t=(0,v.XG)({layer:this,url:e,nonStandardUrlAllowed:!1,logger:U});this._set("url",t.url),null!=t.layerId&&this._set("layerId",t.layerId)}writeUrl(e,t,r,a){(0,v.wH)(this,e,"layers",t,a)}get parsedUrl(){const e=this._get("url"),t=(0,p.mN)(e);return null!=this.layerId&&(t.path=`${t.path}/layers/${this.layerId}`),t}async _fetchIndexAndUpdateExtent(e,t){this.indexInfo=(0,S.T)(this.parsedUrl.path,this.rootNode,e,this.apiKey,U,t),null==this.fullExtent||this.fullExtent.hasZ||this._updateExtent(await this.indexInfo)}_updateExtent(e){if("page"===e?.type){const t=e.rootIndex%e.pageSize,r=e.rootPage?.nodes?.[t];if(null==r||null==r.obb||null==r.obb.center||null==r.obb.halfSize)throw new s.Z("sceneservice:invalid-node-page","Invalid node page.");if(r.obb.center[0]<T||null==this.fullExtent||this.fullExtent.hasZ)return;const a=r.obb.halfSize,o=r.obb.center[2],i=Math.sqrt(a[0]*a[0]+a[1]*a[1]+a[2]*a[2]);this.fullExtent.zmin=o-i,this.fullExtent.zmax=o+i}else if("node"===e?.type){const t=e.rootNode?.mbs;if(!Array.isArray(t)||4!==t.length||t[0]<T)return;const r=t[2],a=t[3],{fullExtent:o}=this;o&&(o.zmin=r-a,o.zmax=r+a)}}async _fetchService(e){if(null==this.url)throw new s.Z("sceneservice:url-not-set","Scene service can not be loaded without valid portal item or url");if(null==this.layerId&&/SceneServer\/*$/i.test(this.url)){const t=await this._fetchFirstLayerId(e);null!=t&&(this.layerId=t)}return this._fetchServiceLayer(e)}async _fetchFirstLayerId(e){const t=await(0,o.default)(this.url,{query:{f:"json",token:this.apiKey},responseType:"json",signal:e});if(t.data&&Array.isArray(t.data.layers)&&t.data.layers.length>0)return t.data.layers[0].id}async _fetchServiceLayer(e){const t=await(0,o.default)(this.parsedUrl?.path??"",{query:{f:"json",token:this.apiKey},responseType:"json",signal:e});t.ssl&&(this.url=this.url.replace(/^http:/i,"https:"));let r=!1;if(t.data.layerType&&"Voxel"===t.data.layerType&&(r=!0),r)return this._fetchVoxelServiceLayer();const a=t.data;this.read(a,this._getServiceContext()),this.validateLayer(a)}async _fetchVoxelServiceLayer(e){const t=(await(0,o.default)(this.parsedUrl?.path+"/layer",{query:{f:"json",token:this.apiKey},responseType:"json",signal:e})).data;this.read(t,this._getServiceContext()),this.validateLayer(t)}_getServiceContext(){return{origin:"service",portalItem:this.portalItem,portal:this.portalItem?.portal,url:this.parsedUrl}}async _ensureLoadBeforeSave(){await this.load(),"beforeSave"in this&&"function"==typeof this.beforeSave&&await this.beforeSave()}validateLayer(e){}_updateTypeKeywords(e,t,r){e.typeKeywords||(e.typeKeywords=[]);const a=t.getTypeKeywords();for(const t of a)e.typeKeywords.push(t);e.typeKeywords&&(e.typeKeywords=e.typeKeywords.filter(((e,t,r)=>r.indexOf(e)===t)),r===K.newItem&&(e.typeKeywords=e.typeKeywords.filter((e=>"Hosted Service"!==e))))}async _saveAs(e,t){const r={...Z,...t};let a=_.default.from(e);a||(U.error("_saveAs(): requires a portal item parameter"),await Promise.reject(new s.Z("sceneservice:portal-item-required","_saveAs() requires a portal item to save to"))),a.id&&(a=a.clone(),a.id=null);const o=a.portal||I.Z.getDefault();await this._ensureLoadBeforeSave(),a.type=j,a.portal=o;const i={origin:"portal-item",url:null,messages:[],portal:o,portalItem:a,writtenProperties:[],blockedRelativeUrls:[],resources:{toAdd:[],toUpdate:[],toKeep:[],pendingOperations:[]}},n={layers:[this.write({},i)]};return await Promise.all(i.resources.pendingOperations??[]),await this._validateAgainstJSONSchema(n,i,r),a.url=this.url,a.title||(a.title=this.title),this._updateTypeKeywords(a,r,K.newItem),await o.signIn(),await(o.user?.addItem({item:a,folder:r&&r.folder,data:n})),await C(this.resourceReferences,i,null),this.portalItem=a,(0,y.D)(i),i.portalItem=a,a}async _save(e){const t={...Z,...e};if(!this.portalItem)throw U.error("_save(): requires the .portalItem property to be set"),new s.Z("sceneservice:portal-item-not-set","Portal item to save to has not been set on this SceneService");if(this.portalItem.type!==j)throw U.error("_save(): Non-matching portal item type. Got "+this.portalItem.type+", expected "+j),new s.Z("sceneservice:portal-item-wrong-type",`Portal item needs to have type "${j}"`);await this._ensureLoadBeforeSave();const r={origin:"portal-item",url:this.portalItem.itemUrl&&(0,p.mN)(this.portalItem.itemUrl),messages:[],portal:this.portalItem.portal||I.Z.getDefault(),portalItem:this.portalItem,writtenProperties:[],blockedRelativeUrls:[],resources:{toAdd:[],toUpdate:[],toKeep:[],pendingOperations:[]}},a={layers:[this.write({},r)]};return await Promise.all(r.resources.pendingOperations??[]),await this._validateAgainstJSONSchema(a,r,t),this.portalItem.url=this.url,this.portalItem.title||(this.portalItem.title=this.title),this._updateTypeKeywords(this.portalItem,t,K.existingItem),await this.portalItem.update({data:a}),await C(this.resourceReferences,r,null),(0,y.D)(r),this.portalItem}async _validateAgainstJSONSchema(e,t,r){let a=t.messages?.filter((e=>"error"===e.type)).map((e=>new s.Z(e.name,e.message,e.details)))??[];r?.validationOptions?.ignoreUnsupported&&(a=a.filter((e=>"layer:unsupported"!==e.name&&"symbol:unsupported"!==e.name&&"symbol-layer:unsupported"!==e.name&&"property:unsupported"!==e.name&&"url:unsupported"!==e.name&&"scenemodification:unsupported"!==e.name)));const o=r?.validationOptions,i=o?.enabled,n=null;if(i&&n){const t=(await n()).validate(e,r.portalItemLayerType);if(t.length>0){const e=`Layer item did not validate:\n${t.join("\n")}`;if(U.error(`_validateAgainstJSONSchema(): ${e}`),"throw"===o.failPolicy){const e=t.map((e=>new s.Z("sceneservice:schema-validation",e))).concat(a);throw new s.Z("sceneservice-validate:error","Failed to save layer item due to schema validation, see `details.errors`.",{combined:e})}}}if(a.length>0)throw new s.Z("sceneservice:save","Failed to save SceneService due to unsupported or invalid content. See 'details.errors' for more detailed information",{errors:a})}};return(0,a._)([(0,c.Cb)(w.id)],t.prototype,"id",void 0),(0,a._)([(0,c.Cb)({type:g.Z})],t.prototype,"spatialReference",void 0),(0,a._)([(0,u.r)("spatialReference",["spatialReference","store.indexCRS","store.geographicCRS"])],t.prototype,"readSpatialReference",null),(0,a._)([(0,c.Cb)({type:m.Z})],t.prototype,"fullExtent",void 0),(0,a._)([(0,u.r)("fullExtent",["fullExtent","store.extent","spatialReference","store.indexCRS","store.geographicCRS"])],t.prototype,"readFullExtent",null),(0,a._)([(0,c.Cb)({readOnly:!0,type:f.Z})],t.prototype,"heightModelInfo",void 0),(0,a._)([(0,c.Cb)({type:Number,json:{name:"layerDefinition.minScale",write:!0,origins:{service:{read:{source:"minScale"},write:!1}}}})],t.prototype,"minScale",void 0),(0,a._)([(0,c.Cb)({type:Number,json:{name:"layerDefinition.maxScale",write:!0,origins:{service:{read:{source:"maxScale"},write:!1}}}})],t.prototype,"maxScale",void 0),(0,a._)([(0,c.Cb)({readOnly:!0})],t.prototype,"version",void 0),(0,a._)([(0,u.r)("version",["store.version"])],t.prototype,"readVersion",null),(0,a._)([(0,c.Cb)({type:String,json:{read:{source:"copyrightText"}}})],t.prototype,"copyright",void 0),(0,a._)([(0,c.Cb)({type:String,json:{read:!1}})],t.prototype,"sublayerTitleMode",void 0),(0,a._)([(0,c.Cb)({type:String})],t.prototype,"title",void 0),(0,a._)([(0,u.r)("portal-item","title")],t.prototype,"readTitlePortalItem",null),(0,a._)([(0,u.r)("service","title",["name"])],t.prototype,"readTitleService",null),(0,a._)([(0,c.Cb)({type:Number,json:{origins:{service:{read:{source:"id"}},"portal-item":{write:{target:"id",isRequired:!0,ignoreOrigin:!0},read:!1}}}})],t.prototype,"layerId",void 0),(0,a._)([(0,c.Cb)(w.HQ)],t.prototype,"url",null),(0,a._)([(0,h.c)("url")],t.prototype,"writeUrl",null),(0,a._)([(0,c.Cb)()],t.prototype,"parsedUrl",null),(0,a._)([(0,c.Cb)({readOnly:!0})],t.prototype,"store",void 0),(0,a._)([(0,c.Cb)({type:String,readOnly:!0,json:{read:{source:"store.rootNode"}}})],t.prototype,"rootNode",void 0),t=(0,a._)([(0,d.j)(N)],t),t},T=-1e38;var K,E;(E=K||(K={}))[E.existingItem=0]="existingItem",E[E.newItem=1]="newItem";const j="Scene Service",Z={getTypeKeywords:()=>[],portalItemLayerType:"unknown",validationOptions:{enabled:!0,ignoreUnsupported:!1,failPolicy:"throw"}};var q;!function(e){e[e.SAVE=0]="SAVE",e[e.SAVE_AS=1]="SAVE_AS"}(q||(q={}))},61064:(e,t,r)=>{r.d(t,{T:()=>i});var a=r(3172),o=r(20102),s=r(70586);async function i(e,t,r,i,n,l){let p=null;if((0,s.pC)(r)){const t=`${e}/nodepages/`,o=t+Math.floor(r.rootIndex/r.nodesPerPage);try{return{type:"page",rootPage:(await(0,a.default)(o,{query:{f:"json",token:i},responseType:"json",signal:l})).data,rootIndex:r.rootIndex,pageSize:r.nodesPerPage,lodMetric:r.lodSelectionMetricType,urlPrefix:t}}catch(e){(0,s.pC)(n)&&n.warn("#fetchIndexInfo()","Failed to load root node page. Falling back to node documents.",o,e),p=e}}if(!t)return null;const c=`${e}/nodes/`,u=c+(t&&t.split("/").pop());try{return{type:"node",rootNode:(await(0,a.default)(u,{query:{f:"json",token:i},responseType:"json",signal:l})).data,urlPrefix:c}}catch(e){throw new o.Z("sceneservice:root-node-missing","Root node missing.",{pageError:p,nodeError:e,url:u})}}},97873:(e,t,r)=>{r.r(t),r.d(t,{addOrUpdateResource:()=>l,contentToBlob:()=>h,fetchResources:()=>n,getSiblingOfSameType:()=>y,getSiblingOfSameTypeI:()=>m,removeAllResources:()=>c,removeResource:()=>p,splitPrefixFileNameAndExtension:()=>d});var a=r(3172),o=r(20102),s=r(70586),i=r(17452);async function n(e,t={},r){await e.load(r);const a=(0,i.v_)(e.itemUrl,"resources"),{start:o=1,num:n=10,sortOrder:l="asc",sortField:p="created"}=t,c={query:{start:o,num:n,sortOrder:l,sortField:p,token:e.apiKey},signal:(0,s.U2)(r,"signal")},u=await e.portal.request(a,c);return{total:u.total,nextStart:u.nextStart,resources:u.resources.map((({created:t,size:r,resource:a})=>({created:new Date(t),size:r,resource:e.resourceFromPath(a)})))}}async function l(e,t,r,a){if(!e.hasPath())throw new o.Z(`portal-item-resource-${t}:invalid-path`,"Resource does not have a valid path");const n=e.portalItem;await n.load(a);const l=(0,i.v_)(n.userItemUrl,"add"===t?"addResources":"updateResources"),[p,c]=u(e.path),d=await h(r),y=new FormData;return p&&"."!==p&&y.append("resourcesPrefix",p),(0,s.pC)(a)&&a.compress&&y.append("compress","true"),y.append("fileName",c),y.append("file",d,c),y.append("f","json"),(0,s.pC)(a)&&a.access&&y.append("access",a.access),await n.portal.request(l,{method:"post",body:y,signal:(0,s.U2)(a,"signal")}),e}async function p(e,t,r){if(!t.hasPath())throw new o.Z("portal-item-resources-remove:invalid-path","Resource does not have a valid path");await e.load(r);const a=(0,i.v_)(e.userItemUrl,"removeResources");await e.portal.request(a,{method:"post",query:{resource:t.path},signal:(0,s.U2)(r,"signal")}),t.portalItem=null}async function c(e,t){await e.load(t);const r=(0,i.v_)(e.userItemUrl,"removeResources");return e.portal.request(r,{method:"post",query:{deleteAll:!0},signal:(0,s.U2)(t,"signal")})}function u(e){const t=e.lastIndexOf("/");return-1===t?[".",e]:[e.slice(0,t),e.slice(t+1)]}function d(e){const[t,r]=function(e){const t=(0,i.Ml)(e);return(0,s.Wi)(t)?[e,""]:[e.slice(0,e.length-t.length-1),`.${t}`]}(e),[a,o]=u(t);return[a,o,r]}async function h(e){return e instanceof Blob?e:(await(0,a.default)(e.url,{responseType:"blob"})).data}function y(e,t){if(!e.hasPath())return null;const[r,,a]=d(e.path);return e.portalItem.resourceFromPath((0,i.v_)(r,t+a))}function m(e,t){if(!e.hasPath())return null;const[r,,a]=d(e.path);return e.portalItem.resourceFromPath((0,i.v_)(r,t+a))}}}]);