<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.base.BaseDatabaseStructureMapper">
    
    <resultMap type="org.thingsboard.server.dao.model.sql.base.BaseDatabaseStructure" id="BaseDatabaseStructureResult">
        <result property="id"    column="id"    />
        <result property="version"    column="version"    />
        <result property="changeType"    column="change_type"    />
        <result property="objectName"    column="object_name"    />
        <result property="objectType"    column="object_type"    />
        <result property="changeDescription"    column="change_description"    />
        <result property="sqlStatement"    column="sql_statement"    />
        <result property="status"    column="status"    />
        <result property="executedBy"    column="executed_by"    />
        <result property="executedAt"    column="executed_at"    />
        <result property="manageType"    column="manage_type"    />
    </resultMap>

    <sql id="selectBaseDatabaseStructureVo">
        select id, version, change_type, object_name, object_type, change_description, sql_statement, status, executed_by, executed_at, manage_type from base_database_structure
    </sql>

    <select id="selectBaseDatabaseStructureList" parameterType="org.thingsboard.server.dao.model.sql.base.BaseDatabaseStructure" resultMap="BaseDatabaseStructureResult">
        <include refid="selectBaseDatabaseStructureVo"/>
        <where>  
            <if test="version != null  and version != ''"> and version = #{version}</if>
            <if test="changeType != null  and changeType != ''"> and change_type = #{changeType}</if>
            <if test="objectName != null  and objectName != ''"> and object_name like concat('%', #{objectName}, '%')</if>
            <if test="objectType != null  and objectType != ''"> and object_type = #{objectType}</if>
            <if test="changeDescription != null  and changeDescription != ''"> and change_description = #{changeDescription}</if>
            <if test="sqlStatement != null  and sqlStatement != ''"> and sql_statement = #{sqlStatement}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="executedBy != null  and executedBy != ''"> and executed_by = #{executedBy}</if>
            <if test="executedAt != null "> and executed_at = #{executedAt}</if>
            <if test="manageType != null  and manageType != ''"> and manage_type = #{manageType}</if>
        </where>
    </select>
    
    <select id="selectBaseDatabaseStructureById" parameterType="String" resultMap="BaseDatabaseStructureResult">
        <include refid="selectBaseDatabaseStructureVo"/>
        where id = #{id}
    </select>

    <insert id="insertBaseDatabaseStructure" parameterType="org.thingsboard.server.dao.model.sql.base.BaseDatabaseStructure">
        insert into base_database_structure
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="version != null">version,</if>
            <if test="changeType != null">change_type,</if>
            <if test="objectName != null">object_name,</if>
            <if test="objectType != null">object_type,</if>
            <if test="changeDescription != null">change_description,</if>
            <if test="sqlStatement != null">sql_statement,</if>
            <if test="status != null">status,</if>
            <if test="executedBy != null">executed_by,</if>
            <if test="executedAt != null">executed_at,</if>
            <if test="manageType != null">manage_type,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="version != null">#{version},</if>
            <if test="changeType != null">#{changeType},</if>
            <if test="objectName != null">#{objectName},</if>
            <if test="objectType != null">#{objectType},</if>
            <if test="changeDescription != null">#{changeDescription},</if>
            <if test="sqlStatement != null">#{sqlStatement},</if>
            <if test="status != null">#{status},</if>
            <if test="executedBy != null">#{executedBy},</if>
            <if test="executedAt != null">#{executedAt},</if>
            <if test="manageType != null">#{manageType},</if>
         </trim>
    </insert>

    <update id="updateBaseDatabaseStructure" parameterType="org.thingsboard.server.dao.model.sql.base.BaseDatabaseStructure">
        update base_database_structure
        <trim prefix="SET" suffixOverrides=",">
            <if test="version != null">version = #{version},</if>
            <if test="changeType != null">change_type = #{changeType},</if>
            <if test="objectName != null">object_name = #{objectName},</if>
            <if test="objectType != null">object_type = #{objectType},</if>
            <if test="changeDescription != null">change_description = #{changeDescription},</if>
            <if test="sqlStatement != null">sql_statement = #{sqlStatement},</if>
            <if test="status != null">status = #{status},</if>
            <if test="executedBy != null">executed_by = #{executedBy},</if>
            <if test="executedAt != null">executed_at = #{executedAt},</if>
            <if test="manageType != null">manage_type = #{manageType},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBaseDatabaseStructureById" parameterType="String">
        delete from base_database_structure where id = #{id}
    </delete>

    <delete id="deleteBaseDatabaseStructureByIds" parameterType="String">
        delete from base_database_structure where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>