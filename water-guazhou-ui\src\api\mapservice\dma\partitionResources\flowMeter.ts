import { request } from '@/plugins/axios'

/**
 * 查询流量表列表
 * @param params
 * @returns
 */
export const GetDmaPartitionFlowMeter = (
  params: IQueryPagerParams & {
    partitionId?: string
    brand?: string
    type?: string
    code?: string
    meterType?: string
  }
) => {
  return request({
    url: '/api/spp/dma/partition/flowMeter/list',
    method: 'get',
    params
  })
}
/**
 * 添加流量表
 * @param params
 * @returns
 */
export const AddDmaPartitionFlowMeter = (params: {
  partitionId: string
  type: string
  code: string
  position: string
  meterType: string
  brand: string
  caliber: string
  pipe: string
  isRemote: string
  year: string
  remark: string
  img: string
}) => {
  return request({
    url: '/api/spp/dma/partition/flowMeter',
    method: 'post',
    data: params
  })
}
/**
 * 删除流量表
 * @param ids
 * @returns
 */
export const DeleteDmaPartitionFlowMeter = (ids: string[]) => {
  return request({
    url: '/api/spp/dma/partition/flowMeter',
    method: 'delete',
    data: ids
  })
}
