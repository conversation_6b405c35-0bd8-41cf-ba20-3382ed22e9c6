import{d as o,a8 as r,g as n,n as d,ax as s,p as a,bh as p,aw as c,av as _,i as m,C as u}from"./index-r0dFAfgr.js";const f={class:"title-text"},h=o({__name:"TitleHeader",props:{title:{},titleWidth:{},type:{},size:{}},setup(i){const t=i,l=r(()=>({"--titleWidth":(t.titleWidth||200)+"px","--diamondLeft":(t.titleWidth||200)-76+"px"}));return(e,y)=>(n(),d("div",{class:c(["title-card__header",[t.type,t.size]]),style:_(m(l))},[s(e.$slots,"title",{},()=>[a("span",f,[a("i",null,p(e.title),1)])],!0),s(e.$slots,"right",{},void 0,!0)],6))}}),x=u(h,[["__scopeId","data-v-554cf08f"]]);export{x as default};
