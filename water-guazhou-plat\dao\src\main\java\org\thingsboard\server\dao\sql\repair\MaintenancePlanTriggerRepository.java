package org.thingsboard.server.dao.sql.repair;

import org.springframework.data.jpa.repository.JpaRepository;
import org.thingsboard.server.dao.model.sql.MaintenancePlanTriggerEntity;
import org.thingsboard.server.dao.model.sql.RepairPlanTriggerEntity;

public interface MaintenancePlanTriggerRepository extends JpaRepository<MaintenancePlanTriggerEntity, String> {
    MaintenancePlanTriggerEntity findByMainId(String mainId);
}
