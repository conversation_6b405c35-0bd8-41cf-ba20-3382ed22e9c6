<!-- 管网属性编辑 -->
<template>
  <RightDrawerMap
    ref="refMap"
    title="属性编辑"
    :detail-max-min="true"
    :detail-extra="true"
    @map-loaded="onMaploaded"
  >
    <Form ref="refForm" :config="FormConfig"></Form>
    <template #detail-header>
      <span>查询结果</span>
    </template>
    <template #detail-extra>
      <FormTableColumnFilter
        v-if="refDetailTable?.TableConfig_Detail.columns"
        :columns="refDetailTable.TableConfig_Detail.columns"
        :show-tooltip="true"
      ></FormTableColumnFilter>
    </template>
    <template #detail-default>
      <InlineForm ref="refFormInline" :config="FormConfigInline"> </InlineForm>

      <div class="table-box">
        <DetailTable
          ref="refDetailTable"
          @row-click="handleRowClick"
          @refresh-data="startQuery"
        ></DetailTable>
      </div>
    </template>
  </RightDrawerMap>
</template>
<script lang="ts" setup>
// 定义全局变量，判断是否使用GeoServer模式
const isGeoServerMode = (window as any).GIS_SERVER_SWITCH === true;
import SketchViewModel from '@arcgis/core/widgets/Sketch/SketchViewModel.js';
import { Promotion } from '@element-plus/icons-vue';
import { queryLayerClassName } from '@/api/mapservice';
import {
  applyEdits,
  generate4548Graphic,
  getGraphicLayer,
  getLayerOids,
  getSubLayerIds,
  initBufferParams,
  queryBufferPolygon,
  setSymbol
} from '@/utils/MapHelper';
// 导入GeoServer相关工具函数
import {
  GetFieldConfig as GetFieldConfigByGeoserver,
  GetFieldValueByGeoserver,
  QueryByPolygon
} from '@/utils/geoserver/wfsUtils';
import RightDrawerMap from '../../components/common/RightDrawerMap.vue';
import DetailTable from '../../components/common/DetailTable.vue';
import {
  GetFieldConfig,
  GetFieldUniqueValue
} from '@/api/mapservice/fieldconfig';
import { SLConfirm, SLMessage } from '@/utils/Message';
import { formatterDate, formatTree } from '@/utils/GlobalHelper';
import { initPipeEditForm } from './config';
import { useUserStore } from '@/store';
import { PostGisOperateLog } from '@/api/system/gisSetting';
import { EGigLogFunc, EGisLogApp, EGisLogOperateType } from '../../config';

const refDetailTable = ref<InstanceType<typeof DetailTable>>();
const state = reactive<{
  layerIds: number[];
  layerInfos: any[];
  curLayerInfo?: any;
  loading: boolean;
  uniqueing: boolean;
  curFieldNode?: any;
  curLayerFields: NormalOption[];
}>({
  layerIds: [],
  layerInfos: [],
  loading: false,
  uniqueing: false,
  curLayerFields: []
});

const staticState: {
  view?: __esri.MapView;
  sketch?: __esri.SketchViewModel;
  graphic?: __esri.Graphic;
  graphicsLayer?: __esri.GraphicsLayer;
  sketchCompHandler?: any;
  sketchUpdateHandler?: any;
} = {};
const refForm = ref<IFormIns>();

const FormConfig = reactive<IFormConfig>({
  labelPosition: 'top',
  group: [
    {
      fieldset: {
        desc: '绘制工具'
      },
      fields: [
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              title: '绘制点',
              disabled: () => state.loading,
              iconifyIcon: 'gis:circle',
              click: () => initDraw('point')
            },
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              title: '绘制多边形',
              disabled: () => state.loading,
              iconifyIcon: 'mdi:shape-polygon-plus',
              click: () => initDraw('polygon')
            },
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              title: '绘制矩形',
              disabled: () => state.loading,
              iconifyIcon: 'ep:crop',
              click: () => initDraw('rectangle')
            },
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              title: '绘制椭圆',
              disabled: () => state.loading,
              iconifyIcon: 'mdi:ellipse-outline',
              click: () => initDraw('circle')
            },
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              title: '清除图形',
              disabled: () => state.loading,
              iconifyIcon: 'ep:delete',
              click: () => clearGraphicsLayer()
            }
          ]
        }
      ]
    },
    {
      id: 'layer',
      fieldset: {
        desc: '选择图层'
      },
      fields: [
        {
          type: 'tree',
          checkStrictly: true,
          showCheckbox: true,
          field: 'layerid',
          nodeKey: 'value',
          options: [],
          handleCheckChange: (data, isChecked) => {
            if (isChecked) {
              // 设置选中的图层ID
              refForm.value && (refForm.value.dataForm.layerid = [data.value]);
              if (isGeoServerMode) {
                // GeoServer模式下，根据图层名称查找图层信息
                state.curLayerInfo = state.layerInfos.find(
                  (item) => item.value === data.value || item.label === data.value
                );
              } else {
                // ArcGIS模式下，根据图层ID查找图层信息
                state.curLayerInfo = state.layerInfos.find(
                  (item) => item.layerid === data.value
                );
              }

              // 清空字段选择
              refFormInline.value?.dataForm &&
                (refFormInline.value.dataForm.field = '');

              // 刷新详情表格
              refDetailTable.value?.refreshDetail();

              // 触发字段列表的加载
              // 这里不需要手动触发，因为已经设置了setDataBy: 'layerid'
              // 当layerid变化时，会自动触发setData函数
            }
          }
        }
      ]
    },
    {
      id: 'layer',
      fieldset: {
        desc: '图层字段'
      },
      fields: [
        {
          type: 'list',
          data: [],
          className: 'sql-list-wrapper',
          setDataBy: 'layerid',  // 添加这一行，当layerid变化时自动触发setData函数
          setData: async (config: IFormList, row) => {
            if (!row.layerid?.length) {
              state.curLayerFields.length = 0;
              return;
            }
            const layerid = row.layerid[0];
            if (isGeoServerMode) {
              // GeoServer模式
              try {
                console.log('使用GeoServer模式获取字段信息:', layerid);
                const fields = await GetFieldConfigByGeoserver(layerid);

                if (fields && fields.data && fields.data.featureTypes && fields.data.featureTypes[0]) {
                  // 获取字段列表
                  const properties = fields.data.featureTypes[0].properties || [];
                  config.data = properties;
                  // 过滤字段
                  const ignoredFields = [
                    'START_SID',
                    'END_SID',
                    'SID',
                    'OBJECTID',
                    'PIPELENGTH',
                    'X',
                    'Y',
                    'the_geom',  // GeoServer特有的几何字段
                    'geom'       // GeoServer特有的几何字段
                  ];

                  // 创建字段列表
                  const curLayerFields = properties
                    .filter((item: any) => {
                      // 过滤出可编辑的字段
                      return (
                        ignoredFields.indexOf(item.name) === -1 &&
                        item.type !== 'gml:GeometryPropertyType' && // 排除几何字段
                        ['int', 'long', 'double', 'float', 'string', 'date'].indexOf(
                          item.type.split(':')[1]?.toLowerCase() || ''
                        ) !== -1
                      );
                    })
                    .map((item: any, index: number) => {
                      // 将GeoServer字段转换为表单项
                      const type = item.type.split(':')[1]?.toLowerCase() || '';
                      let fieldType = 'input';

                      if (['int', 'long', 'double', 'float'].includes(type)) {
                        fieldType = 'input-number';
                      } else if (type === 'date') {
                        fieldType = 'date';
                      }

                      return {
                        id: index,
                        field: item.name,
                        label: item.name,
                        type: fieldType,
                        disabled: false,
                        readonly: false
                      };
                    })
                    .sort((a: any, b: any) => {
                      return a.id - b.id;
                    });

                  state.curLayerFields = formatTree(curLayerFields || [], {
                    id: 'field',
                    label: 'label',
                    value: 'field'
                  });
                  resetInlineFromConfig(curLayerFields);
                } else {
                  console.error('无法获取GeoServer字段信息');
                }
              } catch (error) {
                console.error('获取GeoServer字段信息时出错:', error);
              }
            } else {
              // ArcGIS模式
              const layerName = state.layerInfos.find(
                (item) => item.layerid === layerid
              )?.layername;
              if (!layerName) return;
              const fields = await GetFieldConfig(layerName);
              config.data = fields.data?.result?.rows;
              const res = await initPipeEditForm(layerid);
              const ignoredFields = [
                'START_SID',
                'END_SID',
                'SID',
                'OBJECTID',
                // 'START_DEPTH',
                // 'END_DEPTH',
                'PIPELENGTH',
                'X',
                'Y'
                // 'Z',
                // 'SUBTYPE'
              ];
              const curLayerFields: IFormItem[] = res
                .filter((item: any) => {
                  return (
                    ignoredFields.indexOf(item.field) === -1 &&
                    item.disabled !== true &&
                    item.readonly !== true &&
                    ['input', 'input-number', 'number', 'date'].indexOf(
                      item.type
                    ) !== -1
                  );
                })
                .map((item) => {
                  item.id = config.data.findIndex((o) => o.name === item.field);
                  return item;
                })
                .sort((a, b) => {
                  return a.id - b.id;
                });
              state.curLayerFields = formatTree(curLayerFields || [], {
                id: 'field',
                label: 'label',
                value: 'field'
              });
              resetInlineFromConfig(curLayerFields);
            }
          },

          displayField: isGeoServerMode ? 'name' : 'alias',
          valueField: 'name',
          highlightCurrentRow: true,
          nodeClick: (node) => {
            state.curFieldNode = node;
            if(isGeoServerMode){
              // GeoServer模式下，需要用双引号包裹字段名
              appendSQL(`"${node.name}"`);
            }else{
              // ArcGIS模式下，直接使用字段名
              appendSQL(node.name);
            }
          }
        }
      ]
    },
    {
      id: 'field-construct',
      fieldset: {
        desc: '构建查询语句'
      },
      fields: [
        {
          type: 'btn-group',
          size: 'small',
          style: {
            width: '40%',
            display: 'flex',
            flexWrap: 'wrap'
          },
          className: 'sql-btns-wrapper',
          btns: [
            {
              perm: true,
              text: '=',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('=');
              }
            },
            {
              perm: true,
              text: '模糊',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL("like '%替换此处%'");
              }
            },
            {
              perm: true,
              text: '>',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('>');
              }
            },
            {
              perm: true,
              text: '<',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('<');
              }
            },
            {
              perm: true,
              text: '非',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('<>');
              }
            },
            {
              perm: true,
              text: '并且',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('and');
              }
            },
            {
              perm: true,
              text: '或者',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('or');
              }
            },
            {
              perm: true,
              text: '%',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('%');
              }
            }
          ],
          extraFormItem: [
            {
              type: 'list',
              wrapperStyle: {
                width: '60%',
                height: '144px'
              },
              className: 'sql-list-wrapper',
              field: 'uniqueValue',
              data: [],
              nodeClick: (node) => {
                appendSQL("'" + node + "'");
              },
              filters: [
                {
                  type: 'btn-group',
                  btns: [
                    {
                      perm: true,
                      text: () =>
                        state.uniqueing ? '正在获取唯一值' : '获取唯一值',
                      loading: () => state.uniqueing,
                      disabled: () => state.loading,
                      styles: {
                        width: '100%',
                        borderRadius: '0'
                      },
                      click: () => getUniqueValue()
                    }
                  ]
                }
              ]
            }
          ]
        }
      ]
    },
    {
      fieldset: {
        desc: '组合查询条件'
      },
      fields: [
        {
          type: 'textarea',
          field: 'sql',
          placeholder: 'OBJECTID > 0'
        },
        {
          type: 'btn-group',
          itemContainerStyle: {
            marginBottom: '8px'
          },
          btns: [
            {
              perm: true,
              text: '清除组合条件',
              type: 'danger',
              disabled: () => state.loading,
              click: () => clear(),
              styles: {
                width: '100%'
              }
            }
          ]
        }
      ]
    },
    {
      fields: [
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: '查询',
              styles: {
                width: '100%'
              },
              click: () => startQuery()
            },
            {
              perm: true,
              text: '重置',
              type: 'default',
              styles: {
                width: '100%'
              },
              click: () => resetForm()
            }
          ]
        }
      ]
    }
  ]
});
const appendSQL = (val) => {
  if (!refForm.value) return;
  if (!refForm.value?.dataForm) refForm.value.dataForm = {};
  const sql = refForm.value.dataForm.sql || ' ';
  refForm.value.dataForm.sql = sql + val + ' ';
};

const getUniqueValue = async () => {
  try {
    // 检查必要的对象是否存在
    if (!state.curFieldNode) {
      SLMessage.warning('请先选择字段');
      return;
    }

    // 检查字段名称是否存在
    const fieldName = state.curFieldNode.name || state.curFieldNode.field;
    if (!fieldName) {
      SLMessage.warning('无法获取字段名称');
      return;
    }
    console.log('获取字段唯一值:', fieldName);

    // 检查图层ID是否存在
    if (!refForm.value || !refForm.value.dataForm) {
      SLMessage.warning('表单对象不存在');
      return;
    }

    const layerid = refForm.value.dataForm.layerid;
    if (!layerid?.length) {
      SLMessage.warning('请先选择一个图层');
      return;
    }

    // 设置加载状态
    state.uniqueing = true;

    // 获取extraFormItem，确保它存在
    const extraFormItemGroup = FormConfig.group.find(
      (item) => item.id === 'field-construct'
    );
    if (!extraFormItemGroup || !extraFormItemGroup.fields || !extraFormItemGroup.fields[0]) {
      console.error('extraFormItemGroup不存在或为空');
      SLMessage.error('无法获取唯一值列表组件');
      state.uniqueing = false;
      return;
    }

    const extraFormItem = extraFormItemGroup.fields[0].extraFormItem;
    if (!extraFormItem || !extraFormItem[0]) {
      console.error('extraFormItem不存在或为空');
      SLMessage.error('无法获取唯一值列表组件');
      state.uniqueing = false;
      return;
    }

    const field = extraFormItem[0] as IFormList;
    if (!field) {
      console.error('无法获取列表字段');
      SLMessage.error('无法获取唯一值列表组件');
      state.uniqueing = false;
      return;
    }

    // 根据不同的模式获取唯一值
    if (isGeoServerMode) {
      // GeoServer模式
      try {
        // 获取图层名称
        const layerName = state.curLayerInfo?.layerTitle || state.curLayerInfo?.label || layerid[0];
        console.log('使用GeoServer模式获取唯一值:', layerName, fieldName);

        // 调用GeoServer API获取唯一值
        const response = await GetFieldValueByGeoserver({
          layerName: layerName,
          fiedName: fieldName
        });

        // 检查响应是否有效
        if (!response || !response.data) {
          console.error('获取GeoServer唯一值响应无效:', response);
          SLMessage.error('获取唯一值失败');
          state.uniqueing = false;
          return;
        }

        const data = response.data;
        console.log('获取到的GeoServer数据:', data);

        // 检查数据是否有效
        if (!data.features || !Array.isArray(data.features)) {
          console.error('无效的GeoServer数据格式:', data);
          SLMessage.warning('无法获取唯一值');
          state.uniqueing = false;
          return;
        }

        // 创建一个空集合来存储唯一的字段值
        const uniqueValues = new Set();

        // 遍历特征数组
        data.features.forEach(feature => {
          if (feature && feature.properties) {
            // 添加字段值到集合中
            uniqueValues.add(feature.properties[fieldName]);
          }
        });

        // 转换为数组并过滤空值
        const uniqueValuesArray = Array.from(uniqueValues).filter(value => value !== null && value !== undefined);
        console.log('提取的唯一值:', uniqueValuesArray);

        // 设置列表数据
        field.data = uniqueValuesArray;
      } catch (error) {
        console.error('获取GeoServer字段唯一值时出错:', error);
        SLMessage.error('获取唯一值失败');
      }
    } else {
      // ArcGIS模式
      try {
        console.log('使用ArcGIS模式获取唯一值:', layerid[0], fieldName);

        // 调用ArcGIS API获取唯一值
        const res = await GetFieldUniqueValue({
          layerid: layerid[0],
          field_name: fieldName
        });

        // 检查响应是否有效
        if (!res || !res.data || !res.data.result || !res.data.result.rows) {
          console.error('获取ArcGIS唯一值响应无效:', res);
          SLMessage.error('获取唯一值失败');
          state.uniqueing = false;
          return;
        }

        // 设置列表数据
        field.data = res.data.result.rows;
      } catch (error) {
        console.error('获取ArcGIS字段唯一值时出错:', error);
        SLMessage.error('获取唯一值失败');
      }
    }
  } catch (error) {
    console.error('获取唯一值时出错:', error);
    SLMessage.error('获取唯一值失败');
  } finally {
    // 无论成功还是失败，都重置加载状态
    state.uniqueing = false;
  }
};
const clear = () => {
  refForm.value?.dataForm && (refForm.value.dataForm.sql = '');
};

const clearGraphicsLayer = () => {
  staticState.graphicsLayer?.removeAll();
  staticState.graphic = undefined;
};
const initDraw = (type: 'polygon' | 'point' | 'rectangle' | 'circle') => {
  staticState.sketchCompHandler?.remove();
  staticState.sketchUpdateHandler?.remove();
  staticState.sketch?.destroy();
  staticState.graphicsLayer?.removeAll();
  staticState.sketch = new SketchViewModel({
    view: staticState.view,
    layer: staticState.graphicsLayer,
    polygonSymbol: setSymbol('polygon', {
      color: [255, 0, 0, 0.3],
      outlineColor: [255, 0, 0, 1]
    }) as any,
    polylineSymbol: setSymbol('polyline') as any,
    pointSymbol: setSymbol('point') as any
  });
  staticState.sketch?.create(type);
  staticState.sketchUpdateHandler = staticState.sketch?.on(
    'update',
    (result: any) => {
      if (result.state === 'complete') {
        console.log(result.graphics[0]);

        staticState.graphic = result.graphics[0];
      }
    }
  );
  staticState.sketchCompHandler = staticState.sketch?.on(
    'create',
    (result: any) => {
      if (result.state === 'complete') {
        console.log(result.graphic);

        staticState.graphic = result.graphic;
      }
    }
  );
};
const refMap = ref<InstanceType<typeof RightDrawerMap>>();
const startQuery = async () => {
  refMap.value?.toggleCustomDetail(true);
  const layerIds = refForm.value?.dataForm.layerid || [];
  let geometry = staticState.graphic?.geometry;

  // 如果是点几何，创建缓冲区
  if (geometry?.type === 'point') {
    const polygons = await queryBufferPolygon(
      initBufferParams({
        bufferSpatialReference: staticState.view?.spatialReference,
        distances: [12],
        geometries: [geometry],
        outSpatialReference: staticState.view?.spatialReference,
        geodesic: true,
        unit: 'meters',
        unionResults: false
      })
    );
    geometry = polygons[0];
  }

  if (isGeoServerMode) {
    try {
      // GeoServer模式下的查询
      console.log('使用GeoServer模式查询:', layerIds, geometry?.rings);

      // 确保多边形是闭合的
      if (geometry?.rings && geometry.rings[0] &&
          geometry.rings[0][0] !== geometry.rings[0][geometry.rings[0].length - 1]) {
        // 如果不是闭合的，复制第一个点到最后
        geometry.rings[0].push([...geometry.rings[0][0]]);
      }

      const response = await QueryByPolygon(layerIds, geometry?.rings[0], refForm.value?.dataForm?.sql);
      console.log('查询响应:', response);
      let features = response.data.features;
      console.log('查询到的要素:', features);

      if (features && features.length > 0) {
        console.log('查询结果:', features);

        // 处理查询结果，按图层分组
        const uniqueLayerNames = new Set();
        features.forEach(feature => {
          if (feature.id) {
            const layerName = feature.id.split('.')[0];
            uniqueLayerNames.add(layerName);
          }
        });

        // 创建tabs数组
        const tabs = [];
        uniqueLayerNames.forEach(layerName => {
          const layerFeatures = features.filter(feature =>
            feature.id && feature.id.split('.')[0] === layerName
          );

          if (layerFeatures.length > 0) {
            tabs.push({
              name: layerName,
              label: `${layerName}(${layerFeatures.length})`,
              data: layerFeatures
            });
          }
        });

        // 刷新详情表格，直接传递查询结果
        if (tabs.length > 0) {
          refDetailTable.value?.refreshDetail(
            staticState.view,
            {
              layerid: layerIds[0],
              layername: tabs[0]?.name,
              oids: tabs[0]?.data,
            }
          );
        } else {
          SLMessage.info('查询结果为空');
        }
      } else {
        SLMessage.info('查询结果为空');
      }
    } catch (error) {
      console.error('执行GeoServer查询时出错:', error);
      SLMessage.error('查询失败，请检查查询条件是否正确');
    }
  } else {
    // ArcGIS模式
    const queryParams = {
      where: refForm.value?.dataForm.sql || '1=1',
      geometry
    };
    const tabs = await getLayerOids(layerIds, state.layerInfos, queryParams);
    refDetailTable.value?.refreshDetail(
      staticState.view,
      {
        layerid: layerIds[0],
        layername: tabs[0]?.name,
        oids: tabs[0]?.data
      },
      {
        queryParams
      }
    );
  }
};
const resetForm = () => {
  refForm.value?.resetForm();
};
const FormConfigInline = reactive<IFormConfig>({
  labelPosition: 'right',
  labelWidth: 150,
  group: [
    {
      fields: [
        {
          labelWidth: 150,
          type: 'select',
          field: 'field',
          label: '属性批量编辑： 字段',
          options: computed<NormalOption[]>(() => state.curLayerFields) as any
        },
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: '更新',
              loading: (): boolean => !!FormConfigInline.submitting,
              disabled: (row): boolean => !row.field,
              svgIcon: shallowRef(Promotion),
              click: () => handleRefreshDetail()
            }
          ]
        }
        // {
        //   type: 'input',
        //   itemContainerStyle: {
        //     marginLeft: 'auto'
        //   },
        //   field: 'tablefilter'
        // }
      ]
    }
  ]
});
const refFormInline = ref<IInlineFormIns>();
const resetInlineFromConfig = (fields: IFormItem[]) => {

  console.log('重置内联表单配置:', fields, isGeoServerMode);

  // 如果没有字段，返回
  if (!fields || !fields.length) {
    console.warn('没有可用的字段');
    return;
  }

  const newFields = fields.map((item: any) => {
    // 创建新对象，避免修改原始对象
    const newItem: any = { ...item };

    // 在GeoServer模式下，可能需要调整字段名称或其他属性
    if (isGeoServerMode) {
      // 对于GeoServer模式，确保必要的属性存在
      if (!newItem.field && newItem.name) {
        newItem.field = newItem.name;
      }

      if (!newItem.label) {
        newItem.label = newItem.field || newItem.name || '';
      }
    }

    // 设置隐藏属性
    newItem.hidden = true;
    newItem.handleHidden = (params: any, _query: any, config: any) => {
      config.hidden = !params.field || params.field !== newItem.field;
    };

    return newItem;
  });

  // 将字段添加到内联表单中
  try {
    FormConfigInline.group[0].fields.splice(
      1,
      FormConfigInline.group[0].fields.length - 2,
      ...newFields
    );
    console.log('内联表单字段更新成功');
  } catch (error) {
    console.error('更新内联表单字段时出错:', error);
  }
};
const handleRefreshDetail = async () => {
  const layerid = refForm.value?.dataForm.layerid?.[0];
  if (layerid === undefined) return;
  const features = refDetailTable.value?.staticState.tabFeatures;
  if (!features?.length) return;
  const field = refFormInline.value?.dataForm.field;
  const value = refFormInline.value?.dataForm[field];
  if (!field) {
    SLMessage.warning('请选择字段');
    return;
  }
  if (value !== 0 && !value) {
    try {
      await SLConfirm('当前字段值为空，确定提交？', '提示信息');
    } catch (error) {
      return;
    }
  }

  SLConfirm('应用到空间数据库？', '提示信息')
    .then(() => {
      if (isGeoServerMode) {
        // GeoServer模式
        try {
          console.log('使用GeoServer模式更新属性:', layerid, field, value);
          FormConfigInline.submitting = true;

          // 导入WFS-T工具函数
          import('@/utils/geoserver/wfsTransactionUtils').then(async ({ updateFeatureAttributes }) => {
            try {
              // 准备要更新的属性
              // 在GeoServer中，字段名称通常是大写的
              const attributes = {
                [field]: value
              };
              console.log('要更新的属性:', attributes);

              // 输出要素对象的结构，帮助调试
              console.log('要素对象结构:', features[0]);
              console.log('要素对象属性:', Object.keys(features[0]));

              // 收集要素ID和属性信息用于日志
              const ids = features.map(item => {
                // 从要素ID中提取对象ID
                // 尝试不同的属性来获取ID
                const featureId = item.id || item.attributes?.OBJECTID || item.properties?.id || item.properties?.OBJECTID;
                console.log('要素ID:', featureId);

                if (typeof featureId === 'string') {
                  const parts = featureId.split('.');
                  return parts.length > 1 ? parts[1] : featureId;
                } else {
                  return featureId;
                }
              });

              // 获取字段名称
              const fieldName = state.curLayerFields.find(
                (item) => item.value === field
              )?.label || field;

              // 获取图层名称
              // 在GeoServer模式下，我们需要使用图层的名称而不是ID
              const layerName = state.curLayerInfo?.layerTitle || state.curLayerInfo?.label || layerid;
              console.log('使用图层名称进行更新:', layerName);

              // 执行WFS-T更新操作
              const response = await updateFeatureAttributes(layerName, features, attributes);
              console.log('GeoServer更新响应:', response);

              // 判断是否成功
              if (response.data && response.data.includes('<wfs:totalUpdated>')) {
                // 提取更新的要素数量
                const updatedCount = response.data.match(/<wfs:totalUpdated>(\d+)<\/wfs:totalUpdated>/)?.[1] || '0';

                if (parseInt(updatedCount) > 0) {
                  // 更新成功
                  SLMessage.success(`属性更新成功，共更新${updatedCount}个要素`);

                  // 记录日志
                  PostGisOperateLog({
                    optionName: EGigLogFunc.SHUXINBIANJI,
                    type: EGisLogApp.BASICGIS,
                    content: `对ID为${ids.join('、')}的${layerid}，统一将属性【${fieldName}】修改为【${value}】`,
                    optionType: EGisLogOperateType.UPDATE
                  }).catch(() => {
                    console.log('生成gis操作日志失败');
                  });

                  // 更新表格中的数据
                  features.forEach((item) => {
                    if (item.properties) {
                      item.properties[field] = value;
                    }
                  });

                  // 重新查询以刷新表格
                  startQuery();
                } else {
                  // 没有要素被更新
                  SLMessage.warning('没有要素被更新');
                }
              } else {
                // 更新失败
                SLMessage.error('属性更新失败');
                console.error('更新响应不符合预期:', response.data);
              }
            } catch (error) {
              console.error('WFS-T更新操作失败:', error);
              SLMessage.error('属性更新失败');
            } finally {
              FormConfigInline.submitting = false;
            }
          }).catch(error => {
            console.error('加载WFS-T工具函数失败:', error);
            SLMessage.error('加载属性编辑功能失败');
            FormConfigInline.submitting = false;
          });
        } catch (error) {
          console.error('GeoServer模式下更新属性时出错:', error);
          SLMessage.error('属性更新失败');
          FormConfigInline.submitting = false;
        }
      } else {
        // ArcGIS模式
        const submitFeatures = features.map((item) => {
          // 使用通用方法
          const g = item;
          g.attributes[field] = value;
          g.attributes['UPDATEDBY'] = useUserStore().user?.firstName;
          g.attributes['UPDATEDDATE'] = moment().format(formatterDate);
          return g;
        });
        const fieldName = state.curLayerFields.find(
          (item) => item.value === field
        )?.label;
        const ids = submitFeatures.map((item) => item.attributes.OBJECTID);
        const layername = state.curLayerInfo.layername;
        FormConfigInline.submitting = true;
        applyEdits(layerid, {
          updateFeatures: submitFeatures || []
        })
          .then(() => {
            PostGisOperateLog({
              optionName: EGigLogFunc.SHUXINBIANJI,
              type: EGisLogApp.BASICGIS,
              content: `对OBJECTID为${ids.join('、')}的${layername}，统一将属性【${fieldName}】修改为【${value}】`,
              optionType: EGisLogOperateType.UPDATE
            }).catch(() => {
              console.log('生成gis操作日志失败');
            });
            startQuery();
          })
          .finally(() => {
            FormConfigInline.submitting = false;
          });
      }
    })
    .catch(() => {
      //
    });
};
const handleRowClick = (row) => {
  // 判断是否为GeoServer模式
  if (isGeoServerMode) {
    // GeoServer模式下，使用OBJECTID属性
    refDetailTable.value?.extentTo(staticState.view, row.OBJECTID);
  } else {
    // ArcGIS模式下，使用OBJECTID属性
    refDetailTable.value?.extentTo(staticState.view, row.OBJECTID);
  }
};
const getLayerInfo = () => {
  if(isGeoServerMode){
    const field = FormConfig.group[1].fields[0] as IFormTree
    const layerInfo = staticState.view?.layerViews.items[0].layer.sublayers;
    let layers = layerInfo.items.map(item => {
      return {
        label: item.name,
        value: item.name,
        // data: item
      }
    });
    field.options = layers;// [{ label: '管线类', value: -2, children: layers }]
    refForm.value && (refForm.value.dataForm.layerid = state.layerIds)
  } else {
    state.layerIds = getSubLayerIds(staticState.view);
    queryLayerClassName(state.layerIds).then((layerInfo) => {
      state.layerInfos = layerInfo.data?.result?.rows || [];
      const field = FormConfig.group[1].fields[0] as IFormTree;
      const points = state.layerInfos
        .filter((item) => item.geometrytype === 'esriGeometryPoint')
        .map((item) => {
          return {
            label: item.layername,
            value: item.layerid,
            data: item
          };
        });
      const lines = state.layerInfos
        .filter((item) => item.geometrytype === 'esriGeometryPolyline')
        .map((item) => {
          return {
            label: item.layername,
            value: item.layerid,
            data: item
          };
        });
      field &&
        (field.options = [
          { label: '管线类', value: -2, children: lines, disabled: true },
          { label: '管点类', value: -1, children: points, disabled: true }
        ]);
      if (!refForm.value) return;
      refForm.value.dataForm.layerid = state.layerIds.slice(0, 1);
      state.curLayerInfo = state.layerInfos.find(
        (item) => item.layerid === state.layerIds[0]
      );
    });
  }
};
const onMaploaded = async (view) => {
  staticState.view = view;
  staticState.graphicsLayer = getGraphicLayer(staticState.view, {
    id: 'pipe-add',
    title: '属性编辑'
  });
  setTimeout(() => {
    getLayerInfo();
  }, 1000);
};
onBeforeUnmount(() => {
  staticState.sketchCompHandler?.remove();
  staticState.sketchUpdateHandler?.remove();
  staticState.sketch?.destroy();
  staticState.graphicsLayer?.removeAll();
});
</script>
<style lang="scss" scoped>
.el-button {
  --el-fill-color: transparent;
  --el-fill-color-light: transparent;
}
.darkblue {
  .top-toolbar {
    background-color: rgba(21, 45, 68, 0.9);
  }
}
.top-toolbar {
  position: absolute;
  top: 20px;
  left: 50%;
  border-radius: 4px;
  transform: translateX(-50%);
  display: flex;
  flex-wrap: nowrap;
  .el-button + .el-button {
    margin: 0;
  }
}
.edit-toogle {
  dd {
    margin-left: 0;
    font-size: 14px;
    padding: 0 8px;
    line-height: 25px;
    display: flex;
    align-items: center;
    word-break: keep-all;
    cursor: pointer;
    & > .el-icon,
    & > .el-checkbox {
      margin-right: 8px;
    }
    &.disabled {
      color: var(--el-disabled-text-color);
      cursor: not-allowed;
    }
    &.active {
      color: var(--el-color-primary);
    }
  }
  dt {
    padding: 12px 0;
    font-size: 12px;
  }
}
.table-box {
  height: calc(100% - 50px);
}
</style>
