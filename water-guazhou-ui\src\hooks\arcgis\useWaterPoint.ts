import Circle from './Geometries/Circle';

export const useWaterPoint = (
  containerId: string,
  options?: { popIdPrefix?: string }
) => {
  let container: HTMLElement | undefined | null;
  let extentHandler: any;
  // 获取父元素
  const __getContainer = () => {
    container = document.getElementById(containerId);
    return container;
  };
  // const canvasList: HTMLCanvasElement[] = []
  const dataList: {
    id: string;
    point?: __esri.Point;
    color?: string;
    canvas?: HTMLCanvasElement;
  }[] = [];
  /**
   * 创建canvas元素
   * @param attributes
   * @returns
   */
  const _createCanvas = (
    /** canvas的元素属性 */
    attributes: {
      id: string;
      width?: number;
      height?: number;
    }
  ) => {
    const canvas = document.createElement('canvas');
    canvas.id = attributes.id;
    canvas.width = attributes.width || 25;
    canvas.height = attributes.height || 25;
    canvas.style.position = 'absolute';
    canvas.style.cursor = 'pointer';
    return canvas;
  };
  const _addCircle = (canvas: HTMLCanvasElement, color?: string) => {
    const radius = Math.min(canvas.width, canvas.height);
    const circle = new Circle(
      radius / 2,
      canvas.width / 2,
      canvas.height / 2,
      color || '#67c23a'
    );
    const step = radius / 33 / 2;
    _drawCircles(canvas, circle, step);
  };
  /**
   * 在canvas中绘制一个圆
   * @param canvas
   * @returns
   */
  const _drawCircles = (
    canvas: HTMLCanvasElement,
    circle: Circle,
    step = 0.2
  ) => {
    const context = canvas.getContext('2d');
    if (context === null) {
      console.warn('_drawCircles：', 'context is null, progress exited');
      return;
    }
    const backCanvas = document.createElement('canvas');
    const backContext = backCanvas.getContext('2d');
    if (backContext === null) return;
    backCanvas.width = canvas.width;
    backCanvas.height = canvas.height;
    context.globalAlpha = 0.95;
    backContext.globalCompositeOperation = 'copy';
    circle.draw(context);
    circle.expandRadius(step);
    setTimeout(() => {
      backContext.drawImage(canvas, 0, 0, canvas.width, canvas.height);
      context.clearRect(0, 0, canvas.width, canvas.height);
      _drawCircles(canvas, circle, step);
      context.drawImage(backCanvas, 0, 0, canvas.width, canvas.height);
    }, 33);
  };
  /**
   * 重置canvas的位置
   * @param view
   * @param geometry
   * @param canvas
   */
  const _resetCanvasPosition = (
    view?: __esri.MapView,
    point?: __esri.Point,
    canvas?: HTMLCanvasElement
  ) => {
    if (!canvas || !point || !view) return;
    const screenPoint = view.toScreen(point);
    if (screenPoint === null) return;
    canvas.style.left = screenPoint.x - canvas.width / 2 + 'px';
    canvas.style.top = screenPoint.y - canvas.height / 2 + 'px';
  };
  const addMany = (
    view?: __esri.MapView,
    data?: { id: string; point: __esri.Point; canvas: HTMLCanvasElement }[],
    addPopOptions?: {
      width: number;
      height: number;
      color?: string;
    }
  ) => {
    __getContainer();
    if (!view || !container || !data?.length) return;
    data = data.map((item) => {
      const canvas = _createCanvas({
        id: (options?.popIdPrefix ?? 'point_canvas_') + item.id,
        ...(addPopOptions || {})
      });
      // canvasList.push(canvas)

      container?.appendChild(canvas);
      item.canvas = canvas;
      _addCircle(canvas, addPopOptions?.color);
      _resetCanvasPosition(view, item.point, canvas);
      return item;
    });
    dataList.push(...data);
    watchExtent(view);
  };
  const setContainer = (div: HTMLElement | undefined | null) => {
    if (div === null) return;
    container = div;
  };
  const add = (
    view?: __esri.MapView,
    data?: { id: string; point?: __esri.Point; canvas?: any },
    addPopOptions?: {
      width?: number;
      height?: number;
      color?: string;
    }
  ) => {
    if (!view || !data) return;
    __getContainer();
    if (!extentHandler) watchExtent(view);
    const canvas = _createCanvas({
      id: (options?.popIdPrefix ?? 'point_canvas_') + data.id,
      ...addPopOptions
    });
    data.canvas = canvas;
    // canvasList.push(canvas)
    dataList.push(data);
    container?.appendChild(canvas);
    _addCircle(canvas, addPopOptions?.color);
    _resetCanvasPosition(view, data.point, canvas);
  };
  const removeAll = () => {
    if (!container) return;

    dataList.map((item) => {
      item.canvas && container?.removeChild(item.canvas);
    });
    dataList.length = 0;
    // canvasList.length = 0
  };
  const remove = (id: string) => {
    const obj = dataList.find((item) => item.id === id);
    obj?.canvas && container?.removeChild(obj.canvas);
  };
  const watchExtent = (view?: __esri.MapView) => {
    if (!view) return;
    extentHandler && extentHandler?.remove();
    extentHandler = view.watch('extent', () => {
      dataList.map((item) => {
        const canvas = item.canvas;
        _resetCanvasPosition(view, item.point, canvas);
      });
    });
  };
  const removeWatchExtent = () => {
    extentHandler?.remove();
    extentHandler = undefined;
  };
  const destroy = () => {
    removeAll();
    removeWatchExtent();
  };
  onBeforeUnmount(() => {
    destroy();
  });
  return {
    watchExtent,
    removeWatchExtent,
    setContainer,
    removeAll,
    remove,
    add,
    addMany,
    destroy
  };
};
export default useWaterPoint;
