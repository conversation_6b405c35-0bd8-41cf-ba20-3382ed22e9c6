package org.thingsboard.server.dao.model.sql;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.TB_OPERATING_INCOME_INPUT_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class OperatingIncomeInput {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.TB_OPERATING_INCOME_INPUT_STATION_ID)
    private String stationId;

    @Column(name = ModelConstants.TB_OPERATING_INCOME_INPUT_TS)
    private String ts;

    @Column(name = ModelConstants.TB_OPERATING_INCOME_INPUT_WATER_SALES)
    private BigDecimal waterSales;

    @Column(name = ModelConstants.TB_OPERATING_INCOME_INPUT_MONEY)
    private BigDecimal money;

    @Column(name = ModelConstants.TB_OPERATING_INCOME_INPUT_UPDATE_USER)
    private String updateUser;

    @Column(name = ModelConstants.UPDATE_TIME)
    private Date updateTime;

    @Column(name = ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;


}