package org.thingsboard.server.dao.smartPipe;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.request.PartitionMountRequest;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.PartitionCustMeter;
import org.thingsboard.server.dao.sql.smartPipe.PartitionCustMeterMapper;

import java.util.Date;
import java.util.List;

/**
 *
 */
@Service
public class PartitionCustMeterServiceImpl implements PartitionCustMeterService {

    @Autowired
    private PartitionCustMeterMapper partitionCustMeterMapper;

    @Override
    public PartitionCustMeter save(PartitionCustMeter partitionCustMeter) {
        if (StringUtils.isBlank(partitionCustMeter.getId())) {
            partitionCustMeter.setCreateTime(new Date());
            partitionCustMeterMapper.insert(partitionCustMeter);
        } else {
            partitionCustMeterMapper.updateById(partitionCustMeter);
        }
        return partitionCustMeter;
    }


    @Override
    public PageData<PartitionCustMeter> getList(PartitionMountRequest request) {
        IPage<PartitionCustMeter> page = new Page<>(request.getPage(), request.getSize());
        IPage<PartitionCustMeter> result = partitionCustMeterMapper.getList(page, request);
        return new PageData<>(result.getTotal(), result.getRecords());
    }

    @Override
    public void delete(List<String> ids) {
        partitionCustMeterMapper.deleteBatchIds(ids);
    }

}
