package org.thingsboard.server.dao.model.sql.base;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 平台管理-推送方案配置对象 base_push_scheme_configuration
 *
 * <AUTHOR>
 * @date 2025-06-18
 */
@ApiModel(value = "推送方案配置", description = "推送方案配置实体类")
@Data
public class BasePushSchemeConfiguration {

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 方案名称
     */
    @ApiModelProperty(value = "方案名称")
    private String name;

    /**
     * 关联模板
     */
    @ApiModelProperty(value = "关联模板")
    private String templateId;

    /**
     * 目标用户类型
     */
    @ApiModelProperty(value = "目标用户类型")
    private String targetType;

    /**
     * 触发条件（事件触发、定时任务、手动触发）
     */
    @ApiModelProperty(value = "触发条件（事件触发、定时任务、手动触发）")
    private String triggerType;

    /**
     * 发送策略（立即发送、延迟发送、指定事件发送）
     */
    @ApiModelProperty(value = "发送策略（立即发送、延迟发送、指定事件发送）")
    private String sendStrategy;

    /**
     * 状态（0-禁用，1-启用）
     */
    @ApiModelProperty(value = "状态（0-禁用，1-启用）")
    private String status;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createTime;
}
