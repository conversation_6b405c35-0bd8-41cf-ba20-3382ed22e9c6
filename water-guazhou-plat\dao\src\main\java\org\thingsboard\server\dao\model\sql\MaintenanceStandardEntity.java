package org.thingsboard.server.dao.model.sql;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.DTO.MaintenanceStandardDetailDTO;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;
import java.util.Date;
import java.util.List;

@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.MAINTENANCE_STANDARD_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class MaintenanceStandardEntity {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.MAINTENANCE_STANDARD_NAME)
    private String name;

    @Column(name = ModelConstants.MAINTENANCE_STANDARD_DEVICE_TYPE)
    private String deviceType;

    @Column(name = ModelConstants.MAINTENANCE_STANDARD_REMARK)
    private String remark;

    @Column(name = ModelConstants.MAINTENANCE_STANDARD_DETAIL)
    private String detail;

    @Column(name = ModelConstants.CREATOR_PROPERTY)
    private String creator;

    @Column(name = ModelConstants.CREATE_TIME)
    private Date createTime;

    @Column(name = ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;

    @Transient
    private List<MaintenanceStandardDetailDTO> detailList;

    public MaintenanceStandardEntity(String id, String name, String deviceType, String remark, String creator, Date createTime, String tenantId) {
        this.id = id;
        this.name = name;
        this.deviceType = deviceType;
        this.remark = remark;
        this.creator = creator;
        this.createTime = createTime;
        this.tenantId = tenantId;
    }

}
