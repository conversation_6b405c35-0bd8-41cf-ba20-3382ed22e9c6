package org.thingsboard.server.dao.version;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.AppVersionEntity;
import org.thingsboard.server.dao.sql.version.AppVersionRepository;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class AppVersionService {

    @Autowired
    private AppVersionRepository appVersionRepository;

    /**
     * 获取最新版本的APP信息
     *
     * @return app信息
     */
    public AppVersionEntity getNewVersion() {
        Page<AppVersionEntity> page = appVersionRepository.findAll(new PageRequest(0, 1, Sort.Direction.DESC, "createTime"));
        List<AppVersionEntity> content = page.getContent();
        if (content != null && content.size() > 0) {
            return content.get(0);
        }

        return null;
    }

    public void save(AppVersionEntity entity) {
        if (entity.getCreateTime() == null) {
            entity.setCreateTime(new Date());
        }

        appVersionRepository.save(entity);
    }

    public PageData<AppVersionEntity> findList(int page, int size) {
        PageRequest pageRequest = new PageRequest(page - 1, size, Sort.Direction.DESC, "createTime");
        Page<AppVersionEntity> pageResult = appVersionRepository.findAll(pageRequest);
        return new PageData<>(pageResult.getTotalElements(), pageResult.getContent());
    }

    public AppVersionEntity getNewVersion(String appKey, String tenantKey) {
        List<AppVersionEntity> content;
        if (StringUtils.isNotBlank(tenantKey)) {
            content = appVersionRepository.findByAppKeyAndTenantKeyOrderByCreateTimeDesc(appKey, tenantKey);
        } else {
            content = appVersionRepository.findByAppKeyOrderByCreateTimeDesc(appKey);
        }
        if (content != null && content.size() > 0) {
            return content.get(0);
        }

        return null;
    }

    public void remove(List<String> ids) {
        for (String id : ids) {
            appVersionRepository.delete(id);
        }
    }
}
