package org.thingsboard.server.dao.sql.smartOperation.construction;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionTaskInfo;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemScope;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionTaskInfoPageRequest;

@Mapper
public interface SoConstructionTaskInfoMapper extends BaseMapper<SoConstructionTaskInfo> {
    @SuppressWarnings("methodNotInXmlInspection")
    IPage<SoConstructionTaskInfo> findByPage(SoConstructionTaskInfoPageRequest request);

    @SuppressWarnings("methodNotInXmlInspection")
    boolean update(SoConstructionTaskInfo entity);

    @SuppressWarnings("methodNotInXmlInspection")
    boolean updateFully(SoConstructionTaskInfo entity);

    int save(SoConstructionTaskInfo entity);

    boolean markAsComplete(@Param("tableName") String tableName, @Param("id") String id, @Param("scope") SoGeneralSystemScope scope);

    boolean markAsCompleteDirect(@Param("constructionCode") String constructionCode,
                                 @Param("tenantId") String tenantId,
                                 @Param("scope") SoGeneralSystemScope scope);

    boolean isComplete(@Param("tableName") String tableName, @Param("id") String id, @Param("scope") SoGeneralSystemScope scope);

    boolean isCompleteDirect(@Param("tableName") String tableName,
                             @Param("constructionCode") String constructionCode,
                             @Param("tenantId") String tenantId,
                             @Param("scope") SoGeneralSystemScope scope);
}
