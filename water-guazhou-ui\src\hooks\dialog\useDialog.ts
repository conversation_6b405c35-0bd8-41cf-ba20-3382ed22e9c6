/**
 * 弹窗的属性状态
 * @param props 标题类型与显示文本的对应关系，{add: '新增',edit: '编辑'}
 * @returns
 */
export const useDialog = (props?: Record<string, string>) => {
  const dialogType = ref<'add' | 'edit' | 'detail'>('add')
  const dialogTitleDefault = {
    add: '新增',
    edit: '编辑',
    detail: '详情',
    ...(props || {})
  }
  const dialogTitle = computed(() => {
    return dialogTitleDefault[dialogType.value]
  })
  return {
    dialogType,
    dialogTitle
  }
}
