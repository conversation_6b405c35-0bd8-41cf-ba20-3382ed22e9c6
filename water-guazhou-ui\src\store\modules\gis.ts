import { AutoLogin, GetPipeLayerIds, queryLayerClassName } from '@/api/mapservice'
import storage from '@/utils/storage'

export const useGisStore = defineStore('gis', () => {
  const initialState: Store_Gis_State = {
    gToken: localStorage.getItem('gToken') || '',
    gUserInfo: storage.get<Store_Gis_State_UserInfo>('gUserInfo'),
    gLayerConfig: undefined,
    gLayerInfos: undefined,
    gLayerIds: undefined,
    gLayerOption_Line: undefined,
    gLayerOption_Point: undefined
  }
  const state = reactive<Store_Gis_State>(initialState)
  const acitons = {
    SET_gToken: payload => {
      localStorage.setItem('gToken', payload)
      state.gToken = payload
    },
    SET_gUserInfo: payload => {
      storage.set('gUserInfo', payload)
    },
    SET_gLayerConfig: (payload: IGisLayerConfig[]) => {
      state.gLayerConfig = payload
    },
    SET_gLayerInfoes: (payload: ILayerInfo[]) => {
      state.gLayerInfos = payload
      state.gLayerIds = payload?.map(item => item.layerid)
      state.gLayerOption_Line = payload
        ?.filter(item => item.geometrytype === 'esriGeometryPolyline')
        .map(item => {
          return {
            label: item.layername,
            value: item.layerid,
            data: item
          }
        }) || []
      state.gLayerOption_Point = payload
        ?.filter(item => item.geometrytype === 'esriGeometryPoint')
        .map(item => {
          return {
            label: item.layername,
            value: item.layerid,
            data: item
          }
        }) || []
    },
    Auth: async () => {
      if (!state.gToken || !state.gUserInfo) {
        try {
          const res = await AutoLogin()
          if (res.data?.code === 10000) {
            acitons.SET_gToken(res.data?.result?.token)
            acitons.SET_gUserInfo({
              ...(res.data?.result || {}),
              username: '0000303'
            })
          }
        } catch (error) {
          console.log('gis用户授权失败')
        }
      }
    },
    SET_gLayerInfoesAsync: async () => {
      try {
        if (!state.gToken) await acitons.Auth()
        if (!state.gLayerIds) {
          const layerIds = await GetPipeLayerIds()
          state.gLayerIds = layerIds.data?.result?.rows?.map(item => item.layerid)
        }
        if (!state.gLayerInfos) {
          const layerInfo = await queryLayerClassName(state.gLayerIds || [])
          state.gLayerInfos = layerInfo.data?.result?.rows || []
        }
      } catch (error) {
        console.log(error)
      }
    }
  }
  return { ...toRefs(state), ...acitons }
})
