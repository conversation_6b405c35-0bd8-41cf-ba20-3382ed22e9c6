/**
 * Copyright © 2016-2019 The Thingsboard Authors
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.common.data.utils;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.thingsboard.server.common.data.kv.AttributeKvEntry;

import java.io.IOException;
import java.util.*;

import static java.util.Calendar.*;

public class TimeDiff {


    public static List<Long> sortTimeStr(List<String> dateList) {
        Long[] dates = new Long[dateList.size()];
        for (int i = 0; i < dateList.size(); i++) {
            dates[i] = DateUtils.str2Date(dateList.get(i), DateUtils.DATE_FORMATE_DEFAULT).getTime();
        }
        return sortTime(dates);
    }

    private static List<Long> sortTime(Long[] dateList) {
        Arrays.sort(dateList);
        List<Long> resultList = new ArrayList<>();
        for (int i = 0; i < dateList.length; i++) {
            resultList.add(dateList[i]);
        }
        return resultList;
    }

    /**
     * 解析换表时间返回排序过的时间map
     * 注：换表时间格式为timestamp，起始时间-结束时间，多个换表时间之间以，进行分割
     *
     * @param changeTime
     */
    public static HashMap<Long, Long> changemeter(String changeTime) {
        ObjectMapper objectMapper = new ObjectMapper();
        HashMap<Long, Long> result = new HashMap<>();
        try {
            JsonNode jsonNode = objectMapper.readTree(changeTime);
            Iterator<JsonNode> elements = jsonNode.elements();
            elements.forEachRemaining(json -> {
                result.put(DateUtils.str2Date(json.get("start").asText(), DateUtils.DATE_FORMATE_DEFAULT).getTime(), DateUtils.str2Date(json.get("end").asText(), DateUtils.DATE_FORMATE_DEFAULT).getTime());
            });
        } catch (IOException e) {
            e.printStackTrace();
        }
        return result;
    }

    /**
     * 获取当天的第一个时间点
     *
     * @return
     */
    public static long getTodayFirstTime(Long s) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date(s));
        calendar.set(HOUR_OF_DAY, 0);
        calendar.set(MINUTE, 0);
        calendar.set(SECOND, 0);
        calendar.set(MILLISECOND, 0);
        return calendar.getTimeInMillis();
    }

    /**
     * 获取当年的第一个时间点
     *
     * @return
     */
    public static long getYearFirstTime() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(HOUR_OF_DAY, 0);
        calendar.set(MINUTE, 0);
        calendar.set(SECOND, 0);
        calendar.set(MILLISECOND, 0);
        calendar.set(Calendar.DAY_OF_YEAR, 1);
        return calendar.getTimeInMillis();
    }

    /**
     * 获取当月的第一个时间点
     *
     * @return
     */
    public static long getMonthFirstTime() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(HOUR_OF_DAY, 0);
        calendar.set(MINUTE, 0);
        calendar.set(SECOND, 0);
        calendar.set(MILLISECOND, 0);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        return calendar.getTimeInMillis();
    }

    /**
     * 判断抄表时间是否在当天的换表时间之前
     * 如果抄表时间在换表时间之前，则取当天的抄表时间到换表时间之间的最后一条数据为换表数据，如果不是，则取前一天到换表时间的最后一条数据为最后一条数据
     *
     * @param startTime
     * @param list
     * @return
     */
    public static boolean CompareChangemeterTime(Long startTime, List<AttributeKvEntry> list) {
        Calendar changemeterTime = Calendar.getInstance();
        changemeterTime.setTimeInMillis(startTime);
        Date readmeterTime = new Date();
        Calendar readmeterCalender = Calendar.getInstance();
        if (list != null || list.size() == 1) {
            readmeterTime = DateUtils.getMeterReadingTime(readmeterTime, list.get(0).getValueAsString(), DateUtils.DAY);
        } else
            readmeterTime = DateUtils.getMeterReadingTime(readmeterTime, null, DateUtils.DAY);
        readmeterCalender.setTime(readmeterTime);
        readmeterCalender.set(Calendar.YEAR, changemeterTime.get(Calendar.YEAR));
        readmeterCalender.set(Calendar.MONTH, changemeterTime.get(Calendar.MONTH));
        readmeterCalender.set(Calendar.DAY_OF_MONTH, changemeterTime.get(Calendar.DAY_OF_MONTH));
        return readmeterCalender.before(changemeterTime);
    }


    /**
     * 判断所选时间是否处于两个时间段之间
     *
     * @param time
     * @param start
     * @param end
     * @return
     */
    public static boolean betweenTwoTimes(Long time, Long start, Long end) {
        if (String.valueOf(time).length() < 13)
            time = time * 1000;
        return (time > start && time < end);
    }

    /**
     * 根据起始时间和抄表时间来换算当前时间属于那一天
     */
    public static String getDayByReadmeter(Long l, String hour, String type) {
        if (l.toString().length() < 13)
            l = l * 1000;
        if (hour == null)
            return DateUtils.date2StrByType(l, type);
        else {
            Long time = DateUtils.getMeterReadingTime(new Date(l), hour, type).getTime();
            String result = null;
            if (l < time) {
                result = getLastTime(l, type);
            } else {
                result = DateUtils.date2StrByType(l, type);
            }
            return result;
        }
    }


    public static String fifiteen2day(String time, String type, String value) {
        int hour = 0;
        int minute = 0;
        if (value != null) {
            hour = Integer.parseInt(value.split(":")[0]);
            minute = Integer.parseInt(value.split(":")[1]);
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(DateUtils.str2Date(time, DateUtils.DATE_FORMATE_MINUTE));
        switch (type) {
            case DateUtils.HOUR: {
                if (calendar.get(MINUTE) == 0)
                    calendar.set(HOUR_OF_DAY, calendar.get(HOUR_OF_DAY) - 1);
                return DateUtils.date2Str(calendar.getTime(), DateUtils.DATE_FORMATE_HOUR);
            }
            case DateUtils.DAY: {
                if (calendar.get(HOUR_OF_DAY) < hour || (calendar.get(HOUR_OF_DAY) == hour && calendar.get(MINUTE) <= minute))
                    calendar.set(Calendar.DAY_OF_YEAR, calendar.get(Calendar.DAY_OF_YEAR) - 1);
                return DateUtils.date2Str(calendar.getTime(), DateUtils.DATE_FORMATE_DAY);
            }
            case DateUtils.MONTH: {
                if (calendar.get(Calendar.DAY_OF_MONTH) == 1 && (calendar.get(HOUR_OF_DAY) < hour || (calendar.get(HOUR_OF_DAY) == hour && calendar.get(MINUTE) <= minute)))
                    calendar.set(Calendar.DAY_OF_YEAR, calendar.get(Calendar.DAY_OF_YEAR) - 1);
                return DateUtils.date2Str(calendar.getTime(), DateUtils.DATE_FORMATE_MONTH);
            }
            default: {
                if (calendar.get(Calendar.DAY_OF_YEAR) == 1 && (calendar.get(HOUR_OF_DAY) < hour || (calendar.get(HOUR_OF_DAY) == hour && calendar.get(MINUTE) <= minute)))
                    calendar.set(Calendar.DAY_OF_YEAR, calendar.get(Calendar.DAY_OF_YEAR) - 1);
                return DateUtils.date2Str(calendar.getTime(), DateUtils.DATE_FORMATE_YEAR);
            }
        }
    }

    /**
     * 根据类型获取上一个同类型时间节点
     *
     * @param l
     * @param type
     * @return
     */
    public static String getLastTime(Long l, String type) {
        if (l.toString().length() < 13)
            l = l * 1000;
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(l);
        switch (type) {
            case DateUtils.MONTH: {
                calendar.set(Calendar.MONTH, calendar.get(Calendar.MONTH) - 1);
                return DateUtils.date2Str(calendar.getTime(), DateUtils.DATE_FORMATE_MONTH);
            }
            case DateUtils.YEAR: {
                calendar.set(Calendar.YEAR, calendar.get(Calendar.YEAR) - 1);
                return DateUtils.date2Str(calendar.getTime(), DateUtils.DATE_FORMATE_YEAR);
            }
            default: {
                calendar.set(Calendar.DAY_OF_YEAR, calendar.get(Calendar.DAY_OF_YEAR) - 1);
                return DateUtils.date2Str(calendar.getTime(), DateUtils.DATE_FORMATE_DAY);
            }
        }
    }


    /**
     * 根据传入时间来获取距离当前时间最近的时间点
     *
     * @param ts
     * @return
     */
    public static String getTrulyMinute(Long ts, String type) {
        Calendar calendar = Calendar.getInstance();
        if (ts.toString().length() < 13)
            ts = ts * 1000;
        calendar.setTimeInMillis(ts);
        switch (type) {
            case DateUtils.SECOND: {
                if (calendar.get(SECOND) == 0)
                    return DateUtils.date2Str(calendar.getTime(), DateUtils.DATE_FORMATE_DEFAULT);
                if (calendar.get(SECOND) <= 30)
                    calendar.set(SECOND, 30);
                else {
                    calendar.set(MINUTE, calendar.get(MINUTE) + 1);
                    calendar.set(SECOND, 0);
                }
                return DateUtils.date2Str(calendar.getTime(), DateUtils.DATE_FORMATE_DEFAULT);
            }
            case DateUtils.MINUTE: {
                return DateUtils.date2Str(calendar.getTime(), DateUtils.DATE_FORMATE_MINUTE);

            }
            case DateUtils.FIVE_MINUTE: {
                if (calendar.get(MINUTE) <= 55 && calendar.get(MINUTE) % 5 == 0)
                    return DateUtils.date2Str(calendar.getTime(), DateUtils.DATE_FORMATE_MINUTE);
                else if (calendar.get(MINUTE) < 55) {
                    calendar.set(MINUTE, calendar.get(MINUTE) + 5 - calendar.get(MINUTE) % 5);
                } else {
                    calendar.set(Calendar.HOUR, calendar.get(calendar.HOUR) + 1);
                    calendar.set(MINUTE, 0);
                }
                return DateUtils.date2Str(calendar.getTime(), DateUtils.DATE_FORMATE_MINUTE);
            }
            case DateUtils.TEN_MINUTE: {
                if (calendar.get(MINUTE) <= 50 && calendar.get(MINUTE) % 10 == 0)
                    return DateUtils.date2Str(calendar.getTime(), DateUtils.DATE_FORMATE_MINUTE);
                else if (calendar.get(MINUTE) < 50) {
                    calendar.set(MINUTE, calendar.get(MINUTE) + 10 - calendar.get(MINUTE) % 10);
                } else {
                    calendar.set(Calendar.HOUR, calendar.get(calendar.HOUR) + 1);
                    calendar.set(MINUTE, 0);
                }
                return DateUtils.date2Str(calendar.getTime(), DateUtils.DATE_FORMATE_MINUTE);
            }
            case DateUtils.FIFTEEN_MINUTE: {
                if (calendar.get(MINUTE) == 0)
                    return DateUtils.date2Str(calendar.getTime(), DateUtils.DATE_FORMATE_MINUTE);
                if (calendar.get(MINUTE) <= 15)
                    calendar.set(MINUTE, 15);
                else if (calendar.get(MINUTE) <= 30)
                    calendar.set(MINUTE, 30);
                else if (calendar.get(MINUTE) <= 45)
                    calendar.set(MINUTE, 45);
                else {
                    calendar.set(Calendar.HOUR, calendar.get(calendar.HOUR) + 1);
                    calendar.set(MINUTE, 0);
                }
                return DateUtils.date2Str(calendar.getTime(), DateUtils.DATE_FORMATE_MINUTE);
            }
            default: {
                return DateUtils.date2Str(calendar.getTime(), DateUtils.DATE_FORMATE_MINUTE);
            }
        }

    }

    /**
     * 获取当前时间点上一个15分钟时间点
     *
     * @param ts
     * @return
     */
    public static String getLastFiveMinute(String ts) {
        Date date = DateUtils.str2Date(ts, DateUtils.DATE_FORMATE_MINUTE);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.setTimeInMillis(calendar.getTimeInMillis() - DateUtils.FIFTEEN_MINUTE_TIMESTAMP);
        return DateUtils.date2Str(calendar.getTime(), DateUtils.DATE_FORMATE_MINUTE);
    }

    /**
     * 获取当前时间点上一个15分钟时间点
     *
     * @param ts
     * @return
     */
    public static String getAfterFiveMinute(String ts, String type) {

        Date date = type.equals(DateUtils.SECOND) ? DateUtils.str2Date(ts, DateUtils.DATE_FORMATE_DEFAULT) : DateUtils.str2Date(ts, DateUtils.DATE_FORMATE_MINUTE);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        switch (type) {
            case DateUtils.SECOND: {
                calendar.setTimeInMillis(calendar.getTimeInMillis() + 1000 * 30);
                return DateUtils.date2Str(calendar.getTime(), DateUtils.DATE_FORMATE_DEFAULT);
            }
            case DateUtils.MINUTE: {
                calendar.setTimeInMillis(calendar.getTimeInMillis() + 1000 * 60);
                break;
            }
            case DateUtils.FIVE_MINUTE: {
                calendar.setTimeInMillis(calendar.getTimeInMillis() + 1000 * 60 * 5);
                break;
            }
            case DateUtils.TEN_MINUTE: {
                calendar.setTimeInMillis(calendar.getTimeInMillis() + 1000 * 60 * 10);
                break;
            }
            case DateUtils.FIFTEEN_MINUTE: {
                calendar.setTimeInMillis(calendar.getTimeInMillis() + DateUtils.FIFTEEN_MINUTE_TIMESTAMP);
                break;
            }
            case DateUtils.THIRTY_MINUTE: {
                calendar.setTimeInMillis(calendar.getTimeInMillis() + DateUtils.FIFTEEN_MINUTE_TIMESTAMP*2);
                break;
            }
            default:
        }
        return DateUtils.date2Str(calendar.getTime(), DateUtils.DATE_FORMATE_MINUTE);
    }

    /**
     * 获取当前时间点上一个小时间点
     *
     * @param ts
     * @return
     */
    public static String getLastHour(Long ts, String type, String meterReadTime) {
        int hour = 0;
        int minute = 0;
        if (meterReadTime != null) {
            hour = Integer.parseInt(meterReadTime.split(":")[0]);
            minute = Integer.parseInt(meterReadTime.split(":")[1]);
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(ts * 1000);
        switch (type) {
            case DateUtils.HOUR: {
                if (calendar.get(MINUTE) == 0) {
                    calendar.set(HOUR_OF_DAY, calendar.get(HOUR_OF_DAY) - 1);
                    return DateUtils.date2Str(calendar.getTime(), DateUtils.DATE_FORMATE_HOUR);
                } else
                    return null;
            }
            case DateUtils.DAY: {
                if (calendar.get(MINUTE) == minute && calendar.get(HOUR_OF_DAY) == hour) {
                    calendar.set(Calendar.DAY_OF_YEAR, calendar.get(Calendar.DAY_OF_YEAR) - 1);
                    return DateUtils.date2Str(calendar.getTime(), DateUtils.DATE_FORMATE_DAY);
                } else
                    return null;
            }
            case DateUtils.MONTH: {
                if (calendar.get(MINUTE) == minute && calendar.get(HOUR_OF_DAY) == hour && calendar.get(Calendar.DAY_OF_MONTH) == 1) {
                    calendar.set(Calendar.MONTH, calendar.get(Calendar.MONTH) - 1);
                    return DateUtils.date2Str(calendar.getTime(), DateUtils.DATE_FORMATE_MONTH);
                } else
                    return null;
            }
            case DateUtils.YEAR: {
                if (calendar.get(MINUTE) == minute && calendar.get(HOUR_OF_DAY) == hour && calendar.get(Calendar.DAY_OF_MONTH) == 1 && calendar.get(Calendar.MONTH) == 0) {
                    calendar.set(Calendar.YEAR, calendar.get(Calendar.YEAR) - 1);
                    return DateUtils.date2Str(calendar.getTime(), DateUtils.DATE_FORMATE_YEAR);
                } else
                    return null;
            }
        }
        return null;
    }


    /**
     * 判断所选时间是否处于两个抄表时间之间(将传入时间根据抄表时间进行格式化)
     */
    public static boolean betweenTwoDays(Long time, Long start, String hour) {
        Date readmeterTime = new Date(start);
        Calendar readmeterCalender = Calendar.getInstance();
        if (hour != null) {
            readmeterTime = DateUtils.getMeterReadingTime(readmeterTime, hour, DateUtils.DAY);
        } else
            readmeterTime = DateUtils.getMeterReadingTime(readmeterTime, null, DateUtils.DAY);
        return (time > readmeterTime.getTime() && time < DateUtils.nextDay(readmeterTime.getTime()));
    }

    /**
     * 获取换表时间当天的抄表时间
     *
     * @param startTime
     * @param list
     * @return
     */
    public static Long getReadmeterTimeInChange(Long startTime, List<AttributeKvEntry> list) {
        Calendar changemeterTime = Calendar.getInstance();
        changemeterTime.setTimeInMillis(startTime);
        Date readmeterTime = new Date();
        Calendar readmeterCalender = Calendar.getInstance();
        if (list != null || list.size() == 1) {
            readmeterTime = DateUtils.getMeterReadingTime(readmeterTime, list.get(0).getValueAsString(), DateUtils.DAY);
        } else
            readmeterTime = DateUtils.getMeterReadingTime(readmeterTime, null, DateUtils.DAY);
        readmeterCalender.setTime(readmeterTime);
        readmeterCalender.set(Calendar.YEAR, changemeterTime.get(Calendar.YEAR));
        readmeterCalender.set(Calendar.MONTH, changemeterTime.get(Calendar.MONTH));
        readmeterCalender.set(Calendar.DAY_OF_MONTH, changemeterTime.get(Calendar.DAY_OF_MONTH));
        return readmeterCalender.getTimeInMillis();
    }


    public static boolean isOnTimeSharing(String date, List<String> times) {
        for (String time : times) {
            String[] array = time.split("-");
            Calendar now = Calendar.getInstance();
            now.setTime(DateUtils.str2Date(date, DateUtils.DATE_FORMATE_MINUTE));

            Calendar before = Calendar.getInstance();
            before.setTime(now.getTime());
            if (now.get(HOUR_OF_DAY) == 0 && now.get(MINUTE) == 0 && array[1].split(":")[0].equals("24"))
                before.set(Calendar.DAY_OF_YEAR, before.get(Calendar.DAY_OF_YEAR) - 1);
            before.set(HOUR_OF_DAY, Integer.parseInt(array[0].split(":")[0]));
            before.set(MINUTE, Integer.parseInt(array[0].split(":")[1]));

            Calendar after = Calendar.getInstance();
            after.setTime(now.getTime());
            if (array[1].split(":")[0].equals("24")) {
                after.set(Calendar.DAY_OF_YEAR, after.get(Calendar.DAY_OF_YEAR) + 1);
                after.set(HOUR_OF_DAY, 0);
                after.set(MINUTE, 0);
            } else {
                after.set(HOUR_OF_DAY, Integer.parseInt(array[1].split(":")[0]));
                after.set(MINUTE, Integer.parseInt(array[1].split(":")[1]));
            }

            if ((now.getTimeInMillis() >= before.getTimeInMillis()) && (now.getTimeInMillis() <= after.getTimeInMillis()))
                return true;
        }
        return false;
    }


    public static List<String> getMinuteByTime(long start, long end) {
        List<String> list = new ArrayList<>();
        while (start <= end) {
            list.add(DateUtils.date2Str(start, DateUtils.DATE_FORMATE_MINUTE));
            start = start + 1000 * 60;
        }
        return list;
    }


    /**
     * 根据类型，修正数据查询的时间
     *
     * @param start
     * @param
     * @return
     */
    public static Long convertStartByType(long start, String type) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(start);
        switch (type) {
            case DateUtils.SECOND: {
                calendar.set(SECOND, 0);
                start = calendar.getTimeInMillis();
                break;
            }
            case DateUtils.MINUTE: {
                calendar.set(MILLISECOND, 0);
                calendar.set(SECOND, 0);
                start = calendar.getTimeInMillis() - 60 * 1000;
                break;
            }
            case DateUtils.FIVE_MINUTE: {
                calendar.set(MILLISECOND, 0);
                calendar.set(SECOND, 0);
                calendar.set(MINUTE, ((calendar.get(MINUTE) / 5) - 1) * 5);
                start = calendar.getTimeInMillis();
                break;
            }
            case DateUtils.TEN_MINUTE: {
                calendar.set(MILLISECOND, 0);
                calendar.set(SECOND, 0);
                calendar.set(MINUTE, ((calendar.get(MINUTE) / 10) - 1) * 10);
                start = calendar.getTimeInMillis();
                break;
            }
            case DateUtils.FIFTEEN_MINUTE: {
                calendar.set(MILLISECOND, 0);
                calendar.set(SECOND, 0);
                calendar.set(MINUTE, ((calendar.get(MINUTE) / 15) - 1) * 15);
                start = calendar.getTimeInMillis();
                break;
            }
            case DateUtils.THIRTY_MINUTE: {
                calendar.set(MILLISECOND, 0);
                calendar.set(SECOND, 0);
                calendar.set(MINUTE, ((calendar.get(MINUTE) / 30) - 1) * 30);
                start = calendar.getTimeInMillis();
                break;
            }
            case "1h": {
                calendar.set(MILLISECOND, 0);
                calendar.set(SECOND, 0);
                calendar.set(MINUTE, 0);
                start = calendar.getTimeInMillis();
                break;
            }
            case "1d": {
                calendar.set(MILLISECOND, 0);
                calendar.set(SECOND, 0);
                calendar.set(MINUTE, 0);
                calendar.set(HOUR_OF_DAY, 0);
                start = calendar.getTimeInMillis();
                break;
            }
            case "1nc": {
                calendar.set(MILLISECOND, 0);
                calendar.set(SECOND, 0);
                calendar.set(MINUTE, 0);
                calendar.set(HOUR_OF_DAY, 0);
                calendar.set(DAY_OF_MONTH, 1);
                start = calendar.getTimeInMillis();
                break;
            }
            case "1yc": {
                calendar.set(MILLISECOND, 0);
                calendar.set(SECOND, 0);
                calendar.set(MINUTE, 0);
                calendar.set(HOUR_OF_DAY, 0);
                calendar.set(DAY_OF_YEAR, 1);
                start = calendar.getTimeInMillis();
                break;
            }
            default:
                break;
        }
        return start;
    }


    /**
     * 根据类别获取当前类别的开始时间
     *
     * @return
     */
    public static long getStartTimeByType(String type) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(HOUR, 0);
        calendar.set(MINUTE, 0);
        calendar.set(SECOND, 0);
        calendar.set(MILLISECOND, 0);
        switch (type) {
            case DateUtils.DAY:
                return calendar.getTimeInMillis();
            case DateUtils.WEEK:
                calendar.set(DAY_OF_WEEK, 2);
                return calendar.getTimeInMillis();
            case DateUtils.MONTH:
                calendar.set(DAY_OF_MONTH, 1);
                return calendar.getTimeInMillis();
            case DateUtils.SEASON:
                calendar.set(MONTH, getSeasonMonthfirst(calendar.get(MONTH)));
                calendar.set(DAY_OF_MONTH, 1);
                return calendar.getTimeInMillis();
            default:
                calendar.set(DAY_OF_YEAR, 1);
                return calendar.getTimeInMillis();
        }
    }


    /**
     * 根据类别获取当前类别的结束时间
     *
     * @return
     */
    public static long getEndTimeByType(String type) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(HOUR, 0);
        calendar.set(MINUTE, 0);
        calendar.set(SECOND, 0);
        calendar.set(MILLISECOND, 0);
        switch (type) {
            case DateUtils.DAY:
                calendar.set(DAY_OF_YEAR, calendar.get(DAY_OF_YEAR) + 1);
                return calendar.getTimeInMillis();
            case DateUtils.WEEK:
                calendar.set(DAY_OF_WEEK, 2);
                calendar.set(DAY_OF_YEAR, calendar.get(DAY_OF_YEAR) + 7);
                return calendar.getTimeInMillis();
            case DateUtils.MONTH:
                calendar.set(DAY_OF_MONTH, 1);
                calendar.set(MONTH, calendar.get(MONTH) + 1);
                return calendar.getTimeInMillis();
            case DateUtils.SEASON:
                calendar.set(MONTH, getSeasonMonthLast(calendar.get(MONTH))+1);
                calendar.set(DAY_OF_MONTH, 1);
                return calendar.getTimeInMillis();
            default:
                calendar.set(YEAR,calendar.get(YEAR)+1);
                calendar.set(DAY_OF_YEAR, 1);
                return calendar.getTimeInMillis();
        }
    }


    /**
     * 获取当前季度的第一个月
     *
     * @param month
     * @return
     */
    public static int getSeasonMonthfirst(int month) {
        if (month >= 0 && month <= 2) {
            return 0;
        } else if (month >= 3 && month <= 5) {
            return 3;
        } else if (month >= 6 && month <= 8) {
            return 6;
        } else {
            return 9;
        }
    }

    /**
     * 获取当前季度的第一个月
     *
     * @param month
     * @return
     */
    public static int getSeasonMonthLast(int month) {
        if (month >= 0 && month <= 2) {
            return 2;
        } else if (month >= 3 && month <= 5) {
            return 5;
        } else if (month >= 6 && month <= 8) {
            return 8;
        } else {
            return 11;
        }
    }


}