package org.thingsboard.server.dao.circuit;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.smartProduction.circuit.CircuitConfig;
import org.thingsboard.server.dao.sql.smartProduction.circuit.CircuitConfigMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.circuit.CircuitConfigPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.circuit.CircuitConfigSaveRequest;

import java.util.List;

@Service
public class CircuitConfigServiceImpl implements CircuitConfigService {
    @Autowired
    private CircuitConfigMapper mapper;

    @Override
    public IPage<CircuitConfig> findAllConditional(CircuitConfigPageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    public CircuitConfig save(CircuitConfigSaveRequest entity) {
        return QueryUtil.saveOrUpdateOneByRequest(entity, mapper::insert, mapper::updateById);
    }

    @Override
    public boolean update(CircuitConfig entity) {
        return mapper.update(entity);
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }

    @Override
    public List<String> getTypes(String tenantId) {
        return mapper.getTypes(tenantId);
    }
}
