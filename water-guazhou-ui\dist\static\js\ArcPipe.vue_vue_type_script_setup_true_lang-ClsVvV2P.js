import u from"./WMSLayer-mTaW758E.js";import{B as S,_ as I}from"./AnimatedLinesLayer-B2VbV4jv.js";import{d as y,W as _,ad as g,o as f,X as G,g as h,n as E}from"./index-r0dFAfgr.js";import{dw as L}from"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./geometryEngineBase-BhsKaODW.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./project-DUuzYgGl.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import{geodesicLength as C}from"./geometryEngine-OGzB5MRq.js";const N=()=>{const a=new S({id:"animatedlineslayer",title:"管网流动光线"});return{addTo:e=>{e==null||e.map.add(a),window.SITE_CONFIG.GIS_CONFIG.gisDissolvedService?L(window.SITE_CONFIG.GIS_CONFIG.gisService+"/"+window.SITE_CONFIG.GIS_CONFIG.gisDissolvedService+"/0",{where:"1=1",geometry:e==null?void 0:e.extent,returnGeometry:!0,outFields:["OBJECTID","SHAPE.LEN"]}).then(i=>{const r=i.features.filter(s=>s.attributes["SHAPE.LEN"]>=150).map(s=>{const t=s.geometry;return t.paths=t.paths.filter(l=>C({paths:[l],spatialReference:e==null?void 0:e.spatialReference},"meters")>150),s.attributes.color=[11,255,140],{attributes:s.attributes,geometry:t,spatialReference:e==null?void 0:e.spatialReference}});a.removeAll(),a.addMany(r)}):console.log("未配置流动光线服务地址")}}},F={class:"arcpipe"},j=y({__name:"ArcPipe",props:{disableLineAnimation:{type:Boolean}},emits:["pipe-loaded"],setup(a,{emit:p}){const e=p,i=a,r=_(),s=g("view");let t;GIS_SERVER_SWITCH?t=new u({id:"pipelayer",title:"供水管网",url:"/geoserver/guazhou/wms",sublayers:[{name:"管线",type:"MultiLineString",spatialReferences:"EPSG:3857"},{name:"测点",type:"Point",spatialReferences:"EPSG:3857"}],visible:!0,version:"1.1.0"}):t=new I({id:"pipelayer",title:"管网",url:window.SITE_CONFIG.GIS_CONFIG.gisService+window.SITE_CONFIG.GIS_CONFIG.gisPipeDynamicService}),s==null||s.map.add(t);const l=N();return f(async()=>{var o,m;try{await t.when(),i.disableLineAnimation||l.addTo(s);const n=[];t==null||t.allSublayers.map(d=>n.push(d.id)),await r.Auth();const c=await G(n);r.SET_gLayerInfoes((m=(o=c.data)==null?void 0:o.result)==null?void 0:m.rows)}catch(n){console.log(n),console.log("加载管网失败")}e("pipe-loaded",t)}),(o,m)=>(h(),E("div",F))}});export{j as _};
