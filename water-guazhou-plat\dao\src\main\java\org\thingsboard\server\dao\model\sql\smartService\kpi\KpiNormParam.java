package org.thingsboard.server.dao.model.sql.smartService.kpi;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * kpi指标配置
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2021-11-15
 */
@TableName("tb_service_kpi_norm_param")
@Data
public class KpiNormParam {

    @TableId
    private String id;

    private String code;

    private String name;

    private Double weight;

    private String type;

    private Integer orderNum;

    private String jiaJianType;

    private String remark;

    private Boolean enabled;

    private String source;

    private Date createTime;

    private Date updateTime;

    private String tenantId;

}
