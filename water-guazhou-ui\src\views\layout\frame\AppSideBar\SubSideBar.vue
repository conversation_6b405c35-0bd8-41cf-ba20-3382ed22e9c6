<template>
  <div class="submenu-bg overlay-y">
    <template v-for="(cMenu, index) in useAppStore().subMenuParentRoute?.children || []" :key="cMenu.path">
      <div v-if="!cMenu.meta?.hidden" class="submenu-group">
        <div class="submenu-title">
          {{ cMenu.meta?.title }}
        </div>
        <template v-if="cMenu.children?.length">
          <div class="submenu-items">
            <template v-for="(cCMenu, index1) in cMenu.children" :key="index1">
              <SubSideBarItem
                v-if="!cCMenu.meta?.hidden"
                :menu="cCMenu"
                :index="index1"
                :class="[themes[index1 % 4], index1 % 3 === 2 ? 'long' : 'short']"
              ></SubSideBarItem>
            </template>
          </div>
        </template>
        <SubSideBarItem v-else :key="cMenu.path" :menu="cMenu" :index="index % 2" :class="[themes[index % 4], 'short']">
        </SubSideBarItem>
      </div>
    </template>
  </div>
</template>

<script lang="ts" setup>
import SubSideBarItem from './SubSideBarItem.vue';
import { useAppStore } from '@/store';

const themes = ['lightblue', 'darkblue', 'lightgreen', 'darkgreen'];

// 添加默认导出
defineOptions({
  name: 'SubSideBar'
});
</script>

<style lang="scss" scoped>
.submenu-bg {
  width: 260px;
  padding: 12px;
  background: rgba(208, 217, 247, 0.64);
  backdrop-filter: blur(10.5px);
  /* Note: backdrop-filter has minimal browser support */
  // transform: matrix(1, 0, 0, -1, 0, 0);
  height: 100%;
  position: relative;
}

.submenu-group__items {
  display: flex;
}

.submenu-group {
  padding-top: 20px;

  &:nth-child(1) {
    padding-top: 0;
  }
}

.submenu-items {
  display: flex;
  flex-wrap: wrap;
}

.submenu-title {
  font-family: 'PingFang SC';
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  line-height: 22px;
  color: #383f56;
  margin: 0 0 12px 0;
}
</style>
