package org.thingsboard.server.controller.waterSource.inspection;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.sql.smartProduction.circuit.CircuitConfig;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.circuit.CircuitConfigPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.circuit.CircuitConfigSaveRequest;
import org.thingsboard.server.dao.circuit.CircuitConfigService;
import org.thingsboard.server.utils.imodel.annotations.IStarController;

import java.util.List;

@IStarController
@RequestMapping("/api/sp/circuitConfig")
public class CircuitConfigController extends BaseController {
    @Autowired
    private CircuitConfigService service;


    @GetMapping
    public IPage<CircuitConfig> findAllConditional(CircuitConfigPageRequest request) {
        return service.findAllConditional(request);
    }

    @PostMapping
    public CircuitConfig save(@RequestBody CircuitConfigSaveRequest req) throws ThingsboardException {
        return service.save(req);
    }

    @PatchMapping("/{id}")
    public boolean edit(@RequestBody CircuitConfigSaveRequest req, @PathVariable String id) {
        return service.update(req.unwrap(id));
    }

    @DeleteMapping("/{id}")
    public boolean delete(@PathVariable String id) {
        return service.delete(id);
    }
    
    @GetMapping("/types")
    public List<String> names() throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return service.getTypes(tenantId);
    }
}