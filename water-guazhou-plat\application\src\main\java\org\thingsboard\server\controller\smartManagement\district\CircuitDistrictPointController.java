package org.thingsboard.server.controller.smartManagement.district;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.controller.base.BaseController;;
import org.thingsboard.server.dao.model.sql.smartManagement.district.CircuitDistrictPoint;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.district.CircuitDistrictPointPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.district.CircuitDistrictPointSaveRequest;
import org.thingsboard.server.dao.district.CircuitDistrictPointService;
import org.thingsboard.server.utils.imodel.annotations.IStarController;

@IStarController
@RequestMapping("/api/sm/circuitDistrictPoint")
public class CircuitDistrictPointController extends BaseController {
    @Autowired
    private CircuitDistrictPointService service;


    @GetMapping
    public IPage<CircuitDistrictPoint> findAllConditional(CircuitDistrictPointPageRequest request) {
        return service.findAllConditional(request);
    }

    @GetMapping("/{id}")
    public CircuitDistrictPoint findById(@PathVariable String id) {
        return service.findById(id);
    }

    @PostMapping
    public CircuitDistrictPoint save(@RequestBody CircuitDistrictPointSaveRequest req) {
        return service.save(req);
    }

    // @PatchMapping("/{id}")
    public boolean edit(@RequestBody CircuitDistrictPointSaveRequest req, @PathVariable String id) {
        return service.update(req.unwrap(id));
    }

    @DeleteMapping("/{id}")
    public boolean delete(@PathVariable String id) {
        return service.delete(id);
    }
}