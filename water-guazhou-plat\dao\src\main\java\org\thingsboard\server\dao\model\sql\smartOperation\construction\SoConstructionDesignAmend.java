package org.thingsboard.server.dao.model.sql.smartOperation.construction;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseUsername;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.util.Date;


@Getter
@Setter
@ResponseEntity
public class SoConstructionDesignAmend {
    // id
    private String id;

    // 编号
    private String code;

    // 所属设计编号
    private String designCode;

    // 所属工程编号
    private String constructionCode;

    // 变更类型
    private String type;

    // 说明
    private String remark;

    // 附件信息
    private String attachments;

    // 创建人
    @ParseUsername
    private String creator;

    // 创建时间
    private Date createTime;

    // 客户id
    private String tenantId;

}

