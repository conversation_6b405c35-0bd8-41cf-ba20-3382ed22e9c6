import { sortBy } from 'lodash-es'
import { SLConfirm } from '@/utils/Message'

export const useScadaTable = () => {
  const TableConfig = ref<ITable>({
    height: 400,
    dataList: [],
    operationWidth: 220,
    operations: [
      {
        perm: true,
        type: 'primary',
        isTextBtn: true,
        text: '上移',
        icon: 'iconfont icon-shangyi',
        click: (row: any) => handleScadaRowUp(row)
      },
      {
        perm: true,
        type: 'primary',
        isTextBtn: true,
        text: '下移',
        icon: 'iconfont icon-xiayi',
        click: (row: any) => handleScadaRowDown(row)
      },
      {
        perm: true,
        type: 'danger',
        isTextBtn: true,
        text: '删除',
        icon: 'iconfont icon-shanchu',
        click: (row: any) => handleScadaDelete(row)
      }
    ],
    columns: [
      { label: '序号', prop: 'orderNum', width: 80 },
      { label: '组态名称', prop: 'name', formItemConfig: { type: 'input' } },
      { label: '组态链接', prop: 'url', formItemConfig: { type: 'input' } }
    ],
    pagination: { hide: true }
  })
  const handleScadaRowUp = (row: any) => {
    const orderNum = row.orderNum
    const nextRow = TableConfig.value.dataList.find(
      item => item.orderNum === orderNum - 1
    )
    // 当有上一个才进行换位操作
    if (nextRow) {
      row.orderNum--
      nextRow.orderNum++
      resetScadasOrder()
    }
  }
  const handleScadaRowDown = (row: any) => {
    const orderNum = row.orderNum
    const nextRow = TableConfig.value.dataList.find(
      item => item.orderNum === orderNum + 1
    )
    // 当有下一个才进行换位操作
    if (nextRow) {
      row.orderNum++
      nextRow.orderNum--
      resetScadasOrder()
    }
  }
  const handleScadaDelete = (row: any) => {
    if (!row) return
    SLConfirm('确定移除此行？', '提示信息')
      .then(() => {
        TableConfig.value.dataList = TableConfig.value.dataList.filter(
          item => item.orderNum !== row.orderNum
        )
        resetScadasOrder()
      })
      .catch(() => {
        //
      })
  }
  const addScadaRow = () => {
    resetScadasOrder()
    TableConfig.value.dataList.push({
      orderNum: TableConfig.value.dataList.length + 1,
      name: '名称' + moment().valueOf(),
      url: 'https://www.baidu.com'
    })
  }

  const resetScadasOrder = () => {
    TableConfig.value.dataList = sortBy(
      TableConfig.value.dataList,
      item => item.orderNum
    )
  }
  const refreshData = async () => {
    TableConfig.value.dataList = []
  }
  return {
    addScadaRow,
    TableConfig,
    refreshData
  }
}
