import{_ as ge}from"./Search-NSrhrIa_.js";import{bF as k,d as ye,M as _e,c as v,r as f,j as he,s as u,x as w,S as V,bC as be,o as ve,g as j,n as G,q as r,i as p,F as d,p as B,aj as z,G as S,ca as Q,aB as ke,aJ as De,h as Fe,u as we,J as xe,bK as Te,I as Ie,bU as Le,aK as Re,aL as Ue,bW as Ce,K as qe,aq as Me,al as Ne,b7 as Z,ak as Se,bM as J,bq as ee,C as Ye}from"./index-r0dFAfgr.js";import{_ as Ee}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as We}from"./CardTable-rdWOL4_6.js";import{_ as Oe}from"./CardSearch-CB_HNR-Q.js";import{X as te}from"./xlsx-rVJkW9yq.js";import{d as Ae,g as Pe,h as Ve,s as je}from"./pumpRoomInfo-DV75B9MO.js";import{k as Be,l as ze,m as Je,n as $e,o as Xe,q as He,r as Ke}from"./index-CjYkj_FN.js";import"./index-C9hz-UZb.js";const Ge=[{label:"设备编码",prop:"code",minWidth:120},{label:"设备名称",prop:"name",minWidth:120},{label:"设备简称",prop:"nickname",minWidth:120},{label:"厂家名称",prop:"companyName",minWidth:120},{label:"设备型号",prop:"model",minWidth:120},{label:"安装人",prop:"installUserName",minWidth:120},{label:"安装日期",prop:"installDate",minWidth:120,formatter:(y,D)=>k(D).format("YYYY-MM-DD")},{label:"录入日期",prop:"createTime",minWidth:120,formatter:(y,D)=>k(D).format("YYYY-MM-DD")},{minWidth:120,label:"性能参数",prop:"performanceParameters"},{label:"备注",prop:"remark"}],Qe=[{label:"设备名称",field:"name",type:"input",placeholder:"请输入设备名称"},{label:"设备简称",field:"nickname",type:"input",placeholder:"请输入设备简称"},{label:"厂家名称",field:"companyName",type:"input",placeholder:"请输入厂家名称"},{label:"设备型号",field:"model",type:"input",placeholder:"请输入设备型号"},{label:"安装人",field:"installUserName",type:"input",placeholder:"请输入安装人"},{label:"安装日期",field:"installDateFrom",type:"daterange"},{label:"录入日期",field:"fromTime",type:"daterange",format:"YYYY-MM-DD"}],Y=[{label:"设备编码",field:"code",prop:"code",type:"input",rules:[{required:!0,message:"请填写设备编码"}],placeholder:"请填写设备编码"},{label:"设备名称",field:"name",prop:"name",type:"input",rules:[{required:!0,message:"请填写设备名称"}],placeholder:"请填写设备名称"},{label:"设备简称",field:"nickname",prop:"nickname",type:"input",rules:[{required:!0,message:"请填写设备简称"}],placeholder:"请填写设备简称"},{label:"厂家名称",field:"companyName",prop:"companyName",type:"input",rules:[{required:!0,message:"请填写厂家名称"}],placeholder:"请填写厂家名称"},{label:"设备型号",field:"model",prop:"model",type:"input",rules:[{required:!0,message:"请填写设备型号"}],placeholder:"请填写设备型号"},{label:"安装人",field:"installUserName",prop:"installUserName",type:"input",rules:[{required:!0,message:"请填写安装人"}],placeholder:"请填写安装人"},{label:"安装日期",field:"installDate",prop:"installDate",type:"date",rules:[{required:!0,message:"请选择安装日期"}]},{label:"性能参数 ",field:"performanceParameters",prop:"performanceParameters",type:"textarea",rules:[{required:!0,message:"请填写性能参数"}],placeholder:"请填写性能参数"},{label:"备注",field:"remark",prop:"remark",type:"textarea",placeholder:"请填写备注"}],ae={设备编码:"code",设备名称:"name",设备简称:"nickname",泵个数:"pumpNum",厂家名称:"companyName",设备型号:"model",安装人:"installUserName",安装日期:"installDate",性能参数:"performanceParameters",备注:"remark"},Ze=y=>new Promise(D=>{const I=new FileReader;I.onload=U=>{const h=new Uint8Array(U.target.result),C=te.read(h,{type:"array"}),L=C.Sheets[C.SheetNames[0]],x=te.utils.sheet_to_json(L);D(x)},I.readAsArrayBuffer(y.raw)}),et=()=>{let y=Y;return y=y.concat(Y),y.push({type:"textarea",label:"备注",field:"remark",prop:"remark",placeholder:"请输入备注"}),y},tt={class:"wrapper"},at={class:"buttons"},ot={class:"btns"},lt=ye({__name:"EquipmentLedger",setup(y){const{$messageSuccess:D,$messageError:I}=_e(),U=v(),h=v(),C=v(),L=v(),x=v(),$=v(),E=v(),W=v(),o=f({pumpRooms:[],fileType:"",pumpRoomId:"",fileActionUrl:he().actionUrl+"/file/api/upload/file",dataList:[],rowId:""}),q=f({pumpRoomId:""}),oe=f({pumpRoomId:[{required:!0,message:"请选择泵房",trigger:"blur"}]}),le=f({filters:[{type:"input",label:"设备编码",field:"code",placeholder:"请输入设备编码"},...Qe],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",svgIcon:u(Ne),click:()=>F()},{perm:!0,text:"重置",type:"default",svgIcon:u(Z),click:()=>{var t;(t=x.value)==null||t.resetForm()}},{perm:!0,text:"添加",type:"success",svgIcon:u(Se),click:()=>{T.title="新增",X()}},{perm:!0,text:"导入",type:"warning",svgIcon:u(Q),click:()=>{var t;A(),(t=L.value)==null||t.openDialog()}},{perm:!0,text:"导出",type:"primary",svgIcon:u(z),click:()=>se()}]}]}),re=f({title:"导入",dialogWidth:1200,group:[],cancel:!0,btns:[{perm:!0,text:"确定导入",click:async()=>{var t;await((t=U.value)==null?void 0:t.validate(async e=>{e&&(o.dataList.length>0?(console.log(o.dataList),o.dataList=o.dataList.map(a=>a={...a,pumpRoomId:q.pumpRoomId}),Be(o.dataList).then(a=>{var n,l;((n=a.data)==null?void 0:n.code)===200?(A(),(l=L.value)==null||l.closeDialog(),w.success("提交成功"),F()):w.error("提交失败")}).catch(a=>{console.log(a),w.error("提交失败")})):w.warning("请导入正确的xlsx文件！"))}))}}]}),ne=t=>{o.dataList=[],Ze(t).then(e=>{e&&e.forEach(l=>{const c={};for(const i in l){if(typeof ae[i]>"u"){w.info("设备编码/设备名称/设备简称/泵个数/厂家名称/设备型号/安装人/安装日期/性能参数/备注; 且每行均有对应数据!");return}c[ae[i]]=l[i]}o.dataList.push(c)});const a=o.dataList.map(l=>JSON.stringify(l)),n=[...new Set(a)];O.dataList=n.map(l=>JSON.parse(l))})},se=async()=>{var g;const t=((g=x.value)==null?void 0:g.queryParams)||{},[e,a]=t.installDateFrom,[n,l]=t.fromTime,c={...t,page:b.pagination.page||1,size:-1,installDateFrom:e?k(e).startOf("day").valueOf():null,installDateTo:a?k(a).startOf("day").valueOf():null,fromTime:n,toTime:l},i=await ze(c),m=window.URL.createObjectURL(i.data);console.log(m);const s=document.createElement("a");s.style.display="none",s.href=m,s.setAttribute("download","机房信息列表.xlsx"),document.body.appendChild(s),s.click()},ie=async()=>{const t=await Ke(),e=window.URL.createObjectURL(t.data);console.log(e);const a=document.createElement("a");a.style.display="none",a.href=e,a.setAttribute("download","机房信息模板.xlsx"),document.body.appendChild(a),a.click(),window.URL.revokeObjectURL(a.href)},O=f({indexVisible:!0,columns:Y.slice(1),dataList:[],pagination:{hide:!0}}),pe=async(t,e)=>{const a={fileName:e.name,fileAddress:e.response,label:o.fileType,host:o.rowId};je(a).then(()=>{R(o.fileType,o.rowId)})},ce=f({defaultParams:{time:[]},filters:[{type:"input",label:"文件名",field:"fileName",placeholder:"请输入文件名"},{type:"daterange",label:"上传日期",field:"fromTime",format:"YYYY-MM-DD"},{type:"btn-group",btns:[{perm:!0,text:"查询",click:()=>R(o.fileType,o.rowId)},{perm:!0,text:"重置",type:"default",svgIcon:u(Z),click:()=>{var t;(t=E.value)==null||t.resetForm()}}]}]}),b=f({indexVisible:!0,columns:Ge.concat([{minWidth:120,label:"施工图纸",prop:"constructionFile",formItemConfig:{type:"btn-group",btns:[{perm:!0,text:"查看",type:"default",svgIcon:u(J),click:t=>{var e;console.log(t.id),o.fileType="constructionFile",(e=W.value)==null||e.openDialog(),R(o.fileType,t.id)}}]}},{minWidth:120,label:"其他文件",prop:"otherFile",formItemConfig:{type:"btn-group",btns:[{perm:!0,text:"查看",type:"default",svgIcon:u(J),click:t=>{var e;o.fileType="otherFile",(e=W.value)==null||e.openDialog(),R(o.fileType,t.id)}}]}}]),operations:[{perm:!0,text:"修改",svgIcon:u(J),click:t=>{T.title="修改",X(t)}},{perm:!0,text:"删除",type:"danger",svgIcon:u(ee),click:t=>me(t)}],dataList:[],pagination:{page:1,limit:20,total:0,refreshData:({page:t,size:e})=>{b.pagination.page=t,b.pagination.limit=e,F()}}}),de=f({dialogWidth:1050,title:"上传文件",labelWidth:120,group:[{fields:et()}]}),T=f({dialogWidth:600,title:"新增",labelWidth:120,group:[{fields:Y}],submit:t=>{t.pumpRoomId="",V("确定提交？","提示信息").then(()=>{T.title==="新增"?Je(t).then(()=>{var e,a,n;(a=(e=h.value)==null?void 0:e.refForm)==null||a.resetForm(),(n=h.value)==null||n.closeDialog(),w.success("提交成功"),F()}):$e(t).then(()=>{var e,a,n;(a=(e=h.value)==null?void 0:e.refForm)==null||a.resetForm(),(n=h.value)==null||n.closeDialog(),w.success("提交成功"),F()})}).catch(()=>{})}}),X=t=>{var e;T.defaultValue=t?{...t,installDate:k(t.installDate).format()}:{},(e=h.value)==null||e.openDialog()},me=t=>{V("确定删除?","提示信息").then(()=>{Xe(t.id).then(()=>{F()})}).catch(()=>{})},H=f({indexVisible:!0,columns:[{label:"文件",prop:"fileName",minWidth:120},{label:"上传时间",prop:"uploadTime",minWidth:120,formatter:(t,e)=>e?k(e).format("YYYY-MM-DD HH:mm:ss"):""}],dataList:[],operations:[{perm:!0,text:"下载",type:"primary",svgIcon:u(z),click:t=>{be(t.fileAddress,t.fileName)}},{perm:!0,text:"删除",type:"danger",svgIcon:u(ee),click:t=>{V("确定删除该附件, 是否继续?","删除提示").then(()=>{Ae(t.id).then(e=>{var a;((a=e.data)==null?void 0:a.code)===200?D("删除成功"):I("删除失败"),R(o.fileType,o.rowId)}).catch(e=>{I(e)})})}}],pagination:{hide:!0}}),F=async()=>{var m,s,g;const t=((m=x.value)==null?void 0:m.queryParams)||{},[e,a]=t.installDateFrom,[n,l]=t.fromTime,c={...t,page:b.pagination.page||1,size:b.pagination.limit||20,installDateFrom:e?k(e).startOf("day").valueOf():null,installDateTo:a?k(a).startOf("day").valueOf():null,fromTime:n,toTime:l},i=await He(c);console.log(i.data.data.total),b.pagination.total=(s=i.data)==null?void 0:s.data.total,b.dataList=(g=i.data)==null?void 0:g.data.data},R=async(t,e)=>{var m,s,g,M,N;const a=((m=E.value)==null?void 0:m.queryParams)||{fromTime:[]},[n,l]=a.fromTime||[],c={...a,host:e,label:t,fromTime:n,toTime:l,page:1,size:99999};o.rowId=e;const i=await Pe(c);console.log((g=(s=i.data)==null?void 0:s.data)==null?void 0:g.data),H.dataList=(N=(M=i.data)==null?void 0:M.data)==null?void 0:N.data},A=()=>{o.dataList=[],O.dataList=[]};return ve(async()=>{var l,c,i,m;const t=await Ve({size:999,page:1}),e=(c=(l=t==null?void 0:t.data)==null?void 0:l.data)==null?void 0:c.data;o.pumpRooms=e;const a=e.map(s=>({label:s.name,value:s.id})),n=(m=(i=T.group[0])==null?void 0:i.fields)==null?void 0:m.find(s=>s.field==="pumpRoomId");n.options=a,F()}),(t,e)=>{const a=Oe,n=We,l=Ee,c=xe,i=Te,m=Ie,s=Le,g=Re,M=Ue,N=Ce,ue=qe,K=Me,fe=ge;return j(),G("div",tt,[r(a,{ref_key:"refSearch",ref:x,config:p(le)},null,8,["config"]),r(n,{ref_key:"refTable",ref:C,config:p(b),class:"card-table"},null,8,["config"]),r(l,{ref_key:"refForm",ref:h,config:p(T)},null,8,["config"]),r(l,{ref_key:"refUploadDialog",ref:L,config:p(re)},{default:d(()=>[r(ue,{ref_key:"ruleFormRef",ref:U,rules:p(oe),model:p(q)},{default:d(()=>[r(N,null,{default:d(()=>[r(s,{span:12},{default:d(()=>[r(m,null,{default:d(()=>[B("div",at,[r(c,{type:"primary",icon:p(z),plain:!0,onClick:ie},{default:d(()=>e[2]||(e[2]=[S(" 下载模板 ")])),_:1},8,["icon"]),r(i,{ref:"upload",action:"action",accept:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","show-file-list":!0,"auto-upload":!1,"on-remove":A,limit:1,"on-change":ne},{tip:d(()=>e[4]||(e[4]=[B("div",{class:"el-upload__tip"}," 只能导入xlsx文件, 请确保导入的文件单元格格式为文本! ",-1)])),default:d(()=>[r(c,{type:"primary",icon:p(Q)},{default:d(()=>e[3]||(e[3]=[S(" 添加文件 ")])),_:1},8,["icon"])]),_:1},512)])]),_:1})]),_:1}),r(s,{span:12},{default:d(()=>[r(m,{label:"泵房",required:"",prop:"pumpRoomId"},{default:d(()=>[r(M,{modelValue:p(q).pumpRoomId,"onUpdate:modelValue":e[0]||(e[0]=_=>p(q).pumpRoomId=_),placeholder:"请选择泵房"},{default:d(()=>[(j(!0),G(ke,null,De(p(o).pumpRooms,(_,P)=>(j(),Fe(g,{key:P,label:_.name,value:_.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["rules","model"]),r(K,{config:p(O)},null,8,["config"])]),_:1},8,["config"]),r(l,{ref_key:"refFileUpload",ref:W,config:p(de)},{default:d(()=>[r(fe,{ref_key:"uploadSearch",ref:E,config:p(ce)},null,8,["config"]),B("div",ot,[r(i,{ref_key:"uploadRef",ref:$,action:p(o).fileActionUrl,"show-file-list":!0,accept:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","auto-upload":!1,multiple:"",headers:{"X-Authorization":"Bearer "+p(we)().token},"on-success":(_,P)=>pe(_,P),class:"upload-demo"},{trigger:d(()=>[r(c,{type:"primary"},{default:d(()=>e[5]||(e[5]=[S(" 读取文件 ")])),_:1})]),_:1},8,["action","headers","on-success"]),r(c,{type:"success",onClick:e[1]||(e[1]=()=>{var _;return(_=p($))==null?void 0:_.submit()})},{default:d(()=>e[6]||(e[6]=[S(" 上传 ")])),_:1})]),r(K,{config:p(H),class:"form-table"},null,8,["config"])]),_:1},8,["config"])])}}}),ft=Ye(lt,[["__scopeId","data-v-69a596c0"]]);export{ft as default};
