######################################################################
# Build Tools

.gradle
/build/
!gradle/wrapper/gradle-wrapper.jar

target/
!.mvn/wrapper/maven-wrapper.jar

######################################################################
# IDE

### STS ###
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
**/.history/**
.history/

### IntelliJ IDEA ###
.idea
*.iws
*.iml
*.ipr

### JRebel ###
rebel.xml

### NetBeans ###
nbproject/private/
build/*
nbbuild/
dist/
nbdist/
.nb-gradle/
*.zip

######################################################################
# Others
*.log
*.xml.versionsBackup
*.swp

!*/build/*.java
!*/build/*.html
!*/build/*.xml

# Maven
**/target/
target/
*.jar
*.war
*.ear
*.class

# IDE - IntelliJ IDEA
.idea/
*.iml
*.iws
*.ipr
.idea_modules/

# IDE - Eclipse
.classpath
.project
.settings/
.metadata/
bin/

# IDE - VS Code
.vscode/

# Logs
logs/
*.log
log/

# OS specific
.DS_Store
Thumbs.db

# Temporary files
*.tmp
*.bak
*.swp
*~.nib

# Spring Boot
.springBeans

# Others
*.zip
*.tar.gz
*.rar
.env
