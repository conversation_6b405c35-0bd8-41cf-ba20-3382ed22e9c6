<template>
  <el-row :gutter="20">
    <el-col :span="6">
      <el-card class="box-card">
        <descriptions
          v-if="show === 1 || show === 3"
          ref="refForm"
          :config="props.basic"
        ></descriptions>
        <descriptions
          v-else-if="show === 2 || show === 14 || show === 15 || show === 16"
          :key="key"
          ref="refForm"
          :config="allConfig"
        ></descriptions>
        <descriptions
          v-else
          ref="refForm"
          :key="key"
          :config="basicConfig"
        ></descriptions>
      </el-card>
    </el-col>
    <el-col :span="18">
      <!-- 项目完成信息 -->
      <xmwcqk v-if="show === 1" :id="props.config.code || ''" />
      <!-- 项目工程 -->
      <xmgc v-if="show === 1" :id="props.config.code || ''" />
      <!-- 招投标流程详情 -->
      <cytbgs
        v-if="show === 2"
        :id="props.config.code || ''"
        :config="props.config || {}"
      />
      <!-- 工程完成情况 -->
      <gcwcqk v-if="show === 3" :id="props.config.code || ''" />
      <!-- 所属项目完成情况 -->
      <ssxmwcqk v-if="show === 3" :id="props.config.projectCode || ''" />
      <!-- 工程设计基础信息 -->
      <gcsjjcxx v-if="show === 4" :config="props.config || {}" />
      <!-- 设计变更 -->
      <sjbg
        v-if="show === 4"
        :id="props.config.projectCode || ''"
        :config="props.config || {}"
      />
      <!-- 工程预算基础信息 -->
      <gcysjcxx
        v-if="show === 5"
        :id="props.config.projectCode || ''"
        :config="props.config || {}"
      />
      <!-- 工程预算基础信息 -->
      <qzjbxx
        v-if="show === 6"
        :id="props.config.projectCode || ''"
        :config="props.config || {}"
      />
      <!-- 合同基本信息 -->
      <htjbxx
        v-if="show === 7"
        :id="props.config.projectCode || ''"
        :config="props.config || {}"
      />
      <!-- 合同变更 -->
      <htbg
        v-if="show === 7"
        :id="props.config.projectCode || ''"
        :config="props.config || {}"
      />
      <!-- 费用管理 -->
      <fygl
        v-if="show === 8"
        :id="props.config.projectCode || ''"
        :config="props.config || {}"
      />
      <!-- 设计变更 -->
      <!-- <gcssxq
        v-if="show===9"
        :id="props.config.projectCode||''"
        :config="props.config||{}"
      /> -->
      <!-- 实施详情 -->
      <ssxq
        v-if="show === 10"
        :id="props.config.projectCode || ''"
        :config="props.config || {}"
      />
      <!-- 验收期工程基础信息 -->
      <ysqgcjcxx
        v-if="show === 11"
        :id="props.config.projectCode || ''"
        :config="props.config || {}"
      />
      <!-- 所属工程结算基础信息 -->
      <ssgcjsjcxx
        v-if="show === 12"
        :id="props.config.projectCode || ''"
        :config="props.config || {}"
      />
      <!-- 所属归档基础信息 -->
      <ssgdjcxx
        v-if="show === 13"
        :id="props.config.projectCode || ''"
        :config="props.config || {}"
      />
      <!-- 项目总验收基础信息 -->
      <xmzysjcxx
        v-if="show === 14"
        :id="props.config.projectCode || ''"
        :config="props.config || {}"
      />
      <!-- 项目总结算基础信息 -->
      <xmzjsjcxx
        v-if="show === 15"
        :id="props.config.projectCode || ''"
        :config="props.config || {}"
      />
      <!-- 项目总归档基础信息 -->
      <xmzgdjcxx
        v-if="show === 16"
        :id="props.config.projectCode || ''"
        :config="props.config || {}"
      />
    </el-col>
  </el-row>
</template>

<script lang="ts" setup>
import {
  getConstruction,
  getProjectList
} from '@/api/engineeringManagement/projectManagement';
import { GenNonDuplicateID } from '@/utils/GlobalHelper';
import xmwcqk from './xmwcqk.vue';
import xmgc from './xmgc.vue';
import cytbgs from './cytbgs.vue';
import gcwcqk from './gcwcqk.vue';
import ssxmwcqk from './ssxmwcqk.vue';
import gcsjjcxx from './gcsjjcxx.vue';
import sjbg from './sjbg.vue';
import gcysjcxx from './gcysjcxx.vue';
import qzjbxx from './qzjbxx.vue';
import htjbxx from './htjbxx.vue';
import htbg from './htbg.vue';
import fygl from './fygl.vue';
import ssxq from './ssxq.vue';
// import gcssxq from './gcssxq.vue'
import ysqgcjcxx from './ysqgcjcxx.vue';
import ssgcjsjcxx from './ssgcjsjcxx.vue';
import ssgdjcxx from './ssgdjcxx.vue';
import xmzysjcxx from './xmzysjcxx.vue';
import xmzjsjcxx from './xmzjsjcxx.vue';
import xmzgdjcxx from './xmzgdjcxx.vue';

const props = defineProps<{ config: any; basic?: any; show: number }>();
const refForm = ref<IDescriptionsIns>();
const key = ref(GenNonDuplicateID());

const basicConfig = reactive<IDescriptionsConfig>({
  defaultValue: {},
  border: false,
  direction: 'horizontal',
  column: 1,
  title: '项目基础信息',
  fields: [
    { type: 'text', label: '项目编号:', field: 'project.code' },
    { type: 'text', label: '项目名称:', field: 'project.name' },
    { type: 'text', label: '启动时间:', field: 'project.startTimeName' },
    {
      type: 'text',
      label: '预计结束时间:',
      field: 'project.expectEndTimeName'
    },
    { type: 'text', label: '项目负责人:', field: 'project.principal' },
    { type: 'divider', text: '' },
    { type: 'text', label: '工程编号:', field: 'construction.code' },
    { type: 'text', label: '工程名称:', field: 'construction.name' },
    { type: 'text', label: '工程类别:', field: 'construction.typeName' },
    { type: 'text', label: '工程预算(万元):', field: 'construction.estimate' }
  ]
});

const allConfig = reactive<IDescriptionsConfig>({
  defaultValue: {},
  border: false,
  direction: 'horizontal',
  column: 1,
  title: '项目基础信息',
  fields: [
    { type: 'text', label: '项目编号:', field: 'code' },
    { type: 'text', label: '项目名称:', field: 'name' },
    { type: 'text', label: '项目负责人:', field: 'principal' },
    { type: 'text', label: '联系电话:', field: 'phone' },
    { type: 'text', label: '项目类别:', field: 'typeName' },
    { type: 'text', label: '项目规模:', field: 'scale' },
    { type: 'text', label: '项目启动时间:', field: 'startTimeName' },
    { type: 'text', label: '项目概算(万元):', field: 'estimate' }
  ]
});

const refreshData = async () => {
  const constructionCode = props.config.constructionCode;
  if (
    props.show !== 2 &&
    props.show !== 14 &&
    props.show !== 15 &&
    props.show !== 16
  ) {
    getConstruction(constructionCode).then((res) => {
      basicConfig.defaultValue = { ...res.data.data };
      key.value = GenNonDuplicateID();
    });
  } else {
    const params = {
      page: -1,
      size: 20,
      code: props.config.code
    };
    getProjectList(params).then((res) => {
      allConfig.defaultValue = { ...(res.data.data.data[0] || {}) };
      key.value = GenNonDuplicateID();
    });
  }
};

onMounted(() => {
  if (props.show !== 1) {
    refreshData();
  }
});
</script>

<style lang="scss" scoped>
.box-card {
  min-height: 770px;
}
</style>
