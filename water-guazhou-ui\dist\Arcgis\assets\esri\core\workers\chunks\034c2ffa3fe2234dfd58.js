"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[4729],{22303:(t,e,o)=>{o.d(e,{Z:()=>u});var r,i,s=o(35270),n=o(22021),a=o(70586),l=o(75215);function p(t){return(0,n.uZ)((0,l.vU)(t),0,255)}function d(t,e,o){return t=Number(t),isNaN(t)?o:t<e?e:t>o?o:t}class h{static blendColors(t,e,o,r=new h){return r.r=Math.round(t.r+(e.r-t.r)*o),r.g=Math.round(t.g+(e.g-t.g)*o),r.b=Math.round(t.b+(e.b-t.b)*o),r.a=t.a+(e.a-t.a)*o,r._sanitize()}static fromRgb(t,e){const o=t.toLowerCase().match(/^(rgba?|hsla?)\(([\s\.\-,%0-9]+)\)/);if(o){const t=o[2].split(/\s*,\s*/),r=o[1];if("rgb"===r&&3===t.length||"rgba"===r&&4===t.length){const o=t[0];if("%"===o.charAt(o.length-1)){const o=t.map((t=>2.56*parseFloat(t)));return 4===t.length&&(o[3]=parseFloat(t[3])),h.fromArray(o,e)}return h.fromArray(t.map((t=>parseFloat(t))),e)}if("hsl"===r&&3===t.length||"hsla"===r&&4===t.length)return h.fromArray((0,s.B7)(parseFloat(t[0]),parseFloat(t[1])/100,parseFloat(t[2])/100,parseFloat(t[3])),e)}return null}static fromHex(t,e=new h){if(4!==t.length&&7!==t.length||"#"!==t[0])return null;const o=4===t.length?4:8,r=(1<<o)-1;let i=Number("0x"+t.substr(1));return isNaN(i)?null:(["b","g","r"].forEach((t=>{const s=i&r;i>>=o,e[t]=4===o?17*s:s})),e.a=1,e)}static fromArray(t,e=new h){return e._set(Number(t[0]),Number(t[1]),Number(t[2]),Number(t[3])),isNaN(e.a)&&(e.a=1),e._sanitize()}static fromString(t,e){const o=(0,s.St)(t)?(0,s.h$)(t):null;return o&&h.fromArray(o,e)||h.fromRgb(t,e)||h.fromHex(t,e)}static fromJSON(t){return t&&new h([t[0],t[1],t[2],t[3]/255])}static toUnitRGB(t){return(0,a.pC)(t)?[t.r/255,t.g/255,t.b/255]:null}static toUnitRGBA(t){return(0,a.pC)(t)?[t.r/255,t.g/255,t.b/255,null!=t.a?t.a:1]:null}constructor(t){this.r=255,this.g=255,this.b=255,this.a=1,t&&this.setColor(t)}get isBright(){return.299*this.r+.587*this.g+.114*this.b>=127}setColor(t){return"string"==typeof t?h.fromString(t,this):Array.isArray(t)?h.fromArray(t,this):(this._set(t.r??0,t.g??0,t.b??0,t.a??1),t instanceof h||this._sanitize()),this}toRgb(){return[this.r,this.g,this.b]}toRgba(){return[this.r,this.g,this.b,this.a]}toHex(){const t=this.r.toString(16),e=this.g.toString(16),o=this.b.toString(16);return`#${t.length<2?"0"+t:t}${e.length<2?"0"+e:e}${o.length<2?"0"+o:o}`}toCss(t=!1){const e=this.r+", "+this.g+", "+this.b;return t?`rgba(${e}, ${this.a})`:`rgb(${e})`}toString(){return this.toCss(!0)}toJSON(){return this.toArray()}toArray(t=h.AlphaMode.ALWAYS){const e=p(this.r),o=p(this.g),r=p(this.b);return t===h.AlphaMode.ALWAYS||1!==this.a?[e,o,r,p(255*this.a)]:[e,o,r]}clone(){return new h(this.toRgba())}hash(){return this.r<<24|this.g<<16|this.b<<8|255*this.a}equals(t){return(0,a.pC)(t)&&t.r===this.r&&t.g===this.g&&t.b===this.b&&t.a===this.a}_sanitize(){return this.r=Math.round(d(this.r,0,255)),this.g=Math.round(d(this.g,0,255)),this.b=Math.round(d(this.b,0,255)),this.a=d(this.a,0,1),this}_set(t,e,o,r){this.r=t,this.g=e,this.b=o,this.a=r}}h.prototype.declaredClass="esri.Color",(i=(r=h||(h={})).AlphaMode||(r.AlphaMode={}))[i.ALWAYS=0]="ALWAYS",i[i.UNLESS_OPAQUE=1]="UNLESS_OPAQUE";const u=h},51773:(t,e,o)=>{o.d(e,{Z:()=>tt});var r=o(43697),i=o(2368),s=o(46791),n=o(96674),a=o(22974),l=o(92604),p=o(95330),d=o(5600),h=o(90578),u=o(71715),c=o(52011),y=o(30556),m=o(75215),g=o(35671),f=o(84649),v=o(63801),b=o(48074),_=o(38745),w=o(9190),C=o(10214),I=o(71423),x=o(44951);const S={base:null,key:"type",typeMap:{attachment:f.Z,media:C.Z,text:x.Z,expression:_.Z,field:w.Z,relationship:I.Z}};var F,j=o(11223),T=o(422);o(67676);let Z=F=class extends n.wq{constructor(t){super(t),this.returnTopmostRaster=null,this.showNoDataRecords=null}clone(){return new F({showNoDataRecords:this.showNoDataRecords,returnTopmostRaster:this.returnTopmostRaster})}};(0,r._)([(0,d.Cb)({type:Boolean,json:{write:!0}})],Z.prototype,"returnTopmostRaster",void 0),(0,r._)([(0,d.Cb)({type:Boolean,json:{write:!0}})],Z.prototype,"showNoDataRecords",void 0),Z=F=(0,r._)([(0,c.j)("esri.popup.LayerOptions")],Z);const N=Z;var A,M=o(44729);let O=A=class extends n.wq{constructor(t){super(t),this.showRelatedRecords=null,this.orderByFields=null}clone(){return new A({showRelatedRecords:this.showRelatedRecords,orderByFields:this.orderByFields?(0,a.d9)(this.orderByFields):null})}};(0,r._)([(0,d.Cb)({type:Boolean,json:{write:!0}})],O.prototype,"showRelatedRecords",void 0),(0,r._)([(0,d.Cb)({type:[M.Z],json:{write:!0}})],O.prototype,"orderByFields",void 0),O=A=(0,r._)([(0,c.j)("esri.popup.RelatedRecordsInfo")],O);const E=O;var L,R=o(79742),D=o(15923),P=o(10699);let U=L=class extends((0,P.IG)(D.Z)){constructor(t){super(t),this.active=!1,this.className=null,this.disabled=!1,this.id=null,this.indicator=!1,this.title=null,this.type=null,this.visible=!0}clone(){return new L({active:this.active,className:this.className,disabled:this.disabled,id:this.id,indicator:this.indicator,title:this.title,visible:this.visible})}};(0,r._)([(0,d.Cb)()],U.prototype,"active",void 0),(0,r._)([(0,d.Cb)()],U.prototype,"className",void 0),(0,r._)([(0,d.Cb)()],U.prototype,"disabled",void 0),(0,r._)([(0,d.Cb)()],U.prototype,"id",void 0),(0,r._)([(0,d.Cb)()],U.prototype,"indicator",void 0),(0,r._)([(0,d.Cb)()],U.prototype,"title",void 0),(0,r._)([(0,d.Cb)()],U.prototype,"type",void 0),(0,r._)([(0,d.Cb)()],U.prototype,"visible",void 0),U=L=(0,r._)([(0,c.j)("esri.support.actions.ActionBase")],U);const B=U;var J;let Y=J=class extends B{constructor(t){super(t),this.image=null,this.type="button"}clone(){return new J({active:this.active,className:this.className,disabled:this.disabled,id:this.id,indicator:this.indicator,title:this.title,visible:this.visible,image:this.image})}};(0,r._)([(0,d.Cb)()],Y.prototype,"image",void 0),Y=J=(0,r._)([(0,c.j)("esri.support.Action.ActionButton")],Y);const k=Y;var $;let q=$=class extends B{constructor(t){super(t),this.image=null,this.type="toggle",this.value=!1}clone(){return new $({active:this.active,className:this.className,disabled:this.disabled,id:this.id,indicator:this.indicator,title:this.title,visible:this.visible,image:this.image,value:this.value})}};(0,r._)([(0,d.Cb)()],q.prototype,"image",void 0),(0,r._)([(0,d.Cb)()],q.prototype,"value",void 0),q=$=(0,r._)([(0,c.j)("esri.support.Action.ActionToggle")],q);const H=q,z=l.Z.getLogger("esri.PopupTemplate"),W="relationships/",V="expression/",G=s.Z.ofType({key:"type",defaultKeyValue:"button",base:B,typeMap:{button:k,toggle:H}}),K={base:v.Z,key:"type",typeMap:{media:C.Z,custom:b.Z,text:x.Z,attachments:f.Z,fields:w.Z,expression:_.Z,relationship:I.Z}},Q=["attachments","fields","media","text","expression","relationship"];let X=class extends((0,i.J)(n.wq)){constructor(){super(...arguments),this.actions=null,this.content="",this.expressionInfos=null,this.fieldInfos=null,this.layerOptions=null,this.lastEditInfoEnabled=!0,this.outFields=null,this.overwriteActions=!1,this.returnGeometry=!1,this.title=""}castContent(t){return Array.isArray(t)?t.map((t=>(0,m.N7)(K,t))):"string"==typeof t||"function"==typeof t||t instanceof HTMLElement||(0,p.y8)(t)?t:(z.error("content error","unsupported content value",{value:t}),null)}readContent(t,e){const{popupElements:o}=e;return Array.isArray(o)&&o.length>0?this._readPopupInfoElements(e.description,e.mediaInfos,o):this._readPopupInfo(e)}writeContent(t,e,o,r){"string"!=typeof t?Array.isArray(t)&&(e.popupElements=t.filter((t=>Q.includes(t.type))).map((t=>t&&t.toJSON(r))),e.popupElements.forEach((t=>{"attachments"===t.type?this._writeAttachmentContent(e):"media"===t.type?this._writeMediaContent(t,e):"text"===t.type?this._writeTextContent(t,e):"relationship"===t.type&&this._writeRelationshipContent(t,e)}))):e.description=t}writeFieldInfos(t,e,o,r){const{content:i}=this,s=Array.isArray(i)?i:null;if(t){const o=s?s.filter((t=>"fields"===t.type)):[],i=o.length&&o.every((t=>t.fieldInfos?.length));e.fieldInfos=t.filter(Boolean).map((t=>{const e=t.toJSON(r);return i&&(e.visible=!1),e}))}if(s)for(const t of s)"fields"===t.type&&this._writeFieldsContent(t,e)}writeLayerOptions(t,e,o,r){e[o]=!t||null===t.showNoDataRecords&&null===t.returnTopmostRaster?null:t.toJSON(r)}writeTitle(t,e){e.title=t||""}async collectRequiredFields(t,e){const o=this.expressionInfos||[];await this._collectExpressionInfoFields(t,e,[...o,...this._getContentExpressionInfos(this.content,o)]),(0,g.gd)(t,e,[...this.outFields||[],...this._getActionsFields(this.actions),...this._getTitleFields(this.title),...this._getContentFields(this.content)])}async getRequiredFields(t){const e=new Set;return await this.collectRequiredFields(e,t),[...e].sort()}_writeFieldsContent(t,e){if(!Array.isArray(t.fieldInfos)||!t.fieldInfos.length)return;const o=(0,a.d9)(t.fieldInfos);Array.isArray(e.fieldInfos)?o.forEach((t=>{const o=e.fieldInfos.find((e=>e.fieldName.toLowerCase()===t.fieldName.toLowerCase()));o?o.visible=!0:e.fieldInfos.push(t)})):e.fieldInfos=o}_writeAttachmentContent(t){t.showAttachments||(t.showAttachments=!0)}_writeRelationshipContent(t,e){const o=t.orderByFields?.map((e=>this._toFieldOrderJSON(e,t.relationshipId)))||[],r=[...e.relatedRecordsInfo?.orderByFields||[],...o];e.relatedRecordsInfo={showRelatedRecords:!0,...r?.length&&{orderByFields:r}}}_writeTextContent(t,e){!e.description&&t.text&&(e.description=t.text)}_writeMediaContent(t,e){if(!Array.isArray(t.mediaInfos)||!t.mediaInfos.length)return;const o=(0,a.d9)(t.mediaInfos);Array.isArray(e.mediaInfos)?e.mediaInfos=[...e.mediaInfos,...o]:e.mediaInfos=o}_readPopupInfoElements(t,e,o){const r={description:!1,mediaInfos:!1};return o.map((o=>"media"===o.type?(o.mediaInfos||!e||r.mediaInfos||(o.mediaInfos=e,r.mediaInfos=!0),C.Z.fromJSON(o)):"text"===o.type?(o.text||!t||r.description||(o.text=t,r.description=!0),x.Z.fromJSON(o)):"attachments"===o.type?f.Z.fromJSON(o):"fields"===o.type?w.Z.fromJSON(o):"expression"===o.type?_.Z.fromJSON(o):"relationship"===o.type?I.Z.fromJSON(o):void 0)).filter(Boolean)}_toRelationshipContent(t){const{field:e,order:o}=t;if(!e?.startsWith(W))return null;const r=e.replace(W,"").split("/");if(2!==r.length)return null;const i=parseInt(r[0],10),s=r[1];return"number"==typeof i&&s?I.Z.fromJSON({relationshipId:i,orderByFields:[{field:s,order:o}]}):null}_toFieldOrderJSON(t,e){const{order:o,field:r}=t;return{field:`${W}${e}/${r}`,order:o}}_readPopupInfo({description:t,mediaInfos:e,showAttachments:o,relatedRecordsInfo:r={showRelatedRecords:!1}}){const i=[];t?i.push(new x.Z({text:t})):i.push(new w.Z),Array.isArray(e)&&e.length&&i.push(C.Z.fromJSON({mediaInfos:e})),o&&i.push(f.Z.fromJSON({displayType:"auto"}));const{showRelatedRecords:s,orderByFields:n}=r;return s&&n?.length&&n.forEach((t=>{const e=this._toRelationshipContent(t);e&&i.push(e)})),i.length?i:t}_getContentElementFields(t){const e=t?.type;if("attachments"===e)return[...this._extractFieldNames(t.title),...this._extractFieldNames(t.description)];if("custom"===e)return t.outFields||[];if("fields"===e)return[...this._extractFieldNames(t.title),...this._extractFieldNames(t.description),...this._getFieldInfoFields(t.fieldInfos??this.fieldInfos)];if("media"===e){const e=t.mediaInfos||[];return[...this._extractFieldNames(t.title),...this._extractFieldNames(t.description),...e.reduce(((t,e)=>[...t,...this._getMediaInfoFields(e)]),[])]}return"text"===e?this._extractFieldNames(t.text):[]}_getMediaInfoFields(t){const{caption:e,title:o,value:r}=t,i=r||{},{fields:s,normalizeField:n,tooltipField:a,sourceURL:l,linkURL:p}=i,d=[...this._extractFieldNames(o),...this._extractFieldNames(e),...this._extractFieldNames(l),...this._extractFieldNames(p),...s??[]];return n&&d.push(n),a&&d.push(a),d}_getContentExpressionInfos(t,e){return Array.isArray(t)?t.reduce(((t,e)=>[...t,..."expression"===e.type&&e.expressionInfo?[e.expressionInfo]:[]]),e):[]}_getContentFields(t){return"string"==typeof t?this._extractFieldNames(t):Array.isArray(t)?t.reduce(((t,e)=>[...t,...this._getContentElementFields(e)]),[]):[]}async _collectExpressionInfoFields(t,e,o){o&&await Promise.all(o.map((o=>(0,g.io)(t,e,o.expression))))}_getFieldInfoFields(t){return t?t.filter((t=>void 0===t.visible||!!t.visible)).map((t=>t.fieldName)).filter((t=>!t.startsWith(W)&&!t.startsWith(V))):[]}_getActionsFields(t){return t?t.toArray().reduce(((t,e)=>[...t,...this._getActionFields(e)]),[]):[]}_getActionFields(t){const{className:e,title:o,type:r}=t,i="button"===r||"toggle"===r?t.image:"";return[...this._extractFieldNames(o),...this._extractFieldNames(e),...this._extractFieldNames(i)]}_getTitleFields(t){return"string"==typeof t?this._extractFieldNames(t):[]}_extractFieldNames(t){if(!t||"string"!=typeof t)return[];const e=t.match(/{[^}]*}/g);if(!e)return[];const o=/\{(\w+):.+\}/,r=e.filter((t=>!(0===t.indexOf(`{${W}`)||0===t.indexOf(`{${V}`)))).map((t=>t.replace(o,"{$1}")));return r?r.map((t=>t.slice(1,-1))):[]}};(0,r._)([(0,d.Cb)({type:G})],X.prototype,"actions",void 0),(0,r._)([(0,d.Cb)()],X.prototype,"content",void 0),(0,r._)([(0,h.p)("content")],X.prototype,"castContent",null),(0,r._)([(0,u.r)("content",["description","fieldInfos","popupElements","mediaInfos","showAttachments","relatedRecordsInfo"])],X.prototype,"readContent",null),(0,r._)([(0,y.c)("content",{popupElements:{type:s.Z.ofType(S)},showAttachments:{type:Boolean},mediaInfos:{type:s.Z.ofType(R.V)},description:{type:String},relatedRecordsInfo:{type:E}})],X.prototype,"writeContent",null),(0,r._)([(0,d.Cb)({type:[j.Z],json:{write:!0}})],X.prototype,"expressionInfos",void 0),(0,r._)([(0,d.Cb)({type:[T.Z]})],X.prototype,"fieldInfos",void 0),(0,r._)([(0,y.c)("fieldInfos")],X.prototype,"writeFieldInfos",null),(0,r._)([(0,d.Cb)({type:N})],X.prototype,"layerOptions",void 0),(0,r._)([(0,y.c)("layerOptions")],X.prototype,"writeLayerOptions",null),(0,r._)([(0,d.Cb)({type:Boolean,json:{read:{source:"showLastEditInfo"},write:{target:"showLastEditInfo"},default:!0}})],X.prototype,"lastEditInfoEnabled",void 0),(0,r._)([(0,d.Cb)()],X.prototype,"outFields",void 0),(0,r._)([(0,d.Cb)()],X.prototype,"overwriteActions",void 0),(0,r._)([(0,d.Cb)()],X.prototype,"returnGeometry",void 0),(0,r._)([(0,d.Cb)({json:{type:String}})],X.prototype,"title",void 0),(0,r._)([(0,y.c)("title")],X.prototype,"writeTitle",null),X=(0,r._)([(0,c.j)("esri.PopupTemplate")],X);const tt=X},90344:(t,e,o)=>{o.d(e,{Ze:()=>g,p6:()=>f});var r=o(35454),i=o(70171);const s={year:"numeric",month:"numeric",day:"numeric"},n={year:"numeric",month:"long",day:"numeric"},a={year:"numeric",month:"short",day:"numeric"},l={year:"numeric",month:"long",weekday:"long",day:"numeric"},p={hour:"numeric",minute:"numeric"},d={...p,second:"numeric"},h={"short-date":s,"short-date-short-time":{...s,...p},"short-date-short-time-24":{...s,...p,hour12:!1},"short-date-long-time":{...s,...d},"short-date-long-time-24":{...s,...d,hour12:!1},"short-date-le":s,"short-date-le-short-time":{...s,...p},"short-date-le-short-time-24":{...s,...p,hour12:!1},"short-date-le-long-time":{...s,...d},"short-date-le-long-time-24":{...s,...d,hour12:!1},"long-month-day-year":n,"long-month-day-year-short-time":{...n,...p},"long-month-day-year-short-time-24":{...n,...p,hour12:!1},"long-month-day-year-long-time":{...n,...d},"long-month-day-year-long-time-24":{...n,...d,hour12:!1},"day-short-month-year":a,"day-short-month-year-short-time":{...a,...p},"day-short-month-year-short-time-24":{...a,...p,hour12:!1},"day-short-month-year-long-time":{...a,...d},"day-short-month-year-long-time-24":{...a,...d,hour12:!1},"long-date":l,"long-date-short-time":{...l,...p},"long-date-short-time-24":{...l,...p,hour12:!1},"long-date-long-time":{...l,...d},"long-date-long-time-24":{...l,...d,hour12:!1},"long-month-year":{month:"long",year:"numeric"},"short-month-year":{month:"short",year:"numeric"},year:{year:"numeric"},"short-time":p,"long-time":d},u=(0,r.w)()({shortDate:"short-date",shortDateShortTime:"short-date-short-time",shortDateShortTime24:"short-date-short-time-24",shortDateLongTime:"short-date-long-time",shortDateLongTime24:"short-date-long-time-24",shortDateLE:"short-date-le",shortDateLEShortTime:"short-date-le-short-time",shortDateLEShortTime24:"short-date-le-short-time-24",shortDateLELongTime:"short-date-le-long-time",shortDateLELongTime24:"short-date-le-long-time-24",longMonthDayYear:"long-month-day-year",longMonthDayYearShortTime:"long-month-day-year-short-time",longMonthDayYearShortTime24:"long-month-day-year-short-time-24",longMonthDayYearLongTime:"long-month-day-year-long-time",longMonthDayYearLongTime24:"long-month-day-year-long-time-24",dayShortMonthYear:"day-short-month-year",dayShortMonthYearShortTime:"day-short-month-year-short-time",dayShortMonthYearShortTime24:"day-short-month-year-short-time-24",dayShortMonthYearLongTime:"day-short-month-year-long-time",dayShortMonthYearLongTime24:"day-short-month-year-long-time-24",longDate:"long-date",longDateShortTime:"long-date-short-time",longDateShortTime24:"long-date-short-time-24",longDateLongTime:"long-date-long-time",longDateLongTime24:"long-date-long-time-24",longMonthYear:"long-month-year",shortMonthYear:"short-month-year",year:"year"}),c=(u.apiValues,u.toJSON.bind(u),u.fromJSON.bind(u),{ar:"ar-u-nu-latn-ca-gregory"});let y=new WeakMap,m=h["short-date-short-time"];function g(t){return t?h[t]:null}function f(t,e){return function(t){const e=t||m;let o=y.get(e);if(!o){const t=(0,i.Kd)(),r=c[(0,i.Kd)()]||t;o=new Intl.DateTimeFormat(r,e),y.set(e,o)}return o}(e).format(t)}(0,i.Ze)((()=>{y=new WeakMap,m=h["short-date-short-time"]}))},18848:(t,e,o)=>{o.d(e,{sh:()=>l,uf:()=>p});var r=o(70586),i=o(70171);const s={ar:"ar-u-nu-latn"};let n=new WeakMap,a={};function l(t={}){const e={};return null!=t.digitSeparator&&(e.useGrouping=t.digitSeparator),null!=t.places&&(e.minimumFractionDigits=e.maximumFractionDigits=t.places),e}function p(t,e){return-0===t&&(t=0),function(t){const e=t||a;if(!n.has(e)){const o=(0,i.Kd)(),r=s[(0,i.Kd)()]||o;n.set(e,new Intl.NumberFormat(r,t))}return(0,r.j0)(n.get(e))}(e).format(t)}(0,i.Ze)((()=>{n=new WeakMap,a={}}))},16859:(t,e,o)=>{o.d(e,{I:()=>C});var r=o(43697),i=o(68773),s=o(40330),n=o(3172),a=o(66643),l=o(20102),p=o(92604),d=o(70586),h=o(95330),u=o(17452),c=o(5600),y=(o(75215),o(67676),o(71715)),m=o(52011),g=o(30556),f=o(84230),v=o(65587),b=o(15235),_=o(86082),w=o(14661);const C=t=>{let e=class extends t{constructor(){super(...arguments),this.resourceReferences={portalItem:null,paths:[]},this.userHasEditingPrivileges=!0,this.userHasFullEditingPrivileges=!1,this.userHasUpdateItemPrivileges=!1}destroy(){this.portalItem=(0,d.SC)(this.portalItem)}set portalItem(t){t!==this._get("portalItem")&&(this.removeOrigin("portal-item"),this._set("portalItem",t))}readPortalItem(t,e,o){if(e.itemId)return new b.default({id:e.itemId,portal:o&&o.portal})}writePortalItem(t,e){t&&t.id&&(e.itemId=t.id)}async loadFromPortal(t,e){if(this.portalItem&&this.portalItem.id)try{const r=await o.e(8062).then(o.bind(o,18062));return(0,h.k_)(e),await r.load({instance:this,supportedTypes:t.supportedTypes,validateItem:t.validateItem,supportsData:t.supportsData,layerModuleTypeMap:t.layerModuleTypeMap},e)}catch(t){throw(0,h.D_)(t)||p.Z.getLogger(this.declaredClass).warn(`Failed to load layer (${this.title}, ${this.id}) portal item (${this.portalItem.id})\n  ${t}`),t}}async finishLoadEditablePortalLayer(t){this._set("userHasEditingPrivileges",await this._fetchUserHasEditingPrivileges(t).catch((t=>((0,h.r9)(t),!0))))}async _setUserPrivileges(t,e){if(!i.Z.userPrivilegesApplied)return this.finishLoadEditablePortalLayer(e);if(this.url)try{const{features:{edit:o,fullEdit:r},content:{updateItem:i}}=await this._fetchUserPrivileges(t,e);this._set("userHasEditingPrivileges",o),this._set("userHasFullEditingPrivileges",r),this._set("userHasUpdateItemPrivileges",i)}catch(t){(0,h.r9)(t)}}async _fetchUserPrivileges(t,e){let o=this.portalItem;if(!t||!o||!o.loaded||o.sourceUrl)return this._fetchFallbackUserPrivileges(e);const r=t===o.id;if(r&&o.portal.user)return(0,w.Ss)(o);let i,n;if(r)i=o.portal.url;else try{i=await(0,f.oP)(this.url,e)}catch(t){(0,h.r9)(t)}if(!i||!(0,u.Zo)(i,o.portal.url))return this._fetchFallbackUserPrivileges(e);try{const t=(0,d.pC)(e)?e.signal:null;n=await(s.id?.getCredential(`${i}/sharing`,{prompt:!1,signal:t}))}catch(t){(0,h.r9)(t)}if(!n)return{features:{edit:!0,fullEdit:!1},content:{updateItem:!1}};try{if(r?await o.reload():(o=new b.default({id:t,portal:{url:i}}),await o.load(e)),o.portal.user)return(0,w.Ss)(o)}catch(t){(0,h.r9)(t)}return{features:{edit:!0,fullEdit:!1},content:{updateItem:!1}}}async _fetchFallbackUserPrivileges(t){let e=!0;try{e=await this._fetchUserHasEditingPrivileges(t)}catch(t){(0,h.r9)(t)}return{features:{edit:e,fullEdit:!1},content:{updateItem:!1}}}async _fetchUserHasEditingPrivileges(t){const e=this.url?s.id?.findCredential(this.url):null;if(!e)return!0;const o=I.credential===e?I.user:await this._fetchEditingUser(t);return I.credential=e,I.user=o,(0,d.Wi)(o)||null==o.privileges||o.privileges.includes("features:user:edit")}async _fetchEditingUser(t){const e=this.portalItem?.portal?.user;if(e)return e;const o=s.id.findServerInfo(this.url??"");if(!o?.owningSystemUrl)return null;const r=`${o.owningSystemUrl}/sharing/rest`,i=v.Z.getDefault();if(i&&i.loaded&&(0,u.Fv)(i.restUrl)===(0,u.Fv)(r))return i.user;const l=`${r}/community/self`,p=(0,d.pC)(t)?t.signal:null,h=await(0,a.q6)((0,n.default)(l,{authMode:"no-prompt",query:{f:"json"},signal:p}));return h.ok?_.default.fromJSON(h.value.data):null}read(t,e){e&&(e.layer=this),super.read(t,e)}write(t,e){const o=e&&e.portal,r=this.portalItem&&this.portalItem.id&&(this.portalItem.portal||v.Z.getDefault());return o&&r&&!(0,u.tm)(r.restUrl,o.restUrl)?(e.messages&&e.messages.push(new l.Z("layer:cross-portal",`The layer '${this.title} (${this.id})' cannot be persisted because it refers to an item on a different portal than the one being saved to. To save, set layer.portalItem to null or save to the same portal as the item associated with the layer`,{layer:this})),null):super.write(t,{...e,layer:this})}};return(0,r._)([(0,c.Cb)({type:b.default})],e.prototype,"portalItem",null),(0,r._)([(0,y.r)("web-document","portalItem",["itemId"])],e.prototype,"readPortalItem",null),(0,r._)([(0,g.c)("web-document","portalItem",{itemId:{type:String}})],e.prototype,"writePortalItem",null),(0,r._)([(0,c.Cb)({clonable:!1})],e.prototype,"resourceReferences",void 0),(0,r._)([(0,c.Cb)({type:Boolean,readOnly:!0})],e.prototype,"userHasEditingPrivileges",void 0),(0,r._)([(0,c.Cb)({type:Boolean,readOnly:!0})],e.prototype,"userHasFullEditingPrivileges",void 0),(0,r._)([(0,c.Cb)({type:Boolean,readOnly:!0})],e.prototype,"userHasUpdateItemPrivileges",void 0),e=(0,r._)([(0,m.j)("esri.layers.mixins.PortalLayer")],e),e},I={credential:null,user:null}},11223:(t,e,o)=>{o.d(e,{Z:()=>p});var r,i=o(43697),s=o(96674),n=o(5600),a=(o(75215),o(67676),o(52011));let l=r=class extends s.wq{constructor(t){super(t),this.name=null,this.title=null,this.expression=null,this.returnType=null}clone(){return new r({name:this.name,title:this.title,expression:this.expression,returnType:this.returnType})}};(0,i._)([(0,n.Cb)({type:String,json:{write:!0}})],l.prototype,"name",void 0),(0,i._)([(0,n.Cb)({type:String,json:{write:!0}})],l.prototype,"title",void 0),(0,i._)([(0,n.Cb)({type:String,json:{write:!0}})],l.prototype,"expression",void 0),(0,i._)([(0,n.Cb)({type:["string","number"],json:{write:!0}})],l.prototype,"returnType",void 0),l=r=(0,i._)([(0,a.j)("esri.popup.ExpressionInfo")],l);const p=l},422:(t,e,o)=>{o.d(e,{Z:()=>c});var r,i=o(43697),s=o(35454),n=o(96674),a=o(22974),l=o(5600),p=(o(75215),o(36030)),d=o(52011),h=o(63061);let u=r=class extends n.wq{constructor(t){super(t),this.fieldName=null,this.format=null,this.isEditable=!1,this.label=null,this.stringFieldOption="text-box",this.statisticType=null,this.tooltip=null,this.visible=!0}clone(){return new r({fieldName:this.fieldName,format:this.format?(0,a.d9)(this.format):null,isEditable:this.isEditable,label:this.label,stringFieldOption:this.stringFieldOption,statisticType:this.statisticType,tooltip:this.tooltip,visible:this.visible})}};(0,i._)([(0,l.Cb)({type:String,json:{write:!0}})],u.prototype,"fieldName",void 0),(0,i._)([(0,l.Cb)({type:h.Z,json:{write:!0}})],u.prototype,"format",void 0),(0,i._)([(0,l.Cb)({type:Boolean,json:{write:!0,default:!1}})],u.prototype,"isEditable",void 0),(0,i._)([(0,l.Cb)({type:String,json:{write:!0}})],u.prototype,"label",void 0),(0,i._)([(0,p.J)(new s.X({richtext:"rich-text",textarea:"text-area",textbox:"text-box"}),{default:"text-box"})],u.prototype,"stringFieldOption",void 0),(0,i._)([(0,l.Cb)({type:["count","sum","min","max","avg","stddev","var"],json:{write:!0}})],u.prototype,"statisticType",void 0),(0,i._)([(0,l.Cb)({type:String,json:{write:!0}})],u.prototype,"tooltip",void 0),(0,i._)([(0,l.Cb)({type:Boolean,json:{write:!0}})],u.prototype,"visible",void 0),u=r=(0,i._)([(0,d.j)("esri.popup.FieldInfo")],u);const c=u},84649:(t,e,o)=>{o.d(e,{Z:()=>p});var r,i=o(43697),s=o(5600),n=(o(75215),o(67676),o(52011)),a=o(63801);let l=r=class extends a.Z{constructor(t){super(t),this.description=null,this.displayType="auto",this.title=null,this.type="attachments"}clone(){return new r({description:this.description,displayType:this.displayType,title:this.title})}};(0,i._)([(0,s.Cb)({type:String,json:{write:!0}})],l.prototype,"description",void 0),(0,i._)([(0,s.Cb)({type:["auto","preview","list"],json:{write:!0}})],l.prototype,"displayType",void 0),(0,i._)([(0,s.Cb)({type:String,json:{write:!0}})],l.prototype,"title",void 0),(0,i._)([(0,s.Cb)({type:["attachments"],readOnly:!0,json:{read:!1,write:!0}})],l.prototype,"type",void 0),l=r=(0,i._)([(0,n.j)("esri.popup.content.AttachmentsContent")],l);const p=l},41463:(t,e,o)=>{o.d(e,{Z:()=>d});var r,i=o(43697),s=o(5600),n=(o(75215),o(67676),o(52011)),a=o(50379),l=o(87102);let p=r=class extends a.Z{constructor(t){super(t),this.type="bar-chart"}clone(){return new r({altText:this.altText,title:this.title,caption:this.caption,value:this.value?this.value.clone():null})}};(0,i._)([(0,s.Cb)({type:["bar-chart"],readOnly:!0,json:{type:["barchart"],read:!1,write:l.l.write}})],p.prototype,"type",void 0),p=r=(0,i._)([(0,n.j)("esri.popup.content.BarChartMediaInfo")],p);const d=p},87131:(t,e,o)=>{o.d(e,{Z:()=>d});var r,i=o(43697),s=o(5600),n=(o(75215),o(67676),o(52011)),a=o(50379),l=o(87102);let p=r=class extends a.Z{constructor(t){super(t),this.type="column-chart"}clone(){return new r({altText:this.altText,title:this.title,caption:this.caption,value:this.value?this.value.clone():null})}};(0,i._)([(0,s.Cb)({type:["column-chart"],readOnly:!0,json:{type:["columnchart"],read:!1,write:l.l.write}})],p.prototype,"type",void 0),p=r=(0,i._)([(0,n.j)("esri.popup.content.ColumnChartMediaInfo")],p);const d=p},63801:(t,e,o)=>{o.d(e,{Z:()=>l});var r=o(43697),i=o(96674),s=o(5600),n=(o(75215),o(67676),o(52011));let a=class extends i.wq{constructor(t){super(t),this.type=null}};(0,r._)([(0,s.Cb)({type:["attachments","custom","fields","media","text","expression","relationship"],readOnly:!0,json:{read:!1,write:!0}})],a.prototype,"type",void 0),a=(0,r._)([(0,n.j)("esri.popup.content.Content")],a);const l=a},48074:(t,e,o)=>{o.d(e,{Z:()=>d});var r,i=o(43697),s=o(22974),n=o(5600),a=(o(75215),o(52011)),l=o(63801);let p=r=class extends l.Z{constructor(t){super(t),this.creator=null,this.destroyer=null,this.outFields=null,this.type="custom"}clone(){return new r({creator:this.creator,destroyer:this.destroyer,outFields:Array.isArray(this.outFields)?(0,s.d9)(this.outFields):null})}};(0,i._)([(0,n.Cb)()],p.prototype,"creator",void 0),(0,i._)([(0,n.Cb)()],p.prototype,"destroyer",void 0),(0,i._)([(0,n.Cb)()],p.prototype,"outFields",void 0),(0,i._)([(0,n.Cb)({type:["custom"],readOnly:!0})],p.prototype,"type",void 0),p=r=(0,i._)([(0,a.j)("esri.popup.content.CustomContent")],p);const d=p},38745:(t,e,o)=>{o.d(e,{Z:()=>c});var r,i=o(43697),s=o(5600),n=(o(75215),o(67676),o(52011)),a=o(96674);let l=r=class extends a.wq{constructor(t){super(t),this.title=null,this.expression=null,this.returnType="dictionary"}clone(){return new r({title:this.title,expression:this.expression})}};(0,i._)([(0,s.Cb)({type:String,json:{write:!0}})],l.prototype,"title",void 0),(0,i._)([(0,s.Cb)({type:String,json:{write:!0}})],l.prototype,"expression",void 0),(0,i._)([(0,s.Cb)({type:["dictionary"],readOnly:!0,json:{read:!1,write:!0}})],l.prototype,"returnType",void 0),l=r=(0,i._)([(0,n.j)("esri.popup.ElementExpressionInfo")],l);const p=l;var d,h=o(63801);let u=d=class extends h.Z{constructor(t){super(t),this.expressionInfo=null,this.type="expression"}clone(){return new d({expressionInfo:this.expressionInfo?.clone()})}};(0,i._)([(0,s.Cb)({type:p,json:{write:!0}})],u.prototype,"expressionInfo",void 0),(0,i._)([(0,s.Cb)({type:["expression"],readOnly:!0,json:{read:!1,write:!0}})],u.prototype,"type",void 0),u=d=(0,i._)([(0,n.j)("esri.popup.content.ExpressionContent")],u);const c=u},9190:(t,e,o)=>{o.d(e,{Z:()=>u});var r,i=o(43697),s=o(22974),n=o(5600),a=(o(75215),o(52011)),l=o(30556),p=o(422),d=o(63801);let h=r=class extends d.Z{constructor(t){super(t),this.attributes=null,this.description=null,this.fieldInfos=null,this.title=null,this.type="fields"}writeFieldInfos(t,e){e.fieldInfos=t&&t.map((t=>t.toJSON()))}clone(){return new r((0,s.d9)({attributes:this.attributes,description:this.description,fieldInfos:this.fieldInfos,title:this.title}))}};(0,i._)([(0,n.Cb)({type:Object,json:{write:!0}})],h.prototype,"attributes",void 0),(0,i._)([(0,n.Cb)({type:String,json:{write:!0}})],h.prototype,"description",void 0),(0,i._)([(0,n.Cb)({type:[p.Z]})],h.prototype,"fieldInfos",void 0),(0,i._)([(0,l.c)("fieldInfos")],h.prototype,"writeFieldInfos",null),(0,i._)([(0,n.Cb)({type:String,json:{write:!0}})],h.prototype,"title",void 0),(0,i._)([(0,n.Cb)({type:["fields"],readOnly:!0,json:{read:!1,write:!0}})],h.prototype,"type",void 0),h=r=(0,i._)([(0,a.j)("esri.popup.content.FieldsContent")],h);const u=h},13151:(t,e,o)=>{o.d(e,{Z:()=>c});var r,i=o(43697),s=o(5600),n=(o(75215),o(67676),o(52011)),a=o(35320),l=o(96674);let p=r=class extends l.wq{constructor(t){super(t),this.linkURL=null,this.sourceURL=null}clone(){return new r({linkURL:this.linkURL,sourceURL:this.sourceURL})}};(0,i._)([(0,s.Cb)({type:String,json:{write:!0}})],p.prototype,"linkURL",void 0),(0,i._)([(0,s.Cb)({type:String,json:{write:!0}})],p.prototype,"sourceURL",void 0),p=r=(0,i._)([(0,n.j)("esri.popup.content.support.ImageMediaInfoValue")],p);const d=p;var h;let u=h=class extends a.Z{constructor(t){super(t),this.refreshInterval=null,this.type="image",this.value=null}clone(){return new h({altText:this.altText,title:this.title,caption:this.caption,refreshInterval:this.refreshInterval,value:this.value?this.value.clone():null})}};(0,i._)([(0,s.Cb)({type:Number,json:{write:!0}})],u.prototype,"refreshInterval",void 0),(0,i._)([(0,s.Cb)({type:["image"],readOnly:!0,json:{read:!1,write:!0}})],u.prototype,"type",void 0),(0,i._)([(0,s.Cb)({type:d,json:{write:!0}})],u.prototype,"value",void 0),u=h=(0,i._)([(0,n.j)("esri.popup.content.ImageMediaInfo")],u);const c=u},55869:(t,e,o)=>{o.d(e,{Z:()=>d});var r,i=o(43697),s=o(5600),n=(o(75215),o(67676),o(52011)),a=o(50379),l=o(87102);let p=r=class extends a.Z{constructor(t){super(t),this.type="line-chart"}clone(){return new r({altText:this.altText,title:this.title,caption:this.caption,value:this.value?this.value.clone():null})}};(0,i._)([(0,s.Cb)({type:["line-chart"],readOnly:!0,json:{type:["linechart"],read:!1,write:l.l.write}})],p.prototype,"type",void 0),p=r=(0,i._)([(0,n.j)("esri.popup.content.LineChartMediaInfo")],p);const d=p},10214:(t,e,o)=>{o.d(e,{Z:()=>v});var r,i=o(43697),s=o(22974),n=o(5600),a=(o(75215),o(71715)),l=o(52011),p=o(30556),d=o(41463),h=o(87131),u=o(63801),c=o(13151),y=o(55869),m=o(13353),g=o(79742);let f=r=class extends u.Z{constructor(t){super(t),this.activeMediaInfoIndex=null,this.attributes=null,this.description=null,this.mediaInfos=null,this.title=null,this.type="media"}readMediaInfos(t){return t&&t.map((t=>"image"===t.type?c.Z.fromJSON(t):"barchart"===t.type?d.Z.fromJSON(t):"columnchart"===t.type?h.Z.fromJSON(t):"linechart"===t.type?y.Z.fromJSON(t):"piechart"===t.type?m.Z.fromJSON(t):void 0)).filter(Boolean)}writeMediaInfos(t,e){e.mediaInfos=t&&t.map((t=>t.toJSON()))}clone(){return new r((0,s.d9)({activeMediaInfoIndex:this.activeMediaInfoIndex,attributes:this.attributes,description:this.description,mediaInfos:this.mediaInfos,title:this.title}))}};(0,i._)([(0,n.Cb)()],f.prototype,"activeMediaInfoIndex",void 0),(0,i._)([(0,n.Cb)({type:Object,json:{write:!0}})],f.prototype,"attributes",void 0),(0,i._)([(0,n.Cb)({type:String,json:{write:!0}})],f.prototype,"description",void 0),(0,i._)([(0,n.Cb)({types:[g.V]})],f.prototype,"mediaInfos",void 0),(0,i._)([(0,a.r)("mediaInfos")],f.prototype,"readMediaInfos",null),(0,i._)([(0,p.c)("mediaInfos")],f.prototype,"writeMediaInfos",null),(0,i._)([(0,n.Cb)({type:String,json:{write:!0}})],f.prototype,"title",void 0),(0,i._)([(0,n.Cb)({type:["media"],readOnly:!0,json:{read:!1,write:!0}})],f.prototype,"type",void 0),f=r=(0,i._)([(0,l.j)("esri.popup.content.MediaContent")],f);const v=f},13353:(t,e,o)=>{o.d(e,{Z:()=>d});var r,i=o(43697),s=o(5600),n=(o(75215),o(67676),o(52011)),a=o(50379),l=o(87102);let p=r=class extends a.Z{constructor(t){super(t),this.type="pie-chart"}clone(){return new r({altText:this.altText,title:this.title,caption:this.caption,value:this.value?this.value.clone():null})}};(0,i._)([(0,s.Cb)({type:["pie-chart"],readOnly:!0,json:{type:["piechart"],read:!1,write:l.l.write}})],p.prototype,"type",void 0),p=r=(0,i._)([(0,n.j)("esri.popup.content.PieChartMediaInfo")],p);const d=p},71423:(t,e,o)=>{o.d(e,{Z:()=>h});var r=o(43697),i=o(2368),s=o(5600),n=o(75215),a=(o(67676),o(52011)),l=o(63801),p=o(44729);let d=class extends((0,i.J)(l.Z)){constructor(t){super(t),this.description=null,this.displayCount=null,this.displayType="list",this.orderByFields=null,this.relationshipId=null,this.title=null,this.type="relationship"}};(0,r._)([(0,s.Cb)({type:String,json:{write:!0}})],d.prototype,"description",void 0),(0,r._)([(0,s.Cb)({type:Number,json:{type:n.z8,write:!0}})],d.prototype,"displayCount",void 0),(0,r._)([(0,s.Cb)({type:["list"],json:{write:!0}})],d.prototype,"displayType",void 0),(0,r._)([(0,s.Cb)({type:[p.Z],json:{write:!0}})],d.prototype,"orderByFields",void 0),(0,r._)([(0,s.Cb)({type:Number,json:{type:n.z8,write:!0}})],d.prototype,"relationshipId",void 0),(0,r._)([(0,s.Cb)({type:String,json:{write:!0}})],d.prototype,"title",void 0),(0,r._)([(0,s.Cb)({type:["relationship"],readOnly:!0,json:{read:!1,write:!0}})],d.prototype,"type",void 0),d=(0,r._)([(0,a.j)("esri.popup.content.RelationshipContent")],d);const h=d},44951:(t,e,o)=>{o.d(e,{Z:()=>p});var r,i=o(43697),s=o(5600),n=(o(75215),o(67676),o(52011)),a=o(63801);let l=r=class extends a.Z{constructor(t){super(t),this.text=null,this.type="text"}clone(){return new r({text:this.text})}};(0,i._)([(0,s.Cb)({type:String,json:{write:!0}})],l.prototype,"text",void 0),(0,i._)([(0,s.Cb)({type:["text"],readOnly:!0,json:{read:!1,write:!0}})],l.prototype,"type",void 0),l=r=(0,i._)([(0,n.j)("esri.popup.content.TextContent")],l);const p=l},50379:(t,e,o)=>{o.d(e,{Z:()=>f});var r,i=o(43697),s=o(5600),n=(o(75215),o(67676),o(52011)),a=o(35320),l=o(96674),p=o(22974),d=o(15923);let h=r=class extends d.Z{constructor(t){super(t),this.fieldName=null,this.tooltip=null,this.value=null}clone(){return new r({fieldName:this.fieldName,tooltip:this.tooltip,value:this.value})}};(0,i._)([(0,s.Cb)()],h.prototype,"fieldName",void 0),(0,i._)([(0,s.Cb)()],h.prototype,"tooltip",void 0),(0,i._)([(0,s.Cb)()],h.prototype,"value",void 0),h=r=(0,i._)([(0,n.j)("esri.popup.content.support.ChartMediaInfoValueSeries")],h);const u=h;var c;let y=c=class extends l.wq{constructor(t){super(t),this.fields=[],this.normalizeField=null,this.series=[],this.tooltipField=null}clone(){return new c({fields:(0,p.d9)(this.fields),normalizeField:this.normalizeField,tooltipField:this.tooltipField})}};(0,i._)([(0,s.Cb)({type:[String],json:{write:!0}})],y.prototype,"fields",void 0),(0,i._)([(0,s.Cb)({type:String,json:{write:!0}})],y.prototype,"normalizeField",void 0),(0,i._)([(0,s.Cb)({type:[u],json:{read:!1}})],y.prototype,"series",void 0),(0,i._)([(0,s.Cb)({type:String,json:{write:!0}})],y.prototype,"tooltipField",void 0),y=c=(0,i._)([(0,n.j)("esri.popup.content.support.ChartMediaInfoValue")],y);const m=y;let g=class extends a.Z{constructor(t){super(t),this.type=null,this.value=null}};(0,i._)([(0,s.Cb)({type:["bar-chart","column-chart","line-chart","pie-chart"],readOnly:!0,json:{read:!1,write:!0}})],g.prototype,"type",void 0),(0,i._)([(0,s.Cb)({type:m,json:{write:!0}})],g.prototype,"value",void 0),g=(0,i._)([(0,n.j)("esri.popup.content.mixins.ChartMediaInfo")],g);const f=g},35320:(t,e,o)=>{o.d(e,{Z:()=>l});var r=o(43697),i=o(96674),s=o(5600),n=(o(75215),o(67676),o(52011));let a=class extends i.wq{constructor(t){super(t),this.altText=null,this.caption="",this.title="",this.type=null}};(0,r._)([(0,s.Cb)({type:String,json:{write:!0}})],a.prototype,"altText",void 0),(0,r._)([(0,s.Cb)({type:String,json:{write:!0}})],a.prototype,"caption",void 0),(0,r._)([(0,s.Cb)({type:String,json:{write:!0}})],a.prototype,"title",void 0),(0,r._)([(0,s.Cb)({type:["image","bar-chart","column-chart","line-chart","pie-chart"],readOnly:!0,json:{read:!1,write:!0}})],a.prototype,"type",void 0),a=(0,r._)([(0,n.j)("esri.popup.content.mixins.MediaInfo")],a);const l=a},87102:(t,e,o)=>{o.d(e,{l:()=>r});const r=(0,o(35454).w)()({barchart:"bar-chart",columnchart:"column-chart",linechart:"line-chart",piechart:"pie-chart"})},79742:(t,e,o)=>{o.d(e,{V:()=>l});var r=o(41463),i=o(87131),s=o(13151),n=o(55869),a=o(13353);const l={base:o(35320).Z,key:"type",defaultKeyValue:"image",typeMap:{"bar-chart":r.Z,"column-chart":i.Z,"line-chart":n.Z,"pie-chart":a.Z,image:s.Z}}},63061:(t,e,o)=>{o.d(e,{Z:()=>y});var r=o(43697);const i=(0,o(35454).w)()({shortDate:"short-date",shortDateShortTime:"short-date-short-time",shortDateShortTime24:"short-date-short-time-24",shortDateLongTime:"short-date-long-time",shortDateLongTime24:"short-date-long-time-24",shortDateLE:"short-date-le",shortDateLEShortTime:"short-date-le-short-time",shortDateLEShortTime24:"short-date-le-short-time-24",shortDateLELongTime:"short-date-le-long-time",shortDateLELongTime24:"short-date-le-long-time-24",longMonthDayYear:"long-month-day-year",longMonthDayYearShortTime:"long-month-day-year-short-time",longMonthDayYearShortTime24:"long-month-day-year-short-time-24",longMonthDayYearLongTime:"long-month-day-year-long-time",longMonthDayYearLongTime24:"long-month-day-year-long-time-24",dayShortMonthYear:"day-short-month-year",dayShortMonthYearShortTime:"day-short-month-year-short-time",dayShortMonthYearShortTime24:"day-short-month-year-short-time-24",dayShortMonthYearLongTime:"day-short-month-year-long-time",dayShortMonthYearLongTime24:"day-short-month-year-long-time-24",longDate:"long-date",longDateShortTime:"long-date-short-time",longDateShortTime24:"long-date-short-time-24",longDateLongTime:"long-date-long-time",longDateLongTime24:"long-date-long-time-24",longMonthYear:"long-month-year",shortMonthYear:"short-month-year",year:"year"});i.toJSON.bind(i),i.fromJSON.bind(i);var s,n=o(96674),a=o(5600),l=o(75215),p=(o(67676),o(36030)),d=o(52011),h=o(90344),u=o(18848);let c=s=class extends n.wq{constructor(t){super(t),this.dateFormat=null,this.dateTimeFormatOptions=null,this.digitSeparator=!1,this.places=null}clone(){return new s({dateFormat:this.dateFormat,digitSeparator:this.digitSeparator,places:this.places})}format(t){return this.dateFormat?(0,h.p6)(t,{...(0,h.Ze)(this.dateFormat),...this.dateTimeFormatOptions}):(0,u.uf)(t,(0,u.sh)(this))}formatRasterPixelValue(t){if(t.includes("-"))return t;let e,o;return t.trim().includes(",")?(e=",",o=e+" ",this._formatDelimitedString(t,e,o,this)):t.trim().includes(";")?(e=";",o=e+" ",this._formatDelimitedString(t,e,o,this)):t.trim().includes(" ")?(e=o=" ",this._formatDelimitedString(t,e,o,this)):this.format(Number(t))}_formatDelimitedString(t,e,o,r){return t&&e&&o&&r?t.trim().split(e).map((t=>this.format(Number(t)))).join(o):t}};(0,r._)([(0,p.J)(i)],c.prototype,"dateFormat",void 0),(0,r._)([(0,a.Cb)({type:Object,json:{read:!1}})],c.prototype,"dateTimeFormatOptions",void 0),(0,r._)([(0,a.Cb)({type:Boolean,json:{write:!0}})],c.prototype,"digitSeparator",void 0),(0,r._)([(0,a.Cb)({type:l.z8,json:{write:!0}})],c.prototype,"places",void 0),c=s=(0,r._)([(0,d.j)("esri.popup.support.FieldInfoFormat")],c);const y=c},44729:(t,e,o)=>{o.d(e,{Z:()=>p});var r,i=o(43697),s=o(96674),n=o(5600),a=(o(75215),o(67676),o(52011));let l=r=class extends s.wq{constructor(t){super(t),this.field=null,this.order=null}clone(){return new r({field:this.field,order:this.order})}};(0,i._)([(0,n.Cb)({type:String,json:{write:!0}})],l.prototype,"field",void 0),(0,i._)([(0,n.Cb)({type:["asc","desc"],json:{write:!0}})],l.prototype,"order",void 0),l=r=(0,i._)([(0,a.j)("esri.popup.support.RelatedRecordsInfoFieldOrder")],l);const p=l}}]);