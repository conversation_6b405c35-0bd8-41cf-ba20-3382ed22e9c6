/*! For license information please see 14b113a6fdcf81e56674.js.LICENSE.txt */
"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[6261],{44291:(t,e,r)=>{r.d(e,{v:()=>N});var n=["input","select","textarea","a[href]","button","[tabindex]:not(slot)","audio[controls]","video[controls]",'[contenteditable]:not([contenteditable="false"])',"details>summary:first-of-type","details"],o=n.join(","),i="undefined"==typeof Element,a=i?function(){}:Element.prototype.matches||Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector,s=!i&&Element.prototype.getRootNode?function(t){return t.getRootNode()}:function(t){return t.ownerDocument},l=function(t,e,r){var n=Array.prototype.slice.apply(t.querySelectorAll(o));return e&&a.call(t,o)&&n.unshift(t),n.filter(r)},c=function t(e,r,n){for(var i=[],s=Array.from(e);s.length;){var l=s.shift();if("SLOT"===l.tagName){var c=l.assignedElements(),u=t(c.length?c:l.children,!0,n);n.flatten?i.push.apply(i,u):i.push({scopeParent:l,candidates:u})}else{a.call(l,o)&&n.filter(l)&&(r||!e.includes(l))&&i.push(l);var d=l.shadowRoot||"function"==typeof n.getShadowRoot&&n.getShadowRoot(l),f=!n.shadowRootFilter||n.shadowRootFilter(l);if(d&&f){var p=t(!0===d?l.children:d.children,!0,n);n.flatten?i.push.apply(i,p):i.push({scopeParent:l,candidates:p})}else s.unshift.apply(s,l.children)}}return i},u=function(t,e){return t.tabIndex<0&&(e||/^(AUDIO|VIDEO|DETAILS)$/.test(t.tagName)||t.isContentEditable)&&isNaN(parseInt(t.getAttribute("tabindex"),10))?0:t.tabIndex},d=function(t,e){return t.tabIndex===e.tabIndex?t.documentOrder-e.documentOrder:t.tabIndex-e.tabIndex},f=function(t){return"INPUT"===t.tagName},p=function(t){var e=t.getBoundingClientRect(),r=e.width,n=e.height;return 0===r&&0===n},g=function(t,e){return!(e.disabled||function(t){return f(t)&&"hidden"===t.type}(e)||function(t,e){var r=e.displayCheck,n=e.getShadowRoot;if("hidden"===getComputedStyle(t).visibility)return!0;var o=a.call(t,"details>summary:first-of-type")?t.parentElement:t;if(a.call(o,"details:not([open]) *"))return!0;if(r&&"full"!==r&&"legacy-full"!==r){if("non-zero-area"===r)return p(t)}else{if("function"==typeof n){for(var i=t;t;){var l=t.parentElement,c=s(t);if(l&&!l.shadowRoot&&!0===n(l))return p(t);t=t.assignedSlot?t.assignedSlot:l||c===t.ownerDocument?l:c.host}t=i}if(function(t){for(var e,r=s(t).host,n=!!(null!==(e=r)&&void 0!==e&&e.ownerDocument.contains(r)||t.ownerDocument.contains(t));!n&&r;){var o;n=!(null===(o=r=s(r).host)||void 0===o||!o.ownerDocument.contains(r))}return n}(t))return!t.getClientRects().length;if("legacy-full"!==r)return!0}return!1}(e,t)||function(t){return"DETAILS"===t.tagName&&Array.prototype.slice.apply(t.children).some((function(t){return"SUMMARY"===t.tagName}))}(e)||function(t){if(/^(INPUT|BUTTON|SELECT|TEXTAREA)$/.test(t.tagName))for(var e=t.parentElement;e;){if("FIELDSET"===e.tagName&&e.disabled){for(var r=0;r<e.children.length;r++){var n=e.children.item(r);if("LEGEND"===n.tagName)return!!a.call(e,"fieldset[disabled] *")||!n.contains(t)}return!0}e=e.parentElement}return!1}(e))},b=function(t,e){return!(function(t){return function(t){return f(t)&&"radio"===t.type}(t)&&!function(t){if(!t.name)return!0;var e,r=t.form||s(t),n=function(t){return r.querySelectorAll('input[type="radio"][name="'+t+'"]')};if("undefined"!=typeof window&&void 0!==window.CSS&&"function"==typeof window.CSS.escape)e=n(window.CSS.escape(t.name));else try{e=n(t.name)}catch(t){return console.error("Looks like you have a radio button with a name attribute containing invalid CSS selector characters and need the CSS.escape polyfill: %s",t.message),!1}var o=function(t,e){for(var r=0;r<t.length;r++)if(t[r].checked&&t[r].form===e)return t[r]}(e,t.form);return!o||o===t}(t)}(e)||u(e)<0||!g(t,e))},h=function(t){var e=parseInt(t.getAttribute("tabindex"),10);return!!(isNaN(e)||e>=0)},m=function t(e){var r=[],n=[];return e.forEach((function(e,o){var i=!!e.scopeParent,a=i?e.scopeParent:e,s=u(a,i),l=i?t(e.candidates):a;0===s?i?r.push.apply(r,l):r.push(a):n.push({documentOrder:o,tabIndex:s,item:e,isScope:i,content:l})})),n.sort(d).reduce((function(t,e){return e.isScope?t.push.apply(t,e.content):t.push(e.content),t}),[]).concat(r)},v=function(t,e){var r;return r=(e=e||{}).getShadowRoot?c([t],e.includeContainer,{filter:b.bind(null,e),flatten:!1,getShadowRoot:e.getShadowRoot,shadowRootFilter:h}):l(t,e.includeContainer,b.bind(null,e)),m(r)},y=function(t,e){if(e=e||{},!t)throw new Error("No node provided");return!1!==a.call(t,o)&&b(e,t)},w=n.concat("iframe").join(","),x=function(t,e){if(e=e||{},!t)throw new Error("No node provided");return!1!==a.call(t,w)&&g(e,t)};function k(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function A(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?k(Object(r),!0).forEach((function(e){O(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):k(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function O(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=typeof t||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==typeof e?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var S=function(t){return"Tab"===t.key||9===t.keyCode},T=function(t){return S(t)&&!t.shiftKey},E=function(t){return S(t)&&t.shiftKey},C=function(t){return setTimeout(t,0)},I=function(t,e){var r=-1;return t.every((function(t,n){return!e(t)||(r=n,!1)})),r},L=function(t){for(var e=arguments.length,r=new Array(e>1?e-1:0),n=1;n<e;n++)r[n-1]=arguments[n];return"function"==typeof t?t.apply(void 0,r):t},F=function(t){return t.target.shadowRoot&&"function"==typeof t.composedPath?t.composedPath()[0]:t.target},j=[],N=function(t,e){var r,n=(null==e?void 0:e.document)||document,o=(null==e?void 0:e.trapStack)||j,i=A({returnFocusOnDeactivate:!0,escapeDeactivates:!0,delayInitialFocus:!0,isKeyForward:T,isKeyBackward:E},e),a={containers:[],containerGroups:[],tabbableGroups:[],nodeFocusedBeforeActivation:null,mostRecentlyFocusedNode:null,active:!1,paused:!1,delayInitialFocusTimer:void 0},s=function(t,e,r){return t&&void 0!==t[e]?t[e]:i[r||e]},u=function(t){return a.containerGroups.findIndex((function(e){var r=e.container,n=e.tabbableNodes;return r.contains(t)||n.find((function(e){return e===t}))}))},d=function(t){var e=i[t];if("function"==typeof e){for(var r=arguments.length,o=new Array(r>1?r-1:0),a=1;a<r;a++)o[a-1]=arguments[a];e=e.apply(void 0,o)}if(!0===e&&(e=void 0),!e){if(void 0===e||!1===e)return e;throw new Error("`".concat(t,"` was specified but was not a node, or did not return a node"))}var s=e;if("string"==typeof e&&!(s=n.querySelector(e)))throw new Error("`".concat(t,"` as selector refers to no known node"));return s},f=function(){var t=d("initialFocus");if(!1===t)return!1;if(void 0===t)if(u(n.activeElement)>=0)t=n.activeElement;else{var e=a.tabbableGroups[0];t=e&&e.firstTabbableNode||d("fallbackFocus")}if(!t)throw new Error("Your focus-trap needs to have at least one focusable element");return t},p=function(){if(a.containerGroups=a.containers.map((function(t){var e,r,n=v(t,i.tabbableOptions),o=(e=t,(r=(r=i.tabbableOptions)||{}).getShadowRoot?c([e],r.includeContainer,{filter:g.bind(null,r),flatten:!0,getShadowRoot:r.getShadowRoot}):l(e,r.includeContainer,g.bind(null,r)));return{container:t,tabbableNodes:n,focusableNodes:o,firstTabbableNode:n.length>0?n[0]:null,lastTabbableNode:n.length>0?n[n.length-1]:null,nextTabbableNode:function(t){var e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],r=o.findIndex((function(e){return e===t}));if(!(r<0))return e?o.slice(r+1).find((function(t){return y(t,i.tabbableOptions)})):o.slice(0,r).reverse().find((function(t){return y(t,i.tabbableOptions)}))}}})),a.tabbableGroups=a.containerGroups.filter((function(t){return t.tabbableNodes.length>0})),a.tabbableGroups.length<=0&&!d("fallbackFocus"))throw new Error("Your focus-trap must have at least one container with at least one tabbable node in it at all times")},b=function t(e){!1!==e&&e!==n.activeElement&&(e&&e.focus?(e.focus({preventScroll:!!i.preventScroll}),a.mostRecentlyFocusedNode=e,function(t){return t.tagName&&"input"===t.tagName.toLowerCase()&&"function"==typeof t.select}(e)&&e.select()):t(f()))},h=function(t){var e=d("setReturnFocus",t);return e||!1!==e&&t},m=function(t){var e=F(t);u(e)>=0||(L(i.clickOutsideDeactivates,t)?r.deactivate({returnFocus:i.returnFocusOnDeactivate&&!x(e,i.tabbableOptions)}):L(i.allowOutsideClick,t)||t.preventDefault())},w=function(t){var e=F(t),r=u(e)>=0;r||e instanceof Document?r&&(a.mostRecentlyFocusedNode=e):(t.stopImmediatePropagation(),b(a.mostRecentlyFocusedNode||f()))},k=function(t){if(("Escape"===(e=t).key||"Esc"===e.key||27===e.keyCode)&&!1!==L(i.escapeDeactivates,t))return t.preventDefault(),void r.deactivate();var e;(i.isKeyForward(t)||i.isKeyBackward(t))&&function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=F(t);p();var n=null;if(a.tabbableGroups.length>0){var o=u(r),s=o>=0?a.containerGroups[o]:void 0;if(o<0)n=e?a.tabbableGroups[a.tabbableGroups.length-1].lastTabbableNode:a.tabbableGroups[0].firstTabbableNode;else if(e){var l=I(a.tabbableGroups,(function(t){var e=t.firstTabbableNode;return r===e}));if(l<0&&(s.container===r||x(r,i.tabbableOptions)&&!y(r,i.tabbableOptions)&&!s.nextTabbableNode(r,!1))&&(l=o),l>=0){var c=0===l?a.tabbableGroups.length-1:l-1;n=a.tabbableGroups[c].lastTabbableNode}else S(t)||(n=s.nextTabbableNode(r,!1))}else{var f=I(a.tabbableGroups,(function(t){var e=t.lastTabbableNode;return r===e}));if(f<0&&(s.container===r||x(r,i.tabbableOptions)&&!y(r,i.tabbableOptions)&&!s.nextTabbableNode(r))&&(f=o),f>=0){var g=f===a.tabbableGroups.length-1?0:f+1;n=a.tabbableGroups[g].firstTabbableNode}else S(t)||(n=s.nextTabbableNode(r))}}else n=d("fallbackFocus");n&&(S(t)&&t.preventDefault(),b(n))}(t,i.isKeyBackward(t))},O=function(t){var e=F(t);u(e)>=0||L(i.clickOutsideDeactivates,t)||L(i.allowOutsideClick,t)||(t.preventDefault(),t.stopImmediatePropagation())},N=function(){if(a.active)return function(t,e){if(t.length>0){var r=t[t.length-1];r!==e&&r.pause()}var n=t.indexOf(e);-1===n||t.splice(n,1),t.push(e)}(o,r),a.delayInitialFocusTimer=i.delayInitialFocus?C((function(){b(f())})):b(f()),n.addEventListener("focusin",w,!0),n.addEventListener("mousedown",m,{capture:!0,passive:!1}),n.addEventListener("touchstart",m,{capture:!0,passive:!1}),n.addEventListener("click",O,{capture:!0,passive:!1}),n.addEventListener("keydown",k,{capture:!0,passive:!1}),r},D=function(){if(a.active)return n.removeEventListener("focusin",w,!0),n.removeEventListener("mousedown",m,!0),n.removeEventListener("touchstart",m,!0),n.removeEventListener("click",O,!0),n.removeEventListener("keydown",k,!0),r};return(r={get active(){return a.active},get paused(){return a.paused},activate:function(t){if(a.active)return this;var e=s(t,"onActivate"),r=s(t,"onPostActivate"),o=s(t,"checkCanFocusTrap");o||p(),a.active=!0,a.paused=!1,a.nodeFocusedBeforeActivation=n.activeElement,e&&e();var i=function(){o&&p(),N(),r&&r()};return o?(o(a.containers.concat()).then(i,i),this):(i(),this)},deactivate:function(t){if(!a.active)return this;var e=A({onDeactivate:i.onDeactivate,onPostDeactivate:i.onPostDeactivate,checkCanReturnFocus:i.checkCanReturnFocus},t);clearTimeout(a.delayInitialFocusTimer),a.delayInitialFocusTimer=void 0,D(),a.active=!1,a.paused=!1,function(t,e){var r=t.indexOf(e);-1!==r&&t.splice(r,1),t.length>0&&t[t.length-1].unpause()}(o,r);var n=s(e,"onDeactivate"),l=s(e,"onPostDeactivate"),c=s(e,"checkCanReturnFocus"),u=s(e,"returnFocus","returnFocusOnDeactivate");n&&n();var d=function(){C((function(){u&&b(h(a.nodeFocusedBeforeActivation)),l&&l()}))};return u&&c?(c(h(a.nodeFocusedBeforeActivation)).then(d,d),this):(d(),this)},pause:function(){return a.paused||!a.active||(a.paused=!0,D()),this},unpause:function(){return a.paused&&a.active?(a.paused=!1,p(),N(),this):this},updateContainerElements:function(t){var e=[].concat(t).filter(Boolean);return a.containers=e.map((function(t){return"string"==typeof t?n.querySelector(t):t})),a.active&&p(),this}}).updateContainerElements(t),r}},8728:(t,e,r)=>{r.d(e,{Z:()=>nt});var n={exports:{}},o={},i={exports:{}},a={};function s(){return{"align-content":!1,"align-items":!1,"align-self":!1,"alignment-adjust":!1,"alignment-baseline":!1,all:!1,"anchor-point":!1,animation:!1,"animation-delay":!1,"animation-direction":!1,"animation-duration":!1,"animation-fill-mode":!1,"animation-iteration-count":!1,"animation-name":!1,"animation-play-state":!1,"animation-timing-function":!1,azimuth:!1,"backface-visibility":!1,background:!0,"background-attachment":!0,"background-clip":!0,"background-color":!0,"background-image":!0,"background-origin":!0,"background-position":!0,"background-repeat":!0,"background-size":!0,"baseline-shift":!1,binding:!1,bleed:!1,"bookmark-label":!1,"bookmark-level":!1,"bookmark-state":!1,border:!0,"border-bottom":!0,"border-bottom-color":!0,"border-bottom-left-radius":!0,"border-bottom-right-radius":!0,"border-bottom-style":!0,"border-bottom-width":!0,"border-collapse":!0,"border-color":!0,"border-image":!0,"border-image-outset":!0,"border-image-repeat":!0,"border-image-slice":!0,"border-image-source":!0,"border-image-width":!0,"border-left":!0,"border-left-color":!0,"border-left-style":!0,"border-left-width":!0,"border-radius":!0,"border-right":!0,"border-right-color":!0,"border-right-style":!0,"border-right-width":!0,"border-spacing":!0,"border-style":!0,"border-top":!0,"border-top-color":!0,"border-top-left-radius":!0,"border-top-right-radius":!0,"border-top-style":!0,"border-top-width":!0,"border-width":!0,bottom:!1,"box-decoration-break":!0,"box-shadow":!0,"box-sizing":!0,"box-snap":!0,"box-suppress":!0,"break-after":!0,"break-before":!0,"break-inside":!0,"caption-side":!1,chains:!1,clear:!0,clip:!1,"clip-path":!1,"clip-rule":!1,color:!0,"color-interpolation-filters":!0,"column-count":!1,"column-fill":!1,"column-gap":!1,"column-rule":!1,"column-rule-color":!1,"column-rule-style":!1,"column-rule-width":!1,"column-span":!1,"column-width":!1,columns:!1,contain:!1,content:!1,"counter-increment":!1,"counter-reset":!1,"counter-set":!1,crop:!1,cue:!1,"cue-after":!1,"cue-before":!1,cursor:!1,direction:!1,display:!0,"display-inside":!0,"display-list":!0,"display-outside":!0,"dominant-baseline":!1,elevation:!1,"empty-cells":!1,filter:!1,flex:!1,"flex-basis":!1,"flex-direction":!1,"flex-flow":!1,"flex-grow":!1,"flex-shrink":!1,"flex-wrap":!1,float:!1,"float-offset":!1,"flood-color":!1,"flood-opacity":!1,"flow-from":!1,"flow-into":!1,font:!0,"font-family":!0,"font-feature-settings":!0,"font-kerning":!0,"font-language-override":!0,"font-size":!0,"font-size-adjust":!0,"font-stretch":!0,"font-style":!0,"font-synthesis":!0,"font-variant":!0,"font-variant-alternates":!0,"font-variant-caps":!0,"font-variant-east-asian":!0,"font-variant-ligatures":!0,"font-variant-numeric":!0,"font-variant-position":!0,"font-weight":!0,grid:!1,"grid-area":!1,"grid-auto-columns":!1,"grid-auto-flow":!1,"grid-auto-rows":!1,"grid-column":!1,"grid-column-end":!1,"grid-column-start":!1,"grid-row":!1,"grid-row-end":!1,"grid-row-start":!1,"grid-template":!1,"grid-template-areas":!1,"grid-template-columns":!1,"grid-template-rows":!1,"hanging-punctuation":!1,height:!0,hyphens:!1,icon:!1,"image-orientation":!1,"image-resolution":!1,"ime-mode":!1,"initial-letters":!1,"inline-box-align":!1,"justify-content":!1,"justify-items":!1,"justify-self":!1,left:!1,"letter-spacing":!0,"lighting-color":!0,"line-box-contain":!1,"line-break":!1,"line-grid":!1,"line-height":!1,"line-snap":!1,"line-stacking":!1,"line-stacking-ruby":!1,"line-stacking-shift":!1,"line-stacking-strategy":!1,"list-style":!0,"list-style-image":!0,"list-style-position":!0,"list-style-type":!0,margin:!0,"margin-bottom":!0,"margin-left":!0,"margin-right":!0,"margin-top":!0,"marker-offset":!1,"marker-side":!1,marks:!1,mask:!1,"mask-box":!1,"mask-box-outset":!1,"mask-box-repeat":!1,"mask-box-slice":!1,"mask-box-source":!1,"mask-box-width":!1,"mask-clip":!1,"mask-image":!1,"mask-origin":!1,"mask-position":!1,"mask-repeat":!1,"mask-size":!1,"mask-source-type":!1,"mask-type":!1,"max-height":!0,"max-lines":!1,"max-width":!0,"min-height":!0,"min-width":!0,"move-to":!1,"nav-down":!1,"nav-index":!1,"nav-left":!1,"nav-right":!1,"nav-up":!1,"object-fit":!1,"object-position":!1,opacity:!1,order:!1,orphans:!1,outline:!1,"outline-color":!1,"outline-offset":!1,"outline-style":!1,"outline-width":!1,overflow:!1,"overflow-wrap":!1,"overflow-x":!1,"overflow-y":!1,padding:!0,"padding-bottom":!0,"padding-left":!0,"padding-right":!0,"padding-top":!0,page:!1,"page-break-after":!1,"page-break-before":!1,"page-break-inside":!1,"page-policy":!1,pause:!1,"pause-after":!1,"pause-before":!1,perspective:!1,"perspective-origin":!1,pitch:!1,"pitch-range":!1,"play-during":!1,position:!1,"presentation-level":!1,quotes:!1,"region-fragment":!1,resize:!1,rest:!1,"rest-after":!1,"rest-before":!1,richness:!1,right:!1,rotation:!1,"rotation-point":!1,"ruby-align":!1,"ruby-merge":!1,"ruby-position":!1,"shape-image-threshold":!1,"shape-outside":!1,"shape-margin":!1,size:!1,speak:!1,"speak-as":!1,"speak-header":!1,"speak-numeral":!1,"speak-punctuation":!1,"speech-rate":!1,stress:!1,"string-set":!1,"tab-size":!1,"table-layout":!1,"text-align":!0,"text-align-last":!0,"text-combine-upright":!0,"text-decoration":!0,"text-decoration-color":!0,"text-decoration-line":!0,"text-decoration-skip":!0,"text-decoration-style":!0,"text-emphasis":!0,"text-emphasis-color":!0,"text-emphasis-position":!0,"text-emphasis-style":!0,"text-height":!0,"text-indent":!0,"text-justify":!0,"text-orientation":!0,"text-overflow":!0,"text-shadow":!0,"text-space-collapse":!0,"text-transform":!0,"text-underline-position":!0,"text-wrap":!0,top:!1,transform:!1,"transform-origin":!1,"transform-style":!1,transition:!1,"transition-delay":!1,"transition-duration":!1,"transition-property":!1,"transition-timing-function":!1,"unicode-bidi":!1,"vertical-align":!1,visibility:!1,"voice-balance":!1,"voice-duration":!1,"voice-family":!1,"voice-pitch":!1,"voice-range":!1,"voice-rate":!1,"voice-stress":!1,"voice-volume":!1,volume:!1,"white-space":!1,widows:!1,width:!0,"will-change":!1,"word-break":!0,"word-spacing":!0,"word-wrap":!0,"wrap-flow":!1,"wrap-through":!1,"writing-mode":!1,"z-index":!1}}var l=/javascript\s*\:/gim;a.whiteList={"align-content":!1,"align-items":!1,"align-self":!1,"alignment-adjust":!1,"alignment-baseline":!1,all:!1,"anchor-point":!1,animation:!1,"animation-delay":!1,"animation-direction":!1,"animation-duration":!1,"animation-fill-mode":!1,"animation-iteration-count":!1,"animation-name":!1,"animation-play-state":!1,"animation-timing-function":!1,azimuth:!1,"backface-visibility":!1,background:!0,"background-attachment":!0,"background-clip":!0,"background-color":!0,"background-image":!0,"background-origin":!0,"background-position":!0,"background-repeat":!0,"background-size":!0,"baseline-shift":!1,binding:!1,bleed:!1,"bookmark-label":!1,"bookmark-level":!1,"bookmark-state":!1,border:!0,"border-bottom":!0,"border-bottom-color":!0,"border-bottom-left-radius":!0,"border-bottom-right-radius":!0,"border-bottom-style":!0,"border-bottom-width":!0,"border-collapse":!0,"border-color":!0,"border-image":!0,"border-image-outset":!0,"border-image-repeat":!0,"border-image-slice":!0,"border-image-source":!0,"border-image-width":!0,"border-left":!0,"border-left-color":!0,"border-left-style":!0,"border-left-width":!0,"border-radius":!0,"border-right":!0,"border-right-color":!0,"border-right-style":!0,"border-right-width":!0,"border-spacing":!0,"border-style":!0,"border-top":!0,"border-top-color":!0,"border-top-left-radius":!0,"border-top-right-radius":!0,"border-top-style":!0,"border-top-width":!0,"border-width":!0,bottom:!1,"box-decoration-break":!0,"box-shadow":!0,"box-sizing":!0,"box-snap":!0,"box-suppress":!0,"break-after":!0,"break-before":!0,"break-inside":!0,"caption-side":!1,chains:!1,clear:!0,clip:!1,"clip-path":!1,"clip-rule":!1,color:!0,"color-interpolation-filters":!0,"column-count":!1,"column-fill":!1,"column-gap":!1,"column-rule":!1,"column-rule-color":!1,"column-rule-style":!1,"column-rule-width":!1,"column-span":!1,"column-width":!1,columns:!1,contain:!1,content:!1,"counter-increment":!1,"counter-reset":!1,"counter-set":!1,crop:!1,cue:!1,"cue-after":!1,"cue-before":!1,cursor:!1,direction:!1,display:!0,"display-inside":!0,"display-list":!0,"display-outside":!0,"dominant-baseline":!1,elevation:!1,"empty-cells":!1,filter:!1,flex:!1,"flex-basis":!1,"flex-direction":!1,"flex-flow":!1,"flex-grow":!1,"flex-shrink":!1,"flex-wrap":!1,float:!1,"float-offset":!1,"flood-color":!1,"flood-opacity":!1,"flow-from":!1,"flow-into":!1,font:!0,"font-family":!0,"font-feature-settings":!0,"font-kerning":!0,"font-language-override":!0,"font-size":!0,"font-size-adjust":!0,"font-stretch":!0,"font-style":!0,"font-synthesis":!0,"font-variant":!0,"font-variant-alternates":!0,"font-variant-caps":!0,"font-variant-east-asian":!0,"font-variant-ligatures":!0,"font-variant-numeric":!0,"font-variant-position":!0,"font-weight":!0,grid:!1,"grid-area":!1,"grid-auto-columns":!1,"grid-auto-flow":!1,"grid-auto-rows":!1,"grid-column":!1,"grid-column-end":!1,"grid-column-start":!1,"grid-row":!1,"grid-row-end":!1,"grid-row-start":!1,"grid-template":!1,"grid-template-areas":!1,"grid-template-columns":!1,"grid-template-rows":!1,"hanging-punctuation":!1,height:!0,hyphens:!1,icon:!1,"image-orientation":!1,"image-resolution":!1,"ime-mode":!1,"initial-letters":!1,"inline-box-align":!1,"justify-content":!1,"justify-items":!1,"justify-self":!1,left:!1,"letter-spacing":!0,"lighting-color":!0,"line-box-contain":!1,"line-break":!1,"line-grid":!1,"line-height":!1,"line-snap":!1,"line-stacking":!1,"line-stacking-ruby":!1,"line-stacking-shift":!1,"line-stacking-strategy":!1,"list-style":!0,"list-style-image":!0,"list-style-position":!0,"list-style-type":!0,margin:!0,"margin-bottom":!0,"margin-left":!0,"margin-right":!0,"margin-top":!0,"marker-offset":!1,"marker-side":!1,marks:!1,mask:!1,"mask-box":!1,"mask-box-outset":!1,"mask-box-repeat":!1,"mask-box-slice":!1,"mask-box-source":!1,"mask-box-width":!1,"mask-clip":!1,"mask-image":!1,"mask-origin":!1,"mask-position":!1,"mask-repeat":!1,"mask-size":!1,"mask-source-type":!1,"mask-type":!1,"max-height":!0,"max-lines":!1,"max-width":!0,"min-height":!0,"min-width":!0,"move-to":!1,"nav-down":!1,"nav-index":!1,"nav-left":!1,"nav-right":!1,"nav-up":!1,"object-fit":!1,"object-position":!1,opacity:!1,order:!1,orphans:!1,outline:!1,"outline-color":!1,"outline-offset":!1,"outline-style":!1,"outline-width":!1,overflow:!1,"overflow-wrap":!1,"overflow-x":!1,"overflow-y":!1,padding:!0,"padding-bottom":!0,"padding-left":!0,"padding-right":!0,"padding-top":!0,page:!1,"page-break-after":!1,"page-break-before":!1,"page-break-inside":!1,"page-policy":!1,pause:!1,"pause-after":!1,"pause-before":!1,perspective:!1,"perspective-origin":!1,pitch:!1,"pitch-range":!1,"play-during":!1,position:!1,"presentation-level":!1,quotes:!1,"region-fragment":!1,resize:!1,rest:!1,"rest-after":!1,"rest-before":!1,richness:!1,right:!1,rotation:!1,"rotation-point":!1,"ruby-align":!1,"ruby-merge":!1,"ruby-position":!1,"shape-image-threshold":!1,"shape-outside":!1,"shape-margin":!1,size:!1,speak:!1,"speak-as":!1,"speak-header":!1,"speak-numeral":!1,"speak-punctuation":!1,"speech-rate":!1,stress:!1,"string-set":!1,"tab-size":!1,"table-layout":!1,"text-align":!0,"text-align-last":!0,"text-combine-upright":!0,"text-decoration":!0,"text-decoration-color":!0,"text-decoration-line":!0,"text-decoration-skip":!0,"text-decoration-style":!0,"text-emphasis":!0,"text-emphasis-color":!0,"text-emphasis-position":!0,"text-emphasis-style":!0,"text-height":!0,"text-indent":!0,"text-justify":!0,"text-orientation":!0,"text-overflow":!0,"text-shadow":!0,"text-space-collapse":!0,"text-transform":!0,"text-underline-position":!0,"text-wrap":!0,top:!1,transform:!1,"transform-origin":!1,"transform-style":!1,transition:!1,"transition-delay":!1,"transition-duration":!1,"transition-property":!1,"transition-timing-function":!1,"unicode-bidi":!1,"vertical-align":!1,visibility:!1,"voice-balance":!1,"voice-duration":!1,"voice-family":!1,"voice-pitch":!1,"voice-range":!1,"voice-rate":!1,"voice-stress":!1,"voice-volume":!1,volume:!1,"white-space":!1,widows:!1,width:!0,"will-change":!1,"word-break":!0,"word-spacing":!0,"word-wrap":!0,"wrap-flow":!1,"wrap-through":!1,"writing-mode":!1,"z-index":!1},a.getDefaultWhiteList=s,a.onAttr=function(t,e,r){},a.onIgnoreAttr=function(t,e,r){},a.safeAttrValue=function(t,e){return l.test(e)?"":e};var c=function(t){return String.prototype.trim?t.trim():t.replace(/(^\s*)|(\s*$)/g,"")},u=function(t){return String.prototype.trimRight?t.trimRight():t.replace(/(\s*$)/g,"")},d=a,f=function(t,e){";"!==(t=u(t))[t.length-1]&&(t+=";");var r=t.length,n=!1,o=0,i=0,a="";function s(){if(!n){var r=c(t.slice(o,i)),s=r.indexOf(":");if(-1!==s){var l=c(r.slice(0,s)),u=c(r.slice(s+1));if(l){var d=e(o,a.length,l,u,r);d&&(a+=d+"; ")}}}o=i+1}for(;i<r;i++){var l=t[i];if("/"===l&&"*"===t[i+1]){var d=t.indexOf("*/",i+2);if(-1===d)break;o=(i=d+1)+1,n=!1}else"("===l?n=!0:")"===l?n=!1:";"===l?n||s():"\n"===l&&s()}return c(a)};function p(t){return null==t}function g(t){(t=function(t){var e={};for(var r in t)e[r]=t[r];return e}(t||{})).whiteList=t.whiteList||d.whiteList,t.onAttr=t.onAttr||d.onAttr,t.onIgnoreAttr=t.onIgnoreAttr||d.onIgnoreAttr,t.safeAttrValue=t.safeAttrValue||d.safeAttrValue,this.options=t}g.prototype.process=function(t){if(!(t=(t=t||"").toString()))return"";var e=this.options,r=e.whiteList,n=e.onAttr,o=e.onIgnoreAttr,i=e.safeAttrValue;return f(t,(function(t,e,a,s,l){var c=r[a],u=!1;if(!0===c?u=c:"function"==typeof c?u=c(s):c instanceof RegExp&&(u=c.test(s)),!0!==u&&(u=!1),s=i(a,s)){var d,f={position:e,sourcePosition:t,source:l,isWhite:u};return u?p(d=n(a,s,f))?a+":"+s:d:p(d=o(a,s,f))?void 0:d}}))};var b=g;!function(t,e){var r=a,n=b;for(var o in(e=t.exports=function(t,e){return new n(e).process(t)}).FilterCSS=n,r)e[o]=r[o]}(i,i.exports);var h={indexOf:function(t,e){var r,n;if(Array.prototype.indexOf)return t.indexOf(e);for(r=0,n=t.length;r<n;r++)if(t[r]===e)return r;return-1},forEach:function(t,e,r){var n,o;if(Array.prototype.forEach)return t.forEach(e,r);for(n=0,o=t.length;n<o;n++)e.call(r,t[n],n,t)},trim:function(t){return String.prototype.trim?t.trim():t.replace(/(^\s*)|(\s*$)/g,"")},spaceIndex:function(t){var e=/\s|\n|\t/.exec(t);return e?e.index:-1}},m=i.exports.FilterCSS,v=i.exports.getDefaultWhiteList,y=h;var w=new m;function x(t){return t.replace(k,"&lt;").replace(A,"&gt;")}var k=/</g,A=/>/g,O=/"/g,S=/&quot;/g,T=/&#([a-zA-Z0-9]*);?/gim,E=/&colon;?/gim,C=/&newline;?/gim,I=/((j\s*a\s*v\s*a|v\s*b|l\s*i\s*v\s*e)\s*s\s*c\s*r\s*i\s*p\s*t\s*|m\s*o\s*c\s*h\s*a):/gi,L=/e\s*x\s*p\s*r\s*e\s*s\s*s\s*i\s*o\s*n\s*\(.*/gi,F=/u\s*r\s*l\s*\(.*/gi;function j(t){return t.replace(O,"&quot;")}function N(t){return t.replace(S,'"')}function D(t){return t.replace(T,(function(t,e){return"x"===e[0]||"X"===e[0]?String.fromCharCode(parseInt(e.substr(1),16)):String.fromCharCode(parseInt(e,10))}))}function P(t){return t.replace(E,":").replace(C," ")}function R(t){for(var e="",r=0,n=t.length;r<n;r++)e+=t.charCodeAt(r)<32?" ":t.charAt(r);return y.trim(e)}function z(t){return R(t=P(t=D(t=N(t))))}function V(t){return x(t=j(t))}o.whiteList={a:["target","href","title"],abbr:["title"],address:[],area:["shape","coords","href","alt"],article:[],aside:[],audio:["autoplay","controls","crossorigin","loop","muted","preload","src"],b:[],bdi:["dir"],bdo:["dir"],big:[],blockquote:["cite"],br:[],caption:[],center:[],cite:[],code:[],col:["align","valign","span","width"],colgroup:["align","valign","span","width"],dd:[],del:["datetime"],details:["open"],div:[],dl:[],dt:[],em:[],figcaption:[],figure:[],font:["color","size","face"],footer:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],header:[],hr:[],i:[],img:["src","alt","title","width","height"],ins:["datetime"],li:[],mark:[],nav:[],ol:[],p:[],pre:[],s:[],section:[],small:[],span:[],sub:[],summary:[],sup:[],strong:[],strike:[],table:["width","border","align","valign"],tbody:["align","valign"],td:["width","rowspan","colspan","align","valign"],tfoot:["align","valign"],th:["width","rowspan","colspan","align","valign"],thead:["align","valign"],tr:["rowspan","align","valign"],tt:[],u:[],ul:[],video:["autoplay","controls","crossorigin","loop","muted","playsinline","poster","preload","src","height","width"]},o.getDefaultWhiteList=function(){return{a:["target","href","title"],abbr:["title"],address:[],area:["shape","coords","href","alt"],article:[],aside:[],audio:["autoplay","controls","crossorigin","loop","muted","preload","src"],b:[],bdi:["dir"],bdo:["dir"],big:[],blockquote:["cite"],br:[],caption:[],center:[],cite:[],code:[],col:["align","valign","span","width"],colgroup:["align","valign","span","width"],dd:[],del:["datetime"],details:["open"],div:[],dl:[],dt:[],em:[],figcaption:[],figure:[],font:["color","size","face"],footer:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],header:[],hr:[],i:[],img:["src","alt","title","width","height"],ins:["datetime"],li:[],mark:[],nav:[],ol:[],p:[],pre:[],s:[],section:[],small:[],span:[],sub:[],summary:[],sup:[],strong:[],strike:[],table:["width","border","align","valign"],tbody:["align","valign"],td:["width","rowspan","colspan","align","valign"],tfoot:["align","valign"],th:["width","rowspan","colspan","align","valign"],thead:["align","valign"],tr:["rowspan","align","valign"],tt:[],u:[],ul:[],video:["autoplay","controls","crossorigin","loop","muted","playsinline","poster","preload","src","height","width"]}},o.onTag=function(t,e,r){},o.onIgnoreTag=function(t,e,r){},o.onTagAttr=function(t,e,r){},o.onIgnoreTagAttr=function(t,e,r){},o.safeAttrValue=function(t,e,r,n){if(r=z(r),"href"===e||"src"===e){if("#"===(r=y.trim(r)))return"#";if("http://"!==r.substr(0,7)&&"https://"!==r.substr(0,8)&&"mailto:"!==r.substr(0,7)&&"tel:"!==r.substr(0,4)&&"data:image/"!==r.substr(0,11)&&"ftp://"!==r.substr(0,6)&&"./"!==r.substr(0,2)&&"../"!==r.substr(0,3)&&"#"!==r[0]&&"/"!==r[0])return""}else if("background"===e){if(I.lastIndex=0,I.test(r))return""}else if("style"===e){if(L.lastIndex=0,L.test(r))return"";if(F.lastIndex=0,F.test(r)&&(I.lastIndex=0,I.test(r)))return"";!1!==n&&(r=(n=n||w).process(r))}return V(r)},o.escapeHtml=x,o.escapeQuote=j,o.unescapeQuote=N,o.escapeHtmlEntities=D,o.escapeDangerHtml5Entities=P,o.clearNonPrintableCharacter=R,o.friendlyAttrValue=z,o.escapeAttrValue=V,o.onIgnoreTagStripAll=function(){return""},o.StripTagBody=function(t,e){"function"!=typeof e&&(e=function(){});var r=!Array.isArray(t),n=[],o=!1;return{onIgnoreTag:function(i,a,s){if(function(e){return!!r||-1!==y.indexOf(t,e)}(i)){if(s.isClosing){var l="[/removed]",c=s.position+l.length;return n.push([!1!==o?o:s.position,c]),o=!1,l}return o||(o=s.position),"[removed]"}return e(i,a,s)},remove:function(t){var e="",r=0;return y.forEach(n,(function(n){e+=t.slice(r,n[0]),r=n[1]})),e+=t.slice(r)}}},o.stripCommentTag=function(t){for(var e="",r=0;r<t.length;){var n=t.indexOf("\x3c!--",r);if(-1===n){e+=t.slice(r);break}e+=t.slice(r,n);var o=t.indexOf("--\x3e",n);if(-1===o)break;r=o+3}return e},o.stripBlankChar=function(t){var e=t.split("");return(e=e.filter((function(t){var e=t.charCodeAt(0);return!(127===e||e<=31&&10!==e&&13!==e)}))).join("")},o.cssFilter=w,o.getDefaultCSSWhiteList=v;var G={},B=h;function M(t){var e,r=B.spaceIndex(t);return e=-1===r?t.slice(1,-1):t.slice(1,r+1),"/"===(e=B.trim(e).toLowerCase()).slice(0,1)&&(e=e.slice(1)),"/"===e.slice(-1)&&(e=e.slice(0,-1)),e}function q(t){return"</"===t.slice(0,2)}var $=/[^a-zA-Z0-9\\_:.-]/gim;function W(t,e){for(;e<t.length;e++){var r=t[e];if(" "!==r)return"="===r?e:-1}}function U(t,e){for(;e<t.length;e++){var r=t[e];if(" "!==r)return"'"===r||'"'===r?e:-1}}function _(t,e){for(;e>0;e--){var r=t[e];if(" "!==r)return"="===r?e:-1}}function H(t){return function(t){return'"'===t[0]&&'"'===t[t.length-1]||"'"===t[0]&&"'"===t[t.length-1]}(t)?t.substr(1,t.length-2):t}G.parseTag=function(t,e,r){var n="",o=0,i=!1,a=!1,s=0,l=t.length,c="",u="";t:for(s=0;s<l;s++){var d=t.charAt(s);if(!1===i){if("<"===d){i=s;continue}}else if(!1===a){if("<"===d){n+=r(t.slice(o,s)),i=s,o=s;continue}if(">"===d){n+=r(t.slice(o,i)),c=M(u=t.slice(i,s+1)),n+=e(i,n.length,c,u,q(u)),o=s+1,i=!1;continue}if('"'===d||"'"===d)for(var f=1,p=t.charAt(s-f);""===p.trim()||"="===p;){if("="===p){a=d;continue t}p=t.charAt(s-++f)}}else if(d===a){a=!1;continue}}return o<t.length&&(n+=r(t.substr(o))),n},G.parseAttr=function(t,e){var r=0,n=0,o=[],i=!1,a=t.length;function s(t,r){if(!((t=(t=B.trim(t)).replace($,"").toLowerCase()).length<1)){var n=e(t,r||"");n&&o.push(n)}}for(var l=0;l<a;l++){var c,u=t.charAt(l);if(!1!==i||"="!==u)if(!1===i||l!==n){if(/\s|\n|\t/.test(u)){if(t=t.replace(/\s|\n|\t/g," "),!1===i){if(-1===(c=W(t,l))){s(B.trim(t.slice(r,l))),i=!1,r=l+1;continue}l=c-1;continue}if(-1===(c=_(t,l-1))){s(i,H(B.trim(t.slice(r,l)))),i=!1,r=l+1;continue}}}else{if(-1===(c=t.indexOf(u,l+1)))break;s(i,B.trim(t.slice(n+1,c))),i=!1,r=(l=c)+1}else i=t.slice(r,l),r=l+1,n='"'===t.charAt(r)||"'"===t.charAt(r)?r:U(t,l+1)}return r<t.length&&(!1===i?s(t.slice(r)):s(i,H(B.trim(t.slice(r))))),B.trim(o.join(" "))};var K=i.exports.FilterCSS,X=o,Y=G,Z=Y.parseTag,Q=Y.parseAttr,J=h;function tt(t){return null==t}function et(t){(t=function(t){var e={};for(var r in t)e[r]=t[r];return e}(t||{})).stripIgnoreTag&&(t.onIgnoreTag&&console.error('Notes: cannot use these two options "stripIgnoreTag" and "onIgnoreTag" at the same time'),t.onIgnoreTag=X.onIgnoreTagStripAll),t.whiteList||t.allowList?t.whiteList=function(t){var e={};for(var r in t)Array.isArray(t[r])?e[r.toLowerCase()]=t[r].map((function(t){return t.toLowerCase()})):e[r.toLowerCase()]=t[r];return e}(t.whiteList||t.allowList):t.whiteList=X.whiteList,t.onTag=t.onTag||X.onTag,t.onTagAttr=t.onTagAttr||X.onTagAttr,t.onIgnoreTag=t.onIgnoreTag||X.onIgnoreTag,t.onIgnoreTagAttr=t.onIgnoreTagAttr||X.onIgnoreTagAttr,t.safeAttrValue=t.safeAttrValue||X.safeAttrValue,t.escapeHtml=t.escapeHtml||X.escapeHtml,this.options=t,!1===t.css?this.cssFilter=!1:(t.css=t.css||{},this.cssFilter=new K(t.css))}et.prototype.process=function(t){if(!(t=(t=t||"").toString()))return"";var e=this.options,r=e.whiteList,n=e.onTag,o=e.onIgnoreTag,i=e.onTagAttr,a=e.onIgnoreTagAttr,s=e.safeAttrValue,l=e.escapeHtml,c=this.cssFilter;e.stripBlankChar&&(t=X.stripBlankChar(t)),e.allowCommentTag||(t=X.stripCommentTag(t));var u=!1;e.stripIgnoreTagBody&&(u=X.StripTagBody(e.stripIgnoreTagBody,o),o=u.onIgnoreTag);var d=Z(t,(function(t,e,u,d,f){var p={sourcePosition:t,position:e,isClosing:f,isWhite:Object.prototype.hasOwnProperty.call(r,u)},g=n(u,d,p);if(!tt(g))return g;if(p.isWhite){if(p.isClosing)return"</"+u+">";var b=function(t){var e=J.spaceIndex(t);if(-1===e)return{html:"",closing:"/"===t[t.length-2]};var r="/"===(t=J.trim(t.slice(e+1,-1)))[t.length-1];return r&&(t=J.trim(t.slice(0,-1))),{html:t,closing:r}}(d),h=r[u],m=Q(b.html,(function(t,e){var r=-1!==J.indexOf(h,t),n=i(u,t,e,r);return tt(n)?r?(e=s(u,t,e,c))?t+'="'+e+'"':t:tt(n=a(u,t,e,r))?void 0:n:n}));return d="<"+u,m&&(d+=" "+m),b.closing&&(d+=" /"),d+">"}return tt(g=o(u,d,p))?l(d):g}),l);return u&&(d=u.remove(d)),d};var rt=et;!function(t,e){var r=o,n=G,i=rt;function a(t,e){return new i(e).process(t)}(e=t.exports=a).filterXSS=a,e.FilterXSS=i,function(){for(var t in r)e[t]=r[t];for(var o in n)e[o]=n[o]}(),"undefined"!=typeof self&&"undefined"!=typeof DedicatedWorkerGlobalScope&&self instanceof DedicatedWorkerGlobalScope&&(self.filterXSS=t.exports)}(n,n.exports);var nt=function(){function t(t,e){var r,o=this;this.arcgisWhiteList={a:["href","style","target"],abbr:["title"],audio:["autoplay","controls","loop","muted","preload"],b:[],br:[],dd:["style"],div:["align","style"],dl:["style"],dt:["style"],em:[],figcaption:["style"],figure:["style"],font:["color","face","size","style"],h1:["style"],h2:["style"],h3:["style"],h4:["style"],h5:["style"],h6:["style"],hr:[],i:[],img:["alt","border","height","src","style","width"],li:[],ol:[],p:["style"],source:["media","src","type"],span:["style"],strong:[],sub:["style"],sup:["style"],table:["border","cellpadding","cellspacing","height","style","width"],tbody:[],tr:["align","height","style","valign"],td:["align","colspan","height","nowrap","rowspan","style","valign","width"],th:["align","colspan","height","nowrap","rowspan","style","valign","width"],u:[],ul:[],video:["autoplay","controls","height","loop","muted","poster","preload","width"]},this.allowedProtocols=["http","https","mailto","iform","tel","flow","lfmobile","arcgis-navigator","arcgis-appstudio-player","arcgis-survey123","arcgis-collector","arcgis-workforce","arcgis-explorer","arcgis-trek2there","arcgis-quickcapture","mspbi","comgooglemaps","pdfefile","pdfehttp","pdfehttps","boxapp","boxemm","awb","awbs","gropen","radarscope"],this.arcgisFilterOptions={allowCommentTag:!0,safeAttrValue:function(t,e,r,i){return"a"===t&&"href"===e||("img"===t||"source"===t)&&"src"===e?o.sanitizeUrl(r):n.exports.safeAttrValue(t,e,r,i)}},this._entityMap={"&":"&#x38;","<":"&#x3C;",">":"&#x3E;",'"':"&#x22;","'":"&#x27;","/":"&#x2F;"},t&&!e?r=t:t&&e?(r=Object.create(this.arcgisFilterOptions),Object.keys(t).forEach((function(e){"whiteList"===e?r.whiteList=o._extendObjectOfArrays([o.arcgisWhiteList,t.whiteList||{}]):r[e]=t[e]}))):(r=Object.create(this.arcgisFilterOptions)).whiteList=this.arcgisWhiteList,this.xssFilterOptions=r,this._xssFilter=new n.exports.FilterXSS(r)}return t.prototype.sanitize=function(t,e){switch(void 0===e&&(e={}),typeof t){case"number":return isNaN(t)||!isFinite(t)?null:t;case"boolean":return t;case"string":return this._xssFilter.process(t);case"object":return this._iterateOverObject(t,e);default:if(e.allowUndefined&&void 0===t)return;return null}},t.prototype.sanitizeUrl=function(t,e){var r=(null!=e?e:{}).isProtocolRequired,o=void 0===r||r,i=this._trim(t.substring(0,t.indexOf(":"))),a="/"===t,s=/^#/.test(t),l=i&&this.allowedProtocols.indexOf(i.toLowerCase())>-1;return a||s||l?n.exports.escapeAttrValue(t):i||o?"":n.exports.escapeAttrValue("https://".concat(t))},t.prototype.sanitizeHTMLAttribute=function(t,e,r,o){return"function"==typeof this.xssFilterOptions.safeAttrValue?this.xssFilterOptions.safeAttrValue(t,e,r,o):n.exports.safeAttrValue(t,e,r,o)},t.prototype.validate=function(t,e){void 0===e&&(e={});var r=this.sanitize(t,e);return{isValid:t===r,sanitized:r}},t.prototype.encodeHTML=function(t){var e=this;return String(t).replace(/[&<>"'\/]/g,(function(t){return e._entityMap[t]}))},t.prototype.encodeAttrValue=function(t){var e=/^[a-zA-Z0-9]$/;return String(t).replace(/[\x00-\xFF]/g,(function(r,n){return e.test(r)?r:"&#x".concat(Number(t.charCodeAt(n)).toString(16),";")}))},t.prototype._extendObjectOfArrays=function(t){var e={};return t.forEach((function(t){Object.keys(t).forEach((function(r){Array.isArray(t[r])&&Array.isArray(e[r])?e[r]=e[r].concat(t[r]):e[r]=t[r]}))})),e},t.prototype._iterateOverObject=function(t,e){var r=this;void 0===e&&(e={});try{var n=!1,o=void 0;if(Array.isArray(t))o=t.reduce((function(t,o){var i=r.validate(o,e);return i.isValid?t.concat([o]):(n=!0,t.concat([i.sanitized]))}),[]);else{if(!function(t){if("object"!=typeof t||null===t)return!1;if("[object Object]"!==Object.prototype.toString.call(t))return!1;var e=Object.getPrototypeOf(t);if(null===e)return!0;for(;null!==Object.getPrototypeOf(e);)e=Object.getPrototypeOf(e);return Object.getPrototypeOf(t)===e}(t)){if(e.allowUndefined&&void 0===t)return;return null}o=Object.keys(t).reduce((function(o,i){var a=t[i],s=r.validate(a,e);return s.isValid?o[i]=a:(n=!0,o[i]=s.sanitized),o}),{})}return n?o:t}catch(t){return null}},t.prototype._trim=function(t){return String.prototype.trim?t.trim():t.replace(/(^\s*)|(\s*$)/g,"")},t}()},65161:(t,e,r)=>{r.d(e,{Y:()=>n});const n=t=>i.$resourcesUrl$=t,o="undefined"!=typeof window?window:{},i=(o.document,o.HTMLElement,{$flags$:0,$resourcesUrl$:"",jmp:t=>t(),raf:t=>requestAnimationFrame(t),ael:(t,e,r,n)=>t.addEventListener(e,r,n),rel:(t,e,r,n)=>t.removeEventListener(e,r,n),ce:(t,e)=>new CustomEvent(t,e)});var a="undefined"==typeof Element;function s(){const{classList:t}=document.body,e=window.matchMedia("(prefers-color-scheme: dark)").matches,r=()=>t.contains("calcite-mode-dark")||t.contains("calcite-mode-auto")&&e?"dark":"light",n=t=>document.body.dispatchEvent(new CustomEvent("calciteModeChange",{bubbles:!0,detail:{mode:t}})),o=t=>{i!==t&&n(t),i=t};let i=r();n(i),window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change",(t=>o(t.matches?"dark":"light"))),new MutationObserver((()=>o(r()))).observe(document.body,{attributes:!0,attributeFilter:["class"]})}a||Element.prototype.matches||Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector,!a&&Element.prototype.getRootNode,"undefined"!=typeof window&&"undefined"!=typeof location&&"undefined"!=typeof document&&window.location===location&&window.document===document&&("interactive"===document.readyState?s():document.addEventListener("DOMContentLoaded",(()=>s()),{once:!0}))}}]);