package org.thingsboard.server.dao.util.imodel.response.model;

import org.thingsboard.server.dao.util.imodel.response.exception.InvalidEntityException;
import org.thingsboard.server.dao.util.imodel.response.exception.ViewChangeException;
import org.thingsboard.server.dao.util.imodel.response.handler.CustomFailureHandler;
import org.thingsboard.server.dao.util.imodel.response.handler.ValidationFailureHandler;
import org.thingsboard.server.dao.util.imodel.response.Responsible;

import java.util.HashMap;

public class Model extends HashMap<String, Object> implements IModel, Responsible {
    private CustomFailureHandler failureDelegate;
    private ValidationFailureHandler invalidDelegate;
    private ValidationFailureHandler validationFailureDelegate;
    protected String path = null;
    private String valid = null;


    @Override
    public void reclaim() {
        valid = null;
        path = null;
        failureDelegate = null;
        invalidDelegate = null;
        validationFailureDelegate = null;
    }

    @Override
    public void valid(String valid) {
        this.valid = valid;
    }

    @Override
    public boolean valid() {
        return this.valid != null;
    }

    @Override
    public void redirectTo(String path) throws ViewChangeException {
        this.path = "redirect:" + path;
        throw new ViewChangeException();
    }

    @Override
    public void forwardTo(String routeName) throws ViewChangeException {
        this.path = routeName;
        throw new ViewChangeException();
    }

    @Override
    public String currentPath() {
        return this.path;
    }

    public CustomFailureHandler getFailureDelegate() {
        return failureDelegate;
    }

    public void setFailureDelegate(CustomFailureHandler failureDelegate) {
        this.failureDelegate = failureDelegate;
    }

    public ValidationFailureHandler getInvalidDelegate() {
        return invalidDelegate;
    }

    public void setInvalidDelegate(ValidationFailureHandler invalidDelegate) {
        this.invalidDelegate = invalidDelegate;
    }

    @Override
    public void invalidCallback(ValidationFailureHandler callback) {
        if (valid == null || validationFailureDelegate != null)
            return;

        validationFailureDelegate = callback;
        throw new InvalidEntityException(valid);
    }

    @Override
    public void failureCallback(CustomFailureHandler callback) {
        failureDelegate = callback;
    }

    @Override
    public CustomFailureHandler failureCallback() {
        return failureDelegate;
    }

    @Override
    public ValidationFailureHandler validationFailureDelegate() {
        return validationFailureDelegate;
    }

    @Override
    public Object postProcess(ReturnHelper returnHelper, Object arg) {
        for (String key : keySet()) {
            put(key, returnHelper.process(get(key), null));
        }
        return this;
    }


}
