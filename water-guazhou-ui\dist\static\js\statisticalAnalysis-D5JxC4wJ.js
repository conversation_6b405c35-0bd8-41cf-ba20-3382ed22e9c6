import{m as e}from"./index-r0dFAfgr.js";function a(t){return e({url:"/istar/api/water/source/getWaterSupplyReport",method:"get",params:t})}function p(t){return e({url:"/istar/api/water/source/getWaterSupplyConsumptionReport",method:"get",params:t})}function o(t){return e({url:"/istar/api/water/source/getWaterSupplyDetailReport",method:"get",params:t})}function u(t){return e({url:"/istar/api/water/source/getWaterSupplyDetailReport/export",method:"get",params:t,responseType:"blob"})}function n(t){return e({url:"/istar/api/water/source/getWaterOutletAndInletReport",method:"get",params:t})}function i(t){return e({url:"/istar/api/water/source/getWaterSupplyAndEnergyData",method:"get",params:t})}function l(t){return e({url:"/istar/api/water/source/getWaterSupplyAndEnergyDataDetail",method:"get",params:t})}function s(t){return e({url:"/istar/api/water/source/getOperationOverview",method:"get",params:t})}export{i as a,l as b,p as c,o as d,u as e,s as f,n as g,a as h};
