import{i as De,s as M,a4 as et,w as W,ak as ht,e as w,y as S,a as Q,W as ft,b as q,R as dt,u as tt,an as mt,Z as pt,a6 as yt,o as gt}from"./Point-WxyopZva.js";import{z as pe,hk as it,w as G,cM as st,cS as rt,bH as nt,c as xt,t as It,Q as wt,R as bt,de as St,dh as Rt,eg as vt,q as Tt,V as _t,cU as me,dk as Ft,cN as kt,cR as Ct,bI as Pt,e as Mt}from"./MapView-DaoQedLH.js";import{c as $t,w as Me,l as at,$ as Je,S as Ne,V as Ot,L as Dt,j as He,n as Bt,m as zt,a as Et,o as Jt,d as Nt}from"./fetchRasterInfo-B6Y1j1SM.js";import{R as F,T as $,aO as X,a$ as Ht,a4 as Lt,eZ as At}from"./index-r0dFAfgr.js";import{K as Wt,l as qt}from"./widget-BcWKanF2.js";import{U as ot,L as jt}from"./pe-B8dP0-Ut.js";import{g as lt,y as Gt,a as Ut,s as Vt,p as ct,h as Le,m as Xt,d as Ae,v as Yt}from"./multidimensionalUtils-BqWBjmd-.js";import{S as Kt,b as ye,T as Qt,r as We,u as ge,p as Zt,g as Be,j as ei,c as ti,v as ii,N as si,U as ri,R as ni,e as Ie,B as ai,L as oi,D as li}from"./RasterSymbolizer-BF_flzvK.js";import{a as qe,x as je,h as Ge,d as ci,m as ui}from"./RawBlockCache-BtrwijFI.js";import{u as hi}from"./pixelRangeUtils-Dr0gmLDH.js";import{b as Te,F as fi,D as di,a as mi,j as pi,d as yi,f as gi,R as xi,S as Ii,m as wi}from"./dataUtils-DovfQoP5.js";import{T as Ue,U as we,j as Ve,o as Xe,n as bi,Q as _e,J as Fe,$ as Si,V as Ri,C as vi}from"./rasterProjectionHelper-BvgFmUDx.js";import{C as $e,f as Ti,i as _i,m as ze,c as Fi,h as ki}from"./utils-Dvxaaxmn.js";import{n as Ci,z as Pi}from"./TilemapCache-BPMaYmR0.js";import"./generateRendererUtils-Bt0vqUD2.js";const Ye=8,Mi=256;let U=class extends Wt(ft){constructor(){super(...arguments),this.rasterJobHandler=null,this.datasetName=null,this.datasetFormat=null,this.hasUniqueSourceStorageInfo=!0,this.rasterInfo=null,this.ioConfig={sampling:"closest"}}async init(){const i=Ue();this.addResolvingPromise(i),await this.when()}normalizeCtorArgs(i){return i&&i.ioConfig&&(i={...i,ioConfig:{resolution:null,bandIds:null,sampling:"closest",tileInfo:pe.create(),...i.ioConfig}}),i}get _isGlobalWrappableSource(){const{rasterInfo:i}=this,e=we(i.spatialReference);return F(e)&&i.extent.width>=e/2}set url(i){this._set("url",it(i,De.getLogger(this.declaredClass)))}async open(i){throw new M("BaseRaster:open-not-implemented","open() is not implemented")}async fetchTile(i,e,r,t={}){const s=t.tileInfo||this.rasterInfo.storageInfo.tileInfo,n=this.getTileExtentFromTileInfo(i,e,r,s);return this.fetchPixels(n,s.size[0],s.size[1],t)}async identify(i,e={}){var P;i=et(W,i).clone().normalize();const{multidimensionalDefinition:r,timeExtent:t}=e,{rasterInfo:s}=this,{hasMultidimensionalTranspose:n,multidimensionalInfo:a}=s;let{transposedVariableName:o}=e;const l=F(a)&&n&&(t!=null||lt(r));l&&!o&&(o=F(r)&&r.length>0?r[0].variableName??void 0:a.variables[0].name,e={...e,transposedVariableName:o}),e=this._getRequestOptionsWithSliceId(e);const{spatialReference:c,extent:u}=s,{datumTransformation:h}=e;let f=Ve(i,c,h);if(!u.intersects(f))return{location:f,value:null};if(F(s.transform)){const O=s.transform.inverseTransform(f);if(!s.nativeExtent.intersects(O))return{location:O,value:null};f=O}let d=0;const p=F(o)&&F(a)&&s.hasMultidimensionalTranspose;if(this.datasetFormat==="Function"){const O=this.primaryRasters.rasters[0];if(p)return O.identify(f,e);const{pixelSize:H}=s,B=3,D=H.x*B/2,N=H.y*B/2,L=new G({xmin:f.x-D,xmax:f.x+D,ymin:f.y-N,ymax:f.y+N,spatialReference:c}),j={interpolation:"nearest"},{pixelBlock:J}=await O.fetchPixels(L,B,B,j),{pixelBlock:z}=await this.fetchPixels(L,B,B,j);if($(J))return{location:f,value:null};const Z=Math.floor(B*B*.5),xe=!J.mask||J.mask[Z]?J.pixels.map(ce=>ce[Z]):null;let le;return F(z)&&(le=!z.mask||z.mask[Z]?z.pixels.map(ce=>ce[Z]):void 0),{location:f,value:xe,processedValue:le,pyramidLevel:0}}if(!p){if(e.srcResolution)d=Xe(e.srcResolution,s,this.ioConfig.sampling).pyramidLevel;else if(d=await this.computeBestPyramidLevelForLocation(i,e),d==null)return{location:f,value:null}}const g=this.identifyPixelLocation(f,d,null,p);if(g===null)return{location:f,value:null};const{row:m,col:y,rowOffset:x,colOffset:I,blockWidth:v}=g,k=o??X(e.sliceId),_=qe(this.url,k),C=`${d}/${m}/${y}`;let T=je(_,null,C);$(T)&&(T=this.fetchRawTile(d,m,y,e),Ge(_,null,C,T));const R=await T;if($(R)||!((P=R.pixels)!=null&&P.length))return{location:f,value:null};const b=x*v+I;return this._processIdentifyResult(R,{srcLocation:f,position:b,pyramidLevel:d,useTransposedTile:!!p,requestSomeSlices:l,identifyOptions:e})}async fetchPixels(i,e,r,t={}){if(i=bi(i),(t=this._getRequestOptionsWithSliceId(t)).requestRawData)return this._fetchPixels(i,e,r,t);const s=we(i.spatialReference),n=_e(i);if($(s)||n===0||n===1&&this._isGlobalWrappableSource)return this._fetchPixels(i,e,r,t);if(n>=3)return{extent:i,pixelBlock:null};const a=[],{xmin:o,xmax:l}=i,c=Math.round(s/(l-o)*e),u=c-Math.round((s/2-o)/(l-o)*e);let h=0;const f=[];for(let m=0;m<=n;m++){const y=new G({xmin:m===0?o:-s/2,xmax:m===n?l-s*m:s/2,ymin:i.ymin,ymax:i.ymax,spatialReference:i.spatialReference}),x=m===0?c-u:m===n?e-h:c;h+=x,f.push(x);const I=t.disableWrapAround&&m>0?null:this._fetchPixels(y,x,r,t);a.push(I)}const d=(await Promise.all(a)).map(m=>m==null?void 0:m.pixelBlock);let p=null;const g={width:e,height:r};return this.rasterJobHandler?p=(await this.rasterJobHandler.mosaicAndTransform({srcPixelBlocks:d,srcMosaicSize:g,destDimension:null,coefs:null,sampleSpacing:null,interpolation:"nearest",alignmentInfo:null,blockWidths:f},t)).pixelBlock:p=Te(d,g,{blockWidths:f}),{extent:i,srcExtent:Fe(i,this.rasterInfo.spatialReference,t.datumTransformation),pixelBlock:p}}async fetchRawPixels(i,e,r,t={}){e={x:Math.floor(e.x),y:Math.floor(e.y)};const s=await this._fetchRawTiles(i,e,r,t),{nativeExtent:n,nativePixelSize:a,storageInfo:o}=this.rasterInfo,l=2**i,c=a.x*l,u=a.y*l,h=new G({xmin:n.xmin+c*e.x,xmax:n.xmin+c*(e.x+r.width-1),ymin:n.ymax-u*(e.y+r.height-1),ymax:n.ymax-u*e.y,spatialReference:n.spatialReference});if(!s)return{extent:h,srcExtent:h,pixelBlock:null};const{pixelBlocks:f,mosaicSize:d}=s;if(f.length===1&&F(f[0])&&f[0].width===r.width&&f[0].height===r.height)return{extent:h,srcExtent:h,pixelBlock:s.pixelBlocks[0]};const p=i>0?o.pyramidBlockWidth:o.blockWidth,g=i>0?o.pyramidBlockHeight:o.blockHeight,m={x:e.x%p,y:e.y%g};let y;return this.rasterJobHandler?y=(await this.rasterJobHandler.mosaicAndTransform({srcPixelBlocks:f,srcMosaicSize:d,destDimension:r,clipOffset:m,clipSize:r,coefs:null,sampleSpacing:null,interpolation:t.interpolation,alignmentInfo:null,blockWidths:null},t)).pixelBlock:y=Te(f,d,{clipOffset:m,clipSize:r}),{extent:h,srcExtent:h,pixelBlock:y}}fetchRawTile(i,e,r,t){throw new M("BaseRaster:read-not-implemented","fetchRawTile() is not implemented")}computeExtent(i){return Fe(this.rasterInfo.extent,i)}decodePixelBlock(i,e){return!this.rasterJobHandler||e.useCanvas?Kt(i,e):this.rasterJobHandler.decode({data:i,options:e})}async request(i,e,r=0){const{customFetchParameters:t}=this.ioConfig,{range:s,query:n,headers:a}=e;r=r??e.retryCount??this.ioConfig.retryCount;const o=s?{Range:`bytes=${s.from}-${s.to}`}:null;try{return await ot(i,{...e,query:{...n,...t},headers:{...a,...o}})}catch(l){if(r>0)return r--,this.request(i,e,r);throw l}}getSliceIndex(i){const{multidimensionalInfo:e}=this.rasterInfo;return $(e)||$(i)||i.length===0?null:Gt(i,e)}getTileExtentFromTileInfo(i,e,r,t){const s=Ht(t.lodAt(i));return this.getTileExtent({x:s.resolution,y:s.resolution},e,r,t.origin,t.spatialReference,t.size)}updateTileInfo(){const{storageInfo:i,spatialReference:e,extent:r,pixelSize:t}=this.rasterInfo;if(!i.tileInfo){const s=[],n=i.maximumPyramidLevel||0;let a=Math.max(t.x,t.y),o=1/.0254*96*a;for(let c=0;c<=n;c++)s.push(new st({level:n-c,resolution:a,scale:o})),a*=2,o*=2;const l=new W({x:r.xmin,y:r.ymax,spatialReference:e});i.tileInfo=new pe({origin:l,size:[i.blockWidth,i.blockHeight],spatialReference:e,lods:s}),i.isVirtualTileInfo=!0}}createRemoteDatasetStorageInfo(i,e=512,r=512,t){const{width:s,height:n,nativeExtent:a,pixelSize:o,spatialReference:l}=i,c=new W({x:a.xmin,y:a.ymax,spatialReference:l});t==null&&(t=Math.max(0,Math.round(Math.log(Math.max(s,n))/Math.LN2-8)));const u=this.computeBlockBoundary(a,512,512,{x:a.xmin,y:a.ymax},[o],t);i.storageInfo=new ye({blockWidth:e,blockHeight:r,pyramidBlockWidth:e,pyramidBlockHeight:r,origin:c,firstPyramidLevel:1,maximumPyramidLevel:t,blockBoundary:u})}async computeBestPyramidLevelForLocation(i,e={}){return 0}computeBlockBoundary(i,e,r,t,s,n=0,a=2){if(s.length===1&&n>0){s=[...s];let{x:u,y:h}=s[0];for(let f=0;f<n;f++)u*=a,h*=a,s.push({x:u,y:h})}const o=[],{x:l,y:c}=t;for(let u=0;u<s.length;u++){const{x:h,y:f}=s[u];o.push({minCol:Math.floor((i.xmin-l+.1*h)/e/h),maxCol:Math.floor((i.xmax-l-.1*h)/e/h),minRow:Math.floor((c-i.ymax+.1*f)/r/f),maxRow:Math.floor((c-i.ymin-.1*f)/r/f)})}return o}getPyramidPixelSize(i){const{nativePixelSize:e}=this.rasterInfo,{pyramidResolutions:r,pyramidScalingFactor:t}=this.rasterInfo.storageInfo;if(i===0)return e;if(F(r)&&r.length)return r[i-1];const s=t**i;return{x:e.x*s,y:e.y*s}}identifyPixelLocation(i,e,r,t){const{spatialReference:s,nativeExtent:n,storageInfo:a}=this.rasterInfo,{maximumPyramidLevel:o,origin:l,transposeInfo:c}=a,u=t&&F(c)?c.tileSize[0]:a.blockWidth,h=t&&F(c)?c.tileSize[1]:a.blockHeight,f=Ve(i,s,r);if(!n.intersects(f)||e<0||e>o)return null;const d=this.getPyramidPixelSize(e),{x:p,y:g}=d,m=(l.y-f.y)/g/h,y=(f.x-l.x)/p/u,x=Math.min(h-1,Math.floor((m-Math.floor(m))*h)),I=Math.min(u-1,Math.floor((y-Math.floor(y))*u));return{pyramidLevel:e,row:Math.floor(m),col:Math.floor(y),rowOffset:x,colOffset:I,blockWidth:u,srcLocation:f}}getTileExtent(i,e,r,t,s,n){const[a,o]=n,l=t.x+r*a*i.x,c=l+a*i.x,u=t.y-e*o*i.y,h=u-o*i.y;return new G({xmin:l,xmax:c,ymin:h,ymax:u,spatialReference:s})}getBlockWidthHeight(i){return{blockWidth:i>0?this.rasterInfo.storageInfo.pyramidBlockWidth:this.rasterInfo.storageInfo.blockWidth,blockHeight:i>0?this.rasterInfo.storageInfo.pyramidBlockHeight:this.rasterInfo.storageInfo.blockHeight}}isBlockOutside(i,e,r){const t=this.rasterInfo.storageInfo.blockBoundary[i];return!t||t.maxRow<e||t.maxCol<r||t.minRow>e||t.minCol>r}async _fetchPixels(i,e,r,t={}){let s=_e(i);if(s>=2)return{extent:i,pixelBlock:null};const n=this._getSourceDataInfo(i,e,r,t),{pyramidLevel:a,pyramidResolution:o,srcResolution:l,srcExtent:c,srcWidth:u,srcHeight:h}=n;if(u===0||h===0)return{extent:i,srcExtent:c,pixelBlock:null};const f=X(this.rasterInfo.transform),d=(f==null?void 0:f.type)==="gcs-shift",p=F(we(i.spatialReference));!d&&p||(s=_e(n.srcExtent,d));const g=this.rasterInfo.storageInfo,m={x:Math.floor((c.xmin-g.origin.x)/o.x+.1),y:Math.floor((g.origin.y-c.ymax)/o.y+.1)},y=await this._fetchRawTiles(a,m,{width:u,height:h,wrapCount:s},t);if(!y)return{extent:i,srcExtent:c,pixelBlock:null};const x=a>0?g.pyramidBlockWidth:g.blockWidth,I=a>0?g.pyramidBlockHeight:g.blockHeight,v=x===u&&I===h&&m.x%x==0&&m.y%I==0,k=new W({x:(i.xmax-i.xmin)/e,y:(i.ymax-i.ymin)/r,spatialReference:i.spatialReference}),_=!i.spatialReference.equals(this.rasterInfo.spatialReference),{datumTransformation:C}=t;if(!_&&v&&y.pixelBlocks.length===1&&x===e&&I===r&&l.x===k.x&&l.y===k.y)return{extent:i,srcExtent:c,pixelBlock:y.pixelBlocks[0]};const T=p&&F(we(c.spatialReference)),R=t.requestProjectedLocalDirections&&this.rasterInfo.dataType.startsWith("vector");R&&!this.rasterJobHandler&&await Ue();const b=this.rasterJobHandler?await this.rasterJobHandler.getProjectionOffsetGrid({projectedExtent:i,srcBufferExtent:y.extent,pixelSize:k.toJSON(),datumTransformation:C,rasterTransform:f,hasWrapAround:s>0||T,isAdaptive:this.ioConfig.optimizeProjectionAccuracy!==!1,includeGCSGrid:R},t):Si({projectedExtent:i,srcBufferExtent:y.extent,pixelSize:k,datumTransformation:C,rasterTransform:f,hasWrapAround:s>0||T,isAdaptive:!1,includeGCSGrid:R});let P;const O=!t.requestRawData,H={rows:b.spacing[0],cols:b.spacing[1]},B=X(this._getRasterTileAlignmentInfo(a,y.extent.xmin)),{pixelBlocks:D,mosaicSize:N,isPartiallyFilled:L}=y;let j=null;if(this.rasterJobHandler)({pixelBlock:P,localNorthDirections:j}=await this.rasterJobHandler.mosaicAndTransform({srcPixelBlocks:D,srcMosaicSize:N,destDimension:O?{width:e,height:r}:null,coefs:O?b.coefficients:null,sampleSpacing:O?H:null,projectDirections:R,gcsGrid:R?b.gcsGrid:null,isUV:this.rasterInfo.dataType==="vector-uv",interpolation:t.interpolation,alignmentInfo:B,blockWidths:null},t));else{const J=Te(D,N,{alignmentInfo:B});P=O?fi(J,{width:e,height:r},b.coefficients,H,t.interpolation):J,R&&b.gcsGrid&&(j=di({width:e,height:r},b.gcsGrid),P=mi(P,this.rasterInfo.dataType,j))}return t.requestRawData||R?{srcExtent:c,pixelBlock:P,transformGrid:b,localNorthDirections:j,extent:i,isPartiallyFilled:L}:{srcExtent:c,extent:i,pixelBlock:P}}async _fetchRawTiles(i,e,r,t){const{origin:s,blockBoundary:n}=this.rasterInfo.storageInfo,{blockWidth:a,blockHeight:o}=this.getBlockWidthHeight(i);let{x:l,y:c}=e,{width:u,height:h,wrapCount:f}=r;const d=this._getRasterTileAlignmentInfo(i,0);t.buffer&&(l-=t.buffer.cols,c-=t.buffer.rows,u+=2*t.buffer.cols,h+=2*t.buffer.rows);let p=0,g=0,m=0;f&&F(d)&&({worldColumnCountFromOrigin:g,originColumnOffset:m,rightPadding:p}=d,g*d.blockWidth-p>=l+u&&(p=0));const y=Math.floor(l/a),x=Math.floor(c/o),I=Math.floor((l+u+p-1)/a),v=Math.floor((c+h+p-1)/o),k=n[i];if(!k)return null;const{minRow:_,minCol:C,maxCol:T,maxRow:R}=k;if(f===0&&(v<_||I<C||x>R||y>T))return null;const b=new Array;let P=!1;const O=this.ioConfig.allowPartialFill==null?t.allowPartialFill:this.ioConfig.allowPartialFill;for(let J=x;J<=v;J++)for(let z=y;z<=I;z++){let Z=z;if(!t.disableWrapAround&&f&&F(d)&&g<=z&&(Z=z-g-m),J>=_&&Z>=C&&R>=J&&T>=Z){const xe=this._fetchRawTile(i,J,Z,t);O?b.push(new Promise(le=>{xe.then(ce=>le(ce)).catch(()=>{P=!0,le(null)})})):b.push(xe)}else b.push(Promise.resolve(null))}if(b.length===0)return null;const H=await Promise.all(b),B={height:(v-x+1)*o,width:(I-y+1)*a},{spatialReference:D}=this.rasterInfo,N=this.getPyramidPixelSize(i),{x:L,y:j}=N;return{extent:new G({xmin:s.x+y*a*L,xmax:s.x+(I+1)*a*L,ymin:s.y-(v+1)*o*j,ymax:s.y-x*o*j,spatialReference:D}),pixelBlocks:H,mosaicSize:B,isPartiallyFilled:P}}_fetchRawTile(i,e,r,t){const s=this.rasterInfo.storageInfo.blockBoundary[i];if(!s)return Promise.resolve(null);const{minRow:n,minCol:a,maxCol:o,maxRow:l}=s;if(e<n||r<a||e>l||r>o)return Promise.resolve(null);const c=qe(this.url,t.sliceId),u=`${i}/${e}/${r}`;let h=je(c,t.registryId,u);if($(h)){const f=new AbortController;h=this.fetchRawTile(i,e,r,{...t,signal:f.signal}),Ge(c,t.registryId,u,h,f),h.catch(()=>ci(c,t.registryId,u))}return t.signal&&ht(t,()=>{ui(c,t.registryId,u)}),h}_computeMagDirValues(i){var l;const{bandCount:e,dataType:r}=this.rasterInfo;if(!(e===2&&r==="vector-magdir"||r==="vector-uv")||(i==null?void 0:i.length)!==2||!((l=i[0])!=null&&l.length))return null;const t=i[0].length;if(r==="vector-magdir"){const c=i[1].map(u=>(u+360)%360);return[i[0],c]}const[s,n]=i,a=[],o=[];for(let c=0;c<t;c++){const[u,h]=pi([s[c],n[c]]);a.push(u),o.push(h)}return[a,o]}_getRasterTileAlignmentInfo(i,e){return this._rasterTileAlighmentInfo==null&&(this._rasterTileAlighmentInfo=Ri(this.rasterInfo)),$(this._rasterTileAlighmentInfo.pyramidsInfo)?null:{startX:e,halfWorldWidth:this._rasterTileAlighmentInfo.halfWorldWidth,hasGCSSShiftTransform:this._rasterTileAlighmentInfo.hasGCSSShiftTransform,...this._rasterTileAlighmentInfo.pyramidsInfo[i]}}_getSourceDataInfo(i,e,r,t={}){const s={datumTransformation:t.datumTransformation,pyramidLevel:0,pyramidResolution:null,srcExtent:null,srcHeight:0,srcResolution:null,srcWidth:0};t.srcResolution&&(s.srcResolution=t.srcResolution,this._updateSourceDataInfo(i,s));const n=this.rasterInfo.storageInfo.maximumPyramidLevel||0,{srcWidth:a,srcHeight:o,pyramidLevel:l}=s,c=a/e,u=o/r,h=l<n&&c*u>=16,f=l===n&&this._requireTooManySrcTiles(a,o,e,r);if(h||f||a===0||o===0){const d=new W({x:(i.xmax-i.xmin)/e,y:(i.ymax-i.ymin)/r,spatialReference:i.spatialReference});let p=vi(d,this.rasterInfo.spatialReference,i,s.datumTransformation);const g=!p||t.srcResolution&&p.x+p.y<t.srcResolution.x+t.srcResolution.y;if(h&&t.srcResolution&&g){const m=Math.round(Math.log(Math.max(c,u))/Math.LN2)-1;if(n-l+3>=m){const y=2**m;p={x:t.srcResolution.x*y,y:t.srcResolution.y*y}}}p&&(s.srcResolution=p,this._updateSourceDataInfo(i,s))}return this._requireTooManySrcTiles(s.srcWidth,s.srcHeight,e,r)&&(s.srcWidth=0,s.srcHeight=0),s}_requireTooManySrcTiles(i,e,r,t){const{tileInfo:s}=this.rasterInfo.storageInfo;return Math.ceil(i/s.size[0])*Math.ceil(e/s.size[1])>=Mi||i/r>Ye||e/t>Ye}_updateSourceDataInfo(i,e){e.srcWidth=0,e.srcHeight=0;const r=this.rasterInfo.spatialReference,{srcResolution:t,datumTransformation:s}=e,{pyramidLevel:n,pyramidResolution:a,excessiveReading:o}=Xe(t,this.rasterInfo,this.ioConfig.sampling);if(o)return;let l=e.srcExtent||Fe(i,r,s);if(l==null)return;const c=X(this.rasterInfo.transform);c&&(l=c.inverseTransform(l)),e.srcExtent=l;const u=Math.ceil((l.xmax-l.xmin)/a.x-.1),h=Math.ceil((l.ymax-l.ymin)/a.y-.1);e.pyramidLevel=n,e.pyramidResolution=a,e.srcWidth=u,e.srcHeight=h}_getRequestOptionsWithSliceId(i){return F(this.rasterInfo.multidimensionalInfo)&&i.sliceId==null&&(i={...i,sliceId:this.getSliceIndex(i.multidimensionalDefinition)}),i}_processIdentifyResult(i,e){const{srcLocation:r,position:t,pyramidLevel:s,useTransposedTile:n}=e,a=i.pixels[0].length/i.width/i.height;if(!(!i.mask||i.mask[t]))return{location:r,value:null};const{multidimensionalInfo:o}=this.rasterInfo;if($(o)||!n){const m=i.pixels.map(I=>I[t]),y={location:r,value:m,pyramidLevel:s},x=this._computeMagDirValues(m.map(I=>[I]));return x!=null&&x.length&&(y.magdirValue=x.map(I=>I[0])),y}let l=i.pixels.map(m=>m.slice(t*a,t*a+a)),c=this._computeMagDirValues(l);const{requestSomeSlices:u,identifyOptions:h}=e;let f=Ut(o,h.transposedVariableName);if(u){const m=Vt(f,X(h.multidimensionalDefinition),X(h.timeExtent));l=l.map(y=>m.map(x=>y[x])),c=c==null?void 0:c.map(y=>m.map(x=>y[x])),f=m.map(y=>f[y])}const d=i.noDataValues||this.rasterInfo.noDataValue,p={pixels:l,pixelType:i.pixelType};let g;return F(d)&&(hi(p,d),g=p.mask),{location:r,value:null,dataSeries:f.map((m,y)=>{const x={value:(g==null?void 0:g[y])===0?null:l.map(I=>I[y]),multidimensionalDefinition:m.multidimensionalDefinition.map(I=>new ct({...I,isSlice:!0}))};return c!=null&&c.length&&(x.magdirValue=[c[0][y],c[1][y]]),x}),pyramidLevel:s}}};w([S()],U.prototype,"_rasterTileAlighmentInfo",void 0),w([S({readOnly:!0})],U.prototype,"_isGlobalWrappableSource",null),w([S(rt)],U.prototype,"url",null),w([S({type:String,json:{write:!0}})],U.prototype,"datasetName",void 0),w([S({type:String,json:{write:!0}})],U.prototype,"datasetFormat",void 0),w([S()],U.prototype,"hasUniqueSourceStorageInfo",void 0),w([S()],U.prototype,"rasterInfo",void 0),w([S()],U.prototype,"ioConfig",void 0),w([S()],U.prototype,"sourceJSON",void 0),U=w([Q("esri.layers.support.rasterDatasets.BaseRaster")],U);const ne=U;let te=class extends ne{constructor(){super(...arguments),this.datasetFormat="Function",this.tileType="Raster",this.rasterFunction=null}async open(e){var c,u;await this.init();const{rasterFunction:r}=this;(u=(c=this.primaryRasters)==null?void 0:c.rasters)!=null&&u.length?r.sourceRasters=this.primaryRasters.rasters:this.primaryRasters=r.getPrimaryRasters();const{rasters:t,rasterIds:s}=this.primaryRasters,n=t.map(h=>h.rasterInfo?void 0:h.open(e));await Promise.all(n);const a=t.map(({rasterInfo:h})=>h),o=r.bind({rasterInfos:a,rasterIds:s});if(!o.success||a.length===0)throw new M("raster-function:open",`cannot bind the function: ${o.error??""}`);await this.syncJobHandler();const l=a[0];this.hasUniqueSourceStorageInfo=a.length===1||a.slice(1).every(h=>this._hasSameStorageInfo(h,l)),this.set("sourceJSON",t[0].sourceJSON),this.set("rasterInfo",r.rasterInfo)}async syncJobHandler(){var e;return(e=this.rasterJobHandler)==null?void 0:e.updateRasterFunction(this.rasterFunction)}async fetchPixels(e,r,t,s={}){var f;const{rasters:n,rasterIds:a}=this.primaryRasters,o=n.map(d=>d.fetchPixels(e,r,t,s)),l=await Promise.all(o),c=l.map(d=>d.pixelBlock);if(s.skipRasterFunction||c.every(d=>$(d)))return l[0];const u=((f=l.find(d=>F(d.pixelBlock)))==null?void 0:f.extent)??e,h=this.rasterJobHandler?await this.rasterJobHandler.process({extent:u,primaryPixelBlocks:c,primaryRasterIds:a}):this.rasterFunction.process({extent:u,primaryPixelBlocks:c,primaryRasterIds:a});return{...l[0],pixelBlock:h}}_hasSameStorageInfo(e,r){const{storageInfo:t,pixelSize:s,spatialReference:n,extent:a}=e,{storageInfo:o,pixelSize:l,spatialReference:c,extent:u}=r;return s.x===l.x&&s.y===l.y&&n.equals(c)&&a.equals(u)&&t.blockHeight===o.blockHeight&&t.blockWidth===o.blockWidth&&t.maximumPyramidLevel===o.maximumPyramidLevel}};w([S({type:String,json:{write:!0}})],te.prototype,"datasetFormat",void 0),w([S()],te.prototype,"tileType",void 0),w([S()],te.prototype,"rasterFunction",void 0),w([S()],te.prototype,"primaryRasters",void 0),te=w([Q("esri.layers.support.rasterDatasets.FunctionRaster")],te);const Oe=te,Ke=De.getLogger("esri.layers.mixins.ImageryTileMixin"),$i=i=>{let e=class extends i{constructor(...t){var s,n;super(...t),this._isConstructedFromFunctionRaster=!1,this._rasterJobHandler={instance:null,refCount:0,connectionPromise:null},this.bandIds=null,this.copyright=null,this.interpolation="nearest",this.multidimensionalSubset=null,this.raster=null,this.rasterFunction=null,this.rasterInfo=null,this.sourceJSON=null,this.spatialReference=null,this.symbolizer=null,this._isConstructedFromFunctionRaster=((n=(s=t[0])==null?void 0:s.raster)==null?void 0:n.datasetFormat)==="Function"}get fullExtent(){var t;return(t=this.rasterInfo)==null?void 0:t.extent}set multidimensionalDefinition(t){this._set("multidimensionalDefinition",t),this.updateRenderer()}get tileInfo(){var t;return(t=this.rasterInfo)==null?void 0:t.storageInfo.tileInfo}set url(t){this._set("url",it(t,Ke))}set renderer(t){this._set("renderer",t),this.updateRenderer()}async convertVectorFieldData(t,s){if($(t)||!this.rasterInfo)return null;const n=this._rasterJobHandler.instance,a=this.rasterInfo.dataType;return n?n.convertVectorFieldData({pixelBlock:t,dataType:a},s):yi(t,a)}async createFlowMesh(t,s){const n=this._rasterJobHandler.instance;return n?n.createFlowMesh(t,s):gi(t.meshType,t.simulationSettings,t.flowData,F(s.signal)?s.signal:new AbortController().signal)}normalizeRasterFetchOptions(t){var o,l;const{multidimensionalInfo:s}=this.rasterInfo??{};if($(s))return t;let n=t.multidimensionalDefinition||this.multidimensionalDefinition;!$(n)&&n.length||(n=Le(this.raster.rasterInfo,{multidimensionalSubset:this.multidimensionalSubset}));const a=t.timeExtent||this.timeExtent;if(F(n)&&F(a)&&(F(a.start)||F(a.end))){n=n.map(y=>y.clone());const c=(l=(o=s.variables.find(({name:y})=>y===n[0].variableName))==null?void 0:o.dimensions)==null?void 0:l.find(({name:y})=>y==="StdTime"),u=n.find(({dimensionName:y})=>y==="StdTime");if(!c||!u)return{...t,multidimensionalDefinition:null};const{start:h,end:f}=a,d=$(h)?null:h.getTime(),p=$(f)?null:f.getTime(),g=d??p,m=p??d;if(F(c.values)){const y=c.values.filter(x=>{if(Array.isArray(x)){if(g===m)return x[0]<=g&&x[1]>=g;const I=x[0]<=g&&x[1]>g||x[0]<m&&x[1]>=m,v=x[0]>=g&&x[1]<=m||x[0]<g&&x[1]>m;return I||v}return g===m?x===g:x>=g&&x<=m});if(y.length){const x=y.sort((I,v)=>g===m?(I[0]??I)-(v[0]??v):Math.abs((I[1]??I)-m)-Math.abs((v[1]??v)-m))[0];u.values=[x]}else n=null}else if(c.hasRegularIntervals&&c.extent){const[y,x]=c.extent;g>x||m<y?n=null:u.values=g===m?[g]:[Math.max(y,g),Math.min(x,m)]}}return F(n)&&Xt(n,this.multidimensionalSubset)?{...t,multidimensionalDefinition:null}:{...t,multidimensionalDefinition:n}}async updateRasterFunction(){var u,h;if(this.type!=="imagery-tile"||!this.rasterFunction&&!this._cachedRasterFunctionJson||JSON.stringify(this.rasterFunction)===JSON.stringify(this._cachedRasterFunctionJson))return;if(this._isConstructedFromFunctionRaster&&this.raster.datasetFormat==="Function"){const f=this.raster.rasterFunction.toJSON();return!this.rasterFunction&&f&&this._set("rasterFunction",Me.fromJSON(f)),void(this._cachedRasterFunctionJson=(u=this.rasterFunction)==null?void 0:u.toJSON())}let t,s=this.raster,n=!1;s.datasetFormat==="Function"?(t=s.primaryRasters.rasters,s=t[0],n=!0):t=[s];const{rasterFunction:a}=this;if(a){const f={raster:s};t.length>1&&t.forEach(g=>f[g.url]=g);const d=$e(a.rasterFunctionDefinition??a.toJSON(),f),p=new Oe({rasterFunction:d});p.rasterJobHandler=this._rasterJobHandler.instance,await p.open(),this._cachedRasterFunctionJson=(h=this.rasterFunction)==null?void 0:h.toJSON(),this.raster=p}else this.raster=s,this._cachedRasterFunctionJson=null;if(this._cachedRendererJson=null,!n&&!a)return;const{bandIds:o}=this,{bandCount:l}=this.raster.rasterInfo,c=o!=null&&o.length?o.some(f=>f>=l):l>=3;o&&(c||this.renderer.type!=="raster-stretch")&&this._set("bandIds",null),this._configDefaultRenderer("auto")}async updateRenderer(){var c;const{loaded:t,symbolizer:s}=this;if(!t||!s)return;const{rasterInfo:n}=this.raster,a=(c=Ae(n,{multidimensionalDefinition:this.multidimensionalDefinition,multidimensionalSubset:this.multidimensionalSubset}))==null?void 0:c.name,o=Je({...this.renderer.toJSON(),variableName:a});if(JSON.stringify(this._cachedRendererJson)===JSON.stringify(o))return;const l=this._rasterJobHandler.instance;l&&(s.rasterInfo=Ne(n,a),s.rendererJSON=o,s.bind(),await l.updateSymbolizer(s),this._cachedRendererJson=o)}async applyRenderer(t,s){const n=t&&t.pixelBlock;if(!(F(n)&&n.pixels&&n.pixels.length>0))return null;let a;await this.updateRenderer();const o=this._rasterJobHandler.instance,l=this.bandIds??[];return a=o?await o.symbolize({...t,simpleStretchParams:s,bandIds:l}):this.symbolizer.symbolize({...t,simpleStretchParams:s,bandIds:l}),a}getTileUrl(t,s,n){return this.raster.datasetFormat==="RasterTileServer"?`${this.url}/tile/${t}/${s}/${n}`:""}getCompatibleTileInfo(t,s,n=!1){if(!this.loaded||$(s))return null;if(n&&t.equals(this.spatialReference))return this.tileInfo;const a=dt(t);return pe.create({size:256,spatialReference:t,origin:a?{x:a.origin[0],y:a.origin[1]}:{x:s.xmin,y:s.ymax}})}getCompatibleFullExtent(t){return this.loaded?(this._compatibleFullExtent&&this._compatibleFullExtent.spatialReference.equals(t)||(this._compatibleFullExtent=this.raster.computeExtent(t)),this._compatibleFullExtent):null}async fetchTile(t,s,n,a={}){if(r(this),a.requestAsImageElement){const l=this.getTileUrl(t,s,n);return ot(l,{responseType:"image",query:{...this.refreshParameters,...this.raster.ioConfig.customFetchParameters},signal:a.signal}).then(c=>c.data)}const{rasterInfo:o}=this;if(F(o.multidimensionalInfo)&&(a=this.normalizeRasterFetchOptions(a),$(a.multidimensionalDefinition))){const l=a.tileInfo||o.storageInfo.tileInfo;return{extent:this.raster.getTileExtentFromTileInfo(t,s,n,l),pixelBlock:null}}return await this._initJobHandler(),await this.updateRasterFunction(),this.renderer.type==="raster-shaded-relief"&&(a={...a,buffer:{cols:1,rows:1}}),this.raster.fetchTile(t,s,n,a)}async fetchPixels(t,s,n,a={}){return F(this.rasterInfo.multidimensionalInfo)&&(a=this.normalizeRasterFetchOptions(a),$(a.multidimensionalDefinition))?{extent:t,pixelBlock:null}:(await this._initJobHandler(),await this.updateRasterFunction(),this.raster.fetchPixels(t,s,n,a))}async identify(t,s={}){var l;const{raster:n,rasterInfo:a}=this;if(F(a.multidimensionalInfo)&&!(a.hasMultidimensionalTranspose&&(lt(s.multidimensionalDefinition)||s.transposedVariableName||s.timeExtent))&&(s=this.normalizeRasterFetchOptions(s),$(s.multidimensionalDefinition)))return{location:t,value:null};const o=(l=this.multidimensionalSubset)==null?void 0:l.areaOfInterest;if(o&&!o.contains(t))throw new M("imagery-tile-mixin:identify","the request cannot be fulfilled when falling outside of the multidimensional subset");return n.identify(t,s)}increaseRasterJobHandlerUsage(){this._rasterJobHandler.refCount++}decreaseRasterJobHandlerUsage(){this._rasterJobHandler.refCount--,this._rasterJobHandler.refCount<=0&&this._shutdownJobHandler()}hasStandardTime(){var a,o,l;const t=(a=this.rasterInfo)==null?void 0:a.multidimensionalInfo;if($(t)||((o=this.rasterInfo)==null?void 0:o.dataType)!=="standard-time")return!1;const s=this.multidimensionalDefinition,n=(l=s==null?void 0:s[0])==null?void 0:l.variableName;return t.variables.some(c=>c.name===n&&(!(s!=null&&s[0].dimensionName)||c.dimensions.some(u=>u.name==="StdTime")))}getStandardTimeValue(t){return new Date(24*(t-25569)*3600*1e3).toString()}getMultidimensionalSubsetVariables(t){var n;const s=t??((n=this.rasterInfo)==null?void 0:n.multidimensionalInfo);return Yt(this.multidimensionalSubset,s)}_configDefaultSettings(){this._configDefaultInterpolation(),this.multidimensionalDefinition||(this.multidimensionalDefinition=Le(this.raster.rasterInfo,{multidimensionalSubset:this.multidimensionalSubset})),this._configDefaultRenderer()}_initJobHandler(){if(this._rasterJobHandler.connectionPromise!=null)return this._rasterJobHandler.connectionPromise;const t=new Bt;return this._rasterJobHandler.connectionPromise=t.initialize().then(()=>{r(this),this._rasterJobHandler.instance=t,this.raster.rasterJobHandler=t,this.renderer&&this.updateRenderer(),this.raster.datasetFormat==="Function"&&this.raster.syncJobHandler()}).catch(()=>{}),this._rasterJobHandler.connectionPromise}_shutdownJobHandler(){this._rasterJobHandler.instance&&this._rasterJobHandler.instance.destroy(),this._rasterJobHandler.instance=null,this._rasterJobHandler.connectionPromise=null,this._rasterJobHandler.refCount=0,this._cachedRendererJson=null,this.raster&&(this.raster.rasterJobHandler=null)}_configDefaultInterpolation(){var t;if(this.interpolation==null){r(this);const{raster:s}=this,n=Ot(s.rasterInfo,s.tileType,(t=this.sourceJSON)==null?void 0:t.defaultResamplingMethod);this._set("interpolation",n)}}_configDefaultRenderer(t="no"){var c,u,h,f,d;r(this);const{rasterInfo:s}=this.raster;!this.bandIds&&s.bandCount>1&&(this.bandIds=Dt(s));const n=(c=Ae(s,{multidimensionalDefinition:this.multidimensionalDefinition,multidimensionalSubset:this.multidimensionalSubset}))==null?void 0:c.name;if(!this.renderer||t==="override"){const p=He(s,{bandIds:this.bandIds,variableName:n});this.raster.datasetFormat==="WCSServer"&&p.type==="raster-stretch"&&((((u=s.statistics)==null?void 0:u[0].max)??0)>1e24||(((h=s.statistics)==null?void 0:h[0].min)??0)<-1e24)&&(p.dynamicRangeAdjustment=!0,p.statistics=null,p.stretchType==="none"&&(p.stretchType="min-max")),this.renderer=p}const a=Je({...this.renderer.toJSON(),variableName:n}),o=Ne(s,n);this.symbolizer?(this.symbolizer.rendererJSON=a,this.symbolizer.rasterInfo=o):this.symbolizer=new Qt({rendererJSON:a,rasterInfo:o});const l=this.symbolizer.bind();if(l.success){if(t==="auto"){const{colormap:p}=this.raster.rasterInfo,g=this.renderer;if(F(p))if(g.type!=="raster-colormap")this._configDefaultRenderer("override");else{const m=He(this.raster.rasterInfo);JSON.stringify(m)!==JSON.stringify(g)&&this._configDefaultRenderer("override")}else if(g.type==="raster-stretch"){const m=(f=this.bandIds)==null?void 0:f.length,y=(d=g.statistics)==null?void 0:d.length;!g.dynamicRangeAdjustment&&y&&m&&y!==m&&this._configDefaultRenderer("override")}}}else Ke.warn("imagery-tile-mixin",l.error||"The given renderer is not supported by the layer."),t==="auto"&&this._configDefaultRenderer("override")}};function r(t){if(!t.raster||!t.rasterInfo)throw new M("imagery-tile","no raster")}return w([S()],e.prototype,"_cachedRendererJson",void 0),w([S()],e.prototype,"_cachedRasterFunctionJson",void 0),w([S()],e.prototype,"_compatibleFullExtent",void 0),w([S()],e.prototype,"_isConstructedFromFunctionRaster",void 0),w([S()],e.prototype,"_rasterJobHandler",void 0),w([S()],e.prototype,"bandIds",void 0),w([S({json:{origins:{service:{read:{source:"copyrightText"}}}}})],e.prototype,"copyright",void 0),w([S({json:{read:!1}})],e.prototype,"fullExtent",null),w([S()],e.prototype,"interpolation",void 0),w([S()],e.prototype,"ioConfig",void 0),w([S({type:[ct],json:{write:!0}})],e.prototype,"multidimensionalDefinition",null),w([S({type:$t,json:{write:!0}})],e.prototype,"multidimensionalSubset",void 0),w([S()],e.prototype,"raster",void 0),w([S({type:Me})],e.prototype,"rasterFunction",void 0),w([S()],e.prototype,"rasterInfo",void 0),w([S()],e.prototype,"sourceJSON",void 0),w([S({readOnly:!0,type:q,json:{read:!1}})],e.prototype,"spatialReference",void 0),w([S({json:{read:!1}})],e.prototype,"tileInfo",null),w([S(rt)],e.prototype,"url",null),w([S({types:at})],e.prototype,"renderer",null),w([S()],e.prototype,"symbolizer",void 0),e=w([Q("esri.layers.ImageryTileMixin")],e),e};function Oi(i){const e=i.fields,r=i.records,t=e.some(c=>c.name.toLowerCase()==="oid")?"OBJECTID":"OID",s=[{name:t,type:"esriFieldTypeOID",alias:"OID"}].concat(e.map(c=>({name:c.name,type:"esriFieldType"+c.typeName,alias:c.name}))),n=s.map(c=>c.name),a=[];let o=0,l=0;return r.forEach(c=>{const u={};for(u[t]=o++,l=1;l<n.length;l++)u[n[l]]=c[l-1];a.push({attributes:u})}),{displayFieldName:"",fields:s,features:a}}class ut{static get supportedVersions(){return[5]}static parse(e){const r=new DataView(e),t=3&r.getUint8(0);if(t!==3)return{header:{version:t},recordSet:null};const s=r.getUint32(4,!0),n=r.getUint16(8,!0),a=r.getUint16(10,!0),o={version:t,recordCount:s,headerByteCount:n,recordByteCount:a};let l=32;const c=[],u=[];let h;if(t===3){for(;r.getUint8(l)!==13;)h=String.fromCharCode(r.getUint8(l+11)).trim(),c.push({name:We(new Uint8Array(e,l,11)),type:h,typeName:["String","Date","Double","Boolean","String","Integer"][["C","D","F","L","M","N"].indexOf(h)],length:r.getUint8(l+16)}),l+=32;if(l+=1,c.length>0)for(;u.length<s&&e.byteLength-l>a;){const f=[];r.getUint8(l)===32?(l+=1,c.forEach(d=>{if(d.type==="C")f.push(We(new Uint8Array(e,l,d.length)).trim());else if(d.type==="N")f.push(parseInt(String.fromCharCode.apply(null,new Uint8Array(e,l,d.length)).trim(),10));else if(d.type==="F")f.push(parseFloat(String.fromCharCode.apply(null,new Uint8Array(e,l,d.length)).trim()));else if(d.type==="D"){const p=String.fromCharCode.apply(null,new Uint8Array(e,l,d.length)).trim();f.push(new Date(parseInt(p.substring(0,4),10),parseInt(p.substring(4,6),10)-1,parseInt(p.substring(6,8),10)))}l+=d.length}),u.push(f)):l+=a}}return{header:o,fields:c,records:u,recordSet:Oi({fields:c,records:u})}}}const ie=new Map;ie.set("int16","esriFieldTypeSmallInteger"),ie.set("int32","esriFieldTypeInteger"),ie.set("int64","esriFieldTypeInteger"),ie.set("float32","esriFieldTypeSingle"),ie.set("float64","esriFieldTypeDouble"),ie.set("text","esriFieldTypeString");const Qe=8;let he=class extends ne{constructor(){super(...arguments),this.storageInfo=null,this.datasetFormat="CRF"}async open(e){await this.init();const{data:r}=await this.request(this.url+"/conf.json",{signal:e==null?void 0:e.signal});if(!this._validateHeader(r))throw new M("cloudraster:open","Invalid or unsupported conf.json.");this.datasetName=this.url.slice(this.url.lastIndexOf("/")+1);const{storageInfo:t,rasterInfo:s}=this._parseHeader(r);if(s.dataType==="thematic"){const n=await this._fetchAuxiliaryInformation();s.attributeTable=n}this._set("storageInfo",t),this._set("rasterInfo",s),this.ioConfig.retryCount=this.ioConfig.retryCount||0}async fetchRawTile(e,r,t,s={}){const{transposeInfo:n}=this.rasterInfo.storageInfo,{transposedVariableName:a}=s,o=!(!n||!a),l=o?0:this.rasterInfo.storageInfo.maximumPyramidLevel-e;if(l<0)return null;const c=this._buildCacheFilePath(l,r,t,s.multidimensionalDefinition,a),u=this._getIndexRecordFromBundle(r,t,o),h=await this.request(c,{range:{from:0,to:this.storageInfo.headerSize-1},responseType:"array-buffer",signal:s.signal});if(!h)return null;const f=new Uint8Array(h.data),d=this._getTileEndAndContentType(f,u);if(d.recordSize===0)return null;const p=await this.request(c,{range:{from:d.position,to:d.position+d.recordSize},responseType:"array-buffer",signal:s.signal});if(!p)return null;const[g,m]=this._getTileSize(o);return this.decodePixelBlock(p.data,{width:g,height:m,planes:null,pixelType:null,returnInterleaved:o})}_validateHeader(e){const r=["origin","extent","geodataXform","LODInfos","blockWidth","blockHeight","bandCount","pixelType","pixelSizeX","pixelSizeY","format","packetSize"];return e&&e.type==="RasterInfo"&&!r.some(t=>!e[t])}_parseHeader(e){var J;const r=["u1","u2","u4","u8","s8","u16","s16","u32","s32","f32","f64"][e.pixelType],{bandCount:t,histograms:s,colormap:n,blockWidth:a,blockHeight:o,firstPyramidLevel:l,maximumPyramidLevel:c}=e,u=e.statistics&&e.statistics.map(z=>({min:z.min,max:z.max,avg:z.mean,stddev:z.standardDeviation,median:z.median,mode:z.mode})),h=e.extent.spatialReference,f=(J=e.geodataXform)==null?void 0:J.spatialReference,d=new q(h!=null&&h.wkid||h!=null&&h.wkt?h:f);let p=new G({xmin:e.extent.xmin,ymin:e.extent.ymin,xmax:e.extent.xmax,ymax:e.extent.ymax,spatialReference:d});const g=new W({x:e.pixelSizeX,y:e.pixelSizeY,spatialReference:d}),m=Math.round((p.xmax-p.xmin)/g.x),y=Math.round((p.ymax-p.ymin)/g.y),x=this._parseTransform(e.geodataXform),I=x?p:null;x&&(p=x.forwardTransform(p),g.x=(p.xmax-p.xmin)/m,g.y=(p.ymax-p.ymin)/y);const v=e.properties??{},k=e.format.toLowerCase().replace("cache/",""),_=new W(e.origin.x,e.origin.y,d);let C,T,R,b;if(n&&n.colors)for(C=[],T=0;T<n.colors.length;T++)R=n.colors[T],b=n.values?n.values[T]:T,C.push([b,255&R,R<<16>>>24,R<<8>>>24,R>>>24]);const P=e.LODInfos,O=[];for(T=0;T<P.levels.length;T++)O.push(new st({level:P.levels[T],resolution:P.resolutions[T],scale:96/.0254*P.resolutions[T]}));const H=new pe({dpi:96,lods:O,format:k,origin:_,size:[a,o],spatialReference:d}),B={recordSize:Qe,packetSize:e.packetSize,headerSize:e.packetSize*e.packetSize*Qe+64},D=[{maxCol:Math.ceil(m/a)-1,maxRow:Math.ceil(y/o)-1,minCol:0,minRow:0}];let N=2;if(c>0)for(T=0;T<c;T++)D.push({maxCol:Math.ceil(m/N/a)-1,maxRow:Math.ceil(y/N/o)-1,minCol:0,minRow:0}),N*=2;const L=e.mdInfo;let j=null;if(L&&v._yxs){const z=v._yxs;j={packetSize:z.PacketSize,tileSize:[z.TileXSize,z.TileYSize]}}return{storageInfo:B,rasterInfo:new ge({width:m,height:y,pixelType:r,bandCount:t,extent:p,nativeExtent:I,transform:x,spatialReference:d,pixelSize:g,keyProperties:v,statistics:u,histograms:s,multidimensionalInfo:L,colormap:C,storageInfo:new ye({blockWidth:a,blockHeight:o,pyramidBlockWidth:a,pyramidBlockHeight:o,origin:_,tileInfo:H,transposeInfo:j,firstPyramidLevel:l,maximumPyramidLevel:c,blockBoundary:D})})}}_parseTransform(e){var t,s;if(!Ti(e))throw new M("cloudraster:open","the data contains unsupported geodata transform types");const r=_i(e);if(r.type==="identity")return null;if(r.type!=="polynomial"||!((t=r.forwardCoefficients)!=null&&t.length)||!((s=r.inverseCoefficients)!=null&&s.length))throw new M("cloudraster:open","the data contains unsupported geodata transforms - both forward and inverse coefficients are required currently");return r}async _fetchAuxiliaryInformation(e){const r=this.request(this.url+"/conf.vat.json",{signal:e}).then(a=>a.data).catch(()=>null),t=this.request(this.url+"/conf.vat.dbf",{responseType:"array-buffer",signal:e}).then(a=>a.data).catch(()=>null),s=await Promise.all([r,t]);let n;if(s[0]){let a=s[0].fields;const o=s[0].values;if(a&&o){a=a.map(c=>({type:c.name==="OID"?"esriFieldTypeOID":ie.get(c.type),name:c.name,alias:c.alias||c.name}));const l=o.map(c=>({attributes:c}));a&&o&&(n={fields:a,features:l})}}return!n&&s[1]&&(n=ut.parse(s[1]).recordSet),nt.fromJSON(n)}_buildCacheFilePath(e,r,t,s,n){const a=this._getPackageSize(!!n),o=Math.floor(r/a)*a,l=Math.floor(t/a)*a,c="R"+this._toHexString4(o)+"C"+this._toHexString4(l);let u="L";u+=e>=10?e.toString():"0"+e.toString();const{multidimensionalInfo:h}=this.rasterInfo,f=s==null?void 0:s[0];if($(h)||!f)return`${this.url}/_alllayers/${u}/${c}.bundle`;let d="_yxs";if(!n){d=h.variables.find(m=>m.name===f.variableName).dimensions[0].values.indexOf(f.values[0]).toString(16);const g=4-d.length;for(let m=0;m<g;m++)d="0"+d;d="S"+d}const p=this._getVariableFolderName(n||f.variableName);return`${this.url}/_alllayers/${p}/${d}/${u}/${c}.bundle`}_getPackageSize(e=!1){const{transposeInfo:r}=this.rasterInfo.storageInfo;return e&&F(r)?r.packetSize??0:this.storageInfo.packetSize}_getTileSize(e=!1){const{storageInfo:r}=this.rasterInfo,{transposeInfo:t}=r;return e&&F(t)?t.tileSize:r.tileInfo.size}_getVariableFolderName(e){return(e=e.trim())===""?"_v":e.replace(/[\{|\}\-]/g,"_").replace("\\*","_v")}_getIndexRecordFromBundle(e,r,t=!1){const s=this._getPackageSize(t),n=s*(e%s)+r%s;if(n<0)throw new Error("Invalid level / row / col");return 20+n*this.storageInfo.recordSize+44}_getTileEndAndContentType(e,r){const t=e.subarray(r,r+8);let s,n=0;for(s=0;s<5;s++)n|=(255&t[s])<<8*s;const a=0xffffffffff&n;for(n=0,s=5;s<8;s++)n|=(255&t[s])<<8*(s-5);return{position:a,recordSize:0xffffffffff&n}}_toHexString4(e){let r=e.toString(16);if(r.length!==4){let t=4-r.length;for(;t-- >0;)r="0"+r}return r}};w([S({readOnly:!0})],he.prototype,"storageInfo",void 0),w([S({type:String,json:{write:!0}})],he.prototype,"datasetFormat",void 0),he=w([Q("esri.layers.support.rasterDatasets.CloudRaster")],he);const Di=he;let fe=class extends ne{constructor(){super(...arguments),this.datasetFormat="MEMORY",this.data=null}async open(i){await this.init();const e=this.data,{pixelBlock:r,statistics:t,histograms:s,name:n,keyProperties:a,nativeExtent:o,transform:l}=this.data,{width:c,height:u,pixelType:h}=r,f=e.extent??new G({xmin:-.5,ymin:.5,xmax:c-.5,ymax:u-.5,spatialReference:new q({wkid:3857})}),d=e.isPseudoSpatialReference??!e.extent,p={x:f.width/c,y:f.height/u},g=new ge({width:c,height:u,pixelType:h,extent:f,nativeExtent:o,transform:l,pixelSize:p,spatialReference:f.spatialReference,bandCount:r.pixels.length,keyProperties:a||{},statistics:t,isPseudoSpatialReference:d,histograms:s});this.createRemoteDatasetStorageInfo(g,512,512),this._set("rasterInfo",g),this.updateTileInfo(),await this._buildInMemoryRaster(r,{width:512,height:512},i),this.datasetName=n,this.url="/InMemory/"+n}fetchRawTile(i,e,r,t={}){const s=this._pixelBlockTiles.get(`${i}/${e}/${r}`);return Promise.resolve(s)}async _buildInMemoryRaster(i,e,r){var c,u;const t=this.rasterInfo.storageInfo.maximumPyramidLevel,s=this.rasterJobHandler?this.rasterJobHandler.split({pixelBlock:i,tileSize:e,maximumPyramidLevel:t},r):Promise.resolve(xi(i,e,t)),n=F(this.rasterInfo.statistics),a=F(this.rasterInfo.histograms),o=n?Promise.resolve({statistics:null,histograms:null}):this.rasterJobHandler?this.rasterJobHandler.estimateStatisticsHistograms({pixelBlock:i},r):Promise.resolve(Zt(i)),l=await tt([s,o]);if(!l[0].value&&l[1].value)throw new M("inmemory-raster:open","failed to build in memory raster");this._pixelBlockTiles=l[0].value,n||(this.rasterInfo.statistics=(c=l[1].value)==null?void 0:c.statistics),a||(this.rasterInfo.histograms=(u=l[1].value)==null?void 0:u.histograms)}};w([S({type:String,json:{write:!0}})],fe.prototype,"datasetFormat",void 0),w([S()],fe.prototype,"data",void 0),fe=w([Q("esri.layers.support.rasterDatasets.InMemoryRaster")],fe);const Bi=fe;function oe(i,e){if(!i||!e)return[];let r=e;e.includes("/")?(r=e.slice(0,e.indexOf("/")),e=e.slice(e.indexOf("/")+1)):e="";const t=[];if(e){const n=oe(i,r);for(let a=0;a<n.length;a++)oe(n[a],e).forEach(o=>t.push(o));return t}const s=i.getElementsByTagNameNS("*",r);if(!s||s.length===0)return[];for(let n=0;n<s.length;n++)t.push(s[n]||s.item[n]);return t}function V(i,e){if(!i||!e)return null;let r=e;e.includes("/")?(r=e.slice(0,e.indexOf("/")),e=e.slice(e.indexOf("/")+1)):e="";const t=oe(i,r);return t.length>0?e?V(t[0],e):t[0]:null}function K(i,e=null){const r=e?V(i,e):i;let t;return r?(t=r.textContent||r.nodeValue,t?t.trim():null):null}function zi(i,e){const r=oe(i,e),t=[];let s;for(let n=0;n<r.length;n++)s=r[n].textContent||r[n].nodeValue,s&&(s=s.trim(),s!==""&&t.push(s));return t}function be(i,e){return zi(i,e).map(r=>Number(r))}function re(i,e){const r=K(i,e);return Number(r)}function ke(i,e){var s;const r=(s=i==null?void 0:i.nodeName)==null?void 0:s.toLowerCase(),t=e.toLowerCase();return r.slice(r.lastIndexOf(":")+1)===t}function Ze(i,e){if(!i||!e)return null;const r=[];for(let t=0;t<i.length;t++)r.push(i[t]),r.push(e[t]);return r}function Ei(i){const e=V(i,"GeodataXform"),r=ve(re(e,"SpatialReference/WKID")||K(e,"SpatialReference/WKT"));if(e.getAttribute("xsi:type")!=="typens:PolynomialXform")return{spatialReference:r,transform:null};const t=re(e,"PolynomialOrder")??1,s=be(e,"CoeffX/Double"),n=be(e,"CoeffY/Double"),a=be(e,"InverseCoeffX/Double"),o=be(e,"InverseCoeffY/Double"),l=Ze(s,n),c=Ze(a,o);return{spatialReference:r,transform:l&&c&&l.length&&c.length?new ze({spatialReference:r,polynomialOrder:t,forwardCoefficients:l,inverseCoefficients:c}):null}}function Ji(i){var f;const e=re(i,"NoDataValue"),r=V(i,"Histograms/HistItem"),t=re(r,"HistMin"),s=re(r,"HistMax"),n=re(r,"BucketCount"),a=(f=K(r,"HistCounts"))==null?void 0:f.split("|").map(d=>Number(d));let o,l,c,u;oe(i,"Metadata/MDI").forEach(d=>{const p=Number(d.textContent??d.nodeValue);switch(d.getAttribute("key").toUpperCase()){case"STATISTICS_MINIMUM":o=p;break;case"STATISTICS_MAXIMUM":l=p;break;case"STATISTICS_MEAN":c=p;break;case"STATISTICS_STDDEV":u=p}});const h=re(i,"Metadata/SourceBandIndex");return{noDataValue:e,histogram:a!=null&&a.length&&t!=null&&s!=null?{min:t,max:s,size:n||a.length,counts:a}:null,sourceBandIndex:h,statistics:o!=null&&l!=null?{min:o,max:l,avg:c,stddev:u}:null}}function ve(i){if(!i)return null;let e=Number(i);if(!isNaN(e)&&e!==0)return new q({wkid:e});if((i=String(i)).startsWith("COMPD_CS")){if(!i.includes("VERTCS")||!i.includes("GEOGCS")&&!i.startsWith("PROJCS"))return null;const r=i.indexOf("VERTCS"),t=i.indexOf("PROJCS"),s=t>-1?t:i.indexOf("GEOGCS");if(s===-1)return null;const n=i.slice(s,i.lastIndexOf("]",r)+1).trim(),a=i.slice(r,i.lastIndexOf("]")).trim();e=Ce(n);const o=new q(e?{wkid:e}:{wkt:n}),l=Ce(a);return l&&(o.vcsWkid=l),o}return i.startsWith("GEOGCS")||i.startsWith("PROJCS")?(e=Ce(i),new q(e!==0?{wkid:e}:{wkt:i})):null}function Ce(i){var s;const e=i.replace(/\]/g,"[").replace(/\"/g,"").split("[").map(n=>n.trim()).filter(n=>n!==""),r=e[e.length-1].split(","),t=(s=r[0])==null?void 0:s.toLowerCase();if((t==="epsg"||t==="esri")&&i.endsWith('"]]')){const n=Number(r[1]);if(!isNaN(n)&&n!==0)return n}return 0}function Ee(i){var t;if(((t=i==null?void 0:i.documentElement.tagName)==null?void 0:t.toLowerCase())!=="pamdataset")return{};const e={spatialReference:null,transform:null,metadata:{},rasterBands:[],statistics:null,histograms:null};i.documentElement.childNodes.forEach(s=>{if(s.nodeType===1){if(ke(s,"SRS")){if(!e.spatialReference){const n=K(s);e.spatialReference=ve(n)}}else if(ke(s,"Metadata"))if(s.getAttribute("domain")==="xml:ESRI"){const{spatialReference:n,transform:a}=Ei(s);e.transform=a,e.spatialReference||(e.spatialReference=n)}else oe(s,"MDI").forEach(n=>e.metadata[n.getAttribute("key")]=K(n));else if(ke(s,"PAMRasterBand")){const n=Ji(s);n.sourceBandIndex!=null&&e.rasterBands[n.sourceBandIndex]==null?e.rasterBands[n.sourceBandIndex]=n:e.rasterBands.push(n)}}});const r=e.rasterBands;if(r.length){const s=!!r[0].statistics;e.statistics=s?r.map(a=>a.statistics).filter(F):null;const n=!!r[0].histogram;e.histograms=n?r.map(a=>a.histogram).filter(F):null}return e}let Re=class extends ne{async open(i){await this.init();const e=await this._fetchData(i);let{spatialReference:r,statistics:t,histograms:s,transform:n}=await this._fetchAuxiliaryData(i);const a=!r;a&&(r=new q({wkid:3857})),s!=null&&s.length&&t==null&&(t=Be(s));const{width:o,height:l}=e;let c=new G({xmin:-.5,ymin:.5-l,xmax:o-.5,ymax:.5,spatialReference:r});const u=n?n.forwardTransform(c):c;let h=!0;if(n){const d=n.forwardCoefficients;h=d&&d[1]===0&&d[2]===0,h&&(n=null,c=u)}const f=new Bi({data:{extent:u,nativeExtent:c,transform:n,pixelBlock:e,statistics:t,histograms:s,keyProperties:{DateType:"Processed"},isPseudoSpatialReference:a}});await f.open(),f.data=null,this._set("rasterInfo",f.rasterInfo),this._inMemoryRaster=f}fetchRawTile(i,e,r,t={}){return this._inMemoryRaster.fetchRawTile(i,e,r,t)}async _fetchData(i){const{data:e}=await this.request(this.url,{responseType:"array-buffer",signal:i==null?void 0:i.signal}),r=ei(e).toUpperCase();if(r!=="JPG"&&r!=="PNG"&&r!=="GIF"&&r!=="BMP")throw new M("image-aux-raster:open","the data is not a supported format");this._set("datasetFormat",r);const t=r.toLowerCase(),s=t==="gif"||t==="bmp"||!Lt("ios"),n=await this.decodePixelBlock(e,{format:t,useCanvas:s,hasNoZlibMask:!0});if(n==null)throw new M("image-aux-raster:open","the data cannot be decoded");return n}async _fetchAuxiliaryData(i){var c;const e=X(i==null?void 0:i.signal),r=this.ioConfig.skipExtensions??[],t=r.includes("aux.xml")?null:this.request(this.url+".aux.xml",{responseType:"xml",signal:e}),s=this.datasetFormat,n=s==="JPG"?"jgw":s==="PNG"?"pgw":s==="BMP"?"bpw":null,a=n&&r.includes(n)?null:this.request(this.url.slice(0,this.url.lastIndexOf("."))+"."+n,{responseType:"text",signal:e}),o=await tt([t,a]);if(e!=null&&e.aborted)throw mt();const l=Ee((c=o[0].value)==null?void 0:c.data);if(!l.transform){const u=o[1].value?o[1].value.data.split(`
`).slice(0,6).map(h=>Number(h)):null;l.transform=(u==null?void 0:u.length)===6?new ze({forwardCoefficients:[u[4],u[5],u[0],-u[1],u[2],-u[3]]}):null}return l}};w([S({type:String,json:{write:!0}})],Re.prototype,"datasetFormat",void 0),Re=w([Q("esri.layers.support.rasterDatasets.ImageAuxRaster")],Re);const Se=Re;let de=class extends ne{constructor(){super(...arguments),this._levelOffset=0,this._tilemapCache=null,this._slices=null,this.datasetFormat="RasterTileServer",this.tileType=null}async open(e){var T,R;await this.init();const r=e&&e.signal,t=this.sourceJSON?{data:this.sourceJSON}:await this.request(this.url,{query:{f:"json"},signal:r});t.ssl&&(this.url=this.url.replace(/^http:/i,"https:"));const s=t.data;if(this.sourceJSON=s,!s)throw new M("imageserverraster:open","cannot initialize tiled image service, missing service info");if(!s.tileInfo)throw new M("imageserverraster:open","use ImageryLayer to open non-tiled image services");this._fixScaleInServiceInfo();const n=["jpg","jpeg","png","png8","png24","png32","mixed"];this.tileType=s.cacheType,this.tileType==null&&(n.includes(s.tileInfo.format.toLowerCase())?this.tileType="Map":s.tileInfo.format.toLowerCase()==="lerc"?this.tileType="Elevation":this.tileType="Raster"),this.datasetName=((T=s.name)==null?void 0:T.slice(s.name.indexOf("/")+1))??"";const a=await this._fetchRasterInfo({signal:r});if($(a))throw new M("image-server-raster:open","cannot initialize image service");const o=this.tileType==="Map"?Ci(s.tileInfo,s):pe.fromJSON(s.tileInfo);At(o);const[l,c]=this._computeMinMaxLOD(a,o),{extent:u,pixelSize:h}=a,f=.5/a.width*h.x,d=Math.max(h.x,h.y),{lods:p}=o;(this.tileType!=="Map"&&s.maxScale!==0||Math.abs(h.x-h.y)>f||!p.some(b=>Math.abs(b.resolution-d)<f))&&(h.x=h.y=l.resolution,a.width=Math.ceil((u.xmax-u.xmin)/h.x-.1),a.height=Math.ceil((u.ymax-u.ymin)/h.y-.1));const g=l.level-c.level,[m,y]=o.size,x=[],I=[];p.forEach((b,P)=>{b.level>=c.level&&b.level<=l.level&&x.push({x:b.resolution,y:b.resolution}),P<p.length-1&&I.push(Math.round(10*b.resolution/p[P+1].resolution)/10)}),x.sort((b,P)=>b.x-P.x);const v=this.computeBlockBoundary(u,m,y,o.origin,x,g),k=x.length>1?x.slice(1):null;let _;s.transposeInfo&&(_={tileSize:[s.transposeInfo.rows,s.transposeInfo.cols],packetSize:((R=a.keyProperties)==null?void 0:R._yxs.PacketSize)??0});const C=I.length<=1||I.length>=3&&I.slice(0,I.length-1).every(b=>b===I[0])?I[0]??2:Math.round(10/(c.resolution/l.resolution)**(-1/g))/10;if(a.storageInfo=new ye({blockWidth:o.size[0],blockHeight:o.size[1],pyramidBlockWidth:o.size[0],pyramidBlockHeight:o.size[1],pyramidResolutions:k,pyramidScalingFactor:C,compression:o.format,origin:o.origin,firstPyramidLevel:1,maximumPyramidLevel:g,tileInfo:o,transposeInfo:_,blockBoundary:v}),this._fixGCSShift(a),this._set("rasterInfo",a),s.capabilities.toLowerCase().includes("tilemap")){const b={tileInfo:a.storageInfo.tileInfo,parsedUrl:jt(this.url),url:this.url,tileServers:[],type:"tile"};this._tilemapCache=new Pi({layer:b})}}async fetchRawTile(e,r,t,s={}){const{storageInfo:n,extent:a}=this.rasterInfo,{transposeInfo:o}=n,l=F(o)&&!!s.transposedVariableName;if(this._slices&&!l&&s.sliceId==null)return null;const c=l?0:n.maximumPyramidLevel-e+this._levelOffset,u=`${this.url}/tile/${c}/${r}/${t}`,h=this._slices?l?{variable:s.transposedVariableName}:{sliceId:s.sliceId||0}:null,{data:f}=await this.request(u,{query:h,responseType:"array-buffer",signal:s.signal});if(!f)return null;const d=l?o.tileSize:n.tileInfo.size,p=await this.decodePixelBlock(f,{width:d[0],height:d[1],planes:null,pixelType:null,isPoint:this.tileType==="Elevation",returnInterleaved:l,noDataValue:X(this.rasterInfo.noDataValue)});if(p==null)return null;const g=n.blockBoundary[e];if(n.compression!=="jpg"||t>g.minCol&&t<g.maxCol&&r>g.minRow&&r<g.maxRow)return p;const{origin:m,blockWidth:y,blockHeight:x}=n,{x:I,y:v}=this.getPyramidPixelSize(e),k=Math.round((a.xmin-m.x)/I)%y,_=Math.round((a.xmax-m.x)/I)%y||y,C=Math.round((m.y-a.ymax)/v)%x,T=Math.round((m.y-a.ymin)/v)%x||x,R=t===g.minCol?k:0,b=r===g.minRow?C:0,P=t===g.maxCol?_:y,O=r===g.maxRow?T:x;return Ii(p,{x:R,y:b},{width:P-R,height:O-b}),p}getSliceIndex(e){if(!this._slices||$(e)||e.length===0)return null;const r=e;for(let t=0;t<this._slices.length;t++){const s=this._slices[t].multidimensionalDefinition;if(s.length===r.length&&!s.some(n=>{const a=r.find(o=>n.variableName===o.variableName&&o.dimensionName===n.dimensionName);return a?(Array.isArray(n.values[0])?`${n.values[0][0]}-${n.values[0][1]}`:n.values[0])!==(Array.isArray(a.values[0])?`${a.values[0][0]}-${a.values[0][1]}`:a.values[0]):!0}))return t}return null}async fetchVariableStatisticsHistograms(e,r){const t=this.request(this.url+"/statistics",{query:{variable:e,f:"json"},signal:r}).then(a=>{var o;return(o=a.data)==null?void 0:o.statistics}),s=this.request(this.url+"/histograms",{query:{variable:e,f:"json"},signal:r}).then(a=>{var o;return(o=a.data)==null?void 0:o.histograms}),n=await Promise.all([t,s]);return n[0]&&n[0].forEach(a=>{a.avg=a.mean,a.stddev=a.standardDeviation}),{statistics:n[0]||null,histograms:n[1]||null}}async computeBestPyramidLevelForLocation(e,r={}){if(!this._tilemapCache)return 0;let t=this.identifyPixelLocation(e,0,X(r.datumTransformation));if(t===null)return null;let s=0;const{maximumPyramidLevel:n}=this.rasterInfo.storageInfo;let a=n-s+this._levelOffset;const o=t.srcLocation;for(;a>=0;){try{if(await this._tilemapCache.fetchAvailability(a,t.row,t.col,r)==="available")break}catch{}if(a--,s++,t=this.identifyPixelLocation(o,s,X(r.datumTransformation)),t===null)return null}return a===-1||t==null?null:s}async _fetchRasterInfo(e){const r=this.sourceJSON;if(this.tileType==="Map"){const o=r.fullExtent||r.extent,l=Math.ceil((o.xmax-o.xmin)/r.pixelSizeX-.1),c=Math.ceil((o.ymax-o.ymin)/r.pixelSizeY-.1),u=q.fromJSON(r.spatialReference||o.spatialReference),h=new W({x:r.pixelSizeX,y:r.pixelSizeY,spatialReference:u});return new ge({width:l,height:c,bandCount:3,extent:G.fromJSON(o),spatialReference:u,pixelSize:h,pixelType:"u8",statistics:null,keyProperties:{DataType:"processed"}})}const{signal:t}=e,s=zt(this.url,this.sourceJSON,{signal:t,query:this.ioConfig.customFetchParameters}),n=r.hasMultidimensions?this.request(`${this.url}/slices`,{query:{f:"json"},signal:t}).then(o=>o.data&&o.data.slices).catch(()=>null):null,a=await Promise.all([s,n]);return this._slices=a[1],a[0]}_fixScaleInServiceInfo(){const{sourceJSON:e}=this;e.minScale&&e.minScale<0&&(e.minScale=0),e.maxScale&&e.maxScale<0&&(e.maxScale=0)}_fixGCSShift(e){const{extent:r,spatialReference:t}=e;r.xmin>-1&&r.xmax>181&&(t!=null&&t.wkid)&&t.isGeographic&&(e.nativeExtent=e.extent,e.transform=new Fi,e.extent=e.transform.forwardTransform(r))}_computeMinMaxLOD(e,r){const{pixelSize:t}=e,s=.5/e.width*t.x,{lods:n}=r,a=r.lodAt(Math.max.apply(null,n.map(d=>d.level))),o=r.lodAt(Math.min.apply(null,n.map(d=>d.level))),{tileType:l}=this;if(l==="Map")return this._levelOffset=n[0].level,[a,o];if(l==="Raster")return[n.find(d=>d.resolution===t.x)??a,o];const{minScale:c,maxScale:u}=this.sourceJSON;let h=a;u>0&&(h=n.find(d=>Math.abs(d.scale-u)<s),h||(h=n.filter(d=>d.scale>u).sort((d,p)=>d.scale>p.scale?1:-1)[0]??a));let f=o;return c>0&&(f=n.find(d=>Math.abs(d.scale-c)<s)??o,this._levelOffset=f.level-o.level),[h,f]}};w([S({type:String,json:{write:!0}})],de.prototype,"datasetFormat",void 0),w([S()],de.prototype,"tileType",void 0),de=w([Q("esri.layers.support.rasterDatasets.ImageServerRaster")],de);const Ni=de,Y=new Map;Y.set("Int8","s8"),Y.set("UInt8","u8"),Y.set("Int16","s16"),Y.set("UInt16","u16"),Y.set("Int32","s32"),Y.set("UInt32","u32"),Y.set("Float32","f32"),Y.set("Float64","f32"),Y.set("Double64","f32");const ee=new Map;ee.set("none",{blobExtension:".til",isOneSegment:!0,decoderFormat:"bip"}),ee.set("lerc",{blobExtension:".lrc",isOneSegment:!1,decoderFormat:"lerc"}),ee.set("deflate",{blobExtension:".pzp",isOneSegment:!0,decoderFormat:"deflate"}),ee.set("jpeg",{blobExtension:".pjg",isOneSegment:!0,decoderFormat:"jpg"});let ae=class extends ne{constructor(){super(...arguments),this._files=null,this._storageIndex=null,this.datasetFormat="MRF"}async open(i){var g;await this.init(),this.datasetName=this.url.slice(this.url.lastIndexOf("/")+1);const e=i?X(i.signal):null,r=await this.request(this.url,{responseType:"xml",signal:e}),{rasterInfo:t,files:s}=this._parseHeader(r.data);if(((g=this.ioConfig.skipExtensions)==null?void 0:g.indexOf("aux.xml"))===-1){const m=await this._fetchAuxiliaryData(i);m!=null&&(t.statistics=m.statistics??t.statistics,t.histograms=m.histograms,m.histograms&&$(t.statistics)&&(t.statistics=Be(m.histograms)))}this._set("rasterInfo",t),this._files=s;const n=await this.request(s.index,{responseType:"array-buffer",signal:e});this._storageIndex=this._parseIndex(n.data);const{blockWidth:a,blockHeight:o}=this.rasterInfo.storageInfo,l=this.rasterInfo.storageInfo.pyramidScalingFactor,{width:c,height:u}=this.rasterInfo,h=[],f=this._getBandSegmentCount();let d=0,p=-1;for(;d<this._storageIndex.length;){p++;const m=Math.ceil(c/a/l**p)-1,y=Math.ceil(u/o/l**p)-1;d+=(m+1)*(y+1)*f*4,h.push({maxRow:y,maxCol:m,minCol:0,minRow:0})}this.rasterInfo.storageInfo.blockBoundary=h,p>0&&(this.rasterInfo.storageInfo.firstPyramidLevel=1,this.rasterInfo.storageInfo.maximumPyramidLevel=p),this.updateTileInfo()}async fetchRawTile(i,e,r,t={}){const{blockWidth:s,blockHeight:n,blockBoundary:a}=this.rasterInfo.storageInfo,o=a[i];if(!o||o.maxRow<e||o.maxCol<r||o.minRow>e||o.minCol>r)return null;const{bandCount:l,pixelType:c}=this.rasterInfo,{ranges:u,actualTileWidth:h,actualTileHeight:f}=this._getTileLocation(i,e,r);if(!u||u.length===0)return null;if(u[0].from===0&&u[0].to===0){const R=new Uint8Array(s*n);return new wi({width:s,height:n,pixels:null,mask:R,validPixelCount:0})}const{bandIds:d}=this.ioConfig,p=this._getBandSegmentCount(),g=[];let m=0;for(m=0;m<p;m++)(!d||d.indexOf[m]>-1)&&g.push(this.request(this._files.data,{range:{from:u[m].from,to:u[m].to},responseType:"array-buffer",signal:t.signal}));const y=await Promise.all(g),x=y.map(R=>R.data.byteLength).reduce((R,b)=>R+b),I=new Uint8Array(x);let v=0;for(m=0;m<p;m++)I.set(new Uint8Array(y[m].data),v),v+=y[m].data.byteLength;const k=ee.get(this.rasterInfo.storageInfo.compression).decoderFormat,_=await this.decodePixelBlock(I.buffer,{width:s,height:n,format:k,planes:(d==null?void 0:d.length)||l,pixelType:c});if(_==null)return null;if(F(this.rasterInfo.noDataValue)&&k!=="lerc"&&!_.mask){const R=this.rasterInfo.noDataValue[0];if(R!=null){const b=_.width*_.height,P=new Uint8Array(b);if(Math.abs(R)>1e24)for(m=0;m<b;m++)Math.abs((_.pixels[0][m]-R)/R)>1e-6&&(P[m]=1);else for(m=0;m<b;m++)_.pixels[0][m]!==R&&(P[m]=1);_.mask=P}}let C=0,T=0;if(h!==s||f!==n){let R=_.mask;if(R)for(m=0;m<n;m++)if(T=m*s,m<f)for(C=h;C<s;C++)R[T+C]=0;else for(C=0;C<s;C++)R[T+C]=0;else for(R=new Uint8Array(s*n),_.mask=R,m=0;m<f;m++)for(T=m*s,C=0;C<h;C++)R[T+C]=1}return _}_parseIndex(i){if(i.byteLength%16>0)throw new Error("invalid array buffer must be multiples of 16");let e,r,t,s,n,a;if(ti){for(r=new Uint8Array(i),s=new ArrayBuffer(i.byteLength),t=new Uint8Array(s),n=0;n<i.byteLength/4;n++)for(a=0;a<4;a++)t[4*n+a]=r[4*n+3-a];e=new Uint32Array(s)}else e=new Uint32Array(i);return e}_getBandSegmentCount(){return ee.get(this.rasterInfo.storageInfo.compression).isOneSegment?1:this.rasterInfo.bandCount}_getTileLocation(i,e,r){const{blockWidth:t,blockHeight:s,pyramidScalingFactor:n}=this.rasterInfo.storageInfo,{width:a,height:o}=this.rasterInfo,l=this._getBandSegmentCount();let c,u,h,f=0,d=0;for(h=0;h<i;h++)d=n**h,c=Math.ceil(a/t/d),u=Math.ceil(o/s/d),f+=c*u;d=n**i,c=Math.ceil(a/t/d),u=Math.ceil(o/s/d),f+=e*c+r,f*=4*l;const p=this._storageIndex.subarray(f,f+4*l);let g=0,m=0;const y=[];for(let x=0;x<l;x++)g=p[4*x+0]*2**32+p[4*x+1],m=g+p[4*x+2]*2**32+p[4*x+3],y.push({from:g,to:m});return{ranges:y,actualTileWidth:r<c-1?t:Math.ceil(a/d)-t*(c-1),actualTileHeight:e<u-1?s:Math.ceil(o/d)-s*(u-1)}}_parseHeader(i){const e=V(i,"MRF_META/Raster");if(!e)throw new M("mrf:open","not a valid MRF format");const r=V(e,"Size"),t=parseInt(r.getAttribute("x"),10),s=parseInt(r.getAttribute("y"),10),n=parseInt(r.getAttribute("c"),10),a=(K(e,"Compression")||"none").toLowerCase();if(!ee.has(a))throw new M("mrf:open","currently does not support compression "+a);const o=K(e,"DataType")||"UInt8",l=Y.get(o);if(l==null)throw new M("mrf:open","currently does not support pixel type "+o);const c=V(e,"PageSize"),u=parseInt(c.getAttribute("x"),10),h=parseInt(c.getAttribute("y"),10),f=V(e,"DataValues");let d,p;if(f&&(p=f.getAttribute("NoData"),p!=null&&(d=p.trim().split(" ").map(P=>parseFloat(P)))),V(i,"MRF_META/CachedSource"))throw new M("mrf:open","currently does not support MRF referencing other data files");const g=V(i,"MRF_META/GeoTags"),m=V(g,"BoundingBox");let y,x=!1;if(m!=null){const P=parseFloat(m.getAttribute("minx")),O=parseFloat(m.getAttribute("miny")),H=parseFloat(m.getAttribute("maxx")),B=parseFloat(m.getAttribute("maxy")),D=K(g,"Projection")||"";let N=q.WGS84;if(D!=="LOCAL_CS[]")if(D.toLowerCase().startsWith("epsg:")){const L=Number(D.slice(5));isNaN(L)||L===0||(N=new q({wkid:L}))}else N=ve(D)??q.WGS84;else x=!0,N=new q({wkid:3857});y=new G(P,O,H,B),y.spatialReference=N}else x=!0,y=new G({xmin:-.5,ymin:.5-s,xmax:t-.5,ymax:.5,spatialReference:new q({wkid:3857})});const I=V(i,"MRF_META/Rsets"),v=parseInt(I&&I.getAttribute("scale")||"2",10),k=y.spatialReference,_=new ye({origin:new W({x:y.xmin,y:y.ymax,spatialReference:k}),blockWidth:u,blockHeight:h,pyramidBlockWidth:u,pyramidBlockHeight:h,compression:a,pyramidScalingFactor:v}),C=new W({x:y.width/t,y:y.height/s,spatialReference:k}),T=new ge({width:t,height:s,extent:y,isPseudoSpatialReference:x,spatialReference:k,bandCount:n,pixelType:l,pixelSize:C,noDataValue:d,storageInfo:_}),R=K(i,"datafile"),b=K(i,"IndexFile");return{rasterInfo:T,files:{mrf:this.url,index:b||this.url.replace(".mrf",".idx"),data:R||this.url.replace(".mrf",ee.get(a).blobExtension)}}}async _fetchAuxiliaryData(i){try{const{data:e}=await this.request(this.url+".aux.xml",{responseType:"xml",signal:i==null?void 0:i.signal});return Ee(e)}catch{return null}}};w([S()],ae.prototype,"_files",void 0),w([S()],ae.prototype,"_storageIndex",void 0),w([S({type:String,json:{write:!0}})],ae.prototype,"datasetFormat",void 0),ae=w([Q("esri.layers.support.rasterIO.MRFRaster")],ae);const Hi=ae,Pe=(i,e)=>{var r;return(r=i.get(e))==null?void 0:r.values},ue=(i,e)=>{var r,t;return(t=(r=i.get(e))==null?void 0:r.values)==null?void 0:t[0]};let se=class extends ne{constructor(){super(...arguments),this._files=null,this._headerInfo=null,this._bufferSize=1048576,this.datasetFormat="TIFF"}async open(i){await this.init();const e=i?X(i.signal):null,{data:r}=await this.request(this.url,{range:{from:0,to:this._bufferSize},responseType:"array-buffer",signal:e});if(!r)throw new M("tiffraster:open","failed to open url "+this.url);this.datasetName=this.url.slice(this.url.lastIndexOf("/")+1,this.url.lastIndexOf("."));const{littleEndian:t,firstIFDPos:s,isBigTiff:n}=ii(r),a=[];await this._readIFDs(a,r,t,s,0,n?8:4,e);const{imageInfo:o,rasterInfo:l}=this._parseIFDs(a),c=si(a),u=ri(a);if(this._headerInfo={littleEndian:t,isBigTiff:n,ifds:a,pyramidIFDs:c,maskIFDs:u,...o},this._set("rasterInfo",l),!o.isSupported)throw new M("tiffraster:open","this tiff is not supported: "+o.message);if(!o.tileWidth)throw new M("tiffraster:open","none-tiled tiff is not optimized for access, convert to COG and retry.");const{skipExtensions:h=[]}=this.ioConfig;if(!h.includes("aux.xml")){const f=await this._fetchAuxiliaryMetaData(i);f!=null&&this._processPAMInfo(f,l)}h.includes("vat.dbf")||l.bandCount!==1||l.pixelType!=="u8"||(l.attributeTable=await this._fetchAuxiliaryTable(i),F(l.attributeTable)&&(l.keyProperties.DataType="thematic")),this.updateTileInfo()}async fetchRawTile(i,e,r,t={}){var n;if(!((n=this._headerInfo)!=null&&n.isSupported)||this.isBlockOutside(i,e,r))return null;const s=await this._fetchRawTiffTile(i,e,r,!1,t);if(F(s)&&this._headerInfo.hasMaskBand){const a=await this._fetchRawTiffTile(i,e,r,!0,t);F(a)&&a.pixels[0]instanceof Uint8Array&&(s.mask=a.pixels[0])}return s}_parseIFDs(i){var O,H;const e=ni(i),{width:r,height:t,tileWidth:s,tileHeight:n,planes:a,pixelType:o,compression:l,firstPyramidLevel:c,maximumPyramidLevel:u,pyramidBlockWidth:h,pyramidBlockHeight:f,tileBoundary:d,affine:p,metadata:g}=e,m=((O=e.extent.spatialReference)==null?void 0:O.wkt)||((H=e.extent.spatialReference)==null?void 0:H.wkid);let y=ve(m),x=!!e.isPseudoGeographic;y==null&&(x=!0,y=new q({wkid:3857}));const I=new G({...e.extent,spatialReference:y}),v=new W(I?{x:I.xmin,y:I.ymax,spatialReference:y}:{x:0,y:0}),k=new ye({blockWidth:s,blockHeight:n,pyramidBlockWidth:h,pyramidBlockHeight:f,compression:l,origin:v,firstPyramidLevel:c,maximumPyramidLevel:u,blockBoundary:d}),_=new W({x:(I.xmax-I.xmin)/r,y:(I.ymax-I.ymin)/t,spatialReference:y}),C=g?{BandProperties:g.bandProperties,DataType:g.dataType}:{};let T=null;const R=ue(i[0],"PHOTOMETRICINTERPRETATION"),b=Pe(i[0],"COLORMAP");if(R<=3&&(b==null?void 0:b.length)>3&&b.length%3==0){T=[];const B=b.length/3;for(let D=0;D<B;D++)T.push([D,b[D]>>>8,b[D+B]>>>8,b[D+2*B]>>>8])}const P=new ge({width:r,height:t,bandCount:a,pixelType:o,pixelSize:_,storageInfo:k,spatialReference:y,isPseudoSpatialReference:x,keyProperties:C,extent:I,colormap:T,statistics:g?g.statistics:null});return p!=null&&p.length&&(P.nativeExtent=new G({xmin:-.5,ymin:.5-t,xmax:r-.5,ymax:.5,spatialReference:y}),P.transform=new ze({polynomialOrder:1,forwardCoefficients:[p[2]+p[0]/2,p[5]-p[3]/2,p[0],p[3],-p[1],-p[4]]}),P.extent=P.transform.forwardTransform(P.nativeExtent),P.pixelSize=new W({x:(I.xmax-I.xmin)/r,y:(I.ymax-I.ymin)/t,spatialReference:y}),k.origin.x=-.5,k.origin.y=.5),{imageInfo:e,rasterInfo:P}}_processPAMInfo(i,e){if(e.statistics=i.statistics??e.statistics,e.histograms=i.histograms,i.histograms&&$(e.statistics)&&(e.statistics=Be(i.histograms)),i.transform&&$(e.transform)){e.transform=i.transform,e.nativeExtent=e.extent;const r=e.transform.forwardTransform(e.nativeExtent);e.pixelSize=new W({x:(r.xmax-r.xmin)/e.width,y:(r.ymax-r.ymin)/e.height,spatialReference:e.spatialReference}),e.extent=r}e.isPseudoSpatialReference&&i.spatialReference&&(e.spatialReference=i.spatialReference)}async _readIFDs(i,e,r,t,s,n=4,a){if(!t)return null;(t>=e.byteLength||t<0)&&(e=(await this.request(this.url,{range:{from:t+s,to:t+s+this._bufferSize},responseType:"array-buffer",signal:a})).data,s=t+s,t=0);const o=await this._readIFD(e,r,t,s,Ie.TIFF_TAGS,n,a);if(i.push(o.ifd),!o.nextIFD)return null;await this._readIFDs(i,e,r,o.nextIFD-s,s,n,a)}async _readIFD(i,e,r,t,s=Ie.TIFF_TAGS,n=4,a){var l,c;if(!i)return null;const o=ai(i,e,r,t,s,n);if(o.success){const u=[];if((l=o.ifd)==null||l.forEach(h=>{h.values||u.push(h)}),u.length>0){const h=u.map(d=>d.offlineOffsetSize).filter(F),f=Math.min.apply(null,h.map(d=>d[0]));if(Math.min.apply(null,h.map(d=>d[0]+d[1]))-f<=this._bufferSize){const{data:d}=await this.request(this.url,{range:{from:f,to:f+this._bufferSize},responseType:"array-buffer",signal:a});i=d,t=f,u.forEach(p=>oi(i,e,p,t))}}if((c=o.ifd)!=null&&c.has("GEOKEYDIRECTORY")){const h=o.ifd.get("GEOKEYDIRECTORY"),f=h==null?void 0:h.values;if(f&&f.length>4){const d=f[0]+"."+f[1]+"."+f[2],p=await this._readIFD(i,e,h.valueOffset+6-t,t,Ie.GEO_KEYS,2,a);h.data=p.ifd,h.data&&h.data.set("GEOTIFFVersion",{id:0,type:2,valueCount:1,valueOffset:null,values:[d]})}}return o}if(o.requiredBufferSize&&o.requiredBufferSize!==i.byteLength)return(i=(await this.request(this.url,{range:{from:t,to:t+o.requiredBufferSize+4},responseType:"array-buffer",signal:a})).data).byteLength<o.requiredBufferSize?null:this._readIFD(i,e,0,t,Ie.TIFF_TAGS,4,a)}async _fetchRawTiffTile(i,e,r,t,s={}){const n=this._getTileLocation(i,e,r,t);if(!n)return null;const{ranges:a,actualTileWidth:o,actualTileHeight:l,ifd:c}=n,u=a.map(_=>this.request(this.url,{range:_,responseType:"array-buffer",signal:s.signal})),h=await Promise.all(u),f=h.map(_=>_.data.byteLength).reduce((_,C)=>_+C),d=h.length===1?h[0].data:new ArrayBuffer(f),p=[0],g=[0];if(h.length>1){const _=new Uint8Array(d);for(let C=0,T=0;C<h.length;C++){const R=h[C].data;_.set(new Uint8Array(R),T),p[C]=T,T+=R.byteLength,g[C]=R.byteLength}}const{blockWidth:m,blockHeight:y}=this.getBlockWidthHeight(i),x=await this.decodePixelBlock(d,{format:"tiff",customOptions:{headerInfo:this._headerInfo,ifd:c,offsets:p,sizes:g},width:m,height:y,planes:null,pixelType:null});if(x==null)return null;let I,v,k;if(o!==m||l!==y){let _=x.mask;if(_)for(I=0;I<y;I++)if(k=I*m,I<l)for(v=o;v<m;v++)_[k+v]=0;else for(v=0;v<m;v++)_[k+v]=0;else for(_=new Uint8Array(m*y),x.mask=_,I=0;I<l;I++)for(k=I*m,v=0;v<o;v++)_[k+v]=1}return x}_getTileLocation(i,e,r,t=!1){const{firstPyramidLevel:s,blockBoundary:n}=this.rasterInfo.storageInfo,a=i===0?0:i-(s-1),{_headerInfo:o}=this;if(!o)return null;const l=t?o.maskIFDs[a]:a===0?o==null?void 0:o.ifds[0]:o==null?void 0:o.pyramidIFDs[a-1];if(!l)return null;const c=li(l,o),u=Pe(l,"TILEOFFSETS");if(u===void 0)return null;const h=Pe(l,"TILEBYTECOUNTS"),{minRow:f,minCol:d,maxRow:p,maxCol:g}=n[a];if(e>p||r>g||e<f||r<d)return null;const m=ue(l,"IMAGEWIDTH"),y=ue(l,"IMAGELENGTH"),x=ue(l,"TILEWIDTH"),I=ue(l,"TILELENGTH"),v=c?this.rasterInfo.bandCount:1,k=v*e*(g+1)+r,_=[{from:u[k],to:u[k+v-1]+h[k+v-1]-1}];if(c){let R=!0;for(let b=0;b<v;b++)if(u[k+b]+h[k+b]!==u[k+b+1]){R=!1;break}if(!R)for(let b=0;b<v;b++)_[b]={from:u[k+b],to:u[k+b]+h[k+b]-1}}const C=u[k],T=h[k];return C==null||T==null?null:{ranges:_,ifd:l,actualTileWidth:r===g&&m%x||x,actualTileHeight:e===p&&y%I||I}}async _fetchAuxiliaryMetaData(i){try{const{data:e}=await this.request(this.url+".aux.xml",{responseType:"xml",signal:i==null?void 0:i.signal});return Ee(e)}catch{return null}}async _fetchAuxiliaryTable(i){try{const{data:e}=await this.request(this.url+".vat.dbf",{responseType:"array-buffer",signal:i==null?void 0:i.signal}),r=ut.parse(e);return r!=null&&r.recordSet?nt.fromJSON(r.recordSet):null}catch{return null}}};w([S()],se.prototype,"_files",void 0),w([S()],se.prototype,"_headerInfo",void 0),w([S()],se.prototype,"_bufferSize",void 0),w([S({type:String,json:{write:!0}})],se.prototype,"datasetFormat",void 0),se=w([Q("esri.layers.support.rasterDatasets.TIFFRaster")],se);const Li=se,A=new Map;A.set("CRF",{desc:"Cloud Raster Format",constructor:Di}),A.set("MRF",{desc:"Meta Raster Format",constructor:Hi}),A.set("TIFF",{desc:"GeoTIFF",constructor:Li}),A.set("RasterTileServer",{desc:"Raster Tile Server",constructor:Ni}),A.set("JPG",{desc:"JPG Raster Format",constructor:Se}),A.set("PNG",{desc:"PNG Raster Format",constructor:Se}),A.set("GIF",{desc:"GIF Raster Format",constructor:Se}),A.set("BMP",{desc:"BMP Raster Format",constructor:Se});class Ai{static get supportedFormats(){const e=new Set;return A.forEach((r,t)=>e.add(t)),e}static async open(e){const{url:r,ioConfig:t,sourceJSON:s}=e;let n=e.datasetFormat;n==null&&r.lastIndexOf(".")&&(n=r.slice(r.lastIndexOf(".")+1).toUpperCase()),n==="OVR"||n==="TIF"?n="TIFF":n!=="JPG"&&n!=="JPEG"&&n!=="JFIF"||(n="JPG"),r.toLowerCase().includes("/imageserver")&&!r.toLowerCase().includes("/wcsserver")&&(n="RasterTileServer");const a={url:r,sourceJSON:s,datasetFormat:n,ioConfig:t??{bandIds:null,sampling:null}};let o,l;if(n&&this.supportedFormats.has(n)){if(n==="CRF"&&!(t!=null&&t.enableCRF))throw new M("rasterfactory:open",`cannot open raster: ${r}`);return o=A.get(n).constructor,l=new o(a),await l.open({signal:e.signal}),l}if(n)throw new M("rasterfactory:open","not a supported format "+n);const c=Array.from(A.keys());let u=0;const h=()=>(n=c[u++],n&&(n!=="CRF"||t!=null&&t.enableCRF)?(o=A.get(n).constructor,l=new o(a),l.open({signal:e.signal}).then(()=>l).catch(()=>h())):null);return h()}static register(e,r,t){A.has(e.toUpperCase())||A.set(e.toUpperCase(),{desc:r,constructor:t})}}let E=class extends xt(It(wt(bt(St($i(Rt(vt(Tt(_t(Mt)))))))))){constructor(...i){super(...i),this._primaryRasters=null,this.bandIds=null,this.interpolation=null,this.legendEnabled=!0,this.isReference=null,this.listMode="show",this.sourceJSON=null,this.version=null,this.type="imagery-tile",this.operationalLayerType="ArcGISTiledImageServiceLayer",this.popupEnabled=!0,this.popupTemplate=null,this.fields=null}normalizeCtorArgs(i,e){return typeof i=="string"?{url:i,...e}:i}load(i){const e=F(i)?i.signal:null;return this.addResolvingPromise(this.loadFromPortal({supportedTypes:["Image Service"]},i).catch(pt).then(()=>this._openRaster(e))),Promise.resolve(this)}get defaultPopupTemplate(){return this.createPopupTemplate()}get rasterFields(){var o;let i=[new me({name:"Raster.ServicePixelValue",alias:"Pixel Value",domain:null,editable:!1,length:50,type:"string"})];const{rasterInfo:e}=this,r=e==null?void 0:e.attributeTable,t=F(r)?r.fields:null,s="Raster.";if(t){const l=t.filter(c=>c.type!=="oid"&&c.name.toLowerCase()!=="value").map(c=>{const u=c.clone();return u.name=s+c.name,u});i=i.concat(l)}const n=e==null?void 0:e.dataType,a=e==null?void 0:e.multidimensionalInfo;if((n==="vector-magdir"||n==="vector-uv")&&F(a)){const l=(o=a.variables[0].unit)==null?void 0:o.trim(),c="Magnitude"+(l?` (${l})`:"");i.push(new me({name:"Raster.Magnitude",alias:c,domain:null,editable:!1,type:"double"})),i.push(new me({name:"Raster.Direction",alias:"Direction (°)",domain:null,editable:!1,type:"double"}))}return i}set renderer(i){this._set("renderer",i),this.updateRenderer()}readRenderer(i,e,r){const t=e&&e.layerDefinition&&e.layerDefinition.drawingInfo&&e.layerDefinition.drawingInfo.renderer,s=Et(t,r)||void 0;if(s!=null)return s}createPopupTemplate(i){return Ft({fields:this.rasterFields,title:this.title},i)}async generateRasterInfo(i,e){if(!(i=et(Me,i)))return this._primaryRasters[0].rasterInfo;try{const r={raster:this._primaryRasters[0]};this._primaryRasters.length>1&&this._primaryRasters.forEach(n=>r[n.url]=n);const t=$e(i.toJSON(),r),s=new Oe({rasterFunction:t});return await s.open(e),s.rasterInfo}catch{return null}}write(i,e){const{raster:r}=this;if(this.loaded?r.datasetFormat==="RasterTileServer"&&(r.tileType==="Raster"||r.tileType==="Map"):this.url&&/\/ImageServer(\/|\/?$)/i.test(this.url))return super.write(i,e);if(e&&e.messages){const t=`${e.origin}/${e.layerContainerType||"operational-layers"}`;e.messages.push(new M("layer:unsupported",`Layers (${this.title}, ${this.id}) of type '${this.declaredClass}' are not supported in the context of '${t}'`,{layer:this}))}return null}async _openRaster(i){let e=!1;if(this.raster)this.raster.rasterInfo||await this.raster.open(),this.raster.datasetFormat==="Function"?(e=!0,this._primaryRasters=this.raster.primaryRasters.rasters):this._primaryRasters=[this.raster],this.url=this.raster.url;else{const{rasterFunction:t}=this,s=[this.url];t&&ki(t.toJSON(),s);const n=await Promise.all(s.map(o=>Ai.open({url:o,sourceJSON:this.sourceJSON,ioConfig:{sampling:"closest",...this.ioConfig,customFetchParameters:this.customParameters},signal:i}))),a=n.findIndex(o=>o==null);if(a>-1)throw new M("imagery-tile-layer:open",`cannot open raster: ${s[a]}`);if(this._primaryRasters=n,t){const o={raster:this._primaryRasters[0]};this._primaryRasters.length>1&&this._primaryRasters.forEach(u=>o[u.url]=u);const l=$e(t.rasterFunctionDefinition??t.toJSON(),o),c=new Oe({rasterFunction:l});try{await c.open(),this.raster=c}catch(u){const h=De.getLogger(this.declaredClass);u instanceof M&&h.error("imagery-tile-layer:open",u.message),h.warn("imagery-tile-layer:open","the raster function cannot be applied and is removed"),this._set("rasterFunction",null),this.raster=n[0]}}else this.raster=n[0]}const r=this.raster.rasterInfo;if(!r)throw new M("imagery-tile-layer:load","cannot load resources on "+this.url);if(this._set("rasterInfo",e?r:this._primaryRasters[0].rasterInfo),this._set("spatialReference",r.spatialReference),this.sourceJSON=this.sourceJSON||this.raster.sourceJSON,this.sourceJSON!=null){const t=this.raster.tileType==="Map"&&this.sourceJSON.minLOD!=null&&this.sourceJSON.maxLOD!=null?this.sourceJSON:{...this.sourceJSON,minScale:0,maxScale:0};this.read(t,{origin:"service"})}this.title||(this.title=this.raster.datasetName),this.raster.tileType==="Map"&&(this.popupEnabled=!1),this._configDefaultSettings(),this.addHandles(qt(()=>this.customParameters,t=>{this.raster&&(this.raster.ioConfig.customFetchParameters=t)}))}};w([S()],E.prototype,"_primaryRasters",void 0),w([S({type:[yt],json:{write:{overridePolicy(){var i;return{enabled:!this.loaded||this.raster.tileType==="Raster"||((i=this.bandIds)==null?void 0:i.join(","))!=="0,1,2"}}}}})],E.prototype,"bandIds",void 0),w([S({json:{write:{overridePolicy(){return{enabled:!this.loaded||this.raster.tileType==="Raster"||this.interpolation!=="bilinear"}}}}}),kt(Jt)],E.prototype,"interpolation",void 0),w([S(Ct)],E.prototype,"legendEnabled",void 0),w([S({type:Boolean,json:{read:!1,write:{enabled:!0,overridePolicy:()=>({enabled:!1})}}})],E.prototype,"isReference",void 0),w([S({type:["show","hide"]})],E.prototype,"listMode",void 0),w([S({json:{read:!0,write:!0}})],E.prototype,"blendMode",void 0),w([S()],E.prototype,"sourceJSON",void 0),w([S({readOnly:!0,json:{origins:{service:{read:{source:"currentVersion"}}}}})],E.prototype,"version",void 0),w([S({readOnly:!0,json:{read:!1}})],E.prototype,"type",void 0),w([S({type:["ArcGISTiledImageServiceLayer"]})],E.prototype,"operationalLayerType",void 0),w([S({type:Boolean,value:!0,json:{read:{source:"disablePopup",reader:(i,e)=>!e.disablePopup},write:{target:"disablePopup",overridePolicy(){return{enabled:!this.loaded||this.raster.tileType==="Raster"}},writer(i,e,r){e[r]=!i}}}})],E.prototype,"popupEnabled",void 0),w([S({type:Pt,json:{read:{source:"popupInfo"},write:{target:"popupInfo",overridePolicy(){return{enabled:!this.loaded||this.raster.tileType==="Raster"}}}}})],E.prototype,"popupTemplate",void 0),w([S({readOnly:!0})],E.prototype,"defaultPopupTemplate",null),w([S({readOnly:!0,type:[me]})],E.prototype,"fields",void 0),w([S({readOnly:!0,type:[me]})],E.prototype,"rasterFields",null),w([S({types:at,json:{name:"layerDefinition.drawingInfo.renderer",write:{overridePolicy(){var e;const i=((e=this.renderer)==null?void 0:e.type)==="raster-stretch"&&this.renderer.stretchType==="none"&&!this.renderer.useGamma;return{enabled:!this.loaded||this.raster.tileType==="Raster"||!i}}},origins:{"web-scene":{types:Nt,name:"layerDefinition.drawingInfo.renderer",write:{overridePolicy:i=>({enabled:i&&i.type!=="vector-field"&&i.type!=="flow"})}}}}})],E.prototype,"renderer",null),w([gt("renderer")],E.prototype,"readRenderer",null),E=w([Q("esri.layers.ImageryTileLayer")],E);const os=E;export{os as default};
