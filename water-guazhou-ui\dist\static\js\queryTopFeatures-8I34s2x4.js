import{L as p,U as m,V as F}from"./pe-B8dP0-Ut.js";import{R as l}from"./index-r0dFAfgr.js";import{c1 as f,aq as E,b_ as x,c2 as O}from"./MapView-DaoQedLH.js";const c="Layer does not support extent calculation.";function g(r,e){var s,y;const o=r.geometry,t=r.toJSON(),n=t;if(l(o)&&(n.geometry=JSON.stringify(o),n.geometryType=O(o),n.inSR=o.spatialReference.wkid||JSON.stringify(o.spatialReference)),(s=t.topFilter)!=null&&s.groupByFields&&(n.topFilter.groupByFields=t.topFilter.groupByFields.join(",")),(y=t.topFilter)!=null&&y.orderByFields&&(n.topFilter.orderByFields=t.topFilter.orderByFields.join(",")),t.topFilter&&(n.topFilter=JSON.stringify(n.topFilter)),t.objectIds&&(n.objectIds=t.objectIds.join(",")),t.orderByFields&&(n.orderByFields=t.orderByFields.join(",")),t.outFields&&!(e!=null&&e.returnCountOnly||e!=null&&e.returnExtentOnly||e!=null&&e.returnIdsOnly)?t.outFields.includes("*")?n.outFields="*":n.outFields=t.outFields.join(","):delete n.outFields,t.outSR?n.outSR=t.outSR.wkid||JSON.stringify(t.outSR):o&&t.returnGeometry&&(n.outSR=n.inSR),t.returnGeometry&&delete t.returnGeometry,t.timeExtent){const d=t.timeExtent,{start:i,end:u}=d;i==null&&u==null||(n.time=i===u?i:`${i??"null"},${u??"null"}`),delete t.timeExtent}return n}async function w(r,e,o,t){const n=await a(r,e,"json",t);return f(e,o,n.data),n}async function B(r,e,o){return l(e.timeExtent)&&e.timeExtent.isEmpty?{data:{objectIds:[]}}:a(r,e,"json",o,{returnIdsOnly:!0})}async function h(r,e,o){return l(e.timeExtent)&&e.timeExtent.isEmpty?{data:{count:0,extent:null}}:a(r,e,"json",o,{returnExtentOnly:!0,returnCountOnly:!0}).then(t=>{const n=t.data;if(n.hasOwnProperty("extent"))return t;if(n.features)throw new Error(c);if(n.hasOwnProperty("count"))throw new Error(c);return t})}function I(r,e,o){return l(e.timeExtent)&&e.timeExtent.isEmpty?Promise.resolve({data:{count:0}}):a(r,e,"json",o,{returnIdsOnly:!0,returnCountOnly:!0})}function a(r,e,o,t={},n={}){const s=typeof r=="string"?p(r):r,y=e.geometry?[e.geometry]:[];return t.responseType="json",E(y,null,t).then(d=>{const i=d&&d[0];l(i)&&((e=e.clone()).geometry=i);const u=x({...s.query,f:o,...n,...g(e,n)});return m(F(s.path,"queryTopFeatures"),{...t,query:{...u,...t.query}})})}export{I as a,w as d,B as m,h as p};
