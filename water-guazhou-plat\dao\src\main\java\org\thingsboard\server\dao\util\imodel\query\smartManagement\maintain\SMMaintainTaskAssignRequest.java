package org.thingsboard.server.dao.util.imodel.query.smartManagement.maintain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.query.GeneralTaskRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

import java.util.Date;

@Getter
@Setter
public class SMMaintainTaskAssignRequest implements GeneralTaskRequest {
    // 所属任务Id
    private String taskId;

    // 养护人员Id
    @NotNullOrEmpty
    private String maintainUser;

    // 开始时间
    @NotNullOrEmpty
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date beginTime;

    // 结束时间
    @NotNullOrEmpty
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    // 备注
    private String remark;


}
