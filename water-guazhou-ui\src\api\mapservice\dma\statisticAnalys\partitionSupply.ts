import { request } from '@/plugins/axios'

/**
 * 查询分区供水量报表
 * @param params
 * @returns
 */
export const GetDmaPartitionSupplyReport = (params: {
  partitionId: string
  type: string
  date?: string
  start?: string
  end?: string
}) => {
  return request({
    url: '/api/spp/statisticsReport/partitionSupply',
    method: 'get',
    params
  })
}
/**
 * 查询分区供水量报表表头
 * @param params
 * @returns
 */
export const GetDmaPartitionSupplyReportHeader = (params: {
  partitionId: string
}) => {
  return request({
    url: '/api/spp/statisticsReport/partitionSupplyHeader',
    method: 'get',
    params
  })
}
/**
 * 查询流量日报表列表
 * @param params
 * @returns
 */
export const GetDmaPartitionDayliFlowReport = (params: {
  partitionIds: string
  day: string
}) => {
  return request({
    url: '/api/spp/statisticsReport/dayFlow',
    method: 'get',
    params
  })
}
