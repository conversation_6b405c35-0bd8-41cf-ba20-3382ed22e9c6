import { ISLFormItem } from '../SLFormItem/type'
import { ISLMoreFilter } from '../SLMoreFilter/type'

export type ISLOperation = {
  type?:
    | ''
    | 'default'
    | 'success'
    | 'primary'
    | 'warning'
    | 'info'
    | 'danger'
    | ((
        row?: any
      ) => '' | 'default' | 'success' | 'primary' | 'warning' | 'info' | 'danger' | undefined)
  text?: string | ((row?: any) => string)
  icon?: string | ((row?: any) => string)
  color?: string | ((row?: any) => string)
  bgColor?: string | ((row?: any) => string)
  styles?: string | Record<string, any> | ((row?: any) => string)
  perm?: boolean | ((row?: any) => boolean)
  size?: 'small' | 'default' | 'large'
  disabled?: boolean | ((row?: any) => boolean)
  loading?: boolean | ((row?: any) => boolean)
  isTextBtn?: boolean
  click?: (row?: any) => void
}

export type ISLCardSearch = {
  size?: 'small' | 'default' | 'large'
  filters?: ISLFormItem[] // 默认左边筛选内容
  rightFilter?: ISLFormItem[] // 右边筛选内容
  moreFilter?: ISLMoreFilter // 下滑的更多筛选内容
  operations?: ISLOperation[] // 默认左边的按钮组
  midOperations?: ISLOperation[] // 中间的按钮组
  rightOperations?: ISLOperation[] // 右边的按钮组
  defaultParams?: Record<string, any> // 传入默认值
  handleSearch?: (...args: any[]) => void // 定义默认的搜索方法
  labelWidth?: string // 带单位的长度
}
