import { request } from '@/plugins/axios'

/**
 * 查询水厂供水报表
 * @param params
 * @returns
 */
export const GetDmaPlantSupplyReport = (params: {
  type: string
  date: string
  start: string
  end: string
}) => {
  return request({
    url: '/api/spp/waterFactory/monitorPoint/report',
    method: 'get',
    params
  })
}
/**
 * 查询水厂供水报表表头
 * @param params
 * @returns
 */
export const GetDmaPlantSupplyReportHeader = () => {
  return request({
    url: '/api/spp/waterFactory/monitorPoint/reportHeader',
    method: 'get'
  })
}
/**
 * 移除监测点
 * @param ids
 * @returns
 */
export const RemoveDmaPlantMonitorPoint = (ids: string[]) => {
  return request({
    url: '/api/spp/waterFactory/monitorPoint',
    method: 'delete',
    data: ids
  })
}
/**
 * 编辑监测点的排序
 * @param params
 * @returns
 */
export const EditDmaPlantMonitorOrder = (params: {
  id: string
  orderNum: number
}) => {
  return request({
    url: '/api/spp/waterFactory/monitorPoint/changeOrderNum',
    method: 'post',
    data: params
  })
}
/**
 * 变更监测点方向
 * @param id
 * @returns
 */
export const EditDmaPlantMonitorDirection = (id: string) => {
  return request({
    url: `/api/spp/waterFactory/monitorPoint/changeDirection/${id}`,
    method: 'post'
  })
}
/**
 * 查询水厂监测点列表
 * @param params
 * @returns
 */
export const GetDmaPlantMonitorList = (params: { name: string }) => {
  return request({
    url: '/api/spp/waterFactory/monitorPoint/list',
    method: 'get',
    params
  })
}

/**
 * 添加监测点
 * @param params
 * @returns
 */
export const AddDmaPlantMonitor = (params: {
  factoryName: string
  orderNum: number
  deviceId: string
  direction: string
}) => {
  return request({
    url: '/api/spp/waterFactory/monitorPoint',
    method: 'post',
    data: params
  })
}
/**
 * 查询设备列表
 * @param params
 * @returns
 */
export const GetDmaPlantMonitorDevices = (
  params: IQueryPagerParams & {
    name?: string
  }
) => {
  return request({
    url: '/api/device/getWaterFactoryDevice',
    method: 'get',
    params
  })
}
