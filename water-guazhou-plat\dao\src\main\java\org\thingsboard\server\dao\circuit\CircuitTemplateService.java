package org.thingsboard.server.dao.circuit;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.smartProduction.circuit.CircuitConfig;
import org.thingsboard.server.dao.model.sql.smartProduction.circuit.CircuitTemplate;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.circuit.CircuitTemplatePageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.circuit.CircuitTemplateSaveRequest;

import java.util.List;

public interface CircuitTemplateService {
    /**
     * 分页条件查询巡检模板
     *
     * @param request 分页查询条件
     * @return 分页查询结果
     */
    IPage<CircuitTemplate> findAllConditional(CircuitTemplatePageRequest request);

    /**
     * 保存巡检模板
     *
     * @param entity 实体信息
     * @return 保存好的实体
     */
    CircuitTemplate save(CircuitTemplateSaveRequest entity);

    /**
     * 增量修改
     *
     * @param entity 实体信息
     * @return 是否修改成功
     */
    boolean update(CircuitTemplate entity);

    /**
     * 删除
     *
     * @param id 唯一标识
     * @return 是否删除成功
     */
    boolean delete(String id);

    /**
     * 通过模板id来获取所有的{@link CircuitConfig 任务条目配置模板}
     *
     * @param templateId 唯一标识
     * @return 是否删除成功
     */
    List<String> getSettings(String templateId);

}
