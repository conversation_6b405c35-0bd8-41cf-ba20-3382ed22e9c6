package org.thingsboard.server.controller.base;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.dao.model.request.SystemNotifyTypeListRequest;
import org.thingsboard.server.dao.model.sql.notify.SystemNotifyType;
import org.thingsboard.server.dao.notify.SystemNotifyTypeService;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

@RestController
@RequestMapping("api/systemNotifyType")
public class SystemNotifyTypeController extends BaseController {

    @Autowired
    private SystemNotifyTypeService systemNotifyTypeService;

    @GetMapping("list")
    public IstarResponse findList(SystemNotifyTypeListRequest request) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        request.setTenantId(tenantId);
        return IstarResponse.ok(systemNotifyTypeService.findList(request));
    }

    @PostMapping
    public IstarResponse save(@RequestBody SystemNotifyType systemNotifyType) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        systemNotifyType.setTenantId(tenantId);
        String userId = UUIDConverter.fromTimeUUID(getCurrentUser().getUuidId());
        systemNotifyType.setCreator(userId);
        return IstarResponse.ok(systemNotifyTypeService.save(systemNotifyType));
    }

    @DeleteMapping("{id}")
    public IstarResponse delete(@PathVariable String id) {
        systemNotifyTypeService.delete(id);
        return IstarResponse.ok();
    }

}
