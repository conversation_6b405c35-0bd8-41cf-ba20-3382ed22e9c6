import{_ as x}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as k}from"./CardTable-rdWOL4_6.js";import{_ as v}from"./CardSearch-CB_HNR-Q.js";import{d as q,c as _,r as p,s as d,S as h,o as T,g as D,n as F,q as u,i as m,al as S,b7 as L,ak as V}from"./index-r0dFAfgr.js";import{a as I,d as B,c as E}from"./waterInspection-DqEu1Oyl.js";import{p as y}from"./data-DPRqSSi2.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";const j={class:"wrapper"},G=q({__name:"index",setup(w){const s=_(),c=_(),b=p({filters:[{type:"select",label:"项目类型",field:"itemType",options:y},{type:"input",label:"项目名称",field:"name"}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",svgIcon:d(S),click:()=>n()},{perm:!0,text:"重置",type:"default",svgIcon:d(L),click:()=>{var e;(e=c.value)==null||e.resetForm()}},{perm:!0,text:"添加",svgIcon:d(V),click:()=>{i.title="新增",f()}}]}]}),a=p({indexVisible:!0,columns:[{label:"项目分类",prop:"itemType"},{label:"项目名称",prop:"name"},{label:"巡检方法",prop:"method"},{label:"巡检要求",prop:"require"}],operations:[{perm:!0,text:"修改",click:e=>{i.title="修改",f(e)}},{perm:!0,text:"删除",type:"danger",click:e=>C(e)}],dataList:[],pagination:{page:1,limit:20,total:0,refreshData:({page:e,size:o})=>{a.pagination.page=e,a.pagination.limit=o,n()}}}),i=p({dialogWidth:500,title:"新增",defaultValue:{itemType:"泵房"},group:[{fields:[{type:"select",label:"项目分类",field:"itemType",options:y,clearable:!1},{type:"input",label:"项目名称",field:"name",rules:[{required:!0,message:"请输入项目名称"}],placeholder:"请输入项目名称"},{type:"textarea",label:"巡检方法",field:"method",rules:[{required:!0,message:"请输入巡检方法"}],placeholder:"请输入巡检方法"},{type:"textarea",label:"巡检要求",field:"require",rules:[{required:!0,message:"请输入巡检要求"}],placeholder:"请输入巡检要求"}]}]}),f=e=>{var o;i.defaultValue={...e||{}},i.submit=t=>{h("确定提交？","提示信息").then(()=>{t.type="水源",t.id=e?e.id:null,I(t).then(()=>{var l,r;(l=s.value)==null||l.resetForm(),(r=s.value)==null||r.closeDialog(),n()})}).catch(()=>{})},(o=s.value)==null||o.openDialog()},C=e=>{h("确定删除?","提示信息").then(()=>{B(e.id).then(()=>{n()})}).catch(()=>{})},n=async()=>{var l,r,g;const e=((l=c.value)==null?void 0:l.queryParams)||{},o={page:a.pagination.page||1,size:a.pagination.limit||20,type:"水源",...e},t=await E(o);console.log(t.data.data.total),a.pagination.total=(r=t.data)==null?void 0:r.data.total,a.dataList=(g=t.data)==null?void 0:g.data.data};return T(async()=>{n()}),(e,o)=>{const t=v,l=k,r=x;return D(),F("div",j,[u(t,{ref_key:"refSearch",ref:c,config:m(b)},null,8,["config"]),u(l,{config:m(a),class:"card-table"},null,8,["config"]),u(r,{ref_key:"refForm",ref:s,config:m(i)},null,8,["config"])])}}});export{G as default};
