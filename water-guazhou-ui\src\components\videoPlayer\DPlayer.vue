<template>
  <div class="video_box">
    <div ref="DPlayerIns" style="width: 100%; height: 100%"></div>
  </div>
</template>

<script lang="ts" setup>
import Hls from 'hls.js';
import DPlayer from 'dplayer';

const props = defineProps<{ videoInfo: any }>();
const hls = new Hls();
const DPlayerIns = ref<HTMLDivElement>();
const dp = ref();

function init() {
  dp.value = new DPlayer({
    element: DPlayerIns.value,
    loop: false,
    autoplay: true, // 自动播放
    // theme: '#0093ff', //颜色
    // loop: true, //循环播放
    // lang: 'zh-cn', //语言
    // screenshot: false, //开启截图
    // hotkey: false, //控制按钮
    // preload: 'auto', //视频预加载
    // volume: 0.7, //默认音量
    preventClickToggle: true,
    live: props.videoInfo.videoUrl.live, // 直播模式
    mutex: false, // 多个视频播放(false允许/true不允许)
    video: {
      url: props.videoInfo.videoUrl.m3u8uri,
      type: 'customHls',
      customType: {
        customHls(video, player) {
          hls.loadSource(video.src);
          hls.attachMedia(video);
        }
      }
    }
  });
}

onMounted(() => {
  init();
});

onUnmounted(() => {
  hls.stopLoad();
  hls.detachMedia();
  dp.value = null;
});
</script>

<style lang="scss" scoped>
.video_box {
  width: 100%;
  height: 100%;
  position: relative;
}
</style>
