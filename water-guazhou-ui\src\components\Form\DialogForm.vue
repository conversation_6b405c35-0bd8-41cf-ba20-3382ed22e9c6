<template>
  <div class="system-add-menu-container">
    <el-dialog
      id="print"
      v-model="state.visible"
      :title="config.title"
      :width="config.dialogWidth || '60%'"
      :fullscreen="config.fullscreen"
      :draggable="config.draggable"
      :top="config.top"
      :modal="config.model"
      :close-on-click-modal="config.closeOnClickModal === true"
      :close-on-press-escape="config.closeOnPressEscape === true"
      :show-close="config.showClose"
      :destroy-on-close="config.desTroyOnClose !== false"
      :append-to-body="config.appendToBody || false"
      @close="emit('close')"
    >
      <slot name="default">
        <div class="dialog-form-container">
          <Form ref="refForm" :config="config">
            <template #fieldSlot="slotProp">
              <slot
                name="fieldSlot"
                :config="slotProp.config"
                :row="slotProp.row"
              ></slot>
            </template>
          </Form>
        </div>
      </slot>

      <template v-if="config.cancel !== false || config.submit" #footer>
        <span class="dialog-footer">
          <template v-if="config.btns">
            <Button
              v-for="(item, i) in config.btns"
              :key="i"
              :config="item"
              :size="item?.size || 'default'"
              :loading="config.submitting"
            ></Button>
          </template>
          <el-button
            v-if="config?.print || false"
            type="primary"
            size="default"
            @click="handlePrint"
          >
            打 印</el-button
          >
          <el-button
            v-if="config.submit"
            type="primary"
            size="default"
            :loading="config.submitting"
            @click="Submit(false)"
          >
            {{ config.submitText || '确 定' }}
          </el-button>
          <el-button
            v-if="config.cancel !== false"
            size="default"
            @click="closeDialog"
            >关 闭</el-button
          >
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { reactive, watch, ref } from 'vue';
// import { printHTML, printJSON } from '@/utils/printUtils'
import Form from './Form.vue';
import { outPutPdf } from '@/utils/outPdf';

const props = defineProps<{
  config: IDialogFormConfig;
}>();
const emit = defineEmits(['close']);
const refForm = ref<InstanceType<typeof Form>>();
const state = reactive<{
  visible: boolean;
}>({
  visible: false
});

// 打开弹窗
const openDialog = () => {
  state.visible = true;
};
// 关闭弹窗
const closeDialog = () => {
  state.visible = false;
  // emit('close')
};
const resetForm = () => {
  refForm.value?.resetForm();
};
// 保存 save
const Submit = (status?: any) => {
  // 插槽下处理
  if (refForm.value === undefined && props.config.submit) {
    props.config?.submit(false);
  } else {
    refForm.value?.Submit(status);
  }
};

// 打印报表
const handlePrint = () => {
  // printHTML({ printId: 'print' })
  outPutPdf('print', '', false, true, (res) => {
    console.log(res);
  });
};

watch(
  () => state.visible,
  (newVal: boolean) => {
    if (newVal && props.config.desTroyOnClose) refForm.value?.resetForm();
  }
);
defineExpose({
  refForm,
  Submit,
  closeDialog,
  openDialog,
  resetForm
});
</script>
<style lang="scss">
.dialog-form-container {
  padding-top: 12px;
}
</style>
