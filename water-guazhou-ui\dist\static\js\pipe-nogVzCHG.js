import{V as t,W as s,U as i}from"./index-r0dFAfgr.js";const n=()=>t({method:"post",url:window.SITE_CONFIG.GIS_CONFIG.gisPipeDynamicService+"?f=pjson"}),o=e=>t({url:window.SITE_CONFIG.GIS_CONFIG.gisPipeDynamicService+"/exts/TFGeo/apiSOE/statistic",method:"get",params:{usertoken:s().gToken,f:"pjson",where:"1=1",...e||{}}}),a=e=>t({url:window.SITE_CONFIG.GIS_CONFIG.gisPipeDynamicService+"/exts/TFGeo/apiSOE/getExtent",method:"get",params:e}),p=()=>t({url:window.SITE_CONFIG.GIS_CONFIG.gisPipeDynamicService+"/legend?f=pjson",method:"get"}),u=e=>i({url:"/api/gis/AlongPipeQuery",method:"post",params:e}),G=()=>i({url:"/api/gis/PipeByRangeDiameter",method:"post"}),c=()=>i({url:"/api/gis/PipeByBuildYear",method:"post"});export{u as G,o as P,n as Q,G as a,c as b,a as c,p as d};
