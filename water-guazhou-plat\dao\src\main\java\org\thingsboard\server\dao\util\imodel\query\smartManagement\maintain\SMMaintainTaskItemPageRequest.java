package org.thingsboard.server.dao.util.imodel.query.smartManagement.maintain;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartManagement.maintaince.SMMaintainTaskItem;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

@Getter
@Setter
public class SMMaintainTaskItemPageRequest extends AdvancedPageableQueryEntity<SMMaintainTaskItem, SMMaintainTaskItemPageRequest> {
    // 所属任务
    @NotNullOrEmpty
    private String taskId;

    // 是否完成
    private Boolean isComplete;

}
