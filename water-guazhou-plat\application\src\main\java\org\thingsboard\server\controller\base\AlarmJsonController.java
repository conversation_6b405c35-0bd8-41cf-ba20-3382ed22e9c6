/**
 * Copyright © 2016-2019 The Thingsboard Authors
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.controller.base;

import org.springframework.data.repository.query.Param;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.VO.AlarmLinkedUser;
import org.thingsboard.server.common.data.alarm.AlarmJsonId;
import org.thingsboard.server.common.data.alarm.AttrAlarmJson;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.DeviceId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.common.data.relation.RelationTypeGroup;
import org.thingsboard.server.service.aspect.annotation.SysLog;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

/**
 * 新的报警controller
 * 2019-01-02
 * jerry
 */
@RestController
@RequestMapping("/api")
public class AlarmJsonController extends BaseController {

    public static final String ALARM_ID = "alarmId";


    /**
     * 获取登录用户所在企业的报警设置
     * @return 报警设置列表
     * @throws ThingsboardException exception
      */
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN',  'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE', 'TENANT_SYS', 'CUSTOMER_USER')")
    @RequestMapping(value = "/alarmJson", method = RequestMethod.GET)
    @ResponseBody
    @SysLog(detail = DataConstants.OPERATING_TYPE_ALARM_JSON_LIST_GET)
    public List<AttrAlarmJson> getAlarmByTenantId() throws ThingsboardException {
        List<AttrAlarmJson> alarmJsons = new ArrayList<>();
        try {
            alarmJsons = alarmJsonService.findByTenant(getTenantId()).get();
        } catch (InterruptedException e) {
            e.printStackTrace();
        } catch (ExecutionException e) {
            e.printStackTrace();
        }
        return alarmJsons;
    }

    /**
     * 获取项目下的告警设置
     * @param projectId 项目ID
     * @return 报警设置列表
     * @throws ThingsboardException exception
     */
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN',  'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE', 'TENANT_SYS', 'CUSTOMER_USER')")
    @RequestMapping(value = "/alarmJson/project/{projectId}", method = RequestMethod.GET)
    @ResponseBody
    @SysLog(detail = DataConstants.OPERATING_TYPE_ALARM_JSON_LIST_GET)
    public List<AttrAlarmJson> getAlarmByProjectId(@PathVariable String projectId) throws ThingsboardException {
        List<AttrAlarmJson> alarmJsons = new ArrayList<>();
        alarmJsons = alarmJsonService.findByProjectId(projectId);
        return alarmJsons;
    }

    /**
     * 获取项目下的告警设置
     * @param projectId 项目ID
     * @return 报警设置列表
     * @throws ThingsboardException exception
     */
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN',  'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE', 'TENANT_SYS', 'CUSTOMER_USER')")
    @RequestMapping(value = "/alarmJson/project/page/{projectId}", method = RequestMethod.GET)
    @ResponseBody
    @SysLog(detail = DataConstants.OPERATING_TYPE_ALARM_JSON_LIST_GET)
    public PageData<AttrAlarmJson> getAlarmPageByProjectId(@PathVariable String projectId, @RequestParam int page, @RequestParam int size, @RequestParam(required = false, defaultValue = "") String keyword) throws ThingsboardException {
        List<AttrAlarmJson> alarmJsons = new ArrayList<>();
        alarmJsons = alarmJsonService.findByProjectId(projectId);

        // 名称过滤
        alarmJsons = alarmJsons.stream().filter(e -> e.getAlarmName().contains(keyword)).collect(Collectors.toList());

        return PageData.page(alarmJsons, page, size);
    }

    @PreAuthorize("hasAnyAuthority('SYS_ADMIN',  'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE', 'TENANT_SYS', 'CUSTOMER_USER')")
    @RequestMapping(value = "/alarmJson/get", method = RequestMethod.GET)
    @ResponseBody
    @SysLog(detail = DataConstants.OPERATING_TYPE_ALARM_JSON_LIST_GET)
    public List<AttrAlarmJson> getAlarmByDeviceId(@Param("deviceId") String deviceId) {
        List<AttrAlarmJson> alarmJsons = new ArrayList<>();
        alarmJsons = alarmJsonService.findByDevice(new DeviceId(UUIDConverter.fromString(deviceId)));
        return alarmJsons;
    }


    /**
     * 保存报警设置
     * @param attrAlarmJson 报警设置
     * @return 报警设置内容
     */
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN',  'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE', 'TENANT_SYS', 'CUSTOMER_USER')")
    @RequestMapping(value = "/alarmJson/save", method = RequestMethod.POST)
    @ResponseBody
    @SysLog(options = DataConstants.OPERATING_TYPE.OPERATING_TYPE_OPTIONS_SETTING_ALARM_SAVE)
    public AttrAlarmJson saveAlarmJson(@RequestBody AttrAlarmJson attrAlarmJson) {
        try {
            checkNotNull(attrAlarmJson);
            return alarmJsonService.createOrUpdateAlarmJson(attrAlarmJson);
        } catch (ThingsboardException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 报警关联联系人
     * @param alarmLinkedUsers 报警关联人
     * @return 报警设置内容
     */
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN',  'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE', 'TENANT_SYS', 'CUSTOMER_USER')")
    @RequestMapping(value = "/alarmJson/linkedUser/{alarmJsonId}", method = RequestMethod.POST)
    @ResponseBody
    @SysLog(options = DataConstants.OPERATING_TYPE.OPERATING_TYPE_OPTIONS_SETTING_ALARM_SAVE)
    public boolean saveAlarmJson(@PathVariable("alarmJsonId")String alarmJsonId, @RequestBody List<AlarmLinkedUser> alarmLinkedUsers) throws ThingsboardException {
            return alarmJsonService.linkedUser(alarmLinkedUsers,alarmJsonId,getTenantId(), RelationTypeGroup.USER);
    }

    /**
     * 报警关联外部联系人
     * @param alarmLinkedUsers 报警关联人
     * @return 报警设置内容
     */
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN',  'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE', 'TENANT_SYS', 'CUSTOMER_USER')")
    @RequestMapping(value = "/alarmJson/linkedExtraUser/{alarmJsonId}", method = RequestMethod.POST)
    @ResponseBody
    @SysLog(options = DataConstants.OPERATING_TYPE.OPERATING_TYPE_OPTIONS_SETTING_ALARM_SAVE)
    public boolean linkedAlarmJson(@PathVariable("alarmJsonId")String alarmJsonId, @RequestBody List<AlarmLinkedUser> alarmLinkedUsers) throws ThingsboardException {
        return alarmJsonService.linkedUser(alarmLinkedUsers,alarmJsonId,getTenantId(),RelationTypeGroup.EXTAR_USER);
    }


    /**
     * 获取报警关联联系人
     * @return 报警设置内容
     */
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN',  'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE', 'TENANT_SYS', 'CUSTOMER_USER')")
    @GetMapping(value = "/alarmJson/linkedUser/{alarmJsonId}")
    @ResponseBody
    @SysLog(options = DataConstants.OPERATING_TYPE.OPERATING_TYPE_OPTIONS_SETTING_ALARM_SAVE)
    public List<AlarmLinkedUser> getAlarmLinkedUser(@PathVariable("alarmJsonId") String attrAlarmJsonId) {
        try {
            checkNotNull(attrAlarmJsonId);
            return alarmJsonService.getAllLinkedUser(getTenantId(),attrAlarmJsonId);
        } catch (ThingsboardException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 获取报警外部关联人
     * @return 报警设置内容
     */
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN',  'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE', 'TENANT_SYS', 'CUSTOMER_USER')")
    @GetMapping(value = "/alarmJson/linkedExtraUser/{alarmJsonId}")
    @ResponseBody
    @SysLog(options = DataConstants.OPERATING_TYPE.OPERATING_TYPE_OPTIONS_SETTING_ALARM_SAVE)
    public List<AlarmLinkedUser> getAlarmLinkedExtraUser(@PathVariable("alarmJsonId") String attrAlarmJsonId) {
        try {
            checkNotNull(attrAlarmJsonId);
            return alarmJsonService.getAllLinkedExtraUser(getTenantId(),attrAlarmJsonId);
        } catch (ThingsboardException e) {
            e.printStackTrace();
        }
        return null;
    }



    @PreAuthorize("hasAnyAuthority('SYS_ADMIN',  'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE', 'CUSTOMER_USER')")
    @RequestMapping(value = "/alarmJson/delete", method = RequestMethod.DELETE)
    @ResponseStatus(value = HttpStatus.OK)
    @SysLog(options = DataConstants.OPERATING_TYPE.OPERATING_TYPE_OPTIONS_SETTING_ALARM_DELETE)
    public void deleteAlarm(@Param("id") String id) {
        try {
            AttrAlarmJson attrAlarmJson = alarmJsonService.findAlarmById(new AlarmJsonId(UUIDConverter.fromString(id)));
            alarmJsonService.deleteAlarmJsonById(attrAlarmJson);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


}