package org.thingsboard.server.dao.maintainCircuit.circuit;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.common.data.utils.DateUtils;
import org.thingsboard.server.dao.model.sql.deviceManage.DeviceType;
import org.thingsboard.server.dao.model.sql.maintainCircuit.circuit.CircuitPointM;
import org.thingsboard.server.dao.model.sql.maintainCircuit.circuit.CircuitTaskC;
import org.thingsboard.server.dao.model.sql.maintainCircuit.circuit.CircuitTaskM;
import org.thingsboard.server.dao.model.sql.maintainCircuit.circuit.CircuitTaskPoint;
import org.thingsboard.server.dao.sql.deviceType.DeviceTypeMapper;
import org.thingsboard.server.dao.sql.maintainCircuit.circuit.CircuitTaskCMapper;
import org.thingsboard.server.dao.sql.maintainCircuit.circuit.CircuitTaskMMapper;
import org.thingsboard.server.dao.sql.maintainCircuit.circuit.CircuitTaskPointMapper;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-08-24
 */
@Slf4j
@Service
@Transactional
public class CircuitTaskMServiceImpl implements CircuitTaskMService {

    @Autowired
    private CircuitTaskMMapper circuitTaskMMapper;

    @Autowired
    private DeviceTypeMapper deviceTypeMapper;

    @Autowired
    private CircuitTaskCMapper circuitTaskCMapper;

    @Autowired
    private CircuitTaskPointMapper circuitTaskPointMapper;

    @Autowired
    private CircuitPointMService circuitPointMService;

    @Override
    public PageData getList(String code, String name, String planName, String teamName, String userName, String status, String auditStatus, String deviceLabelCode, String deviceId, String userId, Date startStartTime, Date startEndTime, Date endStartTime, Date endEndTime, int page, int size, String tenantId) {

        List<CircuitTaskM> circuitTaskMList = circuitTaskMMapper.getList(code, name, planName, teamName, userName, status, auditStatus, deviceLabelCode, deviceId, userId, startStartTime, startEndTime, endStartTime, endEndTime, page, size, tenantId);

        int total = circuitTaskMMapper.getListCount(code, name, planName, teamName, userName, status, auditStatus, deviceLabelCode, deviceId, userId, startStartTime, startEndTime, endStartTime, endEndTime, tenantId);

        return new PageData(total, circuitTaskMList);
    }

    @Override
    public CircuitTaskM getDetail(String mainId) {
        CircuitTaskM circuitTaskM = circuitTaskMMapper.getById(mainId);
        List<CircuitTaskC> circuitTaskCList = circuitTaskCMapper.getList(circuitTaskM.getId());
        // 所属分类链表
        for (CircuitTaskC circuitTaskC : circuitTaskCList) {
            this.setType(circuitTaskC);
        }

        circuitTaskM.setCircuitTaskCList(circuitTaskCList);

        // 获取巡检点
        List<CircuitTaskPoint> list = circuitTaskPointMapper.getList(circuitTaskM.getId());
        for (CircuitTaskPoint circuitTaskPoint : list) {
            CircuitPointM circuitPointM = circuitPointMService.getById(circuitTaskPoint.getPointId());
            circuitTaskPoint.setCircuitPointM(circuitPointM);
        }
        circuitTaskM.setCircuitTaskPointList(list);

        return circuitTaskM;
    }

    @Override
    public CircuitTaskM save(CircuitTaskM circuitTaskM) {

        if (StringUtils.isBlank(circuitTaskM.getId())) {
            circuitTaskM.setStatus("0");
            circuitTaskM.setCreateTime(new Date());
            circuitTaskM.setCode("XJ" + new SimpleDateFormat("yyyyMMddHHmmssSSS").format(new Date()));;
            circuitTaskM.setAuditStatus("0");;

            circuitTaskMMapper.insert(circuitTaskM);
        } else {
            circuitTaskMMapper.updateById(circuitTaskM);
        }

        // 删除设备
        Map deleteMap = new HashMap<>();
        deleteMap.put("main_id", circuitTaskM.getId());
        circuitTaskCMapper.deleteByMap(deleteMap);

        if (circuitTaskM.getCircuitTaskCList() != null) {
            for (CircuitTaskC circuitTaskC : circuitTaskM.getCircuitTaskCList()) {
                circuitTaskC.setMainId(circuitTaskM.getId());
                circuitTaskC.setTenantId(circuitTaskM.getTenantId());
                circuitTaskC.setCreateTime(new Date());
                circuitTaskCMapper.insert(circuitTaskC);
            }
        }

        // 删除巡检点
        circuitTaskPointMapper.deleteByMap(deleteMap);
        if (circuitTaskM.getCircuitTaskPointList() != null) {
            for (CircuitTaskPoint circuitTaskPoint : circuitTaskM.getCircuitTaskPointList()) {
                circuitTaskPoint.setMainId(circuitTaskM.getId());
                circuitTaskPoint.setTenantId(circuitTaskM.getTenantId());
                circuitTaskPoint.setCreateTime(new Date());
                circuitTaskPointMapper.insert(circuitTaskPoint);
            }
        }


        return circuitTaskM;
    }

    @Override
    public IstarResponse delete(List<String> ids) {
        circuitTaskMMapper.deleteBatchIds(ids);

        // 删除子表
        QueryWrapper<CircuitTaskC> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("main_id", ids);
        circuitTaskCMapper.delete(queryWrapper);

        QueryWrapper<CircuitTaskPoint> circuitTaskPointQueryWrapper = new QueryWrapper<>();
        circuitTaskPointMapper.delete(circuitTaskPointQueryWrapper);

        return IstarResponse.ok("删除成功");
    }

    @Override
    public void reviewer(CircuitTaskM circuitTaskM) {
        CircuitTaskM circuitTaskM1 = new CircuitTaskM();
        circuitTaskM1.setId(circuitTaskM.getId());
        circuitTaskM1.setAuditor(circuitTaskM.getAuditor());
        circuitTaskM1.setAuditRemark(circuitTaskM.getAuditRemark());
        circuitTaskM1.setAuditStatus(circuitTaskM.getAuditStatus());
        circuitTaskM1.setAuditTime(new Date());

        circuitTaskMMapper.updateById(circuitTaskM1);
    }

    @Override
    public void changeStatus(CircuitTaskM circuitTaskM) {
        CircuitTaskM circuitTaskM1 = new CircuitTaskM();
        circuitTaskM1.setId(circuitTaskM.getId());
        circuitTaskM1.setStatus(circuitTaskM.getStatus());

        circuitTaskMMapper.updateById(circuitTaskM1);
    }

    @Override
    public List<CircuitTaskM> findAll() {
        return circuitTaskMMapper.selectByMap(new HashMap<>());
    }

    @Override
    public List statistics(Long startTime, Long endTime, String tenantId) {
        return circuitTaskMMapper.statistics(startTime == null ? null : new Date(startTime), endTime == null ? null : new Date(endTime), tenantId);
    }

    @Override
    public boolean checkAuditor(String id, String userId) {
        CircuitTaskM circuitTaskM = circuitTaskMMapper.selectById(id);
        if (circuitTaskM.getAuditor() != null && !userId.equals(circuitTaskM.getAuditor())) {
            return false;
        }
        return true;
    }

    @Override
    public IstarResponse receive(String id, String userId) {
        CircuitTaskM circuitTaskM = circuitTaskMMapper.selectById(id);
        if (circuitTaskM == null) {
            return IstarResponse.error("该任务不存在");
        }
        if (!userId.equals(circuitTaskM.getUserId())) {
            return IstarResponse.error("您没有接收权限");
        }
        if (!"0".equals(circuitTaskM.getStatus())) {
            return IstarResponse.error("该任务已接收");
        }

        circuitTaskM.setStatus("1");
        circuitTaskM.setRealStartTime(new Date());

        circuitTaskMMapper.updateById(circuitTaskM);

        return IstarResponse.ok("接收成功");
    }

    @Override
    public Integer getNotCompleteNum(String userId, String tenantId) {
        return circuitTaskMMapper.getNotCompleteNum(userId, tenantId);
    }


    private void setType(CircuitTaskC circuitTaskC) {
        if (StringUtils.isBlank(circuitTaskC.getTypeId())) {
            circuitTaskC.setLinkedType("-");
            return;
        }
        String linkedType = "-";
        String topType = "-";
        DeviceType deviceType = null;
        String parentId = circuitTaskC.getTypeId();
        for (int i = 0; i < 5; i++) {
            deviceType = deviceTypeMapper.selectById(parentId);
            if (deviceType == null) {
                break;
            }
            linkedType = deviceType.getName() + ">" + linkedType;
            if (StringUtils.isBlank(deviceType.getParentId())) {
                topType = deviceType.getName();
                break;
            }
            parentId = deviceType.getParentId();
        }
        if (linkedType.length() >= 2) {
            linkedType = linkedType.substring(0, linkedType.length() - 2);
        }

        circuitTaskC.setLinkedType(linkedType);
        circuitTaskC.setTopType(topType);
        circuitTaskC.setType(linkedType);
        try {
            if (linkedType.contains(">")) {
                circuitTaskC.setType(linkedType.substring(linkedType.lastIndexOf(">") + 1));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        // 上次操作时间
        CircuitTaskC circuitTaskC1 = circuitTaskCMapper.selectFirstByDeviceLabelCode(circuitTaskC.getDeviceLabelCode(), circuitTaskC.getCreateTime());
        if (circuitTaskC1 != null) {
            circuitTaskC.setLastModifyTime(circuitTaskC1.getCreateTime());
        }
    }


    private boolean checkTimeIsToday(Date nextExecuteTime) throws ParseException {
        String string = DateUtils.date2Str(new Date(), DateUtils.DATE_FORMATE_DAY);
        SimpleDateFormat dateFormat = new SimpleDateFormat(DateUtils.DATE_FORMATE_DEFAULT);
//        Date todayStart = dateFormat.parse(string + " 00:00:00");
        Date todayEnd = dateFormat.parse(string + " 23:59:59");

        if (/*nextExecuteTime.getTime() > todayStart.getTime() && */nextExecuteTime.getTime() < todayEnd.getTime()) {
            return true;
        }

        return false;
    }

}
