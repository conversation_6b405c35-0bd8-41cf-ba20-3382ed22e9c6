package org.thingsboard.server.common.data.VO;

import lombok.Data;

/**
 * DMA水平衡表基础信息
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-04-26
 */
@Data
public class DmaPartitionInfoVO {

    /**
     * 所属区域
     */
    private String pname;

    /**
     * 区域编码
     */
    private String code;

    /**
     * 区域名称
     */
    private String name;

    /**
     * 居民用户数
     */
    private Integer householdsNumber;

    /**
     * 实际入住数
     */
    private Integer realHouseholdsNumber;

    /**
     * 总表是否收费
     */
    private boolean isCharge;

    /**
     * 供水方式
     */
    private String supplyMode;

    /**
     * 区域属性
     */
    private String partitionType;

    /**
     * 考核表数量
     */
    private Integer meterNumber;

    /**
     *水表品牌
     */
    private String meterBrand;

    /**
     * 合理用水
     */
    private Double rationalUse;

}
