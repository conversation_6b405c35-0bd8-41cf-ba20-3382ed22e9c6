<template>
  <div class="leak-detection-details-container">
    <!-- 筛选栏 -->
    <el-form :model="searchParams" :inline="true" class="filter-form">
      <el-form-item label="方案名称">
        <el-input v-model="searchParams.name" placeholder="请输入方案名称" clearable />
      </el-form-item>
      <el-form-item label="分区">
        <el-select v-model="searchParams.partition" placeholder="全部" clearable style="width: 140px;">
          <el-option label="全部" value="" />
          <el-option v-for="item in partitionList" :key="item" :label="item" :value="item" />
        </el-select>
      </el-form-item>
      <el-form-item label="分段">
        <el-select v-model="searchParams.segment" placeholder="全部" clearable style="width: 140px;">
          <el-option label="全部" value="" />
          <el-option v-for="item in segmentList" :key="item" :label="item" :value="item" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态">
        <el-select v-model="searchParams.status" placeholder="全部" clearable style="width: 120px;">
          <el-option label="全部" value="" />
          <el-option label="未开始" value="未开始" />
          <el-option label="进行中" value="进行中" />
          <el-option label="已完成" value="已完成" />
        </el-select>
      </el-form-item>
      <el-form-item label="时间">
        <el-date-picker v-model="searchParams.dateRange" type="daterange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">查询</el-button>
        <el-button @click="resetSearch">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 明细表 -->
    <el-table :data="filteredDetails" border style="width: 100%">
      <el-table-column prop="name" label="方案名称" min-width="140" />
      <el-table-column prop="partition" label="分区" min-width="100" />
      <el-table-column prop="segment" label="分段" min-width="100" />
      <el-table-column prop="leader" label="负责人" min-width="100" />
      <el-table-column prop="status" label="状态" min-width="100" />
      <el-table-column prop="lossRate" label="最新漏损率(%)" min-width="120" />
      <el-table-column prop="flow" label="最新流量(m³/h)" min-width="120" />
      <el-table-column prop="pressure" label="最新压力(MPa)" min-width="120" />
      <el-table-column prop="leakCount" label="发现漏点数" min-width="100" />
      <el-table-column label="操作" width="180">
        <template #default="scope">
          <el-button type="primary" link @click="openHistory(scope.row)">回溯</el-button>
          <el-button type="info" link @click="openDetail(scope.row)">详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-model:current-page="pagination.page"
      v-model:page-size="pagination.pageSize"
      :total="filteredDetails.length"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      style="margin-top: 16px; text-align: right;"
    />

    <!-- 回溯弹窗 -->
    <el-dialog v-model="historyDialogVisible" title="分段监测历史回溯" width="700px">
      <div style="height:320px;">
        <div ref="historyChartRef" style="width:100%;height:100%;"></div>
      </div>
      <template #footer>
        <el-button @click="historyDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>

    <!-- 详情弹窗 -->
    <el-dialog v-model="detailDialogVisible" title="分段详情" width="600px">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="方案名称">{{ detailForm.name }}</el-descriptions-item>
        <el-descriptions-item label="分区">{{ detailForm.partition }}</el-descriptions-item>
        <el-descriptions-item label="分段">{{ detailForm.segment }}</el-descriptions-item>
        <el-descriptions-item label="负责人">{{ detailForm.leader }}</el-descriptions-item>
        <el-descriptions-item label="状态">{{ detailForm.status }}</el-descriptions-item>
        <el-descriptions-item label="最新漏损率">{{ detailForm.lossRate }}%</el-descriptions-item>
        <el-descriptions-item label="最新流量">{{ detailForm.flow }} m³/h</el-descriptions-item>
        <el-descriptions-item label="最新压力">{{ detailForm.pressure }} MPa</el-descriptions-item>
        <el-descriptions-item label="发现漏点数">{{ detailForm.leakCount }}</el-descriptions-item>
      </el-descriptions>
      <el-divider>监测历史</el-divider>
      <el-table :data="detailForm.monitorHistory" border size="small">
        <el-table-column prop="time" label="时间" min-width="140" />
        <el-table-column prop="lossRate" label="漏损率(%)" min-width="100" />
        <el-table-column prop="flow" label="流量(m³/h)" min-width="100" />
        <el-table-column prop="pressure" label="压力(MPa)" min-width="100" />
      </el-table>
      <template #footer>
        <el-button @click="detailDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'

const partitionList = ['A区', 'B区', 'C区', 'D区']
const segmentList = ['A1段', 'A2段', 'B1段', 'C1段', 'D1段']

// 假数据
const planDetails = ref([
  {
    id: 1,
    name: 'A区主干管分段探漏',
    partition: 'A区',
    segment: 'A1段',
    leader: '李四',
    status: '已完成',
    lossRate: 7.2,
    flow: 120,
    pressure: 2.5,
    leakCount: 2,
    monitorHistory: [
      { time: '2023-10-10 08:00', lossRate: 8.0, flow: 130, pressure: 2.4 },
      { time: '2023-10-10 12:00', lossRate: 7.5, flow: 125, pressure: 2.5 },
      { time: '2023-10-10 16:00', lossRate: 7.2, flow: 120, pressure: 2.5 }
    ]
  },
  {
    id: 2,
    name: 'A区主干管分段探漏',
    partition: 'A区',
    segment: 'A2段',
    leader: '王五',
    status: '进行中',
    lossRate: 8.5,
    flow: 135,
    pressure: 2.3,
    leakCount: 1,
    monitorHistory: [
      { time: '2023-10-10 08:00', lossRate: 9.0, flow: 140, pressure: 2.2 },
      { time: '2023-10-10 12:00', lossRate: 8.7, flow: 138, pressure: 2.3 },
      { time: '2023-10-10 16:00', lossRate: 8.5, flow: 135, pressure: 2.3 }
    ]
  },
  {
    id: 3,
    name: 'B区分段探漏',
    partition: 'B区',
    segment: 'B1段',
    leader: '赵六',
    status: '未开始',
    lossRate: 9.1,
    flow: 110,
    pressure: 2.1,
    leakCount: 0,
    monitorHistory: [
      { time: '2023-10-10 08:00', lossRate: 9.1, flow: 110, pressure: 2.1 }
    ]
  }
])

const searchParams = ref({
  name: '',
  partition: '',
  segment: '',
  status: '',
  dateRange: []
})

const pagination = ref({
  page: 1,
  pageSize: 10,
})

const filteredDetails = computed(() => {
  let data = planDetails.value.filter(row => {
    const nameMatch = !searchParams.value.name || row.name.includes(searchParams.value.name)
    const partitionMatch = !searchParams.value.partition || row.partition === searchParams.value.partition
    const segmentMatch = !searchParams.value.segment || row.segment === searchParams.value.segment
    const statusMatch = !searchParams.value.status || row.status === searchParams.value.status
    let dateMatch = true
    if (searchParams.value.dateRange?.length === 2) {
      const d = new Date(row.monitorHistory[row.monitorHistory.length - 1]?.time)
      const start = new Date(searchParams.value.dateRange[0])
      const end = new Date(searchParams.value.dateRange[1])
      dateMatch = d >= start && d <= end
    }
    return nameMatch && partitionMatch && segmentMatch && statusMatch && dateMatch
  })
  // 分页
  const start = (pagination.value.page - 1) * pagination.value.pageSize
  const end = start + pagination.value.pageSize
  return data.slice(start, end)
})

function handleSearch() {
  pagination.value.page = 1
}
function resetSearch() {
  searchParams.value.name = ''
  searchParams.value.partition = ''
  searchParams.value.segment = ''
  searchParams.value.status = ''
  searchParams.value.dateRange = []
  pagination.value.page = 1
}

// 回溯弹窗
const historyDialogVisible = ref(false)
const historyChartRef = ref()
let historyChartInstance: any = null
function openHistory(row: any) {
  historyDialogVisible.value = true
  nextTick(() => {
    if (!historyChartInstance) {
      historyChartInstance = echarts.init(historyChartRef.value)
    }
    const times = row.monitorHistory.map((d: any) => d.time)
    historyChartInstance.setOption({
      title: { text: '分段监测历史曲线', left: 'center' },
      tooltip: { trigger: 'axis' },
      legend: { data: ['漏损率', '流量', '压力'] },
      xAxis: { type: 'category', data: times },
      yAxis: [
        { type: 'value', name: '漏损率(%)', position: 'left' },
        { type: 'value', name: '流量(m³/h)', position: 'right' },
        { type: 'value', name: '压力(MPa)', position: 'right', offset: 60 }
      ],
      series: [
        { name: '漏损率', type: 'line', yAxisIndex: 0, data: row.monitorHistory.map((d: any) => d.lossRate) },
        { name: '流量', type: 'line', yAxisIndex: 1, data: row.monitorHistory.map((d: any) => d.flow) },
        { name: '压力', type: 'line', yAxisIndex: 2, data: row.monitorHistory.map((d: any) => d.pressure) }
      ]
    })
  })
}

// 详情弹窗
const detailDialogVisible = ref(false)
const detailForm = ref<any>({ monitorHistory: [] })
function openDetail(row: any) {
  detailForm.value = JSON.parse(JSON.stringify(row))
  detailDialogVisible.value = true
}
</script>

<style scoped>
.leak-detection-details-container {
  padding: 20px;
  background: #fff;
  border-radius: 6px;
}
.filter-form {
  margin-bottom: 16px;
}
</style> 