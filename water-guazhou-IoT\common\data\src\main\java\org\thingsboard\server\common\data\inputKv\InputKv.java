/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.common.data.inputKv;

import lombok.Data;

import java.io.Serializable;

@Data
public class InputKv implements Serializable {
    private String entityId;
    private String entityType;
    private String key;
    private long ts;
    private String value;
    private String projectId;

    public InputKv() {
    }

    public InputKv(String entityId, String entityType, String key, long ts, String value) {
        this.entityId = entityId;
        this.entityType = entityType;
        this.key = key;
        this.ts = ts;
        this.value = value;
    }
}
