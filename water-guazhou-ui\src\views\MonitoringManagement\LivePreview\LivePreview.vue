<!-- 视频监控管理-实时预览 -->
<template>
  <div class="wrapper">
    <el-card class="box-card" style="width: 100%; height: 100%" shadow="never">
      <Split style="width: 100%; height: 100%" :value="0.15">
        <template #left>
          <div class="header">
            <el-collapse
              v-model="state.activeNames"
              style="height: calc(100% - 0px); width: 100%; position: relative"
            >
              <el-collapse-item class="FZXX" title="视频列表" name="1">
                <slot name="video">
                  <SimpleTree :config="TreeData"></SimpleTree>
                </slot>
              </el-collapse-item>
              <el-collapse-item class="ptz" title="云台" name="3">
                <PTZ :config="state.currentVideo"></PTZ>
              </el-collapse-item>
              <!-- <el-collapse-item title="视频参数" name="4">
                              <Parameters></Parameters>
                          </el-collapse-item> -->
            </el-collapse>
          </div>
        </template>
        <template #right>
          <div class="content">
            <div class="content_body" id="videos">
              <el-row style="height: 100%" ref="el">
                <template v-for="i in state.total">
                  <el-col
                    @click="selectedEffect(i, true)"
                    :class="{
                      item_1: state.group === 24,
                      item_4: state.group === 12,
                      item_9: state.group === 8,
                      item: true,
                      item_selected: state.videoList[i]?.status ?? false
                    }"
                    style="position: relative"
                    :span="state.group"
                  >
                    <div v-if="!state.videoList[i]?.url" class="item_bg">
                      待选中视频源
                    </div>
                    <DPlayer
                      v-else-if="state.videoList[i]?.type === 'customHls'"
                      style="width: 100%; height: 100%"
                      :video-info="{
                        live: true,
                        hotkey: false,
                        preload: 'auto',
                        autoplay: true,
                        video: {
                          url: state.videoList[i]?.url ?? '',
                          type: 'customHls'
                        }
                      }"
                    ></DPlayer>
                    <WSPlayer
                      v-else-if="state.videoList[i]?.type === 'ws'"
                      :ref="
                        (el) => {
                          videosRef[i] = el;
                        }
                      "
                      :config="{
                        url: state.videoList[i]?.url ?? '',
                        index: i,
                        talkurl: state.videoList[i]?.talkurl
                      }"
                      @setVolume="setVolume"
                    ></WSPlayer>
                    <XGPlayer
                      v-else
                      :video-info="{
                        live: true,
                        hotkey: false,
                        preload: 'auto',
                        autoplay: true,
                        video: {
                          url: state.videoList[i]?.url ?? '',
                          type: 'flv'
                        }
                      }"
                    ></XGPlayer>
                    <el-button
                      :class="{ no_close: false }"
                      class="close"
                      text
                      circle
                      color="#232323"
                      @click.stop="closeVideo(i)"
                    >
                      <el-icon slot="10">
                        <Close />
                      </el-icon>
                    </el-button>
                  </el-col>
                </template>
              </el-row>
            </div>
            <div class="content_title">
              <div class="btns">
                <Intercom
                  ref="refIntercom"
                  @talkStart="talkStart"
                  @talkStop="talkStop"
                  @putVolume="putVolume"
                ></Intercom>
              </div>
              <div class="btns">
                <Btns
                  @change-full-screen="changeFullScreen"
                  @set-view="setView"
                  @close-all="closeVideo"
                >
                </Btns>
              </div>
            </div>
          </div>
        </template>
      </Split>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { Close } from '@element-plus/icons-vue';
import SimpleTree from '@/components/SimpleTree/SimpleTree.vue';
import { GeneralTable } from '@/utils/GeneralProcessing';
import { getCameraTree, getVideoUrlById } from '@/api/video';
import DPlayer from '@/components/videoPlayer/DPlayer_v2.vue';
import WSPlayer from '@/components/videoPlayer/WSPlayer.vue';
import XGPlayer from '@/components/videoPlayer/XGPlayer.vue';
import Split from '@/components/Split/Split.vue';
import PTZ from './components/PTZ.vue';
// import Parameters from './components/Parameters.vue'
import Btns from './components/btns.vue';
import Intercom from './components/Intercom.vue';
import { ElMessage } from 'element-plus';
import { videoStore } from '@/store/modules/video';
import { useDraggable } from 'vue-draggable-plus';

const props = defineProps<{
  config?: {
    total: number;
  };
}>();

const videosRef = ref<any>([]);
const refIntercom = ref();
const el = ref();

const state = reactive<{
  fullScreen: boolean;
  activeNames: string[];
  value1: string;
  index: number;
  group: number;
  total: number;
  status: boolean;
  videoList: {
    status: boolean;
    url?: string;
    talkurl?: string | null;
    type?: string;
    id?: string;
    key: string | null;
  }[];
  currentVideo: any;
}>({
  fullScreen: false,
  activeNames: ['1'],
  value1: '',
  index: 1,
  group: 8,
  total: 9,
  status: false,
  videoList: [
    { status: false, url: '', talkurl: '', type: 'customHls', key: null },
    { status: false, url: '', talkurl: '', type: 'customHls', key: null },
    { status: false, url: '', talkurl: '', type: 'customHls', key: null },
    { status: false, url: '', talkurl: '', type: 'customHls', key: null },
    { status: false, url: '', talkurl: '', type: 'customHls', key: null },
    { status: false, url: '', talkurl: '', type: 'customHls', key: null },
    { status: false, url: '', talkurl: '', type: 'customHls', key: null },
    { status: false, url: '', talkurl: '', type: 'customHls', key: null },
    { status: false, url: '', talkurl: '', type: 'customHls', key: null },
    { status: false, url: '', talkurl: '', type: 'customHls', key: null }
  ],
  currentVideo: {}
});

const { start } = useDraggable(el, state.videoList as any, {
  animation: 150,
  ghostClass: 'ghost',
  onStart() {
    console.log('start');
    console.table(state.videoList);
  },
  onUpdate(val) {
    console.log('update', val);
    console.table(state.videoList);
    ArrayExchange((val.oldIndex || 0) + 1, (val.newIndex || 0) + 1);
  }
});

// 数组交换位置
const ArrayExchange = (v1, v2) => {
  [state.videoList[v1], state.videoList[v2]] = [
    state.videoList[v2],
    state.videoList[v1]
  ];
  state.index = v2;
  console.table(state.videoList);
};

// 分组信息
const TreeData = reactive<simpleTree>({
  search: true,
  defaultExpandAll: false,
  data: [],
  currentProject: {},
  tag: {
    noShow: (val) => {
      return !('status' in val.nodeDetail) || val.nodeDetail.status === null;
    },
    label: (val) => (val.nodeDetail.status === '1' ? '在线' : '离线'),
    type: (val) => (val.nodeDetail.status === '1' ? 'success' : 'danger')
  },
  click: (val) => {
    if (val.type === 'video') {
      getPlaySourceData(val);
    }
    // else {
    //   TreeData.currentProject = val;
    //   getVideoData();
    // }
  }
});

// 摄像头数据
const videoData = reactive<any>({
  search: true,
  data: [],
  click: (data) => {
    TreeData.currentProject = data;
    getPlaySourceData(data);
  }
});

// 选中效果
const selectedEffect = (data, status?) => {
  // 获取语音对讲地址
  // state.videoList[data]?.id &&
  //   getVoiceIntercom(state.videoList[data]?.id)
  //     .then((res) => {
  //       if (res.data.code === 200) {
  //         state.videoList[data].talkurl = res.data.data;
  //       } else {
  //         state.videoList[data].talkurl = null;
  //       }
  //       // 重置对讲按钮
  //       refIntercom.value?.refresh(state.videoList[data], data);
  //     })
  //     .catch(() => {
  //       // 重置对讲按钮
  //       state.videoList[data].talkurl = null;
  //       refIntercom.value?.refresh(state.videoList[data], data);
  //     });
  state.status = status;
  state.videoList.forEach((item, index) => {
    if (item.status === true) {
      // 关闭对讲状态
      videosRef.value[index]?.talkStop();
    }
    item.status = false;
    if (data === index) {
      state.videoList[index].status = true;
      state.currentVideo = state.videoList[index];
      state.index = data;
    }
  });
};

// 视频类型
const setVideoType = (url) => {
  if (url.indexOf('ws') !== -1) {
    return 'ws';
  } else {
    return 'customHls';
  }
};

// 获取视频数据
const getPlaySourceData = (row) => {
  if (state.videoList.some((item) => item.key === row.id)) {
    ElMessage.warning('当前摄像头已打开，请关闭后再试');
    return;
  }
  getVideoUrlById(row.id).then((res) => {
    setVideoPlayer(row, { data: { data: res.data } }, row.id);
  });
};

// 设置播放窗口
const setVideoPlayer = (row, res, key: string) => {
  // 判断重复
  let status = false;
  state.videoList.forEach((item, index) => {
    if (item.url === (res.data.data || '')) {
      closeVideo(index);
      state.status = false;
      state.videoList[state.index] = {
        ...state.videoList[state.index],
        ...row,
        url: res.data.data || '',
        type: setVideoType(res.data.data || ''),
        key
      };
      status = true;
    }
  });
  if (status) return;
  if (state.status) {
    state.videoList[state.index] = {
      ...state.videoList[state.index],
      ...row,
      url: res.data.data || '',
      type: setVideoType(res.data.data || ''),
      key
    };
    state.status = false;
    return;
  }
  const val = state.videoList[state.index];
  if (val.url === '' && state.index < state.total) {
    state.videoList[state.index] = {
      ...state.videoList[state.index],
      ...row,
      url: res.data.data || '',
      type: setVideoType(res.data.data || ''),
      key
    };
    return;
  }
  if (state.index < state.total) {
    state.index++;
    state.videoList[state.index] = {
      ...state.videoList[state.index],
      ...row,
      url: res.data.data || '',
      type: setVideoType(res.data.data || ''),
      key
    };
    selectedEffect(state.index, false);
    return;
  } else {
    state.videoList[1] = {
      ...state.videoList[1],
      ...row,
      url: res.data.data || '',
      type: setVideoType(res.data.data || ''),
      key
    };
    selectedEffect(1, false);
    return;
  }
};

const refreshTree = () => {
  GeneralTable({}, getCameraTree).then((res) => {
    TreeData.data = traverse(res.data, 'children');
  });
};

function traverse(val: any, children: 'children' | string = 'children') {
  // 对传入的值进行映射处理，递归转换每个对象
  val.map((obj) => {
    if (obj) {
      if (obj.type === 'video') return obj;
      // 遍历keys对象，根据规则转换当前对象的属性
      obj['name'] = obj['name'] + ` (${obj['num']})`;

      // 如果当前对象包含子元素，则对子元素进行相同的处理
      if (obj[children] && obj[children].length) {
        traverse(obj[children], children);
      }
    }
    return obj;
  });
  return val;
}

// 获取摄像头列表
const getVideoData = () => {
  const params = {
    size: 9999,
    page: 1,
    projectId: TreeData.currentProject.id
  };
  // GeneralTable(params, getCameraList).then((res) => {
  //   videoData.data = res.data;
  // });
};

// 关闭视频
const closeVideo = (index) => {
  if (index)
    state.videoList[index] = {
      status: state.videoList[index].status,
      url: '',
      talkurl: '',
      type: 'customHls',
      key: null
    };
  else
    state.videoList = state.videoList.map((item) => {
      refIntercom.value?.refresh(item, 100);
      item = {
        status: item.status,
        url: '',
        talkurl: '',
        type: 'customHls',
        key: null
      };
      return item;
    });
};

// 全屏控制
const changeFullScreen = () => {
  const fullarea: any = document.getElementById('videos');
  const isFullscreen =
    (document as any).fullScreen ||
    (document as any).mozFullScreen ||
    (document as any).webkitIsFullScreen;
  if (isFullscreen) {
    // 退出全屏,三目运算符
    document.exitFullscreen && document.exitFullscreen();
    state.fullScreen = false;
  } else {
    // 进入全屏,多重短路表达式
    (fullarea.requestFullscreen && fullarea.requestFullscreen()) ||
      (fullarea.mozRequestFullScreen && fullarea.mozRequestFullScreen()) ||
      (fullarea.webkitRequestFullscreen &&
        fullarea.webkitRequestFullscreen()) ||
      (fullarea.msRequestFullscreen && fullarea.msRequestFullscreen());
    state.fullScreen = true;
  }
};

const setView = (group, total) => {
  state.group = group;
  state.total = total;
  for (let i = total + 1; i < state.videoList.length; i++) {
    state.videoList[i].url = '';
  }
};

// 初始化摄像头音量
const setVolume = (status, volume) => {
  refIntercom.value?.setVolume(status, volume);
};
// 设置摄像头音量
const putVolume = (val) => {
  videosRef.value[val]?.setVolume(val);
};

const talkStart = (val) => {
  videosRef.value[val]?.talkStart();
};

const talkStop = (val) => {
  videosRef.value[val]?.talkStop();
};

onMounted(() => {
  refreshTree();

  if (props?.config?.total) {
    state.total = props.config.total;
    switch (props.config.total) {
      case 1:
        state.group = 24;
        break;
      case 4:
        state.group = 12;
        break;
      case 9:
        state.group = 8;
        break;
      default:
        break;
    }
  }
});

defineExpose({ getPlaySourceData, getVideoData });

// 销毁组件
onUnmounted(() => {
  videoStore().setValue({
    videoList: state.videoList,
    group: state.group,
    total: state.total
  });
});

onBeforeMount(() => {
  if (!props.config) {
    const key = videoStore().putValue();
    for (let i in key) {
      state[i] = key[i];
    }
  }
});
</script>

<style lang="scss" scoped>
.box-card {
  :deep(.el-card__body) {
    padding: 0px !important;
    display: flex;
    height: 100%;
  }
}

.header {
  width: 100%;
  height: 100%;
  border-right: 1px solid var(--live-border-color);
  overflow-y: auto;
}

.el-collapse-item.el-collapse-item__wrap {
  overflow-y: auto;
}

.el-collapse-item {
  :deep(.el-collapse-item__header) {
    padding: 0 10px;
    background-color: var(--el-bg-color-overlay);
    border: 0px solid #000;
  }

  :deep(.el-collapse-item__wrap) {
    height: calc(100% - 48px);
    overflow-y: auto;
    padding: 15px 10px 0 10px;
  }
}

.el-collapse-item:deep(.el-calendar__body) {
  padding: 40px;
}

.content {
  width: 100%;
  height: 100%;

  .content_title {
    width: 100%;
    height: 36px;
    border-top: 1px solid var(--live-border-color, #e6e6e6);
    display: flex;
    justify-content: space-between;
  }

  .content_body {
    width: 100%;
    height: calc(100% - 36px);
  }

  .video {
    flex: 1;
  }
}

.el-collapse {
  height: 100%;
  display: flex;
  flex-direction: column;

  transition: height 2s;
}

.btns {
  display: flex;
  align-items: center;
}

.el-button {
  margin: 0px;
  height: 100%;
}

.interval {
  width: 1px;
  height: 80%;
  background-color: rgb(124, 124, 124);
}

.item {
  border: 2px solid #565656;

  &:hover > .close {
    visibility: visible;
  }
}

.item_selected {
  border: 2px solid #ff0000;
}

.item_1 {
  height: 100%;
  width: 100%;
}

.item_4 {
  height: 50%;
  width: 50%;
}

.item_9 {
  height: 33.33%;
  width: 33.33%;
}

.item_bg {
  background-color: #000000;
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #868686;
}

.close {
  visibility: hidden;
  width: 10px;
  height: 10px;
  position: absolute;
  top: 10px;
  right: 10px;
  color: #e6e6e6;
  z-index: 20002;

  &:hover {
    color: #ff0000;
  }
}

.no_close {
  display: none;
  z-index: 10000;
}

.FZXX {
  height: calc(100% - 47px);
  :deep(div) {
    border: 0px solid #000;
  }
}

.ptz {
  width: 100%;
  position: absolute;
  bottom: 0px;
  left: 0px;
}
</style>
