package org.thingsboard.server.dao.sql.workOrder;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrderCollaboration;
import org.thingsboard.server.dao.util.imodel.query.workOrder.WorkOrderCollaborationPageRequest;

@Mapper
public interface NewlyWorkOrderCollaborationMapper extends BaseMapper<WorkOrderCollaboration> {
    IPage<WorkOrderCollaboration> findByPage(WorkOrderCollaborationPageRequest request);

    @Select("select count(id) from work_order_collaboration wc where wc.id = #{id} and wc.status = #{name}")
    boolean isStatus(@Param("id") String id, @Param("name") String name);

    @Select("select count(id) from work_order_collaboration wc where wc.id = #{id} and user_id = #{currentUserUUID}")
    boolean isUserProcess(@Param("id") String id, @Param("currentUserUUID") String currentUserUUID);

    @Update("update work_order_collaboration set status = #{status} where id = #{id}")
    boolean setCollaborationStatus(@Param("id") String id, @Param("status") String status);
}
