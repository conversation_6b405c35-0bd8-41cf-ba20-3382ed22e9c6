import{m as v,d as b,c as p,r as y,e8 as c,am as d,g as i,n as l,aB as h,aJ as S,p as o,bh as r,i as w,ar as g,C as B}from"./index-r0dFAfgr.js";import{n as N}from"./shuichangzonglan-HwbtusbI.js";function k(n){return v({url:"/api/revenue/custInfo/list",method:"GET",params:n})}const x={class:"total-statistic"},C={class:"block"},I={class:"count"},T={class:"value"},z={class:"unit"},D=b({__name:"TotalStatistic_zilianda",props:{custom:{type:Boolean},waterSupplyNum:{},waterSupply:{},data:{}},setup(n){const t=n,m=p({梓莲达:"46b4960678843b025563c6fa86c5f21b",金峰水厂:"b7507185378b2e2bde66053d0abb4c27",北部水厂:"e7c762b34e63ed75ed708d52ae849120",西部水厂:"96c0dd4cb78d6bd654423b4f427f4ec4"});p();const s=y({items:t.data||[{title:"今日供水量",value:c(0),unit:"m³"},{title:"产销率",value:"0.00",unit:"%"},{title:"服务用户数",value:c(0),unit:"人"}]}),_=()=>{console.log(t.waterSupply),g(1).then(a=>{k({page:1,size:1,orgId:m.value[t.waterSupply.name]}).then(u=>{var e;s.items[2].value=c(((e=u.data)==null?void 0:e.data.total)??0)})}),N({name:t.waterSupply.name}).then(a=>{s.items[1].value=a.data.data,s.items[1].unit="%"})};return d(()=>t.waterSupplyNum,a=>{s.items[0].value=a.todayWaterSupply}),d(()=>t.waterSupply,a=>{_()}),(a,u)=>(i(),l("div",x,[(i(!0),l(h,null,S(w(s).items,(e,f)=>(i(),l("div",{key:f,class:"statistic-item"},[o("div",C,r(e.title),1),o("div",I,[o("span",T,r(e.value),1),o("span",z,r(e.unit),1)])]))),128))]))}}),W=B(D,[["__scopeId","data-v-dcf961fa"]]);export{W as default};
