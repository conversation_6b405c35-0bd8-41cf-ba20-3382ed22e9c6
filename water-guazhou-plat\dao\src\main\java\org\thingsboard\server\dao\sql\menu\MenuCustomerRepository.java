/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.sql.menu;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.id.MenuTenantId;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.model.sql.MenuCustomerEntity;

import java.util.List;

public interface MenuCustomerRepository extends CrudRepository<MenuCustomerEntity, String> {

    @Transactional
    @Modifying
    @Query("DELETE FROM MenuCustomerEntity mc1 WHERE mc1.id in (" +
            "SELECT DISTINCT mc2.id FROM MenuCustomerEntity mc2, MenuTenantEntity mt " +
            "WHERE mc2.menuTenantId NOT IN (SELECT mt1.id FROM MenuTenantEntity mt1))")
    void deleteByTenantId();


    @Query("SELECT DISTINCT mc FROM MenuCustomerEntity mc, MenuTenantEntity mt WHERE mc.menuTenantId = mt.id AND mt.tenantId = :tenantId AND mc.parentId = :parentId ORDER BY mc.orderNum desc")
    List<MenuCustomerEntity> findMenuByTenantId(@Param("tenantId") String tenantId, @Param("parentId") String parentId);

    @Query("SELECT mp.type FROM MenuTenantEntity mt, MenuPoolEntity mp " +
            "WHERE mt.menuPoolId = mp.id AND mt.tenantId = :tenantId")
    List<Integer> getTypesByTenantId(@Param("tenantId") String tenantId);

    @Query("SELECT DISTINCT mp.type,mp.defaultName,mp.additionalInfo FROM MenuTenantEntity mt, MenuPoolEntity mp " +
            "WHERE mt.menuPoolId = mp.id AND mt.tenantId = :tenantId")
    List getTypes(@Param("tenantId") String tenantId);

    List<MenuCustomerEntity> findByIdInOrderByOrderNumDesc(Object[] id);

    @Query("SELECT DISTINCT mp.type FROM MenuPoolEntity mp, MenuTenantEntity mt, MenuCustomerEntity mc " +
            "WHERE mt.menuPoolId = mp.id AND mt.id = mc.menuTenantId " +
            "AND mc.menuTenantId = :menuTenantId")
    Integer getTypeByMenuTenantId(@Param("menuTenantId") String menuTenantId);

    @Query("SELECT DISTINCT mc " +
            "FROM MenuCustomerEntity mc, MenuTenantEntity mt " +
            "WHERE mc.menuTenantId = mt.id AND mt.tenantId = :tenantId ORDER BY mc.orderNum desc")
    List<MenuCustomerEntity> findMenuByTenantId(@Param("tenantId") String tenantId);

    @Query("SELECT mc FROM MenuTenantEntity mt, MenuCustomerEntity mc " +
            "WHERE mt.id = mc.menuTenantId " +
            "AND mc.menuTenantId = :menuTenantId AND mt.tenantId = :tenantId")
    MenuCustomerEntity findByMenuTenantId(@Param("menuTenantId") String menuTenantId,
                                          @Param("tenantId") String tenantId);

    @Query("SELECT DISTINCT mc FROM MenuTenantEntity mt, MenuCustomerEntity mc " +
            "WHERE mt.id = mc.menuTenantId " +
            "AND mc.menuTenantId IN :menuTenantId AND mt.tenantId = :tenantId")
    List<MenuCustomerEntity> findByMenuTenantId(@Param("menuTenantId") List<String> menuTenantId,
                                          @Param("tenantId") String tenantId);

    void deleteByParentId(String parentId);
}
