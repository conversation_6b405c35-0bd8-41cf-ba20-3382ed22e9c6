package org.thingsboard.server.dao.construction.project;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.dao.construction.SoConstructionService;
import org.thingsboard.server.dao.model.sql.smartOperation.ConstructionWorkflow;
import org.thingsboard.server.dao.model.sql.smartOperation.device.SoDeviceItem;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralType;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoProject;
import org.thingsboard.server.dao.sql.smartOperation.construction.project.SoProjectMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.*;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemScope.SO_PROJECT;

@Service
public class SoProjectServiceImpl implements SoProjectService {
    @Autowired
    private SoProjectMapper mapper;

    @Autowired
    private SoGeneralTypeService typeService;

    @Autowired
    private SoDeviceItemService deviceItemService;

    @Autowired
    private SoConstructionService constructionService;

    @Autowired
    private SoProjectOperateRecordService recordService;

    @Autowired
    private SoBiddingService biddingService;


    @Override
    public IPage<SoProject> findAllConditional(SoProjectPageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    @Transactional
    public SoProject save(SoProjectSaveRequest entity) {
        return QueryUtil.saveOrUpdateOneByRequest(entity, e -> {
            int count = mapper.insert(e);
            String code = mapper.getCodeById(e.getId());
            recordService.save(SoProjectOperateRecordSaveRequest.of(entity, code, "项目%s启动", "启动"));

            SoBiddingSaveRequest bidding = new SoBiddingSaveRequest();
            bidding.setProjectCode(code);
            bidding.setItems(Collections.emptyList());
            biddingService.save(bidding);
            return count;
        }, mapper::updateFully);
    }

    @Override
    public boolean update(SoProject entity) {
        return mapper.update(entity);
    }

    @Override
    public boolean delete(String id) {
        return mapper.remove(id) > 0;
    }

    @Override
    public String generateCode(String tenantId) {
        return mapper.generateCode(tenantId);
    }

    @Override
    public boolean isCodeExists(String code, String tenantId, String id) {
        return mapper.isCodeExists(code, tenantId, id);
    }

    @Override
    public List<ConstructionWorkflow> completionInfo(String projectCode, String tenantId) {
        List<String> codeList = constructionService.getAllCodeByProject(projectCode, tenantId);
        return codeList.stream()
                .flatMap(constructionCode ->
                        constructionService.completionInfo(constructionCode, tenantId).stream())
                .collect(Collectors.toList());
    }

    @Override
    public boolean canBeDelete(String id) {
        return mapper.canBeDelete(id);
    }

    // region 类型配置
    @Override
    public IPage<SoGeneralType> getTypes(SoGeneralTypePageRequest request) {
        request.setScope(SO_PROJECT);
        return typeService.findAllConditional(request);
    }

    @Override
    public SoGeneralType saveType(SoGeneralTypeSaveRequest request) {
        request.setScope(SO_PROJECT);
        return typeService.save(request);
    }
    // endregion

    // region 设备项管理
    @Override
    public IPage<SoDeviceItem> getDevices(SoDeviceItemPageRequest request) {
        return deviceItemService.findAllConditional(request);
    }

    @Override
    public List<SoDeviceItem> saveDevice(List<SoDeviceItemSaveRequest> request) {
        for (SoDeviceItemSaveRequest req : request) {
            req.setScope(SO_PROJECT);
        }
        return deviceItemService.saveAll(request, SO_PROJECT);
    }

    // endregion
}
