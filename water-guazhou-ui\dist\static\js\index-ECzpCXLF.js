import{_ as S}from"./TreeBox-DDD2iwoR.js";import{_ as T}from"./CardTable-rdWOL4_6.js";import{_ as L}from"./CardSearch-CB_HNR-Q.js";import{_ as k}from"./index-BJ-QPYom.js";import{d as P,M as N,c as g,a9 as p,s as _,x as h,r as b,o as R,g as D,h as I,F as y,q as s,i as d,b7 as O}from"./index-r0dFAfgr.js";import{I as i}from"./common-CvK_P_ao.js";import w from"./QRCodePopover-CuQy9_i-.js";import{r as F,c as M}from"./equipmentManage-DuoY00aj.js";import{a as j}from"./ledgerManagement-CkhtRd8m.js";import{g as A}from"./equipmentOutStock-BiNkB8x8.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";import"./qrcode.vue.esm-DGEiBAmZ.js";const Z=P({__name:"index",setup(B){const{$btnPerms:m}=N(),c=g(),x=g({filters:[{label:"标签编码",field:"deviceLabelCode",type:"input"},{label:"设备名称",field:"name",type:"input"},{label:"设备型号",field:"model",type:"input"},{label:"供应商",field:"supplierId",type:"select-tree",checkStrictly:!0,defaultExpandAll:!0,autoFillOptions:e=>{F({page:1,size:99999}).then(t=>{e.options=p(t.data.data.data||[])})}},{label:"所属仓库",field:"storehouseId",type:"select-tree",checkStrictly:!0,defaultExpandAll:!0,autoFillOptions:e=>{A({page:1,size:-1}).then(t=>{e.options=p(t.data.data.data||[])})}}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",icon:i.QUERY,click:()=>r()},{type:"default",perm:!0,text:"重置",svgIcon:_(O),click:()=>{var e;(e=c.value)==null||e.resetForm(),r()}},{perm:!0,text:"批量下载",icon:i.EXPORT,click:()=>{var t;const e=[];(t=a.selectList)==null||t.forEach(o=>{e.push(f(o))}),e.length?u(e):h.warning("请选择设备")}}]}]}),a=b({defaultExpandAll:!0,indexVisible:!0,selectList:[],handleSelectChange:e=>{a.selectList=e},columns:[{label:"标签编码",prop:"deviceLabelCode"},{label:"设备名称",prop:"name"},{label:"设备型号",prop:"model"},{label:"所属大类",prop:"topType"},{label:"所属类别",prop:"type"},{label:"供应商",prop:"supplierName"},{label:"所在仓库",prop:"storehouseName"},{label:"存放位置",prop:"shelvesName"}],operationWidth:"200px",operations:[{type:"primary",text:"二维码",icon:i.DETAIL,component:_(w),perm:m("RoleManageEdit")},{type:"primary",text:"下载",perm:m("RoleManageEdit"),icon:i.EXPORT,click:e=>{u([f(e)])}}],dataList:[],pagination:{page:1,limit:20,total:0,refreshData:({page:e,size:t})=>{a.pagination.page=e,a.pagination.limit=t,r()}}}),n=b({title:"",data:[],currentProject:{},expandOnClickNode:!1,isFilterTree:!0,treeNodeHandleClick:e=>{n.currentProject=e,r()}}),r=async()=>{var t;const e={size:a.pagination.limit,page:a.pagination.page,deviceTypeId:n.currentProject.id,...((t=c.value)==null?void 0:t.queryParams)||{}};j(e).then(o=>{a.dataList=o.data.data.data||[],a.pagination.total=o.data.data.total||0})};function v(){M().then(e=>{n.data=p(e.data.data||[]),n.currentProject=e.data.data[0],r()})}function u(e){const t=document==null?void 0:document.getElementsByTagName("canvas"),o=document.createElement("a");e.forEach(l=>{o.href=t[l].toDataURL("img/png")||"",o.download=a.dataList[l].deviceLabelCode||"",o.click()}),h.success("下载中")}function f(e){return a.dataList.indexOf(e)}return R(async()=>{v()}),(e,t)=>{const o=k,l=L,C=T,E=S;return D(),I(E,null,{tree:y(()=>[s(o,{"tree-data":d(n)},null,8,["tree-data"])]),default:y(()=>[s(l,{ref_key:"refSearch",ref:c,config:d(x)},null,8,["config"]),s(C,{id:"cs111",config:d(a),class:"card-table"},null,8,["config"])]),_:1})}}});export{Z as default};
