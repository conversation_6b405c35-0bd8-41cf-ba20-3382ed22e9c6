<!-- 工程管理-详情-签证基本信息 -->
<template>
  <el-card class="card">
    <descriptions :config="basicConfig"></descriptions>
  </el-card>
</template>

<script lang="ts" setup>
const props = defineProps<{ config: any }>();

const basicConfig = reactive<IDescriptionsConfig>({
  defaultValue: computed(() => props.config) as any,
  border: true,
  direction: 'horizontal',
  column: 2,
  title: '签证基本信息',
  fields: [
    { type: 'text', label: '签证编号:', field: 'code' },
    { type: 'text', label: '工程名称:', field: 'constructionName' },
    { type: 'text', label: '施工地点:', field: 'address' },
    { type: 'text', label: '施工单位:', field: 'constructOrganization' },
    { type: 'text', label: '施工日期:', field: 'constructTimeName' },
    { type: 'text', label: '建设单位:', field: 'buildOrganization' },
    { type: 'text', label: '监理单位:', field: 'supervisorOrganization' },
    { type: 'text', label: '审计单位:', field: 'auditOrganization' },
    { type: 'text', label: '添加人:', field: 'creatorName' },
    {
      type: 'text',
      label: '添加时间:',
      field: 'createTime',
      formatter: (row) => dayjs(row).format('YYYY-MM-DD HH:mm:ss')
    }
  ]
});
</script>

<style lang="scss" scoped>
.card {
  margin-bottom: 20px;
}
</style>
