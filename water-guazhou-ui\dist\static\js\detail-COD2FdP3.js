import{d as w,c as L,r as A,o as F,am as W,ay as M,g as c,n as f,p as d,q as t,F as a,G as i,bh as l,h as U,an as E,i as H,t as J,aB as C,aJ as z,b as K,ds as h,cZ as j,c6 as q,c_ as Z,bl as $,cE as Q,bm as X,dt as Y,du as ee,dv as te,C as ae}from"./index-r0dFAfgr.js";/* empty css                         */import{_ as se}from"./index-C9hz-UZb.js";/* empty css                             */import{j as le,k as oe}from"./index-CpGhZCTT.js";import{d as ne,i as V}from"./detailSteps-Bp-QHOXn.js";/* empty css                */const ie={class:"item"},re={class:"item"},de={class:"item"},ce={class:"timeline-item-wrapper"},_e={class:"timeline-item-title"},pe={class:"title"},me={class:"time"},ue={key:0,class:"text"},fe={class:"text-name"},Ee={class:"text-name"},be={key:1,class:"text"},ye=w({__name:"detail",props:{id:{}},setup(D){const I=L("first"),v=D,G=(p,r)=>{console.log(p,r)},e=A({detail:{},timeline:[]}),R=A({status:"",statusName:""}),g=async()=>{var m,u,b,y;const p=v.id||"";if(!p)return K.error("系统错误");e.timeline=[];const r=await le(p);e.detail=((m=r.data)==null?void 0:m.data)||{},R.status=e.detail.status;const n=await oe(p);e.timeline.push({type:"attr-table",data:{typeName:"发起",processUserId:e.detail.organizerId,processRemark:"发起工单",nextProcessUserId:"",processTime:(b=(u=r.data)==null?void 0:u.data)==null?void 0:b.createTime,...e.detail},columns:V("CREATE")});const N=["CREATE","ARRIVING","PROCESSING","SUBMIT","CHARGEBACK_REVIEW","REVIEW","APPROVED","REJECTED","CHARGEBACK","TERMINATED","HANDOVER_REVIEW","REASSIGN"];(y=n.data)==null||y.data.map(_=>{const s=_.processAdditionalInfo&&JSON.parse(_.processAdditionalInfo)||{};s!=null&&s.audioUrl&&(s.audioUrl=h(s.audioUrl)),s!=null&&s.videoUrl&&(s.videoUrl=h(s.videoUrl)),s!=null&&s.otherFileUrl&&(s.otherFileUrl=h(s.otherFileUrl));const T=N.includes(_.type);s.nextProcessUserId&&delete s.nextProcessUserId,e.timeline.unshift({type:T?"attr-table":"text",data:{..._,...s},columns:V(_.type)}),console.log(e.timeline)})};return F(()=>{g()}),W(v,()=>{g()}),(p,r)=>{const n=j,N=q,m=Z,u=$,b=M("User"),y=Q,_=X,s=Y,T=se,P=ee,B=te;return c(),f("div",null,[d("div",ie,[t(ne,{config:R},null,8,["config"])]),d("div",re,[t(_,{modelValue:H(I),"onUpdate:modelValue":r[0]||(r[0]=o=>J(I)?I.value=o:null),class:"demo-tabs",onTabClick:G},{default:a(()=>[t(u,{label:"基础信息",name:"first"},{default:a(()=>[t(m,{title:""},{default:a(()=>[t(n,{label:"工单编号："},{default:a(()=>[i(l(e.detail.serialNo),1)]),_:1}),t(n,{label:"标题："},{default:a(()=>[i(l(e.detail.title),1)]),_:1}),t(n,{label:"来源："},{default:a(()=>[i(l(e.detail.source),1)]),_:1}),t(n,{label:"紧急程度："},{default:a(()=>[t(N,{type:e.detail.level==="紧急"?"warning":e.detail.level==="非常紧急"?"danger":""},{default:a(()=>[i(l(e.detail.level),1)]),_:1},8,["type"])]),_:1}),t(n,{label:"类型："},{default:a(()=>[i(l(e.detail.type),1)]),_:1}),t(n,{label:"发起人："},{default:a(()=>[i(l(e.detail.organizerName),1)]),_:1}),t(n,{label:"业务单号："},{default:a(()=>[i(l(e.detail.serialNo),1)]),_:1}),t(n,{label:"地址："},{default:a(()=>[i(l(e.detail.address),1)]),_:1})]),_:1})]),_:1}),t(u,{label:"处理信息",name:"second"},{default:a(()=>[t(m,{title:""},{default:a(()=>[t(n,{label:"处理人："},{default:a(()=>[e.detail.processUserName?(c(),U(y,{key:0},{default:a(()=>[t(b)]),_:1})):E("",!0),i(" "+l(e.detail.processUserName),1)]),_:1}),t(n,{label:"处理级别："},{default:a(()=>[i(l(e.detail.processLevel&&e.detail.processLevel+"级别"),1)]),_:1}),t(n,{label:"预计完成时间："},{default:a(()=>[i(l(e.detail.estimatedFinishTime),1)]),_:1}),t(n,{label:"完成时间："},{default:a(()=>[i(l(e.detail.completeTime),1)]),_:1})]),_:1})]),_:1})]),_:1},8,["modelValue"])]),d("div",de,[t(B,null,{default:a(()=>[(c(!0),f(C,null,z(e.timeline,(o,O)=>(c(),U(P,{key:O,hollow:!0,type:"primary","hide-timestamp":!0,placement:"top"},{default:a(()=>[d("div",ce,[d("div",_e,[d("span",pe,l(o.data.typeName),1),d("span",me,l(o.data.processTime),1)]),t(T,{class:"timeline-item-content"},{default:a(()=>{var S,k,x;return[o.type==="text"?(c(),f(C,{key:0},[o.data.type==="ASSIGN"?(c(),f("p",ue,[r[1]||(r[1]=i(" 任务派发给了 ")),d("span",fe,l((S=o.data)==null?void 0:S.nextProcessUserName),1),r[2]||(r[2]=i("， 操作人： ")),d("span",Ee,l((k=o.data)==null?void 0:k.processUserName),1)])):E("",!0),o.data.type==="RESOLVING"?(c(),f("p",be,l((x=o.data)==null?void 0:x.nextProcessUserName)+" 接收了工单 ",1)):E("",!0)],64)):E("",!0),(o==null?void 0:o.type)==="attr-table"?(c(),U(s,{key:1,data:o.data,columns:o.columns},null,8,["data","columns"])):E("",!0)]}),_:2},1024)])]),_:2},1024))),128))]),_:1})])])}}}),ge=ae(ye,[["__scopeId","data-v-b7456028"]]);export{ge as default};
