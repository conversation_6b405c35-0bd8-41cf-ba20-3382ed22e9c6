package org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoProject;
import org.thingsboard.server.dao.util.imodel.query.IStarHttpRequest;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
public class SoProjectSaveRequest extends SaveRequest<SoProject> {
    // 编号
    @NotNullOrEmpty
    private String code;

    // 名称
    @NotNullOrEmpty
    private String name;

    // 类别id
    private String typeId;

    // 规模
    private String scale;

    // 单位
    @NotNullOrEmpty
    private String organization;

    // 负责人
    @NotNullOrEmpty
    private String principal;

    // 联系电话
    private String phone;

    // 详细地址
    private String address;

    // 项目概况
    private String remark;

    // 概算，万元
    private BigDecimal estimate;

    // 启动时间
    private Date startTime;

    // 预计结束时间
    private Date expectEndTime;

    // 附件信息
    private String attachments;

    @Override
    public String valid(IStarHttpRequest request) {
        /*if (!code.startsWith("Q")) {
            return "工程编号需要以Q开头";
        }*/

        return null;
    }

    @Override
    protected SoProject build() {
        SoProject entity = new SoProject();
        entity.setCreateTime(createTime());
        entity.setCreator(currentUserUUID());
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    @Override
    protected SoProject update(String id) {
        SoProject entity = new SoProject();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(SoProject entity) {
        entity.setCode(code);
        entity.setName(name);
        entity.setTypeId(typeId);
        entity.setScale(scale);
        entity.setOrganization(organization);
        entity.setPrincipal(principal);
        entity.setPhone(phone);
        entity.setAddress(address);
        entity.setRemark(remark);
        entity.setEstimate(estimate);
        entity.setStartTime(startTime);
        entity.setExpectEndTime(expectEndTime);
        entity.setAttachments(attachments);
        entity.setUpdateTime(createTime());
        entity.setUpdateUser(currentUserUUID());
    }
}