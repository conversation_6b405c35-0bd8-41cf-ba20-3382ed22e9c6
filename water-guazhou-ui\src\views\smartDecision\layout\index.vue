<template>
  <div class="large-screen overlay-y">
    <div ref="refContainer" class="smart-decision">
      <div class="layout-item smart-decision__main">
        <slot> </slot>
      </div>
      <Header
        v-if="showHeaders"
        class="layout-item smart-decision__header"
        :title="title"
        :logo="logo"
        @fullscreen="handleFullScreen"
      ></Header>

      <Footer
        v-if="showBars"
        :bar-items="barItems"
        class="layout-item smart-decision__footer"
        @to="to"
      ></Footer>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { toggleFullscreen } from '@/utils/GlobalHelper';
import Footer from './Footer/index.vue';
import Header from './Header/index.vue';

const emit = defineEmits(['to']);
defineProps<{
  showBars: boolean;
  showHeaders: boolean;
  title?: string;
  logo?: string;
}>();
const refContainer = ref<HTMLDivElement>();
const barItems = ref<ISMART_DECISION_CONFIG_Bar[]>(
  window.SITE_CONFIG.SMART_DECISION_CONFIG.bars || []
);

const to = (path: ISMART_DECISION_CONFIG_Bar) => {
  emit('to', path);
};
const handleFullScreen = async () => {
  if (!refContainer.value) return;
  await toggleFullscreen(refContainer.value);
};
onBeforeMount(() => {
  emit('to', barItems.value[0]);
});
</script>
<style lang="scss" scoped>
.large-screen {
  width: 100%;
  height: 100%;
  user-select: none;
}
.smart-decision {
  width: 1920px;
  height: 1080px;
  position: relative;
  background-color: rgb(0, 10, 20);
  background: url(../imgs/bg_blue.png) 0 0 /100% 100% no-repeat;
  .layout-item {
    position: absolute;
    z-index: 1;
  }
  .smart-decision__footer {
    position: absolute;
    bottom: 0;
  }
  .smart-decision__header {
    top: 0;
  }
  .smart-decision__main {
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
  }
}
</style>
