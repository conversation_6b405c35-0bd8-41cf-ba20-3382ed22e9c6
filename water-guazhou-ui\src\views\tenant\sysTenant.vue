<template>
  <div class="wrapper">
    <CardSearch
      ref="cardSearch"
      class="cardSearch"
      :config="SLCardSearchConfig"
    />
    <CardTable
      class="cardtable"
      :config="SLCardTableConfig"
    />
    <DialogForm
      ref="refDialogForm"
      :config="DialogFormConfig"
      @refreshData="refreshData"
    />
    <TenantManager
      ref="refTenantDialog"
      :config="tenantManagerManage"
    />
  </div>
</template>

<script lang="ts" setup>
import TenantManager from './components/tenantManager.vue'
import { getTenants, deleteTenants, saveTenant } from '@/api/tenant'
import { getAppList } from '@/api/application'
import { SLConfirm, SLMessage } from '@/utils/Message'

import { initSysTenantFormColumn } from '.'
import { ICardSearchIns, IDialogFormIns } from '@/components/type'

const cardSearch = ref<ICardSearchIns>()
const refDialogForm = ref<IDialogFormIns>()
const refTenantDialog = ref<InstanceType<typeof TenantManager>>()
const SLCardSearchConfig = reactive<ISearch>({
  labelWidth: '60px',
  filters: [
    {
      type: 'input',
      field: 'name',
      label: '搜索',
      onChange: () => refreshData()
    },
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          type: 'primary',
          text: '查询',
          click: () => refreshData()
        },
        { perm: true, type: 'primary', text: '添加', click: () => handleAdd() },
        {
          perm: true,
          type: 'warning',
          text: '批量删除',
          click: () => handleDelete()
        }
      ]
    }
  ],
  defaultParams: {}
})
const SLCardTableConfig = reactive<ICardTable>({
  columns: [{ prop: 'name', label: '名称' }],
  dataList: [],
  operationWidth: 400,
  operations: [
    {
      text: '管理企业管理员',
      perm: true,
      icon: 'iconfont icon-icon_fuzhi',
      click: row => {
        tenantManagerManage.currentId = row.id
        refTenantDialog.value?.open()
      }
    },
    {
      text: '企业授权管理',
      perm: true,
      isTextBtn: true,
      icon: 'iconfont icon-icon_fuzhi',
      click: row => {
        tenantManagerManage.currentId = row.id
        refTenantDialog.value?.open()
      }
    },
    {
      text: '编辑',
      // perm: $btnPerms('pc_gateway_deviceTable_edit'),
      perm: true,
      isTextBtn: true,
      icon: 'iconfont icon-bianji',
      click: row => handleAdd(row)
    },
    {
      // perm: $btnPerms('pc_gateway_sensorTable_del'),
      text: '删除',
      perm: true,
      isTextBtn: true,
      icon: 'iconfont icon-shanchu',
      click: row => handleDelete(row)
    }
  ],
  pagination: {
    total: 0,
    page: 1,
    align: 'right',
    limit: 20,
    handlePage: (val: number) => {
      SLCardTableConfig.pagination.page = val
      refreshData()
    },
    handleSize: (val: number) => {
      SLCardTableConfig.pagination.limit = val
      refreshData()
    }
  },
  handleSelectChange: (val: any) => {
    SLCardTableConfig.selectList = val
  }
})
const DialogFormConfig = reactive<IDialogFormConfig>({
  title: '添加企业',
  dialogWidth: 500,
  submit: async (params: any) => {
    try {
      const res = await saveTenant(params)
      if (res.data) {
        SLMessage.success('操作成功')
        refDialogForm.value?.closeDialog()
      } else {
        SLMessage.error('操作失败')
      }
      DialogFormConfig.submitting = false
    } catch (error) {
      DialogFormConfig.submitting = false
    }
  },
  submitting: false,
  defaultValue: {},
  group: []
})
const handleAdd = (row?: any) => {
  try {
    DialogFormConfig.title = row ? '编辑企业' : '添加企业'

    if (row) {
      const additionalInfo = typeof row.additionalInfo === 'string'
        ? JSON.parse(row.additionalInfo)
        : row.additionalInfo
      DialogFormConfig.defaultValue = {
        ...(row ?? {}),
        ...additionalInfo
      }
    } else {
      DialogFormConfig.defaultValue = {}
    }
    refDialogForm.value?.refForm?.resetForm()
    refDialogForm.value?.openDialog()
  } catch (error) {
    console.log(error)
  }
}
const refreshData = async () => {
  SLCardTableConfig.loading = true
  const query = cardSearch.value?.queryParams
  const paramsObj = {
    page: SLCardTableConfig.pagination.page,
    limit: SLCardTableConfig.pagination.limit,
    ...(query || {})
  }
  try {
    const res = await getTenants(paramsObj)
    SLCardTableConfig.dataList = res.data?.data || []
    SLCardTableConfig.pagination.total = res.data?.total || 0
    SLCardTableConfig.loading = false
  } catch (error) {
    SLCardTableConfig.loading = false
  }
}
const tenantManagerManage = reactive<DialogConfig>({
  title: '企业管理员列表',
  visible: false,
  currentId: '',
  close: () => {
    refTenantDialog.value?.close()
  }
})
const handleDelete = (row?: any) => {
  SLConfirm('确定删除指定企业?', '删除提示').then(() => {
    // 删除角色方法
    const ids: string[] | undefined = row
      ? [row.id.id]
      : SLCardTableConfig.selectList?.map(item => item.id?.id)
    if (!ids?.length) {
      SLMessage.warning('请先选择要删除的企业')
      return
    }
    deleteTenants(ids).then(() => {
      SLMessage.success('操作成功')
      refreshData()
    })
  })
}
const initFormColumn = (data?: any[]) => {
  const options = data?.map(item => {
    return {
      label: item.appName,
      value: item.id
    }
  }) || []
  DialogFormConfig.group = initSysTenantFormColumn(options)
}
onMounted(async () => {
  refreshData()
  const res = await getAppList()
  initFormColumn(res.data)
})
</script>

<style lang="scss" scoped>
.wrapper {
  width: 100%;
  height: 100%;
  padding: 15px;
}
.cardtable {
  height: calc(100% - 80px);
}
</style>
