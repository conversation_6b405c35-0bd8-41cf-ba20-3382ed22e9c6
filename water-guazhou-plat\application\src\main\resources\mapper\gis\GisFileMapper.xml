<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.gis.GisFileMapper">
    <select id="findList" resultType="org.thingsboard.server.dao.model.sql.gis.GisFile">
        SELECT
            a.*
        FROM
            tb_gis_file a
        <where>
            <if test="param.tenantId != null and param.tenantId != ''">
                AND a.tenant_id = #{param.tenantId}
            </if>
            <if test="param.keyword != null and param.keyword != ''">
                AND (a.name LIKE '%' || #{param.keyword} || '%' OR a.remark LIKE '%' || #{param.keyword} || '%')
            </if>
            <if test="param.status != null and param.status != ''">
                AND a.status = #{param.status}
            </if>
            <if test="param.fileType != null and param.fileType != ''">
                AND a.file_type = #{param.fileType}
            </if>
            <if test="param.deviceType != null and param.deviceType != ''">
                AND a.device_type = #{param.deviceType}
            </if>
            <if test="param.deviceCode != null and param.deviceCode != ''">
                AND a.device_code = #{param.deviceCode}
            </if>
        </where>
        ORDER BY a.upload_time DESC
    </select>

</mapper>