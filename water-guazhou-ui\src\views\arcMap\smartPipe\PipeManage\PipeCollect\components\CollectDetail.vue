<!--
  工程详情
 -->
<template>
  <div class="tab-box">
    <el-tabs v-model="state.curPage" type="border-card" style="height: 100%">
      <el-tab-pane label="工单信息">
        <detail :id="props.row?.workOrderId"></detail>
      </el-tab-pane>
      <el-tab-pane label="采集信息">
        <Search
          v-if="props.row?.status === ECollectStatus.待审核"
          :config="SearchConfig"
        ></Search>
        <Tabs
          v-if="TabConfig.tabs.length"
          v-model="state.curTab"
          :config="TabConfig"
          style="margin-bottom: 12px"
          @change="refreshTabData"
        ></Tabs>
        <!--
        toDo: 添加操作，定位
       -->
        <FormTable
          class="table-box"
          :class="[
            TabConfig.tabs.length ? 'hasTab' : 'noTab',
            props.row?.status === ECollectStatus.待审核 ? 'hasAudit' : 'noAudit'
          ]"
          :config="TableConfig"
        ></FormTable>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script lang="ts" setup>
import Point from '@arcgis/core/geometry/Point';
import Polyline from '@arcgis/core/geometry/Polyline';
import SimpleLineSymbol from '@arcgis/core/symbols/SimpleLineSymbol';
import Graphic from '@arcgis/core/Graphic';
import PictureMarkerSymbol from '@arcgis/core/symbols/PictureMarkerSymbol';
import detail from '@/views/workorder/components/detail.vue';
import {
  ECollectAuditStatus,
  ECollectStatus,
  ECollectPipeType
} from '../config';
import { SLConfirm, SLMessage, SLPrompt } from '@/utils/Message';
import { formatDate } from '@/utils/DateFormatter';
import {
  AuditPipeCollect,
  DeletePipeCollectData,
  GetPipeCollectDataLayerids
} from '@/api/mapservice/pipeCollection';
import { useGisStore } from '@/store';
import { usePipeLegends } from '@/hooks/arcgis/useLegends';
import {
  getGraphicLayer,
  gotoAndHighLight,
  applyEdits
} from '@/utils/MapHelper';
import { usePipeCollectData } from '../../hooks/usePipeCollect';

const emit = defineEmits(['rowClick', 'audit']);
const view = inject('view') as __esri.MapView;
const props = defineProps<{
  row?: Record<string, any>;
}>();
const state = reactive<{
  layerInfoes: ILayerInfo[];
  curTab: number | undefined;
  curPage?: number;
}>({
  layerInfoes: [],
  curTab: undefined,
  curPage: undefined
});
const SearchConfig = reactive<ISearch>({
  filters: [
    {
      type: 'btn-group',
      label: '审核：',
      field: 'layerid',
      btns: [
        {
          perm: true,
          text: '同意',
          disabled() {
            return TableConfig.loading;
          },
          click: () => handleAudit(ECollectAuditStatus.PASS)
        },
        {
          perm: true,
          text: '拒绝',
          type: 'danger',
          disabled() {
            return TableConfig.loading;
          },
          click: () => handleAudit(ECollectAuditStatus.REJECT)
        }
      ]
    }
  ],
  defaultParams: {}
});
const handleAudit = async (status: ECollectAuditStatus) => {
  try {
    const remark = await SLPrompt('审核意见', '审核');
    if (remark.action !== 'confirm') return;
    if (status === ECollectAuditStatus.PASS) {
      const confirm = await SLConfirm('确定上传到空间数据库？', '提示信息');
      if (confirm !== 'confirm') return;
    }

    TableConfig.loading = true;
    const res = await AuditPipeCollect({
      id: props.row?.id,
      remark: remark.value,
      status
    });

    if (res.data.code !== 200) {
      SLMessage.error(res.data.message || '操作失败');
      TableConfig.loading = false;
      return;
    }
    if (status === ECollectAuditStatus.PASS) {
      SLMessage.success('正在上传...');
      await uploadToSpaceDatabase();
      graphicsLayer?.graphics && view?.goTo(graphicsLayer?.graphics);
    } else {
      SLMessage.success('已拒绝');
    }
    emit('audit', status);
  } catch (error) {
    console.log(error);
  }
  TableConfig.loading = false;
};
const uploadToSpaceDatabase = async () => {
  if (props.row?.status === ECollectStatus.待审核) {
    try {
      const p = await Promise.allSettled(
        legends.legends.value.map((item) => {
          return applyEdits(
            item.layerId,
            {
              addFeatures: graphicsLayer?.graphics
                .filter(
                  (o) => Number(o.attributes.row.layerId) === item.layerId
                )
                .map(
                  (o) =>
                    new Graphic({
                      geometry: o.geometry,
                      attributes: {
                        ...(o.attributes.row || {}),
                        createdate: moment(o.attributes.row.createTime).format(
                          'YYYYMMDD'
                        )
                      }
                    })
                )
            }
            // { rollbackOnFailureEnabled: true }
          );
        })
      );
      p.map((item) => {
        if (item.status === 'fulfilled') {
          if (item.value.addFeatureResults.some((o) => o.error)) {
            console.log(item.value.addFeatureResults);
          } else {
            console.log('添加成功');
          }
        }
      });
    } catch (error) {
      console.log(error);
    }
  }
};
const TabConfig = reactive<ITabs>({
  tabs: [],
  type: 'tabs'
});
const PointColumns = [
  { minWidth: 220, label: '编号', prop: 'sid' },
  { minWidth: 100, label: '名称', prop: 'name' },
  { minWidth: 100, label: '材质', prop: 'material' },
  { minWidth: 100, label: '所在道路', prop: 'laneway' },
  { minWidth: 100, label: '地址', prop: 'address' },
  { minWidth: 100, label: '埋设类型', prop: 'burytype' },
  { minWidth: 100, label: '口径', prop: 'diameter' },
  { minWidth: 100, label: '埋深', prop: 'depth' },
  { minWidth: 100, label: 'x', prop: 'x' },
  { minWidth: 100, label: 'y', prop: 'y' },
  { minWidth: 100, label: 'z', prop: 'z' },
  {
    minWidth: 160,
    label: '创建时间',
    prop: 'createTime',
    formatter: (row, val) => formatDate(val)
  },
  { minWidth: 100, label: '创建人', prop: 'creatorName' }
];
const LineColumns = [
  { minWidth: 220, label: '编号', prop: 'sid' },
  { minWidth: 100, label: '名称', prop: 'name' },
  { minWidth: 220, label: '起点编号', prop: 'start_sid' },
  { minWidth: 220, label: '终点编号', prop: 'end_sid' },
  { minWidth: 100, label: '起点埋深', prop: 'start_depth' },
  { minWidth: 100, label: '终点埋深', prop: 'end_depth' },
  { minWidth: 100, label: '起点高程', prop: 'start_z' },
  { minWidth: 100, label: '终点高程', prop: 'end_z' },
  { minWidth: 100, label: '材质', prop: 'material' },
  { minWidth: 100, label: '所在道路', prop: 'laneway' },
  { minWidth: 100, label: '地址', prop: 'address' },
  { minWidth: 100, label: '埋设类型', prop: 'burytype' },
  { minWidth: 100, label: '口径', prop: 'diameter' },
  {
    minWidth: 160,
    label: '创建时间',
    prop: 'createTime',
    formatter: (row, val) => formatDate(val)
  },
  { minWidth: 100, label: '创建人', prop: 'creatorName' }
];
const TableConfig = reactive<ITable>({
  columns: LineColumns,
  dataList: [],
  operations: [
    { perm: true, type: 'success', text: '定位', click: (row) => goTo(row) },
    {
      perm: () => props.row?.status === ECollectStatus.处理中,
      type: 'danger',
      text: '删除',
      click: (row) => removeRow(row)
    }
  ],
  pagination: {
    hide: true,
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page || 1;
      TableConfig.pagination.limit = size || 20;
      refreshData();
    }
  }
});
const removeRow = async (row: any) => {
  SLConfirm(
    row.type === ECollectPipeType.点
      ? '确定删除点及相关联管线吗？'
      : '确定删除？',
    '提示信息'
  ).then(async () => {
    try {
      const res = await DeletePipeCollectData(
        pipeCollectData.list.value
          .filter(
            (item) =>
              item.id === row.id ||
              item.sid === row.sid ||
              item.start_sid === row.sid ||
              item.end_sid === row.sid
          )
          .map((item) => item.id)
      );
      if (res.data.code === 200) {
        SLMessage.success('删除成功');
        refreshData();
      } else {
        SLMessage.error('删除失败');
      }
    } catch (error) {
      SLMessage.error('删除失败');
      console.log(error);
    }
  });
};
const goTo = (row: any) => {
  emit('rowClick', row);
  const graphic = graphicsLayer?.graphics.find(
    (item) => item.attributes.row.id === row.id
  );
  if (graphic) {
    gotoAndHighLight(view, graphic, { duration: 500, zoom: 13 });
  }
};
const pipeCollectData = usePipeCollectData();

const refreshMap = () => {
  const points: any = [];
  pipeCollectData.list.value.map((item) => {
    if (item.type === ECollectPipeType.线) {
      const startPoint = new Point({
        longitude: item.x,
        latitude: item.y,
        spatialReference: view?.spatialReference
      });
      const endPoint = new Point({
        longitude: item.end_x,
        latitude: item.end_y,
        spatialReference: view?.spatialReference
      });
      points.push(
        new Graphic({
          geometry: new Polyline({
            paths: [
              [
                [startPoint.x, startPoint.y],
                [endPoint.x, endPoint.y]
              ]
            ],
            spatialReference: view?.spatialReference
          }),
          symbol: new SimpleLineSymbol({
            color: '#318DFF',
            style: 'solid', // 线的样式 dash|dash-dot|solid等
            width: 2
          }),
          attributes: {
            row: item
          }
        })
      );
    } else {
      const legend = legends.legendRecords.value[item.layerId];
      const cLegend = legend?.legend[0];
      if (cLegend) {
        points.push(
          new Graphic({
            geometry: new Point({
              longitude: item.x,
              latitude: item.y,
              spatialReference: view?.spatialReference
            }),
            symbol: new PictureMarkerSymbol({
              width: cLegend.width || 20,
              height: cLegend.height || 20,
              url: `${window.SITE_CONFIG.GIS_CONFIG.gisService + window.SITE_CONFIG.GIS_CONFIG.gisPipeDynamicService}/${
                legend.layerId
              }/images/${cLegend.url}`
            }),
            attributes: {
              row: item
            }
          })
        );
      }
    }
  });
  graphicsLayer?.removeAll();
  graphicsLayer?.addMany(points);
};
const refreshTabData = () => {
  const layerInfo = useGisStore().gLayerInfos?.find(
    (item) => item.layerid === state.curTab
  );
  TableConfig.columns =
    layerInfo?.geometrytype === 'esriGeometryPolyline'
      ? LineColumns
      : PointColumns;
  TableConfig.dataList = pipeCollectData.list.value.filter(
    (item) => Number(item.layerId) === state.curTab
  );
};
const refreshData = async () => {
  try {
    if (!props.row?.id) return;
    TableConfig.loading = true;
    await pipeCollectData.getList(props.row.id);

    refreshTabData();
    refreshMap();
    refreshTab();
  } catch (error) {
    console.log(error);
  }
  TableConfig.loading = false;
};
const refreshTab = () => {
  if (state.curPage === 0) return;
  GetPipeCollectDataLayerids(props.row?.id).then((res) => {
    TabConfig.tabs = res.data.data?.map((item) => {
      const layerInfo = useGisStore().gLayerInfos?.find(
        (a) => a.layerid === Number(item)
      );
      console.log(layerInfo);
      return {
        label: layerInfo?.layername,
        value: layerInfo?.layerid
      };
    });
    if (TabConfig.tabs.length) {
      state.curTab = TabConfig.tabs[0].value;
      refreshTabData();
    }
  });
};
const legends = usePipeLegends();
let graphicsLayer: __esri.GraphicsLayer | undefined;
onMounted(async () => {
  await legends.getLegends();
  refreshData();
  graphicsLayer = getGraphicLayer(view, {
    id: 'pipe-collect',
    title: '采集数据'
  });
});
onBeforeUnmount(() => {
  graphicsLayer?.removeAll();
});
</script>
<style lang="scss" scoped>
:deep(.el-tabs__content) {
  height: calc(100% - 40px);
  .el-tab-pane {
    height: 100%;
    overflow-y: auto;
    overflow-y: overlay;
  }
}
.table-box {
  height: 100%;
  &.hasTab {
    height: calc(100% - 52px);
    &.hasAudit {
      height: calc(100% - 92px);
    }
  }
  &.hasAudit {
    height: calc(100% - 40px);
  }
}
</style>
