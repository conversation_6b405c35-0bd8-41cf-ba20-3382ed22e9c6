package org.thingsboard.server.dao.model.DTO;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2023-01-31
 */
@Data
public class CustMeterInfoDTO  {
    
    private String id;
    
    private  String orgName;
    
    private  String meterBookName;
    
    private String code;
    
    private String name;
    
    private String phone;
    
    private String address;
    
    private  String waterCategoryName;
    
    private  String industryCategoryName;

    private String statusName;

    private Integer meterCopyOrderNumber;

    private String priceCode;

    private String priceName;

    private String priceType;

    private BigDecimal balance;

    private String copyUserName;

    private String meterCode;

    private Date changeMeterTime;

    private String caliberName;

    private String typeName;

    private String brandName;

    private BigDecimal currentRead;

    private String installAddress;

    private String steelSealNumber;

    private String wellCode;

    private BigDecimal frozenBalance;

    private String paymentMethodName;

}
