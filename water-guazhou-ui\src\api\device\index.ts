import request from '@/plugins/axios';

/** get DeviceByID */
export function getDeviceByID(deviceId) {
  return request({
    url: `/api/device/${deviceId}`,
    method: 'get'
  });
}

/** delete Device */
export function deleteDevice(deviceid) {
  return request({
    url: `/api/device/${deviceid}?needUpdateGateway=true`,
    method: 'delete'
  });
}

// 获取所有设备网关  info
export function getDevicesAll(key) {
  return request({
    url: `/api/tenant/devicesAll?key=${key}`,
    method: 'get'
  });
}

export function getDevicesListByGatewayId(getwayId, params) {
  return request({
    url: `/api/gateway/devices/page/${getwayId}`,
    method: 'get',
    params
  });
}

/**
 * 复制网关
 * -
 * @param {*} gatewayId 复制的网关的ID
 * @param {*} projectId 复制到哪个项目
 */
export function copyGateway(gatewayId, projectId) {
  return request({
    url: `/api/copy/gateway/${gatewayId}/${projectId}`,
    method: 'POST'
  });
}

/**
 * 删除网关并且同步删除该网关下的采集器
 * -
 * @param {*} gatewayId 要删除的网关id
 */
export function deleteGatewayAndDevice(gatewayId) {
  return request({
    url: `/api/delete/gateway/${gatewayId}`,
    method: 'DELETE'
  });
}

/**
 * 录入数据
 *
 * @param {*} params 录入的数据列表
 */
export function addData(params) {
  return request({
    url: '/api/data/maintenance/addData',
    method: 'POST',
    data: params
  });
}

// 设备变量设备信息 包含组下对应变量 /api/deviceTemplate/protocol/group/{templateId}
export function getDeviceVarGroup(templateId) {
  return request({
    url: `/api/deviceTemplate/protocol/group/${templateId}`,
    method: 'GET'
  });
}

// device-t-controller      device teplate 设备模板/api

// 新增设备模板  仅模板操作，不涉及属性
export function addTemplate(params) {
  return request({
    url: '/api/deviceTemplate',
    method: 'post',
    data: params
  });
}

// 修改设备模板
export function editTemplate(params) {
  return request({
    url: '/api/deviceTemplate/edit',
    method: 'post',
    data: params
  });
}

// 查询设备模板（包含协议详情）
export function getTemplateProtocol(id) {
  return request({
    url: `/api/deviceTemplate/protocol/${id}`,
    method: 'get'
  });
}

// 查询设备模板列表（不包含协议详情）
export function getTemplateList() {
  return request({
    url: '/api/deviceTemplate/list',
    method: 'get'
  });
}

// 按协议类型查询设备模板列表
export function getTemplateListByType(type) {
  return request({
    url: `/api/deviceTemplate/list/${type}`,
    method: 'get'
  });
}

// 删除模板信息
export function delTemplate(id) {
  return request({
    url: `/api/deviceTemplate/${id}`,
    method: 'delete'
  });
}

// 保存设备模板及协议信息
export function saveTemplateProtocol(params) {
  return request({
    url: '/api/deviceTemplate/save/deviceTemplateAndProtocol',
    method: 'post',
    data: params
  });
}

// 复制设备模板及协议信息
export function copyTemplateProtocol(templateId) {
  return request({
    url: `/api/deviceTemplate/copy/${templateId}`,
    method: 'post'
  });
}

// 导出
export function exportTemplateProtocol(templateIds) {
  return request({
    url: `/api/deviceTemplate/export?id=${templateIds}`,
    method: 'get'
  });
}

// ================ 关联获取设备信息 ====================
// 获取网关和对应的采集器
export function getHostOrDevice(projectId) {
  return request({
    url: `/api/tenant/deviceSearchTree/${projectId}`,
    method: 'get'
  });
}

// 按协议类型获取网关和对应的采集器
export function getHostOrDeviceByType(projectId, type, params) {
  return request({
    url: `/api/tenant/deviceSearchTree/${projectId}/${type}`,
    method: 'get',
    params
  });
}

// 获取项目下的设备列表（不包含网关）
export function getDevice(projectId) {
  return request({
    url: `/api/project/devices/${projectId}`,
    method: 'get'
  });
}

// 获取项目下的网关列表
export function getGateway(projectId) {
  return request({
    url: `/api/project/gateway/${projectId}`,
    method: 'get'
  });
}

// =============== 单独  数据监测用 ==============
export function getData(deviceId) {
  return request({
    url: `/api/device/fullData/${deviceId}`,
    method: 'get'
  });
}

// 分页数据监测
export function getDatasList(deviceId, params) {
  return request({
    url: `/istar/api/device/fullData/page/${deviceId}`,
    method: 'get',
    params
  });
}

// 1.2. 网关新建 也是网关 POST /api/device
export function createdHostDevice(params) {
  return request({
    url: '/api/device',
    method: 'post',
    data: params
  });
}

// 项目操作员新建 post /api/newUser   authority为CUSTOMER_USER
export function addUser(params) {
  return request({
    url: '/api/newUser',
    method: 'post',
    data: params
  });
}

/**
 * 获取设备属性列表
 * @param deviceId 设备id
 * @returns
 */
export const getAttrListData = (deviceId: string) =>
  request({
    url: `/api/deviceTemplate/protocol/list/${deviceId}`,
    method: 'get'
  });
/**
 * 添加采集器
 * @param params
 * @returns
 */
export const addSensor = (params: any) =>
  request({
    url: '/api/device?needUpdateGateway=true',
    method: 'post',
    data: params
  });

// 设备型号列表
export const getDeviceModelList = (params: {
  name: string;
  brand: string;
  page: number | undefined | string;
  size: number | undefined | string;
}) =>
  request({
    url: '/api/device/modelInfo/list',
    method: 'get',
    params
  });
