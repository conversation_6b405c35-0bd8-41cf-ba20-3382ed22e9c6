<template>
  <el-button
    :icon="Search"
    @click="extentTo"
  >
    缩放至
  </el-button>
</template>
<script lang="ts" setup>
import { Search } from '@element-plus/icons-vue'

const props = defineProps<{
  visible?: boolean
  config: {
    id: string
    extentTo:(id: string) => void
  }
}>()
const extentTo = () => {
  props.config.extentTo?.(props.config.id)
}
</script>
<style lang="scss" scoped>
.pop-image {
  width: 400px;
}
.content {
  padding-right: 8px;
  width: 100%;
  height: 200px;
}
</style>
