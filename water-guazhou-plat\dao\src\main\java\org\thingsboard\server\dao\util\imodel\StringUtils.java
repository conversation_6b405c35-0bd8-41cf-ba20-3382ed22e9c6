package org.thingsboard.server.dao.util.imodel;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;

public class StringUtils {
    @Deprecated
    public static boolean isNullOrBlank(String str) {
        return org.apache.commons.lang3.StringUtils.isEmpty(str) || org.apache.commons.lang3.StringUtils.isBlank(str);
    }

    public static boolean isNullOrEmpty(String str) {
        return str == null || str.length() == 0;
    }

    public static String generateRandomFileName(String suffix) {
        String fileName = IdWorker.get32UUID();
        if (suffix != null) {
            return fileName + "." + suffix;
        }

        return fileName;
    }
}
