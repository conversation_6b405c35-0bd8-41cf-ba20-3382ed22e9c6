package org.thingsboard.server.dao.model.sql.maintainCircuit.circuit;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.TB_STATION_CIRCUIT_TASK_TABLE)
@TableName(ModelConstants.TB_STATION_CIRCUIT_TASK_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class StationCircuitTask {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.TB_STATION_CIRCUIT_TASK_CODE)
    private String code;

    @Column(name = ModelConstants.TB_STATION_CIRCUIT_TASK_NAME)
    private String name;

    @Column(name = ModelConstants.TB_STATION_CIRCUIT_TASK_TYPE)
    private String type;

    @Column(name = ModelConstants.TB_STATION_CIRCUIT_TASK_STATION_ID)
    private String stationId;

    @Column(name = ModelConstants.TB_STATION_CIRCUIT_TASK_OPTION_DEP)
    private String optionDep;

    @Column(name = ModelConstants.TB_STATION_CIRCUIT_TASK_OPTION_USER_ID)
    private String optionUserId;

    @Column(name = ModelConstants.TB_STATION_CIRCUIT_TASK_START_TIME)
    private Date startTime;

    @Column(name = ModelConstants.TB_STATION_CIRCUIT_TASK_END_TIME)
    private Date endTime;

    @Column(name = ModelConstants.TB_STATION_CIRCUIT_TASK_REAL_START_TIME)
    private Date realStartTime;

    @Column(name = ModelConstants.TB_STATION_CIRCUIT_TASK_REAL_END_TIME)
    private Date realEndTime;

    @Column(name = ModelConstants.TB_STATION_CIRCUIT_TASK_STATUS)
    private String status;

    @Column(name = ModelConstants.TB_STATION_CIRCUIT_TASK_AUDIT_DEP)
    private String auditDep;

    @Column(name = ModelConstants.TB_STATION_CIRCUIT_TASK_AUDIT_USER_ID)
    private String auditUserId;

    @Column(name = ModelConstants.TB_STATION_CIRCUIT_TASK_AUDIT_RESULT)
    private String auditResult;

    @Column(name = ModelConstants.TB_STATION_CIRCUIT_TASK_AUDIT_REMARK)
    private String auditRemark;

    @Column(name = ModelConstants.TB_STATION_CIRCUIT_TASK_AUDIT_TIME)
    private Date auditTime;

    @Column(name = ModelConstants.TB_STATION_CIRCUIT_PLAN_SCHEME_ID)
    private String schemeId;

    @Column(name = ModelConstants.CREATE_TIME)
    private Date createTime;

    @Column(name = ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;

    @Transient
    @TableField(exist = false)
    private String auditDepName;

    @Transient
    @TableField(exist = false)
    private String auditUserName;

    @Transient
    @TableField(exist = false)
    private String optionDepName;

    @Transient
    @TableField(exist = false)
    private String optionUserName;

    @Transient
    @TableField(exist = false)
    private String stationName;

    public static List<StationCircuitTask> build(StationCircuitPlan plan) {
        String stationIds = plan.getStationIds();
        List<StationCircuitTask> taskList = new ArrayList<>();
        for (String stationId : stationIds.split(",")) {
            StationCircuitTask task = new StationCircuitTask();
            task.setName(plan.getName());
            task.setType(plan.getType());
            task.setStationId(stationId);
            task.setOptionDep(plan.getOptionDep());
            task.setOptionUserId(plan.getOptionUserId());
            task.setStartTime(plan.getStartTime());
            task.setEndTime(plan.getEndTime());
            task.setStatus(DataConstants.STATION_CIRCUIT_TASK_STATUS.NEW.getValue());
            task.setAuditDep(plan.getAuditDep());
            task.setAuditUserId(plan.getAuditUserId());
            task.setTenantId(plan.getTenantId());
            task.setCreateTime(plan.getCreateTime());
            task.setSchemeId(plan.getSchemeId());

            taskList.add(task);
        }

        return taskList;
    }


}
