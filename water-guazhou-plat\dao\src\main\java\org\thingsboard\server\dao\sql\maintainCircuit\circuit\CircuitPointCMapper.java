package org.thingsboard.server.dao.sql.maintainCircuit.circuit;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.maintainCircuit.circuit.CircuitPointC;

import java.util.Date;
import java.util.List;

/**
 * userMybatis
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-08-23
 */
@Mapper
public interface CircuitPointCMapper extends BaseMapper<CircuitPointC> {

    List<CircuitPointC> getList(@Param("mainId") String mainId);

    List<CircuitPointC> getListByMainIdIn(List<String> midList);
}
