<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartOperation.construction.project.SoProjectAcceptMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->
        accept.id,
        project.code       as project_code,
        project.name       as project_name,
        project.start_time as project_start_time,
        accept.accept_time,
        accept.remark,
        accept.attachments,
        accept.creator,
        accept.create_time,
        accept.update_user,
        accept.update_time,
        accept.tenant_id
        <!--@sql from so_project_accept accept, so_project project -->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.smartOperation.project.SoProjectAccept">
        <result column="id" property="id"/>
        <result column="project_code" property="projectCode"/>
        <result column="project_name" property="projectName"/>
        <result column="project_start_time" property="projectStartTime"/>
        <result column="accept_time" property="acceptTime"/>
        <result column="remark" property="remark"/>
        <result column="attachments" property="attachments"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>
    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from so_project project
                 left join so_project_accept accept
                           on project.code = accept.project_code and project.tenant_id = accept.tenant_id
        <where>
            <if test="projectCode != null and projectCode != ''">
                and project_code like '%' || #{projectCode} || '%'
            </if>
            <if test="projectName != null and projectName != ''">
                and project.name like '%' || #{projectName} || '%'
            </if>
            and project.tenant_id = #{tenantId}
        </where>
        order by project.create_time desc
    </select>

    <update id="update">
        update so_project_accept
        <set>
            <if test="acceptTime != null">
                accept_time = #{acceptTime},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="attachments != null">
                attachments = #{attachments},
            </if>
        </set>
        where id = #{id}
    </update>

    <update id="updateFully">
        update so_project_accept
        set accept_time = #{acceptTime},
            remark      = #{remark},
            attachments = #{attachments}
        where id = #{id}
    </update>

    <insert id="save">
        INSERT INTO so_project_accept(id,
                                      project_code,
                                      accept_time,
                                      remark,
                                      attachments,
                                      creator,
                                      create_time,
                                      update_user,
                                      update_time,
                                      tenant_id)
        VALUES (#{id},
                #{projectCode},
                #{acceptTime},
                #{remark},
                #{attachments},
                #{creator},
                #{createTime},
                #{updateUser},
                #{updateTime},
                #{tenantId})
    </insert>

    <select id="getIdByProjectCodeAndTenantId" resultType="java.lang.String">
        select id
        from so_project_accept
        where project_code = #{projectCode}
          and tenant_id = #{tenantId}
    </select>
</mapper>