import{d as q,c as g,r as x,b as n,g as I,h as _,F as C,q as N,i as O,_ as L,X as M}from"./index-r0dFAfgr.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import{a as S}from"./LayerHelper-Cn-iiqxI.js";import{g as V}from"./QueryHelper-ILO3qZqg.js";import{GetFieldConfig as B,GetFieldUniqueValue as D}from"./fieldconfig-Bk3o1wi7.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import R from"./RightDrawerMap-D5PhmGFO.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./project-DUuzYgGl.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./pipe-nogVzCHG.js";import"./executeForIds-BLdIsxvI.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./ArcView-DpMnCY82.js";import"./ViewHelper-BGCZjxXH.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./ArcStationWarning-BF9YrSzF.js";/* empty css                         */import"./useWaterPoint-Bv0z6ym6.js";import"./DateFormatter-Bm9a68Ax.js";import"./zhandian-YaGuQZe6.js";import"./useStation-DJgnSZIA.js";import"./DrawerBox-CLde5xC8.js";import"./SideDrawer-CBntChyn.js";import"./PipeDetail-CTBPYFJW.js";import"./Search-NSrhrIa_.js";import"./FormTableColumnFilter-BT7pLXIC.js";import"./config-fy91bijz.js";import"./ArcBRTools-BO92yznB.js";import"./PipeLength.vue_vue_type_script_setup_true_lang-BZfUsvDf.js";import"./arcWidgetButton-0glIxrt7.js";import"./StatisticsHelper-D-s_6AyQ.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./wfsUtils-DXofo3da.js";import"./usePipelineGroup-Ba1pmWz_.js";import"./GroupLayer-DhhN3FyN.js";import"./lazyLayerLoader-DbM9sT1W.js";import"./WFSLayer-1TtJp3nx.js";import"./clientSideDefaults-VQhQaYxh.js";import"./QueryEngineCapabilities-Dk3NJwmm.js";import"./wfsUtils-Du031XaC.js";import"./geojson-MBFu2-HZ.js";import"./xmlUtils-CtUoQO7q.js";import"./ListWindow-WS05QqV0.js";import"./PopLayout-BP55MvL7.js";import"./PoiSearchV2-D7yNeLuv.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./URLHelper-B9aplt5w.js";import"./useLayerList-DmEwJ-ws.js";import"./useScaleBar-Beed-z91.js";const zr=q({__name:"MudValves",setup(G){const o=g(),d=g(),c={},r=x({loading:!1,tabs:[],layerIds:[],layerInfos:[]}),u=x({group:[{fieldset:{desc:"选择字段"},fields:[{type:"list",data:[],className:"sql-list-wrapper",setData:async(t,i)=>{var m,l,f,y;if(!((m=i.layerid)!=null&&m.length))return;const e=i.layerid[0],p=(l=r.layerInfos.find(k=>k.layerid===e))==null?void 0:l.layername;if(!p)return;const s=await B(p);t.data=(y=(f=s.data)==null?void 0:f.result)==null?void 0:y.rows},setDataBy:"layerid",displayField:"alias",valueField:"name",highlightCurrentRow:!0,nodeClick:t=>{r.curFieldNode=t,a(t.name)}}]},{id:"field-construct",fieldset:{desc:"属性过滤"},fields:[{type:"btn-group",size:"small",style:{width:"40%",display:"flex",flexWrap:"wrap"},className:"sql-btns-wrapper",btns:[{perm:!0,text:"=",styles:{margin:"6px",width:"50px"},click:()=>{a("=")}},{perm:!0,text:"模糊",styles:{margin:"6px",width:"50px"},click:()=>{a("like '%替换此处%'")}},{perm:!0,text:">",styles:{margin:"6px",width:"50px"},click:()=>{a(">")}},{perm:!0,text:"<",styles:{margin:"6px",width:"50px"},click:()=>{a("<")}},{perm:!0,text:"非",styles:{margin:"6px",width:"50px"},click:()=>{a("<>")}},{perm:!0,text:"并且",styles:{margin:"6px",width:"50px"},click:()=>{a("and")}},{perm:!0,text:"或者",styles:{margin:"6px",width:"50px"},click:()=>{a("or")}},{perm:!0,text:"%",styles:{margin:"6px",width:"50px"},click:()=>{a("%")}}],extraFormItem:[{type:"list",wrapperStyle:{width:"60%",height:"144px"},className:"sql-list-wrapper",field:"uniqueValue",data:[],nodeClick:t=>{a("'"+t+"'")},filters:[{type:"btn-group",btns:[{perm:!0,text:()=>r.curOperate==="uniqueing"?"正在获取唯一值":"获取唯一值",loading:()=>r.curOperate==="uniqueing",disabled:()=>r.curOperate==="detailing",styles:{width:"100%",borderRadius:"0"},click:()=>h()}]}]}]}]},{fieldset:{desc:"组合查询条件"},fields:[{type:"textarea",field:"sql",placeholder:"OBJECTID > 0"},{type:"btn-group",itemContainerStyle:{marginBottom:"8px"},btns:[{perm:!0,text:"清除组合条件",type:"danger",disabled:()=>r.curOperate==="detailing",click:()=>w(),styles:{width:"100%"}}]},{type:"btn-group",btns:[{perm:!0,text:"查询",disabled:()=>r.layerIds.length===0,click:()=>v(),styles:{width:"100%"}}]}]}],labelPosition:"top",gutter:12,defaultValue:{layerid:[]}}),a=t=>{var e;if(!o.value)return;(e=o.value)!=null&&e.dataForm||(o.value.dataForm={});const i=o.value.dataForm.sql||" ";o.value.dataForm.sql=i+t+" "},w=()=>{var t;(t=o.value)!=null&&t.dataForm&&(o.value.dataForm.sql="")},h=async()=>{var i,e;if(!r.curFieldNode)return;const t=(i=o.value)==null?void 0:i.dataForm.layerid;if(!(t!=null&&t.length)){n.warning("请先选择一个图层");return}r.curOperate="uniqueing";try{const p=await D({layerid:t[0],field_name:r.curFieldNode.name}),s=(e=u.group.find(l=>l.id==="field-construct"))==null?void 0:e.fields[0].extraFormItem,m=s&&s[0];m&&(m.data=p.data.result.rows)}catch{n.error("获取唯一值失败")}r.curOperate=""},F=async()=>{var i,e;if(r.layerIds=S(c.view,void 0,void 0,"排泥阀"),!r.layerIds.length){n.warning("当前没有排泥阀");return}const t=await M(r.layerIds);r.layerInfos=((e=(i=t.data)==null?void 0:i.result)==null?void 0:e.rows)||[],o.value&&(o.value.dataForm.layerid=r.layerIds)},v=async()=>{var i,e,p;const{layerid:t}=((i=o.value)==null?void 0:i.dataForm)||{};if(!(t!=null&&t.length)){n.warning("排泥阀服务不存在");return}r.tabs=await V(t||[],r.layerInfos,{where:((e=o.value)==null?void 0:e.dataForm.sql)||"1=1"}),(p=d.value)==null||p.refreshDetail(r.tabs)},b=async t=>{c.view=t,await F()};return(t,i)=>{const e=L;return I(),_(R,{ref_key:"refMap",ref:d,title:"排泥阀展示",onMapLoaded:b},{default:C(()=>[N(e,{ref_key:"refForm",ref:o,config:O(u)},null,8,["config"])]),_:1},512)}}});export{zr as default};
