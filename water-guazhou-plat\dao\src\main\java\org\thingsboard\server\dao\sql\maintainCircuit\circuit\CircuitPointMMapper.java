package org.thingsboard.server.dao.sql.maintainCircuit.circuit;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.maintainCircuit.circuit.CircuitPointM;

import java.util.List;

/**
 * userMybatis
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-08-23
 */
@Mapper
public interface CircuitPointMMapper extends BaseMapper<CircuitPointM> {

    List<CircuitPointM> getList(@Param("name") String name, @Param("page") int page, @Param("size") int size, @Param("tenantId") String tenantId);

    int getListCount(@Param("name") String name, @Param("tenantId") String tenantId);

    CircuitPointM getById(@Param("mainId") String mainId);
}
