<template>
  <div class="timer">
    <template v-for="item in timers" :key="item">
      <span class="value">{{ item.label }}</span>
      <span v-if="item.unit" class="unit">{{ item.unit }}</span>
    </template>
  </div>
</template>
<script lang="ts" setup>
import { padStart } from 'lodash-es';

const timers = ref<
  {
    label: string;
    unit: string;
  }[]
>([]);
const filterWeekName = () => {
  const newDate = new Date();
  const weekday = [
    '星期日',
    '星期一',
    '星期二',
    '星期三',
    '星期四',
    '星期五',
    '星期六'
  ];
  return weekday[newDate.getDay()];
};
onBeforeMount(() => {
  timers.value = [
    {
      label: padStart(moment().get('year').toString(), 4, '0'),
      unit: '年'
    },
    {
      label: padStart((moment().get('M') + 1).toString(), 2, '0'),
      unit: '月'
    },
    {
      label: padStart(moment().get('D').toString(), 2, '0'),
      unit: '日'
    },
    {
      label: filterWeekName(),
      unit: ''
    }
  ];
});
</script>
<style lang="scss" scoped>
.timer {
  font-family: 'PingFang SC';
  font-style: normal;
  font-weight: 600;
  font-size: 18px;
  line-height: 25px;

  color: #ffffff;

  .unit {
    color: #b8d2ff;
    font-weight: 400;
  }
  .value,
  .unit {
    margin-left: 8px;
  }
}
</style>
