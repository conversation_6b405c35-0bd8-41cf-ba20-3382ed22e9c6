package org.thingsboard.server.dao.model.sql.smartOperation.device;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseUsername;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.util.Date;


@Getter
@Setter
@ResponseEntity
public class SoDevice {
    // id
    private String id;

    // 设备编码
    private String serialId;

    // 设备类别编码
    private String typeSerialId;

    // 设备类别名称
    private String typeName;

    // 顶级设备类别编码
    private String topTypeSerialId;

    // 顶级设备类别名称
    private String topTypeName;

    // 名称
    private String name;

    // 型号
    private String model;

    // 单位
    private String unit;

    // 标识
    private String mark;

    // 排序编号
    private Integer orderNum;

    // 备注
    private String remark;

    // 创建人
    @ParseUsername
    private String creator;

    // 创建时间
    private Date createTime;

    // 客户id
    private String tenantId;

}
