package org.thingsboard.server.dao.model.sql.workOrder;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class WorkOrderCountOfStatusResponse {
    // 待接收数量
    private int waitingCount;

    // 已接收(未完成)数量
    private int receiveCount;

    public WorkOrderCountOfStatusResponse() {
    }

    public WorkOrderCountOfStatusResponse(int waitingCount, int receiveCount) {
        this.waitingCount = waitingCount;
        this.receiveCount = receiveCount;
    }
}
