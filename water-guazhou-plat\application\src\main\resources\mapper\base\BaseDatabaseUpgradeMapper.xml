<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.base.BaseDatabaseUpgradeMapper">
    
    <resultMap type="org.thingsboard.server.dao.model.sql.base.BaseDatabaseUpgrade" id="BaseDatabaseUpgradeResult">
        <result property="id"    column="id"    />
        <result property="versionFrom"    column="version_from"    />
        <result property="versionTo"    column="version_to"    />
        <result property="executionTime"    column="execution_time"    />
        <result property="operatorId"    column="operator_id"    />
        <result property="status"    column="status"    />
        <result property="sqlFilePath"    column="sql_file_path"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectBaseDatabaseUpgradeVo">
        select id, version_from, version_to, execution_time, operator_id, status, sql_file_path, remark from base_database_upgrade
    </sql>

    <select id="selectBaseDatabaseUpgradeList" parameterType="org.thingsboard.server.dao.model.sql.base.BaseDatabaseUpgrade" resultMap="BaseDatabaseUpgradeResult">
        <include refid="selectBaseDatabaseUpgradeVo"/>
        <where>  
            <if test="versionFrom != null  and versionFrom != ''"> and version_from like concat('%', #{versionFrom}, '%')</if>
            <if test="versionTo != null  and versionTo != ''"> and version_to like concat('%', #{versionTo}, '%')</if>
            <if test="executionTime != null "> and execution_time = #{executionTime}</if>
            <if test="operatorId != null  and operatorId != ''"> and operator_id = #{operatorId}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="sqlFilePath != null  and sqlFilePath != ''"> and sql_file_path = #{sqlFilePath}</if>
        </where>
    </select>
    
    <select id="selectBaseDatabaseUpgradeById" parameterType="String" resultMap="BaseDatabaseUpgradeResult">
        <include refid="selectBaseDatabaseUpgradeVo"/>
        where id = #{id}
    </select>

    <insert id="insertBaseDatabaseUpgrade" parameterType="org.thingsboard.server.dao.model.sql.base.BaseDatabaseUpgrade">
        insert into base_database_upgrade
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="versionFrom != null">version_from,</if>
            <if test="versionTo != null">version_to,</if>
            <if test="executionTime != null">execution_time,</if>
            <if test="operatorId != null">operator_id,</if>
            <if test="status != null">status,</if>
            <if test="sqlFilePath != null">sql_file_path,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="versionFrom != null">#{versionFrom},</if>
            <if test="versionTo != null">#{versionTo},</if>
            <if test="executionTime != null">#{executionTime},</if>
            <if test="operatorId != null">#{operatorId},</if>
            <if test="status != null">#{status},</if>
            <if test="sqlFilePath != null">#{sqlFilePath},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateBaseDatabaseUpgrade" parameterType="org.thingsboard.server.dao.model.sql.base.BaseDatabaseUpgrade">
        update base_database_upgrade
        <trim prefix="SET" suffixOverrides=",">
            <if test="versionFrom != null">version_from = #{versionFrom},</if>
            <if test="versionTo != null">version_to = #{versionTo},</if>
            <if test="executionTime != null">execution_time = #{executionTime},</if>
            <if test="operatorId != null">operator_id = #{operatorId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="sqlFilePath != null">sql_file_path = #{sqlFilePath},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBaseDatabaseUpgradeById" parameterType="String">
        delete from base_database_upgrade where id = #{id}
    </delete>

    <delete id="deleteBaseDatabaseUpgradeByIds" parameterType="String">
        delete from base_database_upgrade where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>