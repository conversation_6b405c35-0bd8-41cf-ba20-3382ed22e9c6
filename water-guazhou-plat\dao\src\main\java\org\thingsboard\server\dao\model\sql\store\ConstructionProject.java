package org.thingsboard.server.dao.model.sql.store;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.sql.department.DepartmentMapper;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseTenantName;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseUsername;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseViaMapper;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.util.Date;

@Getter
@Setter
@ResponseEntity
public class ConstructionProject {
    // id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    // 项目编码
    private String code;

    // 项目名称
    private String name;

    // 项目地址
    private String address;

    // 施工部门ID
    @ParseViaMapper(DepartmentMapper.class)
    private String orgId;

    // 施工方
    private String constructionSide;

    // 项目添加时间
    private Date insertTime;

    // 施工开始时间
    private Date startTime;

    // 施工结束时间
    private Date endTime;

    // 创建人
    @ParseUsername
    private String creator;

    // 创建时间
    private Date createTime;

    // 更新时间
    private Date updateTime;

    // 租户ID
    @ParseTenantName
    private String tenantId;

}
