import{a7 as s}from"./index-r0dFAfgr.js";function f(e){const n=["#47A2FF ","#53C8D1","#59CB74","#FBD444","#7F6AAD","#585247"];return{tooltip:{trigger:"item"},legend:{type:"scroll",orient:"vertical",right:"10%",top:"center",selectedMode:!1,icon:"pin",data:(e==null?void 0:e.name)||[],textStyle:{color:"#77899c",rich:{unum:{color:"#4ed139",width:40,align:"right"}}},formatter(o){var r;const i=((e==null?void 0:e.name)||[]).indexOf(o);return`{uname|${o}} {unum|${((r=e==null?void 0:e.data)==null?void 0:r[i])||0}次}`}},color:n,series:[{name:"报警",type:"pie",radius:[0,90],center:["30%","50%"],label:{show:!1},labelLine:{show:!1},data:((e==null?void 0:e.data)||[]).map((o,i)=>{var r;return{name:(r=e==null?void 0:e.name)==null?void 0:r[i],value:o}})}]}}function u(e){const n=["#f36c6c","#e6cf4e","#20d180","#0093ff"];let l;const o=[{value:36,name:"系列一"},{value:54,name:"系列二"},{value:29,name:"系列三"},{value:25,name:"系列四"},{value:55,name:"系列五"},{value:69,name:"系列6"},{value:75,name:"系列7"},{value:85,name:"系列8"}],i=new Array(o.length).fill(100);return{legend:{show:!1},grid:{left:0,right:0,containLabel:!0},xAxis:{show:!1,type:"value"},yAxis:[{type:"category",inverse:!0,axisLine:{show:!1},axisTick:{show:!1},axisPointer:{label:{show:!0,margin:30}},data:o.map(t=>t.name),axisLabel:{margin:100,fontSize:14,align:"left",color:"#333",rich:{a1:{color:"#fff",backgroundColor:n[0],width:30,height:30,align:"center",borderRadius:2},a2:{color:"#fff",backgroundColor:n[1],width:30,height:30,align:"center",borderRadius:2},a3:{color:"#fff",backgroundColor:n[2],width:30,height:30,align:"center",borderRadius:2},b:{color:"#fff",backgroundColor:n[3],width:30,height:30,align:"center",borderRadius:2}},formatter(t){let a=o.map(c=>c.name).indexOf(t);return a+=1,a-1<3?["{a"+a+"|"+a+"}  "+t].join(`
`):["{b|"+a+"}  "+t].join(`
`)}}},{type:"category",inverse:!0,axisTick:"none",axisLine:"none",show:!0,data:o.map(t=>t.value),axisLabel:{show:!0,fontSize:14,color:"#333",formatter:"{value} 次"}}],series:[{z:2,name:"value",type:"bar",barWidth:20,zlevel:1,data:o.map((t,a)=>(l={color:a>3?n[3]:n[a]},{value:t.value,itemStyle:l})),label:{show:!1,position:"right",color:"#333333",fontSize:14,offset:[10,0]}},{name:"背景",type:"bar",barWidth:20,barGap:"-100%",itemStyle:{normal:{color:"rgba(118, 111, 111, 0.55)"}},data:i}]}}function h(e,n,l="0"){const o=[],i=[];return l==="1"?e==null||e.forEach(t=>{o.push(t.dayTimeKey),i.push(t.value)}):e&&e.forEach(t=>{t.dayDataList.forEach(a=>{o.push(`${t.dayTimeKey}: ${a.ts}`),i.push(a.value)})}),{tooltip:{trigger:"axis"},xAxis:{type:"category",data:o||[]},yAxis:{type:"value"},grid:{top:"40",left:"1%",right:"1%",bottom:"16%",containLabel:!0},legend:{itemGap:50,data:[n||""]},dataZoom:[{type:"inside",start:0,end:100},{start:0,end:10}],series:[{name:n||"",type:"line",symbolSize:6,label:{show:!0,position:"top",textStyle:{color:"#fff"}},itemStyle:{normal:{color:"#28ffb3"}},areaStyle:{normal:{color:new s(0,0,0,1,[{offset:0,color:"rgba(0,154,120,0.6)"},{offset:1,color:"rgba(0,0,0, 0)"}],!1)}},data:i||[]}]}}export{u as a,h as d,f as s};
