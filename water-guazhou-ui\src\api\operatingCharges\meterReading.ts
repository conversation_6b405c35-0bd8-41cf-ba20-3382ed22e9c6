// 营收-抄表管理
import request from '@/plugins/axios';

/**
 * 分页查询抄表册列表
 * @param params
 * @returns
 */
export function getMeterBookList(params: {
  page: string | number;
  size: string | number;
  orgId?: string;
  code?: string;
  dayOfMonth?: string | number;
  lastMonth?: Date;
  cycle?: string;
  status?: string;
  type?: string;
}) {
  return request({
    url: `/api/revenue/meterBook/list`,
    method: 'GET',
    params
  });
}

/**
 * 分页查询抄表记录
 * @param params
 * @returns
 */
export function getReadMeterDataList(params: {
  page: string | number | undefined;
  size: string | number | undefined;
  orgId?: string;
  meterBookId?: string;
  meterBookCode?: string;
  meterCopyUser?: string;
  userCode?: string;
  userName?: string;
  sendStatus?: string;
  waterType?: string;
  meterType?: string;
  caliber?: string;
  remark?: string;
  beginYm?: string;
  endYm?: string;
}) {
  return request({
    url: `/api/revenue/readMeterData/list`,
    method: 'GET',
    params
  });
}

/**
 * 查询回传表底数据列表
 * @param params
 * @returns
 */
export function getRemoteMeterDataList(params: {
  page: string | number | undefined;
  size: string | number | undefined;
  orgId?: string;
  meterBookId?: string;
  meterBookCode?: string;
  userCode?: string;
  userName?: string;
  type?: string;
  remoteMeterCode?: string;
  userAddress?: string;
  userType?: string;
  beginYm?: string;
  endYm?: string;
}) {
  return request({
    url: `/api/revenue/remoteMeterData/list`,
    method: 'GET',
    params
  });
}
