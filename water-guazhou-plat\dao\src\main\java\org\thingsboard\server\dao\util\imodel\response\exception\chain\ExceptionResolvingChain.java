package org.thingsboard.server.dao.util.imodel.response.exception.chain;


import org.thingsboard.server.dao.util.imodel.response.exception.chain.handlers.CustomFailureExceptionHandler;
import org.thingsboard.server.dao.util.imodel.response.exception.chain.handlers.InvalidEntityExceptionHandler;
import org.thingsboard.server.dao.util.imodel.response.exception.chain.handlers.ViewChangeExceptionHandler;
import org.thingsboard.server.dao.util.imodel.response.model.ReturnHelper;

import java.util.ArrayList;
import java.util.List;

public class ExceptionResolvingChain {

    private final List<ExceptionHandler<? extends Throwable>> handlers;


    public ExceptionResolvingChain() {
        handlers = new ArrayList<>();
        handlers.add(new CustomFailureExceptionHandler());
        handlers.add(new InvalidEntityExceptionHandler());
        handlers.add(new ViewChangeExceptionHandler());
    }

    @SuppressWarnings({"unchecked", "rawtypes"})
    public void resolve(ReturnHelper wrap, Throwable e) throws Throwable {
        for (; ; ) {
            try {

                for (ExceptionHandler handler : handlers) {
                    if (handler.canHandle(e)) {
                        handler.handle(this, wrap, e);
                        return;
                    }
                }
                break;
            } catch (Throwable ex) {
                e = ex;
            }
        }

        throw e;
    }
}
