package org.thingsboard.server.dao.model.sql.smartOperation.project;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseUsername;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.math.BigDecimal;
import java.util.Date;


@Getter
@Setter
@ResponseEntity
public class SoProject {
    // id
    private String id;

    // 编号
    private String code;

    // 名称
    private String name;

    // 类别id
    private String typeId;

    // 类别名称
    @TableField(exist = false)
    private String typeName;

    // 规模
    private String scale;

    // 单位
    private String organization;

    // 负责人
    private String principal;

    // 联系电话
    private String phone;

    // 详细地址
    private String address;

    // 项目概况
    private String remark;

    // 概算，万元
    private BigDecimal estimate;

    // 启动时间
    private Date startTime;

    // 预计结束时间
    private Date expectEndTime;

    // 附件信息
    private String attachments;

    // 创建时间
    private Date createTime;

    // 创建人id
    @ParseUsername
    private String creator;

    // 最后更新时间
    private Date updateTime;

    // 最后更新用户
    @ParseUsername
    private String updateUser;

    // 客户id
    private String tenantId;

    // 是否可以被删除
    private Boolean canBeDelete;


}
