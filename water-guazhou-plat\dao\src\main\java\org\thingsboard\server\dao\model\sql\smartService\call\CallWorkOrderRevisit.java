package org.thingsboard.server.dao.model.sql.smartService.call;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 号码归属地
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2021-11-01
 */
@TableName("tb_service_work_order_revisit")
@Data
public class CallWorkOrderRevisit {

    @TableId
    private String id;

    private String workOrderId;

    private String creator;

    private transient String creatorName;

    private Date createTime;

    private Date revisitTime;

    private String remark;

    private String revisitPerson;

    private String tenantId;

}
