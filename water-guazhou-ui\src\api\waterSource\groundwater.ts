import request from '@/plugins/axios'

/**
 * 获取地下水涵养水位列表
 * @param params 查询参数
 * @returns
 */
export function getGroundwaterLevelList(params: any) {
  return request({
    url: '/api/groundwater/level/list',
    method: 'get',
    params
  })
}

/**
 * 获取地下水涵养水位详情
 * @param id 记录ID
 * @returns
 */
export function getGroundwaterLevelById(id: string) {
  return request({
    url: `/api/groundwater/level/${id}`,
    method: 'get'
  })
}

/**
 * 保存地下水涵养水位记录
 * @param data 水位数据
 * @returns
 */
export function saveGroundwaterLevel(data: {
  id?: string
  wellName: string
  wellLocation: string
  waterLevel: number
  measureDate: string
  remark?: string
}) {
  return request({
    url: '/api/groundwater/level',
    method: 'post',
    data
  })
}

/**
 * 更新地下水涵养水位记录
 * @param data 水位数据
 * @returns
 */
export function updateGroundwaterLevel(data: {
  id: string
  wellName: string
  wellLocation: string
  waterLevel: number
  measureDate: string
  remark?: string
}) {
  return request({
    url: '/api/groundwater/level/update',
    method: 'post',
    data
  })
}

/**
 * 删除地下水涵养水位记录
 * @param idList ID列表
 * @returns
 */
export function deleteGroundwaterLevel(idList: string[]) {
  return request({
    url: '/api/groundwater/level',
    method: 'delete',
    data: idList
  })
}

/**
 * 获取地下水水位变化数据
 * @param params 分析参数
 * @returns
 */
export function getGroundwaterLevelAnalysis(params: {
  stationId?: string
  startTime?: number
  endTime?: number
}) {
  return request({
    url: '/api/groundwater/level/change',
    method: 'get',
    params
  })
}

/**
 * 分析地下水涵养水位
 * @param params 分析参数
 * @returns
 */
export function getGroundwaterRecharge(params: {
  areaId?: string
  startTime?: number
  endTime?: number
}) {
  return request({
    url: '/api/groundwater/recharge/analyze',
    method: 'get',
    params
  })
}




