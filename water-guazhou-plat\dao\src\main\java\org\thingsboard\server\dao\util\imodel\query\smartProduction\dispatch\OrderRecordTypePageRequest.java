package org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartProduction.dispatch.OrderRecordType;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;

@Getter
@Setter
public class OrderRecordTypePageRequest extends AdvancedPageableQueryEntity<OrderRecordType, OrderRecordTypePageRequest> {
    // 类型名称
    private String name;

}
