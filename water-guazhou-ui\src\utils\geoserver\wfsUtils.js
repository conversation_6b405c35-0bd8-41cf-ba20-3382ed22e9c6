import {request} from '@/plugins/axios/geoserver'
// import * as webMercatorUtils from "@arcgis/core/geometry/support/webMercatorUtils.js";
const geoserverUrl = '/geoserver/guazhou/wfs'
export const QueryByPolygon = (layerName, polygonCoordinates,where) => {
  let filter = '1=1';
  if(polygonCoordinates){
    const gmlPolygon = polygonCoordinates.map(coord => `${coord[0]} ${coord[1]}`).join(',');
    filter = `intersects(geom,POLYGON((${gmlPolygon})))`;
  }
  let url = `${geoserverUrl}?SERVICE=WFS&VERSION=2.0.0&REQUEST=GetFeature&typeName=${layerName}&outputFormat=application/json&SRS=EPSG:3857&CQL_FILTER=${filter}`;
  if(where){
    url += ` AND (${where})`
  }
  return request({
    url,
    method: 'get',
  })
}

export const GetFieldConfig = (layerName) => {
  const url = `${geoserverUrl}?SERVICE=WFS&VERSION=1.0.0&REQUEST=DescribeFeatureType&typeName=${layerName}&outputFormat=application/json`;
  return request({
    url,
    method: 'get',
  })
}

/**
 * 获取字段唯一值
 */
export const GetFieldValueByGeoserver = (params) => {
  return request({
    url:`/geoserver/ows?service=WFS&version=1.1.0&request=GetFeature&typeName=${params.layerName}&propertyName=${params.fiedName}&outputFormat=application/json`,
    method: 'get',
  });
};

/**
 * 通用分组统计：按字段分组计数
 * @param {string} layerName 图层名
 * @param {string} groupField 分组字段
 * @param {string} [where] 属性过滤条件
 * @param {Array} [polygonCoordinates] 空间圈选坐标
 * @returns {Promise<Array<{name: string, value: number}>>}
 */
export const groupStatisticsByField = async (layerName, groupField, where, polygonCoordinates) => {
  // 拼接空间过滤
  let filter = '1=1';
  if (polygonCoordinates) {
    const gmlPolygon = polygonCoordinates.map(coord => `${coord[0]} ${coord[1]}`).join(',');
    filter = `intersects(geom,POLYGON((${gmlPolygon})))`;
  }
  if (where) {
    filter += ` AND (${where})`;
  }
  const url = `${geoserverUrl}?SERVICE=WFS&VERSION=2.0.0&REQUEST=GetFeature&typeName=${layerName}&outputFormat=application/json&SRS=EPSG:3857&CQL_FILTER=${filter}`;
  const res = await request({ url, method: 'get' });
  // 前端分组计数
  const groupMap = new Map();
  (res.data.features || []).forEach(f => {
    const key = f.properties[groupField] ?? '未知';
    groupMap.set(key, (groupMap.get(key) || 0) + 1);
  });
  return Array.from(groupMap.entries()).map(([name, value]) => ({ name, value }));
};

/**
 * 通用分组统计：按字段分组统计管长（前端聚合）
 * @param {string} layerName 图层名
 * @param {string} groupField 分组字段
 * @param {string} lengthField 管长字段名
 * @param {string} [where] 属性过滤条件
 * @param {Array} [polygonCoordinates] 空间圈选坐标
 * @returns {Promise<Array<{name: string, value: number}>>}
 */
export const groupLengthStatisticsByField = async (layerName, groupField, lengthField, where, polygonCoordinates) => {
  // 拼接空间过滤
  let filter = '1=1';
  if (polygonCoordinates) {
    const gmlPolygon = polygonCoordinates.map(coord => `${coord[0]} ${coord[1]}`).join(',');
    filter = `intersects(geom,POLYGON((${gmlPolygon})))`;
  }
  if (where) {
    filter += ` AND (${where})`;
  }
  const url = `${geoserverUrl}?SERVICE=WFS&VERSION=2.0.0&REQUEST=GetFeature&typeName=${layerName}&outputFormat=application/json&SRS=EPSG:3857&CQL_FILTER=${filter}`;
  const res = await request({ url, method: 'get' });
  // 前端分组统计管长
  const groupMap = new Map();
  (res.data.features || []).forEach(f => {
    const key = f.properties[groupField] ?? '未知';
    const len = Number(f.properties[lengthField]) || 0;
    groupMap.set(key, (groupMap.get(key) || 0) + len);
  });
  return Array.from(groupMap.entries()).map(([name, value]) => ({ name, value }));
};