import{_ as h}from"./index-BlG8PIOK.js";import{c as v}from"./useStation-DJgnSZIA.js";import{d as b,r as w,o as D,g as c,n as p,dy as C,q as g,F as y,p as s,aB as L,aJ as M,bh as e,i as d,aw as k,l as x,bE as B,C as N}from"./index-r0dFAfgr.js";import"./zhandian-YaGuQZe6.js";const S={class:"item_view"},V={class:"td"},z={class:"td"},H={class:"td"},J={class:"td"},E={class:"td"},F={class:"td"},I=b({__name:"YLJC",props:{size:{}},setup(r){const l=r,n=w({data:[]}),i={step:.2,limitMoveNum:6},_=v(),m=()=>{_.getLatestData({type:"测流压站,流量监测站,压力监测站"}).then(o=>{n.data=(o==null?void 0:o.map(a=>(a.time=a.time?x(a.time,B).format("MM/DD HH:mm"):"",a)))||[]})};return D(()=>{m()}),(o,a)=>{const f=h;return c(),p("div",{class:k(["card",l.size])},[a[0]||(a[0]=C('<div class="table-header" data-v-b78fa057><span class="th" data-v-b78fa057>监测点名称</span><span class="th" data-v-b78fa057>压力</span><span class="th" data-v-b78fa057>温度</span><span class="th" data-v-b78fa057>累计流量</span><span class="th" data-v-b78fa057>瞬时流量</span><span class="th" data-v-b78fa057>读取时间</span></div>',1)),g(f,{data:d(n).data,"class-option":i,class:"warp"},{default:y(()=>[s("ul",S,[(c(!0),p(L,null,M(d(n).data,(t,u)=>(c(),p("li",{key:u,class:"table-body-row"},[s("span",V,e(t.name),1),s("span",z,e(t.pressure??"--")+" MPa",1),s("span",H,e(t.temperature??"--")+" ℃",1),s("span",J,e(t.total_flow??"--")+" m³",1),s("span",E,e(t.Instantaneous_flow??"--")+" m³/h",1),s("span",F,e(t.time??"--"),1)]))),128))])]),_:1},8,["data"])],2)}}}),j=N(I,[["__scopeId","data-v-b78fa057"]]);export{j as default};
