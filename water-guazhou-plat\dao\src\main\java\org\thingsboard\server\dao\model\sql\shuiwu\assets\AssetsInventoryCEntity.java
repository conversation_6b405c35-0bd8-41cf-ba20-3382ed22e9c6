package org.thingsboard.server.dao.model.sql.shuiwu.assets;

import lombok.Data;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;

/**
 * 设备台账
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2021-10-19
 */
@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.ASSETS_INVENTORY_C_TABLE)
public class AssetsInventoryCEntity {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.ASSETS_INVENTORY_C_PID)
    private String pid;

    @Column(name = ModelConstants.ASSETS_INVENTORY_C_DEVICE_ID)
    private String deviceId;

    @Column(name = ModelConstants.ASSETS_INVENTORY_C_CHANGE)
    private String change;

    @Column(name = ModelConstants.ASSETS_INVENTORY_C_REMARK)
    private String remark;

    @Column(name = ModelConstants.ASSETS_INVENTORY_C_INVENTORY_TIME)
    private Long inventoryTime;

    @Column(name = ModelConstants.ASSETS_INVENTORY_C_INVENTORY_PERSON_ID)
    private String inventoryPersonId;

    @Column(name = ModelConstants.ASSETS_INVENTORY_C_STATUS)
    private String status;

    @Column(name = ModelConstants.ASSETS_INVENTORY_C_CREATE_TIME)
    private Long createTime;

    @Column(name = ModelConstants.ASSETS_INVENTORY_C_UPDATE_TIME)
    private Long updateTime;

    @Column(name = ModelConstants.ASSETS_INVENTORY_C_TENANT_ID)
    private String tenantId;


    private transient String inventoryPersonName;

    private transient String deviceNo;

    private transient String deviceName;

    private transient String specificationModel;

}
