html.dark {
  .el-card {
    --el-card-bg-color: #222536;
    --el-card-border-color: #2d2d2e;

    // &.darkblue {
    //   --el-card-bg-color: transparent;
    //   --el-card-border-color: rgba(21, 45, 68, 1);
    // }
  }
  .el-tabs--border-card{
    background-color: var(--el-bg-color);
    &>.el-tabs__header {
      .el-tabs__item{
        &.is-active{
          background-color: var(--el-bg-color);
        }
      }
    }
  }
  .el-table {
    --el-table-header-bg-color: #2E3449;
    // --el-fill-color-lighter: #121d2c
  }

  // .panel {
  //   .el-table {

  //     --el-table-header-bg-color: #142843;
  //     --el-fill-color-lighter: #121d2c;
  //   }
  // }

  .el-dialog__header {
    margin-right: 0;
    border-bottom: 1px solid #333;
  }

  .el-tabs--top.tabs-basic {
    .el-tabs__header {
      background: #272a3e;
    }
  }

  .el-picker__popper.el-popper,
  .el-select__popper.el-popper,
  .el-popover {
    // --el-popover-bg-color: #222536;

    // .el-popper__arrow::before {
    //   border: 1px solid #222536;
    //   background: #222536;
    // }

    // &.darkblue {
    //   --el-popover-bg-color: rgba(21, 45, 68, 0.9);
    //   border: 1px solid rgba(21, 45, 68, 0.9);
    //   background: rgba(21, 45, 68, 1);

    //   .el-popper__arrow::before {
    //     border: 1px solid rgba(21, 45, 68, 0.9);
    //     background: rgba(21, 45, 68, 1);
    //   }
    // }
  }

  // .el-picker__popper.el-popper {
  //   &.darkblue {
  //     .el-picker-panel {
  //       background: none;
  //     }
  //   }
  // }
}

// .darkblue {
//   .el-table {
//     --el-table-header-bg-color: rgb(25, 41, 65) !important;
//     --el-fill-color-lighter: rgba(21, 45, 68, 1) !important;
//   }

//   .el-table__body-wrapper,
//   .el-table__footer-wrapper,
//   .el-table__header-wrapper {
//     tr {
//       td {

//         &.el-table-fixed-column--right,
//         &.el-table-fixed-column--left {
//           background-color: rgba(21, 45, 68, 1) !important;
//         }
//       }
//     }
//   }

//   &.el-tabs {
//     background-color: transparent;

//     .el-tabs__header {
//       background-color: transparent;

//       .el-tabs__item {
//         &.is-active {
//           background-color: rgba(21, 45, 68, 1);
//         }
//       }
//     }
//   }
// }
