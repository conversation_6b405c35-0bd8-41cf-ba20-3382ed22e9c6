import{d as A,M as P,c as u,r as f,ci as V,cj as _,g as h,h as b,F as q,p as I,q as v,i as l,an as D,t as L,L as z}from"./index-r0dFAfgr.js";import{_ as B}from"./index-DyRO7tCT.js";import{_ as R}from"./CardTable-rdWOL4_6.js";import{_ as O}from"./CardSearch-CB_HNR-Q.js";const $={class:"dialogPageContainer"},J=A({__name:"tenantManager",props:{config:{}},setup(y,{expose:w}){const d=y,{$btnPerms:j,$message:p}=P(),i=u(!1),x=()=>{i.value=!1},N=()=>{i.value=!0,n()},m=f({filters:[{label:"搜索",field:"name",type:"input"},{type:"btn-group",btns:[{text:"查询",perm:!0,click:()=>n()},{text:"添加",perm:!0,click:()=>{r.value.title="添加管理员",r.value.visible=!0}},{text:"删除",perm:!0,click:()=>g(null)}]}]}),s=f({loading:!1,dataList:[],selectList:[],handleSelectChange:e=>{s.selectList=e},columns:[{prop:"name",label:"名称"},{prop:"firstName",label:"昵称"}],operations:[{text:"编辑",isTextBtn:!0,perm:!0,icon:"iconfont icon-bianji",click:e=>{r.value.title="编辑管理员",r.value.defaultValue={...e,...JSON.parse(e.additionalInfo)},r.value.visible=!0}},{text:"删除",isTextBtn:!0,perm:!0,icon:"iconfont icon-shanchu",click:e=>g(e)}],operationWidth:"160px",pagination:{page:1,limit:20,total:0,layout:"total, sizes, prev, pager, next, jumper",handleSize:e=>{s.pagination.limit=e,n()},handlePage:e=>{s.pagination.page=e,n()}}}),r=u({visible:!1,title:"",close:()=>r.value.visible=!1,editUrl:"api/device",addUrl:"api/newUser?istarCreatePasswordUrl="+encodeURIComponent("https://ems.istarscloud.com/createPassword"),defaultValue:{},columns:[{type:"input",label:"用户邮箱",key:"email",rules:[{required:!0,validator:C,trigger:"blur"}],message:"管理员账号即邮箱"},{type:"password",label:"密码:",key:"password",rules:[{required:!0,message:"请输入密码"}]},{type:"input",label:"联系手机",key:"phone",rules:[{required:!0,validator:E,trigger:"blur"}]},{type:"select",label:"管理员权限:",key:"authority",options:[{value:"TENANT_ADMIN",label:"企业管理员"}],rules:[{required:!0,message:"请选择管理员权限",trigger:"change"}]},{type:"input",label:"昵称",key:"lastName",rules:[{validator:k,trigger:"blur"},{required:!0,message:"请输入昵称",trigger:"blur"},{max:16,message:"昵称输入不可超过16位",trigger:"blur"}]}],setSubmitParams(e){return e.firstName=e.lastName,e.name=e.email,e.tenantId=d.config.currentId,e}}),c=u(),n=async()=>{var a,t;c.value&&((t=(a=c.value)==null?void 0:a.queryParams)==null||t.name);const e=await V(d.config.currentId.id);s.dataList=e.data.data},g=async e=>{var a;e?(await _(e.id.id),p.success("删除成功"),n()):(await((a=s.selectList)==null?void 0:a.forEach(async t=>{await _(t.id.id)})),p.success("删除成功"),n())};function C(e,a,t){const o=/^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+(.[a-zA-Z0-9_-])+/;if(!a)return t(new Error("邮箱不能为空"));setTimeout(()=>{o.test(a)?t():t(new Error("请输入正确的邮箱格式"))},100)}function k(e,a,t){a.trim()===""?t(new Error("名称不可为空，请输入")):t()}function E(e,a,t){const o=/^1[34578]\d{9}$$/;if(!a)return t(new Error("电话号码不能为空"));setTimeout(()=>{Number.isInteger(+a)?o.test(a)?t():t(new Error("电话号码格式不正确")):t(new Error("请输入数字值"))},100)}return w({visible:i,cardSearch:c,cardSearchConfig:m,cardTableConfig:s,refreshData:n,addOrUpdateConfig:r,open:N,close:x}),(e,a)=>{const t=O,o=R,S=B,U=z;return h(),b(U,{modelValue:l(i),"onUpdate:modelValue":a[0]||(a[0]=T=>L(i)?i.value=T:null),width:"800px",title:e.config.title||"","lock-scroll":!1},{default:q(()=>[I("div",$,[v(t,{ref_key:"cardSearch",ref:c,class:"cardSearch",config:l(m)},null,8,["config"]),v(o,{class:"card-table",config:l(s)},null,8,["config"]),l(r).visible?(h(),b(S,{key:0,ref:"addOrUpdate",config:l(r),onRefreshData:n},null,8,["config"])):D("",!0)])]),_:1},8,["modelValue","title"])}}});export{J as _};
