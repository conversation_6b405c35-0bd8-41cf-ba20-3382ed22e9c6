<template>
    <div class="panel-card">
      <div class="panel-card__header">
        <span class="panel-card__title">{{ title }}</span>
      </div>
      <div class="panel-card__body">
        <slot />
      </div>
    </div>
  </template>
  
  <script setup>
  // 标题通过props传递
  const props = defineProps({
    title: {
      type: String,
      default: ''
    }
  })
  </script>
  
  <style lang="scss" scoped>
  .panel-card {
    background: url('../../assets/img/pannelCardBg.svg');
    background-repeat: no-repeat;
    width: 100%;
    height: 100%;
    background-size: cover;
    // padding: 0 0 18px 0;
    overflow: hidden;
  
    &__header {
      // padding: 16px 24px 8px 24px;
      width: 100%;
      height: 48px;
      display: flex;
      align-items: center;
    }
    &__title {
      font-size: 20px;
      font-weight: bold;
      letter-spacing: 1px;
      color: #fff;
      margin-left: 40px;
      font-family: 'MStiffHeiPRC-Bold', sans-serif;
      text-shadow: 0 0 10px rgba(0, 195, 255, 0.5);
      position: relative;
      
      &::after {
        content: '';
        position: absolute;
        bottom: -5px;
        left: 0;
        width: 100%;
        height: 2px;
        background: linear-gradient(90deg, rgba(0, 183, 255, 0), rgba(0, 183, 255, 0.8), rgba(0, 183, 255, 0));
      }
    }
    &__body {
      color: #fff;
      font-size: 16px;
      height: calc(100% - 48px);
      padding: 16px;
    }
  }
  </style>
  