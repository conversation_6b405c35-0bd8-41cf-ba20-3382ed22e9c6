package org.thingsboard.server.dao.largeScreen;

import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.largeScreen.LargeScreenConfig;
import org.thingsboard.server.dao.util.imodel.query.largeScreen.LargeScreenConfigRequest;

import java.util.List;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2024-01-30
 */
public interface LargeScreenConfigService {

    PageData<LargeScreenConfig> getList(LargeScreenConfigRequest largeScreenConfigRequest);

    LargeScreenConfig save(LargeScreenConfig largeScreenConfig);

    void delete(List<String> idList);
}
