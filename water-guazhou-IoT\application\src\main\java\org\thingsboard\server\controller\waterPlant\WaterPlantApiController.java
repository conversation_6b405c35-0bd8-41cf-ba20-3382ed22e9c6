package org.thingsboard.server.controller.waterPlant;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.production.ProductionService;
import org.thingsboard.server.dao.stationData.customization.WaterPlantService;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.Arrays;
import java.util.List;

/**
 * 水厂站点定制数据API
 */
@RestController
@RequestMapping("api/station/data/waterPlant")
public class WaterPlantApiController extends BaseController {

    @Autowired
    private WaterPlantService waterPlantService;

    @Autowired
    private ProductionService productionService;

    @GetMapping("waterSupply")
    public Object waterSupply(@RequestParam String stationId) throws ThingsboardException {
        return waterPlantService.waterSupply(stationId, getTenantId());
    }

    @GetMapping("pressureByStation")
    public Object pressureByStation(@RequestParam String stations,
                                    @RequestParam Long start,
                                    @RequestParam Long end,
                                    @RequestParam String type) throws ThingsboardException {
        List<String> stationIdList = Arrays.asList(stations.split(","));

        return waterPlantService.pressureByStation(stationIdList, start, end, type, getTenantId());
    }

    @GetMapping("drugConsumptionAnalyze")
    public Object drugConsumptionAnalyze(@RequestParam String stationId) throws Exception {
        return waterPlantService.drugConsumptionAnalyze(stationId, getTenantId());
    }

    @GetMapping("yearDrugConsumptionAnalyze")
    public IstarResponse yearDrugConsumptionAnalyze(@RequestParam String stationId) throws Exception {
        return IstarResponse.ok(waterPlantService.yearDrugConsumptionAnalyze(stationId, getTenantId()));
    }

    @GetMapping("drugConsumptionAnalyzeByType")
    public IstarResponse drugConsumptionAnalyzeByType(@RequestParam String stationId,
                                                      @RequestParam Long startTime, @RequestParam Long endTime) throws Exception {
        return IstarResponse.ok(waterPlantService.drugConsumptionAnalyzeByType(stationId, startTime, endTime, getTenantId()));
    }

    @GetMapping("waterQualityAnalyze")
    public Object waterQualityAnalyze(@RequestParam String stationId, @RequestParam String type,
                                      @RequestParam Long start, @RequestParam Long end) throws ThingsboardException {
        return waterPlantService.waterQualityAnalyze(stationId, type, start, end, getTenantId());
    }

    /**
     * 查询指定站点的出水流量曲线
     */
    @GetMapping("stationFlowRate")
    public Object stationPressure(@RequestParam String stationId) throws ThingsboardException {
        return waterPlantService.stationFlowRate(stationId, getTenantId());
    }

    /**
     * 查询指定站点的出水压力曲线
     */
    @GetMapping("stationPressure")
    public Object stationFlowRate(@RequestParam String stationId) throws ThingsboardException {
        return waterPlantService.stationPressure(stationId, getTenantId());
    }

    /**
     * 生产看板-总览统计
     */
    @GetMapping("productionDashboardView")
    public Object productionDashboardView() throws ThingsboardException {
        return waterPlantService.productionDashboardView(getTenantId());
    }


    /**
     * 产销差
     * 售水数据来自营收数据录入
     */
    @GetMapping("productionAndSales")
    public IstarResponse productionAndSales(@RequestParam String stationId, @RequestParam String year) throws ThingsboardException {
        return IstarResponse.ok(productionService.productionAndSales(stationId, year, getTenantId()));
    }

}
