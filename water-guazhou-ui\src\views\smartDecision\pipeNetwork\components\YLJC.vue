<template>
  <div
    class="card"
    :class="props.size"
  >
    <div class="table-header">
      <span class="th">监测点名称</span>
      <span class="th">压力</span>
      <span class="th">温度</span>
      <span class="th">累计流量</span>
      <span class="th">瞬时流量</span>
      <span class="th">读取时间</span>
    </div>
    <VueScroll
      :data="state.data"
      :class-option="optionHover"
      class="warp"
    >
      <ul class="item_view">
        <li
          v-for="(item, i) in state.data"
          :key="i"
          class="table-body-row"
        >
          <span class="td">{{ item.name }}</span>
          <span class="td">{{ item.pressure ?? '--' }} MPa</span>
          <span class="td">{{ item.temperature ?? '--' }} ℃</span>
          <span class="td">{{ item.total_flow ?? '--' }} m³</span>
          <span class="td">{{ item.Instantaneous_flow ?? '--' }} m³/h</span>
          <span class="td">{{ item.time ?? '--' }}</span>
        </li>
      </ul>
    </VueScroll>
  </div>
</template>

<script lang="ts" setup>
import { useStationsLatestData } from '@/hooks/station/useStation'
import { formatterDateTime } from '@/utils/GlobalHelper'

const props = defineProps<{ size?: 'small' }>()
const state = reactive<{
  data: {
    name: string
    pressure: string
    temperature: string
    total_flow: string
    Instantaneous_flow?: string
    time: string
  }[]
}>({
  data: []
})

const optionHover = {
  step: 0.2,
  limitMoveNum: 6
}
const station = useStationsLatestData()
const refreshData = () => {
  station
    .getLatestData({
      type: '测流压站,流量监测站,压力监测站'
    })
    .then(res => {
      state.data = res?.map(item => {
        item.time =item.time? moment(item.time, formatterDateTime).format('MM/DD HH:mm'):''
        return item
      }) || []
    })
}
onMounted(() => {
  refreshData()
})
</script>

<style lang="scss" scoped>
.card {
  width: 100%;
  font-size: 16px;
  height: 100%;
  padding: 20px;
  .warp {
    height: calc(100% - 38px);
    width: 100%;
    margin: 0 auto;
    overflow: hidden;
  }
  &.small {
    .td,
    .th {
      display: inline-block;
      width: 80px;
      &:nth-child(1) {
        width: 160px;
      }
      &:nth-child(4) {
        width: 120px;
      }
      &:nth-child(6) {
        width: 90px;
      }
    }
  }
}
.over_hide {
  overflow: hidden;
}
.table-header,
.table-body-row {
  width: 100%;
  height: 38px;
  line-height: 28px;
  font-size: 14px;
  span {
    // width: 100%;
    text-align: center;
  }
}
.td,
.th {
  display: inline-block;
  width: 120px;
  line-height: 38px;
  &:nth-child(1) {
    width: 240px;
  }
  &:nth-child(6) {
    width: 130px;
  }
}
.item_view {
  margin: 0;
  list-style: none;
  padding: 0;
  li {
    line-height: 24px;
    &:nth-child(odd) {
      background-color: rgba(128, 188, 253, 0.4);
    }
  }
}
</style>
