package org.thingsboard.server.dao.dataSource;

import com.influxdb.query.FluxTable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.Device;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.VO.DefaultMapObject;
import org.thingsboard.server.common.data.constantsAttribute.LoocalMap;
import org.thingsboard.server.common.data.constantsAttribute.PropAttribute;
import org.thingsboard.server.common.data.dataSource.DataFromDataSource;
import org.thingsboard.server.common.data.dataSource.DataSourceRequest;
import org.thingsboard.server.common.data.exception.ThingsboardErrorCode;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.DeviceId;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.kv.AttributeKvEntry;
import org.thingsboard.server.common.data.kv.TsKvEntry;
import org.thingsboard.server.common.data.utils.*;
import org.thingsboard.server.dao.attributes.AttributesDao;
import org.thingsboard.server.dao.device.DeviceService;
import org.thingsboard.server.dao.influx.InfluxService;
import org.thingsboard.server.dao.model.sql.DataSourceEntity;
import org.thingsboard.server.dao.timeseries.TimeseriesService;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static org.thingsboard.server.common.data.utils.DateUtils.WEEK;

/**
 * <AUTHOR>
 * @date 2020/2/27 14:19
 * 设备数据源处理类
 */
@Service
public class DeviceDataSourceProcess {

    @Autowired
    private DeviceService deviceService;

    @Autowired
    private TimeseriesService timeseriesService;

    @Autowired
    private AttributesDao attributesDao;

    @Autowired
    private InfluxService influxService;


    /**
     * 处理设备类数据源，跟据时间段和数据间隔获取数据并返回
     *
     * @param deviceDataSource  设备数据源
     * @param dataSourceRequest 数据源请求
     * @return List<DataFromDataSource>
     */
    public DataFromDataSource processDeviceDataSource(DataSourceEntity deviceDataSource, DataSourceRequest dataSourceRequest) {
        DataFromDataSource dataFromDataSource = new DataFromDataSource();
        //获取设备
        Device device = deviceService.findDeviceById(new DeviceId(UUIDConverter.fromString(deviceDataSource.getDeviceId())));
        //获取所有数据
        if (dataSourceRequest.getInterval().equalsIgnoreCase("all")) {
            dataSourceRequest.setStartTime(device.getCreatedTime());
            dataSourceRequest.setEndTime(System.currentTimeMillis());
        }
        dataFromDataSource.setDataSourceId(deviceDataSource.getId());
        dataFromDataSource.setDataSourceName(deviceDataSource.getSourceName());
        //从openTsdb对应属性的数据
        String formula = deviceDataSource.getDeviceId() + "." + deviceDataSource.getProperty();
        long startTime = TimeDiff.convertStartByType(dataSourceRequest.getStartTime(), dataSourceRequest.getInterval());
        try {
            List<FluxTable> fluxTables = influxService.findData(Collections.singletonList(formula), startTime, dataSourceRequest.getEndTime(),dataSourceRequest.getInterval());
            if (fluxTables != null && fluxTables.size() > 0) {
                List<DefaultMapObject> list = new ArrayList<>();
                fluxTables.forEach(res -> {
                    List<DefaultMapObject> result = convertByFormula(device.getTenantId(), UUIDConverter.fromTimeUUID(device.getUuidId()), res, deviceDataSource.getProperty(), deviceDataSource.getStatisticsType(), null, dataSourceRequest.getInterval(), dataSourceRequest.getStartTime(), dataSourceRequest.getEndTime());
                    //result.forEach((key, value) -> list.add(DefaultMapObject.builder().ts(key).value(value == null ? null : String.valueOf(value.doubleValue())).build()));
                    list.addAll(result);
                });
//                if(!dataSourceRequest.getInterval().endsWith("d")){
//                    list.remove(list.get(0));
//                }
                dataFromDataSource.setData(list);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return dataFromDataSource;
    }


    /**
     * 分组获取数据
     *
     * @param tenantId
     * @param response
     * @param propType
     * @param readmeterTime
     * @param type
     * @param start
     * @param end
     * @return
     */
    public List<DefaultMapObject> convertByFormula
    (TenantId tenantId, String deviceId, FluxTable response, String prop, String propType, String readmeterTime, String type, long start, long end) {
        List<DefaultMapObject> result = new ArrayList<>();
        if (response == null) {
            return result;
        }
        //查找换表时间
        try {
            PropAttribute propAttribute = getProp(tenantId, deviceId, prop);
            if (propAttribute == null) {
                return result;
            }
            LinkedHashMap<Long, Long> changeMeterMap = new LinkedHashMap<>();
            Optional<AttributeKvEntry> changemeterAttr = attributesDao.find(tenantId, new DeviceId(UUIDConverter.fromString(deviceId)), DataConstants.SERVER_SCOPE, DataConstants.ATTRIBUTE_CHANGE_METER).get();
            changemeterAttr.ifPresent(attributeKvEntry ->
                    changeMeterMap.putAll(TimeDiff.changemeter(attributeKvEntry.getValueAsString())));
            //筛选出换表数据
            ArrayList<ArrayList<LoocalMap>> linkedHashMaps = ResponseUtil.handleResponseTs(response, changeMeterMap);
            if (propType != null) {
                result.addAll(groupDataByType(linkedHashMaps, propType, readmeterTime, type, start, end));
            } else {
                result.addAll(groupDataByType(linkedHashMaps, propAttribute.getStatType(), readmeterTime, type, start, end));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;

    }


    /**
     * 把数据按每天进行分组并对数据进行处理
     *
     * @param data
     * @return
     */
    public List<DefaultMapObject> groupDataByType(ArrayList<ArrayList<LoocalMap>> data, String
            statType, String readmeterTime, String type, long start, long end) {
        ArrayList<LinkedHashMap<String, ArrayList<DefaultMapObject>>> linkedHashMaps = new ArrayList<>();
        // 2018-10-30 修改：在分组数据之前使用类型对时间做分组处理
        // 2018-11-05 修改：去掉之前的填充逻辑，对不同时间类型的数据进行不同的处理，将每个时间段内的最后一条数据加入到该时间段内
        ArrayList<String> dates = DateUtils.findDates(start, end, type);
        Iterator iter = data.iterator();
        while (iter.hasNext()) {
            ArrayList<LoocalMap> loocalMapArrayList = (ArrayList<LoocalMap>) iter.next();
            Iterator attr = loocalMapArrayList.iterator();
            LinkedHashMap<String, ArrayList<DefaultMapObject>> map = new LinkedHashMap<>();
            dates.forEach(d -> {
                map.put(d, new ArrayList<>());
            });
            while (attr.hasNext()) {
                LoocalMap entry = (LoocalMap) attr.next();
                String timeLay = null;
                String lastTimeLay = null;
                switch (type) {
                    case "1h":
                    case DateUtils.HOUR: {
                        timeLay = DateUtils.date2Str(entry.getKey(), DateUtils.DATE_FORMATE_HOUR);
                        lastTimeLay = TimeDiff.getLastHour(entry.getKey(), DateUtils.HOUR, readmeterTime);
                        break;
                    }
                    case "1d":
                    case DateUtils.DAY: {
                        timeLay = TimeDiff.getDayByReadmeter(entry.getKey(), readmeterTime, DateUtils.DAY);
                        lastTimeLay = TimeDiff.getLastHour(entry.getKey(), DateUtils.DAY, readmeterTime);
                        break;
                    }
                    case WEEK: {
                        timeLay = TimeDiff.getDayByReadmeter(entry.getKey(), readmeterTime, WEEK);
                        lastTimeLay = TimeDiff.getLastHour(entry.getKey(), WEEK, readmeterTime);
                        break;
                    }
                    case "1nc":
                    case DateUtils.MONTH: {
                        timeLay = TimeDiff.getDayByReadmeter(entry.getKey(), readmeterTime, DateUtils.MONTH);
                        lastTimeLay = TimeDiff.getLastHour(entry.getKey(), DateUtils.MONTH, readmeterTime);
                        break;
                    }
                    case "1yc":
                    case DateUtils.YEAR: {
                        timeLay = TimeDiff.getDayByReadmeter(entry.getKey(), readmeterTime, DateUtils.YEAR);
                        lastTimeLay = TimeDiff.getLastHour(entry.getKey(), DateUtils.YEAR, readmeterTime);
                        break;
                    }
                    case "all": {
                        timeLay = "all";
                        lastTimeLay = "all";
                        break;
                    }
                    case DateUtils.SECOND:
//                    case DateUtils.MINUTE:
//                    case DateUtils.FIVE_MINUTE:
//                    case DateUtils.TEN_MINUTE:
//                    case DateUtils.FIFTEEN_MINUTE:
                    default:
                        timeLay = TimeDiff.getTrulyMinute(entry.getKey(), type);
                        lastTimeLay = TimeDiff.getAfterFiveMinute(timeLay, type);
                        break;
                }
                if (timeLay != null && map.get(timeLay) != null) {
                    map.get(timeLay).add(DefaultMapObject.builder().ts(String.valueOf(entry.getKey())).value(entry.getData().toString()).tm(timeLay).build());
                }
                if (statType.equalsIgnoreCase(AttributeConstants.TELEMETRY_DATA_TYPE_ACCUMULATE) && lastTimeLay != null && map.get(lastTimeLay) != null) {
                    map.get(lastTimeLay).add(DefaultMapObject.builder().ts(String.valueOf(entry.getKey())).tm(lastTimeLay).value(entry.getData().toString()).build());
                }
            }
            linkedHashMaps.add(map);
        }
        
        // 不进行差值求和
        if ("".equals(type)) {
            List<DefaultMapObject> list = new ArrayList<>();
            data.forEach(innerData -> {
                if (innerData != null && !innerData.isEmpty()) {
                    list.addAll(innerData.stream().map(loocalMap -> DefaultMapObject.builder()
                            .tm(DateUtils.date2Str(loocalMap.getKey(), "yyyy-MM-dd HH:mm:ss"))
                            .ts(loocalMap.getKey() + "")
                            .value(loocalMap.getData().toString())
                            .build()
                    ).collect(Collectors.toList()));
                }
            });
            return list;
        }

        //对数据进行差值求和
        LinkedHashMap<String, DefaultMapObject> map = new LinkedHashMap<>();
        linkedHashMaps.forEach(list -> list.forEach((key, value) -> {
            if (AttributeConstants.TELEMETRY_DATA_TYPE_RANDOM.equals(statType)) {
                if (value.size() > 0) {
                    map.put(key, value.get(value.size() - 1));
                } else {
                    map.put(key, DefaultMapObject.builder().value(null).tm(key).ts(String.valueOf(DateUtils.convertDateToDate(key, type)).substring(0, 10)).build());
                }
            } else {
                if (value.size() > 0) {
                    if (map.containsKey(key) && map.get(key) != null) {
                        BigDecimal bigDecimal = new BigDecimal(map.get(key).getValue());
                        BigDecimal d = bigDecimal.add(new BigDecimal(value.get(value.size() - 1).getValue())).subtract(new BigDecimal(value.get(0).getValue()));
                        map.get(key).setValue(d.toString());
                        map.put(key, map.get(key));
                    } else {
                        map.put(key, DefaultMapObject.builder().value(String.valueOf(new BigDecimal(value.get(value.size() - 1).getValue()).subtract(new BigDecimal(value.get(0).getValue())))).tm(key).ts(String.valueOf(DateUtils.convertDateToDate(key, type)).substring(0, 10)).build());
                    }
                } else {
                    map.putIfAbsent(key, DefaultMapObject.builder().value(null).tm(key).ts(String.valueOf(DateUtils.convertDateToDate(key, type)).substring(0, 10)).build());
                }
            }
        }));

        List<DefaultMapObject> list = new ArrayList<>();
        map.forEach((key, value) -> list.add(value));
        return list;
    }


    public PropAttribute getProp(TenantId tenantId, String deviceId, String propName) throws ThingsboardException {
        try {
            Optional<AttributeKvEntry> prop = attributesDao.find(tenantId, new DeviceId(UUIDConverter.fromString(deviceId)), DataConstants.SHARED_SCOPE, DataConstants.ATTRIBUTE_PROP).get();
            PropAttribute propAttribute = new PropAttribute();
            prop.ifPresent(attr -> {
                Iterator<com.fasterxml.jackson.databind.JsonNode> elements = JsonNodeUtils.readJson(attr.getValueAsString());
                elements.forEachRemaining(element -> {
                    if (element.get("propertyCategory").asText().equals(propName)) {
                        propAttribute.setName(propName);
                        propAttribute.setStatType(element.get("statType").asText());
                    }
                });
            });
            return propAttribute.getStatType() == null ? null : propAttribute;
        } catch (Exception e) {
            throw new ThingsboardException(DataConstants.RESPONSE_ERROR_GET_DEVICE_INFO + e.getMessage(), ThingsboardErrorCode.GENERAL);
        }
    }


    /**
     * 处理数据源，跟据时间段和数据间隔获取数据并返回
     *
     * @param deviceDataSource  设备数据源
     * @param dataSourceRequest 数据源请求
     * @return List<DataFromDataSource>
     */
    public DataFromDataSource processRestApiDataSource(DataSourceEntity deviceDataSource, DataSourceRequest dataSourceRequest) {
        DataFromDataSource dataFromDataSource = new DataFromDataSource();
        //获取设备
        dataFromDataSource.setDataSourceId(deviceDataSource.getId());
        dataFromDataSource.setDataSourceName(deviceDataSource.getSourceName());
        //从openTsdb对应属性的数据
        String formula;
        if (deviceDataSource.getProperty() != null) {
            formula = deviceDataSource.getId() + "." + deviceDataSource.getProperty();
        } else {
            formula = deviceDataSource.getId() + "." + deviceDataSource.getPreparation();
        }
        try {
            List<FluxTable> fluxTables = influxService.findData(Collections.singletonList(formula), dataSourceRequest.getStartTime(), dataSourceRequest.getEndTime(),dataSourceRequest.getInterval());
            if (fluxTables != null && fluxTables.size() > 0) {
                List<DefaultMapObject> list = new ArrayList<>();
                fluxTables.forEach(res -> {
                    ArrayList<ArrayList<LoocalMap>> linkedHashMaps = ResponseUtil.handleResponseTs(res, null);
                    List<DefaultMapObject> result = groupDataByType(linkedHashMaps, "0", null, dataSourceRequest.getInterval(), dataSourceRequest.getStartTime(), dataSourceRequest.getEndTime());
                    result.forEach(ob -> {
                        if (ob.getValue() != null) {
                            ob.setValue(new BigDecimal(ob.getValue()).setScale(2, BigDecimal.ROUND_HALF_DOWN).toString());
                        }
                        list.add(ob);
                    });
                });
                dataFromDataSource.setData(list);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return dataFromDataSource;
    }


    /**
     * 获取设备的最后一次数据
     *
     * @param deviceDataSource
     * @return
     */
    public DataFromDataSource processDeviceLastData(DataSourceEntity deviceDataSource) {
        DataFromDataSource dataFromDataSource = new DataFromDataSource();
        //获取设备
        dataFromDataSource.setDataSourceId(deviceDataSource.getId());
        dataFromDataSource.setDataSourceName(deviceDataSource.getSourceName());
        TsKvEntry tsKvEntry = getDeviceLastData(deviceDataSource);
        if (tsKvEntry != null) {
            dataFromDataSource.setData(Collections.singletonList(DefaultMapObject.builder()
                    .ts(String.valueOf(tsKvEntry.getTs())).value(tsKvEntry.getValueAsString()).build()));
        }
        return dataFromDataSource;
    }


    /**
     * 获取设备的最后一次数据
     *
     * @param deviceDataSource
     * @return
     */
    public TsKvEntry getDeviceLastData(DataSourceEntity deviceDataSource) {
        TsKvEntry tsKvEntry = timeseriesService.findLatestByKey(new DeviceId(UUIDConverter.fromString(deviceDataSource.getDeviceId())), deviceDataSource.getProperty());
        return tsKvEntry;
    }
}
