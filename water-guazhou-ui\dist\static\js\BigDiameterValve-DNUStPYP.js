import{d as F,c as v,r as h,b as _,g as x,h as C,F as M,q as V,i as B,_ as I}from"./index-r0dFAfgr.js";import L from"./RightDrawerMap-D5PhmGFO.js";import{QueryByPolygon as P}from"./wfsUtils-DXofo3da.js";import"./ArcView-DpMnCY82.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./widget-BcWKanF2.js";import"./pe-B8dP0-Ut.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./TileLayer-B5vQ99gG.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./commonProperties-DqNQ4F00.js";import"./project-DUuzYgGl.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./IdentifyResult-4DxLVhTm.js";import"./ViewHelper-BGCZjxXH.js";import"./fieldconfig-Bk3o1wi7.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./LayerHelper-Cn-iiqxI.js";import"./pipe-nogVzCHG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./ArcStationWarning-BF9YrSzF.js";/* empty css                         */import"./useWaterPoint-Bv0z6ym6.js";import"./DateFormatter-Bm9a68Ax.js";import"./zhandian-YaGuQZe6.js";import"./useStation-DJgnSZIA.js";import"./DrawerBox-CLde5xC8.js";import"./SideDrawer-CBntChyn.js";import"./PipeDetail-CTBPYFJW.js";import"./Search-NSrhrIa_.js";import"./FormTableColumnFilter-BT7pLXIC.js";import"./QueryHelper-ILO3qZqg.js";import"./config-fy91bijz.js";import"./ArcBRTools-BO92yznB.js";import"./PipeLength.vue_vue_type_script_setup_true_lang-BZfUsvDf.js";import"./arcWidgetButton-0glIxrt7.js";import"./StatisticsHelper-D-s_6AyQ.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./usePipelineGroup-Ba1pmWz_.js";import"./GroupLayer-DhhN3FyN.js";import"./lazyLayerLoader-DbM9sT1W.js";import"./WFSLayer-1TtJp3nx.js";import"./clientSideDefaults-VQhQaYxh.js";import"./QueryEngineCapabilities-Dk3NJwmm.js";import"./wfsUtils-Du031XaC.js";import"./geojson-MBFu2-HZ.js";import"./xmlUtils-CtUoQO7q.js";import"./ListWindow-WS05QqV0.js";import"./PopLayout-BP55MvL7.js";import"./PoiSearchV2-D7yNeLuv.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./URLHelper-B9aplt5w.js";import"./useLayerList-DmEwJ-ws.js";import"./useScaleBar-Beed-z91.js";const Or=F({__name:"BigDiameterValve",setup(R){const n=v(),y=v(),g={},a=h({tabs:[],loading:!1,layerIds:[],layerInfos:[]}),b=h({group:[{fieldset:{desc:"管径范围"},fields:[{type:"range",rangeType:"select",field:"range",options:[{label:"50",value:50},{label:"100",value:100},{label:"300",value:300},{label:"500",value:500},{label:"800",value:800},{label:"1000",value:1e3}],startPlaceHolder:"0",endPlaceHolder:"1000+",startOptionDisabled:(t,r)=>r&&Number(r)<t.value,endOptionDisabled:(t,r)=>r&&t.value<Number(r),onChange:t=>{console.log(t)}}]},{fieldset:{desc:"选择图层"},fields:[{type:"tree",options:[],checkStrictly:!0,showCheckbox:!0,field:"layerid",nodeKey:"value"},{type:"btn-group",btns:[{perm:!0,styles:{width:"100%"},text:"展示",loading:()=>a.loading,click:()=>N()}]}]}],labelPosition:"top",gutter:12,defaultValue:{range:["100","300"]}}),w=()=>{var p,i;const t=b.group[1].fields[0],r=((i=(p=g.view)==null?void 0:p.layerViews)==null?void 0:i.items)||[];let e=[];r.forEach(s=>{var m,l;(((l=(m=s.layer)==null?void 0:m.sublayers)==null?void 0:l.items)||[]).forEach(o=>{o.type&&o.type.toLowerCase().includes("point")&&e.push({label:o.name,value:o.name,layername:o.name,type:o.type,spatialReferences:o.spatialReferences})})}),t.options=e,n.value&&(n.value.dataForm.layerid=e.map(s=>s.value))},N=async()=>{var t,r,e;a.loading=!0;try{const{range:p,layerid:i}=((t=n.value)==null?void 0:t.dataForm)||{};if(!(i!=null&&i.length))_.warning("请选择阀门图层");else{let s="1=1";const[c,m]=p||[],l=[];for(const o of i){let f=((r=(await P(o,void 0,s)).data)==null?void 0:r.features)||[];(c||m)&&(f=f.filter(D=>{const u=D.properties.管径;if(!u||!u.startsWith("DN"))return!1;const d=parseInt(u.replace("DN",""),10);return isNaN(d)?!1:(!c||d>=c)&&(!m||d<=m)})),l.push({name:o,data:f})}a.tabs=l,(e=y.value)==null||e.refreshDetail(a.tabs)}}catch(p){_.error(p.message)}a.loading=!1},k=async t=>{g.view=t,setTimeout(()=>{w()},1e3)};return(t,r)=>{const e=I;return x(),C(L,{ref_key:"refMap",ref:y,title:"大口径阀门展示",onMapLoaded:k},{default:M(()=>[V(e,{ref_key:"refForm",ref:n,config:B(b)},null,8,["config"])]),_:1},512)}}});export{Or as default};
