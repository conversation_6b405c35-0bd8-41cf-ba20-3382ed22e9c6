package org.thingsboard.server.dao.dispatch;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.smartProduction.dispatch.MessageRecord;
import org.thingsboard.server.dao.model.sql.smartProduction.dispatch.MessageTemplate;
import org.thingsboard.server.dao.sql.smartProduction.dispatch.MessageRecordMapper;
import org.thingsboard.server.dao.sql.smartProduction.dispatch.MessageTemplateMapper;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch.MessageQueueMessageRecordSendRequestInfo;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch.MessageRecordPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch.MessageRecordSendRequest;
import org.thingsboard.server.dao.util.reflection.ExceptionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static org.thingsboard.server.dao.model.sql.smartProduction.dispatch.MessageRecordStatus.SENDING;
import static org.thingsboard.server.dao.model.sql.smartProduction.dispatch.MessageRecordStatus.SUCCESS;

@Service
@Slf4j
public class MessageRecordServiceImpl implements MessageRecordService {
    @Autowired
    private MessageRecordMapper mapper;

    @Autowired
    private MessageTemplateMapper templateMapper;

    @Autowired(required = false)
    @Qualifier("alibaba-short-message-rabbit-template")
    private AmqpTemplateDeliver rabbitTemplate;


    @Override
    public IPage<MessageRecord> findAllConditional(MessageRecordPageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    public MessageRecord save(MessageRecord entity) {
        mapper.insert(entity);
        return entity;
    }

    @Override
    public boolean update(MessageRecord entity) {
        return mapper.update(entity);
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }

    @Override
    public boolean send(MessageRecordSendRequest req) {
        MessageTemplate template = templateMapper.selectById(req.getTemplateId());
        req.setCode(template.getCode());
        req.setSignKey(template.getSignKey());
        req.resolveVariables(template.getContent(), template.getVariableNames());

        List<MessageRecordSendRequest.MessageRecordSendRequestInfo> messageList = new ArrayList<>(req.getMockList());
        boolean send = mapper.send(messageList, rabbitTemplate == null, req.getReliable()) > 0;
        if (rabbitTemplate != null && req.getSendTime() == null) {
            for (MessageRecordSendRequest.MessageRecordSendRequestInfo info : messageList) {
                rabbitTemplate.convertAndSend(
                        "short-message-exchange",
                        req.getReliable() ? "to.reliable" : "to.vulnerable",
                        JSON.toJSONString(info.toMessageQueueEntity()));
            }
        }
        return send;
    }

    @Override
    public synchronized List<MessageQueueMessageRecordSendRequestInfo> getReadyMessages(boolean markAsSending) {
        List<MessageQueueMessageRecordSendRequestInfo> readyMessages = mapper.getReadyMessages();

        if (markAsSending && readyMessages.size() > 0) {
            // noinspection Convert2MethodRef 标记为Sending(发送中)状态
            int count = mapper.markBatchStatus(
                    readyMessages.stream().map(x -> x.getId()).collect(Collectors.toList()),
                    SENDING.name()
            );
            if (count != readyMessages.size()) {
                ExceptionUtils.silentThrow("存在可能的短信记录删除");
            }
        }
        return readyMessages;
    }

    @Override
    public boolean markAsSuccess(String id) {
        return mapper.markStatus(id, SUCCESS.name());
    }

    @Override
    public boolean markAsFailure(String id, String code) {
        return mapper.markStatus(id, code);
    }

}
