package org.thingsboard.server.dao.util.imodel.query.smartManagement.plan;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartManagement.plan.SMCircuitTask;
import org.thingsboard.server.dao.util.imodel.query.GeneralTaskRequest;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;

import java.util.Date;

@Getter
@Setter
public class SMCircuitTaskSaveRequest extends SaveRequest<SMCircuitTask> implements GeneralTaskRequest {
    // 任务编号
    private String code;

    // 所属巡检计划Id
    private String planId;

    // 所属片区区域、路线(点阵)Id
    private String districtAreaId;

    // 是否为常规计划
    private Boolean isNormalPlan;

    // 是否需要反馈
    private Boolean isNeedFeedback;

    // 行进方式：车巡、步行
    private String moveType;

    // 设备Id，多个用逗号隔开
    private String devices;

    // 专项设备Id，多个用逗号隔开
    private String specialDevices;

    // 任务名称
    private String name;

    // 接收人员Id
    private String receiveUserId;

    // 共同完成人Id，多个用逗号隔开
    private String collaborateUserId;

    // 到位距离
    private String presentDistance;

    // 描述
    private String remark;

    // 创建人Id
    private String creator;

    // 开始时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date beginTime;

    // 结束时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    public SMCircuitTaskSaveRequest() {
    }

    @Override
    protected SMCircuitTask build() {
        SMCircuitTask entity = new SMCircuitTask();
        entity.setCreator(currentUserUUID());
        entity.setCreateTime(createTime());
        entity.setTenantId(tenantId());
        saveSet(entity);
        return entity;
    }

    @Override
    protected SMCircuitTask update(String id) {
        SMCircuitTask entity = new SMCircuitTask();
        entity.setId(id);
        updateSet(entity);
        return entity;
    }

    private void saveSet(SMCircuitTask entity) {
        entity.setCode(code);
        entity.setPlanId(planId);
        entity.setDistrictAreaId(districtAreaId);
        entity.setIsNormalPlan(isNormalPlan);
        entity.setIsNeedFeedback(isNeedFeedback);
        entity.setMoveType(moveType);
        entity.setDevices(devices);
        entity.setSpecialDevices(specialDevices);
        entity.setName(name);
        entity.setReceiveUserId(receiveUserId);
        entity.setCollaborateUserId(collaborateUserId);
        entity.setPresentDistance(presentDistance);
        entity.setRemark(remark);
        entity.setBeginTime(beginTime);
        entity.setEndTime(endTime);
        entity.setStatus(getCreateStatus().name());
    }

    private void updateSet(SMCircuitTask entity) {
        entity.setRemark(remark);
    }


}