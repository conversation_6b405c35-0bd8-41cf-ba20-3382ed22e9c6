<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.base.BaseProductAuthorizationMapper">
    
    <resultMap type="org.thingsboard.server.dao.model.sql.base.BaseProductAuthorization" id="BaseProductAuthorizationResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="type"    column="type"    />
        <result property="description"    column="description"    />
        <result property="routeConfig"    column="route_config"    />
        <result property="config"    column="config"    />
        <result property="enabled"    column="enabled"    />
    </resultMap>

    <sql id="selectBaseProductAuthorizationVo">
        select id, name, type, description, route_config, config, enabled from base_product_authorization
    </sql>

    <select id="selectBaseProductAuthorizationList" parameterType="org.thingsboard.server.dao.model.sql.base.BaseProductAuthorization" resultMap="BaseProductAuthorizationResult">
        <include refid="selectBaseProductAuthorizationVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
            <if test="routeConfig != null  and routeConfig != ''"> and route_config = #{routeConfig}</if>
            <if test="config != null  and config != ''"> and config = #{config}</if>
            <if test="enabled != null  and enabled != ''"> and enabled = #{enabled}</if>
        </where>
    </select>
    
    <select id="selectBaseProductAuthorizationById" parameterType="String" resultMap="BaseProductAuthorizationResult">
        <include refid="selectBaseProductAuthorizationVo"/>
        where id = #{id}
    </select>

    <insert id="insertBaseProductAuthorization" parameterType="org.thingsboard.server.dao.model.sql.base.BaseProductAuthorization">
        insert into base_product_authorization
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="name != null">name,</if>
            <if test="type != null">type,</if>
            <if test="description != null">description,</if>
            <if test="routeConfig != null">route_config,</if>
            <if test="config != null">config,</if>
            <if test="enabled != null">enabled,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="name != null">#{name},</if>
            <if test="type != null">#{type},</if>
            <if test="description != null">#{description},</if>
            <if test="routeConfig != null">#{routeConfig},</if>
            <if test="config != null">#{config},</if>
            <if test="enabled != null">#{enabled},</if>
         </trim>
    </insert>

    <update id="updateBaseProductAuthorization" parameterType="org.thingsboard.server.dao.model.sql.base.BaseProductAuthorization">
        update base_product_authorization
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="type != null">type = #{type},</if>
            <if test="description != null">description = #{description},</if>
            <if test="routeConfig != null">route_config = #{routeConfig},</if>
            <if test="config != null">config = #{config},</if>
            <if test="enabled != null">enabled = #{enabled},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBaseProductAuthorizationById" parameterType="String">
        delete from base_product_authorization where id = #{id}
    </delete>

    <delete id="deleteBaseProductAuthorizationByIds" parameterType="String">
        delete from base_product_authorization where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>