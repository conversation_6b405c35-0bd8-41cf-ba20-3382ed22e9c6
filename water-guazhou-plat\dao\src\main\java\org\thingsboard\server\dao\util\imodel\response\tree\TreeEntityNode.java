package org.thingsboard.server.dao.util.imodel.response.tree;

import com.baomidou.mybatisplus.annotation.TableField;
import org.thingsboard.server.dao.util.imodel.response.ResponseMap;
import org.thingsboard.server.dao.util.imodel.response.Responsible;
import org.thingsboard.server.dao.util.imodel.response.model.ReturnHelper;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Deprecated
public abstract class TreeEntityNode implements Responsible {
    @TableField(exist = false)
    private List<TreeEntityNode> nodes;
    @TableField(exist = false)
    private int layer = 1;

    public int layer() {
        return layer;
    }

    public void layer(int layer) {
        this.layer = layer + 1;
    }

    public void addChild(TreeEntityNode node) {
        if (nodes == null)
            nodes = new ArrayList<>();
        nodes.add(node);
    }

    public void addChildren(List<? extends TreeEntityNode> nodes) {
        for (TreeEntityNode node : nodes) {
            addChild(node);
        }
    }

    public void markAsEmptyTreeNode() {
        nodes = Collections.emptyList();
    }

    public abstract String getId();

    @Override
    public Object postProcess(ReturnHelper returnHelper, Object arg) {
        ResponseMap result = returnHelper.convertToMap(this, arg);
        result.put("layer", layer);
        if (nodes == null || nodes.equals(Collections.emptyList()))
            return result;

        result.put("children", returnHelper.process(nodes, null));
        return result;
    }
}
