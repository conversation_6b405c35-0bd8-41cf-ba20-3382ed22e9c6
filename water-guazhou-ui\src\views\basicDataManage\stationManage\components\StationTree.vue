<template>
  <div class="station-tree" :class="{ isDark: useAppStore().isDark }">
    <SLTree ref="refInnerTree" :tree-data="stationTree.TreeData.value">
    </SLTree>
  </div>
</template>
<script lang="ts" setup>
import { useStationTree } from '../hooks';
import { useAppStore } from '@/store';
import router from '@/router';

const emit = defineEmits(['add', 'edit']);
const refInnerTree = ref<ISLTreeIns>();
const props = defineProps<{
  projectId?: string;
}>();
const stationTree = useStationTree();

const refreshTree = () => {
  stationTree.refreshStationTree({
    type: refInnerTree.value?.queryParams?.type,
    projectId: props.projectId
  });
};
watchEffect(() => {
  refreshTree();
});
onMounted(() => {
  refInnerTree.value?.queryParams &&
    (refInnerTree.value.queryParams.type =
      router.currentRoute.value.query?.type?.toString());
  stationTree.init({
    refreshTree,
    nodeClick: (data?: NormalOption) => {
      stationTree.TreeData.value.currentProject = data;
      if (data) {
        emit('edit', data);
        stationTree.optType.value = 'edit';
      } else {
        emit('add');
        stationTree.optType.value = 'add';
      }
    }
  });
});
defineExpose({
  refreshTree
});
</script>
<style lang="scss" scoped>
.dark {
  .station-tree {
    border-right: 1px solid var(--el-border-color);
  }
}

.station-tree {
  width: 380px;
  border-right: 1px solid var(--el-border-color);
}

.station-tree {
  &.isDark {
    :deep(.el-tree) {
      .el-tree-node {
        &.is-current > .el-tree-node__content,
        .el-tree-node__content:hover {
          background-color: #30344a;
        }
      }
    }
  }

  :deep(.el-tree) {
    .el-tree-node {
      &.is-current > .el-tree-node__content,
      .el-tree-node__content:hover {
        background-color: #d1e2f5;
        background-image: none;
      }
    }
  }
}
</style>
