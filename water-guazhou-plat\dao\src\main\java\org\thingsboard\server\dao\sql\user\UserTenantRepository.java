package org.thingsboard.server.dao.sql.user;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.repository.CrudRepository;
import org.thingsboard.server.dao.model.sql.UserCredentialsEntity;
import org.thingsboard.server.dao.model.sql.UserTenant;

import java.util.List;

public interface UserTenantRepository extends CrudRepository<UserTenant, String>, JpaRepository<UserTenant, String> {
    List<UserTenant> findByUserId(String userId);

    void deleteByTenantId(String tenantId);
}
