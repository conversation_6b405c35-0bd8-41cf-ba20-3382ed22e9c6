<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.smartService.kpi.KpiNormManualConfigMapper">
    <select id="getList" resultType="org.thingsboard.server.dao.model.sql.smartService.kpi.KpiNormManualConfig">
        select a.*, b.name as normName, b.weight weight, d.first_name as seatsName
        from tb_service_kpi_norm_manual_config a
        left join tb_service_kpi_norm_param b on a.norm_id = b.id
        left join tb_service_seats_user c on a.seats_id = c.id
        left join tb_user d on c.user_id = d.id
        <where>
            <if test="enabled != null">
                and b.enabled = #{enabled}
            </if>
            <if test="month != null and month != ''">
                and a.month = #{month}
            </if>
            <if test="seatsId != null and seatsId != ''">
                and a.seats_id = #{seatsId}
            </if>
            <if test="score != null and score != ''">
                and a.score = #{score}
            </if>
            and a.tenant_id = #{tenantId}
        </where>
        order by a.create_time desc
        offset (#{page} - 1) * #{size}  limit #{size}
    </select>

    <select id="getListCount" resultType="int">
        select count(*)
        from tb_service_kpi_norm_manual_config a
        left join tb_service_kpi_norm_param b on a.norm_id = b.id
        left join tb_service_seats_user c on a.seats_id = c.id
        left join tb_user d on c.user_id = d.id
        <where>
            <if test="enabled != null">
                and b.enabled = #{enabled}
            </if>
            <if test="month != null and month != ''">
                and a.month = #{month}
            </if>
            <if test="seatsId != null and seatsId != ''">
                and a.seats_id = #{seatsId}
            </if>
            <if test="score != null and score != ''">
                and a.score = #{score}
            </if>
            and a.tenant_id = #{tenantId}
        </where>
    </select>
</mapper>