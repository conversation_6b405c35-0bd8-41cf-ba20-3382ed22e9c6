import{d as F,c as v,r as D,b as k,Q as q,g as z,h as N,F as b,p as O,q as L,i as n,_ as V,X as G}from"./index-r0dFAfgr.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import{g as Q,a as $}from"./LayerHelper-Cn-iiqxI.js";import{g as j}from"./QueryHelper-ILO3qZqg.js";import{E as J}from"./StatisticsHelper-D-s_6AyQ.js";import{u as P}from"./arcWidgetButton-0glIxrt7.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import U from"./RightDrawerMap-D5PhmGFO.js";import X from"./StatisticsCharts-CyK-dNnC.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./project-DUuzYgGl.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./pipe-nogVzCHG.js";import"./executeForIds-BLdIsxvI.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./ArcView-DpMnCY82.js";import"./ViewHelper-BGCZjxXH.js";import"./fieldconfig-Bk3o1wi7.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./ArcStationWarning-BF9YrSzF.js";/* empty css                         */import"./useWaterPoint-Bv0z6ym6.js";import"./DateFormatter-Bm9a68Ax.js";import"./zhandian-YaGuQZe6.js";import"./useStation-DJgnSZIA.js";import"./DrawerBox-CLde5xC8.js";import"./SideDrawer-CBntChyn.js";import"./PipeDetail-CTBPYFJW.js";import"./Search-NSrhrIa_.js";import"./FormTableColumnFilter-BT7pLXIC.js";import"./config-fy91bijz.js";import"./ArcBRTools-BO92yznB.js";import"./PipeLength.vue_vue_type_script_setup_true_lang-BZfUsvDf.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./wfsUtils-DXofo3da.js";import"./usePipelineGroup-Ba1pmWz_.js";import"./GroupLayer-DhhN3FyN.js";import"./lazyLayerLoader-DbM9sT1W.js";import"./WFSLayer-1TtJp3nx.js";import"./clientSideDefaults-VQhQaYxh.js";import"./QueryEngineCapabilities-Dk3NJwmm.js";import"./wfsUtils-Du031XaC.js";import"./geojson-MBFu2-HZ.js";import"./xmlUtils-CtUoQO7q.js";import"./ListWindow-WS05QqV0.js";import"./PopLayout-BP55MvL7.js";import"./PoiSearchV2-D7yNeLuv.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./URLHelper-B9aplt5w.js";import"./useLayerList-DmEwJ-ws.js";import"./useScaleBar-Beed-z91.js";import"./useDetector-BRcb7GRN.js";import"./useHighLight-DPevRAc5.js";import"./ToolHelper-BiiInOzB.js";import"./geoserverUtils-wjOSMa7E.js";import"./echart-BoVIcYbV.js";import"./config-DqqM5K5L.js";import"./OutsideWorkOrder-C6s8joBt.js";import"./index-CpGhZCTT.js";const mi=F({__name:"ValveDiameter",setup(Z){const f=v(),E=v(),i=D({tabs:[],layerInfos:[],layerIds:[],loading:!1}),e={},R=D({group:[{fieldset:{desc:"绘制工具"},fields:[{type:"btn-group",btns:[{perm:!0,text:"",type:"default",size:"large",title:"绘制多边形",disabled:()=>i.loading,iconifyIcon:"mdi:shape-polygon-plus",click:()=>y("polygon")},{perm:!0,text:"",type:"default",size:"large",title:"绘制矩形",disabled:()=>i.loading,iconifyIcon:"ep:crop",click:()=>y("rectangle")},{perm:!0,text:"",type:"default",size:"large",title:"绘制圆形",disabled:()=>i.loading,iconifyIcon:"mdi:ellipse-outline",click:()=>y("circle")},{perm:!0,text:"",type:"default",size:"large",title:"清除图形",disabled:()=>i.loading,iconifyIcon:"ep:delete",click:()=>I()}]},{type:"btn-group",btns:[{perm:!0,text:"统计",styles:{width:"100%"},loading:()=>i.loading,click:()=>w()}]}]}],labelPosition:"top",gutter:12}),S=(t,r)=>{const o=t.label;g(o,r,!0)},x=(t,r)=>{const o=t==null?void 0:t.label;g(o,r)},C=(t,r)=>{const o=t==null?void 0:t.name;g(o,r)},g=async(t,r,o)=>{var p,s;if(t===void 0)return;const a=t.replace(/[a-z][A-Z]/gi,""),l=a==="合计"?"1=1":a==="--"?"DIAMETER is null":"DIAMETER='"+a+"'";await w(l,r);const d=(p=c.value)==null?void 0:p.getCurLayer();(s=c.value)==null||s.refreshDetail(d,o??!0)},y=t=>{var r;e.view&&(I(),(r=e.sketch)==null||r.create(t))},I=()=>{var t;(t=e.graphicsLayer)==null||t.removeAll(),e.graphics=void 0},A=async()=>{var r,o;i.layerIds=$(e.view,void 0,void 0,"阀门");const t=await G(i.layerIds);i.layerInfos=((o=(r=t.data)==null?void 0:r.result)==null?void 0:o.rows)||[]},c=v(),w=async(t,r)=>{var o,a,l,d;k.info("正在统计，请稍候...");try{if(i.loading=!0,!i.layerIds.length)k.warning("当前没有阀门");else{const p=r===void 0?i.layerIds:i.layerInfos.filter(m=>m.layername===r).map(m=>m.layerid),s=await j(p,i.layerInfos,{where:t||"1=1",geometry:(o=e.graphics)==null?void 0:o.geometry});if(r!==void 0)if(i.tabs.length){const m=i.tabs.find(h=>h.name===r),u=s.find(h=>h.name===r);m&&(m.data=u==null?void 0:u.data),(a=c.value)==null||a.refreshTable(r,p==null?void 0:p[0])}else i.tabs=s;else i.tabs=s;((l=f.value)==null?void 0:l.isCustomOpened())||(d=f.value)==null||d.toggleCustomDetail(!0)}}catch(p){console.log(p),k.error("统计失败")}i.loading=!1},{initSketch:M,destroySketch:T}=P(),_=t=>{e.graphics=t.graphics[0]},B=t=>{e.view=t,e.graphicsLayer=Q(e.view,{id:"search-valve-diameter",title:"按口径统计阀门"}),e.sketch=M(e.view,e.graphicsLayer,{createCallBack:_,updateCallBack:_}),A()};return q(()=>{var t,r;T(),(t=e.graphicsLayer)==null||t.removeAll(),(r=e.graphicsLayer)==null||r.destroy()}),(t,r)=>{const o=V;return z(),N(U,{ref_key:"refMap",ref:f,title:"按口径统计阀门","full-content":!0,onMapLoaded:B},{"detail-header":b(()=>r[1]||(r[1]=[O("span",null,"统计结果",-1)])),"detail-default":b(()=>{var a;return[L(X,{ref_key:"refStatisticsCharts",ref:c,view:e.view,"layer-ids":n(i).layerIds,"query-params":{where:"1=1",geometry:(a=e.graphics)==null?void 0:a.geometry},"statistics-params":{group_fields:["DIAMETER"],statistic_field:n(J).OBJECTID,statistic_type:"1"},tabs:n(i).tabs,unit:"个",prefix:"DN",onDetailRefreshed:r[0]||(r[0]=l=>n(i).loading=!1),onAttrRowClick:x,onBarClick:C,onRingClick:C,onTotalRowClick:S},null,8,["view","layer-ids","query-params","statistics-params","tabs"])]}),default:b(()=>[L(o,{ref_key:"refForm",ref:E,config:n(R)},null,8,["config"])]),_:1},512)}}});export{mi as default};
