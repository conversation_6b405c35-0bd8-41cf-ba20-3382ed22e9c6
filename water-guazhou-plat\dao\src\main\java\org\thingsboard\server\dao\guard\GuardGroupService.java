package org.thingsboard.server.dao.guard;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.smartProduction.guard.GuardGroup;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.guard.GuardGroupPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.guard.GuardGroupSaveRequest;

public interface GuardGroupService {
    GuardGroup findById(String id);

    IPage<GuardGroup> findAllConditional(GuardGroupPageRequest request);

    GuardGroup save(GuardGroupSaveRequest entity);

    boolean delete(String id);

}
