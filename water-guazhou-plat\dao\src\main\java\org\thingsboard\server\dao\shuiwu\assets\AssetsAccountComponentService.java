package org.thingsboard.server.dao.shuiwu.assets;

import org.thingsboard.server.dao.model.sql.shuiwu.assets.AssetsAccountComponentEntity;

import java.util.List;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2021-10-20
 */
public interface AssetsAccountComponentService {
    void save(AssetsAccountComponentEntity assetsAccountComponentEntity);

    AssetsAccountComponentEntity findOne(String sparePartId);

    List<AssetsAccountComponentEntity> findListByPid(String id);

    void deleteByPid(String id);
}
