import { seatsUserList } from '@/api/CTI/seatsManage'

import { dictList, workOrderTypeList } from '@/api/CTI/systemConfiguration'

const useCTI = () => {
  const getOrderFromOption = ():NormalOption[] => {
    return [
      { label: '用户', value: '用户' },
      { label: '市长热线', value: '市长热线' },
      { label: '公司内部', value: '公司内部' },
      { label: '其他', value: '其他' },
      { label: '电台媒体', value: '电台媒体' },
      { label: '网站平台', value: '网站平台' }
    ]
  }
  const getAreaOption = ():NormalOption[] => {
    return [
      { label: '东区营业所', value: '东区营业所' },
      { label: '东南区营业所', value: '东南区营业所' },
      { label: '南区营业所', value: '南区营业所' },
      { label: '西区营业所', value: '西区营业所' },
      { label: '管网中心', value: '管网中心' },
      { label: '其它', value: '其它' }
    ]
  }
  const getOrderTypeOption = async () => {
    const params = {
      isDel: 0
    }
    const res = await workOrderTypeList(params)
    const data = res.data?.data
    return data.map(d => {
      return { label: d.name, value: d.id, data: d.children }
    })
  }
  const getOrderStatus = (isAll?:boolean):NormalOption[] => {
    const status = [
      { label: '待接单', value: 'PENDING' },
      { label: '分派', value: 'ASSIGN' },
      { label: '接收', value: 'RESOLVING' },
      { label: '到场', value: 'ARRIVING' },
      { label: '处理', value: 'PROCESSING' },
      { label: '完工', value: 'APPROVED' },
      { label: '回访', value: 'REVISIT' },
      { label: '办结', value: 'CONCLUDE' },
      { label: '终止', value: 'TERMINATED' },
      { label: '审核退回', value: 'CHARGEBACK' },
      { label: '退单驳回', value: 'REJECTED' }
    ]
    if (isAll) status.unshift({ label: '全部', value: '' })
    return status
  }

  const statusOption = ():NormalOption[] => {
    return [
      { label: '全部', value: '' },
      { label: '未接通', value: '0' },
      { label: '接通', value: '1' },
      { label: '忙', value: '2' },
      { label: '语音留言', value: '3' }
    ]
  }

  const directOptions = ():NormalOption[] => {
    return [
      { label: '全部', value: '' },
      { label: '呼入', value: '0' },
      { label: '呼出', value: '1' },
      { label: '内线', value: '2' }
    ]
  }

  const seatUserList = async () => {
    const params = {
      page: 1,
      size: 999
    }
    const res = await seatsUserList(params)
    const data = res.data?.data.data
    return data.map(d => {
      return { label: d.userName, value: d.id }
    })
  }
  const areaList = async () => {
    const params = {
      pid: '2d28676d2f3d1541c23c306bc895e276'
    }
    const res = await dictList(params)
    const data = res.data?.data
    return data.map(d => {
      return { label: d.name, value: d.id }
    })
  }
  const sourceList = async () => {
    const params = {
      pid: 'c434676651adcd8e11d1e563eb687e06'
    }
    const res = await dictList(params)
    const data = res.data?.data
    return data.map(d => {
      return { label: d.name, value: d.id }
    })
  }

  return {
    getOrderTypeOption,
    getOrderStatus,
    sourceList,
    areaList,
    directOptions,
    statusOption,
    seatUserList
  }
}
export default useCTI
