package org.thingsboard.server.dao.sql.smartOperation.construction;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionContractAmend;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionContractAmendPageRequest;

@Mapper
public interface SoConstructionContractAmendMapper extends BaseMapper<SoConstructionContractAmend> {
    IPage<SoConstructionContractAmend> findByPage(SoConstructionContractAmendPageRequest request);

    boolean update(SoConstructionContractAmend entity);

    boolean updateFully(SoConstructionContractAmend entity);
}
