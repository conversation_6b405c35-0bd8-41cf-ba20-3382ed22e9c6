const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["static/js/WhereClause-CNjGNHY9.js","static/js/index-r0dFAfgr.js","static/css/index-ByiGXvnH.css","static/js/executionError-BOo4jP8A.js","static/js/Point-WxyopZva.js","static/js/MapView-DaoQedLH.js","static/js/widget-BcWKanF2.js","static/js/pe-B8dP0-Ut.js"])))=>i.map(i=>d[i]);
import{a3 as y,T as g}from"./index-r0dFAfgr.js";import{i as h,s as d}from"./Point-WxyopZva.js";import{L as w,S as v,x as b}from"./widget-BcWKanF2.js";import{d3 as x,d4 as _,am as E,d5 as F}from"./MapView-DaoQedLH.js";import"./pe-B8dP0-Ut.js";const p=h.getLogger("esri.layers.support.labelFormatUtils"),m={type:"simple",evaluate:()=>null},V={getAttribute:(a,n)=>a.field(n)};async function S(a,n,e){if(!a||!a.symbol||!n)return m;const o=a.where,i=x(a),s=o?await y(()=>import("./WhereClause-CNjGNHY9.js").then(r=>r.W),__vite__mapDeps([0,1,2,3,4,5,6,7])):null;let l;if(i.type==="arcade"){const r=await _(i.expression,e,n);if(g(r))return m;l={type:"arcade",evaluate(u){try{const t=r.evaluate({$feature:"attributes"in u?r.repurposeFeature(u):u});if(t!=null)return t.toString()}catch{p.error(new d("arcade-expression-error","Encountered an error when evaluating label expression for feature",{feature:u,expression:i}))}return null},needsHydrationToEvaluate:()=>F(i.expression)==null}}else l={type:"simple",evaluate:r=>i.expression.replace(/{[^}]*}/g,u=>{const t=u.slice(1,-1),c=n.get(t);if(!c)return u;let f=null;return"attributes"in r?r&&r.attributes&&(f=r.attributes[c.name]):f=r.field(c.name),f==null?"":L(f,c)})};if(o){let r;try{r=s.WhereClause.create(o,n)}catch(t){return p.error(new d("bad-where-clause","Encountered an error when evaluating where clause, ignoring",{where:o,error:t})),m}const u=l.evaluate;l.evaluate=t=>{const c="attributes"in t?void 0:V;try{if(r.testFeature(t,c))return u(t)}catch(f){p.error(new d("bad-where-clause","Encountered an error when evaluating where clause for feature",{where:o,feature:t,error:f}))}return null}}return l}function L(a,n){if(a==null)return"";const e=n.domain;if(e){if(e.type==="codedValue"||e.type==="coded-value"){const i=a;for(const s of e.codedValues)if(s.code===i)return s.name}else if(e.type==="range"){const i=+a,s="range"in e?e.range[0]:e.minValue,l="range"in e?e.range[1]:e.maxValue;if(s<=i&&i<=l)return e.name}}let o=a;return n.type==="date"||n.type==="esriFieldTypeDate"?o=w(o,v("short-date")):E(n)&&(o=b(+o)),o||""}export{S as createLabelFunction,L as formatField};
