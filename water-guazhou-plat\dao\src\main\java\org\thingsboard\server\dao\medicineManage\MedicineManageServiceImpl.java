package org.thingsboard.server.dao.medicineManage;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.DTO.CountObjDTO;
import org.thingsboard.server.dao.model.sql.MedicineManage;
import org.thingsboard.server.dao.model.sql.alarmV2.AlarmCenter;
import org.thingsboard.server.dao.sql.medicineManage.MedicineManageRepository;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

@Slf4j
@Service
public class MedicineManageServiceImpl implements MedicineManageService {

    @Autowired
    private MedicineManageRepository medicineManageRepository;

    @Override
    public MedicineManage save(MedicineManage medicineManage) {
        return medicineManageRepository.save(medicineManage);
    }

    @Override
    public PageData<MedicineManage> list(int page, int size, String stationId) {
        PageRequest pageRequest = new PageRequest(page - 1, size, new Sort(Sort.Direction.DESC, "time"));

        Page<MedicineManage> pageResult = medicineManageRepository.findByStationId(stationId, pageRequest);

        return new PageData<>(pageResult.getTotalElements(), pageResult.getContent());
    }

    @Override
    public List<MedicineManage> findAll(String stationId) {
        return medicineManageRepository.findByStationId(stationId);
    }

    @Override
    public List<String> typeList(TenantId tenantId) {
        return medicineManageRepository.groupByMedicineType();
    }

    @Override
    public List countByType(Long startTime, Long endTime, TenantId tenantId) {
        return medicineManageRepository.countByType(UUIDConverter.fromTimeUUID(tenantId.getId()), new Date(startTime), new Date(endTime));
    }

    @Override
    public List<JSONObject> yearReport(TenantId tenantId) {
        Calendar instance = Calendar.getInstance();
        instance.set(Calendar.MONTH, instance.getActualMinimum(Calendar.MONTH));
        instance.set(Calendar.DAY_OF_MONTH, instance.getActualMinimum(Calendar.DAY_OF_MONTH));
        instance.set(Calendar.HOUR_OF_DAY, 0);
        instance.set(Calendar.MINUTE, 0);
        instance.set(Calendar.SECOND, 0);
        Date yearStart = instance.getTime();
        Date yearEnd = new Date();

        List<MedicineManage> medicineManageList = medicineManageRepository
                .findByTimeBetweenAndTenantId(yearStart, yearEnd, UUIDConverter.fromTimeUUID(tenantId.getId()));

        // 将数据按每个月分组
        Map<String, List<MedicineManage>> monthMedicineMap = new LinkedHashMap<>();
        // 初始化Map
        for (int i = 1; i <= 12; i++) {
            monthMedicineMap.put(instance.get(Calendar.YEAR) + "-" + String.format("%02d", i), new ArrayList<>());
        }
        // 进行数据分组
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM");
        for (MedicineManage medicineManage : medicineManageList) {
            Date time = medicineManage.getTime();
            String format = dateFormat.format(time);
            List<MedicineManage> monthAlarmList = monthMedicineMap.get(format);
            monthAlarmList.add(medicineManage);
        }

        // 数据统计
        List<JSONObject> resultList = new ArrayList<>();
        for (Map.Entry<String, List<MedicineManage>> entry : monthMedicineMap.entrySet()) {
            String key = entry.getKey();
            List<MedicineManage> value = entry.getValue();
            JSONObject data = new JSONObject();
            data.put("month", key);
            List<JSONObject> countList = new ArrayList<>();
            if (value != null) {
                Map<String, BigDecimal> countMap = new HashMap<>();
                for (MedicineManage medicineManage : value) {
                    String medicineType = medicineManage.getMedicineType();
                    BigDecimal decimal = countMap.get(medicineType);
                    if (decimal == null) {
                        decimal = new BigDecimal("0");
                    }
                    decimal = decimal.add(medicineManage.getNum());

                    countMap.put(medicineType, decimal);
                }
                for (Map.Entry<String, BigDecimal> countEntry : countMap.entrySet()) {
                    String key1 = countEntry.getKey();
                    BigDecimal value1 = countEntry.getValue();
                    JSONObject countObj = new JSONObject();
                    countObj.put("type", key1);
                    countObj.put("count", value1);
                    countList.add(countObj);
                }
            }
            if (countList.size() > 0) {
                data.put("count", countList);
            } else {
                data.put("count", null);
            }

            resultList.add(data);
        }

        return resultList;
    }

    @Override
    public List<MedicineManage> findByTimeAndStationId(Long startTime, Long endTime, String stationId) {
        return medicineManageRepository.findByTimeBetweenAndTenantId(new Date(startTime), new Date(endTime), stationId);
    }

    @Override
    public void remove(List<String> ids) {
        for (String id : ids) {
            medicineManageRepository.delete(id);
        }
    }
}
