"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[9936,3172,9880],{99880:(e,t,r)=>{r.d(t,{V:()=>l});var n=r(68773),s=(r(3172),r(20102)),i=r(92604),o=r(17452);const a=i.Z.getLogger("esri.assets");function l(e){if(!n.Z.assetsPath)throw a.errorOnce("The API assets location needs to be set using config.assetsPath. More information: https://arcg.is/1OzLe50"),new s.Z("assets:path-not-set","config.assetsPath is not set");return(0,o.v_)(n.Z.assetsPath,e)}},88764:(e,t,r)=>{r.d(t,{q:()=>l});var n,s,i,o={},a={get exports(){return o},set exports(e){o=e}};n=a,s=function(){function e(r,n,s,i,o){for(;i>s;){if(i-s>600){var a=i-s+1,l=n-s+1,c=Math.log(a),h=.5*Math.exp(2*c/3),u=.5*Math.sqrt(c*h*(a-h)/a)*(l-a/2<0?-1:1);e(r,n,Math.max(s,Math.floor(n-l*h/a+u)),Math.min(i,Math.floor(n+(a-l)*h/a+u)),o)}var d=r[n],m=s,f=i;for(t(r,s,n),o(r[i],d)>0&&t(r,s,i);m<f;){for(t(r,m,f),m++,f--;o(r[m],d)<0;)m++;for(;o(r[f],d)>0;)f--}0===o(r[s],d)?t(r,s,f):t(r,++f,i),f<=n&&(s=f+1),n<=f&&(i=f-1)}}function t(e,t,r){var n=e[t];e[t]=e[r],e[r]=n}function r(e,t){return e<t?-1:e>t?1:0}return function(t,n,s,i,o){e(t,n,s||0,i||t.length-1,o||r)}},void 0!==(i=s())&&(n.exports=i);const l=o},10661:(e,t,r)=>{r.d(t,{s:()=>s});var n=r(42100);class s extends n.s{notify(){const e=this._observers;if(e&&e.length>0){const t=e.slice();for(const e of t)e.onInvalidated(),e.onCommitted()}}}},24133:(e,t,r)=>{r.d(t,{Q:()=>a});var n=r(67676),s=r(70586),i=r(44553),o=r(88764);class a{constructor(e=9,t){this._compareMinX=u,this._compareMinY=d,this._toBBox=e=>e,this._maxEntries=Math.max(4,e||9),this._minEntries=Math.max(2,Math.ceil(.4*this._maxEntries)),t&&("function"==typeof t?this._toBBox=t:this._initFormat(t)),this.clear()}destroy(){this.clear(),_.prune(),b.prune(),M.prune(),v.prune()}all(e){this._all(this._data,e)}search(e,t){let r=this._data;const n=this._toBBox;if(w(e,r))for(_.clear();r;){for(let s=0,i=r.children.length;s<i;s++){const i=r.children[s],o=r.leaf?n(i):i;w(e,o)&&(r.leaf?t(i):g(e,o)?this._all(i,t):_.push(i))}r=_.pop()}}collides(e){let t=this._data;const r=this._toBBox;if(!w(e,t))return!1;for(_.clear();t;){for(let n=0,s=t.children.length;n<s;n++){const s=t.children[n],i=t.leaf?r(s):s;if(w(e,i)){if(t.leaf||g(e,i))return!0;_.push(s)}}t=_.pop()}return!1}load(e){if(!e.length)return this;if(e.length<this._minEntries){for(let t=0,r=e.length;t<r;t++)this.insert(e[t]);return this}let t=this._build(e.slice(0,e.length),0,e.length-1,0);if(this._data.children.length)if(this._data.height===t.height)this._splitRoot(this._data,t);else{if(this._data.height<t.height){const e=this._data;this._data=t,t=e}this._insert(t,this._data.height-t.height-1,!0)}else this._data=t;return this}insert(e){return e&&this._insert(e,this._data.height-1),this}clear(){return this._data=new O([]),this}remove(e){if(!e)return this;let t,r=this._data,i=null,o=0,a=!1;const l=this._toBBox(e);for(M.clear(),v.clear();r||M.length>0;){if(r||(r=(0,s.j0)(M.pop()),i=M.data[M.length-1],o=v.pop()??0,a=!0),r.leaf&&(t=(0,n.cq)(r.children,e,r.children.length,r.indexHint),-1!==t))return r.children.splice(t,1),M.push(r),this._condense(M),this;a||r.leaf||!g(r,l)?i?(o++,r=i.children[o],a=!1):r=null:(M.push(r),v.push(o),o=0,i=r,r=r.children[0])}return this}toJSON(){return this._data}fromJSON(e){return this._data=e,this}_all(e,t){let r=e;for(b.clear();r;){if(!0===r.leaf)for(const e of r.children)t(e);else b.pushArray(r.children);r=b.pop()??null}}_build(e,t,r,n){const s=r-t+1;let i=this._maxEntries;if(s<=i){const n=new O(e.slice(t,r+1));return l(n,this._toBBox),n}n||(n=Math.ceil(Math.log(s)/Math.log(i)),i=Math.ceil(s/i**(n-1)));const o=new S([]);o.height=n;const a=Math.ceil(s/i),c=a*Math.ceil(Math.sqrt(i));y(e,t,r,c,this._compareMinX);for(let s=t;s<=r;s+=c){const t=Math.min(s+c-1,r);y(e,s,t,a,this._compareMinY);for(let r=s;r<=t;r+=a){const s=Math.min(r+a-1,t);o.children.push(this._build(e,r,s,n-1))}}return l(o,this._toBBox),o}_chooseSubtree(e,t,r,n){for(;n.push(t),!0!==t.leaf&&n.length-1!==r;){let r,n=1/0,s=1/0;for(let i=0,o=t.children.length;i<o;i++){const o=t.children[i],a=m(o),l=p(e,o)-a;l<s?(s=l,n=a<n?a:n,r=o):l===s&&a<n&&(n=a,r=o)}t=r||t.children[0]}return t}_insert(e,t,r){const n=this._toBBox,s=r?e:n(e);M.clear();const i=this._chooseSubtree(s,this._data,t,M);for(i.children.push(e),h(i,s);t>=0&&M.data[t].children.length>this._maxEntries;)this._split(M,t),t--;this._adjustParentBBoxes(s,M,t)}_split(e,t){const r=e.data[t],n=r.children.length,s=this._minEntries;this._chooseSplitAxis(r,s,n);const i=this._chooseSplitIndex(r,s,n);if(!i)return void console.log("  Error: assertion failed at PooledRBush._split: no valid split index");const o=r.children.splice(i,r.children.length-i),a=r.leaf?new O(o):new S(o);a.height=r.height,l(r,this._toBBox),l(a,this._toBBox),t?e.data[t-1].children.push(a):this._splitRoot(r,a)}_splitRoot(e,t){this._data=new S([e,t]),this._data.height=e.height+1,l(this._data,this._toBBox)}_chooseSplitIndex(e,t,r){let n,s,i;n=s=1/0;for(let o=t;o<=r-t;o++){const t=c(e,0,o,this._toBBox),a=c(e,o,r,this._toBBox),l=x(t,a),h=m(t)+m(a);l<n?(n=l,i=o,s=h<s?h:s):l===n&&h<s&&(s=h,i=o)}return i}_chooseSplitAxis(e,t,r){const n=e.leaf?this._compareMinX:u,s=e.leaf?this._compareMinY:d;this._allDistMargin(e,t,r,n)<this._allDistMargin(e,t,r,s)&&e.children.sort(n)}_allDistMargin(e,t,r,n){e.children.sort(n);const s=this._toBBox,i=c(e,0,t,s),o=c(e,r-t,r,s);let a=f(i)+f(o);for(let n=t;n<r-t;n++){const t=e.children[n];h(i,e.leaf?s(t):t),a+=f(i)}for(let n=r-t-1;n>=t;n--){const t=e.children[n];h(o,e.leaf?s(t):t),a+=f(o)}return a}_adjustParentBBoxes(e,t,r){for(let n=r;n>=0;n--)h(t.data[n],e)}_condense(e){for(let t=e.length-1;t>=0;t--){const r=e.data[t];if(0===r.children.length)if(t>0){const s=e.data[t-1],i=s.children;i.splice((0,n.cq)(i,r,i.length,s.indexHint),1)}else this.clear();else l(r,this._toBBox)}}_initFormat(e){const t=["return a"," - b",";"];this._compareMinX=new Function("a","b",t.join(e[0])),this._compareMinY=new Function("a","b",t.join(e[1])),this._toBBox=new Function("a","return {minX: a"+e[0]+", minY: a"+e[1]+", maxX: a"+e[2]+", maxY: a"+e[3]+"};")}}function l(e,t){c(e,0,e.children.length,t,e)}function c(e,t,r,n,s){s||(s=new O([])),s.minX=1/0,s.minY=1/0,s.maxX=-1/0,s.maxY=-1/0;for(let i,o=t;o<r;o++)i=e.children[o],h(s,e.leaf?n(i):i);return s}function h(e,t){e.minX=Math.min(e.minX,t.minX),e.minY=Math.min(e.minY,t.minY),e.maxX=Math.max(e.maxX,t.maxX),e.maxY=Math.max(e.maxY,t.maxY)}function u(e,t){return e.minX-t.minX}function d(e,t){return e.minY-t.minY}function m(e){return(e.maxX-e.minX)*(e.maxY-e.minY)}function f(e){return e.maxX-e.minX+(e.maxY-e.minY)}function p(e,t){return(Math.max(t.maxX,e.maxX)-Math.min(t.minX,e.minX))*(Math.max(t.maxY,e.maxY)-Math.min(t.minY,e.minY))}function x(e,t){const r=Math.max(e.minX,t.minX),n=Math.max(e.minY,t.minY),s=Math.min(e.maxX,t.maxX),i=Math.min(e.maxY,t.maxY);return Math.max(0,s-r)*Math.max(0,i-n)}function g(e,t){return e.minX<=t.minX&&e.minY<=t.minY&&t.maxX<=e.maxX&&t.maxY<=e.maxY}function w(e,t){return t.minX<=e.maxX&&t.minY<=e.maxY&&t.maxX>=e.minX&&t.maxY>=e.minY}function y(e,t,r,n,i){const a=[t,r];for(;a.length;){const t=(0,s.j0)(a.pop()),r=(0,s.j0)(a.pop());if(t-r<=n)continue;const l=r+Math.ceil((t-r)/n/2)*n;(0,o.q)(e,l,r,t,i),a.push(r,l,l,t)}}const _=new i.Z,b=new i.Z,M=new i.Z,v=new i.Z({deallocator:void 0});class q{constructor(){this.minX=1/0,this.minY=1/0,this.maxX=-1/0,this.maxY=-1/0}}class T extends q{constructor(){super(...arguments),this.height=1,this.indexHint=new n.SO}}class O extends T{constructor(e){super(),this.children=e,this.leaf=!0}}class S extends T{constructor(e){super(),this.children=e,this.leaf=!1}}},24470:(e,t,r)=>{r.d(t,{Gv:()=>w,HH:()=>c,SO:()=>u,Ue:()=>i,al:()=>a,cS:()=>p,fS:()=>g,jE:()=>d,jn:()=>h,kK:()=>m,oJ:()=>l,r3:()=>f}),r(80442),r(22021);var n=r(70586),s=r(6570);function i(e=y){return[e[0],e[1],e[2],e[3]]}function o(e,t){return e!==t&&(e[0]=t[0],e[1]=t[1],e[2]=t[2],e[3]=t[3]),e}function a(e,t,r,n,s=i()){return s[0]=e,s[1]=t,s[2]=r,s[3]=n,s}function l(e,t=i()){return t[0]=e.xmin,t[1]=e.ymin,t[2]=e.xmax,t[3]=e.ymax,t}function c(e,t){return new s.Z({xmin:e[0],ymin:e[1],xmax:e[2],ymax:e[3],spatialReference:t})}function h(e,t,r){if((0,n.Wi)(t))o(r,e);else if("length"in t)x(t)?(r[0]=Math.min(e[0],t[0]),r[1]=Math.min(e[1],t[1]),r[2]=Math.max(e[2],t[2]),r[3]=Math.max(e[3],t[3])):2!==t.length&&3!==t.length||(r[0]=Math.min(e[0],t[0]),r[1]=Math.min(e[1],t[1]),r[2]=Math.max(e[2],t[0]),r[3]=Math.max(e[3],t[1]));else switch(t.type){case"extent":r[0]=Math.min(e[0],t.xmin),r[1]=Math.min(e[1],t.ymin),r[2]=Math.max(e[2],t.xmax),r[3]=Math.max(e[3],t.ymax);break;case"point":r[0]=Math.min(e[0],t.x),r[1]=Math.min(e[1],t.y),r[2]=Math.max(e[2],t.x),r[3]=Math.max(e[3],t.y)}}function u(e){return function(e){return(0,n.Wi)(e)||e[0]>=e[2]?0:e[2]-e[0]}(e)*function(e){return e[1]>=e[3]?0:e[3]-e[1]}(e)}function d(e,t,r){return t>=e[0]&&r>=e[1]&&t<=e[2]&&r<=e[3]}function m(e,t){return Math.max(t[0],e[0])<=Math.min(t[2],e[2])&&Math.max(t[1],e[1])<=Math.min(t[3],e[3])}function f(e,t){return t[0]>=e[0]&&t[2]<=e[2]&&t[1]>=e[1]&&t[3]<=e[3]}function p(e){return e?o(e,w):i(w)}function x(e){return null!=e&&4===e.length}function g(e,t){return x(e)&&x(t)?e[0]===t[0]&&e[1]===t[1]&&e[2]===t[2]&&e[3]===t[3]:e===t}const w=[1/0,1/0,-1/0,-1/0],y=[0,0,0,0]},61787:(e,t,r)=>{r.r(t),r.d(t,{default:()=>a});var n=r(80442),s=r(70586),i=r(24133),o=r(66459);class a{async createIndex(e,t){const r=new Array;if(!e.vertexAttributes||!e.vertexAttributes.position)return new i.Q;const n=this._createMeshData(e),o=(0,s.pC)(t)?await t.invoke("createIndexThread",n,{transferList:r}):this.createIndexThread(n).result;return this._createPooledRBush().fromJSON(o)}createIndexThread(e){const t=new Float64Array(e.position),r=this._createPooledRBush();return e.components?this._createIndexComponentsThread(r,t,e.components.map((e=>new Uint32Array(e)))):this._createIndexAllThread(r,t)}_createIndexAllThread(e,t){const r=new Array(t.length/9);let n=0;for(let e=0;e<t.length;e+=9)r[n++]=l(t,e+0,e+3,e+6);return e.load(r),{result:e.toJSON()}}_createIndexComponentsThread(e,t,r){let n=0;for(const e of r)n+=e.length/3;const s=new Array(n);let i=0;for(const e of r)for(let r=0;r<e.length;r+=3)s[i++]=l(t,3*e[r+0],3*e[r+1],3*e[r+2]);return e.load(s),{result:e.toJSON()}}_createMeshData(e){const t=(e.transform?(0,o.I5)({position:e.vertexAttributes.position,normal:null,tangent:null},e.transform,e.spatialReference).position:e.vertexAttributes.position).buffer;return!e.components||e.components.some((e=>!e.faces))?{position:t}:{position:t,components:e.components.map((e=>e.faces))}}_createPooledRBush(){return new i.Q(9,(0,n.Z)("esri-csp-restrictions")?e=>e:[".minX",".minY",".maxX",".maxY"])}}function l(e,t,r,n){return{minX:Math.min(e[t+0],e[r+0],e[n+0]),maxX:Math.max(e[t+0],e[r+0],e[n+0]),minY:Math.min(e[t+1],e[r+1],e[n+1]),maxY:Math.max(e[t+1],e[r+1],e[n+1]),p0:[e[t+0],e[t+1],e[t+2]],p1:[e[r+0],e[r+1],e[r+2]],p2:[e[n+0],e[n+1],e[n+2]]}}},3172:(e,t,r)=>{r.r(t),r.d(t,{default:()=>f});var n=r(68773),s=r(40330),i=r(20102),o=r(80442),a=r(22974),l=r(70586),c=r(95330),h=r(17452),u=r(19745),d=r(71058),m=r(85958);async function f(e,t){const a=(0,h.HK)(e),u=(0,h.jc)(e);u||a||(e=(0,h.Fv)(e));const w={url:e,requestOptions:{...(0,l.Wg)(t)}};let y=(0,h.oh)(e);if(y){const e=await async function(e,t){if(null!=e.responseData)return e.responseData;if(e.headers&&(t.requestOptions.headers={...t.requestOptions.headers,...e.headers}),e.query&&(t.requestOptions.query={...t.requestOptions.query,...e.query}),e.before){let r,n;try{n=await e.before(t)}catch(e){r=q("request:interceptor",e,t)}if((n instanceof Error||n instanceof i.Z)&&(r=q("request:interceptor",n,t)),r)throw e.error&&e.error(r),r;return n}}(y,w);if(null!=e)return{data:e,getHeader:b,httpStatus:200,requestOptions:w.requestOptions,url:w.url};y.after||y.error||(y=null)}if(e=w.url,"image"===(t=w.requestOptions).responseType){if((0,o.Z)("host-webworker")||(0,o.Z)("host-node"))throw q("request:invalid-parameters",new Error("responseType 'image' is not supported in Web Workers or Node environment"),w)}else if(a)throw q("request:invalid-parameters",new Error("Data URLs are not supported for responseType = "+t.responseType),w);if("head"===t.method){if(t.body)throw q("request:invalid-parameters",new Error("body parameter cannot be set when method is 'head'"),w);if(a||u)throw q("request:invalid-parameters",new Error("data and blob URLs are not supported for method 'head'"),w)}if(await async function(){(0,o.Z)("host-webworker")?p||(p=await r.e(9884).then(r.bind(r,29884))):f._abortableFetch||(f._abortableFetch=globalThis.fetch.bind(globalThis))}(),p)return p.execute(e,t);const _=new AbortController;(0,c.fu)(t,(()=>_.abort()));const M={controller:_,credential:void 0,credentialToken:void 0,fetchOptions:void 0,hasToken:!1,interceptor:y,params:w,redoRequest:!1,useIdentity:x.useIdentity,useProxy:!1,useSSL:!1,withCredentials:!1},v=await async function(e){let t,r;await async function(e){const t=e.params.url,r=e.params.requestOptions,i=e.controller.signal,o=r.body;let a=null,l=null;if(g&&"HTMLFormElement"in globalThis&&(o instanceof FormData?a=o:o instanceof HTMLFormElement&&(a=new FormData(o))),"string"==typeof o&&(l=o),e.fetchOptions={cache:r.cacheBust&&!f._abortableFetch.polyfill?"no-cache":"default",credentials:"same-origin",headers:r.headers||{},method:"head"===r.method?"HEAD":"GET",mode:"cors",priority:x.priority,redirect:"follow",signal:i},(a||l)&&(e.fetchOptions.body=a||l),"anonymous"===r.authMode&&(e.useIdentity=!1),e.hasToken=!!(/token=/i.test(t)||r.query?.token||a?.get("token")),!e.hasToken&&n.Z.apiKey&&(0,d.r)(t)&&(r.query||(r.query={}),r.query.token=n.Z.apiKey,e.hasToken=!0),e.useIdentity&&!e.hasToken&&!e.credentialToken&&!O(t)&&!(0,c.Hc)(i)){let n;"immediate"===r.authMode?(await T(),n=await s.id.getCredential(t,{signal:i}),e.credential=n):"no-prompt"===r.authMode?(await T(),n=await s.id.getCredential(t,{prompt:!1,signal:i}).catch((()=>{})),e.credential=n):s.id&&(n=s.id.findCredential(t)),n&&(e.credentialToken=n.token,e.useSSL=!!n.ssl)}}(e);try{do{[t,r]=await S(e)}while(!await C(e,t,r))}catch(r){const n=q("request:server",r,e.params,t);throw n.details.ssl=e.useSSL,e.interceptor&&e.interceptor.error&&e.interceptor.error(n),n}const i=e.params.url;if(r&&/\/sharing\/rest\/(accounts|portals)\/self/i.test(i)){if(!e.hasToken&&!e.credentialToken&&r.user?.username&&!(0,h.kl)(i)){const e=(0,h.P$)(i,!0);e&&x.trustedServers.push(e)}Array.isArray(r.authorizedCrossOriginNoCorsDomains)&&(0,m.Hu)(r.authorizedCrossOriginNoCorsDomains)}const o=e.credential;if(o&&s.id){const e=s.id.findServerInfo(o.server);let t=e&&e.owningSystemUrl;if(t){t=t.replace(/\/?$/,"/sharing");const e=s.id.findCredential(t,o.userId);e&&-1===s.id._getIdenticalSvcIdx(t,e)&&e.resources.unshift(t)}}return{data:r,getHeader:t?e=>t?.headers.get(e):b,httpStatus:t?.status??200,requestOptions:e.params.requestOptions,ssl:e.useSSL,url:e.params.url}}(M);return y?.after?.(v),v}let p;const x=n.Z.request,g="FormData"in globalThis,w=[499,498,403,401],y=["COM_0056","COM_0057","SB_0008"],_=[/\/arcgis\/tokens/i,/\/sharing(\/rest)?\/generatetoken/i,/\/rest\/info/i],b=()=>null,M=Symbol();function v(e){const t=(0,h.P$)(e);return!t||t.endsWith(".arcgis.com")||f._corsServers.includes(t)||(0,h.kl)(t)}function q(e,t,r,n){let s="Error";const o={url:r.url,requestOptions:r.requestOptions,getHeader:b,ssl:!1};if(t instanceof i.Z)return t.details?(t.details=(0,a.d9)(t.details),t.details.url=r.url,t.details.requestOptions=r.requestOptions):t.details=o,t;if(t){const e=n&&(e=>n.headers.get(e)),r=n&&n.status,i=t.message;i&&(s=i),e&&(o.getHeader=e),o.httpStatus=(null!=t.httpCode?t.httpCode:t.code)||r||0,o.subCode=t.subcode,o.messageCode=t.messageCode,"string"==typeof t.details?o.messages=[t.details]:o.messages=t.details,o.raw=M in t?t[M]:t}return(0,c.D_)(t)?(0,c.zE)():new i.Z(e,s,o)}async function T(){s.id||await Promise.all([r.e(6261),r.e(1400),r.e(450)]).then(r.bind(r,73660))}function O(e){return _.some((t=>t.test(e)))}async function S(e){let t=e.params.url;const r=e.params.requestOptions,n=e.fetchOptions??{},i=(0,h.jc)(t)||(0,h.HK)(t),a=r.responseType||"json",l=i?0:null!=r.timeout?r.timeout:x.timeout;let d=!1;if(!i){e.useSSL&&(t=(0,h.hO)(t)),r.cacheBust&&"default"===n.cache&&(t=(0,h.ZN)(t,"request.preventCache",Date.now()));let i={...r.query};e.credentialToken&&(i.token=e.credentialToken);let a=(0,h.B7)(i);(0,o.Z)("esri-url-encodes-apostrophe")&&(a=a.replace(/'/g,"%27"));const l=t.length+1+a.length;let c;d="delete"===r.method||"post"===r.method||"put"===r.method||!!r.body||l>x.maxUrlLength;const f=r.useProxy||!!(0,h.ed)(t);if(f){const e=(0,h.b7)(t);c=e.path,!d&&c.length+1+l>x.maxUrlLength&&(d=!0),e.query&&(i={...e.query,...i})}if("HEAD"===n.method&&(d||f)){if(d){if(l>x.maxUrlLength)throw q("request:invalid-parameters",new Error("URL exceeds maximum length"),e.params);throw q("request:invalid-parameters",new Error("cannot use POST request when method is 'head'"),e.params)}if(f)throw q("request:invalid-parameters",new Error("cannot use proxy when method is 'head'"),e.params)}if(d?(n.method="delete"===r.method?"DELETE":"put"===r.method?"PUT":"POST",r.body?t=(0,h.fl)(t,i):(n.body=(0,h.B7)(i),n.headers||(n.headers={}),n.headers["Content-Type"]="application/x-www-form-urlencoded")):t=(0,h.fl)(t,i),f&&(e.useProxy=!0,t=`${c}?${t}`),i.token&&g&&n.body instanceof FormData&&!(0,u.P)(t)&&n.body.set("token",i.token),r.hasOwnProperty("withCredentials"))e.withCredentials=r.withCredentials;else if(!(0,h.D6)(t,(0,h.TI)()))if((0,h.kl)(t))e.withCredentials=!0;else if(s.id){const r=s.id.findServerInfo(t);r&&r.webTierAuth&&(e.withCredentials=!0)}e.withCredentials&&(n.credentials="include",(0,m.jH)(t)&&await(0,m.jz)(d?(0,h.fl)(t,i):t))}let p,w,y=0,_=!1;l>0&&(y=setTimeout((()=>{_=!0,e.controller.abort()}),l));try{if("native-request-init"===r.responseType)w=n,w.url=t;else if("image"!==r.responseType||"default"!==n.cache||"GET"!==n.method||d||function(e){if(e)for(const t of Object.getOwnPropertyNames(e))if(e[t])return!0;return!1}(r.headers)||!i&&!e.useProxy&&x.proxyUrl&&!v(t)){if(p=await f._abortableFetch(t,n),e.useProxy||function(e){const t=(0,h.P$)(e);t&&!f._corsServers.includes(t)&&f._corsServers.push(t)}(t),"native"===r.responseType)w=p;else if("HEAD"!==n.method)if(p.ok){switch(a){case"array-buffer":w=await p.arrayBuffer();break;case"blob":case"image":w=await p.blob();break;default:w=await p.text()}if(y&&(clearTimeout(y),y=0),"json"===a||"xml"===a||"document"===a)if(w)switch(a){case"json":w=JSON.parse(w);break;case"xml":w=k(w,"application/xml");break;case"document":w=k(w,"text/html")}else w=null;if(w){if("array-buffer"===a||"blob"===a){const e=p.headers.get("Content-Type");if(e&&/application\/json|text\/plain/i.test(e)&&w["blob"===a?"size":"byteLength"]<=750)try{const e=await new Response(w).json();e.error&&(w=e)}catch{}}"image"===a&&w instanceof Blob&&(w=await B(URL.createObjectURL(w),e,!0))}}else w=await p.text()}else w=await B(t,e)}catch(n){if("AbortError"===n.name){if(_)throw new Error("Timeout exceeded");throw(0,c.zE)("Request canceled")}if(!(!p&&n instanceof TypeError&&x.proxyUrl)||r.body||"delete"===r.method||"head"===r.method||"post"===r.method||"put"===r.method||e.useProxy||v(t))throw n;e.redoRequest=!0,(0,h.tD)({proxyUrl:x.proxyUrl,urlPrefix:(0,h.P$)(t)??""})}finally{y&&clearTimeout(y)}return[p,w]}function k(e,t){let r;try{r=(new DOMParser).parseFromString(e,t)}catch{}if(!r||r.getElementsByTagName("parsererror").length)throw new SyntaxError("XML Parse error");return r}async function C(e,t,r){if(e.redoRequest)return e.redoRequest=!1,!1;const n=e.params.requestOptions;if(!t||"native"===n.responseType||"native-request-init"===n.responseType)return!0;let i,o;if(!t.ok)throw i=new Error(`Unable to load ${t.url} status: ${t.status}`),i[M]=r,i;r&&(r.error?i=r.error:"error"===r.status&&Array.isArray(r.messages)&&(i={...r},i[M]=r,i.details=r.messages));let a,l=null;i&&(o=Number(i.code),l=i.hasOwnProperty("subcode")?Number(i.subcode):null,a=i.messageCode,a=a&&a.toUpperCase());const c=n.authMode;if(403===o&&(4===l||i.message&&i.message.toLowerCase().includes("ssl")&&!i.message.toLowerCase().includes("permission"))){if(!e.useSSL)return e.useSSL=!0,!1}else if(!e.hasToken&&e.useIdentity&&("no-prompt"!==c||498===o)&&void 0!==o&&w.includes(o)&&!O(e.params.url)&&(403!==o||a&&!y.includes(a)&&(null==l||2===l&&e.credentialToken))){await T();try{const t=await s.id.getCredential(e.params.url,{error:q("request:server",i,e.params),prompt:"no-prompt"!==c,signal:e.controller.signal,token:e.credentialToken});return e.credential=t,e.credentialToken=t.token,e.useSSL=e.useSSL||t.ssl,!1}catch(t){if("no-prompt"===c)return e.credential=void 0,e.credentialToken=void 0,!1;i=t}}if(i)throw i;return!0}function B(e,t,r=!1){const n=t.controller.signal,s=new Image;return t.withCredentials?s.crossOrigin="use-credentials":s.crossOrigin="anonymous",s.alt="",s.fetchPriority=x.priority,s.src=e,(0,m.fY)(s,e,r,n)}f._abortableFetch=null,f._corsServers=["https://server.arcgisonline.com","https://services.arcgisonline.com"]},71058:(e,t,r)=>{r.d(t,{r:()=>i});var n=r(17452);const s=["elevation3d.arcgis.com","js.arcgis.com","jsdev.arcgis.com","jsqa.arcgis.com","static.arcgis.com"];function i(e){const t=(0,n.P$)(e,!0);return!!t&&t.endsWith(".arcgis.com")&&!s.includes(t)&&!e.endsWith("/sharing/rest/generateToken")}},85958:(e,t,r)=>{r.d(t,{Hu:()=>h,fY:()=>l,jH:()=>u,jz:()=>d});var n=r(68773),s=r(80442),i=r(70586),o=r(95330),a=r(17452);function l(e,t,r=!1,n){return new Promise(((a,l)=>{if((0,o.Hc)(n))return void l(c());let h=()=>{m(),l(new Error(`Unable to load ${t}`))},u=()=>{const t=e;m(),a(t)},d=()=>{if(!e)return;const t=e;m(),t.src="",l(c())};const m=()=>{(0,s.Z)("esri-image-decode")||(e.removeEventListener("error",h),e.removeEventListener("load",u)),h=null,u=null,e=null,(0,i.pC)(n)&&n.removeEventListener("abort",d),d=null,r&&URL.revokeObjectURL(t)};(0,i.pC)(n)&&n.addEventListener("abort",d),(0,s.Z)("esri-image-decode")?e.decode().then(u,h):(e.addEventListener("error",h),e.addEventListener("load",u))}))}function c(){try{return new DOMException("Aborted","AbortError")}catch{const e=new Error;return e.name="AbortError",e}}function h(e){n.Z.request.crossOriginNoCorsDomains||(n.Z.request.crossOriginNoCorsDomains={});const t=n.Z.request.crossOriginNoCorsDomains;for(let r of e)r=r.toLowerCase(),/^https?:\/\//.test(r)?t[(0,a.P$)(r)??""]=0:(t[(0,a.P$)("http://"+r)??""]=0,t[(0,a.P$)("https://"+r)??""]=0)}function u(e){const t=n.Z.request.crossOriginNoCorsDomains;if(t){let r=(0,a.P$)(e);if(r)return r=r.toLowerCase(),!(0,a.D6)(r,(0,a.TI)())&&t[r]<Date.now()-36e5}return!1}async function d(e){const t=n.Z.request.crossOriginNoCorsDomains,r=(0,a.P$)(e);t&&r&&(t[r.toLowerCase()]=Date.now());const s=(0,a.mN)(e);e=s.path,"json"===s.query?.f&&(e+="?f=json");try{await fetch(e,{mode:"no-cors",credentials:"include"})}catch{}}}}]);