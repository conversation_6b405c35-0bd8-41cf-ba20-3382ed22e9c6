package org.thingsboard.server.dao.model.sql.smartService.wechat;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.util.Date;

@Getter
@Setter
@ResponseEntity
@TableName("wx_message_record_item")
public class WxMessageRecordItem {
    // id
    private String id;

    // 所属信息记录
    private String messageRecordId;

    // 消息id
    private Long messageId;

    // 微信接收用户Id
    private String openId;

    // 发送时间
    private Date sendDate;

    // 客户id
    private String tenantId;

    public static WxMessageRecordItem rawData(String messageRecordId, String openId, String tenantId) {
        WxMessageRecordItem entity = new WxMessageRecordItem();
        entity.setMessageRecordId(messageRecordId);
        entity.setOpenId(openId);
        entity.setMessageId(0L);
        entity.setTenantId(tenantId);
        return entity;
    }

}
