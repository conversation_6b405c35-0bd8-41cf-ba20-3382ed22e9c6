package org.thingsboard.server.dao.model.sql.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 平台管理-Mobile搭建对象 base_build_mobile
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@ApiModel(value = "Mobile搭建", description = "平台管理-Mobile搭建实体类")
@Data
public class BaseBuildMobile {


    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 应用名称
     */
    @ApiModelProperty(value = "应用名称")
    private String name;

    /**
     * 系统图标地址
     */
    @ApiModelProperty(value = "系统图标地址")
    private String iconUrl;

    /**
     * 系统登录页面地址
     */
    @ApiModelProperty(value = "系统登录页面地址")
    private String loginUrl;

    /**
     * 系统皮肤地址
     */
    @ApiModelProperty(value = "系统皮肤地址")
    private String skinUrl;

    /**
     * 系统风格
     */
    @ApiModelProperty(value = "系统风格")
    private String style;

    /**
     * 是否云登录
     */
    @ApiModelProperty(value = "是否云登录")
    private String cloudLogin;
}
