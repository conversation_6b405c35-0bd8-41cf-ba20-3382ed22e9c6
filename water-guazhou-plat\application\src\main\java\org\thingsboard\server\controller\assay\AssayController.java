package org.thingsboard.server.controller.assay;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.fileupload.ISysFileService;
import org.thingsboard.server.dao.model.sql.assay.Assay;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;
import org.thingsboard.server.dao.waterSource.AssayService;

import java.util.List;
import java.util.Map;

/**
 * 水质化验记录管理
 */
@Slf4j
@RestController
@RequestMapping("/api/assay")
@Api(tags = "化验记录")
public class AssayController extends BaseController {

    @Autowired
    private AssayService assayService;
    @Autowired
    private ISysFileService fileService;

    @ApiOperation(value = "获取化验记录列表")
//    @PreAuthorize("hasAnyAuthority('TENANT_ADMIN', 'CUSTOMER_USER')")
    @GetMapping("list")
    public IstarResponse getList(@RequestParam Map<String, Object> params) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return IstarResponse.ok(assayService.getList(params, tenantId));
    }

    @ApiOperation(value = "保存化验记录")
//    @PreAuthorize("hasAuthority('TENANT_ADMIN')")
    @PostMapping
    public IstarResponse save(@RequestBody Assay assay) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        assay.setTenantId(tenantId);
        assay.setCreator(UUIDConverter.fromTimeUUID(getCurrentUser().getUuidId()));
        return IstarResponse.ok(assayService.save(assay));
    }

    @ApiOperation(value = "批量删除化验记录")
//    @PreAuthorize("hasAuthority('TENANT_ADMIN')")
    @DeleteMapping
    public IstarResponse delete(@RequestBody List<String> idList) throws ThingsboardException {
        assayService.delete(idList);
        return IstarResponse.ok();
    }

    @ApiOperation(value = "获取单个化验记录")
//    @PreAuthorize("hasAnyAuthority('TENANT_ADMIN', 'CUSTOMER_USER')")
    @GetMapping("/{id}")
    public IstarResponse getById(@PathVariable String id) throws ThingsboardException {
        return IstarResponse.ok(assayService.getById(id));
    }

    @ApiOperation(value = "更新")
//    @PreAuthorize("hasAuthority('TENANT_ADMIN')")
    @PostMapping("/update")
    public IstarResponse update(@RequestBody Assay assay) throws ThingsboardException {
        return IstarResponse.ok(assayService.uploadReport(assay));
    }

}
