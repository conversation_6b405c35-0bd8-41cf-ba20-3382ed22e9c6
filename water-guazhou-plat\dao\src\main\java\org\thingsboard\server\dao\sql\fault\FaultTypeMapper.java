package org.thingsboard.server.dao.sql.fault;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.fault.FaultType;

import java.util.List;

/**
 * userMybatis
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-11-08
 */
@Mapper
public interface FaultTypeMapper extends BaseMapper<FaultType> {

    List<FaultType> getList(@Param("deviceTypeSerialIds") List<String> deviceTypeSerialIds, @Param("name") String name, @Param("page") int page, @Param("size") int size, @Param("tenantId") String tenantId);

    int getListCount(@Param("deviceTypeSerialIds") List<String> deviceTypeSerialIds, @Param("name") String name, @Param("tenantId") String tenantId);
}
