<template>
  <div>
    <Form ref="refForm" :config="FormConfig"></Form>
  </div>
</template>
<script lang="ts" setup>
import {
  GetFieldConfig,
  GetFieldUniqueValue
} from '@/api/mapservice/fieldconfig';
import { SLMessage } from '@/utils/Message';
import { GexExtent } from '@/api/mapservice/pipe';
import {
  excuteQuery,
  extentTo,
  getLayer,
  initQueryParams,
  setMapCursor
} from '@/utils/MapHelper';
import { useGisStore } from '@/store';

const props = defineProps<{
  view?: __esri.MapView;
}>();
const state = reactive<{
  curOperate: 'uniqueing' | 'viewing' | '';
  pipeLayerOption: NormalOption[];
  curNode: any;
  thematicIndex: number;
  layerFields: any;
}>({
  curOperate: '',
  pipeLayerOption: [],
  curNode: undefined,
  thematicIndex: 0,
  layerFields: {}
});
const staticState: {
  thematicLayers: { id: string; layer: __esri.MapImageLayer }[];
} = {
  thematicLayers: []
};
const refForm = ref<IFormIns>();
const TableConfig_Thematic = reactive<ITable>({
  dataList: [],
  indexVisible: true,
  handleSelectChange: (rows) => {
    TableConfig_Thematic.selectList = rows;
    const ids = rows?.map((item) => item.id) || [];
    staticState.thematicLayers.map((item) => {
      item.layer.visible = ids.indexOf(item.id) !== -1;
    });
  },
  handleRowDbClick: (row) => {
    extentTo(props.view, row.extent);
  },
  columns: [{ label: '专题图名称', prop: 'name' }],
  pagination: {
    hide: true,
    refreshData: ({ page, size }) => {
      TableConfig_Thematic.pagination.page = page;
      TableConfig_Thematic.pagination.limit = size;
      refreshTable();
    },
    layout: 'total, sizes, jumper'
  }
});
const FormConfig = reactive<IFormConfig>({
  labelPosition: 'top',
  gutter: 12,
  group: [
    {
      fields: [
        {
          type: 'tabs',
          tabs: [
            { label: '专题图设置', value: 'setting' },
            { label: '专题图列表', value: 'list' }
          ],
          field: 'type'
        }
      ]
    },
    {
      handleHidden: (row, query, group) => {
        group.hidden = row.type !== 'setting';
      },
      fieldset: {
        desc: '图层名称：'
      },
      fields: [{ type: 'select', options: [], field: 'layer' }]
    },
    {
      handleHidden: (row, query, group) => {
        group.hidden = row.type !== 'setting';
      },
      fieldset: {
        desc: '专题图名称：'
      },
      fields: [{ type: 'input', field: 'name' }]
    },
    {
      handleHidden: (row, query, group) => {
        group.hidden = row.type !== 'setting';
      },
      fieldset: {
        desc: '图层字段'
      },
      fields: [
        {
          type: 'list',
          data: [],
          className: 'sql-list-wrapper',
          setData: async (config: IFormList, row) => {
            if (!row.layer) return;
            const fields = await getFields(row.layer);
            config.data = fields;
          },
          setDataBy: 'layer',
          displayField: 'alias',
          valueField: 'name',
          highlightCurrentRow: true,
          nodeClick: (node) => {
            state.curNode = node;
            appendSQL(node.name);
          }
        }
      ]
    },
    {
      handleHidden: (row, query, group) => {
        group.hidden = row.type !== 'setting';
      },
      id: 'field-construct',
      fieldset: {
        desc: '构建查询语句'
      },
      fields: [
        {
          type: 'btn-group',
          size: 'small',
          style: {
            width: '40%',
            display: 'flex',
            flexWrap: 'wrap'
          },
          className: 'sql-btns-wrapper',
          btns: [
            {
              perm: true,
              text: '=',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('=');
              }
            },
            {
              perm: true,
              text: '模糊',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL("like '%替换此处%'");
              }
            },
            {
              perm: true,
              text: '>',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('>');
              }
            },
            {
              perm: true,
              text: '<',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('<');
              }
            },
            {
              perm: true,
              text: '非',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('<>');
              }
            },
            {
              perm: true,
              text: '并且',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('and');
              }
            },
            {
              perm: true,
              text: '或者',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('or');
              }
            },
            {
              perm: true,
              text: '%',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('%');
              }
            }
          ],
          extraFormItem: [
            {
              type: 'list',
              wrapperStyle: {
                width: '60%',
                height: '144px'
              },
              className: 'sql-list-wrapper',
              field: 'uniqueValue',
              data: [],
              nodeClick: (node) => {
                appendSQL("'" + node + "'");
              },
              filters: [
                {
                  type: 'btn-group',
                  btns: [
                    {
                      perm: true,
                      text: () =>
                        state.curOperate === 'uniqueing'
                          ? '正在获取唯一值'
                          : '获取唯一值',
                      loading: () => state.curOperate === 'uniqueing',
                      disabled: () => state.curOperate === 'viewing',
                      styles: {
                        width: '100%',
                        borderRadius: '0'
                      },
                      click: () => getUniqueValue()
                    }
                  ]
                }
              ]
            }
          ]
        }
      ]
    },
    {
      handleHidden: (row, query, group) => {
        group.hidden = row.type !== 'setting';
      },
      fieldset: {
        desc: '组合查询条件'
      },
      fields: [
        {
          type: 'textarea',
          field: 'sql',
          placeholder: 'OBJECTID > 0'
        },
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: '清除',
              type: 'danger',
              disabled: () => state.curOperate === 'viewing',
              click: () => clear(),
              styles: {
                width: '100%'
              }
            },
            {
              perm: true,
              text: () =>
                state.curOperate === 'viewing' ? '正在处理中' : '显示',
              disabled: () => state.curOperate === 'viewing',
              loading: () => state.curOperate === 'viewing',
              click: () => startView(),
              styles: {
                width: '100%'
              }
            }
          ]
        }
      ]
    },
    {
      handleHidden: (row, query, group) => {
        group.hidden = row.type !== 'list';
      },
      fields: [
        {
          style: {
            height: '500px'
          },
          type: 'table',
          config: TableConfig_Thematic
        },
        {
          type: 'btn-group',
          itemContainerStyle: {
            marginTop: '15px'
          },
          btns: [
            {
              perm: true,
              type: 'danger',
              text: '删除选中专题图',
              styles: {
                width: '100%'
              },
              click: () => {
                const selectedIds = TableConfig_Thematic.selectList?.map(
                  (item) => item.id
                );
                const layers = staticState.thematicLayers
                  .filter((item) => {
                    return selectedIds?.indexOf(item.id) !== -1;
                  })
                  .map((item) => item.layer);
                props.view?.map.removeMany(layers);
                TableConfig_Thematic.dataList =
                  TableConfig_Thematic.dataList.filter((item) => {
                    return selectedIds?.indexOf(item.id) === -1;
                  });
                TableConfig_Thematic.selectList = [];
              }
            }
          ]
        }
      ]
    }
  ],
  defaultValue: {
    type: 'setting'
  }
});

const initBaseLayer = () => {
  if (!props.view) return;
  const pipeLayer: any = props.view?.map.findLayerById('pipelayer');
  state.pipeLayerOption = [];
  pipeLayer?.sublayers?.map((item) => {
    state.pipeLayerOption?.push({
      label: item.title,
      value: item.title,
      id: item.id
    });
  });
  const layerField = FormConfig.group[0].fields[0] as IFormSelect;
  layerField && (layerField.options = state.pipeLayerOption);
  refForm.value?.dataForm &&
    (refForm.value.dataForm.layer =
      state.pipeLayerOption && state.pipeLayerOption[0]?.value);
};
const getFields = async (layername): Promise<any[]> => {
  if (state.layerFields[layername]) return state.layerFields[layername];

  const res = await GetFieldConfig(layername);
  const result = res.data?.result?.rows || [];
  state.layerFields[layername] = result;
  return result;
};
const getUniqueValue = async () => {
  if (!state.curNode) return;
  state.curOperate = 'uniqueing';
  try {
    const layerid = state.pipeLayerOption.find(
      (item) => item.label === refForm.value?.dataForm.layer
    )?.id;
    const res = await GetFieldUniqueValue({
      layerid,
      field_name: state.curNode.name
    });
    const extraFormItem = FormConfig.group.find(
      (item) => item.id === 'field-construct'
    )?.fields[0].extraFormItem;
    const field = extraFormItem && (extraFormItem[0] as IFormList);
    field && (field.data = res.data.result.rows);
  } catch (error) {
    SLMessage.error('获取唯一值失败');
  }
  state.curOperate = '';
};
const appendSQL = (val) => {
  if (!refForm.value) return;
  if (!refForm.value?.dataForm) refForm.value.dataForm = {};
  const sql = refForm.value.dataForm.sql || ' ';
  refForm.value.dataForm.sql = sql + val + ' ';
};
const clear = () => {
  refForm.value?.dataForm && (refForm.value.dataForm.sql = '');
};
const startView = async () => {
  if (!props.view) return;
  const layer = refForm.value?.dataForm?.layer;
  if (!layer) {
    SLMessage.warning('请选择图层');
    return;
  }
  if (!refForm.value?.dataForm.name) {
    SLMessage.warning('请输入专题图名称');
    return;
  }
  const sql = refForm.value?.dataForm?.sql;
  if (!sql) {
    SLMessage.warning('请选择查询条件');
    return;
  }
  setMapCursor('loading');
  state.curOperate = 'viewing';
  try {
    const layerId = state.pipeLayerOption.find(
      (item) => item.label === layer
    )?.id;
    const url =
      window.SITE_CONFIG.GIS_CONFIG.gisService +
      window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService +
      '/' +
      layerId;
    // 确保专题图是可以查询的
    await excuteQuery(
      url,
      initQueryParams({
        where: sql,
        returnGeometry: true,
        outFields: state.layerFields[layer]?.map((item) => item.name) || ['*']
      })
    );
    const thematicId = 'Thematic_' + state.thematicIndex++;
    const pipeLayer = getLayer(props.view, {
      id: thematicId,
      title: refForm.value?.dataForm.name,
      type: 'MapImageLayer',
      url:
        window.SITE_CONFIG.GIS_CONFIG.gisService +
        window.SITE_CONFIG.GIS_CONFIG.gisPipeDynamicService
    }) as __esri.MapImageLayer;
    const extentres = await GexExtent({
      usertoken: useGisStore().gToken,
      layerid: layerId,
      where: sql,
      f: 'pjson'
    });
    const extent = extentres.data.result;
    pipeLayer.when(() => {
      extentTo(props.view, extent);
    });
    staticState.thematicLayers.push({ id: thematicId, layer: pipeLayer });
    const row = {
      id: thematicId,
      name: refForm.value.dataForm.name,
      sql: sql || '1=1',
      extent
    };
    TableConfig_Thematic.dataList.push(row);
    TableConfig_Thematic.selectList?.push(row);
  } catch (error) {
    SLMessage.error('显示失败，请联系管理员');
  }
  state.curOperate = '';
  setMapCursor('');
};
const refreshTable = () => {
  //
};
onMounted(() => {
  initBaseLayer();
});
onBeforeUnmount(() => {
  state.curOperate = '';
});
</script>
<style lang="scss" scoped></style>
<style>
.sql-btns-wrapper,
.sql-list-wrapper {
  box-shadow: 0 0 0 1px var(--el-border-color);
}
</style>
