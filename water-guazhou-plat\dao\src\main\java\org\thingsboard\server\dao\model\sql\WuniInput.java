package org.thingsboard.server.dao.model.sql;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = "tb_wuni")
@NoArgsConstructor
@AllArgsConstructor
public class WuniInput {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.TB_MEDICINE_MANAGE_STATION_ID)
    private String stationId;

    private BigDecimal num;

    private BigDecimal numRate;

    private BigDecimal pam;

    private Date time;

    @Transient
    private String month;

    @Transient
    private String timeStr;

    private String tenantId;


}
