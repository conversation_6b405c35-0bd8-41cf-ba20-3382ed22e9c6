import{d as v,M as S,c as u,s as x,r as d,x as m,a8 as _,bQ as E,S as I,ar as V,a9 as w,bT as M,D as T,o as N,g as R,n as W,q as f,i as g,b6 as F,b7 as Y}from"./index-r0dFAfgr.js";import{_ as q}from"./CardTable-rdWOL4_6.js";import{_ as A}from"./CardSearch-CB_HNR-Q.js";import B from"./teamTable-52nD9foE.js";import{I as o}from"./common-CvK_P_ao.js";import{f as H}from"./DateFormatter-Bm9a68Ax.js";import{p as O,d as P,g as Q}from"./equipmentInspection-Ci5RL-WV.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";const $={class:"wrapper"},re=v({__name:"index",setup(j){const{$btnPerms:c}=S(),b=u(),p=u(),h=u(),C=u({filters:[{label:"班组名称",field:"name",type:"input"}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",icon:o.QUERY,click:()=>i()},{type:"default",perm:!0,text:"重置",svgIcon:x(Y),click:()=>{var e;(e=b.value)==null||e.resetForm(),i()}},{perm:c("RoleManageAdd"),type:"success",text:"新增",icon:o.ADD,click:()=>U()}]}]}),l=d({defaultExpandAll:!1,indexVisible:!0,expandable:!0,expandComponent:x(B),columns:[{label:"班组名称",prop:"name"},{label:"备注",prop:"remark"},{label:"创建人",prop:"creatorName"},{label:"创建时间",prop:"createTime",formatter:e=>H(e.createTime,"YYYY-MM-DD HH:mm")}],operations:[{type:"primary",text:"编辑",perm:c("RoleManageEdit"),icon:o.EDIT,click:e=>k(e)},{type:"danger",text:"删除",icon:o.DELETE,perm:c("RoleManageDelete"),click:e=>L(e)}],dataList:[],pagination:{page:1,limit:20,total:0,refreshData:({page:e,size:t})=>{l.pagination.page=e,l.pagination.limit=t,i()}}}),n=d({title:"新增",labelWidth:"130px",submit:e=>{var r;let t="添加成功";if(e.id&&(t="修改成功"),!e.maintainCircuitTeamCList||((r=e.maintainCircuitTeamCList)==null?void 0:r.length)===0){m.warning("请添加班组人员");return}e.type="保养班组",O(e).then(()=>{var s;m.success(t),i(),(s=p.value)==null||s.closeDrawer()})},defaultValue:{},group:[{fields:[{xl:12,type:"input",label:"班组名称",field:"name",rules:[{required:!0,message:"请输入班组名称"}]},{xl:18,type:"textarea",label:"备注/说明",field:"remark"},{type:"table",field:"maintainCircuitTeamCList",config:{indexVisible:!0,titleRight:[{style:{justifyContent:"flex-end"},items:[{type:"btn-group",btns:[{text:"添加班组成员",perm:!0,click:()=>{var e;(e=h.value)==null||e.openDrawer()}}]}]}],height:"350px",dataList:_(()=>a.selectUser),columns:[{label:"成员账户",prop:"email"},{label:"成员名称",prop:"userName"}],operations:[{text:"移除",type:"danger",icon:o.DELETE,perm:c("RoleManageDelete"),click:e=>{a.selectUser=a.selectUser.filter(t=>t.userId!==e.userId)}}],pagination:{hide:!0}}}]}]}),D=d({title:"添加成员",labelWidth:"100px",width:500,submit:e=>{e.userId===void 0&&m.warning("请选择成员"),a.UserList.forEach(t=>{var r;t.value===e.userId&&(e.userName=t.userName,a.selectUser.push(t),(r=h.value)==null||r.closeDrawer())}),a.selectUser=E(a.selectUser,"userId")},defaultValue:{},group:[{fields:[{type:"select-tree",label:"执行部门",field:"key1",defaultExpandAll:!0,checkStrictly:!0,options:_(()=>a.WaterSupplyTree),onChange:e=>{a.getUserListValue(e)}},{type:"select",label:"执行人员",field:"userId",options:_(()=>a.UserList)}]}]}),U=()=>{var e;n.title="新增班组",a.selectUser=[],n.defaultValue={},(e=p.value)==null||e.openDrawer()},k=e=>{var t;n.title="修改班组",a.selectUser=e.maintainCircuitTeamCList,n.defaultValue={...e||{}},(t=p.value)==null||t.openDrawer()},L=e=>{I("确定删除该班组, 是否继续?","删除提示").then(()=>{P([e.id]).then(()=>{m.success("删除成功"),i()})})},a=d({selectUser:[],WaterSupplyTree:[],UserList:[],getWaterSupplyTreeValue:()=>{V(2).then(t=>{a.WaterSupplyTree=w(t.data.data||[])})},getUserListValue:e=>{M({pid:e}).then(t=>{const r=t.data.data.data||[];a.UserList=r.map(s=>({label:s.firstName,value:T(s.id.id),userName:s.firstName,email:s.email,userId:T(s.id.id)}))})}}),i=async()=>{var t;const e={size:l.pagination.limit,page:l.pagination.page,type:"保养班组",name:"",...((t=b.value)==null?void 0:t.queryParams)||{}};Q(e).then(r=>{l.dataList=r.data.data.data||[],l.pagination.total=r.data.data||0})};return N(()=>{i(),a.getWaterSupplyTreeValue()}),(e,t)=>{const r=A,s=q,y=F;return R(),W("div",$,[f(r,{ref_key:"refSearch",ref:b,config:g(C)},null,8,["config"]),f(s,{config:g(l),class:"card-table"},null,8,["config"]),f(y,{ref_key:"refForm",ref:p,config:g(n)},null,8,["config"]),f(y,{ref_key:"refForm1",ref:h,config:g(D)},null,8,["config"])])}}});export{re as default};
