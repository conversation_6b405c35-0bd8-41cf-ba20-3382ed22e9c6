var u,d=new Uint8Array(16);function i(){if(!u&&(u=typeof crypto<"u"&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)||typeof msCrypto<"u"&&typeof msCrypto.getRandomValues=="function"&&msCrypto.getRandomValues.bind(msCrypto),!u))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return u(d)}const r=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i;function a(t){return typeof t=="string"&&r.test(t)}var n=[];for(var s=0;s<256;++s)n.push((s+256).toString(16).substr(1));function g(t){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,f=(n[t[e+0]]+n[t[e+1]]+n[t[e+2]]+n[t[e+3]]+"-"+n[t[e+4]]+n[t[e+5]]+"-"+n[t[e+6]]+n[t[e+7]]+"-"+n[t[e+8]]+n[t[e+9]]+"-"+n[t[e+10]]+n[t[e+11]]+n[t[e+12]]+n[t[e+13]]+n[t[e+14]]+n[t[e+15]]).toLowerCase();if(!a(f))throw TypeError("Stringified UUID is invalid");return f}function p(t,e,f){t=t||{};var o=t.random||(t.rng||i)();return o[6]=o[6]&15|64,o[8]=o[8]&63|128,g(o)}export{p as v};
