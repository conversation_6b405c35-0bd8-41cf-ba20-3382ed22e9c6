<template>
  <SLCard class="card-search-box">
    <Search
      ref="refSearch"
      :config="config"
    ></Search>
  </SLCard>
</template>
<script lang="ts" setup>
import { computed, ref } from 'vue'
import Search from './Search.vue'

defineProps<{ config: ISearch }>()
const refSearch = ref<InstanceType<typeof Search>>()
const toggleMore = () => {
  refSearch.value?.toggleMore()
}
const queryParams = computed(() => refSearch.value?.queryParams)
const resetForm = () => {
  refSearch.value?.resetForm()
}
defineExpose({
  queryParams,
  toggleMore,
  resetForm
})
</script>
<style lang="scss" scoped>
.card-search-box{
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  padding: 12px 0;
  background-color: #fff;
}
.dark{
  .card-search-box{
    background-color: var(--el-bg-color);
  }
}
</style>
