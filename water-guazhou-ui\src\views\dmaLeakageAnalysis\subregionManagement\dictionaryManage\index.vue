<template>
  <!-- 字典管理 -->
  <TreeBox>
    <template #tree>
      <SLTree :tree-data="TreeData" />
    </template>
    <CardSearch ref="cardSearch" :config="SearchConfig" />
    <CardTable :config="TableConfig" class="card-table" />
    <DialogForm ref="refForm" :config="FormConfig"></DialogForm>
  </TreeBox>
</template>

<script lang="ts" setup>
import { Delete, Edit, Plus } from '@element-plus/icons-vue'
import { SLConfirm, SLMessage } from '@/utils/Message'
import { formatDate } from '@/utils/DateFormatter'
import {
  getDictList,
  saveDict,
  delDict
} from '@/api/dma/dict'

const refForm = ref<IDialogFormIns>()
const SearchConfig = reactive<ISearch>({
  filters: [
    // { label: '标题', field: 'title', type: 'input' },
    // { label: '内容', field: 'value', type: 'input' },
    // { label: '添加时间', field: 'time', type: 'datetimerange' },
    {
      type: 'btn-group',
      btns: [
        {
          text: '新增',
          perm: true,
          svgIcon: shallowRef(Plus),
          click: () => {
            FormConfig.defaultValue = { type: TreeData.currentProject.value }
            FormConfig.title = '新增'
            refForm.value?.openDialog()
          }
        }
      ]
    }
  ]
})

const TableConfig = reactive<ITable>({
  loading: false,
  dataList: [],
  indexVisible: true,
  columns: [
    {
      label: '代码类型编号',
      prop: 'code'
    },
    {
      label: '代码类型',
      prop: 'type',
      formatter: (row: any) => {
        return row.type === '1' ? '成本类型' : ''
      }
    },
    {
      label: '字典名称',
      prop: 'label'
    },
    {
      label: '字典值',
      prop: 'value'
    },
    {
      label: '排序',
      prop: 'orderNum'
    },
    {
      label: '创建时间',
      prop: 'createTime',
      formatter: row => formatDate(row.createTime, 'YYYY-MM-DD HH:mm:ss')
    }
  ],
  operations: [
    {
      text: '编辑',
      isTextBtn: true,
      perm: true,
      svgIcon: shallowRef(Edit),
      click: row => {
        FormConfig.title = '编辑'
        FormConfig.defaultValue = {
          ...row
        }
        refForm.value?.openDialog()
      }
    },
    {
      perm: true,
      text: '删除',
      isTextBtn: true,
      type: 'danger',
      svgIcon: shallowRef(Delete),
      click: row => handleDelete(row)
    }
  ],
  operationWidth: '200px',
  pagination: {
    refreshData: ({ page, size }) => {
      TableConfig.pagination.limit = size
      TableConfig.pagination.page = page
      refreshData()
    }
  }
})
const FormConfig = reactive<IDialogFormConfig>({
  dialogWidth: 500,
  title: '新增字典',
  defaultValue: {},
  submit: params => {
    console.log(params)
    SLConfirm('确定提交？', '提示信息').then(async () => {
      FormConfig.submitting = true
      try {
        saveDict(params).then((res: any) => {
          if (res.data.code === 200) {
            SLMessage.success('提交成功')
          }
        })
        await refreshData()
        refForm.value?.closeDialog()
      } catch (error) {
        SLMessage.error('提交失败')
        console.dir(error)
      }
      FormConfig.submitting = false
    })
  },
  group: [
    {
      fields: [
        {
          type: 'input',
          label: '代码分类',
          field: 'code',
          rules: [{ required: true, message: '请填写代码分类' }]
        }, {
          type: 'input',
          label: '字典名称',
          field: 'label',
          rules: [{ required: true, message: '请填写字典名称' }]
        }, {
          type: 'input',
          label: '字典值',
          field: 'value',
          rules: [{ required: true, message: '请填写字典值' }]
        },
        {
          type: 'number',
          label: '排序',
          field: 'orderNum'
        }
      ]
    }
  ]
})
const TreeData = reactive<SLTreeConfig>({
  title: '字典管理',
  currentProject: {},
  data: [
    {
      value: 1,
      label: '成本类型'
    }
  ],
  isFilterTree: true,
  treeNodeHandleClick: data => {
    // 设置当前选中项目信息
    TreeData.currentProject = data
    TableConfig.pagination.page = 1
    refreshData()
  }
})

const refreshData = async () => {
  const query = {
    page: TableConfig.pagination.page || 1,
    size: TableConfig.pagination.limit || 20,
    type: TreeData.currentProject.value
  }
  getDictList(query).then((res: any) => {
    if (res.data.code === 200) {
      console.log(res.data.data)
      const { data } = res.data
      TableConfig.dataList = data.data
      TableConfig.pagination.total = data.total
    }
  })
}

// 删除字典
const handleDelete = (row?: any) => {
  SLConfirm('确定删除?', '删除提示').then(() => {
    delDict([row.id]).then((res: any) => {
      if (res.data.code === 200) {
        SLMessage.success('删除成功')
        refreshData()
      }
    })
  })
}

onMounted(async () => {
  //
})
</script>
<style lang="scss" scoped>
.card-table {
  height: calc(100% - 100px);
}
</style>
