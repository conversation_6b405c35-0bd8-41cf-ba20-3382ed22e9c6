import{d as B,cN as M,r as q,c as A,o as F,ay as O,g as d,n as p,bo as y,i as r,q as v,p as m,F as b,aB as G,aJ as T,h as E,G as P,bh as g,bt as j,dz as U,dA as W,br as $,ab as J,C as H}from"./index-r0dFAfgr.js";import{h as K}from"./chart-wy3NEK2T.js";import{f as Q,d as X}from"./zhandian-YaGuQZe6.js";import{g as I}from"./echarts-Bhn8T7lM.js";import{u as Y}from"./useDetector-BRcb7GRN.js";import{a as Z}from"./headwaterMonitoring-BgK7jThW.js";import{c as tt}from"./useStation-DJgnSZIA.js";const et={class:"one-map-detail"},at={class:"row1"},ot={class:"pie-charts"},st={class:"row2"},it={class:"detail-attrgrou-radio"},nt={class:"detail-right"},rt={class:"list-items overlay-y"},lt={class:"item-label"},ct={class:"item-content"},dt={class:"chart-box"},ut=B({__name:"WaterPoolMonitoringDetail",emits:["refresh","mounted"],setup(pt,{expose:V,emit:w}){const D=w,{proxy:C}=M(),t=q({curRadio:"",radioGroup:[],pieChart1:I(0,{max:5,title:"液位(m)"}),lineChartOption:null,stationRealTimeData:[],detailLoading:!1}),N=a=>{const o=J(a);return{value:+o.value.toFixed(2),unit:o.unit}},k=tt(),z=async a=>{var o;D("refresh",{title:a.name}),t.detailLoading=!0;try{if(a.fromAllStation){const i=await k.getLatestData({type:"水池监测站"});t.curRow=i.find(u=>u.stationId===a.stationId)}else t.curRow=a;const s=Z({deviceId:(o=t.curRow)==null?void 0:o.level_deviceId,attr:"level"}).then(i=>{var e,n,f;const l=((e=i.data)==null?void 0:e.data).todayDataList.map(h=>h.value);t.lineChartOption=K({line1:{data:l,unit:((n=t.curRow)==null?void 0:n.level_unit)??"m",name:"液位"}}),(f=C.$refs.refChart4)==null||f.resize()}).finally(()=>{L()}),_=Q({stationId:a==null?void 0:a.stationId}).then(i=>{t.radioGroup=i.data||[],t.curRadio=t.radioGroup[0],R(t.radioGroup[0])});Promise.all([s,_]).finally(()=>{t.detailLoading=!1})}catch{t.detailLoading=!1}},R=async a=>{var l,e,n,f,h;try{const c=await X((l=t.curRow)==null?void 0:l.stationId,a);t.stationRealTimeData=c.data||[]}catch{}const o=(e=t.stationRealTimeData.find(c=>c.property==="level"))==null?void 0:e.maxValue,s=(n=t.stationRealTimeData.find(c=>c.property==="ywgbj"))==null?void 0:n.value,i=((f=t.stationRealTimeData.find(c=>c.property==="cgqlc"))==null?void 0:f.value)??s??o??5,u=N(((h=t.curRow)==null?void 0:h.level)||0);t.pieChart1=I(u.value,{max:i,title:"液位("+(u.unit||"")+"m)"})};V({refreshDetail:z});const L=()=>{Array.from({length:2}).map((a,o)=>{var s;(s=C.$refs["refChart"+(o+1)])==null||s.resize()})},S=Y(),x=A();return F(()=>{D("mounted"),S.listenToMush(x.value,L)}),(a,o)=>{const s=j,_=O("VChart"),i=U,u=W,l=$;return d(),p("div",et,[y((d(),p("div",at,[v(s,{size:"default",title:"高位水池监测",type:"simple",class:"row-title"}),m("div",ot,[m("div",{ref_key:"refChartDiv",ref:x,class:"pie-chart"},[v(_,{ref:"refChart1",option:r(t).pieChart1},null,8,["option"])],512)])])),[[l,r(t).detailLoading]]),m("div",st,[m("div",it,[v(u,{modelValue:r(t).curRadio,"onUpdate:modelValue":o[0]||(o[0]=e=>r(t).curRadio=e),onChange:R},{default:b(()=>[(d(!0),p(G,null,T(r(t).radioGroup,(e,n)=>(d(),E(i,{key:n,label:e},{default:b(()=>[P(g(e),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),m("div",nt,[y((d(),p("div",rt,[(d(!0),p(G,null,T(r(t).stationRealTimeData,(e,n)=>(d(),p("div",{key:n,class:"list-item"},[m("div",lt,g(e.propertyName),1),m("div",ct,g(e.value||"--")+" "+g(e.unit),1)]))),128))])),[[l,r(t).detailLoading]]),y((d(),p("div",dt,[v(_,{ref:"refChart4",option:r(t).lineChartOption},null,8,["option"])])),[[l,r(t).detailLoading]])])])])}}}),Dt=H(ut,[["__scopeId","data-v-a2fea57a"]]);export{Dt as default};
