<!-- 片区管理 -->
<template>
  <RightDrawerMap
    ref="refMap"
    title="片区管理"
    :right-drawer-width="600"
    @map-loaded="onMaploaded"
  >
    <div class="form-wrapper">
      <Form
        ref="refForm"
        :config="FormConfig"
      ></Form>
      <div class="table-box">
        <FormTable :config="TableConfig"></FormTable>
      </div>
    </div>

    <Panel
      v-if="state.isMounted"
      ref="refPanelArea"
      :title="state.curAreaOp"
      :telport="'#arcmap-wrapper'"
      :draggable="true"
      class="area-panel"
    >
      <Form
        ref="refFormArea"
        :config="FormConfigArea"
      ></Form>
    </Panel>
    <Panel
      v-if="state.isMounted"
      ref="refPanelDistrict"
      :title="state.curDistrictOp"
      :telport="'#arcmap-wrapper'"
      :draggable="true"
      :class="'district-panel'"
    >
      <Form
        ref="refFormDistrict"
        :config="FormConfigDistrict"
      ></Form>
    </Panel>
    <Panel
      v-if="state.isMounted"
      ref="refPanelKeyPoint"
      :title="'添加关键点'"
      :telport="'#arcmap-wrapper'"
      :draggable="true"
      :class="'keypoint-panel'"
    >
      <Form
        ref="refFormKeyPoint"
        :config="FormConfigKeyPoint"
      ></Form>
    </Panel>
  </RightDrawerMap>
</template>
<script lang="ts" setup>
import { Delete, Edit, Plus } from '@element-plus/icons-vue'
import Graphic from '@arcgis/core/Graphic.js'
import Point from '@arcgis/core/geometry/Point.js'
import { queryLayerClassName } from '@/api/mapservice'
import { IFormIns, IPanelIns } from '@/components/type'
import {
  createGeometry,
  getGraphicLayer,
  getSubLayerIds,
  gotoAndHighLight,
  initBufferParams,
  queryBufferPolygon,
  setSymbol
} from '@/utils/MapHelper'
import RightDrawerMap from '../../components/common/RightDrawerMap.vue'
import AreaTreeNode from './components/AreaTreeNode.vue'
import {
  AddArea,
  AddDistrict,
  AddKeyPoint,
  DeleteArea,
  DeleteDistrict,
  DeleteKeyPoint,
  GetAreaTree,
  GetDistrictPointsJson,
  GetKeyPoint
} from '@/api/patrol'
import { formatTree } from '@/utils/GlobalHelper'
import { SLConfirm, SLMessage } from '@/utils/Message'
import { useSketch } from '@/hooks/arcgis'

const refForm = ref<IFormIns>()
const refMap = ref<InstanceType<typeof RightDrawerMap>>()
const refFormArea = ref<IFormIns>()
const refPanelArea = ref<IPanelIns>()
const refFormDistrict = ref<IFormIns>()
const refPanelDistrict = ref<IPanelIns>()
const refFormKeyPoint = ref<IFormIns>()
const refPanelKeyPoint = ref<IPanelIns>()

const staticState: {
  view?: __esri.MapView
  graphicsLayer?: __esri.GraphicsLayer
  keypointLayer?: __esri.GraphicsLayer
  bufferLayer?: __esri.GraphicsLayer
  sketch?: __esri.SketchViewModel
  graphic?: __esri.Graphic
  bufferGeometry?: __esri.Geometry
  keyPoint?: __esri.Point
} = {}
const state = reactive<{
  tabs: any[]
  loading: boolean
  layerIds: number[]
  layerInfos: any[]
  isMounted: boolean
  curArea?: NormalOption
  curDistrict?: NormalOption
  curAreaOp?: string
  curDistrictOp?: string
}>({
  tabs: [],
  loading: false,
  layerIds: [],
  layerInfos: [],
  isMounted: false
})
const TableConfig = reactive<ITable>({
  dataList: [],
  columns: [
    { minWidth: 120, label: '关键点名称', prop: 'name' },
    { minWidth: 160, label: '创建时间', prop: 'createTime' },
    { minWidth: 120, label: '备注', prop: 'remark' }
  ],
  operations: [
    {
      perm: true,
      text: '删除',
      type: 'danger',
      svgIcon: shallowRef(Delete),
      click: row => handleDeleteKeyPoint(row)
    }
  ],
  handleRowClick: row => handleLocateKeyPoint(row),
  pagination: {
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page || 1
      TableConfig.pagination.limit = size || 20
      refreshKeyPoint()
    }
  }
})
const handleLocateKeyPoint = row => {
  const g = new Point({
    latitude: row.lat,
    longitude: row.lon,
    spatialReference: staticState.view?.spatialReference
  })
  gotoAndHighLight(staticState.view, new Graphic({ geometry: g }))
}
const handleDeleteKeyPoint = (row?: any) => {
  if (!row) return
  SLConfirm('确定删除?', '提示信息')
    .then(() => {
      DeleteKeyPoint(row?.id)
        .then(res => {
          if (res.data.code === 200) {
            SLMessage.success(res.data.message)
            refreshKeyPoint()
          } else {
            SLMessage.error(res.data.message)
          }
        })
        .catch(() => {
          SLMessage.error('系统错误')
        })
    })
    .catch(() => {
      //
    })
}
const FormConfig = reactive<IFormConfig>({
  group: [
    {
      fields: [
        {
          type: 'input',
          field: 'keyword',
          placeholder: '输入关键字筛选'
          // appendBtns: [
          //   {
          //     perm: true,
          //     isTextBtn: true,
          //     svgIcon: shallowRef(Refresh),
          //     click: () => refreshData()
          //   }
          // ]
        },
        {
          type: 'tree',
          field: 'area',
          style: {
            height: '200px'
          },
          filterBy: 'keyword',
          nodeClick: async data => {
            staticState.graphicsLayer?.removeAll()
            staticState.bufferLayer?.removeAll()
            staticState.keypointLayer?.removeAll()
            if (data.data.layer !== 2) return
            state.curDistrict = data
            // 获取区域图形坐标
            GetDistrictPointsJson(data.value).then(res => {
              const type = data.data.type === '区域' ? 'polygon' : 'polyline'
              if (!res.data.data) return
              staticState.bufferLayer?.removeAll()
              staticState.graphicsLayer?.removeAll()
              const pointjson = JSON.parse(res.data.data)
              if (pointjson.bufferGeometry) {
                staticState.bufferGeometry = createGeometry(
                  'polygon',
                  pointjson.bufferGeometry.rings,
                  pointjson.bufferGeometry.spatialReference
                )
                const bufferGraphic = new Graphic({
                  geometry: staticState.bufferGeometry,
                  symbol: setSymbol(
                    staticState.bufferGeometry?.type || 'polygon',
                    {
                      color: [0, 255, 0, 0.1],
                      outlineWidth: 1,
                      outlineColor: '#00ff00'
                    }
                  )
                }) as __esri.Graphic
                staticState.bufferLayer?.add(bufferGraphic)
              }
              if (pointjson.geometry) {
                const geo = createGeometry(
                  type,
                  type === 'polygon'
                    ? pointjson.geometry.rings
                    : pointjson.geometry.paths,
                  pointjson.geometry.spatialReference
                )
                staticState.graphic = new Graphic({
                  geometry: geo,
                  symbol: setSymbol(type)
                }) as __esri.Graphic
                staticState.graphicsLayer?.add(staticState.graphic)
                gotoAndHighLight(staticState.view, staticState.graphic, {
                  avoidHighlight: true
                })
              }
            })
            // 获取关键点列表
            refreshKeyPoint()
          },
          customNode: shallowRef(AreaTreeNode),
          expandOnClickNode: false,
          customProps: {
            iconBtns: [
              {
                isTextBtn: true,
                perm: (data: NormalOption): boolean => data.data.layer !== 2,
                svgIcon: shallowRef(Plus),
                click: data => handleAdd(data)
              },
              {
                isTextBtn: true,
                perm: (data: NormalOption): boolean => data.data.parentId !== null,
                svgIcon: shallowRef(Edit),
                click: data => handleEdit(data)
              },
              {
                isTextBtn: true,
                type: 'danger',
                perm: (data: NormalOption): boolean => data.data.parentId !== null,
                svgIcon: shallowRef(Delete),
                click: data => handleDelete(data)
              }
            ],
            textBtns: [
              {
                isTextBtn: true,
                perm: (data: NormalOption): boolean => {
                  console.log(data)
                  if (data.path?.length === 2) return false
                  return data.data.layer === 1 && data.data.parentId !== null
                },
                text: '添加区域/路线',
                click: data => handleAddDistrict(data)
              },
              {
                isTextBtn: true,
                perm: (data: NormalOption): boolean => data.data.layer === 2 && data.data.parentId !== null,
                text: '添加关键点',
                click: data => handleAddKeyPoint(data)
              }
            ]
          },
          options: []
        }
      ]
    },
    {
      fieldset: {
        desc: '片区信息'
      },
      fields: []
    }
  ],
  labelPosition: 'top',
  gutter: 12
})
const handleAddKeyPoint = async (data: NormalOption) => {
  state.curDistrict = data
  closePanels()
  FormConfigKeyPoint.defaultValue = {
    areaId: state.curDistrict?.value
  }
  refPanelKeyPoint.value?.Open()
  await nextTick()
  refFormKeyPoint.value?.resetForm()
}
const handleAdd = async (data: NormalOption) => {
  state.curArea = data
  state.curAreaOp = '添加片区'
  FormConfigArea.defaultValue = {
    parentId: data.value
  }
  closePanels()
  refPanelArea.value?.Open()
  await nextTick()
  refFormArea.value?.resetForm()
}
const handleAddDistrict = async (data: NormalOption) => {
  state.curArea = data
  state.curDistrictOp = '添加区域/路线'
  FormConfigDistrict.defaultValue = {
    districtId: data.value,
    type: '区域',
    buffer: data.data.buffer
  }
  closePanels()
  refPanelDistrict.value?.Open()
  await nextTick()
  refFormDistrict.value?.resetForm()
}
const closePanels = () => {
  refPanelArea.value?.Close()
  refPanelKeyPoint.value?.Close()
  refPanelDistrict.value?.Close()
}
const handleEdit = async (data: NormalOption) => {
  closePanels()
  if (data.data.layer === 1) {
    state.curAreaOp = '编辑片区'
    refPanelArea.value?.Open()
    await nextTick()
    refFormArea.value && (refFormArea.value.dataForm = data.data)
  } else if (data.data.layer === 2) {
    state.curDistrictOp = '编辑区域'
    refPanelDistrict.value?.Open()
    await nextTick()
    refFormDistrict.value && (refFormDistrict.value.dataForm = data.data)
  }
}
const handleDelete = (data: NormalOption) => {
  SLConfirm('确定删除？', '提示信息')
    .then(async () => {
      try {
        const res = data.data.layer === 1
          ? await DeleteArea(data.value)
          : await DeleteDistrict(data.value)
        if (res.data.code === 200) {
          SLMessage.success(res.data.message)
          refreshData()
        } else {
          SLMessage.error(res.data.message)
        }
      } catch (error) {
        SLMessage.error('操作失败')
      }
    })
    .catch(() => {
      //
    })
}
const FormConfigArea = reactive<IFormConfig>({
  gutter: 0,
  group: [
    {
      fields: [
        {
          type: 'input',
          label: '片区名称',
          field: 'name',
          rules: [{ required: true, message: '请输入片区名称' }]
        },

        { type: 'textarea', label: '备注', field: 'remark' },
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: '取消',
              type: 'default',
              styles: {
                marginLeft: 'auto'
              },
              click: () => refPanelArea.value?.Close()
            },
            {
              perm: true,
              text: '确定',
              click: () => refFormArea.value?.Submit()
            }
          ]
        }
      ]
    }
  ],
  labelPosition: 'right',
  submit: async params => {
    try {
      const res = await AddArea(params)
      if (res.data.code === 200) {
        SLMessage.success(res.data.message)
        refreshData()
        refPanelArea.value?.Close()
      } else {
        SLMessage.error(res.data.message)
      }
    } catch (error) {
      SLMessage.error('系统错误')
    }
  }
})
const FormConfigDistrict = reactive<IDialogFormConfig>({
  title: '添加区域/路线',
  gutter: 0,
  draggable: true,
  dialogWidth: 300,
  model: false,
  labelPosition: 'top',
  group: [
    {
      fields: [
        {
          type: 'radio-button',
          field: 'type',
          label: '类型',
          options: [
            { label: '区域', value: '区域' },
            { label: '路线', value: '路线' }
          ],
          rules: [{ required: true, message: '请选择类型' }]
        },
        {
          type: 'input',
          label: '名称',
          field: 'name',
          rules: [{ required: true, message: '请输入名称' }]
        },
        {
          handleHidden: (params, query, config) => {
            config.hidden = params.type !== '路线'
          },
          type: 'input-number',
          label: '缓冲距离',
          field: 'buffer',
          append: '米'
        },
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: '绘制',
              styles: {
                width: '100%'
              },
              type: 'success',
              click: () => startDraw()
            }
          ]
        },
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: '取消',
              type: 'default',
              styles: {
                width: '100%'
              },
              click: () => refPanelDistrict.value?.Close()
            },
            {
              perm: true,
              text: '确定',
              styles: {
                width: '100%'
              },
              click: () => refFormDistrict.value?.Submit()
            }
          ]
        }
      ]
    }
  ],
  submit: async params => {
    try {
      if (!staticState.graphic) {
        SLMessage.warning('请先绘制图形')
        return
      }
      const points = {
        geometry: staticState.graphic?.geometry?.toJSON(),
        bufferGeometry: staticState.bufferGeometry?.toJSON()
      }
      const submitParams = {
        ...params,
        points: JSON.stringify(points)
      }
      const res = await AddDistrict(submitParams)
      if (res.data.code === 200) {
        SLMessage.success(res.data.message)
        refreshData()
        refPanelDistrict.value?.Close()
      } else {
        SLMessage.error(res.data.message)
      }
    } catch (error) {
      SLMessage.error('系统错误')
    }
  },
  defaultValue: {
    type: '区域',
    buffer: 30
  }
})
const startDraw = () => {
  staticState.graphicsLayer?.removeAll()
  staticState.bufferLayer?.removeAll()
  staticState.graphic = undefined
  staticState.bufferGeometry = undefined
  const type = refFormDistrict.value?.dataForm.type
  staticState.graphicsLayer
    && staticState.sketch
    && (staticState.sketch.layer = staticState.graphicsLayer)
  staticState.sketch?.create(type === '路线' ? 'polyline' : 'polygon')
}
const FormConfigKeyPoint = reactive<IFormConfig>({
  group: [
    {
      fields: [
        {
          type: 'input',
          label: '名称',
          field: 'name',
          rules: [{ required: true, message: '请输入名称' }]
        },
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: '绘制',
              styles: {
                width: '100%'
              },
              type: 'success',
              click: () => startDrawKeyPoint()
            }
          ]
        },
        {
          type: 'textarea',
          label: '备注',
          field: 'remark'
        },
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: '取消',
              type: 'default',
              styles: { width: '100%' },
              click: () => refPanelKeyPoint.value?.Close()
            },
            {
              perm: true,
              text: '确定',
              styles: { width: '100%' },
              click: () => refFormKeyPoint.value?.Submit()
            }
          ]
        }
      ]
    }
  ],
  labelPosition: 'top',
  gutter: 0,
  defaultValue: {},
  submit: params => {
    if (!staticState.keyPoint) {
      SLMessage.warning('请先绘制关键点')
      return
    }
    const submitParams = {
      ...params,
      lon: staticState.keyPoint.longitude,
      lat: staticState.keyPoint.latitude
    }
    AddKeyPoint(submitParams)
      .then(res => {
        if (res.data.code === 200) {
          SLMessage.success(res.data.message)
          refreshKeyPoint()
          refPanelKeyPoint.value?.Close()
        } else {
          SLMessage.error(res.data.message)
        }
      })
      .catch(() => {
        SLMessage.error('添加失败')
      })
  }
})
const refreshKeyPoint = () => {
  GetKeyPoint({ areaId: state.curDistrict?.value })
    .then(res => {
      staticState.keypointLayer?.removeAll()
      const data = res.data.data
      TableConfig.dataList = data?.data || []
      TableConfig.pagination.total = data?.total || 0
      data?.data.map(item => {
        const geometry = new Point({
          longitude: item.lon,
          latitude: item.lat,
          spatialReference: staticState.view?.spatialReference
        })
        const g = new Graphic({
          geometry,
          symbol: setSymbol('point', {
            outlineWidth: 1,
            outlineColor: '#00ffff',
            color: '#ffffff'
          })
        })
        const t = new Graphic({
          geometry,
          symbol: setSymbol('text', {
            text: item.name
          })
        })
        staticState.keypointLayer?.addMany([g, t])
      })
    })
    .catch(() => {
      SLMessage.error('刷新关键点失败')
    })
}
const startDrawKeyPoint = () => {
  staticState.keypointLayer?.removeAll()
  staticState.keyPoint = undefined
  staticState.keypointLayer
    && staticState.sketch
    && (staticState.sketch.layer = staticState.keypointLayer)
  staticState.sketch?.create('point')
}
const getLayerInfo = async () => {
  state.layerIds = getSubLayerIds(staticState.view)
  const layerInfo = await queryLayerClassName(state.layerIds)
  state.layerInfos = layerInfo.data?.result?.rows || []
}

const { initSketch, destroySketch } = useSketch()
const resolveDrawEnd = (result: ISketchHandlerParameter) => {
  if (result.state !== 'complete') return
  const graphic = result.graphics[0]
  if (graphic.geometry.type === 'point') {
    staticState.keyPoint = graphic.geometry as __esri.Point
  } else {
    staticState.graphic = graphic
    if (graphic.geometry.type === 'polyline') {
      const distance = refFormDistrict.value?.dataForm.buffer
      distance
        && queryBufferPolygon(
          initBufferParams({
            bufferSpatialReference: staticState.view?.spatialReference,
            distances: [distance],
            geometries: [graphic.geometry],
            outSpatialReference: staticState.view?.spatialReference,
            geodesic: true,
            unit: 'meters',
            unionResults: false
          })
        ).then(res => {
          staticState.bufferGeometry = res[0]
          if (res.length) {
            const bufferGraphic = new Graphic({
              geometry: staticState.bufferGeometry,
              symbol: setSymbol(staticState.bufferGeometry.type, {
                color: [0, 255, 0, 0.1],
                outlineWidth: 1,
                outlineColor: '#00ff00'
              })
            })
            staticState.bufferLayer?.removeAll()
            staticState.bufferLayer?.add(bufferGraphic)
            staticState.sketch?.complete()
          }
        })
    }
  }
}
const onMaploaded = async view => {
  staticState.view = view

  staticState.bufferLayer = getGraphicLayer(staticState.view, {
    id: 'area-buffer',
    title: '缓冲区'
  })
  staticState.graphicsLayer = getGraphicLayer(staticState.view, {
    id: 'area-manage',
    title: '片区'
  })
  staticState.keypointLayer = getGraphicLayer(staticState.view, {
    id: 'key-point',
    title: '关键点'
  })
  staticState.sketch = initSketch(staticState.view, staticState.graphicsLayer, {
    createCallBack: resolveDrawEnd,
    updateCallBack: resolveDrawEnd
  })
  await getLayerInfo()
}
const refreshData = () => {
  GetAreaTree().then(res => {
    const tree = FormConfig.group[0].fields[1] as IFormTree
    const node = res.data.data
    tree.options = formatTree([node])
  })
}
onMounted(() => {
  state.isMounted = true
  refreshData()
})
onBeforeUnmount(() => {
  destroySketch()
})
</script>
<style lang="scss" scoped>
.form-wrapper {
  min-height: 610px;
  height: 100%;
  .table-box {
    height: calc(100% - 330px);
  }
}
</style>
<style lang="scss">
.district-panel {
  width: 300px;
  left: 50px;
  top: 50px;
}
.area-panel {
  width: 450px;
  left: 50%;
  top: 200px;
}
.keypoint-panel {
  width: 300px;
  left: 50px;
  top: 50px;
}
</style>
