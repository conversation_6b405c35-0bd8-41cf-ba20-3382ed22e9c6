"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[9634,3172,9880],{22303:(e,t,r)=>{r.d(t,{Z:()=>d});var n,o,i=r(35270),a=r(22021),s=r(70586),u=r(75215);function c(e){return(0,a.uZ)((0,u.vU)(e),0,255)}function l(e,t,r){return e=Number(e),isNaN(e)?r:e<t?t:e>r?r:e}class f{static blendColors(e,t,r,n=new f){return n.r=Math.round(e.r+(t.r-e.r)*r),n.g=Math.round(e.g+(t.g-e.g)*r),n.b=Math.round(e.b+(t.b-e.b)*r),n.a=e.a+(t.a-e.a)*r,n._sanitize()}static fromRgb(e,t){const r=e.toLowerCase().match(/^(rgba?|hsla?)\(([\s\.\-,%0-9]+)\)/);if(r){const e=r[2].split(/\s*,\s*/),n=r[1];if("rgb"===n&&3===e.length||"rgba"===n&&4===e.length){const r=e[0];if("%"===r.charAt(r.length-1)){const r=e.map((e=>2.56*parseFloat(e)));return 4===e.length&&(r[3]=parseFloat(e[3])),f.fromArray(r,t)}return f.fromArray(e.map((e=>parseFloat(e))),t)}if("hsl"===n&&3===e.length||"hsla"===n&&4===e.length)return f.fromArray((0,i.B7)(parseFloat(e[0]),parseFloat(e[1])/100,parseFloat(e[2])/100,parseFloat(e[3])),t)}return null}static fromHex(e,t=new f){if(4!==e.length&&7!==e.length||"#"!==e[0])return null;const r=4===e.length?4:8,n=(1<<r)-1;let o=Number("0x"+e.substr(1));return isNaN(o)?null:(["b","g","r"].forEach((e=>{const i=o&n;o>>=r,t[e]=4===r?17*i:i})),t.a=1,t)}static fromArray(e,t=new f){return t._set(Number(e[0]),Number(e[1]),Number(e[2]),Number(e[3])),isNaN(t.a)&&(t.a=1),t._sanitize()}static fromString(e,t){const r=(0,i.St)(e)?(0,i.h$)(e):null;return r&&f.fromArray(r,t)||f.fromRgb(e,t)||f.fromHex(e,t)}static fromJSON(e){return e&&new f([e[0],e[1],e[2],e[3]/255])}static toUnitRGB(e){return(0,s.pC)(e)?[e.r/255,e.g/255,e.b/255]:null}static toUnitRGBA(e){return(0,s.pC)(e)?[e.r/255,e.g/255,e.b/255,null!=e.a?e.a:1]:null}constructor(e){this.r=255,this.g=255,this.b=255,this.a=1,e&&this.setColor(e)}get isBright(){return.299*this.r+.587*this.g+.114*this.b>=127}setColor(e){return"string"==typeof e?f.fromString(e,this):Array.isArray(e)?f.fromArray(e,this):(this._set(e.r??0,e.g??0,e.b??0,e.a??1),e instanceof f||this._sanitize()),this}toRgb(){return[this.r,this.g,this.b]}toRgba(){return[this.r,this.g,this.b,this.a]}toHex(){const e=this.r.toString(16),t=this.g.toString(16),r=this.b.toString(16);return`#${e.length<2?"0"+e:e}${t.length<2?"0"+t:t}${r.length<2?"0"+r:r}`}toCss(e=!1){const t=this.r+", "+this.g+", "+this.b;return e?`rgba(${t}, ${this.a})`:`rgb(${t})`}toString(){return this.toCss(!0)}toJSON(){return this.toArray()}toArray(e=f.AlphaMode.ALWAYS){const t=c(this.r),r=c(this.g),n=c(this.b);return e===f.AlphaMode.ALWAYS||1!==this.a?[t,r,n,c(255*this.a)]:[t,r,n]}clone(){return new f(this.toRgba())}hash(){return this.r<<24|this.g<<16|this.b<<8|255*this.a}equals(e){return(0,s.pC)(e)&&e.r===this.r&&e.g===this.g&&e.b===this.b&&e.a===this.a}_sanitize(){return this.r=Math.round(l(this.r,0,255)),this.g=Math.round(l(this.g,0,255)),this.b=Math.round(l(this.b,0,255)),this.a=l(this.a,0,1),this}_set(e,t,r,n){this.r=e,this.g=t,this.b=r,this.a=n}}f.prototype.declaredClass="esri.Color",n=f||(f={}),(o=n.AlphaMode||(n.AlphaMode={}))[o.ALWAYS=0]="ALWAYS",o[o.UNLESS_OPAQUE=1]="UNLESS_OPAQUE";const d=f},99880:(e,t,r)=>{r.d(t,{V:()=>u});var n=r(68773),o=(r(3172),r(20102)),i=r(92604),a=r(17452);const s=i.Z.getLogger("esri.assets");function u(e){if(!n.Z.assetsPath)throw s.errorOnce("The API assets location needs to be set using config.assetsPath. More information: https://arcg.is/1OzLe50"),new o.Z("assets:path-not-set","config.assetsPath is not set");return(0,a.v_)(n.Z.assetsPath,e)}},46521:(e,t,r)=>{function n(){return[1,0,0,0,1,0,0,0,1]}function o(e,t,r,n,o,i,a,s,u){return[e,t,r,n,o,i,a,s,u]}function i(e,t){return new Float64Array(e,t,9)}r.d(t,{a:()=>i,c:()=>n,f:()=>o}),Object.freeze(Object.defineProperty({__proto__:null,clone:function(e){return[e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7],e[8]]},create:n,createView:i,fromValues:o},Symbol.toStringTag,{value:"Module"}))},51305:(e,t,r)=>{r.d(t,{c:()=>h,g:()=>l,j:()=>I,k:()=>g,m:()=>f,s:()=>c});var n=r(46521),o=r(94961),i=r(65617),a=r(46851),s=r(17896),u=r(98766);function c(e,t,r){r*=.5;const n=Math.sin(r);return e[0]=n*t[0],e[1]=n*t[1],e[2]=n*t[2],e[3]=Math.cos(r),e}function l(e,t){const r=2*Math.acos(t[3]),n=Math.sin(r/2);return n>(0,a.g)()?(e[0]=t[0]/n,e[1]=t[1]/n,e[2]=t[2]/n):(e[0]=1,e[1]=0,e[2]=0),r}function f(e,t,r){const n=t[0],o=t[1],i=t[2],a=t[3],s=r[0],u=r[1],c=r[2],l=r[3];return e[0]=n*l+a*s+o*c-i*u,e[1]=o*l+a*u+i*s-n*c,e[2]=i*l+a*c+n*u-o*s,e[3]=a*l-n*s-o*u-i*c,e}function d(e,t,r,n){const o=t[0],i=t[1],s=t[2],u=t[3];let c,l,f,d,h,p=r[0],g=r[1],m=r[2],b=r[3];return l=o*p+i*g+s*m+u*b,l<0&&(l=-l,p=-p,g=-g,m=-m,b=-b),1-l>(0,a.g)()?(c=Math.acos(l),f=Math.sin(c),d=Math.sin((1-n)*c)/f,h=Math.sin(n*c)/f):(d=1-n,h=n),e[0]=d*o+h*p,e[1]=d*i+h*g,e[2]=d*s+h*m,e[3]=d*u+h*b,e}function h(e,t){return e[0]=-t[0],e[1]=-t[1],e[2]=-t[2],e[3]=t[3],e}function p(e,t){const r=t[0]+t[4]+t[8];let n;if(r>0)n=Math.sqrt(r+1),e[3]=.5*n,n=.5/n,e[0]=(t[5]-t[7])*n,e[1]=(t[6]-t[2])*n,e[2]=(t[1]-t[3])*n;else{let r=0;t[4]>t[0]&&(r=1),t[8]>t[3*r+r]&&(r=2);const o=(r+1)%3,i=(r+2)%3;n=Math.sqrt(t[3*r+r]-t[3*o+o]-t[3*i+i]+1),e[r]=.5*n,n=.5/n,e[3]=(t[3*o+i]-t[3*i+o])*n,e[o]=(t[3*o+r]+t[3*r+o])*n,e[i]=(t[3*i+r]+t[3*r+i])*n}return e}function g(e,t,r,n){const o=.5*Math.PI/180;t*=o,r*=o,n*=o;const i=Math.sin(t),a=Math.cos(t),s=Math.sin(r),u=Math.cos(r),c=Math.sin(n),l=Math.cos(n);return e[0]=i*u*l-a*s*c,e[1]=a*s*l+i*u*c,e[2]=a*u*c-i*s*l,e[3]=a*u*l+i*s*c,e}const m=u.c,b=u.s,y=u.a,w=f,O=u.b,A=u.d,T=u.l,C=u.e,M=C,v=u.f,S=v,E=u.n,I=u.g,U=u.h,L=(0,i.c)(),N=(0,i.f)(1,0,0),R=(0,i.f)(0,1,0),x=(0,o.a)(),F=(0,o.a)(),P=(0,n.c)();Object.freeze(Object.defineProperty({__proto__:null,add:y,calculateW:function(e,t){const r=t[0],n=t[1],o=t[2];return e[0]=r,e[1]=n,e[2]=o,e[3]=Math.sqrt(Math.abs(1-r*r-n*n-o*o)),e},conjugate:h,copy:m,dot:A,equals:U,exactEquals:I,fromEuler:g,fromMat3:p,getAxisAngle:l,identity:function(e){return e[0]=0,e[1]=0,e[2]=0,e[3]=1,e},invert:function(e,t){const r=t[0],n=t[1],o=t[2],i=t[3],a=r*r+n*n+o*o+i*i,s=a?1/a:0;return e[0]=-r*s,e[1]=-n*s,e[2]=-o*s,e[3]=i*s,e},len:M,length:C,lerp:T,mul:w,multiply:f,normalize:E,random:function(e){const t=a.R,r=t(),n=t(),o=t(),i=Math.sqrt(1-r),s=Math.sqrt(r);return e[0]=i*Math.sin(2*Math.PI*n),e[1]=i*Math.cos(2*Math.PI*n),e[2]=s*Math.sin(2*Math.PI*o),e[3]=s*Math.cos(2*Math.PI*o),e},rotateX:function(e,t,r){r*=.5;const n=t[0],o=t[1],i=t[2],a=t[3],s=Math.sin(r),u=Math.cos(r);return e[0]=n*u+a*s,e[1]=o*u+i*s,e[2]=i*u-o*s,e[3]=a*u-n*s,e},rotateY:function(e,t,r){r*=.5;const n=t[0],o=t[1],i=t[2],a=t[3],s=Math.sin(r),u=Math.cos(r);return e[0]=n*u-i*s,e[1]=o*u+a*s,e[2]=i*u+n*s,e[3]=a*u-o*s,e},rotateZ:function(e,t,r){r*=.5;const n=t[0],o=t[1],i=t[2],a=t[3],s=Math.sin(r),u=Math.cos(r);return e[0]=n*u+o*s,e[1]=o*u-n*s,e[2]=i*u+a*s,e[3]=a*u-i*s,e},rotationTo:function(e,t,r){const n=(0,s.e)(t,r);return n<-.999999?((0,s.f)(L,N,t),(0,s.u)(L)<1e-6&&(0,s.f)(L,R,t),(0,s.n)(L,L),c(e,L,Math.PI),e):n>.999999?(e[0]=0,e[1]=0,e[2]=0,e[3]=1,e):((0,s.f)(L,t,r),e[0]=L[0],e[1]=L[1],e[2]=L[2],e[3]=1+n,E(e,e))},scale:O,set:b,setAxes:function(e,t,r,n){const o=P;return o[0]=r[0],o[3]=r[1],o[6]=r[2],o[1]=n[0],o[4]=n[1],o[7]=n[2],o[2]=-t[0],o[5]=-t[1],o[8]=-t[2],E(e,p(e,o))},setAxisAngle:c,slerp:d,sqlerp:function(e,t,r,n,o,i){return d(x,t,o,i),d(F,r,n,i),d(e,x,F,2*i*(1-i)),e},sqrLen:S,squaredLength:v,str:function(e){return"quat("+e[0]+", "+e[1]+", "+e[2]+", "+e[3]+")"}},Symbol.toStringTag,{value:"Module"}))},37140:(e,t,r)=>{function n(){const e=new Float32Array(4);return e[3]=1,e}function o(e){const t=new Float32Array(4);return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t}r.d(t,{b:()=>o,c:()=>n}),Object.freeze(Object.defineProperty({__proto__:null,clone:o,create:n,createView:function(e,t){return new Float32Array(e,t,4)},fromValues:function(e,t,r,n){const o=new Float32Array(4);return o[0]=e,o[1]=t,o[2]=r,o[3]=n,o}},Symbol.toStringTag,{value:"Module"}))},94961:(e,t,r)=>{function n(){return[0,0,0,1]}function o(e){return[e[0],e[1],e[2],e[3]]}function i(e,t){return new Float64Array(e,t,4)}r.d(t,{I:()=>a,a:()=>n,b:()=>o,c:()=>i});const a=[0,0,0,1];Object.freeze(Object.defineProperty({__proto__:null,IDENTITY:a,clone:o,create:n,createView:i,fromValues:function(e,t,r,n){return[e,t,r,n]}},Symbol.toStringTag,{value:"Module"}))},72119:(e,t,r)=>{function n(){return new Float32Array(3)}function o(e){const t=new Float32Array(3);return t[0]=e[0],t[1]=e[1],t[2]=e[2],t}function i(e,t,r){const n=new Float32Array(3);return n[0]=e,n[1]=t,n[2]=r,n}function a(){return n()}function s(){return i(1,1,1)}function u(){return i(1,0,0)}function c(){return i(0,1,0)}function l(){return i(0,0,1)}r.d(t,{b:()=>o,c:()=>n,f:()=>i});const f=a(),d=s(),h=u(),p=c(),g=l();Object.freeze(Object.defineProperty({__proto__:null,ONES:d,UNIT_X:h,UNIT_Y:p,UNIT_Z:g,ZEROS:f,clone:o,create:n,createView:function(e,t){return new Float32Array(e,t,3)},fromValues:i,ones:s,unitX:u,unitY:c,unitZ:l,zeros:a},Symbol.toStringTag,{value:"Module"}))},36030:(e,t,r)=>{r.d(t,{J:()=>i});var n=r(35454),o=r(5600);function i(e,t={}){const r=e instanceof n.X?e:new n.X(e,t),i={type:t?.ignoreUnknown??1?r.apiValues:String,json:{type:r.jsonValues,read:!t?.readOnly&&{reader:r.read},write:{writer:r.write}}};return void 0!==t?.readOnly&&(i.readOnly=!!t.readOnly),void 0!==t?.default&&(i.json.default=t.default),void 0!==t?.name&&(i.json.name=t.name),void 0!==t?.nonNullable&&(i.nonNullable=t.nonNullable),(0,o.Cb)(i)}},10661:(e,t,r)=>{r.d(t,{s:()=>o});var n=r(42100);class o extends n.s{notify(){const e=this._observers;if(e&&e.length>0){const t=e.slice();for(const e of t)e.onInvalidated(),e.onCommitted()}}}},24470:(e,t,r)=>{r.d(t,{Gv:()=>y,HH:()=>c,SO:()=>f,Ue:()=>i,al:()=>s,cS:()=>g,fS:()=>b,jE:()=>d,jn:()=>l,kK:()=>h,oJ:()=>u,r3:()=>p}),r(80442),r(22021);var n=r(70586),o=r(6570);function i(e=w){return[e[0],e[1],e[2],e[3]]}function a(e,t){return e!==t&&(e[0]=t[0],e[1]=t[1],e[2]=t[2],e[3]=t[3]),e}function s(e,t,r,n,o=i()){return o[0]=e,o[1]=t,o[2]=r,o[3]=n,o}function u(e,t=i()){return t[0]=e.xmin,t[1]=e.ymin,t[2]=e.xmax,t[3]=e.ymax,t}function c(e,t){return new o.Z({xmin:e[0],ymin:e[1],xmax:e[2],ymax:e[3],spatialReference:t})}function l(e,t,r){if((0,n.Wi)(t))a(r,e);else if("length"in t)m(t)?(r[0]=Math.min(e[0],t[0]),r[1]=Math.min(e[1],t[1]),r[2]=Math.max(e[2],t[2]),r[3]=Math.max(e[3],t[3])):2!==t.length&&3!==t.length||(r[0]=Math.min(e[0],t[0]),r[1]=Math.min(e[1],t[1]),r[2]=Math.max(e[2],t[0]),r[3]=Math.max(e[3],t[1]));else switch(t.type){case"extent":r[0]=Math.min(e[0],t.xmin),r[1]=Math.min(e[1],t.ymin),r[2]=Math.max(e[2],t.xmax),r[3]=Math.max(e[3],t.ymax);break;case"point":r[0]=Math.min(e[0],t.x),r[1]=Math.min(e[1],t.y),r[2]=Math.max(e[2],t.x),r[3]=Math.max(e[3],t.y)}}function f(e){return function(e){return(0,n.Wi)(e)||e[0]>=e[2]?0:e[2]-e[0]}(e)*function(e){return e[1]>=e[3]?0:e[3]-e[1]}(e)}function d(e,t,r){return t>=e[0]&&r>=e[1]&&t<=e[2]&&r<=e[3]}function h(e,t){return Math.max(t[0],e[0])<=Math.min(t[2],e[2])&&Math.max(t[1],e[1])<=Math.min(t[3],e[3])}function p(e,t){return t[0]>=e[0]&&t[2]<=e[2]&&t[1]>=e[1]&&t[3]<=e[3]}function g(e){return e?a(e,y):i(y)}function m(e){return null!=e&&4===e.length}function b(e,t){return m(e)&&m(t)?e[0]===t[0]&&e[1]===t[1]&&e[2]===t[2]&&e[3]===t[3]:e===t}const y=[1/0,1/0,-1/0,-1/0],w=[0,0,0,0]},3172:(e,t,r)=>{r.r(t),r.d(t,{default:()=>p});var n=r(68773),o=r(40330),i=r(20102),a=r(80442),s=r(22974),u=r(70586),c=r(95330),l=r(17452),f=r(19745),d=r(71058),h=r(85958);async function p(e,t){const s=(0,l.HK)(e),f=(0,l.jc)(e);f||s||(e=(0,l.Fv)(e));const y={url:e,requestOptions:{...(0,u.Wg)(t)}};let w=(0,l.oh)(e);if(w){const e=await async function(e,t){if(null!=e.responseData)return e.responseData;if(e.headers&&(t.requestOptions.headers={...t.requestOptions.headers,...e.headers}),e.query&&(t.requestOptions.query={...t.requestOptions.query,...e.query}),e.before){let r,n;try{n=await e.before(t)}catch(e){r=M("request:interceptor",e,t)}if((n instanceof Error||n instanceof i.Z)&&(r=M("request:interceptor",n,t)),r)throw e.error&&e.error(r),r;return n}}(w,y);if(null!=e)return{data:e,getHeader:A,httpStatus:200,requestOptions:y.requestOptions,url:y.url};w.after||w.error||(w=null)}if(e=y.url,"image"===(t=y.requestOptions).responseType){if((0,a.Z)("host-webworker")||(0,a.Z)("host-node"))throw M("request:invalid-parameters",new Error("responseType 'image' is not supported in Web Workers or Node environment"),y)}else if(s)throw M("request:invalid-parameters",new Error("Data URLs are not supported for responseType = "+t.responseType),y);if("head"===t.method){if(t.body)throw M("request:invalid-parameters",new Error("body parameter cannot be set when method is 'head'"),y);if(s||f)throw M("request:invalid-parameters",new Error("data and blob URLs are not supported for method 'head'"),y)}if(await async function(){(0,a.Z)("host-webworker")?g||(g=await r.e(9884).then(r.bind(r,29884))):p._abortableFetch||(p._abortableFetch=globalThis.fetch.bind(globalThis))}(),g)return g.execute(e,t);const O=new AbortController;(0,c.fu)(t,(()=>O.abort()));const T={controller:O,credential:void 0,credentialToken:void 0,fetchOptions:void 0,hasToken:!1,interceptor:w,params:y,redoRequest:!1,useIdentity:m.useIdentity,useProxy:!1,useSSL:!1,withCredentials:!1},C=await async function(e){let t,r;await async function(e){const t=e.params.url,r=e.params.requestOptions,i=e.controller.signal,a=r.body;let s=null,u=null;if(b&&"HTMLFormElement"in globalThis&&(a instanceof FormData?s=a:a instanceof HTMLFormElement&&(s=new FormData(a))),"string"==typeof a&&(u=a),e.fetchOptions={cache:r.cacheBust&&!p._abortableFetch.polyfill?"no-cache":"default",credentials:"same-origin",headers:r.headers||{},method:"head"===r.method?"HEAD":"GET",mode:"cors",priority:m.priority,redirect:"follow",signal:i},(s||u)&&(e.fetchOptions.body=s||u),"anonymous"===r.authMode&&(e.useIdentity=!1),e.hasToken=!!(/token=/i.test(t)||r.query?.token||s?.get("token")),!e.hasToken&&n.Z.apiKey&&(0,d.r)(t)&&(r.query||(r.query={}),r.query.token=n.Z.apiKey,e.hasToken=!0),e.useIdentity&&!e.hasToken&&!e.credentialToken&&!S(t)&&!(0,c.Hc)(i)){let n;"immediate"===r.authMode?(await v(),n=await o.id.getCredential(t,{signal:i}),e.credential=n):"no-prompt"===r.authMode?(await v(),n=await o.id.getCredential(t,{prompt:!1,signal:i}).catch((()=>{})),e.credential=n):o.id&&(n=o.id.findCredential(t)),n&&(e.credentialToken=n.token,e.useSSL=!!n.ssl)}}(e);try{do{[t,r]=await E(e)}while(!await U(e,t,r))}catch(r){const n=M("request:server",r,e.params,t);throw n.details.ssl=e.useSSL,e.interceptor&&e.interceptor.error&&e.interceptor.error(n),n}const i=e.params.url;if(r&&/\/sharing\/rest\/(accounts|portals)\/self/i.test(i)){if(!e.hasToken&&!e.credentialToken&&r.user?.username&&!(0,l.kl)(i)){const e=(0,l.P$)(i,!0);e&&m.trustedServers.push(e)}Array.isArray(r.authorizedCrossOriginNoCorsDomains)&&(0,h.Hu)(r.authorizedCrossOriginNoCorsDomains)}const a=e.credential;if(a&&o.id){const e=o.id.findServerInfo(a.server);let t=e&&e.owningSystemUrl;if(t){t=t.replace(/\/?$/,"/sharing");const e=o.id.findCredential(t,a.userId);e&&-1===o.id._getIdenticalSvcIdx(t,e)&&e.resources.unshift(t)}}return{data:r,getHeader:t?e=>t?.headers.get(e):A,httpStatus:t?.status??200,requestOptions:e.params.requestOptions,ssl:e.useSSL,url:e.params.url}}(T);return w?.after?.(C),C}let g;const m=n.Z.request,b="FormData"in globalThis,y=[499,498,403,401],w=["COM_0056","COM_0057","SB_0008"],O=[/\/arcgis\/tokens/i,/\/sharing(\/rest)?\/generatetoken/i,/\/rest\/info/i],A=()=>null,T=Symbol();function C(e){const t=(0,l.P$)(e);return!t||t.endsWith(".arcgis.com")||p._corsServers.includes(t)||(0,l.kl)(t)}function M(e,t,r,n){let o="Error";const a={url:r.url,requestOptions:r.requestOptions,getHeader:A,ssl:!1};if(t instanceof i.Z)return t.details?(t.details=(0,s.d9)(t.details),t.details.url=r.url,t.details.requestOptions=r.requestOptions):t.details=a,t;if(t){const e=n&&(e=>n.headers.get(e)),r=n&&n.status,i=t.message;i&&(o=i),e&&(a.getHeader=e),a.httpStatus=(null!=t.httpCode?t.httpCode:t.code)||r||0,a.subCode=t.subcode,a.messageCode=t.messageCode,"string"==typeof t.details?a.messages=[t.details]:a.messages=t.details,a.raw=T in t?t[T]:t}return(0,c.D_)(t)?(0,c.zE)():new i.Z(e,o,a)}async function v(){o.id||await Promise.all([r.e(6261),r.e(1400),r.e(450)]).then(r.bind(r,73660))}function S(e){return O.some((t=>t.test(e)))}async function E(e){let t=e.params.url;const r=e.params.requestOptions,n=e.fetchOptions??{},i=(0,l.jc)(t)||(0,l.HK)(t),s=r.responseType||"json",u=i?0:null!=r.timeout?r.timeout:m.timeout;let d=!1;if(!i){e.useSSL&&(t=(0,l.hO)(t)),r.cacheBust&&"default"===n.cache&&(t=(0,l.ZN)(t,"request.preventCache",Date.now()));let i={...r.query};e.credentialToken&&(i.token=e.credentialToken);let s=(0,l.B7)(i);(0,a.Z)("esri-url-encodes-apostrophe")&&(s=s.replace(/'/g,"%27"));const u=t.length+1+s.length;let c;d="delete"===r.method||"post"===r.method||"put"===r.method||!!r.body||u>m.maxUrlLength;const p=r.useProxy||!!(0,l.ed)(t);if(p){const e=(0,l.b7)(t);c=e.path,!d&&c.length+1+u>m.maxUrlLength&&(d=!0),e.query&&(i={...e.query,...i})}if("HEAD"===n.method&&(d||p)){if(d){if(u>m.maxUrlLength)throw M("request:invalid-parameters",new Error("URL exceeds maximum length"),e.params);throw M("request:invalid-parameters",new Error("cannot use POST request when method is 'head'"),e.params)}if(p)throw M("request:invalid-parameters",new Error("cannot use proxy when method is 'head'"),e.params)}if(d?(n.method="delete"===r.method?"DELETE":"put"===r.method?"PUT":"POST",r.body?t=(0,l.fl)(t,i):(n.body=(0,l.B7)(i),n.headers||(n.headers={}),n.headers["Content-Type"]="application/x-www-form-urlencoded")):t=(0,l.fl)(t,i),p&&(e.useProxy=!0,t=`${c}?${t}`),i.token&&b&&n.body instanceof FormData&&!(0,f.P)(t)&&n.body.set("token",i.token),r.hasOwnProperty("withCredentials"))e.withCredentials=r.withCredentials;else if(!(0,l.D6)(t,(0,l.TI)()))if((0,l.kl)(t))e.withCredentials=!0;else if(o.id){const r=o.id.findServerInfo(t);r&&r.webTierAuth&&(e.withCredentials=!0)}e.withCredentials&&(n.credentials="include",(0,h.jH)(t)&&await(0,h.jz)(d?(0,l.fl)(t,i):t))}let g,y,w=0,O=!1;u>0&&(w=setTimeout((()=>{O=!0,e.controller.abort()}),u));try{if("native-request-init"===r.responseType)y=n,y.url=t;else if("image"!==r.responseType||"default"!==n.cache||"GET"!==n.method||d||function(e){if(e)for(const t of Object.getOwnPropertyNames(e))if(e[t])return!0;return!1}(r.headers)||!i&&!e.useProxy&&m.proxyUrl&&!C(t)){if(g=await p._abortableFetch(t,n),e.useProxy||function(e){const t=(0,l.P$)(e);t&&!p._corsServers.includes(t)&&p._corsServers.push(t)}(t),"native"===r.responseType)y=g;else if("HEAD"!==n.method)if(g.ok){switch(s){case"array-buffer":y=await g.arrayBuffer();break;case"blob":case"image":y=await g.blob();break;default:y=await g.text()}if(w&&(clearTimeout(w),w=0),"json"===s||"xml"===s||"document"===s)if(y)switch(s){case"json":y=JSON.parse(y);break;case"xml":y=I(y,"application/xml");break;case"document":y=I(y,"text/html")}else y=null;if(y){if("array-buffer"===s||"blob"===s){const e=g.headers.get("Content-Type");if(e&&/application\/json|text\/plain/i.test(e)&&y["blob"===s?"size":"byteLength"]<=750)try{const e=await new Response(y).json();e.error&&(y=e)}catch{}}"image"===s&&y instanceof Blob&&(y=await L(URL.createObjectURL(y),e,!0))}}else y=await g.text()}else y=await L(t,e)}catch(n){if("AbortError"===n.name){if(O)throw new Error("Timeout exceeded");throw(0,c.zE)("Request canceled")}if(!(!g&&n instanceof TypeError&&m.proxyUrl)||r.body||"delete"===r.method||"head"===r.method||"post"===r.method||"put"===r.method||e.useProxy||C(t))throw n;e.redoRequest=!0,(0,l.tD)({proxyUrl:m.proxyUrl,urlPrefix:(0,l.P$)(t)??""})}finally{w&&clearTimeout(w)}return[g,y]}function I(e,t){let r;try{r=(new DOMParser).parseFromString(e,t)}catch{}if(!r||r.getElementsByTagName("parsererror").length)throw new SyntaxError("XML Parse error");return r}async function U(e,t,r){if(e.redoRequest)return e.redoRequest=!1,!1;const n=e.params.requestOptions;if(!t||"native"===n.responseType||"native-request-init"===n.responseType)return!0;let i,a;if(!t.ok)throw i=new Error(`Unable to load ${t.url} status: ${t.status}`),i[T]=r,i;r&&(r.error?i=r.error:"error"===r.status&&Array.isArray(r.messages)&&(i={...r},i[T]=r,i.details=r.messages));let s,u=null;i&&(a=Number(i.code),u=i.hasOwnProperty("subcode")?Number(i.subcode):null,s=i.messageCode,s=s&&s.toUpperCase());const c=n.authMode;if(403===a&&(4===u||i.message&&i.message.toLowerCase().includes("ssl")&&!i.message.toLowerCase().includes("permission"))){if(!e.useSSL)return e.useSSL=!0,!1}else if(!e.hasToken&&e.useIdentity&&("no-prompt"!==c||498===a)&&void 0!==a&&y.includes(a)&&!S(e.params.url)&&(403!==a||s&&!w.includes(s)&&(null==u||2===u&&e.credentialToken))){await v();try{const t=await o.id.getCredential(e.params.url,{error:M("request:server",i,e.params),prompt:"no-prompt"!==c,signal:e.controller.signal,token:e.credentialToken});return e.credential=t,e.credentialToken=t.token,e.useSSL=e.useSSL||t.ssl,!1}catch(t){if("no-prompt"===c)return e.credential=void 0,e.credentialToken=void 0,!1;i=t}}if(i)throw i;return!0}function L(e,t,r=!1){const n=t.controller.signal,o=new Image;return t.withCredentials?o.crossOrigin="use-credentials":o.crossOrigin="anonymous",o.alt="",o.fetchPriority=m.priority,o.src=e,(0,h.fY)(o,e,r,n)}p._abortableFetch=null,p._corsServers=["https://server.arcgisonline.com","https://services.arcgisonline.com"]},71058:(e,t,r)=>{r.d(t,{r:()=>i});var n=r(17452);const o=["elevation3d.arcgis.com","js.arcgis.com","jsdev.arcgis.com","jsqa.arcgis.com","static.arcgis.com"];function i(e){const t=(0,n.P$)(e,!0);return!!t&&t.endsWith(".arcgis.com")&&!o.includes(t)&&!e.endsWith("/sharing/rest/generateToken")}},85958:(e,t,r)=>{r.d(t,{Hu:()=>l,fY:()=>u,jH:()=>f,jz:()=>d});var n=r(68773),o=r(80442),i=r(70586),a=r(95330),s=r(17452);function u(e,t,r=!1,n){return new Promise(((s,u)=>{if((0,a.Hc)(n))return void u(c());let l=()=>{h(),u(new Error(`Unable to load ${t}`))},f=()=>{const t=e;h(),s(t)},d=()=>{if(!e)return;const t=e;h(),t.src="",u(c())};const h=()=>{(0,o.Z)("esri-image-decode")||(e.removeEventListener("error",l),e.removeEventListener("load",f)),l=null,f=null,e=null,(0,i.pC)(n)&&n.removeEventListener("abort",d),d=null,r&&URL.revokeObjectURL(t)};(0,i.pC)(n)&&n.addEventListener("abort",d),(0,o.Z)("esri-image-decode")?e.decode().then(f,l):(e.addEventListener("error",l),e.addEventListener("load",f))}))}function c(){try{return new DOMException("Aborted","AbortError")}catch{const e=new Error;return e.name="AbortError",e}}function l(e){n.Z.request.crossOriginNoCorsDomains||(n.Z.request.crossOriginNoCorsDomains={});const t=n.Z.request.crossOriginNoCorsDomains;for(let r of e)r=r.toLowerCase(),/^https?:\/\//.test(r)?t[(0,s.P$)(r)??""]=0:(t[(0,s.P$)("http://"+r)??""]=0,t[(0,s.P$)("https://"+r)??""]=0)}function f(e){const t=n.Z.request.crossOriginNoCorsDomains;if(t){let r=(0,s.P$)(e);if(r)return r=r.toLowerCase(),!(0,s.D6)(r,(0,s.TI)())&&t[r]<Date.now()-36e5}return!1}async function d(e){const t=n.Z.request.crossOriginNoCorsDomains,r=(0,s.P$)(e);t&&r&&(t[r.toLowerCase()]=Date.now());const o=(0,s.mN)(e);e=o.path,"json"===o.query?.f&&(e+="?f=json");try{await fetch(e,{mode:"no-cors",credentials:"include"})}catch{}}},90447:(e,t,r)=>{r.r(t),r.d(t,{default:()=>T});var n=r(70586),o=r(1533),i=r(51305),a=r(37140),s=r(17896),u=r(72119),c=r(44547),l=r(82971),f=r(23030),d=r(14008),h=r(46329),p=r(91891),g=r(25683);function m(e,t,r){return(0,n.pC)(e)&&e.attributeInfo.useElevation?t?function(e,t){const r=new Float64Array(t);for(let n=0;n<t;n++)r[n]=e[3*n+2];return r}(t,r):null:(0,n.pC)(e)&&e.attributeInfo.storageInfo?(0,p.qM)(e.attributeInfo.storageInfo,e.buffer,r):null}function b(e,t,r,n,o){const i=e.length/3;let a=0;for(let s=0;s<i;s++){let i=!0;for(let e=0;e<n.length&&i;e++){const{filterJSON:t}=n[e],r=o[e].values[s];switch(t.type){case"pointCloudValueFilter":{const e="exclude"===t.mode;t.values.includes(r)===e&&(i=!1);break}case"pointCloudBitfieldFilter":{const e=w(t.requiredSetBits),n=w(t.requiredClearBits);(r&e)===e&&0==(r&n)||(i=!1);break}case"pointCloudReturnFilter":{const e=15&r,n=r>>>4&15,o=n>1,a=1===e,s=e===n;let u=!1;for(const e of t.includedReturns)if("last"===e&&s||"firstOfMany"===e&&a&&o||"lastOfMany"===e&&s&&o||"single"===e&&!o){u=!0;break}u||(i=!1);break}}}i&&(r[a]=s,e[3*a]=e[3*s],e[3*a+1]=e[3*s+1],e[3*a+2]=e[3*s+2],t[3*a]=t[3*s],t[3*a+1]=t[3*s+1],t[3*a+2]=t[3*s+2],a++)}return a}function y(e){return null==e||"none"===e?null:"low-four-bit"===e?e=>15&e:"high-four-bit"===e?e=>(240&e)>>4:"absolute-value"===e?e=>Math.abs(e):"modulo-ten"===e?e=>e%10:null}function w(e){let t=0;for(const r of e||[])t|=1<<r;return t}class O{transform(e){const t=this._transform(e),r=[t.points.buffer,t.rgb.buffer];(0,n.pC)(t.pointIdFilterMap)&&r.push(t.pointIdFilterMap.buffer);for(const e of t.attributes)"buffer"in e.values&&(0,o.eP)(e.values.buffer)&&e.values.buffer!==t.rgb.buffer&&r.push(e.values.buffer);return Promise.resolve({result:t,transferList:r})}_transform(e){const t=function(e,t){if(null==e.encoding||""===e.encoding){const r=(0,p.W7)(t,e);if((0,n.Wi)(r.vertexAttributes.position))return;const o=(0,p.I_)(t,r.vertexAttributes.position),i=r.header.fields,a=[i.offsetX,i.offsetY,i.offsetZ],s=[i.scaleX,i.scaleY,i.scaleZ],u=o.length/3,c=new Float64Array(3*u);for(let e=0;e<u;e++)c[3*e]=o[3*e]*s[0]+a[0],c[3*e+1]=o[3*e+1]*s[1]+a[1],c[3*e+2]=o[3*e+2]*s[2]+a[2];return c}if("lepcc-xyz"===e.encoding)return(0,g.Gi)(t).result}(e.schema,e.geometryBuffer);let r=t.length/3,o=null;const i=[],a=m(e.primaryAttributeData,t,r);(0,n.pC)(e.primaryAttributeData)&&a&&i.push({attributeInfo:e.primaryAttributeData.attributeInfo,values:a});const s=m(e.modulationAttributeData,t,r);(0,n.pC)(e.modulationAttributeData)&&s&&i.push({attributeInfo:e.modulationAttributeData.attributeInfo,values:s});let u=function(e,t,r,n){const{rendererJSON:o,isRGBRenderer:i}=e;let a=null,s=null;if(t&&i)a=t;else if(t&&"pointCloudUniqueValueRenderer"===o?.type){s=h.Z.fromJSON(o);const e=s.colorUniqueValueInfos;a=new Uint8Array(3*n);const r=y(s.fieldTransformType);for(let o=0;o<n;o++){const n=(r?r(t[o]):t[o])+"";for(let t=0;t<e.length;t++)if(e[t].values.includes(n)){a[3*o]=e[t].color.r,a[3*o+1]=e[t].color.g,a[3*o+2]=e[t].color.b;break}}}else if(t&&"pointCloudStretchRenderer"===o?.type){s=d.Z.fromJSON(o);const e=s.stops;a=new Uint8Array(3*n);const r=y(s.fieldTransformType);for(let o=0;o<n;o++){const n=r?r(t[o]):t[o],i=e.length-1;if(n<e[0].value)a[3*o]=e[0].color.r,a[3*o+1]=e[0].color.g,a[3*o+2]=e[0].color.b;else if(n>=e[i].value)a[3*o]=e[i].color.r,a[3*o+1]=e[i].color.g,a[3*o+2]=e[i].color.b;else for(let t=1;t<e.length;t++)if(n<e[t].value){const r=(n-e[t-1].value)/(e[t].value-e[t-1].value);a[3*o]=e[t].color.r*r+e[t-1].color.r*(1-r),a[3*o+1]=e[t].color.g*r+e[t-1].color.g*(1-r),a[3*o+2]=e[t].color.b*r+e[t-1].color.b*(1-r);break}}}else if(t&&"pointCloudClassBreaksRenderer"===o?.type){s=f.Z.fromJSON(o);const e=s.colorClassBreakInfos;a=new Uint8Array(3*n);const r=y(s.fieldTransformType);for(let o=0;o<n;o++){const n=r?r(t[o]):t[o];for(let t=0;t<e.length;t++)if(n>=e[t].minValue&&n<=e[t].maxValue){a[3*o]=e[t].color.r,a[3*o+1]=e[t].color.g,a[3*o+2]=e[t].color.b;break}}}else{a=new Uint8Array(3*n);for(let e=0;e<a.length;e++)a[e]=255}if(r&&s&&s.colorModulation){const e=s.colorModulation.minValue,t=s.colorModulation.maxValue,o=.3;for(let i=0;i<n;i++){const n=r[i],s=n>=t?1:n<=e?o:o+(1-o)*(n-e)/(t-e);a[3*i]=s*a[3*i],a[3*i+1]=s*a[3*i+1],a[3*i+2]=s*a[3*i+2]}}return a}(e.rendererInfo,a,s,r);if(e.filterInfo&&e.filterInfo.length>0&&(0,n.pC)(e.filterAttributesData)){const a=e.filterAttributesData.filter(n.pC).map((e=>{const n=m(e,t,r),o={attributeInfo:e.attributeInfo,values:n};return i.push(o),o}));o=new Uint32Array(r),r=b(t,u,o,e.filterInfo,a)}for(const n of e.userAttributesData){const e=m(n,t,r);i.push({attributeInfo:n.attributeInfo,values:e})}3*r<u.length&&(u=new Uint8Array(u.buffer.slice(0,3*r))),this._applyElevationOffsetInPlace(t,r,e.elevationOffset);const c=this._transformCoordinates(t,r,e.obb,l.Z.fromJSON(e.inSR),l.Z.fromJSON(e.outSR));return{obb:e.obb,points:c,rgb:u,attributes:i,pointIdFilterMap:o}}_transformCoordinates(e,t,r,n,o){if(!(0,c.CM)(e,n,0,e,o,0,t))throw new Error("Can't reproject");const a=(0,u.f)(r.center[0],r.center[1],r.center[2]),l=(0,u.c)(),f=(0,u.c)();(0,i.c)(A,r.quaternion);const d=new Float32Array(3*t);for(let n=0;n<t;n++)l[0]=e[3*n]-a[0],l[1]=e[3*n+1]-a[1],l[2]=e[3*n+2]-a[2],(0,s.q)(f,l,A),r.halfSize[0]=Math.max(r.halfSize[0],Math.abs(f[0])),r.halfSize[1]=Math.max(r.halfSize[1],Math.abs(f[1])),r.halfSize[2]=Math.max(r.halfSize[2],Math.abs(f[2])),d[3*n]=l[0],d[3*n+1]=l[1],d[3*n+2]=l[2];return d}_applyElevationOffsetInPlace(e,t,r){if(0!==r)for(let n=0;n<t;n++)e[3*n+2]+=r}}const A=(0,a.c)();function T(){return new O}},91891:(e,t,r)=>{r.d(t,{I_:()=>d,W7:()=>g,qM:()=>b});var n=r(20102),o=r(22974),i=r(92604),a=r(25683),s=r(35065);const u=i.Z.getLogger("esri.views.3d.layers.i3s.I3SBinaryReader");function c(e,t,r){let o="",i=0;for(;i<r;){const a=e[t+i];if(a<128)o+=String.fromCharCode(a),i++;else if(a>=192&&a<224){if(i+1>=r)throw new n.Z("utf8-decode-error","UTF-8 Decode failed. Two byte character was truncated.");const s=(31&a)<<6|63&e[t+i+1];o+=String.fromCharCode(s),i+=2}else if(a>=224&&a<240){if(i+2>=r)throw new n.Z("utf8-decode-error","UTF-8 Decode failed. Multi byte character was truncated.");const s=(15&a)<<12|(63&e[t+i+1])<<6|63&e[t+i+2];o+=String.fromCharCode(s),i+=3}else{if(!(a>=240&&a<248))throw new n.Z("utf8-decode-error","UTF-8 Decode failed. Invalid multi byte sequence.");{if(i+3>=r)throw new n.Z("utf8-decode-error","UTF-8 Decode failed. Multi byte character was truncated.");const s=(7&a)<<18|(63&e[t+i+1])<<12|(63&e[t+i+2])<<6|63&e[t+i+3];if(s>=65536){const e=55296+(s-65536>>10),t=56320+(1023&s);o+=String.fromCharCode(e,t)}else o+=String.fromCharCode(s);i+=4}}}return o}function l(e,t){const r={byteOffset:0,byteCount:0,fields:Object.create(null)};let n=0;for(let o=0;o<t.length;o++){const i=t[o],a=i.valueType||i.type,s=w[a];r.fields[i.property]=s(e,n),n+=y[a].BYTES_PER_ELEMENT}return r.byteCount=n,r}function f(e,t,r){const o=[];let i,a,s=0;for(a=0;a<e;a+=1){if(i=t[a],i>0){if(o.push(c(r,s,i-1)),0!==r[s+i-1])throw new n.Z("string-array-error","Invalid string array: missing null termination.")}else o.push(null);s+=i}return o}function d(e,t){return new(0,y[t.valueType])(e,t.byteOffset,t.count*t.valuesPerElement)}function h(e,t,r){const i=null!=t.header?l(e,t.header):{byteOffset:0,byteCount:0,fields:{count:r}},a={header:i,byteOffset:i.byteCount,byteCount:0,entries:Object.create(null)};let s=i.byteCount;for(let e=0;e<t.ordering.length;e++){const r=t.ordering[e],u=(0,o.d9)(t[r]);if(u.count=i.fields.count??0,"String"===u.valueType){if(u.byteOffset=s,u.byteCount=i.fields[r+"ByteCount"],"UTF-8"!==u.encoding)throw new n.Z("unsupported-encoding","Unsupported String encoding.",{encoding:u.encoding});if(u.timeEncoding&&"ECMA_ISO8601"!==u.timeEncoding)throw new n.Z("unsupported-time-encoding","Unsupported time encoding.",{timeEncoding:u.timeEncoding})}else{if(!O(u.valueType))throw new n.Z("unsupported-value-type","Unsupported binary valueType",{valueType:u.valueType});{const e=A(u.valueType);s+=s%e!=0?e-s%e:0,u.byteOffset=s,u.byteCount=e*u.valuesPerElement*u.count}}s+=u.byteCount??0,a.entries[r]=u}return a.byteCount=s-a.byteOffset,a}function p(e,t,r){if(t!==e&&u.error(`Invalid ${r} buffer size\n expected: ${e}, actual: ${t})`),t<e)throw new n.Z("buffer-too-small","Binary buffer is too small",{expectedSize:e,actualSize:t})}function g(e,t){const r=l(e,t&&t.header);let n=r.byteCount;const o={isDraco:!1,header:r,byteOffset:r.byteCount,byteCount:0,vertexAttributes:{}},i=r.fields,a=null!=i.vertexCount?i.vertexCount:i.count;for(const e of t.ordering){if(!t.vertexAttributes[e])continue;const r={...t.vertexAttributes[e],byteOffset:n,count:a},i=m[e]?m[e]:"_"+e;o.vertexAttributes[i]=r,n+=A(r.valueType)*r.valuesPerElement*a}const s=i.faceCount;if(t.faces&&s){o.faces={};for(const e of t.ordering){if(!t.faces[e])continue;const r={...t.faces[e],byteOffset:n,count:s};o.faces[e]=r,n+=A(r.valueType)*r.valuesPerElement*s}}const u=i.featureCount;if(t.featureAttributes&&t.featureAttributeOrder&&u){o.featureAttributes={};for(const e of t.featureAttributeOrder){if(!t.featureAttributes[e])continue;const r={...t.featureAttributes[e],byteOffset:n,count:u};o.featureAttributes[e]=r,n+=("UInt64"===r.valueType?8:A(r.valueType))*r.valuesPerElement*u}}return p(n,e.byteLength,"geometry"),o.byteCount=n-o.byteOffset,o}const m={position:s.T.POSITION,normal:s.T.NORMAL,color:s.T.COLOR,uv0:s.T.UV0,region:s.T.UVREGION};function b(e,t,r){if("lepcc-rgb"===e.encoding)return(0,a.IT)(t);if("lepcc-intensity"===e.encoding)return(0,a.ti)(t);if(null!=e.encoding&&""!==e.encoding)throw new n.Z("unknown-attribute-storage-info-encoding","Unknown Attribute Storage Info Encoding");e["attributeByteCounts "]&&!e.attributeByteCounts&&(u.warn("Warning: Trailing space in 'attributeByteCounts '."),e.attributeByteCounts=e["attributeByteCounts "]),"ObjectIds"===e.ordering[0]&&e.hasOwnProperty("objectIds")&&(u.warn("Warning: Case error in objectIds"),e.ordering[0]="objectIds");const o=h(t,e,r);p(o.byteOffset+o.byteCount,t.byteLength,"attribute");const i=o.entries.attributeValues||o.entries.objectIds;if(i){if("String"===i.valueType){const e=o.entries.attributeByteCounts,r=d(t,e),n=function(e,t){return new Uint8Array(e,t.byteOffset,t.byteCount)}(t,i);return i.timeEncoding?function(e,t,r){return f(e,t,r).map((e=>{const t=e?Date.parse(e):null;return t&&!Number.isNaN(t)?t:null}))}(e.count,r,n):f(e.count,r,n)}return d(t,i)}throw new n.Z("bad-attribute-storage-info","Bad attributeStorageInfo specification.")}const y={Float32:Float32Array,Float64:Float64Array,UInt8:Uint8Array,Int8:Int8Array,UInt16:Uint16Array,Int16:Int16Array,UInt32:Uint32Array,Int32:Int32Array},w={Float32:(e,t)=>new DataView(e,0).getFloat32(t,!0),Float64:(e,t)=>new DataView(e,0).getFloat64(t,!0),UInt8:(e,t)=>new DataView(e,0).getUint8(t),Int8:(e,t)=>new DataView(e,0).getInt8(t),UInt16:(e,t)=>new DataView(e,0).getUint16(t,!0),Int16:(e,t)=>new DataView(e,0).getInt16(t,!0),UInt32:(e,t)=>new DataView(e,0).getUint32(t,!0),Int32:(e,t)=>new DataView(e,0).getInt32(t,!0)};function O(e){return y.hasOwnProperty(e)}function A(e){return O(e)?y[e].BYTES_PER_ELEMENT:0}},25683:(e,t,r)=>{r.d(t,{Gi:()=>s,IT:()=>f,ti:()=>h});var n=r(20102);const o=!0;function i(e,t,r){return{identifier:String.fromCharCode.apply(null,new Uint8Array(e,r+0,10)),version:t.getUint16(r+10,o),checksum:t.getUint32(r+12,o)}}function a(e,t){return{sizeLo:e.getUint32(t+0,o),sizeHi:e.getUint32(t+4,o),minX:e.getFloat64(t+8,o),minY:e.getFloat64(t+16,o),minZ:e.getFloat64(t+24,o),maxX:e.getFloat64(t+32,o),maxY:e.getFloat64(t+40,o),maxZ:e.getFloat64(t+48,o),errorX:e.getFloat64(t+56,o),errorY:e.getFloat64(t+64,o),errorZ:e.getFloat64(t+72,o),count:e.getUint32(t+80,o),reserved:e.getUint32(t+84,o)}}function s(e){const t=new DataView(e,0);let r=0;const{identifier:o,version:s}=i(e,t,r);if(r+=16,"LEPCC     "!==o)throw new n.Z("lepcc-decode-error","Bad identifier");if(s>1)throw new n.Z("lepcc-decode-error","Unknown version");const c=a(t,r);if(r+=88,c.sizeHi*2**32+c.sizeLo!==e.byteLength)throw new n.Z("lepcc-decode-error","Bad size");const l=new Float64Array(3*c.count),f=[],d=[],h=[],p=[];if(r=u(e,r,f),r=u(e,r,d),r=u(e,r,h),r=u(e,r,p),r!==e.byteLength)throw new n.Z("lepcc-decode-error","Bad length");let g=0,m=0;for(let e=0;e<f.length;e++){m+=f[e];let t=0;for(let r=0;r<d[e];r++){t+=h[g];const e=p[g];l[3*g]=Math.min(c.maxX,c.minX+2*c.errorX*t),l[3*g+1]=Math.min(c.maxY,c.minY+2*c.errorY*m),l[3*g+2]=Math.min(c.maxZ,c.minZ+2*c.errorZ*e),g++}}return{errorX:c.errorX,errorY:c.errorY,errorZ:c.errorZ,result:l}}function u(e,t,r){const n=[];t=c(e,t,n);const o=[];for(let i=0;i<n.length;i++){o.length=0,t=c(e,t,o);for(let e=0;e<o.length;e++)r.push(o[e]+n[i])}return t}function c(e,t,r){const i=new DataView(e,t),a=i.getUint8(0),s=31&a,u=!!(32&a),c=(192&a)>>6;let l=0;if(0===c)l=i.getUint32(1,o),t+=5;else if(1===c)l=i.getUint16(1,o),t+=3;else{if(2!==c)throw new n.Z("lepcc-decode-error","Bad count type");l=i.getUint8(1),t+=2}if(u)throw new n.Z("lepcc-decode-error","LUT not implemented");const f=Math.ceil(l*s/8),d=new Uint8Array(e,t,f);let h=0,p=0,g=0;const m=-1>>>32-s;for(let e=0;e<l;e++){for(;p<s;)h|=d[g]<<p,p+=8,g+=1;r[e]=h&m,h>>>=s,p-=s,p+s>32&&(h|=d[g-1]>>8-p)}return t+g}function l(e,t){return{sizeLo:e.getUint32(t+0,o),sizeHi:e.getUint32(t+4,o),count:e.getUint32(t+8,o),colorMapCount:e.getUint16(t+12,o),lookupMethod:e.getUint8(t+14),compressionMethod:e.getUint8(t+15)}}function f(e){const t=new DataView(e,0);let r=0;const{identifier:o,version:a}=i(e,t,r);if(r+=16,"ClusterRGB"!==o)throw new n.Z("lepcc-decode-error","Bad identifier");if(a>1)throw new n.Z("lepcc-decode-error","Unknown version");const s=l(t,r);if(r+=16,s.sizeHi*2**32+s.sizeLo!==e.byteLength)throw new n.Z("lepcc-decode-error","Bad size");if((2===s.lookupMethod||1===s.lookupMethod)&&0===s.compressionMethod){if(3*s.colorMapCount+s.count+r!==e.byteLength||s.colorMapCount>256)throw new n.Z("lepcc-decode-error","Bad count");const t=new Uint8Array(e,r,3*s.colorMapCount),o=new Uint8Array(e,r+3*s.colorMapCount,s.count),i=new Uint8Array(3*s.count);for(let e=0;e<s.count;e++){const r=o[e];i[3*e]=t[3*r],i[3*e+1]=t[3*r+1],i[3*e+2]=t[3*r+2]}return i}if(0===s.lookupMethod&&0===s.compressionMethod){if(3*s.count+r!==e.byteLength||0!==s.colorMapCount)throw new n.Z("lepcc-decode-error","Bad count");return new Uint8Array(e,r).slice()}if(s.lookupMethod<=2&&1===s.compressionMethod){if(r+3!==e.byteLength||1!==s.colorMapCount)throw new n.Z("lepcc-decode-error","Bad count");const o=t.getUint8(r),i=t.getUint8(r+1),a=t.getUint8(r+2),u=new Uint8Array(3*s.count);for(let e=0;e<s.count;e++)u[3*e]=o,u[3*e+1]=i,u[3*e+2]=a;return u}throw new n.Z("lepcc-decode-error","Bad method "+s.lookupMethod+","+s.compressionMethod)}function d(e,t){return{sizeLo:e.getUint32(t+0,o),sizeHi:e.getUint32(t+4,o),count:e.getUint32(t+8,o),scaleFactor:e.getUint16(t+12,o),bitsPerPoint:e.getUint8(t+14),reserved:e.getUint8(t+15)}}function h(e){const t=new DataView(e,0);let r=0;const{identifier:o,version:a}=i(e,t,r);if(r+=16,"Intensity "!==o)throw new n.Z("lepcc-decode-error","Bad identifier");if(a>1)throw new n.Z("lepcc-decode-error","Unknown version");const s=d(t,r);if(r+=16,s.sizeHi*2**32+s.sizeLo!==e.byteLength)throw new n.Z("lepcc-decode-error","Bad size");const u=new Uint16Array(s.count);if(8===s.bitsPerPoint){if(s.count+r!==e.byteLength)throw new n.Z("lepcc-decode-error","Bad size");const t=new Uint8Array(e,r,s.count);for(let e=0;e<s.count;e++)u[e]=t[e]*s.scaleFactor}else if(16===s.bitsPerPoint){if(2*s.count+r!==e.byteLength)throw new n.Z("lepcc-decode-error","Bad size");const t=new Uint16Array(e,r,s.count);for(let e=0;e<s.count;e++)u[e]=t[e]*s.scaleFactor}else{const t=[];if(c(e,r,t)!==e.byteLength)throw new n.Z("lepcc-decode-error","Bad size");for(let e=0;e<s.count;e++)u[e]=t[e]*s.scaleFactor}return u}},35065:(e,t,r)=>{var n;r.d(t,{T:()=>n}),function(e){e.POSITION="position",e.NORMAL="normal",e.UV0="uv0",e.AUXPOS1="auxpos1",e.AUXPOS2="auxpos2",e.COLOR="color",e.SYMBOLCOLOR="symbolColor",e.SIZE="size",e.TANGENT="tangent",e.OFFSET="offset",e.SUBDIVISIONFACTOR="subdivisionFactor",e.COLORFEATUREATTRIBUTE="colorFeatureAttribute",e.SIZEFEATUREATTRIBUTE="sizeFeatureAttribute",e.OPACITYFEATUREATTRIBUTE="opacityFeatureAttribute",e.DISTANCETOSTART="distanceToStart",e.UVMAPSPACE="uvMapSpace",e.BOUNDINGRECT="boundingRect",e.UVREGION="uvRegion",e.NORMALCOMPRESSED="normalCompressed",e.PROFILERIGHT="profileRight",e.PROFILEUP="profileUp",e.PROFILEVERTEXANDNORMAL="profileVertexAndNormal",e.FEATUREVALUE="featureValue",e.MODELORIGINHI="modelOriginHi",e.MODELORIGINLO="modelOriginLo",e.MODEL="model",e.MODELNORMAL="modelNormal",e.INSTANCECOLOR="instanceColor",e.INSTANCEFEATUREATTRIBUTE="instanceFeatureAttribute",e.LOCALTRANSFORM="localTransform",e.GLOBALTRANSFORM="globalTransform",e.BOUNDINGSPHERE="boundingSphere",e.MODELORIGIN="modelOrigin",e.MODELSCALEFACTORS="modelScaleFactors",e.FEATUREATTRIBUTE="featureAttribute",e.STATE="state",e.LODLEVEL="lodLevel",e.POSITION0="position0",e.POSITION1="position1",e.NORMALA="normalA",e.NORMALB="normalB",e.COMPONENTINDEX="componentIndex",e.VARIANTOFFSET="variantOffset",e.VARIANTSTROKE="variantStroke",e.VARIANTEXTENSION="variantExtension",e.U8PADDING="u8padding",e.U16PADDING="u16padding",e.SIDENESS="sideness",e.START="start",e.END="end",e.UP="up",e.EXTRUDE="extrude",e.OBJECTANDLAYERIDCOLOR="objectAndLayerIdColor",e.OBJECTANDLAYERIDCOLOR_INSTANCED="objectAndLayerIdColor_instanced"}(n||(n={}))}}]);