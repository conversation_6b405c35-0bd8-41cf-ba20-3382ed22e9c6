import{e as h,y as p,a as V,x as xe,w as be,j as F,i as C,u as J,s as X}from"./Point-WxyopZva.js";import{dG as z,dI as Te,dJ as O,dK as G,dM as we,d_ as ve,bu as le,bO as q,bP as Y,A as ue,eH as Pe,c_ as Ie,bD as K,w as Re,dO as Ue,bB as Se,bC as $e,z as A,bt as Fe,j as Ce,g as ce}from"./MapView-DaoQedLH.js";import{cl as M,R as g,aO as j,T as Me}from"./index-r0dFAfgr.js";import{l as P,w as Z,U as ze,k as Ve}from"./widget-BcWKanF2.js";import{m as ee}from"./multidimensionalUtils-BqWBjmd-.js";import{y as Be,m as Ge,h as Le}from"./RasterVFDisplayObject-C4NlpKNN.js";import{f as Ee,u as ke}from"./LayerView-BSt9B8Gh.js";import{n as he,m as De,r as Oe}from"./dataUtils-DovfQoP5.js";import{r as Ae}from"./Container-BwXq1a-x.js";import{L as W,M as qe,P as te,U as je,G as We,D as Ne,R as L,I as se,Y as Qe,V as He}from"./enums-BDQrMlcz.js";import{l as Je,c as Xe,_ as re,d as $,O as T,g as N,A as Ye,E as Ke,p as Ze,h as et,T as tt}from"./rasterUtils-Bn8ImO52.js";import{E as st}from"./Texture-BYqObwfn.js";import{o as S}from"./definitions-826PWLuy.js";import{t as rt,n as ie}from"./WGLContainer-Dyx9110G.js";import{x as it}from"./FramebufferObject-8j9PRuxE.js";import{r as de}from"./TiledDisplayObject-C5kAiJtw.js";import{T as k}from"./enums-L38xj_2E.js";import{i as me}from"./TileContainer-CC8_A7ZF.js";import{g as ae,f as ne,u as at,a as nt}from"./RawBlockCache-BtrwijFI.js";import{U as ot,i as lt,v as oe,J as ut}from"./rasterProjectionHelper-BvgFmUDx.js";import{r as pe}from"./util-DPgA-H2V.js";import{s as ct}from"./popupUtils-BjdidZV3.js";import{i as ht}from"./RefreshableLayerView-DUeNHzrW.js";import"./pe-B8dP0-Ut.js";import"./VertexElementDescriptor-BOD-G50G.js";import"./color-DAS1c3my.js";import"./enums-B5k73o5q.js";import"./number-CoJp78Rz.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./vec4f32-CjrfB-0a.js";import"./ProgramTemplate-tdUBoAol.js";import"./MaterialKey-BYd7cMLJ.js";import"./alignmentUtils-CkNI7z7C.js";import"./utils-DPUVnAXL.js";import"./StyleDefinition-Bnnz5uyC.js";import"./config-MDUrh2eL.js";import"./GeometryUtils-BRRfazic.js";import"./earcut-BJup91r2.js";const dt={bandCount:3,outMin:0,outMax:1,minCutOff:[0,0,0],maxCutOff:[255,255,255],factor:[1/255,1/255,1/255],useGamma:!1,gamma:[1,1,1],gammaCorrection:[1,1,1],colormap:null,colormapOffset:null,stretchType:"none",type:"stretch"};let mt=class extends Ae{constructor(e=null,t=null,r=null){super(),this._textureInvalidated=!0,this._colormapTextureInvalidated=!0,this._rasterTexture=null,this._rasterTextureBandIds=null,this._transformGridTexture=null,this._colormapTexture=null,this._colormap=null,this._supportsBilinearTexture=!0,this._processedTexture=null,this.functionTextures=[],this.projected=!1,this.stencilRef=0,this.coordScale=[1,1],this._processed=!1,this._symbolizerParameters=null,this.height=null,this.isRendereredSource=!1,this.pixelRatio=1,this.resolution=0,this.rotation=0,this._source=null,this.rawPixelData=null,this._suspended=!1,this._bandIds=null,this._interpolation=null,this._transformGrid=null,this.width=null,this.x=0,this.y=0,this.source=e,this.transformGrid=t,this.interpolation=r}destroy(){this._disposeTextures()}get processedTexture(){return this._processedTexture}set processedTexture(e){this._processedTexture!==e&&(this._disposeTextures(!0),this._processedTexture=e)}get rasterTexture(){return this._rasterTexture}set rasterTexture(e){var t;this._rasterTexture!==e&&((t=this._rasterTexture)==null||t.dispose(),this._rasterTexture=e)}get processed(){return this._processed}set processed(e){this._processed=e,e||(M(this.processedTexture),this.invalidateTexture())}get symbolizerParameters(){return this._symbolizerParameters||dt}set symbolizerParameters(e){this._symbolizerParameters!==e&&(this._symbolizerParameters=e,this._colormapTextureInvalidated=!0,this.commonUniforms=null)}get source(){return this._source}set source(e){this._source!==e&&(this._source=e,this._rasterTexture&&(this._rasterTexture.dispose(),this._rasterTexture=null,this._rasterTextureBandIds=null),this.projected=!1,this.invalidateTexture())}get suspended(){return this._suspended}set suspended(e){this._suspended&&!e&&this.stage&&(this.ready(),this.requestRender()),this._suspended=e}get bandIds(){return this._bandIds}set bandIds(e){this._bandIds=e,this._isBandIdschanged(e)&&(this.projected=!1,this.invalidateTexture())}get interpolation(){return this._interpolation||"nearest"}set interpolation(e){this._interpolation=e,this._rasterTexture&&this._rasterTexture.setSamplingMode(this._getTextureSamplingMethod(e||"nearest")==="bilinear"?W.LINEAR:W.NEAREST)}get transformGrid(){return this._transformGrid}set transformGrid(e){this._transformGrid=e,this._transformGridTexture=M(this._transformGridTexture)}invalidateTexture(){this._textureInvalidated||(this._textureInvalidated=!0,this.requestRender())}_createTransforms(){return{dvs:z()}}setTransform(e){const t=Te(this.transforms.dvs),[r,i]=e.toScreenNoRotation([0,0],[this.x,this.y]),a=this.resolution/this.pixelRatio/e.resolution,n=a*this.width,o=a*this.height,l=Math.PI*this.rotation/180;O(t,t,G(r,i)),O(t,t,G(n/2,o/2)),we(t,t,-l),O(t,t,G(-n/2,-o/2)),ve(t,t,G(n,o)),le(this.transforms.dvs,e.displayViewMat3,t)}getTextures({forProcessing:e=!1,useProcessedTexture:t=!1}={}){const r=t?this._processedTexture??this._rasterTexture:this._rasterTexture,i=[],a=[];return r?t?(a.push(r),i.push("u_image"),this._colormapTexture&&(a.push(this._colormapTexture),i.push("u_colormap")),{names:i,textures:a}):(this._transformGridTexture&&(a.push(this._transformGridTexture),i.push("u_transformGrid")),a.push(r),i.push("u_image"),this._colormapTexture&&!e&&(a.push(this._colormapTexture),i.push("u_colormap")),{names:i,textures:a}):{names:i,textures:a}}onAttach(){this.invalidateTexture()}onDetach(){this.invalidateTexture()}updateTexture({context:e}){if(!this.stage)return void this._disposeTextures();const t=this._isValidSource(this.source);t&&this._colormapTextureInvalidated&&(this._colormapTextureInvalidated=!1,this._updateColormapTexture(e)),this._textureInvalidated&&(this._textureInvalidated=!1,this._createOrDestroyRasterTexture(e),this._rasterTexture&&(t?this.transformGrid&&!this._transformGridTexture&&(this._transformGridTexture=Je(e,this.transformGrid)):this._rasterTexture.setData(null)),this.suspended||(this.ready(),this.requestRender()))}updateProcessedTexture(){const{functionTextures:e}=this;e.length!==0&&(this.processedTexture=e.shift(),e.forEach(t=>t==null?void 0:t.dispose()),e.length=0)}_createOrDestroyRasterTexture(e){var n,o;const t=g(this.source)?he(this.source,this.bandIds):null;if(!this._isValidSource(t))return void(this._rasterTexture&&(this._rasterTexture.dispose(),this._rasterTextureBandIds=null,this._rasterTexture=null));const r=!this._isBandIdschanged(this.bandIds);if(this._rasterTexture){if(r)return;this._rasterTexture.dispose(),this._rasterTextureBandIds=null,this._rasterTexture=null}this._supportsBilinearTexture=!!((n=e.capabilities.textureFloat)!=null&&n.textureFloatLinear);const i=this._getTextureSamplingMethod(this.interpolation),a=this.isRendereredSource||!((o=e.capabilities.textureFloat)!=null&&o.textureFloat);this._rasterTexture=Xe(e,t,i,a),this.projected=!1,this._processed=!1,this._rasterTextureBandIds=this.bandIds?[...this.bandIds]:null}_isBandIdschanged(e){const t=this._rasterTextureBandIds;return!(t==null&&e==null||t&&e&&t.join("")===e.join(""))}_isValidSource(e){var t;return g(e)&&((t=e.pixels)==null?void 0:t.length)>0}_getTextureSamplingMethod(e){const{type:t,colormap:r}=this.symbolizerParameters,i=t==="lut"||t==="stretch"&&g(r);return!this._supportsBilinearTexture||i||e!=="bilinear"&&e!=="cubic"?"nearest":"bilinear"}_updateColormapTexture(e){const t=this._colormap,r=this.symbolizerParameters.colormap;return r?t?r.length!==t.length||r.some((i,a)=>i!==t[a])?(this._colormapTexture&&(this._colormapTexture.dispose(),this._colormapTexture=null),this._colormapTexture=re(e,r),void(this._colormap=r)):void 0:(this._colormapTexture=re(e,r),void(this._colormap=r)):(this._colormapTexture&&(this._colormapTexture.dispose(),this._colormapTexture=null),void(this._colormap=null))}_disposeTextures(e=!1){this._transformGridTexture&&(this._transformGridTexture.dispose(),this._transformGridTexture=null),!e&&this._colormapTexture&&(this._colormapTexture.dispose(),this._colormapTexture=null,this._colormap=null,this._colormapTextureInvalidated=!0),!e&&this._rasterTexture&&(this._rasterTexture.dispose(),this._rasterTexture=null,this._rasterTextureBandIds=null),this._processedTexture&&(this._processedTexture.dispose(),this._processedTexture=null)}};function pt(s){return g(s.source)}function Q(s){const e=[];return s&&(e.push("applyProjection"),s.spacing[0]===1&&e.push("lookupProjection")),e}function fe(s,e,t){var a;const r=!((a=t.capabilities.textureFloat)!=null&&a.textureFloatLinear),i=[];return s==="cubic"?i.push("bicubic"):s==="bilinear"&&(e?(i.push("bilinear"),i.push("nnedge")):r&&i.push("bilinear")),i}const ft={vsPath:"raster/common",fsPath:"raster/lut",attributes:new Map([["a_position",0],["a_texcoord",1]])};function gt(s,e,t){const r=t?[]:Q(e.transformGrid);return{defines:r,program:s.painter.materialManager.getProgram(ft,r)}}function _t(s,e,t,r,i=!1){const{names:a,textures:n}=t.getTextures({useProcessedTexture:i});$(s.context,e,a,n),T(e,r,t.commonUniforms),e.setUniformMatrix3fv("u_dvsMat3",t.transforms.dvs);const{colormap:o,colormapOffset:l}=t.symbolizerParameters,u=N(o,l);T(e,r,u)}const yt={createProgram:gt,bindTextureAndUniforms:_t},xt={vsPath:"raster/common",fsPath:"raster/hillshade",attributes:new Map([["a_position",0],["a_texcoord",1]])};function bt(s,e,t){const{colormap:r}=e.symbolizerParameters,i=[...t?[]:Q(e.transformGrid),...fe(e.interpolation,r!=null,s.context)];return r!=null&&i.push("applyColormap"),{defines:i,program:s.painter.materialManager.getProgram(xt,i)}}function Tt(s,e,t,r,i=!1){const{names:a,textures:n}=t.getTextures({useProcessedTexture:i});$(s.context,e,a,n),T(e,r,t.commonUniforms),e.setUniformMatrix3fv("u_dvsMat3",t.transforms.dvs);const o=t.symbolizerParameters,{colormap:l,colormapOffset:u}=o;if(l!=null){const m=N(l,u);T(e,r,m)}const d=Ye(o);T(e,r,d)}const wt={createProgram:bt,bindTextureAndUniforms:Tt},vt={vsPath:"raster/common",fsPath:"raster/stretch",attributes:new Map([["a_position",0],["a_texcoord",1]])};function Pt(s,e,t){const{colormap:r}=e.symbolizerParameters,i=[...t?[]:Q(e.transformGrid),...fe(e.interpolation,r!=null,s.context)];return e.isRendereredSource&&!t?i.push("noop"):r!=null&&i.push("applyColormap"),{defines:i,program:s.painter.materialManager.getProgram(vt,i)}}function It(s,e,t,r,i=!1){const{names:a,textures:n}=t.getTextures({useProcessedTexture:i});$(s.context,e,a,n),T(e,r,t.commonUniforms),e.setUniformMatrix3fv("u_dvsMat3",t.transforms.dvs);const o=t.symbolizerParameters,{colormap:l,colormapOffset:u}=o;if(l!=null){const m=N(l,u);T(e,r,m)}const d=Ke(o);T(e,r,d)}const Rt={createProgram:Pt,bindTextureAndUniforms:It},E=new Map;function Ut(s){return E.get(s)}E.set("lut",yt),E.set("hillshade",wt),E.set("stretch",Rt);const St=[1,1],$t=[2,0,0,0,2,0,-1,-1,0];function b(s,e,t){const{context:r,rasterFunction:i,hasBranches:a}=s,{raster:n}=i.parameters,o=a?(n==null?void 0:n.id)??-1:0,l=t.functionTextures[o]??t.rasterTexture;$(r,e,["u_image"],[l])}function ge(s,e,t){const{rasters:r}=s.rasterFunction.parameters;if(!r)return;if(r.length<2)return b(s,e,t);const i=r.filter(a=>a.name!=="Constant").map(a=>a.id!=null&&a.name!=="Identity"?t.functionTextures[a.id]:t.rasterTexture);if($(s.context,e,["u_image","u_image1","u_image2"].slice(0,i.length),i),i.length!==r.length){if(r.length===2){const a=r.findIndex(l=>l.name==="Constant"),n=a===0?[0,1,0,1,0,0,0,0,0]:[1,0,0,0,1,0,0,0,0],{value:o}=r[a].parameters;e.setUniform1f("u_image1Const",o),e.setUniformMatrix3fv("u_imageSwap",n)}else if(r.length===3){const a=[];if(r.forEach((n,o)=>n.name==="Constant"&&a.push(o)),a.length===1){const{value:n}=r[a[0]].parameters;e.setUniform1f("u_image1Const",n);const o=a[0]===0?[0,1,0,0,0,1,1,0,0]:a[0]===1?[1,0,0,0,0,1,0,1,0]:[1,0,0,0,1,0,0,0,1];e.setUniformMatrix3fv("u_imageSwap",o)}else if(a.length===2){const{value:n}=r[a[0]].parameters;e.setUniform1f("u_image1Const",n);const{value:o}=r[a[1]].parameters;e.setUniform1f("u_image2Const",o);const l=r.findIndex(d=>d.name!=="Constant"),u=l===0?[1,0,0,0,1,0,0,0,1]:l===1?[0,1,0,1,0,0,0,0,1]:[0,0,1,1,0,0,0,1,0];e.setUniformMatrix3fv("u_imageSwap",u)}}}}function x(s){s.setUniform2fv("u_coordScale",St),s.setUniformMatrix3fv("u_dvsMat3",$t)}const Ft={vsPath:"raster/rfx/vs",fsPath:"raster/rfx/aspect",attributes:new Map([["a_position",0],["a_texcoord",1]])};function Ct(s,e){return s.painter.materialManager.getProgram(Ft,[])}function Mt(s,e,t){b(s,e,t),x(e);const{width:r,height:i,resolution:a}=t;e.setUniform2fv("u_srcImageSize",[r,i]),e.setUniform2fv("u_cellSize",[a,a])}const zt={createProgram:Ct,bindTextureAndUniforms:Mt},Vt={vsPath:"raster/rfx/vs",fsPath:"raster/rfx/bandarithmetic",attributes:new Map([["a_position",0],["a_texcoord",1]])};function Bt(s,e){const{painter:t,rasterFunction:r}=s,{indexType:i}=r.parameters;return t.materialManager.getProgram(Vt,[i])}function Gt(s,e,t){b(s,e,t),x(e);const{bandIndexMat3:r}=s.rasterFunction.parameters;e.setUniformMatrix3fv("u_bandIndexMat3",r)}const Lt={createProgram:Bt,bindTextureAndUniforms:Gt},Et={vsPath:"raster/rfx/vs",fsPath:"raster/rfx/compositeband",attributes:new Map([["a_position",0],["a_texcoord",1]])};function kt(s,e){return s.painter.materialManager.getProgram(Et,[])}function Dt(s,e,t){ge(s,e,t),x(e)}const Ot={createProgram:kt,bindTextureAndUniforms:Dt},At={vsPath:"raster/rfx/vs",fsPath:"raster/rfx/convolution",attributes:new Map([["a_position",0],["a_texcoord",1]])};function qt(s,e){const{painter:t,rasterFunction:r}=s,{kernelRows:i,kernelCols:a}=r.parameters,n=[{name:"rows",value:i},{name:"cols",value:a}];return t.materialManager.getProgram(At,n)}function jt(s,e,t){b(s,e,t),x(e),e.setUniform2fv("u_srcImageSize",[t.width,t.height]);const{kernel:r,clampRange:i}=s.rasterFunction.parameters;e.setUniform1fv("u_kernel",r),e.setUniform2fv("u_clampRange",i)}const Wt={createProgram:qt,bindTextureAndUniforms:jt},Nt={vsPath:"raster/rfx/vs",fsPath:"raster/rfx/extractband",attributes:new Map([["a_position",0],["a_texcoord",1]])};function Qt(s,e){return s.painter.materialManager.getProgram(Nt,[])}function Ht(s,e,t){b(s,e,t),x(e);const{bandIndexMat3:r}=s.rasterFunction.parameters;e.setUniformMatrix3fv("u_bandIndexMat3",r)}const Jt={createProgram:Qt,bindTextureAndUniforms:Ht},Xt={vsPath:"raster/rfx/vs",fsPath:"raster/rfx/local",attributes:new Map([["a_position",0],["a_texcoord",1]])},Yt=new Set(["sinh","cosh","tanh","asinh","acosh","atanh"]);function Kt(s){const{painter:e,rasterFunction:t}=s,{imageCount:r,operationName:i,rasters:a,isOutputRounded:n}=t.parameters;let o=i.toLowerCase();s.context.type===q.WEBGL1&&Yt.has(o)&&(o=`polyfill${o}`);const l=[o];r===2&&l.push("twoImages");const u=a.filter(d=>d.name==="Constant");return u.length&&(l.push("oneConstant"),u.length===2&&l.push("twoConstant")),n&&l.push("roundOutput"),e.materialManager.getProgram(Xt,l)}function Zt(s,e,t){ge(s,e,t),x(e);const{domainRange:r}=s.rasterFunction.parameters;e.setUniform2fv("u_domainRange",r)}const es={createProgram:Kt,bindTextureAndUniforms:Zt},ts={vsPath:"raster/rfx/vs",fsPath:"raster/rfx/mask",attributes:new Map([["a_position",0],["a_texcoord",1]])};function ss(s,e){const{painter:t,rasterFunction:r}=s,i=r.parameters.bandCount>1?["multiBand"]:[];return t.materialManager.getProgram(ts,i)}function rs(s,e,t){b(s,e,t),x(e);const{includedRanges:r,noDataValues:i}=s.rasterFunction.parameters;e.setUniform1fv("u_includedRanges",r),e.setUniform1fv("u_noDataValues",i)}const is={createProgram:ss,bindTextureAndUniforms:rs},as={vsPath:"raster/rfx/vs",fsPath:"raster/rfx/ndvi",attributes:new Map([["a_position",0],["a_texcoord",1]])};function ns(s,e){const{painter:t,rasterFunction:r}=s,i=r.parameters.scaled?["scaled"]:[];return t.materialManager.getProgram(as,i)}function os(s,e,t){b(s,e,t),x(e);const{bandIndexMat3:r}=s.rasterFunction.parameters;e.setUniformMatrix3fv("u_bandIndexMat3",r)}const ls={createProgram:ns,bindTextureAndUniforms:os},us={vsPath:"raster/rfx/vs",fsPath:"raster/rfx/remap",attributes:new Map([["a_position",0],["a_texcoord",1]])};function cs(s,e){return s.painter.materialManager.getProgram(us,[])}function hs(s,e,t){b(s,e,t),x(e);const{noDataRanges:r,rangeMaps:i,allowUnmatched:a,clampRange:n}=s.rasterFunction.parameters;e.setUniform1fv("u_noDataRanges",r),e.setUniform1fv("u_rangeMaps",i),e.setUniform1f("u_unmatchMask",a?1:0),e.setUniform2fv("u_clampRange",n)}const ds={createProgram:cs,bindTextureAndUniforms:hs},ms={vsPath:"raster/common",fsPath:"raster/reproject",attributes:new Map([["a_position",0],["a_texcoord",1]])};function ps(s,e){var o;const{painter:t}=s,r=[],i=!((o=s.context.capabilities.textureFloat)!=null&&o.textureFloatLinear),{interpolation:a,transformGrid:n}=e;return a==="cubic"?r.push("bicubic"):a==="bilinear"&&i&&r.push("bilinear"),n&&(r.push("applyProjection"),n.spacing[0]===1&&r.push("lookupProjection")),t.materialManager.getProgram(ms,r)}function fs(s,e,t){const{names:r,textures:i}=t.getTextures({forProcessing:!0});$(s.context,e,r,i),e.setUniform1f("u_scale",1),e.setUniform2fv("u_offset",[0,0]),e.setUniform2fv("u_coordScale",[1,1]),e.setUniformMatrix3fv("u_dvsMat3",[2,0,0,0,2,0,-1,-1,0]),e.setUniform1i("u_flipY",0),e.setUniform1f("u_opacity",1);const{width:a,height:n,source:o,transformGrid:l}=t;e.setUniform2fv("u_srcImageSize",[o.width,o.height]),e.setUniform2fv("u_targetImageSize",[a,n]),e.setUniform2fv("u_transformSpacing",l?l.spacing:Y),e.setUniform2fv("u_transformGridSize",l?l.size:Y)}const gs={createProgram:ps,bindTextureAndUniforms:fs},_s={vsPath:"raster/rfx/vs",fsPath:"raster/rfx/slope",attributes:new Map([["a_position",0],["a_texcoord",1]])};function ys(s,e){const{painter:t,rasterFunction:r}=s,{slopeType:i}=r.parameters,a=i==="percent-rise"?["percentRise"]:[];return t.materialManager.getProgram(_s,a)}function xs(s,e,t){b(s,e,t),x(e);const{width:r,height:i,resolution:a}=t,{zFactor:n,slopeType:o,pixelSizePower:l,pixelSizeFactor:u}=s.rasterFunction.parameters;e.setUniform2fv("u_srcImageSize",[r,i]),e.setUniform2fv("u_cellSize",[a,a]),e.setUniform1f("u_zFactor",n),e.setUniform1f("u_pixelSizePower",o==="adjusted"?l:0),e.setUniform1f("u_pixelSizeFactor",o==="adjusted"?u:0)}const bs={createProgram:ys,bindTextureAndUniforms:xs},Ts={vsPath:"raster/rfx/vs",fsPath:"raster/rfx/stretch",attributes:new Map([["a_position",0],["a_texcoord",1]])};function ws(s,e){const{useGamma:t,bandCount:r,isOutputRounded:i}=s.rasterFunction.parameters,a=[];return t&&a.push("useGamma"),r>1&&a.push("multiBand"),i&&a.push("roundOutput"),s.painter.materialManager.getProgram(Ts,a)}function vs(s,e,t){b(s,e,t),x(e);const{width:r,height:i}=t,a=s.rasterFunction.parameters;e.setUniform2fv("u_srcImageSize",[r,i]),e.setUniform1f("u_minOutput",a.outMin),e.setUniform1f("u_maxOutput",a.outMax),e.setUniform1fv("u_factor",a.factor),e.setUniform1fv("u_minCutOff",a.minCutOff),e.setUniform1fv("u_maxCutOff",a.maxCutOff),e.setUniform1fv("u_gamma",a.gamma),e.setUniform1fv("u_gammaCorrection",a.gammaCorrection)}const Ps={createProgram:ws,bindTextureAndUniforms:vs},y=new Map;function _e(s,e,t){const r={width:e,height:t,target:qe.TEXTURE_2D,pixelFormat:te.RGBA,internalFormat:s.type===q.WEBGL2?je.RGBA32F:te.RGBA,samplingMode:W.NEAREST,dataType:We.FLOAT,isImmutable:s.type===q.WEBGL2,wrapMode:Ne.CLAMP_TO_EDGE,flipped:!1};return new st(s,r)}function Is(s,e,t,r){const{context:i,requestRender:a,allowDelayedRender:n}=s,o=r.createProgram(s,t);if(n&&g(a)&&!o.compiled)return a(),null;const{width:l,height:u}=t;return i.bindFramebuffer(e),i.setViewport(0,0,l,u),i.useProgram(o),o}function Rs(s){return y.get(s.toLowerCase())}function Us(s,e,t,r){const i=s.rasterFunction.name.toLowerCase(),a=i==="reproject"?gs:Rs(i);if(a==null)return;const n=Is(s,t,r,a);if(!n)return;a.bindTextureAndUniforms(s,n,r),e.draw();const{width:o,height:l}=r,u=_e(s.context,o,l);if(t.copyToTexture(0,0,o,l,0,0,u),i==="reproject")r.rasterTexture=u,r.projected=!0;else{const d=s.hasBranches?s.rasterFunction.id:0;r.functionTextures[d]=u}}y.set("aspect",zt),y.set("bandarithmetic",Lt),y.set("compositeband",Ot),y.set("convolution",Wt),y.set("extractband",Jt),y.set("local",es),y.set("mask",is),y.set("ndvi",ls),y.set("remap",ds),y.set("slope",bs),y.set("stretch",Ps);class Ss extends rt{constructor(){super(...arguments),this.name="raster",this._quad=null,this._rendererUniformInfos=new Map,this._fbo=null}dispose(){M(this._quad),M(this._fbo)}prepareState(e){const{context:t,renderPass:r}=e,i=r==="raster";t.setBlendingEnabled(!i),t.setBlendFunctionSeparate(L.ONE,L.ONE_MINUS_SRC_ALPHA,L.ONE,L.ONE_MINUS_SRC_ALPHA),t.setColorMask(!0,!0,!0,!0),t.setStencilWriteMask(0),t.setStencilTestEnabled(!i)}draw(e,t){if(!pt(t)||t.suspended)return;const{renderPass:r}=e;if(r!=="raster-bitmap")return r==="raster"?this._process(e,t):void this._drawBitmap(e,t,!0);this._drawBitmap(e,t)}_process(e,t){const{rasterFunction:r}=e,i=r.name==="Reproject";if(!(i?!(t.rasterTexture&&t.projected):!t.processed))return;const{timeline:a,context:n}=e;a.begin(this.name);const o=n.getBoundFramebufferObject(),l=n.getViewport();i||(t.processedTexture=M(t.processedTexture)),n.setStencilFunction(se.EQUAL,t.stencilRef,255),t.updateTexture(e),this._initQuad(n);const{isStandardRasterTileSize:u,fbo:d}=this._getRasterFBO(n,t.width,t.height);Us(e,this._quad,d,t),u||d.dispose(),n.bindFramebuffer(o),n.setViewport(l.x,l.y,l.width,l.height),a.end(this.name)}_drawBitmap(e,t,r=!1){const{timeline:i,context:a}=e;if(i.begin(this.name),a.setStencilFunction(se.EQUAL,t.stencilRef,255),t.updateTexture(e),r&&!t.processedTexture){if(t.updateProcessedTexture(),!t.processedTexture)return void i.end(this.name);t.processed=!0}this._initBitmapCommonUniforms(t);const n=t.symbolizerParameters.type,o=Ut(n),{requestRender:l,allowDelayedRender:u}=e,{defines:d,program:m}=o.createProgram(e,t,r);if(u&&g(l)&&!m.compiled)return void l();a.useProgram(m);const c=this._getUniformInfos(n,a,m,d);this._quad||(this._quad=new ie(a,[0,0,1,0,0,1,1,1])),o.bindTextureAndUniforms(e,m,t,c,r),this._quad.draw(),i.end(this.name)}_initBitmapCommonUniforms(e){if(!e.commonUniforms){const t=tt(1,[0,0]),{transformGrid:r,width:i,height:a}=e,n=Ze(r,[i,a],[e.source.width,e.source.height],1,!1);e.commonUniforms={...t,...n,u_coordScale:e.coordScale}}}_getRasterFBO(e,t,r){const i=t===S||r===S;return i?(this._fbo||(this._fbo=this._createNewFBO(e,t,r)),{isStandardRasterTileSize:i,fbo:this._fbo}):{isStandardRasterTileSize:i,fbo:this._createNewFBO(e,t,r)}}_createNewFBO(e,t,r){const i=_e(e,t,r);return new it(e,{colorTarget:Qe.TEXTURE,depthStencilTarget:He.NONE,width:t,height:r},i)}_initQuad(e){this._quad||(this._quad=new ie(e,[0,0,1,0,0,1,1,1]))}_getUniformInfos(e,t,r,i){const a=i.length>0?e+"-"+i.join("-"):e;if(this._rendererUniformInfos.has(a))return this._rendererUniformInfos.get(a);const n=et(t,r);return this._rendererUniformInfos.set(a,n),n}}class $s extends de{constructor(e,t,r,i,a,n,o=null){super(e,t,r,i,a,n),this.bitmap=null,this.bitmap=new mt(o,null,null),this.bitmap.coordScale=[a,n],this.bitmap.once("isReady",()=>this.ready())}destroy(){super.destroy(),this.bitmap.destroy(),this.bitmap=null,this.stage=null}set stencilRef(e){this.bitmap.stencilRef=e}get stencilRef(){return this.bitmap.stencilRef}setTransform(e){super.setTransform(e),this.bitmap.transforms.dvs=this.transforms.dvs}_createTransforms(){return{dvs:z(),tileMat3:z()}}onAttach(){this.bitmap.stage=this.stage}onDetach(){this.bitmap.stage=null}}let Fs=class extends me{constructor(){super(...arguments),this.isCustomTilingScheme=!1}createTile(e){const t=this._getTileBounds(e),[r,i]=this._tileInfoView.tileInfo.size,a=this._tileInfoView.getTileResolution(e.level);return new $s(e,a,t[0],t[3],r,i)}prepareRenderPasses(e){const t=e.registerRenderPass({name:"imagery (tile)",brushes:[Ss],target:()=>this.children.map(r=>r.bitmap),drawPhase:k.MAP});return[...super.prepareRenderPasses(e),t]}doRender(e){if(!this.visible||e.drawPhase!==k.MAP)return;const{rasterFunctionChain:t}=this;if(!t)return e.renderPass="raster-bitmap",void super.doRender(e);const[r,i]=this._tileInfoView.tileInfo.size;if(e.renderPass="raster",e.rasterFunction={name:"Reproject",parameters:{targetImageSize:[r,i]},pixelType:"f32",id:0,isNoopProcess:!1},super.doRender(e),t==null?void 0:t.functions.length){const{functions:a,hasBranches:n}=t;for(let o=0;o<a.length;o++){const l=a[o];l.name!=="Constant"&&l.name!=="Identity"&&(e.renderPass="raster",e.rasterFunction=l,e.hasBranches=n,super.doRender(e))}}e.rasterFunction=null,e.renderPass="bitmap",super.doRender(e)}_getTileBounds(e){const t=this._tileInfoView.getTileBounds(ue(),e);if(this.isCustomTilingScheme&&e.world){const{tileInfo:r}=this._tileInfoView,i=Pe(r.spatialReference);if(i){const a=r.lodAt(e.level);if(!a)return t;const{resolution:n}=a,o=i/n%r.size[0],l=o?(r.size[0]-o)*n:0;t[0]-=l*e.world,t[2]-=l*e.world}}return t}};const Cs=[0,0];let _=class extends Ie{constructor(){super(...arguments),this._emptyTilePixelBlock=null,this._tileStrategy=null,this._tileInfoView=null,this._fetchQueue=null,this._blockCacheRegistryUrl=null,this._blockCacheRegistryId=null,this._srcResolutions=[],this.previousLOD=null,this._needBlockCacheUpdate=!1,this._globalSymbolizerParams=null,this._symbolizerParams=null,this._abortController=null,this._isCustomTilingScheme=!1,this._rasterFunctionState="na",this._globalUpdateRequested=!1,this.attached=!1,this.timeExtent=null,this.redrawOrRefetch=xe(async(s={})=>{if(!this.previousLOD||this.layerView.suspended)return;const e=this._rasterFunctionState;s.reprocess&&(await this.updatingHandles.addPromise(this.layer.updateRasterFunction()),this.updateRasterFunctionParameters());const t=this._rasterFunctionState,{type:r}=this;return s.refetch||r!=="raster"&&s.reprocess||t==="cpu"||e==="cpu"?this.updatingHandles.addPromise(this.doRefresh()):this.updatingHandles.addPromise(this._redrawImage(s.signal))})}get useWebGLForProcessing(){return this._get("useWebGLForProcessing")??!0}set useWebGLForProcessing(s){this._set("useWebGLForProcessing",s)}get useProgressiveUpdate(){return this._get("useProgressiveUpdate")==null||this._get("useProgressiveUpdate")}set useProgressiveUpdate(s){if(this._tileStrategy&&this.useProgressiveUpdate!==s){this._tileStrategy.destroy(),this.container.removeAllChildren();const e=this._getCacheSize(s);this._tileStrategy=new K({cachePolicy:"purge",acquireTile:t=>this.acquireTile(t),releaseTile:t=>this.releaseTile(t),cacheSize:e,tileInfoView:this._tileInfoView}),this._set("useProgressiveUpdate",s),this.layerView.requestUpdate()}}update(s){var a;this._fetchQueue.pause(),this._fetchQueue.state=s.state,this._tileStrategy.update(s),this._fetchQueue.resume();const{extent:e,resolution:t,scale:r}=s.state,i=this._tileInfoView.getClosestInfoForScale(r);if(this.layer.raster){if(!this.useProgressiveUpdate||this._needBlockCacheUpdate){const n=this._srcResolutions[i.level],o=e.toJSON?e:Re.fromJSON(e);ae(this._blockCacheRegistryUrl,this._blockCacheRegistryId,o,t,n,this.layer.raster.ioConfig.sampling)}this._needBlockCacheUpdate=!1,((a=this.previousLOD)==null?void 0:a.level)!==i.level&&(this.previousLOD=i,this._symbolizerParams==null||this.layerView.hasTilingEffects||this._updateSymbolizerParams(),this._tileStrategy.updateCacheSize(0))}}moveEnd(){!this.layerView.hasTilingEffects&&this.useProgressiveUpdate||(this._abortController&&this._abortController.abort(),this._abortController=new AbortController,this._fetchQueue.length===0&&this._redrawImage(this._abortController.signal).then(()=>{this._globalUpdateRequested=!1,this.layerView.requestUpdate()}));const s=this._getCacheSize(this.useProgressiveUpdate);this._tileStrategy.updateCacheSize(s),this.layerView.requestUpdate()}get updating(){var s;return((s=this._fetchQueue)==null?void 0:s.updating)||this._globalUpdateRequested||!(!this.updatingHandles||!this.updatingHandles.updating)}attach(){Ue("2d").supportsTextureFloat||(this.useWebGLForProcessing=!1),this._initializeTileInfo(),this._tileInfoView=new Se(this.layerView.tileInfo,this.layerView.fullExtent);const s=this._computeFetchConcurrency();this._fetchQueue=new $e({tileInfoView:this._tileInfoView,concurrency:s,process:(t,r)=>this._fetchTile1(t,r)});const e=this._getCacheSize(this.useProgressiveUpdate);this._tileStrategy=new K({cachePolicy:"purge",acquireTile:t=>this.acquireTile(t),releaseTile:t=>this.releaseTile(t),cacheSize:e,tileInfoView:this._tileInfoView}),this._updateBlockCacheRegistry()}detach(){this._tileStrategy.destroy(),this._fetchQueue.clear(),this.container.removeAllChildren(),this._fetchQueue=this._tileStrategy=this._tileInfoView=null,ne(this._blockCacheRegistryUrl,this._blockCacheRegistryId),this._blockCacheRegistryUrl=this._blockCacheRegistryId=null}acquireTile(s){const e=this.container.createTile(s);return this._enqueueTileFetch(e),this.layerView.requestUpdate(),this._needBlockCacheUpdate=!0,this._globalUpdateRequested=this.layerView.hasTilingEffects||!this.useProgressiveUpdate,e}releaseTile(s){this._fetchQueue.abort(s.key.id),this.container.removeChild(s),s.once("detach",()=>{s.destroy(),this.layerView.requestUpdate()}),this.layerView.requestUpdate()}createEmptyTilePixelBlock(s=null){const e=s==null||s.join(",")===this._tileInfoView.tileInfo.size.join(",");if(e&&g(this._emptyTilePixelBlock))return this._emptyTilePixelBlock;s=s||this._tileInfoView.tileInfo.size;const[t,r]=s,i=new De({width:t,height:r,pixels:[new Uint8Array(t*r)],mask:new Uint8Array(t*r),pixelType:"u8"});return e&&(this._emptyTilePixelBlock=i),i}_getBandIds(){if(!("rasterFunctionChain"in this.container)||!this.container.rasterFunctionChain)return this.layer.bandIds;const{bandIds:s,raster:e}=this.layer,t="rasterFunction"in e?e.rasterFunction.rawInputBandIds:null;return s!=null&&s.length&&(t!=null&&t.length)&&e.rasterInfo.bandCount!==1?s.map(r=>t[Math.min(r,t.length-1)]):s||t}updateRasterFunctionParameters(){}_fetchTile1(s,e){const t=g(e)?e.signal:null,r=this.canUseWebGLForProcessing(),{layerView:i}=this,{tileInfo:a}=i,n=!a.isWrappable&&g(ot(i.view.spatialReference)),o=r&&this.layer.raster.hasUniqueSourceStorageInfo,l={allowPartialFill:!0,datumTransformation:i.datumTransformation,interpolation:r?"nearest":this.layer.interpolation,registryId:this._blockCacheRegistryId,requestRawData:o,skipRasterFunction:this.type==="raster"&&this.container.rasterFunctionChain!=null,signal:j(t),srcResolution:this._srcResolutions[s.level],timeExtent:i.timeExtent,tileInfo:a,disableWrapAround:n};return this.fetchTile(s,l)}_getCacheSize(s){return s?40:0}_initializeTileInfo(){const{layerView:s}=this,e=s.view.spatialReference,t=new be({x:s.fullExtent.xmin,y:s.fullExtent.ymax,spatialReference:e});if(this._canUseLayerLODs()){const{lods:o}=this.layer.tileInfo,l=o.map(({scale:m})=>m),u=A.create({spatialReference:e,size:S,scales:l}),d=e.isGeographic?.01*1e-5:.01;if(this._isCustomTilingScheme=Math.abs(u.origin.x-t.x)>d,(u.origin.x===0||u.origin.x>t.x)&&(u.origin=t),!this._isCustomTilingScheme){const m=A.create({spatialReference:e,size:S}).lods.map(({scale:c})=>c);this._isCustomTilingScheme=l.some(c=>!m.some(f=>Math.abs(f-c)<.001))}return s.set("tileInfo",u),void(this._srcResolutions=o.map(({resolution:m})=>({x:m,y:m})))}const{scales:r,srcResolutions:i,isCustomTilingScheme:a}=lt(this.layer.rasterInfo,e,S),n=A.create({spatialReference:e,size:S,scales:r});(n.origin.x===0||n.origin.x>t.x)&&(n.origin=t),this._isCustomTilingScheme=a,s.set("tileInfo",n),this._srcResolutions=i??[]}_canUseLayerLODs(){var a;const{layer:s,layerView:e}=this;if(s.raster.tileType!=="Map")return!1;const{lods:t}=s.tileInfo,r=(a=e.view.constraints)==null?void 0:a.effectiveLODs;if(!((r==null?void 0:r.length)===t.length&&r.every(({scale:n},o)=>Math.abs(n-t[o].scale)<.001)))return!1;const i=[];for(let n=0;n<t.length-1;n++)i.push(Math.round(10*t[n].resolution/t[n+1].resolution)/10);return i.some(n=>n!==n[0])}_computeFetchConcurrency(){const{blockBoundary:s}=this.layer.rasterInfo.storageInfo,e=s[s.length-1];return(e.maxCol-e.minCol+1)*(e.maxRow-e.minRow+1)>64?2:10}async _enqueueTileFetch(s,e){this.updatingHandles.addPromise(this._enqueueTileFetch1(s,e))}async _enqueueTileFetch1(s,e){var t;if(!this._fetchQueue.has(s.key.id)){try{const r=await this._fetchQueue.push(s.key),i=this._getBandIds();let a=!this.useProgressiveUpdate||this.layerView.hasTilingEffects&&!this._globalSymbolizerParams;if(this._globalUpdateRequested&&!this.layerView.moving&&this._fetchQueue.length===0){a=!1;try{await this._redrawImage((t=this._abortController)==null?void 0:t.signal)}catch(l){F(l)&&C.getLogger(this.declaredClass).error(l)}this._globalUpdateRequested=!1}!this.canUseWebGLForProcessing()&&this.type!=="rasterVF"||this.layerView.hasTilingEffects||this._symbolizerParams!=null||this._updateSymbolizerParams();const n=this._tileInfoView.getTileCoords(Cs,s.key),o=this._tileInfoView.getTileResolution(s.key);await this.updateTileSource(s,{source:r,symbolizerParams:this._symbolizerParams,globalSymbolizerParams:this._globalSymbolizerParams,suspended:a,bandIds:i,coords:n,resolution:o}),s.once("attach",()=>this.layerView.requestUpdate()),this.container.addChild(s)}catch(r){F(r)||C.getLogger(this.declaredClass).error(r)}this.layerView.requestUpdate()}}async _redrawImage(s){if(this.container.children.length===0)return;await this.layer.updateRenderer(),this.layerView.hasTilingEffects?await this._updateGlobalSymbolizerParams(s):(this._updateSymbolizerParams(),this._globalSymbolizerParams=null);const e=this.container.children.map(async t=>this.updateTileSymbolizerParameters(t,{local:this._symbolizerParams,global:this._globalSymbolizerParams}));await J(e),this.container.requestRender()}async _updateGlobalSymbolizerParams(s){const e={srcResolution:this._srcResolutions[this.previousLOD.level],registryId:this._blockCacheRegistryId,signal:s},t=await this.layer.fetchPixels(this.layerView.view.extent,this.layerView.view.width,this.layerView.view.height,e);if(!t||!t.pixelBlock)return;const{resolution:r}=this.previousLOD,i=this._getBandIds(),a=this.layer.symbolizer.generateWebGLParameters({pixelBlock:he(t.pixelBlock,i),isGCS:this.layerView.view.spatialReference.isGeographic,resolution:{x:r,y:r},bandIds:i});!this.canUseWebGLForProcessing()&&a&&a.type==="stretch"&&this.layer.renderer&&this.layer.renderer.type==="raster-stretch"&&(a.factor=a.factor.map(n=>255*n),a.outMin=Math.round(255*a.outMin),a.outMax=Math.round(255*a.outMax)),this._globalSymbolizerParams=a}_updateSymbolizerParams(){const{resolution:s}=this.previousLOD,e=this._getBandIds();this._symbolizerParams=this.layer.symbolizer.generateWebGLParameters({pixelBlock:null,isGCS:this.layerView.view.spatialReference.isGeographic,resolution:{x:s,y:s},bandIds:e})}_updateBlockCacheRegistry(s=!1){const{layer:e,layerView:t}=this,{url:r,raster:i}=e,{multidimensionalDefinition:a}=e.normalizeRasterFetchOptions({multidimensionalDefinition:e.multidimensionalDefinition,timeExtent:t.timeExtent}),n=i.rasterInfo.multidimensionalInfo?i.getSliceIndex(a):null,o=nt(r,n);if(o!==this._blockCacheRegistryUrl){if(this._blockCacheRegistryUrl!=null&&ne(this._blockCacheRegistryUrl,this._blockCacheRegistryId),this._blockCacheRegistryId=at(o,i.rasterInfo),s){const{view:l}=t,u=this._tileInfoView.getClosestInfoForScale(l.scale),d=this._srcResolutions[u.level];ae(o,this._blockCacheRegistryId,l.extent,l.resolution,d,i.ioConfig.sampling)}this._blockCacheRegistryUrl=o}}async doRefresh(){if(!this.attached)return;await this.layer.updateRenderer(),this.layerView.hasTilingEffects||this._updateSymbolizerParams(),this._updateBlockCacheRegistry(!0),this._fetchQueue.reset();const s=[];this._globalUpdateRequested=this.layerView.hasTilingEffects||!this.useProgressiveUpdate,this._tileStrategy.tiles.forEach(e=>s.push(this._enqueueTileFetch(e))),await J(s)}};h([p()],_.prototype,"_fetchQueue",void 0),h([p()],_.prototype,"_globalUpdateRequested",void 0),h([p()],_.prototype,"attached",void 0),h([p()],_.prototype,"container",void 0),h([p()],_.prototype,"layer",void 0),h([p()],_.prototype,"layerView",void 0),h([p()],_.prototype,"type",void 0),h([p()],_.prototype,"useWebGLForProcessing",null),h([p()],_.prototype,"useProgressiveUpdate",null),h([p()],_.prototype,"timeExtent",void 0),h([p()],_.prototype,"updating",null),_=h([V("esri.views.2d.layers.imagery.BaseImageryTileSubView2D")],_);let I=class extends _{constructor(){super(...arguments),this.type="raster"}attach(){super.attach(),this.container=new Fs(this._tileInfoView),this.container.isCustomTilingScheme=this._isCustomTilingScheme,this.updateRasterFunctionParameters()}detach(){super.detach(),this.container.removeAllChildren(),this.container=null}canUseWebGLForProcessing(){return this.useWebGLForProcessing&&this.layer.symbolizer.canRenderInWebGL&&!(this.layer.interpolation==="majority"&&pe(this.layer))}fetchTile(e,t){return this.layer.fetchTile(e.level,e.row,e.col,t)}updateRasterFunctionParameters(){const{raster:e,type:t}=this.layer,{container:r}=this;if(e.datasetFormat!=="Function"||t==="wcs")return r.rasterFunctionChain=null,r.children.forEach(c=>{const{bitmap:f}=c;f&&(f.suspended=!0,f.processed=!1,f.projected&&(f.invalidateTexture(),f.rasterTexture=null))}),void(this._rasterFunctionState="na");const i=this._rasterFunctionState,{rasterFunction:a,primaryRasters:n}=e,o=a.supportsGPU&&(!n||n.rasters.length<=1),l=o?a.getFlatWebGLFunctionChain():null,{renderer:u}=this.layer,d=!o||!(l!=null&&l.functions.length)||u.type==="raster-stretch"&&u.dynamicRangeAdjustment||!this.canUseWebGLForProcessing();r.rasterFunctionChain=d?null:l;const m=a==null?"na":r.rasterFunctionChain?"gpu":"cpu";r.children.forEach(c=>{const{bitmap:f}=c;f&&(f.suspended=i!==m,f.processed=!1,f.processedTexture=null)}),this._rasterFunctionState=m}async updateTileSource(e,t){const r=this._getBandIds(),i=this._getLayerInterpolation(),a=this.canUseWebGLForProcessing(),{source:n,globalSymbolizerParams:o,suspended:l,coords:u,resolution:d}=t,m=this.layerView.hasTilingEffects?o:t.symbolizerParams,{bitmap:c}=e;if([c.x,c.y]=u,c.resolution=d,n&&g(n)&&g(n.pixelBlock)){const f={extent:n.extent,pixelBlock:n.pixelBlock};if(c.rawPixelData=f,a)c.source=n.pixelBlock,c.isRendereredSource=!1;else{const v=await this.layer.applyRenderer(f,(o==null?void 0:o.type)==="stretch"?o:void 0);c.source=v,c.isRendereredSource=!0}c.symbolizerParameters=a?m:null,a?c.transformGrid||(c.transformGrid=n.transformGrid):c.transformGrid=null}else{const f=this.createEmptyTilePixelBlock();c.source=f,c.symbolizerParameters=a?m:null,c.transformGrid=null}c.bandIds=a?r:null,c.width=this._tileInfoView.tileInfo.size[0],c.height=this._tileInfoView.tileInfo.size[1],c.interpolation=i,c.suspended=l,c.invalidateTexture()}async updateTileSymbolizerParameters(e,t){const{local:r,global:i}=t,a=this._getBandIds(),n=this._getLayerInterpolation(),o=this.canUseWebGLForProcessing(),{bitmap:l}=e,{rawPixelData:u}=l;!o&&g(u)?(l.source=await this.layer.applyRenderer(u,(i==null?void 0:i.type)==="stretch"?i:void 0),l.isRendereredSource=!0):(l.isRendereredSource&&g(u)&&(l.source=u.pixelBlock),l.isRendereredSource=!1),l.symbolizerParameters=o?this.layerView.hasTilingEffects?i:r:null,l.bandIds=o?a:null,l.interpolation=n,l.suspended=!1}_getLayerInterpolation(){const e=this.layer.renderer.type;if(e==="raster-colormap"||e==="unique-value"||e==="class-breaks")return"nearest";const{interpolation:t}=this.layer,{renderer:r}=this.layer;return r.type==="raster-stretch"&&r.colorRamp!=null?t==="bilinear"||t==="cubic"?"bilinear":"nearest":t}};h([p()],I.prototype,"container",void 0),h([p()],I.prototype,"layer",void 0),h([p()],I.prototype,"type",void 0),I=h([V("esri.views.2d.layers.imagery.ImageryTileView2D")],I);const Ms=I;class zs extends de{constructor(e,t,r,i,a,n,o=null){super(e,t,r,i,a,n),this.tileData=new Be(o),this.tileData.coordScale=[a,n],this.tileData.once("isReady",()=>this.ready())}destroy(){super.destroy(),this.tileData.destroy(),this.tileData=null,this.stage=null}set stencilRef(e){this.tileData.stencilRef=e}get stencilRef(){return this.tileData.stencilRef}_createTransforms(){return{dvs:z(),tileMat3:z()}}setTransform(e){super.setTransform(e);const t=this.resolution/(e.resolution*e.pixelRatio),r=this.transforms.tileMat3,[i,a]=this.tileData.offset,n=[this.x+i*this.resolution,this.y-a*this.resolution],[o,l]=e.toScreenNoRotation([0,0],n),{symbolTileSize:u}=this.tileData.symbolizerParameters,d=Math.round((this.width-this.tileData.offset[0])/u)*u,m=Math.round((this.height-this.tileData.offset[1])/u)*u,c=d/this.rangeX*t,f=m/this.rangeY*t;Fe(r,c,0,0,0,f,0,o,l,1),le(this.transforms.dvs,e.displayViewMat3,r),this.tileData.transforms.dvs=this.transforms.dvs}onAttach(){this.tileData.stage=this.stage}onDetach(){this.tileData.stage=null}}class Vs extends me{constructor(){super(...arguments),this.isCustomTilingScheme=!1,this.symbolTypes=["triangle"]}createTile(e){const t=this._tileInfoView.getTileBounds(ue(),e),[r,i]=this._tileInfoView.tileInfo.size,a=this._tileInfoView.getTileResolution(e.level);return new zs(e,a,t[0],t[3],r,i)}prepareRenderPasses(e){const t=e.registerRenderPass({name:"imagery (vf tile)",brushes:[Ge],target:()=>this.children.map(r=>r.tileData),drawPhase:k.MAP});return[...super.prepareRenderPasses(e),t]}doRender(e){this.visible&&e.drawPhase===k.MAP&&this.symbolTypes.forEach(t=>{e.renderPass=t,super.doRender(e)})}}let R=class extends _{constructor(){super(...arguments),this._handle=null,this.type="rasterVF"}canUseWebGLForProcessing(){return!1}async fetchTile(s,e){e={...e,interpolation:"nearest",requestProjectedLocalDirections:!0};const t=await this.layer.fetchTile(s.level,s.row,s.col,e);return this.layer.rasterInfo.dataType==="vector-magdir"&&(t!=null&&t.pixelBlock)&&(t.pixelBlock=await this.layer.convertVectorFieldData(t.pixelBlock,e)),t}updateTileSource(s,e){const t=e.symbolizerParams,{tileData:r}=s;r.key=s.key,r.width=this._tileInfoView.tileInfo.size[0],r.height=this._tileInfoView.tileInfo.size[1];const{symbolTileSize:i}=t,{source:a}=e;if(r.offset=this._getTileSymbolOffset(r.key,i),g(a)&&g(a.pixelBlock)){const n={extent:a.extent,pixelBlock:a.pixelBlock};r.rawPixelData=n,r.symbolizerParameters=t,r.source=this._sampleVectorFieldData(a.pixelBlock,t,r.offset)}else{const n=[Math.round((this._tileInfoView.tileInfo[0]-r.offset[0])/i),Math.round((this._tileInfoView.tileInfo[1]-r.offset[1])/i)],o=this.createEmptyTilePixelBlock(n);r.source=o,r.symbolizerParameters=t}return r.invalidateVAO(),Promise.resolve()}updateTileSymbolizerParameters(s,e){var o;const t=e.local,{symbolTileSize:r}=t,{tileData:i}=s;i.offset=this._getTileSymbolOffset(i.key,r);const a=i.symbolizerParameters.symbolTileSize;i.symbolizerParameters=t;const n=(o=i.rawPixelData)==null?void 0:o.pixelBlock;return g(n)&&a!==r&&(i.source=this._sampleVectorFieldData(n,i.symbolizerParameters,i.offset)),Promise.resolve()}attach(){super.attach(),this.container=new Vs(this._tileInfoView),this.container.isCustomTilingScheme=this._isCustomTilingScheme,this._updateSymbolType(this.layer.renderer),this._handle=P(()=>this.layer.renderer,s=>this._updateSymbolType(s))}detach(){var s;super.detach(),this.container.removeAllChildren(),(s=this._handle)==null||s.remove(),this._handle=null,this.container=null}_getTileSymbolOffset(s,e){const t=s.col*this._tileInfoView.tileInfo.size[0]%e,r=s.row*this._tileInfoView.tileInfo.size[1]%e;return[t>e/2?e-t:-t,r>e/2?e-r:-r]}_sampleVectorFieldData(s,e,t){const{symbolTileSize:r}=e;return Oe(s,"vector-uv",r,t)}_updateSymbolType(s){s.type==="vector-field"&&(this.container.symbolTypes=s.style==="wind-barb"?["scalar","triangle"]:s.style==="simple-scalar"?["scalar"]:["triangle"])}};h([p()],R.prototype,"container",void 0),h([p()],R.prototype,"layer",void 0),h([p()],R.prototype,"type",void 0),R=h([V("esri.views.2d.layers.imagery.VectorFieldTileView2D")],R);const Bs=R,Gs=s=>{let e=class extends s{constructor(){super(...arguments),this._rasterFieldPrefix="Raster.",this.layer=null,this.view=null,this.tileInfo=null}get fullExtent(){return this._getfullExtent()}_getfullExtent(){return this.projectFullExtent(this.view.spatialReference)}get hasTilingEffects(){return this.layer.renderer&&"dynamicRangeAdjustment"in this.layer.renderer&&this.layer.renderer.dynamicRangeAdjustment}get datumTransformation(){return oe(j(this.layer.fullExtent),this.view.spatialReference,!0)}supportsSpatialReference(t){return!!this.projectFullExtent(t)}projectFullExtent(t){const r=j(this.layer.fullExtent),i=oe(r,t,!1);return ut(r,t,i)}async fetchPopupFeatures(t,r){const{layer:i}=this;if(!t)throw new X("imageryTileLayerView:fetchPopupFeatures","Nothing to fetch without area",{layer:i});const{popupEnabled:a}=i,n=ct(i,r);if(!a||Me(n))throw new X("imageryTileLayerView:fetchPopupFeatures","Missing required popupTemplate or popupEnabled",{popupEnabled:a,popupTemplate:n});const o=[],{value:l,magdirValue:u}=await i.identify(t,{timeExtent:this.timeExtent});let d="";if(l&&l.length){d=i.type==="imagery-tile"&&i.hasStandardTime()&&l[0]!=null?l.map(D=>i.getStandardTimeValue(D)).join(", "):l.join(", ");const m={ObjectId:0};m["Raster.ServicePixelValue"]=d;const c=i.rasterInfo.attributeTable;if(g(c)){const{fields:D,features:ye}=c,H=D.find(({name:w})=>w.toLowerCase()==="value"),B=H?ye.find(w=>String(w.attributes[H.name])===d):null;if(B)for(const w in B.attributes)B.attributes.hasOwnProperty(w)&&(m[this._rasterFieldPrefix+w]=B.attributes[w])}const f=i.rasterInfo.dataType;f!=="vector-magdir"&&f!=="vector-uv"||(m["Raster.Magnitude"]=u==null?void 0:u[0],m["Raster.Direction"]=u==null?void 0:u[1]);const v=new ce(this.fullExtent.clone(),null,m);v.layer=i,v.sourceLayer=v.layer,o.push(v)}return o}};return h([p()],e.prototype,"layer",void 0),h([p(Ce)],e.prototype,"timeExtent",void 0),h([p()],e.prototype,"view",void 0),h([p()],e.prototype,"fullExtent",null),h([p()],e.prototype,"tileInfo",void 0),h([p({readOnly:!0})],e.prototype,"hasTilingEffects",null),h([p()],e.prototype,"datumTransformation",null),e=h([V("esri.views.layers.ImageryTileLayerView")],e),e};let U=class extends Gs(ht(Ee(ke))){constructor(){super(...arguments),this._useWebGLForProcessing=!0,this._useProgressiveUpdate=!0,this.subview=null}get useWebGLForProcessing(){return this._useWebGLForProcessing}set useWebGLForProcessing(s){this._useWebGLForProcessing=s,this.subview&&"useWebGLForProcessing"in this.subview&&(this.subview.useWebGLForProcessing=s)}get useProgressiveUpdate(){return this._useWebGLForProcessing}set useProgressiveUpdate(s){this._useProgressiveUpdate=s,this.subview&&"useProgressiveUpdate"in this.subview&&(this.subview.useProgressiveUpdate=s)}update(s){var e;(e=this.subview)==null||e.update(s),this.notifyChange("updating")}isUpdating(){return!this.subview||this.subview.updating}attach(){this.layer.increaseRasterJobHandlerUsage(),this._updateSubview(),this.addAttachHandles([P(()=>{const{layer:s}=this;return{bandIds:s.bandIds,renderer:s.renderer,interpolation:s.interpolation,multidimensionalDefinition:s.multidimensionalDefinition,rasterFunction:s.type==="imagery-tile"?s.rasterFunction:null}},(s,e)=>{var l,u;const t=s.interpolation!==(e==null?void 0:e.interpolation)&&(s.interpolation==="majority"||(e==null?void 0:e.interpolation)==="majority")&&pe(this.layer),r=s.renderer!==(e==null?void 0:e.renderer)&&((l=e==null?void 0:e.renderer)==null?void 0:l.type)!==((u=s.renderer)==null?void 0:u.type);r&&this._updateSubview();const i=s.multidimensionalDefinition!==(e==null?void 0:e.multidimensionalDefinition),a=s.rasterFunction!==(e==null?void 0:e.rasterFunction),n=a&&!this._useWebGLForProcessing,o=i||t||r||n;this.subview.redrawOrRefetch({refetch:o,reprocess:a}).catch(d=>{F(d)||C.getLogger(this.declaredClass).error(d)}),this.notifyChange("updating")}),P(()=>this.layer.blendMode??"normal",s=>{this.subview.container.blendMode=s},Z),P(()=>this.layer.effect??null,s=>{this.subview.container.effect=s},Z),P(()=>this.layer.multidimensionalSubset??null,(s,e)=>{const{multidimensionalDefinition:t}=this.layer;g(t)&&ee(t,s)!==ee(t,e)&&(this.subview.redrawOrRefetch({refetch:!0}).catch(r=>{F(r)||C.getLogger(this.declaredClass).error(r)}),this.notifyChange("updating"))},ze),P(()=>this.timeExtent,()=>{this.subview.timeExtent=this.timeExtent,this.subview.redrawOrRefetch({refetch:!0}).catch(s=>{F(s)||C.getLogger(this.declaredClass).error(s)})},Ve)])}detach(){var s;this.layer.decreaseRasterJobHandlerUsage(),this._detachSubview(this.subview),(s=this.subview)==null||s.destroy(),this.subview=null}moveStart(){this.requestUpdate()}viewChange(){this.requestUpdate()}moveEnd(){this.subview.moveEnd()}async hitTest(s,e){return[{type:"graphic",layer:this.layer,mapPoint:s,graphic:new ce({attributes:{},geometry:s.clone()})}]}doRefresh(){return this.subview?this.subview.doRefresh():Promise.resolve()}_updateSubview(){var r;const s=this.layer.renderer.type==="vector-field"?"rasterVF":this.layer.renderer.type==="flow"?"flow":"raster";if(this.subview){if(this.subview.type===s)return void this._attachSubview(this.subview);this._detachSubview(this.subview),(r=this.subview)==null||r.destroy(),this.subview=null}const{layer:e}=this;let t;if(t=s==="rasterVF"?new Bs({layer:e,layerView:this}):s==="flow"?new Le({layer:e,layerView:this}):new Ms({layer:e,layerView:this}),"useWebGLForProcessing"in t&&(t.useWebGLForProcessing=this._useWebGLForProcessing),"useProgressiveUpdate"in t&&(t.useProgressiveUpdate=this._useProgressiveUpdate),"previousLOD"in t){const{subview:i}=this;t.previousLOD=i&&"previousLOD"in i?i.previousLOD:null}this._attachSubview(t),this.subview=t,this.requestUpdate()}_attachSubview(s){s&&!s.attached&&(s.attach(),s.attached=!0,this.container.addChildAt(s.container,0),s.container.blendMode=this.layer.blendMode,s.container.effect=this.layer.effect)}_detachSubview(s){s!=null&&s.attached&&(this.container.removeChild(s.container),s.detach(),s.attached=!1)}};h([p()],U.prototype,"subview",void 0),h([p()],U.prototype,"useWebGLForProcessing",null),h([p()],U.prototype,"useProgressiveUpdate",null),U=h([V("esri.views.2d.layers.ImageryTileLayerView2D")],U);const Pr=U;export{Pr as default};
