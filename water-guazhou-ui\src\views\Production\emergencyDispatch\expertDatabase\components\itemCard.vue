<template>
  <el-card :body-style="{ padding: '0px' }">
    <div class="flex" style="padding: 14px">
      <!-- <div class="plan">
        王
      </div> -->
      <div class="mag_left_20 text">
        <div>
          <span>姓名</span>: <span>{{ props.config?.name ?? '' }}</span>
        </div>
        <div>
          <span>性别</span>: <span>{{ props.config?.genderv ?? '' }}</span>
        </div>
        <div>
          <span>电话</span>: <span>{{ props.config?.phone ?? '' }}</span>
        </div>
        <div>
          <span>部门</span>: <span>{{ props.config?.department ?? '' }}</span>
        </div>
        <div>
          <span>职称</span>:
          <span>{{ props.config?.professionalTitle ?? '' }}</span>
        </div>
        <div>
          <span>工作单位</span>: <span>{{ props.config?.deptName ?? '' }}</span>
        </div>
      </div>
    </div>
    <div class="flex jucon_around">
      <el-button text @click="emit('edit', 0, props.config)"> 编辑 </el-button>
      <el-button text @click="emit('openDetail', 1, props.config)">
        详情
      </el-button>
      <el-button text @click="emit('del', 2, props.config)"> 删除 </el-button>
    </div>
  </el-card>
</template>

<script lang="ts" setup>
const props = defineProps<{ config: any }>();

const emit = defineEmits(['openDetail']);

// const openDetail = () => {
//   emit('openDetail', 1, props.config);
// };
</script>

<style lang="scss" scoped>
.flex {
  display: flex;
}
.plan {
  height: 40px;
  width: 40px;
  border-radius: 20px;
  line-height: 40px;
  text-align: center;
  background-color: rgb(146, 160, 160);
  font-weight: bold;
}

.mag_left_20 {
  margin-left: 20px;
}
.text {
  span {
    font-size: 14px;
  }
}
.jucon_around {
  justify-content: space-around;
}
</style>
