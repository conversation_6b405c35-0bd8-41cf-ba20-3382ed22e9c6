<!-- 智慧管网 -->
<template>
  <div class="content">
    <!-- 底图 -->
    <Map
      ref="refMap"
      class="map"
      :tools="['pipe']"
      @pipe-loaded="refreshPipeData"
    />
    <div class="toprow">
      <DeviceGroup
        :devie-data="state.deviceData"
        class="device-group"
      ></DeviceGroup>
      <div class="loss-rate">
        <TitleHeader
          :title="'漏损指标'"
          :type="'simple'"
          :title-width="180"
          style="border-radius: 18px 0 0 18px"
        ></TitleHeader>
        <div class="loss-rate-items">
          <LightPlat
            v-for="(item, i) in state.lossRates"
            :key="i"
            :data="item"
            class="loss-rate-items__item"
          ></LightPlat>
        </div>
      </div>
      <div class="toprow-right">
        <TitleCard
          class="toprow-right__item"
          :title="'设备统计'"
        >
          <DeviceStatic :pipe-data="state.pipeData"></DeviceStatic>
        </TitleCard>
        <TitleCard
          class="toprow-right__item"
          :title="'产销差分析'"
        >
          <CXCFX></CXCFX>
        </TitleCard>
        <TitleCard
          class="toprow-right__item"
          :title="'供水管网口径统计'"
          :title-width="240"
        >
          <GSGWKJTJ
            :layerids="state.layerIds"
            :layerinfos="state.layerInfos"
          />
        </TitleCard>
        <TitleCard
          class="toprow-right__item"
          :title="'分区统计'"
        >
          <FQTJ />
        </TitleCard>
      </div>
    </div>
    <div class="bottomrow">
      <TitleCard
        class="bottomrow-item bottomrow-left"
        :title="'流量监测'"
      >
        <LLJK></LLJK>
      </TitleCard>
      <TitleCard
        class="bottomrow-item bottomrow-center"
        :title="'压力监测'"
      >
        <YLJC></YLJC>
      </TitleCard>
      <TitleCard
        class="bottomrow-item bottomrow-right"
        :title="'漏损排行'"
      >
        <LSPH></LSPH>
      </TitleCard>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { queryLayerClassName } from '@/api/mapservice'
import Map from '@/components/arcMap/Map.vue'
import useStation from '@/hooks/station/useStation'
import { EStatisticField, getSubLayerIds, staticPipe } from '@/utils/MapHelper'
import { StationTypeEnums } from '@/views/basicDataManage/stationManage/data'
import TitleCard from '../components/TitleCard.vue'
import TitleHeader from '../components/TitleHeader.vue'
import CXCFX from './components/CXCFX.vue'
import DeviceGroup from './components/DeviceGroup.vue'
import DeviceStatic from './components/DeviceStatic.vue'
import FQTJ from './components/FQTJ.vue'
import GSGWKJTJ from './components/GSGWKJTJ.vue'
import LLJK from './components/LLJK.vue'
import LSPH from './components/LSPH.vue'
import LightPlat from './components/LightPlat.vue'
import YLJC from './components/YLJC.vue'

const refMap = ref<InstanceType<typeof Map>>()

const staticState: {
  view?: __esri.MapView
} = {}
const state = reactive<{
  lossRates: ILightPlatData[]
  pipeData: {
    pipeLength: number
    valve: number
    meter: number
    drayAirValve: number
    hydrant: number
    threeCorss: number
  }
  deviceData: {
    pipeLength: number
    waterQuality: number
    pressure: number
    bigUser: number
    secondary: number
    flow: number
  }
  layerInfos: any[]
  layerIds: any[]
}>({
  lossRates: [
    { type: 'up', delta: 11.8, value: 23.8, title: '产销差率' },
    { type: 'down', delta: 11.8, value: 13.8, title: '综合漏损率' },
    { type: 'up', delta: 13.8, value: 3.8, title: '漏损率' }
  ],
  pipeData: {
    pipeLength: 0,
    valve: 0,
    meter: 0,
    drayAirValve: 0,
    hydrant: 0,
    threeCorss: 0
  },
  deviceData: {
    pipeLength: 0,
    waterQuality: 0,
    pressure: 0,
    bigUser: 0,
    secondary: 0,
    flow: 0
  },
  layerInfos: [],
  layerIds: []
})

// 管线统计
const staticStatePipe = async () => {
  const lengthRes = await staticPipe('length', {
    layerIds: state.layerInfos
      .filter(item => item.geometrytype === 'esriGeometryPolyline')
      .map(item => item.layerid)
  })
  state.pipeData.pipeLength = lengthRes[0]?.rows?.[0]?.[EStatisticField.ShapeLen]
  state.deviceData.pipeLength = state.pipeData.pipeLength
  const countRes = await staticPipe('count', {
    layerIds: state.layerIds
  })
  countRes.map(item => {
    const count = item.rows[0]['OBJECTID']
    switch (item.layername) {
      case '阀门':
        state.pipeData.valve = count
        break
      case '水表':
        state.pipeData.meter = count
        break
      case '排气阀':
        state.pipeData.drayAirValve = count
        break
      case '消防栓':
        state.pipeData.hydrant = count
        break
      case '三通':
        state.pipeData.threeCorss = count
        break
      default:
        break
    }
  })
}
const getLayerInfo = async () => {
  state.layerIds = getSubLayerIds(staticState.view)
  const layerInfo = await queryLayerClassName(state.layerIds)
  state.layerInfos = layerInfo.data?.result?.rows || []
  // refreshData()
}
const refreshPipeData = async () => {
  await getLayerInfo()
  await staticStatePipe()
}
// 站点信息
const stations = useStation()
const refreshStationData = async () => {
  const all = await stations.getAllStationOption()
  state.deviceData.waterQuality = all.filter(
    item => item.data.type === StationTypeEnums.SHUIZHIJIANCEZHAN
  ).length
  state.deviceData.pressure = all.filter(
    item => [StationTypeEnums.YALIJIANCEZHAN, StationTypeEnums.CHELIUYAZHAN].indexOf(
      item.data.type
    ) !== -1
  ).length
  state.deviceData.bigUser = all.filter(
    item => item.data.type === StationTypeEnums.DAYONGHU
  ).length
  state.deviceData.secondary = all.filter(
    item => item.data.type === StationTypeEnums.BENGZHAN
  ).length
  state.deviceData.flow = all.filter(
    item => [
      StationTypeEnums.CHELIUYAZHAN,
      StationTypeEnums.LIULIANGJIANCEZHAN
    ].indexOf(item.data.type) !== -1
  ).length
}
onMounted(async () => {
  refreshStationData()
  staticState.view = await refMap.value?.init({
    zoom: 11,
    defaultFilter: 'grayscale(0%) invert(100%) opacity(100%)',
    defaultFilterColor: 'rgb(255 218 189)'
  })
})

onBeforeUnmount(() => {
  refMap.value?.destroy()
  staticState.view = undefined
})
</script>

<style lang="scss" scoped>
.content {
  width: 100%;
  height: 100%;
  padding: 100px 50px 30px;
  color: #fff;
  .toprow {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    .device-group {
      position: relative;
      margin-right: auto;
    }
    .loss-rate {
      border-radius: 18px;
      position: relative;
      width: 178px;
      align-self: flex-end;
      margin-right: 20px;
      margin-bottom: 12px;
      background: linear-gradient(
        180deg,
        rgba(31, 62, 158, 0.69) 0%,
        rgba(17, 46, 136, 0) 100%
      );
      .loss-rate-items {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 20px;
        .loss-rate-items__item {
          margin-bottom: 20px;
          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
    .toprow-right {
      width: 874px;
      display: flex;
      flex-wrap: wrap;
      .toprow-right__item {
        height: 290px;
        width: 431px;
        margin: 0 6px;
        margin-bottom: 12px;
        &:nth-child(odd) {
          margin-left: 0;
        }
        &:nth-child(even) {
          margin-right: 0;
        }
      }
    }
  }
  .bottomrow {
    display: flex;
    justify-content: space-between;
    flex-direction: row;
    flex-wrap: nowrap;
    .bottomrow-item {
      height: 330px;
    }
    .bottomrow-left {
      width: 470px;
    }
    .bottomrow-center {
      // width: 890px;
      width: calc(100% - 925px);
    }
    .bottomrow-right {
      width: 431px;
    }
  }
  .map {
    position: absolute;
    top: -35%;
    left: -50%;
    width: 150%;
    height: 135%;
    background-color: transparent;
  }
}
</style>
