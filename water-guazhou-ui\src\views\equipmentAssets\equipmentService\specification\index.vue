<!-- 保养规范 -->
<template>
  <TreeBox>
    <template #tree>
      <SLTree :tree-data="TreeData" />
    </template>
    <CardSearch
      ref="refSearch"
      :config="cardSearchConfig"
    />
    <CardTable
      :config="TableConfig"
      class="card-table"
    />
    <DialogForm
      ref="refForm"
      :config="addOrUpdateConfig"
    ></DialogForm>
  </TreeBox>
</template>

<script lang="ts" setup>
import { Refresh } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { ICONS } from '@/common/constans/common'
import useGlobal from '@/hooks/global/useGlobal'
import { SLConfirm } from '@/utils/Message'
import { useBusinessStore } from '@/store'
import { formatDate } from '@/utils/DateFormatter'
import { getDeviceTypeTree } from '@/api/equipment_assets/equipmentManage'
import {
  getstandardSerch,
  poststandard,
  deletestandard
} from '@/api/equipment_assets/equipmentService'
import { traverse } from '@/utils/GlobalHelper'

const { $btnPerms } = useGlobal()

const refForm = ref<IDialogFormIns>()

const refSearch = ref<ICardSearchIns>()

const cardSearchConfig = ref<ISearch>({
  filters: [
    { label: '关键字', field: 'keywords', type: 'input' },
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          icon: ICONS.QUERY,
          click: () => refreshData()
        },
        {
          type: 'default',
          perm: true,
          text: '重置',
          svgIcon: shallowRef(Refresh),
          click: () => {
            refSearch.value?.resetForm()
            TreeData.currentProject = {}
            refreshData()
          }
        },
        {
          perm: true,
          text: '新建',
          icon: ICONS.ADD,
          type: 'success',
          click: () => clickCreatedRole('新建')
        }
      ]
    }
  ]
})

const TableConfig = reactive<ICardTable>({
  defaultExpandAll: true,
  indexVisible: true,
  rowKey: 'id',
  columns: [
    { label: '类别名称', prop: 'deviceTypeName' },
    { label: '保养方法说明', prop: 'method' },
    { label: '注意事项', prop: 'remark' },
    { label: '添加人', prop: 'creatorName' },
    {
      label: '添加时间',
      prop: 'createTime',
      formatter: row => formatDate(row.createTime, 'YYYY-MM-DD HH:mm')
    }
  ],
  operationWidth: '200px',
  operations: [
    {
      type: 'primary',
      text: '编辑',
      icon: ICONS.EDIT,
      perm: $btnPerms('RoleManageEdit'),
      click: row => clickEdit(row)
    },
    {
      type: 'danger',
      text: '删除',
      perm: $btnPerms('RoleManageDelete'),
      icon: ICONS.DELETE,
      click: row => handleDelete(row)
    }
  ],
  dataList: [],
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page
      TableConfig.pagination.limit = size
      refreshData()
    }
  }
})

const addOrUpdateConfig = reactive<IDialogFormConfig>({
  title: '新增',
  labelWidth: '120px',
  dialogWidth: '500px',
  submitting: false,
  submit: (params: any) => {
    addOrUpdateConfig.submitting = true
    params.serialId = TreeData.currentProject.serialId
    let val = '新增成功'
    if (params.id) {
      val = '修改成功'
    }
    poststandard(params).then(() => {
      ElMessage.success(val)
      refreshData()
      refForm.value?.closeDialog()
      addOrUpdateConfig.submitting = false
    }).catch(error => {
      addOrUpdateConfig.submitting = false
      ElMessage.warning(error)
    })
  },

  defaultValue: {},
  group: [
    {
      fields: [
        {
          type: 'textarea',
          label: '保养方法说明',
          field: 'method',
          rules: [{ required: true, message: '请输入保养方法说明' }]
        },
        {
          type: 'textarea',
          label: '注意事项',
          field: 'remark',
          rules: [{ required: true, message: '请输入注意事项' }]
        }
      ]
    }
  ]
})
const TreeData = reactive<SLTreeConfig>({
  title: ' ',
  data: [],
  currentProject: {},
  expandOnClickNode: false,
  isFilterTree: true,
  treeNodeHandleClick: data => {
    // 设置当前选中项目信息
    TreeData.currentProject = data
    useBusinessStore().SET_selectedProject(data)
    refreshData()
  }
})

const clickCreatedRole = (title?: any) => {
  if (!TreeData.currentProject.id) {
    ElMessage.warning('请选中类别')
    return
  }
  addOrUpdateConfig.title = title || '编辑'
  addOrUpdateConfig.defaultValue = {}
  refForm.value?.openDialog()
}

const clickEdit = (row: { [x: string]: any }) => {
  addOrUpdateConfig.title = '编辑'
  addOrUpdateConfig.defaultValue = { ...(row || {}) }
  refForm.value?.openDialog()
}

const handleDelete = (row: { id: string }) => {
  SLConfirm('确定删除指定保养标准?', '删除提示').then(() => {
    deletestandard([row.id]).then(() => {
      ElMessage.success('删除成功')
      refreshData()
    })
  })
}

function init() {
  getDeviceTypeTree().then(res => {
    TreeData.data = traverse(res.data.data || [])
    refreshData()
  })
}

function refreshData() {
  const params = {
    size: TableConfig.pagination.limit,
    page: TableConfig.pagination.page,
    deviceTypeId: TreeData.currentProject.id || '',
    ...(refSearch.value?.queryParams || {})
  }
  getstandardSerch(params).then(res => {
    TableConfig.dataList = res.data.data.data || []
    TableConfig.pagination.total = res.data.data.total || 0
  })
}

onMounted(async () => {
  init()
})
</script>
