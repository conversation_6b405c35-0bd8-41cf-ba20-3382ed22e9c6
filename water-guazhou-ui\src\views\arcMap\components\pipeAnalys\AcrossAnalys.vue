<template>
  <div>
    <Form ref="refForm" :config="FormConfig"> </Form>
    <Panel
      ref="refChartPanel"
      custom-class="gis-across-analys-panel"
      title="横剖面分析结果"
    >
      <div class="chart-box">
        <VChart ref="refChart" :option="state.chartOption"></VChart>
      </div>
    </Panel>
    <PipeDetail
      ref="refDetail"
      :tabs="state.tabs"
      :telport="telport"
      @refreshed="state.curOperate = 'viewingDetail'"
      @refreshing="state.curOperate = 'detailing'"
      @close="state.curOperate = 'analysed'"
      @rowdblclick="handleLocate"
    ></PipeDetail>
  </div>
</template>
<script lang="ts" setup>
import { max, min } from 'lodash-es';
import {
  createPolyline,
  getGraphicLayer,
  initDrawer,
  setMapCursor
} from '@/utils/MapHelper';
import { HorizentalSectionAnalysis } from '@/api/mapservice/pipeAnalys';
import { useGisStore } from '@/store';
import { SLMessage } from '@/utils/Message';
import PipeDetail from '../common/PipeDetail.vue';
import { IECharts } from '@/plugins/echart';

const refDetail = ref<InstanceType<typeof PipeDetail>>();
const refChartPanel = ref<IPanelIns>();
const refChart = ref<IECharts>();

const props = defineProps<{
  view?: __esri.MapView;
  telport?: string;
}>();
const state = reactive<{
  curOperate:
    | 'picking'
    | 'analysing'
    | 'analysed'
    | 'detailing'
    | 'viewingDetail'
    | '';
  tabs: { label: string; name: string; data?: any }[];
  chartOption: any;
}>({
  curOperate: '',
  tabs: [],
  chartOption: null
});
const staticState: {
  drawer?: __esri.Draw;
  drawAction?: __esri.DrawAction;
  graphicsLayer?: __esri.GraphicsLayer;
  vertices: any[];
  soeResult: {
    Depth: number;
    Diameter: number;
    Distance: number;
    FromPointDefinition: string;
    FromPointNameDefinition: string;
    ID: string;
    Material: string;
    PipeLineDefinition: string;
    PipeLineNameDefinition: string;
    PipePointDefinition: string;
    PipePointNameDefinition: string;
    ToPointDefinition: string;
    ToPointNameDefinition: string;
    X: number;
    Y: number;
    Z: number;
    ZGround: number;
  }[];
  pipeInfo: {
    ID: string[];
    ZGround: number[];
    Distance: number[];
    Depth: number[];
    Diameter: number[];
    Z: number[];
    Material: string[];
  };
} = {
  vertices: [],
  soeResult: [],
  pipeInfo: {
    ID: [],
    ZGround: [],
    Distance: [],
    Depth: [],
    Diameter: [],
    Z: [],
    Material: []
  }
};
const refForm = ref<IFormIns>();
const TableConfig_AnalysResut = reactive<ITable>({
  columns: [
    { label: '设备类型', prop: 'name' },
    { label: '数量', prop: 'count', formatter: (row) => row.data?.length }
  ],
  dataList: [],
  pagination: {
    hide: true
  }
});
const FormConfig = reactive<IFormConfig>({
  group: [
    {
      fieldset: {
        desc: '绘制剖面线'
      },
      fields: [
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              type: 'warning',
              loading: () => state.curOperate === 'analysing',
              disabled: () => state.curOperate === 'analysing',
              text: () =>
                state.curOperate === 'picking'
                  ? '正在绘制剖面线'
                  : state.curOperate === 'analysing'
                    ? '分析中'
                    : '点击绘制剖面线',
              click: () => startDraw(),
              styles: {
                width: '100%'
              }
            }
          ]
        }
      ]
    },
    {
      fieldset: {
        desc: '分析结果'
      },
      fields: [
        {
          type: 'table',
          style: {
            height: '250px'
          },
          config: TableConfig_AnalysResut
        },
        {
          type: 'btn-group',
          itemContainerStyle: {
            marginTop: '20px'
          },
          btns: [
            {
              perm: true,
              text: '详细信息',
              disabled: () =>
                state.curOperate !== 'viewingDetail' &&
                state.curOperate !== 'analysed',
              loading: () => state.curOperate === 'detailing',
              click: () => viewDetail(),
              styles: {
                width: '100%'
              }
            }
          ]
        }
      ]
    }
  ],
  labelPosition: 'top',
  gutter: 12
});
const startDraw = async () => {
  if (!props.view) return;
  refChartPanel.value?.Close();
  setMapCursor('crosshair');
  state.curOperate = 'picking';
  staticState.vertices = [];
  staticState.drawAction?.destroy();
  staticState.drawer?.destroy();
  staticState.drawer = initDrawer(props.view);
  staticState.drawAction = staticState.drawer?.create('polyline');
  staticState.drawAction?.on(['vertex-add', 'cursor-update'], updateVertices);
  staticState.drawAction?.on('draw-complete', async (e) => {
    updateVertices(e);
    setMapCursor('');
    await startAnalys();
  });
};
const updateVertices = (e) => {
  if (e.vertices.length >= 2) {
    const vertices = [e.vertices[0], e.vertices[e.vertices.length - 1]];
    staticState.vertices = vertices;
    const graphic = createPolyline(vertices, props.view?.spatialReference);
    staticState.graphicsLayer?.removeAll();
    graphic && staticState.graphicsLayer?.add(graphic);
  }
};
const startAnalys = async () => {
  if (!props.view) {
    SLMessage.error('地图服务未就绪，请稍候再试');
    return;
  }
  state.curOperate = 'analysing';
  try {
    const res = await HorizentalSectionAnalysis({
      UserToken: useGisStore().gToken || '',
      X1: staticState.vertices[0][0],
      X2: staticState.vertices[1][0],
      Y1: staticState.vertices[0][1],
      Y2: staticState.vertices[1][1],
      f: 'pjson'
    });

    staticState.soeResult = res.data?.Values || [];
    if (res.data.Status !== 'successed') {
      SLMessage.error('分析失败');
      state.curOperate = '';
      return;
    }
    state.tabs = [];

    staticState.pipeInfo.ID = [];
    staticState.pipeInfo.ZGround = [];
    staticState.pipeInfo.Distance = [];
    staticState.pipeInfo.Depth = [];
    staticState.pipeInfo.Diameter = [];
    staticState.pipeInfo.Z = [];
    staticState.pipeInfo.Material = [];
    staticState.soeResult.map((item) => {
      const nameoid = (
        item.PipeLineNameDefinition || item.PipePointNameDefinition
      )?.split(':');
      let layername = '';
      let oid = '';
      if (nameoid.length === 2) {
        layername = nameoid[0];
        oid = nameoid[1];
        const tab = state.tabs.find((l) => l.label === layername);
        if (!tab) {
          state.tabs.push({ label: layername, name: layername, data: [oid] });
        } else {
          tab.data.push(oid);
        }
      }
      staticState.pipeInfo.ID.push(item.ID);
      staticState.pipeInfo.ZGround.push(item.ZGround);
      staticState.pipeInfo.Distance.push(item.Distance);
      staticState.pipeInfo.Depth.push(item.Depth);
      staticState.pipeInfo.Diameter.push(item.Diameter);
      staticState.pipeInfo.Z.push(item.Z);
      staticState.pipeInfo.Material.push(item.Material);
    });

    TableConfig_AnalysResut.dataList = state.tabs;
    refChart.value?.clear();
    state.chartOption = null;
    refChartPanel.value?.Open();
    state.chartOption = initChartOption();
    nextTick(() => {
      refChart.value?.resize();
    });
  } catch (error) {
    SLMessage.error('系统错误');
    state.curOperate = '';
  }
  state.curOperate = 'analysed';
};
const initChartOption = () => {
  const lineData: any[] = [];
  const points = staticState.pipeInfo.ZGround.map((item, i) => {
    const placePoint = [
      staticState.pipeInfo.Distance[i],
      staticState.pipeInfo.ZGround[i]
    ];
    lineData.push(placePoint);
    return [staticState.pipeInfo.Distance[i], staticState.pipeInfo.Z[i]];
  });
  const diameterMax = max(staticState.pipeInfo.Diameter) || 0;
  const diameterMin = min(staticState.pipeInfo.Diameter) || 0;
  const symbolSizeMin = 5;
  const symbolSizeMax = 10;
  const option = {
    legend: {
      selectedMode: false,
      textStyle: {
        color: '#fff'
      }
    },
    toolbox: {
      show: true,
      itemSize: 20,
      feature: {
        mark: { show: true },
        restore: { show: true },
        saveAsImage: { show: true }
      }
    },
    dataZoom: {
      show: true,
      start: 0,
      end: 100,
      textStyle: {
        color: '#fff'
      }
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params) => {
        if (params.seriesName === '地面') {
          return (
            '地面点高程：' +
            staticState.pipeInfo.ZGround[params.dataIndex].toFixed(2)
          );
        }
        if (params[0].seriesName === '管点') {
          return (
            '管径：' +
            staticState.pipeInfo.Diameter[params[0].dataIndex] +
            '<br/>' +
            '材质:' +
            staticState.pipeInfo.Material[params[0].dataIndex] +
            '<br/>' +
            '埋深:' +
            Number(staticState.pipeInfo.Depth[params[0].dataIndex].toFixed(2)) /
              8
          );
        }
      }
    },
    xAxis: {
      type: 'value', // 坐标类型
      // data: xAxisData,
      show: true, // 是否显示
      position: 'bottom', // 位置
      name: '距离', // 坐标轴的名称
      nameLocation: 'end', // 坐标轴名称位置
      nameTextStyle: {}, //
      boundaryGap: [0, 0],
      min: null,
      max: null,
      color: '#A9D2E1',
      axisLabel: {
        formatter: '{value}m',
        textStyle: {
          color: '#ffffff'
        }
      },
      splitLine: {
        show: false,
        color: '#00ff00'
      },
      splitArea: {
        show: false
      }
    },
    yAxis: {
      name: '高程',
      type: 'value',
      scale: true,
      color: '#A9D2E1',
      axisLabel: {
        formatter: '{value}m',
        textStyle: {
          color: '#ffffff'
        }
      },
      splitArea: {
        show: false
      },
      splitLine: {
        lineStyle: {
          color: '#FFFFFF',
          opacity: 0.2,
          type: 'dashed'
        }
      }
    },
    grid: {
      left: 100
    },
    series: [
      {
        name: '管点',
        type: 'scatter',
        tooltip: {
          trigger: 'axis'
        },

        legendHoverLink: true,
        symbol: 'emptyCircle',
        symbolSize(value) {
          let index;
          for (let i = 0; i < points.length; i++) {
            if (points[i][1] === value[1]) {
              index = i;
            }
          }
          // 优化管径展示
          if (diameterMax === diameterMin) {
            return symbolSizeMin;
          }
          return (
            symbolSizeMin +
            ((staticState.pipeInfo.Diameter[index] - diameterMin) /
              (diameterMax - diameterMin)) *
              (symbolSizeMax - symbolSizeMin)
          );
        },
        itemStyle: {
          normal: {
            color: 'red',
            borderWidth: 2,
            borderColor: '#070707',
            // 点对应的文本
            label: {
              show: true,
              formatter: (params) => {
                //
                return staticState.pipeInfo.ID[params.dataIndex];
              },
              position: 'bottom',
              textStyle: {
                color: '#fff',
                align: 'right',
                baseline: 'bottom',
                fontSize: '10px'
              }
            }
          },
          emphasis: {
            color: '#aa0000',
            borderWidth: 2,
            borderColor: '#070707'
          }
        },
        data: points
      },

      {
        name: '地面',
        type: 'line',
        clickable: true, // 数据图形是否可点击，默认开启，如果没有click事件响应可以关闭
        // itemStyle: null,
        data: lineData,
        markePoint: {},

        itemStyle: {
          normal: {
            lineStyle: { width: 2, color: '#aaaaaa' }, // 线样式
            areaStyle: { color: '#AE6F39', type: 'default' },
            label: {
              show: true,
              formatter: (params) => {
                if (
                  params.data[0] <
                    staticState.pipeInfo.Distance[params.dataIndex - 1] + 5 &&
                  params.dataIndex > 0
                ) {
                  const blank = '';
                  return blank;
                }
                return params.data[1].toFixed(1);
              }
            }
          }
        },
        markeLine: {
          data: [{ type: 'average', name: '平均高程' }]
        },

        stack: null, //
        xAxisIndexs: 0,
        yAxisIndex: 0,
        symbol: null, // 'circle' | 'rectangle' | 'triangle' | 'diamond' |'emptyCircle' | 'emptyRectangle' | 'emptyTriangle' | 'emptyDiamond'
        symbolSize: 2 | 4,
        symbolRotate: null, // 标志图形旋转角度[-180,180]
        showAllSymbol: false,
        // smooth: true, //smooth为true时lineStyle不支持虚线
        dataFilter: 'nearst', // 'nearest', 'min', 'max', 'average'
        legendHoverLink: true // 是否启用图例（legend）hover时的联动响应（高亮显示）
      }
    ]
  };
  return option;
};

const handleLocate = async () => {
  props.view && refDetail.value?.extentTo(props.view);
};
const clearGraphicsLayer = () => {
  if (!props.view) return;
  staticState.graphicsLayer = getGraphicLayer(props.view, {
    id: 'across-analys',
    title: '横剖面分析'
  });
  staticState.graphicsLayer?.removeAll();
};
const destroy = () => {
  staticState.graphicsLayer &&
    props.view?.map.remove(staticState.graphicsLayer);
  staticState.drawer?.destroy();
  staticState.drawAction?.destroy();
};
onMounted(() => {
  clearGraphicsLayer();
});
onBeforeUnmount(() => {
  refChartPanel.value?.Close();
  destroy();
});
const viewDetail = () => {
  state.curOperate = 'detailing';
  refDetail.value?.openDialog();
};
</script>
<style lang="scss" scoped>
.chart-box {
  width: 100%;
  height: 100%;
}
</style>
<style lang="scss">
.gis-across-analys-panel {
  width: 600px;
  height: 450px;
  position: absolute;
  left: calc(50% - 300px);
  top: 150px;
  overflow: hidden;
}
</style>
