import{_ as L}from"./ArcLayout-CHnHL9Pv.js";import{d as M,c as n,e as P,o as U,g as A,h as F,F as a,q as c,G as m,p as u,bh as y,i as f,b as v,J as T,bK as h,C as w}from"./index-r0dFAfgr.js";import{_ as z}from"./CoordinateConverter.vue_vue_type_script_setup_true_lang-P4M5h95Z.js";import"./ArcView-DpMnCY82.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./widget-BcWKanF2.js";import"./pe-B8dP0-Ut.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./TileLayer-B5vQ99gG.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./commonProperties-DqNQ4F00.js";import"./project-DUuzYgGl.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./IdentifyResult-4DxLVhTm.js";import"./ViewHelper-BGCZjxXH.js";import"./fieldconfig-Bk3o1wi7.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./LayerHelper-Cn-iiqxI.js";import"./pipe-nogVzCHG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./ArcStationWarning-BF9YrSzF.js";/* empty css                         */import"./useWaterPoint-Bv0z6ym6.js";import"./DateFormatter-Bm9a68Ax.js";import"./zhandian-YaGuQZe6.js";import"./useStation-DJgnSZIA.js";import"./arcWidgetButton-0glIxrt7.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./StatisticsHelper-D-s_6AyQ.js";import"./useLayerList-DmEwJ-ws.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./URLHelper-B9aplt5w.js";import"./ArcPipe.vue_vue_type_script_setup_true_lang-ClsVvV2P.js";const E={style:{height:"200px"},class:"code code-bg code-font"},O=M({__name:"GeoJsonConverter",setup(V){const i=n(),b=n(),d=n(),_=n(),x=n(!1),C=()=>{if(i.value){if(!d.value){v.warning("请先填入geojson数据");return}try{const r=JSON.parse(d.value),o=r==null?void 0:r.features;o==null||o.map(t=>{var e,p,l;!t||!i.value||((e=t.properties)!=null&&e.center&&(t.properties.center=i.value.convert(t.properties.center)),(p=t.properties)!=null&&p.centroid&&(t.properties.centroid=i.value.convert(t.properties.centroid)),(l=t.geometry)!=null&&l.coordinates&&(t.geometry.coordinates=k(t.geometry)),t.bbox&&(t.bbox=[...i.value.convert([t.bbox[0],t.bbox[1]]),...i.value.convert([t.bbox[0],t.bbox[1]])]))}),_.value=r}catch{v.error("转换失败")}}},k=r=>{var o;if(r.type==="MultiPolygon")return r.coordinates.map(t=>t.map(e=>e.map(p=>{var l;return(l=i.value)==null?void 0:l.convert(p)})));if(r.type==="LineString")return r.coordinates.map(t=>{var e;return(e=i.value)==null?void 0:e.convert(t)});if(r.type==="Point")return(o=i.value)==null?void 0:o.convert(r.coordinates)},g=n(),G=P(),S=()=>{g.value&&G.copy(g.value.innerText).then(()=>{v.success("复制成功")}).catch(r=>{console.log(r),v.error("复制失败")})},s=n(),J=r=>{if(!s.value)return;s.value.clearFiles();const o=r[0];s.value.handleStart(o)},N=r=>{const o=new FileReader;return o.readAsText(r,"UTF-8"),o.onload=t=>{var p;const e=(p=t.target)==null?void 0:p.result;d.value=JSON.stringify(JSON.parse(e))},!1},B=(r,o)=>{var t;console.log(r,o),(t=s.value)==null||t.submit()};return U(()=>{var r,o;(o=(r=b.value)==null?void 0:r.refPanel)==null||o.toggleMaxMin("max")}),(r,o)=>{const t=T,e=h,p=L;return A(),F(p,{ref_key:"refArcLayout",ref:b,"panel-default-visible":!0,"panel-max-min":!0,"hide-panel-close":!0,"panel-dragable":!1,"panel-default-maxmin":"max","panel-title":"Geojson几何转换 —— 经纬度坐标转换成xy坐标"},{"detail-default":a(()=>[c(e,{ref_key:"refUpload",ref:s,action:"",limit:1,"on-exceed":J,"auto-upload":!1,"before-upload":N,"on-change":B},{trigger:a(()=>[c(t,{size:"small",type:"primary"},{default:a(()=>o[0]||(o[0]=[m(" 上传geojson文件 ")])),_:1})]),_:1},512),u("pre",E,[o[1]||(o[1]=m("        ")),u("code",null,y(f(d)??"上传文件预览"),1),o[2]||(o[2]=m(`
      `))]),c(t,{loading:f(x),type:"primary",style:{margin:"10px 0"},size:"small",onClick:C},{default:a(()=>o[3]||(o[3]=[m(" 转换 ")])),_:1},8,["loading"]),c(t,{loading:f(x),type:"success",size:"small",onClick:S},{default:a(()=>o[4]||(o[4]=[m(" 复制结果 ")])),_:1},8,["loading"]),u("pre",{ref_key:"refPre",ref:g,class:"code code-bg code-font"},[o[5]||(o[5]=m("        ")),u("code",null,y(f(_)),1),o[6]||(o[6]=m(`
      `))],512)]),default:a(()=>[c(z,{ref_key:"refConverter",ref:i},null,512)]),_:1},512)}}}),Gt=w(O,[["__scopeId","data-v-dacb6cae"]]);export{Gt as default};
