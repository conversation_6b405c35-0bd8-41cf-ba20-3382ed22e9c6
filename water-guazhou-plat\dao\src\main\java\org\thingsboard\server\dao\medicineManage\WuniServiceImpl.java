package org.thingsboard.server.dao.medicineManage;

import com.gexin.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.utils.DateUtils;
import org.thingsboard.server.dao.model.sql.WuniInput;
import org.thingsboard.server.dao.model.sql.alarmV2.AlarmCenter;
import org.thingsboard.server.dao.sql.medicineManage.WuniRepository;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;

@Slf4j
@Service
public class WuniServiceImpl implements WuniService {

    @Autowired
    private WuniRepository wuniRepository;

    @Override
    public List<WuniInput> findList(TenantId tenantId) {
        Calendar instance = Calendar.getInstance();
        instance.set(Calendar.MONTH, instance.getActualMinimum(Calendar.MONTH));
        instance.set(Calendar.DAY_OF_MONTH, instance.getActualMinimum(Calendar.DAY_OF_MONTH));
        instance.set(Calendar.HOUR_OF_DAY, 0);
        instance.set(Calendar.MINUTE, 0);
        instance.set(Calendar.SECOND, 0);
        Date yearStart = instance.getTime();
        Date yearEnd = new Date();

        // 初始化Map
        Map<String, WuniInput> wuniMap = new LinkedHashMap<>();
        for (int i = 1; i <= 12; i++) {
            String month = instance.get(Calendar.YEAR) + "-" + String.format("%02d", i);
            WuniInput wuniInput = new WuniInput();
            wuniInput.setMonth(month);
            wuniMap.put(instance.get(Calendar.YEAR) + "-" + String.format("%02d", i), wuniInput);
        }

        List<WuniInput> dataList = wuniRepository.findByTimeBetweenOrderByTimeAsc(yearStart, yearEnd);
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM");
        if (dataList != null) {
            for (WuniInput wuniInput : dataList) {
                String format = dateFormat.format(wuniInput.getTime());
                wuniMap.put(format, wuniInput);
            }
        }

        return new ArrayList<>(wuniMap.values());
    }

    @Override
    public void save(WuniInput wuniInput) {
        wuniRepository.save(wuniInput);
    }

    @Override
    public Object findMonthReport(String month, String stationId) {
        Date monthStart = DateUtils.str2Date(month, "yyyy-MM");
        Calendar instance = Calendar.getInstance();
        instance.setTime(monthStart);
        instance.set(Calendar.DAY_OF_MONTH, instance.getActualMaximum(Calendar.DAY_OF_MONTH));

        Date monthEnd = instance.getTime();

        List<WuniInput> wuniList = wuniRepository.findByTimeBetweenOrderByTimeAsc(monthStart, monthEnd);
        // 统计数据
        WuniInput avgData = new WuniInput();
        avgData.setTimeStr("平均值");
        WuniInput minData = new WuniInput();
        minData.setTimeStr("最低");
        WuniInput maxData = new WuniInput();
        maxData.setTimeStr("最高");
        WuniInput totalData = new WuniInput();
        totalData.setTimeStr("合计");
        if (wuniList != null && wuniList.size() > 0) {
            for (WuniInput wuniData : wuniList) {
                BigDecimal num = wuniData.getNum();
                if (minData.getNum() == null || num.compareTo(minData.getNum()) < 0) {
                    minData.setNum(num);
                }
                if (maxData.getNum() == null || num.compareTo(maxData.getNum()) > 0) {
                    maxData.setNum(num);
                }
                if (totalData.getNum() == null) {
                    totalData.setNum(num);
                } else {
                    if (num == null) {
                        num = BigDecimal.ZERO;
                    }
                    totalData.setNum(totalData.getNum().add(num));
                }

                BigDecimal numRate = wuniData.getNumRate();
                if (minData.getNumRate() == null || numRate.compareTo(minData.getNumRate()) < 0) {
                    minData.setNumRate(numRate);
                }
                if (maxData.getNumRate() == null || numRate.compareTo(maxData.getNumRate()) > 0) {
                    maxData.setNumRate(numRate);
                }
                if (totalData.getNumRate() == null) {
                    totalData.setNumRate(numRate);
                } else {
                    if (numRate == null) {
                        numRate = BigDecimal.ZERO;
                    }
                    totalData.setNumRate(totalData.getNumRate().add(numRate));
                }

                BigDecimal pam = wuniData.getPam();
                if (minData.getPam() == null || pam.compareTo(minData.getPam()) < 0) {
                    minData.setPam(pam);
                }
                if (maxData.getPam() == null || pam.compareTo(maxData.getPam()) > 0) {
                    maxData.setPam(pam);
                }
                if (totalData.getPam() == null) {
                    totalData.setPam(pam);
                } else {
                    if (pam == null) {
                        pam = BigDecimal.ZERO;
                    }
                    totalData.setPam(totalData.getPam().add(pam));
                }
            }
            // 计算平均值
            int size = wuniList.size();
            BigDecimal num = totalData.getNum();
            BigDecimal numRate = totalData.getNumRate();
            BigDecimal pam = totalData.getPam();

            avgData.setNum(num.divide(BigDecimal.valueOf(size), 2, RoundingMode.HALF_UP));
            avgData.setNumRate(numRate.divide(BigDecimal.valueOf(size), 2, RoundingMode.HALF_UP));
            avgData.setPam(pam.divide(BigDecimal.valueOf(size), 2, RoundingMode.HALF_UP));

            wuniList.add(minData);
            wuniList.add(maxData);
            wuniList.add(avgData);
            wuniList.add(totalData);
            SimpleDateFormat dayFormat = new SimpleDateFormat("yyyy-MM-dd");
            for (WuniInput wuniInput : wuniList) {
                if (wuniInput.getTime() != null) {
                    wuniInput.setTimeStr(dayFormat.format(wuniInput.getTime()));
                }
            }
        }

        return wuniList;
    }

    @Override
    public void remove(List<String> ids) {
        for (String id : ids) {
            wuniRepository.delete(id);
        }
    }
}
