package org.thingsboard.server.dao.util.imodel.query.smartManagement.district;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartManagement.district.CircuitDistrictPoint;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

@Getter
@Setter
public class CircuitDistrictPointPageRequest extends AdvancedPageableQueryEntity<CircuitDistrictPoint, CircuitDistrictPointPageRequest> {
    // 所属区域、路线(点阵)Id
    @NotNullOrEmpty
    private String areaId;

}
