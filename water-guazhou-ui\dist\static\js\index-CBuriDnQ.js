import{_ as I}from"./TreeBox-DDD2iwoR.js";import{d as U,M as W,c as g,s as L,S as h,x as c,r as p,a8 as P,ar as V,a9 as y,o as j,g as q,h as F,F as b,q as u,i as d,b6 as M,b7 as N}from"./index-r0dFAfgr.js";import{_ as R}from"./CardTable-rdWOL4_6.js";import{_ as B}from"./CardSearch-CB_HNR-Q.js";import{_ as $}from"./index-BJ-QPYom.js";import{I as i}from"./common-CvK_P_ao.js";import{p as A,a as O,d as z,g as G,b as H}from"./emergencyDispatch-BKfr3jS6.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";const ne=U({__name:"index",setup(K){const{$btnPerms:_}=W(),l=g(),m=g(),x=g({filters:[{label:"名称",field:"name",type:"input"},{type:"btn-group",btns:[{perm:!0,text:"查询",icon:i.QUERY,click:()=>o()},{type:"default",perm:!0,text:"重置",svgIcon:L(N),click:()=>{var e;(e=m.value)==null||e.resetForm(),o()}},{perm:!0,text:"新建",icon:i.ADD,type:"success",click:()=>S()},{perm:!0,text:"同步平台人员",icon:i.SEND,type:"success",click:()=>{h("确定同步平台人员？","同步提示").then(()=>{A().then(()=>{c.success("同步成功"),o()}).catch(e=>{c.warning(e)})})}}]}]}),n=p({defaultExpandAll:!0,indexVisible:!0,rowKey:"id",columns:[{label:"组织",prop:"deptName"},{label:"人员名称",prop:"name"},{label:"联系方式",prop:"phone"}],operationWidth:"240px",operations:[{type:"primary",text:"编辑",icon:i.EDIT,perm:_("RoleManageEdit"),click:e=>E(e)},{type:"danger",text:"删除",perm:_("RoleManageDelete"),icon:i.DELETE,click:e=>T(e)}],dataList:[],pagination:{page:1,limit:20,total:0,refreshData:({page:e,size:t})=>{n.pagination.page=e,n.pagination.limit=t,o()}}}),r=p({title:"新增",width:"500px",labelWidth:"100px",submitting:!1,submit:e=>{r.submitting=!0;let t="新增成功";e.id&&(t="修改成功"),O(e).then(()=>{var a;r.submitting=!1,(a=l.value)==null||a.closeDrawer(),c.success(t),o()}).catch(a=>{r.submitting=!1,c.warning(a)})},defaultValue:{},group:[{fields:[{type:"input",label:"用户名称",field:"name",rules:[{required:!0,message:"请输入用户名称"}]},{type:"input-number",label:"联系方式",field:"phone",rules:[{required:!0,message:"请输入联系方式"}]},{type:"select-tree",label:"所属组织",field:"deptId",checkStrictly:!0,options:P(()=>f.WaterSupplyTree),rules:[{required:!0,message:"请输入所属组织"}]}]}]}),s=p({title:" ",data:[],currentProject:{},isFilterTree:!0,treeNodeHandleClick:e=>{s.currentProject=e,o()}}),S=()=>{var e;r.title="新增",r.defaultValue={deptId:s.currentProject.id||{}},(e=l.value)==null||e.openDrawer()},E=e=>{var t;r.title="编辑",r.defaultValue={category:e.parentId,...e||{}},(t=l.value)==null||t.openDrawer()},T=e=>{h("确定删除该应急人员","删除提示").then(()=>{z(e.id).then(()=>{c.success("删除成功"),o()}).catch(t=>{c.error(t.toString())})})},f=p({WaterSupplyTree:[],getWaterSupplyTreeValue:()=>{V(2).then(t=>{f.WaterSupplyTree=y(t.data.data||[])})}});function k(){H(2).then(e=>{s.data=y(e.data.data||[]),s.currentProject=e.data.data[0]})}const o=async()=>{var t;const e={size:n.pagination.limit,page:n.pagination.page,deptId:s.currentProject.id,...((t=m.value)==null?void 0:t.queryParams)||{}};G(e).then(a=>{n.dataList=a.data.data.data||[],n.pagination.total=a.data.data.total||0})};return j(async()=>{k(),f.getWaterSupplyTreeValue()}),(e,t)=>{const a=$,D=B,C=R,w=M,v=I;return q(),F(v,null,{tree:b(()=>[u(a,{"tree-data":d(s)},null,8,["tree-data"])]),default:b(()=>[u(D,{ref_key:"refSearch",ref:m,config:d(x)},null,8,["config"]),u(C,{class:"card-table",config:d(n)},null,8,["config"]),u(w,{ref_key:"refForm",ref:l,config:d(r)},null,8,["config"])]),_:1})}}});export{ne as default};
