package org.thingsboard.server.controller.waterPlant;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.stationData.customization.WaterPlantViewService;

@RestController
@RequestMapping("api/waterPlant/view/data")
public class WaterPlantViewApiController extends BaseController {

    @Autowired
    private WaterPlantViewService waterPlantViewService;

    /**
     * 不同时段用水情况
     */
    @GetMapping("timeAreaUseWater")
    public Object timeAreaUseWater(@RequestParam String projectId,
                                   @RequestParam(required = false) Long start,
                                   @RequestParam(required = false) Long end) throws ThingsboardException {
        return waterPlantViewService.timeAreaUseWater(projectId, start, end, getTenantId());
    }

    /**
     * 用水分析
     */
    @GetMapping("weekSupplyWaterAnalyze")
    public Object weekSupplyWaterAnalyze(@RequestParam String projectId) throws ThingsboardException {
        return waterPlantViewService.weekSupplyWaterAnalyze(projectId, getTenantId());
    }


    /**
     * 设备数量以及供水量统计
     */
    @GetMapping("viewCount")
    public Object viewCount(@RequestParam String projectId) throws ThingsboardException {
        return waterPlantViewService.viewCount(projectId, getTenantId());
    }

    @GetMapping("produceDashboardCount")
    public Object produceDashboardCount(@RequestParam String projectId, @RequestParam String stationType) throws ThingsboardException {
        return waterPlantViewService.produceDashboardCount(projectId, stationType, getTenantId());
    }

}
