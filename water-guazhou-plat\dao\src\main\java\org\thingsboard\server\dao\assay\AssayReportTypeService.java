package org.thingsboard.server.dao.assay;

import com.alibaba.fastjson.JSONObject;
import org.thingsboard.server.dao.model.sql.assay.AssayReportAddress;
import org.thingsboard.server.dao.model.sql.assay.AssayReportItem;
import org.thingsboard.server.dao.model.sql.assay.AssayReportType;

import java.util.List;

public interface AssayReportTypeService {

    void remove(List<String> ids);

    List<AssayReportType> findList(String name, String tenantId);

    void save(AssayReportType entity);

    List<AssayReportItem> getItemList(String reportId);

    void saveItemList(List<AssayReportItem> entityList, String tenantId);

    List<AssayReportAddress> getAddressList(String reportId);

    void saveChildList(JSONObject jsonObject, String tenantId);
}
