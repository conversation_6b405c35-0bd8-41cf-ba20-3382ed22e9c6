package org.thingsboard.server.dao.model.sql.gis;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;
import java.util.Date;

@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.TB_GIS_PLAN_TABLE)
@TableName(ModelConstants.TB_GIS_PLAN_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class GisPlan {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.TB_GIS_PLAN_NAME)
    private String name;

    @Column(name = ModelConstants.TB_GIS_PLAN_TYPE)
    private String type;

    @Column(name = ModelConstants.TB_GIS_PLAN_USER_ID)
    private String userId;

    @Column(name = ModelConstants.TB_GIS_PLAN_REMARK)
    private String remark;

    @Column(name = ModelConstants.TB_GIS_PLAN_DETAIL)
    private String detail;

    @Column(name = ModelConstants.CREATE_TIME)
    private Date createTime;


}
