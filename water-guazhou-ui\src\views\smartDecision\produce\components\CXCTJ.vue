<template>
  <TitleCard title="产销差统计">
    <template #title>
      <div class="card-header">
        <span class="title-text">
          <i>产销差统计</i>
        </span>
        <div class="left"></div>
        <!-- <inline-form
          ref="refProSaleForm"
          style="width: auto"
          :config="psConfig"
        ></inline-form> -->
      </div>
    </template>
    <div
      ref="refDiv"
      class="chart-box"
    >
      <VChart
        ref="refChart"
        :option="state.barChartOption"
        :theme="useAppStore().isDark ? 'blackBackground' : 'whiteBackground'"
      ></VChart>
    </div>
  </TitleCard>
</template>
<script lang="ts" setup>
import { operatingIncomeInputList } from '@/api/revenueEntry'
import { getWaterSupplyDetailReport } from '@/api/waterFactoryManage/factoryReport'
// import { IInlineFormIns } from '@/components/type'
import { useDetector } from '@/hooks/echarts'
import useStation from '@/hooks/station/useStation'
import { useAppStore } from '@/store'
import TitleCard from '../../components/TitleCard.vue'

const state = reactive<{
  barChartOption: any
}>({
  barChartOption: null
})
// const refProSaleForm = ref<IInlineFormIns>()
// const psConfig = reactive<IFormConfig>({
//   defaultValue: {},
//   group: [
//     {
//       fields: [
//         {
//           type: 'select-tree',
//           field: 'stationId',
//           options: [],
//           className: 'transparentinput',
//           async autoFillOptions(config) {
//             try {
//               await stations.getStationOption('水厂', undefined, true)
//               config.options = stations.StationList.value
//               refProSaleForm.value?.dataForm
//                 && (refProSaleForm.value.dataForm.stationId = stations.StationList.value[0].value)
//               refreshData()
//             } catch (error) {
//               //
//             }
//           },
//           onChange: () => refreshData()
//         }
//       ]
//     }
//   ]
// })
const stations = useStation()
const initChart = (xData: any[] = [], supply: any[] = [], sale: any[] = []) => {
  const rate = supply.map((item, i) => {
    return item
      ? Number((((item - (sale[i] ?? 0)) / item) * 100).toFixed(2))
      : 0
  })
  state.barChartOption = {
    backgroundColor: 'transparent',
    legend: {
      left: 'right',
      top: 'top',
      type: 'scroll',
      textStyle: {
        color: '#fff',
        fontSize: 12
      }
    },
    tooltip: {
      trigger: 'axis'
    },
    grid: {
      left: 10,
      right: 10,
      top: 50,
      bottom: 10,
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: xData || [],

      axisLabel: {
        show: true,
        textStyle: {
          // color: '#656b84' // 更改坐标轴文字颜色
          // fontSize: 14 //更改坐标轴文字大小
        }
      },
      axisTick: {
        show: false
      },
      splitLine: {
        show: false
      }
    },
    yAxis: [
      {
        position: 'left',
        type: 'value',
        name: 'm³',
        // axisLine: {
        //   show: false
        // },
        axisLabel: {
          show: true,
          textStyle: {
            // color: '#656b84' // 更改坐标轴文字颜色
            // fontSize: 14 //更改坐标轴文字大小
          }
        },
        axisTick: {
          show: false
        },
        splitLine: {
          lineStyle: {
            type: 'dashed',
            color: '#666'
          }
        }
      },
      {
        type: 'value',
        name: '产销差（%）',

        axisLabel: {
          show: true,
          textStyle: {
            // color: '#656b84' // 更改坐标轴文字颜色
            // fontSize: 14 //更改坐标轴文字大小
          }
        },
        axisTick: {
          show: false
        },
        splitLine: {
          show: false
        }
      }
    ],
    series: [
      {
        name: '供水量',
        type: 'bar',
        barWidth: 5,
        data: supply || []
      },
      {
        name: '售水量',
        type: 'bar',
        barWidth: 5,
        data: sale || []
      },
      {
        name: '产销差',
        type: 'line',
        yAxisIndex: 1,
        data: rate || []
      }
    ]
  }
}
const getStationId = async () => {
  await stations.getStationOption('水厂', undefined, true)
  return stations.StationList.value[0]?.value
}
const refreshData = async () => {
  const supply: number[] = []
  const sales: number[] = []
  const xData: string[] = []
  const year = moment().get('y').toString()
  const stationId = await getStationId()
  // const stationId = refProSaleForm.value?.dataForm.stationId || stations.StationList.value[0].value
  if (stationId) {
    const res = await operatingIncomeInputList({
      stationId,
      year
    })
    res.data.data.map(item => {
      xData.push(item.ts)
      sales.push(item.waterSales)
    })
    const res1 = await getWaterSupplyDetailReport({
      stationIdList: stationId,
      queryType: 'year',
      start: moment().startOf('y').valueOf(),
      end: moment().endOf('y').valueOf()
    })
    res1.data.data?.tableDataList?.map((item, i) => {
      if (i > 11) return
      supply.push(item.total)
    })
  }

  initChart(xData, supply, sales)
}
const detector = useDetector()
const refChart = ref()
const refDiv = ref()
onMounted(() => {
  refreshData()
  detector.listenToMush(refDiv.value, () => {
    refChart.value?.resize()
  })
})
</script>
<style lang="scss" scoped>
.chart-box {
  width: 100%;
  height: 100%;
}

.card-header {
  display: flex;
  align-items: center;
  word-break: keep-all;
  justify-content: space-between;
  width: 100%;
  .left {
    display: flex;
    align-items: center;
  }
  :deep(.el-form-item--default) {
    margin-bottom: 0;
  }
}
.title-text {
  font-weight: 600;
  color: #fff;
  line-height: 36px;
  padding-left: 25px;
}
</style>
<style lang="scss">
.card-header {
  .transparentinput {
    width: 150px;
    .el-input__wrapper {
      background-color: transparent;
      box-shadow: 0 0 0 1px #6bc7fd inset;
      .el-input__inner {
        color: #fff;
      }
    }
  }
}
</style>
