import{j as k,h as I}from"./widget-BcWKanF2.js";/*!
 * All material copyright ESRI, All Rights Reserved, unless otherwise specified.
 * See https://github.com/Esri/calcite-components/blob/master/LICENSE.md for details.
 * v1.0.8-next.4
 */const f="hidden-form-input";function m(e){return"checked"in e}const h=new WeakMap,E=new WeakSet;function S(e,t){const n="calciteInternalFormComponentRegister";let r=!1;return e.addEventListener(n,a=>{r=a.composedPath().some(o=>E.has(o)),a.stopPropagation()},{once:!0}),t.dispatchEvent(new CustomEvent(n,{bubbles:!0,composed:!0})),r}function y(e){const{formEl:t}=e;return t?("requestSubmit"in t?t.requestSubmit():t.submit(),!0):!1}function L(e){const{el:t,value:n}=e,r=k(t,"form");if(!r||S(r,t))return;e.formEl=r,e.defaultValue=n,m(e)&&(e.defaultChecked=e.checked);const a=(e.onFormReset||R).bind(e);r.addEventListener("reset",a),h.set(e.el,a),E.add(t)}function R(){if(m(this)){this.checked=this.defaultChecked;return}this.value=this.defaultValue}function q(e){const{el:t,formEl:n}=e;if(!n)return;const r=h.get(t);n.removeEventListener("reset",r),h.delete(t),e.formEl=null,E.delete(t)}const C=e=>{e.target.dispatchEvent(new CustomEvent("calciteInternalHiddenInputChange",{bubbles:!0}))},F=e=>e.removeEventListener("change",C);function H(e){const{el:t,formEl:n,name:r,value:a}=e,{ownerDocument:o}=t,c=t.querySelectorAll(`input[slot="${f}"]`);if(!n||!r){c.forEach(s=>{F(s),s.remove()});return}const u=Array.isArray(a)?a:[a],i=[],v=new Set;c.forEach(s=>{const d=u.find(g=>g==s.value);d!=null?(v.add(d),b(e,s,d)):i.push(s)});let l;u.forEach(s=>{if(v.has(s))return;let d=i.pop();d||(d=o.createElement("input"),d.slot=f),l||(l=o.createDocumentFragment()),l.append(d),d.addEventListener("change",C),b(e,d,s)}),l&&t.append(l),i.forEach(s=>{F(s),s.remove()})}function b(e,t,n){var u;const{defaultValue:r,disabled:a,name:o,required:c}=e;t.defaultValue=r,t.disabled=a,t.name=o,t.required=c,t.tabIndex=-1,m(e)?(t.checked=e.checked,t.defaultChecked=e.defaultChecked,t.value=e.checked?n||"on":""):t.value=n||"",(u=e.syncHiddenFormInput)==null||u.call(e,t)}const P=({component:e})=>(H(e),I("slot",{name:f}));export{P as H,L as c,q as d,y as s};
