<template>
  <div ref="refDiv" class="table-cahrt">
    <div class="left">
      <FormTable :config="TableConfig"></FormTable>
    </div>
    <div class="right">
      <FieldSet
        v-if="!TableConfig.currentRow"
        type="simple"
        title="DMA分区产销差率排行榜"
      ></FieldSet>
      <div class="chart" :class="TableConfig.currentRow ? 'full' : ''">
        <VChart
          v-if="TableConfig.currentRow"
          ref="refChart"
          :option="state.rowOption"
        ></VChart>
        <VChart v-else ref="refChart" :option="state.option"></VChart>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { graphic } from 'echarts';
import { useDetector } from '@/hooks/echarts';
import { useAppStore } from '@/store';
import { GetPartitionLastMonthFlow } from '@/api/mapservice/dma';

const props = defineProps<{
  data: any;
}>();
const TableConfig = reactive<ITable>({
  dataList: [],
  defaultExpandAll: true,
  indexVisible: true,
  rowKey: 'partitionId',
  treeProps: {
    children: 'subPartitionNRWDTOList'
  },
  columns: [
    { fixed: 'left', minWidth: 160, label: '分区名称', prop: 'partitionName' },
    { minWidth: 120, label: '校准产销差(%)', prop: 'nrw' },
    { minWidth: 120, label: '日期', prop: 'date' },
    { minWidth: 120, label: '用户数(户)', prop: 'userNum' },
    {
      minWidth: 120,
      label: 'm³',
      align: 'center',
      prop: 'key',
      subColumns: [
        { minWidth: 120, label: '供水总量', prop: 'supplyTotal' },
        { minWidth: 120, label: '校准用水量', prop: 'correctUseWater' },
        { minWidth: 120, label: '进水量', prop: 'inWater' },
        { minWidth: 120, label: '出水量', prop: 'outWater' },
        { minWidth: 120, label: '漏失总量', prop: 'lossTotal' }
      ]
    }
  ],
  highlightCurrentRow: true,
  pagination: {
    hide: true
  },
  handleRowClick: async (row) => {
    TableConfig.currentRow = row;
    state.rowOption = await initRowOption();
  }
});
const initRowOption = async () => {
  if (!TableConfig.currentRow) return;
  const res = await GetPartitionLastMonthFlow({
    partitionId: TableConfig.currentRow.partitionId
  });
  const xData = res.data?.data?.x || [];
  const yData = res.data?.data?.y || [];
  const row = TableConfig.currentRow || {};

  return {
    title: {
      text: (row.partitionName || '') + '上月流量',
      top: 20
    },
    tooltip: {
      trigger: 'axis'
    },
    grid: {
      containLabel: true,
      left: 20,
      right: 40,
      top: 80,
      bottom: 30
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: xData
    },
    yAxis: {
      name: 'm³',
      type: 'value',
      splitLine: {
        lineStyle: {
          type: 'dashed',
          color: useAppStore().isDark
            ? 'rgba(255,255,255,0.25)'
            : 'rgba(0,0,0,0.25)'
        }
      }
    },
    series: [
      {
        name: row.partitionName,
        type: 'line',
        data: yData,
        smooth: true,
        markPoint: {
          data: [
            { type: 'max', name: 'Max' },
            { type: 'min', name: 'Min' }
          ]
        },
        markLine: {
          data: [{ type: 'average', name: 'Avg' }]
        }
      }
    ]
  };
};
const generateSupplyOption = () => {
  const data = TableConfig.dataList?.[0]?.subPartitionNRWDTOList || [];
  const yData: any[] = [];
  const xData = data.map((item) => {
    yData.push(item.partitionName);
    return item.nrw;
  });
  const max = Math.max(...xData) || 100;
  const isDark = useAppStore().isDark;
  const list = yData.map((item, index) => {
    const obj = {
      name: item,
      value: xData[index],
      num: xData[index]
    };
    return obj;
  });

  const list1 = yData.map((item, index) => {
    const obj = {
      name: item,
      value: max,
      label: {
        show: true,
        position: 'right',
        fontSize: 14,
        color: isDark ? 'rgba(255,255,255,0.8)' : 'rgba(0,0,0,0.8)',
        offset: [16, 0],
        formatter() {
          return xData[index];
        }
      }
    };
    return obj;
  });
  const list2 = yData.map((item, index) => {
    const obj = {
      name: item,
      value: xData[index],
      label: xData[index]
    };
    return obj;
  });
  const options = {
    tooltip: {
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      top: 0,
      left: 10,
      right: 80,
      bottom: 0,
      containLabel: true
    },
    xAxis: {
      type: 'value',
      splitLine: {
        show: false
      },
      axisLine: {
        show: false
      },
      axisLabel: {
        show: false
      },
      axisTick: {
        show: false
      },
      position: 'top'
    },
    yAxis: {
      type: 'category',
      data: yData,
      inverse: true, // 倒叙
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        textStyle: {
          color: isDark ? 'rgba(255,255,255,0.65)' : 'rgba(0,0,0,0.65)',
          fontSize: 14,
          fontFamily: 'TencentSans'
        }
      }
    },
    dataZoom: [
      {
        type: 'slider',
        show: false, // 隐藏或显示（true）组件
        backgroundColor: 'rgba(0,0,0,0)', // 组件的背景颜色。
        fillerColor: isDark ? 'rgba(255,255,255,0.3)' : 'rgba(0,0,0,0.6)', // 选中范围的填充颜色。
        borderColor: 'rgb(0,0,0,0.25)', // 边框颜色
        showDetail: false, // 是否显示detail，即拖拽时候显示详细数值信息
        startValue: 0, // 数据窗口范围的起始数值
        endValue: 5, // 数据窗口范围的结束数值（一页显示多少条数据）
        yAxisIndex: [0], // 控制哪个轴，如果是 number 表示控制一个轴，如果是 Array 表示控制多个轴。此处控制第二根轴
        filterMode: 'empty',
        width: 8, // 滚动条高度
        right: 3, // 距离右边
        handleSize: 0, // 控制手柄的尺寸
        zoomLoxk: true, // 是否锁定选择区域（或叫做数据窗口）的大小
        top: 10,
        height: '90%'
      },
      {
        // 没有下面这块的话，只能拖动滚动条，鼠标滚轮在区域内不能控制外部滚动条
        type: 'inside',
        yAxisIndex: [0, 1], // 控制哪个轴，如果是 number 表示控制一个轴，如果是 Array 表示控制多个轴。此处控制第二根轴
        zoomOnMouseWheel: false, // 滚轮是否触发缩放
        moveOnMouseMove: true, // 鼠标移动能否触发平移
        moveOnMouseWheel: true // 鼠标滚轮能否触发平移
      }
    ],

    series: [
      {
        type: 'bar',
        barGap: '-100%',
        barWidth: 14,
        z: 1,
        itemStyle: {
          color: new graphic.LinearGradient(
            0,
            0,
            1,
            0,
            [
              {
                offset: 0,
                color: 'rgba(0,255,255,1)'
              },
              {
                offset: 1,
                color: 'rgba(255,0,0,1)'
              }
            ],
            false
          )
        },
        data: list
      },
      {
        type: 'bar',
        barWidth: 14,
        z: 0,
        itemStyle: {
          color: isDark ? 'rgba(26, 49, 99, 1)' : 'rgba(26, 49, 99, 0.1)'
        },
        tooltip: {
          show: false
        },
        data: list1
      },
      {
        type: 'pictorialBar',
        symbolRepeat: 'fixed',
        symbolMargin: 6,
        symbol: 'rect',
        z: 2,
        symbolClip: true,
        symbolSize: [1, 14],
        symbolPosition: 'start',
        itemStyle: {
          color: isDark ? 'rgba(255,255,255,0.6)' : 'rgba(0,0,0,0.6)'
        },
        data: list2
      }
    ]
  };
  return options;
};
const state = reactive<{
  option: any;
  rowOption: any;
}>({
  option: null,
  rowOption: null
});
const refreshTable = () => {
  TableConfig.dataList = [props.data?.partitionNRW];
  TableConfig.currentRow = undefined;
  state.option = generateSupplyOption();
};
const refDiv = ref();
const refChart = ref();
const detector = useDetector();
watch(
  () => props.data,
  () => {
    refreshTable();
  }
);
onMounted(() => {
  detector.listenToMush(refDiv.value, () => {
    refChart.value?.resize();
  });
});
</script>
<style lang="scss" scoped>
.table-cahrt {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  .left,
  .right {
    height: 100%;
  }
  .left {
    width: 60%;
  }
  .right {
    width: 40%;
    padding: 0 12px;
    .chart {
      height: calc(100% - 62px);
      width: 100%;
      &.full {
        height: 100%;
      }
    }
  }
}
</style>
