package org.thingsboard.server.dao.base.impl;

import java.util.List;
import java.util.UUID;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.base.IBaseDatabaseConnectionService;
import org.thingsboard.server.dao.model.sql.base.BaseDatabaseConnection;
import org.thingsboard.server.dao.sql.base.BaseDatabaseConnectionMapper;
import org.thingsboard.server.dao.util.imodel.query.base.BaseDatabaseConnectionPageRequest;

/**
 * 平台管理-数据库连接Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
@Service
public class BaseDatabaseConnectionServiceImpl implements IBaseDatabaseConnectionService {

    @Autowired
    private BaseDatabaseConnectionMapper baseDatabaseConnectionMapper;

    /**
     * 查询平台管理-数据库连接
     *
     * @param id 平台管理-数据库连接主键
     * @return 平台管理-数据库连接
     */
    @Override
    public BaseDatabaseConnection selectBaseDatabaseConnectionById(String id) {
        return baseDatabaseConnectionMapper.selectBaseDatabaseConnectionById(id);
    }

    /**
     * 查询平台管理-数据库连接列表
     *
     * @param baseDatabaseConnection 平台管理-数据库连接
     * @return 平台管理-数据库连接
     */
    @Override
    public IPage<BaseDatabaseConnection> selectBaseDatabaseConnectionList(BaseDatabaseConnectionPageRequest baseDatabaseConnection) {
        return baseDatabaseConnectionMapper.selectBaseDatabaseConnectionList(baseDatabaseConnection);
    }

    /**
     * 新增平台管理-数据库连接
     *
     * @param baseDatabaseConnection 平台管理-数据库连接
     * @return 结果
     */
    @Override
    public int insertBaseDatabaseConnection(BaseDatabaseConnection baseDatabaseConnection) {
        baseDatabaseConnection.setId(UUID.randomUUID().toString().replace("-", ""));
        return baseDatabaseConnectionMapper.insertBaseDatabaseConnection(baseDatabaseConnection);
    }

    /**
     * 修改平台管理-数据库连接
     *
     * @param baseDatabaseConnection 平台管理-数据库连接
     * @return 结果
     */
    @Override
    public int updateBaseDatabaseConnection(BaseDatabaseConnection baseDatabaseConnection) {
        return baseDatabaseConnectionMapper.updateBaseDatabaseConnection(baseDatabaseConnection);
    }

    /**
     * 批量删除平台管理-数据库连接
     *
     * @param ids 需要删除的平台管理-数据库连接主键
     * @return 结果
     */
    @Override
    public int deleteBaseDatabaseConnectionByIds(List<String> ids) {
        return baseDatabaseConnectionMapper.deleteBaseDatabaseConnectionByIds(ids);
    }

    /**
     * 删除平台管理-数据库连接信息
     *
     * @param id 平台管理-数据库连接主键
     * @return 结果
     */
    @Override
    public int deleteBaseDatabaseConnectionById(String id) {
        return baseDatabaseConnectionMapper.deleteBaseDatabaseConnectionById(id);
    }
}
