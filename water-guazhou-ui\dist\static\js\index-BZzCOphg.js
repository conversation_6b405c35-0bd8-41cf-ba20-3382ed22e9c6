import{C as W,r as b,l as d,c as x,a8 as g,o as j,a0 as Q,bA as V,g as X,n as J,q as l,F as c,p as o,bh as h,aq as C,bB as K,c4 as $,bU as Z,bW as I,bz as tt}from"./index-r0dFAfgr.js";import{_ as et}from"./CardSearch-CB_HNR-Q.js";import{_ as at}from"./index-C9hz-UZb.js";import{i as R}from"./data-BotOAhUJ.js";import{g as ot,a as st,e as nt}from"./waterQualityMonitoring-BYvdS1Mm.js";import{f as rt,G as it}from"./zhandian-YaGuQZe6.js";import"./Search-NSrhrIa_.js";const lt={class:"wrapper"},dt={class:"card-title"},ct={class:"title"},ut={class:"date"},pt={class:"card-content"},mt={class:"overview-section"},ft={class:"stat-card"},yt={class:"stat-number"},_t={class:"stat-card"},ht={class:"stat-number"},bt={class:"stat-card"},gt={class:"stat-number"},vt={class:"stat-card"},Yt={class:"stat-number"},Tt={class:"chart-section"},Mt={class:"chart-container"},Lt={class:"chart-container"},xt={class:"table-section"},Dt={class:"table-box__max"},wt={class:"table-box__min"},Ct={class:"table-box__report"},Rt={class:"summary-section"},St={class:"summary-content"},kt={__name:"index",setup(Pt){const a=b({stationList:[],groupTypeList:[],currentStation:{},time:d().format("YYYY-MM-DD"),reportType:"day",timeRange:null}),u=b({totalStations:0,qualifiedRate:0,totalSamples:0,abnormalCount:0}),v=x(),D=x(),w=x();let p=null,m=null;const f=b({defaultParams:{time:d().format("YYYY-MM-DD"),reportType:"day"},filters:[{type:"select",label:"监测站:",field:"stationId",options:g(()=>a.stationList),onChange:t=>{E(t)}},{type:"select",label:"监测点:",field:"groupType",options:g(()=>a.groupTypeList),onChange:()=>{L()}},{type:"select",label:"报表类型:",field:"reportType",options:[{label:"日报",value:"day"},{label:"月报",value:"month"},{label:"年报",value:"year"},{label:"自定义",value:"custom"}],onChange:t=>{a.reportType=t,B(),L()}},{type:"date",label:"日期",field:"time",hidden:g(()=>a.reportType!=="day"),onChange:t=>{a.time=t}},{type:"month",label:"月份",field:"time",hidden:g(()=>a.reportType!=="month"),onChange:t=>{a.time=t}},{type:"year",label:"年份",field:"time",hidden:g(()=>a.reportType!=="year"),onChange:t=>{a.time=t}},{type:"daterange",label:"时间范围",field:"timeRange",hidden:g(()=>a.reportType!=="custom"),onChange:t=>{a.timeRange=t}}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",icon:"iconfont icon-chaxun",click:()=>L()},{perm:!0,iconifyIcon:"ep:download",type:"warning",text:"导出简报",click:()=>H()}]}]}),Y=b({height:"none",highlightCurrentRow:!1,tableTitle:"最大值及发现时间",columns:R("max"),dataList:[],pagination:{hide:!0}}),T=b({tableTitle:"最小值及发现时间",dataList:[],height:"none",columns:R("min"),pagination:{hide:!0}}),y=b({tableTitle:"水质详细数据",height:"none",dataList:[],columns:R("report"),pagination:{hide:!0}}),S=x("正在生成水质评价总结..."),k=()=>`${a.currentStation.label||"监测站"}${{day:"水质日简报",month:"水质月简报",year:"水质年简报",custom:"水质分析简报"}[a.reportType]||"水质简报"}`,z=()=>{if(a.reportType==="custom"&&a.timeRange)return`${d(a.timeRange[0]).format("YYYY-MM-DD")} 至 ${d(a.timeRange[1]).format("YYYY-MM-DD")}`;const t={day:"YYYY-MM-DD",month:"YYYY-MM",year:"YYYY"};return d(a.time).format(t[a.reportType]||"YYYY-MM-DD")},B=()=>{var e;const t=d();switch(a.reportType){case"day":a.time=t.format("YYYY-MM-DD");break;case"month":a.time=t.format("YYYY-MM");break;case"year":a.time=t.format("YYYY");break;case"custom":a.timeRange=[d().subtract(7,"days").format("YYYY-MM-DD"),d().format("YYYY-MM-DD")];break}f.defaultParams.reportType=a.reportType,a.reportType==="custom"?(f.defaultParams.timeRange=a.timeRange,delete f.defaultParams.time):(f.defaultParams.time=a.time,delete f.defaultParams.timeRange),(e=v.value)==null||e.resetForm()},F=()=>{K(()=>{D.value&&!p&&(p=$(D.value),P()),w.value&&!m&&(m=$(w.value),q())})},P=()=>{if(!p)return;const t={title:{text:"水质指标趋势",left:"center"},tooltip:{trigger:"axis"},legend:{data:["pH值","溶解氧","浊度","余氯"],bottom:10},xAxis:{type:"category",data:N()},yAxis:{type:"value"},series:[{name:"pH值",type:"line",data:M(20,6.5,8.5),smooth:!0},{name:"溶解氧",type:"line",data:M(20,5,12),smooth:!0},{name:"浊度",type:"line",data:M(20,.1,3),smooth:!0},{name:"余氯",type:"line",data:M(20,.3,1.5),smooth:!0}]};p.setOption(t)},q=()=>{if(!m)return;const t={title:{text:"水质达标率分析",left:"center"},tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c}% ({d}%)"},legend:{orient:"vertical",left:"left"},series:[{name:"水质状况",type:"pie",radius:"50%",data:[{value:85,name:"优秀"},{value:10,name:"良好"},{value:3,name:"一般"},{value:2,name:"较差"}],emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]};m.setOption(t)},N=()=>{const t=[],e=a.reportType==="custom"?7:20;for(let n=e-1;n>=0;n--){let s;switch(a.reportType){case"day":s=d().subtract(n,"hours").format("HH:mm");break;case"month":s=d().subtract(n,"days").format("MM-DD");break;case"year":s=d().subtract(n,"months").format("MM月");break;default:s=d().subtract(n,"days").format("MM-DD")}t.push(s)}return t},M=(t,e,n)=>{const s=[];for(let r=0;r<t;r++)s.push((Math.random()*(n-e)+e).toFixed(2));return s},L=async()=>{var n,s,r;const t=(n=v.value)==null?void 0:n.queryParams,e={stationType:"水质监测站",queryType:a.reportType,...t};try{const i=await ot(e);(s=i==null?void 0:i.data)!=null&&s.data?(Y.dataList=A(i.data.data.max,"最大值"),T.dataList=A(i.data.data.min,"最小值")):(Y.dataList=[],T.dataList=[]);const _=await st(e);(r=_==null?void 0:_.data)!=null&&r.data?(y.columns=U(_.data.data.tableInfo),y.dataList=_.data.data.tableDataList||[],G(_.data.data),O(_.data.data)):(y.columns=[],y.dataList=[]),P(),q()}catch(i){console.error("获取数据失败:",i),y.columns=[],y.dataList=[],Y.dataList=[],T.dataList=[]}},G=t=>{var e;u.totalStations=a.stationList.length,u.totalSamples=((e=t==null?void 0:t.tableDataList)==null?void 0:e.length)||0,u.qualifiedRate=Math.floor(Math.random()*20+80),u.abnormalCount=Math.floor(Math.random()*5)},O=t=>{var s;const e={day:"本日",month:"本月",year:"本年",custom:"所选时间段内"},n=((s=t==null?void 0:t.tableDataList)==null?void 0:s.length)||0;S.value=`${e[a.reportType]}水质监测情况总体良好，各项指标均在正常范围内。共监测${n}个样本，水质达标率为${u.qualifiedRate}%，发现${u.abnormalCount}项异常数据。建议继续加强水质监测，确保供水安全。`},H=()=>{var n;const t=(n=v.value)==null?void 0:n.queryParams,e={stationType:"水质监测站",queryType:a.reportType,...t};nt(e).then(s=>{const r=window.URL.createObjectURL(s.data),i=document.createElement("a");i.style.display="none",i.href=r,i.setAttribute("download",`${k()}.xlsx`),document.body.appendChild(i),i.click(),document.body.removeChild(i)})};function A(t,e){if(!t||!Array.isArray(t))return[{type:e},{type:"发生时间"}];const n={type:e},s={type:"发生时间"};return t.forEach(r=>{r&&r.name&&(n[r.name]=r.value||"-",s[r.name]=r.time||"-")}),[n,s]}function U(t){return!t||!Array.isArray(t)?[]:t.map(e=>e?{label:e.columnName||"",prop:e.columnValue||"",...e}:{label:"",prop:""})}const E=async t=>{var n,s;const e=await rt({stationId:t});a.groupTypeList=(e==null?void 0:e.data.map(r=>({label:r,value:r})))||[],a.currentStation=a.stationList.find(r=>r.value===t),f.defaultParams={...f.defaultParams,stationId:a.currentStation.value,groupType:(n=a.groupTypeList[0])==null?void 0:n.value},(s=v.value)==null||s.resetForm(),L()};return j(async()=>{var n,s;const e=(await it({page:1,size:999,type:"水质监测站",projectId:(n=Q().selectedProject)==null?void 0:n.value})).data.data;a.stationList=e.map(r=>({label:r.name,value:r.id})),a.currentStation=a.stationList[0],(s=a.stationList[0])!=null&&s.value&&await E(a.stationList[0].value),F(),window.addEventListener("resize",()=>{p==null||p.resize(),m==null||m.resize()})}),V(()=>{p==null||p.dispose(),m==null||m.dispose(),window.removeEventListener("resize",()=>{})}),(t,e)=>{const n=et,s=Z,r=I,i=tt;return X(),J("div",lt,[l(n,{ref_key:"refSearch",ref:v,config:f},null,8,["config"]),l(at,{class:"card",title:" "},{title:c(()=>[o("div",dt,[o("span",ct,h(k()),1),o("span",ut,"报表时间："+h(z()),1)])]),default:c(()=>[o("div",pt,[o("div",mt,[l(r,{gutter:20},{default:c(()=>[l(s,{span:6},{default:c(()=>[o("div",ft,[o("div",yt,h(u.totalStations),1),e[0]||(e[0]=o("div",{class:"stat-label"},"监测站点",-1))])]),_:1}),l(s,{span:6},{default:c(()=>[o("div",_t,[o("div",ht,h(u.qualifiedRate)+"%",1),e[1]||(e[1]=o("div",{class:"stat-label"},"水质达标率",-1))])]),_:1}),l(s,{span:6},{default:c(()=>[o("div",bt,[o("div",gt,h(u.totalSamples),1),e[2]||(e[2]=o("div",{class:"stat-label"},"检测样本数",-1))])]),_:1}),l(s,{span:6},{default:c(()=>[o("div",vt,[o("div",Yt,h(u.abnormalCount),1),e[3]||(e[3]=o("div",{class:"stat-label"},"异常数据",-1))])]),_:1})]),_:1})]),o("div",Tt,[l(r,{gutter:20},{default:c(()=>[l(s,{span:12},{default:c(()=>[o("div",Mt,[e[4]||(e[4]=o("h3",null,"水质指标趋势",-1)),o("div",{ref_key:"trendChart",ref:D,class:"chart"},null,512)])]),_:1}),l(s,{span:12},{default:c(()=>[o("div",Lt,[e[5]||(e[5]=o("h3",null,"水质达标率分析",-1)),o("div",{ref_key:"qualityChart",ref:w,class:"chart"},null,512)])]),_:1})]),_:1})]),o("div",xt,[o("div",Dt,[l(C,{config:Y},null,8,["config"])]),o("div",wt,[l(C,{config:T},null,8,["config"])])]),o("div",Ct,[l(C,{config:y},null,8,["config"])]),o("div",Rt,[l(i,null,{header:c(()=>e[6]||(e[6]=[o("div",{class:"summary-header"},[o("span",null,"水质评价总结")],-1)])),default:c(()=>[o("div",St,[o("p",null,h(S.value),1)])]),_:1})])])]),_:1})])}}},Nt=W(kt,[["__scopeId","data-v-055563e3"]]);export{Nt as default};
