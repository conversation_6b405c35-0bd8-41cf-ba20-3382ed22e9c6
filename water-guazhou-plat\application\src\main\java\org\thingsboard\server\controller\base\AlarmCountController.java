package org.thingsboard.server.controller.base;

import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.dao.alarm.AlarmCountService;

@RestController
@RequestMapping("api/alarmcount")
public class AlarmCountController extends BaseController {

    @Autowired
    private AlarmCountService alarmCountService;

    @PostMapping("powerQuantityAlarmCount")
    public Object powerQuantityCount(@RequestBody JSONObject params) throws ThingsboardException {
        return alarmCountService.powerQuantityAlarmCount(params, getTenantId());
    }

}
