package org.thingsboard.server.dao.util;

import com.alibaba.fastjson.JSONObject;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.thingsboard.server.dao.sql.smartPipe.PartitionMountMapper;
import org.thingsboard.server.dao.sql.smartPipe.PipePartitionTotalFlowMapper;

import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2023-04-23
 */
@Component
public class InfluxUtil {
    @Autowired
    private PartitionMountMapper partitionMountMapper;

    @Autowired
    private PipePartitionTotalFlowMapper pipePartitionTotalFlowMapper;

    @Value("${hostname.myhost}")
    private String myHost;

    public JSONObject getData(List<String> deviceList, Long start, Long end, String type) {
        // 先找所有分区关联的流量计
        CloseableHttpClient httpClient = HttpClientBuilder.create().build();

            HttpPost httpPost = new HttpPost(myHost + "/istar/api/device/dataNoAuth");
        httpPost.setHeader("Content-Type", "application/json");
        JSONObject body = new JSONObject();
        body.put("start", start);
        body.put("end", end);
        body.put("type", type);
        body.put("attributes", deviceList);

        StringEntity stringEntity = new StringEntity(body.toString(), StandardCharsets.UTF_8);
        stringEntity.setContentType("application/json");
        httpPost.setEntity(stringEntity);

        try {
            CloseableHttpResponse execute = httpClient.execute(httpPost);
            if (execute.getStatusLine().getStatusCode() != 200) {
                return new JSONObject();
            }
            String resultString = EntityUtils.toString(execute.getEntity());
            JSONObject jsonObject = JSONObject.parseObject(resultString, JSONObject.class);
            return jsonObject;
        } catch (Exception e) {
            return new JSONObject();
        }
    }
}
