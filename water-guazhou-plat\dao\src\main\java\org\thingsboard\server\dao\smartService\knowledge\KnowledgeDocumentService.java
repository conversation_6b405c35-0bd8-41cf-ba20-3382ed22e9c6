package org.thingsboard.server.dao.smartService.knowledge;

import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.smartService.knowledge.KnowledgeDocument;

import java.util.List;

/**
 * 知识库
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-10-27
 */
public interface KnowledgeDocumentService {
    PageData getList(String typeId, String name, int page, int size, String tenantId);

    KnowledgeDocument save(KnowledgeDocument knowledgeDocument);

    int delete(List<String> ids);
}
