package org.thingsboard.server.dao.model.sql.store;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseTenantName;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;
import org.thingsboard.server.dao.util.imodel.response.tree.Identifiable;

import java.util.Date;

@Getter
@Setter
@ResponseEntity
public class GoodsShelf implements Identifiable {
    // id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    // 父级ID
    private String parentId;

    // 父级名称
    @TableField(exist = false)
    private String parentName;

    // 货架编码
    private String code;

    // 货架名称
    private String name;

    // 排序，升序
    private Integer orderNum;

    // 备注
    private String remark;

    // 层级
    private Integer layer;

    // 创建时间
    private Date createTime;

    // 租户ID
    @ParseTenantName
    private String tenantId;
}
