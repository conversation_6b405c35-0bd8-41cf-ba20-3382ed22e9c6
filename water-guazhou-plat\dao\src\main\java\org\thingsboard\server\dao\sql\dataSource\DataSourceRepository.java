package org.thingsboard.server.dao.sql.dataSource;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.thingsboard.server.dao.model.sql.DataSourceEntity;
import org.thingsboard.server.dao.util.SqlDao;

import java.util.List;

@SqlDao
public interface DataSourceRepository extends CrudRepository<DataSourceEntity, String> {

    @Query("SELECT d FROM DataSourceEntity d WHERE d.id in (select p.dataSourceId from DataSourceRelationEntity p where p.originateId =:projectId) and d.type = :type and d.enable =:enable order by d.updateTime")
    List<DataSourceEntity> findByProjectIdAndType(@Param("projectId") String projectId,
                                                  @Param("type") String type,
                                                  @Param("enable") int enable);


    @Query("SELECT d FROM DataSourceEntity d WHERE d.id in (select p.dataSourceId from DataSourceRelationEntity p where p.originateId =:originatorId) and d.type = :type ")
    List<DataSourceEntity> findAllByOriginatorIdAndType(@Param("originatorId") String originatorId,
                                                        @Param("type") String type);

    @Query("SELECT d FROM DataSourceEntity d WHERE d.id in (select p.dataSourceId from DataSourceRelationEntity p where p.originateId in :originatorId) and d.type = :type and d.property = :property")
    List<DataSourceEntity> findByOriginatorIdInAndTypeAndProperty(@Param("originatorId") List<String> originatorIds,
                                                                  @Param("type") String type, @Param("property") String property);

    @Query("SELECT DISTINCT d, dr.originateId FROM DataSourceEntity d, DataSourceRelationEntity dr " +
            "WHERE d.id = dr.dataSourceId AND d.type = ?1 AND d.tenantId = ?2")
    List findByTypeAndTenantId(String type, String tenantId);


    @Query("SELECT d FROM DataSourceEntity d WHERE d.id in (select p.dataSourceId from DataSourceRelationEntity p where p.originateId =:projectId)  and d.enable =:enable")
    List<DataSourceEntity> findByProjectId(@Param("projectId") String projectId,
                                                  @Param("enable") int enable);


    @Query("SELECT count (d.id) FROM DataSourceEntity d WHERE d.id in (select p.dataSourceId from DataSourceRelationEntity p where p.originateId =:originatorI)  and d.sourceName =:name")
    int countByOriginatorId(String originatorId,String name);

    List<DataSourceEntity> findByIdIn(List<String> dataSourceIds);
}
