<template>
  <div
    class="video_box"
    @dblclick="wholeFullScreen"
  >
    <div
      ref="DPlayerIns"
      style="width: 100%; height: 100%"
    ></div>
  </div>
</template>

<script lang="ts" setup>
import Player from 'xgplayer'
import 'xgplayer/dist/index.min.css'
import FlvPlugin from 'xgplayer-flv'
import HlsPlugin from 'xgplayer-hls'

const props = defineProps<{
  videoInfo: DPlayerIns
}>()
const DPlayerIns = ref<HTMLDivElement>()
const dp = ref()

function initXGPlayer() {
  dp.value = new Player({
    el: DPlayerIns.value,
    plugins: props.videoInfo.video.type === 'hls' ? [HlsPlugin] : [FlvPlugin],
    autoplay: props.videoInfo?.autoplay ?? false,
    url: props.videoInfo.video.url,
    height: '100%',
    width: '100%',
  })
}

// 最大化
const wholeFullScreen = () => {
  dp.value.getFullscreen()
}

onMounted(() => {
  initXGPlayer()
})

const destroy = () => {
  dp.value?.destroy()
  dp.value = null
}

onUnmounted(() => {
  destroy()
})

defineExpose({
  destroy,
})
</script>

<style lang="scss" scoped>
.video_box {
  width: 100%;
  height: 100%;
  z-index: 100000;
}
</style>
