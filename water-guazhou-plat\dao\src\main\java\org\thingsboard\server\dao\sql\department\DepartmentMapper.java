package org.thingsboard.server.dao.sql.department;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.thingsboard.server.dao.model.sql.department.Department;
import org.thingsboard.server.dao.util.imodel.query.department.DepartmentPageRequest;
import org.thingsboard.server.dao.util.imodel.response.DepartmentInfo;

import java.util.List;

@Mapper
public interface DepartmentMapper extends BaseMapper<Department> {
    @Select("select * from tb_department where parent_id = #{pid} and tenant_id = #{tenantId}")
    List<Department> findRoots(@Param("pid") String pid, @Param("tenantId") String tenantId);

    List<Department> findChildren(@Param("pid") String pid, @Param("tenantId") String tenantId);

    boolean canBeDelete(String id);

    boolean canBeAdd(String parentId);

    boolean update(Department department);

    List<Department> findAll(DepartmentPageRequest department);

    default String getNameById(String id) {
        return getNameByMultiId(id);
    }

    String getDepartmentId(String userId);

    @SuppressWarnings("unused")
    default String getDepartmentName(String id) {
        return getNameById(id);
    }

    String getDirectOrgByDepartmentId(String id);

    boolean removeAllDepartment(String tenantId);

    String getNameByMultiId(String multiId);

    DepartmentInfo getInfoById(String departmentId);

    int deleteWithChildrenRecursive(String id);

    String countUserByDepartment(String tenantId);


}
