package org.thingsboard.server.controller.maintainCircuit.circuit;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.maintainCircuit.circuit.CircuitPlanMService;
import org.thingsboard.server.dao.model.sql.maintainCircuit.circuit.CircuitPlanM;
import org.thingsboard.server.dao.util.imodel.StringUtils;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.Date;
import java.util.List;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-09-08
 */
@RestController
@RequestMapping("api/circuit/plan/m")
public class CircuitPlanMController extends BaseController {

    @Autowired
    private CircuitPlanMService circuitPlanMService;

    @GetMapping
    public IstarResponse getList(@RequestParam(required = false, defaultValue = "") String planName,
                                 @RequestParam(required = false, defaultValue = "") String teamName,
                                 @RequestParam(required = false, defaultValue = "") String userName,
                                 Long startStartTime,
                                 Long startEndTime,
                                 Long endStartTime,
                                 Long endEndTime,
                                 int page, int size) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());

        return IstarResponse.ok(circuitPlanMService.getList(planName, teamName, userName, startStartTime == null ? null : new Date(startStartTime), startEndTime == null ? null : new Date(startEndTime), endStartTime == null ? null : new Date(endStartTime), endEndTime == null ? null : new Date(endEndTime), page, size, tenantId));
    }

    @GetMapping("detail/{mainId}")
    public IstarResponse getDetail(@PathVariable String mainId) {
        return IstarResponse.ok(circuitPlanMService.getDetail(mainId));
    }

    @PostMapping
    public IstarResponse save(@RequestBody CircuitPlanM circuitPlanM) throws ThingsboardException {
        // 审核过不许修改
        if (!StringUtils.isNullOrEmpty(circuitPlanM.getId()) && !"0".equals(circuitPlanM.getStatus())) {
            return IstarResponse.error("改计划已审核，不允许修改！");
        }
        circuitPlanM.setCreator(UUIDConverter.fromTimeUUID(getCurrentUser().getUuidId()));
        circuitPlanM.setTenantId(UUIDConverter.fromTimeUUID(getTenantId().getId()));
        return IstarResponse.ok(circuitPlanMService.save(circuitPlanM));
    }

    @PostMapping("reviewer")
    public IstarResponse reviewer(@RequestBody CircuitPlanM circuitPlanM) throws ThingsboardException {
        circuitPlanM.setReviewer(UUIDConverter.fromTimeUUID(getCurrentUser().getUuidId()));
        circuitPlanMService.reviewer(circuitPlanM);

        return IstarResponse.ok("审核成功");
    }

    @DeleteMapping
    public IstarResponse delete(@RequestBody List<String> ids) {
        return circuitPlanMService.delete(ids);
    }



    /**
     * 保养计划
     */
    @GetMapping("list/{deviceLabelCode}")
    public IstarResponse getList(@PathVariable String deviceLabelCode, int page, int size) {
        return IstarResponse.ok(circuitPlanMService.getMaintainList(deviceLabelCode, page, size));
    }
}
