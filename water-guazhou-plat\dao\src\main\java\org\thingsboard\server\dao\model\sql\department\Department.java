package org.thingsboard.server.dao.model.sql.department;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseTenantName;
import org.thingsboard.server.dao.util.imodel.response.tree.Identifiable;

import java.util.Date;

@Getter
@Setter
@TableName("tb_department")
public class Department implements Identifiable {
    @TableId(type = IdType.ASSIGN_UUID)
    // "ID"
    private String id;

    // "父级ID"
    private String parentId;

    @TableField(exist = false)
    private String parentName;

    // "部门名称"
    private String name;

    // "部门类型"
    private String type;

    // "排序，升序"
    private Integer orderNum;

    // "创建时间"
    private Date createTime;

    // "租户ID"
    @ParseTenantName
    private String tenantId;

    @TableField(exist = false)
    private Integer layer;
}
