package org.thingsboard.server.dao.data_source_relation;

import org.thingsboard.server.common.data.dataSource.DataFromDataSource;
import org.thingsboard.server.common.data.dataSource.DataSourceRequest;
import org.thingsboard.server.common.data.dataSource.DataSourceType;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.dao.model.sql.DataSourceEntity;
import org.thingsboard.server.dao.model.sql.OriginDataEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/2/26 14:35
 */
public interface DataSourceRelationService {

  boolean mountRelation(String originateId,List<String> dataSourceId,String type);

}
