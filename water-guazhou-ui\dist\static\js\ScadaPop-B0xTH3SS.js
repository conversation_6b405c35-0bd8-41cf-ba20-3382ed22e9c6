import{d as T,r as m,a8 as k,am as C,o as B,g as s,n as L,q as n,F as c,p as _,h as l,i,dt as A,cU as E,bl as I,aq as S,bm as D,C as N}from"./index-r0dFAfgr.js";import{g as P,d as q}from"./zhandian-YaGuQZe6.js";import{f as F}from"./DateFormatter-Bm9a68Ax.js";const G={class:"pop-image"},U={class:"content attrs overlay-y"},V={class:"content alarms overlay-y"},z=T({__name:"ScadaPop",props:{visible:{type:Boolean},config:{}},setup(g){const o=g,p=m({attributes:[]}),f=k(()=>{var t,a;return((a=(t=o.config.info)==null?void 0:t.imageUrl)==null?void 0:a.split(","))||[]}),u=m({maxHeight:300,dataList:[],columns:[{label:"报警时间",prop:"startTs",formatter:t=>F(t.startTs)},{label:"状态",prop:"type"}],pagination:{hide:!0}}),r=async t=>{try{t==="1"?await b():t==="0"&&o.config.info.type==="attrs"&&await y()}catch(a){console.log(a)}},b=async()=>{try{const t=await P(o.config.info.stationId);u.dataList=t.data||[]}catch(t){console.log(t)}},y=async()=>{var a;const t=await q(o.config.info.stationId);p.attributes=(a=t.data)==null?void 0:a.map(e=>({label:e.propertyName,value:(e.value||"--")+" "+(e.unit||""),data:e}))};return C(()=>o.visible,()=>{r("0")}),B(()=>{r("0")}),(t,a)=>{const e=A,h=E,d=I,v=S,w=D;return s(),L("div",G,[n(w,{"tab-position":"right",onTabChange:r},{default:c(()=>[n(d,{label:"信息"},{default:c(()=>[_("div",U,[t.config.info.type==="custom-attrs"?(s(),l(e,{key:0,columns:t.config.info.columns,attributes:t.config.info.attributes,data:t.config.info.attrData,rows:t.config.info.rows},null,8,["columns","attributes","data","rows"])):t.config.info.type==="attrs"?(s(),l(e,{key:1,attributes:i(p).attributes},null,8,["attributes"])):(s(),l(h,{key:2,"preview-teleported":!0,"close-on-press-escape":!0,"hide-on-click-modal":!0,style:{width:"100%",height:"100%"},src:i(f)[0],"preview-src-list":i(f),fit:"cover"},null,8,["src","preview-src-list"]))])]),_:1}),n(d,{label:"报警",lazy:!0},{default:c(()=>[_("div",V,[n(v,{config:i(u)},null,8,["config"])])]),_:1})]),_:1})])}}}),j=N(z,[["__scopeId","data-v-8ddf155f"]]);export{j as default};
