import{d as y,c as m,am as T,o as w,g as i,h,F as c,p as r,q as _,i as p,cs as k,G as V,t as N,bb as B,ct as E,bk as A,d8 as F,C as D}from"./index-r0dFAfgr.js";const I={style:{cursor:"pointer"}},K={class:"filter-box"},P={class:"filter-header"},q={class:"filter-tree"},G=y({__name:"FormTableColumnFilter",props:{columns:{},showTooltip:{type:Boolean}},setup(C){const l=C,n=m(),b={children:"subColumns",label:"label"},a=m(!0),f=(o,e=[])=>{var t;e.push(o.prop),(t=o.subColumns)!=null&&t.length&&o.subColumns.map(s=>f(s,e))},d=o=>{var t,s;const e=[];l.columns.map(u=>f(u,e)),o?(t=n.value)==null||t.setCheckedKeys(e):(s=n.value)==null||s.setCheckedKeys([])},g=(o,e)=>{o.hidden=!e},v=o=>{var e;(e=n.value)==null||e.setChecked(o.data.prop,o.checked,!1)};return T(()=>l.columns,()=>{a.value=!0,d(!0)}),w(()=>{d(!0)}),(o,e)=>{const t=E,s=A,u=F;return i(),h(u,{placement:"right",trigger:"click",width:200},{reference:c(()=>[r("span",I,[l.showTooltip?(i(),h(t,{key:0,content:"过滤表格字段"},{default:c(()=>[_(p(k),{icon:"ep:filter"})]),_:1})):(i(),h(p(k),{key:1,icon:"ep:filter"}))])]),default:c(()=>[r("div",K,[r("div",P,[_(s,{modelValue:p(a),"onUpdate:modelValue":e[0]||(e[0]=x=>N(a)?a.value=x:null),onChange:d},{default:c(()=>e[1]||(e[1]=[V(" 全选 ")])),_:1},8,["modelValue"])]),r("div",q,[_(p(B),{ref_key:"refTree",ref:n,data:l.columns,"show-checkbox":"","default-expand-all":"","node-key":"prop",props:b,draggable:!0,"check-strictly":!0,onCheckChange:g,onNodeDrop:v},null,8,["data"])])])]),_:1})}}}),R=D(G,[["__scopeId","data-v-972a819c"]]);export{R as _};
