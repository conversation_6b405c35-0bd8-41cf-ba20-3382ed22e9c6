package org.thingsboard.server.dao.dispatch;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.smartProduction.dispatch.OrderRecordType;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch.OrderRecordTypePageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch.OrderRecordTypeSaveRequest;

public interface OrderRecordTypeService {
    /**
     * 分页条件查询指令记录类型
     *
     * @param request 分页查询条件
     * @return 分页查询结果
     */
    IPage<OrderRecordType> findAllConditional(OrderRecordTypePageRequest request);

    /**
     * 保存询指令记录类型
     *
     * @param entity 实体信息
     * @return 保存好的实体
     */
    OrderRecordType save(OrderRecordTypeSaveRequest entity);

    /**
     * 增量修改
     *
     * @param entity 实体信息
     * @return 是否修改成功
     */
    boolean update(OrderRecordType entity);

    /**
     * 删除
     *
     * @param id 唯一标识
     * @return 是否删除成功
     */
    boolean delete(String id);

    /**
     * 通过id获取指令记录类型
     *
     * @param id 指令记录类型id
     * @return 指令记录类型
     */
    OrderRecordType findById(String id);

}
