<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.smartPipe.PartitionMapper">

    <select id="getRootIdNameMapList" resultType="org.thingsboard.server.dao.model.DTO.PartitionTreeDTO">
        select a.id, a.name, a.type from tb_pipe_partition a where (a.pid is null or a.pid = '') and a.tenant_id = #{tenantId} order by order_num, a.create_time desc
    </select>

    <select id="getRootIdNameList" resultType="org.thingsboard.server.dao.model.sql.smartPipe.dma.Partition">
        select a.id, a.name from tb_pipe_partition a where (a.pid is null or a.pid = '') and a.tenant_id = #{tenantId} order by order_num, a.create_time desc
    </select>

    <select id="getAllIdNameByPid" resultType="org.thingsboard.server.dao.model.sql.smartPipe.dma.Partition">
        select * from tb_pipe_partition a where a.pid = #{pid} order by order_num, a.create_time desc
    </select>

    <select id="getIdNameById" resultType="org.thingsboard.server.dao.model.DTO.PartitionTreeDTO">
        select a.id, a.name from tb_pipe_partition a where a.id = #{partitionId} order by order_num, a.create_time desc
    </select>

    <select id="getAll" resultType="org.thingsboard.server.dao.model.sql.smartPipe.dma.Partition">
        select a.* from tb_pipe_partition a
       <where>
           and a.tenant_id = #{param.tenantId}
        <if test="param.name != null and param.name != ''">
            and a.name like '%'||#{name}||'%'
        </if>
        <if test="param.partitionIdList != null and param.partitionIdList.size() > 0">
            <if test="param.partitionIdList.size() > 1">
                and a.id in
                <foreach collection="param.partitionIdList" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
        </if>
        <if test="param.type != null and param.type != ''">
            and a.type = #{param.type}
        </if>
       </where>
        order by order_num, a.create_time
    </select>

    <select id="getDetail" resultType="org.thingsboard.server.dao.model.sql.smartPipe.dma.Partition">
        select a.*, ifnull(b.num, 0) as bigUserNum, ifnull(c.num, 0) as revenueUserNum
        from tb_pipe_partition a
            left join (select b.partition_id, count(*) as num from tb_pipe_partition_mount b where b.type = '3' group by b.partition_id) b on b.partition_id = a.id
            left join (select b.partition_id, count(*) as num from tb_pipe_partition_cust b group by b.partition_id) c on c.partition_id = a.id and b.partition_id = c.partition_id
        where a.id = #{id}
    </select>

    <select id="getAllId" resultType="java.lang.String">
        select id from tb_pipe_partition where tenant_id = #{tenantId} and type = '2' order by create_time desc
    </select>

    <select id="getAllByType" resultType="org.thingsboard.server.dao.model.sql.smartPipe.dma.Partition">
        select id, pid, name, status, type from tb_pipe_partition a
            <where>
                <if test="type != null and type != ''">
                    and a.type = #{type}
                </if>
                <if test="status != null and status != ''">
                    and a.status = #{status}
                </if>
                and tenant_id = #{tenantId}
            </where>
    </select>

    <select id="getAllIdNameByPidIn" resultType="org.thingsboard.server.dao.model.sql.smartPipe.dma.Partition">
        select id, name from tb_pipe_partition a where pid in
        <foreach collection="partitionIdList" open="(" separator="," close=")" item="item">
            #{item}
        </foreach>
        <if test="type != null and type != ''">
            and type = #{type}
        </if>
    </select>

    <select id="getUserNum" resultType="com.alibaba.fastjson.JSONObject">
        select b.partition_id as id, count(*) as "userNum" from tb_pipe_partition_cust b
        where b.partition_id in
            <foreach collection="partitionIdList" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        group by b.partition_id
    </select>

    <select id="getCopiedNum" resultType="com.alibaba.fastjson.JSONObject">
        select parti.id, count(distinct read_data.cust_code) as total from tb_pipe_partition parti
            left join tb_pipe_partition_cust cust on parti.id = cust.partition_id
            left join revenue.tb_meter_base_number read_data on cust.cust_code = read_data.cust_code
                and read_data.ym in
                <foreach collection="ymList" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            where partition_id in
            <foreach collection="partitionIdList" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>

            group by parti.id

    </select>

    <select id="getAllByCondition" resultType="org.thingsboard.server.dao.model.sql.smartPipe.dma.Partition">
        select a.* from tb_pipe_partition a
        <where>
            a.tenant_id = #{tenantId}
            <if test="name != null and name != ''">
                and a.name like '%'||#{name}||'%'
            </if>
            <if test="type != null and type != ''">
                and a.type = #{type}
            </if>
            <if test="status != null and status != ''">
                and a.status = #{status}
            </if>
        </where>
        order by order_num, a.create_time desc

    </select>
    <select id="getDMAOverview" resultType="org.thingsboard.server.dao.model.DTO.DMAOverviewDTO">
        select a.*, parent.name as parentName
        from tb_pipe_partition a
        left join tb_pipe_partition parent on a.pid = parent.id
        <where>
            a.tenant_id = #{tenantId}
            and a.type = '2'
            <if test="name != null and name != ''">
                and a.name like '%'||#{name}||'%'
            </if>
            <if test="status != null and status != ''">
                and a.status = #{status}
            </if>
        </where>
        order by order_num, a.create_time desc
    </select>

    <select id="getMountNum" resultType="com.alibaba.fastjson.JSONObject">
        select a.id, ifnull(c.num, 0) as "num"
        from tb_pipe_partition a
        left join (select b.partition_id, count(*) as num from tb_pipe_partition_mount b
            where 1=1
            <if test="mountType != null and mountType != ''">
                and b.type = #{mountType}
            </if>
            <if test="direction != null and direction != ''">
                <choose>
                    <when test="direction == 'in'.toString()">
                        and b.direction != '4'
                    </when>
                    <otherwise>
                        and b.direction != '3'
                    </otherwise>
                </choose>

            </if>
            group by b.partition_id)
            c on c.partition_id = a.id
        where a.id in
        <foreach collection="partitionIdList" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>
</mapper>