import{_ as u}from"./index-BlG8PIOK.js";import{d,g as a,n as l,p as n,q as _,F as m,aB as f,aJ as g,bh as t,C as v}from"./index-r0dFAfgr.js";const x={class:"card"},y={style:{"text-align":"left"}},h=d({__name:"scroll",props:{config:{}},setup(p){const e=p,r={step:.2,limitMoveNum:10};return(B,o)=>{const c=u;return a(),l("div",x,[o[0]||(o[0]=n("div",{class:"title"},[n("span",{style:{"text-align":"left"}},"监测点名称"),n("span",null,"浊度(NTU)"),n("span",null,"余氯(mg/L)"),n("span",null,"PH"),n("span",null,"读取时间")],-1)),_(c,{data:e.config,"class-option":r,class:"warp"},{default:m(()=>[n("div",null,[(a(!0),l(f,null,g(e.config,(s,i)=>(a(),l("li",{key:i,class:"title"},[n("span",y,t(s.name),1),n("span",null,t(s.turbidity),1),n("span",null,t(s.remainder),1),n("span",null,t(s.ph),1),n("span",null,t(s.time),1)]))),128))])]),_:1},8,["data"])])}}}),b=v(h,[["__scopeId","data-v-d21c4b09"]]);export{b as default};
