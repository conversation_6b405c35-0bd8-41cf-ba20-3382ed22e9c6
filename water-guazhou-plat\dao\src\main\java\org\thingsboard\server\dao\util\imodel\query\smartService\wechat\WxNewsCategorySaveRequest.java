package org.thingsboard.server.dao.util.imodel.query.smartService.wechat;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartService.wechat.WxNewsCategory;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;

@Getter
@Setter
public class WxNewsCategorySaveRequest extends SaveRequest<WxNewsCategory> {
    // 栏目名称
    private String name;

    // 编号
    private Integer serialNo;


    @Override
    protected WxNewsCategory build() {
        WxNewsCategory entity = new WxNewsCategory();
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    @Override
    protected WxNewsCategory update(String id) {
        WxNewsCategory entity = new WxNewsCategory();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(WxNewsCategory entity) {
        entity.setName(name);
        entity.setSerialNo(serialNo);
    }

}