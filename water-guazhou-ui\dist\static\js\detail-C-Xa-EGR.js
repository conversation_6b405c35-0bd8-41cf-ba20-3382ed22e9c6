import{d as S,c as g,r as d,a8 as b,x as n,S as _,b as L,a9 as V,o as A,g as W,n as B,q as u,i as m,F as P,aq as q,b6 as H,C as R}from"./index-r0dFAfgr.js";import{_ as j}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{M as z,N as $,O as U,b as G,D as J}from"./manage-BReaEVJk.js";import{m as K,l as Q,n as X}from"./device-DWHb0XjG.js";import{h as Z}from"./projectManagement-CDcrrCQ1.js";import{f as C}from"./DateFormatter-Bm9a68Ax.js";import{S as k}from"./data-Dv9-Tstw.js";import ee from"./detail-DEo1RlcF.js";import{I as te}from"./common-CvK_P_ao.js";import"./index-CCFuhOrs.js";/* empty css                             */import"./DPlayer-Be2AurQX.js";import"./DPlayer.min-_HMH7IVX.js";import"./xmwcqk-Cxfq91Sa.js";import"./CardTable-rdWOL4_6.js";import"./index-C9hz-UZb.js";import"./xmgc-Czrw1pVN.js";import"./cytbgs-WJxYGJyW.js";import"./gcwcqk-CV4EMT8B.js";import"./ssxmwcqk-BJgrXy2o.js";import"./gcsjjcxx-lLqauOhu.js";import"./sjbg-L9B2uWB9.js";import"./data-DDQ4eWNr.js";import"./gcysjcxx-BB9DfF9W.js";import"./qzjbxx-D98fv1p0.js";import"./htjbxx-CcjVPiVa.js";import"./htbg-CJ8T-1F4.js";import"./fygl-BCgGpKLc.js";import"./ssxq-C8LIbr3S.js";import"./ysqgcjcxx-5zZQS7XS.js";import"./ssgcjsjcxx-BD3tZw0Z.js";import"./ssgdjcxx-4P0LZdbp.js";import"./xmzysjcxx-DxVVq7LT.js";import"./xmzjsjcxx-C3UxQ9jk.js";import"./xmzgdjcxx-LKGnYC4Q.js";const ae={class:"team_table"},ie=S({__name:"detail",props:{config:{}},emits:["extendedReturn"],setup(I,{emit:E}){const v=g(),h=g(),y=g(),f=g(),M=I,x=E,w=d({loading:!1,indexVisible:!0,dataList:b(()=>M.config.items),columns:[{prop:"contractName",label:"所属合同"},{prop:"contractTypeName",label:"合同类别"},{prop:"beginTime",label:"工期开始时间",formatter:e=>C(e.beginTime,"YYYY-MM-DD HH:mm:ss")},{prop:"endTime",label:"工期结束时间",formatter:e=>C(e.endTime,"YYYY-MM-DD HH:mm:ss")},{prop:"creatorName",label:"创建人"},{prop:"createTime",label:"创建时间",formatter:e=>C(e.createTime,"YYYY-MM-DD HH:mm:ss")},{prop:"status",label:"工作状态",tag:!0,tagColor:e=>{var t;return((t=k.find(a=>a.value===e.status))==null?void 0:t.color)||""},formatter:e=>{var t;return(t=k.find(a=>a.value===e.status))==null?void 0:t.label}}],operationWidth:"320px",operations:[{text:"详情",perm:!0,click:e=>{var t;o.selected=e,(t=h.value)==null||t.openDrawer()}},{disabled:e=>e.status==="COMPLETED",text:"完成",perm:!0,click:e=>{z(e.id).then(t=>{t.data.code===200?n.success("已完成"):n.warning("完成失败"),x("extendedReturn",{})})}},{text:"查看设备",perm:!0,click:e=>{var t;p.defaultValue={code:e.code,contractCode:e.contractCode},o.getProjectDeviceValue(),(t=y.value)==null||t.openDialog()}},{disabled:e=>e.status==="COMPLETED",text:"编辑",perm:!0,click:e=>F(e)},{disabled:e=>e.status==="COMPLETED",text:"删除",perm:!0,click:e=>N(e)},{disabled:e=>e.status==="COMPLETED",text:"添加设备",perm:!0,click:e=>{var a;p.defaultValue={code:e.code,contractCode:e.contractCode};const t=r.group[0].fields.find(i=>i.type==="table");t.config.selectList=[],r.defaultValue={code:e.code},o.geDeviceListValue(),(a=f.value)==null||a.openDialog()}}],pagination:{hide:!0}}),c=d({title:"添加实施明细",appendToBody:!0,labelWidth:"130px",dialogWidth:"1000px",submitting:!1,submit:e=>{let t="新增";if(e.id&&(t="修改"),e.beginTime>e.endTime){n.warning("时间范围异常");return}c.submitting=!0,$(e).then(a=>{var i;c.submitting=!1,a.data.code===200?(n.success(t+"成功"),(i=v.value)==null||i.closeDialog()):n.warning(t+"失败"),x("extendedReturn",{})}).catch(a=>{c.submitting=!1,n.warning(a)})},defaultValue:{},group:[{fields:[{xs:12,type:"input",label:"实施编号",field:"code",rules:[{required:!0,message:"请输入实施编号"}],disabled:!0},{xs:12,type:"select",label:"所属合同编号",field:"contractCode",options:b(()=>o.contractList)},{xs:12,type:"date",label:"工期开始时间",field:"beginTime",rules:[{required:!0,message:"请输入工期开始时间"}],format:"x"},{xs:12,type:"date",label:"工期结束时间",field:"endTime",rules:[{required:!0,message:"请输入工期结束时间"}],format:"x"},{xs:12,type:"input",label:"工程负责人",field:"principal",rules:[{required:!0,message:"请输入工程负责人"}]},{xs:12,type:"input",label:"联系电话",field:"phone"},{xs:12,type:"input",label:"施工班组",field:"constructClass"},{type:"textarea",label:"详细说明",field:"remark"}]}]}),p=d({title:"查看设备",labelWidth:"130px",appendToBody:!0,dialogWidth:"1000px",defaultValue:{},group:[{fields:[{type:"table",field:"deviceTable",config:{indexVisible:!0,height:"350px",dataList:b(()=>o.deviceInformation),columns:[{label:"设备名称",prop:"deviceName"},{label:"设备编码",prop:"serialId"},{label:"设备型号",prop:"model"},{label:"所属大类",prop:"deviceTopTypeName"},{label:"所属分类",prop:"deviceType"},{label:"设备标识",prop:"mark"},{label:"计量单位",prop:"unit"},{label:"清单总量",prop:"amount"}],operations:[{perm:!0,text:"删除",icon:te.DELETE,click:e=>{_("确定删除该设备","删除提示").then(()=>{Z(e.id).then(()=>{n.success("删除成功"),o.getProjectDeviceValue()}).catch(t=>{n.warning(t)})})}}],pagination:{hide:!0}}}]}]}),r=d({title:"添加设备",labelWidth:"80px",appendToBody:!0,dialogWidth:"1000px",defaultValue:{},submitting:!1,submit:(e,t)=>{var a,i;if(t)o.geDeviceListValue(e);else{let s=!1;const D=r.group[0].fields.find(l=>l.type==="table");if(((a=D.config.selectList)==null?void 0:a.length)===0){n.warning("请勾选设备");return}const O=(i=D.config.selectList)==null?void 0:i.map(l=>((l.number===0||!l.number)&&(n.warning("数量最少为1台"),s=!0),l.number>l.rest&&(n.warning("申请数量超过剩余数量"),s=!0),{serialId:l.serialId,amount:l.number||0}));if(s)return;r.submitting=!0,K(e.code,O).then(l=>{var T;r.submitting=!1,l.data.code===200?(n.success("添加成功"),(T=f.value)==null||T.closeDialog()):n.warning("添加失败")}).catch(l=>{r.submitting=!1,n.warning(l)})}},group:[{fields:[{xs:6,type:"input",field:"serialId",label:"设备编码"},{xs:6,type:"input",field:"name",label:"设备名称"},{xs:6,type:"input",field:"model",label:"设备型号"},{xs:6,type:"btn-group",btns:[{text:"查询",perm:!0,click:()=>{var e;(e=f.value)==null||e.Submit(!0)}},{text:"重置",perm:!0,type:"default",click:()=>{var e;(e=f.value)==null||e.resetForm(),o.geDeviceListValue()}}]},{type:"table",config:{indexVisible:!0,height:"350px",dataList:b(()=>o.deviceInformation),handleSelectChange:e=>{const t=r.group[0].fields.find(a=>a.type==="table");t.config.selectList=e},selectList:[],columns:[{label:"设备名称",prop:"deviceName"},{label:"设备编码",prop:"serialId"},{label:"设备型号",prop:"model"},{label:"所属大类",prop:"deviceTopTypeName"},{label:"所属分类",prop:"deviceType"},{label:"设备标识",prop:"mark"},{label:"计量单位",prop:"unit"},{label:"清单总量",prop:"rest"},{label:"申请数量",prop:"number",minWidth:"120px",formItemConfig:{type:"number",field:"number",min:0}}],pagination:{page:1,limit:20,total:0,refreshData:({page:e,size:t})=>{const a=r.group[0].fields.find(i=>i.type==="table");a.config.pagination.page=e,a.config.pagination.limit=t,o.geDeviceListValue()}}}}]}]}),Y=d({title:"详情",group:[],width:"80%",modalClass:"lightColor",appendToBody:!0,cancel:!1}),F=e=>{var t;c.title="编辑实施明细",c.defaultValue={...e||{},time:[e.workTimeBegin,e.workTimeEnd]},o.getConstructionContract(e.contractCode),(t=v.value)==null||t.openDialog()},N=e=>{_("确定删除？","提示信息").then(()=>{U(e.id).then(t=>{t.data.code===200?L.success(t.data.message):L.error(t.data.message),x("extendedReturn",{})}).catch(t=>{n.warning(t)})}).catch(()=>{})},o=d({ConstructionContractType:[],deviceInformation:[],contractList:[],selected:{},getOptions:()=>{G({page:1,size:-1}).then(e=>{o.ConstructionContractType=V(e.data.data.data||[],"children")})},geDeviceListValue:e=>{var i;o.deviceInformation=[];const t=r.group[0].fields.find(s=>s.type==="table"),a={page:t.config.pagination.page||1,size:t.config.pagination.limit||20,...e};Q((i=p.defaultValue)==null?void 0:i.contractCode,a).then(s=>{o.deviceInformation=s.data.data.data||[],t.config.pagination.total=s.data.data.total||0})},getProjectDeviceValue:()=>{var a;o.deviceInformation=[];const e=p.group[0].fields.find(i=>i.type==="table"),t={page:e.config.pagination.page||1,size:e.config.pagination.limit||20};X((a=p.defaultValue)==null?void 0:a.code,t).then(i=>{o.deviceInformation=i.data.data.data||[],e.config.pagination.total=i.data.data.total||0})},getConstructionContract:e=>{J(e).then(t=>{o.contractList=V(t.data.data.data||[],"children",{label:"name",value:"code"})})}});return A(()=>{o.getOptions(),o.geDeviceListValue()}),(e,t)=>{const a=q,i=j,s=H;return W(),B("div",ae,[u(a,{config:m(w)},null,8,["config"]),u(i,{ref_key:"refForm",ref:v,class:"dialogForm",config:m(c)},null,8,["config"]),u(i,{ref_key:"refDeviceForm",ref:y,config:m(p)},null,8,["config"]),u(i,{ref_key:"refAddDeviceForm",ref:f,config:m(r)},null,8,["config"]),u(s,{ref_key:"refDetail",ref:h,config:m(Y)},{default:P(()=>[u(ee,{config:m(o).selected,show:10},null,8,["config"])]),_:1},8,["config"])])}}}),Be=R(ie,[["__scopeId","data-v-4fbc625b"]]);export{Be as default};
