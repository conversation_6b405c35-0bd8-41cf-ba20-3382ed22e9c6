import{_ as J}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{c as D,a8 as j,D as E,b as h,S as W,a1 as K,d as Y,r as S,a0 as H,a9 as Q,am as X,o as Z,g as F,n as w,p as M,bh as tt,h as P,F as O,G as et,an as U,q as V,i as T,J as at,aq as ot,_ as it,C as rt}from"./index-r0dFAfgr.js";import{_ as nt}from"./FormMap-BGaXSqQF.js";import{b as st,P as lt,D as pt}from"./zhandian-YaGuQZe6.js";import{g as $,A as C,i as dt}from"./data-CLo2TII-.js";import{a as ut}from"./index-BggOjNGp.js";import{s as mt}from"./sortBy-DDhdj0i5.js";import{S as ct}from"./index-D9ERhRP6.js";import"./ArcView-DpMnCY82.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./widget-BcWKanF2.js";import"./pe-B8dP0-Ut.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./TileLayer-B5vQ99gG.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./commonProperties-DqNQ4F00.js";import"./project-DUuzYgGl.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./IdentifyResult-4DxLVhTm.js";import"./ViewHelper-BGCZjxXH.js";import"./fieldconfig-Bk3o1wi7.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./LayerHelper-Cn-iiqxI.js";import"./pipe-nogVzCHG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./URLHelper-B9aplt5w.js";import"./ArcPipe.vue_vue_type_script_setup_true_lang-ClsVvV2P.js";import"./utils-D5nxoMq3.js";const ft=()=>{const y=gt(),k=D([]),d=[],x=i=>{var r;return{type:"select",options:((r=d.find(v=>v.deviceId===i))==null?void 0:r.attrList)??[],onChange:(v,n,b)=>{var t,a;const f=(t=b==null?void 0:b.options)==null?void 0:t.find(o=>o.value===v);n.name=f==null?void 0:f.label,n.attr=v,n.unit=(a=f==null?void 0:f.data)==null?void 0:a.unit}}},u=D({height:400,dataList:[],columns:[{minWidth:60,label:"序号",prop:"orderNum",align:"center",width:60},{minWidth:180,label:"设备",prop:"deviceId",formItemConfig:{type:"select",label:"",field:"",options:j(()=>k.value||[]),onChange:(i,e)=>{if(!i||!e.attrAliasFormItemConfig)return;const r=E(i);$(r).then(v=>{e.attrAliasFormItemConfig.options=v})}}},{minWidth:150,label:"变量名",prop:"attrAlias",formItemConfig:{type:"select",options:[]}},{minWidth:150,label:"字段名称",prop:"attr"},{minWidth:150,label:"别名",prop:"name",formItemConfig:{type:"input",label:"",field:""}},{minWidth:120,label:"最小值",prop:"min",formItemConfig:{type:"input-number",onChange:(i,e)=>{e.min===void 0&&(e.min=""),e.max===void 0&&(e.max=""),e.min&&e.max&&Number(e.min)>Number(e.max)&&h.warning("最小值不能大于最大值"),e.range=e.min+","+e.max}}},{minWidth:120,label:"最大值",prop:"max",formItemConfig:{type:"input-number",onChange:(i,e)=>{e.min===void 0&&(e.min=""),e.max===void 0&&(e.max=""),e.min&&e.max&&Number(e.min)>Number(e.max)&&h.warning("最大值不能小于最小值"),e.range=e.min+","+e.max}}},{minWidth:100,label:"单位",prop:"unit",formItemConfig:{type:"input"}}],operationWidth:220,rowKey:"id",operations:[{perm:!0,type:"primary",isTextBtn:!0,text:"上移",icon:"iconfont icon-shangyi",click:i=>c(i)},{perm:!0,type:"primary",isTextBtn:!0,text:"下移",icon:"iconfont icon-xiayi",click:i=>B(i)},{perm:!0,type:"danger",isTextBtn:!0,text:"删除",icon:"iconfont icon-shanchu",click:i=>s(i)}],pagination:{hide:!0}}),m=async i=>{const e=await ut(i);k.value=K(e.data||[],{label:"name",value:"id.id",id:"id.id",children:"children"})},c=i=>{const e=i.orderNum,r=u.value.dataList.find(v=>v.orderNum===e-1);r&&(i.orderNum--,r.orderNum++,N())},B=i=>{const e=i.orderNum,r=u.value.dataList.find(v=>v.orderNum===e+1);r&&(i.orderNum++,r.orderNum--,N())},s=i=>{i&&W("确定移除此行？","提示信息").then(()=>{const e=u.value.dataList.findIndex(r=>r.orderNum===i.orderNum);u.value.dataList.splice(e,1),N()}).catch(()=>{})},R=(i,e)=>{const r=x();u.value.dataList.push({stationId:e,orderNum:u.value.dataList.length+1,deviceId:"",attrAlias:"",attr:"",name:"",type:i,attrAliasFormItemConfig:r})},N=()=>{const i=mt(u.value.dataList,e=>e.orderNum);u.value.dataList=i.map((e,r)=>(e.orderNum=r+1,e))},L=async i=>{var e;u.value.loading=!0;try{const r=((e=y.group.value.find(n=>n.type===i))==null?void 0:e.attrList)||[],v=r.map(n=>n.deviceId);await A(v),u.value.dataList=(r==null?void 0:r.map(n=>{var b;if(n.range){const f=(b=n.range)==null?void 0:b.split(",");(f==null?void 0:f.length)===2&&(n.min=f[0],n.max=f[1])}return n.attrAlias=n.attr,n.attrAliasFormItemConfig=x(n.deviceId),n}))||[]}catch{}u.value.loading=!1},A=async i=>{const e=Array.from(new Set(i)).filter(n=>!!n&&d.findIndex(b=>b.deviceId===n)===-1),r=e.map(n=>$(E(n)));(await Promise.allSettled(r)).map((n,b)=>{n.status==="fulfilled"&&d.push({deviceId:e[b],attrList:n.value})})};return{TableConfig:u,refreshTableColumns:m,addAttrRow:R,refreshData:L,attrGroup:y,devices:k}},gt=()=>{const y=D([]);return{removeAttrGroup:x=>{const u=y.value.findIndex(m=>m.type===x);u>-1&&y.value.splice(u,1)},initAttrGroupData:async x=>{if(x===void 0)y.value=[];else{const u=await st({stationId:x||""});y.value=u.data||[]}return y.value.length===0&&y.value.push({type:C,attrList:[]}),y.value},group:y}},vt={class:"station-detail"},yt={class:"righ-header"},ht={class:"title"},bt={key:0,style:{width:"100%",height:"450px"}},It={key:1,style:{width:"100%",height:"400px"}},xt={key:0,style:{width:"100%",height:"450px"}},_t={key:1,style:{width:"100%",height:"400px"}},Ct=Y({__name:"StationDetail",props:{stationInfo:{},projectId:{}},emits:["deleted","submited"],setup(y,{emit:k}){const d=D(),x=D(),u=k,m=y,c=S({location:void 0,activeTab:"",videoList:[]}),B=D([{name:"",unit:"",type:"shuzhi"}]),s=ft(),R=()=>{var t;N.defaultValue={orderNumber:1},(t=x.value)==null||t.openDialog()},N=S({title:"视频绑定",dialogWidth:500,group:[{fields:[{type:"select-tree",options:H().projectList,label:"视频分组",field:"videoGrouping",checkStrictly:!0,rules:[{required:!0,message:"请选择视频分组"}],onChange(t){ct(t,{page:1,size:999}).then(o=>{c.videoList=Q(o.data||[])})}},{type:"select",label:"视频",field:"videoList",multiple:!0,options:j(()=>c.videoList),rules:[{required:!0,message:"请选择视频"}]}]}],labelPosition:"right",labelWidth:"100px",defaultValue:{orderNumber:1},submit:t=>{W("确定提交？","提示信息").then(()=>{var a;try{const o=t.videoList.map(l=>c.videoList.find(p=>p.id===l));L.dataList=[...L.dataList,...o],h.success("添加成功"),(a=x.value)==null||a.closeDialog()}catch(o){h.error("添加失败"),console.log(o)}})}}),L=S({titleRight:[{style:{marginLeft:"auto"},items:[{type:"btn-group",btns:[{perm:!0,type:"primary",text:"绑定视频",icon:"iconfont icon-jia",click:()=>R()}]}]}],height:"none",indexVisible:!0,columns:[{label:"名称",prop:"name",minWidth:"250px"},{label:"类型",prop:"videoType",formatter:t=>{}}],dataList:[],operationWidth:200,operations:[{perm:!0,text:"取消绑定",click:t=>{W("确定取消绑定？","提示信息").then(async()=>{try{L.dataList=L.dataList.filter(a=>a.id!==t.id)}catch(a){console.log(a),h.error("操作失败")}})}}],pagination:{hide:!0}}),A=S({labelPosition:"top",defaultValue:{},group:[{fieldset:{type:"underline",desc:"静态属性"},fields:[{xs:24,sm:12,md:8,lg:8,xl:8,type:"input",label:"名称：",field:"name",rules:[{required:!0,message:"请输入名称"}]},{xs:24,sm:12,md:8,lg:8,xl:8,type:"select",label:"类型：",field:"type",options:dt(),rules:[{required:!0,message:"请输入类型"}]},{xs:24,sm:12,md:8,lg:8,xl:8,type:"number",min:0,label:"排序",field:"orderNum"},{xs:24,sm:12,md:8,lg:8,xl:8,type:"select-tree",options:j(()=>H().projectList),label:"所属区域",field:"projectId"},{xs:24,sm:12,type:"input",label:"地址：",field:"address"},{type:"form-map",showInput:!0,field:"location"},{xs:24,type:"image",label:"图片：",field:"imgs"},{xs:24,sm:16,md:16,lg:16,xl:16,type:"textarea",maxRow:6,minRow:6,label:"备注：",field:"remark"}]},{fieldset:{desc:"动态属性"},fields:[{type:"tabs",label:"",field:"stationAttrInfo_type",tabs:j(()=>s.attrGroup.group.value.map(t=>({label:t.type,value:t.type}))??[]),closable:!0,handleTabRemove:async(t,a)=>{var o,l,p,I;t&&t!==C&&await W("确定删除？","提示信息"),s.attrGroup.removeAttrGroup(t),s.attrGroup.group.value.length||(s.attrGroup.group.value.push({type:C,attrList:[]}),a=C),(o=d.value)!=null&&o.dataForm&&(a?d.value.dataForm.stationAttrInfo_type=a:d.value.dataForm.stationAttrInfo_type=((p=(l=d.value)==null?void 0:l.dataForm)==null?void 0:p.stationAttrInfo_type)||C),s.TableConfig.value.dataList=((I=s.attrGroup.group.value.find(_=>{var G,g;return _.type===((g=(G=d.value)==null?void 0:G.dataForm)==null?void 0:g.stationAttrInfo_type)}))==null?void 0:I.attrList)||[]},handleTabClick(t){const a=t.paneName;f(a)},btns:[{type:"primary",size:"small",perm:!0,text:"添加分组",click:()=>{var t;return(t=n.value)==null?void 0:t.openDialog()}},{type:"primary",size:"small",perm:!0,text:"添加变量",click:()=>{var t,a,o,l,p,I;if(((a=(t=d.value)==null?void 0:t.dataForm)==null?void 0:a.stationAttrInfo_type)===C){h.warning("请先添加分组");return}s.addAttrRow((l=(o=d.value)==null?void 0:o.dataForm)==null?void 0:l.stationAttrInfo_type,(I=(p=d.value)==null?void 0:p.dataForm)==null?void 0:I.id)}}]},{type:"table",label:"",config:s.TableConfig.value},{type:"tabs",label:"",field:"extraInfo",tabs:[{value:"scada",label:"组态配置"},{value:"formVal",label:"填报配置"},{value:"视频",label:"视频"}]},{type:"textarea",label:"组态路径",field:"scadaUrl",handleHidden:(t,a,o)=>{o.hidden=t.extraInfo!=="scada"}},{xs:24,label:"",type:"table",field:"users",handleHidden:(t,a,o)=>{o.hidden=t.extraInfo!=="formVal"},config:{indexVisible:!0,height:"380px",titleRight:[{style:{marginLeft:"auto"},items:[{type:"btn-group",btns:[{perm:!0,type:"primary",text:"新增",icon:"iconfont icon-jia",click:()=>{B.value.push({name:"",unit:"",type:"shuzhi"})}}]}]}],dataList:j(()=>B.value),columns:[{label:"名称",prop:"name",formItemConfig:{type:"input"}},{label:"单位",prop:"unit",formItemConfig:{type:"input"}},{label:"类型",prop:"type",formItemConfig:{type:"select",options:[{label:"数值",value:"shuzhi"},{label:"开关",value:"kaiguan"}]}}],operationWidth:100,operations:[{text:"删除",isTextBtn:!0,perm:!0,type:"danger",iconifyIcon:"ep:delete"}],pagination:{hide:!0}}},{type:"table",handleHidden:(t,a,o)=>{o.hidden=t.extraInfo!=="视频"},config:L}],groupBtns:{styles:{paddingTop:"8px",textAlign:"right"},btns:[{perm:!0,text:"确定",loading:()=>!!A.submitting,click:()=>{var t;(t=d.value)==null||t.Submit()}}]}}],submit:async t=>{var a,o;A.submitting=!0;try{const l=s.attrGroup.group.value.find(_=>_.type===c.activeTab);l&&(l.attrList=s.TableConfig.value.dataList||[]);const p={orderNum:999,...t||{},id:(a=m.stationInfo)==null?void 0:a.value,projectId:t.projectId??m.projectId,location:(o=c.location)==null?void 0:o.join(","),stationAttrInfo:[...s.attrGroup.group.value],videoList:L.dataList.map(_=>_.id).join(","),additionalInfo:"",createTime:new Date,info:{}};(await lt(p)).data&&(h.success("提交成功"),u("submited"),r())}catch{h.error("保存失败")}A.submitting=!1}}),i=t=>{d.value&&(c.location=t)},e=(t,a)=>{a.address=t.data.result.formatted_address||""},r=async()=>{var a,o,l,p,I,_,G;if(!d.value)return;s.refreshTableColumns(m.projectId),await s.attrGroup.initAttrGroupData((a=m.stationInfo)==null?void 0:a.value);const t=(l=(o=s.attrGroup.group.value)==null?void 0:o[0])==null?void 0:l.type;A.defaultValue={orderNum:999,...((p=m.stationInfo)==null?void 0:p.data)||{},projectId:((_=(I=m.stationInfo)==null?void 0:I.data)==null?void 0:_.projectId)??m.projectId,stationAttrInfo_type:t||C,extraInfo:"scada"},(G=d.value)==null||G.resetForm(),f(t)},v=()=>{W("确定删除？","提示信息").then(async()=>{var t,a;try{const o=(t=m.stationInfo)==null?void 0:t.value;if(!o){h.warning("请选择一个站点");return}(a=(await pt([o])).data)!=null&&a.length?(h.success("删除成功"),u("deleted"),r()):h.error("删除失败")}catch{h.error("删除失败")}}).catch(()=>{})},n=D(),b=S({title:"请输入分组名称",dialogWidth:450,group:[{fields:[{field:"name",label:"",type:"input"}]}],labelPosition:"top",defaultValue:{stationAttrInfo_type:C},cancel:!0,submit:t=>{var l;const a=s.attrGroup.group;if(a.value.find(p=>p.type===t.name)){h.warning("请不要重复添加相同的分组");return}a.value=a.value.filter(p=>p.type!==C),a.value.push({type:t.name,attrList:[]}),d.value&&(d.value.dataForm.stationAttrInfo_type=t.name),f(t.name),(l=n.value)==null||l.closeDialog()}}),f=t=>{if(c.activeTab){const a=s.attrGroup.group.value.find(o=>o.type===c.activeTab);a&&(a.attrList=s.TableConfig.value.dataList||[])}c.activeTab=t,t===C&&s.attrGroup.group.value.length===0&&s.attrGroup.group.value.push({type:"请添加分组",attrList:[]}),s.refreshData(t)};return X(()=>m.stationInfo,()=>{var t,a,o,l;s.TableConfig.value.dataList=[],s.attrGroup.group.value=[],c.activeTab=C,c.location=((l=(o=(a=(t=m.stationInfo)==null?void 0:t.data)==null?void 0:a.location)==null?void 0:o.split(","))==null?void 0:l.map(p=>parseFloat(p)||p))||window.SITE_CONFIG.GIS_CONFIG.gisDefaultCenter||[116,29],r()}),Z(()=>{r()}),(t,a)=>{var G;const o=at,l=nt,p=ot,I=it,_=J;return F(),w("div",vt,[M("div",yt,[M("span",ht,tt(((G=m.stationInfo)==null?void 0:G.label)??"添加站点"),1),m.stationInfo?(F(),P(o,{key:0,type:"danger",size:"small",onClick:v},{default:O(()=>a[2]||(a[2]=[et(" 删除 ")])),_:1})):U("",!0)]),m.stationInfo?(F(),P(I,{key:0,ref_key:"refForm",ref:d,config:T(A)},{fieldSlot:O(({config:g,row:q})=>[g.field==="location"?(F(),w("div",bt,[V(l,{modelValue:T(c).location,"onUpdate:modelValue":a[0]||(a[0]=z=>T(c).location=z),row:q,"show-input":g.showInput,disabled:g.disabled,readonly:g.readonly,"handle-inverse-geocodeing":e,onChange:i},null,8,["modelValue","row","show-input","disabled","readonly"])])):g.type==="table"?(F(),w("div",It,[V(p,{ref:"refTable",config:g.config},null,8,["config"])])):U("",!0)]),_:1},8,["config"])):(F(),P(I,{key:1,ref_key:"refForm",ref:d,config:T(A)},{fieldSlot:O(({config:g,row:q})=>[g.field==="location"?(F(),w("div",xt,[V(l,{modelValue:T(c).location,"onUpdate:modelValue":a[1]||(a[1]=z=>T(c).location=z),row:q,"show-input":g.showInput,disabled:g.disabled,readonly:g.readonly,"handle-inverse-geocodeing":e,onChange:i},null,8,["modelValue","row","show-input","disabled","readonly"])])):g.type==="table"?(F(),w("div",_t,[V(p,{ref:"refTable",config:g.config},null,8,["config"])])):U("",!0)]),_:1},8,["config"])),V(_,{ref_key:"refDialogForm",ref:n,config:T(b)},null,8,["config"]),V(_,{ref_key:"refVideoDialog",ref:x,config:T(N)},null,8,["config"])])}}}),Ye=rt(Ct,[["__scopeId","data-v-8f34aa99"]]);export{Ye as default};
