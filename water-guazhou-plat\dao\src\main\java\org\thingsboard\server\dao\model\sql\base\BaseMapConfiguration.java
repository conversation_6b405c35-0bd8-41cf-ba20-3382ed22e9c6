package org.thingsboard.server.dao.model.sql.base;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;
import org.thingsboard.server.dao.util.imodel.response.tree.Identifiable;

/**
 * 平台管理-底图配置对象 base_map_configuration
 *
 * <AUTHOR>
 * @date 2025-05-22
 */
@ApiModel(value = "底图配置", description = "平台管理-底图配置实体类")
@Data
public class BaseMapConfiguration implements Identifiable {
    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键ID")
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 底图名称
     */
    @ApiModelProperty(value = "底图名称")
    private String name;

    /**
     * 底图类型
     */
    @ApiModelProperty(value = "底图类型")
    private String type;

    /**
     * 底图服务地址模板
     */
    @ApiModelProperty(value = "底图服务地址模板")
    private String url;

    /**
     * 底图状态（0-禁用，1-启用）
     */
    @ApiModelProperty(value = "底图状态")
    private String status;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
}
