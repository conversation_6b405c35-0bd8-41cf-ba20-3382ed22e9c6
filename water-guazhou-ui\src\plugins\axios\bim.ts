/* eslint-disable @typescript-eslint/explicit-module-boundary-types */

import axios from 'axios'

const config = {
  baseURL: window.SITE_CONFIG.bimUrl,
  timeout: 1000 * 180,
  method: 'post'
}

const _axios = axios.create(config)

_axios.interceptors.request.use(
  config => {
    // 在请求发送之前做某事
    // token(必带)
    config.headers['Token'] = `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`
    return config
  },
  error => {
    console.log(error) // for debug
    // Do something with request error
    return Promise.reject(error)
  }
)

// Add a response interceptor
_axios.interceptors.response.use(
  response => {
    // Do something with response data
    /**
     * 下面的注释为通过在response里，自定义code来标示请求状态
     * 当code返回如下情况则说明权限有问题，登出并返回到登录页
     * 如想通过xmlhttprequest来状态码标识 逻辑可写在下面error中
     * 以下代码均为样例，请结合自生需求加以修改，若不需要，则可删除
     * code为非200是抛错 可结合自己业务进行修改
     */
    const res = response.status
    if (res !== 200 && res !== 201 && res !== 204) {
      return Promise.reject(new Error(response.data?.message || '错误'))
    }
    return response
  },
  error => {
    const message = error.response?.data?.message || error.response?.message || error.message
    return Promise.reject(new Error(message))
  }
)

export const request = _axios
export default _axios
