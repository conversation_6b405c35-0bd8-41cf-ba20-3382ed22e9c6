import Expand from '@arcgis/core/widgets/Expand'
import LayerList from '@arcgis/core/widgets/LayerList'

export const useLayerList = () => {
  let layerList: __esri.LayerList | undefined
  let llExpand: __esri.Expand | undefined
  const init = (view: __esri.MapView | undefined, widgetPosition?: string) => {
    if (!view) return
    layerList = new LayerList({
      view,
      multipleSelectionEnabled: true,
      listItemCreatedFunction: async event => {
        const item = event.item
        if (item.layer.type !== 'group') {
          // don't show legend twice
          item.panel = {
            content: 'legend',
            open: false
          }
        }
      }
    })
    llExpand = new Expand({
      view,
      content: layerList,
      expandTooltip: '图层管理'
    })
    llExpand && view.ui?.add(llExpand, widgetPosition || 'top-right')
    return llExpand
  }
  const destroy = () => {
    layerList?.destroy()
    llExpand?.destroy()
  }
  onBeforeUnmount(() => {
    destroy()
  })
  return {
    init
  }
}

export default useLayerList
