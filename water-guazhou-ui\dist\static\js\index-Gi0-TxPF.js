import{_ as B}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as E}from"./CardTable-rdWOL4_6.js";import{_ as I}from"./CardSearch-CB_HNR-Q.js";import{_ as P}from"./btnpermissions.vue_vue_type_style_index_0_lang-hIVeQYq2.js";import A from"./rolemenu-H2d-SI7j.js";import{d as U,s as q,j as F}from"./index-CaaU9niG.js";import{I as h}from"./common-CvK_P_ao.js";import{d as L,c as u,M as T,r as f,b as t,S as V,o as z,u as N,g as b,n as O,q as d,h as $,an as j,C as W}from"./index-r0dFAfgr.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";const G={class:"wrapper"},H=L({__name:"index",setup(Q){const m=u(),{$btnPerms:r}=T(),v=u({filters:[{label:"搜索",field:"name",type:"input"},{type:"btn-group",btns:[{perm:!0,text:"查询",icon:h.QUERY,click:()=>i()},{perm:r("RoleManageAdd"),text:"添加角色",icon:h.ADD,click:()=>D()}]}]}),n=f({dataList:[],columns:[{prop:"name",label:"角色名称"}],operations:[{text:"菜单授权",icon:"iconfont icon-menu",perm:r("RoleManageMenuAuth"),click:e=>R(e)},{text:"按钮权限",icon:"iconfont icon-quanxian",perm:r("RoleManageButtonAuth"),click:e=>k(e)},{text:"编辑",perm:r("RoleManageEdit"),icon:"iconfont icon-bianji",click:e=>x(e)},{type:"danger",text:"删除",icon:"iconfont icon-shanchu",perm:r("RoleManageDelete"),click:e=>y(e)}],operationWidth:"400px",pagination:{refreshData:({page:e,size:o})=>{n.pagination.page=e,n.pagination.limit=o,i()}}}),l=f({visible:!1,roleId:"",close:()=>l.visible=!1}),p=f({roleId:"",curRole:{}}),_=u(),i=async()=>{var a;const e={size:n.pagination.limit||20,page:n.pagination.page||1,...((a=_.value)==null?void 0:a.queryParams)||{}},o=await U(e);console.log(o.data),n.dataList=o.data.data||[],n.pagination.total=o.data.total||0},c=f({dialogWidth:450,title:"添加角色",group:[{fields:[{type:"input",label:"角色名称",field:"name",rules:[{required:!0,message:"请输入角色名称"}]}]}],submit:async e=>{var o;try{await q(e),t.success("操作成功"),(o=m.value)==null||o.closeDialog(),i()}catch{t.error("操作失败")}}}),D=()=>{var e;if(s.value){t.error("无权限");return}c.defaultValue={},c.title="添加角色",(e=m.value)==null||e.openDialog()},R=e=>{if(s.value){t.error("无权限");return}l.roleId=e.id.id,l.visible=!0},g=u(),k=e=>{var o;p.curRole=e,p.roleId=e.id.id,(o=g.value)==null||o.openDialog()},C=()=>{var e;i(),(e=g.value)==null||e.closeDialog()},x=e=>{var o,a;if(s.value){t.error("无权限");return}c.defaultValue={...e||{},id:(o=e==null?void 0:e.id)==null?void 0:o.id},c.title="编辑角色",(a=m.value)==null||a.openDialog()},y=e=>{if(s.value){t.error("无权限");return}V("确定删除该角色, 是否继续?","删除提示").then(()=>{F(e.id.id).then(()=>{i(),t.success("删除成功")}).catch(o=>{t.warning(o)})})},s=u(!1);return z(()=>{s.value=N().roles[0]==="CUSTOMER_USER",i()}),(e,o)=>{const a=I,S=E,M=B;return b(),O("div",G,[d(a,{ref_key:"refSearch",ref:_,config:v.value},null,8,["config"]),d(S,{config:n,class:"card-table"},null,8,["config"]),l.visible?(b(),$(A,{key:0,"table-config":l},null,8,["table-config"])):j("",!0),d(P,{ref_key:"refBtnPermDialog",ref:g,"role-id":p.roleId,"cur-role":p.curRole,onHandleclose:C},null,8,["role-id","cur-role"]),d(M,{ref_key:"refDialogForm",ref:m,config:c},null,8,["config"])])}}}),ne=W(H,[["__scopeId","data-v-ca53c111"]]);export{ne as default};
