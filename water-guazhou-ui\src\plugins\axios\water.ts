import axios from 'axios'
import { useGisStore } from '@/store'

const _axios = axios.create({
  baseURL: '/water'
})

_axios.interceptors.request.use(config => {
  const GisToken = useGisStore().gToken
  config.headers && (config.headers['Authorization-Token'] = GisToken)
  return config
}, error => {
  return Promise.reject(error)
})

_axios.interceptors.response.use(response => {
  return response
}, error => {
  // let message = error.response?.data?.message || error.response?.message || error?.message
  // const status = error.response?.data?.status || error.response?.status || error?.status
  // switch (status) {
  //   case 404:
  //     message = '请求失败，资源未找到'
  //     break
  //   case 405:
  //     message = '当前请求方法不受支持'
  //     break
  //   default:
  //     break
  // }
  return Promise.reject(error)
})
export const requestWater = _axios
