package org.thingsboard.server.dao.model.sql.install;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 报装流程类型
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-10-14
 */
@TableName("tb_gan_install_c")
@Data
public class GanInstallC {
    @TableId
    private String id;

    private String pid;

    private Integer stepNo;

    private String file;

    private String remark;

    private String status;

    private Date createTime;

    private String creator;

}
