package org.thingsboard.server.dao.pumpHouse;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.smartProduction.pumpHouse.PumpHouseStorage;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.pumpHouse.PumpHouseStoragePageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.pumpHouse.PumpHouseStorageSaveRequest;

import java.util.List;

public interface PumpHouseStorageService {
    /**
     * 分页条件查询泵房台账
     *
     * @param request 分页查询条件
     * @return 分页查询结果
     */
    IPage<PumpHouseStorage> findAllConditional(PumpHouseStoragePageRequest request);

    /**
     * 保存泵房台账
     *
     * @param entity 实体信息
     * @return 保存好的实体
     */
    PumpHouseStorage save(PumpHouseStorageSaveRequest entity);

    /**
     * 增量修改
     *
     * @param entity 实体信息
     * @return 是否修改成功
     */
    boolean update(PumpHouseStorage entity);

    /**
     * 删除
     *
     * @param id 唯一标识
     * @return 是否删除成功
     */
    boolean delete(String id);

    /**
     * 批量保存
     *
     * @param entities 泵房台账信息列表
     * @return 保存好的数据
     */
    List<PumpHouseStorage> saveAll(List<PumpHouseStorageSaveRequest> entities);

}
