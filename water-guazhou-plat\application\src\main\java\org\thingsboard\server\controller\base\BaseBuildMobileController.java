package org.thingsboard.server.controller.base;

import java.util.List;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.thingsboard.server.dao.base.IBaseBuildMobileService;
import org.thingsboard.server.dao.model.sql.base.BaseBuildMobile;
import org.thingsboard.server.dao.util.imodel.query.base.BaseBuildMobilePageRequest;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;
import org.thingsboard.server.service.aspect.annotation.MonitorPerformance;

/**
 * 平台管理-Mobile搭建Controller
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@Api(tags = "平台管理-Mobile搭建")
@RestController
@RequestMapping("api/base/build/mobile")
public class BaseBuildMobileController extends BaseController {

    @Autowired
    private IBaseBuildMobileService baseBuildMobileService;

    /**
     * 查询平台管理-Mobile搭建列表
     */
    @MonitorPerformance(description = "平台管理-查询Mobile搭建列表")
    @ApiOperation(value = "查询Mobile搭建列表")
    @GetMapping("/list")
    public IstarResponse list(BaseBuildMobilePageRequest baseBuildMobile) {
        return IstarResponse.ok(baseBuildMobileService.selectBaseBuildMobileList(baseBuildMobile));
    }

    /**
     * 获取平台管理-Mobile搭建详细信息
     */
    @MonitorPerformance(description = "平台管理-查询Mobile搭建详情")
    @ApiOperation(value = "查询Mobile搭建详情")
    @GetMapping(value = "/getDetail")
    public IstarResponse getInfo(String id) {
        return IstarResponse.ok(baseBuildMobileService.selectBaseBuildMobileById(id));
    }

    /**
     * 新增平台管理-Mobile搭建
     */
    @MonitorPerformance(description = "平台管理-新增Mobile搭建")
    @ApiOperation(value = "新增Mobile搭建")
    @PostMapping("/add")
    public IstarResponse add(@RequestBody BaseBuildMobile baseBuildMobile) {
        return IstarResponse.ok(baseBuildMobileService.insertBaseBuildMobile(baseBuildMobile));
    }

    /**
     * 修改平台管理-Mobile搭建
     */
    @MonitorPerformance(description = "平台管理-修改Mobile搭建")
    @ApiOperation(value = "修改Mobile搭建")
    @PostMapping("/edit")
    public IstarResponse edit(@RequestBody BaseBuildMobile baseBuildMobile) {
        return IstarResponse.ok(baseBuildMobileService.updateBaseBuildMobile(baseBuildMobile));
    }

    /**
     * 删除平台管理-Mobile搭建
     */
    @MonitorPerformance(description = "平台管理-删除Mobile搭建")
    @ApiOperation(value = "删除Mobile搭建")
    @DeleteMapping("/deleteIds")
    public IstarResponse remove(@RequestBody List<String> ids) {
        return IstarResponse.ok(baseBuildMobileService.deleteBaseBuildMobileByIds(ids));
    }
}
