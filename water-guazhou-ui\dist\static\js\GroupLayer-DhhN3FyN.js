import{l as p,e as t,y as r,m as b,a as v}from"./Point-WxyopZva.js";import{c as u,t as c,Q as f,R as m,T as _,U as g,V as L,W as O,X as w,Y as M,Z as V,e as $}from"./MapView-DaoQedLH.js";import{R as C}from"./index-r0dFAfgr.js";import{l as d,U as h}from"./widget-BcWKanF2.js";import{a as x}from"./lazyLayerLoader-DbM9sT1W.js";import"./pe-B8dP0-Ut.js";let s=class extends u(c(f(m(_(g(L($))))))){constructor(i){super(i),this._visibilityHandles={},this.allLayers=new O({getCollections:()=>[this.layers],getChildrenFunction:e=>"layers"in e?e.layers:null}),this.allTables=w(this),this.fullExtent=void 0,this.operationalLayerType="GroupLayer",this.spatialReference=void 0,this.type="group"}initialize(){this._enforceVisibility(this.visibilityMode,this.visible),this.addHandles(d(()=>this.visible,this._onVisibilityChange.bind(this),h))}_writeLayers(i,e,a,l){const o=[];if(!i)return o;i.forEach(y=>{const n=M(y,l.webmap?l.webmap.getLayerJSONFromResourceInfo(y):null,l);C(n)&&n.layerType&&o.push(n)}),e.layers=o}set portalItem(i){this._set("portalItem",i)}set visibilityMode(i){const e=this._get("visibilityMode")!==i;this._set("visibilityMode",i),e&&this._enforceVisibility(i,this.visible)}load(i){return this.addResolvingPromise(this.loadFromPortal({supportedTypes:["Feature Service","Feature Collection","Scene Service"],layerModuleTypeMap:x},i)),Promise.resolve(this)}async loadAll(){return V(this,i=>{i(this.layers,this.tables)})}layerAdded(i){i.visible&&this.visibilityMode==="exclusive"?this._turnOffOtherLayers(i):this.visibilityMode==="inherited"&&(i.visible=this.visible),this._visibilityHandles[i.uid]=d(()=>i.visible,e=>this._onChildVisibilityChange(i,e),h)}layerRemoved(i){const e=this._visibilityHandles[i.uid];e&&(e.remove(),delete this._visibilityHandles[i.uid]),this._enforceVisibility(this.visibilityMode,this.visible)}_turnOffOtherLayers(i){this.layers.forEach(e=>{e!==i&&(e.visible=!1)})}_enforceVisibility(i,e){if(!p(this).initialized)return;const a=this.layers;let l=a.find(o=>o.visible);switch(i){case"exclusive":a.length&&!l&&(l=a.getItemAt(0),l.visible=!0),this._turnOffOtherLayers(l);break;case"inherited":a.forEach(o=>{o.visible=e})}}_onVisibilityChange(i){this.visibilityMode==="inherited"&&this.layers.forEach(e=>{e.visible=i})}_onChildVisibilityChange(i,e){switch(this.visibilityMode){case"exclusive":e?this._turnOffOtherLayers(i):this._isAnyLayerVisible()||(i.visible=!0);break;case"inherited":i.visible=this.visible}}_isAnyLayerVisible(){return this.layers.some(i=>i.visible)}};t([r({readOnly:!0,dependsOn:[]})],s.prototype,"allLayers",void 0),t([r({readOnly:!0})],s.prototype,"allTables",void 0),t([r()],s.prototype,"fullExtent",void 0),t([r({json:{read:!0,write:!0}})],s.prototype,"blendMode",void 0),t([r({json:{read:!1,write:{ignoreOrigin:!0}}})],s.prototype,"layers",void 0),t([b("layers")],s.prototype,"_writeLayers",null),t([r({type:["GroupLayer"]})],s.prototype,"operationalLayerType",void 0),t([r({json:{origins:{"web-document":{read:!1,write:!1}}}})],s.prototype,"portalItem",null),t([r()],s.prototype,"spatialReference",void 0),t([r({json:{read:!1},readOnly:!0,value:"group"})],s.prototype,"type",void 0),t([r({type:["independent","inherited","exclusive"],value:"independent",json:{write:!0,origins:{"web-map":{read:!1,write:!1}}}})],s.prototype,"visibilityMode",null),s=t([v("esri.layers.GroupLayer")],s);const H=s;export{H as default};
