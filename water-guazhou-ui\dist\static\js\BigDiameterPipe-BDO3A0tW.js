import{d as x,c as b,r as v,b as _,g as F,h as I,F as M,q as C,i as P,_ as B}from"./index-r0dFAfgr.js";import L from"./RightDrawerMap-D5PhmGFO.js";import{QueryByPolygon as R}from"./wfsUtils-DXofo3da.js";import"./ArcView-DpMnCY82.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./widget-BcWKanF2.js";import"./pe-B8dP0-Ut.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./TileLayer-B5vQ99gG.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./commonProperties-DqNQ4F00.js";import"./project-DUuzYgGl.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./IdentifyResult-4DxLVhTm.js";import"./ViewHelper-BGCZjxXH.js";import"./fieldconfig-Bk3o1wi7.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./LayerHelper-Cn-iiqxI.js";import"./pipe-nogVzCHG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./ArcStationWarning-BF9YrSzF.js";/* empty css                         */import"./useWaterPoint-Bv0z6ym6.js";import"./DateFormatter-Bm9a68Ax.js";import"./zhandian-YaGuQZe6.js";import"./useStation-DJgnSZIA.js";import"./DrawerBox-CLde5xC8.js";import"./SideDrawer-CBntChyn.js";import"./PipeDetail-CTBPYFJW.js";import"./Search-NSrhrIa_.js";import"./FormTableColumnFilter-BT7pLXIC.js";import"./QueryHelper-ILO3qZqg.js";import"./config-fy91bijz.js";import"./ArcBRTools-BO92yznB.js";import"./PipeLength.vue_vue_type_script_setup_true_lang-BZfUsvDf.js";import"./arcWidgetButton-0glIxrt7.js";import"./StatisticsHelper-D-s_6AyQ.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./usePipelineGroup-Ba1pmWz_.js";import"./GroupLayer-DhhN3FyN.js";import"./lazyLayerLoader-DbM9sT1W.js";import"./WFSLayer-1TtJp3nx.js";import"./clientSideDefaults-VQhQaYxh.js";import"./QueryEngineCapabilities-Dk3NJwmm.js";import"./wfsUtils-Du031XaC.js";import"./geojson-MBFu2-HZ.js";import"./xmlUtils-CtUoQO7q.js";import"./ListWindow-WS05QqV0.js";import"./PopLayout-BP55MvL7.js";import"./PoiSearchV2-D7yNeLuv.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./URLHelper-B9aplt5w.js";import"./useLayerList-DmEwJ-ws.js";import"./useScaleBar-Beed-z91.js";const Qr=x({__name:"BigDiameterPipe",setup(S){const c=b(),f=b(),u={},p=v({tabs:[],loading:!1,layerIds:[],layerInfos:[]}),d=v({group:[{fieldset:{desc:"管径范围"},fields:[{type:"range",rangeType:"select",field:"range",options:[{label:"50",value:50},{label:"100",value:100},{label:"300",value:300},{label:"500",value:500},{label:"800",value:800},{label:"1000",value:1e3}],startPlaceHolder:"0",endPlaceHolder:"1000+",startOptionDisabled:(r,o)=>o&&Number(o)<r.value,endOptionDisabled:(r,o)=>o&&r.value<Number(o)}]},{fieldset:{desc:"选择图层"},fields:[{type:"tree",options:[],checkStrictly:!0,showCheckbox:!0,field:"layerid",nodeKey:"value"},{type:"btn-group",btns:[{perm:!0,styles:{width:"100%"},text:"展示",loading:()=>p.loading,click:()=>w()}]}]}],labelPosition:"top",gutter:12,defaultValue:{}}),h=()=>{var i;const r=d.group[1].fields[0];let e=((i=u.view)==null?void 0:i.layerViews.items[0].layer.sublayers).items.filter(t=>t.type&&t.type.toLowerCase().includes("line")).map(t=>({label:t.name,value:t.name,layername:t.name,type:t.type,spatialReferences:t.spatialReferences}));r.options=e},w=async()=>{var r,o,e;p.loading=!0;try{const{range:i,layerid:t}=((r=c.value)==null?void 0:r.dataForm)||{};if(!(t!=null&&t.length))_.warning("请选择管线图层");else{let k="1=1";const[m,a]=i||[],y=[];for(const g of t){let s=((o=(await R(g,void 0,k)).data)==null?void 0:o.features)||[];(m||a)&&(s=s.filter(D=>{const l=D.properties.管径;if(!l||!l.startsWith("DN"))return!1;const n=parseInt(l.replace("DN",""),10);return isNaN(n)?!1:(!m||n>=m)&&(!a||n<=a)})),y.push({name:g,data:s})}p.tabs=y,(e=f.value)==null||e.refreshDetail(p.tabs)}}catch(i){_.error(i.message)}p.loading=!1},N=async r=>{u.view=r,setTimeout(()=>{h()},1e3)};return(r,o)=>{const e=B;return F(),I(L,{ref_key:"refMap",ref:f,title:"大口径管线展示",onMapLoaded:N},{default:M(()=>[C(e,{ref_key:"refForm",ref:c,config:P(d)},null,8,["config"])]),_:1},512)}}});export{Qr as default};
