import{d as c,r as a,o as p,g as l,n as m,q as _,i as u,_ as f,C as d}from"./index-r0dFAfgr.js";const g={class:"onemap-panel-wrapper"},h=c({__name:"videoSurveillance",props:{view:{},menu:{}},emits:["highlightMark","addMarks"],setup(o,{emit:n}){const t=n,r=o,s=a({group:[{fields:[{type:"input",field:"layer",append:"刷新"},{type:"tree",checkStrictly:!0,options:[],nodeClick:e=>{t("highlightMark",r.menu,e==null?void 0:e.value)}}]}],labelPosition:"top",gutter:12});return p(()=>{}),(e,v)=>{const i=f;return l(),m("div",g,[_(i,{ref:"refForm",config:u(s)},null,8,["config"])])}}}),y=d(h,[["__scopeId","data-v-593a31e2"]]);export{y as default};
