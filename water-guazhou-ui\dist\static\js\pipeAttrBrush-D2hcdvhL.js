import{s as _,g as R,d as M,c as Q}from"./FeatureHelper-Da16o0mu.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import{c as G,Q as z,X as U,b as P}from"./index-r0dFAfgr.js";import"./MapView-DaoQedLH.js";import"./commonProperties-DqNQ4F00.js";import{i as H,e as W}from"./IdentifyHelper-RJWmLn49.js";import{a as J,g as O}from"./LayerHelper-Cn-iiqxI.js";import{g as K}from"./QueryHelper-ILO3qZqg.js";import{s as A}from"./ToolHelper-BiiInOzB.js";import{GetFieldConfig as V,GetFieldUniqueValue as X}from"./fieldconfig-Bk3o1wi7.js";import"./Point-WxyopZva.js";import{u as j}from"./arcWidgetButton-0glIxrt7.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./geometryEngineBase-BhsKaODW.js";const Y=p=>{const x=p;let s,i,l,r;const f=H();let a,o,e;const I=v=>{v&&(e=v,o=O(e,{id:"pipe-brush-referdev",title:"参考设备"}),a=O(e,{id:"pipe-brush-text",title:"捕捉标注"}),A("crosshair"),i=e.on("pointer-move",u=>{s&&clearTimeout(s),a==null||a.removeAll(),r=void 0,s=setTimeout(()=>{const F=e==null?void 0:e.toMap(u);w(F,s)},200)}),l=e==null?void 0:e.on("click",()=>{if(!r){P.warning("没有相关设备");return}r.feature.symbol=_(r.feature.geometry.type,{color:[255,0,255,1],outlineColor:[255,0,0,1],outlineWidth:2}),o==null||o.removeAll(),a==null||a.removeAll(),A(""),o==null||o.add(r.feature),i==null||i.remove(),l==null||l.remove(),R(e,r.feature,{avoidHighlight:!0})}))},w=async(v,u)=>{var F,C,t,d;if(!(!e||!v))try{f.geometry=v,f.mapExtent=e.extent;const n=(C=(F=x.value)==null?void 0:F.dataForm.layerid)==null?void 0:C[0];if(n===void 0)return;f.layerIds=[n];const y=await W(window.SITE_CONFIG.GIS_CONFIG.gisService+window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService,f);if(u!==s||(a==null||a.removeAll(),r=(t=y.results)==null?void 0:t.filter(b=>b.layerId===n)[0],!r))return;const m=M((d=r.feature)==null?void 0:d.geometry,v);if(!m)return;const h=Q({geometry:m,symbol:_("text",{yOffset:15,text:r.layerName+":"+r.feature.attributes.新编号})});a==null||a.add(h)}catch{a==null||a.removeAll()}},g=()=>{o==null||o.removeAll(),a==null||a.removeAll(),i==null||i.remove(),l==null||l.remove(),s&&clearTimeout(s),A(""),r=void 0},k=()=>r,q=()=>{g(),a&&(e==null||e.map.remove(a)),o&&(e==null||e.map.remove(o))};return z(()=>{q()}),{start:I,clear:g,destroy:q,getIdentifyResult:k}},Z=()=>{let p,x,s;const{initSketch:i,destroySketch:l}=j();let r;const f=g=>{g&&(r=g,s=O(r,{id:"pipe-brush",title:"范围"}),p=i(r,s,{createCallBack:o,updateCallBack:o}))},a=g=>{s==null||s.removeAll(),x=void 0,p==null||p.create(g)},o=g=>{x=g.graphics[0]},e=()=>{l(),s&&(r==null||r.map.remove(s))},I=()=>{s==null||s.removeAll(),x=void 0,p==null||p.cancel()},w=()=>x;return z(()=>{e()}),{init:f,destroy:e,getGraphic:w,create:a,clear:I}},fe=(p,x,s)=>{const i=p,l={curLayerFields:[],layerInfos:[],layerIds:[],tabs:[]},r=G(!1),f=G(!1),a=G(""),o=Y(p),e=Z();let I;const w=t=>{t&&(I=t,e.init(I),g(I))},g=async t=>{var h,b,S;l.layerIds=J(t);const d=await U(l.layerIds);l.layerInfos=((b=(h=d.data)==null?void 0:h.result)==null?void 0:b.rows)||[];const n=(S=k.value.group.find(c=>c.id==="layer"))==null?void 0:S.fields[0],y=l.layerInfos.filter(c=>c.geometrytype==="esriGeometryPoint").map(c=>({label:c.layername,value:c.layerid,data:c})),m=l.layerInfos.filter(c=>c.geometrytype==="esriGeometryPolyline").map(c=>({label:c.layername,value:c.layerid,data:c}));n&&(n.options=[{label:"管点类",value:-1,disabled:!0,children:y},{label:"管线类",value:-2,disabled:!0,children:m}]),i.value&&(i.value.dataForm.layerid=[m[0].value])},k=G({group:[{id:"layer",fieldset:{desc:"选择刷新图层"},fields:[{type:"tree",options:[],checkStrictly:!0,showCheckbox:!0,field:"layerid",nodeKey:"value",handleCheckChange:(t,d)=>{d&&(i.value&&(i.value.dataForm.layerid=[t.value]),v(),e.clear(),o.clear(),l.tabs=[],s())}}]},{fieldset:{desc:"选择参考设备"},fields:[{type:"btn-group",btns:[{perm:!0,text:"点击地图选择",iconifyIcon:"gis:arrow-o",click:()=>{o.start(I),l.tabs=[],s()}}]}]},{fieldset:{desc:"绘制工具"},fields:[{type:"btn-group",btns:[{perm:!0,text:"",type:"default",size:"large",title:"绘制多边形",disabled:()=>r.value,iconifyIcon:"mdi:shape-polygon-plus",click:()=>e.create("polygon")},{perm:!0,text:"",type:"default",size:"large",title:"绘制矩形",disabled:()=>r.value,iconifyIcon:"ep:crop",click:()=>e.create("rectangle")},{perm:!0,text:"",type:"default",size:"large",title:"绘制椭圆",disabled:()=>r.value,iconifyIcon:"mdi:ellipse-outline",click:()=>e.create("circle")},{perm:!0,text:"",type:"default",size:"large",title:"清除图形",disabled:()=>r.value,iconifyIcon:"ep:delete",click:()=>e.clear()}]}]},{id:"layer",fieldset:{desc:"图层字段"},fields:[{type:"list",data:[],className:"sql-list-wrapper",setData:async(t,d)=>{var h,b,S,c,B,D,T;if(!((h=d.layerid)!=null&&h.length)){l.curLayerFields.length=0;return}const n=d.layerid[0],y=(b=l.layerInfos.find(N=>N.layerid===n))==null?void 0:b.layername;if(!y)return;const m=await V(y);t.data=(c=(S=m.data)==null?void 0:S.result)==null?void 0:c.rows,l.curLayerFields=((D=(B=m.data)==null?void 0:B.result)==null?void 0:D.rows)||[],a.value=(T=l.layerInfos.find(N=>N.layerid===d.layerid[0]))==null?void 0:T.layername},setDataBy:"layerid",displayField:"alias",valueField:"name",highlightCurrentRow:!0,nodeClick:t=>{l.curFieldNode=t,u(t.name)}}]},{id:"field-construct",fieldset:{desc:"构建查询语句"},fields:[{type:"btn-group",size:"small",style:{width:"40%",display:"flex",flexWrap:"wrap"},className:"sql-btns-wrapper",btns:[{perm:!0,text:"=",styles:{margin:"6px",width:"50px"},click:()=>{u("=")}},{perm:!0,text:"模糊",styles:{margin:"6px",width:"50px"},click:()=>{u("like '%替换此处%'")}},{perm:!0,text:">",styles:{margin:"6px",width:"50px"},click:()=>{u(">")}},{perm:!0,text:"<",styles:{margin:"6px",width:"50px"},click:()=>{u("<")}},{perm:!0,text:"非",styles:{margin:"6px",width:"50px"},click:()=>{u("<>")}},{perm:!0,text:"并且",styles:{margin:"6px",width:"50px"},click:()=>{u("and")}},{perm:!0,text:"或者",styles:{margin:"6px",width:"50px"},click:()=>{u("or")}},{perm:!0,text:"%",styles:{margin:"6px",width:"50px"},click:()=>{u("%")}}],extraFormItem:[{type:"list",wrapperStyle:{width:"60%",height:"144px"},className:"sql-list-wrapper",field:"uniqueValue",data:[],nodeClick:t=>{u("'"+t+"'")},filters:[{type:"btn-group",btns:[{perm:!0,text:()=>f.value?"正在获取唯一值":"获取唯一值",loading:()=>f.value,disabled:()=>r.value,styles:{width:"100%",borderRadius:"0"},click:()=>F()}]}]}]}]},{fieldset:{desc:"组合查询条件"},fields:[{type:"textarea",field:"sql",placeholder:"OBJECTID > 0"},{type:"btn-group",itemContainerStyle:{marginBottom:"8px"},btns:[{perm:!0,text:"清除组合条件",type:"danger",disabled:()=>r.value,click:()=>v(),styles:{width:"100%"}}]}]},{fields:[{type:"btn-group",btns:[{perm:!0,text:"查询",styles:{width:"100%"},click:()=>C()},{perm:!0,text:"重置",type:"default",styles:{width:"100%"},click:()=>q()}]}]}],labelPosition:"top",gutter:12,defaultValue:{length:1}}),q=()=>{var t;(t=i.value)==null||t.resetForm()},v=()=>{var t;(t=i.value)!=null&&t.dataForm&&(i.value.dataForm.sql="")},u=t=>{var n;if(!i.value)return;(n=i.value)!=null&&n.dataForm||(i.value.dataForm={});const d=i.value.dataForm.sql||" ";i.value.dataForm.sql=d+t+" "},F=async()=>{var d,n;if(!l.curFieldNode)return;const t=(d=i.value)==null?void 0:d.dataForm.layerid;if(!(t!=null&&t.length)){P.warning("请先选择一个图层");return}f.value=!0;try{const y=await X({layerid:t[0],field_name:l.curFieldNode.name}),m=(n=k.value.group.find(b=>b.id==="field-construct"))==null?void 0:n.fields[0].extraFormItem,h=m&&m[0];h&&(h.data=y.data.result.rows)}catch{P.error("获取唯一值失败")}f.value=!1},C=async()=>{var n,y,m;const t=((n=i.value)==null?void 0:n.dataForm.layerid)||[],d={where:((y=i.value)==null?void 0:y.dataForm.sql)||"1=1",geometry:(m=e.getGraphic())==null?void 0:m.geometry};l.tabs=await K(t,l.layerInfos,d),x()};return{init:w,loading:r,curLayerName:a,state:l,FormConfig:k,startQuery:C,draw:e,pick:o}};export{fe as u};
