import TileLayer from '@arcgis/core/layers/TileLayer.js';
import MapImageLayer from '@arcgis/core/layers/MapImageLayer.js';
import FeatureLayer from '@arcgis/core/layers/FeatureLayer.js';
import WMSLayer from '@arcgis/core/layers/WMSLayer.js';
import TileInfo from '@arcgis/core/layers/support/TileInfo.js';
import TintLayer from './Layers/TintLayer.js';

// 定义瓦片结构
const tileInfo = new TileInfo({
  // "dpi": 90.71428571428571,
  dpi: 96,
  // rows: 256,
  // cols: 256,
  size: [256, 256],
  // compressionQuality: 0,
  origin: {
    x: -180,
    y: 90
  },
  spatialReference: {
    wkid: 4490
  },
  lods: [
    { level: 0, resolution: 0.703125, scale: 295829355.454566 },
    { level: 1, resolution: 0.3515625, scale: 147914677.727283 },
    { level: 2, resolution: 0.17578125, scale: 73957338.863641 },
    { level: 3, resolution: 0.087890625, scale: 36978669.431821 },
    { level: 4, resolution: 0.0439453125, scale: 18489334.71591 },
    { level: 5, resolution: 0.02197265625, scale: 9244667.357955 },
    { level: 6, resolution: 0.010986328125, scale: 4622333.678978 },
    { level: 7, resolution: 0.0054931640625, scale: 2311166.839489 },
    { level: 8, resolution: 0.00274658203125, scale: 1155583.419744 },
    { level: 9, resolution: 0.001373291015625, scale: 577791.709872 },
    { level: 10, resolution: 0.0006866455078125, scale: 288895.854936 },
    { level: 11, resolution: 0.00034332275390625, scale: 144447.927468 },
    { level: 12, resolution: 0.000171661376953125, scale: 72223.963734 },
    { level: 13, resolution: 8.58306884765625e-5, scale: 36111.981867 },
    { level: 14, resolution: 4.291534423828125e-5, scale: 18055.990934 },
    { level: 15, resolution: 2.1457672119140625e-5, scale: 9027.995467 },
    { level: 16, resolution: 1.0728836059570313e-5, scale: 4513.997733 },
    { level: 17, resolution: 5.3644180297851563e-6, scale: 2256.998867 },
    { level: 18, resolution: 0.000002682209014892578, scale: 1128.499433 }
  ]
});
export const useLayer = () => {
  const tdtKey = window.SITE_CONFIG.GIS_CONFIG.gisTdtToken;
  const createServiceLayer = (
    view: __esri.MapView,
    type: 'tiled' | 'MapImage' | 'Feature' | 'WMSLayer',
    options
  ) => {
    switch (type) {
      case 'MapImage':
        return new MapImageLayer({
          ...options
          // legendEnabled: false
          // listMode: 'hide-children'
        });
      case 'tiled':
        return new TileLayer(options);
      case 'Feature':
        return new FeatureLayer(options);
      case 'WMSLayer':
        return new WMSLayer(options);
      default:
        break;
    }
  };
  const createTdtLayer = (params: {
    type:
      | 'vec_c'
      | 'vec_w'
      | 'cva_c'
      | 'cva_w'
      | 'img_c'
      | 'img_w'
      | 'cia_c'
      | 'cia_w'
      | 'ter_c'
      | 'ter_w'
      | 'cta_c'
      | 'cta_w'
      | 'ibo_c'
      | 'ibo_w'
      | 'eva_c'
      | 'eva_w'
      | 'eia_c'
      | 'eia_w';
    color?: any;
    filter?: string;
    urlTemplate?: string;
  }) => {
    const pTypes = params.type || 'vec_w';
    const types = pTypes.split('_');
    const tiledLayer = new TintLayer({
      urlTemplate:
        params.urlTemplate ||
        `http://t0.tianditu.gov.cn/${pTypes}/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=${types[0]}&STYLE=default&TILEMATRIXSET=${types[1]}&FORMAT=tiles&TILEMATRIX={level}&TILEROW={row}&TILECOL={col}&tk=${tdtKey}`,
        // `http://t0.tianditu.gov.cn/${'img_f'}/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=${types[0]}&STYLE=default&TILEMATRIXSET=${types[1]}&FORMAT=tiles&TILEMATRIX={level}&TILEROW={row}&TILECOL={col}&tk=${tdtKey}`,
      //`http://t0.tianditu.gov.cn/vec_f/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=vec&STYLE=default&TILEMATRIXSET=f&FORMAT=tiles&TILEMATRIX={level}&TILEROW={row}&TILECOL={col}&tk=你的key`
      // urlTemplate: `http://{subDomain}.tianditu.gov.cn/DataServer?T=${type}&x={col}&y={row}&l={level}&tk=${tdtKey}`,
      subDomains: ['t0', 't1', 't2', 't3', 't4', 't5', 't6', 't7'],
      color: params.color,
      filter: params.filter
    });
    // tiledLayer.tileInfo = tileInfo
    return tiledLayer;
  };

  const createGDLayer = (
    type?: '6' | '8',
    color?: any,
    urlTemplate?: string
  ) => {
    const tiledLayer = new TintLayer({
      urlTemplate:
        urlTemplate ||
        `http://webst01.is.autonavi.com/appmaptile?style=${type}&x={col}&y={row}&z={level}`,

      // urlTemplate: `http://{subDomain}.tianditu.gov.cn/DataServer?T=${type}&x={col}&y={row}&l={level}&tk=${tdtKey}`,
      subDomains: [],
      color
    });
    return tiledLayer;
  };

  const createArcGISImageryLayer = (params?: {
    color?: any;
    filter?: string;
  }) => {
    console.log('=== 创建ArcGIS World Imagery图层 ===');
    console.log('参数:', params);
    
    const tiledLayer = new TintLayer({
      urlTemplate: 'https://services.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{level}/{row}/{col}',
      subDomains: [],
      color: params?.color,
      filter: params?.filter
    });
    
    console.log('ArcGIS图层创建成功:', tiledLayer);
    console.log('=== 创建ArcGIS World Imagery图层结束 ===');
    
    return tiledLayer;
  };

  return {
    createServiceLayer,
    createTdtLayer,
    createGDLayer,
    createArcGISImageryLayer
  };
};

export default useLayer;
