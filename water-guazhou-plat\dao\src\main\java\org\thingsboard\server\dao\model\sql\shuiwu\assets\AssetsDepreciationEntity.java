package org.thingsboard.server.dao.model.sql.shuiwu.assets;

import lombok.Data;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;
import java.math.BigDecimal;

/**
 * 设备台账
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2021-10-19
 */
@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.SHUIWU_ASSETS_DEPRECIATION_TABLE)
public class AssetsDepreciationEntity {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.SHUIWU_ASSETS_DEPRECIATION_PID)
    private String pid;

    @Column(name = ModelConstants.SHUIWU_ASSETS_DEPRECIATION_DEPRECIATION_MONTH)
    private String depreciationMonth;

    @Column(name = ModelConstants.SHUIWU_ASSETS_DEPRECIATION_COUNT_MONTH)
    private Integer countMonth;

    @Column(name = ModelConstants.SHUIWU_ASSETS_DEPRECIATION_DEPRECIATION_CURRENT)
    private BigDecimal depreciationCurrent;

    @Column(name = ModelConstants.SHUIWU_ASSETS_DEPRECIATION_DEPRECIATION_TOTAL)
    private BigDecimal depreciationTotal;

    @Column(name = ModelConstants.SHUIWU_ASSETS_DEPRECIATION_CREATE_TIME)
    private Long createTime;

    @Column(name = ModelConstants.SHUIWU_ASSETS_DEPRECIATION_PROJECT_ID)
    private String projectId;

    @Column(name = ModelConstants.SHUIWU_ASSETS_DEPRECIATION_TENANT_ID)
    private String tenantId;

    private transient String deviceNo; // 设备编号

    private transient String deviceName; // 设备名

    private transient String depreciationMethod; //折旧方法

    private transient BigDecimal purchaseAmount; // 采购金额

    private transient Integer useLife; // 使用寿命（月）

    private transient Double netResidualRate; //净残率

    private transient BigDecimal originValue; // 原值

    private transient BigDecimal netValue; //净值

    public AssetsDepreciationEntity(AssetsDepreciationEntity assetsDepreciationEntity, String deviceNo, String deviceName, String depreciationMethod, BigDecimal purchaseAmount, Integer useLife, Double netResidualRate, BigDecimal originValue, BigDecimal netValue) {
        this.id = assetsDepreciationEntity.id;
        this.pid = assetsDepreciationEntity.pid;
        this.depreciationCurrent = assetsDepreciationEntity.getDepreciationCurrent();
        this.depreciationMonth = assetsDepreciationEntity.depreciationMonth;
        this.countMonth = assetsDepreciationEntity.countMonth;
        this.depreciationTotal = assetsDepreciationEntity.depreciationTotal;
        this.createTime = assetsDepreciationEntity.createTime;
        this.projectId = assetsDepreciationEntity.projectId;
        this.tenantId = assetsDepreciationEntity.tenantId;
        this.deviceNo = deviceNo;
        this.deviceName = deviceName;
        this.depreciationMethod = depreciationMethod;
        this.purchaseAmount = purchaseAmount;
        this.useLife = useLife;
        this.netResidualRate = netResidualRate;
        this.originValue = originValue;
        this.netValue = netValue;
    }

    public AssetsDepreciationEntity() {

    }
}
