package org.thingsboard.server.dao.construction;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionApply;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionApplyContainer;
import org.thingsboard.server.dao.model.sql.smartOperation.device.SoDeviceItem;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemJournal;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemScope;
import org.thingsboard.server.dao.sql.smartOperation.construction.SoConstructionApplyMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionApplyPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionApplySaveRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoDeviceItemPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoDeviceItemSaveRequest;
import org.thingsboard.server.dao.construction.project.SoDeviceItemService;

import java.util.List;

import static org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemJournal.SO_CONSTRUCTION_APPLY_JOURNAL;
import static org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemScope.SO_CONSTRUCTION_APPLY;

@Service
public class SoConstructionApplyServiceImpl extends BasicSoConstructionTaskDriveService<SoConstructionApply> implements SoConstructionApplyService {
    @Autowired
    private SoConstructionApplyMapper mapper;

    @Autowired
    private SoDeviceItemService deviceItemService;

    @Override
    public IPage<SoConstructionApplyContainer> findAllConditional(SoConstructionApplyPageRequest request) {
        IPage<SoConstructionApplyContainer> pagedData = QueryUtil.pagify(request, (i, j) -> mapper.findByPage(request), () -> mapper.countByPage(request));
        for (SoConstructionApplyContainer record : pagedData.getRecords()) {
            List<SoConstructionApply> items = record.getItems();
            if (items.size() == 1 && items.get(0).getId() == null) {
                items.clear();
            }
        }
        return pagedData;
    }

    @Override
    @Transactional
    public SoConstructionApply save(SoConstructionApplySaveRequest entity) {
        return QueryUtil.saveOrUpdateOneByRequest(entity, e -> commonSave(entity, e), mapper::updateFully);
    }

    @Override
    public boolean isCodeExists(String code, String tenantId, String id) {
        return mapper.isCodeExists(code, tenantId, id);
    }

    @Override
    @Transactional
    public boolean complete(String id, String userId, String tenantId) {
        boolean success = mapper.markAsComplete(id);
        if (success && mapper.canMarkGlobalAsComplete(id)) {
            if (taskInfoService.markAsComplete(id, getCurrentScope())) {
                recordService.recordCreate(tenantId, userId, mapper.getConstructionCodeById(id), getCurrentJournalType());
            }
        }
        return success;
    }

    @Override
    public boolean update(SoConstructionApply entity) {
        return mapper.update(entity);
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }


    // region 设备项管理
    @Override
    public IPage<SoDeviceItem> getDevices(SoDeviceItemPageRequest request) {
        return deviceItemService.findAllConditional(request);
    }

    @Override
    public List<SoDeviceItem> saveDevice(List<SoDeviceItemSaveRequest> request) {
        for (SoDeviceItemSaveRequest req : request) {
            req.setScope(getCurrentScope());
        }
        return deviceItemService.saveAll(request, getCurrentScope());
    }


    // endregion

    @Override
    public boolean isComplete(String id) {
        return taskInfoService.isComplete(id, getCurrentScope());
    }

    @Override
    public boolean isComplete(String constructionCode, String tenantId) {
        return taskInfoService.isComplete(constructionCode, tenantId, getCurrentScope());
    }

    @Override
    public SoGeneralSystemScope getCurrentScope() {
        return SO_CONSTRUCTION_APPLY;
    }

    @Override
    public SoGeneralSystemJournal getCurrentJournalType() {
        return SO_CONSTRUCTION_APPLY_JOURNAL;
    }

    @Override
    public BaseMapper<SoConstructionApply> getDirectMapper() {
        return mapper;
    }

}
