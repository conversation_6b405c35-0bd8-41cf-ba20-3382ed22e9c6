<template>
  <div class="location-search-box">
    <div class="location-input-box">
      <span class="location-label">经度：</span>
      <el-input-number
        v-model="state.lon"
        :disabled="
          typeof disabled === 'function'
            ? disabled(state.lat, row, { type: 'form-map' })
            : disabled
        "
        :readonly="
          typeof readonly === 'function'
            ? readonly(state.lat, row, { type: 'form-map' })
            : readonly
        "
        :precision="4"
        size="small"
        class="location-input"
        @change="handleInputChange"
      ></el-input-number>
      <span class="location-label margin-l-10">纬度：</span>
      <el-input-number
        v-model="state.lat"
        :disabled="
          typeof disabled === 'function'
            ? disabled(state.lat, row, { type: 'form-map' })
            : disabled
        "
        :readonly="
          typeof readonly === 'function'
            ? readonly(state.lat, row, { type: 'form-map' })
            : readonly
        "
        :precision="4"
        size="small"
        class="location-input"
        @change="handleInputChange"
      ></el-input-number>
    </div>
    <p v-if="!disabled" class="message-text">提示：请点击地图 设置位置信息</p>
  </div>
</template>
<script lang="ts" setup>
import Graphic from '@arcgis/core/Graphic';
import PictureMarkerSymbol from '@arcgis/core/symbols/PictureMarkerSymbol';
import Point from '@arcgis/core/geometry/Point';
import { getStationImageUrl } from '@/utils/URLHelper';
import { bindViewClick } from '@/utils/MapHelper';

const emit = defineEmits(['change', 'update:modelValue', 'loaded']);
const props = defineProps<{
  /**
   * 坐标： 绑定值格式： [lon,lat]
   */
  modelValue?: number[];
  showInput?: boolean;
  row?: any;
  disabled?: boolean | ((value: any, row: any, formItem: IFormItem) => boolean);
  readonly?: boolean | ((value: any, row: any, formItem: IFormItem) => boolean);
}>();
const state = reactive<{
  lon: number | undefined;
  lat: number | undefined;
}>({
  lon: props.modelValue?.[0],
  lat: props.modelValue?.[1]
});
const view = inject('view') as __esri.MapView;

const staticState: {
  centerMark?: __esri.Graphic;
  stationaryWatcher?: __esri.WatchHandle;
  extentWatcher?: __esri.WatchHandle;
} = {};
const resetMarker = (point: __esri.Point) => {
  if (!view) return;
  // const point = view.center;
  staticState.centerMark = new Graphic({
    geometry: point,
    symbol: new PictureMarkerSymbol({
      url: getStationImageUrl('水厂.png'),
      width: 25,
      height: 30,
      yoffset: 15
    })
  }) as __esri.Graphic;
  view.graphics.removeAll();
  view.graphics.add(staticState.centerMark);
};
const handleInputChange = () => {
  gotoMarker(true);
  emit('update:modelValue', [state.lon, state.lat]);
  emit('change', [state.lon, state.lat]);
};
watch(
  () => props.modelValue,
  () => {
    if (props.modelValue?.length === 2) {
      if (
        props.modelValue?.[0] === state.lon &&
        props.modelValue?.[1] === state.lat
      ) {
        return;
      }
      state.lon = props.modelValue?.[0];
      state.lat = props.modelValue?.[1];

      gotoMarker(true);
    }
  }
);
const gotoMarker = (goto?: boolean) => {
  const point = new Point({
    longitude: state.lon,
    latitude: state.lat,
    spatialReference: view?.spatialReference
  });
  if (goto) {
    view
      ?.goTo(
        {
          zoom: 16,
          target: point
        },
        { duration: 500 }
      )
      .then(() => {
        resetMarker(point);
      });
  } else {
    resetMarker(point);
  }
};
onMounted(() => {
  gotoMarker(true);

  bindViewClick(view, (res) => {
    console.log(res);
    if (!res.screenPoint || !view) return;
    const point = view?.toMap(res.screenPoint);
    state.lon = point?.longitude;
    state.lat = point?.latitude;
    emit('update:modelValue', [state.lon, state.lat]);
    emit('change', [state.lon, state.lat]);
    gotoMarker();
  });
});
onBeforeUnmount(() => {
  staticState.extentWatcher?.remove();
  staticState.stationaryWatcher?.remove();
});
const getView = () => {
  return view;
};
defineExpose({
  getView
});
</script>
<style lang="scss" scoped>
.message-text {
  margin: 10px 0 12px 20px;
  color: #39b01c;
  line-height: initial;
}
</style>
