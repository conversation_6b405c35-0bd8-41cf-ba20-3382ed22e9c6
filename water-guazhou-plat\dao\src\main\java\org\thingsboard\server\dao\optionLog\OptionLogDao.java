/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.optionLog;

import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.id.UserId;
import org.thingsboard.server.common.data.optionLog.OptionLog;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.Dao;

import java.util.List;
import java.util.Map;
import java.util.UUID;

public interface OptionLogDao extends Dao<OptionLog> {
    @Override
    List<OptionLog> find();

    @Override
    OptionLog findById(UUID id);

//    @Override
//    ListenableFuture<OptionLog> findByIdAsync(UUID id);

    @Override
    OptionLog save(OptionLog optionLog);

    @Override
    boolean removeById(UUID id);

    List<OptionLog> findByTenantIdAndOptions(TenantId tenantId, String options, long startTime, long endTime);

    List<OptionLog> findByAuthorityAndOptionsAndTenantId(String authority, String options, TenantId tenantId, Long startTime, Long endTime);

    Map<String, Object> findOptionLogByAuthorityAndTenantId(String authority, String tenantId, Long startTime, Long endTime, PageRequest pageRequest);

    List<OptionLog> findByUserId(UserId id, PageRequest request);

    List<OptionLog> findByAuthorityAndOptionsAndTenantIdAndIp(String authorityParam, String options, TenantId tenantId, Long startTime, Long endTime, String ip);

    List<OptionLog> findByTenantIdAndOptionsAndIp(TenantId tenantId, String options, Long startTime, Long endTime, String ip);

    Map<String, Object> findOptionLogByAuthorityAndTenantIdAndIpAndFirstNameLike(String authorityParam, String defaultCustomerUserId, Long startTime, Long endTime, String ip, String keywaord, PageRequest pageRequest);

    int examine(String id, String examineId, String examineName, String examineTenantId, Long examineTime);

    List<OptionLog> findByAuthorityAndOptionsAndTenantIdAndIpAndFirstNameLike(String authorityParam, String options, TenantId tenantId, Long startTime, Long endTime, String ip, String keyword);

    List<OptionLog> findByTenantIdAndOptionsAndIpAndFirstNameLike(TenantId tenantId, String options, Long startTime, Long endTime, String ip, String keyword);

    PageData<OptionLog> findByAuthorityAndOptionsAndTenantIdAndIpAndFirstNameLike(String authorityParam, String options, TenantId tenantId, Long startTime, Long endTime, String ip, String keyword, Pageable pageable);

    PageData<OptionLog> findByTenantIdAndOptionsAndIpAndFirstNameLike(TenantId tenantId, String options, Long startTime, Long endTime, String ip, String keyword, Pageable pageRequest);

}
