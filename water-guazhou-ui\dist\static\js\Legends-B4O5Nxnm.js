import{d as V,am as x,c as C,o as y,g as a,n as s,p as d,aB as u,aJ as i,bh as p,q as _,i as h,cs as B,bk as v,C as L}from"./index-r0dFAfgr.js";const D={class:"legend-list"},E={class:"label"},I=V({__name:"Legends",props:{data:{},header:{}},emits:["click"],setup(k,{emit:m}){const f=m,o=k;x(()=>o.data,()=>n());const l=C([]),n=()=>{l.value=o.data||[]},b=c=>{f("click",c)};return y(()=>{n()}),(c,N)=>{const g=v;return a(),s("ul",D,[d("li",null,[(a(!0),s(u,null,i(o.header,(e,t)=>(a(),s("span",{key:t,class:"label header"},p(e),1))),128))]),(a(!0),s(u,null,i(h(l),(e,t)=>(a(),s("li",{key:t},[d("span",E,p(e.label),1),_(g,{modelValue:e.checked,"onUpdate:modelValue":r=>e.checked=r},null,8,["modelValue","onUpdate:modelValue"]),_(h(B),{icon:"ep:location",color:"#409eff",style:{cursor:"pointer"},onClick:r=>b(e)},null,8,["onClick"])]))),128))])}}}),q=L(I,[["__scopeId","data-v-962ae19d"]]);export{q as default};
