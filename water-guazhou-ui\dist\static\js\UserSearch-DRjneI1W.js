import{d as _,c as u,r as s,g as f,n as d,q as c,i,p as g,aB as m,aq as b,C as h}from"./index-r0dFAfgr.js";import{_ as y}from"./Search-NSrhrIa_.js";import{g as v}from"./gisUser-Ba96nctf.js";const x={class:"table-box"},S=_({__name:"UserSearch",props:{view:{}},setup(k){const n=u(),e=s({dataList:[],columns:[{label:"用户编号",prop:"count"},{label:"用户姓名",prop:"fee"},{label:"用户地址",prop:"time"}],pagination:{refreshData:({page:t,size:a})=>{e.pagination.page=t,e.pagination.limit=a,r()},layout:"total,sizes, jumper"}}),l=s({filters:[{type:"input",label:"阀门编号",field:"valve"},{type:"btn-group",btns:[{perm:!0,text:"查询",type:"default",loading:()=>e.loading===!0,click:()=>r()},{perm:!0,type:"danger",text:"导出",disabled:()=>e.loading===!0,click:()=>{}}]}]}),r=async()=>{var t;e.loading=!0;try{const{valve:a}=((t=n.value)==null?void 0:t.queryParams)||{},o=await v(a==null?void 0:a.split(","));e.dataList=o.data}catch(a){console.dir(a)}e.loading=!1};return(t,a)=>{const o=y,p=b;return f(),d(m,null,[c(o,{ref_key:"refSearch",ref:n,style:{padding:"0"},config:i(l)},null,8,["config"]),g("div",x,[c(p,{config:i(e)},null,8,["config"])])],64)}}}),q=h(S,[["__scopeId","data-v-272ccea1"]]);export{q as default};
