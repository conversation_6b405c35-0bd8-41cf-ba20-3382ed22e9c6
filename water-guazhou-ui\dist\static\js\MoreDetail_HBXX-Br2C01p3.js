import{_ as x}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{z as g,d as C,c as _,r as d,b as l,S as M,o as k,g as v,n as I,q as f,i as m,aB as F,aq as S,C as q}from"./index-r0dFAfgr.js";import{_ as w}from"./Search-NSrhrIa_.js";import{O as B,F as P}from"./options-D6DdN2k5.js";const L=o=>g({url:"/api/spp/dma/partition/custMeter/list",method:"get",params:o}),O=o=>g({url:"/api/spp/dma/partition/custMeter",method:"post",data:o}),T=o=>g({url:"/api/spp/dma/partition/custMeter",method:"delete",data:o}),X=C({__name:"MoreDetail_HBXX",props:{partition:{}},setup(o){const b=o,c=_(),u=_(),h=d({filters:[{labelWidth:60,type:"input",label:"水表口径",field:"caliber",clearable:!1},{type:"input",label:"水表类型",field:"type",clearable:!1},{type:"input",label:"水表厂家",field:"brand",clearable:!1}],operations:[{type:"btn-group",btns:[{perm:!0,iconifyIcon:"ep:search",text:"查询",type:"primary",click:()=>n()},{perm:!0,iconifyIcon:"ep:refresh",text:"重置",type:"default",click:()=>{var e;(e=c.value)==null||e.resetForm()}},{perm:!0,iconifyIcon:"ep:circle-plus",text:"新增",type:"success",click:()=>y()}]}]}),r=d({dataList:[],columns:[{label:"口径",prop:"caliber"},{label:"数量",prop:"num"},{label:"厂家",prop:"brand"},{label:"类型",prop:"type",formatter(e,t){return B[t]??t}},{label:"是否集抄",prop:"isCollectCopy",formatter(e,t){return t==="1"?"是":t==="0"?"否":""}},{label:"备注",prop:"remark"},{label:"现场图片",prop:"img",image:!0}],pagination:{refreshData:({page:e,size:t})=>{r.pagination.page=e,r.pagination.limit=t,n()}},operations:[{perm:!0,text:"编辑",iconifyIcon:"ep:edit",click:e=>y(e)},{perm:!0,text:"删除",iconifyIcon:"ep:delete",type:"danger",click:e=>D(e)}]}),n=async()=>{var e,t;r.loading=!0;try{const a=((e=c.value)==null?void 0:e.queryParams)||{},p=(await L({...a,partitionId:(t=b.partition)==null?void 0:t.value,page:r.pagination.page||1,size:r.pagination.limit||20})).data.data||{};r.dataList=p.data||[],r.pagination.total=p.total||0}catch{}r.loading=!1},y=e=>{var t;s.defaultValue={...e||{}},s.title=e?"编辑户表":"添加户表",(t=u.value)==null||t.openDialog()},D=e=>{const t=e?[e.id]:[];if(!t.length){l.error("请选择要删除的数据");return}M("确定删除?","提示信息").then(async()=>{try{const a=await T(t);a.data.code===200?(l.success("删除成功"),n()):l.error(a.data.message)}catch{l.error("删除失败")}}).catch(()=>{})},s=d({title:"添加户表",dialogWidth:600,labelPosition:"right",group:[{fields:[{lg:24,xl:12,type:"input-number",label:"口径",field:"caliber"},{lg:24,xl:12,type:"input-number",label:"数量",field:"num"},{lg:24,xl:12,type:"select",label:"类型",field:"type",options:P},{lg:24,xl:12,type:"input",label:"厂家",field:"brand"},{type:"radio",label:"是否集抄",field:"isCollectCopy",options:[{label:"是",value:"1"},{label:"否",value:"0"}]},{type:"textarea",label:"备注",field:"remark"},{type:"image",label:"现场图片",field:"img"}]}],submit:async e=>{var t,a;s.submitting=!0;try{const i=await O({...e,partitionId:(t=b.partition)==null?void 0:t.value});i.data.code===200?(l.success("提交成功"),n(),(a=u.value)==null||a.closeDialog()):l.error(i.data.message)}catch{l.error("提交失败")}s.submitting=!1}});return k(()=>{n()}),(e,t)=>{const a=w,i=S,p=x;return v(),I(F,null,[f(a,{ref_key:"refSearch",ref:c,config:m(h),class:"search"},null,8,["config"]),f(i,{config:m(r),class:"table-box"},null,8,["config"]),f(p,{ref_key:"refDialog",ref:u,config:m(s)},null,8,["config"])],64)}}}),W=q(X,[["__scopeId","data-v-865a7b1d"]]);export{W as default};
