package org.thingsboard.server.dao.model.sql.fault;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 故障信息
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-11-07
 */
@TableName("tb_device_fault_solution")
@Data
public class FaultSolution {
    @TableId
    private String id;

    private String mainId;

    private String name;

    private transient String mainName;

    private String remark;

    private String creator;

    private transient String creatorName;

    private Date createTime;

    private Date updateTime;

    private String tenantId;


}
