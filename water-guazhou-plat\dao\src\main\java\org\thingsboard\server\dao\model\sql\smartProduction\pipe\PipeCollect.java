package org.thingsboard.server.dao.model.sql.smartProduction.pipe;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 管网采集
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2023-12-07
 */

@Data
@TableName("tb_pipe_collect")
public class PipeCollect {

    private String id;

    private String name;

    private String code;

    private String type;

    private String workOrderId;

    private String creator;

    @TableField(exist = false)
    private String creatorName;

    private Date createTime;

    private Date receiveTime;

    private String processUser;

    @TableField(exist = false)
    private String processUserName;

    @TableField(exist = false)
    private String processUserDepartmentId;

    private Date processTime;

    private String processRemark;

    private String reviewUser;

    @TableField(exist = false)
    private String reviewUserName;

    private Date reviewTime;

    private String reviewRemark;

    private String status;

    private String remark;

    private String address;

    private String tenantId;

}
