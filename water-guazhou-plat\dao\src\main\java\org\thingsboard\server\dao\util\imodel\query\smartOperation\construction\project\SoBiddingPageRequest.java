package org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoBidding;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;

import java.util.Date;

@Getter
@Setter
public class SoBiddingPageRequest extends AdvancedPageableQueryEntity<SoBidding, SoBiddingPageRequest> {
    // 所属项目编号
    private String projectCode;

    // 所属项目名称
    private String projectName;

    // 所属项目类别id
    private String projectTypeId;

    // 所属项目启动时间开始
    private Date startTimeFrom;

    // 所属项目启动时间截止
    private Date startTimeTo;

}
