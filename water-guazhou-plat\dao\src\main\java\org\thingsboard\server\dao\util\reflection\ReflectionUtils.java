package org.thingsboard.server.dao.util.reflection;

import lombok.extern.slf4j.Slf4j;
import org.thingsboard.server.dao.util.imodel.response.tree.TreeEntityNode;

import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Slf4j
public class ReflectionUtils {
    @SuppressWarnings("unchecked")
    public static <T> T getValue(Field field, Object o) {
        boolean accessible = field.isAccessible();
        field.setAccessible(true);
        Object val;
        try {
            val = field.get(o);
            return (T) val;
        } catch (IllegalAccessException e) {
            throw ExceptionUtils.createSilentThrow("[LFT] Unexpected error happened on get value of %s from %s",
                    field.getName(), field.getName(), o.getClass().getName());
        } finally {
            field.setAccessible(accessible);
        }
    }

    public static String conditionalSetValue(Field field, Object value, Object entity, boolean condition) {
        if (!condition)
            return null;
        boolean accessible = field.isAccessible();
        field.setAccessible(true);
        try {
            field.set(entity, value);
            return null;
        } catch (Exception e) {
            throw ExceptionUtils.createSilentThrow("[LFT] Unexpected error happened on get value of %s from %s", field.getName(), value.getClass().getName());
        } finally {
            field.setAccessible(accessible);
        }
    }

    public static <T extends TreeEntityNode> T newInstance(Class<T> clazz, Class<?> argType, Object arg) {
        try {
            Constructor<T> constructor = clazz.getConstructor(argType);
            return constructor.newInstance(arg);
        } catch (NoSuchMethodException e) {
            throw ExceptionUtils.createSilentThrow("[LFT] Unexpected error happened on get constructor of %s", clazz.getName());
        } catch (InvocationTargetException | InstantiationException | IllegalAccessException e) {
            throw ExceptionUtils.createSilentThrow("[LFT] Unable to instantiate object of %s", clazz.getName());
        }
    }

    public static Method getMethod(String methodName, Class<?>[] types, Class<?> host) {
        try {
            return host.getDeclaredMethod(methodName, types);
        } catch (NoSuchMethodException e) {
            throw ExceptionUtils.createSilentThrow("[LFT] error to get method %s of %s", methodName, host.getName());
        }
    }

    @SuppressWarnings("unchecked")
    public static <T> T callMethod(Method method, Object[] parameters, Object entity) {
        boolean accessible = method.isAccessible();
        method.setAccessible(true);
        try {
            return (T) method.invoke(entity, parameters);
        } catch (IllegalAccessException | InvocationTargetException e) {
            throw ExceptionUtils.createSilentThrow("[LFT] error to call method %s of %s", method.getName(), entity.getClass().getName());
        } finally {
            method.setAccessible(accessible);
        }
    }

    @SuppressWarnings("unchecked")
    public static <T> T callMethod(String method, Object entity) {
        return callMethod(getMethod(method, new Class[0], entity.getClass()), entity);
    }

    public static <T> T callMethod(Method method, Object entity) {
        boolean accessible = method.isAccessible();
        method.setAccessible(true);
        try {
            return (T) method.invoke(entity);
        } catch (IllegalAccessException | InvocationTargetException e) {
            throw ExceptionUtils.createSilentThrow("[LFT] error to call method %s of %s", method.getName(), entity.getClass().getName());
        } finally {
            method.setAccessible(accessible);
        }
    }

    public static Field getField(Class<?> clazz, String fieldName) {
        try {
            return clazz.getDeclaredField(fieldName);
        } catch (NoSuchFieldException e) {
            throw ExceptionUtils.createSilentThrow("[LFT] 对象 %s 中不包含字段 %s", clazz.getName(), fieldName);
        }
    }

    @SuppressWarnings("rawtypes")
    public static List<Field> getAllField(Class clazz) {
        if (clazz == null) {
            return Collections.emptyList();
        }
        List<Field> fieldList = new ArrayList<>();

        Collections.addAll(fieldList, clazz.getDeclaredFields());
        //noinspection ConstantValue
        while (clazz != null && !Object.class.equals(clazz)) {
            clazz = clazz.getSuperclass();
            Collections.addAll(fieldList, clazz.getDeclaredFields());
        }

        return fieldList;
    }

}
