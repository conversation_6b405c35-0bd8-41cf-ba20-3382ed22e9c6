import * as echarts from 'echarts';
import { useAppStore } from '@/store';
import { getOrderTypeOptions } from '../config';
// 柱状图
export const zhuzhuangtu = (data: any[], name?: string) => {
  const Data: number[] = [];
  const xData = data?.map((item) => {
    Data.push(item.value);
    return item.key;
  });
  const _DEPTNUMER = []; // 存入所有科室
  let showEchart = false;
  let namenum = 0;
  if (_DEPTNUMER.length > 0) {
    namenum = Math.floor(100 / (_DEPTNUMER.length / 3)); // 这个3可以顺便调整是用来判断当前视图要显示几个
    if (_DEPTNUMER.length > 3) {
      // 3也可以调整用来判断是否显示滚动条
      showEchart = true;
    } else {
      showEchart = false;
    }
  }
  const options = {
    backgroundColor: useAppStore().isDark ? '#131624' : '#F4F7FA',
    tooltip: {
      trigger: 'item'
    },
    grid: {
      left: 50,
      top: 30,
      right: 30,
      bottom: 50
    },
    legend: {
      show: true,
      icon: 'circle',
      orient: 'horizontal', // 横向排列
      top: '90.5%',
      right: 'center',
      itemWidth: 16.5,
      itemHeight: 6,
      // itemGap: 30,
      textStyle: {
        // color: '#FFFFFF'
        color: '#666',
        fontSize: 14
      }
    },

    dataZoom: [
      {
        type: 'slider',
        realtime: true,
        start: 0,
        end: namenum, // 数据窗口范围的结束百分比。范围是：0 ~ 100。
        height: 5, // 组件高度
        left: 5, // 左边的距离
        right: 5, // 右边的距离
        bottom: 10, // 下边的距离
        show: showEchart, // 是否展示
        fillerColor: 'rgba(17, 100, 210, 0.42)', // 滚动条颜色
        borderColor: 'rgba(17, 100, 210, 0.12)',
        handleSize: 0, // 两边手柄尺寸
        showDetail: false, // 拖拽时是否展示滚动条两侧的文字
        zoomLock: true, // 是否只平移不缩放
        moveOnMouseMove: false, // 鼠标移动能触发数据窗口平移
        // zoomOnMouseWheel: false, //鼠标移动能触发数据窗口缩放

        // 下面是自己发现的一个问题，当点击滚动条横向拖拽拉长滚动条时，会出现文字重叠，导致效果很不好，以此用下面四个属性进行设置，当拖拽时，始终保持显示六个柱状图，可结合自己情况进行设置。添加这个属性前后的对比见**图二**
        startValue: 0, // 从头开始。
        endValue: 6, // 最多六个
        minValueSpan: 6, // 放大到最少几个
        maxValueSpan: 6 //  缩小到最多几个
      },
      {
        type: 'inside', // 支持内部鼠标滚动平移
        start: 0,
        end: namenum,
        zoomOnMouseWheel: false, // 关闭滚轮缩放
        moveOnMouseWheel: true, // 开启滚轮平移
        moveOnMouseMove: true // 鼠标移动能触发数据窗口平移
      }
    ],

    xAxis: [
      {
        data: xData,
        axisLabel: {
          textStyle: {
            color: '#666',
            fontSize: 12
          },
          margin: 30 // 刻度标签与轴线之间的距离。
        },

        axisLine: {
          show: true, // 不显示x轴
          lineStyle: {
            color: '#666'
          }
        },
        axisTick: {
          show: false // 不显示刻度
        },
        boundaryGap: true,
        splitLine: {
          show: false,
          width: 0.08,
          lineStyle: {
            type: 'solid',
            color: '#666'
          }
        }
      }
    ],
    yAxis: [
      {
        name,
        splitLine: {
          show: true,
          lineStyle: {
            color: '#666',
            type: 'dashed'
          }
        },
        axisTick: {
          show: false
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: '#666'
          }
        },
        axisLabel: {
          textStyle: {
            color: '#666',
            fontSize: 12
          }
        }
      }
    ],
    series: [
      {
        // 柱底圆片
        name: '',
        type: 'pictorialBar',
        symbolSize: [40, 20], // 调整截面形状
        symbolOffset: [0, 10],
        z: 12,
        itemStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(
              0,
              0,
              0,
              1,
              [
                {
                  offset: 0,
                  color: '#02D6EA'
                },
                {
                  offset: 1,
                  color: '#02D6EA'
                }
              ],
              false
            )
          }
        },
        data: Data
      },

      // 柱体
      {
        name: '',
        type: 'bar',
        barWidth: 40,
        // barGap: '0%',
        label: {
          show: true,
          position: 'top',
          offset: [0, -10]
        },
        itemStyle: {
          normal: {
            color: {
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              type: 'linear',
              global: false,
              colorStops: [
                {
                  // 第一节下面
                  offset: 0,
                  color: '#057DFE'
                },
                {
                  offset: 1,
                  color: '#02D7EA'
                }
              ]
            }
          }
        },

        data: Data
      },

      // 柱顶圆片
      {
        name: '',
        type: 'pictorialBar',
        symbolSize: [40, 20], // 调整截面形状
        symbolOffset: [0, -10],
        z: 12,
        symbolPosition: 'end',
        itemStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(
              0,
              0,
              0,
              1,
              [
                {
                  offset: 0,
                  color: '#50A7FF'
                },
                {
                  offset: 1,
                  color: '#02D6EA'
                }
              ],
              false
            )
          }
        },
        data: Data
      }
    ]
  };
  return options;
};

// 折线图
export const zhexiantu = (data?: any[], type?: string) => {
  const xData: string[] = [];
  const series = data?.map((item, i) => {
    const data =
      item.data?.map((o) => {
        i === 0 &&
          xData.push(
            moment(o.from).format(
              type === 'year'
                ? 'YYYY-MM'
                : type === 'month'
                  ? 'YYYY-MM-DD'
                  : type === 'day'
                    ? 'YYYY-MM-DD HH:mm'
                    : 'YYYY-MM-DD HH:mm:ss'
            )
          );
        return o.value;
      }) || [];
    return {
      name: item.key,
      type: 'line',
      data,
      smooth: true,
      markPoint: {
        data: [
          { type: 'max', name: 'Max' },
          { type: 'min', name: 'Min' }
        ]
      }
    };
  });

  const option = {
    backgroundColor: useAppStore().isDark ? '#131624' : '#F4F7FA',
    color: ['#318DFF', '#A431FF', '#FC2B2B'],
    grid: {
      left: 40,
      right: 30,
      top: 60,
      bottom: 20
    },
    legend: {
      top: 10,
      textStyle: {
        // color: '#FFFFFF'
        color: '#666',
        fontSize: 14
      }
    },
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: xData
    },
    yAxis: {
      name: '事件数量',
      type: 'value',

      splitLine: {
        show: true,
        lineStyle: {
          color: '#666',
          type: 'dashed'
        }
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: '#666'
        }
      },
      axisLabel: {
        textStyle: {
          color: '#666',
          fontSize: 12
        }
      }
    },
    series
  };
  return option;
};
export const PieChartOption = (data: any[], title: string) => {
  const Data =
    data?.map((item) => ({ name: item.key, value: item.value })) || [];
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b} : {c}件 ({d}%)'
    },
    legend: {
      type: 'scroll',
      orient: 'vertical',
      right: 30,
      top: 20,
      bottom: 20,
      textStyle: {
        color: useAppStore().isDark ? '#eee' : '#666'
      }
    },
    series: [
      {
        name: title,
        type: 'pie',
        radius: ['40%', '60%'],
        center: ['45%', '50%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 5,
          borderColor: 'transparent',
          borderWidth: 2
        },
        label: {
          // show: false,
          // position: 'center',
          itemStyle: {
            color: '#fff'
          },
          formatter: '{b} : {c} ({d}%)'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 16,
            fontWeight: 'bold'
          }
        },
        // labelLine: {
        //   show: false
        // },
        data: Data
      }
    ]
  };
  return option;
};

export const BarOption = (data: any[]) => {
  const yData: number[] = [];
  const xData = data.map((item) => {
    yData.push(item.value);
    return item.key;
  });
  const option = {
    tooltip: {
      trigger: 'item'
    },
    grid: {
      left: 40,
      top: 40,
      bottom: 40,
      right: 30
    },
    xAxis: {
      data: xData,
      interval: 'auto'
    },
    yAxis: {
      name: '件',
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#999'
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: '#666',
          type: 'dashed'
        }
      }
    },
    dataZoom: [
      {
        type: 'inside'
      }
    ],
    series: [
      {
        type: 'bar',
        showBackground: true,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#188df0' },
            { offset: 0.5, color: '#188df0' },
            { offset: 1, color: '#83bff6' }
          ])
        },
        barMaxWidth: 40,
        emphasis: {
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#83bff6' },
              { offset: 0.7, color: '#2378f7' },
              { offset: 1, color: '#2378f7' }
            ])
          }
        },
        data: yData
      }
    ]
  };
  return option;
};
export const EventTypeTableData = () => {
  const eventTypes = getOrderTypeOptions();
  const tableData = eventTypes.map((item) => ({
    eventType: item.label,
    total: 0,
    jan: Number((Math.random() * 50 + 30).toFixed(0)),
    feb: Number((Math.random() * 50 + 30).toFixed(0)),
    mar: Number((Math.random() * 50 + 30).toFixed(0)),
    apr: Number((Math.random() * 50 + 30).toFixed(0)),
    may: Number((Math.random() * 50 + 30).toFixed(0)),
    jun: Number((Math.random() * 50 + 30).toFixed(0)),
    jul: Number((Math.random() * 50 + 30).toFixed(0))
  }));
  return tableData.map((obj) => {
    obj.total =
      obj.jan + obj.feb + obj.mar + obj.apr + obj.mar + obj.jun + obj.jul;
    return obj;
  });
};
export const EventTrendTableColumns: IFormTableColumn[] = [
  { minWidth: 150, label: '事件类型', prop: 'eventType' },
  { minWidth: 100, label: '小计', prop: 'total' },
  { minWidth: 100, label: '一月', prop: '01' },
  { minWidth: 100, label: '二月', prop: '02' },
  { minWidth: 100, label: '三月', prop: '03' },
  { minWidth: 100, label: '四月', prop: '04' },
  { minWidth: 100, label: '五月', prop: '05' },
  { minWidth: 100, label: '六月', prop: '06' },
  { minWidth: 100, label: '七月', prop: '07' },
  { minWidth: 100, label: '八月', prop: '08' },
  { minWidth: 100, label: '九月', prop: '09' },
  { minWidth: 100, label: '十月', prop: '10' },
  { minWidth: 100, label: '十一月', prop: '11' },
  { minWidth: 100, label: '十二月', prop: '12' }
];
export const LineOption = (data?: any[]) => {
  const xData = EventTrendTableColumns.map((item) => item.label);
  const series =
    data?.map((obj) => ({
      name: obj.eventType,
      type: 'line',
      data: [
        obj['01'],
        obj['02'],
        obj['03'],
        obj['04'],
        obj['05'],
        obj['06'],
        obj['07'],
        obj['08'],
        obj['09'],
        obj['010'],
        obj['11'],
        obj['12']
      ],
      smooth: true,
      markPoint: {
        data: [
          { type: 'max', name: 'Max' },
          { type: 'min', name: 'Min' }
        ]
      }
    })) || [];
  const option = {
    grid: {
      left: 40,
      right: 30,
      top: 70,
      bottom: 40
    },
    legend: {
      right: 30,
      textStyle: {
        // color: '#FFFFFF'
        color: '#666',
        fontSize: 14
      }
    },
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: xData
    },
    yAxis: {
      name: '事件',
      type: 'value',

      splitLine: {
        show: true,
        lineStyle: {
          color: '#666',
          type: 'dashed'
        }
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: '#666'
        }
      },
      axisLabel: {
        textStyle: {
          color: '#666',
          fontSize: 12
        }
      }
    },
    series
  };
  return option;
};

export const EventTypeAnalysData = () => {
  const eventTypes = getOrderTypeOptions();
  const tableData = eventTypes.map((item) => ({
    eventType: item.label,
    total: Number((Math.random() * 250 + 100).toFixed(0)),
    ratio: '0'
  }));
  const total = tableData.reduce((old, cur) => old + cur.total, 0) || 1;
  return tableData.map((item) => {
    item.ratio = `${(item.total / total) * 100}%`;
    return item;
  });
};
export const EventTypeTableColumns: IFormTableColumn[] = [
  { minWidth: 150, label: '事件类型', prop: 'eventType' },
  { minWidth: 100, label: '数量', prop: 'total' },
  { minWidth: 100, label: '比例', prop: 'ratio' }
];
