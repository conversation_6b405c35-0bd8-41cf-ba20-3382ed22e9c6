/**
 * Copyright © 2016-2019 The Thingsboard Authors
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.common.transport;

import org.thingsboard.server.common.data.rpc.ToDeviceRpcRequestBody;

/**
 * <AUTHOR>
 * @date 2020/1/17 10:54
 */
public interface RpcToFyService {
    boolean sendToDevice(String deviceSecret, String boxId, String body);

    boolean pushDataNow(String deviceSecret, String boxId, String body);

    boolean sendModelToDevice(String deviceSecret, String body);

    Object sendToModbus(String deviceSecret, ToDeviceRpcRequestBody body,int requestId);

}
