package org.thingsboard.server.dao.util.imodel.query;

import java.util.List;
import java.util.function.Function;

public class TreeQuery {
    /**
     * 限制树的高度
     */
    private final int limit;

    /**
     * 子级查询方法引用
     */
    private final Function<String, List<?>> childQuery;

    private TreeQuery(Function<String, List<?>> childQuery, int limit) {
        this.childQuery = childQuery;
        this.limit = limit;
    }

    public static TreeQuery of(Function<String, List<?>> childQuery) {
        return new TreeQuery(childQuery, Integer.MIN_VALUE);
    }

    /**
     * @param childQuery 子级查询方法引用
     * @param limit      小于或等于0代表无限树高度
     */
    public static TreeQuery of(Function<String, List<?>> childQuery, int limit) {
        return new TreeQuery(childQuery, limit - 1);
    }

    public int limit() {
        return limit;
    }

    public List<?> apply(String id) {
        return childQuery.apply(id);
    }
}
