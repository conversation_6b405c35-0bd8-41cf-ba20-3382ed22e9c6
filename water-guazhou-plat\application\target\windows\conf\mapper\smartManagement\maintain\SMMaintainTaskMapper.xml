<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartManagement.maintain.SMMaintainTaskMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->id,
                           name,
                           device,
                           device_name,
                           maintain_user,
                           begin_time,
                           end_time,
                           status,
                           remark,
                           create_time,
                           creator,
                           tenant_id,
                           (select count(1) from sm_maintain_task_item where task_id = out.id) total_task_item_count,
                           (select count(1) from sm_maintain_task_item where task_id = out.id and complete_time is not null) completed_task_item_count
        <!--@sql from sm_maintain_task out -->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.smartManagement.maintaince.SMMaintainTask">
        <result column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="device" property="device"/>
        <result column="device_name" property="deviceName"/>
        <result column="maintain_user" property="maintainUser"/>
        <result column="begin_time" property="beginTime"/>
        <result column="end_time" property="endTime"/>
        <result column="status" property="status"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <result column="creator" property="creator"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="total_task_item_count" property="totalTaskItemCount"/>
        <result column="completed_task_item_count" property="completedTaskItemCount"/>
    </resultMap>

    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sm_maintain_task out
        <where>
            <if test="maintainUser != null and maintainUser != ''">
                and maintain_user = #{maintainUser}
            </if>
            <if test="isComplete != null">
                and (status = #{actualCompleteStatus}) = #{isComplete}
            </if>
            <if test="isAssigned != null">
                and (status in
                <foreach collection="atAssignedStatus" open="(" close=")" separator="," item="element">
                    #{element}
                </foreach>) = #{isAssigned}
            </if>
            <if test="fromTime != null">
                and create_time >= #{fromTime}
            </if>
            <if test="toTime != null">
                and create_time &lt;= #{toTime}
            </if>
            and tenant_id = #{tenantId}
        </where>
        order by create_time desc
    </select>

    <update id="update">
        update sm_maintain_task
        <set>
            <if test="name != null">
                name = #{name},
            </if>
            <if test="device != null">
                device = #{device},
            </if>
            <if test="deviceName != null">
                device_name = #{deviceName},
            </if>
            <if test="maintainUser != null">
                maintain_user = #{maintainUser},
            </if>
            <if test="beginTime != null">
                begin_time = #{beginTime},
            </if>
            <if test="endTime != null">
                end_time = #{endTime},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
        </set>
        where id = #{id}
    </update>

    <update id="assign">
        update sm_maintain_task
        set maintain_user = #{maintainUser},
            begin_time    = #{beginTime},
            end_time      = #{endTime},
            remark        = #{remark},
            status        = #{assignStatus}
        where id = #{taskId}
    </update>

    <update id="complete">
        update sm_maintain_task
        set status = #{completeStatus}
        where id = #{taskId}
    </update>

    <select id="countStatus" resultType="org.thingsboard.server.dao.model.sql.statistic.GeneralTaskStatusStatistic">
        select sm_maintain_task_status_percent_of_user(#{userId}, #{status})
    </select>

    <select id="totalOfUser" resultType="java.lang.Integer">
        select count(1)
        from sm_maintain_task
        where maintain_user = #{userId};
    </select>

    <select id="totalStatusOfUser" resultType="java.lang.Integer">
        select count(1)
        from sm_maintain_task
        where status in
        <foreach collection="status" open="(" close=")" item="element" separator=",">
            #{element}
        </foreach>
        and maintain_user = #{userId}
    </select>
</mapper>