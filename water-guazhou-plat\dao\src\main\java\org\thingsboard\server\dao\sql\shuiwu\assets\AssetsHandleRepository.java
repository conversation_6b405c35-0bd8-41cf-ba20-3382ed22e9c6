package org.thingsboard.server.dao.sql.shuiwu.assets;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.repository.CrudRepository;
import org.thingsboard.server.dao.model.sql.shuiwu.assets.AssetsHandleEntity;

/**
 * 设备处置
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2021-10-19
 */
public interface AssetsHandleRepository extends CrudRepository<AssetsHandleEntity, String> {
    Page<AssetsHandleEntity> findAllByHandleNoLikeAndStatusLikeAndReviewerIdLikeAndApplicantIdLikeAndHandleTypeLikeAndTenantIdAndCreateTimeBetween(String handleNo, String status, String reviewerId, String applicantId, String handleType, String tenantId, Long start, Long end, Pageable pageable);
}
