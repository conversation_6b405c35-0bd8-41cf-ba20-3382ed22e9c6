<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.base.BaseUnderlyingConfigurationMapper">
    
    <resultMap type="org.thingsboard.server.dao.model.sql.base.BaseUnderlyingConfiguration" id="BaseUnderlyingConfigurationResult">
        <result property="id"    column="id"    />
        <result property="hostAddress"    column="host_address"    />
        <result property="hostPort"    column="host_port"    />
        <result property="name"    column="name"    />
        <result property="username"    column="username"    />
        <result property="password"    column="password"    />
        <result property="urlEx"    column="url_ex"    />
        <result property="poolConfig"    column="pool_config"    />
        <result property="safeConfig"    column="safe_config"    />
        <result property="connTimeout"    column="conn_timeout"    />
        <result property="siteId"    column="site_id"    />
        <result property="ipAddress"    column="ip_address"    />
        <result property="dbVersion"    column="db_version"    />
    </resultMap>

    <sql id="selectBaseUnderlyingConfigurationVo">
        select id, host_address, host_port, name, username, password, url_ex, pool_config, safe_config, conn_timeout, site_id, ip_address, db_version from base_underlying_configuration
    </sql>

    <select id="selectBaseUnderlyingConfigurationList" parameterType="org.thingsboard.server.dao.model.sql.base.BaseUnderlyingConfiguration" resultMap="BaseUnderlyingConfigurationResult">
        <include refid="selectBaseUnderlyingConfigurationVo"/>
        <where>  
            <if test="hostAddress != null  and hostAddress != ''"> and host_address = #{hostAddress}</if>
            <if test="hostPort != null  and hostPort != ''"> and host_port = #{hostPort}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="username != null  and username != ''"> and username like concat('%', #{username}, '%')</if>
            <if test="password != null  and password != ''"> and password = #{password}</if>
            <if test="urlEx != null  and urlEx != ''"> and url_ex = #{urlEx}</if>
            <if test="poolConfig != null  and poolConfig != ''"> and pool_config = #{poolConfig}</if>
            <if test="safeConfig != null  and safeConfig != ''"> and safe_config = #{safeConfig}</if>
            <if test="connTimeout != null  and connTimeout != ''"> and conn_timeout = #{connTimeout}</if>
            <if test="siteId != null  and siteId != ''"> and site_id = #{siteId}</if>
            <if test="ipAddress != null  and ipAddress != ''"> and ip_address = #{ipAddress}</if>
            <if test="dbVersion != null  and dbVersion != ''"> and db_version = #{dbVersion}</if>
        </where>
    </select>
    
    <select id="selectBaseUnderlyingConfigurationById" parameterType="String" resultMap="BaseUnderlyingConfigurationResult">
        <include refid="selectBaseUnderlyingConfigurationVo"/>
        where id = #{id}
    </select>

    <insert id="insertBaseUnderlyingConfiguration" parameterType="org.thingsboard.server.dao.model.sql.base.BaseUnderlyingConfiguration">
        insert into base_underlying_configuration
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="hostAddress != null">host_address,</if>
            <if test="hostPort != null">host_port,</if>
            <if test="name != null">name,</if>
            <if test="username != null">username,</if>
            <if test="password != null">password,</if>
            <if test="urlEx != null">url_ex,</if>
            <if test="poolConfig != null">pool_config,</if>
            <if test="safeConfig != null">safe_config,</if>
            <if test="connTimeout != null">conn_timeout,</if>
            <if test="siteId != null">site_id,</if>
            <if test="ipAddress != null">ip_address,</if>
            <if test="dbVersion != null">db_version,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="hostAddress != null">#{hostAddress},</if>
            <if test="hostPort != null">#{hostPort},</if>
            <if test="name != null">#{name},</if>
            <if test="username != null">#{username},</if>
            <if test="password != null">#{password},</if>
            <if test="urlEx != null">#{urlEx},</if>
            <if test="poolConfig != null">#{poolConfig},</if>
            <if test="safeConfig != null">#{safeConfig},</if>
            <if test="connTimeout != null">#{connTimeout},</if>
            <if test="siteId != null">#{siteId},</if>
            <if test="ipAddress != null">#{ipAddress},</if>
            <if test="dbVersion != null">#{dbVersion},</if>
         </trim>
    </insert>

    <update id="updateBaseUnderlyingConfiguration" parameterType="org.thingsboard.server.dao.model.sql.base.BaseUnderlyingConfiguration">
        update base_underlying_configuration
        <trim prefix="SET" suffixOverrides=",">
            <if test="hostAddress != null">host_address = #{hostAddress},</if>
            <if test="hostPort != null">host_port = #{hostPort},</if>
            <if test="name != null">name = #{name},</if>
            <if test="username != null">username = #{username},</if>
            <if test="password != null">password = #{password},</if>
            <if test="urlEx != null">url_ex = #{urlEx},</if>
            <if test="poolConfig != null">pool_config = #{poolConfig},</if>
            <if test="safeConfig != null">safe_config = #{safeConfig},</if>
            <if test="connTimeout != null">conn_timeout = #{connTimeout},</if>
            <if test="siteId != null">site_id = #{siteId},</if>
            <if test="ipAddress != null">ip_address = #{ipAddress},</if>
            <if test="dbVersion != null">db_version = #{dbVersion},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBaseUnderlyingConfigurationById" parameterType="String">
        delete from base_underlying_configuration where id = #{id}
    </delete>

    <delete id="deleteBaseUnderlyingConfigurationByIds" parameterType="String">
        delete from base_underlying_configuration where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>