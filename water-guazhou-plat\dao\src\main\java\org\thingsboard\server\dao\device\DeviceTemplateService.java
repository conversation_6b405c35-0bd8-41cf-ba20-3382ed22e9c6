package org.thingsboard.server.dao.device;

import com.alibaba.fastjson.JSONObject;
import org.thingsboard.server.common.data.device.DeviceTemplateAndProtocol;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.model.sql.DeviceTemplate;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;

public interface DeviceTemplateService {
    DeviceTemplate add(DeviceTemplate deviceTemplate);

    DeviceTemplate edit(DeviceTemplate deviceTemplate);

    DeviceTemplate findById(String id);

    List<DeviceTemplate> findDeviceTemplateByTenant(TenantId tenantId);

    DeviceTemplate deleteById(String id) throws ThingsboardException;

    void saveDeviceTemplateAndProtocol(DeviceTemplateAndProtocol deviceTemplateAndProtocol) throws ThingsboardException;

    DeviceTemplateAndProtocol findDeviceTemplateAndProtocolById(String id) throws ThingsboardException;

    void copyDeviceTemplate(String deviceTemplateId) throws ThingsboardException;

    List<DeviceTemplate> findDeviceTemplateByTenantAndType(TenantId tenantId, String type);

    List<DeviceTemplate> findByIds(List<String> ids);

    void saveAll(List<DeviceTemplate> data);

    Map<String, List<JSONObject>> findDeviceTemplateAndProtocolGroup(String id, TenantId tenantId) throws ThingsboardException, ExecutionException, InterruptedException;

    List<JSONObject> findProtocolById(String id, TenantId tenantId) throws Exception;

    void save(DeviceTemplate template);
}
