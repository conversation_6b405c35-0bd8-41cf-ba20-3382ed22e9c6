package org.thingsboard.server.dao.util.imodel.query.smartProduction.circuit;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartProduction.circuit.CircuitTaskItem;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

@Getter
@Setter
public class CircuitTaskItemSaveRequest extends SaveRequest<CircuitTaskItem> {
    // 主表ID
    @NotNullOrEmpty(refTable = "sp_circuit_task", parentIgnore = true)
    private String mainId;

    // 配置类型，用于数据隔离。三种类型：水源、水厂、二供泵房。
    @NotNullOrEmpty
    private String type;

    // 巡检项目分类
    @NotNullOrEmpty
    private String itemType;

    // 巡检项目名称
    @NotNullOrEmpty
    private String itemName;

    // 巡检方法
    @NotNullOrEmpty
    private String itemMethod;

    // 巡检要求
    @NotNullOrEmpty
    private String itemRequire;

    // 关联工单，若关联了工单，该字段有值
    private String workOrderId;

    @Override
    public CircuitTaskItem build() {
        CircuitTaskItem entity = new CircuitTaskItem();
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    @Override
    public CircuitTaskItem update(String id) {
        CircuitTaskItem entity = new CircuitTaskItem();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(CircuitTaskItem entity) {
        entity.setMainId(mainId);
        entity.setType(type);
        entity.setItemType(itemType);
        entity.setItemName(itemName);
        entity.setItemMethod(itemMethod);
        entity.setItemRequire(itemRequire);
        entity.setWorkOrderId(workOrderId);
    }
}