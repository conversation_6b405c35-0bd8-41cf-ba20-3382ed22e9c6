import { DefaultTheme } from 'vitepress'

const navs: DefaultTheme.Sidebar = {
  '/components/': [],
  '/git/': [{ text: 'git提交校验', link: '/git/commitlint.md' }],
  '/dependences/': [
    { text: '代码校验', link: '/dependences/eslint.md' },
    { text: '图片压缩', link: '/dependences/vite-plugin-image-optimizer.md' },
    { text: '超图相关', link: '/dependences/supermap.md' }
  ],
  '/deploy/': [
    {
      text: '打包部署',
      link: '/deploy/index.md'
    }
  ],
  '/project/': [
    { text: '全局配置', link: '/project/window.md' },
    { text: '首屏配置', link: '/project/firstScreen.md' }
  ],
  '/gis/': [
    {
      text: '静态资源本地化',
      link: '/gis/localization.md'
    },
    {
      text: '全局配置',
      link: '/gis/config.md'
    },
    {
      text: '三维管网发布',
      link: '/gis/d3pipe.md'
    }
  ]
}
export default navs
