import{r as P}from"./AnimatedLinesLayer-B2VbV4jv.js";import"./Point-WxyopZva.js";import"./index-r0dFAfgr.js";import"./MapView-DaoQedLH.js";import"./widget-BcWKanF2.js";import"./pe-B8dP0-Ut.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./TileLayer-B5vQ99gG.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./commonProperties-DqNQ4F00.js";import"./project-DUuzYgGl.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";const E="guazhou",C=g=>{debugger;const e=g.geometry,a=g.type;if(console.log("转换几何对象为GML:",e),console.log("要素类型:",a),!e)throw console.error("无效的几何对象:",e),new Error("无效的几何对象");let s=a?a.toLowerCase():"";console.log("原始type属性:",s);const l="EPSG:3857";switch(s){case"point":return`<gml:Point srsName="${l}">
        <gml:pos>${e.x} ${e.y}</gml:pos>
      </gml:Point>`;case"multipoint":const p=e.points||[];if(p.length===0)throw console.error("多点几何对象没有点"),new Error("多点几何对象没有点");const u=p.map(r=>`<gml:pointMember>
          <gml:Point>
            <gml:pos>${r[0]} ${r[1]}</gml:pos>
          </gml:Point>
        </gml:pointMember>`).join(`
`);return`<gml:MultiPoint srsName="${l}">
        ${u}
      </gml:MultiPoint>`;case"polyline":const i=e.paths||[];if(i.length===0)throw console.error("线几何对象没有路径"),new Error("线几何对象没有路径");const t=i[0];if(!t||t.length<2)throw console.error("线几何对象路径点数不足"),new Error("线几何对象路径点数不足");const o=t.map(r=>`${r[0]} ${r[1]}`).join(" ");return`<gml:LineString srsName="${l}">
        <gml:posList>${o}</gml:posList>
      </gml:LineString>`;case"multilinestring":{const r=e.paths||[];if(r.length===0)throw console.error("多线几何对象没有路径"),new Error("多线几何对象没有路径");const h=r.map(L=>!L||L.length<2?(console.error("多线几何对象路径点数不足"),""):`<gml:lineStringMember>
          <gml:LineString>
            <gml:posList>${L.map(y=>`${y[0]} ${y[1]}`).join(" ")}</gml:posList>
          </gml:LineString>
        </gml:lineStringMember>`).filter(L=>L!=="").join(`
`);if(h==="")throw console.error("多线几何对象没有有效路径"),new Error("多线几何对象没有有效路径");return`<gml:MultiLineString srsName="${l}">
        ${h}
      </gml:MultiLineString>`}case"polygon":const n=e.rings||[];if(n.length===0)throw console.error("面几何对象没有环"),new Error("面几何对象没有环");const c=n[0];if(!c||c.length<4)throw console.error("面几何对象外环点数不足"),new Error("面几何对象外环点数不足");const w=c.map(r=>`${r[0]} ${r[1]}`).join(" ");let m=`<gml:Polygon srsName="${l}">
        <gml:exterior>
          <gml:LinearRing>
            <gml:posList>${w}</gml:posList>
          </gml:LinearRing>
        </gml:exterior>`;for(let r=1;r<n.length;r++){const h=n[r];if(h&&h.length>=4){const L=h.map(M=>`${M[0]} ${M[1]}`).join(" ");m+=`
        <gml:interior>
          <gml:LinearRing>
            <gml:posList>${L}</gml:posList>
          </gml:LinearRing>
        </gml:interior>`}}return m+=`
      </gml:Polygon>`,m;case"multipolygon":const $=e.polygons||[];if($.length===0)throw console.error("多面几何对象没有面"),new Error("多面几何对象没有面");const f=$.map(r=>{if(!r||!r.rings||r.rings.length===0)return console.error("多面几何对象面没有环"),"";const h=r.rings[0];if(!h||h.length<4)return console.error("多面几何对象面外环点数不足"),"";let M=`<gml:polygonMember>
          <gml:Polygon>
            <gml:exterior>
              <gml:LinearRing>
                <gml:posList>${h.map(y=>`${y[0]} ${y[1]}`).join(" ")}</gml:posList>
              </gml:LinearRing>
            </gml:exterior>`;for(let y=1;y<r.rings.length;y++){const T=r.rings[y];if(T&&T.length>=4){const N=T.map(F=>`${F[0]} ${F[1]}`).join(" ");M+=`
            <gml:interior>
              <gml:LinearRing>
                <gml:posList>${N}</gml:posList>
              </gml:LinearRing>
            </gml:interior>`}}return M+=`
          </gml:Polygon>
        </gml:polygonMember>`,M}).filter(r=>r!=="").join(`
`);if(f==="")throw console.error("多面几何对象没有有效面"),new Error("多面几何对象没有有效面");return`<gml:MultiPolygon srsName="${l}">
        ${f}
      </gml:MultiPolygon>`;case"geometrycollection":const d=e.geometries||[];if(d.length===0)throw console.error("几何集合没有几何对象"),new Error("几何集合没有几何对象");const x=d.map(r=>{try{return`<gml:geometryMember>
            ${C(r)}
          </gml:geometryMember>`}catch(h){return console.error("处理几何集合中的几何对象时出错:",h),""}}).filter(r=>r!=="").join(`
`);if(x==="")throw console.error("几何集合没有有效几何对象"),new Error("几何集合没有有效几何对象");return`<gml:MultiGeometry srsName="${l}">
        ${x}
      </gml:MultiGeometry>`;case"envelope":if(!e.xmin||!e.ymin||!e.xmax||!e.ymax)throw console.error("包络矩形缺少坐标:",e),new Error("包络矩形缺少坐标");return`<gml:Envelope srsName="${l}">
        <gml:lowerCorner>${e.xmin} ${e.ymin}</gml:lowerCorner>
        <gml:upperCorner>${e.xmax} ${e.ymax}</gml:upperCorner>
      </gml:Envelope>`;case"circle":if(!e.center||!e.radius)throw console.error("圆形缺少中心点或半径:",e),new Error("圆形缺少中心点或半径");const S=e.center.x||e.center[0],X=e.center.y||e.center[1];return`<gml:Point srsName="${l}">
        <gml:pos>${S} ${X}</gml:pos>
        <gml:radius>${e.radius}</gml:radius>
      </gml:Point>`;default:throw console.error("不支持的几何对象类型:",e.type),new Error(`不支持的几何对象类型: ${e.type}`)}},ve=(g,e,a,s=!1)=>{const l=g.includes(":")?g:`${E}:${g}`;if(console.log("完整的图层名称:",l),e&&e.length>0)console.log("要素对象结构:",e[0]),console.log("要素对象属性:",Object.keys(e[0]));else return console.error("没有要更新的要素"),Promise.reject(new Error("没有要更新的要素"));try{const p=Object.fromEntries(Object.entries(a).filter(([t,o])=>o!=null&&(o!==""||t==="OBJECTID"||t==="SID"))),i=`<?xml version="1.0" encoding="UTF-8"?>
<wfs:Transaction service="WFS" version="1.1.0"
  xmlns:wfs="http://www.opengis.net/wfs"
  xmlns:ogc="http://www.opengis.net/ogc"
  xmlns:gml="http://www.opengis.net/gml"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.opengis.net/wfs http://schemas.opengis.net/wfs/1.1.0/wfs.xsd">
  ${e.map(t=>{let o=t.id;typeof o=="string"&&o.includes(".")&&(o=Number(o.split(".")[1]));const n=Object.entries(p).map(([w,m])=>{const $=w.toUpperCase();console.log("属性名称:",w,"->",$);let f;return m==null?f="":typeof m=="string"?f=m.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;"):f=m,console.log("属性值:",w,"=",f),`<wfs:Property>
          <wfs:Name>${$}</wfs:Name>
          <wfs:Value>${f}</wfs:Value>
        </wfs:Property>`});if(s&&t.geometry){console.log("更新几何信息:",t.geometry);const w=C(t);n.push(`<wfs:Property>
          <wfs:Name>geom</wfs:Name>
          <wfs:Value>${w}</wfs:Value>
        </wfs:Property>`)}const c=n.join("");return`<wfs:Update typeName="${l}">
        ${c}
        <ogc:Filter>
          <ogc:FeatureId fid="${o}"/>
        </ogc:Filter>
      </wfs:Update>`}).join("")}
</wfs:Transaction>`;return console.log("WFS-T更新事务XML:",i),P({url:"/geoserver/wfs",method:"post",headers:{"Content-Type":"application/xml"},data:i})}catch(p){return console.error("构建WFS-T更新请求时出错:",p),Promise.reject(p)}},Be=(g,e)=>{const a=g.includes(":")?g:`${E}:${g}`;console.log("完整的图层名称:",a),console.log("要添加的要素:",e);try{if(!e.geometry)throw new Error("要素缺少几何对象");const l=e.attributes||{};console.log("要素属性:",l);const p=Object.entries(l).filter(([t])=>t!=="OBJECTID"&&t!=="objectid").map(([t,o])=>{const n=t.toUpperCase();let c;return o==null?c="":typeof o=="string"?c=o.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;"):c=o,`<${n}>${c}</${n}>`}).join(""),u=C(e),i=`<?xml version="1.0" encoding="UTF-8"?>
<wfs:Transaction service="WFS" version="1.1.0"
  xmlns:wfs="http://www.opengis.net/wfs"
  xmlns:ogc="http://www.opengis.net/ogc"
  xmlns:gml="http://www.opengis.net/gml"
  xmlns:${E}="${E}"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.opengis.net/wfs http://schemas.opengis.net/wfs/1.1.0/wfs.xsd">
  <wfs:Insert>
    <${a}>
      <geom>${u}</geom>
      ${p}
    </${a}>
  </wfs:Insert>
</wfs:Transaction>`;return console.log("WFS-T事务XML:",i),P({url:"/geoserver/wfs",method:"post",headers:{"Content-Type":"application/xml"},data:i})}catch(s){throw console.error("构建WFS-T添加请求时出错:",s),s}},Re=(g,e)=>{const a=g.includes(":")?g:`${E}:${g}`;console.log("完整的图层名称:",a),console.log("要删除的要素ID:",e);try{const s=e.map(p=>`<ogc:FeatureId fid="${p.includes(".")?p.split(".")[1]:p}"/>`).join(""),l=`<?xml version="1.0" encoding="UTF-8"?>
<wfs:Transaction service="WFS" version="1.1.0"
  xmlns:wfs="http://www.opengis.net/wfs"
  xmlns:ogc="http://www.opengis.net/ogc"
  xmlns:gml="http://www.opengis.net/gml"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.opengis.net/wfs http://schemas.opengis.net/wfs/1.1.0/wfs.xsd">
  <wfs:Delete typeName="${a}">
    <ogc:Filter>
      ${s}
    </ogc:Filter>
  </wfs:Delete>
</wfs:Transaction>`;return console.log("WFS-T删除事务XML:",l),P({url:"/geoserver/wfs",method:"post",headers:{"Content-Type":"application/xml"},data:l})}catch(s){throw console.error("构建WFS-T删除请求时出错:",s),s}},We=(g,e)=>{const a=g.includes(":")?g:`${E}:${g}`;console.log("完整的图层名称:",a),console.log("要删除的要素数量:",e.length);try{const s=e.map(u=>{const i=u.geometry;if(!i)return console.error("要素缺少几何对象:",u),"";if(i.type==="point"){const t=i.x,o=i.y,n=1e-4;return`
          <ogc:BBOX>
            <ogc:PropertyName>geom</ogc:PropertyName>
            <gml:Envelope srsName="EPSG:3857">
              <gml:lowerCorner>${t-n} ${o-n}</gml:lowerCorner>
              <gml:upperCorner>${t+n} ${o+n}</gml:upperCorner>
            </gml:Envelope>
          </ogc:BBOX>
        `}else if(i.type==="polyline"){const t=i.paths||[];if(t.length===0||t[0].length===0)return console.error("线几何没有路径:",i),"";let o=1/0,n=1/0,c=-1/0,w=-1/0;t.forEach($=>{$.forEach(f=>{const d=f[0],x=f[1];o=Math.min(o,d),n=Math.min(n,x),c=Math.max(c,d),w=Math.max(w,x)})});const m=1e-4;return`
          <ogc:BBOX>
            <ogc:PropertyName>geom</ogc:PropertyName>
            <gml:Envelope srsName="EPSG:3857">
              <gml:lowerCorner>${o-m} ${n-m}</gml:lowerCorner>
              <gml:upperCorner>${c+m} ${w+m}</gml:upperCorner>
            </gml:Envelope>
          </ogc:BBOX>
        `}else if(i.type==="polygon"){const t=i.rings||[];if(t.length===0||t[0].length===0)return console.error("面几何没有环:",i),"";let o=1/0,n=1/0,c=-1/0,w=-1/0;t.forEach($=>{$.forEach(f=>{const d=f[0],x=f[1];o=Math.min(o,d),n=Math.min(n,x),c=Math.max(c,d),w=Math.max(w,x)})});const m=1e-4;return`
          <ogc:BBOX>
            <ogc:PropertyName>geom</ogc:PropertyName>
            <gml:Envelope srsName="EPSG:3857">
              <gml:lowerCorner>${o-m} ${n-m}</gml:lowerCorner>
              <gml:upperCorner>${c+m} ${w+m}</gml:upperCorner>
            </gml:Envelope>
          </ogc:BBOX>
        `}else return console.error("不支持的几何类型:",i.type),""}).filter(u=>u!=="");if(s.length===0)throw new Error("没有有效的几何过滤器");const l=s.length>1?`<ogc:Or>${s.join("")}</ogc:Or>`:s[0],p=`<?xml version="1.0" encoding="UTF-8"?>
<wfs:Transaction service="WFS" version="1.1.0"
  xmlns:wfs="http://www.opengis.net/wfs"
  xmlns:ogc="http://www.opengis.net/ogc"
  xmlns:gml="http://www.opengis.net/gml"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.opengis.net/wfs http://schemas.opengis.net/wfs/1.1.0/wfs.xsd">
  <wfs:Delete typeName="${a}">
    <ogc:Filter>
      ${l}
    </ogc:Filter>
  </wfs:Delete>
</wfs:Transaction>`;return console.log("WFS-T基于几何删除事务XML:",p),P({url:"/geoserver/wfs",method:"post",headers:{"Content-Type":"application/xml"},data:p})}catch(s){throw console.error("构建WFS-T基于几何删除请求时出错:",s),s}};export{Be as addFeature,Re as deleteFeature,We as deleteFeatureByGeometry,ve as updateFeatureAttributes};
