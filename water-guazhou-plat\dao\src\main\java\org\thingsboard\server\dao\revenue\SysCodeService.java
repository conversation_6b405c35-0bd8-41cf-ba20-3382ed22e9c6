package org.thingsboard.server.dao.revenue;

import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.revenue.SysCode;
import org.thingsboard.server.dao.model.sql.revenue.SysCodeDetail;

import java.util.List;
import java.util.Map;

public interface SysCodeService {
    /**
     * 查询数据字典项详情
     *
     * @param mainCode 数据字典keys,多个用逗号分隔
     * @param tenantId 租户ID
     * @return 数据
     */
    Object detailList(String mainCodes, TenantId tenantId);

    /**
     * 批量导入用户需要的excel下拉框数据
     *
     * @param tenantId
     * @return
     */
    Map<String, List<String>> getCustParams(String tenantId);

    /**
     * 获取字典对应的code对应名字的id
     *
     * @param tenantId
     * @return
     */
    Map<String, Map<String, String>> getCustNameMap(String tenantId);

    /**
     * 查询全部的数据字典项
     *
     * @param tenantId 租户ID
     * @return 数据
     */
    List<SysCode> findAll(TenantId tenantId);

    /**
     * 保存字典子项
     *
     * @param option 子项
     */
    void saveOption(SysCodeDetail option);

    /**
     * 查询指定数据字典项的子项列表
     *
     * @param page     当前页码
     * @param size     每页记录数
     * @param mainCode 数据字典编码
     * @param name     按名称模糊查询子项
     * @param tenantId 租户ID
     * @return 数据
     */
    PageData<SysCodeDetail> findOptions(int page, int size, String mainCode, String name, TenantId tenantId);

    /**
     * 删除子项
     *
     * @param optionId 子项ID
     */
    void deleteOption(String optionId);

    List<SysCodeDetail> detailList(String code);
}
