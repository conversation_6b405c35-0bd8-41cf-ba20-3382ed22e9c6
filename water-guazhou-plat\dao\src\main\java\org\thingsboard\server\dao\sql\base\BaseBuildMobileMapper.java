package org.thingsboard.server.dao.sql.base;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.base.BaseBuildMobile;
import org.thingsboard.server.dao.util.imodel.query.base.BaseBuildMobilePageRequest;

import java.util.List;

/**
 * 平台管理-Mobile搭建Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@Mapper
public interface BaseBuildMobileMapper {
    /**
     * 查询平台管理-Mobile搭建
     *
     * @param id 平台管理-Mobile搭建主键
     * @return 平台管理-Mobile搭建
     */
    public BaseBuildMobile selectBaseBuildMobileById(String id);

    /**
     * 查询平台管理-Mobile搭建列表
     *
     * @param baseBuildMobile 平台管理-Mobile搭建
     * @return 平台管理-Mobile搭建集合
     */
    public IPage<BaseBuildMobile> selectBaseBuildMobileList(BaseBuildMobilePageRequest baseBuildMobile);

    /**
     * 新增平台管理-Mobile搭建
     *
     * @param baseBuildMobile 平台管理-Mobile搭建
     * @return 结果
     */
    public int insertBaseBuildMobile(BaseBuildMobile baseBuildMobile);

    /**
     * 修改平台管理-Mobile搭建
     *
     * @param baseBuildMobile 平台管理-Mobile搭建
     * @return 结果
     */
    public int updateBaseBuildMobile(BaseBuildMobile baseBuildMobile);

    /**
     * 删除平台管理-Mobile搭建
     *
     * @param id 平台管理-Mobile搭建主键
     * @return 结果
     */
    public int deleteBaseBuildMobileById(String id);

    /**
     * 批量删除平台管理-Mobile搭建
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBaseBuildMobileByIds(@Param("array") List<String> ids);
}
