package org.thingsboard.server.dao.model.sql;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.thingsboard.server.dao.model.ModelConstants;

import javax.persistence.*;

/**
 * 审计记录
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2021-01-19
 */
@Data
@Entity
@Table(name = ModelConstants.AUDIT_RECORD_TABLE)
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
public class AuditRecordEntity {


    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    /**
     * 审计时间
     */
    @Column(name = ModelConstants.AUDIT_RECORD_TIME)
    private Long time;

    /**
     * 审计人名称
     */
    @Column(name = ModelConstants.AUDIT_RECORD_USERNAME)
    private String username;

    /**
     * 审计对象
     */
    @Column(name = ModelConstants.AUDIT_RECORD_TARGET)
    private String target;

    /**
     * 日志条数
     */
    @Column(name = ModelConstants.AUDIT_RECORD_COUNT)
    private Integer count;

    /**
     * 日志分析结果
     */
    @Column(name = ModelConstants.AUDIT_RECORD_RESULT)
    private String result;

    /**
     * 安全事件分析
     */
    @Column(name = ModelConstants.AUDIT_RECORD_EVENT_ANALYSIS)
    private String eventAnalysis;

    /**
     * 审计记录创建人
     */
    @Column(name = ModelConstants.AUDIT_RECORD_CREATED)
    private String created;


    /**
     * 数据更新时间
     */
    @Column(name = ModelConstants.CREATED_TIME)
    private Long createdTime;

}
