@mixin weather() {
  .esri-weather {
    position: relative;
    padding: var(--esri-widget-padding);
    width: fit-content;

    &__content {
      display: flex;
      flex-direction: column;
      gap: $cap-spacing;
      width: min-content;

      // When there is an error we take up horizontal space, but not any vertical
      // space. This way the widget maintains a constant width without us having
      // to specify a fixed value.
      &--has-error {
        pointer-events: none;
        visibility: hidden;
        overflow: hidden;
        height: 0;
      }
    }

    &__selector {
      display: flex;
      align-items: center;
      flex-direction: row;
      gap: $side-spacing--half;
    }

    &__options {
      display: flex;
      flex-direction: column;
      gap: $cap-spacing;
    }

    &__warning {
      display: flex;
      flex-direction: row;
      align-items: center;
      gap: $side-spacing--half;
      font-size: $font-size--small;

      calcite-icon {
        color: var(--calcite-ui-warning);
      }
    }

    // Align calcite slider to the buttons, header, etc
    &__labeled-slider calcite-slider {
      margin: 0 -7px;
      margin-bottom: -14px;
    }
  }
}

@if $include_Weather==true {
  @include weather();
}
