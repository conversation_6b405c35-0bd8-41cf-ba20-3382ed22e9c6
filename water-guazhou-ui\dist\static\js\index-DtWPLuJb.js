import{_ as F}from"./index-C9hz-UZb.js";import{d as R,M as j,bF as p,r as _,am as E,c as b,a8 as G,bB as N,s as L,j as T,a6 as M,bu as H,ay as J,g as U,n as W,q as m,i as s,F as h,cs as S,bo as w,bR as D,p as O,dF as $,dA as K,aq as Q,b7 as X,aj as Y,C as Z}from"./index-r0dFAfgr.js";import{_ as ee}from"./CardSearch-CB_HNR-Q.js";import{l as ae}from"./echart-C8HucFFm.js";import{g as te}from"./queryStatistics-CQ9DBM08.js";import{b as re}from"./zhandian-YaGuQZe6.js";import{u as ne}from"./useStation-DJgnSZIA.js";import{f as oe}from"./formartColumn-D5r7JJ2G.js";import"./Search-NSrhrIa_.js";const se={class:"wrapper"},le=R({__name:"index",setup(ie){const{$messageWarning:I}=j(),{getStationTree:V}=ne();p().date();const t=_({type:"date",chartOption:null,chartName:"",activeName:"echarts",data:null,stationTree:[]});E(()=>t.activeName,()=>{t.activeName==="echarts"&&q()});const y=b(),g=b(),v=b(),C=b();let f=_([]);const A=_({defaultParams:{queryType:"day",day:[p().format(),p().format()],month:[p().subtract(1,"month"),p().format()]},filters:[{type:"select-tree",label:"监测点:",field:"attributeId",clearable:!1,lazy:!0,options:G(()=>t.stationTree),lazyLoad:(e,a)=>{var o,c;if(e.level===0)return a([]);if(((o=e.data.children)==null?void 0:o.length)>0)return a(e.data.children);if(e.isLeaf)return a([]);if((c=e.data)!=null&&c.isLeaf)return a([]);re({stationId:e.data.id}).then(d=>{var r;const u=(r=d.data)==null?void 0:r.map(n=>({label:n.type,value:"",id:"",children:n.attrList.map(l=>({label:l.name,value:l.id,id:l.id,isLeaf:!0}))}));return a(u)})},nodeClick:(e,a)=>{a.isLeaf&&(t.chartName=e.label,N(()=>{k()}))}},{type:"select",label:"比较类型:",field:"queryType",clearable:!1,width:"200px",options:[{label:"日分时(时间段)",value:"day"},{label:"月分日(时间段)",value:"month"}],itemContainerStyle:{width:"240px"}},{type:"daterange",label:"日期",field:"day",clearable:!1,handleHidden:(e,a,o)=>{o.hidden=e.queryType==="month"}},{type:"monthrange",label:"日期",field:"month",clearable:!1,handleHidden:(e,a,o)=>{o.hidden=e.queryType==="day"}}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",click:()=>{var a;(((a=y.value)==null?void 0:a.queryParams)||{}).attributeId?k():I("选择监测点")},icon:"iconfont icon-chaxun"},{type:"default",perm:!0,text:"重置",svgIcon:L(X),click:()=>{var e;(e=y.value)==null||e.resetForm()}},{perm:!0,type:"warning",text:"导出",hide:()=>t.activeName!=="list",svgIcon:L(Y),click:()=>z()}]}]}),i=_({loading:!1,dataList:[],columns:[],operations:[],pagination:{layout:"total, prev, pager, next, jumper",refreshData:({page:e,size:a})=>{i.pagination.page=e,i.pagination.limit=a,i.dataList=f.slice((e-1)*a,e*a)}}}),k=()=>{var d;i.loading=!0;const e=((d=y.value)==null?void 0:d.queryParams)||{},[a,o]=e[e.queryType]||[],c={attributeId:e.attributeId,queryType:e.queryType,start:a?p(a).startOf(e.queryType).valueOf():"",end:o?p(o).endOf(e.queryType).valueOf():""};te(c).then(u=>{var l;const r=(l=u.data)==null?void 0:l.data;t.data=r;const n=oe(r==null?void 0:r.tableInfo);f=r==null?void 0:r.tableDataList,i.columns=n,i.dataList=f==null?void 0:f.slice(0,20),i.pagination.total=r==null?void 0:r.tableDataList.length,q(),i.loading=!1})},P=()=>{var e;(e=g.value)==null||e.resize()},q=()=>{var d,u,r;const e=ae();e.series=[];const a={name:"",smooth:!0,data:[],type:"line",markPoint:{data:[{type:"max",name:"最大值",label:{fontSize:12,color:T().isDark?"#ffffff":"#000000"}},{type:"min",name:"最小值",label:{color:T().isDark?"#ffffff":"#000000"}}]},markLine:{data:[{type:"average",name:"平均值"}]}};e.xAxis.data=(d=t.data)==null?void 0:d.tableDataList.map(n=>n.ts),(u=t.data)==null||u.tableInfo.map(n=>{var l;if(n.columnValue!=="ts"){const x=JSON.parse(JSON.stringify(a));x.name=n.columnName,e.yAxis[0].name=t.chartName+(n.unit?"("+n.unit+")":""),x.data=(l=t.data)==null?void 0:l.tableDataList.map(B=>B[n.columnValue]),e.series.push(x)}}),(r=g.value)==null||r.clear();const c=M({callOnAdd:!0});N(()=>{v.value&&c.listenTo(v.value,()=>{t.chartOption=e,P()})})},z=()=>{var e;(e=C.value)==null||e.exportTable()};return H(async()=>{const e=await V("水质监测站");t.stationTree=e,console.log(" state.stationTree ",t.stationTree)}),(e,a)=>{const o=ee,c=$,d=K,u=J("VChart"),r=Q,n=F;return U(),W("div",se,[m(o,{ref_key:"cardSearch",ref:y,config:s(A)},null,8,["config"]),m(n,{class:"card",title:s(t).chartName+(s(t).activeName==="list"?"周期对比列表":"周期对比曲线")},{right:h(()=>[m(d,{modelValue:s(t).activeName,"onUpdate:modelValue":a[0]||(a[0]=l=>s(t).activeName=l)},{default:h(()=>[m(c,{label:"echarts"},{default:h(()=>[m(s(S),{style:{"margin-right":"1px","font-size":"16px"},icon:"clarity:line-chart-line"})]),_:1}),m(c,{label:"list"},{default:h(()=>[m(s(S),{style:{"margin-right":"1px","font-size":"16px"},icon:"material-symbols:table"})]),_:1})]),_:1},8,["modelValue"])]),default:h(()=>[w(O("div",{ref_key:"echartsDiv",ref:v,class:"chart-box"},[m(u,{ref_key:"refChart",ref:g,theme:s(T)().isDark?"dark":"light",option:s(t).chartOption},null,8,["theme","option"])],512),[[D,s(t).activeName==="echarts"]]),w(O("div",null,[m(r,{ref_key:"refCardTable",ref:C,class:"card-table",config:s(i)},null,8,["config"])],512),[[D,s(t).activeName==="list"]])]),_:1},8,["title"])])}}}),be=Z(le,[["__scopeId","data-v-050d535d"]]);export{be as default};
