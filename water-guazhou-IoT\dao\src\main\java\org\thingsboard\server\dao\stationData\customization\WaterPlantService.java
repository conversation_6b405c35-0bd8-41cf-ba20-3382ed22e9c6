package org.thingsboard.server.dao.stationData.customization;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.Utils.DateUtils;
import org.thingsboard.server.common.data.device.DeviceFullData;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.client.MedicineManageFeignClient;
import org.thingsboard.server.dao.client.StationFeignClient;
import org.thingsboard.server.dao.device.DeviceService;
import org.thingsboard.server.dao.model.DTO.StationAttrDTO;
import org.thingsboard.server.dao.model.sql.MedicineManage;
import org.thingsboard.server.dao.model.sql.StationAttrEntity;
import org.thingsboard.server.dao.model.sql.StationEntity;
import org.thingsboard.server.dao.obtain.BaseObtainDataService;
import org.thingsboard.server.dao.stationData.StationDataService;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 站点类型：水厂
 * 定制化数据查询接口
 */
@Slf4j
@Service
public class WaterPlantService {

    @Autowired
    private StationDataService stationDataService;

    @Autowired
    private StationFeignClient stationFeignClient;

    @Autowired
    private DeviceService deviceService;

    @Autowired
    private BaseObtainDataService obtainDataService;

    @Autowired
    private MedicineManageFeignClient medicineManageFeignClient;


    /**
     * 查询站点的供水量数据
     *
     * @param stationId 站点ID
     */
    public Object waterSupply(String stationId, TenantId tenantId) throws ThingsboardException {
        // 查询站点数据以及站点的动态属性列表
        List<StationAttrDTO> stationAttrDTOList = stationFeignClient.getAllAttrList(stationId);

        StationAttrDTO stationAttr = null;
        for (StationAttrDTO stationAttrDTO : stationAttrDTOList) {
            if (stationAttrDTO.getType().contains(DataConstants.DeviceAttrGroupType.WATER_OUTLET.getValue())) {
                stationAttr = stationAttrDTO;
                break;
            }
        }

        if (stationAttr == null) {
            log.error("水厂未设置供水相关的动态属性分组");
            return null;
        }

        List<DeviceFullData> stationDataDetail = stationDataService.getStationDataDetail(stationId, stationAttr.getType(), true, tenantId);
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        // 时间点
        Calendar instance = Calendar.getInstance();
        // 当前时间
        Date now = instance.getTime();
        instance.set(Calendar.HOUR_OF_DAY, 0);
        instance.set(Calendar.MINUTE, 0);
        instance.set(Calendar.SECOND, 0);
        // 今天的0点
        Date todayStart = instance.getTime();
        // 昨天0点
        Date yesterdayStart = new Date(todayStart.getTime() - (24 * 60 * 60 * 1000));

        JSONObject result = new JSONObject();

        // 查询今日供水量曲线
        for (DeviceFullData deviceFullData : stationDataDetail) {
            if ("Instantaneous_flow".equals(deviceFullData.getProperty())) {// 瞬时流量
                String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(deviceFullData.getDeviceId()));
                List<String> attributes = Collections.singletonList(deviceId + "." +deviceFullData.getProperty());
                LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> data = obtainDataService.getDeviceData(attributes, todayStart.getTime(), now.getTime(), DateUtils.HOUR, null, tenantId);

                JSONObject todayWaterSupply = new JSONObject();
                todayWaterSupply.put("data", data);
                List<JSONObject> todayWaterSupplyKeys = new ArrayList<>();
                JSONObject key = new JSONObject();
                key.put("name", deviceFullData.getPropertyName());
                key.put("key", deviceId + "." +deviceFullData.getProperty());
                todayWaterSupplyKeys.add(key);

                todayWaterSupply.put("keys", todayWaterSupplyKeys);
                result.put("todayWaterSupply", todayWaterSupply);
                break;
            }
        }

        // 查询今日、昨日累计流量
        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> totalFlowData = null;
        for (DeviceFullData deviceFullData : stationDataDetail) {
            if ("total_flow".equals(deviceFullData.getProperty())) {// 累计流量
                String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(deviceFullData.getDeviceId()));
                List<String> attributes = Collections.singletonList(deviceId + "." +deviceFullData.getProperty());
                totalFlowData = obtainDataService.getDeviceData(attributes, yesterdayStart.getTime(), now.getTime(), DateUtils.DAY, null, tenantId);
                break;
            }
        }

        // 查询今日、昨日用电量
        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> energyInData = null;
        for (DeviceFullData deviceFullData : stationDataDetail) {
            if ("ENERGY_IN".equals(deviceFullData.getProperty())) {// 有功电能
                String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(deviceFullData.getDeviceId()));
                List<String> attributes = Collections.singletonList(deviceId + "." +deviceFullData.getProperty());
                energyInData = obtainDataService.getDeviceData(attributes, yesterdayStart.getTime(), now.getTime(), DateUtils.DAY, null, tenantId);
                break;
            }
        }

        // 计算吨水电量
        BigDecimal yesterdayTotalFlow = null;
        BigDecimal yesterdayEnergyIn = null;
        BigDecimal todayTotalFlow = null;
        BigDecimal todayEnergyIn = null;
        if (totalFlowData != null && energyInData != null) {
            LinkedHashMap<String, BigDecimal> yesterdayTotalFlowMap = totalFlowData.get(dateFormat.format(yesterdayStart));
            if (yesterdayTotalFlowMap != null && !yesterdayTotalFlowMap.isEmpty()) {
                yesterdayTotalFlow = new ArrayList<>(yesterdayTotalFlowMap.values()).get(0);
            }
            LinkedHashMap<String, BigDecimal> todayTotalFlowMap = totalFlowData.get(dateFormat.format(todayStart));
            if (todayTotalFlowMap != null && !todayTotalFlowMap.isEmpty()) {
                todayTotalFlow = new ArrayList<>(todayTotalFlowMap.values()).get(0);
            }

            LinkedHashMap<String, BigDecimal> yesterdayEnergyInMap = energyInData.get(dateFormat.format(yesterdayStart));
            if (yesterdayEnergyInMap != null && !yesterdayEnergyInMap.isEmpty()) {
                yesterdayEnergyIn = new ArrayList<>(yesterdayEnergyInMap.values()).get(0);
            }
            LinkedHashMap<String, BigDecimal> todayEnergyInMap = energyInData.get(dateFormat.format(todayStart));
            if (todayEnergyInMap != null && !todayEnergyInMap.isEmpty()) {
                todayEnergyIn = new ArrayList<>(todayEnergyInMap.values()).get(0);
            }
        }

        result.put("yesterdayTotalFlow", yesterdayTotalFlow);
        result.put("yesterdayEnergyIn", yesterdayEnergyIn);
        result.put("todayTotalFlow", todayTotalFlow);
        result.put("todayEnergyIn", todayEnergyIn);

        if (todayEnergyIn != null && todayTotalFlow != null) {
            result.put("todayConsumption", todayEnergyIn.divide(todayTotalFlow, 3, BigDecimal.ROUND_DOWN));
        }
        if (yesterdayEnergyIn != null && yesterdayTotalFlow != null) {
            result.put("yesterdayConsumption", yesterdayEnergyIn.divide(yesterdayTotalFlow, 3, BigDecimal.ROUND_DOWN));
        }

        // 出厂流量和压力曲线
        List<String> attributes = new ArrayList<>();
        List<JSONObject> waterSupplyMonitorKeys = new ArrayList<>();
        for (DeviceFullData deviceFullData : stationDataDetail) {
            if ("Instantaneous_flow".equals(deviceFullData.getProperty())) {// 瞬时流量
                String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(deviceFullData.getDeviceId()));
                attributes.add(deviceId + "." +deviceFullData.getProperty());

                JSONObject key = new JSONObject();
                key.put("name", deviceFullData.getPropertyName());
                key.put("key", deviceId + "." + deviceFullData.getProperty());
                waterSupplyMonitorKeys.add(key);

            }
            if ("pressure".equals(deviceFullData.getProperty())) {// 压力
                String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(deviceFullData.getDeviceId()));
                attributes.add(deviceId + "." +deviceFullData.getProperty());

                JSONObject key = new JSONObject();
                key.put("name", deviceFullData.getPropertyName());
                key.put("key", deviceId + "." + deviceFullData.getProperty());
                waterSupplyMonitorKeys.add(key);
            }
        }
        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> data = obtainDataService.getDeviceData(attributes, todayStart.getTime(), now.getTime(), DateUtils.HOUR, null, tenantId);

        JSONObject waterSupplyMonitor = new JSONObject();
        waterSupplyMonitor.put("data", data);
        waterSupplyMonitor.put("keys", waterSupplyMonitorKeys);
        result.put("waterSupplyMonitor", waterSupplyMonitor);

        // 液位
        stationAttr = null;
        for (StationAttrDTO stationAttrDTO : stationAttrDTOList) {
            if (stationAttrDTO.getType().contains("清水池")) {
                stationAttr = stationAttrDTO;
                break;
            }
        }

        if (stationAttr == null) {
            log.error("水厂未设置清水池相关的动态属性分组");
            return null;
        }

        stationDataDetail = stationDataService.getStationDataDetail(stationId, stationAttr.getType(), true, tenantId);
        List<JSONObject> waterLevelList = new ArrayList<>();

        for (DeviceFullData deviceFullData : stationDataDetail) {
            List<String> waterLevelAttrs = new ArrayList<>();
            String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(deviceFullData.getDeviceId()));
            waterLevelAttrs.add(deviceId + "." +deviceFullData.getProperty());

            LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> waterLevelData =
                    obtainDataService.getDeviceData(waterLevelAttrs, todayStart.getTime(), now.getTime(), DateUtils.HOUR, null, tenantId);

            JSONObject waterLevelObj = new JSONObject();
            waterLevelObj.put("name", deviceFullData.getPropertyName());
            waterLevelObj.put("dataList", waterLevelData);

            waterLevelList.add(waterLevelObj);
        }

        result.put("waterLevel", waterLevelList);

        return result;
    }

    /**
     * 出水压力台账
     */
    public Object pressureByStation(List<String> stationIdList, Long start, Long end, String type, TenantId tenantId) throws ThingsboardException {
        // 查询站点
        List<StationEntity> stationList = stationFeignClient.findByStationIdList("水厂", stationIdList);

        // 查询站点出水动态属性分组
        for (StationEntity station : stationList) {
            // 查询站点数据以及站点的动态属性列表
            List<StationAttrDTO> stationAttrDTOList = stationFeignClient.getAllAttrList(station.getId());

            StationAttrDTO stationAttr = null;
            for (StationAttrDTO stationAttrDTO : stationAttrDTOList) {
                if (stationAttrDTO.getType().contains(DataConstants.DeviceAttrGroupType.WATER_OUTLET.getValue())) {
                    stationAttr = stationAttrDTO;
                    break;
                }
            }
            station.setStationAttrInfo(Collections.singletonList(stationAttr));
        }

        // 查询压力数据
        Map<String, StationEntity> keyMap = new HashMap<>();
        for (StationEntity station : stationList) {
            List<StationAttrDTO> stationAttrInfo = station.getStationAttrInfo();

            if (stationAttrInfo == null || stationAttrInfo.isEmpty()) {
                continue;
            }

            StationAttrDTO stationAttr = stationAttrInfo.get(0);
            List<StationAttrEntity> attrList = stationAttr.getAttrList();
            for (StationAttrEntity attrEntity : attrList) {
                if ("pressure".equals(attrEntity.getAttr())) {
                    keyMap.put(UUIDConverter.fromTimeUUID(UUID.fromString(attrEntity.getDeviceId())) + "." + attrEntity.getAttr(), station);
                    break;
                }
            }
        }

        List<String> attrs = new ArrayList<>(keyMap.keySet());

        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> data = obtainDataService.getDeviceData(attrs, start, end, type, null, tenantId);

        List<JSONObject> keys = new ArrayList<>();
        for (String attr : attrs) {
            StationEntity station = keyMap.get(attr);
            if (station != null) {
                JSONObject key = new JSONObject();
                key.put("name", station.getName());
                key.put("key", attr);
                keys.add(key);
            }
        }

        JSONObject result = new JSONObject();
        result.put("data", data);
        result.put("keys", keys);

        return result;
    }

    /**
     * 查询药耗分析
     */
    public Object drugConsumptionAnalyze(String stationId, TenantId tenantId) throws Exception {
        JSONObject result = new JSONObject();
        // 查询站点信息
        StationEntity station = stationFeignClient.get(stationId);
        if (station == null) {
            return null;
        }
        // 查询站点的出水信息
        List<StationAttrDTO> stationAttrDTOList = stationFeignClient.getAllAttrList(stationId);

        // 按年查询供水量
        StationAttrDTO stationAttr = null;
        for (StationAttrDTO stationAttrDTO : stationAttrDTOList) {
            if (stationAttrDTO.getType().contains(DataConstants.DeviceAttrGroupType.WATER_OUTLET.getValue())) {
                stationAttr = stationAttrDTO;
                break;
            }
        }

        if (stationAttr == null) {
            log.error("水厂未设置供水相关的动态属性分组");
            return null;
        }

        List<DeviceFullData> stationDataDetail = stationDataService.getStationDataDetail(stationId, stationAttr.getType(), true, tenantId);

        SimpleDateFormat monthDateFormat = new SimpleDateFormat("yyyy-MM");
        Calendar instance = Calendar.getInstance();
        instance.set(Calendar.MONTH, 0);
        instance.set(Calendar.DAY_OF_MONTH, 1);
        instance.set(Calendar.HOUR_OF_DAY, 0);
        instance.set(Calendar.MINUTE, 0);
        instance.set(Calendar.SECOND, 0);

        Date yearStart = instance.getTime();
        Date now = new Date();

        instance.set(Calendar.MONTH, 11);
        instance.set(Calendar.DAY_OF_MONTH, 30);
        instance.set(Calendar.HOUR_OF_DAY, 23);
        instance.set(Calendar.MINUTE, 59);
        instance.set(Calendar.SECOND, 59);
        Date yearEnd = instance.getTime();

        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> totalFlowData = null;
        for (DeviceFullData deviceFullData : stationDataDetail) {
            if ("total_flow".equals(deviceFullData.getProperty())) {// 累计流量
                String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(deviceFullData.getDeviceId()));
                List<String> attributes = Collections.singletonList(deviceId + "." +deviceFullData.getProperty());
                totalFlowData = obtainDataService.getDeviceData(attributes, yearStart.getTime(), yearEnd.getTime(), DateUtils.MONTH, null, tenantId);
                break;
            }
        }


        // 查询药耗列表
        List<MedicineManage> medicineManageList = medicineManageFeignClient.all(stationId);

        List<String> medicineMangeTypeList = medicineManageList.stream().map(MedicineManage::getMedicineType).distinct().collect(Collectors.toList());

        Date monthStart = monthDateFormat.parse(monthDateFormat.format(new Date()));

        List<MedicineManage> monthList = medicineManageList.stream()
                .filter(medicineManage -> medicineManage.getTime().getTime() > monthStart.getTime() && medicineManage.getTime().getTime() < now.getTime())
                .collect(Collectors.toList());

        // 统计月累计综合药耗
        JSONObject monthTotalData = new JSONObject();
        if (monthList.size() > 0) {
            BigDecimal total = new BigDecimal("0");
            for (MedicineManage medicineManage : monthList) {
                BigDecimal value = monthTotalData.getBigDecimal(medicineManage.getMedicineType() + "耗量");
                if (value == null) {
                    value = new BigDecimal("0");
                }
                value = value.add(medicineManage.getNum());
                monthTotalData.put(medicineManage.getMedicineType() + "耗量", value);

                total = total.add(medicineManage.getNum());
            }
            monthTotalData.put("总药耗", total);

            String monthKey = monthDateFormat.format(now);
            if (totalFlowData != null) {
                LinkedHashMap<String, BigDecimal> monthData = totalFlowData.get(monthKey);
                ArrayList<BigDecimal> monthDataList = new ArrayList<>(monthData.values());
                if (monthDataList.size() > 0) {
                    BigDecimal v = monthDataList.get(0);

                    BigDecimal monthConsumption = total.divide(v.divide(new BigDecimal("1000"), 2, BigDecimal.ROUND_DOWN), 2, BigDecimal.ROUND_DOWN);
                    monthTotalData.put("吨水药耗", monthConsumption);
                }
            }
        }

        result.put("monthTotalData", monthTotalData);

        // 统计年制水药耗统计
        JSONObject yearTotalData = new JSONObject();
        if (medicineManageList.size() > 0) {
            BigDecimal total = new BigDecimal("0");
            for (MedicineManage medicineManage : monthList) {
                BigDecimal value = yearTotalData.getBigDecimal(medicineManage.getMedicineType() + "耗量");
                if (value == null) {
                    value = new BigDecimal("0");
                }
                value = value.add(medicineManage.getNum());
                yearTotalData.put(medicineManage.getMedicineType() + "耗量", value);

                total = total.add(medicineManage.getNum());
            }
            yearTotalData.put("总药耗", total);
        }

        result.put("yearTotalData", yearTotalData);

        // 药耗年统计曲线
        JSONObject yearData = new JSONObject();
        if (totalFlowData != null) {
            ArrayList<String> keyList = new ArrayList<>(totalFlowData.keySet());

            List<JSONObject> totalDataList = new ArrayList<>();

            Map<String, List<JSONObject>> monthDataMap = new LinkedHashMap<>();
            BigDecimal monthTotal = new BigDecimal("0");

            for (String key : keyList) {
                // 筛选出本月的数据
                List<MedicineManage> monthData = medicineManageList.stream()
                        .filter(medicineManage -> key.equals(monthDateFormat.format(medicineManage.getTime())))
                        .collect(Collectors.toList());
                Map<String, BigDecimal> monthTypeData = new LinkedHashMap<>();
                for (MedicineManage monthDatum : monthData) {
                    String medicineType = monthDatum.getMedicineType();
                    BigDecimal typeTotal = new BigDecimal("0");
                    if (monthTypeData.containsKey(medicineType)) {
                        typeTotal = monthTypeData.get(medicineType);
                    }
                    typeTotal = typeTotal.add(monthDatum.getNum());

                    monthTypeData.put(medicineType, typeTotal);

                    monthTotal = monthTotal.add(monthDatum.getNum());
                }

                // 统计本月数据
                JSONObject monthTotalObj = new JSONObject();
                monthTotalObj.put("date", key);
                monthTotalObj.put("value", monthTotal.toString());
                totalDataList.add(monthTotalObj);

                for (Map.Entry<String, BigDecimal> entry : monthTypeData.entrySet()) {
                    String keyType = entry.getKey();

                    List<JSONObject> list = monthDataMap.get(keyType);
                    if (list == null) {
                        list = new ArrayList<>();
                    }
                    JSONObject object = new JSONObject();
                    object.put("date", key);
                    object.put("type", keyType);
                    object.put("value", entry.getValue());
                    list.add(object);

                    monthDataMap.put(keyType, list);
                }

            }

            yearData.put("total", totalDataList);
            yearData.put("typeData", monthDataMap);

            // 计算吨水单耗曲线
            List<JSONObject> consumptionList = new ArrayList<>();
            for (String s : keyList) {
                // 筛选出本月的数据
                List<MedicineManage> monthData = medicineManageList.stream()
                        .filter(medicineManage -> s.equals(monthDateFormat.format(medicineManage.getTime())))
                        .collect(Collectors.toList());
                BigDecimal monthTotalValue = new BigDecimal("0");
                for (MedicineManage monthDatum : monthData) {
                    monthTotalValue = monthTotalValue.add(monthDatum.getNum());
                }

                LinkedHashMap<String, BigDecimal> data = totalFlowData.get(s);
                JSONObject monthConsumptionData = new JSONObject();
                monthConsumptionData.put("date", s);
                if (data != null) {
                    try {
                        BigDecimal v = new ArrayList<>(data.values()).get(0);
                        monthConsumptionData.put("value", monthTotalValue.divide(v.divide(new BigDecimal("1000"), 2, BigDecimal.ROUND_DOWN), 2, BigDecimal.ROUND_DOWN));
                    } catch (Exception ignored) {
                    }
                }

                consumptionList.add(monthConsumptionData);
            }

            yearData.put("consumptionData", consumptionList);

            // 清空
            monthDataMap = new LinkedHashMap<>();
            monthTotal = new BigDecimal("0");
        }
        result.put("yearData", yearData);

        // 周数据统计
        JSONObject weekData = new JSONObject();

        instance.setTime(now);
        instance.set(Calendar.HOUR_OF_DAY, 0);
        instance.set(Calendar.MINUTE, 0);
        instance.set(Calendar.SECOND, 0);
        Date weekStart = new Date(instance.getTimeInMillis() - (6 * 24 * 60 * 60 * 1000));
        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> weekTotalFlowData = null;
        for (DeviceFullData deviceFullData : stationDataDetail) {
            if ("total_flow".equals(deviceFullData.getProperty())) {// 累计流量
                String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(deviceFullData.getDeviceId()));
                List<String> attributes = Collections.singletonList(deviceId + "." +deviceFullData.getProperty());
                weekTotalFlowData = obtainDataService.getDeviceData(attributes, weekStart.getTime(), now.getTime(), DateUtils.DAY, null, tenantId);
                break;
            }
        }

        if (weekTotalFlowData != null) {
            List<String> weekKeyList = new ArrayList<>(weekTotalFlowData.keySet());

            SimpleDateFormat weekDateFormat = new SimpleDateFormat("yyyy-MM-dd");

            List<JSONObject> weekDataList = new ArrayList<>();
            List<JSONObject> weekTotalFlowDataList = new ArrayList<>();
            List<JSONObject> weekConsumptionDataList = new ArrayList<>();
            for (String key : weekKeyList) {
                // 筛选出本日的药耗数据
                List<MedicineManage> dayData = medicineManageList.stream()
                        .filter(medicineManage -> key.equals(weekDateFormat.format(medicineManage.getTime())))
                        .collect(Collectors.toList());
                BigDecimal dayTotal = new BigDecimal("0");
                for (MedicineManage monthDatum : dayData) {
                    dayTotal = dayTotal.add(monthDatum.getNum());
                }

                JSONObject weekDataObj = new JSONObject();
                weekDataObj.put("date", key);
                weekDataObj.put("value", dayTotal);

                weekDataList.add(weekDataObj);

                // 筛选出本日的用水量数据
                LinkedHashMap<String, BigDecimal> weekDataMap = weekTotalFlowData.get(key);
                BigDecimal dayTotalFlow = new ArrayList<>(weekDataMap.values()).get(0);
                JSONObject dayTotalFlowObj = new JSONObject();
                dayTotalFlowObj.put("date", key);
                dayTotalFlowObj.put("value", dayTotalFlow);
                weekTotalFlowDataList.add(dayTotalFlowObj);

                // 计算吨水药耗
                if (dayTotalFlow != null && dayTotalFlow.doubleValue() != 0) {
                    JSONObject dayConsumptionObj = new JSONObject();
                    dayConsumptionObj.put("date", key);
                    dayConsumptionObj.put("value", dayTotal.divide(dayTotalFlow.divide(new BigDecimal("1000"), 2, BigDecimal.ROUND_DOWN), 2, BigDecimal.ROUND_DOWN));
                    weekConsumptionDataList.add(dayConsumptionObj);
                }
            }

            weekData.put("药耗量", weekDataList);
            weekData.put("供水量", weekTotalFlowDataList);
            weekData.put("吨水药耗", weekConsumptionDataList);
        }

        result.put("weekData", weekData);

        return result;
    }

    public Object waterQualityAnalyze(String stationId, String type, Long start, Long end, TenantId tenantId) throws ThingsboardException {
        JSONObject result = new JSONObject();
        // 查询站点信息
        StationEntity station = stationFeignClient.get(stationId);
        if (station == null) {
            return null;
        }
        // 查询站点的出水信息
        List<StationAttrDTO> stationAttrDTOList = stationFeignClient.getAllAttrList(stationId);

        // 按年查询供水量
        StationAttrDTO getWaterStationAttr = null;
        StationAttrDTO supplyWaterStationAttr = null;

        for (StationAttrDTO stationAttrDTO : stationAttrDTOList) {
            if (stationAttrDTO.getType().contains(DataConstants.DeviceAttrGroupType.WATER_OUTLET.getValue())) {
                supplyWaterStationAttr = stationAttrDTO;
            }
            if (stationAttrDTO.getType().contains(DataConstants.DeviceAttrGroupType.WATER_INLET.getValue())) {
                getWaterStationAttr = stationAttrDTO;
            }
        }

        if (getWaterStationAttr == null || supplyWaterStationAttr == null) {
            log.error("水厂未设置相关的动态属性分组");
            return null;
        }

        List<DeviceFullData> getWaterStationDataDetail = stationDataService.getStationDataDetail(stationId, getWaterStationAttr.getType(), true, tenantId);
        List<DeviceFullData> supplyWaterStationDataDetail = stationDataService.getStationDataDetail(stationId, supplyWaterStationAttr.getType(), true, tenantId);

        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> getWaterTurbidity = null;
        for (DeviceFullData deviceFullData : getWaterStationDataDetail) {
            if ("turbidity".equals(deviceFullData.getProperty())) {// 累计流量
                String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(deviceFullData.getDeviceId()));
                List<String> attributes = Collections.singletonList(deviceId + "." +deviceFullData.getProperty());
                getWaterTurbidity = obtainDataService.getDeviceData(attributes, start, end, type, null, tenantId);
                break;
            }
        }

        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> supplyWaterTurbidity = null;
        for (DeviceFullData deviceFullData : supplyWaterStationDataDetail) {
            if ("turbidity".equals(deviceFullData.getProperty())) {// 累计流量
                String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(deviceFullData.getDeviceId()));
                List<String> attributes = Collections.singletonList(deviceId + "." +deviceFullData.getProperty());
                supplyWaterTurbidity = obtainDataService.getDeviceData(attributes, start, end, type, null, tenantId);
                break;
            }
        }

        JSONObject turbidity = new JSONObject();
        turbidity.put("出水浊度", getWaterTurbidity);
        turbidity.put("取水浊度", supplyWaterTurbidity);
        result.put("turbidity", turbidity);

        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> getWaterConductance = null;
        for (DeviceFullData deviceFullData : getWaterStationDataDetail) {
            if ("conductance".equals(deviceFullData.getProperty())) {// 累计流量
                String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(deviceFullData.getDeviceId()));
                List<String> attributes = Collections.singletonList(deviceId + "." +deviceFullData.getProperty());
                getWaterConductance = obtainDataService.getDeviceData(attributes, start, end, type, null, tenantId);
                break;
            }
        }

        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> supplyWaterConductance = null;
        for (DeviceFullData deviceFullData : supplyWaterStationDataDetail) {
            if ("conductance".equals(deviceFullData.getProperty())) {// 累计流量
                String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(deviceFullData.getDeviceId()));
                List<String> attributes = Collections.singletonList(deviceId + "." +deviceFullData.getProperty());
                supplyWaterConductance = obtainDataService.getDeviceData(attributes, start, end, type, null, tenantId);
                break;
            }
        }

        JSONObject conductance = new JSONObject();
        conductance.put("出水电导率", getWaterConductance);
        conductance.put("取水电导率", supplyWaterConductance);
        result.put("conductance", conductance);

        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> getWaterOxygen = null;
        for (DeviceFullData deviceFullData : getWaterStationDataDetail) {
            if ("conductance".equals(deviceFullData.getProperty())) {// 累计流量
                String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(deviceFullData.getDeviceId()));
                List<String> attributes = Collections.singletonList(deviceId + "." +deviceFullData.getProperty());
                getWaterOxygen = obtainDataService.getDeviceData(attributes, start, end, type, null, tenantId);
                break;
            }
        }

        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> supplyWaterOxygen = null;
        for (DeviceFullData deviceFullData : supplyWaterStationDataDetail) {
            if ("oxygen".equals(deviceFullData.getProperty())) {// 累计流量
                String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(deviceFullData.getDeviceId()));
                List<String> attributes = Collections.singletonList(deviceId + "." +deviceFullData.getProperty());
                supplyWaterOxygen = obtainDataService.getDeviceData(attributes, start, end, type, null, tenantId);
                break;
            }
        }

        JSONObject oxygen = new JSONObject();
        oxygen.put("出水溶解氧", getWaterOxygen);
        oxygen.put("取水溶解氧", supplyWaterOxygen);
        result.put("oxygen", oxygen);

        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> getWaterPh = null;
        for (DeviceFullData deviceFullData : getWaterStationDataDetail) {
            if ("ph".equals(deviceFullData.getProperty())) {// 累计流量
                String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(deviceFullData.getDeviceId()));
                List<String> attributes = Collections.singletonList(deviceId + "." +deviceFullData.getProperty());
                getWaterPh = obtainDataService.getDeviceData(attributes, start, end, type, null, tenantId);
                break;
            }
        }

        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> supplyWaterPh = null;
        for (DeviceFullData deviceFullData : supplyWaterStationDataDetail) {
            if ("ph".equals(deviceFullData.getProperty())) {// 累计流量
                String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(deviceFullData.getDeviceId()));
                List<String> attributes = Collections.singletonList(deviceId + "." +deviceFullData.getProperty());
                supplyWaterPh = obtainDataService.getDeviceData(attributes, start, end, type, null, tenantId);
                break;
            }
        }

        JSONObject ph = new JSONObject();
        ph.put("出水pH", getWaterPh);
        ph.put("取水pH", supplyWaterPh);
        result.put("ph", ph);

        return result;
    }

    /**
     * 查询最近三天的出水流量
     */
    public Object stationFlowRate(String stationId, TenantId tenantId) throws ThingsboardException {
        // 查询站点信息
        StationEntity station = stationFeignClient.get(stationId);
        if (station == null) {
            return null;
        }
        // 查询站点的出水信息
        List<StationAttrDTO> stationAttrDTOList = stationFeignClient.getAllAttrList(stationId);

        // 按年查询供水量
        StationAttrDTO supplyWaterStationAttr = null;

        for (StationAttrDTO stationAttrDTO : stationAttrDTOList) {
            if (stationAttrDTO.getType().contains(DataConstants.DeviceAttrGroupType.WATER_OUTLET.getValue())) {
                supplyWaterStationAttr = stationAttrDTO;
            }
        }

        if (supplyWaterStationAttr == null) {
            log.error("水厂未设置供水相关的动态属性分组");
            return null;
        }

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        // 时间点
        Calendar instance = Calendar.getInstance();
        // 当前时间
        Date now = instance.getTime();
        instance.set(Calendar.HOUR_OF_DAY, 0);
        instance.set(Calendar.MINUTE, 0);
        instance.set(Calendar.SECOND, 0);
        // 今天的0点
        Date todayStart = instance.getTime();
        // 昨天0点
        Date yesterdayStart = new Date(todayStart.getTime() - (24 * 60 * 60 * 1000));
        // 前天0点
        Date beforeYesterdayStart = new Date(yesterdayStart.getTime() - (24 * 60 * 60 * 1000));

        JSONObject result = new JSONObject();

        List<DeviceFullData> stationDataDetail = stationDataService.getStationDataDetail(stationId, supplyWaterStationAttr.getType(), true, tenantId);
        for (DeviceFullData deviceFullData : stationDataDetail) {
            if ("Instantaneous_flow".equals(deviceFullData.getProperty())) {// 瞬时流量
                String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(deviceFullData.getDeviceId()));
                List<String> attributes = Collections.singletonList(deviceId + "." +deviceFullData.getProperty());
                // 今日数据
                LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> todayData
                        = obtainDataService.getDeviceData(attributes, todayStart.getTime(), now.getTime(), DateUtils.HOUR, null, tenantId);
                result.put("today", todayData);
                // 昨日数据
                LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> yesterdayData
                        = obtainDataService.getDeviceData(attributes, yesterdayStart.getTime(), todayStart.getTime(), DateUtils.HOUR, null, tenantId);
                result.put("yesterday", yesterdayData);
                // 前日数据
                LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> beforeYesterdayData
                        = obtainDataService.getDeviceData(attributes, beforeYesterdayStart.getTime(), yesterdayStart.getTime(), DateUtils.HOUR, null, tenantId);
                result.put("beforeYesterday", beforeYesterdayData);


            }
        }

        return result;
    }

    /**
     * 查询最近三天的出水压力
     */
    public Object stationPressure(String stationId, TenantId tenantId) throws ThingsboardException {
        // 查询站点信息
        StationEntity station = stationFeignClient.get(stationId);
        if (station == null) {
            return null;
        }
        // 查询站点的出水信息
        List<StationAttrDTO> stationAttrDTOList = stationFeignClient.getAllAttrList(stationId);

        // 按年查询供水量
        StationAttrDTO supplyWaterStationAttr = null;

        for (StationAttrDTO stationAttrDTO : stationAttrDTOList) {
            if (stationAttrDTO.getType().contains(DataConstants.DeviceAttrGroupType.WATER_OUTLET.getValue())) {
                supplyWaterStationAttr = stationAttrDTO;
            }
        }

        if (supplyWaterStationAttr == null) {
            log.error("水厂未设置供水相关的动态属性分组");
            return null;
        }

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        // 时间点
        Calendar instance = Calendar.getInstance();
        // 当前时间
        Date now = instance.getTime();
        instance.set(Calendar.HOUR_OF_DAY, 0);
        instance.set(Calendar.MINUTE, 0);
        instance.set(Calendar.SECOND, 0);
        // 今天的0点
        Date todayStart = instance.getTime();
        // 昨天0点
        Date yesterdayStart = new Date(todayStart.getTime() - (24 * 60 * 60 * 1000));
        // 前天0点
        Date beforeYesterdayStart = new Date(yesterdayStart.getTime() - (24 * 60 * 60 * 1000));

        JSONObject result = new JSONObject();

        List<DeviceFullData> stationDataDetail = stationDataService.getStationDataDetail(stationId, supplyWaterStationAttr.getType(), true, tenantId);
        for (DeviceFullData deviceFullData : stationDataDetail) {
            if ("pressure".equals(deviceFullData.getProperty())) {// 压力
                String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(deviceFullData.getDeviceId()));
                List<String> attributes = Collections.singletonList(deviceId + "." +deviceFullData.getProperty());
                // 今日数据
                LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> todayData
                        = obtainDataService.getDeviceData(attributes, todayStart.getTime(), now.getTime(), DateUtils.HOUR, null, tenantId);
                result.put("today", todayData);
                // 昨日数据
                LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> yesterdayData
                        = obtainDataService.getDeviceData(attributes, yesterdayStart.getTime(), todayStart.getTime(), DateUtils.HOUR, null, tenantId);
                result.put("yesterday", yesterdayData);
                // 前日数据
                LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> beforeYesterdayData
                        = obtainDataService.getDeviceData(attributes, beforeYesterdayStart.getTime(), yesterdayStart.getTime(), DateUtils.HOUR, null, tenantId);
                result.put("beforeYesterday", beforeYesterdayData);


            }
        }

        return result;
    }

    /**
     * 生产看板-总览统计
     */
    public Object productionDashboardView(TenantId tenantId) {
        // 查询项目下的水厂
        PageData<StationEntity> pageResult = stationFeignClient.list(1, 999999, "水厂", "");
        List<StationEntity> stationList = pageResult.getData();

        if (stationList == null || stationList.isEmpty()) {
            return null;
        }

        List<StationAttrDTO> stationAttrList = new ArrayList<>();
        for (StationEntity station : stationList) {
            // 查询站点的出水信息
            List<StationAttrDTO> stationAttrDTOList = stationFeignClient.getAllAttrList(station.getId());

            for (StationAttrDTO stationAttrDTO : stationAttrDTOList) {
                if (stationAttrDTO.getType().contains(DataConstants.DeviceAttrGroupType.WATER_OUTLET.getValue())) {
                    stationAttrList.add(stationAttrDTO);
                }
            }
        }

        return null;
    }

    public Object yearDrugConsumptionAnalyze(String stationId, TenantId tenantId) throws Exception {
        // 查询站点信息
        StationEntity station = stationFeignClient.get(stationId);
        if (station == null) {
            return null;
        }
        // 查询站点的出水信息
        List<StationAttrDTO> stationAttrDTOList = stationFeignClient.getAllAttrList(stationId);

        // 按年查询供水量
        StationAttrDTO stationAttr = null;
        for (StationAttrDTO stationAttrDTO : stationAttrDTOList) {
            if (stationAttrDTO.getType().contains(DataConstants.DeviceAttrGroupType.WATER_INLET.getValue())) {
                stationAttr = stationAttrDTO;
                break;
            }
        }

        if (stationAttr == null) {
            log.error("水厂未设置进水相关的动态属性分组");
            return null;
        }

        List<DeviceFullData> stationDataDetail = stationDataService.getStationDataDetail(stationId, stationAttr.getType(), true, tenantId);

        SimpleDateFormat monthDateFormat = new SimpleDateFormat("yyyy-MM");
        Calendar instance = Calendar.getInstance();
        instance.set(Calendar.MONTH, 0);
        instance.set(Calendar.DAY_OF_MONTH, 1);
        instance.set(Calendar.HOUR_OF_DAY, 0);
        instance.set(Calendar.MINUTE, 0);
        instance.set(Calendar.SECOND, 0);

        Date yearStart = instance.getTime();
        Date now = new Date();

        instance.set(Calendar.MONTH, 11);
        instance.set(Calendar.DAY_OF_MONTH, 30);
        instance.set(Calendar.HOUR_OF_DAY, 23);
        instance.set(Calendar.MINUTE, 59);
        instance.set(Calendar.SECOND, 59);
        Date yearEnd = instance.getTime();

        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> totalFlowData = null;
        for (DeviceFullData deviceFullData : stationDataDetail) {
            if ("total_flow".equals(deviceFullData.getProperty())) {// 累计流量
                String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(deviceFullData.getDeviceId()));
                List<String> attributes = Collections.singletonList(deviceId + "." +deviceFullData.getProperty());
                totalFlowData = obtainDataService.getDeviceData(attributes, yearStart.getTime(), yearEnd.getTime(), DateUtils.MONTH, null, tenantId);
                break;
            }
        }

        // 查询药耗列表
        List<MedicineManage> medicineManageList = medicineManageFeignClient.all(stationId);


        // 药耗年统计曲线
        List<JSONObject> consumptionList = new ArrayList<>();
        if (totalFlowData != null) {
            ArrayList<String> keyList = new ArrayList<>(totalFlowData.keySet());

            for (String s : keyList) {
                // 筛选出本月的数据
                List<MedicineManage> monthData = medicineManageList.stream()
                        .filter(medicineManage -> s.equals(monthDateFormat.format(medicineManage.getTime())))
                        .collect(Collectors.toList());
                BigDecimal monthTotalValue = new BigDecimal("0");
                for (MedicineManage monthDatum : monthData) {
                    monthTotalValue = monthTotalValue.add(monthDatum.getNum());
                }

                LinkedHashMap<String, BigDecimal> data = totalFlowData.get(s);
                JSONObject monthConsumptionData = new JSONObject();
                monthConsumptionData.put("date", s);
                if (data != null) {
                    try {
                        BigDecimal v = new ArrayList<>(data.values()).get(0);
                        monthConsumptionData.put("value", monthTotalValue.divide(v.divide(new BigDecimal("1000"), 2, BigDecimal.ROUND_DOWN), 2, BigDecimal.ROUND_DOWN));
                    } catch (Exception ignored) {
                    }
                }

                consumptionList.add(monthConsumptionData);
            }

        }

        return consumptionList;
    }

    public Object drugConsumptionAnalyzeByType(String stationId, Long startTime, Long endTime, TenantId tenantId) throws Exception {
        // 查询站点信息
        StationEntity station = stationFeignClient.get(stationId);
        if (station == null) {
            return null;
        }
        // 查询站点的出水信息
        List<StationAttrDTO> stationAttrDTOList = stationFeignClient.getAllAttrList(stationId);

        // 按年查询供水量
        StationAttrDTO stationAttr = null;
        for (StationAttrDTO stationAttrDTO : stationAttrDTOList) {
            if (stationAttrDTO.getType().contains(DataConstants.DeviceAttrGroupType.WATER_INLET.getValue())) {
                stationAttr = stationAttrDTO;
                break;
            }
        }

        if (stationAttr == null) {
            log.error("水厂未设置进水相关的动态属性分组");
            return null;
        }

        List<DeviceFullData> stationDataDetail = stationDataService.getStationDataDetail(stationId, stationAttr.getType(), true, tenantId);

        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> totalFlowData = null;
        for (DeviceFullData deviceFullData : stationDataDetail) {
            if ("total_flow".equals(deviceFullData.getProperty())) {// 累计流量
                String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(deviceFullData.getDeviceId()));
                List<String> attributes = Collections.singletonList(deviceId + "." +deviceFullData.getProperty());
                totalFlowData = obtainDataService.getDeviceData(attributes, startTime, endTime, DateUtils.MONTH, null, tenantId);
                break;
            }
        }

        // 查询药耗列表
        List<MedicineManage> medicineManageList = medicineManageFeignClient.findByTimeAndStationId(startTime, endTime, stationId);
        // 按类型分组
        Map<String, BigDecimal> typeMap = new HashMap<>();
        for (MedicineManage medicineManage : medicineManageList) {
            String medicineType = medicineManage.getMedicineType();
            BigDecimal decimal = typeMap.get(medicineType);
            if (decimal == null) {
                decimal = new BigDecimal("0");
            }

            decimal = decimal.add(medicineManage.getNum());
            typeMap.put(medicineType, decimal);
        }

        List<JSONObject> resultList = new ArrayList<>();
        if (!typeMap.isEmpty()) {
            if (totalFlowData != null) {
                BigDecimal flow = BigDecimal.ZERO;
                for (Map.Entry<String, LinkedHashMap<String, BigDecimal>> entry : totalFlowData.entrySet()) {
                    LinkedHashMap<String, BigDecimal> value = entry.getValue();
                    for (Map.Entry<String, BigDecimal> decimalEntry : value.entrySet()) {
                        BigDecimal dataValue = decimalEntry.getValue();
                        if (dataValue != null) {
                            flow = flow.add(dataValue);
                        }
                    }
                }

                for (Map.Entry<String, BigDecimal> typeMapEntry : typeMap.entrySet()) {
                    String key = typeMapEntry.getKey();
                    BigDecimal value = typeMapEntry.getValue();

                    JSONObject data = new JSONObject();
                    data.put("type", key);
                    data.put("totalFlow", flow);
                    data.put("total", value);
                    if (value != null && flow.doubleValue() != 0) {
                        data.put("consumption", value.divide(flow, 4));
                    }
                    resultList.add(data);
                }

            }
        }

        return resultList;
    }
}
