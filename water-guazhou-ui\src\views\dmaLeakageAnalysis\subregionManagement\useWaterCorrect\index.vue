<!-- 用水量修正 -->
<template>
  <TreeBox>
    <template #tree>
      <SLTree
        ref="refTree"
        :tree-data="TreeData"
      ></SLTree>
    </template>
    <CardSearch
      ref="refSearch"
      :config="SearchConfig"
    ></CardSearch>
    <CardTable
      :config="TableConfig"
      class="card-table"
    ></CardTable>
    <DialogForm
      ref="refForm"
      :config="FormConfig"
    />
    <RecordList
      ref="refRecord"
      :partition-id="TreeData.currentProject?.value"
    ></RecordList>
  </TreeBox>
</template>
<script lang="ts" setup>
import { CorrectDMASupplyTotalFlow, GetDMAReadMeterData, ExportDMAReadMeterData } from '@/api/mapservice/dma'
import { ICardSearchIns, IDialogFormIns } from '@/components/type'
import { usePartition } from '@/hooks/arcgis'
import { formatDate } from '@/utils/DateFormatter'
import { formatterDate, formatterMonth } from '@/utils/GlobalHelper'
import { SLConfirm, SLMessage } from '@/utils/Message'
import RecordList from './components/RecordList.vue'
import { saveAs } from '@/utils/printUtils'

const refForm = ref<IDialogFormIns>()
const refSearch = ref<ICardSearchIns>()
// 分区树配置
const TreeData = reactive<SLTreeConfig>({
  data: [],
  loading: true,
  title: '选择分区',
  expandOnClickNode: false,
  defaultExpandAll: true,
  treeNodeHandleClick: (data: NormalOption) => {
    if (TreeData.currentProject !== data) {
      // TreeData.loading = true
      TreeData.currentProject = data
      refreshData()
    }
  }
})
const partition = usePartition()
const refreshTree = async () => {
  await partition.getTree()
  TreeData.data = partition.Tree.value
  TreeData.currentProject = partition.Tree.value?.[0]
  refreshData()
}
// 查询条件配置
const SearchConfig = reactive<ISearch>({
  defaultParams: {
    type: 'month',
    month: [moment().subtract(1, 'M').startOf('M').format(formatterMonth), moment().endOf('M').format(formatterMonth)],
    date: [moment().subtract(1, 'M').startOf('D').format(formatterDate), moment().endOf('D').format(formatterDate)]
  },
  filters: [
    {
      type: 'input',
      label: '用户名称',
      field: 'custName',
      placeholder: '请输入用户姓名'
    },
    {
      type: 'input',
      label: '营收户号',
      field: 'custCode',
      placeholder: '请输入营收户号'
    },
    {
      type: 'select',
      field: 'type',
      width: 90,
      clearable: false,
      options: [
        { label: '按年月', value: 'month' },
        { label: '按年月日', value: 'date' }
      ],
      label: '选择方式',
      placeholder: '请选择'
    },
    {
      handleHidden: (params, query, config) => {
        config.hidden = params.type === 'date'
      },
      type: 'monthrange',
      format: formatterMonth,
      field: 'month',
      clearable: false
    },
    {
      handleHidden: (params, query, config) => {
        config.hidden = params.type === 'month'
      },
      type: 'daterange',
      field: 'date',
      clearable: false
    }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          iconifyIcon: 'ep:search',
          click: () => refreshData()
        },
        {
          perm: true,
          text: '重置',
          type: 'default',
          iconifyIcon: 'ep:refresh',
          click: () => {
            refSearch.value?.resetForm()
          }
        },
        {
          perm: true,
          text: '导出',
          type: 'primary',
          iconifyIcon: 'ep:download',
          click: () => {
            // exportTable()
            refreshData(true)
          }
        },
        {
          perm: true,
          text: '修正记录',
          type: 'default',
          iconifyIcon: 'mdi:eye',
          click: () => handleRecord()
        }
      ]
    }
  ]
})
// 数据列表配置
const TableConfig = reactive<ICardTable>({
  indexVisible: true,
  dataList: [],
  columns: [
    { label: '分区名称', minWidth: 120, prop: 'partitionName' },
    { label: '用户名称', minWidth: 120, prop: 'custName' },
    { label: '营收户号', minWidth: 120, prop: 'custCode' },
    { label: '联系方式', minWidth: 120, prop: 'phone' },
    { label: '抄表年月', minWidth: 120, prop: 'ym' },
    { label: '修正水量', minWidth: 120, prop: 'correctWater' },
    { label: '抄表水量', minWidth: 120, prop: 'totalWater' },
    {
      label: '本次抄表日期',
      minWidth: 120,
      prop: 'thisReadDate',
      formatter: (row, val) => formatDate(val, formatterDate)
    },
    {
      label: '上次抄表时间',
      minWidth: 120,
      prop: 'lastReadDate',
      formatter: (row, val) => formatDate(val, formatterDate)
    }
  ],
  operations: [
    {
      perm: true,
      text: '修改',
      iconifyIcon: 'ep:edit',
      click: row => handleAddEdit(row)
    }
  ],
  pagination: {
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page
      TableConfig.pagination.limit = size
      refreshData()
    }
  }
})
// 数据表单弹框配置
const FormConfig = reactive<IDialogFormConfig>({
  dialogWidth: 450,
  title: '编辑',
  defaultValue: {},
  group: [
    {
      fields: [
        {
          type: 'input',
          label: '营收户号',
          disabled: true,
          field: 'custCode',
          placeholder: ' '
        },
        {
          type: 'input',
          label: '用户姓名',
          field: 'custName',
          disabled: true,
          placeholder: ' '
        },
        {
          type: 'input',
          label: '联系方式',
          field: 'phone',
          disabled: true,
          placeholder: ' '
        },
        {
          type: 'datetime',
          label: '抄表日期',
          field: 'thisReadDate',
          readonly: true
        },
        {
          type: 'number',
          label: '水量',
          field: 'totalWater'
        }
        // {
        //   type: 'monthrange',
        //   label: '抄表月份',
        //   readonly: true,
        //   field: 'ym',
        //   rules: [{ required: true, message: '请选择抄表月份' }],
        //   placeholder: ' '
        // }
      ]
    }
  ],
  submit: params => {
    SLConfirm('确定提交？', '提示信息')
      .then(async () => {
        try {
          FormConfig.submitting = true
          const submitParams = {
            id: params.id,
            correctWater: params.totalWater
          }
          const res = await CorrectDMASupplyTotalFlow(submitParams)
          if (res.data.code === 200) {
            SLMessage.success('操作成功')
            refreshData()
            refForm.value?.closeDialog()
          } else {
            SLMessage.error(res.data.message)
          }
        } catch (error) {
          SLMessage.error('操作失败')
        }
        FormConfig.submitting = false
      })
      .catch(() => {
        //
      })
  }
})

// 添加修改
const handleAddEdit = (row?: any) => {
  FormConfig.defaultValue = {
    ...(row || {}),
    thisReadDate: formatDate(row.thisReadDate, formatterDate),
    id: row?.id
  }
  refForm.value?.openDialog()
}
const refRecord = ref<InstanceType<typeof RecordList>>()
const handleRecord = () => {
  refRecord.value?.refRecord?.openDialog()
}
// 获取配置数据列表
const refreshData = async (isExport?: boolean) => {
  const query = refSearch.value?.queryParams || {}
  try {
    TableConfig.loading = true
    const submitParams: any = {
      page: TableConfig.pagination.page || 1,
      size: TableConfig.pagination.limit || 20,
      custCode: query.custCode,
      custName: query.custName,
      start: undefined,
      end: undefined,
      partitionId: TreeData.currentProject?.value
    }
    if (query.type === 'month') {
      submitParams.start = (query.month?.[0] && moment(query.month[0], formatterMonth).startOf('M').valueOf()) || undefined
      submitParams.end = (query.month?.[1] && moment(query.month[1], formatterMonth).endOf('M').valueOf()) || undefined
    } else {
      submitParams.start = (query.date?.[0] && moment(query.date[0], formatterDate).startOf('D').valueOf()) || undefined
      submitParams.end = (query.date?.[1] && moment(query.date[1], formatterDate).endOf('D').valueOf()) || undefined
    }
    if (isExport) {
      const res = await ExportDMAReadMeterData(submitParams)
      saveAs(res.data, '用水量')
    } else {
      const res = await GetDMAReadMeterData(submitParams)
      const data = res.data.data
      TableConfig.dataList = data.data || []
      TableConfig.pagination.total = data.total || 0
    }
  } catch (error) {
    //
  }
  TableConfig.loading = false
}

onMounted(async () => {
  refreshTree()
})
</script>
<style lang="scss" scoped>
.table-box {
  height: calc(100% - 100px);
  width: 100%;
}
</style>
