"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[3298,3172,9880],{27535:(e,t,s)=>{var r;s.d(t,{Hy:()=>d,OF:()=>c,TD:()=>f,Tu:()=>m,VO:()=>h,aV:()=>o,kq:()=>u,rH:()=>r}),function(e){e.AsyncNotEnabled="AsyncNotEnabled",e.ModulesNotSupported="ModulesNotSupported",e.CircularModules="CircularModules",e.<PERSON>Reach="NeverReach",e.UnsupportedHashType="UnsupportedHashType",e.InvalidParameter="InvalidParameter",e.UnexpectedToken="UnexpectedToken",e.Unrecognised="Unrecognised",e.UnrecognisedType="UnrecognisedType",e.MaximumCallDepth="MaximumCallDepth",e.BooleanConditionRequired="BooleanConditionRequired",e.TypeNotAllowedInFeature="TypeNotAllowedInFeature",e.KeyMustBeString="KeyMustBeString",e.WrongNumberOfParameters="WrongNumberOfParameters",e.CallNonFunction="CallNonFunction",e.NoFunctionInTemplateLiteral="NoFunctionInTemplateLiteral",e.NoFunctionInDictionary="NoFunctionInDictionary",e.NoFunctionInArray="NoFunctionInArray",e.AssignModuleFunction="AssignModuleFunction",e.LogicExpressionOrAnd="LogicExpressionOrAnd",e.LogicalExpressionOnlyBoolean="LogicalExpressionOnlyBoolean",e.FuncionNotFound="FunctionNotFound",e.InvalidMemberAccessKey="InvalidMemberAccessKey",e.UnsupportedUnaryOperator="UnsupportUnaryOperator",e.InvalidIdentifier="InvalidIdentifier",e.MemberOfNull="MemberOfNull",e.UnsupportedOperator="UnsupportedOperator",e.Cancelled="Cancelled",e.ModuleAccessorMustBeString="ModuleAccessorMustBeString",e.ModuleExportNotFound="ModuleExportNotFound",e.Immutable="Immutable",e.OutOfBounds="OutOfBounds",e.IllegalResult="IllegalResult",e.FieldNotFound="FieldNotFound",e.PortalRequired="PortalRequired",e.LogicError="LogicError",e.ArrayAccessorMustBeNumber="ArrayAccessMustBeNumber",e.KeyAccessorMustBeString="KeyAccessorMustBeString",e.WrongSpatialReference="WrongSpatialReference"}(r||(r={}));const i={[r.TypeNotAllowedInFeature]:"Feature attributes only support dates, numbers, strings, guids.",[r.LogicError]:"Logic error - {reason}",[r.NeverReach]:"Encountered unreachable logic",[r.AsyncNotEnabled]:"Async Arcade must be enabled for this script",[r.ModuleAccessorMustBeString]:"Module accessor must be a string",[r.ModuleExportNotFound]:"Module has no export with provided identifier",[r.ModulesNotSupported]:"Current profile does not support modules",[r.ArrayAccessorMustBeNumber]:"Array accessor must be a number",[r.FuncionNotFound]:"Function not found",[r.FieldNotFound]:"Key not found - {key}",[r.CircularModules]:"Circular module dependencies are not allowed",[r.Cancelled]:"Execution cancelled",[r.UnsupportedHashType]:"Type not supported in hash function",[r.IllegalResult]:"Value is not a supported return type",[r.PortalRequired]:"Portal is required",[r.InvalidParameter]:"Invalid parameter",[r.WrongNumberOfParameters]:"Call with wrong number of parameters",[r.Unrecognised]:"Unrecognised code structure",[r.UnrecognisedType]:"Unrecognised type",[r.WrongSpatialReference]:"Cannot work with geometry in this spatial reference. It is different to the execution spatial reference",[r.BooleanConditionRequired]:"Conditions must use booleans",[r.NoFunctionInDictionary]:"Dictionaries cannot contain functions.",[r.NoFunctionInArray]:"Arrays cannot contain functions.",[r.NoFunctionInTemplateLiteral]:"Template Literals do not expect functions by value.",[r.KeyAccessorMustBeString]:"Accessor must be a string",[r.KeyMustBeString]:"Object keys must be a string",[r.Immutable]:"Object is immutable",[r.InvalidParameter]:"Invalid parameter",[r.UnexpectedToken]:"Unexpected token",[r.MemberOfNull]:"Cannot access property of null object",[r.MaximumCallDepth]:"Exceeded maximum function depth",[r.OutOfBounds]:"Out of bounds",[r.InvalidIdentifier]:"Identifier not recognised",[r.FuncionNotFound]:"Function not found",[r.CallNonFunction]:"Expression is not a function",[r.InvalidMemberAccessKey]:"Cannot access value using a key of this type",[r.AssignModuleFunction]:"Cannot assign function to module variable",[r.UnsupportedUnaryOperator]:"Unsupported unary operator",[r.UnsupportedOperator]:"Unsupported operator",[r.LogicalExpressionOnlyBoolean]:"Logical expressions must be boolean",[r.LogicExpressionOrAnd]:"Logical expression can only be combined with || or &&"};class n extends Error{constructor(...e){super(...e)}}class a extends n{constructor(e,t){super(l(t)+e.message,{cause:e}),this.loc=null,Error.captureStackTrace&&Error.captureStackTrace(this,a),t&&t.loc&&(this.loc=t.loc)}}class o extends Error{constructor(e,t,s,r){super("Execution error - "+l(s)+d(i[t],r)),this.loc=null,this.declaredRootClass="esri.arcade.arcadeexecutionerror",Error.captureStackTrace&&Error.captureStackTrace(this,o),s&&s.loc&&(this.loc=s.loc)}}function l(e){return e&&e.loc?`Line : ${e.loc.start?.line}, ${e.loc.start?.column}: `:""}class c extends Error{constructor(e,t,s,r){super("Compilation error - "+l(s)+d(i[t],r)),this.loc=null,this.declaredRootClass="esri.arcade.arcadecompilationerror",Error.captureStackTrace&&Error.captureStackTrace(this,c),s&&s.loc&&(this.loc=s.loc)}}class u extends Error{constructor(){super("Uncompilable code structures"),this.declaredRootClass="esri.arcade.arcadeuncompilableerror",Error.captureStackTrace&&Error.captureStackTrace(this,u)}}function d(e,t){try{if(!t)return e;for(const s in t){let r=t[s];r||(r=""),e=e.replace("{"+s+"}",t[s])}}catch(e){}return e}function h(e,t,s){return"esri.arcade.arcadeexecutionerror"===s.declaredRootClass||"esri.arcade.arcadecompilationerror"===s.declaredRootClass?null===s.loc&&t&&t.loc?new a(s,{cause:s}):s:("esri.arcade.featureset.support.featureseterror"===s.declaredRootClass||"esri.arcade.featureset.support.sqlerror"===s.declaredRootClass||s.declaredRootClass,t&&t.loc?new a(s,{cause:s}):s)}var f;!function(e){e.UnrecognisedUri="UnrecognisedUri",e.UnsupportedUriProtocol="UnsupportedUriProtocol"}(f||(f={}));const p={[f.UnrecognisedUri]:"Unrecognised uri - {uri}",[f.UnsupportedUriProtocol]:"Unrecognised uri protocol"};class m extends Error{constructor(e,t){super(d(p[e],t)),this.declaredRootClass="esri.arcade.arcademoduleerror",Error.captureStackTrace&&Error.captureStackTrace(this,m)}}},99880:(e,t,s)=>{s.d(t,{V:()=>l});var r=s(68773),i=(s(3172),s(20102)),n=s(92604),a=s(17452);const o=n.Z.getLogger("esri.assets");function l(e){if(!r.Z.assetsPath)throw o.errorOnce("The API assets location needs to be set using config.assetsPath. More information: https://arcg.is/1OzLe50"),new i.Z("assets:path-not-set","config.assetsPath is not set");return(0,a.v_)(r.Z.assetsPath,e)}},74669:(e,t,s)=>{s.d(t,{Z:()=>i});var r=s(69801);class i{constructor(e,t){this._storage=new r.WJ,this._storage.maxSize=e,t&&this._storage.registerRemoveFunc("",t)}put(e,t){this._storage.put(e,t,1,1)}pop(e){return this._storage.pop(e)}get(e){return this._storage.get(e)}clear(){this._storage.clearAll()}destroy(){this._storage.destroy()}}},10661:(e,t,s)=>{s.d(t,{s:()=>i});var r=s(42100);class i extends r.s{notify(){const e=this._observers;if(e&&e.length>0){const t=e.slice();for(const e of t)e.onInvalidated(),e.onCommitted()}}}},37455:(e,t,s)=>{s.d(t,{N:()=>r});const r={convertToGEGeometry:function(e,t){return null==t?null:e.convertJSONToGeometry(t)},exportPoint:function(e,t,s){const r=new i(e.getPointX(t),e.getPointY(t),s),n=e.hasZ(t),a=e.hasM(t);return n&&(r.z=e.getPointZ(t)),a&&(r.m=e.getPointM(t)),r},exportPolygon:function(e,t,s){return new n(e.exportPaths(t),s,e.hasZ(t),e.hasM(t))},exportPolyline:function(e,t,s){return new a(e.exportPaths(t),s,e.hasZ(t),e.hasM(t))},exportMultipoint:function(e,t,s){return new o(e.exportPoints(t),s,e.hasZ(t),e.hasM(t))},exportExtent:function(e,t,s){const r=e.hasZ(t),i=e.hasM(t),n=new l(e.getXMin(t),e.getYMin(t),e.getXMax(t),e.getYMax(t),s);if(r){const s=e.getZExtent(t);n.zmin=s.vmin,n.zmax=s.vmax}if(i){const s=e.getMExtent(t);n.mmin=s.vmin,n.mmax=s.vmax}return n}};class i{constructor(e,t,s){this.x=e,this.y=t,this.spatialReference=s,this.z=void 0,this.m=void 0}}class n{constructor(e,t,s,r){this.rings=e,this.spatialReference=t,this.hasZ=void 0,this.hasM=void 0,s&&(this.hasZ=s),r&&(this.hasM=r)}}class a{constructor(e,t,s,r){this.paths=e,this.spatialReference=t,this.hasZ=void 0,this.hasM=void 0,s&&(this.hasZ=s),r&&(this.hasM=r)}}class o{constructor(e,t,s,r){this.points=e,this.spatialReference=t,this.hasZ=void 0,this.hasM=void 0,s&&(this.hasZ=s),r&&(this.hasM=r)}}class l{constructor(e,t,s,r,i){this.xmin=e,this.ymin=t,this.xmax=s,this.ymax=r,this.spatialReference=i,this.zmin=void 0,this.zmax=void 0,this.mmin=void 0,this.mmax=void 0}}},26993:(e,t,s)=>{s.d(t,{y:()=>w,r:()=>p});var r=s(70586),i=s(35308),n=s(49600),a=s(82397),o=s(8744),l=s(62432),c=s(24452);class u{constructor(e,t,s){this._fieldDataCache=new Map,this._returnDistinctMap=new Map,this.returnDistinctValues=e.returnDistinctValues??!1,this.fieldsIndex=s,this.featureAdapter=t;const r=e.outFields;if(r&&!r.includes("*")){this.outFields=r;let e=0;for(const t of r){const r=(0,l.hr)(t),i=this.fieldsIndex.get(r),n=i?null:(0,l.Jc)(r,s),a=i?i.name:(0,l.nu)(t)||"FIELD_EXP_"+e++;this._fieldDataCache.set(t,{alias:a,clause:n})}}}countDistinctValues(e){return this.returnDistinctValues?(e.forEach((e=>this.getAttributes(e))),this._returnDistinctMap.size):e.length}getAttributes(e){const t=this._processAttributesForOutFields(e);return this._processAttributesForDistinctValues(t)}getFieldValue(e,t,s){const r=s?s.name:t;let i=null;return this._fieldDataCache.has(r)?i=this._fieldDataCache.get(r)?.clause:s||(i=(0,l.Jc)(t,this.fieldsIndex),this._fieldDataCache.set(r,{alias:r,clause:i})),s?this.featureAdapter.getAttribute(e,r):i?.calculateValue(e,this.featureAdapter)}getDataValue(e,t){const s=t.normalizationType,r=t.normalizationTotal;let i=t.field&&this.getFieldValue(e,t.field,this.fieldsIndex.get(t.field));if(t.field2&&(i=`${(0,c.wk)(i)}${t.fieldDelimiter}${(0,c.wk)(this.getFieldValue(e,t.field2,this.fieldsIndex.get(t.field2)))}`,t.field3&&(i=`${i}${t.fieldDelimiter}${(0,c.wk)(this.getFieldValue(e,t.field3,this.fieldsIndex.get(t.field3)))}`)),s&&Number.isFinite(i)){const n="field"===s&&t.normalizationField?this.getFieldValue(e,t.normalizationField,this.fieldsIndex.get(t.normalizationField)):null;i=(0,c.fk)(i,s,n,r)}return i}getExpressionValue(e,t,s,r){const i={attributes:this.featureAdapter.getAttributes(e),layer:{fields:this.fieldsIndex.fields}},n=r.createExecContext(i,s);return r.executeFunction(t,n)}getExpressionValues(e,t,s,r){const i={fields:this.fieldsIndex.fields};return e.map((e=>{const n={attributes:this.featureAdapter.getAttributes(e),layer:i},a=r.createExecContext(n,s);return r.executeFunction(t,a)}))}validateItem(e,t){return this._fieldDataCache.has(t)||this._fieldDataCache.set(t,{alias:t,clause:(0,l.Jc)(t,this.fieldsIndex)}),this._fieldDataCache.get(t)?.clause?.testFeature(e,this.featureAdapter)??!1}validateItems(e,t){return this._fieldDataCache.has(t)||this._fieldDataCache.set(t,{alias:t,clause:(0,l.Jc)(t,this.fieldsIndex)}),this._fieldDataCache.get(t)?.clause?.testSet(e,this.featureAdapter)??!1}_processAttributesForOutFields(e){const t=this.outFields;if(!t||!t.length)return this.featureAdapter.getAttributes(e);const s={};for(const r of t){const{alias:t,clause:i}=this._fieldDataCache.get(r);s[t]=i?i.calculateValue(e,this.featureAdapter):this.featureAdapter.getAttribute(e,t)}return s}_processAttributesForDistinctValues(e){if((0,r.Wi)(e)||!this.returnDistinctValues)return e;const t=this.outFields,s=[];if(t)for(const r of t){const{alias:t}=this._fieldDataCache.get(r);s.push(e[t])}else for(const t in e)s.push(e[t]);const i=`${(t||["*"]).join(",")}=${s.join(",")}`;let n=this._returnDistinctMap.get(i)||0;return this._returnDistinctMap.set(i,++n),n>1?null:e}}var d=s(37427);function h(e,t,s){return{objectId:e,target:t,distance:s,type:"vertex"}}function f(e,t,s,r,i,n=!1){return{objectId:e,target:t,distance:s,type:"edge",start:r,end:i,draped:n}}var p,m,g=s(11490),y=s(35671),x=s(59266);class w{constructor(e,t,s){this.items=e,this.query=t,this.geometryType=s.geometryType,this.hasM=s.hasM,this.hasZ=s.hasZ,this.fieldsIndex=s.fieldsIndex,this.objectIdField=s.objectIdField,this.spatialReference=s.spatialReference,this.featureAdapter=s.featureAdapter}get size(){return this.items.length}createQueryResponseForCount(){const e=new u(this.query,this.featureAdapter,this.fieldsIndex);if(!this.query.outStatistics)return e.countDistinctValues(this.items);const{groupByFieldsForStatistics:t,having:s,outStatistics:r}=this.query,i=t?.length;if(!i)return 1;const n=new Map,a=new Map,o=new Set;for(const i of r){const{statisticType:r}=i,l="exceedslimit"!==r?i.onStatisticField:void 0;if(!a.has(l)){const s=[];for(const r of t){const t=this._getAttributeValues(e,r,n);s.push(t)}a.set(l,this._calculateUniqueValues(s,e.returnDistinctValues))}const c=a.get(l);for(const t in c){const{data:r,items:i}=c[t],n=r.join(",");s&&!e.validateItems(i,s)||o.add(n)}}return o.size}async createQueryResponse(){let e;if(e=this.query.outStatistics?this.query.outStatistics.some((e=>"exceedslimit"===e.statisticType))?this._createExceedsLimitQueryResponse(this.query):await this._createStatisticsQueryResponse(this.query):this._createFeatureQueryResponse(this.query),this.query.returnQueryGeometry){const t=this.query.geometry;(0,o.JY)(this.query.outSR)&&!(0,o.fS)(t.spatialReference,this.query.outSR)?e.queryGeometry=(0,g.S2)({spatialReference:this.query.outSR,...(0,d.iV)(t,t.spatialReference,this.query.outSR)}):e.queryGeometry=(0,g.S2)({spatialReference:this.query.outSR,...t})}return e}createSnappingResponse(e,t){const s=this.featureAdapter,i=v(this.hasZ,this.hasM),{point:n,mode:a}=e,o="number"==typeof e.distance?e.distance:e.distance.x,l="number"==typeof e.distance?e.distance:e.distance.y,c={candidates:[]},u="esriGeometryPolygon"===this.geometryType,d=this._getPointCreator(a,this.spatialReference,t),m=new T(null,0),g=new T(null,0),y={x:0,y:0,z:0};for(const t of this.items){const a=s.getGeometry(t);if((0,r.Wi)(a))continue;const{coords:x,lengths:w}=a;if(m.coords=x,g.coords=x,e.types&p.EDGE){let e=0;for(let r=0;r<w.length;r++){const a=w[r];for(let r=0;r<a;r++,e+=i){const u=m;if(u.coordsIndex=e,r!==a-1){const r=g;r.coordsIndex=e+i;const a=y;b(y,n,u,r);const h=(n.x-a.x)/o,p=(n.y-a.y)/l,m=h*h+p*p;m<=1&&c.candidates.push(f(s.getObjectId(t),d(a),Math.sqrt(m),d(u),d(r)))}}}}if(e.types&p.VERTEX){const e=u?x.length-i:x.length;for(let r=0;r<e;r+=i){const e=m;e.coordsIndex=r;const i=(n.x-e.x)/o,a=(n.y-e.y)/l,u=i*i+a*a;u<=1&&c.candidates.push(h(s.getObjectId(t),d(e),Math.sqrt(u)))}}}return c.candidates.sort(((e,t)=>e.distance-t.distance)),c}_getPointCreator(e,t,s){const i=(0,r.pC)(s)&&!(0,o.fS)(t,s)?e=>(0,d.iV)(e,t,s):e=>e,{hasZ:n}=this;return"3d"===e?n?({x:e,y:t,z:s})=>i({x:e,y:t,z:s}):({x:e,y:t})=>i({x:e,y:t,z:0}):({x:e,y:t})=>i({x:e,y:t})}async createSummaryStatisticsResponse(e){const{field:t,valueExpression:s,normalizationField:r,normalizationType:i,normalizationTotal:n,minValue:a,maxValue:o,scale:l}=e,u=this.fieldsIndex.isDateField(t),d=await this._getDataValues({field:t,valueExpression:s,normalizationField:r,normalizationType:i,normalizationTotal:n,scale:l}),h=(0,c.S5)({normalizationType:i,normalizationField:r,minValue:a,maxValue:o}),f=this.fieldsIndex.get(t),p={value:.5,fieldType:f?.type},m=(0,y.qN)(f)?(0,c.H0)({values:d,supportsNullCount:h,percentileParams:p}):(0,c.i5)({values:d,minValue:a,maxValue:o,useSampleStdDev:!i,supportsNullCount:h,percentileParams:p});return(0,c.F_)(m,u)}async createUniqueValuesResponse(e){const{field:t,valueExpression:s,domains:r,returnAllCodedValues:i,scale:n}=e,a=await this._getDataValues({field:t,field2:e.field2,field3:e.field3,fieldDelimiter:e.fieldDelimiter,valueExpression:s,scale:n}),o=(0,c.eT)(a);return(0,c.Qm)(o,r,i,e.fieldDelimiter)}async createClassBreaksResponse(e){const{field:t,valueExpression:s,normalizationField:r,normalizationType:i,normalizationTotal:n,classificationMethod:a,standardDeviationInterval:o,minValue:l,maxValue:u,numClasses:d,scale:h}=e,f=await this._getDataValues({field:t,valueExpression:s,normalizationField:r,normalizationType:i,normalizationTotal:n,scale:h}),p=(0,c.G2)(f,{field:t,normalizationField:r,normalizationType:i,normalizationTotal:n,classificationMethod:a,standardDeviationInterval:o,minValue:l,maxValue:u,numClasses:d});return(0,c.DL)(p,a)}async createHistogramResponse(e){const{field:t,valueExpression:s,normalizationField:r,normalizationType:i,normalizationTotal:n,classificationMethod:a,standardDeviationInterval:o,minValue:l,maxValue:u,numBins:d,scale:h}=e,f=await this._getDataValues({field:t,valueExpression:s,normalizationField:r,normalizationType:i,normalizationTotal:n,scale:h});return(0,c.oF)(f,{field:t,normalizationField:r,normalizationType:i,normalizationTotal:n,classificationMethod:a,standardDeviationInterval:o,minValue:l,maxValue:u,numBins:d})}_sortFeatures(e,t,s){if(e.length>1&&t&&t.length)for(const r of t.reverse()){const t=r.split(" "),i=t[0],n=this.fieldsIndex.get(i),a=!!t[1]&&"desc"===t[1].toLowerCase(),o=(0,c.Lq)(n?.type,a);e.sort(((e,t)=>{const r=s(e,i,n),a=s(t,i,n);return o(r,a)}))}}_createFeatureQueryResponse(e){const t=this.items,{geometryType:s,hasM:r,hasZ:i,objectIdField:n,spatialReference:o}=this,{outFields:l,outSR:c,quantizationParameters:u,resultRecordCount:d,resultOffset:h,returnZ:f,returnM:p}=e,m=null!=d&&t.length>(h||0)+d,y=l&&(l.includes("*")?[...this.fieldsIndex.fields]:l.map((e=>this.fieldsIndex.get(e))));return{exceededTransferLimit:m,features:this._createFeatures(e,t),fields:y,geometryType:s,hasM:r&&p,hasZ:i&&f,objectIdFieldName:n,spatialReference:(0,g.S2)(c||o),transform:u&&(0,a.vY)(u)||null}}_createFeatures(e,t){const s=new u(e,this.featureAdapter,this.fieldsIndex),{hasM:r,hasZ:i}=this,{orderByFields:n,quantizationParameters:o,returnGeometry:l,returnCentroid:c,maxAllowableOffset:d,resultOffset:h,resultRecordCount:f,returnZ:p=!1,returnM:m=!1}=e,y=i&&p,x=r&&m;let w=[],b=0;const v=[...t];if(this._sortFeatures(v,n,((e,t,r)=>s.getFieldValue(e,t,r))),l||c){const e=(0,a.vY)(o)??void 0;if(l&&!c)for(const t of v)w[b++]={attributes:s.getAttributes(t),geometry:(0,g.Op)(this.geometryType,this.hasZ,this.hasM,this.featureAdapter.getGeometry(t),d,e,y,x)};else if(!l&&c)for(const t of v)w[b++]={attributes:s.getAttributes(t),centroid:(0,g.EG)(this,this.featureAdapter.getCentroid(t,this),e)};else for(const t of v)w[b++]={attributes:s.getAttributes(t),centroid:(0,g.EG)(this,this.featureAdapter.getCentroid(t,this),e),geometry:(0,g.Op)(this.geometryType,this.hasZ,this.hasM,this.featureAdapter.getGeometry(t),d,e,y,x)}}else for(const e of v){const t=s.getAttributes(e);t&&(w[b++]={attributes:t})}const T=h||0;if(null!=f){const e=T+f;w=w.slice(T,Math.min(w.length,e))}return w}_createExceedsLimitQueryResponse(e){let t=!1,s=Number.POSITIVE_INFINITY,i=Number.POSITIVE_INFINITY,n=Number.POSITIVE_INFINITY;for(const t of e.outStatistics??[])if("exceedslimit"===t.statisticType){s=null!=t.maxPointCount?t.maxPointCount:Number.POSITIVE_INFINITY,i=null!=t.maxRecordCount?t.maxRecordCount:Number.POSITIVE_INFINITY,n=null!=t.maxVertexCount?t.maxVertexCount:Number.POSITIVE_INFINITY;break}if("esriGeometryPoint"===this.geometryType)t=this.items.length>s;else if(this.items.length>i)t=!0;else{const e=v(this.hasZ,this.hasM),s=this.featureAdapter;t=this.items.reduce(((e,t)=>{const i=s.getGeometry(t);return e+((0,r.pC)(i)&&i.coords.length||0)}),0)/e>n}return{fields:[{name:"exceedslimit",type:"esriFieldTypeInteger",alias:"exceedslimit",sqlType:"sqlTypeInteger",domain:null,defaultValue:null}],features:[{attributes:{exceedslimit:Number(t)}}]}}async _createStatisticsQueryResponse(e){const t={attributes:{}},s=[],r=new Map,i=new Map,n=new Map,a=new Map,o=new u(e,this.featureAdapter,this.fieldsIndex),l=e.outStatistics,{groupByFieldsForStatistics:c,having:d,orderByFields:h}=e,f=c&&c.length,p=!!f,m=p?c[0]:null,g=p&&!this.fieldsIndex.get(m);for(const e of l??[]){const{outStatisticFieldName:l,statisticType:u}=e,h=e,y="exceedslimit"!==u?e.onStatisticField:void 0,x="percentile_disc"===u||"percentile_cont"===u,w="EnvelopeAggregate"===u||"CentroidAggregate"===u||"ConvexHullAggregate"===u,b=p&&1===f&&(y===m||g)&&"count"===u;if(p){if(!n.has(y)){const e=[];for(const t of c){const s=this._getAttributeValues(o,t,r);e.push(s)}n.set(y,this._calculateUniqueValues(e,!w&&o.returnDistinctValues))}const e=n.get(y);for(const t in e){const{count:s,data:i,items:n,itemPositions:u}=e[t],f=i.join(",");if(!d||o.validateItems(n,d)){const e=a.get(f)||{attributes:{}};if(w){e.aggregateGeometries||(e.aggregateGeometries={});const{aggregateGeometries:t,outStatisticFieldName:s}=await this._getAggregateGeometry(h,n);e.aggregateGeometries[s]=t}else{let t=null;if(b)t=s;else{const e=this._getAttributeValues(o,y,r),s=u.map((t=>e[t]));t=x&&"statisticParameters"in h?this._getPercentileValue(h,s):this._getStatisticValue(h,s,null,o.returnDistinctValues)}e.attributes[l]=t}let t=0;c.forEach(((s,r)=>e.attributes[this.fieldsIndex.get(s)?s:"EXPR_"+ ++t]=i[r])),a.set(f,e)}}}else if(w){t.aggregateGeometries||(t.aggregateGeometries={});const{aggregateGeometries:e,outStatisticFieldName:s}=await this._getAggregateGeometry(h,this.items);t.aggregateGeometries[s]=e}else{const e=this._getAttributeValues(o,y,r);t.attributes[l]=x&&"statisticParameters"in h?this._getPercentileValue(h,e):this._getStatisticValue(h,e,i,o.returnDistinctValues)}s.push({name:l,alias:l,type:"esriFieldTypeDouble"})}const y=p?Array.from(a.values()):[t];return this._sortFeatures(y,h,((e,t)=>e.attributes[t])),{fields:s,features:y}}async _getAggregateGeometry(e,t){const r=await Promise.all([s.e(5837),s.e(247)]).then(s.bind(s,30247)),{statisticType:a,outStatisticFieldName:o}=e,{featureAdapter:l,spatialReference:c,geometryType:u,hasZ:d,hasM:h}=this,f=t.map((e=>(0,g.Op)(u,d,h,l.getGeometry(e)))),p=r.convexHull(c,f,!0)[0],m={aggregateGeometries:null,outStatisticFieldName:null};if("EnvelopeAggregate"===a){const e=p?(0,n._w)(p):(0,n.aO)(r.union(c,f));m.aggregateGeometries={...e,spatialReference:c},m.outStatisticFieldName=o||"extent"}else if("CentroidAggregate"===a){const e=p?(0,i.tO)(p):(0,i.$G)((0,n.aO)(r.union(c,f)));m.aggregateGeometries={x:e[0],y:e[1],spatialReference:c},m.outStatisticFieldName=o||"centroid"}else"ConvexHullAggregate"===a&&(m.aggregateGeometries=p,m.outStatisticFieldName=o||"convexHull");return m}_getStatisticValue(e,t,s,r){const{onStatisticField:i,statisticType:n}=e;let a=null;return a=s?.has(i)?s.get(i):(0,y.qN)(this.fieldsIndex.get(i))?(0,c.H0)({values:t,returnDistinct:r}):(0,c.i5)({values:r?[...new Set(t)]:t,minValue:null,maxValue:null,useSampleStdDev:!0}),s&&s.set(i,a),a["var"===n?"variance":n]}_getPercentileValue(e,t){const{onStatisticField:s,statisticParameters:r,statisticType:i}=e,{value:n,orderBy:a}=r,o=this.fieldsIndex.get(s);return(0,c.XL)(t,{value:n,orderBy:a,fieldType:o?.type,isDiscrete:"percentile_disc"===i})}_getAttributeValues(e,t,s){if(s.has(t))return s.get(t);const r=this.fieldsIndex.get(t),i=this.items.map((s=>e.getFieldValue(s,t,r)));return s.set(t,i),i}_getAttributeDataValues(e,t){return this.items.map((s=>e.getDataValue(s,{field:t.field,field2:t.field2,field3:t.field3,fieldDelimiter:t.fieldDelimiter,normalizationField:t.normalizationField,normalizationType:t.normalizationType,normalizationTotal:t.normalizationTotal})))}async _getAttributeExpressionValues(e,t,s){const{arcadeUtils:r}=await(0,x.LC)(),i=r.createFunction(t),n=s&&r.getViewInfo(s);return e.getExpressionValues(this.items,i,n,r)}_calculateUniqueValues(e,t){const s={},r=this.items,i=r.length;for(let n=0;n<i;n++){const i=r[n],a=[];for(const t of e)a.push(t[n]);const o=a.join(",");null==s[o]?s[o]={count:1,data:a,items:[i],itemPositions:[n]}:(t||s[o].count++,s[o].items.push(i),s[o].itemPositions.push(n))}return s}async _getDataValues(e){const t=new u(this.query,this.featureAdapter,this.fieldsIndex),{valueExpression:s,field:r,normalizationField:i,normalizationType:n,normalizationTotal:a,scale:o}=e,l=s?{viewingMode:"map",scale:o,spatialReference:this.query.outSR||this.spatialReference}:null;return s?this._getAttributeExpressionValues(t,s,l):this._getAttributeDataValues(t,{field:r,field2:e.field2,field3:e.field3,fieldDelimiter:e.fieldDelimiter,normalizationField:i,normalizationType:n,normalizationTotal:a})}}function b(e,t,s,r){const i=r.x-s.x,n=r.y-s.y,a=i*i+n*n,o=(t.x-s.x)*i+(t.y-s.y)*n,l=Math.min(1,Math.max(0,o/a));e.x=s.x+i*l,e.y=s.y+n*l}function v(e,t){return e?t?4:3:t?3:2}(m=p||(p={}))[m.NONE=0]="NONE",m[m.EDGE=1]="EDGE",m[m.VERTEX=2]="VERTEX";class T{constructor(e,t){this.coords=e,this.coordsIndex=t}get x(){return this.coords[this.coordsIndex]}get y(){return this.coords[this.coordsIndex+1]}get z(){return this.coords[this.coordsIndex+2]}}},62432:(e,t,s)=>{s.d(t,{nu:()=>m,hr:()=>p,Jc:()=>h,G3:()=>g,Of:()=>f,z4:()=>d,hO:()=>u});var r=s(20102),i=s(74669),n=s(41534);const a=new class{constructor(e,t){this._cache=new i.Z(e),this._invalidCache=new i.Z(t)}get(e,t){const s=`${t.uid}:${e}`,r=this._cache.get(s);if(r)return r;if(void 0!==this._invalidCache.get(s))return null;try{const r=n.WhereClause.create(e,t);return this._cache.put(s,r),r}catch{return this._invalidCache.put(s,null),null}}}(50,500),o="feature-store:unsupported-query",l=" as ",c=new Set(["esriFieldTypeOID","esriFieldTypeSmallInteger","esriFieldTypeInteger","esriFieldTypeSingle","esriFieldTypeDouble","esriFieldTypeLong","esriFieldTypeDate"]);function u(e,t){if(!t)return!0;const s=a.get(t,e);if(!s)throw new r.Z(o,"invalid SQL expression",{where:t});if(!s.isStandardized)throw new r.Z(o,"where clause is not standard",{where:t});return f(e,s.fieldNames,"where clause contains missing fields"),!0}function d(e,t,s){if(!t)return!0;const i=a.get(t,e);if(!i)throw new r.Z(o,"invalid SQL expression",{having:t});if(!i.isAggregate)throw new r.Z(o,"having does not contain a valid aggregate function",{having:t});const n=i.fieldNames;if(f(e,n,"having contains missing fields"),!i.getExpressions().every((t=>{const{aggregateType:r,field:i}=t,n=e.get(i)?.name;return s.some((t=>{const{onStatisticField:s,statisticType:i}=t,a=e.get(s)?.name;return a===n&&i.toLowerCase().trim()===r}))})))throw new r.Z(o,"expressions in having should also exist in outStatistics",{having:t});return!0}function h(e,t){return e?a.get(e,t):null}function f(e,t,s,i=!0){const n=[];for(const s of t)if("*"!==s&&!e.has(s))if(i){const t=p(s);try{const s=h(t,e);if(!s)throw new r.Z(o,"invalid SQL expression",{where:t});if(!s.isStandardized)throw new r.Z(o,"expression is not standard",{clause:s});f(e,s.fieldNames,"expression contains missing fields")}catch(e){const t=e&&e.details;if(t&&(t.clause||t.where))throw e;t&&t.missingFields?n.push(...t.missingFields):n.push(s)}}else n.push(s);if(n.length)throw new r.Z(o,s,{missingFields:n})}function p(e){return e.split(l)[0]}function m(e){return e.split(l)[1]}function g(e,t){const s=t.get(e);return!!s&&!c.has(s.type)}},37427:(e,t,s)=>{s.d(t,{_W:()=>h,iV:()=>m,oj:()=>y});var r=s(70586),i=s(44547),n=s(37455),a=s(8744),o=s(40488);const l=[0,0];function c(e,t){if(!t)return null;if("x"in t){const s={x:0,y:0};return[s.x,s.y]=e(t.x,t.y,l),null!=t.z&&(s.z=t.z),null!=t.m&&(s.m=t.m),s}if("xmin"in t){const s={xmin:0,ymin:0,xmax:0,ymax:0};return[s.xmin,s.ymin]=e(t.xmin,t.ymin,l),[s.xmax,s.ymax]=e(t.xmax,t.ymax,l),t.hasZ&&(s.zmin=t.zmin,s.zmax=t.zmax,s.hasZ=!0),t.hasM&&(s.mmin=t.mmin,s.mmax=t.mmax,s.hasM=!0),s}return"rings"in t?{rings:u(t.rings,e),hasM:t.hasM,hasZ:t.hasZ}:"paths"in t?{paths:u(t.paths,e),hasM:t.hasM,hasZ:t.hasZ}:"points"in t?{points:d(t.points,e),hasM:t.hasM,hasZ:t.hasZ}:null}function u(e,t){const s=[];for(const r of e)s.push(d(r,t));return s}function d(e,t){const s=[];for(const r of e){const e=t(r[0],r[1],[0,0]);s.push(e),r.length>2&&e.push(r[2]),r.length>3&&e.push(r[3])}return s}async function h(e,t){if(!e||!t)return;const s=Array.isArray(e)?e.map((e=>(0,r.pC)(e.geometry)?e.geometry.spatialReference:null)).filter(r.pC):[e];await(0,i.iQ)(s.map((e=>({source:e,dest:t}))))}const f=c.bind(null,o.hG),p=c.bind(null,o.R6);function m(e,t,s,r){if(!e)return e;if(s||(s=t,t=e.spatialReference),!(0,a.JY)(t)||!(0,a.JY)(s)||(0,a.fS)(t,s))return e;if((0,o.Q8)(t,s)){const t=(0,a.sS)(s)?f(e):p(e);return t.spatialReference=s,t}return(0,i.oj)(n.N,[e],t,s,null,r)[0]}const g=new class{constructor(){this._jobs=[],this._timer=null,this._process=this._process.bind(this)}async push(e,t,s){if(!e||!e.length||!t||!s||(0,a.fS)(t,s))return e;const r={geometries:e,inSpatialReference:t,outSpatialReference:s,resolve:null};return this._jobs.push(r),new Promise((e=>{r.resolve=e,null===this._timer&&(this._timer=setTimeout(this._process,10))}))}_process(){this._timer=null;const e=this._jobs.shift();if(!e)return;const{geometries:t,inSpatialReference:s,outSpatialReference:r,resolve:l}=e;(0,o.Q8)(s,r)?(0,a.sS)(r)?l(t.map(f)):l(t.map(p)):l((0,i.oj)(n.N,t,s,r,null,null)),this._jobs.length>0&&(this._timer=setTimeout(this._process,10))}};function y(e,t,s){return g.push(e,t,s)}},11490:(e,t,s)=>{s.d(t,{EG:()=>b,Op:()=>v,S2:()=>I,Up:()=>T,j6:()=>S,vF:()=>m});var r=s(35454),i=s(70586),n=s(67900),a=s(44547),o=s(49600),l=s(33955),c=s(16306),u=s(8744),d=s(98732),h=s(5428),f=s(37427);const p=new r.X({esriSRUnit_Meter:"meters",esriSRUnit_Kilometer:"kilometers",esriSRUnit_Foot:"feet",esriSRUnit_StatuteMile:"miles",esriSRUnit_NauticalMile:"nautical-miles",esriSRUnit_USNauticalMile:"us-nautical-miles"}),m=Object.freeze({}),g=new h.Z,y=new h.Z,x=new h.Z,w={esriGeometryPoint:d.fQ,esriGeometryPolyline:d.J6,esriGeometryPolygon:d.eG,esriGeometryMultipoint:d.Iv};function b(e,t,s,r=e.hasZ,n=e.hasM){if((0,i.Wi)(t))return null;const a=e.hasZ&&r,o=e.hasM&&n;if(s){const i=(0,d.Nh)(x,t,e.hasZ,e.hasM,"esriGeometryPoint",s,r,n);return(0,d.fQ)(i,a,o)}return(0,d.fQ)(t,a,o)}function v(e,t,s,r,n,a,o=t,l=s){const c=t&&o,u=s&&l,h=(0,i.pC)(r)?"coords"in r?r:r.geometry:null;if((0,i.Wi)(h))return null;if(n){let r=(0,d.zj)(y,h,t,s,e,n,o,l);return a&&(r=(0,d.Nh)(x,r,c,u,e,a)),w[e]?.(r,c,u)??null}if(a){const r=(0,d.Nh)(x,h,t,s,e,a,o,l);return w[e]?.(r,c,u)??null}return(0,d.hY)(g,h,t,s,o,l),w[e]?.(g,c,u)??null}async function T(e,t,s){const{outFields:r,orderByFields:i,groupByFieldsForStatistics:n,outStatistics:a}=e;if(r)for(let e=0;e<r.length;e++)r[e]=r[e].trim();if(i)for(let e=0;e<i.length;e++)i[e]=i[e].trim();if(n)for(let e=0;e<n.length;e++)n[e]=n[e].trim();if(a)for(let e=0;e<a.length;e++)a[e].onStatisticField&&(a[e].onStatisticField=a[e].onStatisticField.trim());return e.geometry&&!e.outSR&&(e.outSR=e.geometry.spatialReference),S(e,t,s)}async function S(e,t,r){if(!e)return null;let{where:a}=e;if(e.where=a=a&&a.trim(),(!a||/^1 *= *1$/.test(a)||t&&t===a)&&(e.where=null),!e.geometry)return e;let d=await async function(e){const{distance:t,units:r}=e,i=e.geometry;if(null==t||"vertexAttributes"in i)return i;const a=i.spatialReference,o=r?p.fromJSON(r):(0,n.qE)(a),l=a&&((0,u.sT)(a)||(0,u.sS)(a))?i:await(0,f._W)(a,u.Zn).then((()=>(0,f.iV)(i,u.Zn)));return(await async function(){return(await Promise.all([s.e(5837),s.e(247)]).then(s.bind(s,30247))).geodesicBuffer}())(l.spatialReference,l,t,o)}(e);if(e.distance=0,e.units=null,"esriSpatialRelEnvelopeIntersects"===e.spatialRel){const{spatialReference:t}=e.geometry;d=(0,o.aO)(d),d.spatialReference=t}if(d){await(0,f._W)(d.spatialReference,r),d=function(e,t){const s=e.spatialReference;return F(e,t)&&(0,l.YX)(e)?{spatialReference:s,rings:[[[e.xmin,e.ymin],[e.xmin,e.ymax],[e.xmax,e.ymax],[e.xmax,e.ymin],[e.xmin,e.ymin]]]}:e}(d,r);const t=(await(0,c.aX)((0,l.im)(d)))[0];if((0,i.Wi)(t))throw m;const s="quantizationParameters"in e&&e.quantizationParameters?.tolerance||"maxAllowableOffset"in e&&e.maxAllowableOffset||0,n=s&&F(d,r)?{densificationStep:8*s}:void 0,a=t.toJSON(),o=await(0,f.iV)(a,a.spatialReference,r,n);if(!o)throw m;o.spatialReference=r,e.geometry=o}return e}function F(e,t){if(!e)return!1;const s=e.spatialReference;return((0,l.YX)(e)||(0,l.oU)(e)||(0,l.l9)(e))&&!(0,u.fS)(s,t)&&!(0,a.Up)(s,t)}function I(e){return e&&C in e?JSON.parse(JSON.stringify(e,E)):e}const C="_geVersion",E=(e,t)=>e!==C?t:void 0},3172:(e,t,s)=>{s.r(t),s.d(t,{default:()=>p});var r=s(68773),i=s(40330),n=s(20102),a=s(80442),o=s(22974),l=s(70586),c=s(95330),u=s(17452),d=s(19745),h=s(71058),f=s(85958);async function p(e,t){const o=(0,u.HK)(e),d=(0,u.jc)(e);d||o||(e=(0,u.Fv)(e));const x={url:e,requestOptions:{...(0,l.Wg)(t)}};let w=(0,u.oh)(e);if(w){const e=await async function(e,t){if(null!=e.responseData)return e.responseData;if(e.headers&&(t.requestOptions.headers={...t.requestOptions.headers,...e.headers}),e.query&&(t.requestOptions.query={...t.requestOptions.query,...e.query}),e.before){let s,r;try{r=await e.before(t)}catch(e){s=F("request:interceptor",e,t)}if((r instanceof Error||r instanceof n.Z)&&(s=F("request:interceptor",r,t)),s)throw e.error&&e.error(s),s;return r}}(w,x);if(null!=e)return{data:e,getHeader:v,httpStatus:200,requestOptions:x.requestOptions,url:x.url};w.after||w.error||(w=null)}if(e=x.url,"image"===(t=x.requestOptions).responseType){if((0,a.Z)("host-webworker")||(0,a.Z)("host-node"))throw F("request:invalid-parameters",new Error("responseType 'image' is not supported in Web Workers or Node environment"),x)}else if(o)throw F("request:invalid-parameters",new Error("Data URLs are not supported for responseType = "+t.responseType),x);if("head"===t.method){if(t.body)throw F("request:invalid-parameters",new Error("body parameter cannot be set when method is 'head'"),x);if(o||d)throw F("request:invalid-parameters",new Error("data and blob URLs are not supported for method 'head'"),x)}if(await async function(){(0,a.Z)("host-webworker")?m||(m=await s.e(9884).then(s.bind(s,29884))):p._abortableFetch||(p._abortableFetch=globalThis.fetch.bind(globalThis))}(),m)return m.execute(e,t);const b=new AbortController;(0,c.fu)(t,(()=>b.abort()));const T={controller:b,credential:void 0,credentialToken:void 0,fetchOptions:void 0,hasToken:!1,interceptor:w,params:x,redoRequest:!1,useIdentity:g.useIdentity,useProxy:!1,useSSL:!1,withCredentials:!1},S=await async function(e){let t,s;await async function(e){const t=e.params.url,s=e.params.requestOptions,n=e.controller.signal,a=s.body;let o=null,l=null;if(y&&"HTMLFormElement"in globalThis&&(a instanceof FormData?o=a:a instanceof HTMLFormElement&&(o=new FormData(a))),"string"==typeof a&&(l=a),e.fetchOptions={cache:s.cacheBust&&!p._abortableFetch.polyfill?"no-cache":"default",credentials:"same-origin",headers:s.headers||{},method:"head"===s.method?"HEAD":"GET",mode:"cors",priority:g.priority,redirect:"follow",signal:n},(o||l)&&(e.fetchOptions.body=o||l),"anonymous"===s.authMode&&(e.useIdentity=!1),e.hasToken=!!(/token=/i.test(t)||s.query?.token||o?.get("token")),!e.hasToken&&r.Z.apiKey&&(0,h.r)(t)&&(s.query||(s.query={}),s.query.token=r.Z.apiKey,e.hasToken=!0),e.useIdentity&&!e.hasToken&&!e.credentialToken&&!C(t)&&!(0,c.Hc)(n)){let r;"immediate"===s.authMode?(await I(),r=await i.id.getCredential(t,{signal:n}),e.credential=r):"no-prompt"===s.authMode?(await I(),r=await i.id.getCredential(t,{prompt:!1,signal:n}).catch((()=>{})),e.credential=r):i.id&&(r=i.id.findCredential(t)),r&&(e.credentialToken=r.token,e.useSSL=!!r.ssl)}}(e);try{do{[t,s]=await E(e)}while(!await M(e,t,s))}catch(s){const r=F("request:server",s,e.params,t);throw r.details.ssl=e.useSSL,e.interceptor&&e.interceptor.error&&e.interceptor.error(r),r}const n=e.params.url;if(s&&/\/sharing\/rest\/(accounts|portals)\/self/i.test(n)){if(!e.hasToken&&!e.credentialToken&&s.user?.username&&!(0,u.kl)(n)){const e=(0,u.P$)(n,!0);e&&g.trustedServers.push(e)}Array.isArray(s.authorizedCrossOriginNoCorsDomains)&&(0,f.Hu)(s.authorizedCrossOriginNoCorsDomains)}const a=e.credential;if(a&&i.id){const e=i.id.findServerInfo(a.server);let t=e&&e.owningSystemUrl;if(t){t=t.replace(/\/?$/,"/sharing");const e=i.id.findCredential(t,a.userId);e&&-1===i.id._getIdenticalSvcIdx(t,e)&&e.resources.unshift(t)}}return{data:s,getHeader:t?e=>t?.headers.get(e):v,httpStatus:t?.status??200,requestOptions:e.params.requestOptions,ssl:e.useSSL,url:e.params.url}}(T);return w?.after?.(S),S}let m;const g=r.Z.request,y="FormData"in globalThis,x=[499,498,403,401],w=["COM_0056","COM_0057","SB_0008"],b=[/\/arcgis\/tokens/i,/\/sharing(\/rest)?\/generatetoken/i,/\/rest\/info/i],v=()=>null,T=Symbol();function S(e){const t=(0,u.P$)(e);return!t||t.endsWith(".arcgis.com")||p._corsServers.includes(t)||(0,u.kl)(t)}function F(e,t,s,r){let i="Error";const a={url:s.url,requestOptions:s.requestOptions,getHeader:v,ssl:!1};if(t instanceof n.Z)return t.details?(t.details=(0,o.d9)(t.details),t.details.url=s.url,t.details.requestOptions=s.requestOptions):t.details=a,t;if(t){const e=r&&(e=>r.headers.get(e)),s=r&&r.status,n=t.message;n&&(i=n),e&&(a.getHeader=e),a.httpStatus=(null!=t.httpCode?t.httpCode:t.code)||s||0,a.subCode=t.subcode,a.messageCode=t.messageCode,"string"==typeof t.details?a.messages=[t.details]:a.messages=t.details,a.raw=T in t?t[T]:t}return(0,c.D_)(t)?(0,c.zE)():new n.Z(e,i,a)}async function I(){i.id||await Promise.all([s.e(6261),s.e(1400),s.e(450)]).then(s.bind(s,73660))}function C(e){return b.some((t=>t.test(e)))}async function E(e){let t=e.params.url;const s=e.params.requestOptions,r=e.fetchOptions??{},n=(0,u.jc)(t)||(0,u.HK)(t),o=s.responseType||"json",l=n?0:null!=s.timeout?s.timeout:g.timeout;let h=!1;if(!n){e.useSSL&&(t=(0,u.hO)(t)),s.cacheBust&&"default"===r.cache&&(t=(0,u.ZN)(t,"request.preventCache",Date.now()));let n={...s.query};e.credentialToken&&(n.token=e.credentialToken);let o=(0,u.B7)(n);(0,a.Z)("esri-url-encodes-apostrophe")&&(o=o.replace(/'/g,"%27"));const l=t.length+1+o.length;let c;h="delete"===s.method||"post"===s.method||"put"===s.method||!!s.body||l>g.maxUrlLength;const p=s.useProxy||!!(0,u.ed)(t);if(p){const e=(0,u.b7)(t);c=e.path,!h&&c.length+1+l>g.maxUrlLength&&(h=!0),e.query&&(n={...e.query,...n})}if("HEAD"===r.method&&(h||p)){if(h){if(l>g.maxUrlLength)throw F("request:invalid-parameters",new Error("URL exceeds maximum length"),e.params);throw F("request:invalid-parameters",new Error("cannot use POST request when method is 'head'"),e.params)}if(p)throw F("request:invalid-parameters",new Error("cannot use proxy when method is 'head'"),e.params)}if(h?(r.method="delete"===s.method?"DELETE":"put"===s.method?"PUT":"POST",s.body?t=(0,u.fl)(t,n):(r.body=(0,u.B7)(n),r.headers||(r.headers={}),r.headers["Content-Type"]="application/x-www-form-urlencoded")):t=(0,u.fl)(t,n),p&&(e.useProxy=!0,t=`${c}?${t}`),n.token&&y&&r.body instanceof FormData&&!(0,d.P)(t)&&r.body.set("token",n.token),s.hasOwnProperty("withCredentials"))e.withCredentials=s.withCredentials;else if(!(0,u.D6)(t,(0,u.TI)()))if((0,u.kl)(t))e.withCredentials=!0;else if(i.id){const s=i.id.findServerInfo(t);s&&s.webTierAuth&&(e.withCredentials=!0)}e.withCredentials&&(r.credentials="include",(0,f.jH)(t)&&await(0,f.jz)(h?(0,u.fl)(t,n):t))}let m,x,w=0,b=!1;l>0&&(w=setTimeout((()=>{b=!0,e.controller.abort()}),l));try{if("native-request-init"===s.responseType)x=r,x.url=t;else if("image"!==s.responseType||"default"!==r.cache||"GET"!==r.method||h||function(e){if(e)for(const t of Object.getOwnPropertyNames(e))if(e[t])return!0;return!1}(s.headers)||!n&&!e.useProxy&&g.proxyUrl&&!S(t)){if(m=await p._abortableFetch(t,r),e.useProxy||function(e){const t=(0,u.P$)(e);t&&!p._corsServers.includes(t)&&p._corsServers.push(t)}(t),"native"===s.responseType)x=m;else if("HEAD"!==r.method)if(m.ok){switch(o){case"array-buffer":x=await m.arrayBuffer();break;case"blob":case"image":x=await m.blob();break;default:x=await m.text()}if(w&&(clearTimeout(w),w=0),"json"===o||"xml"===o||"document"===o)if(x)switch(o){case"json":x=JSON.parse(x);break;case"xml":x=_(x,"application/xml");break;case"document":x=_(x,"text/html")}else x=null;if(x){if("array-buffer"===o||"blob"===o){const e=m.headers.get("Content-Type");if(e&&/application\/json|text\/plain/i.test(e)&&x["blob"===o?"size":"byteLength"]<=750)try{const e=await new Response(x).json();e.error&&(x=e)}catch{}}"image"===o&&x instanceof Blob&&(x=await R(URL.createObjectURL(x),e,!0))}}else x=await m.text()}else x=await R(t,e)}catch(r){if("AbortError"===r.name){if(b)throw new Error("Timeout exceeded");throw(0,c.zE)("Request canceled")}if(!(!m&&r instanceof TypeError&&g.proxyUrl)||s.body||"delete"===s.method||"head"===s.method||"post"===s.method||"put"===s.method||e.useProxy||S(t))throw r;e.redoRequest=!0,(0,u.tD)({proxyUrl:g.proxyUrl,urlPrefix:(0,u.P$)(t)??""})}finally{w&&clearTimeout(w)}return[m,x]}function _(e,t){let s;try{s=(new DOMParser).parseFromString(e,t)}catch{}if(!s||s.getElementsByTagName("parsererror").length)throw new SyntaxError("XML Parse error");return s}async function M(e,t,s){if(e.redoRequest)return e.redoRequest=!1,!1;const r=e.params.requestOptions;if(!t||"native"===r.responseType||"native-request-init"===r.responseType)return!0;let n,a;if(!t.ok)throw n=new Error(`Unable to load ${t.url} status: ${t.status}`),n[T]=s,n;s&&(s.error?n=s.error:"error"===s.status&&Array.isArray(s.messages)&&(n={...s},n[T]=s,n.details=s.messages));let o,l=null;n&&(a=Number(n.code),l=n.hasOwnProperty("subcode")?Number(n.subcode):null,o=n.messageCode,o=o&&o.toUpperCase());const c=r.authMode;if(403===a&&(4===l||n.message&&n.message.toLowerCase().includes("ssl")&&!n.message.toLowerCase().includes("permission"))){if(!e.useSSL)return e.useSSL=!0,!1}else if(!e.hasToken&&e.useIdentity&&("no-prompt"!==c||498===a)&&void 0!==a&&x.includes(a)&&!C(e.params.url)&&(403!==a||o&&!w.includes(o)&&(null==l||2===l&&e.credentialToken))){await I();try{const t=await i.id.getCredential(e.params.url,{error:F("request:server",n,e.params),prompt:"no-prompt"!==c,signal:e.controller.signal,token:e.credentialToken});return e.credential=t,e.credentialToken=t.token,e.useSSL=e.useSSL||t.ssl,!1}catch(t){if("no-prompt"===c)return e.credential=void 0,e.credentialToken=void 0,!1;n=t}}if(n)throw n;return!0}function R(e,t,s=!1){const r=t.controller.signal,i=new Image;return t.withCredentials?i.crossOrigin="use-credentials":i.crossOrigin="anonymous",i.alt="",i.fetchPriority=g.priority,i.src=e,(0,f.fY)(i,e,s,r)}p._abortableFetch=null,p._corsServers=["https://server.arcgisonline.com","https://services.arcgisonline.com"]},71058:(e,t,s)=>{s.d(t,{r:()=>n});var r=s(17452);const i=["elevation3d.arcgis.com","js.arcgis.com","jsdev.arcgis.com","jsqa.arcgis.com","static.arcgis.com"];function n(e){const t=(0,r.P$)(e,!0);return!!t&&t.endsWith(".arcgis.com")&&!i.includes(t)&&!e.endsWith("/sharing/rest/generateToken")}},85958:(e,t,s)=>{s.d(t,{Hu:()=>u,fY:()=>l,jH:()=>d,jz:()=>h});var r=s(68773),i=s(80442),n=s(70586),a=s(95330),o=s(17452);function l(e,t,s=!1,r){return new Promise(((o,l)=>{if((0,a.Hc)(r))return void l(c());let u=()=>{f(),l(new Error(`Unable to load ${t}`))},d=()=>{const t=e;f(),o(t)},h=()=>{if(!e)return;const t=e;f(),t.src="",l(c())};const f=()=>{(0,i.Z)("esri-image-decode")||(e.removeEventListener("error",u),e.removeEventListener("load",d)),u=null,d=null,e=null,(0,n.pC)(r)&&r.removeEventListener("abort",h),h=null,s&&URL.revokeObjectURL(t)};(0,n.pC)(r)&&r.addEventListener("abort",h),(0,i.Z)("esri-image-decode")?e.decode().then(d,u):(e.addEventListener("error",u),e.addEventListener("load",d))}))}function c(){try{return new DOMException("Aborted","AbortError")}catch{const e=new Error;return e.name="AbortError",e}}function u(e){r.Z.request.crossOriginNoCorsDomains||(r.Z.request.crossOriginNoCorsDomains={});const t=r.Z.request.crossOriginNoCorsDomains;for(let s of e)s=s.toLowerCase(),/^https?:\/\//.test(s)?t[(0,o.P$)(s)??""]=0:(t[(0,o.P$)("http://"+s)??""]=0,t[(0,o.P$)("https://"+s)??""]=0)}function d(e){const t=r.Z.request.crossOriginNoCorsDomains;if(t){let s=(0,o.P$)(e);if(s)return s=s.toLowerCase(),!(0,o.D6)(s,(0,o.TI)())&&t[s]<Date.now()-36e5}return!1}async function h(e){const t=r.Z.request.crossOriginNoCorsDomains,s=(0,o.P$)(e);t&&s&&(t[s.toLowerCase()]=Date.now());const i=(0,o.mN)(e);e=i.path,"json"===i.query?.f&&(e+="?f=json");try{await fetch(e,{mode:"no-cors",credentials:"include"})}catch{}}}}]);