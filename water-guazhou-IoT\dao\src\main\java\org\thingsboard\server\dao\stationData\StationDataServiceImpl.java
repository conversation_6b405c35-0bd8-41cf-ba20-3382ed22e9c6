package org.thingsboard.server.dao.stationData;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.Utils.DateUtils;
import org.thingsboard.server.common.data.device.DeviceFullData;
import org.thingsboard.server.common.data.exception.ThingsboardErrorCode;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.DeviceId;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.client.StationFeignClient;
import org.thingsboard.server.dao.device.DeviceService;
import org.thingsboard.server.dao.model.VO.DeviceDataTableInfoVO;
import org.thingsboard.server.dao.model.VO.DynamicTableVO;
import org.thingsboard.server.dao.model.VO.LineChartDataVO;
import org.thingsboard.server.dao.model.sql.StationAttrEntity;
import org.thingsboard.server.dao.model.sql.StationEntity;
import org.thingsboard.server.dao.obtain.BaseObtainDataService;
import org.thingsboard.server.dao.util.DeviceTableInfoUtil;
import org.thingsboard.server.dao.util.StationDataUtil;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class StationDataServiceImpl implements StationDataService {

    @Autowired
    private StationFeignClient stationFeignClient;

    @Autowired
    private DeviceService deviceService;

    @Autowired
    private BaseObtainDataService obtainDataService;

    @Override
    public List<DeviceFullData> getStationDataDetail(String stationId, String type, boolean customName, TenantId tenantId) {
        List<StationAttrEntity> stationAttrList = new ArrayList<>();
        if (StringUtils.isNotBlank(type)) {
            try {
                stationAttrList = stationFeignClient.getAttrList(stationId, type);
            } catch (Exception e) {
                log.warn("获取站点属性异常, e = ", e);
            }
        } else {
            try {
                stationAttrList = stationFeignClient.getStationAllAttrList(stationId);
            } catch (Exception e) {
                log.warn("获取站点属性异常, e = ", e);
            }
        }
        if (stationAttrList == null || stationAttrList.isEmpty()) {
            return new ArrayList<>();
        }

        List<String> deviceIdList = stationAttrList.stream().map(StationAttrEntity::getDeviceId).distinct().collect(Collectors.toList());
        // 将查询到的设备数据按设备以及变量名进行分组
        Map<String, Map<String, DeviceFullData>> deviceDataMap = new HashMap<>();
        // 对数据进行处理(按设备ID以及变量名分组, 方便后续查询)
        for (String deviceId : deviceIdList) {
            if (StringUtils.isBlank(deviceId)) {
                continue;
            }
            List<DeviceFullData> deviceFullData = deviceService.getDeviceFullData(tenantId, new DeviceId(UUID.fromString(deviceId)), "");
            if (deviceFullData != null && deviceFullData.size() > 0) {
                Map<String, DeviceFullData> dataMap = new HashMap<>();
                for (DeviceFullData deviceFullDatum : deviceFullData) {
                    String property = deviceFullDatum.getProperty();
                    dataMap.put(property, deviceFullDatum);
                }
                deviceDataMap.put(deviceId, dataMap);
            }
        }

        List<DeviceFullData> result = new ArrayList<>();
        for (StationAttrEntity stationAttr : stationAttrList) {
            String deviceId = stationAttr.getDeviceId();
            String attr = stationAttr.getAttr();
            Map<String, DeviceFullData> fullDataMap = deviceDataMap.get(deviceId);
            if (fullDataMap != null) {
                DeviceFullData deviceFullData = fullDataMap.get(attr);
                if (deviceFullData != null) {
                    DeviceFullData newDeviceFullData = new DeviceFullData();
                    BeanUtils.copyProperties(deviceFullData, newDeviceFullData);
                    newDeviceFullData.setDeviceId(deviceId);
                    if (customName) {
                        newDeviceFullData.setPropertyName(stationAttr.getName());
                    }
                    result.add(newDeviceFullData);
                }
            }
        }

        return result;
    }

    @Override
    public Object findStationDataList(String stationId, String queryType, Date startTime, Date endTime, TenantId tenantId) {
        try {
            /*
             * 查询站点的动态属性数据列表
             * 1. 查询站点的动态属性列表
             * 2. 查询动态属性列表数据
             * 3. 拼装数据返回
             */
            List<StationAttrEntity> attrList = stationFeignClient.getStationAllAttrList(stationId);

            Map<String, StationAttrEntity> sourceIdMap = new HashMap<>();
            List<String> sourceIds = attrList.stream()
                    .map(stationAttrEntity -> {
                        String sourceId = UUIDConverter.fromTimeUUID(UUID.fromString(stationAttrEntity.getDeviceId())) + "." + stationAttrEntity.getAttr();
                        sourceIdMap.put(sourceId, stationAttrEntity);
                        return sourceId;
                    })
                    .collect(Collectors.toList());

            LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> data = null;
            if ("none".equals(queryType)) {// 直接返回原始数据
                data = obtainDataService.getDeviceOriginalData(sourceIds, startTime.getTime(), endTime.getTime(), null, tenantId);
            } else {// 降采样查询站点数据
                data = obtainDataService.getDeviceData(sourceIds, startTime.getTime(), endTime.getTime(), queryType, null, tenantId);
            }

            List<JSONObject> resultList = new ArrayList<>();
            for (Map.Entry<String, LinkedHashMap<String, BigDecimal>> entry : data.entrySet()) {
                String key = entry.getKey();
                LinkedHashMap<String, BigDecimal> dataMap = entry.getValue();

                JSONObject dataObj = new JSONObject();
                dataObj.put("date", key);
                for (String sourceId : sourceIds) {
                    String[] attrs = sourceId.split("\\.");
                    String attr = attrs[1];

                    StationAttrEntity stationAttr = sourceIdMap.get(sourceId);

                    BigDecimal value = dataMap.get(sourceId);
                    dataObj.put(attr, value);
                }

                resultList.add(dataObj);
            }

            return resultList;
        } catch (ThingsboardException e) {
            e.printStackTrace();
        }

        return new ArrayList<>();
    }

    @Override
    public Object getStationDataDetailList(String stationType, String projectId, TenantId tenantId) {
        // 查询指定类型的站点列表
        PageData<StationEntity> stationEntityPageData = stationFeignClient.list(1, 99999, stationType, projectId);
        List<StationEntity> stationList = stationEntityPageData.getData();

        if (stationList == null) {
            return new ArrayList<>();
        }
        Map<String, List<DeviceFullData>> stationDataMap = new HashMap<>();
        for (StationEntity station : stationList) {
            String stationId = station.getId();

            List<StationAttrEntity> stationAttrList = stationFeignClient.getStationAllAttrList(stationId);
            if (stationAttrList == null || stationAttrList.isEmpty()) {
                continue;
            }

            List<String> deviceIdList = stationAttrList.stream().map(StationAttrEntity::getDeviceId).distinct().collect(Collectors.toList());
            // 将查询到的设备数据按设备以及变量名进行分组
            Map<String, Map<String, DeviceFullData>> deviceDataMap = new HashMap<>();
            // 对数据进行处理(按设备ID以及变量名分组, 方便后续查询)
            for (String deviceId : deviceIdList) {
                if (StringUtils.isBlank(deviceId)) {
                    continue;
                }
                List<DeviceFullData> deviceFullData = deviceService.getDeviceFullData(tenantId, new DeviceId(UUID.fromString(deviceId)), "");
                if (deviceFullData != null && deviceFullData.size() > 0) {
                    Map<String, DeviceFullData> dataMap = new HashMap<>();
                    for (DeviceFullData deviceFullDatum : deviceFullData) {
                        String property = deviceFullDatum.getProperty();
                        dataMap.put(property, deviceFullDatum);
                    }
                    deviceDataMap.put(deviceId, dataMap);
                }
            }

            List<DeviceFullData> stationDataList = new ArrayList<>();
            for (StationAttrEntity stationAttr : stationAttrList) {
                String deviceId = stationAttr.getDeviceId();
                String attr = stationAttr.getAttr();
                Map<String, DeviceFullData> fullDataMap = deviceDataMap.get(deviceId);
                if (fullDataMap != null) {
                    DeviceFullData deviceFullData = fullDataMap.get(attr);
                    if (deviceFullData != null) {
                        deviceFullData.setDeviceId(deviceId);
                        stationDataList.add(deviceFullData);
                    }
                }
            }

            stationDataMap.put(stationId, stationDataList);
        }

        // 拼装站点数据并返回
        List<JSONObject> result = new ArrayList<>();
        for (StationEntity station : stationList) {

            long time = -1;
            List<DeviceFullData> deviceFullData = stationDataMap.get(station.getId());

            JSONObject data = new JSONObject();
            data.put("name", station.getName());
            data.put("stationId", station.getId());
            data.put("location", station.getLocation());
            if (deviceFullData != null && deviceFullData.size() > 0) {
                for (DeviceFullData deviceFullDatum : deviceFullData) {
                    String property = deviceFullDatum.getProperty();
                    data.put(property, deviceFullDatum.getValue());
                    long collectionTime = deviceFullDatum.getCollectionTime();
                    if (collectionTime > time) {
                        data.put("time", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date(collectionTime)));
                        time = collectionTime;
                    }
                    if (!deviceFullDatum.isStatus()) {
                        data.put(property + "_status", "offline");
                    } else {
                        String alarmLevel = deviceFullDatum.getAlarmLevel();
                        if (alarmLevel == null) {
                            alarmLevel = "true";
                        }
                        data.put(property + "_status", alarmLevel);
                    }
                    String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(deviceFullDatum.getDeviceId()));
                    data.put(property + "_deviceId", deviceId);
                    data.put(property + "_unit", deviceFullDatum.getUnit());
                    data.put("unit", deviceFullDatum.getUnit());
                }
            }
            result.add(data);
        }

        return result;
    }

    @Override
    public Object getStationDataDetailListView(String stationType, boolean customName, String projectId, TenantId tenantId) {
        // 查询指定类型的站点列表
        PageData<StationEntity> stationEntityPageData = stationFeignClient.list(1, 99999, stationType, projectId);
        List<StationEntity> stationList = stationEntityPageData.getData();

        if (stationList == null) {
            return new ArrayList<>();
        }
        Map<String, List<DeviceFullData>> stationDataMap = new HashMap<>();
        for (StationEntity station : stationList) {
            try {
                String stationId = station.getId();

                List<StationAttrEntity> stationAttrList = stationFeignClient.getStationAllAttrList(stationId);
                if (stationAttrList == null || stationAttrList.isEmpty()) {
                    continue;
                }

                List<String> deviceIdList = stationAttrList.stream().map(StationAttrEntity::getDeviceId).distinct().collect(Collectors.toList());
                // 将查询到的设备数据按设备以及变量名进行分组
                Map<String, Map<String, DeviceFullData>> deviceDataMap = new HashMap<>();
                // 对数据进行处理(按设备ID以及变量名分组, 方便后续查询)
                for (String deviceId : deviceIdList) {
                    if (StringUtils.isBlank(deviceId)) {
                        continue;
                    }
                    List<DeviceFullData> deviceFullData = deviceService.getDeviceFullData(tenantId, new DeviceId(UUID.fromString(deviceId)), "");
                    if (deviceFullData != null && deviceFullData.size() > 0) {
                        Map<String, DeviceFullData> dataMap = new HashMap<>();
                        for (DeviceFullData deviceFullDatum : deviceFullData) {
                            String property = deviceFullDatum.getProperty();
                            dataMap.put(property, deviceFullDatum);
                        }
                        deviceDataMap.put(deviceId, dataMap);
                    }
                }

                List<DeviceFullData> stationDataList = new ArrayList<>();
                for (StationAttrEntity stationAttr : stationAttrList) {
                    String deviceId = stationAttr.getDeviceId();
                    String attr = stationAttr.getAttr();
                    Map<String, DeviceFullData> fullDataMap = deviceDataMap.get(deviceId);
                    if (fullDataMap != null) {
                        DeviceFullData deviceFullData = fullDataMap.get(attr);
                        if (deviceFullData != null) {
                            deviceFullData.setDeviceId(deviceId);
                            if (customName) {
                                deviceFullData.setPropertyName(stationAttr.getName());
                            }
                            stationDataList.add(deviceFullData);
                        }
                    }
                }

                stationDataMap.put(stationId, stationDataList);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        // 拼装站点数据并返回
        List<JSONObject> result = new ArrayList<>();
        for (StationEntity station : stationList) {

            try {
                List<DeviceFullData> deviceFullData = stationDataMap.get(station.getId());

                JSONObject data = new JSONObject();
                data.put("name", station.getName());
                data.put("stationId", station.getId());
                data.put("location", station.getLocation());
                String status = "";
                List<JSONObject> dataList = new ArrayList<>();
                if (deviceFullData != null && deviceFullData.size() > 0) {
                    for (DeviceFullData deviceFullDatum : deviceFullData) {
                        JSONObject dataObj = new JSONObject();
                        dataObj.put("property", deviceFullDatum.getProperty());
                        dataObj.put("propertyName", deviceFullDatum.getPropertyName());
                        dataObj.put("time", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date(deviceFullDatum.getCollectionTime())));
                        if (!deviceFullDatum.isStatus()) {
                            dataObj.put("status", "offline");
                        } else {
                            String alarmLevel = deviceFullDatum.getAlarmLevel();
                            if (alarmLevel == null) {
                                alarmLevel = "online";
                            }
                            if (deviceFullDatum.getAlarmStatus() != null) {
                                alarmLevel = "alarm";
                            }
                            dataObj.put("status", alarmLevel);
                        }
                        status = dataObj.getString("status");
                        String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(deviceFullDatum.getDeviceId()));
                        dataObj.put("deviceId", deviceId);
                        dataObj.put("value", deviceFullDatum.getValue());
                        dataObj.put("unit", deviceFullDatum.getUnit());

                        dataList.add(dataObj);
                    }
                }
                data.put("dataList", dataList);

                result.add(data);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return result;
    }

    @Override
    public Object getThreeDaysData(String deviceId, String attr, TenantId tenantId) {
        List<String> attributes = new ArrayList<>();
        attributes.add(deviceId + "." + attr);
        // 处理时间
        Calendar instance = Calendar.getInstance();

        // 今日
        instance.set(Calendar.HOUR_OF_DAY, 23);
        instance.set(Calendar.MINUTE, 59);
        instance.set(Calendar.SECOND, 59);
        Date todayEnd = instance.getTime();
        instance.set(Calendar.HOUR_OF_DAY, 0);
        instance.set(Calendar.MINUTE, 0);
        instance.set(Calendar.SECOND, 0);
        Date todayStart = instance.getTime();

        // 昨日
        Date yesterdayStart = new Date(todayStart.getTime() - (24 * 60 * 60 * 1000));
        Date yesterdayEnd = new Date(todayStart.getTime() - 1000);

        // 前日
        Date beforeYesterdayStart = new Date(yesterdayStart.getTime() - (24 * 60 * 60 * 1000));
        Date beforeYesterdayEnd = new Date(yesterdayStart.getTime() - 1000);

        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> beforeYesterdayDataMap = null;
        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> yesterdayDataMap = null;
        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> todayDataMap = null;
        try {
            // 查询前日
            beforeYesterdayDataMap = obtainDataService.getDeviceData(attributes, beforeYesterdayStart.getTime(), beforeYesterdayEnd.getTime(), DateUtils.HOUR, null, tenantId);

            // 查询昨日
            yesterdayDataMap = obtainDataService.getDeviceData(attributes, yesterdayStart.getTime(), yesterdayEnd.getTime(), DateUtils.HOUR, null, tenantId);

            // 查询今日
            todayDataMap = obtainDataService.getDeviceData(attributes, todayStart.getTime(), todayEnd.getTime(), DateUtils.HOUR, null, tenantId);
        } catch (ThingsboardException e) {
            e.printStackTrace();
        }

        // 转数组
        List<JSONObject> beforeYesterdayDataList = new ArrayList<>();
        if (beforeYesterdayDataMap != null) {
            List<JSONObject> list = StationDataUtil.dataMapToShortenTimeKeyList(beforeYesterdayDataMap, DateUtils.HOUR);
            beforeYesterdayDataList.addAll(list);
        }
        List<JSONObject> yesterdayDataList = new ArrayList<>();
        if (yesterdayDataMap != null) {
            List<JSONObject> list = StationDataUtil.dataMapToShortenTimeKeyList(yesterdayDataMap, DateUtils.HOUR);
            yesterdayDataList.addAll(list);
        }
        List<JSONObject> todayDataList = new ArrayList<>();
        if (todayDataMap != null) {
            List<JSONObject> list = StationDataUtil.dataMapToShortenTimeKeyList(todayDataMap, DateUtils.HOUR);
            todayDataList.addAll(list);
        }

        JSONObject result = new JSONObject();
        result.put("beforeYesterdayDataList", beforeYesterdayDataList);
        result.put("yesterdayDataList", yesterdayDataList);
        result.put("todayDataList", todayDataList);

        return result;
    }

    @Override
    public DynamicTableVO stationDayDataQuery(String stationId, Long start, Long end,
                                      Integer filterStart, Integer filterEnd,
                                      String queryType, String group,
                                      String attributeId, TenantId tenantId) throws ThingsboardException {
        // 查询指定站点的数据
        StationEntity station = stationFeignClient.get(stationId);
        if (station == null) {
            throw new ThingsboardException("查询的站点不存在或已被删除!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }

        // 是否只查询指定的数据项
        List<StationAttrEntity> attrList = new ArrayList<>();
        if (StringUtils.isNotBlank(attributeId)) {
            StationAttrEntity attr = stationFeignClient.findAttrById(attributeId);
            attrList.add(attr);
        } else {
            // 查询指定的分组
            attrList.addAll(stationFeignClient.getAttrList(stationId, group));
        }

        // 站点属性map
        Map<String, StationAttrEntity> stationAttrMap = new HashMap<>();
        if (attrList.isEmpty()) {
            throw new ThingsboardException("要查询的站点未设置属性", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }
        for (StationAttrEntity stationAttr : attrList) {
            stationAttrMap.put(UUIDConverter.fromTimeUUID(UUID.fromString(stationAttr.getDeviceId())) + "." + stationAttr.getAttr(), stationAttr);
        }

        // 查询数据
        List<String> queryAttrList = new ArrayList<>(stationAttrMap.keySet());
        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> stationDataMap = obtainDataService.getDeviceDataByOriginal(queryAttrList, start, end, queryType, null, tenantId);

        // 筛选数据
        List<String> timeKeyList = new ArrayList<>(stationDataMap.keySet());
        SimpleDateFormat dateFormat1 = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        SimpleDateFormat dateFormat2 = new SimpleDateFormat("yyyy-MM-dd HH");
        Calendar instance = Calendar.getInstance();
        timeKeyList = timeKeyList.stream().filter(timeKey -> {
            Date date = null;
            if (queryType.endsWith("h")) {// 按小时查
                try {
                    date = dateFormat2.parse(timeKey);
                } catch (ParseException e) {
                    return false;
                }
                instance.setTime(date);
            } else {// 按分钟查
                try {
                    date = dateFormat1.parse(timeKey);
                } catch (ParseException e) {
                    return false;
                }
            }
            instance.setTime(date);
            return filterStart <= instance.get(Calendar.HOUR_OF_DAY) && filterEnd >= instance.get(Calendar.HOUR_OF_DAY);
        }).collect(Collectors.toList());

        List<JSONObject> dataList = new ArrayList<>();
        for (String timeKey : timeKeyList) {
            JSONObject data = new JSONObject();
            data.put("ts", timeKey);
            data.put("name", group);
            LinkedHashMap<String, BigDecimal> dataMap = stationDataMap.get(timeKey);
            for (Map.Entry<String, BigDecimal> dataEntry : dataMap.entrySet()) {
                data.put(dataEntry.getKey(), dataEntry.getValue());
            }

            dataList.add(data);
        }
        // 表格渲染结构
        List<DeviceDataTableInfoVO> deviceDataTableInfoList = new ArrayList<>();
        // 固定结构
        deviceDataTableInfoList.addAll(DeviceTableInfoUtil.stringArrayToDeviceTableInfoList("name:监测组", "ts:数据时间"));

        // 动态结构
        for (String attrKey : queryAttrList) {
            StationAttrEntity stationAttr = stationAttrMap.get(attrKey);
            if (stationAttr != null) {
                DeviceDataTableInfoVO deviceDataTableInfo = new DeviceDataTableInfoVO();
                deviceDataTableInfo.setColumnValue(UUIDConverter.fromTimeUUID(UUID.fromString(stationAttr.getDeviceId())) + "." + stationAttr.getAttr());
                deviceDataTableInfo.setColumnName(stationAttr.getName());
                deviceDataTableInfo.setUnit(stationAttr.getUnit());

                deviceDataTableInfoList.add(deviceDataTableInfo);
            }
        }

        DynamicTableVO result = new DynamicTableVO();
        result.setTableInfo(deviceDataTableInfoList);
        result.setTableDataList(dataList);

        return result;
    }

    @Override
    public DynamicTableVO stationDataQuery(String stationId, Long start, Long end,
                                      String queryType, String group,
                                      List<String> attrs, TenantId tenantId) throws ThingsboardException {
        // 查询指定站点的数据
        StationEntity station = stationFeignClient.get(stationId);
        if (station == null) {
            throw new ThingsboardException("查询的站点不存在或已被删除!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }

        // 是否只查询指定的数据项
        List<StationAttrEntity> attrList = stationFeignClient.getAttrList(stationId, group);
        // 筛选出需要的
        attrList = attrList.stream().filter(stationAttrEntity -> attrs.contains(stationAttrEntity.getAttr())).collect(Collectors.toList());

        // 站点属性map
        Map<String, StationAttrEntity> stationAttrMap = new HashMap<>();
        if (attrList.isEmpty()) {
            throw new ThingsboardException("要查询的站点未设置属性", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }
        for (StationAttrEntity stationAttr : attrList) {
            stationAttrMap.put(UUIDConverter.fromTimeUUID(UUID.fromString(stationAttr.getDeviceId())) + "." + stationAttr.getAttr(), stationAttr);
        }

        // 查询数据
        List<String> queryAttrList = new ArrayList<>(stationAttrMap.keySet());
        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> stationDataMap = obtainDataService.getDeviceDataByOriginal(queryAttrList, start, end, queryType, null, tenantId);

        // 筛选数据
        List<String> timeKeyList = new ArrayList<>(stationDataMap.keySet());

        List<JSONObject> dataList = new ArrayList<>();
        for (String timeKey : timeKeyList) {
            JSONObject data = new JSONObject();
            data.put("ts", timeKey);
            data.put("name", group);
            LinkedHashMap<String, BigDecimal> dataMap = stationDataMap.get(timeKey);
            for (Map.Entry<String, BigDecimal> dataEntry : dataMap.entrySet()) {
                data.put(dataEntry.getKey(), dataEntry.getValue());
            }

            dataList.add(data);
        }
        // 表格渲染结构
        List<DeviceDataTableInfoVO> deviceDataTableInfoList = new ArrayList<>();
        // 固定结构
        deviceDataTableInfoList.addAll(DeviceTableInfoUtil.stringArrayToDeviceTableInfoList("name:监测组", "ts:数据时间"));

        // 动态结构
        for (String attrKey : queryAttrList) {
            StationAttrEntity stationAttr = stationAttrMap.get(attrKey);
            if (stationAttr != null) {
                DeviceDataTableInfoVO deviceDataTableInfo = new DeviceDataTableInfoVO();
                deviceDataTableInfo.setColumnValue(UUIDConverter.fromTimeUUID(UUID.fromString(stationAttr.getDeviceId())) + "." + stationAttr.getAttr());
                deviceDataTableInfo.setColumnName(stationAttr.getName());
                deviceDataTableInfo.setUnit(stationAttr.getUnit());

                deviceDataTableInfoList.add(deviceDataTableInfo);
            }
        }

        DynamicTableVO result = new DynamicTableVO();
        result.setTableInfo(deviceDataTableInfoList);
        result.setTableDataList(dataList);

        return result;
    }

    @Override
    public Object stationAttrDataQueryGroupByDay(String stationId, Long start, Long end,
                                                 Integer filterStart, Integer filterEnd,
                                                 String queryType, String attributeId, TenantId tenantId) throws ThingsboardException {
        // 查询指定站点的数据
        StationEntity station = stationFeignClient.get(stationId);
        if (station == null) {
            throw new ThingsboardException("查询的站点不存在或已被删除!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }

        // 是否只查询指定的数据项
        List<StationAttrEntity> attrList = new ArrayList<>();
        StationAttrEntity attr = stationFeignClient.findAttrById(attributeId);
        attrList.add(attr);

        // 站点属性map
        Map<String, StationAttrEntity> stationAttrMap = new HashMap<>();
        if (attrList.isEmpty()) {
            throw new ThingsboardException("要查询的站点未设置属性", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }
        for (StationAttrEntity stationAttr : attrList) {
            stationAttrMap.put(UUIDConverter.fromTimeUUID(UUID.fromString(stationAttr.getDeviceId())) + "." + stationAttr.getAttr(), stationAttr);
        }

        // 查询数据
        List<String> queryAttrList = new ArrayList<>(stationAttrMap.keySet());
        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> stationDataMap = obtainDataService.getDeviceDataByOriginal(queryAttrList, start, end, queryType, null, tenantId);

        // 筛选数据
        List<String> timeKeyList = new ArrayList<>(stationDataMap.keySet());
        SimpleDateFormat dateFormat1 = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        SimpleDateFormat dateFormat2 = new SimpleDateFormat("yyyy-MM-dd HH");
        Calendar instance = Calendar.getInstance();
        timeKeyList = timeKeyList.stream().filter(timeKey -> {
            Date date = null;
            if (queryType.endsWith("h")) {// 按小时查
                try {
                    date = dateFormat2.parse(timeKey);
                } catch (ParseException e) {
                    return false;
                }
                instance.setTime(date);
            } else {// 按分钟查
                try {
                    date = dateFormat1.parse(timeKey);
                } catch (ParseException e) {
                    return false;
                }
            }
            instance.setTime(date);
            return filterStart <= instance.get(Calendar.HOUR_OF_DAY) && filterEnd >= instance.get(Calendar.HOUR_OF_DAY);
        }).collect(Collectors.toList());

        // 时间按天分组
        Map<String, List<String>> timeKeyGroupMap = new LinkedHashMap<>();
        for (String timeKey : timeKeyList) {
            String dayTimeKey = timeKey.substring(0, 11);
            List<String> dayTimeKeyList = new ArrayList<>();
            if (timeKeyGroupMap.containsKey(dayTimeKey)) {
                dayTimeKeyList = timeKeyGroupMap.get(dayTimeKey);
            }
            dayTimeKeyList.add(timeKey);

            timeKeyGroupMap.put(dayTimeKey, dayTimeKeyList);
        }

        List<JSONObject> resultList = new ArrayList<>();
        for (Map.Entry<String, List<String>> dayTimeKeyEntry : timeKeyGroupMap.entrySet()) {
            String dayTimeKey = dayTimeKeyEntry.getKey();
            List<String> dayTimeKeyList = dayTimeKeyEntry.getValue();

            JSONObject resultObj = new JSONObject();
            resultObj.put("dayTimeKey", dayTimeKey);
            List<LineChartDataVO> dataList = new ArrayList<>();
            for (String timeKey : dayTimeKeyList) {
                LineChartDataVO chartData = new LineChartDataVO();
                chartData.setTs(StationDataUtil.shortenTimeKey(timeKey, queryType));
                LinkedHashMap<String, BigDecimal> dataMap = stationDataMap.get(timeKey);
                if (dataMap != null) {
                    for (Map.Entry<String, BigDecimal> dataEntry : dataMap.entrySet()) {
                        chartData.setValue(dataEntry.getValue());
                    }
                }

                dataList.add(chartData);
            }
            resultObj.put("dayDataList", dataList);
            resultList.add(resultObj);
        }

        return resultList;
    }

    @Override
    public DynamicTableVO getDataCompare(String attributes, String queryType, Long start, Long end, Integer filterStart, Integer filterEnd, TenantId tenantId) throws ThingsboardException {
        // 查询数据项列表
        List<StationAttrEntity> stationAttrList = stationFeignClient.findAttrByIdList(attributes);

        // 获取站点列表
        Optional<String> stationIdsOptional = stationAttrList.stream().map(StationAttrEntity::getStationId).distinct().reduce((s1, s2) -> s1 + "," + s2);
        if (!stationIdsOptional.isPresent()) {
            throw new ThingsboardException("要查询的数据不存在!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }

        List<StationEntity> stationList = stationFeignClient.findByStationIdList(stationIdsOptional.get());
        Map<String, StationEntity> stationMap = stationList.stream().collect(Collectors.toMap(StationEntity::getId, station -> station));

        // 要查询的数据项
        List<String> attributeList = new ArrayList<>();

        // 数据项进行分组
        Map<String, Map<String, StationAttrEntity>> attrMap = new HashMap<>();

        for (StationAttrEntity stationAttr : stationAttrList) {
            // 放入要查询的数据项列表
            String attrKey = UUIDConverter.fromTimeUUID(UUID.fromString(stationAttr.getDeviceId())) + "." + stationAttr.getAttr();
            attributeList.add(attrKey);

            // 属性Map
            Map<String, StationAttrEntity> stationAttrMap = new HashMap<>();
            if (attrMap.containsKey(attrKey)) {
                stationAttrMap = attrMap.get(attrKey);
            }
            stationAttrMap.put(stationAttr.getId(), stationAttr);

            attrMap.put(attrKey, stationAttrMap);
        }

        if (attributeList.isEmpty()) {
            throw new ThingsboardException("要查询的数据项不存在或已被删除!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }

        // 查询数据项数据
        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> stationDataMap =
                obtainDataService.getDeviceDataByOriginal(attributeList, start, end, queryType, null, tenantId);

        // 数据分组
        List<JSONObject> dataList = new ArrayList<>();
        for (Map.Entry<String, LinkedHashMap<String, BigDecimal>> entry : stationDataMap.entrySet()) {
            String timeKey = entry.getKey();
            LinkedHashMap<String, BigDecimal> dataMap = entry.getValue();

            JSONObject data = new JSONObject();
            data.put("ts", timeKey);
            for (Map.Entry<String, BigDecimal> dataEntry : dataMap.entrySet()) {
                String key = dataEntry.getKey();
                BigDecimal value = dataEntry.getValue();
                Map<String, StationAttrEntity> stationAttrMap = attrMap.get(key);
                if (stationAttrMap != null) {
                    for (StationAttrEntity stationAttr : stationAttrMap.values()) {
                        data.put(stationAttr.getId(), value);
                    }
                }
            }

            dataList.add(data);
        }

        // 表格渲染结构
        List<DeviceDataTableInfoVO> deviceDataTableInfoList = new ArrayList<>();
        // 固定结构
        deviceDataTableInfoList.addAll(DeviceTableInfoUtil.stringArrayToDeviceTableInfoList("ts:数据时间"));
        // 动态结构
        for (StationAttrEntity stationAttr : stationAttrList) {
            DeviceDataTableInfoVO dataVO = new DeviceDataTableInfoVO();
            dataVO.setColumnValue(stationAttr.getId());
            dataVO.setUnit(stationAttr.getUnit());

            StationEntity station = stationMap.get(stationAttr.getStationId());
            if (station != null) {
                dataVO.setColumnName(station.getName() + "--" + stationAttr.getType() + "--" + stationAttr.getName());
            }

            deviceDataTableInfoList.add(dataVO);
        }

        DynamicTableVO result = new DynamicTableVO();
        result.setTableInfo(deviceDataTableInfoList);
        result.setTableDataList(dataList);

        return result;
    }

    @Override
    public DynamicTableVO getCycleCompare(String attributeId, String queryType, Long start, Long end, TenantId tenantId) throws ThingsboardException {
        // 报表类型
        String exeQueryType = "";
        switch (queryType) {
            // 日分时
            case "day":
                exeQueryType = "1h";
                break;
            // 月分日
            case "month":
                exeQueryType = "day";
                break;
            default:
                throw new ThingsboardException("非法的报表类型, 仅支持 day、month!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }
        // 查询数据项
        StationAttrEntity stationAttr = stationFeignClient.findAttrById(attributeId);

        if (stationAttr == null) {
            throw new ThingsboardException("查询的数据项不存在或已被删除!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);

        }

        // 执行查询
        String attr = UUIDConverter.fromTimeUUID(UUID.fromString(stationAttr.getDeviceId())) + "." + stationAttr.getAttr();
        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> stationDataMap =
                obtainDataService.getDeviceDataByOriginal(Collections.singletonList(attr), start, end, exeQueryType, null, tenantId);

        // 数据分组
        List<String> allTimeKeyList = new ArrayList<>(stationDataMap.keySet());
        // 筛选数据, 按报表分组
        Map<String, List<String>> timeKeyMap = new LinkedHashMap<>();

        // 将数据时间分组
        for (String timeKey : allTimeKeyList) {
            String timeSubstring = "";
            if (queryType.equals("month")) {// 月分日
                timeSubstring = timeKey.substring(0, 7);// 取月份
            } else {// 日分时
                timeSubstring = timeKey.substring(0, 10);// 取日期
            }
            List<String> timeKeyList = new ArrayList<>();
            if (timeKeyMap.containsKey(timeSubstring)) {
                timeKeyList = timeKeyMap.get(timeSubstring);
            }

            timeKeyList.add(timeKey);
            timeKeyMap.put(timeSubstring, timeKeyList);
        }

        // 数据分组
        Map<String, JSONObject> dataMap = new LinkedHashMap<>();
        for (Map.Entry<String, List<String>> entry : timeKeyMap.entrySet()) {
            String key = entry.getKey();
            List<String> timeKey = entry.getValue();
            for (String time : timeKey) {
                JSONObject data = new JSONObject();
                data.put("ts", StationDataUtil.shortenTimeKey(time, queryType));
                dataMap.put(StationDataUtil.shortenTimeKey(time, queryType), data);
            }
        }

        // 设置数据
        for (Map.Entry<String, List<String>> entry : timeKeyMap.entrySet()) {
            String key = entry.getKey();
            List<String> timeList = entry.getValue();

            for (String time : timeList) {
                JSONObject data = dataMap.get(StationDataUtil.shortenTimeKey(time, queryType));
                LinkedHashMap<String, BigDecimal> dataValueMap = stationDataMap.get(time);
                if (data != null) {
                    if (dataValueMap != null) {
                        for (Map.Entry<String, BigDecimal> dataEntry : dataValueMap.entrySet()) {
                            data.put(key, dataEntry.getValue());
                        }
                    }
                }
            }
        }

        // 表格渲染结构
        List<DeviceDataTableInfoVO> deviceDataTableInfoList = new ArrayList<>();
        // 固定结构
        deviceDataTableInfoList.addAll(DeviceTableInfoUtil.stringArrayToDeviceTableInfoList("ts:数据时间"));
        // 动态结构
        for (String key : timeKeyMap.keySet()) {
            DeviceDataTableInfoVO dataTableInfoVO = new DeviceDataTableInfoVO();
            dataTableInfoVO.setColumnName(stationAttr.getType() + "--" + stationAttr.getName() + "--" + key);
            dataTableInfoVO.setColumnValue(key);
            dataTableInfoVO.setUnit(stationAttr.getUnit());
            deviceDataTableInfoList.add(dataTableInfoVO);
        }

        DynamicTableVO result = new DynamicTableVO();
        result.setTableInfo(deviceDataTableInfoList);
        result.setTableDataList(new ArrayList<>(dataMap.values()));

        return result;
    }

    @Override
    public Object todayDataList(String stationId, TenantId tenantId) throws ThingsboardException {
        // 查询站点
        StationEntity station = stationFeignClient.get(stationId);
        if (station == null) {
            throw new ThingsboardException("要查询的站点不存在!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }

        // 查询站点的数据点列表
        List<StationAttrEntity> stationAllAttrList = stationFeignClient.getStationAllAttrList(stationId);
        if (stationAllAttrList == null || stationAllAttrList.isEmpty()) {
            throw new ThingsboardException("查询的站点未配置数据点!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }
        Map<String, StationAttrEntity> stationAttrMap = stationAllAttrList.stream()
                .collect(Collectors.toMap(
                        stationAttrEntity -> UUIDConverter.fromTimeUUID(UUID.fromString(stationAttrEntity.getDeviceId())) + "." + stationAttrEntity.getAttr(),
                        stationAttrEntity -> stationAttrEntity));

        // 查询数据点的数据
        List<String> attrList = new ArrayList<>();
        for (StationAttrEntity stationAttrEntity : stationAllAttrList) {
            attrList.add(UUIDConverter.fromTimeUUID(UUID.fromString(stationAttrEntity.getDeviceId())) + "." + stationAttrEntity.getAttr());
        }

        // 执行查询
        Calendar instance = Calendar.getInstance();
        instance.set(Calendar.HOUR_OF_DAY, 0);
        instance.set(Calendar.MINUTE, 0);
        instance.set(Calendar.SECOND, 0);
        Date start = instance.getTime();

        instance.set(Calendar.HOUR_OF_DAY, 23);
        instance.set(Calendar.MINUTE, 59);
        instance.set(Calendar.SECOND, 59);
        Date end = instance.getTime();

        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> stationDataMap = obtainDataService.getDeviceData(attrList, start.getTime(), end.getTime(), "hour", null, tenantId);

        // 数据处理
        Map<String, List<JSONObject>> resultMap = new HashMap<>();
        for (Map.Entry<String, LinkedHashMap<String, BigDecimal>> entry : stationDataMap.entrySet()) {
            String timeKey = entry.getKey();
            LinkedHashMap<String, BigDecimal> dataMap = entry.getValue();
            if (dataMap != null && !dataMap.isEmpty()) {
                for (Map.Entry<String, BigDecimal> dataEntry : dataMap.entrySet()) {
                    String key = dataEntry.getKey();
                    BigDecimal dataValue = dataEntry.getValue();

                    List<JSONObject> dataList = new ArrayList<>();
                    if (resultMap.containsKey(key)) {
                        dataList = resultMap.get(key);
                    }
                    JSONObject data = new JSONObject();
                    data.put("ts", timeKey);
                    data.put("value", dataValue);
                    dataList.add(data);

                    resultMap.put(key, dataList);
                }
            }
        }

        // 处理结果为数组并返回
        List<JSONObject> resultList = new ArrayList<>();
        for (Map.Entry<String, List<JSONObject>> entry : resultMap.entrySet()) {
            String key = entry.getKey();
            List<JSONObject> dataList = entry.getValue();
            StationAttrEntity stationAttr = stationAttrMap.get(key);
            String name = stationAttr.getName();

            JSONObject data = new JSONObject();
            data.put("key", key);
            data.put("name", name);
            data.put("unit", stationAttr.getUnit());
            data.put("dataList", dataList);
            resultList.add(data);
        }

        return resultList;
    }

    @Override
    public Object getStationDataDetailByGroup(String stationId, TenantId tenantId) throws ThingsboardException {
        // 查询站点
        StationEntity station = stationFeignClient.get(stationId);
        if (station == null) {
            throw new ThingsboardException("要查询的站点不存在或已被删除!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }

        List<StationAttrEntity> stationAttrList = stationFeignClient.getStationAllAttrList(stationId);
        if (stationAttrList == null || stationAttrList.isEmpty()) {
            throw new ThingsboardException("要查询的站点未正确设置数据点!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }
        // 设备变量分组
        Map<String, List<StationAttrEntity>> stationAttrGroupMap = new LinkedHashMap<>();
        for (StationAttrEntity stationAttr : stationAttrList) {
            List<StationAttrEntity> groupList = new ArrayList<>();
            if (stationAttrGroupMap.containsKey(stationAttr.getType())) {
                groupList = stationAttrGroupMap.get(stationAttr.getType());
            }
            // 数据分组
            groupList.add(stationAttr);
            stationAttrGroupMap.put(stationAttr.getType(), groupList);
        }

        List<String> deviceIdList = stationAttrList.stream().map(StationAttrEntity::getDeviceId).distinct().collect(Collectors.toList());
        // 将查询到的设备数据按设备以及变量名进行分组
        Map<String, Map<String, DeviceFullData>> deviceDataMap = new HashMap<>();
        // 对数据进行处理(按设备ID以及变量名分组, 方便后续查询)
        for (String deviceId : deviceIdList) {
            if (StringUtils.isBlank(deviceId)) {
                continue;
            }
            List<DeviceFullData> deviceFullData = deviceService.getDeviceFullData(tenantId, new DeviceId(UUID.fromString(deviceId)), "");
            if (deviceFullData != null && deviceFullData.size() > 0) {
                Map<String, DeviceFullData> dataMap = new HashMap<>();
                for (DeviceFullData deviceFullDatum : deviceFullData) {
                    String property = deviceFullDatum.getProperty();
                    dataMap.put(property, deviceFullDatum);
                }
                deviceDataMap.put(deviceId, dataMap);
            }
        }

        // 整合数据
        List<JSONObject> result = new ArrayList<>();
        for (Map.Entry<String, List<StationAttrEntity>> entry : stationAttrGroupMap.entrySet()) {
            String key = entry.getKey();
            List<StationAttrEntity> groupList = entry.getValue();
            JSONObject data = new JSONObject();
            data.put("name", key);
            List<DeviceFullData> groupDataList = new ArrayList<>();
            for (StationAttrEntity stationAttr : groupList) {
                String deviceId = stationAttr.getDeviceId();
                String attr = stationAttr.getAttr();
                Map<String, DeviceFullData> fullDataMap = deviceDataMap.get(deviceId);
                if (fullDataMap != null) {
                    DeviceFullData deviceFullData = fullDataMap.get(attr);
                    if (deviceFullData != null) {
                        deviceFullData.setDeviceId(deviceId);
                        deviceFullData.setPropertyName(stationAttr.getName());
                        groupDataList.add(deviceFullData);
                    }
                }
            }
            data.put("dataList", groupDataList);

            result.add(data);
        }

        return result;
    }

    @Override
    public Object getStationDataDetailGroupListView(String stationType, String projectId, TenantId tenantId) throws ThingsboardException {
        // 查询指定类型的站点列表
        PageData<StationEntity> stationEntityPageData = stationFeignClient.list(1, 99999, stationType, projectId);
        List<StationEntity> stationList = stationEntityPageData.getData();

        if (stationList == null) {
            return new ArrayList<>();
        }

        // 拼装站点数据并返回
        List<JSONObject> result = new ArrayList<>();
        for (StationEntity station : stationList) {
            JSONObject data = new JSONObject();
            data.put("name", station.getName());
            data.put("stationId", station.getId());
            data.put("location", station.getLocation());
            String status = "";
            data.put("dataGroup", this.getStationDataDetailByGroup(station.getId(), tenantId));

            result.add(data);
        }

        return result;
    }

    @Override
    public DynamicTableVO getStationDataReport(String stationId, String groupType, String stationType, String time, String queryType, TenantId tenantId) throws Exception {
        // 报表类型
        String exeQueryType = "";
        switch (queryType) {
            // 日分时
            case "day":
                exeQueryType = "1h";
                break;
            // 月分日
            case "month":
                exeQueryType = "day";
                break;
            // 年分月
            case "year":
                exeQueryType = "month";
                break;
            default:
                throw new ThingsboardException("非法的报表类型, 仅支持 day、month!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }
        // 查询指定站点的数据
        StationEntity station = stationFeignClient.get(stationId);
        if (station == null) {
            throw new ThingsboardException("查询的站点不存在或已被删除!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }

        // 是否只查询指定的数据项
        List<StationAttrEntity> attrList = new ArrayList<>();
        // 查询指定的分组
        attrList.addAll(stationFeignClient.getAttrList(stationId, groupType));

        // 站点属性map
        Map<String, StationAttrEntity> stationAttrMap = new HashMap<>();
        if (attrList.isEmpty()) {
            throw new ThingsboardException("要查询的站点未设置属性", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }
        for (StationAttrEntity stationAttr : attrList) {
            stationAttrMap.put(UUIDConverter.fromTimeUUID(UUID.fromString(stationAttr.getDeviceId())) + "." + stationAttr.getAttr(), stationAttr);
        }

        // 查询数据
        List<String> queryAttrList = new ArrayList<>(stationAttrMap.keySet());
        // 数据查询
        Map<String, Date> timeRange = StationDataUtil.getTimeRange(time, queryType);
        Date start = timeRange.get("start");
        Date end = timeRange.get("end");
        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> stationDataMap = obtainDataService.getDeviceDataByOriginal(queryAttrList, start.getTime(), end.getTime(), exeQueryType, null, tenantId);
        if (stationDataMap == null || stationDataMap.isEmpty()) {
            throw new ThingsboardException("无数据", ThingsboardErrorCode.ITEM_NOT_FOUND);
        }
        List<String> timeKeyList = new ArrayList<>(stationDataMap.keySet());

        List<JSONObject> dataList = new ArrayList<>();
        for (String timeKey : timeKeyList) {
            JSONObject data = new JSONObject();
            data.put("ts", timeKey);
            LinkedHashMap<String, BigDecimal> dataMap = stationDataMap.get(timeKey);
            for (Map.Entry<String, BigDecimal> dataEntry : dataMap.entrySet()) {
                data.put(dataEntry.getKey(), dataEntry.getValue());
            }

            dataList.add(data);
        }
        // 表格渲染结构
        List<DeviceDataTableInfoVO> deviceDataTableInfoList = new ArrayList<>();
        // 固定结构
        deviceDataTableInfoList.addAll(DeviceTableInfoUtil.stringArrayToDeviceTableInfoList("ts:数据时间"));

        // 动态结构
        for (String attrKey : queryAttrList) {
            StationAttrEntity stationAttr = stationAttrMap.get(attrKey);
            if (stationAttr != null) {
                DeviceDataTableInfoVO deviceDataTableInfo = new DeviceDataTableInfoVO();
                deviceDataTableInfo.setColumnValue(UUIDConverter.fromTimeUUID(UUID.fromString(stationAttr.getDeviceId())) + "." + stationAttr.getAttr());
                deviceDataTableInfo.setColumnName(stationAttr.getName());
                deviceDataTableInfo.setUnit(stationAttr.getUnit());

                deviceDataTableInfoList.add(deviceDataTableInfo);
            }
        }

        DynamicTableVO result = new DynamicTableVO();
        result.setTableInfo(deviceDataTableInfoList);
        result.setTableDataList(dataList);

        return result;
    }

    @Override
    public Object getMaxAndMinReport(String stationId, String groupType, String stationType, String time, String queryType, TenantId tenantId) throws Exception {
        // 查询指定站点的数据
        StationEntity station = stationFeignClient.get(stationId);
        if (station == null) {
            throw new ThingsboardException("查询的站点不存在或已被删除!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }

        // 查询指定的分组
        List<StationAttrEntity> attrList = new ArrayList<>(stationFeignClient.getAttrList(stationId, groupType));

        // 站点属性map
        Map<String, StationAttrEntity> stationAttrMap = new HashMap<>();
        if (attrList.isEmpty()) {
            throw new ThingsboardException("要查询的站点未设置属性", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }
        for (StationAttrEntity stationAttr : attrList) {
            stationAttrMap.put(UUIDConverter.fromTimeUUID(UUID.fromString(stationAttr.getDeviceId())) + "." + stationAttr.getAttr(), stationAttr);
        }

        // 查询数据
        List<String> queryAttrList = new ArrayList<>(stationAttrMap.keySet());
        // 数据查询
        Map<String, Date> timeRange = StationDataUtil.getTimeRange(time, queryType);
        Date start = timeRange.get("start");
        Date end = timeRange.get("end");
        // 查询原始数据
        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> stationDataMap = obtainDataService.getDeviceOriginalData(queryAttrList, start.getTime(), end.getTime(), null, tenantId);
        if (stationDataMap == null || stationDataMap.isEmpty()) {
            throw new ThingsboardException("设备无数据", ThingsboardErrorCode.GENERAL);
        }
        // 初始化统计map
        Map<String, Map<String, JSONObject>> countMap = new HashMap<>();
        for (Map.Entry<String, StationAttrEntity> entry : stationAttrMap.entrySet()) {
            String key = entry.getKey();
            StationAttrEntity stationAttr = entry.getValue();
            JSONObject maxObj = new JSONObject();
            maxObj.put("value", null);
            maxObj.put("time", null);
            JSONObject minObj = new JSONObject();
            minObj.put("value", null);
            minObj.put("time", null);
            Map<String, JSONObject> countDetailMap = new HashMap<>();
            countDetailMap.put("max", maxObj);
            countDetailMap.put("min", minObj);

            countMap.put(key, countDetailMap);
        }

        // 统计最大最小值
        for (Map.Entry<String, LinkedHashMap<String, BigDecimal>> entry : stationDataMap.entrySet()) {
            String key = entry.getKey();
            LinkedHashMap<String, BigDecimal> valueMap = entry.getValue();
            for (Map.Entry<String, BigDecimal> valueEntry : valueMap.entrySet()) {
                String attrKey = valueEntry.getKey();
                BigDecimal value = valueEntry.getValue();
                if (value == null) {
                    continue;
                }

                Map<String, JSONObject> maxAndMinCountMap = countMap.get(attrKey);
                JSONObject maxObj = maxAndMinCountMap.get("max");
                JSONObject minObj = maxAndMinCountMap.get("min");
                // 判断最大值
                if (maxObj.get("time") == null) {
                    maxObj.put("time", key);
                    maxObj.put("value", value);
                } else {
                    if (value.doubleValue() > maxObj.getDoubleValue("value")) {
                        maxObj.put("time", key);
                        maxObj.put("value", value);
                    }
                }
                // 判断最小值
                if (minObj.get("time") == null) {
                    minObj.put("time", key);
                    minObj.put("value", value);
                } else {
                    if (value.doubleValue() < minObj.getDoubleValue("value")) {
                        minObj.put("time", key);
                        minObj.put("value", value);
                    }
                }
            }
        }

        // 将统计数据封装成数组返回
        JSONObject result = new JSONObject();
        List<JSONObject> maxList = new ArrayList<>();
        List<JSONObject> minList = new ArrayList<>();
        for (Map.Entry<String, Map<String, JSONObject>> entry : countMap.entrySet()) {
            String key = entry.getKey();
            StationAttrEntity stationAttr = stationAttrMap.get(key);
            if (stationAttr == null) {
                continue;
            }
            Map<String, JSONObject> countDetailMap = entry.getValue();
            JSONObject minObj = countDetailMap.get("min");
            JSONObject maxObj = countDetailMap.get("max");
            minObj.put("name", stationAttr.getName());
            maxObj.put("name", stationAttr.getName());

            maxList.add(maxObj);
            minList.add(minObj);
        }

        result.put("max", maxList);
        result.put("min", minList);
        return result;
    }

    @Override
    public DynamicTableVO getStationOriginal(String stationId, String group, Date beginTime, Date endTime, TenantId tenantId) {
        List<StationAttrEntity> stationAttrList = stationFeignClient.getAttrList(stationId, group);
        if (stationAttrList == null || stationAttrList.isEmpty()) {
            return null;
        }

        Map<String, StationAttrEntity> stationAttrMap = new HashMap<>();
        List<String> attrList = stationAttrList.stream().map(stationAttr -> {
            String deviceId = UUIDConverter.fromTimeUUID(new DeviceId(UUID.fromString(stationAttr.getDeviceId())).getId());
            String attr = stationAttr.getAttr();
            String result = deviceId + "." + attr;
            stationAttrMap.put(result, stationAttr);
            return result;
        }).collect(Collectors.toList());
        // 查询数据
        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> originalData = obtainDataService.getDeviceOriginalData(attrList, beginTime.getTime(), endTime.getTime(), null, tenantId);

        Map<String, JSONObject> resultDataMap = new LinkedHashMap<>();
        for (Map.Entry<String, LinkedHashMap<String, BigDecimal>> entry : originalData.entrySet()) {
            String timeKey = entry.getKey();
            LinkedHashMap<String, BigDecimal> dataMap = entry.getValue();

            // 数据
            JSONObject data = new JSONObject();
            data.put("ts", timeKey);
            for (Map.Entry<String, BigDecimal> dataEntry : dataMap.entrySet()) {
                String dataKey = dataEntry.getKey();
                BigDecimal value = dataEntry.getValue();

                data.put(dataKey, value);
            }
            resultDataMap.put(timeKey, data);
        }

        // 表格渲染结构
        List<DeviceDataTableInfoVO> deviceDataTableInfoList = new ArrayList<>();
        // 固定结构
        deviceDataTableInfoList.addAll(DeviceTableInfoUtil.stringArrayToDeviceTableInfoList("ts:数据时间"));
        // 动态结构
        for (StationAttrEntity stationAttr : stationAttrList) {
            String deviceId = UUIDConverter.fromTimeUUID(new DeviceId(UUID.fromString(stationAttr.getDeviceId())).getId());
            String attr = stationAttr.getAttr();
            String attrKey = deviceId + "." + attr;

            DeviceDataTableInfoVO vo = new DeviceDataTableInfoVO();
            vo.setColumnName(stationAttr.getName());
            vo.setColumnValue(attrKey);
            vo.setUnit(stationAttr.getUnit());
            deviceDataTableInfoList.add(vo);
        }

        DynamicTableVO result = new DynamicTableVO();
        result.setTableInfo(deviceDataTableInfoList);
        result.setTableDataList(new ArrayList<>(resultDataMap.values()));

        return result;
    }

    @Override
    public Object getStationDataByList(String stationIds, TenantId tenantId) {
        List<StationEntity> stationList = stationFeignClient.findByStationIdList(stationIds);
        if (stationList == null || stationList.isEmpty()) {
            return new ArrayList<>();
        }
        // 查询站点数据
        List<JSONObject> resultList = new ArrayList<>();
        for (StationEntity station : stationList) {
            JSONObject data = JSON.parseObject(JSON.toJSONString(station));
            List<DeviceFullData> stationDataList = this.getStationDataDetail(station.getId(), "", true, tenantId);
            for (DeviceFullData deviceFullData : stationDataList) {
                try {
                    if (StringUtils.isNotBlank(deviceFullData.getValue())) {
                        if (deviceFullData.getPropertyName().contains("流量")) {
                            BigDecimal decimal = new BigDecimal(deviceFullData.getValue()).setScale(4, BigDecimal.ROUND_HALF_EVEN);
                            deviceFullData.setValue(decimal.toString());
                        } else {
                            BigDecimal decimal = new BigDecimal(deviceFullData.getValue()).setScale(1, BigDecimal.ROUND_HALF_EVEN);
                            deviceFullData.setValue(decimal.toString());
                        }
                    }
                } catch (Exception e) {
                    log.error("保留两位小数失败");
                }
            }
            data.put("dataList", stationDataList);

            resultList.add(data);
        }

        /*resultList = resultList.stream().sorted((o1, o2) -> {
            Integer orderNum1 = o1.getInteger("orderNum");
            Integer orderNum2 = o2.getInteger("orderNum");
            return orderNum1.compareTo(orderNum2);
        }).collect(Collectors.toList());*/

        return resultList;
    }

    @Override
    public Object getStationDataDetail(String stationId, String type, String property, TenantId tenantId) {
        List<StationAttrEntity> stationAttrList = new ArrayList<>();
        if (StringUtils.isNotBlank(type)) {
            try {
                stationAttrList = stationFeignClient.getAttrList(stationId, type);
            } catch (Exception e) {
                log.warn("获取站点属性异常, e = ", e);
            }
        } else {
            try {
                stationAttrList = stationFeignClient.getStationAllAttrList(stationId);
            } catch (Exception e) {
                log.warn("获取站点属性异常, e = ", e);
            }
        }
        if (stationAttrList == null || stationAttrList.isEmpty()) {
            return new ArrayList<>();
        }

        List<String> deviceIdList = stationAttrList.stream().map(StationAttrEntity::getDeviceId).distinct().collect(Collectors.toList());
        // 将查询到的设备数据按设备以及变量名进行分组
        Map<String, Map<String, DeviceFullData>> deviceDataMap = new HashMap<>();
        // 对数据进行处理(按设备ID以及变量名分组, 方便后续查询)
        for (String deviceId : deviceIdList) {
            if (StringUtils.isBlank(deviceId)) {
                continue;
            }
            List<DeviceFullData> deviceFullData = deviceService.getDeviceFullData(tenantId, new DeviceId(UUID.fromString(deviceId)), "");
            if (deviceFullData != null && deviceFullData.size() > 0) {
                Map<String, DeviceFullData> dataMap = new HashMap<>();
                for (DeviceFullData deviceFullDatum : deviceFullData) {
                    String propertyType = deviceFullDatum.getProperty();
                    dataMap.put(property, deviceFullDatum);
                }
                deviceDataMap.put(deviceId, dataMap);
            }
        }

        List<DeviceFullData> result = new ArrayList<>();
        for (StationAttrEntity stationAttr : stationAttrList) {
            String deviceId = stationAttr.getDeviceId();
            String attr = stationAttr.getAttr();
            Map<String, DeviceFullData> fullDataMap = deviceDataMap.get(deviceId);
            if (fullDataMap != null) {
                DeviceFullData deviceFullData = fullDataMap.get(attr);
                if (deviceFullData != null) {
                    deviceFullData.setDeviceId(deviceId);
                    deviceFullData.setPropertyName(stationAttr.getName());
                    result.add(deviceFullData);
                }
            }
        }

        return result;
    }


}
