package org.thingsboard.server.dao.smartService.portal;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.smartService.portal.SsPortalBusiness;
import org.thingsboard.server.dao.sql.smartService.portal.SsPortalBusinessMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.smartService.portal.SsPortalActiveRequest;
import org.thingsboard.server.dao.util.imodel.query.smartService.portal.SsPortalBusinessPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartService.portal.SsPortalBusinessSaveRequest;

@Service
public class SsPortalBusinessServiceImpl implements SsPortalBusinessService {
    @Autowired
    private SsPortalBusinessMapper mapper;

    @Override
    public SsPortalBusiness findById(String id) {
        return mapper.selectById(id);
    }

    @Override
    public IPage<SsPortalBusiness> findAllConditional(SsPortalBusinessPageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    public SsPortalBusiness save(SsPortalBusinessSaveRequest entity) {
        return QueryUtil.saveOrUpdateOneByRequest(entity, mapper::insert, mapper::updateFully);
    }

    @Override
    public boolean update(SsPortalBusiness entity) {
        return mapper.update(entity);
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }

    @Override
    public boolean active(SsPortalActiveRequest request) {
        return mapper.active(request);
    }

}
