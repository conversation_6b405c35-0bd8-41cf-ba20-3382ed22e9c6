import dayjs from 'dayjs';
import XLSX from 'xlsx';

export const machineRoomInfoColumn: any = [
  { label: '设备编码', prop: 'code', minWidth: 120 },
  { label: '设备名称', prop: 'name', minWidth: 120 },
  { label: '设备简称', prop: 'nickname', minWidth: 120 },
  { label: '厂家名称', prop: 'companyName', minWidth: 120 },
  { label: '设备型号', prop: 'model', minWidth: 120 },
  { label: '安装人', prop: 'installUserName', minWidth: 120 },
  {
    label: '安装日期',
    prop: 'installDate',
    minWidth: 120,
    formatter: (row: any, value: any) => {
      return dayjs(value).format('YYYY-MM-DD');
    }
  },
  {
    label: '录入日期',
    prop: 'createTime',
    minWidth: 120,
    formatter: (row: any, value: any) => {
      return dayjs(value).format('YYYY-MM-DD');
    }
  },
  { minWidth: 120, label: '性能参数', prop: 'performanceParameters' },
  { label: '备注', prop: 'remark' }
];

export const moreFilters: any = [
  {
    label: '设备名称',
    field: 'name',
    type: 'input',
    placeholder: '请输入设备名称'
  },
  {
    label: '设备简称',
    field: 'nickname',
    type: 'input',
    placeholder: '请输入设备简称'
  },
  {
    label: '厂家名称',
    field: 'companyName',
    type: 'input',
    placeholder: '请输入厂家名称'
  },
  {
    label: '设备型号',
    field: 'model',
    type: 'input',
    placeholder: '请输入设备型号'
  },
  {
    label: '安装人',
    field: 'installUserName',
    type: 'input',
    placeholder: '请输入安装人'
  },
  { label: '安装日期', field: 'installDateFrom', type: 'daterange' },
  {
    label: '录入日期',
    field: 'fromTime',
    type: 'daterange',
    format: 'YYYY-MM-DD'
  }
];

export const formFields: any = [
  {
    label: '设备编码',
    field: 'code',
    prop: 'code',
    type: 'input',
    rules: [{ required: true, message: '请填写设备编码' }],
    placeholder: '请填写设备编码'
  },
  {
    label: '设备名称',
    field: 'name',
    prop: 'name',
    type: 'input',
    rules: [{ required: true, message: '请填写设备名称' }],
    placeholder: '请填写设备名称'
  },
  {
    label: '设备简称',
    field: 'nickname',
    prop: 'nickname',
    type: 'input',
    rules: [{ required: true, message: '请填写设备简称' }],
    placeholder: '请填写设备简称'
  },
  {
    label: '厂家名称',
    field: 'companyName',
    prop: 'companyName',
    type: 'input',
    rules: [{ required: true, message: '请填写厂家名称' }],
    placeholder: '请填写厂家名称'
  },
  {
    label: '设备型号',
    field: 'model',
    prop: 'model',
    type: 'input',
    rules: [{ required: true, message: '请填写设备型号' }],
    placeholder: '请填写设备型号'
  },
  {
    label: '安装人',
    field: 'installUserName',
    prop: 'installUserName',
    type: 'input',
    rules: [{ required: true, message: '请填写安装人' }],
    placeholder: '请填写安装人'
  },
  {
    label: '安装日期',
    field: 'installDate',
    prop: 'installDate',
    type: 'date',
    rules: [{ required: true, message: '请选择安装日期' }]
  },
  {
    label: '性能参数 ',
    field: 'performanceParameters',
    prop: 'performanceParameters',
    type: 'textarea',
    rules: [{ required: true, message: '请填写性能参数' }],
    placeholder: '请填写性能参数'
  },
  {
    label: '备注',
    field: 'remark',
    prop: 'remark',
    type: 'textarea',
    placeholder: '请填写备注'
  }
];

// 供应商上传对应
export const pumpUpload = {
  设备编码: 'code',
  设备名称: 'name',
  设备简称: 'nickname',
  泵个数: 'pumpNum',
  厂家名称: 'companyName',
  设备型号: 'model',
  安装人: 'installUserName',
  安装日期: 'installDate',
  性能参数: 'performanceParameters',
  备注: 'remark'
};

export const readExcelToJson = (file) => {
  return new Promise((resolve: any) => {
    const reader = new FileReader();
    reader.onload = (e: any) => {
      const data = new Uint8Array(e.target.result);
      const workbook = XLSX.read(data, { type: 'array' });
      // console.log('workbook: ', workbook)
      // 将Excel 第一个sheet内容转为json格式
      const worksheet = workbook.Sheets[workbook.SheetNames[0]];
      const json = XLSX.utils.sheet_to_json(worksheet);
      // console.log('jsonExcel:', json)
      resolve(json);
    };

    reader.readAsArrayBuffer(file.raw);
  });
};

export const formDataFields: any = () => {
  let fields = formFields;
  fields = fields.concat(formFields) as IFormItem[];
  fields.push({
    type: 'textarea',
    label: '备注',
    field: 'remark',
    prop: 'remark',
    placeholder: '请输入备注'
  });
  return fields;
};
