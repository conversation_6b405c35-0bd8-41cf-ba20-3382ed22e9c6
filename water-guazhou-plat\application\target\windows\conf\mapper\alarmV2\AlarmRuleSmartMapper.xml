<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.alarmV2.AlarmRuleSmartMapper">

    <select id="getList" resultType="org.thingsboard.server.dao.model.sql.alarmV2.AlarmRuleSmart">
        select a.*, b.name as stationName, c.name as deviceName, tsa.id as attributeId
        from tb_alarm_rule_smart a
        left join tb_station b on a.station_id = b.id
        left join device c on a.device_id = c.id
        left join tb_station_attr tsa on a.attr = tsa.attr and a.station_id = tsa.station_id
        <where>
            <if test="stationIdList != null">
                and a.station_id in
                <foreach collection="stationIdList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="attrList != null">
                and a.attr in
                <foreach collection="attrList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
</mapper>