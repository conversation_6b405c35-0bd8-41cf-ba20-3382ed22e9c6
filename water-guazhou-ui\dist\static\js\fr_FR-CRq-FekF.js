import{o as d}from"./_commonjsHelpers-DCkdB7M8.js";import{r as _}from"./_commonjs-dynamic-modules-DfYEAvWy.js";function l(o,i){for(var r=0;r<i.length;r++){const e=i[r];if(typeof e!="string"&&!Array.isArray(e)){for(const a in e)if(a!=="default"&&!(a in o)){const s=Object.getOwnPropertyDescriptor(e,a);s&&Object.defineProperty(o,a,s.get?s:{enumerable:!0,get:()=>e[a]})}}}return Object.freeze(Object.defineProperty(o,Symbol.toStringTag,{value:"Module"}))}var u,n,t={},m={get exports(){return t},set exports(o){t=o}};u=m,(n=function(o,i){Object.defineProperty(i,"__esModule",{value:!0}),i.default={_decimalSeparator:",",_thousandSeparator:" ",_percentPrefix:null,_percentSuffix:"%",_big_number_suffix_3:"k",_big_number_suffix_6:"M",_big_number_suffix_9:"G",_big_number_suffix_12:"T",_big_number_suffix_15:"P",_big_number_suffix_18:"E",_big_number_suffix_21:"Z",_big_number_suffix_24:"Y",_small_number_suffix_3:"m",_small_number_suffix_6:"μ",_small_number_suffix_9:"n",_small_number_suffix_12:"p",_small_number_suffix_15:"f",_small_number_suffix_18:"a",_small_number_suffix_21:"z",_small_number_suffix_24:"y",_byte_suffix_B:"B",_byte_suffix_KB:"KB",_byte_suffix_MB:"MB",_byte_suffix_GB:"GB",_byte_suffix_TB:"TB",_byte_suffix_PB:"PB",_date_millisecond:"mm:ss SSS",_date_second:"HH:mm:ss",_date_minute:"HH:mm",_date_hour:"HH:mm",_date_day:"dd MMM",_date_week:"ww",_date_month:"MMM",_date_year:"yyyy",_duration_millisecond:"SSS",_duration_millisecond_second:"ss.SSS",_duration_millisecond_minute:"mm:ss SSS",_duration_millisecond_hour:"hh:mm:ss SSS",_duration_millisecond_day:"d'd' mm:ss SSS",_duration_millisecond_week:"d'd' mm:ss SSS",_duration_millisecond_month:"M'm' dd'd' mm:ss SSS",_duration_millisecond_year:"y'y' MM'm' dd'd' mm:ss SSS",_duration_second:"ss",_duration_second_minute:"mm:ss",_duration_second_hour:"hh:mm:ss",_duration_second_day:"d'd' hh:mm:ss",_duration_second_week:"d'd' hh:mm:ss",_duration_second_month:"M'm' dd'd' hh:mm:ss",_duration_second_year:"y'y' MM'm' dd'd' hh:mm:ss",_duration_minute:"mm",_duration_minute_hour:"hh:mm",_duration_minute_day:"d'd' hh:mm",_duration_minute_week:"d'd' hh:mm",_duration_minute_month:"M'm' dd'd' hh:mm",_duration_minute_year:"y'y' MM'm' dd'd' hh:mm",_duration_hour:"hh'h'",_duration_hour_day:"d'd' hh'h'",_duration_hour_week:"d'd' hh'h'",_duration_hour_month:"M'm' dd'd' hh'h'",_duration_hour_year:"y'y' MM'm' dd'd' hh'h'",_duration_day:"d'd'",_duration_day_week:"d'd'",_duration_day_month:"M'm' dd'd'",_duration_day_year:"y'y' MM'm' dd'd'",_duration_week:"w'w'",_duration_week_month:"w'w'",_duration_week_year:"w'w'",_duration_month:"M'm'",_duration_month_year:"y'y' MM'm'",_duration_year:"y'y'",_era_ad:"AD",_era_bc:"BC",A:"A",P:"P",AM:"AM",PM:"PM","A.M.":"A.M.","P.M.":"P.M.",January:"Janvier",February:"Février",March:"Mars",April:"Avril",May:"Mai",June:"Juin",July:"Juillet",August:"Août",September:"Septembre",October:"Octobre",November:"Novembre",December:"Décembre",Jan:"Jan",Feb:"Fév",Mar:"Mar",Apr:"Avr","May(short)":"Mai",Jun:"Jui",Jul:"Jul",Aug:"Aoû",Sep:"Sep",Oct:"Oct",Nov:"Nov",Dec:"Déc",Sunday:"Dimanche",Monday:"Lundi",Tuesday:"Mardi",Wednesday:"Mercredi",Thursday:"Jeudi",Friday:"Vendredi",Saturday:"Samedi",Sun:"Dim",Mon:"Lun",Tue:"Mar",Wed:"Mer",Thu:"Jeu",Fri:"Ven",Sat:"Sam",_dateOrd:function(r){var e="e";return(r<11||r>13)&&r%10==1&&(e="er"),e},"Zoom Out":"Zoom Arrière",Play:"Joue",Stop:"Arrête",Legend:"Légende","Click, tap or press ENTER to toggle":"cliquez, appuyez ou appuyez sur entrée pour basculer",Loading:"Charger",Home:"Accueil",Chart:"Graphique","Serial chart":"Graphique sérial","X/Y chart":"Graphique X/Y","Pie chart":"Camembert","Gauge chart":"Jauge graphique","Radar chart":"Carte radar","Sankey diagram":"Graphique Sankey","Flow diagram":"représentation schématique","Chord diagram":"diagramme d'accord","TreeMap chart":"carte de l'arbre","Sliced chart":"graphique en tranches",Series:"Séries","Candlestick Series":"Séries chandelier","OHLC Series":"Séries OHLC","Column Series":"Séries de colonnes","Line Series":"Série de lignes","Pie Slice Series":"Tarte tranche Séries","Funnel Series":"Séries d'entonnoir","Pyramid Series":"Séries pyramidale","X/Y Series":"Séries X/Y",Map:"Mappe","Press ENTER to zoom in":"Appuyez sur ENTER pour zoomer","Press ENTER to zoom out":"Appuyez sur ENTER pour effectuer un zoom arrière","Use arrow keys to zoom in and out":"Utilisez les touches fléchées pour zoomer et dézoomer","Use plus and minus keys on your keyboard to zoom in and out":"Utilisez les touches plus et moins de votre clavier pour effectuer un zoom avant ou arrière",Export:"Exporter",Image:"Image",Data:"Data",Print:"Imprimer","Click, tap or press ENTER to open":"Cliquez, appuyez ou appuyez sur ENTER pour ouvrir","Click, tap or press ENTER to print.":"Cliquez, appuyez ou appuyez sur ENTER pour imprimer","Click, tap or press ENTER to export as %1.":"Cliquez, appuyez ou appuyez sur ENTER pour exporter comme %1",'To save the image, right-click this link and choose "Save picture as..."':"Pour enregistrer l'image, cliquez avec le bouton droit sur ce lien et choisissez 'Enregistrer l'image sous ...'",'To save the image, right-click thumbnail on the left and choose "Save picture as..."':"'Pour enregistrer l'image, cliquez sur la vignette à gauche avec le bouton droit de la souris et choisissez 'Enregistrer l'image sous ...'","(Press ESC to close this message)":"(Appuyez sur ESC pour fermer ce message)","Image Export Complete":"Exportation d'image terminée","Export operation took longer than expected. Something might have gone wrong.":"L'exportation a pris plus de temps que prévu. Quelque chose aurait mal tourné.","Saved from":"Enregistré de",PNG:"",JPG:"",GIF:"",SVG:"",PDF:"",JSON:"",CSV:"",XLSX:"","Use TAB to select grip buttons or left and right arrows to change selection":"Utilisez la touche TAB pour sélectionner les boutons des poignées ou les flèches gauche et droite pour modifier la sélection.","Use left and right arrows to move selection":"Utilisez les flèches gauche et droite pour déplacer la sélection","Use left and right arrows to move left selection":"Utilisez les flèches gauche et droite pour déplacer la sélection gauche","Use left and right arrows to move right selection":"Utilisez les flèches gauche et droite pour déplacer la sélection droite","Use TAB select grip buttons or up and down arrows to change selection":"Utilisez les boutons de sélection TAB ou les flèches vers le haut et le bas pour modifier la sélection.","Use up and down arrows to move selection":"Utilisez les flèches haut et bas pour déplacer la sélection","Use up and down arrows to move lower selection":"Utilisez les flèches haut et bas pour déplacer la sélection inférieure","Use up and down arrows to move upper selection":"Utilisez les flèches haut et bas pour déplacer la sélection supérieure","From %1 to %2":"De %1 à %2","From %1":"De %1","To %1":"à %1","No parser available for file: %1":"Aucun analyseur disponible pour le fichier: %1","Error parsing file: %1":"Erreur d'analyse du fichier: %1","Unable to load file: %1":"Impossible de charger le fichier: %1","Invalid date":"Date invalide"}}(_,t))!==void 0&&(u.exports=n);const p=l({__proto__:null,default:d(t)},[t]);export{p as f};
