import{d as le,r as P,c as y,o as ae,g as V,n as k,q as l,F as o,p as v,G as p,bo as oe,h as U,bh as te,i as ne,an as se,y as E,x as d,J as ie,H as re,I as pe,aK as de,aL as me,c2 as ue,K as ge,N as ce,O as fe,P as ye,bz as be,bK as ve,L as _e,br as Ve,C as Te}from"./index-r0dFAfgr.js";import{f as we}from"./DateFormatter-Bm9a68Ax.js";import{g as Ce,d as z,u as he,s as ke}from"./sampling-D7cHdvJO.js";import{u as Se}from"./fileUpload-CoTDqaci.js";const xe={class:"sampling-record"},Ne={class:"card-header"},Fe={class:"header-buttons"},Le={key:1},Pe={class:"pagination"},Ue={class:"el-upload__tip"},Ee={key:0,style:{color:"#67C23A"}},ze={class:"dialog-footer"},De=le({__name:"index",setup(Me){const n=P({pageNum:1,pageSize:10,samplingLocation:"",samplingPerson:"",sampleType:"",samplingTime:void 0,type:"3"}),T=y([]),w=y(0),C=y(!1),m=y([]),D=t=>{m.value=t.map(e=>e.id)},s=P({id:"",samplingLocation:"",samplingPerson:"",samplingMethod:"",sampleNumber:"",sampleType:"",samplingTime:void 0,recordFile:"",remark:"",type:"3"}),M={samplingLocation:[{required:!0,message:"请输入采样地点",trigger:"blur"}],samplingPerson:[{required:!0,message:"请输入采样人员",trigger:"blur"}],samplingMethod:[{required:!0,message:"请输入采样方法",trigger:"blur"}],sampleNumber:[{required:!0,message:"请输入样品编号",trigger:"blur"}],sampleType:[{required:!0,message:"请选择样品类型",trigger:"change"}],samplingTime:[{required:!0,message:"请选择采样时间",trigger:"change"}]},b=y(!1),h=y(""),_=y(),c=async()=>{C.value=!0;try{const t={pageNum:n.pageNum,pageSize:n.pageSize,samplingLocation:n.samplingLocation,samplingPerson:n.samplingPerson,type:"3"};n.sampleType&&(t.sampleType=n.sampleType),n.samplingTime&&(t.samplingTime=n.samplingTime);const e=await Ce(t);e.data&&e.data.data&&e.data.data.data?(T.value=Array.isArray(e.data.data.data)?e.data.data.data:[],w.value=e.data.data.total||0):(console.warn("返回数据格式不正确:",e),T.value=[],w.value=0)}catch(t){console.error("获取数据失败:",t),T.value=[],w.value=0}finally{C.value=!1}},S=()=>{n.pageNum=1,m.value=[],c()},q=()=>{n.samplingLocation="",n.samplingPerson="",n.sampleType="",n.samplingTime=void 0,m.value=[],S()},B=()=>{h.value="新增采样记录",b.value=!0},R=t=>{h.value="编辑采样记录",Object.assign(s,{...t,samplingTime:t.samplingTime?Number(t.samplingTime):void 0}),b.value=!0},I=t=>{E.confirm("确认删除该记录吗？","提示",{type:"warning"}).then(async()=>{try{await z([t.id]),d.success("删除成功"),c()}catch(e){console.error(e),d.error("删除失败")}})},$=()=>{if(m.value.length===0){d.warning("请选择要删除的记录");return}E.confirm(`确认删除选中的 ${m.value.length} 条记录吗？`,"提示",{type:"warning"}).then(async()=>{try{await z(m.value),d.success("批量删除成功"),m.value=[],c()}catch(t){console.error(t),d.error("批量删除失败")}})},A=t=>{t.recordFile&&window.open(t.recordFile)},O=t=>t.size/1024/1024<10?!0:(d.warning("文件大小不能超过10MB"),!1),Y=async t=>{const{file:e}=t;try{const i=await Se(e,"file");s.recordFile=i,d.success("文件上传成功")}catch(i){console.error("文件上传失败:",i),d.error("文件上传失败")}},j=async()=>{_.value&&await _.value.validate(async t=>{if(t)try{const e={...s,samplingTime:s.samplingTime?String(s.samplingTime):String(Date.now()),recordFile:typeof s.recordFile=="string"?s.recordFile:"",type:"3"};s.id?(await he(e),d.success("修改成功")):(await ke(e),d.success("新增成功")),b.value=!1,c()}catch(e){console.error("保存失败:",e),d.error("保存失败")}})},K=()=>{_.value&&_.value.resetFields(),Object.assign(s,{id:"",samplingLocation:"",samplingPerson:"",samplingMethod:"",sampleNumber:"",sampleType:"",samplingTime:void 0,recordFile:"",remark:"",type:"3"});const t=document.querySelector(".upload-demo .el-upload__input");t&&(t.value="")},Q=t=>{n.pageSize=t,m.value=[],c()},G=t=>{n.pageNum=t,m.value=[],c()};return ae(()=>{c()}),(t,e)=>{const i=ie,f=re,r=pe,g=de,x=me,N=ue,F=ge,u=ce,H=fe,J=ye,W=be,X=ve,Z=_e,ee=Ve;return V(),k("div",xe,[l(W,{class:"box-card"},{header:o(()=>[v("div",Ne,[e[17]||(e[17]=v("span",null,"采样记录",-1)),v("div",Fe,[l(i,{type:"primary",onClick:B},{default:o(()=>e[15]||(e[15]=[p("新增记录")])),_:1}),l(i,{type:"danger",disabled:m.value.length===0,onClick:$},{default:o(()=>e[16]||(e[16]=[p("批量删除")])),_:1},8,["disabled"])])])]),default:o(()=>[l(F,{model:n,ref:"queryForm",inline:!0,class:"search-form"},{default:o(()=>[l(r,{label:"采样地点",prop:"samplingLocation"},{default:o(()=>[l(f,{modelValue:n.samplingLocation,"onUpdate:modelValue":e[0]||(e[0]=a=>n.samplingLocation=a),placeholder:"请输入采样地点",clearable:""},null,8,["modelValue"])]),_:1}),l(r,{label:"采样人员",prop:"samplingPerson"},{default:o(()=>[l(f,{modelValue:n.samplingPerson,"onUpdate:modelValue":e[1]||(e[1]=a=>n.samplingPerson=a),placeholder:"请输入采样人员",clearable:""},null,8,["modelValue"])]),_:1}),l(r,{label:"样品类型",prop:"sampleType"},{default:o(()=>[l(x,{modelValue:n.sampleType,"onUpdate:modelValue":e[2]||(e[2]=a=>n.sampleType=a),placeholder:"请选择样品类型",clearable:"",style:{width:"100%","min-width":"150px"}},{default:o(()=>[l(g,{label:"地表水",value:"地表水"}),l(g,{label:"地下水",value:"地下水"}),l(g,{label:"原水",value:"原水"}),l(g,{label:"湖泊水",value:"湖泊水"})]),_:1},8,["modelValue"])]),_:1}),l(r,{label:"采样时间",prop:"samplingTime"},{default:o(()=>[l(N,{modelValue:n.samplingTime,"onUpdate:modelValue":e[3]||(e[3]=a=>n.samplingTime=a),type:"date",placeholder:"选择日期","value-format":"x"},null,8,["modelValue"])]),_:1}),l(r,null,{default:o(()=>[l(i,{type:"primary",onClick:S},{default:o(()=>e[18]||(e[18]=[p("搜索")])),_:1}),l(i,{onClick:q},{default:o(()=>e[19]||(e[19]=[p("重置")])),_:1})]),_:1})]),_:1},8,["model"]),oe((V(),U(H,{data:T.value,style:{width:"100%"},onSelectionChange:D},{default:o(()=>[l(u,{type:"selection",width:"55"}),l(u,{prop:"samplingLocation",label:"采样地点","min-width":"120"}),l(u,{prop:"samplingPerson",label:"采样人员","min-width":"100"}),l(u,{prop:"samplingMethod",label:"采样方法","min-width":"120"}),l(u,{prop:"sampleNumber",label:"样品编号","min-width":"120"}),l(u,{prop:"sampleType",label:"样品类型","min-width":"100"}),l(u,{prop:"samplingTime",label:"采样时间","min-width":"120"},{default:o(({row:a})=>[p(te(a.samplingTime?ne(we)(Number(a.samplingTime),"YYYY-MM-DD"):""),1)]),_:1}),l(u,{prop:"recordFile",label:"采样报告","min-width":"120"},{default:o(({row:a})=>[a.recordFile?(V(),U(i,{key:0,type:"text",onClick:L=>A(a)},{default:o(()=>e[20]||(e[20]=[p("下载")])),_:2},1032,["onClick"])):(V(),k("span",Le,"-"))]),_:1}),l(u,{label:"操作",width:"150"},{default:o(({row:a})=>[l(i,{type:"text",onClick:L=>R(a)},{default:o(()=>e[21]||(e[21]=[p("编辑")])),_:2},1032,["onClick"]),l(i,{type:"text",onClick:L=>I(a)},{default:o(()=>e[22]||(e[22]=[p("删除")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[ee,C.value]]),v("div",Pe,[l(J,{"current-page":n.pageNum,"onUpdate:currentPage":e[4]||(e[4]=a=>n.pageNum=a),"page-size":n.pageSize,"onUpdate:pageSize":e[5]||(e[5]=a=>n.pageSize=a),total:w.value,onSizeChange:Q,onCurrentChange:G,layout:"total, sizes, prev, pager, next, jumper"},null,8,["current-page","page-size","total"])])]),_:1}),l(Z,{title:h.value,modelValue:b.value,"onUpdate:modelValue":e[14]||(e[14]=a=>b.value=a),width:"500px",onClose:K},{footer:o(()=>[v("span",ze,[l(i,{onClick:e[13]||(e[13]=a=>b.value=!1)},{default:o(()=>e[25]||(e[25]=[p("取消")])),_:1}),l(i,{type:"primary",onClick:j},{default:o(()=>e[26]||(e[26]=[p("确定")])),_:1})])]),default:o(()=>[l(F,{ref_key:"formRef",ref:_,model:s,rules:M,"label-width":"100px"},{default:o(()=>[l(r,{label:"采样地点",prop:"samplingLocation"},{default:o(()=>[l(f,{modelValue:s.samplingLocation,"onUpdate:modelValue":e[6]||(e[6]=a=>s.samplingLocation=a),placeholder:"请输入采样地点"},null,8,["modelValue"])]),_:1}),l(r,{label:"采样人员",prop:"samplingPerson"},{default:o(()=>[l(f,{modelValue:s.samplingPerson,"onUpdate:modelValue":e[7]||(e[7]=a=>s.samplingPerson=a),placeholder:"请输入采样人员"},null,8,["modelValue"])]),_:1}),l(r,{label:"采样方法",prop:"samplingMethod"},{default:o(()=>[l(f,{modelValue:s.samplingMethod,"onUpdate:modelValue":e[8]||(e[8]=a=>s.samplingMethod=a),placeholder:"请输入采样方法"},null,8,["modelValue"])]),_:1}),l(r,{label:"样品编号",prop:"sampleNumber"},{default:o(()=>[l(f,{modelValue:s.sampleNumber,"onUpdate:modelValue":e[9]||(e[9]=a=>s.sampleNumber=a),placeholder:"请输入样品编号"},null,8,["modelValue"])]),_:1}),l(r,{label:"样品类型",prop:"sampleType"},{default:o(()=>[l(x,{modelValue:s.sampleType,"onUpdate:modelValue":e[10]||(e[10]=a=>s.sampleType=a),placeholder:"请选择样品类型",style:{width:"100%","min-width":"150px"}},{default:o(()=>[l(g,{label:"地表水",value:"地表水"}),l(g,{label:"地下水",value:"地下水"}),l(g,{label:"原水",value:"原水"}),l(g,{label:"湖泊水",value:"湖泊水"})]),_:1},8,["modelValue"])]),_:1}),l(r,{label:"采样时间",prop:"samplingTime"},{default:o(()=>[l(N,{modelValue:s.samplingTime,"onUpdate:modelValue":e[11]||(e[11]=a=>s.samplingTime=a),type:"date",placeholder:"选择日期","value-format":"x",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),l(r,{label:"采样报告",prop:"recordFile"},{default:o(()=>[l(X,{class:"upload-demo","http-request":Y,"before-upload":O,limit:1},{tip:o(()=>[v("div",Ue,[e[24]||(e[24]=p(" 请上传采样报告文件 ")),s.recordFile?(V(),k("span",Ee," (已上传) ")):se("",!0)])]),default:o(()=>[l(i,{type:"primary"},{default:o(()=>e[23]||(e[23]=[p("点击上传")])),_:1})]),_:1})]),_:1}),l(r,{label:"备注",prop:"remark"},{default:o(()=>[l(f,{modelValue:s.remark,"onUpdate:modelValue":e[12]||(e[12]=a=>s.remark=a),type:"textarea",placeholder:"请输入备注"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["title","modelValue"])])}}}),$e=Te(De,[["__scopeId","data-v-dc7f0706"]]);export{$e as default};
