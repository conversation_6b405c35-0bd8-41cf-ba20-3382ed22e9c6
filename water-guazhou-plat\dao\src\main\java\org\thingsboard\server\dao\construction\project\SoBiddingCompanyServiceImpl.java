package org.thingsboard.server.dao.construction.project;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoBiddingCompany;
import org.thingsboard.server.dao.sql.smartOperation.construction.project.SoBiddingCompanyMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoBiddingCompanyPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoBiddingCompanySaveRequest;

import java.util.List;

@Service
public class SoBiddingCompanyServiceImpl implements SoBiddingCompanyService {
    @Autowired
    private SoBiddingCompanyMapper mapper;

    @Override
    public IPage<SoBiddingCompany> findAllConditional(SoBiddingCompanyPageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    public SoBiddingCompany save(SoBiddingCompanySaveRequest entity) {
        return QueryUtil.saveOrUpdateOneByRequest(entity, mapper);
    }

    @Override
    public boolean update(SoBiddingCompany entity) {
        return mapper.update(entity);
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }

    @Override
    public List<SoBiddingCompany> saveAll(List<SoBiddingCompanySaveRequest> entities) {
        return QueryUtil.saveOrUpdateBatchByRequest(entities, mapper::saveAll, mapper::saveAll);
    }

    @Override
    public int removeAllByBiddingId(String id) {
        return mapper.removeAllByBiddingId(id);
    }
}
