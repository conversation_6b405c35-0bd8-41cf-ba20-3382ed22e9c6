<!-- 工程管理-详情-参与投标公司 -->
<template>
  <el-card class="card">
    <descriptions :key="key" :config="basicConfig"></descriptions>
  </el-card>
  <CardTable
    title="参与投标公司"
    :config="TableConfig"
    class="card-table"
  ></CardTable>
  <CardTable
    title="中标公司"
    :config="winTheBid"
    class="card-table"
  ></CardTable>
</template>

<script lang="ts" setup>
import {
  getBiddingCompany,
  getBidding
} from '@/api/engineeringManagement/projectManagement';
import { GenNonDuplicateID } from '@/utils/GlobalHelper';

const props = defineProps<{ id: string; config: any }>();

const key = ref(GenNonDuplicateID());

const basicConfig = reactive<IDescriptionsConfig>({
  defaultValue: computed(() => data.basic) as any,
  border: true,
  direction: 'horizontal',
  column: 2,
  title: '项目总结算基础信息',
  fields: [
    { type: 'text', label: '参与投标公司数量:', field: 'num' },
    { type: 'text', label: '代理招标公司:', field: 'proxyBiddingCompany' },
    { type: 'text', label: '中标公司名称:', field: 'name' },
    { type: 'text', label: '中标公司联系人:', field: 'contactUser' },
    { type: 'text', label: '中标公司联系电话:', field: 'phone' }
  ]
});

const TableConfig = reactive<ICardTable>({
  defaultExpandAll: true,
  indexVisible: true,
  columns: [
    { label: '公司名称', prop: 'name' },
    { label: '联系人', prop: 'contactUser' },
    { label: '联系电话', prop: 'phone' }
  ],
  dataList: [],
  pagination: {
    hide: true
  }
});

const winTheBid = reactive<ICardTable>({
  defaultExpandAll: true,
  indexVisible: true,
  columns: [
    { label: '公司名称', prop: 'name' },
    { label: '联系人', prop: 'contactUser' },
    { label: '联系电话', prop: 'phone' }
  ],
  dataList: [],
  pagination: {
    hide: true
  }
});

function init(projectCode, preferCompanyId) {
  getBiddingCompany({ page: 1, size: -1, projectCode }).then((res) => {
    TableConfig.dataList = res.data.data.data || [];

    // 基础信息
    res.data.data.data.forEach((item) => {
      if (item.id === preferCompanyId) {
        data.basic = { ...data.basic, ...item };
        winTheBid.dataList = [item];
      }
    });
    data.basic = { ...data.basic, num: TableConfig.dataList.length || 0 };
    key.value = GenNonDuplicateID();
  });
}

const data = reactive({
  basic: {}
});

const refreshData = async () => {
  getBidding({ page: 1, size: -1, projectCode: props.config.code }).then(
    (res) => {
      init(
        res.data.data.data[0]?.projectCode,
        res.data.data.data[0]?.preferCompanyId
      );
      data.basic = {
        ...data.basic,
        proxyBiddingCompany: res.data.data.data[0]?.proxyBiddingCompany
      };
    }
  );
};

onMounted(() => {
  refreshData();
});
</script>

<style lang="scss" scoped>
.card {
  margin-bottom: 20px;
}

.card-table {
  height: 255px;
  margin-bottom: 20px;
}
</style>
