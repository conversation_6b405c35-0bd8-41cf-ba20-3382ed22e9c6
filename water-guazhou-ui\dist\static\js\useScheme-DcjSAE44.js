import{G as d,P as v}from"./SchemeManage.vue_vue_type_script_setup_true_lang-fv9Irhyi.js";import{c as t,b as c,S as p}from"./index-r0dFAfgr.js";const M=i=>{const m=t(i),s=t(),r=t(),u=e=>{s.value=e},S=e=>{r.value=e},g=()=>{var e;(e=s.value)==null||e.openDialog()},h=()=>{var e;(e=r.value)==null||e.openDialog()},n=t([]),l=t(0);return{schemeType:m,list:n,refSaveScheme:s,refSchemeManage:r,getList:async e=>{try{const a=await d(e);return n.value=a.data.data.data||[],l.value=a.data.data.total||0,{data:n.value,total:l.value}}catch{return{data:[],total:0}}},getSaveSchemeRef:u,getSchemeManageRef:S,submitScheme:e=>{p("确定保存为方案？","提示信息").then(async()=>{var a;try{const o=await v(e);o.data.code===200?(c.success("方案保存成功"),(a=s.value)==null||a.closeDialog()):(c.error("方案保存失败"),console.log(o.data.message))}catch(o){c.error("方案保存失败"),console.log(o)}}).catch(()=>{})},parseScheme:e=>{const a=e.detail?JSON.parse(e.detail):void 0;if(!a){c.error("当前方案已失效");return}return a},openManagerDialog:h,openSaveDialog:g}};export{M as u};
