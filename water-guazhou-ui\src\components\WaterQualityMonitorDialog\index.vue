<!-- 水质监控详情弹窗组件 -->
<template>
  <el-dialog
    v-model="visible"
    title="水质监控"
    width="80%"
    :before-close="handleClose"
    class="water-quality-dialog"
  >
    <div v-if="data" class="report-content">
      <!-- 基本信息表格 -->
      <div class="info-section">
        <div class="info-grid">
          <div class="info-item">
            <span class="label">采样点位</span>
            <span class="value">{{ data.stationName }}</span>
          </div>
          <div class="info-item">
            <span class="label">文本内容</span>
            <span class="value">文本内容</span>
          </div>
          <div class="info-item">
            <span class="label">抽样时间</span>
            <span class="value">{{ formatSampleTime(data.sampleTime) }}</span>
          </div>
          <div class="info-item">
            <span class="label">所属水厂</span>
            <span class="value">文本内容</span>
          </div>
          
          <div class="info-item">
            <span class="label">出水流量 m³/h</span>
            <span class="value">{{ data.indicators?.outflow?.toFixed(1) || '文本内容' }}</span>
          </div>
          <div class="info-item">
            <span class="label">进水流量 m³/h</span>
            <span class="value">{{ data.indicators?.inflow?.toFixed(1) || '文本内容' }}</span>
          </div>
          <div class="info-item">
            <span class="label">水质等级</span>
            <span class="value quality-level" :class="getQualityLevelClass(data.waterQualityLevel)">
              {{ data.waterQualityLevel }}
            </span>
          </div>
          
          <div class="info-item">
            <span class="label">出水COD mg/L</span>
            <span class="value">{{ data.indicators?.cod?.toFixed(1) || '文本内容' }}</span>
          </div>
          <div class="info-item">
            <span class="label">出水BODs mg/L</span>
            <span class="value">{{ data.indicators?.bod5?.toFixed(1) || '文本内容' }}</span>
          </div>
          <div class="info-item">
            <span class="label">悬浮物 mg/L</span>
            <span class="value">文本内容</span>
          </div>
          
          <div class="info-item">
            <span class="label">氨氮 mg/L</span>
            <span class="value">{{ data.indicators?.ammoniaNitrogen?.toFixed(3) || '文本内容' }}</span>
          </div>
          <div class="info-item">
            <span class="label">总氮 mg/L</span>
            <span class="value">{{ data.indicators?.totalNitrogen?.toFixed(2) || '文本内容' }}</span>
          </div>
          <div class="info-item">
            <span class="label">总磷 mg/L</span>
            <span class="value">{{ data.indicators?.totalPhosphorus?.toFixed(3) || '文本内容' }}</span>
          </div>
          
          <div class="info-item">
            <span class="label">PH值</span>
            <span class="value">{{ data.indicators?.ph?.toFixed(1) || '文本内容' }}</span>
          </div>
          <div class="info-item">
            <span class="label">粪大肠菌群数</span>
            <span class="value">{{ data.indicators?.fecalColiform?.toFixed(0) || '文本内容' }}</span>
          </div>
        </div>
      </div>

      <!-- 曲线分析 -->
      <div class="chart-section">
        <h3>曲线分析</h3>
        <div class="chart-controls">
          <el-button-group>
            <el-button 
              :type="activeTimeRange === 'day' ? 'primary' : 'default'" 
              size="small"
              @click="handleTimeRangeChange('day')"
            >
              日
            </el-button>
            <el-button 
              :type="activeTimeRange === 'month' ? 'primary' : 'default'" 
              size="small"
              @click="handleTimeRangeChange('month')"
            >
              月
            </el-button>
            <el-button 
              :type="activeTimeRange === 'year' ? 'primary' : 'default'" 
              size="small"
              @click="handleTimeRangeChange('year')"
            >
              年
            </el-button>
          </el-button-group>
        </div>
        
        <!-- 图表区域 -->
        <div class="chart-container">
          <div class="chart-placeholder">
            <p>进水流量 vs 出水流量 趋势图</p>
            <VChart 
              ref="refBarChart"
              :option="barChartOption" 
              class="chart"
            />
          </div>
          
          <div class="chart-placeholder">
            <p>COD vs PH 趋势图</p>
            <VChart 
              ref="refLineChart"
              :option="lineChartOption" 
              class="chart"
            />
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, computed, watch, nextTick } from 'vue'
import { IECharts } from '@/plugins/echart'

// Props
interface WaterQualityData {
  stationName: string
  sampleTime: string
  waterQualityLevel: string
  indicators?: {
    outflow?: number
    inflow?: number
    cod?: number
    bod5?: number
    ammoniaNitrogen?: number
    totalNitrogen?: number
    totalPhosphorus?: number
    ph?: number
    fecalColiform?: number
  }
}

interface Props {
  modelValue: boolean
  data?: WaterQualityData | null
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  data: null
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'close': []
}>()

// Refs
const refBarChart = ref<IECharts>()
const refLineChart = ref<IECharts>()
const activeTimeRange = ref<'day' | 'month' | 'year'>('day')

// Computed
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 格式化采样时间
const formatSampleTime = (time?: string) => {
  if (!time) return 'yyyy-MM-dd'
  const date = new Date(time)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

// 获取水质等级样式类
const getQualityLevelClass = (level: string) => {
  switch (level) {
    case 'I类':
      return 'quality-level-1'
    case 'II类':
      return 'quality-level-2'
    case 'III类':
      return 'quality-level-3'
    case 'IV类':
      return 'quality-level-4'
    case 'V类':
      return 'quality-level-5'
    default:
      return 'quality-level-unknown'
  }
}

// 生成模拟时间数据
const generateTimeData = (range: 'day' | 'month' | 'year') => {
  const data = []
  const count = range === 'day' ? 24 : range === 'month' ? 30 : 12
  
  for (let i = 0; i < count; i++) {
    let label = ''
    if (range === 'day') {
      label = `${i.toString().padStart(2, '0')}:00`
    } else if (range === 'month') {
      label = `${(i + 1).toString().padStart(2, '0')}日`
    } else {
      label = `${(i + 1).toString().padStart(2, '0')}月`
    }
    data.push(label)
  }
  
  return data
}

// 生成模拟数值数据
const generateValueData = (count: number, min: number, max: number) => {
  return Array.from({ length: count }, () => 
    Math.floor(Math.random() * (max - min + 1)) + min
  )
}

// 柱状图配置
const barChartOption = computed(() => {
  const timeData = generateTimeData(activeTimeRange.value)
  const inflowData = generateValueData(timeData.length, 80, 120)
  const outflowData = generateValueData(timeData.length, 75, 110)
  
  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: ['进水流量', '出水流量'],
      top: 10
    },
    grid: {
      left: 50,
      right: 30,
      top: 50,
      bottom: 30
    },
    xAxis: {
      type: 'category',
      data: timeData,
      axisLabel: {
        rotate: activeTimeRange.value === 'day' ? 45 : 0
      }
    },
    yAxis: {
      type: 'value',
      name: '流量(m³/h)',
      splitLine: {
        lineStyle: {
          type: 'dashed'
        }
      }
    },
    series: [
      {
        name: '进水流量',
        type: 'bar',
        data: inflowData,
        itemStyle: {
          color: '#ffa500'
        }
      },
      {
        name: '出水流量',
        type: 'bar',
        data: outflowData,
        itemStyle: {
          color: '#4a90e2'
        }
      }
    ]
  }
})

// 折线图配置
const lineChartOption = computed(() => {
  const timeData = generateTimeData(activeTimeRange.value)
  const codData = generateValueData(timeData.length, 15, 35)
  const phData = generateValueData(timeData.length, 65, 85).map(v => v / 10) // 转换为6.5-8.5范围
  
  return {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['COD', 'PH'],
      top: 10
    },
    grid: {
      left: 50,
      right: 50,
      top: 50,
      bottom: 30
    },
    xAxis: {
      type: 'category',
      data: timeData,
      axisLabel: {
        rotate: activeTimeRange.value === 'day' ? 45 : 0
      }
    },
    yAxis: [
      {
        type: 'value',
        name: 'COD(mg/L)',
        position: 'left',
        splitLine: {
          lineStyle: {
            type: 'dashed'
          }
        }
      },
      {
        type: 'value',
        name: 'PH值',
        position: 'right',
        min: 6,
        max: 9,
        splitLine: {
          show: false
        }
      }
    ],
    series: [
      {
        name: 'COD',
        type: 'line',
        yAxisIndex: 0,
        data: codData,
        smooth: true,
        itemStyle: {
          color: '#ffa500'
        },
        lineStyle: {
          color: '#ffa500'
        }
      },
      {
        name: 'PH',
        type: 'line',
        yAxisIndex: 1,
        data: phData,
        smooth: true,
        itemStyle: {
          color: '#67c23a'
        },
        lineStyle: {
          color: '#67c23a'
        }
      }
    ]
  }
})

// 方法
const handleClose = () => {
  emit('close')
  visible.value = false
}

const handleTimeRangeChange = (range: 'day' | 'month' | 'year') => {
  activeTimeRange.value = range
  // 等待DOM更新后重新渲染图表
  nextTick(() => {
    refBarChart.value?.resize()
    refLineChart.value?.resize()
  })
}

// 监听弹窗显示状态，重新渲染图表
watch(visible, (newVal) => {
  if (newVal) {
    nextTick(() => {
      refBarChart.value?.resize()
      refLineChart.value?.resize()
    })
  }
})
</script>

<style lang="scss" scoped>
// 水质监控弹窗样式
:deep(.water-quality-dialog) {
  .el-dialog__body {
    padding: 20px;
    max-height: 70vh;
    overflow-y: auto;
  }
}

.report-content {
  .info-section {
    margin-bottom: 24px;

    .info-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 16px;
      background-color: #f5f7fa;
      padding: 16px;
      border-radius: 8px;

      .info-item {
        display: flex;
        flex-direction: column;
        gap: 4px;

        .label {
          font-size: 12px;
          color: #666;
          font-weight: normal;
        }

        .value {
          font-size: 14px;
          color: #333;
          font-weight: 500;

          &.quality-level {
            font-weight: bold;
          }
        }
      }
    }
  }

  .chart-section {
    h3 {
      margin: 0 0 16px 0;
      font-size: 16px;
      color: #333;
    }

    .chart-controls {
      margin-bottom: 16px;
    }

    .chart-container {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 20px;

      .chart-placeholder {
        border: 1px solid #e4e7ed;
        border-radius: 8px;
        padding: 16px;
        background-color: #fff;

        p {
          margin: 0 0 12px 0;
          font-size: 14px;
          font-weight: 500;
          color: #333;
          text-align: center;
        }

        .chart {
          height: 300px;
          width: 100%;
        }
      }
    }
  }
}

// 水质等级颜色
.quality-level-1 {
  color: #67c23a;
  font-weight: bold;
}

.quality-level-2 {
  color: #409eff;
  font-weight: bold;
}

.quality-level-3 {
  color: #e6a23c;
  font-weight: bold;
}

.quality-level-4 {
  color: #f56c6c;
  font-weight: bold;
}

.quality-level-5 {
  color: #909399;
  font-weight: bold;
}

.quality-level-unknown {
  color: var(--el-text-color-placeholder);
}

// 响应式适配
@media (max-width: 1200px) {
  .report-content .info-section .info-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .report-content .chart-section .chart-container {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .report-content .info-section .info-grid {
    grid-template-columns: 1fr;
  }
}
</style>
