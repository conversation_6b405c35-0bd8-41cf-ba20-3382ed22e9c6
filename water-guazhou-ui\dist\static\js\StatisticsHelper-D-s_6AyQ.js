import{P as a}from"./pipe-nogVzCHG.js";import{W as u}from"./index-r0dFAfgr.js";var d=(e=>(e.COUNT="1",e.LENGTH="2",e))(d||{}),f=(e=>(e.DIAMETER="DIAMETER",e.MATERIAL="MATERIAL",e))(f||{}),l=(e=>(e.OBJECTID="OBJECTID",e.Shape<PERSON>en="PIPELENGTH",e))(l||{});const I=async(e,r)=>{var t,s;return((s=(t=(await a({usertoken:u().gToken,layerids:JSON.stringify(r.layerIds||[]),group_fields:JSON.stringify(r.group_fields||[]),statistic_field:e==="count"?"OBJECTID":"PIPELENGTH",statistic_type:e==="count"?"1":"2",where:r.where||"1=1",geometry:r.geometry,f:"pjson"})).data)==null?void 0:t.result)==null?void 0:s.rows)||[]},T=async e=>{var o,t,s;const r=await a({...e,layerids:JSON.stringify(e.layerIds),group_fields:JSON.stringify(e.group_fields||[])});return r.data.code===1e4?(((s=(t=(o=r.data)==null?void 0:o.result)==null?void 0:t.rows[0])==null?void 0:s.rows)||[]).map(n=>{var i;return{value:n[e.statistic_field],label:(i=n[e.group_fields[0]])==null?void 0:i.toString()}})||[]:[]};export{l as E,T as a,d as b,f as c,I as s};
