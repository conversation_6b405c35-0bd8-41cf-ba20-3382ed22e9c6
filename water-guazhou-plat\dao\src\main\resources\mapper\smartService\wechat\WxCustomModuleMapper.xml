<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartService.wechat.WxCustomModuleMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->id,
                           icon,
                           name,
                           address,
                           is_enabled,
                           guide_mode,
                           mode,
                           guide_title,
                           guide_type,
                           guide_description,
                           at_top,
                           is_pick,
                           is_recommend,
                           order_num,
                           create_time,
                           tenant_id<!--@sql from wx_custom_module -->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.smartService.wechat.WxCustomModule">
        <result column="id" property="id"/>
        <result column="icon" property="icon"/>
        <result column="name" property="name"/>
        <result column="address" property="address"/>
        <result column="is_enabled" property="isEnabled"/>
        <result column="guide_mode" property="guideMode"/>
        <result column="mode" property="mode"/>
        <result column="guide_title" property="guideTitle"/>
        <result column="guide_type" property="guideType"/>
        <result column="guide_description" property="guideDescription"/>
        <result column="at_top" property="atTop"/>
        <result column="is_pick" property="isPick"/>
        <result column="is_recommend" property="isRecommend"/>
        <result column="order_num" property="orderNum"/>
        <result column="create_time" property="createTime"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wx_custom_module
        <where>
            <if test="name != null and name != ''">
                and "name" like '%' || #{name} || '%'
            </if>
            <if test="isEnabled != null">
                and is_enabled = #{isEnabled}
            </if>
            <if test="fromTime != null">
                and create_time >= #{fromTime}
            </if>
            <if test="toTime != null">
                and create_time &lt;= #{toTime}
            </if>
            and tenant_id = #{tenantId}
        </where>
        order by create_time desc
    </select>

    <update id="updateFully">
        update wx_custom_module
        <set>
            <if test="icon != null">
                icon = #{icon},
            </if>
            <if test="name != null">
                name = #{name},
            </if>
            <if test="introduction != null">
                introduction = #{introduction},
            </if>
            <if test="address != null">
                address = #{address},
            </if>
            <if test="isEnabled != null">
                is_enabled = #{isEnabled},
            </if>
            <if test="guideMode != null">
                guide_mode = #{guideMode},
            </if>
            <if test="mode != null">
                mode = #{mode},
            </if>
            <if test="guideTitle != null">
                guide_title = #{guideTitle},
            </if>
            <if test="guideType != null">
                guide_type = #{guideType},
            </if>
            <if test="guideDescription != null">
                guide_description = #{guideDescription},
            </if>
            <if test="atTop != null">
                at_top = #{atTop},
            </if>
            <if test="isPick != null">
                is_pick = #{isPick},
            </if>
            <if test="isRecommend != null">
                is_recommend = #{isRecommend},
            </if>
            <if test="orderNum != null">
                order_num = #{orderNum},
            </if>
        </set>
        where id = #{id}
    </update>

    <select id="isMaximumTop" resultType="boolean">
        select count(1) >= #{atTopMaximum}
        from wx_custom_module
        where id != #{id}
          and at_top = true
          and tenant_id = #{tenantId}
    </select>
</mapper>