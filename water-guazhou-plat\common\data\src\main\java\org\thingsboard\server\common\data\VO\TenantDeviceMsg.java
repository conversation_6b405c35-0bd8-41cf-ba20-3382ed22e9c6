package org.thingsboard.server.common.data.VO;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2020/5/22 13:11
 */
@Data
@Builder(toBuilder = true)
public class TenantDeviceMsg {
    /**
     * 主机总数
     */
    private long gatewayAllCount;
    /**
     * 主机在线总数
     */
    private long gatewayOnlineCount;
    /**
     * 告警总数
     */
    private long alarmCount;
    /**
     * 主机在线率
     */
    private BigDecimal gatewayOnlineRate ;

    /**
     * 从机总数
     */
    private long deviceAllCount;
    /**
     * 从机在线总数
     */
    private long deviceOnlineCount;
    /**
     * 从机在线率

     */
    private BigDecimal deviceOnlineRate;


}
