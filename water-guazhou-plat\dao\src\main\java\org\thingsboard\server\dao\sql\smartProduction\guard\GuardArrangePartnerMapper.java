package org.thingsboard.server.dao.sql.smartProduction.guard;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.smartProduction.guard.GuardArrangePartner;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.guard.GuardArrangePartnerPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.guard.GuardArrangePartnerSaveRequest;

import java.util.Collection;
import java.util.List;

@Mapper
public interface GuardArrangePartnerMapper extends BaseMapper<GuardArrangePartner> {
    IPage<GuardArrangePartner> findByPage(GuardArrangePartnerPageRequest request);

    int deleteAllByArrangeId(String arrangeId);

    int saveAll(List<GuardArrangePartner> list);

    List<GuardArrangePartnerSaveRequest> selectPendingArrangePartnerSaveTemplate();

    int removeByArrangeIdIn(@Param("arrangeIdCollection")Collection<String> arrangeIdCollection);


}
