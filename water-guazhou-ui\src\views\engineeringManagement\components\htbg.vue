<!-- 工程管理-详情-合同变更 -->
<template>
  <CardTable :config="TableConfig" class="card-table"> </CardTable>
  <DialogForm ref="refForm" :config="addOrUpdateConfig"></DialogForm>
</template>

<script lang="ts" setup>
import { ElMessage } from 'element-plus';
import {
  postConstructionContractAmend,
  getConstructionContractAmendList,
  deleteConstructionContractAmend
} from '@/api/engineeringManagement/manage';
import { GenNonDuplicateID } from '@/utils/GlobalHelper';
import { SLConfirm } from '@/utils/Message';

const refForm = ref<IDialogFormIns>();
const props = defineProps<{ id: string; config: any }>();

const TableConfig = reactive<ICardTable>({
  defaultExpandAll: true,
  indexVisible: true,
  title: '合同变更',
  titleRight: [
    {
      style: {
        justifyContent: 'flex-end'
      },
      items: [
        {
          type: 'btn-group',
          btns: [
            {
              text: '添加',
              perm: true,
              click: () => {
                addOrUpdateConfig.title = '添加合同变更';
                addOrUpdateConfig.defaultValue = {
                  code: `${props.config.code || ''}-${GenNonDuplicateID()}`,
                  contractCode: props.config.code || '',
                  constructionCode: props.config.constructionCode,
                  type: '提高合理化建议'
                };
                refForm.value?.openDialog();
              }
            }
          ]
        }
      ]
    }
  ],
  columns: [
    { label: '合同编号', prop: 'contractCode' },
    {
      label: '变更日期',
      prop: 'amendDate',
      formatter: (row) => dayjs(row.amendDate).format('YYYY-MM-DD')
    },
    { label: '添加人', prop: 'creatorName' },
    { label: '添加时间', prop: 'createTimeName' },
    { label: '变更原因', prop: 'remark' },
    { label: '附件', prop: 'attachments', download: true }
  ],
  operationWidth: '160px',
  operations: [
    {
      isTextBtn: false,
      type: 'success',
      text: '编辑合同',
      perm: true,
      click: (row) => clickEdit(row)
    },
    {
      isTextBtn: false,
      type: 'danger',
      text: '删除',
      perm: true,
      click: (row) => handleDelete(row)
    }
  ],
  dataList: [],
  pagination: {
    hide: true
  }
});

const addOrUpdateConfig = reactive<IDialogFormConfig>({
  title: '添加合同变更',
  labelWidth: '130px',
  dialogWidth: '1000px',
  submitting: false,
  submit: (params: any) => {
    addOrUpdateConfig.submitting = true;
    let text = '新增';
    if (params.id) text = '修改';
    params.pipLengthDesign = JSON.stringify(params.pipLengthDesign);
    postConstructionContractAmend(params)
      .then((res) => {
        addOrUpdateConfig.submitting = false;
        if (res.data.code === 200) {
          ElMessage.success(text + '成功');
          refForm.value?.closeDialog();
          refreshData();
        } else {
          ElMessage.warning(text + '失败');
        }
      })
      .catch((error) => {
        addOrUpdateConfig.submitting = false;
        ElMessage.warning(error);
      });
  },
  defaultValue: {},
  group: [
    {
      fields: [
        {
          xs: 12,
          type: 'date',
          label: '变更日期',
          field: 'amendDate',
          format: 'x'
        },
        {
          type: 'textarea',
          label: '变更原因',
          field: 'remark'
        },
        {
          type: 'file',
          label: '附件',
          field: 'attachments'
        }
      ]
    }
  ]
});

const clickEdit = (row: { [x: string]: any }) => {
  addOrUpdateConfig.title = '编辑合同变更';
  addOrUpdateConfig.defaultValue = {
    ...(row || {}),
    contractCode: props.config.code,
    constructionCode: props.config.constructionCode
  };
  refForm.value?.openDialog();
};

const handleDelete = (row?: any) => {
  SLConfirm('确定删除？', '提示信息')
    .then(() => {
      deleteConstructionContractAmend(row.id).then((res) => {
        if (res.data?.code === 200) {
          ElMessage.success('删除成功');
          refreshData();
        } else {
          ElMessage.warning(res.data?.message);
        }
      });
    })
    .catch(() => {
      //
    });
};

const refreshData = async () => {
  getConstructionContractAmendList({
    page: 1,
    size: -1,
    contractCode: props.config.code,
    constructionCode: props.config.constructionCode
  }).then((res) => {
    TableConfig.dataList = res.data.data.data || [];
  });
};

onMounted(() => {
  refreshData();
});
</script>

<style lang="scss" scoped>
.card-table {
  height: 300px;
  margin-bottom: 20px;
}
</style>
