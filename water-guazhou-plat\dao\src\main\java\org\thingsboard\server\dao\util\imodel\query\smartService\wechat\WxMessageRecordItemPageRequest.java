package org.thingsboard.server.dao.util.imodel.query.smartService.wechat;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartService.wechat.WxMessageRecordItem;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

@Getter
@Setter
public class WxMessageRecordItemPageRequest extends AdvancedPageableQueryEntity<WxMessageRecordItem, WxMessageRecordItemPageRequest> {
    // 0发送失败、1发送中、2发送成功、3发送失败和发送中
    private String status;

    // 所属消息记录id
    @NotNullOrEmpty
    private String recordId;
}
