package org.thingsboard.server.dao.util.imodel.query.smartManagement.plan;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartManagement.plan.SMCircuitPlanResponse;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;

@Getter
@Setter
public class SMCircuitPlanPageRequest extends AdvancedPageableQueryEntity<SMCircuitPlanResponse, SMCircuitPlanPageRequest> {
    // 关键字(计划名称/备注)
    private String keyword;

    // 创建人Id
    private String creator;

}
