package org.thingsboard.server.dao.model.sql.smartProduction.guard;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

@Getter
@Setter
@ResponseEntity
@TableName("guard_place")
public class GuardPlace {
    // 值班地点id
    private String id;

    // 值班地点
    private String address;

    // 排序编号
    private Integer orderNum;

    // 租户id
    private String tenantId;

}
