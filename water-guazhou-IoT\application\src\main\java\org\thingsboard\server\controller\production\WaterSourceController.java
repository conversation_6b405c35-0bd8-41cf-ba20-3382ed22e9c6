package org.thingsboard.server.controller.production;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.Utils.DateUtils;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.client.StationFeignClient;
import org.thingsboard.server.dao.model.VO.DeviceDataTableInfoVO;
import org.thingsboard.server.dao.model.VO.DynamicTableVO;
import org.thingsboard.server.dao.model.sql.StationEntity;
import org.thingsboard.server.dao.production.ProductionService;
import org.thingsboard.server.dao.stationData.StationDataService;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;
import org.thingsboard.server.utils.ExcelUtil;

import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 智慧生产-水源管理
 */
@RestController
@RequestMapping("api/water/source")
public class WaterSourceController extends BaseController {

    @Autowired
    private StationFeignClient stationFeignClient;

    @Autowired
    private ProductionService productionService;

    @Autowired
    private StationDataService stationDataService;

    /**
     * 查询水源地列表以及供水基本信息
     * 包含本日、昨日、本月供水量
     */
    @GetMapping("getWaterSupplyInfo")
    public IstarResponse getWaterSupplyInfo(@RequestParam(required = false, defaultValue = "") String projectId,
                                            @RequestParam(required = false, defaultValue = "") String name) throws ThingsboardException {
        return IstarResponse.ok(productionService.getWaterSupplyInfo(name, DataConstants.StationType.WATER_SOURCE.getValue(), projectId, getTenantId()));
    }

    /**
     * 盐亭大屏供水定制
     * @return
     */
    @GetMapping("getWaterSupplyTotal")
    public IstarResponse getWaterSupplyTotal() throws ThingsboardException {
        String time = new SimpleDateFormat("yyyyMMddHH").format(new Date());
        return IstarResponse.ok(productionService.getWaterSupplyTotal(time, DataConstants.StationType.WATER_SOURCE.getValue(), getTenantId()));
    }

    /**
     * 盐亭大屏供水定制
     * @return
     */
    @GetMapping("getWaterSupplyAllTotal")
    public IstarResponse getWaterSupplyTotalAll(String name) throws ThingsboardException {
        String time = new SimpleDateFormat("yyyyMMddHH").format(new Date());
        return IstarResponse.ok(productionService.getWaterSupplyTotalAll(time, name, getTenantId()));
    }


    @GetMapping("waterPool/getList")
    public IstarResponse getList(@RequestParam(required = false, defaultValue = "") String projectId,
                                 @RequestParam(required = false, defaultValue = "") String name,
                                 @RequestParam String status) throws ThingsboardException {
        String type = DataConstants.StationType.WATER_POOL.getValue();
        return IstarResponse.ok(productionService.getList(type, projectId, name, status, getTenantId()));
    }

    /**
     * 查询单个水源地的供水信息
     * 包含：今日供水曲线、昨日供水曲线、本月供水曲线、今日出口压力、今日出口瞬时流量
     */
    @GetMapping("getWaterSupplyDetail")
    public IstarResponse getWaterSupplyDetail(@RequestParam String stationId) throws ThingsboardException {
        StationEntity station = stationFeignClient.get(stationId);
        if (station == null) {
            return IstarResponse.error("[查询单个水源地供水信息异常] 要查询的水源地不存在, 水源地ID: " + stationId);
        }
        // 供水曲线数据
        JSONObject waterSupplyDetail = productionService.getWaterSupplyDetail(stationId, getTenantId());

        // 今日出水压力、今日出口瞬时流量
        List<String> attrList = new ArrayList<>();
        attrList.add(DataConstants.DeviceAttrType.PRESSURE.getValue());
        attrList.add(DataConstants.DeviceAttrType.INSTANTANEOUS_FLOW.getValue());
        // 今日时间
        Calendar instance = Calendar.getInstance();
        instance.set(Calendar.HOUR_OF_DAY, 0);
        instance.set(Calendar.MINUTE, 0);
        instance.set(Calendar.SECOND, 0);
        Date todayStart = instance.getTime();

        Map<String, List<JSONObject>> stationDataMap = productionService.getStationData(
                stationId, DataConstants.DeviceAttrGroupType.WATER_OUTLET.getValue(), attrList, DateUtils.HOUR, todayStart, new Date(), getTenantId());

        for (Map.Entry<String, List<JSONObject>> entry : stationDataMap.entrySet()) {
            waterSupplyDetail.put(entry.getKey(), entry.getValue());
        }

        return IstarResponse.ok(waterSupplyDetail);
    }

    /**
     * 查询水源地供水量总览
     */
    @GetMapping("getWaterSupplyInfoView")
    public IstarResponse getWaterSupplyInfoView(@RequestParam(required = false, defaultValue = "") String projectId) throws ThingsboardException {
        return IstarResponse.ok(productionService.getWaterSupplyInfoView(DataConstants.StationType.WATER_SOURCE.getValue(), projectId, getTenantId()));
    }

    /**
     * 查询水源地监测数据项实时数据
     */
    @GetMapping("getWaterSupplyInfoDetail")
    public IstarResponse getWaterSupplyInfoDetail(@RequestParam(required = false, defaultValue = "") String projectId) throws ThingsboardException {
        return IstarResponse.ok(stationDataService.getStationDataDetailListView(DataConstants.StationType.WATER_SOURCE.getValue(), true, projectId, getTenantId()));
    }

    /**
     * 查询指定站点的水量报表
     */
    @GetMapping("getWaterSupplyReport")
    public IstarResponse getWaterSupplyReport(@RequestParam Long start, @RequestParam Long end,
                                              @RequestParam String stationId, @RequestParam String queryType) {
        try {
            return IstarResponse.ok(productionService.getWaterSupplyReport(stationId, start, end, queryType, getTenantId()));
        } catch (ThingsboardException e) {
            return IstarResponse.error(e.getMessage());
        }
    }


    /**
     * 查询指定站点的单耗
     */
    @GetMapping("getWaterSupplyConsumptionReport")
    public IstarResponse getWaterSupplyConsumptionReport(@RequestParam Long start, @RequestParam Long end,
                                              @RequestParam String stationId, @RequestParam String queryType) {
        try {
            return IstarResponse.ok(productionService.getWaterSupplyConsumptionReport(stationId, start, end, queryType, getTenantId()));
        } catch (ThingsboardException e) {
            return IstarResponse.error(e.getMessage());
        }
    }

    /**
     * 查询多个站点的水量报表
     */
    @GetMapping("getWaterSupplyDetailReport")
    public IstarResponse getWaterSupplyDetailReport(@RequestParam Long start, @RequestParam Long end,
                                              @RequestParam String stationIdList, @RequestParam String queryType) {
        try {
            String[] stationIdArray = stationIdList.split(",");
            return IstarResponse.ok(productionService.getWaterSupplyDetailReport(DataConstants.StationType.WATER_SOURCE.getValue(), Arrays.stream(stationIdArray).collect(Collectors.toList()), start, end, queryType, getTenantId(), true));
        } catch (ThingsboardException e) {
            return IstarResponse.error(e.getMessage());
        }
    }

    /**
     * 查询指定区域的取水量报表
     */
    @GetMapping("getWaterSupplyDetailReportByProject")
    public IstarResponse getWaterSupplyDetailReportByProject(@RequestParam Long start, @RequestParam Long end,
                                              @RequestParam(required = false) String projectId, @RequestParam String queryType) {
        try {
            // 查询站点
            PageData<StationEntity> stationPageData = stationFeignClient.list(1, 99999, DataConstants.StationType.WATER_SOURCE.getValue(), projectId);
            if (stationPageData.getData() == null || stationPageData.getData().isEmpty()) {
                return IstarResponse.error("该区域下没有配置" + DataConstants.StationType.WATER_SOURCE.getValue());
            }
            List<String> stationIdList = stationPageData.getData().stream().map(StationEntity::getId).collect(Collectors.toList());
            DynamicTableVO tableVO = productionService.getWaterSupplyDetailReport(DataConstants.StationType.WATER_SOURCE.getValue(), stationIdList, start, end, queryType, getTenantId(), false);
            List<JSONObject> dataList = new ArrayList<>();
            List<JSONObject> tableDataList = tableVO.getTableDataList();
            if (tableDataList != null) {
                for (int i = 0; i < tableDataList.size(); i++) {
                    JSONObject data = tableDataList.get(i);
                    JSONObject newData = new JSONObject();
                    newData.put("ts", data.getString("ts"));
                    newData.put("total", data.get("total"));
                    dataList.add(newData);
                }
            }
            return IstarResponse.ok(dataList);
        } catch (ThingsboardException e) {
            return IstarResponse.error(e.getMessage());
        }
    }

    /**
     * 导出多个站点的水量报表
     */
    @GetMapping("getWaterSupplyDetailReport/export")
    public IstarResponse getWaterSupplyDetailReportExport(@RequestParam Long start, @RequestParam Long end,
                                                          @RequestParam String stationIdList, @RequestParam String queryType,
                                                          HttpServletResponse response) {
        try {
            String[] stationIdArray = stationIdList.split(",");
            DynamicTableVO dynamicTableVO = productionService.getWaterSupplyDetailReport(DataConstants.StationType.WATER_SOURCE.getValue(), Arrays.stream(stationIdArray).collect(Collectors.toList()), start, end, queryType, getTenantId(), true);

            List<DeviceDataTableInfoVO> tableInfo = dynamicTableVO.getTableInfo();
            List<JSONObject> data = dynamicTableVO.getTableDataList();

            // 数据列表
            Map headMap = new LinkedHashMap();
            for (DeviceDataTableInfoVO infoVO : tableInfo) {
                if (StringUtils.isNotBlank(infoVO.getUnit())) {
                    headMap.put(infoVO.getColumnValue(), infoVO.getColumnName() + "("+infoVO.getUnit()+")");
                } else {
                    headMap.put(infoVO.getColumnValue(), infoVO.getColumnName());
                }
            }

            // 水质报表
            String title = "供水明细报表";

            JSONArray dataList = JSONArray.parseArray(JSONObject.toJSONString(data));
            ExcelUtil.exportExcelX(title, headMap, dataList, "yyyy-MM-dd HH:mm:ss", 30, false, response);

            return IstarResponse.ok();
        } catch (ThingsboardException e) {
            return IstarResponse.error(e.getMessage());
        }
    }

    /**
     * 查询指定水源地站点的出水进水数据
     * 需要计算：进出水差值、差值率
     */
    @GetMapping("getWaterOutletAndInletReport")
    public IstarResponse getWaterOutletAndInletReport(@RequestParam Long start, @RequestParam Long end, @RequestParam String stationIdList) {
        try {
            String[] stationIdArray = stationIdList.split(",");
            return IstarResponse.ok(productionService.getWaterOutletAndInletReport(DataConstants.StationType.WATER_SOURCE.getValue(), Arrays.stream(stationIdArray).collect(Collectors.toList()), start, end, getTenantId()));
        } catch (ThingsboardException e) {
            return IstarResponse.error(e.getMessage());
        }
    }

    /**
     * 查询水源地列表的电耗数据
     * 包含：本期供水量、本期耗电量、本期上期吨水电耗、吨水电耗差值、变化率
     */
    @GetMapping("getWaterSupplyAndEnergyData")
    public IstarResponse getWaterSupplyAndEnergyData(@RequestParam Long start, @RequestParam Long end, @RequestParam String queryType,
                                                     @RequestParam(required = false) String name) {
        try {
            return IstarResponse.ok(productionService.getWaterSupplyAndEnergyData(DataConstants.StationType.WATER_SOURCE.getValue(), start, end, queryType, name, getTenantId()));
        } catch (ThingsboardException e) {
            return IstarResponse.error(e.getMessage());
        }
    }

    /**
     * 查询单个水源地的耗电数据详情
     */
    @GetMapping("getWaterSupplyAndEnergyDataDetail")
    public IstarResponse getWaterSupplyAndEnergyDataDetail(@RequestParam Long start, @RequestParam Long end,
                                                           @RequestParam String stationId, @RequestParam String queryType) {
        try {
            return IstarResponse.ok(productionService.getWaterSupplyAndEnergyDataDetail(stationId, queryType, start, end, getTenantId()));
        } catch (ThingsboardException e) {
            return IstarResponse.error(e.getMessage());
        }
    }

    /**
     * 查询水源地运行概况统计数据
     * 包含：取水量、用电量、吨水电耗、报警次数等关键指标
     */
    @GetMapping("getOperationOverview")
    public IstarResponse getOperationOverview(@RequestParam Long start, @RequestParam Long end,
                                              @RequestParam String queryType,
                                              @RequestParam(required = false, defaultValue = "") String projectId) {
        try {
            return IstarResponse.ok(productionService.getWaterSourceOperationOverview(start, end, queryType, projectId, getTenantId()));
        } catch (ThingsboardException e) {
            return IstarResponse.error(e.getMessage());
        }
    }

}
