package org.thingsboard.server.dao.sql.base;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.base.BaseMessageConfiguration;
import org.thingsboard.server.dao.util.imodel.query.base.BaseMessageConfigurationPageRequest;

import java.util.List;

/**
 * 平台管理-消息配置Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
@Mapper
public interface BaseMessageConfigurationMapper {
    /**
     * 查询平台管理-消息配置
     *
     * @param id 平台管理-消息配置主键
     * @return 平台管理-消息配置
     */
    public BaseMessageConfiguration selectBaseMessageConfigurationById(String id);

    /**
     * 查询平台管理-消息配置列表
     *
     * @param baseMessageConfiguration 平台管理-消息配置
     * @return 平台管理-消息配置集合
     */
    public IPage<BaseMessageConfiguration> selectBaseMessageConfigurationList(BaseMessageConfigurationPageRequest baseMessageConfiguration);

    /**
     * 新增平台管理-消息配置
     *
     * @param baseMessageConfiguration 平台管理-消息配置
     * @return 结果
     */
    public int insertBaseMessageConfiguration(BaseMessageConfiguration baseMessageConfiguration);

    /**
     * 修改平台管理-消息配置
     *
     * @param baseMessageConfiguration 平台管理-消息配置
     * @return 结果
     */
    public int updateBaseMessageConfiguration(BaseMessageConfiguration baseMessageConfiguration);

    /**
     * 删除平台管理-消息配置
     *
     * @param id 平台管理-消息配置主键
     * @return 结果
     */
    public int deleteBaseMessageConfigurationById(String id);

    /**
     * 批量删除平台管理-消息配置
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBaseMessageConfigurationByIds(@Param("array") List<String> ids);
}
