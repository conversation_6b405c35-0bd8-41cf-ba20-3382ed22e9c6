<!-- 工程管理-详情-设计变更 -->
<template>
  <CardTable :config="TableConfig" class="card-table"> </CardTable>
  <DialogForm ref="refForm" :config="addOrUpdateConfig"></DialogForm>
</template>

<script lang="ts" setup>
import { ElMessage } from 'element-plus';
import {
  postConstructionDesignAmend,
  getConstructionDesignAmendList,
  deleteConstructionDesign
} from '@/api/engineeringManagement/manage';
import { GenNonDuplicateID } from '@/utils/GlobalHelper';
import { SLConfirm } from '@/utils/Message';
import { ChangeType } from './data';

const refForm = ref<IDialogFormIns>();
const props = defineProps<{ id: string; config: any }>();

const TableConfig = reactive<ICardTable>({
  defaultExpandAll: true,
  indexVisible: true,
  title: '设计变更',
  titleRight: [
    {
      style: {
        justifyContent: 'flex-end'
      },
      items: [
        {
          type: 'btn-group',
          btns: [
            {
              text: '添加',
              perm: true,
              click: () => {
                addOrUpdateConfig.defaultValue = {
                  code: `${props.config.code || ''}-${GenNonDuplicateID()}`,
                  designCode: props.config.code || '',
                  constructionCode: props.config.constructionCode,
                  type: '提高合理化建议'
                };
                refForm.value?.openDialog();
              }
            }
          ]
        }
      ]
    }
  ],
  columns: [
    { label: '变更编号', prop: 'code' },
    { label: '变更类型', prop: 'type' },
    { label: '添加人', prop: 'creatorName' },
    { label: '添加时间', prop: 'createTimeName' },
    { label: '说明', prop: 'remark' },
    { label: '附件', prop: 'attachments', download: true }
  ],
  operationWidth: '160px',
  operations: [
    {
      isTextBtn: false,
      type: 'success',
      text: '编辑设计',
      perm: true,
      click: (row) => clickEdit(row)
    },
    {
      isTextBtn: false,
      type: 'danger',
      text: '删除',
      perm: true,
      click: (row) => handleDelete(row)
    }
  ],
  dataList: [],
  pagination: {
    hide: true
  }
});

const addOrUpdateConfig = reactive<IDialogFormConfig>({
  title: '添加设计变更',
  labelWidth: '130px',
  dialogWidth: '1000px',
  submitting: false,
  submit: (params: any) => {
    addOrUpdateConfig.submitting = true;
    let text = '新增';
    if (params.id) text = '修改';
    params.pipLengthDesign = JSON.stringify(params.pipLengthDesign);
    postConstructionDesignAmend(params)
      .then((res) => {
        addOrUpdateConfig.submitting = false;
        if (res.data.code === 200) {
          ElMessage.success(text + '成功');
          refForm.value?.closeDialog();
          refreshData();
        } else {
          ElMessage.warning(text + '失败');
        }
      })
      .catch((error) => {
        addOrUpdateConfig.submitting = false;
        ElMessage.warning(error);
      });
  },
  defaultValue: {},
  group: [
    {
      fields: [
        {
          xs: 12,
          type: 'input',
          label: '变更编号',
          field: 'code',
          disabled: true
        },
        {
          xs: 12,
          type: 'input',
          label: '设计编号',
          field: 'designCode',
          disabled: true
        },
        {
          xs: 12,
          type: 'select',
          label: '变更类型',
          field: 'type',
          options: ChangeType
        },
        {
          type: 'textarea',
          label: '说明',
          field: 'remark'
        },
        {
          type: 'file',
          label: '附件',
          field: 'attachments'
        }
      ]
    }
  ]
});

const clickEdit = (row: { [x: string]: any }) => {
  addOrUpdateConfig.title = '编辑设计变更';
  addOrUpdateConfig.defaultValue = {
    ...(row || {}),
    constructionCode: props.config.constructionCode
  };
  refForm.value?.openDialog();
};

const handleDelete = (row?: any) => {
  SLConfirm('确定删除？', '提示信息')
    .then(() => {
      deleteConstructionDesign(row.id).then((res) => {
        if (res.data?.code === 200) {
          ElMessage.success('删除成功');
          refreshData();
        } else {
          ElMessage.warning(res.data?.message);
        }
      });
    })
    .catch(() => {
      //
    });
};

const refreshData = async () => {
  getConstructionDesignAmendList({
    page: 1,
    size: -1,
    designCode: props.config.code,
    constructionCode: props.config.constructionCode
  }).then((res) => {
    TableConfig.dataList = res.data.data.data || [];
  });
};

onMounted(() => {
  refreshData();
});
</script>

<style lang="scss" scoped>
.card-table {
  height: 300px;
  margin-bottom: 20px;
}
</style>
