package org.thingsboard.server.controller.base;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.thingsboard.server.dao.base.IBaseDatabaseSourceService;
import org.thingsboard.server.dao.model.sql.base.BaseDatabaseSource;
import org.thingsboard.server.dao.util.imodel.query.base.BaseDatabaseSourcePageRequest;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;
import org.thingsboard.server.service.aspect.annotation.MonitorPerformance;

import java.util.List;

/**
 * 平台管理-多数据源Controller
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
@Api(tags = "平台管理-多数据源")
@RestController
@RequestMapping("api/base/database/source")
public class BaseDatabaseSourceController extends BaseController {

    @Autowired
    private IBaseDatabaseSourceService baseDatabaseSourceService;

    /**
     * 查询平台管理-多数据源列表
     */
    @MonitorPerformance(description = "平台管理-查询多数据源列表")
    @ApiOperation(value = "查询多数据源列表")
    @GetMapping("/list")
    public IstarResponse list(BaseDatabaseSourcePageRequest baseDatabaseSource) {
        return IstarResponse.ok(baseDatabaseSourceService.selectBaseDatabaseSourceList(baseDatabaseSource));
    }

    /**
     * 获取平台管理-多数据源详细信息
     */
    @MonitorPerformance(description = "平台管理-查询多数据源详情")
    @ApiOperation(value = "查询多数据源详情")
    @GetMapping(value = "/getDetail")
    public IstarResponse getInfo(String id) {
        return IstarResponse.ok(baseDatabaseSourceService.selectBaseDatabaseSourceById(id));
    }

    /**
     * 新增平台管理-多数据源
     */
    @MonitorPerformance(description = "平台管理-新增多数据源")
    @ApiOperation(value = "新增多数据源")
    @PostMapping("/add")
    public IstarResponse add(@RequestBody BaseDatabaseSource baseDatabaseSource) {
        return IstarResponse.ok(baseDatabaseSourceService.insertBaseDatabaseSource(baseDatabaseSource));
    }

    /**
     * 修改平台管理-多数据源
     */
    @MonitorPerformance(description = "平台管理-修改多数据源")
    @ApiOperation(value = "修改多数据源")
    @PostMapping("/edit")
    public IstarResponse edit(@RequestBody BaseDatabaseSource baseDatabaseSource) {
        return IstarResponse.ok(baseDatabaseSourceService.updateBaseDatabaseSource(baseDatabaseSource));
    }

    /**
     * 删除平台管理-多数据源
     */
    @MonitorPerformance(description = "平台管理-删除多数据源")
    @ApiOperation(value = "删除多数据源")
    @DeleteMapping("/deleteIds")
    public IstarResponse remove(@RequestBody List<String> ids) {
        return IstarResponse.ok(baseDatabaseSourceService.deleteBaseDatabaseSourceByIds(ids));
    }
}
