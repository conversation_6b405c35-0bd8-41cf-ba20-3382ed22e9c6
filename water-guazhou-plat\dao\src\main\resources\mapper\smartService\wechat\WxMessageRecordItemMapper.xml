<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartService.wechat.WxMessageRecordItemMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->id,
                           message_record_id,
                           message_id,
                           open_id,
                           send_date,
                           tenant_id<!--@sql from wx_message_record_item -->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.smartService.wechat.WxMessageRecordItem">
        <result column="id" property="id"/>
        <result column="message_record_id" property="messageRecordId"/>
        <result column="message_id" property="messageId"/>
        <result column="open_id" property="openId"/>
        <result column="send_date" property="sendDate"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wx_message_record_item
        <where>
            <if test="recordId != null and recordId != ''">
                and message_record_id = #{recordId}
            </if>
            <if test="status != null">
                <choose>
                    <when test="status == 0">
                        and message_id = -1
                    </when>
                    <when test="status == 1">
                        and message_id = 0
                    </when>
                    <when test="status == 2">
                        and message_id > 0
                    </when>
                    <when test="status == 3">
                        and message_id &lt;= 0
                    </when>
                </choose>
            </if>
            and tenant_id = #{tenantId}
        </where>
    </select>

    <insert id="saveAll">
        INSERT INTO wx_message_record_item(id,
                                           message_record_id,
                                           message_id,
                                           open_id,
                                           send_date,
                                           tenant_id)VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (#{element.id},
             #{element.messageRecordId},
             #{element.messageId},
             #{element.openId},
             #{element.sendDate},
             #{element.tenantId})
        </foreach>
    </insert>

    <update id="markMessageItemComplete">
        update wx_message_record_item
        set message_id = #{messageId},
            send_date  = now()
        where message_record_id = #{recordId}
    </update>

    <resultMap id="WxMessageRecordItemCompleteStatisticResultMap"
               type="org.thingsboard.server.dao.model.sql.smartService.wechat.WxMessageRecordItemCompleteStatistic">
        <result column="pending_count" property="pendingCount"/>
        <result column="failed_count" property="failedCount"/>
        <result column="completed_count" property="completedCount"/>
    </resultMap>
    <select id="completeStatistic"
            resultMap="WxMessageRecordItemCompleteStatisticResultMap">
        select
        <!--@formatter:off-->
            (select count(1) from wx_message_record_item where message_id = 0 and tenant_id = #{tenantId} and message_record_id like '%'|| #{messageRecordId} ||'%') pending_count,
            (select count(1) from wx_message_record_item where message_id = -1 and tenant_id = #{tenantId} and message_record_id like '%'|| #{messageRecordId} ||'%') failed_count,
            (select count(1) from wx_message_record_item where message_id > 0 and tenant_id = #{tenantId} and message_record_id like '%'|| #{messageRecordId} ||'%') completed_count
        <!--@formatter:on-->
    </select>
</mapper>