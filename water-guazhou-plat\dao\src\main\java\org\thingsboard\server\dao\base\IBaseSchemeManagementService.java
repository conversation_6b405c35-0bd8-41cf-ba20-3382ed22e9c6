package org.thingsboard.server.dao.base;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.base.BaseSchemeManagement;
import org.thingsboard.server.dao.util.imodel.query.base.BaseSchemeManagementPageRequest;

import java.util.List;

/**
 * 平台管理-方案管理Service接口
 *
 * <AUTHOR>
 * @date 2025-05-22
 */
public interface IBaseSchemeManagementService {
    /**
     * 查询平台管理-方案管理
     *
     * @param id 平台管理-方案管理主键
     * @return 平台管理-方案管理
     */
    public BaseSchemeManagement selectBaseSchemeManagementById(String id);

    /**
     * 查询平台管理-方案管理列表
     *
     * @param baseSchemeManagement 平台管理-方案管理
     * @return 平台管理-方案管理集合
     */
    public IPage<BaseSchemeManagement> selectBaseSchemeManagementList(BaseSchemeManagementPageRequest baseSchemeManagement);

    /**
     * 新增平台管理-方案管理
     *
     * @param baseSchemeManagement 平台管理-方案管理
     * @return 结果
     */
    public int insertBaseSchemeManagement(BaseSchemeManagement baseSchemeManagement);

    /**
     * 修改平台管理-方案管理
     *
     * @param baseSchemeManagement 平台管理-方案管理
     * @return 结果
     */
    public int updateBaseSchemeManagement(BaseSchemeManagement baseSchemeManagement);

    /**
     * 批量删除平台管理-方案管理
     *
     * @param ids 需要删除的平台管理-方案管理主键集合
     * @return 结果
     */
    public int deleteBaseSchemeManagementByIds(List<String> ids);

    /**
     * 删除平台管理-方案管理信息
     *
     * @param id 平台管理-方案管理主键
     * @return 结果
     */
    public int deleteBaseSchemeManagementById(String id);

    /**
     * 查询所有平台管理-方案管理数据
     * @return
     */
    public List<BaseSchemeManagement> getAllBaseSchemeManagement();
}
