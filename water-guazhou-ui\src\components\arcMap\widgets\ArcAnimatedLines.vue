<template>
  <div></div>
</template>
<script lang="ts" setup>
import AnimatedLinesLayer from '@/utils/arcgis/Layers/AnimatedLinesLayer';
import request from '@arcgis/core/request';
import * as webMercatorUtils from '@arcgis/core/geometry/support/webMercatorUtils';
import SpatialReference from '@arcgis/core/geometry/SpatialReference';
const view = inject('view') as __esri.MapView;
const layer = new AnimatedLinesLayer({
  id: 'layer-aminated-lines',
  title: '流动线'
});

view.map.layers.add(layer);
const refreshGraphics = () => {
  request(
    'https://arcgis.github.io/arcgis-samples-javascript/sample-data/custom-gl-animated-lines/lines.json',
    {
      responseType: 'json'
    }
  ).then((response) => {
    const graphics = response.data.map((trip) => {
      return {
        attributes: {
          color: trip.color
        },
        geometry: webMercatorUtils.geographicToWebMercator({
          paths: [trip.path],
          type: 'polyline',
          spatialReference: new SpatialReference({
            wkid: 4326
          })
        } as __esri.Polyline)
      };
    });
    layer.addMany(graphics)
    view.goTo(graphics);
  });
};
onMounted(() => {
  refreshGraphics();
});
onBeforeUnmount(() => {
  view.map.removeAll();
});
</script>
<style lang="scss" scoped></style>
