var F,G,H,I={},_={get exports(){return I},set exports(g){I=g}};F=_,G=function(){function g(t,r,n){n=n||2;var x,e,i,u,v,f,c,l=r&&r.length,s=l?r[0]*n:t.length,a=B(t,0,s,n,!0),y=[];if(!a||a.next===a.prev)return y;if(l&&(a=O(t,r,a,n)),t.length>80*n){x=i=t[0],e=u=t[1];for(var o=n;o<s;o+=n)(v=t[o])<x&&(x=v),(f=t[o+1])<e&&(e=f),v>i&&(i=v),f>u&&(u=f);c=(c=Math.max(i-x,u-e))!==0?1/c:0}return d(a,y,n,x,e,c),y}function B(t,r,n,x,e){var i,u;if(e===A(t,r,n,x)>0)for(i=r;i<n;i+=x)u=E(i,t[i],t[i+1],u);else for(i=n-x;i>=r;i-=x)u=E(i,t[i],t[i+1],u);if(u&&z(u,u.next)){var v=u.next;M(u),u=v}return u}function h(t,r){if(!t)return t;r||(r=t);var n,x=t;do if(n=!1,x.steiner||!z(x,x.next)&&p(x.prev,x,x.next)!==0)x=x.next;else{var e=x.prev;if(M(x),(x=r=e)===x.next)break;n=!0}while(n||x!==r);return r}function d(t,r,n,x,e,i,u){if(t){!u&&i&&T(t,x,e,i);for(var v,f,c=t;t.prev!==t.next;)if(v=t.prev,f=t.next,i?K(t,x,e,i):J(t))r.push(v.i/n),r.push(t.i/n),r.push(f.i/n),M(t),t=f.next,c=f.next;else if((t=f)===c){u?u===1?d(t=L(h(t),r,n),r,n,x,e,i,2):u===2&&N(t,r,n,x,e,i):d(h(t),r,n,x,e,i,1);break}}}function J(t){var r=t.prev,n=t,x=t.next;if(p(r,n,x)>=0)return!1;for(var e=t.next.next;e!==t.prev;){if(Z(r.x,r.y,n.x,n.y,x.x,x.y,e.x,e.y)&&p(e.prev,e,e.next)>=0)return!1;e=e.next}return!0}function K(t,r,n,x){var e=t.prev,i=t,u=t.next;if(p(e,i,u)>=0)return!1;for(var v=e.x<i.x?e.x<u.x?e.x:u.x:i.x<u.x?i.x:u.x,f=e.y<i.y?e.y<u.y?e.y:u.y:i.y<u.y?i.y:u.y,c=e.x>i.x?e.x>u.x?e.x:u.x:i.x>u.x?i.x:u.x,l=e.y>i.y?e.y>u.y?e.y:u.y:i.y>u.y?i.y:u.y,s=j(v,f,r,n,x),a=j(c,l,r,n,x),y=t.prevZ,o=t.nextZ;y&&y.z>=s&&o&&o.z<=a;){if(y!==t.prev&&y!==t.next&&Z(e.x,e.y,i.x,i.y,u.x,u.y,y.x,y.y)&&p(y.prev,y,y.next)>=0||(y=y.prevZ,o!==t.prev&&o!==t.next&&Z(e.x,e.y,i.x,i.y,u.x,u.y,o.x,o.y)&&p(o.prev,o,o.next)>=0))return!1;o=o.nextZ}for(;y&&y.z>=s;){if(y!==t.prev&&y!==t.next&&Z(e.x,e.y,i.x,i.y,u.x,u.y,y.x,y.y)&&p(y.prev,y,y.next)>=0)return!1;y=y.prevZ}for(;o&&o.z<=a;){if(o!==t.prev&&o!==t.next&&Z(e.x,e.y,i.x,i.y,u.x,u.y,o.x,o.y)&&p(o.prev,o,o.next)>=0)return!1;o=o.nextZ}return!0}function L(t,r,n){var x=t;do{var e=x.prev,i=x.next.next;!z(e,i)&&C(e,x,x.next,i)&&w(e,i)&&w(i,e)&&(r.push(e.i/n),r.push(x.i/n),r.push(i.i/n),M(x),M(x.next),x=t=i),x=x.next}while(x!==t);return h(x)}function N(t,r,n,x,e,i){var u=t;do{for(var v=u.next.next;v!==u.prev;){if(u.i!==v.i&&W(u,v)){var f=D(u,v);return u=h(u,u.next),f=h(f,f.next),d(u,r,n,x,e,i),void d(f,r,n,x,e,i)}v=v.next}u=u.next}while(u!==t)}function O(t,r,n,x){var e,i,u,v=[];for(e=0,i=r.length;e<i;e++)(u=B(t,r[e]*x,e<i-1?r[e+1]*x:t.length,x,!1))===u.next&&(u.steiner=!0),v.push(V(u));for(v.sort(P),e=0;e<v.length;e++)n=h(n=Q(v[e],n),n.next);return n}function P(t,r){return t.x-r.x}function k(t){if(t.next.prev===t)return t;let r=t;for(;;){const n=r.next;if(n.prev===r||n===r||n===t)break;r=n}return r}function Q(t,r){var n=R(t,r);if(!n)return r;var x=D(n,t),e=h(n,n.next);let i=k(x);return h(i,i.next),e=k(e),k(r===n?e:r)}function R(t,r){var n,x=r,e=t.x,i=t.y,u=-1/0;do{if(i<=x.y&&i>=x.next.y&&x.next.y!==x.y){var v=x.x+(i-x.y)*(x.next.x-x.x)/(x.next.y-x.y);if(v<=e&&v>u){if(u=v,v===e){if(i===x.y)return x;if(i===x.next.y)return x.next}n=x.x<x.next.x?x:x.next}}x=x.next}while(x!==r);if(!n)return null;if(e===u)return n;var f,c=n,l=n.x,s=n.y,a=1/0;x=n;do e>=x.x&&x.x>=l&&e!==x.x&&Z(i<s?e:u,i,l,s,i<s?u:e,i,x.x,x.y)&&(f=Math.abs(i-x.y)/(e-x.x),w(x,t)&&(f<a||f===a&&(x.x>n.x||x.x===n.x&&S(n,x)))&&(n=x,a=f)),x=x.next;while(x!==c);return n}function S(t,r){return p(t.prev,t,r.prev)<0&&p(r.next,t,t.next)<0}function T(t,r,n,x){var e=t;do e.z===null&&(e.z=j(e.x,e.y,r,n,x)),e.prevZ=e.prev,e.nextZ=e.next,e=e.next;while(e!==t);e.prevZ.nextZ=null,e.prevZ=null,U(e)}function U(t){var r,n,x,e,i,u,v,f,c=1;do{for(n=t,t=null,i=null,u=0;n;){for(u++,x=n,v=0,r=0;r<c&&(v++,x=x.nextZ);r++);for(f=c;v>0||f>0&&x;)v!==0&&(f===0||!x||n.z<=x.z)?(e=n,n=n.nextZ,v--):(e=x,x=x.nextZ,f--),i?i.nextZ=e:t=e,e.prevZ=i,i=e;n=x}i.nextZ=null,c*=2}while(u>1);return t}function j(t,r,n,x,e){return(t=1431655765&((t=858993459&((t=252645135&((t=16711935&((t=32767*(t-n)*e)|t<<8))|t<<4))|t<<2))|t<<1))|(r=1431655765&((r=858993459&((r=252645135&((r=16711935&((r=32767*(r-x)*e)|r<<8))|r<<4))|r<<2))|r<<1))<<1}function V(t){var r=t,n=t;do(r.x<n.x||r.x===n.x&&r.y<n.y)&&(n=r),r=r.next;while(r!==t);return n}function Z(t,r,n,x,e,i,u,v){return(e-u)*(r-v)-(t-u)*(i-v)>=0&&(t-u)*(x-v)-(n-u)*(r-v)>=0&&(n-u)*(i-v)-(e-u)*(x-v)>=0}function W(t,r){return t.next.i!==r.i&&t.prev.i!==r.i&&!X(t,r)&&(w(t,r)&&w(r,t)&&Y(t,r)&&(p(t.prev,t,r.prev)||p(t,r.prev,r))||z(t,r)&&p(t.prev,t,t.next)>0&&p(r.prev,r,r.next)>0)}function p(t,r,n){return(r.y-t.y)*(n.x-r.x)-(r.x-t.x)*(n.y-r.y)}function z(t,r){return t.x===r.x&&t.y===r.y}function C(t,r,n,x){var e=m(p(t,r,n)),i=m(p(t,r,x)),u=m(p(n,x,t)),v=m(p(n,x,r));return e!==i&&u!==v||!(e!==0||!b(t,n,r))||!(i!==0||!b(t,x,r))||!(u!==0||!b(n,t,x))||!(v!==0||!b(n,r,x))}function b(t,r,n){return r.x<=Math.max(t.x,n.x)&&r.x>=Math.min(t.x,n.x)&&r.y<=Math.max(t.y,n.y)&&r.y>=Math.min(t.y,n.y)}function m(t){return t>0?1:t<0?-1:0}function X(t,r){var n=t;do{if(n.i!==t.i&&n.next.i!==t.i&&n.i!==r.i&&n.next.i!==r.i&&C(n,n.next,t,r))return!0;n=n.next}while(n!==t);return!1}function w(t,r){return p(t.prev,t,t.next)<0?p(t,r,t.next)>=0&&p(t,t.prev,r)>=0:p(t,r,t.prev)<0||p(t,t.next,r)<0}function Y(t,r){var n=t,x=!1,e=(t.x+r.x)/2,i=(t.y+r.y)/2;do n.y>i!=n.next.y>i&&n.next.y!==n.y&&e<(n.next.x-n.x)*(i-n.y)/(n.next.y-n.y)+n.x&&(x=!x),n=n.next;while(n!==t);return x}function D(t,r){var n=new q(t.i,t.x,t.y),x=new q(r.i,r.x,r.y),e=t.next,i=r.prev;return t.next=r,r.prev=t,n.next=e,e.prev=n,x.next=n,n.prev=x,i.next=x,x.prev=i,x}function E(t,r,n,x){var e=new q(t,r,n);return x?(e.next=x.next,e.prev=x,x.next.prev=e,x.next=e):(e.prev=e,e.next=e),e}function M(t){t.next.prev=t.prev,t.prev.next=t.next,t.prevZ&&(t.prevZ.nextZ=t.nextZ),t.nextZ&&(t.nextZ.prevZ=t.prevZ)}function q(t,r,n){this.i=t,this.x=r,this.y=n,this.prev=null,this.next=null,this.z=null,this.prevZ=null,this.nextZ=null,this.steiner=!1}function A(t,r,n,x){for(var e=0,i=r,u=n-x;i<n;i+=x)e+=(t[u]-t[i])*(t[i+1]+t[u+1]),u=i;return e}return g.deviation=function(t,r,n,x){var e=r&&r.length,i=e?r[0]*n:t.length,u=Math.abs(A(t,0,i,n));if(e)for(var v=0,f=r.length;v<f;v++){var c=r[v]*n,l=v<f-1?r[v+1]*n:t.length;u-=Math.abs(A(t,c,l,n))}var s=0;for(v=0;v<x.length;v+=3){var a=x[v]*n,y=x[v+1]*n,o=x[v+2]*n;s+=Math.abs((t[a]-t[o])*(t[y+1]-t[a+1])-(t[a]-t[y])*(t[o+1]-t[a+1]))}return u===0&&s===0?0:Math.abs((s-u)/u)},g.flatten=function(t){for(var r=t[0][0].length,n={vertices:[],holes:[],dimensions:r},x=0,e=0;e<t.length;e++){for(var i=0;i<t[e].length;i++)for(var u=0;u<r;u++)n.vertices.push(t[e][i][u]);e>0&&(x+=t[e-1].length,n.holes.push(x))}return n},g},(H=G())!==void 0&&(F.exports=H);export{I as r};
