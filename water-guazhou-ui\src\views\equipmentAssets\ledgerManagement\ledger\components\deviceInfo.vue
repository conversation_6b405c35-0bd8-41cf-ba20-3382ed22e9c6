<template>
  <!--设备信息 -->
  <div style="width:calc(100% - 20px)">
    <descriptions
      :key="key"
      ref="refForm"
      :config="addOrUpdateConfig"
    ></descriptions>
  </div>
</template>

<script lang="ts" setup>
import { IDescriptionsIns } from '@/components/type'
import { getBasicInformation } from '@/api/equipment_assets/ledgerManagement'
import { getCustomize } from '@/api/equipment_assets/equipmentManage'
import descriptions from '@/components/Descriptions/index.vue'

const refForm = ref<IDescriptionsIns>()

const props = defineProps<{id:string, serialId:string}>()

const key = ref((new Date()).toString())

const addOrUpdateConfig = reactive<IDescriptionsConfig>({
  title: '',
  defaultValue: {},
  border: true,
  direction: 'vertical',
  labelAlign:'left',
  fields: [
    {
      xl: 8,
      type: 'text',
      label: '设备编号:',
      field: 'serialId',
    }, {
      xl: 8,
      type: 'text',
      label: '设备名称:',
      field: 'deviceName'
    }, {
      xl: 8,
      type: 'text',
      label: '设备单位:',
      field: 'deviceUnit'
    }, {
      xl: 8,
      type: 'text',
      label: '设备类型:',
      field: 'deviceType'
    }, {
      xl: 8,
      type: 'text',
      label: '设备型号:',
      field: 'deviceModel'
    }, {
      xl: 8,
      type: 'text',
      label: '供应商名称:',
      field: 'supplierName'
    }, {
      xl: 8,
      type: 'text',
      label: '采购金额:',
      field: 'price'
    }, {
      xl: 8,
      type: 'text',
      label: '入库时间:',
      field: 'createTime'
    }, {
      xl: 8,
      type: 'text',
      label: '使用年限:',
      field: 'deviceUseYear'
    }, {
      xl: 8,
      type: 'text',
      label: '出库时间:',
      field: 'outTime'
    }, {
      xl: 8,
      type: 'text',
      label: '报废时间:',
      field: 'scrappedTime'
    }, {
      xl: 8,
      type: 'text',
      label: '备注:',
      field: 'remark'
    }, {
      xl: 8,
      type: 'text',
      label: '租户名称:',
      field: 'tenantName'
    }, {
      xl: 24,
      readonly: true,
      type: 'image',
      label: '设备图片:',
      field: 'deviceImages'
    },
    {
      xl: 24,
      readonly: true,
      type: 'file',
      label: '设备文件:',
      field: 'deviceFiles'
    }

  ]
})

const data = reactive({
  prepend: '',
  serialId: '',
  // 自定义属性
  customize: [] as any[],
  getCustomizeValue: id => {
    data.customize = []
    getCustomize(id).then(res => {
      const row = res.data.data || []
      row.forEach(item => {
        const key = {
          xl: 8,
          type: 'text',
          label: item.name + ':',
          field: 'autoField' + item.code
        }
        data.customize.push(key)
      })
      addOrUpdateConfig.fields.splice(12, 0, ...data.customize)
      refreshData()
    })
  }
})

const refreshData = async () => {
  getBasicInformation(props.id).then(res => {
    const row = res.data.data || {}
    for (const i in row) {
      if (row[i] === undefined || row[i] === null) row[i] = ' '
    }

    const autoFields = JSON.parse(row.autoField==' '?'{}':row.autoField)
    for(const key in autoFields){
      autoFields[key.split('.')[0]+key.split('.')[1]] = autoFields[key]
    }
    addOrUpdateConfig.defaultValue = { ...(row) || {}, ...autoFields }
    setTimeout(() => {
      key.value = (new Date()).toString()
    }, 1000)
  })
}

onMounted(() => {
  data.getCustomizeValue(props.serialId.slice(0, -6) + '000000')
})
</script>
