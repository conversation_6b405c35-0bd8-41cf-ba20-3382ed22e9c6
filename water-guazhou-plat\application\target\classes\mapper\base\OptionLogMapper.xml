<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.base.OptionLogMapper">
    
    <resultMap type="org.thingsboard.server.dao.model.sql.base.OptionLogList" id="OptionLogResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="tenantId"    column="tenant_id"    />
        <result property="firstName"    column="first_name"    />
        <result property="authority"    column="authority"    />
        <result property="options"    column="options"    />
        <result property="createTime"    column="create_time"    />
        <result property="additionalInfo"    column="additional_info"    />
        <result property="type"    column="type"    />
        <result property="info"    column="info"    />
    </resultMap>

    <sql id="selectConstantsAttributeVo">
        select id, user_id, tenant_id, first_name, authority, options, create_time, additional_info, type, info from option_log
    </sql>

    <select id="selectLogList" parameterType="org.thingsboard.server.dao.model.sql.base.OptionLogList" resultMap="OptionLogResult">
        <include refid="selectConstantsAttributeVo"/>
        <where>
            options = 'LOGIN'
            <if test="firstName != null  and firstName != ''"> and first_name like concat('%', #{firstName}, '%')</if>
        </where>
    </select>
    <select id="selectOperationLogList" resultType="org.thingsboard.server.dao.model.sql.base.OptionLogList">
        <include refid="selectConstantsAttributeVo"/>
        <where>
            options != 'LOGIN'
            <if test="firstName != null  and firstName != ''"> and first_name like concat('%', #{firstName}, '%')</if>
        </where>
    </select>
</mapper>