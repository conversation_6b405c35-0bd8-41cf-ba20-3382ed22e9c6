package org.thingsboard.server.dao.smartService.portal;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.smartService.portal.SsPortalSitePackage;
import org.thingsboard.server.dao.util.imodel.query.smartService.portal.SsPortalSitePackagePageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartService.portal.SsPortalSitePackageSaveRequest;

import java.util.List;

public interface SsPortalSitePackageService {
    SsPortalSitePackage findById(String id);

    IPage<SsPortalSitePackage> findAllConditional(SsPortalSitePackagePageRequest request);

    SsPortalSitePackage save(SsPortalSitePackageSaveRequest entity);

    boolean update(SsPortalSitePackage entity);

    boolean delete(String id);

    List<SsPortalSitePackage> tree(SsPortalSitePackagePageRequest request);

    boolean canBeDelete(String id);

    boolean canSave(SsPortalSitePackageSaveRequest request);

}
