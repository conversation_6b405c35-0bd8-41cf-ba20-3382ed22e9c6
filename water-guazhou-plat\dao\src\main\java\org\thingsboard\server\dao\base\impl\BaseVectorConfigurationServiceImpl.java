package org.thingsboard.server.dao.base.impl;

import java.util.List;
import java.util.UUID;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.base.IBaseVectorConfigurationService;
import org.thingsboard.server.dao.model.sql.base.BaseVectorConfiguration;
import org.thingsboard.server.dao.sql.base.BaseVectorConfigurationMapper;
import org.thingsboard.server.dao.util.imodel.query.base.BaseVectorConfigurationPageRequest;

/**
 * 公共管理平台-矢量数据配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-06
 */
@Service
public class BaseVectorConfigurationServiceImpl implements IBaseVectorConfigurationService {
    @Autowired
    private BaseVectorConfigurationMapper baseVectorConfigurationMapper;

    /**
     * 查询公共管理平台-矢量数据配置
     *
     * @param id 公共管理平台-矢量数据配置主键
     * @return 公共管理平台-矢量数据配置
     */
    @Override
    public BaseVectorConfiguration selectBaseVectorConfigurationById(String id) {
        return baseVectorConfigurationMapper.selectBaseVectorConfigurationById(id);
    }

    /**
     * 查询公共管理平台-矢量数据配置列表
     *
     * @param baseVectorConfiguration 公共管理平台-矢量数据配置
     * @return 公共管理平台-矢量数据配置
     */
    @Override
    public IPage<BaseVectorConfiguration> selectBaseVectorConfigurationList(BaseVectorConfigurationPageRequest baseVectorConfiguration) {
        return baseVectorConfigurationMapper.selectBaseVectorConfigurationList(baseVectorConfiguration);
    }

    /**
     * 新增公共管理平台-矢量数据配置
     *
     * @param baseVectorConfiguration 公共管理平台-矢量数据配置
     * @return 结果
     */
    @Override
    public int insertBaseVectorConfiguration(BaseVectorConfiguration baseVectorConfiguration) {
        baseVectorConfiguration.setId(UUID.randomUUID().toString().replace("-", ""));
        return baseVectorConfigurationMapper.insertBaseVectorConfiguration(baseVectorConfiguration);
    }

    /**
     * 修改公共管理平台-矢量数据配置
     *
     * @param baseVectorConfiguration 公共管理平台-矢量数据配置
     * @return 结果
     */
    @Override
    public int updateBaseVectorConfiguration(BaseVectorConfiguration baseVectorConfiguration) {
        return baseVectorConfigurationMapper.updateBaseVectorConfiguration(baseVectorConfiguration);
    }

    /**
     * 批量删除公共管理平台-矢量数据配置
     *
     * @param ids 需要删除的公共管理平台-矢量数据配置主键
     * @return 结果
     */
    @Override
    public int deleteBaseVectorConfigurationByIds(List<String> ids) {
        return baseVectorConfigurationMapper.deleteBaseVectorConfigurationByIds(ids);
    }

    /**
     * 删除公共管理平台-矢量数据配置信息
     *
     * @param id 公共管理平台-矢量数据配置主键
     * @return 结果
     */
    @Override
    public int deleteBaseVectorConfigurationById(String id) {
        return baseVectorConfigurationMapper.deleteBaseVectorConfigurationById(id);
    }

    @Override
    public List<BaseVectorConfiguration> selectAllBaseVectorConfiguration() {
        return baseVectorConfigurationMapper.selectAllBaseVectorConfiguration();
    }
}
