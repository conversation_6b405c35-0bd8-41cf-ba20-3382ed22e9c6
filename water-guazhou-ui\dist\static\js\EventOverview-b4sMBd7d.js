import{_ as q}from"./CardTable-rdWOL4_6.js";import{_ as J}from"./CardSearch-CB_HNR-Q.js";import{d as Y,c as m,r as D,s as R,l as d,bH as _,b as l,S as U,o as K,g as j,n as Q,q as h,F as O,p as I,m as X,b7 as Z,bq as tt,ak as et,C as rt}from"./index-r0dFAfgr.js";import{G as at,B as ot}from"./eventOverview-CiZ_uCa9.js";import{e as it}from"./config-DqqM5K5L.js";import st from"./RightDrawerMap-D5PhmGFO.js";import lt from"./EventOverviewForm-DdHPCwFu.js";import nt from"./ReviewDialog-DMgpi0fi.js";import{y as pt,bI as mt,g as ct}from"./MapView-DaoQedLH.js";import{w as ut}from"./Point-WxyopZva.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";import"./DateFormatter-Bm9a68Ax.js";import"./OutsideWorkOrder-C6s8joBt.js";import"./index-CpGhZCTT.js";import"./ArcView-DpMnCY82.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./widget-BcWKanF2.js";import"./pe-B8dP0-Ut.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./TileLayer-B5vQ99gG.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./commonProperties-DqNQ4F00.js";import"./project-DUuzYgGl.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./IdentifyResult-4DxLVhTm.js";import"./ViewHelper-BGCZjxXH.js";import"./fieldconfig-Bk3o1wi7.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./LayerHelper-Cn-iiqxI.js";import"./pipe-nogVzCHG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./ArcStationWarning-BF9YrSzF.js";/* empty css                         */import"./useWaterPoint-Bv0z6ym6.js";import"./zhandian-YaGuQZe6.js";import"./useStation-DJgnSZIA.js";import"./DrawerBox-CLde5xC8.js";import"./SideDrawer-CBntChyn.js";import"./PipeDetail-CTBPYFJW.js";import"./FormTableColumnFilter-BT7pLXIC.js";import"./QueryHelper-ILO3qZqg.js";import"./config-fy91bijz.js";import"./ArcBRTools-BO92yznB.js";import"./PipeLength.vue_vue_type_script_setup_true_lang-BZfUsvDf.js";import"./arcWidgetButton-0glIxrt7.js";import"./StatisticsHelper-D-s_6AyQ.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./wfsUtils-DXofo3da.js";import"./usePipelineGroup-Ba1pmWz_.js";import"./GroupLayer-DhhN3FyN.js";import"./lazyLayerLoader-DbM9sT1W.js";import"./WFSLayer-1TtJp3nx.js";import"./clientSideDefaults-VQhQaYxh.js";import"./QueryEngineCapabilities-Dk3NJwmm.js";import"./wfsUtils-Du031XaC.js";import"./geojson-MBFu2-HZ.js";import"./xmlUtils-CtUoQO7q.js";import"./ListWindow-WS05QqV0.js";import"./PopLayout-BP55MvL7.js";import"./PoiSearchV2-D7yNeLuv.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./URLHelper-B9aplt5w.js";import"./useLayerList-DmEwJ-ws.js";import"./useScaleBar-Beed-z91.js";import"./FormMap-BGaXSqQF.js";import"./ArcPipe.vue_vue_type_script_setup_true_lang-ClsVvV2P.js";import"./useUser-Blb5V02j.js";const dt={class:"wrapper",style:{"overflow-y":"auto"}},ft={class:"detail-wrapper"},vt={class:"detail-table"},gt=Y({__name:"EventOverview",setup(Et){const b=m(),x=m(),y=m(),u=m(!1),v=m(!1),g=m(!1),E=m(null),w=m(!1),N=m(null),T=m([]);let f=null;const V=D({defaultBaseMap:"img_w",defaultCenter:[95.787329,40.516879],zoom:10}),S=D({filters:[{field:"date",label:"创建时间",type:"daterange"},{field:"title",label:"名称",type:"input",placeholder:"请输入事件名称"},{field:"type",label:"类型",type:"cascader",options:[],props:{value:"id",label:"name",children:"children",emitPath:!0,checkStrictly:!1}},{field:"status",label:"状态",type:"select",options:[]}],operations:[{type:"btn-group",btns:[{perm:!0,type:"primary",text:"查询",icon:"iconfont icon-chaxun",click:()=>c()},{perm:!0,type:"default",text:"重置",svgIcon:R(Z),click:()=>k()},{perm:!0,type:"danger",text:"批量删除",disabled:()=>{var t;return!((t=i.selectList)!=null&&t.length)},svgIcon:R(tt),click:()=>$()},{perm:!0,type:"success",text:"新增事件",svgIcon:R(et),click:()=>M()}]}],handleSearch:()=>c(),defaultParams:{date:[d().subtract(7,"day").format(_),d().format(_)]}}),i=D({loading:!1,dataList:[],selectList:[],handleSelectChange:t=>{i.selectList=t,T.value=t},pagination:{refreshData:({page:t,size:e})=>{i.pagination.page=t,i.pagination.limit=e,c()}},selectable:t=>["PENDING_REVIEW","ASSIGNED","PROCESSING"].includes(t.status),columns:[{minWidth:150,prop:"title",label:"事件标题",showOverflowTooltip:!0},{minWidth:150,prop:"typeName",label:"类型"},{minWidth:200,prop:"address",label:"发生地点",showOverflowTooltip:!0,cellStyle:t=>({color:"#409eff",cursor:"pointer",textDecoration:"underline"}),handleClick:t=>{G(t.address,t.coordinate),setTimeout(()=>{var e;(e=y.value)==null||e.toggleCustomDetailMaxmin("normal")},1e3)}},{minWidth:120,prop:"status",label:"状态",tag:!0,tagColor:t=>({PENDING_REVIEW:"#e6a23c",ASSIGNED:"#409eff",PROCESSING:"#67c23a",COMPLETED:"#67c23a",REJECTED:"#f56c6c",PENDING:"#e6a23c",ASSIGN:"#409eff",RESOLVING:"#67c23a",ARRIVING:"#67c23a",SUBMIT:"#909399",REVIEW:"#909399",CHARGEBACK_REVIEW:"#909399",HANDOVER_REVIEW:"#909399",APPROVED:"#67c23a",COMPLETE:"#67c23a",TERMINATED:"#f56c6c",CHARGEBACK:"#f56c6c"})[t.status]||"#909399",formatter:t=>it[t.status]||t.statusName||"-"},{minWidth:100,prop:"organizerName",label:"创建人"},{minWidth:160,prop:"createTime",label:"创建时间",formatter:t=>t.createTime?d(t.createTime).format("YYYY-MM-DD HH:mm:ss"):"-"}],operations:[{perm:!0,text:"查看",type:"success",isTextBtn:!0,click:t=>P(t)},{perm:!0,text:"编辑",type:"primary",isTextBtn:!0,click:t=>L(t),hide:t=>t.status==="COMPLETED"||t.status==="PROCESSING"||t.status==="REJECTED"},{perm:!0,text:"再次发起",type:"primary",isTextBtn:!0,click:t=>A(t),hide:t=>t.status!=="REJECTED"},{perm:!0,text:"审核",type:"primary",isTextBtn:!0,click:t=>B(t),hide:t=>t.status!=="PENDING_REVIEW"}]}),c=async()=>{var t,e,o;i.loading=!0;try{const s=((t=b.value)==null?void 0:t.queryParams)||{},[r,a]=((e=s.date)==null?void 0:e.length)===2?[d(s.date[0],_).valueOf(),d(s.date[1],_).endOf("D").valueOf()]:[d().subtract(7,"day").startOf("D").valueOf(),d().endOf("D").valueOf()],n={page:i.pagination.page||1,size:i.pagination.limit||20,...s,fromTime:r,toTime:a};delete n.date,n.type&&Array.isArray(n.type)&&(n.type=n.type[n.type.length-1]);const C=(o=(await at(n)).data)==null?void 0:o.data;i.dataList=C.data||[],i.pagination.total=C.total||0}catch{l.error("查询失败")}i.loading=!1},k=()=>{var t;(t=b.value)==null||t.resetForm(),c()},G=(t,e)=>{if(!e){l.warning("该事件暂无位置信息");return}if(!f){l.warning("地图尚未加载完成，请稍后重试");return}try{const o=e.split(",");if(o.length!==2){l.error("坐标格式错误");return}const s=parseFloat(o[0]),r=parseFloat(o[1]);if(isNaN(s)||isNaN(r)){l.error("坐标数据无效");return}f.graphics.removeAll();const a=new ut({longitude:s,latitude:r,spatialReference:f.spatialReference}),n=new pt({color:[226,119,40],outline:{color:[255,255,255],width:2},size:14}),p=new mt({title:"事件位置",content:`
        <div style="padding: 8px;">
          <p><strong>地址：</strong>${t}</p>
          <p><strong>经度：</strong>${s}</p>
          <p><strong>纬度：</strong>${r}</p>
        </div>
      `}),C=new ct({geometry:a,symbol:n,popupTemplate:p,attributes:{address:t,longitude:s,latitude:r}});f.graphics.add(C),f.goTo({target:a,zoom:16}).catch(()=>{l.error("地图定位失败")}),l.success("已定位到事件位置")}catch{l.error("地图定位失败")}},M=()=>{v.value=!1,g.value=!1,E.value=null,u.value=!0},P=t=>{v.value=!1,g.value=!0,E.value={...t},u.value=!0},L=t=>{v.value=!0,g.value=!1,E.value={...t},u.value=!0},A=t=>{v.value=!0,g.value=!1,E.value={...t},u.value=!0},W=()=>{u.value=!1,c()},B=t=>{N.value=t,w.value=!0},F=()=>{w.value=!1,c()},$=async()=>{var t,e;try{if(!i.selectList||i.selectList.length===0){l.warning("请选择要删除的事件");return}if(i.selectList.filter(a=>!["PENDING_REVIEW","ASSIGNED","PROCESSING"].includes(a.status)).length>0){l.warning("只能删除待审核、已分派、处理中状态的事件");return}await U(`确认删除选中的 ${i.selectList.length} 个事件吗？`,"批量删除");const s=i.selectList.map(a=>a.id),r=await ot(s);if(r.code===200||((t=r.data)==null?void 0:t.code)===200){const a=r.data.data.successCount,n=r.data.data.totalCount,p=r.data.data.failedCount;a===n?l.success(`成功删除 ${a} 个事件`):l.warning(`成功删除 ${a} 个事件，失败 ${p} 个`)}else{l.error(((e=r.data)==null?void 0:e.msg)||r.message||"批量删除失败");return}i.selectList=[],T.value=[],c()}catch(o){o!=="cancel"&&(console.error("批量删除失败:",o),l.error("批量删除操作失败"))}},H=async()=>{var o,s,r;const t=(o=S.filters)==null?void 0:o.find(a=>a.field==="status");t&&(t.options=[{label:"待审核",value:"PENDING_REVIEW"},{label:"已分派",value:"ASSIGNED"},{label:"处理中",value:"PROCESSING"},{label:"已完成",value:"COMPLETED"},{label:"已驳回",value:"REJECTED"}]);const e=(s=S.filters)==null?void 0:s.find(a=>a.field==="type");if(e)try{const a=await X({url:"/api/workOrderType/list",method:"get",params:{status:1}});if((r=a.data)!=null&&r.data){const n=a.data.data.filter(p=>p.parentId==="0"||p.parentId===0||!p.parentId);e.options=n.map(p=>({id:p.id,name:p.name,children:p.children||[]}))}}catch(a){console.error("获取工单类型失败:",a),l.error("获取工单类型失败")}},z=t=>{f=t,setTimeout(()=>{var e,o;(e=y.value)==null||e.toggleCustomDetail(!0),(o=y.value)==null||o.toggleCustomDetailMaxmin("max")},100)};return K(async()=>{await H(),c()}),(t,e)=>{const o=J,s=q;return j(),Q("div",dt,[h(st,{ref_key:"refMap",ref:y,title:"事件总览","detail-max-min":!0,"hide-right-drawer":!0,"hide-detail-close":!0,"map-config":V,onMapLoaded:z},{"detail-header":O(()=>e[2]||(e[2]=[I("span",null,"事件总览",-1)])),"detail-default":O(()=>[I("div",ft,[h(o,{ref_key:"refSearch",ref:b,config:S,style:{"margin-bottom":"8px"}},null,8,["config"]),I("div",vt,[h(s,{ref_key:"refTable",ref:x,config:i},null,8,["config"])])])]),_:1},8,["map-config"]),h(lt,{modelValue:u.value,"onUpdate:modelValue":e[0]||(e[0]=r=>u.value=r),"is-edit":v.value,"is-view":g.value,"edit-data":E.value,onSubmit:W},null,8,["modelValue","is-edit","is-view","edit-data"]),h(nt,{modelValue:w.value,"onUpdate:modelValue":e[1]||(e[1]=r=>w.value=r),"event-data":N.value,onSubmit:F},null,8,["modelValue","event-data"])])}}}),Vr=rt(gt,[["__scopeId","data-v-a9a86943"]]);export{Vr as default};
