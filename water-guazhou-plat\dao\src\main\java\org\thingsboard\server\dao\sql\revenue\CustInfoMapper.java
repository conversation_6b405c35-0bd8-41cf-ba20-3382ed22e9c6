package org.thingsboard.server.dao.sql.revenue;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.revenue.CustInfo;

import java.util.List;
import java.util.Map;

@Mapper
public interface CustInfoMapper extends BaseMapper<CustInfo> {


    List<Map> sumWaterType(@Param("custCodeList") List<String> custCodeList, @Param("tenantId") String tenantId);

    CustInfo findOneByCodeAndTenantId(@Param("code") String code, @Param("tenantId") String tenantId);

    List<String> findOpenIdListByMeterBookId(@Param("meterBookIds") String meterBookIds, @Param("tenantId") String tenantId);

    List<String> findOpenIdListByDmaId(@Param("dmaIds") String dmaIds, @Param("tenantId") String tenantId);
}
