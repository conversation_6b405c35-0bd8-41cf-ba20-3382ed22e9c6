import{_ as F}from"./index-C9hz-UZb.js";import{d as z,j as B,M as K,a6 as U,r as _,bF as p,c as N,a8 as W,bX as H,s as L,bB as D,am as M,o as G,ah as X,bA as $,ay as J,g as Q,n as Y,q as d,i,F as y,cs as O,bo as S,bR as w,p as I,dF as Z,dA as ee,aq as te,b7 as ae,aj as oe,C as ne}from"./index-r0dFAfgr.js";import{_ as se}from"./CardSearch-CB_HNR-Q.js";import{e as re,d as le}from"./statisticalAnalysis-D5JxC4wJ.js";import{l as ce}from"./echart-D5stWtDc.js";import{u as ie}from"./useStation-DJgnSZIA.js";import"./Search-NSrhrIa_.js";import"./zhandian-YaGuQZe6.js";const de={class:"wrapper"},ue=z({__name:"index",setup(pe){const b=B(),{$messageWarning:P}=K(),j=U(),{getStationTree:q}=ie(),c=_({type:"day",chartOption:null,activeName:"echarts",dataList:{}}),T=p().date(),f=N(),g=N(),v=N(),o=_({data:[],checkedKeys:[],checkedNodes:[]}),k=_({defaultParams:{type:"day",year:p().format(),month:p().format(),day:p().date(T)},filters:[{type:"select-tree",field:"treeData",defaultExpandAll:!0,multiple:!0,options:W(()=>o.data),label:"站点选择",onChange:e=>{o.checkedKeys=e||[],o.checkedNodes=[];for(const a in e){const t=H(o.data,"children","id",a);o.checkedNodes.push(t)}x()}},{type:"radio-button",field:"type",options:[{label:"日报",value:"day"},{label:"月报",value:"month"},{label:"年报",value:"year"}],label:"报告类型"},{type:"date",label:"选择时间",field:"day",clearable:!1,handleHidden:(e,a,t)=>{t.hidden=e.type==="month"||e.type==="year"}},{type:"month",label:"选择时间",field:"month",clearable:!1,handleHidden:(e,a,t)=>{t.hidden=e.type==="day"||e.type==="year"}},{type:"year",label:"选择时间",field:"year",clearable:!1,handleHidden:(e,a,t)=>{t.hidden=e.type==="month"||e.type==="day"}},{type:"btn-group",btns:[{perm:!0,text:"查询",click:()=>{(o.checkedNodes||[]).length>0?x():P("请选择站点")},icon:"iconfont icon-chaxun"},{type:"default",perm:!0,text:"重置",svgIcon:L(ae),click:()=>{var e;(e=f.value)==null||e.resetForm()}},{type:"warning",perm:!0,text:"导出",svgIcon:L(oe),hide:()=>c.activeName!=="list",click:()=>R()}]}]}),R=()=>{var r;const e=((r=f.value)==null?void 0:r.queryParams)||{},t={stationIdList:o.checkedKeys.join(","),queryType:e.type,start:p(e.date).startOf(e.type).valueOf(),end:p(e.date).endOf(e.type).valueOf()};re(t).then(u=>{const n=window.URL.createObjectURL(u.data),l=document.createElement("a");l.style.display="none",l.href=n,l.setAttribute("download","供水量明细表.xlsx"),document.body.appendChild(l),l.click()})},h=_({loading:!1,dataList:[],columns:[],operations:[],pagination:{hide:!0}}),x=()=>{var r;h.loading=!0;const e=((r=f.value)==null?void 0:r.queryParams)||{},t={stationIdList:o.checkedKeys.join(","),queryType:e.type,start:p(e.date).startOf(e.type).valueOf(),end:p(e.date).endOf(e.type).valueOf()};le(t).then(u=>{var m;const n=(m=u.data)==null?void 0:m.data;c.dataList=n;const l=n==null?void 0:n.tableInfo.map(s=>({prop:s.columnValue,label:s.columnName,minWidth:120,unit:s.unit?"("+s.unit+")":""}));console.log(l),h.columns=l,h.dataList=n==null?void 0:n.tableDataList,h.loading=!1,C()})},V=()=>{var e;(e=g.value)==null||e.resize()},C=()=>{D(()=>{var u,n,l,m;const e=ce(),a=(u=c.dataList)==null?void 0:u.tableDataList,t=a==null?void 0:a.slice(0,a.length-6);e.xAxis.data=t==null?void 0:t.map(s=>s.ts),e.series=[],e.yAxis[0].name="流量(m³)";const r=(l=(n=c.dataList)==null?void 0:n.tableInfo)==null?void 0:l.filter(s=>{if(!["数据时间","合计"].includes(s.columnName))return s.columnName});console.log(r);for(const s in r){const E={name:r[s].columnName,smooth:!0,data:t.map(A=>A[r[s].columnValue]),type:"line",markPoint:{data:[{type:"max",name:"最大值",label:{fontSize:12,color:b.isDark?"#ffffff":"#000000"}},{type:"min",name:"最小值",label:{color:b.isDark?"#ffffff":"#000000"}}]},markLine:{data:[{type:"average",name:"平均值"}]}};e.series.push(E)}console.log(e.series),(m=g.value)==null||m.clear(),D(()=>{v.value&&j.listenTo(v.value,()=>{c.chartOption=e,V()})})})};return M(()=>c.activeName,()=>{c.activeName==="echarts"&&C()}),G(async()=>{var a,t;const e=await q("水源地");o.data=e,o.currentProject=X(o.data),k.defaultParams={...k.defaultParams,treeData:[o.currentProject.id]},(a=f.value)==null||a.resetForm(),o.checkedKeys=[o.currentProject.id],(t=o.checkedNodes)==null||t.push(o.currentProject.id),x()}),$(async()=>{}),(e,a)=>{const t=se,r=Z,u=ee,n=J("VChart"),l=te,m=F;return Q(),Y("div",de,[d(t,{ref_key:"cardSearch",ref:f,config:i(k)},null,8,["config"]),d(m,{class:"card",title:" "},{right:y(()=>[d(u,{modelValue:i(c).activeName,"onUpdate:modelValue":a[0]||(a[0]=s=>i(c).activeName=s)},{default:y(()=>[d(r,{label:"echarts"},{default:y(()=>[d(i(O),{style:{"margin-right":"1px","font-size":"16px"},icon:"clarity:line-chart-line"})]),_:1}),d(r,{label:"list"},{default:y(()=>[d(i(O),{style:{"margin-right":"1px","font-size":"16px"},icon:"material-symbols:table"})]),_:1})]),_:1},8,["modelValue"])]),default:y(()=>[S(I("div",{ref_key:"agriEcoDev",ref:v,class:"chart-box"},[d(n,{ref_key:"refChart",ref:g,theme:i(b).isDark?"dark":"light",option:i(c).chartOption},null,8,["theme","option"])],512),[[w,i(c).activeName==="echarts"]]),S(I("div",null,[d(l,{ref:"refCardTable",class:"card-table",config:i(h)},null,8,["config"])],512),[[w,i(c).activeName==="list"]])]),_:1})])}}}),ke=ne(ue,[["__scopeId","data-v-6a489504"]]);export{ke as default};
