{"fieldInfo": "{field<PERSON>abe<PERSON>} is {fieldValue} here.", "other": "Other", "classBreaks": "Class Breaks", "classBreaksNormFieldAsPercent": "Class Breaks with normalizationField as percent", "heatmap": "Heatmap", "dotDensity": "Dot Density", "relationshipPopupTitle": "Relationship", "relationshipNormFieldAsPercent": "Relationship with normalizationField as percent", "simple": "Simple", "simpleNormFieldAsPercent": "Simple with normalizationField as percent", "uniqueValues": "Unique Values", "uniqueValuesNormFieldAsPercent": "Unique Values with normalizationField as percent", "normFieldLabel": "{expression1} divided by {expression2}", "normFieldLabelAsPercent": "Percentage of {expression1} and {expression2}", "competingFields": "Distribution of categories", "mostCommon": "Predominant Category: {expression}", "orderedListOfValues": "Ordered list of values", "sumOfCategories": "Sum of categories", "listOfCategories": "List of categories", "predominantCategoryWithTotalAndStrength": "Predominant category with total and strength", "predominantCategoryWithChart": "Predominant category with chart", "predominantCategory": "Predominant category", "strengthOfPredominance": "Strength of predominance", "marginOfVictory": "Margin of victory", "predominantCategoryValue": "Predominant category value", "predominantCategoryContent": "{expression} is the category with the highest value here.", "predominantCategoryValueContent": "The most common category here is {expression1}, which has a value of {expression2}.", "predominantCategoryValueMarginContent": "{expression1} has a value of {expression2}, which beats all other <b>categories</b> by a margin of {expression3} percentage points.", "predominantCategoryStrengthContent": "With a value of {expression1}, {expression2} is the predominant category in this area, making up {expression3} of all categories.", "predominantCategoryTotalStrengthContent": "With a value of {expression1}, {expression2} is the predominant category in this area, making up {expression3} of the {expression4} in all categories.", "ageInfo_years": "Age, in years, from {startTime} to {endTime}", "ageInfo_months": "Age, in months, from {startTime} to {endTime}", "ageInfo_days": "Age, in days, from {startTime} to {endTime}", "ageInfo_hours": "Age, in hours, from {startTime} to {endTime}", "ageInfo_minutes": "Age, in minutes, from {startTime} to {endTime}", "ageInfo_seconds": "Age, in seconds, from {startTime} to {endTime}", "relationship": {"legendTitle": "Relationship", "HL": "High - Low", "HH": "High - High", "LL": "Low - Low", "LH": "Low - High", "HM": "High - Medium", "ML": "Medium - Low", "MM": "Medium - Medium", "MH": "Medium - High", "LM": "Low - Medium", "HM1": "High - Mild", "HM2": "High - Medium", "M2L": "Medium - Low", "M2M1": "Medium - Mild", "M2M2": "Medium - Medium", "M2H": "Medium - High", "M1L": "Mild - Low", "M1M1": "Mild - Mild", "M1M2": "Mild - Medium", "M1H": "Mild - High", "LM1": "Low - Mild", "LM2": "Low - Medium"}, "clusters": {"clusterPopupTitle": "Cluster summary", "templateTitle": "Clusters", "numFeatures": "Number of features", "avgFieldLabel": "Average {field<PERSON>abel}", "avgNormFieldLabel": "Average {field<PERSON>abel} divided by {normFieldLabel}", "predominantFieldLabel": "Predominant {fieldLabel}", "countSummary": "This cluster represents <b>{count}</b> features.", "avgFieldSummary": "The average value of <b>{field<PERSON><PERSON><PERSON>}</b> within this cluster is <b>{fieldValue}</b>.", "avgNormFieldSummary": "The average value of <b>{fieldLabel}</b> divided by <b>{normFieldLabel}</b> within this cluster is <b>{fieldValue}</b>.", "predominantFieldSummary": "The predominant value of <b>{field<PERSON>abel}</b> within this cluster is <b>{fieldValue}</b>.", "predominantNoneValue": "None"}}