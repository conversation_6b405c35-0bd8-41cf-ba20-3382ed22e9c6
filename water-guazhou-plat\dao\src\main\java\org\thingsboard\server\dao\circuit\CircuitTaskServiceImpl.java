package org.thingsboard.server.dao.circuit;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.model.sql.smartManagement.plan.GeneralTaskProcessingAndCompleteCount;
import org.thingsboard.server.dao.model.sql.smartProduction.circuit.CircuitTask;
import org.thingsboard.server.dao.model.sql.smartProduction.circuit.GeneralTaskStatus;
import org.thingsboard.server.dao.model.sql.statistic.GeneralTaskStatusStatistic;
import org.thingsboard.server.dao.sql.smartProduction.circuit.CircuitTaskMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.circuit.CircuitTaskPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.circuit.CircuitTaskSaveRequest;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.thingsboard.server.dao.model.sql.smartProduction.circuit.GeneralTaskStatus.*;

@Service
public class CircuitTaskServiceImpl implements CircuitTaskService {
    @Autowired
    private CircuitTaskMapper mapper;

    @Autowired
    private CircuitTaskItemService circuitTaskItemService;

    @Autowired
    private CircuitTemplateService circuitTemplateService;


    @Override
    public IPage<CircuitTask> findAllConditional(CircuitTaskPageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    @Transactional
    public CircuitTask save(CircuitTaskSaveRequest entity) {
        // CircuitTask result = QueryUtil.saveOrUpdateOneByRequest(entity, mapper);
        // circuitTaskItemService.save(entity.getItems(result.getId()));
        // circuitTaskItemService.deleteAll(entity.getRemove());
        List<CircuitTask> circuitTasks = saveAllWithGenerateTaskItem(Collections.singletonList(entity.unwrap()));
        return circuitTasks.get(0);
    }

    @Override
    public boolean update(CircuitTask entity) {
        return mapper.update(entity);
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }

    @Override
    public boolean receive(String id, String userId) {
        return mapper.receive(id, userId, RECEIVED);
    }

    @Override
    public boolean sendVerify(String id, String auditUserId, String userId) {
        return mapper.sendVerify(id, userId, auditUserId, VERIFY);
    }

    @Override
    public boolean verify(String id, boolean allow, String userId) {
        return mapper.verify(id, allow, userId);
    }

    @Override
    public boolean deleteAll(List<String> idList) {
        return QueryUtil.deleteBatch(idList, mapper);
    }

    @Override
    @Transactional
    public List<CircuitTask> saveAllWithGenerateTaskItem(List<CircuitTask> taskList) {
        taskList = QueryUtil.saveOrUpdateBatch(taskList, mapper::saveAll, mapper::updateAll);
        for (CircuitTask task : taskList) {
            List<String> settings = circuitTemplateService.getSettings(task.getTemplateId());
            circuitTaskItemService.saveAllToSettings(settings, task.getId());
        }
        return taskList;
    }

    @Override
    public GeneralTaskStatusStatistic countStatusByUser(String userId, GeneralTaskStatus status) {
        return countStatusByUser(userId, Collections.singletonList(status));
    }

    @Override
    public GeneralTaskStatusStatistic countStatusByUser(String userId, List<GeneralTaskStatus> status) {
        return new GeneralTaskStatusStatistic(
                mapper.totalStatusOfUser(userId, status),
                mapper.totalOfUser(userId)
        );
    }

    @Override
    public GeneralTaskProcessingAndCompleteCount CircuitTaskProcessingAndCompleteCount(String userId) {
        return new GeneralTaskProcessingAndCompleteCount(
                mapper.totalStatusOfUser(userId, Arrays.asList(PENDING, RECEIVED)),
                mapper.totalStatusOfUser(userId, Collections.singletonList(APPROVED))
        );
    }

    @Override
    public JSONObject statusCount(TenantId tenantId) {
        QueryWrapper<CircuitTask> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("tenant_id", UUIDConverter.fromTimeUUID(tenantId.getId()));
        List<CircuitTask> list = mapper.selectList(queryWrapper);
        JSONObject result = new JSONObject();
        int pending = 0;
        int received = 0;
        int complete = 0;
        if (list != null && !list.isEmpty()) {
            for (CircuitTask circuitTask : list) {
                String status = circuitTask.getStatus();
                if (PENDING.name().equals(status)) {
                    pending = pending + 1;
                }
                if (RECEIVED.name().equals(status)) {
                    received = received + 1;
                }
                if (VERIFY.name().equals(status)) {
                    complete = complete + 1;
                }
            }
        }
        result.put("pending", pending);
        result.put("received", received);
        result.put("complete", complete);

        return result;
    }

}
