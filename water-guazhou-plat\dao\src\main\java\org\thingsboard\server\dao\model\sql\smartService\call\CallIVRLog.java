package org.thingsboard.server.dao.model.sql.smartService.call;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 号码归属地
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2021-12-01
 */
@TableName("tb_service_call_ivr_log")
@Data
public class CallIVRLog {

    @TableId
    private String id;

    private String callId;

    private transient String phone;

    private String keyInfo;

    private String eventId;

    private transient String eventName = "自定义按键";

    private transient String audioUrl;

    private transient String audioName;

    private String repeat;

    private String source;

    private String dest;

    private String type;

    private String flag;

    private Date callTime;

    private Date listenTime;

    private Date ringTime;

    private Date endTime;

    private Date realEndTime;

    private String evaluate;

    private String fileUrl;

    private Date createTime;

}
