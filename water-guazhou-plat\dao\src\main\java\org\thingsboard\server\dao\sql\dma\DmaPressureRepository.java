package org.thingsboard.server.dao.sql.dma;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.repository.CrudRepository;
import org.thingsboard.server.dao.model.sql.dma.DmaPressureEntity;
import org.thingsboard.server.dao.util.SqlDao;

import java.util.Date;

@SqlDao
public interface DmaPressureRepository extends CrudRepository<DmaPressureEntity, String> {

    Page<DmaPressureEntity> findAllByPartitionIdAndCreateTimeBetweenOrderByCreateTimeDesc(String partitionId, Date start, Date end, Pageable pageable);
}
