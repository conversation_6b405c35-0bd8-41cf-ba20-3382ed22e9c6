<template>
  <TreeBox>
    <template #tree>
      <SLTree :tree-data="TreeData"></SLTree>
    </template>
    <SLCard class="card-search">
      <div class="card-wrapper">
        <InlineForm ref="refSearch" :config="FormConfig"></InlineForm>
      </div>
    </SLCard>
    <SLCard class="card-chart" title="实时数据">
      <VChart
        ref="refChart"
        :theme="useAppStore().isDark ? 'blackBackground' : 'whiteBackground'"
        :option="chartOption"
      ></VChart>
    </SLCard>
    <CardTable class="card-table" :config="TableConfig"></CardTable>
  </TreeBox>
</template>
<script lang="ts" setup>
import { onBeforeUnmount, onMounted, ref } from 'vue';
import TreeBox from '../layout/treeOrDetailFrame/TreeBox.vue';
import {
  useChart,
  useDevice,
  useProject,
  useSearch,
  useTable
} from './composible/useHooks';
import { IECharts } from '@/plugins/echart';
import { useAppStore } from '@/store';

const refSearch = ref<IInlineFormIns>();
const refChart = ref<IECharts>();
const { chartOption, refreshChart } = useChart(refChart);
const { TableConfig, refreshTable } = useTable();
const { FormConfig, initFirstLineOfFilters, clearTimer } = useSearch({
  refSearch,
  withInterval: true,
  refreshCall: (data) => {
    refreshChart(data);
    refreshTable(data);
  }
});
const { deviceList, getDeviceData } = useDevice();
const { TreeData, refreshProject } = useProject(async (data) => {
  TreeData.currentProject = data;
  await getDeviceData(data?.id);
  initFirstLineOfFilters(deviceList.value || []);
});
onMounted(async () => {
  await refreshProject();
  refreshChart();
});
onBeforeUnmount(() => {
  clearTimer();
});
</script>
<style lang="scss" scoped>
.card-wrapper {
  padding: 20px 12px 0;
}
.card-table,
.card-chart {
  height: 400px;
}
.card-search,
.card-chart {
  margin-bottom: 15px;
}
</style>
