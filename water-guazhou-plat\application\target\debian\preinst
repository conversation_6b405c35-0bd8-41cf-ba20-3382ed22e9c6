#!/bin/sh -e

case "$1" in
    install)
        
        
if ! getent group thingsboard-iot >/dev/null; then
    addgroup --system thingsboard-iot
fi

if ! getent passwd thingsboard-iot >/dev/null; then
    adduser --quiet \
            --system \
            --ingroup thingsboard-iot \
            --quiet \
            --disabled-login \
            --disabled-password \
            --home /usr/share/thingsboard-iot \
            --no-create-home \
            -gecos "Thingsboard application" \
            thingsboard-iot
fi

        
        ;;
esac
