export const tableColumns: any = () => {
  return [
    {
      prop: 'name1',
      label: '分区类别',
      minWidth: 120
    },
    {
      prop: 'name1',
      label: '分区名称',
      minWidth: 120
    },
    {
      prop: 'title',
      label: '2022-10-17日供水（总）量（立方米）',
      align: 'center',
      subColumns: [
        {
          prop: 'name2',
          label: '本日流量',
          minWidth: 120
        },
        {
          prop: 'name2',
          label: '环比昨日',
          unit: '(%)',
          minWidth: 120
        },
        {
          prop: 'name2',
          label: '日增减率',
          unit: '(%)',
          minWidth: 120
        },
        {
          prop: 'name2',
          label: '日流量同比去年',
          unit: '(%)',
          minWidth: 120
        },
        {
          prop: 'name2',
          label: '本旬累计',
          minWidth: 120
        },
        {
          prop: 'name2',
          label: '环比上旬',
          unit: '(%)',
          minWidth: 120
        },
        {
          prop: 'name2',
          label: '旬增减率',
          unit: '(%)',
          minWidth: 120
        },
        {
          prop: 'name2',
          label: '旬流量同比去年',
          unit: '(%)',
          minWidth: 120
        },
        {
          prop: 'name2',
          label: '本月累计',
          minWidth: 120
        },
        {
          prop: 'name2',
          label: '环比上月',
          unit: '(%)',
          minWidth: 120
        },
        {
          prop: 'name2',
          label: '月增减率',
          unit: '(%)',
          minWidth: 120
        },
        {
          prop: 'name2',
          label: '月流量同比去年',
          unit: '(%)',
          minWidth: 120
        },
        {
          prop: 'name2',
          label: '本年累计',
          minWidth: 120
        },
        {
          prop: 'name2',
          label: '同比去年',
          unit: '(%)',
          minWidth: 120
        },
        {
          prop: 'name2',
          label: '日流量占比上级流量',
          unit: '(%)',
          minWidth: 120
        },
        {
          prop: 'name2',
          label: '日流量占比环比昨日',
          unit: '(%)',
          minWidth: 120
        },
        {
          prop: 'name2',
          label: '日流量占比环比去年',
          unit: '(%)',
          minWidth: 120
        },
        {
          prop: 'name2',
          label: '旬流量占比上级流量',
          unit: '(%)',
          minWidth: 120
        },
        {
          prop: 'name2',
          label: '旬流量占比环比上旬',
          unit: '(%)',
          minWidth: 120
        },
        {
          prop: 'name2',
          label: '旬流量占比同比去年',
          unit: '(%)',
          minWidth: 120
        },
        {
          prop: 'name2',
          label: '月流量占比上级流量',
          unit: '(%)',
          minWidth: 120
        },
        {
          prop: 'name2',
          label: '月流量占比环比上月',
          unit: '(%)',
          minWidth: 120
        },
        {
          prop: 'name2',
          label: '月流量占比同比去年',
          unit: '(%)',
          minWidth: 120
        },
        {
          prop: 'name2',
          label: '年累计占比上级流量',
          unit: '(%)',
          minWidth: 120
        },
        {
          prop: 'name2',
          label: '年累计占比同比去年',
          unit: '(%)',
          minWidth: 120
        }
      ]
    }
  ]
}
