<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.base.BaseTileConfigurationMapper">
    
    <resultMap type="org.thingsboard.server.dao.model.sql.base.BaseTileConfiguration" id="BaseTileConfigurationResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="description"    column="description"    />
        <result property="type"    column="type"    />
        <result property="url"    column="url"    />
        <result property="format"    column="format"    />
        <result property="tileSize"    column="tile_size"    />
        <result property="matrixSet"    column="matrix_set"    />
        <result property="minZoomLevel"    column="min_zoom_level"    />
        <result property="maxZoomLevel"    column="max_zoom_level"    />
        <result property="opacity"    column="opacity"    />
        <result property="brightness"    column="brightness"    />
    </resultMap>

    <sql id="selectBaseTileConfigurationVo">
        select id, name, description, type, url, format, tile_size, matrix_set, min_zoom_level, max_zoom_level, opacity, brightness from base_tile_configuration
    </sql>

    <select id="selectBaseTileConfigurationList" parameterType="org.thingsboard.server.dao.model.sql.base.BaseTileConfiguration" resultMap="BaseTileConfigurationResult">
        <include refid="selectBaseTileConfigurationVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="url != null  and url != ''"> and url = #{url}</if>
            <if test="format != null  and format != ''"> and format = #{format}</if>
            <if test="tileSize != null  and tileSize != ''"> and tile_size = #{tileSize}</if>
            <if test="matrixSet != null  and matrixSet != ''"> and matrix_set = #{matrixSet}</if>
            <if test="minZoomLevel != null  and minZoomLevel != ''"> and min_zoom_level = #{minZoomLevel}</if>
            <if test="maxZoomLevel != null  and maxZoomLevel != ''"> and max_zoom_level = #{maxZoomLevel}</if>
            <if test="opacity != null  and opacity != ''"> and opacity = #{opacity}</if>
            <if test="brightness != null  and brightness != ''"> and brightness = #{brightness}</if>
        </where>
    </select>
    
    <select id="selectBaseTileConfigurationById" parameterType="String" resultMap="BaseTileConfigurationResult">
        <include refid="selectBaseTileConfigurationVo"/>
        where id = #{id}
    </select>
    <select id="selectAllBaseTileConfiguration"
            resultType="org.thingsboard.server.dao.model.sql.base.BaseTileConfiguration">
        <include refid="selectBaseTileConfigurationVo"/>
    </select>

    <insert id="insertBaseTileConfiguration" parameterType="org.thingsboard.server.dao.model.sql.base.BaseTileConfiguration">
        insert into base_tile_configuration
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="name != null">name,</if>
            <if test="description != null">description,</if>
            <if test="type != null">type,</if>
            <if test="url != null">url,</if>
            <if test="format != null">format,</if>
            <if test="tileSize != null">tile_size,</if>
            <if test="matrixSet != null">matrix_set,</if>
            <if test="minZoomLevel != null">min_zoom_level,</if>
            <if test="maxZoomLevel != null">max_zoom_level,</if>
            <if test="opacity != null">opacity,</if>
            <if test="brightness != null">brightness,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="name != null">#{name},</if>
            <if test="description != null">#{description},</if>
            <if test="type != null">#{type},</if>
            <if test="url != null">#{url},</if>
            <if test="format != null">#{format},</if>
            <if test="tileSize != null">#{tileSize},</if>
            <if test="matrixSet != null">#{matrixSet},</if>
            <if test="minZoomLevel != null">#{minZoomLevel},</if>
            <if test="maxZoomLevel != null">#{maxZoomLevel},</if>
            <if test="opacity != null">#{opacity},</if>
            <if test="brightness != null">#{brightness},</if>
         </trim>
    </insert>

    <update id="updateBaseTileConfiguration" parameterType="org.thingsboard.server.dao.model.sql.base.BaseTileConfiguration">
        update base_tile_configuration
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="description != null">description = #{description},</if>
            <if test="type != null">type = #{type},</if>
            <if test="url != null">url = #{url},</if>
            <if test="format != null">format = #{format},</if>
            <if test="tileSize != null">tile_size = #{tileSize},</if>
            <if test="matrixSet != null">matrix_set = #{matrixSet},</if>
            <if test="minZoomLevel != null">min_zoom_level = #{minZoomLevel},</if>
            <if test="maxZoomLevel != null">max_zoom_level = #{maxZoomLevel},</if>
            <if test="opacity != null">opacity = #{opacity},</if>
            <if test="brightness != null">brightness = #{brightness},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBaseTileConfigurationById" parameterType="String">
        delete from base_tile_configuration where id = #{id}
    </delete>

    <delete id="deleteBaseTileConfigurationByIds" parameterType="String">
        delete from base_tile_configuration where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>