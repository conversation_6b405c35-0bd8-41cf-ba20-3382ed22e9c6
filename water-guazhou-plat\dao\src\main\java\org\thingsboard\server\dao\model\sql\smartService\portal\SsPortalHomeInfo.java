package org.thingsboard.server.dao.model.sql.smartService.portal;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

@Getter
@Setter
@ResponseEntity
@TableName("ss_portal_home_info")
public class SsPortalHomeInfo {
    // id
    private String id;

    // 标题
    private String name;

    // 封面
    private String logo;

    // 链接
    private String address;

    // 二维码图片
    private String qr;

    // 客服电话
    private String phone;

    // 公司邮箱
    private String email;

    // 邮编地址
    private String postcode;

    // 客户id
    private String tenantId;

}
