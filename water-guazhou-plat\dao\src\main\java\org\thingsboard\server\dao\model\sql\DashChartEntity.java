package org.thingsboard.server.dao.model.sql;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.springframework.beans.factory.annotation.Required;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.jws.WebParam;
import javax.persistence.*;

/**
 * <AUTHOR>
 * @date 2020/2/26 10:51
 */
@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.DASH_CHART_TABLE_NAME)
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
public class DashChartEntity {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    /**
     * 名称
     */
    @Column(name = ModelConstants.DEVICE_TEMPLATE_NAME)
    private String name;

    /**
     * 详情
     */
    @Column(name = ModelConstants.ALARM_DETAILS_PROPERTY)
    private String detail;

    /**
     * 是否启用密码保护(0-未启用，1-启用)
     */
    @Column(name = ModelConstants.DASH_CHART_PROTECT)
    private int protect;

    @Column(name = ModelConstants.DASH_CHART_PROTECT_PWD)
    private String protectPwd;

    /**
     * 组态创建时间
     */
    @Column(name = ModelConstants.ALARM_CREATE_TIME)
    private long createTime;

    /**
     * 组态更新时间
     */
    @Column(name = ModelConstants.DATASOURCE_UPDATE_TIME)
    private long updateTime;

    /**
     * 组态ID
     */
    @Column(name = ModelConstants.DASH_CHART_DASHBOARD_ID)
    private String dashBoardId;

    /**
     * 组态ID
     */
    @Column(name = ModelConstants.DASH_CHART_DASHBOARD_JSON_ID)
    private String dashBoardJsonId;

    /**
     * 组态类别(云端组态-cloud，工控机组态-local)
     */
    @Column(name = ModelConstants.ALARM_TYPE_PROPERTY)
    private String chartType;

    @Column(name = ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;

    /**
     * 发起者ID
     */
    @Column(name = ModelConstants.ALARM_ORIGINATOR_ID_PROPERTY)
    private String originatorId;

    @Column(name = ModelConstants.DASH_CHART_DASH_JSON)
    private String dashJson;


}
