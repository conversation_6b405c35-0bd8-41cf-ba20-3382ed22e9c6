package org.thingsboard.server.dao.store;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.store.Store;
import org.thingsboard.server.dao.util.imodel.query.store.StorePageRequest;
import org.thingsboard.server.dao.util.imodel.query.store.StoreSaveRequest;

public interface StoreService {
    /**
     * 分页条件查询仓库信息
     *
     * @param request 分页查询条件
     * @return 分页查询结果
     */
    IPage<Store> findAllConditional(StorePageRequest request);

    /**
     * 保存仓库信息
     *
     * @param entity 实体信息
     * @return 保存好的实体
     */
    Store save(StoreSaveRequest entity);

    /**
     * 增量修改
     *
     * @param entity 实体信息
     * @return 是否修改成功
     */
    boolean update(Store entity);

    /**
     * 删除
     *
     * @param id 唯一标识
     * @return 是否删除成功
     */
    boolean delete(String id);


    /**
     * 仓库是否允许被删除
     *
     * @param id 仓库id
     * @return 是否允许被删除
     */
    boolean canBeDelete(String id);

}
