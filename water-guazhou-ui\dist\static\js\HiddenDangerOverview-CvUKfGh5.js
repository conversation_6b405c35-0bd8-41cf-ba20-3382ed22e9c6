import{d as w,c as n,r as s,a8 as h,s as m,o as x,g as W,n as S,q as p,i as c,F as O,b6 as D,al as I,b7 as P,aj as B}from"./index-r0dFAfgr.js";import{_ as L}from"./CardTable-rdWOL4_6.js";import{_ as N}from"./CardSearch-CB_HNR-Q.js";import{G as q}from"./workorder-jXNat1mh.js";import{u as F}from"./useCTI-CrDoUkpT.js";import{P as y}from"./config-C9CMv0E7.js";import G from"./detail-CU6-qhMl.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";/* empty css                         */import"./index-CpGhZCTT.js";import"./config-DqqM5K5L.js";import"./DateFormatter-Bm9a68Ax.js";import"./OutsideWorkOrder-C6s8joBt.js";import"./detailSteps-BqRp_Y4m.js";/* empty css                */import"./index-CCFuhOrs.js";/* empty css                             */import"./DPlayer-Be2AurQX.js";import"./DPlayer.min-_HMH7IVX.js";const j={class:"wrapper"},le=w({__name:"HiddenDangerOverview",setup(E){const u=n(),d=n(),f=s({orderTypes:[]}),_=n(),g=s({title:"流程明细",group:[],modalClass:"lightColor"}),b=n(""),v=e=>{var t;b.value=e.workOrderId||"",g.title=e.serialNo,(t=_.value)==null||t.openDrawer()},k=s({defaultParams:{statusStage:""},filters:[{type:"radio-button",label:"事件状态",field:"statusStage",options:[{label:"全部",value:""},{label:"待处理",value:"0"},{label:"处理中",value:"1"},{label:"已处理",value:"2"}]},{type:"select",label:"事件类型",field:"type",options:h(()=>f.orderTypes),formatter:(e,t,r)=>{var a,l;return((l=(a=r.options)==null?void 0:a.find(C=>C.value===e))==null?void 0:l.label)||e}},{type:"input",field:"keyword",label:"快速查找"}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",svgIcon:m(I),click:()=>i()},{perm:!0,text:"重置",type:"default",svgIcon:m(P),click:()=>{var e;(e=d.value)==null||e.resetForm(),i()}},{perm:!0,text:"导出",svgIcon:m(B),type:"default",click:()=>{var e;(e=u.value)==null||e.exportTable()}}]}]}),o=s({pagination:{refreshData:({page:e,size:t})=>{o.pagination.page=e||1,o.pagination.limit=t||20,i()}},columns:[{minWidth:120,label:"任务编号",prop:"code"},{minWidth:120,label:"工单编号",prop:"workOrderCode"},{minWidth:120,label:"类型",prop:"type"},{minWidth:120,label:"内容",prop:"content"},{minWidth:120,label:"上报人员",prop:"creatorName"},{minWidth:120,label:"上报时间",prop:"createTime"},{minWidth:120,label:"紧急程度",prop:"level"},{minWidth:120,label:"状态",prop:"status",tag:!0,tagType:h(e=>{var t;return(t=y[e.status])==null?void 0:t.type}),formatter:e=>{var t;return((t=y[e.status])==null?void 0:t.text)||e.status}}],dataList:[],operations:[{perm:!0,isTextBtn:!0,text:"详情",click:e=>v(e)}]}),i=()=>{var e;q((e=d.value)==null?void 0:e.queryParams).then(t=>{var r,a;o.dataList=((r=t.data.data)==null?void 0:r.data)||[],o.pagination.total=((a=t.data.data)==null?void 0:a.total)||0})},{getOrderTypeOption:T}=F();return x(async()=>{i(),f.orderTypes=await T()}),(e,t)=>{const r=N,a=L,l=D;return W(),S("div",j,[p(r,{ref_key:"refSearch",ref:d,config:c(k)},null,8,["config"]),p(a,{ref_key:"refTable",ref:u,class:"card-table",config:c(o)},null,8,["config"]),p(l,{ref_key:"refdetail",ref:_,config:c(g)},{default:O(()=>[p(G,{id:c(b)},null,8,["id"])]),_:1},8,["config"])])}}});export{le as default};
