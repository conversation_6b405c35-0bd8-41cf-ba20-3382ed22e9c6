<!-- 颜色选择器 -->
<template>
  <el-popover
    trigger="click"
    :effect="appStore.isDark === false ? 'light' : 'dark'"
    :width="state.width"
    class="color-picker-popver"
    popper-class="el-color-picker__popper"
    @show="getInputWidth"
  >
    <template #reference>
      <el-input
        v-if="props.input"
        v-model="state.inputColor"
        :style="'--el-input-bg-color:' + state.inputColor"
        @change="handleInputChange"
      ></el-input>
      <div
        v-else
        class="color-pick-trigger"
      >
        <div
          class="color-fill"
          :style="{
            backgroundColor: modelValue || 'rgba(0,0,0,0)'
          }"
        >
          {{ modelValue || 'rgba(0,0,0,0)' }}
        </div>
      </div>
    </template>
    <Chrome
      ref="refPicker"
      v-model="state.color"
    ></Chrome>
  </el-popover>
</template>

<script lang="ts" setup name="ColorPicker">
import { Chrome } from '@ckpack/vue-color'
import { useAppStore } from '@/store'

const appStore = useAppStore()
const refPicker = ref<InstanceType<typeof Chrome>>()
const props = defineProps<{
  modelValue?: string
  size?: ISize
  disabled?: boolean
  colorType?: ColorAttrType
  input?: boolean
}>()
const emit = defineEmits(['update:modelValue', 'change'])
const state = reactive<{
  width?: number
  inputColor: string
  color: string
}>({
  inputColor: props.modelValue || 'rgba(0,0,0,0)',
  color: props.modelValue || 'rgba(0,0,0,0)'
})
const formateColor = (color: any) => {
  let computedColor: any = ''
  if (typeof color === 'string') {
    return color
  }
  switch (props.colorType) {
    case 'hsl':
      computedColor = `hsl(${color.hsl.h},${color.hsl.s},${color.hsl.l},${color.hsl.a})`
      break
    case 'hsv':
      computedColor = `hsv(${color.hsv.h},${color.hsv.s},${color.hsv.v},${color.hsv.a})`
      break

    case 'hex8':
    case 'hex':
      computedColor = color[props.colorType]
      break

    default:
      computedColor = `rgba(${color.rgba.r},${color.rgba.g},${color.rgba.b},${color.rgba.a})`
      break
  }
  return computedColor
}
const handleInputChange = val => {
  emit('update:modelValue', val)
  emit('change', val)
}
watch(
  () => state.color,
  (color: any) => {
    const computedColor = formateColor(color)
    emit('update:modelValue', computedColor)
    emit('change', computedColor)
  }
)
watch(
  () => props.modelValue,
  newVal => {
    state.color = newVal || 'rgba(0,0,0,0)'
    state.inputColor = newVal || 'rgba(0,0,0,0)'
  }
)
// watchEffect(() => {
//   const computedColor = formateColor(state.color)
//   emit('update:modelValue', computedColor)
//   emit('change', computedColor)
// })
const getInputWidth = () => {
  state.width = refPicker.value?.$el.offsetWidth
}
</script>
<style lang="scss">
.color-pick-trigger {
  background-color: #ffffff;
}
.color-fill {
  cursor: pointer;
  width: 140px;
  height: 24px;
  font-size: 12px;
  display: flex;
  align-items: center;
  padding: 0 8px;
  line-height: 24px;
  color: var(--el-text-color-regular);
  box-shadow: 0 0 0 1px var(--el-border-color) inset;
}
</style>
<style>
.el-color-picker__popper {
  padding: 0 !important;
}
.vc-chrome-fields .vc-input__input {
  color: #999;
}
</style>
