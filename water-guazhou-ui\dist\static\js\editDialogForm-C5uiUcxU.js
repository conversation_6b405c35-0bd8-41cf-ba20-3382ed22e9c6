import{_ as G}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{z as E,d as O,cN as $,c as k,r as x,am as z,S as J,b as T,g as b,h as q,F as n,q as a,i as t,n as F,G as y,p as f,bh as d,aB as M,aJ as K,an as Q,dz as X,dA as Y,bU as Z,H as ee,I as le,c2 as ae,bW as te,K as ne,c5 as oe,C as ue}from"./index-r0dFAfgr.js";import{f as ie}from"./index-0NlGN6gS.js";const Ve=v=>E({url:`/api/spp/minFlowConfig/listByPartitionId/${v}`,method:"get"}),re=v=>E({url:"/api/spp/minFlowConfig",method:"post",data:v}),se=O({__name:"editDialogForm",props:{defaultValue:{}},emits:["success"],setup(v,{expose:A,emit:P}){var C;const B=P,{proxy:H}=$(),g=v,_=k(),V=x({tabsList:[],stationTree:[],stationId:"",type:"month",activeName:"存量设置",stockType:"夜间最小流",increaseType:"手动选择"}),l=k({...g.defaultValue||{},incrType:((C=g.defaultValue)==null?void 0:C.incrType)||"1"});z(()=>g.defaultValue,()=>{var i;l.value={...g.defaultValue||{},incrType:((i=g.defaultValue)==null?void 0:i.incrType)||"1"}});const h=x({type:"tabs",tabType:"border-card",width:"100%",tabs:[{label:"存量漏失指标设置",value:"存量设置"},{label:"增量漏失指标设置",value:"增量设置"}],handleTabClick:i=>{V.activeName=i.props.name}}),c=x({dialogWidth:800,title:"修改",group:[],desTroyOnClose:!0,submit:()=>{const i=H.$refs["refForm"+V.activeName];i==null||i.validate((e,r)=>{if(!e){const p=Object.keys(r);i==null||i.scrollToField(p[0]);return}J("确定提交？","提示信息").then(async()=>{var p;try{c.submitting=!0;const u=await re(l.value);u.data.code===200?(T.success("操作成功"),B("success"),(p=_.value)==null||p.closeDialog()):T.error(u.data.message)}catch{T.error("操作失败")}c.submitting=!1}).catch(()=>{})})}}),w=()=>{l.value.collectRate&&(l.value.nightValueMax=((l.value.nightFlowMax||0)*l.value.collectRate/60).toFixed(2),l.value.nightValueMin=((l.value.nightFlowMin||0)*l.value.collectRate/60).toFixed(2))},R=()=>{var e,r,p;const i=l.value.incrType||"1";l.value.incrBase=(i==="1"?(e=g.defaultValue)==null?void 0:e.incrBase:0)||0,i==="1"?(l.value.incrWarn=((r=g.defaultValue)==null?void 0:r.incrWarn)||0,l.value.incrError=((p=g.defaultValue)==null?void 0:p.incrError)||0):N()},N=()=>{const i=l.value.incrType,e=Number(i==="1"?l.value.incrBase:0)||0,r=Number(l.value.mainLineLength||0);l.value.incrWarn=(e+.9936*r+.1408).toFixed(2),l.value.incrError=(e+.9936*r+.1408+.5*r).toFixed(2)};return A({openDialog:()=>{var i;(i=_.value)==null||i.openDialog()}}),(i,e)=>{const r=X,p=Y,u=Z,m=ee,s=le,I=ae,W=te,L=ne,S=oe,j=G;return b(),q(j,{ref_key:"refDialog",ref:_,config:t(c)},{default:n(()=>[a(S,{modelValue:t(V).activeName,"onUpdate:modelValue":e[15]||(e[15]=D=>t(V).activeName=D),config:t(h)},{content:n(D=>{var U;return[a(L,{ref:"refForm"+((U=D.data)==null?void 0:U.value),class:"form",model:t(l)},{default:n(()=>[a(W,{gutter:20},{default:n(()=>[t(V).activeName==="存量设置"?(b(),F(M,{key:0},[a(u,{span:24},{default:n(()=>[a(p,{modelValue:t(V).stockType,"onUpdate:modelValue":e[0]||(e[0]=o=>t(V).stockType=o)},{default:n(()=>[a(r,{label:"夜间最小流"},{default:n(()=>e[16]||(e[16]=[y(" 夜间最小流量 ")])),_:1}),a(r,{label:"夜间最小值"},{default:n(()=>e[17]||(e[17]=[y(" 夜间最小值 ")])),_:1})]),_:1},8,["modelValue"])]),_:1}),a(u,{span:12},{default:n(()=>[a(s,{label:"流量最小值(m³/h)",prop:"nightFlowMin",rules:[{required:!0,message:"请输入流量最小值"}]},{default:n(()=>[a(m,{modelValue:t(l).nightFlowMin,"onUpdate:modelValue":e[1]||(e[1]=o=>t(l).nightFlowMin=o),type:"input-number",onChange:w},null,8,["modelValue"])]),_:1})]),_:1}),a(u,{span:12},{default:n(()=>[a(s,{label:"流量最大值(m³/h)",prop:"nightFlowMax",rules:[{required:!0,message:"请输入流量最大值"}]},{default:n(()=>[a(m,{modelValue:t(l).nightFlowMax,"onUpdate:modelValue":e[2]||(e[2]=o=>t(l).nightFlowMax=o),type:"input-number",onChange:w},null,8,["modelValue"])]),_:1})]),_:1}),a(u,{span:20},{default:n(()=>[f("span",null,"备注:夜间最小流初始值设定,夜间最小值(小于"+d(t(l).nightFlowMin)+"较好 / "+d(t(l).nightFlowMin)+"-"+d(t(l).nightFlowMax)+"一般 / 大于"+d(t(l).nightFlowMax)+"较差)",1)]),_:1}),t(V).stockType==="夜间最小值"?(b(),F(M,{key:0},[a(u,{span:24},{default:n(()=>[a(s,{label:"采集频率",rules:[{required:!0,message:"请输入采集频率"}],prop:"collectRate"},{default:n(()=>[a(p,{modelValue:t(l).collectRate,"onUpdate:modelValue":e[3]||(e[3]=o=>t(l).collectRate=o),onChange:w},{default:n(()=>[(b(!0),F(M,null,K(t(ie),o=>(b(),q(r,{key:o,label:o},{default:n(()=>[y(d(o),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(u,{span:12},{default:n(()=>[a(s,{label:"最小值(m³/h)",rules:[{required:!0,message:"请输入最小值"}],prop:"nightValueMin"},{default:n(()=>[a(m,{modelValue:t(l).nightValueMin,"onUpdate:modelValue":e[4]||(e[4]=o=>t(l).nightValueMin=o),disabled:"",type:"input-number"},null,8,["modelValue"])]),_:1})]),_:1}),a(u,{span:12},{default:n(()=>[a(s,{label:"最大值(m³/h)",prop:"nightValueMax",rules:[{required:!0,message:"请输入最大值"}]},{default:n(()=>[a(m,{modelValue:t(l).nightValueMax,"onUpdate:modelValue":e[5]||(e[5]=o=>t(l).nightValueMax=o),disabled:"",type:"input-number"},null,8,["modelValue"])]),_:1})]),_:1}),a(u,{span:20},{default:n(()=>e[18]||(e[18]=[f("span",null,"备注:最小（大）值根据流量最小（大）值自动计算",-1)])),_:1})],64)):Q("",!0),a(u,{span:20},{default:n(()=>e[19]||(e[19]=[f("div",{class:"title"}," 单位管长夜间净流量: ",-1)])),_:1}),a(u,{span:12},{default:n(()=>[a(s,{label:"最小值(m³/h)",prop:"unitPipeNightFlowMin",rules:[{required:!0,message:"请输入最小值"}]},{default:n(()=>[a(m,{modelValue:t(l).unitPipeNightFlowMin,"onUpdate:modelValue":e[6]||(e[6]=o=>t(l).unitPipeNightFlowMin=o),type:"input-number"},null,8,["modelValue"])]),_:1})]),_:1}),a(u,{span:12},{default:n(()=>[a(s,{label:"最大值(m³/h)",prop:"unitPipeNightFlowMax",rules:[{required:!0,message:"请输入最大值"}]},{default:n(()=>[a(m,{modelValue:t(l).unitPipeNightFlowMax,"onUpdate:modelValue":e[7]||(e[7]=o=>t(l).unitPipeNightFlowMax=o),type:"input-number"},null,8,["modelValue"])]),_:1})]),_:1}),a(u,{span:20},{default:n(()=>[f("span",null,"备注:单位管长初始值设定,夜间最小值(小于"+d(t(l).unitPipeNightFlowMin)+"较好 / "+d(t(l).unitPipeNightFlowMin)+"-"+d(t(l).unitPipeNightFlowMax)+"一般 / 大于"+d(t(l).unitPipeNightFlowMax)+"较差)",1)]),_:1}),a(u,{span:20},{default:n(()=>e[20]||(e[20]=[f("div",{class:"title"}," MNF/日均小时流量(%): ",-1)])),_:1}),a(u,{span:12},{default:n(()=>[a(s,{label:"最小值(%)",prop:"mnfDivDayAvgHourFlowMin",rules:[{required:!0,message:"请输入最小值"}]},{default:n(()=>[a(m,{modelValue:t(l).mnfDivDayAvgHourFlowMin,"onUpdate:modelValue":e[8]||(e[8]=o=>t(l).mnfDivDayAvgHourFlowMin=o),type:"input-number"},null,8,["modelValue"])]),_:1})]),_:1}),a(u,{span:12},{default:n(()=>[a(s,{label:"最大值(%)",prop:"mnfDivDayAvgHourFlowMax",rules:[{required:!0,message:"请输入最大值"}]},{default:n(()=>[a(m,{modelValue:t(l).mnfDivDayAvgHourFlowMax,"onUpdate:modelValue":e[9]||(e[9]=o=>t(l).mnfDivDayAvgHourFlowMax=o),type:"input-number"},null,8,["modelValue"])]),_:1})]),_:1}),a(u,{span:20},{default:n(()=>[f("span",null,"备注:MNF/日均小时流量初始值设定,夜间最小流(小于"+d(t(l).mnfDivDayAvgHourFlowMin)+"%较好 / "+d(t(l).mnfDivDayAvgHourFlowMin)+"%-"+d(t(l).mnfDivDayAvgHourFlowMax)+"%一般 / 大于"+d(t(l).mnfDivDayAvgmnfDivDayAvgHourFlowMaxHourFlowMin)+"%较差)",1)]),_:1}),a(u,{span:20,style:{"margin-top":"20px"}},{default:n(()=>e[21]||(e[21]=[f("span",null,"规则:满足两个及以上较差评为较差.满足三个较好评为较好,其它评价为一般",-1)])),_:1})],64)):(b(),F(M,{key:1},[a(u,{span:24},{default:n(()=>[a(p,{modelValue:t(l).incrType,"onUpdate:modelValue":e[10]||(e[10]=o=>t(l).incrType=o),onChange:R},{default:n(()=>[a(r,{label:"1"},{default:n(()=>e[22]||(e[22]=[y(" 手动选择 ")])),_:1}),a(r,{label:"2"},{default:n(()=>e[23]||(e[23]=[y(" 自动选择 ")])),_:1})]),_:1},8,["modelValue"])]),_:1}),a(u,{span:12},{default:n(()=>[a(s,{label:"日期",prop:"incrTime",rules:[{required:!0,message:"请选择日期"}]},{default:n(()=>[a(I,{modelValue:t(l).incrTime,"onUpdate:modelValue":e[11]||(e[11]=o=>t(l).incrTime=o),type:"date"},null,8,["modelValue"])]),_:1})]),_:1}),a(u,{span:12},{default:n(()=>[a(s,{label:"基准值(m³/h)",prop:"incrBase",rules:[{required:!0,message:"请输入基准值"}]},{default:n(()=>[a(m,{modelValue:t(l).incrBase,"onUpdate:modelValue":e[12]||(e[12]=o=>t(l).incrBase=o),type:"input-number",disabled:t(l).incrType==="2",onChange:N},{suffix:n(()=>e[24]||(e[24]=[y(" m³/h ")])),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),a(u,{span:20},{default:n(()=>e[25]||(e[25]=[f("span",null,"备注:历史测量最小夜间流量最小值为基准值",-1)])),_:1}),a(u,{span:20},{default:n(()=>[a(s,{label:"黄色预警值",prop:"incrWarn",rules:[{required:!0,message:"请输入预警值"}]},{default:n(()=>[a(m,{modelValue:t(l).incrWarn,"onUpdate:modelValue":e[13]||(e[13]=o=>t(l).incrWarn=o),type:"input-number",disabled:t(l).incrType==="2"},null,8,["modelValue","disabled"])]),_:1})]),_:1}),a(u,{span:20},{default:n(()=>e[26]||(e[26]=[f("span",null,"备注:黄色预警值=基准值+0.9936*管线长度+0.1408",-1)])),_:1}),a(u,{span:20},{default:n(()=>[a(s,{label:"红色预警值",prop:"incrError",rules:[{required:!0,message:"请输入预警值"}]},{default:n(()=>[a(m,{modelValue:t(l).incrError,"onUpdate:modelValue":e[14]||(e[14]=o=>t(l).incrError=o),type:"input-number",disabled:t(l).incrType==="2"},null,8,["modelValue","disabled"])]),_:1})]),_:1}),a(u,{span:20},{default:n(()=>e[27]||(e[27]=[f("span",null,"备注:红色预警值=基准值+0.9336*管线长度+0.1408+0.5*管线长度",-1)])),_:1})],64))]),_:1})]),_:2},1032,["model"])]}),_:1},8,["modelValue","config"])]),_:1},8,["config"])}}}),de=ue(se,[["__scopeId","data-v-d2794fb1"]]),be=Object.freeze(Object.defineProperty({__proto__:null,default:de},Symbol.toStringTag,{value:"Module"}));export{Ve as G,be as a,de as e};
