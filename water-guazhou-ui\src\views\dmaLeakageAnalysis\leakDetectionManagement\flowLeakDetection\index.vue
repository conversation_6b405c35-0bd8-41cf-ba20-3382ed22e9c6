<template>
  <div class="flow-leak-detection-container">
    <!-- 筛选栏 -->
    <el-form :model="searchParams" :inline="true" class="filter-form">
      <el-form-item label="方案名称">
        <el-input v-model="searchParams.name" placeholder="请输入方案名称" clearable />
      </el-form-item>
      <el-form-item label="分区">
        <el-select v-model="searchParams.partition" placeholder="全部" clearable style="width: 140px;">
          <el-option label="全部" value="" />
          <el-option v-for="item in partitionList" :key="item" :label="item" :value="item" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态">
        <el-select v-model="searchParams.status" placeholder="全部" clearable style="width: 120px;">
          <el-option label="全部" value="" />
          <el-option label="未开始" value="未开始" />
          <el-option label="进行中" value="进行中" />
          <el-option label="已完成" value="已完成" />
        </el-select>
      </el-form-item>
      <el-form-item label="时间">
        <el-date-picker v-model="searchParams.dateRange" type="daterange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">查询</el-button>
        <el-button @click="resetSearch">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 地图展示方案分布 -->
    <div class="map-container">
      <ArcLayout ref="refArcLayout" @map-loaded="onMapLoaded" />
    </div>

    <!-- 方案点弹窗 -->
    <el-dialog v-model="popupVisible" :title="popupData.name || '方案详情'" width="600px">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="方案名称">{{ popupData.name }}</el-descriptions-item>
        <el-descriptions-item label="分区">{{ popupData.partition }}</el-descriptions-item>
        <el-descriptions-item label="状态">{{ popupData.status }}</el-descriptions-item>
        <el-descriptions-item label="负责人">{{ popupData.leader }}</el-descriptions-item>
        <el-descriptions-item label="最新漏损率">{{ popupData.lossRate }}%</el-descriptions-item>
        <el-descriptions-item label="发现漏点数">{{ popupData.leakCount }}</el-descriptions-item>
      </el-descriptions>
      <el-divider>监测历史</el-divider>
      <div style="height:260px;">
        <div ref="popupChartRef" style="width:100%;height:100%;"></div>
      </div>
      <template #footer>
        <el-button @click="popupVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, nextTick } from 'vue'
import * as echarts from 'echarts'
import ArcLayout from '@/components/arcMap/widgets/ArcLayout.vue'
import Graphic from '@arcgis/core/Graphic'
import SimpleMarkerSymbol from '@arcgis/core/symbols/SimpleMarkerSymbol'
import Point from '@arcgis/core/geometry/Point'

const partitionList = ['A区', 'B区', 'C区', 'D区']

// 假数据
const planList = ref([
  {
    id: 1,
    name: 'A区主干管探漏',
    partition: 'A区',
    status: '进行中',
    lossRate: 9.2,
    leader: '李四',
    leakCount: 2,
    lng: 95.702314,
    lat: 40.500206,
    monitorHistory: [
      { time: '2023-10-10 08:00', lossRate: 10.0 },
      { time: '2023-10-10 12:00', lossRate: 9.5 },
      { time: '2023-10-10 16:00', lossRate: 9.2 }
    ]
  },
  {
    id: 2,
    name: 'A区支管探漏',
    partition: 'A区',
    status: '已完成',
    lossRate: 4.8,
    leader: '王五',
    leakCount: 1,
    lng: 95.703314,
    lat: 40.501206,
    monitorHistory: [
      { time: '2023-10-10 08:00', lossRate: 5.2 },
      { time: '2023-10-10 12:00', lossRate: 5.0 },
      { time: '2023-10-10 16:00', lossRate: 4.8 }
    ]
  },
  {
    id: 3,
    name: 'B区主干管探漏',
    partition: 'B区',
    status: '未开始',
    lossRate: 7.1,
    leader: '赵六',
    leakCount: 0,
    lng: 95.701314,
    lat: 40.499206,
    monitorHistory: [
      { time: '2023-10-10 08:00', lossRate: 7.1 }
    ]
  },
  {
    id: 4,
    name: 'C区主干管探漏',
    partition: 'C区',
    status: '进行中',
    lossRate: 8.6,
    leader: '孙七',
    leakCount: 3,
    lng: 95.704314,
    lat: 40.502206,
    monitorHistory: [
      { time: '2023-10-10 08:00', lossRate: 9.0 },
      { time: '2023-10-10 12:00', lossRate: 8.8 },
      { time: '2023-10-10 16:00', lossRate: 8.6 }
    ]
  }
])

const searchParams = ref({
  name: '',
  partition: '',
  status: '',
  dateRange: []
})

const filteredPlans = computed(() => {
  return planList.value.filter(row => {
    const nameMatch = !searchParams.value.name || row.name.includes(searchParams.value.name)
    const partitionMatch = !searchParams.value.partition || row.partition === searchParams.value.partition
    const statusMatch = !searchParams.value.status || row.status === searchParams.value.status
    let dateMatch = true
    if (searchParams.value.dateRange?.length === 2) {
      const d = new Date(row.monitorHistory[row.monitorHistory.length - 1]?.time)
      const start = new Date(searchParams.value.dateRange[0])
      const end = new Date(searchParams.value.dateRange[1])
      dateMatch = d >= start && d <= end
    }
    return nameMatch && partitionMatch && statusMatch && dateMatch
  })
})

function handleSearch() {}
function resetSearch() {
  searchParams.value.name = ''
  searchParams.value.partition = ''
  searchParams.value.status = ''
  searchParams.value.dateRange = []
}

// 地图相关
const refArcLayout = ref()
const staticsState: { view?: __esri.MapView } = {}
const onMapLoaded = (view: __esri.MapView) => {
  staticsState.view = view
  addPlansToMap()
}
function addPlansToMap() {
  const view = staticsState.view
  if (!view) return
  view.graphics.removeAll()
  filteredPlans.value.forEach(plan => {
    let color: number[] = [40, 180, 40] // 绿色
    if (plan.lossRate > 8) color = [226, 40, 40] // 红色
    else if (plan.lossRate > 5) color = [226, 119, 40] // 橙色
    const markerSymbol = new SimpleMarkerSymbol({
      color,
      outline: { color: [255, 255, 255], width: 2 },
      size: 16
    })
    const point = new Point({ longitude: plan.lng, latitude: plan.lat })
    const graphic = new Graphic({ geometry: point, symbol: markerSymbol, attributes: plan })
    graphic.popupTemplate = {
      title: plan.name,
      content: () => {
        openPopup(plan)
        return ''
      }
    }
    view.graphics.add(graphic)
  })
}

// 方案点弹窗
const popupVisible = ref(false)
const popupData = ref<any>({ monitorHistory: [] })
const popupChartRef = ref()
let popupChartInstance: any = null
function openPopup(plan: any) {
  popupData.value = JSON.parse(JSON.stringify(plan))
  popupVisible.value = true
  nextTick(() => {
    if (!popupChartInstance) {
      popupChartInstance = echarts.init(popupChartRef.value)
    }
    const times = plan.monitorHistory.map((d: any) => d.time)
    popupChartInstance.setOption({
      title: { text: '漏损率历史曲线', left: 'center' },
      tooltip: { trigger: 'axis' },
      xAxis: { type: 'category', data: times },
      yAxis: { type: 'value', name: '漏损率(%)' },
      series: [
        { name: '漏损率', type: 'line', data: plan.monitorHistory.map((d: any) => d.lossRate) }
      ]
    })
  })
}
</script>

<style scoped>
.flow-leak-detection-container {
  padding: 20px;
  background: #fff;
  border-radius: 6px;
}
.filter-form {
  margin-bottom: 16px;
}
.map-container {
  width: 100%;
  height: 520px;
  margin-top: 10px;
  border-radius: 6px;
  overflow: hidden;
  background: #f5f7fa;
}
</style> 