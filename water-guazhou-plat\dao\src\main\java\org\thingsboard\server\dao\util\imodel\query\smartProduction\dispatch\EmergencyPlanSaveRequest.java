package org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartProduction.dispatch.EmergencyPlan;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

@Getter
@Setter
public class EmergencyPlanSaveRequest extends SaveRequest<EmergencyPlan> {
    // 预案名称
    @NotNullOrEmpty
    private String name;

    // 所属站点
    @NotNullOrEmpty
    private String stationId;

    // 注意事项
    private String remark;

    // 处理步骤。富文本
    @NotNullOrEmpty
    private String content;

    @Override
    protected EmergencyPlan build() {
        EmergencyPlan entity = new EmergencyPlan();
        entity.setCreator(currentUserUUID());
        entity.setCreateTime(createTime());
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    @Override
    protected EmergencyPlan update(String id) {
        EmergencyPlan entity = new EmergencyPlan();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(EmergencyPlan entity) {
        entity.setName(name);
        entity.setStationId(stationId);
        entity.setRemark(remark);
        entity.setContent(content);
    }
}