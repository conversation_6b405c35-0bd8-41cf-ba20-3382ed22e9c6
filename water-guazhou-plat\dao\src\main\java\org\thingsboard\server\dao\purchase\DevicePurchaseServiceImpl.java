package org.thingsboard.server.dao.purchase;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.dao.model.sql.purchase.DevicePurchase;
import org.thingsboard.server.dao.sql.purchase.DevicePurchaseMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.purchase.DevicePurchaseItemSaveRequest;
import org.thingsboard.server.dao.util.imodel.query.purchase.DevicePurchasePageRequest;
import org.thingsboard.server.dao.util.imodel.query.purchase.DevicePurchaseSaveRequest;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class DevicePurchaseServiceImpl implements DevicePurchaseService {
    @Autowired
    private DevicePurchaseMapper devicePurchaseMapper;

    @Autowired
    private DevicePurchaseItemService service;

    @Override
    public IPage<DevicePurchase> findAllConditional(DevicePurchasePageRequest request) {
        return devicePurchaseMapper.findByPage(request);
    }

    @Override
    @Transactional
    public DevicePurchase save(DevicePurchaseSaveRequest entity) {
        DevicePurchase devicePurchase = QueryUtil.saveOrUpdateOneByRequest(entity, devicePurchaseMapper::insert, devicePurchaseMapper::update);
        List<DevicePurchaseItemSaveRequest> items = entity.getItems(devicePurchase.getId());
        if (items != null) {
            // noinspection Convert2MethodRef 删除所有未在列表中的设备采购单条目
            service.removeAllByMainOnIdNotIn(entity.getId(),
                    items.stream().map(x -> x.getId()).filter(x -> x != null).collect(Collectors.toList()));
            service.saveAll(items);
        }
        // service.deleteAll(entity.getRemove());
        return devicePurchase;
    }

    @Override
    public boolean update(DevicePurchase entity) {
        return devicePurchaseMapper.update(entity);
    }

    @Override
    @Transactional
    public boolean delete(String id) {
        service.deleteByMainIdOnIdNotIn(id, null);
        return devicePurchaseMapper.deleteById(id) > 0;
    }

}
