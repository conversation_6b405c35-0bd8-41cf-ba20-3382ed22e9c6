import { RouteRecordRaw } from 'vue-router';

export const videoTest: RouteRecordRaw = {
  path: '/VideoTest',
  name: 'VideoTest',
  meta: {
    hidden: false,
    alwaysShow: true,
    title: '视频播放',
    icon: 'iconfont icon-camera',
    roles: ['CUSTOMER_USER', 'TENANT_ADMIN', 'TENANT_SUPPORT']
  },
  children: [
    {
      path: '视频接入',
      name: '视频接入',
      meta: { title: '视频接入', icon: 'iconfont icon-camera' },
      component: () =>
        import('@/views/MonitoringManagement/VideoAccess/VideoAccess.vue')
    },
    {
      path: '实时预览',
      name: '实时预览',
      meta: { title: '实时预览', icon: 'iconfont icon-camera' },
      component: () =>
        import('@/views/MonitoringManagement/LivePreview/LivePreview.vue')
    },
    {
      path: '视频分组',
      name: '视频分组',
      meta: { title: '视频分组', icon: 'iconfont icon-camera' },
      component: () =>
        import(
          '@/views/MonitoringManagement/GroupManagement/GroupManagement.vue'
        )
    }
  ]
};
