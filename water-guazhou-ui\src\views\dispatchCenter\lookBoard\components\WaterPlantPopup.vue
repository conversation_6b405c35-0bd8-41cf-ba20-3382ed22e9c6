<template>
  <div class="water-plant-popup">
    <div class="popup-header">
      <h3>{{ config.stationName || '水厂信息' }}</h3>
      <span class="update-time">更新时间: {{ config.lastUpdateTime }}</span>
    </div>
    
    <div class="popup-content">
      <div class="data-grid">
        <div class="data-item">
          <span class="label">今日出水量:</span>
          <span class="value">{{ config.waterOutput || '--' }}</span>
        </div>
        
        <div class="data-item">
          <span class="label">出水压力:</span>
          <span class="value">{{ config.waterPressure || '--' }}</span>
        </div>
        
        <div class="data-item">
          <span class="label">清水池液位:</span>
          <span class="value">{{ config.waterLevel || '--' }}</span>
        </div>
        
        <div class="data-item">
          <span class="label">用电量:</span>
          <span class="value">{{ config.powerConsumption || '--' }}</span>
        </div>
        
        <div class="data-item">
          <span class="label">运行状态:</span>
          <span class="value" :class="getStatusClass(config.operationStatus)">
            {{ config.operationStatus || '未知' }}
          </span>
        </div>
        
        <div class="data-item">
          <span class="label">上报日期:</span>
          <span class="value">{{ config.reportDate || '--' }}</span>
        </div>
      </div>
      
      <div class="popup-actions">
        <el-button type="primary" size="small" @click="viewDetails">
          查看详情
        </el-button>
        <el-button type="default" size="small" @click="viewHistory">
          历史数据
        </el-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
interface WaterPlantConfig {
  stationName?: string;
  reportDate?: string;
  waterOutput?: string;
  waterPressure?: string;
  waterLevel?: string;
  powerConsumption?: string;
  operationStatus?: string;
  lastUpdateTime?: string;
  stationId?: string;
}

const props = defineProps<{
  visible: boolean;
  config: WaterPlantConfig;
}>();

const emit = defineEmits(['view-details', 'view-history']);

const getStatusClass = (status?: string) => {
  switch (status) {
    case '正常':
      return 'status-normal';
    case '异常':
      return 'status-error';
    case '维护':
      return 'status-warning';
    default:
      return 'status-unknown';
  }
};

const viewDetails = () => {
  emit('view-details', props.config);
};

const viewHistory = () => {
  emit('view-history', props.config);
};
</script>

<style lang="scss" scoped>
.water-plant-popup {
  width: 280px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.popup-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 12px 16px;
  
  h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
  }
  
  .update-time {
    font-size: 12px;
    opacity: 0.9;
    margin-top: 4px;
    display: block;
  }
}

.popup-content {
  padding: 16px;
}

.data-grid {
  display: grid;
  gap: 12px;
  margin-bottom: 16px;
}

.data-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
  
  .label {
    font-size: 14px;
    color: #666;
    font-weight: 500;
  }
  
  .value {
    font-size: 14px;
    color: #333;
    font-weight: 600;
    
    &.status-normal {
      color: #52c41a;
    }
    
    &.status-error {
      color: #ff4d4f;
    }
    
    &.status-warning {
      color: #faad14;
    }
    
    &.status-unknown {
      color: #999;
    }
  }
}

.popup-actions {
  display: flex;
  gap: 8px;
  justify-content: center;
  
  .el-button {
    flex: 1;
  }
}

/* 暗色主题支持 */
:deep(.dark) {
  .water-plant-popup {
    background: #1f1f1f;
    color: #fff;
    
    .data-item {
      border-bottom-color: #333;
      
      .label {
        color: #ccc;
      }
      
      .value {
        color: #fff;
      }
    }
  }
}
</style>
