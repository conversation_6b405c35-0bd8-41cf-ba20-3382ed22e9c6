import{m as _,d as b,c as y,r as x,g,n as C,q as o,F as l,G as p,i as a,h as E,an as F,b as u,J as k,H as N,I as q,K as B,L as I,C as U}from"./index-r0dFAfgr.js";function L(m,n){return _({url:"/api/auth/changePassword",method:"post",data:{currentPassword:m,newPassword:n}})}const S=b({__name:"changePassword",setup(m){const n=y(),e=x({dialogVisible:!1,password:{currentPassword:"",newPassword:"",verifyPassword:""},pwdForm:{currentPassword:[{required:!0,message:"请输入原密码",trigger:"blur"}],newPassword:[{required:!0,message:"请输入新密码",trigger:"blur"},{validator:(i,s,r)=>{var t;s?(e.password.verifyPassword!==""&&((t=n.value)==null||t.validate<PERSON>ield("verifyPassword")),r()):r(new Error("请输入新密码"))},trigger:"blur"},{min:8,max:20,message:"密码不能小于8位，大于20位",trigger:"blur"}],verifyPassword:[{required:!0,message:"请输入再次新密码",trigger:"blur"},{validator:(i,s,r)=>{s!==e.password.newPassword?r(new Error("两次输入密码不一致!")):r()},trigger:"blur"},{min:8,max:20,message:"密码不能小于8位，大于20位",trigger:"blur"}]}}),P=()=>{var i;(i=n.value)==null||i.validate(s=>{if(s){if(e.password.currentPassword===e.password.newPassword)return u.error("新密码与原密码一致，请重新输入"),!1;L(e.password.currentPassword,e.password.newPassword).then(()=>{u.success("修改成功"),e.dialogVisible=!1,f()}).catch(r=>{u.error(r.data.message),console.log(r)})}else return u.error("请按提示输入信息"),!1})},c=()=>{e.dialogVisible=!1,e.password.currentPassword="",e.password.newPassword="",e.password.verifyPassword=""},f=()=>{e.password.currentPassword="",e.password.newPassword="",e.password.verifyPassword=""};return(i,s)=>{const r=k,t=N,w=q,V=B,v=I;return g(),C("div",null,[o(r,{type:"default",text:!0,onClick:s[0]||(s[0]=d=>a(e).dialogVisible=!0)},{default:l(()=>s[5]||(s[5]=[p(" 修改密码 ")])),_:1}),a(e).dialogVisible?(g(),E(v,{key:0,modelValue:a(e).dialogVisible,"onUpdate:modelValue":s[4]||(s[4]=d=>a(e).dialogVisible=d),title:"修改密码","close-on-click-modal":!1,class:"alarm-design",width:"40%",onClose:f},{default:l(()=>[o(V,{ref_key:"refForm",ref:n,model:a(e).password,"label-width":"100px",rules:a(e).pwdForm,class:"change-pwd"},{default:l(()=>[o(w,{label:"当前密码",prop:"currentPassword"},{default:l(()=>[o(t,{modelValue:a(e).password.currentPassword,"onUpdate:modelValue":s[1]||(s[1]=d=>a(e).password.currentPassword=d),type:"password",placeholder:"请输入当前密码"},null,8,["modelValue"])]),_:1}),o(w,{label:"新密码",prop:"newPassword"},{default:l(()=>[o(t,{modelValue:a(e).password.newPassword,"onUpdate:modelValue":s[2]||(s[2]=d=>a(e).password.newPassword=d),type:"password",placeholder:"请输入新密码"},null,8,["modelValue"])]),_:1}),o(w,{label:"确认新密码",prop:"verifyPassword"},{default:l(()=>[o(t,{modelValue:a(e).password.verifyPassword,"onUpdate:modelValue":s[3]||(s[3]=d=>a(e).password.verifyPassword=d),type:"password",placeholder:"请输入新密码"},null,8,["modelValue"])]),_:1}),o(w,{class:"pwd-operation"},{default:l(()=>[o(r,{type:"primary",onClick:P},{default:l(()=>s[6]||(s[6]=[p(" 修改密码 ")])),_:1}),o(r,{type:"default",onClick:c},{default:l(()=>s[7]||(s[7]=[p(" 取 消 ")])),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"])):F("",!0)])}}}),H=U(S,[["__scopeId","data-v-20b0cce3"]]);export{H as default};
