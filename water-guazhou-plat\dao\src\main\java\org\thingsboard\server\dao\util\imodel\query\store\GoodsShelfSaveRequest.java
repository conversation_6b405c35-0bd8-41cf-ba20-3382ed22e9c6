package org.thingsboard.server.dao.util.imodel.query.store;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.store.GoodsShelf;
import org.thingsboard.server.dao.util.imodel.query.IStarHttpRequest;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

import java.util.Date;

import static org.thingsboard.server.dao.util.reflection.ExceptionUtils.testNumber;

@Getter
@Setter
public class GoodsShelfSaveRequest extends SaveRequest<GoodsShelf> {

    // 父级ID
    @NotNullOrEmpty
    private String parentId;

    // 货架编码
    @NotNullOrEmpty
    private String code;

    // 货架名称
    @NotNullOrEmpty
    private String name;

    // 排序，升序
    private Integer orderNum;

    // 备注
    private String remark;

    @Override
    public String valid(IStarHttpRequest request) {
        testNumber(code, "编码只能为数值");
        return super.valid(request);
    }

    @Override
    protected GoodsShelf build() {
        GoodsShelf entity = new GoodsShelf();
        entity.setCreateTime(new Date());
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    @Override
    protected GoodsShelf update(String id) {
        GoodsShelf entity = new GoodsShelf();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(GoodsShelf entity) {
        entity.setParentId(parentId);
        entity.setCode(code.toString());
        entity.setName(name);
        entity.setOrderNum(orderNum);
        entity.setRemark(remark);
    }
}