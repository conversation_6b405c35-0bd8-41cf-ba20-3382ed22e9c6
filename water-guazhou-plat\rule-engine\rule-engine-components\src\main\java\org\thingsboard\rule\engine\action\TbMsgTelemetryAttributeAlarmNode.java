/**
 * Copyright © 2016-2019 The Thingsboard Authors
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.rule.engine.action;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.thingsboard.rule.engine.api.*;
import org.thingsboard.rule.engine.telemetry.TbMsgTimeseriesNodeConfiguration;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.Device;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.VO.AlarmLinkedUser;
import org.thingsboard.server.common.data.alarm.*;
import org.thingsboard.server.common.data.constantsAttribute.PropAttribute;
import org.thingsboard.server.common.data.id.DeviceId;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.kv.AttributeKvEntry;
import org.thingsboard.server.common.data.kv.KvEntry;
import org.thingsboard.server.common.data.kv.TsKvEntry;
import org.thingsboard.server.common.data.plugin.ComponentType;
import org.thingsboard.server.common.data.telemetryAttribute.ResponseTs;
import org.thingsboard.server.common.data.utils.AlarmUtils;
import org.thingsboard.server.common.data.utils.DateUtils;
import org.thingsboard.server.common.data.utils.TimeDiff;
import org.thingsboard.server.common.msg.TbMsg;
import org.thingsboard.server.common.msg.TbMsgMetaData;
import org.thingsboard.server.dao.alarm.AlarmCountService;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.model.sql.AlarmCountEntity;
import org.thingsboard.server.dao.model.sql.StationAttrEntity;
import org.thingsboard.server.dao.model.sql.StationEntity;
import org.thingsboard.server.dao.model.sql.alarmV2.AlarmCenter;
import org.thingsboard.server.dao.model.sql.alarmV2.AlarmRuleSmart;
import org.thingsboard.server.dao.util.mapping.JacksonUtil;

import java.math.BigDecimal;
import java.util.*;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 09/21
 * 根据创建遥测数据的属性来进行报警过滤
 */
@Slf4j
@RuleNode(
        type = ComponentType.ACTION,
        name = "遥测数据报警",
        configClazz = TbMsgTimeseriesNodeConfiguration.class,
        nodeDescription = "遥测数据触发告警",
        nodeDetails = "过滤数据设置的警报",
        uiResources = {"static/rulenode/rulenode-core-config.js", "static/rulenode/rulenode-core-config.css"},
        configDirective = "tbActionNodeTimeseriesConfig",
        icon = "file_upload"
)
public class TbMsgTelemetryAttributeAlarmNode implements TbNode {

    @Override
    public void init(TbContext ctx, TbNodeConfiguration configuration) throws TbNodeException {
    }

    /**
     * 获取数据并使用属性条件进行筛选
     *
     * @param ctx
     * @param msg
     */
    @Override
    public void onMsg(TbContext ctx, TbMsg msg) {
        try {
            filterData2(ctx, msg);
            try {
                // 新报警
                filterData3(ctx, msg);
            } catch (Exception e) {
                e.printStackTrace();
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            ctx.tellNext(msg, TbRelationTypes.SUCCESS);
        }

    }

    @Override
    public void destroy() {
        log.info(this.getClass().getName() + "进行销毁" + System.currentTimeMillis());
    }

    /**
     * Filter all data based on criteria
     *
     * @param tbContext
     * @param tbMsg
     * @return
     */
    private void filterData2(TbContext tbContext, TbMsg tbMsg) {
        try {
            log.info("进入报警规则链");
            Device device = tbContext.getDeviceService().findDeviceById(new DeviceId(UUIDConverter.fromString(tbMsg.getMetaData().getValue(DataConstants.DEVICE_ID))));
            if (device == null) {
                return;
            }
            List<AttrAlarmJson> attributeKvEntrys = tbContext.getAlarmJsonService().findByDevice(device.getId());
            if (attributeKvEntrys == null || attributeKvEntrys.size() < 1) {
                return;
            }
            //报警设置分组
            Map<String, List<AttrAlarmJson>> attr = new HashMap<>();
            attributeKvEntrys.forEach(e -> {
                if (attr.containsKey(e.getAttribute())) {
                    attr.get(e.getAttribute()).add(e);
                } else {
                    attr.put(e.getAttribute(), Stream.of(e).collect(Collectors.toList()));
                }
            });
            //报警过滤
            tbMsg.getMetaData().getTsKvEntryList().forEach(tsKvEntry -> {
                if (attr.containsKey(tsKvEntry.getKey())) {
                    attr.get(tsKvEntry.getKey()).forEach(a -> {
                        alarmFilter(a, tbContext, tsKvEntry, tbMsg, device);
                    });
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }

    }


    private void filterData3(TbContext tbContext, TbMsg tbMsg) {
        try {
            log.info("进入报警规则链");
            // 查询设备信息
            Device device = tbContext.getDeviceService().findDeviceById(new DeviceId(UUIDConverter.fromString(tbMsg.getMetaData().getValue(DataConstants.DEVICE_ID))));
            if (device == null) {
                return;
            }
            // 设备的变量列表
            List<String> attrList = new ArrayList<>();
            Map<String, TsKvEntry> dataMap = new HashMap<>();
            for (TsKvEntry tsKvEntry : tbMsg.getMetaData().getTsKvEntryList()) {
                String key = tsKvEntry.getKey();
                attrList.add(key);

                dataMap.put(key, tsKvEntry);
            }
            // 查询站点属性列表
            List<StationAttrEntity> stationAttrList = tbContext.getStationAttrService().findByDeviceIdAndAttr(device.getUuidId().toString(), attrList);
            if (stationAttrList == null || stationAttrList.isEmpty()) {
                log.info("属性不存在");
                return;
            }
            Map<String, StationAttrEntity> stationAttrMap = new HashMap<>();
            for (StationAttrEntity stationAttr : stationAttrList) {
                stationAttrMap.put(stationAttr.getStationId() + stationAttr.getAttr(), stationAttr);
            }
            // 查询报警配置列表
            List<AlarmRuleSmart> alarmRuleList = tbContext.getAlarmRuleSmartService().findByStationAttrList(stationAttrList.stream().map(StationAttrEntity::getStationId).collect(Collectors.toList()), (stationAttrList.stream().map(StationAttrEntity::getAttr).collect(Collectors.toList())));
            if (alarmRuleList == null || alarmRuleList.isEmpty()) {
                return;
            }
            // 查询站点列表
            List<String> stationIdList = alarmRuleList.stream().map(a -> a.getStationId()).collect(Collectors.toList());
            List<StationEntity> stationList = tbContext.getStationService().findByStationIdList(stationIdList);
            Map<String, StationEntity> stationMap = stationList.stream().collect(Collectors.toMap(StationEntity::getId, station -> station));

            // 遍历告警配置
            for (AlarmRuleSmart alarmRule : alarmRuleList) {
                boolean reBuildFlag = true;
                // 是否重复报警
                AlarmCenter alarmCenter = tbContext.getAlarmCenterService().lastAlarmByAlarmRuleId(alarmRule.getId());
                if (alarmCenter != null && alarmCenter.getAlarmStatus().equals(DataConstants.ALARMV2_ALARM_STATUS.NEW.getValue())) {
                    reBuildFlag = false;
                }

                // 报警间隔
                if (alarmRule.getInterval() != null && reBuildFlag) {
                    if (alarmCenter != null) {
                        if (alarmCenter.getTime().getTime() + alarmRule.getInterval() * 1000 > System.currentTimeMillis()) {
                            reBuildFlag = false;
                        }
                    }
                }

                if ("0".equals(alarmRule.getEnabled())) {
                    reBuildFlag = false;
                }

                // 是否报警时间
                Calendar calendar = Calendar.getInstance();
                // 当前时间
                int currentHour = calendar.get(Calendar.HOUR_OF_DAY);
                int currentMinute = calendar.get(Calendar.MINUTE);
                // 是否到生效时间
                if (StringUtils.isNotBlank(alarmRule.getEnableStartTime()) && reBuildFlag) {
                    String[] split = alarmRule.getEnableStartTime().split(":");
                    if (currentHour < Integer.valueOf(split[0])) {
                        reBuildFlag = false;
                    }
                    if (currentHour == Integer.valueOf(split[0]) && currentMinute < Integer.valueOf(split[1])) {
                        reBuildFlag = false;
                    }
                }
                if (StringUtils.isNotBlank(alarmRule.getEnableEndTime()) && reBuildFlag) {
                    String[] split = alarmRule.getEnableEndTime().split(":");
                    if (Integer.valueOf(split[0]) != 0 && Integer.valueOf(split[1]) != 0) {
                        if (currentHour > Integer.valueOf(split[0])) {
                            reBuildFlag = false;
                        }
                        if (currentHour == Integer.valueOf(split[0]) && currentMinute > Integer.valueOf(split[1])) {
                            reBuildFlag = false;
                        }
                    }
                }

                // 是否报警周几
                if (StringUtils.isNotBlank(alarmRule.getEnableWeekday()) && reBuildFlag) {
                    String[] split = alarmRule.getEnableWeekday().split(",");
                    int weekday = calendar.get(Calendar.DAY_OF_WEEK) - 1;
                    if (weekday == 0) {
                        weekday = 1;
                    }
                    boolean flag = false;
                    for (String s : split) {
                        if (weekday == Integer.valueOf(s)) {
                            flag = true;
                            break;
                        }
                    }
                    if (!flag) {
                        reBuildFlag = false;
                    }
                }
                // 查询最新数据
                TsKvEntry tsKvEntry = dataMap.get(alarmRule.getAttr());
                if (tsKvEntry == null) {
                    continue;
                }

                Double x = alarmRule.getUpLimitValue().doubleValue();
                Double y = alarmRule.getDownLimitValue().doubleValue();

                String valueAsString = tsKvEntry.getValueAsString();
                Double data = Double.valueOf(valueAsString);
                if (data > x || data < y) {
                    if (reBuildFlag) {
                        StationEntity station = stationMap.get(alarmRule.getStationId());
                        createAlarmV3(tbContext, alarmRule, tsKvEntry, station, stationAttrMap.get(station.getId() + alarmRule.getAttr()), "3", device);
                    }
                } else {
                    // 恢复告警
                    tbContext.getAlarmCenterService().restoreAlarm(alarmRule.getId());
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    private void createAlarmV3(TbContext tbContext, AlarmRuleSmart alarmRule, TsKvEntry tsKvEntry, StationEntity station, StationAttrEntity stationAttr, String level, Device device) {
        Date now = new Date();
        AlarmCenter alarmCenter = new AlarmCenter();
        alarmCenter.setStationId(alarmRule.getStationId());
        alarmCenter.setTitle(alarmRule.getStationName());
        alarmCenter.setTime(now);
        alarmCenter.setAlarmType(alarmRule.getWarnType());
        alarmCenter.setAlarmLevel("3");
        alarmCenter.setDeviceId(UUIDConverter.fromTimeUUID(device.getUuidId()));


        String title = "";
        if (station != null) {
            title = station.getName() + "的[" + device.getName() + "]设备" + stationAttr.getName();
        } else {
            title = alarmCenter.getTitle();
        }
        String alarmInfo = "";
        if ("1".equals(alarmRule.getType())) {
            alarmInfo = title + " 采集值为" + tsKvEntry.getValueAsString() + ", 触发报警不在" + alarmRule.getUpLimitValue() + "与" + alarmRule.getDownLimitValue() + "之间";
            ;
            alarmCenter.setProcessMethod("请及时处理");
        }
        alarmCenter.setAlarmInfo(alarmInfo);
        alarmCenter.setAlarmStatus(DataConstants.ALARMV2_ALARM_STATUS.NEW.getValue());// 报警中
        alarmCenter.setProcessStatus(DataConstants.ALARMV2_PROCESS_STATUS.NEW.getValue());// 未处理
        alarmCenter.setTenantId(alarmRule.getTenantId());
        alarmCenter.setAlarmRuleId(alarmRule.getId());
        alarmCenter.setDeviceId(alarmRule.getDeviceId());

        // 保存数据
        tbContext.getAlarmCenterService().save(alarmCenter);

    }


    public void alarmFilter(AttrAlarmJson attrAlarmJson, TbContext tbContext, KvEntry kvEntry, TbMsg msg, Device device) {
        //1.检测是否处于报警时间段内
        log.info("准备执行报警检测流程");
        if (!TimeDiff.isOnTimeSharing(DateUtils.date2Str(new Date(), DateUtils.DATE_FORMATE_DEFAULT), Collections.singletonList(attrAlarmJson.getPeriod()))) {
            return;
        }
        if (attrAlarmJson.getIsCycle()) {
            log.info("周期报警");
            cycleAlarm(attrAlarmJson, tbContext, kvEntry, msg, device);
        } else {
            log.info("非周期报警");
            normalAlarm(attrAlarmJson, tbContext, kvEntry, msg, device);
        }
    }


    /**
     * 周期报警过滤
     *
     * @param attrAlarmJson
     * @param tbContext
     * @param kvEntry
     * @param msg
     * @param device
     */
    public void cycleAlarm(AttrAlarmJson attrAlarmJson, TbContext tbContext, KvEntry kvEntry, TbMsg msg, Device device) {
        //2.检测是否有未恢复的报警信息
        try {
            List<Alarm> alarms = tbContext.getAlarmService().findAlarmByJsonId(attrAlarmJson.getId()).get();
            if (alarms != null && alarms.size() > 0) {
                //检测最后一条报警是否处于当前报警周期内
                Alarm lastAlarm = alarms.get(alarms.size() - 1);
                if (isOnCycle(attrAlarmJson, lastAlarm)) {
                    return;
                }
            }
            alarmCheck(attrAlarmJson, tbContext, kvEntry, msg, device);
        } catch (InterruptedException e) {
            e.printStackTrace();
        } catch (ExecutionException e) {
            e.printStackTrace();
        }
    }


    /**
     * 非周期报警过滤
     *
     * @param attrAlarmJson
     * @param tbContext
     * @param kvEntry
     * @param msg
     * @param device
     */
    public void normalAlarm(AttrAlarmJson attrAlarmJson, TbContext tbContext, KvEntry kvEntry, TbMsg msg, Device device) {
        //2.检测是否有未恢复的报警信息
        List<Alarm> alarm = tbContext.getAlarmService().findUnRestoreAlarmByJsonId(attrAlarmJson.getId(), DataConstants.UN_RESTORE);
        log.info("检测是否有未恢复的报警信息， size = {}", alarm != null ? alarm.size() : 0);
        if (alarm != null && alarm.size() > 0) {
            //3.有尚未恢复的报警,进入报警恢复流程()
            alarmRestore(attrAlarmJson, tbContext, kvEntry, msg, alarm.get(0), device);
        } else {
            //4.没有尚未恢复的报警，则进行报警过滤
            alarmCheck(attrAlarmJson, tbContext, kvEntry, msg, device);
        }
    }


    /**
     * 报警恢复
     *
     * @param attrAlarmJson
     * @param tbContext
     * @param kvEntry
     * @param msg
     */
    public void alarmRestore(AttrAlarmJson attrAlarmJson, TbContext tbContext, KvEntry kvEntry, TbMsg msg, Alarm alarm, Device device) {
        //先进行创建报警过滤
        try {
            JsonNode jsonNode = JacksonUtil.toJsonNode(attrAlarmJson.getParams());
            if (jsonNode.has(DataConstants.REQUEST_ALARM_USE_LAST_DATA) && jsonNode.get(DataConstants.REQUEST_ALARM_USE_LAST_DATA).asBoolean()) {
                ObjectNode objectNode = (ObjectNode) jsonNode;
                objectNode.put(DataConstants.REQUEST_ALARM_LAST_DATA, getLastData(tbContext, attrAlarmJson.getDeviceId(), attrAlarmJson.getAttribute(), false, null, device.getTenantId(), kvEntry));
            }
            ScriptEngine restoreJsEngine = tbContext.createJsScriptEngine(attrAlarmJson.getRestoreScript());
            com.fasterxml.jackson.databind.JsonNode restoreJson = executeJson(restoreJsEngine, JacksonUtil.toString(jsonNode), tbContext, msg);
            restoreJsEngine.destroy();
            if (restoreJson.asText().equals(DataConstants.TRUE)) {
                //当触发数据恢复成功，则进行数据恢复操作
                clearAlarm(alarm, tbContext, kvEntry);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 检查报警设置
     *
     * @param attrAlarmJson
     * @param tbContext
     * @param kvEntry
     * @param msg
     */
    public void alarmCheck(AttrAlarmJson attrAlarmJson, TbContext tbContext, KvEntry kvEntry, TbMsg msg, Device device) {
        //先进行创建报警过滤
        log.info("进入报警过滤");
        try {
            ScriptEngine buildDetailsJsEngine = tbContext.createJsScriptEngine(attrAlarmJson.getAlarmScript());
            JsonNode jsonNode = JacksonUtil.toJsonNode(attrAlarmJson.getParams());
            if ((jsonNode.has(DataConstants.REQUEST_ALARM_USE_LAST_DATA) && jsonNode.get(DataConstants.REQUEST_ALARM_USE_LAST_DATA).asBoolean()) || attrAlarmJson.getIsCycle()) {
                ObjectNode objectNode = (ObjectNode) jsonNode;
                objectNode.put(DataConstants.REQUEST_ALARM_LAST_DATA, getLastData(tbContext, attrAlarmJson.getDeviceId(), attrAlarmJson.getAttribute(), attrAlarmJson.getIsCycle(), attrAlarmJson.getCycle(), device.getTenantId(), kvEntry));
            }
            com.fasterxml.jackson.databind.JsonNode createJson;
            if (attrAlarmJson.getIsCycle()) {
                JSONObject jsonObject = JSONObject.parseObject(msg.getData());
                jsonObject.put(kvEntry.getKey(), jsonNode.get(DataConstants.REQUEST_ALARM_LAST_DATA).asText());
                createJson = executeJson(buildDetailsJsEngine, JacksonUtil.toString(jsonNode), tbContext, new TbMsg(msg.getId(), msg.getType(), msg.getOriginator(), msg.getMetaData(), jsonObject.toJSONString(), msg.getRuleChainId(), msg.getRuleNodeId(), msg.getClusterPartition()));
            } else {
                createJson = executeJson(buildDetailsJsEngine, JacksonUtil.toString(jsonNode), tbContext, msg);
            }
            buildDetailsJsEngine.destroy();
            //检查是否触发报警成功
            if (createJson.asText().equals(DataConstants.TRUE)) {
                if (attrAlarmJson.getIsCycle()) {
                    createCycleAlarm(attrAlarmJson, tbContext, kvEntry, msg);
                } else {
                    createOrUpdateAlarmAlarm(attrAlarmJson, tbContext, kvEntry, msg);
                }

            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 当触发报警后新建报警或更新报警信息
     *
     * @param attrAlarmJson
     * @param tbContext
     * @param kvEntry
     * @param msg
     */
    private void createCycleAlarm(AttrAlarmJson attrAlarmJson, TbContext tbContext, KvEntry kvEntry, TbMsg msg) throws Exception {
        Device device = tbContext.getDeviceService().findDeviceById(new DeviceId(attrAlarmJson.getDeviceId().getId()));
        AlarmReport alarmReport = new AlarmReport();
        alarmReport.setTime(DateUtils.date2Str(System.currentTimeMillis(), DateUtils.DATE_FORMATE_DEFAULT_2));
        alarmReport.setAlarmValue(kvEntry.getValueAsString());
        alarmReport.setSetValue(getSettingData(attrAlarmJson));
        alarmReport.setProp(getAttrName(tbContext, device.getId(), attrAlarmJson.getAttribute()));
        alarmReport.setDeviceName(device.getName());
        Alarm newAlarm = new Alarm();
        newAlarm.setTenantId(attrAlarmJson.getTenantId());
        newAlarm.setAlarmJsonId(UUIDConverter.fromTimeUUID(attrAlarmJson.getId().getId()));
        newAlarm.setStatus(AlarmStatus.CONFIRM_UNACK);
        newAlarm.setLevel(attrAlarmJson.getSeverity());
        newAlarm.setValue(String.valueOf(kvEntry.getValue()));
        newAlarm.setType(attrAlarmJson.getAlarmType());
        newAlarm.setStartTs(System.currentTimeMillis());
        newAlarm.setPropagate(attrAlarmJson.isPropagate());
        newAlarm.setOriginator(attrAlarmJson.getDeviceId());
        newAlarm.setSeverity(attrAlarmJson.getSeverity());
        newAlarm.setAlarmJsonName(attrAlarmJson.getAlarmName());
        newAlarm.setAlarmCycle(attrAlarmJson.getCycle());
        List<AlarmConf> confs = new ArrayList<>();
        confs.add(new AlarmConf(System.currentTimeMillis(), "触发报警 " + "数据值为" + kvEntry.getValueAsString(), DataConstants.ALARM));
        JsonNode jsonNode = JacksonUtil.toJsonNode(new AlarmType(attrAlarmJson.getAlarmType()));
        ObjectNode objectNode = (ObjectNode) jsonNode;
        objectNode.put(DataConstants.ALARM_RECORDING, JacksonUtil.toJsonNode(confs));
        newAlarm.setDetails(objectNode);
        tbContext.getAlarmService().createOrUpdateAlarm(newAlarm);
        sendEmailAndSms(tbContext, newAlarm, device, alarmReport);

        // 新增报警统计
        createAlarmCount(attrAlarmJson, tbContext);
    }

    private void createAlarmCount(AttrAlarmJson attrAlarmJson, TbContext tbContext) {
        if (StringUtils.isNotBlank(attrAlarmJson.getGroupName())) {
            AlarmCountService alarmCountService = tbContext.getAlarmCountService();
            AlarmCountEntity alarmCountEntity = new AlarmCountEntity();
            alarmCountEntity.setAlarmId(UUIDConverter.fromTimeUUID(attrAlarmJson.getId().getId()));
            alarmCountEntity.setTime(System.currentTimeMillis());
            alarmCountEntity.setDeviceId(UUIDConverter.fromTimeUUID(attrAlarmJson.getDeviceId().getId()));
            alarmCountEntity.setGroup(attrAlarmJson.getGroupName());

            alarmCountService.save(alarmCountEntity);
        }
    }

    /**
     * 当触发报警后新建报警或更新报警信息
     *
     * @param attrAlarmJson
     * @param tbContext
     * @param kvEntry
     * @param msg
     */
    private void createOrUpdateAlarmAlarm(AttrAlarmJson attrAlarmJson, TbContext tbContext, KvEntry kvEntry, TbMsg msg) throws Exception {
        List<Alarm> alarm = tbContext.getAlarmService().findUnClearByJsonId(attrAlarmJson.getId(), DataConstants.UN_CLEAR);
        if (alarm == null || alarm.size() < 1) {
            createCycleAlarm(attrAlarmJson, tbContext, kvEntry, msg);
        } else {
            Alarm oldAlarm = alarm.get(0);
            //如果该报警不是已恢复状态，则不进行报警详情记录
            if (oldAlarm.getStatus() == AlarmStatus.RESTORE_ACK) {
                Iterator<JsonNode> iterator = oldAlarm.getDetails().get(DataConstants.ALARM_RECORDING).elements();
                List<AlarmConf> confs = new ArrayList<>();
                iterator.forEachRemaining(jsonNode -> {
                    confs.add(new AlarmConf(jsonNode.get(DataConstants.ALARM_TIMESTAMP).longValue(), jsonNode.get(DataConstants.ALARM_INFO).asText(), jsonNode.get(DataConstants.ALARM_STATUS).asText()));
                });
                ArrayNode objectNode = (ArrayNode) oldAlarm.getDetails().get(DataConstants.ALARM_RECORDING);
                objectNode.add(JacksonUtil.toJsonNode(new AlarmConf(System.currentTimeMillis(), "触发报警 " + "数据值为" + kvEntry.getValueAsString(), DataConstants.ALARM)));
                ObjectNode details = (ObjectNode) oldAlarm.getDetails();
                details.put(DataConstants.ALARM_RECORDING, AlarmUtils.convertAlarm(objectNode));
                oldAlarm.setDetails(details);
                oldAlarm.setStatus(AlarmStatus.CONFIRM_UNACK);
                tbContext.getAlarmService().createOrUpdateAlarm(oldAlarm);
            }
        }

        // 新增报警统计
        createAlarmCount(attrAlarmJson, tbContext);

    }
    /**
     * 当数据已经恢复正常以后，修改报警的状态
     */
    private void clearAlarm(Alarm alarm, TbContext tbContext, KvEntry kvEntry) {
        //如果当前状态为已恢复，不进行处理
        if (alarm.getStatus() == AlarmStatus.RESTORE_ACK) {
            return;
        }
        switch (alarm.getStatus()) {
            case CONFIRM_ACK: {
                alarm.setStatus(AlarmStatus.CLEARED_ACK);
                alarm.setClearTs(System.currentTimeMillis());
                break;
            }
            case CONFIRM_UNACK: {
                alarm.setStatus(AlarmStatus.RESTORE_ACK);
                break;
            }
        }
        ArrayNode objectNode = (ArrayNode) alarm.getDetails().get(DataConstants.ALARM_RECORDING);
        objectNode.add(JacksonUtil.toJsonNode(new AlarmConf(System.currentTimeMillis(), "数据恢复到正常值，数据值为" + kvEntry.getValueAsString(), DataConstants.RESTORE)));
        ObjectNode details = (ObjectNode) alarm.getDetails();
        details.put(DataConstants.ALARM_RECORDING, AlarmUtils.convertAlarm(objectNode));
        alarm.setDetails(details);
        tbContext.getAlarmService().createOrUpdateAlarm(alarm);
    }


    private com.fasterxml.jackson.databind.JsonNode executeJson(ScriptEngine scriptEngine, String params, TbContext tbContext, TbMsg msg) throws Exception {
        return tbContext.getJsExecutor().executeAsync(() -> {
            TbMsg dummyMsg = msg;
            if (params != null) {
                TbMsgMetaData metaData = msg.getMetaData().copy();
                JsonNode jsonNode = JacksonUtil.toJsonNode(params);
                Map<String, String> metadata = new ObjectMapper().convertValue(jsonNode, new TypeReference<Map<String, String>>() {
                });
                metadata.entrySet().forEach(entry -> {
                    metaData.putValue(entry.getKey(), entry.getValue());
                });
                dummyMsg = tbContext.transformMsg(msg, msg.getType(), msg.getOriginator(), metaData, msg.getData());
            }
            return scriptEngine.executeJson(dummyMsg);
        }).get();
    }


    private void sendEmailAndSms(TbContext tbContext, Alarm alarm, Device device, AlarmReport alarmReport) {
        List<AlarmLinkedUser> users = tbContext.getAlarmJsonService().getAlarmLinkedUser(alarm.getAlarmJsonId());
        List<AlarmLinkedUser> extraUsers = tbContext.getAlarmJsonService().getAllLinkedExtraUser(device.getTenantId(), alarm.getAlarmJsonId());
        String subject = alarm.getLevel() + "报警提醒";
        String emailBody = getEmailBody(alarmReport);
        tbContext.getMailExecutor().execute(() -> {
            extraUsers.forEach(user -> {
                if (user.isSendSms()) {
                    tbContext.getMailService().sendSMS(user, alarmReport);
                }
                if (user.isSendEmail()) {
                    tbContext.getMailService().sendEmail(user, subject, emailBody, DataConstants.LOG_TYPE_DEVICE_ALARM);
                }
            });
            users.forEach(user -> {
                if (user.isSendSms()) {
                    tbContext.getMailService().sendSMS(user, alarmReport);
                }
                if (user.isSendEmail()) {
                    tbContext.getMailService().sendEmail(user, subject, emailBody, DataConstants.LOG_TYPE_DEVICE_ALARM);
                }
            });
        });
    }


    public String getEmailBody(AlarmReport alarmReport) {
        String body = "您的设备" + alarmReport.getDeviceName() + alarmReport.getProp() + "于" + alarmReport.getTime() + "触发告警" + ",触发值为" + alarmReport.getAlarmValue() + ",报警设定值为" + alarmReport.getSetValue() + "请尽快前往处理！";
        return body;
    }


    /**
     * 获取
     *
     * @param tbContext
     * @param deviceId
     * @param attr
     * @param isCycle
     * @return
     */
    private String getLastData(TbContext tbContext, DeviceId deviceId, String attr, boolean isCycle, String limit, TenantId tenantId, KvEntry kvEntry) {
        BigDecimal data = new BigDecimal(kvEntry.getValueAsString());
        try {
            if (isCycle) {
                long start = System.currentTimeMillis();
                long end = System.currentTimeMillis();
                Calendar calendar = Calendar.getInstance();
                calendar.setTimeInMillis(start);
                switch (limit) {
                    case DateUtils.DAY: {
                        //设置到该天的首个时间点：
                        calendar.set(Calendar.HOUR_OF_DAY, 0);
                        calendar.set(Calendar.MINUTE, 0);
                        calendar.set(Calendar.SECOND, 0);
                        calendar.set(Calendar.MILLISECOND, 0);
                        start = calendar.getTimeInMillis();
                        calendar.set(Calendar.DAY_OF_YEAR, calendar.get(Calendar.DAY_OF_YEAR) + 1);
                        end = calendar.getTimeInMillis();
                        limit = "1d";
                        break;
                    }
                    case DateUtils.MONTH: {
                        //设置到该月的首个时间点：
                        calendar.set(Calendar.DAY_OF_MONTH, 1);
                        calendar.set(Calendar.HOUR_OF_DAY, 0);
                        calendar.set(Calendar.MINUTE, 0);
                        calendar.set(Calendar.SECOND, 0);
                        calendar.set(Calendar.MILLISECOND, 0);
                        start = calendar.getTimeInMillis();
                        calendar.set(Calendar.MONTH, calendar.get(Calendar.MONTH) + 1);
                        end = calendar.getTimeInMillis();
                        limit = "1nc";
                        break;
                    }
                    default: {
                        //设置到该年的首个时间点：
                        calendar.set(Calendar.MONTH, 1);
                        calendar.set(Calendar.DAY_OF_MONTH, 1);
                        calendar.set(Calendar.HOUR_OF_DAY, 0);
                        calendar.set(Calendar.MINUTE, 0);
                        calendar.set(Calendar.SECOND, 0);
                        calendar.set(Calendar.MILLISECOND, 0);
                        start = calendar.getTimeInMillis();
                        calendar.set(Calendar.YEAR, calendar.get(Calendar.YEAR) + 1);
                        end = calendar.getTimeInMillis();
                        limit = "1yc";
                        break;
                    }
                }

                List<ResponseTs> responseTs = tbContext.getBaseObtainDataService().getFirstDataFromOpenTSDB(start, end, UUIDConverter.fromTimeUUID(deviceId.getId()) + "." + attr, tenantId, limit);
                if (responseTs != null && responseTs.size() > 0) {
                    ResponseTs response = responseTs.get(0);
                    if (response.getDps() != null && response.getDps().size() > 0) {
                        data = new BigDecimal(response.getDps().get(response.getDps().keySet().toArray()[0]));
                    }
                }
                data = new BigDecimal(kvEntry.getValueAsString()).subtract(data);
            } else {
                TsKvEntry kvEntrie = tbContext.getTimeseriesService().findLatestByKey(deviceId, attr);
                if (kvEntrie != null && kvEntrie.getValueAsString() != null) {
                    data = new BigDecimal(kvEntrie.getValueAsString());
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return data.toString();
    }

    private String getSettingData(AttrAlarmJson attrAlarmJson) {
        String settingValue = null;
        JSONObject jsonObject = JSONObject.parseObject(attrAlarmJson.getParams());
        if (jsonObject.containsKey("alarmMinValue") && jsonObject.containsKey("alarmMaxValue")) {
            settingValue = jsonObject.getString("alarmMinValue") + "-" + jsonObject.getString("alarmMaxValue");
        } else if (jsonObject.containsKey("alarmMinValue")) {
            settingValue = jsonObject.getString("alarmMinValue");
        } else if (jsonObject.containsKey("alarmMaxValue")) {
            settingValue = jsonObject.getString("alarmMaxValue");
        }
        if (jsonObject.containsKey("cAlarmFrom") && jsonObject.containsKey("cAlarmTo")) {
            settingValue = jsonObject.getString("cAlarmFrom") + "变动到" + jsonObject.getString("cAlarmTo");
        } else if (jsonObject.containsKey("cAlarmFrom")) {
            settingValue = jsonObject.getString("cAlarmFrom");
        } else if (jsonObject.containsKey("cAlarmTo")) {
            settingValue = jsonObject.getString("cAlarmTo");
        }
        return settingValue;
    }


    private String getAttrName(TbContext tbContext, DeviceId deviceId, String prop) {
        String attrName = null;
        try {
            AttributeKvEntry attributeKvEntry = tbContext.getAttributesService().findNotFuture(deviceId, DataConstants.SHARED_SCOPE, ModelConstants.PROR);
            //当没取到attribute的数据时直接返回
            if (attributeKvEntry == null) {
                return null;
            }
            List<PropAttribute> props = new org.codehaus.jackson.map.ObjectMapper().readValue(attributeKvEntry.getValueAsString(), new org.codehaus.jackson.type.TypeReference<List<PropAttribute>>() {
            });
            for (PropAttribute p : props) {
                if (p.getPropertyCategory().equalsIgnoreCase(prop)) {
                    attrName = p.getName();
                    break;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return attrName;
    }

    /**
     * 检查报警是否在报警周期内
     *
     * @param attrAlarmJson
     * @param alarm
     * @return
     */
    public boolean isOnCycle(AttrAlarmJson attrAlarmJson, Alarm alarm) {
        long start = System.currentTimeMillis();
        long end = System.currentTimeMillis();
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(start);
        switch (attrAlarmJson.getCycle()) {
            case DateUtils.DAY: {
                //设置到该天的首个时间点：
                calendar.set(Calendar.HOUR_OF_DAY, 0);
                calendar.set(Calendar.MINUTE, 0);
                calendar.set(Calendar.SECOND, 0);
                calendar.set(Calendar.MILLISECOND, 0);
                start = calendar.getTimeInMillis();
                calendar.set(Calendar.DAY_OF_YEAR, calendar.get(Calendar.DAY_OF_YEAR) + 1);
                end = calendar.getTimeInMillis();
                break;
            }
            case DateUtils.MONTH: {
                //设置到该月的首个时间点：
                calendar.set(Calendar.DAY_OF_MONTH, 1);
                calendar.set(Calendar.HOUR_OF_DAY, 0);
                calendar.set(Calendar.MINUTE, 0);
                calendar.set(Calendar.SECOND, 0);
                calendar.set(Calendar.MILLISECOND, 0);
                start = calendar.getTimeInMillis();
                calendar.set(Calendar.MONTH, calendar.get(Calendar.MONTH) + 1);
                end = calendar.getTimeInMillis();
                break;
            }
            default: {
                //设置到该年的首个时间点：
                calendar.set(Calendar.MONTH, 1);
                calendar.set(Calendar.DAY_OF_MONTH, 1);
                calendar.set(Calendar.HOUR_OF_DAY, 0);
                calendar.set(Calendar.MINUTE, 0);
                calendar.set(Calendar.SECOND, 0);
                calendar.set(Calendar.MILLISECOND, 0);
                start = calendar.getTimeInMillis();
                calendar.set(Calendar.YEAR, calendar.get(Calendar.YEAR) + 1);
                end = calendar.getTimeInMillis();
                break;
            }
        }

        if (alarm.getStartTs() > start && alarm.getStartTs() < end) {
            return true;
        }
        return false;
    }


}