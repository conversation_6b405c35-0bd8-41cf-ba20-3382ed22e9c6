import{d as E,r as I,g as u,n as c,q as e,F as t,aB as g,aJ as k,h as A,G as s,aK as B,aL as F,I as G,dz as x,dA as C,H as T,J as U,K as q,C as w}from"./index-r0dFAfgr.js";const O={class:"filter-panel"},R=E({__name:"FilterPanel",props:{loading:{type:Boolean,default:!1},stationOptions:{type:Array,default:()=>[]}},emits:["query","reset"],setup(m,{emit:y}){const r=y,a=I({stationIds:[],timeGranularity:"day",pumpType:"electric",schemeName:"",schemeNameAlt:""}),V=()=>{r("query",{...a})},b=()=>{a.stationIds=[],a.timeGranularity="day",a.pumpType="electric",a.schemeName="",a.schemeNameAlt="",r("reset")};return(h,l)=>{const d=B,p=F,n=G,i=x,v=C,_=T,f=U,N=q;return u(),c("div",O,[e(N,{model:a,inline:""},{default:t(()=>[e(n,{label:"数据来源:"},{default:t(()=>[e(p,{modelValue:a.stationIds,"onUpdate:modelValue":l[0]||(l[0]=o=>a.stationIds=o),placeholder:"设备组",multiple:""},{default:t(()=>[(u(!0),c(g,null,k(m.stationOptions,o=>(u(),A(d,{key:o.value,label:o.label,value:o.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(n,{label:"分析周期:"},{default:t(()=>[e(p,{modelValue:a.timeGranularity,"onUpdate:modelValue":l[1]||(l[1]=o=>a.timeGranularity=o),placeholder:"日"},{default:t(()=>[e(d,{label:"小时",value:"hour"}),e(d,{label:"日",value:"day"}),e(d,{label:"周",value:"week"}),e(d,{label:"月",value:"month"})]),_:1},8,["modelValue"])]),_:1}),e(n,{label:"泵站类型:"},{default:t(()=>[e(v,{modelValue:a.pumpType,"onUpdate:modelValue":l[2]||(l[2]=o=>a.pumpType=o)},{default:t(()=>[e(i,{label:"electric"},{default:t(()=>l[5]||(l[5]=[s("电水泵")])),_:1}),e(i,{label:"water"},{default:t(()=>l[6]||(l[6]=[s("中水泵")])),_:1})]),_:1},8,["modelValue"])]),_:1}),e(n,{label:"方案名称:"},{default:t(()=>[e(_,{modelValue:a.schemeName,"onUpdate:modelValue":l[3]||(l[3]=o=>a.schemeName=o),placeholder:"输入方案"},null,8,["modelValue"])]),_:1}),e(n,{label:"方案名称:"},{default:t(()=>[e(_,{modelValue:a.schemeNameAlt,"onUpdate:modelValue":l[4]||(l[4]=o=>a.schemeNameAlt=o),placeholder:"输入方案"},null,8,["modelValue"])]),_:1}),e(n,null,{default:t(()=>[e(f,{type:"primary",loading:m.loading,onClick:V},{default:t(()=>l[7]||(l[7]=[s("查询")])),_:1},8,["loading"]),e(f,{onClick:b},{default:t(()=>l[8]||(l[8]=[s("重置")])),_:1})]),_:1})]),_:1},8,["model"])])}}}),K=w(R,[["__scopeId","data-v-aa78ce0d"]]);export{K as default};
