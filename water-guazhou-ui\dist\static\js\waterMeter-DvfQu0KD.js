import{d as Y,r as w,c as N,W as g,o as j,am as R,j as W,g as F,n as D,q as C,i as v,t as x,_ as G,C as J}from"./index-r0dFAfgr.js";import{C as B}from"./index-CcDafpIP.js";import{r as I,o as h}from"./chart-wy3NEK2T.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import{a as l,c as V}from"./LayerHelper-Cn-iiqxI.js";import{b as q,i as Q}from"./QueryHelper-ILO3qZqg.js";import{P as y}from"./pipe-nogVzCHG.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./project-DUuzYgGl.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./executeForIds-BLdIsxvI.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";const H=Y({__name:"waterMeter",props:{view:{},menu:{}},emits:["highlightMark","addMarks"],setup(k,{emit:S}){const c=S,i=k,T=w({pipeLayerOption:[]}),A=N(),u=N([{label:"0个",value:"水表总数"}]),O=w({height:250,dataList:[],pagination:{hide:!0},columns:[{label:"名称",prop:"LANEWAY",sortable:!0,formatter:t=>t.LANEWAY||"未知"},{label:"数量",prop:"ObjectId"}],handleRowClick:t=>{c("highlightMark",i.menu," LANEWAY like '%"+t.LANEWAY+"%' ",t.LANEWAY)}}),f=w({group:[{id:"meterstatus",fieldset:{type:"underline",desc:"水表状态占比图"},fields:[{type:"vchart",option:I(),style:{width:"100%",height:"150px"},itemContainerStyle:{marginBottom:0},handleClick:t=>{c("highlightMark",i.menu,t.data.name==="未知"?"":' OPENCLOSE="'+(t.data.name||"")+'" ',t.data.nameAlias)}}]},{id:"diameter",fieldset:{type:"underline",desc:"按口径统计水表数量"},fields:[{type:"vchart",option:h(),style:{width:"100%",height:"150px"},itemContainerStyle:{marginBottom:0},handleClick:t=>{c("highlightMark",i.menu,t.data.name==="未知"?"":" DIAMETER="+(t.data.name||"")+" ",t.data.nameAlias)}}]},{id:"METERTYPE",fieldset:{type:"underline",desc:"按类型统计水表数量"},fields:[{type:"vchart",option:h(),style:{width:"100%",height:"150px"},handleClick:t=>{c("highlightMark",i.menu,t.data.name==="未知"?"":' METERTYPE="'+(t.data.name||"")+'" ',t.data.nameAlias)}}]},{id:"LANEWAY",fieldset:{type:"underline",desc:"按所在道路统计水表数量"},fields:[{type:"input",field:"LANEWAY",appendBtns:[{perm:!0,text:"刷新",click:()=>E()}],onChange:()=>E()},{type:"table",config:O}]}],labelPosition:"top",gutter:12}),M=async t=>{if(!i.view)return;const e=l(i.view,void 0,void 0,"水表");return e.length?await q(window.SITE_CONFIG.GIS_CONFIG.gisService+window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService+"/"+e[0],Q({returnGeometry:!1,where:""})):void 0},L=async()=>{var r,p,m,o;if(!i.view)return;const t=(r=f.group.find(a=>a.id==="meterstatus"))==null?void 0:r.fields,e=(t==null?void 0:t.length)&&t[0],s=l(i.view,void 0,void 0,"水表"),n=(o=(m=(p=(await y({usertoken:g().gToken,layerids:JSON.stringify(s),group_fields:JSON.stringify(["OPENCLOSE"]),statistic_field:"ObjectId",statistic_type:"1",where:"",f:"pjson"})).data.result)==null?void 0:p.rows)==null?void 0:m[0])==null?void 0:o.rows.map(a=>({name:a.OPENCLOSE||"未知",nameAlias:a.OPENCLOSE||"未知",value:a.ObjectId||0}));e&&(e.option=I(n,"个","",0))},b=async()=>{var r,p,m,o;if(!i.view)return;const t=(r=f.group.find(a=>a.id==="diameter"))==null?void 0:r.fields,e=(t==null?void 0:t.length)&&t[0],s=l(i.view,void 0,void 0,"水表"),n=(o=(m=(p=(await y({usertoken:g().gToken,layerids:JSON.stringify(s),group_fields:JSON.stringify(["DIAMETER"]),statistic_field:"ObjectId",statistic_type:"1",where:"",f:"pjson"})).data.result.rows)==null?void 0:p[0])==null?void 0:m.rows)==null?void 0:o.map(a=>({name:a.DIAMETER||"未知",nameAlias:"DN"+a.DIAMETER||"未知",value:a.ObjectId||0}));e&&(e.option=h(n,"个"))},P=async()=>{var r,p,m;if(!i.view)return;const t=(r=f.group.find(o=>o.id==="METERTYPE"))==null?void 0:r.fields,e=(t==null?void 0:t.length)&&t[0],s=l(i.view,void 0,void 0,"水表"),n=((m=(p=(await y({usertoken:g().gToken,layerids:JSON.stringify(s),group_fields:JSON.stringify(["METERTYPE"]),statistic_field:"ObjectId",statistic_type:"1",where:"",f:"pjson"})).data.result.rows[0])==null?void 0:p.rows)==null?void 0:m.map(o=>({name:o.METERTYPE||"未知",nameAlias:o.METERTYPE||"未知",value:o.ObjectId||0})))||[];e&&(e.option=h(n,"个"))},E=async()=>{var n,r;if(!i.view)return;const t=(n=A.value)==null?void 0:n.dataForm.LANEWAY,e=t?" LANEWAY like '%"+t+"%' ":"",s=l(i.view,void 0,void 0,"水表"),d=await y({usertoken:g().gToken,layerids:JSON.stringify(s),group_fields:JSON.stringify(["LANEWAY"]),statistic_field:"ObjectId",statistic_type:"1",where:e||"",f:"pjson"});O.dataList=((r=d.data.result.rows[0])==null?void 0:r.rows)||[]},_=()=>{L(),b(),P(),E()};return j(async()=>{_(),T.pipeLayerOption=await V(i.view);const t=await M();u.value[0].label=(t||"0")+" 个"}),R(()=>W().isDark,()=>_()),(t,e)=>{const s=G;return F(),D("div",null,[C(v(B),{modelValue:v(u),"onUpdate:modelValue":e[0]||(e[0]=d=>x(u)?u.value=d:null),span:24},null,8,["modelValue"]),C(s,{ref_key:"refForm",ref:A,config:v(f)},null,8,["config"])])}}}),ne=J(H,[["__scopeId","data-v-897e9628"]]);export{ne as default};
