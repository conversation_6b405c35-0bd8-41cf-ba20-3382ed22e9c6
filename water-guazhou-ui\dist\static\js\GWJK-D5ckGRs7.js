import{_ as C}from"./index-BlG8PIOK.js";import{G as S}from"./bengzhan-Dc7fbek7.js";import{d as V,a8 as B,r as D,c as k,o as E,g as e,n as a,p as i,aB as _,aJ as y,av as p,bh as d,i as l,q as G,F as N,an as b,aw as F,C as J}from"./index-r0dFAfgr.js";const L={class:"table-header-row"},M={key:0,class:"list-wrapper"},T=["title"],H={key:0,class:"isRank"},I={key:0,style:{"margin-left":"2px"}},K={key:0,style:{"margin-left":"2px"}},O={key:1,class:"empty"},q=V({__name:"GWJK",props:{size:{},height:{}},setup(v){const c=v,g=B(()=>({"--wrapper-height":(c.height?c.height:c.size==="small"?160:240)+"px"})),o=D({classOption:{step:.2,limitMoveNum:5},header:[{label:"序号",prop:"sort",minWidth:60,isRank:!0},{label:"监测点",prop:"name",minWidth:160},{label:"瞬时流量",prop:"instance_flow",unit:"m³/h",minWidth:130,color:"#58FEF4"},{label:"压力",prop:"pressure",minWidth:100,color:"#FFEE93",unit:"MPa"}],data:[]}),W=k(),x=k(),z=async()=>{try{const f=(await S({stationType:"压力监测站,测流压站"})).data||[];o.data=f.map(r=>{var s,u;const t=((s=r.dataList)==null?void 0:s.find(m=>m.property==="Instantaneous_flow"))||{},n=((u=r.dataList)==null?void 0:u.find(m=>m.property==="pressure"))||{};return{sort:(t==null?void 0:t.value)??0,name:r.name,instance_flow:((t==null?void 0:t.value)??"--")+" "+((t==null?void 0:t.unit)??""),pressure:((n==null?void 0:n.value)??"--")+" "+((n==null?void 0:n.unit)??""),delta:"-2.19",color:"#FF6565"}}).sort((r,t)=>t.sort-r.sort).map((r,t)=>(r.sort=t+1,r)),o.data=[...o.data]}catch(h){console.log(h)}};return E(()=>{z()}),(h,f)=>{const r=C;return e(),a("div",{class:F(["scroll-list",c.size]),style:p(l(g))},[i("ul",{ref_key:"refHeader",ref:W,class:"list-header"},[i("li",L,[(e(!0),a(_,null,y(l(o).header,(t,n)=>(e(),a("div",{key:n,class:"table-cell",style:p({minWidth:t.minWidth+"px"})},d(t.label),5))),128))])],512),l(o).data.length?(e(),a("div",M,[G(r,{data:l(o).data,"class-option":l(o).classOption},{default:N(()=>[i("ul",{ref_key:"refTable",ref:x},[(e(!0),a(_,null,y(l(o).data,(t,n)=>(e(),a("li",{key:n,class:"table-body-row"},[(e(!0),a(_,null,y(l(o).header,(s,u)=>(e(),a("div",{key:u,class:"table-cell",style:p({minWidth:s.minWidth+"px"}),title:t[s.prop]??"--"},[s.isRank?(e(),a("div",H,[i("span",{style:p({color:s.color})},d(t[s.prop]??"--"),5),s.unit?(e(),a("span",I,d(s.unit),1)):b("",!0)])):(e(),a(_,{key:1},[i("span",{style:p({color:s.color})},d(t[s.prop]??"--"),5),t.unit?(e(),a("span",K,d(t.unit),1)):b("",!0)],64))],12,T))),128))]))),128))],512)]),_:1},8,["data","class-option"])])):(e(),a("div",O,"暂无数据"))],6)}}}),A=J(q,[["__scopeId","data-v-e72d76ae"]]);export{A as default};
