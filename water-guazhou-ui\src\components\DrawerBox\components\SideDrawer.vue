<template>
  <div
    class="layout-drawer"
    :class="[
      'layout-drawer-' + (direction || 'ltr'),
      modelValue === true ? '' : 'collapsed',
      theme || (appStore.isDark ? 'dark' : 'light'),
      absolute ? 'absolute' : ''
    ]"
    :style="styleVars"
  >
    <div v-if="title" class="right-drawer">
      <div class="right-drawer-title">
        <div class="title-container">
          <div class="title-wrapper">
            <div class="title-icon">
              <slot name="title-icon">
                <Icon icon="ep:monitor" />
              </slot>
            </div>
            <div class="title-text">
              <slot name="title">
                {{ title }}
              </slot>
            </div>
            <div class="title-indicator"></div>
          </div>
        </div>
      </div>
      <div class="right-drawer-content overlay-y">
        <slot name="default"></slot>
      </div>
    </div>
    <slot v-else name="default"> </slot>
    <FolderBtn
      v-if="hideBar !== true"
      :direction="direction"
      :collapsed="modelValue"
      :bar-position="barPosition"
      @collapse="handleCollapse"
    ></FolderBtn>
  </div>
</template>
<script lang="ts" setup>
import { useAppStore } from '@/store';
import { Icon } from '@iconify/vue';
const appStore = useAppStore();
const emit = defineEmits<{ (e: 'collapse', isCollapsed: boolean): any }>();
const props = defineProps<{
  /**
   * 隐藏边上的把手
   */
  hideBar?: boolean;
  /** 默认从左向右 */
  direction?: 'ltr' | 'rtl' | 'btt' | 'ttb';
  /** 是否默认折叠 */
  modelValue: boolean;
  theme?: 'darkblue' | 'dark' | 'light';
  /** 按钮的位置，左右不支持left和right,同理，上下不支持top和bottom,默认center */
  barPosition?: 'left' | 'top' | 'center' | 'bottom' | 'right';
  /** 仅对左右有效 */
  absolute?: boolean;
  /** 设置抽屉宽度/高度，默认350,单位px, 对左右抽屉对应宽度，对上下抽屉则是对应高度 */
  width?: number;
  title?: string;
  minWidth?: number;
  padding?: number;
}>();
const styleVars = computed(() => {
  return {
    '--width': (props.width || 350) + 'px',
    '--collapsedWidth':
      -(props.width || 350) +
      (props.modelValue ? 0 : props.minWidth || 0) +
      'px'
  };
});
const handleCollapse = () => {
  emit('collapse', !props.modelValue);
};
</script>
<style lang="scss" scoped>
.layout-drawer {
  background-color: var(--el-bg-color);
}

.layout-drawer {
  transition: all ease 0.5s;
  z-index: 1;

  &.layout-drawer-ltr,
  &.layout-drawer-rtl {
    position: relative;
    width: var(--width);
    height: 100%;

    &.absolute {
      position: absolute;
    }
  }

  &.layout-drawer-btt,
  &.layout-drawer-ttb {
    position: absolute;
    width: 100%;
    height: 350px;
  }
  &.layout-drawer-btt {
    bottom: 0;
    left: 0;
    border-radius: 5px 5px 0 0;
  }
  &.layout-drawer-ltr {
    border-radius: 0 5px 5px 0;
    top: 0;
    left: 0;
  }
  &.layout-drawer-rtl {
    // border-radius: 5px 0 0 5px;
    right: 0;
    top: 0;
  }
  &.layout-drawer-ttb {
    top: 0;
    left: 0;
    border-radius: 0 0 5px 5px;
  }
  &.collapsed {
    &.layout-drawer-ltr {
      margin-left: var(--collapsedWidth);
    }
    &.layout-drawer-rtl {
      margin-right: var(--collapsedWidth);
    }
    &.layout-drawer-btt {
      margin-bottom: -350px;
    }
    &.layout-drawer-ttb {
      margin-top: -350px;
    }

  }
}
.right-drawer {
  width: 100%;
  height: 100%;
}
.right-drawer-title {
  height: 48px;
  display: flex;
  align-items: center;
  position: relative;
  background-color: #f6faff;
  border-bottom: 1px solid #eaeefb;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, #1e88e5, #0d47a1);
    opacity: 0.8;
  }
  
  .title-container {
    display: flex;
    align-items: center;
    height: 100%;
    padding-left: 16px;
    position: relative;
    
    .title-wrapper {
      display: flex;
      align-items: center;
      position: relative;
      height: 100%;
    
      .title-indicator {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 3px;
        background: linear-gradient(90deg, #1e88e5, #00c6ff, #0072ff);
        border-radius: 2px;
        transition: width 0.3s ease;
      }
      
      .title-icon {
        font-size: 18px;
        color: #1e88e5;
        display: flex;
        align-items: center;
        margin-right: 10px;
      }
      
      .title-text {
        font-size: 15px;
        font-weight: 600;
        background: linear-gradient(90deg, #1e88e5, #0d47a1);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}

.dark {
  .right-drawer-title {
    background-color: #1a1d29;
    border-bottom: 1px solid #252a3a;
    
    &::before {
      background: linear-gradient(90deg, #42a5f5, #0d47a1);
    }
    
    .title-container {
      .title-wrapper {
        .title-indicator {
          background: linear-gradient(90deg, #42a5f5, #00c6ff, #0072ff);
        }
        
        .title-icon {
          color: #42a5f5;
        }
        
        .title-text {
          background: linear-gradient(90deg, #42a5f5, #90caf9);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          color: #e0e0e0;
        }
      }
    }
  }
}

.right-drawer-content {
  height: calc(100% - 48px);
  width: 100%;
  padding: 16px;
}
</style>
