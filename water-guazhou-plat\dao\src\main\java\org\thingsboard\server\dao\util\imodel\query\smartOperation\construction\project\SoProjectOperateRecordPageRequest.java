package org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoProjectOperateRecord;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;

@Getter
@Setter
public class SoProjectOperateRecordPageRequest extends AdvancedPageableQueryEntity<SoProjectOperateRecord, SoProjectOperateRecordPageRequest> {
    // 工程编号
    private String code;

    // 工程名称
    private String name;

    @Override
    protected boolean clampToDay() {
        return true;
    }

}
