<!-- gis流量监测点 -->
<template>
  <div class="onemap-panel-wrapper">
    <Cards v-model="cardsvalue" :span="12"></Cards>
    <Form ref="refForm" :config="FormConfig"> </Form>
    <div class="table-box">
      <FormTable :config="TableConfig"></FormTable>
    </div>
  </div>
</template>
<script lang="ts" setup>
import Point from '@arcgis/core/geometry/Point.js';
import { GetWaterFlowStationList } from '@/api/mapservice/onemap';
import { PopImage } from '@/views/arcMap/components';
import { ring } from '../../components/components/chart';
import { Cards } from '../../components';
import { useAppStore } from '@/store';
import { getStationImageUrl } from '@/utils/URLHelper';

const emit = defineEmits(['highlightMark', 'addMarks']);
const props = defineProps<{
  view?: __esri.MapView;
  menu: IMenuItem;
}>();

const cardsvalue = ref([
  { label: '0 个', value: '流量计总数' },
  { label: '0 %', value: '报警率' }
]);

const refForm = ref<IFormIns>();

const statusOptions = [
  { name: 'offline', label: '离线' },
  { name: 'alarm', label: '报警' },
  { name: 'online', label: '正常' }
];
const FormConfig = reactive<IFormConfig>({
  group: [
    {
      id: 'chart',
      fieldset: {
        desc: '监测状态统计',
        type: 'underline',
        style: {
          marginTop: 0
        }
      },
      fields: [
        {
          type: 'vchart',
          option: ring(),
          style: {
            height: '150px'
          }
        }
      ]
    },
    {
      id: 'tab',
      fields: [
        {
          type: 'input',
          field: 'name',
          appendBtns: [
            { perm: true, text: '刷新', click: () => refreshData(true) }
          ],
          onChange: () => refreshData()
        },
        {
          type: 'tabs',
          field: 'type',
          tabs: [
            { label: '全部', value: 'all' },
            ...statusOptions.map((item) => ({ ...item, value: item.name }))
          ],
          tabType: 'simple',
          onChange: () => refreshData()
        }
      ]
    }
  ],
  labelPosition: 'top',
  gutter: 12,
  defaultValue: {
    type: 'all'
  }
});

const TableConfig = reactive<ITable>({
  indexVisible: true,
  dataList: [],
  pagination: {
    hide: true,
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page;
      TableConfig.pagination.limit = size;
    },
    layout: 'total,sizes, jumper'
  },
  handleRowClick: (row) => handleLocate(row),
  columns: [
    {
      minWidth: 200,
      label: '名称',
      prop: 'name',
      sortable: true
    },
    {
      minWidth: 80,
      label: '状态',
      prop: 'status',
      formatter: (row) => {
        return (
          statusOptions.find((o) => o.name === row.status)?.label || row.status
        );
      }
    },
    {
      minWidth: 160,
      label: '更新时间',
      prop: 'lastTime',
      sortable: true
    }
  ]
});

const TableConfig_Detail = reactive<ITable>({
  dataList: [],
  columns: [],
  pagination: {
    refreshData: ({ page, size }) => {
      TableConfig_Detail.pagination.page = page;
      TableConfig_Detail.pagination.limit = size;
      refreshData();
    }
  }
});
const refreshData = async (all?: boolean) => {
  TableConfig.loading = true;
  try {
    const status = refForm.value?.dataForm.type;

    const res = await GetWaterFlowStationList({
      // projectId: store.business.selectedProject?.value,
      name: refForm.value?.dataForm.name,
      status: status === 'all' ? '' : status
    });
    TableConfig.dataList = res.data?.data || [];
    const field = FormConfig.group[0].fields[0] as IFormVChart;
    const total: number = res.data?.data?.length || 0;
    const windows: IArcPopConfig[] = [];
    const statusDatas: any[] = [];
    res.data?.data?.map((item) => {
      // const transValue = transNumberUnit(item.todayWaterSupply || 0)
      const location = item.location?.split(',');
      if (location?.length === 2) {
        const point = new Point({
          longitude: location[0],
          latitude: location[1],
          spatialReference: props.view?.spatialReference
        });
        windows.push({
          id: item.stationId,
          visible: false,
          x: point.x,
          y: point.y,
          offsetY: -40,
          title: item.name,
          customComponent: shallowRef(PopImage),
          customConfig: {
            info: {
              type: 'attrs',
              imageUrl: item.imgs,
              stationId: item.stationId
            }
          },
          attributes: {
            path: props.menu.path,
            id: item.stationId,
            row: item
          },
          symbolConfig: {
            url: getStationImageUrl(
              item.name?.indexOf('热') !== -1 ? '测流压站.png' : '测流点.png'
            )
          }
        });
      }
      let statusData = statusDatas.find((o) => o.name === item.status);
      const { label } = statusOptions.find((o) => o.name === item.status) || {};
      if (!statusData) {
        statusData = {
          name: item.status,
          nameAlias: label,
          value: 1,
          // valueAlias: '1',
          scale: '0%'
        };
        statusDatas.push(statusData);
      } else {
        statusData.value++;
      }
    }) || [];
    statusDatas.map((item) => {
      item.scale =
        total === 0
          ? '0%'
          : ((Number(item.value) / total) * 100).toFixed(2) + '%';
      item.value =
        res.data?.data?.filter((d) => d.status === item.name)?.length || 0;
      return item;
    });
    if (all) {
      const tabs = FormConfig.group.find((item) => item.id === 'tab')
        ?.fields[1] as ITabs;
      if (tabs) {
        tabs.tabs = tabs.tabs.map((item) => {
          const data = statusDatas.find((o) => o.name === item.value);
          const label =
            statusOptions.find((o) => o.name === item.value)?.label || '';
          item.label = label + '(' + (data?.value || 0) + ')';
          return item;
        });
        const totalTab = tabs.tabs.find((item) => item.value === 'all');
        totalTab && (totalTab.label = '全部(' + total + ')');
        field && (field.option = ring(statusDatas, '个', '', 0));
      }

      cardsvalue.value[0].label = total + '个';
      cardsvalue.value[1].label =
        statusDatas.find((item) => item.name === 'alarm')?.scale || '0 %';
    }
    emit('addMarks', {
      windows,
      customWinComp: shallowRef(PopImage)
    });
  } catch (error) {
    console.dir(error);
  }
  TableConfig.loading = false;
};
const handleLocate = async (row?: any) => {
  emit('highlightMark', props.menu, row?.stationId);
};
onMounted(() => {
  refreshData(true);
});
watch(
  () => useAppStore().isDark,
  () => refreshData(true)
);
</script>

<style lang="scss" scoped>
.onemap-panel-wrapper {
  min-height: 610px;
  height: 100%;
}
.chart-box {
  width: 100%;
  height: 150px;
}
.table-box {
  height: calc(100% - 380px);
  min-height: 200px;
}
</style>
