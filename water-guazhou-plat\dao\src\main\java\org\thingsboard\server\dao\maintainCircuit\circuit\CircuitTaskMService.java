package org.thingsboard.server.dao.maintainCircuit.circuit;

import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.maintainCircuit.circuit.CircuitTaskM;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.Date;
import java.util.List;

/**
 * 班组
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-08-24
 */
public interface CircuitTaskMService {
    PageData getList(String code, String name, String planName, String teamName, String userName, String status, String auditStatus, String deviceLabelCode, String deviceId, String userId, Date startStartTime, Date startEndTime, Date endStartTime, Date endEndTime, int page, int size, String tenantId);

    CircuitTaskM getDetail(String mainId);

    CircuitTaskM save(CircuitTaskM circuitTaskM);

    IstarResponse delete(List<String> ids);

    void reviewer(CircuitTaskM circuitTaskM);

    void changeStatus(CircuitTaskM circuitTaskM);

    List<CircuitTaskM> findAll();

    List statistics(Long startTime, Long endTime, String tenantId);

    boolean checkAuditor(String id, String userId);

    IstarResponse receive(String id, String userId);

    Integer getNotCompleteNum(String userId, String tenantId);
}
