package org.thingsboard.server.dao.util;

import org.thingsboard.server.dao.model.VO.DeviceDataTableInfoVO;

import java.util.ArrayList;
import java.util.List;

public class DeviceTableInfoUtil {

    /**
     * 将字符串数组转换为设备数据表格结构
     * 字符串格式：key:value
     */
    public static List<DeviceDataTableInfoVO> stringArrayToDeviceTableInfoList(String... data) {
        // 返回结果
        List<DeviceDataTableInfoVO> deviceDataTableInfoList = new ArrayList<>();

        for (String s : data) {
            String[] columnString = s.split(":");
            DeviceDataTableInfoVO tableInfo = new DeviceDataTableInfoVO();
            tableInfo.setColumnValue(columnString[0]);
            tableInfo.setColumnName(columnString[1]);
            if (columnString.length > 2) {
                tableInfo.setUnit(columnString[2]);
            }

            deviceDataTableInfoList.add(tableInfo);
        }

        return deviceDataTableInfoList;
    }

}
