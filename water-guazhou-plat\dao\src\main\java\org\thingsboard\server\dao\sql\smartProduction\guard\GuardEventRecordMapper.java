package org.thingsboard.server.dao.sql.smartProduction.guard;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.thingsboard.server.dao.model.sql.smartProduction.guard.GuardEventRecord;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.guard.GuardEventRecordPageRequest;

@Mapper
public interface GuardEventRecordMapper extends BaseMapper<GuardEventRecord> {
    IPage<GuardEventRecord> findByPage(GuardEventRecordPageRequest request);

    int deleteByRecordId(@Param("recordId")String recordId);



}
