package org.thingsboard.server.dao.smartPipe;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.request.PartitionMountRequest;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.PartitionFireHydrant;
import org.thingsboard.server.dao.sql.smartPipe.PartitionFireHydrantMapper;

import java.util.Date;
import java.util.List;

/**
 *
 */
@Service
public class PartitionFireHydrantServiceImpl implements PartitionFireHydrantService {

    @Autowired
    private PartitionFireHydrantMapper partitionFireHydrantMapper;

    @Override
    public PartitionFireHydrant save(PartitionFireHydrant partitionFireHydrant) {
        if (StringUtils.isBlank(partitionFireHydrant.getId())) {
            partitionFireHydrant.setCreateTime(new Date());
            partitionFireHydrantMapper.insert(partitionFireHydrant);
        } else {
            partitionFireHydrantMapper.updateById(partitionFireHydrant);
        }
        return partitionFireHydrant;
    }


    @Override
    public PageData<PartitionFireHydrant> getList(PartitionMountRequest request) {
        IPage<PartitionFireHydrant> page = new Page<>(request.getPage(), request.getSize());
        IPage<PartitionFireHydrant> result = partitionFireHydrantMapper.getList(page, request);
        return new PageData<>(result.getTotal(), result.getRecords());
    }

    @Override
    public void delete(List<String> ids) {
        partitionFireHydrantMapper.deleteBatchIds(ids);
    }

}
