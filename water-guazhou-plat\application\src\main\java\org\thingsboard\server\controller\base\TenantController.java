/**
 * Copyright © 2016-2019 The Thingsboard Authors
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.controller.base;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.codehaus.jackson.map.ObjectMapper;
import org.codehaus.jackson.type.TypeReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.repository.query.Param;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.thingsboard.rule.engine.api.MailService;
import org.thingsboard.server.common.data.*;
import org.thingsboard.server.common.data.VO.*;
import org.thingsboard.server.common.data.constantsAttribute.PropAttribute;
import org.thingsboard.server.common.data.exception.ThingsboardErrorCode;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.CustomerId;
import org.thingsboard.server.common.data.id.DeviceId;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.id.UserId;
import org.thingsboard.server.common.data.kv.*;
import org.thingsboard.server.common.data.page.TextPageData;
import org.thingsboard.server.common.data.page.TextPageLink;
import org.thingsboard.server.common.data.security.Authority;
import org.thingsboard.server.common.data.telemetryAttribute.RequestTs;
import org.thingsboard.server.common.data.telemetryAttribute.ResponseTs;
import org.thingsboard.server.common.data.tsdb.DataPoint;
import org.thingsboard.server.common.data.utils.AttributeConstants;
import org.thingsboard.server.dao.attributes.AttributesService;
import org.thingsboard.server.dao.influx.InfluxService;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.model.sql.ProjectEntity;
import org.thingsboard.server.dao.tenant.TenantService;
import org.thingsboard.server.dao.util.mapping.JacksonUtil;
import org.thingsboard.server.service.aspect.annotation.SysLog;
import org.thingsboard.server.service.install.InstallScripts;
import org.thingsboard.server.dao.organization.OrganizationService;

import java.io.File;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

import static org.thingsboard.server.service.rest.RestUtil.doResetPost;

@RestController
@RequestMapping("/api")
@Slf4j
public class TenantController extends BaseController {

    @Autowired
    private InstallScripts installScripts;

    @Autowired
    private TenantService tenantService;

    @Autowired
    private AttributesService attributesService;

    @Autowired
    private MailService mailService;

    @Autowired
    private InfluxService influxService;

    @Autowired
    private BCryptPasswordEncoder passwordEncoder;

    @Autowired
    private OrganizationService organizationService;


    @Value("${install.image_dir}")
    private String imageDir;
    @Value("${hostname.image}")
    private String ip;
    @Value("${defaultLogo}")
    private String defaultLogo;

    @Value("${influx.ip}")
    private String HITSDB_IP;

    @PreAuthorize("hasAnyAuthority('SYS_ADMIN',  'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS', 'CUSTOMER_USER')")
    @RequestMapping(value = "/tenant/{tenantId}", method = RequestMethod.GET)
    @SysLog(detail = DataConstants.OPERATING_TYPE_TENANT_GET)
    @ResponseBody
    public Tenant getTenantById(@PathVariable("tenantId") String strTenantId) throws ThingsboardException {
        checkParameter("tenantId", strTenantId);
        try {
            TenantId tenantId = new TenantId(toUUID(strTenantId));
            return tenantService.findTenantById(tenantId);
        } catch (Exception e) {
            throw handleException(e);
        }
    }

    @PreAuthorize("hasAnyAuthority('SYS_ADMIN',  'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS', 'CUSTOMER_USER')")
    @RequestMapping(value = "/tenant", method = RequestMethod.POST)
    @SysLog(options = DataConstants.OPERATING_TYPE.OPERATING_TYPE_OPTIONS_TENANT_SAVE)
    @ResponseBody
    @Transactional
    public Tenant createOrSaveTenant(@RequestBody Tenant tenant) throws ThingsboardException {
        try {
            boolean newTenant = tenant.getId() == null;
            tenant = checkNotNull(tenantService.saveTenant(tenant));
            if (newTenant) {
                installScripts.createDefaultRuleChains(tenant.getId());
//                deviceService.saveDevice(new Device());
//                createDefaultGateway(tenant.getId());
                // 新增默认应用和默认菜单
                tenantService.createDefaultApplicationAndMenu(tenant.getId());
                // 添加供水单位
                organizationService.initRootOrganization(tenant.getName(), tenant.getName(), tenant.getPhone(), UUIDConverter.fromTimeUUID(tenant.getId().getId()));
            }
            return tenant;
        } catch (Exception e) {
            throw handleException(e);
        }
    }

    @PreAuthorize("hasAnyAuthority('SYS_ADMIN',  'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS', 'CUSTOMER_USER')")
    @RequestMapping(value = "/tenant/edit", method = RequestMethod.POST)
    @SysLog(options = DataConstants.OPERATING_TYPE.OPERATING_TYPE_OPTIONS_TENANT_SAVE)
    @ResponseBody
    public Tenant saveTenant(@RequestBody Tenant tenant) throws ThingsboardException {
        try {
            // 保存tenant
            tenant = checkNotNull(tenantService.saveTenant(tenant));
            return tenant;
        } catch (Exception e) {
            throw handleException(e);
        }
    }

    @PreAuthorize("hasAnyAuthority('SYS_ADMIN',  'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS', 'CUSTOMER_USER')")
    @RequestMapping(value = "/tenant/{tenantId}", method = RequestMethod.DELETE)
    @SysLog(options = DataConstants.OPERATING_TYPE.OPERATING_TYPE_OPTIONS_TENANT_DELETE)
    @ResponseStatus(value = HttpStatus.OK)
    public void deleteTenant(@PathVariable("tenantId") String strTenantId) throws ThingsboardException {
        checkParameter("tenantId", strTenantId);
        try {
            TenantId tenantId = new TenantId(toUUID(strTenantId));
            tenantService.deleteTenant(tenantId);
            // 移除供水单位
            organizationService.removeRootOrganization(UUIDConverter.fromTimeUUID(getTenantId().getId()));
        } catch (Exception e) {
            throw handleException(e);
        }
    }

    @PreAuthorize("hasAnyAuthority('SYS_ADMIN',  'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS', 'CUSTOMER_USER')")
    @RequestMapping(value = "/tenants", params = {"limit"}, method = RequestMethod.GET)
    @SysLog(detail = DataConstants.OPERATING_TYPE_TENANT_GET)
    @ResponseBody
    public TextPageData<Tenant> getTenants(@RequestParam int limit,
                                           @RequestParam(required = false) String textSearch,
                                           @RequestParam(required = false) String idOffset,
                                           @RequestParam(required = false) String textOffset) throws ThingsboardException {
        try {
            TextPageLink pageLink = createPageLink(limit, textSearch, idOffset, textOffset);
            return checkNotNull(tenantService.findTenants(pageLink));
        } catch (Exception e) {
            throw handleException(e);
        }
    }

    @PreAuthorize("hasAnyAuthority('SYS_ADMIN',  'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS', 'CUSTOMER_USER')")
    @RequestMapping(value = "/tenant/attribute", method = RequestMethod.POST)
    @SysLog(detail = DataConstants.OPERATING_TYPE_TENANT_ATTRIBUTE)
    public List<AttributeKvEntry> setTenantAttribute(@RequestBody HashMap<String, String> map) throws ThingsboardException {
        List<AttributeKvEntry> attributeKvEntrys = new ArrayList<>();
        map.keySet().forEach(key -> attributeKvEntrys.add(new BaseAttributeKvEntry(new StringDataEntry(key, map.get(key)), System.currentTimeMillis())));
        attributesService.save(getTenantId(), getTenantId(), DataConstants.SERVER_SCOPE, attributeKvEntrys);
        return attributeKvEntrys;
    }

    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS')")
    @PostMapping(value = "/tenant/uploadImage")
    @SysLog(options = DataConstants.OPERATING_TYPE.OPERATING_TYPE_OPTIONS_TENANT_UPLOAD_LOGO)
    public String uploadImage(@RequestParam("file") MultipartFile file) throws ThingsboardException {
        if (file == null || file.isEmpty()) {
            throw new ThingsboardException("请选择图片进行上传!", ThingsboardErrorCode.INVALID_ARGUMENTS);
        }
        if (StringUtils.isEmpty(imageDir)) {
            imageDir = "/usr/local/thingsboard/image/";
        }
        if (Paths.get(imageDir).toFile().exists()) {
            if (!Paths.get(imageDir).toFile().isDirectory()) {
                throw new RuntimeException("thingsboard.yml中，install.image_dir未能正确配置！");
            }
        } else {// 不存在，新建这个目录
            Paths.get(imageDir).toFile().mkdir();
        }
        String originalFilename = file.getOriginalFilename();
        String suffix = originalFilename.substring(originalFilename.indexOf(".") + 1);
        // 校验文件格式 BMP、JPG、JPEG、PNG
        if (!(suffix.equalsIgnoreCase("BMP") || suffix.equalsIgnoreCase("JPG")
                || suffix.equalsIgnoreCase("JPEG") || suffix.equalsIgnoreCase("PNG"))) {
            throw new ThingsboardException("上传的图片格式不正确，仅支持JPG/JPEG/BMP/PNG四种格式的图片!", ThingsboardErrorCode.INVALID_ARGUMENTS);
        }
        Path filePath = null;
        try {
            // 获取当前登陆的租户
            Tenant tenant = checkNotNull(tenantService.findTenantById(getTenantId()));
            // 上传图片
            String fileName = tenant.getId().getId().toString() + System.currentTimeMillis() + "." + suffix;
            String fileUrl = imageDir + fileName;
            filePath = Paths.get(fileUrl);
            byte[] bytes = file.getBytes();
            Files.write(filePath, bytes);
            // 生成URL
            String logoUrl = ip + fileName;
            // 保存tenant
            JSONObject object = JSONObject.parseObject(tenant.getAdditionalInfo() == null ? "" : tenant.getAdditionalInfo().asText());
            if (object == null) {
                object = new JSONObject();
            }
            // 判断是否已有logo图片
            if (!object.containsKey("logoUrl")) {// 为第一次添加图片,保存logoUrl
                object.put("logoUrl", logoUrl);
                tenant.setAdditionalInfo(JacksonUtil.toJsonNode(object));
                tenantService.saveTenant(tenant);
            } else {
                File[] files = Paths.get(imageDir).toFile().listFiles();
                // 遍历files,删除该租户下原本的logo
                if (files == null) {
                    files = new File[0];
                }
                for (File f : files) {
                    if (f.isFile()) {
                        String name = f.getName();
                        if (name.contains(tenant.getId().getId().toString()) && !name.contains(fileName)) {
                            f.delete();
                        }
                    }
                }
            }
            return logoUrl;
        } catch (Exception e) {
            // 删除已保存的图片
            if (filePath != null) {
                filePath.toFile().delete();
            }
            e.printStackTrace();
            throw new RuntimeException("系统错误, 上传失败");
        }
    }

    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS','CUSTOMER_USER')")
    @GetMapping("/tenant/getLogo")
    public String getLoginUrl() throws ThingsboardException {
        TenantId tenantId = getTenantId();
        Tenant tenant = tenantService.findTenantById(tenantId);
        if (tenant != null && !tenant.getAdditionalInfo().isNull()) {
            String logoUrl = String.valueOf(JacksonUtil.fromString(tenant.getAdditionalInfo().toString(), Map.class).get(DataConstants.LOGO_URL));
            if (StringUtils.isNotBlank(logoUrl)) {
                return logoUrl;
            }
        }
        return defaultLogo;
    }

    /**
     * 获取所有企业的项目和主机信息
     *
     * @return
     * @throws ThingsboardException
     */
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS','CUSTOMER_USER')")
    @GetMapping("/tenant/all/info")
    public Object allInfo() throws ThingsboardException {
        return tenantService.getAllInfo();
    }


    /**
     * 新建tenant的时候，创建默认网关
     *
     * @param tenantId
     */
    private void createDefaultGateway(TenantId tenantId) {
        Device device = new Device();
        device.setCustomerId(new CustomerId(UUIDConverter.fromString(DataConstants.DEFAULT_CUSTOMER_USER_ID)));
        device.setTenantId(tenantId);
        device.setType(DataConstants.GATEWAY_NAME);
        device.setName(DataConstants.DEFAULT_GATEWAY);
        Map<String, Object> map = new HashMap<>();
        map.put("gateway", true);
        device.setAdditionalInfo(JacksonUtil.toJsonNode(map));

        deviceService.saveDevice(device);
    }

    /**
     * 设置企业短信APPkey
     *
     * @param tenantSmsKey
     * @return
     * @throws ThingsboardException
     */
    @PostMapping(value = "/setSmsKey")
    public TenantSmsKey setSmsKey(@RequestBody TenantSmsKey tenantSmsKey) throws ThingsboardException {
        AttributeKvEntry attributeKvEntry = new AttributeKeyKvEntry(new AttributeBaseKey(EntityType.TENANT, UUIDConverter.fromTimeUUID(getTenantId().getId()), DataConstants.SHARED_SCOPE, DataConstants.ALARM_SEND_SMS), JacksonUtil.toString(tenantSmsKey));
        attributesService.save(getTenantId(), DataConstants.SHARED_SCOPE, attributeKvEntry);
        return tenantSmsKey;
    }

    /**
     * 获取企业短信验证码APPkey
     *
     * @return
     * @throws ThingsboardException
     */
    @GetMapping(value = "/getSmsKey")
    public TenantSmsKey getSmsKey() throws ThingsboardException {
        TenantSmsKey tenantSmsKey = new TenantSmsKey();
        AttributeKvEntry attributeKvEntry = attributesService.findNotFuture(getTenantId(), DataConstants.SHARED_SCOPE, DataConstants.ALARM_SEND_SMS);
        if (attributeKvEntry != null) {
            JSONObject jsonObject = JSONObject.parseObject(attributeKvEntry.getValueAsString());
            tenantSmsKey = TenantSmsKey.builder().smsAppKey(jsonObject.getString("smsAppKey"))
                    .smsDeviceKey(jsonObject.getString("smsDeviceKey"))
                    .smsModelKey(jsonObject.getString("smsModelKey"))
                    .captchaKey(jsonObject.getString("captchaKey"))
                    .build();
        }
        return tenantSmsKey;
    }

    /**
     * 测试企业短信AppKey
     *
     * @param phone
     * @return
     * @throws ThingsboardException
     */
    @GetMapping(value = "/testSms")
    public boolean testSmsKey(@RequestParam("phone") String phone) throws ThingsboardException {
        String body = "测试设备，发送测试短信！";
        AlarmLinkedUser alarmLinkedUser = AlarmLinkedUser.builder()
                .userName(getCurrentUser().getName())
                .email(getCurrentUser().getEmail())
                .phone(phone)
                .tenantId(getTenantId())
                .build();
        return mailService.sendToUser(alarmLinkedUser, body, getSmsKey());
    }


    /**
     * @return 获取所有企业的主机，从机，报警数量，并根据报警数量进行排序
     */
    @GetMapping(value = "/tenant/getDeviceDataByRoot")
    public Object getDeviceDataByRoot() {
        List<Tenant> tenants = tenantService.findAll();
        List<TenantDeviceDataVO> tenantDeviceDataVOS = new ArrayList<>();
        tenants.forEach(tenant -> tenantDeviceDataVOS.add(TenantDeviceDataVO.builder().tenantId(UUIDConverter.fromTimeUUID(tenant.getUuidId()))
                .tenantName(tenant.getName())
                .hostNumbers(deviceService.findAllGatewayByTenantId(tenant.getTenantId()))
                .deviceNumbers(deviceService.findAllDeviceByTenantId(tenant.getTenantId()))
                .alarmNumbers(alarmService.getAllAlarmByTenant(tenant.getTenantId()))
                .build()));

        return tenantDeviceDataVOS.stream().sorted((o1, o2) -> o2.getAlarmNumbers().compareTo(o1.getAlarmNumbers())).collect(Collectors.toList());
    }

    /**
     * @return 获取企业下所有项目的主机，从机，报警数量，并根据报警数量进行排序
     */
    @GetMapping(value = "/tenant/getDeviceDataByTenant")
    public Object getDeviceDataByTenant(@RequestParam("tenantId") String tenantId) {
        List<ProjectEntity> projectEntities = projectService.findByTenantId(new TenantId(UUIDConverter.fromString(tenantId)));
        List<ProjectDeviceDataVO> deviceDataVOS = new ArrayList<>();
        projectEntities.forEach(projectEntity -> deviceDataVOS.add(ProjectDeviceDataVO.builder().projectId(projectEntity.getId())
                .projectName(projectEntity.getName())
                .hostNumbers((long) deviceService.findAllGatewayByProjectId(projectEntity.getId()).size())
                .deviceNumbers(deviceService.findAllDeviceByProjectId(projectEntity.getId()))
                .alarmNumbers(alarmService.getAllAlarmByProject(projectEntity.getId()))
                .build()));

        return deviceDataVOS.stream().sorted((o1, o2) -> o2.getAlarmNumbers().compareTo(o1.getAlarmNumbers())).collect(Collectors.toList());
    }

    /**
     * @return 获取企业下所有项目的主机，从机，报警数量，并根据报警数量进行排序
     */
    @GetMapping(value = "/tenant/getAlarmNumberByStatus")
    public Object getAlarmNumberByStatus(@RequestParam Long startTime, @RequestParam Long endTime) throws ThingsboardException {
        List<ProjectEntity> projectEntities = projectService.findByTenantId(getTenantId());
        List<ProjectDeviceDataVO> deviceDataVOS = new ArrayList<>();
        projectEntities.forEach(projectEntity -> deviceDataVOS.add(ProjectDeviceDataVO.builder().projectId(projectEntity.getId())
                .projectName(projectEntity.getName())
                .alarmNumbers(alarmService.getAllAlarmByProjectAndTime(projectEntity.getId(), startTime, endTime))
                .build()));

        return deviceDataVOS.stream().sorted((o1, o2) -> o2.getAlarmNumbers().compareTo(o1.getAlarmNumbers())).collect(Collectors.toList());
    }

    /**
     * @return 获取企业的主机，从机，报警数量，并根据报警数量进行排序
     */
    @GetMapping(value = "/tenant/getDeviceDataByTenantAndTime")
    public Object getDeviceDataByTenant(@RequestParam long startTime, @RequestParam long endTime) throws ThingsboardException {
        Tenant tenant = tenantService.findTenantById(getTenantId());
        TenantDeviceDataVO build = TenantDeviceDataVO.builder().tenantId(UUIDConverter.fromTimeUUID(getTenantId().getId()))
                .tenantName(tenant.getName())
                .alarmNumbers(alarmService.getAllAlarmByTenantAndTime(tenant.getTenantId(), startTime, endTime))
                .build();
        return build;
    }


    /**
     * 平台下所有项目散点
     *
     * @return
     */
    @GetMapping(value = "/root/projectsAddress")
    public Object getAllProjectAddress() {
        List<ProjectEntity> projectEntities = projectService.findAllProject();
        List<ProjectAddress> projectAddresses = new ArrayList<>();
        projectEntities.forEach(projectEntity -> {
            JSONObject jsonObject = JSONObject.parseObject(projectEntity.getAdditionalInfo());
            JSONArray localtion = jsonObject.getJSONArray("location");
            projectAddresses.add(ProjectAddress.builder()
                    .lgtd(localtion.getString(0))
                    .latd(localtion.getString(1))
                    .value("100")
                    .build());
        });

        return projectAddresses;
    }

    /**
     * 企业下所有设备散点
     *
     * @return
     */
    @SneakyThrows
    @GetMapping(value = "/tenant/devicePoint")
    public Object getTenantDevicePoint() {
        List<Device> devices = deviceService.findGateWayByTenantId(getTenantId());
        Tenant tenant = tenantService.findTenantById(getTenantId());
        List<ProjectAddress> projectAddresses = new ArrayList<>();
        devices.forEach(projectEntity -> {
            JSONObject jsonObject = JSONObject.parseObject(projectEntity.getAdditionalInfo().asText());
            JSONArray localtion = jsonObject.getJSONArray("location");
            projectAddresses.add(ProjectAddress.builder()
                    .lgtd(localtion.getString(0))
                    .latd(localtion.getString(1))
                    .value("100")
                    .build());
        });
        projectAddresses.add(ProjectAddress.builder()
                .lgtd(tenant.getLgtd())
                .latd(tenant.getLatd())
                .value("120")
                .build());
        return projectAddresses;
    }

    @GetMapping("/tenant/getCurrentTenantList")
    public List<Tenant> getCurrentTenantList() throws ThingsboardException {
        Authority authority = getCurrentUser().getAuthority();
        if (Authority.SYS_ADMIN.equals(authority)) {
            return tenantService.findAll();
        }
        TenantId tenantId = null;
        if (!(Authority.TENANT_PROMOTE.equals(authority) || Authority.TENANT_SUPPORT.equals(authority))) {
            tenantId = getTenantId();
        }
        UserId userId = getCurrentUser().getId();

        return tenantService.getCurrentTenantList(authority, tenantId, userId);
    }

    /**
     * 企业下设备与企业的飞线
     *
     * @return
     */
    @SneakyThrows
    @GetMapping(value = "/tenant/DeviceAddress")
    public Object getAllDeviceAddress() {
        List<Device> devices = deviceService.findGateWayByTenantId(getTenantId());
        Tenant tenant = tenantService.findTenantById(getTenantId());
        List<DeviceAddress> deviceAddresses = new ArrayList<>();
        devices.forEach(projectEntity -> {
            try {
                JSONObject jsonObject = JSONObject.parseObject(projectEntity.getAdditionalInfo().asText());
                JSONArray localtion = jsonObject.getJSONArray("location");
                deviceAddresses.add(DeviceAddress.builder()
                        .from(tenant.getLgtd() + "," + tenant.getLatd())
                        .to(localtion.getString(0) + "," + localtion.getString(1))
                        .build());
            } catch (Exception e) {
                e.printStackTrace();
            }

        });
        return deviceAddresses;
    }

    @GetMapping("tenant/otherTenantList/{userId}")
    public JSONObject getOtherTenantList(@PathVariable String userId) {
        return tenantService.getOtherTenantList(userId);
    }

    @PostMapping("tenant/setTenantToUser")
    public void setTenantToUser(@RequestBody JSONObject params) throws ThingsboardException {
        if (params.containsKey("fromUserId") && params.containsKey("toUserId") && params.containsKey("tenantIdList")) {
            tenantService.setTenantToUser(params.getString("toUserId"), params.getJSONArray("tenantIdList").toJavaList(String.class));
        }

        throw new ThingsboardException(ThingsboardErrorCode.BAD_REQUEST_PARAMS);
    }

    /**
     * 平台下主机在线率
     *
     * @return
     */
    @SneakyThrows
    @GetMapping(value = "/root/OnlineRate")
    public Object getOnlineRate() {
        long allHost = deviceService.findAllGateway().size();
        long online = deviceService.findAllOnlineGateway();
        JSONObject jsonObject = new JSONObject();
        if (online == 0) {
            jsonObject.put(DataConstants.VALUE, new BigDecimal("0"));
        } else {
            jsonObject.put(DataConstants.VALUE, new BigDecimal(online).divide(new BigDecimal(allHost), 4, BigDecimal.ROUND_HALF_DOWN));
        }
        return Collections.singletonList(jsonObject);
    }

    /**
     * 平台下数据转移
     *
     * @return
     */
    @SneakyThrows
    @GetMapping(value = "/root/transferDeviceData")
    public Object transferDeviceData2Influxdb(@Param("deviceId") String deviceId) {
        if (deviceId != null) {
            Device device = deviceService.findDeviceById(new DeviceId(UUIDConverter.fromString(deviceId)));
            AttributeKvEntry attributeKvEntry = attributesService.findNotFuture(device.getId(), DataConstants.SHARED_SCOPE, ModelConstants.PROR);
            if (attributeKvEntry != null) {
                List<PropAttribute> props = new ObjectMapper().readValue(attributeKvEntry.getValueAsString(), new TypeReference<List<PropAttribute>>() {
                });
                props.forEach(propAttribute -> {
                    transFerData(device, propAttribute);
                });
            }
        } else {
            List<Tenant> tenants = tenantService.findAll();
            tenants.forEach(tenant -> {
                List<Device> devices = deviceService.findAllByTenantId(tenant.getTenantId());
                devices.forEach(device -> {
                    try {
                        AttributeKvEntry attributeKvEntry = attributesService.findNotFuture(device.getId(), DataConstants.SHARED_SCOPE, ModelConstants.PROR);
                        if (attributeKvEntry != null) {
                            List<PropAttribute> props = new ObjectMapper().readValue(attributeKvEntry.getValueAsString(), new TypeReference<List<PropAttribute>>() {
                            });
                            props.forEach(propAttribute -> {
                                transFerData(device, propAttribute);
                            });
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                });
            });
        }
        return "true";
    }


    private void transFerData(Device device, PropAttribute propAttribute) {
        long start = device.getCreatedTime();
        long end = System.currentTimeMillis();
        LinkedHashMap<String, Object> query = new LinkedHashMap<>();
        ArrayList<HashMap<String, Object>> queries = new ArrayList<>();
        query.put(AttributeConstants.TSDB_AGGREGATOR, AttributeConstants.TSDB_AGGREGATOR_TYPE.none);
        query.put(AttributeConstants.TSDB_METRIC, UUIDConverter.fromTimeUUID(device.getTenantId().getId()) + "." + UUIDConverter.fromTimeUUID(device.getUuidId()));
        LinkedHashMap<String, String> tags = new LinkedHashMap<>();
        tags.put(AttributeConstants.TSDB_PROPERTY, propAttribute.getPropertyCategory());
        query.put("tags", tags);
        queries.add(query);
        if (queries.isEmpty()) {
            return;
        }
        String putUrl = HITSDB_IP + ":" + DataConstants.HITSDB_PORT + AttributeConstants.TSDB_API_QUERY;
        try {
            List<ResponseTs> result = doResetPost(putUrl, new RequestTs(start, end, queries));
            if (result != null && result.size() > 0 && result.get(0).getDps() != null) {
                List<DataPoint> dataPoints = new ArrayList<>();
                result.get(0).getDps().forEach((key, value) -> dataPoints.add(new DataPoint(UUIDConverter.fromTimeUUID(device.getUuidId()), Long.parseLong(key) * 1000, value, propAttribute.getPropertyCategory())));
                int a = 0;
                List<DataPoint> re = new ArrayList<>();
                for (int i = 0; i < dataPoints.size(); i++) {
                    if (a <= 1000 && i != dataPoints.size() - 1) {
                        re.add(dataPoints.get(i));
                        a++;
                    } else if (i == dataPoints.size() - 1) {
                        re.add(dataPoints.get(i));
                        influxService.saveDeviceToInflux(re);
                        re.clear();
                        a = 0;
                    } else {
                        influxService.saveDeviceToInflux(re);
                        re.clear();
                        a = 0;
                    }
                }
                log.info(device.getName() + "的属性" + propAttribute.getPropertyCategory() + "数据转移完毕");
            }
        } catch (ThingsboardException e) {
            e.printStackTrace();
        }

    }


}
