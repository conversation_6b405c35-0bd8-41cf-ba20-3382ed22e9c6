import{j as a}from"./index-r0dFAfgr.js";function l(e,t,i){return{name:"",grid:{left:t||80,right:i||80,top:80,bottom:50},legend:{type:"scroll",width:600,top:10,textStyle:{fontSize:12}},tooltip:{trigger:"axis"},xAxis:{type:"category",boundaryGap:!1,data:e},yAxis:[{position:"left",type:"value",name:"",axisLine:{show:!0,lineStyle:{types:"solid"}},axisLabel:{show:!0,textStyle:{}},splitLine:{lineStyle:{type:[5,10],dashOffset:5}}},{position:"right",type:"value",name:"",axisLine:{show:!0,lineStyle:{types:"solid"}},axisLabel:{show:!0,textStyle:{}},splitLine:{lineStyle:{type:[5,10],dashOffset:5}}}],series:[{}]}}const n=(e,t)=>({color:["#ff8516","#1d8932","#de2d43"],legend:{type:"scroll",orient:"vertical",top:"center",right:"right",width:200,formatter:"{name}{value}",textStyle:{color:"#666",fontSize:12}},title:{text:e,bottom:0,left:"center",textStyle:{color:"rgb(137, 168, 195)",fontSize:13}},graphic:{elements:[{type:"image",style:{image:"https://yp.a-hh.cn/zhjk/img.jpg",width:25,height:30},left:"center",top:"center"}]},series:[{startAngle:-60,name:"Access From",type:"pie",radius:["62%","70%"],avoidLabelOverlap:!1,label:{show:!1,position:"center",color:"#ffffff",formatter(){return`0
m³`}},data:[{value:0,name:"报警"},{value:4,name:"正常"},{value:0,name:"异常"}]}]}),r=(e,t,i)=>({series:[{name:"gauge 0",type:"gauge",min:0,max:7,splitNumber:7,startAngle:240,endAngle:-320,radius:"66%",center:["22%","46%"],axisLine:{lineStyle:{width:6,color:[[.3,"#67e0e3"],[.6,"#37a2da"],[1,"#fd666d"]]}},pointer:{itemStyle:{color:"auto"}},axisTick:{distance:1,length:8,lineStyle:{color:"auto",width:1}},splitLine:{distance:1,length:12,lineStyle:{color:"auto",width:2}},axisLabel:{color:"auto",distance:20},title:{show:!0,offsetCenter:[0,"-30%"],color:a().isDark?"#FFFFFF":"#000000"},detail:{fontSize:20,valueAnimation:!0,formatter:"{value}",color:"auto"},data:[{value:e,name:"瞬时流量"}]},{type:"gauge",z:10,startAngle:210,endAngle:-30,radius:"90%",center:["50%","60%"],axisLine:{lineStyle:{width:8,color:[[.3,"#67e0e3"],[.6,"#37a2da"],[1,"#fd666d"]]}},pointer:{itemStyle:{color:"auto"}},axisTick:{distance:1,length:10,lineStyle:{color:"auto",width:1}},splitLine:{distance:1,length:16,lineStyle:{color:"auto",width:2}},axisLabel:{color:"auto",distance:20,fontSize:13},title:{show:!0,offsetCenter:[0,"-30%"],textStyle:{fontWeight:"bolder",fontSize:"14",color:a().isDark?"#FFFFFF":"#000000"}},detail:{fontSize:20,valueAnimation:!0,formatter:"{value}",color:"auto"},data:[{value:t,name:"累计流量"}]},{name:"gauge 4",type:"gauge",min:0,max:7,splitNumber:7,startAngle:150,endAngle:-60,radius:"66%",center:["80%","46%"],axisLine:{lineStyle:{width:6,color:[[.3,"#67e0e3"],[.6,"#37a2da"],[1,"#fd666d"]]}},pointer:{itemStyle:{color:"auto"}},axisTick:{distance:1,length:8,lineStyle:{color:"auto",width:1}},splitLine:{distance:1,length:12,lineStyle:{color:"auto",width:2}},axisLabel:{color:"auto",distance:20,fontSize:12},title:{show:!0,offsetCenter:[0,"-30%"],color:a().isDark?"#FFFFFF":"#000000"},detail:{fontSize:20,valueAnimation:!0,formatter:"{value}",color:"auto"},data:[{value:i||0,name:"压力"}]}]});export{r as g,l,n as p};
