package org.thingsboard.server.dao.util.imodel.query.device;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.deviceManage.DeviceType;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;

@Getter
@Setter
public class DeviceTypePageRequest extends AdvancedPageableQueryEntity<DeviceType, DeviceTypePageRequest> {
    // 类别名称
    private String name;

    // 创建人
    private String creator;

    // 序列号
    private String serialId;
}
