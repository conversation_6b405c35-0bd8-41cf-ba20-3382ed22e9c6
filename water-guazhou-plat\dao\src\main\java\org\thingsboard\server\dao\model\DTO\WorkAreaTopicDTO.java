package org.thingsboard.server.dao.model.DTO;

import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 工单区域-业务主题
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-12-21
 */
@Data
public class WorkAreaTopicDTO {
    private String topicName;

    private List<WorkAreaTopicAreaDTO> topicAreaList;

    private Map<String, WorkAreaTopicAreaDTO> topicAreaMap = new HashMap<>();
}
