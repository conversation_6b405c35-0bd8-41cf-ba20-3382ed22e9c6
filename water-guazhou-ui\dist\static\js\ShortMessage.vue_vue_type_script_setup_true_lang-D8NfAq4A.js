import{d as M,c as l,r as m,a8 as f,aD as D,b as o,aE as I,o as F,aF as T,g as x,n as A,q as g,i as y,aB as w,_ as q}from"./index-r0dFAfgr.js";import{v as k}from"./formValidate-U0WTqY4Y.js";const c=60,P=M({__name:"ShortMessage",setup(E){const u=l(),p=l(),v=(e,s,t)=>{s.trim()!==""?t():t(new Error("请输入有效字符 空格无效"))},n=(e,s,t)=>{if(/[\u4E00-\u9FA5]/g.test(s))t(new Error("不能输入汉字"));else return t()},d=m({group:[{fields:[{type:"select",field:"service",label:"服务提供商",placeholder:"请选择短信服务提供商",options:[{label:"云片网",value:"ypw"}],rules:[{required:!0,message:"请选择短信服务提供商",trigger:"change"}]},{type:"input",field:"smsDeviceKey",label:"设备模板ID",rules:[{required:!0,message:"请输入设备模板ID",trigger:"blur"},{validator:n,trigger:"blur"}]},{type:"input",label:"触发模板ID",field:"smsModelKey",rules:[{required:!0,message:"请输入触发模板ID",trigger:"blur"},{validator:n,trigger:"blur"}]},{type:"input",label:"验证模板ID",field:"captchaKey",rules:[{required:!0,message:"请输入验证模板ID",trigger:"blur"},{validator:n,trigger:"blur"}]},{type:"password",label:"API key",field:"smsAppKey",autocomplete:!1,placeholder:"请输入API key",rules:[{required:!0,message:"请输入apiKey",trigger:"blur"},{validator:v,trigger:"blur"}]}]}],labelPosition:"right",labelWidth:"140px",defaultValue:{service:"ypw",smsAppKey:"dc2b77c6e1f351ab1bef55991642ff16",smsModelKey:"3716214",smsDeviceKey:"3715352",captchaKey:"3770334",sendMes:""},submit(e){_(e)}}),K=m({group:[{fields:[{type:"input",label:"发送短信测试至",field:"sendMes",inputType:"text",rules:[{required:!0,message:"请输入手机号"},{validator:k}]},{type:"btn-group",btns:[{perm:!0,type:"warning",isTextBtn:!1,text:f(()=>r.value?`${a.value} 秒后重新发送`:"发送测试短信"),iconifyIcon:f(()=>r.value?"ep:loading":"ep:promotion"),click:()=>{var e;r.value||(e=p.value)==null||e.Submit()}},{perm:!0,text:"保存",click:()=>{var e;(e=u.value)==null||e.Submit()}}]}]}],labelPosition:"right",labelWidth:"140px",defaultValue:{},submit:e=>{b(e)}}),i=l(null),a=l(c),r=l(!1),b=e=>{i.value||(h(e.sendMes),a.value=c,r.value=!1,i.value=setInterval(()=>{a.value>0&&a.value<=c?a.value--:(r.value=!0,clearInterval(i.value),i.value=null)},1e3))},h=e=>{D(e).then(s=>{s.data?o.success("发送成功"):o.warning("发送短信失败，请检查配置信息是否正确")}).catch(s=>{console.log(s),o.error("发送失败，请检查输入的电话号码是否存在！")})},_=async e=>{const s={smsAppKey:e.smsAppKey,smsDeviceKey:e.smsDeviceKey,smsModelKey:e.smsModelKey,captchaKey:e.captchaKey};I(s).then(t=>{t.data.captchaKey&&o.success("保存成功")})};return F(()=>{T().then(e=>{var s;d.defaultValue={smsAppKey:e.data.smsAppKey,smsModelKey:e.data.smsModelKey,smsDeviceKey:e.data.smsDeviceKey,captchaKey:e.data.captchaKey},(s=u.value)==null||s.resetForm()})}),(e,s)=>{const t=q;return x(),A(w,null,[g(t,{ref_key:"refForm",ref:u,config:y(d)},null,8,["config"]),g(t,{ref_key:"refTestForm",ref:p,config:y(K)},null,8,["config"])],64)}}});export{P as _};
