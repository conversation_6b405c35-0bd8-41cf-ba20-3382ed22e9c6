import { PipeStatistics } from '@/api/mapservice/pipe';
import { useGisStore } from '@/store';

/**
 * 管网统计类型
 */
export enum EStatisticType {
  COUNT = '1',
  LENGTH = '2'
}
/**
 * 管网统计分组
 */
export enum EStatisticGroup {
  DIAMETER = 'DIAMETER',
  MATERIAL = 'MATERIAL'
}
/**
 * 管网统计字段
 */
export enum EStatisticField {
  OBJECTID = 'OBJECTID',
  // ShapeLen = 'SHAPE.Len'
  ShapeLen = 'PIPELENGTH'
}
/**
 * 统计管线
 * @param type 统计类型
 * @param params 参数
 * @returns 返回统计结果数组
 */
export const staticPipe = async (
  type: 'count' | 'length',
  params: {
    group_fields?: string[];
    where?: string;
    geometry?: __esri.Geometry;
    layerIds?: any[];
  }
): Promise<any[]> => {
  // 查询数量
  const res = await PipeStatistics({
    usertoken: useGisStore().gToken,
    layerids: JSON.stringify(params.layerIds || []),
    group_fields: JSON.stringify(params.group_fields || []),
    statistic_field:
      type === 'count' ? EStatisticField.OBJECTID : EStatisticField.ShapeLen,
    statistic_type:
      type === 'count' ? EStatisticType.COUNT : EStatisticType.LENGTH,
    where: params.where || '1=1',
    geometry: params.geometry,
    f: 'pjson'
  });
  return res.data?.result?.rows || [];
};

export const statistic = async (statisticsParams: {
  layerIds: any[];
  group_fields: string[];
  statistic_field: EStatisticField;
  /** 1:数量; 2：长度 */
  statistic_type: EStatisticType;
  where?: string;
  geometry?: any;
}) => {
  const res = await PipeStatistics({
    ...statisticsParams,
    layerids: JSON.stringify(statisticsParams.layerIds),
    group_fields: JSON.stringify(statisticsParams.group_fields || [])
  });
  if (res.data.code === 10000) {
    const data = res.data?.result?.rows[0]?.rows || [];
    return (
      data.map((item) => {
        return {
          value: item[statisticsParams.statistic_field],
          label: item[statisticsParams.group_fields[0]]?.toString()
        };
      }) || []
    );
  }
  return [];
};
