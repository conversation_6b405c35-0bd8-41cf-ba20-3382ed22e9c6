<template>
  <el-row :gutter="5">
    <el-col
      v-for="(item, index) in newValue"
      :key="index"
      :span="props.span"
    >
      <el-card
        shadow="never"
        :body-style="{ display: 'flex', justifyContent: 'center', backgroundColor: styles[index], padding: '5px' }"
        class="cards-item"
      >
        <div class="show">
          <span class="label">{{ item.label }}</span>
          <span class="value">{{ item.value }}</span>
        </div>
      </el-card>
    </el-col>
  </el-row>
</template>

<script lang="ts" setup>
import { PropType } from 'vue'

const props = defineProps({
  modelValue: {
    type: Array as PropType<any[]>,
    default() {
      return []
    }
  },
  span: {
    type: Number,
    default() {
      return 8
    }
  }
})
const emit = defineEmits(['update:modelValue'])

const newValue = computed({
  // 子组件v-model绑定 计算属性, 一旦发生变化, 就会给父组件传递值
  get: () => props.modelValue,
  set: nv => {
    emit('update:modelValue', nv)
  }
})

const styles = ['#528ff8', '#73cb71', '#efbd47', '#528ff8']
</script>

<style lang="scss" scoped>
.cards-item {
  margin-bottom: 5px;
}
.show {
  display: flex;
  font-size: 15px;
  flex-direction: column;
  justify-content: space-around;
  align-items: center;
  .label {
    font-size: 16px;
    line-height: 25px;
  }
  .value {
    font-size: 12px;
  }
}
</style>
