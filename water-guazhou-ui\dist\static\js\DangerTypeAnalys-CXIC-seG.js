import{_ as M}from"./index-C9hz-UZb.js";import{d as w,c as f,r as _,s as D,l as n,o as x,Q as T,ay as B,g as q,n as z,q as s,F as C,p as i,aq as L,al as S,C as V}from"./index-r0dFAfgr.js";import{_ as P}from"./Search-NSrhrIa_.js";import{B as E,P as F}from"./echart-BoVIcYbV.js";import{W as H}from"./workorder-jXNat1mh.js";import{r as A}from"./reduce-BbPixnH6.js";import"./config-DqqM5K5L.js";import"./DateFormatter-Bm9a68Ax.js";import"./OutsideWorkOrder-C6s8joBt.js";import"./index-CpGhZCTT.js";const I={class:"wrapper"},N={class:"content-wrapper"},W={class:"left"},Q={class:"right"},R={class:"right-top"},U={class:"right-bottom"},j=w({__name:"DangerTypeAnalys",setup(G){const m=f(),h=f(),u=f(),O=_({filters:[{type:"radio-button",label:"时间范围",options:[{label:"日",value:"date"},{label:"月",value:"month"},{label:"年",value:"year"}],field:"type",onChange:()=>c()},{clearable:!1,handleHidden:(e,a,t)=>{t.hidden=e.type!=="date"},type:"date",label:"",field:"date"},{clearable:!1,handleHidden:(e,a,t)=>{t.hidden=e.type!=="month"},type:"month",label:"",field:"month"},{clearable:!1,handleHidden:(e,a,t)=>{t.hidden=e.type!=="year"},type:"year",label:"",field:"year"}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",svgIcon:D(S),click:()=>c()}]}],defaultParams:{type:"year",month:n().format("YYYY-MM"),year:n().format("YYYY"),date:n().format("YYYY-MM-DD")}}),l=_({indexVisible:!0,columns:[{label:"事件类型",prop:"key"},{label:"事件数量",prop:"value"},{label:"比例（%）",prop:"percentage"}],dataList:[],pagination:{hide:!0,refreshData:({page:e,size:a})=>{l.pagination.page=e,l.pagination.limit=a,c()}}}),p=_({barOpion:null,pieOption:null}),c=async()=>{var v,g,Y;const e=((v=m.value)==null?void 0:v.queryParams)||{},a=(e==null?void 0:e.type)||"date";let t;switch(a){case"month":t=n(e[a],"YYYY-MM");break;case"year":t=n(e[a],"YYYY");break;default:t=n(e[a],"YYYY-MM-DD");break}const o=((g=(await H({fromTime:t.startOf(a==="year"?"y":a==="month"?"M":"D").valueOf(),toTime:t.endOf(a==="year"?"y":a==="month"?"M":"D").valueOf()})).data)==null?void 0:g.data)||{};p.barOpion=E(o.data||[]),p.pieOption=F(o.data||[],"事件类型分析");const d=A(o.data||[],(r,k)=>r+k.value,0);l.dataList=((Y=o.data)==null?void 0:Y.map(r=>(r.percentage=((r.value||0)/(d||1)*100).toFixed(2),r)))||[]},y=()=>{var e,a;(e=h.value)==null||e.resize(),(a=u.value)==null||a.resize()};return x(()=>{c(),window.addEventListener("resize",y)}),T(()=>{window.removeEventListener("resize",y)}),(e,a)=>{const t=P,b=L,o=B("VChart"),d=M;return q(),z("div",I,[s(d,{class:"card",title:" ",overlay:""},{title:C(()=>[s(t,{ref_key:"refSearch",ref:m,config:O},null,8,["config"])]),default:C(()=>[i("div",N,[i("div",W,[s(b,{config:l},null,8,["config"])]),i("div",Q,[i("div",R,[s(o,{ref_key:"refChart",ref:h,option:p.barOpion},null,8,["option"])]),i("div",U,[s(o,{ref_key:"refChart1",ref:u,option:p.pieOption},null,8,["option"])])])])]),_:1})])}}}),re=V(j,[["__scopeId","data-v-95ef0fd3"]]);export{re as default};
