<template>
  <div class="one-map-detail">
    <div v-loading="state.detailLoading" class="left">
      <div class="flex">
        <span
          >区域总水量<text style="color: #42a0ff">{{ 0 }}</text
          >m³</span
        >
        <span
          >区域售水量<text style="color: #63c63a">{{ 0 }}</text
          >m³/h</span
        >
      </div>
      <div class="flex">
        <span
          >夜间最小流量<text>{{ 0 }}</text
          >m³/h</span
        >
        <span>
          挂接用户
          <text> {{ 0 }}</text>
          户
        </span>
        <span
          >大用户数<text>{{ 0 }}</text
          >户</span
        >
        <span
          >大用户水量占比数<text>{{ 0 }}</text
          >%</span
        >
      </div>
    </div>
    <div class="right">
      <div style="width: 100%; height: 100%">
        <VChart
          ref="refChart2"
          autoresize
          theme="dark"
          :option="option2"
        ></VChart>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { padStart } from 'lodash-es';
import { areaChart, hourlyLine } from '../../components/components/chart';
import { GetBigUserDetail } from '@/api/mapservice/onemap';
import { IECharts } from '@/plugins/echart';

const emit = defineEmits(['refresh', 'mounted']);
const { proxy }: any = getCurrentInstance();
const option2 = hourlyLine();
const refChart2 = ref<IECharts>();

const state = reactive<{
  curRow?: any;
  lineChartOption1: any;
  lineChartOption2: any;
  mapClick?: any;
  stationRealTimeData: any[];
  detailLoading: boolean;
}>({
  lineChartOption1: areaChart(
    Array.from({ length: 24 }).map((item, i) => padStart(i.toString(), 2, '0')),
    [],
    {}
  ),
  lineChartOption2: areaChart(
    Array.from({ length: 30 }).map((item, i) => padStart(i.toString(), 2, '0')),
    [],
    {}
  ),
  stationRealTimeData: [],
  detailLoading: false
});

const refreshDetail = async (row) => {
  emit('refresh', { title: row.name });
  state.detailLoading = true;
  state.curRow = row;
  Array.from({ length: 2 }).map((item, i) => {
    proxy.$refs['refChart' + i]?.resize();
  });
  GetBigUserDetail({
    stationId: row.stationId
  })
    .then((res) => {
      const todayData =
        res.data.data.todayData?.map((item) => item.value) || [];
      const todayXData = res.data.data.todayData?.map((item) => item.ts) || [];
      state.lineChartOption1 = areaChart(todayXData, todayData, {
        unit: 'm³',
        name: '日供水量',
        color1: '#ff0000'
      });

      const monthData =
        res.data.data.monthData?.map((item) => item.value) || [];
      const monthXData = res.data.data.monthData?.map((item) => item.ts) || [];
      state.lineChartOption2 = areaChart(monthData, monthXData, {
        unit: 'm³',
        name: '月供水量',
        color1: '#ff0000'
      });
    })
    .finally(() => {
      state.detailLoading = false;
    });
};
defineExpose({
  refreshDetail
});

onMounted(() => {
  emit('mounted');
  setTimeout(() => {
    window.addEventListener('resize', resizeChart);
    refChart2.value?.resize();
  }, 3000);
});

function resizeChart() {
  refChart2.value?.resize();
}
</script>
<style lang="scss" scoped>
.one-map-detail {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: row;
  .left {
    height: 100%;
    align-items: center;
    padding-right: 8px;
    display: flex;
  }
  .right {
    flex: 1;
    height: 100%;
    padding-left: 8px;
    border-left: 2px solid var(--el-border-color-lighter);
  }
}
</style>
