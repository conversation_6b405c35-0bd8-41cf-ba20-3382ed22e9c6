import{_ as P}from"./Panel-DyoxrWMd.js";import{d as R,c as y,r as v,b as x,W as z,bB as N,o as T,Q as V,ay as B,g as E,n as H,q as h,i as d,F as $,p as W,_ as U,C as X}from"./index-r0dFAfgr.js";import{m as Y}from"./FeatureHelper-Da16o0mu.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./MapView-DaoQedLH.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./Point-WxyopZva.js";import{g as j}from"./LayerHelper-Cn-iiqxI.js";import"./project-DUuzYgGl.js";import{s as D,i as q}from"./ToolHelper-BiiInOzB.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./geometryEngineBase-BhsKaODW.js";import{H as Q}from"./pipeAnalys-BoQOC96l.js";import J from"./PipeDetail-CTBPYFJW.js";import{m as K}from"./max-CCqK09y5.js";import{m as ee}from"./min-ks0CS-3r.js";import"./v4-SoommWqA.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./pipe-nogVzCHG.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./Search-NSrhrIa_.js";import"./FormTableColumnFilter-BT7pLXIC.js";import"./fieldconfig-Bk3o1wi7.js";import"./DateFormatter-Bm9a68Ax.js";import"./QueryHelper-ILO3qZqg.js";import"./config-fy91bijz.js";import"./_baseExtremum-UssVWohW.js";import"./_baseLt-svgXHEqw.js";const te={class:"chart-box"},re=R({__name:"AcrossAnalys",props:{view:{},telport:{}},setup(A){const g=y(),u=y(),b=y(),m=A,o=v({curOperate:"",tabs:[],chartOption:null}),e={vertices:[],soeResult:[],pipeInfo:{ID:[],ZGround:[],Distance:[],Depth:[],Diameter:[],Z:[],Material:[]}},_=y(),w=v({columns:[{label:"设备类型",prop:"name"},{label:"数量",prop:"count",formatter:t=>{var r;return(r=t.data)==null?void 0:r.length}}],dataList:[],pagination:{hide:!0}}),S=v({group:[{fieldset:{desc:"绘制剖面线"},fields:[{type:"btn-group",btns:[{perm:!0,type:"warning",loading:()=>o.curOperate==="analysing",disabled:()=>o.curOperate==="analysing",text:()=>o.curOperate==="picking"?"正在绘制剖面线":o.curOperate==="analysing"?"分析中":"点击绘制剖面线",click:()=>k(),styles:{width:"100%"}}]}]},{fieldset:{desc:"分析结果"},fields:[{type:"table",style:{height:"250px"},config:w},{type:"btn-group",itemContainerStyle:{marginTop:"20px"},btns:[{perm:!0,text:"详细信息",disabled:()=>o.curOperate!=="viewingDetail"&&o.curOperate!=="analysed",loading:()=>o.curOperate==="detailing",click:()=>Z(),styles:{width:"100%"}}]}]}],labelPosition:"top",gutter:12}),k=async()=>{var t,r,n,s,i,p;m.view&&((t=u.value)==null||t.Close(),D("crosshair"),o.curOperate="picking",e.vertices=[],(r=e.drawAction)==null||r.destroy(),(n=e.drawer)==null||n.destroy(),e.drawer=q(m.view),e.drawAction=(s=e.drawer)==null?void 0:s.create("polyline"),(i=e.drawAction)==null||i.on(["vertex-add","cursor-update"],I),(p=e.drawAction)==null||p.on("draw-complete",async f=>{I(f),D(""),await O()}))},I=t=>{var r,n,s;if(t.vertices.length>=2){const i=[t.vertices[0],t.vertices[t.vertices.length-1]];e.vertices=i;const p=Y(i,(r=m.view)==null?void 0:r.spatialReference);(n=e.graphicsLayer)==null||n.removeAll(),p&&((s=e.graphicsLayer)==null||s.add(p))}},O=async()=>{var t,r,n;if(!m.view){x.error("地图服务未就绪，请稍候再试");return}o.curOperate="analysing";try{const s=await Q({UserToken:z().gToken||"",X1:e.vertices[0][0],X2:e.vertices[1][0],Y1:e.vertices[0][1],Y2:e.vertices[1][1],f:"pjson"});if(e.soeResult=((t=s.data)==null?void 0:t.Values)||[],s.data.Status!=="successed"){x.error("分析失败"),o.curOperate="";return}o.tabs=[],e.pipeInfo.ID=[],e.pipeInfo.ZGround=[],e.pipeInfo.Distance=[],e.pipeInfo.Depth=[],e.pipeInfo.Diameter=[],e.pipeInfo.Z=[],e.pipeInfo.Material=[],e.soeResult.map(i=>{var l;const p=(l=i.PipeLineNameDefinition||i.PipePointNameDefinition)==null?void 0:l.split(":");let f="",a="";if(p.length===2){f=p[0],a=p[1];const c=o.tabs.find(G=>G.label===f);c?c.data.push(a):o.tabs.push({label:f,name:f,data:[a]})}e.pipeInfo.ID.push(i.ID),e.pipeInfo.ZGround.push(i.ZGround),e.pipeInfo.Distance.push(i.Distance),e.pipeInfo.Depth.push(i.Depth),e.pipeInfo.Diameter.push(i.Diameter),e.pipeInfo.Z.push(i.Z),e.pipeInfo.Material.push(i.Material)}),w.dataList=o.tabs,(r=b.value)==null||r.clear(),o.chartOption=null,(n=u.value)==null||n.Open(),o.chartOption=C(),N(()=>{var i;(i=b.value)==null||i.resize()})}catch{x.error("系统错误"),o.curOperate=""}o.curOperate="analysed"},C=()=>{const t=[],r=e.pipeInfo.ZGround.map((a,l)=>{const c=[e.pipeInfo.Distance[l],e.pipeInfo.ZGround[l]];return t.push(c),[e.pipeInfo.Distance[l],e.pipeInfo.Z[l]]}),n=K(e.pipeInfo.Diameter)||0,s=ee(e.pipeInfo.Diameter)||0,i=5,p=10;return{legend:{selectedMode:!1,textStyle:{color:"#fff"}},toolbox:{show:!0,itemSize:20,feature:{mark:{show:!0},restore:{show:!0},saveAsImage:{show:!0}}},dataZoom:{show:!0,start:0,end:100,textStyle:{color:"#fff"}},tooltip:{trigger:"axis",formatter:a=>{if(a.seriesName==="地面")return"地面点高程："+e.pipeInfo.ZGround[a.dataIndex].toFixed(2);if(a[0].seriesName==="管点")return"管径："+e.pipeInfo.Diameter[a[0].dataIndex]+"<br/>材质:"+e.pipeInfo.Material[a[0].dataIndex]+"<br/>埋深:"+Number(e.pipeInfo.Depth[a[0].dataIndex].toFixed(2))/8}},xAxis:{type:"value",show:!0,position:"bottom",name:"距离",nameLocation:"end",nameTextStyle:{},boundaryGap:[0,0],min:null,max:null,color:"#A9D2E1",axisLabel:{formatter:"{value}m",textStyle:{color:"#ffffff"}},splitLine:{show:!1,color:"#00ff00"},splitArea:{show:!1}},yAxis:{name:"高程",type:"value",scale:!0,color:"#A9D2E1",axisLabel:{formatter:"{value}m",textStyle:{color:"#ffffff"}},splitArea:{show:!1},splitLine:{lineStyle:{color:"#FFFFFF",opacity:.2,type:"dashed"}}},grid:{left:100},series:[{name:"管点",type:"scatter",tooltip:{trigger:"axis"},legendHoverLink:!0,symbol:"emptyCircle",symbolSize(a){let l;for(let c=0;c<r.length;c++)r[c][1]===a[1]&&(l=c);return n===s?i:i+(e.pipeInfo.Diameter[l]-s)/(n-s)*(p-i)},itemStyle:{normal:{color:"red",borderWidth:2,borderColor:"#070707",label:{show:!0,formatter:a=>e.pipeInfo.ID[a.dataIndex],position:"bottom",textStyle:{color:"#fff",align:"right",baseline:"bottom",fontSize:"10px"}}},emphasis:{color:"#aa0000",borderWidth:2,borderColor:"#070707"}},data:r},{name:"地面",type:"line",clickable:!0,data:t,markePoint:{},itemStyle:{normal:{lineStyle:{width:2,color:"#aaaaaa"},areaStyle:{color:"#AE6F39",type:"default"},label:{show:!0,formatter:a=>a.data[0]<e.pipeInfo.Distance[a.dataIndex-1]+5&&a.dataIndex>0?"":a.data[1].toFixed(1)}}},markeLine:{data:[{type:"average",name:"平均高程"}]},stack:null,xAxisIndexs:0,yAxisIndex:0,symbol:null,symbolSize:6,symbolRotate:null,showAllSymbol:!1,dataFilter:"nearst",legendHoverLink:!0}]}},L=async()=>{var t;m.view&&((t=g.value)==null||t.extentTo(m.view))},F=()=>{var t;m.view&&(e.graphicsLayer=j(m.view,{id:"across-analys",title:"横剖面分析"}),(t=e.graphicsLayer)==null||t.removeAll())},M=()=>{var t,r,n;e.graphicsLayer&&((t=m.view)==null||t.map.remove(e.graphicsLayer)),(r=e.drawer)==null||r.destroy(),(n=e.drawAction)==null||n.destroy()};T(()=>{F()}),V(()=>{var t;(t=u.value)==null||t.Close(),M()});const Z=()=>{var t;o.curOperate="detailing",(t=g.value)==null||t.openDialog()};return(t,r)=>{const n=U,s=B("VChart"),i=P;return E(),H("div",null,[h(n,{ref_key:"refForm",ref:_,config:d(S)},null,8,["config"]),h(i,{ref_key:"refChartPanel",ref:u,"custom-class":"gis-across-analys-panel",title:"横剖面分析结果"},{default:$(()=>[W("div",te,[h(s,{ref_key:"refChart",ref:b,option:d(o).chartOption},null,8,["option"])])]),_:1},512),h(J,{ref_key:"refDetail",ref:g,tabs:d(o).tabs,telport:t.telport,onRefreshed:r[0]||(r[0]=p=>d(o).curOperate="viewingDetail"),onRefreshing:r[1]||(r[1]=p=>d(o).curOperate="detailing"),onClose:r[2]||(r[2]=p=>d(o).curOperate="analysed"),onRowdblclick:L},null,8,["tabs","telport"])])}}}),Ft=X(re,[["__scopeId","data-v-b0fb4f5e"]]);export{Ft as default};
