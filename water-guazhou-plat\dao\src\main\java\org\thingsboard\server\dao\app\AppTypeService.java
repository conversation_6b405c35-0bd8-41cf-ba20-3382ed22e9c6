package org.thingsboard.server.dao.app;

import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.dao.model.sql.AppType;

import java.util.List;

public interface AppTypeService {
    AppType findById(String id);

    AppType save(AppType appType);

    AppType update(AppType appType);

    AppType deleteById(String id) throws ThingsboardException;

    List<AppType> findList();
}
