package org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoProjectArchive;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;

@Getter
@Setter
public class SoProjectArchivePageRequest extends AdvancedPageableQueryEntity<SoProjectArchive, SoProjectArchivePageRequest> {
    // 所属项目编号
    private String projectCode;

    // 所属项目名称
    private String projectName;

}
