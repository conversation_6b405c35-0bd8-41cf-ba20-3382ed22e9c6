package org.thingsboard.server.dao.sql.base;

import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.experimental.PackagePrivate;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.base.BaseSchemeManagement;
import org.thingsboard.server.dao.util.imodel.query.base.BaseSchemeManagementPageRequest;

import java.util.List;

/**
 * 平台管理-方案管理Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-22
 */
@Mapper
public interface BaseSchemeManagementMapper {
    /**
     * 查询平台管理-方案管理
     *
     * @param id 平台管理-方案管理主键
     * @return 平台管理-方案管理
     */
    public BaseSchemeManagement selectBaseSchemeManagementById(String id);

    /**
     * 查询平台管理-方案管理列表
     *
     * @param baseSchemeManagement 平台管理-方案管理
     * @return 平台管理-方案管理集合
     */
    public IPage<BaseSchemeManagement> selectBaseSchemeManagementList(BaseSchemeManagementPageRequest baseSchemeManagement);

    /**
     * 新增平台管理-方案管理
     *
     * @param baseSchemeManagement 平台管理-方案管理
     * @return 结果
     */
    public int insertBaseSchemeManagement(BaseSchemeManagement baseSchemeManagement);

    /**
     * 修改平台管理-方案管理
     *
     * @param baseSchemeManagement 平台管理-方案管理
     * @return 结果
     */
    public int updateBaseSchemeManagement(BaseSchemeManagement baseSchemeManagement);

    /**
     * 删除平台管理-方案管理
     *
     * @param id 平台管理-方案管理主键
     * @return 结果
     */
    public int deleteBaseSchemeManagementById(String id);

    /**
     * 批量删除平台管理-方案管理
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBaseSchemeManagementByIds(@Param("array") List<String> ids);

    public List<BaseSchemeManagement> getAllBaseSchemeManagement();
}
