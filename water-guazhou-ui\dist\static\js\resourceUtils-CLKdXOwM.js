import{c as u}from"./basicInterfaces-Dc_Mm1a-.js";class m{constructor(r){this.data=r,this.type="encoded-mesh-texture",this.encoding=u.KTX2_ENCODING}}function v(n){return(n==null?void 0:n.type)==="encoded-mesh-texture"}async function h(n){return new Promise((r,s)=>{const c=new Blob([n]),o=new FileReader;o.onload=()=>{const t=o.result;r(JSON.parse(t))},o.onerror=t=>{s(t)},o.readAsText(c)})}async function w(n,r){return r===u.KTX2_ENCODING?new m(n):new Promise((s,c)=>{const o=new Blob([n],{type:r}),t=URL.createObjectURL(o),e=new Image,i=()=>{URL.revokeObjectURL(t),"decode"in e?e.decode().then(()=>s(e),()=>s(e)).then(d):(s(e),d())},a=L=>{URL.revokeObjectURL(t),c(L),d()},d=()=>{e.removeEventListener("load",i),e.removeEventListener("error",a)};e.addEventListener("load",i),e.addEventListener("error",a),e.src=t})}export{m as n,w as o,h as r,v as t};
