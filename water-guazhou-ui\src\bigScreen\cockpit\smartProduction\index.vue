<template>
  <div class="production-panel">
    <div class="panel-vertical left">
      <PanelCard title="生产看板">
        <div class="svg-container">
        </div>
      </PanelCard>
      <PanelCard title="水厂工艺">
        <Carousel :images="carouselImages" :interval="4000" />
      </PanelCard>
      <PanelCard title="报警清单">
        <ScrollTable 
          :list="alarmList" 
          :row-stay-time="2000"
          :scroll-animation-time="300"
          @view-detail="viewDetail" 
        />
      </PanelCard>
    </div>
    <div class="panel-vertical right">
      <PanelCard title="水源地生产监测">
        <LineChart 
          :data="sourceData" 
          :xAxisData="hourData"
          themeColor="#00E5BD"
          height="100%"
        />
      </PanelCard>
      <PanelCard title="供水泵房监测">
        <PieChart 
          :data="pumpData" 
          centerTitle="泵房生产"
          centerValue="176"
          centerUnit="万m³"
          height="100%"
        />
      </PanelCard>
      <PanelCard title="水厂生产监测">
        <LineChart 
          :data="plantData" 
          :xAxisData="hourData"
          themeColor="#FFC61A"
          height="100%"
        />
      </PanelCard>
    </div>
  </div>
</template>
<script setup>
import PanelCard from '../components/common/PanelCard.vue'
import ScrollTable from '../components/ScrollTable.vue'
import LineChart from '../components/LineChart.vue'
import PieChart from '../components/PieChart.vue'
import Carousel from '../components/Carousel.vue'
import { ref } from 'vue'

// 轮播图图片路径 - 使用相对路径
import img1 from '../assets/img/img_1.jpg'
import img2 from '../assets/img/img_2.jpg'
import img3 from '../assets/img/img_3.jpg'
import img4 from '../assets/img/img_4.jpg'
import img5 from '../assets/img/img_5.jpg'
import img6 from '../assets/img/img_6.jpg'
import img7 from '../assets/img/img_7.jpg'
import img8 from '../assets/img/img_8.jpg'

const carouselImages = [img1, img2, img3, img4, img5, img6, img7, img8]

// 图表数据
const waterData = [30, 60, 45, 43, 65, 45, 50, 65, 42, 67, 44, 75, 65, 55, 50, 38, 90, 55, 35, 58, 35]
const timeData = Array.from({ length: 25 }, (_, i) => i)

// 水源地生产监测数据
const sourceData = [28, 59, 43, 42, 65, 43, 52, 67, 43, 63, 44, 74, 62, 53, 51, 35, 75, 52, 37, 57, 35, 59, 43, 37]
const hourData = Array.from({ length: 24 }, (_, i) => i)

// 水厂生产监测数据
const plantData = [35, 60, 48, 45, 68, 47, 55, 70, 45, 68, 48, 77, 64, 56, 54, 40, 88, 57, 38, 60, 38, 57, 40, 35]

// 泵房数据
const pumpData = [
  { name: '泵房1', value: 150, percentage: '15%', color: '#FF5252' },
  { name: '泵房2', value: 200, percentage: '20%', color: '#FFC61A' },
  { name: '泵房3', value: 300, percentage: '30%', color: '#47EBEB' },
  { name: '泵房4', value: 350, percentage: '35%', color: '#3D7EFF' }
]

const alarmList = ref([
  {
    event: '这是对应内容',
    time: '2025-06-03 154732',
    status: '待处置'
  },
  {
    event: '这是对应内容',
    time: '2025-06-03 154732',
    status: '处置中'
  },
  {
    event: '这是对应内容',
    time: '2025-06-03 154732',
    status: '处置中'
  },
  {
    event: '这是对应内容',
    time: '2025-06-03 154732',
    status: '处置中'
  },
  {
    event: '这是对应内容',
    time: '2025-06-03 154732',
    status: '处置中'
  },
  {
    event: '这是对应内容',
    time: '2025-06-03 154732',
    status: '处置中'
  }
])

const viewDetail = (item) => {
  console.log('查看详情:', item)
}
</script>
<style lang="scss" scoped>
.production-panel{
  width: 100%;
  height: 100%;
  position: relative;

  .panel-vertical {
    position: absolute;
    top: 24px;
    width: 450px;
    height: calc(100vh - 96px);
    bottom: 0;
    z-index: 10;
    display: flex;
    flex-direction: column;
    pointer-events: auto;
    >div{
      flex: 1;
      margin-bottom: 24px;
    }
  }
  .left{
    left: 24px;
  }
  .right{
    right: 24px;
  }
  
  .svg-container {
    width: 100%;
    height: 100%;
    background: url('./生产看板.svg');
    background-size: cover;
    background-repeat: no-repeat;
  }
}
</style>
