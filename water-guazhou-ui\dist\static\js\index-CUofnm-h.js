import{d as z,c as a,a8 as H,s as b,r as O,o as P,x as f,y as Q,g as R,n as j,q as c,b7 as F,ak as G,eG as J,C as K}from"./index-r0dFAfgr.js";import{I as X}from"./common-CvK_P_ao.js";import{e as Z,f as $}from"./conservationWaterLevel-BIi1yWt3.js";import{g as ee}from"./index-C7go6VEC.js";import{_ as te}from"./CardSearch-CB_HNR-Q.js";import{_ as ae}from"./CardTable-rdWOL4_6.js";import oe from"./WaterLevelDialog-BVxfsHsi.js";import re from"./ImportDialog-D1n38S5_.js";import ne from"./AnalysisDialog-BnwZwSiT.js";import{f as ie}from"./DateFormatter-Bm9a68Ax.js";import{d as le}from"./processNumber-Clv_jqeh.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";/* empty css                 */import"./xlsx-rVJkW9yq.js";const se={class:"wrapper"},ce=z({__name:"index",setup(me){const T=e=>ie(e,"YYYY-MM-DD HH:mm:ss"),i=(e,t=2)=>e==null||e===""?"--":le(e),y=a(!1),h=a([]),_=a(0),s=a(!1),x=a(!1),g=a(!1),m=a("add"),p=a({}),u=a(new Map),w=a([]),v=a(),L=a({labelWidth:"100px",filters:[{type:"select",label:"测点名称",field:"stationId",options:H(()=>[{label:"全部测点",value:""},...w.value.map(e=>({label:e.name,value:e.id}))])},{type:"select",label:"数据来源",field:"dataSource",options:[{label:"手动录入",value:1},{label:"设备采集",value:2}]},{type:"daterange",label:"记录时间",field:"recordTime"}],operations:[{type:"btn-group",btns:[{type:"primary",perm:!0,text:"查询",icon:X.QUERY,click:()=>n()},{type:"default",perm:!0,text:"重置",svgIcon:b(F),click:()=>A()},{type:"primary",perm:!0,text:"新增数据",svgIcon:b(G),click:()=>D()},{type:"warning",perm:!0,text:"智能分析",svgIcon:b(J),click:()=>Y()}]}]}),r=O({title:"涵养水位数据列表",columns:[{label:"测点名称",prop:"stationName",minWidth:140,formatter:e=>C(e.stationId)},{label:"测点位置",prop:"stationLocation",minWidth:180,showOverflowTooltip:!0,formatter:e=>k(e.stationId)},{label:"原水液位(m)",prop:"rawWaterLevel",minWidth:130,align:"center",formatter:e=>i(e.rawWaterLevel)},{label:"地下水位(m)",prop:"groundwaterLevel",minWidth:130,align:"center",formatter:e=>i(e.groundwaterLevel)},{label:"液位变化(m)",prop:"levelChange",minWidth:130,align:"center",formatter:e=>i(e.levelChange)},{label:"降雨量(mm)",prop:"rainfallAmount",minWidth:120,align:"center",formatter:e=>i(e.rainfallAmount)},{label:"蒸发量(mm)",prop:"evaporationAmount",minWidth:120,align:"center",formatter:e=>i(e.evaporationAmount)},{label:"开采量(m³)",prop:"extractionAmount",minWidth:130,align:"center",formatter:e=>i(e.extractionAmount)},{label:"数据来源",prop:"dataSource",minWidth:110,align:"center",formatter:e=>e.dataSource===1?"手动录入":"设备采集"},{label:"记录时间",prop:"recordTime",minWidth:170,align:"center",formatter:e=>T(e.recordTime)},{label:"创建人",prop:"creatorName",minWidth:100,align:"center",formatter:e=>e.creatorName||e.creator||"--"}],dataList:h,operations:[{perm:!0,type:"primary",isTextBtn:!0,text:"查看",icon:"iconfont icon-chakan",click:e=>N(e)},{perm:!0,type:"success",isTextBtn:!0,text:"编辑",icon:"iconfont icon-bianji",click:e=>B(e)},{perm:!0,type:"danger",isTextBtn:!0,text:"删除",icon:"iconfont icon-shanchu",click:e=>M(e)}],pagination:{total:_.value,page:1,limit:10,handlePage:e=>{r.pagination&&(r.pagination.page=e,n())},handleSize:e=>{r.pagination&&(r.pagination.limit=e,n())}}}),C=e=>{const t=u.value.get(e);return(t==null?void 0:t.name)||e||"--"},k=e=>{const t=u.value.get(e);return(t==null?void 0:t.location)||"--"};P(()=>{I(),n()});const I=async()=>{try{const e=await ee({type:"水源地"});if(e.status===200){const t=e.data.data||[];u.value.clear(),w.value=t,t.forEach(o=>{u.value.set(o.id,o)})}}catch(e){console.error("获取站点数据失败:",e)}},n=async()=>{var e,t,o,W,S;y.value=!0;try{const l=((e=v.value)==null?void 0:e.queryParams)||{},q={pageNum:((t=r.pagination)==null?void 0:t.page)||1,pageSize:((o=r.pagination)==null?void 0:o.limit)||10,stationId:l.stationId||"",dataSource:l.dataSource,startTime:(W=l.recordTime)==null?void 0:W[0],endTime:(S=l.recordTime)==null?void 0:S[1]},d=await Z(q);d.data.code===200&&(h.value=d.data.data.list,_.value=d.data.data.total,r.pagination&&(r.pagination.total=d.data.data.total))}catch(l){console.error("获取涵养水位数据失败:",l),f.error("获取数据失败")}finally{y.value=!1}},A=()=>{var e;(e=v.value)==null||e.resetForm(),r.pagination.page=1,n()},D=()=>{m.value="add",p.value={dataSource:1},s.value=!0},B=e=>{m.value="edit",p.value={...e},s.value=!0},N=e=>{m.value="view",p.value={...e},s.value=!0},M=async e=>{try{await Q.confirm("确定要删除这条涵养水位数据吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),(await $(e.id)).data.code===200&&(f.success("删除成功"),n())}catch(t){t!=="cancel"&&(console.error("删除失败:",t),f.error("删除失败"))}},E=()=>{n()},V=()=>{n()},Y=()=>{g.value=!0},U=()=>{f.success("分析任务已启动")};return(e,t)=>(R(),j("div",se,[c(te,{ref_key:"refSearch",ref:v,config:L.value},null,8,["config"]),c(ae,{config:r,class:"card-table"},null,8,["config"]),c(oe,{visible:s.value,"onUpdate:visible":t[0]||(t[0]=o=>s.value=o),"form-data":p.value,"dialog-type":m.value,onConfirm:E},null,8,["visible","form-data","dialog-type"]),c(re,{visible:x.value,"onUpdate:visible":t[1]||(t[1]=o=>x.value=o),onConfirm:V},null,8,["visible"]),c(ne,{visible:g.value,"onUpdate:visible":t[2]||(t[2]=o=>g.value=o),onConfirm:U},null,8,["visible"])]))}}),Le=K(ce,[["__scopeId","data-v-8aeb6f11"]]);export{Le as default};
