package org.thingsboard.server.dao.construction;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionApplyFlow;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionApplyFlowPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionApplyFlowSaveRequest;

public interface SoConstructionApplyFlowService {

    IPage<SoConstructionApplyFlow> findAllConditional(SoConstructionApplyFlowPageRequest request);

    SoConstructionApplyFlow save(SoConstructionApplyFlowSaveRequest entity);

    boolean update(SoConstructionApplyFlow entity);

    boolean delete(String id);

}
