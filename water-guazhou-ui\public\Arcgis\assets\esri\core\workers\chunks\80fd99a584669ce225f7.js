(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[450,9880],{41993:e=>{function t(e){return Promise.resolve().then((()=>{var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}))}t.keys=()=>[],t.resolve=t,t.id=41993,e.exports=t},99880:(e,t,r)=>{"use strict";r.d(t,{V:()=>l});var s=r(68773),i=(r(3172),r(20102)),n=r(92604),o=r(17452);const a=n.Z.getLogger("esri.assets");function l(e){if(!s.Z.assetsPath)throw a.errorOnce("The API assets location needs to be set using config.assetsPath. More information: https://arcg.is/1OzLe50"),new i.Z("assets:path-not-set","config.assetsPath is not set");return(0,o.v_)(s.Z.assetsPath,e)}},32448:(e,t,r)=>{"use strict";r.d(t,{Z:()=>l});var s=r(43697),i=r(15923),n=r(50758),o=r(52011);class a{constructor(){this._emitter=new a.EventEmitter(this)}emit(e,t){return this._emitter.emit(e,t)}on(e,t){return this._emitter.on(e,t)}once(e,t){return this._emitter.once(e,t)}hasEventListener(e){return this._emitter.hasEventListener(e)}}!function(e){class t{constructor(e=null){this._target=e,this._listenersMap=null}clear(){this._listenersMap&&this._listenersMap.clear(),this._listenersMap=null}emit(e,t){const r=this._listenersMap&&this._listenersMap.get(e);if(!r)return!1;const s=this._target||this;return[...r].forEach((e=>{e.call(s,t)})),r.length>0}on(e,t){if(Array.isArray(e)){const r=e.map((e=>this.on(e,t)));return(0,n.AL)(r)}if(e.includes(","))throw new TypeError("Evented.on() with a comma delimited string of event types is not supported");this._listenersMap||(this._listenersMap=new Map);const r=this._listenersMap.get(e)||[];return r.push(t),this._listenersMap.set(e,r),{remove:()=>{const r=this._listenersMap&&this._listenersMap.get(e)||[],s=r.indexOf(t);s>=0&&r.splice(s,1)}}}once(e,t){const r=this.on(e,(e=>{r.remove(),t.call(null,e)}));return r}hasEventListener(e){const t=this._listenersMap&&this._listenersMap.get(e);return null!=t&&t.length>0}}e.EventEmitter=t,e.EventedMixin=e=>{let r=class extends e{constructor(){super(...arguments),this._emitter=new t}destroy(){this._emitter.clear()}emit(e,t){return this._emitter.emit(e,t)}on(e,t){return this._emitter.on(e,t)}once(e,t){return this._emitter.once(e,t)}hasEventListener(e){return this._emitter.hasEventListener(e)}};return r=(0,s._)([(0,o.j)("esri.core.Evented")],r),r};let r=class extends i.Z{constructor(){super(...arguments),this._emitter=new a.EventEmitter(this)}destroy(){this._emitter.clear()}emit(e,t){return this._emitter.emit(e,t)}on(e,t){return this._emitter.on(e,t)}once(e,t){return this._emitter.once(e,t)}hasEventListener(e){return this._emitter.hasEventListener(e)}};r=(0,s._)([(0,o.j)("esri.core.Evented")],r),e.EventedAccessor=r}(a||(a={}));const l=a},609:(e,t,r)=>{"use strict";r.d(t,{D:()=>u,v:()=>c});var s,i,n=r(43697),o=r(15923),a=r(70586),l=r(95330),h=r(52011);(i=s||(s={}))[i.PENDING=0]="PENDING",i[i.RESOLVED=1]="RESOLVED",i[i.REJECTED=2]="REJECTED";class d{constructor(e){this.instance=e,this._resolver=(0,l.dD)(),this._status=s.PENDING,this._resolvingPromises=[],this._resolver.promise.then((()=>{this._status=s.RESOLVED,this._cleanUp()}),(()=>{this._status=s.REJECTED,this._cleanUp()}))}addResolvingPromise(e){this._resolvingPromises.push(e),this._tryResolve()}isResolved(){return this._status===s.RESOLVED}isRejected(){return this._status===s.REJECTED}isFulfilled(){return this._status!==s.PENDING}abort(){this._resolver.reject((0,l.zE)())}when(e,t){return this._resolver.promise.then(e,t)}_cleanUp(){this._allPromise=this._resolvingPromises=this._allPromise=null}_tryResolve(){if(this.isFulfilled())return;const e=(0,l.dD)(),t=[...this._resolvingPromises,(0,a.j0)(e.promise)],r=this._allPromise=Promise.all(t);r.then((()=>{this.isFulfilled()||this._allPromise!==r||this._resolver.resolve(this.instance)}),(e=>{this.isFulfilled()||this._allPromise!==r||(0,l.D_)(e)||this._resolver.reject(e)})),e.resolve()}}const c=e=>{let t=class extends e{constructor(...e){super(...e),this._promiseProps=new d(this),this.addResolvingPromise(Promise.resolve())}isResolved(){return this._promiseProps.isResolved()}isRejected(){return this._promiseProps.isRejected()}isFulfilled(){return this._promiseProps.isFulfilled()}when(e,t){return new Promise(((e,t)=>{this._promiseProps.when(e,t)})).then(e,t)}catch(e){return this.when(null,e)}addResolvingPromise(e){e&&!this._promiseProps.isFulfilled()&&this._promiseProps.addResolvingPromise("_promiseProps"in e?e.when():e)}};return t=(0,n._)([(0,h.j)("esri.core.Promise")],t),t};let u=class extends(c(o.Z)){};u=(0,n._)([(0,h.j)("esri.core.Promise")],u)},17445:(e,t,r)=>{"use strict";r.d(t,{N1:()=>u,YP:()=>l,Z_:()=>m,gx:()=>h,nn:()=>g,on:()=>c,tX:()=>_});var s=r(91460),i=r(50758),n=r(70586),o=r(95330),a=r(26258);function l(e,t,r={}){return d(e,t,r,p)}function h(e,t,r={}){return d(e,t,r,f)}function d(e,t,r={},s){let i=null;const o=r.once?(e,r)=>{s(e)&&((0,n.hw)(i),t(e,r))}:(e,r)=>{s(e)&&t(e,r)};if(i=(0,a.aQ)(e,o,r.sync,r.equals),r.initial){const t=e();o(t,t)}return i}function c(e,t,r,o={}){let a=null,h=null,d=null;function c(){a&&h&&(h.remove(),o.onListenerRemove?.(a),a=null,h=null)}function u(e){o.once&&o.once&&(0,n.hw)(d),r(e)}const p=l(e,((e,r)=>{c(),(0,s.vT)(e)&&(a=e,h=(0,s.on)(e,t,u),o.onListenerAdd?.(e))}),{sync:o.sync,initial:!0});return d=(0,i.kB)((()=>{p.remove(),c()})),d}function u(e,t){return function(e,t,r){if((0,o.Hc)(r))return Promise.reject((0,o.zE)());const s=e();if(t?.(s))return Promise.resolve(s);let a=null;function l(){a=(0,n.hw)(a)}return new Promise(((s,n)=>{a=(0,i.AL)([(0,o.fu)(r,(()=>{l(),n((0,o.zE)())})),d(e,(e=>{l(),s(e)}),{sync:!1,once:!0},t??p)])}))}(e,f,t)}function p(e){return!0}function f(e){return!!e}r(87538);const m={sync:!0},g={initial:!0},_={sync:!0,initial:!0}},41123:(e,t,r)=>{"use strict";r.d(t,{D:()=>i});const s="randomUUID"in crypto;function i(){if(s)return crypto.randomUUID();const e=crypto.getRandomValues(new Uint16Array(8));e[3]=4095&e[3]|16384,e[4]=16383&e[4]|32768;const t=t=>e[t].toString(16).padStart(4,"0");return t(0)+t(1)+"-"+t(2)+"-"+t(3)+"-"+t(4)+"-"+t(5)+t(6)+t(7)}},73660:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>mt});var s=r(40330),i=r(43697),n=r(68773),o=r(3172),a=r(20102),l=r(32448),h=r(91460),d=r(22974),c=r(78286),u=r(95330),p=r(17445),f=r(17452),m=r(5600),g=(r(75215),r(52011)),_=(r(67676),r(940));r(2587),(()=>{if("function"==typeof Element.prototype.closest)return(e,t)=>e.closest(t);Element.prototype.matches||Element.prototype.msMatchesSelector})();var v=r(61247),y=(r(80442),r(92604)),w=r(70586),S=r(609),I=r(41123),k=r(90578),b=r(58971),A=r(38805),T=r(8728),U=r(3894),C=r(50758);const x=new Map;function P(){x.clear()}function R(e){x.delete(e)}y.Z.getLogger("esri.widgets.support.widgetUtils");function O(e){const t=U.Z.acquire();for(let e=0;e<arguments.length;e++){const r=arguments[e],s=typeof r;if("string"===s)t.push(r);else if(Array.isArray(r))t.push.apply(t,r);else if("object"===s)for(const e in r)r[e]&&t.push(e)}const r=t.join(" ");return U.Z.release(t),r}function D(e){this[e.getAttribute("data-node-ref")]=e}(()=>{const e=new Map;new ResizeObserver((t=>{P();for(const r of t)e.get(r.target)?.(r)}))})();const E=["dd","dl","dt","h1","h2","h3","h4","h5","h6","sub","sup","animate","animatetransform","circle","clippath","defs","ellipse","g","image","line","lineargradient","marker","mask","path","pattern","polygon","polyline","radialgradient","rect","stop","svg","switch","symbol","text","textpath","tspan","use"].reduce(((e,t)=>(e[t]=[],e)),{}),N=["align","alink","alt","bgcolor","border","cellpadding","cellspacing","class","color","cols","colspan","coords","d","dir","face","height","hspace","ismap","lang","marginheight","marginwidth","multiple","nohref","noresize","noshade","nowrap","ref","rel","rev","rows","rowspan","scrolling","shape","span","summary","tabindex","title","usemap","valign","value","vlink","vspace","width"],L=new T.Z({whiteList:E,onTagAttr:(e,t,r)=>{const s=`${t}="${r}"`;if(N.includes(t))return s},stripIgnoreTag:!0,stripIgnoreTagBody:["script","style"]},!0),j="http://www.w3.org/",M=`${j}2000/svg`,q=`${j}1999/xlink`;let F=[],$=(e,t)=>{let r={};return Object.keys(e).forEach((t=>{r[t]=e[t]})),t&&Object.keys(t).forEach((e=>{r[e]=t[e]})),r},B=(e,t)=>e.vnodeSelector===t.vnodeSelector&&(e.properties&&t.properties?e.properties.key===t.properties.key&&e.properties.bind===t.properties.bind:!e.properties&&!t.properties),V=e=>{if("string"!=typeof e)throw new Error("Style values must be strings")},z=(e,t,r)=>{if(""!==t.vnodeSelector)for(let s=r;s<e.length;s++)if(B(e[s],t))return s;return-1},Z=(e,t,r,s)=>{let i=e[t];if(""===i.vnodeSelector)return;let n=i.properties;if(!(n?void 0===n.key?n.bind:n.key:void 0))for(let n=0;n<e.length;n++)if(n!==t){let t=e[n];if(B(t,i))throw new Error(`${r.vnodeSelector} had a ${i.vnodeSelector} child ${"added"===s?s:"removed"}, but there is now more than one. You must add unique key properties to make them distinguishable.`)}},H=e=>{if(e.properties){let t=e.properties.enterAnimation;t&&t(e.domNode,e.properties)}},J=[],W=!1,G=e=>{(e.children||[]).forEach(G),e.properties&&e.properties.afterRemoved&&e.properties.afterRemoved.apply(e.properties.bind||e.properties,[e.domNode])},Y=()=>{W=!1,J.forEach(G),J.length=0},K=e=>{J.push(e),W||(W=!0,"undefined"!=typeof window&&"requestIdleCallback"in window?window.requestIdleCallback(Y,{timeout:16}):setTimeout(Y,16))},X=e=>{let t=e.domNode;if(e.properties){let r=e.properties.exitAnimation;if(r){t.style.pointerEvents="none";let s=()=>{t.parentNode&&(t.parentNode.removeChild(t),K(e))};return void r(t,s,e.properties)}}t.parentNode&&(t.parentNode.removeChild(t),K(e))},Q=(e,t,r)=>{if(!t)return;let s=r.eventHandlerInterceptor,i=Object.keys(t),n=i.length;for(let o=0;o<n;o++){let n=i[o],a=t[n];if("className"===n)throw new Error('Property "className" is not supported, use "class".');if("class"===n)ie(e,a,!0);else if("classes"===n){let t=Object.keys(a),r=t.length;for(let s=0;s<r;s++){let r=t[s];a[r]&&e.classList.add(r)}}else if("styles"===n){let t=Object.keys(a),s=t.length;for(let i=0;i<s;i++){let s=t[i],n=a[s];n&&(V(n),r.styleApplyer(e,s,n))}}else if("key"!==n&&null!=a){let i=typeof a;"function"===i?(0===n.lastIndexOf("on",0)&&(s&&(a=s(n,a,e,t)),"oninput"===n&&function(){let e=a;a=function(t){e.apply(this,[t]),t.target["oninput-value"]=t.target.value}}()),e[n]=a):r.namespace===M?"href"===n?e.setAttributeNS(q,n,a):e.setAttribute(n,a):"string"===i&&"value"!==n?"innerHTML"===n?e[n]=L.sanitize(a):ee(e)&&n in e?e[n]=a:e.setAttribute(n,a):e[n]=a}}};function ee(e){if(!(e instanceof Element&&e.tagName.includes("-")))return!1;const t=window.customElements.get(e.tagName.toLowerCase());return!!t&&e instanceof t}let te,re=(e,t,r)=>{((e,t,r)=>{if(t)for(let s of t)se(s,e,void 0,r)})(e,t.children,r),t.text&&(e.textContent=t.text),Q(e,t.properties,r),t.properties&&t.properties.afterCreate&&t.properties.afterCreate.apply(t.properties.bind||t.properties,[e,r,t.vnodeSelector,t.properties,t.children])},se=(e,t,r,s)=>{let i,n=0,o=e.vnodeSelector,a=t.ownerDocument;if(""===o)i=e.domNode=a.createTextNode(e.text),void 0!==r?t.insertBefore(i,r):t.appendChild(i);else{for(let l=0;l<=o.length;++l){let h=o.charAt(l);if(l===o.length||"."===h||"#"===h){let h=o.charAt(n-1),d=o.slice(n,l);"."===h?i.classList.add(d):"#"===h?i.id=d:("svg"===d&&(s=$(s,{namespace:M})),void 0!==s.namespace?i=e.domNode=a.createElementNS(s.namespace,d):(i=e.domNode=e.domNode||a.createElement(d),"input"===d&&e.properties&&void 0!==e.properties.type&&i.setAttribute("type",e.properties.type)),void 0!==r?t.insertBefore(i,r):i.parentNode!==t&&t.appendChild(i)),n=l+1}}re(i,e,s)}},ie=(e,t,r)=>{t&&t.split(" ").forEach((t=>{t&&e.classList.toggle(t,r)}))};te=(e,t,r)=>{let s=e.domNode,i=!1;if(e===t)return!1;let n=!1;if(""===t.vnodeSelector){if(t.text!==e.text){let e=s.ownerDocument.createTextNode(t.text);return s.parentNode.replaceChild(e,s),t.domNode=e,i=!0,i}t.domNode=s}else 0===t.vnodeSelector.lastIndexOf("svg",0)&&(r=$(r,{namespace:M})),e.text!==t.text&&(n=!0,void 0===t.text?s.removeChild(s.firstChild):s.textContent=t.text),t.domNode=s,n=((e,t,r,s,i)=>{if(r===s)return!1;s=s||F;let n,o=(r=r||F).length,a=s.length,l=0,h=0,d=!1;for(;h<a;){let a=l<o?r[l]:void 0,c=s[h];if(void 0!==a&&B(a,c))d=te(a,c,i)||d,l++;else{let a=z(r,c,l+1);if(a>=0){for(n=l;n<a;n++)X(r[n]),Z(r,n,e,"removed");d=te(r[a],c,i)||d,l=a+1}else se(c,t,l<o?r[l].domNode:void 0,i),H(c),Z(s,h,e,"added")}h++}if(o>l)for(n=l;n<o;n++)X(r[n]),Z(r,n,e,"removed");return d})(t,s,e.children,t.children,r)||n,n=((e,t,r,s)=>{if(!r)return;let i=!1,n=Object.keys(r),o=n.length;for(let a=0;a<o;a++){let o=n[a],l=r[o],h=t[o];if("class"===o)h!==l&&(ie(e,h,!1),ie(e,l,!0));else if("classes"===o){let t=e.classList,r=Object.keys(l),s=r.length;for(let e=0;e<s;e++){let s=r[e],n=!!l[s];n!==!!h[s]&&(i=!0,n?t.add(s):t.remove(s))}}else if("styles"===o){let t=Object.keys(l),r=t.length;for(let n=0;n<r;n++){let r=t[n],o=l[r];o!==h[r]&&(i=!0,o?(V(o),s.styleApplyer(e,r,o)):s.styleApplyer(e,r,""))}}else if(l||"string"!=typeof h||(l=""),"value"===o){let t=e[o];t!==l&&(e["oninput-value"]?t===e["oninput-value"]:l!==h)&&(e[o]=l,e["oninput-value"]=void 0),l!==h&&(i=!0)}else if(l!==h){let t=typeof l;"function"===t&&s.eventHandlerInterceptor||(s.namespace===M?"href"===o?e.setAttributeNS(q,o,l):e.setAttribute(o,l):"string"===t?"innerHTML"===o?e[o]=L.sanitize(l):"role"===o&&""===l?e.removeAttribute(o):ee(e)&&o in e?e[o]=l:e.setAttribute(o,l):e[o]!==l&&(e[o]=l),i=!0)}}return i})(s,e.properties,t.properties,r)||n,t.properties&&t.properties.afterUpdate&&t.properties.afterUpdate.apply(t.properties.bind||t.properties,[s,r,t.vnodeSelector,t.properties,t.children]);return n&&t.properties&&t.properties.updateAnimation&&t.properties.updateAnimation(s,t.properties,e.properties),i};let ne=(e,t)=>({getLastRender:()=>e,update:r=>{if(e.vnodeSelector!==r.vnodeSelector)throw new Error("The selector for the root VNode may not be changed. (consider using dom.merge and add one extra level to the virtual DOM)");let s=e;e=r,te(s,r,t)},domNode:e.domNode});const oe={namespace:void 0,performanceLogger:()=>{},eventHandlerInterceptor:void 0,styleApplyer:(e,t,r)=>{"-"===t.charAt(0)?e.style.setProperty(t,r):e.style[t]=r}};let ae,le=e=>$(oe,e),he=(e,t,r)=>(r=le(r),se(t,e,void 0,r),ne(t,r)),de=(e,t,r)=>(r=le(r),se(t,e.parentNode,e,r),ne(t,r)),ce=(e,t,r)=>(r=le(r),t.domNode=e,re(e,t,r),ne(t,r)),ue=(e,t,r)=>(r=le(r),se(t,e.parentNode,e,r),e.parentNode.removeChild(e),ne(t,r));ae=Array.prototype.find?(e,t)=>e.find(t):(e,t)=>e.filter(t)[0];const pe={handleInterceptedEvent:(e,t,r,s)=>(e.scheduleRender(),t.properties[`on${s.type}`].apply(t.properties.bind||r,[s]))},fe={namespace:void 0,performanceLogger:()=>{},eventHandlerInterceptor:void 0,styleApplyer:(e,t,r)=>{e.style[t]=r}},me=(e,t,r=!1)=>{let s=e;return t.forEach(((e,i)=>{const n=s?.children?((e,t)=>e.find(t))(s.children,(t=>t.domNode===e)):void 0;r&&!n&&i!==t.length-1||(s=n)})),s};var ge=r(65161),_e=r(99880);let ve;ve="components/assets";const ye=Symbol("widget"),we=Symbol("widget-test-data"),Se=[],Ie={},ke=new WeakMap;function be(e,t){let r=t.children;if(r&&r.length)for(let t=0;t<r.length;++t)r[t]=be(e,r[t]);else r=Se;const s=t.vnodeSelector;if(Ce(s)){const i=t.properties||Ie,n=i.key||s;return{vnodeSelector:"div",properties:{key:n,afterCreate:Ae,afterUpdate:Te,afterRemoved:Ue,parentWidget:e,widgetConstructor:s,widgetProperties:{...i,key:n,children:r}},children:void 0,text:void 0,domNode:null}}return t}function Ae(e,t,r,{parentWidget:s,widgetConstructor:i,widgetProperties:n}){const o=new i(n);o.container=e,ke.set(e,o),o.afterCreate?.(o,e),s._internalHandles.add((0,C.kB)((()=>Ue(e))))}function Te(e,t,r,{widgetProperties:s}){const i=ke.get(e);i&&(i.set(s),i.afterUpdate?.(i,e))}function Ue(e){const t=ke.get(e);t&&(t.afterRemoved?.(t,e),t.destroy(),ke.delete(e))}function Ce(e){return"function"==typeof e&&e[ye]}const xe=new Set;var Pe,Re=r(70171),Oe=r(94443);let De=0;function Ee(e,t){for(const r in t)null!=e[r]&&("object"==typeof e[r]&&"object"==typeof t[r]?Ee(e[r],t?.[r]):e[r]=t[r]);return e}const Ne=(e=>{let t;const r={...pe,...e},s=(n=r,{...fe,...n}),i=s.performanceLogger;var n;let o,a=!0,l=!1;const h=[],d=[],c=(e,n,o)=>{let a;s.eventHandlerInterceptor=(e,s,n,o)=>function(e){let s;i("domEvent",e);const n=((e,t)=>{const r=[];for(;e&&e!==t;)r.push(e),e=e.parentNode;return r})(e.currentTarget,a.domNode),o=n.some((e=>customElements.get(e?.tagName?.toLowerCase())));if(e.eventPhase!==Event.CAPTURING_PHASE&&o){const t=e.composedPath(),r=t.slice(t.indexOf(e.currentTarget),t.indexOf(a.domNode)).filter((e=>e.getRootNode()===e.ownerDocument)).reverse();s=me(a.getLastRender(),r,!0)}else n.reverse(),s=me(a.getLastRender(),n);let l;return s&&(l=r.handleInterceptedEvent(t,s,this,e)),i("domEventProcessed",e),l},r.postProcessProjectionOptions?.(s);const l=o();a=e(n,l,s),h.push(a),d.push(o),r.afterFirstVNodeRendered&&r.afterFirstVNodeRendered(a,l)};let u=()=>{if(o=void 0,a){a=!1,i("renderStart",void 0);for(let e=0;e<h.length;e++){const t=d[e]();i("rendered",void 0),h[e].update(t),i("patched",void 0)}i("renderDone",void 0),a=!0}};return r.modifyDoRenderImplementation&&(u=r.modifyDoRenderImplementation(u,h,d)),t={renderNow:u,scheduleRender:()=>{o||l||(o=requestAnimationFrame(u))},stop:()=>{o&&(cancelAnimationFrame(o),o=void 0),l=!0},resume:()=>{l=!1,a=!0,t.scheduleRender()},append:(e,t)=>{c(he,e,t)},insertBefore:(e,t)=>{c(de,e,t)},merge:(e,t)=>{c(ce,e,t)},replace:(e,t)=>{c(ue,e,t)},detach:e=>{for(let t=0;t<d.length;t++)if(d[t]===e)return d.splice(t,1),h.splice(t,1)[0];throw new Error("renderFunction was not found")}},t})({postProcessProjectionOptions(e){const t=e.eventHandlerInterceptor,r=/capture$/i;e.eventHandlerInterceptor=(e,s,i,n)=>{const o=t?.(e,s,i,n),a=r.test(e);if(!((e=e.replace(r,"")).toLowerCase()in i)||a){const t=e[2].toLowerCase()+e.slice(3),r=e=>o?.call(i,e);i.addEventListener(t,r,a);const s=()=>i.removeEventListener(t,r,a),l=n.afterRemoved;n.afterRemoved=e=>{l?.(e),s()}}return o}},handleInterceptedEvent(e,t,r,s){const{eventPhase:i,type:n}=s,o=i===Event.CAPTURING_PHASE;let a=`on${n}${o?"capture":""}`;const l=t.properties;(l&&a in l||(a=`on${n[0].toUpperCase()}${n.slice(1)}${o?"Capture":""}`,l&&a in l))&&(P(),e.scheduleRender(),l[a].call(l.bind||r,s))}});let Le=!1,je=class extends((0,S.v)(l.Z.EventedAccessor)){constructor(e,t){super(e,t),this._attached=!1,this._internalHandles=new v.Z,this._projector=Ne,this._readyForTrueRender=!1,this.iconClass="esri-icon-checkbox-unchecked",this.key=this,this._loadLocale=(0,u.Ds)((async()=>{if(this._messageBundleProps&&this._messageBundleProps.length){const e=await(0,u.as)(this._messageBundleProps.map((async({bundlePath:e,propertyName:t})=>{let r=await(0,Oe.ME)(e);this.uiStrings&&Object.keys(this.uiStrings)&&(r=Ee((0,d.d9)(r),this.uiStrings)),this[t]=r})));for(const t of e)t.error&&y.Z.getLogger(this.declaredClass).error("widget-intl:locale-error",this.declaredClass,t.error)}await this.loadLocale()})),(0,ge.Y)((0,f.hF)((0,_e.V)("components/assets")));const r="esri-widget-uid-"+(0,I.D)(),s=this.render.bind(this);this._trackingTarget=new A.M((()=>this.scheduleRender()));const i=()=>{if(!this._readyForTrueRender||this.destroyed)return null;if(!this.visible)return{vnodeSelector:"div",properties:{key:r,class:"",styles:{display:"none"}},domNode:null,children:void 0,text:void 0};const e=s();let{properties:t}=e;t||(e.properties=t={});let{key:i,styles:n}=t;i||(t.key=r),n||(t.styles=n={}),n.display||(n.display="");let o=0;return e.children?.forEach((e=>{if(Ce(e.vnodeSelector))return;let{properties:t}=e;t||(e.properties=t={}),t.key||(t.key=`${this.id}--${o++}`)})),be(this,e)};var n;this.render=()=>{if(Le)return i();let e=function(e){return x.get(e)}(this)??null;if(e)return e;this._trackingTarget.clear(),Le=!0;try{e=(0,b.LJ)(this._trackingTarget,i)}catch(e){throw console.error(e),e}finally{Le=!1}return e&&function(e,t){x.set(e,t)}(this,e),e},this.addResolvingPromise(this._resourcesFetch=this.beforeFirstRender().then((()=>{this._readyForTrueRender=!0,this._postInitialize()}))),n=this._resourcesFetch,xe.add(n),n.finally((()=>xe.delete(n)))}normalizeCtorArgs(e,t){const r={...e};return t&&(r.container=t),r}postInitialize(){}beforeFirstRender(){return Promise.all([this.loadDependencies(),this._loadLocale()]).then((()=>{})).catch(u.H9)}async loadDependencies(){}async loadLocale(){}destroy(){this.destroyed||((0,w.SC)(this._trackingTarget),(0,w.SC)(this.viewModel),this._detach(this.container),this._set("container",null),this._internalHandles.destroy(),this._emitter.clear(),this.render=()=>null,this._projector=null,R(this))}set container(e){this._get("container")||this._set("container",e)}castContainer(e){return function(e){return"string"==typeof e?document.getElementById(e):e??null}(e)}get domNode(){return this.container}set domNode(e){this.container=e}get id(){return this._get("id")||this.get("container.id")||Date.now().toString(16)+"-widget-"+De++}set id(e){e&&this._set("id",e)}get label(){return this.declaredClass.split(".").pop()}set label(e){this._overrideIfSome("label",e)}get renderable(){return this._resourcesFetch}get visible(){return this._get("visible")}set visible(e){this._set("visible",e)}get[(Pe=ye,we)](){return{projector:this._projector}}render(){throw new Error("not implemented")}scheduleRender(){this.destroyed||(R(this),this._projector.scheduleRender())}classes(...e){return O.apply(this,e)}renderNow(){R(this),this._projector.renderNow()}_postInitialize(){if(this.destroyed)return;this.scheduleRender(),this._delegatedEventNames?.length&&this._internalHandles.add((0,p.YP)((()=>this.viewModel),((e,t)=>{t&&this._internalHandles.remove("delegated-events"),e&&(0,h.vT)(e)&&this._internalHandles.add(this._delegatedEventNames.map((t=>(0,h.on)(e,t,(e=>{this.emit(t,e)})))),"delegated-events")}),p.nn)),this.postInitialize();const e=async()=>{await this._loadLocale().catch(u.H9),this.scheduleRender()};this._internalHandles.add([(0,Re.qe)(e),(0,p.YP)((()=>this.uiStrings),e),(0,p.gx)((()=>this.container),(e=>{this.destroyed||this._attach(e)}),{initial:!0,once:!0})])}_attach(e){e&&(this._projector.merge(e,this.render),this._attached=!0)}_detach(e){this._attached&&(this._projector.detach(this.render),this._attached=!1),e?.parentNode?.removeChild(e)}};je[Pe]=!0,(0,i._)([(0,m.Cb)()],je.prototype,"_readyForTrueRender",void 0),(0,i._)([(0,m.Cb)({value:null})],je.prototype,"container",null),(0,i._)([(0,k.p)("container")],je.prototype,"castContainer",null),(0,i._)([(0,m.Cb)()],je.prototype,"iconClass",void 0),(0,i._)([(0,m.Cb)()],je.prototype,"id",null),(0,i._)([(0,m.Cb)()],je.prototype,"label",null),(0,i._)([(0,m.Cb)()],je.prototype,"renderable",null),(0,i._)([(0,m.Cb)()],je.prototype,"uiStrings",void 0),(0,i._)([(0,m.Cb)()],je.prototype,"viewModel",void 0),(0,i._)([(0,m.Cb)({value:!0})],je.prototype,"visible",null),(0,i._)([(0,m.Cb)()],je.prototype,"key",void 0),(0,i._)([(0,m.Cb)()],je.prototype,"children",void 0),(0,i._)([(0,m.Cb)()],je.prototype,"afterCreate",void 0),(0,i._)([(0,m.Cb)()],je.prototype,"afterUpdate",void 0),(0,i._)([(0,m.Cb)()],je.prototype,"afterRemoved",void 0),je=(0,i._)([(0,g.j)("esri.widgets.Widget")],je);const Me=je;function qe(e){return(t,r)=>{t.hasOwnProperty("_messageBundleProps")||(t._messageBundleProps=t._messageBundleProps?t._messageBundleProps.slice():[]),t._messageBundleProps.push({bundlePath:e,propertyName:r})}}var Fe=function(e){return{vnodeSelector:"",properties:void 0,children:void 0,text:e.toString(),domNode:null}},$e=function(e,t){for(var r=0,s=e.length;r<s;r++){var i=e[r];Array.isArray(i)?$e(i,t):null!=i&&!1!==i&&(i.hasOwnProperty("vnodeSelector")||(i=Fe(i)),t.push(i))}};function Be(e,t,...r){return"function"!=typeof e||Ce(e)?function(e,t){for(var r=[],s=2;s<arguments.length;s++)r[s-2]=arguments[s];if(1===r.length&&"string"==typeof r[0])return{vnodeSelector:e,properties:t||void 0,children:void 0,text:r[0],domNode:null};var i=[];return $e(r,i),{vnodeSelector:e,properties:t||void 0,children:i,text:void 0,domNode:null}}(e,t,...r):e(t,...r)}const Ve="esri-identity-form",ze={base:Ve,group:`${Ve}__group`,label:`${Ve}__label`,footer:`${Ve}__footer`,esriInput:"esri-input",esriButton:"esri-button",esriButtonSecondary:"esri-button--secondary"};let Ze=class extends Me{constructor(e,t){super(e,t),this._usernameInputNode=null,this._passwordInputNode=null,this.signingIn=!1,this.server=null,this.resource=null,this.error=null,this.oAuthPrompt=!1}render(){const{error:e,server:t,resource:r,signingIn:s,oAuthPrompt:i,messages:n}=this,o=Be("div",{class:ze.group},(0,_.n)(i?n.oAuthInfo:n.info,{server:t&&/\.arcgis\.com/i.test(t)?"ArcGIS Online":t,resource:`(${r||n.lblItem})`})),a=i?null:Be("div",{class:ze.group,key:"username"},Be("label",{class:ze.label},n.lblUser,Be("input",{value:"",required:!0,autocomplete:"off",spellcheck:!1,type:"text",bind:this,afterCreate:D,"data-node-ref":"_usernameInputNode",class:ze.esriInput}))),l=i?null:Be("div",{class:ze.group,key:"password"},Be("label",{class:ze.label},n.lblPwd,Be("input",{value:"",required:!0,type:"password",bind:this,afterCreate:D,"data-node-ref":"_passwordInputNode",class:ze.esriInput}))),h=Be("div",{class:this.classes(ze.group,ze.footer)},Be("input",{type:"submit",disabled:!!s,value:s?n.lblSigning:n.lblOk,class:ze.esriButton}),Be("input",{type:"button",value:n.lblCancel,bind:this,onclick:this._cancel,class:this.classes(ze.esriButton,ze.esriButtonSecondary)})),d=e?Be("div",null,e.details&&e.details.httpStatus?n.invalidUser:n.noAuthService):null;return Be("form",{class:ze.base,bind:this,onsubmit:this._submit},o,d,a,l,h)}_cancel(){this._set("signingIn",!1),this._usernameInputNode&&(this._usernameInputNode.value=""),this._passwordInputNode&&(this._passwordInputNode.value=""),this.emit("cancel")}_submit(e){e.preventDefault(),this._set("signingIn",!0);const t=this.oAuthPrompt?{}:{username:this._usernameInputNode&&this._usernameInputNode.value,password:this._passwordInputNode&&this._passwordInputNode.value};this.emit("submit",t)}};(0,i._)([(0,m.Cb)(),qe("esri/identity/t9n/identity")],Ze.prototype,"messages",void 0),(0,i._)([(0,m.Cb)()],Ze.prototype,"signingIn",void 0),(0,i._)([(0,m.Cb)()],Ze.prototype,"server",void 0),(0,i._)([(0,m.Cb)()],Ze.prototype,"resource",void 0),(0,i._)([(0,m.Cb)()],Ze.prototype,"error",void 0),(0,i._)([(0,m.Cb)()],Ze.prototype,"oAuthPrompt",void 0),Ze=(0,i._)([(0,g.j)("esri.identity.IdentityForm")],Ze);const He=Ze;var Je=r(44291);const We="esri-identity-modal",Ge={base:We,open:`${We}--open`,closed:`${We}--closed`,title:`${We}__title`,dialog:`${We}__dialog`,content:`${We}__content`,closeButton:`${We}__close-button`,iconClose:"esri-icon-close"};let Ye=class extends Me{constructor(e,t){super(e,t),this.container=document.createElement("div"),this.content=null,this.open=!1,this._focusTrap=null,this._close=()=>{this.open=!1},document.body.appendChild(this.container),this.addHandles((0,p.YP)((()=>this.open),(()=>this._toggleFocusTrap())))}destroy(){this._destroyFocusTrap()}get title(){return this.messages?.auth.signIn}render(){const e=this.id,{open:t,content:r,title:s,messages:i}=this,n=t&&!!r,o={[Ge.open]:n,[Ge.closed]:!n},a=Be("button",{class:Ge.closeButton,"aria-label":i.close,title:i.close,bind:this,onclick:this._close,type:"button"},Be("span",{"aria-hidden":"true",class:Ge.iconClose})),l=`${e}_title`,h=`${e}_content`,d=s?Be("h1",{id:l,class:Ge.title},s):null,c=n?Be("div",{bind:this,class:Ge.dialog,role:"dialog","aria-labelledby":l,"aria-describedby":h,afterCreate:this._createFocusTrap},a,d,this._renderContent(h)):null;return Be("div",{tabIndex:-1,class:this.classes(Ge.base,o)},c)}_destroyFocusTrap(){this._focusTrap?.deactivate({onDeactivate:()=>{}}),this._focusTrap=null}_toggleFocusTrap(){const{_focusTrap:e,open:t}=this;e&&(t?e.activate():e.deactivate())}_createFocusTrap(e){this._destroyFocusTrap();const t=requestAnimationFrame((()=>{this._focusTrap=(0,Je.v)(e,{initialFocus:"input",onDeactivate:this._close}),this._toggleFocusTrap()}));this.addHandles((0,C.kB)((()=>cancelAnimationFrame(t))))}_renderContent(e){const t=this.content;return"string"==typeof t?Be("div",{class:Ge.content,id:e,innerHTML:t}):function(e){return e&&"function"==typeof e.render}(t)?Be("div",{class:Ge.content,id:e},t.render()):t instanceof HTMLElement?Be("div",{class:Ge.content,id:e,bind:t,afterCreate:this._attachToNode}):null}_attachToNode(e){e.appendChild(this)}};(0,i._)([(0,m.Cb)({readOnly:!0})],Ye.prototype,"container",void 0),(0,i._)([(0,m.Cb)()],Ye.prototype,"content",void 0),(0,i._)([(0,m.Cb)()],Ye.prototype,"open",void 0),(0,i._)([(0,m.Cb)(),qe("esri/t9n/common")],Ye.prototype,"messages",void 0),(0,i._)([(0,m.Cb)()],Ye.prototype,"title",null),Ye=(0,i._)([(0,g.j)("esri.identity.IdentityModal")],Ye);const Ke=Ye,Xe="esriJSAPIOAuth";class Qe{constructor(e,t){this.oAuthInfo=null,this.storage=null,this.appId=null,this.codeVerifier=null,this.expires=null,this.refreshToken=null,this.ssl=null,this.stateUID=null,this.token=null,this.userId=null,this.oAuthInfo=e,this.storage=t,this._init()}isValid(){let e=!1;if(this.oAuthInfo&&this.userId&&(this.refreshToken||this.token))if(null==this.expires&&this.refreshToken)e=!0;else if(this.expires){const t=Date.now();this.expires>t&&(this.expires-t)/1e3>60*this.oAuthInfo.minTimeUntilExpiration&&(e=!0)}return e}save(){if(!this.storage)return!1;const e=this._load(),t=this.oAuthInfo;if(t&&t.authNamespace&&t.portalUrl){let r=e[t.authNamespace];r||(r=e[t.authNamespace]={}),this.appId||(this.appId=t.appId),r[t.portalUrl]={appId:this.appId,codeVerifier:this.codeVerifier,expires:this.expires,refreshToken:this.refreshToken,ssl:this.ssl,stateUID:this.stateUID,token:this.token,userId:this.userId};try{this.storage.setItem(Xe,JSON.stringify(e))}catch(e){return console.warn(e),!1}return!0}return!1}destroy(){const e=this._load(),t=this.oAuthInfo;if(t&&t.appId&&t.portalUrl&&(null==this.expires||this.expires>Date.now())&&(this.refreshToken||this.token)){const e=t.portalUrl.replace(/^http:/i,"https:")+"/sharing/rest/oauth2/revokeToken",r=new FormData;if(r.append("f","json"),r.append("auth_token",this.refreshToken||this.token),r.append("client_id",t.appId),r.append("token_type_hint",this.refreshToken?"refresh_token":"access_token"),"function"==typeof navigator.sendBeacon)navigator.sendBeacon(e,r);else{const t=new XMLHttpRequest;t.open("POST",e),t.send(r)}}if(t&&t.authNamespace&&t.portalUrl&&this.storage){const r=e[t.authNamespace];if(r){delete r[t.portalUrl];try{this.storage.setItem(Xe,JSON.stringify(e))}catch(e){console.log(e)}}}t&&(t._oAuthCred=null,this.oAuthInfo=null)}_init(){const e=this._load(),t=this.oAuthInfo;if(t&&t.authNamespace&&t.portalUrl){let r=e[t.authNamespace];r&&(r=r[t.portalUrl],r&&(this.appId=r.appId,this.codeVerifier=r.codeVerifier,this.expires=r.expires,this.refreshToken=r.refreshToken,this.ssl=r.ssl,this.stateUID=r.stateUID,this.token=r.token,this.userId=r.userId))}}_load(){let e={};if(this.storage){const t=this.storage.getItem(Xe);if(t)try{e=JSON.parse(t)}catch(e){console.warn(e)}}return e}}Qe.prototype.declaredClass="esri.identity.OAuthCredential";var et,tt=r(96674);let rt=et=class extends tt.wq{constructor(e){super(e),this._oAuthCred=null,this.appId=null,this.authNamespace="/",this.expiration=20160,this.flowType="auto",this.forceLogin=!1,this.forceUserId=!1,this.locale=null,this.minTimeUntilExpiration=30,this.popup=!1,this.popupCallbackUrl="oauth-callback.html",this.popupWindowFeatures="height=490,width=800,resizable,scrollbars,status",this.portalUrl="https://www.arcgis.com",this.preserveUrlHash=!1,this.userId=null}clone(){return et.fromJSON(this.toJSON())}};(0,i._)([(0,m.Cb)({json:{write:!0}})],rt.prototype,"appId",void 0),(0,i._)([(0,m.Cb)({json:{write:!0}})],rt.prototype,"authNamespace",void 0),(0,i._)([(0,m.Cb)({json:{write:!0}})],rt.prototype,"expiration",void 0),(0,i._)([(0,m.Cb)({json:{write:!0}})],rt.prototype,"flowType",void 0),(0,i._)([(0,m.Cb)({json:{write:!0}})],rt.prototype,"forceLogin",void 0),(0,i._)([(0,m.Cb)({json:{write:!0}})],rt.prototype,"forceUserId",void 0),(0,i._)([(0,m.Cb)({json:{write:!0}})],rt.prototype,"locale",void 0),(0,i._)([(0,m.Cb)({json:{write:!0}})],rt.prototype,"minTimeUntilExpiration",void 0),(0,i._)([(0,m.Cb)({json:{write:!0}})],rt.prototype,"popup",void 0),(0,i._)([(0,m.Cb)({json:{write:!0}})],rt.prototype,"popupCallbackUrl",void 0),(0,i._)([(0,m.Cb)({json:{write:!0}})],rt.prototype,"popupWindowFeatures",void 0),(0,i._)([(0,m.Cb)({json:{write:!0}})],rt.prototype,"portalUrl",void 0),(0,i._)([(0,m.Cb)({json:{write:!0}})],rt.prototype,"preserveUrlHash",void 0),(0,i._)([(0,m.Cb)({json:{write:!0}})],rt.prototype,"userId",void 0),rt=et=(0,i._)([(0,g.j)("esri.identity.OAuthInfo")],rt);const st=rt;let it=class extends tt.wq{constructor(e){super(e),this.adminTokenServiceUrl=null,this.currentVersion=null,this.hasPortal=null,this.hasServer=null,this.owningSystemUrl=null,this.owningTenant=null,this.server=null,this.shortLivedTokenValidity=null,this.tokenServiceUrl=null,this.webTierAuth=null}};(0,i._)([(0,m.Cb)({json:{write:!0}})],it.prototype,"adminTokenServiceUrl",void 0),(0,i._)([(0,m.Cb)({json:{write:!0}})],it.prototype,"currentVersion",void 0),(0,i._)([(0,m.Cb)({json:{write:!0}})],it.prototype,"hasPortal",void 0),(0,i._)([(0,m.Cb)({json:{write:!0}})],it.prototype,"hasServer",void 0),(0,i._)([(0,m.Cb)({json:{write:!0}})],it.prototype,"owningSystemUrl",void 0),(0,i._)([(0,m.Cb)({json:{write:!0}})],it.prototype,"owningTenant",void 0),(0,i._)([(0,m.Cb)({json:{write:!0}})],it.prototype,"server",void 0),(0,i._)([(0,m.Cb)({json:{write:!0}})],it.prototype,"shortLivedTokenValidity",void 0),(0,i._)([(0,m.Cb)({json:{write:!0}})],it.prototype,"tokenServiceUrl",void 0),(0,i._)([(0,m.Cb)({json:{write:!0}})],it.prototype,"webTierAuth",void 0),it=(0,i._)([(0,g.j)("esri.identity.ServerInfo")],it);const nt=it;var ot=r(19745);const at={},lt=e=>{const t=new f.R9(e.owningSystemUrl).host,r=new f.R9(e.server).host,s=/.+\.arcgis\.com$/i;return s.test(t)&&s.test(r)},ht=(e,t)=>!!(lt(e)&&t&&t.some((t=>t.test(e.server))));let dt=null,ct=null;try{dt=window.localStorage,ct=window.sessionStorage}catch{}class ut extends l.Z{constructor(){super(),this._portalConfig=globalThis.esriGeowConfig,this.serverInfos=[],this.oAuthInfos=[],this.credentials=[],this._soReqs=[],this._xoReqs=[],this._portals=[],this._defaultOAuthInfo=null,this._defaultTokenValidity=60,this.dialog=null,this.formConstructor=He,this.tokenValidity=null,this.normalizeWebTierAuth=!1,this._appOrigin="null"!==window.origin?window.origin:window.location.origin,this._appUrlObj=(0,f.mN)(window.location.href),this._busy=null,this._rejectOnPersistedPageShow=!1,this._oAuthLocationParams=null,this._gwTokenUrl="/sharing/rest/generateToken",this._agsRest="/rest/services",this._agsPortal=/\/sharing(\/|$)/i,this._agsAdmin=/(https?:\/\/[^\/]+\/[^\/]+)\/admin\/?(\/.*)?$/i,this._adminSvcs=/\/rest\/admin\/services(\/|$)/i,this._gwDomains=[{regex:/^https?:\/\/www\.arcgis\.com/i,customBaseUrl:"maps.arcgis.com",tokenServiceUrl:"https://www.arcgis.com/sharing/rest/generateToken"},{regex:/^https?:\/\/(?:dev|[a-z\d-]+\.mapsdev)\.arcgis\.com/i,customBaseUrl:"mapsdev.arcgis.com",tokenServiceUrl:"https://dev.arcgis.com/sharing/rest/generateToken"},{regex:/^https?:\/\/(?:devext|[a-z\d-]+\.mapsdevext)\.arcgis\.com/i,customBaseUrl:"mapsdevext.arcgis.com",tokenServiceUrl:"https://devext.arcgis.com/sharing/rest/generateToken"},{regex:/^https?:\/\/(?:qaext|[a-z\d-]+\.mapsqa)\.arcgis\.com/i,customBaseUrl:"mapsqa.arcgis.com",tokenServiceUrl:"https://qaext.arcgis.com/sharing/rest/generateToken"},{regex:/^https?:\/\/[a-z\d-]+\.maps\.arcgis\.com/i,customBaseUrl:"maps.arcgis.com",tokenServiceUrl:"https://www.arcgis.com/sharing/rest/generateToken"}],this._legacyFed=[],this._regexSDirUrl=/http.+\/rest\/services\/?/gi,this._regexServerType=/(\/(FeatureServer|GPServer|GeoDataServer|GeocodeServer|GeoenrichmentServer|GeometryServer|GlobeServer|ImageServer|KnowledgeGraphServer|MapServer|MissionServer|MobileServer|NAServer|NetworkDiagramServer|OGCFeatureServer|ParcelFabricServer|RelationalCatalogServer|SceneServer|StreamServer|UtilityNetworkServer|ValidationServer|VectorTileServer|VersionManagementServer|VideoServer)).*/gi,this._gwUser=/http.+\/users\/([^\/]+)\/?.*/i,this._gwItem=/http.+\/items\/([^\/]+)\/?.*/i,this._gwGroup=/http.+\/groups\/([^\/]+)\/?.*/i,this._rePortalTokenSvc=/\/sharing(\/rest)?\/generatetoken/i,this._createDefaultOAuthInfo=!0,this._hasTestedIfAppIsOnPortal=!1,this._getOAuthLocationParams(),window.addEventListener("pageshow",(e=>{this._pageShowHandler(e)}))}registerServers(e){const t=this.serverInfos;t?(e=e.filter((e=>!this.findServerInfo(e.server))),this.serverInfos=t.concat(e)):this.serverInfos=e,e.forEach((e=>{e.owningSystemUrl&&this._portals.push(e.owningSystemUrl),e.hasPortal&&this._portals.push(e.server)}))}registerOAuthInfos(e){const t=this.oAuthInfos;if(t){for(const r of e){const e=this.findOAuthInfo(r.portalUrl);e&&t.splice(t.indexOf(e),1)}this.oAuthInfos=t.concat(e)}else this.oAuthInfos=e}registerToken(e){e={...e};const t=this._sanitizeUrl(e.server),r=this._isServerRsrc(t);let s,i=this.findServerInfo(t),n=!0;i||(i=new nt,i.server=this._getServerInstanceRoot(t),r?i.hasServer=!0:(i.tokenServiceUrl=this._getTokenSvcUrl(t),i.hasPortal=!0),this.registerServers([i])),s=this._findCredential(t),s?(delete e.server,Object.assign(s,e),n=!1):(s=new pt({userId:e.userId,server:i.server,token:e.token,expires:e.expires,ssl:e.ssl,scope:r?"server":"portal"}),s.resources=[t],this.credentials.push(s)),s.emitTokenChange(!1),n||s.refreshServerTokens()}toJSON(){return(0,d.yd)({serverInfos:this.serverInfos.map((e=>e.toJSON())),oAuthInfos:this.oAuthInfos.map((e=>e.toJSON())),credentials:this.credentials.map((e=>e.toJSON()))})}initialize(e){if(!e)return;"string"==typeof e&&(e=JSON.parse(e));const t=e.serverInfos,r=e.oAuthInfos,s=e.credentials;if(t){const e=[];t.forEach((t=>{t.server&&t.tokenServiceUrl&&e.push(t.declaredClass?t:new nt(t))})),e.length&&this.registerServers(e)}if(r){const e=[];r.forEach((t=>{t.appId&&e.push(t.declaredClass?t:new st(t))})),e.length&&this.registerOAuthInfos(e)}s&&s.forEach((e=>{e.server&&e.token&&e.expires&&e.expires>Date.now()&&((e=e.declaredClass?e:new pt(e)).emitTokenChange(),this.credentials.push(e))}))}findServerInfo(e){let t;e=this._sanitizeUrl(e);for(const r of this.serverInfos)if(this._hasSameServerInstance(r.server,e)){t=r;break}return t}findOAuthInfo(e){let t;e=this._sanitizeUrl(e);for(const r of this.oAuthInfos)if(this._hasSameServerInstance(r.portalUrl,e)){t=r;break}return t}findCredential(e,t){if(!e)return;let r;e=this._sanitizeUrl(e);const s=this._isServerRsrc(e)?"server":"portal";if(t){for(const i of this.credentials)if(this._hasSameServerInstance(i.server,e)&&t===i.userId&&i.scope===s){r=i;break}}else for(const t of this.credentials)if(this._hasSameServerInstance(t.server,e)&&-1!==this._getIdenticalSvcIdx(e,t)&&t.scope===s){r=t;break}return r}getCredential(e,t){let r,s,i=!0;t&&(r=!!t.token,s=t.error,i=!1!==t.prompt),t={...t},e=this._sanitizeUrl(e);const n=new AbortController,o=(0,u.hh)();if(t.signal&&(0,u.fu)(t.signal,(()=>{n.abort()})),(0,u.fu)(n,(()=>{o.reject(new a.Z("identity-manager:user-aborted","ABORTED"))})),(0,u.Hc)(n))return o.promise;t.signal=n.signal;const l=this._isAdminResource(e),h=r?this.findCredential(e):null;let d;if(h&&s&&s.details&&498===s.details.httpStatus)h.destroy();else if(h)return d=new a.Z("identity-manager:not-authorized","You are currently signed in as: '"+h.userId+"'. You do not have access to this resource: "+e,{error:s}),o.reject(d),o.promise;const c=this._findCredential(e,t);if(c)return o.resolve(c),o.promise;let p=this.findServerInfo(e);if(p)!p.hasServer&&this._isServerRsrc(e)&&(p._restInfoPms=this._getTokenSvcUrl(e),p.hasServer=!0);else{const t=this._getTokenSvcUrl(e);if(!t)return d=new a.Z("identity-manager:unknown-resource","Unknown resource - could not find token service endpoint."),o.reject(d),o.promise;p=new nt,p.server=this._getServerInstanceRoot(e),"string"==typeof t?(p.tokenServiceUrl=t,p.hasPortal=!0):(p._restInfoPms=t,p.hasServer=!0),this.registerServers([p])}return p.hasPortal&&void 0===p._selfReq&&(i||(0,f.D6)(p.tokenServiceUrl,this._appOrigin)||this._gwDomains.some((e=>e.tokenServiceUrl===p.tokenServiceUrl)))&&(p._selfReq={owningTenant:t&&t.owningTenant,selfDfd:this._getPortalSelf(p.tokenServiceUrl.replace(this._rePortalTokenSvc,"/sharing/rest/portals/self"),e)}),this._enqueue(e,p,t,o,l)}getResourceName(e){return this._isRESTService(e)?e.replace(this._regexSDirUrl,"").replace(this._regexServerType,"")||"":this._gwUser.test(e)&&e.replace(this._gwUser,"$1")||this._gwItem.test(e)&&e.replace(this._gwItem,"$1")||this._gwGroup.test(e)&&e.replace(this._gwGroup,"$1")||""}generateToken(e,t,r){const s=this._rePortalTokenSvc.test(e.tokenServiceUrl),i=new f.R9(this._appOrigin),n=e.shortLivedTokenValidity;let l,h,d,c,u,p,m,g;t&&(g=this.tokenValidity||n||this._defaultTokenValidity,g>n&&n>0&&(g=n)),r&&(l=r.isAdmin,h=r.serverUrl,d=r.token,p=r.signal,m=r.ssl,e.customParameters=r.customParameters),l?c=e.adminTokenServiceUrl:(c=e.tokenServiceUrl,u=new f.R9(c.toLowerCase()),e.webTierAuth&&r?.serverUrl&&!m&&"http"===i.scheme&&((0,f.D6)(i.uri,c,!0)||"https"===u.scheme&&i.host===u.host&&"7080"===i.port&&"7443"===u.port)&&(c=c.replace(/^https:/i,"http:").replace(/:7443/i,":7080")));const _={query:{request:"getToken",username:t?.username,password:t?.password,serverUrl:h,token:d,expiration:g,referer:l||s?this._appOrigin:null,client:l?"referer":null,f:"json",...e.customParameters},method:"post",authMode:"anonymous",useProxy:this._useProxy(e,r),signal:p,...r?.ioArgs};return s||(_.withCredentials=!1),(0,o.default)(c,_).then((r=>{const s=r.data;if(!s||!s.token)return new a.Z("identity-manager:authentication-failed","Unable to generate token");const i=e.server;return at[i]||(at[i]={}),t&&(at[i][t.username]=t.password),s.validity=g,s}))}isBusy(){return!!this._busy}checkSignInStatus(e){return this.checkAppAccess(e,"").then((e=>e.credential))}checkAppAccess(e,t,r){let s=!1;return this.getCredential(e,{prompt:!1}).then((i=>{let n;const l={f:"json"};if("portal"===i.scope)if(t&&(this._doPortalSignIn(e)||r&&r.force))n=i.server+"/sharing/rest/oauth2/validateAppAccess",l.client_id=t;else{if(!i.token)return{credential:i};n=i.server+"/sharing/rest"}else{if(!i.token)return{credential:i};n=i.server+"/rest/services"}return i.token&&(l.token=i.token),(0,o.default)(n,{query:l,authMode:"anonymous"}).then((e=>{if(!1===e.data.valid)throw new a.Z("identity-manager:not-authorized",`You are currently signed in as: '${i.userId}'.`,e.data);return s=!!e.data.viewOnlyUserTypeApp,{credential:i}})).catch((e=>{if("identity-manager:not-authorized"===e.name)throw e;const t=e.details&&e.details.httpStatus;if(498===t)throw i.destroy(),new a.Z("identity-manager:not-authenticated","User is not signed in.");if(400===t)throw new a.Z("identity-manager:invalid-request");return{credential:i}}))})).then((e=>({credential:e.credential,viewOnly:s})))}setOAuthResponseHash(e){e&&("#"===e.charAt(0)&&(e=e.substring(1)),this._processOAuthPopupParams((0,f.u0)(e)))}setOAuthRedirectionHandler(e){this._oAuthRedirectFunc=e}setProtocolErrorHandler(e){this._protocolFunc=e}signIn(e,t,r={}){const s=(0,u.hh)(),i=()=>{l?.remove(),h?.remove(),d?.remove(),o?.destroy(),this.dialog?.destroy(),this.dialog=o=l=h=d=null},n=()=>{i(),this._oAuthDfd=null,s.reject(new a.Z("identity-manager:user-aborted","ABORTED"))};r.signal&&(0,u.fu)(r.signal,(()=>{n()}));let o=new this.formConstructor;o.resource=this.getResourceName(e),o.server=t.server,this.dialog=new Ke,this.dialog.content=o,this.dialog.open=!0,this.emit("dialog-create");let l=o.on("cancel",n),h=(0,p.YP)((()=>this.dialog.open),n),d=o.on("submit",(e=>{this.generateToken(t,e,{isAdmin:r.isAdmin,signal:r.signal}).then((n=>{i();const o=new pt({userId:e.username,server:t.server,token:n.token,expires:null!=n.expires?Number(n.expires):null,ssl:!!n.ssl,isAdmin:r.isAdmin,validity:n.validity});s.resolve(o)})).catch((e=>{o.error=e,o.signingIn=!1}))}));return s.promise}oAuthSignIn(e,t,r,s){this._oAuthDfd=(0,u.hh)();const i=this._oAuthDfd;let n;s?.signal&&(0,u.fu)(s.signal,(()=>{const e=this._oAuthDfd&&this._oAuthDfd.oAuthWin_;e&&!e.closed?e.close():this.dialog&&m()})),i.resUrl_=e,i.sinfo_=t,i.oinfo_=r;const o=r._oAuthCred;if(o.storage&&("authorization-code"===r.flowType||"auto"===r.flowType&&!r.popup&&t.currentVersion>=8.4)){let e=crypto.getRandomValues(new Uint8Array(32));n=(0,f.rS)(e),o.codeVerifier=n,e=crypto.getRandomValues(new Uint8Array(32)),o.stateUID=(0,f.rS)(e),o.save()||(o.codeVerifier=n=null)}else o.codeVerifier=null;let l,h,d,c;this._getCodeChallenge(n).then((i=>{const n=!s||!1!==s.oAuthPopupConfirmation;r.popup&&n?(l=new this.formConstructor,l.oAuthPrompt=!0,l.server=t.server,this.dialog=new Ke,this.dialog.content=l,this.dialog.open=!0,this.emit("dialog-create"),h=l.on("cancel",m),d=(0,p.YP)((()=>this.dialog.open),m),c=l.on("submit",(()=>{g(),this._doOAuthSignIn(e,t,r,i)}))):this._doOAuthSignIn(e,t,r,i)}));const m=()=>{g(),this._oAuthDfd=null,i.reject(new a.Z("identity-manager:user-aborted","ABORTED"))},g=()=>{h?.remove(),d?.remove(),c?.remove(),l?.destroy(),this.dialog?.destroy(),this.dialog=null};return i.promise}destroyCredentials(){this.credentials&&this.credentials.slice().forEach((e=>{e.destroy()})),this.emit("credentials-destroy")}enablePostMessageAuth(e="https://www.arcgis.com/sharing/rest"){this._postMessageAuthHandle&&this._postMessageAuthHandle.remove(),this._postMessageAuthHandle=(0,h.on)(window,"message",(t=>{if((t.origin===this._appOrigin||t.origin.endsWith(".arcgis.com"))&&"arcgis:auth:requestCredential"===t.data?.type){const r=t.source;this.getCredential(e).then((e=>{r.postMessage({type:"arcgis:auth:credential",credential:{expires:e.expires,server:e.server,ssl:e.ssl,token:e.token,userId:e.userId}},t.origin)})).catch((e=>{r.postMessage({type:"arcgis:auth:error",error:{name:e.name,message:e.message}},t.origin)}))}}))}disablePostMessageAuth(){this._postMessageAuthHandle&&(this._postMessageAuthHandle.remove(),this._postMessageAuthHandle=null)}_getOAuthLocationParams(){let e=window.location.hash;if(e){"#"===e.charAt(0)&&(e=e.substring(1));const t=(0,f.u0)(e);let r=!1;if(t.access_token&&t.expires_in&&t.state&&t.hasOwnProperty("username"))try{t.state=JSON.parse(t.state),t.state.portalUrl&&(this._oAuthLocationParams=t,r=!0)}catch{}else if(t.error&&t.error_description&&(console.log("IdentityManager OAuth Error: ",t.error," - ",t.error_description),"access_denied"===t.error&&(r=!0,t.state)))try{t.state=JSON.parse(t.state)}catch{}r&&(window.location.hash=t.state?.hash||"")}let t=window.location.search;if(t){"?"===t.charAt(0)&&(t=t.substring(1));const e=(0,f.u0)(t);let r=!1;if(e.code&&e.state)try{e.state=JSON.parse(e.state),e.state.portalUrl&&e.state.uid&&(this._oAuthLocationParams=e,r=!0)}catch{}else if(e.error&&e.error_description&&(console.log("IdentityManager OAuth Error: ",e.error," - ",e.error_description),"access_denied"===e.error&&(r=!0,e.state)))try{e.state=JSON.parse(e.state)}catch{}if(r){const t={...e};["code","error","error_description","message_code","persist","state"].forEach((e=>{delete t[e]}));const r=(0,f.B7)(t),s=window.location.pathname+(r?`?${r}`:"")+(e.state?.hash||"");window.history.replaceState(window.history.state,"",s)}}}_getOAuthToken(e,t,r,s,i){return e=e.replace(/^http:/i,"https:"),(0,o.default)(`${e}/sharing/rest/oauth2/token`,{authMode:"anonymous",method:"post",query:s&&i?{grant_type:"authorization_code",code:t,redirect_uri:s,client_id:r,code_verifier:i}:{grant_type:"refresh_token",refresh_token:t,client_id:r}}).then((e=>e.data))}_getCodeChallenge(e){if(e&&globalThis.isSecureContext){const t=(new TextEncoder).encode(e);return crypto.subtle.digest("SHA-256",t).then((e=>(0,f.rS)(new Uint8Array(e))))}return Promise.resolve(null)}_pageShowHandler(e){if(e.persisted&&this.isBusy()&&this._rejectOnPersistedPageShow){const e=new a.Z("identity-manager:user-aborted","ABORTED");this._errbackFunc(e)}}_findCredential(e,t){let r,s,i,n,o=-1;const a=t&&t.token,l=t&&t.resource,h=this._isServerRsrc(e)?"server":"portal",d=this.credentials.filter((t=>this._hasSameServerInstance(t.server,e)&&t.scope===h));if(e=l||e,d.length)if(1===d.length){if(r=d[0],i=this.findServerInfo(r.server),s=i&&i.owningSystemUrl,n=s?this.findCredential(s,r.userId):void 0,o=this._getIdenticalSvcIdx(e,r),!a)return-1===o&&r.resources.push(e),this._addResource(e,n),r;-1!==o&&(r.resources.splice(o,1),this._removeResource(e,n))}else{let t,r;if(d.some((a=>(r=this._getIdenticalSvcIdx(e,a),-1!==r&&(t=a,i=this.findServerInfo(t.server),s=i&&i.owningSystemUrl,n=s?this.findCredential(s,t.userId):void 0,o=r,!0)))),a)t&&(t.resources.splice(o,1),this._removeResource(e,n));else if(t)return this._addResource(e,n),t}}_findOAuthInfo(e){let t=this.findOAuthInfo(e);if(!t)for(const r of this.oAuthInfos)if(this._isIdProvider(r.portalUrl,e)){t=r;break}return t}_addResource(e,t){t&&-1===this._getIdenticalSvcIdx(e,t)&&t.resources.push(e)}_removeResource(e,t){let r=-1;t&&(r=this._getIdenticalSvcIdx(e,t),r>-1&&t.resources.splice(r,1))}_useProxy(e,t){return t&&t.isAdmin&&!(0,f.D6)(e.adminTokenServiceUrl,this._appOrigin)||!this._isPortalDomain(e.tokenServiceUrl)&&"10.1"===String(e.currentVersion)&&!(0,f.D6)(e.tokenServiceUrl,this._appOrigin)}_getOrigin(e){const t=new f.R9(e);return t.scheme+"://"+t.host+(null!=t.port?":"+t.port:"")}_getServerInstanceRoot(e){const t=e.toLowerCase();let r=t.indexOf(this._agsRest);return-1===r&&this._isAdminResource(e)&&(r=this._agsAdmin.test(e)?e.replace(this._agsAdmin,"$1").length:e.search(this._adminSvcs)),-1!==r||(0,ot.P)(t)||(r=t.indexOf("/sharing")),-1===r&&"/"===t.substr(-1)&&(r=t.length-1),r>-1?e.substring(0,r):e}_hasSameServerInstance(e,t){return"/"===e.substr(-1)&&(e=e.slice(0,-1)),e=e.toLowerCase(),t=this._getServerInstanceRoot(t).toLowerCase(),e=this._normalizeAGOLorgDomain(e),t=this._normalizeAGOLorgDomain(t),(e=e.substr(e.indexOf(":")))===t.substr(t.indexOf(":"))}_normalizeAGOLorgDomain(e){const t=/^https?:\/\/(?:cdn|[a-z\d-]+\.maps)\.arcgis\.com/i,r=/^https?:\/\/(?:cdndev|[a-z\d-]+\.mapsdevext)\.arcgis\.com/i,s=/^https?:\/\/(?:cdnqa|[a-z\d-]+\.mapsqa)\.arcgis\.com/i;return t.test(e)?e=e.replace(t,"https://www.arcgis.com"):r.test(e)?e=e.replace(r,"https://devext.arcgis.com"):s.test(e)&&(e=e.replace(s,"https://qaext.arcgis.com")),e}_sanitizeUrl(e){const t=(n.Z.request.proxyUrl||"").toLowerCase(),r=t?e.toLowerCase().indexOf(t+"?"):-1;return-1!==r&&(e=e.substring(r+t.length+1)),e=(0,f.Fv)(e),(0,f.mN)(e).path}_isRESTService(e){return e.includes(this._agsRest)}_isAdminResource(e){return this._agsAdmin.test(e)||this._adminSvcs.test(e)}_isServerRsrc(e){return this._isRESTService(e)||this._isAdminResource(e)}_isIdenticalService(e,t){let r=!1;if(this._isRESTService(e)&&this._isRESTService(t)){const s=this._getSuffix(e).toLowerCase(),i=this._getSuffix(t).toLowerCase();if(r=s===i,!r){const e=/(.*)\/(MapServer|FeatureServer|UtilityNetworkServer).*/gi;r=s.replace(e,"$1")===i.replace(e,"$1")}}else this._isAdminResource(e)&&this._isAdminResource(t)?r=!0:this._isServerRsrc(e)||this._isServerRsrc(t)||!this._isPortalDomain(e)||(r=!0);return r}_isPortalDomain(e){const t=new f.R9(e.toLowerCase()),r=this._portalConfig;let s=this._gwDomains.some((e=>e.regex.test(t.uri)));return!s&&r&&(s=this._hasSameServerInstance(this._getServerInstanceRoot(r.restBaseUrl),t.uri)),s||n.Z.portalUrl&&(s=(0,f.D6)(t,n.Z.portalUrl,!0)),s||(s=this._portals.some((e=>this._hasSameServerInstance(e,t.uri)))),s=s||this._agsPortal.test(t.path),s}_isIdProvider(e,t){let r=-1,s=-1;this._gwDomains.forEach(((i,n)=>{-1===r&&i.regex.test(e)&&(r=n),-1===s&&i.regex.test(t)&&(s=n)}));let i=!1;if(r>-1&&s>-1&&(0===r||4===r?0!==s&&4!==s||(i=!0):1===r?1!==s&&2!==s||(i=!0):2===r?2===s&&(i=!0):3===r&&3===s&&(i=!0)),!i){const r=this.findServerInfo(t),s=r&&r.owningSystemUrl;s&&lt(r)&&this._isPortalDomain(s)&&this._isIdProvider(e,s)&&(i=!0)}return i}_getIdenticalSvcIdx(e,t){let r=-1;for(let s=0;s<t.resources.length;s++){const i=t.resources[s];if(this._isIdenticalService(e,i)){r=s;break}}return r}_getSuffix(e){return e.replace(this._regexSDirUrl,"").replace(this._regexServerType,"$1")}_getTokenSvcUrl(e){let t,r,s;if(this._isRESTService(e)||this._isAdminResource(e)){const s=this._getServerInstanceRoot(e);return t=s+"/admin/generateToken",r=(0,o.default)(e=s+"/rest/info",{query:{f:"json"}}).then((e=>e.data)),{adminUrl:t,promise:r}}if(this._isPortalDomain(e)){let t="";if(this._gwDomains.some((r=>(r.regex.test(e)&&(t=r.tokenServiceUrl),!!t))),t||this._portals.some((r=>(this._hasSameServerInstance(r,e)&&(t=r+this._gwTokenUrl),!!t))),t||(s=e.toLowerCase().indexOf("/sharing"),-1!==s&&(t=e.substring(0,s)+this._gwTokenUrl)),t||(t=this._getOrigin(e)+this._gwTokenUrl),t){const r=new f.R9(e).port;/^http:\/\//i.test(e)&&"7080"===r&&(t=t.replace(/:7080/i,":7443")),t=t.replace(/http:/i,"https:")}return t}if(e.toLowerCase().includes("premium.arcgisonline.com"))return"https://premium.arcgisonline.com/server/tokens"}_processOAuthResponseParams(e,t,r){const s=t._oAuthCred;if(e.code){const i=s.codeVerifier;return s.codeVerifier=null,s.stateUID=null,s.save(),this._getOAuthToken(r.server,e.code,t.appId,this._getRedirectURI(t,!0),i).then((i=>{const n=new pt({userId:i.username,server:r.server,token:i.access_token,expires:Date.now()+1e3*i.expires_in,ssl:i.ssl,oAuthState:e.state,_oAuthCred:s});return t.userId=n.userId,s.storage=i.persist?dt:ct,s.refreshToken=i.refresh_token,s.token=null,s.expires=i.refresh_token_expires_in?Date.now()+1e3*i.refresh_token_expires_in:null,s.userId=n.userId,s.ssl=n.ssl,s.save(),n}))}const i=new pt({userId:e.username,server:r.server,token:e.access_token,expires:Date.now()+1e3*Number(e.expires_in),ssl:"true"===e.ssl,oAuthState:e.state,_oAuthCred:s});return t.userId=i.userId,s.storage=e.persist?dt:ct,s.refreshToken=null,s.token=i.token,s.expires=i.expires,s.userId=i.userId,s.ssl=i.ssl,s.save(),Promise.resolve(i)}_processOAuthPopupParams(e){const t=this._oAuthDfd;if(this._oAuthDfd=null,t)if(clearInterval(this._oAuthIntervalId),this._oAuthOnPopupHandle?.remove(),e.error){const r="access_denied"===e.error,s=new a.Z(r?"identity-manager:user-aborted":"identity-manager:authentication-failed",r?"ABORTED":"OAuth: "+e.error+" - "+e.error_description);t.reject(s)}else this._processOAuthResponseParams(e,t.oinfo_,t.sinfo_).then((e=>{t.resolve(e)})).catch((e=>{t.reject(e)}))}_setOAuthResponseQueryString(e){e&&("?"===e.charAt(0)&&(e=e.substring(1)),this._processOAuthPopupParams((0,f.u0)(e)))}_exchangeToken(e,t,r){return(0,o.default)(`${e}/sharing/rest/oauth2/exchangeToken`,{authMode:"anonymous",method:"post",query:{f:"json",client_id:t,token:r}}).then((e=>e.data.token))}_getPlatformSelf(e,t){return e=e.replace(/^http:/i,"https:"),(0,o.default)(`${e}/sharing/rest/oauth2/platformSelf`,{authMode:"anonymous",headers:{"X-Esri-Auth-Client-Id":t,"X-Esri-Auth-Redirect-Uri":window.location.href.replace(/#.*$/,"")},method:"post",query:{f:"json",expiration:30},withCredentials:!0}).then((e=>e.data))}_getPortalSelf(e,t){let r;return this._gwDomains.some((t=>(t.regex.test(e)&&(r=t.customBaseUrl),!!r))),r?Promise.resolve({allSSL:!0,currentVersion:"8.4",customBaseUrl:r,portalMode:"multitenant",supportsOAuth:!0}):(this._appOrigin.startsWith("https:")?e=e.replace(/^http:/i,"https:").replace(/:7080/i,":7443"):/^http:/i.test(t)&&(e=e.replace(/^https:/i,"http:").replace(/:7443/i,":7080")),(0,o.default)(e,{query:{f:"json"},authMode:"anonymous",withCredentials:!0}).then((e=>e.data)))}_doPortalSignIn(e){const t=this._portalConfig,r=window.location.href,s=this.findServerInfo(e);return!(!t&&!this._isPortalDomain(r)||!(s?s.hasPortal||s.owningSystemUrl&&this._isPortalDomain(s.owningSystemUrl):this._isPortalDomain(e))||!(this._isIdProvider(r,e)||t&&(this._hasSameServerInstance(this._getServerInstanceRoot(t.restBaseUrl),e)||this._isIdProvider(t.restBaseUrl,e))||(0,f.D6)(r,e,!0)))}_checkProtocol(e,t,r,s){let i=!0;const n=s?t.adminTokenServiceUrl:t.tokenServiceUrl;return n.trim().toLowerCase().startsWith("https:")&&!this._appOrigin.startsWith("https:")&&(0,f.ed)(n)&&(i=!!this._protocolFunc&&!!this._protocolFunc({resourceUrl:e,serverInfo:t}),!i)&&r(new a.Z("identity-manager:aborted","Aborted the Sign-In process to avoid sending password over insecure connection.")),i}_enqueue(e,t,r,s,i,n){return s||(s=(0,u.hh)()),s.resUrl_=e,s.sinfo_=t,s.options_=r,s.admin_=i,s.refresh_=n,this._busy?this._hasSameServerInstance(this._getServerInstanceRoot(e),this._busy.resUrl_)?(this._oAuthDfd&&this._oAuthDfd.oAuthWin_&&this._oAuthDfd.oAuthWin_.focus(),this._soReqs.push(s)):this._xoReqs.push(s):this._doSignIn(s),s.promise}_doSignIn(e){this._busy=e,this._rejectOnPersistedPageShow=!1;const t=t=>{const r=e.options_&&e.options_.resource,s=e.resUrl_,i=e.refresh_;let n=!1;this.credentials.includes(t)||(i&&this.credentials.includes(i)?(i.userId=t.userId,i.token=t.token,i.expires=t.expires,i.validity=t.validity,i.ssl=t.ssl,i.creationTime=t.creationTime,n=!0,t=i):this.credentials.push(t)),t.resources||(t.resources=[]),t.resources.includes(r||s)||t.resources.push(r||s),t.scope=this._isServerRsrc(s)?"server":"portal",t.emitTokenChange();const o=this._soReqs,a={};this._soReqs=[],o.forEach((e=>{if(!this._isIdenticalService(s,e.resUrl_)){const r=this._getSuffix(e.resUrl_);a[r]||(a[r]=!0,t.resources.push(e.resUrl_))}})),e.resolve(t),o.forEach((e=>{this._hasSameServerInstance(this._getServerInstanceRoot(s),e.resUrl_)?e.resolve(t):this._soReqs.push(e)})),this._busy=e.resUrl_=e.sinfo_=e.refresh_=null,n||this.emit("credential-create",{credential:t}),this._soReqs.length?this._doSignIn(this._soReqs.shift()):this._xoReqs.length&&this._doSignIn(this._xoReqs.shift())},r=t=>{e.reject(t),this._busy=e.resUrl_=e.sinfo_=e.refresh_=null,this._soReqs.length?this._doSignIn(this._soReqs.shift()):this._xoReqs.length&&this._doSignIn(this._xoReqs.shift())},s=(i,n,o,l)=>{const d=e.sinfo_,c=!e.options_||!1!==e.options_.prompt,p=d.hasPortal&&this._findOAuthInfo(e.resUrl_);let m,g;if(i)t(new pt({userId:i,server:d.server,token:o||null,expires:null!=l?Number(l):null,ssl:!!n}));else if(window!==window.parent&&this._appUrlObj.query?.["arcgis-auth-origin"]&&this._appUrlObj.query?.["arcgis-auth-portal"]&&this._hasSameServerInstance(this._getServerInstanceRoot(this._appUrlObj.query["arcgis-auth-portal"]),e.resUrl_)){window.parent.postMessage({type:"arcgis:auth:requestCredential"},this._appUrlObj.query["arcgis-auth-origin"]);const s=(0,h.on)(window,"message",(e=>{e.source===window.parent&&e.data&&("arcgis:auth:credential"===e.data.type?(s.remove(),e.data.credential.expires<Date.now()?r(new a.Z("identity-manager:credential-request-failed","Parent application's token has expired.")):t(new pt(e.data.credential))):"arcgis:auth:error"===e.data.type&&(s.remove(),"tokenExpiredError"===e.data.error.name?r(new a.Z("identity-manager:credential-request-failed","Parent application's token has expired.")):r(a.Z.fromJSON(e.data.error))))}));(0,u.fu)(e.options_?.signal,(()=>{s.remove()}))}else if(p){let i=p._oAuthCred;if(!i){const e=new Qe(p,dt),t=new Qe(p,ct);e.isValid()&&t.isValid()?e.expires>t.expires?(i=e,t.destroy()):(i=t,e.destroy()):i=e.isValid()?e:t,p._oAuthCred=i}if(i.isValid()){m=new pt({userId:i.userId,server:d.server,token:i.token,expires:i.expires,ssl:i.ssl,_oAuthCred:i});const r=p.appId!==i.appId&&this._doPortalSignIn(e.resUrl_);r||i.refreshToken?(e._pendingDfd=i.refreshToken?this._getOAuthToken(d.server,i.refreshToken,i.appId).then((e=>(m.expires=Date.now()+1e3*e.expires_in,m.token=e.access_token,m))):Promise.resolve(m),e._pendingDfd.then((e=>r?this._exchangeToken(e.server,p.appId,e.token).then((t=>(e.token=t,e))).catch((()=>e)):e)).then((e=>{t(e)})).catch((()=>{i?.destroy(),s()}))):t(m)}else if(this._oAuthLocationParams&&this._hasSameServerInstance(p.portalUrl,this._oAuthLocationParams.state.portalUrl)&&(this._oAuthLocationParams.access_token||this._oAuthLocationParams.code&&this._oAuthLocationParams.state.uid===i.stateUID&&i.codeVerifier)){const s=this._oAuthLocationParams;this._oAuthLocationParams=null,e._pendingDfd=this._processOAuthResponseParams(s,p,d).then((e=>{t(e)})).catch(r)}else{const s=()=>{c?e._pendingDfd=this.oAuthSignIn(e.resUrl_,d,p,e.options_).then(t,r):(g=new a.Z("identity-manager:not-authenticated","User is not signed in."),r(g))};this._doPortalSignIn(e.resUrl_)?e._pendingDfd=this._getPlatformSelf(d.server,p.appId).then((e=>{(0,f.D6)(e.portalUrl,this._appOrigin,!0)?(m=new pt({userId:e.username,server:d.server,expires:Date.now()+1e3*e.expires_in,token:e.token}),t(m)):s()})).catch(s):s()}}else if(c){if(this._checkProtocol(e.resUrl_,d,r,e.admin_)){let s=e.options_;e.admin_&&(s=s||{},s.isAdmin=!0),e._pendingDfd=this.signIn(e.resUrl_,d,s).then(t,r)}}else g=new a.Z("identity-manager:not-authenticated","User is not signed in."),r(g)},i=()=>{const s=e.sinfo_,i=s.owningSystemUrl,n=e.options_;let o,a,l,h;if(n&&(o=n.token,a=n.error,l=n.prompt),h=this._findCredential(i,{token:o,resource:e.resUrl_}),!h)for(const e of this.credentials)if(this._isIdProvider(i,e.server)){h=e;break}if(h){const i=this.findCredential(e.resUrl_,h.userId);if(i)t(i);else if(ht(s,this._legacyFed)){const e=h.toJSON();e.server=s.server,e.resources=null,t(new pt(e))}else(e._pendingDfd=this.generateToken(this.findServerInfo(h.server),null,{serverUrl:e.resUrl_,token:h.token,signal:e.options_.signal,ssl:h.ssl})).then((r=>{t(new pt({userId:h?.userId,server:s.server,token:r.token,expires:null!=r.expires?Number(r.expires):null,ssl:!!r.ssl,isAdmin:e.admin_,validity:r.validity}))}),r)}else this._busy=null,o&&(e.options_.token=null),(e._pendingDfd=this.getCredential(i.replace(/\/?$/,"/sharing"),{resource:e.resUrl_,owningTenant:s.owningTenant,signal:e.options_.signal,token:o,error:a,prompt:l})).then((()=>{this._enqueue(e.resUrl_,e.sinfo_,e.options_,e,e.admin_)}),(t=>{e.resUrl_=e.sinfo_=e.refresh_=null,e.reject(t)}))};this._errbackFunc=r;const n=e.sinfo_.owningSystemUrl,o=this._isServerRsrc(e.resUrl_),l=e.sinfo_._restInfoPms;l?l.promise.then((t=>{const r=e.sinfo_;if(r._restInfoPms){r.adminTokenServiceUrl=r._restInfoPms.adminUrl,r._restInfoPms=null,r.tokenServiceUrl=((0,c.hS)("authInfo.tokenServicesUrl",t)||(0,c.hS)("authInfo.tokenServiceUrl",t)||(0,c.hS)("tokenServiceUrl",t))??null,r.shortLivedTokenValidity=(0,c.hS)("authInfo.shortLivedTokenValidity",t)??null,r.currentVersion=t.currentVersion,r.owningTenant=t.owningTenant;const e=r.owningSystemUrl=t.owningSystemUrl;e&&this._portals.push(e)}o&&r.owningSystemUrl?i():s()}),(()=>{e.sinfo_._restInfoPms=null;const t=new a.Z("identity-manager:server-identification-failed","Unknown resource - could not find token service endpoint.");r(t)})):o&&n?i():e.sinfo_._selfReq?e.sinfo_._selfReq.selfDfd.then((t=>{const r={};let s,i,n,o;return t&&(s=t.user&&t.user.username,r.username=s,r.allSSL=t.allSSL,i=t.supportsOAuth,o=parseFloat(t.currentVersion),"multitenant"===t.portalMode&&(n=t.customBaseUrl),e.sinfo_.currentVersion=o),e.sinfo_.webTierAuth=!!s,s&&this.normalizeWebTierAuth?this.generateToken(e.sinfo_,null,{ssl:r.allSSL}).catch((()=>null)).then((e=>(r.portalToken=e&&e.token,r.tokenExpiration=e&&e.expires,r))):!s&&i&&o>=4.4&&!this._findOAuthInfo(e.resUrl_)?this._generateOAuthInfo({portalUrl:e.sinfo_.server,customBaseUrl:n,owningTenant:e.sinfo_._selfReq.owningTenant}).catch((()=>null)).then((()=>r)):r})).catch((()=>null)).then((t=>{e.sinfo_._selfReq=null,t?s(t.username,t.allSSL,t.portalToken,t.tokenExpiration):s()})):s()}_generateOAuthInfo(e){let t,r=null,s=e.portalUrl;const i=e.customBaseUrl,n=e.owningTenant,a=!this._defaultOAuthInfo&&this._createDefaultOAuthInfo&&!this._hasTestedIfAppIsOnPortal;if(a){r=window.location.href;let e=r.indexOf("?");e>-1&&(r=r.slice(0,e)),e=r.search(/\/(apps|home)\//),r=e>-1?r.slice(0,e):null}return a&&r?(this._hasTestedIfAppIsOnPortal=!0,t=(0,o.default)(r+"/sharing/rest",{query:{f:"json"}}).then((()=>{this._defaultOAuthInfo=new st({appId:"arcgisonline",popupCallbackUrl:r+"/home/<USER>"})}))):t=Promise.resolve(),t.then((()=>{if(this._defaultOAuthInfo)return s=s.replace(/^http:/i,"https:"),(0,o.default)(s+"/sharing/rest/oauth2/validateRedirectUri",{query:{accountId:n,client_id:this._defaultOAuthInfo.appId,redirect_uri:(0,f.hF)(this._defaultOAuthInfo.popupCallbackUrl),f:"json"}}).then((e=>{if(e.data.valid){const t=this._defaultOAuthInfo.clone();e.data.urlKey&&i?t.portalUrl="https://"+e.data.urlKey.toLowerCase()+"."+i:t.portalUrl=s,t.popup=window!==window.top||!((0,f.D6)(s,this._appOrigin)||this._gwDomains.some((e=>e.regex.test(s)&&e.regex.test(this._appOrigin)))),this.oAuthInfos.push(t)}}))}))}_doOAuthSignIn(e,t,r,s){const i=r._oAuthCred,n={portalUrl:r.portalUrl};!r.popup&&r.preserveUrlHash&&window.location.hash&&(n.hash=window.location.hash),i.stateUID&&(n.uid=i.stateUID);const o={client_id:r.appId,response_type:i.codeVerifier?"code":"token",state:JSON.stringify(n),expiration:r.expiration,locale:r.locale,redirect_uri:this._getRedirectURI(r,!!i.codeVerifier)};r.forceLogin&&(o.force_login=!0),r.forceUserId&&r.userId&&(o.prepopulatedusername=r.userId),!r.popup&&this._doPortalSignIn(e)&&(o.redirectToUserOrgUrl=!0),i.codeVerifier&&(o.code_challenge=s||i.codeVerifier,o.code_challenge_method=s?"S256":"plain");const l=r.portalUrl.replace(/^http:/i,"https:")+"/sharing/oauth2/authorize",d=l+"?"+(0,f.B7)(o);if(r.popup){const e=window.open(d,"esriJSAPIOAuth",r.popupWindowFeatures);if(e)e.focus(),this._oAuthDfd.oAuthWin_=e,this._oAuthIntervalId=setInterval((()=>{if(e.closed){clearInterval(this._oAuthIntervalId),this._oAuthOnPopupHandle.remove();const e=this._oAuthDfd;if(e){const t=new a.Z("identity-manager:user-aborted","ABORTED");e.reject(t)}}}),500),this._oAuthOnPopupHandle=(0,h.on)(window,["arcgis:auth:hash","arcgis:auth:location:search"],(e=>{"arcgis:auth:hash"===e.type?this.setOAuthResponseHash(e.detail):this._setOAuthResponseQueryString(e.detail)}));else{const e=new a.Z("identity-manager:popup-blocked","ABORTED");this._oAuthDfd.reject(e)}}else this._rejectOnPersistedPageShow=!0,this._oAuthRedirectFunc?this._oAuthRedirectFunc({authorizeParams:o,authorizeUrl:l,resourceUrl:e,serverInfo:t,oAuthInfo:r}):window.location.href=d}_getRedirectURI(e,t){const r=window.location.href.replace(/#.*$/,"");if(e.popup)return(0,f.hF)(e.popupCallbackUrl);if(t){const e=(0,f.mN)(r);return e.query&&["code","error","error_description","message_code","persist","state"].forEach((t=>{delete e.query[t]})),(0,f.fl)(e.path,e.query)}return r}}ut.prototype.declaredClass="esri.identity.IdentityManagerBase";let pt=class extends l.Z.EventedAccessor{constructor(e){super(e),this._oAuthCred=null,this.tokenRefreshBuffer=2,e&&e._oAuthCred&&(this._oAuthCred=e._oAuthCred)}initialize(){this.resources=this.resources||[],null==this.creationTime&&(this.creationTime=Date.now())}refreshToken(){const e=s.id.findServerInfo(this.server),t=e&&e.owningSystemUrl,r=!!t&&"server"===this.scope,i=r&&ht(e,s.id._legacyFed),n=e.webTierAuth,o=n&&s.id.normalizeWebTierAuth,a=at[this.server],l=a&&a[this.userId];let h,d=this.resources&&this.resources[0],c=r?s.id.findServerInfo(t):null,u={username:this.userId,password:l};if(n&&!o)return;r&&!c&&s.id.serverInfos.some((e=>(s.id._isIdProvider(t,e.server)&&(c=e),!!c)));const p=c?s.id.findCredential(c.server,this.userId):null;if(!r||p){if(!i){if(r)h={serverUrl:d,token:p&&p.token,ssl:p&&p.ssl};else if(o)u=null,h={ssl:this.ssl};else{if(!l){let t;return d&&(d=s.id._sanitizeUrl(d),this._enqueued=1,t=s.id._enqueue(d,e,null,null,this.isAdmin,this),t.then((()=>{this._enqueued=0,this.refreshServerTokens()})).catch((()=>{this._enqueued=0}))),t}this.isAdmin&&(h={isAdmin:!0})}return s.id.generateToken(r?c:e,r?null:u,h).then((e=>{this.token=e.token,this.expires=null!=e.expires?Number(e.expires):null,this.creationTime=Date.now(),this.validity=e.validity,this.emitTokenChange(),this.refreshServerTokens()})).catch((()=>{}))}p?.refreshToken()}}refreshServerTokens(){"portal"===this.scope&&s.id.credentials.forEach((e=>{const t=s.id.findServerInfo(e.server),r=t&&t.owningSystemUrl;e!==this&&e.userId===this.userId&&r&&"server"===e.scope&&(s.id._hasSameServerInstance(this.server,r)||s.id._isIdProvider(r,this.server))&&(ht(t,s.id._legacyFed)?(e.token=this.token,e.expires=this.expires,e.creationTime=this.creationTime,e.validity=this.validity,e.emitTokenChange()):e.refreshToken())}))}emitTokenChange(e){clearTimeout(this._refreshTimer);const t=this.server&&s.id.findServerInfo(this.server),r=t&&t.owningSystemUrl,i=r&&s.id.findServerInfo(r);!1===e||r&&"portal"!==this.scope&&(!i||!i.webTierAuth||s.id.normalizeWebTierAuth)||null==this.expires&&null==this.validity||this._startRefreshTimer(),this.emit("token-change")}destroy(){this.userId=this.server=this.token=this.expires=this.validity=this.resources=this.creationTime=null,this._oAuthCred&&(this._oAuthCred.destroy(),this._oAuthCred=null);const e=s.id.credentials.indexOf(this);e>-1&&s.id.credentials.splice(e,1),this.emitTokenChange(),this.emit("destroy")}toJSON(){const e=(0,d.yd)({userId:this.userId,server:this.server,token:this.token,expires:this.expires,validity:this.validity,ssl:this.ssl,isAdmin:this.isAdmin,creationTime:this.creationTime,scope:this.scope}),t=this.resources;return t&&t.length>0&&(e.resources=t.slice()),e}_startRefreshTimer(){clearTimeout(this._refreshTimer);const e=6e4*this.tokenRefreshBuffer,t=2**31-1;let r=(this.validity?this.creationTime+6e4*this.validity:this.expires)-Date.now();r<0?r=0:r>t&&(r=t),this._refreshTimer=setTimeout(this.refreshToken.bind(this),r>e?r-e:r)}};(0,i._)([(0,m.Cb)()],pt.prototype,"creationTime",void 0),(0,i._)([(0,m.Cb)()],pt.prototype,"expires",void 0),(0,i._)([(0,m.Cb)()],pt.prototype,"isAdmin",void 0),(0,i._)([(0,m.Cb)()],pt.prototype,"oAuthState",void 0),(0,i._)([(0,m.Cb)()],pt.prototype,"resources",void 0),(0,i._)([(0,m.Cb)()],pt.prototype,"scope",void 0),(0,i._)([(0,m.Cb)()],pt.prototype,"server",void 0),(0,i._)([(0,m.Cb)()],pt.prototype,"ssl",void 0),(0,i._)([(0,m.Cb)()],pt.prototype,"token",void 0),(0,i._)([(0,m.Cb)()],pt.prototype,"tokenRefreshBuffer",void 0),(0,i._)([(0,m.Cb)()],pt.prototype,"userId",void 0),(0,i._)([(0,m.Cb)()],pt.prototype,"validity",void 0),pt=(0,i._)([(0,g.j)("esri.identity.Credential")],pt);class ft extends ut{}ft.prototype.declaredClass="esri.identity.IdentityManager";const mt=new ft;(0,s.qh)(mt)},2587:(e,t,r)=>{"use strict";r(90344),r(18848),r(940),r(70171);var s=r(94443),i=r(3172),n=r(20102),o=r(70586);async function a(e){if((0,o.pC)(h.fetchBundleAsset))return h.fetchBundleAsset(e);const t=await(0,i.default)(e,{responseType:"text"});return JSON.parse(t.data)}class l{constructor({base:e="",pattern:t,location:r=new URL(window.location.href)}){let s;s="string"==typeof r?e=>new URL(e,new URL(r,window.location.href)).href:r instanceof URL?e=>new URL(e,r).href:r,this.pattern="string"==typeof t?new RegExp(`^${t}`):t,this.getAssetUrl=s,e=e?e.endsWith("/")?e:e+"/":"",this.matcher=new RegExp(`^${e}(?:(.*)/)?(.*)$`)}fetchMessageBundle(e,t){return async function(e,t,r,i){const o=t.exec(r);if(!o)throw new n.Z("esri-intl:invalid-bundle",`Bundle id "${r}" is not compatible with the pattern "${t}"`);const l=o[1]?`${o[1]}/`:"",h=o[2],d=(0,s.Su)(i),c=`${l}${h}.json`,u=d?`${l}${h}_${d}.json`:c;let p;try{p=await a(e(u))}catch(t){if(u===c)throw new n.Z("intl:unknown-bundle",`Bundle "${r}" cannot be loaded`,{error:t});try{p=await a(e(c))}catch(e){throw new n.Z("intl:unknown-bundle",`Bundle "${r}" cannot be loaded`,{error:e})}}return p}(this.getAssetUrl,this.matcher,e,t)}}const h={};var d,c=r(99880);(0,s.tz)((d={pattern:"esri/",location:c.V},new l(d)))},90344:(e,t,r)=>{"use strict";r.d(t,{Ze:()=>g,p6:()=>_});var s=r(35454),i=r(70171);const n={year:"numeric",month:"numeric",day:"numeric"},o={year:"numeric",month:"long",day:"numeric"},a={year:"numeric",month:"short",day:"numeric"},l={year:"numeric",month:"long",weekday:"long",day:"numeric"},h={hour:"numeric",minute:"numeric"},d={...h,second:"numeric"},c={"short-date":n,"short-date-short-time":{...n,...h},"short-date-short-time-24":{...n,...h,hour12:!1},"short-date-long-time":{...n,...d},"short-date-long-time-24":{...n,...d,hour12:!1},"short-date-le":n,"short-date-le-short-time":{...n,...h},"short-date-le-short-time-24":{...n,...h,hour12:!1},"short-date-le-long-time":{...n,...d},"short-date-le-long-time-24":{...n,...d,hour12:!1},"long-month-day-year":o,"long-month-day-year-short-time":{...o,...h},"long-month-day-year-short-time-24":{...o,...h,hour12:!1},"long-month-day-year-long-time":{...o,...d},"long-month-day-year-long-time-24":{...o,...d,hour12:!1},"day-short-month-year":a,"day-short-month-year-short-time":{...a,...h},"day-short-month-year-short-time-24":{...a,...h,hour12:!1},"day-short-month-year-long-time":{...a,...d},"day-short-month-year-long-time-24":{...a,...d,hour12:!1},"long-date":l,"long-date-short-time":{...l,...h},"long-date-short-time-24":{...l,...h,hour12:!1},"long-date-long-time":{...l,...d},"long-date-long-time-24":{...l,...d,hour12:!1},"long-month-year":{month:"long",year:"numeric"},"short-month-year":{month:"short",year:"numeric"},year:{year:"numeric"},"short-time":h,"long-time":d},u=(0,s.w)()({shortDate:"short-date",shortDateShortTime:"short-date-short-time",shortDateShortTime24:"short-date-short-time-24",shortDateLongTime:"short-date-long-time",shortDateLongTime24:"short-date-long-time-24",shortDateLE:"short-date-le",shortDateLEShortTime:"short-date-le-short-time",shortDateLEShortTime24:"short-date-le-short-time-24",shortDateLELongTime:"short-date-le-long-time",shortDateLELongTime24:"short-date-le-long-time-24",longMonthDayYear:"long-month-day-year",longMonthDayYearShortTime:"long-month-day-year-short-time",longMonthDayYearShortTime24:"long-month-day-year-short-time-24",longMonthDayYearLongTime:"long-month-day-year-long-time",longMonthDayYearLongTime24:"long-month-day-year-long-time-24",dayShortMonthYear:"day-short-month-year",dayShortMonthYearShortTime:"day-short-month-year-short-time",dayShortMonthYearShortTime24:"day-short-month-year-short-time-24",dayShortMonthYearLongTime:"day-short-month-year-long-time",dayShortMonthYearLongTime24:"day-short-month-year-long-time-24",longDate:"long-date",longDateShortTime:"long-date-short-time",longDateShortTime24:"long-date-short-time-24",longDateLongTime:"long-date-long-time",longDateLongTime24:"long-date-long-time-24",longMonthYear:"long-month-year",shortMonthYear:"short-month-year",year:"year"}),p=(u.apiValues,u.toJSON.bind(u),u.fromJSON.bind(u),{ar:"ar-u-nu-latn-ca-gregory"});let f=new WeakMap,m=c["short-date-short-time"];function g(e){return e?c[e]:null}function _(e,t){return function(e){const t=e||m;let r=f.get(t);if(!r){const e=(0,i.Kd)(),s=p[(0,i.Kd)()]||e;r=new Intl.DateTimeFormat(s,t),f.set(t,r)}return r}(t).format(e)}(0,i.Ze)((()=>{f=new WeakMap,m=c["short-date-short-time"]}))},70171:(e,t,r)=>{"use strict";let s;r.d(t,{Kd:()=>o,Ze:()=>d,qe:()=>l});const i=globalThis.esriConfig?.locale??globalThis.dojoConfig?.locale;function n(){return i??globalThis.navigator?.language??"en"}function o(){return void 0===s&&(s=n()),s}const a=[];function l(e){return a.push(e),{remove(){a.splice(a.indexOf(e),1)}}}const h=[];function d(e){return h.push(e),{remove(){a.splice(h.indexOf(e),1)}}}globalThis.addEventListener?.("languagechange",(function(){const e=n();s!==e&&(s=e,[...h].forEach((t=>{t.call(null,e)})),[...a].forEach((t=>{t.call(null,e)})))}))},94443:(e,t,r)=>{"use strict";r.d(t,{ME:()=>p,Su:()=>f,tz:()=>u});var s=r(20102),i=r(95330),n=r(70171);const o=/^([a-z]{2})(?:[-_]([A-Za-z]{2}))?$/,a={ar:!0,bg:!0,bs:!0,ca:!0,cs:!0,da:!0,de:!0,el:!0,en:!0,es:!0,et:!0,fi:!0,fr:!0,he:!0,hr:!0,hu:!0,id:!0,it:!0,ja:!0,ko:!0,lt:!0,lv:!0,nb:!0,nl:!0,pl:!0,"pt-BR":!0,"pt-PT":!0,ro:!0,ru:!0,sk:!0,sl:!0,sr:!0,sv:!0,th:!0,tr:!0,uk:!0,vi:!0,"zh-CN":!0,"zh-HK":!0,"zh-TW":!0};function l(e){return a[e]??!1}const h=[],d=new Map;function c(e){for(const t of d.keys())m(e.pattern,t)&&d.delete(t)}function u(e){return h.includes(e)||(c(e),h.unshift(e)),{remove(){const t=h.indexOf(e);t>-1&&(h.splice(t,1),c(e))}}}async function p(e){const t=(0,n.Kd)();d.has(e)||d.set(e,async function(e,t){const r=[];for(const s of h)if(m(s.pattern,e))try{return await s.fetchMessageBundle(e,t)}catch(e){r.push(e)}if(r.length)throw new s.Z("intl:message-bundle-error",`Errors occurred while loading "${e}"`,{errors:r});throw new s.Z("intl:no-message-bundle-loader",`No loader found for message bundle "${e}"`)}(e,t));const r=d.get(e);return r&&await g.add(r),r}function f(e){if(!o.test(e))return null;const t=o.exec(e);if(null===t)return null;const[,r,s]=t,i=r+(s?"-"+s.toUpperCase():"");return l(i)?i:l(r)?r:null}function m(e,t){return"string"==typeof e?t.startsWith(e):e.test(t)}(0,n.Ze)((()=>{d.clear()}));const g=new class{constructor(){this._numLoading=0,this._dfd=null}async waitForAll(){this._dfd&&await this._dfd.promise}add(e){return this._increase(),e.then((()=>this._decrease()),(()=>this._decrease())),this.waitForAll()}_increase(){this._numLoading++,this._dfd||(this._dfd=(0,i.dD)())}_decrease(){this._numLoading=Math.max(this._numLoading-1,0),this._dfd&&0===this._numLoading&&(this._dfd.resolve(),this._dfd=null)}}},18848:(e,t,r)=>{"use strict";r.d(t,{sh:()=>l,uf:()=>h});var s=r(70586),i=r(70171);const n={ar:"ar-u-nu-latn"};let o=new WeakMap,a={};function l(e={}){const t={};return null!=e.digitSeparator&&(t.useGrouping=e.digitSeparator),null!=e.places&&(t.minimumFractionDigits=t.maximumFractionDigits=e.places),t}function h(e,t){return-0===e&&(e=0),function(e){const t=e||a;if(!o.has(t)){const r=(0,i.Kd)(),s=n[(0,i.Kd)()]||r;o.set(t,new Intl.NumberFormat(s,e))}return(0,s.j0)(o.get(t))}(t).format(e)}(0,i.Ze)((()=>{o=new WeakMap,a={}}))},940:(e,t,r)=>{"use strict";r.d(t,{n:()=>h});var s=r(92604),i=r(78286),n=r(19153),o=r(90344),a=r(18848);const l=s.Z.getLogger("esri.intl.substitute");function h(e,t,r={}){const{format:s={}}=r;return(0,n.gx)(e,(e=>function(e,t,r){let s,n;const o=e.indexOf(":");if(-1===o?s=e.trim():(s=e.slice(0,o).trim(),n=e.slice(o+1).trim()),!s)return"";const a=(0,i.hS)(s,t);if(null==a)return"";const l=(n?r?.[n]:null)??r?.[s];return l?d(a,l):n?c(a,n):u(a)}(e,t,s)))}function d(e,t){switch(t.type){case"date":return(0,o.p6)(e,t.intlOptions);case"number":return(0,a.uf)(e,t.intlOptions);default:return l.warn("missing format descriptor for key {key}"),u(e)}}function c(e,t){switch(t.toLowerCase()){case"dateformat":return(0,o.p6)(e);case"numberformat":return(0,a.uf)(e);default:return l.warn(`inline format is unsupported since 4.12: ${t}`),/^(dateformat|datestring)/i.test(t)?(0,o.p6)(e):/^numberformat/i.test(t)?(0,a.uf)(e):u(e)}}function u(e){switch(typeof e){case"string":return e;case"number":return(0,a.uf)(e);case"boolean":return""+e;default:return e instanceof Date?(0,o.p6)(e):""}}}}]);