import Excel, { ImagePosition, ImageRange, Workbook, Worksheet } from 'exceljs';
import fileDownload from 'js-file-download';
import $ from 'jquery';
import { IECharts } from '@/plugins/echart';

export class TrueExcel {
  workbook: Workbook;

  sheet: Worksheet;

  imageCol: number;

  sheetColumns: Partial<Excel.Column>[];

  mergeCellConfigs: [number, number, number, number][];

  /**
   * 头部行数
   */
  headerRows = 0;

  constructor() {
    this.workbook = new Excel.Workbook();
    this.workbook.created = new Date();
    this.sheet = this.workbook.addWorksheet('My Sheet');
    this.imageCol = 0;
    this.mergeCellConfigs = [];
    this.sheetColumns = [];
  }

  addSheet(name: string) {
    this.sheet = this.workbook.addWorksheet(name);
  }

  /**
   * fetch sheet by name or id
   * @param indexOrName
   */
  getSheet(indexOrName: string | number) {
    this.sheet = this.workbook.getWorksheet(indexOrName) as any;
  }

  /**
   * 根据elementplus的table实例生成excel sheet
   * @param table elementplus table
   */
  addElTable(table) {
    if (!table) {
      console.log('table实例未找到');
      return;
    }
    this.mergeCellConfigs = [];
    this.addTableHeader(table);
    this._dealEmptyHeaderCells();
    this.sheet.columns = this.sheetColumns;
    this._setHeaderStyle();
    this.addTableBody(table);
    this._mergeCells();
  }

  _setHeaderStyle() {
    Array.from({ length: this.headerRows || 0 }).map((item, i) => {
      const row = this.sheet.getRow(i + 1);
      row.height = 20;
      row.eachCell((cell) => {
        cell.alignment = {
          vertical: 'middle',
          horizontal: 'center'
        };
        cell.border = {
          top: { style: 'thin', color: { argb: 'b1b3b8' } },
          left: { style: 'thin', color: { argb: 'b1b3b8' } },
          bottom: { style: 'thin', color: { argb: 'b1b3b8' } },
          right: { style: 'thin', color: { argb: 'b1b3b8' } }
        };
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'c8c9cc' }
        };
        cell.font = {
          bold: true,
          size: 14
        };
      });
    });
  }

  _dealEmptyHeaderCells() {
    this.sheetColumns.map((item) => {
      const header: string[] = item.header as string[];
      item.header = header.map((head) => {
        if (head === null || head === undefined) {
          return '';
        }
        return head;
      });
      return item;
    });
  }
  /**
   * 根据配置向excel添加表格信息
   * @param config ITable
   */
  // addConfigTable (config: ICardTable) {
  //   if (!config) return
  //   this.workbook = new Excel.Workbook()
  //   this.workbook.created = new Date()
  //   this.sheet = this.workbook.addWorksheet(config.title || '工作簿')

  //   this.addConfigTableHeader(config)
  //   this.addConfigTableBody(config)
  // }

  // resolveTableHeader (config: ICardTable) {
  //   let deep = 0
  //   let cols = 0
  //   const sheetColumns: any[] = []
  //   const headerRows: string[][] = []
  //   // 获取表头的列数和行数
  //   const resoleColumns = (columns: IFormTableColumn[]) => {
  //     columns.map((item, i) => {
  //       if (i === 0) deep++
  //       if (item.subColumns?.length) {
  //         resoleColumns(item.subColumns)
  //       } else {
  //         cols++
  //         // sheetColumns.push({key:item.prop,width: parseInt((item.width||item.minWidth||'100').toString())})
  //       }
  //     })
  //   }
  //   const getChildCols = (columns: IFormTableColumn[]) => {
  //     let cols:number = 0
  //     let colValues:string[] = []
  //     const resolve = (columns: IFormTableColumn[],isChild:boolean) => {
  //       columns.map(item=>{
  //         if(item.subColumns?.length){
  //           resolve(item.subColumns,true)
  //         }else{
  //           cols++
  //           colValues.push(item.label)
  //         }
  //       })
  //     }
  //   }
  //   const resolve = (columns: IFormTableColumn[]) => {

  //     columns.map(item=>{
  //       const
  //       if(item.subColumns){

  //       }else{

  //       }
  //     })
  //     this.sheet.getRow(1).eachCell((cell, cellNum) => {
  //       if (!cell) console.log(cellNum)
  //       cell.fill = {
  //         type: 'pattern',
  //         pattern: 'solid',
  //         fgColor: { argb: 'eeeeee' }
  //       }
  //     })
  //     columns.map((item, i) => {
  //       if (i === 0) deep++
  //       if (item.subColumns?.length) {
  //         resolve(item.subColumns)
  //       }
  //     })
  //   }
  //   resoleColumns(config.columns)
  //   resolve(config.columns)
  //   return {
  //     headerRows: deep,
  //     headerCols: 'a'
  //   }
  // }

  // addConfigTableHeader (config) {
  //   headerItems.map((i, item) => {
  //     const head: any = {}

  //     head.header = item.innerText
  //     head.key = item.innerText
  //     head.width = 30
  //     if (item.classList.contains('is-hidden')) {
  //       if (fixedHeaderItems && fixedHeaderItems.length >= headerItems.length) {
  //         head.header = fixedHeaderItems[i].innerText
  //         head.key = fixedHeaderItems[i].innerText
  //       }
  //     }
  //     headerArray.push(head)
  //   })
  //   if (headerArray.length > 0) {
  //     this.sheet.columns = headerArray
  //     this.sheet.getRow(1).eachCell((cell, cellNum) => {
  //       if (!cell) console.log(cellNum)
  //       cell.fill = {
  //         type: 'pattern',
  //         pattern: 'solid',
  //         fgColor: { argb: 'eeeeee' }
  //       }
  //     })
  //   }
  // }

  // addConfigTableBody (config) {}
  /**
   * 添加表头
   * 20220207 lichao 修复因el-table存在固定列而导致的导出的列数据对应不正确的问题
   * @param {*} table
   */
  addTableHeader(table) {
    // const headerArray: any[] = []
    const headerRows = $(table.$el).find(
      '.el-table__header-wrapper .el-table__header tr'
    );
    this.headerRows = headerRows.length || 0;
    headerRows.map((i, item) => {
      this._resolveHeaderRow(i, item);
    });
    // const headerItems = $(table.$el).find(
    //   '.el-table__header-wrapper .el-table__header th'
    // )
    // const fixedHeaderItems = $(table.$el).find(
    //   '.el-table__fixed .el-table__header th'
    // )
    // headerItems.map((i, item) => {
    //   const head: any = {}

    //   head.header = item.innerText
    //   head.key = item.innerText
    //   head.width = 30
    //   if (item.classList.contains('is-hidden')) {
    //     if (fixedHeaderItems && fixedHeaderItems.length >= headerItems.length) {
    //       head.header = fixedHeaderItems[i].innerText
    //       head.key = fixedHeaderItems[i].innerText
    //     }
    //   }
    //   headerArray.push(head)
    // })
    // if (headerArray.length > 0) {
    //   this.sheet.columns = headerArray
    //   this.sheet.getRow(1).eachCell((cell, cellNum) => {
    //     if (!cell) console.log(cellNum)
    //     cell.fill = {
    //       type: 'pattern',
    //       pattern: 'solid',
    //       fgColor: { argb: 'eeeeee' }
    //     }
    //   })
    // }
  }

  /**
   * 执行合并单元格，具体的合并配置根据el-table的rowspan和colspan来自动生成
   */
  _mergeCells() {
    this.mergeCellConfigs.map((item) => {
      this.sheet.mergeCells(item);
    });
  }

  /**
   * 判断当前单元格是否已经被合并过
   * @param row 行号，从1 开始
   * @param col 列号，从1 开始
   */
  _judgeIsMerged(row, col) {
    const flag = this.mergeCellConfigs.findIndex(
      (item) =>
        item[0] <= row && item[2] >= row && item[1] <= col && item[3] >= col
    );
    return flag !== -1;
  }

  /**
   * 处理el-table的表头的行
   * @param i 行，从0 开始
   * @param row 当前行对象，el-table的row
   */
  _resolveHeaderRow(i: number, row: HTMLElement) {
    // const headerArray: Partial<Excel.Column>[] = []
    const headerItems = $(row).find('th');
    let startColNumber = 0;
    headerItems.map((j, item) => {
      // 处理选择框
      if (item.classList.contains('el-table-column--selection')) {
        return;
      }
      // 处理操作按钮
      if (item.innerText === '操作') {
        return;
      }
      while (this._judgeIsMerged(i + 1, startColNumber + 1)) {
        startColNumber++;
      }
      const colSpan = item.colSpan || 1;
      const rowSpan = item.rowSpan || 1;
      if (colSpan > 1 || rowSpan > 1) {
        this.mergeCellConfigs.push([
          i + 1,
          startColNumber + 1,
          i + rowSpan,
          startColNumber + colSpan
        ]);
      }
      const header: string[] =
        (this.sheetColumns[startColNumber]?.header as string[]) || [];
      header[i] = item.innerText || '';
      this.sheetColumns[startColNumber] = {
        header,
        key: item.innerText,
        width: 30 * colSpan
      };
      startColNumber += colSpan;
    });
  }

  /**
   * 添加el-table的表数据
   * @param {*} table el-table实例
   */
  addTableBody(table) {
    const bodyRows = $(table.$el).find(
      '.el-table__body-wrapper .el-table__body tr'
    );
    bodyRows.map((i, row) => {
      const rowArray: any[] = [];
      const rowItems = $(row).find('td');
      const startRowNumber = this.headerRows + i;
      let startColNumber = 0;
      let checked = true;
      rowItems.map((j, item) => {
        // 处理选择框
        if (item.classList.contains('el-table-column--selection')) {
          if (!$(item).find('.is-checked').length) {
            checked = false;
          }
          return;
        }
        // 如果单元格有is-hidden表示可能固定列,需要从固定列中取对应行列的单元格值
        // if (item.classList.contains('is-hidden')) {
        //   if (fixedBodyRows.length >= rowArray.length) {
        //     const fiexedCells = $(fixedBodyRows[i]).find('td')
        //     if (fiexedCells.length >= rowItems.length) {
        //       rowArray.push(fiexedCells[j].innerText)
        //     }
        //   }
        // } else if (item.innerText === '') {
        //   rowArray.push(item.innerText)
        // } else {
        //   rowArray.push(
        //     Number(item.innerText) || Number(item.innerText) === 0
        //       ? Number(item.innerText)
        //       : item.innerText
        //   )
        // }
        while (this._judgeIsMerged(startRowNumber + 1, startColNumber + 1)) {
          startColNumber++;
        }
        const colSpan = item.colSpan || 1;
        const rowSpan = item.rowSpan || 1;
        if (colSpan > 1 || rowSpan > 1) {
          this.mergeCellConfigs.push([
            startRowNumber + 1,
            startColNumber + 1,
            startRowNumber + rowSpan,
            startColNumber + colSpan
          ]);
        }
        rowArray[startColNumber] = item.innerText || '';
        startColNumber += colSpan;
        // if (item.rowSpan > 1 || item.colSpan > 1) {
        //   this.mergeCellConfigs.push([
        //     startRowNumber + 1,
        //     startColNumber + 1,
        //     startRowNumber + item.rowSpan,
        //     startColNumber + item.colSpan
        //   ])

        //   startColNumber += item.colSpan
        // } else {
        //   startColNumber += 1
        // }
      });
      const rowData: any[] = [];
      this.sheetColumns.map((item, j) => {
        let cellData = rowArray[j];
        switch (typeof cellData) {
          case 'string':
          case 'number':
          case 'bigint':
            cellData = rowArray[j];
            break;
          case 'object':
            cellData = JSON.stringify(rowArray[j]);
            break;
          default:
            cellData = '';
            break;
        }
        rowData[j] = cellData;
      });
      // 添加一行并设置行高为20
      if (checked) {
        const newRow = this.sheet.addRow(rowData);
        newRow.height = 20;
        newRow.alignment = {
          vertical: 'middle',
          horizontal: 'left'
        };
        newRow.font = {
          size: 14
        };
      }
    });
  }

  removeOperateRow() {
    const operateRow = this.sheet.getColumn('操作');
    this.sheet.spliceColumns(operateRow.number, 1);
  }

  removeSelectRow() {
    // const operateRow = this.sheet.getColumn('操作')
    this.sheet.spliceColumns(1, 1);
  }

  /**
   * 添加Echart快照到sheet
   * @param chartIns echart ref实例
   * @param range 图片的位置
   */
  addEchartSnapshot(chartIns?: IECharts, range?: ImageRange | ImagePosition) {
    if (!chartIns) {
      console.log('没有获取到当前echart实例');
      return;
    }
    const width = chartIns.getWidth();
    const height = chartIns.getHeight();
    const img = {
      src: chartIns.getDataURL({
        pixelRatio: window.devicePixelRatio || 1
      }),
      width,
      height
    };
    this.addImage(img, range);
  }

  /**
   * 往workbook添加图片，并将图片插入到当前sheet中
   * @param img 带src为base64
   * @param range 图片的位置
   * @return 返回图片id
   */
  addImage(img: any, range?: ImageRange | ImagePosition) {
    const lastCol = this.sheet?.columns?.length || 0;
    const imgId = this.workbook.addImage({
      base64: img.src,
      extension: 'png'
    });
    this.sheet.addImage(
      imgId,
      range || {
        tl: {
          col: lastCol,
          row: this.imageCol
        } as any,
        br: {
          col: img.width / 5 / 19.2 + lastCol,
          row: img.height / 5 / 5 + this.imageCol
        } as any,
        editAs: 'oneCell'
      }
    );
    this.imageCol += img.height / 5 / 5 + 5;
    return imgId;
  }

  export(name?: string) {
    this.workbook.xlsx
      .writeBuffer({
        // base64: true
      })
      .then((xls64) => {
        fileDownload(xls64, name || 'export.xlsx');
      })
      .catch((err) => {
        console.log(err);
      });
  }
}

// export class DownloadExcel {
//   trueExcel() {
//     this.workbook = new Excel.Workbook()
//     this.sheet = this.workbook.addWorksheet('Sheet one')

//     this.workbook.creator = '能源管理系统'
//     this.workbook.created = new Date()
//     this.workbook.modified = new Date()
//   }
//   tabelExportToExcel(data) {
//     this.sheet.properties.defaultRowHeight = 25
//     this.sheet.columns = []
//     const col = Object.keys(data[0])

//     if (data[0].label) {
//       col.forEach(v => {
//         const columnsInfo = {}
//         data.forEach(v => {
//           columnsInfo.header = v.label
//         })
//         columnsInfo.key = v
//         columnsInfo.width = 20
//         this.sheet.columns.push(columnsInfo)
//       })
//     } else {
//       col.forEach(v => {
//         const columnsInfo = {}
//         columnsInfo.header = v
//         columnsInfo.key = v
//         columnsInfo.width = 20
//         this.sheet.columns.push(columnsInfo)
//       })
//     }

//     data.forEach(item => {
//       this.sheet.addRow(item)

//     })
//   }
//   imageExportToExcel() {}

//   downloadExcel() {

//     this.workbook.xlsx.writeBuffer({
//       base64: true
//     }).then((xls64) => {
//       // 构建锚标记和附加文件（在chrome中工作）
//       var a = document.createElement('a')
//       var data = new Blob([xls64], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })

//       var url = URL.createObjectURL(data)
//       a.href = url
//       a.download = Date.now() + 'export.xlsx'
//       document.body.appendChild(a)
//       a.click()
//       setTimeout(() => {
//         document.body.removeChild(a)
//         window.URL.revokeObjectURL(url)
//       }, 0)
//     }).catch((error) => {

//     })
//   }
// }

// // -----------------------------------------------------------------------
// export class Download {
//   tabelExportToExcel(data) {
//     const workbook = new Excel.Workbook()
//     workbook.creator = '能源管理系统'
//     workbook.created = new Date()
//     workbook.modified = new Date()

//     const sheet = workbook.addWorksheet('My Sheet')
//     sheet.properties.defaultRowHeight = 25

//     sheet.columns = [
//       { header: '时间', key: 'date', width: 20 },
//       { header: '姓名', key: 'name', width: 10 },
//       { header: '地址', key: 'address', width: 40 }
//     ]
//     data.forEach(item => {
//       sheet.addRow(item)

//     })

//     workbook.xlsx.writeBuffer({
//       base64: true
//     }).then(xls64 => {
//       // build anchor tag and attach file (works in chrome)
//       // 构建锚标记和附加文件（在chrome中工作）
//       var a = document.createElement('a')
//       var data = new Blob([xls64], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })

//       var url = URL.createObjectURL(data)
//       a.href = url
//       a.download = 'export.xlsx'
//       document.body.appendChild(a)
//       a.click()
//       setTimeout(() => {
//         document.body.removeChild(a)
//         window.URL.revokeObjectURL(url)
//       }, 0)
//     }).catch(error => {

//     })
//   }
// }
