package org.thingsboard.server.dao.sql.workOrder;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrderPushConfig;

import java.util.List;

@Mapper
public interface WorkOrderPushConfigMapper extends BaseMapper<WorkOrderPushConfig> {
    @Select("select * from work_order_push_config where tenant_id = #{tenantId} order by create_time desc")
    List<WorkOrderPushConfig> getList(@Param("tenantId") String tenantId);
}
