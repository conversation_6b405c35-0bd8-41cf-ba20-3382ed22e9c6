<!-- 时段对比 -->
<template>
  <div class="view">
    <Search
      ref="cardSearch"
      :config="cardSearchConfig"
    >
    </Search>
    <SLCard
      class="card-table"
      title=" "
    >
      <template #right>
        <el-radio-group
          v-model="state.activeName"
        >
          <el-radio-button label="echarts">
            <Icon
              style="margin-right: 1px;font-size: 16px"
              icon="clarity:line-chart-line"
            />
          </el-radio-button>
          <el-radio-button label="list">
            <Icon
              style="margin-right: 1px;font-size: 16px"
              icon="material-symbols:table"
            />
          </el-radio-button>
        </el-radio-group>
      </template>
      <!-- 列表模式 -->
      <FormTable
        v-if="state.activeName === 'list'"
        ref="refCard"
        :config="cardTableConfig"
        class="table-box"
      ></FormTable>
      <!-- 图表模式 -->
      <div
        v-if="state.activeName === 'echarts'"
        ref="agriEcoDev"
        class="card-ehcarts"
      >
        <VChart
          ref="refChart"
          :theme="useAppStore().isDark?'dark':''"
          class="card-ehcarts"
          :option="state.chartOption"
        ></VChart>
      </div>
    </SLCard>
  </div>
</template>
<script lang="ts" setup>
import elementResizeDetectorMaker from 'element-resize-detector'
import { Search as SearchIcon, Download } from '@element-plus/icons-vue'
import dayjs from 'dayjs'
import { Icon } from '@iconify/vue'
import { useAppStore } from '@/store'
import { lineOption } from '../../data/echart'
import { IECharts } from '@/plugins/echart'
import { getFlowPeriod } from '@/api/pipeNetworkMonitoring/flowMonitoring'
import useGlobal from '@/hooks/global/useGlobal'

const { $messageWarning } = useGlobal()
const erd = elementResizeDetectorMaker()
const refChart = ref<IECharts>()
const loading = ref<boolean>(false)
const props = defineProps<{
  stationName?: string,
  stationId?: string
}>()
const cardSearch = ref<ISearchIns>()
const agriEcoDev = ref<any>()
const refCard = ref<ICardTableIns>()

const state = reactive<{
  chartOption: any,
  tableDataList: any,
  activeName: string
}>({
  chartOption: null,
  tableDataList: [],
  activeName: 'echarts'
})

watch(
  () => props.stationId,
  () => {
    console.log(props.stationId)
    refreshData()
  }
)

// 搜索栏初始化配置
const cardSearchConfig = reactive<ISearch>({
  defaultParams: {
    day: [dayjs().add(-7, 'day').format(), dayjs().format()],
    month: [dayjs().format(), dayjs().format()],
    queryType: 'day'
  },
  filters: [
    {
      type: 'select',
      label: '统计类型',
      field: 'queryType',
      width: '140px',
      options: [
        { label: '日分时', value: 'day' },
        { label: '月份日', value: 'month' }
      ]
    },
    // {
    //   type: 'select',
    //   label: '类型',
    //   field: 'va2',
    //   width: '140px',
    //   options: [
    //     { label: '日分时', value: '日分时' }
    //   ]
    // },
    { type: 'daterange',
      label: '日期',
      field: 'day',
      width: '300px',
      clearable: false,
      handleHidden: (params: any, query: any, formItem: IFormItem) => {
        formItem.hidden = params.queryType === 'month'
      }
    },
    { type: 'monthrange',
      label: '日期',
      field: 'month',
      width: '300px',
      clearable: false,
      handleHidden: (params: any, query: any, formItem: IFormItem) => {
        formItem.hidden = params.queryType === 'day'
      }
    },
    {
      type: 'btn-group',
      btns: [
        { perm: true,
          text: '查询',
          svgIcon: shallowRef(SearchIcon),
          click: () => {
            if (props.stationId) {
              refreshData()
            } else {
              $messageWarning('请选择监测点')
            }
          }
        },
        {
          perm: true,
          text: '导出',
          type: 'warning',
          svgIcon: shallowRef(Download),
          hide: () => { return state.activeName !== 'list' },
          click: () => {
            refCard.value?.exportTable()
          }
        }
      ]
    }
  ]
})

// 初始化列表配置数据
const cardTableConfig = reactive<ITable>({
  loading: false,
  dataList: [],
  columns: [],
  operations: [],
  showSummary: false,
  operationWidth: '150px',
  pagination: {
    hide: true
  }
})

// 配置加载图表数据
const refuseChart = () => {
  const chartOption = lineOption()
  chartOption.series = []
  const nc = cardTableConfig.columns.filter(column => column.prop !== 'ts')
  nc.map(n => {
    const newData = state.tableDataList.map(chart => {
      if (n.prop.indexOf('Ts') !== -1) {
        return dayjs(chart[n.prop]).format('HH:00')
      }
      return chart[n.prop]
    })
    const serie = {
      name: n.label,
      smooth: true,
      data: newData,
      type: 'line',
      markPoint: {
        data: [
          { type: 'max', name: '最大值' },
          { type: 'min', name: '最小值' }
        ]
      },
      markLine: {
        data: [{ type: 'average', name: '平均值' }]
      }
    }
    chartOption.series.push(serie)
    chartOption.name = '流量数据(m³)'
  })

  chartOption.yAxis[0].name = '流量(m³)'
  chartOption.xAxis.data = state.tableDataList.map(table => table.ts)

  refChart.value?.clear()
  nextTick(() => {
    erd.listenTo(agriEcoDev.value, () => {
      state.chartOption = chartOption
      refChart.value?.resize()
    })
  })
}

const refreshData = async () => {
  loading.value = true
  const query = cardSearch.value?.queryParams || {}
  const [start, end] = query.queryType === 'day' ? query.day : query.month
  const params = {
    start: start ? dayjs(start).startOf(query.queryType).valueOf() : null,
    end: end ? dayjs(end).endOf(query.queryType).valueOf() : null,
    stationId: props.stationId as string,
    queryType: query.queryType
  }
  const res = await getFlowPeriod(params)
  const data = res.data?.data
  const columns:any = [{ prop: 'ts', label: '时间', width: '130px' }]
  const nData:any = []
  for (const key in data) {
    columns.push({ prop: key, label: key, unit: '(m³)' })
    for (const i in data[key]) {
      console.log(i)
      if (nData[i]) {
        nData[i].ts = query.queryType === 'day' ? i + '时' : i + '日'
      } else {
        nData.push({ ts: query.queryType === 'day' ? i + '时' : i + '日' })
      }
      nData[i][key] = data[key][i].value
    }
  }
  cardTableConfig.columns = columns
  cardTableConfig.dataList = nData
  state.tableDataList = nData
  refuseChart()
  loading.value = false
}

onMounted(() => {
  if (props.stationId) {
    refreshData()
  }
})

</script>
<style lang="scss" scoped>
.view{
  height: 100%;
}

.card-ehcarts {
  width: 100%;
  height: 100%;
}

.card-table{
  height: calc(100% - 100px);
  margin-top: 10px;
}
</style>
