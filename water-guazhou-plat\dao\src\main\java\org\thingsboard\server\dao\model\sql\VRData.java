package org.thingsboard.server.dao.model.sql;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;

/**
 * 高新污水厂VR
 */
@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@TableName("tb_vr_data")
@NoArgsConstructor
@AllArgsConstructor
public class VRData {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    private String data;

    private String tenantId;

}
