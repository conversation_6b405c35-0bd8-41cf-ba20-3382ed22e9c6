<template>
  <DialogForm
    ref="refDialog"
    :config="DialogFormConfig"
  >
    <Search
      ref="refSearch"
      :config="SearchConfig"
      style="margin-bottom: 20px"
    ></Search>
    <FormTable :config="TableConfig"></FormTable>
  </DialogForm>
</template>
<script lang="ts" setup>
import { DeleteGisScheme, GetGisSchemeList } from '@/api/mapservice/scheme'
import { IDialogFormIns, ISearchIns } from '@/components/type'
import { formatterDateTime } from '@/utils/GlobalHelper'
import { SLConfirm, SLMessage } from '@/utils/Message'

const props = defineProps<{ type: ISchemeType }>()
const emit = defineEmits(['row-click'])
const SearchConfig = reactive<ISearch>({
  filters: [{ type: 'input', field: 'name', onChange: () => refreshData() }],
  operations: [
    {
      type: 'btn-group',
      btns: [
        { perm: true, text: '查询', iconifyIcon: 'ep:search', click: () => refreshData() },
        { perm: true, text: '重置', iconifyIcon: 'ep:refresh', click: () => resetForm() }
      ]
    }
  ],
  defaultParams: {}
})
const refreshData = async () => {
  if (!props.type) {
    TableConfig.dataList = []
    TableConfig.pagination.total = 0
    TableConfig.pagination.page = 1
    return
  }
  try {
    const params = {
      ...(refSearch.value?.queryParams || {}),
      type: props.type,
      page: TableConfig.pagination.page || 1,
      size: TableConfig.pagination.limit || 20
    }
    const res = await GetGisSchemeList(params)
    TableConfig.dataList = res.data.data.data || []
    TableConfig.pagination.total = res.data.data.total || 0
  } catch (error) {
    console.log(error)
  }
}
const refSearch = ref<ISearchIns>()
const resetForm = () => {
  refSearch.value?.resetForm()
}

const DialogFormConfig = reactive<IDialogFormConfig>({
  title: '方案管理',
  dialogWidth: 700,
  group: [],
  labelPosition: 'right',
  labelWidth: '100px',
  defaultValue: {},
  cancel: false,
  model: false,
  draggable: true,
  closeOnPressEscape: true
})
const refDialog = ref<IDialogFormIns>()
const openDialog = () => {
  refDialog.value?.openDialog()
  refreshData()
}
const closeDialog = () => {
  refDialog.value?.closeDialog()
}
const TableConfig = reactive<ITable>({
  indexVisible: true,
  height: 400,
  columns: [
    { label: '方案名称', prop: 'name' },
    {
      label: '创建时间',
      prop: 'createTime',
      formatter(row, value) {
        return moment(value).format(formatterDateTime)
      }
    },
    {
      label: '备注',
      prop: 'remark'
    }
  ],
  dataList: [],
  operations: [
    { perm: true, text: '使用', click: row => handleRowClick(row) },
    { perm: true, text: '删除', type: 'danger', click: row => handleRemove(row) }
  ],
  pagination: {
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page || 1
      TableConfig.pagination.limit = size || 20
      refreshData()
    }
  }
})
const handleRowClick = (row: any) => {
  TableConfig.currentRow = row
  emit('row-click', row)
}
const handleRemove = (row: any) => {
  SLConfirm('确定删除?', '提示信息')
    .then(async () => {
      try {
        const ids = row.id ? [row.id] : []
        if (!ids.length) {
          SLMessage.error('请先选择要删除的数据')
          return
        }
        const res = await DeleteGisScheme(ids)
        if (res.data.code === 200) {
          SLMessage.success('删除成功')
          refreshData()
        } else {
          SLMessage.error(res.data.message)
        }
      } catch (error) {
        SLMessage.error('删除失败')
        console.log(error)
      }
    })
    .catch(() => {
      //
    })
}
defineExpose({
  openDialog,
  closeDialog
})
</script>
<style lang="scss" scoped></style>
