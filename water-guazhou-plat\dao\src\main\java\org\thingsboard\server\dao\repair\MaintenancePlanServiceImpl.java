package org.thingsboard.server.dao.repair;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.Device;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.User;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.device.DeviceService;
import org.thingsboard.server.dao.model.DTO.TriggerDTO;
import org.thingsboard.server.dao.model.sql.*;
import org.thingsboard.server.dao.model.sql.shuiwu.assets.AssetsAccountEntity;
import org.thingsboard.server.dao.project.ProjectService;
import org.thingsboard.server.dao.shuiwu.assets.AssetsAccountService;
import org.thingsboard.server.dao.sql.repair.MaintenancePlanCRepository;
import org.thingsboard.server.dao.sql.repair.MaintenancePlanRepository;
import org.thingsboard.server.dao.sql.repair.MaintenancePlanTriggerRepository;

import java.text.SimpleDateFormat;
import java.util.*;

@Slf4j
@Service
public class MaintenancePlanServiceImpl implements MaintenancePlanService {

    @Autowired
    private MaintenancePlanRepository maintenancePlanRepository;
    @Autowired
    private MaintenancePlanCRepository maintenancePlanCRepository;
    @Autowired
    private DeviceService deviceService;
    @Autowired
    private MaintenanceStandardService maintenanceStandardService;
    @Autowired
    private ProjectService projectService;
    @Autowired
    private MaintenancePlanTriggerRepository maintenancePlanTriggerRepository;
    @Autowired
    private MaintenanceJobService maintenanceJobService;
    @Autowired
    private AssetsAccountService assetsAccountService;


    @Override
    public MaintenancePlanEntity detail(String id, User currentUser) {
        // 查询主表
        MaintenancePlanEntity plan = maintenancePlanRepository.findOne(id);

        if (plan != null) {
            TenantId tenantId = new TenantId(UUIDConverter.fromString(plan.getTenantId()));
            // 查询项目列表
            List<ProjectEntity> projectList = projectService.findByTenantId(tenantId);
            Map<String, ProjectEntity> projectMap = new HashMap<>();
            if (projectList != null && projectList.size() > 0) {
                projectList.forEach(p -> projectMap.put(p.getId(), p));
            }

            // 查询设备列表
            List<AssetsAccountEntity> assetsAccountList = assetsAccountService.findByTenantId(UUIDConverter.fromTimeUUID(tenantId.getId()));
            Map<String, AssetsAccountEntity> assetsAccountMap = new HashMap<>();
            if (assetsAccountList != null && assetsAccountList.size() > 0) {
                assetsAccountList.forEach(d -> assetsAccountMap.put(d.getId(), d));
            }

            // 查询检修项目列表
            List<MaintenanceStandardEntity> maintenanceStandardList = maintenanceStandardService.findAll("", currentUser.getTenantId());
            Map<String, MaintenanceStandardEntity> maintenanceStandardMap = new HashMap<>();
            if (maintenanceStandardList != null && maintenanceStandardList.size() > 0) {
                maintenanceStandardList.forEach(r -> maintenanceStandardMap.put(r.getId(), r));
            }

            if ("2".equals(plan.getType())) {// 触发性计划
                MaintenancePlanTriggerEntity trigger = maintenancePlanTriggerRepository.findByMainId(id);

                ProjectEntity project = projectMap.get(trigger.getProjectId());
                if (project != null) {
                    trigger.setProjectName(project.getName());
                }

                AssetsAccountEntity assetsAccountEntity = assetsAccountMap.get(trigger.getDeviceId());
                if (assetsAccountEntity != null) {
                    trigger.setDeviceName(assetsAccountEntity.getDeviceName());
                }
                String detail = trigger.getDetail();
                trigger.setTriggerList(JSON.parseArray(detail, TriggerDTO.class));
                // 查询子表
                MaintenanceStandardEntity maintenanceStandard = maintenanceStandardMap.get(trigger.getStandardId());
                if (maintenanceStandard != null) {
                    trigger.setStandardName(maintenanceStandard.getName());
                }

                plan.setTrigger(trigger);
            } else {// 固定日期和预防性计划
                // 查询子表
                List<MaintenancePlanCEntity> childList = maintenancePlanCRepository.findByMainIdOrderByOrderNumber(id);
                for (MaintenancePlanCEntity child : childList) {
                    ProjectEntity project = projectMap.get(child.getProjectId());
                    if (project != null) {
                        child.setProjectName(project.getName());
                    }

                    AssetsAccountEntity assetsAccountEntity = assetsAccountMap.get(child.getDeviceId());
                    if (assetsAccountEntity != null) {
                        child.setDeviceName(assetsAccountEntity.getDeviceName());
                    }

                    MaintenanceStandardEntity maintenanceStandard = maintenanceStandardMap.get(child.getStandardId());
                    if (maintenanceStandard != null) {
                        child.setStandardName(maintenanceStandard.getName());
                    }
                }
                plan.setJobList(childList);
            }

        }

        return plan;
    }

    @Override
    public PageData<MaintenancePlanEntity> findList(int page, int size, String name, User currentUser) {
        // 分页参数
        PageRequest pageable = new PageRequest(page - 1, size, Sort.Direction.DESC, "createTime");

        Page<MaintenancePlanEntity> pageResult = maintenancePlanRepository.findList(name, UUIDConverter.fromTimeUUID(currentUser.getTenantId().getId()), pageable);

        return new PageData<>(pageResult.getTotalElements(), pageResult.getContent());
    }

    @Override
    public MaintenancePlanEntity savePlan(MaintenancePlanEntity entity, User currentUser) {
        // 保存主表
        entity.setCreator(currentUser.getFirstName());
        entity.setCreateTime(new Date());
        entity.setTenantId(UUIDConverter.fromTimeUUID(currentUser.getTenantId().getId()));

        MaintenancePlanEntity save = maintenancePlanRepository.save(entity);
        entity.setId(save.getId());

        // 保存子表
        if ("2".equals(entity.getType())) {
            MaintenancePlanTriggerEntity trigger = entity.getTrigger();
            List<TriggerDTO> triggerList = trigger.getTriggerList();
            trigger.setDetail(JSON.toJSONString(triggerList));

            trigger.setCreateTime(entity.getCreateTime());
            trigger.setTenantId(entity.getTenantId());
            trigger.setMainId(entity.getId());

            maintenancePlanTriggerRepository.save(trigger);
        } else {
            List<MaintenancePlanCEntity> jobList = entity.getJobList();
            for (MaintenancePlanCEntity planC : jobList) {
                planC.setMainId(entity.getId());
                planC.setTenantId(entity.getTenantId());
                planC.setCreateTime(entity.getCreateTime());
            }
            entity.setJobList(maintenancePlanCRepository.save(jobList));

            // 生成检修任务
            this.executePlan(entity, true);
        }

        return entity;
    }

    /**
     * 执行计划生成检修任务
     *
     * @param entity 计划
     * @param create 是否为新建的计划
     */
    @Override
    @Transactional
    public void executePlan(MaintenancePlanEntity entity, boolean create) {
        // 生成检修任务
        List<MaintenancePlanCEntity> jobList = entity.getJobList();
        if (!create) {// 非新建需要查询出任务详情
            jobList = maintenancePlanCRepository.findByMainIdOrderByOrderNumber(entity.getId());
        }

        // 判断是否到达时间
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

        String executeTime = dateFormat.format(entity.getExecuteTime());
        String now = dateFormat.format(new Date());
        if (executeTime.equals(now)) {// 到达时间生成
            if ("1".equals(entity.getStatus())) {// 判断是否启用计划
                // 启用, 生成任务
                buildMaintenanceTask(entity, jobList);

            } else {
                // 停用
            }

            // 变更下一次执行时间
            if ("3".equals(entity.getType()) && entity.getPeriodTime() != null && entity.getPeriodTime() != 0) {
                Date nextExecuteTime = new Date(entity.getExecuteTime().getTime() + (entity.getPeriodTime() * 24 * 60 * 60 * 1000));
                entity.setExecuteTime(nextExecuteTime);
            }

            // 保存计划
            maintenancePlanRepository.save(entity);
        }

    }

    private void buildMaintenanceTask(MaintenancePlanEntity plan, List<MaintenancePlanCEntity> jobList) {
        // 生成维修任务主表
        MaintenanceJobEntity job = new MaintenanceJobEntity();
        job.setName(plan.getName());
        job.setType(plan.getType());
        job.setStatus("1");// 未开始
        job.setExecuteTime(plan.getExecuteTime());
        job.setCreateTime(new Date());
        job.setCreator(plan.getCreator());
        job.setTenantId(plan.getTenantId());

        job = maintenanceJobService.save(job);
        // 生成子表记录
        if (jobList != null) {

            // 查询标准列表
            List<MaintenanceStandardEntity> standardList = maintenanceStandardService.findAll("", plan.getTenantId());
            Map<String, MaintenanceStandardEntity> standardMap = new HashMap<>();
            if (standardList != null) {
                standardList.forEach(s -> standardMap.put(s.getId(), s));
            }

            List<MaintenanceJobCEntity> childList = new ArrayList<>();
            for (MaintenancePlanCEntity planChild : jobList) {
                MaintenanceJobCEntity jobC = new MaintenanceJobCEntity();
                jobC.setMainId(job.getId());
                jobC.setProjectId(planChild.getProjectId());
                jobC.setDeviceId(planChild.getDeviceId());
                jobC.setOrderNumber(planChild.getOrderNumber());
                jobC.setStatus("1");// 未开始

                MaintenanceStandardEntity standard = standardMap.get(planChild.getStandardId());
                if (standard != null) {
                    jobC.setStandardName(standard.getName());
                    jobC.setStandardDetail(planChild.getStandardId());
                }

                jobC.setCreateTime(job.getCreateTime());
                jobC.setTenantId(job.getTenantId());

                childList.add(jobC);
            }
            maintenanceJobService.save(childList);
        }
    }

    @Override
    public void remove(List<String> ids) {
        // 删除检修计划
        for (String id : ids) {
            // 删除主表
            maintenancePlanRepository.delete(id);

            // 删除子表
            maintenancePlanCRepository.removeByMainId(id);
        }
    }

    @Override
    public void changeStatus(String id) {
        MaintenancePlanEntity entity = maintenancePlanRepository.findOne(id);
        String status = entity.getStatus();
        if ("1".equals(status)) {
            entity.setStatus("2");// 停用
        }
        if ("2".equals(status)) {
            entity.setStatus("1");// 启用
        }

        maintenancePlanRepository.save(entity);
    }

    @Override
    public List<MaintenancePlanEntity> findPlanByType(String type) {
        List<MaintenancePlanEntity> list = maintenancePlanRepository.findByType(type);
        if (list != null) {
            list = new ArrayList<>();
        }

        return list;
    }

    @Override
    public void buildTriggerJob(MaintenancePlanEntity plan) {
        // 生成维修任务主表
        MaintenanceJobEntity job = new MaintenanceJobEntity();
        job.setName(plan.getName());
        job.setType(plan.getType());
        job.setStatus("1");// 未开始
        job.setExecuteTime(plan.getExecuteTime());
        job.setCreateTime(new Date());
        job.setCreator(plan.getCreator());
        job.setTenantId(plan.getTenantId());

        job = maintenanceJobService.save(job);

        MaintenancePlanTriggerEntity trigger = plan.getTrigger();
        // 生成子表记录
        if (trigger != null) {
            MaintenanceJobTriggerEntity jobC = new MaintenanceJobTriggerEntity();
            jobC.setMainId(job.getId());
            jobC.setProjectId(trigger.getProjectId());
            jobC.setDeviceId(trigger.getDeviceId());
            jobC.setDetail(trigger.getDetail());
            jobC.setCreateTime(job.getCreateTime());
            jobC.setTenantId(job.getTenantId());

            maintenanceJobService.save(jobC);
        }
    }
}
