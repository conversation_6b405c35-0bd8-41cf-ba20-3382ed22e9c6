package org.thingsboard.server.dao.gis;

import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.model.sql.gis.GisPipeAdditionalInfo;

public interface GisPipeAdditionalInfoService {

    /**
     * 按照图层ID和ObjectId查询管网的附加信息
     *
     * @param layerid  图层ID
     * @param objectid object id
     * @param tenantId 租户ID
     * @return 数据
     */
    GisPipeAdditionalInfo findOne(String layerid, String objectid, TenantId tenantId);

    /**
     * 保存管网附加信息
     *
     * @param entity 管网附加信息
     */
    void save(GisPipeAdditionalInfo entity);
}
