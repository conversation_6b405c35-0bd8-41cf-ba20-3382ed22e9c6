package org.thingsboard.server.dao.logicalFlow;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.User;
import org.thingsboard.server.common.data.exception.ThingsboardErrorCode;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.dao.logicalFlow.BO.LogicalFlowNodeTreeVO;
import org.thingsboard.server.dao.model.sql.LogicalFlow;
import org.thingsboard.server.dao.model.sql.LogicalFlowNode;
import org.thingsboard.server.dao.project.ProjectRelationService;
import org.thingsboard.server.dao.sql.logicalFlow.*;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@Transactional
public class LogicalFlowServiceImpl implements LogicalFlowService {

    @Autowired
    private LogicalFlowRepository logicalFlowRepository;

    @Autowired
    private LogicalFlowNodeRepository logicalFlowNodeRepository;

    @Autowired
    private LogicalFlowHistoryRepository logicalFlowHistoryRepository;

    @Autowired
    private LogicalFlowNodeHistoryRepository logicalFlowNodeHistoryRepository;

    @Autowired
    private LogicalFlowAlarmNodeHistoryRepository logicalFlowAlarmNodeHistoryRepository;

    @Autowired
    private ProjectRelationService projectRelationService;

    @Override
    public LogicalFlow save(LogicalFlow logicalFlow) {
        LogicalFlowNode logicalFlowNode = null;
        if (logicalFlow.getId() == null) {
            // 创建开始节点
            logicalFlowNode = new LogicalFlowNode();
            logicalFlowNode.setScript("1 == 1");
            logicalFlowNode.setOrder(0);
            logicalFlowNode.setType(DataConstants.LogicalFlowNodeType.SIMPLE.name());
            logicalFlowNode.setName("开始");
            logicalFlowNode.setTenantId(logicalFlow.getTenantId());
            logicalFlowNode.setCreateTime(logicalFlow.getCreateTime());
            logicalFlowNode.setParentId(DataConstants.LOGICAL_FLOW_NODE_ROOT_PARENT_ID);
        }
        LogicalFlow save = logicalFlowRepository.save(logicalFlow);
        if (logicalFlowNode != null) {
            logicalFlowNode.setLogicalFlowId(save.getId());
            logicalFlowNodeRepository.save(logicalFlowNode);
        }

        return save;
    }

    @Override
    public LogicalFlow edit(LogicalFlow logicalFlow) {
        // 查询
        LogicalFlow _logicalFlow = logicalFlowRepository.findOne(logicalFlow.getId());
        if (_logicalFlow != null) {
            _logicalFlow.setName(logicalFlow.getName());
            _logicalFlow.setRemark(logicalFlow.getRemark());
            _logicalFlow.setType(logicalFlow.getType());

            _logicalFlow = logicalFlowRepository.save(_logicalFlow);
        }
        return _logicalFlow;
    }

    @Override
    public LogicalFlow findById(String id) {
        return logicalFlowRepository.findOne(id);
    }

    @Override
    public List<LogicalFlow> findList(String type) {
        Sort orders = new Sort(new Sort.Order(Sort.Direction.DESC, "createTime"));
        return logicalFlowRepository.findByTypeAndParentId(type, "0", orders);
    }

    @Override
    public void deleteById(String id) {
        LogicalFlow one = logicalFlowRepository.findOne(id);
        if (one != null) {
            // 删除流程
            logicalFlowRepository.delete(one);
            List<LogicalFlow> childLogicalFlow = logicalFlowRepository.findByParentIdOrderByCreateTime(one.getId());
            logicalFlowRepository.deleteInBatch(childLogicalFlow);

            // 删除流程节点
            logicalFlowNodeRepository.deleteByLogicalFlowId(id);

            // 删除执行历史
            logicalFlowHistoryRepository.deleteByLogicalFlowId(id);
            logicalFlowAlarmNodeHistoryRepository.deleteByLogicalFlowId(id);
            logicalFlowNodeHistoryRepository.deleteByLogicalFlowId(id);

            // 删除子流程的相关数据
            childLogicalFlow.forEach(child -> {
                logicalFlowNodeRepository.deleteByLogicalFlowId(child.getId());
                logicalFlowHistoryRepository.deleteByLogicalFlowId(child.getId());
                logicalFlowAlarmNodeHistoryRepository.deleteByLogicalFlowId(child.getId());
                logicalFlowNodeHistoryRepository.deleteByLogicalFlowId(child.getId());
            });
        }


    }

    @Override
    public List<LogicalFlow> findList(String type, String projectId) {
        if (DataConstants.TriggerType.CLOUD.name().equals(type)) {
            return logicalFlowRepository.findByTypeAndProjectId(type, projectId);
        } else if (DataConstants.TriggerType.LOCAL.name().equals(type)) {
            return logicalFlowRepository.findByTypeAndGatewayIdAndParentIdOrderByCreateTimeDesc(type, projectId, "0");
        }
        return new ArrayList<>();
    }

    @Override
    public LogicalFlow save(String type, String id, LogicalFlow logicalFlow) {
        // 判断逻辑流程类型
        if (DataConstants.TriggerType.CLOUD.name().equals(type)) {
            logicalFlow = this.save(logicalFlow);
            // 保存逻辑流程和项目的关系
            projectRelationService
                    .mountEntityToProject(DataConstants.ProjectRelationEntityType.LOGICAL_FLOW.name(), id, Collections.singletonList(logicalFlow.getId()));

        } else if (DataConstants.TriggerType.LOCAL.name().equals(type)) {
            // 保存主机ID到逻辑流程
            logicalFlow.setGatewayId(id);
            logicalFlow = this.save(logicalFlow);
        }
        return logicalFlow;
    }

    @Override
    public LogicalFlowNode findLogicalFlowNodeById(String id) {
        return logicalFlowNodeRepository.findOne(id);
    }

    @Override
    public LogicalFlowNode deleteLogicalFlowNodeById(String id) {
        LogicalFlowNode _node = this.findLogicalFlowNodeById(id);
        if (_node != null) {
            logicalFlowNodeRepository.delete(id);
        }

        return _node;
    }

    @Override
    public List<LogicalFlowNode> findLogicalFlowNodeByLogicalFlowId(String logicalFlowId) {
        return logicalFlowNodeRepository.findByLogicalFlowId(logicalFlowId);
    }

    @Override
    @Transactional
    public LogicalFlowNode saveLogicalFlowNode(LogicalFlowNode logicalFlowNode) throws ThingsboardException {
        // 保存更新时间
        if (StringUtils.isBlank(logicalFlowNode.getLogicalFlowId())) {
            throw new ThingsboardException(ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }
        LogicalFlow logicalFlow = logicalFlowRepository.findOne(logicalFlowNode.getLogicalFlowId());
        if (logicalFlow == null) {
            throw new ThingsboardException("没有查询到该节点所属的逻辑流程!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }
        logicalFlow.setUpdateTime(System.currentTimeMillis());
        logicalFlowRepository.save(logicalFlow);

        if (StringUtils.isBlank(logicalFlowNode.getId())) {// 新增
            logicalFlowNode.setCreateTime(System.currentTimeMillis());
            return logicalFlowNodeRepository.save(logicalFlowNode);
        } else {// 修改
            // 查询
            LogicalFlowNode _node = this.findLogicalFlowNodeById(logicalFlowNode.getId());
            if (_node != null) {
                _node.setName(logicalFlowNode.getName());
                _node.setParam(logicalFlowNode.getParam());
                _node.setType(logicalFlowNode.getType());
                _node.setOrder(logicalFlowNode.getOrder());
                _node.setScript(logicalFlowNode.getScript());
                _node.setRemark(logicalFlowNode.getRemark());
                _node.setAdditionalInfo(logicalFlowNode.getAdditionalInfo());

                return logicalFlowNodeRepository.save(_node);
            }
        }
        return null;
    }

    @Override
    public List<LogicalFlow> findListByParentId(String parentId) {
        return logicalFlowRepository.findByParentIdOrderByCreateTime(parentId);
    }

    @Override
    public LogicalFlowNodeTreeVO findTreeByLogicalFlowId(String logicalFlowId) {
        List<LogicalFlowNodeTreeVO> nodeTreeVOS = this.findLogicalFlowNodeByLogicalFlowId(logicalFlowId).stream()
                .map(LogicalFlowNodeTreeVO::new)
                .collect(Collectors.toList());

        // 按parentId分组
        Map<String, List<LogicalFlowNodeTreeVO>> nodeTreeVoMap = new HashMap<>();
        nodeTreeVOS.forEach(vo -> {
            List<LogicalFlowNodeTreeVO> list = new ArrayList<>();
            if (nodeTreeVoMap.containsKey(vo.getParentId())) {
                list = nodeTreeVoMap.get(vo.getParentId());
            }
            list.add(vo);
            nodeTreeVoMap.put(vo.getParentId(), list);
        });

        return buildLogicalFlowNodeTree(nodeTreeVoMap);
    }

    @Override
    public void forceStop(String id) {
        LogicalFlow logicalFlow = logicalFlowRepository.findOne(id);
        if (logicalFlow != null) {
            logicalFlow.setStopForce("1");
            logicalFlow.setStatus(DataConstants.LOGICAL_FLOW_DISABLE);
            logicalFlowRepository.save(logicalFlow);
        }
    }

    @Override
    @Transactional
    public void importLogicalFlow(JSONObject logicalFlowJson, User currentUser, String type, String id) throws ThingsboardException {
        Date date = new Date();
        LogicalFlow logicalFlow = logicalFlowJson.toJavaObject(LogicalFlow.class);
        logicalFlow.setId(null);
        logicalFlow.setCreateTime(date.getTime());
        logicalFlow.setUpdateTime(date.getTime());
        logicalFlow.setCreater(currentUser.getLastName());
        LogicalFlow save = this.save(type, id, logicalFlow);

        // 保存逻辑流程节点
        Map<String, String> idMap = new HashMap<>();
        List<LogicalFlowNode> list = new ArrayList<>();
        logicalFlowJson.getJSONArray("nodeList").toJavaList(LogicalFlowNode.class).forEach(node -> {
            String nodeId = node.getId();
            node.setLogicalFlowId(save.getId());
            node.setId(null);
            LogicalFlowNode flowNode = logicalFlowNodeRepository.save(node);
            idMap.put(nodeId, flowNode.getId());
            list.add(flowNode);
        });
        // 设置正确的parent id
        for (LogicalFlowNode logicalFlowNode : list) {
            if (!"0".equals(logicalFlowNode.getParentId())) {
                logicalFlowNode.setParentId(idMap.get(logicalFlowNode.getParentId()));
            }
        }
        // 保存list
        logicalFlowNodeRepository.save(list);
    }

    @Override
    public List<LogicalFlow> findAll(String type, String projectId) {
        if (DataConstants.TriggerType.CLOUD.name().equals(type)) {
            return logicalFlowRepository.findAllByTypeAndProjectId(type, projectId);
        } else if (DataConstants.TriggerType.LOCAL.name().equals(type)) {
            return logicalFlowRepository.findByTypeAndGatewayIdOrderByCreateTimeDesc(type, projectId);
        }
        return new ArrayList<>();
    }

    /**
     * 生成树结构
     *
     * @param nodeTreeVoMap
     * @return
     */
    private LogicalFlowNodeTreeVO buildLogicalFlowNodeTree(Map<String, List<LogicalFlowNodeTreeVO>> nodeTreeVoMap) {
        List<LogicalFlowNodeTreeVO> vos = nodeTreeVoMap.get("0");
        LogicalFlowNodeTreeVO rootNode = vos.get(0);

        // 遍历树
        recursive(rootNode, nodeTreeVoMap);

        return rootNode;
    }

    /**
     * 深度遍历
     *
     * @param rootNode
     * @param nodeTreeVoMap
     */
    private void recursive(LogicalFlowNodeTreeVO rootNode, Map<String, List<LogicalFlowNodeTreeVO>> nodeTreeVoMap) {
        List<LogicalFlowNodeTreeVO> childList = nodeTreeVoMap.get(rootNode.getId());
        if (childList != null) {
            // 设置当前父节点的子节点
            rootNode.setChildren(childList.stream().sorted(Comparator.comparing(LogicalFlowNodeTreeVO::getCreateTime)).collect(Collectors.toList()));
            for (LogicalFlowNodeTreeVO child: childList) {
                recursive(child, nodeTreeVoMap); // 递归调用
            }
        }
    }
}
