package org.thingsboard.server.dao.store;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.dao.model.sql.store.DeviceStorageJournal;
import org.thingsboard.server.dao.model.sql.store.StoreInRecord;
import org.thingsboard.server.dao.model.sql.store.StoreInRecordDetail;
import org.thingsboard.server.dao.sql.department.StoreInRecordDetailMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.store.StoreInRecordDetailPageRequest;
import org.thingsboard.server.dao.util.imodel.query.store.StoreInRecordDetailSaveRequest;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class StoreInRecordDetailServiceImpl implements StoreInRecordDetailService {
    @Autowired
    private StoreInRecordDetailMapper mapper;

    @Autowired
    private DeviceStorageJournalService journalService;


    @Override
    public IPage<StoreInRecordDetail> findAllConditional(StoreInRecordDetailPageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    @Transactional
    public List<StoreInRecordDetail> save(List<StoreInRecordDetailSaveRequest> details, StoreInRecord record) {
        if (record == null) {
            return Collections.emptyList();
            // if (details == null || details.size() == 0) {
            //     return Collections.emptyList();
            // }
            // List<StoreInRecordDetail> result = new ArrayList<>();
            // for (StoreInRecordDetailSaveRequest detail : details) {
            //     StoreInRecord main = storeInRecordMapper.selectById(detail.getMainId());
            //     if (detail.getId() == null) {
            //         DeviceStorageJournal journal = detail.toDeviceStorageJournal(main);
            //         journalService.save(journal);
            //     }
            //     StoreInRecordDetail item = QueryUtil.saveOrUpdateOneByRequest(detail, mapper);
            //     result.add(item);
            // }
            // return result;
        }

        // mapper.removeAllByMainId(record.getId());
        List<DeviceStorageJournal> journals = details.stream().filter(x -> x.getId() == null).map(x -> {
                    // TODO: 可以考虑优化，需要新的数据结构 Collections.nCopies(x.getNum(), x.toDeviceStorageJournal(record));
                    return Stream.generate(() -> x.toDeviceStorageJournal(record)).limit(x.getNum()).collect(Collectors.toList());
                })
                .flatMap(Collection::stream).collect(Collectors.toList());

        List<StoreInRecordDetail> storeInRecordDetails = QueryUtil.saveOrUpdateBatchByRequest(details, mapper::saveAll, mapper::saveAll);
        journalService.saveAll(journals);
        return storeInRecordDetails;
    }

    @Override
    public boolean update(StoreInRecordDetail entity) {
        return mapper.update(entity);
    }

    @Override
    public boolean delete(String id) {
        // TODO: [LFT] 若要删除则需要考虑是否删除对应的台账信息
        return mapper.deleteById(id) > 0;
    }

    @Override
    public boolean deleteAll(List<String> idList) {
        // TODO: [LFT] 若要删除则需要考虑是否删除对应的台账信息
        return QueryUtil.deleteBatch(idList, mapper::deleteBatchIds);
    }

}
