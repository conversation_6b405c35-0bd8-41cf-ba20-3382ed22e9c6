export const tableColumns: any = () => {
  return [
    {
      prop: 'partitionName',
      label: '分区名称',
      align: 'center',
      fixed: 'left',
      minWidth: 160
    },
    {
      prop: 'copyMeterType',
      label: '抄表类型',
      align: 'center',
      minWidth: 120
    },
    {
      label: '夜间最小流',
      align: 'center',
      subColumns: [
        {
          prop: 'nightFlowMin',
          label: '最小值',
          unit: '(m³/h)',
          align: 'center',
          minWidth: 120
        },
        {
          prop: 'nightFlowMax',
          label: '最大值',
          unit: '(m³/h)',
          align: 'center',
          minWidth: 120
        }
      ]
    },
    {
      label: '夜间最小值',
      align: 'center',
      subColumns: [
        {
          prop: 'nightValueMin',
          label: '最小值',
          unit: '(m³/h)',
          align: 'center',
          minWidth: 120
        },
        {
          prop: 'nightValueMax',
          label: '最大值',
          unit: '(m³/h)',
          align: 'center',
          minWidth: 120
        }
      ]
    },
    {
      label: '单位管长夜间流量',
      align: 'center',
      subColumns: [
        {
          prop: 'unitPipeNightFlowMin',
          label: '最小值',
          unit: '(m³/h)',
          align: 'center',
          minWidth: 120
        },
        {
          prop: 'unitPipeNightFlowMax',
          label: '最大值',
          unit: '(m³/h)',
          align: 'center',
          minWidth: 120
        }
      ]
    },
    {
      label: 'MNF/日均小时流量',
      align: 'center',
      subColumns: [
        {
          prop: 'mnfDivDayAvgHourFlowMin',
          label: '最小值',
          unit: '(m³/h)',
          align: 'center',
          minWidth: 120
        },
        {
          prop: 'mnfDivDayAvgHourFlowMax',
          label: '最大值',
          unit: '(m³/h)',
          align: 'center',
          minWidth: 120
        }
      ]
    },
    {
      prop: 'incrBase',
      label: '基准值',
      unit: '(m³/h)',
      align: 'center',
      minWidth: 120
    },
    {
      prop: 'incrWarn',
      label: '黄色预警值',
      align: 'center',
      minWidth: 120
    },
    {
      prop: 'incrError',
      label: '红色预警值',
      align: 'center',
      minWidth: 120
    },
    {
      label: '是否默认',
      align: 'center',
      subColumns: [
        {
          prop: 'stockType',
          label: '存量',
          align: 'center',
          minWidth: 120
        },
        {
          prop: 'incrType',
          label: '增量',
          align: 'center',
          minWidth: 120
        }
      ]
    }
  ]
}
