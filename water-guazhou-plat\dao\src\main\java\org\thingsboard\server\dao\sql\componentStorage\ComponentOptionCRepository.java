package org.thingsboard.server.dao.sql.componentStorage;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.thingsboard.server.dao.model.sql.ComponentOptionCEntity;
import org.thingsboard.server.dao.model.sql.ComponentOptionEntity;

import java.util.List;

public interface ComponentOptionCRepository extends JpaRepository<ComponentOptionCEntity, String> {

    @Query("SELECT coc FROM ComponentOptionCEntity coc " +
            "WHERE coc.mainId = ?1 ORDER BY coc.orderNumber")
    List<ComponentOptionCEntity> findByMainId(String mainId);
}
