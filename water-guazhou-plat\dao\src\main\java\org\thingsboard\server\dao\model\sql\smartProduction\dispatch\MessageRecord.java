package org.thingsboard.server.dao.model.sql.smartProduction.dispatch;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.sql.smartProduction.dispatch.EmergencyUserMapper;
import org.thingsboard.server.dao.util.imodel.response.ResponseMap;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseUsername;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;
import org.thingsboard.server.dao.util.imodel.response.model.JdbcHelper;

import java.util.Date;

@Getter
@Setter
@ResponseEntity
@TableName("sp_message_record")
public class MessageRecord {
    // id
    @TableId
    private String id;

    // 接收人
    private String receiveUserId;

    // 消息内容
    private String content;

    // 接收人手机号
    private String receivePhone;

    // 发送人
    @ParseUsername(withDepartment = true, withOrganization = true)
    private String sendUserId;

    // 发送时间
    private Date sendTime;

    // 是否可靠发送
    private Boolean isReliable;

    // 短信发送状态
    private MessageRecordStatus status;

    // 创建人
    private String creator;

    // 创建时间
    private Date createTime;

    // 租户ID
    private String tenantId;

    private void customizeMap(ResponseMap map, JdbcHelper jdbc) {
        EmergencyUserMapper mapper = jdbc.getMapper(EmergencyUserMapper.class);
        String receiveUserName = mapper.getNameById(receiveUserId);
        map.put("receiveUserName", receiveUserName == null ? receiveUserId : receiveUserName);
    }

}
