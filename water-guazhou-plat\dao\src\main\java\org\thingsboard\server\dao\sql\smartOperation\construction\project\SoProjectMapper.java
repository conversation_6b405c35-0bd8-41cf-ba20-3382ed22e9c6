package org.thingsboard.server.dao.sql.smartOperation.construction.project;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoProject;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoProjectPageRequest;

@Mapper
public interface SoProjectMapper extends BaseMapper<SoProject> {
    IPage<SoProject> findByPage(SoProjectPageRequest request);

    boolean update(SoProject entity);

    int updateFully(SoProject updated);

    String generateCode(String tenantId);

    int remove(String id);

    boolean isCodeExists(@Param("code") String code,
                         @Param("tenantId") String tenantId,
                         @Param("id") String id);

    String getCodeById(String id);


    boolean canBeDelete(String id);

}
