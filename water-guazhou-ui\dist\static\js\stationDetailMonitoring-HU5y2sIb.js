import{d as ma,j as ba,M as va,a6 as fa,bF as b,r as f,c as y,D as H,a8 as ga,s as V,bB as x,am as ha,ay as _a,g as N,h as G,F as m,q as c,n as ya,p as S,i as z,an as R,c5 as ka,dt as Ta,aq as wa,bl as Ca,bm as Sa,al as aa,aj as ea,C as Oa}from"./index-r0dFAfgr.js";import{_ as Da}from"./Search-NSrhrIa_.js";import{_ as La}from"./index-C9hz-UZb.js";import{g as Ia,l as ta}from"./echart-DxEZmJvB.js";import{u as xa}from"./useStation-DJgnSZIA.js";import{g as Na,d as Ya,s as Aa}from"./zhandian-YaGuQZe6.js";import{s as Ma,a as Va}from"./headwaterMonitoring-BgK7jThW.js";const Ga=[{label:"0",value:0},{label:"1",value:1},{label:"2",value:2},{label:"3",value:3},{label:"4",value:4},{label:"5",value:5},{label:"6",value:6},{label:"7",value:7},{label:"8",value:8},{label:"9",value:9},{label:"10",value:10},{label:"11",value:11},{label:"12",value:12},{label:"13",value:13},{label:"14",value:14},{label:"15",value:15},{label:"16",value:16},{label:"17",value:17},{label:"18",value:18},{label:"19",value:19},{label:"20",value:20},{label:"21",value:21},{label:"22",value:22},{label:"23",value:23}],Ra=[[{label:"监测点名称",prop:"name",value:"-"},{label:"类型",prop:"type",value:"-"}],[{label:"安装地址",prop:"address",value:"-"},{label:"经纬度",prop:"location",value:"-"}],[{label:"瞬时流量",prop:"Instantaneous_flow",value:"-"},{label:"累计流量",prop:"total_flow",value:"-"}]],Ea=[[{label:"监测点名称",prop:"name",value:"-"},{label:"水表型号",prop:"name",value:"-"}],[{label:"RTU编号",prop:"name",value:"-"},{label:"SN编号(水表钢印号)",prop:"name",value:"-"}],[{label:"水流方向",prop:"name",value:"-"},{label:"表底数",prop:"name",value:"-"}],[{label:"表安装时间",prop:"name",value:"-"},{label:"安装位置",prop:"name",value:"-"}],[{label:"创建时间",prop:"name",value:"-"},{label:"创建人名称",prop:"name",value:"-"}],[{label:"正瞬时流量",prop:"name",value:"-"},{label:"负瞬时流量",prop:"name",value:"-"}],[{label:"正累计流量",prop:"name",value:"-"},{label:"负累计流量",prop:"name",value:"-"}],[{label:"压力",prop:"name",value:"-"},{label:"营收用户编号",prop:"name",value:"-"}],[{label:"营收用户名称",prop:"name",value:"-"},{label:"管径",prop:"name",value:"-"}],[{label:"电池电压(电压)",prop:"name",value:"-A"},{label:"回传次数",prop:"name",value:"-"}],[{label:"是否安装",prop:"name",value:"-"},{label:"信号强度",prop:"name",value:"-"}],[{label:"今日流量",prop:"name",value:"-"},{label:"昨日流量",prop:"name",value:"-"}],[{label:"本月流量",prop:"name",value:"-"},{label:"本年流量",prop:"name",value:"-"}],[{label:"累计流量倍率",prop:"name",value:"-"},{label:"厂家",prop:"name",value:"-"}],[{label:"水表状态",prop:"name",value:"-"},{label:"用水类型",prop:"name",value:"-"}],[{label:"用户类型",prop:"name",value:"-"},{label:"通讯厂商",prop:"name",value:"-"}],[{label:"通讯制式",prop:"name",value:"-"},{label:"管网信息",prop:"name",value:"-"}],[{label:"通讯卡号",prop:"name",value:"-"},{label:"备注",prop:"name",value:"-"}]],Fa={key:0,class:"content"},ja={class:"top"},Ba={class:"table-box"},Pa={class:"bottom"},Ha=ma({__name:"stationDetailMonitoring",props:{stationId:{},stationDetail:{}},emits:["hiddenLoading","update:model-value"],setup(la,{emit:oa}){const E=ba(),{$messageWarning:na}=va(),{getStationAttrGroups:U}=xa(),ra=oa,$=fa(),k=la,Y=b().date(),t=f({activeName:"status",chartOption:null,chartOption1:null,gaugeCarOption:null,searchActiveName:"list",groupTab:"",currentGroupTabs:[],attributeOptions:[],groupStation:[],rows:[],detailData:{},detailColumns:Ra,archivesTableColumns:Ea}),J=y(),Q=y(),K=y(),O=y(),F=y(),j=y(),B=y(),A=y();let T=f([]),n=f([]);const g=f({loading:!0,currentRow:[],currentRowKey:"property",highlightCurrentRow:!0,dataList:[],columns:[{prop:"propertyName",label:"检测项名称"},{prop:"value",label:"检测项数据"},{prop:"collectionTime",label:"采集时间",formatter:a=>(console.log("dddddd",a.collectionTime),a.collectionTime>0?b(a.collectionTime).format("YYYY-MM-DD HH:mm:ss"):"-")}],operations:[],pagination:{hide:!0},handleRowClick:a=>{g.currentRow=a,q()}}),h=f({loading:!0,dataList:[],indexVisible:!0,columns:[{prop:"name1",label:"报警描述"},{prop:"name2",label:"报警时间"}],operations:[],pagination:{page:1,limit:20,total:0,layout:"total, prev, pager, next, sizes, jumper",handleSize:a=>{h.pagination.limit=a},refreshData:({page:a,size:e})=>{h.pagination.page=a,h.pagination.limit=e,h.dataList=T.slice((a-1)*e,a*e)}}}),d=f({loading:!0,dataList:[],indexVisible:!0,columns:[{prop:"name1",label:"报警描述"},{prop:"name2",label:"报警时间"},{prop:"name2",label:"处理状态"}],operations:[],pagination:{page:1,limit:20,total:0,layout:"total, prev, pager, next, jumper",handleSize:a=>{d.pagination.limit=a},refreshData:({page:a,size:e})=>{var r;d.pagination.page=a,d.pagination.limit=e,d.dataList=(r=n==null?void 0:n.tableDataList)==null?void 0:r.slice((a-1)*e,a*e)}}}),w=f({defaultParams:{date:[b().date(Y-2).format("YYYY-MM-DD"),b().date(Y).format("YYYY-MM-DD")],filterStart:[0,23],group:"",attributeId:""},filters:[{type:"daterange",label:"选中日期",field:"date",clearable:!1},{label:"时间",type:"range",rangeType:"select",field:"filterStart",options:JSON.parse(JSON.stringify(Ga)),startPlaceHolder:"0时",endPlaceHolder:"23时",startOptionDisabled:(a,e)=>e&&Number(e)<a.value,endOptionDisabled:(a,e)=>e&&a.value<=Number(e)},{label:"监测组",field:"group",type:"select",clearable:!1,options:[],autoFillOptions:async a=>{const e=await U(k.stationId);a.options=e,t.groupStation=e},onChange:a=>{var r;console.log("groupStation",a);let e=t.groupStation.find(l=>l.value===a).children;console.log("options",e),e=e==null?void 0:e.map(l=>({label:l.name,value:l.id,data:H(l.deviceId)+"."+l.attr,unit:l.unit?"("+l.unit+")":""})),t.attributeOptions=e,w.defaultParams={...w.defaultParams,attributeId:e[0].id,group:a},(r=A.value)==null||r.resetForm()}},{label:"曲线类型",field:"attributeId",type:"select",clearable:!1,options:ga(()=>t.attributeOptions)}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",click:()=>{d.pagination.page=1,W()},svgIcon:V(aa)},{perm:!0,type:"warning",text:"导出",hide:()=>t.searchActiveName!=="list",svgIcon:V(ea),click:()=>{var a;(a=J.value)==null||a.exportTable()}}]}]}),sa=f({defaultParams:{date:[b().date(Y-2).format("YYYY-MM-DD"),b().date(Y).format("YYYY-MM-DD")]},filters:[{type:"daterange",label:"选择时间",field:"date",clearable:!1},{type:"btn-group",btns:[{perm:!0,text:"查询",click:()=>P(),svgIcon:V(aa)},{perm:!0,type:"warning",text:"导出",svgIcon:V(ea),click:()=>{var a;(a=K.value)==null||a.exportTable()}}]}]}),ia=f({type:"tabs",tabType:"simple",width:"100%",tabs:[{label:"当前状态",value:"status"},{label:"数据查询",value:"search"},{label:"报警信息",value:"alarm"},{label:"仪表档案",value:"archives"}],handleTabClick:a=>{console.log(a.props.name),t.activeName=a.props.name,t.activeName==="search"?(console.log(t.currentGroupTabs),x(()=>{W()})):t.activeName==="alarm"&&x(()=>{P("range")})}}),W=async()=>{var v;const a=((v=A.value)==null?void 0:v.queryParams)||{},[e,r]=a.date||[],[l,o]=a.filterStart||[],s={filterStart:l||0,filterEnd:o||23,queryType:"10m",stationId:k.stationId,group:a==null?void 0:a.group,attributeId:a==null?void 0:a.attributeId,start:e?b(e).startOf("day").valueOf():"",end:r?b(r).endOf("day").valueOf():""};Ma(s).then(i=>{var u,C,_,D,L;if(((u=i.data)==null?void 0:u.code)===200){n=(C=i.data)==null?void 0:C.data;const I=n==null?void 0:n.tableInfo.map(p=>({prop:p.columnValue,label:p.columnName,unit:p.unit?"("+p.unit+")":""}));console.log(I),d.columns=I,d.dataList=(_=n==null?void 0:n.tableDataList)==null?void 0:_.slice(0*20,20),d.pagination.total=(D=n==null?void 0:n.tableDataList)==null?void 0:D.length,d.loading=!1,ua(a==null?void 0:a.attributeId)}else d.columns=[],d.dataList=[],na((L=i.data)==null?void 0:L.message),d.loading=!1}).catch(i=>{console.log(i)})},ua=a=>{var s,v,i;const e=ta(),l=(v=((s=w.filters)==null?void 0:s.find(u=>u.field==="attributeId")).options)==null?void 0:v.find(u=>u.value===a);e.yAxis[0].name=l.label+(l.unit?l.unit:""),e.xAxis.data=n==null?void 0:n.tableDataList.map(u=>u.ts),console.log(a+"."+l.data,n==null?void 0:n.tableDataList);const o={name:l.label,smooth:!0,data:n==null?void 0:n.tableDataList.map(u=>u[l.data]),type:"line",markPoint:{data:[{type:"max",name:"最大值"},{type:"min",name:"最小值"}]}};(i=j.value)==null||i.clear(),e.series=[o],x(()=>{$.listenTo(O.value,()=>{var u;t.chartOption1=e,(u=j.value)==null||u.resize()})})},X=f({type:"tabs",tabType:"simple",width:"100%",stretch:!1,tabs:[],handleTabClick:a=>{Z(a.props.name)}});ha(()=>k.stationId,(a,e)=>{a&&pa()});const pa=()=>{t.activeName="status",console.log("refreshData"),x(async()=>{await ca(k.stationId)})},ca=async a=>{var l,o;t.groupStation=await U(a);const e=t.groupStation[0].children,r=e==null?void 0:e.map(s=>({label:s.name,value:s.id,data:H(s.deviceId)+"."+s.attr}));t.attributeOptions=r,console.log("getStationGroup",e[0].id),w.defaultParams={...w.defaultParams,attributeId:e[0].id,group:t.groupStation[0].value},(l=A.value)==null||l.resetForm(),t.currentGroupTabs=e,X.tabs=e.map(s=>({label:s.label,value:s.id,data:s})),t.groupTab=(o=X.tabs[0])==null?void 0:o.label,await Z(t.groupTab),await P()},P=async a=>{var l;h.loading=!0;let e=b().startOf("month").valueOf(),r=b().endOf("month").valueOf();if(a==="range"){const o=(l=Q.value)==null?void 0:l.queryParams;e=b(o==null?void 0:o.date[0]).startOf("day").valueOf(),r=b(o==null?void 0:o.date[1]).endOf("day").valueOf()}Na(k.stationId,e,r).then(o=>{console.log("res",o),T=o.data,h.dataList=T==null?void 0:T.slice(0*20,20),h.pagination.total=T.length,h.loading=!1})},Z=async a=>{g.loading=!0;const e=await Ya(k.stationId,a);g.dataList=e.data;const r=e==null?void 0:e.data.find(l=>l.property==="total_flow");g.currentRow=r,await q(),g.loading=!1},q=async()=>{var D,L,I;const a=g.currentRow,e=await Aa(k.stationId);t.detailData=e.data;const l=(D=(await Va({deviceId:H(a.deviceId),attr:a.property})).data)==null?void 0:D.data,o=ta(),s=g.dataList.find(p=>p.property==="Instantaneous_flow"),v=g.dataList.find(p=>p.property==="total_flow"),i=g.dataList.find(p=>p.property==="pressure");t.detailData={...t.detailData,Instantaneous_flow:s.value+"(m³/h)",total_flow:v.value+"(m³)",pressure:i?i.value+"("+i.unit+")":""},console.log("state.detailData",t.detailData);const u=Ia(s.value,v.value,i==null?void 0:i.value),C=[{name:"前天",key:"beforeYesterdayDataList"},{name:"昨天",key:"yesterdayDataList"},{name:"今天",key:"todayDataList"}];o.xAxis.data=l.todayDataList.map(p=>p.ts),o.yAxis[0].name="瞬时流量（m³/h）";const _=C.map(p=>{const M=l[p.key].map(da=>da.value);return{name:p.name,smooth:!0,data:M,type:"line",markPoint:{data:[{type:"max",name:"最大值"},{type:"min",name:"最小值"}]},markLine:{data:[{type:"average",name:"平均值"}]}}});o.series=_,(L=F.value)==null||L.clear(),(I=B.value)==null||I.clear(),x(()=>{$.listenTo(O.value,()=>{var p,M;t.chartOption=o,t.gaugeCarOption=u,(p=F.value)==null||p.resize(),(M=B.value)==null||M.resize()})}),ra("hiddenLoading")};return(a,e)=>{const r=ka,l=_a("VChart"),o=La,s=Ta,v=Da,i=wa,u=Ca,C=Sa;return N(),G(o,{class:"wrapper-content",title:" ",overlay:""},{title:m(()=>[c(r,{modelValue:t.activeName,"onUpdate:modelValue":e[0]||(e[0]=_=>t.activeName=_),config:ia},null,8,["modelValue","config"])]),default:m(()=>[t.activeName==="status"?(N(),ya("div",Fa,[S("div",ja,[c(o,{title:"",overlay:"",class:"card-table"},{default:m(()=>[S("div",{ref_key:"echartsDiv",ref:O,class:"chart-box"},[c(l,{ref_key:"refGaugeChart",ref:B,theme:z(E).isDark?"dark":"light",option:t.gaugeCarOption},null,8,["theme","option"])],512)]),_:1}),c(o,{title:"数据详情",overlay:""},{default:m(()=>[S("div",Ba,[c(s,{data:t.detailData,columns:t.detailColumns},null,8,["data","columns"])])]),_:1})]),S("div",Pa,[c(o,{title:"图表分析",overlay:""},{default:m(()=>[S("div",{ref_key:"echartsDiv",ref:O,class:"chart-box"},[c(l,{ref_key:"refChart",ref:F,theme:z(E).isDark?"dark":"light",option:t.chartOption},null,8,["theme","option"])],512)]),_:1})])])):R("",!0),t.activeName==="search"?(N(),G(o,{key:1,class:"wrapper-content content1",title:" ",overlay:""},{title:m(()=>[c(v,{ref_key:"cardSearch",ref:A,config:w},null,8,["config"])]),default:m(()=>[c(C,{modelValue:t.searchActiveName,"onUpdate:modelValue":e[1]||(e[1]=_=>t.searchActiveName=_)},{default:m(()=>[c(u,{label:"列表模式",name:"list"},{default:m(()=>[c(i,{ref_key:"refTable",ref:J,config:d,class:"card-table"},null,8,["config"])]),_:1}),c(u,{label:"图表模式",name:"echarts"},{default:m(()=>[S("div",{ref_key:"echartsDiv",ref:O,class:"chart-box"},[c(l,{ref_key:"refChart1",ref:j,theme:z(E).isDark?"dark":"light",option:t.chartOption1},null,8,["theme","option"])],512)]),_:1})]),_:1},8,["modelValue"])]),_:1})):R("",!0),t.activeName==="alarm"?(N(),G(o,{key:2,class:"wrapper-content content1",title:" ",overlay:""},{title:m(()=>[c(v,{ref_key:"alarmSearch",ref:Q,config:sa},null,8,["config"])]),default:m(()=>[c(i,{ref_key:"alarmTable",ref:K,config:h,class:"card-table"},null,8,["config"])]),_:1})):R("",!0),t.activeName==="archives"?(N(),G(o,{key:3,class:"wrapper-content content1",title:"",overlay:""},{default:m(()=>[c(s,{data:t.detailData,columns:t.detailColumns},null,8,["data","columns"])]),_:1})):R("",!0)]),_:1})}}}),Xa=Oa(Ha,[["__scopeId","data-v-e27bc7d8"]]);export{Xa as default};
