package org.thingsboard.server.dao.util.imodel.query.smartOperation.construction;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartOperation.SoGeneralTaskStatus;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionSettlement;
import org.thingsboard.server.dao.util.imodel.query.IStarHttpRequest;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;

import java.math.BigDecimal;

@Getter
@Setter
public class SoConstructionSettlementSaveRequest extends SaveRequest<SoConstructionSettlement> {
    // 所属工程编号
    private String constructionCode;

    // 结算人
    private String processUser;

    // 结算金额
    private BigDecimal cost;

    // 地址
    private String address;

    // 备注
    private String remark;

    // 附件信息
    private String attachments;

    @Override
    public String valid(IStarHttpRequest request) {
        if (cost != null && BigDecimal.ZERO.compareTo(cost) > 0) {
            return "结算金额不能为负数";
        }

        return super.valid(request);
    }

    @Override
    protected SoConstructionSettlement build() {
        SoConstructionSettlement entity = new SoConstructionSettlement();
        entity.setConstructionCode(constructionCode);
        entity.setStatus(SoGeneralTaskStatus.PROCESSING);
        entity.setCreator(currentUserUUID());
        entity.setCreateTime(createTime());
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    @Override
    protected SoConstructionSettlement update(String id) {
        SoConstructionSettlement entity = new SoConstructionSettlement();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(SoConstructionSettlement entity) {
        entity.setProcessUser(processUser);
        entity.setCost(cost);
        entity.setAddress(address);
        entity.setRemark(remark);
        entity.setAttachments(attachments);
        entity.setUpdateUser(currentUserUUID());
        entity.setUpdateTime(createTime());
    }
}