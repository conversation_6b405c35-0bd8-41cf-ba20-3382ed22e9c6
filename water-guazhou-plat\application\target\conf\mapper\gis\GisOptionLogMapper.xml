<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.gis.GisOptionLogMapper">
    <select id="findList" resultType="org.thingsboard.server.dao.model.sql.gis.GisOptionLog">
        SELECT
            a.*
        FROM
            tb_gis_option_log a
        <where>
            <if test="param.tenantId != null and param.tenantId != ''">
                AND a.tenant_id = #{param.tenantId}
            </if>
            <if test="param.type != null and param.type != ''">
                AND a.type = #{param.type}
            </if>
            <if test="param.optionType != null and param.optionType != ''">
                AND a.option_type = #{param.optionType}
            </if>
            <if test="param.keyword != null and param.keyword != ''">
                AND a.content LIKE '%' || #{param.keyword} || '%'
            </if>
            <if test="param.beginOptionTime != null">
                AND a.option_time <![CDATA[ >= ]]> #{param.beginOptionTime}
            </if>
            <if test="param.endOptionTime != null">
                AND a.option_time <![CDATA[ <= ]]> #{param.endOptionTime}
            </if>
        </where>
        ORDER BY a.option_time DESC
    </select>

</mapper>