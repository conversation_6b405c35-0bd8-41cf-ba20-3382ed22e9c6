// 查询统计
import request from '@/plugins/axios'

// 查询数据对比
export function getDataCompare(params: {
  queryType: string,
  start: number,
  end: number,
  attributes: string,
}) {
  return request({
    url: '/istar/api/station/data/getDataCompare',
    method: 'get',
    params
  })
}

// 查询周期对比
export function getCycleCompare(params: {
  queryType: string,
  start: number,
  end: number,
  attributes: string,
}) {
  return request({
    url: '/istar/api/station/data/getCycleCompare',
    method: 'get',
    params
  })
}

// 导出
export function exportDataCompare(params: {
  attributes: string,
  queryType: string,
  start: string,
  end: string
}) {
  return request({
    url: '/istar/api/station/data/getDataCompare/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
