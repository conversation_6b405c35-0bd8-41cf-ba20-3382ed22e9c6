package org.thingsboard.server.dao.model.sql;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;
import java.util.Date;
import java.util.List;

@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.MAINTENANCE_PLAN_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class MaintenancePlanEntity {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.MAINTENANCE_PLAN_NAME)
    private String name;

    @Column(name = ModelConstants.TYPE)
    private String type;

    @Column(name = ModelConstants.MAINTENANCE_PLAN_EXECUTE_TIME)
    private Date executeTime;

    @Column(name = ModelConstants.MAINTENANCE_PLAN_PERIOD_TIME)
    private Integer periodTime;

    @Column(name = ModelConstants.MAINTENANCE_PLAN_STATUS)
    private String status;

    @Column(name = ModelConstants.CREATOR_PROPERTY)
    private String creator;

    @Column(name = ModelConstants.CREATE_TIME)
    private Date createTime;

    @Column(name = ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;

    @Transient
    private List<MaintenancePlanCEntity> jobList;

    @Transient
    private MaintenancePlanTriggerEntity trigger;

}
