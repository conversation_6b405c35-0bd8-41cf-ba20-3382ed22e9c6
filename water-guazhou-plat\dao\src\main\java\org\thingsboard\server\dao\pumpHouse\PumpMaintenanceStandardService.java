package org.thingsboard.server.dao.pumpHouse;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.smartProduction.pumpHouse.PumpMaintenanceStandard;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.pumpHouse.PumpMaintenanceStandardPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.pumpHouse.PumpMaintenanceStandardSaveRequest;

public interface PumpMaintenanceStandardService {
    /**
     * 分页条件查询泵机维保标准
     *
     * @param request 分页查询条件
     * @return 分页查询结果
     */
    IPage<PumpMaintenanceStandard> findAllConditional(PumpMaintenanceStandardPageRequest request);

    /**
     * 保存泵机维保标准
     *
     * @param entity 实体信息
     * @return 保存好的实体
     */
    PumpMaintenanceStandard save(PumpMaintenanceStandardSaveRequest entity);

    /**
     * 增量修改
     *
     * @param entity 实体信息
     * @return 是否修改成功
     */
    boolean update(PumpMaintenanceStandard entity);

    /**
     * 删除
     *
     * @param id 唯一标识
     * @return 是否删除成功
     */
    boolean delete(String id);

}
