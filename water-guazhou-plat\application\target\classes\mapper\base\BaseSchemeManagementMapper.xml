<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.base.BaseSchemeManagementMapper">
    
    <resultMap type="org.thingsboard.server.dao.model.sql.base.BaseSchemeManagement" id="BaseSchemeManagementResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="mapConfId"    column="map_conf_id"    />
        <result property="pipeConfId"    column="pipe_conf_id"    />
        <result property="status"    column="status"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectBaseSchemeManagementVo">
        select id, name, map_conf_id, pipe_conf_id, status, remark from base_scheme_management
    </sql>

    <select id="selectBaseSchemeManagementList" parameterType="org.thingsboard.server.dao.model.sql.base.BaseSchemeManagement" resultMap="BaseSchemeManagementResult">
        <include refid="selectBaseSchemeManagementVo"/>
        <where>  
            <if test="mapConfId != null  and mapConfId != ''"> and map_conf_id = #{mapConfId}</if>
            <if test="pipeConfId != null  and pipeConfId != ''"> and pipe_conf_id = #{pipeConfId}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectBaseSchemeManagementById" parameterType="String" resultMap="BaseSchemeManagementResult">
        <include refid="selectBaseSchemeManagementVo"/>
        where id = #{id}
    </select>

    <select id="getAllBaseSchemeManagement"
            resultType="org.thingsboard.server.dao.model.sql.base.BaseSchemeManagement">
        <include refid="selectBaseSchemeManagementVo"/>
    </select>

    <insert id="insertBaseSchemeManagement" parameterType="org.thingsboard.server.dao.model.sql.base.BaseSchemeManagement">
        insert into base_scheme_management
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="name != null">name,</if>
            <if test="mapConfId != null">map_conf_id,</if>
            <if test="pipeConfId != null">pipe_conf_id,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="name != null">#{name},</if>
            <if test="mapConfId != null">#{mapConfId},</if>
            <if test="pipeConfId != null">#{pipeConfId},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateBaseSchemeManagement" parameterType="org.thingsboard.server.dao.model.sql.base.BaseSchemeManagement">
        update base_scheme_management
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="mapConfId != null">map_conf_id = #{mapConfId},</if>
            <if test="pipeConfId != null">pipe_conf_id = #{pipeConfId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBaseSchemeManagementById" parameterType="String">
        delete from base_scheme_management where id = #{id}
    </delete>

    <delete id="deleteBaseSchemeManagementByIds" parameterType="String">
        delete from base_scheme_management where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>