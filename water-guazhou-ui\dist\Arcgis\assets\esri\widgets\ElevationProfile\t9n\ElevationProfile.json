{"widgetLabel": "Elevation Profile", "readyPrompt": "Draw or select line to generate an elevation profile.", "readyPromptCreateOnly": "Draw line to generate an elevation profile.", "readyPromptSelectOnly": "Select line to generate an elevation profile.", "creatingPrompt-2d": "Create elevation profile by clicking in the map to place your first point, use double-click for last point.", "creatingPrompt-3d": "Create elevation profile by clicking in the scene to place your first point, use double-click for last point.", "selectingPrompt-2d": "Create elevation profile by clicking in the map to select a line.", "selectingPrompt-3d": "Create elevation profile by clicking in the scene to select a line.", "chartTooltip": "{name}: {elevation}", "clearProfile": "Clear profile", "showSettings": "Show settings", "hideSettings": "Hide settings", "unitSelectLabel": "Units", "uniformChartScalingLabel": "Uniform chart scaling", "uniformChartScalingEnable": "Use uniform scaling for the chart. X and Y axes will have the same scale.", "uniformChartScalingDisable": "Use non-uniform scaling for the chart. Y axis will be scaled to fit elevation values.", "sketchButtonLabel": "New profile", "selectButtonLabel": "Select line", "hideProfile": "Hide {name}", "showProfile": "Show {name}", "errors": {"unknown": "There was an unknown error generating the profile.", "noProfile": "No profile available.", "tooComplex": "Profile could not be generated. Input line is too complex.", "invalidGeometry": "Profile could not be generated. Invalid geometry.", "invalidElevationInfo": "Profile could not be generated. Elevation options of the line are not supported."}, "profiles": {"ground": "Ground", "input": "Input", "query": "Query", "view": "View"}, "statistics": {"notAvailable": "n/a", "maxDistance": "Length", "minElevation": "Min", "maxElevation": "Max", "avgElevation": "Avg", "gain": "<PERSON><PERSON>", "loss": "Loss", "maxSlope": "<PERSON>", "avgSlope": "Avg <PERSON>"}, "zoomOut": "Zoom Out", "showDetails": "Show Details", "hideDetails": "Hide Details"}