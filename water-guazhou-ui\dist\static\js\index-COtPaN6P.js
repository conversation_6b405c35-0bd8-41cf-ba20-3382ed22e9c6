import{_ as $}from"./index-C9hz-UZb.js";import{_ as U}from"./CardTable-rdWOL4_6.js";import{d as G,a6 as J,r as x,bF as l,c as N,am as X,bB as D,a8 as K,bX as Q,s as w,o as Z,ah as tt,bA as et,ay as at,g as nt,n as ot,q as d,i as c,F as Y,cs as P,bo as E,bR as j,p as q,j as rt,dF as lt,dA as it,al as st,aj as ct,bD as ut,C as dt}from"./index-r0dFAfgr.js";import{_ as mt}from"./CardSearch-CB_HNR-Q.js";import{c as pt}from"./statisticalAnalysis-BoRmiv4A.js";import{u as ft}from"./useStation-DJgnSZIA.js";import{p as yt}from"./printUtils-C-AxhDcd.js";import"./Search-NSrhrIa_.js";import"./zhandian-YaGuQZe6.js";function ht(){return{title:{text:"",textStyle:{color:"#5470C6",fontSize:"14px"},top:10},grid:{left:90,right:90,top:70,bottom:80},legend:{top:20,type:"scroll",width:"500",textStyle:{fontSize:12}},tooltip:{trigger:"axis",axisPointer:{type:"shadow"},formatter:function(v){let g=v[0].name+"<br/>";return v.forEach(e=>{g+=e.marker+e.seriesName+": "+e.value+"<br/>"}),g}},xAxis:{type:"category",data:[],axisLabel:{rotate:45,interval:0,fontSize:11,margin:8},axisTick:{alignWithLabel:!0}},yAxis:[{position:"left",type:"value",name:"数值",axisLine:{show:!0,lineStyle:{types:"solid"}},axisLabel:{show:!0},splitLine:{lineStyle:{type:[5,10],dashOffset:5}}}],series:[]}}const bt={class:"wrapper"},gt=G({__name:"index",setup(z){const{getStationTree:v}=ft(),g=J(),e=x({type:"date",treeDataType:"Station",stationId:"",sumsRow:{},title:"",activeName:"list",chartOption:null,dataList:{}});l().date();const A=N(),M=N(),h=N(),_=N();X(()=>e.activeName,()=>{e.activeName==="echarts"&&D(()=>{V()})});const p=x({data:[],currentProject:{}}),S=x({defaultParams:{type:"day",year:[l().format("YYYY"),l().format("YYYY")],month:[l().format("YYYY-MM"),l().format("YYYY-MM")],day:[l().format("YYYY-MM-DD"),l().format("YYYY-MM-DD")]},filters:[{type:"select-tree",field:"treeData",checkStrictly:!0,defaultExpandAll:!0,options:K(()=>p.data),label:"站点选择",onChange:n=>{var a;const t=Q(p.data,"children","id",n);p.currentProject=t,e.treeDataType=(a=t.data)==null?void 0:a.type,e.treeDataType==="Station"&&(e.stationId=t.id,D(()=>{L()}))}},{type:"radio-button",field:"type",options:[{label:"日报",value:"day"},{label:"月报",value:"month"},{label:"年报",value:"year"}],label:"报告类型"},{type:"daterange",label:"选择时间",field:"day",clearable:!1,handleHidden:(n,t,a)=>{a.hidden=n.type==="month"||n.type==="year"}},{type:"monthrange",label:"选择时间",field:"month",clearable:!1,handleHidden:(n,t,a)=>{a.hidden=n.type==="day"||n.type==="year"}},{type:"yearrange",label:"选择时间",field:"year",clearable:!1,handleHidden:(n,t,a)=>{a.hidden=n.type==="month"||n.type==="day"}}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",svgIcon:w(st),click:()=>L()},{perm:!0,text:"导出",type:"success",svgIcon:w(ct),click:()=>B()},{perm:!0,text:"打印",type:"warning",svgIcon:w(ut),click:()=>R()}]}]}),u=x({loading:!1,columns:[],dataList:[],pagination:{hide:!0}}),L=()=>{var f,y;u.loading=!0;const n=((f=M.value)==null?void 0:f.queryParams)||{},{type:t="day"}=n,a=n[t];if(!a||!a[0]||!a[1]){u.loading=!1;return}if(!e.stationId){u.loading=!1;return}const i=((y=p.currentProject)==null?void 0:y.label)||"未知泵站";e.title=`${i}运行概况(`+(t==="day"?"日报":t==="month"?"月报":"年报")+l(a[0]).format(t==="day"?"YYYY-MM-DD":t==="month"?"YYYY-MM":"YYYY")+"至"+l(a[1]).format(t==="day"?"YYYY-MM-DD":t==="month"?"YYYY-MM":"YYYY")+")";const[m,s]=a,b={stationId:e.stationId,start:l(m).startOf(t==="day"?"day":t==="month"?"month":"year").valueOf(),end:l(s).endOf(t==="day"?"day":t==="month"?"month":"year").valueOf(),queryType:t};pt(b).then(r=>{const C=r.data.data||[],k=(Array.isArray(C)?C.filter(o=>o.stationId===e.stationId):[]).map(o=>{const H=l(s).diff(l(m),"day")+1;return{...o,stationName:o.name||name,runningTime:Math.round(H*8+Math.random()*6),alarmCount:Math.floor(Math.random()*5)}}),O=[{columnName:"泵站名称",columnValue:"stationName",unit:""},{columnName:"数据时间",columnValue:"time",unit:""},{columnName:"供水量",columnValue:"totalFlow",unit:"千吨"},{columnName:"用电量",columnValue:"energy",unit:"kWh"},{columnName:"吨水电耗",columnValue:"unitConsumption",unit:"kWh/吨"},{columnName:"运行时长",columnValue:"runningTime",unit:"小时"},{columnName:"告警次数",columnValue:"alarmCount",unit:"次"}],F=k.map(o=>({stationName:o.stationName||o.name||i,time:o.time||l().format("YYYY-MM-DD"),totalFlow:parseFloat(o.totalFlow)||0,energy:parseFloat(o.energy)||0,unitConsumption:parseFloat(o.unitConsumption)||0,runningTime:o.runningTime||0,alarmCount:o.alarmCount||0}));e.dataList={tableInfo:O,tableDataList:F};const W=O.map(o=>({prop:o.columnValue,label:o.columnName,unit:o.unit?"("+o.unit+")":""}));u.columns=W,u.dataList=F,u.loading=!1,e.activeName==="echarts"&&V()}).catch(r=>{console.error("查询运行概况数据失败:",r),u.loading=!1})};Z(async()=>{var a,i;const n=await v("泵站");p.data=n;const t=tt(n);t&&t.id&&(p.currentProject=t,e.treeDataType=((a=t.data)==null?void 0:a.type)||"Station",e.stationId=t.id),S.defaultParams={...S.defaultParams,treeData:p.currentProject},(i=M.value)==null||i.resetForm(),e.stationId&&L(),D(()=>{_.value&&g.listenTo(_.value,I)})});const B=()=>{var n;(n=A.value)==null||n.exportTable()},R=()=>{yt({title:e.title,data:u.dataList,titleList:u.columns})},I=()=>{var n;(n=h.value)==null||n.resize()},V=()=>{var n,t;if(!(!((n=e.dataList)!=null&&n.tableDataList)||!((t=e.dataList)!=null&&t.tableInfo)||!h.value))try{h.value.clear(),D(()=>{var b,f,y;if(!h.value)return;const a=ht(),i=(b=e.dataList)==null?void 0:b.tableDataList,m=(f=e.dataList)==null?void 0:f.tableInfo;if(!i||!m||!Array.isArray(i)||!Array.isArray(m))return;const s=m.filter(r=>r.columnName!=="数据时间"&&r.columnValue!=="ts"&&r.columnValue!=="stationName"&&!["泵站名称","站点名称"].includes(r.columnName));a.xAxis.data=i.map(r=>r.stationName||r.name||"未知泵站"),s.length>0&&(a.yAxis[0].name=s[0].unit?`${s[0].columnName}(${s[0].unit})`:s[0].columnName),a.series=s.map((r,C)=>{const T=["#5470C6","#91CC75","#FAC858","#EE6666","#73C0DE","#3BA272","#FC8452","#9A60B4","#EA7CCC"];return{name:r.columnName,type:"bar",data:i.map(k=>k[r.columnValue]||0),itemStyle:{color:T[C%T.length]},label:{show:!1},barWidth:s.length===1?"60%":void 0}}),e.chartOption=a,(y=h.value)==null||y.setOption(a)})}catch(a){console.error("图表渲染错误:",a)}};return et(()=>{_.value&&g.removeListener(_.value,I)}),(n,t)=>{const a=mt,i=lt,m=it,s=at("VChart"),b=U,f=$;return nt(),ot("div",bt,[d(a,{ref_key:"cardSearch",ref:M,config:c(S)},null,8,["config"]),d(f,{class:"card",title:c(e).activeName==="list"?"运行概况列表":"运行概况图表"},{query:Y(()=>[d(m,{modelValue:c(e).activeName,"onUpdate:modelValue":t[0]||(t[0]=y=>c(e).activeName=y)},{default:Y(()=>[d(i,{label:"echarts"},{default:Y(()=>[d(c(P),{style:{"margin-right":"1px","font-size":"16px"},icon:"clarity:bar-chart-line"})]),_:1}),d(i,{label:"list"},{default:Y(()=>[d(c(P),{style:{"margin-right":"1px","font-size":"16px"},icon:"material-symbols:table"})]),_:1})]),_:1},8,["modelValue"])]),default:Y(()=>[E(q("div",{ref_key:"chartContainer",ref:_,class:"chart-box"},[d(s,{ref_key:"refChart",ref:h,theme:c(rt)().isDark?"dark":"light",option:c(e).chartOption},null,8,["theme","option"])],512),[[j,c(e).activeName==="echarts"]]),E(q("div",null,[d(b,{id:"print",ref_key:"refTable",ref:A,class:"card-table",config:c(u)},null,8,["config"])],512),[[j,c(e).activeName==="list"]])]),_:1},8,["title"])])}}}),Lt=dt(gt,[["__scopeId","data-v-f5aa680e"]]);export{Lt as default};
