package org.thingsboard.server.dao.model.sql.smartManagement.plan;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class SMCircuitUserStatistic {
    // 巡检人员数量/抢修人员数量/抄表人员数量
    private Integer userCount;

    // 外出巡检人员数量/???/用户数量
    private Integer resultUserCount;

    public SMCircuitUserStatistic() {

    }
    
    public SMCircuitUserStatistic(Integer userCount, Integer resultUserCount) {
        this.userCount = userCount;
        this.resultUserCount = resultUserCount;
    }

}
