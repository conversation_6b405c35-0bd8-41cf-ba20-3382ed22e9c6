package org.thingsboard.server.dao.sql.smartManagement.plan;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.smartManagement.plan.SMCircuitPlan;
import org.thingsboard.server.dao.model.sql.smartManagement.plan.SMCircuitPlanResponse;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.plan.SMCircuitPlanPageRequest;

import java.util.Date;

@Mapper
public interface SMCircuitPlanMapper extends BaseMapper<SMCircuitPlan> {
    IPage<SMCircuitPlanResponse> findByPage(SMCircuitPlanPageRequest request);

    boolean update(SMCircuitPlan entity);

    boolean isPlanArranged(@Param("planId") String planId, @Param("beginTime") Date beginTime, @Param("endTime") Date endTime);
}
