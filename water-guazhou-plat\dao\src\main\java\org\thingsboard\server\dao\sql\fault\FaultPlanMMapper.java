package org.thingsboard.server.dao.sql.fault;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.fault.FaultPlanM;

import java.util.Date;
import java.util.List;

/**
 * userMybatis
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-08-23
 */
@Mapper
public interface FaultPlanMMapper extends BaseMapper<FaultPlanM> {

    List<FaultPlanM> getList(@Param("planName") String planName, @Param("teamName") String teamName, @Param("userName") String userName, @Param("startStartTime") Date startStartTime, @Param("startEndTime") Date startEndTime, @Param("endStartTime") Date endStartTime, @Param("endEndTime") Date endEndTime, @Param("page") int page, @Param("size") int size, @Param("tenantId") String tenantId);

    int getListCount(@Param("planName") String planName, @Param("teamName") String teamName, @Param("userName") String userName, @Param("startStartTime") Date startStartTime, @Param("startEndTime") Date startEndTime, @Param("endStartTime") Date endStartTime, @Param("endEndTime") Date endEndTime, @Param("tenantId") String tenantId);

    FaultPlanM getById(@Param("mainId") String mainId);
}
