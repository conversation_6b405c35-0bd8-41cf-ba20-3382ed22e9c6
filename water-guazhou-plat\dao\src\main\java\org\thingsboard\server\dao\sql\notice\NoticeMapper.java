package org.thingsboard.server.dao.sql.notice;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.AreaEntity;
import org.thingsboard.server.dao.model.sql.maintainCircuit.maintain.MaintainPlanM;
import org.thingsboard.server.dao.model.sql.notice.NoticeDomain;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Mapper
public interface NoticeMapper extends BaseMapper<NoticeDomain> {

    boolean savaNotice(@Param("notice") NoticeDomain notice);

    boolean deleteNotice(@Param("noticeId") String noticeId);


    NoticeDomain getNoticeByid(@Param("id") String id);

    List<NoticeDomain> getList(@Param("type") String type, @Param("beginTime") Date beginTime, @Param("endTime") Date endTime, @Param("page") int page, @Param("size") int size);

    int getListCount(@Param("type") String type, @Param("beginTime") Date beginTime, @Param("endTime") Date endTime);

    boolean updateNotice(@Param("notice") NoticeDomain notice);

}
