package org.thingsboard.server.dao.gis;

import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.request.GisFileListRequest;
import org.thingsboard.server.dao.model.request.GisProjectListRequest;
import org.thingsboard.server.dao.model.sql.gis.GisFile;

import java.util.List;

public interface GisFileService {
    void save(GisFile entity);

    PageData<GisFile> findList(GisFileListRequest request, TenantId tenantId);

    void remove(List<String> ids);

    void changeStatus(List<String> ids, String status);
}
