import{z as m,d as C,r as x,c as g,o as v,ay as X,bo as y,i as l,g as z,n as D,q as O,br as j,C as k}from"./index-r0dFAfgr.js";import{u as w}from"./useDetector-BRcb7GRN.js";import{i as _}from"./echart-C9Tas6tA.js";const B=a=>m({url:"/api/jinzhou/NRW",method:"get",params:a}),M={class:"cxcfx"},N=C({__name:"CXCFX_jinzhou",setup(a,{expose:f}){const e=x({loading:!1,cxcOption:_()}),c=(t="1")=>{e.loading=!0,B({type:t}).then(i=>{const n=i.data.data||{},o=[],p=[],u=[],d=[];n.map(s=>{o.push(s.time),p.push(s.supply),u.push(s.use),d.push(s.nrw)}),e.cxcOption=_(o,p,u,d)}).finally(()=>{e.loading=!1})},r=g(),h=w();return v(()=>{h.listenToMush(document.documentElement,()=>{var t;(t=r.value)==null||t.resize()}),c()}),f({refreshData:c}),(t,i)=>{const n=X("VChart"),o=j;return y((z(),D("div",M,[O(n,{ref_key:"refChart",ref:r,option:l(e).cxcOption},null,8,["option"])])),[[o,l(e).loading]])}}}),E=k(N,[["__scopeId","data-v-66e93c1f"]]);export{E as default};
