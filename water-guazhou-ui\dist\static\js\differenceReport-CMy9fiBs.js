import{_ as R}from"./CardTable-rdWOL4_6.js";import{_ as E}from"./Search-NSrhrIa_.js";import"./index-0NlGN6gS.js";import{d as q,c as h,r as g,l,bJ as m,bI as y,o as P,g as T,n as B,q as v,i as I,C as L}from"./index-r0dFAfgr.js";import{E as M}from"./index-Bo22WWST.js";import{a as j,E as F}from"./proSale-DWRhGXcG.js";import"./index-C9hz-UZb.js";const G={class:""},H=q({__name:"differenceReport",props:{partitions:{}},setup(J){const D=h(),d=h(),f=(e,o,r)=>{r.hidden=e.type!==r.field},k=g({defaultParams:{type:"year",year:l().format(m),month:[l().format(y),l().format(y)],yearInterval:[l().format(m),l().format(m)]},filters:[{type:"radio-button",field:"type",options:[{label:"按年",value:"year"},{label:"按年月",value:"month"},{label:"按年区间",value:"yearInterval"}],label:"选择方式",clearable:!1},{handleHidden:f,type:"year",label:"",field:"year",clearable:!1,disabledDate(e){return new Date<e}},{handleHidden:f,type:"monthrange",label:"",field:"month",clearable:!1,format:y,disabledDate(e){return new Date<e}},{handleHidden:f,type:"yearrange",label:"",field:"yearInterval",clearable:!1,disabledDate(e){return new Date<e}},{type:"btn-group",btns:[{perm:!0,text:"查询",iconifyIcon:"ep:search",disabled:()=>!!t.loading,click:()=>i()},{perm:!0,text:"重置",type:"default",iconifyIcon:"ep:refresh",click:()=>{var e;(e=d.value)==null||e.resetForm()}},{perm:!0,text:"导出",type:"primary",iconifyIcon:"ep:download",disabled:()=>!!t.loading,click:()=>{i(!0)}}]}]}),t=g({dataList:[],columns:[{prop:"name",label:l().format(m)+"年产销差报表",align:"center",subColumns:[{prop:"name",label:"分区名称"},{prop:"yearRate",label:"年产销差(%)"}]}],pagination:{hide:!0,refreshData({page:e,size:o}){t.pagination.page=e,t.pagination.limit=o,i()}}}),i=async e=>{var o,r,c,u,b;try{t.loading=!0;const a=((o=d.value)==null?void 0:o.queryParams)||{},s={type:a.type,date:a.type==="year"?a.year:void 0,start:a.type==="month"?(r=a.month)==null?void 0:r[0]:a.type==="yearInterval"?(c=a.yearInterval)==null?void 0:c[0]:void 0,end:a.type==="month"?(u=a.month)==null?void 0:u[1]:a.type==="yearInterval"?(b=a.yearInterval)==null?void 0:b[1]:void 0},_=(await j(s)).data.data||{},x=_.header||[],C=_.data||[];if(t.dataList=C.map(n=>{const p={};return n.data.map((S,w)=>{p[w]=S}),p.name=n.name,p}),t.columns=[{prop:"name",label:(a.type==="year"?s.date:(s.start??"--")+" ~ "+(s.end??"--"))+"产销差报表",align:"center",subColumns:[{prop:"name",label:"分区名称"},...x.map((n,p)=>({minWidth:120,label:n,prop:p.toString()}))]}],e){const n=await F(s);M(n.data,"产销差报表")}}catch{}t.loading=!1};return P(()=>{i()}),(e,o)=>{const r=E,c=R;return T(),B("div",G,[v(r,{ref_key:"refSearch",ref:d,config:I(k),class:"search"},null,8,["config"]),v(c,{ref_key:"refTable",ref:D,class:"card-table",config:I(t)},null,8,["config"])])}}}),Q=L(H,[["__scopeId","data-v-51bca344"]]);export{Q as default};
