/* eslint-disable @typescript-eslint/explicit-module-boundary-types */

import axios from 'axios';
import { getCurTenant } from '@/api/tenant';
import { getToken } from '@/api/login';

const config = {
  // baseURL: window.SITE_CONFIG.apiURL,
  // baseURL: 'http://***********:8765',
  timeout: 1000 * 180
  // withCredentials: true
};

const _axios = axios.create(config);

_axios.interceptors.request.use(
  (config) => {
    // 在请求发送之前做某事
    if (getToken()) {
      // 让每个请求携带token-- ['X-Token']为自定义key 请根据实际情况自行修改
      config.headers['X-Authorization'] = `bearer ${getToken()}`;
    }
    if (getCurTenant()) {
      config.headers.currentTenant = getCurTenant();
    }
    return config;
  },
  (error) => {
    console.log(error); // for debug
    // Do something with request error
    return Promise.reject(error);
  }
);

// Add a response interceptor
_axios.interceptors.response.use(
  (response) => {
    // Do something with response data
    /**
     * 下面的注释为通过在response里，自定义code来标示请求状态
     * 当code返回如下情况则说明权限有问题，登出并返回到登录页
     * 如想通过xmlhttprequest来状态码标识 逻辑可写在下面error中
     * 以下代码均为样例，请结合自生需求加以修改，若不需要，则可删除
     * code为非200是抛错 可结合自己业务进行修改
     */
    const res = response.status;
    if (res !== 200 && res !== 201 && res !== 204) {
      return Promise.reject(new Error('error'));
    }
    return response;
  },
  (error) => {
    let message =
      error.response?.data?.message || error.response?.message || error.message;
    const status =
      error.response?.data?.status || error.response?.status || error.status;
    switch (status) {
      case 404:
        message = '请求失败，资源未找到';
        break;
      case 403:
        message = '对不起，操作权限不足';
        break;
      case 408:
        message = '响应超时，请稍候再试';
        break;
      default:
        break;
    }

    // Do something with response error
    return Promise.reject(message);
  }
);

export const request = _axios;
export default _axios;
