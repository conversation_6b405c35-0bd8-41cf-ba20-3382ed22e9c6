import{_ as S}from"./CardSearch-CB_HNR-Q.js";import{d as q,r as c,l as v,c as D,a8 as L,o as P,a0 as M,g as R,n as k,q as l,F as x,p as i,bh as T,aq as _,C as Y}from"./index-r0dFAfgr.js";import{_ as G}from"./index-C9hz-UZb.js";import{i as f}from"./data-BAeTQf_f.js";import{g as I,a as B,e as E}from"./waterQualityMonitoring-BYvdS1Mm.js";import{f as N,G as j}from"./zhandian-YaGuQZe6.js";import"./Search-NSrhrIa_.js";const A={class:"wrapper"},V={class:"card-title"},W={class:"title"},F={class:"date"},Q={class:"card-content"},U={class:"table-box__max"},z={class:"table-box__min"},O={class:"table-box__report"},$=q({__name:"waterReportDaily",setup(H){const o=c({stationList:[],groupTypeList:[],currentStation:{},time:v().format("YYYY-MM-DD")}),p=D(),u=c({defaultParams:{time:v().format("YYYY-MM-DD")},filters:[{type:"select",label:"监测站:",field:"stationId",options:L(()=>o.stationList),onChange:n=>{b(n)}},{type:"select",label:"监测点:",field:"groupType",options:L(()=>o.groupTypeList),onChange:()=>{m()}},{type:"date",label:"日期",field:"time",onChange:n=>{o.time=n}}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",icon:"iconfont icon-chaxun",click:()=>m()},{perm:!0,iconifyIcon:"ep:download",type:"warning",text:"导出",click:()=>w()}]}]}),g=c({height:"none",highlightCurrentRow:!1,tableTitle:"最大值及发现时间",columns:f("max"),dataList:[],pagination:{hide:!0}}),y=c({tableTitle:"最小值及发现时间",dataList:[],height:"none",columns:f("min"),pagination:{hide:!0}}),d=c({tableTitle:"水质日报表",height:"none",dataList:[],columns:f("report"),pagination:{hide:!0}}),m=async()=>{var a;const t={stationType:"水质监测站",queryType:"day",...(a=p.value)==null?void 0:a.queryParams},e=await I(t);g.dataList=h(e.data.data.max,"最大值"),y.dataList=h(e.data.data.min,"最小值");const s=await B(t);d.columns=C(s.data.data.tableInfo),d.dataList=s.data.data.tableDataList},w=()=>{var e;const t={stationType:"水质监测站",queryType:"day",...(e=p.value)==null?void 0:e.queryParams};E(t).then(s=>{const a=window.URL.createObjectURL(s.data);console.log(a);const r=document.createElement("a");r.style.display="none",r.href=a,r.setAttribute("download",`${o.currentStation.label}水质日报表.xlsx`),document.body.appendChild(r),r.click()})};function h(n,t){const e={type:t},s={type:"发生时间"};return n.forEach(a=>{e[a.name]=a.value,s[a.name]=a.time}),[e,s]}function C(n){return n.map(t=>(t.label=t.columnName,t.prop=t.columnValue,t))}const b=async n=>{var e,s;const t=await N({stationId:n});o.groupTypeList=t==null?void 0:t.data.map(a=>({label:a,value:a})),o.currentStation=o.stationList.find(a=>a.value===n),u.defaultParams={...u.defaultParams,stationId:o.currentStation.value,groupType:(e=o.groupTypeList[0])==null?void 0:e.value},(s=p.value)==null||s.resetForm(),m()};return P(async()=>{var e,s;const t=(await j({page:1,size:999,type:"水质监测站",projectId:(e=M().selectedProject)==null?void 0:e.value})).data.data;o.stationList=t.map(a=>({label:a.name,value:a.id})),o.currentStation=o.stationList[0],await b((s=o.stationList[0])==null?void 0:s.value)}),(n,t)=>{const e=S;return R(),k("div",A,[l(e,{ref_key:"refSearch",ref:p,config:u},null,8,["config"]),l(G,{class:"card",title:" "},{title:x(()=>[i("div",V,[i("span",W,T(o.currentStation.label)+"水质日报表",1),i("span",F,"报表时间："+T(o.time),1)])]),default:x(()=>[i("div",Q,[i("div",U,[l(_,{config:g},null,8,["config"])]),i("div",z,[l(_,{config:y},null,8,["config"])]),i("div",O,[l(_,{config:d},null,8,["config"])])])]),_:1})])}}}),ot=Y($,[["__scopeId","data-v-c99332a8"]]);export{ot as default};
