const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["static/js/MapView-DaoQedLH.js","static/js/index-r0dFAfgr.js","static/css/index-ByiGXvnH.css","static/js/Point-WxyopZva.js","static/js/widget-BcWKanF2.js","static/js/pe-B8dP0-Ut.js"])))=>i.map(i=>d[i]);
import{e as a,y as o,o as v,b as J,a as C,S as se,s as m,r as u,av as ae,i as L,a5 as le,l as F,m as _,ai as H,H as j,a6 as N,a4 as Q,aw as oe,Z as ne}from"./Point-WxyopZva.js";import{U as E,L as X,A as Z,F as pe}from"./pe-B8dP0-Ut.js";import{w as Y,cQ as ye,dq as ue,f as ee,V as re,cP as de,fg as ce,c7 as he,fh as fe,fi as q,bk as W,dk as be,db as me,fj as ge,h as Se,fk as ve,cv as B,cU as Ie,fl as we,C as _e,bI as Ee,dr as Le,ds as Te,fm as G,ee as Oe,aB as xe,W as De,c as Pe,t as Ae,Q as Fe,R as $e,eg as je,q as Me,ef as Ce,de as Ve,Z as Ne,aJ as Re,aE as Ue,bA as ke,cS as qe,e as Be}from"./MapView-DaoQedLH.js";import{a4 as Je,a3 as We,a5 as P,R as M}from"./index-r0dFAfgr.js";import{s as Qe}from"./ArcGISCachedService-CQM8IwuM.js";import{r as Ge}from"./Version-Q4YOKegY.js";import{l as Ke,U as ze}from"./widget-BcWKanF2.js";import{x as He}from"./QueryTask-B4og_2RG.js";import{t as Xe}from"./sublayerUtils-bmirCD0I.js";import{e as Ze}from"./imageBitmapUtils-Db1drMDc.js";const Ye=e=>{let r=class extends e{constructor(){super(...arguments),this.capabilities=void 0,this.copyright=null,this.fullExtent=null,this.legendEnabled=!0,this.spatialReference=null,this.version=void 0,this._allLayersAndTablesPromise=null,this._allLayersAndTablesMap=null}readCapabilities(t,i){const s=i.capabilities&&i.capabilities.split(",").map(ie=>ie.toLowerCase().trim());if(!s)return{operations:{supportsExportMap:!1,supportsExportTiles:!1,supportsIdentify:!1,supportsQuery:!1,supportsTileMap:!1},exportMap:null,exportTiles:null};const l=this.type,p=l!=="tile"&&!!i.supportsDynamicLayers,y=s.includes("query"),h=s.includes("map"),S=!!i.exportTilesAllowed,b=s.includes("tilemap"),I=s.includes("data"),T=l!=="tile"&&(!i.tileInfo||p),O=l!=="tile"&&(!i.tileInfo||p),d=l!=="tile",f=i.cimVersion&&Ge.parse(i.cimVersion),x=(f==null?void 0:f.since(1,4))??!1,D=(f==null?void 0:f.since(2,0))??!1;return{operations:{supportsExportMap:h,supportsExportTiles:S,supportsIdentify:y,supportsQuery:I,supportsTileMap:b},exportMap:h?{supportsArcadeExpressionForLabeling:x,supportsSublayersChanges:d,supportsDynamicLayers:p,supportsSublayerVisibility:T,supportsSublayerDefinitionExpression:O,supportsCIMSymbols:D}:null,exportTiles:S?{maxExportTilesCount:+i.maxExportTilesCount}:null}}readVersion(t,i){let s=i.currentVersion;return s||(s=i.hasOwnProperty("capabilities")||i.hasOwnProperty("tables")?10:i.hasOwnProperty("supportedImageFormatTypes")?9.31:9.3),s}async fetchSublayerInfo(t,i){var s;try{return await this.fetchAllLayersAndTables(i),(s=this._allLayersAndTablesMap)==null?void 0:s.get(t)}catch{return}}async fetchAllLayersAndTables(t){await this.load(t),this._allLayersAndTablesPromise||(this._allLayersAndTablesPromise=E(X(this.url).path+"/layers",{responseType:"json",query:{f:"json",...this.customParameters,token:this.apiKey}}).then(s=>{this._allLayersAndTablesMap=new Map;for(const l of s.data.layers)this._allLayersAndTablesMap.set(l.id,l);return{result:s.data}},s=>({error:s})));const i=await this._allLayersAndTablesPromise;if(se(t),"result"in i)return i.result;throw i.error}};return a([o({readOnly:!0})],r.prototype,"capabilities",void 0),a([v("service","capabilities",["capabilities","exportTilesAllowed","maxExportTilesCount","supportsDynamicLayers","tileInfo"])],r.prototype,"readCapabilities",null),a([o({json:{read:{source:"copyrightText"}}})],r.prototype,"copyright",void 0),a([o({type:Y})],r.prototype,"fullExtent",void 0),a([o(ye)],r.prototype,"id",void 0),a([o({type:Boolean,json:{origins:{service:{read:{enabled:!1}}},read:{source:"showLegend"},write:{target:"showLegend"}}})],r.prototype,"legendEnabled",void 0),a([o(ue)],r.prototype,"popupEnabled",void 0),a([o({type:J})],r.prototype,"spatialReference",void 0),a([o({readOnly:!0})],r.prototype,"version",void 0),a([v("version",["currentVersion","capabilities","tables","supportedImageFormatTypes"])],r.prototype,"readVersion",null),r=a([C("esri.layers.mixins.ArcGISMapService")],r),r};var $;function R(e){return e!=null&&e.type==="esriSMS"}function U(e,r,t){var s;const i=this.originIdOf(r)>=j(t.origin);return{ignoreOrigin:!0,allowNull:i,enabled:!!t&&((s=t.layer)==null?void 0:s.type)==="map-image"&&(t.writeSublayerStructure||i)}}function K(e,r,t){var i;return{enabled:!!t&&((i=t.layer)==null?void 0:i.type)==="tile"&&this._isOverridden(r)}}function g(e,r,t){return{ignoreOrigin:!0,enabled:t&&t.writeSublayerStructure||!1}}function A(e,r,t){return{ignoreOrigin:!0,enabled:!!t&&(t.writeSublayerStructure||this.originIdOf(r)>=j(t.origin))}}let er=0;const w=new Set;w.add("layer"),w.add("parent"),w.add("loaded"),w.add("loadStatus"),w.add("loadError"),w.add("loadWarnings");let n=$=class extends ee(re(de(xe))){constructor(e){super(e),this.capabilities=void 0,this.fields=null,this.fullExtent=null,this.geometryType=null,this.globalIdField=null,this.legendEnabled=!0,this.objectIdField=null,this.popupEnabled=!0,this.popupTemplate=null,this.sourceJSON=null,this.title=null,this.typeIdField=null,this.types=null,this._lastParsedUrl=null}async load(e){return this.addResolvingPromise((async()=>{const{layer:r,source:t,url:i}=this;if(!r&&!i)throw new m("sublayer:missing-layer","Sublayer can't be loaded without being part of a layer",{sublayer:this});let s=null;if(!r||this.originIdOf("url")>u.SERVICE||(t==null?void 0:t.type)==="data-layer")s=(await E(i,{responseType:"json",query:{f:"json"},...e})).data;else{let l=this.id;(t==null?void 0:t.type)==="map-layer"&&(l=t.mapLayerId),s=await r.fetchSublayerInfo(l,e)}s&&(this.sourceJSON=s,this.read({layerDefinition:s},{origin:"service"}))})()),this}readCapabilities(e,r){r=r.layerDefinition||r;const{operations:{supportsQuery:t,supportsQueryAttachments:i},query:{supportsFormatPBF:s},data:{supportsAttachment:l}}=ce(r,this.url);return{exportMap:{supportsModification:!!r.canModifyLayer},operations:{supportsQuery:t,supportsQueryAttachments:i},data:{supportsAttachment:l},query:{supportsFormatPBF:s}}}get defaultPopupTemplate(){return this.createPopupTemplate()}set definitionExpression(e){this._setAndNotifyLayer("definitionExpression",e)}get fieldsIndex(){return new he(this.fields||[])}set floorInfo(e){this._setAndNotifyLayer("floorInfo",e)}readGlobalIdFieldFromService(e,r){if((r=r.layerDefinition||r).globalIdField)return r.globalIdField;if(r.fields){for(const t of r.fields)if(t.type==="esriFieldTypeGlobalID")return t.name}}get id(){return this._get("id")??er++}set id(e){var r,t,i;this._get("id")!==e&&(((i=(t=(r=this.layer)==null?void 0:r.capabilities)==null?void 0:t.exportMap)==null?void 0:i.supportsDynamicLayers)!==!1?this._set("id",e):this._logLockedError("id","capability not available 'layer.capabilities.exportMap.supportsDynamicLayers'"))}set labelingInfo(e){this._setAndNotifyLayer("labelingInfo",e)}writeLabelingInfo(e,r,t,i){e&&e.length&&(r.layerDefinition={drawingInfo:{labelingInfo:e.map(s=>s.write({},i))}})}set labelsVisible(e){this._setAndNotifyLayer("labelsVisible",e)}set layer(e){this._set("layer",e),this.sublayers&&this.sublayers.forEach(r=>r.layer=e)}set listMode(e){this._set("listMode",e)}set minScale(e){this._setAndNotifyLayer("minScale",e)}readMinScale(e,r){return r.minScale||r.layerDefinition&&r.layerDefinition.minScale||0}set maxScale(e){this._setAndNotifyLayer("maxScale",e)}readMaxScale(e,r){return r.maxScale||r.layerDefinition&&r.layerDefinition.maxScale||0}get effectiveScaleRange(){const{minScale:e,maxScale:r}=this;return{minScale:e,maxScale:r}}readObjectIdFieldFromService(e,r){if((r=r.layerDefinition||r).objectIdField)return r.objectIdField;if(r.fields){for(const t of r.fields)if(t.type==="esriFieldTypeOID")return t.name}}set opacity(e){this._setAndNotifyLayer("opacity",e)}readOpacity(e,r){var i;const t=r.layerDefinition;return 1-.01*(((t==null?void 0:t.transparency)!=null?t.transparency:(i=t==null?void 0:t.drawingInfo)==null?void 0:i.transparency)??0)}writeOpacity(e,r,t,i){r.layerDefinition={drawingInfo:{transparency:100-100*e}}}writeParent(e,r){this.parent&&this.parent!==this.layer?r.parentLayerId=ae(this.parent.id):r.parentLayerId=-1}get queryTask(){var p;if(!this.layer)return null;const{spatialReference:e}=this.layer,r="gdbVersion"in this.layer?this.layer.gdbVersion:void 0,{capabilities:t,fieldsIndex:i}=this,s=Je("featurelayer-pbf")&&(t==null?void 0:t.query.supportsFormatPBF),l=((p=t==null?void 0:t.operations)==null?void 0:p.supportsQueryAttachments)??!1;return new He({url:this.url,pbfSupported:s,fieldsIndex:i,gdbVersion:r,sourceSpatialReference:e,queryAttachmentsSupported:l})}set renderer(e){if(e){for(const r of e.getSymbols())if(fe(r)){L.getLogger(this.declaredClass).warn("Sublayer renderer should use 2D symbols");break}}this._setAndNotifyLayer("renderer",e)}get source(){return this._get("source")||new q({mapLayerId:this.id})}set source(e){this._setAndNotifyLayer("source",e)}set sublayers(e){this._handleSublayersChange(e,this._get("sublayers")),this._set("sublayers",e)}castSublayers(e){return le(W.ofType($),e)}writeSublayers(e,r,t){var i;(i=this.sublayers)!=null&&i.length&&(r[t]=this.sublayers.map(s=>s.id).toArray().reverse())}readTypeIdField(e,r){let t=(r=r.layerDefinition||r).typeIdField;if(t&&r.fields){t=t.toLowerCase();const i=r.fields.find(s=>s.name.toLowerCase()===t);i&&(t=i.name)}return t}get url(){var i;const e=((i=this.layer)==null?void 0:i.parsedUrl)??this._lastParsedUrl,r=this.source;if(!e)return null;if(this._lastParsedUrl=e,(r==null?void 0:r.type)==="map-layer")return`${e.path}/${r.mapLayerId}`;const t={layer:JSON.stringify({source:this.source})};return`${e.path}/dynamicLayer?${Z(t)}`}set url(e){this._overrideIfSome("url",e)}set visible(e){this._setAndNotifyLayer("visible",e)}writeVisible(e,r,t,i){r[t]=this.getAtOrigin("defaultVisibility","service")||e}clone(){const{store:e}=F(this),r=new $;return F(r).store=e.clone(w),this.commitProperty("url"),r._lastParsedUrl=this._lastParsedUrl,r}createPopupTemplate(e){return be(this,e)}createQuery(){return new me({returnGeometry:!0,where:this.definitionExpression||"1=1"})}async createFeatureLayer(){var i;if(this.hasOwnProperty("sublayers"))return null;const{layer:e}=this,r=e==null?void 0:e.parsedUrl,t=new(await We(async()=>{const{default:s}=await import("./MapView-DaoQedLH.js").then(l=>l.m9);return{default:s}},__vite__mapDeps([0,1,2,3,4,5]))).default({url:r==null?void 0:r.path});return r&&this.source&&(this.source.type==="map-layer"?t.layerId=this.source.mapLayerId:t.dynamicDataSource=this.source),(e==null?void 0:e.refreshInterval)!=null&&(t.refreshInterval=e.refreshInterval),this.definitionExpression&&(t.definitionExpression=this.definitionExpression),this.floorInfo&&(t.floorInfo=P(this.floorInfo)),this.originIdOf("labelingInfo")>u.SERVICE&&(t.labelingInfo=P(this.labelingInfo)),this.originIdOf("labelsVisible")>u.DEFAULTS&&(t.labelsVisible=this.labelsVisible),this.originIdOf("legendEnabled")>u.DEFAULTS&&(t.legendEnabled=this.legendEnabled),this.originIdOf("visible")>u.DEFAULTS&&(t.visible=this.visible),this.originIdOf("minScale")>u.DEFAULTS&&(t.minScale=this.minScale),this.originIdOf("maxScale")>u.DEFAULTS&&(t.maxScale=this.maxScale),this.originIdOf("opacity")>u.DEFAULTS&&(t.opacity=this.opacity),this.originIdOf("popupTemplate")>u.DEFAULTS&&(t.popupTemplate=P(this.popupTemplate)),this.originIdOf("renderer")>u.SERVICE&&(t.renderer=P(this.renderer)),((i=this.source)==null?void 0:i.type)==="data-layer"&&(t.dynamicDataSource=this.source.clone()),this.originIdOf("title")>u.DEFAULTS&&(t.title=this.title),(e==null?void 0:e.type)==="map-image"&&e.originIdOf("customParameters")>u.DEFAULTS&&(t.customParameters=e.customParameters),(e==null?void 0:e.type)==="tile"&&e.originIdOf("customParameters")>u.DEFAULTS&&(t.customParameters=e.customParameters),t}getField(e){return this.fieldsIndex.get(e)}getFeatureType(e){const{typeIdField:r,types:t}=this;if(!r||!e)return null;const i=e.attributes?e.attributes[r]:void 0;if(i==null)return null;let s=null;return t==null||t.some(l=>{const{id:p}=l;return p!=null&&(p.toString()===i.toString()&&(s=l),!!s)}),s}getFieldDomain(e,r){const t=r&&r.feature,i=this.getFeatureType(t);if(i){const s=i.domains&&i.domains[e];if(s&&s.type!=="inherited")return s}return this._getLayerDomain(e)}async queryAttachments(e,r){var b,I;await this.load(),e=ge.from(e);const t=this.capabilities;if(!((b=t==null?void 0:t.data)!=null&&b.supportsAttachment))throw new m("queryAttachments:not-supported","this layer doesn't support attachments");const{attachmentTypes:i,objectIds:s,globalIds:l,num:p,size:y,start:h,where:S}=e;if(!((I=t==null?void 0:t.operations)!=null&&I.supportsQueryAttachments)&&((i==null?void 0:i.length)>0||(l==null?void 0:l.length)>0||(y==null?void 0:y.length)>0||p||h||S))throw new m("queryAttachments:option-not-supported","when 'capabilities.operations.supportsQueryAttachments' is false, only objectIds is supported",e);if(!(s!=null&&s.length||l!=null&&l.length||S))throw new m("queryAttachments:invalid-query","'objectIds', 'globalIds', or 'where' are required to perform attachment query",e);return this.queryTask.executeAttachmentQuery(e,r)}async queryFeatures(e=this.createQuery(),r){var i,s;if(await this.load(),!this.capabilities.operations.supportsQuery)throw new m("queryFeatures:not-supported","this layer doesn't support queries.");if(!this.url)throw new m("queryFeatures:not-supported","this layer has no url.");const t=await this.queryTask.execute(e,{...r,query:{...(i=this.layer)==null?void 0:i.customParameters,token:(s=this.layer)==null?void 0:s.apiKey}});if(t!=null&&t.features)for(const l of t.features)l.sourceLayer=this;return t}toExportImageJSON(e){var l;const r={id:this.id,source:((l=this.source)==null?void 0:l.toJSON())||{mapLayerId:this.id,type:"mapLayer"}},t=Se(e,this.definitionExpression);M(t)&&(r.definitionExpression=t);const i=["renderer","labelingInfo","opacity","labelsVisible"].reduce((p,y)=>(p[y]=this.originIdOf(y),p),{});if(Object.keys(i).some(p=>i[p]>u.SERVICE)){const p=r.drawingInfo={};if(i.renderer>u.SERVICE&&(p.renderer=this.renderer?this.renderer.toJSON():null),i.labelsVisible>u.SERVICE&&(p.showLabels=this.labelsVisible),this.labelsVisible&&i.labelingInfo>u.SERVICE){!this.loaded&&this.labelingInfo.some(h=>!h.labelPlacement)&&L.getLogger(this.declaredClass).warnOnce(`A Sublayer (title: ${this.title}, id: ${this.id}) has an undefined 'labelPlacement' and so labels cannot be displayed. Either define a valid 'labelPlacement' or call Sublayer.load() to use a default value based on geometry type.`,{sublayer:this});let y=this.labelingInfo;M(this.geometryType)&&(y=ve(this.labelingInfo,B.toJSON(this.geometryType))),p.labelingInfo=y.filter(h=>h.labelPlacement).map(h=>h.toJSON({origin:"service",layer:this.layer})),p.showLabels=!0}i.opacity>u.SERVICE&&(p.transparency=100-100*this.opacity),this._assignDefaultSymbolColors(p.renderer)}return r}_assignDefaultSymbolColors(e){this._forEachSimpleMarkerSymbols(e,r=>{r.color||r.style!=="esriSMSX"&&r.style!=="esriSMSCross"||(r.outline&&r.outline.color?r.color=r.outline.color:r.color=[0,0,0,0])})}_forEachSimpleMarkerSymbols(e,r){if(e){const t=("uniqueValueInfos"in e?e.uniqueValueInfos:"classBreakInfos"in e?e.classBreakInfos:null)??[];for(const i of t)R(i.symbol)&&r(i.symbol);"symbol"in e&&R(e.symbol)&&r(e.symbol),"defaultSymbol"in e&&R(e.defaultSymbol)&&r(e.defaultSymbol)}}_setAndNotifyLayer(e,r){var y,h,S,b;const t=this.layer,i=this._get(e);let s,l;switch(e){case"definitionExpression":case"floorInfo":s="supportsSublayerDefinitionExpression";break;case"minScale":case"maxScale":case"visible":s="supportsSublayerVisibility";break;case"labelingInfo":case"labelsVisible":case"opacity":case"renderer":case"source":s="supportsDynamicLayers",l="supportsModification"}const p=F(this).getDefaultOrigin();if(p!=="service"){if(s&&((S=(h=(y=this.layer)==null?void 0:y.capabilities)==null?void 0:h.exportMap)==null?void 0:S[s])===!1)return void this._logLockedError(e,`capability not available 'layer.capabilities.exportMap.${s}'`);if(l&&((b=this.capabilities)==null?void 0:b.exportMap[l])===!1)return void this._logLockedError(e,`capability not available 'capabilities.exportMap.${l}'`)}e!=="source"||this.loadStatus==="not-loaded"?(this._set(e,r),p!=="service"&&i!==r&&t&&t.emit&&t.emit("sublayer-update",{propertyName:e,target:this})):this._logLockedError(e,"'source' can't be changed after calling sublayer.load()")}_handleSublayersChange(e,r){r&&(r.forEach(t=>{t.parent=null,t.layer=null}),this.handles.removeAll()),e&&(e.forEach(t=>{t.parent=this,t.layer=this.layer}),this.handles.add([e.on("after-add",({item:t})=>{t.parent=this,t.layer=this.layer}),e.on("after-remove",({item:t})=>{t.parent=null,t.layer=null}),e.on("before-changes",t=>{var s,l,p;const i=(p=(l=(s=this.layer)==null?void 0:s.capabilities)==null?void 0:l.exportMap)==null?void 0:p.supportsSublayersChanges;i==null||i||(L.getLogger(this.declaredClass).error(new m("sublayer:sublayers-non-modifiable","Sublayer can't be added, moved, or removed from the layer's sublayers",{sublayer:this,layer:this.layer})),t.preventDefault())})]))}_logLockedError(e,r){const{layer:t,declaredClass:i}=this;L.getLogger(i).error(new m("sublayer:locked",`Property '${String(e)}' can't be changed on Sublayer from the layer '${t==null?void 0:t.id}'`,{reason:r,sublayer:this,layer:t}))}_getLayerDomain(e){const r=this.fieldsIndex.get(e);return r?r.domain:null}};n.test={isMapImageLayerOverridePolicy:e=>e===g||e===U,isTileImageLayerOverridePolicy:e=>e===K},a([o({readOnly:!0})],n.prototype,"capabilities",void 0),a([v("service","capabilities",["layerDefinition.canModifyLayer","layerDefinition.capabilities"])],n.prototype,"readCapabilities",null),a([o()],n.prototype,"defaultPopupTemplate",null),a([o({type:String,value:null,json:{name:"layerDefinition.definitionExpression",write:{allowNull:!0,overridePolicy:U}}})],n.prototype,"definitionExpression",null),a([o({type:[Ie],json:{origins:{service:{read:{source:"layerDefinition.fields"}}}}})],n.prototype,"fields",void 0),a([o({readOnly:!0})],n.prototype,"fieldsIndex",null),a([o({type:we,value:null,json:{name:"layerDefinition.floorInfo",read:{source:"layerDefinition.floorInfo"},write:{target:"layerDefinition.floorInfo",overridePolicy:U},origins:{"web-scene":{read:!1,write:!1}}}})],n.prototype,"floorInfo",null),a([o({type:Y,json:{read:{source:"layerDefinition.extent"}}})],n.prototype,"fullExtent",void 0),a([o({type:B.apiValues,json:{origins:{service:{name:"layerDefinition.geometryType",read:{reader:B.read}}}}})],n.prototype,"geometryType",void 0),a([o({type:String})],n.prototype,"globalIdField",void 0),a([v("service","globalIdField",["layerDefinition.globalIdField","layerDefinition.fields"])],n.prototype,"readGlobalIdFieldFromService",null),a([o({type:N,json:{write:{ignoreOrigin:!0}}})],n.prototype,"id",null),a([o({value:null,type:[_e],json:{read:{source:"layerDefinition.drawingInfo.labelingInfo"},write:{target:"layerDefinition.drawingInfo.labelingInfo",overridePolicy:g}}})],n.prototype,"labelingInfo",null),a([_("labelingInfo")],n.prototype,"writeLabelingInfo",null),a([o({type:Boolean,value:!0,json:{read:{source:"layerDefinition.drawingInfo.showLabels"},write:{target:"layerDefinition.drawingInfo.showLabels",overridePolicy:g}}})],n.prototype,"labelsVisible",null),a([o({value:null})],n.prototype,"layer",null),a([o({type:Boolean,value:!0,json:{origins:{service:{read:{enabled:!1}}},read:{source:"showLegend"},write:{target:"showLegend",overridePolicy:A}}})],n.prototype,"legendEnabled",void 0),a([o({type:["show","hide","hide-children"],value:"show",json:{read:!1,write:!1,origins:{"web-scene":{read:!0,write:!0}}}})],n.prototype,"listMode",null),a([o({type:Number,value:0,json:{write:{overridePolicy:g}}})],n.prototype,"minScale",null),a([v("minScale",["minScale","layerDefinition.minScale"])],n.prototype,"readMinScale",null),a([o({type:Number,value:0,json:{write:{overridePolicy:g}}})],n.prototype,"maxScale",null),a([v("maxScale",["maxScale","layerDefinition.maxScale"])],n.prototype,"readMaxScale",null),a([o({readOnly:!0})],n.prototype,"effectiveScaleRange",null),a([o({type:String})],n.prototype,"objectIdField",void 0),a([v("service","objectIdField",["layerDefinition.objectIdField","layerDefinition.fields"])],n.prototype,"readObjectIdFieldFromService",null),a([o({type:Number,value:1,json:{write:{target:"layerDefinition.drawingInfo.transparency",overridePolicy:g}}})],n.prototype,"opacity",null),a([v("opacity",["layerDefinition.drawingInfo.transparency","layerDefinition.transparency"])],n.prototype,"readOpacity",null),a([_("opacity")],n.prototype,"writeOpacity",null),a([o({json:{type:N,write:{target:"parentLayerId",writerEnsuresNonNull:!0,overridePolicy:g}}})],n.prototype,"parent",void 0),a([_("parent")],n.prototype,"writeParent",null),a([o({type:Boolean,value:!0,json:{read:{source:"disablePopup",reader:(e,r)=>!r.disablePopup},write:{target:"disablePopup",overridePolicy:A,writer(e,r,t){r[t]=!e}}}})],n.prototype,"popupEnabled",void 0),a([o({type:Ee,json:{read:{source:"popupInfo"},write:{target:"popupInfo",overridePolicy:A}}})],n.prototype,"popupTemplate",void 0),a([o({readOnly:!0})],n.prototype,"queryTask",null),a([o({types:Le,value:null,json:{name:"layerDefinition.drawingInfo.renderer",write:{overridePolicy:g},origins:{"web-scene":{types:Te,name:"layerDefinition.drawingInfo.renderer",write:{overridePolicy:g}}}}})],n.prototype,"renderer",null),a([o({types:{key:"type",base:null,typeMap:{"data-layer":G,"map-layer":q}},cast(e){if(e){if("mapLayerId"in e)return Q(q,e);if("dataSource"in e)return Q(G,e)}return e},json:{name:"layerDefinition.source",write:{overridePolicy:g}}})],n.prototype,"source",null),a([o()],n.prototype,"sourceJSON",void 0),a([o({value:null,json:{type:[N],write:{target:"subLayerIds",allowNull:!0,overridePolicy:g}}})],n.prototype,"sublayers",null),a([H("sublayers")],n.prototype,"castSublayers",null),a([_("sublayers")],n.prototype,"writeSublayers",null),a([o({type:String,json:{name:"name",write:{overridePolicy:A}}})],n.prototype,"title",void 0),a([o({type:String})],n.prototype,"typeIdField",void 0),a([v("typeIdField",["layerDefinition.typeIdField"])],n.prototype,"readTypeIdField",null),a([o({type:[Oe],json:{origins:{service:{read:{source:"layerDefinition.types"}}}}})],n.prototype,"types",void 0),a([o({type:String,json:{read:{source:"layerUrl"},write:{target:"layerUrl",overridePolicy:K}}})],n.prototype,"url",null),a([o({type:Boolean,value:!0,json:{read:{source:"defaultVisibility"},write:{target:"defaultVisibility",overridePolicy:g}}})],n.prototype,"visible",null),a([_("visible")],n.prototype,"writeVisible",null),n=$=a([C("esri.layers.support.Sublayer")],n);const V=n,rr=L.getLogger("esri.layers.TileLayer");function tr(e,r){const t=[],i={};return e&&e.forEach(s=>{const l=new V;if(l.read(s,r),i[l.id]=l,s.parentLayerId!=null&&s.parentLayerId!==-1){const p=i[s.parentLayerId];p.sublayers||(p.sublayers=[]),p.sublayers.unshift(l)}else t.unshift(l)}),t}const k=W.ofType(V);function te(e,r){e&&e.forEach(t=>{r(t),t.sublayers&&t.sublayers.length&&te(t.sublayers,r)})}const ir=e=>{let r=class extends e{constructor(...t){super(...t),this.allSublayers=new De({getCollections:()=>[this.sublayers],getChildrenFunction:i=>i.sublayers}),this.sublayersSourceJSON={[u.SERVICE]:{},[u.PORTAL_ITEM]:{},[u.WEB_SCENE]:{},[u.WEB_MAP]:{}},this.addHandles(Ke(()=>this.sublayers,(i,s)=>this._handleSublayersChange(i,s),ze))}readSublayers(t,i){if(!i||!t)return;const{sublayersSourceJSON:s}=this,l=j(i.origin);if(l<u.SERVICE||(s[l]={context:i,visibleLayers:t.visibleLayers||s[l].visibleLayers,layers:t.layers||s[l].layers},l>u.SERVICE))return;this._set("serviceSublayers",this.createSublayersForOrigin("service").sublayers);const{sublayers:p,origin:y}=this.createSublayersForOrigin("web-document"),h=F(this);h.setDefaultOrigin(y),this._set("sublayers",new k(p)),h.setDefaultOrigin("user")}findSublayerById(t){return this.allSublayers.find(i=>i.id===t)}createServiceSublayers(){return this.createSublayersForOrigin("service").sublayers}createSublayersForOrigin(t){const i=j(t==="web-document"?"web-map":t);let s=u.SERVICE,l=this.sublayersSourceJSON[u.SERVICE].layers,p=this.sublayersSourceJSON[u.SERVICE].context,y=null;const h=[u.PORTAL_ITEM,u.WEB_SCENE,u.WEB_MAP].filter(d=>d<=i);for(const d of h){const f=this.sublayersSourceJSON[d];Xe(f.layers)&&(s=d,l=f.layers,p=f.context,f.visibleLayers&&(y={visibleLayers:f.visibleLayers,context:f.context}))}const S=[u.PORTAL_ITEM,u.WEB_SCENE,u.WEB_MAP].filter(d=>d>s&&d<=i);let b=null;for(const d of S){const{layers:f,visibleLayers:x,context:D}=this.sublayersSourceJSON[d];f&&(b={layers:f,context:D}),x&&(y={visibleLayers:x,context:D})}const I=tr(l,p),T=new Map,O=new Set;if(b)for(const d of b.layers)T.set(d.id,d);if(y!=null&&y.visibleLayers)for(const d of y.visibleLayers)O.add(d);return te(I,d=>{b&&d.read(T.get(d.id),b.context),y&&d.read({defaultVisibility:O.has(d.id)},y.context)}),{origin:oe(s),sublayers:new k({items:I})}}read(t,i){super.read(t,i),this.readSublayers(t,i)}_handleSublayersChange(t,i){i&&(i.forEach(s=>{s.parent=null,s.layer=null}),this.handles.remove("sublayers-owner")),t&&(t.forEach(s=>{s.parent=this,s.layer=this}),this.handles.add([t.on("after-add",({item:s})=>{s.parent=this,s.layer=this}),t.on("after-remove",({item:s})=>{s.parent=null,s.layer=null})],"sublayers-owner"),this.type==="tile"&&this.handles.add(t.on("before-changes",s=>{rr.error(new m("tilelayer:sublayers-non-modifiable","ISublayer can't be added, moved, or removed from the layer's sublayers",{layer:this})),s.preventDefault()}),"sublayers-owner"))}};return a([o({readOnly:!0})],r.prototype,"allSublayers",void 0),a([o({readOnly:!0,type:W.ofType(V)})],r.prototype,"serviceSublayers",void 0),a([o({value:null,type:k,json:{read:!1,write:{allowNull:!0,ignoreOrigin:!0}}})],r.prototype,"sublayers",void 0),a([o({readOnly:!0})],r.prototype,"sublayersSourceJSON",void 0),r=a([C("esri.layers.mixins.SublayersOwner")],r),r},z=["Canvas/World_Dark_Gray_Base","Canvas/World_Dark_Gray_Reference","Canvas/World_Light_Gray_Base","Canvas/World_Light_Gray_Reference","Elevation/World_Hillshade","Elevation/World_Hillshade_Dark","Ocean/World_Ocean_Base","Ocean/World_Ocean_Reference","Ocean_Basemap","Reference/World_Boundaries_and_Places","Reference/World_Boundaries_and_Places_Alternate","Reference/World_Transportation","World_Imagery","World_Street_Map","World_Topo_Map"];let c=class extends Pe(ir(Ae(Fe($e(Qe(Ye(je(re(ee(Me(Ce(Ve(Be))))))))))))){constructor(...e){super(...e),this.listMode="show",this.isReference=null,this.operationalLayerType="ArcGISTiledMapServiceLayer",this.resampling=!0,this.sourceJSON=null,this.spatialReference=null,this.path=null,this.sublayers=null,this.type="tile",this.url=null}normalizeCtorArgs(e,r){return typeof e=="string"?{url:e,...r}:e}load(e){const r=M(e)?e.signal:null;return this.addResolvingPromise(this.loadFromPortal({supportedTypes:["Map Service"]},e).catch(ne).then(()=>this._fetchService(r))),Promise.resolve(this)}get attributionDataUrl(){var r;const e=(r=this.parsedUrl)==null?void 0:r.path.toLowerCase();return e?this._getDefaultAttribution(this._getMapName(e)):null}readSpatialReference(e,r){return(e=e||r.tileInfo&&r.tileInfo.spatialReference)&&J.fromJSON(e)}writeSublayers(e,r,t,i){if(!this.loaded||!e)return;const s=e.slice().reverse().flatten(({sublayers:y})=>y&&y.toArray().reverse()).toArray(),l=[],p={writeSublayerStructure:!1,...i};s.forEach(y=>{const h=y.write({},p);l.push(h)}),l.some(y=>Object.keys(y).length>1)&&(r.layers=l)}get tileServers(){var e;return this._getDefaultTileServers((e=this.parsedUrl)==null?void 0:e.path)}castTileServers(e){return Array.isArray(e)?e.map(r=>X(r).path):null}fetchTile(e,r,t,i={}){const{signal:s}=i,l=this.getTileUrl(e,r,t),p={responseType:"image",signal:s,query:{...this.refreshParameters}};return E(l,p).then(y=>y.data)}async fetchImageBitmapTile(e,r,t,i={}){const{signal:s}=i,l=this.getTileUrl(e,r,t),p={responseType:"blob",signal:s,query:{...this.refreshParameters}},{data:y}=await E(l,p);return Ze(y,l)}getTileUrl(e,r,t){var p,y;const i=!this.tilemapCache&&this.supportsBlankTile,s=Z({...(p=this.parsedUrl)==null?void 0:p.query,blankTile:!i&&null,...this.customParameters,token:this.apiKey}),l=this.tileServers;return`${l&&l.length?l[r%l.length]:(y=this.parsedUrl)==null?void 0:y.path}/tile/${e}/${r}/${t}${s?"?"+s:""}`}loadAll(){return Ne(this,e=>{e(this.allSublayers)})}_fetchService(e){return new Promise((r,t)=>{if(this.sourceJSON){if(this.sourceJSON.bandCount!=null&&this.sourceJSON.pixelSizeX!=null)throw new m("tile-layer:unsupported-url","use ImageryTileLayer to open a tiled image service");return void r({data:this.sourceJSON})}if(!this.parsedUrl)throw new m("tile-layer:undefined-url","layer's url is not defined");const i=Re(this.parsedUrl.path);if(M(i)&&i.serverType==="ImageServer")throw new m("tile-layer:unsupported-url","use ImageryTileLayer to open a tiled image service");E(this.parsedUrl.path,{query:{f:"json",...this.parsedUrl.query,...this.customParameters,token:this.apiKey},responseType:"json",signal:e}).then(r,t)}).then(r=>{let t=this.url;if(r.ssl&&(t=this.url=t.replace(/^http:/i,"https:")),this.sourceJSON=r.data,this.read(r.data,{origin:"service",url:this.parsedUrl}),this.version===10.1&&!Ue(t))return this._fetchServerVersion(t,e).then(i=>{this.read({currentVersion:i})}).catch(()=>{})})}_fetchServerVersion(e,r){if(!ke(e))return Promise.reject();const t=e.replace(/(.*\/rest)\/.*/i,"$1")+"/info";return E(t,{query:{f:"json",...this.customParameters,token:this.apiKey},responseType:"json",signal:r}).then(i=>{if(i.data&&i.data.currentVersion)return i.data.currentVersion;throw new m("tile-layer:version-not-available")})}_getMapName(e){const r=e.match(/^(?:https?:)?\/\/(server\.arcgisonline\.com|services\.arcgisonline\.com|ibasemaps-api\.arcgis\.com)\/arcgis\/rest\/services\/([^\/]+(\/[^\/]+)*)\/mapserver/i);return r?r[2]:void 0}_getDefaultAttribution(e){if(e==null)return null;let r;e=e.toLowerCase();for(let t=0,i=z.length;t<i;t++)if(r=z[t],r.toLowerCase().includes(e))return pe("//static.arcgis.com/attribution/"+r);return null}_getDefaultTileServers(e){if(e==null)return[];const r=e.search(/^(?:https?:)?\/\/server\.arcgisonline\.com/i)!==-1,t=e.search(/^(?:https?:)?\/\/services\.arcgisonline\.com/i)!==-1;return r||t?[e,e.replace(r?/server\.arcgisonline/i:/services\.arcgisonline/i,r?"services.arcgisonline":"server.arcgisonline")]:[]}get hasOverriddenFetchTile(){return!this.fetchTile.__isDefault__}};a([o({readOnly:!0})],c.prototype,"attributionDataUrl",null),a([o({type:["show","hide","hide-children"]})],c.prototype,"listMode",void 0),a([o({json:{read:!0,write:!0}})],c.prototype,"blendMode",void 0),a([o({type:Boolean,json:{read:!1,write:{enabled:!0,overridePolicy:()=>({enabled:!1})}}})],c.prototype,"isReference",void 0),a([o({readOnly:!0,type:["ArcGISTiledMapServiceLayer"]})],c.prototype,"operationalLayerType",void 0),a([o({type:Boolean})],c.prototype,"resampling",void 0),a([o()],c.prototype,"sourceJSON",void 0),a([o({type:J})],c.prototype,"spatialReference",void 0),a([v("spatialReference",["spatialReference","tileInfo"])],c.prototype,"readSpatialReference",null),a([o({type:String,json:{origins:{"web-scene":{read:!0,write:!0}},read:!1}})],c.prototype,"path",void 0),a([o({readOnly:!0})],c.prototype,"sublayers",void 0),a([_("sublayers",{layers:{type:[V]}})],c.prototype,"writeSublayers",null),a([o({json:{read:!1,write:!1}})],c.prototype,"popupEnabled",void 0),a([o()],c.prototype,"tileServers",null),a([H("tileServers")],c.prototype,"castTileServers",null),a([o({readOnly:!0,json:{read:!1}})],c.prototype,"type",void 0),a([o(qe)],c.prototype,"url",void 0),c=a([C("esri.layers.TileLayer")],c),c.prototype.fetchTile.__isDefault__=!0;const sr=c,fr=Object.freeze(Object.defineProperty({__proto__:null,default:sr},Symbol.toStringTag,{value:"Module"}));export{sr as D,ir as E,fr as T,V as Z,Ye as y};
