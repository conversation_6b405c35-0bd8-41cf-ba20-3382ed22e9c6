package org.thingsboard.server.dao.sql.fault;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.fault.FaultInfo;

import java.util.List;

/**
 * userMybatis
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-11-08
 */
@Mapper
public interface FaultInfoMapper extends BaseMapper<FaultInfo> {

    List<FaultInfo> getListByMainId(@Param("mainId") String mainId, @Param("name") String name, @Param("tenantId") String tenantId);
}
