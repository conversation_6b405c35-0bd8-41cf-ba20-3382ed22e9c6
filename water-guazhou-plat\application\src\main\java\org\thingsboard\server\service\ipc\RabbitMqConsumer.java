package org.thingsboard.server.service.ipc;

import com.alibaba.fastjson.JSON;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.data.redis.core.BoundHashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.thingsboard.rule.engine.api.MailService;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.User;
import org.thingsboard.server.common.data.VO.AlarmLinkedUser;
import org.thingsboard.server.common.data.alarm.AlarmReport;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.VO.DefaultMapObject;
import org.thingsboard.server.common.data.dataSource.DataFromDataSource;
import org.thingsboard.server.common.data.dataSource.DataSourceRequest;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.id.UserId;
import org.thingsboard.server.config.RabbitMQConfigurationProperties;
import org.thingsboard.server.dao.dataSource.DataSourceService;

import org.thingsboard.server.dao.extraUser.ExtraUserService;
import org.thingsboard.server.dao.logicalFlow.BO.*;
import org.thingsboard.server.dao.logicalFlow.LogicalFlowHistoryService;
import org.thingsboard.server.dao.model.sql.ExtraUser;
import org.thingsboard.server.dao.model.sql.LogicalFlow;
import org.thingsboard.server.dao.model.sql.LogicalFlowAlarmNodeHistory;
import org.thingsboard.server.dao.model.sql.LogicalFlowNodeHistory;
import org.thingsboard.server.dao.user.UserService;
import org.thingsboard.server.service.utils.JsExecuteUtil;
import org.thingsboard.server.service.utils.JsProcessUtil;
import org.thingsboard.server.service.utils.StringUtils;

import javax.script.ScriptException;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 消息消费者
 */
@Slf4j
@Component
@EnableConfigurationProperties(RabbitMQConfigurationProperties.class)
public class RabbitMqConsumer {

    @Autowired
    private AmqpTemplate amqpTemplate;

    @Autowired
    private LogicalFlowService logicalFlowService;

    @Autowired
    private RabbitMQConfigurationProperties rabbitMQConfigurationProperties;

    @Autowired
    private DataSourceService dataSourceService;

    @Autowired
    private MailService mailService;

    @Autowired
    private ExtraUserService extraUserService;
    
    @Autowired
    private UserService userService;

    @Autowired
    private LogicalFlowHistoryService logicalFlowHistoryService;

    @Autowired
    @Qualifier("logicalFlowContinuousRunningNode")
    private BoundHashOperations<String, Object, Object> logicalFlowContinuousRunningNode;

    @Autowired
    @Qualifier("logicalFlowRunningNode")
    private BoundHashOperations<String, Object, Object> logicalFlowRunningNode;

    @Value("${istar-ipc.data_time_range}")
    private long DATA_TIME_RANGE;

    /**
     * 待执行脚本队列监听器
     * 拿到消息后运行js代码，拿到正确结果后再发送至执行结果消息队列
     *
     * @param message 消息
     */
//    @RabbitListener(queues = "queue_execute_script")
    public void queueExecuteScript(String message, Channel channel, Message msgObj) {
        // 执行结果
        Boolean result = false;

        log.debug("[queue_execute_script] 收到消息: {}", message);

        // 拿到消息
        LogicalFlowMessage logicalFlowMessageBO = JSON.parseObject(message, LogicalFlowMessage.class);
        LogicalFlowNodeBO node = logicalFlowMessageBO.getLogicalFlowNodeBo();

        // 记录节点执行历史
        LogicalFlowNodeHistory nodeHistory = new LogicalFlowNodeHistory();
        nodeHistory.setCreatedTime(System.currentTimeMillis());
        nodeHistory.setStartTime(System.currentTimeMillis());
        nodeHistory.setHistoryId(node.getLogicalFlowHistory().getId());
        nodeHistory.setLogicalFlowNodeId(node.getId());
        nodeHistory.setLogicalFlowNodeName(node.getName());
        nodeHistory.setLogicalFlowId(node.getLogicalFlowId());
        if (node.getIsChildFlow()) {
            nodeHistory.setParentNodeId(node.getParentNodeId());
        }

        // 检查是否需要强制停止
        LogicalFlow logicalFlow = logicalFlowService.findById(node.getLogicalFlowId());
        if (logicalFlow != null && org.apache.commons.lang3.StringUtils.isNotBlank(logicalFlow.getStopForce()) && "1".equals(logicalFlow.getStopForce())) {
            // 设置节点运行结果为强制停止
            nodeHistory.setResult("stop_force");

            logicalFlowRunningNode.delete(node.getId());
            if (node.getParentContinuousNodeId() != null) {
                logicalFlowRunningNode.delete(node.getParentContinuousNodeId().getId());
            }
        } else {
            try {
                // 执行
                if (DataConstants.LogicalFlowNodeType.CONTINUOUS.name().equals(logicalFlowMessageBO.getType())) {// 持续运行节点
                    // 执行该节点
                    String script = getRealScript(node);
                    result = JsExecuteUtil.getBooleanByResult(script);
                    if (result) {
                        recordNode(node);

                        long var = logicalFlowMessageBO.getSleepOrContinuousTime();
                        while (var > 0) {
                            long start = System.currentTimeMillis();
                            // 校验该节点是否以在持续运行中
                            if (checkRunning(node)) {// 运行中
                                // 什么都不做, 等待下一次监测
                            } else {
                                // 执行该节点
                                String realScript = getRealScript(node);
                                result = JsExecuteUtil.getBooleanByResult(realScript);
                                log.info("任务执行完毕. 执行结果: [{} -> {}], result : {}, NodeId: {}", node.getScript(), realScript, result, node.getId());
                                node.setIsChildFlow(true);
                                node.setParentNodeId(node.getParentContinuousNodeId().getId());
                                LogicalFlowResultMessage resultMessage = new LogicalFlowResultMessage(node, result);

                                logicalFlowContinuousRunningNode.put(node.getId(), true);
                                amqpTemplate.convertAndSend(rabbitMQConfigurationProperties.getQueueReceiveResult(), JSON.toJSONString(resultMessage));
                            }
                            long time = System.currentTimeMillis() - start;
                            long temp = var;
                            var = var - 1000L - time;
                            if (temp - 1000 > 0) {
                                Thread.sleep(1000L);
                            } else {
                                Thread.sleep(temp);
                            }
                        }
                    }
                    nodeHistory.setLogicalFlowNodeName(node.getParentContinuousNodeId().getName());
                    nodeHistory.setScript(script);
                    nodeHistory.setLogicalFlowNodeId(node.getParentContinuousNodeId().getId());
                    nodeHistory.setResult(result.toString());


                    // 运行持续节点后的节点
                    LogicalFlowResultMessage resultMessage = new LogicalFlowResultMessage(node.getParentContinuousNodeId(), result);
                    amqpTemplate.convertAndSend(rabbitMQConfigurationProperties.getQueueReceiveResult(), JSON.toJSONString(resultMessage));

                } else if (DataConstants.LogicalFlowNodeType.CONTINUOUS_JUDGMENT.name().equals(logicalFlowMessageBO.getType())) {// 持续判断节点
                    long var = JSON.parseObject(logicalFlowMessageBO.getLogicalFlowNodeBo().getParam()).getLong("time");
                    boolean flag = true;

                    while (var > 0 && flag) {
                        // 执行该节点
                        long start = System.currentTimeMillis();
                        String realScript = getRealScript(node);
                        flag = JsExecuteUtil.getBooleanByResult(realScript);
                        long time = System.currentTimeMillis() - start;

                        log.info("持续判断节点. 执行结果: [{} -> {}], result : {}, NodeId: {}", node.getScript(), realScript, flag, node.getId());
                        long temp = var;
                        var = var - 1000L - time;
                        if (temp - 1000 > 0) {
                            Thread.sleep(1000L);
                        } else {
                            Thread.sleep(temp);
                        }
                    }
                    result = flag;
                    log.info("持续判断节点执行完毕. 执行结果: [{}], result : {}, NodeId: {}", node.getScript(), result, node.getId());
                    // 记录执行的脚本和结果
                    nodeHistory.setScript(node.getScript());
                    nodeHistory.setResult(result.toString());
                    // 节点执行完毕, 发送执行结果到消息队列
                    LogicalFlowResultMessage resultMessage = new LogicalFlowResultMessage(node, result);
                    amqpTemplate.convertAndSend(rabbitMQConfigurationProperties.getQueueReceiveResult(), JSON.toJSONString(resultMessage));

                } else if (DataConstants.LogicalFlowNodeType.WAITING.name().equals(logicalFlowMessageBO.getType())) {// 等待节点
                    // 直接睡眠指定时间
                    Thread.sleep(JSON.parseObject(node.getParam()).getLong("time"));
                    nodeHistory.setResult("true");
                    // 节点执行完毕, 发送执行结果到消息队列
                    LogicalFlowResultMessage resultMessage = new LogicalFlowResultMessage(node, true);
                    amqpTemplate.convertAndSend(rabbitMQConfigurationProperties.getQueueReceiveResult(), JSON.toJSONString(resultMessage));
                } else if(DataConstants.LogicalFlowNodeType.ALARM.name().equals(logicalFlowMessageBO.getType())) {// 报警节点
                    // 获取参数
                    AlarmNodeParam alarmNodeParam = JSON.parseObject(node.getParam(), AlarmNodeParam.class);
                    // 查询上一次发送的短信的时间
                    List<LogicalFlowAlarmNodeHistory> list = logicalFlowHistoryService.findByLogicalFlowNodeIdAndSendFlag(node.getId(), "true", 0, 1);
                    // 目前每小时仅发送一次短信
                    // 保存告警日志
                    LogicalFlowAlarmNodeHistory logicalFlowAlarmNodeHistory = new LogicalFlowAlarmNodeHistory();
                    if (list == null || list.isEmpty() || System.currentTimeMillis() - list.get(0).getCreatedTime() >= 3600000) {
                        sendEmailAndSms(node, alarmNodeParam);
                        logicalFlowAlarmNodeHistory.setSendFlag("true");
                    } else {
                        logicalFlowAlarmNodeHistory.setSendFlag("false");
                    }
                    logicalFlowAlarmNodeHistory.setLogicalFlowId(node.getLogicalFlowId());
                    logicalFlowAlarmNodeHistory.setLogicalFlowNodeId(node.getId());
                    logicalFlowAlarmNodeHistory.setCreatedTime(System.currentTimeMillis());
                    logicalFlowAlarmNodeHistory.setRecipient(node.getParam());

                    logicalFlowHistoryService.save(logicalFlowAlarmNodeHistory);

                    nodeHistory.setResult("true");
                    // 节点执行完毕, 发送执行结果到消息队列
                    LogicalFlowResultMessage resultMessage = new LogicalFlowResultMessage(node, true);
                    amqpTemplate.convertAndSend(rabbitMQConfigurationProperties.getQueueReceiveResult(), JSON.toJSONString(resultMessage));
                }  else {// 间隔轮询或普通节点
                    // 延迟执行
                    if (logicalFlowMessageBO.getSleepOrContinuousTime() != null) {
                        Thread.sleep(logicalFlowMessageBO.getSleepOrContinuousTime());
                    }
                    nodeHistory.setCreatedTime(System.currentTimeMillis());
                    nodeHistory.setStartTime(System.currentTimeMillis());
                    // 执行该节点
                    String realScript = getRealScript(node);
                    result = JsExecuteUtil.getBooleanByResult(realScript);
                    log.info("任务执行完毕. 执行结果: [{} -> {}], result : {}, NodeId: {}", node.getScript(), realScript, result, node.getId());

                    nodeHistory.setScript(realScript);
                    nodeHistory.setResult(result.toString());

                    // 节点执行完毕, 发送执行结果到消息队列
                    LogicalFlowResultMessage resultMessage = new LogicalFlowResultMessage(node, result);
                    amqpTemplate.convertAndSend(rabbitMQConfigurationProperties.getQueueReceiveResult(), JSON.toJSONString(resultMessage));
                }
                // 消息确认
//            channel.basicAck(msgObj.getMessageProperties().getDeliveryTag(), false);
            } catch (ScriptException e) {
                log.error("JS代码片段错误! JS: '{}'", node.getScript());
                nodeHistory.setResult("error");
                nodeHistory.setScript(node.getScript());
                logicalFlowRunningNode.delete(node.getId());
                if (node.getParentContinuousNodeId() != null) {
                    logicalFlowRunningNode.delete(node.getParentContinuousNodeId().getId());
                }
                // 将异常的消息发到队列重新执行
//            channel.basicNack(msgObj.getMessageProperties().getDeliveryTag(), false, true);
            } catch (Exception e) {
                log.error("发生未知异常!", e);
                nodeHistory.setResult("error");
                nodeHistory.setScript(node.getScript());
                logicalFlowRunningNode.delete(node.getId());
                if (node.getParentContinuousNodeId() != null) {
                    logicalFlowRunningNode.delete(node.getParentContinuousNodeId().getId());
                }
                // 将异常的消息发到队列重新执行
//            channel.basicNack(msgObj.getMessageProperties().getDeliveryTag(), false, true);
            }
        }

        // 记录实际运行的结束时间
        nodeHistory.setEndTime(System.currentTimeMillis());
        // 保存执行历史
        logicalFlowHistoryService.save(nodeHistory);
    }

    /**
     * 发送邮件和短信到联系人
     *
     * @param node              节点信息
     * @param alarmNodeParam    节点参数
     */
    private void sendEmailAndSms(LogicalFlowNodeBO node, AlarmNodeParam alarmNodeParam) {
        try {
            // 获取发送的联系人
            List<AlarmContact> externalContact = alarmNodeParam.getExternalContact();
            List<AlarmContact> internalContact = alarmNodeParam.getInternalContact();
            String date = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").format(new Date());

            // 查询外部联系人
            List<ExtraUser> extraUserList = extraUserService.findByTenant(node.getTenantId());
            Map<String, ExtraUser> extraUserMap = new HashMap<>();
            extraUserList.forEach(extraUser -> extraUserMap.put(extraUser.getId(), extraUser));

            // 查询内部联系人
            List<User> internalUserList = userService.findUserByTenant(new TenantId(UUIDConverter.fromString(node.getTenantId())));
            Map<String, User> internalUserMap = new HashMap<>();
            internalUserList.forEach(user -> internalUserMap.put(UUIDConverter.fromTimeUUID(user.getUuidId()), user));

            // 发送给外部联系人
            for (AlarmContact alarmContact : externalContact) {
                ExtraUser user = extraUserMap.get(alarmContact.getId());
                AlarmLinkedUser alarmLinkedUser = AlarmLinkedUser.builder()
                        .userName(user.getName() + "(" + user.getEmail() + ")")
                        .email(user.getEmail())
                        .phone(user.getPhone())
                        .tenantId(new TenantId(UUIDConverter.fromString(user.getTenantId())))
                        .build();
                // 发送
                send(node, date, alarmContact, alarmLinkedUser);
            }

            // 发给内部联系人
            for (AlarmContact alarmContact : internalContact) {
                User user = internalUserMap.get(alarmContact.getId());
                AlarmLinkedUser alarmLinkedUser = AlarmLinkedUser.builder()
                        .userName(user.getFirstName()+"("+user.getName()+")")
                        .email(user.getEmail())
                        .phone(user.getPhone())
                        .tenantId(user.getTenantId())
                        .build();

                // 发送
                send(node, date, alarmContact, alarmLinkedUser);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("发送触发报警邮件或短信失败! ", e);
            logicalFlowRunningNode.delete(node.getId());
        }
    }

    /**
     * 发送
     *
     * @param node              节点信息
     * @param date              日期
     * @param alarmContact      联系人
     * @param alarmLinkedUser   构造的发送联系人对象
     */
    private void send(LogicalFlowNodeBO node, String date, AlarmContact alarmContact, AlarmLinkedUser alarmLinkedUser) {
        String logicalFlowName = "";
        // 判断是否为子流程中的报警
        String parentLogicalFlowName = "";
        if (node.getParentContinuousNodeId() != null) {
            parentLogicalFlowName = node.getParentContinuousNodeId().getLogicalFlowHistory().getLogicalFlowName();
            logicalFlowName = logicalFlowName + parentLogicalFlowName + "#";
        }
        if (StringUtils.checkNotNull(node.getLogicalFlowName())) {
            logicalFlowName = logicalFlowName + node.getLogicalFlowName();
        }

        // 发送邮件
        if (alarmContact.isSendEmail()) {
            String subject = "云端触发报警";
            String body = "触发器触发报警, 请前往系统查看详情。 " +
                    "流程名称：[" + logicalFlowName + "], 流程节点节点名称: [" + node.getName() + "], 触发时间: [" + date + "]";
            mailService.sendEmail(alarmLinkedUser, subject, body, DataConstants.LOG_TYPE_LOGICAL_FLOW_ALARM);
        }
        // 发送短信
        if (alarmContact.isSendSms()) {
            mailService.sendLogicalFlowAlarmToUser(alarmLinkedUser, logicalFlowName + "的" + node.getName() + "节点在" + date + "被触发");
        }
    }

    /**
     * 获取真实执行的脚本（即替换过变量的JS脚本）
     *
     * @param node 当前执行的节点
     * @return JS脚本
     */
    private String getRealScript(LogicalFlowNodeBO node) {
        List<VariableData> variableDataList = new ArrayList<>();
        try {
            Map<String, Map<String, String>> variableMap = JsProcessUtil.processOriginScript(node.getScript());
            // 获取变量的真实数据
            variableDataList = getDataByVariableMap(variableMap);
        } catch (Exception e) {
            log.error("获取变量真实数据异常");
            e.printStackTrace();
        }
        // 替换数据到脚本中
        return JsProcessUtil.replaceData2Script(node.getScript(), variableDataList);
    }

    /**
     * 获取真实的变量数据
     *
     * @param variableMap 变量Map
     * @return 变量数据列表
     */
    private List<VariableData> getDataByVariableMap(Map<String, Map<String, String>> variableMap) {
        List<VariableData> result = new ArrayList<>();
        long now = System.currentTimeMillis();
        // 获取中间变量数据
        if (variableMap.containsKey(DataConstants.dataSourceVariableType.BETWIXT.name())) {
            Map<String, String> betwixtVarMap = variableMap.get(DataConstants.dataSourceVariableType.BETWIXT.name());
            for (Map.Entry<String, String> obj : betwixtVarMap.entrySet()) {
                // 查询中间变量数据
                List<DataFromDataSource> dataByDataSource = dataSourceService
                        .findLastDataByDataSource(new DataSourceRequest(Collections.singletonList(obj.getValue()), 0, 0, null));
                DataFromDataSource dataSource = null;
                if (dataByDataSource != null && !dataByDataSource.isEmpty()) {
                    dataSource = dataByDataSource.get(0);
                }
                if (dataSource != null) {
                    // 已排序好的数据, 直接取第一条
                    DefaultMapObject data = dataSource.getData().get(0);
                    if (data != null) {
                        if (System.currentTimeMillis() - Long.parseLong(data.getTs()) > 5 * 60 * 1000) {// 5分钟算有效数据
                            continue;
                        }
                        VariableData variableData = new VariableData();
                        variableData.setDataSourceId(obj.getValue());
                        variableData.setValue(data.getValue());
                        variableData.setVariable(obj.getKey());
                        result.add(variableData);

                    }

                }
            }
        }

        // 获取设备变量数据
        if (variableMap.containsKey(DataConstants.dataSourceVariableType.DEVICE.name())) {
            Map<String, String> deviceVarMap = variableMap.get(DataConstants.dataSourceVariableType.DEVICE.name());
            for (Map.Entry<String, String> obj : deviceVarMap.entrySet()) {
                // 查询设备变量数据
                DataSourceRequest request = new DataSourceRequest(Collections.singletonList(obj.getValue()), now - DATA_TIME_RANGE, now, "1m");
                List<DataFromDataSource> dataByDataSource = dataSourceService.findDataByDataSource(request);
                if (dataByDataSource != null && !dataByDataSource.isEmpty()) {
                    DataFromDataSource data = dataByDataSource.get(0);
                    VariableData variableData = new VariableData();
                    variableData.setDataSourceId(data.getDataSourceId());
                    List<DefaultMapObject> dataList = data.getData();
                    if (dataList != null && !dataList.isEmpty()) {
                        variableData.setValue(new BigDecimal(dataList.get(dataList.size() - 1).getValue()).toString());
                    }
                    variableData.setVariable(obj.getKey());
                    result.add(variableData);
                }
            }
        }

        return result;
    }

    /**
     * 执行结果队列监听器
     * 获取JS代码执行结果
     *
     * @param message 消息
     */
//    @RabbitListener(queues = "queue_receive_result")
    public void queueReceiveResult(String message, Channel channel, Message msgObj) {
        log.debug("[queue_receive_result] 收到消息: {}", message);
        try {
            // 拿到消息
            LogicalFlowResultMessage resultMessage = JSON.parseObject(message, LogicalFlowResultMessage.class);

            if (resultMessage.getResult()) {// 继续执行子节点
                logicalFlowService.executeChildrenNode(resultMessage.getLogicalFlowNodeBo());
            }
            // 当前节点执行完毕
            logicalFlowRunningNode.delete(resultMessage.getLogicalFlowNodeBo().getId());
            // 消息确认
//            channel.basicAck(msgObj.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            log.error("发生未知异常!", e);
        }
    }


    /**
     * 记录持续节点的子节点执行情况
     *
     * @param logicalFlowNodeBo 父节点
     */
    private void recordNode(LogicalFlowNodeBO logicalFlowNodeBo) {
        if (logicalFlowNodeBo.getChildren() == null || logicalFlowNodeBo.getChildren().size() < 1) {
            return;
        }
        for (LogicalFlowNodeBO child : logicalFlowNodeBo.getChildren()) {
            logicalFlowContinuousRunningNode.put(child.getId(), false);
            recordNode(child);
        }
    }

    /**
     * 校验父节点的所有子节点是否处于运行状态
     *
     * @param logicalFlowNodeBO 父节点
     * @return boolean
     */
    private Boolean checkRunning(LogicalFlowNodeBO logicalFlowNodeBO) {
        List<LogicalFlowNodeBO> logicalFlowNodeBOList = new ArrayList<>();
        // 获取所有的节点列表
        getNodeByParent(logicalFlowNodeBO, logicalFlowNodeBOList);

        int count = 5000;
        while (count > 0) {
            for (LogicalFlowNodeBO flowNodeBO : logicalFlowNodeBOList) {
                // redis版本处理
                if (logicalFlowContinuousRunningNode.hasKey(flowNodeBO.getId())
                        && (Boolean) logicalFlowContinuousRunningNode.get(flowNodeBO.getId())) {
                    return true;
                }
                // 本地map处理
                /*if (DataConstants.continuousRunningNode.containsKey(flowNodeBO.getId())
                        && DataConstants.continuousRunningNode.get(flowNodeBO.getId())) {
                    return true;
                }*/
            }
            count--;
        }

        return false;
    }

    /**
     * 从树结构获取平行的子节点列表
     *
     * @param logicalFlowNodeBO 父节点
     * @param list              保存子节点的LIST
     */
    private void getNodeByParent(LogicalFlowNodeBO logicalFlowNodeBO, List<LogicalFlowNodeBO> list) {
        List<LogicalFlowNodeBO> children = logicalFlowNodeBO.getChildren();
        if (children != null) {
            list.addAll(logicalFlowNodeBO.getChildren());
            for (LogicalFlowNodeBO child : children) {
                getNodeByParent(child, list);
            }
        }
    }

}
