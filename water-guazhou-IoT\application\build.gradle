/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import org.apache.tools.ant.filters.ReplaceTokens

buildscript {
    ext {
        osPackageVersion = "8.6.0"
    }
    repositories {
        gradlePluginPortal()
        mavenCentral()
    }
    dependencies {
        classpath("com.netflix.nebula:gradle-ospackage-plugin:${osPackageVersion}")
    }
}
apply plugin: "nebula.ospackage"

buildDir = projectBuildDir
version = projectVersion
distsDirName = "./"

// OS Package plugin configuration
ospackage {
    packageName = pkgName
    version = "${project.version}"
    release = 1
    os = LINUX
    type = BINARY

    into pkgInstallFolder

    user pkgName
    permissionGroup pkgName

    // Copy the actual .jar file
    from(mainJar) {
        // Strip the version from the jar filename
        rename { String fileName ->
            "${pkgName}.jar"
        }
        fileMode 0500
        into "bin"
    }

    // Copy the install files
    from("target/bin/install/install.sh") {
        fileMode 0775
        into "bin/install"
    }

    from("target/bin/install/upgrade.sh") {
        fileMode 0775
        into "bin/install"
    }

    from("target/bin/install/logback.xml") {
        into "bin/install"
    }

    // Copy the config files
    from("target/conf") {
        exclude "${pkgName}.conf"
        fileType CONFIG | NOREPLACE
        fileMode 0754
        into "conf"
    }

    // Copy the data files
    from("target/data") {
        fileType CONFIG | NOREPLACE
        fileMode 0754
        into "data"
    }

    // Copy the extensions files
    from("target/extensions") {
        into "extensions"
    }
}

// Configure our RPM build task
buildRpm {

    arch = NOARCH

    version = projectVersion.replace('-', '')
    archiveName = "${pkgName}.rpm"

    requires("java-1.8.0")

    from("target/conf") {
        include "${pkgName}.conf"
        filter(ReplaceTokens, tokens: ['pkg.platform': 'rpm'])
        fileType CONFIG | NOREPLACE
        fileMode 0754
        into "${pkgInstallFolder}/conf"
    }

    preInstall file("${buildDir}/control/rpm/preinst")
    postInstall file("${buildDir}/control/rpm/postinst")
    preUninstall file("${buildDir}/control/rpm/prerm")
    postUninstall file("${buildDir}/control/rpm/postrm")

    user pkgName
    permissionGroup pkgName

    // Copy the system unit files
    from("${buildDir}/control/${pkgName}.service") {
        addParentDirs = false
        fileMode 0644
        into "/usr/lib/systemd/system"
    }

    directory(pkgLogFolder, 0755)
    link("${pkgInstallFolder}/bin/${pkgName}.yml", "${pkgInstallFolder}/conf/${pkgName}.yml")
    link("/etc/${pkgName}/conf", "${pkgInstallFolder}/conf")
}

// Same as the buildRpm task
buildDeb {

    arch = "all"

    archiveName = "${pkgName}.deb"

    requires("openjdk-8-jre").or("java8-runtime").or("oracle-java8-installer").or("openjdk-8-jre-headless")

    from("target/conf") {
        include "${pkgName}.conf"
        filter(ReplaceTokens, tokens: ['pkg.platform': 'deb'])
        fileType CONFIG | NOREPLACE
        fileMode 0754
        into "${pkgInstallFolder}/conf"
    }

    configurationFile("${pkgInstallFolder}/conf/${pkgName}.conf")
    configurationFile("${pkgInstallFolder}/conf/${pkgName}.yml")
    configurationFile("${pkgInstallFolder}/conf/logback.xml")
    configurationFile("${pkgInstallFolder}/conf/actor-system.conf")

    preInstall file("${buildDir}/control/deb/preinst")
    postInstall file("${buildDir}/control/deb/postinst")
    preUninstall file("${buildDir}/control/deb/prerm")
    postUninstall file("${buildDir}/control/deb/postrm")

    user pkgName
    permissionGroup pkgName

    directory(pkgLogFolder, 0755)
    link("/etc/init.d/${pkgName}", "${pkgInstallFolder}/bin/${pkgName}.jar")
    link("${pkgInstallFolder}/bin/${pkgName}.yml", "${pkgInstallFolder}/conf/${pkgName}.yml")
    link("/etc/${pkgName}/conf", "${pkgInstallFolder}/conf")
}
