import{_ as B}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as E}from"./CardTable-rdWOL4_6.js";import{_ as H}from"./CardSearch-CB_HNR-Q.js";import{d as R,M as O,r as Y,c as _,a8 as y,s as A,S as I,am as $,bF as s,bB as j,bT as z,D as L,bu as W,g as G,n as Q,q as U,i as M,b7 as J}from"./index-r0dFAfgr.js";import{I as q}from"./common-CvK_P_ao.js";import{g as K,h as X,i as Z,j as w,f as ee}from"./waterInspection-DqEu1Oyl.js";import{u as te}from"./useDepartment-BkP08hh6.js";import{u as ae}from"./useStation-DJgnSZIA.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";import"./zhandian-YaGuQZe6.js";const re={class:"wrapper"},ge=R({__name:"index",setup(le){const{$messageSuccess:x,$messageError:b}=O(),{getAllStationOption:k}=ae(),{$btnPerms:h}=O(),{getDepartmentTree:S}=te(),i=Y({departmentTree:[],templateList:[],stationOptionList:[],users:[],startDate:"",intervalDays:null,executionNum:null,executionDays:null}),f=_(),p=_(),N=_("周期性任务"),D=Y({filters:[{label:"计划名称",field:"name",type:"input",placeholder:"请输入计划名称"},{label:"巡检部门",field:"executionUserDepartmentId",type:"select-tree",checkStrictly:!0,options:y(()=>i.departmentTree),onChange:async e=>{var a,r,l;console.log(e);const t=(a=D.filters)==null?void 0:a.find(o=>o.field==="executionUserId");if(e){const o=await T(e);t.options=o}else t.options=[];D.defaultParams={...D.defaultParams,...(r=f.value)==null?void 0:r.queryParams,executionUserId:"",executionUserDepartmentId:e},(l=f.value)==null||l.resetForm()}},{label:"巡检人员",field:"executionUserId",type:"select",placeholder:"请选择巡检人员"},{label:"创建人员",type:"department-user",field:"creator"},{label:"审核人员",type:"department-user",field:"auditUserId"},{label:"开始时间",field:"startDateFrom",type:"daterange"},{label:"结束时间",field:"endDateFrom",type:"daterange"}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",icon:q.QUERY,click:()=>m()},{type:"default",perm:!0,text:"重置",svgIcon:A(J),click:()=>{var e;D.defaultParams={executionUserDepartmentId:""},(e=f.value)==null||e.resetForm()}},{perm:h("RoleManageAdd"),text:"新增",type:"success",icon:q.ADD,click:()=>{n.title="新增",v()}},{perm:h("RoleManageAdd"),text:"批量删除",type:"danger",icon:q.DELETE,click:()=>{I("确定批量删除吗？","提示信息").then(()=>{var t;const e=(t=u.selectList)==null?void 0:t.map(a=>a.id);console.log(e),K(e).then(a=>{var r;((r=a.data)==null?void 0:r.code)===200?(x("删除成功"),m()):b("删除失败")}).catch(a=>{b(a)})}).catch(()=>{})}}]}]});$(()=>[i.startDate,i.intervalDays,i.executionNum,i.executionDays],([e,t,a,r])=>{var l,o,d,c;console.log(e,t,a,r),n.defaultValue={...n.defaultValue,...(o=(l=p.value)==null?void 0:l.refForm)==null?void 0:o.dataForm,endDate:s(e).add(Number(t)+Number(a)+Number(r),"day").format("YYYY-MM-DD")},(c=(d=p.value)==null?void 0:d.refForm)==null||c.resetForm()});const u=Y({loading:!0,defaultExpandAll:!0,indexVisible:!0,selectList:[],columns:[{label:"计划名称",prop:"name"},{label:"计划类型",prop:"planType"},{label:"采用模板",prop:"templateName"},{label:"巡检人员",prop:"executionUserName"},{label:"开始日期",prop:"startDate",formatter:(e,t)=>t?s(t).format("YYYY-MM-DD"):"-"},{label:"结束日期",prop:"endDate",formatter:e=>e.endDate?s(e.endDate).format("YYYY-MM-DD"):s(e.fixedDate).format("YYYY-MM-DD")},{label:"巡检时间",prop:"fixedDate",formatter:(e,t)=>t?s(t).format("YYYY-MM-DD"):"-"},{label:"消耗天数",prop:"executionDays",formatter:(e,t)=>t||"1"},{label:"间隔天数",prop:"intervalDays",formatter:(e,t)=>t||"0"},{label:"执行次数",prop:"executionNum",formatter:(e,t)=>t||"1"},{label:"创建人",prop:"creatorName"},{label:"创建时间",prop:"createTime",formatter:(e,t)=>s(t).format("YYYY-MM-DD HH:mm:ss")}],operations:[{type:"primary",isTextBtn:!0,color:"#4195f0",text:"详情",perm:h("RoleManageEdit"),click:e=>{n.title="详情",v(e)}},{isTextBtn:!0,type:"danger",text:"删除",perm:h("RoleManageDelete"),click:e=>V(e)}],dataList:[],pagination:{page:1,limit:20,total:0,refreshData:({page:e,size:t})=>{u.pagination.page=e,u.pagination.limit=t,m()}},handleSelectChange:e=>{console.log(e),u.selectList=e}}),n=Y({title:"新增",labelWidth:"100px",dialogWidth:800,defaultValue:{planType:"周期性任务",endDate:""},group:[{fields:[{xs:20,type:"radio",label:"计划类别",field:"planType",noBorder:!1,options:[{label:"周期性任务",value:"周期性任务"},{label:"固定日期任务",value:"固定日期任务"}],onChange:e=>{j(()=>{var t,a;e==="固定日期任务"?n.defaultValue={planType:"周期性任务",executionDays:null,intervalDays:null,executionNum:null}:(a=(t=p.value)==null?void 0:t.refForm)==null||a.resetForm(),N.value=e})}},{xs:12,type:"input",label:"计划名称",field:"name",rules:[{required:!0,message:"请输入计划名称"}]},{xs:12,type:"select",label:"巡检模板",field:"templateId",options:y(()=>i.templateList),rules:[{required:!0,message:"请选择模板"}]},{xs:12,type:"date",label:"选择日期",field:"fixedDate",format:"YYYY-MM-DD",handleHidden:(e,t,a)=>{a.hidden=e.planType==="周期性任务"},rules:[{required:!0,message:"请选择任务日期"}]},{xs:12,type:"date",label:"开始时间",field:"startDate",format:"YYYY-MM-DD",min:s().format("YYYY-MM-DD"),handleHidden:(e,t,a)=>{a.hidden=e.planType==="固定日期任务"},rules:[{required:!0,message:"请输入计划开始时间"}],onChange:e=>{i.startDate=e}},{xs:12,type:"date",readonly:!0,label:"结束时间",field:"endDate",format:"YYYY-MM-DD",handleHidden:(e,t,a)=>{a.hidden=e.planType==="固定日期任务"}},{xs:12,type:"select-tree",label:"巡检部门",clearable:!1,checkStrictly:!0,field:"executionUserDepartmentId",options:y(()=>i.departmentTree),rules:[{required:!0,message:"请选择巡检部门"}],onChange:async e=>{const t=n.group[0].fields.find(a=>a.field==="executionUserId");if(e){console.log("4444");const a=await T(e);t.options=a}else t.options=[];n.defaultValue={...n.defaultValue,executionUserId:"",executionUserDepartmentId:e}}},{xs:12,type:"select",label:"巡检人员",clearable:!1,field:"executionUserId",rules:[{required:!0,message:"请选择巡检人员"}]},{xs:12,type:"select-tree",label:"审核部门",clearable:!1,checkStrictly:!0,options:y(()=>i.departmentTree),field:"auditUserIdDepartmentId",rules:[{required:!0,message:"请选择审核部门"}],onChange:async e=>{const t=n.group[0].fields.find(a=>a.field==="auditUserId");if(e){console.log("5555");const a=await T(e);t.options=a}else t.options=[];n.defaultValue={...n.defaultValue,auditUserId:"",auditUserIdDepartmentId:e}}},{xs:12,type:"select",label:"审核人员",field:"auditUserId",clearable:!1,rules:[{required:!0,message:"请选择审核人员"}]},{xs:8,type:"input-number",label:"消耗天数",field:"executionDays",handleHidden:(e,t,a)=>{a.hidden=e.planType==="固定日期任务"},onChange:e=>{i.executionDays=e},rules:[{required:!0,message:"请输入执行天数"}]},{xs:8,type:"input-number",label:"间隔天数",field:"intervalDays",handleHidden:(e,t,a)=>{a.hidden=e.planType==="固定日期任务"},onChange:e=>{i.intervalDays=e},rules:[{required:!0,message:"请输入间隔时间"}]},{xs:8,type:"input-number",label:"执行次数",field:"executionNum",handleHidden:(e,t,a)=>{a.hidden=e.planType==="固定日期任务"},onChange:e=>{i.executionNum=e},rules:[{required:!0,message:"请输入执行次数"}]},{type:"checkbox",field:"",label:"水源列表",rules:[{required:!0}]},{type:"checkbox",field:"stationIds",label:"",rules:[{required:!0,message:"请选择水源列表"}],noBorder:!1,colStyles:{height:"120px",padding:"10px 10px"},options:y(()=>i.stationOptionList)},{xs:100,type:"textarea",minRow:5,label:"备注",field:"remark",colStyles:{marginTop:"20px"}}]}]}),v=e=>{var t,a;n.group.map(r=>{r.fields.map(l=>{l.readonly=!!e,l.field==="endDate"&&(l.readonly=!0)})}),e?(n.submit=void 0,n.defaultValue={...e,stationIds:(t=e.stationIds)==null?void 0:t.split(","),startDate:s(e==null?void 0:e.startDate).format(),endDate:s(e==null?void 0:e.endDate).format(),fixedDate:s(e==null?void 0:e.fixedDate).format(),auditUserId:e.auditUserName,executionUserId:e.executionUserName}):(n.defaultValue={planType:"周期性任务"},n.submit=r=>{I("确定提交？","提示信息").then(()=>{r={...r,type:"水源",fixedDate:r.fixedDate?s(r==null?void 0:r.fixedDate).valueOf():void 0,startDate:r.startDate?s(r==null?void 0:r.startDate).valueOf():void 0,endDate:r.endDate?s(r==null?void 0:r.endDate).valueOf():void 0,stationIds:r.stationIds.join(",")},X(r).then(l=>{var o,d,c,g;((o=l.data)==null?void 0:o.code)===200?((c=(d=p.value)==null?void 0:d.refForm)==null||c.resetForm(),(g=p.value)==null||g.closeDialog(),m(),x("保存成功！")):b("保存失败！")}).catch(l=>{b(l)})}).catch(()=>{})}),(a=p.value)==null||a.openDialog()},V=e=>{I("确定删除该计划, 是否继续?","删除提示").then(()=>{Z(e.id).then(()=>{m(),x("删除成功")}).catch(t=>{x(t.data.message)})})},m=async()=>{var c,g,C,F;u.loading=!0;const e=((c=f.value)==null?void 0:c.queryParams)||{startDate:[],endDate:[]};console.log((g=f.value)==null?void 0:g.queryParams);const[t,a]=e.startDateFrom||[],[r,l]=e.endDateFrom||[],o={...e,type:"水源",startDateFrom:t?s(t).startOf("day").valueOf():null,startDateTo:a?s(a).endOf("day").valueOf():null,endDateFrom:r?s(r).startOf("day").valueOf():null,endDateTo:l?s(l).endOf("day").valueOf():null,page:u.pagination.page||1,size:u.pagination.limit||20},d=await w(o);u.pagination.total=(C=d.data)==null?void 0:C.data.total,u.dataList=(F=d.data)==null?void 0:F.data.data,u.loading=!1},T=async e=>{var l,o;const a=(o=(l=(await z({pid:e,status:1,page:1,size:999})).data)==null?void 0:l.data)==null?void 0:o.data;return a==null?void 0:a.map(d=>({id:L(d.id.id),label:d.firstName,value:L(d.id.id),isLeaf:!0}))},P=async()=>{var t,a;return(a=(t=(await ee({page:1,size:999,type:"水源"})).data)==null?void 0:t.data)==null?void 0:a.data};return W(async()=>{i.departmentTree=await S(2);const e=await P();i.templateList=e.map(t=>({id:t.id,label:t.name,value:t.id})),i.stationOptionList=await k("水源地"),await m()}),(e,t)=>{const a=H,r=E,l=B;return G(),Q("div",re,[U(a,{ref_key:"refSearch",ref:f,config:M(D)},null,8,["config"]),U(r,{config:M(u),class:"card-table"},null,8,["config"]),U(l,{ref_key:"refDialogForm",ref:p,config:M(n)},null,8,["config"])])}}});export{ge as default};
