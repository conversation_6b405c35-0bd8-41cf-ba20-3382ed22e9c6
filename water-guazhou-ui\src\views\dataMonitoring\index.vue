<template>
  <!-- 数据监测 -->
  <TreeBox v-loading="totalLoading">
    <template #tree>
      <SLTree :tree-data="TreeData" />
    </template>
    <CardSearch ref="cardSearch" :config="cardSearchConfig" />
    <CardTable ref="cardTable" class="card-table" :config="cardTableConfig" />
  </TreeBox>
</template>

<script lang="ts" setup>
import { onBeforeUnmount, onMounted, reactive, ref } from 'vue';
import { removeSlash } from '@/utils/removeIdSlash';
import { getDevice, getDatasList } from '@/api/device';
import { ICONS } from '@/common/constans/common';
import useDevice from '@/hooks/device/useDevice';
import { useProject } from './composible/useHooks';
import TreeBox from '../layout/treeOrDetailFrame/TreeBox.vue';
import { formatDate } from '@/utils/DateFormatter';

const { getDeviceTypeTree } = useDevice();
const totalLoading = ref(false);
const deviceList = ref<any[]>([]);
const refreshInterval = ref<any>();
const cardTable = ref<ICardTableIns>();
const cardSearch = ref<ICardSearchIns>();
const cardSearchConfig = reactive<ISearch>({
  defaultParams: {
    group: ''
  },
  filters: [
    {
      type: 'select',
      field: 'deviceType',
      labelWidth: 80,
      label: '设备类型',
      options: [],
      onChange: (val: string) => initSonsorOptions(val)
    },
    {
      label: '采集器',
      labelWidth: 60,
      field: 'sensorId',
      type: 'select',
      options: []
    },
    {
      label: '设备',
      labelWidth: 40,
      field: 'group',
      type: 'select',
      options: [{ label: '全部', value: '' }]
    },
    {
      type: 'btn-group',
      label: '',
      btns: [
        {
          perm: true,
          text: '查询',
          icon: ICONS.QUERY,
          click: () => {
            refreshData();
            refreshInterval.value && clearInterval(refreshInterval.value);
            refreshInterval.value = setInterval(() => refreshData(), 30 * 1000);
          }
        },
        {
          perm: true,
          text: '导出',
          type: 'warning',
          icon: ICONS.EXPORT,
          click: () => exportData(),
          disabled: () =>
            !cardTableConfig.selectList ||
            cardTableConfig.selectList.length === 0
        }
      ]
    }
  ]
});
const cardTableConfig = reactive<ITable>({
  loading: false,
  dataList: [],
  indexVisible: true,
  columns: [
    // { prop: 'serialNumber', label: '测量设备点位' },
    // { prop: 'serialNumber', label: '探头ID' },
    {
      prop: 'collectionTime',
      label: '最后更新时间',
      icon: 'iconfont icon-shijian',
      formatter: (row) => formatDate(row.collectionTime),
      iconStyle: {
        color: '#69e850'
      }
    },
    { prop: 'propertyName', label: '监测量' },
    {
      prop: 'value',
      label: '监测值'
      // formatter: row => (row.value || '-') + row.unit
      // cellStyle: {
      //   color: 'darkblue',
      //   'font-weight': 600,
      //   'font-size': '20px'
      // }
    }
  ],
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    handleSize: (val) => {
      cardTableConfig.pagination.limit = val;
      refreshData();
    },
    handlePage: (val) => {
      cardTableConfig.pagination.page = val;
      refreshData();
    }
  },
  handleSelectChange: (rows: any[]) => {
    cardTableConfig.selectList = rows || [];
  }
});
// 点击项目 获取项目下的主机从机
const getProjectData = (data) => {
  // 设置当前选中项目信息
  TreeData.currentProject = data;
  getDevice(data.id).then((res) => {
    const filter = cardSearchConfig.filters?.find(
      (item) => item.field === 'sensorId'
    ) as IFormSelect;
    if (res.data && res.data.length > 0) {
      deviceList.value = res.data?.map((item) => {
        return {
          label: item.name,
          value: removeSlash(item.id.id),
          type: item.deviceTypeName
        };
      });
      filter.options = deviceList.value;
      if (cardSearch.value) {
        cardSearch.value.queryParams &&
          (cardSearch.value.queryParams.sensorId = removeSlash(
            res.data[0].id.id
          ));
      } else {
        cardSearchConfig.defaultParams = {
          group: '',
          sensorId: removeSlash(res.data[0].id.id)
        };
      }
      refreshData();
      // getDeviceVarGroup(removeSlash(res.data[0].id.id))
    } else {
      filter && filter.type === 'select' && (filter.options = []);
      cardTableConfig.dataList = [];
      cardSearch.value?.queryParams &&
        (cardSearch.value.queryParams.sensorId = '');
    }
  });
};
const exportData = () => {
  cardTable.value?.exportTable();
};
const initDeviceType = async () => {
  const filter = cardSearchConfig.filters?.find(
    (item) => item.field === 'deviceType'
  );
  filter?.type === 'select' && (filter.options = await getDeviceTypeTree());
};
const initSonsorOptions = (type?: string) => {
  const filter = cardSearchConfig.filters?.find(
    (item) => item.field === 'sensorId'
  );
  if (filter?.type === 'select') {
    if (type) {
      filter.options = deviceList.value.filter((item) => item.type === type);
    } else {
      filter.options = deviceList.value || [];
    }
    cardSearch.value?.queryParams &&
      (cardSearch.value.queryParams.sensorId =
        (filter?.options?.length && filter.options[0].value) || '');
  }
  refreshData();
};
const refreshData = async () => {
  cardTableConfig.selectList = [];
  cardTableConfig.loading = true;
  const paramsObj = {
    page: cardTableConfig.pagination.page,
    size: cardTableConfig.pagination.limit,
    projectId: TreeData.currentProject.id,
    sensorId: '',
    group: ''
  };
  if (cardSearch.value) {
    Object.assign(paramsObj, cardSearch.value.queryParams);
  } else {
    Object.assign(paramsObj, cardSearchConfig.defaultParams);
  }

  if (!paramsObj.sensorId) return (cardTableConfig.loading = false);

  const res = await getDatasList(paramsObj.sensorId, {
    group: paramsObj.group,
    page: paramsObj.page,
    size: paramsObj.size
  });
  cardTableConfig.loading = false;

  cardTableConfig.dataList = res.data.data;
  cardTableConfig.pagination.total = res.data.total;
};
const { TreeData, refreshProject } = useProject(async (data) => {
  await getProjectData(data);
});
onBeforeUnmount(() => {
  refreshInterval.value && clearInterval(refreshInterval.value);
});
onMounted(() => {
  refreshProject();
  initDeviceType();
});
</script>

<style lang="scss" scoped></style>
