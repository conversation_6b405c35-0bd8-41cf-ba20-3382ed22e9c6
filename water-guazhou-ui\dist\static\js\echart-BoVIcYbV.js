import{j as p,a7 as s,l as f}from"./index-r0dFAfgr.js";import"./config-DqqM5K5L.js";const u=(t,l)=>{const o=[],i=t==null?void 0:t.map(n=>(o.push(n.value),n.key)),e=[];let r=!1,a=0;return e.length>0&&(a=Math.floor(100/(e.length/3)),e.length>3?r=!0:r=!1),{backgroundColor:p().isDark?"#131624":"#F4F7FA",tooltip:{trigger:"item"},grid:{left:50,top:30,right:30,bottom:50},legend:{show:!0,icon:"circle",orient:"horizontal",top:"90.5%",right:"center",itemWidth:16.5,itemHeight:6,textStyle:{color:"#666",fontSize:14}},dataZoom:[{type:"slider",realtime:!0,start:0,end:a,height:5,left:5,right:5,bottom:10,show:r,fillerColor:"rgba(17, 100, 210, 0.42)",borderColor:"rgba(17, 100, 210, 0.12)",handleSize:0,showDetail:!1,zoomLock:!0,moveOnMouseMove:!1,startValue:0,endValue:6,minValueSpan:6,maxValueSpan:6},{type:"inside",start:0,end:a,zoomOnMouseWheel:!1,moveOnMouseWheel:!0,moveOnMouseMove:!0}],xAxis:[{data:i,axisLabel:{textStyle:{color:"#666",fontSize:12},margin:30},axisLine:{show:!0,lineStyle:{color:"#666"}},axisTick:{show:!1},boundaryGap:!0,splitLine:{show:!1,width:.08,lineStyle:{type:"solid",color:"#666"}}}],yAxis:[{name:l,splitLine:{show:!0,lineStyle:{color:"#666",type:"dashed"}},axisTick:{show:!1},axisLine:{show:!0,lineStyle:{color:"#666"}},axisLabel:{textStyle:{color:"#666",fontSize:12}}}],series:[{name:"",type:"pictorialBar",symbolSize:[40,20],symbolOffset:[0,10],z:12,itemStyle:{normal:{color:new s(0,0,0,1,[{offset:0,color:"#02D6EA"},{offset:1,color:"#02D6EA"}],!1)}},data:o},{name:"",type:"bar",barWidth:40,label:{show:!0,position:"top",offset:[0,-10]},itemStyle:{normal:{color:{x:0,y:0,x2:0,y2:1,type:"linear",global:!1,colorStops:[{offset:0,color:"#057DFE"},{offset:1,color:"#02D7EA"}]}}},data:o},{name:"",type:"pictorialBar",symbolSize:[40,20],symbolOffset:[0,-10],z:12,symbolPosition:"end",itemStyle:{normal:{color:new s(0,0,0,1,[{offset:0,color:"#50A7FF"},{offset:1,color:"#02D6EA"}],!1)}},data:o}]}},x=(t,l)=>{const o=[],i=t==null?void 0:t.map((r,a)=>{var n;const m=((n=r.data)==null?void 0:n.map(c=>(a===0&&o.push(f(c.from).format(l==="year"?"YYYY-MM":l==="month"?"YYYY-MM-DD":l==="day"?"YYYY-MM-DD HH:mm":"YYYY-MM-DD HH:mm:ss")),c.value)))||[];return{name:r.key,type:"line",data:m,smooth:!0,markPoint:{data:[{type:"max",name:"Max"},{type:"min",name:"Min"}]}}});return{backgroundColor:p().isDark?"#131624":"#F4F7FA",color:["#318DFF","#A431FF","#FC2B2B"],grid:{left:40,right:30,top:60,bottom:20},legend:{top:10,textStyle:{color:"#666",fontSize:14}},tooltip:{trigger:"axis"},xAxis:{type:"category",boundaryGap:!1,data:o},yAxis:{name:"事件数量",type:"value",splitLine:{show:!0,lineStyle:{color:"#666",type:"dashed"}},axisLine:{show:!0,lineStyle:{color:"#666"}},axisLabel:{textStyle:{color:"#666",fontSize:12}}},series:i}},b=(t,l)=>{const o=(t==null?void 0:t.map(e=>({name:e.key,value:e.value})))||[];return{tooltip:{trigger:"item",formatter:"{a} <br/>{b} : {c}件 ({d}%)"},legend:{type:"scroll",orient:"vertical",right:30,top:20,bottom:20,textStyle:{color:p().isDark?"#eee":"#666"}},series:[{name:l,type:"pie",radius:["40%","60%"],center:["45%","50%"],avoidLabelOverlap:!1,itemStyle:{borderRadius:5,borderColor:"transparent",borderWidth:2},label:{itemStyle:{color:"#fff"},formatter:"{b} : {c} ({d}%)"},emphasis:{label:{show:!0,fontSize:16,fontWeight:"bold"}},data:o}]}},g=t=>{const l=[],o=t.map(e=>(l.push(e.value),e.key));return{tooltip:{trigger:"item"},grid:{left:40,top:40,bottom:40,right:30},xAxis:{data:o,interval:"auto"},yAxis:{name:"件",axisLine:{show:!1},axisTick:{show:!1},axisLabel:{color:"#999"},splitLine:{show:!0,lineStyle:{color:"#666",type:"dashed"}}},dataZoom:[{type:"inside"}],series:[{type:"bar",showBackground:!0,itemStyle:{color:new s(0,0,0,1,[{offset:0,color:"#188df0"},{offset:.5,color:"#188df0"},{offset:1,color:"#83bff6"}])},barMaxWidth:40,emphasis:{itemStyle:{color:new s(0,0,0,1,[{offset:0,color:"#83bff6"},{offset:.7,color:"#2378f7"},{offset:1,color:"#2378f7"}])}},data:l}]}},h=[{minWidth:150,label:"事件类型",prop:"eventType"},{minWidth:100,label:"小计",prop:"total"},{minWidth:100,label:"一月",prop:"01"},{minWidth:100,label:"二月",prop:"02"},{minWidth:100,label:"三月",prop:"03"},{minWidth:100,label:"四月",prop:"04"},{minWidth:100,label:"五月",prop:"05"},{minWidth:100,label:"六月",prop:"06"},{minWidth:100,label:"七月",prop:"07"},{minWidth:100,label:"八月",prop:"08"},{minWidth:100,label:"九月",prop:"09"},{minWidth:100,label:"十月",prop:"10"},{minWidth:100,label:"十一月",prop:"11"},{minWidth:100,label:"十二月",prop:"12"}],S=t=>{const l=h.map(e=>e.label),o=(t==null?void 0:t.map(e=>({name:e.eventType,type:"line",data:[e["01"],e["02"],e["03"],e["04"],e["05"],e["06"],e["07"],e["08"],e["09"],e["010"],e[11],e[12]],smooth:!0,markPoint:{data:[{type:"max",name:"Max"},{type:"min",name:"Min"}]}})))||[];return{grid:{left:40,right:30,top:70,bottom:40},legend:{right:30,textStyle:{color:"#666",fontSize:14}},tooltip:{trigger:"axis"},xAxis:{type:"category",boundaryGap:!1,data:l},yAxis:{name:"事件",type:"value",splitLine:{show:!0,lineStyle:{color:"#666",type:"dashed"}},axisLine:{show:!0,lineStyle:{color:"#666"}},axisLabel:{textStyle:{color:"#666",fontSize:12}}},series:o}};export{g as B,h as E,S as L,b as P,x as a,u as z};
