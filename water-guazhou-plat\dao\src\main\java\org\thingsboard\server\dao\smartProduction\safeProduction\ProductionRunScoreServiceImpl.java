package org.thingsboard.server.dao.smartProduction.safeProduction;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.request.SafeProductionRequest;
import org.thingsboard.server.dao.model.sql.smartProduction.safeProduction.ProductionRunScore;
import org.thingsboard.server.dao.sql.smartProduction.safeProduction.ProductionRunScoreMapper;

import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Slf4j
@Service
public class ProductionRunScoreServiceImpl implements ProductionRunScoreService {

    @Autowired
    private ProductionRunScoreMapper productionRunScoreMapper;

    @Override
    public List<ProductionRunScore> findList(SafeProductionRequest request) {
        List<ProductionRunScore> list = productionRunScoreMapper.findList(request);
        return list;
    }

    @Override
    public String batchSave(List<ProductionRunScore> productionRunScores) {
        ProductionRunScore productionRunScore = productionRunScores.get(0);
        String year = productionRunScore.getMonth().split("-")[0];
        QueryWrapper<ProductionRunScore> deleteWrapper = new QueryWrapper<>();
        deleteWrapper.like("month", year);

        productionRunScoreMapper.delete(deleteWrapper);

        ExecutorService executorService = Executors.newCachedThreadPool();
        for (ProductionRunScore runScore : productionRunScores) {
            executorService.execute(() -> {
                runScore.setCreateTime(new Date());
                productionRunScoreMapper.insert(runScore);
            });
        }

        executorService.shutdown();
        while (!executorService.isTerminated()) {

        }
        return "保存成功";
    }

    @Override
    public List<JSONObject> yearCount(String year) {
        QueryWrapper<ProductionRunScore> queryWrapper = new QueryWrapper<>();
        queryWrapper.like("month", year);
        List<ProductionRunScore> productionRunScores = productionRunScoreMapper.selectList(queryWrapper);
        Map<String, Map<String, ProductionRunScore>> typeDataMap = new HashMap<>();

        productionRunScores.forEach(productionRunScore -> {
            String type = productionRunScore.getType();
            String month = productionRunScore.getMonth();
            Map<String, ProductionRunScore> dataMap = typeDataMap.get(type);
            if (dataMap == null) {
                dataMap = new HashMap<>();
                typeDataMap.put(type, dataMap);
            }
            dataMap.put(month, productionRunScore);
        });
        List<JSONObject> result = new ArrayList<>();
        for (int i = 1; i <= 12; i++) {
            String month = i < 10 ? "0" + i : i + "";
            month = year + "-" + month;
            JSONObject data = new JSONObject();
            data.put("month", month);
            for (int j = 1; j <= 3; j++) {
                data.put(j + "", 0);
                try {
                    data.put(j + "", typeDataMap.get(j + "").get(month).getScore());
                } catch (Exception e) {

                }
            }
            result.add(data);
        }

        return result;
    }

    @Override
    public List<JSONObject> yearSum(String year) {
        String lastYear = Integer.valueOf(year) - 1 + "";

        List<JSONObject> jsonObjectList = productionRunScoreMapper.yearSum(year);
        List<JSONObject> lastJsonObjectList = productionRunScoreMapper.yearSum(lastYear);

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("year", year);
        for (JSONObject object : jsonObjectList) {
            jsonObject.put(object.getString("type"), object.getDouble("value"));
        }
        JSONObject lastJsonObject = new JSONObject();
        lastJsonObject.put("year", lastYear);
        for (JSONObject object : lastJsonObjectList) {
            lastJsonObject.put(object.getString("type"), object.getDouble("value"));
        }

        List<JSONObject> result = new ArrayList<>();
        result.add(lastJsonObject);
        result.add(jsonObject);

        return result;
    }

    @Override
    public List<JSONObject> scoreRanking(String year) {
        SafeProductionRequest request = new SafeProductionRequest();
        request.setYear(year);
        List<ProductionRunScore> list = findList(request);
        if (list == null || list.size() == 0) {
            return new ArrayList<>();
        }
        Map<String, List<ProductionRunScore>> typeDataMap = new HashMap<>();
        double totalValue = 0;
        for (ProductionRunScore productionRunScore : list) {
            if (typeDataMap.get(productionRunScore.getType()) == null) {
                typeDataMap.put(productionRunScore.getType(), new ArrayList<>());
            }
            typeDataMap.get(productionRunScore.getType()).add(productionRunScore);
            totalValue = totalValue + (productionRunScore.getValue() == null ? 0 : productionRunScore.getValue());
        }

        List<JSONObject> result = new ArrayList<>();
        NumberFormat format = new DecimalFormat("#.##");
        for (int i = 1; i <= 3; i++) {
            List<ProductionRunScore> productionRunScores = typeDataMap.get(i + "");
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("type", i);
            double valueSum = 0;
            double score = 0;
            for (ProductionRunScore productionRunScore : productionRunScores) {
                valueSum += productionRunScore.getValue() == null ? 0 : productionRunScore.getValue();
                score += productionRunScore.getScore() == null ? 0 : productionRunScore.getScore();
            }
            jsonObject.put("valueSum", valueSum);
            jsonObject.put("score", score);
            jsonObject.put("avgScore", 0);
            jsonObject.put("valueRate", 0);
            if (score != 0) {
                jsonObject.put("avgScore", format.format(score / productionRunScores.size()));
            }
            if (totalValue != 0) {
                jsonObject.put("valueRate", format.format(valueSum * 100 / totalValue));
            }
            result.add(jsonObject);
        }
        result.sort(Comparator.comparing(a -> a.getDouble("avgScore")));
        Collections.reverse(result);
        return result;
    }

    @Override
    public Map<String, Map<String, ProductionRunScore>> getMap(SafeProductionRequest request) {
        List<ProductionRunScore> productionRunScores = this.findList(request);
        String year = request.getYear();
        Map<String, Map<String, ProductionRunScore>> typeDataMap = new HashMap<>();

        productionRunScores.forEach(productionRunScore -> {
            String type = productionRunScore.getType();
            String month = productionRunScore.getMonth();
            Map<String, ProductionRunScore> dataMap = typeDataMap.get(month);
            if (dataMap == null) {
                dataMap = new HashMap<>();
                typeDataMap.put(month, dataMap);
            }
            dataMap.put(type, productionRunScore);
        });

        for (int j = 1; j <= 12; j++) {
            String month = j < 10 ? "0" + j : j + "";
            month = year + "-" + month;
            Map<String, ProductionRunScore> dataMap = typeDataMap.get(month);
            if (dataMap == null) {
                dataMap = new HashMap<>();
                typeDataMap.put(month, dataMap);
            }
                for (int i = 1; i <= 3; i++) {
                if (dataMap.get(i + "") == null) {
                    ProductionRunScore productionRunScore = new ProductionRunScore();
                    productionRunScore.setMonth(month);
                    productionRunScore.setScore(0d);
                    productionRunScore.setValue(0d);
                    productionRunScore.setType(i + "");
                    productionRunScore.setCreateTime(new Date());

                    dataMap.put(i + "", productionRunScore);
                }
            }
        }

        return typeDataMap;
    }
}

