<template>
  <div
    v-if="menu?.children?.length"
    class="horizontal-menu"
  >
    <div
      class="menu-wrapper"
      :class="[state.isCollapsed ? 'collapsed' : '']"
    >
      <div
        v-for="(item, i) in menu.children"
        :key="i"
        class="menu-tag"
        :class="[currentSelected.indexOf(item.path) !== -1 ? 'active' : '']"
        @click="() => handleClick(item)"
      >
        <span>{{ item.meta.title }}</span>
      </div>
    </div>
    <div
      class="right-bar"
      :class="[state.isCollapsed ? 'right-bar-collapsed' : '']"
      @click="state.isCollapsed = !state.isCollapsed"
    >
      <el-icon
        class="right-bar-icon"
        :class="[state.isCollapsed ? 'right' : 'left']"
      >
        <ArrowRight />
      </el-icon>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ArrowRight } from '@element-plus/icons-vue'

const emit = defineEmits<{(e: 'click', menu: IMenuItem, isChecked: boolean)
}>()
const props = defineProps<{
  menu?: IMenuItem
}>()
const state = reactive<{
  current: {
    pPath: string
    selectedSubmenus: IMenuItem[]
  }[]
  isCollapsed: boolean
}>({
  current: [],
  isCollapsed: false
})
const currentSelected = computed(() => {
  if (!props.menu?.path) return []
  return (
    state.current
      .find(item => item.pPath === props.menu?.path)
      ?.selectedSubmenus.map(item => item.path) || []
  )
})
const handleClick = (clickedMenu: IMenuItem) => {
  if (!props.menu) return
  let current = state.current.find(item => item.pPath === props.menu?.path)
  if (!current) {
    current = {
      pPath: props.menu.path,
      selectedSubmenus: []
    }
    state.current.push(current)
  }
  const index = current.selectedSubmenus.findIndex(
    item => item.path === clickedMenu.path
  )
  if (index !== -1) {
    current?.selectedSubmenus.splice(index, 1)
    emit('click', clickedMenu, false)
  } else {
    current?.selectedSubmenus.push(clickedMenu)
    emit('click', clickedMenu, true)
  }
}
</script>
<style lang="scss" scoped>
.dark,
.darkblue {
  .horizontal-menu {
    background-color: var(--el-bg-color);
  }
  .menu-tag {
    border-left: solid 1px rgba(173, 173, 173, 0.3);

    // &.active,
    // &:hover {
    //   background-color: var(--el-bg-color-page);
    // }
  }
  .right-bar {
    background-color: transparent;
  }
}
.horizontal-menu {
  display: flex;
  border-radius: 4px;
  height: 40px;
  background-color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
}

.menu-wrapper {
  height: 100%;
  border-radius: 4px 0 0 4px;
  border-right: solid 1px var(--el-border-color);
  overflow: hidden;
  max-width: 800px;
  transition: all 0.5s;

  &.collapsed {
    max-width: 0;
    border: none;
  }

}

.right-bar {
  display: flex;
  align-items: center;
  cursor: pointer;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 0 4px 4px 0;

  // border-left: solid 1px rgba(173,173,173,.3);
  &.right-bar-collapsed {
    border-radius: 4px;
  }

  .right-bar-icon {
    transition: all 0.5s;

    &.left {
      transform: rotate(180deg);
    }

    &.right {
      transform: rotate(0);
    }
  }
}

.menu-tag {
  user-select: none;
  display: inline-flex;
  padding: 0 16px;
  line-height: 40px;
  border-left: solid 1px var(--el-border-color);
  cursor: pointer;
  &:hover {
    background-color: var(--el-color-primary-light-3);
    color: #fdfdfd;
  }
  &.active {
    background-color: var(--el-color-primary);
    color: #fff;
  }
  &:last-child {
    border-radius: 0;
  }

  &:first-child {
    border-radius: 4px 0 0 4px;
    border-left: none;
  }
}
</style>
