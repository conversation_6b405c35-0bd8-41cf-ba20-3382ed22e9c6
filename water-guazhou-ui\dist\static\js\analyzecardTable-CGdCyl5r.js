import{_ as m}from"./index-C9hz-UZb.js";import{d as b,c as g,r as h,a8 as x,g as v,h as C,F as e,p as l,bh as y,q as a,i as s,d_ as k,aw as n,cE as B,J as E,aq as T,C as V}from"./index-r0dFAfgr.js";const w={class:"card_title"},I=b({__name:"analyzecardTable",props:{title:{},value:{},total:{}},setup(c,{expose:r}){const o=g(),i=c,_=h({defaultExpandAll:!0,indexVisible:!0,rowKey:"id",columns:[{label:"规则名称",prop:"title"},{label:"报警信息",prop:"alarmInfo"}],operations:[],dataList:x(()=>i.value),pagination:{hide:!0}});return r({reftable:o}),(t,q)=>{const d=B,p=E,u=T,f=m;return v(),C(f,{class:n({card:!0,maxheight:t.total<=3}),title:" "},{title:e(()=>[l("div",w,[l("div",null,y(t.title),1),a(p,{key:"",text:""},{default:e(()=>[a(d,null,{default:e(()=>[a(s(k))]),_:1})]),_:1})])]),default:e(()=>[a(u,{ref_key:"reftable",ref:o,class:n({"card-table":!0,"max-table-height":t.total<=3}),title:"asdas",config:s(_)},null,8,["class","config"])]),_:1},8,["class"])}}}),L=V(I,[["__scopeId","data-v-eaad70a9"]]);export{L as default};
