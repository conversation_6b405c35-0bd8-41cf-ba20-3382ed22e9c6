import{_ as S}from"./TreeBox-DDD2iwoR.js";import{_ as E}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as P}from"./CardTable-rdWOL4_6.js";import{_ as j}from"./CardSearch-CB_HNR-Q.js";import{_ as I}from"./index-BJ-QPYom.js";import{d as M,M as w,c as m,s as F,r as f,x as l,a0 as N,S as L,o as R,g as V,h as Y,F as _,q as c,i as p,a9 as q,b7 as B}from"./index-r0dFAfgr.js";import{I as d}from"./common-CvK_P_ao.js";import{f as H}from"./DateFormatter-Bm9a68Ax.js";import{c as O}from"./equipmentManage-DuoY00aj.js";import{p as W,d as $,g as A}from"./equipmentService-DanrK8F-.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";const ne=M({__name:"index",setup(U){const{$btnPerms:g}=w(),s=m(),u=m(),h=m({filters:[{label:"关键字",field:"keywords",type:"input"},{type:"btn-group",btns:[{perm:!0,text:"查询",icon:d.QUERY,click:()=>i()},{type:"default",perm:!0,text:"重置",svgIcon:F(B),click:()=>{var e;(e=u.value)==null||e.resetForm(),n.currentProject={},i()}},{perm:!0,text:"新建",icon:d.ADD,type:"success",click:()=>b("新建")}]}]}),o=f({defaultExpandAll:!0,indexVisible:!0,rowKey:"id",columns:[{label:"类别名称",prop:"deviceTypeName"},{label:"保养方法说明",prop:"method"},{label:"注意事项",prop:"remark"},{label:"添加人",prop:"creatorName"},{label:"添加时间",prop:"createTime",formatter:e=>H(e.createTime,"YYYY-MM-DD HH:mm")}],operationWidth:"200px",operations:[{type:"primary",text:"编辑",icon:d.EDIT,perm:g("RoleManageEdit"),click:e=>x(e)},{type:"danger",text:"删除",perm:g("RoleManageDelete"),icon:d.DELETE,click:e=>y(e)}],dataList:[],pagination:{page:1,limit:20,total:0,refreshData:({page:e,size:t})=>{o.pagination.page=e,o.pagination.limit=t,i()}}}),r=f({title:"新增",labelWidth:"120px",dialogWidth:"500px",submitting:!1,submit:e=>{r.submitting=!0,e.serialId=n.currentProject.serialId;let t="新增成功";e.id&&(t="修改成功"),W(e).then(()=>{var a;l.success(t),i(),(a=s.value)==null||a.closeDialog(),r.submitting=!1}).catch(a=>{r.submitting=!1,l.warning(a)})},defaultValue:{},group:[{fields:[{type:"textarea",label:"保养方法说明",field:"method",rules:[{required:!0,message:"请输入保养方法说明"}]},{type:"textarea",label:"注意事项",field:"remark",rules:[{required:!0,message:"请输入注意事项"}]}]}]}),n=f({title:" ",data:[],currentProject:{},expandOnClickNode:!1,isFilterTree:!0,treeNodeHandleClick:e=>{n.currentProject=e,N().SET_selectedProject(e),i()}}),b=e=>{var t;if(!n.currentProject.id){l.warning("请选中类别");return}r.title=e,r.defaultValue={},(t=s.value)==null||t.openDialog()},x=e=>{var t;r.title="编辑",r.defaultValue={...e||{}},(t=s.value)==null||t.openDialog()},y=e=>{L("确定删除指定保养标准?","删除提示").then(()=>{$([e.id]).then(()=>{l.success("删除成功"),i()})})};function D(){O().then(e=>{n.data=q(e.data.data||[]),i()})}function i(){var t;const e={size:o.pagination.limit,page:o.pagination.page,deviceTypeId:n.currentProject.id||"",...((t=u.value)==null?void 0:t.queryParams)||{}};A(e).then(a=>{o.dataList=a.data.data.data||[],o.pagination.total=a.data.data.total||0})}return R(async()=>{D()}),(e,t)=>{const a=I,k=j,T=P,v=E,C=S;return V(),Y(C,null,{tree:_(()=>[c(a,{"tree-data":p(n)},null,8,["tree-data"])]),default:_(()=>[c(k,{ref_key:"refSearch",ref:u,config:p(h)},null,8,["config"]),c(T,{config:p(o),class:"card-table"},null,8,["config"]),c(v,{ref_key:"refForm",ref:s,config:p(r)},null,8,["config"])]),_:1})}}});export{ne as default};
