import{C as f,M as m,g,h,F as o,p as C,q as l,G as n,bb as k,J as p,L as _}from"./index-r0dFAfgr.js";import{e as b,f as T,h as y}from"./index-CaaU9niG.js";const{$message:x}=m(),B={name:"Cataloge",props:["tableConfig"],data(){return{data:[],selectNodes:[]}},computed:{visible(){return this.tableConfig.visible}},created(){b().then(e=>{this.data=e.data})},methods:{change(){console.log(this.$refs.menuTree.getCheckedKeys())},_getTreeByRoleId(){console.log(this.tableConfig),T(this.tableConfig.roleId).then(e=>{this.selectNodes=e.data,this.$refs.menuTree.setCheckedKeys(this.selectNodes)})},clickSaveMenuToRole(){this.tableConfig.close();const e={};e.roleId=this.tableConfig.roleId,e.menuIds=this.$refs.menuTree.getCheckedKeys(),y(e).then(()=>{x({type:"success",message:"操作成功"})})},clearMenuTree(){this.$refs.menuTree.setCheckedKeys([])}}},I={class:"dialog-footer"};function v(e,t,r,M,d,s){const i=k,a=p,c=_;return g(),h(c,{modelValue:s.visible,"onUpdate:modelValue":t[0]||(t[0]=u=>s.visible=u),title:"授权角色的菜单",width:"30%","close-on-click-modal":!1,onOpen:s._getTreeByRoleId},{footer:o(()=>[C("span",I,[l(a,{size:"small",type:"primary",onClick:s.clickSaveMenuToRole},{default:o(()=>t[1]||(t[1]=[n("保存")])),_:1},8,["onClick"]),l(a,{size:"small",onClick:s.clearMenuTree},{default:o(()=>t[2]||(t[2]=[n("清空")])),_:1},8,["onClick"]),l(a,{size:"small",onClick:r.tableConfig.close},{default:o(()=>t[3]||(t[3]=[n("取 消")])),_:1},8,["onClick"])])]),default:o(()=>[l(i,{ref:"menuTree",data:d.data,"show-checkbox":"","node-key":"id","default-expand-all":!0,onCheck:s.change},null,8,["data","onCheck"])]),_:1},8,["modelValue","onOpen"])}const V=f(B,[["render",v]]);export{V as default};
