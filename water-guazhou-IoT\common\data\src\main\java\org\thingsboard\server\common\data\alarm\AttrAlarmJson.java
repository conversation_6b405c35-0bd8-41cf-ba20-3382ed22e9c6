/**
 * Copyright © 2016-2019 The Thingsboard Authors
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.common.data.alarm;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;
import org.thingsboard.server.common.data.HasName;
import org.thingsboard.server.common.data.HasTenantId;
import org.thingsboard.server.common.data.SearchTextBasedWithAdditionalInfo;
import org.thingsboard.server.common.data.id.DeviceId;
import org.thingsboard.server.common.data.id.TenantId;

/**
 * 由于新的报警规则为分离模式，所以同一个attr的同一种类型的报警可以拥有多个报警，在设置报警时即为其设定唯一的id
 */
@Data
public class AttrAlarmJson extends SearchTextBasedWithAdditionalInfo<AlarmJsonId> implements HasName, HasTenantId {

    private String attribute;

    public JsonNode details;

    private String params;
    //检测报警js
    private String alarmScript;
    //检测恢复js
    private String restoreScript;
    //报警类型
    private String alarmType;
    //报警等级
    private String severity;
    //是否发出报警消息
    private boolean propagate;

    private String period;

    private String alarmName;

    private TenantId tenantId;

    private DeviceId deviceId;

    private Long createTime;

    private String restoreType;

    private String cycle;

    private Boolean isCycle = false;

    public AttrAlarmJson() {
        super();
    }

    public AttrAlarmJson(AlarmJsonId alarmJsonId) {
        super(alarmJsonId);
    }

    @Override
    public String getSearchText() {
        return alarmName;
    }

    public AttrAlarmJson(AttrAlarmJson attrAlarmJson) {
        super(attrAlarmJson.getId());
        this.params = attrAlarmJson.getParams();
        this.alarmScript = attrAlarmJson.getAlarmScript();
        this.restoreScript = attrAlarmJson.getRestoreScript();
        this.alarmType = attrAlarmJson.getAlarmType();
        this.severity = attrAlarmJson.getSeverity();
        this.propagate = attrAlarmJson.isPropagate();
        this.alarmName = attrAlarmJson.getAlarmName();
        this.tenantId = attrAlarmJson.getTenantId();
        this.deviceId = attrAlarmJson.getDeviceId();
        this.attribute = attrAlarmJson.getAttribute();
        this.createTime = attrAlarmJson.getCreateTime();
        this.details = attrAlarmJson.getDetails();
        this.period = attrAlarmJson.getPeriod();
        this.restoreType = attrAlarmJson.getRestoreType();
        this.cycle =attrAlarmJson.getCycle();
        this.isCycle=attrAlarmJson.getIsCycle();
    }


    @Override
    public String getName() {
        return alarmName;
    }
}
