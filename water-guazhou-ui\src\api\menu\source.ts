import { request } from '@/plugins/axios'

/**
 * 查询资源列表
 * @param params
 * @returns
 */
export const GetMenuSources = (params?: any) => request({
  url: '/api/menuResource/list',
  method: 'get',
  params
})
/**
 * 查询全部菜单资源列表
 * @param params
 * @returns
 */
export const GetMenuSourceAll = () => request({
  url: '/api/menuResource/findAll',
  method: 'get'
})
/**
 * 删除资源
 * @param ids
 * @returns
 */
export const DeleteMenuSource = (ids: string[]) => request({
  url: '/api/menuResource',
  method: 'delete',
  data: ids
})
/**
 * 新增或编辑菜单
 * @param params
 * @returns
 */
export const PostMenuSource = (params: any) => request({
  url: '/api/menuResource',
  method: 'post',
  data: params
})
/**
 * 获取当前企业的菜单树
 * @param params
 * @returns
 */
export const GetMenuTree = () => request({
  url: '/api/tenantMenus/getSelectableTree',
  method: 'get'
})
/**
 * 查询根菜单的parentId
 * @param params
 * @returns
 */
export const GetMenuRootId = () => request({
  url: '/api/tenantMenus/getRootId',
  method: 'get'
})
/**
 * 查询菜单详情
 * @param id
 * @returns
 */
export const GetMenu = (id: string) => request({
  url: `/api/tenantMenus/${id}`,
  method: 'get'
})
/**
 * 新增可编辑菜单
 * @param params
 * @returns
 */
export const PostMenu = (params: any) => request({
  url: '/api/tenantMenus',
  method: 'post',
  data: params
})
/**
 * 删除菜单
 * @param id
 * @returns
 */
export const DeleteMenu = (ids: string[]) => request({
  url: `/api/tenantMenus`,
  method: 'delete',
  data: ids
})
/**
 * 导出当前企业下的菜单
 * @returns
 */
export const ExportMenu = () => request({
  url: '/api/tenantMenus/export',
  method: 'get',
  responseType: 'blob'
})
export const ImportMenu = (file: any) => request({
  url: '/api/tenantMenus/import',
  method: 'post',
  data: file
})
/** 获取指定应用下的菜单路由 */
export const GetApplicationAsyncRoutes = (id: string) => request({
  url: `/api/tenantMenus/findMenuByTenantApplication?tenantApplicationId=${id}`,
  method: 'get'
})
