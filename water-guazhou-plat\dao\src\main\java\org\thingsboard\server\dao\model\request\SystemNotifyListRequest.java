package org.thingsboard.server.dao.model.request;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
public class SystemNotifyListRequest extends IstarPageRequest {

    private String to;

    private String type;

    private String topic;

    private String fromName;

    private String status;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date beginTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date endTime;

    private String tenantId;

}
