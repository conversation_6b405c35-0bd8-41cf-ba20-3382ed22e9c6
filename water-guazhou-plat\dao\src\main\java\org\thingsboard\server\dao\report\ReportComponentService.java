package org.thingsboard.server.dao.report;

import com.alibaba.fastjson.JSONObject;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.request.ReportRequest;

import java.util.List;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2023-06-07
 */
public interface ReportComponentService {

    JSONObject save(JSONObject reportComponent);

    PageData<JSONObject> getList(ReportRequest request);

    void delete(List<String> ids);

}
