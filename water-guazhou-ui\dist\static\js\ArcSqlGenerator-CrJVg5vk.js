import{_ as y}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{d as L,c as h,r as _,am as T,g as N,h as S,F as W,p,q as g,n as O,aJ as F,i as u,an as K,aB as x,a1 as H,aH as X,by as R,C as q}from"./index-r0dFAfgr.js";import{GetFieldConfig as w}from"./fieldconfig-Bk3o1wi7.js";import{GetFieldConfig as J}from"./wfsUtils-DXofo3da.js";var l=(s=>(s.DENGYU="1",s.BUDENGYU="2",s.DAYU="3",s.DAYUDENGYU="4",s.XIAOYUDENGYU="5",s.XIAOYU="6",s.WEIKONG="7",s.BUWEIKONG="8",s.BAOHAN="9",s.<PERSON>UBAOHAN="10",s))(l||{});const ce={1:"等于",2:"不等于",3:"大于",4:"大于等于",5:"小于等于",6:"小于",7:"为空",8:"不为空",9:"包含",10:"不包含"},A={1:"=",2:"<>",3:">",4:">=",5:"<=",6:"<",7:"is null",8:"is not null",9:"like",10:"not like"},de={OBJECTID:"OBJECTID",ELEVATION:"地面高程",DEPTH:"埋深",DIAMETER:"口径",MATERIAL:"材质",LANEWAY:"所在道路",X:"横坐标",Y:"纵坐标",SID:"设备编号"};var I=(s=>(s.AND="1",s.OR="2",s))(I||{});const re={1:"且",2:"或"},B={1:"and",2:"or"},M={class:"add-more"},P={class:"form-list"},j={key:0,class:"logic"},z={class:"form-items"},Q=["onClick"],Z=L({__name:"ArcSqlGenerator",props:{layerName:{}},emits:["submit"],setup(s,{expose:$,emit:C}){const m=window.GIS_SERVER_SWITCH===!0,b=s,G=C,f=h(),t=_({logicOption:[{label:"或",value:I.OR},{label:"且",value:I.AND}],fieldConfig:[],calcOptions:[{label:"等于",value:l.DENGYU},{label:"不等于",value:l.BUDENGYU},{label:"大于",value:l.DAYU},{label:"大于等于",value:l.DAYUDENGYU},{label:"小于等于",value:l.XIAOYUDENGYU},{label:"小于",value:l.XIAOYU},{label:"为空",value:l.WEIKONG},{label:"不为空",value:l.BUWEIKONG},{label:"包含",value:l.BAOHAN},{label:"不包含",value:l.BUBAOHAN}]}),c=_({dataList:[],height:"none",columns:[{label:"字段",prop:"field",formItemConfig:{type:"select",options:t.fieldConfig}},{label:"运行符",prop:"calc",formItemConfig:{type:"select",options:t.calcOptions}},{label:"值",prop:"value",formItemConfig:{type:"input"}}],pagination:{hide:!0},operations:[{perm:!0,click:(e,a)=>Y(e,a),text:"x"}]}),v=async e=>{var a,d;if(e)try{if(console.log("加载图层字段:",e),m){const o=await J(e);if(console.log("GeoServer字段响应:",o),o&&o.data&&o.data.featureTypes&&o.data.featureTypes[0]){const n=(o.data.featureTypes[0].properties||[]).filter(i=>{var E;const r=((E=i.type.split(":")[1])==null?void 0:E.toLowerCase())||"";return i.name!=="the_geom"&&i.name!=="geom"&&["int","long","double","float","string","date"].indexOf(r)!==-1});t.fieldConfig=n.map(i=>({label:i.name,value:i.name}))}else console.warn("GeoServer字段响应格式无效"),t.fieldConfig=[]}else{const U=((d=(a=(await w(e)).data)==null?void 0:a.result)==null?void 0:d.rows)||[];t.fieldConfig=H(U,{id:"name",label:"alias",value:"name"})}console.log("字段配置已加载:",t.fieldConfig)}catch(o){console.error("获取字段配置失败:",o),t.fieldConfig=[{label:"OBJECTID",value:"OBJECTID"}]}};T(()=>b.layerName,async e=>{e?await v(e):t.fieldConfig=[]},{immediate:!0});const k=()=>{var d;const e=c.dataList.length>0?"1":"",a=((d=t.fieldConfig[0])==null?void 0:d.value)||"OBJECTID";c.dataList.push({logic:e,field:a,calc:l.DENGYU,value:""})},Y=(e,a)=>{c.dataList.splice(a,1)},D=()=>{let e="";return c.dataList.map((a,d)=>{if(a.logic&&d>0){const o=m?B[a.logic].toUpperCase():B[a.logic];e+=` ${o} `}if(m){const o=m?`"${a.field}"`:a.field;switch(a.calc){case l.DENGYU:e+=`${o} = '${a.value??""}'`;break;case l.BUDENGYU:e+=`${o} <> '${a.value??""}'`;break;case l.DAYU:e+=`${o} > '${a.value??""}'`;break;case l.DAYUDENGYU:e+=`${o} >= '${a.value??""}'`;break;case l.XIAOYUDENGYU:e+=`${o} <= '${a.value??""}'`;break;case l.XIAOYU:e+=`${o} < '${a.value??""}'`;break;case l.WEIKONG:e+=`${o} IS NULL`;break;case l.BUWEIKONG:e+=`${o} IS NOT NULL`;break;case l.BAOHAN:e+=`${o} LIKE '%${a.value??""}%'`;break;case l.BUBAOHAN:e+=`${o} NOT LIKE '%${a.value??""}%'`;break}}else switch(a.calc){case l.DENGYU:case l.BUDENGYU:case l.DAYU:case l.DAYUDENGYU:case l.XIAOYUDENGYU:case l.XIAOYU:e+=`${a.field} ${A[a.calc]} '${a.value??""}'`;break;case l.WEIKONG:case l.BUWEIKONG:e+=`${a.field} ${A[a.calc]}`;break;case l.BAOHAN:case l.BUBAOHAN:e+=`${a.field} ${A[a.calc]} '%${a.value??""}%'`;break}}),e},V=_({dialogWidth:800,title:"属性过滤",group:[],submit:()=>{var e;G("submit",{list:c.dataList,sql:D()}),(e=f.value)==null||e.closeDialog()}});return $({openDialog:()=>{var e;b.layerName&&v(b.layerName),(e=f.value)==null||e.openDialog()},closeDialog:()=>{var e;(e=f.value)==null||e.closeDialog()},genSql:D,refDialog:f,TableConfig:c,clear:()=>{c.dataList=[]},Submit:()=>{G("submit",{list:c.dataList,sql:D()})},loadFieldConfig:v}),(e,a)=>{const d=X,o=R,U=y;return N(),S(U,{ref_key:"refDialog",ref:f,config:u(V)},{default:W(()=>[p("div",M,[g(d,{class:"add-more-btn",config:{perm:!0,text:"添加过滤条件",iconifyIcon:"ep:plus",isTextBtn:!0,click:()=>k()}},null,8,["config"])]),p("div",P,[(N(!0),O(x,null,F(u(c).dataList,(n,i)=>(N(),O("div",{key:i,class:"form-row"},[i>0?(N(),O("div",j,[a[0]||(a[0]=p("label",{class:"label"}," 逻辑 ",-1)),g(o,{modelValue:n.logic,"onUpdate:modelValue":r=>n.logic=r,config:{type:"radio",options:u(t).logicOption}},null,8,["modelValue","onUpdate:modelValue","config"])])):K("",!0),p("div",z,[g(o,{modelValue:n.field,"onUpdate:modelValue":r=>n.field=r,config:{type:"select",placeholder:"请选择字段",options:u(t).fieldConfig}},null,8,["modelValue","onUpdate:modelValue","config"]),g(o,{modelValue:n.calc,"onUpdate:modelValue":r=>n.calc=r,config:{type:"select",placeholder:"请选择计算方式",options:u(t).calcOptions}},null,8,["modelValue","onUpdate:modelValue","config"]),g(o,{modelValue:n.value,"onUpdate:modelValue":r=>n.value=r,config:{type:"input",placeholder:"请输入内容",disabled:()=>[u(l).BUWEIKONG,u(l).WEIKONG].includes(n.calc)}},null,8,["modelValue","onUpdate:modelValue","config"]),p("div",{class:"button",onClick:()=>Y(n,i)},"x",8,Q)])]))),128))])]),_:1},8,["config"])}}}),ue=q(Z,[["__scopeId","data-v-13391093"]]);export{re as O,ue as _,de as a,ce as b};
