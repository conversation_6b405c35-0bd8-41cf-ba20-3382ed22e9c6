package org.thingsboard.server.dao.util;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 用户批量导入
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2023-06-09
 */
public class CustBatchImportUtil {
    private static ConcurrentHashMap<String, Map<String, Object>> custBatchImportMap;

    public static ConcurrentHashMap<String, Map<String, Object>> getCustBatchImportMap() {
        if (custBatchImportMap == null) {
            custBatchImportMap = new ConcurrentHashMap<>();
        }
        return custBatchImportMap;
    }
}
