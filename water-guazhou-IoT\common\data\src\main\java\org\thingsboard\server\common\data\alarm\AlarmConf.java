/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.common.data.alarm;

import lombok.Data;

/**
 * 2019 - 01- 28
 * 报警状态触发变更的记录详情
 */
@Data
public class AlarmConf {

    private Long ts;
    private String info;
    private String status;

    public AlarmConf() {
    }

    public AlarmConf(Long ts, String info, String status) {
        this.ts = ts;
        this.info = info;
        this.status = status;
    }
}
