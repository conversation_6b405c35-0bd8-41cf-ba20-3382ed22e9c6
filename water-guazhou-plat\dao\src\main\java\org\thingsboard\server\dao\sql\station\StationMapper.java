package org.thingsboard.server.dao.sql.station;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.thingsboard.server.dao.model.sql.StationEntity;

@Mapper
public interface StationMapper extends BaseMapper<StationEntity> {

    default String getNameById(String ids) {
        if (ids == null || ids.length() == 0) {
            return null;
        }
        String[] idArr = ids.split(",");
        StringBuilder builder = new StringBuilder();
        for (String id : idArr) {
            builder.append(getName(id)).append(",");
        }

        return builder.substring(0, builder.length() - 1);
    }

    @Select("select name from tb_station where id = #{id}")
    String getName(String id);
}
