import{e as t,y as e,a as r}from"./Point-WxyopZva.js";import{t as p,j as a}from"./automaticLengthMeasurementUtils-DljoUgEz.js";let l=class extends p{constructor(o){super(o),this.type="translate-graphic",this.distance=a}};t([e()],l.prototype,"type",void 0),t([e()],l.prototype,"distance",void 0),l=t([r("esri.views.interactive.tooltip.TranslateGraphicTooltipInfo")],l);let c=class extends p{constructor(o){super(o),this.type="translate-graphic-z",this.distance=a}};t([e()],c.prototype,"type",void 0),t([e()],c.prototype,"distance",void 0),c=t([r("esri.views.interactive.tooltip.TranslateGraphicZTooltipInfo")],c);let v=class extends p{constructor(o){super(o),this.type="translate-graphic-xy",this.distance=a}};t([e()],v.prototype,"type",void 0),t([e()],v.prototype,"distance",void 0),v=t([r("esri.views.interactive.tooltip.TranslateGraphicXYTooltipInfo")],v);let i=class extends p{constructor(o){super(o),this.type="translate-vertex",this.distance=a,this.elevation=null,this.area=null,this.totalLength=null}};t([e()],i.prototype,"type",void 0),t([e()],i.prototype,"distance",void 0),t([e()],i.prototype,"elevation",void 0),t([e()],i.prototype,"area",void 0),t([e()],i.prototype,"totalLength",void 0),i=t([r("esri.views.interactive.tooltip.TranslateVertexTooltipInfo")],i);let n=class extends p{constructor(o){super(o),this.type="translate-vertex-z",this.distance=a,this.elevation=null}};t([e()],n.prototype,"type",void 0),t([e()],n.prototype,"distance",void 0),t([e()],n.prototype,"elevation",void 0),n=t([r("esri.views.interactive.tooltip.TranslateVertexZTooltipInfo")],n);let s=class extends p{constructor(o){super(o),this.type="translate-vertex-xy",this.distance=a,this.elevation=null,this.area=null,this.totalLength=null}};t([e()],s.prototype,"type",void 0),t([e()],s.prototype,"distance",void 0),t([e()],s.prototype,"elevation",void 0),t([e()],s.prototype,"area",void 0),t([e()],s.prototype,"totalLength",void 0),s=t([r("esri.views.interactive.tooltip.TranslateVertexXYTooltipInfo")],s);export{v as a,s as c,n as l,i as n,c as p,l as r};
