<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartService.wechat.WxNewsMapper">
    <sql id="Base_Column_List">
        <!--@sql select-->
        id,
        category_id,
        (select name from wx_news_category where id = category_id) category_name,
        content,
        cover,
        create_time,
        order_num,
        serial_no,
        summary,
        tenant_id,
        title
        <!--@sql from wx_news -->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.smartService.wechat.WxNews">
        <id column="id" property="id"/>
        <result column="title" property="title"/>
        <result column="cover" property="cover"/>
        <result column="summary" property="summary"/>
        <result column="content" property="content"/>
        <result column="serial_no" property="serialNo"/>
        <result column="order_num" property="orderNum"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="category_id" property="categoryId"/>
        <result column="category_name" property="categoryName"/>
        <result column="create_time" property="createTime"/>
    </resultMap>
    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wx_news
        <where>
            <if test="title != null and title != ''">
                and title like '%'|| #{title} ||'%'
            </if>
            <if test="categoryId != null and categoryId != ''">
                and category_id = #{categoryId}
            </if>
            <if test="fromTime != null">
                and create_time >= #{fromTime}
            </if>
            <if test="toTime != null">
                and create_time &lt;= #{toTime}
            </if>
            and tenant_id = #{tenantId}
        </where>
        order by order_num ,create_time desc
    </select>

    <select id="getDetailById" resultType="org.thingsboard.server.dao.model.sql.smartService.wechat.WxNews">
        select a.*, b.name as categoryName
        from wx_news a
        left join wx_news_category b on a.category_id = b.id
        where a.id = #{id}
    </select>

    <update id="updateFully">
        update wx_news
        set title       = #{title},
            cover       = #{cover},
            summary     = #{summary},
            content     = #{content},
            order_num   = #{orderNum},
            serial_no   = #{serialNo},
            category_id = #{categoryId}
        where id = #{id}
    </update>

    <insert id="save">
        INSERT INTO wx_news(
            id,
            title,
            summary,
            content,
            category_id,
            serial_no,
            cover,
            create_time,
            order_num,
            tenant_id
        ) VALUES
         (
                #{id},
                #{title},
                #{summary},
                #{content},
                #{categoryId},
                #{serialNo},
                #{cover},
                #{createTime},
                #{orderNum},
                #{tenantId}
        )
    </insert>

    <insert id="insertList">
        INSERT INTO wx_news(
        id,
        title,
        summary,
        content,
        category_id,
        serial_no,
        cover,
        create_time,
        order_num,
        tenant_id
        )VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (
            #{element.id},
            #{element.title},
            #{element.summary},
            #{element.content},
            #{element.categoryId},
            #{element.serialNo},
            #{element.cover},
            #{element.createTime},
            #{element.orderNum},
            #{element.tenantId}
            )
        </foreach>
    </insert>
</mapper>