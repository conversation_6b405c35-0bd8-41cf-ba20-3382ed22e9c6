function jn(e,g){for(var m=0;m<g.length;m++){const p=g[m];if(typeof p!="string"&&!Array.isArray(p)){for(const f in p)if(f!=="default"&&!(f in e)){const h=Object.getOwnPropertyDescriptor(p,f);h&&Object.defineProperty(e,f,h.get?h:{enumerable:!0,get:()=>p[f]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}var tn,S,rn,T={};tn={get exports(){return T},set exports(e){T=e}},S=typeof document<"u"&&document.currentScript?document.currentScript.src:void 0,typeof __filename<"u"&&(S=S||__filename),rn=function(e){var g,m;(e=(e=e||{})!==void 0?e:{}).ready=new Promise(function(n,t){g=n,m=t});var p,f,h,D,P,H,q=Object.assign({},e),C=typeof window=="object",v=typeof importScripts=="function",F=typeof process=="object"&&typeof process.versions=="object"&&typeof process.versions.node=="string",c="";function on(n){return e.locateFile?e.locateFile(n,c):c+n}F?(c=v?require("path").dirname(c)+"/":__dirname+"/",H=()=>{P||(D=require("fs"),P=require("path"))},p=function(n,t){return H(),n=P.normalize(n),D.readFileSync(n,t?void 0:"utf8")},h=n=>{var t=p(n,!0);return t.buffer||(t=new Uint8Array(t)),t},f=(n,t,r)=>{H(),n=P.normalize(n),D.readFile(n,function(o,s){o?r(o):t(s.buffer)})},process.argv.length>1&&process.argv[1].replace(/\\/g,"/"),process.argv.slice(2),process.on("uncaughtException",function(n){if(!(n instanceof In))throw n}),process.on("unhandledRejection",function(n){throw n}),e.inspect=function(){return"[Emscripten Module object]"}):(C||v)&&(v?c=self.location.href:typeof document<"u"&&document.currentScript&&(c=document.currentScript.src),S&&(c=S),c=c.indexOf("blob:")!==0?c.substr(0,c.replace(/[?#].*/,"").lastIndexOf("/")+1):"",p=n=>{var t=new XMLHttpRequest;return t.open("GET",n,!1),t.send(null),t.responseText},v&&(h=n=>{var t=new XMLHttpRequest;return t.open("GET",n,!1),t.responseType="arraybuffer",t.send(null),new Uint8Array(t.response)}),f=(n,t,r)=>{var o=new XMLHttpRequest;o.open("GET",n,!0),o.responseType="arraybuffer",o.onload=()=>{o.status==200||o.status==0&&o.response?t(o.response):r()},o.onerror=r,o.send(null)}),e.print||console.log.bind(console);var w,M,b=e.printErr||console.warn.bind(console);Object.assign(e,q),q=null,e.arguments&&e.arguments,e.thisProgram&&e.thisProgram,e.quit&&e.quit,e.wasmBinary&&(w=e.wasmBinary),e.noExitRuntime,typeof WebAssembly!="object"&&x("no native wasm support detected");var B,A,E,y,d,k,z=!1,L=typeof TextDecoder<"u"?new TextDecoder("utf8"):void 0;function un(n,t,r){for(var o=t+r,s=t;n[s]&&!(s>=o);)++s;if(s-t>16&&n.buffer&&L)return L.decode(n.subarray(t,s));for(var u="";t<s;){var i=n[t++];if(128&i){var l=63&n[t++];if((224&i)!=192){var nn=63&n[t++];if((i=(240&i)==224?(15&i)<<12|l<<6|nn:(7&i)<<18|l<<12|nn<<6|63&n[t++])<65536)u+=String.fromCharCode(i);else{var en=i-65536;u+=String.fromCharCode(55296|en>>10,56320|1023&en)}}else u+=String.fromCharCode((31&i)<<6|l)}else u+=String.fromCharCode(i)}return u}function O(n,t){return n?un(E,n,t):""}function G(n){B=n,e.HEAP8=A=new Int8Array(n),e.HEAP16=new Int16Array(n),e.HEAP32=y=new Int32Array(n),e.HEAPU8=E=new Uint8Array(n),e.HEAPU16=new Uint16Array(n),e.HEAPU32=d=new Uint32Array(n),e.HEAPF32=new Float32Array(n),e.HEAPF64=new Float64Array(n)}e.INITIAL_MEMORY;var X=[],N=[],Y=[];function sn(){if(e.preRun)for(typeof e.preRun=="function"&&(e.preRun=[e.preRun]);e.preRun.length;)fn(e.preRun.shift());W(X)}function an(){W(N)}function cn(){if(e.postRun)for(typeof e.postRun=="function"&&(e.postRun=[e.postRun]);e.postRun.length;)ln(e.postRun.shift());W(Y)}function fn(n){X.unshift(n)}function pn(n){N.unshift(n)}function ln(n){Y.unshift(n)}var _=0,R=null;function hn(n){_++,e.monitorRunDependencies&&e.monitorRunDependencies(_)}function dn(n){if(_--,e.monitorRunDependencies&&e.monitorRunDependencies(_),_==0&&R){var t=R;R=null,t()}}function x(n){e.onAbort&&e.onAbort(n),b(n="Aborted("+n+")"),z=!0,n+=". Build with -sASSERTIONS for more info.";var t=new WebAssembly.RuntimeError(n);throw m(t),t}var a,mn="data:application/octet-stream;base64,";function J(n){return n.startsWith(mn)}function K(n){return n.startsWith("file://")}function Q(n){try{if(n==a&&w)return new Uint8Array(w);if(h)return h(n);throw"both async and sync fetching of the wasm failed"}catch(t){x(t)}}function _n(){if(!w&&(C||v)){if(typeof fetch=="function"&&!K(a))return fetch(a,{credentials:"same-origin"}).then(function(n){if(!n.ok)throw"failed to load wasm binary file at '"+a+"'";return n.arrayBuffer()}).catch(function(){return Q(a)});if(f)return new Promise(function(n,t){f(a,function(r){n(new Uint8Array(r))},t)})}return Promise.resolve().then(function(){return Q(a)})}function yn(){var n={a:En};function t(u,i){var l=u.exports;e.asm=l,G((M=e.asm.g).buffer),k=e.asm.m,pn(e.asm.h),dn()}function r(u){t(u.instance)}function o(u){return _n().then(function(i){return WebAssembly.instantiate(i,n)}).then(function(i){return i}).then(u,function(i){b("failed to asynchronously prepare wasm: "+i),x(i)})}function s(){return w||typeof WebAssembly.instantiateStreaming!="function"||J(a)||K(a)||F||typeof fetch!="function"?o(r):fetch(a,{credentials:"same-origin"}).then(function(u){return WebAssembly.instantiateStreaming(u,n).then(r,function(i){return b("wasm streaming compile failed: "+i),b("falling back to ArrayBuffer instantiation"),o(r)})})}if(hn(),e.instantiateWasm)try{return e.instantiateWasm(n,t)}catch(u){return b("Module.instantiateWasm callback failed with error: "+u),!1}return s().catch(m),{}}function W(n){for(;n.length>0;){var t=n.shift();if(typeof t!="function"){var r=t.func;typeof r=="number"?t.arg===void 0?V(r)():V(r)(t.arg):r(t.arg===void 0?null:t.arg)}else t(e)}}J(a="lerc-wasm.wasm")||(a=on(a));var I=[];function V(n){var t=I[n];return t||(n>=I.length&&(I.length=n+1),I[n]=t=k.get(n)),t}function gn(n,t,r,o){x("Assertion failed: "+O(n)+", at: "+[t?O(t):"unknown filename",r,o?O(o):"unknown function"])}function vn(n){return Z(n+24)+24}function wn(n){this.excPtr=n,this.ptr=n-24,this.set_type=function(t){d[this.ptr+4>>2]=t},this.get_type=function(){return d[this.ptr+4>>2]},this.set_destructor=function(t){d[this.ptr+8>>2]=t},this.get_destructor=function(){return d[this.ptr+8>>2]},this.set_refcount=function(t){y[this.ptr>>2]=t},this.set_caught=function(t){t=t?1:0,A[this.ptr+12>>0]=t},this.get_caught=function(){return A[this.ptr+12>>0]!=0},this.set_rethrown=function(t){t=t?1:0,A[this.ptr+13>>0]=t},this.get_rethrown=function(){return A[this.ptr+13>>0]!=0},this.init=function(t,r){this.set_adjusted_ptr(0),this.set_type(t),this.set_destructor(r),this.set_refcount(0),this.set_caught(!1),this.set_rethrown(!1)},this.add_ref=function(){var t=y[this.ptr>>2];y[this.ptr>>2]=t+1},this.release_ref=function(){var t=y[this.ptr>>2];return y[this.ptr>>2]=t-1,t===1},this.set_adjusted_ptr=function(t){d[this.ptr+16>>2]=t},this.get_adjusted_ptr=function(){return d[this.ptr+16>>2]},this.get_exception_ptr=function(){if($(this.get_type()))return d[this.excPtr>>2];var t=this.get_adjusted_ptr();return t!==0?t:this.excPtr}}function bn(n,t,r){throw new wn(n).init(t,r),n}function An(){x("")}function Rn(n,t,r){E.copyWithin(n,t,t+r)}function xn(){return 2147483648}function Sn(n){try{return M.grow(n-B.byteLength+65535>>>16),G(M.buffer),1}catch{}}function Pn(n){var t=E.length;n>>>=0;var r=xn();if(n>r)return!1;let o=(i,l)=>i+(l-i%l)%l;for(var s=1;s<=4;s*=2){var u=t*(1+.2/s);if(u=Math.min(u,n+100663296),Sn(Math.min(r,o(Math.max(n,u),65536))))return!0}return!1}var En={a:gn,c:vn,b:bn,d:An,f:Rn,e:Pn};yn(),e.___wasm_call_ctors=function(){return(e.___wasm_call_ctors=e.asm.h).apply(null,arguments)},e._lerc_getBlobInfo=function(){return(e._lerc_getBlobInfo=e.asm.i).apply(null,arguments)},e._lerc_getDataRanges=function(){return(e._lerc_getDataRanges=e.asm.j).apply(null,arguments)},e._lerc_decode=function(){return(e._lerc_decode=e.asm.k).apply(null,arguments)},e._lerc_decode_4D=function(){return(e._lerc_decode_4D=e.asm.l).apply(null,arguments)};var Z=e._malloc=function(){return(Z=e._malloc=e.asm.n).apply(null,arguments)};e._free=function(){return(e._free=e.asm.o).apply(null,arguments)};var j,$=e.___cxa_is_pointer_type=function(){return($=e.___cxa_is_pointer_type=e.asm.p).apply(null,arguments)};function In(n){this.name="ExitStatus",this.message="Program terminated with exit("+n+")",this.status=n}function U(n){function t(){j||(j=!0,e.calledRun=!0,z||(an(),g(e),e.onRuntimeInitialized&&e.onRuntimeInitialized(),cn()))}_>0||(sn(),_>0||(e.setStatus?(e.setStatus("Running..."),setTimeout(function(){setTimeout(function(){e.setStatus("")},1),t()},1)):t()))}if(R=function n(){j||U(),j||(R=n)},e.run=U,e.preInit)for(typeof e.preInit=="function"&&(e.preInit=[e.preInit]);e.preInit.length>0;)e.preInit.pop()();return U(),e.ready},tn.exports=rn;const Tn=jn({__proto__:null,default:T},[T]);export{Tn as l};
