package org.thingsboard.server.dao.sql.video;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.thingsboard.server.dao.model.sql.VideoConfigEntity;

@Mapper
public interface VideoConfigMapper extends BaseMapper<VideoConfigEntity> {

    @Select("select * from video_config where tenant_id = #{tenantId}")
    VideoConfigEntity selectByTenantId(@Param("tenantId") String tenantId);
}
