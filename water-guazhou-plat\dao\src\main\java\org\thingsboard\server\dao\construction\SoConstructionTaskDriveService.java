package org.thingsboard.server.dao.construction;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionRelatedEntity;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemJournal;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemScope;

public interface SoConstructionTaskDriveService<T extends SoConstructionRelatedEntity> {

    SoGeneralSystemScope getCurrentScope();

    SoGeneralSystemJournal getCurrentJournalType();

    BaseMapper<T> getDirectMapper();

}
