import{d as u,a0 as p,c as v,o as _,Q as f,g as m,n as w,p as s,aw as o,i as n,C as x}from"./index-r0dFAfgr.js";const b={class:"main"},g={class:"card zutai-card"},j={class:"card-content",style:{bottom:"23%",left:"35%",width:"140px"}},y={class:"row"},B={class:"card-content",style:{bottom:"23%",right:"44%",width:"140px"}},I={class:"row"},C={class:"card-content",style:{bottom:"23%",right:"32%",width:"140px"}},h={class:"row"},k=u({__name:"psj_overview",setup(z){const l=p(),a=v(),e=v({"反洗/排泥阀":{}}),i=()=>{console.log(l.projectList),l.projectList[0].id};return _(()=>{i(),a.value=setInterval(()=>{i()},3e4)}),f(()=>{clearInterval(a.value)}),(L,t)=>{var d,r,c;return m(),w("div",b,[s("div",g,[s("div",j,[t[1]||(t[1]=s("div",{class:"card-title"},[s("span",{style:{color:"#d8feff","text-align":"center"}},"配水井1号阀")],-1)),s("div",y,[t[0]||(t[0]=s("div",{class:"label"},"状态：",-1)),s("div",{class:o(["status",((d=n(e).配水井)==null?void 0:d.psj1_status)=="开"?"online":"unline"])},null,2)])]),s("div",B,[t[3]||(t[3]=s("div",{class:"card-title"},[s("span",{style:{color:"#d8feff","text-align":"center"}},"配水井2号阀")],-1)),s("div",I,[t[2]||(t[2]=s("div",{class:"label"},"状态：",-1)),s("div",{class:o(["status",((r=n(e).配水井)==null?void 0:r.psj1_status2)=="开"?"online":"unline"])},null,2)])]),s("div",C,[t[5]||(t[5]=s("div",{class:"card-title"},[s("span",{style:{color:"#d8feff","text-align":"center"}},"配水井3号阀")],-1)),s("div",h,[t[4]||(t[4]=s("div",{class:"label"},"状态：",-1)),s("div",{class:o(["status",((c=n(e).配水井)==null?void 0:c.psj1_status)=="开"?"online":"unline"])},null,2)])])])])}}}),V=x(k,[["__scopeId","data-v-fea9bd9d"]]);export{V as default};
