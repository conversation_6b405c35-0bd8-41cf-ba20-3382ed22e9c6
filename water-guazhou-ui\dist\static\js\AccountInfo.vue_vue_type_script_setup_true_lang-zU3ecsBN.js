import{d as b,u as F,r as _,s as v,S,a as I,b as n,c as d,e as E,o as N,f as w,g as A,h as C,i as k,_ as x}from"./index-r0dFAfgr.js";import M from"./changePassword-DW35Gs1E.js";const B=b({__name:"AccountInfo",setup(P){var u,m,c;const t=F(),f=(e,r,a)=>{/^1\d{10}$/.test(r)?a():a(new Error("请输入正确手机号"))},s=_({group:[{fields:[{type:"input",label:"电子邮件",field:"email",readonly:!0},{type:"input",label:"账号",field:"name",readonly:!0},{hidden:((u=t.user)==null?void 0:u.authority)!=="TENANT_ADMIN",type:"switch",label:"邮箱接收告警",activeColor:"#3E8EF7",field:"alarmFromEmail",aInfo:!0},{readonly:((m=t.user)==null?void 0:m.authority)==="SYS_ADMIN",type:"input",label:"联系手机",field:"phone",rules:[{required:!0,message:"请输入联系手机",trigger:"blur"},{validator:f,trigger:"blur"}]},{type:"switch",label:"手机接收告警",aInfo:!0,field:"alarmFromSms",activeColor:"#3E8EF7",onChange:()=>p()},{type:"input",label:"昵称",field:"lastName",disabled:((c=t.user)==null?void 0:c.authority)==="SYS_ADMIN",rules:[{max:16,message:"昵称输入不可超过16位",trigger:"blur"}]}]},{fields:[{type:"component",component:v(M)},{type:"btn-group",btns:[{perm:!0,text:"保存",loading:()=>!!s.submitting,click:()=>{var e;(e=o.value)==null||e.Submit()}},{perm:!0,type:"default",text:"复制API Token",click:()=>{h()}}]}]}],labelPosition:"right",labelWidth:"150px",defaultValue:{...t.user||{}},submit:e=>{S("确定提交","提示信息").then(async()=>{try{s.submitting=!0;const r=typeof e.additionalInfo=="string"?JSON.parse(e.additionalInfo):e.additionalInfo,a={...e,lastName:(e.lastName??"").trim(),additionalInfo:{...r||{}}};await I(a),l(),n.success("修改成功")}catch{n.error("系统错误")}s.submitting=!1}).catch(()=>{})}}),o=d(),p=()=>{var e;(e=o.value)!=null&&e.dataForm&&o.value.dataForm.alarmFromSms&&o.value.dataForm.phone===""&&(n.error("打开手机接收告警，必须先填写手机号"),o.value.dataForm.alarmFromSms=!1)},l=async()=>{var r,a;try{await t.GetInfo()}catch{}const e=((r=t.user)==null?void 0:r.additionalInfo)||{};s.defaultValue={...t.user||{},alarmFromEmail:e.alarmFromEmail,alarmFromSms:e.alarmFromSms},(a=o.value)==null||a.resetForm()},i=d(t.token||""),{copy:g}=E({source:i}),h=()=>{g(i.value).then(()=>{n.success("已复制到剪切板")})};return N(()=>{l()}),w(()=>{console.log("active")}),(e,r)=>{const a=x;return A(),C(a,{ref_key:"refForm",ref:o,config:k(s)},null,8,["config"])}}});export{B as _};
