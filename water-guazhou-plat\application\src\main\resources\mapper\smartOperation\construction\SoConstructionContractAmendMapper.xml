<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartOperation.construction.SoConstructionContractAmendMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->
        id,
        contract_code,
        amend_date,
        remark,
        attachments,
        creator,
        create_time,
        tenant_id<!--@sql from so_construction_contract_amend -->
    </sql>
    <resultMap id="BaseResultMap"
               type="org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionContractAmend">
        <result column="id" property="id"/>
        <result column="contract_code" property="contractCode"/>
        <result column="amend_date" property="amendDate"/>
        <result column="remark" property="remark"/>
        <result column="attachments" property="attachments"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from so_construction_contract_amend
        <where>
            <if test="contractCode != null and contractCode != ''">
                and contract_code = #{contractCode}
            </if>
            <if test="fromTime != null">
                and create_time >= #{fromTime}
            </if>
            <if test="toTime != null">
                and create_time &lt;= #{toTime}
            </if>
            and tenant_id = #{tenantId}
        </where>
        order by create_time
    </select>

    <update id="update">
        update so_construction_contract_amend
        <set>
            <if test="amendDate != null">
                amend_date = #{amendDate},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="remark != null">
                attachments = #{attachments}
            </if>
        </set>
        where id = #{id}
    </update>

    <update id="updateFully">
        update so_construction_contract_amend
        set amend_date = #{amendDate},
        remark = #{remark},
        attachments = #{attachments}
        where id = #{id}
    </update>
</mapper>