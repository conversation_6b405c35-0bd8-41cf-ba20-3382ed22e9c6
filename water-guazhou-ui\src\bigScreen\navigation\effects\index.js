/**
 * 背景特效管理器
 * 用于管理背景特效
 */

import { initWaterParticlesEffect } from './water-particles';

// 特效类型枚举
export const EffectType = {
  WATER_PARTICLES: 'water-particles'
};

// 当前活动的特效清理函数
let currentEffectCleanup = null;

/**
 * 初始化指定类型的背景特效
 * @param {string} effectType - 特效类型，使用EffectType枚举
 * @param {string} canvasId - Canvas元素的ID
 * @returns {Function} - 清理函数
 */
export function initBackgroundEffect(effectType, canvasId) {
  // 如果有正在运行的特效，先清理
  if (currentEffectCleanup) {
    currentEffectCleanup();
    currentEffectCleanup = null;
  }
  
  // 根据类型初始化对应的特效
  switch (effectType) {
    case EffectType.WATER_PARTICLES:
      currentEffectCleanup = initWaterParticlesEffect(canvasId);
      break;
    default:
      console.warn(`未知的特效类型: ${effectType}`);
      return () => {};
  }
  
  return currentEffectCleanup;
}

/**
 * 停止当前正在运行的背景特效
 */
export function stopBackgroundEffect() {
  if (currentEffectCleanup) {
    currentEffectCleanup();
    currentEffectCleanup = null;
  }
} 