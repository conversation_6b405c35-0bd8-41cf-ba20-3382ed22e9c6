package org.thingsboard.server.dao.smartService.duty;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.dao.model.sql.smartService.duty.DutyTeamC;
import org.thingsboard.server.dao.model.sql.smartService.duty.DutyTeamM;
import org.thingsboard.server.dao.sql.smartService.duty.DutyTeamCMapper;
import org.thingsboard.server.dao.sql.smartService.duty.DutyTeamMMapper;

import java.util.Date;
import java.util.List;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-10-27
 */
@Slf4j
@Service
@Transactional
public class DutyTeamMServiceImpl implements DutyTeamMService {
    @Autowired
    private DutyTeamMMapper dutyTeamMMapper;

    @Autowired
    private DutyTeamCMapper dutyTeamCMapper;

    @Override
    public List<DutyTeamM> getList(String keywords, String tenantId) {

        List<DutyTeamM> dutyTeamMS = dutyTeamMMapper.getList(keywords, tenantId);

        return dutyTeamMS;

    }

    @Override
    public DutyTeamM save(DutyTeamM dutyTeamM) {
        dutyTeamM.setUpdateTime(new Date());
        if (StringUtils.isBlank(dutyTeamM.getId())) {
            dutyTeamM.setCreateTime(new Date());
            dutyTeamMMapper.insert(dutyTeamM);
        } else {
            dutyTeamMMapper.updateById(dutyTeamM);
        }
        // 保存人员
        QueryWrapper<DutyTeamC> deleteWrapper = new QueryWrapper<>();
        deleteWrapper.eq("main_id", dutyTeamM.getId());

        dutyTeamCMapper.delete(deleteWrapper);
        for (DutyTeamC dutyTeamC : dutyTeamM.getDutyTeamCList()) {
            dutyTeamC.setTenantId(dutyTeamM.getTenantId());
            dutyTeamC.setMainId(dutyTeamM.getId());
            dutyTeamCMapper.insert(dutyTeamC);
        }

        return dutyTeamM;
    }

    @Override
    @Transactional
    public int delete(List<String> ids) {
        // 删除子表
        QueryWrapper<DutyTeamM> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("main_id", ids);

        return dutyTeamMMapper.deleteBatchIds(ids);
    }
}
