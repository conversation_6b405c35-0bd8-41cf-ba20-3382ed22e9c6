<!-- 供应商管理 -->
<template>
  <div class="wrapper">
    <CardSearch
      ref="refSearch"
      :config="cardSearchConfig"
    />
    <CardTable
      :config="TableConfig"
      class="card-table"
    ></CardTable>
    <SLDrawer
      ref="refForm"
      :config="addOrUpdateConfig"
    >
      <div class="item">
        <Form
          ref="refForm1"
          :config="addOrUpdateConfig1"
        ></Form>
        <el-tabs
          v-model="activeName"
          type="card"
          class="demo-tabs"
        >
          <el-tab-pane
            label="资质信息"
            name="first"
          >
            <FormTable :config="qualification"></FormTable>
          </el-tab-pane>
          <el-tab-pane
            label="商品信息"
            name="second"
          >
            <FormTable :config="commodity"></FormTable>
          </el-tab-pane>
        </el-tabs>
      </div>
    </SLDrawer>
    <SLDrawer
      ref="imageForm"
      :config="addImageConfig"
    ></SLDrawer>
    <!-- 设备选中 -->
    <SLDrawer
      ref="refFormDetail"
      :config="addEquipment"
    ></SLDrawer>
    <el-upload
      v-show="false"
      ref="upload"
      action="action"
      accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
      :show-file-list="false"
      :on-change="clickUpload"
    >
      <el-button
        ref="fileRef"
        size="small"
        type="primary"
      >
        点击上传
      </el-button>
    </el-upload>
  </div>
</template>

<script lang="ts" setup>
import { Refresh } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { ICONS } from '@/common/constans/common'
import { ICardSearchIns, ISLDrawerIns } from '@/components/type'
import { getSupplierSerch, deleteSupplier, getMaterialsSerch, getQualificationSerch, getDeviceListSearch, postSupplier } from '@/api/equipment_assets/equipmentManage'
import { importance, status, companySize, phoneVerification, readExcelToJson, supplierUpload } from '../../equipmentAssetsData'
import useGlobal from '@/hooks/global/useGlobal'
import { SLConfirm } from '@/utils/Message'
import { formatDate } from '@/utils/DateFormatter'
import { uniqueFunc } from '@/utils/GlobalHelper'

const { $btnPerms } = useGlobal()

const refSearch = ref<ICardSearchIns>()

const refForm = ref<ISLDrawerIns>()

const fileRef = ref()

const refForm1 = ref<ISLDrawerIns>()

const refFormDetail = ref<ISLDrawerIns>()

const imageForm = ref<ISLDrawerIns>()

const chosen = ref([])

const cardSearchConfig = ref<ISearch>({
  filters: [
    { label: '供应商名称', field: 'name', type: 'input', labelWidth: '90px' },
    { label: '地址关键字', field: 'address', type: 'input', labelWidth: '90px' },
    { label: '供应商状态', field: 'status', type: 'select', labelWidth: '90px', options: status },
    { label: '重要程度', field: 'importance', type: 'select', options: importance }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          icon: ICONS.QUERY,
          click: () => refreshData()
        },
        {
          type: 'default',
          perm: true,
          text: '重置',
          svgIcon: shallowRef(Refresh),
          click: () => {
            refSearch.value?.resetForm()
            refreshData()
          }
        },
        {
          perm: true,
          text: '新增',
          icon: ICONS.ADD,
          type: 'success',
          click: () => clickCreatedRole()
        }
      ]
    }
  ]
})

const TableConfig = reactive<ICardTable>({
  defaultExpandAll: true,
  indexVisible: true,
  columns: [
    { label: '供应商', prop: 'name' },
    { label: '地址', prop: 'address' },
    { label: '联系人', prop: 'contact' },
    { label: '联系电话', prop: 'contactPhone' },
    { label: '网址', prop: 'website' },
    {
      label: '重要程度',
      prop: 'importance',
      formatter: row => {
        const value = importance.find(item => item.value === row.importance)
        return value?.label
      }
    },
    { label: '邮箱', prop: 'email' },
    { label: '发票信息', prop: 'invoice' },
    { label: '供货类别', prop: 'deviceTypeId' },
    {
      label: '状态',
      prop: 'status',
      formatter: row => {
        const value = status.find(item => item.value === row.status)
        return value?.label
      }
    },
    { label: '备注', prop: 'remark' },
    { label: '创建人', prop: 'creatorName' },
    { label: '创建时间', prop: 'createTime', formatter: row => formatDate(row.createTime, 'YYYY-MM-DD') },
    {
      label: '规模',
      prop: 'companySize',
      formatter: row => {
        const value = companySize.find(item => item.value === row.companySize)
        return value?.label
      }
    }
  ],
  operationWidth: '200px',
  operations: [
    {
      type: 'primary',
      text: '编辑',
      icon: ICONS.EXPORT,
      perm: $btnPerms('RoleManageEdit'),
      click: row => clickEdit(row)
    },
    {
      type: 'danger',
      text: '删除',
      perm: $btnPerms('RoleManageDelete'),
      icon: ICONS.DELETE,
      click: row => haneleDelete(row)
    }
  ],
  dataList: [],
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page
      TableConfig.pagination.limit = size
      refreshData()
    }
  }
})

const qualification = reactive<ICardTable>({
  indexVisible: true,
  height: '350px',
  titleRight: [
    {
      style: {
        justifyContent: 'flex-end'
      },
      items: [
        {
          type: 'btn-group',
          btns: [
            {
              text: '添加',
              perm: true,
              click: () => {
                qualification.dataList.push({ id: guid() })
              }
            }
          ]
        }
      ]
    }
  ],
  dataList: [],
  columns: [
    {
      label: '资质名称',
      prop: 'name',
      formItemConfig: {
        type: 'input'
      }
    }, {
      label: '颁发机构',
      prop: 'organization',
      formItemConfig: {
        type: 'input'
      }
    }, {
      label: '获取时间',
      prop: 'time',
      formItemConfig: {
        type: 'date',
        format: 'x'
      }
    }
  ],
  operations: [
    {
      text: '图片',
      perm: $btnPerms('RoleManageEdit'),
      icon: ICONS.DETAIL,
      click: row => {
        addImageConfig.defaultValue = { ...(row) || {} }
        imageForm.value?.openDrawer()
      }
    },
    {
      type: 'danger',
      text: '移除',
      icon: ICONS.DELETE,
      perm: $btnPerms('RoleManageDelete'),
      click: row => {
        qualification.dataList = qualification.dataList.filter(item => item.id !== row.id)
      }
    }
  ],
  pagination: {
    hide: true
  }
})

const commodity = reactive<any>({
  indexVisible: true,
  height: '350px',
  titleRight: [
    {
      style: {
        justifyContent: 'flex-end'
      },
      items: [
        {
          type: 'btn-group',
          btns: [
            {
              text: '增加材料',
              perm: true,
              click: () => {
                data.getDevice()
                refFormDetail.value?.openDrawer()
              }
            }
            // {
            //   text: '导入',
            //   perm: true,
            //   icon: ICONS.UPLOAD,
            //   click: () => {
            //     fileRef.value.$el.click()
            //   }
            // }
          ]
        }
      ]
    }
  ],
  dataList: [],
  columns: [
    {
      label: '设备编码',
      prop: 'serialId'
    }, {
      label: '设备名称',
      prop: 'name'
    }, {
      label: '规格型号',
      prop: 'model'
    }, {
      label: '计算单位',
      prop: 'unit'
    }, {
      label: '采购数量',
      prop: 'num',
      formItemConfig: {
        type: 'input-number'
      }
    }, {
      label: '单价(元)',
      prop: 'price',
      formItemConfig: {
        type: 'input'
      }
    }, {
      label: '税率(%)',
      prop: 'taxRate',
      formItemConfig: {
        type: 'input'
      }
    }
  ],
  operations: [
    {
      type: 'danger',
      text: '移除',
      icon: ICONS.DELETE,
      perm: $btnPerms('RoleManageDelete'),
      click: row => {
        commodity.dataList = commodity.dataList.filter(item => item.id !== row.id)
      }
    }
  ],
  pagination: {
    hide: true
  }
})

const addOrUpdateConfig = reactive<IDrawerConfig>({
  title: '新增',
  labelWidth: '100px',
  submit: () => {
    refForm1.value?.Submit()
  },
  defaultValue: {},
  group: [
  ]
})

const addOrUpdateConfig1 = reactive<IDrawerConfig>({
  title: '新增',
  labelWidth: '100px',
  submit: (params: any, status?:boolean) => {
    if (status === true) {
      refFormDetail.value?.openDrawer()
      data.getDevice()
      return
    }
    let val = '添加成功'
    if (params.id) { val = '修改成功' }
    params.supplierQualificationsList = qualification.dataList
    params.supplierGoodsList = commodity.dataList
    postSupplier(params).then(() => {
      refForm.value?.closeDrawer()
      ElMessage.success(val)
      refreshData()
    }).catch(error => {
      ElMessage.warning(error)
    })
  },

  defaultValue: {},
  group: [
    {
      fields: [
        {
          xl: 18,
          type: 'input',
          label: '供应商名称',
          field: 'name',
          rules: [{ required: true, message: '请输入供应商名称' }]
        },
        {
          xl: 6,
          type: 'select',
          label: '供应商状态',
          field: 'status',
          options: status,
          rules: [{ required: true, message: '请选择供应商状态' }]
        },
        {
          xl: 6,
          type: 'select',
          label: '重要程度',
          field: 'importance',
          options: importance,
          rules: [{ required: true, message: '请选择重要程度' }]
        },
        {
          xl: 6,
          type: 'input',
          label: '邮箱',
          field: 'email',
          rules: [{ type: 'email', message: '请输入正确邮箱地址', trigger: 'blur' }]
        },
        {
          xl: 6,
          type: 'input',
          label: '供货类别',
          field: 'deviceTypeId'
        },
        {
          xl: 6,
          type: 'select',
          label: '公司规模',
          field: 'companySize',
          options: companySize
        },
        {
          xl: 6,
          type: 'input',
          label: '发票信息',
          field: 'invoice'
        },
        {
          xl: 6,
          type: 'input',
          label: '联系人',
          field: 'contact',
          rules: [{ required: true, message: '请输入联系人' }]
        },
        {
          xl: 6,
          type: 'input',
          label: '联系电话',
          field: 'contactPhone',
          rules: phoneVerification
        },
        {
          xl: 6,
          type: 'input',
          label: '网址',
          field: 'website'
        },
        {
          xl: 18,
          type: 'input',
          label: '地址',
          field: 'address',
          rules: [{ required: true, message: '请输入地址' }]
        },
        {
          xl: 24,
          type: 'textarea',
          label: '备注',
          field: 'remark'
        },
        {
          type: 'divider',
          text: '其他信息'
        }
      ]
    }
  ]
})

// 上传图片
const addImageConfig = reactive<IDrawerConfig>({
  title: '图片',
  width: '500px',
  labelWidth: '100px',
  submit: (params:any) => {
    qualification.dataList.map(item => {
      if (item.id === params.id) {
        item.files = params.files
      }
    })
    imageForm.value?.closeDrawer()
  },
  defaultValue: {},
  group: [
    {
      fields: [
        {
          type: 'image',
          label: '资质图像',
          field: 'files'
        }
      ]
    }
  ]
})

// 设备选择
const addEquipment = reactive<IDrawerConfig>({
  title: '设备选择',
  submit: (params: any, status?: boolean) => {
    // 搜索处理
    if (status) {
      data.getDevice(params)
    } else {
      commodity.dataList = [...commodity.dataList, ...chosen.value]
      commodity.dataList = uniqueFunc(commodity.dataList, 'id')
      commodity.dataList = commodity.dataList.map(item => {
        if (!item.num || item.num === null) { item.num = '0' }
        return item
      })
      refFormDetail.value?.closeDrawer()
    }
  },

  defaultValue: {},
  group: [
    {
      fields: [
        {
          xl: 8,
          type: 'input',
          label: '设备编码',
          field: 'serialId'
        },
        {
          xl: 8,
          type: 'input',
          label: '设备名称',
          field: 'name'
        },
        {
          xl: 8,
          type: 'input',
          label: '设备型号',
          field: 'model'
        },
        {
          type: 'table',
          field: 'device',
          config: {
            indexVisible: true,
            height: '350px',
            dataList: computed(() => data.deviceValue) as any,
            selectList: [],
            handleSelectChange: val => {
              chosen.value = val
            },
            titleRight: [
              {
                style: {
                  justifyContent: 'flex-end'
                },
                items: [
                  {
                    type: 'btn-group',
                    btns: [
                      {
                        text: '搜索',
                        perm: true,
                        click: () => {
                          refFormDetail.value?.Submit(true)
                        }
                      }
                    ]
                  }
                ]
              }
            ],
            columns: [
              {
                label: '材料编码',
                prop: 'serialId'
              },
              {
                label: '材料名称',
                prop: 'name'
              },
              {
                label: '型号/规格',
                prop: 'model'
              },
              {
                label: '所属类别',
                prop: 'linkedType'
              },
              {
                label: '单位',
                prop: 'unit'
              }
            ],
            pagination: {
              page: 1,
              limit: 20,
              total: computed(() => data.total) as any,
              refreshData: ({ page, size }) => {
                (addEquipment.group[0].fields[3] as any).config.pagination.page = page;
                (addEquipment.group[0].fields[3] as any).config.pagination.limit = size
                data.getDevice()
              }
            }
          }
        }
      ]
    }
  ]
})

function guid() {
  function S4() {
    return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1)
  }
  return (S4() + S4() + S4() + S4())
}

const clickCreatedRole = () => {
  addOrUpdateConfig.title = '新增'
  chosen.value = []
  addOrUpdateConfig1.defaultValue = {}
  // 商品信息
  commodity.dataList = []
  // 资格信息
  qualification.dataList = []
  refForm.value?.openDrawer()
}

// 上传文件
async function clickUpload(file: any) {
  console.log(file)
  readExcelToJson(file).then((res: any) => {
    let i = 1
    res && res.forEach(el => {
      const val = { number: '' + i++ }
      for (const i in el) {
        val[supplierUpload[i]] = el[i]
      }
      data.selectList.push(val)
    })
    const strings = data.selectList.map(item => JSON.stringify(item))
    const removeDupList = [...new Set(strings)] // 也可以使用Array.from(new Set(strings))
    data.selectList = removeDupList.map((item: any) => JSON.parse(item))
    commodity.dataList = data.selectList
  })
}

const clickEdit = (row: { [x: string]: any }) => {
  addOrUpdateConfig.title = '编辑'
  addOrUpdateConfig1.defaultValue = { ...(row) || {} }
  refForm.value?.openDrawer()
  // 商品信息
  commodity.dataList = row?.supplierGoodsList || []
  // 资格信息
  qualification.dataList = row?.supplierQualificationsList || []
  qualification.dataList = qualification.dataList.map(item => {
    item.time /= 1000
    if (item.id.length === 16) { delete item.id }
  })
  getMaterialsValue(row)
  getQualificationValue(row)
}

const haneleDelete = (row: { id: any }) => {
  SLConfirm('确定删除该供应商, 是否继续?', '删除提示').then(() => {
    // 删除公告方法
    deleteSupplier([row.id])
      .then(() => {
        refreshData()
        ElMessage.success('删除成功')
      })
      .catch(err => {
        console.log(err, 'err')
        ElMessage.error(err.data.message)
      })
  }).catch(error => {
    ElMessage.warning(error)
  })
}

const activeName = ref('first')

// 获取物资信息
function getMaterialsValue(params: any) {
  getMaterialsSerch(params.id).then(res => {
    commodity.dataList = res.data.data || []
  })
}

// 获取资质信息
function getQualificationValue(params: any) {
  getQualificationSerch(params.id).then(res => {
    qualification.dataList = res.data.data || []
  })
}

const data = reactive({
  // 设备列表
  deviceValue: [],
  total: 0,
  // 当前选中列表
  selectList: [] as any[],
  getDevice: (param?: any) => {
    const params = {
      size: TableConfig.pagination.limit,
      page: TableConfig.pagination.page,
      ...param
    }
    delete params.device
    getDeviceListSearch(params).then(res => {
      data.deviceValue = res.data.data.data || []
      data.total = res.data.data.total || 0
    }).catch(error => {
      ElMessage.warning(error)
    })
  }
})

const refreshData = async () => {
  const params = {
    size: TableConfig.pagination.limit,
    page: TableConfig.pagination.page,
    name: '',
    address: '',
    status: '',
    importance: '',
    ...(refSearch.value?.queryParams || {})
  }
  getSupplierSerch(params).then(res => {
    TableConfig.dataList = res.data.data.data || []
    TableConfig.pagination.total = res.data.data.total || 0
  }).catch(error => {
    ElMessage.warning(error)
  })
}

onMounted(() => {
  refreshData()
})
</script>
