import{_ as S}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as w}from"./CardTable-rdWOL4_6.js";import{_ as L}from"./CardSearch-CB_HNR-Q.js";import{z as g,C as v,c as T,r as h,b as n,S as F,o as A,g as k,n as B,q as b}from"./index-r0dFAfgr.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";function q(c){return g({url:"/api/base/scope/configuration/list",method:"get",params:c})}function P(c){return g({url:"/api/base/scope/configuration/getDetail",method:"get",params:{id:c}})}function C(c){return g({url:"/api/base/scope/configuration/add",method:"post",data:c})}function X(c){return g({url:"/api/base/scope/configuration/edit",method:"post",data:c})}function I(c){return g({url:"/api/base/scope/configuration/deleteIds",method:"delete",data:c})}const V={class:"wrapper"},E={__name:"scopeConfig",setup(c){const x=T(),d=T(),m=(e,i,r)=>{if(!i){r();return}/^-?\d+(\.\d+)?$/.test(i)?r():r(new Error("只能输入数字和小数点"))},Y=h({labelWidth:"100px",filters:[{type:"input",label:"范围名称",field:"name",placeholder:"请输入范围名称",onChange:()=>u()},{type:"btn-group",btns:[{type:"primary",perm:!0,text:"查询",click:()=>u()},{perm:!0,type:"primary",text:"新增",click:()=>y()},{perm:!0,type:"danger",text:"批量删除",click:()=>_()}]}],defaultParams:{}}),a=h({columns:[{label:"范围名称",prop:"name"},{label:"区域类型",prop:"regionType"},{label:"最小X坐标",prop:"minX"},{label:"最小Y坐标",prop:"minY"},{label:"最大X坐标",prop:"maxX"},{label:"最大Y坐标",prop:"maxY"},{label:"描述",prop:"description",showOverflowTooltip:!0}],dataList:[],operations:[{perm:!0,type:"primary",isTextBtn:!0,text:"查看详情",click:e=>D(e)},{perm:!0,type:"primary",isTextBtn:!0,text:"编辑",click:e=>y(e)},{perm:!0,type:"danger",isTextBtn:!0,text:"删除",click:e=>_(e)}],pagination:{total:0,page:1,align:"right",limit:20,handlePage:e=>{a.pagination.page=e,u()},handleSize:e=>{a.pagination.limit=e,u()}},handleSelectChange:e=>{a.selectList=e||[]}}),t=h({title:"新增范围设置",group:[{fields:[{type:"input",label:"范围名称",field:"name",rules:[{required:!0,message:"请输入范围名称"}]},{type:"input",label:"区域类型",field:"regionType",rules:[{required:!0,message:"请输入区域类型"}]},{type:"input",label:"最小X坐标",field:"minX",placeholder:"请输入数字或小数",rules:[{required:!0,message:"请输入最小X坐标"},{validator:m,trigger:"blur"}]},{type:"input",label:"最小Y坐标",field:"minY",placeholder:"请输入数字或小数",rules:[{required:!0,message:"请输入最小Y坐标"},{validator:m,trigger:"blur"}]},{type:"input",label:"最大X坐标",field:"maxX",placeholder:"请输入数字或小数",rules:[{required:!0,message:"请输入最大X坐标"},{validator:m,trigger:"blur"}]},{type:"input",label:"最大Y坐标",field:"maxY",placeholder:"请输入数字或小数",rules:[{required:!0,message:"请输入最大Y坐标"},{validator:m,trigger:"blur"}]},{type:"textarea",label:"描述",field:"description",placeholder:"请输入描述信息"}]}],labelPosition:"top",defaultValue:{},dialogWidth:600,draggable:!0,showSubmit:!0,showCancel:!0,cancelText:"取消",submitText:"确定",submit:async e=>{var i;try{if(parseFloat(e.minX)>=parseFloat(e.maxX)){n.error("最小X坐标应该小于最大X坐标");return}if(parseFloat(e.minY)>=parseFloat(e.maxY)){n.error("最小Y坐标应该小于最大Y坐标");return}e.id?(await X(e),n.success("修改成功")):(await C(e),n.success("新增成功")),(i=d.value)==null||i.closeDialog(),u()}catch{n.error("操作失败")}}}),f=()=>{t.group[0].fields.forEach(e=>{e.disabled=!1,e.readonly=!1}),t.showSubmit=!0,t.showCancel=!0,t.cancelText="取消",t.submitText="确定",t.submit=async e=>{var i;try{if(parseFloat(e.minX)>=parseFloat(e.maxX)){n.error("最小X坐标应该小于最大X坐标");return}if(parseFloat(e.minY)>=parseFloat(e.maxY)){n.error("最小Y坐标应该小于最大Y坐标");return}e.id?(await X(e),n.success("修改成功")):(await C(e),n.success("新增成功")),(i=d.value)==null||i.closeDialog(),u()}catch{n.error("操作失败")}},t.footerBtns=void 0},y=e=>{var i;f(),t.title=e?"编辑范围设置":"新增范围设置",t.defaultValue={...e||{}},(i=d.value)==null||i.openDialog()},D=async e=>{var r,o;const i={id:e.id||"1",name:e.name||"测试范围详情",regionType:e.regionType||"矩形区域",minX:e.minX||"116.3",minY:e.minY||"39.9",maxX:e.maxX||"116.5",maxY:e.maxY||"40.1",description:e.description||"这是范围设置的详情数据"};try{console.log("获取详情，行数据:",e);const l=await P(e.id);console.log("详情API响应:",l);let s=null;l.data?l.data.data?s=l.data.data:s=l.data:l&&(s=l),console.log("解析后的详情数据:",s),s||(console.log("使用模拟详情数据"),s=i),f(),t.title="范围设置详情",t.defaultValue={...s},console.log("设置的详情数据:",t.defaultValue),t.group[0].fields.forEach(p=>{p.disabled=!0,p.readonly=!0}),t.showSubmit=!1,t.showCancel=!0,t.cancel=!0,t.cancelText="关闭",t.submitText=void 0,t.submit=void 0,t.submitting=!1,t.footerBtns=[{text:"关闭",type:"default",click:()=>{var p;(p=d.value)==null||p.closeDialog()}}],console.log("详情模式DialogFormConfig配置:",{showSubmit:t.showSubmit,showCancel:t.showCancel,cancel:t.cancel,cancelText:t.cancelText,submitText:t.submitText,submit:t.submit,footerBtns:t.footerBtns}),(r=d.value)==null||r.openDialog()}catch(l){console.error("获取详情失败:",l),console.log("API调用失败，使用模拟详情数据"),f(),t.title="范围设置详情",t.defaultValue={...i},t.group[0].fields.forEach(s=>{s.disabled=!0,s.readonly=!0}),t.showSubmit=!1,t.showCancel=!0,t.cancel=!0,t.cancelText="关闭",t.submitText=void 0,t.submit=void 0,t.submitting=!1,t.footerBtns=[{text:"关闭",type:"default",click:()=>{var s;(s=d.value)==null||s.closeDialog()}}],console.log("详情模式DialogFormConfig配置:",{showSubmit:t.showSubmit,showCancel:t.showCancel,cancel:t.cancel,cancelText:t.cancelText,submitText:t.submitText,submit:t.submit,footerBtns:t.footerBtns}),(o=d.value)==null||o.openDialog(),n.error("API调用失败，当前显示模拟数据")}},_=e=>{F("确定删除？","删除提示").then(async()=>{var i;try{const r=e?[e.id]:((i=a.selectList)==null?void 0:i.map(l=>l.id))||[];if(!r.length){n.warning("请选择要删除的数据");return}(await I(r)).data?(n.success("删除成功"),u()):n.error("删除失败")}catch{n.error("删除失败")}}).catch(()=>{})},u=async()=>{var i;const e=[{id:"1",name:"城市中心区域",regionType:"矩形区域",minX:"116.3",minY:"39.9",maxX:"116.5",maxY:"40.1",description:"北京市中心区域范围设置"},{id:"2",name:"工业园区",regionType:"多边形区域",minX:"116.1",minY:"39.7",maxX:"116.3",maxY:"39.9",description:"工业园区管理范围"}];try{const r=(i=x.value)==null?void 0:i.queryParams;console.log("请求参数:",{page:a.pagination.page,size:a.pagination.limit,...r||{}});const o=await q({page:a.pagination.page,size:a.pagination.limit,...r||{}});console.log("API响应数据:",o),o.data?o.data.records?(a.dataList=o.data.records||[],a.pagination.total=o.data.total||0):o.data.data&&o.data.data.records?(a.dataList=o.data.data.records||[],a.pagination.total=o.data.data.total||0):Array.isArray(o.data)?(a.dataList=o.data,a.pagination.total=o.data.length):Array.isArray(o.data.data)?(a.dataList=o.data.data,a.pagination.total=o.data.data.length):(console.warn("未知的数据结构:",o.data),a.dataList=[],a.pagination.total=0):Array.isArray(o)?(a.dataList=o,a.pagination.total=o.length):(console.warn("无法解析的响应格式:",o),a.dataList=[],a.pagination.total=0),console.log("解析后的数据:",a.dataList),console.log("总数:",a.pagination.total),a.dataList.length===0&&(console.log("使用模拟数据进行测试"),a.dataList=e,a.pagination.total=e.length)}catch(r){console.error("获取数据失败:",r),console.log("API调用失败，使用模拟数据"),a.dataList=e,a.pagination.total=e.length,n.error("API调用失败，当前显示模拟数据")}};return A(()=>{u()}),(e,i)=>{const r=L,o=w,l=S;return k(),B("div",V,[b(r,{ref_key:"refSearch",ref:x,config:Y},null,8,["config"]),b(o,{class:"card-table",config:a},null,8,["config"]),b(l,{ref_key:"refDialogForm",ref:d,config:t},null,8,["config"])])}}},j=v(E,[["__scopeId","data-v-7924c7d4"]]);export{j as default};
