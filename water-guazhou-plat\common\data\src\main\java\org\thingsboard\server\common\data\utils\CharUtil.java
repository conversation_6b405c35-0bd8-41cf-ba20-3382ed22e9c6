/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.common.data.utils;

import java.util.regex.Pattern;

/**
 * 字符通用工具类
 * 
 * <AUTHOR> href="http://www.micmiu.com"></a><PERSON></a>
 */
public class CharUtil {
 
	/**
	 * @param args
	 */
//	public static void main(String[] args) {
//		String[] strArr = new String[] { "www.micmiu.com",
//				"!@#$%^&amp;*()_+{}[]|\"'?/:;<>,.", "！￥……（）——：；“”‘’《》，。？、", "不要啊",
//				"やめて", "韩佳人", "한가인" };
//		for (String str : strArr) {
//			//System.out.println("===========> 测试字符串：" + str);
//			//System.out.println("正则判断：" + isChineseByREG(str) + " -- "
////					+ isChineseByName(str));
//			//System.out.println("Unicode判断结果 ：" + isChinese(str));
//			//System.out.println("详细判断列表：");
//			char[] ch = str.toCharArray();
//			for (int i = 0; i < ch.length; i++) {
//				char c = ch[i];
//				//System.out.println(c + " --> " + (isChinese(c) ? "是" : "否"));
//			}
//		}
//		//System.out.println(new String(Base64.encodeBase64("A相输出频率".getBytes())));
//
//	}
 
	// 根据Unicode编码完美的判断中文汉字和符号
	private static boolean isChinese(char c) {
		Character.UnicodeBlock ub = Character.UnicodeBlock.of(c);
		if (ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS
				|| ub == Character.UnicodeBlock.CJK_COMPATIBILITY_IDEOGRAPHS
				|| ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_A
				|| ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_B
				/*|| ub == Character.UnicodeBlock.CJK_SYMBOLS_AND_PUNCTUATION*/
				/*|| ub == Character.UnicodeBlock.HALFWIDTH_AND_FULLWIDTH_FORMS*/
				/*|| ub == Character.UnicodeBlock.GENERAL_PUNCTUATION*/) {
			return true;
		}
		return false;
	}
 
	// 完整的判断中文汉字和符号
	public static boolean isChinese(String strName) {
		char[] ch = strName.toCharArray();
		for (int i = 0; i < ch.length; i++) {
			char c = ch[i];
			if (isChinese(c)) {
				return true;
			}
		}
		return false;
	}
 
	// 只能判断部分CJK字符（CJK统一汉字）
	public static boolean isChineseByREG(String str) {
		if (str == null) {
			return false;
		}
		Pattern pattern = Pattern.compile("[\\u4E00-\\u9FBF]+");
		return pattern.matcher(str.trim()).find();
	}
 
	// 只能判断部分CJK字符（CJK统一汉字）
	public static boolean isChineseByName(String str) {
		if (str == null) {
			return false;
		}
		// 大小写不同：\\p 表示包含，\\P 表示不包含 
		// \\p{Cn} 的意思为 Unicode 中未被定义字符的编码，\\P{Cn} 就表示 Unicode中已经被定义字符的编码
		String reg = "\\p{InCJK Unified Ideographs}&amp;&amp;\\P{Cn}";
		Pattern pattern = Pattern.compile(reg);
		return pattern.matcher(str.trim()).find();
	}
}