import{d as U,c as R,r as y,s as V,o as $,am as z,j as E,g as O,n as W,q as A,i as N,p as Y,_ as G,aq as J,C as K}from"./index-r0dFAfgr.js";import{w as Q}from"./Point-WxyopZva.js";import{P}from"./index-CcDafpIP.js";import{r as j}from"./chart-wy3NEK2T.js";import{g as X}from"./URLHelper-B9aplt5w.js";import{g as Z}from"./monitoringOverview-DvKhtmcR.js";const aa={class:"onemap-panel-wrapper"},ta={class:"table-box"},ea=U({__name:"fireHydrant",props:{view:{},menu:{}},emits:["highlightMark","addMarks"],setup(q,{emit:H}){const w=H,_=q,C=R([{label:"0 个",value:"压力监测点总数"},{label:"0 %",value:"报警率"}]),k=R(),f=[{name:"online",label:"在线"},{name:"alarm",label:"报警"},{name:"offline",label:"离线"}],v=y({group:[{id:"chart",fieldset:{desc:"监测状态统计",type:"underline",style:{marginTop:0}},fields:[{type:"vchart",option:j(),style:{height:"150px"}}]},{id:"tab",fields:[{type:"input",field:"name",appendBtns:[{perm:!0,text:"刷新",click:()=>r(!0)}],onChange:()=>r()},{type:"tabs",field:"type",tabs:[{label:"全部",value:"all"},...f.map(t=>({...t,value:t.name}))],tabType:"simple",onChange:()=>r()}]}],labelPosition:"top",gutter:12,defaultValue:{type:"all"}}),i=y({indexVisible:!0,dataList:[],pagination:{hide:!0,refreshData:({page:t,size:n})=>{i.pagination.page=t,i.pagination.limit=n},layout:"total,sizes, jumper"},handleRowClick:t=>S(t),columns:[{width:120,label:"名称",prop:"name",sortable:!0},{width:80,label:"压力(bar)",prop:"pressure",sortable:!0},{width:80,label:"状态",prop:"status",formatter:t=>{var n;return((n=f.find(p=>p.name===t.status))==null?void 0:n.label)||t.status}},{width:160,label:"更新时间",prop:"lastTime",sortable:!0}]}),x=y({dataList:[],columns:[],pagination:{refreshData:({page:t,size:n})=>{x.pagination.page=t,x.pagination.limit=n,r()}}}),r=async t=>{var n,p,m,F,I,D,T,L;i.loading=!0;try{const g=(n=k.value)==null?void 0:n.dataForm.type,b=await Z({status:g==="all"?"":g});i.dataList=((p=b.data)==null?void 0:p.data)||[];const M=v.group[0].fields[0],h=((F=(m=b.data)==null?void 0:m.data)==null?void 0:F.length)||0,B=[],u=[];if((D=(I=b.data)==null?void 0:I.data)!=null&&D.map(a=>{var c,d;const e=(c=a.location)==null?void 0:c.split(",");if((e==null?void 0:e.length)===2){const l=new Q({longitude:e[0],latitude:e[1],spatialReference:(d=_.view)==null?void 0:d.spatialReference});B.push({visible:!1,x:l.x,y:l.y,offsetY:-40,id:a.stationId,title:a.name,customComponent:V(P),customConfig:{info:{type:"attrs",imageUrl:a.imgs,stationId:a.stationId}},attributes:{path:_.menu.path,id:a.stationId,row:a},symbolConfig:{url:X("消防栓.png")}})}let s=u.find(l=>l.name===a.status);const{label:o}=f.find(l=>l.name===a.status)||{};s?s.value++:(s={name:a.status,nameAlias:o,value:1,scale:"0%"},u.push(s))}),u.map(a=>{var e,s,o;return a.scale=h===0?"0%":(Number(a.value)/h*100).toFixed(2)+"%",a.value=((o=(s=(e=b.data)==null?void 0:e.data)==null?void 0:s.filter(c=>c.status===a.name))==null?void 0:o.length)||0,a}),t){const a=(T=v.group.find(e=>e.id==="tab"))==null?void 0:T.fields[1];if(a){a.tabs=a.tabs.map(s=>{var d;const o=u.find(l=>l.name===s.value),c=((d=f.find(l=>l.name===s.value))==null?void 0:d.label)||"";return s.label=c+"("+((o==null?void 0:o.value)||0)+")",s});const e=a.tabs.find(s=>s.value==="all");e&&(e.label="全部("+h+")"),M&&(M.option=j(u,"个","",0))}C.value[0].label=h+"个",C.value[1].label=((L=u.find(e=>e.name==="alarm"))==null?void 0:L.scale)||"0 %"}w("addMarks",{windows:B,customWinComp:V(P)})}catch(g){console.dir(g)}i.loading=!1},S=async t=>{w("highlightMark",_.menu,t==null?void 0:t.stationId)};return $(()=>{r(!0)}),z(()=>E().isDark,()=>r(!0)),(t,n)=>{const p=G,m=J;return O(),W("div",aa,[A(p,{ref_key:"refForm",ref:k,config:N(v)},null,8,["config"]),Y("div",ta,[A(m,{config:N(i)},null,8,["config"])])])}}}),pa=K(ea,[["__scopeId","data-v-32d1a81b"]]);export{pa as default};
