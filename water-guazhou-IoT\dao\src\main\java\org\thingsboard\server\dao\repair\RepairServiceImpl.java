/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.repair;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.repair.Repair;
import org.thingsboard.server.common.data.repair.RepairType;

import java.util.List;
import java.util.UUID;

@Slf4j
@Service
@Transactional
public class RepairServiceImpl implements RepairService {

    @Autowired
    private RepairDao repairDao;

    @Override
    public Repair addRepair(Repair repair) {
        // 补全数据
        repair.setCreatedTime(System.currentTimeMillis());
        repair.setStatus(RepairType.WAITING);

        return repairDao.save(repair);
    }

    @Override
    public List<Repair> findByStatus(RepairType repairType) {
        return repairDao.findByStatus(repairType.getCode());
    }

    @Override
    public void updateStatus(List<String> needUpdateRepairIdList, String status) {
        repairDao.updateStatus(needUpdateRepairIdList, status);
    }

    @Override
    public List<Repair> findByTenantId(TenantId tenantId) {
        return repairDao.findByTenantId(tenantId);
    }

    @Override
    public Repair findById(UUID id) {
        return repairDao.findById(id);
    }

    @Override
    public Repair update(Repair repair) {
        return repairDao.save(repair);
    }
}
