package org.thingsboard.server.dao.smartService.portal;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.smartService.portal.SsPortalComment;
import org.thingsboard.server.dao.util.imodel.query.smartService.portal.SsPortalCommentPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartService.portal.SsPortalCommentSaveRequest;

public interface SsPortalCommentService {
    SsPortalComment findById(String id);

    IPage<SsPortalComment> findAllConditional(SsPortalCommentPageRequest request);

    SsPortalComment save(SsPortalCommentSaveRequest entity);

    boolean update(SsPortalComment entity);

    boolean delete(String id);

    boolean canSave(String tenantId);

}
