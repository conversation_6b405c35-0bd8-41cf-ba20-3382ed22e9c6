import{l as C,bi as M,c$ as B,d0 as G,ag as P}from"./MapView-DaoQedLH.js";import{U as D}from"./pe-B8dP0-Ut.js";import{N as S}from"./Point-WxyopZva.js";import{R as u,aO as T,b2 as h,T as U}from"./index-r0dFAfgr.js";import{e as I}from"./mat3f64-BVJGbF0t.js";import{p as j,m as q,c as z,g as V}from"./MeshComponent-CfWisxg8.js";import{c as F,x as O,L as k,O as L,i as _,E as K,T as Q,u as X}from"./BufferView-BcX1hwIm.js";import{t as Y,r as Z,o as H,a as J,f as W,e as tt,n as et}from"./vec33-BEptSvzS.js";import{n as rt,m as ot,r as b,a as nt,f as st,t as E,b as at,d as it,o as ut,e as ct,c as lt,i as ft,g as mt,h as pt,j as $t}from"./DefaultMaterial_COLOR_GAMMA-BI6mUG8I.js";import{_ as dt}from"./georeference-GB1Pt0mj.js";import{t as xt}from"./resourceUtils-CLKdXOwM.js";import{u as gt}from"./Indices-iFKW8TWb.js";import{E as A,D as R}from"./enums-BDQrMlcz.js";import"./widget-BcWKanF2.js";import"./imageUtils-IEWq71TJ.js";import"./types-Cezv0Yl1.js";import"./mat4f64-BCm7QTSd.js";import"./Version-Q4YOKegY.js";import"./quat-CM9ioDFt.js";import"./quatf64-QCogZAoR.js";import"./spatialReferenceEllipsoidUtils-j_kxMN-4.js";import"./axisAngleDegrees-CVgmQKGQ.js";import"./projection-oyk5Uk7v.js";import"./basicInterfaces-Dc_Mm1a-.js";async function te(t,e,n){const s=new rt(Tt(n)),r=(await ot(s,e,n,!0)).model,m=r.lods.shift(),c=new Map,l=new Map;r.textures.forEach((g,y)=>c.set(y,vt(g))),r.materials.forEach((g,y)=>l.set(y,wt(g,c)));const i=bt(m);for(const g of i.parts)yt(i,g,l);const{position:$,normal:f,tangent:o,color:a,texCoord0:p}=i.vertexAttributes,x={position:$.typedBuffer,normal:u(f)?f.typedBuffer:null,tangent:u(o)?o.typedBuffer:null,uv:u(p)?p.typedBuffer:null,color:u(a)?a.typedBuffer:null},w=dt(x,t,n);return{transform:w.transform,components:i.components,spatialReference:t.spatialReference,vertexAttributes:new j({position:w.vertexAttributes.position,normal:w.vertexAttributes.normal,tangent:w.vertexAttributes.tangent,color:x.color,uv:x.uv})}}function Tt(t){const e=t==null?void 0:t.resolveFile;return e?{busy:!1,request:async(n,s,r)=>{const m=e(n);return(await D(m,{responseType:s==="image"?"image":s==="binary"?"array-buffer":"json",signal:u(r)?r.signal:null})).data}}:null}function v(t,e){if(U(t))return"-";const n=t.typedBuffer;return`${S(e,n.buffer,()=>e.size)}/${n.byteOffset}/${n.byteLength}`}function ht(t){return u(t)?t.toString():"-"}function bt(t){let e=0;const n={color:!1,tangent:!1,normal:!1,texCoord0:!1},s=new Map,r=new Map,m=[];for(const c of t.parts){const{attributes:{position:l,normal:i,color:$,tangent:f,texCoord0:o}}=c,a=`
      ${v(l,s)}/
      ${v(i,s)}/
      ${v($,s)}/
      ${v(f,s)}/
      ${v(o,s)}/
      ${ht(c.transform)}
    `;let p=!1;const x=S(r,a,()=>(p=!0,{start:e,length:l.count}));p&&(e+=l.count),i&&(n.normal=!0),$&&(n.color=!0),f&&(n.tangent=!0),o&&(n.texCoord0=!0),m.push({gltf:c,writeVertices:p,region:x})}return{vertexAttributes:{position:b(Q,e),normal:n.normal?b(_,e):null,tangent:n.tangent?b(F,e):null,color:n.color?b(O,e):null,texCoord0:n.texCoord0?b(X,e):null},parts:m,components:[]}}function vt(t){return new q({data:(xt(t.data),t.data),wrap:Ct(t.parameters.wrap)})}function wt(t,e){const n=new C(Mt(t.color,t.opacity)),s=t.emissiveFactor?new C(Bt(t.emissiveFactor)):null;return new z({color:n,colorTexture:T(h(t.textureColor,r=>e.get(r))),normalTexture:T(h(t.textureNormal,r=>e.get(r))),emissiveColor:s,emissiveTexture:T(h(t.textureEmissive,r=>e.get(r))),occlusionTexture:T(h(t.textureOcclusion,r=>e.get(r))),alphaMode:Rt(t.alphaMode),alphaCutoff:t.alphaCutoff,doubleSided:t.doubleSided,metallic:t.metallicFactor,roughness:t.roughnessFactor,metallicRoughnessTexture:T(h(t.textureMetallicRoughness,r=>e.get(r))),colorTextureTransform:t.colorTextureTransform,normalTextureTransform:t.normalTextureTransform,occlusionTextureTransform:t.occlusionTextureTransform,emissiveTextureTransform:t.emissiveTextureTransform,metallicRoughnessTextureTransform:t.metallicRoughnessTextureTransform})}function yt(t,e,n){e.writeVertices&&Et(t,e);const s=e.gltf,r=At(s.indices||s.attributes.position.count,s.primitiveType),m=e.region.start;if(m)for(let c=0;c<r.length;c++)r[c]+=m;t.components.push(new V({faces:r,material:n.get(s.material),trustSourceNormals:!0}))}function Et(t,e){const{position:n,normal:s,tangent:r,color:m,texCoord0:c}=t.vertexAttributes,l=e.region.start,{attributes:i,transform:$}=e.gltf,f=i.position.count;if(Y(n.slice(l,f),i.position,$),u(i.normal)&&u(s)){const o=M(I(),$),a=s.slice(l,f);Z(a,i.normal,o),B(o)&&H(a,a)}else u(s)&&J(s,0,0,1,{dstIndex:l,count:f});if(u(i.tangent)&&u(r)){const o=M(I(),$),a=r.slice(l,f);nt(a,i.tangent,o),B(o)&&st(a,a)}else u(r)&&E(r,0,0,1,1,{dstIndex:l,count:f});if(u(i.texCoord0)&&u(c)?at(c.slice(l,f),i.texCoord0):u(c)&&it(c,0,0,{dstIndex:l,count:f}),u(i.color)&&u(m)){const o=i.color,a=m.slice(l,f);if(o.elementCount===4)o instanceof F?ut(a,o,255):o instanceof O?ct(a,o):o instanceof k&&lt(a,o,8);else{E(a,255,255,255,255);const p=L.fromTypedArray(a.typedBuffer,a.typedBufferStride);o instanceof _?W(p,o,255):o instanceof L?tt(p,o):o instanceof K&&et(p,o,8)}}else u(m)&&E(m.slice(l,f),255,255,255,255)}function At(t,e){switch(e){case A.TRIANGLES:return pt(t,gt);case A.TRIANGLE_STRIP:return mt(t);case A.TRIANGLE_FAN:return ft(t)}}function Rt(t){switch(t){case"OPAQUE":return"opaque";case"MASK":return"mask";case"BLEND":return"blend"}}function Ct(t){return{horizontal:N(t.s),vertical:N(t.t)}}function N(t){switch(t){case R.CLAMP_TO_EDGE:return"clamp";case R.MIRRORED_REPEAT:return"mirror";case R.REPEAT:return"repeat"}}function d(t){return t**(1/$t)*255}function Mt(t,e){return G(d(t[0]),d(t[1]),d(t[2]),e)}function Bt(t){return P(d(t[0]),d(t[1]),d(t[2]))}export{te as loadGLTFMesh};
