package org.thingsboard.server.dao.util.imodel.query.plan;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.plan.PlanTask;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;

import java.util.Date;

@Getter
@Setter
public class PlanTaskPageRequest extends AdvancedPageableQueryEntity<PlanTask, PlanTaskPageRequest> {
    // 关键字
    private String keyWord;

    // 盘点目标仓库ID
    private String storehouseId;

    // 盘点人ID
    private String executionUserId;

    // 盘点人ID
    private String executionDepartmentId;

    // 任务类型。临时任务/计划任务
    private String type;

    // 是否超期
    private Boolean isOverdue;

    // 任务状态
    private String status;

    // 开始日期
    private String startTime;

    // 结束日期
    private String endTime;

    public Date getStartTime() {
        return toDate(startTime);
    }

    public Date getEndTime() {
        return toDate(endTime);
    }
}
