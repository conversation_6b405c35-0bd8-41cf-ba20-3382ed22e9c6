package org.thingsboard.server.controller.leakDetectionManagement;

import lombok.Data;

/**
 * 流量异常详情数据传输对象
 */
@Data
public class FlowAbnormalDetailDto {
    /**
     * 水表编号
     */
    private String meterId;
    
    /**
     * 用户名称
     */
    private String userName;
    
    /**
     * 用户地址
     */
    private String address;
    
    /**
     * 异常类型
     */
    private String abnormalType;
    
    /**
     * 当前流量
     */
    private String currentFlow;
    
    /**
     * 历史平均流量
     */
    private String avgFlow;
    
    /**
     * 异常程度：0-4
     * 0: 轻微
     * 1: 一般
     * 2: 中度
     * 3: 严重
     * 4: 极严重
     */
    private int abnormalLevel;
} 