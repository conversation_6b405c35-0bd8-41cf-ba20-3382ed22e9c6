package org.thingsboard.server.controller.workOrder;

import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrderResource;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrderResourceTypeRelation;
import org.thingsboard.server.dao.orderWork.WorkOrderResourceService;
import org.thingsboard.server.dao.orderWork.WorkOrderResourceTypeRelationService;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.List;

/**
 * 工单数据来源信息
 */
@RestController
@RequestMapping("api/workOrderResource")
public class WorkOrderResourceController extends BaseController {

    @Autowired
    private WorkOrderResourceService workOrderResourceService;
    @Autowired
    private WorkOrderResourceTypeRelationService workOrderResourceTypeRelationService;

    @GetMapping("list")
    public IstarResponse findList(@RequestParam int page, @RequestParam int size,
                                  @RequestParam(required = false, defaultValue = "") String status) throws ThingsboardException {
        return IstarResponse.ok(workOrderResourceService.findList(page, size, status, getTenantId()));
    }

    @GetMapping("all")
    public IstarResponse findAll(@RequestParam(required = false, defaultValue = "1") String status) throws ThingsboardException {
        return IstarResponse.ok(workOrderResourceService.findAll(status, getTenantId()));
    }

    @PostMapping("save")
    public IstarResponse save(@RequestBody WorkOrderResource entity) throws ThingsboardException {
        entity.setTenantId(UUIDConverter.fromTimeUUID(getTenantId().getId()));
        workOrderResourceService.save(entity);
        return IstarResponse.ok();
    }

    @PostMapping("restore")
    public IstarResponse restore(@RequestBody JSONObject param) {
        String id = param.getString("id");
        workOrderResourceService.changeStatus("1", id);

        return IstarResponse.ok();
    }

    @PostMapping("remove")
    public IstarResponse remove(@RequestBody List<String> ids) {
        for (String id : ids) {
            workOrderResourceService.changeStatus("2", id);
        }
        return IstarResponse.ok();
    }

    @PostMapping("saveResourceTypeRelation")
    public IstarResponse saveResourceTypeRelation(@RequestBody WorkOrderResourceTypeRelation relation) {
        workOrderResourceTypeRelationService.save(relation);
        return IstarResponse.ok();
    }


}
