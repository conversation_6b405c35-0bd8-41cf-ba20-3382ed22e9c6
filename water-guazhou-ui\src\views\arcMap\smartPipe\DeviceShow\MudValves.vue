<!-- 排泥阀展示 -->
<template>
  <RightDrawerMap
    ref="refMap"
    title="排泥阀展示"
    @map-loaded="onMaploaded"
  >
    <Form
      ref="refForm"
      :config="FormConfig"
    ></Form>
  </RightDrawerMap>
</template>
<script lang="ts" setup>
import { queryLayerClassName } from '@/api/mapservice'
import { IFormIns } from '@/components/type'
import { getLayerOids, getSubLayerIds } from '@/utils/MapHelper'
import RightDrawerMap from '../../components/common/RightDrawerMap.vue'
import { SLMessage } from '@/utils/Message'
import {
  GetFieldConfig,
  GetFieldUniqueValue
} from '@/api/mapservice/fieldconfig'
// import RangeSelector from '@/components/Form/RangeSelecter.vue'

const refForm = ref<IFormIns>()
const refMap = ref<InstanceType<typeof RightDrawerMap>>()
const staticState: {
  view?: __esri.MapView
  graphicsLayer?: __esri.GraphicsLayer
} = {}
const state = reactive<{
  tabs: any[]
  loading: boolean
  layerIds: number[]
  layerInfos: any[]
  curFieldNode?: any
  curOperate?: 'uniqueing' | 'detailing' | ''
}>({
  loading: false,
  tabs: [],
  layerIds: [],
  layerInfos: []
})
const FormConfig = reactive<IFormConfig>({
  group: [
    {
      fieldset: {
        desc: '选择字段'
      },
      fields: [
        {
          type: 'list',
          data: [],
          className: 'sql-list-wrapper',
          setData: async (config: IFormList, row) => {
            if (!row.layerid?.length) return
            const layerid = row.layerid[0]
            const layerName = state.layerInfos.find(
              item => item.layerid === layerid
            )?.layername
            if (!layerName) return
            const fields = await GetFieldConfig(layerName)
            config.data = fields.data?.result?.rows
          },
          setDataBy: 'layerid',
          displayField: 'alias',
          valueField: 'name',
          highlightCurrentRow: true,
          nodeClick: node => {
            state.curFieldNode = node
            appendSQL(node.name)
          }
        }
      ]
    },
    {
      id: 'field-construct',
      fieldset: {
        desc: '属性过滤'
      },
      fields: [
        {
          type: 'btn-group',
          size: 'small',
          style: {
            width: '40%',
            display: 'flex',
            flexWrap: 'wrap'
          },
          className: 'sql-btns-wrapper',
          btns: [
            {
              perm: true,
              text: '=',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('=')
              }
            },
            {
              perm: true,
              text: '模糊',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL("like '%替换此处%'")
              }
            },
            {
              perm: true,
              text: '>',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('>')
              }
            },
            {
              perm: true,
              text: '<',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('<')
              }
            },
            {
              perm: true,
              text: '非',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('<>')
              }
            },
            {
              perm: true,
              text: '并且',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('and')
              }
            },
            {
              perm: true,
              text: '或者',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('or')
              }
            },
            {
              perm: true,
              text: '%',
              styles: {
                margin: '6px',
                width: '50px'
              },
              click: () => {
                appendSQL('%')
              }
            }
          ],
          extraFormItem: [
            {
              type: 'list',
              wrapperStyle: {
                width: '60%',
                height: '144px'
              },
              className: 'sql-list-wrapper',
              field: 'uniqueValue',
              data: [],
              nodeClick: node => {
                appendSQL("'" + node + "'")
              },
              filters: [
                {
                  type: 'btn-group',
                  btns: [
                    {
                      perm: true,
                      text: () => (state.curOperate === 'uniqueing'
                        ? '正在获取唯一值'
                        : '获取唯一值'),
                      loading: () => state.curOperate === 'uniqueing',
                      disabled: () => state.curOperate === 'detailing',
                      styles: {
                        width: '100%',
                        borderRadius: '0'
                      },
                      click: () => getUniqueValue()
                    }
                  ]
                }
              ]
            }
          ]
        }
      ]
    },
    {
      fieldset: {
        desc: '组合查询条件'
      },
      fields: [
        {
          type: 'textarea',
          field: 'sql',
          placeholder: 'OBJECTID > 0'
        },
        {
          type: 'btn-group',
          itemContainerStyle: {
            marginBottom: '8px'
          },
          btns: [
            {
              perm: true,
              text: '清除组合条件',
              type: 'danger',
              disabled: () => state.curOperate === 'detailing',
              click: () => clear(),
              styles: {
                width: '100%'
              }
            }
          ]
        },
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: '查询',
              disabled: () => state.layerIds.length === 0,
              click: () => handleQuery(),
              styles: {
                width: '100%'
              }
            }
          ]
        }
      ]
    }
  ],
  labelPosition: 'top',
  gutter: 12,
  defaultValue: {
    layerid: []
  }
})
const appendSQL = val => {
  if (!refForm.value) return
  if (!refForm.value?.dataForm) refForm.value.dataForm = {}
  const sql = refForm.value.dataForm.sql || ' '
  refForm.value.dataForm.sql = sql + val + ' '
}

const clear = () => {
  refForm.value?.dataForm && (refForm.value.dataForm.sql = '')
}

const getUniqueValue = async () => {
  if (!state.curFieldNode) return
  const layerid = refForm.value?.dataForm.layerid
  if (!layerid?.length) {
    SLMessage.warning('请先选择一个图层')
    return
  }
  state.curOperate = 'uniqueing'
  try {
    const res = await GetFieldUniqueValue({
      layerid: layerid[0],
      field_name: state.curFieldNode.name
    })
    const extraFormItem = FormConfig.group.find(
      item => item.id === 'field-construct'
    )?.fields[0].extraFormItem
    const field = extraFormItem && (extraFormItem[0] as IFormList)
    field && (field.data = res.data.result.rows)
  } catch (error) {
    SLMessage.error('获取唯一值失败')
  }
  state.curOperate = ''
}
const getLayerInfo = async () => {
  state.layerIds = getSubLayerIds(
    staticState.view,
    undefined,
    undefined,
    '排泥阀'
  )
  if (!state.layerIds.length) {
    SLMessage.warning('当前没有排泥阀')
    return
  }
  const layerInfo = await queryLayerClassName(state.layerIds)
  state.layerInfos = layerInfo.data?.result?.rows || []
  refForm.value && (refForm.value.dataForm.layerid = state.layerIds)
}
const handleQuery = async () => {
  const { layerid } = refForm.value?.dataForm || {}
  if (!layerid?.length) {
    SLMessage.warning('排泥阀服务不存在')
    return
  }
  state.tabs = await getLayerOids(layerid || [], state.layerInfos, {
    where: refForm.value?.dataForm.sql || '1=1'
  })
  refMap.value?.refreshDetail(state.tabs)
}
const onMaploaded = async view => {
  staticState.view = view
  await getLayerInfo()
}
</script>
<style lang="scss" scoped></style>
