import{_ as X}from"./FormTableColumnFilter-BT7pLXIC.js";import{d as j,c as N,r as T,a8 as W,b as D,Q as Y,g as L,h as S,F as w,p as g,bh as Z,i as y,q as x,n as ee,aJ as te,aB as ae,an as A,G as re,b7 as ie,S as oe,_ as le,aK as se,aL as ne,J as pe,aq as me,X as de,C as ce}from"./index-r0dFAfgr.js";import{p as ue}from"./AnimatedLinesLayer-B2VbV4jv.js";import{s as q}from"./FeatureHelper-Da16o0mu.js";import"./pe-B8dP0-Ut.js";import"./MapView-DaoQedLH.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./Point-WxyopZva.js";import{f as U,r as ye,g as O,a as fe}from"./LayerHelper-Cn-iiqxI.js";import{g as ge}from"./QueryHelper-ILO3qZqg.js";import{g as he}from"./ToolHelper-BiiInOzB.js";import{GetFieldConfig as R,GetFieldUniqueValue as ve}from"./fieldconfig-Bk3o1wi7.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./geometryEngineBase-BhsKaODW.js";import be from"./RightDrawerMap-D5PhmGFO.js";import _e from"./DetailTable-Dc-xAY7v.js";import{P as we}from"./gisSetting-CQEP-Q3N.js";import{E as xe,c as Le,d as ke}from"./config-DncLSA-r.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./project-DUuzYgGl.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./pipe-nogVzCHG.js";import"./executeForIds-BLdIsxvI.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./ArcView-DpMnCY82.js";import"./ViewHelper-BGCZjxXH.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./ArcStationWarning-BF9YrSzF.js";/* empty css                         */import"./useWaterPoint-Bv0z6ym6.js";import"./DateFormatter-Bm9a68Ax.js";import"./zhandian-YaGuQZe6.js";import"./useStation-DJgnSZIA.js";import"./DrawerBox-CLde5xC8.js";import"./SideDrawer-CBntChyn.js";import"./PipeDetail-CTBPYFJW.js";import"./Search-NSrhrIa_.js";import"./config-fy91bijz.js";import"./ArcBRTools-BO92yznB.js";import"./PipeLength.vue_vue_type_script_setup_true_lang-BZfUsvDf.js";import"./arcWidgetButton-0glIxrt7.js";import"./StatisticsHelper-D-s_6AyQ.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./wfsUtils-DXofo3da.js";import"./usePipelineGroup-Ba1pmWz_.js";import"./GroupLayer-DhhN3FyN.js";import"./lazyLayerLoader-DbM9sT1W.js";import"./WFSLayer-1TtJp3nx.js";import"./clientSideDefaults-VQhQaYxh.js";import"./QueryEngineCapabilities-Dk3NJwmm.js";import"./wfsUtils-Du031XaC.js";import"./geojson-MBFu2-HZ.js";import"./xmlUtils-CtUoQO7q.js";import"./ListWindow-WS05QqV0.js";import"./PopLayout-BP55MvL7.js";import"./PoiSearchV2-D7yNeLuv.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./URLHelper-B9aplt5w.js";import"./useLayerList-DmEwJ-ws.js";import"./useScaleBar-Beed-z91.js";import"./index-DeAQQ1ej.js";const Fe={class:"detail-wrapper"},Ie={class:"left"},Ce={class:"title"},Ne={class:"title-left"},Te={class:"title-btns"},De={class:"table-box"},Se={class:"right"},qe={class:"title"},Ee={class:"table-box"},Be=j({__name:"EditDevType",setup(Ge){const c=N(),E=N(),a=T({tabs:[],curType:"",layerInfos:[],layerIds:[],loading:!1,curLayerName:"",uniqueing:!1,curLayerFields:[],submitting:!1}),r={},_=T({dataList:[],columns:[{width:100,label:"目标字段",prop:"alias"},{label:"选择对应字段",prop:"name",formItemConfig:{type:"select",readonly:e=>e==="OBJECTID",options:W(()=>a.curLayerFields.map(e=>({label:e.alias,value:e.name,id:e.name}))||[])}}],pagination:{hide:!0}}),k=async()=>{var d,l,s,v;const e=(l=(d=c.value)==null?void 0:d.dataForm)==null?void 0:l.layerid[0],t=a.targetLayer;if(e===void 0||t===void 0||e===t){_.dataList=[];return}const o=a.layerInfos.find(u=>u.layerid===a.targetLayer);if(!o)return;const n=o.layername,i=await R(n),p=a.curLayerFields,m={};p.map(u=>{m[u.name]=u.name}),_.dataList=((v=(s=i.data)==null?void 0:s.result)==null?void 0:v.rows.map(u=>({alias:u.alias,targetName:u.name,name:m[u.name]})))||[]},F=T({group:[{fieldset:{desc:"绘制工具"},fields:[{type:"btn-group",btns:[{perm:!0,text:"",type:"default",size:"large",title:"绘制多边形",disabled:()=>a.loading,iconifyIcon:"mdi:shape-polygon-plus",click:()=>I("polygon")},{perm:!0,text:"",type:"default",size:"large",title:"绘制矩形",disabled:()=>a.loading,iconifyIcon:"ep:crop",click:()=>I("rectangle")},{perm:!0,text:"",type:"default",size:"large",title:"绘制椭圆",disabled:()=>a.loading,iconifyIcon:"mdi:ellipse-outline",click:()=>I("circle")},{perm:!0,text:"",type:"default",size:"large",title:"清除图形",disabled:()=>a.loading,iconifyIcon:"ep:delete",click:()=>H()}]}]},{id:"layer",fieldset:{desc:"选择图层"},fields:[{type:"tree",options:[],checkStrictly:!0,showCheckbox:!0,field:"layerid",nodeKey:"value",handleCheckChange:(e,t)=>{t&&(c.value&&(c.value.dataForm.layerid=[e.value]),k())}}]},{id:"layer",fieldset:{desc:"图层字段"},fields:[{type:"list",data:[],className:"sql-list-wrapper",setData:async(e,t)=>{var p,m,d,l,s,v,u;if(!((p=t.layerid)!=null&&p.length)){a.curLayerFields.length=0;return}const o=t.layerid[0],n=(m=a.layerInfos.find(f=>f.layerid===o))==null?void 0:m.layername;if(!n)return;const i=await R(n);e.data=(l=(d=i.data)==null?void 0:d.result)==null?void 0:l.rows,a.curLayerFields=((v=(s=i.data)==null?void 0:s.result)==null?void 0:v.rows)||[],a.curLayerName=(u=a.layerInfos.find(f=>f.layerid===t.layerid[0]))==null?void 0:u.layername},setDataBy:"layerid",displayField:"alias",valueField:"name",highlightCurrentRow:!0,nodeClick:e=>{a.curFieldNode=e,h(e.name)}}]},{id:"field-construct",fieldset:{desc:"构建查询语句"},fields:[{type:"btn-group",size:"small",style:{width:"40%",display:"flex",flexWrap:"wrap"},className:"sql-btns-wrapper",btns:[{perm:!0,text:"=",styles:{margin:"6px",width:"50px"},click:()=>{h("=")}},{perm:!0,text:"模糊",styles:{margin:"6px",width:"50px"},click:()=>{h("like '%替换此处%'")}},{perm:!0,text:">",styles:{margin:"6px",width:"50px"},click:()=>{h(">")}},{perm:!0,text:"<",styles:{margin:"6px",width:"50px"},click:()=>{h("<")}},{perm:!0,text:"非",styles:{margin:"6px",width:"50px"},click:()=>{h("<>")}},{perm:!0,text:"并且",styles:{margin:"6px",width:"50px"},click:()=>{h("and")}},{perm:!0,text:"或者",styles:{margin:"6px",width:"50px"},click:()=>{h("or")}},{perm:!0,text:"%",styles:{margin:"6px",width:"50px"},click:()=>{h("%")}}],extraFormItem:[{type:"list",wrapperStyle:{width:"60%",height:"144px"},className:"sql-list-wrapper",field:"uniqueValue",data:[],nodeClick:e=>{h("'"+e+"'")},filters:[{type:"btn-group",btns:[{perm:!0,text:()=>a.uniqueing?"正在获取唯一值":"获取唯一值",loading:()=>a.uniqueing,disabled:()=>a.loading,styles:{width:"100%",borderRadius:"0"},click:()=>z()}]}]}]}]},{fieldset:{desc:"组合查询条件"},fields:[{type:"textarea",field:"sql",placeholder:"OBJECTID > 0"},{type:"btn-group",itemContainerStyle:{marginBottom:"8px"},btns:[{perm:!0,text:"清除组合条件",type:"danger",disabled:()=>a.loading,click:()=>J(),styles:{width:"100%"}}]}]},{fields:[{type:"btn-group",btns:[{perm:!0,text:"查询",styles:{width:"100%"},click:()=>C()},{perm:!0,text:"重置",type:"default",styles:{width:"100%"},click:()=>P()}]}]}],labelPosition:"top",gutter:12,defaultValue:{length:1}}),h=e=>{var o;if(!c.value)return;(o=c.value)!=null&&o.dataForm||(c.value.dataForm={});const t=c.value.dataForm.sql||" ";c.value.dataForm.sql=t+e+" "},z=async()=>{var t,o;if(!a.curFieldNode)return;const e=(t=c.value)==null?void 0:t.dataForm.layerid;if(!(e!=null&&e.length)){D.warning("请先选择一个图层");return}a.uniqueing=!0;try{const n=await ve({layerid:e[0],field_name:a.curFieldNode.name}),i=(o=F.group.find(m=>m.id==="field-construct"))==null?void 0:o.fields[0].extraFormItem,p=i&&i[0];p&&(p.data=n.data.result.rows)}catch{D.error("获取唯一值失败")}a.uniqueing=!1},J=()=>{var e;(e=c.value)!=null&&e.dataForm&&(c.value.dataForm.sql="")},P=()=>{var e;(e=c.value)==null||e.resetForm()},H=()=>{var e;(e=r.graphicsLayer)==null||e.removeAll()},I=e=>{var t,o,n,i,p,m,d;(t=r.sketchCompHandler)==null||t.remove(),(o=r.sketchUpdateHandler)==null||o.remove(),(n=r.sketch)==null||n.destroy(),(i=r.graphicsLayer)==null||i.removeAll(),r.sketch=new ue({view:r.view,layer:r.graphicsLayer,polygonSymbol:q("polygon",{color:[255,0,0,.3],outlineColor:[255,0,0,1]}),polylineSymbol:q("polyline"),pointSymbol:q("point")}),(p=r.sketch)==null||p.create(e),r.sketchUpdateHandler=(m=r.sketch)==null?void 0:m.on("update",l=>{l.state==="complete"&&(console.log(l.graphics[0]),r.graphic=l.graphics[0])}),r.sketchCompHandler=(d=r.sketch)==null?void 0:d.on("create",l=>{l.state==="complete"&&(console.log(l.graphic),r.graphic=l.graphic)})},M=async()=>{var n,i,p,m,d,l;a.layerIds=fe(r.view);const e=await de(a.layerIds);a.layerInfos=((p=(i=(n=e.data)==null?void 0:n.result)==null?void 0:i.rows)==null?void 0:p.filter(s=>s.geometrytype==="esriGeometryPoint"))||[];const t=(m=F.group.find(s=>s.id==="layer"))==null?void 0:m.fields[0],o=a.layerInfos.map(s=>({label:s.layername,value:s.layerid,data:s}));t&&(t.options=[{label:"管点类",value:-1,disabled:!0,children:o}]),c.value&&(c.value.dataForm.layerid=[(d=o[0])==null?void 0:d.value]),a.targetLayer=(l=o[1])==null?void 0:l.value},b=N(),$=e=>{var t;(t=b.value)==null||t.extentTo(r.view,e.OBJECTID)},C=async()=>{var n,i,p,m,d,l,s;k(),(n=E.value)==null||n.toggleCustomDetail(!0);const e=((i=c.value)==null?void 0:i.dataForm.layerid)||[],t={where:((p=c.value)==null?void 0:p.dataForm.sql)||"1=1",geometry:(m=r.graphic)==null?void 0:m.geometry},o=await ge(e,a.layerInfos,t);(s=b.value)==null||s.refreshDetail(r.view,{layerid:e[0],layername:(d=o[0])==null?void 0:d.name,oids:(l=o[0])==null?void 0:l.data},{queryParams:t,allAttributes:!0})},Ve=()=>{},Q=()=>{var n;const e=a.curLayerName,t=a.targetLayer,o=(n=a.layerInfos.find(i=>i.layerid===t))==null?void 0:n.layername;oe("应用到空间数据？","提示信息").then(async()=>{var l,s,v,u;const i=(l=b.value)==null?void 0:l.staticState.tabFeatures;if(!(i!=null&&i.length))return;const p=_.dataList.filter(f=>!!(f.name??"")),m=i.map(f=>{const B=window.SITE_CONFIG.SITENAME==="qingyang"?he(f):f,G={};return p.map(V=>{G[V.targetName]=f.attributes[V.name]}),B.attributes=G,B}),d=(v=(s=c.value)==null?void 0:s.dataForm.layerid)==null?void 0:v[0];if(!(d===void 0||t===void 0||d===t)){a.submitting=!0;try{await U(t,{addFeatures:m}),await U(d,{deleteFeatures:i}),we({optionName:xe.XIUGAISBSS,type:Le.BASICGIS,content:`将OBJECTID为${(u=i.map(f=>f.attributes.OBJECTID))==null?void 0:u.join("、")}的【${e}】修改为【${o}】`,optionType:ke.UPDATE}).catch(()=>{console.log("生成gis操作日志失败")}),C(),ye(r.view)}catch{D.error("更新数据失败！")}a.submitting=!1}}).catch(()=>{})},K=e=>{r.view=e,r.graphicsLayer=O(r.view,{id:"pipe-brush",title:"属性刷"}),r.textLayer=O(r.view,{id:"pipe-brush-text",title:"属性刷-标注"}),M()};return Y(()=>{var e,t,o,n,i,p,m;(e=r.sketchCompHandler)==null||e.remove(),(t=r.sketchUpdateHandler)==null||t.remove(),(o=r.sketch)==null||o.destroy(),(n=r.graphicsLayer)==null||n.removeAll(),(i=r.moveEvent)==null||i.remove(),(p=r.clickEvent)==null||p.remove(),(m=r.textLayer)==null||m.removeAll()}),(e,t)=>{const o=le,n=se,i=ne,p=pe,m=me,d=X;return L(),S(be,{ref_key:"refMap",ref:E,title:"修改设备类型","detail-max-min":!0,"full-content":!0,onMapLoaded:K,onDetailRefreshed:t[1]||(t[1]=l=>y(a).loading=!1)},{"detail-header":w(()=>[g("span",null,"修改设备类型查询结果"+Z(y(a).curLayerName&&" - "+y(a).curLayerName),1)]),"detail-default":w(()=>{var l;return[g("div",Fe,[g("div",Ie,[g("div",Ce,[g("div",Ne,[t[2]||(t[2]=g("span",null," 目标设备图层 ",-1)),x(i,{modelValue:y(a).targetLayer,"onUpdate:modelValue":t[0]||(t[0]=s=>y(a).targetLayer=s),size:"small",style:{width:"90px","margin-left":"8px"},onChange:k},{default:w(()=>[(L(!0),ee(ae,null,te(y(a).layerInfos,s=>(L(),S(n,{key:s.layerid,value:s.layerid,label:s.layername},null,8,["value","label"]))),128))]),_:1},8,["modelValue"])]),g("div",Te,[A("",!0),x(p,{type:"success",size:"small",icon:y(ie),disabled:!y(_).dataList.length,loading:y(a).submitting,onClick:Q},{default:w(()=>t[4]||(t[4]=[re(" 刷新 ")])),_:1},8,["icon","disabled","loading"])])]),g("div",De,[x(m,{config:y(_)},null,8,["config"])])]),g("div",Se,[g("div",qe,[t[5]||(t[5]=g("span",null,"待刷新要素",-1)),(l=y(b))!=null&&l.TableConfig_Detail.columns?(L(),S(d,{key:0,"show-tooltip":!0,columns:y(b).TableConfig_Detail.columns},null,8,["columns"])):A("",!0)]),g("div",Ee,[x(_e,{ref_key:"refDetailTable",ref:b,onRowClick:$,onRefreshData:C},null,512)])])])]}),default:w(()=>[x(o,{ref_key:"refForm",ref:c,config:y(F)},null,8,["config"])]),_:1},512)}}}),Ha=ce(Be,[["__scopeId","data-v-aac91b11"]]);export{Ha as default};
