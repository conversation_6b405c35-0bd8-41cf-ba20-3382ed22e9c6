import{d as S,c as g,r as p,az as _,b as a,aA as h,o as P,g as F,n as M,q as c,i as d,aB as k,aC as q,_ as x}from"./index-r0dFAfgr.js";const n="mail",B=S({__name:"EmailSetting",setup(V){const u=g(),l=g();let i="";const m=p({group:[{fields:[{type:"input",field:"mailFrom",label:"邮件来自",placeholder:"如：smtp.163.com",rules:[{required:!0,message:"请输入用户邮箱",trigger:"blur"},{type:"email",message:"请输入正确邮箱地址",trigger:"blur"}]},{type:"select",label:"SMTP协议",field:"smtpProtocol",options:[{label:"smtp",value:"smtp"},{label:"smtps",value:"smtps"}],rules:[{required:!0,message:"请选择SMTP协议",trigger:"blur"}]},{type:"input",field:"smtpHost",label:"SMTP网关",rules:[{required:!0,message:"请输入SMTP网关",trigger:"blur"}]},{type:"input-number",label:"SMTP端口",placeholder:"一般为25或465",field:"smtpPort",rules:[{required:!0,message:"请输入SMTP端口",trigger:"blur"}]},{type:"input-number",label:"超时毫秒数",field:"timeout",rules:[{required:!0,message:"请输入超时毫秒数",trigger:"blur"}]},{type:"switch",label:"启用TLS",field:"enableTls"},{type:"input",label:"用户账号",field:"username",rules:[{required:!0,message:"请输入用户账号",trigger:"blur"},{type:"email",message:"账号为邮箱格式",trigger:"blur"}]},{type:"password",label:"密码",field:"password",rules:[{required:!0,message:"请输入密码",trigger:"blur"},{max:20,min:8,message:"密码在8-20位",trigger:"blur"}]}]}],labelPosition:"right",labelWidth:"100px",defaultValue:{mailFrom:"",smtpProtocol:"",smtpHost:"",smtpPort:"",timeout:"",enableTls:!0,username:"",password:""},submit:e=>{y(e)}}),f=p({group:[{fields:[{type:"input",label:"测试邮件发送至",field:"mailTo",rules:[{required:!0,message:"请输入接收测试邮件的邮箱"},{type:"email",message:"请输入正确邮箱地址"}]},{type:"btn-group",btns:[{perm:!0,type:"warning",isTextBtn:!1,text:"发送测试邮件",iconifyIcon:"ep:promotion",click:()=>{var e;(e=u.value)==null||e.Submit()}},{perm:!0,isTextBtn:!1,text:"保存",click:()=>{var e;(e=l.value)==null||e.Submit()}}]}]}],labelPosition:"right",labelWidth:"100px",defaultValue:{},submit:e=>{b(e)}}),b=e=>{var o;const s=((o=l.value)==null?void 0:o.dataForm)||{},t={id:{id:i},key:n,jsonValue:{...e,...s}};_(t).then(r=>{console.log(r),a.success("发送成功")}).catch(r=>{console.log(r),a.error("发送失败，请检查输入的账号和密码或接收测试邮件的邮箱是否存在！")})},y=e=>{const s={id:{id:i},key:n,jsonValue:{...e}};h(s).then(t=>{console.log(t),a.success("保存成功")}).catch(t=>{a.error("保存失败，用户账号或密码不匹配"),console.log(t)})},T=()=>{q(n).then(e=>{var t;i=e.data.id.id;let s=e.data.jsonValue;s=JSON.parse(JSON.stringify(s),(o,r)=>r==="true"?!0:r==="false"?!1:r),m.defaultValue={...s},(t=l.value)==null||t.resetForm()})};return P(()=>{T()}),(e,s)=>{const t=x;return F(),M(k,null,[c(t,{ref_key:"refForm",ref:l,config:d(m)},null,8,["config"]),c(t,{ref_key:"refTestForm",ref:u,config:d(f)},null,8,["config"])],64)}}});export{B as _};
