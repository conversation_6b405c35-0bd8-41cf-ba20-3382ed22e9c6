package org.thingsboard.server.dao.model.sql.maintainCircuit.circuit;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 保养计划子表
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-09-08
 */
@TableName("tb_device_circuit_task_point")
@Data
public class CircuitTaskPoint {
    @TableId
    private String id;

    private String mainId;

    private String pointId;

    private transient CircuitPointM circuitPointM;

    private Date createTime;

    private String tenantId;

}
