package org.thingsboard.server.dao.client;

import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.dao.model.sql.ProjectEntity;

import java.util.List;

@FeignClient(name = "base-service")
public interface ProjectFeignClient {

    /**
     * 查询指定资源类型以及资源ID的项目
     *
     * @param entityType
     * @param entityId
     * @return
     * @throws ThingsboardException
     */
    @GetMapping("api/project/relation/project/{entityType}/{entityId}")
    List<ProjectEntity> findProjectRelationByEntityTypeAndEntityId(
            @PathVariable String entityType, @PathVariable String entityId) throws ThingsboardException;

}
