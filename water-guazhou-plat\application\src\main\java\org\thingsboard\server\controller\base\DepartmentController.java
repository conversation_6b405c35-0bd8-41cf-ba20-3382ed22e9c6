package org.thingsboard.server.controller.base;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardErrorCode;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.dao.model.sql.department.Department;
import org.thingsboard.server.dao.util.imodel.Environment;
import org.thingsboard.server.dao.util.imodel.query.department.DepartmentPageRequest;
import org.thingsboard.server.dao.util.imodel.query.department.DepartmentSaveRequest;
import org.thingsboard.server.dao.organization.DepartmentService;
import org.thingsboard.server.utils.imodel.annotations.IStarController;

import java.util.List;

@IStarController
@RequestMapping("/api/department")
public class DepartmentController extends BaseController {
    @Autowired
    private DepartmentService departmentService;


    @GetMapping
    public List<Department> findAllConditional(DepartmentPageRequest request) {
        return departmentService.findAllConditional(request);
    }

    @GetMapping("/tree/{pid}")
    public List<Department> findTree(@PathVariable String pid) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return departmentService.findAllStructure(pid,1, tenantId);
    }

    @GetMapping("/tree/{pid}/{depth}")
    public List<Department> findTree(@PathVariable String pid, @PathVariable Integer depth) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return departmentService.findAllStructure(pid, depth, tenantId);
    }

    @PostMapping
    public Department save(@RequestBody DepartmentSaveRequest request) throws ThingsboardException {
        if (!departmentService.canBeAdd(request.getParentId()))
            throw new ThingsboardException("parentId为单位时需要存在且为叶节点", ThingsboardErrorCode.GENERAL);

        if (request.getId() != null) {
            invalidDepartmentCache(request.getId());
        }

        return departmentService.save(request);
    }

    @PatchMapping("/{id}")
    public boolean edit(@RequestBody DepartmentSaveRequest request, @PathVariable String id) {
        invalidDepartmentCache(id);
        return departmentService.update(request.unwrap(id));
    }

    @DeleteMapping("/{id}")
    public boolean delete(@PathVariable String id) throws ThingsboardException {
        if (!departmentService.canBeDelete(id)) {
            throw new ThingsboardException("部门不存在或其下有用户关联，无法删除", ThingsboardErrorCode.GENERAL);
        }
        invalidDepartmentCache(id);
        return departmentService.delete(id);
    }

    private void invalidDepartmentCache(String id) {
        Environment.getEnvironment().invalidDepartmentCache(id);
    }


}
