<!-- 消息中心 -->
<template>
  <div class="wrapper">
    <CardSearch ref="refSearch" :config="SearchConfig"></CardSearch>
    <CardTable class="card-table" :config="TableConfig"></CardTable>
  </div>
</template>
<script lang="ts" setup>
import { useRouter, useRoute } from 'vue-router';
import { getSystemNotify } from '@/api/admin';
import { getSystemNotifyTypeList } from '@/api/system/default';
import { formatDate } from '@/utils/DateFormatter';
import { removeSlash } from '@/utils/removeIdSlash';
import { useUserStore } from '@/store';

const router = useRouter();
const route = useRoute();

const refSearch = ref<ICardSearchIns>();

const type = [
  { label: '未读', value: '0', color: '#409EFF' },
  { label: '已读', value: '1', color: '#67C23A' }
];

const state = reactive<{
  type: string;
}>({
  type: '0'
});

const SearchConfig = reactive<ISearch>({
  defaultParams: {
    type: route.params.id || '0'
  },
  filters: [
    {
      type: 'select',
      field: 'type',
      label: '消息类型',
      options: [
        { label: '待办', value: '0' },
        { label: '报警', value: '1' },
        { label: '通知', value: '2' }
      ],
      onChange: (val) => {
        state.type = val;
      }
    },
    {
      type: 'date',
      label: '起始时间',
      field: 'beginTime'
    },
    {
      type: 'date',
      label: '结束时间',
      field: 'endTime'
    },
    {
      type: 'select',
      field: 'status',
      label: '消息状态',
      options: [
        { label: '未读', value: '0' },
        { label: '已读', value: '1' }
      ]
    },
    {
      type: 'input',
      field: 'fromName',
      label: '发送人名称'
    },
    {
      type: 'input',
      field: 'topic',
      label: '主题'
    }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          iconifyIcon: 'ep:search',
          click: () => refreshData()
        },
        {
          perm: true,
          text: '重置',
          iconifyIcon: 'material-symbols:refresh',
          click: () => {
            refSearch.value?.resetForm();
            refreshData();
          }
        }
      ]
    }
  ]
});
const EMessageRouteType = {
  0: '2',
  1: '1',
  2: '0'
};
const TableConfig = reactive<ITable>({
  dataList: [],
  operationWidth: 220,
  columns: [
    { label: '消息编号', prop: 'code' },
    { label: '消息主题', prop: 'topic' },
    { label: '消息内容', prop: 'content' },
    { label: '消息发送人', prop: 'fromUser' },
    // { label: '消息接收人', prop: 'toUser' },
    { label: '消息发送时间', prop: 'time', formatter: (row) => formatDate(row.time, 'YYYY-MM-DD HH:mm:ss') },
    {
      label: '消息状态',
      prop: 'status',
      tag: true,
      tagColor: (row): string => type.find((item) => item.value === row.status)?.color || '',
      formatter: (row) => type.find((item) => item.value === row.status)?.label
    }
  ],
  pagination: {
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page;
      TableConfig.pagination.limit = size;
      refreshData();
    }
  },
  operations: [
    {
      perm: true,
      text: '详情',
      type: 'success',
      click: (row) => {
        switch (state.type) {
          case '0':
            router.push({ name: 'defaultRoute_GDFP' });
            break;
          case '1':
            router.push({ name: 'defaultRoute_BJZX' });
            break;
          case '2':
            router.push({ name: 'defaultRoute_YWCL' });
            break;
          default:
            break;
        }
      }
    }
  ]
});

const SystemNotifyTypeList = ref<any[]>([]);

const modules = import.meta.glob('../../../views/**/*.vue');
const initSystemNotifyTypeList = () => {
  getSystemNotifyTypeList({ page: '1', size: '9999' } as any).then((res) => {
    SystemNotifyTypeList.value =
      res.data.data.data?.map((item) => {
        const key = {
          path: item.id,
          name: item.id,
          component: modules[/* @vite-ignore */ `../../${item.route}.vue`],
          meta: {
            title: item.menuName,
            icon: 'form',
            roles: ['CUSTOMER_USER', 'TENANT_ADMIN', 'TENANT_SUPPORT']
          }
        };
        // 添加动态路由
        router.addRoute('defaultRoute', key);
        return item;
      }) || [];
  });
};

const refreshData = () => {
  const params: any = {
    size: TableConfig.pagination.limit || 20,
    page: TableConfig.pagination.page || 1,
    to: removeSlash(useUserStore().id),
    ...(refSearch.value?.queryParams || {})
  };
  state.type = params.type;
  getSystemNotify(params).then((res) => {
    TableConfig.dataList = res.data.data.data || [];
    TableConfig.pagination.total = res.data.data.total || 0;
  });
};

// 监听参数变化
watch(route, () => {
  SearchConfig.defaultParams = {
    ...(refSearch.value?.queryParams || {}),
    type: route.params.id || '0'
  };
  state.type = (route.params.id || '0') as string;
  refSearch.value?.resetForm();
  refreshData();
});

onMounted(() => {
  refreshData();
  // initSystemNotifyTypeList()
});

onActivated(() => {
  refreshData();
  // initSystemNotifyTypeList()
});
</script>

<style lang="scss" scoped></style>
