import{_ as F}from"./index-C9hz-UZb.js";import{d as L,M as O,a6 as E,c as y,r as h,bF as u,a8 as f,s as k,bu as j,ah as z,ay as A,g as x,n as w,q as n,i as t,F as d,cs as M,h as R,an as T,j as G,bB as J,c5 as U,dF as W,dA as $,aq as X,al as H,aj as K,C as Q}from"./index-r0dFAfgr.js";import{_ as Z}from"./CardSearch-CB_HNR-Q.js";import{l as ee}from"./echart-DxEZmJvB.js";import{u as ae}from"./useStation-DJgnSZIA.js";import{d as te}from"./flowMonitoring-DtJlPj0G.js";import"./Search-NSrhrIa_.js";import"./zhandian-YaGuQZe6.js";const re={class:"wrapper"},oe=L({__name:"index",setup(ne){const{$messageWarning:_}=O(),q=E(),v=y(),C=y(),p=y(),g=y(),{getStationTree:S,getStationTreeByDisabledType:I}=ae(),e=h({activeName:"day",activePattern:"echarts",stationId:"",chartOption:null,data:[],stationTree:[],chartName:""}),V=h({type:"tabs",tabType:"simple",width:"100%",field:"type",tabs:[{label:"日流量",value:"day"},{label:"月流量",value:"month"},{label:"年流量",value:"year"}],handleTabClick:r=>{var o;e.activeName=r.props.name,(((o=p.value)==null?void 0:o.queryParams)||{}).stationId?N():_("请选择监测点")}}),b=h({defaultParams:{day:u().format("YYYY-MM-DD"),month:u().format("YYYY-MM"),year:u().format("YYYY"),daterange:[u().format("YYYY-MM-DD"),u().format("YYYY-MM-DD")]},filters:[{type:"select-tree",label:"监测点:",defaultExpandAll:!0,field:"stationId",clearable:!1,width:"200px",options:f(()=>e.stationTree),nodeClick:r=>{e.chartName=r.label}},{type:"select",label:"统计类型",field:"va1",width:"140px",options:[{label:"净累计",value:"净累计"},{label:"正向累计",value:"正向累计"},{label:"反向累计",value:"反向累计"}]},{type:"date",label:"日期",field:"day",format:"YYYY-MM-DD",clearable:!1,hidden:f(()=>e.activeName==="year"||e.activeName==="month"||e.activeName==="daterange"),width:300},{type:"month",label:"日期",field:"month",format:"YYYY-MM",clearable:!1,hidden:f(()=>e.activeName==="year"||e.activeName==="day"||e.activeName==="daterange"),width:300},{type:"year",label:"日期",field:"year",format:"YYYY",clearable:!1,hidden:f(()=>e.activeName==="day"||e.activeName==="month"||e.activeName==="daterange"),width:300},{type:"daterange",label:"日期",format:"YYYY-MM",field:"daterange",clearable:!1,hidden:f(()=>e.activeName==="year"||e.activeName==="month"||e.activeName==="day"),width:300}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",svgIcon:k(H),click:()=>{var a;(((a=p.value)==null?void 0:a.queryParams)||{}).stationId?N():_("请选择监测点")}},{perm:!0,text:"导出",type:"warning",svgIcon:k(K),hide:()=>e.activePattern!=="list",click:()=>{var a,o;(((a=p.value)==null?void 0:a.queryParams)||{}).stationId?(o=C.value)==null||o.exportTable():_("请选择监测点")}}]}]}),Y=h({loading:!1,dataList:[],columns:[],operations:[],showSummary:!1,operationWidth:"150px",pagination:{hide:!0}}),B=()=>{var s,c,m;const r=(s=e.data)==null?void 0:s.tableDataList.map(i=>i.ts),a=ee(r);a.series=[];const o={name:e.chartName,smooth:!0,data:[],type:"line",markPoint:{data:[{type:"max",name:"最大值"},{type:"min",name:"最小值"}]},markLine:{data:[{type:"average",name:"平均值"}]}};a.yAxis.name="流量(m³)";const l=JSON.parse(JSON.stringify(o));l.data=(c=e.data)==null?void 0:c.tableDataList.map(i=>i.value),a.series.push(l),(m=v.value)==null||m.clear(),J(()=>{g.value&&q.listenTo(g.value,()=>{var i;e.chartOption=a,(i=v.value)==null||i.resize()})})},N=async()=>{var c,m;const r=((c=p.value)==null?void 0:c.queryParams)||{},a=r[e.activeName],o={stationId:r.stationId,queryType:e.activeName,date:a},s=(m=(await te(o)).data)==null?void 0:m.data;console.log(s),Y.columns=[{prop:"ts",label:"时间",align:"center"},{prop:"value",label:"流量",unit:"(m³)",align:"center"}],Y.dataList=s,e.data.tableDataList=s,B()};return j(async()=>{var l;const r=["流量监测站,测流压站"].join(","),a=await S(r);e.stationTree=a,await I(a,["Project"],!1,"Station");const o=z(a);b.defaultParams={...b.defaultParams,stationId:o.id},(l=p.value)==null||l.resetForm(),await N()}),(r,a)=>{const o=Z,l=U,s=W,c=$,m=X,i=A("VChart"),D=F;return x(),w("div",re,[n(o,{ref_key:"refSearch",ref:p,config:t(b)},null,8,["config"]),n(D,{class:"card-table",title:" "},{title:d(()=>[n(l,{modelValue:t(e).activeName,"onUpdate:modelValue":a[0]||(a[0]=P=>t(e).activeName=P),config:t(V)},null,8,["modelValue","config"])]),default:d(()=>[n(D,{class:"card",title:t(e).activePattern==="list"?"流量数据列表":"流量曲线"},{right:d(()=>[n(c,{modelValue:t(e).activePattern,"onUpdate:modelValue":a[1]||(a[1]=P=>t(e).activePattern=P)},{default:d(()=>[n(s,{label:"echarts"},{default:d(()=>[n(t(M),{style:{"margin-right":"1px","font-size":"16px"},icon:"clarity:line-chart-line"})]),_:1}),n(s,{label:"list"},{default:d(()=>[n(t(M),{style:{"margin-right":"1px","font-size":"16px"},icon:"material-symbols:table"})]),_:1})]),_:1},8,["modelValue"])]),default:d(()=>[t(e).activePattern==="list"?(x(),R(m,{key:0,ref_key:"refCard",ref:C,config:t(Y)},null,8,["config"])):T("",!0),t(e).activePattern==="echarts"?(x(),w("div",{key:1,ref_key:"agriEcoDev",ref:g,class:"card-ehcarts"},[n(i,{ref_key:"refChart",ref:v,class:"card-ehcarts",theme:t(G)().isDark?"dark":"light",option:t(e).chartOption},null,8,["theme","option"])],512)):T("",!0)]),_:1},8,["title"])]),_:1})])}}}),fe=Q(oe,[["__scopeId","data-v-11f8e42c"]]);export{fe as default};
