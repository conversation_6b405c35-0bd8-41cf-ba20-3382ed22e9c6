import{z as a}from"./index-r0dFAfgr.js";const e=t=>a({url:"/api/sm/circuitPlan",method:"delete",data:t}),s=t=>a({url:"/api/sm/circuitPlan",method:"get",params:t}),o=()=>a({url:"/api/sm/circuitPlan/circleList",method:"get"}),i=t=>a({url:"/api/sm/circuitPlan",method:"post",data:t}),c=t=>a({url:"/api/sm/circuitPlan/plan",method:"post",data:t}),l=t=>a({url:"/api/sm/circuitTaskReport",method:"get",params:t}),u=t=>a({url:"/api/sm/circuitTask/assign",method:"post",data:t}),n=t=>a({url:"/api/sm/circuitTask/workOrder",method:"post",data:t}),m=t=>a({url:"/api/sm/circuitTask",method:"delete",data:t}),d=t=>a({url:"/api/sm/circuitTask",method:"get",params:t}),p=()=>a({url:"/api/sm/circuitTask/completeCount",method:"get"}),P=t=>a({url:"/api/sm/circuitTask/audit",method:"post",data:t}),k=()=>a({url:"/api/sm/circuitTask/completeTotal",method:"get"}),h=()=>a({url:"/api/sm/circuitTask/arrivalRate",method:"get"}),T=()=>a({url:"/api/sm/circuitTask/feedbackRate",method:"get"});export{i as A,u as D,o as G,n as P,P as V,d as a,m as b,l as c,k as d,h as e,T as f,e as g,s as h,c as i,p as j};
