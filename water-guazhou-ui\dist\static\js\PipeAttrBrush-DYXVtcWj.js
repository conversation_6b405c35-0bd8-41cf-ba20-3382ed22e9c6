import{_ as O}from"./FormTableColumnFilter-BT7pLXIC.js";import{d as G,r as F,c as E,g as S,h as L,F as D,p as s,bh as R,i as a,q as b,G as x,b7 as P,an as M,b as y,S as H,_ as $,J as q,aq as J,C as U}from"./index-r0dFAfgr.js";import V from"./RightDrawerMap-D5PhmGFO.js";import Y from"./DetailTable-Dc-xAY7v.js";import{u as j}from"./pipeAttrBrush-D2hcdvhL.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./geometryEngineBase-BhsKaODW.js";import{g as X}from"./ToolHelper-BiiInOzB.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import{f as z}from"./LayerHelper-Cn-iiqxI.js";import"./project-DUuzYgGl.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import{P as Z}from"./gisSetting-CQEP-Q3N.js";import{E as K,c as Q,d as W}from"./config-DncLSA-r.js";import{s as tt}from"./config-fy91bijz.js";import"./ArcView-DpMnCY82.js";import"./ViewHelper-BGCZjxXH.js";import"./fieldconfig-Bk3o1wi7.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./ArcStationWarning-BF9YrSzF.js";/* empty css                         */import"./useWaterPoint-Bv0z6ym6.js";import"./DateFormatter-Bm9a68Ax.js";import"./zhandian-YaGuQZe6.js";import"./useStation-DJgnSZIA.js";import"./DrawerBox-CLde5xC8.js";import"./SideDrawer-CBntChyn.js";import"./PipeDetail-CTBPYFJW.js";import"./Search-NSrhrIa_.js";import"./QueryHelper-ILO3qZqg.js";import"./pipe-nogVzCHG.js";import"./ArcBRTools-BO92yznB.js";import"./PipeLength.vue_vue_type_script_setup_true_lang-BZfUsvDf.js";import"./arcWidgetButton-0glIxrt7.js";import"./StatisticsHelper-D-s_6AyQ.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./wfsUtils-DXofo3da.js";import"./usePipelineGroup-Ba1pmWz_.js";import"./GroupLayer-DhhN3FyN.js";import"./lazyLayerLoader-DbM9sT1W.js";import"./WFSLayer-1TtJp3nx.js";import"./clientSideDefaults-VQhQaYxh.js";import"./QueryEngineCapabilities-Dk3NJwmm.js";import"./wfsUtils-Du031XaC.js";import"./geojson-MBFu2-HZ.js";import"./xmlUtils-CtUoQO7q.js";import"./ListWindow-WS05QqV0.js";import"./PopLayout-BP55MvL7.js";import"./PoiSearchV2-D7yNeLuv.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./URLHelper-B9aplt5w.js";import"./useLayerList-DmEwJ-ws.js";import"./useScaleBar-Beed-z91.js";import"./IdentifyHelper-RJWmLn49.js";import"./identify-4SBo5EZk.js";import"./index-DeAQQ1ej.js";const ot={class:"detail-wrapper"},it={class:"left"},et={class:"title"},rt={class:"title-btns"},at={class:"table-box"},st={class:"right"},mt={class:"title"},pt={class:"table-box"},nt=G({__name:"PipeAttrBrush",setup(lt){const h=F({submitting:!1}),_=E(),e=j(_,()=>w(),()=>C()),w=()=>{var t;if(!e.pick.getIdentifyResult()){y.warning("请先选择参考设备");return}(t=I.value)==null||t.toggleCustomDetail(!0),setTimeout(()=>{g()},0),C()},C=()=>{const o=e.pick.getIdentifyResult();if(!o)p.dataList=[];else{const t=["OBJECTID","SID","START_SID","END_SID","START_DEPTH","END_DEPTH","X","Y","Z","AUDITEDBY ","GATHERER","CREATEDBY","CREATEDDATE","UPDATEDDATE","COMPLETIONDATE","DESIGNNO","ARCHIVENO","ATTACH_SID","OperatingMax","FACILITYID",...tt];p.dataList=e.state.curLayerFields.filter(r=>t.indexOf(r.name)===-1).map(r=>(r.value=o==null?void 0:o.feature.attributes[r.alias],r))}g()},g=()=>{var u,d,f,m,n,l,i;const o=e.state.tabs,t={layerid:(d=(u=_.value)==null?void 0:u.dataForm.layerid)==null?void 0:d[0],layername:(f=o[0])==null?void 0:f.name,oids:(m=o[0])==null?void 0:m.data},r={geometry:(n=e.draw.getGraphic())==null?void 0:n.geometry,where:((l=_.value)==null?void 0:l.dataForm.sql)||"1=1"};(i=c.value)==null||i.refreshDetail(T.view,t,{queryParams:r})},I=E(),T={},p=F({dataList:[],handleSelectChange:o=>{p.selectList=o},columns:[{label:"字段名",prop:"alias"},{label:"字段值",prop:"value"}],pagination:{hide:!0}}),c=E(),N=o=>{var t;(t=c.value)==null||t.extentTo(T.view,o.OBJECTID)},k=()=>{if(!e.pick.getIdentifyResult()){y.warning("请重新查询待刷新要素");return}H("应用到空间数据？","提示信息").then(()=>{var m,n,l;const o=p.selectList||[],t=(m=c.value)==null?void 0:m.staticState.tabFeatures,r=[],u=(t==null?void 0:t.map(i=>i.attributes.OBJECTID))||[],d=e.curLayerName.value;if(!(t!=null&&t.length)){y.error("当前没有待刷新要素，操作已终止");return}o.map(i=>{r.push(`将字段【${i.alias}】统一刷新至【${i.value}】`)});const f=t==null?void 0:t.map(i=>{const A=window.SITE_CONFIG.SITENAME==="qingyang"?X(i):i;return o.map(v=>{A.attributes[v.name]=v.value==="Null"?null:v.value}),A});h.submitting=!0,z((l=(n=_.value)==null?void 0:n.dataForm.layerid)==null?void 0:l[0],{updateFeatures:f}).then(()=>{Z({optionName:K.SHUXINGSHUA,type:Q.BASICGIS,content:`对OBJECTID为${u.join("、")}的${d}应用属性刷，${r.join("，")}`,optionType:W.UPDATE}).catch(()=>{console.log("生成gis操作日志失败")}),g()}).finally(()=>{h.submitting=!1})}).catch(()=>{})},B=o=>{T.view=o,e.init(T.view)};return(o,t)=>{const r=$,u=q,d=J,f=O;return S(),L(V,{ref_key:"refMap",ref:I,title:"属性刷","detail-max-min":!0,"full-content":!0,onMapLoaded:B,onDetailRefreshed:t[0]||(t[0]=m=>a(e).loading.value=!1)},{"detail-header":D(()=>[s("span",null,"属性刷查询结果"+R(a(e).curLayerName.value&&" - "+a(e).curLayerName.value),1)]),"detail-default":D(()=>{var m,n,l,i;return[s("div",ot,[s("div",it,[s("div",et,[t[2]||(t[2]=s("span",null," 选择刷新字段 ",-1)),s("div",rt,[b(u,{type:"success",size:"small",icon:a(P),loading:a(h).submitting,disabled:!((m=a(p).selectList)!=null&&m.length),onClick:k},{default:D(()=>t[1]||(t[1]=[x(" 确定 ")])),_:1},8,["icon","loading","disabled"])])]),s("div",at,[b(d,{config:a(p)},null,8,["config"])])]),s("div",st,[s("div",mt,[s("span",null," 待刷新要素 "+R((n=a(p).currentRow)!=null&&n.id?" - 序号"+((l=a(p).currentRow)==null?void 0:l.id):""),1),(i=a(c))!=null&&i.TableConfig_Detail.columns?(S(),L(f,{key:0,columns:a(c).TableConfig_Detail.columns,"show-tooltip":!0},null,8,["columns"])):M("",!0)]),s("div",pt,[b(Y,{ref_key:"refDetailTable",ref:c,onRowClick:N,onRefreshData:g},null,512)])])])]}),default:D(()=>[b(r,{ref_key:"refForm",ref:_,config:a(e).FormConfig.value},null,8,["config"])]),_:1},512)}}}),Ei=U(nt,[["__scopeId","data-v-b9117ed5"]]);export{Ei as default};
