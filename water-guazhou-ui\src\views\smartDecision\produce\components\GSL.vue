<template>
  <div class="gsl-wrapper">
    <div class="supply-items left">
      <SupplyItem
        v-for="(item, i) in state.leftItems"
        :key="i"
        class="supply-item"
        :config="item"
      ></SupplyItem>
    </div>
    <div class="supply-items right">
      <SupplyItem
        v-for="(item, i) in state.rightItems"
        :key="i"
        class="supply-item"
        :config="item"
      ></SupplyItem>
    </div>
    <div class="center_item total">
      总供水量
    </div>
    <div class="center_item plant">
      水厂供水量
    </div>
    <div class="chart">
      <VChart
        ref="refChart"
        :option="两层圆环(gszbData)"
      ></VChart>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { 两层圆环 } from '../charts'
import { gszbData } from '../../smartDecisionData'
import SupplyItem from './SupplyItem.vue'

const state = reactive<{
  leftItems: ITargetItem[]
  rightItems: ITargetItem[]
}>({
  leftItems: [
    {
      label: '今日供水量',
      unit: '万m³',
      status: 'down',
      value: '1.44',
      scale: '11.17%'
    },
    {
      label: '昨日供水量',
      unit: '万m³',
      value: '2.64'
    },
    {
      label: '本月供水量',
      unit: '万m³',
      value: '813.92',
      status: 'up',
      scale: '20.9%'
    }
  ],
  rightItems: [
    {
      label: '礼辛水厂',
      unit: '万m³',
      status: 'up',
      value: '9.95',
      scale: '1.9%'
    },
    {
      label: '二水厂',
      unit: '万m³',
      status: 'up',
      value: '2.64',
      scale: '4.1%'
    },
    {
      label: '三水厂',
      unit: '万m³',
      value: '813.92',
      status: 'down',
      scale: '20.9%'
    }
  ]
})
</script>
<style lang="scss" scoped>
.gsl-wrapper {
  background-image: url('../imgs/smartproduce_center_item.png');
  background-size: 100% 100%;
  background-position: top;
  background-repeat: no-repeat;
  width: 100%;
  height: 260px;
  padding: 20px;
  position: relative;
  .supply-items {
    height: calc(100% - 40px);
    padding: 0;
    position: absolute;
    &.left {
      left: 20px;
    }
    &.right {
      right: 20px;
    }
    .supply-item {
      margin-bottom: 12px;
      &:last-child{
        margin-bottom: 0;
      }
    }
  }
  .center_item {
    position: absolute;
    width: 100px;
    height: 40px;
    background-color: #2e2f78;
    line-height: 40px;
    border-radius: 5px;
    text-align: center;
    &.total {
      top: 20px;
      left: 240px;
    }
    &.plant {
      top: 20px;
      right: 240px;
    }
  }
  .chart {
    position: absolute;
    left: 280px;
    top: 80px;
    width: 360px;
    height: 180px;
  }
}
</style>
