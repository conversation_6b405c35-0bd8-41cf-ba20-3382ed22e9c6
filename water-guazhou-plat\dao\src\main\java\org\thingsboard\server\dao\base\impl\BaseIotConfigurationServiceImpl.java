package org.thingsboard.server.dao.base.impl;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.base.IBaseIotConfigurationService;
import org.thingsboard.server.dao.model.sql.base.BaseIotConfiguration;
import org.thingsboard.server.dao.sql.base.BaseIotConfigurationMapper;
import org.thingsboard.server.dao.util.imodel.query.base.BaseIotConfigurationPageRequest;

/**
 * 平台管理-物联配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@Service
public class BaseIotConfigurationServiceImpl implements IBaseIotConfigurationService {

    @Autowired
    private BaseIotConfigurationMapper baseIotConfigurationMapper;

    /**
     * 查询平台管理-物联配置
     *
     * @param id 平台管理-物联配置主键
     * @return 平台管理-物联配置
     */
    @Override
    public BaseIotConfiguration selectBaseIotConfigurationById(String id) {
        return baseIotConfigurationMapper.selectBaseIotConfigurationById(id);
    }

    /**
     * 查询平台管理-物联配置列表
     *
     * @param baseIotConfiguration 平台管理-物联配置
     * @return 平台管理-物联配置
     */
    @Override
    public IPage<BaseIotConfiguration> selectBaseIotConfigurationList(BaseIotConfigurationPageRequest baseIotConfiguration) {
        return baseIotConfigurationMapper.selectBaseIotConfigurationList(baseIotConfiguration);
    }

    /**
     * 新增平台管理-物联配置
     *
     * @param baseIotConfiguration 平台管理-物联配置
     * @return 结果
     */
    @Override
    public int insertBaseIotConfiguration(BaseIotConfiguration baseIotConfiguration) {
        return baseIotConfigurationMapper.insertBaseIotConfiguration(baseIotConfiguration);
    }

    /**
     * 修改平台管理-物联配置
     *
     * @param baseIotConfiguration 平台管理-物联配置
     * @return 结果
     */
    @Override
    public int updateBaseIotConfiguration(BaseIotConfiguration baseIotConfiguration) {
        return baseIotConfigurationMapper.updateBaseIotConfiguration(baseIotConfiguration);
    }

    /**
     * 批量删除平台管理-物联配置
     *
     * @param ids 需要删除的平台管理-物联配置主键
     * @return 结果
     */
    @Override
    public int deleteBaseIotConfigurationByIds(List<String> ids) {
        return baseIotConfigurationMapper.deleteBaseIotConfigurationByIds(ids);
    }

    /**
     * 删除平台管理-物联配置信息
     *
     * @param id 平台管理-物联配置主键
     * @return 结果
     */
    @Override
    public int deleteBaseIotConfigurationById(String id) {
        return baseIotConfigurationMapper.deleteBaseIotConfigurationById(id);
    }
}
