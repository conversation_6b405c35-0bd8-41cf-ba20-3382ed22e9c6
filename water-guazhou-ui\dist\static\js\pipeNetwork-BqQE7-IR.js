import{d as b,c as A,r as C,o as N,Q as S,g as u,n as h,q as e,p as n,i as c,aB as H,aJ as k,h as E,F as a,X as J,C as x}from"./index-r0dFAfgr.js";import{M as B}from"./Map-BtiwaWSD.js";import{u as U}from"./useStation-DJgnSZIA.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import{a as F}from"./LayerHelper-Cn-iiqxI.js";import"./project-DUuzYgGl.js";import{s as v,E as G}from"./StatisticsHelper-D-s_6AyQ.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import{S as p}from"./data-CLo2TII-.js";import s from"./TitleCard-BgReUNwX.js";import P from"./TitleHeader-CBWfLOPA.js";import Z from"./CXCFX-D3VS1APB.js";import T from"./DeviceGroup-DfMgqr6q.js";import M from"./DeviceStatic-DPI9NTSP.js";import O from"./FQTJ-bBdN7OyF.js";import R from"./GSGWKJTJ-Bf5Vp2iZ.js";import Y from"./LLJK-DMALGaHM.js";import Q from"./LSPH-DmSHTeYD.js";import V from"./LightPlat-BpCirFph.js";import X from"./YLJC-CfYIPw6m.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./PipeLength.vue_vue_type_script_setup_true_lang-BZfUsvDf.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./widget-BcWKanF2.js";import"./arcWidgetButton-0glIxrt7.js";import"./pipe-nogVzCHG.js";import"./PoiSearchV2-D7yNeLuv.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./ViewHelper-BGCZjxXH.js";import"./fieldconfig-Bk3o1wi7.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./useWidgets-BRE-VQU9.js";import"./URLHelper-B9aplt5w.js";import"./useLayerList-DmEwJ-ws.js";import"./useScaleBar-Beed-z91.js";import"./zhandian-YaGuQZe6.js";import"./index-BggOjNGp.js";import"./useDetector-BRcb7GRN.js";import"./echart-C9Tas6tA.js";import"./statistics-CeyexT_5.js";import"./6-4nR55Xef.js";import"./index-CknacZq4.js";import"./usePartition-DkcY9fQ2.js";import"./dma-SMxrzG7b.js";import"./hookupDevice-Bcbk7s68.js";import"./index-BlG8PIOK.js";const q={class:"content"},K={class:"toprow"},W={class:"loss-rate"},z={class:"loss-rate-items"},j={class:"toprow-right"},$={class:"bottomrow"},tt=b({__name:"pipeNetwork",setup(et){const f=A(),y={},t=C({lossRates:[{type:"up",delta:11.8,value:23.8,title:"产销差率"},{type:"down",delta:11.8,value:13.8,title:"综合漏损率"},{type:"up",delta:13.8,value:3.8,title:"漏损率"}],pipeData:{pipeLength:0,valve:0,meter:0,drayAirValve:0,hydrant:0,threeCorss:0},deviceData:{pipeLength:0,waterQuality:0,pressure:0,bigUser:0,secondary:0,flow:0},layerInfos:[],layerIds:[]}),w=async()=>{var i,d,_;const o=await v("length",{layerIds:t.layerInfos.filter(m=>m.geometrytype==="esriGeometryPolyline").map(m=>m.layerid)});t.pipeData.pipeLength=(_=(d=(i=o[0])==null?void 0:i.rows)==null?void 0:d[0])==null?void 0:_[G.ShapeLen],t.deviceData.pipeLength=t.pipeData.pipeLength,(await v("count",{layerIds:t.layerIds})).map(m=>{const l=m.rows[0].OBJECTID;switch(m.layername){case"阀门":t.pipeData.valve=l;break;case"水表":t.pipeData.meter=l;break;case"排气阀":t.pipeData.drayAirValve=l;break;case"消防栓":t.pipeData.hydrant=l;break;case"三通":t.pipeData.threeCorss=l;break}})},g=async()=>{var r,i;t.layerIds=F(y.view);const o=await J(t.layerIds);t.layerInfos=((i=(r=o.data)==null?void 0:r.result)==null?void 0:i.rows)||[]},I=async()=>{await g(),await w()},D=U(),L=async()=>{const o=await D.getAllStationOption();t.deviceData.waterQuality=o.filter(r=>r.data.type===p.SHUIZHIJIANCEZHAN).length,t.deviceData.pressure=o.filter(r=>[p.YALIJIANCEZHAN,p.CHELIUYAZHAN].indexOf(r.data.type)!==-1).length,t.deviceData.bigUser=o.filter(r=>r.data.type===p.DAYONGHU).length,t.deviceData.secondary=o.filter(r=>r.data.type===p.BENGZHAN).length,t.deviceData.flow=o.filter(r=>[p.CHELIUYAZHAN,p.LIULIANGJIANCEZHAN].indexOf(r.data.type)!==-1).length};return N(async()=>{var o;L(),y.view=await((o=f.value)==null?void 0:o.init({zoom:11,defaultFilter:"grayscale(0%) invert(100%) opacity(100%)",defaultFilterColor:"rgb(255 218 189)"}))}),S(()=>{var o;(o=f.value)==null||o.destroy(),y.view=void 0}),(o,r)=>(u(),h("div",q,[e(B,{ref_key:"refMap",ref:f,class:"map",tools:["pipe"],onPipeLoaded:I},null,512),n("div",K,[e(T,{"devie-data":c(t).deviceData,class:"device-group"},null,8,["devie-data"]),n("div",W,[e(P,{title:"漏损指标",type:"simple","title-width":180,style:{"border-radius":"18px 0 0 18px"}}),n("div",z,[(u(!0),h(H,null,k(c(t).lossRates,(i,d)=>(u(),E(V,{key:d,data:i,class:"loss-rate-items__item"},null,8,["data"]))),128))])]),n("div",j,[e(s,{class:"toprow-right__item",title:"设备统计"},{default:a(()=>[e(M,{"pipe-data":c(t).pipeData},null,8,["pipe-data"])]),_:1}),e(s,{class:"toprow-right__item",title:"产销差分析"},{default:a(()=>[e(Z)]),_:1}),e(s,{class:"toprow-right__item",title:"供水管网口径统计","title-width":240},{default:a(()=>[e(R,{layerids:c(t).layerIds,layerinfos:c(t).layerInfos},null,8,["layerids","layerinfos"])]),_:1}),e(s,{class:"toprow-right__item",title:"分区统计"},{default:a(()=>[e(O)]),_:1})])]),n("div",$,[e(s,{class:"bottomrow-item bottomrow-left",title:"流量监测"},{default:a(()=>[e(Y)]),_:1}),e(s,{class:"bottomrow-item bottomrow-center",title:"压力监测"},{default:a(()=>[e(X)]),_:1}),e(s,{class:"bottomrow-item bottomrow-right",title:"漏损排行"},{default:a(()=>[e(Q)]),_:1})])]))}}),to=x(tt,[["__scopeId","data-v-70238a7a"]]);export{to as default};
