package org.thingsboard.server.dao.model.sql;

import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.DTO.StationAttrDTO;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;
import java.util.List;


@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.TB_STATION_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class StationEntity implements Serializable {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.TB_STATION_NAME)
    private String name;

    @Column(name = ModelConstants.TB_STATION_TYPE)
    private String type;

    @Column(name = ModelConstants.TB_STATION_ADDRESS)
    private String address;

    @Column(name = ModelConstants.TB_STATION_LOCATION)
    private String location;

    @Column(name = ModelConstants.TB_STATION_ADDITIONAL_INFO)
    private String additionalInfo;

    @Column(name = ModelConstants.TB_STATION_REMARK)
    private String remark;

    @Column(name = ModelConstants.TB_STATION_IMGS)
    private String imgs;

    @Column(name = ModelConstants.TB_STATION_ORDER_NUM)
    private Integer orderNum;

    @Column(name = ModelConstants.TB_STATION_SCADA_URL)
    private String scadaUrl;

    @Column(name = ModelConstants.CREATE_TIME)
    private Date createTime;

    @Column(name = ModelConstants.PROJECT_RELATION_PROJECT_ID)
    private String projectId;

    @Column(name = ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;

    @Transient
    private JSONObject info;

    @Transient
    private List<StationAttrDTO> stationAttrInfo;

}
