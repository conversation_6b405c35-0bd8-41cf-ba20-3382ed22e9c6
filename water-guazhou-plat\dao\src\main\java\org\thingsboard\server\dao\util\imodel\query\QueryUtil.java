package org.thingsboard.server.dao.util.imodel.query;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;
import org.springframework.cglib.proxy.Enhancer;
import org.springframework.data.jpa.domain.Specification;
import org.thingsboard.server.dao.model.sql.statistic.*;
import org.thingsboard.server.dao.util.TimeUtils;
import org.thingsboard.server.dao.util.imodel.query.cache.QueryCacheContainer;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.maintain.SMMaintainTaskItemSaveRequest;
import org.thingsboard.server.dao.util.imodel.response.tree.Identifiable;
import org.thingsboard.server.dao.util.imodel.response.tree.TreeEntityNode;
import org.thingsboard.server.dao.util.imodel.response.tree.TreeableEntityNode;
import org.thingsboard.server.dao.util.imodel.response.tree.TreeableInterceptor;
import org.thingsboard.server.dao.util.reflection.ReflectionUtils;

import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.function.LongSupplier;
import java.util.stream.Collectors;

import static java.util.concurrent.TimeUnit.MILLISECONDS;
import static org.thingsboard.server.dao.util.imodel.query.PageableQueryEntity.PageableQueryEntityType.MANUAL;

@Slf4j
public class QueryUtil {
    private static final QueryCacheContainer cache = new QueryCacheContainer();

    private static final String DEFAULT_ID_FIELD_NAME = "id";

    private QueryUtil() {

    }

    public static <T> T saveOrUpdateOneByRequest(SaveRequest<T> obj, BaseMapper<T> mapper) {
        return saveOrUpdateOneByRequest(obj, DEFAULT_ID_FIELD_NAME, mapper::insert, mapper::updateById);
    }

    public static <T> T saveOrUpdateOneByRequest(SaveRequest<T> obj, String idName, BaseMapper<T> mapper) {
        return saveOrUpdateOneByRequest(obj, idName, mapper::insert, mapper::updateById);
    }

    public static <T> T saveOrUpdateOneByRequest(SaveRequest<T> obj, Function<T, ?> saveFunction, Function<T, ?> updateFunction) {
        return saveOrUpdateOneByRequest(obj, DEFAULT_ID_FIELD_NAME, saveFunction, updateFunction);
    }

    public static <T> T saveOrUpdateOneByRequest(SaveRequest<T> obj, String idName, Function<T, ?> saveFunction, Function<T, ?> updateFunction) {
        return saveOrUpdateOne(obj.unwrap(), idName, saveFunction, updateFunction);
    }

    public static <T> T saveOrUpdateOne(T obj, BaseMapper<T> mapper) {
        return saveOrUpdateOne(obj, DEFAULT_ID_FIELD_NAME, mapper::insert, mapper::updateById);
    }

    public static <T> T saveOrUpdateOne(T obj, Function<T, ?> saveFunction, Function<T, ?> updateFunction) {
        return saveOrUpdateOne(obj, DEFAULT_ID_FIELD_NAME, saveFunction, updateFunction);
    }

    public static <T> T saveOrUpdateOne(T obj, String idName, Function<T, ?> saveFunction, Function<T, ?> updateFunction) {
        if (obj == null)
            return null;
        Field field = ReflectionUtils.getField(obj.getClass(), idName);
        boolean isSuccess = ReflectionUtils.getValue(field, obj) == null ? convertToBool(saveFunction.apply(obj)) : convertToBool(updateFunction.apply(obj));
        return isSuccess ? obj : null;
    }

    public static <T> List<T> saveBatchByRequest(List<? extends SaveRequest<T>> requests, Function<List<T>, ?> saveFunction) {
        List<T> list = collectRequests(requests);
        saveFunction.apply(list);
        return list;
    }

    public static <T> List<T> saveOrUpdateBatchByRequest(List<? extends SaveRequest<T>> requests, Function<List<T>, ?> saveFunction, Function<List<T>, ?> updateFunction) {
        List<T> list = collectRequests(requests);
        return saveOrUpdateBatch(list, DEFAULT_ID_FIELD_NAME, saveFunction, updateFunction);
    }

    public static <T> List<T> saveOrUpdateBatchByRequest(List<? extends SaveRequest<T>> requests, String idName, Function<List<T>, ?> saveFunction, Function<List<T>, ?> updateFunction) {
        List<T> list = collectRequests(requests);
        return saveOrUpdateBatch(list, idName, saveFunction, updateFunction);
    }


    public static <T> List<T> saveOrUpdateBatch(List<T> items, Function<List<T>, ?> saveFunction, Function<List<T>, ?> updateFunction) {
        return saveOrUpdateBatch(items, DEFAULT_ID_FIELD_NAME, saveFunction, updateFunction);
    }

    public static <T> List<T> saveOrUpdateBatch(List<T> items, String idName,
                                                Function<List<T>, ?> saveFunction, Function<List<T>, ?> updateFunction) {
        if (items == null || items.isEmpty())
            return Collections.emptyList();

        boolean isSuccess;
        if (saveFunction.equals(updateFunction)) {
            isSuccess = convertToBool(saveFunction.apply(items));
        } else {
            Object obj = items.stream().findFirst().get();
            Field field = ReflectionUtils.getField(obj.getClass(), idName);

            Map<Object, List<T>> groups = items.stream().collect(Collectors.groupingBy(t -> ReflectionUtils.getValue(field, t) == null ? 0 : 1));
            List<T> saves = groups.get(0), updates = groups.get(1);
            isSuccess = (saves == null || saves.size() == 0 || convertToBool(saveFunction.apply(saves))) &&
                        (updates == null || updates.size() == 0 || convertToBool(updateFunction.apply(updates)));
        }
        return isSuccess ? items : null;
    }

    public static void disallowUpdate(List<SMMaintainTaskItemSaveRequest> items) {
        for (SMMaintainTaskItemSaveRequest item : items) {
            item.disallowUpdate();
        }
    }

    @FunctionalInterface
    public interface BiLongFunction<R> {
        R apply(long i, long j);

    }

    /**
     * 列表请求分页化
     *
     * @param page  处理后的分页请求
     * @param items 列表请求结果提供者
     * @param total 列表中的条目数
     * @param <T>   条目类型
     * @return 分页化结果
     */
    public static <T> IPage<T> pagify(AdvancedPageableQueryEntity<T, ?> page, BiLongFunction<List<T>> items, LongSupplier total) {
        long offset = page.offset();
        long size = page.getSize();
        page.type(MANUAL);
        if (size > -1)
            page.setTotal(total.getAsLong());
        page.setRecords(page.getTotal() > 0 && page.getCurrent() <= page.getPages() ? items.apply(offset, size) : Collections.emptyList());
        return page;
    }

    @SuppressWarnings("rawtypes")
    public static <T extends AdvancedPageableQueryEntity, U> Specification<U> wrapConditionalQuery(Class<T> clazz, T entity) {
        return wrapConditionalQuery(clazz, entity, null);
    }

    public static <T> List<T> collectRequests(SaveRequest<T>[] reqs) {
        return collectRequests(Arrays.asList(reqs));
    }

    public static <T> List<T> collectRequests(List<? extends SaveRequest<T>> reqs) {
        if (reqs == null)
            return Collections.emptyList();
        return reqs.stream().map(SaveRequest::unwrap).collect(Collectors.toList());
    }

    /**
     * 自动生成条件查询逻辑
     *
     * @param clazz  业务对象类
     * @param entity 业务对象请求
     * @return 查询逻辑
     */
    public static <T extends TimeableQueryEntity, U> Specification<U> wrapConditionalQuery(Class<T> clazz, T entity, AdditionalQueryHandler handler) {
        return wrapConditionalQuery(clazz, entity, handler, "createTime");
    }

    /**
     * 自动生成条件查询逻辑
     *
     * @param clazz  业务对象类
     * @param entity 业务对象请求
     * @return 查询逻辑
     */
    public static <T extends TimeableQueryEntity, U> Specification<U> wrapConditionalQuery(Class<T> clazz, T entity, AdditionalQueryHandler handler, String timeFieldName) {
        return (root, query, builder) -> {
            CriteriaBuilderWrapper wrapper = new CriteriaBuilderWrapper(root, query, builder);

            // 按时间查询过滤
            boolean hasFromDate = entity.getFromTime() != null;
            boolean hasToDate = entity.getToTime() != null;
            if (!hasFromDate && !hasToDate) {
                wrapper.dateGreaterThanOrEqualTo(timeFieldName, TimeUtils.lastOneMonth());
            } else {
                if (hasFromDate)
                    wrapper.dateGreaterThanOrEqualTo(timeFieldName, entity.getFromTime());

                if (hasToDate)
                    wrapper.dateLessThanOrEqualTo(timeFieldName, entity.getToTime());
            }

            // 自定义查询过滤
            cache.resolve(clazz, entity, wrapper);

            if (handler != null) {
                handler.invoke(wrapper);
            }

            return wrapper.build();
        };
    }

    public static <T> Specification<T> findAllOrderByDate(String identityFieldName, Object value, String orderByFieldName) {
        return (root, query, builder) -> {
            CriteriaBuilderWrapper wrapper = new CriteriaBuilderWrapper(root, query, builder);
            wrapper.equalTo(identityFieldName, value, false);
            wrapper.orderByDate(orderByFieldName, true);
            return wrapper.build();
        };
    }


    private static Date parseDate(String pattern) {
        // return DateTime.parse(pattern).toDate();
        return new Date(Long.parseLong(pattern));
    }

    public static void copyNonNullProperties(Object source, Object target) {
        if (source == null || target == null) {
            return;
        }

        final BeanWrapper src = new BeanWrapperImpl(source);
        java.beans.PropertyDescriptor[] pds = src.getPropertyDescriptors();

        Set<String> emptyNames = new HashSet<>();
        for (java.beans.PropertyDescriptor pd : pds) {
            try {
                Object srcValue = src.getPropertyValue(pd.getName());
                if (srcValue == null) emptyNames.add(pd.getName());
            } catch (Exception e) {
                emptyNames.add(pd.getName());
            }
        }

        String[] ignores = emptyNames.toArray(new String[0]);
        BeanUtils.copyProperties(source, target, ignores);
    }

    @SafeVarargs
    @Deprecated
    public static <U extends TreeEntityNode> List<U> queryTree(List<U> roots, Function<String, List<? extends TreeEntityNode>>... childQueries) {
        return queryTree(roots, 0, true, childQueries);
    }

    @SafeVarargs
    @Deprecated
    public static <U extends TreeEntityNode> List<U> queryMixTree(List<U> roots, Function<String, List<? extends TreeEntityNode>>... childQueries) {
        return queryTree(roots, 0, false, childQueries);
    }

    @Deprecated
    public static <U extends TreeEntityNode> List<U> queryTree(List<U> roots, int layer, boolean pure, Function<String, List<? extends TreeEntityNode>>[] childQueries) {
        if (roots.isEmpty())
            return Collections.emptyList();

        for (TreeEntityNode root : roots) {
            // 获取当前层级
            int currentLayer = root.layer() - 1;
            List<TreeEntityNode> nextRoots = new ArrayList<>();
            for (int i = currentLayer; i < childQueries.length; i++) {
                // 查询当前层级的子节点
                List<? extends TreeEntityNode> mixRoots = childQueries[i].apply(root.getId());
                for (TreeEntityNode mixRoot : mixRoots) {
                    mixRoot.layer(i);
                }
                nextRoots.addAll(mixRoots);
                if (pure && (mixRoots.size() > 0 || i - layer > 0))
                    break;
            }

            List<TreeEntityNode> nodes = queryTree(nextRoots, layer, pure, childQueries);

            if (!nodes.isEmpty())
                root.addChildren(nodes);
            else
                root.markAsEmptyTreeNode();
        }

        return roots;
    }

    @SuppressWarnings({"unchecked", "rawtypes"})
    @SafeVarargs
    public static <U> List<U> buildTreeWithFakeRoot(List<U> roots, U entity, Function<String, List<?>>... childQueries) {
        List actualRoots = buildTree(roots, TreeQueryOptions.create().pure(true), childQueries);
        U fakeRoot = createTreeNodeProxy(entity);
        ((TreeableEntityNode) fakeRoot).children(actualRoots);
        return Collections.singletonList(fakeRoot);
    }

    @SafeVarargs
    public static <U> List<U> buildTree(List<U> roots, Function<String, List<?>>... childQueries) {
        return buildTree(roots, TreeQueryOptions.withPure(true), childQueries);
    }

    public static <U> List<U> buildTree(List<U> roots, TreeQuery... childQueries) {
        return buildTree(roots, TreeQueryOptions.withPure(true), childQueries);
    }

    @SafeVarargs
    public static <U> List<U> buildMixTree(List<U> roots, Function<String, List<?>>... childQueries) {
        return buildTree(roots, TreeQueryOptions.withPure(false), childQueries);
    }

    public static <U> List<U> buildMixTree(List<U> roots, TreeQuery... childQueries) {
        return buildTree(roots, TreeQueryOptions.withPure(false), childQueries);
    }

    @SuppressWarnings({"rawtypes", "unchecked"})
    @SafeVarargs
    public static <U> List<U> buildTree(List roots, TreeQueryOptions options, Function<String, List<?>>... childQueries) {
        replaceWithTreeNodeProxys(roots);
        return doBuildTree(roots, options, childQueries);
    }

    @SuppressWarnings({"rawtypes", "unchecked"})
    public static <U> List<U> buildTree(List roots, TreeQueryOptions options, TreeQuery[] childQuerys) {
        replaceWithTreeNodeProxys(roots);
        return doBuildTree(roots, options, childQuerys);
    }

    private static <T extends TreeableEntityNode> List<T> doBuildTree(List<T> roots, TreeQueryOptions options, Function<String, List<?>>[] childQueries) {
        return doBuildTree(roots, options, Arrays.stream(childQueries).map(TreeQuery::of).toArray(TreeQuery[]::new));
    }

    @SuppressWarnings({"rawtypes", "unchecked"})
    private static <T extends TreeableEntityNode> List<T> doBuildTree(List<T> roots, TreeQueryOptions options, TreeQuery[] childQueries) {
        options.maxDepth(childQueries.length);
        if (roots.isEmpty())
            return Collections.emptyList();
        // 装载配置
        for (T root : roots) {
            root.options(options);
        }

        boolean pure = options.pure();
        boolean prune = options.prune();
        for (TreeableEntityNode root : roots) {
            // 获取当前层级
            int currentLayer = root.layer();
            List<TreeableEntityNode> nextRoots = new ArrayList<>();
            for (int j = currentLayer; j < childQueries.length; j++) {
                // 层数限制
                if (root.rest() == 0)
                    break;
                // 查询当前层级的子节点
                if (!(root instanceof Identifiable)) {
                    continue;
                }
                List mixRoots = childQueries[j].apply(root.getId());
                replaceWithTreeNodeProxys(mixRoots);
                for (Object mixRoot : mixRoots) {
                    TreeableEntityNode treeableEntityNode = (TreeableEntityNode) mixRoot;
                    treeableEntityNode.layer(j);
                    treeableEntityNode.rest(root.rest() == Integer.MIN_VALUE ? childQueries[j].limit() : root.take());
                }
                nextRoots.addAll(mixRoots);
                if (pure && (mixRoots.size() > 0 || j - currentLayer > 0))
                    break;
            }

            List<TreeableEntityNode> nodes = doBuildTree(nextRoots, options, childQueries);

            root.children(nodes);
        }

        return roots;
    }


    @SuppressWarnings({"rawtypes", "unchecked"})
    private static void replaceWithTreeNodeProxys(List entitys) {
        entitys.replaceAll(QueryUtil::createTreeNodeProxy);
    }

    @SuppressWarnings("unchecked")
    private static <T> T createTreeNodeProxy(T entity) {
        Enhancer enhancer = new Enhancer();
        enhancer.setSuperclass(entity.getClass());
        enhancer.setInterfaces(new Class[]{TreeableEntityNode.class});
        enhancer.setCallback(new TreeableInterceptor(entity));
        return (T) enhancer.create();
    }

    /**
     * 分类分时统计
     *
     * @param timeUnit 时间推进单位
     * @param req      分时实体
     * @param delegate 数据库查询方法
     * @return 统计实体
     */
    public static StatisticItem statisticByKeyThenTime(StatisticTimeUnit timeUnit, TimeableQueryEntity req, TimedStatisticLongRepoDelegate delegate) {
        return statisticByKeyThenTime(timeUnit, req.getFromTime(), req.getToTime(), delegate);
    }

    /**
     * 分类分时统计
     *
     * @param timeUnit 时间推进单位
     * @param fromTime 开始时间
     * @param toTime   结束时间
     * @param delegate 数据库查询方法
     * @return 统计实体
     */
    public static StatisticItem statisticByKeyThenTime(StatisticTimeUnit timeUnit, Date fromTime, Date toTime, TimedStatisticLongRepoDelegate delegate) {
        fromTime = TimeUtils.computeDefaultIfNull(fromTime, () -> new DateTime().minusMonths(1).toDate());
        toTime = TimeUtils.computeDefaultIfNull(toTime, Date::new);
        TimedStatisticLongWrapperSplitter splitter = new TimedStatisticLongWrapperSplitter();
        executeTimeByTime(timeUnit.getIterator(fromTime, toTime),
                (from, to) -> splitter.split(delegate.invoke(from, to), from, to));
        return splitter;
    }

    /**
     * 分时分类统计
     *
     * @param req      分时实体
     * @param delegate 数据库查询方法
     * @return 统计实体
     */
    public static StatisticItem statisticByTimeThenKey(TimeableQueryEntity req, TimedStatisticLongRepoDelegate delegate) {
        return statisticByTimeThenKey(req.getFromTime(), req.getToTime(), delegate);
    }

    /**
     * 分时分类统计
     *
     * @param fromTime 开始时间
     * @param toTime   结束时间
     * @param delegate 数据库查询方法
     * @return 统计实体
     */
    public static StatisticItem statisticByTimeThenKey(Date fromTime, Date toTime, TimedStatisticLongRepoDelegate delegate) {
        Date from = TimeUtils.computeDefaultIfNull(fromTime, () -> new DateTime().minusMonths(1).toDate());
        Date to = TimeUtils.computeDefaultIfNull(toTime, Date::new);
        return new ScopeStatisticLongWrapper(delegate.invoke(from, to));
    }

    /**
     * 强化统计方案
     *
     * @param timeUnit 时间推进单位
     * @param req      分时实体
     * @param delegate 数据库查询方法
     * @return 统计实体
     */
    public static StatisticItem statistic(StatisticTimeUnit timeUnit, TimeableQueryEntity req,
                                          TimedStatisticLongRepoDelegate delegate) {
        return statistic(timeUnit, timeUnit != null, req.getFromTime(), req.getToTime(), delegate);
    }

    /**
     * 强化统计方案
     *
     * @param timeUnit 时间推进单位
     * @param fromTime 开始时间
     * @param toTime   结束时间
     * @param delegate 数据库查询方法
     * @return 统计实体
     */
    public static StatisticItem statistic(StatisticTimeUnit timeUnit, Date fromTime, Date toTime,
                                          TimedStatisticLongRepoDelegate delegate) {
        return statistic(timeUnit, timeUnit != null, fromTime, toTime, delegate);
    }

    /**
     * 强化统计方案
     *
     * @param timeUnit  时间推进单位
     * @param timeSlice 是否为分类分时统计模式，为false时进行分时分类统计
     * @param fromTime  开始时间
     * @param toTime    结束时间
     * @param delegate  数据库查询方法
     * @return 统计实体
     */
    public static StatisticItem statistic(StatisticTimeUnit timeUnit, boolean timeSlice, Date fromTime, Date toTime,
                                          TimedStatisticLongRepoDelegate delegate) {
        return timeSlice ? statisticByKeyThenTime(timeUnit, fromTime, toTime, delegate) :
                statisticByTimeThenKey(fromTime, toTime, delegate);
    }

    private static void executeTimeByTime(Iterator<StatisticDateEntry> iterator, StatisticTimeCallback callback) {
        while (iterator.hasNext()) {
            StatisticDateEntry entry = iterator.next();
            callback.invoke(entry.getFrom(), entry.getTo());
        }
    }

    private static boolean convertToBool(Object obj) {
        if (obj instanceof Integer) {
            return (int) obj > 0;
        } else if (obj instanceof Boolean) {
            return (boolean) obj;
        }
        return false;
    }

    public static boolean deleteBatch(List<String> remove, BaseMapper<?> mapper) {
        return deleteBatch(remove, mapper::deleteBatchIds);
    }

    public static boolean deleteBatch(List<String> remove, Function<List<String>, Integer> deleteBatchIds) {
        if (remove == null)
            return false;
        return deleteBatchIds.apply(remove) > 0;
    }

    @FunctionalInterface
    public interface NativeSequenceSupplier<T> {
        T get(Date beginTime, Date endTime);

        default SequenceSupplier<T> toGeneralSequenceSupplier() {
            return (beginTime, endTime) -> get(beginTime.toDate(), endTime.toDate());
        }

    }

    @FunctionalInterface
    public interface SequenceSupplier<T> {
        T get(DateTime beginTime, DateTime endTime);

        default T get(Date beginTime, DateTime endTime) {
            return get(new DateTime(beginTime), endTime);
        }

        default T get(Date beginTime, Date endTime) {
            return get(new DateTime(beginTime), new DateTime(endTime));
        }

    }

    /**
     * @param beginTime     开始时间
     * @param executionDays 执行时间
     * @param intervalDays  间隔时间
     * @param executionNum  执行次数
     * @param supplier      包装器
     */
    public static <T> List<T> generateSequence(Date beginTime, int executionDays, int intervalDays, int executionNum, SequenceSupplier<T> supplier) {
        List<T> result = new ArrayList<>();
        DateTime endTime = new DateTime(beginTime).plusDays(executionDays);
        T item = supplier.get(beginTime, endTime);
        for (int i = 0; i < executionNum - 1; i++) {
            result.add(item);
            item = supplier.get(
                    endTime.plusDays(intervalDays)
                    , endTime.plusDays(executionDays));
            endTime = endTime.plusDays(intervalDays + executionDays);
        }
        result.add(item);
        return result;
    }


    public static <T> List<T> generateSequence(Date beginTime, int executionDays, int intervalDays, int executionNum, NativeSequenceSupplier<T> supplier) {
        return generateSequence(beginTime, executionDays, intervalDays, executionNum, supplier.toGeneralSequenceSupplier());
    }


    /**
     * @param from     开始时间
     * @param end      结束时间
     * @param step     推进时间
     * @param stepUnit 推进时间单位
     * @param supplier 包装器
     */
    public static <T> List<T> generateSequence(DateTime from, DateTime end, long step, TimeUnit stepUnit, SequenceSupplier<T> supplier) {
        return generateSequence(from, end, step, stepUnit, 0, MILLISECONDS, supplier);
    }

    /**
     * @param from     开始时间
     * @param end      结束时间
     * @param step     推进时间
     * @param stepUnit 推进时间单位
     * @param skip     间隔时间
     * @param skipUnit 间隔时间单位
     * @param supplier 包装器
     */
    public static <T> List<T> generateSequence(DateTime from, DateTime end, long step, TimeUnit stepUnit, long skip, TimeUnit skipUnit, SequenceSupplier<T> supplier) {
        List<T> result = new ArrayList<>();
        long stepMills = MILLISECONDS.convert(step, stepUnit);
        long skipMills = MILLISECONDS.convert(skip, skipUnit);
        DateTime endTime = from.plus(stepMills);
        T item = supplier.get(from, endTime);
        endTime = endTime.plus(skipMills);
        while (endTime.isBefore(end)) {
            result.add(item);
            DateTime tempEndTime = endTime.plus(stepMills);
            tempEndTime = tempEndTime.isBefore(end) ? tempEndTime : end;
            item = supplier.get(endTime, tempEndTime);
            endTime = tempEndTime.plus(skipMills);
        }
        result.add(item);
        return result;
    }

    /**
     * 推进时间以生成序列，推进方式如下
     * 获取 开始时间（开始时间第一次为from，其它时候由时间间隙生成器提供） ~ 结束时间（结束时间由时间段生成器提供）
     *
     * @param from     开始时间
     * @param end      结束时间
     * @param stepDate 时间段生成器，用于获取当前步骤结束时间
     * @param skipDate 时间间隙生成器，用于制造时间间隙
     * @param supplier 对象生成器
     * @return 生成好的序列
     */
    public static <T> List<T> generateSequence(DateTime from,
                                               DateTime end,
                                               Function<DateTime, DateTime> stepDate,
                                               Function<DateTime, DateTime> skipDate,
                                               SequenceSupplier<T> supplier) {
        List<T> result = new ArrayList<>();
        DateTime endTime = stepDate.apply(from);
        T item = supplier.get(from, endTime);
        endTime = skipDate.apply(endTime);
        while (endTime.isBefore(end)) {
            if (item != null) {
                result.add(item);
            }
            DateTime tempEndTime = stepDate.apply(endTime);
            tempEndTime = tempEndTime.isBefore(end) ? tempEndTime : end;
            item = supplier.get(endTime, tempEndTime);
            endTime = skipDate.apply(tempEndTime);
        }
        result.add(item);
        return result;
    }

    public static <T> List<T> generateSequence(DateTime from,
                                               DateTime end,
                                               Function<DateTime, DateTime> stepDate,
                                               Function<DateTime, DateTime> skipDate,
                                               NativeSequenceSupplier<T> supplier) {
        return generateSequence(from, end, stepDate, skipDate, supplier.toGeneralSequenceSupplier());
    }

    public static void arrangeTimedSaveRequests(List<? extends SaveRequest<?>> requests) {
        DateTime now = new DateTime();
        for (int i = 0; i < requests.size(); i++) {
            SaveRequest<?> request = requests.get(i);
            request.preInitializeCreateTime(now.plusMillis(requests.size() - i - 1).toDate());
        }
    }

}
