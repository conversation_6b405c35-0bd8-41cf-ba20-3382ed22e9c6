package org.thingsboard.server.dao.smartService.portal;

import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.smartService.portal.SsPortalVideo;
import org.thingsboard.server.dao.util.imodel.query.smartService.portal.SsPortalVideoPageRequest;

public interface SsPortalVideoService {

    SsPortalVideo save(SsPortalVideo ssPortalVideo);

    boolean delete(String id);

    PageData<SsPortalVideo> getList(SsPortalVideoPageRequest request);
}
