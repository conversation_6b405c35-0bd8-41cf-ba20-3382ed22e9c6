package org.thingsboard.server.dao.util.imodel.query.smartProduction.guard;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import org.joda.time.DateTime;
import org.thingsboard.server.dao.util.TimeUtils;
import org.thingsboard.server.dao.util.imodel.query.IStarHttpRequest;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.Requestible;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

import java.util.*;
import java.util.stream.Collectors;

@Getter
@Setter
public class GuardArrangeQuickArrangeRequest implements Requestible {

    @Getter
    @Setter
    static class DayOfWeekInfo {
        // 周几（1~7）
        private Integer dayOfWeek;

        // 班次id
        private String classId;

        // 班组id
        private String groupId;

    }

    // 地点id
    @NotNullOrEmpty
    private String placeId;

    // 周安排信息
    @NotNullOrEmpty
    private List<DayOfWeekInfo> dayOfWeekInfoList;

    // 第几月开始
    @NotNullOrEmpty
    private Date fromMonth;

    // 第几月结束
    @NotNullOrEmpty
    private Date toMonth;

    // ===================================
    // 周-班次-周安排数据映射
    @Setter(AccessLevel.NONE)
    @Getter(AccessLevel.NONE)
    private Map<Integer, List<DayOfWeekInfo>> dayOfWeekInfoMap;

    @Setter(AccessLevel.NONE)
    private List<Date> hitDate;

    private boolean rearrangeOnlyCollectHitDate;


    @SuppressWarnings("Convert2MethodRef")
    @Override
    public String valid(IStarHttpRequest request) {
        dayOfWeekInfoMap = dayOfWeekInfoList.stream()
                .collect(Collectors.toMap(
                        x -> x.getDayOfWeek(),
                        x -> {
                            ArrayList<DayOfWeekInfo> list = new ArrayList<>();
                            list.add(x);
                            return list;
                        },
                        (a, b) -> {
                            a.addAll(b);
                            return a;
                        }
                ));

        fromMonth = TimeUtils.floorMonth(fromMonth);
        toMonth = TimeUtils.ceilMonth(toMonth);
        return Requestible.super.valid(request);
    }

    public List<GuardArrangeQuickArrangeData> arrange() {
        hitDate = new ArrayList<>();
        return QueryUtil.generateSequence(
                        new DateTime(fromMonth),
                        new DateTime(toMonth),
                        time -> time.plusDays(1),
                        time -> time, this::createArrange).stream().flatMap(Collection::stream)
                .collect(Collectors.toList());
    }

    public void arrangeCollectHitDateOnly() {
        rearrangeOnlyCollectHitDate = true;
        arrange();
    }

    private List<GuardArrangeQuickArrangeData> createArrange(DateTime fromTime, DateTime toTime) {
        int dayOfWeek = fromTime.dayOfWeek().get();
        Collection<DayOfWeekInfo> infoList = dayOfWeekInfoMap.get(dayOfWeek);
        if (infoList == null || infoList.size() == 0) {
            return Collections.emptyList();
        }

        Date arrangeTime = fromTime.toDate();
        hitDate.add(arrangeTime);
        if (rearrangeOnlyCollectHitDate) {
            return Collections.emptyList();
        }

        return infoList.stream()
                .map(info -> {
                    GuardArrangeQuickArrangeData arrange = new GuardArrangeQuickArrangeData();
                    arrange.setPlaceId(placeId);
                    arrange.setClassId(info.getClassId());
                    arrange.setGroupId(info.getGroupId());
                    arrange.setDayTime(arrangeTime);
                    return arrange;
                }).collect(Collectors.toList());
    }

}