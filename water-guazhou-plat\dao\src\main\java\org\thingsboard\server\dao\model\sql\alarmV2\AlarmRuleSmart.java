package org.thingsboard.server.dao.model.sql.alarmV2;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 报警Version2 报警规则
 */
@Data
@TableName("tb_alarm_rule_smart")
@NoArgsConstructor
@AllArgsConstructor
public class AlarmRuleSmart implements Serializable {

    @TableId
    private String id;

    private String stationId;

    @TableField(exist = false)
    private String stationName;

    private String deviceId;

    @TableField(exist = false)
    private String deviceName;

    @TableField(exist = false)
    private String attributeId;

    private String attr;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    private Integer interval;

    private String enableStartTime;

    private String enableEndTime;

    private String enableWeekday;

    private BigDecimal referenceValue;

    private Integer dataNum;

    private BigDecimal maxValue;

    private BigDecimal minValue;

    private BigDecimal upLimitValue;

    private BigDecimal downLimitValue;

    private String type;

    private Date createTime;

    private String tenantId;

    private String warnType;

    private String enabled;

    @TableField(exist = false)
    private List<AlarmRuleUser> msgList = new ArrayList<>();

    @TableField(exist = false)
    private List<AlarmRuleUser> appList = new ArrayList<>();


}
