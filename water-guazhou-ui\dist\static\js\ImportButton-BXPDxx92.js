import{d as i,g as r,h as _,F as c,q as f,G as d,bh as m,J as u,bK as g,C as h}from"./index-r0dFAfgr.js";/* empty css                                                                     */const B=i({__name:"ImportButton",props:{config:{}},setup(s){const e=s,l=async o=>{const a=o.file,t=new window.FormData;t.append("file",a),e.config.click&&e.config.click(t)};return(o,a)=>{const t=u,p=g;return r(),_(p,{class:"deviceImportBtn",action:"action","show-file-list":!1,"http-request":l},{default:c(()=>[f(t,null,{default:c(()=>{var n;return[d(m(((n=o.config)==null?void 0:n.text)||"导 入"),1)]}),_:1})]),_:1})}}}),I=h(B,[["__scopeId","data-v-3e31a82a"]]);export{I};
