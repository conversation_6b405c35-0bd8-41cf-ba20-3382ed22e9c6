package org.thingsboard.server.dao.util.imodel.query.base;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.thingsboard.server.dao.model.sql.base.BaseProxyConfiguration;
import org.thingsboard.server.dao.util.imodel.query.PageableQueryEntity;

/**
 * 平台管理-代理配置对象 base_proxy_configuration
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
@ApiModel(value = "代理配置", description = "平台管理-代理配置实体类")
@Data
public class BaseProxyConfigurationPageRequest extends PageableQueryEntity<BaseProxyConfiguration> {

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 配置文件信息
     */
    @ApiModelProperty(value = "配置文件信息")
    private String proxyConfig;

    /**
     * nginx版本
     */
    @ApiModelProperty(value = "nginx版本")
    private String version;

    /**
     * 状态（启动、关闭）
     */
    @ApiModelProperty(value = "状态（启动、关闭）")
    private String status;

    /**
     * 处理进程数
     */
    @ApiModelProperty(value = "状处理进程数态（启动、关闭）")
    private String workerProcesses;

    /**
     * 最大并发链接数
     */
    @ApiModelProperty(value = "最大并发链接数")
    private String workerConnections;

    /**
     * 连接超时时间
     */
    @ApiModelProperty(value = "连接超时时间")
    private String keepAliveTimeout;

    /**
     * 日志状态（启动、关闭）
     */
    @ApiModelProperty(value = "日志状态（启动、关闭）")
    private String logStatus;

    /**
     * 缓存状态（启动、关闭）
     */
    @ApiModelProperty(value = "缓存状态（启动、关闭）")
    private String cacheStatus;
}
