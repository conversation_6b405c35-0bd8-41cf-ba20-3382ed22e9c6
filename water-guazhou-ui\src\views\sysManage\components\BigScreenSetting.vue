<template>
  <div class="wrapper">
    <p class="p-title-tips">
      开启菜单 名称 地址 不可为空 不可重复
    </p>
    <div class="big-container-data">
      <div
        v-for="(item, i) in configUrl"
        :key="i"
      >
        <div class="data-row">
          <span class="data-row-span"><i class="iconfont icon-yemian"></i> 页面{{ i + 1 }}</span>
          <span class="row-label">菜单名称</span>
          <el-input
            v-model="item.name"
            placeholder="请输入名称"
            class="row-name-input"
          ></el-input>
          <span class="row-label">地址</span>
          <el-input
            v-model="item.url"
            placeholder="请输入地址"
            class="row-url-input"
          ></el-input>
          <el-switch
            v-model="item.state"
            active-text="开启"
            inactive-text="关闭"
            inactive-color="#C0CCDA"
            active-color="#409EFF"
          ></el-switch>
        </div>
      </div>
    </div>
    <el-button
      class="edit-primary-blue"
      @click="saveBigScreen"
    >
      保存
    </el-button>
  </div>
</template>
<script lang="ts" setup>
import { getConstantsAttributeById, saveConstants } from '@/api/constants'
import { usePermissionStore, useUserStore } from '@/store'
import { SLMessage } from '@/utils/Message'

// 大屏
const configUrl = ref<any>([])
const tenantId = ref(useUserStore().tenantId)
const currentSYS = computed(() => useUserStore().roles[0] === 'SYS_ADMIN')
const sysBigScreen = ref([
  {
    name: '',
    url: '',
    routerUrl: '/extendPage/sysPage/',
    state: false,
    key: 'sysPage1'
  },
  {
    name: '',
    url: '',
    routerUrl: '/extendPage/sysPage/',
    state: false,
    key: 'sysPage2'
  },
  {
    name: '',
    url: '',
    routerUrl: '/extendPage/sysPage/',
    state: false,
    key: 'sysPage3'
  },
  {
    name: '',
    url: '',
    routerUrl: '/extendPage/sysPage/',
    state: false,
    key: 'sysPage4'
  },
  {
    name: '',
    url: '',
    routerUrl: '/extendPage/sysPage/',
    state: false,
    key: 'sysPage5'
  },
  {
    name: '',
    url: '',
    routerUrl: '/extendPage/sysPage/',
    state: false,
    key: 'sysPage6'
  }
])
const TenantBigScreen = ref([
  {
    name: '',
    url: '',
    routerUrl: '/extendPage/sysPage/',
    state: false,
    key: 'tenantPage1'
  },
  {
    name: '',
    url: '',
    routerUrl: '/extendPage/sysPage/',
    state: false,
    key: 'tenantPage2'
  },
  {
    name: '',
    url: '',
    routerUrl: '/extendPage/sysPage/',
    state: false,
    key: 'tenantPage3'
  },
  {
    name: '',
    url: '',
    routerUrl: '/extendPage/sysPage/',
    state: false,
    key: 'tenantPage4'
  },
  {
    name: '',
    url: '',
    routerUrl: '/extendPage/sysPage/',
    state: false,
    key: 'tenantPage5'
  },
  {
    name: '',
    url: '',
    routerUrl: '/extendPage/sysPage/',
    state: false,
    key: 'tenantPage6'
  }
])
// 保存大屏配置
const saveBigScreen = () => {
  console.log(configUrl.value, '----s')
  const data = {}
  const newRouter = []
  const nameText = {} // []
  const urlList = {} // []
  for (const item of configUrl.value) {
    if (item.state) {
      if (item.name === '' || item.url === '') {
        SLMessage.error('开启的菜单 名称 地址 不可为空')
        return
      }
      nameText[item.name] = item.key
      urlList[item.url] = item.key
    }
  }

  for (const [i, item] of configUrl.value.entries()) {
    // 验证开启的菜单
    if (item.state) {
      // if (urlList.includes(item.url) || nameText.includes(item.name)) {
      if (
        (urlList[item.url] && urlList[item.url] !== item.key)
        || (nameText[item.name] && nameText[item.name] !== item.key)
      ) {
        console.log(item, nameText, urlList, 'item')
        SLMessage.error('开启的菜单 名称 地址 不可重复')
        return
      }
      const reg = /^[0-9a-zA-Z\u4e00-\u9fa5]{1,8}$/
      if (!reg.test(item.name)) {
        SLMessage.error('菜单名称应在1到8个字符')
        return
      }
      const valid = /(http|ftp|https):\/\/[\w\-_]+(\.[\w\-_]+)+([\w\-.,@?^=%&:/~+#]*[\w\-@?^=%&/~+#])?/g
      // console.log(IsURL(item.url), '---url')
      if (valid.test(item.url)) {
        // if (this.IsURL(item.url)) {
        const info = {
          name: item.name,
          state: item.state
        } as any
        // 双加密
        const path = encodeURIComponent(item.url)
        info.url = item.routerUrl + btoa(path)
        data[item.key] = info
        const router = {
          path: info.url,
          name: item.key,
          component: () => import('@/views/extendPage/sysPage.vue'),
          meta: { title: item.name, icon: 'icon-daping' }
        }
        newRouter.push(router as never)
      } else {
        const index = i + 1
        const text = '开启的 页面' + index + ' url地址格式不正确'
        SLMessage.error(text)
        return
      }
    } else {
      const info = {
        name: item.name,
        state: item.state,
        url: item.url
      }
      data[item.key] = info
    }
  }
  const params = {
    type: currentSYS.value ? 'sysBigScreen' : 'tenantBigScreen',
    key: currentSYS.value ? 'sys_admin' : tenantId.value,
    value: JSON.stringify(data)
  }
  saveConstants([params]).then(() => {
    const routerInfo = {
      router: newRouter,
      currentSYS: currentSYS.value
    }
    usePermissionStore().setBigScreen(routerInfo)
    SLMessage.success('保存成功 稍后生效')
    location.reload()
    // setTimeout(() => {
    //   this.reload()
    // }, 600)
    console.log(newRouter, '----newRouter')
  })
}

const getBigScreen = () => {
  const BigScreen = currentSYS.value
    ? sysBigScreen.value
    : TenantBigScreen.value
  const params = {
    type: currentSYS.value ? 'sysBigScreen' : 'tenantBigScreen',
    key: currentSYS.value ? 'sys_admin' : tenantId.value
  }
  getConstantsAttributeById(params).then(res => {
    if (!res.data?.length) return
    const rData = JSON.parse(res.data[0].value)
    for (const item of BigScreen) {
      const info = rData[item.key]
      item.name = info.name
      item.state = info.state
      if (info.state) {
        // 双解码
        const url = info.url.split('/extendPage/sysPage/')[1]
        item.url = decodeURIComponent(atob(url))
      } else {
        item.url = info.url
      }
    }
    configUrl.value = BigScreen
    console.log(BigScreen)
  })
}
onMounted(() => {
  configUrl.value = currentSYS.value
    ? sysBigScreen.value
    : TenantBigScreen.value
  getBigScreen()
})
</script>
<style lang="scss" scoped>
.p-title-tips {
  color: rgb(36, 148, 74);
  font-size: 14px;
}
.big-container-data {
  height: 400px;
  overflow-y: auto;
  .data-row {
    width: 100%;
    height: 62px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    .row-label {
      margin: 0 10px 0 50px;
    }
    .row-name-input {
      width: 160px;
    }
    .row-url-input {
      width: 500px;
      margin-right: 50px;
    }
  }
  .icon-yemian {
    color: #2882c6;
  }
}
</style>
