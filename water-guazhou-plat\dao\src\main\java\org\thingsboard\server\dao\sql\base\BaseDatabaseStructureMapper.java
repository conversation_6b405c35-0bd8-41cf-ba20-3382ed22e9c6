package org.thingsboard.server.dao.sql.base;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.base.BaseDatabaseStructure;
import org.thingsboard.server.dao.util.imodel.query.base.BaseDatabaseStructurePageRequest;

import java.util.List;

/**
 * 公共管理-数据库结构修复Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
@Mapper
public interface BaseDatabaseStructureMapper {
    /**
     * 查询公共管理-数据库结构修复
     *
     * @param id 公共管理-数据库结构修复主键
     * @return 公共管理-数据库结构修复
     */
    public BaseDatabaseStructure selectBaseDatabaseStructureById(String id);

    /**
     * 查询公共管理-数据库结构修复列表
     *
     * @param baseDatabaseStructure 公共管理-数据库结构修复
     * @return 公共管理-数据库结构修复集合
     */
    public IPage<BaseDatabaseStructure> selectBaseDatabaseStructureList(BaseDatabaseStructurePageRequest baseDatabaseStructure);

    /**
     * 新增公共管理-数据库结构修复
     *
     * @param baseDatabaseStructure 公共管理-数据库结构修复
     * @return 结果
     */
    public int insertBaseDatabaseStructure(BaseDatabaseStructure baseDatabaseStructure);

    /**
     * 修改公共管理-数据库结构修复
     *
     * @param baseDatabaseStructure 公共管理-数据库结构修复
     * @return 结果
     */
    public int updateBaseDatabaseStructure(BaseDatabaseStructure baseDatabaseStructure);

    /**
     * 删除公共管理-数据库结构修复
     *
     * @param id 公共管理-数据库结构修复主键
     * @return 结果
     */
    public int deleteBaseDatabaseStructureById(String id);

    /**
     * 批量删除公共管理-数据库结构修复
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBaseDatabaseStructureByIds(@Param("array") List<String> ids);
}
