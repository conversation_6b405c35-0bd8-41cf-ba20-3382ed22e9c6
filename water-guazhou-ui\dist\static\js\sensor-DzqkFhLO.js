import{d as b,c as C,r as F,o as V,am as D,j as U,g as M,n as k,q as E,i as g,t as j,_ as B}from"./index-r0dFAfgr.js";import{C as R}from"./index-CcDafpIP.js";import{r as f,o as y}from"./chart-wy3NEK2T.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import{a as c}from"./LayerHelper-Cn-iiqxI.js";import{b as J,i as x}from"./QueryHelper-ILO3qZqg.js";import{P as h}from"./pipe-nogVzCHG.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./project-DUuzYgGl.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./executeForIds-BLdIsxvI.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";const ie=b({__name:"sensor",props:{view:{},menu:{}},emits:["highlightMark","addMarks"],setup(A,{emit:I}){const v=I,r=A,T=C(),u=C([{label:"0 个",value:"设备总数"},{label:"0 个",value:"通信正常"},{label:"0 个",value:"通信异常"}]),m=F({group:[{id:"DEVICETYPE",fieldset:{type:"underline",desc:"设备占比图"},fields:[{type:"vchart",option:f(),style:{width:"100%",height:"150px"},itemContainerStyle:{marginBottom:0},handleClick:t=>{v("highlightMark",r.menu,t.data.name==="未知"?"":" DEVICETYPE="+(t.data.name||"")+" ",t.data.nameAlias)}}]},{id:"STATUS",fieldset:{type:"underline",desc:"通信情况占比"},fields:[{type:"vchart",option:f(),style:{width:"100%",height:"150px"},itemContainerStyle:{marginBottom:0},handleClick:t=>{v("highlightMark",r.menu,t.data.name==="未知"?"":" STATUS="+(t.data.name||"")+" ",t.data.nameAlias)}}]},{id:"DEVICETYPEBAR",fieldset:{type:"underline",desc:"设备数量占比图"},fields:[{type:"vchart",option:y(),style:{width:"100%",height:"150px"},itemContainerStyle:{marginBottom:0}}]},{id:"MANUALFACTORY",fieldset:{type:"underline",desc:"按流量计厂家统计传感器数量"},fields:[{type:"vchart",option:y(),style:{width:"100%",height:"150px"}}]}],labelPosition:"top",gutter:12}),O=async t=>{if(!r.view)return;const i=c(r.view,void 0,void 0,"传感器");return i.length?await J(window.SITE_CONFIG.GIS_CONFIG.gisService+window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService+"/"+i[0],x({returnGeometry:!1,where:""})):void 0},_=async()=>{var o,s,a;if(!r.view)return;const t=(o=m.group.find(e=>e.id==="DEVICETYPE"))==null?void 0:o.fields,i=(t==null?void 0:t.length)&&t[0],n=c(r.view,void 0,void 0,"传感器"),p=(a=(s=(await h({layerids:JSON.stringify(n),group_fields:JSON.stringify(["DEVICETYPE"]),statistic_field:"ObjectId",statistic_type:"1",where:""})).data.result.rows[0])==null?void 0:s.rows)==null?void 0:a.map(e=>({name:e.DEVICETYPE||"未知",nameAlias:e.DEVICETYPE||"未知",value:e.ObjectId||0}));i&&(i.option=f(p,"个","",0))},N=async()=>{var o,s,a,e,S;if(!r.view)return;const t=(o=m.group.find(l=>l.id==="STATUS"))==null?void 0:o.fields,i=(t==null?void 0:t.length)&&t[0],n=c(r.view,void 0,void 0,"传感器"),p=(S=(e=(a=(s=(await h({layerids:JSON.stringify(n),group_fields:JSON.stringify(["STATUS"]),statistic_field:"ObjectId",statistic_type:"1",where:""})).data)==null?void 0:s.result)==null?void 0:a.rows[0])==null?void 0:e.rows)==null?void 0:S.map(l=>({name:l.STATUS||"未知",nameAlias:l.STATUS||"未知",value:l.ObjectId||0}));i&&(i.option=f(p,"个","",0))},Y=async()=>{var o,s,a;if(!r.view)return;const t=(o=m.group.find(e=>e.id==="DEVICETYPEBAR"))==null?void 0:o.fields,i=(t==null?void 0:t.length)&&t[0],n=c(r.view,void 0,void 0,"传感器"),p=(a=(s=(await h({layerids:JSON.stringify(n),group_fields:JSON.stringify(["DEVICETYPE"]),statistic_field:"ObjectId",statistic_type:"1",where:""})).data.result.rows[0])==null?void 0:s.rows)==null?void 0:a.map(e=>({name:e.DEVICETYPE||"未知",nameAlias:e.DEVICETYPE||"未知",value:e.ObjectId||0}));i&&(i.option=y(p,"个"))},P=async()=>{var o,s,a;if(!r.view)return;const t=(o=m.group.find(e=>e.id==="MANUALFACTORY"))==null?void 0:o.fields,i=(t==null?void 0:t.length)&&t[0],n=c(r.view,void 0,void 0,"传感器"),p=(a=(s=(await h({layerids:JSON.stringify(n),group_fields:JSON.stringify(["MANUALFACTORY"]),statistic_field:"ObjectId",statistic_type:"1",where:""})).data.result.rows[0])==null?void 0:s.rows)==null?void 0:a.map(e=>({name:e.MANUALFACTORY||"未知",nameAlias:e.MANUALFACTORY||"未知",value:e.ObjectId||0}));i&&(i.option=y(p,"个"))},w=async()=>{N(),_(),Y(),P();const t=await O();u.value[0].label=(t||"0")+" 个"};return V(()=>{w()}),D(()=>U().isDark,()=>w()),(t,i)=>{const n=B;return M(),k("div",null,[E(g(R),{modelValue:g(u),"onUpdate:modelValue":i[0]||(i[0]=d=>j(u)?u.value=d:null)},null,8,["modelValue"]),E(n,{ref_key:"refForm",ref:T,config:g(m)},null,8,["config"])])}}});export{ie as default};
