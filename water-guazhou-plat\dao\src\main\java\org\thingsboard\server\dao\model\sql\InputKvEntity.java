/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.model.sql;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.thingsboard.server.common.data.inputKv.InputKv;
import org.thingsboard.server.dao.model.BaseSqlEntity;
import org.thingsboard.server.dao.model.SearchTextEntity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import static org.thingsboard.server.dao.model.ModelConstants.*;

@Data
@Entity
@Table(name = "input_kv")
public class InputKvEntity extends BaseSqlEntity<InputKv> implements SearchTextEntity<InputKv> {


    @Id
    @Column(name = ENTITY_ID_COLUMN)
    private String entityId;

    @Id
    @Column(name = ENTITY_TYPE_COLUMN)
    private String entityType;

    @Id
    @Column(name = KEY_COLUMN)
    private String key;

    @Id
    @Column(name = TS_COLUMN)
    private long ts;

    @Id
    @Column(name = VALUE_COLUMN)
    private String value;

    public InputKvEntity() {

    }

    public InputKvEntity(InputKv inputKv) {
        this.entityType = inputKv.getEntityType();
        this.entityId = inputKv.getEntityId();
        this.key = inputKv.getKey();
        this.ts = inputKv.getTs();
        this.value = inputKv.getValue();
    }


    @Override
    public InputKv toData() {
        InputKv inputKv = new InputKv();
        inputKv.setEntityType(entityType);
        inputKv.setEntityId(entityId);
        inputKv.setKey(key);
        inputKv.setTs(ts);
        inputKv.setValue(value);
        return inputKv;
    }

    @Override
    public String getSearchTextSource() {
        return key;
    }

    @Override
    public void setSearchText(String searchText) {
        this.key=searchText;
    }
}
