<template>
  <div>
    <div class="item">
      <detailSteps :config="detailStep"></detailSteps>
    </div>
    <div class="item">
      <el-tabs
        v-model="activeName"
        class="demo-tabs"
        @tab-click="handleClick"
      >
        <el-tab-pane
          label="基础信息"
          name="first"
        >
          <el-descriptions title="">
            <el-descriptions-item label="工单编号：">
              {{ state.detail.serialNo }}
            </el-descriptions-item>
            <el-descriptions-item label="标题：">
              {{ state.detail.title }}
            </el-descriptions-item>
            <el-descriptions-item label="来源：">
              {{ state.detail.source }}
            </el-descriptions-item>
            <el-descriptions-item label="紧急程度：">
              <el-tag
                :type="
                  state.detail.level === '紧急'
                    ? 'warning'
                    : state.detail.level === '非常紧急'
                      ? 'danger'
                      : ''
                "
              >
                {{ state.detail.level }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="类型：">
              {{ state.detail.type }}
            </el-descriptions-item>
            <el-descriptions-item label="发起人：">
              {{ state.detail.organizerName }}
            </el-descriptions-item>
            <el-descriptions-item label="业务单号：">
              {{ state.detail.serialNo }}
            </el-descriptions-item>
            <el-descriptions-item label="地址：">
              {{ state.detail.address }}
            </el-descriptions-item>
          </el-descriptions>
        </el-tab-pane>
        <el-tab-pane
          label="处理信息"
          name="second"
        >
          <el-descriptions title="">
            <el-descriptions-item label="处理人：">
              <el-icon v-if="state.detail.processUserName">
                <User />
              </el-icon>
              {{ state.detail.processUserName }}
            </el-descriptions-item>
            <el-descriptions-item label="处理级别：">
              {{
                state.detail.processLevel &&
                  state.detail.processLevel + '级别'
              }}
            </el-descriptions-item>
            <el-descriptions-item label="预计完成时间：">
              {{ state.detail.estimatedFinishTime }}
            </el-descriptions-item>
            <el-descriptions-item label="完成时间：">
              {{ state.detail.completeTime }}
            </el-descriptions-item>
          </el-descriptions>
        </el-tab-pane>
      </el-tabs>
    </div>
    <div class="item">
      <el-timeline>
        <el-timeline-item
          v-for="(item, i) in state.timeline"
          :key="i"
          :hollow="true"
          type="primary"
          :hide-timestamp="true"
          placement="top"
        >
          <div class="timeline-item-wrapper">
            <div class="timeline-item-title">
              <span class="title">{{ item.data.typeName }}</span>
              <span class="time">{{ item.data.processTime }}</span>
            </div>
            <SLCard class="timeline-item-content">
              <template v-if="item.type === 'text'">
                <p
                  v-if="item.data.type === 'ASSIGN'"
                  class="text"
                >
                  任务派发给了
                  <span class="text-name">{{
                    item.data?.nextProcessUserName
                  }}</span>， 操作人：
                  <span class="text-name">{{
                    item.data?.processUserName
                  }}</span>
                </p>
                <p
                  v-if="item.data.type === 'RESOLVING'"
                  class="text"
                >
                  {{ item.data?.nextProcessUserName }} 接收了工单
                </p>
              </template>

              <AttrTable
                v-if="item?.type === 'attr-table'"
                :data="item.data"
                :columns="item.columns"
              ></AttrTable>
            </SLCard>
          </div>
        </el-timeline-item>
      </el-timeline>
    </div>
  </div>
</template>

<script lang="ts" setup>
import type { TabsPaneContext } from 'element-plus'

import { reactive } from 'vue'
import { GetWorkOrderDetail, GetWorkOrderStages } from '@/api/workorder'
import { SLMessage } from '@/utils/Message'
import { fileStrToArr } from '@/utils/GlobalHelper'
import { initWorkOrderDetailStepInfo } from '../config'
import detailSteps from './detailSteps.vue'

const activeName = ref('first')

const props = defineProps<{
  id:string
}>()

const handleClick = (tab: TabsPaneContext, event: Event) => {
  console.log(tab, event)
}

const state = reactive<{
  detail: Record<string, any>
  timeline: {
    // type?: string
    // typeName?: string
    // processUserId?: string
    // nextProcessUserId?: string
    // processRemark?: string
    // processTime?: string
    // processAdditionalInfo?: any
    type: 'attr-table' | 'text'
    data: any
    columns?: any[]
  }[]
}>({
  detail: {},
  timeline: []
})

const detailStep = reactive({
  status: '',
  statusName: ''
})

const refreshData = async () => {
  const id = props.id || ''
  if (!id) return SLMessage.error('系统错误')
  state.timeline = []
  const res = await GetWorkOrderDetail(id)
  state.detail = res.data?.data || {}
  detailStep.status = state.detail.status
  const timeLineData = await GetWorkOrderStages(id)
  state.timeline.push({
    type: 'attr-table',
    data: {
      typeName: '发起',
      processUserId: state.detail.organizerId,
      processRemark: '发起工单',
      nextProcessUserId: '',
      processTime: res.data?.data?.createTime,
      ...state.detail
    },
    columns: initWorkOrderDetailStepInfo('CREATE')
  })
  // 对发起特殊处理
  // if (('imgUrl' in state.detail) || state.detail?.imgUrl === '') {
  //   serch(state.timeline[0]?.columns, 'imgUrl')
  // }
  // if (('audioUrl' in state.detail) || state.detail?.audioUrl === '') {
  //   serch(state.timeline[0]?.columns, 'audioUrl')
  // }
  // if (('videoUrl' in state.detail) || state.detail?.videoUrl === '') {
  //   serch(state.timeline[0]?.columns, 'videoUrl')
  // }
  // if (('otherFileUrl' in state.detail) || state.detail?.otherFileUrl === '') {
  //   serch(state.timeline[0]?.columns, 'otherFileUrl')
  // }

  const TableItems = [
    'CREATE',
    'ARRIVING',
    'PROCESSING',
    'SUBMIT',
    'CHARGEBACK_REVIEW',
    'REVIEW',
    'APPROVED',
    'REJECTED',
    'CHARGEBACK',
    'TERMINATED',
    'HANDOVER_REVIEW',
    'REASSIGN'
  ]
  timeLineData.data?.data.map(item => {
    const addt = (item.processAdditionalInfo && JSON.parse(item.processAdditionalInfo))
      || {}
    addt?.audioUrl && (addt.audioUrl = fileStrToArr(addt.audioUrl))
    addt?.videoUrl && (addt.videoUrl = fileStrToArr(addt.videoUrl))
    addt?.otherFileUrl && (addt.otherFileUrl = fileStrToArr(addt.otherFileUrl))
    const isTable = TableItems.includes(item.type)
    if (addt.nextProcessUserId) {
      delete addt.nextProcessUserId
    }
    state.timeline.unshift({
      type: isTable ? 'attr-table' : 'text',
      data: {
        ...item,
        ...addt
      },
      columns: initWorkOrderDetailStepInfo(item.type)
    })
    console.log(state.timeline)
  })
}

onMounted(() => {
  refreshData()
})

watch(props, () => {
  refreshData()
})
</script>

<style lang="scss" scoped>
.item {
  padding: 20px;
  background: var(--el-bg-color);
  border-radius: 8px;
  margin-bottom: 10px;
}

.el-timeline{
  margin-top: 10px;
}

.timeline-item-wrapper {
  padding-bottom: 8px;

  .timeline-item-title {
    margin-bottom: 12px;

    .title {
      color: #318DFF;
      font-weight: bold;
      font-size: 14px;
      margin-right: 8px;
    }
  }

  .timeline-item-content {
    padding: 20px;
  }
}

.text {
  font-size: 12px;

}

</style>
