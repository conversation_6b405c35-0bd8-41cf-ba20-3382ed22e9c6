<template>
  <DialogForm ref="refDialog" :config="DialogFormConfig">
    <div class="add-more">
      <Button
        class="add-more-btn"
        :config="{
          perm: true,
          text: '添加过滤条件',
          iconifyIcon: 'ep:plus',
          isTextBtn: true,
          click: () => addTableRow()
        }"
      ></Button>
    </div>
    <div class="form-list">
      <div v-for="(row, i) in TableConfig.dataList" :key="i" class="form-row">
        <div v-if="i > 0" class="logic">
          <label class="label"> 逻辑 </label>
          <FormItem
            v-model="row['logic']"
            :config="{ type: 'radio', options: state.logicOption }"
          ></FormItem>
        </div>
        <div class="form-items">
          <FormItem
            v-model="row['field']"
            :config="{
              type: 'select',
              placeholder: '请选择字段',
              options: state.fieldConfig
            }"
          ></FormItem>
          <FormItem
            v-model="row['calc']"
            :config="{
              type: 'select',
              placeholder: '请选择计算方式',
              options: state.calcOptions
            }"
          ></FormItem>
          <FormItem
            v-model="row['value']"
            :config="{
              type: 'input',
              placeholder: '请输入内容',
              disabled: () =>
                [ECalcOperator.BUWEIKONG, ECalcOperator.WEIKONG].includes(
                  row['calc']
                )
            }"
          ></FormItem>
          <div class="button" @click="() => removeRow(row, i)">x</div>
        </div>
      </div>
    </div>
  </DialogForm>
</template>
<script lang="ts" setup>
import {
  ECalcOperator,
  EGisConditionLogic,
  OCalcOperatorVal,
  OGisConditionLogicVal
} from '@/utils/arcgis/utils/SqlHelper';

type IGISGeneratorRow = {
  logic: string;
  field: string;
  calc: string;
  value: string;
};
interface ISQLGeneratorSubmit {
  (e: 'submit', data: { list: IGISGeneratorRow[]; sql: string }): any;
}
const emit = defineEmits<ISQLGeneratorSubmit>();
const refDialog = ref<IDialogFormIns>();
const state = reactive<{
  logicOption: any[];
  fieldConfig: any[];
  calcOptions: NormalOption[];
}>({
  logicOption: [
    { label: '或', value: EGisConditionLogic.OR },
    { label: '且', value: EGisConditionLogic.AND }
  ],
  fieldConfig: [
    { label: 'OBJECTID', value: 'OBJECTID' },
    { label: '地面高程', value: 'ELEVATION' },
    { label: '埋深', value: 'DEPTH' },
    { label: '起点埋深', value: 'START_DEPTH' },
    { label: '止点埋深', value: 'END_DEPTH' },
    { label: '口径', value: 'DIAMETER' },
    { label: '材质', value: 'MATERIAL' },
    { label: '所在道路', value: 'LANEWAY' },
    { label: '横坐标', value: 'X' },
    { label: '纵坐标', value: 'Y' },
    { label: '设备编号', value: 'SID' },
    { label: '竣工日期', value: 'FINISHDATE' }
  ],
  calcOptions: [
    { label: '等于', value: ECalcOperator.DENGYU },
    { label: '不等于', value: ECalcOperator.BUDENGYU },
    { label: '大于', value: ECalcOperator.DAYU },
    { label: '大于等于', value: ECalcOperator.DAYUDENGYU },
    { label: '小于等于', value: ECalcOperator.XIAOYUDENGYU },
    { label: '小于', value: ECalcOperator.XIAOYU },
    { label: '为空', value: ECalcOperator.WEIKONG },
    { label: '不为空', value: ECalcOperator.BUWEIKONG },
    { label: '包含', value: ECalcOperator.BAOHAN },
    { label: '不包含', value: ECalcOperator.BUBAOHAN }
  ]
});
const TableConfig = reactive<ITable>({
  dataList: [],
  height: 'none',
  columns: [
    {
      label: '字段',
      prop: 'field',
      formItemConfig: {
        type: 'select',
        options: state.fieldConfig
      }
    },
    {
      label: '运行符',
      prop: 'calc',
      formItemConfig: {
        type: 'select',
        options: state.calcOptions
      }
    },
    {
      label: '值',
      prop: 'value',
      formItemConfig: {
        type: 'input'
      }
    }
  ],
  pagination: {
    hide: true
  },
  operations: [
    { perm: true, click: (row, index) => removeRow(row, index), text: 'x' }
  ]
});
const addTableRow = () => {
  const logic = TableConfig.dataList.length > 0 ? '1' : '';
  TableConfig.dataList.push({
    logic,
    field: 'OBJECTID',
    calc: ECalcOperator.DENGYU,
    value: ''
  });
};
const removeRow = (row, index) => {
  TableConfig.dataList.splice(index, 1);
};
const genSql = () => {
  let sql = '';
  TableConfig.dataList.map((item: IGISGeneratorRow, i: number) => {
    if (item.logic && i > 0) {
      sql += ` ${OGisConditionLogicVal[item.logic]} `;
    }
    switch (item.calc) {
      case ECalcOperator.DENGYU:
      case ECalcOperator.BUDENGYU:
      case ECalcOperator.DAYU:
      case ECalcOperator.DAYUDENGYU:
      case ECalcOperator.XIAOYUDENGYU:
      case ECalcOperator.XIAOYU:
        sql += `${item.field} ${OCalcOperatorVal[item.calc]} '${item.value ?? ''}'`;
        break;
      case ECalcOperator.WEIKONG:
      case ECalcOperator.BUWEIKONG:
        sql += `${item.field} ${OCalcOperatorVal[item.calc]}`;
        break;
      case ECalcOperator.BAOHAN:
      case ECalcOperator.BUBAOHAN:
        sql += `${item.field} ${OCalcOperatorVal[item.calc]} '%${item.value ?? ''}%'`;
        break;
      default:
        break;
    }
  });
  return sql;
};
const DialogFormConfig = reactive<IDialogFormConfig>({
  dialogWidth: 800,
  title: '属性过滤',
  group: [],
  submit: () => {
    emit('submit', { list: TableConfig.dataList, sql: genSql() });
    refDialog.value?.closeDialog();
  }
});

const clear = () => {
  TableConfig.dataList = [];
};
const openDialog = () => {
  refDialog.value?.openDialog();
};
const closeDialog = () => {
  refDialog.value?.closeDialog();
};
const Submit = () => {
  emit('submit', { list: TableConfig.dataList, sql: genSql() });
};
defineExpose({
  openDialog,
  closeDialog,
  genSql,
  refDialog,
  TableConfig,
  clear,
  Submit
});
</script>
<style lang="scss" scoped>
.sql-generator {
  width: 100%;
}
.add-more {
  width: 100%;
  display: flex;
  .add-more-btn {
    margin-left: auto;
  }
}
.form-list {
  .form-row {
    .logic {
      display: flex;
      width: 100%;
      align-items: center;
      .label {
        word-break: keep-all;
        padding-right: 12px;
      }
    }
    .form-items {
      display: flex;
      .button {
        cursor: pointer;
        width: 80px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
}
</style>
