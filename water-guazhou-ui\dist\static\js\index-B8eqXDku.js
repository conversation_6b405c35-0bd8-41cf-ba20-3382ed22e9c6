import{m as G,d as de,dM as W,c as V,r as oe,o as pe,bv as me,x as y,y as Y,C as ye,g,n as U,p as h,q as r,F as s,G as i,bo as H,h as b,an as T,bh as P,dz as he,dA as fe,bb as ge,H as ce,I as Be,J as ve,K as Ce,N as Ae,c6 as be,O as Fe,P as De,L as Te,br as Ue}from"./index-r0dFAfgr.js";import{a as Ee}from"./index-CvT4_zhs.js";function ke(t){return G({url:"/api/pipelineDevice/list",method:"get",params:{...{page:1,size:10,deviceType:"",deviceName:"",deviceCode:"",location:"",status:""},...t}})}function Z(t){return G({url:`/api/pipelineDeviceAuth/device/${t}`,method:"get"})}function N(t){return G({url:"/api/pipelineDeviceAuth/batch",method:"post",data:t})}function we(){return G({url:"/api/userGroup/tree",method:"get"})}const Le={pressure:"压力计",flow:"流量计",quality:"水质检测仪"},Ie=async t=>{try{return await ke(t)}catch{return console.warn("管网设备接口不可用，使用模拟数据"),{data:{data:{data:[{id:"1",name:"管网压力计-001",code:"YLJ001",type:"pressure",location:"水厂出水口",status:1,userAuthList:[]},{id:"2",name:"管网流量计-001",code:"LLJ001",type:"flow",location:"主干管道A段",status:1,userAuthList:[]},{id:"3",name:"水质检测仪-001",code:"SZJC001",type:"quality",location:"用户端监测点",status:1,userAuthList:[]}],total:3}}}}},Ve=async()=>{try{return await we()}catch{return console.warn("用户分组接口不可用，使用模拟数据"),{data:[{id:"group1",name:"系统管理员组",children:[]},{id:"group2",name:"设备操作员组",children:[]},{id:"group3",name:"数据查看员组",children:[]}]}}},Ne=de({name:"PipelineDeviceAuth",components:{ChooseUserByRole:W},setup(){const t=V(),e=V(),J=V(),Q=V(),j=V(),u=oe({selectedDeviceType:"",userGroupTreeData:[],userGroupTreeProps:{label:"name",children:"children"},selectedUserGroups:[],queryParams:{page:1,size:10,deviceName:"",deviceCode:"",deviceType:"",userGroupIds:[]},loading:!1,deviceList:[],total:0,selectedDevices:[],currentDevice:null,userBindDialogVisible:!1,userBindForm:{userIds:[],authType:0},userOptions:[],selectedUsers:[],addSelectedUsers:[],batchSelectedUsers:[],authManageDialogVisible:!1,currentDeviceUserAuthList:[],addUserDialogVisible:!1,addUserForm:{userId:"",userName:"",authType:0},batchBindDialogVisible:!1,batchBindForm:{userIds:[],authType:0}}),E=a=>Le[a]||a,q=a=>({pressure:"danger",flow:"primary",quality:"success"})[a]||"info",R=async()=>{try{const a=await Ve();u.userGroupTreeData=a.data||[]}catch(a){console.error("获取用户分组树失败",a),y.error("获取用户分组树失败")}},B=async()=>{var a,n;u.loading=!0;try{const d={...u.queryParams};u.selectedDeviceType&&(d.deviceType=u.selectedDeviceType),u.selectedUserGroups.length>0&&(d.userGroupIds=u.selectedUserGroups);const m=await Ie(d);let p=[];(n=(a=m.data)==null?void 0:a.data)!=null&&n.data?(p=m.data.data.data||[],u.total=m.data.data.total||0):Array.isArray(m.data)&&(p=m.data,u.total=m.data.length||0),(d.deviceName||d.deviceCode)&&(p=p.filter(v=>{const F=!d.deviceName||v.name&&v.name.includes(d.deviceName),D=!d.deviceCode||v.code&&v.code.includes(d.deviceCode);return F&&D}),u.total=p.length),u.deviceList=p.map(v=>({...v,userAuthList:[],loadingUserAuth:!1,userAuthLoaded:!1}))}catch(d){console.error("获取设备列表失败",d),y.error("获取设备列表失败")}finally{u.loading=!1}},A=async a=>{if(!a.userAuthLoaded){a.loadingUserAuth=!0;try{const n=await Z(a.id);let d=[];n.data&&(Array.isArray(n.data)?d=n.data:n.data.data&&Array.isArray(n.data.data)&&(d=n.data.data));const m=d.map(p=>{const v=typeof p.authType=="number"?p.authType:parseInt(p.authType)||0;let F=p.userName||"";if(!F&&p.userId){const D=u.userOptions.find(O=>String(O.id||"")===String(p.userId||""));D&&(F=D.firstName||D.email||"未命名用户")}return{userId:p.userId,userName:F||"未命名用户",userGroupName:p.userGroupName||"未分组",authType:v}});a.userAuthList=m,a.userAuthLoaded=!0}catch(n){console.error("获取设备用户权限列表失败",n),a.userAuthList=[]}finally{a.loadingUserAuth=!1}}},o=async()=>{try{const a=await Ee({});u.userOptions=a.data||[]}catch(a){console.error("获取用户列表失败",a),y.error("获取用户列表失败")}},k=()=>{u.queryParams.page=1,B()},f=(a,n)=>{u.selectedUserGroups=n.checkedKeys,u.queryParams.page=1,B()},C=()=>{u.queryParams.page=1,B()},L=()=>{var a;(a=t.value)==null||a.resetFields(),u.queryParams.deviceName="",u.queryParams.deviceCode="",C()},M=a=>{u.queryParams.size=a,B()},I=a=>{u.queryParams.page=a,B()},w=a=>{u.selectedDevices=a},S=(a,n)=>{n.length>0&&n.includes(a)&&A(a)},z=a=>{u.currentDevice=a,u.userBindForm={userIds:[],authType:0},u.selectedUsers=[],u.userBindDialogVisible=!0},$=()=>{u.batchBindForm={userIds:[],authType:0},u.batchSelectedUsers=[],u.batchBindDialogVisible=!0},l=()=>{Y.confirm(`确认要移除选中的 ${u.selectedDevices.length} 个设备的所有用户权限吗？`,"批量移除权限",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{for(const a of u.selectedDevices)await N({deviceId:a.id,deviceName:a.name,deviceSerial:a.code,userAuthList:[]});y.success("批量移除权限成功"),B()}catch(a){console.error("批量移除权限失败",a),y.error("批量移除权限失败")}}).catch(()=>{})},c=a=>{u.userBindForm.userIds=a.map(n=>n.id),u.selectedUsers=a.map(n=>({id:n.id,name:n.firstName||n.email||"未命名用户"}))},K=a=>{u.batchBindForm.userIds=a.map(n=>n.id),u.batchSelectedUsers=a.map(n=>({id:n.id,name:n.firstName||n.email||"未命名用户"}))},X=a=>{if(a&&a.length>0){const n=a[0];u.addUserForm.userId=n.id,u.addUserForm.userName=n.firstName||n.email||"未命名用户",u.addSelectedUsers=[{id:n.id,name:n.firstName||n.email||"未命名用户"}]}},_=(a,n)=>{u[n].authType===a?u[n].authType=0:u[n].authType=a,u[n]={...u[n]}},x=(a,n)=>{a.authType===n?a.authType=0:a.authType=n,u.currentDeviceUserAuthList=[...u.currentDeviceUserAuthList]},ee=async()=>{if(!u.userBindForm.userIds||u.userBindForm.userIds.length===0){y.warning("请选择用户");return}if(!u.userBindForm.authType||u.userBindForm.authType===0){y.warning("请选择权限类型");return}try{const a=u.userBindForm.userIds.map(n=>{const m=u.selectedUsers.find(p=>p.id===n)||u.userOptions.find(p=>p.id===n);return{userId:n,userName:m&&(m.name||m.firstName||m.email)||"未命名用户",authType:u.userBindForm.authType}});if(u.currentDevice&&u.currentDevice.userAuthList)for(const n of u.currentDevice.userAuthList)a.some(d=>d.userId===n.userId)||a.push(n);await N({deviceId:u.currentDevice.id,deviceName:u.currentDevice.name,deviceSerial:u.currentDevice.code,userAuthList:a}),y.success("用户绑定成功"),u.userBindDialogVisible=!1,B()}catch(a){console.error("用户绑定失败",a),y.error("用户绑定失败")}},te=async()=>{if(!u.batchBindForm.userIds||u.batchBindForm.userIds.length===0){y.warning("请选择用户");return}if(!u.batchBindForm.authType||u.batchBindForm.authType===0){y.warning("请选择权限类型");return}try{for(const a of u.selectedDevices){const n=u.batchBindForm.userIds.map(d=>{const p=u.batchSelectedUsers.find(v=>v.id===d)||u.userOptions.find(v=>v.id===d);return{userId:d,userName:p&&(p.name||p.firstName||p.email)||"未命名用户",authType:u.batchBindForm.authType}});if(a.userAuthList)for(const d of a.userAuthList)n.some(m=>m.userId===d.userId)||n.push(d);await N({deviceId:a.id,deviceName:a.name,deviceSerial:a.code,userAuthList:n})}y.success("批量绑定用户成功"),u.batchBindDialogVisible=!1,B()}catch(a){console.error("批量绑定失败",a),y.error("批量绑定失败")}},ue=async a=>{u.currentDevice=a,u.userOptions.length===0&&await o();try{const n=await Z(a.id);let d=[];n.data&&(Array.isArray(n.data)?d=n.data:n.data.data&&Array.isArray(n.data.data)&&(d=n.data.data));const m=d.map(p=>{const v=typeof p.authType=="number"?p.authType:parseInt(p.authType)||0;let F=p.userName||"";if(!F&&p.userId){const D=u.userOptions.find(O=>String(O.id||"")===String(p.userId||""));D&&(F=D.firstName||D.email||"未命名用户")}return{userId:p.userId,userName:F||"未命名用户",userGroupName:p.userGroupName||"未分组",authType:v}});u.currentDeviceUserAuthList=m,a.userAuthList=[...m],a.userAuthLoaded=!0}catch(n){console.error("获取设备用户权限列表失败",n),u.currentDeviceUserAuthList=[],y.error("获取设备用户权限列表失败")}u.authManageDialogVisible=!0},ae=()=>{u.addUserForm={userId:"",userName:"",authType:0},u.addSelectedUsers=[],u.addUserDialogVisible=!0},re=()=>{if(!u.addUserForm.userId){y.warning("请选择用户");return}if(!u.addUserForm.authType||u.addUserForm.authType===0){y.warning("请选择权限类型");return}const n=(u.addSelectedUsers.length>0?u.addSelectedUsers[0]:null)||u.userOptions.find(d=>d.id===u.addUserForm.userId);u.currentDeviceUserAuthList.push({userId:u.addUserForm.userId,userName:n&&(n.name||n.firstName||n.email||u.addUserForm.userName)||"未命名用户",userGroupName:"未分组",authType:u.addUserForm.authType}),u.addUserDialogVisible=!1},se=a=>{u.currentDeviceUserAuthList.splice(a,1)},ne=async()=>{const a=u.currentDeviceUserAuthList.filter(n=>!n.authType||n.authType===0);if(a.length>0){y.warning(`有${a.length}个用户未选择权限类型，请为所有用户选择权限类型`);return}try{await N({deviceId:u.currentDevice.id,deviceName:u.currentDevice.name,deviceSerial:u.currentDevice.code,userAuthList:u.currentDeviceUserAuthList}),y.success("权限管理成功"),u.authManageDialogVisible=!1,B()}catch(n){console.error("权限管理失败",n),y.error("权限管理失败")}},le=(a,n)=>{Y.confirm("确认删除该用户权限吗？","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{const d=a.userAuthList.filter(m=>m.userId!==n.userId);await N({deviceId:a.id,deviceName:a.name,deviceSerial:a.code,userAuthList:d}),y.success("删除成功"),B()}catch(d){console.error("删除失败",d),y.error("删除失败")}}).catch(()=>{})},ie=(a,n)=>a.userAuthList?a.userAuthList.some(d=>d.authType===n):!1;return pe(()=>{R(),o(),B()}),{queryFormRef:t,userBindFormRef:e,addUserFormRef:J,batchBindFormRef:Q,userGroupTree:j,...me(u),getDeviceTypeName:E,getDeviceTypeTagType:q,handleDeviceTypeChange:k,handleUserGroupCheck:f,handleQuery:C,resetQuery:L,handleSizeChange:M,handleCurrentChange:I,handleSelectionChange:w,handleExpandChange:S,handleUserBind:z,handleBatchUserBind:$,handleBatchRemoveAuth:l,handleUserSelect:c,handleBatchUserSelect:K,handleAddUserSelect:X,handleAuthTypeChange:_,handleRowAuthTypeChange:x,submitUserBind:ee,submitBatchBind:te,handleAuthManage:ue,handleAddUserAuth:ae,submitAddUser:re,handleRemoveUserAuth:se,submitAuthManage:ne,handleDeleteUserAuth:le,hasAuthType:ie}}}),Se={class:"app-container"},ze={class:"pipeline-device-auth-container"},$e={class:"device-type-container"},Pe={class:"device-type-content"},Ge={class:"user-group-section"},qe={class:"device-list-container"},Re={class:"batch-operation"},Me={class:"auth-type-tags"},Oe={key:0},Je={key:1},Qe={key:0},je={key:1},Ke={style:{display:"flex","justify-content":"center",gap:"10px"}},Ye={class:"pagination-container"},He={class:"auth-type-buttons"},Ze={class:"dialog-footer"},We={key:0,class:"empty-data"},Xe={key:0,style:{color:"#999","font-size":"12px"}},_e={class:"auth-type-buttons"},xe={class:"auth-manage-footer"},et={class:"dialog-footer"},tt={class:"auth-type-buttons"},ut={class:"dialog-footer"},at={class:"auth-type-buttons"},rt={class:"dialog-footer"};function st(t,e,J,Q,j,u){var z,$;const E=he,q=fe,R=ge,B=ce,A=Be,o=ve,k=Ce,f=Ae,C=be,L=Fe,M=De,I=W,w=Te,S=Ue;return g(),U("div",Se,[h("div",ze,[h("div",$e,[e[28]||(e[28]=h("div",{class:"device-type-title"},"设备类型",-1)),h("div",Pe,[r(q,{modelValue:t.selectedDeviceType,"onUpdate:modelValue":e[0]||(e[0]=l=>t.selectedDeviceType=l),onChange:t.handleDeviceTypeChange},{default:s(()=>[r(E,{value:""},{default:s(()=>e[23]||(e[23]=[i("全部设备")])),_:1}),r(E,{value:"pressure"},{default:s(()=>e[24]||(e[24]=[i("压力计")])),_:1}),r(E,{value:"flow"},{default:s(()=>e[25]||(e[25]=[i("流量计")])),_:1}),r(E,{value:"quality"},{default:s(()=>e[26]||(e[26]=[i("水质检测仪")])),_:1})]),_:1},8,["modelValue","onChange"])]),h("div",Ge,[e[27]||(e[27]=h("div",{class:"user-group-title"},"用户分组",-1)),r(R,{ref:"userGroupTree",data:t.userGroupTreeData,props:t.userGroupTreeProps,"node-key":"id","highlight-current":"","default-expand-all":"","show-checkbox":"",onCheck:t.handleUserGroupCheck},null,8,["data","props","onCheck"])])]),h("div",qe,[r(k,{model:t.queryParams,ref:"queryFormRef",inline:!0,class:"search-form"},{default:s(()=>[r(A,{label:"设备名称："},{default:s(()=>[r(B,{modelValue:t.queryParams.deviceName,"onUpdate:modelValue":e[1]||(e[1]=l=>t.queryParams.deviceName=l),placeholder:"请输入设备名称",clearable:""},null,8,["modelValue"])]),_:1}),r(A,{label:"设备编号："},{default:s(()=>[r(B,{modelValue:t.queryParams.deviceCode,"onUpdate:modelValue":e[2]||(e[2]=l=>t.queryParams.deviceCode=l),placeholder:"请输入设备编号",clearable:""},null,8,["modelValue"])]),_:1}),r(A,null,{default:s(()=>[r(o,{type:"primary",onClick:t.handleQuery},{default:s(()=>e[29]||(e[29]=[i("查询")])),_:1},8,["onClick"]),r(o,{onClick:t.resetQuery},{default:s(()=>e[30]||(e[30]=[i("重置")])),_:1},8,["onClick"])]),_:1})]),_:1},8,["model"]),h("div",Re,[r(o,{type:"primary",disabled:t.selectedDevices.length===0,onClick:t.handleBatchUserBind},{default:s(()=>e[31]||(e[31]=[i(" 批量绑定用户 ")])),_:1},8,["disabled","onClick"]),r(o,{type:"danger",disabled:t.selectedDevices.length===0,onClick:t.handleBatchRemoveAuth},{default:s(()=>e[32]||(e[32]=[i(" 批量移除权限 ")])),_:1},8,["disabled","onClick"])]),H((g(),b(L,{data:t.deviceList,border:"",onSelectionChange:t.handleSelectionChange,onExpandChange:t.handleExpandChange},{default:s(()=>[r(f,{type:"selection",width:"55"}),r(f,{type:"expand"},{default:s(l=>[H((g(),U("div",null,[r(L,{data:l.row.userAuthList||[],border:"",class:"sub-table"},{default:s(()=>[r(f,{label:"用户名称",prop:"userName",align:"center"}),r(f,{label:"用户组",prop:"userGroupName",align:"center"}),r(f,{label:"权限类型",align:"center"},{default:s(c=>[h("div",Me,[c.row.authType===1?(g(),b(C,{key:0,type:"danger"},{default:s(()=>e[33]||(e[33]=[i("完全控制")])),_:1})):T("",!0),c.row.authType===2?(g(),b(C,{key:1,type:"info"},{default:s(()=>e[34]||(e[34]=[i("只读访问")])),_:1})):T("",!0),c.row.authType===3?(g(),b(C,{key:2,type:"warning"},{default:s(()=>e[35]||(e[35]=[i("数据下载")])),_:1})):T("",!0),c.row.authType===4?(g(),b(C,{key:3,type:"success"},{default:s(()=>e[36]||(e[36]=[i("参数设置")])),_:1})):T("",!0)])]),_:1}),r(f,{label:"操作",align:"center",width:"120"},{default:s(c=>[r(o,{type:"danger",size:"small",onClick:K=>t.handleDeleteUserAuth(l.row,c.row)},{default:s(()=>e[37]||(e[37]=[i("删除")])),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1032,["data"])])),[[S,l.row.loadingUserAuth]])]),_:1}),r(f,{label:"设备名称",prop:"name",align:"center"}),r(f,{label:"设备编号",prop:"code",align:"center"}),r(f,{label:"设备类型",align:"center"},{default:s(l=>[r(C,{type:t.getDeviceTypeTagType(l.row.type)},{default:s(()=>[i(P(t.getDeviceTypeName(l.row.type)),1)]),_:2},1032,["type"])]),_:1}),r(f,{label:"安装位置",prop:"location",align:"center"}),r(f,{label:"绑定用户",align:"center"},{default:s(l=>[l.row.userAuthList&&l.row.userAuthList.length>0?(g(),U("span",Oe,P(l.row.userAuthList.map(c=>c.userName).join(", ")),1)):(g(),U("span",Je,"-"))]),_:1}),r(f,{label:"权限范围",align:"center"},{default:s(l=>[l.row.userAuthList&&l.row.userAuthList.length>0?(g(),U("span",Qe,[t.hasAuthType(l.row,1)?(g(),b(C,{key:0,type:"danger",class:"ml-5"},{default:s(()=>e[38]||(e[38]=[i("完全控制")])),_:1})):T("",!0),t.hasAuthType(l.row,2)?(g(),b(C,{key:1,type:"info",class:"ml-5"},{default:s(()=>e[39]||(e[39]=[i("只读访问")])),_:1})):T("",!0),t.hasAuthType(l.row,3)?(g(),b(C,{key:2,type:"warning",class:"ml-5"},{default:s(()=>e[40]||(e[40]=[i("数据下载")])),_:1})):T("",!0),t.hasAuthType(l.row,4)?(g(),b(C,{key:3,type:"success",class:"ml-5"},{default:s(()=>e[41]||(e[41]=[i("参数设置")])),_:1})):T("",!0)])):(g(),U("span",je,"-"))]),_:1}),r(f,{label:"操作",align:"center",width:"240"},{default:s(l=>[h("div",Ke,[r(o,{type:"primary",size:"small",onClick:c=>t.handleUserBind(l.row)},{default:s(()=>e[42]||(e[42]=[i("用户绑定")])),_:2},1032,["onClick"]),r(o,{type:"success",size:"small",onClick:c=>t.handleAuthManage(l.row)},{default:s(()=>e[43]||(e[43]=[i("权限管理")])),_:2},1032,["onClick"])])]),_:1})]),_:1},8,["data","onSelectionChange","onExpandChange"])),[[S,t.loading]]),h("div",Ye,[r(M,{onSizeChange:t.handleSizeChange,onCurrentChange:t.handleCurrentChange,"current-page":t.queryParams.page,"page-sizes":[10,20,50,100],"page-size":t.queryParams.size,layout:"total, sizes, prev, pager, next, jumper",total:t.total},null,8,["onSizeChange","onCurrentChange","current-page","page-size","total"])])])]),r(w,{title:"用户绑定 - "+(((z=t.currentDevice)==null?void 0:z.name)||""),modelValue:t.userBindDialogVisible,"onUpdate:modelValue":e[8]||(e[8]=l=>t.userBindDialogVisible=l),width:"600px","append-to-body":""},{footer:s(()=>[h("div",Ze,[r(o,{onClick:e[7]||(e[7]=l=>t.userBindDialogVisible=!1)},{default:s(()=>e[48]||(e[48]=[i("取 消")])),_:1}),r(o,{type:"primary",onClick:t.submitUserBind},{default:s(()=>e[49]||(e[49]=[i("确 定")])),_:1},8,["onClick"])])]),default:s(()=>[r(k,{ref:"userBindFormRef",model:t.userBindForm,"label-width":"100px"},{default:s(()=>[r(A,{label:"选择用户",prop:"userIds"},{default:s(()=>[r(I,{users:t.selectedUsers,onCheckUsers:t.handleUserSelect,multiple:""},null,8,["users","onCheckUsers"])]),_:1}),r(A,{label:"权限类型",prop:"authType"},{default:s(()=>[h("div",He,[r(o,{type:t.userBindForm.authType===1?"danger":"default",onClick:e[3]||(e[3]=l=>t.handleAuthTypeChange(1,"userBindForm")),size:"small"},{default:s(()=>e[44]||(e[44]=[i("完全控制")])),_:1},8,["type"]),r(o,{type:t.userBindForm.authType===2?"info":"default",onClick:e[4]||(e[4]=l=>t.handleAuthTypeChange(2,"userBindForm")),size:"small"},{default:s(()=>e[45]||(e[45]=[i("只读访问")])),_:1},8,["type"]),r(o,{type:t.userBindForm.authType===3?"warning":"default",onClick:e[5]||(e[5]=l=>t.handleAuthTypeChange(3,"userBindForm")),size:"small"},{default:s(()=>e[46]||(e[46]=[i("数据下载")])),_:1},8,["type"]),r(o,{type:t.userBindForm.authType===4?"success":"default",onClick:e[6]||(e[6]=l=>t.handleAuthTypeChange(4,"userBindForm")),size:"small"},{default:s(()=>e[47]||(e[47]=[i("参数设置")])),_:1},8,["type"])])]),_:1})]),_:1},8,["model"])]),_:1},8,["title","modelValue"]),r(w,{title:"权限管理 - "+((($=t.currentDevice)==null?void 0:$.name)||""),modelValue:t.authManageDialogVisible,"onUpdate:modelValue":e[10]||(e[10]=l=>t.authManageDialogVisible=l),width:"800px","append-to-body":""},{footer:s(()=>[h("div",et,[r(o,{onClick:e[9]||(e[9]=l=>t.authManageDialogVisible=!1)},{default:s(()=>e[57]||(e[57]=[i("取 消")])),_:1}),r(o,{type:"primary",onClick:t.submitAuthManage},{default:s(()=>e[58]||(e[58]=[i("确 定")])),_:1},8,["onClick"])])]),default:s(()=>[t.currentDeviceUserAuthList.length===0?(g(),U("div",We,e[50]||(e[50]=[h("p",null,"暂无用户权限数据",-1)]))):(g(),b(L,{key:1,data:t.currentDeviceUserAuthList,border:""},{default:s(()=>[r(f,{label:"用户名称",align:"center"},{default:s(l=>[i(P(l.row.userName||"未命名用户")+" ",1),l.row.userName?T("",!0):(g(),U("div",Xe," 用户ID: "+P(l.row.userId),1))]),_:1}),r(f,{label:"用户组",prop:"userGroupName",align:"center"}),r(f,{label:"权限类型",align:"center"},{default:s(l=>[h("div",_e,[r(o,{type:l.row.authType===1?"danger":"default",onClick:c=>t.handleRowAuthTypeChange(l.row,1),size:"small"},{default:s(()=>e[51]||(e[51]=[i("完全控制")])),_:2},1032,["type","onClick"]),r(o,{type:l.row.authType===2?"info":"default",onClick:c=>t.handleRowAuthTypeChange(l.row,2),size:"small"},{default:s(()=>e[52]||(e[52]=[i("只读访问")])),_:2},1032,["type","onClick"]),r(o,{type:l.row.authType===3?"warning":"default",onClick:c=>t.handleRowAuthTypeChange(l.row,3),size:"small"},{default:s(()=>e[53]||(e[53]=[i("数据下载")])),_:2},1032,["type","onClick"]),r(o,{type:l.row.authType===4?"success":"default",onClick:c=>t.handleRowAuthTypeChange(l.row,4),size:"small"},{default:s(()=>e[54]||(e[54]=[i("参数设置")])),_:2},1032,["type","onClick"])])]),_:1}),r(f,{label:"操作",align:"center",width:"120"},{default:s(l=>[r(o,{type:"danger",size:"small",onClick:c=>t.handleRemoveUserAuth(l.$index)},{default:s(()=>e[55]||(e[55]=[i("移除")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),h("div",xe,[r(o,{type:"primary",onClick:t.handleAddUserAuth},{default:s(()=>e[56]||(e[56]=[i("添加用户")])),_:1},8,["onClick"])])]),_:1},8,["title","modelValue"]),r(w,{title:"添加用户",modelValue:t.addUserDialogVisible,"onUpdate:modelValue":e[16]||(e[16]=l=>t.addUserDialogVisible=l),width:"500px","append-to-body":""},{footer:s(()=>[h("div",ut,[r(o,{onClick:e[15]||(e[15]=l=>t.addUserDialogVisible=!1)},{default:s(()=>e[63]||(e[63]=[i("取 消")])),_:1}),r(o,{type:"primary",onClick:t.submitAddUser},{default:s(()=>e[64]||(e[64]=[i("确 定")])),_:1},8,["onClick"])])]),default:s(()=>[r(k,{ref:"addUserFormRef",model:t.addUserForm,"label-width":"100px"},{default:s(()=>[r(A,{label:"选择用户",prop:"userId"},{default:s(()=>[r(I,{users:t.addSelectedUsers,onCheckUsers:t.handleAddUserSelect,multiple:!1},null,8,["users","onCheckUsers"])]),_:1}),r(A,{label:"权限类型",prop:"authType"},{default:s(()=>[h("div",tt,[r(o,{type:t.addUserForm.authType===1?"danger":"default",onClick:e[11]||(e[11]=l=>t.handleAuthTypeChange(1,"addUserForm")),size:"small"},{default:s(()=>e[59]||(e[59]=[i("完全控制")])),_:1},8,["type"]),r(o,{type:t.addUserForm.authType===2?"info":"default",onClick:e[12]||(e[12]=l=>t.handleAuthTypeChange(2,"addUserForm")),size:"small"},{default:s(()=>e[60]||(e[60]=[i("只读访问")])),_:1},8,["type"]),r(o,{type:t.addUserForm.authType===3?"warning":"default",onClick:e[13]||(e[13]=l=>t.handleAuthTypeChange(3,"addUserForm")),size:"small"},{default:s(()=>e[61]||(e[61]=[i("数据下载")])),_:1},8,["type"]),r(o,{type:t.addUserForm.authType===4?"success":"default",onClick:e[14]||(e[14]=l=>t.handleAuthTypeChange(4,"addUserForm")),size:"small"},{default:s(()=>e[62]||(e[62]=[i("参数设置")])),_:1},8,["type"])])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),r(w,{title:"批量绑定用户",modelValue:t.batchBindDialogVisible,"onUpdate:modelValue":e[22]||(e[22]=l=>t.batchBindDialogVisible=l),width:"600px","append-to-body":""},{footer:s(()=>[h("div",rt,[r(o,{onClick:e[21]||(e[21]=l=>t.batchBindDialogVisible=!1)},{default:s(()=>e[69]||(e[69]=[i("取 消")])),_:1}),r(o,{type:"primary",onClick:t.submitBatchBind},{default:s(()=>e[70]||(e[70]=[i("确 定")])),_:1},8,["onClick"])])]),default:s(()=>[r(k,{ref:"batchBindFormRef",model:t.batchBindForm,"label-width":"100px"},{default:s(()=>[r(A,{label:"选择用户",prop:"userIds"},{default:s(()=>[r(I,{users:t.batchSelectedUsers,onCheckUsers:t.handleBatchUserSelect,multiple:""},null,8,["users","onCheckUsers"])]),_:1}),r(A,{label:"权限类型",prop:"authType"},{default:s(()=>[h("div",at,[r(o,{type:t.batchBindForm.authType===1?"danger":"default",onClick:e[17]||(e[17]=l=>t.handleAuthTypeChange(1,"batchBindForm")),size:"small"},{default:s(()=>e[65]||(e[65]=[i("完全控制")])),_:1},8,["type"]),r(o,{type:t.batchBindForm.authType===2?"info":"default",onClick:e[18]||(e[18]=l=>t.handleAuthTypeChange(2,"batchBindForm")),size:"small"},{default:s(()=>e[66]||(e[66]=[i("只读访问")])),_:1},8,["type"]),r(o,{type:t.batchBindForm.authType===3?"warning":"default",onClick:e[19]||(e[19]=l=>t.handleAuthTypeChange(3,"batchBindForm")),size:"small"},{default:s(()=>e[67]||(e[67]=[i("数据下载")])),_:1},8,["type"]),r(o,{type:t.batchBindForm.authType===4?"success":"default",onClick:e[20]||(e[20]=l=>t.handleAuthTypeChange(4,"batchBindForm")),size:"small"},{default:s(()=>e[68]||(e[68]=[i("参数设置")])),_:1},8,["type"])])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}const it=ye(Ne,[["render",st],["__scopeId","data-v-98882215"]]);export{it as default};
