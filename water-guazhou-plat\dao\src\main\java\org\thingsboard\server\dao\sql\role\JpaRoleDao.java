/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.sql.role;

import com.google.common.util.concurrent.ListenableFuture;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Component;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.common.data.role.Role;
import org.thingsboard.server.dao.DaoUtil;
import org.thingsboard.server.dao.model.sql.RoleEntity;
import org.thingsboard.server.dao.role.RoleDao;
import org.thingsboard.server.dao.sql.JpaAbstractDao;
import org.thingsboard.server.dao.util.SqlDao;

import java.util.List;
import java.util.UUID;

@Component
@SqlDao
@Slf4j
public class JpaRoleDao extends JpaAbstractDao<RoleEntity, Role> implements RoleDao {

    @Autowired
    private RoleRepository roleRepository;


    @Override
    protected void setSearchText(RoleEntity entity) {
        super.setSearchText(entity);
    }

    @Override
    public Role save(Role domain) {
        return super.save(domain);
    }

    @Override
    public Role findById(UUID key) {
        return super.findById(key);
    }

//    @Override
//    public ListenableFuture<Role> findByIdAsync(UUID key) {
//        return super.findByIdAsync(key);
//    }

    @Override
    public boolean removeById(UUID id) {
        return super.removeById(id);
    }

    @Override
    public List<Role> find() {
        return super.find();
    }

    @Override
    protected Class<RoleEntity> getEntityClass() {
        return RoleEntity.class;
    }

    @Override
    protected CrudRepository<RoleEntity, String> getCrudRepository() {
        return roleRepository;
    }

    @Override
    public List<Role> findByTenantId(TenantId tenantId) {
        return DaoUtil.convertDataList(roleRepository.findByTenantIdOrderById(UUIDConverter.fromTimeUUID(tenantId.getId())));
    }

    @Override
    public PageData<Role> findList(String name, String tenantId, PageRequest pageRequest) {
        Page<RoleEntity> pageResult = roleRepository.findList(name, tenantId, pageRequest);

        return new PageData<>(pageResult.getTotalElements(), DaoUtil.convertDataList(pageResult.getContent()));
    }
}
