<template>
  <div class="wrapper">
    <CardSearch
      ref="refSearch"
      :config="cardSearchConfig"
    />
    <SLCard
      class="card"
      title=" "
    >
      <template #title>
        <Tabs
          v-model="state.activeName"
          :config="tabsConfig"
        ></Tabs>
      </template>
      <div class="tab">
        <distribution-table
          v-if="state.activeName === '配表分析'"
          :station-name="state.stationName"
          :station-id="state.stationId"
        >
        </distribution-table>
        <peak-valley
          v-if="state.activeName === '谷峰分析'"
          :station-name="state.stationName"
          :station-id="state.stationId"
        ></peak-valley>
        <time-contrast
          v-if="state.activeName === '时段分析'"
          :station-name="state.stationName"
          :station-id="state.stationId"
        ></time-contrast>
        <compared-same
          v-if="state.activeName === '同比曲线'"
          :station-name="state.stationName"
          :station-id="state.stationId"
        ></compared-same>
        <sequential
          v-if="state.activeName === '环比曲线'"
          :station-name="state.stationName"
          :station-id="state.stationId"
        >
        </sequential>
        <small-flow
          v-if="state.activeName === '小流分析'"
          :station-name="state.stationName"
          :station-id="state.stationId"
        ></small-flow>
      </div>
    </SLCard>
  </div>
</template>

<script lang="ts" setup>
import { GetStationAttrs } from '@/api/shuiwureports/zhandian'
import useStation from '@/hooks/station/useStation'
import smallFlow from './components/smallFlow.vue'
import comparedSame from './components/comparedSame.vue'
import distributionTable from './components/distributionTable.vue'
import peakValley from './components/peakValley.vue'
import sequential from './components/sequential.vue'
import timeContrast from './components/timeContrast.vue'
import {
  objectLookup, getFormatTreeNodeDeepestChild
} from '@/utils/GlobalHelper'
import { ISearchIns } from '@/components/type'

const { getStationTree, getStationTreeByDisabledType } = useStation()
const state = reactive<{
  activeName: string,
  stationId: string,
  stationName: string,
}>({
  activeName: '配表分析',
  stationId: '',
  stationName: ''
})

const refSearch = ref<ISearchIns>()
const cardSearchConfig = reactive<ISearch>({
  defaultParams: {},
  filters: [
    {
      type: 'select-tree',
      field: 'treeData',
      checkStrictly: true,
      defaultExpandAll: true,
      options: computed(() => TreeData.data) as any,
      label: '站点选择',
      onChange: async key => {
        const val = objectLookup(TreeData.data, 'children', 'id', key)
        TreeData.currentProject = val
        state.stationId = val.id as string
        state.stationName = val.label as string
        await refreshData()
      }
    }
  ]
})

// tabs
const tabsConfig = reactive<ITabs>({
  type: 'tabs',
  tabType: 'simple',
  width: '100%',
  tabs: [
    { label: '配表分析', value: '配表分析' },
    { label: '谷峰分析', value: '谷峰分析' },
    { label: '时段分析', value: '时段分析' },
    { label: '同比曲线', value: '同比曲线' },
    { label: '环比曲线', value: '环比曲线' }
    // { label: '小流分析', value: '小流分析' }
  ],
  handleTabClick: (tab: any) => {
    //
    state.activeName = tab.props.name
  }
})
// 获取左边树
const TreeData = reactive<SLTreeConfig>({
  data: [],
  title: '区域划分',
  defaultExpandAll: true,
  nodeExpand: async (params: any, node?: any) => {
    if (params.data?.type === 'Station') {
      const attrs = await GetStationAttrs({ stationId: params.id })
      console.log(attrs.data)
      const newAttrs = attrs.data?.map(attr => {
        return {
          label: attr.type,
          value: '',
          id: '',
          disabled: true,
          children: attr.attrList
        }
      })
      node.data.children = newAttrs as any
    }
  },
  treeNodeHandleClick: async (data: NormalOption) => {
    // TreeData.loading = true
    console.log(data)
    TreeData.currentProject = data
    state.stationId = data.id as string
    state.stationName = data.label as string
  }
})

const refreshData = async () => {
  //
}

onMounted(async () => {
  const treeData = await getStationTree('流量监测站,测流压站')
  await getStationTreeByDisabledType(treeData, ['Project'], false, 'Station')
  TreeData.data = treeData
  const currentStation = getFormatTreeNodeDeepestChild(
    treeData
  ) as any
  cardSearchConfig.defaultParams = {
    ...cardSearchConfig.defaultParams,
    treeData: currentStation.id
  }
  refSearch.value?.resetForm()
  state.stationId = currentStation.id
  state.stationName = currentStation.label
  // state.stationId = TreeData.currentProject.id as string
  // TreeData.currentProject = treeData[0]?.children[0]
})
</script>
<style lang="scss" scoped>
.card {
  height: calc(100% - 80px);

  .tab {
    height: 100%;
    width: 100%;
  }
}
</style>
