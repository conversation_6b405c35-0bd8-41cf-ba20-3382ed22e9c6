import { request } from '@/plugins/axios'

// 获取权限配置列表
export function getPermissionConfigList(params: any) {
  return request({
    url: '/api/base/permission/configuration/list',
    method: 'get',
    params
  })
}

// 获取权限配置详情
export function getPermissionConfigDetail(id: string) {
  return request({
    url: '/api/base/permission/configuration/getDetail',
    method: 'get',
    params: { id }
  })
}

// 新增权限配置
export function addPermissionConfig(data: any) {
  return request({
    url: '/api/base/permission/configuration/add',
    method: 'post',
    data
  })
}

// 修改权限配置
export function editPermissionConfig(data: any) {
  return request({
    url: '/api/base/permission/configuration/edit',
    method: 'post',
    data
  })
}

// 删除权限配置
export function deletePermissionConfig(ids: string[]) {
  return request({
    url: '/api/base/permission/configuration/deleteIds',
    method: 'delete',
    data: ids
  })
}

// 获取角色列表
export function getRoleList() {
  return request({
    url: '/api/role/roles',
    method: 'get'
  })
} 