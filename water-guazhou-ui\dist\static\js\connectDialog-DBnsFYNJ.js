import{C,M as O,al as x,b7 as E,D as h,g as v,h as w,F as l,q as t,p,G as r,H as T,J as S,N as U,bk as I,O as y,bl as N,bm as R,L}from"./index-r0dFAfgr.js";import{b as F,d as B,e as J,f as P}from"./index-Bj5d3Vsu.js";const{$messageSuccess:g}=O(),q={name:"ConnectDialog",props:["connectDialog"],setup(){return{Search:x,Refresh:E}},data(){return{filterText:"",form:[],filterTextOuter:"",activePane:"first",userData:[],checkList:[],filterData:[],formOuter:[],connectData:[],filterDataOuter:[]}},computed:{visible(){return this.connectDialog.visible}},created(){this.getData()},methods:{getData(){const o=h(this.connectDialog.tableData.id.id);F(o).then(e=>{this.userData=e.data,this.form=this.userData,console.log(this.form,"form")}),B(o).then(e=>{this.formOuter=e.data,this.connectData=e.data,console.log(this.formOuter,"formoute")})},save(){const o=[];for(const s in this.form){console.log(this.form);const n={};n.userId=this.form[s].userId,n.sendSms=this.form[s].sendSms,n.sendEmail=this.form[s].sendEmail,o.push(n)}console.log(o);const e=h(this.connectDialog.tableData.id.id);J(o,e).then(s=>{s.status===200&&(g("保存成功"),this.connectDialog.close())}),this.getData()},saveOuter(){const o=[];for(const s in this.formOuter){const n={};n.extraUserId=this.formOuter[s].extraUserId,n.sendSms=this.formOuter[s].sendSms,n.sendEmail=this.formOuter[s].sendEmail,o.push(n)}console.log(o,"canshu ");const e=h(this.connectDialog.tableData.id.id);P(o,e).then(s=>{s.status===200&&(g("保存成功"),this.connectDialog.close())}),this.getData()},clickFilterData(){this.filterData=this.userData.filter(o=>o.userName.toLowerCase().includes(this.filterText.toLowerCase())),this.form=this.filterData},clickReset(){this.filterText="",this.filterData=this.userData,this.form=this.filterData},clickFilterOuterData(){this.filterDataOuter=this.connectData.filter(o=>o.userName.toLowerCase().includes(this.filterTextOuter.toLowerCase())),this.formOuter=this.filterDataOuter},clickOuterReset(){this.filterTextOuter="",this.filterDataOuter=this.connectData,this.formOuter=this.filterDataOuter}}},j={class:"left-btn"},A={class:"footer"},G={class:"left-btn"},H={class:"footer"};function M(o,e,s,n,i,d){const D=T,u=S,m=U,f=I,b=y,_=N,k=R,V=L;return v(),w(V,{modelValue:d.visible,"onUpdate:modelValue":e[3]||(e[3]=a=>d.visible=a),title:"告警关联联系人",width:"45%","append-to-body":"",class:"alarm-design","close-on-click-modal":!1,onClose:s.connectDialog.close},{default:l(()=>[t(k,{modelValue:i.activePane,"onUpdate:modelValue":e[2]||(e[2]=a=>i.activePane=a)},{default:l(()=>[t(_,{label:"内部联系人",name:"first"},{default:l(()=>[p("div",j,[t(D,{modelValue:i.filterText,"onUpdate:modelValue":e[0]||(e[0]=a=>i.filterText=a),placeholder:"输入姓名搜索",class:"filter-input"},null,8,["modelValue"]),t(u,{icon:n.Search,class:"query-yellow",onClick:d.clickFilterData},{default:l(()=>e[4]||(e[4]=[r(" 查询 ")])),_:1},8,["icon","onClick"]),t(u,{icon:n.Refresh,class:"reset-green",onClick:d.clickReset},{default:l(()=>e[5]||(e[5]=[r(" 重置 ")])),_:1},8,["icon","onClick"])]),t(b,{ref:"form",stripe:"",data:i.form,height:"500px"},{default:l(()=>[t(m,{label:"姓名",prop:"userName"}),t(m,{label:"手机号",prop:"phone"}),t(m,{label:"短信接收告警",prop:"sendSms"},{default:l(a=>[t(f,{modelValue:a.row.sendSms,"onUpdate:modelValue":c=>a.row.sendSms=c},{default:l(()=>e[6]||(e[6]=[r(" 短信 ")])),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:1}),t(m,{label:"邮件接收告警",prop:"sendEmail"},{default:l(a=>[t(f,{modelValue:a.row.sendEmail,"onUpdate:modelValue":c=>a.row.sendEmail=c},{default:l(()=>e[7]||(e[7]=[r(" 邮件 ")])),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:1})]),_:1},8,["data"]),p("div",A,[t(u,{class:"edit-primary-blue",onClick:d.save},{default:l(()=>e[8]||(e[8]=[r(" 保存 ")])),_:1},8,["onClick"]),t(u,{plain:"",onClick:s.connectDialog.close},{default:l(()=>e[9]||(e[9]=[r(" 取消 ")])),_:1},8,["onClick"])])]),_:1}),t(_,{label:"外部联系人",name:"second"},{default:l(()=>[p("div",G,[t(D,{modelValue:i.filterTextOuter,"onUpdate:modelValue":e[1]||(e[1]=a=>i.filterTextOuter=a),placeholder:"输入姓名搜索",class:"filter-input"},null,8,["modelValue"]),t(u,{icon:n.Search,class:"query-yellow",onClick:d.clickFilterOuterData},{default:l(()=>e[10]||(e[10]=[r(" 查询 ")])),_:1},8,["icon","onClick"]),t(u,{icon:n.Refresh,class:"reset-green",onClick:d.clickOuterReset},{default:l(()=>e[11]||(e[11]=[r(" 重置 ")])),_:1},8,["icon","onClick"])]),t(b,{ref:"forms",stripe:"",data:i.formOuter,height:"500px"},{default:l(()=>[t(m,{label:"姓名",prop:"userName"}),t(m,{label:"手机号",prop:"phone"}),t(m,{label:"短信接收告警",prop:"sendSms"},{default:l(a=>[t(f,{modelValue:a.row.sendSms,"onUpdate:modelValue":c=>a.row.sendSms=c},{default:l(()=>e[12]||(e[12]=[r(" 短信 ")])),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:1}),t(m,{label:"邮件接收告警",prop:"sendEmail"},{default:l(a=>[t(f,{modelValue:a.row.sendEmail,"onUpdate:modelValue":c=>a.row.sendEmail=c},{default:l(()=>e[13]||(e[13]=[r(" 邮件 ")])),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:1})]),_:1},8,["data"]),p("div",H,[t(u,{class:"edit-primary-blue",onClick:d.saveOuter},{default:l(()=>e[14]||(e[14]=[r(" 保存 ")])),_:1},8,["onClick"]),t(u,{plain:"",onClick:s.connectDialog.close},{default:l(()=>e[15]||(e[15]=[r(" 取消 ")])),_:1},8,["onClick"])])]),_:1})]),_:1},8,["modelValue"])]),_:1},8,["modelValue","onClose"])}const Q=C(q,[["render",M],["__scopeId","data-v-8570f2d1"]]);export{Q as default};
