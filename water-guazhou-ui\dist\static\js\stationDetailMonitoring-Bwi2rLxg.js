import{_ as _e}from"./CardSearch-CB_HNR-Q.js";import{d as ye,a6 as ke,bF as p,r as v,c as y,am as xe,f1 as Ie,a8 as X,s as G,bB as E,D as Z,j as N,ay as Te,g as b,n as k,p as u,q as n,av as we,i as S,F as d,h as q,aB as Ce,aJ as Ne,f2 as ee,an as w,cs as te,c5 as Se,aq as Le,cE as De,cU as Ae,dB as Ye,dC as Oe,dF as Me,dA as Ge,al as ae,aj as oe,C as Ee}from"./index-r0dFAfgr.js";import{_ as Fe}from"./Search-NSrhrIa_.js";import{_ as Re}from"./index-B69llYYW.js";import{_ as je}from"./index-C9hz-UZb.js";/* empty css                         */import{l as le}from"./echart-CJcuvGqs.js";import{u as Be}from"./useStation-DJgnSZIA.js";import{s as Pe,t as Ve,d as ze}from"./zhandian-YaGuQZe6.js";import{s as He,a as Ue}from"./headwaterMonitoring-BgK7jThW.js";import{g as Je}from"./URLHelper-B9aplt5w.js";import"./useAmap-D6DJ1T90.js";import"./index-BI1vGJja.js";const Qe=[{label:"0时",value:0},{label:"1时",value:1},{label:"2时",value:2},{label:"3时",value:3},{label:"4时",value:4},{label:"5时",value:5},{label:"6时",value:6},{label:"7时",value:7},{label:"8时",value:8},{label:"9时",value:9},{label:"10时",value:10},{label:"11时",value:11},{label:"12时",value:12},{label:"13时",value:13},{label:"14时",value:14},{label:"15时",value:15},{label:"16时",value:16},{label:"17时",value:17},{label:"18时",value:18},{label:"19时",value:19},{label:"20时",value:20},{label:"21时",value:21},{label:"22时",value:22},{label:"23时",value:23}],We={style:{"margin-top":"10px"}},$e={key:0,class:"content"},Ke={class:"top"},Xe={style:{width:"100%",height:"100%"},class:"card-table"},Ze={style:{display:"flex","justify-content":"space-between",height:"85%"}},qe={class:"card-table"},et={class:"bottom"},tt={style:{width:"32%",overflow:"hidden"}},at={class:"image-slot"},ot={key:1,class:"bottom-1"},lt={class:"no-pictures"},st={style:{width:"32%"}},nt={style:{width:"32%"}},rt={key:1,class:"content1"},it={key:0},ct={key:2,class:"content1"},ut={key:3,class:"content"},dt=ye({__name:"stationDetailMonitoring",props:{stationId:{},monitor:{}},emits:["hiddenLoading","update:model-value"],setup(se,{emit:ne}){const{getStationAttrGroups:re}=Be(),ie=ne,z=ke(),x=se,O=p().date(),a=v({activeName:"status",chartOption:null,chartOption1:null,searchActiveName:"echarts",groupTab:"",currentGroupTabs:[],stationInfo:null,imgs:[],stationInfoColumns:[]}),F=y(),H=y(),U=y(),J=y(),L=y(),R=y(),j=y(),B=y(),ce=y();let D=v([]),c=v([]);xe(()=>[x.stationId,x.monitor,a.searchActiveName],async(e,t)=>{var o,l,s,r;if(e[0]&&(t[0]!==e[0]||((o=t[1])==null?void 0:o.name)!==((l=e[1])==null?void 0:l.name))){const f=await Pe(x.stationId);a.stationInfo=f.data;const m=await Ie((s=f.data)==null?void 0:s.projectId);a.stationInfo.projectName=(r=m==null?void 0:m.data)==null?void 0:r.name,a.imgs=a.stationInfo.imgs?a.stationInfo.imgs.split(","):[],console.log(" state.imgs",e),console.log(" state.imgs111",t),ge(),setTimeout(async()=>{ue(a.stationInfo)},1e3)}t[2]!==e[2]&&a.searchActiveName==="echarts"&&await V()});const ue=async e=>{var o,l;const t=(o=e.location)==null?void 0:o.split(",");(t==null?void 0:t.length)===2&&((l=F.value)==null||l.setMarker(t,{icon:Je("泵站.png")},()=>{Q(e)})),Q(e)},Q=async e=>{var l,s;const t=[{label:"名称",value:e.name},{label:"类型",value:e.type},{label:"所属项目",value:e.projectName},{label:"地址",value:e.address},{label:"经纬度",value:e.location},{label:"备注",value:e.remark}],o=(l=e.location)==null?void 0:l.split(",");(o==null?void 0:o.length)===2&&((s=F.value)==null||s.setListInfoWindow({point:o,values:t,title:e.name}))},I=v({loading:!0,currentRow:[],currentRowKey:"property",highlightCurrentRow:!0,dataList:[],columns:[{prop:"propertyName",label:"检测项名称"},{prop:"value",label:"检测项数据"},{prop:"collectionTime",label:"采集时间",formatter:e=>e.collectionTime>0?p(e.collectionTime).format("YYYY-MM-DD HH:mm:ss"):"-"}],operations:[],pagination:{hide:!0},handleRowClick:e=>{I.currentRow=e,V()}}),h=v({loading:!1,dataList:[],indexVisible:!0,columns:[{prop:"alarmInfo",label:"报警描述"},{prop:"time",label:"报警时间",formatter:(e,t)=>t?p(t).format("YYYY-MM-DD HH:mm:ss"):""}],operations:[],pagination:{layout:"total, prev, pager, next, jumper",refreshData:({page:e,size:t})=>{h.pagination.page=e,h.pagination.limit=t,h.dataList=D.slice((e-1)*t,e*t)}}}),_=v({loading:!0,dataList:[],indexVisible:!0,columns:[],operations:[],pagination:{layout:"total, prev, pager, next, jumper",refreshData:({page:e,size:t})=>{var o;_.pagination.page=e,_.pagination.limit=t,_.dataList=(o=c==null?void 0:c.tableDataList)==null?void 0:o.slice((e-1)*t,e*t)}}}),T=v({defaultParams:{date:[p().date(O-2).format("YYYY-MM-DD"),p().date(O).format("YYYY-MM-DD")],filterStart:[0,23],group:"",attributeId:""},filters:[{type:"daterange",label:"选中日期",field:"date",clearable:!1},{label:"时间",type:"range",rangeType:"select",field:"filterStart",options:JSON.parse(JSON.stringify(Qe)),startPlaceHolder:"0时",endPlaceHolder:"23时",startOptionDisabled:(e,t)=>t&&Number(t)<e.value,endOptionDisabled:(e,t)=>t&&e.value<=Number(t)},{label:"监测组",field:"group",type:"select",clearable:!1,options:[],onChange:e=>W(e)},{label:"曲线类型",field:"attributeId",type:"select",clearable:!1,options:[],hidden:X(()=>a.searchActiveName==="list")}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",click:()=>{_.pagination.page=1,$()},svgIcon:G(ae)},{perm:!0,type:"warning",text:"导出",svgIcon:G(oe),hide:()=>a.searchActiveName!=="list",click:()=>{var e;(e=H.value)==null||e.exportTable()}}]}]}),de=v({defaultParams:{date:[p().date(O-2).format("YYYY-MM-DD"),p().date(O).format("YYYY-MM-DD")]},filters:[{type:"daterange",label:"选择时间",field:"date",clearable:!1},{type:"btn-group",btns:[{perm:!0,text:"查询",click:()=>P(),svgIcon:G(ae)},{perm:!0,type:"warning",text:"导出",svgIcon:G(oe),click:()=>{var e;(e=J.value)==null||e.exportTable()}}]}]}),me=v({type:"tabs",tabType:"simple",width:"100%",tabs:[{label:"当前状态",value:"status"},{label:"数据查询",value:"search"},{label:"报警信息",value:"alarm"}],handleTabClick:e=>{console.log(e.props.name),a.activeName=e.props.name,a.activeName==="search"?(console.log(a.currentGroupTabs),E(()=>{var o;const t=(o=T.filters)==null?void 0:o.find(l=>l.field==="group");t.options=a.currentGroupTabs,T.defaultParams={...T.defaultParams,group:a.currentGroupTabs[0].value},W(a.currentGroupTabs[0].value),$()})):a.activeName==="alarm"&&E(()=>{P("range")})}}),W=e=>{var l,s;const t=a.currentGroupTabs.find(r=>r.value===e),o=(l=T.filters)==null?void 0:l.find(r=>(r==null?void 0:r.field)==="attributeId");o.options=t.children.map(r=>({label:r.name,value:r.id,data:Z(r.deviceId)+"."+r.attr,unit:r.unit?"("+r.unit+")":""})),T.defaultParams={...T.defaultParams,attributeId:t.children[0].id},(s=B.value)==null||s.resetForm(),console.log(e)},$=async()=>{var i,g,A,M;const e=((i=B.value)==null?void 0:i.queryParams)||{},[t,o]=e.date||[],[l,s]=e.filterStart||[],r={filterStart:l||0,filterEnd:s||23,queryType:"10m",stationId:x.stationId,group:e==null?void 0:e.group,start:t?p(t).startOf("day").valueOf():"",end:o?p(o).endOf("day").valueOf():""};c=(g=(await He(r)).data)==null?void 0:g.data;const m=c==null?void 0:c.tableInfo.map(C=>({prop:C.columnValue,label:C.columnName,unit:C.unit?"("+C.unit+")":""}));console.log(m),_.columns=m,_.dataList=(A=c==null?void 0:c.tableDataList)==null?void 0:A.slice(0*20,20),_.pagination.total=(M=c==null?void 0:c.tableDataList)==null?void 0:M.length,_.loading=!1,pe(e==null?void 0:e.attributeId)},pe=e=>{var r,f,m;const t=le(),l=(f=((r=T.filters)==null?void 0:r.find(i=>i.field==="attributeId")).options)==null?void 0:f.find(i=>i.value===e);t.yAxis[0].name=l.label+(l.unit?l.unit:""),t.xAxis.data=c==null?void 0:c.tableDataList.map(i=>i.ts),console.log(e+"."+l.data,c==null?void 0:c.tableDataList);const s={name:l.label,smooth:!0,data:c==null?void 0:c.tableDataList.map(i=>i[l.data]),type:"line",markPoint:{data:[{type:"max",name:"最大值",label:{fontSize:12,color:N().isDark?"#ffffff":"#000000"}},{type:"min",name:"最小值",label:{color:N().isDark?"#ffffff":"#000000"}}]}};t.series=[s],(m=j.value)==null||m.clear(),E(()=>{L.value&&z.listenTo(L.value,()=>{var i;a.chartOption1=t,(i=j.value)==null||i.resize()})})},fe=v({filters:[{type:"radio-button",label:"",field:"groupTab",options:X(()=>a.currentGroupTabs),onChange:e=>{e&&K(e)}}]}),ge=()=>{a.activeName="status",console.log("refreshData"),be()},be=async()=>{var t,o;const e=await re(x.stationId);a.currentGroupTabs=e,fe.defaultParams={groupTab:(t=e[0])==null?void 0:t.id},(o=ce.value)==null||o.resetForm(),await K(x.monitor.name),await P()},P=async e=>{var l;h.loading=!0;let t=p().startOf("month").valueOf(),o=p().endOf("month").valueOf();if(e==="range"){const s=(l=U.value)==null?void 0:l.queryParams;t=p(s==null?void 0:s.date[0]).startOf("day").valueOf(),o=p(s==null?void 0:s.date[1]).endOf("day").valueOf()}Ve({stationId:x.stationId,start:t,end:o,page:1,size:20}).then(s=>{console.log("res",s),D=s.data.data,h.dataList=D==null?void 0:D.data,h.pagination.total=D.total,h.loading=!1})},K=async e=>{I.loading=!0;const t=await ze(x.stationId,e);I.dataList=t.data;const o=t==null?void 0:t.data[0];I.currentRow=o,await V(),console.log(I.currentRow),I.loading=!1},V=async()=>{var f,m;const e=I.currentRow,o=(f=(await Ue({deviceId:Z(e.deviceId),attr:e.property})).data)==null?void 0:f.data,l=le(),s=[{name:"前天",key:"beforeYesterdayDataList"},{name:"昨天",key:"yesterdayDataList"},{name:"今天",key:"todayDataList"}];l.xAxis.data=o.todayDataList.map(i=>i.ts),l.yAxis[0].name=e.propertyName.concat(e.unit?"("+e.unit+")":"");const r=s.map(i=>{const g=o[i.key].map(A=>A.value);return{name:i.name,smooth:!0,data:g,type:"line",markPoint:{data:[{type:"max",name:"最大值",label:{fontSize:12,color:N().isDark?"#ffffff":"#000000"}},{type:"min",name:"最小值",label:{color:N().isDark?"#ffffff":"#000000"}}]},markLine:{data:[{type:"average",name:"平均值"}]}}});l.series=r,(m=R.value)==null||m.clear(),await E(()=>{L.value&&z.listenTo(L.value,()=>{var i;a.chartOption=l,(i=R.value)==null||i.resize()})}),ie("hiddenLoading")};return(e,t)=>{const o=Se,l=Le,s=Te("VChart"),r=De,f=Ae,m=Ye,i=Oe,g=je,A=Re,M=Fe,C=Me,ve=Ge,he=_e;return b(),k("div",null,[u("div",{style:we([{position:"absolute",top:"55px",width:"100%","z-index":"9999"},{background:S(N)().isDark?"#1A293C":"#FFFFFF"}])},[n(o,{modelValue:a.activeName,"onUpdate:modelValue":t[0]||(t[0]=Y=>a.activeName=Y),config:me},null,8,["modelValue","config"])],4),u("div",We,[a.activeName==="status"?(b(),k("div",$e,[u("div",Ke,[u("div",Xe,[u("div",Ze,[u("div",qe,[n(l,{config:I,class:"left-table"},null,8,["config"])]),u("div",{ref_key:"echartsDiv",ref:L,class:"chart-box"},[n(s,{ref_key:"refChart",ref:R,theme:S(N)().isDark?"dark":"light",option:a.chartOption},null,8,["theme","option"])],512)])])]),u("div",et,[u("div",tt,[t[3]||(t[3]=u("div",{class:"title"},"现场实景",-1)),n(g,{class:"chart-box",overlay:""},{default:d(()=>[a.imgs.length>0?(b(),q(i,{key:0,trigger:"click",height:"35vh"},{default:d(()=>[n(m,null,{default:d(()=>[(b(!0),k(Ce,null,Ne(a.imgs,Y=>(b(),q(f,{key:Y,src:Y,style:{height:"100%",width:"100%"}},{error:d(()=>[u("div",at,[n(r,null,{default:d(()=>[n(S(ee))]),_:1})])]),_:2},1032,["src"]))),128))]),_:1})]),_:1})):w("",!0),a.imgs.length===0?(b(),k("div",ot,[u("div",lt,[u("div",null,[n(r,{size:"110px",color:"#E4E7F1"},{default:d(()=>[n(S(ee))]),_:1})]),t[2]||(t[2]=u("div",{style:{width:"70%",margin:"20px auto"}},[u("span",{style:{color:"#54728f"}},"请前往"),u("span",{style:{color:"#54728f"}},"“数据平台”>“档案基础数据”>“现场实品图”"),u("span",{style:{color:"#54728f"}},"界面上传实景图")],-1))])])):w("",!0)]),_:1})]),u("div",st,[t[4]||(t[4]=u("div",{class:"title"},"水质监测站信息",-1)),n(g,{class:"chart-box",title:"",overlay:""},{default:d(()=>[n(A,{ref_key:"refAmap",ref:F,light:!S(N)().isDark,"hide-input":!0,"init-center-mark":!1},null,8,["light"])]),_:1})]),u("div",nt,[t[5]||(t[5]=u("div",{class:"title"},"报警信息",-1)),n(g,{class:"chart-box",title:"",overlay:""},{default:d(()=>[n(l,{config:h},null,8,["config"])]),_:1})])])])):w("",!0),a.activeName==="search"?(b(),k("div",rt,[n(g,{class:"",title:" ",overlay:""},{title:d(()=>[n(M,{ref_key:"cardSearch",ref:B,config:T},null,8,["config"])]),default:d(()=>[n(g,{title:" ",class:"card-table"},{right:d(()=>[n(ve,{modelValue:a.searchActiveName,"onUpdate:modelValue":t[1]||(t[1]=Y=>a.searchActiveName=Y)},{default:d(()=>[n(C,{label:"echarts"},{default:d(()=>[n(S(te),{style:{"margin-right":"1px","font-size":"16px"},icon:"clarity:line-chart-line"})]),_:1}),n(C,{label:"list"},{default:d(()=>[n(S(te),{style:{"margin-right":"1px","font-size":"16px"},icon:"material-symbols:table"})]),_:1})]),_:1},8,["modelValue"])]),default:d(()=>[a.searchActiveName==="list"?(b(),k("div",it,[n(l,{ref_key:"refTable",ref:H,config:_,class:"chart-box"},null,8,["config"])])):w("",!0),a.searchActiveName==="echarts"?(b(),k("div",{key:1,ref_key:"echartsDiv",ref:L,class:"chart-box"},[n(s,{ref_key:"refChart1",ref:j,option:a.chartOption1},null,8,["option"])],512)):w("",!0)]),_:1})]),_:1})])):w("",!0),a.activeName==="alarm"?(b(),k("div",ct,[n(g,{title:" ",overlay:""},{title:d(()=>[n(he,{ref_key:"alarmCardSearch",ref:U,config:de},null,8,["config"])]),default:d(()=>[n(l,{ref_key:"refAlarmTable",ref:J,config:h,class:"chart-box"},null,8,["config"])]),_:1})])):w("",!0),a.activeName==="control"?(b(),k("div",ut)):w("",!0)])])}}}),wt=Ee(dt,[["__scopeId","data-v-12c7e6fd"]]);export{wt as default};
