package org.thingsboard.server.dao.maintainCircuit.maintain;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.common.data.utils.DateUtils;
import org.thingsboard.server.dao.model.sql.UserEntity;
import org.thingsboard.server.dao.model.sql.deviceManage.DeviceType;
import org.thingsboard.server.dao.model.sql.maintainCircuit.maintain.MaintainPlanC;
import org.thingsboard.server.dao.model.sql.maintainCircuit.maintain.MaintainPlanM;
import org.thingsboard.server.dao.model.sql.maintainCircuit.maintain.MaintainTaskC;
import org.thingsboard.server.dao.model.sql.maintainCircuit.maintain.MaintainTaskM;
import org.thingsboard.server.dao.sql.deviceType.DeviceTypeMapper;
import org.thingsboard.server.dao.sql.maintainCircuit.maintain.MaintainPlanCMapper;
import org.thingsboard.server.dao.sql.maintainCircuit.maintain.MaintainPlanMMapper;
import org.thingsboard.server.dao.sql.maintainCircuit.maintain.MaintainTaskCMapper;
import org.thingsboard.server.dao.sql.maintainCircuit.maintain.MaintainTaskMMapper;
import org.thingsboard.server.dao.sql.user.UserMapper;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-08-24
 */
@Slf4j
@Service
@Transactional
public class MaintainPlanMServiceImpl implements MaintainPlanMService {

    @Autowired
    private MaintainPlanMMapper maintainPlanMMapper;

    @Autowired
    private MaintainPlanCMapper maintainPlanCMapper;

    @Autowired
    private DeviceTypeMapper deviceTypeMapper;

    @Autowired
    private MaintainTaskMMapper maintainTaskMMapper;

    @Autowired
    private MaintainTaskCMapper maintainTaskCMapper;

    @Autowired
    private UserMapper userMapper;

    @Override
    public PageData getList(String planName, String teamName, String userName, Date startStartTime, Date startEndTime, Date endStartTime, Date endEndTime, int page, int size, String tenantId) {

        List<MaintainPlanM> maintainPlanMList = maintainPlanMMapper.getList(planName, teamName, userName, startStartTime, startEndTime, endStartTime, endEndTime, page, size, tenantId);

        int total = maintainPlanMMapper.getListCount(planName, teamName, userName, startStartTime, startEndTime, endStartTime, endEndTime, tenantId);

        return new PageData(total, maintainPlanMList);
    }

    @Override
    public MaintainPlanM getDetail(String mainId) {
        MaintainPlanM maintainPlanM = maintainPlanMMapper.getById(mainId);
        List<MaintainPlanC> maintainPlanCList = maintainPlanCMapper.getList(maintainPlanM.getId());
        // 所属分类链表
        for (MaintainPlanC maintainPlanC : maintainPlanCList) {
            this.setType(maintainPlanC);
        }

        maintainPlanM.setMaintainPlanCList(maintainPlanCList);

        return maintainPlanM;
    }

    @Override
    public MaintainPlanM save(MaintainPlanM maintainPlanM) {

        try {
            if (maintainPlanM.getEndTime() == null) {
                int executeDays = maintainPlanM.getExecutionDays();
                int intervalDays = maintainPlanM.getIntervalDays();
                int executionNum = maintainPlanM.getExecutionNum();
                long intervalTime = ((executeDays + intervalDays) * executionNum) * (24 * 3600 * 1000l);
                maintainPlanM.setEndTime(new Date(maintainPlanM.getStartTime().getTime() + intervalTime));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (StringUtils.isBlank(maintainPlanM.getId())) {
            maintainPlanM.setStatus("0");
            maintainPlanM.setCreateTime(new Date());

            maintainPlanMMapper.insert(maintainPlanM);
        } else {
            maintainPlanMMapper.updateById(maintainPlanM);
        }

        Map deleteMap = new HashMap<>();
        deleteMap.put("main_id", maintainPlanM.getId());
        maintainPlanCMapper.deleteByMap(deleteMap);

        if (maintainPlanM.getMaintainPlanCList() != null) {
            for (MaintainPlanC maintainPlanC : maintainPlanM.getMaintainPlanCList()) {
                maintainPlanC.setMainId(maintainPlanM.getId());
                maintainPlanC.setTenantId(maintainPlanM.getTenantId());
                maintainPlanC.setCreateTime(new Date());

                maintainPlanCMapper.insert(maintainPlanC);
            }
        }
        // 保存是生成任务，后续需要审核删除该步骤
        try {
            this.execute(maintainPlanM);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return maintainPlanM;
    }

    @Override
    public IstarResponse delete(List<String> ids) {
        maintainPlanMMapper.deleteBatchIds(ids);

        // 删除子表
        QueryWrapper<MaintainPlanC> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("main_id", ids);
        maintainPlanCMapper.delete(queryWrapper);

        return IstarResponse.ok("删除成功");
    }

    @Override
    public void reviewer(MaintainPlanM maintainPlanM) {
        MaintainPlanM maintainPlanM1 = new MaintainPlanM();
        maintainPlanM1.setId(maintainPlanM.getId());
        maintainPlanM1.setStatus(maintainPlanM.getStatus());
        maintainPlanM1.setReviewer(maintainPlanM.getReviewer());

        maintainPlanMMapper.updateById(maintainPlanM1);

        // 通过则生成任务
        if ("2".equals(maintainPlanM.getStatus())) {
            try {
                maintainPlanM = maintainPlanMMapper.selectById(maintainPlanM.getId());
                this.execute(maintainPlanM);
            } catch (ParseException e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public List<MaintainPlanM> findAll() {
        return maintainPlanMMapper.selectByMap(new HashMap<>());
    }

    @Override
    public void execute(MaintainPlanM maintainPlanM) throws ParseException {


        Integer periodTime = maintainPlanM.getIntervalDays(); // 间隔天数
        Integer executionDays = maintainPlanM.getExecutionDays(); // 执行天数
        Integer executionNum = maintainPlanM.getExecutionNum(); // 执行次数
        Date executeTime = maintainPlanM.getStartTime(); // 开始时间

        Date nextExecuteTime = null;

        if (executeTime == null) {
            return;
        }

        String type = "";

        if (periodTime == null || periodTime == 0) {
            nextExecuteTime = executeTime;
            type = "临时任务";
        } else{
            type = "计划任务";
            // 是否超过执行时间
            // if (new Date().getTime() > (executeTime.getTime() + (executionNum * periodTime * 4 * 60 * 60 * 1000l))) {
            //     return;
            // }
            boolean result = false;
            for (int i = 0; i < executionNum; i++) {
                nextExecuteTime = new Date(executeTime.getTime() + (periodTime * i * 24 * 60 * 60 * 1000));
                result = checkTimeIsToday(nextExecuteTime);
                if (result) {// 不在今天
                    break;
                }
            }
            if (!result) {// 不在今天
                return;
            }
        }

        // 查询保养设置子表内容
        List<MaintainPlanC> maintainPlanCList = maintainPlanCMapper.getList(maintainPlanM.getId());
        maintainPlanM.setStartTime(executeTime);
        if (executionDays != null) {
            maintainPlanM.setEndTime(new Date(executeTime.getTime() + (executionDays * 24 * 60 * 60 * 1000)));
        } else {
            if (maintainPlanM.getEndTime() != null) {
                maintainPlanM.setExecutionDays(Long.valueOf((maintainPlanM.getEndTime().getTime() - maintainPlanM.getStartTime().getTime()) / (24 * 60 * 60 * 1000)).intValue());
            }
        }
        // 生成保养任务
        MaintainTaskM maintainTaskM = saveMaintain(maintainPlanM, maintainPlanCList, type);
    }

    private MaintainTaskM saveMaintain(MaintainPlanM maintainPlanM, List<MaintainPlanC> maintainPlanCList, String type) {
        // 保养任务主表
        MaintainTaskM maintainTaskM = new MaintainTaskM();
        BeanUtils.copyProperties(maintainPlanM, maintainTaskM);
        maintainTaskM.setId(null);
        // 新建
        maintainTaskM.setCode("BY" + new SimpleDateFormat("yyyyMMddHHmmssSSS").format(new Date()));
        maintainTaskM.setStatus("0");
        maintainTaskM.setType(type);
        maintainTaskM.setAuditStatus("0");
        maintainTaskM.setAuditor(maintainPlanM.getReviewer());
        maintainTaskM.setCreateTime(new Date());
        maintainTaskM.setTenantId(maintainPlanM.getTenantId());
        maintainTaskM.setStartTime(maintainPlanM.getStartTime());
        maintainTaskM.setEndTime(maintainPlanM.getEndTime());

        maintainTaskMMapper.insert(maintainTaskM);
        // 保养任务子表
        if (maintainPlanCList != null && !maintainPlanCList.isEmpty()) {
            for (MaintainPlanC maintainPlanC : maintainPlanCList) {
                MaintainTaskC maintainTaskC = new MaintainTaskC();
                BeanUtils.copyProperties(maintainPlanC, maintainTaskC);
                maintainTaskC.setId(null);
                maintainTaskC.setMainId(maintainTaskM.getId());
                maintainTaskC.setStatus("0");
                maintainTaskC.setCreateTime(new Date());
                maintainTaskCMapper.insert(maintainTaskC);
            }
        }

        return maintainTaskM;
    }

    private void setType(MaintainPlanC maintainPlanC) {
        if (StringUtils.isBlank(maintainPlanC.getTypeId())) {
            maintainPlanC.setLinkedType("-");
            return;
        }
        String linkedType = "-";
        String topType = "-";
        DeviceType deviceType = null;
        String parentId = maintainPlanC.getTypeId();
        for (int i = 0; i < 5; i++) {
            deviceType = deviceTypeMapper.selectById(parentId);
            if (deviceType == null) {
                break;
            }
            linkedType = deviceType.getName() + ">" + linkedType;
            if (StringUtils.isBlank(deviceType.getParentId())) {
                topType = deviceType.getName();
                break;
            }
            parentId = deviceType.getParentId();
        }
        if (linkedType.length() >= 2) {
            linkedType = linkedType.substring(0, linkedType.length() - 2);
        }

        maintainPlanC.setLinkedType(linkedType);
        maintainPlanC.setTopType(topType);
        maintainPlanC.setType(linkedType);
        try {
            if (linkedType.contains(">")) {
                maintainPlanC.setType(linkedType.substring(linkedType.lastIndexOf(">") + 1));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    private boolean checkTimeIsToday(Date nextExecuteTime) throws ParseException {
        String string = DateUtils.date2Str(new Date(), DateUtils.DATE_FORMATE_DAY);
        SimpleDateFormat dateFormat = new SimpleDateFormat(DateUtils.DATE_FORMATE_DEFAULT);
//        Date todayStart = dateFormat.parse(string + " 00:00:00");
        Date todayEnd = dateFormat.parse(string + " 23:59:59");

        if (/*nextExecuteTime.getTime() > todayStart.getTime() && */nextExecuteTime.getTime() < todayEnd.getTime()) {
            return true;
        }

        return false;
    }


    @Override
    public PageData<Map> getMaintainList(String deviceLabelCode, int page, int size) {
        List<Map> maintainList = maintainPlanCMapper.getMaintainList(deviceLabelCode, page, size);

        for (Map map : maintainList) {
            // 任务人
            String userIds = (String) map.get("userId");
            String userName = "";
            if (StringUtils.isNotBlank(userIds)) {
                String[] userIdStr = userIds.split(",");
                UserEntity userEntity;
                for (String userId : userIdStr) {
                    userEntity = userMapper.selectById(userId);
                    if (userEntity != null) {
                        userName = userName + "," + userEntity.getFirstName();
                    }
                }
                if (userName.contains(",")) {
                    userName = userName.substring(1);
                }
            }
            map.put("userName", userName);

            // 下一次巡检时间
            Date startTime = (Date) map.get("startTime");
            Integer intervalDays = (Integer) map.get("intervalDays");
            Integer executionNum = (Integer) map.get("executionNum");
            Date nextTime;
            map.put("nextTime", "-");
            if (executionNum != null && intervalDays != null) {
                for (int i = 0; i < executionNum; i++) {
                    nextTime = new Date(startTime.getTime() + (intervalDays * i * 24 * 60 * 60 * 1000));
                    if (nextTime.getTime() > System.currentTimeMillis()) {
                        map.put("nextTime", nextTime);
                        break;
                    }
                }
            }

        }
        int total = maintainPlanCMapper.getMaintainListCount(deviceLabelCode);

        return new PageData<>(total, maintainList);
    }

}
