/**
 * Copyright © 2016-2019 The Thingsboard Authors
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.msg;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.MsgConfigEntity;
import org.thingsboard.server.dao.model.sql.MsgTemplateEntity;
import org.thingsboard.server.dao.sql.msg.MsgConfigMapper;
import org.thingsboard.server.dao.sql.msg.MsgTemplateMapper;

import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class MsgConfigServiceImpl implements MsgConfigService {


    @Autowired
    private MsgConfigMapper msgConfigMapper;

    @Autowired
    private MsgTemplateMapper msgTemplateMapper;

    @Override
    public MsgConfigEntity save(MsgConfigEntity msgConfigEntity) {
        if (StringUtils.isBlank(msgConfigEntity.getId())) {
            msgConfigEntity.setCreateTime(new Date());
            msgConfigMapper.insert(msgConfigEntity);

            return msgConfigEntity;
        }

        msgConfigMapper.updateById(msgConfigEntity);
        return msgConfigEntity;
    }

    @Override
    public PageData<MsgConfigEntity> getList(int page, int size, String name, String tenantId) {
        IPage<MsgConfigEntity> iPage = new Page<>(page, size);

        Page<MsgConfigEntity> list = msgConfigMapper.getList(iPage, name, tenantId);
        return new PageData<>(list.getTotal(), list.getRecords());
    }

    @Override
    public boolean delete(List<String> ids) {
        boolean result = true;
        for (String id : ids) {
            List<MsgTemplateEntity> msgTemplateEntities = msgTemplateMapper.selectByConfigId(id);
            if (msgTemplateEntities.size() > 0) {
                result = false;
                continue;
            }

        msgConfigMapper.deleteById(id);
        }

        return result;
    }
}
