package org.thingsboard.server.dao.sql.input;

import org.springframework.data.jpa.repository.JpaRepository;
import org.thingsboard.server.dao.model.sql.input.InputProductPlan;

import java.util.Date;
import java.util.List;

public interface InputProductPlanRepository extends JpaRepository<InputProductPlan, String> {
    List<InputProductPlan> findByTimeBetweenOrderByTimeAsc(Date monthStart, Date monthEnd);
}
