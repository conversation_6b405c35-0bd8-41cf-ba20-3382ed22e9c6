import{_ as k}from"./index-C9hz-UZb.js";import{d as S,r as d,c as C,a8 as I,bX as w,o as x,ah as T,g as i,n as D,q as f,i as a,F as b,p as V,h as s,an as r,c5 as P,C as B}from"./index-r0dFAfgr.js";import{_ as j}from"./CardSearch-CB_HNR-Q.js";import{b as A}from"./zhandian-YaGuQZe6.js";import{u as E}from"./useStation-DJgnSZIA.js";import F from"./smallFlow-tHEMBdV5.js";import L from"./comparedSame-Dj3GNgaF.js";import q from"./distributionTable-BCAMF7Oi.js";import G from"./peakValley-BNPVj76f.js";import H from"./sequential-Dyc-i8Af.js";import M from"./timeContrast-GFd0Cix-.js";import"./Search-NSrhrIa_.js";import"./CardTable-rdWOL4_6.js";import"./echart-DxEZmJvB.js";import"./flowMonitoring-DtJlPj0G.js";import"./DateFormatter-Bm9a68Ax.js";const U={class:"wrapper"},X={class:"tab"},$=S({__name:"index",setup(z){const{getStationTree:v,getStationTreeByDisabledType:y}=E(),t=d({activeName:"配表分析",stationId:"",stationName:""}),_=C(),u=d({defaultParams:{},filters:[{type:"select-tree",field:"treeData",checkStrictly:!0,defaultExpandAll:!0,options:I(()=>l.data),label:"站点选择",onChange:async e=>{const o=w(l.data,"children","id",e);l.currentProject=o,t.stationId=o.id,t.stationName=o.label,await g()}}]}),h=d({type:"tabs",tabType:"simple",width:"100%",tabs:[{label:"配表分析",value:"配表分析"},{label:"谷峰分析",value:"谷峰分析"},{label:"时段分析",value:"时段分析"},{label:"同比曲线",value:"同比曲线"},{label:"环比曲线",value:"环比曲线"}],handleTabClick:e=>{t.activeName=e.props.name}}),l=d({data:[],title:"区域划分",defaultExpandAll:!0,nodeExpand:async(e,o)=>{var n,c;if(((n=e.data)==null?void 0:n.type)==="Station"){const m=await A({stationId:e.id});console.log(m.data);const p=(c=m.data)==null?void 0:c.map(N=>({label:N.type,value:"",id:"",disabled:!0,children:N.attrList}));o.data.children=p}},treeNodeHandleClick:async e=>{console.log(e),l.currentProject=e,t.stationId=e.id,t.stationName=e.label}}),g=async()=>{};return x(async()=>{var n;const e=await v("流量监测站,测流压站");await y(e,["Project"],!1,"Station"),l.data=e;const o=T(e);u.defaultParams={...u.defaultParams,treeData:o.id},(n=_.value)==null||n.resetForm(),t.stationId=o.id,t.stationName=o.label}),(e,o)=>{const n=j,c=P,m=k;return i(),D("div",U,[f(n,{ref_key:"refSearch",ref:_,config:a(u)},null,8,["config"]),f(m,{class:"card",title:" "},{title:b(()=>[f(c,{modelValue:a(t).activeName,"onUpdate:modelValue":o[0]||(o[0]=p=>a(t).activeName=p),config:a(h)},null,8,["modelValue","config"])]),default:b(()=>[V("div",X,[a(t).activeName==="配表分析"?(i(),s(q,{key:0,"station-name":a(t).stationName,"station-id":a(t).stationId},null,8,["station-name","station-id"])):r("",!0),a(t).activeName==="谷峰分析"?(i(),s(G,{key:1,"station-name":a(t).stationName,"station-id":a(t).stationId},null,8,["station-name","station-id"])):r("",!0),a(t).activeName==="时段分析"?(i(),s(M,{key:2,"station-name":a(t).stationName,"station-id":a(t).stationId},null,8,["station-name","station-id"])):r("",!0),a(t).activeName==="同比曲线"?(i(),s(L,{key:3,"station-name":a(t).stationName,"station-id":a(t).stationId},null,8,["station-name","station-id"])):r("",!0),a(t).activeName==="环比曲线"?(i(),s(H,{key:4,"station-name":a(t).stationName,"station-id":a(t).stationId},null,8,["station-name","station-id"])):r("",!0),a(t).activeName==="小流分析"?(i(),s(F,{key:5,"station-name":a(t).stationName,"station-id":a(t).stationId},null,8,["station-name","station-id"])):r("",!0)])]),_:1})])}}}),lt=B($,[["__scopeId","data-v-1f3291a0"]]);export{lt as default};
