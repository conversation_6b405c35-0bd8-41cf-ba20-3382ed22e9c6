package org.thingsboard.server.dao.sql.componentStorage;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.thingsboard.server.dao.model.sql.ComponentStorageEntity;

import java.util.List;

public interface ComponentStorageRepository extends JpaRepository<ComponentStorageEntity, String> {

    @Query("SELECT cs FROM ComponentStorageEntity cs " +
            "WHERE cs.tenantId = ?4 AND cs.code LIKE %?1% and cs.name LIKE %?2% AND cs.type LIKE ?3 AND cs.isDel = '0'")
    Page<ComponentStorageEntity> findList(String code, String name, String type, String tenantId, Pageable pageable);

    @Query("SELECT cs FROM ComponentStorageEntity cs " +
            "WHERE cs.tenantId = ?1 AND cs.isDel = '0'")
    List<ComponentStorageEntity> findAll(String tenantId);

    @Query("SELECT cs.specification FROM ComponentStorageEntity cs " +
            "WHERE cs.tenantId = ?1 AND cs.isDel = '0' GROUP BY cs.specification")
    List<String> specificationList(String tenantId);

    ComponentStorageEntity findByCodeAndIsDel(String code, String isDel);
}
