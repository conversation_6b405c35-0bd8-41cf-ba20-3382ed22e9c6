<template>
  <el-drawer
    :id="id"
    :key="key"
    v-model="show"
    :title="props.config.title || ''"
    :modal-class="(props.config.modalClass || '') + ' SLDrawer'"
    :size="props.config.width || '75%'"
    :close-on-click-modal="props.config.modalClose"
    :close-on-press-escape="props.config.escapeClose"
    :close-delay="props.config.closeDelay"
    :append-to-body="props.config.appendToBody"
    :before-close="props.config.beforeClose"
    :destroy-on-close="props.config.destroyOnClose"
    :direction="props.config.direction || 'rtl'"
    :lock-scroll="props.config.lockScroll"
    :modal="props.config.modal"
    :show-close="props.config.showClose"
    :with-header="props.config.withHeader"
    :class="props.config.className"
    @close="props.config.onClose"
    @closed="props.config.onClosed"
    @open="props.config.onOpen"
    @opened="props.config.onOpened"
  >
    <template
      v-if="!props.config.title"
      #header
    >
      <div class="slot-title">
        <slot name="title">
        </slot>
      </div>
    </template>

    <slot>
      <Form
        ref="refForm"
        :config="props.config"
      />
    </slot>
    <template
      v-if="config.cancel !== false || config.submit"
      #footer
    >
      <span class="dialog-footer">
        <template v-if="config.btns">
          <Button
            v-for="(item, i) in config.btns"
            :key="i"
            :config="item"
            :size="item?.size || 'default'"
            :loading="config.submitting"
          ></Button>
        </template>
        <el-button
          v-if="config?.print || false"
          type="primary"
          size="default"
          @click="handlePrint"
        >
          打 印</el-button>
        <el-button
          v-if="config.submit"
          type="primary"
          size="default"
          :loading="config.submitting"
          @click="Submit(false)"
        >
          {{ config.submitText || '确 定' }}
        </el-button>
        <el-button
          v-if="config.cancel !== false"
          size="default"
          @click="closeDrawer"
        >关 闭</el-button>

      </span>
    </template>
    <div
      v-if="props.config.showBar"
      class="drawer-bar"
    ></div>
  </el-drawer>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs'
// import { printHTML } from '@/utils/printUtils'
import Form from '../Form/Form.vue'
import { outPutPdf } from '@/utils/outPdf'

const id = dayjs().valueOf()

const refForm = ref<InstanceType<typeof Form>>()

const props = defineProps<{
  config: IDrawerConfig
}>()

const key = ref(dayjs(new Date()).valueOf())

const show = ref(false)

/**
 * 打开抽屉
 * 打开时默认重置表单
 */
const openDrawer = () => {
  key.value = dayjs(new Date()).valueOf()
  show.value = true
}

/**
 * 关闭抽屉
 */
const closeDrawer = () => {
  show.value = false
}
const toggle = (open?: boolean) => {
  if (open === undefined) show.value = !show.value
  else show.value = open
}
// 保存 save
const Submit = (status?: boolean) => {
  // 插槽下处理
  if (refForm.value === undefined && props.config.submit) {
    props.config?.submit(false)
  } else {
    refForm.value?.Submit(status)
  }
}

// 重置
const resetForm = () => {
  refForm.value?.refForm?.resetFields()
}

/**
 * 打印报表
 */
const handlePrint = () => {
  outPutPdf(id, '', false, true, res => {
    console.log(res)
  })
}

watch(
  () => show.value,
  (newVal: boolean) => {
    if (newVal) refForm.value?.resetForm()
  }
)

defineExpose({
  refForm,
  openDrawer,
  closeDrawer,
  Submit,
  resetForm,
  toggle
})
</script>

<style lang="scss">
.SLDrawer {
  height: calc(100% - 52px) !important;
  margin-top: 52px;

  .drawer-bar {
    position: absolute;
    top: 30%;
    left: 0;
    transform: translateX(-100%);
    width: 24px;
    height: 80px;
    background-color: red;
  }

  .el-drawer__header {
    margin-bottom: 20px;
  }

  .slot-title {
    width: 100%;
    display: flex;
    align-items: center;
  }
}

.lightColor {

  .el-drawer__body {
    background-color: var(--el-fill-color-light);
    height: 100%;
  }

  .el-drawer__footer {
    background-color: var(--el-fill-color-light);
  }
}
</style>
