<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.smartService.call.CallLogMapper">
    <select id="getList" resultType="org.thingsboard.server.dao.model.sql.smartService.call.CallLog">
        select a.*, coalesce(c.serial_no, d.serial_no) as serialNo, d.source as sourceName, f.name as area, d.type as workOrderType, d.topic as topicName, i.first_name as seatsName,
        coalesce(extract( epoch from a.end_time - a.call_time)::int4, 0) as callDuration
        from tb_service_call_log a
        left join tb_service_work_order d on d.call_id = a.id
        left join tb_service_seats_user b on a.user_id = b.user_id
        left join tb_user i on a.user_id = i.id
        left join work_order c on d.work_order_id = c.id
        left join tb_service_system_dict e on d.source = e.id
        left join tb_service_system_dict f on d.area = f.id
        left join tb_service_system_work_order_type g on d.type = g.id
        left join tb_service_system_work_order_type h on d.topic = h.id
        where a.phone like '%'||#{phone}||'%'
        <if test="startTime != null">
            and a.listen_time &gt;= to_timestamp(#{startTime} / 1000)
        </if>
        <if test="endTime != null">
            and a.listen_time &lt;= to_timestamp(#{endTime} / 1000)
        </if>
        <if test="source != null and source != ''">
            and e.id = #{source}
        </if>
        <if test="area != null and area != ''">
            and f.id = #{area}
        </if>
        <if test="type != null and type != ''">
            and g.id = #{type}
        </if>
        <if test="topic != null and topic != ''">
            and h.id = #{topic}
        </if>
        <if test="seatsId != null and seatsId != ''">
            and b.id = #{seatsId}
        </if>
        <if test="status != null and status != ''">
            and a.status= #{status}
        </if>
        <if test="direction != null and direction != ''">
            and a.direction= #{direction}
        </if>
        order by a.call_time desc
        offset (#{page} - 1) * #{size}  limit #{size}
    </select>

    <select id="getListCount" resultType="int">
        select count(*)
        from tb_service_call_log a
        left join tb_service_work_order d on d.call_id = a.id
        left join tb_service_seats_user b on a.user_id = b.user_id
        left join tb_user i on a.user_id = i.id
        left join work_order c on d.work_order_id = c.id
        left join tb_service_system_dict e on d.source = e.id
        left join tb_service_system_dict f on d.area = f.id
        left join tb_service_system_work_order_type g on d.type = g.id
        left join tb_service_system_work_order_type h on d.topic = h.id
        where a.phone like '%'||#{phone}||'%'
        <if test="startTime != null">
            and a.listen_time &gt;= to_timestamp(#{startTime} / 1000)
        </if>
        <if test="endTime != null">
            and a.listen_time &lt;= to_timestamp(#{endTime} / 1000)
        </if>
        <if test="source != null and source != ''">
            and e.id = #{source}
        </if>
        <if test="area != null and area != ''">
            and f.id = #{area}
        </if>
        <if test="type != null and type != ''">
            and g.id = #{type}
        </if>
        <if test="topic != null and topic != ''">
            and h.id = #{topic}
        </if>
        <if test="seatsId != null and seatsId != ''">
            and b.id = #{seatsId}
        </if>
        <if test="status != null and status != ''">
            and a.status= #{status}
        </if>
        <if test="direction != null and direction != ''">
            and a.direction= #{direction}
        </if>
    </select>

    <select id="getQueueMonitor" resultType="org.thingsboard.server.dao.model.sql.smartService.call.CallLog">
        select * from tb_service_call_log a
        <if test="day != null and day != ''">
            where a.call_time &gt; to_timestamp(#{day} || ' 00:00:00', 'YYYY-MM-DD HH24:MI:SS')
        and a.call_time &lt;= to_timestamp(#{day} || ' 23:59:59', 'YYYY-MM-DD HH24:MI:SS')
        </if>
        order by a.call_time asc
    </select>
</mapper>