package org.thingsboard.server.dao.model.sql.smartManagement.district;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;
import org.thingsboard.server.dao.util.imodel.response.tree.Identifiable;

@Getter
@Setter
@ResponseEntity
@TableName("sm_circuit_district")
public class SMCircuitDistrict implements Identifiable {
    // id
    @TableId
    private String id;

    // 片区名字
    private String name;

    // 父级Id
    private String parentId;

    // 备注
    private String remark;

    // 租户Id
    private String tenantId;

}
