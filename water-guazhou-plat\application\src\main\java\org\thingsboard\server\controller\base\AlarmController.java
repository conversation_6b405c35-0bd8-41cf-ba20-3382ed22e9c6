/**
 * Copyright © 2016-2019 The Thingsboard Authors
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.controller.base;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.repository.query.Param;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.Device;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.VO.DeviceAlarmMsg;
import org.thingsboard.server.common.data.VO.TenantAlarmMsg;
import org.thingsboard.server.common.data.alarm.*;
import org.thingsboard.server.common.data.exception.ThingsboardErrorCode;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.DeviceId;
import org.thingsboard.server.common.data.id.EntityId;
import org.thingsboard.server.common.data.id.EntityIdFactory;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.common.data.page.TimePageData;
import org.thingsboard.server.common.data.page.TimePageLink;
import org.thingsboard.server.common.data.utils.DateUtils;
import org.thingsboard.server.common.data.utils.TimeDiff;
import org.thingsboard.server.dao.model.sql.ProjectEntity;
import org.thingsboard.server.dao.model.sql.ProjectRelationEntity;
import org.thingsboard.server.dao.model.sql.StationEntity;
import org.thingsboard.server.dao.project.ProjectRelationService;
import org.thingsboard.server.dao.project.ProjectService;
import org.thingsboard.server.dao.sql.device.DeviceRepository;
import org.thingsboard.server.dao.station.StationService;
import org.thingsboard.server.dao.util.mapping.JacksonUtil;
import org.thingsboard.server.service.aspect.annotation.SysLog;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api")
public class AlarmController extends BaseController {

    public static final String ALARM_ID = "alarmId";

    @Autowired
    private DeviceRepository deviceRepository;
    @Autowired
    private ProjectRelationService projectRelationService;
    @Autowired
    private ProjectService projectService;
    @Autowired
    private StationService stationService;

    @PreAuthorize("hasAnyAuthority('SYS_ADMIN',  'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE', 'TENANT_SYS', 'CUSTOMER_USER')")
    @RequestMapping(value = "/alarm/{alarmId}", method = RequestMethod.GET)
    @ResponseBody
    @SysLog(detail = "查询告警记录")
    public Alarm getAlarmById(@PathVariable(ALARM_ID) String strAlarmId) throws ThingsboardException {
        checkParameter(ALARM_ID, strAlarmId);
        try {
            AlarmId alarmId = new AlarmId(toUUID(strAlarmId));

            return checkAlarmId(alarmId);
        } catch (Exception e) {
            throw handleException(e);
        }
    }

    @PreAuthorize("hasAnyAuthority('SYS_ADMIN',  'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE', 'TENANT_SYS', 'CUSTOMER_USER')")
    @RequestMapping(value = "/alarm/info/{alarmId}", method = RequestMethod.GET)
    @ResponseBody
    @SysLog(detail = "查询告警记录")
    public AlarmInfo getAlarmInfoById(@PathVariable(ALARM_ID) String strAlarmId) throws ThingsboardException {
        checkParameter(ALARM_ID, strAlarmId);
        try {
            AlarmId alarmId = new AlarmId(toUUID(strAlarmId));
            return checkAlarmInfoId(alarmId);
        } catch (Exception e) {
            throw handleException(e);
        }
    }

    @PreAuthorize("hasAnyAuthority('SYS_ADMIN',  'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE', 'TENANT_SYS', 'CUSTOMER_USER')")
    @RequestMapping(value = "/alarm", method = RequestMethod.POST)
    @ResponseBody
    @SysLog(options = DataConstants.OPERATING_TYPE.OPERATING_TYPE_OPTIONS_ALARM_ADD)
    public Alarm saveAlarm(@RequestBody Alarm alarm) throws ThingsboardException {
        try {
            alarm.setTenantId(getTenantId());
            return checkNotNull(alarmService.createOrUpdateAlarm(alarm));
        } catch (Exception e) {
            throw handleException(e);
        }
    }

    @PreAuthorize("hasAnyAuthority('SYS_ADMIN',  'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE', 'TENANT_SYS', 'CUSTOMER_USER')")
    @RequestMapping(value = "/alarm/{alarmId}/ack", method = RequestMethod.POST)
    @ResponseStatus(value = HttpStatus.OK)
    @SysLog(options = DataConstants.OPERATING_TYPE.OPERATING_TYPE_OPTIONS_ALARM_ACK)
    public void ackAlarm(@PathVariable(ALARM_ID) String strAlarmId) throws ThingsboardException {
        checkParameter(ALARM_ID, strAlarmId);
        try {
            AlarmId alarmId = new AlarmId(toUUID(strAlarmId));
            checkAlarmId(alarmId);
            alarmService.ackAlarm(getTenantId(), alarmId, System.currentTimeMillis()).get();
        } catch (Exception e) {
            throw handleException(e);
        }
    }

    @PreAuthorize("hasAnyAuthority('SYS_ADMIN',  'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE', 'TENANT_SYS', 'CUSTOMER_USER')")
    @RequestMapping(value = "/alarm/{alarmId}/clear", method = RequestMethod.POST)
    @ResponseStatus(value = HttpStatus.OK)
    @SysLog(options = DataConstants.OPERATING_TYPE.OPERATING_TYPE_OPTIONS_ALARM_CLEAR)
    public void clearAlarm(@PathVariable(ALARM_ID) String strAlarmId) throws ThingsboardException {
        checkParameter(ALARM_ID, strAlarmId);
        try {
            AlarmId alarmId = new AlarmId(toUUID(strAlarmId));
            checkAlarmId(alarmId);
            alarmService.clearAlarm(getTenantId(), alarmId, null, System.currentTimeMillis()).get();
        } catch (Exception e) {
            throw handleException(e);
        }
    }

    @PreAuthorize("hasAnyAuthority('SYS_ADMIN',  'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE', 'TENANT_SYS', 'CUSTOMER_USER')")
    @RequestMapping(value = "/alarm/{entityType}/{entityId}", method = RequestMethod.GET)
    @ResponseBody
    @SysLog(detail = "查询告警记录列表")
    public TimePageData<AlarmInfo> getAlarms(
            @PathVariable("entityType") String strEntityType,
            @PathVariable("entityId") String strEntityId,
            @RequestParam(required = false) String searchStatus,
            @RequestParam(required = false) String status,
            @RequestParam int limit,
            @RequestParam(required = false) Long startTime,
            @RequestParam(required = false) Long endTime,
            @RequestParam(required = false, defaultValue = "false") boolean ascOrder,
            @RequestParam(required = false) String offset,
            @RequestParam(required = false) Boolean fetchOriginator
    ) throws ThingsboardException {
        checkParameter("EntityId", strEntityId);
        checkParameter("EntityType", strEntityType);
        EntityId entityId = EntityIdFactory.getByTypeAndId(strEntityType, strEntityId);
        AlarmSearchStatus alarmSearchStatus = StringUtils.isEmpty(searchStatus) ? null : AlarmSearchStatus.valueOf(searchStatus);
        AlarmStatus alarmStatus = StringUtils.isEmpty(status) ? null : AlarmStatus.valueOf(status);
        if (alarmSearchStatus != null && alarmStatus != null) {
            throw new ThingsboardException("Invalid alarms search query: Both parameters 'searchStatus' " +
                    "and 'status' can't be specified at the same time!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }
        checkEntityId(entityId);
        try {
            TimePageLink pageLink = createPageLink(limit, startTime, endTime, ascOrder, offset);
            return checkNotNull(alarmService.findAlarms(getTenantId(), new AlarmQuery(entityId, pageLink, alarmSearchStatus, alarmStatus, fetchOriginator)).get());
        } catch (Exception e) {
            throw handleException(e);
        }
    }

    @PreAuthorize("hasAnyAuthority('SYS_ADMIN',  'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE', 'TENANT_SYS', 'CUSTOMER_USER')")
    @RequestMapping(value = "/alarm/highestSeverity/{entityType}/{entityId}", method = RequestMethod.GET)
    @ResponseBody
    @SysLog(detail = "获取最高告警级别")
    public String getHighestAlarmSeverity(
            @PathVariable("entityType") String strEntityType,
            @PathVariable("entityId") String strEntityId,
            @RequestParam(required = false) String searchStatus,
            @RequestParam(required = false) String status
    ) throws ThingsboardException {
        checkParameter("EntityId", strEntityId);
        checkParameter("EntityType", strEntityType);
        EntityId entityId = EntityIdFactory.getByTypeAndId(strEntityType, strEntityId);
        AlarmSearchStatus alarmSearchStatus = StringUtils.isEmpty(searchStatus) ? null : AlarmSearchStatus.valueOf(searchStatus);
        AlarmStatus alarmStatus = StringUtils.isEmpty(status) ? null : AlarmStatus.valueOf(status);
        if (alarmSearchStatus != null && alarmStatus != null) {
            throw new ThingsboardException("Invalid alarms search query: Both parameters 'searchStatus' " +
                    "and 'status' can't be specified at the same time!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }
        checkEntityId(entityId);
        try {
            return alarmService.findHighestAlarmSeverity(getTenantId(), entityId, alarmSearchStatus, alarmStatus);
        } catch (Exception e) {
            throw handleException(e);
        }
    }

    @PreAuthorize("hasAnyAuthority('SYS_ADMIN',  'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE', 'TENANT_SYS', 'CUSTOMER_USER')")
    @RequestMapping(value = "/alarm/getAlarm", method = RequestMethod.GET)
    @SysLog(detail = "查询告警记录列表")
    @ResponseBody
    public List<Alarm> getAlarm(
//            @RequestParam(required = false) String deviceId,
//            @RequestParam(required = false) String type,
//            @RequestParam(required = false) String level,
//            @RequestParam(required = false) String status,
            @RequestParam(required = false) long start,
            @RequestParam(required = false) long end
    ) throws ThingsboardException {
        return alarmService.findClearAlarmByTenantAndLevel(getTenantId(), null, null, null, null, start, end)
                .stream().filter(f -> f.getStatus() == AlarmStatus.CLEAR_FORCED || f.getStatus() == AlarmStatus.CLEARED_ACK).collect(Collectors.toList());
    }


    @PreAuthorize("hasAnyAuthority('SYS_ADMIN',  'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE', 'TENANT_SYS', 'CUSTOMER_USER')")
    @RequestMapping(value = "/alarm/getAlarm", method = RequestMethod.POST)
    @SysLog(detail = DataConstants.OPERATING_TYPE_ALARM_INFO)
    @ResponseBody
    public List<Alarm> getAlarm(
            @RequestBody JsonNode jsonNode
//            @RequestParam(required = false) String deviceId,
//            @RequestParam(required = false) String type,
//            @RequestParam(required = false) String level,
//            @RequestParam(required = false) String status,
//            @RequestParam(required = false) long start,
//            @RequestParam(required = false) long end
    ) throws ThingsboardException {
        List<DeviceId> deviceIds = new ArrayList<>();
        Iterator<JsonNode> jsonNodeIterator = jsonNode.get("deviceId").elements();
        jsonNodeIterator.forEachRemaining(jsonNode1 -> {
            deviceIds.add(new DeviceId(UUIDConverter.fromString(jsonNode1.asText())));
        });
        String type = jsonNode.get("type") == null ? null : jsonNode.get("type").asText();
        String level = jsonNode.get("level") == null ? null : jsonNode.get("level").asText();
        String status = jsonNode.get("status") == null ? null : jsonNode.get("status").asText();
        long start = jsonNode.get("start") == null ? 0 : jsonNode.get("start").asLong();
        long end = jsonNode.get("end") == null ? 0 : jsonNode.get("end").asLong();
        return alarmService.findClearAlarmByTenantAndLevel(getTenantId(), deviceIds, type, level, status, start, end)
                .stream().filter(f -> f.getStatus() == AlarmStatus.CLEAR_FORCED || f.getStatus() == AlarmStatus.CLEARED_ACK).collect(Collectors.toList());
    }

    @PreAuthorize("hasAnyAuthority('SYS_ADMIN',  'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE', 'TENANT_SYS', 'CUSTOMER_USER')")
    @RequestMapping(value = "/alarm/getAlarm/projectId", method = RequestMethod.POST)
    @SysLog(detail = DataConstants.OPERATING_TYPE_ALARM_INFO)
    @ResponseBody
    public List<Alarm> getAlarmByProjectId(@RequestBody JsonNode jsonNode) throws ThingsboardException {
        List<DeviceId> deviceIds = new ArrayList<>();
        if (jsonNode.get("deviceId") != null) {
            Iterator<JsonNode> jsonNodeIterator = jsonNode.get("deviceId").elements();
            jsonNodeIterator.forEachRemaining(jsonNode1 -> {
                deviceIds.add(new DeviceId(UUIDConverter.fromString(jsonNode1.asText())));
            });
        }

        String type = jsonNode.get("type") == null ? null : jsonNode.get("type").asText();
        String level = jsonNode.get("level") == null ? null : jsonNode.get("level").asText();
        String status = jsonNode.get("status") == null ? null : jsonNode.get("status").asText();
        long start = jsonNode.get("start") == null ? 0 : jsonNode.get("start").asLong();
        long end = jsonNode.get("end") == null ? 0 : jsonNode.get("end").asLong();
        String projectId = jsonNode.get("projectId") == null ? null : jsonNode.get("projectId").asText();
        return alarmService.findClearAlarmByProjectIdAndLevel(projectId, deviceIds, type, level, status, start, end)
                .stream().filter(f -> f.getStatus() == AlarmStatus.CLEAR_FORCED || f.getStatus() == AlarmStatus.CLEARED_ACK).collect(Collectors.toList());
    }

    @PreAuthorize("hasAnyAuthority('SYS_ADMIN',  'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE', 'TENANT_SYS', 'CUSTOMER_USER')")
    @RequestMapping(value = "/alarm/getAlarm/projectId/page", method = RequestMethod.POST)
    @SysLog(detail = DataConstants.OPERATING_TYPE_ALARM_INFO)
    @ResponseBody
    public PageData<Alarm> getAlarmPageByProjectId(@RequestBody JsonNode jsonNode) throws ThingsboardException {
        List<DeviceId> deviceIds = new ArrayList<>();
        if (jsonNode.get("deviceId") != null) {
            Iterator<JsonNode> jsonNodeIterator = jsonNode.get("deviceId").elements();
            jsonNodeIterator.forEachRemaining(jsonNode1 -> {
                deviceIds.add(new DeviceId(UUIDConverter.fromString(jsonNode1.asText())));
            });
        }

        String type = jsonNode.get("type") == null ? null : jsonNode.get("type").asText();
        String level = jsonNode.get("level") == null ? null : jsonNode.get("level").asText();
        String status = jsonNode.get("status") == null ? null : jsonNode.get("status").asText();
        long start = jsonNode.get("start") == null ? 0 : jsonNode.get("start").asLong();
        long end = jsonNode.get("end") == null ? 0 : jsonNode.get("end").asLong();
        int page = jsonNode.get("page") == null ? 1 : jsonNode.get("page").asInt();
        int size = jsonNode.get("size") == null ? 20 : jsonNode.get("size").asInt();
        String projectId = jsonNode.get("projectId") == null ? null : jsonNode.get("projectId").asText();

        String keyword = jsonNode.get("keyword") == null ? "" : jsonNode.get("keyword").asText();


        List<Alarm> list = alarmService.findClearAlarmByProjectIdAndLevel(projectId, deviceIds, type, level, status, start, end)
                .stream().filter(f -> f.getStatus() == AlarmStatus.CLEAR_FORCED || f.getStatus() == AlarmStatus.CLEARED_ACK).filter(e -> (e.getAlarmJsonName() + e.getDeviceName()).contains(keyword)).collect(Collectors.toList());
        return PageData.page(list, page, size);
    }

    /**
     * 强行解除报警
     *
     * @param jsonNode
     * @return
     */
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN',  'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE', 'TENANT_SYS', 'CUSTOMER_USER')")
    @RequestMapping(value = "/alarm/clear", method = RequestMethod.POST)
    @SysLog(options = DataConstants.OPERATING_TYPE.OPERATING_TYPE_OPTIONS_ALARM_CLEAR)
    @ResponseBody
    public List<Alarm> clearAlarm(@RequestBody JsonNode jsonNode) {
        Iterator<JsonNode> iterator = jsonNode.get("alarmId").elements();
        List<Alarm> alarms = new ArrayList<>();
        //先找出待处理的报警信息
        iterator.forEachRemaining(alarm -> {
            try {
                alarms.add(alarmService.findAlarmByIdAsync(new AlarmId(UUIDConverter.fromString(alarm.asText()))).get());
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
        List<Alarm> result = new ArrayList<>();
        //处理报警信息
        alarms.forEach(alarm -> {
            alarm.setClearTs(new Date().getTime());
            alarm.setStatus(AlarmStatus.CLEAR_FORCED);
            JsonNode json = alarm.getDetails();
            if (json == null) {
                AlarmType ala = new AlarmType("clear");
                json = JacksonUtil.toJsonNode(JacksonUtil.toString(ala));
            }
            ObjectNode objectNode = (ObjectNode) json;
            try {
                objectNode.put("dismissal", getCurrentUser().getName());
                objectNode.put("clearRemarks", jsonNode.get("remark") != null ? jsonNode.get("remark").asText() : null);
            } catch (ThingsboardException e) {
                e.printStackTrace();
            }
            alarm.setDetails(objectNode);
            result.add(alarmService.save(alarm));
        });
        return result;
    }


    /**
     * 确认报警
     *
     * @param jsonNode
     * @return
     */
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN',  'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE', 'TENANT_SYS', 'CUSTOMER_USER')")
    @RequestMapping(value = "/alarm/confirm", method = RequestMethod.POST)
    @SysLog(options = DataConstants.OPERATING_TYPE.OPERATING_TYPE_OPTIONS_ALARM_ACK)
    @ResponseBody
    public List<Alarm> confirAlarm(@RequestBody JsonNode jsonNode) {
        Iterator<JsonNode> iterator = jsonNode.get("alarmId").elements();
        List<Alarm> alarms = new ArrayList<>();
        //先找出待处理的报警信息
        iterator.forEachRemaining(alarm -> {
            try {
                alarms.add(alarmService.findAlarmByIdAsync(new AlarmId(UUIDConverter.fromString(alarm.asText()))).get());
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
        List<Alarm> result = new ArrayList<>();
        //处理报警信息
        alarms.forEach(alarm -> {
            alarm.setClearTs(System.currentTimeMillis());
            if (alarm.getStatus() == AlarmStatus.CONFIRM_UNACK)
                alarm.setStatus(AlarmStatus.CONFIRM_ACK);
            else if (alarm.getStatus() == AlarmStatus.RESTORE_ACK)
                alarm.setStatus(AlarmStatus.CLEARED_ACK);
            JsonNode json = alarm.getDetails();
            if (json == null) {
                AlarmType ala = new AlarmType("clear");
                json = JacksonUtil.toJsonNode(JacksonUtil.toString(ala));
            }
            ObjectNode objectNode = (ObjectNode) json;
            try {
                objectNode.put("confirm", getCurrentUser().getName());
                objectNode.put("confirmRemarks", jsonNode.get("remark") != null ? jsonNode.get("remark").asText() : null);
            } catch (ThingsboardException e) {
                e.printStackTrace();
            }
            alarm.setDetails(objectNode);
            result.add(alarmService.createOrUpdateAlarm(alarm));
        });
        return result;
    }


    /**
     * 根据tenantID获取历史数据
     *
     * @param start
     * @param end
     * @return
     * @throws ThingsboardException
     */
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN',  'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE', 'TENANT_SYS', 'CUSTOMER_USER')")
    @RequestMapping(value = "/alarm/getAlarmHistory", method = RequestMethod.GET)
    @SysLog(detail = DataConstants.OPERATING_TYPE_ALARM_INFO)
    @ResponseBody
    public List<Alarm> getAlarmHistory(
            @RequestParam(required = false) long start,
            @RequestParam(required = false) long end
    ) throws Exception {
        checkNotNull(start);
        checkNotNull(end);
        return alarmService.findHistoryAlarm(getTenantId(), start, end).get();
    }


    /**
     * 根据TENANTID获取实时报警
     */
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN',  'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE', 'TENANT_SYS', 'CUSTOMER_USER')")
    @RequestMapping(value = "/alarm/getAlarmRealTime", method = RequestMethod.GET)
    @SysLog(detail = DataConstants.OPERATING_TYPE_ALARM_INFO)
    @ResponseBody
    public List<Alarm> getAlarmRealTime(
            @RequestParam(required = false) long start,
            @RequestParam(required = false) long end
    ) throws Exception {
        checkNotNull(start);
        checkNotNull(end);
        List<Device> deviceList = deviceService.findAll(getTenantId());
        Map<String, Device> deviceMap = deviceList.stream()
                .collect(Collectors.toMap(device -> device.getUuidId().toString(), device -> device));
        List<Alarm> alarmList = alarmService.findRealTimeAlarm(getTenantId(), start, end).get();
        alarmList = alarmList.stream()
                .map(alarm -> {
                    String deviceId = alarm.getOriginator().getId().toString();
                    Device device = deviceMap.get(deviceId);
                    if (device != null) {
                        alarm.setDeviceName(device.getName());
                    }
                    return alarm;
                })
                .filter(alarm -> StringUtils.isNotBlank(alarm.getDeviceName()))
                .collect(Collectors.toList());
        return alarmList;
    }

    /**
     * 根据TENANTID获取实时报警
     */
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN',  'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE', 'TENANT_SYS', 'CUSTOMER_USER')")
    @RequestMapping(value = "/alarm/getAlarmRealTime/{projectId}", method = RequestMethod.GET)
    @SysLog(detail = DataConstants.OPERATING_TYPE_ALARM_INFO)
    @ResponseBody
    public List<Alarm> getAlarmRealTime(
            @RequestParam(required = false) long start,
            @RequestParam(required = false) long end,
            @PathVariable String projectId
    ) throws Exception {
        checkNotNull(start);
        checkNotNull(end);
        // 查询项目下的子项目
        List<ProjectEntity> list = projectService.findAllChild(getTenantId(), projectId);
        List<String> projectIdList = list.stream().map(ProjectEntity::getId).collect(Collectors.toList());
        projectIdList.add(projectId);

        // 名字过滤
        List<Device> deviceList = deviceService.findByProjectId(projectId, getTenantId());
        Map<String, Device> deviceMap = deviceList.stream().collect(Collectors.toMap(device -> device.getUuidId().toString(), device -> device));

        return alarmService.findAlarmsByProjectIdAndTs(projectIdList, start, end).stream()
                .peek(alarm -> {
                    Device device = deviceMap.get(alarm.getOriginator().getId().toString());
                    if (device != null) {
                        alarm.setDeviceName(device.getName());
                    }
                })
                .collect(Collectors.toList());
    }

    /**
     * 根据TENANTID获取实时报警
     */
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN',  'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE', 'TENANT_SYS', 'CUSTOMER_USER')")
    @RequestMapping(value = "/alarm/getAlarmRealTime/page/{projectId}", method = RequestMethod.GET)
    @SysLog(detail = DataConstants.OPERATING_TYPE_ALARM_INFO)
    @ResponseBody
    public PageData<Alarm> getAlarmRealTimeByPage(
            @RequestParam(required = false) long start,
            @RequestParam(required = false) long end,
            @RequestParam(defaultValue = "") String keyword,
            @PathVariable String projectId,
            @RequestParam int page, @RequestParam int size
    ) throws Exception {
        checkNotNull(start);
        checkNotNull(end);
        // 查询项目下的子项目
        List<ProjectEntity> list = projectService.findAllChild(getTenantId(), projectId);
        List<String> projectIdList = list.stream().map(ProjectEntity::getId).collect(Collectors.toList());
        projectIdList.add(projectId);
        List<Alarm> resultList = alarmService.findAlarmsByProjectIdAndTs(projectIdList, start, end).stream()
                .filter(a -> a.getStatus() != AlarmStatus.CLEARED_ACK && a.getStatus() != AlarmStatus.CLEAR_FORCED).collect(Collectors.toList());
        // 名字过滤
        List<Device> deviceList = deviceService.findByProjectId(projectId, getTenantId());
        Map<String, Device> deviceMap = deviceList.stream().collect(Collectors.toMap(device -> device.getUuidId().toString(), device -> device));

        resultList = resultList.stream()
                .filter(e -> ("" + e.getAlarmJsonName() + e.getDeviceName()).contains(keyword))
                .peek(alarm -> {
                    Device device = deviceMap.get(alarm.getOriginator().getId().toString());
                    if (device != null) {
                        alarm.setDeviceName(device.getName());
                    }
                })
                .collect(Collectors.toList());
        return PageData.page(resultList, page, size);
    }

    /**
     * 根据报警类别查看历史报警
     */
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN',  'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE', 'TENANT_SYS', 'CUSTOMER_USER')")
    @RequestMapping(value = "/alarm/getAlarmHistoryByType", method = RequestMethod.GET)
    @SysLog(detail = DataConstants.OPERATING_TYPE_ALARM_INFO)
    @ResponseBody
    public List<Alarm> getAlarmFromtype(
            @RequestParam(required = false) long start,
            @RequestParam(required = false) long end,
            @RequestParam(required = false) String alarmType
    ) throws Exception {
        checkNotNull(start);
        checkNotNull(end);
        checkNotNull(alarmType);
        return alarmService.findHistoryAlarm(getTenantId(), start, end).get().parallelStream().filter(a -> a.getType().equals(alarmType)).collect(Collectors.toList());
    }


    /**
     * 根据报警类别查看该设备历史报警
     */
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN',  'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE', 'TENANT_SYS', 'CUSTOMER_USER')")
    @RequestMapping(value = "/alarm/getAlarmHistoryByDevice", method = RequestMethod.GET)
    @SysLog(detail = DataConstants.OPERATING_TYPE_ALARM_INFO)
    @ResponseBody
    public List<Alarm> getAlarmFromDevice(
            @RequestParam(required = false) String deviceId,
            @RequestParam(required = false) String alarmType
    ) throws Exception {
        checkNotNull(deviceId);
        checkNotNull(alarmType);
        return alarmService.findHistoryAlarmByDevice(new DeviceId(UUIDConverter.fromString(deviceId)), alarmType).get();
    }

    /**
     * 根据设备查询历史数据
     *
     * @param start
     * @param end
     * @param deviceId
     * @return
     * @throws Exception
     */
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN',  'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE', 'TENANT_SYS', 'CUSTOMER_USER')")
    @RequestMapping(value = "/alarm/getAlarmRealTimeByDevice", method = RequestMethod.GET)
    @SysLog(detail = DataConstants.OPERATING_TYPE_ALARM_INFO)
    @ResponseBody
    public List<Alarm> getAlarmRealTimeByDevice(
            @RequestParam(required = false) long start,
            @RequestParam(required = false) long end,
            @RequestParam(required = false) String deviceId
    ) throws Exception {
        checkNotNull(start);
        checkNotNull(end);
        checkNotNull(deviceId);
        return alarmService.getAlarmRealTimeByDevice(new DeviceId(UUIDConverter.fromString(deviceId)), start, end).get();
    }

    /**
     * 根据设备查询历史数据
     *
     * @param start
     * @param end
     * @param deviceId
     * @return
     * @throws Exception
     */
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN',  'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE', 'TENANT_SYS', 'CUSTOMER_USER')")
    @RequestMapping(value = "/alarm/getAlarmByDevice", method = RequestMethod.GET)
    @SysLog(detail = DataConstants.OPERATING_TYPE_ALARM_INFO)
    @ResponseBody
    public List<Alarm> getAlarmByDevice(
            @RequestParam(required = false) long start,
            @RequestParam(required = false) long end,
            @RequestParam(required = false) String deviceId
    ) throws Exception {
        checkNotNull(start);
        checkNotNull(end);
        checkNotNull(deviceId);
        return alarmService.findHistoryByDeviceId(new DeviceId(UUIDConverter.fromString(deviceId)), start, end).get();
    }

    /**
     * 根据设备查询历史数据
     *
     * @param projectId
     * @param start
     * @param end
     * @return
     * @throws Exception
     */
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN',  'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE', 'TENANT_SYS', 'CUSTOMER_USER')")
    @RequestMapping(value = "/alarm/countAlarm/{projectId}", method = RequestMethod.GET)
    @SysLog(detail = DataConstants.OPERATING_TYPE_ALARM_INFO)
    @ResponseBody
    public Map<String, CountAlarmVO> countAlarm(@RequestParam long start, @RequestParam long end, @PathVariable String projectId) throws Exception {
        checkNotNull(start);
        checkNotNull(end);
        List<Alarm> alarms = alarmService.findRealTimeAlarmAll(projectId, start, end).get();
        Map<String, CountAlarmVO> result = new LinkedHashMap<>();
        result.put("提示", new CountAlarmVO());
        result.put("次要", new CountAlarmVO());
        result.put("重要", new CountAlarmVO());
        result.put("紧急", new CountAlarmVO());
        if (alarms != null && !alarms.isEmpty()) {
            alarms.forEach(alarm -> {
                if (result.containsKey(alarm.getSeverity())) {
                    CountAlarmVO countAlarmVO = result.get(alarm.getSeverity());
                    countAlarmVO.setType(alarm.getSeverity());
                    countAlarmVO.setValue(countAlarmVO.getValue() + 1);
                }
            });
        }

        List<Alarm> all = alarmService.findRealTimeAlarmAll(projectId, 1577808000000L, end).get();
        if (all != null && !all.isEmpty()) {
            all.forEach(alarm -> {
                if (result.containsKey(alarm.getSeverity())) {
                    CountAlarmVO countAlarmVO = result.get(alarm.getSeverity());
                    countAlarmVO.setType(alarm.getSeverity());
                    countAlarmVO.setTotalValue(countAlarmVO.getTotalValue() + 1);
                }
            });
        }

        return result;
    }

    /**
     * 报警统计
     */
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN',  'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE', 'TENANT_SYS', 'CUSTOMER_USER')")
    @RequestMapping(value = "/alarm/countAlarmByTenant", method = RequestMethod.GET)
    @SysLog(detail = DataConstants.OPERATING_TYPE_ALARM_INFO)
    @ResponseBody
    public Object countAlarm(@RequestParam long start, @RequestParam long end) throws Exception {
        checkNotNull(start);
        checkNotNull(end);
        List<Alarm> alarms = alarmService.findRealTimeAlarm(getTenantId(), start, end).get();
        Map<String, Integer> result = new LinkedHashMap<>();
        result.put("提示", 0);
        result.put("次要", 0);
        result.put("重要", 0);
        result.put("紧急", 0);
        if (alarms != null && !alarms.isEmpty()) {
            alarms.forEach(alarm -> {
                if (result.containsKey(alarm.getSeverity())) {
                    Integer value = result.get(alarm.getSeverity());
                    result.put(alarm.getSeverity(), value + 1);
                }
            });
        }

        return result;
    }

    /**
     * 获取设备的报警情况
     *
     * @param deviceId
     * @return
     */
    @GetMapping(value = "/deviceAlarmMsg")
    public DeviceAlarmMsg getDeviceAlarmMsg(@Param("deviceId") String deviceId) {
        return DeviceAlarmMsg.builder()
                .allAlarmCount(alarmService.getAllAlarmByDeviceId(deviceId))
                .onlineAlarmCount(alarmService.getRealTimeAlarmByDeviceId(deviceId))
                .historyAlarmCount(alarmService.getHistoryAlarmByDeviceId(deviceId))
                .build();
    }

    /**
     * 获取企业下的历史报警信息
     *
     * @return
     * @throws ThingsboardException
     */
    @GetMapping(value = "/tenantAlarmMsg")
    public TenantAlarmMsg getTenantAlarmMsg() throws ThingsboardException {
        TenantAlarmMsg tenantAlarmMsg;
        try {
            tenantAlarmMsg = TenantAlarmMsg.builder()
                    .alarmNow(alarmService.findHistoryAlarm(getTenantId(), TimeDiff.getStartTimeByType(DateUtils.DAY), TimeDiff.getEndTimeByType(DateUtils.DAY)).get().size())
                    .alarmWeek(alarmService.findHistoryAlarm(getTenantId(), TimeDiff.getStartTimeByType(DateUtils.WEEK), TimeDiff.getEndTimeByType(DateUtils.WEEK)).get().size())
                    .alarmMonth(alarmService.findHistoryAlarm(getTenantId(), TimeDiff.getStartTimeByType(DateUtils.MONTH), TimeDiff.getEndTimeByType(DateUtils.MONTH)).get().size())
                    .alarmSeason(alarmService.findHistoryAlarm(getTenantId(), TimeDiff.getStartTimeByType(DateUtils.SEASON), TimeDiff.getEndTimeByType(DateUtils.SEASON)).get().size())
                    .alarmYear(alarmService.findHistoryAlarm(getTenantId(), TimeDiff.getStartTimeByType(DateUtils.YEAR), TimeDiff.getEndTimeByType(DateUtils.YEAR)).get().size())
                    .build();
        } catch (Exception e) {
            throw new ThingsboardException("获取报警信息失败！", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }
        return tenantAlarmMsg;
    }

    @GetMapping("/alarm/station/{stationId}")
    public List<Alarm> findAlarmByStation(@PathVariable String stationId,
                                          @RequestParam(required = false) Long start,
                                          @RequestParam(required = false) Long end) {
        if (start == null || end == null) {
            Calendar instance = Calendar.getInstance();
            end = instance.getTimeInMillis();

            instance.set(Calendar.HOUR_OF_DAY, 0);
            instance.set(Calendar.MINUTE, 0);
            instance.set(Calendar.SECOND, 0);

            start = instance.getTimeInMillis();
        }

        return alarmService.findAlarmByStation(stationId, start, end);
    }

    @GetMapping("/alarm/stationAlarmByProjectId")
    public List<Alarm> stationAlarmByProjectId(@RequestParam(required = false, defaultValue = "") String projectId,
                                               @RequestParam(required = false) Long start,
                                               @RequestParam(required = false) Long end) throws ThingsboardException {
        JSONObject param = new JSONObject();
        param.put("projectId", projectId);
        PageData<StationEntity> pageResult = stationService.list(1, 99999, param, getTenantId());
        List<StationEntity> stationList = pageResult.getData();
        if (stationList == null || stationList.isEmpty()) {
            return new ArrayList<>();
        }

        if (start == null && end == null) {
            // 查询7天内
            Calendar instance = Calendar.getInstance();
            end = instance.getTimeInMillis();

            instance.set(Calendar.HOUR_OF_DAY, 0);
            instance.set(Calendar.MINUTE, 0);
            instance.set(Calendar.SECOND, 0);

            start = instance.getTimeInMillis() - (60L * 24 * 60 * 60 * 1000);
        }

        List<Alarm> resultList = new ArrayList<>();
        for (StationEntity station : stationList) {
            List<Alarm> alarmList = alarmService.findAlarmByStation(station.getId(), start, end);
            resultList.addAll(alarmList);
        }

        return resultList.stream().distinct().collect(Collectors.toList());
    }

    // ================== 报表API ================= //

    /**
     * 报警统计-按等级
     */
    @RequestMapping(value = "/alarm/countAlarmByProject", method = RequestMethod.GET)
    @ResponseBody
    public Object countAlarmByProject(@RequestParam(required = false, defaultValue = "") String projectId,
                                      @RequestParam long start, @RequestParam long end) throws Exception {
        checkNotNull(start);
        checkNotNull(end);
        List<Alarm> alarms;
        if (StringUtils.isNotBlank(projectId)) {
            // 查询指定项目的所有子项目的报警（包含指定项目本身）
            List<ProjectEntity> childProjectList = projectService.findAllChild(getTenantId(), projectId);
            alarms = alarmService.findAlarmsByProjectIdAndTs(childProjectList.stream().map(ProjectEntity::getId).collect(Collectors.toList()), start, end);
        } else {
            // 查询所有项目的报警
            List<ProjectEntity> projectList = projectService.findByTenantId(getTenantId());
            alarms = alarmService.findAlarmsByProjectIdAndTs(projectList.stream().map(ProjectEntity::getId).collect(Collectors.toList()), start, end);
        }

        Map<String, Integer> result = new LinkedHashMap<>();
        result.put("提示", 0);
        result.put("次要", 0);
        result.put("重要", 0);
        result.put("紧急", 0);
        if (alarms != null && !alarms.isEmpty()) {
            alarms.forEach(alarm -> {
                if (result.containsKey(alarm.getSeverity())) {
                    Integer value = result.get(alarm.getSeverity());
                    result.put(alarm.getSeverity(), value + 1);
                }
            });
        }

        return result;
    }

    /**
     * 报警总览
     * 报警总数，报警设备数，待确认数，待解除数
     */
    @RequestMapping(value = "/alarm/countAlarm/overview", method = RequestMethod.GET)
    @ResponseBody
    public Object countAlarm(@RequestParam(required = false, defaultValue = "") String projectId,
                             @RequestParam long start, @RequestParam long end) throws Exception {
        checkNotNull(start);
        checkNotNull(end);
        List<Alarm> alarms;
        if (StringUtils.isNotBlank(projectId)) {
            // 查询指定项目的所有子项目的报警（包含指定项目本身）
            List<ProjectEntity> childProjectList = projectService.findAllChild(getTenantId(), projectId);
            alarms = alarmService.findAlarmsByProjectIdAndTs(childProjectList.stream().map(ProjectEntity::getId).collect(Collectors.toList()), start, end);
        } else {
            // 查询所有项目的报警
            List<ProjectEntity> projectList = projectService.findByTenantId(getTenantId());
            alarms = alarmService.findAlarmsByProjectIdAndTs(projectList.stream().map(ProjectEntity::getId).collect(Collectors.toList()), start, end);
        }

        // 报警总数
        int alarmTotal = 0;
        // 报警设备
        int alarmDeviceTotal = 0;
        // 待确认数
        int unconfirmTotal = 0;
        // 待解除数
        int unremoveTotal = 0;

        Set<String> alarmDeviceSet = new HashSet<>();
        // 遍历报警列表进行统计
        for (Alarm alarm : alarms) {
            if (alarm.getOriginator() != null) {
                alarmDeviceSet.add(alarm.getOriginator().getId().toString());
            }

            AlarmStatus status = alarm.getStatus();
            if (!status.isAck()) {
                unconfirmTotal++;
            }
            if (!status.isCleared()) {
                unremoveTotal++;
            }

            alarmTotal++;
        }

        alarmDeviceTotal = alarmDeviceSet.size();

        JSONObject result = new JSONObject();
        result.put("alarmTotal", alarmTotal);
        result.put("alarmDeviceTotal", alarmDeviceTotal);
        result.put("unconfirmTotal", unconfirmTotal);
        result.put("unremoveTotal", unremoveTotal);

        return result;
    }

    @GetMapping("alarm/alarmRank")
    public Object alarmRank(@RequestParam(required = false, defaultValue = "") String projectId,
                            @RequestParam long start, @RequestParam long end) throws ThingsboardException {
        checkNotNull(start);
        checkNotNull(end);
        List<Alarm> alarms;
        if (StringUtils.isNotBlank(projectId)) {
            // 查询指定项目的所有子项目的报警（包含指定项目本身）
            List<ProjectEntity> childProjectList = projectService.findAllChild(getTenantId(), projectId);
            alarms = alarmService.findAlarmsByProjectIdAndTs(childProjectList.stream().map(ProjectEntity::getId).collect(Collectors.toList()), start, end);
        } else {
            // 查询所有项目的报警
            List<ProjectEntity> projectList = projectService.findByTenantId(getTenantId());
            alarms = alarmService.findAlarmsByProjectIdAndTs(projectList.stream().map(ProjectEntity::getId).collect(Collectors.toList()), start, end);
        }
        alarms = new ArrayList<>(alarms);

        // 分组统计
        List<Device> deviceList = deviceService.findByTenantId(getTenantId());
        Map<String, Device> deviceMap = deviceList.stream()
                .collect(Collectors.toMap(device -> UUIDConverter.fromTimeUUID(device.getUuidId()), device -> device));

        // 查询设备所属项目
        List<ProjectRelationEntity> relationList = projectRelationService.findByEntityTypeAndEntityIdIn("DEVICE",
                deviceList.stream().map(device -> UUIDConverter.fromTimeUUID(device.getUuidId())).collect(Collectors.toList()));
        Map<String, ProjectRelationEntity> relationMap = relationList.stream()
                .collect(Collectors.toMap(ProjectRelationEntity::getEntityId, relation -> relation));

        // 查询项目
        List<ProjectEntity> projectList = projectService.findByTenantId(getTenantId());
        Map<String, ProjectEntity> projectMap = projectList.stream()
                .collect(Collectors.toMap(ProjectEntity::getId, project -> project));


        Map<String, List<Alarm>> deviceAlarmMap = new HashMap<>();
        if (alarms.size() > 0) {
            for (Alarm alarm : alarms) {
                EntityId originator = alarm.getOriginator();
                if (originator != null) {
                    String deviceId = UUIDConverter.fromTimeUUID(originator.getId());
                    List<Alarm> dataList = deviceAlarmMap.get(deviceId);
                    if (dataList == null) {
                        dataList = new ArrayList<>();
                    }
                    dataList.add(alarm);
                    deviceAlarmMap.put(deviceId, dataList);
                }
            }
        }

        List<JSONObject> resultList = new ArrayList<>();
        for (Map.Entry<String, List<Alarm>> entry : deviceAlarmMap.entrySet()) {
            String key = entry.getKey();
            List<Alarm> value = entry.getValue();
            Device device = deviceMap.get(key);

            JSONObject result = new JSONObject();
            if (device != null) {
                result.put("deviecName", device.getName());
            } else {
                result.put("deviceName", "");
            }
            result.put("projectName", "");
            result.put("alarm", value == null ? 0 : value.size());
            ProjectRelationEntity projectRelation = relationMap.get(key);
            if (projectRelation != null) {
                ProjectEntity project = projectMap.get(projectRelation.getProjectId());
                result.put("projectName", project.getName());
            }

            resultList.add(result);
        }

        return resultList.stream().sorted((r1, r2) -> {
            Integer alarm1 = r1.getIntValue("alarm");
            Integer alarm2 = r2.getIntValue("alarm");
            return alarm2.compareTo(alarm1);
        }).collect(Collectors.toList());

    }

    @GetMapping("alarm/trend")
    public Object alarmTrend(@RequestParam(required = false, defaultValue = "") String projectId,
                             @RequestParam long start, @RequestParam long end) throws ThingsboardException {
        checkNotNull(start);
        checkNotNull(end);
        List<Alarm> alarms;
        if (StringUtils.isNotBlank(projectId)) {
            // 查询指定项目的所有子项目的报警（包含指定项目本身）
            List<ProjectEntity> childProjectList = projectService.findAllChild(getTenantId(), projectId);
            alarms = alarmService.findAlarmsByProjectIdAndTs(childProjectList.stream().map(ProjectEntity::getId).collect(Collectors.toList()), start, end);
        } else {
            // 查询所有项目的报警
            List<ProjectEntity> projectList = projectService.findByTenantId(getTenantId());
            alarms = alarmService.findAlarmsByProjectIdAndTs(projectList.stream().map(ProjectEntity::getId).collect(Collectors.toList()), start, end);
        }
        alarms = new ArrayList<>(alarms);

        // 按日统计, 计算日期差距多少天
        int days = (int) ((end - start) / (1000 * 3600 * 24));

        Map<String, List<Alarm>> alarmMap = new LinkedHashMap<>();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        for (int i = 0; i < days; i++) {
            Date date = new Date(start + (i * 24 * 60 * 60 * 1000L));
            alarmMap.put(simpleDateFormat.format(date), new ArrayList<>());
        }

        for (Alarm alarm : alarms) {
            Date createTime = new Date(alarm.getCreatedTime());
            String key = simpleDateFormat.format(createTime);
            List<Alarm> alarmList = alarmMap.get(key);
            if (alarmList == null) {
                continue;
            }
            alarmList.add(alarm);
        }

        // 统计结果
        List<JSONObject> resultList = new ArrayList<>();
        for (Map.Entry<String, List<Alarm>> entry : alarmMap.entrySet()) {
            String key = entry.getKey();
            List<Alarm> value = entry.getValue();
            JSONObject result = new JSONObject();
            result.put("date", key);
            result.put("alarm", value == null ? 0 : value.size());
            resultList.add(result);
        }

        return resultList;
    }


    // ================== 报表API ================= //


}
