package org.thingsboard.server.dao.util.imodel.query.smartProduction.pumpHouse;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartProduction.pumpHouse.PumpManage;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;

import java.util.Date;

@Getter
@Setter
public class PumpManagePageRequest extends AdvancedPageableQueryEntity<PumpManage, PumpManagePageRequest> {
    // 所属泵房ID
    private String pumpRoomId;

    // 所属泵房名称
    private String pumpRoomName;

    // 所属泵房编码
    private String pumpRoomCode;

    // 设备编码
    private String code;

    // 设备名称
    private String name;

    // 设备简称
    private String nickname;

    // 泵个数
    private Integer pumpNum;

    // 厂家名称
    private String companyName;

    // 型号
    private String model;

    // 安装人名称
    private String installUserName;

    // 安装时间From
    private String installDateFrom;

    // 安装时间To
    private String installDateTo;

    public Date getInstallDateFrom() {
        return toDate(installDateFrom);
    }

    public Date getInstallDateTo() {
        return toDate(installDateTo);
    }

}
