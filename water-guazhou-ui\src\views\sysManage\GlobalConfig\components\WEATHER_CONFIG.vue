<template>
  <Form
    ref="refForm"
    :config="FormConfig"
  ></Form>
</template>
<script lang="ts" setup>
import { saveTenant } from '@/api/tenant'
import { useUserStore } from '@/store'
import { SLConfirm, SLMessage } from '@/utils/Message'

const refForm = ref<IFormIns>()
const FormConfig = reactive<IFormConfig>({
  group: [
    {
      fields: [
        { type: 'input', label: 'GISWebApi', field: 'gisApi', placeholder: 'http://xxx:8001/webapi' },
        { type: 'input', label: 'GISSDKKey', field: 'gisApiKey' },
        {
          type: 'input',
          label: 'GISSDKAPI',
          field: 'gisSDK',
          placeholder: 'http://www.xxx.com/arcgis_js_api/javascript/4.26'
        },
        { type: 'input', label: '天地图Key', field: 'gisTdtToken' },
        {
          type: 'input',
          label: 'GIS服务地址',
          field: 'gisService',
          placeholder: 'http://xxx:6080/arcgis/rest/services/ANQING/'
        },
        { type: 'input', label: 'GIS代理地址', field: 'gisProxyService', placeholder: '/arcgis/rest/services/ANQING/' },
        { type: 'input', label: '地图默认中心点坐标', field: 'gisDefaultCenter', placeholder: '107.889659, 26.939568' },
        { type: 'number', label: '地图默认缩放级别', field: 'gisDefaultZoom', placeholder: '14' },

        {
          type: 'input',
          label: '管线切片地图服务路径',
          field: 'gisPipeDataService'
        },
        {
          type: 'input',
          label: '管线动态地图服务路径',
          field: 'gisPipeDynamicService'
        },
        {
          type: 'input',
          label: '管线要素服务路径',
          field: 'gisPipeFeatureServiceFeatureServer'
        },
        {
          type: 'input',
          label: '管线要素地图服务路径',
          field: 'gisPipeFeatureServiceMapServer'
        },
        {
          type: 'input',
          label: '爆管分析路径',
          field: 'gisBurstGPService'
        },
        {
          type: 'input',
          label: '连通性分析路径',
          field: 'gisConnectGPService'
        },
        {
          type: 'input',
          label: '沿线分析路径',
          field: 'gisPathAnalysGPService'
        },
        {
          type: 'input',
          label: '关阀分析路径',
          field: 'gisShutValveAnalysGPService'
        },
        {
          type: 'input',
          label: '关阀分析路径',
          field: 'gisShutValveAnalysGPService'
        },
        {
          type: 'input',
          label: '二次关阀分析路径',
          field: 'gisExtendShutValveAnalysGPService'
        },
        {
          type: 'input',
          label: '放大镜服务路径',
          field: 'gisFangDaGPService'
        },
        {
          type: 'input',
          label: '几何服务路径（Utilities）',
          field: 'gisGeometryService'
        },
        {
          type: 'input',
          label: '模板打印服务路径',
          field: 'gisPrintTemplatePath'
        },
        {
          type: 'input',
          label: '地图打印服务路径',
          field: 'gisPrintGPService'
        },
        {
          type: 'input',
          label: '打印工具服务路径',
          field: 'gisPrintingToolsGPService'
        }
      ]
    },
    {
      fields: [
        {
          type: 'btn-group',
          btns: [
            { perm: true, text: '保存', click: () => refForm.value?.Submit() },
            { perm: true, text: '重置', click: () => refForm.value?.resetForm() }
          ]
        }
      ]
    }
  ],
  labelPosition: 'right',
  labelWidth: '100px',
  defaultValue: {
  },
  submit: (params: any) => {
    const submitParams:ISiteConfig = {
      ...getBaseValue(),
      GIS_CONFIG: params
    }
    submitParams
    saveConfig(params)
  }
})
const getDefaultValue = () => {
  return {
    ...(window.SITE_CONFIG?.GIS_CONFIG || {}),
    ...(useUserStore().tenantInfo?.additionalInfo?.GIS_CONFIG || {})
  }
}
const getBaseValue = () => {
  return {
    ...(window.SITE_CONFIG || {}),
    ...(useUserStore().tenantInfo?.additionalInfo || {})
  }
}
const saveConfig = (params: ISiteConfig) => {
  SLConfirm('确定保存？', '提示信息')
    .then(async () => {
      FormConfig.submitting = true
      try {
        console.log(params)
        // await saveTenant(params)
        await useUserStore().InitTenantInfo()
        resetForm()
      } catch (error) {
        SLMessage.error('保存失败')
        console.log(error)
      }
    })
    .catch(() => {
      //
    })
}
const resetForm = () => {
  FormConfig.defaultValue = getDefaultValue()
}
</script>
<style lang="scss" scoped></style>
