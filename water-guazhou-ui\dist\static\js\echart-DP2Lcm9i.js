function e(t){return{title:{text:"分析曲线",textStyle:{fontSize:"14px"},top:10},grid:{left:90,right:90,top:70,bottom:40},legend:{right:150,top:10,type:"scroll",width:500,textStyle:{fontSize:12}},dataZoom:[{type:"inside",start:0,end:30},{start:0,end:10}],tooltip:{trigger:"axis"},xAxis:{type:"category",boundaryGap:!1,data:t},yAxis:[{position:"left",type:"value",name:"供水量(m³)",axisLine:{show:!0,lineStyle:{types:"solid"}},axisLabel:{show:!0,textStyle:{}},splitLine:{lineStyle:{type:[5,10],dashOffset:5}}}],series:[{name:"上限",smooth:!0,data:[150,230,224,218,135,147,260,135,147,260,135,147,260,135,147,260,135,147,260,135,147,260,135,260],type:"line",markPoint:{data:[{type:"max",name:"最大值"},{type:"min",name:"最小值"}]},markLine:{data:[{type:"average",name:"平均值"}]}}]}}export{e as l};
