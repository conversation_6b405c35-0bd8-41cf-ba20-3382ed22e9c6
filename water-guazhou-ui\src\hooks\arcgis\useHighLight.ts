import { setMapCursor } from '@/utils/MapHelper.js';
/**
 * Highlight the feature(s)
 * @returns
 */
export const useHighLight = () => {
  let moveHighLight: any;
  let clickHandler: any;
  const highLightHandlers: any[] = [];
  let highLightedFeature: __esri.Graphic | undefined;
  let timer: any;
  const highlight = (
    view?: __esri.MapView,
    graphic?: __esri.Graphic,
    callBack?: (graphic?: __esri.Graphic) => any,
    delay?: number
  ) => {
    if (!view || !graphic) return;
    const graphicsLayer: __esri.GraphicsLayer =
      graphic.layer as __esri.GraphicsLayer;
    view.whenLayerView(graphicsLayer).then((a) => {
      removeHoverHighLight();
      const highLightHandler = a.highlight(graphic);
      highLightHandlers.push(highLightHandler);
      timer && clearTimeout(timer);
      timer = setTimeout(() => {
        callBack && callBack(graphic);
      }, delay ?? 500);
    });
  };
  /**
   * 鼠标悬浮高亮
   * @param view
   * @param layer 指定高亮图层
   * @param callBack 高亮回调
   * @returns
   */
  const bindHoverHighLight = (
    view?: __esri.MapView,
    layer?: __esri.GraphicsLayer | __esri.GraphicsLayer[],
    callBack?: (graphic?: __esri.Graphic) => any,
    delay?: number
  ) => {
    if (!view || !layer) return;
    moveHighLight?.remove();
    moveHighLight = view.on('pointer-move', (result) => {
      view
        .hitTest(result.native, {
          include: layer
        })
        .then((res) => {
          if (res.results.length) {
            setMapCursor('pointer');
            highLightedFeature =
              res.results[0]?.type === 'graphic'
                ? res.results[0].graphic
                : undefined;
            highLightedFeature &&
              !highLightedFeature?.attributes?.notHighlight &&
              highlight(view, highLightedFeature, callBack, delay);
          } else {
            callBack?.();
            setMapCursor('');
            timer && clearTimeout(timer);
          }
        });
    });
    clickHandler = view.on('click', (result) => {
      view.hitTest(result.native).then((res) => {
        if (!res.results.length) removeHoverHighLight();
      });
    });
  };
  /**
   * 移除鼠标悬浮高亮
   */
  const removeHoverHighLight = () => {
    highLightHandlers.map((item) => item.remove && item.remove());
    highLightHandlers.length = 0;
  };
  const destroy = () => {
    removeHoverHighLight();
    moveHighLight?.remove();
    clickHandler?.remove();
  };
  onBeforeUnmount(() => {
    destroy();
  });
  return {
    bindHoverHighLight,
    removeHoverHighLight,
    destroy,
    highlight
  };
};
export default useHighLight;
