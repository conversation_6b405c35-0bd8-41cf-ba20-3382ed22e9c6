import { sortBy } from 'lodash-es'
import { SLConfirm, SLMessage } from '@/utils/Message'
import { getAttrsOfDevice } from '../data'
import { useStationAttrGroup } from '.'
import { getDevice } from '@/api/device'
import { formatTree } from '@/utils/GlobalHelper'
import { removeSlash } from '@/utils/removeIdSlash'

export const useAttrTable = () => {
  const attrGroup = useStationAttrGroup()
  const devices = ref<NormalOption[]>([])
  // 设备属性缓存
  const deviceAttrListObjs: { deviceId: string; attrList: NormalOption[] }[] = []

  const attrFieldConfig = (deviceId?: string): IFormSelect => {
    const attrList = deviceAttrListObjs.find(item => item.deviceId === deviceId)?.attrList ?? []
    return {
      type: 'select',
      options: attrList,
      onChange: (val: any, row: any, config: IFormSelect) => {
        const option = config?.options?.find(item => item.value === val)
        row.name = option?.label
        row.attr = val
        row.unit = option?.data?.unit
      }
    }
  }
  const TableConfig = ref<ITable>({
    height: 400,
    dataList: [],
    columns: [
      {
        minWidth: 60,
        label: '序号',
        prop: 'orderNum',
        align: 'center',
        width: 60
      },
      {
        minWidth: 180,
        label: '设备',
        prop: 'deviceId',
        formItemConfig: {
          type: 'select',
          label: '',
          field: '',
          options: computed(() => devices.value || []) as any,
          onChange: (val, row) => {
            if (!val) return
            if (!row.attrAliasFormItemConfig) return
            const devid = removeSlash(val)
            getAttrsOfDevice(devid).then(res => {
              // row.attrAliasFormItemConfig.options.length = 0
              row.attrAliasFormItemConfig.options = res
            })
          }
        }
      },
      {
        minWidth: 150,
        label: '变量名',
        prop: 'attrAlias',
        formItemConfig: {
          type: 'select',
          options: []
        }
      },
      { minWidth: 150, label: '字段名称', prop: 'attr' },
      {
        minWidth: 150,
        label: '别名',
        prop: 'name',
        formItemConfig: { type: 'input', label: '', field: '' }
      },
      {
        minWidth: 120,
        label: '最小值',
        prop: 'min',
        formItemConfig: {
          type: 'input-number',
          onChange: (val, row) => {
            if (row.min === undefined) row.min = ''
            if (row.max === undefined) row.max = ''
            if (row.min && row.max) {
              if (Number(row.min) > Number(row.max)) {
                SLMessage.warning('最小值不能大于最大值')
              }
            }
            row.range = row.min + ',' + row.max
          }
        }
      },
      {
        minWidth: 120,
        label: '最大值',
        prop: 'max',
        formItemConfig: {
          type: 'input-number',
          onChange: (val, row) => {
            if (row.min === undefined) row.min = ''
            if (row.max === undefined) row.max = ''
            if (row.min && row.max) {
              if (Number(row.min) > Number(row.max)) {
                SLMessage.warning('最大值不能小于最小值')
              }
            }
            row.range = row.min + ',' + row.max
          }
        }
      },
      {
        minWidth: 100,
        label: '单位',
        prop: 'unit',
        formItemConfig: {
          type: 'input'
        }
      }
    ],
    // indexVisible: true,
    operationWidth: 220,
    rowKey: 'id',
    operations: [
      {
        perm: true,
        type: 'primary',
        isTextBtn: true,
        text: '上移',
        icon: 'iconfont icon-shangyi',
        click: (row: any) => handleAttrVarRowUp(row)
      },
      {
        perm: true,
        type: 'primary',
        isTextBtn: true,
        text: '下移',
        icon: 'iconfont icon-xiayi',
        click: (row: any) => handleAttrVarRowDown(row)
      },
      {
        perm: true,
        type: 'danger',
        isTextBtn: true,
        text: '删除',
        icon: 'iconfont icon-shanchu',
        click: (row: any) => handleAttrVarDelete(row)
      }
    ],
    pagination: {
      hide: true
    }
  })

  const refreshTableColumns = async (projectId?: string) => {
    const res = await getDevice(projectId)
    devices.value = formatTree(res.data || [], {
      label: 'name',
      value: 'id.id',
      id: 'id.id',
      children: 'children'
    })
  }
  const handleAttrVarRowUp = (row: any) => {
    const orderNum = row.orderNum
    const nextRow = TableConfig.value.dataList.find(item => item.orderNum === orderNum - 1)
    // 当有上一个才进行换位操作
    if (nextRow) {
      row.orderNum--
      nextRow.orderNum++
      resetAttrVarsOrder()
    }
  }
  const handleAttrVarRowDown = (row: any) => {
    const orderNum = row.orderNum
    const nextRow = TableConfig.value.dataList.find(item => item.orderNum === orderNum + 1)
    // 当有下一个才进行换位操作
    if (nextRow) {
      row.orderNum++
      nextRow.orderNum--
      resetAttrVarsOrder()
    }
  }

  const handleAttrVarDelete = (row: any) => {
    if (!row) return
    SLConfirm('确定移除此行？', '提示信息')
      .then(() => {
        const index = TableConfig.value.dataList.findIndex(item => item.orderNum === row.orderNum)
        TableConfig.value.dataList.splice(index, 1)
        resetAttrVarsOrder()
      })
      .catch(() => {
        //
      })
  }

  const addAttrRow = (type?: string, stationId?: string) => {
    const formC = attrFieldConfig()
    TableConfig.value.dataList.push({
      stationId,
      orderNum: TableConfig.value.dataList.length + 1,
      deviceId: '',
      attrAlias: '',
      attr: '',
      name: '',
      type,
      attrAliasFormItemConfig: formC
    })
  }
  const resetAttrVarsOrder = () => {
    const list = sortBy(TableConfig.value.dataList, o => o.orderNum)
    TableConfig.value.dataList = list.map((item, i) => {
      // 设置序号从1开始排
      item.orderNum = i + 1
      return item
    })
  }
  const refreshData = async (type?: string | number) => {
    TableConfig.value.loading = true
    try {
      // 先处理
      const attrList = attrGroup.group.value.find(item => item.type === type)?.attrList || []
      const ids = attrList.map(item => item.deviceId)
      await resolveDeviceAttrs(ids)
      TableConfig.value.dataList = attrList?.map(item => {
        if (item.range) {
          const minMax = item.range?.split(',')
          if (minMax?.length === 2) {
            item.min = minMax[0]
            item.max = minMax[1]
          }
        }
        item.attrAlias = item.attr
        item.attrAliasFormItemConfig = attrFieldConfig(item.deviceId)
        return item
      }) || []
      // resetAttrVarsOrder()
      // const p = Promise.allSettled(TableConfig.value.dataList.map(item => getRowAttrs(item)))
    } catch (error) {
      //
    }
    TableConfig.value.loading = false
  }
  const resolveDeviceAttrs = async (ids: string[]) => {
    // 过滤deviceId,从deviceAttrListObjs中拿数据
    //
    const deviceIds = Array.from(new Set(ids)).filter(
      item => !!item && deviceAttrListObjs.findIndex(o => o.deviceId === item) === -1
    )
    const ps = deviceIds.map(devid => {
      return getAttrsOfDevice(removeSlash(devid))
    })
    const res = await Promise.allSettled(ps)
    res.map((item, i) => {
      item.status === 'fulfilled' && deviceAttrListObjs.push({ deviceId: deviceIds[i], attrList: item.value })
    })
  }

  return {
    TableConfig,
    refreshTableColumns,
    addAttrRow,
    refreshData,
    attrGroup,
    devices
  }
}
