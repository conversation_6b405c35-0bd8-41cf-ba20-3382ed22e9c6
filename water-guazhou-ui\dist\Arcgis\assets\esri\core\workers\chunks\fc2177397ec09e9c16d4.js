"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[9230],{92835:(e,t,r)=>{r.d(t,{Z:()=>g});var s,i=r(43697),n=r(96674),o=r(70586),a=r(35463),l=r(5600),h=(r(75215),r(67676),r(71715)),u=r(52011),c=r(30556);let d=s=class extends n.wq{static get allTime(){return p}static get empty(){return m}constructor(e){super(e),this.end=null,this.start=null}readEnd(e,t){return null!=t.end?new Date(t.end):null}writeEnd(e,t){t.end=e?e.getTime():null}get isAllTime(){return this.equals(s.allTime)}get isEmpty(){return this.equals(s.empty)}readStart(e,t){return null!=t.start?new Date(t.start):null}writeStart(e,t){t.start=e?e.getTime():null}clone(){return new s({end:this.end,start:this.start})}equals(e){if(!e)return!1;const t=(0,o.pC)(this.start)?this.start.getTime():this.start,r=(0,o.pC)(this.end)?this.end.getTime():this.end,s=(0,o.pC)(e.start)?e.start.getTime():e.start,i=(0,o.pC)(e.end)?e.end.getTime():e.end;return t===s&&r===i}expandTo(e){if(this.isEmpty||this.isAllTime)return this.clone();const t=(0,o.yw)(this.start,(t=>(0,a.JE)(t,e))),r=(0,o.yw)(this.end,(t=>{const r=(0,a.JE)(t,e);return t.getTime()===r.getTime()?r:(0,a.Nm)(r,1,e)}));return new s({start:t,end:r})}intersection(e){if(!e)return this.clone();if(this.isEmpty||e.isEmpty)return s.empty;if(this.isAllTime)return e.clone();if(e.isAllTime)return this.clone();const t=(0,o.R2)(this.start,-1/0,(e=>e.getTime())),r=(0,o.R2)(this.end,1/0,(e=>e.getTime())),i=(0,o.R2)(e.start,-1/0,(e=>e.getTime())),n=(0,o.R2)(e.end,1/0,(e=>e.getTime()));let a,l;if(i>=t&&i<=r?a=i:t>=i&&t<=n&&(a=t),r>=i&&r<=n?l=r:n>=t&&n<=r&&(l=n),null!=a&&null!=l&&!isNaN(a)&&!isNaN(l)){const e=new s;return e.start=a===-1/0?null:new Date(a),e.end=l===1/0?null:new Date(l),e}return s.empty}offset(e,t){if(this.isEmpty||this.isAllTime)return this.clone();const r=new s,{start:i,end:n}=this;return(0,o.pC)(i)&&(r.start=(0,a.Nm)(i,e,t)),(0,o.pC)(n)&&(r.end=(0,a.Nm)(n,e,t)),r}union(e){if(!e||e.isEmpty)return this.clone();if(this.isEmpty)return e.clone();if(this.isAllTime||e.isAllTime)return p.clone();const t=(0,o.pC)(this.start)&&(0,o.pC)(e.start)?new Date(Math.min(this.start.getTime(),e.start.getTime())):null,r=(0,o.pC)(this.end)&&(0,o.pC)(e.end)?new Date(Math.max(this.end.getTime(),e.end.getTime())):null;return new s({start:t,end:r})}};(0,i._)([(0,l.Cb)({type:Date,json:{write:{allowNull:!0}}})],d.prototype,"end",void 0),(0,i._)([(0,h.r)("end")],d.prototype,"readEnd",null),(0,i._)([(0,c.c)("end")],d.prototype,"writeEnd",null),(0,i._)([(0,l.Cb)({readOnly:!0,json:{read:!1}})],d.prototype,"isAllTime",null),(0,i._)([(0,l.Cb)({readOnly:!0,json:{read:!1}})],d.prototype,"isEmpty",null),(0,i._)([(0,l.Cb)({type:Date,json:{write:{allowNull:!0}}})],d.prototype,"start",void 0),(0,i._)([(0,h.r)("start")],d.prototype,"readStart",null),(0,i._)([(0,c.c)("start")],d.prototype,"writeStart",null),d=s=(0,i._)([(0,u.j)("esri.TimeExtent")],d);const p=new d,m=new d({start:void 0,end:void 0}),g=d},13867:(e,t,r)=>{r.d(t,{Z:()=>i});var s=r(69801);class i{constructor(e,t){this._storage=new s.WJ,this._storage.maxSize=e,t&&this._storage.registerRemoveFunc("",t)}put(e,t,r){this._storage.put(e,t,r,1)}pop(e){return this._storage.pop(e)}get(e){return this._storage.get(e)}clear(){this._storage.clearAll()}destroy(){this._storage.destroy()}get maxSize(){return this._storage.maxSize}set maxSize(e){this._storage.maxSize=e}}},69801:(e,t,r)=>{r.d(t,{WJ:()=>l,Xq:()=>a});var s,i,n=r(70586),o=r(44553);(i=s||(s={}))[i.ALL=0]="ALL",i[i.SOME=1]="SOME";class a{constructor(e,t,r){this._namespace=e,this._storage=t,this._removeFunc=!1,this._hit=0,this._miss=0,this._storage.register(this),this._namespace+=":",r&&(this._storage.registerRemoveFunc(this._namespace,r),this._removeFunc=!0)}destroy(){this._storage.clear(this._namespace),this._removeFunc&&this._storage.deregisterRemoveFunc(this._namespace),this._storage.deregister(this),this._storage=null}get namespace(){return this._namespace.slice(0,-1)}get hitRate(){return this._hit/(this._hit+this._miss)}get size(){return this._storage.size}get maxSize(){return this._storage.maxSize}resetHitRate(){this._hit=this._miss=0}put(e,t,r,s=0){this._storage.put(this._namespace+e,t,r,s)}get(e){const t=this._storage.get(this._namespace+e);return void 0===t?++this._miss:++this._hit,t}pop(e){const t=this._storage.pop(this._namespace+e);return void 0===t?++this._miss:++this._hit,t}updateSize(e,t,r){this._storage.updateSize(this._namespace+e,t,r)}clear(){this._storage.clear(this._namespace)}clearAll(){this._storage.clearAll()}getStats(){return this._storage.getStats()}resetStats(){this._storage.resetStats()}}class l{constructor(e=10485760){this._maxSize=e,this._db=new Map,this._size=0,this._hit=0,this._miss=0,this._removeFuncs=new o.Z,this._users=new o.Z}destroy(){this.clearAll(),this._removeFuncs.clear(),this._users.clear(),this._db=null}register(e){this._users.push(e)}deregister(e){this._users.removeUnordered(e)}registerRemoveFunc(e,t){this._removeFuncs.push([e,t])}deregisterRemoveFunc(e){this._removeFuncs.filterInPlace((t=>t[0]!==e))}get size(){return this._size}get maxSize(){return this._maxSize}set maxSize(e){this._maxSize=Math.max(e,0),this._checkSizeLimit()}put(e,t,r,i){const n=this._db.get(e);if(n&&(this._size-=n.size,this._db.delete(e),n.entry!==t&&this._notifyRemove(e,n.entry,s.ALL)),r>this._maxSize)return void this._notifyRemove(e,t,s.ALL);if(void 0===t)return void console.warn("Refusing to cache undefined entry ");if(!r||r<0)return void console.warn("Refusing to cache entry with invalid size "+r);const o=1+Math.max(i,-3)- -3;this._db.set(e,{entry:t,size:r,lifetime:o,lives:o}),this._size+=r,this._checkSizeLimit()}updateSize(e,t,r){const i=this._db.get(e);if(i&&i.entry===t){for(this._size-=i.size;r>this._maxSize;){const i=this._notifyRemove(e,t,s.SOME);if(!((0,n.pC)(i)&&i>0))return void this._db.delete(e);r=i}i.size=r,this._size+=r,this._checkSizeLimit()}}pop(e){const t=this._db.get(e);if(t)return this._size-=t.size,this._db.delete(e),++this._hit,t.entry;++this._miss}get(e){const t=this._db.get(e);if(void 0!==t)return this._db.delete(e),t.lives=t.lifetime,this._db.set(e,t),++this._hit,t.entry;++this._miss}getStats(){const e={Size:Math.round(this._size/1048576)+"/"+Math.round(this._maxSize/1048576)+"MB","Hit rate":Math.round(100*this._getHitRate())+"%",Entries:this._db.size.toString()},t={},r=new Array;this._db.forEach(((e,s)=>{const i=e.lifetime;r[i]=(r[i]||0)+e.size,this._users.forAll((r=>{const i=r.namespace;if(s.startsWith(i)){const r=t[i]||0;t[i]=r+e.size}}))}));const s={};this._users.forAll((e=>{const r=e.namespace;if(!isNaN(e.hitRate)&&e.hitRate>0){const i=t[r]||0;t[r]=i,s[r]=Math.round(100*e.hitRate)+"%"}else s[r]="0%"}));const i=Object.keys(t);i.sort(((e,r)=>t[r]-t[e])),i.forEach((r=>e[r]=Math.round(t[r]/2**20)+"MB / "+s[r]));for(let t=r.length-1;t>=0;--t){const s=r[t];s&&(e["Priority "+(t+-3-1)]=Math.round(s/this.size*100)+"%")}return e}resetStats(){this._hit=this._miss=0,this._users.forAll((e=>e.resetHitRate()))}clear(e){this._db.forEach(((t,r)=>{r.startsWith(e)&&(this._size-=t.size,this._db.delete(r),this._notifyRemove(r,t.entry,s.ALL))}))}clearAll(){this._db.forEach(((e,t)=>this._notifyRemove(t,e.entry,s.ALL))),this._size=0,this._db.clear()}_getHitRate(){return this._hit/(this._hit+this._miss)}_notifyRemove(e,t,r){let s;return this._removeFuncs.some((i=>{if(e.startsWith(i[0])){const e=i[1](t,r);return"number"==typeof e&&(s=e),!0}return!1})),s}_checkSizeLimit(){if(!(this._size<=this._maxSize))for(const[e,t]of this._db){if(this._db.delete(e),t.lives<=1){this._size-=t.size;const r=this._notifyRemove(e,t.entry,s.SOME);(0,n.pC)(r)&&r>0&&(this._size+=r,t.lives=t.lifetime,t.size=r,this._db.set(e,t))}else--t.lives,this._db.set(e,t);if(this._size<=.9*this.maxSize)return}}}},16453:(e,t,r)=>{r.d(t,{R:()=>v,w:()=>w});var s=r(43697),i=r(15923),n=r(70586),o=r(41103),a=r(22974),l=r(31263);class h{constructor(){this._propertyOriginMap=new Map,this._originStores=new Array(l.kk),this._values=new Map,this.multipleOriginsSupported=!0}clone(e){const t=new h,r=this._originStores[l.s3.DEFAULTS];r&&r.forEach(((e,r)=>{t.set(r,(0,a.d9)(e),l.s3.DEFAULTS)}));for(let r=l.s3.SERVICE;r<l.kk;r++){const s=this._originStores[r];s&&s.forEach(((s,i)=>{e&&e.has(i)||t.set(i,(0,a.d9)(s),r)}))}return t}get(e,t){const r=void 0===t?this._values:this._originStores[t];return r?r.get(e):void 0}keys(e){const t=null==e?this._values:this._originStores[e];return t?[...t.keys()]:[]}set(e,t,r=l.s3.USER){let s=this._originStores[r];if(s||(s=new Map,this._originStores[r]=s),s.set(e,t),!this._values.has(e)||(0,n.j0)(this._propertyOriginMap.get(e))<=r){const s=this._values.get(e);return this._values.set(e,t),this._propertyOriginMap.set(e,r),s!==t}return!1}delete(e,t=l.s3.USER){const r=this._originStores[t];if(!r)return;const s=r.get(e);if(r.delete(e),this._values.has(e)&&this._propertyOriginMap.get(e)===t){this._values.delete(e);for(let r=t-1;r>=0;r--){const t=this._originStores[r];if(t&&t.has(e)){this._values.set(e,t.get(e)),this._propertyOriginMap.set(e,r);break}}}return s}has(e,t){const r=void 0===t?this._values:this._originStores[t];return!!r&&r.has(e)}revert(e,t){for(;t>0&&!this.has(e,t);)--t;const r=this._originStores[t],s=r&&r.get(e),i=this._values.get(e);return this._values.set(e,s),this._propertyOriginMap.set(e,t),i!==s}originOf(e){return this._propertyOriginMap.get(e)||l.s3.DEFAULTS}forEach(e){this._values.forEach(e)}}var u=r(50549),c=r(1153),d=r(52011);const p=e=>{let t=class extends e{constructor(...e){super(...e);const t=(0,n.j0)((0,c.vw)(this)),r=t.store,s=new h;t.store=s,(0,o.M)(t,r,s)}read(e,t){(0,u.i)(this,e,t)}getAtOrigin(e,t){const r=m(this),s=(0,l.M9)(t);if("string"==typeof e)return r.get(e,s);const i={};return e.forEach((e=>{i[e]=r.get(e,s)})),i}originOf(e){return(0,l.x3)(this.originIdOf(e))}originIdOf(e){return m(this).originOf(e)}revert(e,t){const r=m(this),s=(0,l.M9)(t),i=(0,c.vw)(this);let n;n="string"==typeof e?"*"===e?r.keys(s):[e]:e,n.forEach((e=>{i.invalidate(e),r.revert(e,s),i.commit(e)}))}};return t=(0,s._)([(0,d.j)("esri.core.ReadOnlyMultiOriginJSONSupport")],t),t};function m(e){return(0,c.vw)(e).store}let g=class extends(p(i.Z)){};g=(0,s._)([(0,d.j)("esri.core.ReadOnlyMultiOriginJSONSupport")],g);var y=r(76169);const f=e=>{let t=class extends e{constructor(...e){super(...e)}clear(e,t="user"){return _(this).delete(e,(0,l.M9)(t))}write(e={},t){return(0,y.c)(this,e=e||{},t),e}setAtOrigin(e,t,r){(0,c.vw)(this).setAtOrigin(e,t,(0,l.M9)(r))}removeOrigin(e){const t=_(this),r=(0,l.M9)(e),s=t.keys(r);for(const e of s)t.originOf(e)===r&&t.set(e,t.get(e,r),l.s3.USER)}updateOrigin(e,t){const r=_(this),s=(0,l.M9)(t),i=this.get(e);for(let t=s+1;t<l.kk;++t)r.delete(e,t);r.set(e,i,s)}toJSON(e){return this.write({},e)}};return t=(0,s._)([(0,d.j)("esri.core.WriteableMultiOriginJSONSupport")],t),t.prototype.toJSON.isDefaultToJSON=!0,t};function _(e){return(0,c.vw)(e).store}const v=e=>{let t=class extends(f(p(e))){constructor(...e){super(...e)}};return t=(0,s._)([(0,d.j)("esri.core.MultiOriginJSONSupport")],t),t};let w=class extends(v(i.Z)){};w=(0,s._)([(0,d.j)("esri.core.MultiOriginJSONSupport")],w)},64830:(e,t,r)=>{r.d(t,{Z:()=>i});var s=r(70586);class i{constructor(e=(e=>e.values().next().value)){this._peeker=e,this._items=new Set}get length(){return this._items.size}clear(){this._items.clear()}last(){if(0===this._items.size)return;let e;for(e of this._items);return e}peek(){if(0!==this._items.size)return this._peeker(this._items)}push(e){this.contains(e)||this._items.add(e)}contains(e){return this._items.has(e)}pop(){if(0===this.length)return;const e=this.peek();return this._items.delete((0,s.j0)(e)),e}popLast(){if(0===this.length)return;const e=this.last();return this._items.delete((0,s.j0)(e)),e}remove(e){this._items.delete(e)}filter(e){return this._items.forEach((t=>{e(t)||this._items.delete(t)})),this}}},17445:(e,t,r)=>{r.d(t,{N1:()=>d,YP:()=>l,Z_:()=>g,gx:()=>h,nn:()=>y,on:()=>c,tX:()=>f});var s=r(91460),i=r(50758),n=r(70586),o=r(95330),a=r(26258);function l(e,t,r={}){return u(e,t,r,p)}function h(e,t,r={}){return u(e,t,r,m)}function u(e,t,r={},s){let i=null;const o=r.once?(e,r)=>{s(e)&&((0,n.hw)(i),t(e,r))}:(e,r)=>{s(e)&&t(e,r)};if(i=(0,a.aQ)(e,o,r.sync,r.equals),r.initial){const t=e();o(t,t)}return i}function c(e,t,r,o={}){let a=null,h=null,u=null;function c(){a&&h&&(h.remove(),o.onListenerRemove?.(a),a=null,h=null)}function d(e){o.once&&o.once&&(0,n.hw)(u),r(e)}const p=l(e,((e,r)=>{c(),(0,s.vT)(e)&&(a=e,h=(0,s.on)(e,t,d),o.onListenerAdd?.(e))}),{sync:o.sync,initial:!0});return u=(0,i.kB)((()=>{p.remove(),c()})),u}function d(e,t){return function(e,t,r){if((0,o.Hc)(r))return Promise.reject((0,o.zE)());const s=e();if(t?.(s))return Promise.resolve(s);let a=null;function l(){a=(0,n.hw)(a)}return new Promise(((s,n)=>{a=(0,i.AL)([(0,o.fu)(r,(()=>{l(),n((0,o.zE)())})),u(e,(e=>{l(),s(e)}),{sync:!1,once:!0},t??p)])}))}(e,m,t)}function p(e){return!0}function m(e){return!!e}r(87538);const g={sync:!0},y={initial:!0},f={sync:!0,initial:!0}},35463:(e,t,r)=>{r.d(t,{JE:()=>o,Nm:()=>n,rJ:()=>a}),r(80442);const s={milliseconds:1,seconds:1e3,minutes:6e4,hours:36e5,days:864e5,weeks:6048e5,months:26784e5,years:31536e6,decades:31536e7,centuries:31536e8},i={milliseconds:{getter:"getMilliseconds",setter:"setMilliseconds",multiplier:1},seconds:{getter:"getSeconds",setter:"setSeconds",multiplier:1},minutes:{getter:"getMinutes",setter:"setMinutes",multiplier:1},hours:{getter:"getHours",setter:"setHours",multiplier:1},days:{getter:"getDate",setter:"setDate",multiplier:1},weeks:{getter:"getDate",setter:"setDate",multiplier:7},months:{getter:"getMonth",setter:"setMonth",multiplier:1},years:{getter:"getFullYear",setter:"setFullYear",multiplier:1},decades:{getter:"getFullYear",setter:"setFullYear",multiplier:10},centuries:{getter:"getFullYear",setter:"setFullYear",multiplier:100}};function n(e,t,r){const s=new Date(e.getTime());if(t&&r){const e=i[r],{getter:n,setter:o,multiplier:a}=e;if("months"===r){const e=function(e,t){const r=new Date(e,t+1,1);return r.setDate(0),r.getDate()}(s.getFullYear(),s.getMonth()+t);s.getDate()>e&&s.setDate(e)}s[o](s[n]()+t*a)}return s}function o(e,t){switch(t){case"milliseconds":return new Date(e.getTime());case"seconds":return new Date(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours(),e.getMinutes(),e.getSeconds());case"minutes":return new Date(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours(),e.getMinutes());case"hours":return new Date(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours());case"days":return new Date(e.getFullYear(),e.getMonth(),e.getDate());case"weeks":return new Date(e.getFullYear(),e.getMonth(),e.getDate()-e.getDay());case"months":return new Date(e.getFullYear(),e.getMonth(),1);case"years":return new Date(e.getFullYear(),0,1);case"decades":return new Date(e.getFullYear()-e.getFullYear()%10,0,1);case"centuries":return new Date(e.getFullYear()-e.getFullYear()%100,0,1);default:return new Date}}function a(e,t,r){return 0===e?0:e*s[t]/s[r]}},80903:(e,t,r)=>{r.d(t,{Z:()=>l});var s=r(50758),i=r(92604),n=r(95330),o=r(64830),a=r(25045);class l{constructor(){this._inUseClients=new Array,this._clients=new Array,this._clientPromises=new Array,this._ongoingJobsQueue=new o.Z}destroy(){this.close()}get closed(){return!this._clients||!this._clients.length}open(e,t){return new Promise(((r,s)=>{let i=!0;const o=e=>{(0,n.k_)(t.signal),i&&(i=!1,e())};this._clients.length=e.length,this._clientPromises.length=e.length,this._inUseClients.length=e.length;for(let i=0;i<e.length;++i){const l=e[i];(0,n.y8)(l)?this._clientPromises[i]=l.then((e=>(this._clients[i]=new a.default(e,t,(()=>this._ongoingJobsQueue.pop()??null)),o(r),this._clients[i])),(()=>(o(s),null))):(this._clients[i]=new a.default(l,t,(()=>this._ongoingJobsQueue.pop()??null)),this._clientPromises[i]=Promise.resolve(this._clients[i]),o(r))}}))}broadcast(e,t,r){const s=new Array(this._clientPromises.length);for(let i=0;i<this._clientPromises.length;++i){const n=this._clientPromises[i];s[i]=n.then((s=>s?.invoke(e,t,r)))}return s}close(){let e;for(;e=this._ongoingJobsQueue.pop();)e.deferred.reject((0,n.zE)(`Worker closing, aborting job calling '${e.methodName}'`));for(const e of this._clientPromises)e.then((e=>e?.close()));this._clients.length=0,this._clientPromises.length=0}invoke(e,t,r){let s;Array.isArray(r)?(i.Z.getLogger("esri.core.workers.Connection").warn("invoke()","The transferList parameter is deprecated, use the options object instead"),s={transferList:r}):s=r;const o=(0,n.dD)();this._ongoingJobsQueue.push({methodName:e,data:t,invokeOptions:s,deferred:o});for(let e=0;e<this._clientPromises.length;e++){const t=this._clients[e];t?t.jobAdded():this._clientPromises[e].then((e=>e?.jobAdded()))}return o.promise}on(e,t){return Promise.all(this._clientPromises).then((()=>(0,s.AL)(this._clients.map((r=>r.on(e,t))))))}openPorts(){return new Promise((e=>{const t=new Array(this._clientPromises.length);let r=t.length;for(let s=0;s<this._clientPromises.length;++s)this._clientPromises[s].then((i=>{i&&(t[s]=i.openPort()),0==--r&&e(t)}))}))}get test(){return{numClients:this._clients.length}}}},78346:(e,t,r)=>{r.d(t,{bA:()=>U});var s=r(20102),i=r(80442),n=r(95330),o=r(80903),a=r(25045),l=r(40330),h=r(92604),u=r(70586),c=r(94362),d=r(99880),p=r(68773),m=(r(2587),r(17452));const g={};function y(e){const t={async:e.async,isDebug:e.isDebug,locale:e.locale,baseUrl:e.baseUrl,has:{...e.has},map:{...e.map},packages:e.packages&&e.packages.concat()||[],paths:{...e.paths}};return e.hasOwnProperty("async")||(t.async=!0),e.hasOwnProperty("isDebug")||(t.isDebug=!1),e.baseUrl||(t.baseUrl=g.baseUrl),t}var f=r(41213);class _{constructor(){const e=document.createDocumentFragment();["addEventListener","dispatchEvent","removeEventListener"].forEach((t=>{this[t]=(...r)=>e[t](...r)}))}}class v{constructor(){this._dispatcher=new _,this._workerPostMessage({type:c.Cs.HANDSHAKE})}terminate(){}get onmessage(){return this._onmessageHandler}set onmessage(e){this._onmessageHandler&&this.removeEventListener("message",this._onmessageHandler),this._onmessageHandler=e,e&&this.addEventListener("message",e)}get onmessageerror(){return this._onmessageerrorHandler}set onmessageerror(e){this._onmessageerrorHandler&&this.removeEventListener("messageerror",this._onmessageerrorHandler),this._onmessageerrorHandler=e,e&&this.addEventListener("messageerror",e)}get onerror(){return this._onerrorHandler}set onerror(e){this._onerrorHandler&&this.removeEventListener("error",this._onerrorHandler),this._onerrorHandler=e,e&&this.addEventListener("error",e)}postMessage(e){(0,f.Y)((()=>{this._workerMessageHandler(new MessageEvent("message",{data:e}))}))}dispatchEvent(e){return this._dispatcher.dispatchEvent(e)}addEventListener(e,t,r){this._dispatcher.addEventListener(e,t,r)}removeEventListener(e,t,r){this._dispatcher.removeEventListener(e,t,r)}_workerPostMessage(e){(0,f.Y)((()=>{this.dispatchEvent(new MessageEvent("message",{data:e}))}))}async _workerMessageHandler(e){const t=(0,c.QM)(e);if(t&&t.type===c.Cs.OPEN){const{modulePath:e,jobId:r}=t;let s=await a.default.loadWorker(e);s||(s=await import(e));const i=a.default.connect(s);this._workerPostMessage({type:c.Cs.OPENED,jobId:r,data:i})}}}var w=r(70171),b=r(17202);const S=h.Z.getLogger("esri.core.workers.workerFactory"),{HANDSHAKE:k}=c.Cs;let L,E;const I="Failed to create Worker. Fallback to execute module in main thread";async function M(e){return new Promise((t=>{function r(i){const n=(0,c.QM)(i);n&&n.type===k&&(e.removeEventListener("message",r),e.removeEventListener("error",s),t(e))}function s(t){t.preventDefault(),e.removeEventListener("message",r),e.removeEventListener("error",s),S.warn("Failed to create Worker. Fallback to execute module in main thread",t),(e=new v).addEventListener("message",r),e.addEventListener("error",s)}e.addEventListener("message",r),e.addEventListener("error",s)}))}function C(){let e;if(null!=p.Z.default){const t={...p.Z};delete t.default,e=JSON.parse(JSON.stringify(t))}else e=JSON.parse(JSON.stringify(p.Z));e.assetsPath=(0,m.hF)(e.assetsPath),e.defaultAssetsPath=e.defaultAssetsPath?(0,m.hF)(e.defaultAssetsPath):void 0,e.request.interceptors=[],e.log.interceptors=[],e.locale=(0,w.Kd)(),e.has={"esri-csp-restrictions":(0,i.Z)("esri-csp-restrictions"),"esri-2d-debug":!1,"esri-2d-update-debug":(0,i.Z)("esri-2d-update-debug"),"featurelayer-pbf":(0,i.Z)("featurelayer-pbf"),"featurelayer-simplify-thresholds":(0,i.Z)("featurelayer-simplify-thresholds"),"featurelayer-simplify-payload-size-factors":(0,i.Z)("featurelayer-simplify-payload-size-factors"),"featurelayer-simplify-mobile-factor":(0,i.Z)("featurelayer-simplify-mobile-factor"),"esri-atomics":(0,i.Z)("esri-atomics"),"esri-shared-array-buffer":(0,i.Z)("esri-shared-array-buffer"),"esri-tiles-debug":(0,i.Z)("esri-tiles-debug"),"esri-workers-arraybuffer-transfer":(0,i.Z)("esri-workers-arraybuffer-transfer"),"feature-polyline-generalization-factor":(0,i.Z)("feature-polyline-generalization-factor"),"host-webworker":1,"polylabel-placement-enabled":(0,i.Z)("polylabel-placement-enabled")},e.workers.loaderUrl&&(e.workers.loaderUrl=(0,m.hF)(e.workers.loaderUrl)),e.workers.workerPath?e.workers.workerPath=(0,m.hF)(e.workers.workerPath):e.workers.workerPath=(0,m.hF)((0,d.V)("esri/core/workers/RemoteClient.js")),e.workers.useDynamicImport=!1;const t=p.Z.workers.loaderConfig,r=y({baseUrl:t?.baseUrl,locale:(0,w.Kd)(),has:{"csp-restrictions":1,"dojo-test-sniff":0,"host-webworker":1,...t?.has},map:{...t?.map},paths:{...t?.paths},packages:t?.packages||[]}),s={version:l.i8,buildDate:b.r,revision:b.$};return JSON.stringify({esriConfig:e,loaderConfig:r,kernelInfo:s})}let T=0;const{ABORT:O,INVOKE:D,OPEN:A,OPENED:x,RESPONSE:P}=c.Cs;class j{static async create(e){const t=await async function(){if(!(0,i.Z)("esri-workers")||((0,i.Z)("mozilla"),0))return M(new v);if(!L&&!E)try{const e='let globalId=0;const outgoing=new Map,configuration=JSON.parse("{CONFIGURATION}");self.esriConfig=configuration.esriConfig;const workerPath=self.esriConfig.workers.workerPath,HANDSHAKE=0,OPEN=1,OPENED=2,RESPONSE=3,INVOKE=4,ABORT=5;function createAbortError(){const e=new Error("Aborted");return e.name="AbortError",e}function receiveMessage(e){return e&&e.data?"string"==typeof e.data?JSON.parse(e.data):e.data:null}function invokeStaticMessage(e,o,r){const t=r&&r.signal,n=globalId++;return new Promise(((r,i)=>{if(t){if(t.aborted)return i(createAbortError());t.addEventListener("abort",(()=>{outgoing.get(n)&&(outgoing.delete(n),self.postMessage({type:5,jobId:n}),i(createAbortError()))}))}outgoing.set(n,{resolve:r,reject:i}),self.postMessage({type:4,jobId:n,methodName:e,abortable:null!=t,data:o})}))}let workerRevisionChecked=!1;function checkWorkerRevision(e){if(!workerRevisionChecked&&e.kernelInfo){workerRevisionChecked=!0;const{revision:o,version:r}=configuration.kernelInfo,{revision:t,version:n}=e.kernelInfo;esriConfig.assetsPath!==esriConfig.defaultAssetsPath&&o!==t&&console.warn(`Version mismatch detected between ArcGIS API for JavaScript modules and assets. For more information visit https://bit.ly/3QnsuSo.\\nModules version: ${r}\\nAssets version: ${n}`)}}function messageHandler(e){const o=receiveMessage(e);if(!o)return;const r=o.jobId;switch(o.type){case 1:let n;function t(e){const o=n.connect(e);self.postMessage({type:2,jobId:r,data:o},[o])}"function"==typeof define&&define.amd?require([workerPath],(e=>{n=e.default||e,checkWorkerRevision(n),n.loadWorker(o.modulePath).then((e=>e||new Promise((e=>{require([o.modulePath],e)})))).then(t)})):"System"in self&&"function"==typeof System.import?System.import(workerPath).then((e=>(n=e.default,checkWorkerRevision(n),n.loadWorker(o.modulePath)))).then((e=>e||System.import(o.modulePath))).then(t):esriConfig.workers.useDynamicImport?import(workerPath).then((e=>{n=e.default||e,checkWorkerRevision(n),n.loadWorker(o.modulePath).then((e=>e||import(o.modulePath))).then(t)})):(self.RemoteClient||importScripts(workerPath),n=self.RemoteClient.default||self.RemoteClient,checkWorkerRevision(n),n.loadWorker(o.modulePath).then(t));break;case 3:if(outgoing.has(r)){const i=outgoing.get(r);outgoing.delete(r),o.error?i.reject(JSON.parse(o.error)):i.resolve(o.data)}}}self.dojoConfig=configuration.loaderConfig,esriConfig.workers.loaderUrl&&(self.importScripts(esriConfig.workers.loaderUrl),"function"==typeof require&&"function"==typeof require.config&&require.config(configuration.loaderConfig)),self.addEventListener("message",messageHandler),self.postMessage({type:0});'.split('"{CONFIGURATION}"').join(`'${C()}'`);L=URL.createObjectURL(new Blob([e],{type:"text/javascript"}))}catch(e){E=e||{}}let e;if(L)try{e=new Worker(L,{name:"esri-worker-"+T++})}catch(t){S.warn(I,E),e=new v}else S.warn(I,E),e=new v;return M(e)}();return new j(t,e)}constructor(e,t){this._outJobs=new Map,this._inJobs=new Map,this.worker=e,this.id=t,e.addEventListener("message",this._onMessage.bind(this)),e.addEventListener("error",(e=>{e.preventDefault(),h.Z.getLogger("esri.core.workers.WorkerOwner").error(e)}))}terminate(){this.worker.terminate()}async open(e,t={}){const{signal:r}=t,s=(0,c.jt)();return new Promise(((t,i)=>{const o={resolve:t,reject:i,abortHandle:(0,n.$F)(r,(()=>{this._outJobs.delete(s),this._post({type:O,jobId:s})}))};this._outJobs.set(s,o),this._post({type:A,jobId:s,modulePath:e})}))}_onMessage(e){const t=(0,c.QM)(e);if(t)switch(t.type){case x:this._onOpenedMessage(t);break;case P:this._onResponseMessage(t);break;case O:this._onAbortMessage(t);break;case D:this._onInvokeMessage(t)}}_onAbortMessage(e){const t=this._inJobs,r=e.jobId,s=t.get(r);s&&(s.controller&&s.controller.abort(),t.delete(r))}_onInvokeMessage(e){const{methodName:t,jobId:r,data:s,abortable:i}=e,o=i?new AbortController:null,a=this._inJobs,h=l.Nv[t];let u;try{if("function"!=typeof h)throw new TypeError(`${t} is not a function`);u=h.call(null,s,{signal:o?o.signal:null})}catch(e){return void this._post({type:P,jobId:r,error:(0,c.AB)(e)})}(0,n.y8)(u)?(a.set(r,{controller:o,promise:u}),u.then((e=>{a.has(r)&&(a.delete(r),this._post({type:P,jobId:r},e))}),(e=>{a.has(r)&&(a.delete(r),e||(e={message:"Error encountered at method"+t}),(0,n.D_)(e)||this._post({type:P,jobId:r,error:(0,c.AB)(e||{message:`Error encountered at method ${t}`})}))}))):this._post({type:P,jobId:r},u)}_onOpenedMessage(e){const{jobId:t,data:r}=e,s=this._outJobs.get(t);s&&(this._outJobs.delete(t),(0,u.hw)(s.abortHandle),s.resolve(r))}_onResponseMessage(e){const{jobId:t,error:r,data:i}=e,n=this._outJobs.get(t);n&&(this._outJobs.delete(t),(0,u.hw)(n.abortHandle),r?n.reject(s.Z.fromJSON(JSON.parse(r))):n.resolve(i))}_post(e,t,r){return(0,c.oi)(this.worker,e,t,r)}}let R=(0,i.Z)("esri-workers-debug")?1:(0,i.Z)("esri-mobile")?Math.min(navigator.hardwareConcurrency-1,3):(0,i.Z)("host-browser")?navigator.hardwareConcurrency-1:0;R||(R=(0,i.Z)("safari")&&(0,i.Z)("mac")?7:2);let N=0;const F=[];async function z(e,t){const r=new o.Z;return await r.open(e,t),r}async function U(e,t={}){if("string"!=typeof e)throw new s.Z("workers:undefined-module","modulePath is missing");let r=t.strategy||"distributed";if((0,i.Z)("host-webworker")&&!(0,i.Z)("esri-workers")&&(r="local"),"local"===r){let r=await a.default.loadWorker(e);r||(r=await import(e)),(0,n.k_)(t.signal);const s=t.client||r;return z([a.default.connect(r)],{...t,client:s})}if(await async function(){if(H)return H;Z=new AbortController;const e=[];for(let t=0;t<R;t++){const r=j.create(t).then((e=>(F[t]=e,e)));e.push(r)}return H=Promise.all(e),H}(),(0,n.k_)(t.signal),"dedicated"===r){const r=N++%R;return z([await F[r].open(e,t)],t)}if(t.maxNumWorkers&&t.maxNumWorkers>0){const r=Math.min(t.maxNumWorkers,R);if(r<R){const s=new Array(r);for(let i=0;i<r;++i){const r=N++%R;s[i]=F[r].open(e,t)}return z(s,t)}}return z(F.map((r=>r.open(e,t))),t)}let Z,H=null},79235:(e,t,r)=>{r.d(t,{Z:()=>v});var s,i=r(43697),n=r(67676),o=r(35454),a=r(96674),l=r(67900),h=r(20941),u=r(5600),c=(r(75215),r(71715)),d=r(52011),p=r(30556);const m=(0,o.w)()({orthometric:"gravity-related-height",gravity_related_height:"gravity-related-height",ellipsoidal:"ellipsoidal"}),g=m.jsonValues.slice();(0,n.e$)(g,"orthometric");const y=(0,o.w)()({meter:"meters",foot:"feet","us-foot":"us-feet","clarke-foot":"clarke-feet","clarke-yard":"clarke-yards","clarke-link":"clarke-links","sears-yard":"sears-yards","sears-foot":"sears-feet","sears-chain":"sears-chains","benoit-1895-b-chain":"benoit-1895-b-chains","indian-yard":"indian-yards","indian-1937-yard":"indian-1937-yards","gold-coast-foot":"gold-coast-feet","sears-1922-truncated-chain":"sears-1922-truncated-chains","50-kilometers":"50-kilometers","150-kilometers":"150-kilometers"});let f=s=class extends a.wq{constructor(e){super(e),this.heightModel="gravity-related-height",this.heightUnit="meters",this.vertCRS=null}writeHeightModel(e,t,r){return m.write(e,t,r)}readHeightModel(e,t,r){return m.read(e)||(r&&r.messages&&r.messages.push(function(e,t){return new h.Z("height-model:unsupported",`Height model of value '${e}' is not supported`,t)}(e,{context:r})),null)}readHeightUnit(e,t,r){return y.read(e)||(r&&r.messages&&r.messages.push(_(e,{context:r})),null)}readHeightUnitService(e,t,r){return(0,l.$C)(e)||y.read(e)||(r&&r.messages&&r.messages.push(_(e,{context:r})),null)}readVertCRS(e,t){return t.vertCRS||t.ellipsoid||t.geoid}clone(){return new s({heightModel:this.heightModel,heightUnit:this.heightUnit,vertCRS:this.vertCRS})}equals(e){return!!e&&(this===e||this.heightModel===e.heightModel&&this.heightUnit===e.heightUnit&&this.vertCRS===e.vertCRS)}static deriveUnitFromSR(e,t){const r=(0,l.cM)(t);return new s({heightModel:e.heightModel,heightUnit:r,vertCRS:e.vertCRS})}write(e,t){return t={origin:"web-scene",...t},super.write(e,t)}static fromJSON(e){if(!e)return null;const t=new s;return t.read(e,{origin:"web-scene"}),t}};function _(e,t){return new h.Z("height-unit:unsupported",`Height unit of value '${e}' is not supported`,t)}(0,i._)([(0,u.Cb)({type:m.apiValues,constructOnly:!0,json:{origins:{"web-scene":{type:g,default:"ellipsoidal"}}}})],f.prototype,"heightModel",void 0),(0,i._)([(0,p.c)("web-scene","heightModel")],f.prototype,"writeHeightModel",null),(0,i._)([(0,c.r)(["web-scene","service"],"heightModel")],f.prototype,"readHeightModel",null),(0,i._)([(0,u.Cb)({type:y.apiValues,constructOnly:!0,json:{origins:{"web-scene":{type:y.jsonValues,write:y.write}}}})],f.prototype,"heightUnit",void 0),(0,i._)([(0,c.r)("web-scene","heightUnit")],f.prototype,"readHeightUnit",null),(0,i._)([(0,c.r)("service","heightUnit")],f.prototype,"readHeightUnitService",null),(0,i._)([(0,u.Cb)({type:String,constructOnly:!0,json:{origins:{"web-scene":{write:!0}}}})],f.prototype,"vertCRS",void 0),(0,i._)([(0,c.r)("service","vertCRS",["vertCRS","ellipsoid","geoid"])],f.prototype,"readVertCRS",null),f=s=(0,i._)([(0,d.j)("esri.geometry.HeightModelInfo")],f);const v=f},2587:(e,t,r)=>{r(90344),r(18848),r(940),r(70171);var s=r(94443),i=r(3172),n=r(20102),o=r(70586);async function a(e){if((0,o.pC)(h.fetchBundleAsset))return h.fetchBundleAsset(e);const t=await(0,i.default)(e,{responseType:"text"});return JSON.parse(t.data)}class l{constructor({base:e="",pattern:t,location:r=new URL(window.location.href)}){let s;s="string"==typeof r?e=>new URL(e,new URL(r,window.location.href)).href:r instanceof URL?e=>new URL(e,r).href:r,this.pattern="string"==typeof t?new RegExp(`^${t}`):t,this.getAssetUrl=s,e=e?e.endsWith("/")?e:e+"/":"",this.matcher=new RegExp(`^${e}(?:(.*)/)?(.*)$`)}fetchMessageBundle(e,t){return async function(e,t,r,i){const o=t.exec(r);if(!o)throw new n.Z("esri-intl:invalid-bundle",`Bundle id "${r}" is not compatible with the pattern "${t}"`);const l=o[1]?`${o[1]}/`:"",h=o[2],u=(0,s.Su)(i),c=`${l}${h}.json`,d=u?`${l}${h}_${u}.json`:c;let p;try{p=await a(e(d))}catch(t){if(d===c)throw new n.Z("intl:unknown-bundle",`Bundle "${r}" cannot be loaded`,{error:t});try{p=await a(e(c))}catch(e){throw new n.Z("intl:unknown-bundle",`Bundle "${r}" cannot be loaded`,{error:e})}}return p}(this.getAssetUrl,this.matcher,e,t)}}const h={};var u,c=r(99880);(0,s.tz)((u={pattern:"esri/",location:c.V},new l(u)))},90344:(e,t,r)=>{r.d(t,{Ze:()=>y,p6:()=>f});var s=r(35454),i=r(70171);const n={year:"numeric",month:"numeric",day:"numeric"},o={year:"numeric",month:"long",day:"numeric"},a={year:"numeric",month:"short",day:"numeric"},l={year:"numeric",month:"long",weekday:"long",day:"numeric"},h={hour:"numeric",minute:"numeric"},u={...h,second:"numeric"},c={"short-date":n,"short-date-short-time":{...n,...h},"short-date-short-time-24":{...n,...h,hour12:!1},"short-date-long-time":{...n,...u},"short-date-long-time-24":{...n,...u,hour12:!1},"short-date-le":n,"short-date-le-short-time":{...n,...h},"short-date-le-short-time-24":{...n,...h,hour12:!1},"short-date-le-long-time":{...n,...u},"short-date-le-long-time-24":{...n,...u,hour12:!1},"long-month-day-year":o,"long-month-day-year-short-time":{...o,...h},"long-month-day-year-short-time-24":{...o,...h,hour12:!1},"long-month-day-year-long-time":{...o,...u},"long-month-day-year-long-time-24":{...o,...u,hour12:!1},"day-short-month-year":a,"day-short-month-year-short-time":{...a,...h},"day-short-month-year-short-time-24":{...a,...h,hour12:!1},"day-short-month-year-long-time":{...a,...u},"day-short-month-year-long-time-24":{...a,...u,hour12:!1},"long-date":l,"long-date-short-time":{...l,...h},"long-date-short-time-24":{...l,...h,hour12:!1},"long-date-long-time":{...l,...u},"long-date-long-time-24":{...l,...u,hour12:!1},"long-month-year":{month:"long",year:"numeric"},"short-month-year":{month:"short",year:"numeric"},year:{year:"numeric"},"short-time":h,"long-time":u},d=(0,s.w)()({shortDate:"short-date",shortDateShortTime:"short-date-short-time",shortDateShortTime24:"short-date-short-time-24",shortDateLongTime:"short-date-long-time",shortDateLongTime24:"short-date-long-time-24",shortDateLE:"short-date-le",shortDateLEShortTime:"short-date-le-short-time",shortDateLEShortTime24:"short-date-le-short-time-24",shortDateLELongTime:"short-date-le-long-time",shortDateLELongTime24:"short-date-le-long-time-24",longMonthDayYear:"long-month-day-year",longMonthDayYearShortTime:"long-month-day-year-short-time",longMonthDayYearShortTime24:"long-month-day-year-short-time-24",longMonthDayYearLongTime:"long-month-day-year-long-time",longMonthDayYearLongTime24:"long-month-day-year-long-time-24",dayShortMonthYear:"day-short-month-year",dayShortMonthYearShortTime:"day-short-month-year-short-time",dayShortMonthYearShortTime24:"day-short-month-year-short-time-24",dayShortMonthYearLongTime:"day-short-month-year-long-time",dayShortMonthYearLongTime24:"day-short-month-year-long-time-24",longDate:"long-date",longDateShortTime:"long-date-short-time",longDateShortTime24:"long-date-short-time-24",longDateLongTime:"long-date-long-time",longDateLongTime24:"long-date-long-time-24",longMonthYear:"long-month-year",shortMonthYear:"short-month-year",year:"year"}),p=(d.apiValues,d.toJSON.bind(d),d.fromJSON.bind(d),{ar:"ar-u-nu-latn-ca-gregory"});let m=new WeakMap,g=c["short-date-short-time"];function y(e){return e?c[e]:null}function f(e,t){return function(e){const t=e||g;let r=m.get(t);if(!r){const e=(0,i.Kd)(),s=p[(0,i.Kd)()]||e;r=new Intl.DateTimeFormat(s,t),m.set(t,r)}return r}(t).format(e)}(0,i.Ze)((()=>{m=new WeakMap,g=c["short-date-short-time"]}))},18848:(e,t,r)=>{r.d(t,{sh:()=>l,uf:()=>h});var s=r(70586),i=r(70171);const n={ar:"ar-u-nu-latn"};let o=new WeakMap,a={};function l(e={}){const t={};return null!=e.digitSeparator&&(t.useGrouping=e.digitSeparator),null!=e.places&&(t.minimumFractionDigits=t.maximumFractionDigits=e.places),t}function h(e,t){return-0===e&&(e=0),function(e){const t=e||a;if(!o.has(t)){const r=(0,i.Kd)(),s=n[(0,i.Kd)()]||r;o.set(t,new Intl.NumberFormat(s,e))}return(0,s.j0)(o.get(t))}(t).format(e)}(0,i.Ze)((()=>{o=new WeakMap,a={}}))},940:(e,t,r)=>{r.d(t,{n:()=>h});var s=r(92604),i=r(78286),n=r(19153),o=r(90344),a=r(18848);const l=s.Z.getLogger("esri.intl.substitute");function h(e,t,r={}){const{format:s={}}=r;return(0,n.gx)(e,(e=>function(e,t,r){let s,n;const o=e.indexOf(":");if(-1===o?s=e.trim():(s=e.slice(0,o).trim(),n=e.slice(o+1).trim()),!s)return"";const a=(0,i.hS)(s,t);if(null==a)return"";const l=(n?r?.[n]:null)??r?.[s];return l?u(a,l):n?c(a,n):d(a)}(e,t,s)))}function u(e,t){switch(t.type){case"date":return(0,o.p6)(e,t.intlOptions);case"number":return(0,a.uf)(e,t.intlOptions);default:return l.warn("missing format descriptor for key {key}"),d(e)}}function c(e,t){switch(t.toLowerCase()){case"dateformat":return(0,o.p6)(e);case"numberformat":return(0,a.uf)(e);default:return l.warn(`inline format is unsupported since 4.12: ${t}`),/^(dateformat|datestring)/i.test(t)?(0,o.p6)(e):/^numberformat/i.test(t)?(0,a.uf)(e):d(e)}}function d(e){switch(typeof e){case"string":return e;case"number":return(0,a.uf)(e);case"boolean":return""+e;default:return e instanceof Date?(0,o.p6)(e):""}}},65665:(e,t,r)=>{r.r(t),r.d(t,{default:()=>T});var s=r(43697),i=r(3172),n=r(20102),o=r(70586),a=r(16453),l=r(95330),h=r(17452),u=r(5600),c=(r(75215),r(67676)),d=r(71715),p=r(52011),m=r(79235),g=r(87085),y=r(46486),f=r(17287),_=r(38009),v=r(16859),w=r(21506);class b{constructor(e,t,r,s){this._hasNoDataValues=null,this._minValue=null,this._maxValue=null,"pixelData"in e?(this.values=e.pixelData,this.width=e.width,this.height=e.height,this.noDataValue=e.noDataValue):(this.values=e,this.width=t,this.height=r,this.noDataValue=s)}get hasNoDataValues(){if((0,o.Wi)(this._hasNoDataValues)){const e=this.noDataValue;this._hasNoDataValues=this.values.includes(e)}return this._hasNoDataValues}get minValue(){return this._ensureBounds(),(0,o.Wg)(this._minValue)}get maxValue(){return this._ensureBounds(),(0,o.Wg)(this._maxValue)}_ensureBounds(){if((0,o.pC)(this._minValue))return;const{noDataValue:e,values:t}=this;let r=1/0,s=-1/0,i=!0;for(const n of t)n===e?this._hasNoDataValues=!0:(r=n<r?n:r,s=n>s?n:s,i=!1);i?(this._minValue=0,this._maxValue=0):(this._minValue=r,this._maxValue=s>-3e38?s:0)}}var S=r(50758),k=r(92604),L=r(78346);class E{constructor(e,t,r,s,i={}){this._mainMethod=t,this._transferLists=r,this._listeners=[],this._promise=(0,L.bA)(e,{...i,schedule:s}).then((e=>{if(void 0===this._thread){this._thread=e,this._promise=null,i.hasInitialize&&this.broadcast({},"initialize");for(const e of this._listeners)this._connectListener(e)}else e.close()})),this._promise.catch((t=>k.Z.getLogger("esri.core.workers.WorkerHandle").error(`Failed to initialize ${e} worker: ${t}`)))}on(e,t){const r={removed:!1,eventName:e,callback:t,threadHandle:null};return this._listeners.push(r),this._connectListener(r),(0,S.kB)((()=>{r.removed=!0,(0,c.Od)(this._listeners,r),this._thread&&(0,o.pC)(r.threadHandle)&&r.threadHandle.remove()}))}destroy(){this._thread&&(this._thread.close(),this._thread=null),this._promise=null}invoke(e,t){return this.invokeMethod(this._mainMethod,e,t)}invokeMethod(e,t,r){if(this._thread){const s=this._transferLists[e],i=s?s(t):[];return this._thread.invoke(e,t,{transferList:i,signal:r})}return this._promise?this._promise.then((()=>((0,l.k_)(r),this.invokeMethod(e,t,r)))):Promise.reject(null)}broadcast(e,t){return this._thread?Promise.all(this._thread.broadcast(t,e)).then((()=>{})):this._promise?this._promise.then((()=>this.broadcast(e,t))):Promise.reject()}get promise(){return this._promise}_connectListener(e){this._thread&&this._thread.on(e.eventName,e.callback).then((t=>{e.removed||(e.threadHandle=t)}))}}class I extends E{constructor(e=null){super("LercWorker","_decode",{_decode:e=>[e.buffer]},e,{strategy:"dedicated"}),this.schedule=e,this.ref=0}decode(e,t,r){return e&&0!==e.byteLength?this.invoke({buffer:e,options:t},r):Promise.resolve(null)}release(){--this.ref<=0&&(M.forEach(((e,t)=>{e===this&&M.delete(t)})),this.destroy())}}const M=new Map;let C=class extends((0,y.Z)((0,f.Y)((0,_.q)((0,v.I)((0,a.R)(g.Z)))))){constructor(...e){super(...e),this.copyright=null,this.heightModelInfo=null,this.path=null,this.minScale=void 0,this.maxScale=void 0,this.opacity=1,this.operationalLayerType="ArcGISTiledElevationServiceLayer",this.sourceJSON=null,this.type="elevation",this.url=null,this.version=null,this._lercDecoder=function(e=null){let t=M.get((0,o.Wg)(e));return t||((0,o.pC)(e)?(t=new I((t=>e.immediate.schedule(t))),M.set(e,t)):(t=new I,M.set(null,t))),++t.ref,t}()}normalizeCtorArgs(e,t){return"string"==typeof e?{url:e,...t}:e}destroy(){this._lercDecoder=(0,o.RY)(this._lercDecoder)}readVersion(e,t){let r=t.currentVersion;return r||(r=9.3),r}load(e){const t=(0,o.pC)(e)?e.signal:null;return this.addResolvingPromise(this.loadFromPortal({supportedTypes:["Image Service"],supportsData:!1,validateItem:e=>{for(let t=0;t<e.typeKeywords.length;t++)if("elevation 3d layer"===e.typeKeywords[t].toLowerCase())return!0;throw new n.Z("portal:invalid-layer-item-type","Invalid layer item type '${type}', expected '${expectedType}' ",{type:"Image Service",expectedType:"Image Service Elevation 3D Layer"})}},e).catch(l.r9).then((()=>this._fetchImageService(t)))),Promise.resolve(this)}fetchTile(e,t,r,s){const n=(0,o.pC)((s=s||{signal:null}).signal)?s.signal:s.signal=(new AbortController).signal,a={responseType:"array-buffer",signal:n},l={noDataValue:s.noDataValue,returnFileInfo:!0};return this.load().then((()=>this._fetchTileAvailability(e,t,r,s))).then((()=>(0,i.default)(this.getTileUrl(e,t,r),a))).then((e=>this._lercDecoder.decode(e.data,l,n))).then((e=>new b(e)))}getTileUrl(e,t,r){const s=!this.tilemapCache&&this.supportsBlankTile,i=(0,h.B7)({...this.parsedUrl.query,blankTile:!s&&null});return`${this.parsedUrl.path}/tile/${e}/${t}/${r}${i?"?"+i:""}`}async queryElevation(e,t){const{ElevationQuery:s}=await r.e(5642).then(r.bind(r,55642));return(0,l.k_)(t),(new s).query(this,e,t)}async createElevationSampler(e,t){const{ElevationQuery:s}=await r.e(5642).then(r.bind(r,55642));return(0,l.k_)(t),(new s).createSampler(this,e,t)}_fetchTileAvailability(e,t,r,s){return this.tilemapCache?this.tilemapCache.fetchAvailability(e,t,r,s):Promise.resolve("unknown")}async _fetchImageService(e){if(this.sourceJSON)return this.sourceJSON;const t={query:{f:"json",...this.parsedUrl.query},responseType:"json",signal:e},r=await(0,i.default)(this.parsedUrl.path,t);r.ssl&&(this.url=this.url?.replace(/^http:/i,"https:")),this.sourceJSON=r.data,this.read(r.data,{origin:"service",url:this.parsedUrl})}get hasOverriddenFetchTile(){return!this.fetchTile.__isDefault__}};(0,s._)([(0,u.Cb)({json:{read:{source:"copyrightText"}}})],C.prototype,"copyright",void 0),(0,s._)([(0,u.Cb)({readOnly:!0,type:m.Z})],C.prototype,"heightModelInfo",void 0),(0,s._)([(0,u.Cb)({type:String,json:{origins:{"web-scene":{read:!0,write:!0}},read:!1}})],C.prototype,"path",void 0),(0,s._)([(0,u.Cb)({type:["show","hide"]})],C.prototype,"listMode",void 0),(0,s._)([(0,u.Cb)({json:{read:!1,write:!1,origins:{service:{read:!1,write:!1},"portal-item":{read:!1,write:!1},"web-document":{read:!1,write:!1}}},readOnly:!0})],C.prototype,"minScale",void 0),(0,s._)([(0,u.Cb)({json:{read:!1,write:!1,origins:{service:{read:!1,write:!1},"portal-item":{read:!1,write:!1},"web-document":{read:!1,write:!1}}},readOnly:!0})],C.prototype,"maxScale",void 0),(0,s._)([(0,u.Cb)({json:{read:!1,write:!1,origins:{"web-document":{read:!1,write:!1}}}})],C.prototype,"opacity",void 0),(0,s._)([(0,u.Cb)({type:["ArcGISTiledElevationServiceLayer"]})],C.prototype,"operationalLayerType",void 0),(0,s._)([(0,u.Cb)()],C.prototype,"sourceJSON",void 0),(0,s._)([(0,u.Cb)({json:{read:!1},value:"elevation",readOnly:!0})],C.prototype,"type",void 0),(0,s._)([(0,u.Cb)(w.HQ)],C.prototype,"url",void 0),(0,s._)([(0,u.Cb)()],C.prototype,"version",void 0),(0,s._)([(0,d.r)("version",["currentVersion"])],C.prototype,"readVersion",null),C=(0,s._)([(0,p.j)("esri.layers.ElevationLayer")],C),C.prototype.fetchTile.__isDefault__=!0;const T=C},46486:(e,t,r)=>{r.d(t,{Z:()=>u});var s=r(43697),i=(r(66577),r(5600)),n=(r(75215),r(67676),r(71715)),o=r(52011),a=r(45322),l=r(56608),h=r(82971);const u=e=>{let t=class extends e{constructor(){super(...arguments),this.copyright=null,this.minScale=0,this.maxScale=0,this.spatialReference=null,this.tileInfo=null,this.tilemapCache=null}readMinScale(e,t){return null!=t.minLOD&&null!=t.maxLOD?e:0}readMaxScale(e,t){return null!=t.minLOD&&null!=t.maxLOD?e:0}get supportsBlankTile(){return this.version>=10.2}readTilemapCache(e,t){return t.capabilities&&t.capabilities.includes("Tilemap")?new l.y({layer:this}):null}};return(0,s._)([(0,i.Cb)({json:{read:{source:"copyrightText"}}})],t.prototype,"copyright",void 0),(0,s._)([(0,i.Cb)()],t.prototype,"minScale",void 0),(0,s._)([(0,n.r)("service","minScale")],t.prototype,"readMinScale",null),(0,s._)([(0,i.Cb)()],t.prototype,"maxScale",void 0),(0,s._)([(0,n.r)("service","maxScale")],t.prototype,"readMaxScale",null),(0,s._)([(0,i.Cb)({type:h.Z})],t.prototype,"spatialReference",void 0),(0,s._)([(0,i.Cb)({readOnly:!0})],t.prototype,"supportsBlankTile",null),(0,s._)([(0,i.Cb)(a.h)],t.prototype,"tileInfo",void 0),(0,s._)([(0,i.Cb)()],t.prototype,"tilemapCache",void 0),(0,s._)([(0,n.r)("service","tilemapCache",["capabilities"])],t.prototype,"readTilemapCache",null),(0,s._)([(0,i.Cb)()],t.prototype,"version",void 0),t=(0,s._)([(0,o.j)("esri.layers.mixins.ArcGISCachedService")],t),t}},38009:(e,t,r)=>{r.d(t,{q:()=>p});var s=r(43697),i=r(20102),n=r(17452),o=r(5600),a=(r(75215),r(67676),r(52011)),l=r(30556),h=r(50549),u=r(76169);const c={"web-scene/operational-layers":{ArcGISDimensionLayer:!0,ArcGISFeatureLayer:!0,ArcGISImageServiceLayer:!0,ArcGISMapServiceLayer:!0,ArcGISSceneServiceLayer:!0,ArcGISTiledElevationServiceLayer:!0,ArcGISTiledImageServiceLayer:!0,ArcGISTiledMapServiceLayer:!0,BuildingSceneLayer:!0,GroupLayer:!0,IntegratedMeshLayer:!0,OGCFeatureLayer:!0,PointCloudLayer:!0,WebTiledLayer:!0,CSV:!0,GeoJSON:!0,VectorTileLayer:!0,WFS:!0,WMS:!0,KML:!0,RasterDataLayer:!0,Voxel:!0,LineOfSightLayer:!0},"web-scene/basemap":{ArcGISTiledImageServiceLayer:!0,ArcGISTiledMapServiceLayer:!0,WebTiledLayer:!0,OpenStreetMap:!0,VectorTileLayer:!0,ArcGISImageServiceLayer:!0,WMS:!0,ArcGISMapServiceLayer:!0,ArcGISSceneServiceLayer:!0},"web-scene/ground":{ArcGISTiledElevationServiceLayer:!0,RasterDataElevationLayer:!0},"web-map/operational-layers":{ArcGISAnnotationLayer:!0,ArcGISDimensionLayer:!0,ArcGISFeatureLayer:!0,ArcGISImageServiceLayer:!0,ArcGISImageServiceVectorLayer:!0,ArcGISMapServiceLayer:!0,ArcGISStreamLayer:!0,ArcGISTiledImageServiceLayer:!0,ArcGISTiledMapServiceLayer:!0,BingMapsAerial:!0,BingMapsHybrid:!0,BingMapsRoad:!0,CSV:!0,GeoRSS:!0,GeoJSON:!0,GroupLayer:!0,KML:!0,MediaLayer:!0,OGCFeatureLayer:!0,OrientedImageryLayer:!0,SubtypeGroupLayer:!0,VectorTileLayer:!0,WFS:!0,WMS:!0,WebTiledLayer:!0},"web-map/basemap":{ArcGISImageServiceLayer:!0,ArcGISImageServiceVectorLayer:!0,ArcGISMapServiceLayer:!0,ArcGISTiledImageServiceLayer:!0,ArcGISTiledMapServiceLayer:!0,OpenStreetMap:!0,VectorTileLayer:!0,WMS:!0,WebTiledLayer:!0,BingMapsAerial:!0,BingMapsRoad:!0,BingMapsHybrid:!0},"web-map/tables":{ArcGISFeatureLayer:!0},"portal-item/operational-layers":{ArcGISFeatureLayer:!0,ArcGISSceneServiceLayer:!0,PointCloudLayer:!0,BuildingSceneLayer:!0,IntegratedMeshLayer:!0,OrientedImageryLayer:!0}};var d=r(21506);const p=e=>{let t=class extends e{constructor(){super(...arguments),this.title=null}writeListMode(e,t,r,s){(s&&"ground"===s.layerContainerType||e&&(0,u.d)(this,r,{},s))&&(t[r]=e)}writeOperationalLayerType(e,t,r,s){!e||s&&"tables"===s.layerContainerType||(t.layerType=e)}writeTitle(e,t){t.title=e??"Layer"}read(e,t){t&&(t.layer=this),(0,h.$)(this,e,(t=>super.read(e,t)),t)}write(e,t){if(t?.origin){const e=`${t.origin}/${t.layerContainerType||"operational-layers"}`,r=c[e];let s=r&&r[this.operationalLayerType];if("ArcGISTiledElevationServiceLayer"===this.operationalLayerType&&"web-scene/operational-layers"===e&&(s=!1),"ArcGISDimensionLayer"===this.operationalLayerType&&"web-map/operational-layers"===e&&(s=!1),!s)return t.messages?.push(new i.Z("layer:unsupported",`Layers (${this.title}, ${this.id}) of type '${this.declaredClass}' are not supported in the context of '${e}'`,{layer:this})),null}const r=super.write(e,{...t,layer:this}),s=!!t&&!!t.messages&&!!t.messages.filter((e=>e instanceof i.Z&&"web-document-write:property-required"===e.name)).length;return(0,n.jc)(r?.url)?(t?.messages?.push(new i.Z("layer:invalid-url",`Layer (${this.title}, ${this.id}) of type '${this.declaredClass}' using a Blob URL cannot be written to web scenes and web maps`,{layer:this})),null):!this.url&&s?null:r}beforeSave(){}};return(0,s._)([(0,o.Cb)({type:String,json:{write:{ignoreOrigin:!0},origins:{"web-scene":{write:{isRequired:!0,ignoreOrigin:!0}},"portal-item":{write:!1}}}})],t.prototype,"id",void 0),(0,s._)([(0,o.Cb)(d.rT)],t.prototype,"listMode",void 0),(0,s._)([(0,l.c)("listMode")],t.prototype,"writeListMode",null),(0,s._)([(0,o.Cb)({type:String,readOnly:!0,json:{read:!1,write:{target:"layerType",ignoreOrigin:!0},origins:{"portal-item":{write:!1}}}})],t.prototype,"operationalLayerType",void 0),(0,s._)([(0,l.c)("operationalLayerType")],t.prototype,"writeOperationalLayerType",null),(0,s._)([(0,o.Cb)(d.Oh)],t.prototype,"opacity",void 0),(0,s._)([(0,o.Cb)({type:String,json:{write:{ignoreOrigin:!0,writerEnsuresNonNull:!0},origins:{"web-scene":{write:{isRequired:!0,ignoreOrigin:!0,writerEnsuresNonNull:!0}},"portal-item":{write:!1}}},value:"Layer"})],t.prototype,"title",void 0),(0,s._)([(0,l.c)("title"),(0,l.c)(["web-scene"],"title")],t.prototype,"writeTitle",null),(0,s._)([(0,o.Cb)({type:Boolean,json:{name:"visibility"}})],t.prototype,"visible",void 0),t=(0,s._)([(0,a.j)("esri.layers.mixins.OperationalLayer")],t),t}},16859:(e,t,r)=>{r.d(t,{I:()=>S});var s=r(43697),i=r(68773),n=r(40330),o=r(3172),a=r(66643),l=r(20102),h=r(92604),u=r(70586),c=r(95330),d=r(17452),p=r(5600),m=(r(75215),r(67676),r(71715)),g=r(52011),y=r(30556),f=r(84230),_=r(65587),v=r(15235),w=r(86082),b=r(14661);const S=e=>{let t=class extends e{constructor(){super(...arguments),this.resourceReferences={portalItem:null,paths:[]},this.userHasEditingPrivileges=!0,this.userHasFullEditingPrivileges=!1,this.userHasUpdateItemPrivileges=!1}destroy(){this.portalItem=(0,u.SC)(this.portalItem)}set portalItem(e){e!==this._get("portalItem")&&(this.removeOrigin("portal-item"),this._set("portalItem",e))}readPortalItem(e,t,r){if(t.itemId)return new v.default({id:t.itemId,portal:r&&r.portal})}writePortalItem(e,t){e&&e.id&&(t.itemId=e.id)}async loadFromPortal(e,t){if(this.portalItem&&this.portalItem.id)try{const s=await r.e(8062).then(r.bind(r,18062));return(0,c.k_)(t),await s.load({instance:this,supportedTypes:e.supportedTypes,validateItem:e.validateItem,supportsData:e.supportsData,layerModuleTypeMap:e.layerModuleTypeMap},t)}catch(e){throw(0,c.D_)(e)||h.Z.getLogger(this.declaredClass).warn(`Failed to load layer (${this.title}, ${this.id}) portal item (${this.portalItem.id})\n  ${e}`),e}}async finishLoadEditablePortalLayer(e){this._set("userHasEditingPrivileges",await this._fetchUserHasEditingPrivileges(e).catch((e=>((0,c.r9)(e),!0))))}async _setUserPrivileges(e,t){if(!i.Z.userPrivilegesApplied)return this.finishLoadEditablePortalLayer(t);if(this.url)try{const{features:{edit:r,fullEdit:s},content:{updateItem:i}}=await this._fetchUserPrivileges(e,t);this._set("userHasEditingPrivileges",r),this._set("userHasFullEditingPrivileges",s),this._set("userHasUpdateItemPrivileges",i)}catch(e){(0,c.r9)(e)}}async _fetchUserPrivileges(e,t){let r=this.portalItem;if(!e||!r||!r.loaded||r.sourceUrl)return this._fetchFallbackUserPrivileges(t);const s=e===r.id;if(s&&r.portal.user)return(0,b.Ss)(r);let i,o;if(s)i=r.portal.url;else try{i=await(0,f.oP)(this.url,t)}catch(e){(0,c.r9)(e)}if(!i||!(0,d.Zo)(i,r.portal.url))return this._fetchFallbackUserPrivileges(t);try{const e=(0,u.pC)(t)?t.signal:null;o=await(n.id?.getCredential(`${i}/sharing`,{prompt:!1,signal:e}))}catch(e){(0,c.r9)(e)}if(!o)return{features:{edit:!0,fullEdit:!1},content:{updateItem:!1}};try{if(s?await r.reload():(r=new v.default({id:e,portal:{url:i}}),await r.load(t)),r.portal.user)return(0,b.Ss)(r)}catch(e){(0,c.r9)(e)}return{features:{edit:!0,fullEdit:!1},content:{updateItem:!1}}}async _fetchFallbackUserPrivileges(e){let t=!0;try{t=await this._fetchUserHasEditingPrivileges(e)}catch(e){(0,c.r9)(e)}return{features:{edit:t,fullEdit:!1},content:{updateItem:!1}}}async _fetchUserHasEditingPrivileges(e){const t=this.url?n.id?.findCredential(this.url):null;if(!t)return!0;const r=k.credential===t?k.user:await this._fetchEditingUser(e);return k.credential=t,k.user=r,(0,u.Wi)(r)||null==r.privileges||r.privileges.includes("features:user:edit")}async _fetchEditingUser(e){const t=this.portalItem?.portal?.user;if(t)return t;const r=n.id.findServerInfo(this.url??"");if(!r?.owningSystemUrl)return null;const s=`${r.owningSystemUrl}/sharing/rest`,i=_.Z.getDefault();if(i&&i.loaded&&(0,d.Fv)(i.restUrl)===(0,d.Fv)(s))return i.user;const l=`${s}/community/self`,h=(0,u.pC)(e)?e.signal:null,c=await(0,a.q6)((0,o.default)(l,{authMode:"no-prompt",query:{f:"json"},signal:h}));return c.ok?w.default.fromJSON(c.value.data):null}read(e,t){t&&(t.layer=this),super.read(e,t)}write(e,t){const r=t&&t.portal,s=this.portalItem&&this.portalItem.id&&(this.portalItem.portal||_.Z.getDefault());return r&&s&&!(0,d.tm)(s.restUrl,r.restUrl)?(t.messages&&t.messages.push(new l.Z("layer:cross-portal",`The layer '${this.title} (${this.id})' cannot be persisted because it refers to an item on a different portal than the one being saved to. To save, set layer.portalItem to null or save to the same portal as the item associated with the layer`,{layer:this})),null):super.write(e,{...t,layer:this})}};return(0,s._)([(0,p.Cb)({type:v.default})],t.prototype,"portalItem",null),(0,s._)([(0,m.r)("web-document","portalItem",["itemId"])],t.prototype,"readPortalItem",null),(0,s._)([(0,y.c)("web-document","portalItem",{itemId:{type:String}})],t.prototype,"writePortalItem",null),(0,s._)([(0,p.Cb)({clonable:!1})],t.prototype,"resourceReferences",void 0),(0,s._)([(0,p.Cb)({type:Boolean,readOnly:!0})],t.prototype,"userHasEditingPrivileges",void 0),(0,s._)([(0,p.Cb)({type:Boolean,readOnly:!0})],t.prototype,"userHasFullEditingPrivileges",void 0),(0,s._)([(0,p.Cb)({type:Boolean,readOnly:!0})],t.prototype,"userHasUpdateItemPrivileges",void 0),t=(0,s._)([(0,g.j)("esri.layers.mixins.PortalLayer")],t),t},k={credential:null,user:null}},21506:(e,t,r)=>{r.d(t,{qG:()=>b,PV:()=>y,id:()=>k,iR:()=>p,rn:()=>g,rT:()=>I,u1:()=>E,rO:()=>L,Oh:()=>v,bT:()=>w,C_:()=>d,Lx:()=>f,vg:()=>S,YI:()=>c,HQ:()=>m});var s=r(92835),i=r(6570),n=r(82971),o=r(25929),a=r(70586),l=(r(95330),r(35463)),h=r(86787),u=r(65242);const c={type:Boolean,value:!0,json:{origins:{service:{read:!1,write:!1},"web-map":{read:!1,write:!1}},name:"screenSizePerspective",write:!0}},d={type:Boolean,value:!0,json:{name:"disablePopup",read:{reader:(e,t)=>!t.disablePopup},write:{enabled:!0,writer(e,t,r){t[r]=!e}}}},p={type:Boolean,value:!0,nonNullable:!0,json:{name:"showLabels",write:!0}},m={type:String,json:{origins:{"portal-item":{write:!1}},write:{isRequired:!0,ignoreOrigin:!0,writer:o.w}}},g={type:Boolean,value:!0,nonNullable:!0,json:{origins:{service:{read:{enabled:!1}}},name:"showLegend",write:!0}},y={value:null,type:h.Z,json:{origins:{service:{name:"elevationInfo",write:!0}},name:"layerDefinition.elevationInfo",write:!0}};function f(e){return{type:e,readOnly:!0,json:{origins:{service:{read:!0}},read:!1}}}const _={write:!0,read:!0},v={type:Number,json:{origins:{"web-document":_,"portal-item":{write:!0}}}},w={...v,json:{...v.json,origins:{"web-document":{..._,write:{enabled:!0,target:{opacity:{type:Number},"layerDefinition.drawingInfo.transparency":{type:Number}}}}},read:{source:["layerDefinition.drawingInfo.transparency","drawingInfo.transparency"],reader:(e,t,r)=>r&&"service"!==r.origin||!t.drawingInfo||void 0===t.drawingInfo.transparency?t.layerDefinition&&t.layerDefinition.drawingInfo&&void 0!==t.layerDefinition.drawingInfo.transparency?(0,u.b)(t.layerDefinition.drawingInfo.transparency):void 0:(0,u.b)(t.drawingInfo.transparency)}}},b={type:s.Z,readOnly:!0,get(){if(!this.layer?.timeInfo)return null;const{datesInUnknownTimezone:e,timeOffset:t,useViewTime:r}=this.layer,i=this.view?.timeExtent;let n=this.layer.timeExtent;e&&(n=function(e){if(!e)return e;const{start:t,end:r}=e;return new s.Z({start:(0,a.pC)(t)?(0,l.Nm)(t,t.getTimezoneOffset(),"minutes"):t,end:(0,a.pC)(r)?(0,l.Nm)(r,r.getTimezoneOffset(),"minutes"):r})}(n));let o=r?i&&n?i.intersection(n):i||n:n;if(!o||o.isEmpty||o.isAllTime)return o;t&&(o=o.offset(-t.value,t.unit)),e&&(o=function(e){if(!e)return e;const{start:t,end:r}=e;return new s.Z({start:(0,a.pC)(t)?(0,l.Nm)(t,-t.getTimezoneOffset(),"minutes"):t,end:(0,a.pC)(r)?(0,l.Nm)(r,-r.getTimezoneOffset(),"minutes"):r})}(o));const h=this._get("timeExtent");return o.equals(h)?h:o}},S={type:i.Z,readOnly:!0,json:{origins:{service:{read:{source:["fullExtent","spatialReference"],reader:(e,t)=>{const r=i.Z.fromJSON(e);return null!=t.spatialReference&&"object"==typeof t.spatialReference&&(r.spatialReference=n.Z.fromJSON(t.spatialReference)),r}}}},read:!1}},k={type:String,json:{origins:{service:{read:!1},"portal-item":{read:!1}}}},L={type:Number,json:{origins:{service:{write:{enabled:!1}}},read:{source:"layerDefinition.minScale"},write:{target:"layerDefinition.minScale"}}},E={type:Number,json:{origins:{service:{write:{enabled:!1}}},read:{source:"layerDefinition.maxScale"},write:{target:"layerDefinition.maxScale"}}},I={json:{write:{ignoreOrigin:!0},origins:{"web-map":{read:!1,write:!1}}}}},99282:(e,t,r)=>{r.d(t,{a:()=>n});var s=r(67900),i=r(68441);const n={inches:(0,s.En)(1,"meters","inches"),feet:(0,s.En)(1,"meters","feet"),"us-feet":(0,s.En)(1,"meters","us-feet"),yards:(0,s.En)(1,"meters","yards"),miles:(0,s.En)(1,"meters","miles"),"nautical-miles":(0,s.En)(1,"meters","nautical-miles"),millimeters:(0,s.En)(1,"meters","millimeters"),centimeters:(0,s.En)(1,"meters","centimeters"),decimeters:(0,s.En)(1,"meters","decimeters"),meters:(0,s.En)(1,"meters","meters"),kilometers:(0,s.En)(1,"meters","kilometers"),"decimal-degrees":1/(0,s.ty)(1,"meters",i.sv.radius)}},86787:(e,t,r)=>{r.d(t,{Z:()=>w});var s,i=r(43697),n=r(35454),o=r(96674),a=r(70586),l=r(5600),h=(r(75215),r(67676),r(71715)),u=r(52011),c=r(30556),d=r(35671);let p=s=class extends o.wq{constructor(e){super(e)}async collectRequiredFields(e,t){return(0,d.io)(e,t,this.expression)}clone(){return new s({expression:this.expression,title:this.title})}equals(e){return this.expression===e.expression&&this.title===e.title}};(0,i._)([(0,l.Cb)({type:String,json:{write:!0}})],p.prototype,"expression",void 0),(0,i._)([(0,l.Cb)({type:String,json:{write:!0}})],p.prototype,"title",void 0),p=s=(0,i._)([(0,u.j)("esri.layers.support.FeatureExpressionInfo")],p);const m=p;var g,y=r(12541);const f=(0,n.w)()({onTheGround:"on-the-ground",relativeToGround:"relative-to-ground",relativeToScene:"relative-to-scene",absoluteHeight:"absolute-height"}),_=new n.X({foot:"feet",kilometer:"kilometers",meter:"meters",mile:"miles","us-foot":"us-feet",yard:"yards"});let v=g=class extends o.wq{constructor(e){super(e),this.offset=null}readFeatureExpressionInfo(e,t){return null!=e?e:t.featureExpression&&0===t.featureExpression.value?{expression:"0"}:void 0}writeFeatureExpressionInfo(e,t,r,s){t[r]=e.write({},s),"0"===e.expression&&(t.featureExpression={value:0})}get mode(){const{offset:e,featureExpressionInfo:t}=this;return this._isOverridden("mode")?this._get("mode"):(0,a.pC)(e)||t?"relative-to-ground":"on-the-ground"}set mode(e){this._override("mode",e)}set unit(e){this._set("unit",e)}write(e,t){return this.offset||this.mode||this.featureExpressionInfo||this.unit?super.write(e,t):null}clone(){return new g({mode:this.mode,offset:this.offset,featureExpressionInfo:this.featureExpressionInfo?this.featureExpressionInfo.clone():void 0,unit:this.unit})}equals(e){return this.mode===e.mode&&this.offset===e.offset&&this.unit===e.unit&&(0,a._W)(this.featureExpressionInfo,e.featureExpressionInfo)}};(0,i._)([(0,l.Cb)({type:m,json:{write:!0}})],v.prototype,"featureExpressionInfo",void 0),(0,i._)([(0,h.r)("featureExpressionInfo",["featureExpressionInfo","featureExpression"])],v.prototype,"readFeatureExpressionInfo",null),(0,i._)([(0,c.c)("featureExpressionInfo",{featureExpressionInfo:{type:m},"featureExpression.value":{type:[0]}})],v.prototype,"writeFeatureExpressionInfo",null),(0,i._)([(0,l.Cb)({type:f.apiValues,nonNullable:!0,json:{type:f.jsonValues,read:f.read,write:{writer:f.write,isRequired:!0}}})],v.prototype,"mode",null),(0,i._)([(0,l.Cb)({type:Number,json:{write:!0}})],v.prototype,"offset",void 0),(0,i._)([(0,l.Cb)({type:y.f9,json:{type:String,read:_.read,write:_.write}})],v.prototype,"unit",null),v=g=(0,i._)([(0,u.j)("esri.layers.support.ElevationInfo")],v);const w=v},12541:(e,t,r)=>{r.d(t,{Z7:()=>i,f9:()=>n});var s=r(99282);function i(e){return 1/(s.a[e]||1)}const n=function(){const e=Object.keys(s.a);return e.sort(),e}()},65242:(e,t,r)=>{r.d(t,{a:()=>i,b:()=>n});var s=r(75215);function i(e){const t=(0,s.vU)(100*(1-e));return Math.max(0,Math.min(t,100))}function n(e){const t=1-e/100;return Math.max(0,Math.min(t,1))}}}]);