package org.thingsboard.server.dao.smartPipe;

import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.DmaCost;

import java.util.List;
import java.util.Map;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-04-25
 */
public interface DmaCostService {

    DmaCost save(DmaCost dmaCost);

    PageData<DmaCost> getList(Map<String, Object> params);

    void delete(List<String> idList);
}
