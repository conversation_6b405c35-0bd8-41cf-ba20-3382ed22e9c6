package org.thingsboard.server.dao.repair;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.User;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.DTO.RepairStandardDetailDTO;
import org.thingsboard.server.dao.model.sql.RepairStandardEntity;
import org.thingsboard.server.dao.sql.repair.RepairStandardRepository;

import java.util.List;

@Slf4j
@Service
public class RepairStandardServiceImpl implements RepairStandardService {

    @Autowired
    private RepairStandardRepository repairStandardRepository;

    @Override
    public RepairStandardEntity findById(String id) {
        RepairStandardEntity entity = repairStandardRepository.findOne(id);
        String detail = entity.getDetail();
        entity.setDetailList(JSON.parseArray(detail, RepairStandardDetailDTO.class));
        return entity;
    }

    @Override
    public PageData<RepairStandardEntity> findList(int page, int size, String name, String deviceType, User currentUser) {
        // 分页参数
        PageRequest pageable = new PageRequest(page - 1, size, Sort.Direction.DESC, "createTime");

        Page<RepairStandardEntity> pageResult = repairStandardRepository.findList(name, UUIDConverter.fromTimeUUID(currentUser.getTenantId().getId()), deviceType, pageable);

        return new PageData<>(pageResult.getTotalElements(), pageResult.getContent());
    }

    @Override
    public RepairStandardEntity save(RepairStandardEntity entity) {
        List<RepairStandardDetailDTO> detailList = entity.getDetailList();
        entity.setDetail(JSON.toJSONString(detailList));
        return repairStandardRepository.save(entity);
    }

    @Override
    public void remove(List<String> ids) {
        for (String id : ids) {
            repairStandardRepository.delete(id);
        }
    }

    @Override
    public List<RepairStandardEntity> findAll(String name, TenantId tenantId) {
        return repairStandardRepository.findAll(name, UUIDConverter.fromTimeUUID(tenantId.getId()));
    }

    @Override
    public List<RepairStandardEntity> findAll(String name, String tenantId) {
        return repairStandardRepository.findAll(name, tenantId);
    }
}
