import{_ as P}from"./ArcLayout-CHnHL9Pv.js";import{_ as D}from"./index-BJ-QPYom.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import{d as w,r as f,g as e,h as n,F as g,q as h,i as p,p as c,C as y}from"./index-r0dFAfgr.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./project-DUuzYgGl.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import{u as x}from"./usePartition-DkcY9fQ2.js";import{D as k}from"./DrawerBox-CLde5xC8.js";import L from"./BasicInfo-9yuTJLR6.js";import T from"./LineBars-OSTGllv0.js";import B from"./PieCharts-C3euD8Ch.js";import C from"./TableChart-Bf3MZsvn.js";import"./index-0NlGN6gS.js";import{i as j}from"./statistics-CeyexT_5.js";import"./ArcView-DpMnCY82.js";import"./ViewHelper-BGCZjxXH.js";import"./fieldconfig-Bk3o1wi7.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./LayerHelper-Cn-iiqxI.js";import"./pipe-nogVzCHG.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./ArcStationWarning-BF9YrSzF.js";/* empty css                         */import"./useWaterPoint-Bv0z6ym6.js";import"./DateFormatter-Bm9a68Ax.js";import"./zhandian-YaGuQZe6.js";import"./useStation-DJgnSZIA.js";import"./arcWidgetButton-0glIxrt7.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./StatisticsHelper-D-s_6AyQ.js";import"./useLayerList-DmEwJ-ws.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./URLHelper-B9aplt5w.js";import"./ArcPipe.vue_vue_type_script_setup_true_lang-ClsVvV2P.js";import"./dma-SMxrzG7b.js";import"./hookupDevice-Bcbk7s68.js";import"./SideDrawer-CBntChyn.js";import"./useDetector-BRcb7GRN.js";const b={class:"partition-monitoring"},N={class:"top"},I={class:"left"},S={class:"bottom"},A=w({__name:"index",setup(F){const a=x(),o=f({title:"选择分区",data:[],isFilterTree:!0,treeNodeHandleClick:i=>{console.log(i),o.currentProject=i,u();const t=a.List.value.find(m=>m.id===i.id);t.geom&&a.extentToPartition(d.view,JSON.parse(t.geom))}}),r=f({monitoringData:{},curPartition:{}}),d={},u=()=>{var t;if(!o.currentProject)return;const i=a.List.value.find(m=>m.id===o.currentProject.value);r.curPartition=i,j({partitionId:(t=o.currentProject)==null?void 0:t.value}).then(m=>{var s;r.monitoringData=((s=m.data)==null?void 0:s.data)||{}}).catch(()=>{})},v=async i=>{var t;d.view=i,await a.getTree(),o.data=a.Tree.value,o.currentProject=(t=o.data)==null?void 0:t[0],await a.refreshPartitions(d.view),u()};return(i,t)=>{const m=D,s=P;return e(),n(k,{"right-drawer":!0,"right-drawer-bar-position":"top"},{right:g(()=>[h(m,{"tree-data":p(o)},null,8,["tree-data"])]),default:g(()=>{var l,_;return[c("div",b,[c("div",N,[c("div",I,[((l=p(r).curPartition)==null?void 0:l.type)==="2"?(e(),n(L,{key:0,"data-dma":p(r).monitoringData},null,8,["data-dma"])):(e(),n(B,{key:1,data:p(r).monitoringData},null,8,["data"]))]),h(s,{ref:"refArcLayout",onMapLoaded:v},null,512)]),c("div",S,[((_=p(r).curPartition)==null?void 0:_.type)==="1"?(e(),n(C,{key:0,data:p(r).monitoringData},null,8,["data"])):(e(),n(T,{key:1,"data-dma":p(r).monitoringData},null,8,["data-dma"]))])])]}),_:1})}}}),jo=y(A,[["__scopeId","data-v-e01b7731"]]);export{jo as default};
