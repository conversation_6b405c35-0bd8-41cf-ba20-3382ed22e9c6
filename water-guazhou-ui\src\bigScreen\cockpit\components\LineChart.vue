<template>
  <div class="line-chart-container" ref="chartContainer"></div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, computed } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  // 图表数据
  data: {
    type: Array,
    default: () => [30, 60, 45, 43, 65, 45, 50, 65, 42, 67, 44, 75, 65, 55, 50, 38, 90, 55, 35, 58, 35]
  },
  // 图表X轴数据
  xAxisData: {
    type: Array,
    default: () => Array.from({ length: 25 }, (_, i) => i)
  },
  // 主题色
  themeColor: {
    type: String,
    default: '#47EBEB'
  },
  // 图表高度
  height: {
    type: String,
    default: '100%'
  },
  // 图表宽度
  width: {
    type: String,
    default: '100%'
  },
  // X轴单位
  xAxisUnit: {
    type: String,
    default: ''
  },
  // Y轴单位
  yAxisUnit: {
    type: String,
    default: ''
  },
  // 是否显示网格线
  showGrid: {
    type: Boolean,
    default: false
  },
  // 是否显示区域填充
  showAreaStyle: {
    type: Boolean,
    default: true
  },
  // 区域填充透明度
  areaOpacity: {
    type: Number,
    default: 0.6
  },
  // 最大值
  maxValue: {
    type: Number,
    default: 100
  },
  // 是否平滑曲线
  smooth: {
    type: Boolean,
    default: false
  },
  // 线宽
  lineWidth: {
    type: Number,
    default: 1
  },
  // 是否显示数据点
  showSymbol: {
    type: Boolean,
    default: false
  }
})

const chartContainer = ref(null)
let chartInstance = null

// 计算渐变色 - 上深下浅，自动根据主题色生成
const gradientColor = computed(() => {
  // 从主题色提取RGB值，以便创建半透明版本
  const themeColorRgb = hexToRgb(props.themeColor);
  const r = themeColorRgb?.r || 71;
  const g = themeColorRgb?.g || 235;
  const b = themeColorRgb?.b || 235;
  
  // 创建渐变色
  return {
    type: 'linear',
    x: 0,
    y: 0,
    x2: 0,
    y2: 1,
    colorStops: [
      {
        offset: 0,
        color: `rgba(${r}, ${g}, ${b}, 0.3)` // 起始颜色（最上方，最深）
      },
      {
        offset: 0.4,
        color: `rgba(${r}, ${g}, ${b}, 0.2)` // 起始颜色（最上方，最深）
      },
      {
        offset: 1,
        color: `rgba(${r}, ${g}, ${b}, 0)` // 中间颜色（半透明）
      },
    //   {
    //     offset: 1,
    //     color: `rgba(${Math.max(r*0.3, 17)}, ${Math.max(g*0.3, 66)}, ${Math.max(b*0.3, 94)}, 0)` // 结束颜色（最下方，最浅）
    //   }
    ]
  }
})

// 十六进制颜色转RGB
function hexToRgb(hex) {
  if (!hex || typeof hex !== 'string') return null;
  
  // 扩展简写形式（例如："#03F"）
  const shorthandRegex = /^#?([a-f\d])([a-f\d])([a-f\d])$/i;
  hex = hex.replace(shorthandRegex, (m, r, g, b) => r + r + g + g + b + b);
  
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : null;
}

// 初始化图表
const initChart = () => {
  if (!chartContainer.value) return
  
  // 销毁之前的实例
  if (chartInstance) {
    chartInstance.dispose()
  }

  // 创建echarts实例
  chartInstance = echarts.init(chartContainer.value)
  
  // 更新图表
  updateChart()
  
  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)
}

// 更新图表
const updateChart = () => {
  if (!chartInstance) return
  
  const option = {
    grid: {
      top: '10%',
      left: '0%',
      right: '0%',
      bottom: '0%',
      containLabel: true
    },
    tooltip: {
      show: true,
      trigger: 'axis',
      backgroundColor: 'rgba(0, 19, 40, 0.8)',
      borderColor: 'rgba(26, 198, 255, 0.2)',
      borderWidth: 1,
      padding: [10, 15],
      textStyle: {
        color: '#fff',
        fontSize: 14
      },
      axisPointer: {
        type: 'line',
        snap: true, // 自动捕捉到最近的数据点
        lineStyle: {
          color: props.themeColor,
          width: 1,
          type: 'dashed'
        },
        handle: {
          show: false
        },
        label: {
          backgroundColor: props.themeColor
        }
      },
      formatter: params => {
        const dataPoint = params[0];
        if (!dataPoint) return '';
        
        let xValue = dataPoint.axisValue;
        if (props.xAxisUnit) {
          xValue = `${xValue}${props.xAxisUnit}`;
        }
        
        let yValue = dataPoint.value;
        if (props.yAxisUnit) {
          yValue = `${yValue}${props.yAxisUnit}`;
        }
        
        return `
          <div style="margin-bottom:6px;">
            <span style="font-size:13px;color:#ADE6EB;">${xValue}</span>
          </div>
          <div style="display:flex;align-items:center;justify-content:space-between;width:100px;">
            <div style="display:flex;align-items:center;">
              <span style="display:inline-block;width:8px;height:8px;border-radius:50%;background-color:${props.themeColor};margin-right:8px;"></span>
              <span style="color:#ffffff;">数值：</span>
            </div>
            <span style="color:#ffffff;font-weight:bold;">${yValue}</span>
          </div>
        `;
      }
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: props.xAxisData,
      axisLine: {
        show: true,
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.2)'
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: 'rgba(255, 255, 255, 0.5)',
        fontSize: 12,
        formatter: value => {
          return props.xAxisUnit ? `${value}${props.xAxisUnit}` : value;
        }
      },
      splitLine: {
        show: props.showGrid,
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)',
          type: 'dashed'
        }
      },
      name: props.xAxisUnit,
      nameTextStyle: {
        color: 'rgba(255, 255, 255, 0.5)',
        fontSize: 12,
        padding: [0, 0, 0, 10]
      }
    },
    yAxis: {
      type: 'value',
      min: 0,
      max: props.maxValue,
      interval: 20,
      axisLine: {
        show: true,
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.2)'
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: 'rgba(255, 255, 255, 0.5)',
        fontSize: 12,
        formatter: value => {
          return props.yAxisUnit ? `${value}${props.yAxisUnit}` : value;
        }
      },
      splitLine: {
        show: props.showGrid,
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)',
          type: 'dashed'
        }
      },
      name: props.yAxisUnit,
      nameTextStyle: {
        color: 'rgba(255, 255, 255, 0.5)',
        fontSize: 12,
        padding: [0, 10, 0, 0]
      }
    },
    series: [
      {
        type: 'line',
        data: props.data,
        smooth: props.smooth,
        // 完全不显示数据点
        showSymbol: false,
        // 鼠标悬停效果
        emphasis: {
          scale: false,
          focus: 'series',
          itemStyle: {
            opacity: 0 // 确保悬停时也不显示数据点
          }
        },
        // 确保悬停时显示标记点
        triggerLineEvent: true,
        triggerItemEvent: true,
        symbolSize: 0, // 设置为0，不显示数据点
        lineStyle: {
          width: props.lineWidth,
          color: props.themeColor,
          shadowColor: props.themeColor,
          shadowBlur: 10
        },
        itemStyle: {
          opacity: 0, // 完全不显示数据点
          color: props.themeColor
        },
        // 仍然启用tooltip
        tooltip: {
          show: true
        },
        areaStyle: props.showAreaStyle ? {
          color: gradientColor.value,
          opacity: props.areaOpacity,
          origin: 'start' // 确保渐变从数据线开始
        } : null
      }
    ]
  }
  
  chartInstance.setOption(option)
}

// 处理窗口大小变化
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

// 监听数据变化
watch(() => props.data, () => {
  updateChart()
}, { deep: true })

// 监听颜色变化
watch(() => props.themeColor, () => {
  updateChart()
})

// 监听配置变化
watch(() => [props.showGrid, props.showAreaStyle, props.areaOpacity, props.smooth, props.lineWidth, props.maxValue, props.xAxisUnit, props.yAxisUnit, props.showSymbol], () => {
  updateChart()
}, { deep: true })

// 组件挂载后初始化图表
onMounted(() => {
  initChart()
})

// 组件卸载前销毁图表
onUnmounted(() => {
  if (chartInstance) {
    window.removeEventListener('resize', handleResize)
    chartInstance.dispose()
    chartInstance = null
  }
})
</script>

<style lang="scss" scoped>
.line-chart-container {
  width: v-bind('width');
  height: v-bind('height');
  background-color: transparent;
}
</style> 