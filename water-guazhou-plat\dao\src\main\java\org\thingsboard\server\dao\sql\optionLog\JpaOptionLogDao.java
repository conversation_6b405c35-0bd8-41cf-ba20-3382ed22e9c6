/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.sql.optionLog;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Component;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.id.UserId;
import org.thingsboard.server.common.data.optionLog.OptionLog;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.DaoUtil;
import org.thingsboard.server.dao.model.sql.OptionLogEntity;
import org.thingsboard.server.dao.optionLog.OptionLogDao;
import org.thingsboard.server.dao.sql.JpaAbstractDao;
import org.thingsboard.server.dao.util.SqlDao;

import java.util.*;

@Component
@SqlDao
@Slf4j
public class JpaOptionLogDao extends JpaAbstractDao<OptionLogEntity, OptionLog> implements OptionLogDao {

    @Override
    protected void setSearchText(OptionLogEntity entity) {
        super.setSearchText(entity);
    }

    @Override
    public OptionLog save(OptionLog domain) {
        return super.save(domain);
    }

    @Override
    public OptionLog findById(UUID key) {
        return super.findById(key);
    }

//    @Override
//    public ListenableFuture<OptionLog> findByIdAsync(UUID key) {
//        return super.findByIdAsync(key);
//    }

    @Override
    public boolean removeById(UUID id) {
        return super.removeById(id);
    }

    @Override
    public List<OptionLog> findByTenantIdAndOptions(TenantId tenantId, String options, long sTime, long eTime) {
        return DaoUtil.convertDataList(
                optionLogRepository
                        .findByTenantIdAndOptions(
                                UUIDConverter.fromTimeUUID(tenantId.getId()),
                                options,
                                sTime,
                                eTime));
    }

    @Override
    public List<OptionLog> findByAuthorityAndOptionsAndTenantId(String authority, String options, TenantId tenantId, Long startTime, Long endTime) {
        return DaoUtil.convertDataList(
                optionLogRepository
                        .findByAuthorityAndOptionsAndTenantId(
                                authority,
                                options,
                                UUIDConverter.fromTimeUUID(tenantId.getId()),
                                startTime,
                                endTime));
    }

    @Override
    public Map<String, Object> findOptionLogByAuthorityAndTenantId(String authority, String tenantId, Long startTime, Long endTime, PageRequest pageRequest) {
        List<String> authorityList = Arrays.asList(authority.split(","));
        Page<OptionLogEntity> pageResult = optionLogRepository
                .findByAuthorityInAndTenantIdAndOptionsNotAndCreateTimeBetween(authorityList, tenantId, DataConstants.OPERATING_TYPE.LOGIN.name(), startTime, endTime, pageRequest);
        List<OptionLog> dataList = DaoUtil.convertDataList(pageResult.getContent());
        Map<String, Object> result = new HashMap<>();
        result.put("total", pageResult.getTotalElements());
        result.put("data", dataList);

        return result;
    }

    @Override
    public List<OptionLog> findByUserId(UserId id, PageRequest request) {
        return DaoUtil.convertDataList(optionLogRepository.findByUserId(UUIDConverter.fromTimeUUID(id.getId()), request));
    }

    @Override
    public List<OptionLog> findByAuthorityAndOptionsAndTenantIdAndIp(String authorityParam, String options, TenantId tenantId, Long startTime, Long endTime, String ip) {
        return DaoUtil.convertDataList(
                optionLogRepository
                        .findByAuthorityAndOptionsAndTenantIdAndIp(
                                authorityParam,
                                options,
                                UUIDConverter.fromTimeUUID(tenantId.getId()),
                                startTime,
                                endTime,
                                ip));
    }

    @Override
    public List<OptionLog> findByTenantIdAndOptionsAndIp(TenantId tenantId, String options, Long startTime, Long endTime, String ip) {
        return DaoUtil.convertDataList(
                optionLogRepository
                        .findByTenantIdAndOptionsAndIp(
                                UUIDConverter.fromTimeUUID(tenantId.getId()),
                                options,
                                startTime,
                                endTime,
                                ip));
    }

    @Override
    public Map<String, Object> findOptionLogByAuthorityAndTenantIdAndIpAndFirstNameLike(String authorityParam, String tenantId, Long startTime, Long endTime, String ip, String keyword, PageRequest pageRequest) {
        List<String> authorityList = Arrays.asList(authorityParam.split(","));
        Page<OptionLogEntity> pageResult = optionLogRepository
                .findByAuthorityInAndTenantIdAndOptionsNotAndCreateTimeBetweenAndFirstNameLikeAndAdditionalInfoLike(authorityList, tenantId, DataConstants.OPERATING_TYPE.LOGIN.name(), startTime, endTime, keyword, ip, pageRequest);
        List<OptionLog> dataList = DaoUtil.convertDataList(pageResult.getContent());
        Map<String, Object> result = new HashMap<>();
        result.put("total", pageResult.getTotalElements());
        result.put("data", dataList);

        return result;
    }

    @Override
    public int examine(String id, String examineId, String examineName, String examineTenantId, Long examineTime) {
        int row = optionLogRepository.examine(id, examineId, examineName, examineTenantId, examineTime);

        return row;
    }

    @Override
    public List<OptionLog> findByAuthorityAndOptionsAndTenantIdAndIpAndFirstNameLike(String authorityParam, String options, TenantId tenantId, Long startTime, Long endTime, String ip, String keyword) {
        return DaoUtil.convertDataList(
                optionLogRepository
                        .findByAuthorityAndOptionsAndTenantIdAndCreateTimeBetweenAndAdditionalInfoLikeAndFirstNameLike(
                                authorityParam,
                                options,
                                UUIDConverter.fromTimeUUID(tenantId.getId()),
                                startTime,
                                endTime,
                                ip,
                                keyword));
    }

    @Override
    public List<OptionLog> findByTenantIdAndOptionsAndIpAndFirstNameLike(TenantId tenantId, String options, Long startTime, Long endTime, String ip, String keyword) {
        return DaoUtil.convertDataList(
                optionLogRepository
                        .findByTenantIdAndOptionsAndCreateTimeBetweenAndAdditionalInfoLikeAndFirstNameLike(
                                UUIDConverter.fromTimeUUID(tenantId.getId()),
                                options,
                                startTime,
                                endTime,
                                ip,
                                keyword));
    }

    @Override
    public PageData<OptionLog> findByAuthorityAndOptionsAndTenantIdAndIpAndFirstNameLike(String authorityParam, String options, TenantId tenantId, Long startTime, Long endTime, String ip, String keyword, Pageable pageable) {
        Page<OptionLogEntity> pageResult = optionLogRepository
                .findByAuthorityAndOptionsAndTenantIdAndCreateTimeBetweenAndAdditionalInfoLikeAndFirstNameLike(
                        authorityParam,
                        options,
                        UUIDConverter.fromTimeUUID(tenantId.getId()),
                        startTime,
                        endTime,
                        ip,
                        keyword,
                        pageable);

        return new PageData<>(pageResult.getTotalElements(), DaoUtil.convertDataList(pageResult.getContent()));
    }

    @Override
    public PageData<OptionLog> findByTenantIdAndOptionsAndIpAndFirstNameLike(TenantId tenantId, String options, Long startTime, Long endTime, String ip, String keyword, Pageable pageable) {
        Page<OptionLogEntity> pageResult =
                optionLogRepository
                        .findByTenantIdAndOptionsAndCreateTimeBetweenAndAdditionalInfoLikeAndFirstNameLike(
                                UUIDConverter.fromTimeUUID(tenantId.getId()),
                                options,
                                startTime,
                                endTime,
                                ip,
                                keyword,
                                pageable);
        return new PageData<>(pageResult.getTotalElements(), DaoUtil.convertDataList(pageResult.getContent()));
    }

    @Override
    public List<OptionLog> find() {
        return super.find();
    }

    @Autowired
    private OptionLogRepository optionLogRepository;

    @Override
    protected Class<OptionLogEntity> getEntityClass() {
        return OptionLogEntity.class;
    }

    @Override
    protected CrudRepository<OptionLogEntity, String> getCrudRepository() {
        return optionLogRepository;
    }
}
