package org.thingsboard.server.dao.model.sql.plan;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.store.DeviceInfoResponse;
import org.thingsboard.server.dao.sql.department.GoodsShelfMapper;
import org.thingsboard.server.dao.util.imodel.response.annotations.Flatten;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseTenantName;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseViaMapper;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

@Getter
@Setter
@ResponseEntity
public class PlanTaskDetail {
    // id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    // 主表ID
    private String mainId;

    // 设备编码
    private String serialId;

    // 货架余量
    private Integer count;

    // 货架ID
    @ParseViaMapper(GoodsShelfMapper.class)
    private String shelvesId;

    // 盘点数量（正常）
    private Double normalNum;

    // 盘点数量（异常）
    private Double exceptionNum;

    // 盘点说明
    private String remark;

    // 租户ID
    @ParseTenantName
    private String tenantId;

    @Flatten
    @TableField(exist = false)
    private DeviceInfoResponse deviceInfoResponse;

    public PlanTaskDetail() {
    }

    public PlanTaskDetail(String mainId, String serialId, String shelvesId, String tenantId) {
        this.mainId = mainId;
        this.serialId = serialId;
        this.shelvesId = shelvesId;
        this.tenantId = tenantId;
    }

}
