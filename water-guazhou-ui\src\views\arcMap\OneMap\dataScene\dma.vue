<!-- gisdma分区 -->
<template>
  <div class="onemap-panel-wrapper">
    <Cards
      v-model="cardsvalue"
      :span="12"
    ></Cards>

    <Form
      ref="refForm"
      :config="FormConfig"
    >
    </Form>
    <div class="table-box">
      <FormTable :config="TableConfig"></FormTable>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { Cards } from '../../components'
import { ring } from '../../components/components/chart'

const emit = defineEmits(['highlightMark', 'addMarks'])
const props = defineProps<{
  view?: __esri.MapView
  menu: IMenuItem
}>()

const refForm = ref<IFormIns>()

const cardsvalue = ref([
  { label: '0 个', value: '分区总数' },
  { label: '0 %', value: '当前产销差' }
])

const TableConfig = reactive<ITable>({
  indexVisible: true,
  dataList: [],
  pagination: {
    hide: true
  },
  columns: [
    {
      minWidth: 100,
      label: '分区名称',
      prop: 'key1'
    },
    {
      minWidth: 100,
      label: '参考产销差',
      prop: 'key2'
    },
    {
      minWidth: 100,
      label: '类型',
      prop: 'key3'
    }
  ],
  handleRowClick: row => {
    emit('highlightMark', props.menu, row?.id)
  }
})
const FormConfig = reactive<IFormConfig>({
  group: [
    {
      fieldset: {
        type: 'underline',
        desc: '漏损分布'
      },
      fields: [
        {
          type: 'vchart',
          option: ring(),
          style: {
            width: '100%',
            height: '150px'
          }
        }
      ]
    },
    {
      fields: [
        {
          type: 'input',
          field: 'layer',
          append: '刷新'
        }
      ]
    }
  ],
  labelPosition: 'top',
  gutter: 12
})
const refreshData = () => {
  //
}
onMounted(() => {
  refreshData()
})
</script>

<style lang="scss" scoped>
.onemap-panel-wrapper {
  min-height: 610px;
  height: 100%;
}
.table-box {
  height: calc(100% - 335px);
}
</style>
