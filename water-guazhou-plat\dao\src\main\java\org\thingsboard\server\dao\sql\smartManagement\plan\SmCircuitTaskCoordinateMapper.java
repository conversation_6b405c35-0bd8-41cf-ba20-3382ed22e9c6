package org.thingsboard.server.dao.sql.smartManagement.plan;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.thingsboard.server.dao.model.sql.smartManagement.plan.SmCircuitTaskCoordinate;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.plan.SmCircuitTaskCoordinatePageRequest;

@Mapper
public interface SmCircuitTaskCoordinateMapper extends BaseMapper<SmCircuitTaskCoordinate> {
    IPage<SmCircuitTaskCoordinate> findByPage(SmCircuitTaskCoordinatePageRequest request);

    @SuppressWarnings("methodNotInXmlInspection")
    boolean update(SmCircuitTaskCoordinate entity);

    boolean updateFully(SmCircuitTaskCoordinate entity);

}
