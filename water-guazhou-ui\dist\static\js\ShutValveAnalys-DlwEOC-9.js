import{d as le,c as M,r as k,b as S,X as me,W as B,Q as pe,g as ue,n as ce,q as U,i as F,_ as ye,C as de}from"./index-r0dFAfgr.js";import{w as J}from"./MapView-DaoQedLH.js";import{s as q,d as ge,h as fe}from"./FeatureHelper-Da16o0mu.js";import{d as ve,a as be}from"./GPHelper-fLrvVD-A.js";import{i as we,e as he}from"./IdentifyHelper-RJWmLn49.js";import{g as Q,a as z,e as W}from"./LayerHelper-Cn-iiqxI.js";import{e as xe,i as H,f as Se,h as Ie,a as Oe}from"./QueryHelper-ILO3qZqg.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./Point-WxyopZva.js";import{GetFieldConfig as Le}from"./fieldconfig-Bk3o1wi7.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./pe-B8dP0-Ut.js";import"./geometryEngineBase-BhsKaODW.js";import Ce from"./PipeDetail-CTBPYFJW.js";import{b as Ge,g as ke}from"./poi_burst-B3btyDkV.js";import"./widget-BcWKanF2.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./identify-4SBo5EZk.js";import"./scaleUtils-DgkF6NQH.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./pipe-nogVzCHG.js";import"./executeForIds-BLdIsxvI.js";import"./project-DUuzYgGl.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./ExportImageParameters-BiedgHNY.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./Search-NSrhrIa_.js";import"./FormTableColumnFilter-BT7pLXIC.js";import"./DateFormatter-Bm9a68Ax.js";import"./config-fy91bijz.js";const Fe=le({__name:"ShutValveAnalys",props:{view:{},telport:{}},setup(X){const $=M(),P=M(),s=X,r=k({tabs:[],curOperate:""}),e={identifyResult:void 0,markLayer:void 0,mapClick:void 0,queryUrl:window.SITE_CONFIG.GIS_CONFIG.gisService+window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService,queryParams:we(),analysUrl:window.SITE_CONFIG.GIS_CONFIG.gisService+window.SITE_CONFIG.GIS_CONFIG.gisShutValveAnalysGPService,extendAnalysUrl:window.SITE_CONFIG.GIS_CONFIG.gisService+window.SITE_CONFIG.GIS_CONFIG.gisExtendShutValveAnalysGPService,jobid:"",valveFlag:!1,deviceids:[],devicelayerIndex:0,devicelayers:["阀门","计量装置"],mustShutVolveFeatures:[]},C=k({columns:[{label:"阀门类型",prop:"layerName"},{label:"阀门编号",prop:"value"}],dataList:[],pagination:{hide:!0}}),_=k({data:[],columns:[]}),I=k({handleSelectChange:t=>{var n,l;if(!s.view)return;I.selectList=t||[];const a=t.map(p=>p.SID),i=e.mustShutVolveFeatures.filter(p=>a.indexOf(p.attributes.SID)!==-1).map(p=>(p.symbol=q("point",{color:[0,255,255],outlineColor:[255,0,255],outlineWidth:2}),p));e.mustShutValveLayer=Q(s.view,{id:"extentMustShutValveLayer",title:"二次关阀"}),(n=e.mustShutValveLayer)==null||n.removeAll(),(l=e.mustShutValveLayer)==null||l.addMany(i)},dataList:[],columns:[{label:"编号",prop:"SID"},{label:"阀门级别",prop:"VALVECLASS"}],pagination:{hide:!0}}),K=k({columns:[{prop:"yhbh",label:"用户编号"},{prop:"yhxm",label:"用户姓名"},{prop:"yhdz",label:"用户地址"},{prop:"lxdh",label:"联系电话"},{prop:"dxdh",label:"短信电话"},{prop:"ysxz",label:"用水性质"},{prop:"vnum",label:"阀门编号"},{prop:"sbbh",label:"水表编号"}],dataList:[],pagination:{hide:!0}}),Y=k({gutter:12,labelPosition:"top",group:[{fieldset:{desc:"选取阀门"},fields:[{type:"btn-group",btns:[{perm:!0,styles:{width:"100%"},type:"warning",text:()=>r.curOperate==="picking"?"正在选取阀门":"点击选取阀门",disabled:()=>r.curOperate==="analysing"||r.curOperate==="detailing"||r.curOperate==="extendAnalysing"||r.curOperate==="userDetailing",loading:()=>r.curOperate==="picking",click:()=>Z()}]},{type:"table",style:{height:"80px"},config:C}]},{fieldset:{desc:"执行分析"},fields:[{type:"btn-group",itemContainerStyle:{marginBottom:0},btns:[{perm:!0,styles:{width:"100%"},loading:()=>r.curOperate==="analysing",text:()=>r.curOperate==="analysing"?"正在分析":"开始分析",disabled:()=>r.curOperate==="analysing"||r.curOperate==="detailing"||r.curOperate==="extendAnalysing"||r.curOperate==="userDetailing"||!C.dataList.length,click:()=>te()}]}]},{fieldset:{desc:"分析结果"},fields:[{label:"影响范围概览",type:"checkbox",field:"showInMap",options:[{label:"地图显示",value:"show"}],onChange:t=>{e.resultLayer&&(e.resultLayer.visible=!!t.length),e.extendResultLayer&&(e.extendResultLayer.visible=!!t.length)}},{type:"attr-table",style:{minHeight:"50px"},config:_},{type:"table",label:"必关阀",style:{height:"250px"},config:I},{type:"btn-group",itemContainerStyle:{marginBottom:"5px",marginTop:"15px"},btns:[{perm:!0,styles:{width:"100%"},loading:()=>r.curOperate==="extendAnalysing",text:()=>r.curOperate==="extendAnalysing"?"正在分析":"二次关阀分析",disabled:()=>{var t;return!((t=I.selectList)!=null&&t.length)},click:()=>ie()}]},{type:"btn-group",itemContainerStyle:{marginBottom:"5px"},btns:[{perm:!0,styles:{width:"100%"},disabled:()=>r.curOperate==="analysing"||r.curOperate==="detailing"||r.curOperate==="extendAnalysing"||r.curOperate==="userDetailing"||!C.dataList.length,loading:()=>r.curOperate==="detailing",text:()=>r.curOperate==="detailing"?"正在查询":"查看详细结果",click:()=>re()}]},{type:"btn-group",btns:[{perm:!0,styles:{width:"100%"},type:"danger",text:"清除所有",disabled:()=>r.curOperate==="analysing"||r.curOperate==="detailing"||r.curOperate==="extendAnalysing"||r.curOperate==="userDetailing",click:()=>ne()}]}]}],defaultValue:{showInMap:["show"]}}),Z=()=>{s.view&&(D("crosshair"),r.curOperate="picking",e.markLayer=Q(s.view,{id:"shutvalve-analys",title:"关阀分析标注"}),e.mapClick=s.view.on("click",t=>{var a;(a=e.markLayer)==null||a.removeAll(),ee(t)}))},ee=async t=>{var a,i,n,l,p,w,x,c,v,O,L;if(s.view){try{e.queryParams.layerIds=z(s.view,void 0,void 0,"阀门"),e.queryParams.geometry=t.mapPoint,e.queryParams.mapExtent=s.view.extent;const u=await he(e.queryUrl,e.queryParams);(a=s.view)==null||a.graphics.removeAll();const d=(i=u.results)==null?void 0:i.filter(f=>{var o,m;return((m=(o=f.feature)==null?void 0:o.geometry)==null?void 0:m.type)==="point"});if(!d.length){S.warning("没有查询到阀门"),r.curOperate="picked";return}e.identifyResult=d&&d[0],e.shutPoint=d&&ge((l=(n=d[0])==null?void 0:n.feature)==null?void 0:l.geometry,t.mapPoint);const b=e.shutPoint&&fe(e.shutPoint.x,e.shutPoint.y,{picUrl:Ge,spatialReference:(p=s.view)==null?void 0:p.spatialReference,yOffset:8}),h=d[0].feature;h&&(h.symbol=q(h.geometry.type)),d.length&&((w=e.markLayer)==null||w.add(h)),(x=e.markLayer)==null||x.add(b);const g=d.map(f=>{var o,m;return{layerName:f.layerName,layerId:f.layerId,value:(o=f.feature.attributes)==null?void 0:o.OBJECTID,attributes:(m=f.feature)==null?void 0:m.attributes}})||[];C.dataList=g.length&&[g[0]]||[];const y=h==null?void 0:h.geometry.extent;if(y){const f=y.xmax-y.xmin,o=y.ymax-y.ymin,m=y.xmin-f/2,T=y.xmax+f/2,j=y.ymin-o/2,G=y.ymax+o/2;(c=s.view)==null||c.goTo(new J({xmin:m,ymin:j,xmax:T,ymax:G,spatialReference:s.view.spatialReference})),D(""),(v=e.mapClick)!=null&&v.remove&&((O=e.mapClick)==null||O.remove())}}catch{(L=s.view)==null||L.graphics.removeAll(),r.curOperate="";return}r.curOperate="picked"}},E=()=>{var x,c,v,O,L,u,d,b,h,g,y;const t=e.resultSummary;if((x=t==null?void 0:t.layersummary)!=null&&x.length){const f={},o=[];t.layersummary.forEach(m=>{f[m.layerdbname]=m.geometrytype==="esriGeometryPoint"?m.count+"个":m.length+"米",o.push([{label:m.layername,prop:m.layerdbname}])}),_.data=f,_.columns=o}let a=t.xmin||((v=(c=s.view)==null?void 0:c.extent)==null?void 0:v.xmin),i=t.xmax||((L=(O=s.view)==null?void 0:O.extent)==null?void 0:L.xmax),n=t.ymin||((d=(u=s.view)==null?void 0:u.extent)==null?void 0:d.ymin),l=t.ymax||((h=(b=s.view)==null?void 0:b.extent)==null?void 0:h.ymax);const p=i-a,w=l-n;a-=p/2,i+=p/2,n-=w/2,l+=w/2,(y=s.view)==null||y.goTo(new J({xmin:a,ymin:n,xmax:i,ymax:l,spatialReference:(g=s.view)==null?void 0:g.spatialReference}))},te=async t=>{var a,i,n,l,p,w,x;r.curOperate="analysing";try{e.resultLayer&&((a=s.view)==null||a.map.remove(e.resultLayer)),e.extendResultLayer&&((i=s.view)==null||i.map.remove(e.extendResultLayer));const v=(l=(n=(await me(C.dataList[0].layerId)).data)==null?void 0:n.result)==null?void 0:l.rows,O=(v==null?void 0:v.length)&&v[0].layerdbname,L=(p=e.identifyResult)==null?void 0:p.feature.attributes.OBJECTID,u=await ve({valvefeatureclassname:O,valveobjectid:L,bysource:!0,usertoken:B().gToken});if(await u.waitForJobCompletion(),u.jobStatus==="job-succeeded"){e.jobid=u.jobId;const d=await u.fetchResultMapImageLayer(u.jobId);e.resultLayer=d,e.resultLayer.title="关阀分析结果";const b=W(s.view);(w=s.view)==null||w.map.add(e.resultLayer,b);const g=(await u.fetchResultData("summary")).value;(g==null?void 0:g.code)!==1e4?S.error(g.error):(e.resultSummary=(x=g==null?void 0:g.result)==null?void 0:x.summary,E()),r.tabs=e.resultSummary.layersummary.map(y=>({label:y.layername,name:y.layername,data:[]})),await A(r.tabs,0),await V()}else u.jobStatus==="job-cancelled"?S.info("已取消分析"):u.jobStatus==="job-cancelling"?S.info("任务正在取消"):u.jobStatus==="job-failed"&&S.info("分析失败，请联系管理员")}catch{S.info("分析失败，请联系管理员"),r.curOperate="picked";return}r.curOperate="analysed"},A=async(t,a)=>{if(a<t.length){const i=t[a];i.data=await N(i.name,0),a<t.length-1&&await A(t,++a)}},N=async(t,a)=>{var i,n;try{let l=await xe(((r.curOperate==="extendAnalysing"?(i=e.extendResultLayer)==null?void 0:i.url:(n=e.resultLayer)==null?void 0:n.url)||"")+"/"+a,H({where:"layername='"+t+"'",orderByFields:["OBJECTID asc"],returnGeometry:!1}));return l===null&&(l=await N(t,++a)),l}catch{return[]}},V=async()=>{var t,a,i,n,l,p,w;if(s.view){try{I.loading=!0;const c=ae(r.curOperate==="extendAnalysing"&&e.extendAnalysUrl||e.analysUrl)+e.jobid,O=((a=(t=(await ke(e.jobid)).data)==null?void 0:t.layers)==null?void 0:a.filter(o=>o.geometryType==="esriGeometryPoint").map(o=>o.id))||[],L=await Se(c,Ie({layerIds:O||[0,1],searchFields:["mustshut"],returnGeometry:!0,searchText:"1",contains:!1})),u=[],d=[];(i=L.results)==null||i.map(o=>{var j;if(!((j=o.feature)!=null&&j.attributes))return;const m=o.feature.attributes,T=u.find(G=>G.name===m.layername);if(T)T.data.push(m==null?void 0:m.OBJECTID);else{const G=m.layername;u.push({label:G,name:G,id:o.layerId,data:[m.OBJECTID]})}});const b=u.find(o=>o.name==="阀门");if(!b){I.dataList=[],I.loading=!1;return}const h=await z(s.view,void 0,void 0,"阀门"),g=b==null?void 0:b.id,y=await Le("阀门"),f=await Oe(window.SITE_CONFIG.GIS_CONFIG.gisService+window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService+"/"+h[0],H({objectIds:b==null?void 0:b.data,outFields:((p=(l=(n=y.data)==null?void 0:n.result)==null?void 0:l.rows)==null?void 0:p.filter(o=>o.visible).map(o=>o.name))||[],returnGeometry:!0}));(w=f.features)==null||w.map(o=>{d.push({...o.attributes||{},layerId:g})}),e.mustShutVolveFeatures=f.features,I.dataList=d}catch{S.error("必关阀查询失败")}I.loading=!1}},ae=t=>{t=t||window.SITE_CONFIG.GIS_CONFIG.gisService+window.SITE_CONFIG.GIS_CONFIG.gisBurstGPService;const a=/GPServer.+/;return t.replace(a,"MapServer/jobs/")},re=()=>{var t;r.curOperate="detailing",(t=P.value)==null||t.openDialog()},ie=async()=>{var t,a;r.curOperate="extendAnalysing";try{e.resultLayer&&((t=s.view)==null||t.map.remove(e.resultLayer));const i=((a=I.selectList)==null?void 0:a.map(n=>n.OBJECTID))||[];await se("Valve",i),r.curOperate="extendAnalysed"}catch{r.curOperate="analysed"}},se=async(t,a)=>{var n,l;const i=await be({bysource:!0,usertoken:B().gToken,valves:t+":"+a.join(",")});if(await i.waitForJobCompletion(),i.jobStatus==="job-succeeded"){e.jobid=i.jobId;const p=await i.fetchResultMapImageLayer(i.jobId);e.extendResultLayer=p,e.extendResultLayer.title="二次关阀分析结果";const w=W(s.view);(n=s.view)==null||n.map.add(e.extendResultLayer,w);const c=(await i.fetchResultData("summary")).value;(c==null?void 0:c.code)!==1e4?S.error(c.error):(e.resultSummary=(l=c==null?void 0:c.result)==null?void 0:l.summary,E()),r.tabs=e.resultSummary.layersummary.map(v=>({label:v.layername,name:v.layername,data:[]})),await A(r.tabs,0),await V()}else i.jobStatus==="job-cancelled"?S.info("已取消分析"):i.jobStatus==="job-cancelling"?S.info("任务正在取消"):i.jobStatus==="job-failed"&&(S.info("分析失败，请联系管理员"),r.curOperate="analysed")},oe=async()=>{var t;s.view&&((t=P.value)==null||t.extentTo(s.view))},ne=()=>{R(),_.data=[],I.dataList=[],C.dataList=[],K.dataList=[]},D=t=>{const a=document.getElementById("viewDiv");a&&(a.style.cursor=t)},R=()=>{var t,a,i,n,l;D(""),e.resultLayer&&((t=s.view)==null||t.map.remove(e.resultLayer)),e.markLayer&&((a=s.view)==null||a.map.remove(e.markLayer)),e.mustShutValveLayer&&((i=s.view)==null||i.map.remove(e.mustShutValveLayer)),e.extentMustShutValveLayer&&((n=s.view)==null||n.map.remove(e.extentMustShutValveLayer)),(l=e.mapClick)!=null&&l.remove&&e.mapClick.remove()};return pe(()=>{var t,a,i;R(),(t=e.mapClick)!=null&&t.remove&&((a=e.mapClick)==null||a.remove()),e.markLayer&&((i=s.view)==null||i.map.remove(e.markLayer))}),(t,a)=>{const i=ye;return ue(),ce("div",null,[U(i,{ref_key:"refForm",ref:$,config:F(Y)},null,8,["config"]),U(Ce,{ref_key:"refDetail",ref:P,tabs:F(r).tabs,telport:t.telport,onRefreshed:a[0]||(a[0]=()=>F(r).curOperate="viewingDetail"),onRefreshing:a[1]||(a[1]=()=>F(r).curOperate="detailing"),onClose:a[2]||(a[2]=()=>F(r).curOperate="analysed"),onRowdblclick:oe},null,8,["tabs","telport"])])}}}),aa=de(Fe,[["__scopeId","data-v-6ea05659"]]);export{aa as default};
