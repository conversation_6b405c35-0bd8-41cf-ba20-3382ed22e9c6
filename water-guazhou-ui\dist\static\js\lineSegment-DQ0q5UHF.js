import{aN as Q,af as d,aO as l,aW as $,ab as B,aP as q,aQ as A,b0 as y,b1 as j,b2 as P}from"./MapView-DaoQedLH.js";import{s as z,c as s}from"./sphere-NgXH-gLx.js";function w(t){return t?{origin:Q(t.origin),vector:Q(t.vector)}:{origin:d(),vector:d()}}function C(t,r){const n=F.get();return n.origin=t,n.vector=r,n}function I(t,r=w()){return D(t.origin,t.vector,r)}function D(t,r,n=w()){return l(n.origin,t),l(n.vector,r),n}function J(t,r,n=w()){return l(n.origin,t),$(n.vector,r,t),n}function K(t,r){const n=$(s.get(),r,t.origin),c=B(t.vector,n),g=B(t.vector,t.vector),e=j(c/g,0,1),f=$(s.get(),q(s.get(),t.vector,e),n);return B(f,f)}function L(t,r,n){return E(t,r,0,1,n)}function R(t,r,n){return A(n,t.origin,q(n,t.vector,r))}function E(t,r,n,c,g){const{vector:e,origin:f}=t,a=$(s.get(),r,f),p=B(e,a)/y(e);return q(g,e,j(p,n,c)),A(g,g,t.origin)}function T(t,r){if(S(t,C(r.origin,r.direction),!1,k)){const{tA:n,pB:c,distance2:g}=k;if(n>=0&&n<=1)return g;if(n<0)return P(t.origin,c);if(n>1)return P(A(s.get(),t.origin,t.vector),c)}return null}function U(t,r,n){return!!S(t,r,!0,k)&&(l(n,k.pA),!0)}function S(t,r,n,c){const e=t.origin,f=A(s.get(),e,t.vector),a=r.origin,p=A(s.get(),a,r.vector),u=s.get(),i=s.get();if(u[0]=e[0]-a[0],u[1]=e[1]-a[1],u[2]=e[2]-a[2],i[0]=p[0]-a[0],i[1]=p[1]-a[1],i[2]=p[2]-a[2],Math.abs(i[0])<1e-6&&Math.abs(i[1])<1e-6&&Math.abs(i[2])<1e-6)return!1;const o=s.get();if(o[0]=f[0]-e[0],o[1]=f[1]-e[1],o[2]=f[2]-e[2],Math.abs(o[0])<1e-6&&Math.abs(o[1])<1e-6&&Math.abs(o[2])<1e-6)return!1;const N=u[0]*i[0]+u[1]*i[1]+u[2]*i[2],m=i[0]*o[0]+i[1]*o[1]+i[2]*o[2],W=u[0]*o[0]+u[1]*o[1]+u[2]*o[2],x=i[0]*i[0]+i[1]*i[1]+i[2]*i[2],O=(o[0]*o[0]+o[1]*o[1]+o[2]*o[2])*x-m*m;if(Math.abs(O)<1e-6)return!1;let v=(N*m-W*x)/O,b=(N+m*v)/x;n&&(v=j(v,0,1),b=j(b,0,1));const h=s.get(),M=s.get();return h[0]=e[0]+v*o[0],h[1]=e[1]+v*o[1],h[2]=e[2]+v*o[2],M[0]=a[0]+b*i[0],M[1]=a[1]+b*i[1],M[2]=a[2]+b*i[2],c.tA=v,c.tB=b,c.pA=h,c.pB=M,c.distance2=P(h,M),!0}const k={tA:0,tB:0,pA:d(),pB:d(),distance2:0},F=new z(()=>w());export{E as A,T as B,K as M,J as b,I as h,L as j,U as k,R as l,D as m,w as v};
