import{d as n,a0 as r,c as a,o as c,Q as l,g as i,n as _,p as d,C as u}from"./index-r0dFAfgr.js";const p={class:"main"},v=n({__name:"wflc_overview",setup(f){const e=r();a({});const s=a(),t=()=>{console.log(e.projectList),e.projectList[0].id};return c(()=>{t(),s.value=setInterval(()=>{t()},3e4)}),l(()=>{clearInterval(s.value)}),(m,o)=>(i(),_("div",p,o[0]||(o[0]=[d("div",{class:"card zutai-card"},null,-1)])))}}),w=u(v,[["__scopeId","data-v-654391ce"]]);export{w as default};
