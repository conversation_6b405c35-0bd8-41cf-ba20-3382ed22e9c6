package org.thingsboard.server.dao.util.imodel.query.store;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.store.StoreOutRecord;
import org.thingsboard.server.dao.util.TimeUtils;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;

import java.util.Date;

@Getter
@Setter
public class StoreOutRecordPageRequest extends AdvancedPageableQueryEntity<StoreOutRecord, StoreOutRecordPageRequest> {
    // 出库单编号
    private String code;

    // 出库单标题
    private String title;

    // 目标仓库ID
    private String storehouseId;

    // 是否出库完成
    private Boolean isOut;

    // 出库类型
    private String type;

    // 施工项目ID
    private String constructionProjectId;

    // 领用人ID
    private String receiveUserId;

    // 领用部门ID
    private String receiveDepartmentId;

    // 经办人Id
    private String manager;

    // 经办部门Id
    private String managerDepartmentId;

    // 出库时间(起)
    private String outTimeFrom;

    // 出库时间(止)
    private String outTimeTo;

    // 是否报账
    private Boolean reimbursement;

    // 创建人Id
    private String creator;

    // 创建人部门Id
    private String creatorDepartmentId;

    public Date getOutTimeFrom() {
        return TimeUtils.defaultIfNull(outTimeFrom, null);
    }

    public Date getOutTimeTo() {
        if (outTimeTo != null && outTimeTo.getClass().equals(String.class)) {
            outTimeTo = outTimeTo.substring(0, 10) + " 23:59:59";
        }
        return TimeUtils.defaultIfNull(outTimeTo, null);
    }
}
