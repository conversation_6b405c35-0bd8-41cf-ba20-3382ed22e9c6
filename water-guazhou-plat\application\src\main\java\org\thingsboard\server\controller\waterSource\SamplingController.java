package org.thingsboard.server.controller.waterSource;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.fileupload.ISysFileService;
import org.thingsboard.server.dao.model.sql.sampling.Sampling;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;
import org.thingsboard.server.dao.waterSource.SamplingService;

import java.util.List;
import java.util.Map;

/**
 * 水质采样记录管理
 */
@Slf4j
@RestController
@RequestMapping("/api/sampling")
@Api(tags = "采样记录")
public class SamplingController extends BaseController {

    @Autowired
    private SamplingService samplingService;
    @Autowired
    private ISysFileService fileService;

    @ApiOperation(value = "获取采样记录列表")
//    @PreAuthorize("hasAnyAuthority('TENANT_ADMIN', 'CUSTOMER_USER')")
    @GetMapping("list")
    public IstarResponse getList(@RequestParam Map<String, Object> params) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return IstarResponse.ok(samplingService.getList(params, tenantId));
    }

    @ApiOperation(value = "保存采样记录")
//    @PreAuthorize("hasAuthority('TENANT_ADMIN')")
    @PostMapping
    public IstarResponse save(@RequestBody Sampling sampling) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        sampling.setTenantId(tenantId);
        sampling.setCreator(UUIDConverter.fromTimeUUID(getCurrentUser().getUuidId()));
        return IstarResponse.ok(samplingService.save(sampling));
    }

    @ApiOperation(value = "批量删除采样记录")
//    @PreAuthorize("hasAuthority('TENANT_ADMIN')")
    @DeleteMapping
    public IstarResponse delete(@RequestBody List<String> idList) throws ThingsboardException {
        samplingService.delete(idList);
        return IstarResponse.ok();
    }

    @ApiOperation(value = "获取单个采样记录")
//    @PreAuthorize("hasAnyAuthority('TENANT_ADMIN', 'CUSTOMER_USER')")
    @GetMapping("/{id}")
    public IstarResponse getById(@PathVariable String id) throws ThingsboardException {
        return IstarResponse.ok(samplingService.getById(id));
    }

    @ApiOperation(value = "更新")
//    @PreAuthorize("hasAuthority('TENANT_ADMIN')")
    @PostMapping("/update")
    public IstarResponse update(@RequestBody Sampling sampling) throws ThingsboardException {
        return IstarResponse.ok(samplingService.uploadRecord(sampling));
    }

}
