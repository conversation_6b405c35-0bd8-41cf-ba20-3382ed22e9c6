<template>
  <Form
    ref="refForm"
    :config="FormConfig"
  />
</template>
<script lang="ts" setup>
import Extent from '@arcgis/core/geometry/Extent.js'
import { GetFieldConfig, SetFieldConfig } from '@/api/mapservice/fieldconfig'
import { IFormIns } from '@/components/type'
import { SLConfirm, SLMessage } from '@/utils/Message'
import {
  createGraphic,
  createPictureMarker,
  createRect,
  excuteIdentify,
  excuteQuery,
  getGraphicLayer,
  getSubLayerIds,
  initDrawer,
  initIdentifyParams,
  initQueryParams,
  setMapCursor,
  setSymbol
} from '@/utils/MapHelper'
import { formatTree } from '@/utils/GlobalHelper'
import { getMapLocationImageUrl } from '@/utils/URLHelper'

const props = defineProps<{
  view?: __esri.MapView
}>()
const state = reactive<{
  pipeLayerOption: NormalOption[]
  fieldOptions: any[]
}>({
  pipeLayerOption: [],
  fieldOptions: []
})
const staticState: {
  drawer?: __esri.Draw
  queryParams?: any
  drawAction?: __esri.DrawAction
  mapClick?: any
  resultLayer?: __esri.GraphicsLayer
  graphicLayer?: __esri.GraphicsLayer
} = {
  drawer: undefined,
  queryParams: initIdentifyParams()
}
const refForm = ref<IFormIns>()
const AttrTableConfig = reactive<IAttrTable>({
  data: [],
  columns: []
})
const FormConfig = reactive<IFormConfig>({
  labelPosition: 'top',
  gutter: 12,
  group: [
    {
      fields: [
        {
          type: 'tabs',
          field: 'type',
          tabType: 'simple',
          tabs: [
            { label: '属性标注', value: 'anno' },
            { label: '设置', value: 'setting' }
          ]
        }
      ]
    },
    {
      handleHidden: (row, query, group) => {
        group.hidden = row.type !== 'anno'
      },
      fieldset: {
        desc: '要素选择',
        right: [
          {
            style: {
              marginLeft: 'auto'
            },
            items: [
              {
                type: 'btn-group',
                label: '',
                btns: [
                  {
                    perm: true,
                    text: '框选',
                    size: 'small',
                    click: () => {
                      if (!props.view) return
                      staticState.drawAction?.destroy()
                      staticState.drawer?.destroy()
                      staticState.graphicLayer?.removeAll()
                      staticState.drawer = initDrawer(props.view)
                      staticState.drawAction = staticState.drawer?.create('rectangle')
                      staticState.drawAction?.on('vertex-add', drawRect)
                      staticState.drawAction?.on('cursor-update', drawRect)
                      staticState.drawAction?.on('draw-complete', e => startQuery(e, true))
                    }
                  }
                ]
              }
            ]
          }
        ]
      },
      fields: [
        {
          type: 'list',
          field: 'name',
          label: '',
          highlightCurrentRow: true,
          data: [],
          displayField: 'layerName',
          valueField: 'value',
          wrapperStyle: {
            height: '200px'
          },
          nodeClick: async node => {
            Goto(node)
          },
          formatter: (val, row, config: any) => {
            return (
              (row[config.displayField] || '')
              + '/'
              + (row?.attributes?.OBJECTID || '')
            )
          }
        }
      ]
    },
    {
      handleHidden: (row, query, group) => {
        group.hidden = row.type !== 'anno'
      },
      fieldset: {
        desc: '详细'
      },
      fields: [
        {
          type: 'attr-table',
          config: AttrTableConfig
        }
      ]
    },
    {
      handleHidden: (row, query, group) => {
        group.hidden = row.type !== 'setting'
      },
      fieldset: {
        desc: '图层选择'
      },
      fields: [
        {
          type: 'select',
          field: 'layer',
          options: [],
          onChange: () => setFieldOptions()
        }
      ]
    },
    {
      id: 'visibleattrs',
      handleHidden: (row, query, group) => {
        group.hidden = row.type !== 'setting'
      },
      fieldset: {
        desc: '可见属性'
      },
      fields: [
        {
          type: 'tree',
          style: {
            height: '300px'
          },
          field: 'attrs',
          options: [],
          showCheckbox: true,
          multiple: true
        }
      ],
      groupBtns: {
        styles: {
          marginBottom: '20px',
          display: 'flex',
          justifyContent: 'space-between'
        },
        btns: [
          {
            perm: true,
            text: '全选',
            styles: {
              width: '100%'
            },
            click: () => handleResetCheckedFields('all')
          },
          {
            perm: true,
            text: '全不选',
            styles: {
              width: '100%'
            },
            click: () => handleResetCheckedFields('none')
          },
          {
            perm: true,
            text: '重置',
            type: 'default',
            styles: {
              width: '100%'
            },
            click: () => handleResetCheckedFields()
          }
        ]
      }
    },
    {
      handleHidden: (row, query, group) => {
        group.hidden = row.type !== 'setting'
      },
      fields: [
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              styles: {
                width: '100%'
              },
              type: 'success',
              text: '保存',
              click: () => {
                refForm.value?.Submit()
              }
            }
          ]
        }
      ]
    }
  ],
  defaultValue: {
    type: 'anno',
    layer: ''
  },
  submit: params => {
    SLConfirm('确定保存?', '提示信息')
      .then(async () => {
        try {
          if (!params.layer) return
          // console.log(params)
          // const checked: any[] = params?.attrs || []
          // const fields = fieldOptions()
          // fields.map(item => {
          //   item.visible = checked.includes(item.name)
          // })
          const fields = state.fieldOptions.map(item => {
            item.visible = params.attrs.indexOf(item.name) !== -1
            return item
          })
          const res = await SetFieldConfig({
            layername: params.layer,
            fields,
            f: 'pjson'
          })
          if (res.data.code === 10000) {
            SLMessage.success(res.data.message || '保存成功')
          } else {
            SLMessage.error(res.data.message || '保存失败')
          }
        } catch (error) {
          SLMessage.error('系统错误')
        }
      })
      .catch(() => {
        //
      })
  }
})
const setFieldOptions = async () => {
  const layer = refForm.value?.dataForm.layer
  const field = FormConfig.group.find(item => item.id === 'visibleattrs')
    ?.fields[0] as IFormTree
  const res = await GetFieldConfig(layer)
  state.fieldOptions = res.data?.result?.rows || []
  field
    && (field.options = formatTree(state.fieldOptions, {
      id: 'name',
      label: 'alias',
      value: 'name'
    }))
  handleResetCheckedFields()
}
const handleResetCheckedFields = (checkType?: 'all' | 'none') => {
  const value = checkType === 'none'
    ? []
    : checkType === 'all'
      ? state.fieldOptions.map(item => item.name)
      : state.fieldOptions
        .filter(item => item.visible === true)
        .map(item => item.name)
  refForm.value?.dataForm && (refForm.value.dataForm.attrs = value)
}
const drawRect = e => {
  props.view?.graphics.removeAll()
  const vertices = e?.vertices || []
  const rect = createRect(vertices, props.view?.spatialReference)
  const rectGraphic = rect
    && createGraphic({
      geometry: rect,
      symbol: setSymbol('polygon') as any
    })
  rectGraphic && props.view?.graphics.add(rectGraphic)
}
const startQuery = (e: any, isRect?: boolean) => {
  if (!props.view) return
  staticState.queryParams.layerIds = getSubLayerIds(props.view, true)
  staticState.queryParams.geometry = isRect
    ? createRect(e.vertices, props.view?.spatialReference)
    : e.mapPoint
  staticState.queryParams.mapExtent = props.view.extent
  props.view
    && excuteIdentify(window.SITE_CONFIG.GIS_CONFIG.gisService + window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService, staticState.queryParams)
      .then(res => {
        props.view?.graphics.removeAll()
        const field = FormConfig.group[1].fields[0] as IFormList
        if (!field) return
        field.data = res.results?.map(item => {
          return {
            layerName: item.layerName,
            layerId: item.layerId,
            value: item.feature.attributes?.OBJECTID,
            attributes: item.feature?.attributes
          }
        }) || []
        refForm.value && (refForm.value.dataForm['name'] = field.data[0])
        Goto(field.data[0])
      })
      .catch(() => {
        props.view?.graphics.removeAll()
      })
}
const initQuery = (view: __esri.MapView) => {
  if (staticState.drawer) staticState.drawer.destroy()
  staticState.drawer = initDrawer(view)
  staticState.graphicLayer = getGraphicLayer(view, {
    id: 'attr-annotation',
    title: '属性标注'
  })
  staticState.mapClick = view.on('click', e => {
    staticState.graphicLayer?.removeAll()
    const picturemark = createPictureMarker(e.mapPoint.x, e.mapPoint.y, {
      picUrl: getMapLocationImageUrl(),
      spatialReference: view?.spatialReference,
      yOffset: 8
    })
    staticState.graphicLayer?.add(picturemark)
    startQuery(e)
  })
}
const Goto = async (node: any) => {
  const { layerName, layerId, attributes } = node || {}
  if (!props.view) return
  staticState.resultLayer = getGraphicLayer(props.view, {
    id: 'attr-annotation-result',
    title: '属性查询结果'
  })
  const layerfields = await GetFieldConfig(layerName)
  const disPlayFields: any[] = layerfields.data?.result?.rows?.filter(item => item.visible === true) || []
  const outFields = disPlayFields.map(item => {
    return item.name
  })
  const results = await excuteQuery(
    window.SITE_CONFIG.GIS_CONFIG.gisService + window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService + '/' + layerId,
    initQueryParams({
      where: 'OBJECTID =' + (attributes?.OBJECTID || ''),
      outFields
    })
  )

  if (!results?.features?.length) return
  results.features[0].symbol = setSymbol(
    results.features[0].geometry.type
  ) as any
  staticState.resultLayer?.removeAll()
  staticState.resultLayer?.add(results.features[0])
  if (results.features[0].geometry.type === 'point') {
    props.view.goTo(results.features[0].geometry)
  } else {
    const extent = results.features[0].geometry.extent
    const width = extent.xmax - extent.xmin
    const height = extent.ymax - extent.ymin
    const xmin = extent.xmin - width / 2
    const xmax = extent.xmax + width / 2
    const ymin = extent.ymin - height / 2
    const ymax = extent.ymax + height / 2
    props.view?.goTo(
      new Extent({
        xmin,
        ymin,
        xmax,
        ymax,
        spatialReference: props.view.spatialReference
      })
    )
  }
  AttrTableConfig.data = results.features[0].attributes || []
  AttrTableConfig.columns = disPlayFields.map(item => {
    return [
      {
        label: item.alias,
        prop: item.name
      }
    ]
  })
}
const destroy = () => {
  staticState.drawer?.destroy()
  staticState.drawer = undefined
  staticState.drawAction?.destroy()
  staticState.drawAction = undefined
  staticState.resultLayer?.removeAll()
  staticState.resultLayer && props.view?.map.remove(staticState.resultLayer)
  staticState.graphicLayer?.removeAll()
  props.view?.graphics.removeAll()
  staticState.graphicLayer && props.view?.map.remove(staticState.graphicLayer)
  staticState.mapClick?.remove && staticState.mapClick.remove()
}
const initBaseLayer = async () => {
  const pipeLayer: any = props.view?.map.findLayerById('pipelayer')
  state.pipeLayerOption = []
  pipeLayer?.sublayers?.map(item => {
    state.pipeLayerOption?.push({
      label: item.title,
      value: item.title
    })
  })
  const layerField = FormConfig.group[3].fields[0] as IFormSelect
  layerField && (layerField.options = state.pipeLayerOption)

  refForm.value?.dataForm
    && (refForm.value.dataForm.layer = state.pipeLayerOption && state.pipeLayerOption[0]?.value)
  await setFieldOptions()
  handleResetCheckedFields()
}
onMounted(() => {
  props.view && initQuery(props.view)
  initBaseLayer()
  setMapCursor('crosshair')
})
onBeforeUnmount(() => {
  destroy()
  setMapCursor('')
})
</script>
<style lang="scss"></style>
