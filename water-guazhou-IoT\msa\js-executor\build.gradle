/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import org.apache.tools.ant.filters.ReplaceTokens

buildscript {
    ext {
        osPackageVersion = "3.8.0"
    }
    repositories {
        jcenter()
    }
    dependencies {
        classpath("com.netflix.nebula:gradle-ospackage-plugin:${osPackageVersion}")
    }
}


apply plugin: "nebula.ospackage"

buildDir = projectBuildDir
version = projectVersion
distsDirName = "./"

// OS Package plugin configuration
ospackage {
    packageName = pkgName
    version = "${project.version}"
    release = 1
    os = LINUX
    type = BINARY

    into pkgInstallFolder

    user pkgUser
    permissionGroup pkgUser

    // Copy the executable file
    from("target/package/linux/bin/${pkgName}") {
        fileMode 0500
        into "bin"
    }

    // Copy the init file
    from("target/package/linux/init/${pkgName}") {
        fileMode 0500
        into "init"
    }

    // Copy the config files
    from("target/package/linux/conf") {
        fileType CONFIG | NOREPLACE
        fileMode 0754
        into "conf"
    }

}

// Configure our RPM build task
buildRpm {

    arch = X86_64

    version = projectVersion.replace('-', '')
    archiveName = "${pkgName}.rpm"

    preInstall file("${buildDir}/control/rpm/preinst")
    postInstall file("${buildDir}/control/rpm/postinst")
    preUninstall file("${buildDir}/control/rpm/prerm")
    postUninstall file("${buildDir}/control/rpm/postrm")

    user pkgUser
    permissionGroup pkgUser

    // Copy the system unit files
    from("${buildDir}/control/${pkgName}.service") {
        addParentDirs = false
        fileMode 0644
        into "/usr/lib/systemd/system"
    }

    directory(pkgLogFolder, 0755)
    link("/etc/${pkgName}/conf", "${pkgInstallFolder}/conf")
}

// Same as the buildRpm task
buildDeb {

    arch = "amd64"

    archiveName = "${pkgName}.deb"

    configurationFile("${pkgInstallFolder}/conf/${pkgName}.conf")
    configurationFile("${pkgInstallFolder}/conf/custom-environment-variables.yml")
    configurationFile("${pkgInstallFolder}/conf/default.yml")
    configurationFile("${pkgInstallFolder}/conf/logger.js")

    preInstall file("${buildDir}/control/deb/preinst")
    postInstall file("${buildDir}/control/deb/postinst")
    preUninstall file("${buildDir}/control/deb/prerm")
    postUninstall file("${buildDir}/control/deb/postrm")

    user pkgUser
    permissionGroup pkgUser

    directory(pkgLogFolder, 0755)
    link("/etc/init.d/${pkgName}", "${pkgInstallFolder}/init/${pkgName}")
    link("/etc/${pkgName}/conf", "${pkgInstallFolder}/conf")
}
