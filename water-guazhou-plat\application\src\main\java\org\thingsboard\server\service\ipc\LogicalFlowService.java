package org.thingsboard.server.service.ipc;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.data.redis.core.BoundHashOperations;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.config.RabbitMQConfigurationProperties;
import org.thingsboard.server.dao.logicalFlow.BO.LogicalFlowBO;
import org.thingsboard.server.dao.logicalFlow.BO.LogicalFlowMessage;
import org.thingsboard.server.dao.logicalFlow.BO.LogicalFlowNodeBO;
import org.thingsboard.server.dao.logicalFlow.LogicalFlowHistoryService;
import org.thingsboard.server.dao.model.sql.LogicalFlow;
import org.thingsboard.server.dao.model.sql.LogicalFlowHistory;
import org.thingsboard.server.dao.model.sql.LogicalFlowNode;
import org.thingsboard.server.dao.sql.logicalFlow.LogicalFlowNodeRepository;
import org.thingsboard.server.dao.sql.logicalFlow.LogicalFlowRepository;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@EnableConfigurationProperties(RabbitMQConfigurationProperties.class)
public class LogicalFlowService {

    @Autowired
    private LogicalFlowRepository logicalFlowRepository;

    @Autowired
    private LogicalFlowNodeRepository logicalFlowNodeRepository;

    @Autowired
    private AmqpTemplate amqpTemplate;

    @Autowired
    private RabbitMQConfigurationProperties rabbitMQConfigurationProperties;

    @Autowired
    private LogicalFlowHistoryService logicalFlowHistoryService;


    @Autowired
    @Qualifier("logicalFlowContinuousRunningNode")
    private BoundHashOperations<String, Object, Object> logicalFlowContinuousRunningNode;

    @Autowired
    @Qualifier("logicalFlowRunningNode")
    private BoundHashOperations<String, Object, Object> logicalFlowRunningNode;

    public List<LogicalFlow> findAll() {
        return logicalFlowRepository.findAll();
    }

    /**
     * 查询所有可执行的逻辑流程
     *
     * @return List
     */
    public List<LogicalFlowBO> findLogicalFlowBOList() {
        return this.findAll().stream()
                .filter(logicalFlow -> DataConstants.LOGICAL_FLOW_NODE_ROOT_PARENT_ID.equals(logicalFlow.getParentId())
                        && DataConstants.LOGICAL_FLOW_ENABLE.equals(logicalFlow.getStatus())
                        && DataConstants.TriggerType.CLOUD.name().equals(logicalFlow.getType())) // 仅查询主流程
                .map(logicalFlow -> {
                    LogicalFlowBO logicalFlowBO = new LogicalFlowBO(logicalFlow, null);
                    logicalFlowBO.setLogicalFlowNodeBO(findLogicalFlowNodeBOByLogicalId(logicalFlowBO.getId()));
                    return logicalFlowBO;
                })
                .collect(Collectors.toList());
    }

    /**
     * 查询指定逻辑流程的流程节点
     *
     * @param logicalFlowId 逻辑流程ID
     * @return LogicalFlowNodeBO
     */
    public LogicalFlowNodeBO findLogicalFlowNodeBOByLogicalId(String logicalFlowId) {
        // 检查逻辑流程是否存在
        LogicalFlow one = logicalFlowRepository.findOne(logicalFlowId);
        if (one != null) {
            List<LogicalFlowNode> logicalFlowNodeList = logicalFlowNodeRepository
                    .findByLogicalFlowIdAndParentId(logicalFlowId, DataConstants.LOGICAL_FLOW_NODE_ROOT_PARENT_ID);
            if (logicalFlowNodeList !=null && logicalFlowNodeList.size() == 1) {
                LogicalFlowNodeBO logicalFlowNodeBO = new LogicalFlowNodeBO(logicalFlowNodeList.get(0), null);
                logicalFlowNodeBO.setLogicalFlowName(one.getName());
                return processLogicalNodeList(logicalFlowNodeBO);
            }
            log.error("逻辑流程只能有一个根节点! ID: [{}]", logicalFlowId);
            return null;
        }
        log.error("查询的逻辑流程不存在! ID: [{}]", logicalFlowId);
        return null;
    }

    /**
     * 执行根节点
     *
     * @param name      流程名称
     * @param rootNode  根节点
     */
    public void executeRootNode(String name, LogicalFlowNodeBO rootNode) {
        // 校验是否为根节点
        if (DataConstants.LOGICAL_FLOW_NODE_ROOT_PARENT_ID.equals(rootNode.getParentId())) {
            log.debug("执行根节点. 流程名称: [{}], 根节点ID: [{}]", name, rootNode.getId());
            // 记录执行历史
            long time = System.currentTimeMillis();
            LogicalFlowHistory history = new LogicalFlowHistory();
            history.setLogicalFlowId(rootNode.getLogicalFlowId());
            history.setLogicalFlowName(name);
            history.setCreatedTime(time);
            history.setStartTime(time);
            logicalFlowHistoryService.save(history);

            // 记录根节点执行理事（是否需要记录根？目前没有记录开始节点）

            rootNode.setLogicalFlowName(name);
            rootNode.setLogicalFlowHistory(history);
            executeChildrenNode(rootNode);
        }
    }

    /**
     * 执行父节点的所有子节点
     *
     * @param parentNode 父节点
     */
    public void executeChildrenNode(LogicalFlowNodeBO parentNode) {
        // 检查子节点个数
        if (parentNode == null) {
            return;
        }
        List<LogicalFlowNodeBO> children = parentNode.getChildren();
        if (children == null) {
            return;
        }
        if (children.size() == 1) {// 单个节点直接执行
            LogicalFlowNodeBO logicalFlowNodeBO = children.get(0);
            JSONObject param = JSON.parseObject(StringUtils.isEmpty(logicalFlowNodeBO.getParam()) ? "{}" : logicalFlowNodeBO.getParam());
            logicalFlowNodeBO.setParentContinuousNodeId(parentNode);
            logicalFlowNodeBO.setLogicalFlowName(parentNode.getLogicalFlowName());
            logicalFlowNodeBO.setLogicalFlowHistory(parentNode.getLogicalFlowHistory());
            if (parentNode.getIsChildFlow()) {
                logicalFlowNodeBO.setIsChildFlow(true);
                logicalFlowNodeBO.setParentNodeId(parentNode.getParentNodeId());
            }
            if (DataConstants.LogicalFlowNodeType.CONTINUOUS.name().equals(logicalFlowNodeBO.getType())) {
                executeContinuousNode(logicalFlowNodeBO);
            } else {
                executeNode(logicalFlowNodeBO, logicalFlowNodeBO.getType(), Long.valueOf(param.getString("time") == null ? "0" : param.getString("time")));
            }
        } else {// 多个节点
            // 查看父节点是否为间隔轮询类型
            executeNodes(parentNode, children);
        }
    }

    /**
     * 执行多个兄弟节点
     *
     * @param parentNode    父节点
     * @param children      待执行的子节点
     */
    private void executeNodes(LogicalFlowNodeBO parentNode, List<LogicalFlowNodeBO> children) {
        // 判断父节点类型
        if (DataConstants.LogicalFlowNodeType.POLLING.name().equals(parentNode.getType())) {// 轮询
            children = children.stream().sorted(Comparator.comparing(LogicalFlowNode::getCreateTime)).collect(Collectors.toList());
            int i = 0;
            for (LogicalFlowNodeBO child : children) {
                // 计算执行间隔时间
                Long time = JSON.parseObject(parentNode.getParam()).getLong("time");
                log.info("nodeId: {}, time: {}", child.getId(), time * i);
                child.setLogicalFlowName(parentNode.getLogicalFlowName());
                child.setLogicalFlowHistory(parentNode.getLogicalFlowHistory());
                if (parentNode.getIsChildFlow()) {
                    child.setIsChildFlow(true);
                    child.setParentNodeId(parentNode.getParentNodeId());
                }
                executeNode(child, DataConstants.LogicalFlowNodeType.POLLING.name(), time * i);
                i++;
            }
        } else {// 其他类型节点
            for (LogicalFlowNodeBO child : children) {
                child.setLogicalFlowHistory(parentNode.getLogicalFlowHistory());
                child.setLogicalFlowName(parentNode.getLogicalFlowName());
                if (parentNode.getIsChildFlow()) {
                    child.setIsChildFlow(true);
                    child.setParentNodeId(parentNode.getParentNodeId());
                }
                if (DataConstants.LogicalFlowNodeType.CONTINUOUS.name().equals(child.getType())) {
                    if (executeContinuousNode(child))
                        return;
                } else {
                    executeNode(child, child.getType(), 0L);
                }
            }

        }
    }

    /**
     * 持续节点单独逻辑
     *
     * @param child
     * @return
     */
    private boolean executeContinuousNode(LogicalFlowNodeBO child) {
        // 查询持续节点的子流程
        JSONObject param = JSON.parseObject(child.getParam());
        LogicalFlow logicalFlow = this.findById(param.getString("childFlowId"));
        if (logicalFlow == null) {
            log.error("子流程不存在! id = [{}]", param.getString("childFlowId"));
            return true;
        }
        LogicalFlowBO logicalFlowBO = new LogicalFlowBO(logicalFlow, null);
        LogicalFlowNodeBO logicalFlowNodeBOByLogicalId = this.findLogicalFlowNodeBOByLogicalId(logicalFlowBO.getId());
        logicalFlowNodeBOByLogicalId.setLogicalFlowName(logicalFlow.getName());
        logicalFlowBO.setLogicalFlowNodeBO(logicalFlowNodeBOByLogicalId);
        logicalFlowBO.getLogicalFlowNodeBO().setParentContinuousNodeId(child);
        logicalFlowBO.getLogicalFlowNodeBO().setLogicalFlowHistory(child.getLogicalFlowHistory());
        executeNode(logicalFlowBO.getLogicalFlowNodeBO(), DataConstants.LogicalFlowNodeType.CONTINUOUS.name(), Long.valueOf(param.getString("time")));
        return false;
    }

    /**
     * 发送消息到消息队列执行流程节点
     *
     * @param sleepOrContinuousTime         延迟执行时间
     * @param logicalFlowNodeBO             执行的节点
     * @param sleepOrContinuousTime         睡眠或持续时间
     */
    private void executeNode(LogicalFlowNodeBO logicalFlowNodeBO, String type, Long sleepOrContinuousTime) {
        LogicalFlowMessage logicalFlowMessageBO = new LogicalFlowMessage();
        logicalFlowMessageBO.setSleepOrContinuousTime(sleepOrContinuousTime);
        logicalFlowMessageBO.setLogicalFlowNodeBo(logicalFlowNodeBO);
        logicalFlowMessageBO.setType(type);
        logicalFlowRunningNode.put(logicalFlowMessageBO.getLogicalFlowNodeBo().getId(), true);
        amqpTemplate.convertAndSend(rabbitMQConfigurationProperties.getQueueExecuteScript(), JSON.toJSONString(logicalFlowMessageBO));
    }


    /**
     * 通过逻辑流程根节点构建逻辑流程树
     *
     * @param logicalFlowNodeBO 逻辑流程根节点
     */
    private LogicalFlowNodeBO processLogicalNodeList(LogicalFlowNodeBO logicalFlowNodeBO) {
        // 查询此逻辑流程的所有节点
        List<LogicalFlowNode> logicalFlowNodeList = logicalFlowNodeRepository.findByLogicalFlowId(logicalFlowNodeBO.getLogicalFlowId());

        // 生成节点ID及对应的子节点MAP
        Map<String, List<LogicalFlowNodeBO>> childrenMap = new HashMap<>();
        logicalFlowNodeList.forEach(logicalFlowNode -> {
            List<LogicalFlowNodeBO> logicalFlowNodeBOList;
            // key是否存在
            if (childrenMap.containsKey(logicalFlowNode.getParentId())) {
                logicalFlowNodeBOList = childrenMap.get(logicalFlowNode.getParentId());
            } else {
                logicalFlowNodeBOList = new ArrayList<>();
            }
            logicalFlowNodeBOList.add(new LogicalFlowNodeBO(logicalFlowNode, null));
            childrenMap.put(logicalFlowNode.getParentId(), logicalFlowNodeBOList);
        });

        // 生成树
        return buildLogicalFlowNodeTree(childrenMap);
    }

    /**
     * 子节点MAP生成节点树
     *
     * @param childrenMap   子节点MAP
     */
    private LogicalFlowNodeBO buildLogicalFlowNodeTree(Map<String, List<LogicalFlowNodeBO>> childrenMap) {
        List<LogicalFlowNodeBO> logicalFlowNodeBOS = childrenMap.get(DataConstants.LOGICAL_FLOW_NODE_ROOT_PARENT_ID);
        LogicalFlowNodeBO rootNode = logicalFlowNodeBOS.get(0);

        // 遍历树
        recursive(rootNode, childrenMap);

        return rootNode;
    }

    /**
     * 深度优先遍历节点
     *
     * @param parent        父节点
     * @param childrenMap   子节点MAP
     */
    private void recursive(LogicalFlowNodeBO parent, Map<String, List<LogicalFlowNodeBO>> childrenMap) {
        List<LogicalFlowNodeBO> childList = childrenMap.get(parent.getId());
        if (childList != null) {
            // 设置当前父节点的子节点
            parent.setChildren(childList);
            for (LogicalFlowNodeBO child: childList) {
                recursive(child, childrenMap); // 递归调用
            }
        }
    }

    /**
     * 校验父节点的所有子节点是否处于运行状态
     *
     * @param logicalFlowNodeBO 父节点
     * @return boolean
     */
    public Boolean checkRunning(LogicalFlowNodeBO logicalFlowNodeBO) {
        List<LogicalFlowNodeBO> logicalFlowNodeBOList = new ArrayList<>();
        // 获取所有的节点列表
        getNodeByParent(logicalFlowNodeBO, logicalFlowNodeBOList);
        // 校验5000次, 确保任务不会重复执行, 无需担心效率问题, 这个判断一次的耗时最多1毫秒
        int count = 5000;
        while (count > 0) {
//            long start = System.currentTimeMillis();
            for (LogicalFlowNodeBO flowNodeBO : logicalFlowNodeBOList) {
                if (logicalFlowRunningNode.hasKey(flowNodeBO.getId())
                        && (Boolean) logicalFlowRunningNode.get(flowNodeBO.getId())) {
                    return true;
                }
                if (logicalFlowContinuousRunningNode.hasKey(flowNodeBO.getId())
                        && (Boolean) logicalFlowContinuousRunningNode.get(flowNodeBO.getId())) {
                    return true;
                }
            }
            count --;
//            log.info("判断一次需要:" + (System.currentTimeMillis() - start));
        }
        return false;
    }

    /**
     * 从树结构获取平行的子节点列表
     *
     * @param logicalFlowNodeBO 父节点
     * @param list              保存子节点的LIST
     */
    public void getNodeByParent(LogicalFlowNodeBO logicalFlowNodeBO, List<LogicalFlowNodeBO> list) {
        List<LogicalFlowNodeBO> children = logicalFlowNodeBO.getChildren();
        if (children != null) {
            list.addAll(logicalFlowNodeBO.getChildren());
            for (LogicalFlowNodeBO child : children) {
                getNodeByParent(child, list);
            }
        }
    }


    /**
     * 按ID查询逻辑流程节点
     * @param id
     */
    public LogicalFlow findById(String id) {
        return  logicalFlowRepository.findOne(id);
    }

    /**
     * 查询所有的子逻辑流程
     *
     * @return List
     */
    public List<LogicalFlowBO> findChildFlowBOList() {
        return this.findAll().stream()
                .filter(logicalFlow -> !DataConstants.LOGICAL_FLOW_NODE_ROOT_PARENT_ID.equals(logicalFlow.getParentId()))
                .map(logicalFlow -> {
                    LogicalFlowBO logicalFlowBO = new LogicalFlowBO(logicalFlow, null);
                    logicalFlowBO.setLogicalFlowNodeBO(findLogicalFlowNodeBOByLogicalId(logicalFlowBO.getId()));
                    return logicalFlowBO;
                })
                .collect(Collectors.toList());
    }

    public void save(LogicalFlow logicalFlow) {
        logicalFlowRepository.save(logicalFlow);
    }
}
