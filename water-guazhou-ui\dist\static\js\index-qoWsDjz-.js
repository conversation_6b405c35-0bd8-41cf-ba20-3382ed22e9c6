import{d as k,r as y,c as m,bu as $,o as B,bB as H,bA as V,bv as _,C as D,g as l,h as a,F as n,ax as i,p as s,aw as M,G as u,bh as C,an as b,n as S,av as z,bV as A,J as E,L as F}from"./index-r0dFAfgr.js";const L=k({name:"SlDialog",props:{config:{type:Object,default:()=>{}}},emits:["close","heightChangeComplete"],setup(e,o){const f=y({headComponent:e.config.headComponent||null,scrollbar:e.config.scrollbar!==!1,confirmLoading:!1,visible:!0,title:e.config.title||"",width:e.config.width||"560px",contentHeight:e.config.contentHeight,contentMaxHeight:e.config.contentMaxHeight,confirm:{text:e.config.confirm&&e.config.confirm.text||"保 存",show:e.config.confirm&&e.config.confirm.show!==!1,handler:async()=>{e.config.confirm&&e.config.confirm.handler&&await e.config.confirm.handler()}},cancel:{text:e.config.cancel&&e.config.cancel.text?e.config.cancel.text:"关 闭",show:e.config.cancel&&e.config.cancel.show!==!1,handler:()=>c()},footerAlign:e.config.footerAlign||"right",modalClose:e.config.modalClose||!1,escapeClose:e.config.modalClose!==!1}),c=async()=>{e.config.cancel&&e.config.cancel.handler&&await e.config.cancel.handler(),o.emit("close")},d=m(null),r=m();return $(()=>{}),B(()=>{H(()=>{o.emit("heightChangeComplete")})}),V(()=>{}),{..._(f),dialog:d,refDialogFooter:r,handleColse:c}}}),N={class:"sl-dialog-content"},T={ref:"refDialogFooter",class:"sl-dialog__footer"};function U(e,o,f,c,d,r){const v=A,g=E,w=F;return l(),a(w,{ref:"dialog",modelValue:e.visible,"onUpdate:modelValue":o[0]||(o[0]=t=>e.visible=t),width:e.width,title:e.title,"close-on-click-modal":!1,"close-on-press-escape":!1,onClose:o[1]||(o[1]=()=>e.handleColse())},{header:n(()=>[i(e.$slots,"title",{},void 0,!0)]),footer:n(()=>{var t,h;return[s("div",T,[i(e.$slots,"footer",{},void 0,!0)],512),s("div",{class:M(["dialog-footer",e.footerAlign])},[e.cancel&&e.cancel.show!==!1?(l(),a(g,{key:0,size:"small",onClick:(t=e.cancel)==null?void 0:t.handler},{default:n(()=>[u(C(e.cancel.text),1)]),_:1},8,["onClick"])):b("",!0),e.confirm&&e.confirm.show?(l(),a(g,{key:1,size:"small",type:"primary",loading:e.confirmLoading,onClick:(h=e.confirm)==null?void 0:h.handler},{default:n(()=>[u(C(e.confirm.text),1)]),_:1},8,["loading","onClick"])):b("",!0)],2)]}),default:n(()=>[e.scrollbar?(l(),a(v,{key:0,ref:"scrollbar",height:e.contentHeight,"max-height":e.contentMaxHeight},{default:n(()=>[s("div",N,[i(e.$slots,"default",{},void 0,!0)])]),_:3},8,["height","max-height"])):(l(),S("div",{key:1,class:"sl-dialog-content",style:z("overflow:hidden;height:"+e.contentHeight)},[i(e.$slots,"default",{},void 0,!0)],4))]),_:3},8,["modelValue","width","title"])}const G=D(L,[["render",U],["__scopeId","data-v-1a4795d1"]]);export{G as _};
