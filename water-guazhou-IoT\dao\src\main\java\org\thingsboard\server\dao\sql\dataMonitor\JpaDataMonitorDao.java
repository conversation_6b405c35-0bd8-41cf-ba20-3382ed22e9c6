/**
 * Copyright © 2016-2019 The Thingsboard Authors
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.sql.dataMonitor;

import com.google.common.util.concurrent.ListenableFuture;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Component;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.dataMonitor.DataMonitor;
import org.thingsboard.server.common.data.energy.Energy;
import org.thingsboard.server.common.data.id.DeviceId;
import org.thingsboard.server.common.data.id.EnergyId;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.DaoUtil;
import org.thingsboard.server.dao.dataMonitor.DataMonitorDao;
import org.thingsboard.server.dao.energy.EnergyDao;
import org.thingsboard.server.dao.model.sql.DataMonitorEntity;
import org.thingsboard.server.dao.model.sql.EnergyEntity;
import org.thingsboard.server.dao.sql.JpaAbstractSearchTextDao;
import org.thingsboard.server.dao.util.SqlDao;

import java.util.List;

@Component
@SqlDao
public class JpaDataMonitorDao extends JpaAbstractSearchTextDao<DataMonitorEntity, DataMonitor> implements DataMonitorDao {

    @Autowired
    private DataMonitorRepository dataMonitorRepository;

    @Override
    protected Class<DataMonitorEntity> getEntityClass() {
        return DataMonitorEntity.class;
    }

    @Override
    protected CrudRepository<DataMonitorEntity, String> getCrudRepository() {
        return dataMonitorRepository;
    }

    @Override
    public ListenableFuture<List<DataMonitor>> findDataMonitorByTenant(TenantId tenantId) {
        return service.submit(() -> DaoUtil.convertDataList(dataMonitorRepository.findByTenantId(UUIDConverter.fromTimeUUID(tenantId.getId()))));
    }

    @Override
    public ListenableFuture<List<DataMonitor>> findDataMonitorByTenantAndTime(TenantId tenantId, long start, long end) {
        return service.submit(() -> DaoUtil.convertDataList(dataMonitorRepository.findByTenantIdAndtime(UUIDConverter.fromTimeUUID(tenantId.getId()),start,end)));
    }

    @Override
    public ListenableFuture<List<DataMonitor>> findDataMonitorByDeviceAndTime(DeviceId deviceId, long start, long end) {
        return service.submit(() -> DaoUtil.convertDataList(dataMonitorRepository.findByDeviceIdAndtime(UUIDConverter.fromTimeUUID(deviceId.getId()),start,end)));
    }
}
