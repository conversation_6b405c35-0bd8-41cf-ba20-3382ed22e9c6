import { GetMenuSourceAll } from '@/api/menu/source';
import { formatTree } from '@/utils/GlobalHelper';

export const useSourceOptions = () => {
  const options = ref<NormalOption[]>([]);
  const init = async () => {
    try {
      const res = await GetMenuSourceAll();
      options.value =
        formatTree(res.data, {
          id: 'id',
          label: 'name',
          value: 'url',
          children: 'children'
        }) || [];
    } catch (error) {
      options.value = [];
    }
  };
  return {
    init,
    options
  };
};
