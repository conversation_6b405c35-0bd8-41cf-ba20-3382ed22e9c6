import{I as o,d as t}from"./icon-vUORPQEt.js";import"./widget-BcWKanF2.js";import"./Point-WxyopZva.js";import"./index-r0dFAfgr.js";import"./pe-B8dP0-Ut.js";import"./observers-D10wq1Ib.js";/*!
 * All material copyright ESRI, All Rights Reserved, unless otherwise specified.
 * See https://github.com/Esri/calcite-components/blob/master/LICENSE.md for details.
 * v1.0.8-next.4
 */const s=o,c=t;export{s as CalciteIcon,c as defineCustomElement};
