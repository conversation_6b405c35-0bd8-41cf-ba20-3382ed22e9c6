<!-- 总分表差值评估 -->
<template>
  <TreeBox>
    <template #tree>
      <SLTree
        ref="refTree"
        :tree-data="TreeData"
      ></SLTree>
    </template>
    <CardSearch
      ref="refSearch"
      :config="SearchConfig"
    ></CardSearch>
    <CardTable
      ref="refTable"
      class="card-table"
      :config="TableConfig"
    ></CardTable>
    <NewOrder
      ref="refDialog"
      :default-values="{ partitionId: TableConfig.selectList?.[0]?.partitionId }"
    ></NewOrder>
  </TreeBox>
</template>
<script lang="ts" setup>
import { ICardTableIns, ISearchIns } from '@/components/type'
import { SLMessage } from '@/utils/Message'
import { usePartition } from '@/hooks/arcgis'
import {
  ExportDMATotalDifferList,
  GetDMATotalDifferList
} from '@/api/mapservice/dma'
import { formatterDate, formatterMonth } from '@/utils/GlobalHelper'
import NewOrder from '../components/NewOrder.vue'
import { ExportReport } from '@/views/yinshou/baobiao'

const refTable = ref<ICardTableIns>()
const refSearch = ref<ISearchIns>()
const TreeData = reactive<SLTreeConfig>({
  data: [],
  loading: true,
  title: '选择分区',
  expandOnClickNode: false,
  treeNodeHandleClick: (data: NormalOption) => {
    if (TreeData.currentProject !== data) {
      // TreeData.loading = true
      TreeData.currentProject = data
      refreshData()
    }
  }
})
const handleHidden = (params, query, config) => {
  config.hidden = params.type !== config.field
}
// 列表模式搜索配置
const SearchConfig = reactive<ISearch>({
  defaultParams: {
    month: moment().format(formatterMonth),
    day: [
      moment().subtract(1, 'M').format(formatterDate),
      moment().format(formatterDate)
    ],
    type: 'month'
  },
  filters: [
    {
      type: 'radio-button',
      label: '选择方式',
      field: 'type',
      options: [
        { label: '按日期', value: 'day' },
        { label: '按月', value: 'month' }
      ]
    },
    {
      type: 'month',
      label: '',
      field: 'month',
      handleHidden
    },
    {
      type: 'daterange',
      label: '',
      field: 'day',
      handleHidden
    },
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          iconifyIcon: 'ep:search',
          disabled: () => !!TableConfig.loading,
          click: () => refreshData()
        },
        {
          type: 'default',
          perm: true,
          text: '重置',
          iconifyIcon: 'ep:refresh',
          click: () => {
            refSearch.value?.resetForm()
          }
        },
        {
          perm: true,
          text: '工单',
          type: 'success',
          iconifyIcon: 'ep:plus',
          click: () => {
            if (!TableConfig.selectList?.length) {
              SLMessage.warning('请选择一条数据')
            } else if (TableConfig.selectList.length > 1) {
              SLMessage.warning('只能选择一条数据')
            } else {
              refDialog.value?.openDialog()
            }
          }
        },
        {
          perm: true,
          type: 'warning',
          text: '导出',
          iconifyIcon: 'ep:download',
          disabled: () => !!TableConfig.loading,
          click: () => {
            refreshData(true)
          }
        }
      ]
    }
  ]
})

// 列表
const TableConfig = reactive<ITable>({
  dataList: [],
  indexVisible: true,
  rowKey: 'partitionId',
  columns: [
    { prop: 'partitionName', label: '分区名称', minWidth: 180 },
    {
      prop: 'name2',
      label: '抄表情况',
      minWidth: 120,
      align: 'center',
      subColumns: [
        { prop: 'needCopyNum', label: '应抄户', minWidth: 120, sortable: true },
        { prop: 'realCopyNum', label: '实抄户', minWidth: 120, sortable: true },
        { prop: 'notCopyNum', label: '未抄户', minWidth: 120, sortable: true },
        {
          prop: 'copyRate',
          label: '抄表率',
          unit: '(%)',
          minWidth: 120,
          sortable: true
        }
      ]
    },
    {
      prop: 'name3',
      label: '实抄水量分析',
      minWidth: 120,
      align: 'center',
      subColumns: [
        {
          prop: 'supplyTotal',
          label: '供水量(m³)',
          minWidth: 160,
          sortable: true
        },
        {
          prop: 'realCopyWater',
          label: '实抄水量(m³)',
          minWidth: 180,
          sortable: true
        },
        {
          prop: 'nrwWater',
          label: '产销差水量(m³)',
          minWidth: 180,
          sortable: true
        },
        {
          prop: 'nrwRate',
          label: '产销差',
          unit: '(%)',
          minWidth: 140,
          sortable: true
        }
      ]
    },
    {
      prop: 'name4',
      label: '校准水量分析',
      minWidth: 120,
      align: 'center',
      subColumns: [
        {
          prop: 'correctSupplyTotal',
          label: '校准供水量(m³)',
          minWidth: 180,
          sortable: true
        },
        {
          prop: 'correctCopyWater',
          label: '校准用水量(m³)',
          minWidth: 180,
          sortable: true
        },
        {
          prop: 'correctNrwWater',
          label: '校准产销差水量(m³)',
          minWidth: 180,
          sortable: true
        },
        {
          prop: 'correctNrwRate',
          label: '校准产销差',
          unit: '(%)',
          minWidth: 160,
          sortable: true
        }
      ]
    },
    {
      prop: 'name5',
      label: '表观漏失分析',
      align: 'center',
      subColumns: [
        {
          prop: 'referenceLossWater',
          label: '参考漏失水量(m³)',
          minWidth: 200,
          sortable: true
        },
        {
          prop: 'faceLossWater',
          label: '表观漏失水量(m³)',
          minWidth: 200,
          sortable: true
        }
      ]
    }
  ],
  singleSelect: true,

  handleSelectChange(val) {
    TableConfig.selectList = val
  },
  select(row) {
    if (
      TableConfig.selectList?.length
      && TableConfig.selectList.findIndex(
        item => item.partitionId === row.partitionId
      ) !== -1
    ) {
      TableConfig.selectList = []
    } else {
      TableConfig.selectList = [row]
    }
  },
  pagination: {
    hide: true,
    refreshData({ page, size }) {
      TableConfig.pagination.page = page || 1
      TableConfig.pagination.limit = size || 20
      refreshData()
    }
  }
})
const refDialog = ref<InstanceType<typeof NewOrder>>()
// 数据获取
const refreshData = async (isExport?: boolean) => {
  if (!TreeData.currentProject) {
    SLMessage.warning('请选择分区')
    return
  }
  TableConfig.loading = true
  try {
    const query = refSearch.value?.queryParams || {}
    const params = {
      date: query.type === 'month' ? query.month : undefined,
      partitionId: TreeData.currentProject?.value,
      type: query.type,
      start: query.type === 'day' ? query.day?.[0] : undefined,
      end: query.type === 'day' ? query.day?.[1] : undefined
    }
    if (isExport) {
      const res = await ExportDMATotalDifferList(params)
      ExportReport(res.data, '总分表差评估')
    } else {
      const res = await GetDMATotalDifferList(params)
      TableConfig.dataList = res.data?.data || []
    }
  } catch (error) {
    console.log(error)
  }
  TableConfig.loading = false
}
const partition = usePartition()
onMounted(async () => {
  await partition.getTree()
  TreeData.data = partition.Tree.value
  TreeData.currentProject = TreeData.data[0]
  refreshData()
})
</script>
<style lang="scss" scoped>
.wrapper-content {
  height: 100%;
}
</style>
