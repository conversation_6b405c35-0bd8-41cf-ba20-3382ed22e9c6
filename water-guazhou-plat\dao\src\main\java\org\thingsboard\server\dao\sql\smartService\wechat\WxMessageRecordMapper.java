package org.thingsboard.server.dao.sql.smartService.wechat;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.smartService.wechat.WxMessageRecord;
import org.thingsboard.server.dao.util.imodel.query.smartService.wechat.WxMessageRecordPageRequest;

@Mapper
public interface WxMessageRecordMapper extends BaseMapper<WxMessageRecord> {
    IPage<WxMessageRecord> findByPage(WxMessageRecordPageRequest request);

    boolean update(WxMessageRecord entity);

    boolean save(WxMessageRecord entity);

    boolean markAsComplete(String recordId);

    WxMessageRecord getLatestByTemplateId(@Param("templateId") String templateId);
}
