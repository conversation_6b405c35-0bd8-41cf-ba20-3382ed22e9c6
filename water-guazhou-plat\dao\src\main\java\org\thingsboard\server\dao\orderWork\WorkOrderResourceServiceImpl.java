package org.thingsboard.server.dao.orderWork;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrderResource;
import org.thingsboard.server.dao.sql.workOrder.WorkOrderResourceMapper;

import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class WorkOrderResourceServiceImpl implements WorkOrderResourceService {

    @Autowired
    private WorkOrderResourceMapper workOrderResourceMapper;

    @Override
    public PageData<WorkOrderResource> findList(int page, int size, String status, TenantId tenantId) {
        Page<WorkOrderResource> pageRequest = new Page<>(page, size);
        IPage<WorkOrderResource> pageResult = workOrderResourceMapper.findList(pageRequest, status, UUIDConverter.fromTimeUUID(tenantId.getId()));
        return new PageData<>(pageResult.getTotal(), pageResult.getRecords());
    }

    @Override
    public void save(WorkOrderResource entity) {
        if (StringUtils.isBlank(entity.getId())) {
            entity.setCreateTime(new Date());
            entity.setStatus("1");

            workOrderResourceMapper.insert(entity);
        } else {
            workOrderResourceMapper.updateById(entity);
        }
    }

    @Override
    public void changeStatus(String status, String id) {
        WorkOrderResource resource = workOrderResourceMapper.selectById(id);
        if (resource != null) {
            resource.setStatus(status);
            workOrderResourceMapper.updateById(resource);
        }
    }

    @Override
    public List<WorkOrderResource> findAll(String status, TenantId tenantId) {
        QueryWrapper<WorkOrderResource> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("tenant_id", UUIDConverter.fromTimeUUID(tenantId.getId())).eq("status", status);
        return workOrderResourceMapper.selectList(queryWrapper);
    }
}
