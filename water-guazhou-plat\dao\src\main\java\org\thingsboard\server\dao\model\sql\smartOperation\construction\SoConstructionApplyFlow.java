package org.thingsboard.server.dao.model.sql.smartOperation.construction;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseUsername;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.util.Date;

@Getter
@Setter
@ResponseEntity
@TableName("so_construction_apply_flow")
public class SoConstructionApplyFlow {
    // id
    private String id;

    // 工作编号
    private String code;

    // 所属工程实施编号
    private String constructionApplyCode;

    // 工作名称
    private String workName;

    // 阶段
    private String workStage;

    // 开始时间
    private Date beginTime;

    // 完成时间
    private Date endTime;

    // 负责人
    private String headUser;

    // 负责人电话
    private String headUserPhone;

    // 工作内容
    private String workContent;

    // 说明
    private String remark;

    // 创建人
    @ParseUsername
    private String creator;

    // 创建时间
    private Date createTime;

    // 客户id
    private String tenantId;

}
