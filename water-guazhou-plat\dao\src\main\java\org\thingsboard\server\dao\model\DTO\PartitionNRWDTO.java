package org.thingsboard.server.dao.model.DTO;

import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 分区产销差
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2023-04-23
 */
@Data
public class PartitionNRWDTO {

    private String partitionId;

    private String partitionName;

    private BigDecimal nrw;

    private String date;

    private Integer userNum;

    private BigDecimal supplyTotal;

    private BigDecimal correctUseWater;

    private BigDecimal inWater;

    private BigDecimal outWater;

    private BigDecimal lossTotal;

    private List<PartitionNRWDTO> subPartitionNRWDTOList = new ArrayList<>();

}
