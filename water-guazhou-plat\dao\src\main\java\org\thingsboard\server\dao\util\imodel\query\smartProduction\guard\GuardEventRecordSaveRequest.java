package org.thingsboard.server.dao.util.imodel.query.smartProduction.guard;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartProduction.guard.GuardEventRecord;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;

@Getter
@Setter
public class GuardEventRecordSaveRequest extends SaveRequest<GuardEventRecord> {
    // 所属日志id
    private String recordId;

    // 事件内容
    private String content;


    @Override
    protected GuardEventRecord build() {
        GuardEventRecord entity = new GuardEventRecord();
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    @Override
    protected GuardEventRecord update(String id) {
        GuardEventRecord entity = new GuardEventRecord();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    @Override
    protected boolean allowUpdate() {
        return false;
    }

    private void commonSet(GuardEventRecord entity) {
        entity.setRecordId(recordId);
        entity.setContent(content);
        entity.setUserId(currentUserUUID());
        entity.setCreateTime(createTime());
    }

}