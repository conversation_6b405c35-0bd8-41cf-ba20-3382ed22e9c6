<!-- 按口径统计阀门 -->
<template>
  <RightDrawerMap
    ref="refMap"
    :title="'按口径统计阀门'"
    :full-content="true"
    @map-loaded="onMapLoaded"
  >
    <Form
      ref="refForm"
      :config="FormConfig"
    ></Form>
    <template #detail-header>
      <span>统计结果</span>
    </template>
    <template #detail-default>
      <StatisticsCharts
        ref="refStatisticsCharts"
        :view="staticState.view"
        :layer-ids="state.layerIds"
        :query-params="{
          where: '1=1',
          geometry: staticState.graphics?.geometry
        }"
        :statistics-params="{
          group_fields: ['DIAMETER'],
          statistic_field: EStatisticField.OBJECTID,
          statistic_type: '1'
        }"
        :tabs="state.tabs"
        :unit="'个'"
        :prefix="'DN'"
        @detail-refreshed="state.loading = false"
        @attr-row-click="handleAttrRowClick"
        @bar-click="handleChartClick"
        @ring-click="handleChartClick"
        @total-row-click="handleTotalRowClick"
      ></StatisticsCharts>
    </template>
  </RightDrawerMap>
</template>
<script lang="ts" setup>
import { queryLayerClassName } from '@/api/mapservice'
import { useSketch } from '@/hooks/arcgis'
import {
  EStatisticField,
  // extentTo,
  getGraphicLayer,
  getLayerOids,
  getSubLayerIds
} from '@/utils/MapHelper'
import { SLMessage } from '@/utils/Message'
import RightDrawerMap from '../../components/common/RightDrawerMap.vue'
import StatisticsCharts from '../../components/components/StatisticsCharts.vue'

const refMap = ref<InstanceType<typeof RightDrawerMap>>()
const refForm = ref<IFormIns>()

const state = reactive<{
  tabs: any[]
  loading: boolean
  layerInfos: any[]
  layerIds: any[]
}>({
  tabs: [],
  layerInfos: [],
  layerIds: [],
  loading: false
})
const staticState: {
  view?: __esri.MapView
  graphics?: __esri.Graphic
  graphicsLayer?: __esri.GraphicsLayer
  sketch?: __esri.SketchViewModel
} = {}
const FormConfig = reactive<IFormConfig>({
  group: [
    {
      fieldset: {
        desc: '绘制工具'
      },
      fields: [
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              title: '绘制多边形',
              disabled: () => state.loading,
              iconifyIcon: 'mdi:shape-polygon-plus',
              click: () => initDraw('polygon')
            },
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              title: '绘制矩形',
              disabled: () => state.loading,
              iconifyIcon: 'ep:crop',
              click: () => initDraw('rectangle')
            },
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              title: '绘制圆形',
              disabled: () => state.loading,
              iconifyIcon: 'mdi:ellipse-outline',
              click: () => initDraw('circle')
            },
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              title: '清除图形',
              disabled: () => state.loading,
              iconifyIcon: 'ep:delete',
              click: () => clearGraphicsLayer()
            }
          ]
        },
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: '统计',
              styles: {
                width: '100%'
              },
              loading: () => state.loading,
              click: () => startQuery()
            }
          ]
        }
      ]
    }
  ],
  labelPosition: 'top',
  gutter: 12
})
const handleTotalRowClick = (row?: any, tab?: string) => {
  const diameter = row.label
  refreshDetailTable(diameter, tab, true)
}
const handleAttrRowClick = (row: any, tab?: string) => {
  const DIAMETER = row?.label
  refreshDetailTable(DIAMETER, tab)
}
const handleChartClick = (params?: any, tab?: string) => {
  const DIAMETER = params?.name
  refreshDetailTable(DIAMETER, tab)
}
const refreshDetailTable = async (DIAMETER?: string, tab?: string, showAll?: boolean) => {
  if (DIAMETER === undefined) return
  const d = DIAMETER.replace(/[a-z][A-Z]/gi, '')
  const sql = d === '合计' ? '1=1' : d === '--' ? 'DIAMETER is null' : "DIAMETER='" + d + "'"
  await startQuery(sql, tab)
  const curTab = refStatisticsCharts.value?.getCurLayer()
  refStatisticsCharts.value?.refreshDetail(curTab, showAll ?? true)
}
const initDraw = (type: any) => {
  if (!staticState.view) return
  clearGraphicsLayer()
  staticState.sketch?.create(type)
}
const clearGraphicsLayer = () => {
  staticState.graphicsLayer?.removeAll()
  staticState.graphics = undefined
}

const getLayerInfo = async () => {
  state.layerIds = getSubLayerIds(staticState.view, undefined, undefined, '阀门')
  const layerInfo = await queryLayerClassName(state.layerIds)
  state.layerInfos = layerInfo.data?.result?.rows || []
}
const refStatisticsCharts = ref<InstanceType<typeof StatisticsCharts>>()
const startQuery = async (sql?: string, tab?: string) => {
  SLMessage.info('正在统计，请稍候...')
  try {
    state.loading = true
    if (!state.layerIds.length) {
      SLMessage.warning('当前没有阀门')
    } else {
      const layerIds = tab === undefined
        ? state.layerIds
        : state.layerInfos.filter(item => item.layername === tab).map(item => item.layerid)
      const tabs = await getLayerOids(layerIds, state.layerInfos, {
        where: sql || '1=1',
        geometry: staticState.graphics?.geometry
      })
      if (tab !== undefined) {
        if (state.tabs.length) {
          const tabObj = state.tabs.find(item => item.name === tab)
          const newTab = tabs.find(item => item.name === tab)
          tabObj && (tabObj.data = newTab?.data)
          refStatisticsCharts.value?.refreshTable(tab, layerIds?.[0])
        } else {
          state.tabs = tabs
        }
      } else {
        state.tabs = tabs
      }
      const opend = refMap.value?.isCustomOpened()
      if (!opend) {
        refMap.value?.toggleCustomDetail(true)
      }
      // await refMap.value?.refreshDetail(tabs)
      // extentTo(
      //   staticState.view,
      //   staticState.graphics?.geometry?.extent || staticState.view?.extent,
      //   true
      // )
    }
  } catch (error) {
    console.log(error)
    SLMessage.error('统计失败')
  }
  state.loading = false
}
const { initSketch, destroySketch } = useSketch()
const resolveDrawEnd = (result: ISketchHandlerParameter) => {
  staticState.graphics = result.graphics[0]
}
const onMapLoaded = view => {
  staticState.view = view
  staticState.graphicsLayer = getGraphicLayer(staticState.view, {
    id: 'search-valve-diameter',
    title: '按口径统计阀门'
  })
  staticState.sketch = initSketch(staticState.view, staticState.graphicsLayer, {
    createCallBack: resolveDrawEnd,
    updateCallBack: resolveDrawEnd
  })
  getLayerInfo()
}
onBeforeUnmount(() => {
  destroySketch()
  staticState.graphicsLayer?.removeAll()
  staticState.graphicsLayer?.destroy()
})
</script>
<style lang="scss" scoped></style>
