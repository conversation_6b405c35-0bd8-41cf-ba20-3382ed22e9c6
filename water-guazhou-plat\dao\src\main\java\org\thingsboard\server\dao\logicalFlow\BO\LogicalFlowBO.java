package org.thingsboard.server.dao.logicalFlow.BO;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.thingsboard.server.dao.model.sql.LogicalFlow;

/**
 * 逻辑流程 业务对象
 */
@Data
@ToString
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class LogicalFlowBO extends LogicalFlow {
    private LogicalFlowNodeBO logicalFlowNodeBO;

    public LogicalFlowBO(LogicalFlow logicalFlow, LogicalFlowNodeBO logicalFlowNodeBO) {


        super.setId(logicalFlow.getId());
        super.setName(logicalFlow.getName());
        super.setType(logicalFlow.getType());
        super.setRemark(logicalFlow.getRemark());
        super.setCreater(logicalFlow.getCreater());
        super.setCreateTime(logicalFlow.getCreateTime());
        super.setAdditionalInfo(logicalFlow.getAdditionalInfo());
        super.setParentId(logicalFlow.getParentId());
        this.logicalFlowNodeBO = logicalFlowNodeBO;
    }
}
