import{d as k,c as h,r as b,a8 as g,b as d,bu as x,g as N,h as M,F as D,q,i as T,a1 as R,_ as $}from"./index-r0dFAfgr.js";import{g as y}from"./MapView-DaoQedLH.js";import{w as B}from"./Point-WxyopZva.js";import{a as w,s as u,g as E}from"./FeatureHelper-Da16o0mu.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import{g as G,b as V}from"./LayerHelper-Cn-iiqxI.js";import{a as W,i as J}from"./QueryHelper-ILO3qZqg.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";import{G as z,a as H,b as K}from"./area-Bpl-8n1R.js";import{A as Q,G as j}from"./plan-BLf3nu6_.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./geometryEngineBase-BhsKaODW.js";import U from"./RightDrawerMap-D5PhmGFO.js";import{g as X}from"./circuitSettings-CHqJCF5w.js";import"./widget-BcWKanF2.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./project-DUuzYgGl.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./pipe-nogVzCHG.js";import"./executeForIds-BLdIsxvI.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./ArcView-DpMnCY82.js";import"./ViewHelper-BGCZjxXH.js";import"./fieldconfig-Bk3o1wi7.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./ArcStationWarning-BF9YrSzF.js";/* empty css                         */import"./useWaterPoint-Bv0z6ym6.js";import"./DateFormatter-Bm9a68Ax.js";import"./zhandian-YaGuQZe6.js";import"./useStation-DJgnSZIA.js";import"./DrawerBox-CLde5xC8.js";import"./SideDrawer-CBntChyn.js";import"./PipeDetail-CTBPYFJW.js";import"./Search-NSrhrIa_.js";import"./FormTableColumnFilter-BT7pLXIC.js";import"./config-fy91bijz.js";import"./ArcBRTools-BO92yznB.js";import"./PipeLength.vue_vue_type_script_setup_true_lang-BZfUsvDf.js";import"./arcWidgetButton-0glIxrt7.js";import"./StatisticsHelper-D-s_6AyQ.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./wfsUtils-DXofo3da.js";import"./usePipelineGroup-Ba1pmWz_.js";import"./GroupLayer-DhhN3FyN.js";import"./lazyLayerLoader-DbM9sT1W.js";import"./WFSLayer-1TtJp3nx.js";import"./clientSideDefaults-VQhQaYxh.js";import"./QueryEngineCapabilities-Dk3NJwmm.js";import"./wfsUtils-Du031XaC.js";import"./geojson-MBFu2-HZ.js";import"./xmlUtils-CtUoQO7q.js";import"./ListWindow-WS05QqV0.js";import"./PopLayout-BP55MvL7.js";import"./PoiSearchV2-D7yNeLuv.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./URLHelper-B9aplt5w.js";import"./useLayerList-DmEwJ-ws.js";import"./useScaleBar-Beed-z91.js";const or=k({__name:"InspectPlan",setup(Y){const c=h(),P=h(),e={deviceGraphics:[]},n=b({tabs:[],loading:!1,districtOptions:[],layerOptions:[],planPeriod:[],circuitSettingsOptions:[]}),v=b({labelWidth:120,group:[{fields:[{type:"select-tree",field:"districtAreaId",label:"区域",defaultExpandAll:!0,options:g(()=>n.districtOptions),rules:[{required:!0,message:"请选择区域"}],nodeClick:t=>C(t)},{type:"select",label:"设备类别",field:"devices",multiple:!0,options:g(()=>n.layerOptions.filter(t=>t.data.geometrytype!=="esriGeometryPolyline")),onChange:()=>_()},{type:"radio",label:"是否常规计划",field:"isNormalPlan",rules:[{required:!0,message:"请选择是否常规计划"}],options:[{label:"常规",value:"true"},{label:"临时",value:"false"}]},{type:"radio",label:"是否需要反馈",field:"isNeedFeedback",rules:[{required:!0,message:"请选择是否需要反馈"}],options:[{label:"需要",value:"true"},{label:"仅到位",value:"false"}]},{type:"radio",label:"进行方式",field:"moveType",rules:[{required:!0,message:"请选择进行方式"}],options:[{label:"车巡",value:"车巡"},{label:"步行",value:"步行"}]},{type:"select",label:"计划周期",field:"planCircle",options:g(()=>n.planPeriod),rules:[{required:!0,message:"请选择计划周期"}]},{type:"input",label:"计划名称",field:"name",rules:[{required:!0,message:"请输入计划名称"}]},{type:"select",label:"巡检配置",field:"inspectionConfigId",placeholder:"请选择巡检配置（可选）",options:g(()=>n.circuitSettingsOptions),clearable:!0},{type:"textarea",label:"备注",field:"remark"},{type:"btn-group",btns:[{perm:!0,text:"重置",type:"default",styles:{marginLeft:"auto"},click:()=>{var t,i,o;(t=c.value)==null||t.resetForm(),(i=e.graphicsLayer)==null||i.removeAll(),(o=e.deviceLayer)==null||o.removeAll()}},{perm:!0,text:"确定",click:()=>{var t;return(t=c.value)==null?void 0:t.Submit()}}]}]}],labelPosition:"right",gutter:12,defaultValue:{isNormalPlan:"true",isNeedFeedback:"false",moveType:"车巡",planCircle:"",devices:[],inspectionConfigId:""},submit:t=>{const i=e.deviceGraphics.map(r=>r.attributes),o={...t,specialDevices:[],devices:i};Q(o).then(r=>{var p,a,l;r.data.code===200?(d.success(r.data.message),(p=c.value)==null||p.resetForm(),(a=e.graphicsLayer)==null||a.removeAll(),(l=e.deviceLayer)==null||l.removeAll(),e.graphic=void 0,e.deviceGraphics=[]):d.error(r.data.message)}).catch(r=>{d.error(r||"系统错误")})}}),C=t=>{var i,o;t.data.layer===2&&((i=e.graphicsLayer)==null||i.removeAll(),(o=e.deviceLayer)==null||o.removeAll(),z(t.value).then(r=>{var l,m;const p=t.data.type==="区域"?"polygon":"polyline";if(!r.data.data)return;const a=JSON.parse(r.data.data);if(a.geometry){const s=w(p,p==="polygon"?a.geometry.rings:a.geometry.paths,a.geometry.spatialReference);e.graphic=new y({geometry:s,symbol:u(p)}),(l=e.graphicsLayer)==null||l.add(e.graphic),E(e.view,e.graphic,{avoidHighlight:!0})}if(a.bufferGeometry){const s=w("polygon",a.bufferGeometry.rings,a.bufferGeometry.spatialReference),f=new y({geometry:s,symbol:u((s==null?void 0:s.type)||"polygon",{color:[0,255,0,.1],outlineWidth:1,outlineColor:"#00ff00"})});(m=e.graphicsLayer)==null||m.add(f),e.graphic=f}}),L(t.value))},L=t=>{H({areaId:t}).then(i=>{const o=i.data.data;o==null||o.data.map(r=>{var m,s;const p=new B({longitude:r.lon,latitude:r.lat,spatialReference:(m=e.view)==null?void 0:m.spatialReference}),a=new y({geometry:p,symbol:u("point",{outlineWidth:1,outlineColor:"#00ffff",color:"#ffffff"})}),l=new y({geometry:p,symbol:u("text",{text:r.name})});(s=e.graphicsLayer)==null||s.addMany([a,l])})}).catch(i=>{console.log(i.message)})},_=()=>{var i,o;if(!e.graphic){d.warning("请先选择区域"),c.value&&(c.value.dataForm.devices=[]);return}(i=e.deviceLayer)==null||i.removeAll(),e.deviceGraphics=[],((o=c.value)==null?void 0:o.dataForm.devices).map(r=>{var p;W(window.SITE_CONFIG.GIS_CONFIG.gisService+window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService+"/"+r,J({where:"1=1",geometry:(p=e.graphic)==null?void 0:p.geometry})).then(a=>{var l;e.deviceGraphics.push(...a.features),a.features.map(m=>{var s;m.symbol=u(m.geometry.type,{color:[255,0,0,.3],outlineColor:[255,0,0,1],outlineWidth:1,size:15}),m.attributes={serialId:m.attributes.OBJECTID,name:(s=n.layerOptions.find(f=>f.value===r))==null?void 0:s.label}}),(l=e.deviceLayer)==null||l.addMany(a.features)})})},O=async()=>{n.layerOptions=await V(e.view)},I=()=>{K().then(t=>{const i=t.data.data;n.districtOptions=R([i])})},F=()=>{j().then(t=>{var i;v.defaultValue&&(v.defaultValue.planCircle=0),(i=c.value)==null||i.resetForm(),n.planPeriod=t.data.data.map((o,r)=>({label:o,value:r,id:r}))})},S=async()=>{var t,i;try{const o=await X({page:1,size:100,status:"0"});(i=(t=o.data)==null?void 0:t.data)!=null&&i.data&&(n.circuitSettingsOptions=o.data.data.data.map(r=>({label:`${r.name} (${r.code})`,value:r.id,id:r.id,data:r})))}catch(o){console.error("获取巡检配置失败:",o),d.error("获取巡检配置失败")}},A=async t=>{e.view=t,e.graphicsLayer=G(e.view,{id:"area-keypoints",title:"区域信息"}),e.deviceLayer=G(e.view,{id:"device-points",title:"设备"}),await O()};return x(()=>{F(),I(),S()}),(t,i)=>{const o=$;return N(),M(U,{ref_key:"refMap",ref:P,title:"计划制定","right-drawer-width":500,onMapLoaded:A},{default:D(()=>[q(o,{ref_key:"refForm",ref:c,config:T(v)},null,8,["config"])]),_:1},512)}}});export{or as default};
