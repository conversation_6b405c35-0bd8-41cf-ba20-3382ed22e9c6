package org.thingsboard.server.dao.util.imodel.query.smartService.portal;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartService.portal.SsPortalNews;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;

@Getter
@Setter
public class SsPortalNewsPageRequest extends AdvancedPageableQueryEntity<SsPortalNews, SsPortalNewsPageRequest> {
    // 所属目录id
    private String packageId;

    // 标题
    private String title;

    // 是否发布
    private Boolean active;

    // 新闻类型 1-热点 2-推荐
    private Integer type;

}
