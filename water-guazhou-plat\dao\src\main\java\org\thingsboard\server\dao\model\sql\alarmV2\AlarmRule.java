package org.thingsboard.server.dao.model.sql.alarmV2;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * 报警Version2 报警规则
 */
@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.V2_TB_ALARM_RULE_TABLE)
@TableName(ModelConstants.V2_TB_ALARM_RULE_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class AlarmRule implements Serializable {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.TYPE)
    private String type;

    @Column(name = ModelConstants.TITLE_PROPERTY)
    private String title;

    @Column(name = ModelConstants.V2_TB_ALARM_RULE_STATION_ID)
    private String stationId;

    @Column(name = ModelConstants.V2_TB_ALARM_RULE_STATION_ATTR_ID)
    private String stationAttrId;

    @Column(name = ModelConstants.V2_TB_ALARM_RULE_RULE_TYPE)
    private String ruleType;

    @Column(name = ModelConstants.V2_TB_ALARM_RULE_RULE_PARAM)
    private String ruleParam;

    @Column(name = ModelConstants.V2_TB_ALARM_RULE_STATUS)
    private String status;

    @Column(name = ModelConstants.V2_TB_ALARM_RULE_ALARM_LEVEL)
    private String alarmLevel;

    @Column(name = ModelConstants.V2_TB_ALARM_RULE_ALARM_TYPE)
    private String alarmType;

    @Column(name = ModelConstants.V2_TB_ALARM_RULE_PROCESS_METHOD)
    private String processMethod;

    @Column(name = ModelConstants.V2_TB_ALARM_RULE_REMOTE_STATION_ATTR_ID)
    private String remoteStationAttrId;

    @Column(name = ModelConstants.V2_TB_ALARM_RULE_REMOTE_VIDEO_ID)
    private String remoteVideoId;

    @Column(name = ModelConstants.V2_TB_ALARM_RULE_REMOTE_RE_ALARM_VALUE)
    private Integer reAlarmValue;

    @Column(name = ModelConstants.V2_TB_ALARM_RULE_REMOTE_RE_ALARM_TYPE)
    private String reAlarmType;

    @Column(name = ModelConstants.V2_TB_ALARM_RULE_REMOTE_RE_ALARM_UNIT)
    private String reAlarmUnit;

    @Column(name = ModelConstants.V2_TB_ALARM_RULE_SEND_WAY)
    private String sendWay;

    @Column(name = ModelConstants.CREATE_TIME)
    private Date createTime;

    @Column(name = ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;

    @Transient
    @TableField(exist = false)
    private String stationAttrName;

    @Transient
    @TableField(exist = false)
    private String stationName;

    @Transient
    @TableField(exist = false)
    private JSONObject ruleParamObj;

}
