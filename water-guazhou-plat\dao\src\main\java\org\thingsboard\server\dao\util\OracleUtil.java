package org.thingsboard.server.dao.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.sql.*;

/**
 * Oracle连接工具
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2021-10-28
 */
@Component
@Slf4j
public class OracleUtil {
    @Value("${oracle.url}")
    private String url;

    @Value("${oracle.user}")
    private String user;

    @Value("${oracle.password}")
    private String password;

    public Connection getConnection() {
        Connection conn = null;

        try {

            Class.forName("oracle.jdbc.driver.OracleDriver");//加载数据驱动
            conn = DriverManager.getConnection(url, user, password);// 连接数据库

        } catch (ClassNotFoundException e) {
            e.printStackTrace();
            System.out.println("加载数据库驱动失败");
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("连接数据库失败");
        }
        return conn;
    }

    public static void close(Connection conn, PreparedStatement ps, ResultSet rs) {
        try {
            if (rs != null) {
                rs.close();
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }

        try {
            if (ps != null) {
                ps.close();
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }

        try {
            if (conn != null) {
                conn.close();
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }

    }

    /**
     * 删除设备或摄像头
     */
    public void deleteDeviceOrVedioById(String id) {
        if (StringUtils.isBlank(id)) {
            return;
        }
        id = "'%" + id + "%'";
        Connection connection;
        Statement statement;
        try {
            connection = this.getConnection();
            statement = connection.createStatement();
        } catch (SQLException e) {
            log.error("创建Oracle数据库连接失败：{}", e.getMessage());
            return;
        }
        String sql1 = "delete from SCADAPNTS where name like " + id;
        String sql2 = "delete from SCADAPNTS_CON where valname like " + id;
        String sql3 = "delete from SCADAPNTS_DES where name like " + id;
        String sql4 = "delete from SCADA_HISTORY where sid like " + id;
        try {
            statement.executeUpdate(sql1);
            statement.executeUpdate(sql2);
            statement.executeUpdate(sql3);
            statement.executeUpdate(sql4);
        } catch (SQLException e) {
            log.error("从Oracle删除{}失败：{}", id, e.getMessage());
        }
        close(connection, (PreparedStatement) statement, null);
    }

    /**
     * 删除监测点
     */
    public void deleteStationsById(String id) {
        if (StringUtils.isBlank(id)) {
            return;
        }
        Connection connection;
        Statement statement;
        try {
            connection = this.getConnection();
            statement = connection.createStatement();
        } catch (SQLException e) {
            log.error("创建Oracle数据库连接失败：{}", e.getMessage());
            return;
        }

        try {
            // 找出设备id
            String searchSql = "select name from SCADAPNTS_DES where ptnum = '" + id + "'";
            ResultSet resultSet = statement.executeQuery(searchSql);
            String deviceId = "";
            while (resultSet.next()) {
                deviceId = resultSet.getString(1);
                deviceId = deviceId.split("\\.")[0];
                break;
            }
            if (StringUtils.isNotBlank(deviceId)) {
                this.deleteDeviceOrVedioById(deviceId);
            }
            // 删除监测点
            String sql = "delete from SCADAPNTS_STATIONS where ptid = '" + id + "'";
            statement.executeUpdate(sql);

            close(connection, (PreparedStatement) statement, resultSet);
        } catch (SQLException e) {
            log.error("从Oracle删除{}失败：{}", id, e.getMessage());
        }
    }
}
