import { ref } from 'vue'
import { GetPipeLegends } from '@/api/mapservice/pipe'

export const usePipeLegends = () => {
  const legends = ref<
    {
      layerId: number
      layerName: string
      layerType: string
      minScale: number
      maxScale: number
      legend: [
        {
          label: string
          url: string
          imageData: string
          contentType: string
          height: number
          width: number
        }
      ]
    }[]
  >([])
  const legendRecords = computed(() => {
    const obj: any = {}
    legends.value.map(item => {
      obj[item.layerId] = item
    })
    return obj
  })
  const getLegends = async () => {
    try {
      const res = await GetPipeLegends()
      console.log(res)
      legends.value = res.data.layers || []
      return res
    } catch (e) {
      console.log(e)
      legends.value = []
      // TODO handle the exception
    }
  }
  return {
    getLegends,
    legends,
    legendRecords
  }
}
