import{d as y,a8 as h,g as e,h as c,F as a,n as g,aJ as C,q as V,p as s,bh as r,i as w,aB as k,bz as v,bU as x,bW as B,C as E}from"./index-r0dFAfgr.js";const N={class:"show"},F={class:"label"},j={class:"value"},q=y({__name:"cards",props:{modelValue:{type:Array,default(){return[]}},span:{type:Number,default(){return 8}}},emits:["update:modelValue"],setup(d,{emit:p}){const t=d,u=p,_=h({get:()=>t.modelValue,set:o=>{u("update:modelValue",o)}}),m=["#528ff8","#73cb71","#efbd47","#528ff8"];return(o,z)=>{const f=v,i=x,b=B;return e(),c(b,{gutter:5},{default:a(()=>[(e(!0),g(k,null,C(w(_),(n,l)=>(e(),c(i,{key:l,span:t.span},{default:a(()=>[V(f,{shadow:"never","body-style":{display:"flex",justifyContent:"center",backgroundColor:m[l],padding:"5px"},class:"cards-item"},{default:a(()=>[s("div",N,[s("span",F,r(n.label),1),s("span",j,r(n.value),1)])]),_:2},1032,["body-style"])]),_:2},1032,["span"]))),128))]),_:1})}}}),D=E(q,[["__scopeId","data-v-a99cb0e0"]]);export{D as default};
