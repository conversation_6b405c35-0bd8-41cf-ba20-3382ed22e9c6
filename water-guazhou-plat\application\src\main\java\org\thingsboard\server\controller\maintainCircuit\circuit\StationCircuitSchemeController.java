//package org.thingsboard.server.controller.maintainCircuit.circuit;
//
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.*;
//import org.thingsboard.server.common.data.UUIDConverter;
//import org.thingsboard.server.common.data.exception.ThingsboardException;
//import org.thingsboard.server.controller.base.BaseController;
//import org.thingsboard.server.dao.maintainCircuit.StationCircuitSchemeService;
//import org.thingsboard.server.dao.model.sql.maintainCircuit.circuit.StationCircuitScheme;
//import org.thingsboard.server.dao.util.imodel.response.IstarResponse;
//
//import java.util.List;
//
//@RestController
//@RequestMapping("api/stationCircuitScheme")
//public class StationCircuitSchemeController extends BaseController {
//
//    @Autowired
//    private StationCircuitSchemeService stationCircuitSchemeService;
//
//    @PostMapping
//    public IstarResponse save(@RequestBody StationCircuitScheme entity) throws ThingsboardException {
//        entity.setCreateUser(UUIDConverter.fromTimeUUID(getCurrentUser().getUuidId()));
//        entity.setTenantId(UUIDConverter.fromTimeUUID(getTenantId().getId()));
//        stationCircuitSchemeService.save(entity);
//        return IstarResponse.ok();
//    }
//
//    @GetMapping("{id}")
//    public IstarResponse get(@PathVariable String id) {
//        return IstarResponse.ok(stationCircuitSchemeService.findById(id));
//    }
//
//    @GetMapping("list")
//    public IstarResponse findList(@RequestParam int page, @RequestParam int size,
//                                  @RequestParam(required = false) String name,
//                                  @RequestParam(required = false) String stationType) throws ThingsboardException {
//        return IstarResponse.ok(stationCircuitSchemeService.findList(page, size, name, stationType, getTenantId()));
//    }
//
//    @DeleteMapping("remove")
//    public IstarResponse remove(@RequestBody List<String> ids) {
//        stationCircuitSchemeService.remove(ids);
//        return IstarResponse.ok();
//    }
//
//}
