<template>
  <div>
    <ArcWidgetButton
      id="tool-areameasure"
      ref="refBtn"
      :icon="'gis:measure-area-alt'"
      :title="'面积测量'"
      @click="
        (isCollapsed) => handleToolClick('areameasure', '面积测量', isCollapsed)
      "
    ></ArcWidgetButton>
    <Panel
      v-if="mounted"
      ref="refToolPanel"
      :custom-class="'tool-area-measure-panel'"
      :telport="'#arcmap-wrapper'"
      :title="state.toolPanelTitle"
      :destroy-by-close="true"
      :before-close="collapseBtn"
    >
      <Form ref="refForm" :config="FormConfig"></Form>
    </Panel>
  </div>
</template>
<script lang="ts" setup>
import ArcWidgetButton from '@/components/arcMap/arcWidgetButton.vue';
import { useSketch, useWidgets } from '@/hooks/arcgis';
import { calcArea, getGraphicLayer } from '@/utils/MapHelper';

const refToolPanel = ref<IPanelIns>();
const view: __esri.MapView | undefined = inject('view');
const graphicsLayer = getGraphicLayer(view, {
  id: 'measure-area',
  title: '面积测量'
});
const queryGeometry: { rings: any[] } = { rings: [] };
const state = reactive<{
  showOverViewMap: boolean;
  toolPanelTitle: string;
  toolPanelOperate: string;
  measuring: boolean;
}>({
  showOverViewMap: false,
  toolPanelTitle: '',
  toolPanelOperate: '',
  measuring: false
});
const refBtn = ref<InstanceType<typeof ArcWidgetButton>>();
const handleToolClick = async (
  path: string,
  title: string,
  isCollapsed: boolean,
  fromPanel?: boolean
) => {
  if (!isCollapsed) {
    state.toolPanelOperate = path;
    state.toolPanelTitle = title;
    refToolPanel.value?.Open();
  } else {
    !fromPanel && refToolPanel.value?.Close();
  }
};
const collapseBtn = () => {
  graphicsLayer?.removeAll();
  sketch.value?.cancel();
  refBtn.value?.toggle(true);
};
const refForm = ref<IFormIns>();
const FormConfig = reactive<IFormConfig>({
  labelWidth: 60,
  group: [
    {
      fields: [
        {
          type: 'text',
          field: 'area',
          label: '面积',
          style: {
            marginRight: '12px'
          },
          extraFormItem: [
            {
              type: 'select',
              field: 'unit',
              width: '100px',
              options: [
                // { label: '英亩', value: 'acres' },
                { label: '公顷', value: 'hectares' },
                // { label: '平方英尺', value: 'square-feet' },
                { label: '平方米', value: 'square-meters' },
                // { label: '平方码', value: 'square-yards' },
                { label: '平方公里', value: 'square-kilometers' }
                // { label: '平方英里', value: 'square-miles' }
              ],
              onChange: () => resolveMeasure()
            }
          ]
        },
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: '新测量',
              type: 'default',
              loading: () => state.measuring,
              click: () => startMeasure()
            }
          ]
        }
      ]
    }
  ],
  gutter: 12,
  defaultValue: {
    unit: 'square-meters'
  }
});
const { initSketch, destroySketch, sketch } = useSketch();

const resolveMeasure = async () => {
  if (!queryGeometry || !refForm.value) return;
  state.measuring = true;
  try {
    const res = calcArea(
      queryGeometry.rings[0],
      refForm.value.dataForm.unit || 'square-meters',
      view?.spatialReference
    );
    refForm.value.dataForm.area = res.toFixed(2);
  } catch (error) {
    console.dir(error);
  }
  state.measuring = false;
};
const resolveDrawEnd = (result: ISketchHandlerParameter) => {
  if (result.state === 'complete') {
    queryGeometry.rings = (
      result.graphics[0]?.geometry as __esri.Polygon
    ).rings;
    resolveMeasure();
  }
};
const startMeasure = () => {
  graphicsLayer?.removeAll();
  sketch.value?.create('polygon');
};
const destroy = () => {
  removeCustomWidget(view, 'tool-areameasure');
  graphicsLayer?.removeAll();
  destroySketch();
  refToolPanel.value?.Close();
  graphicsLayer && view?.map.remove(graphicsLayer);
};
const { addCustomWidget, removeCustomWidget } = useWidgets();
const mounted = ref<boolean>();
onMounted(() => {
  mounted.value = true;
  addCustomWidget(view, 'tool-areameasure', 'bottom-right');
  initSketch(view, graphicsLayer, {
    updateCallBack: resolveDrawEnd,
    createCallBack: resolveDrawEnd
  });
});
onBeforeUnmount(() => {
  destroy();
});
onUnmounted(() => {
  mounted.value = false;
});
</script>
<style lang="scss" scoped></style>
<style lang="scss">
.tool-area-measure-panel {
  width: 260px;
  height: 250px;
  position: absolute;
  bottom: 15px;
  right: 70px;
}
</style>
