package org.thingsboard.server.dao.model.sql;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/4/26 10:51
 */
@Data
@TableName("tb_msg_template")
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
public class MsgTemplateEntity {

    @TableId
    private String id;

    private String name;

    private String code;

    private String content;

    private String param;

    private String configId;

    private Date createTime;

    private String tenantId;

    private String sql;

    private String type;

    private String enabled;

    @TableField(exist = false)
    private String signName;
}
