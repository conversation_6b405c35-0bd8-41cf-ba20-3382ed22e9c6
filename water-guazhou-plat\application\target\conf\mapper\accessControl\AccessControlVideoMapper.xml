<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="org.thingsboard.server.dao.sql.accessControl.AccessControlVideoMapper">

    <!-- 结果映射 -->
    <resultMap id="AccessControlVideoResultMap" type="org.thingsboard.server.dao.model.sql.AccessControlVideo">
        <id column="id" property="id"/>
        <result column="access_control_id" property="accessControlId"/>
        <result column="video_id" property="videoId"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <!-- 视频结果映射 -->
    <resultMap id="VideoEntityResultMap" type="org.thingsboard.server.dao.model.sql.VideoEntity">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="serial_number" property="serialNumber"/>
        <result column="channel_id" property="channelId"/>
        <result column="url" property="url"/>
        <result column="video_type" property="videoType"/>
        <result column="longitude" property="longitude"/>
        <result column="latitude" property="latitude"/>
        <result column="location" property="location"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="project_id" property="projectId"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 根据门禁ID查询关联的视频列表 -->
    <select id="findVideosByAccessControlId" resultMap="VideoEntityResultMap">
        SELECT v.id, v.name, v.serial_number, v.channel_id, v.url, v.video_type,
               v.longitude, v.latitude, v.location, v.tenant_id, v.project_id, v.update_time
        FROM video v
        INNER JOIN tb_access_control_video acv ON v.id = acv.video_id
        WHERE acv.access_control_id = #{accessControlId}
        ORDER BY acv.create_time DESC
    </select>

    <!-- 根据视频ID查询关联信息 -->
    <select id="findByVideoId" resultMap="AccessControlVideoResultMap">
        SELECT id, access_control_id, video_id, tenant_id, create_time
        FROM tb_access_control_video
        WHERE video_id = #{videoId}
        <if test="tenantId != null and tenantId != ''">
            AND tenant_id = #{tenantId}
        </if>
        ORDER BY create_time DESC
    </select>

    <!-- 根据门禁ID查询关联信息 -->
    <select id="findByAccessControlId" resultMap="AccessControlVideoResultMap">
        SELECT id, access_control_id, video_id, tenant_id, create_time
        FROM tb_access_control_video
        WHERE access_control_id = #{accessControlId}
        <if test="tenantId != null and tenantId != ''">
            AND tenant_id = #{tenantId}
        </if>
        ORDER BY create_time DESC
    </select>

    <!-- 根据门禁ID删除关联 -->
    <delete id="deleteByAccessControlId">
        DELETE FROM tb_access_control_video
        WHERE access_control_id = #{accessControlId}
    </delete>

    <!-- 根据视频ID删除关联 -->
    <delete id="deleteByVideoId">
        DELETE FROM tb_access_control_video
        WHERE video_id = #{videoId}
    </delete>

    <!-- 根据门禁ID和视频ID查询关联 -->
    <select id="findByAccessControlIdAndVideoId" resultMap="AccessControlVideoResultMap">
        SELECT id, access_control_id, video_id, tenant_id, create_time
        FROM tb_access_control_video
        WHERE access_control_id = #{accessControlId}
          AND video_id = #{videoId}
        LIMIT 1
    </select>

    <!-- 根据租户ID查询所有关联 -->
    <select id="findByTenantId" resultMap="AccessControlVideoResultMap">
        SELECT id, access_control_id, video_id, tenant_id, create_time
        FROM tb_access_control_video
        WHERE tenant_id = #{tenantId}
        ORDER BY create_time DESC
    </select>

    <!-- 统计门禁关联的视频数量 -->
    <select id="countVideosByAccessControlId" resultType="int">
        SELECT COUNT(*)
        FROM tb_access_control_video
        WHERE access_control_id = #{accessControlId}
    </select>

    <!-- 统计视频关联的门禁数量 -->
    <select id="countAccessControlsByVideoId" resultType="int">
        SELECT COUNT(*)
        FROM tb_access_control_video
        WHERE video_id = #{videoId}
    </select>

</mapper>
