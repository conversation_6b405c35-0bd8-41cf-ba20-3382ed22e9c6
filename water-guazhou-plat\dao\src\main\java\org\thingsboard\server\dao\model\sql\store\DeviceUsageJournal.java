package org.thingsboard.server.dao.model.sql.store;

import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.sql.department.DepartmentMapper;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseTenantName;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseUsername;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseViaMapper;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.util.Date;

@Getter
@Setter
@ResponseEntity
public class DeviceUsageJournal {
    // id
    @TableId
    private String id;

    // 使用部门
    @ParseViaMapper(DepartmentMapper.class)
    private String departmentId;

    // 设备标签
    private String deviceLabelCode;

    // 使用人员
    @ParseUsername
    private String userId;

    // 领用时间
    private Date receiveTime;

    // 备注
    private String remark;

    // 租户ID
    @ParseTenantName
    private String tenantId;
}
