package org.thingsboard.server.dao.sql.smartService.portal;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.thingsboard.server.dao.model.sql.smartService.portal.SsPortalCompanyIntroduce;

@Mapper
public interface SsPortalCompanyIntroduceMapper extends BaseMapper<SsPortalCompanyIntroduce> {

    SsPortalCompanyIntroduce getByTenantId(String tenantId);

    @SuppressWarnings("methodNotInXmlInspection")
    boolean update(SsPortalCompanyIntroduce entity);

    boolean updateFully(SsPortalCompanyIntroduce entity);

    boolean save(SsPortalCompanyIntroduce entity);

}
