import{u as T}from"./useDetector-BRcb7GRN.js";import{d as S,c as h,a8 as B,am as I,ac as b,o as P,ay as V,g as d,n as x,q as t,i as o,av as z,W as A,r as F,F as i,aB as w,aJ as L,h as M,bU as G,bW as N,C as $}from"./index-r0dFAfgr.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./project-DUuzYgGl.js";import{a as H,E as k,b as D,c as R}from"./StatisticsHelper-D-s_6AyQ.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import{z as W}from"./echart-BoVIcYbV.js";import{_ as q}from"./index-C9hz-UZb.js";import{_ as J}from"./ArcView-DpMnCY82.js";import{_ as U}from"./ArcPipe.vue_vue_type_script_setup_true_lang-ClsVvV2P.js";import Z from"./DeviceTotal-DSbnzXdI.js";import E from"./ValueItem-DDPVPUjf.js";import{p as C}from"./padStart-BKfyZZDO.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./pipe-nogVzCHG.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./config-DqqM5K5L.js";import"./DateFormatter-Bm9a68Ax.js";import"./OutsideWorkOrder-C6s8joBt.js";import"./index-CpGhZCTT.js";import"./ViewHelper-BGCZjxXH.js";import"./fieldconfig-Bk3o1wi7.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./LayerHelper-Cn-iiqxI.js";const j=S({__name:"SimpleLines",props:{width:{},height:{},data:{},xdata:{},unit:{}},setup(v){const m=h(),l=h(),n=v,f=h(),g=B(()=>({width:n.width?n.width+"px":"100%",height:n.height?n.height+"px":"100%"})),y=T();I(()=>n.data,()=>{c()},{deep:!0});const c=()=>{f.value={legend:{right:10,top:10,color:"#B8D2FF"},grid:{left:20,right:20,top:40,bottom:5,containLabel:!0},dataZoom:[{show:!0,type:"inside",start:0,end:100},{show:!1,start:0,end:100}],xAxis:{type:"category",axisLabel:{color:"#B8D2FF",fontSize:10},axisLine:{lineStyle:{color:"#548BD2"}},axisTick:{show:!1},data:n.xdata},yAxis:[{type:"value",name:n.unit,namePosition:"top",axisLabel:{color:"#B8D2FF",fontSize:10},splitLine:{lineStyle:{type:"dashed",color:"#B8D2FF"}},axisTick:{show:!1},axisLine:{show:!1}}],series:n.data.map(a=>({name:a.name,type:"line",smooth:!0,symbol:"none",itemStyle:{color:b(a.color,1)},areaStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:b(a.color,.5)},{offset:1,color:b(a.color,.1)}]}},data:a.values}))}};return P(()=>{c(),y.listenTo(m.value,l)}),(a,u)=>{const p=V("VChart");return d(),x("div",{ref_key:"refContainer",ref:m,style:z(o(g))},[t(p,{ref_key:"refChart",ref:l,option:o(f)},null,8,["option"])],4)}}}),K=S({__name:"ArcPipeBar",props:{layerIds:{},group_fields:{},statistic_field:{},statistic_type:{},prefix:{},height:{},width:{}},setup(v){const m=A(),l=v,n=B(()=>({height:l.height?l.height+"px":"100%",width:l.width?l.width+"px":"100%"})),f=h();I(()=>m.gLayerInfos,()=>{g()});const g=async()=>{var s;let u=[];(s=m.gLayerInfos)!=null&&s.length&&(u=await H({layerIds:l.layerIds,statistic_field:l.statistic_field,statistic_type:l.statistic_type,group_fields:l.group_fields}));const p=u.map(_=>({value:_.value,key:(l.prefix||"")+(_.label||"--")}))||[];f.value=W(p,l.statistic_type==="1"?"个":"m")},y=T(),c=h(),a=h();return P(()=>{g(),y.listenTo(a.value,c)}),(u,p)=>{const s=V("VChart");return d(),x("div",{ref_key:"refContainer",ref:a,style:z(o(n))},[t(s,{ref_key:"refChart",ref:c,option:o(f)},null,8,["option"])],4)}}}),O={class:"wrapper overlay-y"},Q=S({__name:"index",setup(v){const m=F([{span:8,label:"管线总长",value:"221.53",unit:"km",bgColor:"rgb(55, 210, 212)"},{span:8,label:"管线总数",value:"11915",unit:"个",bgColor:"rgb(25, 202, 136)"},{span:8,label:"管点总数",value:"11901",unit:"个",bgColor:"rgb(133, 143, 248)"},{span:6,label:"水泵总数",value:"8",unit:"个",bgColor:"rgb(253, 145, 51)"},{span:6,label:"水池总数",value:"3",unit:"个",bgColor:"rgb(246, 209, 14)"},{span:6,label:"水厂总数",value:"2",unit:"个",bgColor:"rgb(46, 140, 255)"},{span:6,label:"阀门总数",value:"0",unit:"个",bgColor:"rgb(246, 88, 14)"}]),l=F([{label:"最大流量",value:"781.20",unit:"m³/h",rate:11.24,icon:"ep:sort-up"},{label:"最大流速",value:"4.81",unit:"m/s",rate:11.24,icon:"ep:sort-up"},{label:"最高压力",value:"535.29",unit:"m",rate:11.24,icon:"ep:sort-up"},{label:"最低压力",value:"-107.14",unit:"m",rate:11.24,icon:"ep:sort-up"}]),n=F([{label:"综合精度",value:"100.00",unit:"%",rate:3.23,icon:"ep:sort-up"},{label:"压力精度",value:"100.00",unit:"%",rate:2.44,icon:"ep:sort-up"},{label:"流量精度",value:"100.00",unit:"%",rate:4.76,icon:"ep:sort-up"}]);return(f,g)=>{const y=U,c=J,a=q,u=K,p=G,s=N,_=j;return d(),x("div",O,[t(s,{gutter:20},{default:i(()=>[t(p,{lg:12},{default:i(()=>[t(a,{height:630,padding:"20px"},{default:i(()=>[t(c,null,{default:i(()=>[t(y)]),_:1})]),_:1}),t(a,{title:"设备概况",padding:"50px 20px 20px 20px"},{default:i(()=>[t(Z,{"device-total":o(m)},null,8,["device-total"])]),_:1}),t(a,{title:"按管径统计管长",padding:"50px 20px 20px 20px"},{default:i(()=>{var e;return[t(u,{prefix:"DN",height:200,"layer-ids":((e=o(A)().gLayerInfos)==null?void 0:e.filter(r=>r.geometrytype==="esriGeometryPolyline").map(r=>r.layerid))||[],statistic_field:o(k).ShapeLen,statistic_type:o(D).LENGTH,group_fields:[o(R).DIAMETER]},null,8,["layer-ids","statistic_field","statistic_type","group_fields"])]}),_:1}),t(a,{title:"按管材统计管长",padding:"50px 20px 20px 20px"},{default:i(()=>{var e;return[t(u,{height:200,"layer-ids":((e=o(A)().gLayerInfos)==null?void 0:e.filter(r=>r.geometrytype==="esriGeometryPolyline").map(r=>r.layerid))||[],statistic_field:o(k).ShapeLen,statistic_type:o(D).LENGTH,group_fields:[o(R).MATERIAL]},null,8,["layer-ids","statistic_field","statistic_type","group_fields"])]}),_:1})]),_:1}),t(p,{lg:12},{default:i(()=>[t(a,{title:"管网运行参数",padding:"50px 20px 20px 20px"},{default:i(()=>[t(s,null,{default:i(()=>[(d(!0),x(w,null,L(o(l),(e,r)=>(d(),M(p,{key:r,span:6},{default:i(()=>[t(E,{label:e.label,value:e.value,icon:e.icon,rate:e.rate,unit:e.unit},null,8,["label","value","icon","rate","unit"])]),_:2},1024))),128))]),_:1})]),_:1}),t(a,{title:"管网压力变化曲线",padding:"50px 20px 20px 20px"},{default:i(()=>[t(_,{xdata:Array.from({length:24}).map((e,r)=>o(C)((r+1).toString(),2,"0")),data:[{name:"最高压力",color:"#318DFF",values:Array.from({length:24}).map(()=>(Math.random()*.1+5.4).toFixed(2))},{name:"最低压力",color:"#67c23a",values:Array.from({length:24}).map(()=>(Math.random()*.1+5).toFixed(2))},{name:"平均压力",color:"#e6a23c",values:Array.from({length:24}).map(()=>(Math.random()*.1+5.2).toFixed(2))}],unit:"压力(MPa)",height:384},null,8,["xdata","data"])]),_:1}),t(a,{title:"24时供水变化曲线",padding:"50px 20px 20px 20px"},{default:i(()=>[t(_,{xdata:Array.from({length:24}).map((e,r)=>o(C)((r+1).toString(),2,"0")),data:[{name:"R1",color:"#318DFF",values:Array.from({length:24}).map(()=>(Math.random()*200+900).toFixed(2))},{name:"R2",color:"#67c23a",values:Array.from({length:24}).map(()=>(Math.random()*200+300).toFixed(2))},{name:"R3",color:"#e6a23c",values:Array.from({length:24}).map(()=>(Math.random()*200+100).toFixed(2))},{name:"R3",color:"#e6a23c",values:Array.from({length:24}).map(()=>(Math.random()*200+90).toFixed(2))}],unit:"流量(m³/h)",height:380},null,8,["xdata","data"])]),_:1}),t(a,{title:"模型精度",padding:"50px 20px 20px 20px"},{default:i(()=>[t(s,null,{default:i(()=>[(d(!0),x(w,null,L(o(n),(e,r)=>(d(),M(p,{key:r,span:8},{default:i(()=>[t(E,{label:e.label,value:e.value,icon:e.icon,rate:e.rate,unit:e.unit},null,8,["label","value","icon","rate","unit"])]),_:2},1024))),128))]),_:1}),t(_,{xdata:Array.from({length:24}).map((e,r)=>o(C)((r+1).toString(),2,"0")),data:[{name:"R1",color:"#318DFF",values:Array.from({length:24}).map(()=>(Math.random()*200+900).toFixed(2))},{name:"R2",color:"#67c23a",values:Array.from({length:24}).map(()=>(Math.random()*200+300).toFixed(2))},{name:"R3",color:"#e6a23c",values:Array.from({length:24}).map(()=>(Math.random()*200+100).toFixed(2))},{name:"R3",color:"#e6a23c",values:Array.from({length:24}).map(()=>(Math.random()*200+90).toFixed(2))}],unit:"流量(m³/h)",height:404},null,8,["xdata","data"])]),_:1})]),_:1})]),_:1})])}}}),we=$(Q,[["__scopeId","data-v-ae23021e"]]);export{we as default};
