import { request } from '@/plugins/axios/gis'

/**
*
导出地图数据
* @param params
* @returns
*/
export const ExportMapData = (params: {
  /**
   * 0: cad
   * 1: shapefile
   */
  exporttype: 0 | 1
  /** 绘制的几何 */
  geomjson: string
  /** 图层名 */
  outputlayers?: string
  /** 任务名 */
  taskname?: string
  /** 任务描述 */
  description?: string
}) => {
  return request({
    url: '/api/gis/ExportMapData',
    method: 'post',
    data: params
  })
}

/**
 * 查询导出地图数据任务
 * @param params
 * @returns
 */
export const ListExportMapTasks = (params: {
  /** 任务id */
  taskid?: string
  /** 导出类型
   * 0：cad
   * 1: shapefile
   */
  exporttype?: string
  /** 创建人 */
  createuser?: string
  /** 任务名称 */
  taskname?: string
  /** 任务开始时间 */
  startdate?: string
  /** 任务完成时间 */
  enddate?: string
  /** 任务状态 */
  exp_status?: string
  /** 页数 */
  pageindex: number
  /** 每页条数 */
  pagesize: number
}) => {
  return request({
    url: '/api/gis/ListExportMapTasks',
    method: 'post',
    data: params
  })
}
/**
 * 查询导出地图任务
 * @param params
 * @returns
 */
export const ListPrintMapTasks = (params: {
  taskid?: string
  exporttype?: string
  createuser?: string
  taskname?: string
  startdate?: string
  enddate?: string
  exp_status?: string
  pageindex?: number
  pagesize?: number
}) => {
  return request({
    url: '/api/gis/ListPrintMapTasks',
    method: 'post',
    data: params
  })
}
/**
 * 导出地图成pdf文件
 * @param params
 * @returns
 */
export const ExportMap = (params: {
  geomjson?: string
  scale?: number
  taskname?: string
  exporttype?: number
  papersize?: number
  paperrotation?: number
  resolution?: number
}) => {
  return request({
    url: '/api/gis/ExportMap',
    method: 'post',
    data: params
  })
}
