<!-- 成本管理 -->
<template>
  <TreeBox>
    <template #tree>
      <SLTree ref="refTree" :tree-data="TreeData"></SLTree>
    </template>
    <SLCard class="wrapper-content" title="" overlay>
      <CardSearch ref="refSearch" :config="searchConfig"></CardSearch>
      <FormTable class="card-table" :config="tableConfig"></FormTable>
    </SLCard>
    <DialogForm ref="refDialog" :config="dialogConfig">
    </DialogForm>
  </TreeBox>
</template>
<script lang="ts" setup>
import { reactive } from 'vue'
import dayjs from 'dayjs'
import { Refresh, Search as SearchIcon, Plus } from '@element-plus/icons-vue'
import { SLConfirm, SLMessage } from '@/utils/Message'
import { usePartition } from '@/hooks/arcgis'
import { formatDate } from '@/utils/DateFormatter'
import {
  getCostList,
  saveCost
} from '@/api/dma/cost'
import {
  getDictList
} from '@/api/dma/dict'

const state = reactive<{
  dialogType: string
  stationTree: any
  stationId: string,
  dictList: any
}>({
  dialogType: 'add',
  stationTree: [],
  dictList: [],
  stationId: ''
})
const refDialog = ref<IDialogFormIns>()
const refSearch = ref<ISearchIns>()
const TreeData = reactive<SLTreeConfig>({
  data: [],
  loading: false,
  currentProject: null,
  title: '选择分区',
  expandOnClickNode: false,
  treeNodeHandleClick: async (data: NormalOption) => {
    if (TreeData.currentProject !== data) {
      TreeData.currentProject = data
      await refreshData()
    }
  }
})

// 列表模式搜索配置
const searchConfig = reactive<ISearch>({
  defaultParams: {
    stype: 'year',
    year: dayjs().format(),
    month: dayjs().format(),
    day: [dayjs().format(), dayjs().format()]
  },
  filters: [
    {
      type: 'select',
      label: '选择方式',
      clearable: false,
      field: 'stype',
      options: [
        { label: '按年', value: 'year' },
        { label: '按月', value: 'month' },
        { label: '按时间段', value: 'day' }
      ]
    }, {
      type: 'year',
      label: '',
      clearable: false,
      field: 'year',
      handleHidden: (params: any, query: any, formItem: IFormItem) => {
        formItem.hidden = params.stype === 'day' || params.stype === 'month'
      }
    }, {
      type: 'month',
      label: '',
      clearable: false,
      field: 'month',
      handleHidden: (params: any, query: any, formItem: IFormItem) => {
        formItem.hidden = params.stype === 'day' || params.stype === 'year'
      }
    }, {
      type: 'daterange',
      label: '',
      clearable: false,
      field: 'day',
      handleHidden: (params: any, query: any, formItem: IFormItem) => {
        formItem.hidden = params.stype === 'month' || params.stype === 'year'
      }
    }, {
      type: 'select',
      label: '成本类型',
      field: 'type',
      options: computed(() => state.dictList) as any
    },
    {
      type: 'input',
      label: '记录人名称',
      labelWidth: 120,
      field: 'creatorName'
    }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          svgIcon: shallowRef(SearchIcon),
          click: () => refreshData()
        },
        {
          type: 'default',
          perm: true,
          text: '重置',
          svgIcon: shallowRef(Refresh),
          click: () => {
            refSearch.value?.resetForm()
            refreshData()
          }
        },
        {
          perm: true,
          type: 'success',
          text: '新增',
          svgIcon: shallowRef(Plus),
          click: () => {
            state.dialogType = 'add'
            dialogConfig.title = '新建成本记录'
            dialogConfig.defaultValue = {
              startDate: dayjs().format(),
              endDate: dayjs().format(),
              syncMonth: dayjs().format()
            }
            refDialog.value?.refForm?.resetForm()
            refDialog.value?.openDialog()
          }
        }]
    }]
})

// 对话框配置
const dialogConfig = reactive<IDialogFormConfig>({
  dialogWidth: 500,
  title: '新建成本记录',
  labelPosition: 'right',
  submit: params => {
    console.log(params)
    SLConfirm('确定提交？', '提示信息').then(async () => {
      try {
        saveCost(params).then((res: any) => {
          if (res.data.code === 200) {
            SLMessage.success('提交成功')
          }
        })
        await refreshData()
        refDialog.value?.closeDialog()
      } catch (error) {
        SLMessage.error('提交失败')
      }
    })
  },
  group: [
    {
      fields: [
        {
          type: 'select-tree',
          field: 'partitionId',
          rules: [{ required: true, message: '请填选择分区' }],
          placeholder: '请填选择分区',
          label: '分区',
          options: computed(() => TreeData.data) as any
        },
        {
          type: 'input',
          field: 'code',
          rules: [{ required: true, message: '请填写工程编码' }],
          placeholder: '请填写工程编码',
          label: '工程编码'
        }, {
          type: 'select',
          field: 'type',
          rules: [{ required: true, message: '请选择成本类型' }],
          placeholder: '请选择成本类型',
          label: '成本类型',
          options: computed(() => state.dictList) as any
        }, {
          type: 'input',
          field: 'base',
          rules: [{ required: true, message: '请填写成本值' }],
          placeholder: '请填写成本值',
          label: '成本值'
        }, {
          type: 'input',
          field: 'value',
          rules: [{ required: true, message: '请填写成本含量' }],
          placeholder: '请填写成本含量',
          label: '成本含量'
        }, {
          type: 'textarea',
          field: 'remark',
          placeholder: '请填写备注',
          label: '备注'
        }
      ]
    }
  ]
})
// 数据获取
const refreshData = async () => {
  const query = refSearch.value?.queryParams || {}
  let searchDate = {}
  if (query?.stype === 'day') {
    searchDate = {
      start: dayjs(query.day[0]).startOf('day').valueOf(),
      end: dayjs(query.day[1]).endOf('day').valueOf()
    }
  } else if (query?.stype === 'month' || query?.stype === 'year') {
    const date = query.stype === 'month' ? query.month : query.year
    searchDate = {
      start: dayjs(date).startOf(query?.stype).valueOf(),
      end: dayjs(date).endOf(query?.stype).valueOf()
    }
  }
  const params = {
    type: query.type,
    creatorName: query.creatorName,
    page: tableConfig.pagination.page || 1,
    size: tableConfig.pagination.limit || 20,
    ...searchDate,
    partitionId: TreeData.currentProject.id
  }
  getCostList(params).then(res => {
    tableConfig.dataList = res.data.data?.data
    tableConfig.pagination.total = res.data.data?.total
  })
}
// 列表
const tableConfig = reactive<ITable>({
  loading: false,
  dataList: [],
  indexVisible: true,
  columns: [
    { prop: 'code', label: '工程编码', minWidth: 120 },
    {
      prop: 'typeName', label: '成本类型', minWidth: 120
    },
    { prop: 'base', label: '成本值', minWidth: 140 },
    { prop: 'value', label: '成本含量', minWidth: 120 },
    { prop: 'creatorName', label: '记录人名称', minWidth: 120 },
    { prop: 'createTime', label: '记录时间', minWidth: 120, formatter: row => formatDate(row.createTime, 'YYYY-MM-DD HH:mm:ss') },
    { prop: 'remark', label: '备注', minWidth: 120 }
  ],
  pagination: {
    refreshData: ({ page, size }) => {
      tableConfig.pagination.page = page
      tableConfig.pagination.limit = size
      refreshData()
    }
  }
})

const partition = usePartition()
onMounted(async () => {
  await partition.getTree()
  TreeData.data = partition.Tree.value
  TreeData.currentProject = TreeData.data[0]
  getDictList({ type: '1', page: 1, size: 9999 }).then((res) => {
    state.dictList = res.data.data?.data
    console.log(res.data.data?.data)
  })
})
</script>
<style lang="scss" scoped>
.wrapper-content {
  height: 100%;
}

.card-table {
  height: calc(100% - 100px);
  width: 100%;
}
</style>
