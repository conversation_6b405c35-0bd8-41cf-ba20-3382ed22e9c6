function fe(A,H){for(var F=0;F<H.length;F++){const t=H[F];if(typeof t!="string"&&!Array.isArray(t)){for(const d in t)if(d!=="default"&&!(d in A)){const C=Object.getOwnPropertyDescriptor(t,d);C&&Object.defineProperty(A,d,C.get?C:{enumerable:!0,get:()=>t[d]})}}}return Object.freeze(Object.defineProperty(A,Symbol.toStringTag,{value:"Module"}))}var Mn,dn,zn,rn={};Mn={get exports(){return rn},set exports(A){rn=A}},dn=typeof document<"u"&&document.currentScript?document.currentScript.src:void 0,zn=function(A){var H,F,t=(A=A||{})!==void 0?A:{};t.ready=new Promise(function(n,r){H=n,F=r});var d,C={};for(d in t)t.hasOwnProperty(d)&&(C[d]=t[d]);var vn=typeof window=="object",q=typeof importScripts=="function";typeof process=="object"&&typeof process.versions=="object"&&process.versions.node;var en,P="";function Bn(n){return t.locateFile?t.locateFile(n,P):P+n}(vn||q)&&(q?P=self.location.href:typeof document<"u"&&document.currentScript&&(P=document.currentScript.src),dn&&(P=dn),P=P.indexOf("blob:")!==0?P.substr(0,P.replace(/[?#].*/,"").lastIndexOf("/")+1):"",q&&(en=function(n){var r=new XMLHttpRequest;return r.open("GET",n,!1),r.responseType="arraybuffer",r.send(null),new Uint8Array(r.response)}));var V,tn,Nn=t.print||console.log.bind(console),U=t.printErr||console.warn.bind(console);for(d in C)C.hasOwnProperty(d)&&(t[d]=C[d]);C=null,t.arguments&&t.arguments,t.thisProgram&&t.thisProgram,t.quit&&t.quit,t.wasmBinary&&(V=t.wasmBinary),t.noExitRuntime,typeof WebAssembly!="object"&&G("no native wasm support detected");var hn=!1,mn=typeof TextDecoder<"u"?new TextDecoder("utf8"):void 0;function gn(n,r,e){for(var o=r+e,a=r;n[a]&&!(a>=o);)++a;if(a-r>16&&n.subarray&&mn)return mn.decode(n.subarray(r,a));for(var u="";r<a;){var c=n[r++];if(128&c){var i=63&n[r++];if((224&c)!=192){var f=63&n[r++];if((c=(240&c)==224?(15&c)<<12|i<<6|f:(7&c)<<18|i<<12|f<<6|63&n[r++])<65536)u+=String.fromCharCode(c);else{var s=c-65536;u+=String.fromCharCode(55296|s>>10,56320|1023&s)}}else u+=String.fromCharCode((31&c)<<6|i)}else u+=String.fromCharCode(c)}return u}function yn(n,r){return n?gn(v,n,r):""}function qn(n,r,e,o){if(!(o>0))return 0;for(var a=e,u=e+o-1,c=0;c<n.length;++c){var i=n.charCodeAt(c);if(i>=55296&&i<=57343&&(i=65536+((1023&i)<<10)|1023&n.charCodeAt(++c)),i<=127){if(e>=u)break;r[e++]=i}else if(i<=2047){if(e+1>=u)break;r[e++]=192|i>>6,r[e++]=128|63&i}else if(i<=65535){if(e+2>=u)break;r[e++]=224|i>>12,r[e++]=128|i>>6&63,r[e++]=128|63&i}else{if(e+3>=u)break;r[e++]=240|i>>18,r[e++]=128|i>>12&63,r[e++]=128|i>>6&63,r[e++]=128|63&i}}return r[e]=0,e-a}function Ln(n,r,e){return qn(n,v,r,e)}function Gn(n){for(var r=0,e=0;e<n.length;++e){var o=n.charCodeAt(e);o>=55296&&o<=57343&&(o=65536+((1023&o)<<10)|1023&n.charCodeAt(++e)),o<=127?++r:r+=o<=2047?2:o<=65535?3:4}return r}var on,j,v,O,L,p,k,_n,wn,bn,An=typeof TextDecoder<"u"?new TextDecoder("utf-16le"):void 0;function Jn(n,r){for(var e=n,o=e>>1,a=o+r/2;!(o>=a)&&L[o];)++o;if((e=o<<1)-n>32&&An)return An.decode(v.subarray(n,e));for(var u="",c=0;!(c>=r/2);++c){var i=O[n+2*c>>1];if(i==0)break;u+=String.fromCharCode(i)}return u}function Xn(n,r,e){if(e===void 0&&(e=2147483647),e<2)return 0;for(var o=r,a=(e-=2)<2*n.length?e/2:n.length,u=0;u<a;++u){var c=n.charCodeAt(u);O[r>>1]=c,r+=2}return O[r>>1]=0,r-o}function Yn(n){return 2*n.length}function Zn(n,r){for(var e=0,o="";!(e>=r/4);){var a=p[n+4*e>>2];if(a==0)break;if(++e,a>=65536){var u=a-65536;o+=String.fromCharCode(55296|u>>10,56320|1023&u)}else o+=String.fromCharCode(a)}return o}function $n(n,r,e){if(e===void 0&&(e=2147483647),e<4)return 0;for(var o=r,a=o+e-4,u=0;u<n.length;++u){var c=n.charCodeAt(u);if(c>=55296&&c<=57343&&(c=65536+((1023&c)<<10)|1023&n.charCodeAt(++u)),p[r>>2]=c,(r+=4)+4>a)break}return p[r>>2]=0,r-o}function Kn(n){for(var r=0,e=0;e<n.length;++e){var o=n.charCodeAt(e);o>=55296&&o<=57343&&++e,r+=4}return r}function Qn(n,r){return n%r>0&&(n+=r-n%r),n}function Tn(n){on=n,t.HEAP8=j=new Int8Array(n),t.HEAP16=O=new Int16Array(n),t.HEAP32=p=new Int32Array(n),t.HEAPU8=v=new Uint8Array(n),t.HEAPU16=L=new Uint16Array(n),t.HEAPU32=k=new Uint32Array(n),t.HEAPF32=_n=new Float32Array(n),t.HEAPF64=wn=new Float64Array(n)}t.INITIAL_MEMORY;var Cn=[],Pn=[],kn=[];function nr(){if(t.preRun)for(typeof t.preRun=="function"&&(t.preRun=[t.preRun]);t.preRun.length;)tr(t.preRun.shift());an(Cn)}function rr(){an(Pn)}function er(){if(t.postRun)for(typeof t.postRun=="function"&&(t.postRun=[t.postRun]);t.postRun.length;)ir(t.postRun.shift());an(kn)}function tr(n){Cn.unshift(n)}function or(n){Pn.unshift(n)}function ir(n){kn.unshift(n)}var x=0,M=null;function ar(n){x++,t.monitorRunDependencies&&t.monitorRunDependencies(x)}function ur(n){if(x--,t.monitorRunDependencies&&t.monitorRunDependencies(x),x==0&&M){var r=M;M=null,r()}}function G(n){t.onAbort&&t.onAbort(n),U(n="Aborted("+n+")"),hn=!0,n+=". Build with -s ASSERTIONS=1 for more info.";var r=new WebAssembly.RuntimeError(n);throw F(r),r}t.preloadedImages={},t.preloadedAudios={};var T,cr="data:application/octet-stream;base64,";function En(n){return n.startsWith(cr)}function Wn(n){try{if(n==T&&V)return new Uint8Array(V);if(en)return en(n);throw"both async and sync fetching of the wasm failed"}catch(r){G(r)}}function fr(){return V||!vn&&!q||typeof fetch!="function"?Promise.resolve().then(function(){return Wn(T)}):fetch(T,{credentials:"same-origin"}).then(function(n){if(!n.ok)throw"failed to load wasm binary file at '"+T+"'";return n.arrayBuffer()}).catch(function(){return Wn(T)})}function sr(){var n={env:Hn,wasi_snapshot_preview1:Hn};function r(u,c){var i=u.exports;t.asm=i,Tn((tn=t.asm.memory).buffer),bn=t.asm.__indirect_function_table,or(t.asm.__wasm_call_ctors),ur()}function e(u){r(u.instance)}function o(u){return fr().then(function(c){return WebAssembly.instantiate(c,n)}).then(function(c){return c}).then(u,function(c){U("failed to asynchronously prepare wasm: "+c),G(c)})}function a(){return V||typeof WebAssembly.instantiateStreaming!="function"||En(T)||typeof fetch!="function"?o(e):fetch(T,{credentials:"same-origin"}).then(function(u){return WebAssembly.instantiateStreaming(u,n).then(e,function(c){return U("wasm streaming compile failed: "+c),U("falling back to ArrayBuffer instantiation"),o(e)})})}if(ar(),t.instantiateWasm)try{return t.instantiateWasm(n,r)}catch(u){return U("Module.instantiateWasm callback failed with error: "+u),!1}return a().catch(F),{}}function an(n){for(;n.length>0;){var r=n.shift();if(typeof r!="function"){var e=r.func;typeof e=="number"?r.arg===void 0?z(e)():z(e)(r.arg):e(r.arg===void 0?null:r.arg)}else r(t)}}En(T="i3s.wasm")||(T=Bn(T));var J=[];function z(n){var r=J[n];return r||(n>=J.length&&(J.length=n+1),J[n]=r=bn.get(n)),r}function lr(n,r){z(n)(r)}function pr(n){return nn(n+16)+16}function se(n,r){}function dr(n,r){return void 0}function vr(n){this.excPtr=n,this.ptr=n-16,this.set_type=function(r){p[this.ptr+4>>2]=r},this.get_type=function(){return p[this.ptr+4>>2]},this.set_destructor=function(r){p[this.ptr+8>>2]=r},this.get_destructor=function(){return p[this.ptr+8>>2]},this.set_refcount=function(r){p[this.ptr>>2]=r},this.set_caught=function(r){r=r?1:0,j[this.ptr+12>>0]=r},this.get_caught=function(){return j[this.ptr+12>>0]!=0},this.set_rethrown=function(r){r=r?1:0,j[this.ptr+13>>0]=r},this.get_rethrown=function(){return j[this.ptr+13>>0]!=0},this.init=function(r,e){this.set_type(r),this.set_destructor(e),this.set_refcount(0),this.set_caught(!1),this.set_rethrown(!1)},this.add_ref=function(){var r=p[this.ptr>>2];p[this.ptr>>2]=r+1},this.release_ref=function(){var r=p[this.ptr>>2];return p[this.ptr>>2]=r-1,r===1}}function hr(n,r,e){throw new vr(n).init(r,e),n}var X={};function Sn(n){for(;n.length;){var r=n.pop();n.pop()(r)}}function Y(n){return this.fromWireType(k[n>>2])}var D={},I={},Z={},mr=48,gr=57;function yr(n){if(n===void 0)return"_unknown";var r=(n=n.replace(/[^a-zA-Z0-9_]/g,"$")).charCodeAt(0);return r>=mr&&r<=gr?"_"+n:n}function _r(n,r){return n=yr(n),function(){return r.apply(this,arguments)}}function un(n,r){var e=_r(r,function(o){this.name=r,this.message=o;var a=new Error(o).stack;a!==void 0&&(this.stack=this.toString()+`
`+a.replace(/^Error(:[^\n]*)?\n/,""))});return e.prototype=Object.create(n.prototype),e.prototype.constructor=e,e.prototype.toString=function(){return this.message===void 0?this.name:this.name+": "+this.message},e}var Rn=void 0;function Fn(n){throw new Rn(n)}function jn(n,r,e){function o(i){var f=e(i);f.length!==n.length&&Fn("Mismatched type converter count");for(var s=0;s<n.length;++s)E(n[s],f[s])}n.forEach(function(i){Z[i]=r});var a=new Array(r.length),u=[],c=0;r.forEach(function(i,f){I.hasOwnProperty(i)?a[f]=I[i]:(u.push(i),D.hasOwnProperty(i)||(D[i]=[]),D[i].push(function(){a[f]=I[i],++c===u.length&&o(a)}))}),u.length===0&&o(a)}function wr(n){var r=X[n];delete X[n];var e=r.rawConstructor,o=r.rawDestructor,a=r.fields;jn([n],a.map(function(u){return u.getterReturnType}).concat(a.map(function(u){return u.setterArgumentType})),function(u){var c={};return a.forEach(function(i,f){var s=i.fieldName,l=u[f],g=i.getter,_=i.getterContext,w=u[f+a.length],S=i.setter,b=i.setterContext;c[s]={read:function(N){return l.fromWireType(g(_,N))},write:function(N,pn){var R=[];S(b,N,w.toWireType(R,pn)),Sn(R)}}}),[{name:r.name,fromWireType:function(i){var f={};for(var s in c)f[s]=c[s].read(i);return o(i),f},toWireType:function(i,f){for(var s in c)if(!(s in f))throw new TypeError('Missing field:  "'+s+'"');var l=e();for(s in c)c[s].write(l,f[s]);return i!==null&&i.push(o,l),l},argPackAdvance:8,readValueFromPointer:Y,destructorFunction:o}]})}function br(n,r,e,o,a){}function cn(n){switch(n){case 1:return 0;case 2:return 1;case 4:return 2;case 8:return 3;default:throw new TypeError("Unknown type size: "+n)}}function Ar(){for(var n=new Array(256),r=0;r<256;++r)n[r]=String.fromCharCode(r);xn=n}var xn=void 0;function h(n){for(var r="",e=n;v[e];)r+=xn[v[e++]];return r}var In=void 0;function m(n){throw new In(n)}function E(n,r,e){if(e=e||{},!("argPackAdvance"in r))throw new TypeError("registerType registeredInstance requires argPackAdvance");var o=r.name;if(n||m('type "'+o+'" must have a positive integer typeid pointer'),I.hasOwnProperty(n)){if(e.ignoreDuplicateRegistrations)return;m("Cannot register type '"+o+"' twice")}if(I[n]=r,delete Z[n],D.hasOwnProperty(n)){var a=D[n];delete D[n],a.forEach(function(u){u()})}}function Tr(n,r,e,o,a){var u=cn(e);E(n,{name:r=h(r),fromWireType:function(c){return!!c},toWireType:function(c,i){return i?o:a},argPackAdvance:8,readValueFromPointer:function(c){var i;if(e===1)i=j;else if(e===2)i=O;else{if(e!==4)throw new TypeError("Unknown boolean type size: "+r);i=p}return this.fromWireType(i[c>>u])},destructorFunction:null})}var fn=[],y=[{},{value:void 0},{value:null},{value:!0},{value:!1}];function Un(n){n>4&&--y[n].refcount==0&&(y[n]=void 0,fn.push(n))}function Cr(){for(var n=0,r=5;r<y.length;++r)y[r]!==void 0&&++n;return n}function Pr(){for(var n=5;n<y.length;++n)if(y[n]!==void 0)return y[n];return null}function kr(){t.count_emval_handles=Cr,t.get_first_emval=Pr}var $={toValue:function(n){return n||m("Cannot use deleted val. handle = "+n),y[n].value},toHandle:function(n){switch(n){case void 0:return 1;case null:return 2;case!0:return 3;case!1:return 4;default:var r=fn.length?fn.pop():y.length;return y[r]={refcount:1,value:n},r}}};function Er(n,r){E(n,{name:r=h(r),fromWireType:function(e){var o=$.toValue(e);return Un(e),o},toWireType:function(e,o){return $.toHandle(o)},argPackAdvance:8,readValueFromPointer:Y,destructorFunction:null})}function sn(n){if(n===null)return"null";var r=typeof n;return r==="object"||r==="array"||r==="function"?n.toString():""+n}function Wr(n,r){switch(r){case 2:return function(e){return this.fromWireType(_n[e>>2])};case 3:return function(e){return this.fromWireType(wn[e>>3])};default:throw new TypeError("Unknown float type: "+n)}}function Sr(n,r,e){var o=cn(e);E(n,{name:r=h(r),fromWireType:function(a){return a},toWireType:function(a,u){if(typeof u!="number"&&typeof u!="boolean")throw new TypeError('Cannot convert "'+sn(u)+'" to '+this.name);return u},argPackAdvance:8,readValueFromPointer:Wr(r,o),destructorFunction:null})}function Rr(n,r,e,o,a){var u=r.length;u<2&&m("argTypes array size mismatch! Must at least get return value and 'this' types!");for(var c=r[1]!==null&&e!==null,i=!1,f=1;f<r.length;++f)if(r[f]!==null&&r[f].destructorFunction===void 0){i=!0;break}var s=r[0].name!=="void",l=u-2,g=new Array(l),_=[],w=[];return function(){var S;arguments.length!==l&&m("function "+n+" called with "+arguments.length+" arguments, expected "+l+" args!"),w.length=0,_.length=c?2:1,_[0]=a,c&&(S=r[1].toWireType(w,this),_[1]=S);for(var b=0;b<l;++b)g[b]=r[b+2].toWireType(w,arguments[b]),_.push(g[b]);function N(pn){if(i)Sn(w);else for(var R=c?1:2;R<r.length;R++){var ce=R===1?S:g[R-2];r[R].destructorFunction!==null&&r[R].destructorFunction(ce)}if(s)return r[0].fromWireType(pn)}return N(o.apply(null,_))}}function Fr(n,r,e){if(n[r].overloadTable===void 0){var o=n[r];n[r]=function(){return n[r].overloadTable.hasOwnProperty(arguments.length)||m("Function '"+e+"' called with an invalid number of arguments ("+arguments.length+") - expects one of ("+n[r].overloadTable+")!"),n[r].overloadTable[arguments.length].apply(this,arguments)},n[r].overloadTable=[],n[r].overloadTable[o.argCount]=o}}function jr(n,r,e){t.hasOwnProperty(n)?((e===void 0||t[n].overloadTable!==void 0&&t[n].overloadTable[e]!==void 0)&&m("Cannot register public name '"+n+"' twice"),Fr(t,n,n),t.hasOwnProperty(e)&&m("Cannot register multiple overloads of a function with the same number of arguments ("+e+")!"),t[n].overloadTable[e]=r):(t[n]=r,e!==void 0&&(t[n].numArguments=e))}function xr(n,r){for(var e=[],o=0;o<n;o++)e.push(p[(r>>2)+o]);return e}function Ir(n,r,e){t.hasOwnProperty(n)||Fn("Replacing nonexistant public symbol"),t[n].overloadTable!==void 0&&e!==void 0?t[n].overloadTable[e]=r:(t[n]=r,t[n].argCount=e)}function Ur(n,r,e){var o=t["dynCall_"+n];return e&&e.length?o.apply(null,[r].concat(e)):o.call(null,r)}function Or(n,r,e){return n.includes("j")?Ur(n,r,e):z(r).apply(null,e)}function Dr(n,r){var e=[];return function(){e.length=arguments.length;for(var o=0;o<arguments.length;o++)e[o]=arguments[o];return Or(n,r,e)}}function B(n,r){function e(){return n.includes("j")?Dr(n,r):z(r)}n=h(n);var o=e();return typeof o!="function"&&m("unknown function pointer with signature "+n+": "+r),o}var On=void 0;function Dn(n){var r=Vn(n),e=h(r);return W(r),e}function Hr(n,r){var e=[],o={};function a(u){o[u]||I[u]||(Z[u]?Z[u].forEach(a):(e.push(u),o[u]=!0))}throw r.forEach(a),new On(n+": "+e.map(Dn).join([", "]))}function Vr(n,r,e,o,a,u){var c=xr(r,e);n=h(n),a=B(o,a),jr(n,function(){Hr("Cannot call "+n+" due to unbound types",c)},r-1),jn([],c,function(i){var f=[i[0],null].concat(i.slice(1));return Ir(n,Rr(n,f,null,a,u),r-1),[]})}function Mr(n,r,e){switch(r){case 0:return e?function(o){return j[o]}:function(o){return v[o]};case 1:return e?function(o){return O[o>>1]}:function(o){return L[o>>1]};case 2:return e?function(o){return p[o>>2]}:function(o){return k[o>>2]};default:throw new TypeError("Unknown integer type: "+n)}}function zr(n,r,e,o,a){r=h(r),a===-1&&(a=4294967295);var u=cn(e),c=function(s){return s};if(o===0){var i=32-8*e;c=function(s){return s<<i>>>i}}var f=r.includes("unsigned");E(n,{name:r,fromWireType:c,toWireType:function(s,l){if(typeof l!="number"&&typeof l!="boolean")throw new TypeError('Cannot convert "'+sn(l)+'" to '+this.name);if(l<o||l>a)throw new TypeError('Passing a number "'+sn(l)+'" from JS side to C/C++ side to an argument of type "'+r+'", which is outside the valid range ['+o+", "+a+"]!");return f?l>>>0:0|l},argPackAdvance:8,readValueFromPointer:Mr(r,u,o!==0),destructorFunction:null})}function Br(n,r,e){var o=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array][r];function a(u){var c=k,i=c[u>>=2],f=c[u+1];return new o(on,f,i)}E(n,{name:e=h(e),fromWireType:a,argPackAdvance:8,readValueFromPointer:a},{ignoreDuplicateRegistrations:!0})}function Nr(n,r){var e=(r=h(r))==="std::string";E(n,{name:r,fromWireType:function(o){var a,u=k[o>>2];if(e)for(var c=o+4,i=0;i<=u;++i){var f=o+4+i;if(i==u||v[f]==0){var s=yn(c,f-c);a===void 0?a=s:(a+="\0",a+=s),c=f+1}}else{var l=new Array(u);for(i=0;i<u;++i)l[i]=String.fromCharCode(v[o+4+i]);a=l.join("")}return W(o),a},toWireType:function(o,a){a instanceof ArrayBuffer&&(a=new Uint8Array(a));var u=typeof a=="string";u||a instanceof Uint8Array||a instanceof Uint8ClampedArray||a instanceof Int8Array||m("Cannot pass non-string to std::string");var c=(e&&u?function(){return Gn(a)}:function(){return a.length})(),i=nn(4+c+1);if(k[i>>2]=c,e&&u)Ln(a,i+4,c+1);else if(u)for(var f=0;f<c;++f){var s=a.charCodeAt(f);s>255&&(W(i),m("String has UTF-16 code units that do not fit in 8 bits")),v[i+4+f]=s}else for(f=0;f<c;++f)v[i+4+f]=a[f];return o!==null&&o.push(W,i),i},argPackAdvance:8,readValueFromPointer:Y,destructorFunction:function(o){W(o)}})}function qr(n,r,e){var o,a,u,c,i;e=h(e),r===2?(o=Jn,a=Xn,c=Yn,u=function(){return L},i=1):r===4&&(o=Zn,a=$n,c=Kn,u=function(){return k},i=2),E(n,{name:e,fromWireType:function(f){for(var s,l=k[f>>2],g=u(),_=f+4,w=0;w<=l;++w){var S=f+4+w*r;if(w==l||g[S>>i]==0){var b=o(_,S-_);s===void 0?s=b:(s+="\0",s+=b),_=S+r}}return W(f),s},toWireType:function(f,s){typeof s!="string"&&m("Cannot pass non-string to C++ string type "+e);var l=c(s),g=nn(4+l+r);return k[g>>2]=l>>i,a(s,g+4,l+r),f!==null&&f.push(W,g),g},argPackAdvance:8,readValueFromPointer:Y,destructorFunction:function(f){W(f)}})}function Lr(n,r,e,o,a,u){X[n]={name:h(r),rawConstructor:B(e,o),rawDestructor:B(a,u),fields:[]}}function Gr(n,r,e,o,a,u,c,i,f,s){X[n].fields.push({fieldName:h(r),getterReturnType:e,getter:B(o,a),getterContext:u,setterArgumentType:c,setter:B(i,f),setterContext:s})}function Jr(n,r){E(n,{isVoid:!0,name:r=h(r),argPackAdvance:0,fromWireType:function(){},toWireType:function(e,o){}})}function Xr(n){n>4&&(y[n].refcount+=1)}var Yr={};function Zr(n){var r=Yr[n];return r===void 0?h(n):r}function $r(n){return $.toHandle(Zr(n))}function Kr(n,r){var e=I[n];return e===void 0&&m(r+" has unknown type "+Dn(n)),e}function Qr(n,r){var e=(n=Kr(n,"_emval_take_value")).readValueFromPointer(r);return $.toHandle(e)}function ne(){G("")}function re(n,r,e){v.copyWithin(n,r,r+e)}function ee(n){try{return tn.grow(n-on.byteLength+65535>>>16),Tn(tn.buffer),1}catch{}}function te(n){var r=v.length,e=2147483648;if((n>>>=0)>e)return!1;for(var o=1;o<=4;o*=2){var a=r*(1+.2/o);if(a=Math.min(a,n+100663296),ee(Math.min(e,Qn(Math.max(n,a),65536))))return!0}return!1}var K={mappings:{},buffers:[null,[],[]],printChar:function(n,r){var e=K.buffers[n];r===0||r===10?((n===1?Nn:U)(gn(e,0)),e.length=0):e.push(r)},varargs:void 0,get:function(){return K.varargs+=4,p[K.varargs-4>>2]},getStr:function(n){return yn(n)},get64:function(n,r){return n}};function oe(n){return 0}function ie(n,r,e,o,a){}function ae(n,r,e,o){for(var a=0,u=0;u<e;u++){var c=p[r>>2],i=p[r+4>>2];r+=8;for(var f=0;f<i;f++)K.printChar(n,v[c+f]);a+=i}return p[o>>2]=a,0}function ue(n){}Rn=t.InternalError=un(Error,"InternalError"),Ar(),In=t.BindingError=un(Error,"BindingError"),kr(),On=t.UnboundTypeError=un(Error,"UnboundTypeError");var Hn={__call_sighandler:lr,__cxa_allocate_exception:pr,__cxa_atexit:dr,__cxa_throw:hr,_embind_finalize_value_object:wr,_embind_register_bigint:br,_embind_register_bool:Tr,_embind_register_emval:Er,_embind_register_float:Sr,_embind_register_function:Vr,_embind_register_integer:zr,_embind_register_memory_view:Br,_embind_register_std_string:Nr,_embind_register_std_wstring:qr,_embind_register_value_object:Lr,_embind_register_value_object_field:Gr,_embind_register_void:Jr,_emval_decref:Un,_emval_incref:Xr,_emval_new_cstring:$r,_emval_take_value:Qr,abort:ne,emscripten_memcpy_big:re,emscripten_resize_heap:te,fd_close:oe,fd_seek:ie,fd_write:ae,setTempRet0:ue};sr(),t.___wasm_call_ctors=function(){return(t.___wasm_call_ctors=t.asm.__wasm_call_ctors).apply(null,arguments)};var Q,nn=t._malloc=function(){return(nn=t._malloc=t.asm.malloc).apply(null,arguments)},W=t._free=function(){return(W=t._free=t.asm.free).apply(null,arguments)},Vn=t.___getTypeName=function(){return(Vn=t.___getTypeName=t.asm.__getTypeName).apply(null,arguments)};function ln(n){function r(){Q||(Q=!0,t.calledRun=!0,hn||(rr(),H(t),t.onRuntimeInitialized&&t.onRuntimeInitialized(),er()))}x>0||(nr(),x>0||(t.setStatus?(t.setStatus("Running..."),setTimeout(function(){setTimeout(function(){t.setStatus("")},1),r()},1)):r()))}if(t.___embind_register_native_and_builtin_types=function(){return(t.___embind_register_native_and_builtin_types=t.asm.__embind_register_native_and_builtin_types).apply(null,arguments)},t.___errno_location=function(){return(t.___errno_location=t.asm.__errno_location).apply(null,arguments)},t.stackSave=function(){return(t.stackSave=t.asm.stackSave).apply(null,arguments)},t.stackRestore=function(){return(t.stackRestore=t.asm.stackRestore).apply(null,arguments)},t.stackAlloc=function(){return(t.stackAlloc=t.asm.stackAlloc).apply(null,arguments)},t.dynCall_vij=function(){return(t.dynCall_vij=t.asm.dynCall_vij).apply(null,arguments)},t.dynCall_jiji=function(){return(t.dynCall_jiji=t.asm.dynCall_jiji).apply(null,arguments)},M=function n(){Q||ln(),Q||(M=n)},t.run=ln,t.preInit)for(typeof t.preInit=="function"&&(t.preInit=[t.preInit]);t.preInit.length>0;)t.preInit.pop()();return ln(),A.ready},Mn.exports=zn;const le=fe({__proto__:null,default:rn},[rn]);export{le as i};
