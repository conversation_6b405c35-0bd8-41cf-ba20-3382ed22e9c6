<template>
  <svg aria-hidden="true">
    <use :href="symbolId" />
  </svg>
</template>

<script>
import { defineComponent, computed } from 'vue'

export default defineComponent({
  name: 'SvgIcon',
  props: {
    /** 相对于路径assets/icons/svg，如果下面还文件夹，比如assets/icons/svg/customsvg/pipe.svg,则对应name为： customsvg-pipe */
    name: {
      type: String,
      required: true
    }
    // color: {
    //   type: String,
    //   default: '#fff'
    // }
  },
  setup(props) {
    const symbolId = computed(() => `#icon-${props.name}`)
    return { symbolId }
  }
})
</script>
