package org.thingsboard.server.dao.fault;

import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.fault.FaultTaskM;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.Date;
import java.util.List;

/**
 * 班组
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-08-24
 */
public interface FaultTaskMService {
    PageData getList(String code, String name, String planName, String teamName, String userName, String status, String auditStatus, Date startStartTime, Date startEndTime, Date endStartTime, Date endEndTime, int page, int size, String tenantId);

    FaultTaskM getDetail(String mainId);

    FaultTaskM save(FaultTaskM faultTaskM);

    IstarResponse delete(List<String> ids);

    void reviewer(FaultTaskM faultTaskM);

    void changeStatus(FaultTaskM faultTaskM);

    List<FaultTaskM> findAll();

    List statistics(Long startTime, Long endTime, String tenantId);

    boolean checkAuditor(String id, String userId);
}
