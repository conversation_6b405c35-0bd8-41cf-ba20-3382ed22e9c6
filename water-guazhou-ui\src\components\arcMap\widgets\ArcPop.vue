<template>
  <div
    v-show="state.visible"
    ref="refContainer"
    class="arc-infowindow"
  >
    <slot> </slot>
  </div>
</template>
<script lang="ts" setup>
import Graphic from '@arcgis/core/Graphic'
import Point from '@arcgis/core/geometry/Point.js'
import PictureMarkerSymbol from '@arcgis/core/symbols/PictureMarkerSymbol'

const view: __esri.MapView | undefined = inject('view')
let graphic: __esri.Graphic | undefined
const refContainer = ref<HTMLDivElement>()
const props = defineProps<{
  markUrl?: string
  xoffset?: number
  yoffset?: number
  visible?: boolean
  x?: number
  y?: number
  longitude?: number
  latitude?: number
  markWidth?: number
  markHeight?: number
}>()
const state = reactive<{
  visible: boolean
}>({
  visible: !!props.visible
})
const emit = defineEmits(['toggled'])
const open = () => {
  state.visible = true
  highlight()
  emit('toggled', true)
}
const toggle = (flag?: boolean) => {
  state.visible = flag ?? !state.visible
  if (state.visible) {
    highlight()
  }
  emit('toggled', state.visible)
}
const close = () => {
  state.visible = false
  emit('toggled', false)
}
const highlight = () => {
  refContainer.value?.parentElement?.appendChild(refContainer.value)
}
const setPosition = (mapView = view, location?: { x?: number; y?: number; longitude?: number; latitude?: number }) => {
  if (!mapView) return
  location = location || position.value
  if (!location) return
  const point = new Point({
    ...location,
    spatialReference: mapView?.spatialReference
  })
  if (props.markUrl) {
    if (graphic) graphic.geometry = point
    else {
      graphic = new Graphic({
        geometry: point,
        symbol: new PictureMarkerSymbol({
          url: props.markUrl,
          xoffset: props.xoffset,
          yoffset: props.yoffset,
          width: props.markWidth ?? 20,
          height: props.markHeight ?? 20
        })
      })
      view?.graphics.add(graphic)
    }
  }

  if (!refContainer.value) return
  const screenPoint = mapView?.toScreen(point)
  refContainer.value.style.left = (screenPoint?.x || -10000) + (props.xoffset || 0) + 'px'
  refContainer.value.style.top = (screenPoint?.y || -10000) + (props.yoffset || -10) + 'px'
}
const position = computed(() => {
  return {
    x: props.x,
    y: props.y,
    longitude: props.longitude,
    latitude: props.latitude
  }
})
watch(
  () => position.value,
  (newVal: any) => {
    if (!newVal) return
    setPosition()
  },
  {
    immediate: true
  }
)
onBeforeUnmount(() => {
  graphic && view?.graphics.remove(graphic)
})
defineExpose({
  open,
  close,
  toggle,
  setPosition
})
</script>
<style lang="scss" scoped>
.dark {
  .arc-infowindow {
    // background-color: #383e53;
  }
}
.arc-infowindow {
  // background-color: #fff;
  // user-select: none;
  // transform: translateX(calc(-100% - 20px)) translateY(-100%);
  position: absolute;
  // border-radius: 16px 16px 0 16px;
  // box-shadow: 0 8px 16px rgba(0, 0, 0, 0.25);
  // padding: 8px;
}
</style>
