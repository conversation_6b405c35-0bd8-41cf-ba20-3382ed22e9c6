/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.menu;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardErrorCode;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.MenuPoolId;
import org.thingsboard.server.common.data.id.MenuTenantId;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.menu.Menu;
import org.thingsboard.server.common.data.menu.MenuPool;
import org.thingsboard.server.common.data.menu.MenuPoolVO;
import org.thingsboard.server.common.data.menu.MenuTenant;
import org.thingsboard.server.dao.model.ModelConstants;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Transactional
@Slf4j
public class MenuTenantServiceImpl implements MenuTenantService {

    @Autowired
    private MenuTenantDao menuTenantDao;
    @Autowired
    private MenuPoolDao menuPoolDao;
    @Autowired
    private MenuPoolService menuPoolService;
    @Autowired
    private MenuCustomerService menuCustomerService;

    @Override
    public List<MenuTenant> saveMenuTenant(List<MenuPoolId> menuPoolIdList, TenantId tenantId) throws ThingsboardException {
        log.trace("Executing saveTenant menuPoolIdList = [{}], tenantId = [{}]", menuPoolIdList, tenantId);
        // 删除该租户下原有菜单
        menuTenantDao.deleteByTenantId(tenantId);
        // 查询扩展菜单
        /*List<MenuTenant> extensionMenu = menuTenantDao.findByExtensionMenu(tenantId);
        if (!extensionMenu.isEmpty()) {// 从menuPoolIdList中剔除已经存在的menuPoolId
            List<MenuPoolId> poolIds = extensionMenu.stream().map(MenuTenant::getMenuPoolId).collect(Collectors.toList());
            menuPoolIdList = menuPoolIdList.stream().filter(menuPoolId -> !poolIds.contains(menuPoolId)).collect(Collectors.toList());
        }*/

        Map<String, MenuTenant> map = new HashMap<>();
        for (MenuPoolId menuPoolId : menuPoolIdList) {
            // 查询当前菜单的信息
            MenuPool menuPool = menuPoolDao.findById(menuPoolId.getId());
            if (menuPool == null) {
                throw new ThingsboardException("This menuId does not exist",
                        ThingsboardErrorCode.BAD_REQUEST_PARAMS);
            }
            if (menuPool.getParentId().isRootUid()) {// 如果为一级菜单
                // 仅保存当前菜单的menuPoolId
                MenuTenant menuTenant = getMenuTenant(tenantId, menuPool);
                // 放入map中
                map.put(menuPool.getId().getId().toString(), menuTenant);
            } else {// 为二级菜单
                saveMenuTenantRecursion(tenantId, menuPool, map);
            }
        }
        Collection<MenuTenant> values = map.values();
        for (MenuTenant value : values) {
            value.setId(menuTenantDao.save(value).getId());
        }
        ArrayList<MenuTenant> menuTenants = new ArrayList<>(values);
        menuCustomerService.saveMenuCustomer(menuTenants, tenantId);

        return menuTenants;
    }

    private void saveMenuTenantRecursion(TenantId tenantId, MenuPool menuPool, Map<String, MenuTenant> map) {
        MenuTenant child = getMenuTenant(tenantId, menuPool);
        map.put(menuPool.getId().getId().toString(), child);

        if (!map.containsKey(menuPool.getParentId().getId().toString())) {// 若该父菜单不存在
            // 保存该菜单的父菜单
            MenuPool parentMenuPool = menuPoolDao.findById(menuPool.getParentId().getId());
            MenuTenant parent = getMenuTenant(tenantId, parentMenuPool);
            map.put(menuPool.getParentId().getId().toString(), parent);
        }

        // 查询是否还有子菜单
        List<MenuPool> menuPoolChildren = menuPoolService.findByParentId(menuPool.getId());
        if (menuPoolChildren != null) {
            for (MenuPool menuPoolChild : menuPoolChildren) {
                saveMenuTenantRecursion(tenantId, menuPoolChild, map);
            }
        }
    }

    private MenuTenant getMenuTenant(TenantId tenantId, MenuPool menuPool) {
        MenuTenant child = new MenuTenant(tenantId);
        child.setMenuPoolId(menuPool.getId());
        if (menuPool.getStatus() != null && menuPool.getStatus() == DataConstants.MENU_POOL_STATUS_IS_EXTENSION_MENU) {
            child.setIsExtensionMenu(DataConstants.IS_EXTENSION_MENU_TRUE);
        } else {
            child.setIsExtensionMenu(DataConstants.IS_EXTENSION_MENU_FALSE);
        }
        return child;
    }

    @Override
    public Menu findMenuById(MenuTenantId menuTenantId) {
        log.trace("Executing findMenuTenantById [{}]", menuTenantId);
        MenuPool menuPool = menuTenantDao.findById(menuTenantId);

        return menuPoolService.MenuPoolToMenu(menuPool);
    }

    @Override
    public List<Menu> findMenuByTenantId(TenantId tenantId) {
        log.trace("Executing findMenuTenantByTenantId [{}]", tenantId);
        List<Menu> result = new ArrayList<>();
        // 获取一级菜单ID
        MenuPoolId rootId = new MenuPoolId(ModelConstants.MENU_POOL_ROOT);
        // 获取该租户的所有菜单
        List<MenuPool> menuList = menuTenantDao.findMenuByTenantId(tenantId);
        // 筛选出一级菜单
        List<MenuPool> pMenuList = menuList.stream()
                .filter(menuPool -> menuPool.getParentId().equals(rootId))
                .collect(Collectors.toList());
        // 筛选出二级菜单
        List<MenuPool> cMenuList = menuList.stream()
                .filter(menuPool -> !menuPool.getParentId().equals(rootId))
                .collect(Collectors.toList());
        // 遍历一级菜单,设置其二级菜单
        for (MenuPool parent : pMenuList) {
            Menu menu = menuPoolService.MenuPoolToMenu(parent);
            // 筛选出该一级菜单下的二级菜单
            List<MenuPool> children = cMenuList.stream()
                    .filter(menuPool -> menuPool.getParentId().equals(parent.getId()))
                    .collect(Collectors.toList());
            if (children.size() > 0) {
                menu.setChildren(menuPoolService.MenuPoolListToMenuList(children));
            }
            result.add(menu);
        }

        return result;
    }

    @Override
    public List<MenuPoolVO> findMenuPoolVOByTenantId(TenantId tenantId) {
        List<MenuPoolVO> result = new ArrayList<>();
        // 获取一级菜单ID
        MenuPoolId rootId = new MenuPoolId(ModelConstants.MENU_POOL_ROOT);
        // 获取该租户的所有菜单
        List<MenuPool> menuList = menuTenantDao.findMenuByTenantId(tenantId);
        // 筛选出一级菜单
        List<MenuPool> pMenuList = menuList.stream()
                .filter(menuPool -> menuPool.getParentId().equals(rootId))
                .collect(Collectors.toList());
        // 筛选出二级菜单
        List<MenuPool> cMenuList = menuList.stream()
                .filter(menuPool -> !menuPool.getParentId().equals(rootId))
                .collect(Collectors.toList());
        // 遍历一级菜单,设置其二级菜单
        for (MenuPool parent : pMenuList) {
            MenuPoolVO menuPoolVO = MenuPoolVO.toMenuPoolVO(parent);
            // 筛选出该一级菜单下的二级菜单
            List<MenuPool> children = cMenuList.stream()
                    .filter(menuPool -> menuPool.getParentId().equals(parent.getId()))
                    .collect(Collectors.toList());
            if (children.size() > 0) {
                menuPoolVO.setChildren(MenuPoolVO.toMenuPoolVOList(children));
            }
            result.add(menuPoolVO);
        }

        return result;
    }

    @Override
    public List<String> getTreeByTenantId(TenantId tenantId) {
        List<String> result = new ArrayList<>();
        List<String> uuidList = menuTenantDao.getTreeByTenantId(tenantId);
        uuidList.forEach(uuid -> result.add(new MenuPoolId(UUIDConverter.fromString(uuid)).getId().toString()));

        return result;
    }

    @Override
    public List<MenuTenant> findAll(TenantId tenantId) {
        return menuTenantDao.findByTenantId(tenantId);
    }
}
