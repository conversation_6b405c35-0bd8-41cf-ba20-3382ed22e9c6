/**
 * Copyright © 2016-2019 The Thingsboard Authors
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.device;

import com.google.common.util.concurrent.ListenableFuture;
import org.thingsboard.server.common.data.Device;
import org.thingsboard.server.common.data.EntitySubtype;
import org.thingsboard.server.common.data.User;
import org.thingsboard.server.common.data.device.DeviceControl;
import org.thingsboard.server.common.data.device.DeviceFullData;
import org.thingsboard.server.common.data.device.DeviceSearchQuery;
import org.thingsboard.server.common.data.id.CustomerId;
import org.thingsboard.server.common.data.id.DeviceId;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.common.data.page.TextPageData;
import org.thingsboard.server.common.data.page.TextPageLink;
import org.thingsboard.server.dao.model.DTO.DevicePartitionDTO;
import org.thingsboard.server.dao.model.request.PartitionMountRequest;

import java.util.List;

public interface DeviceService {

    Device findDeviceById(TenantId tenantId, DeviceId deviceId);

    Device findDeviceById(DeviceId deviceId);

    ListenableFuture<Device> findDeviceByIdAsync(TenantId tenantId, DeviceId deviceId);

    ListenableFuture<Device> findDeviceByIdAsync(DeviceId deviceId);

    Device findDeviceByTenantIdAndName(TenantId tenantId, String name);

    Device saveDevice(Device device);

    List<Device> findAll();

    Device assignDeviceToCustomer(TenantId tenantId, DeviceId deviceId, CustomerId customerId);

    Device unassignDeviceFromCustomer(TenantId tenantId, DeviceId deviceId);

    void deleteDevice(TenantId tenantId, DeviceId deviceId);

    TextPageData<Device> findDevicesByTenantId(TenantId tenantId, TextPageLink pageLink);

    TextPageData<Device> findDevicesByTenantIdAndType(TenantId tenantId, String type, TextPageLink pageLink);

    ListenableFuture<List<Device>> findDevicesByTenantIdAndIdsAsync(TenantId tenantId, List<DeviceId> deviceIds);

    void deleteDevicesByTenantId(TenantId tenantId);

    TextPageData<Device> findDevicesByTenantIdAndCustomerId(TenantId tenantId, CustomerId customerId, TextPageLink pageLink);

    TextPageData<Device> findDevicesByTenantIdAndCustomerIdAndType(TenantId tenantId, CustomerId customerId, String type, TextPageLink pageLink);

    ListenableFuture<List<Device>> findDevicesByTenantIdCustomerIdAndIdsAsync(TenantId tenantId, CustomerId customerId, List<DeviceId> deviceIds);

    void unassignCustomerDevices(TenantId tenantId, CustomerId customerId);

    ListenableFuture<List<Device>> findDevicesByQuery(TenantId tenantId, DeviceSearchQuery query);

    ListenableFuture<List<EntitySubtype>> findDeviceTypesByTenantId(TenantId tenantId);

    List<Device> findAllByTenantId(TenantId tenantId, String key);

    List<Device> findAllByTenantId(TenantId tenantId);

    List<Device> findGateWayByTenantId(TenantId tenantId);

    List<Device> findDeviceByGateWayId(DeviceId gateWayId);

    void updateDeviceOnline(DeviceId deviceId);

    Device findDeviceByAdditionalInfo(String gatewayId);

    List<Device> findByProjectId(String projectId, TenantId tenantId);

    List<Device> findByTemplateId(String id);

    List<Device> findGateWayByProjectId(String projectId);

    List<Device> findAllGateway();

    List<Device> findAllGatewayByTenantId(String tenantId);

    List<Device> findAllGatewayByProjectId(String projectId);

    long findAllDevice();

    long findAllDeviceByProjectId(String projectId);

    long findAllDeviceByTenantId(TenantId tenantId);

    long findAllModbusDevice();

    long findAllModbusDeviceByProjectId(String projectId);

    long findAllModbusDeviceByTenantId(TenantId tenantId);

    long findAllDtuDevice();

    long findAllDtuDeviceByTenantId(String tenantId);

    long findAllDtuDeviceByProjectId(String projectId);

    long findAllOnlineDevice();

    long findAllOnlineGateway();

    long findAllOfflineGateway();

    long findAllOnlineDeviceByTenantId(TenantId tenantId);

    long findAllOfflineGatewayByTenantId(TenantId tenantId);

    long findAllOnlineGatewayByTenantId(TenantId tenantId);

    long findAllGatewayByTenantId(TenantId tenantId);

    long findAllOnlineDeviceByProjectId(String projectId);

    long findAllOfflineGatewayByProject(String projectId);

    long findAllOnlineGatewayByProject(String projectId);

    long findAllOffLineDevice();

    long findAllOffLineDeviceByTenantId(TenantId tenantId);

    long findAllOffLineDeviceByProjectId(String projectId);

    List<Device> findAllByProjectId(String project, String key);

    void deleteGateway(String gatewayId);

    List<Device> findByProjectIdAndType(String projectId, String type);

    List<Device> findAllByGateway(String gatewayId, String key);

    List<Device> findByTenantId(TenantId tenantId);

    List<Device> getMqttDeviceAndDevices(TenantId tenantId);

    void deleteByProjectId(String projectId);

    List<Device> findByTemplateId(String id, String attribute);

    List<Device> findCloudDeviceList();

    List<Device> getGatewayByTenantId(TenantId id);

    List<Device> getCloudDeviceList(List<Device> deviceList, TenantId tenantId);

    List<Device> findByIdIn(List<String> deviceIdList, String tenantId);

    List<Device> findByProjectIdAndTypeAndName(String projectId, String type, String name);

    List<Device> findByForeignKey(String deviceId);

    List<Device> findByForeignKeyIn(List<String> deviceCodeList);

    List<Device> findAll(TenantId tenantId);

    List<DeviceFullData> getDeviceFullData(TenantId tenantId, DeviceId deviceId, String group);

    Object deviceControl(DeviceControl control, User currentUser);

    List<Device> findByDeviceTypeName(String deviceTypeName, TenantId tenantId);

    Device getDeviceInfo(DeviceId id);

    PageData<DevicePartitionDTO> getPartitionDevice(PartitionMountRequest request);

    PageData<DevicePartitionDTO> getWaterFactoryDevice(PartitionMountRequest request);
}
