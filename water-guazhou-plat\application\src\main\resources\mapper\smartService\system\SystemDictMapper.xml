<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.smartService.system.SystemDictMapper">
    <select id="getList" resultType="org.thingsboard.server.dao.model.sql.smartService.system.SystemDict">
        select a.*, b.name as typeName
        from tb_service_system_dict a left join tb_service_knowledge_base_type b on a.type_id = b.id
        where 1 = 1
        <if test="typeIds.size() > 0">
            and b.id in 
            <foreach collection="typeIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="title != null and title != ''">
            and a.title like '%' || #{title} || '%'
        </if>
        <if test="content != null and title != ''">
            and a.content like '%' || #{content} || '%'
        </if>
        <if test="startTime != null">
            and a.create_time &gt;= to_timestamp(#{startTime} / 1000)
        </if>
        <if test="endTime != null">
            and a.create_time &lt;= to_timestamp(#{endTime} / 1000)
        </if>
        order by a.create_time desc
        offset (#{page} - 1) * #{size}  limit #{size}
    </select>

    <select id="getListCount" resultType="int">
        select count(*)
        from tb_service_knowledge_base a left join tb_service_knowledge_base_type b on a.type_id = b.id
        where 1 = 1
        <if test="typeIds.size() > 0">
            and b.id in
            <foreach collection="typeIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="title != null and title != ''">
            and a.title like '%' || #{title} || '%'
        </if>
        <if test="content != null and title != ''">
            and a.content like '%' || #{content} || '%'
        </if>
        <if test="startTime != null">
            and a.create_time &gt;= to_timestamp(#{startTime} / 1000)
        </if>
        <if test="endTime != null">
            and a.create_time &lt;= to_timestamp(#{endTime} / 1000)
        </if>
    </select>
    <select id="selectAreaList" resultType="java.util.Map">
        select id, name from tb_service_system_dict where pid = '2d28676d2f3d1541c23c306bc895e276' and tenant_id = #{tenantId}
    </select>
</mapper>