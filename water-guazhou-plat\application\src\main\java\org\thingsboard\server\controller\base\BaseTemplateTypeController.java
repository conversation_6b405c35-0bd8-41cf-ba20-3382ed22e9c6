package org.thingsboard.server.controller.base;

import java.util.List;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.thingsboard.server.dao.base.IBaseTemplateTypeService;
import org.thingsboard.server.dao.model.sql.base.BaseTemplateType;
import org.thingsboard.server.dao.util.imodel.query.base.BaseTemplateTypePageRequest;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;
import org.thingsboard.server.service.aspect.annotation.MonitorPerformance;

/**
 * 平台管理-模型类型Controller
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
@Api(tags = "平台管理-模型类型")
@RestController
@RequestMapping("api/base/template/type")
public class BaseTemplateTypeController extends BaseController {

    @Autowired
    private IBaseTemplateTypeService baseTemplateTypeService;

    /**
     * 查询平台管理-模型类型列表
     */
    @MonitorPerformance(description = "平台管理-查询模型类型列表")
    @ApiOperation(value = "查询模型类型列表")
    @GetMapping("/list")
    public IstarResponse list(BaseTemplateTypePageRequest baseTemplateType) {
        return IstarResponse.ok(baseTemplateTypeService.selectBaseTemplateTypeList(baseTemplateType));
    }

    /**
     * 获取平台管理-模型类型详细信息
     */
    @MonitorPerformance(description = "平台管理-查询模型类型详情")
    @ApiOperation(value = "查询模型类型详情")
    @GetMapping(value = "/getDetail")
    public IstarResponse getInfo(String id) {
        return IstarResponse.ok(baseTemplateTypeService.selectBaseTemplateTypeById(id));
    }

    /**
     * 新增平台管理-模型类型
     */
    @MonitorPerformance(description = "平台管理-新增模型类型")
    @ApiOperation(value = "新增模型类型")
    @PostMapping("/add")
    public IstarResponse add(@RequestBody BaseTemplateType baseTemplateType) {
        return IstarResponse.ok(baseTemplateTypeService.insertBaseTemplateType(baseTemplateType));
    }

    /**
     * 修改平台管理-模型类型
     */
    @MonitorPerformance(description = "平台管理-修改模型类型")
    @ApiOperation(value = "修改模型类型")
    @PostMapping("/edit")
    public IstarResponse edit(@RequestBody BaseTemplateType baseTemplateType) {
        return IstarResponse.ok(baseTemplateTypeService.updateBaseTemplateType(baseTemplateType));
    }

    /**
     * 删除平台管理-模型类型
     */
    @MonitorPerformance(description = "平台管理-删除模型类型")
    @ApiOperation(value = "删除模型类型")
    @DeleteMapping("/deleteIds")
    public IstarResponse remove(@RequestBody List<String> ids) {
        return IstarResponse.ok(baseTemplateTypeService.deleteBaseTemplateTypeByIds(ids));
    }
}
