package org.thingsboard.server.controller.base;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.dao.model.sql.QueryTemplate;
import org.thingsboard.server.dao.settings.QueryTemplateService;

import java.util.List;

@RestController
@RequestMapping("api/queryTemplate")
public class QueryTemplateController extends BaseController {

    @Autowired
    private QueryTemplateService queryTemplateService;

    @GetMapping
    public List<QueryTemplate> list() throws ThingsboardException {
        return queryTemplateService.findList(getTenantId());
    }

    @GetMapping("{type}")
    public QueryTemplate findByType(@PathVariable String type) throws ThingsboardException {
        return queryTemplateService.findByType(type, getTenantId());
    }

    @PostMapping
    public List<QueryTemplate> saveAll(@RequestBody List<QueryTemplate> entityList) throws ThingsboardException {
        for (QueryTemplate queryTemplate : entityList) {
            queryTemplate.setTenantId(UUIDConverter.fromTimeUUID(getTenantId().getId()));
        }
        queryTemplateService.saveAll(entityList);
        return entityList;
    }

}
