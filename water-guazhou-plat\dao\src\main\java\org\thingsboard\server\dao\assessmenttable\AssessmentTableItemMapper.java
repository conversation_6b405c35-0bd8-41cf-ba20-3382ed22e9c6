package org.thingsboard.server.dao.assessmenttable;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.assessmenttable.AssessmentTableItemEntity;

import java.util.List;
import java.util.UUID;

/**
 * 考核表明细项Mapper接口
 */
@Mapper
public interface AssessmentTableItemMapper extends BaseMapper<AssessmentTableItemEntity> {

    /**
     * 根据考核表ID查询明细项列表
     *
     * @param assessmentTableId 考核表ID
     * @return 明细项列表
     */
    List<AssessmentTableItemEntity> selectByAssessmentTableId(@Param("assessmentTableId") String assessmentTableId);
    
    /**
     * 根据考核表ID删除明细项
     *
     * @param assessmentTableId 考核表ID
     * @return 删除数量
     */
    int deleteByAssessmentTableId(@Param("assessmentTableId") String assessmentTableId);
    
    /**
     * 批量删除明细项（根据考核表ID列表）
     *
     * @param assessmentTableIds 考核表ID列表
     * @return 删除数量
     */
    int batchDeleteByAssessmentTableIds(@Param("assessmentTableIds") List<String> assessmentTableIds);
} 