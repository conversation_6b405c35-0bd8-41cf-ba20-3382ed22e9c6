package org.thingsboard.server.dao.smartPipe;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.request.PartitionMountRequest;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.PartitionValve;
import org.thingsboard.server.dao.sql.smartPipe.PartitionValveMapper;

import java.util.Date;
import java.util.List;

/**
 *
 */
@Service
public class PartitionValveServiceImpl implements PartitionValveService {

    @Autowired
    private PartitionValveMapper partitionValveMapper;

    @Override
    public PartitionValve save(PartitionValve partitionValve) {
        if (StringUtils.isBlank(partitionValve.getId())) {
            partitionValve.setCreateTime(new Date());
            partitionValveMapper.insert(partitionValve);
        } else {
            partitionValveMapper.updateById(partitionValve);
        }
        return partitionValve;
    }


    @Override
    public PageData<PartitionValve> getList(PartitionMountRequest request) {
        IPage<PartitionValve> page = new Page<>(request.getPage(), request.getSize());
        IPage<PartitionValve> result = partitionValveMapper.getList(page, request);
        result.getRecords().stream().forEach(a -> {
            try {
                a.setTypeName(DataConstants.PARTITION_VALUE_TYPE.getByValue(a.getType()).getName());
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
        return new PageData<>(result.getTotal(), result.getRecords());
    }

    @Override
    public void delete(List<String> ids) {
        partitionValveMapper.deleteBatchIds(ids);
    }

}
