import{aQ as K,T as P,R as b}from"./index-r0dFAfgr.js";import{i as F,x as $,s as u,u as R}from"./Point-WxyopZva.js";import{i as h}from"./originUtils-DOOsZebp.js";import{aF as M,aG as y,H as q,aH as D,aI as G,aJ as H,aK as U,aL as v,aM as d}from"./MapView-DaoQedLH.js";import{r as Y}from"./fetchService-B3xiPs3_.js";import{o as g}from"./jsonContext-C-xrKYgv.js";import"./multiOriginJSONSupportUtils-C0wm8_Yw.js";import"./widget-BcWKanF2.js";import"./pe-B8dP0-Ut.js";const j=F.getLogger("esri.layers.FeatureLayer"),f="Feature Service";function p(e,a){return`Layer (title: ${e.title}, id: ${e.id}) of type '${e.declaredClass}' ${a}`}function x(e,a){if(a.type!==f)throw new u("feature-layer:portal-item-wrong-type",p(e,`should have portal item of type "${f}"`))}async function J(e){if(await e.load(),M(e))throw new u("feature-layer:save",p(e,"using an in-memory source cannot be saved to a portal item"))}function B(e,a){let t=(e.messages??[]).filter(({type:r})=>r==="error").map(({name:r,message:s,details:o})=>new u(r,s,o));if(a!=null&&a.ignoreUnsupported&&(t=t.filter(({name:r})=>r!=="layer:unsupported"&&r!=="symbol:unsupported"&&r!=="symbol-layer:unsupported"&&r!=="property:unsupported"&&r!=="url:unsupported")),t.length>0)throw new u("feature-layer:save","Failed to save feature layer due to unsupported or invalid content. See 'details.errors' for more detailed information",{errors:t})}async function T(e,a,t){"beforeSave"in e&&typeof e.beforeSave=="function"&&await e.beforeSave();const r=e.write({},a);return B(a,t),r}function E(e){const{layer:a,layerJSON:t}=e;return a.isTable?{layers:[],tables:[t]}:{layers:[t],tables:[]}}function L(e){y(e,d.JSAPI),e.typeKeywords&&(e.typeKeywords=e.typeKeywords.filter((a,t,r)=>r.indexOf(a)===t))}function C(e){const a=e.portalItem;if(!a)throw j.error("save: requires the portalItem property to be set"),new u("feature-layer:portal-item-not-set",p(e,"requires the portalItem property to be set"));if(!a.loaded)throw new u("feature-layer:portal-item-not-loaded",p(e,"cannot be saved to a portal item that does not exist or is inaccessible"));x(e,a)}async function _(e,a){return/\/\d+\/?$/.test(e.url??"")?E(a[0]):z(e,a)}async function z(e,a){const{layer:{url:t,customParameters:r,apiKey:s}}=a[0];let o=await e.fetchData("json");o&&o.layers!=null&&o.tables!=null||(o=await Q(o,{url:t??"",customParameters:r,apiKey:s},a.map(n=>n.layer.layerId)));for(const n of a)N(n.layer,n.layerJSON,o);return o}async function Q(e,a,t){var r,s;e||(e={}),(r=e).layers||(r.layers=[]),(s=e).tables||(s.tables=[]);const{url:o,customParameters:n,apiKey:l}=a,{serviceJSON:i,layersJSON:c}=await Y(o,{customParameters:n,apiKey:l}),m=I(e.layers,i.layers,t),w=I(e.tables,i.tables,t);e.layers=m.itemResources,e.tables=w.itemResources;const O=[...m.added,...w.added],A=c?[...c.layers,...c.tables]:[];return await k(e,O,o,A),e}function I(e,a,t){const r=K(e,a,(o,n)=>o.id===n.id);e=e.filter(o=>!r.removed.some(n=>n.id===o.id));const s=r.added.map(({id:o})=>({id:o}));return s.forEach(({id:o})=>{e.push({id:o})}),{itemResources:e,added:s.filter(({id:o})=>!t.includes(o))}}async function k(e,a,t,r){const s=a.map(({id:o})=>new q({url:t,layerId:o,sourceJSON:r.find(({id:n})=>n===o)}));await R(s.map(o=>o.load())),s.forEach(o=>{const{layerId:n,loaded:l,defaultPopupTemplate:i}=o;!l||P(i)||N(o,{id:n,popupInfo:i.toJSON()},e)})}function N(e,a,t){e.isTable?S(t.tables,a):S(t.layers,a)}function S(e,a){if(!e)return;const t=e.findIndex(({id:r})=>r===a.id);t===-1?e.push(a):e[t]=a}function V(e,a){var t,r;let s=D.from(a);return s.id&&(s=s.clone(),s.id=null),(t=s).type??(t.type=f),(r=s).portal??(r.portal=G.getDefault()),x(e,s),s}async function W(e,a){const{url:t,layerId:r,title:s,fullExtent:o,isTable:n}=e,l=H(t),i=b(l)&&l.serverType==="FeatureServer";a.url=i?t:`${t}/${r}`,a.title||(a.title=s),a.extent=null,!n&&b(o)&&(a.extent=await U(o)),v(a,d.METADATA),v(a,d.MULTI_LAYER),y(a,d.SINGLE_LAYER),n&&y(a,d.TABLE),L(a)}async function X(e,a,t){var s;const r=e.portal;await(r==null?void 0:r.signIn()),await((s=r==null?void 0:r.user)==null?void 0:s.addItem({item:e,data:a,folder:t==null?void 0:t.folder}))}const de=$(Z);async function Z(e,a){await J(e),C(e);const t=e.portalItem,r=g(t),s=await T(e,r,a),o=await _(t,[{layer:e,layerJSON:s}]);return L(t),await t.update({data:o}),h(r),t}const pe=$(ee);async function ee(e,a,t){await J(e);const r=V(e,a),s=g(r),o=E({layer:e,layerJSON:await T(e,s,t)});return await W(e,r),await X(r,o,t),e.portalItem=r,h(s),r}export{de as save,pe as saveAs};
