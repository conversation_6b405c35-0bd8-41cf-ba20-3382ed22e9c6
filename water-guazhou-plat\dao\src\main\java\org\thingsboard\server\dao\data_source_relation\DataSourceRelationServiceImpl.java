package org.thingsboard.server.dao.data_source_relation;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.dataSource.DataSourceService;

import java.util.LinkedHashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/7 17:21
 */
@Service
public class DataSourceRelationServiceImpl  implements DataSourceRelationService {


    @Autowired
    private DataSourceRelationDao dataSourceRelationDao;

    @Override
    public boolean mountRelation(String originateId, List<String> dataSourceId,String type) {
        return dataSourceRelationDao.mountRelation(originateId, dataSourceId,type);
    }

}
