import{z as t}from"./index-r0dFAfgr.js";const o=e=>t({url:"/api/deviceStorageJournal",method:"get",params:e}),i=e=>t({url:"/api/deviceStorageJournal/exportExcel",method:"get",responseType:"blob",params:e}),s=e=>t({url:"/api/deviceStorageJournal/rest",method:"get",params:e}),r=e=>t({url:"/api/deviceStorageJournal/restWithoutSplit",method:"get",params:e}),l=e=>t({url:"/api/deviceSettleJournal",method:"post",data:e}),c=e=>t({url:"/api/deviceSettleJournal",method:"get",params:e}),n=e=>t({url:"/api/deviceUsageJournal",method:"post",data:e}),p=e=>t({url:"/api/deviceUsageJournal",method:"get",params:e}),g=e=>t({url:"/api/deviceStorageJournal/exportDeviceLabelExcel",method:"get",responseType:"blob",params:e}),u=e=>t({url:`/api/deviceStorageJournal/${e}/detail`,method:"get"}),d=e=>t({url:`/api/fault/report/statistics/${e}`,method:"get"}),m=e=>t({url:`/api/fault/report/list/${e}`,method:"get",params:{page:"1",size:"99999"}}),h=e=>t({url:`/api/maintain/task/c/statistics/${e}`,method:"get"}),v=e=>t({url:`/api/maintain/plan/m/list/${e}`,method:"get",params:{page:"1",size:"99999"}}),J=e=>t({url:`/api/circuit/task/c/statistics/${e}`,method:"get"}),S=e=>t({url:`/api/circuit/plan/m/list/${e}`,method:"get",params:{page:"1",size:"99999"}});export{o as a,n as b,c,p as d,g as e,i as f,u as g,s as h,d as i,m as j,h as k,v as l,J as m,S as n,r as o,l as p};
