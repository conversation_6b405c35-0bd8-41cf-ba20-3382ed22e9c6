package org.thingsboard.server.controller.base;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.dao.model.sql.AssayRecord;
import org.thingsboard.server.dao.shuiwu.AssayRecordService;

import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("api/assayRecord")
public class AssayRecordController extends BaseController {

    @Autowired
    private AssayRecordService assayRecordService;

    @PostMapping
    public AssayRecord save(@RequestBody AssayRecord assayRecord) throws ThingsboardException {
        if (assayRecord.getTime() == null) {
            assayRecord.setTime(new Date());
        }
        assayRecord.setTenantId(UUIDConverter.fromTimeUUID(getTenantId().getId()));

        return assayRecordService.save(assayRecord);
    }

    @GetMapping("list")
    public List<AssayRecord> list(@RequestParam String stationId) {
        return assayRecordService.findListByStationId(stationId);
    }

    @GetMapping("listByTenant")
    public List<AssayRecord> list() throws ThingsboardException {
        return assayRecordService.findListByTenantId(getTenantId());
    }

    @GetMapping("waterQualityAnalysis")
    public Object waterQualityAnalysis(@RequestParam String stationId,
                                       @RequestParam Long start, @RequestParam Long end) {
        return assayRecordService.waterQualityAnalysis(stationId, start, end);
    }

}
