<!-- 隐患总览 -->
<template>
  <div class="wrapper">
    <CardSearch
      ref="refSearch"
      :config="SearchConfig"
    ></CardSearch>
    <CardTable
      ref="refTable"
      class="card-table"
      :config="TableConfig"
    ></CardTable>
    <SLDrawer
      ref="refdetail"
      :config="detailConfig"
    >
      <detail :id="selectedId"></detail>
    </SLDrawer>
  </div>
</template>
<script lang="ts" setup>
import {
  Download,
  // Filter,
  Refresh,
  Search as SearchIcon
} from '@element-plus/icons-vue'
import { ICardSearchIns, ICardTableIns, ISLDrawerIns } from '@/components/type'
import { GetWorkOrderOverview } from '@/api/patrol'
import useCTI from '@/hooks/CTI/useCTI'
import { PatrolTaskStatusConfig } from '../config'
import detail from '@/views/workorder/components/detail.vue'

const refTable = ref<ICardTableIns>()
const refSearch = ref<ICardSearchIns>()
const state = reactive<{
  orderTypes: NormalOption[]
}>({
  orderTypes: []
})
const refdetail = ref<ISLDrawerIns>()
// 明细弹框
const detailConfig = reactive<IDrawerConfig>({
  title: '流程明细',
  group: [],
  modalClass: 'lightColor'
})
const selectedId = ref<string>('')

const handleDetail = (row: any) => {
  selectedId.value = row.workOrderId || ''
  detailConfig.title = row.serialNo
  refdetail.value?.openDrawer()
  // router.push({
  //   name: 'WorkOrderDetail',
  //   query: {
  //     id: row.id
  //   }
  // })
}
const SearchConfig = reactive<ISearch>({
  defaultParams: {
    statusStage: ''
  },
  filters: [
    {
      type: 'radio-button',
      label: '事件状态',
      field: 'statusStage',
      options: [
        { label: '全部', value: '' },
        { label: '待处理', value: '0' },
        { label: '处理中', value: '1' },
        { label: '已处理', value: '2' }
      ]
    },
    {
      type: 'select',
      label: '事件类型',
      field: 'type',
      options: computed(() => state.orderTypes) as any,
      formatter: (val, row, config: any) => {
        return config.options?.find(item => item.value === val)?.label || val
      }
    },
    {
      type: 'input',
      field: 'keyword',
      label: '快速查找'
    }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        // {
        //   perm: true,
        //   svgIcon: shallowRef(Filter),
        //   isBlockBtn: true,
        //   type: 'default',
        //   click: () => {
        //     refSearch.value?.toggleMore()
        //   }
        // },
        {
          perm: true,
          text: '查询',
          svgIcon: shallowRef(SearchIcon),
          click: () => refreshData()
        },
        {
          perm: true,
          text: '重置',
          type: 'default',
          svgIcon: shallowRef(Refresh),
          click: () => {
            refSearch.value?.resetForm()
            refreshData()
          }
        },
        {
          perm: true,
          text: '导出',
          svgIcon: shallowRef(Download),
          type: 'default',
          click: () => {
            refTable.value?.exportTable()
          }
        }
      ]
    }
  ]
  // moreFilters: [
  // {
  //   type: 'datetimerange',
  //   label: '上报时间',
  //   field: 'date'
  // },

  // ]
})
const TableConfig = reactive<ITable>({
  pagination: {
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page || 1
      TableConfig.pagination.limit = size || 20
      refreshData()
    }
  },
  columns: [
    { minWidth: 120, label: '任务编号', prop: 'code' },
    { minWidth: 120, label: '工单编号', prop: 'workOrderCode' },
    { minWidth: 120, label: '类型', prop: 'type' },
    { minWidth: 120, label: '内容', prop: 'content' },
    { minWidth: 120, label: '上报人员', prop: 'creatorName' },
    { minWidth: 120, label: '上报时间', prop: 'createTime' },
    { minWidth: 120, label: '紧急程度', prop: 'level' },
    {
      minWidth: 120,
      label: '状态',
      prop: 'status',
      tag: true,
      tagType: computed(row => PatrolTaskStatusConfig[row.status]?.type) as any,
      // tagEffect: 'plain',
      // tagColor: row => {
      //   return PatrolTaskStatusConfig[row.status]?.color || ''
      // },
      formatter: row => {
        return PatrolTaskStatusConfig[row.status]?.text || row.status
      }
    }
  ],
  dataList: [],
  operations: [
    {
      perm: true,
      isTextBtn: true,
      text: '详情',
      click: row => handleDetail(row)
    }
  ]
})
const refreshData = () => {
  GetWorkOrderOverview(refSearch.value?.queryParams).then(res => {
    TableConfig.dataList = res.data.data?.data || []
    TableConfig.pagination.total = res.data.data?.total || 0
  })
}
const { getOrderTypeOption } = useCTI()
onMounted(async () => {
  refreshData()
  state.orderTypes = await getOrderTypeOption()
})
</script>
<style lang="scss" scoped></style>
