package org.thingsboard.server.dao.util.imodel.response.tree;

import org.springframework.cglib.proxy.MethodInterceptor;
import org.springframework.cglib.proxy.MethodProxy;
import org.thingsboard.server.dao.util.imodel.query.TreeQueryOptions;
import org.thingsboard.server.dao.util.imodel.response.ResponseMap;
import org.thingsboard.server.dao.util.imodel.response.Responsible;
import org.thingsboard.server.dao.util.imodel.response.model.ReturnHelper;
import org.thingsboard.server.dao.util.reflection.ReflectionUtils;

import java.lang.reflect.Method;
import java.util.Collections;
import java.util.List;

public class TreeableInterceptor implements MethodInterceptor {

    private static final String LAYER_METHOD_NAME = "layer";

    private static final String CHILDREN_METHOD_NAME = "children";

    private static final String POST_PROCESS_METHOD_NAME = "postProcess";

    private static final String GET_ID_METHOD_NAME = "getId";

    private static final String REST_METHOD_NAME = "rest";

    private static final String TAKE_METHOD_NAME = "take";

    private static final String OPTIONS_METHOD_NAME = "options";

    private static final String SIZE_METHOD_NAME = "size";

    private final Object entity;

    private TreeQueryOptions options;

    private int layer = 1;

    private int limit = Integer.MIN_VALUE;

    private List<TreeableEntityNode> nodes;


    public TreeableInterceptor(Object entity) {
        this.entity = entity;
    }

    @Override
    @SuppressWarnings("unchecked")
    public Object intercept(Object o, Method method, Object[] objects, MethodProxy methodProxy) throws Throwable {
        if (options == null) {
            options = TreeQueryOptions.create();
        }
        if (method.getDeclaringClass().equals(TreeableEntityNode.class)) {
            switch (method.getName()) {
                case LAYER_METHOD_NAME:
                    // 设置或获取layer
                    if (objects.length > 0)
                        layer = (int) objects[0] + 1;
                    else
                        return layer - 1;
                    break;

                case CHILDREN_METHOD_NAME:
                    // 设置子节点列表
                    this.nodes = (List<TreeableEntityNode>) objects[0];
                    if (options.prune()) {
                        // 剪枝
                        for (int i = this.nodes.size() - 1; i >= 0; i--) {
                            if (this.nodes.get(i).size() == 0 && this.nodes.get(i).layer() + 1 != options.maxDepth()) {
                                this.nodes.remove(i);
                            }
                        }
                    }
                    break;

                case GET_ID_METHOD_NAME:
                    // 获取对象id
                    return methodProxy.invoke(entity, objects);

                case REST_METHOD_NAME:
                    // 设置与查询剩余可使用的深度
                    if (objects.length > 0)
                        limit = (int) objects[0];
                    else
                        return limit;
                    break;

                case TAKE_METHOD_NAME:
                    // 拿取一次深度
                    return limit - 1;

                case SIZE_METHOD_NAME:
                    // 拿取一次深度
                    return this.nodes.size();

                case OPTIONS_METHOD_NAME:
                    // 装载树配置
                    this.options = (TreeQueryOptions) objects[0];
                    break;
            }
            return null;
        } else if (method.getDeclaringClass().equals(Responsible.class) && method.getName().equals(POST_PROCESS_METHOD_NAME)) {
            int index = layer - 1;
            ReturnHelper returnHelper = (ReturnHelper) objects[0];
            ResponseMap map = returnHelper.convertToMap(entity, objects[1]);
            map.putIfAbsent("layer", layer);

            // 映射一个统一的字段，方便前端渲染
            String mappingName = options.getMappingName(index);
            if (mappingName != null) {
                map.put("name", ReflectionUtils.getValue(ReflectionUtils.getField(entity.getClass(), mappingName), entity));
            }

            map.put("labelOfTree", options.labelOf(index));

            if (nodes == null || nodes.equals(Collections.emptyList()))
                return map;
            map.put("children", returnHelper.process(nodes, null));
            return map;
        }
        return methodProxy.invoke(entity, objects);
    }

}
