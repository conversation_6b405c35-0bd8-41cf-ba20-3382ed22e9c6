package org.thingsboard.server.dao.sql.smartOperation.construction.project;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoProjectArchive;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoProjectFiles;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoProjectArchivePageRequest;

@Mapper
public interface SoProjectArchiveMapper extends BaseMapper<SoProjectArchive> {
    IPage<SoProjectArchive> findByPage(SoProjectArchivePageRequest request);

    boolean update(SoProjectArchive entity);

    boolean save(SoProjectArchive entity);

    boolean updateFully(SoProjectArchive entity);

    boolean canArchive(@Param("projectCode") String projectCode, @Param("tenantId") String tenantId);

    SoProjectFiles findFilesByConstructionCode(@Param("projectCode") String projectCode,
                                               @Param("tenantId") String tenantId);

    String getIdByProjectCodeAndTenantId(@Param("projectCode") String projectCode, @Param("tenantId") String tenantId);


}
