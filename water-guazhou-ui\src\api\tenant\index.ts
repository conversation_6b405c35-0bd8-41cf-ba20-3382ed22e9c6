import Cookies from 'js-cookie';
import request from '@/plugins/axios';

// 获取账户下的企业
export function getCurrentTenantList() {
  return request({
    url: '/api/tenant/getCurrentTenantList',
    method: 'get'
  });
}

export function getCurTenant() {
  return Cookies.get('currentTenant');
}

export function setCurTenant(tenantId) {
  return Cookies.set('currentTenant', tenantId);
}

/** get Logo */
export function getLogo() {
  return request({
    url: '/api/tenant/getLogo',
    method: 'get'
  });
}

/** save Tenant */
// TODO: fix parameters
export function saveTenant(params) {
  return request({
    url: '/api/tenant',
    method: 'post',
    data: params
  });
}

/** get Tenants */
export function getTenants(params?: any) {
  return request({
    url: '/api/tenants',
    method: 'get',
    params: params || {
      limit: 9999
    }
  });
}

/** delete Tenants */
export function deleteTenants(tenantId) {
  return request({
    url: `/api/tenant/${tenantId}`,
    method: 'delete'
  });
}

/** 获取单独tenant信息 */
export function getTenantInfo(tenantId) {
  return request({
    url: `/api/tenant/${tenantId}`,
    method: 'get'
  });
}

// 获取账户下的企业
export function delTenant(tenantId) {
  return request({
    url: `/api/tenant/${tenantId}`,
    method: 'delete'
  });
}

// 获取所有企业和，自己拥有的企业 （市场推广
export function getAllTenantList(userId) {
  return request({
    url: `/api/tenant/otherTenantList/${userId}`,
    method: 'get'
  });
}

// 赋予企业给指定用户
export function setTenantToUser(data) {
  return request({
    url: `/api/tenant/setTenantToUser`,
    method: 'post',
    data
  });
}
