package org.thingsboard.server.dao.util.imodel.response.device;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.sql.purchase.DevicePurchaseItemMapper;
import org.thingsboard.server.dao.sql.supplier.SupplierMapper;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseTenantName;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseViaMapper;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.util.Date;

@Getter
@Setter
@ResponseEntity
public class DevicePurchaseInquiryItemResponse {
    // 设备询价ID
    private String id;

    // 采购单子表ID
    @ParseViaMapper(value = DevicePurchaseItemMapper.class, method = "getNameByItemId")
    private String purchaseDetailId;

    // 供应商ID
    @ParseViaMapper(SupplierMapper.class)
    private String supplierId;

    // 联系人
    private String contact;

    // 联系方式
    private String contactPhone;

    // 单价
    private Double price;

    // 询价日期
    private Date inquiryTime;

    // 意向供应商
    private Boolean intentionSupplier;

    // 附件
    private String file;

    // 租户ID
    @ParseTenantName
    private String tenantId;
}
