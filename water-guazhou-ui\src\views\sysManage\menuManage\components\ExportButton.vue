<template>
  <Button :config="btn"></Button>
</template>
<script lang="ts" setup>
import { ExportMenu } from '@/api/menu/source';

const btn = reactive<IButton>({
  perm: true,
  type: 'primary',
  isTextBtn: false,
  iconifyIcon: 'ep:download',
  text: '导出',
  click: () => handleExport()
});
const handleExport = () => {
  ExportMenu().then((res) => {
    const url = window.URL.createObjectURL(res.data);
    const link = document.createElement('a');
    link.style.display = 'none';
    link.href = url;
    link.setAttribute('download', `菜单信息.json`);
    document.body.appendChild(link);
    link.click();
  });
};
</script>
<style lang="scss" scoped></style>
