package org.thingsboard.server.dao.sql.base;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.base.BaseMapConfiguration;
import org.thingsboard.server.dao.util.imodel.query.base.BaseMapConfigurationPageRequest;

import java.util.List;

/**
 * 平台管理-底图配置Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-22
 */
@Mapper
public interface BaseMapConfigurationMapper extends BaseMapper<BaseMapConfiguration> {
    /**
     * 查询平台管理-底图配置
     *
     * @param id 平台管理-底图配置主键
     * @return 平台管理-底图配置
     */
    public BaseMapConfiguration selectBaseMapConfigurationById(String id);

    /**
     * 查询平台管理-底图配置列表
     *
     * @param baseMapConfiguration 平台管理-底图配置
     * @return 平台管理-底图配置集合
     */
    public IPage<BaseMapConfiguration> selectBaseMapConfigurationList(BaseMapConfigurationPageRequest baseMapConfiguration);

    /**
     * 新增平台管理-底图配置
     *
     * @param baseMapConfiguration 平台管理-底图配置
     * @return 结果
     */
    public int insertBaseMapConfiguration(BaseMapConfiguration baseMapConfiguration);

    /**
     * 修改平台管理-底图配置
     *
     * @param baseMapConfiguration 平台管理-底图配置
     * @return 结果
     */
    public int updateBaseMapConfiguration(BaseMapConfiguration baseMapConfiguration);

    /**
     * 删除平台管理-底图配置
     *
     * @param id 平台管理-底图配置主键
     * @return 结果
     */
    public int deleteBaseMapConfigurationById(String id);

    /**
     * 批量删除平台管理-底图配置
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBaseMapConfigurationByIds(@Param("array") List<String> ids);
}
