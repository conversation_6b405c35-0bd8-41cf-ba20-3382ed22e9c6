import{_ as r}from"./CardTable-rdWOL4_6.js";import{c as p}from"./manage-BReaEVJk.js";import{d as c,r as l,o as i,g as d,h as m,i as _,C as f}from"./index-r0dFAfgr.js";import"./index-C9hz-UZb.js";const u=c({__name:"xmgc",props:{id:{}},setup(t){const o=t,a=l({defaultExpandAll:!0,indexVisible:!0,columns:[{label:"工程编号",prop:"name"},{label:"工程名称",prop:"name"},{label:"工程地址",prop:"address"},{label:"工程类别",prop:"typeName"},{label:"工程预算(万元)",prop:"estimate"},{label:"申请单位",prop:"fitstpartName"}],dataList:[],pagination:{hide:!0}}),n=async()=>{p({page:1,size:-1,projectCode:o.id}).then(e=>{a.dataList=e.data.data.data||[]})};return i(()=>{n()}),(e,b)=>{const s=r;return d(),m(s,{title:"项目工程",config:_(a),class:"card-table"},null,8,["config"])}}}),L=f(u,[["__scopeId","data-v-ab136591"]]);export{L as default};
