package org.thingsboard.server.dao.shuiwu;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.Device;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.User;
import org.thingsboard.server.common.data.exception.ThingsboardErrorCode;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.DeviceId;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.id.UserId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.device.DeviceService;
import org.thingsboard.server.dao.model.sql.ProjectEntity;
import org.thingsboard.server.dao.model.sql.RepairJobCEntity;
import org.thingsboard.server.dao.model.sql.RepairJobEntity;
import org.thingsboard.server.dao.model.sql.shuiwu.PipeEntity;
import org.thingsboard.server.dao.model.sql.shuiwu.SwXjrwCEntity;
import org.thingsboard.server.dao.model.sql.shuiwu.SwXjrwMEntity;
import org.thingsboard.server.dao.model.sql.shuiwu.assets.AssetsAccountEntity;
import org.thingsboard.server.dao.project.ProjectService;
import org.thingsboard.server.dao.repair.RepairJobService;
import org.thingsboard.server.dao.shuiwu.assets.AssetsAccountService;
import org.thingsboard.server.dao.sql.shuiwu.SwXjrwCRepository;
import org.thingsboard.server.dao.sql.shuiwu.SwXjrwMRepository;
import org.thingsboard.server.dao.user.UserService;

import javax.transaction.Transactional;
import java.util.*;

@Slf4j
@Service
public class SwXjrwServiceImpl implements SwXjrwService {

    @Autowired
    private SwXjrwMRepository swXjrwMRepository;
    @Autowired
    private SwXjrwCRepository swXjrwCRepository;
    @Autowired
    private UserService userService;
    @Autowired
    private DeviceService deviceService;
    @Autowired
    private ProjectService projectService;
    @Autowired
    private CriterionService criterionService;
    @Autowired
    private PipeService pipeService;
    @Autowired
    private RepairJobService repairJobService;
    @Autowired
    private AssetsAccountService assetsAccountService;

    @Override
    public SwXjrwMEntity getXjrwM(String id) {
        // 查询主表
        SwXjrwMEntity entity = swXjrwMRepository.findOne(id);
        TenantId tenantId = new TenantId(UUIDConverter.fromString(entity.getTenantId()));

        // 项目列表
        List<ProjectEntity> projectList = projectService.findByTenantId(tenantId);
        Map<String, ProjectEntity> projectMap = new HashMap<>();
        projectList.forEach(project -> projectMap.put(project.getId(), project));

        List<User> userList = userService.findUserByTenant(tenantId);
        Map<String, User> userMap = new HashMap<>();
        if (userList != null) {
            userList.forEach(user -> userMap.put(UUIDConverter.fromTimeUUID(user.getUuidId()), user));
        }
        String users = entity.getUsers();
        StringBuilder usernames = new StringBuilder();
        if (StringUtils.isNotBlank(users)) {
            String[] usersArray = users.split(",");
            for (int i = 0; i < usersArray.length; i++) {
                User user = userMap.get(usersArray[i]);
                if (user == null) {
                    continue;
                }
                if (i < usersArray.length - 1) {
                    usernames.append(user.getFirstName()).append(",");
                } else {
                    usernames.append(user.getFirstName());
                }
            }
        }
        entity.setUserNames(usernames.toString());

        // 查询子表
        List<SwXjrwCEntity> list = swXjrwCRepository.findByMainIdOrderByOrderNum(id);

        for (SwXjrwCEntity child : list) {
            // 用户名
            String userId = child.getUserId();
            if (StringUtils.isNotBlank(userId)) {
                User user = userMap.get(userId);
                if (user != null) {
                    child.setUserName(user.getFirstName());
                }
            }
            // 设备名
            String deviceType = child.getDeviceType();
            AssetsAccountEntity assetsAccount = assetsAccountService.findById(child.getDeviceId());
            if (assetsAccount != null) {
                child.setDeviceName(assetsAccount.getDeviceName());
                child.setDeviceAddress(assetsAccount.getDevicePosition());

                // 查询关联的设备
                if (StringUtils.isNotBlank(assetsAccount.getDeviceId())) {
                    Device device = deviceService.findDeviceById(new DeviceId(UUIDConverter.fromString(assetsAccount.getDeviceId())));
                    if (device != null) {
                        JsonNode additionalInfo = device.getAdditionalInfo();
                        if (additionalInfo != null) {
                            JSONObject data = JSON.parseObject(additionalInfo.asText());
                            JSONArray location = data.getJSONArray("location");
                            if (location != null) {
                                List<Double> locationArray = location.toJavaList(Double.class);
                                Double longitude = locationArray.get(0);
                                Double latitude = locationArray.get(1);

                                child.setLongitude(longitude);
                                child.setLatitude(latitude);
                            }
                        }
                    }
                }
            }
            // 所在项目
            ProjectEntity project = projectMap.get(child.getProjectId());
            if (project != null) {
                child.setProjectName(project.getName());
            }
        }

        entity.setJobList(list);

        return entity;
    }

    @Override
    public PageData<SwXjrwMEntity> findList(int page, int size, String content, String status, String deviceId, TenantId tenantId) {
        // 分页
        PageRequest pageable = new PageRequest(page - 1, size, new Sort(Sort.Direction.DESC, "executeTime"));
        // 状态
        List<String> statusList = new ArrayList<>();
        if (StringUtils.isNotBlank(status)) {
            statusList.addAll(Arrays.asList(status.split(",")));
        } else {
            statusList.add("0");
            statusList.add("1");
            statusList.add("2");
            statusList.add("4");
        }

        Page<SwXjrwMEntity> pageResult = swXjrwMRepository.findList(content, statusList, UUIDConverter.fromTimeUUID(tenantId.getId()), deviceId, pageable);

        List<SwXjrwMEntity> list = pageResult.getContent();

        List<User> userList = userService.findUserByTenant(tenantId);
        Map<String, User> userMap = new HashMap<>();
        if (userList != null) {
            userList.forEach(user -> userMap.put(UUIDConverter.fromTimeUUID(user.getUuidId()), user));
        }

        for (SwXjrwMEntity xjrw : list) {
            String users = xjrw.getUsers();
            StringBuilder usernames = new StringBuilder();
            if (StringUtils.isNotBlank(users)) {
                String[] usersArray = users.split(",");
                for (int i = 0; i < usersArray.length; i++) {
                    User user = userMap.get(usersArray[i]);
                    if (user == null) {
                        continue;
                    }
                    if (i < usersArray.length - 1) {
                        usernames.append(user.getFirstName()).append(",");
                    } else {
                        usernames.append(user.getFirstName());
                    }
                }
            }
            xjrw.setUserNames(usernames.toString());
        }

        return new PageData<>(pageResult.getTotalElements(), pageResult.getContent());
    }

    @Override
    public PageData<SwXjrwMEntity> findHistoryList(int page, int size, String content, Date createTime1, Date createTime2, String deviceId, TenantId tenantId) {
        // 分页
        PageRequest pageable = new PageRequest(page - 1, size, new Sort(Sort.Direction.DESC, "executeTime"));

        if (createTime1 == null || createTime2 == null) {
            createTime1 = new Date(946656000000L);
            createTime2 = new Date();
        }

        Page<SwXjrwMEntity> pageResult = swXjrwMRepository.findHistoryList(content, UUIDConverter.fromTimeUUID(tenantId.getId()), createTime1, createTime2, deviceId, pageable);

        return new PageData<>(pageResult.getTotalElements(), pageResult.getContent());
    }

    @Override
    public void saveXJRWM(SwXjrwMEntity xjrwmEntity) {
        swXjrwMRepository.save(xjrwmEntity);
    }

    @Override
    public void save(List<SwXjrwCEntity> xjrwcEntityList) {
        swXjrwCRepository.save(xjrwcEntityList);
    }

    @Override
    public JSONObject complete(JSONObject params) {
        JSONObject result = new JSONObject();
        // 查询子任务是否存在
        String jobId = params.getString("jobId");
        SwXjrwCEntity xjrwc = swXjrwCRepository.findOne(jobId);

        if (xjrwc == null) {
            result.put("code", 404);
            result.put("message", "子任务不存在!");

            return result;
        }

        String recordStatus = params.getString("recordStatus");
        String imgs = params.getString("imgs");
        String voiceFile = params.getString("voiceFile");
        String remark = params.getString("remark");
        Boolean ok = params.getBoolean("ok");

        xjrwc.setRecordStatus(recordStatus);
        xjrwc.setImgs(imgs);
        xjrwc.setVoiceFile(voiceFile);
        xjrwc.setRemark(remark);
        xjrwc.setOk(ok);

        // 设置执行人
        xjrwc.setUserId(params.getString("userId"));
        // 设置状态
        xjrwc.setStatus("1");// 已完成
        xjrwc.setEndTime(new Date());

        swXjrwCRepository.save(xjrwc);

        result.put("code", 200);
        result.put("message", "操作完成");

        // 判断当前任务是否生成故障报修
        if (ok == null) {// 默认为已解决
            ok = true;
        }

        if (!ok) {// 若为未解决需要生成维修任务
            try {
                RepairJobEntity repairTask = new RepairJobEntity();
                repairTask.setName("巡检故障报修");
                repairTask.setType("4");
                RepairJobCEntity jobCEntity = new RepairJobCEntity();
                jobCEntity.setProjectId(xjrwc.getProjectId());
                jobCEntity.setDeviceId(xjrwc.getDeviceId());
                jobCEntity.setImgs(imgs);
                jobCEntity.setRemark(remark);
                repairTask.setJobList(Collections.singletonList(jobCEntity));

                repairJobService.faultReport(repairTask, params.getObject("currentUser", User.class));
            } catch (Exception e) {
                log.error("【巡检故障】生成故障保修任务失败, 失败原因: ", e);
            }
        }

        return result;
    }

    @Override
    public Object end(JSONObject params) {
        JSONObject result = new JSONObject();
        String id = params.getString("id");
        // 校验任务是否存在
        SwXjrwMEntity xjrw = swXjrwMRepository.findOne(id);
        if (xjrw == null || "3".equals(xjrw.getStatus())) {
            result.put("code", 500);
            result.put("message", "该任务不存在或已经结束");

            return result;
        }

        // 完成
        xjrw.setStatus("3");// 完成状态
        xjrw.setEndTime(new Date());
        swXjrwMRepository.save(xjrw);

        result.put("code", 200);
        result.put("message", "操作成功");

        return result;
    }

    @Override
    @Transactional
    public JSONObject checkExecutor(String xjrwId, User currentUser) throws ThingsboardException {
        JSONObject result = new JSONObject();
        SwXjrwMEntity xjrwm = swXjrwMRepository.findOne(xjrwId);
        if (xjrwm != null) {
            // 校验任务人
            if (StringUtils.isNotBlank(xjrwm.getUserId()) && !xjrwm.getUserId().equals(UUIDConverter.fromTimeUUID(currentUser.getUuidId()))) {
                User userEntity = userService.findUserById(new UserId(UUIDConverter.fromString(xjrwm.getUserId())));
                throw new ThingsboardException("任务由" + userEntity.getFirstName() + "[" + userEntity.getLoginName() + "]" +"执行!", ThingsboardErrorCode.GENERAL);
            }

            // 查看是否到达时间
            Date executeTime = xjrwm.getExecuteTime();
            Date now = new Date();
            // 判断是否已经到达执行时间
            long time = now.getTime() - executeTime.getTime();
            if (-60000 <= time) {// 到达执行时间
                // 检测是否有权限执行
                String users = xjrwm.getUsers();
                if (StringUtils.isNotBlank(users)) {
                    // 记录开始时间
                    xjrwm.setStartTime(new Date());
                    swXjrwMRepository.save(xjrwm);
                    boolean contains = users.contains(UUIDConverter.fromTimeUUID(currentUser.getUuidId()));
                    if (contains) {
                        result.put("result", true);
                    } else {
                        result.put("result", false);

                        List<User> userList = userService.findByIdIn(Arrays.asList(users.split(",")));
                        Optional<String> names = userList.stream().map(User::getFirstName).reduce((name1, name2) -> name1 + "," + name2);
                        result.put("users", names.orElse(""));
                    }

                }
                return result;
            } else {
                throw new ThingsboardException("未到达任务执行时间!", ThingsboardErrorCode.GENERAL);
            }

        }

        return result;
    }

    @Override
    public List<SwXjrwMEntity> findAllByTenantId(TenantId tenantId) {
        return swXjrwMRepository.findAllByTenantId(UUIDConverter.fromTimeUUID(tenantId.getId()));
    }

    @Override
    public Object groupByStatus(String deviceId) {
        List list = swXjrwMRepository.groupByStatus(deviceId);
        if (list == null || list.isEmpty()) {
            return new ArrayList<>();
        }

        List<JSONObject> result = new ArrayList<>();
        for (Object o : list) {
            Object[] objects = (Object[]) o;
            JSONObject data = new JSONObject();
            data.put("status", objects[0]);
            data.put("count", objects[1]);
            result.add(data);
        }

        return result;
    }

    @Override
    public List<SwXjrwMEntity> findAllByTenantId(TenantId tenantId, Date startTime, Date endTime) {
        return swXjrwMRepository.findByTenantIdAndExecuteTimeBetweenOrderByExecuteTime(UUIDConverter.fromTimeUUID(tenantId.getId()), startTime, endTime);
    }

    @Override
    public Object countStatus(Long start, Long end, User currentUser) {
        TenantId tenantId = currentUser.getTenantId();
        Date startTime = null;
        Date endTime = new Date();
        if (start != null && end != null) {
            startTime = new Date(start);
            endTime = new Date(end);
        }

        // 分组统计数量
        List list = null;
        if (startTime != null) {
            list = swXjrwMRepository.groupByStatus(startTime, endTime, UUIDConverter.fromTimeUUID(tenantId.getId()));
        } else {
            list = swXjrwMRepository.groupByStatusAll(UUIDConverter.fromTimeUUID(tenantId.getId()));
        }
        JSONObject result = new JSONObject();
        int ready = 0;
        int execute = 0;
        int complete = 0;
        for (Object o : list) {
            Object[] objects = (Object[]) o;
            String status = (String) objects[0];
            Long count = (Long) objects[1];
            if ("0".equals(status) && count != null) {// 待执行
                ready = Math.toIntExact(count);
            }
            if (("1".equals(status) || "2".equals(status)) && count != null) {// 执行中
                execute += count;
            }
            if ("3".equals(status) && count != null) {// 已完成
                complete = Math.toIntExact(count);
            }
        }
        result.put("ready", ready);
        result.put("execute", execute);
        result.put("complete", complete);

        return result;
    }
}
