import{d as tt,r as u,c as S,a0 as m,bB as L,s as x,bF as et,o as at,g as I,n as D,q as r,F as v,i as n,p as c,aw as ot,j as st,aB as O,aJ as J,bh as T,cs as nt,an as R,h as lt,c5 as it,_ as rt,aq as ct,cU as pt,b6 as dt,ak as U,al as W,d$ as ut,b7 as mt,aj as ft,C as gt}from"./index-r0dFAfgr.js";import{_ as bt}from"./index-C9hz-UZb.js";import _t from"./stationDetailMonitoring-C7k2lcgo.js";import{p as yt}from"./echart-UKlzIoq9.js";import{u as vt}from"./useStation-DJgnSZIA.js";import{G as ht,e as wt,k as xt}from"./zhandian-YaGuQZe6.js";import{b as It}from"./waterQualityMonitoring-BYvdS1Mm.js";import{f as Dt}from"./DateFormatter-Bm9a68Ax.js";import{e as jt}from"./flowMonitoring-DtJlPj0G.js";import{r as Y}from"./chart-wy3NEK2T.js";import{d as Tt}from"./onemap-CEunQziB.js";import{G as kt}from"./Group1-DZYehK7n.js";import{a as Ct,b as St,p as Pt}from"./alarm-DY6-LWP-.js";import"./CardSearch-CB_HNR-Q.js";import"./Search-NSrhrIa_.js";import"./index-B69llYYW.js";import"./useAmap-D6DJ1T90.js";import"./index-BI1vGJja.js";import"./URLHelper-B9aplt5w.js";/* empty css                         */import"./headwaterMonitoring-BgK7jThW.js";const Lt={class:"wrapper"},Nt={key:0,class:"monitoring-tab"},Vt={class:"top-box"},Bt={class:"bottom-box"},Ft={class:"monitor-title"},zt={class:"title"},Gt=["onClick"],Ht={class:"monitor-table"},Mt={class:"monitor"},Ot={style:{"padding-left":"10px"}},Jt=tt({__name:"index",setup(Rt){var H,M;const{getStationTree:$}=vt(),N=[{name:"offline",label:"离线"},{name:"alarm",label:"报警"},{name:"online",label:"正常"}],o=u({activeName:"监测模式",drawerTitle:"",tabsList:[],stationTree:[],treeDataType:"Project",stationId:"",pieOption:null,stationInfo:{},JianCeDatas:[],icon:kt}),V=S(),B=S(),F=S(),P=S([]),f=u({data:[],loading:!0,title:"区域划分",expandOnClickNode:!1,treeNodeHandleClick:async t=>{f.currentProject!==t&&(f.loading=!0,f.currentProject=t,o.treeDataType=t.data.type,o.treeDataType==="Project"?(m().SET_selectedProject(t),await k(),o.stationId=""):L(()=>{o.stationId=t.id,o.stationInfo=t}))}}),A=u({title:"",labelWidth:"130px",width:"80%",group:[],cancel:!1,onClosed:()=>{o.treeDataType,o.stationId=""}}),z=u({group:[{id:"chart",fields:[{type:"vchart",option:Y(),style:{height:"150px"}}]}],labelPosition:"top",gutter:12,defaultValue:{type:"all"}}),E=()=>{f.loading=!1},q=async()=>{var b,_,w,p;const t=await Tt({projectId:(b=m().selectedProject)==null?void 0:b.value,status:""}),e=z.group[0].fields[0],a=((w=(_=t.data)==null?void 0:_.data)==null?void 0:w.length)||0,d=[],l=(p=t.data)==null?void 0:p.data;l==null||l.map(s=>{var C;let i=d.find(y=>y.status===s.status);const{label:j}=N.find(y=>y.name===s.status)||{};i?i.value++:(i={name:((C=N.find(y=>y.name===s.status))==null?void 0:C.label)??s.status,status:s.status,nameAlias:j,value:1,scale:"0%"},d.push(i))}),d.map(s=>(s.scale=a===0?"0%":Number(s.value)/a*100+"%",s)),e&&(e.option=Y(d,"个"))};u({filters:[{type:"input",label:"RTU编号",field:"name"},{type:"select",label:"排序方式",field:"name",options:[]},{type:"select",label:"排序顺序",field:"name",options:[]},{type:"input",label:"筛选条件",field:"name"},{type:"btn-group",btns:[{perm:!0,text:"添加条件",type:"success",svgIcon:x(U),click:()=>{}},{perm:!0,text:"查询",svgIcon:x(W),click:()=>k()}]}],defaultParams:{projectId:(H=m().selectedProject)==null?void 0:H.value}}),u({filters:[{type:"input",label:"监测点名称",labelWidth:"120px",field:"name1"},{type:"input",label:"RTU编号",field:"name1"},{type:"input-number",label:"超过不通讯时长",labelWidth:"120px",field:"name1"},{type:"btn-group",btns:[{type:"default",perm:!0,svgIcon:x(ut),text:"",isBlockBtn:!0,click:()=>{var t;(t=F.value)==null||t.toggleMore()}},{perm:!0,text:"添加条件",type:"success",svgIcon:x(U),click:()=>{}},{perm:!0,text:"查询",svgIcon:x(W),click:()=>k()},{type:"default",perm:!0,text:"重置",svgIcon:x(mt),click:()=>{var t;(t=F.value)==null||t.resetForm()}},{perm:!0,type:"warning",text:"导出",svgIcon:x(ft),click:()=>{}}]}],moreFilters:[{type:"select",label:"排序顺序",field:"name1",options:[]},{type:"select",label:"筛选条件",field:"name1",options:[{label:"正常",value:"正常"}]}],defaultParams:{projectId:(M=m().selectedProject)==null?void 0:M.value}});const Q=u({type:"tabs",tabType:"simple",width:"100%",tabs:[{label:"监测模式",value:"监测模式"},{label:"列表模式",value:"列表模式"}],handleTabClick:t=>{console.log("动都不动不得不对不对",t.props.name)}}),k=async()=>{var b,_,w,p;await G(),f.loading=!1;const t=await ht({page:g.pagination.page||1,size:g.pagination.limit||20,type:"水质监测站",projectId:(b=m().selectedProject)==null?void 0:b.value}),e=[],a=(_=t.data)==null?void 0:_.data;a.map(async s=>{var y;const i=await wt({stationType:"水质监测站",projectId:(y=m().selectedProject)==null?void 0:y.value});console.log("d",s);const j=i.data.find(Z=>Z.name===s.name),C={...s,...j};e.push(C)}),console.log("dataList",e),g.dataList=e,g.pagination.total=(w=t.data)==null?void 0:w.total;const l=(await jt({stationType:"水质监测站",projectId:(a==null?void 0:a.value)||((p=m().selectedProject)==null?void 0:p.value)})).data;P.value=[],l==null||l.map(s=>{P.value.push({id:s.stationId,title:s.name,monitorData:s.dataList||[]}),o.JianCeDatas=P.value}),f.loading=!1,await K(),await q()},K=async()=>{var t;(t=B.value)==null||t.clear(),L(()=>{var e;o.pieOption=yt(),(e=B.value)==null||e.resize()})},h=u({loading:!1,dataList:[],indexVisible:!0,columns:[{prop:"alarmInfo",label:"报警描述"},{prop:"time",label:"报警时间",formatter:t=>et(t.time).format("YYYY-MM-DD HH:mm:ss")},{prop:"alarmType",label:"报警类型",formatter:t=>{var e;return(e=Ct.find(a=>a.value===t.alarmType))==null?void 0:e.label}},{prop:"alarmStatus",label:"报警状态",formatter:t=>{var e;return(e=St.find(a=>a.value===t.alarmStatus))==null?void 0:e.label}},{prop:"processStatus",label:"处理状态",formatter:t=>{var e;return(e=Pt.find(a=>a.value===t.alarmStatus))==null?void 0:e.label}}],operations:[],pagination:{refreshData:({page:t,size:e})=>{h.pagination.page=t,h.pagination.limit=e,G()}}}),G=async()=>{var d,l;const t={alarmStatus:"1",stationType:"水质监测站",projectId:(d=m().selectedProject)==null?void 0:d.value,size:h.pagination.limit||20,page:h.pagination.page||1},a=(l=(await xt(t)).data)==null?void 0:l.data;console.log(a),h.dataList=(a==null?void 0:a.data)||[],h.pagination.total=(a==null?void 0:a.total)||0},g=u({loading:!1,dataList:[],indexVisible:!0,columns:[{prop:"name",label:"监测点名称",align:"center",sortable:!0},{prop:"time",label:"读取时间",align:"center",sortable:!0},{prop:"remainder",label:"余氯",align:"center",sortable:!0},{prop:"turbidity",label:"浊度",align:"center",sortable:!0},{prop:"ph",label:"PH",align:"center",sortable:!0},{prop:"oxygen",label:"溶氧",align:"center",sortable:!0},{prop:"conductance",label:"电导率",align:"center",sortable:!0},{prop:"createTime",label:"安装时间",align:"center",sortable:!0,formatter:(t,e)=>Dt(e)},{prop:"address",label:"安装位置",align:"center",sortable:!0}],operations:[],pagination:{page:1,limit:20,total:0,layout:"total, prev, pager, next, sizes, jumper",handleSize:t=>{g.pagination.limit=t},refreshData:({page:t,size:e})=>{g.pagination.page=t,g.pagination.limit=e}}}),X=t=>{var e;(e=V.value)==null||e.openDrawer(),L(()=>{o.drawerTitle=t.title,o.stationInfo=t,o.stationId=t.id,o.treeDataType="Station"}),console.log(t)};return at(async()=>{const t=await $("水质监测站"),e=m().selectedProject,a=await It({projectId:e.id});console.log(a),f.data=t,f.currentProject=t[0],k()}),(t,e)=>{const a=it,d=rt,l=bt,b=ct,_=pt,w=dt;return I(),D("div",Lt,[r(l,{class:"wrapper-content",title:" "},{title:v(()=>[r(a,{modelValue:n(o).activeName,"onUpdate:modelValue":e[0]||(e[0]=p=>n(o).activeName=p),config:n(Q)},null,8,["modelValue","config"])]),default:v(()=>[n(o).activeName==="监测模式"?(I(),D("div",Nt,[c("div",Vt,[r(l,{class:"card",title:"监测状态统计"},{default:v(()=>[r(d,{ref:"refForm",config:n(z)},null,8,["config"])]),_:1}),r(l,{class:"table",title:""},{default:v(()=>[r(b,{class:"",config:n(h)},null,8,["config"])]),_:1})]),c("div",Bt,[c("div",{class:ot(["card-item",{isDark:n(st)().isDark}])},[(I(!0),D(O,null,J(n(o).JianCeDatas,(p,s)=>(I(),D("div",{key:s,class:"card-content"},[r(l,{title:" ",class:"inner-card left"},{title:v(()=>[c("div",Ft,[c("div",zt,[r(_,{src:n(o).icon,style:{width:"35px",height:"36px"}},null,8,["src"]),c("span",null,T(p.title),1)]),c("div",{onClick:i=>X(p)},[r(n(nt),{icon:"ph:warning-circle-bold",style:{color:"#4f7db8","font-size":"18px"}})],8,Gt)])]),default:v(()=>[c("div",Ht,[c("div",Mt,[(I(!0),D(O,null,J(p.monitorData,(i,j)=>(I(),D("div",{key:j,class:"box-1"},[c("div",null,T(i.propertyName)+" "+T(i.unit?"("+i.unit+")":""),1),c("div",null,T(i.value||"无"),1)]))),128))])])]),_:2},1024)]))),128))],2)])])):R("",!0),n(o).activeName==="列表模式"?(I(),lt(b,{key:1,class:"table",config:n(g)},null,8,["config"])):R("",!0)]),_:1}),r(w,{ref_key:"refDrawer",ref:V,config:n(A)},{title:v(()=>[r(_,{src:n(o).icon,style:{width:"35px",height:"36px"}},null,8,["src"]),c("span",Ot,T(n(o).drawerTitle),1)]),default:v(()=>[r(_t,{"station-id":n(o).stationId,monitor:n(o).stationInfo,onHiddenLoading:E},null,8,["station-id","monitor"])]),_:1},8,["config"])])}}}),pe=gt(Jt,[["__scopeId","data-v-37ada52c"]]);export{pe as default};
