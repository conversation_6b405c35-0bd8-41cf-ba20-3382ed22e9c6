package org.thingsboard.server.dao.model.sql.revenue;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 抄核数据副本-抄表数据
 */
@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.READ_METER_DATA_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class ReadMeterData {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.READ_METER_DATA_CODE)
    private String code;

    @Column(name = ModelConstants.READ_METER_DATA_YM)
    private String ym;

    @Column(name = ModelConstants.READ_METER_DATA_YM_TIME)
    private Date ymTime;

    @Column(name = ModelConstants.READ_METER_DATA_USER_CODE)
    private String userCode;

    @Column(name = ModelConstants.READ_METER_DATA_ORG_ID)
    private String orgId;

    @Column(name = ModelConstants.READ_METER_DATA_METER_ID)
    private String meterId;

    @Column(name = ModelConstants.READ_METER_DATA_POINT_ID)
    private String pointId;

    @Column(name = ModelConstants.READ_METER_DATA_METER_COPY_NUMBER)
    private Integer meterCopyNumber;

    @Column(name = ModelConstants.READ_METER_DATA_LAST_READ_NUM)
    private BigDecimal lastReadNum;

    @Column(name = ModelConstants.READ_METER_DATA_LAST_READ_WATER)
    private BigDecimal lastReadWater;

    @Column(name = ModelConstants.READ_METER_DATA_THIS_READ_NUM)
    private BigDecimal thisReadNum;

    @Column(name = ModelConstants.READ_METER_DATA_THIS_READ_WATER)
    private BigDecimal thisReadWater;

    @Column(name = ModelConstants.READ_METER_DATA_APPEND_WATER)
    private BigDecimal appendWater;

    @Column(name = ModelConstants.READ_METER_DATA_TOTAL_WATER)
    private BigDecimal totalWater;

    @Column(name = ModelConstants.READ_METER_DATA_READ_STATUS)
    private String readStatus;

    @Column(name = ModelConstants.READ_METER_DATA_SEND_STATUS)
    private String sendStatus;

    @Column(name = ModelConstants.READ_METER_DATA_EXCEPTION_TYPE)
    private String exceptionType;

    @Column(name = ModelConstants.READ_METER_DATA_LAST_READ_DATE)
    private Date lastReadDate;

    @Column(name = ModelConstants.READ_METER_DATA_THIS_READ_DATE)
    private Date thisReadDate;

    @Column(name = ModelConstants.READ_METER_DATA_DATA_TIME)
    private Date dataTime;

    @Column(name = ModelConstants.READ_METER_DATA_RECORD_TYPE)
    private String recordType;

    @Column(name = ModelConstants.READ_METER_DATA_TYPE)
    private String type;

    @Column(name = ModelConstants.READ_METER_DATA_METER_ADDRESS)
    private String meterAddress;

    @Column(name = ModelConstants.READ_METER_DATA_METER_COPY_USER)
    private String meterCopyUser;

    @Column(name = ModelConstants.READ_METER_DATA_METER_REMARK)
    private String remark;

    @Column(name = ModelConstants.READ_METER_DATA_IMGS)
    private String imgs;

    @Column(name = ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;

    public ReadMeterData(String code, String ym, Date ymTime, String userCode, String orgId, String meterId, String pointId, Integer meterCopyNumber, BigDecimal lastReadNum, BigDecimal lastReadWater, BigDecimal thisReadNum, BigDecimal thisReadWater, BigDecimal appendWater, BigDecimal totalWater, String readStatus, String exceptionType, Date lastReadDate, Date thisReadDate, String recordType, String meterAddress, String tenantId) {
        this.code = code;
        this.ym = ym;
        this.ymTime = ymTime;
        this.userCode = userCode;
        this.orgId = orgId;
        this.meterId = meterId;
        this.pointId = pointId;
        this.meterCopyNumber = meterCopyNumber;
        this.lastReadNum = lastReadNum;
        this.lastReadWater = lastReadWater;
        this.thisReadNum = thisReadNum;
        this.thisReadWater = thisReadWater;
        this.appendWater = appendWater;
        this.totalWater = totalWater;
        this.readStatus = readStatus;
        this.exceptionType = exceptionType;
        this.lastReadDate = lastReadDate;
        this.thisReadDate = thisReadDate;
        this.recordType = recordType;
        this.meterAddress = meterAddress;
        this.tenantId = tenantId;
    }
}
