import{R as V,e as b,y as D,a as Ge,S as v,j as K,b as qe,Z as _e,v as Ze}from"./Point-WxyopZva.js";import{o as ve,V as Ne,Z as Ye,b as Qe}from"./cimAnalyzer-CMgqZsaO.js";import{A as C,v as Xe,c2 as ye,hK as We,J as de,fO as Je,hL as se,hM as et,az as tt,ay as st,av as it,ao as Oe,fA as rt,dH as nt,ga as ot,hN as at,L as lt,cx as E,cP as ht,f as dt,dO as ct,hO as ut,hP as ft,K as be,I as De,bl as pt,F as Ce,G as ie,hQ as mt,hR as yt,hS as gt}from"./MapView-DaoQedLH.js";import{R as T,aO as xt,T as z,a4 as _t,eZ as vt}from"./index-r0dFAfgr.js";import{l as bt}from"./widget-BcWKanF2.js";import{p as Dt}from"./normalizeUtilsSync-NMksarRY.js";import{f as Ct,g as wt}from"./projectionSupport-BDUl30tr.js";import{U as Tt}from"./definitions-826PWLuy.js";import{S as Ue}from"./enums-L38xj_2E.js";import{x as ge,F as Y,j as ce,H as Ft,Y as Bt,P as J,R as St,b as Rt}from"./color-DAS1c3my.js";import{f as It,E as we}from"./FramebufferObject-8j9PRuxE.js";import{F as Mt}from"./enums-BDQrMlcz.js";import{m as Lt,i as zt}from"./AttributeStoreView-B0-phoCE.js";import{U as $t}from"./MaterialKey-BYd7cMLJ.js";import{e as Q,o as At,x as Gt,n as Ot,i as Ut,E as Pt}from"./Matcher-v9ErZwmD.js";import{w as Te,$ as Vt,o as jt}from"./schemaUtils-DLXXqxNF.js";import{c as kt,b as Kt,r as Et,M as Ht}from"./ComputedAttributeStorage-CF7WDnl8.js";import{O as qt,U as A}from"./quantizationUtils-DtI9CsYu.js";import"./alignmentUtils-CkNI7z7C.js";import"./number-CoJp78Rz.js";const y=-1;let Fe=class{constructor(){this._dirties=[{vertexFrom:y,vertexCount:y,indexFrom:y,indexCount:y,allDirty:!1},{vertexFrom:y,vertexCount:y,indexFrom:y,indexCount:y,allDirty:!1},{vertexFrom:y,vertexCount:y,indexFrom:y,indexCount:y,allDirty:!1},{vertexFrom:y,vertexCount:y,indexFrom:y,indexCount:y,allDirty:!1},{vertexFrom:y,vertexCount:y,indexFrom:y,indexCount:y,allDirty:!1}]}hasDirty(){return this._dirties.some(e=>e.indexCount!==y||e.allDirty)}markAllClean(){for(const e of this._dirties)e.indexFrom=y,e.indexCount=y,e.vertexFrom=y,e.vertexCount=y,e.allDirty=!1}markAllDirty(){for(const e of this._dirties)e.allDirty=!0}forEach(e){for(let t=0;t<this._dirties.length;++t){const{indexCount:s,indexFrom:i,vertexCount:r,vertexFrom:n,allDirty:a}=this._dirties[t],d={};let l,h=!1;(a||n!==y&&r>0)&&(d.geometry={count:r,from:n,allDirty:a},h=!0),(a||i!==y&&s>0)&&(l={count:s,from:i,allDirty:a},h=!0),h&&e({indices:l,vertices:d},t)}}markDirtyIndices(e,t,s){const i=this._dirties[e],r=t,n=s;if(!i.allDirty)if(i.indexCount!==y){const a=Math.min(i.indexFrom,r),d=Math.max(i.indexFrom+i.indexCount,r+n)-a;i.indexFrom=a,i.indexCount=d}else i.indexFrom=r,i.indexCount=n}markDirtyVertices(e,t,s,i){const r=this._dirties[e],n=s,a=i;if(!r.allDirty)if(r.vertexCount!==y){const d=Math.min(r.vertexFrom,n),l=Math.max(r.vertexFrom+r.vertexCount,n+a)-d;r.vertexFrom=d,r.vertexCount=l}else r.vertexFrom=n,r.vertexCount=a}},Zt=class{get largestRange(){return this._largestRange}constructor(e){this._largestRange=null,this._parent=e,this._updateLargestRange()}rangeCreated(e){(!this._largestRange||e.count>this._largestRange.count)&&(this._largestRange=e)}rangeResized(e,t){e===this._largestRange?e.count<t&&this._updateLargestRange():(!this._largestRange||e.count>this._largestRange.count)&&(this._largestRange=e)}findBestRange(e){let t=this._parent._freeHead,s=null;for(;t!==null;)t.count>=e&&(!s||t.count-e<s.count-e)&&(s=t),t=t.next;return s}findAdjacentRanges(e,t){let s=!0,i=!1,r=null,n=this._parent._freeHead;for(;s&&!i;){const a=r!==null?r.from+r.count:0,d=n!==null?n.from:this._parent._size;e>=a&&e+t<=d?(s=!1,i=!0):n!==null?(r=n,n=n.next):s=!1}return[r,n]}_updateLargestRange(){let e=null,t=this._parent._freeHead;for(;t!==null;)(!e||t.count>e.count)&&(e=t),t=t.next;this._largestRange=e}},H=class L{constructor(e,t){this._allocated=0,this._size=e,this._freeHead=e>0?{from:0,count:e,prev:null,next:null}:null,this._bookKeeper=t||new Zt(this),this._freeHead&&this._bookKeeper.rangeCreated(this._freeHead)}allocate(e){const t=this._bookKeeper.findBestRange(e);if(t==null)return-1;const s=t.from,i=t.count;if(t.from+=e,t.count-=e,this._bookKeeper.rangeResized(t,s,i),this._allocated+=e,t.count===0){const r=t.prev!==null?this._freeHead:t.next;L._removeRange(t),this._freeHead=r}return s}free(e,t){const[s,i]=this._bookKeeper.findAdjacentRanges(e,t),r={from:e,count:t,prev:s??null,next:i??null};if(s!=null&&(s.next=r),i!=null&&(i.prev=r),this._bookKeeper.rangeCreated(r),this._allocated-=t,i!=null&&r.from+r.count===i.from){const n=r.from,a=r.count;L._fuse(r,i),L._removeRange(i),this._bookKeeper.rangeResized(r,n,a),this._bookKeeper.rangeResized(i,void 0,0)}if(s!=null&&s.from+s.count===r.from){const n=s.from,a=s.count;L._fuse(s,r),L._removeRange(r),this._bookKeeper.rangeResized(s,n,a),this._bookKeeper.rangeResized(r,void 0,0)}this._freeHead=r.prev!==null?this._freeHead:r}get fragmentation(){const e=this._size-this._allocated;return e===0?0:1-this._bookKeeper.largestRange.count/e}static _removeRange(e){e.prev!==null?e.next!==null?(e.prev.next=e.next,e.next.prev=e.prev):e.prev.next=null:e.next!==null&&(e.next.prev=null)}static _fuse(e,t){e.count+=t.count,e.next=t.next,t.from+=t.count,t.count=0,t.next!==null&&(t.next.prev=e)}};const U=["FILL","LINE","MARKER","TEXT","LABEL"];function Nt(o){const e=o.getStrides(),t={};for(let s=0;s<e.length;s++)t[U[s]]=e[s];return t}const Be=.5;let Se=class Pe{constructor(e,t,s,i){this._strides=e,this._displayList=t,this._freeListsAndStorage={},this._dirtyMap=null,this._dirtyMap=s;for(const r in e){this._freeListsAndStorage[r]={vtxFreeList:i?new H(i):null,idxFreeList:i?new H(i):null,vertexBuffers:{},indexBuffer:i?new Uint32Array(i):null};for(const n in e[r])this._freeListsAndStorage[r].vertexBuffers[n]={data:i?ge(i,e[r][n]):null,stride:e[r][n]}}}static fromTileData(e,t){var d;const s=Nt(e),i=[0,0,0,0,0],r=[0,0,0,0,0];for(const l of e.tileDisplayData.displayObjects)for(const h of l.displayRecords)i[h.geometryType]=Math.max(i[h.geometryType],h.vertexFrom+h.vertexCount),r[h.geometryType]=Math.max(r[h.geometryType],h.indexFrom+h.indexCount);const n=new Pe(s,e.tileDisplayData.displayList,t,void 0),a=((d=e.tileBufferData)==null?void 0:d.geometries)??[];for(let l=0;l<a.length;++l){const h=i[l],c=r[l],u=a[l],f=U[l],m=n._storageFor(f),p=a[l].indexBuffer;m.indexBuffer=p,m.idxFreeList=new H(p.length),m.idxFreeList.allocate(c);let g=0;for(const _ in u.vertexBuffer){const x=a[l].vertexBuffer[_];m.vertexBuffers[_].data=x.data,m.vertexBuffers[_].stride=x.stride;const S=Y(x.stride),k=x.data.length*S/x.stride;g||(g=k)}m.vtxFreeList=new H(g),m.vtxFreeList.allocate(h)}return n}delete(e){const t=U[e.geometryType];this._freeVertices(t,e.vertexFrom,e.vertexCount),this._freeIndices(t,e.indexFrom,e.indexCount),this._displayList.removeFromList(e),e.vertexFrom=void 0,e.indexFrom=void 0}setMeshData(e,t,s,i,r){const n=U[e.geometryType];let a,d;e.meshData=null,e.vertexFrom===void 0?(d=t.vertexCount,a=this._allocateVertices(n,d)):t.vertexCount>e.vertexCount?(this._freeVertices(n,e.vertexFrom,e.vertexCount),d=t.vertexCount,a=this._allocateVertices(n,d)):t.vertexCount===e.vertexCount?(a=e.vertexFrom,d=e.vertexCount):(this._freeVertices(n,e.vertexFrom+t.vertexCount,e.vertexCount-t.vertexCount),a=e.vertexFrom,d=t.vertexCount);let l,h,c,u=!0;if(e.indexFrom===void 0?(l=r,c=t.indexCount,h=this._allocateIndices(n,c)):t.indexCount>e.indexCount?(l=this._displayList.removeFromList(e),this._freeIndices(n,e.indexFrom,e.indexCount),c=t.indexCount,h=this._allocateIndices(n,c)):t.indexCount===e.indexCount?(u=!1,h=e.indexFrom,c=e.indexCount):(l=this._displayList.removeFromList(e),this._freeIndices(n,e.indexFrom+t.indexCount,e.indexCount-t.indexCount),h=e.indexFrom,c=t.indexCount),a!==-1&&h!==-1){const f=this._storageFor(n);if(ce(a,h,f.vertexBuffers,f.indexBuffer,t,s,i),e.vertexFrom=a,e.indexFrom=h,e.vertexCount=t.vertexCount,e.indexCount=t.indexCount,this._dirtyMap){this._dirtyMap.markDirtyIndices(e.geometryType,e.indexFrom,e.indexCount);for(const m in s)this._dirtyMap.markDirtyVertices(e.geometryType,m,e.vertexFrom,e.vertexCount)}return u&&this._displayList.addToList(e,l),!0}return a!==-1&&this._freeVertices(n,a,d),h!==-1&&this._freeIndices(n,h,c),e.setMeshDataFromBuffers(t,s,i),e.vertexFrom=void 0,e.vertexCount=0,e.indexFrom=void 0,e.indexCount=0,!1}tryAddMeshData(e,t){const s=t.vertexBuffer,i=t.indexBuffer,r=U[e.geometryType],n=this._allocateVertices(r,e.vertexCount);if(n===-1)return this._freeVertices(r,n,e.vertexCount),!1;const a=this._allocateIndices(r,e.indexCount);if(a===-1)return this._freeVertices(r,n,e.vertexCount),this._freeIndices(r,a,e.indexCount),!1;const d=this._storageFor(r);if(ce(n,a,d.vertexBuffers,d.indexBuffer,e,s,i),e.vertexFrom=n,e.indexFrom=a,this._dirtyMap){this._dirtyMap.markDirtyIndices(e.geometryType,e.indexFrom,e.indexCount);for(const l in s)this._dirtyMap.markDirtyVertices(e.geometryType,l,n,e.vertexCount)}return this._displayList.addToList(e),!0}_allocateVertices(e,t){var n,a;const s=this._storageFor(e),i=(n=s.vtxFreeList)==null?void 0:n.allocate(t);if(i==null||i===-1)return-1;const r=(a=s.vtxFreeList)==null?void 0:a.fragmentation;return r==null||r>Be?-1:i}_freeVertices(e,t,s){var i;(i=this._storageFor(e).vtxFreeList)==null||i.free(t,s)}_freeIndices(e,t,s){var i;(i=this._storageFor(e).idxFreeList)==null||i.free(t,s)}_allocateIndices(e,t){var n,a;const s=this._storageFor(e),i=(n=s.idxFreeList)==null?void 0:n.allocate(t);if(i==null||i===-1)return-1;const r=(a=s.idxFreeList)==null?void 0:a.fragmentation;return r==null||r>Be?-1:i}_storageFor(e){return this._freeListsAndStorage[e]}_stridesFor(e,t){return this._strides[e][t]}},Yt=class{constructor(e){this.geometryMap=Ft(()=>({indexBuffer:we.createIndex(e,Mt.STATIC_DRAW),vao:null}),(t,s)=>({vertexBuffer:we.createVertex(e,Bt[s])}))}dispose(){for(let e=0;e<5;e++){const t=this.geometryMap[e];if(t){t.data.vao&&t.data.vao.dispose(!1),t.data.indexBuffer&&t.data.indexBuffer.dispose();for(const s in t.buffers)t.buffers[s]&&t.buffers[s].data.vertexBuffer.dispose()}}}get(e){const t=this.geometryMap[e];return{getVAO(s,i,r){if(!t.data.vao){const n={};for(const a in t.buffers)n[a]=t.buffers[a].data.vertexBuffer;t.data.vao=new It(s,r,i,n,t.data.indexBuffer)}return t.data.vao}}}has(e){return this.geometryMap[e]!=null}upload(e,t){t.forEach((s,i)=>{this._upload(s,i,e)})}_upload(e,t,s){if(e.indices&&(e.indices.allDirty?this._uploadIndices(s,t):e.indices.from!=null&&e.indices.count!=null&&this._uploadIndices(s,t,e.indices.from,e.indices.count)),e.vertices){const i=e.vertices;for(const r in i){const n=i[r];n.allDirty?this._uploadVertices(s,t,r):n.from!=null&&n.count!=null&&this._uploadVertices(s,t,r,n.from,n.count)}}}_uploadVertices(e,t,s,i,r){const n=this.geometryMap[t];if(!n)return;const a=e.geometries[t].vertexBuffer[s];if(!a)return;const{data:d,stride:l}=a;if(n.buffers[s]&&d.length>0){const h=l/d.BYTES_PER_ELEMENT;i!=null&&r!=null?n.buffers[s].data.vertexBuffer.setSubData(d,i*h,i*h,(i+r)*h):n.buffers[s].data.vertexBuffer.setData(d)}}_uploadIndices(e,t,s,i){const r=this.geometryMap[t];if(!r)return;const n=e.geometries[t].indexBuffer;r.data.indexBuffer&&n.length>0&&(s!=null&&i!=null?r.data.indexBuffer.setSubData(n,s,s,s+i):r.data.indexBuffer.setData(n))}};class Qt extends Lt{constructor(){super(...arguments),this._data=null,this._displayList=null,this._lastCommitTime=0,this._hasData=!1,this._invalidated=!1,this._wglBuffers=null,this._dirtyMap=new Fe}destroy(){super.destroy(),this.clear()}get hasData(){return!!this._hasData}get displayObjects(){return this._displayObjects??[]}getGeometry(e){return this._wglBuffers&&this._wglBuffers.has(e)?this._wglBuffers.get(e):null}getDisplayList(){return this._displayList}patch(e){var i,r;if(e.clear===!0)return this.clear(),void(this._hasData=!1);const t=e.addOrUpdate,s=e.remove;!this._data&&t&&((i=t.tileDisplayData)!=null&&i.displayObjects.length)?(t.tileDisplayData.computeDisplayList(),this._dirtyMap=new Fe,this._dispRecStore=Se.fromTileData(t,this._dirtyMap),this._data=t,this._dirtyMap.markAllDirty(),this._hasData=!0,e.end&&this.ready()):this._data&&(t&&((r=t.tileDisplayData)!=null&&r.displayObjects.length)||s.length)?this._doPatchData(e):e.end&&this.ready(),e.end&&!this._data&&this.clear(),this.requestRender(),this.emit("change")}commit(e){e.time&&e.time===this._lastCommitTime||(this._lastCommitTime=e.time,this.visible&&this._data&&(this._wglBuffers||(this._wglBuffers=new Yt(e.context)),(this._dirtyMap.hasDirty()||this._invalidated)&&(this._invalidated=!1,this._wglBuffers.upload(this._data.tileBufferData,this._dirtyMap),this._displayList=this._data.tileDisplayData.displayList.clone(),this._displayObjects=this._data.tileDisplayData.displayObjects.slice(),this._dirtyMap.markAllClean())))}clear(){this._data=null,this._displayList=null,this._dispRecStore=null,this._wglBuffers&&(this._wglBuffers.dispose(),this._wglBuffers=null)}_doPatchData(e){this._invalidated=!0,this._patchData(e)||(this._dirtyMap.markAllDirty(),this._data.reshuffle(),this._dispRecStore=Se.fromTileData(this._data,this._dirtyMap)),this.requestRender()}_patchData(e){let t=!0;const s=e.addOrUpdate&&e.addOrUpdate.tileDisplayData&&e.addOrUpdate.tileDisplayData.displayObjects||[],i=(e.remove||[]).slice();for(const a of s)a.insertAfter!=null&&i.push(a.id);let r;i.length>0&&(r=new Set(i));const n=this._data.tileDisplayData;for(const a of i){const d=n.displayObjectRegistry.get(a);if(d){n.displayList.removeFromList(d.displayRecords);for(const l of d.displayRecords)this._dispRecStore.delete(l);n.displayObjectRegistry.delete(a)}}r!=null&&r.size&&(n.displayObjects=n.displayObjects.filter(a=>!r.has(a.id)));for(const a of s){let d,l=n.displayObjectRegistry.get(a.id);if(l){const c=l.displayRecords;l.set(a),l.displayRecords=c;const u=l.displayRecords.length;for(let f=0;f<u;++f){const m=l.displayRecords[f],p=a.displayRecords[f];(f>=a.displayRecords.length||m.geometryType!==p.geometryType||m.symbolLevel!==p.symbolLevel||m.zOrder!==p.zOrder||m.materialKey!==p.materialKey)&&(this._dispRecStore.delete(l.displayRecords[f]),f<a.displayRecords.length&&(l.displayRecords[f]=void 0))}l.displayRecords.length=a.displayRecords.length}else{let c;l=a.copy(),l.displayRecords=[],n.displayObjectRegistry.set(a.id,l);const u=n.displayObjects;if(l.insertAfter!=null)if(d={},l.insertAfter>=0){const f=n.displayObjectRegistry.get(l.insertAfter);f?(c=u.indexOf(f)+1,c<u.length?u.splice(c,0,l):(u.push(l),c=u.length)):(u.push(l),c=u.length)}else u.unshift(l),c=0;else u.push(l),c=u.length;if(d){const f=a.displayRecords.length>0?1:0;let m=0;for(let p=c-1;p>=0&&m<f;--p)for(let g=u[p].displayRecords.length-1;g>=0&&m<f;--g){const _=u[p].displayRecords[g],x=n.displayList.getDPInfoType();d[x]||(d[x]=_,++m)}}}const h=a.displayRecords.length;for(let c=0;c<h;++c){const u=a.displayRecords[c];let f=l.displayRecords[c];f?(f.meshData=u.meshData,f.materialKey=u.materialKey):(f=u.copy(),f.vertexFrom=void 0,f.indexFrom=void 0,l.displayRecords[c]=f);const m=u.geometryType,p=n.displayList.getDPInfoType(),g=e.addOrUpdate.tileBufferData.geometries[m],_=g.vertexBuffer,x=g.indexBuffer;let S;d&&(S=d[p]?n.displayList.splitAfter(d[p]):-1),t=this._dispRecStore.setMeshData(f,u,_,x,S)&&t,d&&f.indexFrom!=null&&f.indexFrom!=null&&(d[p]=f)}}return t}}let Ve=class{constructor(){this._byGeometryType=null}get satisfied(){return!this._byGeometryType}reset(){this._byGeometryType=null}verticesFor(e){return this._byGeometryType?this._byGeometryType[e].vertices:0}indicesFor(e){return this._byGeometryType?this._byGeometryType[e].indices:0}needMore(e,t,s){if(!t&&!s)return;this._byGeometryType||(this._byGeometryType=[{vertices:0,indices:0},{vertices:0,indices:0},{vertices:0,indices:0},{vertices:0,indices:0},{vertices:0,indices:0}]);const i=this._byGeometryType[e];i.vertices+=t,i.indices+=s}};const re=5;let ne=class ue{constructor(){this.geometries=[{indexBuffer:void 0,vertexBuffer:{}},{indexBuffer:void 0,vertexBuffer:{}},{indexBuffer:void 0,vertexBuffer:{}},{indexBuffer:void 0,vertexBuffer:{}},{indexBuffer:void 0,vertexBuffer:{}}]}clone(){const e=new ue;for(let t=0;t<this.geometries.length;t++){const s=this.geometries[t],i=e.geometries[t];i.indexBuffer=s.indexBuffer.slice(),i.vertexBuffer={};for(const r in s.vertexBuffer){const{data:n,stride:a}=s.vertexBuffer[r];i.vertexBuffer[r]={data:n.slice(),stride:a}}}return e}static deserialize(e){const t=new ue;for(let s=0;s<re;++s){t.geometries[s].indexBuffer=new Uint32Array(e.geometries[s].indexBuffer),t.geometries[s].vertexBuffer={};for(const i in e.geometries[s].vertexBuffer)t.geometries[s].vertexBuffer[i]={data:J(e.geometries[s].vertexBuffer[i].data,e.geometries[s].vertexBuffer[i].stride),stride:e.geometries[s].vertexBuffer[i].stride}}return t}serialize(){const e={geometries:[{indexBuffer:this.geometries[0].indexBuffer.buffer,vertexBuffer:{}},{indexBuffer:this.geometries[1].indexBuffer.buffer,vertexBuffer:{}},{indexBuffer:this.geometries[2].indexBuffer.buffer,vertexBuffer:{}},{indexBuffer:this.geometries[3].indexBuffer.buffer,vertexBuffer:{}},{indexBuffer:this.geometries[4].indexBuffer.buffer,vertexBuffer:{}}]};for(let t=0;t<re;++t)for(const s in this.geometries[t].vertexBuffer)e.geometries[t].vertexBuffer[s]={data:this.geometries[t].vertexBuffer[s].data.buffer,stride:this.geometries[t].vertexBuffer[s].stride};return e}getBuffers(){const e=[];for(let t=0;t<re;++t){e.push(this.geometries[t].indexBuffer.buffer);for(const s in this.geometries[t].vertexBuffer)e.push(this.geometries[t].vertexBuffer[s].data.buffer)}return e}};function q(o,e,t,...s){e<o.length?o.splice(e,t,...s):o.push(...s)}let Xt=class je{constructor(){this.symbolLevels=[]}replay(e,t,s){for(const i of this.symbolLevels)for(const r of i.zLevels){const n=r.geometryDPInfo.unified;if(n)for(const a of n){const{geometryType:d,materialKey:l,indexFrom:h,indexCount:c}=a,u=$t.load(l).symbologyType,f=e.painter.getBrush(d,u),m={geometryType:d,materialKey:l,indexFrom:h,indexCount:c,target:t.getGeometry(d)};f.prepareState(e),f.drawGeometry(e,t,m,s)}}}get empty(){return!this.symbolLevels||this.symbolLevels.length===0}clear(){this.symbolLevels.length=0}addToList(e,t){if(Array.isArray(e))for(const s of e)this._addToList(s,t);else this._addToList(e,t)}removeFromList(e){Array.isArray(e)||(e=[e]);let t=null;for(const s of e)t=this._removeFromList(s);return t}clone(){const e=new je;for(const t of this.symbolLevels)e.symbolLevels.push(t.clone());return e}splitAfter(e){const t=this._getDisplayList(e.symbolLevel,e.zOrder),s=t.length,i=e.indexFrom+e.indexCount;for(let r=0;r<s;++r){const n=t[r];if(n.geometryType===e.geometryType&&i>n.indexFrom&&i<=n.indexFrom+n.indexCount){if(i<n.indexFrom+n.indexCount){const a=new oe;a.geometryType=n.geometryType,a.materialKey=n.materialKey,a.indexFrom=i,a.indexCount=n.indexFrom+n.indexCount-i,t.splice(r+1,0,a),n.indexCount=i-n.indexFrom}return r}}}_addToList(e,t){const s=e.symbolLevel,i=e.zOrder,r=this._getDisplayList(s,i),n=t??r.length-1,a=n>=0&&n<r.length?r[n]:null;if(a!==null&&a.materialKey===e.materialKey&&a.indexFrom+a.indexCount===e.indexFrom&&a.geometryType===e.geometryType)a.indexCount+=e.indexCount;else{const d=new oe;d.indexFrom=e.indexFrom,d.indexCount=e.indexCount,d.materialKey=e.materialKey,d.geometryType=e.geometryType,q(r,n+1,0,d)}}_removeFromList(e){const t=e.symbolLevel,s=e.zOrder,i=this._getDisplayList(t,s),r=i.length;let n;for(let a=0;a<r;++a){const d=i[a];if(e.indexFrom+e.indexCount>d.indexFrom&&e.indexFrom<d.indexFrom+d.indexCount&&d.geometryType===e.geometryType){n=a;break}}if(n!==void 0){const a=i[n];if(e.indexFrom===a.indexFrom)return a.indexCount-=e.indexCount,a.indexFrom+=e.indexCount,a.indexCount===0&&q(i,n,1),n-1;if(e.indexFrom+e.indexCount===a.indexFrom+a.indexCount)return a.indexCount-=e.indexCount,a.indexCount===0?(q(i,n,1),n-1):n;{const d=a.indexFrom,l=e.indexFrom-a.indexFrom,h=e.indexCount,c=a.indexFrom+a.indexCount-(e.indexFrom+e.indexCount);a.indexCount=l;const u=new oe;return u.geometryType=a.geometryType,u.materialKey=a.materialKey,u.indexFrom=d+l+h,u.indexCount=c,q(i,n+1,0,u),n}}return null}_getDisplayList(e,t){let s;const i=this.symbolLevels.length;for(let a=0;a<i;a++)if(this.symbolLevels[a].symbolLevel===e){s=this.symbolLevels[a];break}let r;s||(s=new xe,s.symbolLevel=e,this.symbolLevels.push(s));const n=s.zLevels.length;for(let a=0;a<n;a++)if(s.zLevels[a].zLevel===t){r=s.zLevels[a];break}return r||(r=new Wt,r.geometryDPInfo=new te,r.zLevel=t,s.zLevels.push(r)),r.geometryDPInfo.unified||(r.geometryDPInfo.unified=[]),r.geometryDPInfo.unified}getDPInfoType(){return"unified"}},oe=class ke{constructor(){this.materialKey=null,this.indexFrom=0,this.indexCount=0}clone(){const e=new ke;return e.geometryType=this.geometryType,e.materialKey=this.materialKey,e.indexFrom=this.indexFrom,e.indexCount=this.indexCount,e}};class te{constructor(){this.fill=null,this.line=null,this.marker=null,this.text=null,this.label=null,this.unified=null}clone(){const e=new te;return e.fill=this.fill&&this.fill.map(t=>t.clone()),e.line=this.line&&this.line.map(t=>t.clone()),e.marker=this.marker&&this.marker.map(t=>t.clone()),e.text=this.text&&this.text.map(t=>t.clone()),e.label=this.label&&this.label.map(t=>t.clone()),e.unified=this.unified&&this.unified.map(t=>t.clone()),e}}let Wt=class Ke{constructor(){this.geometryDPInfo=new te}clone(){const e=new Ke;return e.zLevel=this.zLevel,e.geometryDPInfo=this.geometryDPInfo.clone(),e}};class xe{constructor(){this.zLevels=[]}clone(){const e=new xe;e.symbolLevel=this.symbolLevel;for(const t of this.zLevels)e.zLevels.push(t.clone());return e}}let Re=class{constructor(){this.vertexData=new Map,this.vertexCount=0,this.indexData=[]}clear(){this.vertexData.clear(),this.vertexCount=0,this.indexData=[]}update(e,t,s){for(const i in e)this.vertexData.set(i,e[i]);for(const i in this.vertexData)e[i]===null&&this.vertexData.delete(i);this.vertexCount=t,this.indexData=s}},fe=class pe{constructor(e,t,s,i=0,r=0){this.id=e,this.geometryType=t,this.materialKey=s,this.minZoom=i,this.maxZoom=r,this.meshData=null,this.symbolLevel=0,this.zOrder=0,this.vertexFrom=0,this.vertexCount=0,this.indexFrom=0,this.indexCount=0,this._sortKey=null}get sortKey(){return this._sortKey==null&&this._computeSortKey(),this._sortKey}clone(){return this.copy()}copy(){const e=new pe(this.id,this.geometryType,this.materialKey);return e.vertexFrom=this.vertexFrom,e.vertexCount=this.vertexCount,e.indexFrom=this.indexFrom,e.indexCount=this.indexCount,e.zOrder=this.zOrder,e.symbolLevel=this.symbolLevel,e.meshData=this.meshData,e.minZoom=this.minZoom,e.maxZoom=this.maxZoom,e}setMeshDataFromBuffers(e,t,s){const i=new Re;for(const r in t){const n=t[r].stride,a=t[r].data;if(!a)continue;const d=[],l=Y(n);for(let h=0;h<n*e.vertexCount/l;++h)d[h]=a[h+n*e.vertexFrom/l];i.vertexData.set(r,d)}i.indexData.length=0;for(let r=0;r<e.indexCount;++r)i.indexData[r]=s[r+e.indexFrom]-e.vertexFrom;i.vertexCount=e.vertexCount,this.meshData=i}readMeshDataFromBuffers(e,t){this.meshData?this.meshData.clear():this.meshData=new Re;for(const s in e){const i=e[s].stride,r=e[s].data,n=[],a=Y(i);for(let d=0;d<i*this.vertexCount/a;++d)n[d]=r[d+i*this.vertexFrom/a];this.meshData.vertexData.set(s,n)}this.meshData.indexData.length=0;for(let s=0;s<this.indexCount;++s)this.meshData.indexData[s]=t[s+this.indexFrom]-this.vertexFrom;this.meshData.vertexCount=this.vertexCount}writeMeshDataToBuffers(e,t,s,i){if(this.meshData){for(const r in t){const n=t[r].stride,a=this.meshData.vertexData.get(r),d=t[r].data,l=Y(n);for(let h=0;h<n*this.meshData.vertexCount/l;++h)d[h+n*e/l]=a[h]}for(let r=0;r<this.meshData.indexData.length;++r)i[r+s]=this.meshData.indexData[r]+e;this.vertexFrom=e,this.vertexCount=this.meshData.vertexCount,this.indexFrom=s,this.indexCount=this.meshData.indexData.length}}static writeAllMeshDataToBuffers(e,t,s){let i=0,r=0;for(const n of e)n.writeMeshDataToBuffers(i,t,r,s),i+=n.vertexCount,r+=n.indexCount}_computeSortKey(){this._sortKey=(31&this.symbolLevel)<<12|(127&this.zOrder)<<4|7&this.geometryType}serialize(e){return e.push(this.geometryType),e.push(this.materialKey),e.push(this.vertexFrom),e.push(this.vertexCount),e.push(this.indexFrom),e.push(this.indexCount),e.push(this.minZoom),e.push(this.maxZoom),e}static deserialize(e,t){const s=e.readInt32(),i=e.readInt32(),r=new pe(t.id,s,i);return r.vertexFrom=e.readInt32(),r.vertexCount=e.readInt32(),r.indexFrom=e.readInt32(),r.indexCount=e.readInt32(),r.minZoom=e.readInt32(),r.maxZoom=e.readInt32(),r}};function Ee(o,e){if(e!==null){o.push(e.length);for(const t of e)t.serialize(o);return o}o.push(0)}function Jt(o,e,t){const s=o.readInt32(),i=new Array(s);for(let r=0;r<i.length;r++)i[r]=e.deserialize(o,t);return i}let He=class X{constructor(e){this.insertAfter=null,this.id=e,this.displayRecords=[]}copy(){const e=new X(this.id);return e.set(this),e}clone(){const e=new X(this.id);return e.displayRecords=this.displayRecords.map(t=>t.clone()),e.insertAfter=this.insertAfter,e}set(e){this.id=e.id,this.displayRecords=e.displayRecords,this.insertAfter=e.insertAfter}serialize(e){return e.push(this.id),Ee(e,this.displayRecords),e}static deserialize(e){const t=e.readInt32(),s=new X(t),i={id:t};return s.displayRecords=Jt(e,fe,i)??[],s}};class j{constructor(){this.displayObjects=[],this._displayList=null}get displayObjectRegistry(){if(!this._displayObjectRegistry){this._displayObjectRegistry=new Map;for(const e of this.displayObjects)this._displayObjectRegistry.set(e.id,e)}return this._displayObjectRegistry}get displayList(){return this._displayList}computeDisplayList(){this._displayList=new Xt;for(const e of this.displayObjects)for(const t of e.displayRecords)this._displayList.addToList(t)}clone(){const e=new j;return this.displayObjects&&(e.displayObjects=this.displayObjects.map(t=>t.clone())),e}serialize(e){return Ee(e,this.displayObjects),e}_deserializeObjects(e){const t=e.readInt32(),s=new Array(t),i=new Map;for(let r=0;r<s.length;++r){const n=He.deserialize(e);s[r]=n,i.set(n.id,n)}this.displayObjects=s,this._displayList=null,this._displayObjectRegistry=i}static deserialize(e){const t=new j;return t._deserializeObjects(e),t}}let ae=class me{constructor(e,t){this.data=e,this.stride=t}static decode(e){const t=J(e.data,e.stride),s=e.stride;return new me(t,s)}static fromVertexVector(e){const t=J(e.data.buffer(),e.stride),s=e.stride;return new me(t,s)}};class ${constructor(e,t,s){this.geometryType=e,this.indexBuffer=new Uint32Array(t),this.namedBuffers=s}static decode(e){const t=e.geometryType,s=e.indexBuffer,i={};for(const r in e.namedBuffers)i[r]=ae.decode(e.namedBuffers[r]);return new $(t,s,i)}static fromVertexData(e,t){const s=e.indices,i=J(e.vertices,e.stride),r=e.stride,n={geometry:new ae(i,r)};return new $(t,s,n)}static fromVertexVectors(e){const t=e.geometryType,s=e.indexVector.buffer(),i={};for(const r in e.namedVectors)i[r]=ae.fromVertexVector(e.namedVectors[r]);return new $(t,s,i)}}class es{get vertexCount(){const e=this.stride/4,t=this.data.length/e;return t!==(0|t)&&console.debug("Corrupted stride"),t}constructor(e,t){this.data=e,this.stride=t}transfer(e,t){const s=this.data.buffer();e.vertexCount=this.vertexCount,e.data=s,e.stride=this.stride,t.push(s)}}let ts=class{constructor(e,t,s){this.geometryType=e,this.indexVector=new Q(Uint32Array,6*t),this.namedVectors={};const i=St(e,s);for(const r in i){const n=i[r];let a;switch(n%4){case 0:case 2:a=new Q(Uint32Array,n*t);break;case 1:case 3:a=new Q(Uint8Array,n*t)}this.namedVectors[r]=new es(a,n)}}get(e){return this.namedVectors[e].data}getVector(e){return this.namedVectors[e]}transfer(e,t){const s=this.indexVector.buffer(),i={};t.push(s);for(const r in this.namedVectors){const n=this.namedVectors[r];i[r]={},n.transfer(i[r],t)}e.geometryType=this.geometryType,e.indexBuffer=s,e.namedBuffers=i,this.destroy()}intoBuffers(){const e=$.fromVertexVectors(this);return this.destroy(),e}destroy(){this.indexVector=null,this.namedVectors=null}};const Z=new Ve,R=new Ve,Ie=1.5,ss=5;function is(o,e){const t={};for(const s in o){const i={data:ge(e,o[s]),stride:o[s]};t[s]=i}return t}function rs(o){return[o.fill||{},o.line||{},o.icon||{},o.text||{},o.label||{}]}function ns(o){const e=[[],[],[],[],[]],t=o;for(const s of t)for(const i of s.displayRecords)e[i.geometryType].push(i);return e}let Me=class B{constructor(){this.tileDisplayData=null,this.tileBufferData=null}reshuffle(){var n,a,d;if(Z.reset(),!this.tileDisplayData)return;const e=ns(this.tileDisplayData.displayObjects);for(const l of e)for(const h of l)h&&Z.needMore(h.geometryType,h.meshData?h.meshData.vertexCount:h.vertexCount,h.meshData?h.meshData.indexData.length:h.indexCount);const t=e.length,s=new ne;for(let l=0;l<t;++l){s.geometries[l].indexBuffer=new Uint32Array(Math.round(Ie*Z.indicesFor(l)));const h=[],c=(n=this.tileBufferData)==null?void 0:n.geometries[l].vertexBuffer;if(!c)continue;for(const p in c)h.push(c[p].stride);const u=B._computeVertexAlignment(h),f=Math.round(Ie*Z.verticesFor(l)),m=B._align(f,u);for(const p in c){const g=c[p].stride;s.geometries[l].vertexBuffer[p]={stride:g,data:ge(m,g)}}}R.reset(),(a=this.tileDisplayData.displayList)==null||a.clear();for(let l=0;l<t;++l){const h=e[l];for(const c of h){if(c.meshData)c.writeMeshDataToBuffers(R.verticesFor(l),s.geometries[l].vertexBuffer,R.indicesFor(l),s.geometries[l].indexBuffer),c.meshData=null;else{const u=(d=this.tileBufferData)==null?void 0:d.geometries[l];if(u){const f=u.vertexBuffer,m=u.indexBuffer,p=s.geometries[l].vertexBuffer,g=s.geometries[l].indexBuffer,_=R.verticesFor(l),x=R.indicesFor(l);ce(_,x,p,g,c,f,m),c.vertexFrom=_,c.indexFrom=x}}R.needMore(l,c.vertexCount,c.indexCount)}}const{displayList:i,displayObjects:r}=this.tileDisplayData;if(i)for(const l of r)i.addToList(l.displayRecords);this.tileBufferData=s}getStrides(){var s;const e=[],t=(s=this.tileBufferData)==null?void 0:s.geometries;if(!t)return e;for(let i=0;i<t.length;++i){const r=t[i];e[i]={};for(const n in r.vertexBuffer)e[i][n]=r.vertexBuffer[n].stride}return e}clone(){var t,s;const e=new B;return e.tileBufferData=((t=this.tileBufferData)==null?void 0:t.clone())??null,e.tileDisplayData=((s=this.tileDisplayData)==null?void 0:s.clone())??null,e}_guessSize(){var n;const e=((n=this.tileDisplayData)==null?void 0:n.displayObjects)??[],t=Math.min(e.length,4),s=12,i=40;let r=0;for(let a=0;a<t;a++)r=Math.max(r,e[a].displayRecords.length);return 2*(e.length*s+e.length*r*i)}serialize(){const e=this.tileBufferData.serialize(),t=this.tileBufferData.getBuffers(),s=this.tileDisplayData.serialize(new Q(Int32Array,this._guessSize())).buffer();return t.push(s),{result:{displayData:s,bufferData:e},transferList:t}}static fromVertexData(e,t){const s={},i=new Map;for(const r of t)i.set(r.id,r);return Rt(r=>{const n=e.data[r];if(T(n)){const a=zt.from(n.records).getCursor();for(;a.next();){const d=a.id,l=a.materialKey,h=a.indexFrom,c=a.indexCount,u=a.vertexFrom,f=a.vertexCount,m=i.get(d),p=new fe(d,r,l);p.indexFrom=h,p.indexCount=c,p.vertexFrom=u,p.vertexCount=f,m.displayRecords.push(p)}s[r]=$.fromVertexData(n,r)}else s[r]=new ts(r,0,Ue.DEFAULT).intoBuffers()}),B.fromMeshData({displayObjects:t,vertexBuffersMap:s})}static fromMeshData(e){const t=new B,s=new j,i=new ne;s.displayObjects=e.displayObjects;for(const r in e.vertexBuffersMap){const n=e.vertexBuffersMap[r];i.geometries[r].indexBuffer=n.indexBuffer,i.geometries[r].vertexBuffer=n.namedBuffers}return t.tileDisplayData=s,t.tileBufferData=i,t}static bind(e,t){const s=new B;return s.tileDisplayData=e,s.tileBufferData=t,s}static create(e,t){const s=new B;s.tileDisplayData=new j,s.tileDisplayData.displayObjects=e;const i=[0,0,0,0,0],r=[0,0,0,0,0],n=[[],[],[],[],[]];for(const l of e)for(const h of l.displayRecords)n[h.geometryType].push(h),i[h.geometryType]+=h.meshData.vertexCount,r[h.geometryType]+=h.meshData.indexData.length;const a=new ne,d=rs(t);for(let l=0;l<ss;l++){const h=new Uint32Array(r[l]),c=is(d[l],i[l]);fe.writeAllMeshDataToBuffers(n[l],c,h),a.geometries[l]={indexBuffer:h,vertexBuffer:c}}return s.tileBufferData=a,s}static _align(e,t){const s=e%t;return s===0?e:e+(t-s)}static _computeVertexAlignment(e){let t=!1,s=!1;for(const i of e)i%4==2?t=!0:i%4!=0&&(s=!0);return s?4:t?2:1}};const I=512,F=50;function os(o,e){const t=V(e);if(!t)return null;const[s,i]=t.valid;return o[2]>i?[C([o[0],o[1],i,o[3]]),C([s,o[1],s+o[2]-i,o[3]])]:o[0]<s?[C([s,o[1],o[2],o[3]]),C([i-(s-o[0]),o[1],i,o[3]])]:null}function as(o){return o==="text"||o==="esriTS"}function ls(o){return o==="simple-marker"||o==="picture-marker"||o==="esriSMS"||o==="esriPMS"}function Le(o){switch(xt(o.geometry).type){case"point":case"multipoint":return 0;case"polyline":return 1;case"polygon":case"extent":return 2}return 0}function hs(o){if(!o)return null;const{xmin:e,ymin:t,xmax:s,ymax:i,spatialReference:r}=o;return new Xe({rings:[[[e,t],[e,i],[s,i],[s,t],[e,t]]],spatialReference:r})}class ee extends kt{static from(e,t,s){const i=Kt.createInstance(),r=[],n=e.filter(a=>!!a.geometry);for(const a of n){const d=ye(a.geometry);We(r,[a],d,!1,!1,t)}return new ee(i,r,s)}constructor(e,t,s){super(e,t,null),this._transform=s}get geometryType(){const e=this._current;return e?e.geometryType:null}get insertAfter(){return this._current.insertAfter}readGraphic(){return this._current}getCursor(){return this.copy()}copy(){const e=new ee(this.instance,this._features,this._transform);return this.copyInto(e),e}}const G=new Oe,le=new Oe,ze="esriGeometryPolyline";function $e(o){o.coords.length=0,o.lengths.length=0}let P=class W{constructor(){this.bounds=C(),this.graphic=null}static acquire(e=null,t,s,i,r){let n;return W._pool.length===0?n=new W:(n=W._pool.pop(),this._set.delete(n)),n.acquire(e,t,s,i,r),n}static release(e){e&&!this._set.has(e)&&(e.release(),this._pool.push(e),this._set.add(e))}static getCentroidQuantized(e,t){if(de(e.geometry)){const s=e.symbol;if(z(s))return null;if((s==null?void 0:s.layers.length)>0&&s.layers.some(i=>i.type==="text"||i.type==="marker")){const i=Je(e.geometry);return i!==null?qt(t,{},{x:i[0],y:i[1]},!1,!1):null}}return null}acquire(e=null,t,s,i,r){e&&this.set(e,t,s,i,r)}release(){this.graphic=null,this.symbolResource=null,this.geometry=null}get symbol(){return this.symbolResource.symbol}set(e,t,s,i,r){this.graphic=e,this.geometry=s,this.symbolResource=t,this.bounds=i,r&&(this.size=r)}getGeometryQuantized(e,t,s,i){const r=this.geometry,n=ye(r);if(z(n))return null;switch(n){case"esriGeometryPolygon":{const a=r,{rings:d}=a;if(!d||d.length===0)return null;let l;if(l=d.length===1&&d[0].length===2?A(e,{paths:[[d[0][0],d[0][1]]]}):A(e,this.geometry),!l){const h={x:d[0][0][0],y:d[0][0][1]};if(l=A(e,h),l){const{x:c,y:u}=l;return{rings:[[[c-1,u],[1,-1],[1,1],[-1,1],[-1,-1]]]}}}return l}case"esriGeometryPolyline":{const a=r;$e(G),$e(le);const d=a.hasZ??!1,l=a.hasM??!1;return et(G,a),tt(le,G,d,l,ze,e.scale[0]),st(G,le,d,l,ze,e),it(G,a.hasZ??!1,a.hasM??!1)}case"esriGeometryMultipoint":{const a=r,d=.5*i*Math.max(Math.abs(this.size[0])+this.size[2]-this.size[0],Math.abs(this.size[1])+this.size[3]-this.size[1]),l=V(s);let h=a.points;if(l){const[c,u]=l.valid,f=u-c;h=h.filter(m=>{if(m[0]+d>u||m[0]-d<c){const p=[...m];return m[0]+d>u?p[0]-=f:p[0]+=f,se(t,m,d)||se(t,p,d)}return se(t,m,d)})}return h.length===0?{points:h}:A(e,{points:h})}}return A(e,this.geometry)}};P._pool=[],P._set=new Set;const O={minX:0,minY:0,maxX:0,maxY:0},M=C(),Ae=1e-5;function N(o,e,t,s,i){return O.minX=e,O.minY=t,O.maxX=s,O.maxY=i,o.search(O)}function ds(o){return{minX:o.bounds[0],minY:o.bounds[1],maxX:o.bounds[2],maxY:o.bounds[3]}}class cs{constructor(e,t,s,i,r,n,a){this._graphics=i,this._onAdd=r,this._onRemove=n,this._hashToCIM=a,this._index=rt(9,ds),this._itemByGraphic=new Map,this._inflatedSizeHelper=new ve,this._tileInfoView=e,this._uidFieldName=s;const d=e.getClosestInfoForScale(t);d&&(this._resolution=this._tileInfoView.getTileResolution(d.level))}setResourceManager(e){this._cimResourceManager=e,this._hittestDrawHelper=new Ne(e)}hitTest(e,t,s,i,r){var m;e=nt(e,this._tileInfoView.spatialReference);const n=.5*i*window.devicePixelRatio*s;M[0]=e-n,M[1]=t-n,M[2]=e+n,M[3]=t+n;const a=.5*i*(s+F),d=N(this._index,e-a,t-a,e+a,t+a);if(!d||d.length===0)return[];const l=[],h=C(),c=C();for(const p of d){const{geometry:g,symbolResource:_}=p;this._getSymbolBounds(h,_,g,c,r),c[3]=c[2]=c[1]=c[0]=0,ot(h,M)&&((m=p.graphic)!=null&&m.visible)&&l.push(p)}if(l.length===0)return[];const u=this._hittestDrawHelper,f=[];for(const p of l){const{geometry:g,symbolResource:_}=p,{hash:x,textInfo:S}=_,k=this._hashToCIM.get(x);k&&u.hitTest(M,k.symbol,g,S,r,i)&&f.push(p)}return f.sort(us),f.map(p=>p.graphic)}getGraphicsData(e,t,s){const i=this._searchForItems(t);if(i.length===0||s.length===0)return[];i.sort((c,u)=>c.zorder-u.zorder),i[0].insertAfter=-1;for(let c=1;c<i.length;c++)i[c].insertAfter=i[c-1].graphic.uid;i.sort((c,u)=>c.graphic.uid-u.graphic.uid),s.sort((c,u)=>c.uid-u.uid);let r,n=0,a=0;const d=t.resolution,l=[],h={originPosition:"upperLeft",scale:[d,d],translate:[t.bounds[0],t.bounds[3]]};for(const c of s){for(a=-2;n<i.length;)if(r=i[n],n++,c.uid===r.graphic.uid){a=r.insertAfter;break}if(!(r!=null&&r.geometry)||a===-2)continue;const u=r.getGeometryQuantized(h,t.bounds,this._tileInfoView.spatialReference,d),f={...r.graphic.attributes};f[this._uidFieldName]=c.uid,r.groupId==null&&(r.groupId=e.createTemplateGroup(r.symbol,null)),l.push({centroid:P.getCentroidQuantized(r,h),geometry:u,attributes:f,symbol:r.symbol,groupId:r.groupId,insertAfter:a,zorder:r.zorder})}return l.sort((c,u)=>c.zorder-u.zorder),l}queryTileData(e,t){if(this._graphics.length===0)return[];const{bounds:s,resolution:i}=t,r=this._searchForItems(t),n=[];return r.length===0||this._createTileGraphics(n,e,r,{originPosition:"upperLeft",scale:[i,i],translate:[s[0],s[3]]},t),n}has(e){return this._itemByGraphic.has(e)}getBounds(e){const t=this._itemByGraphic.get(e);return t?t.bounds:null}getAllBounds(){return Array.from(this._itemByGraphic.values()).filter(e=>e.graphic.visible).map(e=>e.bounds)}addOrModify(e,t,s){if(!e||z(t))return;this.has(e)&&this.remove(e),this._onAdd(e);const i=[0,0,0,0],r=this._getSymbolBounds(null,t,s,i,0),n=P.acquire(e,t,s,T(r)?r:null,i);return this._itemByGraphic.set(e,n),s&&this._index.insert(n),n.bounds}remove(e){if(!this._itemByGraphic.has(e))return;this._onRemove(e);const t=this._itemByGraphic.get(e);t!=null&&t.bounds&&this._index.remove(t),this._itemByGraphic.delete(e)}updateZ(){const e=this._graphics.items;let t,s;for(let i=0;i<e.length;i++)s=e[i],t=this._itemByGraphic.get(s),t&&(t.zorder=i)}update(e,t,s){const i=this._itemByGraphic.get(e);i.groupId=null;const r=at(i.bounds);this._index.remove(i);const n=this._getSymbolBounds(i.bounds,t,s,i.size,0);return T(n)&&i.set(e,t,s,n,i.size),s&&this._index.insert(i),{oldBounds:r,newBounds:i.bounds}}updateLevel(e){if(this._resolution===e)return;this._resolution=e,this._index.clear();const t=this._itemByGraphic,s=[];for(const[i,r]of t){const n=this._getSymbolBounds(r.bounds,r.symbolResource,r.geometry,r.size,0);r.geometry&&T(n)&&(r.bounds=n,s.push(r))}this._index.load(s)}clear(){this._itemByGraphic.clear(),this._index.clear()}_createTileGraphics(e,t,s,i,r){const n=this._uidFieldName,a=this._tileInfoView.spatialReference,{bounds:d,resolution:l}=r;let h,c,u,f;s.sort((m,p)=>m.zorder-p.zorder);for(let m=0;m<s.length;m++){u=s[m],h=u.graphic,c=u.getGeometryQuantized(i,d,a,l),f=m===0?-1:s[m-1].graphic.uid;const p={...u.graphic.attributes};p[n]=h.uid,u.groupId==null&&(u.groupId=t.createTemplateGroup(u.symbol,null)),e.push({centroid:P.getCentroidQuantized(u,i),geometry:c,attributes:p,symbol:u.symbol,groupId:u.groupId,insertAfter:f,zorder:u.zorder})}}_searchForItems(e){const t=this._tileInfoView.spatialReference,s=e.bounds,i=V(t);if(i&&t.isWrappable){const[r,n]=i.valid,a=Math.abs(s[2]-n)<Ae,d=Math.abs(s[0]-r)<Ae;if((!a||!d)&&(a||d)){const l=e.resolution;let h;h=C(a?[r,s[1],r+l*F,s[3]]:[n-l*F,s[1],n,s[3]]);const c=N(this._index,s[0],s[1],s[2],s[3]),u=N(this._index,h[0],h[1],h[2],h[3]);return[...new Set([...c,...u])]}}return N(this._index,s[0],s[1],s[2],s[3])}_getSymbolBounds(e,t,s,i,r){if(!t||!t.symbol||!s)return null;if(e||(e=C()),lt(e,s),!i||i[0]===0&&i[1]===0&&i[2]===0&&i[3]===0){const{hash:d,textInfo:l}=t,h=this._hashToCIM.get(d);if(!h)return null;i||(i=[0,0,0,0]);const c=this._inflatedSizeHelper.getSymbolInflateSize(i,h.symbol,this._cimResourceManager,r,l);i[0]=E(c[0]),i[1]=E(c[1]),i[2]=E(c[2]),i[3]=E(c[3])}const n=this._resolution,a=ve.safeSize(i);return e[0]-=a*n,e[1]-=a*n,e[2]+=a*n,e[3]+=a*n,e}}const us=(o,e)=>{const t=Le(o.graphic),s=Le(e.graphic);return t===s?e.zorder-o.zorder:t-s},fs=o=>{let e=class extends ht(o){constructor(){super(...arguments),this.graphics=null,this.renderer=null}};return b([D()],e.prototype,"graphics",void 0),b([D()],e.prototype,"renderer",void 0),b([D()],e.prototype,"updating",void 0),b([D()],e.prototype,"view",void 0),e=b([Ge("esri.views.layers.GraphicsView")],e),e},ps=_t("esri-2d-graphic-debug");function he(o,e,t){let s=t.get(o);return s||(s={tile:e,addedOrModified:[],removed:[]},t.set(o,s)),s}let w=class extends fs(dt(Ze)){constructor(o){super(o),this._storage=new Et,this._displayIds=new Map,this._controller=new AbortController,this._tiles=new Map,this._graphicStoreUpdate=!1,this._graphicsSet=new Set,this._matcher=Promise.resolve(null),this._tileUpdateSet=new Set,this._tilesToUpdate=new Map,this._graphicIdToAbortController=new Map,this._attached=!1,this._updatingGraphicsTimer=null,this._hashToExpandedSymbol=new Map,this._hashToExpandedSymbolPromise=new Map,this._hashToCIMSymbolPromise=new Map,this._hashToCIM=new Map,this._processing=!1,this._needsProcessing=!1,this._pendingUpdate={added:new Set,updated:new Set,removed:new Set},this.lastUpdateId=-1,this.updateRequested=!1,this.defaultPointSymbolEnabled=!0,this.graphicUpdateHandler=this.graphicUpdateHandler.bind(this)}destroy(){this._updatingGraphicsTimer&&(clearTimeout(this._updatingGraphicsTimer),this._updatingGraphicsTimer=null,this.notifyChange("updating")),this._controller.abort(),this.container.destroy(),this._set("graphics",null),this._graphicStore.clear(),this._attributeStore=null,this._hashToExpandedSymbol.clear(),this.view=null,this.renderer=null,this._hashToCIM.clear(),this._hashToCIMSymbolPromise.clear(),this._hashToExpandedSymbolPromise.clear()}_createMatcher(o,e,t){if(o){const s=Te(o),i=Vt({indexCount:0,fields:{}},"feature",o,s);this._matcher=At(i,e,null,t)}}_createDisplayId(o){let e=this._displayIds.get(o);return e||(e=this._storage.createDisplayId(),this._displayIds.set(o,e)),e}initialize(){this._attributeStore=new Ht({type:"local",initialize:i=>Promise.resolve(this.container.attributeView.initialize(i)),update:i=>this.container.attributeView.requestUpdate(i),render:()=>this.container.requestRender()},ct("2d"),()=>this.notifyChange("updating")),this.container.hasHighlight=()=>this._attributeStore.hasHighlight;const o=i=>{this._createDisplayId(i.uid),this._setFilterState(i.uid,i.visible)},e=i=>{const r=this._displayIds.get(i.uid);this._displayIds.delete(i.uid),this._storage.releaseDisplayId(r)},t=new Gt(this.container.getMaterialItems.bind(this.container),this.view.featuresTilingScheme.tileInfo);this._graphicStore=new cs(this.view.featuresTilingScheme,this.view.state.scale,this.uid,this.graphics,o,e,this._hashToCIM),this._meshFactory=new Ot(null,this.uid,t),this._templateStore=t,this.handles.add([bt(()=>this.renderer,i=>{this._createMatcher(i,t,this.container.stage.resourceManager);for(const r of this.graphics)this._pendingUpdate.updated.add(r);this.requestUpdate()}),this.view.graphicsTileStore.on("update",this._onTileUpdate.bind(this)),this.container.on("attach",()=>{ps&&this.container.enableRenderingBounds(()=>this._graphicStore.getAllBounds()),this.graphics.items.length>0&&this._graphicsChangeHandler({target:this.graphics,added:this.graphics.items,removed:[],moved:[]}),this.handles.add(this.graphics.on("change",r=>this._graphicsChangeHandler(r)),"graphics");const i=this.container.stage.resourceManager;this._createMatcher(this.renderer,t,i),this._graphicStore.setResourceManager(i),this._attached=!0,this.notifyChange("updating")})]);const s=this.view.graphicsTileStore.tiles;this._onTileUpdate({added:s,removed:[]})}get updating(){return!this._attached||this._updatingGraphicsTimer!==null||this._tileUpdateSet.size>0||this._tilesToUpdate.size>0||this._attributeStore.isUpdating()}hitTest(o){if(!this.view||!this.view.position)return[];const{resolution:e,rotation:t}=this.view.state;return this._graphicStore.hitTest(o.x,o.y,2,e,t)}update(o){v(this._controller.signal);const e=o.state,{resolution:t}=e;if(this._graphicStore.updateLevel(t),this._graphicStoreUpdate=!0,this.updateRequested=!1,this._pendingUpdate.updated.size>0){if(!this._processing)return void this._updateGraphics();this._needsProcessing=!0}}viewChange(){this.requestUpdate()}requestUpdate(){this.updateRequested||(this.updateRequested=!0,this.requestUpdateCallback())}processUpdate(o){this.updateRequested&&(this.updateRequested=!1,this.update(o))}graphicUpdateHandler(o){const{graphic:e,property:t}=o;switch(t){case"attributes":case"geometry":case"symbol":this._pendingUpdate.updated.add(e),this.requestUpdate();break;case"visible":this._setFilterState(e.uid,e.visible),this._attributeStore.sendUpdates()}}setHighlight(o){const e=o.map(t=>this._displayIds.get(t)).filter(T);this._attributeStore.setHighlight(o,e)}_getIntersectingTiles(o){const e=this._graphicStore.getBounds(o);if(!e||ut(e)===0||ft(e)===0)return[];const t=os(e,this.view.spatialReference);return T(t)?[...new Set([...this.view.graphicsTileStore.boundsIntersections(t[0]),...this.view.graphicsTileStore.boundsIntersections(t[1])])]:this.view.graphicsTileStore.boundsIntersections(e)}async _updateTile(o){v(this._controller.signal);const e=o.tile,t=this._getGraphicsData(this._templateStore,e,o.addedOrModified),s=await this._processGraphics(e,t);return v(this._controller.signal),this._patchTile(e.key,{type:"update",addOrUpdate:s,remove:o.removed,end:!0,clear:!1,sort:!1}),s}_patchTile(o,e){const t=this._tiles.get(o);t&&(this.container.onTileData(t,e),this.container.requestRender())}_graphicsChangeHandler(o){const e=this._pendingUpdate;for(const t of o.added)e.added.add(t);for(const t of o.moved)e.updated.add(t);for(const t of o.removed)this._pendingUpdate.added.has(t)?e.added.delete(t):e.removed.add(t);this._processing?this._needsProcessing=!0:this._updateGraphics()}_getGraphicsToUpdate(){const o={added:[],removed:[],updated:[]};if(!this.graphics)return o;const e=this._pendingUpdate;for(const t of this.graphics.items)e.added.has(t)?o.added.push(t):e.updated.has(t)&&o.updated.push(t);for(const t of e.removed)this._graphicStore.has(t)&&o.removed.push(t);return e.added.clear(),e.removed.clear(),e.updated.clear(),o}async _updateGraphics(){this._processing=!0;const{added:o,removed:e,updated:t}=this._getGraphicsToUpdate(),s=this._tilesToUpdate;let i;try{if(!this._graphicStoreUpdate){const h=this.view.state,{resolution:c}=h;this._graphicStore.updateLevel(c)}const r=[],n=new Array(o.length+e.length);for(let h=0;h<t.length;h++){const c=t[h],u=this._getIntersectingTiles(c);for(const f of u)i=f.id,he(i,f,s).removed.push(this._displayIds.get(c.uid));r.push(this._updateGraphic(c,null)),n[h]=c}const a=t.length;for(let h=0;h<o.length;h++){const c=o[h];n[a+h]=c,this._graphicsSet.add(c),r.push(this._addGraphic(c))}for(const h of e){this._abortProcessingGraphic(h.uid);const c=this._getIntersectingTiles(h);for(const u of c)i=u.id,he(i,u,s).removed.push(this._displayIds.get(h.uid));this._graphicsSet.delete(h),this._graphicStore.remove(h)}let d;this._flipUpdatingGraphics(),await Promise.all(r);for(let h=0;h<n.length;h++){d=n[h];const c=this._getIntersectingTiles(d);for(const u of c)i=u.id,he(i,u,s).addedOrModified.push(d)}this._graphicStore.updateZ();const l=[];for(const[h,c]of s)l.push(this._updateTile(c));await Promise.all(l)}catch(r){K(r)}for(const r of e)try{const n=await this._getSymbolForGraphic(r,null);if(n){const a=n.hash();this._hashToExpandedSymbol.delete(a)}}catch(n){K(n)}s.clear(),this.notifyChange("updating"),this._processing=!1,this._needsProcessing&&(this._needsProcessing=!1,this._updateGraphics())}_getArcadeInfo(o){const e=(o.attributes?Object.keys(o.attributes):[]).map(t=>({name:t,alias:t,type:typeof o.attributes[t]=="string"?"esriFieldTypeString":"esriFieldTypeDouble"}));return z(o.geometry)?null:{geometryType:ye(o.geometry),spatialReference:qe.fromJSON(o.geometry.spatialReference),fields:e}}_getSymbolForGraphic(o,e){return v(this._controller.signal),T(o.symbol)?Promise.resolve(o.symbol):T(this.renderer)?this.renderer.getSymbolAsync(o,{scale:this.view.scale,signal:T(e)?e.signal:null}):Promise.resolve(this._getNullSymbol(o))}_getCIMSymbol(o,e,t){let s=this._hashToCIM.get(e);if(s)return Promise.resolve(s);const i=Ye(o);if(T(i)){if(i.type==="CIMSymbolReference")return s=i,this._hashToCIM.set(e,s),Promise.resolve(s);let r=this._hashToCIMSymbolPromise.get(e);return r||(r=i.fetchCIMSymbol(t).then(n=>(this._hashToCIM.set(e,n.data),this._hashToCIMSymbolPromise.delete(e),n)).catch(n=>(this._hashToCIMSymbolPromise.delete(e),_e(n),null)),this._hashToCIMSymbolPromise.set(e,r),r)}return Promise.resolve(null)}_expandCIMSymbol(o,e,t,s){const i=this._hashToExpandedSymbol.get(t);if(i)return Promise.resolve(i);let r=this._hashToExpandedSymbolPromise.get(t);if(r)return r;const n=this.container.stage,a=this._getArcadeInfo(e),d=Te(null),l=jt(o,d);return r=Ut(l,a,n.resourceManager,s),this._hashToExpandedSymbolPromise.set(t,r),r.then(h=>(this._hashToExpandedSymbol.set(t,h),this._hashToExpandedSymbolPromise.delete(t),h)),r}async _getSymbolResources(o,e){return v(this._controller.signal),this.container.stage?this._getSymbolForGraphic(o,e).then(t=>{if(!t)return null;const s=t.hash();return this._getCIMSymbol(t,s,e).then(i=>z(i)?null:this._expandCIMSymbol(i,o,s,e).then(r=>{const n=r.layers.filter(a=>a.type==="text"&&typeof a.text=="string");if(n&&n.length>0){const a=new Array(n.length);for(let l=0;l<n.length;l++){const h=n[l],c=[],[u]=Qe(h.text);h.text=u;for(let f=0;f<u.length;f++)c.push(u.charCodeAt(f));a[l]={symbol:h,id:l,glyphIds:c}}const d=new Map;return this.container.getMaterialItems(a).then(l=>{vt(l);for(let h=0;h<n.length;h++){const c=n[h];d.set(c.cim,{text:c.text,mosaicItem:l[h].mosaicItem})}return{symbol:r,textInfo:d,hash:s}})}return{symbol:r,hash:s}}))}).catch(t=>(_e(t),null)):null}async _projectAndNormalizeGeometry(o,e){if(v(this._controller.signal),z(o.geometry)||o.geometry.type==="mesh")return null;let t=o.geometry;if(de(t)){const r=t.rings;t.rings=r}else if(be(t)){const r=t.paths;t.paths=r}else if(De(t)){const r=await this._getSymbolForGraphic(o,e);v(this._controller.signal),r&&(ls(r.type)||as(r.type))?t=t.center:t=hs(t)}await Ct(t.spatialReference,this.view.spatialReference);const s=Dt(t),i=wt(s,t.spatialReference,this.view.spatialReference);return i&&pt(i),i}_onTileUpdate(o){const e=V(this.view.spatialReference);if(o.added&&o.added.length>0)for(const t of o.added)this._addNewTile(t,e);if(o.removed&&o.removed.length>0)for(const t of o.removed)this._removeTile(t.key)}async _addGraphic(o){this._abortProcessingGraphic(o.uid),v(this._controller.signal);const e=new AbortController;this._graphicIdToAbortController.set(o.uid,e);const t={signal:e.signal};try{await this._addOrUpdateGraphic(o,t),v(this._controller.signal),this._graphicIdToAbortController.delete(o.uid)}catch(s){if(this._graphicIdToAbortController.delete(o.uid),!K(s))throw s}}_updateGraphic(o,e){v(this._controller.signal);const t=this._projectAndNormalizeGeometry(o,e),s=this._getSymbolResources(o,e);return Promise.all([t,s]).then(([i,r])=>{v(this._controller.signal),this._graphicStore.addOrModify(o,r,i)})}_addOrUpdateGraphic(o,e){v(this._controller.signal);const t=this._projectAndNormalizeGeometry(o,e),s=this._getSymbolResources(o,e);return Promise.all([t,s]).then(([i,r])=>{v(this._controller.signal),this._graphicsSet.has(o)&&this._graphicStore.addOrModify(o,r,i)})}_addTile(o){const e=this.view.featuresTilingScheme.getTileBounds(C(),o),t=this.view.featuresTilingScheme.getTileResolution(o.level),s=new Qt(o,t,e[0],e[3]);return this._tiles.set(o,s),this.container.addChild(s),s}async _addNewTile(o,e){const t=this._addTile(o.key),s=this._graphicStore.queryTileData(this._templateStore,o);if(s.length===0)return;if(e){const r=Math.round((e.valid[1]-e.valid[0])/o.resolution);for(const n of s)n.geometry&&(Ce(n.geometry)||ie(n.geometry))&&this._wrapPoints(n,r)}const i=o.key;this._tileUpdateSet.add(o.key),this.notifyChange("updating");try{const r={type:"update",clear:!1,addOrUpdate:await this._processGraphics(o,s),remove:[],end:!0,sort:!1};t.patch(r),this._tileUpdateSet.delete(i),this.notifyChange("updating")}catch(r){if(this._tileUpdateSet.delete(i),this.notifyChange("updating"),!K(r))throw r}}_removeTile(o){if(!this._tiles.has(o))return;const e=this._tiles.get(o);this.container.removeChild(e),e.destroy(),this._tiles.delete(o)}_setFilterState(o,e){const t=this._displayIds.get(o),s=this._attributeStore.getHighlightFlag(o);this._attributeStore.setData(t,0,0,s|(e?Tt:0))}_getGraphicsData(o,e,t){const s=this.view,i=V(s.spatialReference),r=this._graphicStore.getGraphicsData(o,e,t);if(i){const n=Math.round((i.valid[1]-i.valid[0])/e.resolution);for(const a of r)a.geometry&&(Ce(a.geometry)||ie(a.geometry))&&this._wrapPoints(a,n)}return r}_wrapPoints(o,e){const t=o.geometry;ie(t)?this._wrapMultipoint(t,e):this._wrapPoint(o,e)}_wrapMultipoint(o,e){const t=o.points,s=[];let i=0,r=0;for(const[n,a]of t){if(s.push([n+i,a]),i=0,e===I){const d=5*F;n+r<d?(s.push([e,0]),i=-e):n+r>I-d&&(s.push([-e,0]),i=e)}else n+r<-F?(s.push([e,0]),i=-e):n+r>I+F&&(s.push([-e,0]),i=e);r+=n}o.points=s}_wrapPoint(o,e){const t=o.geometry;if(e===I){const s=5*F;t.x<s?o.geometry={points:[[t.x,t.y],[e,0]]}:t.x>I-s&&(o.geometry={points:[[t.x,t.y],[-e,0]]})}else t.x<-F?o.geometry={points:[[t.x,t.y],[e,0]]}:t.x>I+F&&(o.geometry={points:[[t.x,t.y],[-e,0]]})}_processGraphics(o,e,t){if(!(e&&e.length)||!this._meshFactory)return Promise.resolve(null);const s=ee.from(e,this.uid,o.transform),i=this._meshFactory;return this._matcher.then(r=>i.analyzeGraphics(s,this.container.stage.resourceManager,r,null,null,t).then(()=>(this._attributeStore.sendUpdates(),this._processAnalyzedGraphics(o,s))))}_processAnalyzedGraphics(o,e){const t=this._meshFactory,s=e.getSize(),i=e.getCursor(),r={features:s,records:s,metrics:0},n=new Pt(o.key.id,r,Ue.DEFAULT,!1,!1),a=[];for(;i.next();){const c=i.readGraphic();c.insertAfter=c.insertAfter===-1?-1:this._displayIds.get(c.insertAfter),c.displayId=this._displayIds.get(c.attributes[this.uid]);const u=new He(c.displayId);u.insertAfter=c.insertAfter,a.push(u),t.writeGraphic(n,i,o.level,this.container.stage.resourceManager)}const d=o.tileInfoView.tileInfo.isWrappable,l=n.serialize(d);if(l.length!==1)return new Me;const h=l[0].message;return Me.fromVertexData(h,a)}_abortProcessingGraphic(o){var e;(e=this._graphicIdToAbortController.get(o))==null||e.abort()}_getNullSymbol(o){const e=o.geometry;return be(e)?mt:de(e)||De(e)?yt:this.defaultPointSymbolEnabled?gt:null}_flipUpdatingGraphics(){this._updatingGraphicsTimer&&clearTimeout(this._updatingGraphicsTimer),this._updatingGraphicsTimer=setTimeout(()=>{this._updatingGraphicsTimer=null,this.notifyChange("updating")},160),this.notifyChange("updating")}};b([D({constructOnly:!0})],w.prototype,"requestUpdateCallback",void 0),b([D()],w.prototype,"container",void 0),b([D({constructOnly:!0})],w.prototype,"graphics",void 0),b([D()],w.prototype,"updating",null),b([D()],w.prototype,"view",void 0),b([D()],w.prototype,"updateRequested",void 0),b([D()],w.prototype,"defaultPointSymbolEnabled",void 0),w=b([Ge("esri.views.2d.layers.support.GraphicsView2D")],w);const js=w;export{js as a};
