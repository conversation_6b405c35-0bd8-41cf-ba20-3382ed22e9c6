package org.thingsboard.server.dao.model.sql.store;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.sql.department.GoodsShelfMapper;
import org.thingsboard.server.dao.util.imodel.response.annotations.Flatten;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseTenantName;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseViaMapper;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

@Getter
@Setter
@ResponseEntity
public class StoreInRecordDetail {
    // id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    // 入库单主表ID
    private String mainId;

    // 设备编码
    private String serialId;

    // 货架余量
    private Integer count;

    // 货架ID
    @ParseViaMapper(GoodsShelfMapper.class)
    private String shelvesId;

    // 数量
    private Double num;

    // 单价
    private Double price;

    // 税率
    private Double taxRete;

    // 租户ID
    @ParseTenantName
    private String tenantId;

    @Flatten
    @TableField(exist = false)
    private DeviceInfoResponse deviceInfoResponse;
}
