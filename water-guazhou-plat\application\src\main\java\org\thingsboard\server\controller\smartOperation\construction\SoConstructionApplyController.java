package org.thingsboard.server.controller.smartOperation.construction;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionApply;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionApplyContainer;
import org.thingsboard.server.dao.model.sql.smartOperation.device.SoDeviceItem;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemScope;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionApplyPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionApplySaveRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoDeviceItemPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoDeviceItemSaveRequest;
import org.thingsboard.server.dao.util.imodel.response.ExcelFileInfo;
import org.thingsboard.server.dao.util.reflection.ExceptionUtils;
import org.thingsboard.server.dao.construction.SoConstructionApplyService;
import org.thingsboard.server.utils.imodel.annotations.IStarController2;

import java.util.List;

@IStarController2
@RequestMapping("/api/so/constructionApply")
public class SoConstructionApplyController extends BaseController {
    @Autowired
    private SoConstructionApplyService service;


    @GetMapping
    public IPage<SoConstructionApplyContainer> findAllConditional(SoConstructionApplyPageRequest request) {
        return service.findAllConditional(request);
    }

    @GetMapping("/export/excel")
    public ExcelFileInfo exportExcel(SoConstructionApplyPageRequest request) throws ThingsboardException {
        if (request.getConstructionCode() == null) {
            ExceptionUtils.silentThrow("所属工程编号未传入");
        }
        List<SoConstructionApplyContainer> records = findAllConditional(request).getRecords();
        List<SoConstructionApply> items = null;
        if (records.size() > 0) {
            items = records.get(0).getItems();
        }
        return ExcelFileInfo.of(
                        String.format("工程%s-实施信息", request.getConstructionCode()), items
                )
                .nextTitle("code", "实施编号")
                .nextTitle("contractName", "所属合同")
                .nextTitle("contractType", "合同类别")
                .nextTitle("beginTimeName", "工期开始时间")
                .nextTitle("endTimeName", "工期开始时间")
                .nextTitle("creatorName", "创建人")
                .nextTitle("createTimeName", "创建时间");
    }

    @GetMapping("/export/global/excel")
    public ExcelFileInfo exportGlobalExcel(SoConstructionApplyPageRequest request) {
        return ExcelFileInfo.of("工程实施管理列表", findAllConditional(request).getRecords())
                .nextTitle("constructionCode", "工程编号")
                .nextTitle("constructionName", "工程名称");
    }

    @PostMapping
    public SoConstructionApply save(@RequestBody SoConstructionApplySaveRequest req) {
        req.preCheckUpdate(() -> !service.isComplete(req.getConstructionCode(), req.tenantId()), "已完成不可更改");
        return service.save(req);
    }

    @PostMapping("/{id}/complete")
    public boolean complete(@PathVariable String id) throws ThingsboardException {
        String userId = UUIDConverter.fromTimeUUID(getCurrentUser().getUuidId());
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return service.complete(id, userId, tenantId);
    }

    // @PatchMapping("/{id}")
    public boolean edit(@RequestBody SoConstructionApplySaveRequest req, @PathVariable String id) {
        if (StringUtils.isNotEmpty(req.getCode()) && service.isCodeExists(req.getCode(), req.tenantId(), req.getId())) {
            ExceptionUtils.silentThrow("编码重复");
        }
        return service.update(req.unwrap(id));
    }

    @DeleteMapping("/{id}")
    public boolean delete(@PathVariable String id) {
        if (service.isComplete(id)) {
            ExceptionUtils.silentThrow("已完成，不可删除");
        }
        return service.delete(id);
    }

    // region 设备项管理
    @GetMapping("/{code}/device")
    public IPage<SoDeviceItem> getDevices(@PathVariable String code, SoDeviceItemPageRequest request) {
        request.setScope(SoGeneralSystemScope.SO_CONSTRUCTION_APPLY);
        request.setIdentifier(code);
        request.withoutCode();
        return service.getDevices(request);
    }

    @PostMapping("/{code}/device")
    public List<SoDeviceItem> saveDevice(@PathVariable String code, @RequestBody List<SoDeviceItemSaveRequest> request) {
        for (SoDeviceItemSaveRequest item : request) {
            item.setIdentifier(code);
        }
        return service.saveDevice(request);
    }
    // endregion
}