package org.thingsboard.server.dao.sql.maintainCircuit.maintain;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.maintainCircuit.maintain.MaintainPlanC;

import java.util.List;
import java.util.Map;

/**
 * userMybatis
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-08-23
 */
@Mapper
public interface MaintainPlanCMapper extends BaseMapper<MaintainPlanC> {

    List<MaintainPlanC> getList(@Param("mainId") String mainId);

    List<Map> getMaintainList(@Param("deviceLabelCode") String deviceLabelCode, @Param("page") int page, @Param("size") int size);

    int getMaintainListCount(@Param("deviceLabelCode") String deviceLabelCode);

}
