package org.thingsboard.server.dao.model.request;

import lombok.Data;

@Data
public class PartitionCustRequest {

    private int page;

    private int size;

    private String partitionId; // 分区id

    private String partitionName; // 分区名

    private String meterBookId; // 册本id

    private String custCode;  // 用户编号

    private String custName;  // 户名

    private String waterCategory; // 用水类别

    private String copyMeterUser; // 抄表员

    private String businessHall; // 营业所

    private String isMount; // 是否挂接

    private String address;  // 用水地址

    private String tenantId;

    private Long start;

    private Long end;

}
