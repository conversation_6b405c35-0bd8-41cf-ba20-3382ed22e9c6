import Point from '@arcgis/core/geometry/Point.js'
import { GetDistrictPointsJson, GetKeyPoint } from '@/api/patrol'
import {
  createGeometry,
  createGraphic,
  getGeometryCenterPoint,
  getGraphicLayer,
  gotoAndHighLight,
  setSymbol
} from '@/utils/MapHelper'
import { useWaterPoint } from '@/hooks/arcgis'

export const useDistrict = (containerId: string) => {
  const staticState: {
    graphicsLayer?: __esri.GraphicsLayer
    keyPointLayer?: __esri.GraphicsLayer
    view?: __esri.MapView
  } = {}
  const waterPoint = useWaterPoint(containerId)
  const removeAll = () => {
    staticState.graphicsLayer?.removeAll()
    staticState.keyPointLayer?.removeAll()
  }
  const add = async (
    view?: __esri.MapView,
    areaId?: string,
    options?: {
      goto?: boolean
      highlight?: boolean
      ratio?: number
      showKeyPoint?: boolean
    }
  ) => {
    if (!view || areaId === undefined) return
    staticState.view = view
    staticState.graphicsLayer = getGraphicLayer(staticState.view, {
      id: 'district-area',
      title: '区域/路线'
    })
    removeAll()
    const res = await GetDistrictPointsJson(areaId)
    if (!res.data.data) return
    const pointjson = JSON.parse(res.data.data)
    if (!pointjson.geometry) return
    // 只有这两种情况：面和线
    const type = pointjson.geometry.rings
      ? 'polygon'
      : pointjson.geometry.paths
        ? 'polyline'
        : ''
    if (!type) return
    const geo = createGeometry(
      type,
      type === 'polygon' ? pointjson.geometry.rings : pointjson.geometry.paths,
      pointjson.geometry.spatialReference
    )
    const graphic = createGraphic({
      geometry: geo,
      symbol: setSymbol(type),
      attributes: { areaId }
    })
    staticState.graphicsLayer?.add(graphic)
    options?.goto
      && gotoAndHighLight(staticState.view, graphic, {
        avoidHighlight: !options.highlight,
        ratio: options.ratio
      })
    options?.showKeyPoint && refreshKeyPoint(areaId)
    if (!pointjson.bufferGeometry?.rings) return
    const bufferGeometry = createGeometry(
      'polygon',
      pointjson.bufferGeometry.rings,
      pointjson.bufferGeometry.spatialReference
    )
    const buffer = createGraphic({
      geometry: bufferGeometry,
      symbol: setSymbol(bufferGeometry?.type || 'polygon', {
        color: [0, 255, 0, 0.1],
        outlineWidth: 1,
        outlineColor: '#00ff00'
      })
    })
    staticState.graphicsLayer?.add(buffer)
  }

  const refreshKeyPoint = (areaId: string) => {
    staticState.keyPointLayer = getGraphicLayer(staticState.view, {
      id: 'key-point',
      title: '关键点'
    })
    GetKeyPoint({ areaId })
      .then(res => {
        const data = res.data.data?.data
        data?.map(item => {
          const geometry = new Point({
            longitude: item.lon,
            latitude: item.lat,
            spatialReference: staticState.view?.spatialReference
          })
          const g = createGraphic({
            geometry,
            symbol: setSymbol('point', {
              outlineWidth: 1,
              outlineColor: '#00ffff',
              color: '#ff0000'
            }),
            attributes: { ...item }
          })
          const t = createGraphic({
            geometry,
            symbol: setSymbol('text', {
              text: item.name,
              color: '#ff0000',
              yOffset: -25
            }),
            attributes: { ...item }
          })
          staticState.keyPointLayer?.addMany([g, t])
        })
      })
      .catch((error: any) => {
        console.log(error.message)
      })
  }
  const destroy = () => {
    staticState.graphicsLayer
      && staticState.view?.map?.remove(staticState.graphicsLayer)
    staticState.keyPointLayer
      && staticState.view?.map?.remove(staticState.keyPointLayer)
  }
  const extentTo = (type: 'area' | 'point', id?: string) => {
    const graphic = type === 'area'
      ? staticState.graphicsLayer?.graphics?.[0]
      : staticState.keyPointLayer?.graphics?.find(
        item => item.attributes?.id === id
      )
    if (!graphic) return
    gotoAndHighLight(staticState.view, graphic, { avoidHighlight: true })

    if (id === undefined) return
    waterPoint.removeAll()
    waterPoint.add(staticState.view, {
      id,
      point: getGeometryCenterPoint(graphic.geometry)
    })
  }
  const removeHighlight = () => {
    waterPoint.removeAll()
  }
  return {
    removeAll,
    add,
    destroy,
    refreshKeyPoint,
    extentTo,
    removeHighlight
  }
}
