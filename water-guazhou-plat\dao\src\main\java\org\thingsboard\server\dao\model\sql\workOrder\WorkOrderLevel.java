package org.thingsboard.server.dao.model.sql.workOrder;

import org.thingsboard.server.dao.util.TimeUtils;
import org.thingsboard.server.dao.util.imodel.response.NameDisplayableEnum;

import java.util.Date;
import java.util.concurrent.TimeUnit;

public enum WorkOrderLevel implements NameDisplayableEnum {
    A(12, TimeUnit.HOURS),
    B(1, TimeUnit.DAYS),
    C(3, TimeUnit.DAYS),
    D(7, TimeUnit.DAYS),
    ;


    private final long milliseconds;

    private final String displayName;

    WorkOrderLevel(int value, TimeUnit unit) {
        milliseconds = unit.toMillis(value);
        displayName = value + TimeUtils.timeUnitToChinese(unit);
    }

    public long getMilliseconds() {
        return milliseconds;
    }
    public Date getExpectFinishDate(Date start) {
        return new Date(start.getTime() + this.getMilliseconds());
    }

    public String getDisplayName() {
        return displayName;
    }
}
