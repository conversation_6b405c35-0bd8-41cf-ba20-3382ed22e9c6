import{d as v,M as L,c as m,a8 as w,s as E,r as d,x as g,a9 as _,o as N,g as j,n as A,q as l,i as s,F as M,b6 as O,b7 as F}from"./index-r0dFAfgr.js";import{_ as R}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as S}from"./CardTable-rdWOL4_6.js";import{_ as B}from"./CardSearch-CB_HNR-Q.js";import{I as h}from"./common-CvK_P_ao.js";import{t as V,u as Y,g as I,v as P}from"./manage-BReaEVJk.js";import{g as U}from"./projectManagement-CDcrrCQ1.js";import{f as W}from"./DateFormatter-Bm9a68Ax.js";import z from"./detail-DEo1RlcF.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";import"./index-CCFuhOrs.js";/* empty css                             */import"./DPlayer-Be2AurQX.js";import"./DPlayer.min-_HMH7IVX.js";import"./xmwcqk-Cxfq91Sa.js";import"./xmgc-Czrw1pVN.js";import"./cytbgs-WJxYGJyW.js";import"./gcwcqk-CV4EMT8B.js";import"./ssxmwcqk-BJgrXy2o.js";import"./gcsjjcxx-lLqauOhu.js";import"./sjbg-L9B2uWB9.js";import"./data-DDQ4eWNr.js";import"./gcysjcxx-BB9DfF9W.js";import"./qzjbxx-D98fv1p0.js";import"./htjbxx-CcjVPiVa.js";import"./htbg-CJ8T-1F4.js";import"./fygl-BCgGpKLc.js";import"./ssxq-C8LIbr3S.js";import"./ysqgcjcxx-5zZQS7XS.js";import"./ssgcjsjcxx-BD3tZw0Z.js";import"./ssgdjcxx-4P0LZdbp.js";import"./xmzysjcxx-DxVVq7LT.js";import"./xmzjsjcxx-C3UxQ9jk.js";import"./xmzgdjcxx-LKGnYC4Q.js";const $={class:"wrapper"},Ee=v({__name:"archive",setup(q){const{$btnPerms:u}=L(),f=m(),c=m(),b=m(),y=m({filters:[{label:"工程编号",field:"constructionCode",type:"input"},{label:"工程名称",field:"constructionName",type:"input"},{label:"工程类别",field:"constructionTypeId",type:"select",options:w(()=>i.projectType)}],operations:[{type:"btn-group",btns:[{type:"default",perm:!0,text:"导出",icon:h.DOWNLOAD,click:()=>{V().then(e=>{const t=window.URL.createObjectURL(e.data),a=document.createElement("a");a.style.display="none",a.href=t,a.setAttribute("download","归档管理.xlsx"),document.body.appendChild(a),a.click()})}},{type:"default",perm:!0,text:"重置",svgIcon:E(F),click:()=>{var e;(e=f.value)==null||e.resetForm(),n()}},{perm:!0,text:"查询",icon:h.QUERY,click:()=>n()}]}]}),r=d({defaultExpandAll:!0,indexVisible:!0,columns:[{label:"工程编号",prop:"constructionCode"},{label:"工程名称",prop:"constructionName"},{label:"工程类别",prop:"constructionTypeName"},{label:"创建人",prop:"creatorName"},{label:"创建时间",prop:"createTime",formatter:e=>W(e.createTime,"YYYY-MM-DD HH:mm:ss")},{label:"工作状态",prop:"status",tag:!0,tagColor:e=>e.createTime===null?"#409EFF":"#67C23A",formatter:e=>e.createTime===null?"处理中":"已完成"}],operationWidth:"260px",operations:[{disabled:e=>!e.createTime,isTextBtn:!1,text:"详情",perm:u("RoleManageEdit"),click:e=>{var t;i.selected=e,(t=b.value)==null||t.openDrawer()}},{disabled:e=>e.createTime!==null,isTextBtn:!1,type:"primary",text:"添加归档",perm:u("RoleManageEdit"),click:e=>{T(e)}},{disabled:e=>e.createTime===null,isTextBtn:!1,type:"success",text:"编辑归档",perm:u("RoleManageEdit"),click:e=>C(e)}],dataList:[],pagination:{page:1,limit:20,total:0,refreshData:({page:e,size:t})=>{r.pagination.page=e,r.pagination.limit=t,n()}}}),o=d({title:"添加工程预算信息",labelWidth:"130px",dialogWidth:"1000px",submitting:!1,submit:e=>{o.submitting=!0;let t="新增";e.id&&(t="修改"),e.pipLengthDesign=JSON.stringify(e.pipLengthDesign),Y(e).then(a=>{var p;o.submitting=!1,a.data.code===200?(g.success(t+"成功"),(p=c.value)==null||p.closeDialog(),n()):g.warning(t+"失败")}).catch(a=>{o.submitting=!1,g.warning(a)})},defaultValue:{},group:[{fields:[{xs:12,type:"input",label:"工程编号",field:"constructionCode",disabled:!0},{xs:12,type:"input",label:"工程名称",field:"constructionName",disabled:!0},{xs:12,type:"input",label:"工程类别",field:"constructionTypeName",disabled:!0},{type:"textarea",label:"备注",field:"remark"},{type:"file",label:"附件",field:"attachments"}]}]}),x=d({title:"详情",group:[],width:"80%",modalClass:"lightColor",cancel:!1}),T=e=>{var t;o.title="添加工程归档",i.DesignTubeLength=[],o.defaultValue={...e||{}},(t=c.value)==null||t.openDialog()},C=e=>{var t;o.title="编辑工程归档",i.DesignTubeLength=[],o.defaultValue={...e||{}},(t=c.value)==null||t.openDialog()},i=d({projectList:[],projectType:[],selected:{},DesignTubeLength:[],getOptions:()=>{U({page:1,size:-1}).then(e=>{i.projectList=_(e.data.data.data||[],"children",{label:"name",value:"code"})}),I({page:1,size:-1}).then(e=>{i.projectType=_(e.data.data.data||[],"children")})}}),n=async()=>{var t;const e={size:r.pagination.limit||20,page:r.pagination.page||1,...((t=f.value)==null?void 0:t.queryParams)||{}};P(e).then(a=>{r.dataList=a.data.data.data||[],r.pagination.total=a.data.data.total||0})};return N(()=>{n(),i.getOptions()}),(e,t)=>{const a=B,p=S,D=R,k=O;return j(),A("div",$,[l(a,{ref_key:"refSearch",ref:f,config:s(y)},null,8,["config"]),l(p,{config:s(r),class:"card-table"},null,8,["config"]),l(D,{ref_key:"refForm",ref:c,config:s(o)},null,8,["config"]),l(k,{ref_key:"refDetail",ref:b,config:s(x)},{default:M(()=>[l(z,{config:s(i).selected,show:13},null,8,["config"])]),_:1},8,["config"])])}}});export{Ee as default};
