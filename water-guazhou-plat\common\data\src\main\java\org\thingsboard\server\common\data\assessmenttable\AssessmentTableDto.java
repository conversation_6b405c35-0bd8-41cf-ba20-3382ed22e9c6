package org.thingsboard.server.common.data.assessmenttable;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.thingsboard.server.common.data.id.TenantId;

import java.util.Date;
import java.util.List;

/**
 * 考核表DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class AssessmentTableDto {

    /**
     * id
     */
    @JsonProperty("id")
    private String id;

    /**
     * 租户ID
     */
    @JsonProperty("tenantId")
    private TenantId tenantId;

    /**
     * 考核表名称
     */
    @JsonProperty("name")
    private String name;

    /**
     * 区域
     */
    @JsonProperty("region")
    private String region;

    /**
     * 所在分区
     */
    @JsonProperty("partition")
    private String partition;

    /**
     * 考核周期（年月，格式：YYYY-MM）
     */
    @JsonProperty("period")
    private String period;
    
    /**
     * 考核类型
     * 1: 水质考核
     * 2: 供水安全考核
     * 3: 管网漏损考核
     * 4: 能耗考核
     * 5: 设备运行考核
     * 6: 综合评估
     */
    @JsonProperty("assessmentType")
    private String assessmentType;
    
    /**
     * 考核等级
     * A: 优秀
     * B: 良好
     * C: 合格
     * D: 不合格
     */
    @JsonProperty("assessmentLevel")
    private String assessmentLevel;
    
    /**
     * 考核状态
     * 0: 草稿
     * 1: 已提交
     * 2: 审核中
     * 3: 已通过
     * 4: 已驳回
     */
    @JsonProperty("status")
    private String status;
    
    /**
     * 总分
     */
    @JsonProperty("totalScore")
    private Double totalScore;
    
    /**
     * 审核人
     */
    @JsonProperty("reviewer")
    private String reviewer;
    
    /**
     * 审核时间
     */
    @JsonProperty("reviewTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date reviewTime;

    /**
     * 备注
     */
    @JsonProperty("remark")
    private String remark;

    /**
     * 创建人
     */
    @JsonProperty("creator")
    private String creator;

    /**
     * 创建时间
     */
    @JsonProperty("createTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    
    /**
     * 考核表明细项列表
     */
    @JsonProperty("items")
    private List<AssessmentTableItemDto> items;
} 