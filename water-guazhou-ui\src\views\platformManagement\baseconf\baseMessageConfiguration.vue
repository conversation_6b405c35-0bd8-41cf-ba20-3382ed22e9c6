<template>
  <div class="wrapper">
    <CardSearch
      ref="refSearch"
      :config="SearchConfig"
    ></CardSearch>
    <CardTable
      class="card-table"
      :config="TableConfig"
    ></CardTable>
    <DialogForm
      ref="refDialogForm"
      :config="DialogFormConfig"
    ></DialogForm>
  </div>
</template>

<script setup>
import { onMounted, reactive, ref } from 'vue'
import { 
  getMessageConfigList,
  addMessageConfig,
  editMessageConfig,
  deleteMessageConfig,
  getMessageConfigDetail
} from '@/api/platformManagement/baseMessageConfiguration'
import { SLConfirm, SLMessage } from '@/utils/Message'

const refSearch = ref()
const refDialogForm = ref()

const SearchConfig = reactive({
  labelWidth: '100px',
  filters: [
    { 
      type: 'input', 
      label: '配置名称', 
      field: 'configName', 
      placeholder: '请输入配置名称',
      onChange: () => refreshData() 
    },
    {
      type: 'btn-group',
      btns: [
        { type: 'primary', perm: true, text: '查询', click: () => refreshData() },
        { perm: true, type: 'primary', text: '新增', click: () => handleAdd() },
        { perm: true, type: 'danger', text: '批量删除', click: () => handleDelete() }
      ]
    }
  ],
  defaultParams: {}
})

const TableConfig = reactive({
  columns: [
    { label: '配置名称', prop: 'configName' },
    { label: '消息类型', prop: 'messageType' },
    { label: '目标平台', prop: 'platform' },
    { label: '启用状态', prop: 'status' },
    { label: '消息标题模板', prop: 'titleTemplate' },
    { label: '消息内容模板', prop: 'contentTemplate' },
    { label: '内容编码格式', prop: 'encoding' },
    { label: '推送失败重试次数', prop: 'retryCount' },
    { label: '重试间隔', prop: 'retryInterval' },
    { label: '每秒推送速率限制', prop: 'rateLimit' },
    { label: '是否异步推送', prop: 'isAsync' },
  ],
  dataList: [],
  operations: [
    {
      perm: true,
      type: 'primary',
      isTextBtn: true,
      text: '查看详情',
      click: (row) => handleDetail(row)
    },
    {
      perm: true,
      type: 'primary',
      isTextBtn: true,
      text: '编辑',
      click: (row) => handleAdd(row)
    },
    {
      perm: true,
      type: 'danger',
      isTextBtn: true,
      text: '删除',
      click: (row) => handleDelete(row)
    }
  ],
  pagination: {
    total: 0,
    page: 1,
    align: 'right',
    limit: 20,
    handlePage: (page) => {
      TableConfig.pagination.page = page
      refreshData()
    },
    handleSize: (size) => {
      TableConfig.pagination.limit = size
      refreshData()
    }
  },
  handleSelectChange: (rows) => {
    TableConfig.selectList = rows || []
  }
})

const DialogFormConfig = reactive({
  title: '新增消息配置',
  group: [
    {
      fields: [
        {
          type: 'input',
          label: '配置名称',
          field: 'configName',
          rules: [{ required: true, message: '请输入配置名称' }]
        },
        {
          type: 'input',
          label: '消息类型',
          field: 'messageType',
          rules: [{ required: true, message: '请输入消息类型' }]
        },
        {
          type: 'input',
          label: '目标平台',
          field: 'platform',
          rules: [{ required: true, message: '请输入目标平台' }]
        },
        {
          type: 'input',
          label: '启用状态',
          field: 'status',
          rules: [{ required: true, message: '请输入启用状态' }]
        },
        {
          type: 'input',
          label: '消息标题模板',
          field: 'titleTemplate',
          rules: [{ required: true, message: '请输入消息标题模板' }]
        },
        {
          type: 'input',
          label: '消息内容模板',
          field: 'contentTemplate',
          rules: [{ required: true, message: '请输入消息内容模板' }]
        },
        {
          type: 'input',
          label: '内容编码格式',
          field: 'encoding',
          rules: [{ required: true, message: '请输入内容编码格式' }]
        },
        {
          type: 'input',
          label: '推送失败重试次数',
          field: 'retryCount',
          rules: [{ required: true, message: '请输入推送失败重试次数' }]
        },
        {
          type: 'input',
          label: '重试间隔',
          field: 'retryInterval',
          rules: [{ required: true, message: '请输入重试间隔' }]
        },
        {
          type: 'input',
          label: '每秒推送速率限制',
          field: 'rateLimit',
          rules: [{ required: true, message: '请输入每秒推送速率限制' }]
        },
        {
          type: 'input',
          label: '是否异步推送',
          field: 'isAsync',
          rules: [{ required: true, message: '请输入是否异步推送' }]
        }
      ]
    }
  ],
  labelPosition: 'top',
  defaultValue: {},
  dialogWidth: 600,
  draggable: true,
  showSubmit: true,
  showCancel: true,
  cancelText: '取消',
  submitText: '确定',
  submit: async (params) => {
    try {
      if (params.id) {
        await editMessageConfig(params)
        SLMessage.success('修改成功')
      } else {
        await addMessageConfig(params)
        SLMessage.success('新增成功')
      }
      refDialogForm.value?.closeDialog()
      refreshData()
    } catch (error) {
      SLMessage.error('操作失败')
    }
  }
})

// 重置对话框配置
const resetDialogConfig = () => {
  DialogFormConfig.group[0].fields.forEach(field => {
    field.disabled = false
    field.readonly = false
  })
  
  DialogFormConfig.showSubmit = true
  DialogFormConfig.showCancel = true
  DialogFormConfig.cancelText = '取消'
  DialogFormConfig.submitText = '确定'
  
  DialogFormConfig.submit = async (params) => {
    try {
      if (params.id) {
        await editMessageConfig(params)
        SLMessage.success('修改成功')
      } else {
        await addMessageConfig(params)
        SLMessage.success('新增成功')
      }
      refDialogForm.value?.closeDialog()
      refreshData()
    } catch (error) {
      SLMessage.error('操作失败')
    }
  }
}

// 查看详情
const handleDetail = async (row) => {
  try {
    const res = await getMessageConfigDetail(row.id)
    const detailData = res.data?.data || res
    
    resetDialogConfig()
    DialogFormConfig.title = '消息配置详情'
    DialogFormConfig.defaultValue = { ...detailData }
    DialogFormConfig.group[0].fields.forEach(field => {
      field.disabled = true
    })
    DialogFormConfig.showSubmit = false
    DialogFormConfig.cancelText = '关闭'
    refDialogForm.value?.openDialog()
  } catch (error) {
    SLMessage.error('获取详情失败')
  }
}

// 新增/编辑
const handleAdd = (row) => {
  resetDialogConfig()
  
  if (row) {
    DialogFormConfig.title = '编辑消息配置'
    DialogFormConfig.defaultValue = { ...row }
  } else {
    DialogFormConfig.title = '新增消息配置'
    DialogFormConfig.defaultValue = {}
  }
  
  refDialogForm.value?.openDialog()
}

// 删除
const handleDelete = async (row) => {
  try {
    const ids = row ? [row.id] : TableConfig.selectList.map(item => item.id)
    if (!ids.length) {
      SLMessage.warning('请选择要删除的数据')
      return
    }
    
    await SLConfirm('确定要删除选中的数据吗？')
    await deleteMessageConfig(ids)
    SLMessage.success('删除成功')
    refreshData()
  } catch (error) {
    if (error !== 'cancel') {
      SLMessage.error('删除失败')
    }
  }
}

// 刷新数据
const refreshData = async () => {
  try {
    const res = await getMessageConfigList({
      page: TableConfig.pagination.page,
      size: TableConfig.pagination.limit,
      ...(refSearch.value?.queryParams || {})
    })
    const responseData = res.data?.data || res
    TableConfig.dataList = responseData.records || responseData
    TableConfig.pagination.total = responseData.total || responseData.length || 0
  } catch (error) {
    SLMessage.error('数据加载失败')
  }
}

onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.wrapper {
  padding: 20px;
}

.card-table {
  margin-top: 20px;
}
</style>