# gis资源本地化(@arcgis.core)

## 方式一 资源存放到项目目录中（推荐）

1. 把/mode_modules/@arcgis/core/assets目录拷贝到public/Arcgis目录中

2. main.ts中添加如下内容：

   ```ts
   import esriConfig from '@arcgis/core/config';
   import '@arcgis/core/assets/esri/themes/dark/main.scss';
   // 指定资源路径
   esriConfig.assetsPath = '/Arcgis/assets';
   ```

## 方式二 在iis中部署arcgisjsapi

优点是打包会秒快一点，包体积会小一点
缺点是此方法会比方法一更麻烦点，截止4.26版本官方不建议这么做，因为会导致更多的请求，影响用户体验，因为文件没有打包，都是散文件

### 部署

1. 进行[arcgisjsapi](https://developers.arcgis.com/javascript/latest/)官网
2. 目前版本4.26,点击sign in，需要登录进入dashboard才能下载，如果没有注册过需要先注册，访问外网有困难的话注册可能很慢
3. 登录后点击DashBoard后即可看到Downloads标签，点击进入选择对应版本的api（当前arcgis_js_v426_api.zip)进行下载
4. 下载后解压并进行部署
5. iis创建网站，并指定资源目录（刚解压的文件的目录，比如C:\weiqing\sites\web\arcgis_js_api_sdk\arcgis_js_v426_api）,
6. 配置跨域
   1. 点击HTTP响应标头,点击添加
   2. 添加：Access-Control-Allow-Headers/X-Requested-With,Authorization-Token,Token,departmentid,Content-Type
   3. 添加：Access-Control-Allow-Methods/GET, POST, PUT, DELETE, OPTIONS
   4. 添加：Access-Control-Allow-Origin/\*
7. 打开目录浏览： 点击目录浏览，点击启动
8. 文件夹中修改install.html为index.html
9. iis中点击默认文档，将index.html移动到第一位
10. 刷新网站并重启，点击预览，默认打开index.html即部署完成

### 配置

1. main.ts中添加如下内容：

   ```ts
   import esriConfig from '@arcgis/core/config';
   import '@arcgis/core/assets/esri/themes/dark/main.scss';
   // 指定资源路径，根据具体的部署服务器信息修改路径
   esriConfig.assetsPath =
     'http://***********:8003/arcgis_js_api/javascript/4.26/@arcgis/core/assets/';
   ```

2. 所有引入文件都要加后缀

   ```js
   // 之前
   import Polyline from '@arcgis/core/geometry/Polyline';
   ```

   ```js
   // 之后
   import Polyline from '@arcgis/core/geometry/Polyline.js';
   ```

3. vite.config.ts文件修改

   ```js
   resolve: {
     alias: [
       ...,
       {
           // 作用是把所有@arcgis/core的文件引入都转发到iis服务器的api中
         find: '@arcgis/core',
         replacement:
           'http://***********:8003/arcgis_js_api/javascript/4.26/@arcgis/core'
       }
     ]
   }
   ```

到此方法二就完成了
