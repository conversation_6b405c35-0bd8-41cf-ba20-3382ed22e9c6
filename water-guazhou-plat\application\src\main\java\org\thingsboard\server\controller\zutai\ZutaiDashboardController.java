package org.thingsboard.server.controller.zutai;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.zutai.ZutaiDashboardService;

import java.util.List;
import java.util.Map;

/**
 * 组态详情
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2021-06-15
 */
@RestController
@RequestMapping("api/zutai/dashboard")
public class ZutaiDashboardController extends BaseController {
    @Autowired
    private ZutaiDashboardService dashboardService;

    /**
     * 获取详情
     * @param params
     * @return
     * @throws ThingsboardException
     */
    @GetMapping
    public List getList(@RequestParam(required = false) Map params) throws ThingsboardException {
        params.put("tenantId", UUIDConverter.fromTimeUUID(getTenantId().getId()));
        return dashboardService.getList(params);
    }

    /**
     * 保存
     * @param zutaiDashboardEntityMap
     * @return
     * @throws ThingsboardException
     */
    @PostMapping
    public Map save(@RequestBody Map zutaiDashboardEntityMap) throws ThingsboardException {
        zutaiDashboardEntityMap.put("tenantId", UUIDConverter.fromTimeUUID(getTenantId().getId()));
        return dashboardService.save(zutaiDashboardEntityMap);
    }

    /**
     * 删除
     * @param id
     */
    @DeleteMapping
    public void delete(@RequestParam String id) {
        dashboardService.delete(id);
    }
}
