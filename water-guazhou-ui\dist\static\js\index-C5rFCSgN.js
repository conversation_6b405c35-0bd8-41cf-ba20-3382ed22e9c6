import{j as m,z as W,d as L,c as C,r as k,l as c,o as N,bB as w,Q as G,ay as I,g as h,n as S,q as v,F as x,p as o,bh as D,h as P,aq as F,C as M}from"./index-r0dFAfgr.js";import{_ as R}from"./Search-NSrhrIa_.js";import{_ as A}from"./index-C9hz-UZb.js";const O=i=>{console.log("图表数据:",i);const d=i.map(l=>l.month),n=i.map(l=>l.waterSupplyIssues),b=i.map(l=>l.heatSupplyIssues),_=i.map(l=>l.pipelineMaintenance),a=i.map(l=>l.valveMaintenance);return console.log("月份:",d),console.log("水表问题:",n),console.log("热表设备:",b),console.log("管网维修:",_),console.log("阀门维修:",a),{backgroundColor:m().isDark?"#131624":"#F4F7FA",tooltip:{trigger:"axis",axisPointer:{type:"shadow"},formatter:function(l){let r=`${l[0].axisValue}<br/>`;return l.forEach(y=>{r+=`${y.marker}${y.seriesName}: ${y.value}<br/>`}),r}},legend:{data:["水表问题","热表设备","管网维修","阀门维修"],top:10,right:20,textStyle:{color:m().isDark?"#fff":"#666",fontSize:12}},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:[{type:"category",data:d,axisTick:{alignWithLabel:!0},axisLabel:{color:m().isDark?"#fff":"#666",fontSize:12,rotate:d.length>6?45:0,interval:0},axisLine:{lineStyle:{color:m().isDark?"#444":"#ddd"}}}],yAxis:[{type:"value",minInterval:1,axisLabel:{color:m().isDark?"#fff":"#666",fontSize:12,formatter:"{value}"},axisLine:{lineStyle:{color:m().isDark?"#444":"#ddd"}},splitLine:{lineStyle:{color:m().isDark?"#333":"#eee"}}}],series:[{name:"水表问题",type:"bar",emphasis:{focus:"series"},data:n,itemStyle:{color:"#5B9BD5"},barWidth:"15%",barGap:"10%"},{name:"热表设备",type:"bar",emphasis:{focus:"series"},data:b,itemStyle:{color:"#70AD47"},barWidth:"15%",barGap:"10%"},{name:"管网维修",type:"bar",emphasis:{focus:"series"},data:_,itemStyle:{color:"#FFC000"},barWidth:"15%",barGap:"10%"},{name:"阀门维修",type:"bar",emphasis:{focus:"series"},data:a,itemStyle:{color:"#264478"},barWidth:"15%",barGap:"10%"}]}},V=i=>W({url:"/api/repairOverview",method:"get",params:i}),z=i=>W({url:"/api/repairOverview/statistics",method:"get",params:i}),q={class:"wrapper",key:"repair-overview-page"},Y={class:"chart-header"},$={class:"chart-legend"},j={class:"legend-item"},J={class:"legend-value"},Q={class:"legend-item"},U={class:"legend-value"},H={class:"legend-item"},K={class:"legend-value"},X={class:"legend-item"},Z={class:"legend-value"},ee={key:1,style:{height:"400px",display:"flex","align-items":"center","justify-content":"center"}},ae={key:0},te={key:1},E="YYYY-MM-DD",oe=L({__name:"index",setup(i){const d=C(),n=C(),b=()=>{var t;(t=d.value)==null||t.resetForm(),g()},_=k({filters:[{field:"date",label:"创建时间",type:"daterange"},{field:"status",label:"状态",type:"select",options:[{label:"待审核",value:"PENDING_REVIEW"},{label:"待接单",value:"ASSIGNED"},{label:"处理中",value:"PROCESSING"},{label:"已完成",value:"COMPLETED"},{label:"已撤回",value:"REJECTED"}]}],operations:[{type:"btn-group",btns:[{perm:!0,type:"primary",text:"查询",icon:"iconfont icon-chaxun",click:()=>g()},{perm:!0,type:"default",text:"重置",click:()=>b()}]}],handleSearch:()=>g(),defaultParams:{date:[c().subtract(7,"day").format(E),c().format(E)]}}),a=k({barOption:null,summary:{waterSupplyTotal:0,heatSupplyTotal:0,pipelineTotal:0,valveTotal:0},loading:!1}),r=k({columns:[{label:"维修事件",prop:"title",minWidth:150},{label:"上报地址",prop:"address",minWidth:200},{label:"维修类型",prop:"typeName",minWidth:120},{label:"维修状态",prop:"status",minWidth:100,formatter:t=>({PENDING_REVIEW:"待审核",ASSIGNED:"待接单",PROCESSING:"处理中",COMPLETED:"已完成",REJECTED:"已撤回"})[t.status]||t.status},{label:"创建时间",prop:"createTime",minWidth:180,formatter:t=>t.createTime||"-"},{label:"创建人",prop:"organizerName",minWidth:100},{label:"备注",prop:"remark",minWidth:150}],dataList:[],pagination:{page:1,limit:10,total:0,change:(t,e)=>{r.pagination.page=t,r.pagination.limit=e,g()}}}),y=async()=>{var t;try{a.loading=!0;const e={fromTime:c().subtract(1,"year").valueOf(),toTime:c().valueOf()},s=((t=(await z(e)).data)==null?void 0:t.data)||{};if(console.log("月度数据:",s.monthlyData),a.summary=s.summary||a.summary,n.value)try{n.value.clear()}catch(f){console.warn("图表清理失败:",f)}await w();const u=O(s.monthlyData||[]);a.barOption=u,n.value&&u&&n.value.setOption(u)}catch(e){console.error("获取维修图表数据失败:",e);const p=O([]);a.barOption=p,a.summary={waterSupplyTotal:0,heatSupplyTotal:0,pipelineTotal:0,valveTotal:0},n.value&&n.value.setOption(p)}finally{a.loading=!1}},g=async()=>{var t,e,p;try{const s=((t=d.value)==null?void 0:t.queryParams)||{},[u,f]=((e=s.date)==null?void 0:e.length)===2?[c(s.date[0]).valueOf(),c(s.date[1]).valueOf()]:[c().subtract(7,"days").valueOf(),c().valueOf()],B={fromTime:u,toTime:f,title:s.title,type:s.type,status:s.status,page:r.pagination.page,size:r.pagination.limit},T=((p=(await V(B)).data)==null?void 0:p.data)||{};r.dataList=T.data||[],r.pagination.total=T.total||0}catch(s){console.error("获取维修记录数据失败:",s),r.dataList=[],r.pagination.total=0}};return N(async()=>{try{await w(),await y(),await g()}catch(t){console.error("页面初始化失败:",t),a.barOption||(a.barOption=O([]))}}),G(()=>{var t,e;if(n.value)try{(e=(t=n.value).dispose)==null||e.call(t)}catch(p){console.warn("图表清理失败:",p)}a.barOption=null,a.loading=!1}),(t,e)=>{const p=I("VChart"),s=A,u=R,f=F;return h(),S("div",q,[v(s,{class:"card",title:"维修统计",overlay:""},{default:x(()=>[o("div",Y,[o("div",$,[o("div",j,[e[0]||(e[0]=o("span",{class:"legend-color",style:{"background-color":"#5B9BD5"}},null,-1)),e[1]||(e[1]=o("span",{class:"legend-text"},"水表问题",-1)),o("span",J,D(a.summary.waterSupplyTotal),1)]),o("div",Q,[e[2]||(e[2]=o("span",{class:"legend-color",style:{"background-color":"#70AD47"}},null,-1)),e[3]||(e[3]=o("span",{class:"legend-text"},"热表设备",-1)),o("span",U,D(a.summary.heatSupplyTotal),1)]),o("div",H,[e[4]||(e[4]=o("span",{class:"legend-color",style:{"background-color":"#FFC000"}},null,-1)),e[5]||(e[5]=o("span",{class:"legend-text"},"管网维修",-1)),o("span",K,D(a.summary.pipelineTotal),1)]),o("div",X,[e[6]||(e[6]=o("span",{class:"legend-color",style:{"background-color":"#264478"}},null,-1)),e[7]||(e[7]=o("span",{class:"legend-text"},"阀门维修",-1)),o("span",Z,D(a.summary.valveTotal),1)])])]),a.barOption?(h(),P(p,{key:0,ref_key:"refBarChart",ref:n,option:a.barOption,style:{height:"400px",width:"100%"}},null,8,["option"])):(h(),S("div",ee,[a.loading?(h(),S("span",ae,"加载中...")):(h(),S("span",te,"暂无数据"))]))]),_:1}),v(s,{class:"card",title:" ",overlay:""},{title:x(()=>[v(u,{ref_key:"refSearch",ref:d,config:_},null,8,["config"])]),_:1}),v(s,{class:"card table-card",title:"维修记录",overlay:""},{default:x(()=>[v(f,{config:r},null,8,["config"])]),_:1})])}}}),ie=M(oe,[["__scopeId","data-v-e0d45887"]]);export{ie as default};
