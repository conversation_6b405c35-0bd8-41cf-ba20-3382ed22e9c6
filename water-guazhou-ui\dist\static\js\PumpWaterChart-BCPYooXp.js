import{d as u,c as h,am as f,o as y,g as _,n as v,p as c,c4 as x,C as b}from"./index-r0dFAfgr.js";const g={class:"chart-container"},w=u({__name:"PumpWaterChart",props:{chartData:{type:Array,default:()=>[]}},setup(i){const s=i,o=h(null);let t=null;const p=()=>{o.value&&(t=x(o.value),n())},n=()=>{if(!t)return;const e=s.chartData,r=e.map(a=>a.stationName),l=e.map(a=>a.pumpWater),d=e.map(a=>a.otherWater),m={tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},legend:{data:["中水量","其他量"],top:10},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",data:r},yAxis:{type:"value",name:"水量(m³)"},series:[{name:"中水量",type:"bar",data:l,itemStyle:{color:"#5470c6"},label:{show:!0,position:"top"}},{name:"其他量",type:"bar",data:d,itemStyle:{color:"#91cc75"},label:{show:!0,position:"top"}}]};t.setOption(m)};return f(()=>s.chartData,()=>{n()},{deep:!0}),y(()=>{p(),window.addEventListener("resize",()=>{t&&t.resize()})}),(e,r)=>(_(),v("div",g,[r[0]||(r[0]=c("div",{class:"chart-title"},"泵水量与其他量",-1)),c("div",{ref_key:"chartRef",ref:o,class:"chart"},null,512)]))}}),W=b(w,[["__scopeId","data-v-61513b55"]]);export{W as default};
