package org.thingsboard.server.dao.model.sql.maintainCircuit;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 班组主表
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-09-07
 */
@TableName("tb_maintain_circuit_team_c")
@Data
public class MaintainCircuitTeamC {
    
    @TableId
    private String id;

    private String mainId;

    private String userId;

    private transient String userName;

    private transient String email;

    private Date createTime;

    private String tenantId;
}
