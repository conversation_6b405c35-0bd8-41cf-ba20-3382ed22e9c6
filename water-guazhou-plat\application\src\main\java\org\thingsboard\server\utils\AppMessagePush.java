package org.thingsboard.server.utils;

import com.getui.push.v2.sdk.ApiHelper;
import com.getui.push.v2.sdk.GtApiConfiguration;
import com.getui.push.v2.sdk.api.PushApi;
import com.getui.push.v2.sdk.common.ApiResult;
import com.getui.push.v2.sdk.dto.req.Audience;
import com.getui.push.v2.sdk.dto.req.Settings;
import com.getui.push.v2.sdk.dto.req.message.PushChannel;
import com.getui.push.v2.sdk.dto.req.message.PushDTO;
import com.getui.push.v2.sdk.dto.req.message.PushMessage;
import com.getui.push.v2.sdk.dto.req.message.android.AndroidDTO;
import com.getui.push.v2.sdk.dto.req.message.android.GTNotification;
import com.getui.push.v2.sdk.dto.req.message.android.ThirdNotification;
import com.getui.push.v2.sdk.dto.req.message.android.Ups;
import com.getui.push.v2.sdk.dto.req.message.ios.Alert;
import com.getui.push.v2.sdk.dto.req.message.ios.Aps;
import com.getui.push.v2.sdk.dto.req.message.ios.IosDTO;
import com.gexin.rp.sdk.base.IPushResult;
import com.gexin.rp.sdk.base.ITemplate;
import com.gexin.rp.sdk.base.impl.SingleMessage;
import com.gexin.rp.sdk.base.impl.Target;
import com.gexin.rp.sdk.exceptions.RequestException;
import com.gexin.rp.sdk.http.IGtPush;
import com.gexin.rp.sdk.template.NotificationTemplate;
import com.gexin.rp.sdk.template.style.Style0;

import java.util.Map;

public class AppMessagePush {

    public static final String AppID = "hAWC9swNsHAusBR2qiEqY6";
    public static final String AppSecret = "SerO1olMRQ74RxBZW0eug7";
    public static final String AppKey = "L6jU60O2IZ8KYQp5KG2nN9";
    public static final String MasterSecret = "3OgdhFKceHAR4XvVTINh3A";
    public static final String HOST = "http://sdk.open.api.igexin.com/apiex.htm";

    /**
     *
     * @param cidOrAlias
     *            别名或者cid
     * @param msg
     *            透传消息内容
     * @param type
     *            1-cid推，2-别名推
     */
    public static void pushToSingle(String cidOrAlias, String msg, int type) {
        IGtPush push = new IGtPush(HOST, AppKey, MasterSecret);
        ITemplate template = buildNotificationTemplate(msg);
        SingleMessage message = new SingleMessage();
        // 是否离线推送
        message.setOffline(true);
        // 离线有效时间，单位为毫秒，可选
        message.setOfflineExpireTime(24 * 3600 * 1000);
        // 消息内容
        message.setData(template);
        // 可选，1为wifi，0为不限制网络环境。根据手机处于的网络情况，决定是否下发
        message.setPushNetWorkType(0);

        Target target = new Target();
        target.setAppId(AppID);
        if (type == 1) {
            target.setClientId(cidOrAlias);
        } else if (type == 2) {
            // 按别名推送
            target.setAlias(cidOrAlias);
        }
        IPushResult ret = null;
        try {
            ret = push.pushMessageToSingle(message, target);
        } catch (RequestException e) {
            e.printStackTrace();
            // 推送失败时，进行重推
            ret = push.pushMessageToSingle(message, target, e.getRequestId());
        }
        if (ret != null) {
            System.out.println(ret.getResponse().toString());
        } else {
            System.out.println("服务器响应异常");
        }
    }

    public static NotificationTemplate buildNotificationTemplate(String msg) {
        NotificationTemplate template = new NotificationTemplate();
        // 设置APPID与APPKEY
        template.setAppId(AppID);
        template.setAppkey(AppKey);

        Style0 style = new Style0();
        // 设置通知栏标题与内容
        style.setTitle("群推通知栏标题");
        style.setText(msg);
        // 配置通知栏图标
        style.setLogo("icon.png");
        // 配置通知栏网络图标
        style.setLogoUrl("");
        // 设置通知是否响铃，震动，或者可清除
        style.setRing(true);
        style.setVibrate(true);
        style.setClearable(true);
        template.setStyle(style);

        // 透传消息设置，1为强制启动应用，客户端接收到消息后就会立即启动应用；2为等待应用启动
        template.setTransmissionType(2);
        template.setTransmissionContent("请输入您要透传的内容");
        return template;
    }

    public static void main(String[] args) {
        //填写应用配置，参数在“Uni Push”下的“应用配置”页面中获取
        GtApiConfiguration apiConfiguration = new GtApiConfiguration();
        //填写应用配置，参数在“Uni Push”下的“应用配置”页面中获取
        apiConfiguration.setAppId("OMMZ09jdv6A9lWco4SoPH5");
        apiConfiguration.setAppKey("oW4Tb5sp8e70LbLCfRlRU5");
        apiConfiguration.setMasterSecret("8aLt9xTr38A7kyG54ENvc4");
        apiConfiguration.setDomain("https://restapi.getui.com/v2/");
        // 实例化ApiHelper对象，用于创建接口对象
        ApiHelper apiHelper = ApiHelper.build(apiConfiguration);
        // 创建对象，建议复用。目前有PushApi、StatisticApi、UserApi
        PushApi pushApi = apiHelper.creatApi(PushApi.class);
        //根据cid进行单推
        PushDTO<Audience> pushDTO = new PushDTO<Audience>();
        // 设置推送参数，requestid需要每次变化唯一
        pushDTO.setRequestId(System.currentTimeMillis() + "");
        Settings settings = new Settings();
        pushDTO.setSettings(settings);
        //消息有效期，走厂商消息必须设置该值
        settings.setTtl(3600000);

        //在线走个推通道时推送的消息体
        PushMessage pushMessage = new PushMessage();
        GTNotification gtNotification = new GTNotification();
        gtNotification.setBody("测试消息");
        gtNotification.setTitle("测试标题");
        gtNotification.setClickType("none");
        pushMessage.setNotification(gtNotification);
        pushDTO.setPushMessage(pushMessage);
        //此格式的透传消息由 unipush 做了特殊处理，会自动展示通知栏。开发者也可自定义其它格式，在客户端自己处理。
//        pushMessage.setTransmission(" {title:\"标题\",content:\"内容\",payload:\"自定义数据\"}");
        // 设置接收人信息
        Audience audience = new Audience();
        pushDTO.setAudience(audience);
        audience.addCid("e406deca1c66ac865b2b16a8d6081b11");
        /*//设置离线推送时的消息体
        PushChannel pushChannel = new PushChannel();
        //安卓离线厂商通道推送的消息体
        AndroidDTO androidDTO = new AndroidDTO();
        Ups ups = new Ups();
        ThirdNotification thirdNotification = new ThirdNotification();
        ups.setNotification(thirdNotification);
        thirdNotification.setTitle("安卓离线展示的标题");
        thirdNotification.setBody("安卓离线展示的内容");
        thirdNotification.setClickType("intent");
        //注意：intent参数必须按下方文档（特殊参数说明）要求的固定格式传值，intent错误会导致客户端无法收到消息
        thirdNotification.setIntent("请填写固定格式的intent");
        androidDTO.setUps(ups);
        pushChannel.setAndroid(androidDTO);*/

        //ios离线apn通道推送的消息体
        /*Alert alert = new Alert();
        alert.setTitle("苹果离线通知栏标题");
        alert.setBody("苹果离线通知栏内容");
        Aps aps = new Aps();
        aps.setContentAvailable(0);
        aps.setSound("default");
        aps.setAlert(alert);
        IosDTO iosDTO = new IosDTO();
        iosDTO.setAps(aps);
        iosDTO.setType("notify");
        pushChannel.setIos(iosDTO);
*/
//        pushDTO.setPushChannel(pushChannel);

        // 进行cid单推
        ApiResult<Map<String, Map<String, String>>> apiResult = pushApi.pushToSingleByCid(pushDTO);
        if (apiResult.isSuccess()) {
            // success
            System.out.println(apiResult.getData());
        } else {
            // failed
            System.out.println("code:" + apiResult.getCode() + ", msg: " + apiResult.getMsg());
        }
    }

    /**
     * 单cid推送
     *
     * @param cid
     * @param title
     * @param content
     * @return
     */
    public static boolean pushToSingleByCid(PushApi pushApi, String cid, String title, String content) {
        //推送消息体
        PushDTO<Audience> pushDTO = buildPushDTO(title, content);
        //设置接收人信息
        Audience audience = new Audience();
        pushDTO.setAudience(audience);
        audience.addCid(cid);// cid
        //进行cid单推
        ApiResult<Map<String, Map<String, String>>> apiResult = pushApi.pushToSingleByCid(pushDTO);
        if (apiResult.isSuccess()) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 构建推送消息体
     *
     * @param title
     * @param content
     * @return
     */
    private static PushDTO<Audience> buildPushDTO(String title, String content) {
        PushDTO<Audience> pushDTO = new PushDTO<>();
        // 设置推送参数
        pushDTO.setRequestId(System.currentTimeMillis() + "");

        //配置推送条件
        Settings settings = new Settings();
        pushDTO.setSettings(settings);
        //消息有效期，走厂商消息需要设置该值
        settings.setTtl(3600000);

        //安卓在线通道走个推推送时的消息体（在线通道不支持ios）
        PushMessage pushMessage = new PushMessage();
        pushDTO.setPushMessage(pushMessage);
        //通知消息
        GTNotification notification = new GTNotification();
        pushMessage.setNotification(notification);
        notification.setTitle(title + "在线通道通知消息标题");
        notification.setBody(content + "在线通道通知消息内容");
        notification.setClickType("intent");
        notification.setIntent("intent://com.getui.push/detail?#Intent;scheme=gtpushscheme;launchFlags=0x4000000;package=com.getui.demo;component=com.getui.demo/com.getui.demo.DemoActivity;S.payload=payloadStr;end");
        return pushDTO;
    }


}
