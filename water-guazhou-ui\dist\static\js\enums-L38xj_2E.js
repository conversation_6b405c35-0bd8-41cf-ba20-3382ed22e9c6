var I,L,A,_,S,O,N;(function(T){T[T.FILL=0]="FILL",T[T.LINE=1]="LINE",T[T.MARKER=2]="MARKER",T[T.TEXT=3]="TEXT",T[T.LABEL=4]="LABEL"})(I||(I={})),function(T){T[T.NONE=0]="NONE",T[T.MAP=1]="MAP",T[T.LABEL=2]="LABEL",T[T.LABEL_ALPHA=4]="LABEL_ALPHA",T[T.HITTEST=8]="HITTEST",T[T.HIGHLIGHT=16]="HIGHLIGHT",T[T.CLIP=32]="CLIP",T[T.DEBUG=64]="DEBUG",T[T.NUM_DRAW_PHASES=9]="NUM_DRAW_PHASES"}(L||(L={})),function(T){T[T.SIZE=0]="SIZE",T[T.COLOR=1]="COLOR",T[T.OPACITY=2]="OPACITY",T[T.ROTATION=3]="ROTATION"}(A||(A={})),function(T){T[T.NONE=0]="NONE",T[T.OPACITY=1]="OPACITY",T[T.COLOR=2]="COLOR",T[T.ROTATION=4]="ROTATION",T[T.SIZE_MINMAX_VALUE=8]="SIZE_MINMAX_VALUE",T[T.SIZE_SCALE_STOPS=16]="SIZE_SCALE_STOPS",T[T.SIZE_FIELD_STOPS=32]="SIZE_FIELD_STOPS",T[T.SIZE_UNIT_VALUE=64]="SIZE_UNIT_VALUE"}(_||(_={})),function(T){T[T.MINMAX_TARGETS_OUTLINE=128]="MINMAX_TARGETS_OUTLINE",T[T.SCALE_TARGETS_OUTLINE=256]="SCALE_TARGETS_OUTLINE",T[T.FIELD_TARGETS_OUTLINE=512]="FIELD_TARGETS_OUTLINE",T[T.UNIT_TARGETS_OUTLINE=1024]="UNIT_TARGETS_OUTLINE"}(S||(S={})),function(T){T[T.SPRITE=0]="SPRITE",T[T.GLYPH=1]="GLYPH"}(O||(O={})),function(T){T[T.DEFAULT=0]="DEFAULT",T[T.SIMPLE=1]="SIMPLE",T[T.DOT_DENSITY=2]="DOT_DENSITY",T[T.OUTLINE_FILL=3]="OUTLINE_FILL",T[T.OUTLINE_FILL_SIMPLE=4]="OUTLINE_FILL_SIMPLE",T[T.HEATMAP=5]="HEATMAP",T[T.PIE_CHART=6]="PIE_CHART"}(N||(N={}));export{S as A,I as E,A as I,_ as L,N as S,L as T,O as _};
