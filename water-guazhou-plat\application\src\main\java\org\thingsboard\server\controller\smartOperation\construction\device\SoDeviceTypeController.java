package org.thingsboard.server.controller.smartOperation.construction.device;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardErrorCode;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.sql.smartOperation.device.SoDeviceType;
import org.thingsboard.server.dao.util.imodel.query.GeneralDeviceUtil;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.device.SoDeviceTypePageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.device.SoDeviceTypeSaveRequest;
import org.thingsboard.server.dao.construction.device.SoDeviceTypeService;
import org.thingsboard.server.utils.imodel.annotations.IStarController2;

import java.util.List;

@IStarController2
@RequestMapping("/api/so/deviceType")
public class SoDeviceTypeController extends BaseController {
    @Autowired
    private SoDeviceTypeService service;

    @GetMapping
    public IPage<SoDeviceType> findAllConditional(SoDeviceTypePageRequest request) {
        return service.findAllConditional(request);
    }

    @GetMapping("/tree")
    public List<SoDeviceType> findTree() throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return service.findAllStructure(tenantId);
    }

    @PostMapping
    public SoDeviceType save(@RequestBody SoDeviceTypeSaveRequest req) throws ThingsboardException {
        req.setParentId(
                service.getIdBySerialId(
                        GeneralDeviceUtil.extractParentSerialId(req.getSerialId()),
                        UUIDConverter.fromTimeUUID(getTenantId().getId())
                )
        );
        GeneralDeviceUtil.checkDeviceSerialId(req, service);
        return service.save(req);
    }

    // @PatchMapping("/{id}")
    public boolean edit(@RequestBody SoDeviceTypeSaveRequest req, @PathVariable String id) throws ThingsboardException {
        if (req.getParentId() != null || req.getSerialId() != null) {
            if (req.getParentId() == null)
                req.setParentId(service.getParentId(id));

            GeneralDeviceUtil.checkDeviceSerialId(req, service);
        }

        return service.update(req.unwrap(id));
    }

    @DeleteMapping("/{id}")
    public boolean delete(@PathVariable String id) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        if (!service.canBeDelete(id, tenantId))
            throw new ThingsboardException("其下有设备类型或设备信息，无法执行删除操作", ThingsboardErrorCode.GENERAL);
        return service.delete(id);
    }
}