"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[8915],{13867:(e,t,i)=>{i.d(t,{Z:()=>r});var s=i(69801);class r{constructor(e,t){this._storage=new s.WJ,this._storage.maxSize=e,t&&this._storage.registerRemoveFunc("",t)}put(e,t,i){this._storage.put(e,t,i,1)}pop(e){return this._storage.pop(e)}get(e){return this._storage.get(e)}clear(){this._storage.clearAll()}destroy(){this._storage.destroy()}get maxSize(){return this._storage.maxSize}set maxSize(e){this._storage.maxSize=e}}},16306:(e,t,i)=>{i.d(t,{aX:()=>S});var s=i(68773),r=i(20102),n=i(92604),a=i(70586),o=i(38913),l=i(58901),c=i(73913),u=i(8744),h=i(40488),f=(i(66577),i(3172)),d=i(33955),p=i(11282),m=i(17452);async function y(e,t,i){const s="string"==typeof e?(0,m.mN)(e):e,r=t[0].spatialReference,n=(0,d.Ji)(t[0]),a={...i,query:{...s.query,f:"json",sr:r.wkid?r.wkid:JSON.stringify(r),geometries:JSON.stringify((l=t,{geometryType:(0,d.Ji)(l[0]),geometries:l.map((e=>e.toJSON()))}))}},{data:o}=await(0,f.default)(s.path+"/simplify",a);var l;return function(e,t,i){const s=(0,d.q9)(t);return e.map((e=>{const t=s.fromJSON(e);return t.spatialReference=i,t}))}(o.geometries,n,r)}const g=n.Z.getLogger("esri.geometry.support.normalizeUtils");function x(e){return"polygon"===e[0].type}function I(e){return"polyline"===e[0].type}function b(e,t,i){if(t){const t=function(e,t){if(!(e instanceof l.Z||e instanceof o.Z)){const e="straightLineDensify: the input geometry is neither polyline nor polygon";throw g.error(e),new r.Z(e)}const i=(0,c.x3)(e),s=[];for(const e of i){const i=[];s.push(i),i.push([e[0][0],e[0][1]]);for(let s=0;s<e.length-1;s++){const r=e[s][0],n=e[s][1],a=e[s+1][0],o=e[s+1][1],l=Math.sqrt((a-r)*(a-r)+(o-n)*(o-n)),c=(o-n)/l,u=(a-r)/l,h=l/t;if(h>1){for(let e=1;e<=h-1;e++){const s=e*t,a=u*s+r,o=c*s+n;i.push([a,o])}const e=(l+Math.floor(h-1)*t)/2,s=u*e+r,a=c*e+n;i.push([s,a])}i.push([a,o])}}return function(e){return"polygon"===e.type}(e)?new o.Z({rings:s,spatialReference:e.spatialReference}):new l.Z({paths:s,spatialReference:e.spatialReference})}(e,1e6);e=(0,h.Sx)(t,!0)}return i&&(e=(0,c.Sy)(e,i)),e}function w(e,t,i){if(Array.isArray(e)){const s=e[0];if(s>t){const i=(0,c.XZ)(s,t);e[0]=s+i*(-2*t)}else if(s<i){const t=(0,c.XZ)(s,i);e[0]=s+t*(-2*i)}}else{const s=e.x;if(s>t){const i=(0,c.XZ)(s,t);e=e.clone().offset(i*(-2*t),0)}else if(s<i){const t=(0,c.XZ)(s,i);e=e.clone().offset(t*(-2*i),0)}}return e}function _(e,t){let i=-1;for(let s=0;s<t.cutIndexes.length;s++){const r=t.cutIndexes[s],n=t.geometries[s],a=(0,c.x3)(n);for(let e=0;e<a.length;e++){const t=a[e];t.some((i=>{if(i[0]<180)return!0;{let i=0;for(let e=0;e<t.length;e++){const s=t[e][0];i=s>i?s:i}i=Number(i.toFixed(9));const s=-360*(0,c.XZ)(i,180);for(let i=0;i<t.length;i++){const t=n.getPoint(e,i);n.setPoint(e,i,t.clone().offset(s,0))}return!0}}))}if(r===i){if(x(e))for(const t of(0,c.x3)(n))e[r]=e[r].addRing(t);else if(I(e))for(const t of(0,c.x3)(n))e[r]=e[r].addPath(t)}else i=r,e[r]=n}return e}async function S(e,t,i){if(!Array.isArray(e))return S([e],t);t&&"string"!=typeof t&&g.warn("normalizeCentralMeridian()","The url object is deprecated, use the url string instead");const r="string"==typeof t?t:t?.url??s.Z.geometryServiceUrl;let n,m,x,I,R,v,C,T,k=0;const F=[],P=[];for(const t of e)if((0,a.Wi)(t))P.push(t);else if(n||(n=t.spatialReference,m=(0,u.C5)(n),x=n.isWebMercator,v=x?102100:4326,I=c.UZ[v].maxX,R=c.UZ[v].minX,C=c.UZ[v].plus180Line,T=c.UZ[v].minus180Line),m)if("mesh"===t.type)P.push(t);else if("point"===t.type)P.push(w(t.clone(),I,R));else if("multipoint"===t.type){const e=t.clone();e.points=e.points.map((e=>w(e,I,R))),P.push(e)}else if("extent"===t.type){const e=t.clone()._normalize(!1,!1,m);P.push(e.rings?new o.Z(e):e)}else if(t.extent){const e=t.extent,i=(0,c.XZ)(e.xmin,R)*(2*I);let s=0===i?t.clone():(0,c.Sy)(t.clone(),i);e.offset(i,0),e.intersects(C)&&e.xmax!==I?(k=e.xmax>k?e.xmax:k,s=b(s,x),F.push(s),P.push("cut")):e.intersects(T)&&e.xmin!==R?(k=e.xmax*(2*I)>k?e.xmax*(2*I):k,s=b(s,x,360),F.push(s),P.push("cut")):P.push(s)}else P.push(t.clone());else P.push(t);let M=(0,c.XZ)(k,I),O=-90;const D=M,B=new l.Z;for(;M>0;){const e=360*M-180;B.addPath([[e,O],[e,-1*O]]),O*=-1,M--}if(F.length>0&&D>0){const t=_(F,await async function(e,t,i,s){const r=(0,p.en)(e),n=t[0].spatialReference,a={...s,query:{...r.query,f:"json",sr:JSON.stringify(n),target:JSON.stringify({geometryType:(0,d.Ji)(t[0]),geometries:t}),cutter:JSON.stringify(i)}},o=await(0,f.default)(r.path+"/cut",a),{cutIndexes:l,geometries:c=[]}=o.data;return{cutIndexes:l,geometries:c.map((e=>{const t=(0,d.im)(e);return t.spatialReference=n,t}))}}(r,F,B,i)),s=[],n=[];for(let i=0;i<P.length;i++){const r=P[i];if("cut"!==r)n.push(r);else{const r=t.shift(),o=e[i];(0,a.pC)(o)&&"polygon"===o.type&&o.rings&&o.rings.length>1&&r.rings.length>=o.rings.length?(s.push(r),n.push("simplify")):n.push(x?(0,h.$)(r):r)}}if(!s.length)return n;const o=await y(r,s,i),l=[];for(let e=0;e<n.length;e++){const t=n[e];"simplify"!==t?l.push(t):l.push(x?(0,h.$)(o.shift()):o.shift())}return l}const Z=[];for(let e=0;e<P.length;e++){const t=P[e];if("cut"!==t)Z.push(t);else{const e=F.shift();Z.push(!0===x?(0,h.$)(e):e)}}return Z}},73913:(e,t,i)=>{i.d(t,{Sy:()=>l,UZ:()=>a,XZ:()=>o,x3:()=>c});var s=i(58901),r=i(82971),n=i(33955);const a={102100:{maxX:20037508.342788905,minX:-20037508.342788905,plus180Line:new s.Z({paths:[[[20037508.342788905,-20037508.342788905],[20037508.342788905,20037508.342788905]]],spatialReference:r.Z.WebMercator}),minus180Line:new s.Z({paths:[[[-20037508.342788905,-20037508.342788905],[-20037508.342788905,20037508.342788905]]],spatialReference:r.Z.WebMercator})},4326:{maxX:180,minX:-180,plus180Line:new s.Z({paths:[[[180,-180],[180,180]]],spatialReference:r.Z.WGS84}),minus180Line:new s.Z({paths:[[[-180,-180],[-180,180]]],spatialReference:r.Z.WGS84})}};function o(e,t){return Math.ceil((e-t)/(2*t))}function l(e,t){const i=c(e);for(const e of i)for(const i of e)i[0]+=t;return e}function c(e){return(0,n.oU)(e)?e.rings:e.paths}},92045:(e,t,i)=>{i.r(t),i.d(t,{default:()=>lt});var s=i(43697),r=i(51773),n=i(30030),a=i(20102),o=i(92604),l=i(70586),c=i(16453),u=i(95330),h=i(17445),f=i(5600),d=i(75215),p=(i(67676),i(36030)),m=i(71715),y=i(52011),g=i(87085),x=i(17287),I=i(71612),b=i(17017),w=(i(66577),i(3172)),_=i(8744),S=i(66677),R=i(21506),v=i(35956),C=i(29876),T=i(40297),k=i(88281),F=i(11145),P=i(96674),M=i(609),O=i(39450),D=i(36679),B=i(99815),Z=i(73506),z=i(94139);const J=new Map,N=new class{constructor(e=15e3,t=5e3){this._timer=null,this._cachedBlocks=new Map,this._size=-1,this._duration=e,this._interval=Math.min(e,t)}decreaseRefCount(e,t){const i=e+"/"+t,s=this._cachedBlocks;if(s.has(i)){const e=s.get(i);return e.refCount--,e.refCount<=0&&(s.delete(i),e.controller&&e.controller.abort()),e.refCount}return 0}getBlock(e,t){const i=e+"/"+t,s=this._cachedBlocks;if(s.has(i)){const e=s.get(i);return e.ts=Date.now(),e.refCount++,s.delete(i),s.set(i,e),e.block}return null}putBlock(e,t,i,s){const r=this._cachedBlocks,n=e+"/"+t;if(r.has(n)){const e=r.get(n);e.ts=Date.now(),e.refCount++}else r.set(n,{block:i,ts:Date.now(),refCount:1,controller:s});this._trim(),this._updateTimer()}deleteBlock(e,t){const i=this._cachedBlocks,s=e+"/"+t;i.has(s)&&i.delete(s)}updateMaxSize(e){this._size=e,this._trim()}empty(){this._cachedBlocks.clear(),this._clearTimer()}getCurrentSize(){return this._cachedBlocks.size}_updateTimer(){if(null!=this._timer)return;const e=this._cachedBlocks;this._timer=setInterval((()=>{const t=Array.from(e),i=Date.now();for(let s=0;s<t.length&&t[s][1].ts<=i-this._duration;s++)e.delete(t[s][0]);0===e.size&&this._clearTimer()}),this._interval)}_trim(){const e=this._cachedBlocks;if(-1===this._size||this._size>=e.size)return;const t=Array.from(e);for(let i=0;i<t.length-this._size;i++)e.delete(t[i][0])}_clearTimer(){null!=this._timer&&(clearInterval(this._timer),this._timer=null)}};function W(e,t){return null==t?e:`${e}?sliceId=${t}`}function E(e,t,i){const s=J.get(e);if(!s)return null==t?N.decreaseRefCount(e,i):0;if(null==t||null==s[t])return N.decreaseRefCount(e,i);const r=s[t]?.cache,n=r?.get(i);if(r&&n){if(n.refCount--,0===n.refCount){r.delete(i);for(let e=0;e<s.length;e++)s[e]?.cache.delete(i);n.controller&&n.controller.abort()}return n.refCount}return 0}function H(e,t,i){const s=J.get(e);if(!s)return null==t?N.getBlock(e,i):null;if(null==t||null==s[t]){for(let e=0;e<s.length;e++){const t=s[e]?.cache.get(i);if(t)return t.refCount++,t.block}return N.getBlock(e,i)}const r=s[t]?.cache.get(i);if(r)return r.refCount++,r.block;for(let e=0;e<s.length;e++){if(e===t||!s[e])continue;const r=s[e]?.cache,n=r?.get(i);if(r&&n)return n.refCount++,r.set(i,n),n.block}return null}function A(e,t,i,s,r=null){const n=J.get(e);if(!n)return void(null==t&&N.putBlock(e,i,s,r));if(null==t||null==n[t])return void N.putBlock(e,i,s,r);const a={refCount:1,block:s,isResolved:!1,isRejected:!1,controller:r};s.then((()=>a.isResolved=!0)).catch((()=>a.isRejected=!0)),n[t]?.cache.set(i,a)}function L(e,t,i){const s=J.get(e);s?null!=t&&null!=s[t]?s[t]?.cache.delete(i):N.deleteBlock(e,i):null==t&&N.deleteBlock(e,i)}var q=i(81578),j=i(75993),G=i(55914),U=i(80676),$=i(6570);let V=class extends((0,M.v)(P.wq)){constructor(){super(...arguments),this.rasterJobHandler=null,this.datasetName=null,this.datasetFormat=null,this.hasUniqueSourceStorageInfo=!0,this.rasterInfo=null,this.ioConfig={sampling:"closest"}}async init(){const e=(0,Z.zD)();this.addResolvingPromise(e),await this.when()}normalizeCtorArgs(e){return e&&e.ioConfig&&(e={...e,ioConfig:{resolution:null,bandIds:null,sampling:"closest",tileInfo:F.Z.create(),...e.ioConfig}}),e}get _isGlobalWrappableSource(){const{rasterInfo:e}=this,t=(0,Z.ut)(e.spatialReference);return(0,l.pC)(t)&&e.extent.width>=t/2}set url(e){this._set("url",(0,S.Nm)(e,o.Z.getLogger(this.declaredClass)))}async open(e){throw new a.Z("BaseRaster:open-not-implemented","open() is not implemented")}async fetchTile(e,t,i,s={}){const r=s.tileInfo||this.rasterInfo.storageInfo.tileInfo,n=this.getTileExtentFromTileInfo(e,t,i,r);return this.fetchPixels(n,r.size[0],r.size[1],s)}async identify(e,t={}){e=(0,d.TJ)(z.Z,e).clone().normalize();const{multidimensionalDefinition:i,timeExtent:s}=t,{rasterInfo:r}=this,{hasMultidimensionalTranspose:n,multidimensionalInfo:a}=r;let{transposedVariableName:o}=t;const c=(0,l.pC)(a)&&n&&(null!=s||(0,B.WU)(i));c&&!o&&(o=(0,l.pC)(i)&&i.length>0?i[0].variableName??void 0:a.variables[0].name,t={...t,transposedVariableName:o}),t=this._getRequestOptionsWithSliceId(t);const{spatialReference:u,extent:h}=r,{datumTransformation:f}=t;let p=(0,Z.nF)(e,u,f);if(!h.intersects(p))return{location:p,value:null};if((0,l.pC)(r.transform)){const e=r.transform.inverseTransform(p);if(!r.nativeExtent.intersects(e))return{location:e,value:null};p=e}let m=0;const y=(0,l.pC)(o)&&(0,l.pC)(a)&&r.hasMultidimensionalTranspose;if("Function"===this.datasetFormat){const e=this.primaryRasters.rasters[0];if(y)return e.identify(p,t);const{pixelSize:i}=r,s=3,n=i.x*s/2,a=i.y*s/2,o=new $.Z({xmin:p.x-n,xmax:p.x+n,ymin:p.y-a,ymax:p.y+a,spatialReference:u}),c={interpolation:"nearest"},{pixelBlock:h}=await e.fetchPixels(o,s,s,c),{pixelBlock:f}=await this.fetchPixels(o,s,s,c);if((0,l.Wi)(h))return{location:p,value:null};const d=Math.floor(s*s*.5),m=!h.mask||h.mask[d]?h.pixels.map((e=>e[d])):null;let g;return(0,l.pC)(f)&&(g=!f.mask||f.mask[d]?f.pixels.map((e=>e[d])):void 0),{location:p,value:m,processedValue:g,pyramidLevel:0}}if(!y)if(t.srcResolution)m=(0,Z.kr)(t.srcResolution,r,this.ioConfig.sampling).pyramidLevel;else if(m=await this.computeBestPyramidLevelForLocation(e,t),null==m)return{location:p,value:null};const g=this.identifyPixelLocation(p,m,null,y);if(null===g)return{location:p,value:null};const{row:x,col:I,rowOffset:b,colOffset:w,blockWidth:_}=g,S=o??(0,l.Wg)(t.sliceId),R=W(this.url,S),v=`${m}/${x}/${I}`;let C=H(R,null,v);(0,l.Wi)(C)&&(C=this.fetchRawTile(m,x,I,t),A(R,null,v,C));const T=await C;if((0,l.Wi)(T)||!T.pixels?.length)return{location:p,value:null};const k=b*_+w;return this._processIdentifyResult(T,{srcLocation:p,position:k,pyramidLevel:m,useTransposedTile:!!y,requestSomeSlices:c,identifyOptions:t})}async fetchPixels(e,t,i,s={}){if(e=(0,Z.kZ)(e),(s=this._getRequestOptionsWithSliceId(s)).requestRawData)return this._fetchPixels(e,t,i,s);const r=(0,Z.ut)(e.spatialReference),n=(0,Z.Hq)(e);if((0,l.Wi)(r)||0===n||1===n&&this._isGlobalWrappableSource)return this._fetchPixels(e,t,i,s);if(n>=3)return{extent:e,pixelBlock:null};const a=[],{xmin:o,xmax:c}=e,u=Math.round(r/(c-o)*t),h=u-Math.round((r/2-o)/(c-o)*t);let f=0;const d=[];for(let l=0;l<=n;l++){const p=new $.Z({xmin:0===l?o:-r/2,xmax:l===n?c-r*l:r/2,ymin:e.ymin,ymax:e.ymax,spatialReference:e.spatialReference}),m=0===l?u-h:l===n?t-f:u;f+=m,d.push(m);const y=s.disableWrapAround&&l>0?null:this._fetchPixels(p,m,i,s);a.push(y)}const p=(await Promise.all(a)).map((e=>e?.pixelBlock));let m=null;const y={width:t,height:i};return m=this.rasterJobHandler?(await this.rasterJobHandler.mosaicAndTransform({srcPixelBlocks:p,srcMosaicSize:y,destDimension:null,coefs:null,sampleSpacing:null,interpolation:"nearest",alignmentInfo:null,blockWidths:d},s)).pixelBlock:(0,G.us)(p,y,{blockWidths:d}),{extent:e,srcExtent:(0,Z.tB)(e,this.rasterInfo.spatialReference,s.datumTransformation),pixelBlock:m}}async fetchRawPixels(e,t,i,s={}){t={x:Math.floor(t.x),y:Math.floor(t.y)};const r=await this._fetchRawTiles(e,t,i,s),{nativeExtent:n,nativePixelSize:a,storageInfo:o}=this.rasterInfo,c=2**e,u=a.x*c,h=a.y*c,f=new $.Z({xmin:n.xmin+u*t.x,xmax:n.xmin+u*(t.x+i.width-1),ymin:n.ymax-h*(t.y+i.height-1),ymax:n.ymax-h*t.y,spatialReference:n.spatialReference});if(!r)return{extent:f,srcExtent:f,pixelBlock:null};const{pixelBlocks:d,mosaicSize:p}=r;if(1===d.length&&(0,l.pC)(d[0])&&d[0].width===i.width&&d[0].height===i.height)return{extent:f,srcExtent:f,pixelBlock:r.pixelBlocks[0]};const m=e>0?o.pyramidBlockWidth:o.blockWidth,y=e>0?o.pyramidBlockHeight:o.blockHeight,g={x:t.x%m,y:t.y%y};let x;return x=this.rasterJobHandler?(await this.rasterJobHandler.mosaicAndTransform({srcPixelBlocks:d,srcMosaicSize:p,destDimension:i,clipOffset:g,clipSize:i,coefs:null,sampleSpacing:null,interpolation:s.interpolation,alignmentInfo:null,blockWidths:null},s)).pixelBlock:(0,G.us)(d,p,{clipOffset:g,clipSize:i}),{extent:f,srcExtent:f,pixelBlock:x}}fetchRawTile(e,t,i,s){throw new a.Z("BaseRaster:read-not-implemented","fetchRawTile() is not implemented")}computeExtent(e){return(0,Z.tB)(this.rasterInfo.extent,e)}decodePixelBlock(e,t){return!this.rasterJobHandler||t.useCanvas?(0,j.J)(e,t):this.rasterJobHandler.decode({data:e,options:t})}async request(e,t,i=0){const{customFetchParameters:s}=this.ioConfig,{range:r,query:n,headers:a}=t;i=i??t.retryCount??this.ioConfig.retryCount;const o=r?{Range:`bytes=${r.from}-${r.to}`}:null;try{return await(0,w.default)(e,{...t,query:{...n,...s},headers:{...a,...o}})}catch(s){if(i>0)return i--,this.request(e,t,i);throw s}}getSliceIndex(e){const{multidimensionalInfo:t}=this.rasterInfo;return(0,l.Wi)(t)||(0,l.Wi)(e)||0===e.length?null:(0,B.gk)(e,t)}getTileExtentFromTileInfo(e,t,i,s){const r=(0,l.s3)(s.lodAt(e));return this.getTileExtent({x:r.resolution,y:r.resolution},t,i,s.origin,s.spatialReference,s.size)}updateTileInfo(){const{storageInfo:e,spatialReference:t,extent:i,pixelSize:s}=this.rasterInfo;if(!e.tileInfo){const r=[],n=e.maximumPyramidLevel||0;let a=Math.max(s.x,s.y),o=1/.0254*96*a;for(let e=0;e<=n;e++)r.push(new O.Z({level:n-e,resolution:a,scale:o})),a*=2,o*=2;const l=new z.Z({x:i.xmin,y:i.ymax,spatialReference:t});e.tileInfo=new F.Z({origin:l,size:[e.blockWidth,e.blockHeight],spatialReference:t,lods:r}),e.isVirtualTileInfo=!0}}createRemoteDatasetStorageInfo(e,t=512,i=512,s){const{width:r,height:n,nativeExtent:a,pixelSize:o,spatialReference:l}=e,c=new z.Z({x:a.xmin,y:a.ymax,spatialReference:l});null==s&&(s=Math.max(0,Math.round(Math.log(Math.max(r,n))/Math.LN2-8)));const u=this.computeBlockBoundary(a,512,512,{x:a.xmin,y:a.ymax},[o],s);e.storageInfo=new D.Z({blockWidth:t,blockHeight:i,pyramidBlockWidth:t,pyramidBlockHeight:i,origin:c,firstPyramidLevel:1,maximumPyramidLevel:s,blockBoundary:u})}async computeBestPyramidLevelForLocation(e,t={}){return 0}computeBlockBoundary(e,t,i,s,r,n=0,a=2){if(1===r.length&&n>0){r=[...r];let{x:e,y:t}=r[0];for(let i=0;i<n;i++)e*=a,t*=a,r.push({x:e,y:t})}const o=[],{x:l,y:c}=s;for(let s=0;s<r.length;s++){const{x:n,y:a}=r[s];o.push({minCol:Math.floor((e.xmin-l+.1*n)/t/n),maxCol:Math.floor((e.xmax-l-.1*n)/t/n),minRow:Math.floor((c-e.ymax+.1*a)/i/a),maxRow:Math.floor((c-e.ymin-.1*a)/i/a)})}return o}getPyramidPixelSize(e){const{nativePixelSize:t}=this.rasterInfo,{pyramidResolutions:i,pyramidScalingFactor:s}=this.rasterInfo.storageInfo;if(0===e)return t;if((0,l.pC)(i)&&i.length)return i[e-1];const r=s**e;return{x:t.x*r,y:t.y*r}}identifyPixelLocation(e,t,i,s){const{spatialReference:r,nativeExtent:n,storageInfo:a}=this.rasterInfo,{maximumPyramidLevel:o,origin:c,transposeInfo:u}=a,h=s&&(0,l.pC)(u)?u.tileSize[0]:a.blockWidth,f=s&&(0,l.pC)(u)?u.tileSize[1]:a.blockHeight,d=(0,Z.nF)(e,r,i);if(!n.intersects(d))return null;if(t<0||t>o)return null;const p=this.getPyramidPixelSize(t),{x:m,y}=p,g=(c.y-d.y)/y/f,x=(d.x-c.x)/m/h,I=Math.min(f-1,Math.floor((g-Math.floor(g))*f)),b=Math.min(h-1,Math.floor((x-Math.floor(x))*h));return{pyramidLevel:t,row:Math.floor(g),col:Math.floor(x),rowOffset:I,colOffset:b,blockWidth:h,srcLocation:d}}getTileExtent(e,t,i,s,r,n){const[a,o]=n,l=s.x+i*a*e.x,c=l+a*e.x,u=s.y-t*o*e.y,h=u-o*e.y;return new $.Z({xmin:l,xmax:c,ymin:h,ymax:u,spatialReference:r})}getBlockWidthHeight(e){return{blockWidth:e>0?this.rasterInfo.storageInfo.pyramidBlockWidth:this.rasterInfo.storageInfo.blockWidth,blockHeight:e>0?this.rasterInfo.storageInfo.pyramidBlockHeight:this.rasterInfo.storageInfo.blockHeight}}isBlockOutside(e,t,i){const s=this.rasterInfo.storageInfo.blockBoundary[e];return!s||s.maxRow<t||s.maxCol<i||s.minRow>t||s.minCol>i}async _fetchPixels(e,t,i,s={}){let r=(0,Z.Hq)(e);if(r>=2)return{extent:e,pixelBlock:null};const n=this._getSourceDataInfo(e,t,i,s),{pyramidLevel:a,pyramidResolution:o,srcResolution:c,srcExtent:u,srcWidth:h,srcHeight:f}=n;if(0===h||0===f)return{extent:e,srcExtent:u,pixelBlock:null};const d=(0,l.Wg)(this.rasterInfo.transform),p="gcs-shift"===d?.type,m=(0,l.pC)((0,Z.ut)(e.spatialReference));!p&&m||(r=(0,Z.Hq)(n.srcExtent,p));const y=this.rasterInfo.storageInfo,g={x:Math.floor((u.xmin-y.origin.x)/o.x+.1),y:Math.floor((y.origin.y-u.ymax)/o.y+.1)},x=await this._fetchRawTiles(a,g,{width:h,height:f,wrapCount:r},s);if(!x)return{extent:e,srcExtent:u,pixelBlock:null};const I=a>0?y.pyramidBlockWidth:y.blockWidth,b=a>0?y.pyramidBlockHeight:y.blockHeight,w=I===h&&b===f&&g.x%I==0&&g.y%b==0,_=new z.Z({x:(e.xmax-e.xmin)/t,y:(e.ymax-e.ymin)/i,spatialReference:e.spatialReference}),S=!e.spatialReference.equals(this.rasterInfo.spatialReference),{datumTransformation:R}=s;if(!S&&w&&1===x.pixelBlocks.length&&I===t&&b===i&&c.x===_.x&&c.y===_.y)return{extent:e,srcExtent:u,pixelBlock:x.pixelBlocks[0]};const v=m&&(0,l.pC)((0,Z.ut)(u.spatialReference)),C=s.requestProjectedLocalDirections&&this.rasterInfo.dataType.startsWith("vector");C&&!this.rasterJobHandler&&await(0,Z.zD)();const T=this.rasterJobHandler?await this.rasterJobHandler.getProjectionOffsetGrid({projectedExtent:e,srcBufferExtent:x.extent,pixelSize:_.toJSON(),datumTransformation:R,rasterTransform:d,hasWrapAround:r>0||v,isAdaptive:!1!==this.ioConfig.optimizeProjectionAccuracy,includeGCSGrid:C},s):(0,Z.Qp)({projectedExtent:e,srcBufferExtent:x.extent,pixelSize:_,datumTransformation:R,rasterTransform:d,hasWrapAround:r>0||v,isAdaptive:!1,includeGCSGrid:C});let k;const F=!s.requestRawData,P={rows:T.spacing[0],cols:T.spacing[1]},M=(0,l.Wg)(this._getRasterTileAlignmentInfo(a,x.extent.xmin)),{pixelBlocks:O,mosaicSize:D,isPartiallyFilled:B}=x;let J=null;if(this.rasterJobHandler){const e=await this.rasterJobHandler.mosaicAndTransform({srcPixelBlocks:O,srcMosaicSize:D,destDimension:F?{width:t,height:i}:null,coefs:F?T.coefficients:null,sampleSpacing:F?P:null,projectDirections:C,gcsGrid:C?T.gcsGrid:null,isUV:"vector-uv"===this.rasterInfo.dataType,interpolation:s.interpolation,alignmentInfo:M,blockWidths:null},s);({pixelBlock:k,localNorthDirections:J}=e)}else{const e=(0,G.us)(O,D,{alignmentInfo:M});k=F?(0,G.Uk)(e,{width:t,height:i},T.coefficients,P,s.interpolation):e,C&&T.gcsGrid&&(J=(0,G.Qh)({width:t,height:i},T.gcsGrid),k=(0,U.xQ)(k,this.rasterInfo.dataType,J))}return s.requestRawData||C?{srcExtent:u,pixelBlock:k,transformGrid:T,localNorthDirections:J,extent:e,isPartiallyFilled:B}:{srcExtent:u,extent:e,pixelBlock:k}}async _fetchRawTiles(e,t,i,s){const{origin:r,blockBoundary:n}=this.rasterInfo.storageInfo,{blockWidth:a,blockHeight:o}=this.getBlockWidthHeight(e);let{x:c,y:u}=t,{width:h,height:f,wrapCount:d}=i;const p=this._getRasterTileAlignmentInfo(e,0);s.buffer&&(c-=s.buffer.cols,u-=s.buffer.rows,h+=2*s.buffer.cols,f+=2*s.buffer.rows);let m=0,y=0,g=0;d&&(0,l.pC)(p)&&(({worldColumnCountFromOrigin:y,originColumnOffset:g,rightPadding:m}=p),y*p.blockWidth-m>=c+h&&(m=0));const x=Math.floor(c/a),I=Math.floor(u/o),b=Math.floor((c+h+m-1)/a),w=Math.floor((u+f+m-1)/o),_=n[e];if(!_)return null;const{minRow:S,minCol:R,maxCol:v,maxRow:C}=_;if(0===d&&(w<S||b<R||I>C||x>v))return null;const T=new Array;let k=!1;const F=null==this.ioConfig.allowPartialFill?s.allowPartialFill:this.ioConfig.allowPartialFill;for(let t=I;t<=w;t++)for(let i=x;i<=b;i++){let r=i;if(!s.disableWrapAround&&d&&(0,l.pC)(p)&&y<=i&&(r=i-y-g),t>=S&&r>=R&&C>=t&&v>=r){const i=this._fetchRawTile(e,t,r,s);F?T.push(new Promise((e=>{i.then((t=>e(t))).catch((()=>{k=!0,e(null)}))}))):T.push(i)}else T.push(Promise.resolve(null))}if(0===T.length)return null;const P=await Promise.all(T),M={height:(w-I+1)*o,width:(b-x+1)*a},{spatialReference:O}=this.rasterInfo,D=this.getPyramidPixelSize(e),{x:B,y:Z}=D;return{extent:new $.Z({xmin:r.x+x*a*B,xmax:r.x+(b+1)*a*B,ymin:r.y-(w+1)*o*Z,ymax:r.y-I*o*Z,spatialReference:O}),pixelBlocks:P,mosaicSize:M,isPartiallyFilled:k}}_fetchRawTile(e,t,i,s){const r=this.rasterInfo.storageInfo.blockBoundary[e];if(!r)return Promise.resolve(null);const{minRow:n,minCol:a,maxCol:o,maxRow:c}=r;if(t<n||i<a||t>c||i>o)return Promise.resolve(null);const h=W(this.url,s.sliceId),f=`${e}/${t}/${i}`;let d=H(h,s.registryId,f);if((0,l.Wi)(d)){const r=new AbortController;d=this.fetchRawTile(e,t,i,{...s,signal:r.signal}),A(h,s.registryId,f,d,r),d.catch((()=>L(h,s.registryId,f)))}return s.signal&&(0,u.fu)(s,(()=>{E(h,s.registryId,f)})),d}_computeMagDirValues(e){const{bandCount:t,dataType:i}=this.rasterInfo;if((2!==t||"vector-magdir"!==i)&&"vector-uv"!==i||2!==e?.length||!e[0]?.length)return null;const s=e[0].length;if("vector-magdir"===i){const t=e[1].map((e=>(e+360)%360));return[e[0],t]}const[r,n]=e,a=[],o=[];for(let e=0;e<s;e++){const[t,i]=(0,U.Tg)([r[e],n[e]]);a.push(t),o.push(i)}return[a,o]}_getRasterTileAlignmentInfo(e,t){return null==this._rasterTileAlighmentInfo&&(this._rasterTileAlighmentInfo=(0,Z.P_)(this.rasterInfo)),(0,l.Wi)(this._rasterTileAlighmentInfo.pyramidsInfo)?null:{startX:t,halfWorldWidth:this._rasterTileAlighmentInfo.halfWorldWidth,hasGCSSShiftTransform:this._rasterTileAlighmentInfo.hasGCSSShiftTransform,...this._rasterTileAlighmentInfo.pyramidsInfo[e]}}_getSourceDataInfo(e,t,i,s={}){const r={datumTransformation:s.datumTransformation,pyramidLevel:0,pyramidResolution:null,srcExtent:null,srcHeight:0,srcResolution:null,srcWidth:0};s.srcResolution&&(r.srcResolution=s.srcResolution,this._updateSourceDataInfo(e,r));const n=this.rasterInfo.storageInfo.maximumPyramidLevel||0,{srcWidth:a,srcHeight:o,pyramidLevel:l}=r,c=a/t,u=o/i,h=l<n&&c*u>=16,f=l===n&&this._requireTooManySrcTiles(a,o,t,i);if(h||f||0===a||0===o){const a=new z.Z({x:(e.xmax-e.xmin)/t,y:(e.ymax-e.ymin)/i,spatialReference:e.spatialReference});let o=(0,Z.VO)(a,this.rasterInfo.spatialReference,e,r.datumTransformation);const f=!o||s.srcResolution&&o.x+o.y<s.srcResolution.x+s.srcResolution.y;if(h&&s.srcResolution&&f){const e=Math.round(Math.log(Math.max(c,u))/Math.LN2)-1;if(n-l+3>=e){const t=2**e;o={x:s.srcResolution.x*t,y:s.srcResolution.y*t}}}o&&(r.srcResolution=o,this._updateSourceDataInfo(e,r))}return this._requireTooManySrcTiles(r.srcWidth,r.srcHeight,t,i)&&(r.srcWidth=0,r.srcHeight=0),r}_requireTooManySrcTiles(e,t,i,s){const{tileInfo:r}=this.rasterInfo.storageInfo;return Math.ceil(e/r.size[0])*Math.ceil(t/r.size[1])>=256||e/i>8||t/s>8}_updateSourceDataInfo(e,t){t.srcWidth=0,t.srcHeight=0;const i=this.rasterInfo.spatialReference,{srcResolution:s,datumTransformation:r}=t,{pyramidLevel:n,pyramidResolution:a,excessiveReading:o}=(0,Z.kr)(s,this.rasterInfo,this.ioConfig.sampling);if(o)return;let c=t.srcExtent||(0,Z.tB)(e,i,r);if(null==c)return;const u=(0,l.Wg)(this.rasterInfo.transform);u&&(c=u.inverseTransform(c)),t.srcExtent=c;const h=Math.ceil((c.xmax-c.xmin)/a.x-.1),f=Math.ceil((c.ymax-c.ymin)/a.y-.1);t.pyramidLevel=n,t.pyramidResolution=a,t.srcWidth=h,t.srcHeight=f}_getRequestOptionsWithSliceId(e){return(0,l.pC)(this.rasterInfo.multidimensionalInfo)&&null==e.sliceId&&(e={...e,sliceId:this.getSliceIndex(e.multidimensionalDefinition)}),e}_processIdentifyResult(e,t){const{srcLocation:i,position:s,pyramidLevel:r,useTransposedTile:n}=t,a=e.pixels[0].length/e.width/e.height;if(e.mask&&!e.mask[s])return{location:i,value:null};const{multidimensionalInfo:o}=this.rasterInfo;if((0,l.Wi)(o)||!n){const t=e.pixels.map((e=>e[s])),n={location:i,value:t,pyramidLevel:r},a=this._computeMagDirValues(t.map((e=>[e])));return a?.length&&(n.magdirValue=a.map((e=>e[0]))),n}let c=e.pixels.map((e=>e.slice(s*a,s*a+a))),u=this._computeMagDirValues(c);const{requestSomeSlices:h,identifyOptions:f}=t;let d=(0,B.MO)(o,f.transposedVariableName);if(h){const e=(0,B.Ur)(d,(0,l.Wg)(f.multidimensionalDefinition),(0,l.Wg)(f.timeExtent));c=c.map((t=>e.map((e=>t[e])))),u=u?.map((t=>e.map((e=>t[e])))),d=e.map((e=>d[e]))}const p=e.noDataValues||this.rasterInfo.noDataValue,m={pixels:c,pixelType:e.pixelType};let y;return(0,l.pC)(p)&&((0,q.A)(m,p),y=m.mask),{location:i,value:null,dataSeries:d.map(((e,t)=>{const i={value:0===y?.[t]?null:c.map((e=>e[t])),multidimensionalDefinition:e.multidimensionalDefinition.map((e=>new v.Z({...e,isSlice:!0})))};return u?.length&&(i.magdirValue=[u[0][t],u[1][t]]),i})),pyramidLevel:r}}};(0,s._)([(0,f.Cb)()],V.prototype,"_rasterTileAlighmentInfo",void 0),(0,s._)([(0,f.Cb)({readOnly:!0})],V.prototype,"_isGlobalWrappableSource",null),(0,s._)([(0,f.Cb)(R.HQ)],V.prototype,"url",null),(0,s._)([(0,f.Cb)({type:String,json:{write:!0}})],V.prototype,"datasetName",void 0),(0,s._)([(0,f.Cb)({type:String,json:{write:!0}})],V.prototype,"datasetFormat",void 0),(0,s._)([(0,f.Cb)()],V.prototype,"hasUniqueSourceStorageInfo",void 0),(0,s._)([(0,f.Cb)()],V.prototype,"rasterInfo",void 0),(0,s._)([(0,f.Cb)()],V.prototype,"ioConfig",void 0),(0,s._)([(0,f.Cb)()],V.prototype,"sourceJSON",void 0),V=(0,s._)([(0,y.j)("esri.layers.support.rasterDatasets.BaseRaster")],V);const X=V;let Y=class extends X{constructor(){super(...arguments),this.datasetFormat="Function",this.tileType="Raster",this.rasterFunction=null}async open(e){await this.init();const{rasterFunction:t}=this;this.primaryRasters?.rasters?.length?t.sourceRasters=this.primaryRasters.rasters:this.primaryRasters=t.getPrimaryRasters();const{rasters:i,rasterIds:s}=this.primaryRasters,r=i.map((t=>t.rasterInfo?void 0:t.open(e)));await Promise.all(r);const n=i.map((({rasterInfo:e})=>e)),o=t.bind({rasterInfos:n,rasterIds:s});if(!o.success||0===n.length)throw new a.Z("raster-function:open",`cannot bind the function: ${o.error??""}`);await this.syncJobHandler();const l=n[0];this.hasUniqueSourceStorageInfo=1===n.length||n.slice(1).every((e=>this._hasSameStorageInfo(e,l))),this.set("sourceJSON",i[0].sourceJSON),this.set("rasterInfo",t.rasterInfo)}async syncJobHandler(){return this.rasterJobHandler?.updateRasterFunction(this.rasterFunction)}async fetchPixels(e,t,i,s={}){const{rasters:r,rasterIds:n}=this.primaryRasters,a=r.map((r=>r.fetchPixels(e,t,i,s))),o=await Promise.all(a),c=o.map((e=>e.pixelBlock));if(s.skipRasterFunction||c.every((e=>(0,l.Wi)(e))))return o[0];const u=o.find((e=>(0,l.pC)(e.pixelBlock)))?.extent??e,h=this.rasterJobHandler?await this.rasterJobHandler.process({extent:u,primaryPixelBlocks:c,primaryRasterIds:n}):this.rasterFunction.process({extent:u,primaryPixelBlocks:c,primaryRasterIds:n});return{...o[0],pixelBlock:h}}_hasSameStorageInfo(e,t){const{storageInfo:i,pixelSize:s,spatialReference:r,extent:n}=e,{storageInfo:a,pixelSize:o,spatialReference:l,extent:c}=t;return s.x===o.x&&s.y===o.y&&r.equals(l)&&n.equals(c)&&i.blockHeight===a.blockHeight&&i.blockWidth===a.blockWidth&&i.maximumPyramidLevel===a.maximumPyramidLevel}};(0,s._)([(0,f.Cb)({type:String,json:{write:!0}})],Y.prototype,"datasetFormat",void 0),(0,s._)([(0,f.Cb)()],Y.prototype,"tileType",void 0),(0,s._)([(0,f.Cb)()],Y.prototype,"rasterFunction",void 0),(0,s._)([(0,f.Cb)()],Y.prototype,"primaryRasters",void 0),Y=(0,s._)([(0,y.j)("esri.layers.support.rasterDatasets.FunctionRaster")],Y);const K=Y;var Q=i(98722),ee=i(67058),te=i(72758),ie=i(23808),se=i(82971);const re=o.Z.getLogger("esri.layers.mixins.ImageryTileMixin"),ne=e=>{let t=class extends e{constructor(...e){super(...e),this._isConstructedFromFunctionRaster=!1,this._rasterJobHandler={instance:null,refCount:0,connectionPromise:null},this.bandIds=null,this.copyright=null,this.interpolation="nearest",this.multidimensionalSubset=null,this.raster=null,this.rasterFunction=null,this.rasterInfo=null,this.sourceJSON=null,this.spatialReference=null,this.symbolizer=null,this._isConstructedFromFunctionRaster="Function"===e[0]?.raster?.datasetFormat}get fullExtent(){return this.rasterInfo?.extent}set multidimensionalDefinition(e){this._set("multidimensionalDefinition",e),this.updateRenderer()}get tileInfo(){return this.rasterInfo?.storageInfo.tileInfo}set url(e){this._set("url",(0,S.Nm)(e,re))}set renderer(e){this._set("renderer",e),this.updateRenderer()}async convertVectorFieldData(e,t){if((0,l.Wi)(e)||!this.rasterInfo)return null;const i=this._rasterJobHandler.instance,s=this.rasterInfo.dataType;return i?i.convertVectorFieldData({pixelBlock:e,dataType:s},t):(0,U.KC)(e,s)}async createFlowMesh(e,t){const i=this._rasterJobHandler.instance;return i?i.createFlowMesh(e,t):(0,ie.GE)(e.meshType,e.simulationSettings,e.flowData,(0,l.pC)(t.signal)?t.signal:(new AbortController).signal)}normalizeRasterFetchOptions(e){const{multidimensionalInfo:t}=this.rasterInfo??{};if((0,l.Wi)(t))return e;let i=e.multidimensionalDefinition||this.multidimensionalDefinition;!(0,l.Wi)(i)&&i.length||(i=(0,B.Tj)(this.raster.rasterInfo,{multidimensionalSubset:this.multidimensionalSubset}));const s=e.timeExtent||this.timeExtent;if((0,l.pC)(i)&&(0,l.pC)(s)&&((0,l.pC)(s.start)||(0,l.pC)(s.end))){i=i.map((e=>e.clone()));const r=t.variables.find((({name:e})=>e===i[0].variableName))?.dimensions?.find((({name:e})=>"StdTime"===e)),n=i.find((({dimensionName:e})=>"StdTime"===e));if(!r||!n)return{...e,multidimensionalDefinition:null};const{start:a,end:o}=s,c=(0,l.Wi)(a)?null:a.getTime(),u=(0,l.Wi)(o)?null:o.getTime(),h=c??u,f=u??c;if((0,l.pC)(r.values)){const e=r.values.filter((e=>{if(Array.isArray(e)){if(h===f)return e[0]<=h&&e[1]>=h;const t=e[0]<=h&&e[1]>h||e[0]<f&&e[1]>=f,i=e[0]>=h&&e[1]<=f||e[0]<h&&e[1]>f;return t||i}return h===f?e===h:e>=h&&e<=f}));if(e.length){const t=e.sort(((e,t)=>h===f?(e[0]??e)-(t[0]??t):Math.abs((e[1]??e)-f)-Math.abs((t[1]??t)-f)))[0];n.values=[t]}else i=null}else if(r.hasRegularIntervals&&r.extent){const[e,t]=r.extent;h>t||f<e?i=null:n.values=h===f?[h]:[Math.max(e,h),Math.min(t,f)]}}return(0,l.pC)(i)&&(0,B.nb)(i,this.multidimensionalSubset)?{...e,multidimensionalDefinition:null}:{...e,multidimensionalDefinition:i}}async updateRasterFunction(){if("imagery-tile"!==this.type||!this.rasterFunction&&!this._cachedRasterFunctionJson||JSON.stringify(this.rasterFunction)===JSON.stringify(this._cachedRasterFunctionJson))return;if(this._isConstructedFromFunctionRaster&&"Function"===this.raster.datasetFormat){const e=this.raster.rasterFunction.toJSON();return!this.rasterFunction&&e&&this._set("rasterFunction",T.Z.fromJSON(e)),void(this._cachedRasterFunctionJson=this.rasterFunction?.toJSON())}let e,t=this.raster,i=!1;"Function"===t.datasetFormat?(e=t.primaryRasters.rasters,t=e[0],i=!0):e=[t];const{rasterFunction:s}=this;if(s){const i={raster:t};e.length>1&&e.forEach((e=>i[e.url]=e));const r=(0,Q.Ue)(s.rasterFunctionDefinition??s.toJSON(),i),n=new K({rasterFunction:r});n.rasterJobHandler=this._rasterJobHandler.instance,await n.open(),this._cachedRasterFunctionJson=this.rasterFunction?.toJSON(),this.raster=n}else this.raster=t,this._cachedRasterFunctionJson=null;if(this._cachedRendererJson=null,!i&&!s)return;const{bandIds:r}=this,{bandCount:n}=this.raster.rasterInfo,a=r?.length?r.some((e=>e>=n)):n>=3;r&&(a||"raster-stretch"!==this.renderer.type)&&this._set("bandIds",null),this._configDefaultRenderer("auto")}async updateRenderer(){const{loaded:e,symbolizer:t}=this;if(!e||!t)return;const{rasterInfo:i}=this.raster,s=(0,B.WY)(i,{multidimensionalDefinition:this.multidimensionalDefinition,multidimensionalSubset:this.multidimensionalSubset})?.name,r=(0,ee.ol)({...this.renderer.toJSON(),variableName:s});if(JSON.stringify(this._cachedRendererJson)===JSON.stringify(r))return;const n=this._rasterJobHandler.instance;n&&(t.rasterInfo=(0,ee.FI)(i,s),t.rendererJSON=r,t.bind(),await n.updateSymbolizer(t),this._cachedRendererJson=r)}async applyRenderer(e,t){const i=e&&e.pixelBlock;if(!((0,l.pC)(i)&&i.pixels&&i.pixels.length>0))return null;let s;await this.updateRenderer();const r=this._rasterJobHandler.instance,n=this.bandIds??[];return s=r?await r.symbolize({...e,simpleStretchParams:t,bandIds:n}):this.symbolizer.symbolize({...e,simpleStretchParams:t,bandIds:n}),s}getTileUrl(e,t,i){return"RasterTileServer"===this.raster.datasetFormat?`${this.url}/tile/${e}/${t}/${i}`:""}getCompatibleTileInfo(e,t,i=!1){if(!this.loaded||(0,l.Wi)(t))return null;if(i&&e.equals(this.spatialReference))return this.tileInfo;const s=(0,_.C5)(e);return F.Z.create({size:256,spatialReference:e,origin:s?{x:s.origin[0],y:s.origin[1]}:{x:t.xmin,y:t.ymax}})}getCompatibleFullExtent(e){return this.loaded?(this._compatibleFullExtent&&this._compatibleFullExtent.spatialReference.equals(e)||(this._compatibleFullExtent=this.raster.computeExtent(e)),this._compatibleFullExtent):null}async fetchTile(e,t,s,r={}){if(i(this),r.requestAsImageElement){const i=this.getTileUrl(e,t,s);return(0,w.default)(i,{responseType:"image",query:{...this.refreshParameters,...this.raster.ioConfig.customFetchParameters},signal:r.signal}).then((e=>e.data))}const{rasterInfo:n}=this;if((0,l.pC)(n.multidimensionalInfo)&&(r=this.normalizeRasterFetchOptions(r),(0,l.Wi)(r.multidimensionalDefinition))){const i=r.tileInfo||n.storageInfo.tileInfo;return{extent:this.raster.getTileExtentFromTileInfo(e,t,s,i),pixelBlock:null}}return await this._initJobHandler(),await this.updateRasterFunction(),"raster-shaded-relief"===this.renderer.type&&(r={...r,buffer:{cols:1,rows:1}}),this.raster.fetchTile(e,t,s,r)}async fetchPixels(e,t,i,s={}){return(0,l.pC)(this.rasterInfo.multidimensionalInfo)&&(s=this.normalizeRasterFetchOptions(s),(0,l.Wi)(s.multidimensionalDefinition))?{extent:e,pixelBlock:null}:(await this._initJobHandler(),await this.updateRasterFunction(),this.raster.fetchPixels(e,t,i,s))}async identify(e,t={}){const{raster:i,rasterInfo:s}=this;if((0,l.pC)(s.multidimensionalInfo)&&(!s.hasMultidimensionalTranspose||!((0,B.WU)(t.multidimensionalDefinition)||t.transposedVariableName||t.timeExtent))&&(t=this.normalizeRasterFetchOptions(t),(0,l.Wi)(t.multidimensionalDefinition)))return{location:e,value:null};const r=this.multidimensionalSubset?.areaOfInterest;if(r&&!r.contains(e))throw new a.Z("imagery-tile-mixin:identify","the request cannot be fulfilled when falling outside of the multidimensional subset");return i.identify(e,t)}increaseRasterJobHandlerUsage(){this._rasterJobHandler.refCount++}decreaseRasterJobHandlerUsage(){this._rasterJobHandler.refCount--,this._rasterJobHandler.refCount<=0&&this._shutdownJobHandler()}hasStandardTime(){const e=this.rasterInfo?.multidimensionalInfo;if((0,l.Wi)(e)||"standard-time"!==this.rasterInfo?.dataType)return!1;const t=this.multidimensionalDefinition,i=t?.[0]?.variableName;return e.variables.some((e=>e.name===i&&(!t?.[0].dimensionName||e.dimensions.some((e=>"StdTime"===e.name)))))}getStandardTimeValue(e){return new Date(24*(e-25569)*3600*1e3).toString()}getMultidimensionalSubsetVariables(e){const t=e??this.rasterInfo?.multidimensionalInfo;return(0,B.jj)(this.multidimensionalSubset,t)}_configDefaultSettings(){this._configDefaultInterpolation(),this.multidimensionalDefinition||(this.multidimensionalDefinition=(0,B.Tj)(this.raster.rasterInfo,{multidimensionalSubset:this.multidimensionalSubset})),this._configDefaultRenderer()}_initJobHandler(){if(null!=this._rasterJobHandler.connectionPromise)return this._rasterJobHandler.connectionPromise;const e=new k.Z;return this._rasterJobHandler.connectionPromise=e.initialize().then((()=>{i(this),this._rasterJobHandler.instance=e,this.raster.rasterJobHandler=e,this.renderer&&this.updateRenderer(),"Function"===this.raster.datasetFormat&&this.raster.syncJobHandler()})).catch((()=>{})),this._rasterJobHandler.connectionPromise}_shutdownJobHandler(){this._rasterJobHandler.instance&&this._rasterJobHandler.instance.destroy(),this._rasterJobHandler.instance=null,this._rasterJobHandler.connectionPromise=null,this._rasterJobHandler.refCount=0,this._cachedRendererJson=null,this.raster&&(this.raster.rasterJobHandler=null)}_configDefaultInterpolation(){if(null==this.interpolation){i(this);const{raster:e}=this,t=(0,ee.In)(e.rasterInfo,e.tileType,this.sourceJSON?.defaultResamplingMethod);this._set("interpolation",t)}}_configDefaultRenderer(e="no"){i(this);const{rasterInfo:t}=this.raster;!this.bandIds&&t.bandCount>1&&(this.bandIds=(0,ee.YD)(t));const s=(0,B.WY)(t,{multidimensionalDefinition:this.multidimensionalDefinition,multidimensionalSubset:this.multidimensionalSubset})?.name;if(!this.renderer||"override"===e){const e=(0,ee.Ob)(t,{bandIds:this.bandIds,variableName:s});"WCSServer"===this.raster.datasetFormat&&"raster-stretch"===e.type&&((t.statistics?.[0].max??0)>1e24||(t.statistics?.[0].min??0)<-1e24)&&(e.dynamicRangeAdjustment=!0,e.statistics=null,"none"===e.stretchType&&(e.stretchType="min-max")),this.renderer=e}const r=(0,ee.ol)({...this.renderer.toJSON(),variableName:s}),n=(0,ee.FI)(t,s);this.symbolizer?(this.symbolizer.rendererJSON=r,this.symbolizer.rasterInfo=n):this.symbolizer=new te.Z({rendererJSON:r,rasterInfo:n});const a=this.symbolizer.bind();if(a.success){if("auto"===e){const{colormap:e}=this.raster.rasterInfo,t=this.renderer;if((0,l.pC)(e))if("raster-colormap"!==t.type)this._configDefaultRenderer("override");else{const e=(0,ee.Ob)(this.raster.rasterInfo);JSON.stringify(e)!==JSON.stringify(t)&&this._configDefaultRenderer("override")}else if("raster-stretch"===t.type){const e=this.bandIds?.length,i=t.statistics?.length;!t.dynamicRangeAdjustment&&i&&e&&i!==e&&this._configDefaultRenderer("override")}}}else re.warn("imagery-tile-mixin",a.error||"The given renderer is not supported by the layer."),"auto"===e&&this._configDefaultRenderer("override")}};function i(e){if(!e.raster||!e.rasterInfo)throw new a.Z("imagery-tile","no raster")}return(0,s._)([(0,f.Cb)()],t.prototype,"_cachedRendererJson",void 0),(0,s._)([(0,f.Cb)()],t.prototype,"_cachedRasterFunctionJson",void 0),(0,s._)([(0,f.Cb)()],t.prototype,"_compatibleFullExtent",void 0),(0,s._)([(0,f.Cb)()],t.prototype,"_isConstructedFromFunctionRaster",void 0),(0,s._)([(0,f.Cb)()],t.prototype,"_rasterJobHandler",void 0),(0,s._)([(0,f.Cb)()],t.prototype,"bandIds",void 0),(0,s._)([(0,f.Cb)({json:{origins:{service:{read:{source:"copyrightText"}}}}})],t.prototype,"copyright",void 0),(0,s._)([(0,f.Cb)({json:{read:!1}})],t.prototype,"fullExtent",null),(0,s._)([(0,f.Cb)()],t.prototype,"interpolation",void 0),(0,s._)([(0,f.Cb)()],t.prototype,"ioConfig",void 0),(0,s._)([(0,f.Cb)({type:[v.Z],json:{write:!0}})],t.prototype,"multidimensionalDefinition",null),(0,s._)([(0,f.Cb)({type:C.Z,json:{write:!0}})],t.prototype,"multidimensionalSubset",void 0),(0,s._)([(0,f.Cb)()],t.prototype,"raster",void 0),(0,s._)([(0,f.Cb)({type:T.Z})],t.prototype,"rasterFunction",void 0),(0,s._)([(0,f.Cb)()],t.prototype,"rasterInfo",void 0),(0,s._)([(0,f.Cb)()],t.prototype,"sourceJSON",void 0),(0,s._)([(0,f.Cb)({readOnly:!0,type:se.Z,json:{read:!1}})],t.prototype,"spatialReference",void 0),(0,s._)([(0,f.Cb)({json:{read:!1}})],t.prototype,"tileInfo",null),(0,s._)([(0,f.Cb)(R.HQ)],t.prototype,"url",null),(0,s._)([(0,f.Cb)({types:n.dr})],t.prototype,"renderer",null),(0,s._)([(0,f.Cb)()],t.prototype,"symbolizer",void 0),t=(0,s._)([(0,y.j)("esri.layers.ImageryTileMixin")],t),t};var ae=i(38009),oe=i(16859),le=i(34760),ce=i(72965),ue=i(28294),he=i(1231),fe=i(69608),de=i(48526),pe=i(20095);function me(e){const t=e.fields,i=e.records,s=t.some((e=>"oid"===e.name.toLowerCase()))?"OBJECTID":"OID",r=[{name:s,type:"esriFieldTypeOID",alias:"OID"}].concat(t.map((e=>({name:e.name,type:"esriFieldType"+e.typeName,alias:e.name})))),n=r.map((e=>e.name)),a=[];let o=0,l=0;return i.forEach((e=>{const t={};for(t[s]=o++,l=1;l<n.length;l++)t[n[l]]=e[l-1];a.push({attributes:t})})),{displayFieldName:"",fields:r,features:a}}class ye{static get supportedVersions(){return[5]}static parse(e){const t=new DataView(e),i=3&t.getUint8(0);if(3!==i)return{header:{version:i},recordSet:null};const s=t.getUint32(4,!0),r=t.getUint16(8,!0),n=t.getUint16(10,!0),a={version:i,recordCount:s,headerByteCount:r,recordByteCount:n};let o=32;const l=[],c=[];let u;if(3===i){for(;13!==t.getUint8(o);)u=String.fromCharCode(t.getUint8(o+11)).trim(),l.push({name:(0,pe.f)(new Uint8Array(e,o,11)),type:u,typeName:["String","Date","Double","Boolean","String","Integer"][["C","D","F","L","M","N"].indexOf(u)],length:t.getUint8(o+16)}),o+=32;if(o+=1,l.length>0)for(;c.length<s&&e.byteLength-o>n;){const i=[];32===t.getUint8(o)?(o+=1,l.forEach((t=>{if("C"===t.type)i.push((0,pe.f)(new Uint8Array(e,o,t.length)).trim());else if("N"===t.type)i.push(parseInt(String.fromCharCode.apply(null,new Uint8Array(e,o,t.length)).trim(),10));else if("F"===t.type)i.push(parseFloat(String.fromCharCode.apply(null,new Uint8Array(e,o,t.length)).trim()));else if("D"===t.type){const s=String.fromCharCode.apply(null,new Uint8Array(e,o,t.length)).trim();i.push(new Date(parseInt(s.substring(0,4),10),parseInt(s.substring(4,6),10)-1,parseInt(s.substring(6,8),10)))}o+=t.length})),c.push(i)):o+=n}}return{header:a,fields:l,records:c,recordSet:me({fields:l,records:c})}}}var ge=i(87521),xe=i(74889);const Ie=new Map;Ie.set("int16","esriFieldTypeSmallInteger"),Ie.set("int32","esriFieldTypeInteger"),Ie.set("int64","esriFieldTypeInteger"),Ie.set("float32","esriFieldTypeSingle"),Ie.set("float64","esriFieldTypeDouble"),Ie.set("text","esriFieldTypeString");let be=class extends X{constructor(){super(...arguments),this.storageInfo=null,this.datasetFormat="CRF"}async open(e){await this.init();const{data:t}=await this.request(this.url+"/conf.json",{signal:e?.signal});if(!this._validateHeader(t))throw new a.Z("cloudraster:open","Invalid or unsupported conf.json.");this.datasetName=this.url.slice(this.url.lastIndexOf("/")+1);const{storageInfo:i,rasterInfo:s}=this._parseHeader(t);if("thematic"===s.dataType){const e=await this._fetchAuxiliaryInformation();s.attributeTable=e}this._set("storageInfo",i),this._set("rasterInfo",s),this.ioConfig.retryCount=this.ioConfig.retryCount||0}async fetchRawTile(e,t,i,s={}){const{transposeInfo:r}=this.rasterInfo.storageInfo,{transposedVariableName:n}=s,a=!(!r||!n),o=a?0:this.rasterInfo.storageInfo.maximumPyramidLevel-e;if(o<0)return null;const l=this._buildCacheFilePath(o,t,i,s.multidimensionalDefinition,n),c=this._getIndexRecordFromBundle(t,i,a),u=await this.request(l,{range:{from:0,to:this.storageInfo.headerSize-1},responseType:"array-buffer",signal:s.signal});if(!u)return null;const h=new Uint8Array(u.data),f=this._getTileEndAndContentType(h,c);if(0===f.recordSize)return null;const d=await this.request(l,{range:{from:f.position,to:f.position+f.recordSize},responseType:"array-buffer",signal:s.signal});if(!d)return null;const[p,m]=this._getTileSize(a);return this.decodePixelBlock(d.data,{width:p,height:m,planes:null,pixelType:null,returnInterleaved:a})}_validateHeader(e){return e&&"RasterInfo"===e.type&&!["origin","extent","geodataXform","LODInfos","blockWidth","blockHeight","bandCount","pixelType","pixelSizeX","pixelSizeY","format","packetSize"].some((t=>!e[t]))}_parseHeader(e){const t=["u1","u2","u4","u8","s8","u16","s16","u32","s32","f32","f64"][e.pixelType],{bandCount:i,histograms:s,colormap:r,blockWidth:n,blockHeight:a,firstPyramidLevel:o,maximumPyramidLevel:l}=e,c=e.statistics&&e.statistics.map((e=>({min:e.min,max:e.max,avg:e.mean,stddev:e.standardDeviation,median:e.median,mode:e.mode}))),u=e.extent.spatialReference,h=e.geodataXform?.spatialReference,f=new se.Z(u?.wkid||u?.wkt?u:h);let d=new $.Z({xmin:e.extent.xmin,ymin:e.extent.ymin,xmax:e.extent.xmax,ymax:e.extent.ymax,spatialReference:f});const p=new z.Z({x:e.pixelSizeX,y:e.pixelSizeY,spatialReference:f}),m=Math.round((d.xmax-d.xmin)/p.x),y=Math.round((d.ymax-d.ymin)/p.y),g=this._parseTransform(e.geodataXform),x=g?d:null;g&&(d=g.forwardTransform(d),p.x=(d.xmax-d.xmin)/m,p.y=(d.ymax-d.ymin)/y);const I=e.properties??{},b=e.format.toLowerCase().replace("cache/",""),w=new z.Z(e.origin.x,e.origin.y,f);let _,S,R,v;if(r&&r.colors)for(_=[],S=0;S<r.colors.length;S++)R=r.colors[S],v=r.values?r.values[S]:S,_.push([v,255&R,R<<16>>>24,R<<8>>>24,R>>>24]);const C=e.LODInfos,T=[];for(S=0;S<C.levels.length;S++)T.push(new O.Z({level:C.levels[S],resolution:C.resolutions[S],scale:96/.0254*C.resolutions[S]}));const k=new F.Z({dpi:96,lods:T,format:b,origin:w,size:[n,a],spatialReference:f}),P={recordSize:8,packetSize:e.packetSize,headerSize:e.packetSize*e.packetSize*8+64},M=[{maxCol:Math.ceil(m/n)-1,maxRow:Math.ceil(y/a)-1,minCol:0,minRow:0}];let B=2;if(l>0)for(S=0;S<l;S++)M.push({maxCol:Math.ceil(m/B/n)-1,maxRow:Math.ceil(y/B/a)-1,minCol:0,minRow:0}),B*=2;const Z=e.mdInfo;let J=null;if(Z&&I._yxs){const e=I._yxs;J={packetSize:e.PacketSize,tileSize:[e.TileXSize,e.TileYSize]}}return{storageInfo:P,rasterInfo:new de.Z({width:m,height:y,pixelType:t,bandCount:i,extent:d,nativeExtent:x,transform:g,spatialReference:f,pixelSize:p,keyProperties:I,statistics:c,histograms:s,multidimensionalInfo:Z,colormap:_,storageInfo:new D.Z({blockWidth:n,blockHeight:a,pyramidBlockWidth:n,pyramidBlockHeight:a,origin:w,tileInfo:k,transposeInfo:J,firstPyramidLevel:o,maximumPyramidLevel:l,blockBoundary:M})})}}_parseTransform(e){if(!(0,ge.j)(e))throw new a.Z("cloudraster:open","the data contains unsupported geodata transform types");const t=(0,ge.c)(e);if("identity"===t.type)return null;if("polynomial"!==t.type||!t.forwardCoefficients?.length||!t.inverseCoefficients?.length)throw new a.Z("cloudraster:open","the data contains unsupported geodata transforms - both forward and inverse coefficients are required currently");return t}async _fetchAuxiliaryInformation(e){const t=this.request(this.url+"/conf.vat.json",{signal:e}).then((e=>e.data)).catch((()=>null)),i=this.request(this.url+"/conf.vat.dbf",{responseType:"array-buffer",signal:e}).then((e=>e.data)).catch((()=>null)),s=await Promise.all([t,i]);let r;if(s[0]){let e=s[0].fields;const t=s[0].values;if(e&&t){e=e.map((e=>({type:"OID"===e.name?"esriFieldTypeOID":Ie.get(e.type),name:e.name,alias:e.alias||e.name})));const i=t.map((e=>({attributes:e})));e&&t&&(r={fields:e,features:i})}}return!r&&s[1]&&(r=ye.parse(s[1]).recordSet),xe.Z.fromJSON(r)}_buildCacheFilePath(e,t,i,s,r){const n=this._getPackageSize(!!r),a=Math.floor(t/n)*n,o=Math.floor(i/n)*n,c="R"+this._toHexString4(a)+"C"+this._toHexString4(o);let u="L";u+=e>=10?e.toString():"0"+e.toString();const{multidimensionalInfo:h}=this.rasterInfo,f=s?.[0];if((0,l.Wi)(h)||!f)return`${this.url}/_alllayers/${u}/${c}.bundle`;let d="_yxs";if(!r){d=h.variables.find((e=>e.name===f.variableName)).dimensions[0].values.indexOf(f.values[0]).toString(16);const e=4-d.length;for(let t=0;t<e;t++)d="0"+d;d="S"+d}const p=this._getVariableFolderName(r||f.variableName);return`${this.url}/_alllayers/${p}/${d}/${u}/${c}.bundle`}_getPackageSize(e=!1){const{transposeInfo:t}=this.rasterInfo.storageInfo;return e&&(0,l.pC)(t)?t.packetSize??0:this.storageInfo.packetSize}_getTileSize(e=!1){const{storageInfo:t}=this.rasterInfo,{transposeInfo:i}=t;return e&&(0,l.pC)(i)?i.tileSize:t.tileInfo.size}_getVariableFolderName(e){return""===(e=e.trim())?"_v":e.replace(/[\{|\}\-]/g,"_").replace("\\*","_v")}_getIndexRecordFromBundle(e,t,i=!1){const s=this._getPackageSize(i),r=s*(e%s)+t%s;if(r<0)throw new Error("Invalid level / row / col");return 20+r*this.storageInfo.recordSize+44}_getTileEndAndContentType(e,t){const i=e.subarray(t,t+8);let s,r=0;for(s=0;s<5;s++)r|=(255&i[s])<<8*s;const n=0xffffffffff&r;for(r=0,s=5;s<8;s++)r|=(255&i[s])<<8*(s-5);return{position:n,recordSize:0xffffffffff&r}}_toHexString4(e){let t=e.toString(16);if(4!==t.length){let e=4-t.length;for(;e-- >0;)t="0"+t}return t}};(0,s._)([(0,f.Cb)({readOnly:!0})],be.prototype,"storageInfo",void 0),(0,s._)([(0,f.Cb)({type:String,json:{write:!0}})],be.prototype,"datasetFormat",void 0),be=(0,s._)([(0,y.j)("esri.layers.support.rasterDatasets.CloudRaster")],be);const we=be;var _e=i(80442),Se=i(15612);let Re=class extends X{constructor(){super(...arguments),this.datasetFormat="MEMORY",this.data=null}async open(e){await this.init();const t=this.data,{pixelBlock:i,statistics:s,histograms:r,name:n,keyProperties:a,nativeExtent:o,transform:l}=this.data,{width:c,height:u,pixelType:h}=i,f=t.extent??new $.Z({xmin:-.5,ymin:.5,xmax:c-.5,ymax:u-.5,spatialReference:new se.Z({wkid:3857})}),d=t.isPseudoSpatialReference??!t.extent,p={x:f.width/c,y:f.height/u},m=new de.Z({width:c,height:u,pixelType:h,extent:f,nativeExtent:o,transform:l,pixelSize:p,spatialReference:f.spatialReference,bandCount:i.pixels.length,keyProperties:a||{},statistics:s,isPseudoSpatialReference:d,histograms:r});this.createRemoteDatasetStorageInfo(m,512,512),this._set("rasterInfo",m),this.updateTileInfo(),await this._buildInMemoryRaster(i,{width:512,height:512},e),this.datasetName=n,this.url="/InMemory/"+n}fetchRawTile(e,t,i,s={}){const r=this._pixelBlockTiles.get(`${e}/${t}/${i}`);return Promise.resolve(r)}async _buildInMemoryRaster(e,t,i){const s=this.rasterInfo.storageInfo.maximumPyramidLevel,r=this.rasterJobHandler?this.rasterJobHandler.split({pixelBlock:e,tileSize:t,maximumPyramidLevel:s},i):Promise.resolve((0,G.Vl)(e,t,s)),n=(0,l.pC)(this.rasterInfo.statistics),o=(0,l.pC)(this.rasterInfo.histograms),c=n?Promise.resolve({statistics:null,histograms:null}):this.rasterJobHandler?this.rasterJobHandler.estimateStatisticsHistograms({pixelBlock:e},i):Promise.resolve((0,Se.Hv)(e)),h=await(0,u.as)([r,c]);if(!h[0].value&&h[1].value)throw new a.Z("inmemory-raster:open","failed to build in memory raster");this._pixelBlockTiles=h[0].value,n||(this.rasterInfo.statistics=h[1].value?.statistics),o||(this.rasterInfo.histograms=h[1].value?.histograms)}};(0,s._)([(0,f.Cb)({type:String,json:{write:!0}})],Re.prototype,"datasetFormat",void 0),(0,s._)([(0,f.Cb)()],Re.prototype,"data",void 0),Re=(0,s._)([(0,y.j)("esri.layers.support.rasterDatasets.InMemoryRaster")],Re);const ve=Re;function Ce(e,t){if(!e||!t)return[];let i=t;t.includes("/")?(i=t.slice(0,t.indexOf("/")),t=t.slice(t.indexOf("/")+1)):t="";const s=[];if(t){const r=Ce(e,i);for(let e=0;e<r.length;e++)Ce(r[e],t).forEach((e=>s.push(e)));return s}const r=e.getElementsByTagNameNS("*",i);if(!r||0===r.length)return[];for(let e=0;e<r.length;e++)s.push(r[e]||r.item[e]);return s}function Te(e,t){if(!e||!t)return null;let i=t;t.includes("/")?(i=t.slice(0,t.indexOf("/")),t=t.slice(t.indexOf("/")+1)):t="";const s=Ce(e,i);return s.length>0?t?Te(s[0],t):s[0]:null}function ke(e,t=null){const i=t?Te(e,t):e;let s;return i?(s=i.textContent||i.nodeValue,s?s.trim():null):null}function Fe(e,t){return function(e,t){const i=Ce(e,t),s=[];let r;for(let e=0;e<i.length;e++)r=i[e].textContent||i[e].nodeValue,r&&(r=r.trim(),""!==r&&s.push(r));return s}(e,t).map((e=>Number(e)))}function Pe(e,t){const i=ke(e,t);return Number(i)}function Me(e,t){const i=e?.nodeName?.toLowerCase(),s=t.toLowerCase();return i.slice(i.lastIndexOf(":")+1)===s}var Oe=i(87390);function De(e,t){if(!e||!t)return null;const i=[];for(let s=0;s<e.length;s++)i.push(e[s]),i.push(t[s]);return i}function Be(e){if(!e)return null;let t=Number(e);if(!isNaN(t)&&0!==t)return new se.Z({wkid:t});if((e=String(e)).startsWith("COMPD_CS")){if(!e.includes("VERTCS")||!e.includes("GEOGCS")&&!e.startsWith("PROJCS"))return null;const i=e.indexOf("VERTCS"),s=e.indexOf("PROJCS"),r=s>-1?s:e.indexOf("GEOGCS");if(-1===r)return null;const n=e.slice(r,e.lastIndexOf("]",i)+1).trim(),a=e.slice(i,e.lastIndexOf("]")).trim();t=Ze(n);const o=new se.Z(t?{wkid:t}:{wkt:n}),l=Ze(a);return l&&(o.vcsWkid=l),o}return e.startsWith("GEOGCS")||e.startsWith("PROJCS")?(t=Ze(e),new se.Z(0!==t?{wkid:t}:{wkt:e})):null}function Ze(e){const t=e.replace(/\]/g,"[").replace(/\"/g,"").split("[").map((e=>e.trim())).filter((e=>""!==e)),i=t[t.length-1].split(","),s=i[0]?.toLowerCase();if(("epsg"===s||"esri"===s)&&e.endsWith('"]]')){const e=Number(i[1]);if(!isNaN(e)&&0!==e)return e}return 0}function ze(e){if("pamdataset"!==e?.documentElement.tagName?.toLowerCase())return{};const t={spatialReference:null,transform:null,metadata:{},rasterBands:[],statistics:null,histograms:null};e.documentElement.childNodes.forEach((e=>{if(1===e.nodeType)if(Me(e,"SRS")){if(!t.spatialReference){const i=ke(e);t.spatialReference=Be(i)}}else if(Me(e,"Metadata"))if("xml:ESRI"===e.getAttribute("domain")){const{spatialReference:i,transform:s}=function(e){const t=Te(e,"GeodataXform"),i=Be(Pe(t,"SpatialReference/WKID")||ke(t,"SpatialReference/WKT"));if("typens:PolynomialXform"!==t.getAttribute("xsi:type"))return{spatialReference:i,transform:null};const s=Pe(t,"PolynomialOrder")??1,r=Fe(t,"CoeffX/Double"),n=Fe(t,"CoeffY/Double"),a=Fe(t,"InverseCoeffX/Double"),o=Fe(t,"InverseCoeffY/Double"),l=De(r,n),c=De(a,o);return{spatialReference:i,transform:l&&c&&l.length&&c.length?new Oe.Z({spatialReference:i,polynomialOrder:s,forwardCoefficients:l,inverseCoefficients:c}):null}}(e);t.transform=s,t.spatialReference||(t.spatialReference=i)}else Ce(e,"MDI").forEach((e=>t.metadata[e.getAttribute("key")]=ke(e)));else if(Me(e,"PAMRasterBand")){const i=function(e){const t=Pe(e,"NoDataValue"),i=Te(e,"Histograms/HistItem"),s=Pe(i,"HistMin"),r=Pe(i,"HistMax"),n=Pe(i,"BucketCount"),a=ke(i,"HistCounts")?.split("|").map((e=>Number(e)));let o,l,c,u;Ce(e,"Metadata/MDI").forEach((e=>{const t=Number(e.textContent??e.nodeValue);switch(e.getAttribute("key").toUpperCase()){case"STATISTICS_MINIMUM":o=t;break;case"STATISTICS_MAXIMUM":l=t;break;case"STATISTICS_MEAN":c=t;break;case"STATISTICS_STDDEV":u=t}}));const h=Pe(e,"Metadata/SourceBandIndex");return{noDataValue:t,histogram:a?.length&&null!=s&&null!=r?{min:s,max:r,size:n||a.length,counts:a}:null,sourceBandIndex:h,statistics:null!=o&&null!=l?{min:o,max:l,avg:c,stddev:u}:null}}(e);null!=i.sourceBandIndex&&null==t.rasterBands[i.sourceBandIndex]?t.rasterBands[i.sourceBandIndex]=i:t.rasterBands.push(i)}}));const i=t.rasterBands;if(i.length){const e=!!i[0].statistics;t.statistics=e?i.map((e=>e.statistics)).filter(l.pC):null;const s=!!i[0].histogram;t.histograms=s?i.map((e=>e.histogram)).filter(l.pC):null}return t}let Je=class extends X{async open(e){await this.init();const t=await this._fetchData(e);let{spatialReference:i,statistics:s,histograms:r,transform:n}=await this._fetchAuxiliaryData(e);const a=!i;a&&(i=new se.Z({wkid:3857})),r?.length&&null==s&&(s=(0,Se.Oh)(r));const{width:o,height:l}=t;let c=new $.Z({xmin:-.5,ymin:.5-l,xmax:o-.5,ymax:.5,spatialReference:i});const u=n?n.forwardTransform(c):c;let h=!0;if(n){const e=n.forwardCoefficients;h=e&&0===e[1]&&0===e[2],h&&(n=null,c=u)}const f=new ve({data:{extent:u,nativeExtent:c,transform:n,pixelBlock:t,statistics:s,histograms:r,keyProperties:{DateType:"Processed"},isPseudoSpatialReference:a}});await f.open(),f.data=null,this._set("rasterInfo",f.rasterInfo),this._inMemoryRaster=f}fetchRawTile(e,t,i,s={}){return this._inMemoryRaster.fetchRawTile(e,t,i,s)}async _fetchData(e){const{data:t}=await this.request(this.url,{responseType:"array-buffer",signal:e?.signal}),i=(0,j.y)(t).toUpperCase();if("JPG"!==i&&"PNG"!==i&&"GIF"!==i&&"BMP"!==i)throw new a.Z("image-aux-raster:open","the data is not a supported format");this._set("datasetFormat",i);const s=i.toLowerCase(),r="gif"===s||"bmp"===s||!(0,_e.Z)("ios"),n=await this.decodePixelBlock(t,{format:s,useCanvas:r,hasNoZlibMask:!0});if(null==n)throw new a.Z("image-aux-raster:open","the data cannot be decoded");return n}async _fetchAuxiliaryData(e){const t=(0,l.Wg)(e?.signal),i=this.ioConfig.skipExtensions??[],s=i.includes("aux.xml")?null:this.request(this.url+".aux.xml",{responseType:"xml",signal:t}),r=this.datasetFormat,n="JPG"===r?"jgw":"PNG"===r?"pgw":"BMP"===r?"bpw":null,a=n&&i.includes(n)?null:this.request(this.url.slice(0,this.url.lastIndexOf("."))+"."+n,{responseType:"text",signal:t}),o=await(0,u.as)([s,a]);if(t?.aborted)throw(0,u.zE)();const c=ze(o[0].value?.data);if(!c.transform){const e=o[1].value?o[1].value.data.split("\n").slice(0,6).map((e=>Number(e))):null;c.transform=6===e?.length?new Oe.Z({forwardCoefficients:[e[4],e[5],e[0],-e[1],e[2],-e[3]]}):null}return c}};(0,s._)([(0,f.Cb)({type:String,json:{write:!0}})],Je.prototype,"datasetFormat",void 0),Je=(0,s._)([(0,y.j)("esri.layers.support.rasterDatasets.ImageAuxRaster")],Je);const Ne=Je;var We=i(17452),Ee=i(45322),He=i(56608),Ae=i(29680),Le=i(26059);let qe=class extends X{constructor(){super(...arguments),this._levelOffset=0,this._tilemapCache=null,this._slices=null,this.datasetFormat="RasterTileServer",this.tileType=null}async open(e){await this.init();const t=e&&e.signal,i=this.sourceJSON?{data:this.sourceJSON}:await this.request(this.url,{query:{f:"json"},signal:t});i.ssl&&(this.url=this.url.replace(/^http:/i,"https:"));const s=i.data;if(this.sourceJSON=s,!s)throw new a.Z("imageserverraster:open","cannot initialize tiled image service, missing service info");if(!s.tileInfo)throw new a.Z("imageserverraster:open","use ImageryLayer to open non-tiled image services");this._fixScaleInServiceInfo(),this.tileType=s.cacheType,null==this.tileType&&(["jpg","jpeg","png","png8","png24","png32","mixed"].includes(s.tileInfo.format.toLowerCase())?this.tileType="Map":"lerc"===s.tileInfo.format.toLowerCase()?this.tileType="Elevation":this.tileType="Raster"),this.datasetName=s.name?.slice(s.name.indexOf("/")+1)??"";const r=await this._fetchRasterInfo({signal:t});if((0,l.Wi)(r))throw new a.Z("image-server-raster:open","cannot initialize image service");const n="Map"===this.tileType?(0,Ee.d)(s.tileInfo,s):F.Z.fromJSON(s.tileInfo);(0,l.O3)(n);const[o,c]=this._computeMinMaxLOD(r,n),{extent:u,pixelSize:h}=r,f=.5/r.width*h.x,d=Math.max(h.x,h.y),{lods:p}=n;("Map"!==this.tileType&&0!==s.maxScale||Math.abs(h.x-h.y)>f||!p.some((e=>Math.abs(e.resolution-d)<f)))&&(h.x=h.y=o.resolution,r.width=Math.ceil((u.xmax-u.xmin)/h.x-.1),r.height=Math.ceil((u.ymax-u.ymin)/h.y-.1));const m=o.level-c.level,[y,g]=n.size,x=[],I=[];p.forEach(((e,t)=>{e.level>=c.level&&e.level<=o.level&&x.push({x:e.resolution,y:e.resolution}),t<p.length-1&&I.push(Math.round(10*e.resolution/p[t+1].resolution)/10)})),x.sort(((e,t)=>e.x-t.x));const b=this.computeBlockBoundary(u,y,g,n.origin,x,m),w=x.length>1?x.slice(1):null;let _;s.transposeInfo&&(_={tileSize:[s.transposeInfo.rows,s.transposeInfo.cols],packetSize:r.keyProperties?._yxs.PacketSize??0});const S=I.length<=1||I.length>=3&&I.slice(0,I.length-1).every((e=>e===I[0]))?I[0]??2:Math.round(10/(c.resolution/o.resolution)**(-1/m))/10;if(r.storageInfo=new D.Z({blockWidth:n.size[0],blockHeight:n.size[1],pyramidBlockWidth:n.size[0],pyramidBlockHeight:n.size[1],pyramidResolutions:w,pyramidScalingFactor:S,compression:n.format,origin:n.origin,firstPyramidLevel:1,maximumPyramidLevel:m,tileInfo:n,transposeInfo:_,blockBoundary:b}),this._fixGCSShift(r),this._set("rasterInfo",r),s.capabilities.toLowerCase().includes("tilemap")){const e={tileInfo:r.storageInfo.tileInfo,parsedUrl:(0,We.mN)(this.url),url:this.url,tileServers:[],type:"tile"};this._tilemapCache=new He.y({layer:e})}}async fetchRawTile(e,t,i,s={}){const{storageInfo:r,extent:n}=this.rasterInfo,{transposeInfo:a}=r,o=(0,l.pC)(a)&&!!s.transposedVariableName;if(this._slices&&!o&&null==s.sliceId)return null;const c=o?0:r.maximumPyramidLevel-e+this._levelOffset,u=`${this.url}/tile/${c}/${t}/${i}`,h=this._slices?o?{variable:s.transposedVariableName}:{sliceId:s.sliceId||0}:null,{data:f}=await this.request(u,{query:h,responseType:"array-buffer",signal:s.signal});if(!f)return null;const d=o?a.tileSize:r.tileInfo.size,p=await this.decodePixelBlock(f,{width:d[0],height:d[1],planes:null,pixelType:null,isPoint:"Elevation"===this.tileType,returnInterleaved:o,noDataValue:(0,l.Wg)(this.rasterInfo.noDataValue)});if(null==p)return null;const m=r.blockBoundary[e];if("jpg"!==r.compression||i>m.minCol&&i<m.maxCol&&t>m.minRow&&t<m.maxRow)return p;const{origin:y,blockWidth:g,blockHeight:x}=r,{x:I,y:b}=this.getPyramidPixelSize(e),w=Math.round((n.xmin-y.x)/I)%g,_=Math.round((n.xmax-y.x)/I)%g||g,S=Math.round((y.y-n.ymax)/b)%x,R=Math.round((y.y-n.ymin)/b)%x||x,v=i===m.minCol?w:0,C=t===m.minRow?S:0,T=i===m.maxCol?_:g,k=t===m.maxRow?R:x;return(0,G.pW)(p,{x:v,y:C},{width:T-v,height:k-C}),p}getSliceIndex(e){if(!this._slices||(0,l.Wi)(e)||0===e.length)return null;const t=e;for(let e=0;e<this._slices.length;e++){const i=this._slices[e].multidimensionalDefinition;if(i.length===t.length&&!i.some((e=>{const i=t.find((t=>e.variableName===t.variableName&&t.dimensionName===e.dimensionName));return!i||(Array.isArray(e.values[0])?`${e.values[0][0]}-${e.values[0][1]}`:e.values[0])!==(Array.isArray(i.values[0])?`${i.values[0][0]}-${i.values[0][1]}`:i.values[0])})))return e}return null}async fetchVariableStatisticsHistograms(e,t){const i=this.request(this.url+"/statistics",{query:{variable:e,f:"json"},signal:t}).then((e=>e.data?.statistics)),s=this.request(this.url+"/histograms",{query:{variable:e,f:"json"},signal:t}).then((e=>e.data?.histograms)),r=await Promise.all([i,s]);return r[0]&&r[0].forEach((e=>{e.avg=e.mean,e.stddev=e.standardDeviation})),{statistics:r[0]||null,histograms:r[1]||null}}async computeBestPyramidLevelForLocation(e,t={}){if(!this._tilemapCache)return 0;let i=this.identifyPixelLocation(e,0,(0,l.Wg)(t.datumTransformation));if(null===i)return null;let s=0;const{maximumPyramidLevel:r}=this.rasterInfo.storageInfo;let n=r-s+this._levelOffset;const a=i.srcLocation;for(;n>=0;){try{if("available"===await this._tilemapCache.fetchAvailability(n,i.row,i.col,t))break}catch{}if(n--,s++,i=this.identifyPixelLocation(a,s,(0,l.Wg)(t.datumTransformation)),null===i)return null}return-1===n||null==i?null:s}async _fetchRasterInfo(e){const t=this.sourceJSON;if("Map"===this.tileType){const e=t.fullExtent||t.extent,i=Math.ceil((e.xmax-e.xmin)/t.pixelSizeX-.1),s=Math.ceil((e.ymax-e.ymin)/t.pixelSizeY-.1),r=se.Z.fromJSON(t.spatialReference||e.spatialReference),n=new z.Z({x:t.pixelSizeX,y:t.pixelSizeY,spatialReference:r});return new de.Z({width:i,height:s,bandCount:3,extent:$.Z.fromJSON(e),spatialReference:r,pixelSize:n,pixelType:"u8",statistics:null,keyProperties:{DataType:"processed"}})}const{signal:i}=e,s=(0,Le.g)(this.url,this.sourceJSON,{signal:i,query:this.ioConfig.customFetchParameters}),r=t.hasMultidimensions?this.request(`${this.url}/slices`,{query:{f:"json"},signal:i}).then((e=>e.data&&e.data.slices)).catch((()=>null)):null,n=await Promise.all([s,r]);return this._slices=n[1],n[0]}_fixScaleInServiceInfo(){const{sourceJSON:e}=this;e.minScale&&e.minScale<0&&(e.minScale=0),e.maxScale&&e.maxScale<0&&(e.maxScale=0)}_fixGCSShift(e){const{extent:t,spatialReference:i}=e;t.xmin>-1&&t.xmax>181&&i?.wkid&&i.isGeographic&&(e.nativeExtent=e.extent,e.transform=new Ae.Z,e.extent=e.transform.forwardTransform(t))}_computeMinMaxLOD(e,t){const{pixelSize:i}=e,s=.5/e.width*i.x,{lods:r}=t,n=t.lodAt(Math.max.apply(null,r.map((e=>e.level)))),a=t.lodAt(Math.min.apply(null,r.map((e=>e.level)))),{tileType:o}=this;if("Map"===o)return this._levelOffset=r[0].level,[n,a];if("Raster"===o)return[r.find((e=>e.resolution===i.x))??n,a];const{minScale:l,maxScale:c}=this.sourceJSON;let u=n;c>0&&(u=r.find((e=>Math.abs(e.scale-c)<s)),u||(u=r.filter((e=>e.scale>c)).sort(((e,t)=>e.scale>t.scale?1:-1))[0]??n));let h=a;return l>0&&(h=r.find((e=>Math.abs(e.scale-l)<s))??a,this._levelOffset=h.level-a.level),[u,h]}};(0,s._)([(0,f.Cb)({type:String,json:{write:!0}})],qe.prototype,"datasetFormat",void 0),(0,s._)([(0,f.Cb)()],qe.prototype,"tileType",void 0),qe=(0,s._)([(0,y.j)("esri.layers.support.rasterDatasets.ImageServerRaster")],qe);const je=qe;var Ge=i(5847),Ue=i(48279);const $e=new Map;$e.set("Int8","s8"),$e.set("UInt8","u8"),$e.set("Int16","s16"),$e.set("UInt16","u16"),$e.set("Int32","s32"),$e.set("UInt32","u32"),$e.set("Float32","f32"),$e.set("Float64","f32"),$e.set("Double64","f32");const Ve=new Map;Ve.set("none",{blobExtension:".til",isOneSegment:!0,decoderFormat:"bip"}),Ve.set("lerc",{blobExtension:".lrc",isOneSegment:!1,decoderFormat:"lerc"}),Ve.set("deflate",{blobExtension:".pzp",isOneSegment:!0,decoderFormat:"deflate"}),Ve.set("jpeg",{blobExtension:".pjg",isOneSegment:!0,decoderFormat:"jpg"});let Xe=class extends X{constructor(){super(...arguments),this._files=null,this._storageIndex=null,this.datasetFormat="MRF"}async open(e){await this.init(),this.datasetName=this.url.slice(this.url.lastIndexOf("/")+1);const t=e?(0,l.Wg)(e.signal):null,i=await this.request(this.url,{responseType:"xml",signal:t}),{rasterInfo:s,files:r}=this._parseHeader(i.data);if(-1===this.ioConfig.skipExtensions?.indexOf("aux.xml")){const t=await this._fetchAuxiliaryData(e);null!=t&&(s.statistics=t.statistics??s.statistics,s.histograms=t.histograms,t.histograms&&(0,l.Wi)(s.statistics)&&(s.statistics=(0,Se.Oh)(t.histograms)))}this._set("rasterInfo",s),this._files=r;const n=await this.request(r.index,{responseType:"array-buffer",signal:t});this._storageIndex=this._parseIndex(n.data);const{blockWidth:a,blockHeight:o}=this.rasterInfo.storageInfo,c=this.rasterInfo.storageInfo.pyramidScalingFactor,{width:u,height:h}=this.rasterInfo,f=[],d=this._getBandSegmentCount();let p=0,m=-1;for(;p<this._storageIndex.length;){m++;const e=Math.ceil(u/a/c**m)-1,t=Math.ceil(h/o/c**m)-1;p+=(e+1)*(t+1)*d*4,f.push({maxRow:t,maxCol:e,minCol:0,minRow:0})}this.rasterInfo.storageInfo.blockBoundary=f,m>0&&(this.rasterInfo.storageInfo.firstPyramidLevel=1,this.rasterInfo.storageInfo.maximumPyramidLevel=m),this.updateTileInfo()}async fetchRawTile(e,t,i,s={}){const{blockWidth:r,blockHeight:n,blockBoundary:a}=this.rasterInfo.storageInfo,o=a[e];if(!o||o.maxRow<t||o.maxCol<i||o.minRow>t||o.minCol>i)return null;const{bandCount:c,pixelType:u}=this.rasterInfo,{ranges:h,actualTileWidth:f,actualTileHeight:d}=this._getTileLocation(e,t,i);if(!h||0===h.length)return null;if(0===h[0].from&&0===h[0].to){const e=new Uint8Array(r*n);return new Ge.Z({width:r,height:n,pixels:null,mask:e,validPixelCount:0})}const{bandIds:p}=this.ioConfig,m=this._getBandSegmentCount(),y=[];let g=0;for(g=0;g<m;g++)(!p||p.indexOf[g]>-1)&&y.push(this.request(this._files.data,{range:{from:h[g].from,to:h[g].to},responseType:"array-buffer",signal:s.signal}));const x=await Promise.all(y),I=x.map((e=>e.data.byteLength)).reduce(((e,t)=>e+t)),b=new Uint8Array(I);let w=0;for(g=0;g<m;g++)b.set(new Uint8Array(x[g].data),w),w+=x[g].data.byteLength;const _=Ve.get(this.rasterInfo.storageInfo.compression).decoderFormat,S=await this.decodePixelBlock(b.buffer,{width:r,height:n,format:_,planes:p?.length||c,pixelType:u});if(null==S)return null;if((0,l.pC)(this.rasterInfo.noDataValue)&&"lerc"!==_&&!S.mask){const e=this.rasterInfo.noDataValue[0];if(null!=e){const t=S.width*S.height,i=new Uint8Array(t);if(Math.abs(e)>1e24)for(g=0;g<t;g++)Math.abs((S.pixels[0][g]-e)/e)>1e-6&&(i[g]=1);else for(g=0;g<t;g++)S.pixels[0][g]!==e&&(i[g]=1);S.mask=i}}let R=0,v=0;if(f!==r||d!==n){let e=S.mask;if(e)for(g=0;g<n;g++)if(v=g*r,g<d)for(R=f;R<r;R++)e[v+R]=0;else for(R=0;R<r;R++)e[v+R]=0;else for(e=new Uint8Array(r*n),S.mask=e,g=0;g<d;g++)for(v=g*r,R=0;R<f;R++)e[v+R]=1}return S}_parseIndex(e){if(e.byteLength%16>0)throw new Error("invalid array buffer must be multiples of 16");let t,i,s,r,n,a;if(Ue.f){for(i=new Uint8Array(e),r=new ArrayBuffer(e.byteLength),s=new Uint8Array(r),n=0;n<e.byteLength/4;n++)for(a=0;a<4;a++)s[4*n+a]=i[4*n+3-a];t=new Uint32Array(r)}else t=new Uint32Array(e);return t}_getBandSegmentCount(){return Ve.get(this.rasterInfo.storageInfo.compression).isOneSegment?1:this.rasterInfo.bandCount}_getTileLocation(e,t,i){const{blockWidth:s,blockHeight:r,pyramidScalingFactor:n}=this.rasterInfo.storageInfo,{width:a,height:o}=this.rasterInfo,l=this._getBandSegmentCount();let c,u,h,f=0,d=0;for(h=0;h<e;h++)d=n**h,c=Math.ceil(a/s/d),u=Math.ceil(o/r/d),f+=c*u;d=n**e,c=Math.ceil(a/s/d),u=Math.ceil(o/r/d),f+=t*c+i,f*=4*l;const p=this._storageIndex.subarray(f,f+4*l);let m=0,y=0;const g=[];for(let e=0;e<l;e++)m=p[4*e+0]*2**32+p[4*e+1],y=m+p[4*e+2]*2**32+p[4*e+3],g.push({from:m,to:y});return{ranges:g,actualTileWidth:i<c-1?s:Math.ceil(a/d)-s*(c-1),actualTileHeight:t<u-1?r:Math.ceil(o/d)-r*(u-1)}}_parseHeader(e){const t=Te(e,"MRF_META/Raster");if(!t)throw new a.Z("mrf:open","not a valid MRF format");const i=Te(t,"Size"),s=parseInt(i.getAttribute("x"),10),r=parseInt(i.getAttribute("y"),10),n=parseInt(i.getAttribute("c"),10),o=(ke(t,"Compression")||"none").toLowerCase();if(!Ve.has(o))throw new a.Z("mrf:open","currently does not support compression "+o);const l=ke(t,"DataType")||"UInt8",c=$e.get(l);if(null==c)throw new a.Z("mrf:open","currently does not support pixel type "+l);const u=Te(t,"PageSize"),h=parseInt(u.getAttribute("x"),10),f=parseInt(u.getAttribute("y"),10),d=Te(t,"DataValues");let p,m;if(d&&(m=d.getAttribute("NoData"),null!=m&&(p=m.trim().split(" ").map((e=>parseFloat(e))))),Te(e,"MRF_META/CachedSource"))throw new a.Z("mrf:open","currently does not support MRF referencing other data files");const y=Te(e,"MRF_META/GeoTags"),g=Te(y,"BoundingBox");let x,I=!1;if(null!=g){const e=parseFloat(g.getAttribute("minx")),t=parseFloat(g.getAttribute("miny")),i=parseFloat(g.getAttribute("maxx")),s=parseFloat(g.getAttribute("maxy")),r=ke(y,"Projection")||"";let n=se.Z.WGS84;if("LOCAL_CS[]"!==r)if(r.toLowerCase().startsWith("epsg:")){const e=Number(r.slice(5));isNaN(e)||0===e||(n=new se.Z({wkid:e}))}else n=Be(r)??se.Z.WGS84;else I=!0,n=new se.Z({wkid:3857});x=new $.Z(e,t,i,s),x.spatialReference=n}else I=!0,x=new $.Z({xmin:-.5,ymin:.5-r,xmax:s-.5,ymax:.5,spatialReference:new se.Z({wkid:3857})});const b=Te(e,"MRF_META/Rsets"),w=parseInt(b&&b.getAttribute("scale")||"2",10),_=x.spatialReference,S=new D.Z({origin:new z.Z({x:x.xmin,y:x.ymax,spatialReference:_}),blockWidth:h,blockHeight:f,pyramidBlockWidth:h,pyramidBlockHeight:f,compression:o,pyramidScalingFactor:w}),R=new z.Z({x:x.width/s,y:x.height/r,spatialReference:_}),v=new de.Z({width:s,height:r,extent:x,isPseudoSpatialReference:I,spatialReference:_,bandCount:n,pixelType:c,pixelSize:R,noDataValue:p,storageInfo:S}),C=ke(e,"datafile"),T=ke(e,"IndexFile");return{rasterInfo:v,files:{mrf:this.url,index:T||this.url.replace(".mrf",".idx"),data:C||this.url.replace(".mrf",Ve.get(o).blobExtension)}}}async _fetchAuxiliaryData(e){try{const{data:t}=await this.request(this.url+".aux.xml",{responseType:"xml",signal:e?.signal});return ze(t)}catch{return null}}};(0,s._)([(0,f.Cb)()],Xe.prototype,"_files",void 0),(0,s._)([(0,f.Cb)()],Xe.prototype,"_storageIndex",void 0),(0,s._)([(0,f.Cb)({type:String,json:{write:!0}})],Xe.prototype,"datasetFormat",void 0),Xe=(0,s._)([(0,y.j)("esri.layers.support.rasterIO.MRFRaster")],Xe);const Ye=Xe;var Ke=i(94793),Qe=i(9832);const et=(e,t)=>e.get(t)?.values,tt=(e,t)=>e.get(t)?.values?.[0];let it=class extends X{constructor(){super(...arguments),this._files=null,this._headerInfo=null,this._bufferSize=1048576,this.datasetFormat="TIFF"}async open(e){await this.init();const t=e?(0,l.Wg)(e.signal):null,{data:i}=await this.request(this.url,{range:{from:0,to:this._bufferSize},responseType:"array-buffer",signal:t});if(!i)throw new a.Z("tiffraster:open","failed to open url "+this.url);this.datasetName=this.url.slice(this.url.lastIndexOf("/")+1,this.url.lastIndexOf("."));const{littleEndian:s,firstIFDPos:r,isBigTiff:n}=(0,Ke.cK)(i),o=[];await this._readIFDs(o,i,s,r,0,n?8:4,t);const{imageInfo:c,rasterInfo:u}=this._parseIFDs(o),h=(0,Ke.ee)(o),f=(0,Ke.I7)(o);if(this._headerInfo={littleEndian:s,isBigTiff:n,ifds:o,pyramidIFDs:h,maskIFDs:f,...c},this._set("rasterInfo",u),!c.isSupported)throw new a.Z("tiffraster:open","this tiff is not supported: "+c.message);if(!c.tileWidth)throw new a.Z("tiffraster:open","none-tiled tiff is not optimized for access, convert to COG and retry.");const{skipExtensions:d=[]}=this.ioConfig;if(!d.includes("aux.xml")){const t=await this._fetchAuxiliaryMetaData(e);null!=t&&this._processPAMInfo(t,u)}d.includes("vat.dbf")||1!==u.bandCount||"u8"!==u.pixelType||(u.attributeTable=await this._fetchAuxiliaryTable(e),(0,l.pC)(u.attributeTable)&&(u.keyProperties.DataType="thematic")),this.updateTileInfo()}async fetchRawTile(e,t,i,s={}){if(!this._headerInfo?.isSupported||this.isBlockOutside(e,t,i))return null;const r=await this._fetchRawTiffTile(e,t,i,!1,s);if((0,l.pC)(r)&&this._headerInfo.hasMaskBand){const n=await this._fetchRawTiffTile(e,t,i,!0,s);(0,l.pC)(n)&&n.pixels[0]instanceof Uint8Array&&(r.mask=n.pixels[0])}return r}_parseIFDs(e){const t=(0,Ke.FI)(e),{width:i,height:s,tileWidth:r,tileHeight:n,planes:a,pixelType:o,compression:l,firstPyramidLevel:c,maximumPyramidLevel:u,pyramidBlockWidth:h,pyramidBlockHeight:f,tileBoundary:d,affine:p,metadata:m}=t;let y=Be(t.extent.spatialReference?.wkt||t.extent.spatialReference?.wkid),g=!!t.isPseudoGeographic;null==y&&(g=!0,y=new se.Z({wkid:3857}));const x=new $.Z({...t.extent,spatialReference:y}),I=new z.Z(x?{x:x.xmin,y:x.ymax,spatialReference:y}:{x:0,y:0}),b=new D.Z({blockWidth:r,blockHeight:n,pyramidBlockWidth:h,pyramidBlockHeight:f,compression:l,origin:I,firstPyramidLevel:c,maximumPyramidLevel:u,blockBoundary:d}),w=new z.Z({x:(x.xmax-x.xmin)/i,y:(x.ymax-x.ymin)/s,spatialReference:y}),_=m?{BandProperties:m.bandProperties,DataType:m.dataType}:{};let S=null;const R=tt(e[0],"PHOTOMETRICINTERPRETATION"),v=et(e[0],"COLORMAP");if(R<=3&&v?.length>3&&v.length%3==0){S=[];const e=v.length/3;for(let t=0;t<e;t++)S.push([t,v[t]>>>8,v[t+e]>>>8,v[t+2*e]>>>8])}const C=new de.Z({width:i,height:s,bandCount:a,pixelType:o,pixelSize:w,storageInfo:b,spatialReference:y,isPseudoSpatialReference:g,keyProperties:_,extent:x,colormap:S,statistics:m?m.statistics:null});return p?.length&&(C.nativeExtent=new $.Z({xmin:-.5,ymin:.5-s,xmax:i-.5,ymax:.5,spatialReference:y}),C.transform=new Oe.Z({polynomialOrder:1,forwardCoefficients:[p[2]+p[0]/2,p[5]-p[3]/2,p[0],p[3],-p[1],-p[4]]}),C.extent=C.transform.forwardTransform(C.nativeExtent),C.pixelSize=new z.Z({x:(x.xmax-x.xmin)/i,y:(x.ymax-x.ymin)/s,spatialReference:y}),b.origin.x=-.5,b.origin.y=.5),{imageInfo:t,rasterInfo:C}}_processPAMInfo(e,t){if(t.statistics=e.statistics??t.statistics,t.histograms=e.histograms,e.histograms&&(0,l.Wi)(t.statistics)&&(t.statistics=(0,Se.Oh)(e.histograms)),e.transform&&(0,l.Wi)(t.transform)){t.transform=e.transform,t.nativeExtent=t.extent;const i=t.transform.forwardTransform(t.nativeExtent);t.pixelSize=new z.Z({x:(i.xmax-i.xmin)/t.width,y:(i.ymax-i.ymin)/t.height,spatialReference:t.spatialReference}),t.extent=i}t.isPseudoSpatialReference&&e.spatialReference&&(t.spatialReference=e.spatialReference)}async _readIFDs(e,t,i,s,r,n=4,a){if(!s)return null;(s>=t.byteLength||s<0)&&(t=(await this.request(this.url,{range:{from:s+r,to:s+r+this._bufferSize},responseType:"array-buffer",signal:a})).data,r=s+r,s=0);const o=await this._readIFD(t,i,s,r,Qe.Z.TIFF_TAGS,n,a);if(e.push(o.ifd),!o.nextIFD)return null;await this._readIFDs(e,t,i,o.nextIFD-r,r,n,a)}async _readIFD(e,t,i,s,r=Qe.Z.TIFF_TAGS,n=4,a){if(!e)return null;const o=(0,Ke.vr)(e,t,i,s,r,n);if(o.success){const i=[];if(o.ifd?.forEach((e=>{e.values||i.push(e)})),i.length>0){const r=i.map((e=>e.offlineOffsetSize)).filter(l.pC),n=Math.min.apply(null,r.map((e=>e[0])));if(Math.min.apply(null,r.map((e=>e[0]+e[1])))-n<=this._bufferSize){const{data:r}=await this.request(this.url,{range:{from:n,to:n+this._bufferSize},responseType:"array-buffer",signal:a});e=r,s=n,i.forEach((i=>(0,Ke.Dq)(e,t,i,s)))}}if(o.ifd?.has("GEOKEYDIRECTORY")){const i=o.ifd.get("GEOKEYDIRECTORY"),r=i?.values;if(r&&r.length>4){const n=r[0]+"."+r[1]+"."+r[2],o=await this._readIFD(e,t,i.valueOffset+6-s,s,Qe.Z.GEO_KEYS,2,a);i.data=o.ifd,i.data&&i.data.set("GEOTIFFVersion",{id:0,type:2,valueCount:1,valueOffset:null,values:[n]})}}return o}if(o.requiredBufferSize&&o.requiredBufferSize!==e.byteLength){const i=await this.request(this.url,{range:{from:s,to:s+o.requiredBufferSize+4},responseType:"array-buffer",signal:a});return(e=i.data).byteLength<o.requiredBufferSize?null:this._readIFD(e,t,0,s,Qe.Z.TIFF_TAGS,4,a)}}async _fetchRawTiffTile(e,t,i,s,r={}){const n=this._getTileLocation(e,t,i,s);if(!n)return null;const{ranges:a,actualTileWidth:o,actualTileHeight:l,ifd:c}=n,u=a.map((e=>this.request(this.url,{range:e,responseType:"array-buffer",signal:r.signal}))),h=await Promise.all(u),f=h.map((e=>e.data.byteLength)).reduce(((e,t)=>e+t)),d=1===h.length?h[0].data:new ArrayBuffer(f),p=[0],m=[0];if(h.length>1){const e=new Uint8Array(d);for(let t=0,i=0;t<h.length;t++){const s=h[t].data;e.set(new Uint8Array(s),i),p[t]=i,i+=s.byteLength,m[t]=s.byteLength}}const{blockWidth:y,blockHeight:g}=this.getBlockWidthHeight(e),x=await this.decodePixelBlock(d,{format:"tiff",customOptions:{headerInfo:this._headerInfo,ifd:c,offsets:p,sizes:m},width:y,height:g,planes:null,pixelType:null});if(null==x)return null;let I,b,w;if(o!==y||l!==g){let e=x.mask;if(e)for(I=0;I<g;I++)if(w=I*y,I<l)for(b=o;b<y;b++)e[w+b]=0;else for(b=0;b<y;b++)e[w+b]=0;else for(e=new Uint8Array(y*g),x.mask=e,I=0;I<l;I++)for(w=I*y,b=0;b<o;b++)e[w+b]=1}return x}_getTileLocation(e,t,i,s=!1){const{firstPyramidLevel:r,blockBoundary:n}=this.rasterInfo.storageInfo,a=0===e?0:e-(r-1),{_headerInfo:o}=this;if(!o)return null;const l=s?o.maskIFDs[a]:0===a?o?.ifds[0]:o?.pyramidIFDs[a-1];if(!l)return null;const c=(0,Ke.If)(l,o),u=et(l,"TILEOFFSETS");if(void 0===u)return null;const h=et(l,"TILEBYTECOUNTS"),{minRow:f,minCol:d,maxRow:p,maxCol:m}=n[a];if(t>p||i>m||t<f||i<d)return null;const y=tt(l,"IMAGEWIDTH"),g=tt(l,"IMAGELENGTH"),x=tt(l,"TILEWIDTH"),I=tt(l,"TILELENGTH"),b=c?this.rasterInfo.bandCount:1,w=b*t*(m+1)+i,_=[{from:u[w],to:u[w+b-1]+h[w+b-1]-1}];if(c){let e=!0;for(let t=0;t<b;t++)if(u[w+t]+h[w+t]!==u[w+t+1]){e=!1;break}if(!e)for(let e=0;e<b;e++)_[e]={from:u[w+e],to:u[w+e]+h[w+e]-1}}const S=u[w],R=h[w];return null==S||null==R?null:{ranges:_,ifd:l,actualTileWidth:i===m&&y%x||x,actualTileHeight:t===p&&g%I||I}}async _fetchAuxiliaryMetaData(e){try{const{data:t}=await this.request(this.url+".aux.xml",{responseType:"xml",signal:e?.signal});return ze(t)}catch{return null}}async _fetchAuxiliaryTable(e){try{const{data:t}=await this.request(this.url+".vat.dbf",{responseType:"array-buffer",signal:e?.signal}),i=ye.parse(t);return i?.recordSet?xe.Z.fromJSON(i.recordSet):null}catch{return null}}};(0,s._)([(0,f.Cb)()],it.prototype,"_files",void 0),(0,s._)([(0,f.Cb)()],it.prototype,"_headerInfo",void 0),(0,s._)([(0,f.Cb)()],it.prototype,"_bufferSize",void 0),(0,s._)([(0,f.Cb)({type:String,json:{write:!0}})],it.prototype,"datasetFormat",void 0),it=(0,s._)([(0,y.j)("esri.layers.support.rasterDatasets.TIFFRaster")],it);const st=it,rt=new Map;rt.set("CRF",{desc:"Cloud Raster Format",constructor:we}),rt.set("MRF",{desc:"Meta Raster Format",constructor:Ye}),rt.set("TIFF",{desc:"GeoTIFF",constructor:st}),rt.set("RasterTileServer",{desc:"Raster Tile Server",constructor:je}),rt.set("JPG",{desc:"JPG Raster Format",constructor:Ne}),rt.set("PNG",{desc:"PNG Raster Format",constructor:Ne}),rt.set("GIF",{desc:"GIF Raster Format",constructor:Ne}),rt.set("BMP",{desc:"BMP Raster Format",constructor:Ne});class nt{static get supportedFormats(){const e=new Set;return rt.forEach(((t,i)=>e.add(i))),e}static async open(e){const{url:t,ioConfig:i,sourceJSON:s}=e;let r=e.datasetFormat;null==r&&t.lastIndexOf(".")&&(r=t.slice(t.lastIndexOf(".")+1).toUpperCase()),"OVR"===r||"TIF"===r?r="TIFF":"JPG"!==r&&"JPEG"!==r&&"JFIF"!==r||(r="JPG"),t.toLowerCase().includes("/imageserver")&&!t.toLowerCase().includes("/wcsserver")&&(r="RasterTileServer");const n={url:t,sourceJSON:s,datasetFormat:r,ioConfig:i??{bandIds:null,sampling:null}};let o,l;if(r&&this.supportedFormats.has(r)){if("CRF"===r&&!i?.enableCRF)throw new a.Z("rasterfactory:open",`cannot open raster: ${t}`);return o=rt.get(r).constructor,l=new o(n),await l.open({signal:e.signal}),l}if(r)throw new a.Z("rasterfactory:open","not a supported format "+r);const c=Array.from(rt.keys());let u=0;const h=()=>(r=c[u++],r&&("CRF"!==r||i?.enableCRF)?(o=rt.get(r).constructor,l=new o(n),l.open({signal:e.signal}).then((()=>l)).catch((()=>h()))):null);return h()}static register(e,t,i){rt.has(e.toUpperCase())||rt.set(e.toUpperCase(),{desc:t,constructor:i})}}var at=i(32163);let ot=class extends((0,I.h)((0,ce.M)((0,ae.q)((0,oe.I)((0,b.N)(ne((0,ue.n)((0,x.Y)((0,le.Q)((0,c.R)(g.Z))))))))))){constructor(...e){super(...e),this._primaryRasters=null,this.bandIds=null,this.interpolation=null,this.legendEnabled=!0,this.isReference=null,this.listMode="show",this.sourceJSON=null,this.version=null,this.type="imagery-tile",this.operationalLayerType="ArcGISTiledImageServiceLayer",this.popupEnabled=!0,this.popupTemplate=null,this.fields=null}normalizeCtorArgs(e,t){return"string"==typeof e?{url:e,...t}:e}load(e){const t=(0,l.pC)(e)?e.signal:null;return this.addResolvingPromise(this.loadFromPortal({supportedTypes:["Image Service"]},e).catch(u.r9).then((()=>this._openRaster(t)))),Promise.resolve(this)}get defaultPopupTemplate(){return this.createPopupTemplate()}get rasterFields(){let e=[new he.Z({name:"Raster.ServicePixelValue",alias:"Pixel Value",domain:null,editable:!1,length:50,type:"string"})];const{rasterInfo:t}=this,i=t?.attributeTable,s=(0,l.pC)(i)?i.fields:null;if(s){const t=s.filter((e=>"oid"!==e.type&&"value"!==e.name.toLowerCase())).map((e=>{const t=e.clone();return t.name="Raster."+e.name,t}));e=e.concat(t)}const r=t?.dataType,n=t?.multidimensionalInfo;if(("vector-magdir"===r||"vector-uv"===r)&&(0,l.pC)(n)){const t=n.variables[0].unit?.trim(),i="Magnitude"+(t?` (${t})`:"");e.push(new he.Z({name:"Raster.Magnitude",alias:i,domain:null,editable:!1,type:"double"})),e.push(new he.Z({name:"Raster.Direction",alias:"Direction (°)",domain:null,editable:!1,type:"double"}))}return e}set renderer(e){this._set("renderer",e),this.updateRenderer()}readRenderer(e,t,i){const s=t&&t.layerDefinition&&t.layerDefinition.drawingInfo&&t.layerDefinition.drawingInfo.renderer,r=(0,n.ij)(s,i)||void 0;if(null!=r)return r}createPopupTemplate(e){return(0,at.eZ)({fields:this.rasterFields,title:this.title},e)}async generateRasterInfo(e,t){if(!(e=(0,d.TJ)(T.Z,e)))return this._primaryRasters[0].rasterInfo;try{const i={raster:this._primaryRasters[0]};this._primaryRasters.length>1&&this._primaryRasters.forEach((e=>i[e.url]=e));const s=(0,Q.Ue)(e.toJSON(),i),r=new K({rasterFunction:s});return await r.open(t),r.rasterInfo}catch{return null}}write(e,t){const{raster:i}=this;if(this.loaded?"RasterTileServer"===i.datasetFormat&&("Raster"===i.tileType||"Map"===i.tileType):this.url&&/\/ImageServer(\/|\/?$)/i.test(this.url))return super.write(e,t);if(t&&t.messages){const e=`${t.origin}/${t.layerContainerType||"operational-layers"}`;t.messages.push(new a.Z("layer:unsupported",`Layers (${this.title}, ${this.id}) of type '${this.declaredClass}' are not supported in the context of '${e}'`,{layer:this}))}return null}async _openRaster(e){let t=!1;if(this.raster)this.raster.rasterInfo||await this.raster.open(),"Function"===this.raster.datasetFormat?(t=!0,this._primaryRasters=this.raster.primaryRasters.rasters):this._primaryRasters=[this.raster],this.url=this.raster.url;else{const{rasterFunction:t}=this,i=[this.url];t&&(0,Q.G8)(t.toJSON(),i);const s=await Promise.all(i.map((t=>nt.open({url:t,sourceJSON:this.sourceJSON,ioConfig:{sampling:"closest",...this.ioConfig,customFetchParameters:this.customParameters},signal:e})))),r=s.findIndex((e=>null==e));if(r>-1)throw new a.Z("imagery-tile-layer:open",`cannot open raster: ${i[r]}`);if(this._primaryRasters=s,t){const e={raster:this._primaryRasters[0]};this._primaryRasters.length>1&&this._primaryRasters.forEach((t=>e[t.url]=t));const i=(0,Q.Ue)(t.rasterFunctionDefinition??t.toJSON(),e),r=new K({rasterFunction:i});try{await r.open(),this.raster=r}catch(e){const t=o.Z.getLogger(this.declaredClass);e instanceof a.Z&&t.error("imagery-tile-layer:open",e.message),t.warn("imagery-tile-layer:open","the raster function cannot be applied and is removed"),this._set("rasterFunction",null),this.raster=s[0]}}else this.raster=s[0]}const i=this.raster.rasterInfo;if(!i)throw new a.Z("imagery-tile-layer:load","cannot load resources on "+this.url);if(this._set("rasterInfo",t?i:this._primaryRasters[0].rasterInfo),this._set("spatialReference",i.spatialReference),this.sourceJSON=this.sourceJSON||this.raster.sourceJSON,null!=this.sourceJSON){const e="Map"===this.raster.tileType&&null!=this.sourceJSON.minLOD&&null!=this.sourceJSON.maxLOD?this.sourceJSON:{...this.sourceJSON,minScale:0,maxScale:0};this.read(e,{origin:"service"})}this.title||(this.title=this.raster.datasetName),"Map"===this.raster.tileType&&(this.popupEnabled=!1),this._configDefaultSettings(),this.addHandles((0,h.YP)((()=>this.customParameters),(e=>{this.raster&&(this.raster.ioConfig.customFetchParameters=e)})))}};(0,s._)([(0,f.Cb)()],ot.prototype,"_primaryRasters",void 0),(0,s._)([(0,f.Cb)({type:[d.z8],json:{write:{overridePolicy(){return{enabled:!this.loaded||"Raster"===this.raster.tileType||"0,1,2"!==this.bandIds?.join(",")}}}}})],ot.prototype,"bandIds",void 0),(0,s._)([(0,f.Cb)({json:{write:{overridePolicy(){return{enabled:!this.loaded||"Raster"===this.raster.tileType||"bilinear"!==this.interpolation}}}}}),(0,p.J)(fe.c)],ot.prototype,"interpolation",void 0),(0,s._)([(0,f.Cb)(R.rn)],ot.prototype,"legendEnabled",void 0),(0,s._)([(0,f.Cb)({type:Boolean,json:{read:!1,write:{enabled:!0,overridePolicy:()=>({enabled:!1})}}})],ot.prototype,"isReference",void 0),(0,s._)([(0,f.Cb)({type:["show","hide"]})],ot.prototype,"listMode",void 0),(0,s._)([(0,f.Cb)({json:{read:!0,write:!0}})],ot.prototype,"blendMode",void 0),(0,s._)([(0,f.Cb)()],ot.prototype,"sourceJSON",void 0),(0,s._)([(0,f.Cb)({readOnly:!0,json:{origins:{service:{read:{source:"currentVersion"}}}}})],ot.prototype,"version",void 0),(0,s._)([(0,f.Cb)({readOnly:!0,json:{read:!1}})],ot.prototype,"type",void 0),(0,s._)([(0,f.Cb)({type:["ArcGISTiledImageServiceLayer"]})],ot.prototype,"operationalLayerType",void 0),(0,s._)([(0,f.Cb)({type:Boolean,value:!0,json:{read:{source:"disablePopup",reader:(e,t)=>!t.disablePopup},write:{target:"disablePopup",overridePolicy(){return{enabled:!this.loaded||"Raster"===this.raster.tileType}},writer(e,t,i){t[i]=!e}}}})],ot.prototype,"popupEnabled",void 0),(0,s._)([(0,f.Cb)({type:r.Z,json:{read:{source:"popupInfo"},write:{target:"popupInfo",overridePolicy(){return{enabled:!this.loaded||"Raster"===this.raster.tileType}}}}})],ot.prototype,"popupTemplate",void 0),(0,s._)([(0,f.Cb)({readOnly:!0})],ot.prototype,"defaultPopupTemplate",null),(0,s._)([(0,f.Cb)({readOnly:!0,type:[he.Z]})],ot.prototype,"fields",void 0),(0,s._)([(0,f.Cb)({readOnly:!0,type:[he.Z]})],ot.prototype,"rasterFields",null),(0,s._)([(0,f.Cb)({types:n.dr,json:{name:"layerDefinition.drawingInfo.renderer",write:{overridePolicy(){const e="raster-stretch"===this.renderer?.type&&"none"===this.renderer.stretchType&&!this.renderer.useGamma;return{enabled:!this.loaded||"Raster"===this.raster.tileType||!e}}},origins:{"web-scene":{types:n.FK,name:"layerDefinition.drawingInfo.renderer",write:{overridePolicy:e=>({enabled:e&&"vector-field"!==e.type&&"flow"!==e.type})}}}}})],ot.prototype,"renderer",null),(0,s._)([(0,m.r)("renderer")],ot.prototype,"readRenderer",null),ot=(0,s._)([(0,y.j)("esri.layers.ImageryTileLayer")],ot);const lt=ot},11282:(e,t,i)=>{i.d(t,{cv:()=>o,en:()=>a,lA:()=>n}),i(68773),i(40330);var s=i(22974),r=i(17452);function n(e,t){return t?{...t,query:{...e??{},...t.query}}:{query:e}}function a(e){return"string"==typeof e?(0,r.mN)(e):(0,s.d9)(e)}function o(e,t,i){const s={};for(const r in e){if("declaredClass"===r)continue;const n=e[r];if(null!=n&&"function"!=typeof n)if(Array.isArray(n)){s[r]=[];for(let e=0;e<n.length;e++)s[r][e]=o(n[e])}else if("object"==typeof n)if(n.toJSON){const e=n.toJSON(i&&i[r]);s[r]=t?e:JSON.stringify(e)}else s[r]=t?n:JSON.stringify(n);else s[r]=n}return s}i(71058)}}]);