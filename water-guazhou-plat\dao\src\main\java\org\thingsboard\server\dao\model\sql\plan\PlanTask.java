package org.thingsboard.server.dao.model.sql.plan;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Getter;
import lombok.Setter;
import org.joda.time.DateTime;
import org.thingsboard.server.dao.model.sql.smartProduction.circuit.GeneralTaskStatus;
import org.thingsboard.server.dao.sql.plan.PlanTaskMapper;
import org.thingsboard.server.dao.util.imodel.response.annotations.*;

import java.util.Date;

@Getter
@Setter
@ResponseEntity
public class PlanTask {
    // id
    @TableId(type = IdType.ASSIGN_UUID)
    @InfoViaMapper(name = "isRight", mapper = PlanTaskMapper.class)
    private String id;

    // 任务编号
    private String code;

    // 任务名称
    private String name;

    // 盘点类型
    private String executionType;

    // 盘点目标仓库ID
    private String storehouseId;

    // 盘点目标仓库名称
    private String storehouseName;

    // 盘点人ID
    @ParseUsername(withDepartment = true)
    private String executionUserId;

    // 开始日期
    private Date startTime;

    // 结束日期
    private Date endTime;

    // 实际盘点开始时间
    private Date realStartTime;

    // 实际盘点结束时间
    private Date realEndTime;

    // 创建人
    @ParseUsername
    private String creator;

    // 创建时间
    private Date createTime;

    // 任务类型。临时任务/计划任务
    private String type;

    // 任务状态
    private GeneralTaskStatus status;

    // 盘点结果
    private String result;

    // 备注
    private String remark;

    // 租户ID
    @ParseTenantName
    private String tenantId;

    // 是否超期
    @TableField(exist = false)
    @Compute("isOverdue")
    private Boolean isOverdue;

    @SuppressWarnings("unused")
    private Boolean isOverdue() {
        return realEndTime == null && new DateTime(endTime).isBeforeNow();
    }
}
