/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.maintain;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.id.DeviceId;
import org.thingsboard.server.common.data.id.MaintainId;
import org.thingsboard.server.common.data.id.MaintainRecordId;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.maintain.Maintain;
import org.thingsboard.server.common.data.maintain.MaintainRecord;

import java.util.List;

@Slf4j
@Service
@Transactional
public class MaintainRecordServiceImpl implements MaintainRecordService {

    @Autowired
    private MaintainRecordDao maintainRecordDao;

    @Override
    public MaintainRecord addMaintainRecord(MaintainRecord maintainRecord) {
        if (maintainRecord.getCreateTime() == null)
            maintainRecord.setCreateTime(System.currentTimeMillis());
        return maintainRecordDao.save(maintainRecord);
    }

    @Override
    public List<MaintainRecord> findByTenantId(TenantId tenantId) {
        return maintainRecordDao.findByTenantId(tenantId);
    }

    @Override
    public MaintainRecord findById(MaintainRecordId maintainRecordId) {
        return maintainRecordDao.findById(maintainRecordId.getId());
    }

    @Override
    public List<MaintainRecord> findByStatus(MaintainId maintainId, String status) {
        return maintainRecordDao.findByStatus(maintainId, status);
    }

    @Override
    public List<MaintainRecord> findByMaintainId(MaintainId maintainId) {
        return maintainRecordDao.findByMaintainId(maintainId);
    }

    @Override
    public List<MaintainRecord> findByDeviceId(DeviceId deviceId) {
        return maintainRecordDao.findByDeviceId(deviceId);
    }

    @Override
    public List<MaintainRecord> findByTenantIdAndStatus(TenantId tenantId, String status) {
        return maintainRecordDao.findByTenantIdAndStatus(tenantId,status);
    }


}
