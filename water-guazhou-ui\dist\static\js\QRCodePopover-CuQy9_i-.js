import{d as c,g as l,h as p,F as e,q as t,G as d,J as _,d8 as i,C as u}from"./index-r0dFAfgr.js";import{Q as f}from"./qrcode.vue.esm-DGEiBAmZ.js";const m=c({__name:"QRCodePopover",props:{row:{}},setup(r){const n=r;return(v,o)=>{const a=_,s=i;return l(),p(s,{key:0,trigger:"click",placement:"left",width:"100px"},{reference:e(()=>[t(a,{class:"iconfont icon-erweima1 operation-btn",text:!0,size:"small",style:{color:"#09a3cd",border:"none"}},{default:e(()=>o[0]||(o[0]=[d(" 二维码 ")])),_:1})]),default:e(()=>[t(f,{value:n.row.id,size:100,level:"H"},null,8,["value"])]),_:1})}}}),C=u(m,[["__scopeId","data-v-641e7f85"]]);export{C as default};
