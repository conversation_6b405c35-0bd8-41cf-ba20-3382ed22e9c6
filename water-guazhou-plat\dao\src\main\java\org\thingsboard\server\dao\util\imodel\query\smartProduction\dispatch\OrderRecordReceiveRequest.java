package org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartProduction.dispatch.OrderRecordStatus;
import org.thingsboard.server.dao.util.imodel.query.Requestible;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

import java.util.List;

@Getter
@Setter
public class OrderRecordReceiveRequest implements Requestible {
    // 指令id列表
    private List<String> idList;

    // 接收人
    @NotNullOrEmpty
    private String receiveUserId;

    public OrderRecordStatus getStatus() {
        return OrderRecordStatus.WAITING_REPLY;
    }
}
