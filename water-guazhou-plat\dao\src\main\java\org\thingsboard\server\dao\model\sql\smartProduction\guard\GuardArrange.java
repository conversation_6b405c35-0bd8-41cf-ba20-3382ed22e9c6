package org.thingsboard.server.dao.model.sql.smartProduction.guard;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseUsername;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.util.Date;

@Getter
@Setter
@ResponseEntity
@TableName("guard_arrange")
public class GuardArrange {
    public static final String TEMP_GUARD = "临时值班";

    // 排班id
    private String id;

    // 地点id
    private String placeId;

    // 地点名称
    @TableField(exist = false)
    private String placeName;

    // 班次id
    private String classId;

    // 班次名称
    private String className;

    // 值班日期
    private Date dayTime;

    // 开始时间
    private String beginTime;

    // 结束时间
    private String endTime;

    // 班组id
    private String groupId;

    // 班组名称
    private String groupName;

    // 班组部门id
    private String departmentId;

    // 值班长id
    private String head;

    @TableField(exist = false)
    private String headName;

    // 修改人id
    @ParseUsername
    private String updateUserId;

    // 修改时间
    private Date updateTime;

    // 创建人id
    @ParseUsername
    private String creator;

    // 创建时间
    private Date createTime;

    // 租户id
    private String tenantId;

    // 用、隔开的成员名称
    @TableField(exist = false)
    private String partnerNames;

}
