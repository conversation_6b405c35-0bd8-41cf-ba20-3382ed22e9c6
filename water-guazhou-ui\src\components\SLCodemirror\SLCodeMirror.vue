<template>
  <Codemirror
    v-model="value"
    placeholder="请输入"
    :style="{ height: '100%', width: '100%', fontSize: '12px' }"
    :autofocus="true"
    :indent-with-tab="true"
    :tab-size="1"
    :extensions="extensions"
    @ready="print('ready', $event)"
    @change="print('change', $event)"
    @focus="print('focus', $event)"
    @blur="print('blur', $event)"
  />
</template>

<script lang="ts" setup>
import { Codemirror } from 'vue-codeMirror';
import { javascript } from '@codemirror/lang-javascript';
import { json } from '@codemirror/lang-json';
import { sql } from '@codemirror/lang-sql';
import { oneDark } from '@codemirror/theme-one-dark';

const props = defineProps<{
  modelValue: string | any[];
  type: 'list' | 'string' | 'object';
  language: 'javascript' | 'sql' | 'json';
}>();

const emits = defineEmits(['update:modelValue']);

// 内容 HTML
const value = computed({
  get: () => {
    if (Array.isArray(props.modelValue))
      return JSON.stringify(props.modelValue);
    return props.modelValue;
  },
  set: (nv) => {
    const value: any =
      props.type === 'list' ? JSON.parse(nv.replaceAll('\n', '')) || [] : nv;
    emits('update:modelValue', value);
  }
});

const extensions: any = [setLanguage(props.language), oneDark];

function setLanguage(val) {
  switch (val) {
    case 'sql':
      return sql();
    case 'json':
      return json();
    default:
      return javascript();
  }
}

function print(type: string, params: any) {
  console.log(type, params);
}
</script>
