package org.thingsboard.server.dao.maintain;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.smartManagement.maintaince.SMMaintainTaskItem;
import org.thingsboard.server.dao.sql.smartManagement.maintain.SMMaintainTaskItemMapper;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.maintain.SMMaintainTaskItemPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.maintain.SMMaintainTaskItemReportRequest;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.maintain.SMMaintainTaskItemSaveRequest;

import java.util.List;

import static org.thingsboard.server.dao.util.imodel.query.QueryUtil.*;

@Service
public class SMMaintainTaskItemServiceImpl implements SMMaintainTaskItemService {
    @Autowired
    private SMMaintainTaskItemMapper mapper;

    @Override
    public IPage<SMMaintainTaskItem> findAllConditional(SMMaintainTaskItemPageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    public SMMaintainTaskItem save(SMMaintainTaskItemSaveRequest entity) {
        return saveOrUpdateOneByRequest(entity, mapper);
    }

    @Override
    public boolean update(SMMaintainTaskItem entity) {
        return mapper.update(entity);
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }

    @Override
    public List<SMMaintainTaskItem> saveAll(List<SMMaintainTaskItemSaveRequest> items) {
        disallowUpdate(items);
        return saveBatchByRequest(items, mapper::saveAll);
    }

    @Override
    public boolean report(SMMaintainTaskItemReportRequest req) {
        return mapper.report(req);
    }

    @Override
    public boolean removeAllByTaskId(String id) {
        return mapper.removeAllByTaskId(id) > 0;
    }
}
