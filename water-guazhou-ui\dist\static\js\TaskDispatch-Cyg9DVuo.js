import{_ as F}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{d as L,c as b,r as c,s,l as I,bE as T,b as m,S,bB as W,o as M,g as q,n as B,q as d,F as D,p as h,i as _,aB as U,aq as R,al as V,b7 as j,bq as x,da as A,X as E,C as G}from"./index-r0dFAfgr.js";import{_ as z}from"./Search-NSrhrIa_.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import{a as X}from"./LayerHelper-Cn-iiqxI.js";import"./project-DUuzYgGl.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";import{g as $,h as H,i as J}from"./plan-BLf3nu6_.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import K from"./RightDrawerMap-D5PhmGFO.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./pipe-nogVzCHG.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./ArcView-DpMnCY82.js";import"./ViewHelper-BGCZjxXH.js";import"./fieldconfig-Bk3o1wi7.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./ArcStationWarning-BF9YrSzF.js";/* empty css                         */import"./useWaterPoint-Bv0z6ym6.js";import"./DateFormatter-Bm9a68Ax.js";import"./zhandian-YaGuQZe6.js";import"./useStation-DJgnSZIA.js";import"./DrawerBox-CLde5xC8.js";import"./SideDrawer-CBntChyn.js";import"./PipeDetail-CTBPYFJW.js";import"./FormTableColumnFilter-BT7pLXIC.js";import"./QueryHelper-ILO3qZqg.js";import"./config-fy91bijz.js";import"./ArcBRTools-BO92yznB.js";import"./PipeLength.vue_vue_type_script_setup_true_lang-BZfUsvDf.js";import"./arcWidgetButton-0glIxrt7.js";import"./StatisticsHelper-D-s_6AyQ.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./wfsUtils-DXofo3da.js";import"./usePipelineGroup-Ba1pmWz_.js";import"./GroupLayer-DhhN3FyN.js";import"./lazyLayerLoader-DbM9sT1W.js";import"./WFSLayer-1TtJp3nx.js";import"./clientSideDefaults-VQhQaYxh.js";import"./QueryEngineCapabilities-Dk3NJwmm.js";import"./wfsUtils-Du031XaC.js";import"./geojson-MBFu2-HZ.js";import"./xmlUtils-CtUoQO7q.js";import"./ListWindow-WS05QqV0.js";import"./PopLayout-BP55MvL7.js";import"./PoiSearchV2-D7yNeLuv.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./URLHelper-B9aplt5w.js";import"./useLayerList-DmEwJ-ws.js";import"./useScaleBar-Beed-z91.js";const O={class:"detail-wrapper"},Q={class:"detail-table"},Y=L({__name:"TaskDispatch",setup(Z){const f=b(),u=b(),n=b(),y={},g=c({tabs:[],loading:!1,layerIds:[],layerInfos:[],planPeriod:[]}),C=c({scrollBarGradientColor:"#fafafa",filters:[{type:"datetimerange",field:"date",label:"创建时间"},{type:"input",field:"keyword",clearable:!1,label:"快速查找"}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",svgIcon:s(V),click:()=>p()},{perm:!0,type:"default",text:"重置",svgIcon:s(j),click:()=>{var e;(e=f.value)==null||e.resetForm(),p()}},{perm:!0,type:"danger",text:"批量删除",disabled:()=>{var e;return!((e=a.selectList)!=null&&e.length)},svgIcon:s(x),click:()=>k()}]}],defaultParams:{date:[I().subtract(6,"M").format(T),I().format(T)]}}),a=c({dataList:[],handleSelectChange:e=>a.selectList=e||[],columns:[{minWidth:120,align:"center",label:"是否常规计划",prop:"isNormalPlan",tag:!0,tagColor:e=>e.isNormalPlan?"#409eff":"#0cbb4a",formatter:e=>e.isNormalPlan?"常规":"临时"},{minWidth:120,align:"center",label:"是否需要反馈",prop:"isNeedFeedback",tag:!0,tagColor:e=>e.isNeedFeedback===!1?"#0cbb4a":"#409eff",formatter:e=>e.isNeedFeedback===!1?"仅到位":"需要"},{minWidth:120,label:"计划名称",prop:"name"},{minWidth:120,label:"区域/路线名称",prop:"districtAreaName"},{minWidth:120,label:"计划周期",prop:"planCircleName"},{minWidth:120,label:"巡检方式",prop:"moveType"},{minWidth:120,label:"创建人",prop:"creatorName"},{minWidth:120,label:"创建时间",prop:"createTime"}],pagination:{refreshData:({page:e,size:t})=>{a.pagination.page=e||1,a.pagination.limit=t||20,p()}},operationWidth:140,operations:[{perm:!0,text:"任务制定",svgIcon:s(A),click:e=>{a.currentRow=e,N()}},{perm:!0,type:"danger",text:"删除",svgIcon:s(x),click:e=>k(e)}]}),k=e=>{var r;const t=e?[e.id]:((r=a.selectList)==null?void 0:r.map(i=>i.id))||[];if(!t.length){m.warning("请先选择要删除的数据");return}S("确定删除?","提示信息").then(()=>{$(t).then(i=>{i.data.code===200?(m.success(i.data.message),p()):m.error(i.data.message)}).catch(()=>{m.error("系统错误")})}).catch(()=>{})},N=async()=>{var t,r;const e=a.currentRow;v.defaultValue={planName:e==null?void 0:e.name,planId:e==null?void 0:e.id},(t=n.value)==null||t.openDialog(),await W(),(r=n.value)==null||r.resetForm()},p=()=>{var o;const{keyword:e,date:t}=((o=f.value)==null?void 0:o.queryParams)||{},[r,i]=t||[];H({page:a.pagination.page||1,size:a.pagination.limit||20,keyword:e,fromTime:r,toTime:i}).then(l=>{a.dataList=l.data.data.data||[],a.pagination.total=l.data.data.total||0})},v=c({title:"任务制定",dialogWidth:500,labelPosition:"right",group:[{fields:[{type:"input",label:"计划名称",readonly:!0,field:"planName"},{type:"input",label:"任务名称",field:"name",rules:[{required:!0,message:"请输入任务名称"}]},{type:"user-select",label:"接收人员",field:"receiveUserId",rules:[{required:!0,message:"请选择接收人员"}]},{type:"datetimerange",label:"起止时间",field:"beginTime",rules:[{required:!0,message:"请选择起止时间"}]},{type:"user-select",label:"共同完成人",multiple:!0,field:"collaborateUserId"},{type:"input-number",label:"到位距离",append:"米",field:"presentDistance"},{type:"textarea",label:"任务描述",field:"remark"}]}],submit:e=>{var r,i;const t={...e,beginTime:e.beginTime&&e.beginTime[0],endTime:e.beginTime&&e.beginTime[1],planId:a.currentRow.id,collaborateUserId:(r=e.collaborateUserId)==null?void 0:r.join(","),receiveUserId:(i=e.receiveUserId)==null?void 0:i.join(",")};J(t).then(o=>{var l;o.data.code===200?(m.success(o.data.message),(l=n.value)==null||l.closeDialog()):m.error(o.data.message)}).catch(()=>{m.error("系统错误")})},defaultValue:{presentDistance:0}}),P=async()=>{var t,r;g.layerIds=X(y.view);const e=await E(g.layerIds);g.layerInfos=((r=(t=e.data)==null?void 0:t.result)==null?void 0:r.rows)||[]},w=async e=>{var t,r;y.view=e,(t=u.value)==null||t.toggleCustomDetail(!0),(r=u.value)==null||r.toggleCustomDetailMaxmin("max"),await P()};return M(async()=>{p()}),(e,t)=>{const r=z,i=R,o=F;return q(),B(U,null,[d(K,{ref_key:"refMap",ref:u,title:"任务制定","detail-max-min":!0,"hide-right-drawer":!0,"hide-detail-close":!0,onMapLoaded:w},{"detail-header":D(()=>t[0]||(t[0]=[h("span",null,"任务制定",-1)])),"detail-default":D(()=>[h("div",O,[d(r,{ref_key:"refSearch",ref:f,config:_(C),style:{"margin-bottom":"8px"}},null,8,["config"]),h("div",Q,[d(i,{config:_(a)},null,8,["config"])])])]),_:1},512),d(o,{ref_key:"refDialogForm",ref:n,config:_(v)},null,8,["config"])],64)}}}),ir=G(Y,[["__scopeId","data-v-3217e32d"]]);export{ir as default};
