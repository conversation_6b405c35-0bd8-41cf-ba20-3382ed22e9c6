import { ref, reactive } from 'vue'
import { getPumpStationMonitorData, getPumpStationDetail } from '@/api/waterMonitoring/pumpStation'
import { ElMessage } from 'element-plus'

export default function usePumpStationData() {
  const loading = ref(false)
  const queryParams = reactive({
    stationIds: [],
    timeGranularity: 'day',
    startTime: Date.now() - 24 * 60 * 60 * 1000,
    endTime: Date.now(),
    pumpType: 'electric'
  })
  
  // 图表数据
  const pumpWaterData = ref([])
  const processWaterData = ref([])
  const trendData = ref([])
  const stationDetailData = ref([])
  
  // 获取监控数据
  const getMonitorData = async () => {
    loading.value = true
    try {
      const res = await getPumpStationMonitorData(queryParams)
      if (res.data && res.data.data) {
        const data = res.data.data
        pumpWaterData.value = data.pumpWaterData || []
        processWaterData.value = data.processWaterData || []
        trendData.value = data.trendData || []
      }
    } catch (error) {
      console.error('获取监控数据失败:', error)
      ElMessage.error('获取监控数据失败')
    } finally {
      loading.value = false
    }
  }
  
  // 获取泵站详情
  const getStationDetail = async () => {
    try {
      const res = await getPumpStationDetail({
        stationIds: queryParams.stationIds
      })
      if (res.data && res.data.data) {
        stationDetailData.value = res.data.data || []
      }
    } catch (error) {
      console.error('获取泵站详情失败:', error)
      ElMessage.error('获取泵站详情失败')
    }
  }
  
  // 更新查询参数
  const updateQueryParams = (params) => {
    Object.assign(queryParams, params)
  }
  
  // 重置查询参数
  const resetQueryParams = () => {
    queryParams.stationIds = []
    queryParams.timeGranularity = 'day'
    queryParams.startTime = Date.now() - 24 * 60 * 60 * 1000
    queryParams.endTime = Date.now()
    queryParams.pumpType = 'electric'
  }
  
  return {
    loading,
    queryParams,
    pumpWaterData,
    processWaterData,
    trendData,
    stationDetailData,
    getMonitorData,
    getStationDetail,
    updateQueryParams,
    resetQueryParams
  }
}
