var e;(function(E){E.POSITION="position",E.NORMAL="normal",E.UV0="uv0",E.AUXPOS1="auxpos1",E.AUXPOS2="auxpos2",E.COLOR="color",E.SYMBOLCOLOR="symbolColor",E.SIZE="size",E.TANGENT="tangent",E.OFFSET="offset",E.SUBDIVISIONFACTOR="subdivisionFactor",E.COLORFEATUREATTRIBUTE="colorFeatureAttribute",E.SIZEFEATUREATTRIBUTE="sizeFeatureAttribute",E.OPACITYFEATUREATTRIBUTE="opacityFeatureAttribute",E.DISTANCETOSTART="distanceToStart",E.UVMAPSPACE="uvMapSpace",E.BOUNDINGRECT="boundingRect",E.UVREGION="uvRegion",E.NORMALCOMPRESSED="normalCompressed",E.PROFILERIGHT="profileRight",E.PROFILEUP="profileUp",E.PROFILEVERTEXANDNORMAL="profileVertexAndNormal",E.FEATUREVALUE="featureValue",E.MODELORIGINHI="modelOriginHi",E.MODELORIGINLO="modelOriginLo",E.MODEL="model",E.MODELNORMAL="modelNormal",E.INSTANCECOLOR="instanceColor",E.INSTANCEFEATUREATTRIBUTE="instanceFeatureAttribute",E.LOCALTRANSFORM="localTransform",E.GLOBALTRANSFORM="globalTransform",E.BOUNDINGSPHERE="boundingSphere",E.MODELORIGIN="modelOrigin",E.MODELSCALEFACTORS="modelScaleFactors",E.FEATUREATTRIBUTE="featureAttribute",E.STATE="state",E.LODLEVEL="lodLevel",E.POSITION0="position0",E.POSITION1="position1",E.NORMALA="normalA",E.NORMALB="normalB",E.COMPONENTINDEX="componentIndex",E.VARIANTOFFSET="variantOffset",E.VARIANTSTROKE="variantStroke",E.VARIANTEXTENSION="variantExtension",E.U8PADDING="u8padding",E.U16PADDING="u16padding",E.SIDENESS="sideness",E.START="start",E.END="end",E.UP="up",E.EXTRUDE="extrude",E.OBJECTANDLAYERIDCOLOR="objectAndLayerIdColor",E.OBJECTANDLAYERIDCOLOR_INSTANCED="objectAndLayerIdColor_instanced"})(e||(e={}));export{e as O};
