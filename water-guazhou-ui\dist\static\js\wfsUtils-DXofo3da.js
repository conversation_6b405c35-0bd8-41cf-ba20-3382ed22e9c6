import{r as m}from"./AnimatedLinesLayer-B2VbV4jv.js";import"./Point-WxyopZva.js";import"./index-r0dFAfgr.js";import"./MapView-DaoQedLH.js";import"./widget-BcWKanF2.js";import"./pe-B8dP0-Ut.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./TileLayer-B5vQ99gG.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./commonProperties-DqNQ4F00.js";import"./project-DUuzYgGl.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";const u="/geoserver/guazhou/wfs",$t=(t,r,o)=>{let a="1=1";r&&(a=`intersects(geom,POLYGON((${r.map(e=>`${e[0]} ${e[1]}`).join(",")})))`);let s=`${u}?SERVICE=WFS&VERSION=2.0.0&REQUEST=GetFeature&typeName=${t}&outputFormat=application/json&SRS=EPSG:3857&CQL_FILTER=${a}`;return o&&(s+=` AND (${o})`),m({url:s,method:"get"})},Nt=t=>{const r=`${u}?SERVICE=WFS&VERSION=1.0.0&REQUEST=DescribeFeatureType&typeName=${t}&outputFormat=application/json`;return m({url:r,method:"get"})},Rt=t=>m({url:`/geoserver/ows?service=WFS&version=1.1.0&request=GetFeature&typeName=${t.layerName}&propertyName=${t.fiedName}&outputFormat=application/json`,method:"get"}),ft=async(t,r,o,a,s)=>{const e=`${u}?SERVICE=WFS&VERSION=2.0.0&REQUEST=GetFeature&typeName=${t}&outputFormat=application/json&SRS=EPSG:3857&CQL_FILTER=1=1`,c=await m({url:e,method:"get"}),n=new Map;return(c.data.features||[]).forEach(i=>{const p=i.properties[r]??"未知",E=Number(i.properties[o])||0;n.set(p,(n.get(p)||0)+E)}),Array.from(n.entries()).map(([i,p])=>({name:i,value:p}))};export{Nt as GetFieldConfig,Rt as GetFieldValueByGeoserver,$t as QueryByPolygon,ft as groupLengthStatisticsByField};
