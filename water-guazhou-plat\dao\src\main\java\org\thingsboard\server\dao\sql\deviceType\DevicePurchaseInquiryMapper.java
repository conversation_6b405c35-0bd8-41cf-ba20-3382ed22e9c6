package org.thingsboard.server.dao.sql.deviceType;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.purchase.DevicePurchaseInquiry;
import org.thingsboard.server.dao.util.imodel.query.device.DevicePurchaseInquiryPageRequest;
import org.thingsboard.server.dao.util.imodel.response.device.DevicePurchaseInquiryItemResponse;

import java.util.List;

@Mapper
public interface DevicePurchaseInquiryMapper extends BaseMapper<DevicePurchaseInquiry> {
    IPage<DevicePurchaseInquiryItemResponse> findByPage(DevicePurchaseInquiryPageRequest request);

    boolean update(DevicePurchaseInquiry entity);

    int updateAll(List<DevicePurchaseInquiry> list);

    int saveAll(List<DevicePurchaseInquiry> list);

    int deleteByPurchaseItemMainId(@Param("mainId") String mainId, @Param("idList") List<String> idList);

    int deleteBatchByPurchaseItemId(List<String> remove);

    boolean deleteByPurchaseItemId(String id);

    DevicePurchaseInquiry inquiry(DevicePurchaseInquiry entity);

    int removeAllByMainId(String mainId);

    int deleteByPurchaseItemMainIdOnIdNotIn(@Param("mainId") String mainId, @Param("idList") List<String> idList);


}
