package org.thingsboard.server.dao.base.impl;

import java.util.List;
import java.util.UUID;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.base.IBaseMessageConfigurationService;
import org.thingsboard.server.dao.model.sql.base.BaseMessageConfiguration;
import org.thingsboard.server.dao.sql.base.BaseMessageConfigurationMapper;
import org.thingsboard.server.dao.util.imodel.query.base.BaseMessageConfigurationPageRequest;

/**
 * 平台管理-消息配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
@Service
public class BaseMessageConfigurationServiceImpl implements IBaseMessageConfigurationService {

    @Autowired
    private BaseMessageConfigurationMapper baseMessageConfigurationMapper;

    /**
     * 查询平台管理-消息配置
     *
     * @param id 平台管理-消息配置主键
     * @return 平台管理-消息配置
     */
    @Override
    public BaseMessageConfiguration selectBaseMessageConfigurationById(String id) {
        return baseMessageConfigurationMapper.selectBaseMessageConfigurationById(id);
    }

    /**
     * 查询平台管理-消息配置列表
     *
     * @param baseMessageConfiguration 平台管理-消息配置
     * @return 平台管理-消息配置
     */
    @Override
    public IPage<BaseMessageConfiguration> selectBaseMessageConfigurationList(BaseMessageConfigurationPageRequest baseMessageConfiguration) {
        return baseMessageConfigurationMapper.selectBaseMessageConfigurationList(baseMessageConfiguration);
    }

    /**
     * 新增平台管理-消息配置
     *
     * @param baseMessageConfiguration 平台管理-消息配置
     * @return 结果
     */
    @Override
    public int insertBaseMessageConfiguration(BaseMessageConfiguration baseMessageConfiguration) {
        baseMessageConfiguration.setId(UUID.randomUUID().toString().replace("-", ""));
        return baseMessageConfigurationMapper.insertBaseMessageConfiguration(baseMessageConfiguration);
    }

    /**
     * 修改平台管理-消息配置
     *
     * @param baseMessageConfiguration 平台管理-消息配置
     * @return 结果
     */
    @Override
    public int updateBaseMessageConfiguration(BaseMessageConfiguration baseMessageConfiguration) {
        return baseMessageConfigurationMapper.updateBaseMessageConfiguration(baseMessageConfiguration);
    }

    /**
     * 批量删除平台管理-消息配置
     *
     * @param ids 需要删除的平台管理-消息配置主键
     * @return 结果
     */
    @Override
    public int deleteBaseMessageConfigurationByIds(List<String> ids) {
        return baseMessageConfigurationMapper.deleteBaseMessageConfigurationByIds(ids);
    }

    /**
     * 删除平台管理-消息配置信息
     *
     * @param id 平台管理-消息配置主键
     * @return 结果
     */
    @Override
    public int deleteBaseMessageConfigurationById(String id) {
        return baseMessageConfigurationMapper.deleteBaseMessageConfigurationById(id);
    }
}
