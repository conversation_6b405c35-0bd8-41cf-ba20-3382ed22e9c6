import{d as m,a8 as d,g as n,n as r,aB as _,aJ as g,q as x,i as l,h as E,F as v,p as u,an as h,aH as y,cE as w,C as B}from"./index-r0dFAfgr.js";import{l as C,m as O}from"./config-DqqM5K5L.js";import"./DateFormatter-Bm9a68Ax.js";import"./OutsideWorkOrder-C6s8joBt.js";import"./index-CpGhZCTT.js";const k={class:"step-wrapper"},V=m({__name:"OrderStepTags",props:{config:{}},setup(f){const s=f,c=d(()=>{let e=s.config.colum?C():O();s.config.status==="REJECTED"&&(e=e.filter(t=>t.value!=="APPROVED")),s.config.status==="APPROVED"&&(e=e.filter(t=>t.value!=="REJECTED"));const a=e.findIndex(t=>t.value===s.config.status);return a===-1?[{perm:!0,text:s.config.statusName,type:"info"}]:e.map((t,o)=>({perm:!0,text:t.label,type:a>o?"primary":a===o?"success":"info"}))});return(e,a)=>{const t=y,o=w;return n(),r("div",k,[(n(!0),r(_,null,g(l(c),(p,i)=>(n(),r("div",{key:i,class:"step-item"},[x(t,{config:p,type:p.type},null,8,["config","type"]),i!==l(c).length-1?(n(),E(o,{key:0,style:{width:"40px",height:"100%","font-size":"24px"}},{default:v(()=>a[0]||(a[0]=[u("svg",{viewBox:"0 0 1024 1024",xmlns:"http://www.w3.org/2000/svg","data-v-78e17ca8":""},[u("path",{fill:"currentColor",d:"M754.752 480H160a32 32 0 1 0 0 64h594.752L521.344 777.344a32 32 0 0 0 45.312 45.312l288-288a32 32 0 0 0 0-45.312l-288-288a32 32 0 1 0-45.312 45.312L754.752 480z"})],-1)])),_:1})):h("",!0)]))),128))])}}}),S=B(V,[["__scopeId","data-v-ff02aca3"]]);export{S as default};
