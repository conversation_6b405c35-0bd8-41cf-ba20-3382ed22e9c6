package org.thingsboard.server.dao.repair;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.Device;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.User;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.device.DeviceService;
import org.thingsboard.server.dao.model.DTO.TriggerDTO;
import org.thingsboard.server.dao.model.sql.*;
import org.thingsboard.server.dao.model.sql.shuiwu.assets.AssetsAccountEntity;
import org.thingsboard.server.dao.project.ProjectService;
import org.thingsboard.server.dao.shuiwu.assets.AssetsAccountService;
import org.thingsboard.server.dao.sql.repair.RepairPlanCRepository;
import org.thingsboard.server.dao.sql.repair.RepairPlanRepository;
import org.thingsboard.server.dao.sql.repair.RepairPlanTriggerRepository;

import java.text.SimpleDateFormat;
import java.util.*;

@Slf4j
@Service
public class RepairPlanServiceImpl implements RepairPlanService {

    @Autowired
    private RepairPlanRepository repairPlanRepository;
    @Autowired
    private RepairPlanCRepository repairPlanCRepository;
    @Autowired
    private DeviceService deviceService;
    @Autowired
    private RepairStandardService repairStandardService;
    @Autowired
    private ProjectService projectService;
    @Autowired
    private RepairPlanTriggerRepository repairPlanTriggerRepository;
    @Autowired
    private RepairJobService repairJobService;
    @Autowired
    private AssetsAccountService assetsAccountService;


    @Override
    public RepairPlanEntity detail(String id, User currentUser) {
        // 查询主表
        RepairPlanEntity plan = repairPlanRepository.findOne(id);

        if (plan != null) {
            TenantId tenantId = new TenantId(UUIDConverter.fromString(plan.getTenantId()));
            // 查询项目列表
            List<ProjectEntity> projectList = projectService.findByTenantId(tenantId);
            Map<String, ProjectEntity> projectMap = new HashMap<>();
            if (projectList != null && projectList.size() > 0) {
                projectList.forEach(p -> projectMap.put(p.getId(), p));
            }

            // 查询设备列表
            List<AssetsAccountEntity> assetsAccountList = assetsAccountService.findByTenantId(UUIDConverter.fromTimeUUID(tenantId.getId()));
            Map<String, AssetsAccountEntity> assetsAccountMap = new HashMap<>();
            if (assetsAccountList != null && assetsAccountList.size() > 0) {
                assetsAccountList.forEach(d -> assetsAccountMap.put(d.getId(), d));
            }

            // 查询检修项目列表
            List<RepairStandardEntity> repairStandardList = repairStandardService.findAll("", currentUser.getTenantId());
            Map<String, RepairStandardEntity> repairStandardMap = new HashMap<>();
            if (repairStandardList != null && repairStandardList.size() > 0) {
                repairStandardList.forEach(r -> repairStandardMap.put(r.getId(), r));
            }

            if ("2".equals(plan.getType())) {// 触发性计划
                RepairPlanTriggerEntity trigger = repairPlanTriggerRepository.findByMainId(id);

                ProjectEntity project = projectMap.get(trigger.getProjectId());
                if (project != null) {
                    trigger.setProjectName(project.getName());
                }

                AssetsAccountEntity assetsAccountEntity = assetsAccountMap.get(trigger.getDeviceId());
                if (assetsAccountEntity != null) {
                    trigger.setDeviceName(assetsAccountEntity.getDeviceName());
                }
                String detail = trigger.getDetail();
                trigger.setTriggerList(JSON.parseArray(detail, TriggerDTO.class));

                // 查询子表
                RepairStandardEntity repairStandardEntity = repairStandardMap.get(trigger.getStandardId());
                if (repairStandardEntity != null) {
                    trigger.setStandardName(repairStandardEntity.getName());
                }

                plan.setTrigger(trigger);
            } else {// 固定日期和预防性计划
                // 查询子表
                List<RepairPlanCEntity> childList = repairPlanCRepository.findByMainIdOrderByOrderNumber(id);
                for (RepairPlanCEntity child : childList) {
                    ProjectEntity project = projectMap.get(child.getProjectId());
                    if (project != null) {
                        child.setProjectName(project.getName());
                    }

                    AssetsAccountEntity assetsAccountEntity = assetsAccountMap.get(child.getDeviceId());
                    if (assetsAccountEntity != null) {
                        child.setDeviceName(assetsAccountEntity.getDeviceName());
                    }

                    RepairStandardEntity repairStandard = repairStandardMap.get(child.getStandardId());
                    if (repairStandard != null) {
                        child.setStandardName(repairStandard.getName());
                    }
                }
                plan.setJobList(childList);
            }
        }

        return plan;
    }

    @Override
    public PageData<RepairPlanEntity> findList(int page, int size, String name, String status, User currentUser) {
        // 分页参数
        PageRequest pageable = new PageRequest(page - 1, size, Sort.Direction.DESC, "createTime");
        // 参数处理
        List<String> statusList = new ArrayList<>();
        if (StringUtils.isBlank(status)) {
            statusList.add("1");
            statusList.add("2");
        } else {
            statusList.addAll(Arrays.asList(status.split(",")));
        }

        Page<RepairPlanEntity> pageResult = repairPlanRepository.findList(name, UUIDConverter.fromTimeUUID(currentUser.getTenantId().getId()), statusList, pageable);

        return new PageData<>(pageResult.getTotalElements(), pageResult.getContent());
    }

    @Override
    public RepairPlanEntity savePlan(RepairPlanEntity entity, User currentUser) {
        // 保存主表
        entity.setCreator(currentUser.getFirstName());
        entity.setCreateTime(new Date());
        entity.setTenantId(UUIDConverter.fromTimeUUID(currentUser.getTenantId().getId()));

        RepairPlanEntity save = repairPlanRepository.save(entity);
        entity.setId(save.getId());

        // 保存子表
        if ("2".equals(entity.getType())) {// 触发性任务
            RepairPlanTriggerEntity trigger = entity.getTrigger();
            List<TriggerDTO> triggerList = trigger.getTriggerList();
            trigger.setDetail(JSON.toJSONString(triggerList));

            trigger.setCreateTime(entity.getCreateTime());
            trigger.setTenantId(entity.getTenantId());
            trigger.setMainId(entity.getId());

            repairPlanTriggerRepository.save(trigger);
        } else {
            List<RepairPlanCEntity> jobList = entity.getJobList();
            for (RepairPlanCEntity planC : jobList) {
                planC.setMainId(entity.getId());
                planC.setTenantId(entity.getTenantId());
                planC.setCreateTime(entity.getCreateTime());
            }
            entity.setJobList(repairPlanCRepository.save(jobList));

            // 生成检修任务
            this.executePlan(entity, true);
        }

        return entity;
    }

    /**
     * 执行计划生成检修任务
     *
     * @param entity 计划
     * @param create 是否为新建的计划
     */
    @Override
    @Transactional
    public void executePlan(RepairPlanEntity entity, boolean create) {
        // 生成检修任务
        List<RepairPlanCEntity> jobList = entity.getJobList();
        if (!create) {// 非新建需要查询出任务详情
            jobList = repairPlanCRepository.findByMainIdOrderByOrderNumber(entity.getId());
        }

        // 判断是否到达时间
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

        String executeTime = dateFormat.format(entity.getExecuteTime());
        String now = dateFormat.format(new Date());
        if (executeTime.equals(now)) {// 到达时间生成
            if ("1".equals(entity.getStatus())) {// 判断是否启用计划
                // 启用, 生成任务
                buildRepairTask(entity, jobList);

            } else {
                // 停用
            }

            // 变更下一次执行时间
            if ("3".equals(entity.getType()) && entity.getPeriodTime() != null && entity.getPeriodTime() != 0) {
                Date nextExecuteTime = new Date(entity.getExecuteTime().getTime() + (entity.getPeriodTime() * 24 * 60 * 60 * 1000));
                entity.setExecuteTime(nextExecuteTime);
            }

            // 保存计划
            repairPlanRepository.save(entity);
        }

    }

    /**
     * 生成维修任务
     *
     * @param plan  维修任务
     * @param jobList 任务详情
     */
    private void buildRepairTask(RepairPlanEntity plan, List<RepairPlanCEntity> jobList) {
        // 生成维修任务主表
        RepairJobEntity job = new RepairJobEntity();
        job.setName(plan.getName());
        job.setType(plan.getType());
        job.setStatus("1");// 未开始
        job.setExecuteTime(plan.getExecuteTime());
        job.setCreateTime(new Date());
        job.setCreator(plan.getCreator());
        job.setTenantId(plan.getTenantId());

        job = repairJobService.save(job);
        // 生成子表记录
        if (jobList != null) {

            // 查询标准列表
            List<RepairStandardEntity> standardList = repairStandardService.findAll("", plan.getTenantId());
            Map<String, RepairStandardEntity> standardMap = new HashMap<>();
            if (standardList != null) {
                standardList.forEach(s -> standardMap.put(s.getId(), s));
            }

            List<RepairJobCEntity> childList = new ArrayList<>();
            for (RepairPlanCEntity planChild : jobList) {
                RepairJobCEntity jobC = new RepairJobCEntity();
                jobC.setMainId(job.getId());
                jobC.setProjectId(planChild.getProjectId());
                jobC.setDeviceId(planChild.getDeviceId());
                jobC.setOrderNumber(planChild.getOrderNumber());
                jobC.setStatus("1");// 未开始

                RepairStandardEntity standard = standardMap.get(planChild.getStandardId());
                if (standard != null) {
                    jobC.setStandardName(standard.getName());
                    jobC.setStandardDetail(planChild.getStandardId());
                }

                jobC.setCreateTime(job.getCreateTime());
                jobC.setTenantId(job.getTenantId());

                childList.add(jobC);
            }
            repairJobService.save(childList);
        }

    }

    @Override
    public void remove(List<String> ids) {
        // 删除检修计划
        for (String id : ids) {
            // 删除主表
            repairPlanRepository.delete(id);

            // 删除子表
            repairPlanCRepository.removeByMainId(id);
        }
    }

    @Override
    public void changeStatus(String id) {
        RepairPlanEntity entity = repairPlanRepository.findOne(id);
        String status = entity.getStatus();
        if ("1".equals(status)) {
            entity.setStatus("2");// 停用
        }
        if ("2".equals(status)) {
            entity.setStatus("1");// 启用
        }

        repairPlanRepository.save(entity);
    }

    @Override
    public List<RepairPlanEntity> findPlanByType(String type) {
        List<RepairPlanEntity> list = repairPlanRepository.findByType(type);
        if (list == null) {
            list = new ArrayList<>();
        }
        return list;
    }

    @Override
    public RepairPlanTriggerEntity findTrigger(String id) {
        return repairPlanTriggerRepository.findByMainId(id);
    }

    @Override
    public void buildTriggerJob(RepairPlanEntity plan) {
        // 生成维修任务主表
        RepairJobEntity job = new RepairJobEntity();
        job.setName(plan.getName());
        job.setType(plan.getType());
        job.setStatus("1");// 未开始
        job.setExecuteTime(plan.getExecuteTime());
        job.setCreateTime(new Date());
        job.setCreator(plan.getCreator());
        job.setTenantId(plan.getTenantId());

        job = repairJobService.save(job);

        RepairPlanTriggerEntity trigger = plan.getTrigger();
        // 生成子表记录
        if (trigger != null) {
            RepairJobTriggerEntity jobC = new RepairJobTriggerEntity();
            jobC.setMainId(job.getId());
            jobC.setProjectId(trigger.getProjectId());
            jobC.setDeviceId(trigger.getDeviceId());
            jobC.setDetail(trigger.getDetail());
            jobC.setCreateTime(job.getCreateTime());
            jobC.setTenantId(job.getTenantId());

            repairJobService.save(jobC);
        }
    }
}
