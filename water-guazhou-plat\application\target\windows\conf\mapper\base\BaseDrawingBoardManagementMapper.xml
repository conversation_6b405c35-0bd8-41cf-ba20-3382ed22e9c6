<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.base.BaseDrawingBoardManagementMapper">
    
    <resultMap type="org.thingsboard.server.dao.model.sql.base.BaseDrawingBoardManagement" id="BaseDrawingBoardManagementResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="description"    column="description"    />
        <result property="status"    column="status"    />
        <result property="version"    column="version"    />
        <result property="url"    column="url"    />
    </resultMap>

    <sql id="selectBaseDrawingBoardManagementVo">
        select id, name, description, status, version, url from base_drawing_board_management
    </sql>

    <select id="selectBaseDrawingBoardManagementList" parameterType="org.thingsboard.server.dao.model.sql.base.BaseDrawingBoardManagement" resultMap="BaseDrawingBoardManagementResult">
        <include refid="selectBaseDrawingBoardManagementVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="version != null  and version != ''"> and version = #{version}</if>
            <if test="url != null  and url != ''"> and url = #{url}</if>
        </where>
    </select>
    
    <select id="selectBaseDrawingBoardManagementById" parameterType="String" resultMap="BaseDrawingBoardManagementResult">
        <include refid="selectBaseDrawingBoardManagementVo"/>
        where id = #{id}
    </select>

    <insert id="insertBaseDrawingBoardManagement" parameterType="org.thingsboard.server.dao.model.sql.base.BaseDrawingBoardManagement">
        insert into base_drawing_board_management
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="name != null">name,</if>
            <if test="description != null">description,</if>
            <if test="status != null">status,</if>
            <if test="version != null">version,</if>
            <if test="url != null">url,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="name != null">#{name},</if>
            <if test="description != null">#{description},</if>
            <if test="status != null">#{status},</if>
            <if test="version != null">#{version},</if>
            <if test="url != null">#{url},</if>
         </trim>
    </insert>

    <update id="updateBaseDrawingBoardManagement" parameterType="org.thingsboard.server.dao.model.sql.base.BaseDrawingBoardManagement">
        update base_drawing_board_management
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="description != null">description = #{description},</if>
            <if test="status != null">status = #{status},</if>
            <if test="version != null">version = #{version},</if>
            <if test="url != null">url = #{url},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBaseDrawingBoardManagementById" parameterType="String">
        delete from base_drawing_board_management where id = #{id}
    </delete>

    <delete id="deleteBaseDrawingBoardManagementByIds" parameterType="String">
        delete from base_drawing_board_management where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>