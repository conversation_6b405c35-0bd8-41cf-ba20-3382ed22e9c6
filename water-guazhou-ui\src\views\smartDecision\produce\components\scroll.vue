<template>
  <div class="card">
    <div class="title">
      <span style="text-align: left;">监测点名称</span>
      <span>浊度(NTU)</span>
      <span>余氯(mg/L)</span>
      <span>PH</span>
      <span>读取时间</span>
    </div>
    <VueScroll
      :data="props.config"
      :class-option="optionHover"
      class="warp"
    >
      <div>
        <li
          v-for="(item, i) in props.config"
          :key="i"
          class="title"
        >
          <span style="text-align: left;">{{ item.name }}</span>
          <span>{{ item.turbidity }}</span>
          <span>{{ item.remainder }}</span>
          <span>{{ item.ph }}</span>
          <span>{{ item.time }}</span>
        </li>
      </div>
    </VueScroll>
  </div>
</template>

<script lang="ts" setup>
const props = defineProps<{ config: any[] }>()

const optionHover = {
  step: 0.2,
  limitMoveNum: 10
}

</script>

<style lang="scss" scoped>
.card {
  overflow: hidden;
  font-size: 13px;
  height: 100%;
  padding: 8px;
}

.over_hide {
  overflow: hidden;
}

.title {
  display: flex;
  line-height: 30px;
  span {
    text-align: left;
  }

  span:nth-child(1) {
    width: 180px;
  }

  span:nth-child(2) {
    width: 120px;
  }

  span:nth-child(3) {
    width: 120px;
  }

  span:nth-child(4) {
    width: 70px;
  }

  span:nth-child(5) {
    width: 70px;
  }
}

.warp {
  height: calc(100% - 30px);
  overflow: hidden;
  li {
    height: 30px;
    line-height: 30px;
    font-size: 12px;
  }
  li:nth-child(2n) {
    background-color: rgb(26, 69, 173);
  }
}
</style>
