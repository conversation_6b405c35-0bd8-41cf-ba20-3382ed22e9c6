package org.thingsboard.server.dao.model.sql;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.thingsboard.server.dao.model.ModelConstants;

import javax.persistence.*;

/**
 * <AUTHOR>
 * @date 2020/4/26 10:51
 */
@Data
@Entity
@Table(name = ModelConstants.MSG_LOG_TABLE_NAME)
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
public class MsgLogEntity {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    /**
     * 用户名
     */
    @Column(name = ModelConstants.AUDIT_LOG_USER_NAME_PROPERTY)
    private String userName;

    /**
     * 手机号
     */
    @Column(name = ModelConstants.PHONE_PROPERTY)
    private String phone;
    /**
     * 邮箱地址
     */
    @Column(name = ModelConstants.EMAIL_PROPERTY)
    private String email;
    /**
     * 发送状态
     */
    @Column(name = ModelConstants.CUSTOMER_ROLE_STATUS)
    private String status;
    /**
     * 发送内容
     */
    @Column(name = ModelConstants.MSG_LOG_MSG_BODY)
    private String msgBody;
    /**
     * 消息类别
     */
    @Column(name = ModelConstants.ALARM_TYPE_PROPERTY)
    private String msgType;
    /**
     * 创建时间
     */
    @Column(name = ModelConstants.DATASOURCE_UPDATE_TIME)
    private long updateTime;


    @Column(name = ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;

    @Column(name = ModelConstants.MSG_LOG_LOG_TYPE)
    private String logType;


    /**
     * 备注
     */
    @Column(name = ModelConstants.DATASOURCE_FORMAT)
    private String format;

}
