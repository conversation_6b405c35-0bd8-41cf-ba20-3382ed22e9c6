import{d as S,M as j,c as f,a8 as y,s as v,r,x as p,a9 as x,o as O,g as R,n as M,q as c,i as n,F as B,b6 as V,b7 as I}from"./index-r0dFAfgr.js";import{_ as P}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as F}from"./CardTable-rdWOL4_6.js";import{_ as U}from"./CardSearch-CB_HNR-Q.js";import{I as h}from"./common-CvK_P_ao.js";import{S as Y,T as z,U as A,g as W,V as q}from"./manage-BReaEVJk.js";import{g as w}from"./projectManagement-CDcrrCQ1.js";import{S as _}from"./data-Dv9-Tstw.js";import G from"./detail-DEo1RlcF.js";import{f as $}from"./DateFormatter-Bm9a68Ax.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";import"./index-CCFuhOrs.js";/* empty css                             */import"./DPlayer-Be2AurQX.js";import"./DPlayer.min-_HMH7IVX.js";import"./xmwcqk-Cxfq91Sa.js";import"./xmgc-Czrw1pVN.js";import"./cytbgs-WJxYGJyW.js";import"./gcwcqk-CV4EMT8B.js";import"./ssxmwcqk-BJgrXy2o.js";import"./gcsjjcxx-lLqauOhu.js";import"./sjbg-L9B2uWB9.js";import"./data-DDQ4eWNr.js";import"./gcysjcxx-BB9DfF9W.js";import"./qzjbxx-D98fv1p0.js";import"./htjbxx-CcjVPiVa.js";import"./htbg-CJ8T-1F4.js";import"./fygl-BCgGpKLc.js";import"./ssxq-C8LIbr3S.js";import"./ysqgcjcxx-5zZQS7XS.js";import"./ssgcjsjcxx-BD3tZw0Z.js";import"./ssgdjcxx-4P0LZdbp.js";import"./xmzysjcxx-DxVVq7LT.js";import"./xmzjsjcxx-C3UxQ9jk.js";import"./xmzgdjcxx-LKGnYC4Q.js";const H={class:"wrapper"},Re=S({__name:"projectBudget",setup(J){const{$btnPerms:d}=j(),g=f(),m=f(),b=f(),C=f({filters:[{label:"工程编号",field:"constructionCode",type:"input"},{label:"工程名称",field:"constructionName",type:"input"},{label:"工程类别",field:"constructionTypeId",type:"select",options:y(()=>i.projectType)},{label:"创建时间",field:"time",type:"daterange"}],operations:[{type:"btn-group",btns:[{type:"default",perm:!0,text:"导出",icon:h.DOWNLOAD,click:()=>{Y().then(e=>{const t=window.URL.createObjectURL(e.data),a=document.createElement("a");a.style.display="none",a.href=t,a.setAttribute("download","工程预算.xlsx"),document.body.appendChild(a),a.click()})}},{type:"default",perm:!0,text:"重置",svgIcon:v(I),click:()=>{var e;(e=g.value)==null||e.resetForm(),s()}},{perm:!0,text:"查询",icon:h.QUERY,click:()=>s()}]}]}),l=r({defaultExpandAll:!0,indexVisible:!0,columns:[{label:"工程编号",prop:"constructionCode"},{label:"工程名称",prop:"constructionName"},{label:"工程类别",prop:"constructionTypeName"},{label:"预算金额(万元)",prop:"cost"},{label:"创建人",prop:"creatorName"},{label:"创建时间",prop:"createTime",formatter:e=>$(e.createTime,"YYYY-MM-DD HH:mm:ss")},{label:"工作状态",prop:"status",tag:!0,tagColor:e=>{var t;return((t=_.find(a=>a.value===e.status))==null?void 0:t.color)||""},formatter:e=>{var t;return(t=_.find(a=>a.value===e.status))==null?void 0:t.label}}],operationWidth:"300px",operations:[{disabled:e=>!e.status,isTextBtn:!1,text:"详情",perm:d("RoleManageEdit"),click:e=>{var t;i.selected=e,(t=b.value)==null||t.openDrawer()}},{disabled:e=>e.status!==null,isTextBtn:!1,type:"primary",text:"添加预算",perm:d("RoleManageEdit"),click:e=>{E(e)}},{disabled:e=>e.status!=="PROCESSING",isTextBtn:!1,type:"success",text:"编辑预算",perm:d("RoleManageEdit"),click:e=>L(e)},{disabled:e=>e.status!=="PROCESSING",isTextBtn:!1,text:"完成",perm:d("RoleManageEdit"),click:e=>{z(e.constructionCode).then(t=>{t.data.code===200?p.success("已完成"):p.warning("完成失败"),s()})}}],dataList:[],pagination:{page:1,limit:20,total:0,refreshData:({page:e,size:t})=>{l.pagination.page=e,l.pagination.limit=t,s()}}}),o=r({title:"添加工程预算信息",labelWidth:"130px",dialogWidth:"1000px",submitting:!1,submit:e=>{o.submitting=!0;let t="新增";e.id&&(t="修改"),e.pipLengthDesign=JSON.stringify(e.pipLengthDesign),A(e).then(a=>{var u;o.submitting=!1,a.data.code===200?(p.success(t+"成功"),(u=m.value)==null||u.closeDialog(),s()):p.warning(t+"失败")}).catch(a=>{o.submitting=!1,p.warning(a)})},defaultValue:{},group:[{fields:[{xs:12,type:"input",label:"工程编号",field:"constructionCode",disabled:!0},{xs:12,type:"input",label:"工程名称",field:"constructionName",disabled:!0},{xs:12,type:"input",label:"预算人",field:"budgeter",rules:[{required:!0,message:"请输入预算人"}]},{xs:12,type:"number",label:"预算金额(万元)",field:"cost"},{xs:12,type:"textarea",label:"地址",field:"address"},{type:"textarea",label:"备注",field:"remark"},{type:"file",label:"附件",field:"attachments"}]}]}),T=r({title:"详情",group:[],width:"80%",modalClass:"lightColor",cancel:!1}),D=r({defaultValue:y(()=>i.selected),border:!1,direction:"horizontal",column:1,title:"项目基础信息",fields:[{type:"text",label:"项目编号:",field:"project.code"},{type:"text",label:"项目名称:",field:"project.name"},{type:"text",label:"启动时间:",field:"project.startTimeName"},{type:"text",label:"预计结束时间:",field:"project.expectEndTimeName"},{type:"text",label:"项目负责人:",field:"project.principal"},{type:"divider",text:""},{type:"text",label:"工程编号:",field:"constructionCode"},{type:"text",label:"工程名称:",field:"constructionName"},{type:"text",label:"工程类别:",field:"constructionTypeName"},{type:"text",label:"工程预算(万元):",field:"cost"}]}),E=e=>{var t;o.title="添加工程预算",i.DesignTubeLength=[],o.defaultValue={...e||{}},(t=m.value)==null||t.openDialog()},L=e=>{var t;o.title="编辑工程预算",i.DesignTubeLength=[],o.defaultValue={...e||{}},i.DesignTubeLength=(e==null?void 0:e.pipLengthDesign)&&JSON.parse(e==null?void 0:e.pipLengthDesign)||[],(t=m.value)==null||t.openDialog()},i=r({projectList:[],projectType:[],selected:{},DesignTubeLength:[],getOptions:()=>{w({page:1,size:-1}).then(e=>{i.projectList=x(e.data.data.data||[],"children",{label:"name",value:"code"})}),W({page:1,size:-1}).then(e=>{i.projectType=x(e.data.data.data||[],"children")})}}),s=async()=>{var t;const e={size:l.pagination.limit||20,page:l.pagination.page||1,...((t=g.value)==null?void 0:t.queryParams)||{}};e!=null&&e.time&&(e.fromTime=e.time[0],e.toTime=e.time[1],delete e.time),q(e).then(a=>{l.dataList=a.data.data.data||[],l.pagination.total=a.data.data.total||0})};return O(()=>{s(),i.getOptions()}),(e,t)=>{const a=U,u=F,k=P,N=V;return R(),M("div",H,[c(a,{ref_key:"refSearch",ref:g,config:n(C)},null,8,["config"]),c(u,{config:n(l),class:"card-table"},null,8,["config"]),c(k,{ref_key:"refForm",ref:m,config:n(o)},null,8,["config"]),c(N,{ref_key:"refDetail",ref:b,config:n(T)},{default:B(()=>[c(G,{config:n(i).selected,basic:n(D),show:5},null,8,["config","basic"])]),_:1},8,["config"])])}}});export{Re as default};
