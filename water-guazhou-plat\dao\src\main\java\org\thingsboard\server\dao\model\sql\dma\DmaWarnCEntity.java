package org.thingsboard.server.dao.model.sql.dma;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;
import java.util.Date;

/**
 * DMA分析
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-04-24
 */
@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.DMA_WARN_C_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class DmaWarnCEntity {
    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.DMA_WARN_C_PID)
    private String pid;

    @Column(name = ModelConstants.DMA_WARN_C_STATUS)
    private String status;

    @Column(name = ModelConstants.DMA_WARN_C_REMARK)
    private String remark;

    @Column(name = ModelConstants.DMA_WARN_C_CREATE_TIME)
    private Date createTime;

}
