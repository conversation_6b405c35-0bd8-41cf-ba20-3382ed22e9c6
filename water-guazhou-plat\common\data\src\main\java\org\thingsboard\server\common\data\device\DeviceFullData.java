/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.common.data.device;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2020/1/9 14:15
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DeviceFullData {

    /**
     * 变量
     */
    private String property;
    /**
     * 变量名称
     */
    private String propertyName;
    /**
     * 采集状态
     */
    private boolean status;
    /**
     * 当前值
     */
    private String value;
    /**
     * 采集时间
     */
    private long collectionTime;
    /**
     * 变量类别
     */
    private String entityType;

    private String unit;

    private String propertyType;

    private String serialNumber;

    private String pointAddress;

    private String group;

    private String controlOptions;
}
