import{m as S,C as _,M as U,u as I,eX as w,g as u,h as d,F as l,p as h,q as t,G as p,n as g,aJ as b,aB as M,an as c,aK as L,aL as q,I as B,ff as J,H as O,dz as z,dA as G,dF as K,e4 as H,K as P,J as X,d8 as j,bz as Q,L as W}from"./index-r0dFAfgr.js";import{l as Y}from"./lodash.default-B3JdLn1L.js";import"./reduce-BbPixnH6.js";import"./padStart-BKfyZZDO.js";import"./_baseExtremum-UssVWohW.js";import"./_baseLt-svgXHEqw.js";import"./sortBy-DDhdj0i5.js";import"./max-CCqK09y5.js";import"./minBy-DBQvPu-j.js";import"./_baseSum-Cz9yialR.js";import"./min-ks0CS-3r.js";import"./sumBy-Dpy7mNiE.js";function Z(o,e){if(Array.isArray(o)){const s=o[0],i=o[1],a=`/plugins/telemetry/${s}/${i}/values/attributes`;return S({url:a,method:"get"})}return S({url:`/api/plugins/telemetry/${o}/${e}/values/attributes`,method:"get"})}const{$messageError:V}=U(),$={name:"SettingsDialog",props:["dialogConfig"],emits:["Save"],data(){const o=(e,s,i)=>{s.trim()===""?i(new Error("输入不可为空，请输入")):i()};return{formData:{alarmRemarks:"",alarmType:"scope",alarmName:"",deviceName:"",deviceId:{entityType:"DEVICE",id:""},attribute:"",details:{alarmRemarks:""},isCycle:!1,cycle:null,params:"",alarmScript:"",restoreScript:"",severity:"",period:"00:00-24:00",restoreType:"scope"},period:{start:"",end:""},groupList:[{name:"电压越上限",value:"电压越上限"},{name:"电压越下限",value:"电压越下限"},{name:"电压波动",value:"电压波动"},{name:"三相电压不平衡",value:"三相电压不平衡"},{name:"谐波电流",value:"谐波电流"}],alarmTypes:[{value:"scope",label:"范围告警",rLabel:"范围恢复"},{value:"change",label:"变动告警",rLabel:"变动恢复"}],severityList:[{value:"tips",label:"提示"},{value:"secondary",label:"次要"},{value:"urgent",label:"紧急"},{value:"serious",label:"严重"}],alarmDisabled:!1,devicesList:this.dialogConfig.deviceList,energyTypes:[],deviceInfo:[],rules:{alarmType:[{required:!0,message:"请选择告警类型",trigger:"change"}],deviceName:[{required:!0,message:"请选择仪表",trigger:"change"}],attribute:[{required:!0,message:"请选择仪表后选择监测",trigger:"change"}],name:[{required:!0,message:"请输入告警名称",trigger:"blur"},{validator:o,trigger:"blur"}],severity:[{required:!0,message:"请选择告警级别",trigger:"blur"}],cycle:[{required:!0,message:"请选择周期",trigger:"change"}]},collapse:["1","2"]}},computed:{visible(){return this.dialogConfig.visible}},created(){console.log(this.dialogConfig);const o=this.dialogConfig.temp;for(const e in o)this.formData[e]=o[e];if(this.formData.restoreType=this.formData.recoverValue==="取反"?this.formData.alarmType:this.formData.restoreType,o.params&&o.params!==""){this.alarmDisabled=!0;const e=JSON.parse(o.params);this.formData.alarmMinValue=e.alarmMinValue,this.formData.alarmMaxValue=e.alarmMaxValue,this.formData.recoverMinValue=e.recoverMinValue,this.formData.recoverMaxValue=e.recoverMaxValue,this.formData.cAlarmFrom=e.cAlarmFrom,this.formData.cAlarmTo=e.cAlarmTo,this.formData.cRecoverFrom=e.cRecoverFrom,this.formData.cRecoverTo=e.cRecoverTo}else this.formData.params={};this.period.start=this.formData.period.split("-")[0],this.period.end=this.formData.period.split("-")[1],this.formData.deviceId.id!==""&&o.deviceId?(this.formData.deviceName=o.deviceId.id,this.formData.alarmRemarks=o.details.alarmRemarks,this.deviceSelect()):this.formData.alarmRemarks=""},methods:{deviceSelect(){const o=this.formData.deviceName;Z("DEVICE",o).then(e=>{this.energyTypes=[],this.deviceInfo=e.data,e.data.forEach(s=>{s.key==="prop"&&(this.energyTypes=JSON.parse(s.value))})})},alarmTypeChange(){},isCycleChange(){this.formData.isCycle?this.formData.cycle="day":this.formData.cycle=null},Save(){this.$refs.formData.validate(o=>{if(o){if((this.formData.recoverMinValue||this.formData.recoverMinValue===0)&&(this.formData.recoverMaxValue||this.formData.recoverMaxValue===0)&&this.formData.recoverMinValue>=this.formData.recoverMaxValue)return V("输入错误：恢复最小值需小于恢复最大值"),!1;if((this.formData.alarmMinValue||this.formData.alarmMinValue===0)&&(this.formData.alarmMaxValue||this.formData.alarmMaxValue===0)&&this.formData.alarmMinValue>=this.formData.alarmMaxValue)return V("输入错误：告警最小值需小于告警最大值"),!1;this.saveSubmit()}else return V("请核实填写信息后提交"),!1})},saveSubmit(){const o=this.formData.attribute;if(this.formData.params={},this.formData.alarmType==="scope")if(this.formData.alarmMinValue||this.formData.alarmMaxValue||this.formData.alarmMinValue===0||this.formData.alarmMaxValue===0){if(this.formData.alarmMinValue===0&&this.formData.alarmMaxValue===0)return V("范围告警,告警值不可同时为0"),!1;this.scriptCode("scope",this.formData.alarmMinValue,this.formData.alarmMaxValue,o,"reverse"),this.alarmVal(this.formData.alarmMinValue,this.formData.alarmMaxValue,"告警值","alarm"),this.formData.details.setAlarmType="范围告警"}else return V("范围告警,至少输入一个告警值"),!1;else if((this.formData.cAlarmFrom||this.formData.cAlarmFrom===0)&&(this.formData.cAlarmTo||this.formData.cAlarmTo===0))this.scriptCode("change",this.formData.cAlarmFrom,this.formData.cAlarmTo,o,"reverse"),this.formData.details.alarmSetValue=this.formData.cAlarmFrom+" → "+this.formData.cAlarmTo,this.formData.details.setAlarmType="变动告警";else return V("变动告警,变动值必填"),!1;if(this.formData.restoreType==="scope")if(this.formData.recoverMinValue||this.formData.recoverMaxValue||this.formData.recoverMinValue===0||this.formData.recoverMaxValue===0){if(this.formData.recoverMinValue===0&&this.formData.recoverMaxValue===0)return V("范围告警,告警值不可同时为0"),!1;this.scriptCode("scope",this.formData.recoverMinValue,this.formData.recoverMaxValue,o,"handM"),this.alarmVal(this.formData.recoverMinValue,this.formData.recoverMaxValue,"恢复值","hand"),this.formData.details.rType="范围恢复",this.formData.details.recoverSet=!0}else this.formData.details.recoverSet=!1,this.formData.details.rType="默认同类型";else if(this.formData.cRecoverFrom||this.formData.cRecoverFrom===0||this.formData.cRecoverTo||this.formData.cRecoverTo===0)if((this.formData.cRecoverFrom||this.formData.cRecoverFrom===0)&&(this.formData.cRecoverTo||this.formData.cRecoverTo===0))this.scriptCode("change",this.formData.cRecoverFrom,this.formData.cRecoverTo,o,"handM"),this.formData.details.recoverSetValue=this.formData.cRecoverFrom+" → "+this.formData.cRecoverTo,this.formData.details.rType="变动恢复",this.formData.details.recoverSet=!0;else return V("变动恢复,两个变动值必填"),!1;else this.formData.details.recoverSet=!1,this.formData.details.rType="默认同类型";this.formData.details.recoverSet||(this.formData.details.recoverSetValue="取反",this.formData.restoreType=this.formData.alarmType),this.formData.params=JSON.stringify(this.formData.params),this.formData.details.alarmRemarks=this.formData.alarmRemarks,this.formData.deviceId.id=this.formData.deviceName,this.energyTypes.forEach(i=>{i.propertyCategory===this.formData.attribute&&(this.formData.details.attributeName=i.name)}),this.formData.details.deviceName=this.formData.deviceName,this.formData.severity=this.formData.severity.trim(),this.formData.alarmName=this.formData.name.trim(),this.formData.period=this.period.start+"-"+this.period.end,this.formData.tenantId||(this.formData.tenantId={entityType:"TENANT",id:I().tenantId||w.get("tenantId")});const e=Y.omit(this.formData,["alarmRemarks","deviceName","alarmMaxValue","alarmMinValue","alarmTypeName","recoverMaxValue","recoverMinValue","cycleName"]),s={};for(const i in e)s[i]=this.formData[i];delete s.state,this.$emit("Save",s),this.dialogConfig.close()},scriptCode(o,e,s,i,a){let f="",n="";if(o==="scope"){let D=!0,m=!0;e?D=!0:D=e===0,s?m=!0:m=s===0,D&&m&&(f=`try {var result = {}; var min = ${e}; var max = ${s}; if (min < parseFloat(msg.${i}) && parseFloat(msg.${i}) < max) {
            return true; } else { return false }} catch(e){return e}`,a==="reverse"&&(n=`try {var result = {}; var min = ${e}; var max = ${s}; if (min >= parseFloat(msg.${i}) || parseFloat(msg.${i}) >= max) {
            return true; } else { return false }} catch(e){return e}`)),D&&!m&&(f=`try {var result = {}; var min = ${e}; if (min > parseFloat(msg.${i})) {
            return true; } else { return false }} catch(e){return e}`,a==="reverse"&&(n=`try {var result = {}; var min = ${e}; if (min <= parseFloat(msg.${i})) {
              return true; } else { return false }} catch(e) { return e}`)),!D&&m&&(f=`try {var result = {}; var max = ${s}; if (max < parseFloat(msg.${i})) {
            return true; } else { return false }} catch(e){return e}`,a==="reverse"&&(n=`try {var result = {}; var max = ${s}; if (max >= parseFloat(msg.${i})) {
              return true; } else { return false }} catch(e){return e}`)),a==="reverse"?(this.formData.params.alarmMaxValue=s,this.formData.params.alarmMinValue=e):(this.formData.params.recoverMaxValue=s,this.formData.params.recoverMinValue=e)}else f=`try {if (parseFloat(metadata.lastData) === ${e} && parseFloat(msg.${i}) === ${s}) {
          return true; } else { return false } } catch(e) { return e }`,a==="reverse"&&(n=`try {if (parseFloat(metadata.lastData) === ${s} && parseFloat(msg.${i}) === ${e}) {
            return true; } else { return false }} catch(e){return e}`),this.formData.params.useLastData=!0,a==="reverse"?(this.formData.params.cAlarmFrom=e,this.formData.params.cAlarmTo=s):(this.formData.params.cRecoverFrom=e,this.formData.params.cRecoverTo=s);return a==="reverse"?(this.formData.alarmScript=f,this.formData.restoreScript=n):this.formData.restoreScript=f,!0},alarmVal(o,e,s,i){let a="",f=!0,n=!0;if(o?f=!0:f=o===0,e?n=!0:n=e===0,f&&n)a=o+" < "+s+" < "+e;else{const D=o||o===0?s+" < "+o:"",m=e||e===0?s+" > "+e:"";a=D+m}i==="alarm"?this.formData.details.alarmSetValue=a:this.formData.details.recoverSetValue=a}}},ee={class:"dialog-footer"};function ae(o,e,s,i,a,f){const n=L,D=q,m=B,x=J,k=O,y=z,C=G,F=K,v=H,R=P,T=X,N=j,A=Q,E=W;return f.visible?(u(),d(E,{key:0,modelValue:f.visible,"onUpdate:modelValue":e[20]||(e[20]=r=>f.visible=r),title:"告警设置",width:"630px","append-to-body":"",class:"alarm-design","close-on-click-modal":!1,onClose:s.dialogConfig.close},{footer:l(()=>[h("span",ee,[t(T,{plain:"",onClick:s.dialogConfig.close},{default:l(()=>e[36]||(e[36]=[p("取 消")])),_:1},8,["onClick"]),t(T,{type:"primary",onClick:e[19]||(e[19]=r=>f.Save(a.formData))},{default:l(()=>e[37]||(e[37]=[p("保 存")])),_:1})])]),default:l(()=>[t(R,{ref:"formData",model:a.formData,rules:a.rules,class:"alarm-settings-form","label-width":"130px"},{default:l(()=>[t(m,{label:"仪表名称",prop:"deviceName"},{default:l(()=>[a.formData.id?(u(),d(D,{key:1,modelValue:a.formData.deviceName,"onUpdate:modelValue":e[1]||(e[1]=r=>a.formData.deviceName=r),placeholder:"请选择仪表2",disabled:""},{default:l(()=>[(u(!0),g(M,null,b(a.devicesList,r=>(u(),d(n,{key:r.value,label:r.label,value:r.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])):(u(),d(D,{key:0,modelValue:a.formData.deviceName,"onUpdate:modelValue":e[0]||(e[0]=r=>a.formData.deviceName=r),placeholder:"请选择仪表",style:{width:"400px"},filterable:"",onChange:f.deviceSelect},{default:l(()=>[(u(!0),g(M,null,b(a.devicesList,r=>(u(),d(n,{key:r.value,label:r.label,value:r.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","onChange"]))]),_:1}),t(m,{label:"监测数据",prop:"attribute"},{default:l(()=>[t(D,{modelValue:a.formData.attribute,"onUpdate:modelValue":e[2]||(e[2]=r=>a.formData.attribute=r),placeholder:"请选择仪表后选择监测",style:{width:"400px"}},{default:l(()=>[(u(!0),g(M,null,b(a.energyTypes,r=>(u(),d(n,{key:r.propertyCategory,label:r.name,value:r.propertyCategory},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(m,{label:"有效时段",required:""},{default:l(()=>[t(x,{modelValue:a.period.start,"onUpdate:modelValue":e[3]||(e[3]=r=>a.period.start=r),placeholder:"起始时间",editable:!1,clearable:!1,"picker-options":{start:"00:00",step:"00:15",end:"24:00",maxTime:a.period.end},style:{width:"180px"}},null,8,["modelValue","picker-options"]),e[21]||(e[21]=h("span",{class:"sperater"},"   -     ",-1)),t(x,{modelValue:a.period.end,"onUpdate:modelValue":e[4]||(e[4]=r=>a.period.end=r),placeholder:"结束时间",editable:!1,clearable:!1,"picker-options":{start:"00:00",step:"00:15",end:"24:00",minTime:a.period.start},style:{width:"180px"}},null,8,["modelValue","picker-options"])]),_:1}),t(m,{label:"告警名称",prop:"name"},{default:l(()=>[t(k,{modelValue:a.formData.name,"onUpdate:modelValue":e[5]||(e[5]=r=>a.formData.name=r),class:"severity",placeholder:"请输入告警名称",style:{width:"400px"}},null,8,["modelValue"])]),_:1}),t(m,{label:"告警级别",prop:"severity"},{default:l(()=>[t(C,{modelValue:a.formData.severity,"onUpdate:modelValue":e[6]||(e[6]=r=>a.formData.severity=r)},{default:l(()=>[t(y,{label:"提示"},{default:l(()=>e[22]||(e[22]=[p(" 提示告警 ")])),_:1}),t(y,{label:"次要"},{default:l(()=>e[23]||(e[23]=[p(" 次要告警 ")])),_:1}),t(y,{label:"重要"},{default:l(()=>e[24]||(e[24]=[p(" 重要告警 ")])),_:1}),t(y,{label:"紧急"},{default:l(()=>e[25]||(e[25]=[p(" 紧急告警 ")])),_:1})]),_:1},8,["modelValue"])]),_:1}),t(m,{label:"告警描述"},{default:l(()=>[t(k,{modelValue:a.formData.alarmRemarks,"onUpdate:modelValue":e[7]||(e[7]=r=>a.formData.alarmRemarks=r),type:"textarea",autosize:{minRows:3,maxRows:5},placeholder:"请输入告警描述",style:{width:"400px"}},null,8,["modelValue"])]),_:1}),t(m,{label:"告警类型",prop:"alarmType"},{default:l(()=>[t(C,{modelValue:a.formData.alarmType,"onUpdate:modelValue":e[8]||(e[8]=r=>a.formData.alarmType=r),disabled:a.alarmDisabled,onChange:f.alarmTypeChange},{default:l(()=>[t(F,{label:"scope"},{default:l(()=>e[26]||(e[26]=[p(" 范围告警 ")])),_:1}),t(F,{label:"change"},{default:l(()=>e[27]||(e[27]=[p(" 变动告警 ")])),_:1})]),_:1},8,["modelValue","disabled","onChange"])]),_:1}),a.formData.isCycle?(u(),d(m,{key:0,label:"周期",prop:"cycle"},{default:l(()=>[t(D,{modelValue:a.formData.cycle,"onUpdate:modelValue":e[9]||(e[9]=r=>a.formData.cycle=r),placeholder:"请选择周期"},{default:l(()=>[t(n,{value:"day",label:"日"}),t(n,{value:"month",label:"月"}),t(n,{value:"year",label:"年"})]),_:1},8,["modelValue"])]),_:1})):c("",!0),a.formData.alarmType==="scope"?(u(),d(m,{key:1,label:"告警触发最小值"},{default:l(()=>[t(v,{modelValue:a.formData.alarmMinValue,"onUpdate:modelValue":e[10]||(e[10]=r=>a.formData.alarmMinValue=r),placeholder:"请输入最小告警值",controls:!1,style:{width:"400px"}},null,8,["modelValue"])]),_:1})):c("",!0),a.formData.alarmType==="scope"?(u(),d(m,{key:2,label:"告警触发最大值"},{default:l(()=>[t(v,{modelValue:a.formData.alarmMaxValue,"onUpdate:modelValue":e[11]||(e[11]=r=>a.formData.alarmMaxValue=r),placeholder:"请输入最大告警值",controls:!1,style:{width:"400px"}},null,8,["modelValue"])]),_:1})):c("",!0),a.formData.alarmType==="change"?(u(),d(m,{key:3,label:"变动告警"},{default:l(()=>[e[28]||(e[28]=p(" 从 ")),t(v,{modelValue:a.formData.cAlarmFrom,"onUpdate:modelValue":e[12]||(e[12]=r=>a.formData.cAlarmFrom=r),class:"change-alarm",placeholder:"请输入最小告警值",controls:!1},null,8,["modelValue"]),e[29]||(e[29]=p(" 到 ")),t(v,{modelValue:a.formData.cAlarmTo,"onUpdate:modelValue":e[13]||(e[13]=r=>a.formData.cAlarmTo=r),class:"change-alarm",placeholder:"请输入最大告警值",controls:!1},null,8,["modelValue"]),e[30]||(e[30]=p(" 告警 "))]),_:1})):c("",!0),a.formData.isCycle?c("",!0):(u(),d(m,{key:4,label:"告警恢复"},{default:l(()=>[t(N,{placement:"right",width:"400",trigger:"click"},{reference:l(()=>[t(T,{class:"delete-orange"},{default:l(()=>e[33]||(e[33]=[p(" 手动设置 ")])),_:1})]),default:l(()=>[t(R,{"label-width":"110px"},{default:l(()=>[t(m,{label:"恢复类型"},{default:l(()=>[t(D,{modelValue:a.formData.restoreType,"onUpdate:modelValue":e[14]||(e[14]=r=>a.formData.restoreType=r),placeholder:"请选择告警类型",disabled:a.alarmDisabled},{default:l(()=>[(u(!0),g(M,null,b(a.alarmTypes,r=>(u(),d(n,{key:r.value,label:r.rLabel,value:r.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1}),a.formData.restoreType==="scope"?(u(),d(m,{key:0,label:"恢复触发最小值"},{default:l(()=>[t(v,{modelValue:a.formData.recoverMinValue,"onUpdate:modelValue":e[15]||(e[15]=r=>a.formData.recoverMinValue=r),placeholder:"请输入最小告警值",controls:!1},null,8,["modelValue"])]),_:1})):c("",!0),a.formData.restoreType==="scope"?(u(),d(m,{key:1,label:"恢复触发最大值"},{default:l(()=>[t(v,{modelValue:a.formData.recoverMaxValue,"onUpdate:modelValue":e[16]||(e[16]=r=>a.formData.recoverMaxValue=r),placeholder:"请输入最大告警值",controls:!1},null,8,["modelValue"])]),_:1})):c("",!0),a.formData.restoreType==="change"?(u(),d(m,{key:2,label:"变动恢复"},{default:l(()=>[e[31]||(e[31]=p(" 从 ")),t(v,{modelValue:a.formData.cRecoverFrom,"onUpdate:modelValue":e[17]||(e[17]=r=>a.formData.cRecoverFrom=r),placeholder:"请输入给定警值",controls:!1},null,8,["modelValue"])]),_:1})):c("",!0),a.formData.restoreType==="change"?(u(),d(m,{key:3,label:""},{default:l(()=>[e[32]||(e[32]=p(" 到 ")),t(v,{modelValue:a.formData.cRecoverTo,"onUpdate:modelValue":e[18]||(e[18]=r=>a.formData.cRecoverTo=r),placeholder:"请输入目标警值",controls:!1},null,8,["modelValue"])]),_:1})):c("",!0)]),_:1})]),_:1}),e[34]||(e[34]=h("span",{class:"restore-text"},"默认恢复为设置的同类型取反",-1))]),_:1})),a.formData.alarmType==="scope"?(u(),d(A,{key:5,class:"box-card msg"},{default:l(()=>e[35]||(e[35]=[h("span",null,[p("说明： "),h("p",null,"1.最大值与最小值最少填写一项")],-1),h("p",null,"2.若只填写最小值：监测数据 < 最小值, 则触发告警",-1),h("p",null,"3.若只填写最大值：监测数据 > 最大值, 则触发告警",-1),h("p",null," 4.若同时填写最小值与最大值：最小值 <监测数据< 最大值, 则触发告警 ",-1)])),_:1})):c("",!0)]),_:1},8,["model","rules"])]),_:1},8,["modelValue","onClose"])):c("",!0)}const De=_($,[["render",ae],["__scopeId","data-v-32f721ff"]]);export{De as default};
