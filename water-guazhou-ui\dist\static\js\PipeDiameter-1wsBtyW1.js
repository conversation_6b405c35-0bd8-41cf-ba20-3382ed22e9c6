import{d as F,c as k,r as _,b as L,Q as q,g as z,h as N,F as b,p as G,q as D,i as n,_ as O,X as P}from"./index-r0dFAfgr.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import{g as Q,a as V}from"./LayerHelper-Cn-iiqxI.js";import{g as $}from"./QueryHelper-ILO3qZqg.js";import{E as j}from"./StatisticsHelper-D-s_6AyQ.js";import{u as U}from"./arcWidgetButton-0glIxrt7.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import X from"./RightDrawerMap-D5PhmGFO.js";import Z from"./StatisticsCharts-CyK-dNnC.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./project-DUuzYgGl.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./pipe-nogVzCHG.js";import"./executeForIds-BLdIsxvI.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./ArcView-DpMnCY82.js";import"./ViewHelper-BGCZjxXH.js";import"./fieldconfig-Bk3o1wi7.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./ArcStationWarning-BF9YrSzF.js";/* empty css                         */import"./useWaterPoint-Bv0z6ym6.js";import"./DateFormatter-Bm9a68Ax.js";import"./zhandian-YaGuQZe6.js";import"./useStation-DJgnSZIA.js";import"./DrawerBox-CLde5xC8.js";import"./SideDrawer-CBntChyn.js";import"./PipeDetail-CTBPYFJW.js";import"./Search-NSrhrIa_.js";import"./FormTableColumnFilter-BT7pLXIC.js";import"./config-fy91bijz.js";import"./ArcBRTools-BO92yznB.js";import"./PipeLength.vue_vue_type_script_setup_true_lang-BZfUsvDf.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./wfsUtils-DXofo3da.js";import"./usePipelineGroup-Ba1pmWz_.js";import"./GroupLayer-DhhN3FyN.js";import"./lazyLayerLoader-DbM9sT1W.js";import"./WFSLayer-1TtJp3nx.js";import"./clientSideDefaults-VQhQaYxh.js";import"./QueryEngineCapabilities-Dk3NJwmm.js";import"./wfsUtils-Du031XaC.js";import"./geojson-MBFu2-HZ.js";import"./xmlUtils-CtUoQO7q.js";import"./ListWindow-WS05QqV0.js";import"./PopLayout-BP55MvL7.js";import"./PoiSearchV2-D7yNeLuv.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./URLHelper-B9aplt5w.js";import"./useLayerList-DmEwJ-ws.js";import"./useScaleBar-Beed-z91.js";import"./useDetector-BRcb7GRN.js";import"./useHighLight-DPevRAc5.js";import"./ToolHelper-BiiInOzB.js";import"./geoserverUtils-wjOSMa7E.js";import"./echart-BoVIcYbV.js";import"./config-DqqM5K5L.js";import"./OutsideWorkOrder-C6s8joBt.js";import"./index-CpGhZCTT.js";const se=F({__name:"PipeDiameter",setup(H){const f=k(),S=k(),c=k(),e=_({tabs:[],layerInfos:[],layerIds:[],loading:!1}),i={},R=_({group:[{fieldset:{desc:"绘制工具"},fields:[{type:"btn-group",btns:[{perm:!0,text:"",type:"default",size:"large",title:"绘制多边形",disabled:()=>e.loading,iconifyIcon:"mdi:shape-polygon-plus",click:()=>g("polygon")},{perm:!0,text:"",type:"default",size:"large",title:"绘制矩形",disabled:()=>e.loading,iconifyIcon:"ep:crop",click:()=>g("rectangle")},{perm:!0,text:"",type:"default",size:"large",title:"绘制圆形",disabled:()=>e.loading,iconifyIcon:"mdi:ellipse-outline",click:()=>g("circle")},{perm:!0,text:"",type:"default",size:"large",title:"清除图形",disabled:()=>e.loading,iconifyIcon:"ep:delete",click:()=>w()}]},{type:"btn-group",btns:[{perm:!0,text:"统计",styles:{width:"100%"},loading:()=>e.loading,click:()=>C()}]}]}],labelPosition:"top",gutter:12}),x=(r,t)=>{const o=r.label;y(o,t,!0)},E=(r,t)=>{const o=r.label;y(o,t)},v=(r,t)=>{const o=r.name;y(o,t)},y=async(r,t,o)=>{var p,s;if(r===void 0)return;const a=r.replace(/[a-z][A-Z]/gi,"");let m="";a==="合计"?m="1=1":m=a==="--"?"DIAMETER is null":"DIAMETER='"+a+"'",await C(m,t);const d=(p=c.value)==null?void 0:p.getCurLayer();(s=c.value)==null||s.refreshDetail(d,o??!0)},g=r=>{var t;w(),(t=i.sketch)==null||t.create(r)},w=()=>{var r;(r=i.graphicsLayer)==null||r.removeAll(),i.graphics=void 0},A=async()=>{var t,o;e.layerIds=V(i.view);const r=await P(e.layerIds);e.layerInfos=((o=(t=r.data)==null?void 0:t.result)==null?void 0:o.rows)||[],e.layerIds=e.layerInfos.filter(a=>a.geometrytype==="esriGeometryPolyline").map(a=>a.layerid)},C=async(r,t)=>{var o,a,m,d;L.info("正在统计，请稍候...");try{e.loading=!0;const p=t===void 0?e.layerIds:e.layerInfos.filter(l=>l.layername===t).map(l=>l.layerid),s=await $(p,e.layerInfos,{where:r||"1=1",geometry:(o=i.graphics)==null?void 0:o.geometry});if(t!==void 0)if(e.tabs.length){const l=e.tabs.find(h=>h.name===t),u=s.find(h=>h.name===t);l&&(l.data=u==null?void 0:u.data),(a=c.value)==null||a.refreshTable(t,p==null?void 0:p[0])}else e.tabs=s;else e.tabs=s;((m=f.value)==null?void 0:m.isCustomOpened())||(d=f.value)==null||d.toggleCustomDetail(!0)}catch(p){console.log(p),e.loading=!1,L.error("统计失败")}},{initSketch:M,destroySketch:B}=U(),I=r=>{i.graphics=r.graphics[0]},T=r=>{i.view=r,i.graphicsLayer=Q(i.view,{id:"search-pipe-diameter",title:"按口径统计管长"}),i.sketch=M(i.view,i.graphicsLayer,{createCallBack:I,updateCallBack:I}),A()};return q(()=>{var r,t;B(),(r=i.graphicsLayer)==null||r.removeAll(),(t=i.graphicsLayer)==null||t.destroy()}),(r,t)=>{const o=O;return z(),N(X,{ref_key:"refMap",ref:f,title:"按口径统计管长","full-content":!0,onMapLoaded:T},{"detail-header":b(()=>t[1]||(t[1]=[G("span",null,"统计结果",-1)])),"detail-default":b(()=>{var a;return[D(Z,{ref_key:"refStatisticsCharts",ref:c,view:i.view,"layer-ids":n(e).layerIds,"query-params":{where:"1=1",geometry:(a=i.graphics)==null?void 0:a.geometry},"statistics-params":{group_fields:["DIAMETER"],statistic_field:n(j).ShapeLen,statistic_type:"2"},tabs:n(e).tabs,prefix:"DN",unit:"m",onDetailRefreshed:t[0]||(t[0]=m=>n(e).loading=!1),onAttrRowClick:E,onRingClick:v,onBarClick:v,onTotalRowClick:x},null,8,["view","layer-ids","query-params","statistics-params","tabs"])]}),default:b(()=>[D(o,{ref_key:"refForm",ref:S,config:n(R)},null,8,["config"])]),_:1},512)}}});export{se as default};
