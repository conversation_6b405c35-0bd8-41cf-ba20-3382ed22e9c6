package org.thingsboard.server.dao.repair;

import org.thingsboard.server.common.data.User;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.RepairJobTriggerEntity;
import org.thingsboard.server.dao.model.sql.RepairPlanEntity;
import org.thingsboard.server.dao.model.sql.RepairPlanTriggerEntity;

import java.util.List;

public interface RepairPlanService {
    RepairPlanEntity detail(String id, User currentUser);

    PageData<RepairPlanEntity> findList(int page, int size, String name, String status, User currentUser);

    RepairPlanEntity savePlan(RepairPlanEntity entity, User currentUser);

    void executePlan(RepairPlanEntity entity, boolean create);

    void remove(List<String> ids);

    void changeStatus(String id);

    List<RepairPlanEntity> findPlanByType(String type);

    RepairPlanTriggerEntity findTrigger(String id);

    void buildTriggerJob(RepairPlanEntity plan);
}
