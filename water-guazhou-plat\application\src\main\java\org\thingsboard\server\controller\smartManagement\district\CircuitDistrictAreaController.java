package org.thingsboard.server.controller.smartManagement.district;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.controller.base.BaseController;;
import org.thingsboard.server.dao.model.sql.smartManagement.district.SMCircuitDistrictArea;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.district.CircuitDistrictAreaPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.district.CircuitDistrictAreaSaveRequest;
import org.thingsboard.server.dao.util.reflection.ExceptionUtils;
import org.thingsboard.server.dao.district.CircuitDistrictAreaService;
import org.thingsboard.server.utils.imodel.annotations.IStarController;

@IStarController
@RequestMapping("/api/sm/circuitDistrictArea")
public class CircuitDistrictAreaController extends BaseController {
    @Autowired
    private CircuitDistrictAreaService service;


    @GetMapping
    public IPage<SMCircuitDistrictArea> findAllConditional(CircuitDistrictAreaPageRequest request) {
        return service.findAllConditional(request);
    }

    @GetMapping("/{id}/points")
    public String getPoints(@PathVariable String id) {
        return service.getPoints(id);
    }

    @PostMapping
    public SMCircuitDistrictArea save(@RequestBody CircuitDistrictAreaSaveRequest req) {
        if (!service.isValidDistrictId(req.getDistrictId()))
            ExceptionUtils.silentThrow("所在区域非法");
        return service.save(req);
    }

    @PatchMapping("/{id}")
    public boolean edit(@RequestBody CircuitDistrictAreaSaveRequest req, @PathVariable String id) {
        return service.update(req.unwrap(id));
    }

    @DeleteMapping("/{id}")
    public boolean delete(@PathVariable String id) {
        return service.delete(id);
    }
}