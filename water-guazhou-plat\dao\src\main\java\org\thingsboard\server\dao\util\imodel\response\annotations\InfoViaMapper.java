package org.thingsboard.server.dao.util.imodel.response.annotations;

import joptsimple.internal.Strings;

import java.lang.annotation.*;

@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
@Documented
@Deprecated
public @interface InfoViaMapper {

    /**
     * 信息的key
     * 调用key的getter形式方法
     * 如name为treePath，则自动转换为getTreePath
     *
     * @return 信息的key
     */
    String name();

    /**
     * 使用指定Mapper来获取Id字段对应的名字
     *
     * @return 目标Mapper
     */
    Class<?> mapper();

    /**
     * 通常用于将查询出的Id转换为Name
     *
     * @return Mapper中的方法名
     */
    String nextName() default Strings.EMPTY;

    String prefix() default Strings.EMPTY;
}
