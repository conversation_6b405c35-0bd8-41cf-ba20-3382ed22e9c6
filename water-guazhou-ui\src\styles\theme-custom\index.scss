html {
  .el-table {
    // --el-table-header-bg-color: #F4F7FA;
    // --el-fill-color-lighter: #121d2c
  }
  .el-overlay-dialog {
    &::-webkit-scrollbar {
      display: none;
    }
  }
  .el-input {
    &.el-input--default {
      .el-input__inner {
        height: 30px !important;
      }
    }
  }
  .el-menu--popup,
  .el-sub-menu .el-menu-item{
    min-width: 100px;
  }
  .el-dialog__header {
    margin-right: 0;
    border-bottom: 1px solid #eee;
  }

  // tabs 切换选项卡
  .el-tabs--top.tabs-basic {
    .el-tabs__header {
      background: #f5f7fa;
      height: 56px;
      border: none;
      .el-tabs__nav-wrap .el-tabs__nav {
        height: 56px;
        border: none;
        .el-tabs__active-bar{
          display: none;
        }
        .el-tabs__item {
          border: none;
          height: 56px;
          line-height: 56px;
          width: 120px;
          text-align: center;
          padding: 0 20px;

          &.is-active {
            border-bottom: 2px solid #F69080;
            color: #F69080;
          }
        }
      }
    }

  }

  // .el-tabs {

  //   .el-tabs__nav-wrap {
  //     padding: 0 16px;
  //   }

  //   .el-tabs__nav-scroll {
  //     height: 100%;

  //     .el-tabs__nav {
  //       .el-tabs__item {
  //         font-size: 16px;
  //         font-weight: 500;
  //         text-align: center;

  //         &:hover,
  //         &:active {
  //           color: #409eff;
  //         }
  //       }

  //     }
  //   }


  //   .el-tabs__content {
  //     height: calc(100% - 40px);
  //     width: calc(100% - 20px);
  //     margin: 0 10px;
  //   }
  // }
}
