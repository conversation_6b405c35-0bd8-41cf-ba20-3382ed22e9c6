import{v as s,b as c,u as h,w as m}from"./MapView-DaoQedLH.js";import{w as x}from"./Point-WxyopZva.js";import"./index-r0dFAfgr.js";import"./widget-BcWKanF2.js";import"./pe-B8dP0-Ut.js";const V={convertToGEGeometry:p,exportPoint:g,exportPolygon:u,exportPolyline:f,exportMultipoint:l,exportExtent:M};function p(t,e){if(e==null)return null;let o="cache"in e?e.cache._geVersion:void 0;return o==null&&(o=t.convertJSONToGeometry(e),"cache"in e&&(e.cache._geVersion=o)),o}function g(t,e,o){const a=t.hasZ(e),n=t.hasM(e),i=new x({x:t.getPointX(e),y:t.getPointY(e),spatialReference:o});return a&&(i.z=t.getPointZ(e)),n&&(i.m=t.getPointM(e)),i.cache._geVersion=e,i}function u(t,e,o){const a=new s({rings:t.exportPaths(e),hasZ:t.hasZ(e),hasM:t.hasM(e),spatialReference:o});return a.cache._geVersion=e,a}function f(t,e,o){const a=new c({paths:t.exportPaths(e),hasZ:t.hasZ(e),hasM:t.hasM(e),spatialReference:o});return a.cache._geVersion=e,a}function l(t,e,o){const a=new h({hasZ:t.hasZ(e),hasM:t.hasM(e),points:t.exportPoints(e),spatialReference:o});return a.cache._geVersion=e,a}function M(t,e,o){const a=t.hasZ(e),n=t.hasM(e),i=new m({xmin:t.getXMin(e),ymin:t.getYMin(e),xmax:t.getXMax(e),ymax:t.getYMax(e),spatialReference:o});if(a){const r=t.getZExtent(e);i.zmin=r.vmin,i.zmax=r.vmax}if(n){const r=t.getMExtent(e);i.mmin=r.vmin,i.mmax=r.vmax}return i.cache._geVersion=e,i}export{V as hydratedAdapter};
