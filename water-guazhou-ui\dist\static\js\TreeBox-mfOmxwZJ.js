import{d as p,r as f,g as a,n as _,p as s,ax as l,i as t,h as c,cW as u,cX as m,aw as r,j as S,C as k}from"./index-r0dFAfgr.js";const h=p({__name:"TreeBox",emits:["collapse"],setup(v,{emit:i}){const n=i,e=f({treeState:!1}),d=()=>{e.treeState=!e.treeState,n("collapse",e.treeState)};return(o,B)=>(a(),_("div",{class:r(["layout-tree-detail-container",{dark:t(S)().isDark}])},[s("div",{class:r(["left-project-tree-list",{"tree-hidden":t(e).treeState}])},[l(o.$slots,"tree",{},void 0,!0),s("p",{class:"control-fold-btn",onClick:d},[t(e).treeState?(a(),c(t(u),{key:0})):(a(),c(t(m),{key:1}))])],2),s("div",{class:r(["right-detail-box",{"fill-width":t(e).treeState}])},[l(o.$slots,"default",{},void 0,!0)],2)],2))}}),C=k(h,[["__scopeId","data-v-c96a45d2"]]);export{C as default};
