package org.thingsboard.server.dao.smartPipe;

import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.request.PartitionMountRequest;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.PartitionMount;

import java.util.List;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-04-25
 */
public interface PartitionMountService {

    PartitionMount save(PartitionMount partitionMount);

    /**
     * 变更水流方向
     * @param id
     * @return
     */
    String changeDirection(String id);

    /**
     * 获取挂载列表
     * @param request
     * @return
     */
    PageData<PartitionMount> getList(PartitionMountRequest request);

    /**
     * 批量挂载
     * @param partitionMountList
     * @param tenantId
     * @return
     */
    List batchSave(List<PartitionMount> partitionMountList, String tenantId);

    /**
     * 移出
     * @param ids
     * @return
     */
    void delete(List<String> ids);
}
