<template>
  <div
    class="esri-widget esri-expand esri-component esri-widget--button custom-toolbar"
    @click="handleClick"
  >
    <el-icon
      :size="16"
      class="tool-icon"
    >
      <Icon
        v-if="state.collapsed"
        :icon="icon"
      ></Icon>
      <DArrowRight v-else />
    </el-icon>
  </div>
</template>
<script lang="ts" setup>
import { DArrowRight } from '@element-plus/icons-vue'
import { Icon } from '@iconify/vue'

const emit = defineEmits<{(e: 'click', isCollapsed: boolean) }>()
defineProps<{
  icon: any
}>()
const state = reactive<{
  collapsed: boolean
}>({
  collapsed: true
})
const toggle = (isCollapsed?: boolean) => {
  state.collapsed = isCollapsed !== undefined ? isCollapsed : !state.collapsed
}
const handleClick = () => {
  state.collapsed = !state.collapsed
  emit('click', state.collapsed)
}
defineExpose({
  toggle
})
</script>
<style lang="scss" scoped>
.custom-toolbar {
  line-height: 32px;
  text-align: center;
  display: flex;

  .tool-icon {
    margin: auto;
  }
}
</style>
