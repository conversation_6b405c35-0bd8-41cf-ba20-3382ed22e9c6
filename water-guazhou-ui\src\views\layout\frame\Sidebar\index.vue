<template>
  <div class="sidebar-container" :class="{ dark: useAppStore().isDark }">
    <el-scrollbar class="side-menu-scrollbar" wrap-class="scrollbar-wrapper">
      <el-menu
        v-if="state.showMenu"
        :key="expandDefault[0]"
        ref="refMenu"
        mode="vertical"
        router
        :show-timeout="200"
        :default-active="state.activePath"
        :unique-opened="true"
        :default-openeds="expandDefault"
      >
        <template v-for="(route, index) in usePermissionStore().routers">
          <!-- 只处理二级及以上的，因为第一级为布局 -->
          <sidebar-item
            v-if="route.children?.length"
            :key="index"
            :item="route"
            :is-nest="false"
            :icon="route.meta?.icon"
          ></sidebar-item>
        </template>
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script lang="ts" setup>
import { computed, onMounted, reactive, ref, watch } from 'vue';
import { useRouter } from 'vue-router';
import { useAppStore, usePermissionStore } from '@/store';
import SidebarItem from './SidebarItem.vue';
import { IELMenu } from '@/common/types/element-plus';

const router = useRouter();
const refMenu = ref<IELMenu>();
const state = reactive<{
  activePath: string;
  showMenu: boolean;
}>({
  activePath: router.currentRoute.value.fullPath,
  showMenu: false
});
watch(
  () => router.currentRoute.value.fullPath,
  () => {
    if (state.activePath !== router.currentRoute.value.fullPath) {
      state.activePath = router.currentRoute.value.fullPath;
    }
  }
);
const expandDefault = computed(
  () =>
    [
      usePermissionStore().routers.filter((item) => item.hidden !== true)[0]?.path
    ] || []
);

onMounted(() => {
  state.showMenu = true;
});
</script>

<style lang="scss" scoped>
.sidebar-container {
  user-select: none;
  border-right: 1px solid var(--el-border-color);
  height: 100%;
  width: 200px;
  &.dark {
    background-color: #1d1f2e;
    border: none;
  }
}
.el-menu {
  border: none;
  height: 100%;
  width: 200px;
}
</style>
