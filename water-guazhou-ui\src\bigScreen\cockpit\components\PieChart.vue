<template>
  <div class="pie-chart-container" ref="chartContainer"></div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  // 图表数据
  data: {
    type: Array,
    default: () => [
      { name: '泵房1', value: 150, percentage: '15%', color: '#FF5252' },
      { name: '泵房2', value: 200, percentage: '20%', color: '#FFC61A' },
      { name: '泵房3', value: 300, percentage: '30%', color: '#47EBEB' },
      { name: '泵房4', value: 350, percentage: '35%', color: '#3D7EFF' }
    ]
  },
  // 中心标题
  centerTitle: {
    type: String,
    default: '泵房生产'
  },
  // 中心数值
  centerValue: {
    type: String,
    default: '176'
  },
  // 中心单位
  centerUnit: {
    type: String,
    default: '万m³'
  },
  // 图表高度
  height: {
    type: String,
    default: '100%'
  },
  // 图表宽度
  width: {
    type: String,
    default: '100%'
  },
  // 环形图内径占比
  innerRadius: {
    type: String,
    default: '55%'
  },
  // 环形图外径占比
  outerRadius: {
    type: String,
    default: '85%'
  }
})

const chartContainer = ref(null)
let chartInstance = null

// 初始化图表
const initChart = () => {
  if (!chartContainer.value) return
  
  // 销毁之前的实例
  if (chartInstance) {
    chartInstance.dispose()
  }

  // 创建echarts实例
  chartInstance = echarts.init(chartContainer.value)
  
  // 更新图表
  updateChart()
  
  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)
}

// 更新图表
const updateChart = () => {
  if (!chartInstance) return
  
  // 计算装饰圆环的半径
  // 提取数字部分
  const innerRadius = parseInt(props.innerRadius.replace('%', ''));
  const outerRadius = parseInt(props.outerRadius.replace('%', ''));
  // 内圈装饰圈
  const innerDecorRadius = (innerRadius - 8) + '%';
  const innerDecorRadiusInner = (innerRadius - 9) + '%'; // 1px宽度
  // 外圈装饰圈
  const outerDecorRadius = (outerRadius + 8) + '%';
  const outerDecorRadiusInner = (outerRadius + 7) + '%'; // 1px宽度

  // 准备数据
  const seriesData = props.data.map(item => ({
    value: item.value,
    name: item.name,
    percentage: item.percentage,
    itemStyle: {
      color: item.color
    },
    // 为每个数据项单独设置labelLine样式
    labelLine: {
      lineStyle: {
        color: item.color
      }
    }
  }))
  
  const option = {
    grid: {
      top: '15%',
      left: '0%',
      right: '0%',
      bottom: '15%',
      containLabel: true
    },
    tooltip: {
      show: true,
      trigger: 'item',
      backgroundColor: 'rgba(0, 19, 40, 0.8)',
      borderColor: 'rgba(26, 198, 255, 0.2)',
      borderWidth: 1,
      padding: [10, 15],
      textStyle: {
        color: '#fff',
        fontSize: 14
      },
      formatter: params => {
        // 只对主饼图数据显示tooltip
        if (params.seriesIndex !== 2) return;
        
        return `
          <div style="display:flex;align-items:center;margin-bottom:5px;">
            <span style="display:inline-block;width:8px;height:8px;border-radius:50%;background-color:${params.color};margin-right:8px;"></span>
            <span style="font-size:14px;color:#ffffff;font-weight:bold;">${params.name}</span>
          </div>
          <div style="margin-left:16px;line-height:20px;">
            <div style="display:flex;justify-content:space-between;margin-bottom:4px;">
              <span style="color:#ADE6EB;">数值：</span>
              <span style="color:#ffffff;font-weight:bold;">${params.data.value}</span>
            </div>
            <div style="display:flex;justify-content:space-between;">
              <span style="color:#ADE6EB;">占比：</span>
              <span style="color:#ffffff;font-weight:bold;">${params.data.percentage}</span>
            </div>
          </div>
        `;
      }
    },
    legend: {
      show: true,
      orient: 'vertical',
      right: '0%',
      top: 'center',
      itemWidth: 8,
      itemHeight: 8,
      icon: 'circle',
      formatter: name => {
        const item = props.data.find(d => d.name === name)
        return `{a|${item.name}}`
      },
      textStyle: {
        rich: {
          a: {
            fontSize: 12,
            color: '#fff',
            padding: [0, 0, 0, 5]
          }
        }
      }
    },
    series: [
      // 内圈装饰圆环
      {
        type: 'pie',
        radius: [innerDecorRadiusInner, innerDecorRadius],
        center: ['40%', '50%'],
        silent: true, // 不响应事件
        tooltip: {
          show: false
        },
        label: {
          show: false
        },
        animation: false,
        hoverAnimation: false,
        data: [{
          value: 1,
          itemStyle: {
            color: 'rgba(26, 198, 255, 0.3)', // 添加0.3透明度
            borderWidth: 0
          }
        }]
      },
      // 外圈装饰圆环
      {
        type: 'pie',
        radius: [outerDecorRadiusInner, outerDecorRadius],
        center: ['40%', '50%'],
        silent: true, // 不响应事件
        tooltip: {
          show: false
        },
        label: {
          show: false
        },
        animation: false,
        hoverAnimation: false,
        data: [{
          value: 1,
          itemStyle: {
            color: 'rgba(26, 198, 255, 0.3)', // 添加0.3透明度
            borderWidth: 0
          }
        }]
      },
      // 主数据圆环
      {
        name: props.centerTitle,
        type: 'pie',
        radius: [props.innerRadius, props.outerRadius],
        center: ['40%', '50%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderColor: 'rgba(0, 0, 0, 0.1)',
          borderWidth: 1
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(26, 198, 255, 0.5)'
          },
          scale: true,
          scaleSize: 5
        },
        label: {
          show: true,
          position: 'outside',
          formatter: params => {
            // 使用特殊方式设置颜色，通过formatter返回带样式的文本
            const color = params.color || (params.data && params.data.itemStyle && params.data.itemStyle.color);
            // 对每个数据项使用独立的样式名称，确保颜色正确
            return [
              `{a${params.dataIndex}|${params.data.percentage}}`,
              `{b|${params.data.value}}`
            ].join('\n');
          },
          rich: {
            b: {
              fontSize: 14,
              color: '#ADE6EB',
              lineHeight: 14
            }
          },
          alignTo: 'labelLine',
          edgeDistance: '10%',
          distanceToLabelLine: 5,
          minMargin: 5
        },
        labelLine: {
          show: true,
          length: 10,
          length2: 30,
          smooth: true
        },
        labelLayout: {
          hideOverlap: true,
          moveOverlap: 'shiftY'
        },
        data: seriesData
      }
    ]
  }
  
  // 生成series前，为每个数据项的rich配置添加动态样式
  props.data.forEach((item, index) => {
    // 为每个数据项创建独立的富文本样式
    option.series[2].label.rich[`a${index}`] = {
      fontSize: 20,
      color: item.color,
      fontWeight: 'bold',
      lineHeight: 20
    };
  });
  
  // 使用DOM元素添加中心文字，而不是图表元素
  chartInstance.setOption(option)
  
  // 在图表渲染完成后添加中心文字
  setTimeout(() => {
    // 移除可能存在的旧元素
    const oldCenter = chartContainer.value.querySelector('.pie-chart-center-text')
    if (oldCenter) {
      oldCenter.remove()
    }
    
    // 创建新的中心文字容器
    const centerTextDiv = document.createElement('div')
    centerTextDiv.className = 'pie-chart-center-text'
    centerTextDiv.style.position = 'absolute'
    centerTextDiv.style.left = '40%'
    centerTextDiv.style.top = '50%'
    centerTextDiv.style.transform = 'translate(-50%, -50%)'
    centerTextDiv.style.textAlign = 'center'
    centerTextDiv.style.zIndex = '10'
    
    // 创建数值和单位行
    const valueRow = document.createElement('div')
    valueRow.style.whiteSpace = 'nowrap'
    
    // 添加数值
    const valueSpan = document.createElement('span')
    valueSpan.textContent = props.centerValue
    valueSpan.style.color = '#fff'
    valueSpan.style.fontSize = '24px'
    valueSpan.style.fontWeight = 'bold'
    
    // 添加单位
    const unitSpan = document.createElement('span')
    unitSpan.textContent = props.centerUnit
    unitSpan.style.color = '#fff'
    unitSpan.style.fontSize = '14px'
    unitSpan.style.marginLeft = '5px'
    
    // 添加标题
    const titleDiv = document.createElement('div')
    titleDiv.textContent = props.centerTitle
    titleDiv.style.color = '#fff'
    titleDiv.style.fontSize = '14px'
    titleDiv.style.marginTop = '5px'
    
    // 组装元素
    valueRow.appendChild(valueSpan)
    valueRow.appendChild(unitSpan)
    centerTextDiv.appendChild(valueRow)
    centerTextDiv.appendChild(titleDiv)
    
    // 添加到容器
    chartContainer.value.appendChild(centerTextDiv)
  }, 100)
}

// 处理窗口大小变化
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
    // 重新调整中心文字位置
    updateChart()
  }
}

// 监听数据变化
watch(() => props.data, () => {
  updateChart()
}, { deep: true })

// 监听中心文本变化
watch(() => [props.centerTitle, props.centerValue, props.centerUnit], () => {
  updateChart()
})

// 组件挂载后初始化图表
onMounted(() => {
  initChart()
})

// 组件卸载前销毁图表
onUnmounted(() => {
  if (chartInstance) {
    window.removeEventListener('resize', handleResize)
    chartInstance.dispose()
    chartInstance = null
  }
})
</script>

<style lang="scss" scoped>
.pie-chart-container {
  width: v-bind('width');
  height: v-bind('height');
  background-color: transparent;
  position: relative;
}
</style> 