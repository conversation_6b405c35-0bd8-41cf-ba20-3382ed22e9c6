<template>
  <div class="navbar-container" :class="{ dark: appStore.isDark }">
    <div class="navbar" mode="horizontal">
      <div class="left-box">
        <div class="menu-logo unified-theme-bg-color">
          <img :src="appStore.logo" alt="" />
        </div>
        <div v-if="alarmView" class="title-box">
          <p class="title">
            {{ appStore.appname }}
          </p>
        </div>
      </div>
      <div id="fill" class="fill">
        <div v-if="state.bulletinLast.title === '' && !alarmView">
          <span class="login-info"
            >上次登录时间：{{ state.lastLoginDate }}</span
          >
          <span class="login-info margin-l"
            >上次登录IP：{{ state.lastLoginIp }}</span
          >
        </div>
        <AlarmScrollView v-if="false" />
        <Horizontal v-if="alarmView"></Horizontal>
      </div>
      <div class="right-box">
        <p v-if="useBusinessStore().usePortal" class="return-app" @click="JumpApp">
          <i class="el-icon-s-home"></i> 返回门户
        </p>
        <!-- 告警 -->
        <el-badge v-if="alarmShow" class="alarm" :value="state.alarm">
          <Bell @click="jumpToAlarm" />
        </el-badge>

        <!-- 项目范围选择 -->
        <!-- <ChangeProject
          v-if="
            alarmView && !store.business.usePortal && store.business.useprojectapp
          "
        ></ChangeProject> -->
        <!-- <ChangeProject v-show="false"></ChangeProject> -->
        <!-- 账号信息 -->
        <el-dropdown class="avatar-container" trigger="click">
          <div class="avatar-wrapper">
            <span>{{ userStore.user?.name }}</span>
            <img class="user-avatar" :src="userLog" />
          </div>
          <template #dropdown>
            <el-dropdown-menu class="user-dropdown">
              <router-link class="inlineBlock" to="/accountManage/index">
                <el-dropdown-item>
                  <span>账户管理</span>
                </el-dropdown-item>
                <el-dropdown-item>
                  <ChangeTheme></ChangeTheme>
                </el-dropdown-item>
              </router-link>
              <el-dropdown-item divided>
                <el-button
                  class="long-btn"
                  size="small"
                  :text="true"
                  @click="logout"
                >
                  登出
                </el-button>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useRouter } from 'vue-router';
import { Bell } from '@element-plus/icons-vue';
import { useAppStore, useBusinessStore, usePermissionStore, useUserStore } from '@/store';
import AlarmScrollView from './alarmScrollView.vue';
import { getLastLogin } from '@/api/login';
import { getLogo } from '@/api/tenant';
import userLog from '@/assets/images/user-log.png';

import Horizontal from './Horizontal/index.vue';
import ChangeTheme from './ChangeTheme.vue';
// import ChangeProject from './ChangeProject.vue'
import { hasPermission } from '@/utils/RouterHelper';

const appStore = useAppStore();
const userStore = useUserStore();
const router = useRouter();
const state = reactive<{
  userLog: string;
  userName: string;
  alarm: number;
  customColor: boolean;
  currentTime: string;
  lastLoginDate: string;
  lastLoginIp: string;
  bulletinScroll: any;
  bulletinLast: {
    title: string;
    body: any;
  };
  audioMute: boolean;
}>({
  userLog,
  userName: useUserStore().name,
  alarm: 0,
  customColor: true,
  currentTime: '',
  lastLoginDate: '',
  lastLoginIp: '',
  bulletinScroll: null,
  bulletinLast: {
    title: '',
    body: ''
  },
  audioMute: true
});
const alarmShow = computed(() => {
  const isSysAdmin =
    userStore.roles.includes('SYS_ADMIN') &&
    userStore.roles.includes('TENANT_SYS');
  if (isSysAdmin) return false;
  return usePermissionStore().addRouters.some(
    (item) => item.label === '告警管理'
  );
});

const jumpToAlarm = () => {
  router.push({
    path: '/HVACPage/realTimeAlarm/index/ah'
  });
};
const JumpApp = () => {
  router.push('/app');
};
const logout = async () => {
  userStore.LogOut();
};
const getLastLoginInfo = async () => {
  try {
    const res = await getLastLogin();
    state.lastLoginDate = res.data.lastLoginDate;
    state.lastLoginIp = res.data.lastLoginIp;
  } catch (error: any) {
    console.log(error.message);
  }
};
const alarmView = computed(() => !hasPermission(['SYS_ADMIN']));
onBeforeMount(() => {
  state.bulletinScroll && clearInterval(state.bulletinScroll);
});
onMounted(async () => {
  try {
    const res = await getLogo();
    appStore.ToggleLogo(res.data);
  } catch (error: any) {
    console.log(error.message);
  }
  appStore.SetAppVersion();
  !alarmView.value && getLastLoginInfo();
});
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.navbar-container {
  user-select: none;
  height: 60px;
  background-color: rgb(162, 160, 160);
  background-image: url('@/assets/images/header.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  &.dark {
    background-color: #353c49;
    opacity: 0.8;
  }
}
.navbar {
  display: flex;
  // background: #2e303e;
  border-radius: 0 !important;
  justify-content: space-between;
}
.left-box {
  display: flex;

  .title-box {
    padding-left: 20px;
    color: #fff;
    // min-width: 150px;
    text-align: center;
    // padding: 0 10px;
    // width: 300px;
    .title {
      // margin: 17px 0 0 0;
      line-height: 60px;
      font-size: 28px;
      font-family: STXinwei;
      font-weight: 500;
      // text-shadow: 0 0 20px rgba(0, 255, 255, 0.8);
    }
    .detail {
      margin: 0 0 7px 0;
      font-size: 12px;
      line-height: 16px;
      letter-spacing: 1.5px;
    }
  }
}
.fill {
  flex: 1;
  text-align: center;
  overflow: hidden;
  padding: 0 8px;
  // background-color: #0d1249;
  white-space: nowrap;
  display: flex;
  .login-info {
    color: #fff;
    font-size: 14px;
    line-height: 60px;
  }
  .margin-l {
    margin-left: 30px;
  }
  .bulletin {
    margin: 0 0;
    height: 100%;
    font-size: 14px;
  }
  .bulletin-title {
    color: #ffeb00;
    font-weight: 600;
  }
  .bulletin-browsing {
    height: 100%;
    // display: flex;
    overflow: hidden;
    white-space: nowrap;
    #scroll_begin,
    #scroll_end {
      display: inline;
    }
    .body {
      color: #ffe088;
    }
    .scroll-body {
      margin-left: 80px;
    }
  }
}
.right-box {
  display: flex;
  align-items: center;
  .min-name {
    font-size: 24px;
    font-weight: 600;
    color: #1bacff;
    margin-right: 1.5rem;
  }
  .sys-sign-btn {
    color: #fff;
    padding: 0 10px;
    margin: 0 1.5rem 0 0;
    // background-color: #FFB800;
    border: 1px solid #fff700;
    line-height: 30px;
    border-radius: 5px;
    cursor: pointer;
    &:hover,
    &:active,
    &:focus {
      background-color: rgb(245, 176, 2);
    }
  }

  .return-app {
    cursor: pointer;
    color: #fff;
    margin: 0 8px;
    font-size: 14px;
  }
  .current-time {
    color: #fff;
    margin: 0 1.5rem 0 0;
  }
  .downloadApp {
    color: #fff;
    margin: 0 1.5rem 0 0;
    cursor: pointer;
    font-size: 14px;
    > i.iconfont::before {
      font-size: 16px;
    }
  }
  .alarm {
    margin-right: 12px;
    width: 1em;
    height: 1em;
    sup {
      top: 15px;
    }
  }
  // .alarm-icon {
  //   color: #fff;
  // }
  .page-theme {
    margin-right: 1.5rem;
    display: flex;
    height: 50px;
    align-items: center;
    .theme-change {
      .el-switch__label.is-active {
        color: #fff;
      }
    }
  }
  .avatar-container {
    .avatar-wrapper {
      margin-right: 30px;
      color: #fff;
      cursor: pointer;
      // margin-top: 5px;
      position: relative;
      height: 50px;
      display: flex;
      align-items: center;
      .user-avatar {
        width: 30px;
        height: 30px;
        margin-left: 10px;
        border-radius: 50%;
      }
      .el-icon-caret-bottom {
        position: absolute;
        right: -20px;
        top: 20px;
        font-size: 12px;
      }
    }
  }
}
.menu-logo {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 200px;
  padding-left: 4px;
  img {
    width: 100%;
    height: 54px;
  }
}

.long-btn {
  width: 100%;
}
</style>
