package org.thingsboard.server.dao.model.sql.assay;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;
import java.util.Date;

/**
 * 水质-化验报告-报告化验项配置
 */
@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.ASSAY_REPORT_TYPE_TABLE)
@TableName(ModelConstants.ASSAY_REPORT_TYPE_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class AssayReportType {

    @Id
    @TableId
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @TableField(value = ModelConstants.ASSAY_REPORT_TYPE_NAME)
    private String name;

    @TableField(value = ModelConstants.ASSAY_REPORT_TYPE_FILE)
    private String file;

    @TableField(value = ModelConstants.REMARK)
    private String remark;

    @TableField(value = ModelConstants.CREATE_TIME)
    private Date createTime;

    @TableField(value = ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;


}
