package org.thingsboard.server.common.data.device;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class DeviceTemplateAndProtocol {

    private String id;

    private String name;

    private String remark;

    private String additionalInfo;

    private Long createTime;

    private String tenantId;

    private String type;

    private List<Object> protocolList;

}
