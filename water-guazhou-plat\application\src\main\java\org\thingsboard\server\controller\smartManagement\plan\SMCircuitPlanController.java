package org.thingsboard.server.controller.smartManagement.plan;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.controller.base.BaseController;;
import org.thingsboard.server.dao.model.sql.smartManagement.plan.SMCircuitPlanResponse;
import org.thingsboard.server.dao.model.sql.smartManagement.plan.SMCircuitPlan;
import org.thingsboard.server.dao.model.sql.smartManagement.plan.SMCircuitTaskPlanRequest;
import org.thingsboard.server.dao.model.sql.smartManagement.plan.SMPlanCircle;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.plan.SMCircuitPlanPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.plan.SMCircuitPlanSaveRequest;
import org.thingsboard.server.dao.util.reflection.ExceptionUtils;
import org.thingsboard.server.dao.plan.SMCircuitPlanService;
import org.thingsboard.server.utils.imodel.annotations.IStarController;

import java.util.List;

@IStarController
@RequestMapping("/api/sm/circuitPlan")
public class SMCircuitPlanController extends BaseController {
    @Autowired
    private SMCircuitPlanService service;


    @GetMapping
    public IPage<SMCircuitPlanResponse> findAllConditional(SMCircuitPlanPageRequest request) {
        return service.findAllConditional(request);
    }

    @PostMapping("/plan")
    public int plan(@RequestBody SMCircuitTaskPlanRequest req) {
        if (service.isPlanArranged(req.getPlanId(), req.getBeginTime(), req.getEndTime()))
            ExceptionUtils.silentThrow("与已有计划任务时间重合");
        return service.plan(req);
    }

    @GetMapping("/circleList")
    public List<String> getIntervalList() {
        return SMPlanCircle.nameList;
    }

    @PostMapping
    public SMCircuitPlan save(@RequestBody SMCircuitPlanSaveRequest req) {
        return service.save(req);
    }

    @PatchMapping("/{id}")
    public boolean edit(@RequestBody SMCircuitPlanSaveRequest req, @PathVariable String id) {
        return service.update(req.unwrap(id));
    }

    @DeleteMapping("/{id}")
    public boolean delete(@PathVariable String id) {
        return service.delete(id);
    }

    @DeleteMapping
    public boolean delete(@RequestBody List<String> idList) {
        return service.deleteAll(idList);
    }
}