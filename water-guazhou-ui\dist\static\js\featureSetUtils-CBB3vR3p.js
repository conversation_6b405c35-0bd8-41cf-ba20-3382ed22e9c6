import{U as G,r as le}from"./pe-B8dP0-Ut.js";import{f as N,o as b,n as C,s as v,t as x}from"./WhereClause-CNjGNHY9.js";import{v as A,t as y,m as O,p as $,a as R,g as Ie,L as be,T as J,h as oe,R as Ce,N as _e,b as De,A as Q,S as Re,c as q,d as Te,f as ke,e as g}from"./SpatialFilter-LhUD8pWs.js";import{al as ee,am as S,a2 as ve,af as k,w as xe,as as M,at as te,au as Ne,av as z,b as Ae,aw as Le}from"./arcadeUtils-1twpZNeO.js";import{b as ie,a3 as Oe,a2 as Ee}from"./Point-WxyopZva.js";import{g as P,c7 as Pe,cU as j,g$ as ye,h0 as ge,db as L,fp as qe,bH as je,cn as Ge,h1 as ue,H as T,ar as Ue,dw as de,gb as Be,ee as Me,e2 as he,aH as We}from"./MapView-DaoQedLH.js";import{c as ce}from"./arcadeTimeUtils-CyWQANWo.js";import{a$ as W,eZ as $e}from"./index-r0dFAfgr.js";import{n as Qe,s as Ve}from"./executeForIds-BLdIsxvI.js";let me=class{constructor(){this.declaredRootClass="esri.arcade.featureSetCollection",this._layerById={},this._layerByName={}}add(e,t,i){this._layerById[t]=i,this._layerByName[e]=i}async featureSetByName(e,t=!0,i=["*"]){return this._layerByName[e]===void 0?null:this._layerByName[e]}async featureSetById(e,t=!0,i=["*"]){return this._layerById[e]===void 0?null:this._layerById[e]}castToText(e=!1){return"object, FeatureSetCollection"}};class U extends A{constructor(e){super(e),this.declaredClass="esri.arcade.featureset.actions.AttributeFilter",this._maxProcessing=1e3,this._parent=e.parentfeatureset,e.whereclause instanceof N?(this._whereclause=e.whereclause,this._whereClauseFunction=null):(this._whereClauseFunction=e.whereclause,this._whereclause=null)}_initialiseFeatureSet(){this._parent!==null?(this.fields=this._parent.fields.slice(0),this.geometryType=this._parent.geometryType,this.objectIdField=this._parent.objectIdField,this.globalIdField=this._parent.globalIdField,this.spatialReference=this._parent.spatialReference,this.hasM=this._parent.hasM,this.hasZ=this._parent.hasZ,this.typeIdField=this._parent.typeIdField,this.types=this._parent.types):(this.fields=[],this.typeIdField="",this.objectIdField="",this.globalIdField="",this.spatialReference=new ie({wkid:4326}),this.geometryType=ee.point)}async _getSet(e){if(this._wset===null){await this._ensureLoaded();const t=await this._parent._getFilteredSet("",null,this._whereclause,null,e);return this._checkCancelled(e),this._whereClauseFunction!==null?this._wset=new y(t._candidates.slice(0).concat(t._known.slice(0)),[],t._ordered,this._clonePageDefinition(t.pagesDefinition)):this._wset=new y(t._candidates.slice(0),t._known.slice(0),t._ordered,this._clonePageDefinition(t.pagesDefinition)),this._wset}return this._wset}_isInFeatureSet(e){var i;let t=(i=this._parent)==null?void 0:i._isInFeatureSet(e);return t===S.NotInFeatureSet?t:(t=this._idstates[e],t===void 0?S.Unknown:t)}_getFeature(e,t,i){return this._parent._getFeature(e,t,i)}_getFeatures(e,t,i,s){return this._parent._getFeatures(e,t,i,s)}_featureFromCache(e){return this._parent._featureFromCache(e)}executeWhereClause(e){var t;return((t=this._whereclause)==null?void 0:t.testFeature(e))??!1}async executeWhereClauseDeferred(e){if(this._whereClauseFunction!==null){const t=this._whereClauseFunction(e);return Oe(t),t}return this.executeWhereClause(e)}async _fetchAndRefineFeatures(e,t,i){var n,l,u;const s=new y([],e,!1,null),a=Math.min(t,e.length);if(await((n=this._parent)==null?void 0:n._getFeatures(s,-1,a,i)),this._checkCancelled(i),this._whereClauseFunction==null){for(let o=0;o<a;o++){const d=(l=this._parent)==null?void 0:l._featureFromCache(e[o]);this.executeWhereClause(d)===!0?this._idstates[e[o]]=S.InFeatureSet:this._idstates[e[o]]=S.NotInFeatureSet}return"success"}const r=[];for(let o=0;o<a;o++){const d=(u=this._parent)==null?void 0:u._featureFromCache(e[o]);r.push(await this.executeWhereClauseDeferred(d))}for(let o=0;o<t;o++)r[o]===!0?this._idstates[e[o]]=S.InFeatureSet:this._idstates[e[o]]=S.NotInFeatureSet;return"success"}async _getFilteredSet(e,t,i,s,a){this._whereClauseFunction!==null||(i!==null?this._whereclause!==null&&(i=O(this._whereclause,i)):i=this._whereclause),await this._ensureLoaded();const r=await this._parent._getFilteredSet(e,t,i,s,a);let n;return this._checkCancelled(a),n=this._whereClauseFunction!==null?new y(r._candidates.slice(0).concat(r._known.slice(0)),[],r._ordered,this._clonePageDefinition(r.pagesDefinition)):new y(r._candidates.slice(0),r._known.slice(0),r._ordered,this._clonePageDefinition(r.pagesDefinition)),n}async _stat(e,t,i,s,a,r,n){if(this._whereClauseFunction!==null)return a===null&&i===""&&s===null?this._manualStat(e,t,r,n):{calculated:!1};let l=this._whereclause;a!==null&&this._whereclause!==null&&(l=O(this._whereclause,a));const u=await this._parent._stat(e,t,i,s,l,r,n);return u.calculated===!1?a===null&&i===""&&s===null?this._manualStat(e,t,r,n):{calculated:!1}:u}async _canDoAggregates(e,t,i,s,a){return this._whereClauseFunction===null&&(a!==null?this._whereclause!==null&&(a=O(this._whereclause,a)):a=this._whereclause,this._parent!==null&&this._parent._canDoAggregates(e,t,i,s,a))}async _getAggregatePagesDataSourceDefinition(e,t,i,s,a,r,n){if(this._parent===null)throw new b(C.NeverReach);return a!==null?this._whereclause!==null&&(a=O(this._whereclause,a)):a=this._whereclause,this._parent._getAggregatePagesDataSourceDefinition(e,t,i,s,a,r,n)}static registerAction(){A._featuresetFunctions.filter=function(e){if(typeof e=="function")return new U({parentfeatureset:this,whereclause:e});let t=null;return e instanceof N&&(t=e),new U({parentfeatureset:this,whereclause:t})}}}class V{constructor(e){this.field=e,this.sqlRewritable=!1}postInitialization(e,t){}}let K=class extends V{constructor(e){super(e),this.sqlRewritable=!0}extractValue(e){return e.attributes[this.field.name]}rewriteSql(e){return{rewritten:this.sqlRewritable,where:e}}};class Je extends V{constructor(e,t,i){super(ve(e)),this.originalField=e,this.sqlRewritable=!0,this.field.name=t,this.field.alias=i}rewriteSql(e,t){return{rewritten:this.sqlRewritable,where:$(e,this.field.name,this.originalField.name,t.getFieldsIndex())}}extractValue(e){return e.attributes[this.originalField.name]}}let ze=class w extends V{constructor(e,t,i){super(e),this.codefield=t,this.lkp=i,this.reverseLkp={};for(const s in i)this.reverseLkp[i[s]]=s;this.sqlRewritable=!0}rewriteSql(e,t){const i=this.evaluateNodeToWhereClause(e.parseTree,k.Standardised,this.field.name,this.codefield instanceof N?R(this.codefield,k.Standardised):this.codefield,e.parameters);return i.includes(w.BADNESS)?{rewritten:!1,where:e}:{rewritten:this.sqlRewritable,where:N.create(i,W(t._parent).getFieldsIndex())}}evaluateNodeToWhereClause(e,t,i=null,s=null,a){let r,n,l,u;switch(e.type){case"interval":return Ce(this.evaluateNodeToWhereClause(e.value,t,i,s,a),e.qualifier,e.op);case"case-expression":{let o=" CASE ";e.format==="simple"&&(o+=this.evaluateNodeToWhereClause(e.operand,t,i,w.BADNESS,a));for(let d=0;d<e.clauses.length;d++)o+=" WHEN "+this.evaluateNodeToWhereClause(e.clauses[d].operand,t,i,w.BADNESS,a)+" THEN "+this.evaluateNodeToWhereClause(e.clauses[d].value,t,i,w.BADNESS,a);return e.else!==null&&(o+=" ELSE "+this.evaluateNodeToWhereClause(e.else,t,i,w.BADNESS,a)),o+=" END ",o}case"parameter":{const o=a[e.value.toLowerCase()];if(typeof o=="string")return"'"+o.toString().replace(/'/g,"''")+"'";if(o instanceof Date)return J(o,t,null);if(o instanceof ce)return oe(o,t,null);if(o instanceof Array){const d=[];for(let h=0;h<o.length;h++)typeof o[h]=="string"?d.push("'"+o[h].toString().replace(/'/g,"''")+"'"):o[h]instanceof Date?d.push(J(o[h],t,null)):o[h]instanceof ce?d.push(oe(o[h],t,null)):d.push(o[h].toString());return d}return o.toString()}case"expression-list":n=[];for(const o of e.value)n.push(this.evaluateNodeToWhereClause(o,t,i,s,a));return n;case"unary-expression":return" ( NOT "+this.evaluateNodeToWhereClause(e.expr,t,i,w.BADNESS,a)+" ) ";case"binary-expression":switch(e.operator){case"AND":return" ("+this.evaluateNodeToWhereClause(e.left,t,i,s,a)+" AND "+this.evaluateNodeToWhereClause(e.right,t,i,s,a)+") ";case"OR":return" ("+this.evaluateNodeToWhereClause(e.left,t,i,s,a)+" OR "+this.evaluateNodeToWhereClause(e.right,t,i,s,a)+") ";case"IS":if(e.right.type!=="null")throw new v(x.UnsupportedIsRhs);return" ("+this.evaluateNodeToWhereClause(e.left,t,i,s,a)+" IS NULL )";case"ISNOT":if(e.right.type!=="null")throw new v(x.UnsupportedIsRhs);return" ("+this.evaluateNodeToWhereClause(e.left,t,i,s,a)+" IS NOT NULL )";case"IN":if(r=[],e.right.type==="expression-list"){if(e.left.type==="column-reference"&&e.left.column.toUpperCase()===this.field.name.toUpperCase()){const o=[];let d=!0;for(const h of e.right.value){if(h.type!=="string"){d=!1;break}if(this.lkp[h.value]===void 0){d=!1;break}o.push(this.lkp[h.value].toString())}if(d)return" ("+this.evaluateNodeToWhereClause(e.left,t,i,s,a)+" IN ("+o.join(",")+")) "}return r=this.evaluateNodeToWhereClause(e.right,t,i,s,a)," ("+this.evaluateNodeToWhereClause(e.left,t,i,s,a)+" IN ("+r.join(",")+")) "}return u=this.evaluateNodeToWhereClause(e.right,t,i,s,a),u instanceof Array?" ("+this.evaluateNodeToWhereClause(e.left,t,i,s,a)+" IN ("+u.join(",")+")) ":" ("+this.evaluateNodeToWhereClause(e.left,t,i,s,a)+" IN ("+u+")) ";case"NOT IN":if(r=[],e.right.type==="expression-list"){if(e.left.type==="column-reference"&&e.left.column.toUpperCase()===this.field.name.toUpperCase()){const o=[];let d=!0;for(const h of e.right.value){if(h.type!=="string"){d=!1;break}if(this.lkp[h.value]===void 0){d=!1;break}o.push(this.lkp[h.value].toString())}if(d)return" ("+this.evaluateNodeToWhereClause(e.left,t,i,s,a)+" NOT IN ("+o.join(",")+")) "}return r=this.evaluateNodeToWhereClause(e.right,t,i,s,a)," ("+this.evaluateNodeToWhereClause(e.left,t,i,s,a)+" NOT IN ("+r.join(",")+")) "}return u=this.evaluateNodeToWhereClause(e.right,t,i,s,a),u instanceof Array?" ("+this.evaluateNodeToWhereClause(e.left,t,i,s,a)+" NOT IN ("+u.join(",")+")) ":" ("+this.evaluateNodeToWhereClause(e.left,t,i,s,a)+" NOT IN ("+u+")) ";case"BETWEEN":return l=this.evaluateNodeToWhereClause(e.right,t,i,w.BADNESS,a)," ("+this.evaluateNodeToWhereClause(e.left,t,i,w.BADNESS,a)+" BETWEEN "+l[0]+" AND "+l[1]+" ) ";case"NOTBETWEEN":return l=this.evaluateNodeToWhereClause(e.right,t,i,w.BADNESS,a)," ("+this.evaluateNodeToWhereClause(e.left,t,i,w.BADNESS,a)+" NOT BETWEEN "+l[0]+" AND "+l[1]+" ) ";case"LIKE":return e.escape!==""?" ("+this.evaluateNodeToWhereClause(e.left,t,i,w.BADNESS,a)+" LIKE "+this.evaluateNodeToWhereClause(e.right,t,i,w.BADNESS,a)+" ESCAPE '"+e.escape+"') ":" ("+this.evaluateNodeToWhereClause(e.left,t,i,w.BADNESS,a)+" LIKE "+this.evaluateNodeToWhereClause(e.right,t,i,w.BADNESS,a)+") ";case"NOT LIKE":return e.escape!==""?" ("+this.evaluateNodeToWhereClause(e.left,t,i,w.BADNESS,a)+" NOT LIKE "+this.evaluateNodeToWhereClause(e.right,t,i,w.BADNESS,a)+" ESCAPE '"+e.escape+"') ":" ("+this.evaluateNodeToWhereClause(e.left,t,i,w.BADNESS,a)+" NOT LIKE "+this.evaluateNodeToWhereClause(e.right,t,i,w.BADNESS,a)+") ";case"<>":case"=":if(e.left.type==="column-reference"&&e.right.type==="string"){if(e.left.column.toUpperCase()===this.field.name.toUpperCase()&&this.lkp[e.right.value.toString()]!==void 0)return" ("+s+" "+e.operator+" "+this.lkp[e.right.value.toString()].toString()+") "}else if(e.right.type==="column-reference"&&e.left.type==="string"&&e.right.column.toUpperCase()===this.field.name.toUpperCase())return" ("+this.lkp[e.right.value.toString()].toString()+" "+e.operator+" "+s+") ";return" ("+this.evaluateNodeToWhereClause(e.left,t,i,w.BADNESS,a)+" "+e.operator+" "+this.evaluateNodeToWhereClause(e.right,t,i,w.BADNESS,a)+") ";case"<":case">":case">=":case"<=":case"*":case"-":case"+":case"/":case"||":return" ("+this.evaluateNodeToWhereClause(e.left,t,i,w.BADNESS,a)+" "+e.operator+" "+this.evaluateNodeToWhereClause(e.right,t,i,w.BADNESS,a)+") "}case"null":return"null";case"boolean":return e.value===!0?"1":"0";case"string":return"'"+e.value.toString().replace(/'/g,"''")+"'";case"timestamp":case"date":return J(e.value,t,null);case"number":return e.value.toString();case"current-time":return be(e.mode==="date",t);case"column-reference":return i&&i.toLowerCase()===e.column.toLowerCase()?"("+s+")":e.column;case"data-type":return e.value;case"function":{const o=this.evaluateNodeToWhereClause(e.args,t,i,w.BADNESS,a);return Ie(e.name,o,t)}}throw new v(x.UnsupportedSyntax,{node:e.type})}extractValue(e){return this.codefield instanceof N?this.reverseLkp[this.codefield.calculateValueCompiled(e)]:this.reverseLkp[e.attributes[this.codefield]]}};ze.BADNESS="_!!!_BAD_LKP_!!!!";class Ke extends V{constructor(e,t){super(e),this._sql=t}rewriteSql(e,t){return{rewritten:!0,where:$(e,this.field.name,R(this._sql,k.Standardised),t.getFieldsIndex())}}extractValue(e){return this._sql.calculateValueCompiled(e)}}class Ze extends A{static findField(e,t){for(const i of e)if(i.name.toLowerCase()===t.toString().toLowerCase())return i;return null}constructor(e){super(e),this._calcFunc=null,this.declaredClass="esri.arcade.featureset.actions.Adapted",this.adaptedFields=[],this._extraFilter=null,this._extraFilter=e.extraFilter,this._parent=e.parentfeatureset,this._maxProcessing=30,this.adaptedFields=e.adaptedFields}_initialiseFeatureSet(){this._parent!==null?(this.geometryType=this._parent.geometryType,this.objectIdField=this._parent.objectIdField,this.globalIdField=this._parent.globalIdField,this.spatialReference=this._parent.spatialReference,this.hasM=this._parent.hasM,this.hasZ=this._parent.hasZ,this.typeIdField=this._parent.typeIdField,this.types=this._parent.types):(this.spatialReference=new ie({wkid:4326}),this.objectIdField="",this.globalIdField="",this.geometryType=ee.point,this.typeIdField="",this.types=null),this.fields=[];for(const e of this.adaptedFields)e.postInitialization(this,this._parent),this.fields.push(e.field)}async _getSet(e){var t;if(this._wset===null){await this._ensureLoaded();let i=null;return i=this._extraFilter?await this._getFilteredSet("",null,null,null,e):await((t=this._parent)==null?void 0:t._getSet(e)),this._checkCancelled(e),$e(i),this._wset=new y(i._candidates.slice(0),i._known.slice(0),i._ordered,this._clonePageDefinition(i.pagesDefinition)),this._wset}return this._wset}_isInFeatureSet(e){return W(this._parent)._isInFeatureSet(e)}async _getFeatures(e,t,i,s){var o,d;const a=[];t!==-1&&this._featureCache[t]===void 0&&a.push(t);const r=this._maxQueryRate();if(this._checkIfNeedToExpandKnownPage(e,r)===!0)return await this._expandPagedSet(e,r,0,0,s),this._getFeatures(e,t,i,s);let n=0;for(let h=e._lastFetchedIndex;h<e._known.length&&(n++,n<=i&&(e._lastFetchedIndex+=1),!(this._featureCache[e._known[h]]===void 0&&(e._known[h]!==t&&a.push(e._known[h]),a.length>=r)));h++);if(a.length===0)return"success";e=new y([],a,e._ordered,null);const l=Math.min(a.length,i);await((o=this._parent)==null?void 0:o._getFeatures(e,-1,l,s)),this._checkCancelled(s);const u=[];for(let h=0;h<l;h++){const f=(d=this._parent)==null?void 0:d._featureFromCache(a[h]);f!==void 0&&u.push({geometry:f.geometry,attributes:f.attributes,id:a[h]})}for(const h of u){const f=[];for(const _ of this.adaptedFields)f[_.field.name]=_.extractValue(h);this._featureCache[h.id]=new P({attributes:f,geometry:xe(h.geometry)})}return"success"}async _fetchAndRefineFeatures(){throw new b(C.NeverReach)}async _getFilteredSet(e,t,i,s,a){let r=!1;const n=this._reformulateWithoutAdaptions(i);r=n.cannot,i=n.where;let l=!1;if(s!==null){l=!0;const d=[];for(const h of this.adaptedFields)if(!(h instanceof K)&&s.scanForField(h.field.name)===!0){if(!(h instanceof Je)){s=null,l=!1;break}d.push({field:h.field.name,newfield:h.originalField.name})}s&&d.length>0&&(s=s.replaceFields(d))}i!==null?this._extraFilter!==null&&(i=O(this._extraFilter,i)):i=this._extraFilter,await this._ensureLoaded();const u=await W(this._parent)._getFilteredSet(e,t,i,s,a);let o;return this._checkCancelled(a),o=r===!0?new y(u._candidates.slice(0).concat(u._known.slice(0)),[],l===!0&&u._ordered,this._clonePageDefinition(u.pagesDefinition)):new y(u._candidates.slice(0),u._known.slice(0),l===!0&&u._ordered,this._clonePageDefinition(u.pagesDefinition)),o}_reformulateWithoutAdaptions(e){const t={cannot:!1,where:e};if(e!==null){for(const i of this.adaptedFields)if(_e(e,i.field.name)===!0){const s=i.rewriteSql(e,this);if(s.rewritten!==!0){t.cannot=!0,t.where=null;break}t.where=s.where}}return t}async _stat(e,t,i,s,a,r,n){let l=!1,u=this._reformulateWithoutAdaptions(t);if(l=u.cannot,t=u.where,u=this._reformulateWithoutAdaptions(a),l=l||u.cannot,(a=u.where)!==null?this._extraFilter!==null&&(a=O(this._extraFilter,a)):a=this._extraFilter,l===!0)return a===null&&i===""&&s===null?this._manualStat(e,t,r,n):{calculated:!1};const o=await W(this._parent)._stat(e,t,i,s,a,r,n);return o.calculated===!1?a===null&&i===""&&s===null?this._manualStat(e,t,r,n):{calculated:!1}:o}async _canDoAggregates(e,t,i,s,a){if(this._parent===null)return!1;for(let l=0;l<e.length;l++)for(const u of this.adaptedFields)if(e[l].toLowerCase()===u.field.name.toLowerCase()&&!(u instanceof K))return!1;const r=[];for(let l=0;l<t.length;l++){const u=t[l];if(u.workingexpr!==null){const o=this._reformulateWithoutAdaptions(u.workingexpr);if(o.cannot)return!1;const d=u.clone();d.workingexpr=o.where,r.push(d)}else r.push(u)}const n=this._reformulateWithoutAdaptions(a);return!n.cannot&&((a=n.where)!==null?this._extraFilter!==null&&(a=O(this._extraFilter,a)):a=this._extraFilter,this._parent._canDoAggregates(e,r,i,s,a))}async _getAggregatePagesDataSourceDefinition(e,t,i,s,a,r,n){if(this._parent===null)throw new b(C.NeverReach);const l=[];for(let o=0;o<t.length;o++){const d=t[o];if(d.workingexpr!==null){const h=this._reformulateWithoutAdaptions(d.workingexpr);if(h.cannot)throw new b(C.NeverReach);const f=d.clone();f.workingexpr=h.where,l.push(f)}else l.push(d)}const u=this._reformulateWithoutAdaptions(a);if(u.cannot)throw new b(C.NeverReach);return(a=u.where)!==null?this._extraFilter!==null&&(a=O(this._extraFilter,a)):a=this._extraFilter,this._parent._getAggregatePagesDataSourceDefinition(e,l,i,s,a,r,n)}}function fe(c,e){return c===e?0:c===null?-1:e===null?1:c<e?-1:1}class B{constructor(e){const t=e.split(",");this._fields=[],this._directions=[];for(let i=0;i<t.length;i++){const s=t[i].match(/\S+/g);this._fields.push(s[0]),s.length===2?s[1].toLowerCase()==="asc"?this._directions.push(1):this._directions.push(0):this._directions.push(1)}}constructClause(){let e="";for(let t=0;t<this._fields.length;t++)t!==0&&(e+=","),e+=this._fields[t],this._directions[t]===1?e+=" ASC":e+=" DESC";return e}order(e){e.sort((t,i)=>{for(let s=0;s<this._fields.length;s++){const a=this.featureValue(t.feature,this._fields[s],s),r=this.featureValue(i.feature,this._fields[s],s);let n=0;if(n=this._directions[s]===1?fe(a,r):-1*fe(a,r),n!==0)return n}return 0})}scanForField(e){for(let t=0;t<this._fields.length;t++)if(this._fields[t].toLowerCase().trim()===e.toLowerCase().trim())return!0;return!1}replaceFields(e){let t="";for(let i=0;i<this._fields.length;i++){i!==0&&(t+=",");let s=this._fields[i];for(const a of e)if(s.toLowerCase()===a.field.toLowerCase()){s=a.newfield;break}t+=s,this._directions[i]===1?t+=" ASC":t+=" DESC"}return new B(t)}featureValue(e,t,i){const s=e.attributes[t];if(s!==void 0)return s;for(const a in e.attributes)if(t.toLowerCase()===a.toLowerCase())return this._fields[i]=a,e.attributes[a];return null}}let Z=class we extends A{constructor(e){super(e),this._orderbyclause=null,this.declaredClass="esri.arcade.featureset.actions.OrderBy",this._maxProcessing=100,this._orderbyclause=e.orderbyclause,this._parent=e.parentfeatureset}async _getSet(e){if(this._wset===null){await this._ensureLoaded();const t=await this._getFilteredSet("",null,null,this._orderbyclause,e);return this._checkCancelled(e),this._wset=t,this._wset}return this._wset}async manualOrderSet(e,t){var a;const i=await this.getIdColumnDictionary(e,[],-1,t);(a=this._orderbyclause)==null||a.order(i);const s=new y([],[],!0,null);for(let r=0;r<i.length;r++)s._known.push(i[r].id);return s}async getIdColumnDictionary(e,t,i,s){if(i<e._known.length-1){const a=this._maxQueryRate();if(e._known[i+1]==="GETPAGES")return await M(this._parent._expandPagedSet(e,a,0,0,s)),this.getIdColumnDictionary(e,t,i,s);let r=i+1;const n=[];for(;r<e._known.length&&e._known[r]!=="GETPAGES";)n.push(e._known[r]),r++;i+=n.length;const l=await M(this._parent._getFeatureBatch(n,s));this._checkCancelled(s);for(const u of l)t.push({id:u.attributes[this.objectIdField],feature:u});return this.getIdColumnDictionary(e,t,i,s)}return e._candidates.length>0?(await M(this._refineSetBlock(e,this._maxProcessingRate(),s)),this._checkCancelled(s),this.getIdColumnDictionary(e,t,i,s)):t}_isInFeatureSet(e){return this._parent._isInFeatureSet(e)}_getFeatures(e,t,i,s){return this._parent._getFeatures(e,t,i,s)}_featureFromCache(e){if(this._featureCache[e]===void 0){const t=this._parent._featureFromCache(e);return t===void 0?void 0:t===null?null:(this._featureCache[e]=t,t)}return this._featureCache[e]}async _fetchAndRefineFeatures(){throw new b(C.NeverReach)}async _getFilteredSet(e,t,i,s,a){await this._ensureLoaded();const r=await this._parent._getFilteredSet(e,t,i,s===null?this._orderbyclause:s,a);this._checkCancelled(a);const n=new y(r._candidates.slice(0),r._known.slice(0),r._ordered,this._clonePageDefinition(r.pagesDefinition));let l=!0;if(r._candidates.length>0&&(l=!1),n._ordered===!1){let u=await this.manualOrderSet(n,a);return l===!1&&(t===null&&i===null||(u=new y(u._candidates.slice(0).concat(u._known.slice(0)),[],u._ordered,this._clonePageDefinition(u.pagesDefinition)))),u}return n}static registerAction(){A._featuresetFunctions.orderBy=function(e){return e===""?this:new we({parentfeatureset:this,orderbyclause:new B(e)})}}};function He(c){if(c.parseTree.type==="function"){if(c.parseTree.args.value.length===0)return{name:c.parseTree.name,expr:null};if(c.parseTree.args.value.length>1)throw new v(x.MissingStatisticParameters);const e=N.create(De(c.parseTree.args.value[0],k.Standardised,c.parameters),c.fieldsIndex);return{name:c.parseTree.name,expr:e}}return null}let pe=class H{constructor(){this.field="",this.tofieldname="",this.typeofstat="MIN",this.workingexpr=null}clone(){const e=new H;return e.field=this.field,e.tofieldname=this.tofieldname,e.typeofstat=this.typeofstat,e.workingexpr=this.workingexpr,e}static parseStatField(e,t,i){const s=new H;s.field=e;const a=N.create(t,i),r=He(a);if(r===null)throw new v(x.UnsupportedSqlFunction,{function:""});const n=r.name.toUpperCase().trim();if(n==="MIN"){if(s.typeofstat="MIN",s.workingexpr=r.expr,a===null)throw new v(x.InvalidFunctionParameters,{function:"min"})}else if(n==="MAX"){if(s.typeofstat="MAX",s.workingexpr=r.expr,a===null)throw new v(x.InvalidFunctionParameters,{function:"max"})}else if(n==="COUNT")s.typeofstat="COUNT",s.workingexpr=r.expr;else if(n==="STDEV"){if(s.typeofstat="STDDEV",s.workingexpr=r.expr,a===null)throw new v(x.InvalidFunctionParameters,{function:"stdev"})}else if(n==="SUM"){if(s.typeofstat="SUM",s.workingexpr=r.expr,a===null)throw new v(x.InvalidFunctionParameters,{function:"sum"})}else if(n==="MEAN"){if(s.typeofstat="AVG",s.workingexpr=r.expr,a===null)throw new v(x.InvalidFunctionParameters,{function:n})}else if(n==="AVG"){if(s.typeofstat="AVG",s.workingexpr=r.expr,a===null)throw new v(x.InvalidFunctionParameters,{function:"avg"})}else{if(n!=="VAR")throw new v(x.UnsupportedSqlFunction,{function:n});if(s.typeofstat="VAR",s.workingexpr=r.expr,a===null)throw new v(x.InvalidFunctionParameters,{function:"var"})}return s}toStatisticsName(){switch(this.typeofstat.toUpperCase()){case"MIN":return"min";case"MAX":return"max";case"SUM":return"sum";case"COUNT":default:return"count";case"VAR":return"var";case"STDDEV":return"stddev";case"AVG":return"avg"}}};function Xe(c){if(!c)return"COUNT";switch(c.toLowerCase()){case"max":return"MAX";case"var":case"variance":return"VAR";case"avg":case"average":case"mean":return"AVG";case"min":return"MIN";case"sum":return"SUM";case"stdev":case"stddev":return"STDDEV";case"count":return"COUNT"}return"COUNT"}let Ye=class Fe extends A{constructor(e){super(e),this._decodedStatsfield=[],this._decodedGroupbyfield=[],this._candosimplegroupby=!0,this.phsyicalgroupbyfields=[],this.objectIdField="ROW__ID",this._internalObjectIdField="ROW__ID",this._adaptedFields=[],this.declaredClass="esri.arcade.featureset.actions.Aggregate",this._uniqueIds=1,this._maxQuery=10,this._maxProcessing=10,this._parent=e.parentfeatureset,this._config=e}isTable(){return!0}async _getSet(e){if(this._wset===null){const t=await this._getFilteredSet("",null,null,null,e);return this._wset=t,this._wset}return this._wset}_isInFeatureSet(){return S.InFeatureSet}_nextUniqueName(e){for(;e["T"+this._uniqueIds.toString()]===1;)this._uniqueIds++;const t="T"+this._uniqueIds.toString();return e[t]=1,t}_convertToEsriFieldType(e){return e}_initialiseFeatureSet(){const e={};let t=!1,i=1;const s=this._parent?this._parent.getFieldsIndex():new Pe([]);for(this.objectIdField="ROW__ID",this.globalIdField="";t===!1;){let r=!1;for(let n=0;n<this._config.groupbyfields.length;n++)if(this._config.groupbyfields[n].name.toLowerCase()===this.objectIdField.toLowerCase()){r=!0;break}if(r===!1){for(let n=0;n<this._config.statsfields.length;n++)if(this._config.statsfields[n].name.toLowerCase()===this.objectIdField.toLowerCase()){r=!0;break}}r===!1?t=!0:(this.objectIdField="ROW__ID"+i.toString(),i++)}for(const r of this._config.statsfields){const n=new pe;n.field=r.name,n.tofieldname=r.name,n.workingexpr=r.expression instanceof N?r.expression:N.create(r.expression,s),n.typeofstat=Xe(r.statistic),this._decodedStatsfield.push(n)}this._decodedGroupbyfield=[];for(const r of this._config.groupbyfields){const n={name:r.name,singlefield:null,tofieldname:r.name,expression:r.expression instanceof N?r.expression:N.create(r.expression,s)};this._decodedGroupbyfield.push(n)}if(this._parent!==null){this.geometryType=this._parent.geometryType,this.spatialReference=this._parent.spatialReference,this.hasM=this._parent.hasM,this.hasZ=this._parent.hasZ,this.typeIdField="";for(const r of this._parent.fields)e[r.name.toUpperCase()]=1;this.types=null}else this.geometryType=ee.point,this.typeIdField="",this.types=null,this.spatialReference=new ie({wkid:4326});this.fields=[];const a=new pe;a.field=this._nextUniqueName(e),a.tofieldname=this.objectIdField,a.workingexpr=N.create(this._parent.objectIdField,this._parent.getFieldsIndex()),a.typeofstat="MIN",this._decodedStatsfield.push(a);for(const r of this._decodedGroupbyfield){const n=new j;if(r.name=this._nextUniqueName(e),n.name=r.tofieldname,n.alias=n.name,Q(r.expression)){const l=this._parent.getField(R(r.expression,k.Standardised));if(!l)throw new b(C.AggregationFieldNotFound);r.name=l.name,r.singlefield=l.name,this.phsyicalgroupbyfields.push(l.name),n.type=l.type}else{n.type=this._convertToEsriFieldType(Re(r.expression,this._parent.fields));const l=new j;l.name=r.name,l.alias=l.name,this.phsyicalgroupbyfields.push(r.name),this._adaptedFields.push(new Ke(l,r.expression)),this._candosimplegroupby=!1}this.fields.push(n)}if(this._adaptedFields.length>0)for(const r of this._parent.fields)this._adaptedFields.push(new K(r));for(let r=0;r<this._decodedStatsfield.length;r++){const n=new j;let l=null;const u=this._decodedStatsfield[r];u.field=this._nextUniqueName(e),u.tofieldname===this.objectIdField&&(this._internalObjectIdField=u.field),n.name=u.tofieldname,n.alias=n.name;const o=u.workingexpr!==null&&Q(u.workingexpr)?R(u.workingexpr,k.Standardised):"";switch(this._decodedStatsfield[r].typeofstat){case"SUM":if(o!==""){if(l=this._parent.getField(o),!l)throw new b(C.AggregationFieldNotFound);n.type=l.type}else n.type="double";break;case"MIN":case"MAX":if(o!==""){if(l=this._parent.getField(o),!l)throw new b(C.AggregationFieldNotFound);n.type=l.type}else n.type="double";break;case"COUNT":n.type="integer";break;case"STDDEV":case"VAR":case"AVG":if(o!==""&&(l=this._parent.getField(o),!l))throw new b(C.AggregationFieldNotFound);n.type="double"}this.fields.push(n)}}async _canDoAggregates(){return!1}async _getFeatures(e,t,i,s){t!==-1&&this._featureCache[t];const a=this._maxQuery;return this._checkIfNeedToExpandKnownPage(e,a)===!0?(await this._expandPagedSet(e,a,0,0,s),this._getFeatures(e,t,i,s)):"success"}async _getFilteredSet(e,t,i,s,a){if(e!=="")return new y([],[],!0,null);let r=null;const n={ordered:!1,nowhereclause:!1};if(await this._ensureLoaded(),i!==null){for(let u=0;u<this._decodedStatsfield.length;u++)if(_e(i,this._decodedStatsfield[u].tofieldname)===!0){n.nowhereclause=!0,i=null;break}}if(s!==null){n.ordered=!0;for(let u=0;u<this._decodedStatsfield.length;u++)if(s.scanForField(this._decodedStatsfield[u].tofieldname)===!0){s=null,n.ordered=!1;break}if(s!==null){for(const u of this._decodedGroupbyfield)if(u.singlefield===null&&s.scanForField(u.tofieldname)===!0){s=null,n.ordered=!1;break}}}if(this._candosimplegroupby!==!1&&await this._parent._canDoAggregates(this.phsyicalgroupbyfields,this._decodedStatsfield,"",null,null)){let u=null;i&&(u=this._reformulateWhereClauseWithoutGroupByFields(i));let o=null;s&&(o=this._reformulateOrderClauseWithoutGroupByFields(s));const d=await this._parent._getAggregatePagesDataSourceDefinition(this.phsyicalgroupbyfields,this._decodedStatsfield,"",null,u,o,this._internalObjectIdField);return this._checkCancelled(a),r=n.nowhereclause===!0?new y(d._candidates.slice(0).concat(d._known.slice(0)),[],n.ordered===!0&&d._ordered,this._clonePageDefinition(d.pagesDefinition)):new y(d._candidates.slice(0),d._known.slice(0),n.ordered===!0&&d._ordered,this._clonePageDefinition(d.pagesDefinition)),r}let l=this._parent;if(this._adaptedFields.length>0&&(l=new Ze({parentfeatureset:this._parent,adaptedFields:this._adaptedFields,extraFilter:null})),n.nowhereclause===!0)r=new y(["GETPAGES"],[],!1,{aggregatefeaturesetpagedefinition:!0,resultOffset:0,resultRecordCount:this._maxQuery,internal:{fullyResolved:!1,workingItem:null,type:"manual",iterator:null,set:[],subfeatureset:new Z({parentfeatureset:l,orderbyclause:new B(this.phsyicalgroupbyfields.join(",")+","+this._parent.objectIdField+" ASC")})}});else{let u=l;if(i!==null){let o=null;i&&(o=this._reformulateWhereClauseWithoutGroupByFields(i)),u=new U({parentfeatureset:u,whereclause:o})}r=new y(["GETPAGES"],[],!1,{aggregatefeaturesetpagedefinition:!0,resultOffset:0,resultRecordCount:this._maxQuery,internal:{fullyResolved:!1,workingItem:null,type:"manual",iterator:null,set:[],subfeatureset:new Z({parentfeatureset:u,orderbyclause:new B(this.phsyicalgroupbyfields.join(",")+","+this._parent.objectIdField+" ASC")})}})}return r}_reformulateWhereClauseWithoutStatsFields(e){for(const t of this._decodedStatsfield)e=$(e,t.tofieldname,R(t.workingexpr,k.Standardised),this._parent.getFieldsIndex());return e}_reformulateWhereClauseWithoutGroupByFields(e){for(const t of this._decodedGroupbyfield)t.tofieldname!==t.name&&(e=$(e,t.tofieldname,R(t.expression,k.Standardised),this._parent.getFieldsIndex()));return e}_reformulateOrderClauseWithoutGroupByFields(e){const t=[];for(const i of this._decodedGroupbyfield)i.tofieldname!==i.name&&t.push({field:i.tofieldname,newfield:i.name});return t.length>0?e.replaceFields(t):e}_clonePageDefinition(e){return e===null?null:e.aggregatefeaturesetpagedefinition===!0?{aggregatefeaturesetpagedefinition:!0,resultRecordCount:e.resultRecordCount,resultOffset:e.resultOffset,internal:e.internal}:this._parent._clonePageDefinition(e)}async _refineSetBlock(e,t,i){return this._checkIfNeedToExpandCandidatePage(e,this._maxQuery)===!0?(await this._expandPagedSet(e,this._maxQuery,0,0,i),this._refineSetBlock(e,t,i)):(this._checkCancelled(i),e._candidates.length,this._refineKnowns(e,t),e._candidates.length,e._candidates.length,e)}_expandPagedSet(e,t,i,s,a){return this._expandPagedSetFeatureSet(e,t,i,s,a)}async _getPhysicalPage(e,t,i){if(e.pagesDefinition.aggregatefeaturesetpagedefinition===!0)return this._sequentialGetPhysicalItem(e,e.pagesDefinition.resultRecordCount,i,[]);const s=await this._getAgregagtePhysicalPage(e,t,i);for(const a of s){const r={geometry:a.geometry,attributes:{}};for(const n of this._decodedGroupbyfield)r.attributes[n.tofieldname]=a.attributes[n.name];for(const n of this._decodedStatsfield)r.attributes[n.tofieldname]=a.attributes[n.field];this._featureCache[r.attributes[this.objectIdField]]=new P(r)}return s.length}_sequentialGetPhysicalItem(e,t,i,s){return new Promise((a,r)=>{e.pagesDefinition.internal.iterator===null&&(e.pagesDefinition.internal.iterator=e.pagesDefinition.internal.subfeatureset.iterator(i)),e.pagesDefinition.internal.fullyResolved===!0||t===0?a(s.length):this._nextAggregateItem(e,t,i,s,n=>{n===null?a(s.length):(t-=1,a(this._sequentialGetPhysicalItem(e,t,i,s)))},r)})}_nextAggregateItem(e,t,i,s,a,r){try{M(e.pagesDefinition.internal.iterator.next()).then(n=>{if(n===null)if(e.pagesDefinition.internal.workingItem!==null){const l=this._calculateAndAppendAggregateItem(e.pagesDefinition.internal.workingItem);s.push(l),e.pagesDefinition.internal.workingItem=null,e.pagesDefinition.internal.set.push(l.attributes[this.objectIdField]),e.pagesDefinition.internal.fullyResolved=!0,a(null)}else e.pagesDefinition.internal.fullyResolved=!0,a(null);else{const l=this._generateAggregateHash(n);if(e.pagesDefinition.internal.workingItem===null)e.pagesDefinition.internal.workingItem={features:[n],id:l};else{if(l!==e.pagesDefinition.internal.workingItem.id){const u=this._calculateAndAppendAggregateItem(e.pagesDefinition.internal.workingItem);return s.push(u),e.pagesDefinition.internal.workingItem=null,e.pagesDefinition.internal.set.push(u.attributes[this.objectIdField]),t-=1,e.pagesDefinition.internal.workingItem={features:[n],id:l},void a(u)}e.pagesDefinition.internal.workingItem.features.push(n)}this._nextAggregateItem(e,t,i,s,a,r)}},r)}catch(n){r(n)}}_calculateFieldStat(e,t,i){const s=[];for(let a=0;a<e.features.length;a++)if(t.workingexpr!==null){const r=t.workingexpr.calculateValue(e.features[a]);r!==null&&s.push(r)}else s.push(null);switch(t.typeofstat){case"MIN":i.attributes[t.tofieldname]=q("min",s,-1);break;case"MAX":i.attributes[t.tofieldname]=q("max",s,-1);break;case"SUM":i.attributes[t.tofieldname]=q("sum",s,-1);break;case"COUNT":i.attributes[t.tofieldname]=s.length;break;case"VAR":i.attributes[t.tofieldname]=q("var",s,-1);break;case"STDDEV":i.attributes[t.tofieldname]=q("stddev",s,-1);break;case"AVG":i.attributes[t.tofieldname]=q("avg",s,-1)}return!0}_calculateAndAppendAggregateItem(e){const t={attributes:{},geometry:null};for(const s of this._decodedGroupbyfield){const a=s.singlefield?e.features[0].attributes[s.singlefield]:s.expression.calculateValue(e.features[0]);t.attributes[s.tofieldname]=a}for(const s of this._decodedStatsfield)this._calculateFieldStat(e,s,t);const i=[];for(let s=0;s<this._decodedStatsfield.length;s++)i.push(this._calculateFieldStat(e,this._decodedStatsfield[s],t));return this._featureCache[t.attributes[this.objectIdField]]=new P({attributes:t.attributes,geometry:t.geometry}),t}_generateAggregateHash(e){let t="";for(const i of this._decodedGroupbyfield){const s=i.singlefield?e.attributes[i.singlefield]:i.expression.calculateValue(e);t+=s==null?":":":"+s.toString()}return ye(t,ge.String)}async _stat(){return{calculated:!1}}async getFeatureByObjectId(){return null}static registerAction(){A._featuresetFunctions.groupby=function(e,t){return new Fe({parentfeatureset:this,groupbyfields:e,statsfields:t})}}};class se extends A{constructor(e){super(e),this._topnum=0,this.declaredClass="esri.arcade.featureset.actions.Top",this._countedin=0,this._maxProcessing=100,this._topnum=e.topnum,this._parent=e.parentfeatureset}async _getSet(e){if(this._wset===null){await this._ensureLoaded();const t=await this._parent._getSet(e);return this._wset=new y(t._candidates.slice(0),t._known.slice(0),!1,this._clonePageDefinition(t.pagesDefinition)),this._setKnownLength(this._wset)>this._topnum&&(this._wset._known=this._wset._known.slice(0,this._topnum)),this._setKnownLength(this._wset)>=this._topnum&&(this._wset._candidates=[]),this._wset}return this._wset}_setKnownLength(e){return e._known.length>0&&e._known[e._known.length-1]==="GETPAGES"?e._known.length-1:e._known.length}_isInFeatureSet(e){const t=this._parent._isInFeatureSet(e);if(t===S.NotInFeatureSet)return t;const i=this._idstates[e];return i===S.InFeatureSet||i===S.NotInFeatureSet?i:t===S.InFeatureSet&&i===void 0?this._countedin<this._topnum?(this._idstates[e]=S.InFeatureSet,this._countedin++,S.InFeatureSet):(this._idstates[e]=S.NotInFeatureSet,S.NotInFeatureSet):S.Unknown}async _expandPagedSet(e,t,i,s,a){if(this._parent===null)throw new b(C.NotImplemented);if(t>this._topnum&&(t=this._topnum),this._countedin>=this._topnum&&e.pagesDefinition.internal.set.length<=e.pagesDefinition.resultOffset){let n=e._known.length;return n>0&&e._known[n-1]==="GETPAGES"&&(e._known.length=n-1),n=e._candidates.length,n>0&&e._candidates[n-1]==="GETPAGES"&&(e._candidates.length=n-1),"success"}const r=await this._parent._expandPagedSet(e,t,i,s,a);return this._setKnownLength(e)>this._topnum&&(e._known.length=this._topnum),this._setKnownLength(e)>=this._topnum&&(e._candidates.length=0),r}async _getFeatures(e,t,i,s){const a=[],r=this._maxQueryRate();if(this._checkIfNeedToExpandKnownPage(e,r)===!0)return await this._expandPagedSet(e,r,0,0,s),this._getFeatures(e,t,i,s);t!==-1&&this._featureCache[t]===void 0&&a.push(t);let n=0;for(let o=e._lastFetchedIndex;o<e._known.length&&(n++,n<=i&&(e._lastFetchedIndex+=1),!(this._featureCache[e._known[o]]===void 0&&(e._known[o]!==t&&a.push(e._known[o]),a.length>r)));o++);if(a.length===0)return"success";const l=new y([],a,!1,null),u=Math.min(a.length,i);await this._parent._getFeatures(l,-1,u,s);for(let o=0;o<u;o++){const d=this._parent._featureFromCache(a[o]);d!==void 0&&(this._featureCache[a[o]]=d)}return"success"}async _getFilteredSet(e,t,i,s,a){await this._ensureLoaded();const r=await this._getSet(a);return new y(r._candidates.slice(0).concat(r._known.slice(0)),[],!1,this._clonePageDefinition(r.pagesDefinition))}_refineKnowns(e,t){let i=0,s=null;const a=[];for(let r=0;r<e._candidates.length;r++){const n=this._isInFeatureSet(e._candidates[r]);if(n===S.InFeatureSet){if(e._known.push(e._candidates[r]),i+=1,s===null?s={start:r,end:r}:s.end===r-1?s.end=r:(a.push(s),s={start:r,end:r}),e._known.length>=this._topnum)break}else if(n===S.NotInFeatureSet)s===null?s={start:r,end:r}:s.end===r-1?s.end=r:(a.push(s),s={start:r,end:r}),i+=1;else if(n===S.Unknown)break;if(i>=t)break}s!==null&&a.push(s);for(let r=a.length-1;r>=0;r--)e._candidates.splice(a[r].start,a[r].end-a[r].start+1);this._setKnownLength(e)>this._topnum&&(e._known=e._known.slice(0,this._topnum)),this._setKnownLength(e)>=this._topnum&&(e._candidates=[])}async _stat(){return{calculated:!1}}async _canDoAggregates(){return!1}static registerAction(){A._featuresetFunctions.top=function(e){return new se({parentfeatureset:this,topnum:e})}}}let et=class X extends A{constructor(e){super(e),this.declaredClass="esri.arcade.featureset.sources.FeatureLayerDynamic",this._removeGeometry=!1,this._overrideFields=null,this.formulaCredential=null,this._pageJustIds=!1,this._requestStandardised=!1,this._useDefinitionExpression=!0,this._cachedDateMetaData={},e.spatialReference&&(this.spatialReference=e.spatialReference),this._transparent=!0,this._maxProcessing=1e3,this._layer=e.layer,this._wset=null,e.outFields!==void 0&&(this._overrideFields=e.outFields),e.includeGeometry!==void 0&&(this._removeGeometry=e.includeGeometry===!1)}_maxQueryRate(){return te}end(){return this._layer}optimisePagingFeatureQueries(e){this._pageJustIds=e}get urlQueryPath(){return this._layer.parsedUrl.path||""}convertQueryToLruCacheKey(e){const t=this.urlQueryPath+","+Ne(e.toJSON());return ye(t,ge.String)}async loadImpl(){return this._layer.loaded===!0?(this._initialiseFeatureSet(),this):(await this._layer.load(),this._initialiseFeatureSet(),this)}_initialiseFeatureSet(){if(this.spatialReference==null&&(this.spatialReference=this._layer.spatialReference),this.geometryType=this._layer.geometryType,this.fields=this._layer.fields.slice(0),this._layer.outFields&&!(this._layer.outFields.length===1&&this._layer.outFields[0]==="*")){const e=[];for(const t of this.fields)if(t.type==="oid")e.push(t);else for(const i of this._layer.outFields)if(i.toLowerCase()===t.name.toLowerCase()){e.push(t);break}this.fields=e}if(this._overrideFields!==null)if(this._overrideFields.length===1&&this._overrideFields[0]==="*")this._overrideFields=null;else{const e=[],t=[];for(const i of this.fields)if(i.type==="oid")e.push(i),t.push(i.name);else for(const s of this._overrideFields)if(s.toLowerCase()===i.name.toLowerCase()){e.push(i),t.push(i.name);break}this.fields=e,this._overrideFields=t}if(this._layer.source&&this._layer.source.sourceJSON){const e=this._layer.source.sourceJSON.currentVersion;this._layer.source.sourceJSON.useStandardizedQueries===!0?(this._databaseType=k.StandardisedNoInterval,e!=null&&e>=10.61&&(this._databaseType=k.Standardised)):e!=null&&(e>=10.5&&(this._databaseType=k.StandardisedNoInterval,this._requestStandardised=!0),e>=10.61&&(this._databaseType=k.Standardised))}this.objectIdField=this._layer.objectIdField;for(const e of this.fields)e.type==="global-id"&&(this.globalIdField=e.name);this.hasM=this._layer.supportsM,this.hasZ=this._layer.supportsZ,this.typeIdField=this._layer.typeIdField??"",this.types=this._layer.types}_isInFeatureSet(){return S.InFeatureSet}async _refineSetBlock(e){return e}_candidateIdTransform(e){return e}async _getSet(e){if(this._wset===null){await this._ensureLoaded();const t=await this._getFilteredSet("",null,null,null,e);return this._wset=t,t}return this._wset}async _runDatabaseProbe(e){await this._ensureLoaded();const t=new L;this.datesInUnknownTimezone&&(t.timeReferenceUnknownClient=!0),t.where=e.replace("OBJECTID",this._layer.objectIdField);try{return await this._layer.queryObjectIds(t),!0}catch{return!1}}_canUsePagination(){return!(!this._layer.capabilities||!this._layer.capabilities.query||this._layer.capabilities.query.supportsPagination!==!0)}_cacheableFeatureSetSourceKey(){return this._layer.url}pbfSupportedForQuery(e){var i,s;const t=(s=(i=this._layer)==null?void 0:i.capabilities)==null?void 0:s.query;return!e.outStatistics&&(t==null?void 0:t.supportsFormatPBF)===!0&&(t==null?void 0:t.supportsQuantizationEditMode)===!0}async queryPBF(e){e.quantizationParameters={mode:"edit"};const t=await qe(this._layer.parsedUrl,e,new Be({}));return je.fromJSON(Ge(t.data)).unquantize()}get gdbVersion(){return this._layer&&this._layer.capabilities&&this._layer.capabilities.data&&this._layer.capabilities.data.isVersioned?this._layer.gdbVersion?this._layer.gdbVersion:"SDE.DEFAULT":""}nativeCapabilities(){return{title:this._layer.title??"",source:this,canQueryRelated:!0,capabilities:this._layer.capabilities,databaseType:this._databaseType,requestStandardised:this._requestStandardised}}executeQuery(e,t){const i=t==="execute"?de:t==="executeForCount"?Qe:Ve,s=t==="execute"&&this.pbfSupportedForQuery(e);let a=null;if(this.recentlyUsedQueries){const r=this.convertQueryToLruCacheKey(e);a=this.recentlyUsedQueries.getFromCache(r),a===null&&(a=s!==!0?i(this._layer.parsedUrl.path,e):this.queryPBF(e),this.recentlyUsedQueries.addToCache(r,a),a=a.catch(n=>{var l;throw(l=this.recentlyUsedQueries)==null||l.removeFromCache(r),n}))}return this.featureSetQueryInterceptor&&this.featureSetQueryInterceptor.preLayerQueryCallback({layer:this._layer,query:e,method:t}),a===null&&(a=s!==!0?i(this._layer.parsedUrl.path,e):this.queryPBF(e)),a}async _getFilteredSet(e,t,i,s,a){const r=await this.databaseType();if(this.isTable()&&t&&e!==null&&e!=="")return new y([],[],!0,null);if(this._canUsePagination())return this._getFilteredSetUsingPaging(e,t,i,s,a);let n="",l=!1;s!==null&&this._layer.capabilities&&this._layer.capabilities.query&&this._layer.capabilities.query.supportsOrderBy===!0&&(n=s.constructClause(),l=!0);const u=new L;this.datesInUnknownTimezone&&(u.timeReferenceUnknownClient=!0),u.where=i===null?t===null?"1=1":"":R(i,r),this._requestStandardised&&(u.sqlFormat="standard"),u.spatialRelationship=this._makeRelationshipEnum(e),u.outSpatialReference=this.spatialReference,u.orderByFields=n!==""?n.split(","):null,u.geometry=t===null?null:t,u.relationParameter=this._makeRelationshipParam(e);let o=await this.executeQuery(u,"executeForIds");return o===null&&(o=[]),this._checkCancelled(a),new y([],o,l,null)}_expandPagedSet(e,t,i,s,a){return this._expandPagedSetFeatureSet(e,t,i,s,a)}async _getFilteredSetUsingPaging(e,t,i,s,a){var f;let r="",n=!1;s!==null&&this._layer.capabilities&&this._layer.capabilities.query&&this._layer.capabilities.query.supportsOrderBy===!0&&(r=s.constructClause(),n=!0);const l=await this.databaseType();let u=i===null?t===null?"1=1":"":R(i,l);this._layer.definitionExpression&&this._useDefinitionExpression&&(u=u!==""?"(("+this._layer.definitionExpression+") AND ("+u+"))":this._layer.definitionExpression);let o=this._maxQueryRate();const d=(f=this._layer.capabilities)==null?void 0:f.query.maxRecordCount;d!=null&&d<o&&(o=d);let h=null;if(this._pageJustIds===!0)h=new y([],["GETPAGES"],n,{spatialRel:this._makeRelationshipEnum(e),relationParam:this._makeRelationshipParam(e),outFields:this._layer.objectIdField,resultRecordCount:o,resultOffset:0,geometry:t===null?null:t,where:u,orderByFields:r,returnGeometry:!1,returnIdsOnly:"false",internal:{set:[],lastRetrieved:0,lastPage:0,fullyResolved:!1}});else{let _=!0;this._removeGeometry===!0&&(_=!1);const m=this._overrideFields!==null?this._overrideFields:this._fieldsIncludingObjectId(this._layer.outFields?this._layer.outFields:["*"]);h=new y([],["GETPAGES"],n,{spatialRel:this._makeRelationshipEnum(e),relationParam:this._makeRelationshipParam(e),outFields:m.join(","),resultRecordCount:o,resultOffset:0,geometry:t===null?null:t,where:u,orderByFields:r,returnGeometry:_,returnIdsOnly:"false",internal:{set:[],lastRetrieved:0,lastPage:0,fullyResolved:!1}})}return await this._expandPagedSet(h,o,0,1,a),h}_clonePageDefinition(e){return e===null?null:e.groupbypage!==!0?{groupbypage:!1,spatialRel:e.spatialRel,relationParam:e.relationParam,outFields:e.outFields,resultRecordCount:e.resultRecordCount,resultOffset:e.resultOffset,geometry:e.geometry,where:e.where,orderByFields:e.orderByFields,returnGeometry:e.returnGeometry,returnIdsOnly:e.returnIdsOnly,internal:e.internal}:{groupbypage:!0,spatialRel:e.spatialRel,relationParam:e.relationParam,outFields:e.outFields,resultRecordCount:e.resultRecordCount,useOIDpagination:e.useOIDpagination,generatedOid:e.generatedOid,groupByFieldsForStatistics:e.groupByFieldsForStatistics,resultOffset:e.resultOffset,outStatistics:e.outStatistics,geometry:e.geometry,where:e.where,orderByFields:e.orderByFields,returnGeometry:e.returnGeometry,returnIdsOnly:e.returnIdsOnly,internal:e.internal}}async _getPhysicalPage(e,t,i){const s=e.pagesDefinition.internal.lastRetrieved,a=s,r=e.pagesDefinition.internal.lastPage,n=new L;this._requestStandardised&&(n.sqlFormat="standard"),this.datesInUnknownTimezone&&(n.timeReferenceUnknownClient=!0),n.spatialRelationship=e.pagesDefinition.spatialRel,n.relationParameter=e.pagesDefinition.relationParam,n.outFields=e.pagesDefinition.outFields.split(","),n.num=e.pagesDefinition.resultRecordCount,n.start=e.pagesDefinition.internal.lastPage,n.geometry=e.pagesDefinition.geometry,n.where=e.pagesDefinition.where,n.orderByFields=e.pagesDefinition.orderByFields!==""?e.pagesDefinition.orderByFields.split(","):null,n.returnGeometry=e.pagesDefinition.returnGeometry,n.outSpatialReference=this.spatialReference;const l=await this.executeQuery(n,"execute");if(this._checkCancelled(i),e.pagesDefinition.internal.lastPage!==r)return"done";const u=this._layer.objectIdField;for(let o=0;o<l.features.length;o++)e.pagesDefinition.internal.set[a+o]=l.features[o].attributes[u];if(this._pageJustIds===!1)for(let o=0;o<l.features.length;o++)this._featureCache[l.features[o].attributes[u]]=l.features[o];return(l.exceededTransferLimit===void 0&&l.features.length!==e.pagesDefinition.resultRecordCount||l.exceededTransferLimit===!1)&&(e.pagesDefinition.internal.fullyResolved=!0),e.pagesDefinition.internal.lastRetrieved=s+l.features.length,e.pagesDefinition.internal.lastPage+=e.pagesDefinition.resultRecordCount,"done"}_fieldsIncludingObjectId(e){if(e===null)return[this.objectIdField];const t=e.slice(0);if(t.includes("*"))return t;let i=!1;for(const s of t)if(s.toUpperCase()===this.objectIdField.toUpperCase()){i=!0;break}return i===!1&&t.push(this.objectIdField),t}async _getFeatures(e,t,i,s){const a=[];if(t!==-1&&this._featureCache[t]===void 0&&a.push(t),this._checkIfNeedToExpandKnownPage(e,this._maxProcessingRate())===!0)return await this._expandPagedSet(e,this._maxProcessingRate(),0,0,s),this._getFeatures(e,t,i,s);let r=0;for(let o=e._lastFetchedIndex;o<e._known.length;o++){if(e._lastFetchedIndex+=1,r++,this._featureCache[e._known[o]]===void 0){let d=!1;if(this._layer._mode!==null&&this._layer._mode!==void 0){const h=this._layer._mode;if(h._featureMap[e._known[o]]!==void 0){const f=h._featureMap[e._known[o]];f!==null&&(d=!0,this._featureCache[e._known[o]]=f)}}if(d===!1&&(e._known[o]!==t&&a.push(e._known[o]),a.length>=this._maxProcessingRate()-1))break}if(r>=i&&a.length===0)break}if(a.length===0)return"success";const n=new L;this._requestStandardised&&(n.sqlFormat="standard"),this.datesInUnknownTimezone&&(n.timeReferenceUnknownClient=!0),n.objectIds=a,n.outFields=this._overrideFields!==null?this._overrideFields:this._fieldsIncludingObjectId(this._layer.outFields?this._layer.outFields:["*"]),n.returnGeometry=!0,this._removeGeometry===!0&&(n.returnGeometry=!1),n.outSpatialReference=this.spatialReference;const l=await this.executeQuery(n,"execute");if(this._checkCancelled(s),l.error!==void 0)throw new b(C.RequestFailed,{reason:l.error});const u=this._layer.objectIdField;for(let o=0;o<l.features.length;o++)this._featureCache[l.features[o].attributes[u]]=l.features[o];return"success"}async _getDistinctPages(e,t,i,s,a,r,n,l,u){var F;await this._ensureLoaded();const o=await this.databaseType();let d=i.parseTree.column;const h=this._layer.fields??[];for(let p=0;p<h.length;p++)if(h[p].name.toLowerCase()===d.toLowerCase()){d=h[p].name;break}const f=new L;this._requestStandardised&&(f.sqlFormat="standard"),this.datesInUnknownTimezone&&(f.timeReferenceUnknownClient=!0);let _=r===null?a===null?"1=1":"":R(r,o);this._layer.definitionExpression&&this._useDefinitionExpression&&(_=_!==""?"(("+this._layer.definitionExpression+") AND ("+_+"))":this._layer.definitionExpression),f.where=_,f.spatialRelationship=this._makeRelationshipEnum(s),f.relationParameter=this._makeRelationshipParam(s),f.geometry=a===null?null:a,f.returnDistinctValues=!0,f.returnGeometry=!1,f.outFields=[d];const m=await this.executeQuery(f,"execute");if(this._checkCancelled(u),!m.hasOwnProperty("features"))throw new b(C.InvalidStatResponse);let I=!1;for(let p=0;p<h.length;p++)if(h[p].name===d){h[p].type==="date"&&(I=!0);break}for(let p=0;p<m.features.length;p++){if(I){const D=m.features[p].attributes[d];D!==null?l.push(new Date(D)):l.push(D)}else l.push(m.features[p].attributes[d]);if(l.length>=n)break}return m.features.length===0?l:m.features.length===((F=this._layer.capabilities)==null?void 0:F.query.maxRecordCount)&&l.length<n?{calculated:!0,result:await this._getDistinctPages(e+m.features.length,t,i,s,a,r,n,l,u)}:l}async _distinctStat(e,t,i,s,a,r,n){return{calculated:!0,result:await this._getDistinctPages(0,e,t,i,s,a,r,[],n)}}isTable(){return this._layer.isTable||this._layer.geometryType===null||this._layer.type==="table"||this._layer.geometryType===""||this._layer.geometryType==="esriGeometryNull"}async _countstat(e,t,i,s){const a=await this.databaseType(),r=new L;if(this._requestStandardised&&(r.sqlFormat="standard"),this.isTable()&&i&&t!==null&&t!=="")return{calculated:!0,result:0};let n=s===null?i===null?"1=1":"":R(s,a);return this._layer.definitionExpression&&this._useDefinitionExpression&&(n=n!==""?"(("+this._layer.definitionExpression+") AND ("+n+"))":this._layer.definitionExpression),r.where=n,this.datesInUnknownTimezone&&(r.timeReferenceUnknownClient=!0),r.where=n,r.spatialRelationship=this._makeRelationshipEnum(t),r.relationParameter=this._makeRelationshipParam(t),r.geometry=i===null?null:i,r.returnGeometry=!1,{calculated:!0,result:await this.executeQuery(r,"executeForCount")}}async _stats(e,t,i,s,a,r,n){await this._ensureLoaded();const l=this._layer.capabilities&&this._layer.capabilities.query&&this._layer.capabilities.query.supportsSqlExpression===!0,u=this._layer.capabilities&&this._layer.capabilities.query&&this._layer.capabilities.query.supportsStatistics===!0,o=this._layer.capabilities&&this._layer.capabilities.query&&this._layer.capabilities.query.supportsDistinct===!0;if(e==="count")return o?this._countstat(e,i,s,a):{calculated:!1};if(u===!1||Q(t)===!1&&l===!1||t.isStandardized===!1)return i!==""||a!==null?{calculated:!1}:this._manualStat(e,t,r,n);if(e==="distinct")return o===!1?i!==""||a!==null?{calculated:!1}:this._manualStat(e,t,r,n):this._distinctStat(e,t,i,s,a,r,n);const d=await this.databaseType();if(this.isTable()&&s&&i!==null&&i!=="")return{calculated:!0,result:null};const h=new L;this._requestStandardised&&(h.sqlFormat="standard");let f=a===null?s===null?"1=1":"":R(a,d);this._layer.definitionExpression&&this._useDefinitionExpression&&(f=f!==""?"(("+this._layer.definitionExpression+") AND ("+f+"))":this._layer.definitionExpression),h.where=f,h.spatialRelationship=this._makeRelationshipEnum(i),h.relationParameter=this._makeRelationshipParam(i),h.geometry=s===null?null:s,this.datesInUnknownTimezone&&(h.timeReferenceUnknownClient=!0);const _=new ue;_.statisticType=Te(e),_.onStatisticField=R(t,d),_.outStatisticFieldName="ARCADE_STAT_RESULT",h.returnGeometry=!1;let m="ARCADE_STAT_RESULT";h.outStatistics=[_];const I=await this.executeQuery(h,"execute");if(!I.hasOwnProperty("features")||I.features.length===0)throw new b(C.InvalidStatResponse);let F=!1;const p=I.fields??[];for(let D=0;D<p.length;D++)if(p[D].name.toUpperCase()==="ARCADE_STAT_RESULT"){m=p[D].name,p[D].type==="date"&&(F=!0);break}if(F){let D=I.features[0].attributes[m];return D!==null&&(D=new Date(I.features[0].attributes[m])),{calculated:!0,result:D}}return{calculated:!0,result:I.features[0].attributes[m]}}_stat(e,t,i,s,a,r,n){return this._stats(e,t,i,s,a,r,n)}async _canDoAggregates(e,t){var r,n;await this._ensureLoaded();let i=!1;const s=(r=this._layer.capabilities)==null?void 0:r.query,a=(s==null?void 0:s.supportsSqlExpression)===!0;if(s!=null&&s.supportsStatistics===!0&&s.supportsOrderBy===!0&&(i=!0),i)for(let l=0;l<t.length-1;l++)(((n=t[l].workingexpr)==null?void 0:n.isStandardized)===!1||Q(t[l].workingexpr)===!1&&a===!1)&&(i=!1);return i!==!1}_makeRelationshipEnum(e){if(e.includes("esriSpatialRelRelation"))return"relation";switch(e){case"esriSpatialRelRelation":return"relation";case"esriSpatialRelIntersects":return"intersects";case"esriSpatialRelContains":return"contains";case"esriSpatialRelOverlaps":return"overlaps";case"esriSpatialRelWithin":return"within";case"esriSpatialRelTouches":return"touches";case"esriSpatialRelCrosses":return"crosses";case"esriSpatialRelEnvelopeIntersects":return"envelope-intersects"}return e}_makeRelationshipParam(e){return e.includes("esriSpatialRelRelation")?e.split(":")[1]:""}async _getAggregatePagesDataSourceDefinition(e,t,i,s,a,r,n){var I;await this._ensureLoaded();const l=await this.databaseType();let u="",o=!1,d=!1;r!==null&&this._layer.capabilities&&this._layer.capabilities.query&&this._layer.capabilities.query.supportsOrderBy===!0&&(u=r.constructClause(),d=!0),this._layer.capabilities&&this._layer.capabilities.query&&this._layer.capabilities.query.supportsPagination===!1&&(d=!1,o=!0,u=this._layer.objectIdField);const h=[];for(let F=0;F<t.length;F++){const p=new ue;p.onStatisticField=t[F].workingexpr!==null?R(t[F].workingexpr,l):"",p.outStatisticFieldName=t[F].field,p.statisticType=t[F].toStatisticsName(),h.push(p)}u===""&&(u=e.join(","));let f=this._maxQueryRate();const _=(I=this._layer.capabilities)==null?void 0:I.query.maxRecordCount;_!=null&&_<f&&(f=_);let m=a===null?s===null?"1=1":"":R(a,l);return this._layer.definitionExpression&&this._useDefinitionExpression&&(m=m!==""?"(("+this._layer.definitionExpression+") AND ("+m+"))":this._layer.definitionExpression),new y([],["GETPAGES"],d,{groupbypage:!0,spatialRel:this._makeRelationshipEnum(i),relationParam:this._makeRelationshipParam(i),outFields:["*"],useOIDpagination:o,generatedOid:n,resultRecordCount:f,resultOffset:0,groupByFieldsForStatistics:e,outStatistics:h,geometry:s===null?null:s,where:m,orderByFields:u,returnGeometry:!1,returnIdsOnly:!1,internal:{lastMaxId:-1,set:[],lastRetrieved:0,lastPage:0,fullyResolved:!1}})}async _getAgregagtePhysicalPage(e,t,i){let s=e.pagesDefinition.where;e.pagesDefinition.useOIDpagination===!0&&(s=s!==""?"("+s+") AND ("+e.pagesDefinition.generatedOid+">"+e.pagesDefinition.internal.lastMaxId.toString()+")":e.pagesDefinition.generatedOid+">"+e.pagesDefinition.internal.lastMaxId.toString());const a=e.pagesDefinition.internal.lastRetrieved,r=a,n=e.pagesDefinition.internal.lastPage,l=new L;if(this._requestStandardised&&(l.sqlFormat="standard"),l.where=s,l.spatialRelationship=e.pagesDefinition.spatialRel,l.relationParameter=e.pagesDefinition.relationParam,l.outFields=e.pagesDefinition.outFields,l.outStatistics=e.pagesDefinition.outStatistics,l.geometry=e.pagesDefinition.geometry,l.groupByFieldsForStatistics=e.pagesDefinition.groupByFieldsForStatistics,l.num=e.pagesDefinition.resultRecordCount,l.start=e.pagesDefinition.internal.lastPage,l.returnGeometry=e.pagesDefinition.returnGeometry,this.datesInUnknownTimezone&&(l.timeReferenceUnknownClient=!0),l.orderByFields=e.pagesDefinition.orderByFields!==""?e.pagesDefinition.orderByFields.split(","):null,this.isTable()&&l.geometry&&l.spatialRelationship)return[];const u=await this.executeQuery(l,"execute");if(this._checkCancelled(i),!u.hasOwnProperty("features"))throw new b(C.InvalidStatResponse);const o=[];if(e.pagesDefinition.internal.lastPage!==n)return[];for(let d=0;d<u.features.length;d++)e.pagesDefinition.internal.set[r+d]=u.features[d].attributes[e.pagesDefinition.generatedOid];for(let d=0;d<u.features.length;d++)o.push(new P({attributes:u.features[d].attributes,geometry:null}));return e.pagesDefinition.useOIDpagination===!0?u.features.length===0?e.pagesDefinition.internal.fullyResolved=!0:e.pagesDefinition.internal.lastMaxId=u.features[u.features.length-1].attributes[e.pagesDefinition.generatedOid]:(u.exceededTransferLimit===void 0&&u.features.length!==e.pagesDefinition.resultRecordCount||u.exceededTransferLimit===!1)&&(e.pagesDefinition.internal.fullyResolved=!0),e.pagesDefinition.internal.lastRetrieved=a+u.features.length,e.pagesDefinition.internal.lastPage+=e.pagesDefinition.resultRecordCount,o}static create(e,t,i,s,a){const r=new T({url:e,outFields:t===null?["*"]:t});return new X({layer:r,spatialReference:i,lrucache:s,interceptor:a})}relationshipMetaData(){return this._layer&&this._layer.source&&this._layer.source.sourceJSON&&this._layer.source.sourceJSON.relationships?this._layer.source.sourceJSON.relationships:[]}serviceUrl(){return z(this._layer.parsedUrl.path)}async queryAttachments(e,t,i,s,a){const r=this._layer.capabilities;if(r!=null&&r.data.supportsAttachment&&(r!=null&&r.operations.supportsQueryAttachments)){const n={objectIds:[e],returnMetadata:a};(t&&t>0||i&&i>0)&&(n.size=[t&&t>0?t:0,i&&i>0?i:t+1]),s&&s.length>0&&(n.attachmentTypes=s),this.featureSetQueryInterceptor&&this.featureSetQueryInterceptor.preLayerQueryCallback({layer:this._layer,query:n,method:"attachments"});const l=await this._layer.queryAttachments(n),u=[];return l&&l[e]&&l[e].forEach(o=>{const d=this._layer.parsedUrl.path+"/"+e.toString()+"/attachments/"+o.id.toString();let h=null;a&&o.exifInfo&&(h=Ae.convertJsonToArcade(o.exifInfo,"system",!0)),u.push(new Le(o.id,o.name,o.contentType,o.size,d,h))}),u}return[]}async queryRelatedFeatures(e){var s;const t={f:"json",relationshipId:e.relationshipId.toString(),definitionExpression:e.where,outFields:(s=e.outFields)==null?void 0:s.join(","),returnGeometry:e.returnGeometry.toString()};e.resultOffset!==void 0&&e.resultOffset!==null&&(t.resultOffset=e.resultOffset.toString()),e.resultRecordCount!==void 0&&e.resultRecordCount!==null&&(t.resultRecordCount=e.resultRecordCount.toString()),e.orderByFields&&(t.orderByFields=e.orderByFields.join(",")),e.objectIds&&e.objectIds.length>0&&(t.objectIds=e.objectIds.join(",")),e.outSpatialReference&&(t.outSR=JSON.stringify(e.outSpatialReference.toJSON())),this.featureSetQueryInterceptor&&this.featureSetQueryInterceptor.preRequestCallback({layer:this._layer,queryPayload:t,method:"relatedrecords",url:this._layer.parsedUrl.path+"/queryRelatedRecords"});const i=await G(this._layer.parsedUrl.path+"/queryRelatedRecords",{responseType:"json",query:t});if(i.data){const a={},r=i.data;if(r&&r.relatedRecordGroups){const n=r.spatialReference;for(const l of r.relatedRecordGroups){const u=l.objectId,o=[];for(const d of l.relatedRecords){d.geometry&&(d.geometry.spatialReference=n);const h=new P({geometry:d.geometry?Ue(d.geometry):null,attributes:d.attributes});o.push(h)}a[u]={features:o,exceededTransferLimit:r.exceededTransferLimit===!0}}}return a}throw new b(C.InvalidRequest)}async getFeatureByObjectId(e,t){const i=new L;i.outFields=t,i.returnGeometry=!1,i.outSpatialReference=this.spatialReference,i.where=this.objectIdField+"="+e.toString(),this.datesInUnknownTimezone&&(i.timeReferenceUnknownClient=!0),this.featureSetQueryInterceptor&&this.featureSetQueryInterceptor.preLayerQueryCallback({layer:this._layer,query:i,method:"execute"});const s=await de(this._layer.parsedUrl.path,i);return s.features.length===1?s.features[0]:null}async getIdentityUser(){var t;await this.load();const e=(t=le)==null?void 0:t.findCredential(this._layer.url);return e?e.userId:null}async getOwningSystemUrl(){var s;await this.load();const e=(s=le)==null?void 0:s.findServerInfo(this._layer.url);if(e)return e.owningSystemUrl;let t=this._layer.url;const i=t.toLowerCase().indexOf("/rest/services");if(t=i>-1?t.substring(0,i):t,t){t+="/rest/info";try{const a=await G(t,{query:{f:"json"}});let r="";return a.data&&a.data.owningSystemUrl&&(r=a.data.owningSystemUrl),r}catch{return""}}return""}getDataSourceFeatureSet(){const e=new X({layer:this._layer,spatialReference:this.spatialReference??void 0,outFields:this._overrideFields??void 0,includeGeometry:!this._removeGeometry,lrucache:this.recentlyUsedQueries??void 0,interceptor:this.featureSetQueryInterceptor??void 0});return e._useDefinitionExpression=!1,e}get preferredTimeReference(){var e,t;return this._cachedDateMetaData.preferredTimeReference===void 0&&(this._cachedDateMetaData.preferredTimeReference=((t=(e=this._layer)==null?void 0:e.preferredTimeReference)==null?void 0:t.toJSON())??null),this._cachedDateMetaData.preferredTimeReference}get dateFieldsTimeReference(){var e,t;return this._cachedDateMetaData.dateFieldsTimeReference===void 0&&(this._cachedDateMetaData.dateFieldsTimeReference=((t=(e=this._layer)==null?void 0:e.dateFieldsTimeReference)==null?void 0:t.toJSON())??null),this._cachedDateMetaData.dateFieldsTimeReference}get datesInUnknownTimezone(){return this._layer.datesInUnknownTimezone}get editFieldsInfo(){var e,t;return this._cachedDateMetaData.editFieldsInfo===void 0&&(this._cachedDateMetaData.editFieldsInfo=((t=(e=this._layer)==null?void 0:e.editFieldsInfo)==null?void 0:t.toJSON())??null),this._cachedDateMetaData.editFieldsInfo}get timeInfo(){var e,t;return this._cachedDateMetaData.timeInfo===void 0&&(this._cachedDateMetaData.timeInfo=((t=(e=this._layer)==null?void 0:e.timeInfo)==null?void 0:t.toJSON())??null),this._cachedDateMetaData.timeInfo}};class ae extends A{constructor(e){super(e),this.declaredClass="esri.arcade.featureset.sources.FeatureLayerMemory",this._removeGeometry=!1,this._overrideFields=null,this._forceIsTable=!1,e.spatialReference&&(this.spatialReference=e.spatialReference),this._transparent=!0,this._maxProcessing=1e3,this._layer=e.layer,this._wset=null,e.isTable===!0&&(this._forceIsTable=!0),e.outFields!==void 0&&(this._overrideFields=e.outFields),e.includeGeometry!==void 0&&(this._removeGeometry=e.includeGeometry===!1)}_maxQueryRate(){return te}end(){return this._layer}optimisePagingFeatureQueries(){}async loadImpl(){return this._layer.loaded===!0?(this._initialiseFeatureSet(),this):(await this._layer.load(),this._initialiseFeatureSet(),this)}get gdbVersion(){return""}_initialiseFeatureSet(){if(this.spatialReference==null&&(this.spatialReference=this._layer.spatialReference),this.geometryType=this._layer.geometryType,this.fields=this._layer.fields.slice(0),this._layer.outFields&&!(this._layer.outFields.length===1&&this._layer.outFields[0]==="*")){const e=[];for(const t of this.fields)if(t.type==="oid")e.push(t);else for(const i of this._layer.outFields)if(i.toLowerCase()===t.name.toLowerCase()){e.push(t);break}this.fields=e}if(this._overrideFields!==null)if(this._overrideFields.length===1&&this._overrideFields[0]==="*")this._overrideFields=null;else{const e=[],t=[];for(const i of this.fields)if(i.type==="oid")e.push(i),t.push(i.name);else for(const s of this._overrideFields)if(s.toLowerCase()===i.name.toLowerCase()){e.push(i),t.push(i.name);break}this.fields=e,this._overrideFields=t}this.objectIdField=this._layer.objectIdField;for(const e of this.fields)e.type==="global-id"&&(this.globalIdField=e.name);this.hasM=this._layer.supportsM,this.hasZ=this._layer.supportsZ,this._databaseType=k.Standardised,this.typeIdField=this._layer.typeIdField,this.types=this._layer.types}isTable(){return this._forceIsTable||this._layer.isTable||this._layer.type==="table"||!this._layer.geometryType}_isInFeatureSet(){return S.InFeatureSet}_candidateIdTransform(e){return e}async _getSet(e){if(this._wset===null){await this._ensureLoaded();const t=await this._getFilteredSet("",null,null,null,e);return this._wset=t,t}return this._wset}_changeFeature(e){const t={};for(const i of this.fields)t[i.name]=e.attributes[i.name];return new P({geometry:this._removeGeometry===!0?null:e.geometry,attributes:t})}async _getFilteredSet(e,t,i,s,a){let r="",n=!1;if(s!==null&&(r=s.constructClause(),n=!0),this.isTable()&&t&&e!==null&&e!=="")return new y([],[],!0,null);const l=new L;l.where=i===null?t===null?"1=1":"":R(i,k.Standardised),l.spatialRelationship=this._makeRelationshipEnum(e),l.outSpatialReference=this.spatialReference,l.orderByFields=r!==""?r.split(","):null,l.geometry=t===null?null:t,l.returnGeometry=!0,l.relationParameter=this._makeRelationshipParam(e);const u=await this._layer.queryFeatures(l);if(u===null)return new y([],[],n,null);this._checkCancelled(a);const o=[];return u.features.forEach(d=>{const h=d.attributes[this._layer.objectIdField];o.push(h),this._featureCache[h]=this._changeFeature(d)}),new y([],o,n,null)}_makeRelationshipEnum(e){if(e.includes("esriSpatialRelRelation"))return"relation";switch(e){case"esriSpatialRelRelation":return"relation";case"esriSpatialRelIntersects":return"intersects";case"esriSpatialRelContains":return"contains";case"esriSpatialRelOverlaps":return"overlaps";case"esriSpatialRelWithin":return"within";case"esriSpatialRelTouches":return"touches";case"esriSpatialRelCrosses":return"crosses";case"esriSpatialRelEnvelopeIntersects":return"envelope-intersects"}return e}_makeRelationshipParam(e){return e.includes("esriSpatialRelRelation")?e.split(":")[1]:""}async _queryAllFeatures(){if(this._wset)return this._wset;const e=new L;if(e.where="1=1",await this._ensureLoaded(),this._layer.source&&this._layer.source.items){const s=[];return this._layer.source.items.forEach(a=>{const r=a.attributes[this._layer.objectIdField];s.push(r),this._featureCache[r]=this._changeFeature(a)}),this._wset=new y([],s,!1,null),this._wset}const t=await this._layer.queryFeatures(e),i=[];return t.features.forEach(s=>{const a=s.attributes[this._layer.objectIdField];i.push(a),this._featureCache[a]=this._changeFeature(s)}),this._wset=new y([],i,!1,null),this._wset}async _getFeatures(e,t,i){const s=[];t!==-1&&this._featureCache[t]===void 0&&s.push(t);for(let a=e._lastFetchedIndex;a<e._known.length&&(e._lastFetchedIndex+=1,!(this._featureCache[e._known[a]]===void 0&&(e._known[a]!==t&&s.push(e._known[a]),s.length>i)));a++);if(s.length===0)return"success";throw new b(C.MissingFeatures)}async _refineSetBlock(e){return e}async _stat(){return{calculated:!1}}async _canDoAggregates(){return!1}relationshipMetaData(){return[]}static _cloneAttr(e){const t={};for(const i in e)t[i]=e[i];return t}nativeCapabilities(){return{title:this._layer.title??"",canQueryRelated:!1,source:this,capabilities:this._layer.capabilities,databaseType:this._databaseType,requestStandardised:!0}}static create(e,t){let i=e.layerDefinition.objectIdField;const s=e.layerDefinition.typeIdField?e.layerDefinition.typeIdField:"",a=[];if(e.layerDefinition.types)for(const f of e.layerDefinition.types)a.push(Me.fromJSON(f));let r=e.layerDefinition.geometryType;r===void 0&&(r=e.featureSet.geometryType||"");let n=e.featureSet.features;const l=t.toJSON();if(i===""||i===void 0){let f=!1;for(const _ of e.layerDefinition.fields)if(_.type==="oid"||_.type==="esriFieldTypeOID"){i=_.name,f=!0;break}if(f===!1){let _="FID",m=!0,I=0;for(;m;){let p=!0;for(const D of e.layerDefinition.fields)if(D.name===_){p=!1;break}p===!0?m=!1:(I++,_="FID"+I.toString())}e.layerDefinition.fields.push({type:"esriFieldTypeOID",name:_,alias:_});const F=[];for(let p=0;p<n.length;p++)F.push({geometry:e.featureSet.features[p].geometry,attributes:e.featureSet.features[p].attributes?this._cloneAttr(e.featureSet.features[p].attributes):{}}),F[p].attributes[_]=p;n=F,i=_}}const u=[];for(const f of e.layerDefinition.fields)f instanceof j?u.push(f):u.push(j.fromJSON(f));let o=r;switch(o||(o=""),o){case"esriGeometryPoint":o="point";break;case"esriGeometryPolyline":o="polyline";break;case"esriGeometryPolygon":o="polygon";break;case"esriGeometryExtent":o="extent";break;case"esriGeometryMultipoint":o="multipoint";break;case"":case"esriGeometryNull":o="esriGeometryNull"}if(o!=="esriGeometryNull")for(const f of n)f.geometry&&!(f.geometry instanceof Ee)&&(f.geometry.type=o,f.geometry.spatialReference===void 0&&(f.geometry.spatialReference=l));else for(const f of n)f.geometry&&(f.geometry=null);const d={outFields:["*"],source:n,fields:u,types:a,typeIdField:s,objectIdField:i,spatialReference:t};o!==""&&o!=="esriGeometryNull"&&o!==null&&(d.geometryType=o);const h=new T(d);return new ae({layer:h,spatialReference:t,isTable:o===null||o===""||o==="esriGeometryNull"})}async queryAttachments(){return[]}async getFeatureByObjectId(e){const t=new L;t.where=this.objectIdField+"="+e.toString();const i=await this._layer.queryFeatures(t);return i.features.length===1?i.features[0]:null}async getOwningSystemUrl(){return""}async getIdentityUser(){return""}get preferredTimeReference(){var e,t;return((t=(e=this._layer)==null?void 0:e.preferredTimeReference)==null?void 0:t.toJSON())??null}get dateFieldsTimeReference(){var e,t;return((t=(e=this._layer)==null?void 0:e.dateFieldsTimeReference)==null?void 0:t.toJSON())??null}get datesInUnknownTimezone(){var e;return(e=this._layer)==null?void 0:e.datesInUnknownTimezone}get editFieldsInfo(){var e,t;return((t=(e=this._layer)==null?void 0:e.editFieldsInfo)==null?void 0:t.toJSON())??null}get timeInfo(){var e,t;return((t=(e=this._layer)==null?void 0:e.timeInfo)==null?void 0:t.toJSON())??null}}class tt extends A{constructor(e){super(e),this.declaredClass="esri.arcade.featureset.sources.FeatureLayerRelated",this._findObjectId=-1,this._requestStandardised=!1,this._removeGeometry=!1,this._overrideFields=null,this.featureObjectId=null,e.spatialReference&&(this.spatialReference=e.spatialReference),this._transparent=!0,this._maxProcessing=1e3,this._layer=e.layer,this._wset=null,this._findObjectId=e.objectId,this.featureObjectId=e.objectId,this.relationship=e.relationship,this._relatedLayer=e.relatedLayer,e.outFields!==void 0&&(this._overrideFields=e.outFields),e.includeGeometry!==void 0&&(this._removeGeometry=e.includeGeometry===!1)}_maxQueryRate(){return te}end(){return this._layer}optimisePagingFeatureQueries(){}async loadImpl(){var e;return await Promise.all([this._layer.load(),(e=this._relatedLayer)==null?void 0:e.load()]),this._initialiseFeatureSet(),this}nativeCapabilities(){return this._relatedLayer.nativeCapabilities()}_initialiseFeatureSet(){if(this.spatialReference==null&&(this.spatialReference=this._layer.spatialReference),this.geometryType=this._relatedLayer.geometryType,this.fields=this._relatedLayer.fields.slice(0),this._overrideFields!==null)if(this._overrideFields.length===1&&this._overrideFields[0]==="*")this._overrideFields=null;else{const t=[],i=[];for(const s of this.fields)if(s.type==="oid")t.push(s),i.push(s.name);else for(const a of this._overrideFields)if(a.toLowerCase()===s.name.toLowerCase()){t.push(s),i.push(s.name);break}this.fields=t,this._overrideFields=i}const e=this._layer.nativeCapabilities();e&&(this._databaseType=e.databaseType,this._requestStandardised=e.requestStandardised),this.objectIdField=this._relatedLayer.objectIdField,this.globalIdField=this._relatedLayer.globalIdField,this.hasM=this._relatedLayer.supportsM,this.hasZ=this._relatedLayer.supportsZ,this.typeIdField=this._relatedLayer.typeIdField,this.types=this._relatedLayer.types}async databaseType(){return await this._relatedLayer.databaseType(),this._databaseType=this._relatedLayer._databaseType,this._databaseType}isTable(){return this._relatedLayer.isTable()}_isInFeatureSet(){return S.InFeatureSet}_candidateIdTransform(e){return e}async _getSet(e){if(this._wset===null){await this._ensureLoaded();const t=await this._getFilteredSet("",null,null,null,e);return this._wset=t,t}return this._wset}_changeFeature(e){const t={};for(const i of this.fields)t[i.name]=e.attributes[i.name];return new P({geometry:this._removeGeometry===!0?null:e.geometry,attributes:t})}async _getFilteredSet(e,t,i,s,a){var F;if(await this.databaseType(),this.isTable()&&t&&e!==null&&e!=="")return new y([],[],!0,null);const r=this._layer.nativeCapabilities();if(r.canQueryRelated===!1)return new y([],[],!0,null);if((F=r.capabilities)!=null&&F.queryRelated&&r.capabilities.queryRelated.supportsPagination)return this._getFilteredSetUsingPaging(e,t,i,s,a);let n="",l=!1;s!==null&&r.capabilities&&r.capabilities.queryRelated&&r.capabilities.queryRelated.supportsOrderBy===!0&&(n=s.constructClause(),l=!0);const u=new he;u.objectIds=[this._findObjectId];const o=this._overrideFields!==null?this._overrideFields:this._fieldsIncludingObjectId(this._relatedLayer.fields?this._relatedLayer.fields.map(p=>p.name):["*"]);u.outFields=o,u.relationshipId=this.relationship.id,u.where="1=1";let d=!0;this._removeGeometry===!0&&(d=!1),u.returnGeometry=d,this._requestStandardised&&(u.sqlFormat="standard"),u.outSpatialReference=this.spatialReference,u.orderByFields=n!==""?n.split(","):null;const h=await r.source.queryRelatedFeatures(u);this._checkCancelled(a);const f=h[this._findObjectId]?h[this._findObjectId].features:[],_=[];for(let p=0;p<f.length;p++)this._featureCache[f[p].attributes[this._relatedLayer.objectIdField]]=f[p],_.push(f[p].attributes[this._relatedLayer.objectIdField]);const m=t&&e!==null&&e!=="",I=i!=null;return new y(m||I?_:[],m||I?[]:_,l,null)}_fieldsIncludingObjectId(e){if(e===null)return[this.objectIdField];const t=e.slice(0);if(t.includes("*"))return t;let i=!1;for(const s of t)if(s.toUpperCase()===this.objectIdField.toUpperCase()){i=!0;break}return i===!1&&t.push(this.objectIdField),t}async _getFilteredSetUsingPaging(e,t,i,s,a){var F,p;let r="",n=!1;const l=this._layer.nativeCapabilities();s!==null&&l&&((F=l.capabilities)!=null&&F.queryRelated)&&l.capabilities.queryRelated.supportsOrderBy===!0&&(r=s.constructClause(),n=!0),await this.databaseType();const u="1=1";let o=this._maxQueryRate();const d=(p=l.capabilities)==null?void 0:p.query.maxRecordCount;d!=null&&d<o&&(o=d);const h=t&&e!==null&&e!=="",f=i!=null;let _=null,m=!0;this._removeGeometry===!0&&(m=!1);const I=this._overrideFields!==null?this._overrideFields:this._fieldsIncludingObjectId(this._relatedLayer.fields?this._relatedLayer.fields.map(D=>D.name):["*"]);return _=new y(h||f?["GETPAGES"]:[],h||f?[]:["GETPAGES"],n,{outFields:I.join(","),resultRecordCount:o,resultOffset:0,objectIds:[this._findObjectId],where:u,orderByFields:r,returnGeometry:m,returnIdsOnly:"false",internal:{set:[],lastRetrieved:0,lastPage:0,fullyResolved:!1}}),await this._expandPagedSet(_,o,0,0,a),_}_expandPagedSet(e,t,i,s,a){return this._expandPagedSetFeatureSet(e,t,i,s,a)}_clonePageDefinition(e){return e===null?null:e.groupbypage!==!0?{groupbypage:!1,outFields:e.outFields,resultRecordCount:e.resultRecordCount,resultOffset:e.resultOffset,where:e.where,objectIds:e.objectIds,orderByFields:e.orderByFields,returnGeometry:e.returnGeometry,returnIdsOnly:e.returnIdsOnly,internal:e.internal}:{groupbypage:!0,outFields:e.outFields,resultRecordCount:e.resultRecordCount,useOIDpagination:e.useOIDpagination,generatedOid:e.generatedOid,groupByFieldsForStatistics:e.groupByFieldsForStatistics,resultOffset:e.resultOffset,outStatistics:e.outStatistics,geometry:e.geometry,where:e.where,objectIds:e.objectIds,orderByFields:e.orderByFields,returnGeometry:e.returnGeometry,returnIdsOnly:e.returnIdsOnly,internal:e.internal}}async _getPhysicalPage(e,t,i){const s=e.pagesDefinition.internal.lastRetrieved,a=s,r=e.pagesDefinition.internal.lastPage,n=this._layer.nativeCapabilities(),l=new he;this._requestStandardised===!0&&(l.sqlFormat="standard"),l.relationshipId=this.relationship.id,l.objectIds=e.pagesDefinition.objectIds,l.resultOffset=e.pagesDefinition.internal.lastPage,l.resultRecordCount=e.pagesDefinition.resultRecordCount,l.outFields=e.pagesDefinition.outFields.split(","),l.where=e.pagesDefinition.where,l.orderByFields=e.pagesDefinition.orderByFields!==""?e.pagesDefinition.orderByFields.split(","):null,l.returnGeometry=e.pagesDefinition.returnGeometry,l.outSpatialReference=this.spatialReference;const u=await n.source.queryRelatedFeatures(l);if(this._checkCancelled(i),e.pagesDefinition.internal.lastPage!==r)return 0;const o=u[this._findObjectId]?u[this._findObjectId].features:[];for(let h=0;h<o.length;h++)e.pagesDefinition.internal.set[a+h]=o[h].attributes[this._relatedLayer.objectIdField];for(let h=0;h<o.length;h++)this._featureCache[o[h].attributes[this._relatedLayer.objectIdField]]=o[h];const d=!u[this._findObjectId]||u[this._findObjectId].exceededTransferLimit===!1;return o.length!==e.pagesDefinition.resultRecordCount&&d&&(e.pagesDefinition.internal.fullyResolved=!0),e.pagesDefinition.internal.lastRetrieved=s+o.length,e.pagesDefinition.internal.lastPage+=e.pagesDefinition.resultRecordCount,o.length}async _getFeatures(e,t,i,s){const a=[];t!==-1&&this._featureCache[t]===void 0&&a.push(t);const r=this._maxQueryRate();if(this._checkIfNeedToExpandKnownPage(e,r)===!0)return await this._expandPagedSet(e,r,0,0,s),this._getFeatures(e,t,i,s);let n=0;for(let l=e._lastFetchedIndex;l<e._known.length&&(n++,n<=i&&(e._lastFetchedIndex+=1),!(e._known[l]!=="GETPAGES"&&this._featureCache[e._known[l]]===void 0&&(e._known[l]!==t&&a.push(e._known[l]),a.length>i)))&&!(n>=i&&a.length===0);l++);if(a.length===0)return"success";throw new b(C.MissingFeatures)}async _refineSetBlock(e,t,i){return e}async _stat(e,t,i,s,a,r,n){return{calculated:!1}}get gdbVersion(){return this._relatedLayer.gdbVersion}async _canDoAggregates(e,t,i,s,a){return!1}relationshipMetaData(){return this._relatedLayer.relationshipMetaData()}serviceUrl(){return this._relatedLayer.serviceUrl()}queryAttachments(e,t,i,s,a){return this._relatedLayer.queryAttachments(e,t,i,s,a)}getFeatureByObjectId(e,t){return this._relatedLayer.getFeatureByObjectId(e,t)}getOwningSystemUrl(){return this._relatedLayer.getOwningSystemUrl()}getIdentityUser(){return this._relatedLayer.getIdentityUser()}getDataSourceFeatureSet(){return this._relatedLayer}get preferredTimeReference(){var e;return((e=this._relatedLayer)==null?void 0:e.preferredTimeReference)??null}get dateFieldsTimeReference(){var e;return((e=this._relatedLayer)==null?void 0:e.dateFieldsTimeReference)??null}get datesInUnknownTimezone(){var e;return(e=this._relatedLayer)==null?void 0:e.datesInUnknownTimezone}get editFieldsInfo(){var e;return((e=this._relatedLayer)==null?void 0:e.editFieldsInfo)??null}get timeInfo(){var e;return((e=this._relatedLayer)==null?void 0:e.timeInfo)??null}}function it(){g.applicationCache===null&&(g.applicationCache=new g)}async function Y(c,e){if(g.applicationCache){const t=g.applicationCache.getLayerInfo(c);if(t){const a=await t;return new T({url:c,outFields:e,sourceJSON:a})}const i=new T({url:c,outFields:e}),s=(async()=>(await i.load(),i.sourceJSON))();if(g.applicationCache){g.applicationCache.setLayerInfo(c,s);try{return await s,i}catch(a){throw g.applicationCache.clearLayerInfo(c),a}}return await s,i}return new T({url:c,outFields:e})}async function re(c,e,t,i,s,a=null){return E(await Y(c,["*"]),e,t,i,s,a)}function E(c,e=null,t=null,i=!0,s=null,a=null){const r={layer:c,spatialReference:e,outFields:t,includeGeometry:i,lrucache:s,interceptor:a};return c._hasMemorySource()===!0?new ae(r):new et(r)}async function st(c){if(g.applicationCache!==null){const t=g.applicationCache.getLayerInfo(c);if(t!==null)return t}const e=(async()=>{const t=await G(c,{responseType:"json",query:{f:"json"}});return t.data?t.data:null})();if(g.applicationCache!==null){g.applicationCache.setLayerInfo(c,e);try{return await e}catch(t){throw g.applicationCache.clearLayerInfo(c),t}}return e}async function at(c,e){const t="QUERYDATAELEMTS:"+e.toString()+":"+c;if(g.applicationCache!==null){const s=g.applicationCache.getLayerInfo(t);if(s!==null)return s}const i=(async()=>{const s=await G(c+"/queryDataElements",{method:"post",responseType:"json",query:{layers:JSON.stringify([e.toString()]),f:"json"}});if(s.data){const a=s.data;if(a.layerDataElements&&a.layerDataElements[0])return a.layerDataElements[0]}throw new b(C.DataElementsNotFound)})();if(g.applicationCache!==null){g.applicationCache.setLayerInfo(t,i);try{return await i}catch(s){throw g.applicationCache.clearLayerInfo(t),s}}return i}async function Se(c){if(g.applicationCache!==null){const t=g.applicationCache.getLayerInfo(c);if(t!==null)return t}const e=(async()=>{const t=await G(c,{responseType:"json",query:{f:"json"}});if(t.data){const i=t.data;return i.layers||(i.layers=[]),i.tables||(i.tables=[]),i}return{layers:[],tables:[]}})();if(g.applicationCache!==null){g.applicationCache.setLayerInfo(c,e);try{return await e}catch(t){throw g.applicationCache.clearLayerInfo(c),t}}return e}async function rt(c,e){const t={metadata:null,networkId:-1,unVersion:3,terminals:[],queryelem:null,layerNameLkp:{},lkp:null},i=await Se(c);if(t.metadata=i,i.controllerDatasetLayers&&i.controllerDatasetLayers.utilityNetworkLayerId!==void 0&&i.controllerDatasetLayers.utilityNetworkLayerId!==null){if(i.layers)for(const r of i.layers)t.layerNameLkp[r.id]=r.name;if(i.tables)for(const r of i.tables)t.layerNameLkp[r.id]=r.name;const s=i.controllerDatasetLayers.utilityNetworkLayerId;t.networkId=s;const a=await at(c,s);if(a){t.queryelem=a,t.queryelem&&t.queryelem.dataElement&&t.queryelem.dataElement.schemaGeneration!==void 0&&(t.unVersion=t.queryelem.dataElement.schemaGeneration),t.lkp={},t.queryelem.dataElement.domainNetworks||(t.queryelem.dataElement.domainNetworks=[]);for(const n of t.queryelem.dataElement.domainNetworks){for(const l of n.edgeSources?n.edgeSources:[]){const u={layerId:l.layerId,sourceId:l.sourceId,className:t.layerNameLkp[l.layerId]?t.layerNameLkp[l.layerId]:null};u.className&&(t.lkp[u.className]=u)}for(const l of n.junctionSources?n.junctionSources:[]){const u={layerId:l.layerId,sourceId:l.sourceId,className:t.layerNameLkp[l.layerId]?t.layerNameLkp[l.layerId]:null};u.className&&(t.lkp[u.className]=u)}}if(t.queryelem.dataElement.terminalConfigurations)for(const n of t.queryelem.dataElement.terminalConfigurations)for(const l of n.terminals)t.terminals.push({terminalId:l.terminalId,terminalName:l.terminalName});const r=await st(c+"/"+s);if(r.systemLayers&&r.systemLayers.associationsTableId!==void 0&&r.systemLayers.associationsTableId!==null){const n=[];t.unVersion>=4&&(n.push("STATUS"),n.push("PERCENTALONG"));let l=await re(c+"/"+r.systemLayers.associationsTableId.toString(),e,["OBJECTID","FROMNETWORKSOURCEID","TONETWORKSOURCEID","FROMGLOBALID","TOGLOBALID","TOTERMINALID","FROMTERMINALID","ASSOCIATIONTYPE","ISCONTENTVISIBLE","GLOBALID",...n],!1,null,null);return await l.load(),t.unVersion>=4&&(l=l.filter(N.create("STATUS NOT IN (1, 2, 3, 4, 5, 6, 7, 9, 10, 11, 12, 13, 14, 15, 17, 18, 19, 20, 21, 22, 23, 25, 26, 27, 28, 29, 30, 31, 33, 34, 35, 36, 37, 38, 39, 41, 42, 43, 44, 45, 46, 47, 49, 50, 51, 52, 53, 54, 55, 57, 58, 59, 60, 61, 62,63)",l.getFieldsIndex())),await l.load()),{lkp:t.lkp,associations:l,unVersion:t.unVersion,terminals:t.terminals}}return{associations:null,unVersion:t.unVersion,lkp:null,terminals:[]}}return{associations:null,unVersion:t.unVersion,lkp:null,terminals:[]}}return{associations:null,unVersion:t.unVersion,lkp:null,terminals:[]}}async function nt(c,e,t,i=null,s=null,a=!0,r=null,n=null){let l=c.serviceUrl();if(!l)return null;l=l.charAt(l.length-1)==="/"?l+e.relatedTableId.toString():l+"/"+e.relatedTableId.toString();const u=await re(l,i,s,a,r,n);return new tt({layer:c,relatedLayer:u,relationship:e,objectId:t,spatialReference:i,outFields:s,includeGeometry:a,lrucache:r,interceptor:n})}U.registerAction(),Ye.registerAction(),Z.registerAction(),ke.registerAction(),se.registerAction();class lt extends me{constructor(e,t=null,i=null,s=null){super(),this._map=e,this._overridespref=t,this._lrucache=i,this._interceptor=s,this._instantLayers=[]}_makeAndAddFeatureSet(e,t=!0,i=null){const s=E(e,this._overridespref,i===null?["*"]:i,t,this._lrucache,this._interceptor);return this._instantLayers.push({featureset:s,opitem:e,includeGeometry:t,outFields:JSON.stringify(i)}),s}async featureSetByName(e,t=!0,i=null){if(this._map.loaded!==void 0&&this._map.load!==void 0&&this._map.loaded===!1)return await this._map.load(),this.featureSetByName(e,t,i);i===null&&(i=["*"]),i=(i=i.slice(0)).sort();const s=JSON.stringify(i);for(let r=0;r<this._instantLayers.length;r++){const n=this._instantLayers[r];if(n.opitem.title===e&&n.includeGeometry===t&&n.outFields===s)return this._instantLayers[r].featureset}const a=this._map.allLayers.find(r=>r instanceof T&&r.title===e);if(a)return this._makeAndAddFeatureSet(a,t,i);if(this._map.tables){const r=this._map.tables.find(n=>!!(n.title&&n.title===e||n.title&&n.title===e));if(r){if(r instanceof T)return this._makeAndAddFeatureSet(r,t,i);if(!r._materializedTable){const n=r.outFields?r:{...r,outFields:["*"]};r._materializedTable=new T(n)}return await r._materializedTable.load(),this._makeAndAddFeatureSet(r._materializedTable,t,i)}}return null}async featureSetById(e,t=!0,i=["*"]){if(this._map.loaded!==void 0&&this._map.load!==void 0&&this._map.loaded===!1)return await this._map.load(),this.featureSetById(e,t,i);i===null&&(i=["*"]),i=(i=i.slice(0)).sort();const s=JSON.stringify(i);for(let r=0;r<this._instantLayers.length;r++){const n=this._instantLayers[r];if(n.opitem.id===e&&n.includeGeometry===t&&n.outFields===s)return this._instantLayers[r].featureset}const a=this._map.allLayers.find(r=>r instanceof T&&r.id===e);if(a)return this._makeAndAddFeatureSet(a,t,i);if(this._map.tables){const r=this._map.tables.find(n=>n.id===e);if(r){if(r instanceof T)return this._makeAndAddFeatureSet(r,t,i);if(!r._materializedTable){const n={...r,outFields:["*"]};r._materializedTable=new T(n)}return await r._materializedTable.load(),this._makeAndAddFeatureSet(r._materializedTable,t,i)}}return null}}class ne extends me{constructor(e,t=null,i=null,s=null){super(),this._url=e,this._overridespref=t,this._lrucache=i,this._interceptor=s,this.metadata=null,this._instantLayers=[]}get url(){return this._url}_makeAndAddFeatureSet(e,t=!0,i=null){const s=E(e,this._overridespref,i===null?["*"]:i,t,this._lrucache);return this._instantLayers.push({featureset:s,opitem:e,includeGeometry:t,outFields:JSON.stringify(i)}),s}async _loadMetaData(){const e=await Se(this._url);return this.metadata=e,e}load(){return this._loadMetaData()}clone(){return new ne(this._url,this._overridespref,this._lrucache,this._interceptor)}async featureSetByName(e,t=!0,i=null){i===null&&(i=["*"]),i=(i=i.slice(0)).sort();const s=JSON.stringify(i);for(let n=0;n<this._instantLayers.length;n++){const l=this._instantLayers[n];if(l.opitem.title===e&&l.includeGeometry===t&&l.outFields===s)return this._instantLayers[n].featureset}const a=await this._loadMetaData();let r=null;for(const n of a.layers?a.layers:[])n.name===e&&(r=n);if(!r)for(const n of a.tables?a.tables:[])n.name===e&&(r=n);if(r){const n=await Y(this._url+"/"+r.id,["*"]);return this._makeAndAddFeatureSet(n,t,i)}return null}async featureSetById(e,t=!0,i=["*"]){i===null&&(i=["*"]),i=(i=i.slice(0)).sort();const s=JSON.stringify(i);e=e!=null?e.toString():"";for(let n=0;n<this._instantLayers.length;n++){const l=this._instantLayers[n];if(l.opitem.id===e&&l.includeGeometry===t&&l.outFields===s)return this._instantLayers[n].featureset}const a=await this._loadMetaData();let r=null;for(const n of a.layers?a.layers:[])n.id!==null&&n.id!==void 0&&n.id.toString()===e&&(r=n);if(!r)for(const n of a.tables?a.tables:[])n.id!==null&&n.id!==void 0&&n.id.toString()===e&&(r=n);if(r){const n=await Y(this._url+"/"+r.id,["*"]);return this._makeAndAddFeatureSet(n,t,i)}return null}}function ot(c,e,t=null,i=null){return new lt(c,e,t,i)}function ut(c,e,t=null,i=null){return new ne(c,e,t,i)}function dt(c,e,t,i,s){if(c===null)return null;if(c instanceof T){switch(e){case"datasource":return E(c,s,c.outFields,!0,t,i).getDataSourceFeatureSet();case"parent":case"root":return E(c,s,c.outFields,!0,t,i)}return null}if(c instanceof A)switch(e){case"datasource":return c.getDataSourceFeatureSet();case"parent":return c;case"root":return c.getRootFeatureSet()}return null}async function ht(c,e,t,i,s,a,r,n=null){if(g.applicationCache){const u=g.applicationCache.getLayerInfo(c+":"+a.url);if(u){const o=await u;return E(new T({url:z(o.url)+"/"+e,outFields:["*"]}),t,i,s,r,n)}}const l=new We({id:c,portal:a}).load();g.applicationCache&&g.applicationCache.setLayerInfo(c+":"+a.url,l);try{const u=await l;return E(new T({url:z(u.url??"")+"/"+e,outFields:["*"]}),t,i,s,r,n)}catch(u){throw g.applicationCache&&g.applicationCache.clearLayerInfo(c+":"+a.url),u}}const bt=Object.freeze(Object.defineProperty({__proto__:null,constructAssociationMetaDataFeatureSetFromUrl:rt,constructFeatureSet:E,constructFeatureSetFromPortalItem:ht,constructFeatureSetFromRelationship:nt,constructFeatureSetFromUrl:re,convertToFeatureSet:dt,createFeatureSetCollectionFromMap:ot,createFeatureSetCollectionFromService:ut,initialiseMetaDataCache:it},Symbol.toStringTag,{value:"Module"}));export{rt as A,nt as C,Ze as D,dt as E,re as I,E as S,B as a,Z as b,U as c,se as d,me as e,ae as f,ze as g,K as h,bt as i,ht as j,Je as x,Ke as y};
