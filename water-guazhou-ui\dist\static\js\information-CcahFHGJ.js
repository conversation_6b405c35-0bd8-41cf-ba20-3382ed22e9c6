import{_ as F}from"./TreeBox-DDD2iwoR.js";import{_ as q}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as M}from"./CardTable-rdWOL4_6.js";import{_ as R}from"./CardSearch-CB_HNR-Q.js";import{_ as V}from"./index-BJ-QPYom.js";import{d as O,M as w,c as _,x as n,s as B,r as y,a8 as S,a9 as v,S as W,o as $,g as C,h as L,F as T,q as I,i as p,b7 as A}from"./index-r0dFAfgr.js";import{I as d}from"./common-CvK_P_ao.js";import{b as U,c as Y,e as z,g as G,f as H}from"./device-DWHb0XjG.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";const le=O({__name:"information",setup(Q){const{$btnPerms:k}=w(),u=_(),b=_(),E=_(new Date().toString()),N=_({filters:[{label:"设备编码",field:"serialId",type:"input"},{label:"设备名称",field:"name",type:"input"},{label:"设备型号",field:"model",type:"input"}],operations:[{type:"btn-group",btns:[{perm:!0,text:"新建",icon:d.ADD,type:"success",click:()=>P("新建")},{perm:!0,text:"批量删除",icon:d.DELETE,type:"danger",click:()=>{var e;if(((e=a.selectList)==null?void 0:e.length)===0){n.warning("请选中需要删除的设备");return}x()}},{type:"default",perm:!0,text:"重置",svgIcon:B(A),click:()=>{var e;(e=b.value)==null||e.resetForm(),c()}},{perm:!0,text:"查询",icon:d.QUERY,click:()=>c()}]}]}),a=y({defaultExpandAll:!0,indexVisible:!0,selectList:[],handleSelectChange:e=>{a.selectList=e},columns:[{label:"设备编码",prop:"serialId"},{label:"设备名称",prop:"name"},{label:"设备型号",prop:"model"},{label:"所属大类",prop:"topTypeName"},{label:"所属类别",prop:"typeName"},{label:"设备标识",prop:"mark"},{label:"计量单位",prop:"unit"},{label:"备注",prop:"remark"},{label:"创建人",prop:"creatorName"},{label:"创建时间",prop:"createTimeName"}],operationWidth:"200px",operations:[{type:"primary",text:"编辑",icon:d.EDIT,perm:k("RoleManageEdit"),click:e=>j(e)},{type:"danger",text:"删除",perm:k("RoleManageDelete"),icon:d.DELETE,click:e=>x(e)}],dataList:[],pagination:{page:1,limit:20,total:0,refreshData:({page:e,size:t})=>{a.pagination.page=e,a.pagination.limit=t,c()}}}),o=y({title:"新增",labelWidth:"120px",dialogWidth:"600px",submit:e=>{e.serialId=s.prepend+e.serialIdNum;for(const t in a.dataList)if(e.serialId===a.dataList[t].serialId&&e.serialId!==s.serialId){n.warning("设备编码重复");return}for(const t in e)t.indexOf("autoField.")!==-1&&e[t];U(e).then(t=>{var r;t.data.code===500?n.success(t.data.message):((r=u.value)==null||r.closeDialog(),n.success("操作成功"),c())}).catch(t=>{n.warning(t)})},defaultValue:{},group:[{fields:[{type:"select-tree",label:"所属类别",field:"typeId",checkStrictly:!0,options:S(()=>v(l.data)),readonly:!0},{type:"hint",text:"注：编码规则(长度14)>=12(级别1)+001(级别2)+001(级别3)+000000(设备编码)"},{type:"input",label:"设备编码",field:"serialIdNum",prepend:S(()=>s.prepend),rules:[{required:!0,message:"请输入设备编码"},{min:6,max:6,message:"编码为6位"}]},{type:"input",label:"设备名称",field:"name",rules:[{required:!0,message:"请输入设备名称"}]},{type:"input",label:"设备型号",field:"model",rules:[{required:!0,message:"请输入设备型号"}]},{type:"input",label:"设备标识",field:"mark"},{type:"input",label:"计量单位",field:"unit"},{type:"textarea",label:"备注",field:"remark"}]}]}),l=y({title:"设备类别",data:[],currentProject:{},expandOnClickNode:!1,isFilterTree:!0,treeNodeHandleClick:e=>{l.currentProject=e,c()}}),P=e=>{var t;if(!l.currentProject.id){n.warning("请选中设备类型");return}if(l.currentProject.level===0){n.warning("请选择设备类别");return}s.prepend=l.currentProject.serialId.slice(0,8),o.title=e,o.defaultValue={typeId:l.currentProject.id||"",useYear:"0",maintenanceCycle:"0",minStock:"0"},(t=u.value)==null||t.openDialog()},j=e=>{var t;o.title="编辑",s.serialId=e.serialId,s.prepend=e.serialId.slice(0,8),e.serialIdNum=e.serialId.slice(8,14),o.group[0].fields.length>16&&o.group[0].fields.splice(13,o.group[0].fields.length-16),o.defaultValue={...e||{}},(t=u.value)==null||t.openDialog()},x=e=>{W("确定删除选中设备","删除提示").then(()=>{var t;if(e)Y(e.id).then(()=>{n.success("删除成功"),h()}).catch(r=>{n.error(r.toString())});else{const r=((t=a.selectList)==null?void 0:t.map(i=>i.id))??[];z(r).then(()=>{n.success("删除成功"),h()}).catch(i=>{n.error(i.toString())})}})};function h(){G().then(e=>{l.data=v(e.data.data||[]),c()})}const s=y({prepend:"",serialId:""}),c=async()=>{var t,r;const e={size:a.pagination.limit||20,page:a.pagination.page||1,typeSerialId:(t=l.currentProject)==null?void 0:t.serialId,...(r=b.value)==null?void 0:r.queryParams};H(e).then(i=>{var m,f,g,D;a.dataList=((f=(m=i==null?void 0:i.data)==null?void 0:m.data)==null?void 0:f.data)||[],a.pagination.total=((D=(g=i==null?void 0:i.data)==null?void 0:g.data)==null?void 0:D.total)||0})};return $(async()=>{h()}),(e,t)=>{const r=V,i=R,m=M,f=q,g=F;return C(),L(g,null,{tree:T(()=>[I(r,{"tree-data":p(l)},null,8,["tree-data"])]),default:T(()=>[I(i,{ref_key:"refSearch",ref:b,config:p(N)},null,8,["config"]),I(m,{config:p(a),class:"card-table"},null,8,["config"]),(C(),L(f,{key:p(E),ref_key:"refForm",ref:u,config:p(o)},null,8,["config"]))]),_:1})}}});export{le as default};
