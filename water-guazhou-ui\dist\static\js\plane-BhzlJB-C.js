import{aR as M,ac as L,ae as T,aP as O,aW as h,aQ as d,ab as s,b1 as R,af as _,aX as x}from"./MapView-DaoQedLH.js";import{R as W}from"./index-r0dFAfgr.js";import{c as I,r as j,f as C}from"./sphere-NgXH-gLx.js";function G(n){const r=n[0]*n[0]+n[4]*n[4]+n[8]*n[8],u=n[1]*n[1]+n[5]*n[5]+n[9]*n[9],t=n[2]*n[2]+n[6]*n[6]+n[10]*n[10];return Math.sqrt(Math.max(r,u,t))}function S(n,r){const u=Math.sqrt(r[0]*r[0]+r[4]*r[4]+r[8]*r[8]),t=Math.sqrt(r[1]*r[1]+r[5]*r[5]+r[9]*r[9]),o=Math.sqrt(r[2]*r[2]+r[6]*r[6]+r[10]*r[10]);return M(n,u,t,o),n}function k(n,r,u){Math.abs(n[0])>Math.abs(n[1])?M(r,0,1,0):M(r,1,0,0),L(u,n,r),T(r,r),L(r,u,n),T(u,u)}function v(n=F){return[n[0],n[1],n[2],n[3]]}function n1(n=F[0],r=F[1],u=F[2],t=F[3]){return y(n,r,u,t,j.get())}function w(n,r){return y(r[0],r[1],r[2],r[3],n)}function y(n,r,u,t,o=v()){return o[0]=n,o[1]=r,o[2]=u,o[3]=t,o}function z(n,r,u){const t=r[0]*r[0]+r[1]*r[1]+r[2]*r[2],o=Math.abs(t-1)>1e-5&&t>1e-12?1/Math.sqrt(t):1;return u[0]=r[0]*o,u[1]=r[1]*o,u[2]=r[2]*o,u[3]=-(u[0]*n[0]+u[1]*n[1]+u[2]*n[2]),u}function B(n,r,u,t=v()){const o=u[0]-r[0],a=u[1]-r[1],c=u[2]-r[2],e=n[0]-r[0],i=n[1]-r[1],A=n[2]-r[2],E=a*A-c*i,P=c*e-o*A,X=o*i-a*e,l=E*E+P*P+X*X,p=Math.abs(l-1)>1e-5&&l>1e-12?1/Math.sqrt(l):1;return t[0]=E*p,t[1]=P*p,t[2]=X*p,t[3]=-(t[0]*n[0]+t[1]*n[1]+t[2]*n[2]),t}function H(n,r,u,t,o){const a=n.length/3;if(a<3)return!1;M($,n[3*u],n[3*u+1],n[3*u+2]);let c=t,e=!1;for(;c<a-1&&!e;){const i=3*c;M(g,n[i],n[i+1],n[i+2]),c++,e=!x($,g)}if(!e)return!1;for(c=Math.max(c,o),e=!1;c<a&&!e;){const i=3*c;M(N,n[i],n[i+1],n[i+2]),c++,h(b,$,g),T(b,b),h(m,g,N),T(m,m),e=!x($,N)&&!x(g,N)&&Math.abs(s(b,m))<J}return e?(B($,g,N,r),!0):(u!==0||t!==1||o!==2)&&H(n,r,0,1,2)}const J=.99619469809,$=_(),g=_(),N=_(),b=_(),m=_();function r1(n,r,u){return r!==n&&w(n,r),n[3]=-s(n,u),n}function u1(n,r){return r[0]=-n[0],r[1]=-n[1],r[2]=-n[2],r[3]=-n[3],r}function t1(n,r,u,t){return L(N,r,n),z(u,N,t)}function o1(n,r,u,t){return q(n,r,h(I.get(),u,r),U,t)}function c1(n,r,u){return!!W(r)&&q(n,r.origin,r.direction,V,u)}function e1(n,r,u){return q(n,r.origin,r.vector,f.NONE,u)}function a1(n,r,u){return q(n,r.origin,r.vector,f.CLAMP,u)}function i1(n,r){return Q(n,r)>=0}function f1(n,r){const u=s(n,r.ray.direction),t=-Q(n,r.ray.origin);if(u>-1e-6&&u<1e-6)return t>0;const o=t/u;return u>0?o<r.c1&&(r.c1=o):o>r.c0&&(r.c0=o),r.c0<=r.c1}function I1(n,r,u){const t=O(I.get(),n,-n[3]),o=K(n,h(I.get(),r,t),I.get());return d(u,o,t),u}function N1(n,r,u,t){const o=n,a=I.get(),c=I.get();k(o,a,c);const e=h(I.get(),u,r),i=C(a,e),A=C(c,e),E=C(o,e);return M(t,i,A,E)}function K(n,r,u){const t=O(I.get(),n,s(n,r));return h(u,r,t),u}function Q(n,r){return s(n,r)+n[3]}function q(n,r,u,t,o){const a=s(n,u);if(a===0)return!1;let c=-(s(n,r)+n[3])/a;return t&f.CLAMP&&(c=R(c,0,1)),!(!(t&f.INFINITE_MIN)&&c<0||!(t&f.INFINITE_MAX)&&c>1)&&(d(o,r,O(o,u,c)),!0)}function M1(n){return n}const F=[0,0,1,0];var f;(function(n){n[n.NONE=0]="NONE",n[n.CLAMP=1]="CLAMP",n[n.INFINITE_MIN=4]="INFINITE_MIN",n[n.INFINITE_MAX=8]="INFINITE_MAX"})(f||(f={}));const U=f.INFINITE_MIN|f.INFINITE_MAX,V=f.INFINITE_MAX;export{w as A,i1 as B,n1 as E,y as F,f1 as H,I1 as J,N1 as K,r1 as L,t1 as O,u1 as P,e1 as U,Q as V,M1 as Y,z as _,S as a,H as b,B as j,G as l,v as p,o1 as q,a1 as w,c1 as x};
