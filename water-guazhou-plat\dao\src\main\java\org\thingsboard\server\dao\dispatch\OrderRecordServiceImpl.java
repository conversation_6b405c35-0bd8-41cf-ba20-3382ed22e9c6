package org.thingsboard.server.dao.dispatch;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.smartProduction.dispatch.OrderRecord;
import org.thingsboard.server.dao.model.sql.smartProduction.dispatch.OrderRecordStatus;
import org.thingsboard.server.dao.sql.smartProduction.dispatch.OrderRecordMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch.*;

import java.util.List;

import static org.thingsboard.server.dao.model.sql.smartProduction.dispatch.OrderRecordStatus.WAITING_RECEIVE;

@Service
public class OrderRecordServiceImpl implements OrderRecordService {
    @Autowired
    private OrderRecordMapper mapper;

    @Override
    public IPage<OrderRecord> findAllConditional(OrderRecordPageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    public List<OrderRecord> save(List<OrderRecordSaveRequest> entitys) {
        // QueryUtil.arrangeTimedSaveRequests(entitys);
        return QueryUtil.saveOrUpdateBatchByRequest(entitys, mapper::saveAll, null);
    }

    @Override
    public boolean update(OrderRecord entity) {
        return mapper.update(entity);
    }

    @Override
    public boolean delete(List<String> idList) {
        return mapper.deleteBatchIds(idList) > 0;
    }

    @Override
    public boolean receive(OrderRecordReceiveRequest req) {
        return mapper.receive(req);
    }

    @Override
    public boolean reject(OrderRecordRejectRequest req) {
        return mapper.reject(req);
    }

    @Override
    public boolean reply(OrderRecordReplyRequest req) {
        return mapper.reply(req);
    }

    @Override
    public boolean execute(OrderRecordExecuteRequest req) {
        return mapper.execute(req);
    }

    @Override
    public boolean send(List<String> idList) {
        return mapper.sendBatch(idList, WAITING_RECEIVE) > 0;
    }

    @Override
    public boolean isStatus(String id, OrderRecordStatus status) {
        return mapper.isStatus(id, status);
    }
}
