<!--步骤管理-->
<template>
  <TreeBox v-loading="!!TreeData.loading">
    <template #tree>
      <SLTree :tree-data="TreeData" />
    </template>
    <CardSearch ref="refSearch" :config="searchConfig" />
    <CardTable ref="refTable" :config="tableConfig" class="card-table" />
    <DialogForm ref="refForm" :config="formConfig" />
  </TreeBox>
</template>
<script lang="ts" setup>
import {
  Delete,
  Plus,
  Edit,
  TrendCharts,
  Search
} from '@element-plus/icons-vue';
import { useBusinessStore } from '@/store';
import { SLConfirm } from '@/utils/Message';
import {
  processTypeList,
  processStepList,
  delProcessStep,
  editProcessStep,
  getAllUserByPidStr,
  formList
} from '@/api/engineeringManagement/process';
import { formatTree } from '@/utils/GlobalHelper';
import useGlobal from '@/hooks/global/useGlobal';
import { removeSlash } from '@/utils/removeIdSlash';
import useDepartment from '@/hooks/department/useDepartment';
import router from '@/router';

const { getDepartmentTree } = useDepartment();
const { $messageError, $messageSuccess, $messageWarning } = useGlobal();
const refTable = ref<ICardTableIns>();
const refSearch = ref<ICardSearchIns>();
const refForm = ref<IDialogFormIns>();
const state = reactive<{
  departmentTree: any;
  formData: any
}>({
  departmentTree: [],
  formData:[]
});
// 新增按钮
const searchConfig = reactive<ISearch>({
  filters: [{ type: 'input', label: '类型名称', field: 'name' }],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '新增',
          svgIcon: shallowRef(Plus),
          click: () => {
            formConfig.defaultValue = {
              departmentId: []
            };
            refForm.value?.openDialog();
          }
        },
        {
          perm: true,
          text: '查询',
          svgIcon: shallowRef(Search),
          click: () => refreshData()
        }
      ]
    }
  ]
});

// 数据列表配置
const tableConfig = reactive<ICardTable>({
  loading: true,
  indexVisible: true,
  columns: [
    { label: '编号', prop: 'code', minWidth: 120, align: 'center' },
    { label: '工程类型', prop: 'mainName', minWidth: 120, align: 'center' },
    { label: '节点序号', prop: 'orderNum', minWidth: 120, align: 'center' },
    { label: '节点位置', prop: 'type', minWidth: 120, align: 'center' },
    { label: '表单', prop: 'formName', minWidth: 120, align: 'center' },
    { label: '节点名称', prop: 'name', minWidth: 120, align: 'center' },
    {
      label: '预计执行天数',
      prop: 'executionDay',
      minWidth: 120,
      align: 'center'
    },
    { label: '显示步骤', prop: 'title', minWidth: 120, align: 'center' },
    { label: '操作人员', prop: 'userNames', minWidth: 120, align: 'center' }
  ],
  dataList: [],
  operationFixed: 'right',
  operationWidth: 260,
  operations: [
    {
      perm: true,
      text: '编辑',
      isTextBtn: false,
      svgIcon: shallowRef(Edit),
      click: (row) => handleAddEdit(row)
    },
    {
      perm: true,
      text: '删除',
      isTextBtn: false,
      type: 'danger',
      svgIcon: shallowRef(Delete),
      click: (row) => handleDelProcessStep([row.id])
    },
    {
      perm: true,
      text: '表单',
      isTextBtn: false,
      type: 'warning',
      svgIcon: shallowRef(TrendCharts),
      click: (row) => {
        router.push({ name: 'GCGL010104', query: { formId: row.formId } });
      }
    }
  ],
  pagination: {
    hide: true
  }
});
// 弹框表单配置
const formConfig = reactive<IDialogFormConfig>({
  title: '新增',
  labelWidth: 120,
  dialogWidth: 500,
  group: [
    {
      fields: [
        {
          type: 'select',
          label: '节点位置',
          field: 'type',
          options: [
            { label: '开始', value: '开始' },
            { label: '过程', value: '过程' },
            { label: '结束', value: '结束' }
          ],
          rules: [{ required: true, message: '请选择节点位置' }]
        },
        {
          type: 'input-number',
          label: '节点序号',
          field: 'orderNum',
          rules: [{ required: true, message: '请填写节点序号' }],
          placeholder: '请填写节点序号'
        },
        {
          type: 'input',
          label: '节点名称',
          field: 'name',
          rules: [{ required: true, message: '请填写节点名称' }],
          placeholder: '请填写节点名称'
        },
        {
          type: 'select-tree',
          label: '操作部门',
          field: 'departmentId',
          multiple: true,
          showCheckbox: true,
          rules: [{ required: true, message: '请选择操作部门' }],
          checkStrictly: false,
          options: computed(() => state.departmentTree) as any,
          onChange: async (val: any) => {
            await setUserSelect(val);
          }
        },
        {
          type: 'select',
          multiple: true,
          label: '操作人员',
          field: 'users',
          options: [],
          rules: [{ required: true, message: '请填写操作人员' }],
          placeholder: '请填写操作人员'
        },
        {
          type: 'select',
          label: '表单名称',
          field: 'formId',
          options: computed(()=>state.formData) as any,
          rules: [{ required: true, message: '请选择表单名称' }],
          placeholder: '请选择表单名称'
        },
        {
          type: 'input',
          label: '显示步骤',
          field: 'title',
          rules: [{ required: true, message: '请填写显示步骤' }],
          placeholder: '请填写显示步骤'
        },
        {
          type: 'input-number',
          label: '预计执行天数',
          field: 'executionDay',
          rules: [{ required: true, message: '请填写预计执行天数' }],
          placeholder: '请填写预计执行天数'
        }
      ]
    }
  ],
  submit: (params: any) => {
    SLConfirm('确定提交？', '提示信息').then(() => {
      formConfig.submitting = true;
      params = {
        ...params,
        users: params.users.join(','),
        mainId: TreeData.currentProject?.value,
        departmentId: params.departmentId.join(',')
      };
      editProcessStep(params)
        .then((res) => {
          if (res.data?.code === 200) {
            refForm.value?.closeDialog();
            formConfig.submitting = false;
            $messageSuccess('保存成功');
            refreshData();
          } else {
            $messageError(res.data?.message);
            formConfig.submitting = false;
          }
        })
        .catch((error) => {
          $messageError(error);
          formConfig.submitting = false;
        });
    });
  }
});

const setUserSelect = async (val: any) => {
  const users = formConfig.group[0].fields?.find(
    (field) => field.field === 'users'
  ) as IFormSelect;
  if (val) {
    const userOptions = await userList(val);
    console.log(userOptions);
    users.options = userOptions;
  } else {
    users.options = [];
  }
  formConfig.defaultValue = {
    ...formConfig.defaultValue,
    ...refForm.value?.refForm?.dataForm,
    users: ''
  };
  refForm.value?.refForm?.resetForm();
};

// 分类树
const TreeData = reactive<SLTreeConfig>({
  title: '工程类型',
  data: [],
  currentProject: {},
  isFilterTree: true,
  treeNodeHandleClick: (data) => {
    // 设置当前选中项目信息
    TreeData.currentProject = data;
    useBusinessStore().SET_selectedProject(data);
    refreshData();
  }
});
// 获取部门用户
const userList = async (val: any) => {
  const res = await getAllUserByPidStr({ pid: val.join(',') });
  const users = res.data?.data;
  const userOptions: INormalOption[] = users?.map((user) => {
    return {
      id: removeSlash(user.id.id),
      label: user.firstName,
      value: removeSlash(user.id.id)
    };
  });
  return userOptions;
};
// 附件弹框配置
const handleAddEdit = async (row?: any) => {
  // 表单列表
 
  let defaultValue = {};
  if (row) {
    const departmentIdArr = row.departmentId.split(',');
    await setUserSelect(departmentIdArr);
    defaultValue = {
      ...row,
      departmentId: departmentIdArr,
      users: row.users.split(',')
    };
  }
  formConfig.defaultValue = defaultValue;
  // formConfig.submit = (params: any) => {
  //   SLConfirm('确定提交？', '提示信息').then(() => {
  //     formConfig.submitting = true
  //     params = {
  //       ...params,
  //       users: params.users.join(','),
  //       mainId: TreeData.currentProject?.value,
  //       departmentId: params.departmentId.join(','),
  //       id: row ? row.id : null
  //     }
  //     editProcessStep(params).then(res => {
  //       if (res.data?.code === 200) {
  //         refForm.value?.closeDialog()
  //         formConfig.submitting = false
  //         $messageSuccess('保存成功')
  //         refreshData()
  //       } else {
  //         $messageError(res.data?.message)
  //         formConfig.submitting = false
  //       }
  //     }).catch(error => {
  //       $messageError(error)
  //       formConfig.submitting = false
  //     })
  //   })
  // }
  refForm.value?.openDialog();
};
// 删除数据
const handleDelProcessStep = (ids: string[]) => {
  SLConfirm('确定删除？', '提示信息').then(() => {
    delProcessStep(ids)
      .then(() => {
        $messageSuccess('删除成功');
        refreshData();
      })
      .catch((error) => {
        $messageWarning(error);
      });
  });
};

// 刷新数据
const refreshData = async () => {
  tableConfig.loading = true;
  const queryParams = refSearch.value?.queryParams || {};
  const params = {
    ...queryParams,
    page: 1,
    size: 9999,
    mainId: TreeData.currentProject?.value
  };
  const result = await processStepList(params);
  const data = result.data?.data?.data;
  tableConfig.dataList = data;
  tableConfig.loading = false;
};

onMounted(async () => {
  // 获取部门树
  state.departmentTree = await getDepartmentTree(2);
  const result = await processTypeList({ page: 1, size: 9999 });
  const data = result.data?.data.data;
  TreeData.data = formatTree(data || []);
  TreeData.currentProject = TreeData.data[0];
  await refreshData();

  const res = await formList({ page: 1, size: 9999 });
  state.formData = res.data?.data?.data.map((data) => {
    return { label: data.name, value: data.id };
  });

});
</script>
