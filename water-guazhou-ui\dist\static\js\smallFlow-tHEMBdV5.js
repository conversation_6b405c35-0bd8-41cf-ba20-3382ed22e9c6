import{_ as v}from"./CardTable-rdWOL4_6.js";import{_ as m}from"./Search-NSrhrIa_.js";import{d as _,c as n,r as s,bF as a,s as t,g,n as h,q as u,i as c,al as y,b7 as w,aj as T,C as x}from"./index-r0dFAfgr.js";import"./index-C9hz-UZb.js";const S=[{label:"0时",value:0},{label:"1时",value:1},{label:"2时",value:2},{label:"3时",value:3},{label:"4时",value:4},{label:"5时",value:5},{label:"6时",value:6},{label:"7时",value:7},{label:"8时",value:8},{label:"9时",value:9},{label:"10时",value:10},{label:"11时",value:11},{label:"12时",value:12},{label:"13时",value:13},{label:"14时",value:14},{label:"15时",value:15},{label:"16时",value:16},{label:"17时",value:17},{label:"18时",value:18},{label:"19时",value:19},{label:"20时",value:20},{label:"21时",value:21},{label:"22时",value:22},{label:"23时",value:23}],F={class:"view"},k=_({__name:"smallFlow",setup(C){const r=n(),o=n(),i=s({defaultParams:{filterStart:[0,23],date:[a().format(),a().format()]},filters:[{type:"select",label:"统计类型",field:"va1",width:"140px",options:[{label:"净累计",value:"净累计"}]},{type:"daterange",label:"日期",field:"date"},{label:"时间",type:"range",rangeType:"select",field:"filterStart",options:JSON.parse(JSON.stringify(S)),startPlaceHolder:"0时",endPlaceHolder:"23时",startOptionDisabled:(e,l)=>l&&Number(l)<e.value,endOptionDisabled:(e,l)=>l&&e.value<=Number(l)},{type:"input-number",label:"最小流量",field:"v2"}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",svgIcon:t(y),click:()=>p()},{perm:!0,text:"重置",type:"default",svgIcon:t(w),click:()=>{var e;(e=r.value)==null||e.resetForm()}},{perm:!0,text:"导出",type:"warning",svgIcon:t(T),click:()=>{var e;(e=o.value)==null||e.exportTable()}}]}]}),b=s({loading:!1,dataList:[],columns:[{prop:"differenceRate",label:"日期",formatter:(e,l)=>a(l).format("YYYY-MM-DD")},{prop:"name",label:"起始读数"},{prop:"outletTotalFlow",label:"结束读数",unit:"(m³/h)"},{prop:"inletTotalFlow",label:"用水量",unit:"(m³)",sortable:!0},{prop:"differenceTotalFlow",label:"最大瞬时流量",sortable:!0},{prop:"differenceTotalFlow",label:"最小瞬时流量",sortable:!0},{prop:"differenceRate",label:"统计"}],operations:[],showSummary:!1,operationWidth:"150px",pagination:{hide:!0}}),p=async()=>{};return(e,l)=>{const f=m,d=v;return g(),h("div",F,[u(f,{ref_key:"refSearch",ref:r,config:c(i)},null,8,["config"]),u(d,{ref_key:"refTable",ref:o,class:"card-table",config:c(b)},null,8,["config"])])}}}),Y=x(k,[["__scopeId","data-v-bb25eb6f"]]);export{Y as default};
