import{ap as p}from"./index-r0dFAfgr.js";const d=(o,r)=>{l(o).then(e=>{a(e,r)})},l=o=>new Promise(r=>{const e=new XMLHttpRequest;e.open("GET",o,!0),e.responseType="blob",e.onload=()=>{e.status===200&&r(e.response)},e.send()}),a=(o,r)=>{const e=document.createElement("a"),s=document.querySelector("body");e.href=window.URL.createObjectURL(o),e.download=r,e.style.display="none",s.appendChild(e),e.click(),s.removeChild(e),window.URL.revokeObjectURL(e.href)},c=o=>{let r=JSON.parse(JSON.stringify(o.data)),e=JSON.parse(JSON.stringify(o.titleList));r=r.map(t=>(e.forEach(n=>{n.formatter?(t[n.prop]=n.formatter(t)??"",console.log(t.toString(),t[n.prop])):t[n.prop]===0||t[n.prop]||(t[n.prop]=" ")}),t)),e=e.map(t=>({field:t.prop,displayName:t.label})),p({printable:r,properties:e,type:"json",style:"@page {margin:10mm 10mm 0 10mm;page-size:8px;};",targetStyles:["*"],documentTitle:o.title})};export{d,c as p,a as s};
