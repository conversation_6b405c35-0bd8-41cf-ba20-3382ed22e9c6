package org.thingsboard.server.dao.smartService.call;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.smartService.knowledge.KnowledgeNotice;
import org.thingsboard.server.dao.sql.smartService.call.TellAreaCodeInfoMapper;

import java.util.List;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-10-27
 */
@Slf4j
@Service
@Transactional
public class TellAreaCodeInfoServiceImpl implements TellAreaCodeInfoService {
    @Autowired
    private TellAreaCodeInfoMapper tellAreaCodeInfoMapper;

    @Override
    public PageData getList(String keywords, int page, int size) {

        List<KnowledgeNotice> knowledgeNotices = tellAreaCodeInfoMapper.getList(keywords, page, size);

        int total = tellAreaCodeInfoMapper.getListCount(keywords);

        return new PageData(total, knowledgeNotices);

    }

}
