import { request } from '@/plugins/axios';

// 巡检班组
// 分页条件查询巡检班组信息
export const getInspectionTeamSerch = (params?: {
  page: number | undefined;
  size: number | undefined;
  name?: string;
  type?: string;
}) =>
  request({
    url: `/api/maintainCircuit/team/m`,
    method: 'get',
    params
  });

// 添加/修改巡检班组
export const postInspectionTeam = (params?: {
  id?: string;
  type: string;
  name: string;
  remark: string;
  maintainCircuitTeamCList?: string[];
}) =>
  request({
    url: `/api/maintainCircuit/team/m`,
    method: 'post',
    data: params
  });

// 删除巡检班组
export const deleteInspectionTeam = (ids: string[]) =>
  request({
    url: `/api/maintainCircuit/team/m`,
    method: 'delete',
    data: ids
  });

// 巡检标准
// 分页条件查询巡检标准信息
export const getstandardSerch = (params?: {
  page: number | undefined;
  size: number | undefined;
  keywords?: string;
  deviceTypeId?: string;
}) =>
  request({
    url: `/api/circuit/standard/m`,
    method: 'get',
    params
  });

// 添加/修改巡检标准
export const poststandard = (params?: {
  id?: string;
  serialId: string;
  method: string;
  remark: string;
}) =>
  request({
    url: `/api/circuit/standard/m`,
    method: 'post',
    data: params
  });

// 删除巡检标准
export const deletestandard = (ids: string[]) =>
  request({
    url: `/api/circuit/standard/m`,
    method: 'delete',
    data: ids
  });

// 巡检统计
// 条件查询巡检统计信息
export const getStatisticsSerch = (params?: {
  startTime?: string;
  endTime?: string;
}) =>
  request({
    url: `/api/circuit/task/m/statistics`,
    method: 'get',
    params
  });
