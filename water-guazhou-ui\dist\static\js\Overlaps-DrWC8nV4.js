import{d as T,c as _,r as h,b as y,Q as F,g as N,h as B,F as b,p as n,bh as I,i as c,q as w,_ as M,aq as P,X as S,C as q}from"./index-r0dFAfgr.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import{a as O}from"./LayerHelper-Cn-iiqxI.js";import"./project-DUuzYgGl.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import V from"./RightDrawerMap-D5PhmGFO.js";import{c as A}from"./pipeCheck-BaGB4XFi.js";import G from"./DetailTable-Dc-xAY7v.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./pipe-nogVzCHG.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./ArcView-DpMnCY82.js";import"./ViewHelper-BGCZjxXH.js";import"./fieldconfig-Bk3o1wi7.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./ArcStationWarning-BF9YrSzF.js";/* empty css                         */import"./useWaterPoint-Bv0z6ym6.js";import"./DateFormatter-Bm9a68Ax.js";import"./zhandian-YaGuQZe6.js";import"./useStation-DJgnSZIA.js";import"./DrawerBox-CLde5xC8.js";import"./SideDrawer-CBntChyn.js";import"./PipeDetail-CTBPYFJW.js";import"./Search-NSrhrIa_.js";import"./FormTableColumnFilter-BT7pLXIC.js";import"./QueryHelper-ILO3qZqg.js";import"./config-fy91bijz.js";import"./ArcBRTools-BO92yznB.js";import"./PipeLength.vue_vue_type_script_setup_true_lang-BZfUsvDf.js";import"./arcWidgetButton-0glIxrt7.js";import"./StatisticsHelper-D-s_6AyQ.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./wfsUtils-DXofo3da.js";import"./usePipelineGroup-Ba1pmWz_.js";import"./GroupLayer-DhhN3FyN.js";import"./lazyLayerLoader-DbM9sT1W.js";import"./WFSLayer-1TtJp3nx.js";import"./clientSideDefaults-VQhQaYxh.js";import"./QueryEngineCapabilities-Dk3NJwmm.js";import"./wfsUtils-Du031XaC.js";import"./geojson-MBFu2-HZ.js";import"./xmlUtils-CtUoQO7q.js";import"./ListWindow-WS05QqV0.js";import"./PopLayout-BP55MvL7.js";import"./PoiSearchV2-D7yNeLuv.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./URLHelper-B9aplt5w.js";import"./useLayerList-DmEwJ-ws.js";import"./useScaleBar-Beed-z91.js";const Q={class:"detail-wrapper"},$={class:"left"},E={class:"table-box"},J={class:"right"},K={class:"title"},U={class:"table-box"},X=T({__name:"Overlaps",setup(j){const u=_(),g=_(),o=h({tabs:[],curType:"",layerInfos:[],layerIds:[],loading:!1,curLayerName:""}),d={queryParams:{geometry:void 0,where:"1=1"}},i=h({dataList:[],indexVisible:!0,columns:[{label:"重叠个数",prop:"overlapsfeaturecount"}],pagination:{hide:!0},handleRowClick:t=>{i.currentRow=t,v()}}),v=async()=>{var t,r,a,s;i.currentRow&&(await((a=f.value)==null?void 0:a.refreshDetail(d.view,{layername:i.currentRow.overlapsresult[0].layeralias,layerid:(t=o.layerInfos.find(l=>l.layername===i.currentRow.overlapsresult[0].layeralias))==null?void 0:t.layerid,oids:((r=i.currentRow.overlapsresult[0])==null?void 0:r.result)||[]},{page:1})),(s=g.value)==null||s.toggleCustomDetail(!0))},C=h({group:[{id:"layer",fieldset:{desc:"选择图层"},fields:[{type:"tree",options:[],checkStrictly:!0,showCheckbox:!0,field:"layerid",nodeKey:"value",handleCheckChange:(t,r)=>{r&&u.value&&(u.value.dataForm.layerid=[t.value])}},{type:"btn-group",btns:[{perm:!0,text:()=>o.loading?"正在检查，过程稍长，请耐心等待！":"检查",styles:{width:"100%"},loading:()=>o.loading,click:()=>k()}]}]}],labelPosition:"top",gutter:12,defaultValue:{length:1}}),L=async()=>{var l,p,m;o.layerIds=O(d.view);const t=await S(o.layerIds);o.layerInfos=((p=(l=t.data)==null?void 0:l.result)==null?void 0:p.rows)||[];const r=(m=C.group.find(e=>e.id==="layer"))==null?void 0:m.fields[0],a=o.layerInfos.filter(e=>e.geometrytype==="esriGeometryPoint").map(e=>({label:e.layername,value:e.layerid,data:e})),s=o.layerInfos.filter(e=>e.geometrytype==="esriGeometryPolyline").map(e=>({label:e.layername,value:e.layerid,data:e}));r&&(r.options=[{label:"管点类",value:-1,disabled:!0,children:a},{label:"管线类",value:-2,disabled:!0,children:s}])},f=_(),R=t=>{var r;(r=f.value)==null||r.extentTo(d.view,t.OBJECTID)},k=async()=>{var t,r,a,s,l,p;o.loading=!0,y.info("正在检查，请稍候..."),i.dataList=[],i.currentRow=void 0;try{o.tabs.length=0;const m=((r=(t=u.value)==null?void 0:t.dataForm.layerid)==null?void 0:r.filter(e=>e>=0))||[];if(!m.length)y.warning("请选择一个图层");else{o.curLayerName=((a=o.layerInfos.find(x=>x.layerid===m[0]))==null?void 0:a.layername)||"";const e=await A({layer:o.curLayerName});(s=e.data.result)!=null&&s.length||y.success("没有相关内容"),(l=g.value)==null||l.toggleCustomDetail(!0),(p=f.value)==null||p.clearTable(),i.dataList=e.data.result||[],i.currentRow=i.dataList[0],v()}}catch(m){console.log(m),y.error(m.message)}o.loading=!1},D=t=>{d.view=t,L()};return F(()=>{var t,r,a;(t=d.graphicsLayer)==null||t.removeAll(),(r=d.drawAction)==null||r.destroy(),(a=d.drawer)==null||a.destroy()}),(t,r)=>{const a=M,s=P;return N(),B(V,{ref_key:"refMap",ref:g,title:"重叠检查","detail-max-min":!0,"full-content":!0,onMapLoaded:D,onDetailRefreshed:r[0]||(r[0]=l=>c(o).loading=!1)},{"detail-header":b(()=>[n("span",null,"重叠检查结果"+I(c(o).curLayerName&&" - "+c(o).curLayerName),1)]),"detail-default":b(()=>{var l,p;return[n("div",Q,[n("div",$,[r[1]||(r[1]=n("div",{class:"title"}," 重叠列表 ",-1)),n("div",E,[w(s,{config:c(i)},null,8,["config"])])]),n("div",J,[n("div",K," 重叠要素详情 "+I((l=c(i).currentRow)!=null&&l.id?" - 序号"+((p=c(i).currentRow)==null?void 0:p.id):""),1),n("div",U,[w(G,{ref_key:"refDetailTable",ref:f,onRowClick:R,onRefreshData:v},null,512)])])])]}),default:b(()=>[w(a,{ref_key:"refForm",ref:u,config:c(C)},null,8,["config"])]),_:1},512)}}}),Zt=q(X,[["__scopeId","data-v-f1b2bbfb"]]);export{Zt as default};
