import{d as U,cN as $,r as q,c as J,o as j,ay as H,g as l,n as p,bo as R,i,q as m,p as n,F as T,aB as W,aJ as k,h as K,G as Q,bh as g,ab as X,bt as Y,dz as Z,dA as tt,br as et,C as at}from"./index-r0dFAfgr.js";import{h as ot}from"./chart-wy3NEK2T.js";import{m as it}from"./onemap-CEunQziB.js";import{f as st,d as rt}from"./zhandian-YaGuQZe6.js";import{g as h}from"./echarts-Bhn8T7lM.js";import{a as nt}from"./useDetector-BRcb7GRN.js";import{g as lt}from"./headwaterMonitoring-BgK7jThW.js";const dt={class:"one-map-detail"},ct={class:"row1"},ut={class:"pie-charts"},pt={class:"pie-chart"},mt={class:"pie-chart"},ht={class:"row2"},_t={class:"detail-attrgrou-radio"},ft={class:"detail-right"},vt={class:"list-items overlay-y"},Ct={class:"item-label"},gt={class:"item-content"},yt={class:"chart-box"},Rt=U({__name:"WaterSourceDetail",emits:["refresh","mounted"],setup(xt,{expose:w,emit:z}){const x=z,{proxy:I}=$(),F=nt(),t=q({curRadio:"",radioGroup:[],pieChart1:h(0,{max:100,title:"今日供水量(万m³)"}),pieChart2:h(0,{max:100,title:"昨日供水量(万m³)"}),pieChart3:h(0,{max:3e3,title:"本月供水量(万m³)"}),lineChartOption:null,stationRealTimeData:[],detailLoading:!1}),D=J(),B=async e=>{var s,d,c,v,C;if(!((e==null?void 0:e.stationId)!==void 0&&e.stationId===((s=t.curRow)==null?void 0:s.stationId))){x("refresh",{title:e.name}),t.detailLoading=!0;try{if(e.fromAllStation){const u=await lt();t.curRow=(d=u.data)==null?void 0:d.data.find(o=>o.stationId===e.stationId)}else t.curRow=e;const r=u=>{const o=X(u??0);return{value:+o.value.toFixed(2),unit:o.unit}},a=r((c=t.curRow)==null?void 0:c.todayWaterSupply),_=r((v=t.curRow)==null?void 0:v.yesterdayWaterSupply),A=r((C=t.curRow)==null?void 0:C.monthWaterSupply);t.pieChart1=h(a.value,{max:100,title:"今日供水量("+(a.unit||"")+"m³)"}),t.pieChart2=h(_.value,{max:100,title:"昨日供水量("+(a.unit||"")+"m³)"}),t.pieChart3=h(A.value,{max:1e3,title:"本月供水量("+(a.unit||"")+"m³)"}),b();const O=it({stationId:e.stationId}).then(u=>{var L,G,V,N;const o=(L=u.data)==null?void 0:L.data,M=((G=o==null?void 0:o.pressure)==null?void 0:G.map(y=>{var f;return(f=y.value)==null?void 0:f.toFixed(2)}))||[],P=((V=o==null?void 0:o.Instantaneous_flow)==null?void 0:V.map(y=>{var f;return(f=y.value)==null?void 0:f.toFixed(2)}))||[];t.lineChartOption=ot({line1:{data:P,unit:"m³/h",name:"瞬时流量"},line2:{data:M,unit:"MPa",name:"压力"}}),(N=I.$refs.refChart4)==null||N.resize()}),E=st({stationId:e.stationId}).then(u=>{t.radioGroup=u.data||[],t.curRadio=t.radioGroup[0],S()});Promise.all([O,E]).finally(()=>{t.detailLoading=!1})}catch(r){console.log(r),t.detailLoading=!1}}},S=async()=>{var e;if(t.curRadio){if(((e=t.curRow)==null?void 0:e.stationId)===void 0){t.stationRealTimeData=[];return}const s=await rt(t.curRow.stationId,t.curRadio);t.stationRealTimeData=s.data||[]}};w({refreshDetail:B});const b=()=>{Array.from({length:3}).map((e,s)=>{var d;(d=I.$refs["refChart"+(s+1)])==null||d.resize()})};return j(()=>{x("mounted"),F.listenToMush(D.value,b)}),(e,s)=>{const d=Y,c=H("VChart"),v=Z,C=tt,r=et;return l(),p("div",dt,[R((l(),p("div",ct,[m(d,{size:"default",title:"水源监测",type:"simple",class:"row-title"}),n("div",ut,[n("div",{ref_key:"refChartDiv",ref:D,class:"pie-chart"},[m(c,{ref:"refChart1",option:i(t).pieChart1},null,8,["option"])],512),n("div",pt,[m(c,{ref:"refChart2",option:i(t).pieChart2},null,8,["option"])]),n("div",mt,[m(c,{ref:"refChart3",option:i(t).pieChart3},null,8,["option"])])])])),[[r,i(t).detailLoading]]),n("div",ht,[n("div",_t,[m(C,{modelValue:i(t).curRadio,"onUpdate:modelValue":s[0]||(s[0]=a=>i(t).curRadio=a),onChange:S},{default:T(()=>[(l(!0),p(W,null,k(i(t).radioGroup,(a,_)=>(l(),K(v,{key:_,label:a},{default:T(()=>[Q(g(a),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),n("div",ft,[R((l(),p("div",vt,[(l(!0),p(W,null,k(i(t).stationRealTimeData,(a,_)=>(l(),p("div",{key:_,class:"list-item"},[n("div",Ct,g(a.propertyName),1),n("div",gt,g(a.value||"--")+" "+g(a.unit),1)]))),128))])),[[r,i(t).detailLoading]]),R((l(),p("div",yt,[m(c,{ref:"refChart4",option:i(t).lineChartOption},null,8,["option"])])),[[r,i(t).detailLoading]])])])])}}}),Nt=at(Rt,[["__scopeId","data-v-4ec2b07b"]]);export{Nt as default};
