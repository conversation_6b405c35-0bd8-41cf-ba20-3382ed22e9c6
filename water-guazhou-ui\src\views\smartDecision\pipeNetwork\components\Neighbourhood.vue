<template>
  <div></div>
</template>
<script lang="ts" setup>
import GraphicsLayer from '@arcgis/core/layers/GraphicsLayer';
import Graphic from '@arcgis/core/Graphic';
import Polygon from '@arcgis/core/geometry/Polygon';
import SimpleFillSymbol from '@arcgis/core/symbols/SimpleFillSymbol';
import Color from '@arcgis/core/Color';
import TextSymbol from '@arcgis/core/symbols/TextSymbol';
import { hexToRgba } from '@/utils/GlobalHelper';
import { bindViewClick, setMapCursor } from '@/utils/MapHelper';
import { GetPartitionTree } from '@/api/mapservice/dma';
import { GetPartitionDetail } from '@/api/mapservice/dma';
// import { setMapCursor } from '@/utils/MapHelper'
// import { useSketch } from '@/hooks/arcgis'

const emit = defineEmits(['click']);
const view = inject('view') as __esri.MapView;
const layer = new GraphicsLayer({
  id: 'neighbourhood',
  title: '小区边界'
});
view.map.add(layer);

// const { initSketch, destroySketch } = useSketch()

// const resolveDrawEnd = (result: ISketchHandlerParameter) => {
//   if (result.state === 'complete') {
//     console.log(result.graphics[0]?.geometry)
//     // layer.removeAll()
//     sketch.create('polygon')
//   } else if (result.state === 'cancel') {
//     sketch.create('polygon')
//   }
// }
// const sketch = initSketch(view, layer, {
//   updateCallBack: resolveDrawEnd,
//   createCallBack: resolveDrawEnd
// })
const addNeighbourHood = (
  id: string,
  title: string,
  rings: number[][][],
  hex: string
) => {
  layer.addMany([
    new Graphic({
      geometry: new Polygon({ rings, spatialReference: view.spatialReference }),
      symbol: new SimpleFillSymbol({
        color: new Color(hexToRgba(hex, 0.4)),
        outline: {
          width: 1,
          color: new Color(hex)
        }
      }),
      attributes: {
        id,
        title
      }
    }),
    new Graphic({
      geometry: new Polygon({ rings, spatialReference: view.spatialReference }),
      symbol: new TextSymbol({
        text: title,
        font: {
          size: 12
        },
        haloSize: 1,
        haloColor: new Color('#ffffff'),
        color: new Color(hex)
      }),
      attributes: {
        id,
        title
      }
    })
  ]);
};
const neighbours = reactive<
  { id: string; title: string; color: string; boundary: number[][][] }[]
>([]);
const initArea = () => {
  layer.removeAll();
  neighbours.map((item) => {
    addNeighbourHood(item.id, item.title, item.boundary, item.color);
  });
};
view.when().then(() => {
  initArea();
});
onMounted(() => {
  // sketch.create('polygon')
  // setMapCursor('crosshair')
  emit('click', { id: '1', title: '文庭雅苑' });
  bindViewClick(view, (res) => {
    if (res.results?.[0]?.type === 'graphic') {
      const attributes = res.results?.[0]?.graphic.attributes;
      emit('click', attributes);
    }
  });
  view.on('pointer-move', (e) => {
    view.hitTest(e).then((response) => {
      if (response.results?.[0]?.type === 'graphic') {
        setMapCursor('pointer');
      } else {
        setMapCursor('');
      }
    });
  });
});

const getLeafNodes = (obj) => {
  let result: any = [];

  function traverse(currentObj) {
    currentObj.map((item) => {
      if (item.children && item.children.length > 0) {
        traverse(item.children);
      } else {
        result.push(item);
      }
    });
  }

  traverse(obj);
  return result;
};

const init = () => {
  GetPartitionTree().then((res) => {
    const data = getLeafNodes(res.data);
    const ax: any[] = [];
    data.forEach((item) => {
      ax.push(GetPartitionDetail(item.id));
    });
    Promise.all(ax).then((response) => {
      response.forEach((val) => {
        neighbours.push({
          id: val.data.id,
          title: val.data.name,
          color: '#eb7953',
          boundary: JSON.parse(val.data.geom.slice(1, -1))
        });
      });
      initArea();
    });
  });
};

onBeforeMount(() => {
  init();
});

onBeforeUnmount(() => {
  // destroySketch()
  layer.removeAll();
  view.map.remove(layer);
});
</script>
<style lang="scss" scoped></style>
