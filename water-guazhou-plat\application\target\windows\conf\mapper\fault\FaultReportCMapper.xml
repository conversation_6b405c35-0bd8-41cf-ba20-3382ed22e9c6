<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.fault.FaultReportCMapper">

    <select id="getList" resultType="org.thingsboard.server.dao.model.sql.fault.FaultReportC">

        select a.*, b.model as model, b.serial_id as serialId, b.name, b.type_id as typeId, area.name as installAddressName, settle.address detailInstallAddressName
        from tb_device_fault_report_c a
                 left join device_storage_journal journal on a.device_label_code = journal.device_label_code
                 left join m_device b on journal.serial_id = b.serial_id
                 left join device_settle_journal settle on a.device_label_code = settle.device_label_code
                 left join tb_area area on settle.install_address_id = area.id
        where a.main_id = #{mainId}
        order by a.create_time desc

    </select>

    <select id="countByLabelCode" resultType="int">
        select count(*) from tb_device_fault_report_c where device_label_code = #{deviceLabelCode}
    </select>

    <select id="selectFirstByDeviceLabelCode" resultType="org.thingsboard.server.dao.model.sql.fault.FaultReportC">
        select * from tb_device_fault_report_c where device_label_code = #{deviceLabelCode} order by create_time desc limit 1 offset 0
    </select>
    
    <select id="getGradeCountByDeviceLabelCode" resultType="java.util.Map">
        select d.level, count(d.level)
        from tb_device_fault_report_c a
        left join device_storage_journal b on a.device_label_code = b.device_label_code
        left join tb_device_fault_report c on a.main_id = c.id
        left join work_order d on c.work_order_id = d.id

        where a.device_label_code = #{deviceLabelCode}
        group by d.level
    </select>

    <select id="getNowYearRepairByDeviceLabelCode" resultType="java.util.Map">
        select to_char(a.create_time::DATE, 'YYYY-MM') as month, count(a.id)
        from tb_device_fault_report_c a
                 left join device_storage_journal b on a.device_label_code = b.device_label_code
                 left join tb_device_fault_report c on a.main_id = c.id
                 left join work_order d on c.work_order_id = d.id

        where a.device_label_code = #{deviceLabelCode} and a.create_time &gt;= #{nowYear}
        group by to_char(a.create_time::DATE, 'YYYY-MM')
    </select>

    <select id="getRepairList" resultType="java.util.Map">
        select c.title, d.type, c.start_time as "startTime", d.id as "workOrderId", d.title as "workOrderName"
        from tb_device_fault_report_c a
        left join device_storage_journal b on a.device_label_code = b.device_label_code
        left join tb_device_fault_report c on a.main_id = c.id
        left join work_order d on c.work_order_id = d.id

        where a.device_label_code = #{deviceLabelCode}
        offset (#{page} - 1) * #{size} limit #{size}
    </select>

    <select id="getRepairListCount" resultType="int">
        select count(*)
        from tb_device_fault_report_c a
        left join device_storage_journal b on a.device_label_code = b.device_label_code
        left join tb_device_fault_report c on a.main_id = c.id
        left join work_order d on c.work_order_id = d.id

        where a.device_label_code = #{deviceLabelCode}
    </select>

</mapper>