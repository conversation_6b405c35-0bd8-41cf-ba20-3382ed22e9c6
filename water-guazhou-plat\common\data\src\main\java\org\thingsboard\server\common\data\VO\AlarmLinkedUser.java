package org.thingsboard.server.common.data.VO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.id.UserId;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2020/4/24 16:59
 */
@Data
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
public class AlarmLinkedUser implements Serializable {

    private UserId userId;
    private String userName;
    private boolean sendSms;
    private boolean sendEmail;
    private String phone;
    private String email;
    private TenantId tenantId;
    private String extraUserId;
}



