package org.thingsboard.server.dao.util.imodel.query.smartProduction.circuit;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartProduction.circuit.CircuitPlan;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;

import java.util.Date;

@Getter
@Setter
public class CircuitPlanPageRequest extends AdvancedPageableQueryEntity<CircuitPlan, CircuitPlanPageRequest> {
    // 配置类型，用于数据隔离。三种类型：水源、水厂、二供泵房。
    private String type;

    // 计划类别。周期性任务、固定日期任务
    private String planType;

    // 计划名称
    private String name;

    // 巡检模板ID
    private String templateId;

    // 执行巡检人员
    private String executionUserId;

    // 执行巡检人员所在部门
    private String executionUserDepartmentId;

    // 巡检成果审核人员
    private String auditUserId;

    // 执行天数
    private Integer executionDays;

    // 间隔天数
    private Integer intervalDays;

    // 执行次数
    private Integer executionNum;

    // 周期开始时间。当计划类别为：周期性任务。该字段有值。
    private String startDateFrom;

    // 周期开始时间。当计划类别为：周期性任务。该字段有值。
    private String startDateTo;

    // 周期结束时间。当计划类别为：周期性任务。该字段有值。
    private String endDateFrom;

    // 周期结束时间。当计划类别为：周期性任务。该字段有值。
    private String endDateTo;

    // 要巡检的站点列表，多个用逗号分隔
    private String stationIds;

    // 创建人
    private String creator;

    public Date getStartDateFrom() {
        return toDate(startDateFrom);
    }

    public Date getStartDateTo() {
        return toDate(startDateTo);
    }

    public Date getEndDateFrom() {
        return toDate(endDateFrom);
    }

    public Date getEndDateTo() {
        return toDate(endDateTo);
    }
}
