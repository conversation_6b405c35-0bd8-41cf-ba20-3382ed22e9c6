package org.thingsboard.server.dao.model.sql;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.thingsboard.server.dao.model.ModelConstants;

import javax.persistence.*;

/**
 * <AUTHOR>
 * @date 2020/3/27 13:37
 */
@Data
@Entity
@Table(name = ModelConstants.CAPTCHA_CAPTCHA)
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
public class CaptchaEntity {


    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    /**
     * 验证码
     */
    @Column(name = ModelConstants.CAPTCHA_CAPTCHA)
    private String captcha;

    /**
     * 电话
     */
    @Column(name = ModelConstants.PHONE_PROPERTY)
    private String phone;

    /**
     * 创建时间
     */
    @Column(name = ModelConstants.CREATE_TIME)
    private long createTime;


    /**
     * 是否失效
     */
    @Column(name = ModelConstants.CAPTCHA_INVALID)
    private String invalid;

    /**
     * 企业ID
     */
    @Column(name = ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;
    /**
     * 用户ID
     */
    @Column(name = ModelConstants.USER_ID_PROPERTY)
    private String userId;
}
