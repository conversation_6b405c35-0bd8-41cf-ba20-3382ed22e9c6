import{d as M,b3 as R,c as f,r as B,am as E,a8 as V,ch as m,o as y,g as a,n as r,q as _,F as n,h as c,aB as v,aJ as A,an as h,i as b,p as D,cn as N,aw as S,j as q,cV as F,bV as I,cE as L,C as j}from"./index-r0dFAfgr.js";import{_ as z}from"./SidebarItem.vue_vue_type_style_index_0_lang-DsbAQbjU.js";const J=M({__name:"index",setup($){const s=R(),w=f(),e=f(!1),k=()=>{e.value=!e.value},t=B({activePath:s.currentRoute.value.fullPath,showMenu:!1});E(()=>s.currentRoute.value.fullPath,()=>{t.activePath!==s.currentRoute.value.fullPath&&(t.activePath=s.currentRoute.value.fullPath)});const u=V(()=>{var l;return[(l=m().routers.filter(i=>i.hidden!==!0)[0])==null?void 0:l.path]});return y(()=>{t.showMenu=!0}),(l,i)=>{const C=F,P=I,g=L;return a(),r("div",{class:S(["sidebar-container",[{dark:b(q)().isDark},{collapsed:e.value}]])},[_(P,{class:"side-menu-scrollbar","wrap-class":"scrollbar-wrapper"},{default:n(()=>[t.showMenu?(a(),c(C,{key:u.value[0],ref_key:"refMenu",ref:w,mode:"vertical",router:"","show-timeout":200,"default-active":t.activePath,"unique-opened":!0,"default-openeds":u.value,collapse:e.value},{default:n(()=>[(a(!0),r(v,null,A(b(m)().routers,(o,x)=>{var d,p;return a(),r(v,null,[(d=o.children)!=null&&d.length?(a(),c(z,{key:x,item:o,"is-nest":!1,icon:(p=o.meta)==null?void 0:p.icon,collapsed:e.value},null,8,["item","icon","collapsed"])):h("",!0)],64)}),256))]),_:1},8,["default-active","default-openeds","collapse"])):h("",!0)]),_:1}),D("div",{class:"sidebar-toggle-bottom",onClick:k},[_(g,null,{default:n(()=>[(a(),c(N(e.value?"ArrowRight":"ArrowLeft")))]),_:1})])],2)}}}),K=j(J,[["__scopeId","data-v-8c2dbebb"]]);export{K as default};
