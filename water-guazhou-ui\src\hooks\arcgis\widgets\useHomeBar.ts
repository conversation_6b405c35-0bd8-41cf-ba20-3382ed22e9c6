import Home from '@arcgis/core/widgets/Home.js'

export const useHomeBar = () => {
  let home: __esri.Home | undefined
  const init = (
    view: __esri.MapView,
    widgetPosition?: string,
    options?: {
      targetGeometry?: __esri.Geometry
      scale?: number
    }
  ) => {
    home = new Home({
      view,
      viewpoint: {
        scale: options?.scale,
        targetGeometry: options?.targetGeometry || view.extent?.clone()
      }
    })
    home && view.ui?.add(home, widgetPosition || 'bottom-right')
    return home
  }
  const destroy = () => {
    home?.destroy()
  }
  onBeforeUnmount(() => {
    destroy()
  })
  return {
    init
  }
}

export default useHomeBar
