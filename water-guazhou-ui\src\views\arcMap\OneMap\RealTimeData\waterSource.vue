<!-- gis水源 -->
<template>
  <div class="onemap-panel-wrapper">
    <Form ref="refForm" :config="FormConfig"> </Form>
    <div class="table-box">
      <FormTable :config="TableConfig"></FormTable>
    </div>
  </div>
</template>
<script lang="ts" setup>
import Point from '@arcgis/core/geometry/Point.js';
import { ring } from '../../components/components/chart';

import { transNumberUnit } from '@/utils/GlobalHelper';
import { PopImage } from '@/views/arcMap/components';
import { useAppStore } from '@/store';
import { getWaterSupplyInfo } from '@/api/headwatersManage/headwaterMonitoring';
import { getStationImageUrl } from '@/utils/URLHelper';

const emit = defineEmits(['highlightMark', 'addMarks']);
const props = defineProps<{
  view?: __esri.MapView;
  menu: IMenuItem;
}>();
const refForm = ref<IFormIns>();
const FormConfig = reactive<IFormConfig>({
  group: [
    {
      id: 'chart',
      fieldset: {
        desc: '今日供水量各水源占比图',
        type: 'underline',
        style: {
          marginTop: 0
        }
      },
      fields: [
        {
          type: 'vchart',
          option: ring(),
          style: {
            height: '150px'
          }
        }
      ]
    },
    {
      fields: [
        {
          type: 'input',
          field: 'name',
          appendBtns: [
            {
              perm: true,
              text: '刷新',
              click: () => refreshData()
            }
          ],
          onChange: () => refreshData()
        }
      ]
    }
  ],
  labelPosition: 'top',
  gutter: 12
});
const TableConfig = reactive<ITable>({
  indexVisible: true,
  dataList: [],
  pagination: {
    hide: true,
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page;
      TableConfig.pagination.limit = size;
    },
    layout: 'total,sizes, jumper'
  },
  handleRowClick: (row) => handleLocate(row),
  columns: [
    {
      minWidth: 120,
      label: '名称',
      prop: 'name',
      sortable: true
    },
    {
      minWidth: 150,
      label: '今日供水量(m³)',
      prop: 'todayWaterSupply',
      sortable: true
    },
    {
      minWidth: 150,
      label: '更新时间',
      prop: 'lastTime',
      sortable: true
    }
  ]
});

const TableConfig_Detail = reactive<ITable>({
  dataList: [],
  columns: [],
  pagination: {
    refreshData: ({ page, size }) => {
      TableConfig_Detail.pagination.page = page;
      TableConfig_Detail.pagination.limit = size;
      refreshData();
    }
  }
});
const refreshData = async () => {
  TableConfig.loading = true;
  try {
    const res = await getWaterSupplyInfo({
      name: refForm.value?.dataForm.name
    });
    TableConfig.dataList = res.data?.data || [];
    const field = FormConfig.group[0].fields[0] as IFormVChart;
    const total: number = res.data?.data?.reduce((prev, curr: any) => {
      return prev + Number(curr.todayWaterSupply || '0');
    }, 0);
    const windows: IArcPopConfig[] = [];
    const data =
      res.data?.data?.map((item) => {
        const transValue = transNumberUnit(item.todayWaterSupply || 0);
        const location = item.location?.split(',');
        if (location?.length === 2) {
          const point = new Point({
            longitude: location[0],
            latitude: location[1],
            spatialReference: props.view?.spatialReference
          });
          windows.push({
            visible: false,
            id: item.stationId,
            x: point.x,
            y: point.y,
            offsetY: -40,
            title: item.name,
            customComponent: shallowRef(PopImage),
            customConfig: {
              info: {
                type: 'attrs',
                imageUrl: item.imgs,
                stationId: item.stationId
              }
            },
            attributes: {
              path: props.menu.path,
              id: item.stationId,
              row: item
            },
            symbolConfig: {
              url: getStationImageUrl('水源地.png')
            }
          });
        }

        return {
          name: item.name,
          // nameAlias?: string | undefined;
          value: item.todayWaterSupply,
          valueAlias: transValue.value.toFixed(2) + transValue.unit,
          scale:
            ((Number(item.todayWaterSupply) / total) * 100).toFixed(2) + '%'
        };
      }) || [];
    field && (field.option = ring(data, 'm³'));
    emit('addMarks', {
      windows
    });
  } catch (error) {
    console.dir(error);
  }
  TableConfig.loading = false;
};
const handleLocate = async (row?: any) => {
  emit('highlightMark', props.menu, row?.stationId);
};
watch(
  () => useAppStore().isDark,
  () => {
    refreshData();
  }
);
onMounted(() => {
  refreshData();
});
</script>

<style lang="scss" scoped>
.onemap-panel-wrapper {
  min-height: 610px;
  height: 100%;
}
.chart-box {
  width: 100%;
  height: 150px;
}
.table-box {
  width: 100%;
  height: calc(100% - 265px);
}
</style>
