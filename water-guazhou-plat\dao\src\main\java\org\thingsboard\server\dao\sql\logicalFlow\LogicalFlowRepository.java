package org.thingsboard.server.dao.sql.logicalFlow;

import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.thingsboard.server.dao.model.sql.LogicalFlow;

import java.util.List;

public interface LogicalFlowRepository extends JpaRepository<LogicalFlow, String> {
    List<LogicalFlow> findByType(String type, Sort orders);

    @Query("SELECT lf FROM LogicalFlow lf, ProjectRelationEntity pr " +
            "WHERE pr.entityType = 'LOGICAL_FLOW' AND lf.id = pr.entityId AND lf.type = ?1 AND pr.projectId = ?2 AND lf.parentId = '0' " +
            "ORDER BY lf.createTime DESC")
    List<LogicalFlow> findByTypeAndProjectId(String type, String projectId);

    @Query("SELECT lf FROM LogicalFlow lf, ProjectRelationEntity pr " +
            "WHERE pr.entityType = 'LOGICAL_FLOW' AND lf.id = pr.entityId AND lf.type = ?1 AND pr.projectId = ?2 " +
            "ORDER BY lf.createTime DESC")
    List<LogicalFlow> findAllByTypeAndProjectId(String type, String projectId);

    @Query("SELECT lf FROM LogicalFlow lf, ProjectRelationEntity pr " +
            "WHERE pr.entityType = 'LOGICAL_FLOW' AND lf.id = pr.entityId AND lf.type = ?1 AND pr.projectId = ?2 " +
            "ORDER BY lf.createTime DESC")
    List<LogicalFlow> findByTypeAndProjectIdAndAll(String type, String projectId);

    List<LogicalFlow> findByTypeAndGatewayIdAndParentIdOrderByCreateTimeDesc(String type, String gatewayId, String parentId);

    List<LogicalFlow> findByTypeAndGatewayIdOrderByCreateTimeDesc(String type, String gatewayId);

    List<LogicalFlow> findByParentIdOrderByCreateTime(String parentId);

    List<LogicalFlow> findByTypeAndParentId(String type, String parentId, Sort orders);

    List<LogicalFlow> findByTenantIdAndType(String tenantId, String cloud);

    void deleteByParentId(String id);
}
