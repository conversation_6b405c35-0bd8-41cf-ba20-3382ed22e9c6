package org.thingsboard.server.controller.smartOperation.construction.project;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.sql.smartOperation.ConstructionWorkflow;
import org.thingsboard.server.dao.model.sql.smartOperation.device.SoDeviceItem;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemScope;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralType;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoProject;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.*;
import org.thingsboard.server.dao.util.imodel.response.ExcelFileInfo;
import org.thingsboard.server.dao.construction.project.SoProjectService;
import org.thingsboard.server.dao.util.reflection.ExceptionUtils;
import org.thingsboard.server.utils.imodel.annotations.IStarController2;

import java.util.List;

@IStarController2
@RequestMapping("/api/so/project")
public class SoProjectController extends BaseController {
    @Autowired
    private SoProjectService service;

    @GetMapping("/codeGen")
    public String code() throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return service.generateCode(tenantId);
    }

    @GetMapping("/completionInfo/{projectCode}")
    public List<ConstructionWorkflow> completionInfo(@PathVariable String projectCode) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return service.completionInfo(projectCode, tenantId);
    }

    @GetMapping
    public IPage<SoProject> findAllConditional(SoProjectPageRequest request) {
        return service.findAllConditional(request);
    }

    @GetMapping("/export/excel")
    public ExcelFileInfo exportExcel(SoProjectPageRequest request) {
        return ExcelFileInfo.of("项目立项列表", findAllConditional(request).getRecords())
                .nextTitle("code", "项目编号")
                .nextTitle("name", "项目名称")
                .nextTitle("typeName", "项目类别")
                .nextTitle("estimate", "项目概算(万元)")
                .nextTitle("startTimeName", "启动时间")
                .nextTitle("expectEndTimeName", "预计结束时间")
                .nextTitle("principal", "项目负责人");
    }

    @PostMapping
    public SoProject save(@RequestBody SoProjectSaveRequest req) {
        return service.save(req);
    }

    // @PatchMapping("/{id}")
    public boolean edit(@RequestBody SoProjectSaveRequest req, @PathVariable String id) {
        return service.update(req.unwrap(id));
    }

    @DeleteMapping("/{id}")
    public boolean delete(@PathVariable String id) {
        if (!service.canBeDelete(id)) {
            ExceptionUtils.silentThrow("项目已有关联项，无法删除。");
        }
        return service.delete(id);
    }


    //region 类型配置
    @GetMapping("/type")
    public IPage<SoGeneralType> getTypes(SoGeneralTypePageRequest request) {
        return service.getTypes(request);
    }

    @PostMapping("/type")
    public SoGeneralType saveType(@RequestBody SoGeneralTypeSaveRequest request) {
        return service.saveType(request);
    }
    //endregion




    //region 设备项管理
    @GetMapping("/{projectCode}/device")
    public IPage<SoDeviceItem> getDevices(@PathVariable String projectCode, SoDeviceItemPageRequest request) {
        request.setScope(SoGeneralSystemScope.SO_PROJECT);
        request.setIdentifier(projectCode);
        request.withoutCode();
        return service.getDevices(request);
    }

    @PostMapping("/{projectCode}/device")
    public List<SoDeviceItem> saveDevice(@PathVariable String projectCode, @RequestBody List<SoDeviceItemSaveRequest> request) {
        for (SoDeviceItemSaveRequest req : request) {
            req.setIdentifier(projectCode);
        }
        return service.saveDevice(request);
    }
    //endregion
}