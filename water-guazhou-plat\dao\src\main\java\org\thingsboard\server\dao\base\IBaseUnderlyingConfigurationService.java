package org.thingsboard.server.dao.base;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.base.BaseUnderlyingConfiguration;
import org.thingsboard.server.dao.util.imodel.query.base.BaseUnderlyingConfigurationPageRequest;

import java.util.List;

/**
 * 平台管理-基础配置Service接口
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
public interface IBaseUnderlyingConfigurationService {
    /**
     * 查询平台管理-基础配置
     *
     * @param id 平台管理-基础配置主键
     * @return 平台管理-基础配置
     */
    public BaseUnderlyingConfiguration selectBaseUnderlyingConfigurationById(String id);

    /**
     * 查询平台管理-基础配置列表
     *
     * @param baseUnderlyingConfiguration 平台管理-基础配置
     * @return 平台管理-基础配置集合
     */
    public IPage<BaseUnderlyingConfiguration> selectBaseUnderlyingConfigurationList(BaseUnderlyingConfigurationPageRequest baseUnderlyingConfiguration);

    /**
     * 新增平台管理-基础配置
     *
     * @param baseUnderlyingConfiguration 平台管理-基础配置
     * @return 结果
     */
    public int insertBaseUnderlyingConfiguration(BaseUnderlyingConfiguration baseUnderlyingConfiguration);

    /**
     * 修改平台管理-基础配置
     *
     * @param baseUnderlyingConfiguration 平台管理-基础配置
     * @return 结果
     */
    public int updateBaseUnderlyingConfiguration(BaseUnderlyingConfiguration baseUnderlyingConfiguration);

    /**
     * 批量删除平台管理-基础配置
     *
     * @param ids 需要删除的平台管理-基础配置主键集合
     * @return 结果
     */
    public int deleteBaseUnderlyingConfigurationByIds(List<String> ids);

    /**
     * 删除平台管理-基础配置信息
     *
     * @param id 平台管理-基础配置主键
     * @return 结果
     */
    public int deleteBaseUnderlyingConfigurationById(String id);
}
