package org.thingsboard.server.controller.base;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.dao.alarm.AlarmService;
import org.thingsboard.server.dao.alarm.AlarmV2Service;
import org.thingsboard.server.dao.project.ProjectRelationService;
import org.thingsboard.server.dao.project.ProjectService;
import org.thingsboard.server.dao.sql.device.DeviceRepository;
import org.thingsboard.server.dao.station.StationService;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

@RestController
@RequestMapping("api/alarm/v2")
public class AlarmV2Controller extends BaseController {

    @Autowired
    private DeviceRepository deviceRepository;
    @Autowired
    private ProjectRelationService projectRelationService;
    @Autowired
    private ProjectService projectService;
    @Autowired
    private StationService stationService;
    @Autowired
    protected AlarmV2Service alarmV2Service;

    @GetMapping("list")
    public IstarResponse list(@RequestParam int page, @RequestParam int size,
                              @RequestParam(required = false, defaultValue = "") String stationType,
                              @RequestParam(required = false, defaultValue = "") String projectId,
                              @RequestParam(required = false) Long start,
                              @RequestParam(required = false) Long end,
                              @RequestParam(required = false) String status,
                              @RequestParam(required = false) String level) throws ThingsboardException {
        try {
            return IstarResponse.ok(alarmV2Service.findList(stationType, projectId, start, end, status, level, page, size, getTenantId()));
        } catch (Exception e) {
            return IstarResponse.error(e.getMessage());
        }
    }

    @GetMapping("all")
    public IstarResponse list(@RequestParam(required = false, defaultValue = "") String stationType,
                              @RequestParam(required = false, defaultValue = "") String projectId,
                              @RequestParam(required = false) Long start,
                              @RequestParam(required = false) Long end,
                              @RequestParam(required = false) String status,
                              @RequestParam(required = false) String level) throws ThingsboardException {
        try {
            return IstarResponse.ok(alarmV2Service.findList(stationType, projectId, start, end, status, level, getTenantId()));
        } catch (Exception e) {
            return IstarResponse.error(e.getMessage());
        }
    }

    @GetMapping("trendByType")
    public IstarResponse trendByType(@RequestParam(required = false, defaultValue = "") String stationType,
                                     @RequestParam(required = false, defaultValue = "") String projectId,
                                     @RequestParam Long start, @RequestParam Long end,
                                     @RequestParam(required = false) String status,
                                     @RequestParam(required = false) String level) {
        try {
            return IstarResponse.ok(alarmV2Service.trendByType(stationType, projectId, start, end, status, level, getTenantId()));
        } catch (Exception e) {
            return IstarResponse.error(e.getMessage());
        }
    }

}
