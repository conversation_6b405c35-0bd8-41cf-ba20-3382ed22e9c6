package org.thingsboard.server.dao.shuiwu.assets;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.shuiwu.assets.AssetsAccountComponentEntity;
import org.thingsboard.server.dao.sql.shuiwu.assets.AssetsAccountComponentRepository;

import java.util.List;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2021-10-20
 */
@Service
@Slf4j
public class AssetsAccountComponentServiceImpl implements AssetsAccountComponentService {
    @Autowired
    private AssetsAccountComponentRepository assetsAccountComponentRepository;

    @Override
    public void save(AssetsAccountComponentEntity assetsAccountComponentEntity) {
        assetsAccountComponentEntity.setCreateTime(System.currentTimeMillis());

        assetsAccountComponentRepository.save(assetsAccountComponentEntity);
    }

    @Override
    public AssetsAccountComponentEntity findOne(String sparePartId) {
        return assetsAccountComponentRepository.findOne(sparePartId);
    }

    @Override
    public List<AssetsAccountComponentEntity> findListByPid(String id) {
        return assetsAccountComponentRepository.findAllByPidOrderByCreateTimeDesc(id);
    }

    @Override
    public void deleteByPid(String id) {
        assetsAccountComponentRepository.deleteAllByPid(id);
    }
}
