package org.thingsboard.server.dao.model.DTO;

import lombok.Data;

/**
 * 工单区域-业务主题
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-12-21
 */
@Data
public class SeatsCallCountDTO {

    private String seatsName;

    private Integer in;

    private Integer duration;

    private Integer miss;

    private String missRate;


    public SeatsCallCountDTO() {
    }

    public SeatsCallCountDTO(String seatsName) {
        this.seatsName = seatsName;
        this.in = 0;
        this.duration = 0;
        this.miss = 0;
        this.missRate = "0%";
    }
}
