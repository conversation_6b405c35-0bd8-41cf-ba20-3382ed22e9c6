import * as echarts from 'echarts'

export function lineOption(dateX?: any, data1?: any, data2?: any, left?:number, right?:number) {
  const option = {
    color: ['#318DFF', '#FFB800', '#FC2B2B'],
    grid: {
      left: left || 80,
      right: right || 80,
      top: 60,
      bottom: 40
    },
    legend: {
      width: '500',
      type: 'scroll',
      textStyle: {
        fontSize: 12
      }
    },
    tooltip: { trigger: 'axis' },
    xAxis: {
      type: 'category',
      data: dateX
    },
    yAxis: [{
      position: 'left',
      type: 'value',
      name: '压力(Mpa)',
      splitNumber: 5,
      axisLine: {
        show: true,
        lineStyle: {
          types: 'solid'
        }
      },
      axisLabel: {
        show: true,
        textStyle: {
          color: '#656b84' // 更改坐标轴文字颜色
          // fontSize: 14 //更改坐标轴文字大小
        }
      },
      splitLine: {
        lineStyle: {
          type: [5, 10],
          dashOffset: 5
        }
      }
    },
    {
      position: 'right',
      type: 'value',
      name: '',
      axisLine: {
        show: true,
        lineStyle: {
          types: 'solid'
        }
      },
      axisLabel: {
        show: true
      },
      splitLine: {
        lineStyle: {
          type: [5, 10],
          dashOffset: 5
        }
      }
    }],
    series: [
      {
        name: '出口压力(Mpa)',
        smooth: true,
        data: data1,
        type: 'line'
      },
      {
        name: '瞬时流量(m³/h)',
        smooth: true,
        data: data2,
        type: 'line',
        yAxisIndex: 1
      }
    ]
  }
  return option
}

export const pieOption = (title: string, color: string, data: any) => {
  const option = {
    color: [
      new echarts.graphic.LinearGradient(0, 0, 0, 1, [
        {
          offset: 0,
          color
        },
        {
          offset: 1,
          color: 'rgba(21, 45, 68,0.5)'
        }
      ]),
      'rgba(21, 45, 68,0.5)'
    ],
    title: {
      text: title,
      bottom: 0,
      left: 'center',
      textStyle: {
        color: 'rgb(137, 168, 195)',
        fontSize: 13
      }
    },
    series: [
      {
        startAngle: -60,
        name: 'Access From',
        type: 'pie',
        radius: ['62%', '70%'],
        avoidLabelOverlap: false,
        label: {
          show: true,
          position: 'center',
          color: '#ffffff',
          formatter() {
            return (data || 0) + '\n' + 'm³'
          }
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '12',
            fontWeight: 'bold'
          }
        },
        data: [
          { value: 10, name: '' },
          { value: 4, name: '' }
        ]
      }
    ]
  }
  return option
}

export const barOption = (title?: string, color?: string, dataX?: any[], data?: any[], left?: number, right?: number) => {
  const option = {
    color: ['#318DFF', '#43B530', '#FC2B2B'],
    xAxis: {
      type: 'category',
      data: dataX
    },
    grid: {
      left: left || 50,
      right: right || 50,
      top: 50,
      bottom: 50
    },
    legend: {
      // right: 150,
      // top: 10,
      width: '500',
      type: 'scroll',
      textStyle: {
        fontSize: 12
      }
    },
    yAxis: [{
      position: 'left',
      type: 'value',
      name: '吨水能耗(kw.h/m³)',
      splitNumber: 5,
      axisLine: {
        show: true,
        lineStyle: {
          types: 'solid'
        }
      },
      axisLabel: {
        show: true
      },
      splitLine: {
        lineStyle: {
          type: [5, 10],
          dashOffset: 5
        }
      }
    }],
    series: [
      {
        data,
        type: 'bar'
      }
    ]
  }
  return option
}
