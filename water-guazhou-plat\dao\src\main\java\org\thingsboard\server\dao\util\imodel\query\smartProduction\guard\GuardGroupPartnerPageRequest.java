package org.thingsboard.server.dao.util.imodel.query.smartProduction.guard;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartProduction.guard.GuardGroupPartner;
import org.thingsboard.server.dao.util.imodel.query.PageableQueryEntity;

@Getter
@Setter
public class GuardGroupPartnerPageRequest extends PageableQueryEntity<GuardGroupPartner> {
    // 班组id
    private String groupId;

}
