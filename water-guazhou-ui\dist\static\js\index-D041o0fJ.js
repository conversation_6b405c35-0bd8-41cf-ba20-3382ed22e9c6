import{m as t,a9 as l}from"./index-r0dFAfgr.js";const o=async e=>{const a=await t({url:`/api/revenue/sysCode/detailList?mainCode=${e}`,method:"get"});return l(a.data.data[e].details||[],"children",{label:"name",value:"key"})},r=async(e={label:"name",value:"id"})=>{const a=await t({url:"/api/revenue/meterBook/all",method:"GET",params:e});return l(a.data.data||[],"children",{label:e.label||"name",value:e.value||"id"})};export{r as a,o as g};
