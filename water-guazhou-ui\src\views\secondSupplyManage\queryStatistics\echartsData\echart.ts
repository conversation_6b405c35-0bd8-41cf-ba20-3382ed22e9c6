// 变化曲线图
export function lineOption(dataX?: any[]) {
  return {
    // backgroundColor: store.app.isDark ? '#131624' : '#F4F7FA',
    title: {
      text: '分析曲线',
      textStyle: {
        fontSize: '14px'
      },
      top: 10
    },
    grid: {
      left: 90,
      right: 90,
      top: 70,
      bottom: 40
    },
    legend: {
      top: 10,
      type: 'scroll',
      width: 500,
      textStyle: {
        fontSize: 12
      }
    },
    dataZoom: [
      {
        type: 'inside',
        start: 0,
        end: 30
      },
      {
        start: 0,
        end: 10
      }
    ],
    tooltip: { trigger: 'axis' },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: dataX
    },
    yAxis: [{
      position: 'left',
      type: 'value',
      name: '供水量(m³)',
      axisLine: {
        show: true,
        lineStyle: {
          types: 'solid'
        }
      },
      axisLabel: {
        show: true,
        textStyle: {
          // fontSize: 14 //更改坐标轴文字大小
        }
      },
      splitLine: {
        lineStyle: {
          type: [5, 10],
          dashOffset: 5
        }
      }
    }],
    series: [
      {
        name: '上限',
        smooth: true,
        data: [150, 230, 224, 218, 135, 147, 260, 135, 147, 260, 135, 147, 260, 135, 147, 260, 135, 147, 260, 135, 147, 260, 135, 260],
        type: 'line',
        markPoint: {
          data: [
            {
              type: 'max',
              name: '最大值'
            },
            {
              type: 'min',
              name: '最小值'
            }
          ]
        },
        markLine: {
          data: [{
            type: 'average',
            name: '平均值'
          }]
        }
      }
    ]
  }
}
