package org.thingsboard.server.dao.sql.smartProduction.guard;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.thingsboard.server.dao.model.sql.smartProduction.guard.GuardGroupPartner;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.guard.GuardGroupPartnerPageRequest;

import java.util.List;

@Mapper
public interface GuardGroupPartnerMapper extends BaseMapper<GuardGroupPartner> {
    IPage<GuardGroupPartner> findByPage(GuardGroupPartnerPageRequest request);

    int deleteAllByGroupId(String groupId);

    int saveAll(List<GuardGroupPartner> list);

}
