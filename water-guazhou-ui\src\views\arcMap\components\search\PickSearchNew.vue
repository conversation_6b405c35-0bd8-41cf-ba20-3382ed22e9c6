<!-- 点选查询 -->
<template>
  <DrawerBox
    ref="refDrawerBox"
    :right-drawer="true"
    :right-drawer-title="'点选查询'"
    :right-drawer-bar-position="'top'"
  >
    <ArcLayout @map-loaded="onMapLoaded" @pipe-loaded="onPipeLoaded">
      <template #map-bars>
        <ArcPops ref="refPops" :pops="state.pops" :close-on-click-modal="true">
          <template #default="{ config }">
            <div style="width: 200px; height: 200px; background-color: red">
              {{ config.id }}
            </div>
          </template>
        </ArcPops>
      </template>
    </ArcLayout>
    <template #right>
      <Form ref="refForm" :config="FormConfig"></Form>
      <FormTable :config="TableConfig"></FormTable>
    </template>
  </DrawerBox>
</template>
<script lang="ts" setup>
import { useGisStore } from '@/store';
import {
  excuteIdentify,
  setSymbol,
  getGraphicLayer,
  initIdentifyParams,
  setMapCursor,
  gotoAndHighLight,
  getGeometryCenter
} from '@/utils/MapHelper';
import { SLMessage } from '@/utils/Message';

const refDrawerBox = ref<IDrawerBoxIns>();
const refPops = ref<IArcPopsIns>();
const refForm = ref<IFormIns>();
const state = reactive<{
  pops: IArcPopConfig[];
}>({
  pops: []
});
const staticState: {
  view?: __esri.MapView;
  graphicsLayer?: __esri.GraphicsLayer;
  mapClick?: any;
  identifyResults: any[];
} = {
  view: undefined,
  identifyResults: []
};
const FormConfig = reactive<IFormConfig>({
  gutter: 12,
  labelPosition: 'top',
  group: [
    {
      fieldset: {
        desc: '选择图层'
      },
      fields: [
        {
          type: 'tree',
          options: [],
          checkStrictly: false,
          showCheckbox: true,
          field: 'layerid',
          nodeKey: 'value'
        }
      ]
    },
    {
      fieldset: {
        desc: '点击地图进行查询'
      },
      fields: []
    }
  ]
});
const TableConfig = reactive<ITable>({
  height: 'none',
  columns: [
    { label: '类型', prop: 'layerName' },
    { label: '编号', prop: '新编号' }
  ],
  pagination: {
    hide: true
  },
  handleRowClick: async (row) => {
    const result = staticState.identifyResults.find(
      (item) => item.feature.attributes.OBJECTID === row.OBJECTID
    );
    await gotoAndHighLight(staticState.view, result?.feature);
    // openPop(row.OBJECTID)
    refPops.value?.togglePopById(row.OBJECTID, true);
  },
  dataList: []
});
const getLayerInfo = () => {
  const gisStore = useGisStore();
  const field = FormConfig.group[0].fields[0] as IFormTree;
  const points = gisStore.gLayerInfos
    ?.filter((item) => item.geometrytype === 'esriGeometryPoint')
    .map((item) => {
      return {
        label: item.layername,
        value: item.layerid,
        data: item
      };
    });
  const lines = gisStore.gLayerInfos
    ?.filter((item) => item.geometrytype === 'esriGeometryPolyline')
    .map((item) => {
      return {
        label: item.layername,
        value: item.layerid,
        data: item
      };
    });
  field &&
    (field.options = [
      { label: '管点类', value: -1, children: points },
      { label: '管线类', value: -2, children: lines }
    ]);
  refForm.value && (refForm.value.dataForm.layerid = gisStore.gLayerIds);
};
const pickPipe = () => {
  if (!staticState.view) return;
  setMapCursor('crosshair');
  staticState.graphicsLayer = getGraphicLayer(staticState.view, {
    id: 'search-pick',
    title: '查询结果'
  });
  staticState.mapClick = staticState.view?.on('click', async (e) => {
    await doIdentify(e);
  });
};

const doIdentify = async (e: any) => {
  if (!staticState.view) return;
  try {
    const queryParams = initIdentifyParams({
      layerIds: refForm.value?.dataForm.layerid || [],
      geometry: e.mapPoint,
      mapExtent: staticState.view.extent
    });

    const res = await excuteIdentify(
      window.SITE_CONFIG.GIS_CONFIG.gisService +
        window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService,
      queryParams
    );
    if (!res.results?.length) {
      SLMessage.warning('没有查询到设备');
      return;
    }
    staticState.graphicsLayer?.removeAll();
    staticState.identifyResults = res.results || [];
    staticState.identifyResults.length > 50 &&
      (staticState.identifyResults.length = 50);
    const tableData: any[] = [];
    const features: __esri.Graphic[] = [];
    staticState.identifyResults.map((item) => {
      tableData.push({
        layerName: item.layerName,
        layerId: item.layerId,
        ...item.feature.attributes
      });
      item.feature.symbol = setSymbol(item.feature.geometry.type);
      features.push(item.feature);
      const center = getGeometryCenter(item.feature.geometry) || [];
      state.pops.push({
        id: item.feature.attributes.OBJECTID,
        visible: false,
        x: center[0],
        y: center[1],
        title: item.layerName + '(' + item.feature.attributes['新编号'] + ')',
        attributes: {
          row: item.feature.attributes,
          id: item.feature.attributes.OBJECTID
        }
      });
    });
    staticState.graphicsLayer?.addMany(features);
    TableConfig.dataList = tableData;
    await gotoAndHighLight(
      staticState.view,
      staticState.identifyResults[0].feature
    );
    const id = staticState.identifyResults[0]?.feature.attributes.OBJECTID;
    refPops.value?.togglePopById(id, true);
  } catch (error) {
    //
  }
};

const onMapLoaded = async (view) => {
  staticState.view = view;
};
const onPipeLoaded = () => {
  getLayerInfo();
  pickPipe();
};
onMounted(() => {
  refDrawerBox.value?.toggleDrawer('rtl', true);
});
onBeforeUnmount(() => {
  staticState.mapClick?.remove();
  staticState.graphicsLayer &&
    staticState.view?.map.remove(staticState.graphicsLayer);
});
</script>
<style lang="scss" scoped></style>
