<!-- 点选查询 -->
<template>
  <DrawerBox
    ref="refDrawerBox"
    :right-drawer="true"
    :right-drawer-title="'点选查询'"
    :right-drawer-bar-position="'top'"
  >
    <ArcLayout @map-loaded="onMapLoaded" @pipe-loaded="onPipeLoaded">
      <template #map-bars>
        <ArcPops ref="refPops" :pops="state.pops" :close-on-click-modal="true">
          <template #default="{ config }">
            <div style="width: 200px; height: 200px; background-color: red">
              {{ config.id }}
            </div>
          </template>
        </ArcPops>
      </template>
    </ArcLayout>
    <template #right>
      <Form ref="refForm" :config="FormConfig"></Form>
      <FormTable :config="TableConfig"></FormTable>
    </template>
  </DrawerBox>
</template>
<script lang="ts" setup>
import { useGisStore } from '@/store';
import Graphic from "@arcgis/core/Graphic.js";
import Point from "@arcgis/core/geometry/Point";
import Polyline from "@arcgis/core/geometry/Polyline";
import Polygon from "@arcgis/core/geometry/Polygon";
import SpatialReference from '@arcgis/core/geometry/SpatialReference';
import {
  excuteIdentifyByGeoserver,
  setSymbol,
  getGraphicLayer,
  initIdentifyParams,
  setMapCursor,
  gotoAndHighLight,
  getGeometryCenter
} from '@/utils/MapHelper';
import { SLMessage } from '@/utils/Message';

/**
 * 将GeoJSON格式的几何对象转换为ArcGIS几何类实例
 * @param geoJson GeoJSON格式的几何对象
 * @returns ArcGIS几何类实例
 */
const convertGeoJSONToArcGIS = (geoJson: any) => {
  if (!geoJson) return null;

  const spatialReference = new SpatialReference({ wkid: 4326 });

  switch (geoJson.type) {
    case 'Point':
      return new Point({
        x: geoJson.coordinates[0],
        y: geoJson.coordinates[1],
        spatialReference
      });

    case 'MultiPoint':
      // MultiPoint应该使用Multipoint类，但这里简化处理，返回第一个点
      return new Point({
        x: geoJson.coordinates[0][0],
        y: geoJson.coordinates[0][1],
        spatialReference
      });

    case 'LineString':
      return new Polyline({
        paths: [geoJson.coordinates],
        spatialReference
      });

    case 'MultiLineString':
      return new Polyline({
        paths: geoJson.coordinates,
        spatialReference
      });

    case 'Polygon':
      return new Polygon({
        rings: geoJson.coordinates,
        spatialReference
      });

    case 'MultiPolygon':
      // 多边形需要特殊处理，将多个多边形合并为一个
      const rings: any[] = [];
      geoJson.coordinates.forEach((polygonCoords: any[]) => {
        polygonCoords.forEach((ring: any[]) => {
          rings.push(ring);
        });
      });

      return new Polygon({
        rings,
        spatialReference
      });

    default:
      console.error('不支持的几何类型:', geoJson.type);
      return null;
  }
};

const refDrawerBox = ref<IDrawerBoxIns>();
const refPops = ref<IArcPopsIns>();
const refForm = ref<IFormIns>();
const state = reactive<{
  pops: IArcPopConfig[];
}>({
  pops: []
});
const staticState: {
  view?: __esri.MapView;
  graphicsLayer?: __esri.GraphicsLayer;
  mapClick?: any;
  identifyResults: any[];
} = {
  view: undefined,
  identifyResults: []
};
const FormConfig = reactive<IFormConfig>({
  gutter: 12,
  labelPosition: 'top',
  group: [
    {
      fieldset: {
        desc: '选择图层'
      },
      fields: [
        {
          type: 'tree',
          options: [],
          checkStrictly: false,
          showCheckbox: true,
          field: 'layerid',
          nodeKey: 'value'
        }
      ]
    },
    {
      fieldset: {
        desc: '点击地图进行查询'
      },
      fields: []
    }
  ]
});
const TableConfig = reactive<ITable>({
  height: 'none',
  columns: [
    { label: '类型', prop: 'layerName' },
    { label: '编号', prop: 'OBJECTID' }
  ],
  pagination: {
    hide: true
  },
  handleRowClick: async (row) => {
    const result = staticState.identifyResults.find(
      (item) => item.properties?.OBJECTID === row.OBJECTID
    );
    if (result) {
      // 将GeoJSON转换为ArcGIS Graphic
      const geometry = convertGeoJSONToArcGIS(result.geometry);
      if (geometry) {
        const graphic = new Graphic({
          geometry: geometry,
          attributes: result.properties || {},
          symbol: setSymbol(geometry.type)
        });
        await gotoAndHighLight(staticState.view, graphic);
      }
    }
    debugger
    refPops.value?.togglePopById(row.OBJECTID, true);
  },
  dataList: []
});

const getLayerInfo = () => {
  const field = FormConfig.group[0].fields[0] as IFormTree;
  if (staticState.view?.layerViews && staticState.view.layerViews.length > 0) {
    const layerView = staticState.view.layerViews.getItemAt(1);
    if (layerView?.layer && 'sublayers' in layerView.layer) {
      const layerInfo = (layerView.layer as any).sublayers;
      if (layerInfo) {
        let layers = layerInfo.items.map(item => {
          return {
            label: item.name,
            value: item.name,
          }
        });
        field.options = layers;
      }
    }
  }
};

const pickPipe = () => {
  if (!staticState.view) return;
  setMapCursor('crosshair');
  staticState.graphicsLayer = getGraphicLayer(staticState.view, {
    id: 'search-pick',
    title: '查询结果'
  });
  staticState.mapClick = staticState.view?.on('click', async (e) => {
    await doIdentify(e);
  });
};

const doIdentify = async (e: any) => {
  if (!staticState.view) return;
  try {
    const layerIds = refForm.value?.dataForm.layerid || [];
    if (!layerIds.length) {
      SLMessage.warning('请先选择要查询的图层');
      return;
    }

    // 使用GeoServer的WMS服务进行查询
    const response = await excuteIdentifyByGeoserver(
      staticState.view,
      '/geoserver/guazhou/wms',
      layerIds.join(','),
      e
    );

    if (!response || !response.data || !response.data.features || !response.data.features.length) {
      SLMessage.warning('没有查询到设备');
      return;
    }

    staticState.graphicsLayer?.removeAll();
    staticState.identifyResults = response.data.features || [];
    
    // 限制结果数量
    if (staticState.identifyResults.length > 50) {
      staticState.identifyResults = staticState.identifyResults.slice(0, 50);
    }

    const tableData: any[] = [];
    const features: __esri.Graphic[] = [];
    state.pops = [];

    staticState.identifyResults.forEach((item) => {
      // 将GeoJSON转换为ArcGIS Graphic
      const geometry = convertGeoJSONToArcGIS(item.geometry);
      if (geometry) {
        const graphic = new Graphic({
          geometry: geometry,
          attributes: item.properties || {},
          symbol: setSymbol(geometry.type)
        });

        features.push(graphic);

        // 计算几何中心点用于弹窗显示
        const center = getGeometryCenter(geometry) || [];
        
        state.pops.push({
          id: item.properties?.OBJECTID || item.id,
          visible: false,
          x: center[0],
          y: center[1],
          title: `${item.id || '未知图层'}(${item.properties?.OBJECTID || '未知编号'})`,
          attributes: {
            row: item.properties || {},
            id: item.properties?.OBJECTID || item.id
          }
        });

        tableData.push({
          layerName: item.id || '未知图层',
          layerId: item.id,
          OBJECTID: item.properties?.OBJECTID || item.id,
          ...item.properties
        });
      }
    });

    staticState.graphicsLayer?.addMany(features);
    TableConfig.dataList = tableData;

    // 高亮显示第一个结果
    if (features.length > 0) {
      await gotoAndHighLight(staticState.view, features[0]);
      const firstId = staticState.identifyResults[0]?.properties?.OBJECTID || staticState.identifyResults[0]?.id;
      refPops.value?.togglePopById(firstId, true);
    }
  } catch (error) {
    console.error('查询失败:', error);
    SLMessage.error('查询失败，请重试');
  }
};

const onMapLoaded = async (view) => {
  staticState.view = view;
};

const onPipeLoaded = () => {
  setTimeout(() => {
    getLayerInfo();
    pickPipe();
  }, 1000);
};

onMounted(() => {
  refDrawerBox.value?.toggleDrawer('rtl', true);
});

onBeforeUnmount(() => {
  staticState.mapClick?.remove();
  staticState.graphicsLayer &&
    staticState.view?.map.remove(staticState.graphicsLayer);
});
</script>
<style lang="scss" scoped></style>
