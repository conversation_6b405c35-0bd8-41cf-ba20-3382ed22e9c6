import{_ as D}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as T}from"./CardTable-rdWOL4_6.js";import{_ as w}from"./CardSearch-CB_HNR-Q.js";import{z as u,C as P,c as y,r as d,b as i,S as k,o as q,g as L,n as V,q as g}from"./index-r0dFAfgr.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";function B(l){return u({url:"/api/base/push/scheme/configuration/list",method:"get",params:l})}function F(l){return u({url:"/api/base/push/scheme/configuration/getDetail",method:"get",params:{id:l}})}function _(l){return u({url:"/api/base/push/scheme/configuration/add",method:"post",data:l})}function S(l){return u({url:"/api/base/push/scheme/configuration/edit",method:"post",data:l})}function I(l){return u({url:"/api/base/push/scheme/configuration/deleteIds",method:"delete",data:l})}const z={class:"wrapper"},E={__name:"basePushSchemeConfiguration",setup(l){const m=y(),c=y(),C=d({labelWidth:"100px",filters:[{type:"input",label:"方案名称",field:"name",placeholder:"请输入方案名称",onChange:()=>n()},{type:"btn-group",btns:[{type:"primary",perm:!0,text:"查询",click:()=>n()},{perm:!0,type:"primary",text:"新增",click:()=>h()},{perm:!0,type:"danger",text:"批量删除",click:()=>b()}]}],defaultParams:{}}),o=d({columns:[{label:"方案名称",prop:"name"},{label:"关联模板",prop:"templateId"},{label:"目标用户类型",prop:"targetType"},{label:"触发条件",prop:"triggerType"},{label:"发送策略",prop:"sendStrategy"},{label:"状态",prop:"status",formatter:e=>e.status===1?"启用":"禁用"},{label:"创建时间",prop:"createTime"}],dataList:[],operations:[{perm:!0,type:"primary",isTextBtn:!0,text:"查看详情",click:e=>x(e)},{perm:!0,type:"primary",isTextBtn:!0,text:"编辑",click:e=>h(e)},{perm:!0,type:"danger",isTextBtn:!0,text:"删除",click:e=>b(e)}],pagination:{total:0,page:1,align:"right",limit:20,handlePage:e=>{o.pagination.page=e,n()},handleSize:e=>{o.pagination.limit=e,n()}},handleSelectChange:e=>{o.selectList=e||[]}}),a=d({title:"新增推送方案配置",group:[{fields:[{type:"input",label:"方案名称",field:"name",rules:[{required:!0,message:"请输入方案名称"}]},{type:"input",label:"关联模板",field:"templateId",rules:[{required:!0,message:"请输入关联模板"}]},{type:"input",label:"目标用户类型",field:"targetType",rules:[{required:!0,message:"请输入目标用户类型"}]},{type:"select",label:"触发条件",field:"triggerType",options:[{label:"事件触发",value:"事件触发"},{label:"定时任务",value:"定时任务"},{label:"手动触发",value:"手动触发"}],rules:[{required:!0,message:"请选择触发条件"}]},{type:"select",label:"发送策略",field:"sendStrategy",options:[{label:"立即发送",value:"立即发送"},{label:"延迟发送",value:"延迟发送"},{label:"指定事件发送",value:"指定事件发送"}],rules:[{required:!0,message:"请选择发送策略"}]},{type:"switch",label:"状态",field:"status",activeValue:1,inactiveValue:0,rules:[{required:!0,message:"请选择状态"}]},{type:"date",label:"创建时间",field:"createTime",rules:[{required:!0,message:"请选择创建时间"}]}]}],labelPosition:"top",defaultValue:{},dialogWidth:600,draggable:!0,showSubmit:!0,showCancel:!0,cancelText:"取消",submitText:"确定",submit:async e=>{var t;try{e.id?(await S(e),i.success("修改成功")):(await _(e),i.success("新增成功")),(t=c.value)==null||t.closeDialog(),n()}catch{i.error("操作失败")}}}),f=()=>{a.group[0].fields.forEach(e=>{e.disabled=!1,e.readonly=!1}),a.showSubmit=!0,a.showCancel=!0,a.cancelText="取消",a.submitText="确定",a.submit=async e=>{var t;try{e.id?(await S(e),i.success("修改成功")):(await _(e),i.success("新增成功")),(t=c.value)==null||t.closeDialog(),n()}catch{i.error("操作失败")}}},x=async e=>{var t,r;try{const s=await F(e.id),p=((t=s.data)==null?void 0:t.data)||s;f(),a.title="推送方案配置详情",a.defaultValue={...p},a.group[0].fields.forEach(v=>{v.disabled=!0}),a.showSubmit=!1,a.cancelText="关闭",(r=c.value)==null||r.openDialog()}catch{i.error("获取详情失败")}},h=e=>{var t;f(),e?(a.title="编辑推送方案配置",a.defaultValue={...e}):(a.title="新增推送方案配置",a.defaultValue={}),(t=c.value)==null||t.openDialog()},b=async e=>{try{const t=e?[e.id]:o.selectList.map(r=>r.id);if(!t.length){i.warning("请选择要删除的数据");return}await k("确定要删除选中的数据吗？"),await I(t),i.success("删除成功"),n()}catch(t){t!=="cancel"&&i.error("删除失败")}},n=async()=>{var e,t;try{const r=await B({page:o.pagination.page,size:o.pagination.limit,...((e=m.value)==null?void 0:e.queryParams)||{}}),s=((t=r.data)==null?void 0:t.data)||r;o.dataList=s.records||s,o.pagination.total=s.total||s.length||0}catch{i.error("数据加载失败")}};return q(()=>{n()}),(e,t)=>{const r=w,s=T,p=D;return L(),V("div",z,[g(r,{ref_key:"refSearch",ref:m,config:C},null,8,["config"]),g(s,{class:"card-table",config:o},null,8,["config"]),g(p,{ref_key:"refDialogForm",ref:c,config:a},null,8,["config"])])}}},G=P(E,[["__scopeId","data-v-6d1ace5c"]]);export{G as default};
