import{R as P,T as ie,fA as se}from"./index-r0dFAfgr.js";import{c as L}from"./Texture-BYqObwfn.js";import{bO as R}from"./MapView-DaoQedLH.js";import{H as p,t as S}from"./enums-BDQrMlcz.js";const le=["layout","centroid","smooth","case","mat2x2","mat2x3","mat2x4","mat3x2","mat3x3","mat3x4","mat4x2","mat4x3","mat4x4","uint","uvec2","uvec3","uvec4","samplerCubeShadow","sampler2DArray","sampler2DArrayShadow","isampler2D","isampler3D","isamplerCube","isampler2DArray","usampler2D","usampler3D","usamplerCube","usampler2DArray","coherent","restrict","readonly","writeonly","resource","atomic_uint","noperspective","patch","sample","subroutine","common","partition","active","filter","image1D","image2D","image3D","imageCube","iimage1D","iimage2D","iimage3D","iimageCube","uimage1D","uimage2D","uimage3D","uimageCube","image1DArray","image2DArray","iimage1DArray","iimage2DArray","uimage1DArray","uimage2DArray","image1DShadow","image2DShadow","image1DArrayShadow","image2DArrayShadow","imageBuffer","iimageBuffer","uimageBuffer","sampler1DArray","sampler1DArrayShadow","isampler1D","isampler1DArray","usampler1D","usampler1DArray","isampler2DRect","usampler2DRect","samplerBuffer","isamplerBuffer","usamplerBuffer","sampler2DMS","isampler2DMS","usampler2DMS","sampler2DMSArray","isampler2DMSArray","usampler2DMSArray","trunc","round","roundEven","isnan","isinf","floatBitsToInt","floatBitsToUint","intBitsToFloat","uintBitsToFloat","packSnorm2x16","unpackSnorm2x16","packUnorm2x16","unpackUnorm2x16","packHalf2x16","unpackHalf2x16","outerProduct","transpose","determinant","inverse","texture","textureSize","textureProj","textureLod","textureOffset","texelFetch","texelFetchOffset","textureProjOffset","textureLodOffset","textureProjLod","textureProjLodOffset","textureGrad","textureGradOffset","textureProjGrad","textureProjGradOffset"];var $,C={},ce={get exports(){return C},set exports(r){C=r}};($=["precision","highp","mediump","lowp","attribute","const","uniform","varying","break","continue","do","for","while","if","else","in","out","inout","float","int","void","bool","true","false","discard","return","mat2","mat3","mat4","vec2","vec3","vec4","ivec2","ivec3","ivec4","bvec2","bvec3","bvec4","sampler1D","sampler2D","sampler3D","samplerCube","sampler1DShadow","sampler2DShadow","struct","asm","class","union","enum","typedef","template","this","packed","goto","switch","default","inline","noinline","volatile","public","static","extern","external","interface","long","short","double","half","fixed","unsigned","input","output","hvec2","hvec3","hvec4","dvec2","dvec3","dvec4","fvec2","fvec3","fvec4","sampler2DRect","sampler3DRect","sampler2DRectShadow","sizeof","cast","namespace","using"])!==void 0&&(ce.exports=$);const fe=C;var B,b={},me={get exports(){return b},set exports(r){b=r}};B=me,function(r){var e=["<<=",">>=","++","--","<<",">>","<=",">=","==","!=","&&","||","+=","-=","*=","/=","%=","&=","^^","^=","|=","(",")","[","]",".","!","~","*","/","%","+","-","<",">","&","^","|","?",":","=",",",";","{","}"];e!==void 0&&(B.exports=e)}();const I=b;var A={},ue={get exports(){return A},set exports(r){A=r}};(function(r){(function(e){var o=function(){return["abs","acos","all","any","asin","atan","ceil","clamp","cos","cross","dFdx","dFdy","degrees","distance","dot","equal","exp","exp2","faceforward","floor","fract","gl_BackColor","gl_BackLightModelProduct","gl_BackLightProduct","gl_BackMaterial","gl_BackSecondaryColor","gl_ClipPlane","gl_ClipVertex","gl_Color","gl_DepthRange","gl_DepthRangeParameters","gl_EyePlaneQ","gl_EyePlaneR","gl_EyePlaneS","gl_EyePlaneT","gl_Fog","gl_FogCoord","gl_FogFragCoord","gl_FogParameters","gl_FragColor","gl_FragCoord","gl_FragData","gl_FragDepth","gl_FragDepthEXT","gl_FrontColor","gl_FrontFacing","gl_FrontLightModelProduct","gl_FrontLightProduct","gl_FrontMaterial","gl_FrontSecondaryColor","gl_LightModel","gl_LightModelParameters","gl_LightModelProducts","gl_LightProducts","gl_LightSource","gl_LightSourceParameters","gl_MaterialParameters","gl_MaxClipPlanes","gl_MaxCombinedTextureImageUnits","gl_MaxDrawBuffers","gl_MaxFragmentUniformComponents","gl_MaxLights","gl_MaxTextureCoords","gl_MaxTextureImageUnits","gl_MaxTextureUnits","gl_MaxVaryingFloats","gl_MaxVertexAttribs","gl_MaxVertexTextureImageUnits","gl_MaxVertexUniformComponents","gl_ModelViewMatrix","gl_ModelViewMatrixInverse","gl_ModelViewMatrixInverseTranspose","gl_ModelViewMatrixTranspose","gl_ModelViewProjectionMatrix","gl_ModelViewProjectionMatrixInverse","gl_ModelViewProjectionMatrixInverseTranspose","gl_ModelViewProjectionMatrixTranspose","gl_MultiTexCoord0","gl_MultiTexCoord1","gl_MultiTexCoord2","gl_MultiTexCoord3","gl_MultiTexCoord4","gl_MultiTexCoord5","gl_MultiTexCoord6","gl_MultiTexCoord7","gl_Normal","gl_NormalMatrix","gl_NormalScale","gl_ObjectPlaneQ","gl_ObjectPlaneR","gl_ObjectPlaneS","gl_ObjectPlaneT","gl_Point","gl_PointCoord","gl_PointParameters","gl_PointSize","gl_Position","gl_ProjectionMatrix","gl_ProjectionMatrixInverse","gl_ProjectionMatrixInverseTranspose","gl_ProjectionMatrixTranspose","gl_SecondaryColor","gl_TexCoord","gl_TextureEnvColor","gl_TextureMatrix","gl_TextureMatrixInverse","gl_TextureMatrixInverseTranspose","gl_TextureMatrixTranspose","gl_Vertex","greaterThan","greaterThanEqual","inversesqrt","length","lessThan","lessThanEqual","log","log2","matrixCompMult","max","min","mix","mod","normalize","not","notEqual","pow","radians","reflect","refract","sign","sin","smoothstep","sqrt","step","tan","texture2D","texture2DLod","texture2DProj","texture2DProjLod","textureCube","textureCubeLod","texture2DLodEXT","texture2DProjLodEXT","textureCubeLodEXT","texture2DGradEXT","texture2DProjGradEXT","textureCubeGradEXT","textureSize","texelFetch"]}();o!==void 0&&(r.exports=o)})()})(ue);const ge=A;var _=999,O=9999,D=0,w=1,V=2,N=3,G=4,M=5,he=6,de=7,_e=8,X=9,pe=10,H=11,xe=["block-comment","line-comment","preprocessor","operator","integer","float","ident","builtin","keyword","whitespace","eof","integer"];function ve(){var r,e,o,t=0,i=0,n=_,a=[],c=[],l=1,s=0,f=0,m=!1,d=!1,g="";return function(u){return c=[],u!==null?Q(u.replace?u.replace(/\r\n/g,`
`):u):W()};function h(u){u.length&&c.push({type:xe[n],data:u,position:f,line:l,column:s})}function Q(u){var v;for(t=0,o=(g+=u).length;r=g[t],t<o;){switch(v=t,n){case D:t=ee();break;case w:t=Y();break;case V:t=F();break;case N:t=te();break;case G:t=oe();break;case H:t=re();break;case M:t=ae();break;case O:t=ne();break;case X:t=J();break;case _:t=Z()}v!==t&&(g[v]===`
`?(s=0,++l):++s)}return i+=t,g=g.slice(t),c}function W(u){return a.length&&h(a.join("")),n=pe,h("(eof)"),c}function Z(){return a=a.length?[]:a,e==="/"&&r==="*"?(f=i+t-1,n=D,e=r,t+1):e==="/"&&r==="/"?(f=i+t-1,n=w,e=r,t+1):r==="#"?(n=V,f=i+t,t):/\s/.test(r)?(n=X,f=i+t,t):(m=/\d/.test(r),d=/[^\w_]/.test(r),f=i+t,n=m?G:d?N:O,t)}function J(){return/[^\s]/g.test(r)?(h(a.join("")),n=_,t):(a.push(r),e=r,t+1)}function F(){return r!=="\r"&&r!==`
`||e==="\\"?(a.push(r),e=r,t+1):(h(a.join("")),n=_,t)}function Y(){return F()}function ee(){return r==="/"&&e==="*"?(a.push(r),h(a.join("")),n=_,t+1):(a.push(r),e=r,t+1)}function te(){if(e==="."&&/\d/.test(r))return n=M,t;if(e==="/"&&r==="*")return n=D,t;if(e==="/"&&r==="/")return n=w,t;if(r==="."&&a.length){for(;U(a););return n=M,t}if(r===";"||r===")"||r==="("){if(a.length)for(;U(a););return h(r),n=_,t+1}var u=a.length===2&&r!=="=";if(/[\w_\d\s]/.test(r)||u){for(;U(a););return n=_,t}return a.push(r),e=r,t+1}function U(u){for(var v,T,j=0;;){if(v=I.indexOf(u.slice(0,u.length+j).join("")),T=I[v],v===-1){if(j--+u.length>0)continue;T=u.slice(0,1).join("")}return h(T),f+=T.length,(a=a.slice(T.length)).length}}function re(){return/[^a-fA-F0-9]/.test(r)?(h(a.join("")),n=_,t):(a.push(r),e=r,t+1)}function oe(){return r==="."||/[eE]/.test(r)?(a.push(r),n=M,e=r,t+1):r==="x"&&a.length===1&&a[0]==="0"?(n=H,a.push(r),e=r,t+1):/[^\d]/.test(r)?(h(a.join("")),n=_,t):(a.push(r),e=r,t+1)}function ae(){return r==="f"&&(a.push(r),e=r,t+=1),/[eE]/.test(r)||r==="-"&&/[eE]/.test(e)?(a.push(r),e=r,t+1):/[^\d]/.test(r)?(h(a.join("")),n=_,t):(a.push(r),e=r,t+1)}function ne(){if(/[^\d\w_]/.test(r)){var u=a.join("");return n=fe.indexOf(u)>-1?_e:ge.indexOf(u)>-1?de:he,h(a.join("")),n=_,t}return a.push(r),e=r,t+1}}function Te(r){var e=ve(),o=[];return o=(o=o.concat(e(r))).concat(e(null))}function ye(r){return Te(r)}function Se(r){return r.map(e=>e.type!=="eof"?e.data:"").join("")}const E=["GL_OES_standard_derivatives","GL_EXT_frag_depth","GL_EXT_draw_buffers","GL_EXT_shader_texture_lod"];function Me(r,e="100",o="300 es"){const t=/^\s*\#version\s+([0-9]+(\s+[a-zA-Z]+)?)\s*/;for(const i of r)if(i.type==="preprocessor"){const n=t.exec(i.data);if(n){const a=n[1].replace(/\s\s+/g," ");if(a===o)return a;if(a===e)return i.data="#version "+o,e;throw new Error("unknown glsl version: "+a)}}return r.splice(0,0,{type:"preprocessor",data:"#version "+o},{type:"whitespace",data:`
`}),null}function Ue(r,e){for(let o=e-1;o>=0;o--){const t=r[o];if(t.type!=="whitespace"&&t.type!=="block-comment"){if(t.type!=="keyword")break;if(t.data==="attribute"||t.data==="in")return!0}}return!1}function y(r,e,o,t){t=t||o;for(const i of r)if(i.type==="ident"&&i.data===o)return t in e?e[t]++:e[t]=0,y(r,e,t+"_"+e[t],t);return o}function K(r,e,o="afterVersion"){function t(l,s){for(let f=s;f<l.length;f++){const m=l[f];if(m.type==="operator"&&m.data===";")return f}return null}function i(l){let s=-1,f=0,m=-1;for(let d=0;d<l.length;d++){const g=l[d];if(g.type==="preprocessor"&&(g.data.match(/\#(if|ifdef|ifndef)\s+.+/)?++f:g.data.match(/\#endif\s*.*/)&&--f),o!=="afterVersion"&&o!=="afterPrecision"||g.type==="preprocessor"&&/^#version/.test(g.data)&&(m=Math.max(m,d)),o==="afterPrecision"&&g.type==="keyword"&&g.data==="precision"){const h=t(l,d);if(h===null)throw new Error("precision statement not followed by any semicolons!");m=Math.max(m,h)}s<m&&f===0&&(s=d)}return s+1}const n={data:`
`,type:"whitespace"},a=l=>l<r.length&&/[^\r\n]$/.test(r[l].data);let c=i(r);a(c-1)&&r.splice(c++,0,n);for(const l of e)r.splice(c++,0,l);a(c-1)&&a(c)&&r.splice(c,0,n)}function De(r,e,o,t="lowp"){K(r,[{type:"keyword",data:"out"},{type:"whitespace",data:" "},{type:"keyword",data:t},{type:"whitespace",data:" "},{type:"keyword",data:o},{type:"whitespace",data:" "},{type:"ident",data:e},{type:"operator",data:";"}],"afterPrecision")}function we(r,e,o,t,i="lowp"){K(r,[{type:"keyword",data:"layout"},{type:"operator",data:"("},{type:"keyword",data:"location"},{type:"whitespace",data:" "},{type:"operator",data:"="},{type:"whitespace",data:" "},{type:"integer",data:t.toString()},{type:"operator",data:")"},{type:"whitespace",data:" "},{type:"keyword",data:"out"},{type:"whitespace",data:" "},{type:"keyword",data:i},{type:"whitespace",data:" "},{type:"keyword",data:o},{type:"whitespace",data:" "},{type:"ident",data:e},{type:"operator",data:";"}],"afterPrecision")}function Ee(r,e){let o,t,i=-1;for(let n=e;n<r.length;n++){const a=r[n];if(a.type==="operator"&&(a.data==="["&&(o=n),a.data==="]")){t=n;break}a.type==="integer"&&(i=parseInt(a.data,10))}return o&&t&&r.splice(o,t-o+1),i}function z(r,e){const o=Pe();if(P(o))return o;const t=ye(r);if(Me(t,"100","300 es")==="300 es")return r;let i=null,n=null;const a={},c={};for(let l=0;l<t.length;++l){const s=t[l];switch(s.type){case"keyword":e===p.VERTEX_SHADER&&s.data==="attribute"?s.data="in":s.data==="varying"&&(s.data=e===p.VERTEX_SHADER?"out":"in");break;case"builtin":if(/^texture(2D|Cube)(Proj)?(Lod|Grad)?(EXT)?$/.test(s.data.trim())&&(s.data=s.data.replace(/(2D|Cube|EXT)/g,"")),e===p.FRAGMENT_SHADER&&s.data==="gl_FragColor"&&(i||(i=y(t,a,"fragColor"),De(t,i,"vec4")),s.data=i),e===p.FRAGMENT_SHADER&&s.data==="gl_FragData"){const f=Ee(t,l+1),m=y(t,a,"fragData");we(t,m,"vec4",f,"mediump"),s.data=m}else e===p.FRAGMENT_SHADER&&s.data==="gl_FragDepthEXT"&&(n||(n=y(t,a,"gl_FragDepth")),s.data=n);break;case"ident":if(le.includes(s.data)){if(e===p.VERTEX_SHADER&&Ue(t,l))throw new Error("attribute in vertex shader uses a name that is a reserved word in glsl 300 es");s.data in c||(c[s.data]=y(t,a,s.data)),s.data=c[s.data]}}}for(let l=t.length-1;l>=0;--l){const s=t[l];if(s.type==="preprocessor"){const f=s.data.match(/\#extension\s+(.*)\:/);if(f&&f[1]&&E.includes(f[1].trim())){const g=t[l+1];t.splice(l,g&&g.type==="whitespace"?2:1)}const m=s.data.match(/\#ifdef\s+(.*)/);m&&m[1]&&E.includes(m[1].trim())&&(s.data="#if 1");const d=s.data.match(/\#ifndef\s+(.*)/);d&&d[1]&&E.includes(d[1].trim())&&(s.data="#if 0")}}return Le(r,Se(t))}function Pe(r){return null}function Le(r,e){return e}const Ce=4294967295;class be{constructor(e,o,t,i,n=new Map){this._context=e,this._locations=i,this._uniformBlockBindings=n,this._refCount=1,this._compiled=!1,this._nameToUniformLocation={},this._nameToUniform1={},this._nameToUniform1v=new Map,this._nameToUniform2=new Map,this._nameToUniform3=new Map,this._nameToUniform4=new Map,this._nameToUniformMatrix3=new Map,this._nameToUniformMatrix4=new Map,e||console.error("RenderingContext isn't initialized!"),o.length===0&&console.error("Shaders source should not be empty!"),this._context.type===R.WEBGL2&&(o=z(o,p.VERTEX_SHADER),t=z(t,p.FRAGMENT_SHADER)),this._vShader=q(this._context,p.VERTEX_SHADER,o),this._fShader=q(this._context,p.FRAGMENT_SHADER,t),this._vShader&&this._fShader||console.error("Error loading shaders!"),this._context.instanceCounter.increment(S.Shader,this),L()&&(this.vertexShader=o,this.fragmentShader=t);const a=this._context.gl,c=a.createProgram();if(a.attachShader(c,this._vShader),a.attachShader(c,this._fShader),this._locations.forEach((l,s)=>a.bindAttribLocation(c,l,s)),a.linkProgram(c),L()&&!a.getProgramParameter(c,a.LINK_STATUS)&&console.error(`Could not link shader
validated: ${a.getProgramParameter(c,a.VALIDATE_STATUS)}, gl error ${a.getError()}, vertex: ${a.getShaderParameter(this._vShader,a.COMPILE_STATUS)}, fragment: ${a.getShaderParameter(this._fShader,a.COMPILE_STATUS)}, info log: ${a.getProgramInfoLog(c)}, vertex source: ${this.vertexShader}, fragment source: ${this.fragmentShader}`),this._context.type===R.WEBGL2){const l=a;for(const[s,f]of this._uniformBlockBindings){const m=l.getUniformBlockIndex(c,s);m<Ce&&l.uniformBlockBinding(c,m,f)}}this._glName=c,this._context.instanceCounter.increment(S.Program,this)}get glName(){return this._glName}get hasGLName(){return P(this._glName)}get compiled(){if(this._compiled)return!0;const e=this._context.gl.getExtension("KHR_parallel_shader_compile");return e==null||ie(this.glName)?(this._compiled=!0,!0):(this._compiled=!!this._context.gl.getProgramParameter(this.glName,e.COMPLETION_STATUS_KHR),this._compiled)}dispose(){if(--this._refCount>0)return;const e=this._context.gl;this._vShader&&(e.deleteShader(this._vShader),this._vShader=null,this._context.instanceCounter.decrement(S.Shader,this)),this._fShader&&(e.deleteShader(this._fShader),this._fShader=null),this._glName&&(e.deleteProgram(this._glName),this._glName=null,this._context.instanceCounter.decrement(S.Program,this))}ref(){++this._refCount}_getUniformLocation(e){return this._nameToUniformLocation[e]===void 0&&P(this.glName)&&(++k.numUniforms,this._nameToUniformLocation[e]=this._context.gl.getUniformLocation(this.glName,e)),this._nameToUniformLocation[e]}hasUniform(e){return this._getUniformLocation(e)!==null}setUniform1i(e,o){const t=this._nameToUniform1[e];t!==void 0&&o===t||(this._context.gl.uniform1i(this._getUniformLocation(e),o),this._nameToUniform1[e]=o)}setUniform1iv(e,o){x(this._nameToUniform1v,e,o)&&this._context.gl.uniform1iv(this._getUniformLocation(e),o)}setUniform2iv(e,o){x(this._nameToUniform2,e,o)&&this._context.gl.uniform2iv(this._getUniformLocation(e),o)}setUniform3iv(e,o){x(this._nameToUniform3,e,o)&&this._context.gl.uniform3iv(this._getUniformLocation(e),o)}setUniform4iv(e,o){x(this._nameToUniform4,e,o)&&this._context.gl.uniform4iv(this._getUniformLocation(e),o)}setUniform1f(e,o){const t=this._nameToUniform1[e];t!==void 0&&o===t||(this._context.gl.uniform1f(this._getUniformLocation(e),o),this._nameToUniform1[e]=o)}setUniform1fv(e,o){x(this._nameToUniform1v,e,o)&&this._context.gl.uniform1fv(this._getUniformLocation(e),o)}setUniform2f(e,o,t){const i=this._nameToUniform2.get(e);i===void 0?(this._context.gl.uniform2f(this._getUniformLocation(e),o,t),this._nameToUniform2.set(e,[o,t])):o===i[0]&&t===i[1]||(this._context.gl.uniform2f(this._getUniformLocation(e),o,t),i[0]=o,i[1]=t)}setUniform2fv(e,o){x(this._nameToUniform2,e,o)&&this._context.gl.uniform2fv(this._getUniformLocation(e),o)}setUniform3f(e,o,t,i){const n=this._nameToUniform3.get(e);n===void 0?(this._context.gl.uniform3f(this._getUniformLocation(e),o,t,i),this._nameToUniform3[e]=[o,t,i]):o===n[0]&&t===n[1]&&i===n[2]||(this._context.gl.uniform3f(this._getUniformLocation(e),o,t,i),n[0]=o,n[1]=t,n[2]=i)}setUniform3fv(e,o){x(this._nameToUniform3,e,o)&&this._context.gl.uniform3fv(this._getUniformLocation(e),o)}setUniform4f(e,o,t,i,n){const a=this._nameToUniform4.get(e);a===void 0?(this._context.gl.uniform4f(this._getUniformLocation(e),o,t,i,n),this._nameToUniform4.set(e,[o,t,i,n])):a!==void 0&&o===a[0]&&t===a[1]&&i===a[2]&&n===a[3]||(this._context.gl.uniform4f(this._getUniformLocation(e),o,t,i,n),a[0]=o,a[1]=t,a[2]=i,a[3]=n)}setUniform4fv(e,o){x(this._nameToUniform4,e,o)&&this._context.gl.uniform4fv(this._getUniformLocation(e),o)}setUniformMatrix3fv(e,o,t=!1){x(this._nameToUniformMatrix3,e,o)&&this._context.gl.uniformMatrix3fv(this._getUniformLocation(e),t,o)}setUniformMatrix4fv(e,o,t=!1){x(this._nameToUniformMatrix4,e,o)&&this._context.gl.uniformMatrix4fv(this._getUniformLocation(e),t,o)}stop(){}}function q(r,e,o){const t=r.gl,i=t.createShader(e);return t.shaderSource(i,o),t.compileShader(i),L()&&!t.getShaderParameter(i,t.COMPILE_STATUS)&&(console.error("Compile error in ".concat(e===p.VERTEX_SHADER?"vertex":"fragment"," shader")),console.error(t.getShaderInfoLog(i)),console.error(Ae(o))),k.enabled&&(k.compiledLOC+=o.match(/\n/g).length+1),i}function Ae(r){let e=2;return r.replace(/\n/g,()=>`
`+ke(e++)+":")}function ke(r){return r>=1e3?r.toString():("  "+r).slice(-3)}function x(r,e,o){const t=r.get(e);return t?se(t,o):(r.set(e,Array.from(o)),!0)}const k={compiledLOC:0,numUniforms:0,enabled:!1};let Be=class{constructor(e){this._readFile=e}resolveIncludes(e){return this._resolve(e)}_resolve(e,o=new Map){if(o.has(e))return o.get(e);const t=this._read(e);if(!t)throw new Error(`cannot find shader file ${e}`);const i=/^[^\S\n]*#include\s+<(\S+)>[^\S\n]?/gm;let n=i.exec(t);const a=[];for(;n!=null;)a.push({path:n[1],start:n.index,length:n[0].length}),n=i.exec(t);let c=0,l="";return a.forEach(s=>{l+=t.slice(c,s.start),l+=o.has(s.path)?"":this._resolve(s.path,o),c=s.start+s.length}),l+=t.slice(c),o.set(e,l),l}_read(e){return this._readFile(e)}};function Oe(r,e,o=""){return new be(r,o+e.shaders.vertexShader,o+e.shaders.fragmentShader,e.attributes)}export{Oe as a,Be as e,be as h};
