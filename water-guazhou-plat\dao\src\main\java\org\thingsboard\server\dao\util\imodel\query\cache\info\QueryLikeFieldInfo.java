package org.thingsboard.server.dao.util.imodel.query.cache.info;

import org.thingsboard.server.dao.util.imodel.query.CriteriaBuilderWrapper;
import org.thingsboard.server.dao.util.imodel.query.cache.QueryFieldInfo;

import java.lang.reflect.Field;

public class QueryLikeFieldInfo extends QueryFieldInfo<String> {

    private final String prefix;
    private final String suffix;

    public QueryLikeFieldInfo(boolean or, Field field, String prefix, String suffix) {
        super(or, field);
        this.prefix = prefix;
        this.suffix = suffix;
    }

    @Override
    public void process(Object entity, CriteriaBuilderWrapper wrapper, String value) {
        wrapper.like(name, prefix + value + suffix, or);
    }

}
