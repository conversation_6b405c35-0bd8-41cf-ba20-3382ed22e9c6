import{d,r as u,o as v,g as i,n as r,p as s,i as a,q as h,F as l,aB as b,aJ as f,h as j,dB as C,dC as x,C as B}from"./index-r0dFAfgr.js";/* empty css                         */import{_ as I,a as k,b as y,c as S,d as w,e as E,f as F,g as G}from"./img_8-BTVxQQlz.js";const L="/static/jpg/gs-D6dridZK.jpg",N={class:"img-box"},V={class:"img-top"},Y=["src"],$=["src"],q=d({__name:"SCGY",setup(A){const c=t=>new URL(Object.assign({"../imgs/scroll/img_1.jpg":I,"../imgs/scroll/img_2.jpg":k,"../imgs/scroll/img_3.jpg":y,"../imgs/scroll/img_4.jpg":S,"../imgs/scroll/img_5.jpg":w,"../imgs/scroll/img_6.jpg":E,"../imgs/scroll/img_7.jpg":F,"../imgs/scroll/img_8.jpg":G})[`../imgs/scroll/${t}`],import.meta.url).href,_=u({currentImage:"",imgs:[...Array.from({length:8}).map((t,e)=>{const o=`img_${e+1}.jpg`;return{label:e.toString(),img:c(o)}})]}),g=t=>{_.currentImage=_.imgs[t].img};return v(()=>{g(0)}),(t,e)=>{const o=C,m=x;return i(),r("div",N,[e[0]||(e[0]=s("div",{class:"img-top"},[s("img",{src:L,alt:""})],-1)),s("div",V,[s("img",{src:a(_).currentImage,alt:""},null,8,Y)]),s("div",null,[h(m,{interval:4e3,type:"card",height:"120px",class:"mg_top_10",onChange:g},{default:l(()=>[(i(!0),r(b,null,f(a(_).imgs,(n,p)=>(i(),j(o,{key:p},{default:l(()=>[s("img",{src:n.img,style:{width:"100%",height:"160px"}},null,8,$)]),_:2},1024))),128))]),_:1})])])}}}),M=B(q,[["__scopeId","data-v-796d5167"]]);export{M as default};
