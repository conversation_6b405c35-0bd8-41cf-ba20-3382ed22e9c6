package org.thingsboard.server.controller.base;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.dao.model.sql.AppVersionEntity;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;
import org.thingsboard.server.dao.version.AppVersionService;

import java.util.List;

@RestController
@RequestMapping("api/app/version")
public class AppVersionController extends BaseController {

    @Autowired
    private AppVersionService appVersionService;

    @GetMapping("check/version")
    public AppVersionEntity checkVersion() {
        return appVersionService.getNewVersion();
    }

    @GetMapping("checkVersion/{appKey}")
    public IstarResponse checkNewVersion(@PathVariable String appKey, @RequestParam(required = false) String tenantKey) {
        return IstarResponse.ok(appVersionService.getNewVersion(appKey, tenantKey));
    }

    @PostMapping("save")
    public IstarResponse save(@RequestBody AppVersionEntity entity) {
        appVersionService.save(entity);

        return IstarResponse.ok();
    }

    @GetMapping("list")
    public IstarResponse findList(@RequestParam int page, @RequestParam int size) {
        return IstarResponse.ok(appVersionService.findList(page, size));
    }

    @DeleteMapping("remove")
    public IstarResponse remove(@RequestBody List<String> ids) {
        appVersionService.remove(ids);
        return IstarResponse.ok();
    }


}
