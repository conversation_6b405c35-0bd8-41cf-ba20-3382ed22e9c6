import{_ as pe}from"./Panel-DyoxrWMd.js";import{_ as de,a as ue}from"./PipeLength.vue_vue_type_script_setup_true_lang-BZfUsvDf.js";import{d as U,c as O,r as q,a8 as me,am as fe,bo as R,bR as H,i as d,g as m,n as E,p as w,aw as F,bh as ve,h as y,F as k,q as b,d5 as ge,bw as I,an as M,cy as he,d6 as we,ax as x,av as be,cE as G,C as J,cN as ye,o as ke,Q as _e,aB as Ce,aJ as Pe,cn as Be,cs as Me,W as $e}from"./index-r0dFAfgr.js";import{a as Oe,b as xe,e as Le,c as Ae,d as Te,_ as We}from"./arcWidgetButton-0glIxrt7.js";import{w as Q}from"./Point-WxyopZva.js";import{g as De}from"./MapView-DaoQedLH.js";import{P as Se}from"./PoiSearchV2-D7yNeLuv.js";import{s as ze,g as Ie}from"./FeatureHelper-Da16o0mu.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import{h as Re}from"./LayerHelper-Cn-iiqxI.js";import"./project-DUuzYgGl.js";import{i as He}from"./ViewHelper-BGCZjxXH.js";import{u as Ee}from"./useWidgets-BRE-VQU9.js";/* empty css                                                                      */import{a as Ne}from"./URLHelper-B9aplt5w.js";import{u as Fe}from"./useLayerList-DmEwJ-ws.js";import{u as Ue,a as qe}from"./useScaleBar-Beed-z91.js";import"./index-0NlGN6gS.js";import"./geometryEngineBase-BhsKaODW.js";const Ge={class:"arc-infowindow-wrapper"},Je={class:"content-wrapper"},Qe=U({__name:"PopLayout",props:{offsetx:{},offsety:{},title:{},width:{},height:{},maxHeight:{},maxWidth:{},showClose:{type:Boolean},highLight:{type:Boolean},visible:{type:Boolean},backgroundColor:{},status:{},showMore:{type:Boolean},showBack:{type:Boolean},theme:{},x:{},y:{}},emits:["highlight","more","back","toggled","refreshPosition"],setup(T,{expose:W,emit:D}){const h=O(),f=T,c=q({visible:!!f.visible}),v=D,S=()=>{c.visible=!0,C(),v("toggled",!0)},L=a=>{c.visible=a??!c.visible,c.visible&&C(),v("toggled",c.visible)},_=()=>{c.visible=!1,v("toggled",!1)},C=()=>{var a,n;(n=(a=h.value)==null?void 0:a.parentElement)==null||n.appendChild(h.value),v("highlight")},l=(a,n)=>{if(!h.value||!a||(n=n||e.value,!n))return;const P=new Q({x:n==null?void 0:n.x,y:n==null?void 0:n.y,spatialReference:a==null?void 0:a.spatialReference}),g=a==null?void 0:a.toScreen(P);h.value.style.left=((g==null?void 0:g.x)||-1e4)+(f.offsetx||0)+"px",h.value.style.top=((g==null?void 0:g.y)||-1e4)+(f.offsety||-10)+"px"},e=me(()=>({x:f.x,y:f.y}));return fe(()=>e.value,a=>{a&&v("refreshPosition")},{immediate:!0}),W({open:S,close:_,toggle:L,setPosition:l}),(a,n)=>{const P=G;return R((m(),E("div",{ref_key:"refContainer",ref:h,class:F(["arc-infowindow",[a.highLight?"active":"",f.theme]]),style:be({backgroundColor:a.backgroundColor})},[w("div",Ge,[w("div",{class:"title-wrapper",onClick:C},[w("span",{class:F(["title",a.status])},ve(a.title),3),a.showMore?(m(),y(P,{key:0,class:"btn btn-more",onClick:n[0]||(n[0]=I(g=>a.$emit("more"),["stop"]))},{default:k(()=>[b(d(ge))]),_:1})):M("",!0),a.showBack?(m(),y(P,{key:1,class:"btn btn-back",onClick:n[1]||(n[1]=I(g=>a.$emit("back"),["stop"]))},{default:k(()=>[b(d(he))]),_:1})):M("",!0),b(P,{class:"btn btn-close",onClick:I(_,["stop"])},{default:k(()=>[b(d(we))]),_:1})]),w("div",Je,[x(a.$slots,"default",{},void 0,!0)])])],6)),[[H,d(c).visible]])}}}),Xe=J(Qe,[["__scopeId","data-v-57ca6c32"]]),Ye={class:"pop-container"},Ze={class:"tools-temp-wrapper"},je={id:"overviewmap",class:"overviewmap"},Ke=U({__name:"Map",props:{fullContent:{type:Boolean},panelCustomClass:{},tools:{},pops:{},detailClose:{type:Boolean},detailMaxMin:{type:Boolean}},emits:["pipe-loaded","detail-closed","detail-opened","pop-more","pop-back","click","pop-toggle"],setup(T,{expose:W,emit:D}){const h=O(),f=O(),{proxy:c}=ye(),v=O(),S=O(),L=["areameasure","pipelength"],_=D,C=T,l=q({mounted:!1,showOverViewMap:!1,toolPanelTitle:"",toolPanelOperate:""}),e={},a=async(t,o,r,i)=>{var p,u;r?(u=f.value)==null||u.Close():(l.toolPanelOperate=t,l.toolPanelTitle=o,(p=f.value)==null||p.Open(),L.map(B=>{var $;B!==l.toolPanelOperate&&(($=c.$refs["refArcWidgetButton-tool-"+B])==null||$.toggle(!0))}))},n=()=>{L.map(t=>{var o;(o=c.$refs["refArcWidgetButton-tool-"+t])==null||o.toggle(!0)})},P=()=>{_("detail-opened")},g=()=>{_("detail-closed")},X=t=>{var r,i;if(!e.view||t.length!==2)return;const o=new De({geometry:new Q({longitude:t==null?void 0:t[0],latitude:t==null?void 0:t[1],spatialReference:e.view.spatialReference}),symbol:ze("picture",{url:Ne(),yOffset:-8})});(r=e.view)==null||r.graphics.removeAll(),(i=e.view)==null||i.graphics.add(o),Ie(e.view,o,{avoidHighlight:!0,zoom:16})},N=t=>{var o;(o=v.value)==null||o.Toggle(t)},Y=()=>{var t;return(t=v.value)==null?void 0:t.visible},Z=t=>{var o;(o=v.value)==null||o.toggleMaxMin(t)},j=t=>{var r;const o=c.$refs["refPop"+t.id];o!=null&&o[0]&&((r=o[0])==null||r.setPosition(e.view))},K=(t,o)=>{var p,u;V();const r=t.id;if(r===void 0)return;const i=c.$refs["refPop"+r];i!=null&&i[0]&&((p=i[0])==null||p.toggle(o),(u=i[0])==null||u.setPosition(e.view))},V=()=>{var t;(t=C.pops)==null||t.map(o=>{var p;const r=o==null?void 0:o.id;if(!r)return;const i=c.$refs["refPop"+r];i!=null&&i.length&&((p=i[0])==null||p.toggle(!1))})},ee=Ue(),te=qe(),oe=Fe(),{addCustomWidget:z}=Ee(),se=Oe(),ae=xe(),ie=Le(),re=Ae(),ne=Te(),le=async t=>(await $e().Auth(),e.view=He({el:h.value,...t}),await e.view.when(),e.view.watch("extent",()=>{var r;(r=C.pops)==null||r.map(i=>{var u;(u=c.$refs["refPop"+i.id][0])==null||u.setPosition(e.view)})}),(C.tools||["search","coordinate","pipe","scale","zoom","pipelength","area","print","legend","overviewmap","home","layerlist"]).map(r=>{if(e.view)switch(r){case"search":e.view.ui.add("tool-search-poi","top-right");break;case"layerlist":oe.init(e.view);break;case"coordinate":te.init(e.view);break;case"pipe":Re(e.view).then(i=>{_("pipe-loaded"),e.legend&&(e.legend.layerInfos=[{title:i.title,layer:i}])});break;case"scale":ee.init(e.view);break;case"zoom":ne.init(e.view,"bottom-right");break;case"pipelength":z(e.view,"tool-pipelength","bottom-right");break;case"area":z(e.view,"tool-areameasure","bottom-right");break;case"print":se.init(e.view,"","bottom-right");break;case"overviewmap":re.init(e.view,"overviewmap"),z(e.view,"gis-overview","bottom-right");break;case"home":e.homeBar=ae.init(e.view);break;case"legend":e.legend=ie.init(e.view,"botom-right",[]);break}}),e.view),ce=()=>{var t,o,r,i,p,u,B;(t=e.view)!=null&&t.destroy||((o=e.homeBar)==null||o.destroy(),(r=e.view)==null||r.map.removeHandles(),(i=e.view)==null||i.removeHandles(),(p=e.view)==null||p.map.removeAll(),(u=e.view)==null||u.map.destroy(),(B=e.view)==null||B.destroy(),e.view=void 0)};return ke(()=>{l.mounted=!0}),_e(()=>{N(!1)}),W({init:le,destroy:ce,toggleDetail:N,toggleDetailMaxmin:Z,isDetailOpened:Y}),(t,o)=>{const r=Xe,i=We,p=G,u=de,B=ue,$=pe;return m(),E("div",{id:"arcmap-wrapper",ref_key:"refMapWrapper",ref:S,class:"map-wrapper",onClick:o[3]||(o[3]=s=>_("click",s))},[w("div",{id:"viewDiv",ref_key:"refMap",ref:h,class:"viewDiv"},null,512),w("div",Ye,[(m(!0),E(Ce,null,Pe(t.pops,s=>R((m(),y(r,{key:s.id,ref_for:!0,ref:"refPop"+s.id,title:s.title,"show-more":s.showMore,"show-back":s.showBack,status:s.status,"background-color":s.bgColor,x:s.x,y:s.y,latitude:s.latitude,longitude:s.longitude,offsetx:s.offsetX,offsety:s.offsetY,onMore:A=>t.$emit("pop-more",s),onBack:A=>t.$emit("pop-back",s),onToggle:A=>K(s,A),onRefreshPosition:A=>j(s)},{default:k(()=>[x(t.$slots,"pop-default",{config:s},()=>[(m(),y(Be(s.customComponent),{visible:s.visible,config:s.customConfig},null,8,["visible","config"]))],!0)]),_:2},1032,["title","show-more","show-back","status","background-color","x","y","latitude","longitude","offsetx","offsety","onMore","onBack","onToggle","onRefreshPosition"])),[[H,s.visible]])),128))]),b(Se,{id:"tool-search-poi",onChange:X}),w("div",Ze,[b(i,{id:"tool-pipelength",ref:"refArcWidgetButton-tool-pipelength",icon:"mdi:ruler",title:"管线长度",onClick:o[0]||(o[0]=s=>a("pipelength","管线长度测量",s))},null,512),b(i,{id:"tool-areameasure",ref:"refArcWidgetButton-tool-areameasure",icon:"gis:measure-area-alt",title:"面积测量",onClick:o[1]||(o[1]=s=>a("areameasure","面积测量",s))},null,512),w("div",{id:"gis-overview",class:"esri-widget esri-expand esri-component esri-widget--button custom-toolbar",onClick:o[2]||(o[2]=()=>d(l).showOverViewMap=!d(l).showOverViewMap)},[b(p,{size:16,class:"tool-icon"},{default:k(()=>[b(d(Me),{icon:d(l).showOverViewMap?"ep:d-arrow-right":"mdi:earth"},null,8,["icon"])]),_:1})])]),R(w("div",je,null,512),[[H,d(l).showOverViewMap]]),d(l).mounted?(m(),y($,{key:0,ref_key:"refToolPanel",ref:f,"custom-class":"tool-panel",telport:"#arcmap-wrapper",title:d(l).toolPanelTitle,"destroy-by-close":!0,"before-close":()=>n()},{default:k(()=>[d(l).toolPanelOperate==="pipelength"?(m(),y(u,{key:0,view:e.view},null,8,["view"])):M("",!0),d(l).toolPanelOperate==="areameasure"?(m(),y(B,{key:1,view:e.view},null,8,["view"])):M("",!0)]),_:1},8,["title","before-close"])):M("",!0),d(l).mounted?(m(),y($,{key:1,ref_key:"refPanel",ref:v,"custom-class":t.panelCustomClass||"gis-detail-panel",telport:"#arcmap-wrapper",draggable:!1,"full-content":t.fullContent,"destroy-by-close":!0,"after-open":P,"show-close":t.detailClose??!0,"max-min":t.detailMaxMin??!0,"before-close":g},{header:k(()=>[x(t.$slots,"detail-header",{},void 0,!0)]),default:k(()=>[x(t.$slots,"detail-default",{},void 0,!0)]),_:3},8,["custom-class","full-content","show-close","max-min"])):M("",!0),x(t.$slots,"map-bars",{},void 0,!0)],512)}}}),kt=J(Ke,[["__scopeId","data-v-bad79371"]]);export{kt as M};
