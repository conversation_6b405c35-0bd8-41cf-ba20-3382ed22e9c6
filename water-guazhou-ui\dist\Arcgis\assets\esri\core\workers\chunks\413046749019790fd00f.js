"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[908],{84552:(e,t,r)=>{r.d(t,{Z:()=>y});var i=r(43697),s=r(2368),n=r(96674),o=r(35463),a=r(5600),l=(r(75215),r(67676),r(36030)),p=r(52011),u=r(78981);let d=class extends((0,s.J)(n.wq)){constructor(e){super(e),this.unit="milliseconds",this.value=0}toMilliseconds(){return(0,o.rJ)(this.value,this.unit,"milliseconds")}};(0,i._)([(0,l.J)(u.v,{nonNullable:!0})],d.prototype,"unit",void 0),(0,i._)([(0,a.Cb)({type:Number,json:{write:!0},nonNullable:!0})],d.prototype,"value",void 0),d=(0,i._)([(0,p.j)("esri.TimeInterval")],d);const y=d},3920:(e,t,r)=>{r.d(t,{p:()=>p,r:()=>u});var i=r(43697),s=r(15923),n=r(61247),o=r(5600),a=r(52011),l=r(72762);const p=e=>{let t=class extends e{destroy(){this.destroyed||(this._get("handles")?.destroy(),this._get("updatingHandles")?.destroy())}get handles(){return this._get("handles")||new n.Z}get updatingHandles(){return this._get("updatingHandles")||new l.t}};return(0,i._)([(0,o.Cb)({readOnly:!0})],t.prototype,"handles",null),(0,i._)([(0,o.Cb)({readOnly:!0})],t.prototype,"updatingHandles",null),t=(0,i._)([(0,a.j)("esri.core.HandleOwner")],t),t};let u=class extends(p(s.Z)){};u=(0,i._)([(0,a.j)("esri.core.HandleOwner")],u)},68668:(e,t,r)=>{r.d(t,{G:()=>a,w:()=>l});var i=r(66643),s=r(46791),n=r(83379),o=r(70586);async function a(e,t){return await e.load(),l(e,t)}async function l(e,t){const r=[],a=(...e)=>{for(const t of e)(0,o.Wi)(t)||(Array.isArray(t)?a(...t):s.Z.isCollection(t)?t.forEach((e=>a(e))):n.Z.isLoadable(t)&&r.push(t))};t(a);let l=null;if(await(0,i.UI)(r,(async e=>{const t=await(0,i.q6)(function(e){return"loadAll"in e&&"function"==typeof e.loadAll}(e)?e.loadAll():e.load());!1!==t.ok||l||(l=t)})),l)throw l.error;return e}},42033:(e,t,r)=>{r.d(t,{E:()=>s,_:()=>n});var i=r(70586);async function s(e,t){const{WhereClause:i}=await r.e(1534).then(r.bind(r,41534));return i.create(e,t)}function n(e,t){return(0,i.pC)(e)?(0,i.pC)(t)?`(${e}) AND (${t})`:e:t}},72762:(e,t,r)=>{r.d(t,{t:()=>d});var i=r(43697),s=r(15923),n=r(61247),o=r(70586),a=r(17445),l=r(1654),p=r(5600),u=r(52011);let d=class extends s.Z{constructor(){super(...arguments),this.updating=!1,this._handleId=0,this._handles=new n.Z,this._scheduleHandleId=0,this._pendingPromises=new Set}destroy(){this.removeAll(),this._handles.destroy()}add(e,t,r={}){return this._installWatch(e,t,r,a.YP)}addWhen(e,t,r={}){return this._installWatch(e,t,r,a.gx)}addOnCollectionChange(e,t,{initial:r=!1,final:i=!1}={}){const s=++this._handleId;return this._handles.add([(0,a.on)(e,"after-changes",this._createSyncUpdatingCallback(),a.Z_),(0,a.on)(e,"change",t,{onListenerAdd:r?e=>t({added:e.toArray(),removed:[]}):void 0,onListenerRemove:i?e=>t({added:[],removed:e.toArray()}):void 0})],s),{remove:()=>this._handles.remove(s)}}addPromise(e){if((0,o.Wi)(e))return e;const t=++this._handleId;this._handles.add({remove:()=>{this._pendingPromises.delete(e)&&(0!==this._pendingPromises.size||this._handles.has(y)||this._set("updating",!1))}},t),this._pendingPromises.add(e),this._set("updating",!0);const r=()=>this._handles.remove(t);return e.then(r,r),e}removeAll(){this._pendingPromises.clear(),this._handles.removeAll(),this._set("updating",!1)}_installWatch(e,t,r={},i){const s=++this._handleId;r.sync||this._installSyncUpdatingWatch(e,s);const n=i(e,t,r);return this._handles.add(n,s),{remove:()=>this._handles.remove(s)}}_installSyncUpdatingWatch(e,t){const r=this._createSyncUpdatingCallback(),i=(0,a.YP)(e,r,{sync:!0,equals:()=>!1});return this._handles.add(i,t),i}_createSyncUpdatingCallback(){return()=>{this._handles.remove(y),++this._scheduleHandleId;const e=this._scheduleHandleId;this._get("updating")||this._set("updating",!0),this._handles.add((0,l.Os)((()=>{e===this._scheduleHandleId&&(this._set("updating",this._pendingPromises.size>0),this._handles.remove(y))})),y)}}};(0,i._)([(0,p.Cb)({readOnly:!0})],d.prototype,"updating",void 0),d=(0,i._)([(0,u.j)("esri.core.support.WatchUpdatingTracking")],d);const y=-42},30359:(e,t,r)=>{r.r(t),r.d(t,{default:()=>Ie});var i=r(43697),s=r(46791),n=r(20102),o=r(3920),a=r(61247),l=r(68668),p=r(70586),u=r(16453),d=r(95330),y=r(17445),c=r(42033),h=r(17452),m=r(5600),b=(r(75215),r(67676),r(71715)),f=r(52011),g=r(31263),v=r(87085),w=r(54295),_=r(17287),C=r(71612),S=r(17017),F=r(66361),I=r(53713),T=r(38009),x=r(16859),O=r(34760),j=r(72965),E=r(28294),A=r(66677),P=r(21506),L=r(50957),R=r(53518),Z=r(35671),U=r(96674),D=r(30556),Q=r(72729);let k=class extends U.wq{constructor(){super(...arguments),this.code=null,this.defaultValues={},this.domains=null,this.name=null}readDomains(e){if(!e)return null;const t={};for(const r of Object.keys(e))t[r]=(0,Q.im)(e[r]);return t}writeDomains(e,t){if(!e)return;const r={};for(const t of Object.keys(e))e[t]&&(r[t]=e[t]?.toJSON());t.domains=r}};(0,i._)([(0,m.Cb)({type:Number,json:{write:!0}})],k.prototype,"code",void 0),(0,i._)([(0,m.Cb)({type:Object,json:{write:!0}})],k.prototype,"defaultValues",void 0),(0,i._)([(0,m.Cb)({json:{write:!0}})],k.prototype,"domains",void 0),(0,i._)([(0,b.r)("domains")],k.prototype,"readDomains",null),(0,i._)([(0,D.c)("domains")],k.prototype,"writeDomains",null),(0,i._)([(0,m.Cb)({type:String,json:{write:!0}})],k.prototype,"name",void 0),k=(0,i._)([(0,f.j)("esri.layers.support.Subtype")],k);const M=k;var q=r(51773),N=r(16050),G=(r(12501),r(28756),r(92271),r(72529),r(5499)),z=r(84382),H=r(81571),V=(r(91423),r(80442),r(10699)),W=r(22974),B=r(83379),$=r(92604),J=r(78286),K=r(63213),Y=r(55343),X=r(70082),ee=r(54306),te=r(30707),re=r(14165),ie=r(32163),se=r(28412);const ne=["charts","editingEnabled","formTemplate","labelsVisible","labelingInfo","legendEnabled","minScale","maxScale","opacity","popupEnabled","popupTemplate","renderer","subtypeCode","templates","title","visible"],oe={key:"type",base:G.Z,errorContext:"renderer",typeMap:{simple:z.Z,"unique-value":H.Z,"class-breaks":N.Z}},ae=(0,R.v)(),le=(0,K.d)({types:oe});let pe=0;function ue(e){const t=e.json.write;return"object"==typeof t?t.ignoreOrigin=!0:e.json.write={ignoreOrigin:!0},e}function de(e){switch(e){case"point":case"multipoint":return se.xA.clone();case"polyline":return se.CJ.clone();case"polygon":case"multipatch":return se.z3.clone();default:return null}}function ye(e,t){return null==e?null:t.subtypes?.find((t=>t.code===e))}function ce(e,t){let r=null;switch(t.geometryType){case"esriGeometryPoint":case"esriGeometryMultipoint":r="point";break;case"esriGeometryPolyline":r="line";break;case"esriGeometryPolygon":case"esriGeometryMultiPatch":r="polygon";break;default:t.type,r=null}const i={},s=ye(e,t);if((0,p.pC)(s)){const{defaultValues:e}=s;for(const t in e)i[t]=e[t]}return i[t.subtypeField]=e,new X.Z({name:"New Feature",drawingTool:r,prototype:{attributes:i}})}const he="esri.layers.support.SubtypeSublayer";let me=class extends((0,o.p)((0,u.R)((0,V.IG)(B.Z)))){constructor(e){super(e),this.charts=null,this.editingEnabled=!0,this.fieldOverrides=null,this.fieldsIndex=null,this.formTemplate=null,this.id=`${Date.now().toString(16)}-subtype-sublayer-${pe++}`,this.type="subtype-sublayer",this.labelsVisible=!0,this.labelingInfo=null,this.layerType="ArcGISFeatureLayer",this.legendEnabled=!0,this.listMode="show",this.minScale=0,this.maxScale=0,this.opacity=1,this.popupEnabled=!0,this.popupTemplate=null,this.subtypeCode=null,this.templates=null,this.title=null,this.visible=!0}get capabilities(){return this.parent?.capabilities}get effectiveCapabilities(){return this.parent?.effectiveCapabilities}get effectiveEditingEnabled(){const{parent:e}=this;return e?e.effectiveEditingEnabled&&this.editingEnabled:this.editingEnabled}get elevationInfo(){return this.parent?.elevationInfo}writeFieldOverrides(e,t,r){const{fields:i,parent:s}=this;let n;if(i){n=[];let e=0;i.forEach((({name:t,alias:r,editable:i,visible:o})=>{if(!o)return;const a=s?.fields?.find((e=>e.name===t));if(!a)return;const l={name:t};let p=!1;r!==a.alias&&(l.alias=r,p=!0),i!==a.editable&&(l.editable=i,p=!0),n.push(l),p&&e++})),0===e&&n.length===i.length&&(n=null)}else n=(0,W.d9)(e);n?.length&&(0,J.RB)(r,n,t)}get fields(){const{parent:e,fieldOverrides:t,subtypeCode:r}=this,i=e?.fields;if(!e||!i?.length)return null;const{subtypes:s,subtypeField:n}=e,o=s?.find((e=>e.code===r)),a=o?.defaultValues,l=o?.domains,p=[];for(const e of i){const i=e.clone(),{name:s}=i,o=t?.find((e=>e.name===s));if(i.visible=!t||!!o,o){const{alias:e,editable:t}=o;e&&(i.alias=e),!1===t&&(i.editable=!1)}const u=a?.[s]??null;i.defaultValue=s===n?r:u;const d=l?.[s]??null;i.domain=s===n?null:d?"inherited"===d.type?i.domain:d.clone():null,p.push(i)}return p}get geometryType(){return this.parent?.geometryType}get effectiveScaleRange(){const{minScale:e,maxScale:t}=this;return{minScale:e,maxScale:t}}get objectIdField(){return this.parent||$.Z.getLogger(he).error(fe("objectIdField")),this.parent?.objectIdField}get defaultPopupTemplate(){return this.createPopupTemplate()}set renderer(e){(0,Z.YN)(e,this.fieldsIndex),this._override("renderer",e)}get renderer(){if(this._isOverridden("renderer"))return this._get("renderer");const{parent:e}=this;return e&&!e.isTable&&"mesh"!==e.geometryType?function(e){return new z.Z({symbol:de(e)})}(e.geometryType):null}readRendererFromService(e,t,r){if("Table"===t.type)return null;const i=t.drawingInfo?.renderer,s=le(i,t,r);let n;const{subtypeCode:o}=this;if(null!=o&&function(e,t){return!(!t||"unique-value"!==e?.type||"string"!=typeof e.field||e.field.toLowerCase()!==t.toLowerCase()||e.field2||e.field3||e.valueExpression)}(s,t.subtypeField)){const e=s.uniqueValueInfos?.find((({value:e})=>(e="number"==typeof e?String(e):e)===String(o)));e&&(n=new z.Z({symbol:e.symbol}))}else"simple"!==s?.type||s.visualVariables?.length||(n=s);return n}readRenderer(e,t,r){const i=t?.layerDefinition?.drawingInfo?.renderer;if(!i)return;const s=i.visualVariables?.some((e=>"rotationInfo"!==e.type));return s?void 0:le(i,t,r)||void 0}get spatialReference(){return this.parent?.spatialReference}readTemplatesFromService(e,t){return[ce(this.subtypeCode,t)]}readTitleFromService(e,t){const r=ye(this.subtypeCode,t);return(0,p.pC)(r)?r.name:null}get url(){return this.parent?.url}get userHasUpdateItemPrivileges(){return!!this.parent?.userHasUpdateItemPrivileges}async addAttachment(e,t){const{parent:r}=this;if(!r)throw fe("addAttachment");if(e.getAttribute(r.subtypeField)!==this.subtypeCode)throw new n.Z("subtype-sublayer:addAttachment","The feature provided does not belong to this SubtypeSublayer");return r.addAttachment(e,t)}async updateAttachment(e,t,r){const{parent:i}=this;if(!i)throw fe("updateAttachment");if(e.getAttribute(i.subtypeField)!==this.subtypeCode)throw new n.Z("subtype-sublayer:updateAttachment","The feature provided does not belong to this SubtypeSublayer");return i.updateAttachment(e,t,r)}async deleteAttachments(e,t){const{parent:r}=this;if(!r)throw fe("deleteAttachments");if(e.getAttribute(r.subtypeField)!==this.subtypeCode)throw new n.Z("subtype-sublayer:deleteAttachments","The feature provided does not belong to this SubtypeSublayer");return r.deleteAttachments(e,t)}async applyEdits(e,t){if(!this.parent)throw fe("applyEdits");return this.parent.applyEdits(e,t)}createPopupTemplate(e){let t=this;const{parent:r,fields:i,title:s}=this;if(r){const{displayField:e,editFieldsInfo:n,objectIdField:o}=r;t={displayField:e,editFieldsInfo:n,fields:i,objectIdField:o,title:s}}return(0,ie.eZ)(t,e)}createQuery(){if(!this.parent)throw fe("createQuery");const e=(0,L.rP)(this.parent),t=`${this.parent.subtypeField}=${this.subtypeCode}`;return e.where=(0,c._)(t,this.parent.definitionExpression),e}getField(e){return this.fieldsIndex.get(e)}getFieldDomain(e){return this._getLayerDomain(e)}hasUserOverrides(){return ne.some((e=>this.originIdOf(e)===g.s3.USER))}async queryAttachments(e,t){const r=await this.load();if(!r.parent)throw fe("queryAttachments");const i=e.clone();return i.where=be(i.where,r.parent.subtypeField,r.subtypeCode),r.parent.queryAttachments(e,t)}async queryFeatures(e,t){const r=await this.load();if(!r.parent)throw fe("queryFeatures");const i=re.Z.from(e)??r.createQuery();return(0,p.pC)(e)&&(i.where=be(i.where,r.parent.subtypeField,r.subtypeCode)),r.parent.queryFeatures(i,t)}_getLayerDomain(e){const t=this.fieldsIndex.get(e);return t?t.domain:null}};(0,i._)([(0,m.Cb)({readOnly:!0,json:{read:!1}})],me.prototype,"capabilities",null),(0,i._)([(0,m.Cb)({readOnly:!0,json:{read:!1}})],me.prototype,"effectiveCapabilities",null),(0,i._)([(0,m.Cb)({json:{write:{ignoreOrigin:!0}}})],me.prototype,"charts",void 0),(0,i._)([(0,m.Cb)({type:Boolean,nonNullable:!0,json:{name:"enableEditing",write:{ignoreOrigin:!0}}})],me.prototype,"editingEnabled",void 0),(0,i._)([(0,m.Cb)({type:Boolean,readOnly:!0})],me.prototype,"effectiveEditingEnabled",null),(0,i._)([(0,m.Cb)({readOnly:!0,json:{read:!1}})],me.prototype,"elevationInfo",null),(0,i._)([(0,m.Cb)({readOnly:!0,json:{name:"layerDefinition.fieldOverrides",origins:{service:{read:!1}},write:{ignoreOrigin:!0,allowNull:!0}}})],me.prototype,"fieldOverrides",void 0),(0,i._)([(0,D.c)("fieldOverrides")],me.prototype,"writeFieldOverrides",null),(0,i._)([(0,m.Cb)({...ae.fields,readOnly:!0,json:{read:!1}})],me.prototype,"fields",null),(0,i._)([(0,m.Cb)(ae.fieldsIndex)],me.prototype,"fieldsIndex",void 0),(0,i._)([(0,m.Cb)({type:Y.Z,json:{name:"formInfo",write:{ignoreOrigin:!0}}})],me.prototype,"formTemplate",void 0),(0,i._)([(0,m.Cb)({type:String,readOnly:!0,json:{origins:{service:{read:!1}},write:{ignoreOrigin:!0}}})],me.prototype,"id",void 0),(0,i._)([(0,m.Cb)({readOnly:!0,json:{read:!1}})],me.prototype,"geometryType",null),(0,i._)([(0,m.Cb)({readOnly:!0,json:{read:!1}})],me.prototype,"type",void 0),(0,i._)([(0,m.Cb)(ue((0,W.d9)(P.iR)))],me.prototype,"labelsVisible",void 0),(0,i._)([(0,m.Cb)({type:[ee.Z],json:{name:"layerDefinition.drawingInfo.labelingInfo",origins:{service:{read:!1}},read:{reader:te.r},write:{ignoreOrigin:!0}}})],me.prototype,"labelingInfo",void 0),(0,i._)([(0,m.Cb)({type:["ArcGISFeatureLayer"],readOnly:!0,json:{read:!1,write:{ignoreOrigin:!0}}})],me.prototype,"layerType",void 0),(0,i._)([(0,m.Cb)(ue((0,W.d9)(P.rn)))],me.prototype,"legendEnabled",void 0),(0,i._)([(0,m.Cb)({type:["show","hide"]})],me.prototype,"listMode",void 0),(0,i._)([(0,m.Cb)((()=>{const e=(0,W.d9)(P.rO);return e.json.origins.service.read=!1,ue(e)})())],me.prototype,"minScale",void 0),(0,i._)([(0,m.Cb)((()=>{const e=(0,W.d9)(P.u1);return e.json.origins.service.read=!1,ue(e)})())],me.prototype,"maxScale",void 0),(0,i._)([(0,m.Cb)({readOnly:!0})],me.prototype,"effectiveScaleRange",null),(0,i._)([(0,m.Cb)({readOnly:!0,json:{read:!1}})],me.prototype,"objectIdField",null),(0,i._)([(0,m.Cb)({type:Number,range:{min:0,max:1},nonNullable:!0,json:{write:{ignoreOrigin:!0}}})],me.prototype,"opacity",void 0),(0,i._)([(0,m.Cb)()],me.prototype,"parent",void 0),(0,i._)([(0,m.Cb)(ue((0,W.d9)(P.C_)))],me.prototype,"popupEnabled",void 0),(0,i._)([(0,m.Cb)({type:q.Z,json:{name:"popupInfo",write:{ignoreOrigin:!0}}})],me.prototype,"popupTemplate",void 0),(0,i._)([(0,m.Cb)({readOnly:!0})],me.prototype,"defaultPopupTemplate",null),(0,i._)([(0,m.Cb)({types:oe,json:{write:{target:"layerDefinition.drawingInfo.renderer",ignoreOrigin:!0}}})],me.prototype,"renderer",null),(0,i._)([(0,b.r)("service","renderer",["drawingInfo.renderer","subtypeField","type"])],me.prototype,"readRendererFromService",null),(0,i._)([(0,b.r)("renderer",["layerDefinition.drawingInfo.renderer"])],me.prototype,"readRenderer",null),(0,i._)([(0,m.Cb)({readOnly:!0,json:{read:!1}})],me.prototype,"spatialReference",null),(0,i._)([(0,m.Cb)({type:Number,json:{origins:{service:{read:!1}},write:{ignoreOrigin:!0}}})],me.prototype,"subtypeCode",void 0),(0,i._)([(0,m.Cb)({type:[X.Z],json:{name:"layerDefinition.templates",write:{ignoreOrigin:!0}}})],me.prototype,"templates",void 0),(0,i._)([(0,b.r)("service","templates",["geometryType","subtypeField","subtypes","type"])],me.prototype,"readTemplatesFromService",null),(0,i._)([(0,m.Cb)({type:String,json:{write:{ignoreOrigin:!0}}})],me.prototype,"title",void 0),(0,i._)([(0,b.r)("service","title",["subtypes"])],me.prototype,"readTitleFromService",null),(0,i._)([(0,m.Cb)({readOnly:!0,json:{read:!1}})],me.prototype,"url",null),(0,i._)([(0,m.Cb)({readOnly:!0})],me.prototype,"userHasUpdateItemPrivileges",null),(0,i._)([(0,m.Cb)({type:Boolean,nonNullable:!0,json:{name:"visibility",write:{ignoreOrigin:!0}}})],me.prototype,"visible",void 0),me=(0,i._)([(0,f.j)(he)],me);const be=(e,t,r)=>{const i=new RegExp(`${t}=[0-9]`),s=`${t}=${r}`,n=(0,p.Pt)(e,"");return i.test(n)?n.replace(i,s):(0,c._)(s,n)},fe=e=>new n.Z(`This sublayer must have a parent SubtypeGroupLayer in order to use ${e}`),ge=me;var ve=r(76259),we=r(60199);const _e="SubtypeGroupLayer";function Ce(e,t){return new n.Z("layer:unsupported",`Layer (${e.title}, ${e.id}) of type '${e.declaredClass}' ${t}`,{layer:e})}const Se=(0,R.v)();let Fe=class extends((0,I.B)((0,F.o1)((0,C.h)((0,E.n)((0,j.M)((0,O.Q)((0,_.Y)((0,T.q)((0,x.I)((0,u.R)((0,S.N)((0,w.V)((0,o.p)(v.Z)))))))))))))){constructor(...e){super(...e),this._handles=new a.Z,this._sublayersCollectionChanged=!1,this._sublayerLookup=new Map,this.fields=null,this.fieldsIndex=null,this.outFields=null,this.subtypes=null,this.sublayers=new(s.Z.ofType(ge)),this.timeInfo=null,this.title="Layer",this.type="subtype-group",this.addHandles((0,y.YP)((()=>this.sublayers),((e,t)=>this._handleSublayersChange(e,t)),y.Z_))}destroy(){this.source?.destroy(),this._handles=(0,p.SC)(this._handles)}normalizeCtorArgs(e,t){return"string"==typeof e?{url:e,...t}:e}load(e){const t=(0,p.pC)(e)?e.signal:null,r=this.loadFromPortal({supportedTypes:["Feature Service"]},e).catch(d.r9).then((async()=>{if(!this.url)throw new n.Z("subtype-grouplayer:missing-url-or-source","SubtypeGroupLayer must be created with either a url or a portal item");if(null==this.layerId)throw new n.Z("subtype-grouplayer:missing-layerid","layerId is required for a SubtypeGroupLayer created with url");return this._initLayerProperties(await this.createGraphicsSource(t))})).then((()=>this._setUserPrivileges(this.serviceItemId,e))).then((()=>(0,L.nU)(this,e)));return this.addResolvingPromise(r),Promise.resolve(this)}get createQueryVersion(){return this.commitProperty("definitionExpression"),this.commitProperty("timeExtent"),this.commitProperty("timeOffset"),this.commitProperty("geometryType"),this.commitProperty("gdbVersion"),this.commitProperty("historicMoment"),this.commitProperty("returnZ"),this.commitProperty("capabilities"),this.commitProperty("returnM"),(this._get("createQueryVersion")??0)+1}get editingEnabled(){return this.loaded&&null!=this.capabilities&&this.capabilities.operations.supportsEditing&&this.userHasEditingPrivileges}get effectiveEditingEnabled(){return(0,L.sX)(this)}get parsedUrl(){const e=(0,h.mN)(this.url);return null!=e&&null!=this.layerId&&(e.path=(0,h.v_)(e.path,this.layerId.toString())),e}set source(e){this._get("source")!==e&&this._set("source",e)}readTitleFromService(e,{name:t}){return this.url?(0,A.a7)(this.url,t):t}async addAttachment(e,t){return(0,L.JD)(this,e,t,_e)}async updateAttachment(e,t,r){return(0,L.Y5)(this,e,t,r,_e)}async applyEdits(e,t){return(0,L.Jj)(this,e,t)}on(e,t){return super.on(e,t)}async createGraphicsSource(e){const{default:t}=await(0,d.Hl)(Promise.all([r.e(4599),r.e(9771)]).then(r.bind(r,6502)),e);return new t({layer:this}).load({signal:e})}createQuery(){const e=(0,L.rP)(this),t=this.sublayers.map((e=>e.subtypeCode));return e.where=(0,c._)(`${this.subtypeField} IN (${t.join(",")})`,this.definitionExpression),e}async deleteAttachments(e,t){return(0,L.FV)(this,e,t,_e)}async fetchRecomputedExtents(e){return(0,L.Ci)(this,e,_e)}getFieldDomain(e,t){return this._getLayerDomain(e)}getField(e){return this.fieldsIndex.get(e)}findSublayerForFeature(e){const t=this.fieldsIndex.get(this.subtypeField),r=e.attributes[t.name];return this._sublayerLookup.get(r)}loadAll(){return(0,l.G)(this,(e=>{e(this.sublayers)}))}async queryAttachments(e,t){return(0,L.SU)(this,e,t,_e)}async queryFeatures(e,t){const r=await this.load(),i=re.Z.from(e)??r.createQuery(),s=(0,p.Pt)(i.outFields,[]);s.includes(this.subtypeField)||(s.push(this.subtypeField),i.outFields=s);const n=await r.source.queryFeatures(i,t);if(n?.features)for(const e of n.features)e.layer=e.sourceLayer=this.findSublayerForFeature(e);return n}async queryObjectIds(e,t){return(0,L.tD)(this,e,t,_e)}async queryFeatureCount(e,t){return(0,L.VG)(this,e,t,_e)}async queryExtent(e,t){return(0,L.KE)(this,e,t,_e)}async queryRelatedFeatures(e,t){return(0,L.kp)(this,e,t,_e)}async queryRelatedFeaturesCount(e,t){return(0,L.C9)(this,e,t,_e)}write(e,t){const{origin:r,layerContainerType:i,messages:s}=t;if(this.isTable){if("web-scene"===r||"web-map"===r&&"tables"!==i)return s?.push(Ce(this,"using a table source cannot be written to web scenes and web maps")),null}else if(this.loaded&&"web-map"===r&&"tables"===i)return s?.push(Ce(this,"using a non-table source cannot be written to tables in web maps")),null;return this.sublayers?.length?super.write(e,t):(s?.push(new n.Z("web-document-write:invalid-property",`Layer (${this.title}, ${this.id}) of type '${this.declaredClass}' has invalid value for 'sublayers' property. 'sublayers' collection should contain at least one sublayer`,{layer:this})),null)}serviceSupportsSpatialReference(e){return!!this.loaded&&(0,we.D)(this,e)}_getLayerDomain(e){const t=this.fieldsIndex.get(e);return t?t.domain:null}async _initLayerProperties(e){this._set("source",e);const{sourceJSON:t}=e;if(t&&(this.sourceJSON=t,this.read(t,{origin:"service",url:this.parsedUrl})),this.isTable)throw new n.Z("subtype-grouplayer:unsupported-source","SubtypeGroupLayer cannot be created using a layer with table source");if(!this.subtypes?.length)throw new n.Z("subtype-grouplayer:missing-subtypes","SubtypeGroupLayer must be created using a layer with subtypes");this._verifyFields(),(0,Z.UF)(this.timeInfo,this.fieldsIndex)}async hasDataChanged(){return(0,L.gG)(this)}_verifyFields(){const e=this.parsedUrl?.path??"undefined";this.objectIdField||console.log("SubtypeGroupLayer: 'objectIdField' property is not defined (url: "+e+")"),this.isTable||-1!==e.search(/\/FeatureServer\//i)||this.fields?.some((e=>"geometry"===e.type))||console.log("SubtypeGroupLayer: unable to find field of type 'geometry' in the layer 'fields' list. If you are using a map service layer, features will not have geometry (url: "+e+")")}_handleSublayersChange(e,t){t&&(t.forEach((e=>{e.parent=null})),this.handles.remove("sublayers-owner"),this._sublayerLookup.clear()),e&&(e.forEach((e=>{e.parent=this,this._sublayerLookup.set(e.subtypeCode,e)})),this._sublayersCollectionChanged=!1,this.handles.add([e.on("after-add",(({item:e})=>{e.parent=this,this._sublayerLookup.set(e.subtypeCode,e)})),e.on("after-remove",(({item:e})=>{e.parent=null,this._sublayerLookup.delete(e.subtypeCode)})),e.on("after-changes",(()=>{this._sublayersCollectionChanged=!0}))],"sublayers-owner"))}};(0,i._)([(0,m.Cb)({readOnly:!0})],Fe.prototype,"createQueryVersion",null),(0,i._)([(0,m.Cb)({readOnly:!0})],Fe.prototype,"editingEnabled",null),(0,i._)([(0,m.Cb)({readOnly:!0})],Fe.prototype,"effectiveEditingEnabled",null),(0,i._)([(0,m.Cb)({...Se.fields,readOnly:!0,json:{origins:{service:{read:!0}},read:!1}})],Fe.prototype,"fields",void 0),(0,i._)([(0,m.Cb)(Se.fieldsIndex)],Fe.prototype,"fieldsIndex",void 0),(0,i._)([(0,m.Cb)(P.id)],Fe.prototype,"id",void 0),(0,i._)([(0,m.Cb)({type:["show","hide","hide-children"]})],Fe.prototype,"listMode",void 0),(0,i._)([(0,m.Cb)({value:"SubtypeGroupLayer",type:["SubtypeGroupLayer"]})],Fe.prototype,"operationalLayerType",void 0),(0,i._)([(0,m.Cb)(Se.outFields)],Fe.prototype,"outFields",void 0),(0,i._)([(0,m.Cb)({readOnly:!0})],Fe.prototype,"parsedUrl",null),(0,i._)([(0,m.Cb)()],Fe.prototype,"source",null),(0,i._)([(0,m.Cb)({type:[M],readOnly:!0,json:{read:!1,origins:{service:{read:!0}}}})],Fe.prototype,"subtypes",void 0),(0,i._)([(0,m.Cb)({type:s.Z.ofType(ge),json:{origins:{service:{read:{source:"subtypes",reader:(e,t,r)=>{const i=e.map((({code:e})=>{const i=new ge({subtypeCode:e});return i.read(t,r),i}));return new(s.Z.ofType(ge))(i)}}}},name:"layers",write:{overridePolicy(e,t,r){const i=this.originOf("sublayers"),s=g.s3.PORTAL_ITEM;let n=!0;if((0,g.M9)(i)===s&&(0,g.M9)(r.origin)>s){const t=e.some((e=>e.hasUserOverrides()));n=this._sublayersCollectionChanged||t}return{enabled:n,ignoreOrigin:!0}}}}})],Fe.prototype,"sublayers",void 0),(0,i._)([(0,m.Cb)({type:ve.Z})],Fe.prototype,"timeInfo",void 0),(0,i._)([(0,m.Cb)({json:{origins:{"portal-item":{write:{ignoreOrigin:!0,writerEnsuresNonNull:!0}}}}})],Fe.prototype,"title",void 0),(0,i._)([(0,b.r)("service","title",["name"])],Fe.prototype,"readTitleFromService",null),(0,i._)([(0,m.Cb)({json:{read:!1}})],Fe.prototype,"type",void 0),Fe=(0,i._)([(0,f.j)("esri.layers.SubtypeGroupLayer")],Fe);const Ie=Fe},54295:(e,t,r)=>{r.d(t,{V:()=>o});var i=r(43697),s=r(5600),n=(r(75215),r(67676),r(52011));const o=e=>{let t=class extends e{get apiKey(){return this._isOverridden("apiKey")?this._get("apiKey"):"portalItem"in this?this.portalItem?.apiKey:null}set apiKey(e){null!=e?this._override("apiKey",e):(this._clearOverride("apiKey"),this.clear("apiKey","user"))}};return(0,i._)([(0,s.Cb)({type:String})],t.prototype,"apiKey",null),t=(0,i._)([(0,n.j)("esri.layers.mixins.APIKeyMixin")],t),t}},17287:(e,t,r)=>{r.d(t,{Y:()=>p});var i=r(43697),s=r(92604),n=r(70586),o=r(5600),a=(r(75215),r(67676),r(52011)),l=r(66677);const p=e=>{let t=class extends e{get title(){if(this._get("title")&&"defaults"!==this.originOf("title"))return this._get("title");if(this.url){const e=(0,l.Qc)(this.url);if((0,n.pC)(e)&&e.title)return e.title}return this._get("title")||""}set title(e){this._set("title",e)}set url(e){this._set("url",(0,l.Nm)(e,s.Z.getLogger(this.declaredClass)))}};return(0,i._)([(0,o.Cb)()],t.prototype,"title",null),(0,i._)([(0,o.Cb)({type:String})],t.prototype,"url",null),t=(0,i._)([(0,a.j)("esri.layers.mixins.ArcGISService")],t),t}},28294:(e,t,r)=>{r.d(t,{n:()=>y});var i=r(43697),s=r(92835),n=r(84552),o=r(5600),a=(r(75215),r(67676),r(71715)),l=r(52011),p=r(35671),u=r(76259),d=r(78981);const y=e=>{let t=class extends e{constructor(){super(...arguments),this.timeExtent=null,this.timeOffset=null,this.useViewTime=!0}readOffset(e,t){const r=t.timeInfo.exportOptions;if(!r)return null;const i=r.timeOffset,s=d.v.fromJSON(r.timeOffsetUnits);return i&&s?new n.Z({value:i,unit:s}):null}set timeInfo(e){(0,p.UF)(e,this.fieldsIndex),this._set("timeInfo",e)}};return(0,i._)([(0,o.Cb)({type:s.Z,json:{write:!1}})],t.prototype,"timeExtent",void 0),(0,i._)([(0,o.Cb)({type:n.Z})],t.prototype,"timeOffset",void 0),(0,i._)([(0,a.r)("service","timeOffset",["timeInfo.exportOptions"])],t.prototype,"readOffset",null),(0,i._)([(0,o.Cb)({value:null,type:u.Z,json:{write:!0,origins:{"web-document":{read:!1,write:!1},"portal-item":{read:!1,write:!1}}}})],t.prototype,"timeInfo",null),(0,i._)([(0,o.Cb)({type:Boolean,json:{read:{source:"timeAnimation"},write:{target:"timeAnimation"},origins:{"web-scene":{read:!1,write:!1}}}})],t.prototype,"useViewTime",void 0),t=(0,i._)([(0,l.j)("esri.layers.mixins.TemporalLayer")],t),t}},70082:(e,t,r)=>{r.d(t,{Z:()=>d});var i=r(43697),s=r(2368),n=r(35454),o=r(96674),a=r(5600),l=(r(75215),r(67676),r(52011));const p=new n.X({esriFeatureEditToolAutoCompletePolygon:"auto-complete-polygon",esriFeatureEditToolCircle:"circle",esriFeatureEditToolEllipse:"ellipse",esriFeatureEditToolFreehand:"freehand",esriFeatureEditToolLine:"line",esriFeatureEditToolNone:"none",esriFeatureEditToolPoint:"point",esriFeatureEditToolPolygon:"polygon",esriFeatureEditToolRectangle:"rectangle",esriFeatureEditToolArrow:"arrow",esriFeatureEditToolTriangle:"triangle",esriFeatureEditToolLeftArrow:"left-arrow",esriFeatureEditToolRightArrow:"right-arrow",esriFeatureEditToolUpArrow:"up-arrow",esriFeatureEditToolDownArrow:"down-arrow"});let u=class extends((0,s.J)(o.wq)){constructor(e){super(e),this.name=null,this.description=null,this.drawingTool=null,this.prototype=null,this.thumbnail=null}};(0,i._)([(0,a.Cb)({json:{write:!0}})],u.prototype,"name",void 0),(0,i._)([(0,a.Cb)({json:{write:!0}})],u.prototype,"description",void 0),(0,i._)([(0,a.Cb)({json:{read:p.read,write:p.write}})],u.prototype,"drawingTool",void 0),(0,i._)([(0,a.Cb)({json:{write:!0}})],u.prototype,"prototype",void 0),(0,i._)([(0,a.Cb)({json:{write:!0}})],u.prototype,"thumbnail",void 0),u=(0,i._)([(0,l.j)("esri.layers.support.FeatureTemplate")],u);const d=u},56765:(e,t,r)=>{r.d(t,{Z:()=>u});var i,s=r(43697),n=r(46791),o=r(96674),a=r(5600),l=(r(75215),r(67676),r(52011));let p=i=class extends o.wq{constructor(e){super(e),this.floorField=null,this.viewAllMode=!1,this.viewAllLevelIds=new n.Z}clone(){return new i({floorField:this.floorField,viewAllMode:this.viewAllMode,viewAllLevelIds:this.viewAllLevelIds})}};(0,s._)([(0,a.Cb)({type:String,json:{write:!0}})],p.prototype,"floorField",void 0),(0,s._)([(0,a.Cb)({json:{read:!1,write:!1}})],p.prototype,"viewAllMode",void 0),(0,s._)([(0,a.Cb)({json:{read:!1,write:!1}})],p.prototype,"viewAllLevelIds",void 0),p=i=(0,s._)([(0,l.j)("esri.layers.support.LayerFloorInfo")],p);const u=p},76259:(e,t,r)=>{r.d(t,{Z:()=>b});var i=r(43697),s=r(92835),n=r(84552),o=r(2368),a=r(96674),l=r(70586),p=r(5600),u=(r(75215),r(67676),r(71715)),d=r(52011),y=r(30556),c=r(80216);function h(e,t){return n.Z.fromJSON({value:e,unit:t})}let m=class extends((0,o.J)(a.wq)){constructor(e){super(e),this.cumulative=!1,this.endField=null,this.fullTimeExtent=null,this.hasLiveData=!1,this.interval=null,this.startField=null,this.timeReference=null,this.trackIdField=null,this.useTime=!0}readFullTimeExtent(e,t){if(!t.timeExtent||!Array.isArray(t.timeExtent)||2!==t.timeExtent.length)return null;const r=new Date(t.timeExtent[0]),i=new Date(t.timeExtent[1]);return new s.Z({start:r,end:i})}writeFullTimeExtent(e,t){e&&(0,l.pC)(e.start)&&(0,l.pC)(e.end)?t.timeExtent=[e.start.getTime(),e.end.getTime()]:t.timeExtent=null}readInterval(e,t){return t.timeInterval&&t.timeIntervalUnits?h(t.timeInterval,t.timeIntervalUnits):t.defaultTimeInterval&&t.defaultTimeIntervalUnits?h(t.defaultTimeInterval,t.defaultTimeIntervalUnits):null}writeInterval(e,t){t.timeInterval=e?.toJSON().value??null,t.timeIntervalUnits=e?.toJSON().unit??null}};(0,i._)([(0,p.Cb)({type:Boolean,json:{name:"exportOptions.timeDataCumulative",write:!0}})],m.prototype,"cumulative",void 0),(0,i._)([(0,p.Cb)({type:String,json:{name:"endTimeField",write:{enabled:!0,allowNull:!0}}})],m.prototype,"endField",void 0),(0,i._)([(0,p.Cb)({type:s.Z,json:{write:{enabled:!0,allowNull:!0}}})],m.prototype,"fullTimeExtent",void 0),(0,i._)([(0,u.r)("fullTimeExtent",["timeExtent"])],m.prototype,"readFullTimeExtent",null),(0,i._)([(0,y.c)("fullTimeExtent")],m.prototype,"writeFullTimeExtent",null),(0,i._)([(0,p.Cb)({type:Boolean,json:{write:!0}})],m.prototype,"hasLiveData",void 0),(0,i._)([(0,p.Cb)({type:n.Z,json:{write:{enabled:!0,allowNull:!0}}})],m.prototype,"interval",void 0),(0,i._)([(0,u.r)("interval",["timeInterval","timeIntervalUnits","defaultTimeInterval","defaultTimeIntervalUnits"])],m.prototype,"readInterval",null),(0,i._)([(0,y.c)("interval")],m.prototype,"writeInterval",null),(0,i._)([(0,p.Cb)({type:String,json:{name:"startTimeField",write:{enabled:!0,allowNull:!0}}})],m.prototype,"startField",void 0),(0,i._)([(0,p.Cb)({type:c.Z,json:{write:{enabled:!0,allowNull:!0}}})],m.prototype,"timeReference",void 0),(0,i._)([(0,p.Cb)({type:String,json:{write:{enabled:!0,allowNull:!0}}})],m.prototype,"trackIdField",void 0),(0,i._)([(0,p.Cb)({type:Boolean,json:{name:"exportOptions.useTime",write:!0}})],m.prototype,"useTime",void 0),m=(0,i._)([(0,d.j)("esri.layers.support.TimeInfo")],m);const b=m},53518:(e,t,r)=>{r.d(t,{v:()=>a});var i=r(92604),s=r(1231),n=r(99514),o=r(35671);function a(){return{fields:{type:[s.Z],value:null},fieldsIndex:{readOnly:!0,get(){return new n.Z(this.fields||[])}},outFields:{type:[String],json:{read:!1},set:function(e){this._userOutFields=e,this.notifyChange("outFields")},get:function(){const e=this._userOutFields;if(!e||!e.length)return null;if(e.includes("*"))return["*"];if(!this.fields)return e;for(const t of e){const r=this.fieldsIndex?.has(t);r||i.Z.getLogger("esri.layers.support.fieldProperties").error("field-attributes-layer:invalid-field",`Invalid field ${t} found in outFields`,{layer:this,outFields:e})}return(0,o.Q0)(this.fieldsIndex,e)}}}}},72064:(e,t,r)=>{r.d(t,{h:()=>d});var i=r(80442),s=r(70586),n=r(66677);const o={name:"supportsName",size:"supportsSize",contentType:"supportsContentType",keywords:"supportsKeywords",exifInfo:"supportsExifInfo"};function a(e,t,r){return!!(e&&e.hasOwnProperty(t)?e[t]:r)}function l(e,t,r){return e&&e.hasOwnProperty(t)?e[t]:r}function p(e){const t=e?.supportedSpatialAggregationStatistics?.map((e=>e.toLowerCase()));return{envelope:!!t?.includes("envelopeaggregate"),centroid:!!t?.includes("centroidaggregate"),convexHull:!!t?.includes("convexhullaggregate")}}function u(e,t){const r=e?.supportedOperationsWithCacheHint?.map((e=>e.toLowerCase()));return!!r?.includes(t.toLowerCase())}function d(e,t){return{analytics:y(e),attachment:c(e),data:h(e),metadata:m(e),operations:b(e.capabilities,e,t),query:f(e,t),queryRelated:g(e),queryTopFeatures:v(e),editing:w(e)}}function y(e){return{supportsCacheHint:u(e.advancedQueryCapabilities,"queryAnalytics")}}function c(e){const t=e.attachmentProperties,r={supportsName:!1,supportsSize:!1,supportsContentType:!1,supportsKeywords:!1,supportsExifInfo:!1,supportsCacheHint:u(e.advancedQueryCapabilities,"queryAttachments"),supportsResize:a(e,"supportsAttachmentsResizing",!1)};return t&&Array.isArray(t)&&t.forEach((e=>{const t=o[e.name];t&&(r[t]=!!e.isEnabled)})),r}function h(e){return{isVersioned:a(e,"isDataVersioned",!1),supportsAttachment:a(e,"hasAttachments",!1),supportsM:a(e,"hasM",!1),supportsZ:a(e,"hasZ",!1)}}function m(e){return{supportsAdvancedFieldProperties:a(e,"supportsFieldDescriptionProperty",!1)}}function b(e,t,r){const i=e?e.toLowerCase().split(",").map((e=>e.trim())):[],o=r?(0,n.Qc)(r):null,l=i.includes((0,s.pC)(o)&&"MapServer"===o.serverType?"data":"query"),p=i.includes("editing")&&!t.datesInUnknownTimezone;let u=p&&i.includes("create"),d=p&&i.includes("delete"),y=p&&i.includes("update");const c=i.includes("changetracking"),h=t.advancedQueryCapabilities;return p&&!(u||d||y)&&(u=d=y=!0),{supportsCalculate:a(t,"supportsCalculate",!1),supportsTruncate:a(t,"supportsTruncate",!1),supportsValidateSql:a(t,"supportsValidateSql",!1),supportsAdd:u,supportsDelete:d,supportsEditing:p,supportsChangeTracking:c,supportsQuery:l,supportsQueryAnalytics:a(h,"supportsQueryAnalytic",!1),supportsQueryAttachments:a(h,"supportsQueryAttachments",!1),supportsQueryTopFeatures:a(h,"supportsTopFeaturesQuery",!1),supportsResizeAttachments:a(t,"supportsAttachmentsResizing",!1),supportsSync:i.includes("sync"),supportsUpdate:y,supportsExceedsLimitStatistics:a(t,"supportsExceedsLimitStatistics",!1)}}function f(e,t){const r=e.advancedQueryCapabilities,s=e.ownershipBasedAccessControlForFeatures,o=e.archivingInfo,d=e.currentVersion,y=t?.includes("MapServer"),c=!y||d>=(0,i.Z)("mapserver-pbf-version-support"),h=(0,n.M8)(t),m=new Set((e.supportedQueryFormats??"").split(",").map((e=>e.toLowerCase().trim())));return{supportsStatistics:a(r,"supportsStatistics",e.supportsStatistics),supportsPercentileStatistics:a(r,"supportsPercentileStatistics",!1),supportsSpatialAggregationStatistics:a(r,"supportsSpatialAggregationStatistics",!1),supportedSpatialAggregationStatistics:p(r),supportsCentroid:a(r,"supportsReturningGeometryCentroid",!1),supportsDistance:a(r,"supportsQueryWithDistance",!1),supportsDistinct:a(r,"supportsDistinct",e.supportsAdvancedQueries),supportsExtent:a(r,"supportsReturningQueryExtent",!1),supportsGeometryProperties:a(r,"supportsReturningGeometryProperties",!1),supportsHavingClause:a(r,"supportsHavingClause",!1),supportsOrderBy:a(r,"supportsOrderBy",e.supportsAdvancedQueries),supportsPagination:a(r,"supportsPagination",!1),supportsQuantization:a(e,"supportsCoordinatesQuantization",!1),supportsQuantizationEditMode:a(e,"supportsQuantizationEditMode",!1),supportsQueryGeometry:a(e,"supportsReturningQueryGeometry",!1),supportsResultType:a(r,"supportsQueryWithResultType",!1),supportsMaxRecordCountFactor:a(r,"supportsMaxRecordCountFactor",!1),supportsSqlExpression:a(r,"supportsSqlExpression",!1),supportsStandardizedQueriesOnly:a(e,"useStandardizedQueries",!1),supportsTopFeaturesQuery:a(r,"supportsTopFeaturesQuery",!1),supportsQueryByOthers:a(s,"allowOthersToQuery",!0),supportsHistoricMoment:a(o,"supportsQueryWithHistoricMoment",!1),supportsFormatPBF:c&&m.has("pbf"),supportsDisjointSpatialRelationship:a(r,"supportsDisjointSpatialRel",!1),supportsCacheHint:a(r,"supportsQueryWithCacheHint",!1)||u(r,"query"),supportsDefaultSpatialReference:a(r,"supportsDefaultSR",!1),supportsCompactGeometry:h,supportsFullTextSearch:a(r,"supportsFullTextSearch",!1),maxRecordCountFactor:l(e,"maxRecordCountFactor",void 0),maxRecordCount:l(e,"maxRecordCount",void 0),standardMaxRecordCount:l(e,"standardMaxRecordCount",void 0),tileMaxRecordCount:l(e,"tileMaxRecordCount",void 0)}}function g(e){const t=e.advancedQueryCapabilities,r=a(t,"supportsAdvancedQueryRelated",!1);return{supportsPagination:a(t,"supportsQueryRelatedPagination",!1),supportsCount:r,supportsOrderBy:r,supportsCacheHint:u(t,"queryRelated")}}function v(e){return{supportsCacheHint:u(e.advancedQueryCapabilities,"queryTopFilter")}}function w(e){const t=e.ownershipBasedAccessControlForFeatures;return{supportsGeometryUpdate:a(e,"allowGeometryUpdates",!0),supportsGlobalId:a(e,"supportsApplyEditsWithGlobalIds",!1),supportsReturnServiceEditsInSourceSpatialReference:a(e,"supportsReturnServiceEditsInSourceSR",!1),supportsRollbackOnFailure:a(e,"supportsRollbackOnFailureParameter",!1),supportsUpdateWithoutM:a(e,"allowUpdateWithoutMValues",!1),supportsUploadWithItemId:a(e,"supportsAttachmentsByUploadId",!1),supportsDeleteByAnonymous:a(t,"allowAnonymousToDelete",!0),supportsDeleteByOthers:a(t,"allowOthersToDelete",!0),supportsUpdateByAnonymous:a(t,"allowAnonymousToUpdate",!0),supportsUpdateByOthers:a(t,"allowOthersToUpdate",!0)}}},78981:(e,t,r)=>{r.d(t,{v:()=>i});const i=(0,r(35454).w)()({esriTimeUnitsMilliseconds:"milliseconds",esriTimeUnitsSeconds:"seconds",esriTimeUnitsMinutes:"minutes",esriTimeUnitsHours:"hours",esriTimeUnitsDays:"days",esriTimeUnitsWeeks:"weeks",esriTimeUnitsMonths:"months",esriTimeUnitsYears:"years",esriTimeUnitsDecades:"decades",esriTimeUnitsCenturies:"centuries",esriTimeUnitsUnknown:void 0})},51706:(e,t,r)=>{var i,s;function n(e){return e&&"esri.renderers.visualVariables.SizeVariable"===e.declaredClass}function o(e){return null!=e&&!isNaN(e)&&isFinite(e)}function a(e){return e.valueExpression?i.Expression:e.field&&"string"==typeof e.field?i.Field:i.Unknown}function l(e,t){const r=t||a(e),n=e.valueUnit||"unknown";return r===i.Unknown?s.Constant:e.stops?s.Stops:null!=e.minSize&&null!=e.maxSize&&null!=e.minDataValue&&null!=e.maxDataValue?s.ClampedLinear:"unknown"===n?null!=e.minSize&&null!=e.minDataValue?e.minSize&&e.minDataValue?s.Proportional:s.Additive:s.Identity:s.RealWorldSize}r.d(t,{PS:()=>a,QW:()=>l,RY:()=>i,hL:()=>s,iY:()=>n,qh:()=>o}),function(e){e.Unknown="unknown",e.Expression="expression",e.Field="field"}(i||(i={})),function(e){e.Unknown="unknown",e.Stops="stops",e.ClampedLinear="clamped-linear",e.Proportional="proportional",e.Additive="additive",e.Constant="constant",e.Identity="identity",e.RealWorldSize="real-world-size"}(s||(s={}))},56545:(e,t,r)=>{r.d(t,{Z:()=>y});var i,s=r(43697),n=r(96674),o=r(22974),a=r(5600),l=r(75215),p=r(52011),u=r(30556);let d=i=class extends n.wq{constructor(e){super(e),this.attachmentTypes=null,this.attachmentsWhere=null,this.cacheHint=void 0,this.keywords=null,this.globalIds=null,this.name=null,this.num=null,this.objectIds=null,this.returnMetadata=!1,this.size=null,this.start=null,this.where=null}writeStart(e,t){t.resultOffset=this.start,t.resultRecordCount=this.num||10}clone(){return new i((0,o.d9)({attachmentTypes:this.attachmentTypes,attachmentsWhere:this.attachmentsWhere,cacheHint:this.cacheHint,keywords:this.keywords,where:this.where,globalIds:this.globalIds,name:this.name,num:this.num,objectIds:this.objectIds,returnMetadata:this.returnMetadata,size:this.size,start:this.start}))}};(0,s._)([(0,a.Cb)({type:[String],json:{write:!0}})],d.prototype,"attachmentTypes",void 0),(0,s._)([(0,a.Cb)({type:String,json:{read:{source:"attachmentsDefinitionExpression"},write:{target:"attachmentsDefinitionExpression"}}})],d.prototype,"attachmentsWhere",void 0),(0,s._)([(0,a.Cb)({type:Boolean,json:{write:!0}})],d.prototype,"cacheHint",void 0),(0,s._)([(0,a.Cb)({type:[String],json:{write:!0}})],d.prototype,"keywords",void 0),(0,s._)([(0,a.Cb)({type:[Number],json:{write:!0}})],d.prototype,"globalIds",void 0),(0,s._)([(0,a.Cb)({json:{write:!0}})],d.prototype,"name",void 0),(0,s._)([(0,a.Cb)({type:Number,json:{read:{source:"resultRecordCount"}}})],d.prototype,"num",void 0),(0,s._)([(0,a.Cb)({type:[Number],json:{write:!0}})],d.prototype,"objectIds",void 0),(0,s._)([(0,a.Cb)({type:Boolean,json:{default:!1,write:!0}})],d.prototype,"returnMetadata",void 0),(0,s._)([(0,a.Cb)({type:[Number],json:{write:!0}})],d.prototype,"size",void 0),(0,s._)([(0,a.Cb)({type:Number,json:{read:{source:"resultOffset"}}})],d.prototype,"start",void 0),(0,s._)([(0,u.c)("start"),(0,u.c)("num")],d.prototype,"writeStart",null),(0,s._)([(0,a.Cb)({type:String,json:{read:{source:"definitionExpression"},write:{target:"definitionExpression"}}})],d.prototype,"where",void 0),d=i=(0,s._)([(0,p.j)("esri.rest.support.AttachmentQuery")],d),d.from=(0,l.se)(d);const y=d},58333:(e,t,r)=>{r.d(t,{ET:()=>n,I4:()=>s,eG:()=>l,lF:()=>o,lj:()=>u,qP:()=>a,wW:()=>p});const i=[252,146,31,255],s={type:"esriSMS",style:"esriSMSCircle",size:6,color:i,outline:{type:"esriSLS",style:"esriSLSSolid",width:.75,color:[153,153,153,255]}},n={type:"esriSLS",style:"esriSLSSolid",width:.75,color:i},o={type:"esriSFS",style:"esriSFSSolid",color:[252,146,31,196],outline:{type:"esriSLS",style:"esriSLSSolid",width:.75,color:[255,255,255,191]}},a={type:"esriTS",color:[255,255,255,255],font:{family:"arial-unicode-ms",size:10,weight:"bold"},horizontalAlignment:"center",kerning:!0,haloColor:[0,0,0,255],haloSize:1,rotated:!1,text:"",xoffset:0,yoffset:0,angle:0},l={type:"esriSMS",style:"esriSMSCircle",color:[0,0,0,255],outline:null,size:10.5},p={type:"esriSLS",style:"esriSLSSolid",color:[0,0,0,255],width:1.5},u={type:"esriSFS",style:"esriSFSSolid",color:[0,0,0,255],outline:null}}}]);