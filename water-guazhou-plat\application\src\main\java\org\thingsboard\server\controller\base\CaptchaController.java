/**
 * Copyright © 2016-2019 The Thingsboard Authors
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.controller.base;

import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.repository.query.Param;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.thingsboard.rule.engine.api.MailService;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.VO.AlarmLinkedUser;
import org.thingsboard.server.common.data.VO.BaseResult;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.dao.model.sql.CaptchaEntity;
import org.thingsboard.server.dao.model.sql.OperateLogEntity;

import java.util.List;

/**
 * 验证码相关API
 */
@RestController
@RequestMapping("/api/captcha")
public class CaptchaController extends BaseController {


    @Autowired
    private MailService mailService;

    /**
     * 获取验证码
     *
     * @param phone
     * @return
     * @throws ThingsboardException
     */
    @GetMapping(value = "/getCaptcha")
    public BaseResult testSmsKey(@Param("phone") String phone) throws ThingsboardException {

        String captcha = null;
        //获取该手机号是否有还未失效的验证码
        CaptchaEntity captchaEntity = captchaService.getCaptchaByPhoneAndInvalid(phone, DataConstants.ROOT_PROJECT_PARENT_ID);
        if (captchaEntity != null) {
            //检查是否过期(默认2min)
            if (System.currentTimeMillis() - captchaEntity.getCreateTime() > 1000 * 60 * 2) {
                captchaEntity.setInvalid(DataConstants.IS_DELETE_YES);
                captchaService.saveCaptcha(captchaEntity);
            } else {
                captcha = captchaEntity.getCaptcha();
            }
        }
        //未读取到历史验证码记录，重新生成验证码并记录
        if (captcha == null) {
            captcha = getCaptcha();
            CaptchaEntity captchaData = CaptchaEntity.builder()
                    .captcha(captcha)
                    .createTime(System.currentTimeMillis())
                    .phone(phone)
                    .invalid(DataConstants.ROOT_PROJECT_PARENT_ID)
                    .tenantId(UUIDConverter.fromTimeUUID(getTenantId().getId()))
                    .userId(UUIDConverter.fromTimeUUID(getCurrentUser().getUuidId()))
                    .build();
            captchaService.saveCaptcha(captchaData);
        }
        AlarmLinkedUser alarmLinkedUser = AlarmLinkedUser.builder()
                .userName(getCurrentUser().getName())
                .email(getCurrentUser().getEmail())
                .phone(phone)
                .tenantId(getTenantId())
                .build();
        boolean result = mailService.sendCaptcha(alarmLinkedUser, captcha);
        if (result) {
            return BaseResult.builder().isResult(true).msg("发送短信成功！").build();
        } else {
            return BaseResult.builder().isResult(false).msg("发送短信失败！").build();
        }
    }

    /**
     * 开启操作模式
     *
     * @param captcha
     */
    @GetMapping(value = "/startOperate")
    public BaseResult startOperateModel(@Param("captcha") String captcha, @Param("phone") String phone) throws ThingsboardException {
        //check是否有未关闭的操作模式记录
        OperateLogEntity oldLog = operateLogService.findByPhoneAndInvalid(phone, DataConstants.ROOT_PROJECT_PARENT_ID);
        if (oldLog != null) {
            oldLog.setInvalid(DataConstants.IS_DELETE_YES);
            operateLogService.saveLog(oldLog);
        }

        //检验该验证码是否正确
        OperateLogEntity operateLogEntity = OperateLogEntity.builder()
                .captcha(captcha)
                .createTime(System.currentTimeMillis())
                .phone(phone)
                .tenantId(UUIDConverter.fromTimeUUID(getTenantId().getId()))
                .type(DataConstants.ORERATE_TYPE_START_OPERATE)
                .userId(UUIDConverter.fromTimeUUID(getCurrentUser().getUuidId())).build();
        CaptchaEntity captchaEntity = captchaService.getCaptchaByCaptchaAndInvalidAndPhone(captcha, DataConstants.ROOT_PROJECT_PARENT_ID,phone);
        if (captchaEntity == null) {
            //记录失败日志
            operateLogEntity.setInvalid(DataConstants.IS_DELETE_YES);
            operateLogEntity.setResult(DataConstants.SEND_MSG_FAILED);
            operateLogEntity.setResultMsg("验证码无效！");
            operateLogService.saveLog(operateLogEntity);
            return BaseResult.builder()
                    .isResult(false)
                    .msg("验证码无效！")
                    .build();
        } else {
            //校验验证码失效时间
            if (System.currentTimeMillis() - captchaEntity.getCreateTime() > 1000 * 60 * 10) {
                captchaEntity.setInvalid(DataConstants.IS_DELETE_YES);
                captchaService.saveCaptcha(captchaEntity);
                operateLogEntity.setInvalid(DataConstants.IS_DELETE_YES);
                operateLogEntity.setResult(DataConstants.SEND_MSG_FAILED);
                operateLogEntity.setResultMsg("验证码已过期！");
                operateLogService.saveLog(operateLogEntity);
                return BaseResult.builder()
                        .isResult(false)
                        .msg("验证码失效！")
                        .build();
            } else {
                //创建新的开启操作模式记录
                captchaEntity.setInvalid(DataConstants.IS_DELETE_YES);
                captchaService.saveCaptcha(captchaEntity);
                operateLogEntity.setInvalid(DataConstants.ROOT_PROJECT_PARENT_ID);
                operateLogEntity.setResult(DataConstants.SEND_MSG_SUCCESS);
                operateLogService.saveLog(operateLogEntity);
                return BaseResult.builder()
                        .isResult(true)
                        .msg("开启操作模式成功！")
                        .build();
            }
        }

    }

    /**
     * 关闭操作模式
     *
     * @param phone
     */
    @GetMapping(value = "/finishOperate")
    public BaseResult finishOperateModel(@Param("phone") String phone, @Param("captcha") String captcha) throws ThingsboardException {
        OperateLogEntity operateLogEntity = OperateLogEntity.builder()
                .captcha(captcha)
                .createTime(System.currentTimeMillis())
                .phone(phone)
                .tenantId(UUIDConverter.fromTimeUUID(getTenantId().getId()))
                .type(DataConstants.ORERATE_TYPE_FINISH_OPERATE)
                .userId(UUIDConverter.fromTimeUUID(getCurrentUser().getUuidId())).build();
        //check是否有未关闭的操作模式记录
        OperateLogEntity oldLog = operateLogService.findByPhoneAndInvalid(phone, DataConstants.ROOT_PROJECT_PARENT_ID);
        if (oldLog != null) {
            oldLog.setInvalid(DataConstants.IS_DELETE_YES);
            operateLogService.saveLog(oldLog);
            operateLogEntity.setInvalid(DataConstants.IS_DELETE_YES);
            operateLogEntity.setResult(DataConstants.SEND_MSG_SUCCESS);
            operateLogService.saveLog(operateLogEntity);
            return BaseResult.builder()
                    .isResult(true)
                    .msg("关闭操作模式成功！")
                    .build();
        } else {
            operateLogEntity.setInvalid(DataConstants.IS_DELETE_YES);
            operateLogEntity.setResult(DataConstants.SEND_MSG_FAILED);
            operateLogEntity.setResultMsg("没有需要结束的操作状态！");
            operateLogService.saveLog(operateLogEntity);
            return BaseResult.builder()
                    .isResult(false)
                    .msg("没有需要结束的操作状态！")
                    .build();
        }
    }

    @SneakyThrows
    @GetMapping("/getLog")
    public List<OperateLogEntity> findOperateLog(@Param(DataConstants.START) long start,
                                                 @Param(DataConstants.END) long end) {
               return operateLogService.findByTenantIdAndTime(getTenantId(),start,end);
    }

    /**
     * 生成6位随机数验证码
     *
     * @return
     */
    public static String getCaptcha() {
        StringBuilder captcha = new StringBuilder();
        for (int i = 0; i < 6; i++) {
            captcha.append(((int) Math.floor(Math.random() * 10)));
        }
        return captcha.toString();
    }


}
