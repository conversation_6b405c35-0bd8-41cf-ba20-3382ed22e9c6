import{_ as G}from"./index-C9hz-UZb.js";import{d as H,M,a6 as J,r as b,c as v,am as W,bF as u,a8 as Q,s as T,j as C,bB as U,bu as $,ay as X,g as Y,n as Z,q as p,i,F as _,cs as q,bo as O,bR as A,p as I,dF as ee,dA as te,aq as ae,al as le,b7 as ne,aj as se,C as re}from"./index-r0dFAfgr.js";import{_ as oe}from"./CardSearch-CB_HNR-Q.js";import{l as ie}from"./echart-DJHHUFsr.js";import{a as ce}from"./queryStatistics-CQ9DBM08.js";import{u as de}from"./useStation-DJgnSZIA.js";import{f as ue}from"./formartColumn-D5r7JJ2G.js";import{b as me}from"./zhandian-YaGuQZe6.js";import"./Search-NSrhrIa_.js";const pe={class:"wrapper"},fe=H({__name:"index",setup(ye){const{$messageWarning:S}=M(),V=J(),{getStationTree:B,getStationTreeByDisabledType:E,getStationAttrGroups:P}=de(),n=b({type:"date",chartOption:null,stationTree:[],activeName:"echarts",data:null,checkedKeys:[]}),z=[{label:"1 m",value:"1m"},{label:"5 m",value:"5m"},{label:"10 m",value:"10m"},{label:"15 m",value:"15m"}],x=v(),k=v(),g=v(),w=v();let f=b([]);const L=b({data:[],title:"区域划分",showCheckbox:!0,defaultExpandAll:!1,accordion:!1,checkedKeys:[],handleCheck:(e,t)=>{console.log(t.checkedNodes,t.checkedKeys),L.checkedKeys=t.checkedKeys||[],L.checkedNodes=t.checkedNodes||[],N()},nodeExpand:async(e,t)=>{var a;if(((a=e.data)==null?void 0:a.type)==="Station"&&e.children[0].id===0){const s=await P(e.id,!0);t.data.children=s}}});W(()=>n.activeName,()=>{n.activeName==="echarts"&&D()});const j=b({defaultParams:{queryType:"15m",type:"day",year:[u().format(),u().format()],month:[u().format(),u().format()],day:[u().startOf("day").format(),u().format()]},filters:[{type:"select-tree",label:"监测点:",multiple:!0,field:"attributeId",clearable:!1,showCheckbox:!0,lazy:!0,options:Q(()=>n.stationTree),lazyLoad:(e,t)=>{var a,s;if(e.level===0)return t([]);if(((a=e.data.children)==null?void 0:a.length)>0)return t(e.data.children);if(e.isLeaf)return t([]);if((s=e.data)!=null&&s.isLeaf)return t([]);me({stationId:e.data.id}).then(d=>{var o;const r=(o=d.data)==null?void 0:o.map(m=>({label:m.type,value:"",id:"",children:m.attrList.map(l=>({label:l.name,value:l.id,id:l.id,isLeaf:!0}))}));return t(r)})}},{type:"radio-button",field:"type",options:[{label:"日",value:"day"},{label:"月",value:"month"},{label:"年",value:"year"}],label:"时间频率"},{type:"datetimerange",label:"选择日期",field:"day",clearable:!1,handleHidden:(e,t,a)=>{a.hidden=e.type==="month"||e.type==="year"}},{type:"monthrange",label:"选择日期",field:"month",clearable:!1,handleHidden:(e,t,a)=>{a.hidden=e.type==="day"||e.type==="year"}},{type:"yearrange",label:"选择日期",field:"year",clearable:!1,handleHidden:(e,t,a)=>{a.hidden=e.type==="month"||e.type==="day"}},{type:"select",label:"时间间隔:",field:"queryType",clearable:!1,allowCreate:!0,options:z,itemContainerStyle:{width:"180px"},handleHidden:(e,t,a)=>{a.hidden=e.type==="month"||e.type==="year"}}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",click:()=>{var t;const e=((t=g.value)==null?void 0:t.queryParams)||{};e.attributeId&&e.attributeId.length>0?N():S("选择监测点")},svgIcon:T(le)},{type:"default",perm:!0,text:"重置",svgIcon:T(ne),click:()=>{var e;(e=g.value)==null||e.resetForm()}},{text:"导出",perm:!0,type:"warning",svgIcon:T(se),hide:()=>n.activeName!=="list",click:()=>F()}]}]}),c=b({loading:!1,dataList:[],columns:[],operations:[],pagination:{refreshData:({page:e,size:t})=>{c.pagination.page=e,c.pagination.limit=t,c.dataList=f==null?void 0:f.slice((e-1)*t,e*t)}}}),F=()=>{var e;c.dataList.length>0?(e=w.value)==null||e.exportTable():S("无数据导出")},N=()=>{var o;c.loading=!0;const e=((o=g.value)==null?void 0:o.queryParams)||{};console.log(e);const[t,a]=e[e.type]||[];let s=0,d=0;e.type==="day"?(s=t?u(t).valueOf():"",d=a?u(a).valueOf():""):(s=t?u(t).startOf(e.type).valueOf():"",d=a?u(a).endOf(e.type).valueOf():"");const r={attributes:e.attributeId.join(","),queryType:e.type==="month"?"day":e.type==="year"?"month":e.queryType,start:s,end:d};ce(r).then(m=>{var h;const l=(h=m.data)==null?void 0:h.data;n.data=l,f=l==null?void 0:l.tableDataList;const y=ue(l==null?void 0:l.tableInfo);c.columns=y,c.dataList=f.slice(0*20,1*20),c.pagination.total=l==null?void 0:l.tableDataList.length,c.loading=!1,D()})},K=()=>{var e;(e=x.value)==null||e.resize()},D=()=>{var a,s,d;const e=ie();e.series=[];const t={name:"",smooth:!0,data:[],type:"line",markPoint:{data:[{type:"max",name:"最大值",label:{fontSize:12,color:C().isDark?"#ffffff":"#000000"}},{type:"min",name:"最小值",label:{color:C().isDark?"#ffffff":"#000000"}}]},markLine:{data:[{type:"average",name:"平均值"}]}};e.xAxis.data=(a=n.data)==null?void 0:a.tableDataList.map(r=>r.ts),(s=n.data)==null||s.tableInfo.map((r,o)=>{var m;if(r.columnValue!=="ts"){const l=JSON.parse(JSON.stringify(t));l.name=r.columnName,l.data=(m=n.data)==null?void 0:m.tableDataList.map(h=>h[r.columnValue]);const y=r.columnName.split("--")[2]+(r.unit?"("+r.unit+")":"");o===1?e.yAxis[0].name=y:o>1&&(e.yAxis.find(R=>R.name===y)||(l.yAxisIndex=o-1,e.grid.right=50*(o-1),e.yAxis.push({position:"right",alignTicks:!0,type:"value",name:y,offset:50*(o-2),axisLine:{show:!0,lineStyle:{types:"solid"}},axisLabel:{show:!0},splitLine:{lineStyle:{type:[5,10],dashOffset:5}}}))),e.series.push(l)}}),(d=x.value)==null||d.clear(),U(()=>{k.value&&V.listenTo(k.value,()=>{n.chartOption=e,K()})})};return $(async()=>{const e=await B("水质监测站");await E(e,["Project","Station"],!1,"Station"),n.stationTree=e,console.log(" state.stationTree ",n.stationTree)}),(e,t)=>{const a=oe,s=ee,d=te,r=X("VChart"),o=ae,m=G;return Y(),Z("div",pe,[p(a,{ref_key:"cardSearch",ref:g,config:i(j)},null,8,["config"]),p(m,{class:"card",title:i(n).activeName==="list"?"水质对比列表":"水质对比曲线"},{right:_(()=>[p(d,{modelValue:i(n).activeName,"onUpdate:modelValue":t[0]||(t[0]=l=>i(n).activeName=l)},{default:_(()=>[p(s,{label:"echarts"},{default:_(()=>[p(i(q),{style:{"margin-right":"1px","font-size":"16px"},icon:"clarity:line-chart-line"})]),_:1}),p(s,{label:"list"},{default:_(()=>[p(i(q),{style:{"margin-right":"1px","font-size":"16px"},icon:"material-symbols:table"})]),_:1})]),_:1},8,["modelValue"])]),default:_(()=>[O(I("div",{ref_key:"agriEcoDev",ref:k,class:"chart-box"},[p(r,{ref_key:"refChart",ref:x,theme:i(C)().isDark?"dark":"light",option:i(n).chartOption},null,8,["theme","option"])],512),[[A,i(n).activeName==="echarts"]]),O(I("div",null,[p(o,{ref_key:"refCardTable",ref:w,class:"card-table",config:i(c)},null,8,["config"])],512),[[A,i(n).activeName==="list"]])]),_:1},8,["title"])])}}}),Se=re(fe,[["__scopeId","data-v-ebb4df23"]]);export{Se as default};
