import{c as k,r as U,d as V,ad as J,o as $,Q as q,bo as Q,bR as K,i as d,g as C,n as O,p as o,bh as S,q as x,F as w,aB as X,aJ as Y,h as Z,G as R,S as H,x as T,bz as tt,du as at,dv as st,J as et,C as ot}from"./index-r0dFAfgr.js";/* empty css                         */import{w as rt}from"./Point-WxyopZva.js";import"./MapView-DaoQedLH.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./project-DUuzYgGl.js";import{u as nt}from"./useWaterPoint-Bv0z6ym6.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import{f as lt}from"./DateFormatter-Bm9a68Ax.js";import{k as it,m as ct,E as mt}from"./zhandian-YaGuQZe6.js";import{a as pt}from"./useStation-DJgnSZIA.js";const ut=()=>{const m=k(),_=U({total:0,data:[]}),f=k({});return{alarms:_,getalarms:async i=>{var r;try{const v=((r=(await it(i)).data)==null?void 0:r.data)||{};return _.total=v.total??0,_.data=v.data||[],v}catch{}},setCurAlarmGroup:i=>{i&&(m.value=f.value[i])},curAlarmGroup:m,groupAlarmsByStationId:async()=>{try{const i=pt();await i.getStations(),_.data.map(r=>{if(f.value[r.stationId])f.value[r.stationId].alarms.push(r);else{const e=i.stations.value.find(v=>v.id===r.stationId);if(!(e!=null&&e.location))return;f.value[r.stationId]={station:e,alarms:[r]}}})}catch{}},alarmGroups:f}},dt={1:"提醒报警",2:"重要报警",3:"紧急报警"},ft=m=>dt[m],vt={1:"报警中",2:"已恢复",3:"已解除"},_t=m=>vt[m],yt={1:"未处理",2:"处理中",3:"已处理"},St=m=>yt[m],gt=["title"],ht={class:"inner"},At={class:"alarm-item"},wt={class:"value"},xt={class:"alarm-item"},kt={class:"value"},Ct={class:"alarm-item"},Gt={class:"value"},bt={class:"alarm-item"},Et={class:"value"},Pt={class:"alarm-item"},It={style:{width:"100%",display:"flex","justify-content":"end"}},G="alarm_pop_canvas_",Bt=V({__name:"ArcStationWarning",emits:["click"],setup(m,{expose:_,emit:f}){const b=f,p=k(),s=J("view"),i=nt("viewDiv",{popIdPrefix:G}),r=k(!1),e=ut(),v=async()=>{var t,n;const a=[];await e.groupAlarmsByStationId();for(const c in e.alarmGroups.value){const l=(n=(t=e.alarmGroups.value[c].station)==null?void 0:t.location)==null?void 0:n.split(",");a.push({id:c,point:l?new rt({longitude:l==null?void 0:l[0],latitude:l==null?void 0:l[1],spatialReference:s==null?void 0:s.spatialReference}):void 0})}i.removeAll(),i.addMany(s,a,{width:30,height:30,color:"#ff0000"})},E=async()=>{i.removeAll(),Promise.allSettled([e.getalarms({page:1,size:99999,processStatus:"1",alarmStatus:"1"})]).then(()=>{v()})};let g;const P=a=>{const t=a.target;if(t!=null&&t.id.startsWith(G)){const n=t.id.replace(G,"");b("click",n),e.setCurAlarmGroup(n),r.value=!0,g=s==null?void 0:s.toMap(a);const c=g&&(s==null?void 0:s.toScreen(g));c&&p.value&&(p.value.style.left=c.x+"px",p.value.style.top=c.y+"px")}else r.value=!1},F=a=>{a===void 0?r.value=!r.value:r.value=!!a};let u;const M=()=>{s&&(u&&(u==null||u.remove()),u=s.watch("extent",()=>{if(!g||!s)return;const a=s.toScreen(g);a!==null&&p.value&&(p.value.style.left=a.x+"px",p.value.style.top=a.y+"px")}))};function W(){H("确定解除选中的告警, 是否继续?","解除提示").then(async()=>{var t;let a=[];a=(((t=e.curAlarmGroup.value)==null?void 0:t.alarms)||[]).map(n=>n.id),ct(a).then(n=>{n.data.code===200?(T.success("解除告警成功"),E()):T.warning("解除告警失败")})})}const z=()=>{var t;const a={page:1,size:99999,processStatus:"1",alarmStatus:"1",stationId:(((t=e.curAlarmGroup.value)==null?void 0:t.alarms)||[]).map(n=>n.stationId).join(",")};mt(a).then(n=>{var h,A;const c=window.URL.createObjectURL(n.data),l=document.createElement("a");l.style.display="none",l.href=c,l.setAttribute("download",`${(A=(h=e.curAlarmGroup.value)==null?void 0:h.station)==null?void 0:A.name}.xlsx`),document.body.appendChild(l),l.click()})};return $(async()=>{var a;document.addEventListener("click",P,!1),(a=p.value)==null||a.addEventListener("click",t=>{t.stopPropagation()}),await(s==null?void 0:s.when()),window.SITE_CONFIG.GIS_CONFIG.gisShowAlarms!==!1&&E(),M()}),q(()=>{document.removeEventListener("click",P),u==null||u.remove()}),_({togglePop:F}),(a,t)=>{var A,I,B,L;const n=tt,c=at,l=st,h=et;return Q((C(),O("div",{ref_key:"refPop",ref:p,"data-id":"alarm-pop",class:"alarm-water-pop"},[o("div",{class:"title",title:(I=(A=d(e).curAlarmGroup.value)==null?void 0:A.station)==null?void 0:I.name},[o("span",null,S((L=(B=d(e).curAlarmGroup.value)==null?void 0:B.station)==null?void 0:L.name),1)],8,gt),o("div",ht,[x(l,{style:{padding:"5px"}},{default:w(()=>{var j;return[(C(!0),O(X,null,Y((j=d(e).curAlarmGroup.value)==null?void 0:j.alarms,(y,D)=>{var N;return C(),Z(c,{key:D,timestamp:(N=d(lt)(y.time))==null?void 0:N.toString(),placement:"top"},{default:w(()=>[x(n,{class:"alarm-card"},{default:w(()=>[o("div",At,[t[0]||(t[0]=o("span",{class:"label"},"报警设备:",-1)),o("span",wt,S(y.deviceName),1)]),o("div",xt,[t[1]||(t[1]=o("span",{class:"label"},"紧急程度:",-1)),o("span",kt,S(d(ft)(y.alarmLevel)),1)]),o("div",Ct,[t[2]||(t[2]=o("span",{class:"label"},"报警状态:",-1)),o("span",Gt,S(d(_t)(y.alarmStatus)),1)]),o("div",bt,[t[3]||(t[3]=o("span",{class:"label"},"处理状态:",-1)),o("span",Et,S(d(St)(y.processStatus)),1)]),o("div",Pt,S(y.alarmInfo),1)]),_:2},1024)]),_:2},1032,["timestamp"])}),128))]}),_:1}),o("div",It,[x(h,{type:"success",onClick:z},{default:w(()=>t[4]||(t[4]=[R("导出告警")])),_:1}),x(h,{type:"danger",onClick:W},{default:w(()=>t[5]||(t[5]=[R("解除告警")])),_:1})])])],512)),[[K,d(r)]])}}}),Xt=ot(Bt,[["__scopeId","data-v-6e896f36"]]);export{Xt as _};
