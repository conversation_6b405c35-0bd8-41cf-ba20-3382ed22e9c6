package org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartProduction.dispatch.OrderRecordStatus;
import org.thingsboard.server.dao.util.imodel.query.Requestible;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

import java.util.List;

import static org.thingsboard.server.dao.model.sql.smartProduction.dispatch.OrderRecordStatus.REPLIED;
import static org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch.OrderRecordCompleteStatus.COMPLETED;

@Getter
@Setter
public class OrderRecordReplyRequest implements Requestible {
    // 指令id列表
    @NotNullOrEmpty
    private List<String> idList;

    // 是否标记为已完成
    // @NotNullOrEmpty
    // private Boolean isComplete;

    // 回复的指令内容
    private String replyContent;


    public OrderRecordStatus getStatus() {
        return REPLIED;
    }

    public OrderRecordCompleteStatus getCompleteStatus() {
        // return isComplete ? COMPLETED : PROCESSING;
        return COMPLETED;
    }

}
