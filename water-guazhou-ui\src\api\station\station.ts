import request from '@/plugins/axios'

/**
 * 获取站点列表
 * @param params 查询参数
 * @returns 
 */
export function getStationList(params: {
  page: number
  size: number
  type?: string
  projectId?: string
}) {
  return request({
    url: '/api/station/list',
    method: 'get',
    params
  })
}

/**
 * 获取站点详情
 * @param id 站点ID
 * @returns 
 */
export function getStationById(id: string) {
  return request({
    url: `/api/station/${id}`,
    method: 'get'
  })
}
