package org.thingsboard.server.dao.sql.plan;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.thingsboard.server.dao.model.sql.plan.PlanTaskDetail;
import org.thingsboard.server.dao.util.imodel.query.plan.PlanTaskDetailCompleteRequest;
import org.thingsboard.server.dao.util.imodel.query.plan.PlanTaskDetailPageRequest;

import java.util.List;

@Mapper
public interface PlanTaskDetailMapper extends BaseMapper<PlanTaskDetail> {
    IPage<PlanTaskDetail> findByPage(PlanTaskDetailPageRequest request);

    boolean update(PlanTaskDetail entity);

    int saveAll(List<PlanTaskDetail> list);

    int updateAll(List<PlanTaskDetail> list);

    boolean complete(PlanTaskDetailCompleteRequest req);

    int deleteByMainId(String id);

    boolean reset(String id);
}
