import BasemapGallary from '@arcgis/core/widgets/BasemapGallery.js';
import Expand from '@arcgis/core/widgets/Expand.js';
import { useLayer } from '..';

const getGisPics = (name: string | URL) => {
  const href = new URL(`../../../assets/images/gis/${name}`, import.meta.url)
    ?.href;
  return href;
};
export const useBasemapGallary = (onChange?: (e: any) => any) => {
  let basemapGallary: __esri.BasemapGallery | undefined;
  let bgExpand: __esri.Expand | undefined;
  const init = (
    view?: __esri.MapView,
    widgetPosition?: string,
    poi?: boolean
  ) => {
    const { createTdtLayer } = useLayer();
    const cva_w = createTdtLayer({ type: 'cva_w' });
    const vec_w = createTdtLayer({ type: 'vec_w' });
    const vec_w_dark = createTdtLayer({
      type: 'vec_w',
      color: 'rgba(255, 255, 255, 0.0)',
      filter: 'grayscale(0%) invert(100%) opacity(100%)',
      urlTemplate:
        'http://{subDomain}.tianditu.com/DataServer?T=vec_w&x={col}&y={row}&l={level}&tk=' +
        window.SITE_CONFIG.GIS_CONFIG.gisTdtToken
    });
    const img_w = createTdtLayer({ type: 'img_w' });
    const cia_w = createTdtLayer({ type: 'cia_w' });
    const showPoi = poi ?? window.SITE_CONFIG.GIS_CONFIG.gisDefaultPoi;
    basemapGallary = new BasemapGallary({
      view,
      container: document.createElement('div'),
      source: [
        {
          id: 'dark_vec_w',
          title: '暗色',
          baseLayers: [vec_w_dark, ...(showPoi ? [cva_w] : [])],
          // referenceLayers: showPoi ? [cva_w] : [],
          thumbnailUrl: getGisPics('dark.png')
        },
        {
          id: 'vec_w',
          title: '矢量',
          baseLayers: [vec_w, ...(showPoi ? [cva_w] : [])],
          // referenceLayers: showPoi ? [cva_w] : [],
          thumbnailUrl: getGisPics('vect.png')
        },
        {
          id: 'img_w',
          title: '影像',
          baseLayers: [img_w, ...(showPoi ? [cia_w] : [])],
          // referenceLayers: showPoi ? [cia_w] : [],
          thumbnailUrl: getGisPics('img.png')
        }
      ]
    });
    bgExpand = new Expand({
      view,
      content: basemapGallary,
      expanded: false,
      expandTooltip: '切换底图'
    });
    basemapGallary?.watch('activeBasemap', (e) => {
      onChange?.(e);
      bgExpand?.collapse();
    });
    bgExpand && view?.ui.add(bgExpand, widgetPosition || 'bottom-right');
    return bgExpand;
  };
  const destroy = () => {
    basemapGallary?.destroy();
    bgExpand?.destroy();
  };
  onBeforeUnmount(() => {
    destroy();
  });
  return {
    init
  };
};
export default useBasemapGallary;
