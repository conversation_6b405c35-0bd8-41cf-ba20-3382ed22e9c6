package org.thingsboard.server.dao.gis;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.request.GisProjectListRequest;
import org.thingsboard.server.dao.model.sql.gis.GisProject;
import org.thingsboard.server.dao.sql.gis.GisProjectMapper;

import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class GisProjectServiceImpl implements GisProjectService {

    @Autowired
    private GisProjectMapper gisProjectMapper;

    @Override
    public void save(GisProject entity) {
        if (StringUtils.isBlank(entity.getId())) {
            entity.setCreateTime(new Date());
            gisProjectMapper.insert(entity);
        } else {
            gisProjectMapper.updateById(entity);
        }
    }

    @Override
    public PageData<GisProject> findList(GisProjectListRequest request, TenantId tenantId) {
        Page<GisProject> pageRequest = new Page<>(request.getPage(), request.getSize());
        request.setTenantId(UUIDConverter.fromTimeUUID(tenantId.getId()));

        IPage<GisProject> pageResult = gisProjectMapper.findList(pageRequest, request);

        return new PageData<>(pageResult.getTotal(), pageResult.getRecords());
    }

    @Override
    public void remove(List<String> ids) {
        gisProjectMapper.deleteBatchIds(ids);
    }
}
