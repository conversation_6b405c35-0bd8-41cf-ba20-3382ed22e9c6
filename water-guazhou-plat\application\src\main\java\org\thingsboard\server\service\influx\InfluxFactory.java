package org.thingsboard.server.service.influx;

import com.influxdb.client.InfluxDBClient;
import com.influxdb.client.InfluxDBClientFactory;
import com.influxdb.client.flux.FluxClient;
import com.influxdb.client.flux.FluxClientFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.thingsboard.server.common.data.DataConstants;

import javax.annotation.PostConstruct;

/**
 * <AUTHOR>
 * @date 2020/8/3 21:45
 */

@Component
public class InfluxFactory {

//    private static char[] token = "thENZcFOP3aoDScB_UNN4udxcpMAVz9qfiWtXXydR9a9PD0uqD-y0k3luo9oh38fO9SW_xTd9B90CbaP4j9SpQ==".toCharArray();
//        private static char[] token = "NFKpUw3-uhOIs_ajtfypo9p7GBe9pgpw7i4ETGMN-Boh-Va1e-BMeQZ7uq1EPa8wK0TMEqnH0kr5nI5E4NacJg==".toCharArray();// 黄平124
//    private static char[] token = "OTfbRctitzvqBvBJhlXFnKQafb3vdSbi945-9ouJZs6RhTUHzrspvFaWlh0cNV_Y_aN1CCxviSy5e1p5z_WOwg==".toCharArray();// 化德
//    private static String org = "istar";

    @Value("${influx.ip}")
    private String ipValue;

    @Value("${influx.port}")
    private String portValue;

    @Value("${influx.token}")
    private String tokenValue;

    @Value("${influx.org}")
    private String orgValue;

    private static char[] token;
    private static String org;
    private static String ip;
    private static String port;

    @PostConstruct
    public void setValue() {
        token = this.tokenValue.toCharArray();
        org = this.orgValue;
        ip = this.ipValue;
        port = this.portValue;
    }


    //flux 查询实例
    private static FluxClient fluxClient;
    //influx 数据存储实例
    private static InfluxDBClient influxDBClient;


    /**
     * 初始化flux实例
     * @return
     */
    public static FluxClient createFluxClient() {
        if (fluxClient == null || !fluxClient.ping()) {
            return FluxClientFactory.create(ip + ":" + port + "?readTimeout=10000&connectTimeout=10000&logLevel=NONE");
        } else {
            return fluxClient;
        }
    }

    /**
     * 初始化influx实例
     * @return
     */
    public static InfluxDBClient createInFluxClient() {
        if (influxDBClient == null) {
            return InfluxDBClientFactory.create(ip + ":" + port, token, org, DataConstants.INFLUX_DEVICE_DATA);
        } else {
            return influxDBClient;
        }
    }
}
