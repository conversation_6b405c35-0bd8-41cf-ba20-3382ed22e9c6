<!-- 专家库 -->
<template>
  <div class="wrapper">
    <CardSearch ref="refSearch" :config="cardSearchConfig" />
    <div style="height: calc(100% - 100px)">
      <el-row :gutter="20">
        <el-col
          v-for="(item, i) in config"
          :key="i"
          class="mag_bot_20"
          :span="6"
        >
          <itemCard
            :config="item"
            @openDetail="onClick"
            @edit="onClick"
            @del="onClick"
          />
        </el-col>
      </el-row>
    </div>
    <SLDrawer ref="refForm" :config="addorUpdateConfig"></SLDrawer>
    <SLDrawer ref="refDetail" :config="DetailConfig">
      <Detail :config="onConfig"></Detail>
    </SLDrawer>
  </div>
</template>

<script lang="ts" setup>
import { Refresh, Plus } from '@element-plus/icons-vue';
import { ICONS } from '@/common/constans/common';
import itemCard from './components/itemCard.vue';
import Detail from './components/detail.vue';
import {
  expertList,
  addExpert,
  editExpert,
  delExpert
} from '@/api/server/index';
import {
  GeneralProcessing,
  GeneralTable,
  UniversalDelete
} from '@/utils/GeneralProcessing';

const refForm = ref<ISLDrawerIns>();
const refDetail = ref<ISLDrawerIns>();
const refSearch = ref<ICardSearchIns>();

const onConfig = ref();

const config = ref<any[]>([]);

const cardSearchConfig = ref<ISearch>({
  filters: [
    { label: '专家姓名', field: 'name', type: 'input' },
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          icon: ICONS.QUERY,
          click: () => refreshData(true)
        },
        {
          type: 'default',
          perm: true,
          text: '重置',
          svgIcon: shallowRef(Refresh),
          click: () => {
            refSearch.value?.resetForm();
            refreshData();
          }
        },
        {
          type: 'success',
          perm: true,
          text: '新增',
          svgIcon: shallowRef(Plus),
          click: () => {
            addorUpdateConfig.defaultValue = { gender: '男' };
            refForm.value?.openDrawer();
          }
        }
      ]
    }
  ]
});

const addorUpdateConfig = reactive<IDrawerConfig>({
  title: '新增',
  labelWidth: '100px',
  submit: (params: any) => {
    GeneralProcessing(params, addExpert, editExpert, addorUpdateConfig).then(
      () => {
        refForm.value?.closeDrawer();
        refreshData();
      }
    );
  },
  defaultValue: {},
  group: [
    {
      fields: [
        {
          type: 'divider',
          text: '基本信息'
        },
        {
          xl: 8,
          type: 'input',
          label: '姓名',
          field: 'name',
          rules: [{ required: true, message: '请输入姓名' }]
        },
        {
          xl: 8,
          type: 'switch',
          label: '性别',
          field: 'gender',
          activeText: '男',
          inActiveText: '女',
          activeValue: '男',
          inActiveValue: '女',
          inActiveColor: '#66b1ff'
        },
        {
          xl: 8,
          type: 'input-number',
          label: '联系电话',
          field: 'phone',
          rules: [{ required: true, message: '请输入联系电话' }]
        },
        {
          xl: 8,
          type: 'input',
          label: '联系邮箱',
          field: 'email',
          rules: [{ required: true, message: '请输入联系邮箱' }]
        },
        {
          xl: 8,
          type: 'input',
          label: '最高学历',
          field: 'educationLevel',
          rules: [{ required: true, message: '请输入最高学历' }]
        },
        {
          type: 'divider',
          text: '行业领域'
        },
        {
          xl: 8,
          type: 'input',
          label: '职称',
          field: 'professionalTitle',
          rules: [{ required: true, message: '请输入职称' }]
        },
        {
          xl: 8,
          type: 'date',
          label: '职称评定时间',
          field: 'professionalTitleTime'
        },
        {
          xl: 8,
          type: 'input',
          label: '行业领域',
          field: 'industrySector',
          rules: [{ required: true, message: '请输入行业领域' }]
        },
        {
          xl: 8,
          type: 'date',
          label: '行业从事时间',
          field: 'industryTime'
        },
        {
          xl: 8,
          type: 'input',
          label: '工作单位',
          field: 'deptName',
          rules: [{ required: true, message: '请输入工作单位' }]
        },
        {
          xl: 8,
          type: 'input-number',
          label: '单位电话',
          field: 'deptPhone',
          rules: [{ required: true, message: '请输入单位电话' }]
        },
        {
          type: 'divider',
          text: '工作经历'
        },
        {
          type: 'wangeditor',
          label: '工作经历',
          field: 'jobHistory'
        },
        {
          type: 'divider',
          text: '所获荣誉'
        },
        {
          type: 'wangeditor',
          label: '所获荣誉',
          field: 'honorHistory'
        },
        {
          type: 'divider',
          text: '学术成就'
        },
        {
          type: 'wangeditor',
          label: '学术成就',
          field: 'academicHistory'
        }
      ]
    }
  ]
});

const DetailConfig = reactive<IDrawerConfig>({
  title: '详情',
  labelWidth: '100px',
  submit: (params: any) => {
    //
  },

  defaultValue: {},
  group: [
    {
      fields: []
    }
  ]
});

const onClick = (type, val) => {
  switch (type) {
    case 0:
      addorUpdateConfig.defaultValue = { ...val };
      refForm.value?.openDrawer();
      break;
    case 1:
      onConfig.value = { ...val };
      refDetail.value?.openDrawer();
      break;
    case 2:
      UniversalDelete(val.id, delExpert, '确定删除该专家信息').then(() => {
        refreshData();
      });
      break;
    default:
      break;
  }
};

const refreshData = async (serch?: boolean) => {
  const params = {
    page: 1,
    size: 9999,
    ...(refSearch.value?.queryParams || {})
  };
  GeneralTable(params, expertList).then((res) => {
    config.value = res.data;
  });
};

onMounted(async () => {
  refreshData();
});
</script>
<style lang="scss" scoped>
.card-table {
  height: calc(100% - 100px);
}

.mag_bot_20 {
  margin-bottom: 20px;
}
</style>

<style scoped>
.card-table :deep(.el-table__expand-icon--expanded) {
  width: 6px;
}
</style>
