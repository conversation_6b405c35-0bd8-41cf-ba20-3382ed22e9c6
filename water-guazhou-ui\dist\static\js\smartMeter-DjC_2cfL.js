import{d as z,c as S,r as x,s as N,o as E,am as G,j as O,g as W,n as Y,q as D,i as y,t as H,p as J,_ as K,aq as Q,C as X}from"./index-r0dFAfgr.js";import{w as Z}from"./Point-WxyopZva.js";import{a as aa}from"./onemap-CEunQziB.js";import{P as U,C as ta}from"./index-CcDafpIP.js";import{r as j}from"./chart-wy3NEK2T.js";import{g as ea}from"./URLHelper-B9aplt5w.js";import{b as sa}from"./useStation-DJgnSZIA.js";import"./zhandian-YaGuQZe6.js";const na={class:"onemap-panel-wrapper"},la={class:"table-box"},oa=z({__name:"smartMeter",props:{view:{},menu:{}},emits:["highlightMark","addMarks"],setup(q,{emit:A}){const F=A,C=q,c=S([{label:"0 个",value:"智能水表总数"},{label:"-- %",value:"报警率"}]),w=S(),f=[{name:"offline",label:"离线"},{name:"alarm",label:"报警"},{name:"online",label:"正常"}],k=x({group:[{id:"chart",fieldset:{desc:"监测状态统计",type:"underline",style:{marginTop:0}},fields:[{type:"vchart",option:j(),style:{height:"150px"}}]},{id:"tab",fields:[{type:"input",field:"name",appendBtns:[{perm:!0,text:"刷新",click:()=>r()}],onChange:()=>r()},{type:"tabs",field:"type",tabs:[{label:"全部",value:"all"},...f.map(t=>({...t,value:t.name}))],tabType:"simple",onChange:()=>r()}]}],labelPosition:"top",gutter:12,defaultValue:{type:"all"}}),i=x({indexVisible:!0,dataList:[],pagination:{hide:!0,refreshData:({page:t,size:n})=>{i.pagination.page=t,i.pagination.limit=n},layout:"total,sizes, jumper"},handleRowClick:t=>$(t),columns:[{width:200,label:"名称",prop:"name",sortable:!0},{width:80,label:"状态",prop:"status",formatter:t=>{var n;return((n=f.find(p=>p.name===t.status))==null?void 0:n.label)||t.status}},{width:160,label:"更新时间",prop:"lastTime",sortable:!0}]}),I=x({dataList:[],columns:[],pagination:{refreshData:({page:t,size:n})=>{I.pagination.page=t,I.pagination.limit=n,r()}}});sa();const r=async t=>{var n,p,g,b,T,M,V,R,L;i.loading=!0;try{const h=(n=w.value)==null?void 0:n.dataForm.type,v=await aa({name:(p=w.value)==null?void 0:p.dataForm.name,status:h==="all"?"":h});i.dataList=((g=v.data)==null?void 0:g.data)||[];const B=k.group[0].fields[0],_=((T=(b=v.data)==null?void 0:b.data)==null?void 0:T.length)||0,P=[],u=[];if((V=(M=v.data)==null?void 0:M.data)!=null&&V.map(a=>{var d,m;const e=(d=a.location)==null?void 0:d.split(",");if((e==null?void 0:e.length)===2){const l=new Z({longitude:e[0],latitude:e[1],spatialReference:(m=C.view)==null?void 0:m.spatialReference});P.push({visible:!1,x:l.x,y:l.y,offsetY:-40,id:a.stationId,title:a.name,customComponent:N(U),customConfig:{info:{type:"attrs",imageUrl:a.imgs,stationId:a.stationId}},attributes:{path:C.menu.path,id:a.stationId,row:a},symbolConfig:{url:ea("测压点.png")}})}let s=u.find(l=>l.name===a.status);const{label:o}=f.find(l=>l.name===a.status)||{};s?s.value++:(s={name:a.status,nameAlias:o,value:1,scale:"0%"},u.push(s))}),u.map(a=>{var e,s,o;return a.scale=_===0?"0%":(Number(a.value)/_*100).toFixed(2)+"%",a.value=((o=(s=(e=v.data)==null?void 0:e.data)==null?void 0:s.filter(d=>d.status===a.name))==null?void 0:o.length)||0,a}),t){const a=(R=k.group.find(e=>e.id==="tab"))==null?void 0:R.fields[1];if(a){a.tabs=a.tabs.map(s=>{var m;const o=u.find(l=>l.name===s.value),d=((m=f.find(l=>l.name===s.value))==null?void 0:m.label)||"";return s.label=d+"("+((o==null?void 0:o.value)||0)+")",s});const e=a.tabs.find(s=>s.value==="all");e&&(e.label="全部("+_+")"),B&&(B.option=j(u,"个","",0))}c.value[0].label=_+"个",c.value[1].label=((L=u.find(e=>e.name==="alarm"))==null?void 0:L.scale)||"-- %"}F("addMarks",{windows:P,customWinComp:N(U)})}catch(h){console.dir(h)}i.loading=!1},$=async t=>{F("highlightMark",C.menu,t==null?void 0:t.stationId)};return E(()=>{r(!0)}),G(()=>O().isDark,()=>r(!0)),(t,n)=>{const p=K,g=Q;return W(),Y("div",na,[D(y(ta),{modelValue:y(c),"onUpdate:modelValue":n[0]||(n[0]=b=>H(c)?c.value=b:null),span:12},null,8,["modelValue"]),D(p,{ref_key:"refForm",ref:w,config:y(k)},null,8,["config"]),J("div",la,[D(g,{config:y(i)},null,8,["config"])])])}}}),ga=X(oa,[["__scopeId","data-v-6803456a"]]);export{ga as default};
