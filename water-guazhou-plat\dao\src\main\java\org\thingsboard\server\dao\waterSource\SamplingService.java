package org.thingsboard.server.dao.waterSource;

import org.springframework.web.multipart.MultipartFile;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.sampling.Sampling;

import java.util.List;
import java.util.Map;

/**
 * 采样记录服务接口
 */
public interface SamplingService {

    /**
     * 获取采样记录列表
     */
    PageData<Sampling> getList(Map<String, Object> params, String tenantId);

    /**
     * 保存采样记录
     */
    Sampling save(Sampling entity);

    /**
     * 批量删除采样记录
     */
    void delete(List<String> idList);

    /**
     * 获取单个采样记录
     */
    Sampling getById(String id);

    /**
     * 上传采样记录文件
     */
    Sampling uploadRecord(Sampling sampling);
}
