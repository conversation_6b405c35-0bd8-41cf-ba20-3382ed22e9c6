import{d as P,c as q,r as x,bH as N,b as J,o as L,g as R,n as k,q as $,i as M,aq as A,C as H}from"./index-r0dFAfgr.js";import{GetFieldConfig as Q}from"./fieldconfig-Bk3o1wi7.js";import{f as j}from"./DateFormatter-Bm9a68Ax.js";import{s as F,g as V}from"./FeatureHelper-Da16o0mu.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./MapView-DaoQedLH.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./Point-WxyopZva.js";import{q as W,g as z}from"./LayerHelper-Cn-iiqxI.js";import{a as v,i as G,d as K}from"./QueryHelper-ILO3qZqg.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./geometryEngineBase-BhsKaODW.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./project-DUuzYgGl.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./pipe-nogVzCHG.js";const U={class:"table-box"},X=P({__name:"PipeDetail",emits:["refreshed","close","refreshing","rowclick","mounted"],setup(Y,{expose:O,emit:S}){const f=S,t={tabFeatures:[]},g=q(),e=x({dataList:[],columns:[{label:"OBJECTID",prop:"OBJECTID"}],height:"none",handleRowClick:o=>{var r;e.currentRow=o,t.view&&T(t.view,(r=g.value)==null?void 0:r.queryParams.type,o.OBJECTID)},pagination:{refreshData:({page:o,size:r})=>{e.pagination.page=o,e.pagination.limit=r,_(t.view,t.tab,t.queryParams,!0)}}}),y=()=>{e.dataList=[],e.loading=!1},_=async(o,r,p,m)=>{var n,l,d,I,b,C;m||(t.view=o,t.tab=r,t.queryParams=p);try{if(e.loading=!0,!t.tab){y();return}const s=await W(t.tab);if(s===void 0||s===-1){y();return}m||(t.allRes=await v(`${window.SITE_CONFIG.GIS_CONFIG.gisService+window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService}/${s}`,G({orderByFields:["OBJECTID asc"],outFields:["OBJECTID"],returnGeometry:!0,...t.queryParams||{}})));const c=((n=t.allRes)==null?void 0:n.features.map(i=>i.attributes.OBJECTID))||[],E=await Q(t.tab);t.fieldConfig=(d=(l=E.data)==null?void 0:l.result)==null?void 0:d.rows,e.pagination.total=c.length||0;const w=K(c,e.pagination.page||1,e.pagination.limit||20),h=[];if(w.length){const i=await v(`${window.SITE_CONFIG.GIS_CONFIG.gisService+window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService}/${s}`,G({orderByFields:["OBJECTID asc"],outFields:["*"],objectIds:w||[],returnGeometry:!0,...t.queryParams||{where:"1=1"}}));i.features.map(a=>{h.push(a.attributes),a.symbol=F(a.geometry.type)});const u=[];i.fields.map(a=>{const D={label:a.alias,prop:a.name,minWidth:160};a.type==="date"&&(D.formatter=B=>j(B[a.name],N)),u.push(D)}),e.columns=u}if(e.dataList=h,c!=null&&c.length){const i=z(t.view,{id:"pipe-detail",title:"详情展示"});i&&(i.removeAll(),(I=t.allRes)==null||I.features.map(u=>u.symbol=F(u.geometry.type)),i.addMany(((b=t.allRes)==null?void 0:b.features)||[]),t.tabFeatures=((C=t.allRes)==null?void 0:C.features)||[])}}catch(s){console.dir(s),J.error("查询失败")}e.loading=!1,f("refreshed")},T=async(o,r,p)=>{var n,l;r=r||((l=(n=g.value)==null?void 0:n.queryParams)==null?void 0:l.type);const m=t.tabFeatures.find(d=>d.attributes.OBJECTID===p);m&&await V(o,m)};return L(()=>{f("mounted")}),O({refreshDetail:_}),(o,r)=>{const p=A;return R(),k("div",U,[$(p,{config:M(e)},null,8,["config"])])}}}),ge=H(X,[["__scopeId","data-v-98b6644e"]]);export{ge as default};
