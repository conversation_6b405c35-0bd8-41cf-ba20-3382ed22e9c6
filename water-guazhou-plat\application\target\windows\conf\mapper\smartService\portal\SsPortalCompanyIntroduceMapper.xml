<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartService.portal.SsPortalCompanyIntroduceMapper">
    <sql id="Base_Column_List">
        <!--@sql select-->
        id,
        introduce,
        detail,
        tenant_id
        <!--@sql from ss_portal_company_introduce -->
    </sql>
    <resultMap id="BaseResultMap"
               type="org.thingsboard.server.dao.model.sql.smartService.portal.SsPortalCompanyIntroduce">
        <result column="id" property="id"/>
        <result column="introduce" property="introduce"/>
        <result column="detail" property="detail"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <select id="getByTenantId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ss_portal_company_introduce
        where tenant_id = #{tenantId}
    </select>

    <update id="updateFully">
        update ss_portal_company_introduce
        set introduce = #{introduce},
            detail    = #{detail}
        where id = #{id}
    </update>

    <insert id="save">
        INSERT INTO ss_portal_company_introduce(id,
                                                introduce,
                                                detail,
                                                tenant_id)
        VALUES (#{id},
                #{introduce},
                #{detail},
                #{tenantId})
        on conflict(tenant_id) do update
            set introduce = #{introduce},
                detail    = #{detail}
    </insert>
</mapper>