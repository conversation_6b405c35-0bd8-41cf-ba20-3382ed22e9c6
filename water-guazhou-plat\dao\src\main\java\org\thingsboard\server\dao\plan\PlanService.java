package org.thingsboard.server.dao.plan;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.plan.Plan;
import org.thingsboard.server.dao.util.imodel.query.plan.PlanPageRequest;
import org.thingsboard.server.dao.util.imodel.query.plan.PlanSaveRequest;

public interface PlanService {
    /**
     * 分页条件查询盘点计划
     *
     * @param request 分页查询条件
     * @return 分页查询结果
     */
    IPage<Plan> findAllConditional(PlanPageRequest request);

    /**
     * 保存盘点计划
     *
     * @param entity 实体信息
     * @return 保存好的实体
     */
    Plan save(PlanSaveRequest entity);

    /**
     * 增量修改
     *
     * @param entity 实体信息
     * @return 是否修改成功
     */
    boolean update(Plan entity);

    /**
     * 删除
     *
     * @param id 唯一标识
     * @return 是否删除成功
     */
    boolean delete(String id);

}
