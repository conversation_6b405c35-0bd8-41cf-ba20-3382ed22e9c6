/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.sql.lot;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Component;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.lot.LotCard;
import org.thingsboard.server.dao.DaoUtil;
import org.thingsboard.server.dao.lot.LotCardDao;
import org.thingsboard.server.dao.model.sql.LotCardEntity;
import org.thingsboard.server.dao.sql.JpaAbstractDao;
import org.thingsboard.server.dao.util.SqlDao;

import java.util.List;

@Component
@SqlDao
@Slf4j
public class JpaLotCardDao extends JpaAbstractDao<LotCardEntity, LotCard> implements LotCardDao {

    @Autowired
    private LotCardRepository lotCardRepository;


    @Override
    protected Class<LotCardEntity> getEntityClass() {
        return LotCardEntity.class;
    }

    @Override
    protected CrudRepository<LotCardEntity, String> getCrudRepository() {
        return lotCardRepository;
    }


    @Override
    public List<LotCard> findByTenantId(TenantId tenantId) {
        return DaoUtil.convertDataList(lotCardRepository.findByTenantId(UUIDConverter.fromTimeUUID(tenantId.getId())));
    }
}
