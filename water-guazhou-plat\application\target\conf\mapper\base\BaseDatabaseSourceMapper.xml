<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.base.BaseDatabaseSourceMapper">
    
    <resultMap type="org.thingsboard.server.dao.model.sql.base.BaseDatabaseSource" id="BaseDatabaseSourceResult">
        <result property="id"    column="id"    />
        <result property="scName"    column="sc_name"    />
        <result property="dbType"    column="db_type"    />
        <result property="dbHost"    column="db_host"    />
        <result property="dbPort"    column="db_port"    />
        <result property="dbName"    column="db_name"    />
        <result property="dbUsername"    column="db_username"    />
        <result property="password"    column="password"    />
        <result property="dbMaxPoolSize"    column="db_max_pool_size"    />
        <result property="minIdle"    column="min_idle"    />
    </resultMap>

    <sql id="selectBaseDatabaseSourceVo">
        select id, sc_name, db_type, db_host, db_port, db_name, db_username, password, db_max_pool_size, min_idle from base_database_source
    </sql>

    <select id="selectBaseDatabaseSourceList" parameterType="org.thingsboard.server.dao.model.sql.base.BaseDatabaseSource" resultMap="BaseDatabaseSourceResult">
        <include refid="selectBaseDatabaseSourceVo"/>
        <where>  
            <if test="scName != null  and scName != ''"> and sc_name like concat('%', #{scName}, '%')</if>
            <if test="dbType != null  and dbType != ''"> and db_type = #{dbType}</if>
            <if test="dbHost != null  and dbHost != ''"> and db_host = #{dbHost}</if>
            <if test="dbPort != null  and dbPort != ''"> and db_port = #{dbPort}</if>
            <if test="dbName != null  and dbName != ''"> and db_name like concat('%', #{dbName}, '%')</if>
            <if test="dbUsername != null  and dbUsername != ''"> and db_username like concat('%', #{dbUsername}, '%')</if>
            <if test="password != null  and password != ''"> and password = #{password}</if>
            <if test="dbMaxPoolSize != null "> and db_max_pool_size = #{dbMaxPoolSize}</if>
            <if test="minIdle != null "> and min_idle = #{minIdle}</if>
        </where>
    </select>
    
    <select id="selectBaseDatabaseSourceById" parameterType="String" resultMap="BaseDatabaseSourceResult">
        <include refid="selectBaseDatabaseSourceVo"/>
        where id = #{id}
    </select>

    <insert id="insertBaseDatabaseSource" parameterType="org.thingsboard.server.dao.model.sql.base.BaseDatabaseSource">
        insert into base_database_source
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="scName != null">sc_name,</if>
            <if test="dbType != null">db_type,</if>
            <if test="dbHost != null">db_host,</if>
            <if test="dbPort != null">db_port,</if>
            <if test="dbName != null">db_name,</if>
            <if test="dbUsername != null">db_username,</if>
            <if test="password != null">password,</if>
            <if test="dbMaxPoolSize != null">db_max_pool_size,</if>
            <if test="minIdle != null">min_idle,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="scName != null">#{scName},</if>
            <if test="dbType != null">#{dbType},</if>
            <if test="dbHost != null">#{dbHost},</if>
            <if test="dbPort != null">#{dbPort},</if>
            <if test="dbName != null">#{dbName},</if>
            <if test="dbUsername != null">#{dbUsername},</if>
            <if test="password != null">#{password},</if>
            <if test="dbMaxPoolSize != null">#{dbMaxPoolSize},</if>
            <if test="minIdle != null">#{minIdle},</if>
         </trim>
    </insert>

    <update id="updateBaseDatabaseSource" parameterType="org.thingsboard.server.dao.model.sql.base.BaseDatabaseSource">
        update base_database_source
        <trim prefix="SET" suffixOverrides=",">
            <if test="scName != null">sc_name = #{scName},</if>
            <if test="dbType != null">db_type = #{dbType},</if>
            <if test="dbHost != null">db_host = #{dbHost},</if>
            <if test="dbPort != null">db_port = #{dbPort},</if>
            <if test="dbName != null">db_name = #{dbName},</if>
            <if test="dbUsername != null">db_username = #{dbUsername},</if>
            <if test="password != null">password = #{password},</if>
            <if test="dbMaxPoolSize != null">db_max_pool_size = #{dbMaxPoolSize},</if>
            <if test="minIdle != null">min_idle = #{minIdle},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBaseDatabaseSourceById" parameterType="String">
        delete from base_database_source where id = #{id}
    </delete>

    <delete id="deleteBaseDatabaseSourceByIds" parameterType="String">
        delete from base_database_source where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>