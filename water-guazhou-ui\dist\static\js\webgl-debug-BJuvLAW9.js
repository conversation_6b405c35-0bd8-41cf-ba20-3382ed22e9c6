function z(s){return window.WebGL2RenderingContext&&s instanceof window.WebGL2RenderingContext}let G=class{constructor(h,i,u,F,B,I,U,L,C){this.createQuery=h,this.deleteQuery=i,this.resultAvailable=u,this.getResult=F,this.disjoint=B,this.beginTimeElapsed=I,this.endTimeElapsed=U,this.createTimestamp=L,this.timestampBits=C}},x=!1;function re(s,h){if(h.disjointTimerQuery)return null;let i=s.getExtension("EXT_disjoint_timer_query_webgl2");return i&&z(s)?new G(()=>s.createQuery(),u=>{s.deleteQuery(u),x=!1},u=>s.getQueryParameter(u,s.QUERY_RESULT_AVAILABLE),u=>s.getQueryParameter(u,s.QUERY_RESULT),()=>s.getParameter(i.GPU_DISJOINT_EXT),u=>{x||(x=!0,s.beginQuery(i.TIME_ELAPSED_EXT,u))},()=>{s.endQuery(i.TIME_ELAPSED_EXT),x=!1},u=>i.queryCounterEXT(u,i.TIMESTAMP_EXT),()=>s.getQuery(i.TIMESTAMP_EXT,i.QUERY_COUNTER_BITS_EXT)):(i=s.getExtension("EXT_disjoint_timer_query"),i?new G(()=>i.createQueryEXT(),u=>{i.deleteQueryEXT(u),x=!1},u=>i.getQueryObjectEXT(u,i.QUERY_RESULT_AVAILABLE_EXT),u=>i.getQueryObjectEXT(u,i.QUERY_RESULT_EXT),()=>s.getParameter(i.GPU_DISJOINT_EXT),u=>{x||(x=!0,i.beginQueryEXT(i.TIME_ELAPSED_EXT,u))},()=>{i.endQueryEXT(i.TIME_ELAPSED_EXT),x=!1},u=>i.queryCounterEXT(u,i.TIMESTAMP_EXT),()=>i.getQueryEXT(i.TIMESTAMP_EXT,i.QUERY_COUNTER_BITS_EXT)):null)}var Q,W,k={},ee={get exports(){return k},set exports(s){k=s}};Q=ee,(W=function(){var s=function(e){window.console&&window.console.log&&window.console.log(e)},h=function(e){window.console&&window.console.error?window.console.error(e):s(e)},i={enable:{1:{0:!0}},disable:{1:{0:!0}},getParameter:{1:{0:!0}},drawArrays:{3:{0:!0}},drawElements:{4:{0:!0,2:!0}},createShader:{1:{0:!0}},getShaderParameter:{2:{1:!0}},getProgramParameter:{2:{1:!0}},getShaderPrecisionFormat:{2:{0:!0,1:!0}},getVertexAttrib:{2:{1:!0}},vertexAttribPointer:{6:{2:!0}},bindTexture:{2:{0:!0}},activeTexture:{1:{0:!0}},getTexParameter:{2:{0:!0,1:!0}},texParameterf:{3:{0:!0,1:!0}},texParameteri:{3:{0:!0,1:!0,2:!0}},texImage2D:{9:{0:!0,2:!0,6:!0,7:!0},6:{0:!0,2:!0,3:!0,4:!0}},texSubImage2D:{9:{0:!0,6:!0,7:!0},7:{0:!0,4:!0,5:!0}},copyTexImage2D:{8:{0:!0,2:!0}},copyTexSubImage2D:{8:{0:!0}},generateMipmap:{1:{0:!0}},compressedTexImage2D:{7:{0:!0,2:!0}},compressedTexSubImage2D:{8:{0:!0,6:!0}},bindBuffer:{2:{0:!0}},bufferData:{3:{0:!0,2:!0}},bufferSubData:{3:{0:!0}},getBufferParameter:{2:{0:!0,1:!0}},pixelStorei:{2:{0:!0,1:!0}},readPixels:{7:{4:!0,5:!0}},bindRenderbuffer:{2:{0:!0}},bindFramebuffer:{2:{0:!0}},checkFramebufferStatus:{1:{0:!0}},framebufferRenderbuffer:{4:{0:!0,1:!0,2:!0}},framebufferTexture2D:{5:{0:!0,1:!0,2:!0}},getFramebufferAttachmentParameter:{3:{0:!0,1:!0,2:!0}},getRenderbufferParameter:{2:{0:!0,1:!0}},renderbufferStorage:{4:{0:!0,1:!0}},clear:{1:{0:{enumBitwiseOr:["COLOR_BUFFER_BIT","DEPTH_BUFFER_BIT","STENCIL_BUFFER_BIT"]}}},depthFunc:{1:{0:!0}},blendFunc:{2:{0:!0,1:!0}},blendFuncSeparate:{4:{0:!0,1:!0,2:!0,3:!0}},blendEquation:{1:{0:!0}},blendEquationSeparate:{2:{0:!0,1:!0}},stencilFunc:{3:{0:!0}},stencilFuncSeparate:{4:{0:!0,1:!0}},stencilMaskSeparate:{2:{0:!0}},stencilOp:{3:{0:!0,1:!0,2:!0}},stencilOpSeparate:{4:{0:!0,1:!0,2:!0,3:!0}},cullFace:{1:{0:!0}},frontFace:{1:{0:!0}},drawArraysInstancedANGLE:{4:{0:!0}},drawElementsInstancedANGLE:{5:{0:!0,2:!0}},blendEquationEXT:{1:{0:!0}}},u=null,F=null;function B(e){if(u==null)for(var r in u={},F={},e)typeof e[r]=="number"&&(u[e[r]]=r,F[r]=e[r])}function I(){if(u==null)throw"WebGLDebugUtils.init(ctx) not called"}function U(e){return I(),u[e]!==void 0}function L(e){I();var r=u[e];return r!==void 0?"gl."+r:"/*UNKNOWN WebGL ENUM*/ 0x"+e.toString(16)}function C(e,r,c,a){var l;if((l=i[e])!==void 0&&(l=l[r])!==void 0&&l[c]){if(typeof l[c]=="object"&&l[c].enumBitwiseOr!==void 0){for(var o=l[c].enumBitwiseOr,b=0,f=[],S=0;S<o.length;++S){var E=F[o[S]];a&E&&(b|=E,f.push(L(E)))}return b===a?f.join(" | "):L(a)}return L(a)}return a===null?"null":a===void 0?"undefined":a.toString()}function Y(e,r){for(var c="",a=r.length,l=0;l<a;++l)c+=(l==0?"":", ")+C(e,a,l,r[l]);return c}function O(e,r,c){e.__defineGetter__(c,function(){return r[c]}),e.__defineSetter__(c,function(a){r[c]=a})}function N(e,r,c,a){a=a||e,B(e),r=r||function(E,_,p){for(var m="",R=p.length,P=0;P<R;++P)m+=(P==0?"":", ")+C(_,R,P,p[P]);h("WebGL error "+L(E)+" in "+_+"("+m+")")};var l={};function o(E,_){return function(){c&&c(_,arguments);var p=E[_].apply(E,arguments),m=a.getError();return m!=0&&(l[m]=!0,r(m,_,arguments)),p}}var b={};for(var f in e)if(typeof e[f]=="function")if(f!="getExtension")b[f]=o(e,f);else{var S=o(e,f);b[f]=function(){return N(S.apply(e,arguments),r,c,a)}}else O(b,e,f);return b.getError=function(){for(var E in l)if(l.hasOwnProperty(E)&&l[E])return l[E]=!1,E;return e.NO_ERROR},b}function w(e){var r=e.getParameter(e.MAX_VERTEX_ATTRIBS),c=e.createBuffer();e.bindBuffer(e.ARRAY_BUFFER,c);for(var a=0;a<r;++a)e.disableVertexAttribArray(a),e.vertexAttribPointer(a,4,e.FLOAT,!1,0,0),e.vertexAttrib1f(a,0);e.deleteBuffer(c);var l=e.getParameter(e.MAX_TEXTURE_IMAGE_UNITS);for(a=0;a<l;++a)e.activeTexture(e.TEXTURE0+a),e.bindTexture(e.TEXTURE_CUBE_MAP,null),e.bindTexture(e.TEXTURE_2D,null);for(e.activeTexture(e.TEXTURE0),e.useProgram(null),e.bindBuffer(e.ARRAY_BUFFER,null),e.bindBuffer(e.ELEMENT_ARRAY_BUFFER,null),e.bindFramebuffer(e.FRAMEBUFFER,null),e.bindRenderbuffer(e.RENDERBUFFER,null),e.disable(e.BLEND),e.disable(e.CULL_FACE),e.disable(e.DEPTH_TEST),e.disable(e.DITHER),e.disable(e.SCISSOR_TEST),e.blendColor(0,0,0,0),e.blendEquation(e.FUNC_ADD),e.blendFunc(e.ONE,e.ZERO),e.clearColor(0,0,0,0),e.clearDepth(1),e.clearStencil(-1),e.colorMask(!0,!0,!0,!0),e.cullFace(e.BACK),e.depthFunc(e.LESS),e.depthMask(!0),e.depthRange(0,1),e.frontFace(e.CCW),e.hint(e.GENERATE_MIPMAP_HINT,e.DONT_CARE),e.lineWidth(1),e.pixelStorei(e.PACK_ALIGNMENT,4),e.pixelStorei(e.UNPACK_ALIGNMENT,4),e.pixelStorei(e.UNPACK_FLIP_Y_WEBGL,!1),e.pixelStorei(e.UNPACK_PREMULTIPLY_ALPHA_WEBGL,!1),e.UNPACK_COLORSPACE_CONVERSION_WEBGL&&e.pixelStorei(e.UNPACK_COLORSPACE_CONVERSION_WEBGL,e.BROWSER_DEFAULT_WEBGL),e.polygonOffset(0,0),e.sampleCoverage(1,!1),e.scissor(0,0,e.canvas.width,e.canvas.height),e.stencilFunc(e.ALWAYS,0,4294967295),e.stencilMask(4294967295),e.stencilOp(e.KEEP,e.KEEP,e.KEEP),e.viewport(0,0,e.canvas.width,e.canvas.height),e.clear(e.COLOR_BUFFER_BIT|e.DEPTH_BUFFER_BIT|e.STENCIL_BUFFER_BIT);e.getError(););}function K(e){var r,c,a=[],l=[],o={},b=1,f=!1,S=[],E=0,_=0,p=!1,m=0,R={};function P(t){return typeof t=="function"?t:function(n){t.handleEvent(n)}}e.getContext=(c=e.getContext,function(){var t=c.apply(e,arguments);if(t instanceof WebGLRenderingContext){if(t!=r){if(r)throw"got different context";o=Z(r=t)}return o}return t});var V=function(t){a.push(P(t))},j=function(t){l.push(P(t))};function q(t){var n=t.addEventListener;t.addEventListener=function(d,T,A){switch(d){case"webglcontextlost":V(T);break;case"webglcontextrestored":j(T);break;default:n.apply(t,arguments)}}}function H(){for(var t=Object.keys(R),n=0;n<t.length;++n)delete R[t]}function v(){++_,f||E==_&&e.loseContext()}function J(t,n){var d=t[n];return function(){if(v(),!f)return d.apply(t,arguments)}}function $(){for(var t=0;t<S.length;++t){var n=S[t];n instanceof WebGLBuffer?r.deleteBuffer(n):n instanceof WebGLFramebuffer?r.deleteFramebuffer(n):n instanceof WebGLProgram?r.deleteProgram(n):n instanceof WebGLRenderbuffer?r.deleteRenderbuffer(n):n instanceof WebGLShader?r.deleteShader(n):n instanceof WebGLTexture&&r.deleteTexture(n)}}function X(t){return{statusMessage:t,preventDefault:function(){p=!0}}}return q(e),e.loseContext=function(){if(!f){for(f=!0,E=0,++b;r.getError(););H(),R[r.CONTEXT_LOST_WEBGL]=!0;var t=X("context lost"),n=a.slice();setTimeout(function(){for(var d=0;d<n.length;++d)n[d](t);m>=0&&setTimeout(function(){e.restoreContext()},m)},0)}},e.restoreContext=function(){f&&l.length&&setTimeout(function(){if(!p)throw"can not restore. webglcontestlost listener did not call event.preventDefault";$(),w(r),f=!1,_=0,p=!1;for(var t=l.slice(),n=X("context restored"),d=0;d<t.length;++d)t[d](n)},0)},e.loseContextInNCalls=function(t){if(f)throw"You can not ask a lost contet to be lost";E=_+t},e.getNumCalls=function(){return _},e.setRestoreTimeout=function(t){m=t},e;function Z(t){for(var n in t)typeof t[n]=="function"?o[n]=J(t,n):O(o,t,n);o.getError=function(){if(v(),!f)for(;g=r.getError();)R[g]=!0;for(var g in R)if(R[g])return delete R[g],g;return o.NO_ERROR};for(var d=["createBuffer","createFramebuffer","createProgram","createRenderbuffer","createShader","createTexture"],T=0;T<d.length;++T){var A=d[T];o[A]=function(g){return function(){if(v(),f)return null;var y=g.apply(t,arguments);return y.__webglDebugContextLostId__=b,S.push(y),y}}(t[A])}var D=["getActiveAttrib","getActiveUniform","getBufferParameter","getContextAttributes","getAttachedShaders","getFramebufferAttachmentParameter","getParameter","getProgramParameter","getProgramInfoLog","getRenderbufferParameter","getShaderParameter","getShaderInfoLog","getShaderSource","getTexParameter","getUniform","getUniformLocation","getVertexAttrib"];for(T=0;T<D.length;++T)A=D[T],o[A]=function(g){return function(){return v(),f?null:g.apply(t,arguments)}}(o[A]);var M=["isBuffer","isEnabled","isFramebuffer","isProgram","isRenderbuffer","isShader","isTexture"];for(T=0;T<M.length;++T)A=M[T],o[A]=function(g){return function(){return v(),!f&&g.apply(t,arguments)}}(o[A]);return o.checkFramebufferStatus=function(g){return function(){return v(),f?o.FRAMEBUFFER_UNSUPPORTED:g.apply(t,arguments)}}(o.checkFramebufferStatus),o.getAttribLocation=function(g){return function(){return v(),f?-1:g.apply(t,arguments)}}(o.getAttribLocation),o.getVertexAttribOffset=function(g){return function(){return v(),f?0:g.apply(t,arguments)}}(o.getVertexAttribOffset),o.isContextLost=function(){return f},o}}return{init:B,mightBeEnum:U,glEnumToString:L,glFunctionArgToString:C,glFunctionArgsToString:Y,makeDebugContext:N,makeLostContextSimulatingCanvas:K,resetToInitialState:w}}())!==void 0&&(Q.exports=W);export{re as T,z as n,k as r};
