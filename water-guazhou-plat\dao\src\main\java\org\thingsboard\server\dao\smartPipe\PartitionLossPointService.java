package org.thingsboard.server.dao.smartPipe;

import com.alibaba.fastjson.JSONObject;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.request.PartitionMountRequest;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.PartitionLossPoint;

import java.util.List;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2023-05-25
 */
public interface PartitionLossPointService {

    PartitionLossPoint save(PartitionLossPoint partitionMount);

    PageData<PartitionLossPoint> getList(PartitionMountRequest request);

    void delete(List<String> ids);

    List<JSONObject> getLossPointReport(String name, String tenantId);
}
