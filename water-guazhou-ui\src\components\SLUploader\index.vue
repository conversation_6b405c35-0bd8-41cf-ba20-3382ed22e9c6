<template>
  <div>
    <el-upload
      class="sl-uploader"
      list-type="picture-card"
      :file-list="state.slFileList"
      :action="imgActionUrl"
      :disabled="isDisabled"
      :auto-upload="true"
      :limit="limit"
      :multiple="multiple"
      :on-exceed="handleExceed"
      :on-success="handleSuccess"
      :on-preview="handlePictureCardPreview"
      :on-remove="handleRemove"
      :before-upload="beforeUpload"
      :headers="{ 'X-Authorization': 'Bearer ' + useUserStore().token }"
    >
      <template #default>
        <el-icon>
          <Plus></Plus>
        </el-icon>
      </template>
      <template v-if="limit && !disabled" #tip>
        <div class="el-upload__tip">最多上传{{ limit }}个图片</div>
      </template>
    </el-upload>
    <el-dialog
      v-model="state.dialogVisible"
      width="50%"
      :title="state.dialogTitle"
    >
      <el-image
        style="width: 100%"
        :src="state.dialogImageUrl"
        :fit="'contain'"
      ></el-image>
    </el-dialog>
  </div>
</template>
<script lang="ts" setup name="SLUploader">
import { Plus } from '@element-plus/icons-vue';
import { useAppStore, useUserStore } from '@/store';
import { formateFiles } from './utils';
import { FileItem } from '@/common/types/common';
import { SLMessage } from '@/utils/Message';

const emit = defineEmits(['update:modelValue', 'handleSuccess']);
const props = defineProps<{
  modelValue?: any;
  disabled?: boolean;
  url?: string;
  limit?: number;
  multiple?: boolean;
  returnType?: 'arrStr' | 'comma' | 'arr';
}>();
const state = reactive<{
  dialogImageUrl: string;
  dialogTitle: string;
  dialogVisible: boolean;
  slFileList: FileItem[];
  UploadStatus: boolean;
}>({
  dialogImageUrl: '',
  dialogTitle: '',
  dialogVisible: false,
  slFileList: formateFiles(props.modelValue || ''),
  UploadStatus: false
});
const imgActionUrl =
  props.url && props.url !== ''
    ? props.url
    : useAppStore().actionUrl + 'file/api/upload/image';
const handleExceed = (files: any, FileList: any) => {
  console.log(files, FileList);

  SLMessage.warning(`最多可添加${props.limit}份文件`);
};
const handleRemove = (file, fileList) => {
  const list = fileList
    ? fileList.map((item) => {
        const obj = {
          url: item.response || item.url,
          name: item.name
        };
        return obj;
      })
    : [];
  emitValue(list);
};
const handlePictureCardPreview = (file) => {
  console.log(file.url);
  state.dialogImageUrl = file.url;
  state.dialogTitle = file.name;
  state.dialogVisible = true;
};

const isDisabled = computed(() => props.disabled);

const handleSuccess = (response, file, fileList) => {
  state.UploadStatus = false;
  const list = fileList
    ? fileList.map((item) => {
        if (item.status === 'uploading') {
          state.UploadStatus = true;
        }

        const obj = {
          url: item.response || item.url,
          name: item.name
        };

        return obj;
      })
    : [];
  console.log(list);
  emitValue(list);
  emit('handleSuccess', list);
};
const emitValue = (list: any) => {
  emit(
    'update:modelValue',
    props.returnType === 'arr'
      ? list
      : props.returnType === 'arrStr'
        ? JSON.stringify(list)
        : list.map((item) => item.url).join(',')
  );
};
const beforeUpload = (file: any) => {
  const isJPG = file.type === 'image/jpeg' || file.type === 'image/png';

  if (!isJPG) {
    SLMessage.error('只能上传jpeg或png格式的图片！');
  }
  return isJPG;
};

watch(props, () => {
  if (!props.modelValue) {
    state.slFileList = [];
  } else {
    !state.UploadStatus &&
      (state.slFileList = formateFiles(props.modelValue || ''));
  }
});

onMounted(() => {
  if (state.slFileList) {
    emitValue(state.slFileList);
  }
});
</script>

<style lang="scss" scoped>
$form-bg-color: #222536;
.sl-uploader {
  :deep(.el-upload-list--picture-card) {
    &.is-disabled + .el-upload--picture-card {
      display: none;
    }
    .el-upload-list__item {
      width: 96px;
      height: 96px;
    }
  }
  :deep(.el-upload) {
    &.el-upload--picture-card {
      width: 96px;
      height: 96px;
      line-height: 96px;
      border: 1px dashed #42455b;
    }
  }
}
.img-box {
  width: 100%;
  .img-full {
    width: 100%;
    height: 100%;
  }
}
// :deep(.el-dialog) {
//   .el-dialog__header {
//     padding: 5px;
//     font-size: 14px;
//   }
// }
.iconfont {
  margin-right: 0;
}
:deep(.el-progress--circle) {
  width: 80px;
  height: 80px;
  // 源码中进度条写到行内的，所以只能限制最大宽度来控制进度条的大小
  .el-progress-circle {
    max-width: 80px;
    max-height: 80px;
  }
}
</style>
