package org.thingsboard.server.dao.util.imodel.query.smartService.wechat;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartService.wechat.WxPaymentConfig;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;

@Getter
@Setter
public class WxPaymentConfigSaveRequest extends SaveRequest<WxPaymentConfig> {
    // 商户号
    private String merchantNo;

    // 支付key
    private String paymentKey;


    @Override
    protected WxPaymentConfig build() {
        WxPaymentConfig entity = new WxPaymentConfig();
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    @Override
    protected WxPaymentConfig update(String id) {
        WxPaymentConfig entity = new WxPaymentConfig();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(WxPaymentConfig entity) {
        entity.setMerchantNo(merchantNo);
        entity.setPaymentKey(paymentKey);
    }

}