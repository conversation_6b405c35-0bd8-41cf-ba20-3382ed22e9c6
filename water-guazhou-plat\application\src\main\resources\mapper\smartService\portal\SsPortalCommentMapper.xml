<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartService.portal.SsPortalCommentMapper">
    <sql id="Base_Column_List">
        <!--@sql select-->
        id,
        name,
        phone,
        email,
        content,
        create_time,
        tenant_id
        <!--@sql from ss_portal_comment -->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.smartService.portal.SsPortalComment">
        <result column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="phone" property="phone"/>
        <result column="email" property="email"/>
        <result column="content" property="content"/>
        <result column="create_time" property="createTime"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>
    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ss_portal_comment
        <where>
            <if test="name != null and name != ''">
                and name like '%'|| #{name} ||'%'
            </if>
            <if test="fromTime != null">
                and create_time >= #{fromTime}
            </if>
            <if test="toTime != null">
                and create_time &lt;= #{toTime}
            </if>
            and tenant_id = #{tenantId}
        </where>
        order by create_time desc
    </select>

    <update id="updateFully">
        update ss_portal_comment
        set name    = #{name},
            phone   = #{phone},
            email   = #{email},
            content = #{content}
        where id = #{id}
    </update>
    
    <select id="canSave" resultType="boolean">
        select count(1) > 0
        from tenant
        where id = #{tenantId}
    </select>
</mapper>