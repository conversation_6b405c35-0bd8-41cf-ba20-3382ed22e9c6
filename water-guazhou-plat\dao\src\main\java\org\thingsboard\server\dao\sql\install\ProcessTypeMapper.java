package org.thingsboard.server.dao.sql.install;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.Form;
import org.thingsboard.server.dao.model.sql.install.ProcessType;

import java.util.List;

/**
 * userMybatis
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-10-17
 */
@Mapper
public interface ProcessTypeMapper extends BaseMapper<ProcessType> {

    List<ProcessType> getList(@Param("name") String name, @Param("type") String type, @Param("tenantId") String tenantId, @Param("page") int page, @Param("size") int size);

    int getListCount(@Param("name") String name, @Param("tenantId") String tenantId);

    Form getApplyData(@Param("typeId") String typeId);
}
