package org.thingsboard.server.controller.base;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.thingsboard.server.dao.model.sql.TenantLogin;
import org.thingsboard.server.dao.tenant.TenantLoginService;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

@RestController
@RequestMapping("api/tenantLogin")
public class TenantLoginController extends BaseController {

    @Autowired
    private TenantLoginService tenantLoginService;

    @PostMapping("login")
    public IstarResponse login(@RequestBody JSONObject loginInfo) {
        String account = loginInfo.getString("account");
        String password = loginInfo.getString("password");
        if (StringUtils.isBlank(account) || StringUtils.isBlank(password)) {
            return IstarResponse.error("账号密码不能为空!");
        }
        try {
            return IstarResponse.ok(tenantLoginService.login(account, password));
        } catch (Exception e) {
           return IstarResponse.error(e.getMessage());
        }
    }

}
