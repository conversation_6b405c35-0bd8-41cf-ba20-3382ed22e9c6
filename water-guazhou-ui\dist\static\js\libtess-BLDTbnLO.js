function Zn(y,O){for(var T=0;T<O.length;T++){const f=O[T];if(typeof f!="string"&&!Array.isArray(f)){for(const t in f)if(t!=="default"&&!(t in y)){const E=Object.getOwnPropertyDescriptor(f,t);E&&Object.defineProperty(y,t,E.get?E:{enumerable:!0,get:()=>f[t]})}}}return Object.freeze(Object.defineProperty(y,Symbol.toStringTag,{value:"Module"}))}var K,pn,D={},$n={get exports(){return D},set exports(y){D=y}};K=$n,(pn=function(){function y(O){const T=O.locateFile,f={};var t=t!==void 0?t:{};const E=(()=>{let n;return{resolve:e=>n(e),promise:new Promise(e=>n=e)}})(),mn=()=>E.promise;t.locateFile=T,t.onRuntimeInitialized=()=>{E.resolve(f)},f.Module=t,f.whenLoaded=mn;var h,j={};for(h in t)t.hasOwnProperty(h)&&(j[h]=t[h]);var z,W,U,R,P,Q=typeof window=="object",I=typeof importScripts=="function",hn=typeof process=="object"&&typeof process.versions=="object"&&typeof process.versions.node=="string",p="";function gn(n){return t.locateFile?t.locateFile(n,p):p+n}hn?(p=I?require("path").dirname(p)+"/":__dirname+"/",z=function(n,e){return R||(R=require("fs")),P||(P=require("path")),n=P.normalize(n),R.readFileSync(n,e?null:"utf8")},U=function(n){var e=z(n,!0);return e.buffer||(e=new Uint8Array(e)),wn(e.buffer),e},W=function(n,e,r){R||(R=require("fs")),P||(P=require("path")),n=P.normalize(n),R.readFile(n,function(o,i){o?r(o):e(i.buffer)})},process.argv.length>1&&process.argv[1].replace(/\\/g,"/"),process.argv.slice(2),K.exports=t,process.on("uncaughtException",function(n){if(!(n instanceof Yn))throw n}),process.on("unhandledRejection",q),t.inspect=function(){return"[Emscripten Module object]"}):(Q||I)&&(I?p=self.location.href:typeof document<"u"&&document.currentScript&&(p=document.currentScript.src),p=p.indexOf("blob:")!==0?p.substr(0,p.lastIndexOf("/")+1):"",z=function(n){var e=new XMLHttpRequest;return e.open("GET",n,!1),e.send(null),e.responseText},I&&(U=function(n){var e=new XMLHttpRequest;return e.open("GET",n,!1),e.responseType="arraybuffer",e.send(null),new Uint8Array(e.response)}),W=function(n,e,r){var o=new XMLHttpRequest;o.open("GET",n,!0),o.responseType="arraybuffer",o.onload=function(){o.status==200||o.status==0&&o.response?e(o.response):r()},o.onerror=r,o.send(null)});var dn=t.print||console.log.bind(console),S=t.printErr||console.warn.bind(console);for(h in j)j.hasOwnProperty(h)&&(t[h]=j[h]);j=null,t.arguments&&t.arguments,t.thisProgram&&t.thisProgram,t.quit&&t.quit;var x,N,V=0,yn=function(n){V=n},vn=function(){return V};t.wasmBinary&&(x=t.wasmBinary),t.noExitRuntime,typeof WebAssembly!="object"&&q("no native wasm support detected");var Z=!1;function wn(n,e){n||q("Assertion failed: "+e)}var $,H,M,m,nn=typeof TextDecoder<"u"?new TextDecoder("utf8"):void 0;function tn(n,e,r){for(var o=e+r,i=e;n[i]&&!(i>=o);)++i;if(i-e>16&&n.subarray&&nn)return nn.decode(n.subarray(e,i));for(var u="";e<i;){var a=n[e++];if(128&a){var l=63&n[e++];if((224&a)!=192){var d=63&n[e++];if((a=(240&a)==224?(15&a)<<12|l<<6|d:(7&a)<<18|l<<12|d<<6|63&n[e++])<65536)u+=String.fromCharCode(a);else{var C=a-65536;u+=String.fromCharCode(55296|C>>10,56320|1023&C)}}else u+=String.fromCharCode((31&a)<<6|l)}else u+=String.fromCharCode(a)}return u}function bn(n,e){return n?tn(H,n,e):""}function An(n,e){return n%e>0&&(n+=e-n%e),n}function en(n){$=n,t.HEAP8=new Int8Array(n),t.HEAP16=new Int16Array(n),t.HEAP32=M=new Int32Array(n),t.HEAPU8=H=new Uint8Array(n),t.HEAPU16=new Uint16Array(n),t.HEAPU32=new Uint32Array(n),t.HEAPF32=new Float32Array(n),t.HEAPF64=new Float64Array(n)}t.INITIAL_MEMORY;var rn=[],on=[],un=[];function _n(){if(t.preRun)for(typeof t.preRun=="function"&&(t.preRun=[t.preRun]);t.preRun.length;)Pn(t.preRun.shift());G(rn)}function En(){G(on)}function Rn(){if(t.postRun)for(typeof t.postRun=="function"&&(t.postRun=[t.postRun]);t.postRun.length;)Tn(t.postRun.shift());G(un)}function Pn(n){rn.unshift(n)}function Sn(n){on.unshift(n)}function Tn(n){un.unshift(n)}var v=0,F=null;function jn(n){v++,t.monitorRunDependencies&&t.monitorRunDependencies(v)}function In(n){if(v--,t.monitorRunDependencies&&t.monitorRunDependencies(v),v==0&&F){var e=F;F=null,e()}}function q(n){throw t.onAbort&&t.onAbort(n),S(n+=""),Z=!0,n="abort("+n+"). Build with -s ASSERTIONS=1 for more info.",new WebAssembly.RuntimeError(n)}t.preloadedImages={},t.preloadedAudios={};var s,xn="data:application/octet-stream;base64,";function an(n){return n.startsWith(xn)}function sn(n){return n.startsWith("file://")}function fn(n){try{if(n==s&&x)return new Uint8Array(x);if(U)return U(n);throw"both async and sync fetching of the wasm failed"}catch(e){q(e)}}function Hn(){if(!x&&(Q||I)){if(typeof fetch=="function"&&!sn(s))return fetch(s,{credentials:"same-origin"}).then(function(n){if(!n.ok)throw"failed to load wasm binary file at '"+s+"'";return n.arrayBuffer()}).catch(function(){return fn(s)});if(W)return new Promise(function(n,e){W(s,function(r){n(new Uint8Array(r))},e)})}return Promise.resolve().then(function(){return fn(s)})}function Mn(){var n={a:kn};function e(u,a){var l=u.exports;t.asm=l,en((N=t.asm.m).buffer),m=t.asm.q,Sn(t.asm.n),In()}function r(u){e(u.instance)}function o(u){return Hn().then(function(a){return WebAssembly.instantiate(a,n)}).then(u,function(a){S("failed to asynchronously prepare wasm: "+a),q(a)})}function i(){return x||typeof WebAssembly.instantiateStreaming!="function"||an(s)||sn(s)||typeof fetch!="function"?o(r):fetch(s,{credentials:"same-origin"}).then(function(u){return WebAssembly.instantiateStreaming(u,n).then(r,function(a){return S("wasm streaming compile failed: "+a),S("falling back to ArrayBuffer instantiation"),o(r)})})}if(jn(),t.instantiateWasm)try{return t.instantiateWasm(n,e)}catch(u){return S("Module.instantiateWasm callback failed with error: "+u),!1}return i(),{}}function G(n){for(;n.length>0;){var e=n.shift();if(typeof e!="function"){var r=e.func;typeof r=="number"?e.arg===void 0?m.get(r)():m.get(r)(e.arg):r(e.arg===void 0?null:e.arg)}else e(t)}}function Fn(){throw"longjmp"}function qn(n,e,r){H.copyWithin(n,e,e+r)}function Cn(n){try{return N.grow(n-$.byteLength+65535>>>16),en(N.buffer),1}catch{}}function On(n){var e=H.length,r=2147483648;if((n>>>=0)>r)return!1;for(var o=1;o<=4;o*=2){var i=e*(1+.2/o);if(i=Math.min(i,n+100663296),Cn(Math.min(r,An(Math.max(n,i),65536))))return!0}return!1}an(s="libtess.wasm")||(s=gn(s));var L={mappings:{},buffers:[null,[],[]],printChar:function(n,e){var r=L.buffers[n];e===0||e===10?((n===1?dn:S)(tn(r,0)),r.length=0):r.push(e)},varargs:void 0,get:function(){return L.varargs+=4,M[L.varargs-4>>2]},getStr:function(n){return bn(n)},get64:function(n,e){return n}};function Wn(n,e,r,o){for(var i=0,u=0;u<r;u++){for(var a=M[e+8*u>>2],l=M[e+(8*u+4)>>2],d=0;d<l;d++)L.printChar(n,H[a+d]);i+=l}return M[o>>2]=i,0}function Un(){return vn()}function Ln(n){yn(n)}var kn={h:Fn,l:qn,g:On,f:Wn,b:Un,k:Nn,d:zn,j:Gn,i:Xn,e:Dn,c:Bn,a:Ln};Mn(),t.___wasm_call_ctors=function(){return(t.___wasm_call_ctors=t.asm.n).apply(null,arguments)},t._malloc=function(){return(t._malloc=t.asm.o).apply(null,arguments)},t._free=function(){return(t._free=t.asm.p).apply(null,arguments)},t._triangulate=function(){return(t._triangulate=t.asm.r).apply(null,arguments)};var k,w=t.stackSave=function(){return(w=t.stackSave=t.asm.s).apply(null,arguments)},b=t.stackRestore=function(){return(b=t.stackRestore=t.asm.t).apply(null,arguments)},A=t._setThrew=function(){return(A=t._setThrew=t.asm.u).apply(null,arguments)};function Bn(n,e,r){var o=w();try{m.get(n)(e,r)}catch(i){if(b(o),i!==i+0&&i!=="longjmp")throw i;A(1,0)}}function Dn(n,e){var r=w();try{m.get(n)(e)}catch(o){if(b(r),o!==o+0&&o!=="longjmp")throw o;A(1,0)}}function zn(n,e){var r=w();try{return m.get(n)(e)}catch(o){if(b(r),o!==o+0&&o!=="longjmp")throw o;A(1,0)}}function Nn(n){var e=w();try{return m.get(n)()}catch(r){if(b(e),r!==r+0&&r!=="longjmp")throw r;A(1,0)}}function Gn(n,e,r){var o=w();try{return m.get(n)(e,r)}catch(i){if(b(o),i!==i+0&&i!=="longjmp")throw i;A(1,0)}}function Xn(n,e,r,o){var i=w();try{return m.get(n)(e,r,o)}catch(u){if(b(i),u!==u+0&&u!=="longjmp")throw u;A(1,0)}}function Yn(n){this.name="ExitStatus",this.message="Program terminated with exit("+n+")",this.status=n}function X(n){function e(){k||(k=!0,t.calledRun=!0,Z||(En(),t.onRuntimeInitialized&&t.onRuntimeInitialized(),Rn()))}v>0||(_n(),v>0||(t.setStatus?(t.setStatus("Running..."),setTimeout(function(){setTimeout(function(){t.setStatus("")},1),e()},1)):e()))}if(F=function n(){k||X(),k||(F=n)},t.run=X,t.preInit)for(typeof t.preInit=="function"&&(t.preInit=[t.preInit]);t.preInit.length>0;)t.preInit.pop()();X();let Y=null,g=null,_=null,B=null;const c=f.Module,Jn=2,cn=4e3;let ln=0;const Kn=(n,e,r)=>{Y||(Y=c._triangulate);let o=c.HEAPF32;const i=c.HEAP32.BYTES_PER_ELEMENT,u=2,a=o.BYTES_PER_ELEMENT;r>ln&&(ln=r,_&&(c._free(_),_=0),g&&(c._free(g),g=0)),_||(_=c._malloc(r*a)),B||(B=c._malloc(cn*i));const l=r*Jn;g||(g=c._malloc(l*a)),o=c.HEAPF32,o.set(n,_/a),c.HEAP32.set(e,B/i);const d=l/u,C=Y(_,B,Math.min(e.length,cn),u,g,d),Qn=C*u;o=c.HEAPF32;const Vn=o.slice(g/a,g/a+Qn),J={};return J.buffer=Vn,J.vertexCount=C,J};return f.triangulate=Kn,f.whenLoaded()}return{load:y}}())!==void 0&&(K.exports=pn);const nt=Zn({__proto__:null,default:D},[D]);export{nt as l};
