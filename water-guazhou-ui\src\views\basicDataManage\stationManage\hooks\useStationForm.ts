import { SLConfirm, SLMessage } from '@/utils/Message'
import { AttrGroupName_none, initStationType } from '../data'
import { useAttrTable, useScadaTable } from '.'

export const useStationForm = (options: {
  refForm: any
  refDialogFormGroup: any
  submitSuccessCallBack?: (params) => Promise<void>
}) => {
  let activeTab = ''
  const attrTable = useAttrTable()
  const scadaTable = useScadaTable()
  const FormConfig = ref<IFormConfig>({
    labelPosition: 'top',
    defaultValue: {},
    group: [
      {
        fieldset: {
          type: 'underline',
          desc: '静态属性'
        },
        fields: [
          {
            xs: 24,
            sm: 12,
            md: 8,
            lg: 8,
            xl: 8,
            type: 'input',
            label: '名称：',
            field: 'name',
            rules: [{ required: true, message: '请输入名称' }]
          },
          {
            xs: 24,
            sm: 12,
            md: 8,
            lg: 8,
            xl: 8,
            type: 'select',
            label: '类型：',
            field: 'type',
            options: initStationType(),
            rules: [{ required: true, message: '请输入类型' }]
          },
          {
            xs: 24,
            sm: 12,
            md: 8,
            lg: 8,
            xl: 8,
            type: 'number',
            min: 0,
            label: '排序',
            field: 'orderNum'
          },
          {
            xs: 24,
            type: 'input',
            label: '地址：',
            field: 'address'
          },
          // {
          //   xs: 24,
          //   sm: 12,
          //   md: 8,
          //   lg: 8,
          //   xl: 8,
          //   type: 'input-number',
          //   label: 'X坐标：',
          //   field: 'x'
          // },
          // {
          //   xs: 24,
          //   sm: 12,
          //   md: 8,
          //   lg: 8,
          //   xl: 8,
          //   type: 'input',
          //   label: 'Y坐标：',
          //   field: 'y'
          // },
          // { type: 'amap', field: 'location', resultType: 'str' },
          { type: 'form-map', showInput: true, field: 'location' },
          {
            xs: 24,
            sm: 8,
            md: 8,
            lg: 8,
            xl: 8,
            type: 'avatar',
            label: '图片：',
            field: 'imgs'
          },
          {
            xs: 24,
            sm: 16,
            md: 16,
            lg: 16,
            xl: 16,
            type: 'textarea',
            maxRow: 6,
            minRow: 6,
            label: '备注：',
            field: 'remark'
          }
        ]
      },
      {
        fieldset: {
          desc: '动态属性'
        },
        fields: [],

        groupBtns: {
          styles: {
            paddingTop: '8px',
            textAlign: 'right'
          },
          btns: [
            {
              perm: true,
              text: '确定',
              loading: (): boolean => !!FormConfig.value.submitting,
              click: () => {
                options.refForm.value?.Submit()
              }
              //  async (params: any) => {
              //   try {
              //     // 把当前标签对应的table数据映射到attrGroup中
              //     const group = attrTable.attrGroup.group.value.find(
              //       o => o.type === activeTab
              //     )
              //     if (group) {
              //       group.attrList = attrTable.TableConfig.value.dataList || []
              //     }
              //     const submitParams = {
              //       ...(params || {}),
              //       stationAttrInfo: [...attrTable.attrGroup.group.value],
              //       //  attrTable.TableConfig.value.dataList || [],
              //       additionalInfo: '',
              //       orderNum: 999,
              //       createTime: new Date(),
              //       info: {}
              //     }
              //     const res = await PostStation(submitParams)
              //     if (res.data) {
              //       SLMessage.success('提交成功')
              //       options.submitSuccessCallBack?.()
              //     }
              //   } catch (error) {
              //     //
              //   }
              // }
            }
          ]
        }
      }
    ],
    submit: async params => {
      FormConfig.value.submitting = true
      const group = attrTable.attrGroup.group.value.find(o => o.type === activeTab)
      if (group) {
        group.attrList = attrTable.TableConfig.value.dataList || []
      }
      const submitParams = {
        orderNum: 999,
        ...(params || {}),
        stationAttrInfo: [...attrTable.attrGroup.group.value],
        //  attrTable.TableConfig.value.dataList || [],
        additionalInfo: '',
        // location: params?.location?.join(','),
        createTime: new Date(),
        info: {}
      }
      await options.submitSuccessCallBack?.(submitParams)
    }
  })
  const initDynamicAttrConfig = () => {
    FormConfig.value.group[1].fields.length = 0
    FormConfig.value.group[1].fields = [
      {
        type: 'tabs',
        label: '',
        tabType: 'card',
        field: 'stationAttrInfo_type',
        tabs: computed(() => attrTable.attrGroup.group.value.map(item => {
          return {
            label: item.type,
            value: item.type
          }
        })) as any,
        closable: true,
        // handleTabClick: (tab: TabsPaneContext) => {
        //   if (tab.paneName === AttrGroupName_none) {
        //     options.refDialogFormGroup.value?.openDialog()
        //   }
        // },
        handleTabRemove: async (tagName: any, nextTabName?: any) => {
          if (tagName && tagName !== AttrGroupName_none) {
            await SLConfirm('确定删除？', '提示信息')
          }
          attrTable.attrGroup.removeAttrGroup(tagName)
          // attrGroups.value = attrGroups.value.filter(item => item !== tagName)
          // 当删除的是最后一个标签是，则添加一个替代标签
          if (!attrTable.attrGroup.group.value.length) {
            attrTable.attrGroup.group.value.push({
              type: AttrGroupName_none,
              attrList: []
            })
            nextTabName = AttrGroupName_none
          }
          if (options.refForm?.value) {
            options.refForm.value.stationAttrInfo_type = nextTabName || options.refForm.value?.dataForm?.stationAttrInfo_type || AttrGroupName_none
          }
          // 重置属性表数据
          attrTable.TableConfig.value.dataList = attrTable.attrGroup.group.value.find(item => item.type === options.refForm.value.stationAttrInfo_type)
            ?.attrList || []
        },
        onChange: (tab: string) => {
          console.log(tab)
          if (activeTab) {
            // 保存之前点击的tab
            const oldGroup = attrTable.attrGroup.group.value.find(item => item.type === activeTab)
            if (oldGroup) {
              oldGroup.attrList = attrTable.TableConfig.value.dataList || []
            }
          }
          // 把本次点击的tab保存起来，下次点击可以用这个值来查上次上一次点击的tab，目的是用来同步table数据到attrGroup,attrGroup存的数据就是提交的时候对应的stationAttrInfo字段的数据
          activeTab = tab
          // 点击的是添加标签时
          if (tab === AttrGroupName_none) {
            if (attrTable.attrGroup.group.value.length === 0) {
              attrTable.attrGroup.group.value.push({
                type: '请添加分组',
                attrList: []
              })
            }
            // options.refDialogFormGroup.value?.openDialog()
          }
          //  else {
          //   // 否则刷新table
          //   attrTable.refreshData(tab)
          //   // attrTable.TableConfig.value.dataList = attrTable.attrGroup.group.value.find(item => item.type === tab)
          //   //   ?.attrList || []
          // }
          attrTable.refreshData(tab)
        },
        btns: [
          {
            type: 'primary',
            size: 'small',
            perm: true,
            text: '添加分组',
            click: () => options.refDialogFormGroup.value?.openDialog()
          },
          {
            type: 'primary',
            size: 'small',
            perm: true,
            text: '添加变量',
            click: () => {
              if (options.refForm.value?.dataForm?.stationAttrInfo_type === AttrGroupName_none) {
                SLMessage.warning('请先添加分组')
                return
              }
              attrTable.addAttrRow(
                options.refForm.value?.dataForm?.stationAttrInfo_type,
                options.refForm.value?.dataForm?.id
              )
            }
          }
        ]
      },
      {
        type: 'table',
        label: '',
        config: attrTable.TableConfig.value
      },
      {
        type: 'tabs',
        label: '',
        field: 'extraInfo',
        tabs: [
          {
            value: 'scada',
            label: '组态配置'
          }
        ]
        // btns: [
        //   {
        //     type: 'primary',
        //     size: 'small',
        //     text: '添加组态',
        //     perm: true,
        //     click: scadaTable.addScadaRow
        //   }
        // ]
      },
      {
        type: 'textarea',
        label: '组态路径',
        field: 'scadaUrl'
        // config: scadaTable.TableConfig.value
      }
    ]
  }
  const resetForm = async (defaultParams?: { id?: string; projectId?: string; location?: string }) => {
    if (!options.refForm?.value) return
    attrTable.refreshTableColumns(defaultParams?.projectId)

    await attrTable.attrGroup.initAttrGroupData(defaultParams?.id)
    const group = attrTable.attrGroup.group.value?.[0]?.type
    // 单独提到slot逻辑中，这段无用了
    // const location = defaultParams?.location?.split(',')
    //   || window.SITE_CONFIG.GIS_CONFIG.gisDefaultCenter || [116, 29]
    // location.map(item => parseInt(item) || item)

    FormConfig.value.defaultValue = {
      orderNum: 999,
      ...(defaultParams || {}),
      location,
      stationAttrInfo_type: group || AttrGroupName_none,
      extraInfo: 'scada'
    }
    options.refForm.value?.resetForm()
    // attrTable.refreshData(group)
    // scadaTable.refreshData();
  }
  return {
    FormConfig,
    initDynamicAttrConfig,
    resetForm,
    attrTable,
    scadaTable
  }
}
