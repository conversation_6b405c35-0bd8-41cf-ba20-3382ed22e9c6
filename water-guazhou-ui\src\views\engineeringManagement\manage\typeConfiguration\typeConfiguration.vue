<!-- 工程管理-工程管理-类型配置 -->
<template>
  <div class="wrapper">
    <CardSearch ref="refSearch" :config="cardSearchConfig" />
    <CardTable :config="TableConfig" class="card-table"></CardTable>
    <DialogForm ref="refForm" :config="addOrUpdateConfig"></DialogForm>
  </div>
</template>

<script lang="ts" setup>
import { Refresh } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import {
  getProjectType,
  postProjectType,
  getConstructionContractType,
  postConstructionContractType
} from '@/api/engineeringManagement/manage';
import { ICONS } from '@/common/constans/common';

const refForm = ref<IDialogFormIns>();
const radio = ref('项目类型');

const cardSearchConfig = ref<ISearch>({
  defaultParams: {
    radio: '项目类型'
  },
  filters: [
    {
      label: '',
      field: 'radio',
      type: 'radio-button',
      options: [
        { label: '项目类型', value: '项目类型' },
        { label: '合同分类', value: '合同分类' }
      ],
      onChange: (val) => {
        radio.value = val;
      }
    }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          type: 'success',
          perm: true,
          text: '添加',
          icon: ICONS.ADD,
          click: () => {
            addValue();
          }
        },
        {
          type: 'default',
          perm: true,
          text: '重置',
          svgIcon: shallowRef(Refresh),
          click: () => {
            refreshData();
          }
        },
        {
          perm: true,
          text: '查询',
          icon: ICONS.QUERY,
          click: () => refreshData()
        }
      ]
    }
  ]
});

const TableConfig = reactive<ICardTable>({
  defaultExpandAll: true,
  indexVisible: true,
  columns: [
    { label: '名称', prop: 'name' },
    { label: '排序编号', prop: 'orderNum' }
  ],
  operationWidth: '100px',
  operations: [
    {
      isTextBtn: false,
      text: '编辑',
      perm: true,
      click: (row) => {
        addOrUpdateConfig.title = '编辑信息';
        addOrUpdateConfig.defaultValue = { ...row };
        refForm.value?.openDialog();
      }
    }
  ],
  dataList: [],
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page;
      TableConfig.pagination.limit = size;
      refreshData();
    }
  }
});

const addOrUpdateConfig = reactive<IDialogFormConfig>({
  title: '添加信息',
  labelWidth: '100px',
  dialogWidth: '500px',
  submitting: false,
  submit: (params: any) => {
    let text = '添加';
    if (params.id) {
      text = '编辑';
    }
    addOrUpdateConfig.submitting = true;
    if (radio.value === '项目类型') {
      postProjectType(params)
        .then((res) => {
          addOrUpdateConfig.submitting = false;
          if (res.data.code === 200) {
            ElMessage.success(text + '成功');
            refForm.value?.closeDialog();
            refreshData();
          } else {
            ElMessage.warning(text + '失败');
          }
        })
        .catch((error) => {
          ElMessage.warning(error);
        });
    } else {
      postConstructionContractType(params)
        .then((res) => {
          addOrUpdateConfig.submitting = false;
          if (res.data.code === 200) {
            ElMessage.success(text + '成功');
            refForm.value?.closeDialog();
            refreshData();
          } else {
            ElMessage.warning(text + '失败');
          }
        })
        .catch((error) => {
          ElMessage.warning(error);
        });
    }
  },
  defaultValue: {},
  group: [
    {
      fields: [
        {
          type: 'input',
          label: '类型名称',
          field: 'name',
          rules: [{ required: true, message: '请输入类型名称' }]
        },
        {
          type: 'input-number',
          label: '排序',
          field: 'orderNum',
          rules: [{ required: true, message: '请输入排序' }]
        }
      ]
    }
  ]
});

function addValue() {
  addOrUpdateConfig.title = '添加信息';
  addOrUpdateConfig.defaultValue = {};
  refForm.value?.openDialog();
}

const refreshData = async () => {
  const params = {
    size: TableConfig.pagination.limit || 20,
    page: TableConfig.pagination.page || 1
  };
  TableConfig.dataList = [];
  TableConfig.pagination.total = 0;
  if (radio.value === '项目类型') {
    getProjectType(params).then((res) => {
      TableConfig.dataList = res.data.data.data || [];
      TableConfig.pagination.total = res.data.data.total || 0;
    });
  } else {
    getConstructionContractType(params).then((res) => {
      TableConfig.dataList = res.data.data.data || [];
      TableConfig.pagination.total = res.data.data.total || 0;
    });
  }
};

watch(radio, () => {
  refreshData();
});

onMounted(() => {
  refreshData();
});
</script>
