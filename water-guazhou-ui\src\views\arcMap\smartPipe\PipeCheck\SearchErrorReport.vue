<!-- **错误属性上报查询** -->
<template>
  <DrawerBox
    ref="refDrawer"
    :left-drawer="true"
    :left-drawer-absolute="false"
    :left-drawer-bar-hide="false"
    :bottom-drawer="true"
    :left-drawer-width="400"
    :left-drawer-title="' '"
    :bottom-drawer-bar-position="'right'"
    :bottom-drawer-title="'属性上报列表'"
  >
    <ArcLayout
      @map-loaded="onMapLoaded"
      @pipe-loaded="onPipeLoaded"
    ></ArcLayout>
    <template #right>
      <Form ref="refForm" :config="FormConfig"></Form>
      <FormTable :config="TableConfig"></FormTable>
    </template>
    <template #left-title>
      <span>{{ customConfig.title }}</span>
    </template>
    <template #left>
      <ErrorPopTable :config="customConfig"></ErrorPopTable>
    </template>
    <template #bottom>
      <InlineForm ref="refForm" :config="FormConfig"></InlineForm>
      <div class="attr-table-box">
        <FormTable ref="refTable" :config="TableConfig"></FormTable>
      </div>
    </template>
  </DrawerBox>
</template>
<script lang="ts" setup>
import {
  DeleteMapErrorRecords,
  GetErrorReportList
} from '@/api/mapservice/errorReport';
import Graphic from '@arcgis/core/Graphic';
import {
  excuteQuery,
  // from4548Graphic,
  getGraphicLayer,
  gotoAndHighLight,
  initQueryParams,
  setSymbol
} from '@/utils/MapHelper';
import { convertGeoJSONToArcGIS, queryFeatureByIdGeoserver } from '@/utils/geoserver/geoserverUtils';
import { GetAllFieldConfig } from '@/api/mapservice/fieldconfig';
import { SLConfirm, SLMessage } from '@/utils/Message';
import { tagColors, reportType } from './data';
import ErrorPopTable from './components/ErrorPopTable.vue';
import { useGisStore, useUserStore } from '@/store';
import { removeSlash } from '@/utils/removeIdSlash';
import { formatDate } from '@/utils/DateFormatter';
import { formatterDateTime } from '@/utils/GlobalHelper';

const customConfig = ref<{
  hideFooter: boolean;
  row?: any;
  fid?: string;
  layer?: number;
  dataList: any[];
  title: string;
  img?: string;
}>({
  hideFooter: true,
  row: undefined,
  dataList: [],
  title: ''
});
const refDrawer =
  ref<
    InstanceType<
      (typeof import('@/components/DrawerBox/DrawerBox.vue'))['default']
    >
  >();
const refTable = ref<IFormTableIns>();
const refForm = ref<IInlineFormIns>();
const state = reactive<{
  fieldConfig: IGISFieldConfig[];
}>({
  fieldConfig: []
});
const staticState: {
  view?: __esri.MapView;
  graphicsLayer?: __esri.GraphicsLayer;
  identifyResults: any[];
} = {
  identifyResults: []
};
const FormConfig = reactive<IFormConfig>({
  labelWidth: 70,
  labelPosition: 'right',
  group: [
    {
      fields: [
        {
          type: 'radio-button',
          field: 'createuser',
          options: [
            { label: '全部', value: '' },
            { label: '我上报的', value: 'myself' }
          ],
          onChange: () => refreshData()
        },
        // {
        //   labelWidth: 70,
        //   type: 'select',
        //   field: 'layer',
        //   label: '设备类型',
        //   options: []
        // },
        // {
        //   type: 'select',
        //   field: 'reporter',
        //   label: '上报人',
        //   options: []
        // },
        {
          type: 'daterange',
          field: 'reportTime',
          label: '上报时间'
        },
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: '查询',
              click: () => refreshData(),
              iconifyIcon: 'ep:search'
            },
            {
              perm: true,
              text: '重置',
              type: 'default',
              click: () => resetForm(),
              iconifyIcon: 'ep:refresh'
            },
            {
              perm: true,
              text: '导出',
              type: 'default',
              click: () => exportData(),
              iconifyIcon: 'ep:download'
            },
            {
              perm: (row) => {
                return row.createuser === 'myself';
              },
              text: '删除',
              type: 'danger',
              click: () => deleteData(),
              iconifyIcon: 'ep:delete'
            }
          ]
        }
      ]
    }
  ],
  defaultValue: {
    reportTime: [
      // moment().subtract(6, 'M').format('YYYY-MM-DD HH:mm:ss'),
      // moment().format('YYYY-MM-DD HH:mm:ss')
    ],
    createuser: ''
  }
});
const deleteData = () => {
  const ids = TableConfig.selectList?.map((item) => item.id);
  if (!ids?.length) {
    SLMessage.warning('请选择要删除的数据');
    return;
  }
  SLConfirm('确定删除？', '提示信息')
    .then(async () => {
      try {
        const res = await DeleteMapErrorRecords(ids);
        if (res.data.code === 200) {
          SLMessage.success('删除成功');
          refreshData();
        } else {
          SLMessage.error('删除失败');
        }
      } catch (error) {
        SLMessage.error('删除失败');
      }
    })
    .catch(() => {
      //
    });
};
const TableConfig = reactive<ITable>({
  dataList: [],
  columns: [
    { minWidth: 120, label: '设备类别', prop: 'layer' },
    { minWidth: 120, label: '上报人', prop: 'uploadUserName' },
    {
      minWidth: 120,
      label: '上报时间',
      prop: 'uploadTime',
      formatter(row, value) {
        return formatDate(value, formatterDateTime);
      }
    },
    {
      width: 120,
      label: '上报内容',
      prop: 'uploadContent',
      formItemConfig: {
        type: 'btn-group',
        btns: [
          {
            perm: true,
            text: '查看',
            isTextBtn: true,
            click: (row) => handleViewDetail(row)
          }
        ]
      }
    },
    { minWidth: 120, label: '审批人', prop: 'approvalUserName' },
    {
      minWidth: 120,
      label: '审批时间',
      prop: 'approvalTime',
      formatter(row, value) {
        return formatDate(value, formatterDateTime);
      }
    },
    {
      width: 120,
      label: '状态',
      prop: 'status',
      tag: true,
      align: 'center',
      tagColor: (row) => tagColors[row.status],
      formatter(row, value) {
        return reportType[value] || '待处理';
      }
    },
    { minWidth: 120, label: '备注', prop: 'remark' }
  ],
  pagination: {
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page;
      TableConfig.pagination.limit = size;
      refreshData();
    }
  },
  handleSelectChange(val) {
    TableConfig.selectList = val || [];
  }
});

/**
 * 查看错误上报详情
 * @param row 上报记录行数据
 */
const handleViewDetail = async (row: any) => {
  if (!row.fid) {
    SLMessage.warning('缺少设备ID信息');
    return;
  }

  try {
    // 解析上报内容
    const content: any = row.uploadContent ? JSON.parse(row.uploadContent) : [];

    // 查找图层信息
    let feature: __esri.Graphic | null = null;

    // 判断是否使用GeoServer
    if (window.GIS_SERVER_SWITCH) {
      // GeoServer模式 - 使用封装好的queryFeatureByIdGeoserver函数
      try {
        // 调用封装好的函数查询要素
        const result = await queryFeatureByIdGeoserver({
          typeName: row.layer,  // 图层名称
          id: row.fid,          // 要素ID
          workspace: 'guazhou',  // 工作空间名称
          outputSRS: 'EPSG:3857'  // 使用WGS84坐标系
        });

        // 处理查询结果
        if (result.success && result.feature) {
          // 查询成功，使用返回的要素
          const geoJsonFeature = result.feature;
          const geometry = convertGeoJSONToArcGIS(geoJsonFeature.geometry);

          // 创建Graphic对象
          feature = new Graphic({
            geometry: geometry,
            attributes: geoJsonFeature.properties || {}
          });

          // 设置符号
          feature.symbol = setSymbol(geometry.type);
        } else {
          SLMessage.warning('未找到设备信息');
          return;
        }
      } catch (error) {
        console.error('GeoServer查询失败:', error);
        SLMessage.error('查询设备信息失败');
        return;
      }
    } else {
      // ArcGIS模式 - 使用ArcGIS REST API查询要素
      const layerInfo = useGisStore().gLayerInfos?.find(
        (item) => item.layername === row.layer
      );
      const layerId = layerInfo?.layerid;

      if (!layerInfo) {
        SLMessage.warning('未找到图层信息');
        return;
      }

      // 执行查询
      const res = await excuteQuery(
        window.SITE_CONFIG.GIS_CONFIG.gisService +
          window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService +
          '/' +
          layerId,
        initQueryParams({
          where: 'OBJECTID = ' + row.fid,
          outFields: ['*']
        })
      );

      if (res.features && res.features.length > 0) {
        feature = res.features[0];
        feature.symbol = setSymbol(feature.geometry.type);
      } else {
        SLMessage.warning('未找到设备信息');
        return;
      }
    }

    // 如果找到了要素，显示详情
    if (feature) {
      // 清除之前的图形并添加新的
      staticState.graphicsLayer?.removeAll();
      staticState.graphicsLayer?.add(feature);

      // 高亮显示要素
      await gotoAndHighLight(staticState.view, feature);

      // 获取字段信息
      const field = state.fieldConfig.find((item) => item.layername === row.layer)?.fields || [];

      // 构建数据列表
      const dataList = content
        .filter((item: any) => item.name !== 'img')
        .map((item: any) => {
          const fieldInfo = field.find(
            (o) => o.name.toLocaleLowerCase() === item.name?.toLocaleLowerCase()
          );
          return {
            ...(fieldInfo || {}),
            oldvalue: item.oldvalue,
            newvalue: item.newvalue
          };
        });

      // 更新UI
      staticState.identifyResults = [{ feature, layerName: row.layer, layerId: row.fid }];

      // 设置自定义配置
      customConfig.value = {
        hideFooter: true,
        row,
        img: content.find((item: any) => item.name === 'img')?.newvalue,
        dataList,
        title: row.layer + '(' + (feature.attributes['SID'] || feature.attributes['OBJECTID'] || row.fid) + ')'
      };

      // 打开左侧抽屉
      refDrawer.value?.toggleDrawer('ltr', true);
    }
  } catch (error) {
    console.error('处理错误上报详情失败:', error);
    SLMessage.error('数据错误');
  }
};

const userStore = useUserStore();
const refreshData = () => {
  const { createuser, reportTime } = refForm.value?.dataForm || {};
  const start = reportTime?.[0];
  let end = reportTime?.[1];
  end && (end = moment(end, 'YYYY-MM-DD').add(1, 'd').format('YYYY-MM-DD'));
  GetErrorReportList({
    page: TableConfig.pagination.page || 1,
    size: TableConfig.pagination.limit || 20,
    uploadUser:
      createuser === ''
        ? ''
        : userStore.user?.id?.id && removeSlash(userStore.user?.id?.id),
    beginTime: start,
    endTime: end
  })
    .then((res) => {
      const data = res.data.data;
      TableConfig.dataList = data?.data || [];
      TableConfig.pagination.total = data?.total || 0;
    })
    .catch(() => {
      TableConfig.dataList = [];
      TableConfig.pagination.total = 0;
    });
};
const resetForm = () => {
  refForm.value?.resetForm();
};
const exportData = () => {
  refTable.value?.exportTable();
};
const initFieldConfig = async () => {
  const res = await GetAllFieldConfig();
  state.fieldConfig = res.data.result || [];
};
const onMapLoaded = async (view: __esri.MapView) => {
  staticState.view = view;
};
const onPipeLoaded = () => {
  staticState.graphicsLayer = getGraphicLayer(staticState.view, {
    id: 'error-report',
    title: '错误属性设备'
  });
};
onMounted(() => {
  refreshData();
  refDrawer.value?.toggleDrawer('btt', true);
  initFieldConfig();
});
</script>
<style lang="scss" scoped>
.attr-table-box {
  height: calc(100% - 50px);
}
</style>
