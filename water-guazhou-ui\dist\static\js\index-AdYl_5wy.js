import{d as U,j as z,a8 as G,r as C,c as j,am as K,o as q,ay as H,g as h,n as f,p as m,q as t,F as o,G as l,i as u,al as J,aj as W,bD as Q,bh as i,bP as N,e5 as X,an as D,x as V,aK as Y,aL as Z,I as $,dF as tt,dA as et,c2 as ot,cE as at,J as lt,K as nt,bz as st,N as it,O as dt,C as rt}from"./index-r0dFAfgr.js";const pt={class:"page-container"},mt={class:"search-section"},ut={class:"content-section"},ct={class:"card-header"},_t={class:"mode-switch"},ht={key:0,class:"chart-container"},ft={key:0,class:"chart-wrapper"},gt={key:1,class:"chart-loading"},yt={key:1,class:"table-container"},bt=U({__name:"index",setup(Ct){const P=z(),k=G(()=>P.isDark),d=C({stationId:"station1",type:"day",dateRange:[new Date,new Date]}),r=j("list"),c=C({loading:!1,list:[]}),y=C({option:null}),w=()=>[{sampleTime:"2025-04-10 09:00",sampleLocation:"出水口",weather:"晴",outletCod:"15.2",outletBod:"8.5",suspendedSolids:"12.3",ammoniaNitrogen:"2.1",totalNitrogen:"8.7",totalPhosphorus:"0.8",outletPh:"7.2",fecalColiform:"1000"},{sampleTime:"2025-04-15 09:00",sampleLocation:"出水口",weather:"阴",outletCod:"18.7",outletBod:"9.2",suspendedSolids:"14.1",ammoniaNitrogen:"2.5",totalNitrogen:"9.3",totalPhosphorus:"0.9",outletPh:"7.1",fecalColiform:"1200"},{sampleTime:"2025-04-20 09:00",sampleLocation:"出水口",weather:"雨",outletCod:"22.1",outletBod:"11.8",suspendedSolids:"16.7",ammoniaNitrogen:"3.2",totalNitrogen:"10.5",totalPhosphorus:"1.1",outletPh:"6.9",fecalColiform:"1500"},{sampleTime:"2025-04-25 09:00",sampleLocation:"出水口",weather:"晴",outletCod:"16.8",outletBod:"9.7",suspendedSolids:"13.5",ammoniaNitrogen:"2.3",totalNitrogen:"8.9",totalPhosphorus:"0.7",outletPh:"7.3",fecalColiform:"900"}],S=()=>{const s=w();y.option={title:{text:"水质指标趋势图",left:"center",textStyle:{fontSize:16,fontWeight:"bold"}},tooltip:{trigger:"axis",axisPointer:{type:"cross"}},legend:{data:["出水COD","BOD5","氨氮","总氮","总磷"],top:30,type:"scroll"},grid:{left:"3%",right:"4%",bottom:"3%",top:"15%",containLabel:!0},xAxis:{type:"category",boundaryGap:!1,data:s.map(e=>e.sampleTime.split(" ")[0])},yAxis:{type:"value",name:"浓度 (mg/L)",min:0},series:[{name:"出水COD",type:"line",data:s.map(e=>parseFloat(e.outletCod)),smooth:!0,itemStyle:{color:"#5470C6"},lineStyle:{width:2}},{name:"BOD5",type:"line",data:s.map(e=>parseFloat(e.outletBod)),smooth:!0,itemStyle:{color:"#91CC75"},lineStyle:{width:2}},{name:"氨氮",type:"line",data:s.map(e=>parseFloat(e.ammoniaNitrogen)),smooth:!0,itemStyle:{color:"#FAC858"},lineStyle:{width:2}},{name:"总氮",type:"line",data:s.map(e=>parseFloat(e.totalNitrogen)),smooth:!0,itemStyle:{color:"#EE6666"},lineStyle:{width:2}},{name:"总磷",type:"line",data:s.map(e=>parseFloat(e.totalPhosphorus)),smooth:!0,itemStyle:{color:"#73C0DE"},lineStyle:{width:2}}]}};K(r,s=>{s==="chart"&&setTimeout(()=>{S()},100)});const v=()=>{c.loading=!0,setTimeout(()=>{c.list=w(),c.loading=!1},500)},B=()=>{v(),r.value==="chart"&&S()},L=()=>{V.info("导出功能开发中...")},F=()=>{V.info("打印功能开发中...")};return q(()=>{v()}),(s,e)=>{const T=Y,O=Z,g=$,_=tt,x=et,I=ot,p=at,b=lt,A=nt,E=st,R=H("VChart"),n=it,M=dt;return h(),f("div",pt,[m("div",mt,[t(E,null,{default:o(()=>[t(A,{model:d,inline:""},{default:o(()=>[t(g,{label:"站点选择"},{default:o(()=>[t(O,{modelValue:d.stationId,"onUpdate:modelValue":e[0]||(e[0]=a=>d.stationId=a),placeholder:"请选择站点",style:{width:"200px"}},{default:o(()=>[t(T,{label:"污水处理厂",value:"station1"})]),_:1},8,["modelValue"])]),_:1}),t(g,{label:"报告类型"},{default:o(()=>[t(x,{modelValue:d.type,"onUpdate:modelValue":e[1]||(e[1]=a=>d.type=a)},{default:o(()=>[t(_,{label:"day"},{default:o(()=>e[4]||(e[4]=[l("日报")])),_:1}),t(_,{label:"month"},{default:o(()=>e[5]||(e[5]=[l("月报")])),_:1}),t(_,{label:"year"},{default:o(()=>e[6]||(e[6]=[l("年报")])),_:1})]),_:1},8,["modelValue"])]),_:1}),t(g,{label:"时间范围"},{default:o(()=>[t(I,{modelValue:d.dateRange,"onUpdate:modelValue":e[2]||(e[2]=a=>d.dateRange=a),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",style:{width:"320px"}},null,8,["modelValue"])]),_:1}),t(g,null,{default:o(()=>[t(b,{type:"primary",onClick:B},{default:o(()=>[t(p,null,{default:o(()=>[t(u(J))]),_:1}),e[7]||(e[7]=l(" 查询 "))]),_:1}),t(b,{type:"warning",onClick:L},{default:o(()=>[t(p,null,{default:o(()=>[t(u(W))]),_:1}),e[8]||(e[8]=l(" 导出 "))]),_:1}),t(b,{type:"success",onClick:F},{default:o(()=>[t(p,null,{default:o(()=>[t(u(Q))]),_:1}),e[9]||(e[9]=l(" 打印 "))]),_:1})]),_:1})]),_:1},8,["model"])]),_:1})]),m("div",ut,[t(E,null,{header:o(()=>[m("div",ct,[m("span",null,i(r.value==="list"?"水质列表":"水质图表"),1),m("div",_t,[t(x,{modelValue:r.value,"onUpdate:modelValue":e[3]||(e[3]=a=>r.value=a)},{default:o(()=>[t(_,{label:"chart"},{default:o(()=>[t(p,null,{default:o(()=>[t(u(N))]),_:1})]),_:1}),t(_,{label:"list"},{default:o(()=>[t(p,null,{default:o(()=>[t(u(X))]),_:1})]),_:1})]),_:1},8,["modelValue"])])])]),default:o(()=>[r.value==="chart"?(h(),f("div",ht,[y.option?(h(),f("div",ft,[t(R,{option:y.option,theme:k.value?"dark":"light",style:{width:"100%",height:"500px"}},null,8,["option","theme"])])):(h(),f("div",gt,[t(p,{size:"64",color:"#ccc"},{default:o(()=>[t(u(N))]),_:1}),e[10]||(e[10]=m("p",{style:{color:"#999","margin-top":"16px"}},"正在加载图表...",-1))]))])):D("",!0),r.value==="list"?(h(),f("div",yt,[t(M,{data:c.list,loading:c.loading,border:"",stripe:"",style:{width:"100%"}},{default:o(()=>[t(n,{prop:"sampleTime",label:"采样时间","min-width":"150"}),t(n,{prop:"sampleLocation",label:"采样地点","min-width":"120"}),t(n,{prop:"weather",label:"天气","min-width":"80"}),t(n,{prop:"outletCod",label:"出水COD","min-width":"120"},{default:o(({row:a})=>[l(i(a.outletCod)+" mg/L ",1)]),_:1}),t(n,{prop:"outletBod",label:"BOD5","min-width":"120"},{default:o(({row:a})=>[l(i(a.outletBod)+" mg/L ",1)]),_:1}),t(n,{prop:"suspendedSolids",label:"悬浮物","min-width":"120"},{default:o(({row:a})=>[l(i(a.suspendedSolids)+" mg/L ",1)]),_:1}),t(n,{prop:"ammoniaNitrogen",label:"氨氮","min-width":"120"},{default:o(({row:a})=>[l(i(a.ammoniaNitrogen)+" mg/L ",1)]),_:1}),t(n,{prop:"totalNitrogen",label:"总氮","min-width":"120"},{default:o(({row:a})=>[l(i(a.totalNitrogen)+" mg/L ",1)]),_:1}),t(n,{prop:"totalPhosphorus",label:"总磷","min-width":"120"},{default:o(({row:a})=>[l(i(a.totalPhosphorus)+" mg/L ",1)]),_:1}),t(n,{prop:"outletPh",label:"出水PH值","min-width":"120"}),t(n,{prop:"fecalColiform",label:"粪大肠菌群数","min-width":"150"},{default:o(({row:a})=>[l(i(a.fecalColiform)+" 个/L ",1)]),_:1})]),_:1},8,["data","loading"])])):D("",!0)]),_:1})])])}}}),St=rt(bt,[["__scopeId","data-v-a5ace54a"]]);export{St as default};
