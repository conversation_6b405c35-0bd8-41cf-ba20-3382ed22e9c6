package org.thingsboard.server.service.aspect;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.thingsboard.server.dao.model.sql.base.ApiPerformance;
import org.thingsboard.server.dao.optionLog.ApiPerformanceServiceImpl;
import org.thingsboard.server.service.aspect.annotation.MonitorPerformance;
import org.thingsboard.server.service.security.model.SecurityUser;

import java.util.Date;

/**
 * 接口性能监控切面
 */
@Aspect
@Component
public class PerformanceAspect {

    @Autowired
    private ApiPerformanceServiceImpl apiPerformanceService;

    @Around("@annotation(monitorPerformance)")
    public Object measureExecutionTime(ProceedingJoinPoint joinPoint, MonitorPerformance monitorPerformance) throws Throwable {
        String description = monitorPerformance.description();
        long startTime = System.currentTimeMillis();
        Object proceed;
        boolean success = true;

        try {
            proceed = joinPoint.proceed();
        } catch (Exception e) {
            success = false;
            throw e;
        } finally {
            // 计算接口执行时间
            long executionTime = System.currentTimeMillis() - startTime;
            recordPerformance(joinPoint, executionTime, success, description);
        }

        return proceed;
    }

    private void recordPerformance(ProceedingJoinPoint joinPoint, long executionTime, boolean success, String description) {
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        String methodName = methodSignature.getName();
        String className = joinPoint.getTarget().getClass().getName();

        ApiPerformance performance = new ApiPerformance();
        performance.setDescription(description);
        performance.setMethodName(methodName);
        performance.setClassName(className);
        performance.setExecutionTime(String.valueOf(executionTime));
        performance.setCallTime(new Date());
        if (success) {
            performance.setSuccess("true");
        } else {
            performance.setSuccess("false");
        }
        // 获取用户id信息
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.getPrincipal() instanceof SecurityUser) {
            SecurityUser user = (SecurityUser) authentication.getPrincipal();
            if (user != null) {
                performance.setUserId(String.valueOf(user.getId()));
            }
        }
        // 异步保存（可选）
        new Thread(() -> apiPerformanceService.insertApiPerformance(performance)).start();
    }
}
