<template>
  <div
    v-show="showAlarmPop"
    ref="refPop"
    data-id="alarm-pop"
    class="alarm-water-pop"
  >
    <div class="title" :title="alarms.curAlarmGroup.value?.station?.name">
      <span>{{ alarms.curAlarmGroup.value?.station?.name }}</span>
    </div>
    <div class="inner">
      <!-- <div class="alarm-item type">
        <label for="">报警类型：</label> <span>{{ formatAlarmType(alarms.curAlarm.value?.type) }}</span>
      </div>
      <div class="alarm-item level">
        <label for="">紧急程度：</label> <span>{{ alarms.curAlarm.value?.severity }}</span>
      </div>
      <div class="alarm-item status">
        <label for="">报警状态：</label> <span>{{ formatAlarmStatus(alarms.curAlarm.value?.status) }}</span>
      </div>
      <div class="alarm-item status">
        <label for="">报警值：</label> <span>{{ alarms.curAlarm.value?.value }}</span>
      </div>
      <div class="alarm-item info">
        <label for="">报警信息：</label>
        <span>
          {{ alarms.curAlarm.value?.alarmJsonName }}
        </span>
      </div> -->
      <!-- <div class="alarm-item info">
        <label for="">报警记录：</label>
      </div> -->
      <el-timeline style="padding: 5px">
        <el-timeline-item
          v-for="(item, index) in alarms.curAlarmGroup.value?.alarms"
          :key="index"
          :timestamp="formatDate(item.time)?.toString()"
          placement="top"
        >
          <el-card class="alarm-card">
            <div class="alarm-item">
              <span class="label">报警设备:</span
              ><span class="value">{{ item.deviceName }}</span>
            </div>
            <div class="alarm-item">
              <span class="label">紧急程度:</span
              ><span class="value">{{
                formatAlarmLevel(item.alarmLevel)
              }}</span>
            </div>
            <div class="alarm-item">
              <span class="label">报警状态:</span
              ><span class="value">{{
                formatAlarmStatus(item.alarmStatus)
              }}</span>
            </div>
            <div class="alarm-item">
              <span class="label">处理状态:</span
              ><span class="value">{{
                formatProcessStatus(item.processStatus)
              }}</span>
            </div>
            <div class="alarm-item">
              {{ item.alarmInfo }}
            </div>
          </el-card>
        </el-timeline-item>
      </el-timeline>
      <div style="width: 100%; display: flex; justify-content: end">
        <el-button type="success" @click="exportTable">导出告警</el-button>
        <el-button type="danger" @click="ClearAlarm">解除告警</el-button>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import Point from '@arcgis/core/geometry/Point';
import { useWaterPoint } from '@/hooks/arcgis';
import { formatDate } from '@/utils/DateFormatter';
import { SLConfirm } from '@/utils/Message';
import {
  postClearAlarm,
  ExportAlarmReport
} from '@/api/shuiwureports/zhandian';
import {
  useAlarms,
  formatAlarmLevel,
  formatAlarmStatus,
  formatProcessStatus
} from '@/hooks/useAlarm';
import { ElMessage } from 'element-plus';
// import router from '@/router'

const emit = defineEmits(['click']);
const refPop = ref<HTMLDivElement>();
const viewIns: __esri.MapView | undefined = inject('view');
const popIdPrefix = 'alarm_pop_canvas_';
const waterpoint = useWaterPoint('viewDiv', { popIdPrefix });
const showAlarmPop = ref<boolean>(false);
const alarms = useAlarms();
const resolveStationAlarms = async () => {
  const data: any[] = [];
  await alarms.groupAlarmsByStationId();
  for (const key in alarms.alarmGroups.value) {
    const location =
      alarms.alarmGroups.value[key].station?.location?.split(',');
    data.push({
      id: key,
      point: location
        ? new Point({
            longitude: location?.[0],
            latitude: location?.[1],
            spatialReference: viewIns?.spatialReference
          })
        : undefined
    });
  }

  waterpoint.removeAll();
  waterpoint.addMany(viewIns, data, {
    width: 30,
    height: 30,
    color: '#ff0000'
  });
};
const addStationWarnings = async () => {
  waterpoint.removeAll();
  Promise.allSettled([
    alarms.getalarms({
      page: 1,
      size: 99999,
      processStatus: '1',
      alarmStatus: '1'
    })
  ]).then(() => {
    resolveStationAlarms();
  });
};
let popPoint: __esri.Point | undefined;
const handleClick = (e: MouseEvent) => {
  const curDom = e.target as HTMLCanvasElement | undefined;
  if (curDom?.id.startsWith(popIdPrefix)) {
    const alarmId = curDom.id.replace(popIdPrefix, '');

    emit('click', alarmId);
    alarms.setCurAlarmGroup(alarmId);
    showAlarmPop.value = true;
    popPoint = viewIns?.toMap(e);
    const screenPoint = popPoint && viewIns?.toScreen(popPoint);
    if (screenPoint && refPop.value) {
      refPop.value.style.left = screenPoint.x + 'px';
      refPop.value.style.top = screenPoint.y + 'px';
    }
  } else {
    showAlarmPop.value = false;
  }
};
const togglePop = (flag?: boolean) => {
  if (flag === undefined) {
    showAlarmPop.value = !showAlarmPop.value;
  } else {
    showAlarmPop.value = !!flag;
  }
};
let extentHandler: any;
const watchExtent = () => {
  if (!viewIns) return;
  extentHandler && extentHandler?.remove();
  extentHandler = viewIns.watch('extent', () => {
    if (!popPoint) return;
    if (!viewIns) return;
    const screenPoint = viewIns.toScreen(popPoint);
    if (screenPoint === null) return;
    if (refPop.value) {
      refPop.value.style.left = screenPoint.x + 'px';
      refPop.value.style.top = screenPoint.y + 'px';
    }
  });
};

function ClearAlarm() {
  SLConfirm('确定解除选中的告警, 是否继续?', '解除提示').then(async () => {
    let ids: any = [];

    ids = (alarms.curAlarmGroup.value?.alarms || []).map((item) => item.id);
    postClearAlarm(ids).then((res) => {
      if (res.data.code === 200) {
        ElMessage.success('解除告警成功');
        addStationWarnings();
      } else {
        ElMessage.warning('解除告警失败');
      }
    });
  });
}

const exportTable = () => {
  const params = {
    page: 1,
    size: 99999,
    processStatus: '1',
    alarmStatus: '1',
    stationId: (alarms.curAlarmGroup.value?.alarms || [])
      .map((item) => item.stationId)
      .join(',')
  };
  ExportAlarmReport(params).then((res) => {
    const url = window.URL.createObjectURL(res.data);
    const link = document.createElement('a');
    link.style.display = 'none';
    link.href = url;
    link.setAttribute(
      'download',
      `${alarms.curAlarmGroup.value?.station?.name}.xlsx`
    );
    document.body.appendChild(link);
    link.click();
  });
};

onMounted(async () => {
  document.addEventListener('click', handleClick, false);
  refPop.value?.addEventListener('click', (e) => {
    e.stopPropagation();
  });

  await viewIns?.when();
  if (window.SITE_CONFIG.GIS_CONFIG.gisShowAlarms !== false) {
    addStationWarnings();
  }
  watchExtent();
});
onBeforeUnmount(() => {
  document.removeEventListener('click', handleClick);
  extentHandler?.remove();
});
defineExpose({
  togglePop
});
</script>
<style lang="scss" scoped>
.alarm-water-pop {
  position: absolute;
  width: 240px;
  height: 260px;
  background-color: var(--el-bg-color);
  transform: translateX(-260px) translateY(-250px);
  border-radius: 8px 8px 0 8px;
  padding: 12px;
  font-size: 12px;
  overflow: hidden;
  color: var(--el-text-color-primary);
  .inner {
    height: calc(100% - 30px);
    overflow: hidden;
    overflow-y: auto;

    &::-webkit-scrollbar {
      display: none;
    }
  }
  .title {
    width: 100%;
    font-size: 18px;
    height: 1em;
    padding: 0;
    margin: 0;
    margin-bottom: 12px;
    & > span {
      font-size: 14px;
      font-weight: 600;
      word-break: keep-all;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
  .alarm-card {
    --el-card-padding: 12px;
  }
  .alarm-item {
    line-height: 1.5em;
    padding: 0;
    margin: 0;
    font-size: 12px;
    margin-bottom: 8px;
    .label {
      font-weight: 600;
      margin-right: 8px;
    }
    .value {
    }
  }
  .alarm-records-title {
    font-size: 12px;
    font-weight: 600;
  }
}
</style>
