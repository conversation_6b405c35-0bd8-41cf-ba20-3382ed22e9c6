<!-- 工程管理-设备管理-设备类别 -->
<template>
  <TreeBox>
    <template #tree>
      <SLTree :tree-data="TreeData" />
    </template>
    <CardSearch ref="refSearch" :config="cardSearchConfig" />
    <CardTable class="card-table" :config="TableConfig" />
    <DialogForm ref="refForm" :config="addOrUpdateConfig"></DialogForm>
  </TreeBox>
</template>

<script lang="ts" setup>
import { Refresh } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { ICONS } from '@/common/constans/common';
import useGlobal from '@/hooks/global/useGlobal';
import { SLConfirm } from '@/utils/Message';
import {
  geDeviceTypeTree,
  postDeviceType,
  geDeviceTypeList,
  deleteDeviceType
} from '@/api/engineeringManagement/device';
import { traverse, objectFlattening } from '@/utils/GlobalHelper';
import { formatDate } from '@/utils/DateFormatter';

const { $btnPerms } = useGlobal();

const refForm = ref<IDialogFormIns>();

const refSearch = ref<ICardSearchIns>();

const cardSearchConfig = ref<ISearch>({
  filters: [
    { label: '类别编码', field: 'serialId', type: 'input' },
    { label: '类别名称', field: 'name', type: 'input' }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '新建顶级类别',
          icon: ICONS.ADD,
          type: 'success',
          click: () => clickCreatedRole('新建顶级类别')
        },
        {
          type: 'default',
          perm: true,
          text: '重置',
          svgIcon: shallowRef(Refresh),
          click: () => {
            refSearch.value?.resetForm();
            refreshData();
          }
        },
        {
          perm: true,
          text: '查询',
          icon: ICONS.QUERY,
          click: () => refreshData(true)
        }
      ]
    }
  ]
});

const TableConfig = reactive<ICardTable>({
  defaultExpandAll: true,
  indexVisible: true,
  rowKey: 'id',
  columns: [
    {
      label: '类别编码',
      prop: 'serialId',
      formatter: (row) =>
        row.serialId.replace(/(\d{2})(\d{3})(\d{3})(\d{6})/, '$1-$2-$3-$4')
    },
    { label: '类别名称(层级)', prop: 'treePath' },
    { label: '节点级别', prop: 'level' },
    { label: '排序编号', prop: 'orderNum' },
    { label: '备注', prop: 'remark' },
    { label: '创建人', prop: 'creatorName' },
    {
      label: '创建时间',
      prop: 'createTime',
      formatter: (row) => formatDate(row.createTime, 'YYYY-MM-DD HH:mm:ss')
    }
  ],
  operationWidth: '240px',
  operations: [
    {
      disabled: (row) => row.level === 3,
      type: 'success',
      text: '新建子集',
      icon: ICONS.ADD,
      perm: $btnPerms('RoleManageAdd'),
      click: (row) => clickCreatedRole('新建子集', row)
    },
    {
      disabled: (row) => row.level === 0,
      type: 'primary',
      text: '编辑',
      icon: ICONS.EDIT,
      perm: $btnPerms('RoleManageEdit'),
      click: (row) => clickEdit(row)
    },
    {
      disabled: (row) => row.level === 0,
      type: 'danger',
      text: '删除',
      perm: $btnPerms('RoleManageDelete'),
      icon: ICONS.DELETE,
      click: (row) => handleDelete(row)
    }
  ],
  dataList: [],
  pagination: {
    hide: true
  }
});

const addOrUpdateConfig = reactive<IDialogFormConfig>({
  title: '新增',
  dialogWidth: '500px',
  labelWidth: '100px',
  submit: (params: any) => {
    const key = params.serialId;
    params.serialId = data.prepend + params.serialIdNum + data.append;
    // 编码判断
    if (!(key === params.serialId) && codeCheck(params)) return;
    let val = '添加成功';
    if (params.id) val = '编辑成功';
    postDeviceType(params)
      .then(() => {
        ElMessage.success(val);
        refForm.value?.closeDialog();
        init();
      })
      .catch((error) => {
        ElMessage.warning(error);
      });
  },
  defaultValue: {},
  group: [
    {
      fields: [
        {
          type: 'select-tree',
          label: '所属类别',
          field: 'category',
          readonly: true,
          checkStrictly: true,
          options: computed(() => traverse(TreeData.data)) as any
        },
        {
          type: 'input',
          label: '类别编码',
          field: 'serialIdNum',
          rules: [{ required: true, message: '请输入类别编码' }],
          prepend: computed(() => data.prepend) as any,
          append: computed(() => data.append) as any
        },
        {
          type: 'hint',
          text: '注：编码规则(长度14)>=12(级别1)+001(级别2)+001(级别3)+000000(设备编码)'
        },
        {
          type: 'input',
          label: '类别名称',
          field: 'name',
          rules: [{ required: true, message: '请输入类别名称' }]
        },
        {
          type: 'input',
          label: '节点级别',
          field: 'level',
          disabled: true
        },
        {
          type: 'input-number',
          label: '排序编号',
          field: 'sortNum',
          rules: [{ required: true, message: '请输入排序编号' }]
        },
        {
          type: 'textarea',
          label: '备注/说明',
          field: 'remark'
        }
      ]
    }
  ]
});

// 类别编码校验
function codeCheck(params) {
  if (params.serialId.length !== 14) {
    ElMessage.warning('类别编码格式错误');
    return true;
  }
  const value = objectFlattening(TreeData.data || [], 'children', 'serialId');
  if (value.includes(params.serialId)) {
    ElMessage.warning('类别编码重复');
    return true;
  }
}

function encodingDefault(params, level: number) {
  const value =
    objectFlattening(params.children || [params] || [], ' ', 'serialId') || [];
  let max = Math.max(...value);
  if (level === 1) {
    max += 1000000000000;
  }
  if (level === 2) {
    max += 1000000000;
  }
  if (level === 3) {
    max += 1000000;
  }
  return (max + '').length === 13 ? '0' + max + '' : max + '';
}

const TreeData = reactive<SLTreeConfig>({
  title: '设备类别',
  data: [],
  currentProject: {},
  expandOnClickNode: false,
  isFilterTree: true,
  treeNodeHandleClick: (data) => {
    // 设置当前选中项目信息
    TreeData.currentProject = data;
    refreshData();
  }
});

const clickCreatedRole = (title?: any, row?: any) => {
  if (title === '新建顶级类别' && !TreeData.data.length) {
    ElMessage.warning('请等待结构加载完成');
    return;
  }
  addOrUpdateConfig.title = title;
  let level: any = 1;
  const serialId = encodingDefault(TreeData.data[0], level);
  let serialIdNum = '';
  if (level === 1) {
    data.prepend = '';
    data.append = serialId.slice(2, 14);
    serialIdNum = serialId.slice(0, 2);
  }
  addOrUpdateConfig.defaultValue = {
    category: TreeData.data[0]?.id || '',
    level: '1',
    serialIdNum
  };
  if (title === '新建子集') {
    level = row.level * 1 + 1;
    const serialId = encodingDefault(row, level);
    if (level === 2) {
      data.prepend = serialId.slice(0, 2);
      data.append = serialId.slice(5, 14);
      serialIdNum = serialId.slice(2, 5);
    }
    if (level === 3) {
      data.prepend = serialId.slice(0, 5);
      data.append = serialId.slice(8, 14);
      serialIdNum = serialId.slice(5, 8);
    }
    if (level > 3) {
      ElMessage.warning('最多三级');
      return;
    }
    addOrUpdateConfig.defaultValue = {
      parentId: row.id,
      category: row.id,
      level,
      serialIdNum
    };
  }

  refForm.value?.openDialog();
};

const clickEdit = (row: { [x: string]: any }) => {
  addOrUpdateConfig.title = '编辑';
  const level = row.level;
  if (level === 1) {
    data.prepend = '';
    data.append = row.serialId.slice(2, 14);
    row.serialIdNum = row.serialId.slice(0, 2);
  }
  if (level === 2) {
    data.prepend = row.serialId.slice(0, 2);
    data.append = row.serialId.slice(5, 14);
    row.serialIdNum = row.serialId.slice(2, 5);
  }
  if (level === 3) {
    data.prepend = row.serialId.slice(0, 5);
    data.append = row.serialId.slice(8, 14);
    row.serialIdNum = row.serialId.slice(5, 8);
  }
  addOrUpdateConfig.defaultValue = {
    category: row.parentId,
    sortNum: row.orderNum,
    ...(row || {})
  };
  refForm.value?.openDialog();
};

const handleDelete = (row?: any) => {
  SLConfirm('确定删除该设备类别', '删除提示').then(() => {
    deleteDeviceType(row.id)
      .then(() => {
        ElMessage.success('删除成功');
        init();
      })
      .catch((error) => {
        ElMessage.error(error.toString());
      });
  });
};

function init() {
  geDeviceTypeTree().then((res) => {
    TreeData.data = traverse(res.data.data || []);
    TreeData.currentProject = res.data.data[0];
    TableConfig.dataList = TreeData.data;
  });
}

const data = reactive({
  prepend: '',
  append: ''
});

const refreshData = async (serch?: boolean) => {
  if (serch) {
    const params: any = {
      page: -1,
      size: 20,
      ...(refSearch.value?.queryParams || {})
    };
    if (params.serialId) {
      params.serialId = params.serialId.replace(/-/g, '');
    }
    geDeviceTypeList(params).then((res) => {
      TableConfig.dataList = res.data.data.data;
    });
  } else {
    TableConfig.dataList = [TreeData.currentProject];
  }
};

onMounted(async () => {
  init();
});
</script>
