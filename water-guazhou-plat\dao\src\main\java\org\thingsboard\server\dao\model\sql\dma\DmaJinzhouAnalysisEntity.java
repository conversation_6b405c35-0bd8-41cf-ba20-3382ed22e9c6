package org.thingsboard.server.dao.model.sql.dma;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 锦州产销差分析
 */
@Data
@TypeDef(name = "json", typeClass = JsonStringType.class)
@TableName("dma_jinzhou_analysis")
@NoArgsConstructor
@AllArgsConstructor
public class DmaJinzhouAnalysisEntity {
    @TableId
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @TableField("codes")
    private String codes;

    @TableField("month")
    private String month;

    @TableField("supply")
    private BigDecimal supply;

    @TableField("use")
    private BigDecimal use;

    @TableField("user_num")
    private Integer userNum;

    @Column(name = ModelConstants.DMA_ANALYSIS_CREATE_TIME)
    private Date createTime;

}
