package org.thingsboard.server.dao.smartService.seats;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.department.Department;
import org.thingsboard.server.dao.model.sql.department.Organization;
import org.thingsboard.server.dao.model.sql.smartService.seats.SeatsUser;
import org.thingsboard.server.dao.sql.department.DepartmentMapper;
import org.thingsboard.server.dao.sql.department.OrganizationMapper;
import org.thingsboard.server.dao.sql.smartService.seats.SeatsUserMapper;

import java.util.Date;
import java.util.List;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-10-27
 */
@Slf4j
@Service
@Transactional
public class SeatsUserServiceImpl implements SeatsUserService {
    @Autowired
    private SeatsUserMapper seatsUserMapper;
    @Autowired
    private DepartmentMapper departmentMapper;
    @Autowired
    private OrganizationMapper organizationMapper;
    @Autowired
    private SeatsExtensionService seatsExtensionService;

    @Override
    public PageData getList(String keywords, int page, int size, String tenantId) {

        List<SeatsUser> seatsUserList = seatsUserMapper.getList(keywords, page, size);

        // 获取组织
        this.getOrganizationName(seatsUserList);

        int total = seatsUserMapper.getListCount(keywords);

        return new PageData(total, seatsUserList);

    }

    private void getOrganizationName(List<SeatsUser> seatsUserList) {
        for (SeatsUser seatsUser : seatsUserList) {
            if (StringUtils.isNotBlank(seatsUser.getDepartmentId())) {
                Department department = departmentMapper.selectById(seatsUser.getDepartmentId());
                String parentId = department.getParentId();
                while (department != null) {
                    department = departmentMapper.selectById(department.getParentId());
                }
                if (department != null) {
                    parentId = department.getParentId();
                }
                if (parentId != null) {
                    Organization organization = organizationMapper.selectById(parentId);
                    if (organization != null) {
                        seatsUser.setOrganizationName(organization.getName());
                    }
                }
            }
        }

    }

    private Integer getUserNo() {
        Integer userNo = seatsUserMapper.getUserNo();
        if (userNo == null) {
            userNo = 1000;
        }
        return userNo + 1;
    }

    @Override
    public SeatsUser save(SeatsUser seatsUser) {
        seatsUser.setUserNo(null);
        seatsUserMapper.updateById(seatsUser);

        return seatsUser;
    }

    @Override
    public int delete(List<String> ids) {
        return seatsUserMapper.deleteBatchIds(ids);
    }

    @Override
    public String check(List<String> seatsUserIdList, String tenantId) {
        QueryWrapper<SeatsUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("user_id", seatsUserIdList);
        List<SeatsUser> list = seatsUserMapper.selectList(queryWrapper);
        for (SeatsUser seatsUser : list) {
            if (seatsUserIdList.contains(seatsUser.getUserId())) {
                return "存在已绑定的用户，请先解绑或重新选择";
            }
        }

        SeatsUser seatsUser;
        for (int i = 0; i < seatsUserIdList.size(); i++) {
            seatsUser = new SeatsUser();
            seatsUser.setUserId(seatsUserIdList.get(i));
            seatsUser.setTenantId(tenantId);
            seatsUser.setCreateTime(new Date());
            seatsUser.setUserNo(getUserNo());
            seatsUser.setOrderNum(seatsUser.getUserNo());
            seatsUserMapper.insert(seatsUser);
        }
        return "";
    }

}
