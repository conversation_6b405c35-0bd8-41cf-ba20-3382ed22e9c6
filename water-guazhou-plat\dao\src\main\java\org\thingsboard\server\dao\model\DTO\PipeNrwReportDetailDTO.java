package org.thingsboard.server.dao.model.DTO;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 流量分析
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2023-05-04
 */
@Data
public class PipeNrwReportDetailDTO implements Serializable {

    private String partitionId;

    private String code;

    private String partitionName;

    private BigDecimal correctNrwRate = BigDecimal.ZERO;

    private BigDecimal supplyTotal = BigDecimal.ZERO;

    private BigDecimal correctUseWater = BigDecimal.ZERO;

    private Integer userNum = 0;

    private BigDecimal inWater = BigDecimal.ZERO;

    private BigDecimal outWater = BigDecimal.ZERO;

    private BigDecimal noIncomeWater = BigDecimal.ZERO;


}
