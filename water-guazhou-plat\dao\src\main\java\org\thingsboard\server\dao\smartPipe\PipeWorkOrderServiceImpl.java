package org.thingsboard.server.dao.smartPipe;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.DTO.PipeWorkOrderDTO;
import org.thingsboard.server.dao.model.request.PipeWorkOrderRequest;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.PipeWorkOrder;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrder;
import org.thingsboard.server.dao.orderWork.NewlyWorkOrderService;
import org.thingsboard.server.dao.sql.smartPipe.PipeWorkOrderMapper;
import org.thingsboard.server.dao.util.imodel.query.workOrder.PipeWorkOrderSaveRequest;
import org.thingsboard.server.dao.util.imodel.query.workOrder.WorkOrderSaveRequest;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;

/**
 *
 */
@Service
public class PipeWorkOrderServiceImpl implements PipeWorkOrderService {

    @Autowired
    private PipeWorkOrderMapper pipeWorkOrderMapper;

    @Autowired
    private NewlyWorkOrderService newlyWorkOrderService;

    @Override
    public WorkOrderSaveRequest save(PipeWorkOrderSaveRequest pipeWorkOrderSaveRequest) {
        pipeWorkOrderSaveRequest.setSource("漏损控制");
        WorkOrder workOrder = newlyWorkOrderService.save(pipeWorkOrderSaveRequest);

        PipeWorkOrder pipeWorkOrder = new PipeWorkOrder();
        pipeWorkOrder.setWorkOrderId(workOrder.getId());
        pipeWorkOrder.setPartitionId(pipeWorkOrderSaveRequest.getPartitionId());
        pipeWorkOrder.setCreateTime(new Date());
        pipeWorkOrder.setTenantId(workOrder.getTenantId());
        pipeWorkOrderMapper.insert(pipeWorkOrder);
        return pipeWorkOrderSaveRequest;
    }


    @Override
    public PageData<PipeWorkOrderDTO> getList(PipeWorkOrderRequest request) {
        IPage<PipeWorkOrderDTO> page = new Page<>(request.getPage(), request.getSize());

        // 事件处理
        if (StringUtils.isNotBlank(request.getType())) {
            try {
                LocalDate startTime = null;
                LocalDate endTime = null;
                switch (request.getType()) {
                    case "year":
                        startTime = LocalDate.of(Integer.valueOf(request.getYear()), 1, 1);
                        endTime = startTime.plusYears(1L);
                        break;

                    case "month":
                        startTime = LocalDate.parse(request.getMonth() + "-01", DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                        endTime = startTime.plusMonths(1L);
                        break;

                    case "day":
                        startTime = LocalDate.parse(request.getStart(), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                        endTime = LocalDate.parse(request.getEnd(), DateTimeFormatter.ofPattern("yyyy-MM-dd")).plusDays(1L);
                }
                request.setStartTime(startTime);
                request.setEndTime(endTime);
            } catch (Exception e) {
            }

        }
        IPage<PipeWorkOrderDTO> result = pipeWorkOrderMapper.getList(page, request);
        return new PageData<>(result.getTotal(), result.getRecords());
    }

    @Override
    public void delete(List<String> ids) {
        pipeWorkOrderMapper.deleteBatchIds(ids);
    }

}
