package org.thingsboard.server.dao.base.impl;

import java.util.List;
import java.util.UUID;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.base.IBasePushTemplateConfigurationService;
import org.thingsboard.server.dao.model.sql.base.BasePushTemplateConfiguration;
import org.thingsboard.server.dao.sql.base.BasePushTemplateConfigurationMapper;
import org.thingsboard.server.dao.util.imodel.query.base.BasePushTemplateConfigurationPageRequest;

/**
 * 平台管理-推送模板配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-18
 */
@Service
public class BasePushTemplateConfigurationServiceImpl implements IBasePushTemplateConfigurationService {

    @Autowired
    private BasePushTemplateConfigurationMapper basePushTemplateConfigurationMapper;

    /**
     * 查询平台管理-推送模板配置
     *
     * @param id 平台管理-推送模板配置主键
     * @return 平台管理-推送模板配置
     */
    @Override
    public BasePushTemplateConfiguration selectBasePushTemplateConfigurationById(String id) {
        return basePushTemplateConfigurationMapper.selectBasePushTemplateConfigurationById(id);
    }

    /**
     * 查询平台管理-推送模板配置列表
     *
     * @param basePushTemplateConfiguration 平台管理-推送模板配置
     * @return 平台管理-推送模板配置
     */
    @Override
    public IPage<BasePushTemplateConfiguration> selectBasePushTemplateConfigurationList(BasePushTemplateConfigurationPageRequest basePushTemplateConfiguration) {
        return basePushTemplateConfigurationMapper.selectBasePushTemplateConfigurationList(basePushTemplateConfiguration);
    }

    /**
     * 新增平台管理-推送模板配置
     *
     * @param basePushTemplateConfiguration 平台管理-推送模板配置
     * @return 结果
     */
    @Override
    public int insertBasePushTemplateConfiguration(BasePushTemplateConfiguration basePushTemplateConfiguration) {
        basePushTemplateConfiguration.setId(UUID.randomUUID().toString().replace("-", ""));
        return basePushTemplateConfigurationMapper.insertBasePushTemplateConfiguration(basePushTemplateConfiguration);
    }

    /**
     * 修改平台管理-推送模板配置
     *
     * @param basePushTemplateConfiguration 平台管理-推送模板配置
     * @return 结果
     */
    @Override
    public int updateBasePushTemplateConfiguration(BasePushTemplateConfiguration basePushTemplateConfiguration) {
        return basePushTemplateConfigurationMapper.updateBasePushTemplateConfiguration(basePushTemplateConfiguration);
    }

    /**
     * 批量删除平台管理-推送模板配置
     *
     * @param ids 需要删除的平台管理-推送模板配置主键
     * @return 结果
     */
    @Override
    public int deleteBasePushTemplateConfigurationByIds(List<String> ids) {
        return basePushTemplateConfigurationMapper.deleteBasePushTemplateConfigurationByIds(ids);
    }

    /**
     * 删除平台管理-推送模板配置信息
     *
     * @param id 平台管理-推送模板配置主键
     * @return 结果
     */
    @Override
    public int deleteBasePushTemplateConfigurationById(String id) {
        return basePushTemplateConfigurationMapper.deleteBasePushTemplateConfigurationById(id);
    }
}
