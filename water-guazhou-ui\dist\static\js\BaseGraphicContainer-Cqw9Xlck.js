import{cl as A,aW as R}from"./index-r0dFAfgr.js";import{o as D}from"./FeatureContainer-B5oUlI2-.js";import{dG as c,dH as E,dI as C,bu as I,dJ as O,dK as L,dL as $,dM as N}from"./MapView-DaoQedLH.js";import{r as S}from"./vec3f32-nZdmKIgz.js";import{r as T}from"./Container-BwXq1a-x.js";import{e as w}from"./color-DAS1c3my.js";import{E as m,f as F}from"./FramebufferObject-8j9PRuxE.js";import{R as p,E as P,C as x,F as g,I as U}from"./enums-BDQrMlcz.js";const W=Math.PI/180,q=4;class G extends T{constructor(r){super(),this._program=null,this._vao=null,this._vertexBuffer=null,this._indexBuffer=null,this._dvsMat3=c(),this._localOrigin={x:0,y:0},this._getBounds=r}destroy(){this._vao&&(this._vao.dispose(!0),this._vao=null,this._vertexBuffer=null,this._indexBuffer=null),this._program=A(this._program)}doRender(r){const{context:t}=r,n=this._getBounds();if(n.length<1)return;this._createShaderProgram(t),this._updateMatricesAndLocalOrigin(r),this._updateBufferData(t,n),t.setBlendingEnabled(!0),t.setDepthTestEnabled(!1),t.setStencilWriteMask(0),t.setStencilTestEnabled(!1),t.setBlendFunction(p.ONE,p.ONE_MINUS_SRC_ALPHA),t.setColorMask(!0,!0,!0,!0);const d=this._program;t.bindVAO(this._vao),t.useProgram(d),d.setUniformMatrix3fv("u_dvsMat3",this._dvsMat3),t.gl.lineWidth(1),t.drawElements(P.LINES,8*n.length,x.UNSIGNED_INT,0),t.bindVAO()}_createTransforms(){return{dvs:c()}}_createShaderProgram(r){if(this._program)return;const t=`precision highp float;
        uniform mat3 u_dvsMat3;

        attribute vec2 a_position;

        void main() {
          mediump vec3 pos = u_dvsMat3 * vec3(a_position, 1.0);
          gl_Position = vec4(pos.xy, 0.0, 1.0);
        }`,n=`precision mediump float;
      void main() {
        gl_FragColor = vec4(0.75, 0.0, 0.0, 0.75);
      }`;this._program=r.programCache.acquire(t,n,v().attributes)}_updateMatricesAndLocalOrigin(r){const{state:t}=r,{displayMat3:n,size:d,resolution:u,pixelRatio:a,rotation:o,viewpoint:e}=t,h=W*o,{x:i,y}=e.targetGeometry,b=E(i,t.spatialReference);this._localOrigin.x=b,this._localOrigin.y=y;const _=a*d[0],f=a*d[1],B=u*_,M=u*f,l=C(this._dvsMat3);I(l,l,n),O(l,l,L(_/2,f/2)),$(l,l,S(d[0]/B,-f/M,1)),N(l,l,-h)}_updateBufferData(r,t){const{x:n,y:d}=this._localOrigin,u=2*q*t.length,a=new Float32Array(u),o=new Uint32Array(8*t.length);let e=0,h=0;for(const i of t)i&&(a[2*e+0]=i[0]-n,a[2*e+1]=i[1]-d,a[2*e+2]=i[0]-n,a[2*e+3]=i[3]-d,a[2*e+4]=i[2]-n,a[2*e+5]=i[3]-d,a[2*e+6]=i[2]-n,a[2*e+7]=i[1]-d,o[h+0]=e+0,o[h+1]=e+3,o[h+2]=e+3,o[h+3]=e+2,o[h+4]=e+2,o[h+5]=e+1,o[h+6]=e+1,o[h+7]=e+0,e+=4,h+=8);if(this._vertexBuffer?this._vertexBuffer.setData(a.buffer):this._vertexBuffer=m.createVertex(r,g.DYNAMIC_DRAW,a.buffer),this._indexBuffer?this._indexBuffer.setData(o):this._indexBuffer=m.createIndex(r,g.DYNAMIC_DRAW,o),!this._vao){const i=v();this._vao=new F(r,i.attributes,i.bufferLayouts,{geometry:this._vertexBuffer},this._indexBuffer)}}}const v=()=>w("bounds",{geometry:[{location:0,name:"a_position",count:2,type:x.FLOAT}]});let Q=class extends D{constructor(s){super(s),this.hasHighlight=()=>!0}destroy(){super.destroy(),this._boundsRenderer=R(this._boundsRenderer)}enableRenderingBounds(s){this._boundsRenderer=new G(s),this.requestRender()}get hasLabels(){return!1}onTileData(s,r){s.patch(r),this.contains(s)||this.addChild(s),this.requestRender()}onTileError(s){s.clear(),this.contains(s)||this.addChild(s)}_renderChildren(s,r){for(const t of this.children)t.isReady&&t.hasData&&(t.commit(s),s.context.setStencilFunction(U.EQUAL,t.stencilRef,255),t.getDisplayList().replay(s,t,r))}};export{Q as n};
