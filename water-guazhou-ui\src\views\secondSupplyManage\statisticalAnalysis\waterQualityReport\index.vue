<!-- 水量报告 -->
<template>
  <div class="wrapper">
    <div
      v-loading="totalLoading"
      class="main"
    >
      <div class="right">
        <CardSearch
          ref="cardSearch"
          :config="cardSearchConfig"
        />
        <CardTable
          id="print"
          ref="refTable"
          class="card-table"
          :config="cardTableConfig"
        />
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import dayjs from 'dayjs'
import { Printer } from '@element-plus/icons-vue'
import { reportType } from '../data/data'
import { getFormatTreeNodeDeepestChild, objectLookup } from '@/utils/GlobalHelper'
import { ICardTableIns, ISearchIns } from '@/components/type'
import { getWaterSupplyReport } from '@/api/secondSupplyManage/statisticalAnalysis'
import useStation from '@/hooks/station/useStation'
import { printJSON } from '@/utils/printUtils'
import { TrueExcel } from '@/utils/exportExcel'

const excel = new TrueExcel()
const { getStationTree } = useStation()
const state = reactive<{
  queryType: 'day' | 'month' | 'year';
  treeDataType: string;
  stationId: string;
  title: string
  sums: any;
  min: any;
  max: any;
  average: any;
  calculation: any;
  columns:any;
}>({
  queryType: 'day',
  treeDataType: 'Station',
  stationId: '',
  title: '',
  sums: {},
  min: {},
  max: {},
  average: {},
  calculation: [],
  columns: []
})
const totalLoading = ref<boolean>(false)
const today = dayjs().date()

const refTable = ref<ICardTableIns>()
const cardSearch = ref<ISearchIns>()

// 水源站点树
const TreeData = reactive<SLTreeConfig>({
  data: [],
  currentProject: {}
})

// 搜索栏初始化配置
const cardSearchConfig = reactive<ISearch>({
  defaultParams: {
    type: 'day',
    year: [dayjs().format(), dayjs().format()],
    month: [dayjs().format(), dayjs().format()],
    day: [dayjs().date(today - 6).format('YYYY-MM-DD'), dayjs().date(today).format('YYYY-MM-DD')]

  },
  filters: [
    {
      type: 'select-tree',
      field: 'treeData',
      checkStrictly: true,
      defaultExpandAll: true,
      options: computed(() => TreeData.data) as any,
      label: '站点选择',
      onChange: key => {
        const val = objectLookup(TreeData.data, 'children', 'id', key)
        TreeData.currentProject = val
        state.treeDataType = val.data.type as string
        if (state.treeDataType === 'Station') {
          state.stationId = val.id as string
          refreshData()
        }
      }
    },
    {
      type: 'radio-button',
      field: 'type',
      options: reportType,
      label: '报告类型'
    },
    { type: 'daterange',
      label: '选择时间',
      field: 'day',
      clearable: false,
      handleHidden: (params: any, query: any, formItem: IFormItem) => {
        formItem.hidden = params.type === 'month' || params.type === 'year'
      }
    },
    { type: 'monthrange',
      label: '选择时间',
      field: 'month',
      clearable: false,
      handleHidden: (params: any, query: any, formItem: IFormItem) => {
        formItem.hidden = params.type === 'day' || params.type === 'year'
      }
    },
    { type: 'yearrange',
      label: '选择时间',
      field: 'year',
      clearable: false,
      handleHidden: (params: any, query: any, formItem: IFormItem) => {
        formItem.hidden = params.type === 'month' || params.type === 'day'
      }
    },
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          click: () => refreshData(),
          icon: 'iconfont icon-chaxun'
        },
        {
          text: '导出',
          // perm: $btnPerms('user_manage_addUser'),
          perm: true,
          type: 'warning',
          icon: 'iconfont icon-xiazai',
          click: () => _exportWaterQuality()
        },
        {
          perm: true,
          text: '打印',
          type: 'success',
          svgIcon: shallowRef(Printer),
          click: () => handlePrint()
        }
      ]
    }
  ]
})

// 初始化列表配置数据
const cardTableConfig = reactive<ITable>({
  loading: false,
  dataList: [],
  columns: [],
  operations: [],
  operationWidth: '150px',
  pagination: {
    hide: true
  }
})

// 刷新列表 模拟数据
const refreshData = () => {
  cardTableConfig.loading = true
  const queryParams = cardSearch.value?.queryParams as any || {}
  const type = reportType.find(type => type.value === queryParams.type)
  const date = queryParams[type.value]
  console.log(type)
  state.title = TreeData.currentProject.label + '水量报表' + '(' + type.label + dayjs(date[0]).format(type.data) + '至' + dayjs(date[1]).format(type.data) + ')'
  cardTableConfig.title = state.title
  const stationId = TreeData.currentProject.id
  const params = {
    stationId,
    start: dayjs(date[0]).startOf(queryParams.type).valueOf(),
    end: dayjs(date[1]).endOf(queryParams.type).valueOf(),
    queryType: queryParams.type
  }
  getWaterSupplyReport(params).then(res => {
    const data = res.data?.data

    state.columns = data?.tableInfo?.map((item: any) => {
      return {
        prop: item.columnValue,
        label: item.columnName,
        minwidth: 130,
        unit: item.unit ? '(' + (item.unit) + ')' : ''
      }
    })
    cardTableConfig.columns = state.columns
    cardTableConfig.dataList = data?.tableDataList
    cardTableConfig.loading = false
    console.log(state.columns.length)
    // getSummaries(state.columns, data?.tableDataList)
  })
}
// 计算合计 暂时无用
// const getSummaries = (columns: any, data: any) => {
//   const sums: string[] = []
//   const average: string[] = []
//   const min: string[] = []
//   const max: string[] = []
//   columns?.forEach((column, index) => {
//     if (index === 0) {
//       sums[index] = '合计'
//       average[index] = '平均值'
//       min[index] = '最小值'
//       max[index] = '最大值'
//       state.min[index] = min[index]
//       state.max[index] = max[index]
//       return
//     }
//     const values = data.map(item => Number(item[column.prop]))
//     if (!values.every(value => Number.isNaN(value))) {
//       sums[index] = `${values.reduce((prev, curr) => {
//         const value = Number(curr)
//         if (!Number.isNaN(value)) {
//           return Number(new BigNumber(prev).plus(curr))
//         }
//         return prev
//       }, 0)}`
//       values.map((value, i) => {
//         min[index] = i === 0 || parseFloat(min[index]) - value >= 0 ? '' + value : min[index]
//         max[index] = i === 0 || (parseFloat(max[index]) - value) <= 0 ? ('' + value) : max[index]
//       })
//       average[index] = '' + (Number(sums[index]) / 24).toFixed(2)
//     } else {
//       sums[index] = '-'
//       average[index] = '-'
//       min[index] = '-'
//       max[index] = '-'
//     }
//     state.min[index] = min[index]
//     state.max[index] = max[index]
//   })
//   // state.average = average
//   // state.max = max
//   // state.min = min
//   // state.sums = sums
//   state.calculation = []
//   state.calculation.push(state.min)
//   state.calculation.push(state.average)
//   state.calculation.push(state.max)
//   console.log(state.average)
// }
// 导出水量报告
const _exportWaterQuality = () => {
  // refTable.value?.exportTable()
  excel.addElTable(refTable.value)
  // excel.sheet.addRow(state.sums)
  // excel.sheet.addRow(state.average)
  // excel.sheet.addRow(state.max)
  // excel.sheet.addRow(state.min)
  excel.export()
}

// 打印报表
const handlePrint = () => {
  printJSON({ title: cardTableConfig.title, titleList: cardTableConfig.columns, data: cardTableConfig.dataList })
}

onMounted(async () => {
  TreeData.data = await getStationTree('泵站')
  TreeData.currentProject = getFormatTreeNodeDeepestChild(TreeData.data)
  cardSearchConfig.defaultParams = { ...cardSearchConfig.defaultParams, treeData: TreeData.currentProject }
  cardSearch.value?.resetForm()
  refreshData()
})
</script>

<style lang="scss" scoped>
.main {
  height: 100%;
  display: flex;

  .left {
    width: 300px;
    border-right: 1px solid var(--el-border-color);
  }

  .right {
    width: 100%;
    // padding-left: 20px;
    background-color :var(--el-mask-color);
    height: 100%;
    overflow-y: auto;
  }
}
:deep(.title){
  width: 100%;
  text-align: center;
}
.card-table{
  page-break-before: always;
  height: calc(100% - 80px)
}
</style>
