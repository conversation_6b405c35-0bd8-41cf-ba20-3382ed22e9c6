package org.thingsboard.server.dao.sql.smartService.wechat;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.smartService.wechat.WxNews;
import org.thingsboard.server.dao.util.imodel.query.smartService.wechat.WxNewsPageRequest;

@Mapper
public interface WxNewsMapper extends BaseMapper<WxNews> {
    IPage<WxNews> findByPage(WxNewsPageRequest request);

    boolean save(WxNews entity);

    boolean updateFully(WxNews entity);

    WxNews getDetailById(@Param("id") String id);
}
