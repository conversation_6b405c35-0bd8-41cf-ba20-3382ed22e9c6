package org.thingsboard.server.dao.util.imodel.query.purchase;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.purchase.DevicePurchaseItem;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

import java.util.Date;

@Getter
@Setter
public class DevicePurchaseItemSaveRequest extends SaveRequest<DevicePurchaseItem> {
    // 采购单ID
    @NotNullOrEmpty(parentIgnore = true)
    private String mainId;

    // 设备编码
    @NotNullOrEmpty
    private String serialId;

    // 询价截止日期
    @NotNullOrEmpty
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date inquiryEndTime;

    // 购买数量
    @NotNullOrEmpty
    private Double num;

    // 是否完成询价
    private Boolean inquired;

    public DevicePurchaseItem build() {
        DevicePurchaseItem entity = new DevicePurchaseItem();
        entity.setTenantId(tenantId());
        entity.setMainId(mainId);
        commonSet(entity);
        return entity;
    }

    public DevicePurchaseItem update(String id) {
        DevicePurchaseItem entity = new DevicePurchaseItem();
        entity.setId(id);
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    private void commonSet(DevicePurchaseItem entity) {
        entity.setInquired(inquired);
        entity.setSerialId(serialId);
        entity.setInquiryEndTime(inquiryEndTime);
        entity.setNum(num);
    }
}