<!--
  采集审核、数据入库、审核退回
  对采集完成并提交的工程进行数据审核，审核通过后可一键入库，不通过则退回
 -->
<!--
  工程列表
  1. 工程列表展示（分在办工程和已办工程）
  2. 工程信息预览
  3. 工程详细查看（包含办理过程）
  4. 工程采集数据导出
  5. 删除工程（仅授权过的用户可删）
  6. 新建工程
  7. 工程派发
 -->
<template>
  <div class="wrapper">
    <CardSearch
      ref="refSearch"
      :config="SearchConfig"
    ></CardSearch>
    <CardTable
      :config="TableConfig"
      class="card-table"
    ></CardTable>
    <DialogForm
      ref="refDialog"
      :config="DialogFormConfig"
    ></DialogForm>
    <SLDrawer
      ref="refDrawer"
      :config="DrawerConfig"
    >
      <template #title>
        <span>{{ TableConfig.currentRow?.name }}</span> <span>{{ TableConfig.currentRow?.code }}</span>
      </template>
      <CollectDetail :row="TableConfig.currentRow"></CollectDetail>
    </SLDrawer>
  </div>
</template>
<script lang="ts" setup>
import CollectDetail from './components/CollectDetail.vue'

const refSearch = ref<ICardSearchIns>()
const SearchConfig = reactive<ISearch>({
  filters: [
    { type: 'input', label: '工程名称', field: 'name' },
    { type: 'input', label: '工程编号', field: 'code' }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        { perm: true, text: '搜索', click: () => refreshData() },
        { perm: true, text: '重置', click: () => resetSearch() }
      ]
    }
  ],
  defaultParams: {}
})
const resetSearch = () => {
  refSearch.value?.resetForm()
}
const TableConfig = reactive<ITable>({
  columns: [
    { label: '工程编号', prop: 'code' },
    { label: '工程名称', prop: 'name' },
    { label: '竣工日期', prop: 'finishDate' }
  ],
  dataList: [],
  operations: [
    { perm: true, text: '详情', click: row => handleDetail(row) },
    { perm: (row: any) => row.audit === 'complete', text: '审核', click: row => handleAudit(row) },
    { perm: (row: any) => row.audit !== 'complete', text: '入库', click: row => handleInput(row) }
  ],
  pagination: {
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page || 1
      TableConfig.pagination.limit = size || 20
      refreshData()
    }
  }
})
const handleAudit = (row: any) => {
  //
}
const handleInput = (row: any) => {
  //
}
const refreshData = () => {
  try {
    const params = {
      ...refSearch.value?.queryParams,
      page: TableConfig.pagination.page,
      size: TableConfig.pagination.limit
    }
    console.log(params)
    // const res = await GetProjectList(params)
    // TableConfig.dataList = res.data.data || []
    // TableConfig.pagination.total = res.data.total || 0

    TableConfig.dataList = [
      { id: 'aaa', code: 'aaa', name: '工程1' },
      { id: 'bbb', code: 'bbb', name: '工程2' }
    ]
  } catch (error) {
    console.log(error)
  }
}

const refDialog = ref<IDialogFormIns>()
const DialogFormConfig = reactive<IDialogFormConfig>({
  title: '创建项目',
  dialogWidth: 550,
  group: [
    {
      fields: [
        { label: '工程编号', field: 'code', type: 'input' },
        { label: '工程名称', field: 'name', type: 'input' }
      ]
    }
  ],
  labelPosition: 'right',
  labelWidth: '100px',
  defaultValue: {},
  submit: () => {
    refDialog.value?.closeDialog()
  }
})
const createPro = () => {
  refDialog.value?.openDialog()
}
const refDrawer = ref<ISLDrawerIns>()

const DrawerConfig = reactive<IDrawerConfig>({
  title: '',
  group: [],
  appendToBody: false
})
const handleDetail = (row: any) => {
  TableConfig.currentRow = row
  refDrawer.value?.openDrawer()
}
onMounted(() => {
  refreshData()
})
</script>
<style lang="scss" scoped></style>
