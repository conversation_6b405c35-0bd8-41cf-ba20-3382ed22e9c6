package org.thingsboard.server.dao.circuit;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.model.sql.smartManagement.plan.GeneralTaskProcessingAndCompleteCount;
import org.thingsboard.server.dao.model.sql.smartProduction.circuit.CircuitTask;
import org.thingsboard.server.dao.model.sql.smartProduction.circuit.GeneralTaskStatus;
import org.thingsboard.server.dao.model.sql.statistic.GeneralTaskStatusStatistic;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.circuit.CircuitTaskItemCompleteRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.circuit.CircuitTaskPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.circuit.CircuitTaskSaveRequest;

import java.util.List;

public interface CircuitTaskService {
    /**
     * 分页条件查询巡检任务单
     *
     * @param request 分页查询条件
     * @return 分页查询结果
     */
    IPage<CircuitTask> findAllConditional(CircuitTaskPageRequest request);

    /**
     * 保存巡检任务单
     *
     * @param entity 实体信息
     * @return 保存好的实体
     */
    CircuitTask save(CircuitTaskSaveRequest entity);

    /**
     * 增量修改
     *
     * @param entity 实体信息
     * @return 是否修改成功
     */
    boolean update(CircuitTask entity);

    /**
     * 删除
     *
     * @param id 唯一标识
     * @return 是否删除成功
     */
    boolean delete(String id);

    /**
     * 接收任务
     *
     * @param id     任务id
     * @param userId 接收任务的用户id
     * @return 是否成功
     */
    boolean receive(String id, String userId);

    /**
     * 请求任务审核，请求审核任务的前提是{@link CircuitTaskItemService#complete(String, CircuitTaskItemCompleteRequest) 任务条目都已完成}
     *
     * @param id          需要提交审核的任务id
     * @param auditUserId 审核人id
     * @param userId      任务执行人id，用于检验当前任务是不是此用户在负责
     * @return 是否成功
     */
    boolean sendVerify(String id, String auditUserId, String userId);

    /**
     * 审核任务
     *
     * @param id     需要审核的任务id
     * @param allow  是否通过
     * @param userId 审核人id，用于检验是不是指定的审核人
     * @return 是否成功
     */
    boolean verify(String id, boolean allow, String userId);

    /**
     * 批量删除
     *
     * @param idList id列表
     * @return 是否成功
     */
    boolean deleteAll(List<String> idList);

    /**
     * 批量保存巡检任务，在保存的同时生成任务条目，
     * 巡检任务条目的生成模板来源于{@link CircuitTask#getTemplateId() 巡检任务实体中的templateId}字段
     *
     * @param taskList 巡检任务实体列表
     * @return 保存好的巡检任务
     */
    List<CircuitTask> saveAllWithGenerateTaskItem(List<CircuitTask> taskList);

    /**
     * 获取指定任务状态的统计信息，其中
     * {@link GeneralTaskStatusStatistic#getTotal() 任务总数}用于查看用户总共接收了多少任务，
     * {@link GeneralTaskStatusStatistic#getCount() 指定状态任务数}用于查看处于此状态的任务有多少个，
     * {@link GeneralTaskStatusStatistic#getPercent() 指定状态任务数}为
     * {@link GeneralTaskStatusStatistic#getCount() 指定状态任务数}/{@link GeneralTaskStatusStatistic#getTotal() 任务总数}
     * 计算而出的结果
     *
     * @param userId 用户id
     * @param status 任务状态
     * @return 指定任务状态的统计信息
     */
    GeneralTaskStatusStatistic countStatusByUser(String userId, GeneralTaskStatus status);

    /**
     * 获取指定任务状态的统计信息，其中
     * {@link GeneralTaskStatusStatistic#getTotal() 任务总数}用于查看用户总共接收了多少任务，
     * {@link GeneralTaskStatusStatistic#getCount() 指定状态任务数}用于查看处于此状态的任务有多少个，
     * {@link GeneralTaskStatusStatistic#getPercent() 指定状态任务数}为
     * {@link GeneralTaskStatusStatistic#getCount() 指定状态任务数}/{@link GeneralTaskStatusStatistic#getTotal() 任务总数}
     * 计算而出的结果
     *
     * @param userId 用户id
     * @param status 任务状态列表
     * @return 指定任务状态的统计信息
     */
    GeneralTaskStatusStatistic countStatusByUser(String userId, List<GeneralTaskStatus> status);

    /**
     * 获取{@link GeneralTaskProcessingAndCompleteCount#getProcessingCount() 进行状态}和
     * {@link GeneralTaskProcessingAndCompleteCount#getCompleteCount() 完成状态}的任务数量
     *
     * @param userId 用户id
     * @return 进行状态和完成状态的任务数量
     */
    GeneralTaskProcessingAndCompleteCount CircuitTaskProcessingAndCompleteCount(String userId);

    /**
     * 统计各个状态的任务数量
     *
     * @param tenantId 租户ID
     * @return 数据
     */
    JSONObject statusCount(TenantId tenantId);

}
