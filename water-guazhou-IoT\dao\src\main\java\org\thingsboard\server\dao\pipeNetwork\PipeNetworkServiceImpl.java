package org.thingsboard.server.dao.pipeNetwork;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.Utils.DateUtils;
import org.thingsboard.server.common.data.exception.ThingsboardErrorCode;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.common.data.tsdb.DataPoint;
import org.thingsboard.server.dao.client.DeviceModelInfoClient;
import org.thingsboard.server.dao.client.StationFeignClient;
import org.thingsboard.server.dao.model.DTO.ExceptionByChangeRatioDTO;
import org.thingsboard.server.dao.model.DTO.ExceptionByFlowDTO;
import org.thingsboard.server.dao.model.DTO.ExceptionByReverseDTO;
import org.thingsboard.server.dao.model.DTO.ExceptionByZeroDTO;
import org.thingsboard.server.dao.model.VO.DeviceDataTableInfoVO;
import org.thingsboard.server.dao.model.VO.DynamicTableVO;
import org.thingsboard.server.dao.model.VO.LineChartDataVO;
import org.thingsboard.server.dao.model.sql.DeviceModelInfoEntity;
import org.thingsboard.server.dao.model.sql.StationAttrEntity;
import org.thingsboard.server.dao.model.sql.StationEntity;
import org.thingsboard.server.dao.obtain.BaseObtainDataService;
import org.thingsboard.server.dao.stationData.StationDataService;
import org.thingsboard.server.dao.util.DeviceTableInfoUtil;
import org.thingsboard.server.dao.util.StationDataUtil;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class PipeNetworkServiceImpl implements PipeNetworkService {

    @Autowired
    private StationFeignClient stationFeignClient;

    @Autowired
    private DeviceModelInfoClient deviceModelInfoClient;

    @Autowired
    private StationDataService stationDataService;

    @Autowired
    private BaseObtainDataService obtainDataService;

    @Override
    public DynamicTableVO getFlowReport(String stationId, Long start, Long end, String queryType, TenantId tenantId) throws ThingsboardException {
        // 报表类型
        String suffix = "";
        switch (queryType) {
            case "day":
                queryType = "1h";
                suffix = "时";
                break;
            case "month":
                queryType = "day";
                suffix = "日";
                break;
            case "year":
                queryType = "month";
                suffix = "月";
                break;
            /*case "year":
                queryType = "month";
                suffix = "年";
                break;*/
            default:
                throw new ThingsboardException("非法的报表类型, 仅支持 day、month、year!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }
        // 查询站点数据
        StationEntity station = stationFeignClient.get(stationId);
        if (station == null) {
            throw new ThingsboardException("要查询的站点不存在!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }

        List<StationAttrEntity> stationAttrList = stationFeignClient.getStationAllAttrList(stationId);

        List<String> attributes = new ArrayList<>();
        String unit = "";
        for (StationAttrEntity attrEntity : stationAttrList) {
            if (DataConstants.DeviceAttrType.TOTAL_FLOW.getValue().equals(attrEntity.getAttr())) {// 累计流量
                try {
                    String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(attrEntity.getDeviceId()));
                    attributes.add(deviceId + "." + attrEntity.getAttr());
                    unit = attrEntity.getUnit();
                } catch (Exception e) {
                    log.error("[查询站点数据异常] 查询站点ID: {}", station.getId());
                }
            }
        }
        if (attributes.size() == 0) {
            throw new ThingsboardException("查询的流量监测站未配置累计流量!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }

        // 查询出水累计流量
        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> stationDataMap = obtainDataService.getDeviceData(attributes, start, end, queryType, null, tenantId);

        // 按报表类型筛选数据
        List<String> allTimeKeyList = new ArrayList<>(stationDataMap.keySet());
        Map<String, List<String>> timeKeyMap = new LinkedHashMap<>();

        // 将数据时间分组
        for (String timeKey : allTimeKeyList) {
            String timeSubstring = "";
            if (queryType.equals("month")) {// 年报表
                timeSubstring = timeKey.substring(0, 4);// 取年份
            } else if (queryType.equals("day")) {// 月报表
                timeSubstring = timeKey.substring(5, 7);// 取月份
            } else {// 日报表
                timeSubstring = timeKey.substring(0, 10);// 取日期
            }
            List<String> timeKeyList = new ArrayList<>();
            if (timeKeyMap.containsKey(timeSubstring)) {
                timeKeyList = timeKeyMap.get(timeSubstring);
            }

            timeKeyList.add(timeKey);
            timeKeyMap.put(timeSubstring, timeKeyList);
        }

        // 数据分组
        Map<String, JSONObject> dataMap = new LinkedHashMap<>();
        for (Map.Entry<String, List<String>> entry : timeKeyMap.entrySet()) {
            String key = entry.getKey();
            List<String> timeKey = entry.getValue();
            for (String time : timeKey) {
                JSONObject data = new JSONObject();
                data.put("ts", StationDataUtil.shortenTimeKey(time, queryType));
                dataMap.put(StationDataUtil.shortenTimeKey(time, queryType), data);
            }
        }

        // 设置数据
        for (Map.Entry<String, List<String>> entry : timeKeyMap.entrySet()) {
            String key = entry.getKey();
            List<String> timeList = entry.getValue();

            for (String time : timeList) {
                JSONObject data = dataMap.get(StationDataUtil.shortenTimeKey(time, queryType));
                BigDecimal flow = null;
                LinkedHashMap<String, BigDecimal> dataValueMap = stationDataMap.get(time);
                if (dataValueMap != null) {
                    for (Map.Entry<String, BigDecimal> dataEntry : dataValueMap.entrySet()) {
                        if (dataEntry.getValue() != null) {
                            if (flow == null) {
                                flow = new BigDecimal("0");
                            }
                            flow = flow.add(dataEntry.getValue());
                        }
                    }
                }
                data.put(key, flow);

                BigDecimal max = flow;
                BigDecimal min = flow;
                BigDecimal total = new BigDecimal("0");
                if (data.getBigDecimal("max") == null || (max != null && data.getBigDecimal("max").doubleValue() < max.doubleValue())) {
                    data.put("max", max);
                }
                if (data.getBigDecimal("min") == null || (min != null && data.getBigDecimal("min").doubleValue() > min.doubleValue())) {
                    data.put("min", min);
                }
                if (data.getBigDecimal("total") != null) {
                    total = data.getBigDecimal("total");
                }
                if (flow != null) {
                    data.put("total", total.add(flow));
                }
            }

        }

        // 表格渲染结构
        List<DeviceDataTableInfoVO> deviceDataTableInfoList = new ArrayList<>();
        // 固定结构
        deviceDataTableInfoList.addAll(DeviceTableInfoUtil.stringArrayToDeviceTableInfoList("ts:数据时间"));
        // 动态结构
        for (String key : timeKeyMap.keySet()) {
            DeviceDataTableInfoVO dataTableInfoVO = new DeviceDataTableInfoVO();
            dataTableInfoVO.setColumnName(key + suffix);
            dataTableInfoVO.setColumnValue(key);
            dataTableInfoVO.setUnit(unit);
            deviceDataTableInfoList.add(dataTableInfoVO);
        }
        // 固定结构
        deviceDataTableInfoList.addAll(DeviceTableInfoUtil.stringArrayToDeviceTableInfoList("min:最小值", "max:最大值", "total:总计"));

        DynamicTableVO result = new DynamicTableVO();
        result.setTableInfo(deviceDataTableInfoList);
        ArrayList<JSONObject> dataList = new ArrayList<>(dataMap.values());
        for (JSONObject object : dataList) {
            object.put("ts", object.getString("ts") + suffix);
        }
        result.setTableDataList(dataList);

        return result;
    }

    @Override
    public DynamicTableVO getFlowDetailReport(String stationType, List<String> stationIdList, Long start, Long end, String queryType, TenantId tenantId) throws ThingsboardException {
        // 返回的列表数据预设
        Map<String, JSONObject> resultDataMap = new LinkedHashMap<>();
        // 报表类型
        String suffix = "";
        Calendar instance = Calendar.getInstance();
        switch (queryType) {
            case "day":
                suffix = "时";
                queryType = "1h";
                instance.setTime(new Date(start));
                Date time = instance.getTime();
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
                long tempTime = time.getTime();
                while (tempTime <= end) {
                    Date tempDate = new Date(tempTime);
                    JSONObject obj = new JSONObject();
                    obj.put("ts", dateFormat.format(tempDate));
                    resultDataMap.put(dateFormat.format(tempDate), obj);
                    tempTime = tempTime + (60 * 60 * 1000);
                }

                break;
            case "month":
                suffix = "日";
                queryType = "day";
                instance.setTime(new Date(start));
                int days = instance.getActualMaximum(Calendar.DAY_OF_MONTH);
                for (int i = 1; i <= days; i++) {
                    JSONObject obj = new JSONObject();
                    obj.put("ts", i + suffix);
                    resultDataMap.put(i + "", obj);
                }
                break;
            case "year":
                suffix = "月";
                queryType = "month";
                for (int i = 1; i <= 12; i++) {
                    JSONObject obj = new JSONObject();
                    obj.put("ts", i + suffix);
                    resultDataMap.put(i + "", obj);
                }
                break;
            default:
                throw new ThingsboardException("非法的报表类型, 仅支持 day、month、year!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }

        // 查询站点列表
        List<StationEntity> stationList = stationFeignClient.findByStationIdList(stationType, stationIdList);

        if (stationList == null || stationList.isEmpty()) {
            throw new ThingsboardException("要查询的站点列表不存在!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }

        Map<String, StationEntity> stationMap = stationList.stream().collect(Collectors.toMap(StationEntity::getId, station -> station));

        // 查询站点供水量数据
        Map<String, List<String>> attributesMap = new LinkedHashMap<>();
        String unit = "";
        for (StationEntity station : stationList) {
            List<String> attributes = new ArrayList<>();
            List<StationAttrEntity> stationAttrList = stationFeignClient.getStationAllAttrList(station.getId());

            for (StationAttrEntity stationAttrEntity : stationAttrList) {
                if (DataConstants.DeviceAttrType.TOTAL_FLOW.getValue().equals(stationAttrEntity.getAttr())) {// 累计流量
                    try {
                        String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(stationAttrEntity.getDeviceId()));
                        attributes.add(deviceId + "." + stationAttrEntity.getAttr());
                        unit = stationAttrEntity.getUnit();
                    } catch (Exception e) {
                        log.error("[查询站点数据异常] 查询站点ID: {}", station.getId());
                    }
                }
            }
            attributesMap.put(station.getId(), attributes);
        }


        // 固定行
        JSONObject maxObj = new JSONObject();
        maxObj.put("ts", "最大值");
        JSONObject maxTimeObj = new JSONObject();
        maxTimeObj.put("ts", "最大值时间");
        JSONObject minObj = new JSONObject();
        minObj.put("ts", "最小值");
        JSONObject minTimeObj = new JSONObject();
        minTimeObj.put("ts", "最小值时间");
        JSONObject avgObj = new JSONObject();
        avgObj.put("ts", "平均值");
        JSONObject totalObj = new JSONObject();
        totalObj.put("ts", "合计");

        // 分组查询数据
        for (Map.Entry<String, List<String>> attrEntry : attributesMap.entrySet()) {
            String key = attrEntry.getKey();
            List<String> attrList = attrEntry.getValue();
            if (attrList == null || attrList.isEmpty()) {
                continue;
            }

            // 查询数据
            LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> stationDataMap = obtainDataService.getDeviceData(attrList, start, end, queryType, null, tenantId);

            BigDecimal total = new BigDecimal("0");
            BigDecimal max = null;
            BigDecimal min = null;
            String maxFlag = "-";// 记录最大值的时间
            String minFlag = "-";// 记录最小值的时间
            int num = 0; // 次数

            // 合计Map
            Map<String, BigDecimal> totalMap = new LinkedHashMap<>();

            // 设置站点供水量数据
            for (Map.Entry<String, LinkedHashMap<String, BigDecimal>> entry : stationDataMap.entrySet()) {
                String timeKey = entry.getKey();
                LinkedHashMap<String, BigDecimal> dataMap = entry.getValue();
                BigDecimal flow = new BigDecimal("0");
                for (Map.Entry<String, BigDecimal> dataEntry : dataMap.entrySet()) {
                    if (dataEntry.getValue() != null) {
                        flow = flow.add(dataEntry.getValue());
                    }
                }

                total = total.add(flow);
                num++;
                if (max == null || max.doubleValue() < flow.doubleValue()) {
                    max = flow;
                    maxFlag = StationDataUtil.shortenTimeKey(timeKey, queryType);
                }
                if (min == null || min.doubleValue() > flow.doubleValue()) {
                    min = flow;
                    minFlag = StationDataUtil.shortenTimeKey(timeKey, queryType);
                }


                if ("1h".equals(queryType)) {
                    String resultMapKey = timeKey + ":00";
                    JSONObject data = resultDataMap.get(resultMapKey);
                    data.put(key, flow);

                    resultDataMap.put(resultMapKey, data);
                    // 设置合计map
                    BigDecimal nowTotal = new BigDecimal("0");
                    if (totalMap.containsKey(resultMapKey)) {
                        nowTotal = totalMap.get(resultMapKey);
                    }
                    nowTotal = nowTotal.add(flow);
                    totalMap.put(resultMapKey, nowTotal);

                } else {
                    int resultMapKey = Integer.parseInt(StationDataUtil.shortenTimeKey(timeKey, queryType));
                    JSONObject data = resultDataMap.get(resultMapKey);
                    data.put(key, flow);

                    resultDataMap.put(resultMapKey + "", data);
                    // 设置合计map
                    BigDecimal nowTotal = new BigDecimal("0");
                    if (totalMap.containsKey(resultMapKey)) {
                        nowTotal = totalMap.get(resultMapKey);
                    }
                    nowTotal = nowTotal.add(flow);
                    totalMap.put(resultMapKey + "", nowTotal);
                }


            }

            // 设置最大最小值平均值等数据
            if (max != null) {
                maxObj.put(key, max);
                maxTimeObj.put(key, Integer.parseInt(maxFlag) + suffix);
            }
            if (min != null) {
                minObj.put(key, min);
                minTimeObj.put(key, minFlag + suffix);
            }
            // 平均值
            if (num != 0) {
                avgObj.put(key, total.divide(BigDecimal.valueOf(num), 2, BigDecimal.ROUND_DOWN));
            }
            // 合计
            totalObj.put(key, total);
        }

        // 设置固定行数据
        List<JSONObject> dataList = new ArrayList<>(resultDataMap.values());
        dataList.add(maxObj);
        dataList.add(maxTimeObj);
        dataList.add(minObj);
        dataList.add(minTimeObj);
        dataList.add(avgObj);
        dataList.add(totalObj);

        // 表格渲染结构
        List<DeviceDataTableInfoVO> deviceDataTableInfoList = new ArrayList<>();
        // 固定结构
        deviceDataTableInfoList.addAll(DeviceTableInfoUtil.stringArrayToDeviceTableInfoList("ts:数据时间", "total:合计"));

        // 动态结构
        for (StationEntity station : stationList) {
            DeviceDataTableInfoVO tableInfoVO = new DeviceDataTableInfoVO();
            tableInfoVO.setUnit(unit);
            tableInfoVO.setColumnName(station.getName());
            tableInfoVO.setColumnValue(station.getId());
            deviceDataTableInfoList.add(tableInfoVO);
        }

        DynamicTableVO result = new DynamicTableVO();
        result.setTableDataList(dataList);
        result.setTableInfo(deviceDataTableInfoList);

        return result;
    }

    @Override
    public Object getMeterConfigChart(String stationType, String stationId, Long start, Long end, TenantId tenantId) throws ThingsboardException {
        // 查询站点信息
        StationEntity station = stationFeignClient.get(stationId);
        if (station == null) {
            throw new ThingsboardException("查询的站点不存在!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

        // 初始化曲线数据map
        Map<String, List<LineChartDataVO>> dataMap = new LinkedHashMap<>();
        dataMap.put(dateFormat.format(new Date(start)), new ArrayList<>());
        long temp = start + (24 * 60 * 60 * 1000);
        while (temp < end) {
            List<LineChartDataVO> dataList = new ArrayList<>();
            dataMap.put(dateFormat.format(new Date(temp)), dataList);

            temp = temp + (24 * 60 * 60 * 1000);
        }

        // 查询数据
        List<String> attributes = new ArrayList<>();
        List<StationAttrEntity> stationAttrList = stationFeignClient.getStationAllAttrList(station.getId());

        for (StationAttrEntity stationAttrEntity : stationAttrList) {
            if (DataConstants.DeviceAttrType.INSTANTANEOUS_FLOW.getValue().equals(stationAttrEntity.getAttr())) {// 瞬时流量
                try {
                    String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(stationAttrEntity.getDeviceId()));
                    attributes.add(deviceId + "." + stationAttrEntity.getAttr());
                } catch (Exception e) {
                    log.error("[查询站点数据异常] 查询站点ID: {}", station.getId());
                }
            }
        }
        if (attributes.size() == 0) {
            throw new ThingsboardException("查询的流量监测站未配置累计流量!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }
        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> stationDataMap =
                obtainDataService.getDeviceData(attributes, start, end, DateUtils.HOUR, null, tenantId);

        // 数据分组
        if (stationDataMap != null) {
            for (Map.Entry<String, LinkedHashMap<String, BigDecimal>> entry : stationDataMap.entrySet()) {
                String key = entry.getKey();
                LinkedHashMap<String, BigDecimal> data = entry.getValue();

                String dateKey = key.split(" ")[0];
                List<LineChartDataVO> dataList = dataMap.get(dateKey);

                BigDecimal value = null;
                if (data != null) {
                    for (Map.Entry<String, BigDecimal> dataEntry : data.entrySet()) {
                        if (dataEntry.getValue() != null) {
                            if (value == null) {
                                value = new BigDecimal("0");
                            }
                            value = value.add(dataEntry.getValue());
                        }
                    }
                }

                LineChartDataVO dataVO = new LineChartDataVO();
                dataVO.setTs(key);
                dataVO.setValue(value);

                dataList.add(dataVO);
                dataMap.put(dateKey, dataList);
            }
        }

        JSONObject resultData = new JSONObject();
        resultData.put("stationInfo", station);
        resultData.put("lineData", dataMap);

        return resultData;
    }

    @Override
    public Object getFlowPeak(String stationType, String stationId, Long start, Long end, TenantId tenantId) throws ThingsboardException {
        // 查询站点信息
        StationEntity station = stationFeignClient.get(stationId);
        if (station == null) {
            throw new ThingsboardException("查询的站点不存在!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

        // 初始化曲线数据map
        Map<String, List<LineChartDataVO>> dataMap = new LinkedHashMap<>();
        dataMap.put(dateFormat.format(new Date(start)), new ArrayList<>());
        long temp = start + (24 * 60 * 60 * 1000);
        while (temp < end) {
            List<LineChartDataVO> dataList = new ArrayList<>();
            dataMap.put(dateFormat.format(new Date(temp)), dataList);

            temp = temp + (24 * 60 * 60 * 1000);
        }

        // 查询数据
        List<String> attributes = new ArrayList<>();
        List<StationAttrEntity> stationAttrList = stationFeignClient.getStationAllAttrList(station.getId());

        for (StationAttrEntity stationAttrEntity : stationAttrList) {
            if (DataConstants.DeviceAttrType.INSTANTANEOUS_FLOW.getValue().equals(stationAttrEntity.getAttr())) {// 瞬时流量
                try {
                    String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(stationAttrEntity.getDeviceId()));
                    attributes.add(deviceId + "." + stationAttrEntity.getAttr());
                } catch (Exception e) {
                    log.error("[查询站点数据异常] 查询站点ID: {}", station.getId());
                }
            }
        }
        if (attributes.size() == 0) {
            throw new ThingsboardException("查询的流量监测站未配置累计流量!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }
        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> stationDataMap =
                obtainDataService.getDeviceData(attributes, start, end, DateUtils.HOUR, null, tenantId);

        // 数据分组
        if (stationDataMap != null) {
            for (Map.Entry<String, LinkedHashMap<String, BigDecimal>> entry : stationDataMap.entrySet()) {
                String key = entry.getKey();
                LinkedHashMap<String, BigDecimal> data = entry.getValue();

                String dateKey = key.split(" ")[0];
                List<LineChartDataVO> dataList = dataMap.get(dateKey);

                BigDecimal value = null;
                if (data != null) {
                    for (Map.Entry<String, BigDecimal> dataEntry : data.entrySet()) {
                        if (dataEntry.getValue() != null) {
                            if (value == null) {
                                value = new BigDecimal("0");
                            }
                            value = value.add(dataEntry.getValue());
                        }
                    }
                }

                LineChartDataVO dataVO = new LineChartDataVO();
                dataVO.setTs(key);
                dataVO.setValue(value);

                dataList.add(dataVO);
                dataMap.put(dateKey, dataList);
            }
        }

        // 数据统计
        List<JSONObject> resultList = new ArrayList<>();
        for (Map.Entry<String, List<LineChartDataVO>> entry : dataMap.entrySet()) {
            String key = entry.getKey();
            List<LineChartDataVO> dataList = entry.getValue();

            // 统计
            BigDecimal max = null;
            BigDecimal min = null;
            BigDecimal avg = null;
            BigDecimal total = new BigDecimal("0");
            int num = 0;
            String maxTs = "";
            String minTs = "";
            for (LineChartDataVO dataVO : dataList) {
                BigDecimal value = dataVO.getValue();
                if (value == null) {
                    continue;
                }
                if (max == null || value.doubleValue() > max.doubleValue()) {
                    max = value;
                    maxTs = dataVO.getTs();
                }
                if (min == null || value.doubleValue() < min.doubleValue()) {
                    min = value;
                    minTs = dataVO.getTs();
                }
                total = total.add(value);
                num++;
            }

            // 计算平均值
            if (num != 0) {
                avg = total.divide(new BigDecimal(num), 2, BigDecimal.ROUND_DOWN);
            }

            JSONObject data = new JSONObject();
            data.put("max", max);
            data.put("maxTs", maxTs);
            data.put("min", min);
            data.put("minTs", minTs);
            data.put("avg", avg);
            data.put("ts", key);

            resultList.add(data);
        }

        return resultList;
    }

    @Override
    public Object getFlowPeriod(String stationType, String stationId, Long start, Long end, String queryType, TenantId tenantId) throws ThingsboardException {
        // 查询站点信息
        StationEntity station = stationFeignClient.get(stationId);
        if (station == null) {
            throw new ThingsboardException("查询的站点不存在!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }

        // 初始化曲线数据map
        Map<String, List<LineChartDataVO>> dataMap = new LinkedHashMap<>();
        SimpleDateFormat dateFormat = null;
        String exeQueryType = "";
        switch (queryType) {
            case "day":
                dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                dataMap.put(dateFormat.format(new Date(start)), new ArrayList<>());
                long temp = start + (24 * 60 * 60 * 1000);
                while (temp < end) {
                    List<LineChartDataVO> dataList = new ArrayList<>();
                    dataMap.put(dateFormat.format(new Date(temp)), dataList);

                    temp = temp + (24 * 60 * 60 * 1000);
                }
                exeQueryType = DateUtils.HOUR;
                break;
            case "month":
                dateFormat = new SimpleDateFormat("yyyy-MM");
                Calendar instance = Calendar.getInstance();
                instance.setTime(new Date(start));

                while (instance.getTimeInMillis() < end) {
                    List<LineChartDataVO> dataList = new ArrayList<>();
                    dataMap.put(dateFormat.format(instance.getTime()), dataList);

                    instance.set(Calendar.MONTH, instance.get(Calendar.MONTH) + 1);
                }
                exeQueryType = DateUtils.DAY;
                break;
            default:
                throw new ThingsboardException("非法的查询类型, 仅支持日分时或月分日!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }

        // 查询数据
        List<String> attributes = new ArrayList<>();
        List<StationAttrEntity> stationAttrList = stationFeignClient.getStationAllAttrList(station.getId());

        for (StationAttrEntity stationAttrEntity : stationAttrList) {
            if (DataConstants.DeviceAttrType.TOTAL_FLOW.getValue().equals(stationAttrEntity.getAttr())) {// 累计流量
                try {
                    String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(stationAttrEntity.getDeviceId()));
                    attributes.add(deviceId + "." + stationAttrEntity.getAttr());
                } catch (Exception e) {
                    log.error("[查询站点数据异常] 查询站点ID: {}", station.getId());
                }
            }
        }
        if (attributes.size() == 0) {
            throw new ThingsboardException("查询的流量监测站未配置累计流量!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }
        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> stationDataMap =
                obtainDataService.getDeviceData(attributes, start, end, exeQueryType, null, tenantId);

        // 数据分组
        if (stationDataMap != null) {
            for (Map.Entry<String, LinkedHashMap<String, BigDecimal>> entry : stationDataMap.entrySet()) {
                String key = entry.getKey();
                LinkedHashMap<String, BigDecimal> data = entry.getValue();

                String dateKey = "";
                switch (queryType) {
                    case "day":
                        dateKey = key.split(" ")[0];
                        break;
                    case "month":
                        dateKey = key.substring(0, 7);
                        break;
                }
                List<LineChartDataVO> dataList = dataMap.get(dateKey);

                BigDecimal value = null;
                if (data != null) {
                    for (Map.Entry<String, BigDecimal> dataEntry : data.entrySet()) {
                        if (dataEntry.getValue() != null) {
                            if (value == null) {
                                value = new BigDecimal("0");
                            }
                            value = value.add(dataEntry.getValue());
                        }
                    }
                }

                LineChartDataVO dataVO = new LineChartDataVO();
                dataVO.setTs(key);
                dataVO.setValue(value);

                dataList.add(dataVO);
                dataMap.put(dateKey, dataList);
            }
        }

        return dataMap;
    }

    @Override
    public Object getFlowRatio(String stationType, String stationId, Long start, Long end, String type, TenantId tenantId, String attr) throws ThingsboardException {
        // 查询站点信息
        StationEntity station = stationFeignClient.get(stationId);
        if (station == null) {
            throw new ThingsboardException("查询的站点不存在!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }

        // 初始化时间
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM");
        Calendar instance = Calendar.getInstance();
        instance.setTime(new Date(start));

        Long lastStart = null;
        Long lastEnd = null;
        switch (type) {
            case "1":// 同比
                instance.set(Calendar.YEAR, instance.get(Calendar.YEAR) - 1);
                lastStart = instance.getTimeInMillis();
                instance.setTime(new Date(end));
                instance.set(Calendar.YEAR, instance.get(Calendar.YEAR) - 1);
//                instance.set(Calendar.DAY_OF_MONTH, instance.getActualMaximum(Calendar.DAY_OF_MONTH));
                lastEnd = instance.getTimeInMillis();
                break;
            case "2":// 环比
                instance.set(Calendar.MONTH, instance.get(Calendar.MONTH) - 1);
                lastStart = instance.getTimeInMillis();
                instance.setTime(new Date(end));
                instance.set(Calendar.MONTH, instance.get(Calendar.MONTH) - 1);
//                instance.set(Calendar.DAY_OF_MONTH, instance.getActualMaximum(Calendar.DAY_OF_MONTH));
                lastEnd = instance.getTimeInMillis();
                break;
            default:
                throw new ThingsboardException("非法的查询类型, 仅支持日分时或月分日!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }

        // 查询数据
        List<String> attributes = new ArrayList<>();
        List<StationAttrEntity> stationAttrList = stationFeignClient.getStationAllAttrList(station.getId());

        for (StationAttrEntity stationAttrEntity : stationAttrList) {
            if (StringUtils.isBlank(attr)) {
                attr = DataConstants.DeviceAttrType.TOTAL_FLOW.getValue();
            }
            if (attr.equals(stationAttrEntity.getAttr())) {// 累计流量
                try {
                    String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(stationAttrEntity.getDeviceId()));
                    attributes.add(deviceId + "." + stationAttrEntity.getAttr());
                } catch (Exception e) {
                    log.error("[查询站点数据异常] 查询站点ID: {}", station.getId());
                }
            }
        }
        if (attributes.size() == 0) {
            throw new ThingsboardException("查询的流量监测站未配置累计流量!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }
        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> thisStationDataMap =
                obtainDataService.getDeviceData(attributes, start, end, DateUtils.DAY, null, tenantId);

        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> lastStationDataMap =
                obtainDataService.getDeviceData(attributes, lastStart, lastEnd, DateUtils.DAY, null, tenantId);

        // 数据处理
        List<LineChartDataVO> thisDataList = new ArrayList<>();
        if (thisStationDataMap != null) {
            for (Map.Entry<String, LinkedHashMap<String, BigDecimal>> entry : thisStationDataMap.entrySet()) {
                String key = entry.getKey();
                LinkedHashMap<String, BigDecimal> dataMap = entry.getValue();
                BigDecimal value = null;
                if (dataMap != null) {
                    for (Map.Entry<String, BigDecimal> dataEntry : dataMap.entrySet()) {
                        if (dataEntry.getValue() != null) {
                            if (value == null) {
                                value = new BigDecimal("0");
                            }
                            value = value.add(dataEntry.getValue());
                        }
                    }
                }
                LineChartDataVO dataVO = new LineChartDataVO();
                dataVO.setTs(key);
                dataVO.setValue(value);
                thisDataList.add(dataVO);
            }
        }
        List<LineChartDataVO> lastDataList = new ArrayList<>();
        if (lastStationDataMap != null) {
            for (Map.Entry<String, LinkedHashMap<String, BigDecimal>> entry : lastStationDataMap.entrySet()) {
                String key = entry.getKey();
                LinkedHashMap<String, BigDecimal> dataMap = entry.getValue();
                BigDecimal value = null;
                if (dataMap != null) {
                    for (Map.Entry<String, BigDecimal> dataEntry : dataMap.entrySet()) {
                        if (dataEntry.getValue() != null) {
                            if (value == null) {
                                value = new BigDecimal("0");
                            }
                            value = value.add(dataEntry.getValue());
                        }
                    }
                }
                LineChartDataVO dataVO = new LineChartDataVO();
                dataVO.setTs(key);
                dataVO.setValue(value);
                lastDataList.add(dataVO);
            }
        }

        JSONObject result = new JSONObject();
        result.put(dateFormat.format(new Date(start)), thisDataList);
        result.put(dateFormat.format(new Date(lastStart)), lastDataList);

        return result;
    }

    @Override
    public Object getPressurePeriod(String stationType, String stationId, Long start, Long end, String queryType, TenantId tenantId) throws ThingsboardException {
        // 查询站点信息
        StationEntity station = stationFeignClient.get(stationId);
        if (station == null) {
            throw new ThingsboardException("查询的站点不存在!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }

        // 初始化曲线数据map
        Map<String, List<LineChartDataVO>> dataMap = new LinkedHashMap<>();
        SimpleDateFormat dateFormat = null;
        String exeQueryType = "";
        switch (queryType) {
            case "day":
                dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                dataMap.put(dateFormat.format(new Date(start)), new ArrayList<>());
                long temp = start + (24 * 60 * 60 * 1000);
                while (temp < end) {
                    List<LineChartDataVO> dataList = new ArrayList<>();
                    dataMap.put(dateFormat.format(new Date(temp)), dataList);

                    temp = temp + (24 * 60 * 60 * 1000);
                }
                exeQueryType = DateUtils.HOUR;
                break;
            case "month":
                dateFormat = new SimpleDateFormat("yyyy-MM");
                Calendar instance = Calendar.getInstance();
                instance.setTime(new Date(start));

                while (instance.getTimeInMillis() < end) {
                    List<LineChartDataVO> dataList = new ArrayList<>();
                    dataMap.put(dateFormat.format(instance.getTime()), dataList);

                    instance.set(Calendar.MONTH, instance.get(Calendar.MONTH) + 1);
                }
                exeQueryType = DateUtils.DAY;
                break;
            default:
                throw new ThingsboardException("非法的查询类型, 仅支持日分时或月分日!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }

        // 查询数据
        List<String> attributes = new ArrayList<>();
        List<StationAttrEntity> stationAttrList = stationFeignClient.getStationAllAttrList(station.getId());

        for (StationAttrEntity stationAttrEntity : stationAttrList) {
            if (DataConstants.DeviceAttrType.PRESSURE.getValue().equals(stationAttrEntity.getAttr())) {// 压力
                try {
                    String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(stationAttrEntity.getDeviceId()));
                    attributes.add(deviceId + "." + stationAttrEntity.getAttr());
                } catch (Exception e) {
                    log.error("[查询站点数据异常] 查询站点ID: {}", station.getId());
                }
            }
        }
        if (attributes.size() == 0) {
            throw new ThingsboardException("查询的压力监测站未配置压力点位!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }
        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> stationDataMap =
                obtainDataService.getDeviceData(attributes, start, end, exeQueryType, null, tenantId);

        // 数据分组
        if (stationDataMap != null) {
            for (Map.Entry<String, LinkedHashMap<String, BigDecimal>> entry : stationDataMap.entrySet()) {
                String key = entry.getKey();
                LinkedHashMap<String, BigDecimal> data = entry.getValue();

                String dateKey = "";
                switch (queryType) {
                    case "day":
                        dateKey = key.split(" ")[0];
                        break;
                    case "month":
                        dateKey = key.substring(0, 7);
                        break;
                }
                List<LineChartDataVO> dataList = dataMap.get(dateKey);

                BigDecimal value = null;
                if (data != null) {
                    for (Map.Entry<String, BigDecimal> dataEntry : data.entrySet()) {
                        if (dataEntry.getValue() != null) {
                            if (value == null) {
                                value = new BigDecimal("0");
                            }
                            value = value.add(dataEntry.getValue());
                        }
                    }
                }

                LineChartDataVO dataVO = new LineChartDataVO();
                dataVO.setTs(key);
                dataVO.setValue(value);

                dataList.add(dataVO);
                dataMap.put(dateKey, dataList);
            }
        }

        return dataMap;
    }

    @Override
    public List<ExceptionByChangeRatioDTO> getExceptionChangeRatio(String stationId, String type, String queryType, Date startTime, Date endTime, Integer range, String attr, TenantId tenantId) throws ThingsboardException {
        // 查询站点信息
        StationEntity station = stationFeignClient.get(stationId);
        if (station == null) {
            throw new ThingsboardException("查询的站点不存在!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }

        // 查询数据
        List<String> attributes = new ArrayList<>();
        List<StationAttrEntity> stationAttrList = stationFeignClient.getStationAllAttrList(station.getId());

        for (StationAttrEntity stationAttrEntity : stationAttrList) {
            if (StringUtils.isBlank(attr)) {
                attr = DataConstants.DeviceAttrType.INSTANTANEOUS_FLOW.getValue();
            }
            if (attr.equals(stationAttrEntity.getAttr())) {// 瞬时流量
                try {
                    String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(stationAttrEntity.getDeviceId()));
                    attributes.add(deviceId + "." + stationAttrEntity.getAttr());
                    break;
                } catch (Exception e) {
                    log.error("[查询站点数据异常] 查询站点ID: {}", station.getId());
                }
            }
        }
        if (attributes.size() == 0) {
            throw new ThingsboardException("查询的流量监测站未配置瞬时流量!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }
        // 结束时间为当天23:59:59
        endTime = new Date(endTime.getTime() + (24 * 60 * 60 * 1000) - 1000);
        Date tempStartTime = new Date(startTime.getTime());
        // 判断分析类型
        if ("1".equals(type)) {// 连续分析
            // 对比上一次
        }
        if ("2".equals(type)) {// 同比分析
            // 同比需要往前提一天数据
            startTime = new Date(startTime.getTime() - (24 * 60 * 60 * 1000));
        }
        if ("3".equals(type)) {// 环比分析
            // 对比上月
            startTime = new Date(startTime.getTime() - (31 * 24 * 60 * 60 * 1000L));
        }
        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> dataMap =
                obtainDataService.getDeviceData(attributes, startTime.getTime(), endTime.getTime(), queryType, null, tenantId);

        List<ExceptionByChangeRatioDTO> resultList = new ArrayList<>();

        Map<String, BigDecimal> newDateMap = new LinkedHashMap<>();
        for (Map.Entry<String, LinkedHashMap<String, BigDecimal>> entry : dataMap.entrySet()) {
            String key = entry.getKey();
            BigDecimal value = null;
            LinkedHashMap<String, BigDecimal> valueMap = entry.getValue();
            if (valueMap != null) {
                for (Map.Entry<String, BigDecimal> dataEntry : valueMap.entrySet()) {
                    BigDecimal decimal = dataEntry.getValue();
                    if (decimal != null) {
                        value = decimal;
                    }
                }
            }
            if (value == null) {
                continue;
            }
            newDateMap.put(key, value);
        }

        // 数据分析
        // 连续分析、环比分析
        if ("1".equals(type)) {
            for (Map.Entry<String, BigDecimal> entry : newDateMap.entrySet()) {
                String key = entry.getKey();
                BigDecimal value = entry.getValue();

                ExceptionByChangeRatioDTO ratioDTO = new ExceptionByChangeRatioDTO();
                ratioDTO.setStationId(station.getId());
                ratioDTO.setStationName(station.getName());
                ratioDTO.setUnit(stationAttrList.get(0).getUnit());
                ratioDTO.setValueDate(key);
                ratioDTO.setValue(value);

                resultList.add(ratioDTO);
            }

            for (int i = 1; i < newDateMap.size(); i++) { // 第一个数据无对比项
                ExceptionByChangeRatioDTO thisData = resultList.get(i);
                ExceptionByChangeRatioDTO compareData = resultList.get(i - 1);

                thisData.setCompareValue(compareData.getValue());
                thisData.setCompareValueDate(compareData.getValueDate());
                if (thisData.getValue() != null && thisData.getCompareValue() != null) {// 计算变化率
                    BigDecimal divideValue = thisData.getCompareValue();
                    if (divideValue.doubleValue() == 0) {
                        divideValue = BigDecimal.valueOf(1);
                    }
                    BigDecimal ratio = thisData.getCompareValue().subtract(thisData.getValue()).divide(divideValue, 4, BigDecimal.ROUND_HALF_EVEN).abs();
                    // 计算百分比
                    thisData.setRatio(ratio.multiply(BigDecimal.valueOf(100)));
                }
            }
        }

        // 同比分析
        if ("2".equals(type) || "3".equals(type)) {
            boolean flag = false;

            String formatStr = "yyyy-MM-dd HH:mm";
            if ("1h".equals(queryType)) {
                formatStr = "yyyy-MM-dd HH";
            }
            for (Map.Entry<String, BigDecimal> entry : newDateMap.entrySet()) {
                String key = entry.getKey();
                BigDecimal value = entry.getValue();
                if (key.equals(DateUtils.date2Str(tempStartTime, formatStr))) {
                    flag = true;
                }

                if (flag) {
                    ExceptionByChangeRatioDTO ratioDTO = new ExceptionByChangeRatioDTO();
                    ratioDTO.setStationId(station.getId());
                    ratioDTO.setStationName(station.getName());
                    ratioDTO.setUnit(stationAttrList.get(0).getUnit());
                    ratioDTO.setValueDate(key);
                    ratioDTO.setValue(value);

                    resultList.add(ratioDTO);
                }
            }

            SimpleDateFormat dateFormat = new SimpleDateFormat(formatStr);
            // 数据分析
            for (ExceptionByChangeRatioDTO ratioDTO : resultList) {
                String valueDate = ratioDTO.getValueDate();
                Date parse = null;
                try {
                    parse = dateFormat.parse(valueDate);
                } catch (ParseException e) {
                    continue;
                }
                String compareDate = dateFormat.format(new Date(parse.getTime() - (24 * 60 * 60 * 1000)));
                if ("3".equals(type)) {
                    // 上个月的同一天
                    compareDate = LocalDateTime.ofInstant(Instant.ofEpochMilli(parse.getTime()), ZoneId.systemDefault()).minusMonths(1L).format(DateTimeFormatter.ofPattern(formatStr));
                }

                BigDecimal compareValue = newDateMap.get(compareDate);
                ratioDTO.setCompareValue(compareValue);
                ratioDTO.setCompareValueDate(compareDate);
                if (ratioDTO.getValue() != null && ratioDTO.getCompareValue() != null) {// 计算变化率
                    BigDecimal divideValue = ratioDTO.getCompareValue();
                    if (divideValue.doubleValue() == 0) {
                        divideValue = BigDecimal.valueOf(1);
                    }
                    BigDecimal ratio = ratioDTO.getCompareValue().subtract(ratioDTO.getValue()).divide(divideValue, 4, BigDecimal.ROUND_HALF_EVEN).abs();
                    // 计算百分比
                    ratioDTO.setRatio(ratio.multiply(BigDecimal.valueOf(100)));
                }
            }
        }

        // 按range过滤数据
        if (range != null) {
            resultList = resultList.stream()
                    .filter(data -> data.getRatio() != null && data.getRatio().compareTo(BigDecimal.valueOf(range)) > 0)
                    .collect(Collectors.toList());
        }
        Collections.reverse(resultList);

        return resultList;
    }

    @Override
    public List<ExceptionByReverseDTO> getExceptionReverseData(String stationId, Date startTime, Date endTime, Double range, TenantId tenantId) throws ThingsboardException {
        // 查询站点信息
        StationEntity station = stationFeignClient.get(stationId);
        if (station == null) {
            throw new ThingsboardException("查询的站点不存在!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }

        // 查询数据
        List<String> attributes = new ArrayList<>();
        List<StationAttrEntity> stationAttrList = stationFeignClient.getStationAllAttrList(station.getId());

        for (StationAttrEntity stationAttrEntity : stationAttrList) {
            if (DataConstants.DeviceAttrType.INSTANTANEOUS_FLOW.getValue().equals(stationAttrEntity.getAttr())) {// 瞬时流量
                try {
                    String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(stationAttrEntity.getDeviceId()));
                    attributes.add(deviceId + "." + stationAttrEntity.getAttr());
                    break;
                } catch (Exception e) {
                    log.error("[查询站点数据异常] 查询站点ID: {}", station.getId());
                }
            }
        }
        if (attributes.size() == 0) {
            throw new ThingsboardException("查询的流量监测站未配置瞬时流量!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }
        // 结束时间为当天23:59:59
        endTime = new Date(endTime.getTime() + (24 * 60 * 60 * 1000) - 1000);
        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> dataMap =
                obtainDataService.getDeviceOriginalData(attributes, startTime.getTime(), endTime.getTime(), null, tenantId);

        List<ExceptionByReverseDTO> resultList = new ArrayList<>();
        for (Map.Entry<String, LinkedHashMap<String, BigDecimal>> entry : dataMap.entrySet()) {
            String key = entry.getKey();

            ExceptionByReverseDTO reverseDTO = new ExceptionByReverseDTO();
            reverseDTO.setStationId(stationId);
            reverseDTO.setStationName(station.getName());
            reverseDTO.setUnit(stationAttrList.get(0).getUnit());
            reverseDTO.setValueDate(key);

            LinkedHashMap<String, BigDecimal> valueMap = entry.getValue();
            if (valueMap != null) {
                for (Map.Entry<String, BigDecimal> dataEntry : valueMap.entrySet()) {
                    BigDecimal decimal = dataEntry.getValue();
                    if (decimal != null) {
                        reverseDTO.setValue(decimal);
                    }
                }
            }
            resultList.add(reverseDTO);
        }

        // 按range筛选
        if (range != null) {
            BigDecimal rangeValue = BigDecimal.valueOf(range).negate();
            resultList = resultList.stream()
                    .filter(data -> data.getValue() != null && data.getValue().compareTo(rangeValue) < 0)
                    .collect(Collectors.toList());
        } else {
            resultList = resultList.stream()
                    .filter(data -> data.getValue() != null && data.getValue().compareTo(BigDecimal.ZERO) < 0)
                    .collect(Collectors.toList());
        }
        Collections.reverse(resultList);
        // 将负数转换为正数返回前端显示
        resultList = resultList.stream().peek(data -> data.setValue(data.getValue().negate())).collect(Collectors.toList());

        return resultList;
    }

    @Override
    public List<ExceptionByZeroDTO> getExceptionZeroData(String stationId, Date startTime, Date endTime, Integer range, String attr, TenantId tenantId) throws ThingsboardException {
        // 查询站点信息
        StationEntity station = stationFeignClient.get(stationId);
        if (station == null) {
            throw new ThingsboardException("查询的站点不存在!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }

        // 查询数据
        List<String> attributes = new ArrayList<>();
        List<StationAttrEntity> stationAttrList = stationFeignClient.getStationAllAttrList(station.getId());

        for (StationAttrEntity stationAttrEntity : stationAttrList) {
            if (StringUtils.isBlank(attr)) {
                attr = DataConstants.DeviceAttrType.INSTANTANEOUS_FLOW.getValue();
            }
            if (attr.equals(stationAttrEntity.getAttr())) {// 瞬时流量
                try {
                    String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(stationAttrEntity.getDeviceId()));
                    attributes.add(deviceId + "." + stationAttrEntity.getAttr());
                    break;
                } catch (Exception e) {
                    log.error("[查询站点数据异常] 查询站点ID: {}", station.getId());
                }
            }
        }
        if (attributes.size() == 0) {
            throw new ThingsboardException("查询的流量监测站未配置瞬时流量!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }
        // 结束时间为当天23:59:59
        endTime = new Date(endTime.getTime() + (24 * 60 * 60 * 1000) - 1000);
        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> dataMap =
                obtainDataService.getDeviceOriginalData(attributes, startTime.getTime(), endTime.getTime(), null, tenantId);

        Map<String, BigDecimal> newDateMap = new LinkedHashMap<>();
        for (Map.Entry<String, LinkedHashMap<String, BigDecimal>> entry : dataMap.entrySet()) {
            String key = entry.getKey();
            BigDecimal value = null;
            LinkedHashMap<String, BigDecimal> valueMap = entry.getValue();
            if (valueMap != null) {
                for (Map.Entry<String, BigDecimal> dataEntry : valueMap.entrySet()) {
                    BigDecimal decimal = dataEntry.getValue();
                    if (decimal != null) {
                        value = decimal;
                    }
                }
            }
            newDateMap.put(key, value);
        }

        // 按照日期分组
        Map<String, LinkedHashMap<String, BigDecimal>> dateGroupMap = new LinkedHashMap<>();
        for (Map.Entry<String, BigDecimal> entry : newDateMap.entrySet()) {
            String key = entry.getKey();
            BigDecimal value = entry.getValue();

            String dayStr = key.substring(0, 10);
            LinkedHashMap<String, BigDecimal> dayMap = dateGroupMap.get(dayStr);
            if (dayMap == null) {
                dayMap = new LinkedHashMap<>();
            }
            dayMap.put(key, value);

            dateGroupMap.put(dayStr, dayMap);
        }

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        // 按日期计算零流量时间
        List<ExceptionByZeroDTO> resultList = new ArrayList<>();
        for (Map.Entry<String, LinkedHashMap<String, BigDecimal>> entry : dateGroupMap.entrySet()) {
            String key = entry.getKey();
            LinkedHashMap<String, BigDecimal> valueMap = entry.getValue();
            valueMap = valueMap.entrySet().stream().sorted(Comparator.comparing(Map.Entry::getKey)).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (e1, e2) -> e2, LinkedHashMap::new));
            BigDecimal value = BigDecimal.ZERO;
            boolean isZero = false;
            if (valueMap != null && valueMap.size() > 0) {
                long lastTime = -1;
                isZero = false;
                for (Map.Entry<String, BigDecimal> valueEntry : valueMap.entrySet()) {
                    String dateStr = valueEntry.getKey();
                    BigDecimal data = valueEntry.getValue();
                    if (data == null || data.doubleValue() == 0) {// 为0
                        if (!isZero) {
                            // 记录本次为0的时间
                            try {
                                lastTime = dateFormat.parse(dateStr).getTime();
                                isZero = true;
                            } catch (ParseException e) {
                            }
                        }
                    } else {
                        // 不为0
                        if (isZero) {// 已记录0值
                            try {
                                long time = dateFormat.parse(dateStr).getTime();
                                // 计算两次的差值, 该值即为零流量的持续时间
                                value = value.add(BigDecimal.valueOf(time - lastTime).abs());
                                // 重置lastTime和标识
                                lastTime = -1;
                                isZero = false;
                            } catch (ParseException e) {
                            }
                        }
                    }
                }
            }
            ExceptionByZeroDTO zeroDTO = new ExceptionByZeroDTO();
            zeroDTO.setStationId(station.getId());
            zeroDTO.setStationName(station.getName());
            // 计算成分钟数

            if (value.doubleValue() == 0 && isZero) {
                value = BigDecimal.valueOf(24 * 60 * 60 * 1000L);
                if (key.equals(LocalDate.now().toString())) {
                    value = BigDecimal.valueOf(System.currentTimeMillis() - LocalDate.now().atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli());
                }
            }
            zeroDTO.setValue(value.divide(BigDecimal.valueOf(60000), 2, BigDecimal.ROUND_HALF_EVEN));
            zeroDTO.setValueDate(key);

            resultList.add(zeroDTO);
        }
        // 按range筛选
        if (range != null) {
            resultList = resultList.stream()
                    .filter(data -> data.getValue().intValue() >= range)
                    .collect(Collectors.toList());
        }
        Collections.reverse(resultList);

        return resultList;
    }

    @Override
    public List<JSONObject> getDayReport(String stationName, Date dayTime, TenantId tenantId) throws ThingsboardException {
        // 查询站点信息
        PageData<StationEntity> pageData = stationFeignClient.list(1, 99999,
                DataConstants.StationType.FLOW_FLOW_MONITORING.getValue() + "," + DataConstants.StationType.FLOW_MONITORING.getValue(), "");

        if (pageData.getTotal() == 0) {
            throw new ThingsboardException("无流量站点!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }
        List<StationEntity> stationList = pageData.getData();
        if (StringUtils.isNotBlank(stationName)) {
            stationList = stationList.stream().filter(station -> station.getName().contains(stationName)).collect(Collectors.toList());
        }

        List<String> attributes = new ArrayList<>();
        Map<String, StationEntity> attrStationMap = new HashMap<>();
        Map<String, StationAttrEntity> attrInfoMap = new HashMap<>();
        for (StationEntity station : stationList) {
            // 查询数据
            List<StationAttrEntity> stationAttrList = stationFeignClient.getStationAllAttrList(station.getId());
            for (StationAttrEntity stationAttrEntity : stationAttrList) {
                if (DataConstants.DeviceAttrType.TOTAL_FLOW.getValue().equals(stationAttrEntity.getAttr())) {// 累计流量
                    try {
                        String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(stationAttrEntity.getDeviceId()));
                        attributes.add(deviceId + "." + stationAttrEntity.getAttr());

                        attrStationMap.put(deviceId + "." + stationAttrEntity.getAttr(), station);
                        attrInfoMap.put(station.getId(), stationAttrEntity);

                        break;
                    } catch (Exception e) {
                        log.error("[查询站点数据异常] 查询站点ID: {}", station.getId());
                    }
                }
            }
        }
        if (attributes.size() == 0) {
            throw new ThingsboardException("查询的流量监测站未配置累计流量!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }

        // 结束时间为当天23:59:59
        Date endTime = new Date(dayTime.getTime() + (24 * 60 * 60 * 1000) - 1000);
        Date tempStartTime = new Date(dayTime.getTime());
        Date startTime = new Date(dayTime.getTime() - (24 * 60 * 60 * 1000));

        // 查询数据
        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> dataMap =
                obtainDataService.getDeviceDataByOriginal(attributes, startTime.getTime(), endTime.getTime(), DateUtils.DAY, null, tenantId);

        Map<String, LinkedHashMap<String, BigDecimal>> stationDataMap = new LinkedHashMap<>();
        for (Map.Entry<String, LinkedHashMap<String, BigDecimal>> entry : dataMap.entrySet()) {
            // 时间
            String key = entry.getKey();
            // 数据map
            LinkedHashMap<String, BigDecimal> value = entry.getValue();
            for (Map.Entry<String, BigDecimal> dataEntry : value.entrySet()) {
                String attrKey = dataEntry.getKey();
                StationEntity station = attrStationMap.get(attrKey);
                if (station == null) {
                    continue;
                }
                LinkedHashMap<String, BigDecimal> stationData = stationDataMap.get(station.getId());
                if (stationData == null) {
                    stationData = new LinkedHashMap<>();
                }
                stationData.put(key, dataEntry.getValue());

                stationDataMap.put(station.getId(), stationData);
            }
        }

        // 数据返回
        List<JSONObject> resultList = new ArrayList<>();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        for (StationEntity station : stationList) {
            JSONObject data = new JSONObject();
            data.put("stationId", station.getId());
            data.put("stationName", station.getName());
            // 获取数据
            LinkedHashMap<String, BigDecimal> stationData = stationDataMap.get(station.getId());
            BigDecimal todayData = stationData.get(dateFormat.format(tempStartTime));
            BigDecimal yesterdayData = stationData.get(dateFormat.format(startTime));
            if (todayData != null && yesterdayData != null) {
                data.put("useData", todayData.subtract(yesterdayData));
            }
            data.put("todayData", todayData);
            data.put("yesterdayData", yesterdayData);
            data.put("time", dateFormat.format(tempStartTime));

            StationAttrEntity stationAttr = attrInfoMap.get(station.getId());
            if (stationAttr != null) {
                String range = stationAttr.getRange();
                try {
                    if (StringUtils.isNotBlank(range)) {
                        String[] rangeArray = range.split(",");
                        data.put("min", rangeArray[0]);
                        data.put("max", rangeArray[1]);

                        if (data.getBigDecimal("useData") != null) {
                            if (data.getBigDecimal("useData").doubleValue() < new BigDecimal(rangeArray[0]).doubleValue()) {
                                data.put("useData", data.getBigDecimal("useData") + "(低限)");
                            }
                            if (data.getBigDecimal("useData").doubleValue() > new BigDecimal(rangeArray[1]).doubleValue()) {
                                data.put("useData", data.getBigDecimal("useData") + "(高限)");
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error("[解析数据变量上下限异常] 错误的站点ID: {}, 错误的配置: {}", station.getId(), range);
                }
            }
            resultList.add(data);
        }

        return resultList;
    }

    @Override
    public List<ExceptionByFlowDTO> getExceptionFlowData(String stationId, Date startTime, Date endTime, Integer divideHour, Integer overloadHour, TenantId tenantId) throws ThingsboardException {
        // 查询站点信息
        StationEntity station = stationFeignClient.get(stationId);
        if (station == null) {
            throw new ThingsboardException("查询的站点不存在!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }

        // 查询数据
        List<String> attributes = new ArrayList<>();
        List<StationAttrEntity> stationAttrList = stationFeignClient.getStationAllAttrList(station.getId());
        DeviceModelInfoEntity deviceModelInfo = null;

        for (StationAttrEntity stationAttrEntity : stationAttrList) {
            String attr = DataConstants.DeviceAttrType.INSTANTANEOUS_FLOW.getValue();
            if (attr.equals(stationAttrEntity.getAttr())) {// 瞬时流量
                try {
                    String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(stationAttrEntity.getDeviceId()));
                    deviceModelInfo = deviceModelInfoClient.getByDeviceId(deviceId);
                    if (deviceModelInfo == null) {
                        throw new ThingsboardException("查询的站点未绑定设备型号!", ThingsboardErrorCode.ITEM_NOT_FOUND);
                    }
                    attributes.add(deviceId + "." + stationAttrEntity.getAttr());
                    break;
                } catch (Exception e) {
                    log.error("[查询站点数据异常] 查询站点ID: {}", station.getId());
                }
            }
        }
        if (attributes.size() == 0) {
            throw new ThingsboardException("查询的流量监测站未配置瞬时流量!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }
        // 结束时间为当天23:59:59
        endTime = new Date(endTime.getTime() + (24 * 60 * 60 * 1000) - 1000);
        LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> dataMap =
                obtainDataService.getDeviceOriginalData(attributes, startTime.getTime(), endTime.getTime(), null, tenantId);


        if (dataMap == null) {
            throw new ThingsboardException("查询数据为空!", ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }

        Map<String, BigDecimal> newDateMap = new LinkedHashMap<>();
        for (Map.Entry<String, LinkedHashMap<String, BigDecimal>> entry : dataMap.entrySet()) {
            String key = entry.getKey();
            BigDecimal value = null;
            LinkedHashMap<String, BigDecimal> valueMap = entry.getValue();
            if (valueMap != null) {
                for (Map.Entry<String, BigDecimal> dataEntry : valueMap.entrySet()) {
                    BigDecimal decimal = dataEntry.getValue();
                    if (decimal != null) {
                        value = decimal;
                    }
                }
            }
            newDateMap.put(key, value);
        }

        // 按照日期分组
        Map<String, LinkedHashMap<String, BigDecimal>> dateGroupMap = new LinkedHashMap<>();
        for (Map.Entry<String, BigDecimal> entry : newDateMap.entrySet()) {
            String key = entry.getKey();
            BigDecimal value = entry.getValue();

            String dayStr = key.substring(0, 10);
            LinkedHashMap<String, BigDecimal> dayMap = dateGroupMap.get(dayStr);
            if (dayMap == null) {
                dayMap = new LinkedHashMap<>();
            }
            dayMap.put(key, value);

            dateGroupMap.put(dayStr, dayMap);
        }

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        // 按日期计算零流量时间
        List<ExceptionByFlowDTO> resultList = new ArrayList<>();
        for (Map.Entry<String, LinkedHashMap<String, BigDecimal>> entry : dateGroupMap.entrySet()) {
            String key = entry.getKey();
            LinkedHashMap<String, BigDecimal> valueMap = entry.getValue();
            valueMap = valueMap.entrySet().stream().sorted(Comparator.comparing(Map.Entry::getKey)).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (e1, e2) -> e2, LinkedHashMap::new));
            BigDecimal divideValueHour = BigDecimal.ZERO;
            BigDecimal overloadValueHour = BigDecimal.ZERO;
            boolean isDivide = false;
            if (valueMap != null && valueMap.size() > 0) {
                long lastTime = -1;
                isDivide = false;
                for (Map.Entry<String, BigDecimal> valueEntry : valueMap.entrySet()) {
                    String dateStr = valueEntry.getKey();
                    BigDecimal data = valueEntry.getValue();
                    if (data == null || data.doubleValue() < deviceModelInfo.getDivideFlow().doubleValue()) {// 为0
                        if (!isDivide) {
                            // 记录本次为0的时间
                            try {
                                lastTime = dateFormat.parse(dateStr).getTime();
                                isDivide = true;
                            } catch (ParseException e) {
                            }
                        }
                    } else {
                        // 不为0
                        if (isDivide) {// 已记录0值
                            try {
                                long time = dateFormat.parse(dateStr).getTime();
                                // 计算两次的差值, 该值即为零流量的持续时间
                                divideValueHour = divideValueHour.add(BigDecimal.valueOf(time - lastTime).abs());
                                // 重置lastTime和标识
                                lastTime = -1;
                                isDivide = false;
                            } catch (ParseException e) {
                            }
                        }
                    }
                }
            }

            // 过载上
            boolean isOverflow = false;
            if (valueMap != null && valueMap.size() > 0) {
                long lastTime = -1;
                isOverflow = false;
                for (Map.Entry<String, BigDecimal> valueEntry : valueMap.entrySet()) {
                    String dateStr = valueEntry.getKey();
                    BigDecimal data = valueEntry.getValue();
                    if (data == null || data.doubleValue() > deviceModelInfo.getOverloadFlow().doubleValue()) {// 为0
                        if (!isOverflow) {
                            // 记录本次为0的时间
                            try {
                                lastTime = dateFormat.parse(dateStr).getTime();
                                isOverflow = true;
                            } catch (ParseException e) {
                            }
                        }
                    } else {
                        // 不为0
                        if (isOverflow) {// 已记录0值
                            try {
                                long time = dateFormat.parse(dateStr).getTime();
                                // 计算两次的差值, 该值即为零流量的持续时间
                                overloadValueHour = overloadValueHour.add(BigDecimal.valueOf(time - lastTime).abs());
                                // 重置lastTime和标识
                                lastTime = -1;
                                isOverflow = false;
                            } catch (ParseException e) {
                            }
                        }
                    }
                }
            }
            ExceptionByFlowDTO zeroDTO = new ExceptionByFlowDTO();
            zeroDTO.setStationId(station.getId());
            zeroDTO.setStationName(station.getName());
            // 计算成小时数

            if (divideValueHour.doubleValue() == 0 && isDivide) {
                divideValueHour = BigDecimal.valueOf(24 * 60 * 60 * 1000L);
                if (key.equals(LocalDate.now().toString())) {
                    divideValueHour = BigDecimal.valueOf(System.currentTimeMillis() - LocalDate.now().atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli());
                }
            }

            if (overloadValueHour.doubleValue() == 0 && isOverflow) {
                overloadValueHour = BigDecimal.valueOf(24 * 60 * 60 * 1000L);
                if (key.equals(LocalDate.now().toString())) {
                    divideValueHour = BigDecimal.valueOf(System.currentTimeMillis() - LocalDate.now().atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli());
                }
            }
            zeroDTO.setDivideHour(divideValueHour.divide(BigDecimal.valueOf(60 * 60000), 2, BigDecimal.ROUND_UP));
            zeroDTO.setOverloadHour(overloadValueHour.divide(BigDecimal.valueOf(60 * 60000), 2, BigDecimal.ROUND_UP));
            zeroDTO.setValueDate(key);

            resultList.add(zeroDTO);
        }
        // 筛选
        if (divideHour != null) {
            resultList = resultList.stream()
                    .filter(data -> data.getDivideHour().doubleValue() >= divideHour)
                    .collect(Collectors.toList());
        }
        // 筛选
        if (overloadHour != null) {
            resultList = resultList.stream()
                    .filter(data -> data.getOverloadHour().doubleValue() >= overloadHour)
                    .collect(Collectors.toList());
        }
        Collections.reverse(resultList);

        return resultList;
    }

    @Override
    public List<JSONObject> getReadingsBetween(String stationIds, Long start, Long end, TenantId tenantId) {
        // 查询站点列表
        List<StationEntity> stationList = stationFeignClient.findByStationIdList(stationIds);
        if (stationList == null || stationList.isEmpty()) {
            return new ArrayList<>();
        }

        Map<String, StationEntity> stationMap = stationList.stream().collect(Collectors.toMap(StationEntity::getId, station -> station));

        // 查询站点供水量数据
        Map<String, List<String>> attributesMap = new LinkedHashMap<>();
        String unit = "";
        for (StationEntity station : stationList) {
            List<String> attributes = new ArrayList<>();
            List<StationAttrEntity> stationAttrList = stationFeignClient.getStationAllAttrList(station.getId());

            for (StationAttrEntity stationAttrEntity : stationAttrList) {
                if (DataConstants.DeviceAttrType.TOTAL_FLOW.getValue().equals(stationAttrEntity.getAttr())) {// 累计流量
                    try {
                        String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(stationAttrEntity.getDeviceId()));
                        attributes.add(deviceId + "." + stationAttrEntity.getAttr());
                        unit = stationAttrEntity.getUnit();
                    } catch (Exception e) {
                        log.error("[查询站点数据异常] 查询站点ID: {}", station.getId());
                    }
                }
                //TODO 2025/4/9
//                if (DataConstants.DeviceAttrType.POSITIVE.getValue().equals(stationAttrEntity.getAttr())) {// 正向流量
//                    try {
//                        String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(stationAttrEntity.getDeviceId()));
//                        attributes.add(deviceId + "." + stationAttrEntity.getAttr());
//                        unit = stationAttrEntity.getUnit();
//                    } catch (Exception e) {
//                        log.error("[查询站点数据异常] 查询站点ID: {}", station.getId());
//                    }
//                }
//                if (DataConstants.DeviceAttrType.REVERSE.getValue().equals(stationAttrEntity.getAttr())) {// 反向
//                    try {
//                        String deviceId = UUIDConverter.fromTimeUUID(UUID.fromString(stationAttrEntity.getDeviceId()));
//                        attributes.add(deviceId + "." + stationAttrEntity.getAttr());
//                        unit = stationAttrEntity.getUnit();
//                    } catch (Exception e) {
//                        log.error("[查询站点数据异常] 查询站点ID: {}", station.getId());
//                    }
//                }
            }
            attributesMap.put(station.getId(), attributes);
        }

        List<JSONObject> resultList = new ArrayList<>();
        for (Map.Entry<String, List<String>> attrEntry : attributesMap.entrySet()) {
            String key = attrEntry.getKey();
            List<String> attrList = attrEntry.getValue();
            if (attrList == null || attrList.isEmpty()) {
                continue;
            }

            //TODO 2025/4/9
            // 查询数据
//            Map<String, List<DataPoint>> stationDataMap = obtainDataService.getFirstAndLastData(attrList, start, end, tenantId);
//            List<DataPoint> firstDataList = stationDataMap.get("first");
//            List<DataPoint> lastDataList = stationDataMap.get("last");

            JSONObject data = new JSONObject();
            StationEntity station = stationMap.get(key);
            data.put("stationId", key);
            data.put("name", station.getName());
            data.put("start", start);
            data.put("end", end);
//            for (DataPoint dataPoint : firstDataList) {
//                data.put("first_" + dataPoint.getProp(), dataPoint.getValue());
//            }
//            for (DataPoint dataPoint : lastDataList) {
//                data.put("last_" + dataPoint.getProp(), dataPoint.getValue());
//            }
            resultList.add(data);

        }

        return resultList;
    }
}
