import{_ as R}from"./index-C9hz-UZb.js";import{_ as G}from"./CardTable-rdWOL4_6.js";import{d as M,M as H,r as b,c as y,am as J,bF as p,a8 as $,bB as I,s as C,a6 as Q,bu as U,ay as W,g as x,n as N,q as u,i as o,F as _,cs as w,j as X,an as D,dF as K,dA as Y,al as Z,b7 as ee,aj as ae,C as te}from"./index-r0dFAfgr.js";import{_ as ne}from"./CardSearch-CB_HNR-Q.js";import{g as re,l as se}from"./queryStatistics-HnNdnFZW.js";import{b as oe}from"./zhandian-YaGuQZe6.js";import{u as le}from"./useStation-DJgnSZIA.js";import{f as ie}from"./formartColumn-D5r7JJ2G.js";import"./Search-NSrhrIa_.js";const ce={class:"wrapper"},de={key:1},me=M({__name:"index",setup(ue){const{$messageWarning:P,$messageError:V}=H(),{getStationTree:A}=le(),t=b({type:"day",chartName:"",chartOption:null,activeName:"echarts",data:null,stationTree:[]}),f=y(),v=y(),k=y(),z=y(),q=y();let h=b([]);J(()=>t.activeName,()=>{t.activeName==="echarts"&&O()});const g=b({defaultParams:{queryType:"day",day:[p().format(),p().format()],month:[p().subtract(1,"month"),p().format()]},filters:[{type:"select-tree",label:"监测点:",field:"attributeId",clearable:!1,options:$(()=>t.stationTree),lazy:!0,lazyLoad:(e,a)=>{var r,l;if(e.level===0)return a([]);if(((r=e.data.children)==null?void 0:r.length)>0)return a(e.data.children);if(e.isLeaf)return a([]);if((l=e.data)!=null&&l.isLeaf)return a([]);oe({stationId:e.data.id}).then(c=>{var n;const d=(n=c.data)==null?void 0:n.map(m=>({label:m.type,value:"",id:"",children:m.attrList.map(s=>({label:s.name,value:s.id,id:s.id,isLeaf:!0}))}));return a(d)})},nodeClick:(e,a)=>{a.isLeaf&&(t.chartName=e.label,I(()=>{L()}))}},{type:"select",label:"比较类型:",field:"queryType",clearable:!1,width:"200px",options:[{label:"日分时(时间段)",value:"day"},{label:"月分日(时间段)",value:"month"}],itemContainerStyle:{width:"240px"},onChange:e=>{B(e)}},{type:"daterange",label:"日期",field:"day",clearable:!1,handleHidden:(e,a,r)=>{r.hidden=e.queryType==="month"}},{type:"monthrange",label:"日期",field:"month",clearable:!1,handleHidden:(e,a,r)=>{r.hidden=e.queryType==="day"}}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",click:()=>{var a;i.pagination.page=1,(((a=f.value)==null?void 0:a.queryParams)||{}).attributeId?L():P("选择监测点")},svgIcon:C(Z)},{type:"default",perm:!0,text:"重置",svgIcon:C(ee),click:()=>{var e;(e=f.value)==null||e.resetForm()}},{perm:!0,type:"warning",text:"导出",hide:()=>t.activeName!=="list",svgIcon:C(ae),click:()=>F()}]}]}),i=b({loading:!1,dataList:[],columns:[],pagination:{refreshData:({page:e,size:a})=>{i.pagination.page=e,i.pagination.limit=a,i.dataList=h.slice((e-1)*a,e*a)}}}),B=e=>{var r,l;q.value=e;const a=(r=g.filters)==null?void 0:r.find(c=>c.field==="date");a.type=q[e],e==="monthrange"&&(g.defaultParams={...g.defaultParams,date:[p().add(-1,"month").startOf("month"),p().endOf("month")]},(l=f.value)==null||l.resetForm())},L=()=>{var c;i.loading=!0;const e=((c=f.value)==null?void 0:c.queryParams)||{},[a,r]=e[e.queryType]||[],l={attributeId:e.attributeId,queryType:e.queryType,start:a?p(a).startOf(e.queryType).valueOf():"",end:r?p(r).endOf(e.queryType).valueOf():""};re(l).then(d=>{var s;const n=(s=d.data)==null?void 0:s.data;t.data=n;const m=ie(n==null?void 0:n.tableInfo);h=n==null?void 0:n.tableDataList,i.columns=m,i.dataList=h==null?void 0:h.slice(0*20,20),i.pagination.total=n==null?void 0:n.tableDataList.length,i.loading=!1,O()}).catch(d=>{V(d)})},E=()=>{var e;(e=v.value)==null||e.resize()},O=()=>{var d,n,m;const e=(d=t.data)==null?void 0:d.tableDataList.map(s=>s.ts),a=se(e);a.series=[];const r={name:"",smooth:!0,data:[],type:"line",markPoint:{data:[{type:"max",name:"最大值"},{type:"min",name:"最小值"}]},markLine:{data:[{type:"average",name:"平均值"}]}};(n=t.data)==null||n.tableInfo.map(s=>{var S;if(s.columnValue!=="ts"){const T=JSON.parse(JSON.stringify(r));T.name=s.columnName,T.data=(S=t.data)==null?void 0:S.tableDataList.map(j=>j[s.columnValue]),a.yAxis[0].name=t.chartName+(s.unit?"("+s.unit+")":""),a.series.push(T)}}),(m=v.value)==null||m.clear();const c=Q({callOnAdd:!0});I(()=>{c.listenTo(k.value,()=>{t.chartOption=a,E()})})},F=()=>{var e;(e=z.value)==null||e.exportTable()};return U(async()=>{const e=["流量监测站,测流压站"].join(","),a=await A(e);t.stationTree=a,console.log(" state.stationTree ",t.stationTree)}),(e,a)=>{const r=ne,l=K,c=Y,d=W("VChart"),n=G,m=R;return x(),N("div",ce,[u(r,{ref_key:"cardSearch",ref:f,config:o(g)},null,8,["config"]),u(m,{class:"card-table",title:o(t).chartName+(o(t).activeName==="list"?"周期对比列表":"周期对比曲线")},{right:_(()=>[u(c,{modelValue:o(t).activeName,"onUpdate:modelValue":a[0]||(a[0]=s=>o(t).activeName=s)},{default:_(()=>[u(l,{label:"echarts"},{default:_(()=>[u(o(w),{style:{"margin-right":"1px","font-size":"16px"},icon:"clarity:line-chart-line"})]),_:1}),u(l,{label:"list"},{default:_(()=>[u(o(w),{style:{"margin-right":"1px","font-size":"16px"},icon:"material-symbols:table"})]),_:1})]),_:1},8,["modelValue"])]),default:_(()=>[o(t).activeName==="echarts"?(x(),N("div",{key:0,ref_key:"echartsDiv",ref:k,class:"chart-box"},[u(d,{ref_key:"refChart",ref:v,theme:o(X)().isDark?"dark":"light",option:o(t).chartOption},null,8,["theme","option"])],512)):D("",!0),o(t).activeName==="list"?(x(),N("div",de,[u(n,{ref:"refCardTable",class:"cardTable",config:o(i)},null,8,["config"])])):D("",!0)]),_:1},8,["title"])])}}}),Ce=te(me,[["__scopeId","data-v-91d9e148"]]);export{Ce as default};
