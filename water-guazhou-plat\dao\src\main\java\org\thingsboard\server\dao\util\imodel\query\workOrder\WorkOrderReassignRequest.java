package org.thingsboard.server.dao.util.imodel.query.workOrder;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrder;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrderDetail;
import org.thingsboard.server.dao.util.imodel.query.IStarHttpRequest;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;

import java.util.Date;

import static org.thingsboard.server.dao.model.sql.workOrder.WorkOrderStatus.REASSIGN;

@Getter
@Setter
public class WorkOrderReassignRequest extends SaveRequest<WorkOrderDetail> {
    // 下一步的处理人
    private String stepProcessUserId;

    @Override
    public String valid(IStarHttpRequest request) {
        if (stepProcessUserId == null) {
            return "重新指定的用户为空";
        }

        stepProcessUserId = parseUUID(stepProcessUserId);

        return null;
    }

    public WorkOrderDetail process(WorkOrder order) {
        Date now = new Date();
        String stepProcessUserId = parseUUID(this.stepProcessUserId);
        // region order
        // 下一步处理人
        order.setStepProcessUserId(stepProcessUserId);
        order.setProcessUserId(stepProcessUserId);
        order.setStatus(REASSIGN.getToStage());
        order.setUpdateTime(now);
        // endregion

        // region detail
        WorkOrderDetail detail = new WorkOrderDetail();
        detail.setMainId(order.getId());
        detail.setType(REASSIGN);
        // 记录当前处理用户为处理人
        detail.setProcessUserId(currentUserUUID());
        detail.setProcessTime(now);
        // 下一步处理人需要传入
        detail.setNextProcessUserId(stepProcessUserId);
        // endregion
        return detail;
    }

    @Override
    public WorkOrderDetail build() {
        return null;
    }

    @Override
    public WorkOrderDetail update(String id) {
        return null;
    }
}
