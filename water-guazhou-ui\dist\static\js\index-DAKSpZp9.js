import{d as x,c as s,r as a,Q as L,g as w,h as C,F as I,q as n,i as l,_ as F,aq as B,C as D}from"./index-r0dFAfgr.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import{g as M}from"./LayerHelper-Cn-iiqxI.js";import"./project-DUuzYgGl.js";import{u as S}from"./arcWidgetButton-0glIxrt7.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import z from"./RightDrawerMap-D5PhmGFO.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./pipe-nogVzCHG.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./ArcView-DpMnCY82.js";import"./ViewHelper-BGCZjxXH.js";import"./fieldconfig-Bk3o1wi7.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./ArcStationWarning-BF9YrSzF.js";/* empty css                         */import"./useWaterPoint-Bv0z6ym6.js";import"./DateFormatter-Bm9a68Ax.js";import"./zhandian-YaGuQZe6.js";import"./useStation-DJgnSZIA.js";import"./DrawerBox-CLde5xC8.js";import"./SideDrawer-CBntChyn.js";import"./PipeDetail-CTBPYFJW.js";import"./Search-NSrhrIa_.js";import"./FormTableColumnFilter-BT7pLXIC.js";import"./QueryHelper-ILO3qZqg.js";import"./config-fy91bijz.js";import"./ArcBRTools-BO92yznB.js";import"./PipeLength.vue_vue_type_script_setup_true_lang-BZfUsvDf.js";import"./StatisticsHelper-D-s_6AyQ.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./wfsUtils-DXofo3da.js";import"./usePipelineGroup-Ba1pmWz_.js";import"./GroupLayer-DhhN3FyN.js";import"./lazyLayerLoader-DbM9sT1W.js";import"./WFSLayer-1TtJp3nx.js";import"./clientSideDefaults-VQhQaYxh.js";import"./QueryEngineCapabilities-Dk3NJwmm.js";import"./wfsUtils-Du031XaC.js";import"./geojson-MBFu2-HZ.js";import"./xmlUtils-CtUoQO7q.js";import"./ListWindow-WS05QqV0.js";import"./PopLayout-BP55MvL7.js";import"./PoiSearchV2-D7yNeLuv.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./URLHelper-B9aplt5w.js";import"./useLayerList-DmEwJ-ws.js";import"./useScaleBar-Beed-z91.js";const q=x({__name:"index",setup(A){const d=s(),f=s(),r=a({tabs:[],layerInfos:[],layerIds:[],loading:!1}),t={},g=a({group:[{fieldset:{desc:"绘制工具"},fields:[{type:"btn-group",btns:[{perm:!0,text:"",type:"default",size:"large",title:"绘制多边形",disabled:()=>r.loading,iconifyIcon:"mdi:shape-polygon-plus",click:()=>m("polygon")},{perm:!0,text:"",type:"default",size:"large",title:"绘制矩形",disabled:()=>r.loading,iconifyIcon:"ep:crop",click:()=>m("rectangle")},{perm:!0,text:"",type:"default",size:"large",title:"绘制圆形",disabled:()=>r.loading,iconifyIcon:"mdi:ellipse-outline",click:()=>m("circle")},{perm:!0,text:"",type:"default",size:"large",title:"清除图形",disabled:()=>r.loading,iconifyIcon:"ep:delete",click:()=>y()}]}]}],labelPosition:"top",gutter:12}),e=a({columns:[{label:"分区",prop:"name"},{label:"创建时间",prop:"time"}],dataList:[],operations:[],pagination:{refreshData:({page:o,size:i})=>{e.pagination.page=o||1,e.pagination.limit=i||20,u()}}}),u=()=>{},m=o=>{var i,p;t.view&&((i=t.graphicsLayer)==null||i.removeAll(),t.graphics=void 0,(p=k.value)==null||p.create(o))},y=()=>{var o;(o=t.graphicsLayer)==null||o.removeAll(),t.graphics=void 0},{initSketch:_,destroySketch:h,sketch:k}=S(),c=o=>{o.state==="complete"&&(t.graphics=o.graphics[0],console.log(JSON.stringify(t.graphics)))},b=async o=>{t.view=o,t.graphicsLayer=M(t.view,{id:"search-quick",title:"快速查询"}),_(t.view,t.graphicsLayer,{updateCallBack:c,createCallBack:c})};return L(()=>{var o;t.graphicsLayer&&((o=t.view)==null||o.map.remove(t.graphicsLayer)),h()}),(o,i)=>{const p=F,v=B;return w(),C(z,{ref_key:"refMap",ref:d,title:"管网分析",onMapLoaded:b},{default:I(()=>[n(p,{ref_key:"refForm",ref:f,config:l(g)},null,8,["config"]),n(v,{class:"table-box",config:l(e)},null,8,["config"])]),_:1},512)}}}),Eo=D(q,[["__scopeId","data-v-15ea2903"]]);export{Eo as default};
