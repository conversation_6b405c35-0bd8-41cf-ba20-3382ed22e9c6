<!-- gis管线 -->
<template>
  <div class="onemap-panel-wrapper">
    <Cards v-model="cardsvalue" :span="12"></Cards>
    <Form ref="refForm" :config="FormConfig"></Form>
  </div>
</template>
<script lang="ts" setup>
import {
  GetPipeLengthByDiameterRange,
  GetPipeLengthByYear,
  PipeStatistics
} from '@/api/mapservice/pipe';
import { useAppStore, useGisStore } from '@/store';
import { EStatisticField, getPipeLineLayerOption } from '@/utils/MapHelper';
import { SLMessage } from '@/utils/Message';
import { sumBy } from 'lodash-es';
import { Cards } from '../../components';
import { oneHistogram, ring } from '../../components/components/chart';

const emit = defineEmits(['highlightMark', 'addMarks']);
const props = defineProps<{
  view?: __esri.MapView;
  menu: IMenuItem;
}>();

const state = reactive<{
  pipeLayerOption: NormalOption[];
}>({
  pipeLayerOption: []
});

const cardsvalue = ref([
  { label: '0 km', value: '管网总长度' },
  { label: '0 条', value: '管网总数量' }
]);
const refForm = ref<IFormIns>();
const FormConfig = reactive<IFormConfig>({
  labelPosition: 'top',
  group: [
    {
      id: 'diameter',
      fieldset: {
        type: 'underline',
        desc: '按管径区间统计管长'
      },
      fields: [
        {
          type: 'vchart',
          option: ring(),
          handleClick: (params: any) => {
            const range = params.data.name?.split('-');
            if (range.length < 2) {
              SLMessage.error('参数错误');
              return;
            }
            let sql = '';
            if (range[0] !== '') {
              if (range[1] !== '') {
                sql =
                  ' DIAMETER <= ' +
                  range[1] +
                  ' and DIAMETER > ' +
                  range[0] +
                  ' ';
              } else {
                sql = ' DIAMETER>' + range[0] + ' ';
              }
            } else {
              sql = ' DIAMETER<=' + range[1] + ' ';
            }
            emit('highlightMark', props.menu, sql, params.data.nameAlias);
            // queryPipeLine(
            //   params.data,
            //   sql
            //   // ' DIAMETER=' + (params.data.name || '0') + ' '
            // )
          },
          style: {
            width: '100%',
            height: '150px'
          },
          itemContainerStyle: {
            marginBottom: 0
          }
        }
      ]
    },
    {
      id: 'year',
      fieldset: {
        type: 'underline',
        desc: '按年份统计管长'
      },
      fields: [
        {
          type: 'vchart',
          option: oneHistogram(),
          style: {
            width: '100%',
            height: '200px'
          },
          itemContainerStyle: {
            marginBottom: 0
          }
        }
      ]
    },
    {
      id: 'diameter-bar',
      fieldset: {
        type: 'underline',
        desc: '按管径统计管长'
      },
      fields: [
        {
          type: 'vchart',
          option: oneHistogram(),
          style: {
            width: '100%',
            height: '150px'
          },
          handleClick: (params: any) => {
            emit(
              'highlightMark',
              props.menu,
              " DIAMETER='" + (params.data.name || '0') + "' ",
              params.data.nameAlias
            );
          },
          // queryPipeLine(
          //   params.data,
          //   ' DIAMETER=' + (params.data.name || '0') + ' '
          // ),
          itemContainerStyle: {
            marginBottom: 0
          }
        }
      ]
    },
    {
      id: 'material-bar',
      fieldset: {
        type: 'underline',
        desc: '按管材统计管长'
      },
      fields: [
        {
          type: 'vchart',
          option: oneHistogram(),
          style: {
            width: '100%',
            height: '150px'
          },
          handleClick: (params: any) => {
            //   queryPipeLine(
            //   params.data,
            //   params.data.name && " MATERIAL='" + params.data.name + "' "
            // )
            emit(
              'highlightMark',
              props.menu,
              " MATERIAL='" + params.data.name + "' ",
              params.name
            );
          }
        }
      ]
    }
  ]
});
// const queryPipeLine = async (data: any, sql?: string) => {
//   if (!props.view) return
//   const layerid = state.pipeLayerOption[0]?.data.layerid
//   if (layerid === undefined) return
//   const res = await excuteQuery(
//     window.SITE_CONFIG.GIS_CONFIG.gisService + window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService + '/' + layerid,
//     initQueryParams({
//       returnGeometry: true,
//       where: sql || ''
//       // ' DIAMETER=' + (data.name || 0) + ' '
//     })
//   )
//   const symbol = setSymbol('polyline')
//   const layer = getGraphicLayer(props.view, {
//     id: props.menu.path,
//     title: props.menu.meta.title
//   })
//   layer?.removeAll()
//   res.features.map(item => (item.symbol = symbol))
//   layer?.addMany(res.features)
//   SLMessage.success('查询成功！')
// }
const getLengthTotal = async (group_fields?: string[]) => {
  const res = await PipeStatistics({
    usertoken: useGisStore().gToken,
    layerids: JSON.stringify(
      state.pipeLayerOption.map((item) => item.id) || []
    ),
    group_fields: JSON.stringify(group_fields || ['DIAMETER']),
    statistic_field: EStatisticField.ShapeLen,
    statistic_type: '2',
    where: '',
    f: 'pjson'
  });
  const data: any[] = res.data?.result?.rows[0]?.rows || [];
  return data;
};
const getCountTotal = async () => {
  const staticField = 'OBJECTID';
  const res = await PipeStatistics({
    usertoken: useGisStore().gToken,
    layerids: JSON.stringify(
      state.pipeLayerOption.map((item) => item.id) || []
    ),
    group_fields: JSON.stringify(['DIAMETER']),
    statistic_field: staticField,
    statistic_type: '1',
    where: '',
    f: 'pjson'
  });
  const data: any[] = res.data?.result?.rows[0]?.rows || [];
  const total = data.reduce((prev, cur) => {
    return (cur && cur[staticField] && prev + cur[staticField]) || 0;
  }, 0);
  cardsvalue.value[1].label = total + ' 条';
};
// const refreshDiameterBar = (data:{DIAMETER:number, [EStatisticField.ShapeLen]:number}[]) => {
//   const field = FormConfig.group.find(item => item.id === 'diameter-bar')?.fields[0] as IFormVChart
//   if (!field) return
//   const xData = data.map(item => 'DN' + item.DIAMETER)
//   const Data = data.map(item => ((item[EStatisticField.ShapeLen] || 0) / 1000).toFixed(2))
//   field.option = oneHistogram(xData, Data, 'km')
// }
const refreshDiameterRing = async () => {
  const res = await GetPipeLengthByDiameterRange();
  const data = res.data.result;
  const chartData = data.map((item) => {
    return {
      name: item.itemname,
      nameAlias: 'DN' + item.itemname,
      value: item.sumlength,
      valueAlias: item.sumlength,
      scale: item.itempercent + '%'
    };
  });
  const field = FormConfig.group.find((item) => item.id === 'diameter')
    ?.fields[0] as IFormVChart;
  field && (field.option = ring(chartData, 'km', 'DN'));
  const total = sumBy(chartData, (item: any) => item.value);
  cardsvalue.value[0].label = total.toFixed(2) + ' km';
};
const refreshYearBar = async () => {
  const res = await GetPipeLengthByYear();
  const data = res.data.result;
  const chartData = data.map((item) => {
    return {
      name: item.itemname,
      nameAlias: item.itemname + '年',
      value: item.sumlength,
      valueAlias: item.sumlength,
      scale: item.itempercent + '%'
    };
  });
  const field1 = FormConfig.group.find((item) => item.id === 'year')
    ?.fields[0] as IFormVChart;
  field1 && (field1.option = oneHistogram(chartData, 'km'));
};
const refreshDiameterBar = (data: {
  data: { DIAMETER: number; [EStatisticField.ShapeLen]: number }[];
  total: number;
}) => {
  const chartData = data.data.map((item) => {
    return {
      name: item.DIAMETER?.toString() || '未知',
      nameAlias: 'DN' + (item.DIAMETER?.toFixed(0) || '未知'),
      value: ((item[EStatisticField.ShapeLen] || 0) / 1000).toFixed(4),
      valueAlias: ((item[EStatisticField.ShapeLen] || 0) / 1000).toFixed(4),
      scale:
        (((item[EStatisticField.ShapeLen] || 0) / data.total) * 100).toFixed(
          0
        ) + '%'
    };
  });

  const field1 = FormConfig.group.find((item) => item.id === 'diameter-bar')
    ?.fields[0] as IFormVChart;
  field1 && (field1.option = oneHistogram(chartData, 'km'));
};
const refreshMaterialBar = (
  data: { MATERIAL: string; [EStatisticField.ShapeLen]: number }[]
) => {
  const field = FormConfig.group.find((item) => item.id === 'material-bar')
    ?.fields[0] as IFormVChart;
  if (!field) return;
  const chartData = data
    .filter((item) => item.MATERIAL !== null)
    .map((item) => {
      return {
        name: item.MATERIAL,
        value: ((item[EStatisticField.ShapeLen] || 0) / 1000).toFixed(2)
      };
    });
  field.option = oneHistogram(chartData, 'km');
};
const refreshData = () => {
  getLengthTotal().then((data) => {
    const total: number = data.reduce((prev, cur) => {
      return (
        (cur &&
          cur[EStatisticField.ShapeLen] &&
          prev + cur[EStatisticField.ShapeLen]) ||
        0
      );
    }, 0);
    refreshDiameterBar({ data, total });
    // refreshDiameterBar(data || [])
  });
  refreshYearBar();
  refreshDiameterRing();
  getLengthTotal(['MATERIAL']).then((data) => {
    refreshMaterialBar(data);
  });
  getCountTotal();
};
watch(
  () => useAppStore().isDark,
  () => refreshData()
);
onBeforeMount(async () => {
  state.pipeLayerOption = await getPipeLineLayerOption(props.view);
  refreshData();
});
</script>

<style lang="scss" scoped>
.onemap-panel-wrapper {
  min-height: 610px;
  height: 100%;
}
</style>
