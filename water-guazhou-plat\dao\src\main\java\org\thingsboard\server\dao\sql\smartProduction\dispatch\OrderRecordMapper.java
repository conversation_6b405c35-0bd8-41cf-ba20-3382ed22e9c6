package org.thingsboard.server.dao.sql.smartProduction.dispatch;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.smartProduction.dispatch.OrderRecord;
import org.thingsboard.server.dao.model.sql.smartProduction.dispatch.OrderRecordStatus;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch.*;

import java.util.List;

@Mapper
public interface OrderRecordMapper extends BaseMapper<OrderRecord> {
    IPage<OrderRecord> findByPage(OrderRecordPageRequest request);

    boolean update(OrderRecord entity);

    boolean receive(OrderRecordReceiveRequest req);

    boolean reject(OrderRecordRejectRequest req);

    boolean reply(OrderRecordReplyRequest req);

    boolean execute(OrderRecordExecuteRequest req);

    int saveAll(List<OrderRecord> list);

    int changeStatusBatch(@Param("list") List<String> idList, @Param("status") OrderRecordStatus status);

    int sendBatch(@Param("list") List<String> idList, @Param("status") OrderRecordStatus status);

    boolean isStatus(@Param("id") String id, @Param("status") OrderRecordStatus status);
}
