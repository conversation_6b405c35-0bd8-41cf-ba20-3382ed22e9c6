import{d as G,r as F,c as s,u as m,j as x,a8 as M,v as J,o as O,ei as P,ej as j,a1 as q,g as u,h as k,F as c,q as r,n as _,cn as z,p as D,i as C,cs as K,an as W,G as $,b as n,ek as H,bK as R,I as X,el as Q,J as Y,K as Z,br as ee,C as ae}from"./index-r0dFAfgr.js";const te={key:0,class:"avatar-wrapper svg-container"},oe={key:1,class:"avatar-wrapper"},le=["src"],se={key:2,class:"avatar"},re=G({__name:"EnterpriseSetting",setup(ne){const h=F({menuSourse:[]}),I=(e,a)=>{var t;return(t=a.label)==null?void 0:t.includes(e)},V=s({"X-Authorization":"Bearer "+m().token}),N=s(x().actionUrl+"/file/api/upload/image"),g=s([]),ce=s(window.SITE_CONFIG.imgUrl+"api/fileRegistry/upload"),B=s({name:[{required:!0,message:"请输入企业名称",trigger:"blur"},{validator:(e,a,t)=>{a.trim()!==""?t():t(new Error("请输入有效字符 空格无效"))},trigger:"blur"}],imageUrl:[{required:!0,message:"请上传企业logo",trigger:"blur"}]}),o=s({name:"",logoUrl:"",app:"",firstScreen:""}),E=e=>{let a=!0;const t=e.size/1024/1024<5;return e.type==="image/jpeg"||e.type==="image/png"||e.type==="image/bmp"||e.type==="image/x-bmp"||e.type==="image/svg+xml"?a=!0:(a=!1,n.error("上传logo图片只能是 JPG,JPEG,PNG,BMP,SVG 格式!")),t||n.error("上传logo图片大小不能超过 5MB!"),a&&t},U=e=>e?e.toLowerCase().endsWith(".svg"):!1,d=s(""),L=M(()=>d.value?J("div",{innerHTML:d.value,class:"svg-content"}):null),y=async e=>{try{const a=await fetch(e);if(a.ok){const t=await a.text();if(t.includes("<svg"))return d.value=t,!0}return!1}catch(a){return console.error("Failed to load SVG:",a),!1}},T=async(e,a)=>{const t=a.type==="image/svg+xml"||typeof e=="string"&&e.toLowerCase().endsWith(".svg");o.value.logoUrl=e,t?(await y(e),n.success("SVG 图片已上传 请保存操作")):n.success("图片已上传 请保存操作"),console.log(e,a,"图片上传")},ue=()=>!0,b=s(!1),de=async(e,a)=>{b.value=!0;try{console.log(e,a);const t=e.data||{};g.value=[{name:t.fileName,url:t.fileAddress}],o.value.app=JSON.stringify(g.value)}catch{n.warning("上传失败")}b.value=!1},S=s(),f=s({}),A=()=>{S.value.validate(e=>{if(e)f.value.additionalInfo=o.value,H(f.value).then(async a=>{var t,l;n.success("修改成功"),m().InitTenantInfo(),x().ToggleLogo((l=(t=a.data)==null?void 0:t.additionalInfo)==null?void 0:l.logoUrl)}).catch(a=>console.log(a));else return n.warning("请按提示输入"),!1})};return O(async()=>{var e,a,t;try{const l=await P(((a=(e=m().user)==null?void 0:e.tenantId)==null?void 0:a.id)||"");f.value=l.data||{};const i=(t=l.data)==null?void 0:t.additionalInfo,p=typeof i=="string"?JSON.parse(i):i||{},v=p.app;o.value={...p},g.value=v?JSON.parse(v):[],o.value.logoUrl&&U(o.value.logoUrl)&&await y(o.value.logoUrl)}catch(l){console.error("Failed to load tenant info:",l)}j().then(l=>{h.menuSourse=q(l.data,{id:"id",label:"name",value:"url",children:"children"})||[]})}),(e,a)=>{const t=R,l=X,i=Q,p=Y,v=Z,pe=ee;return u(),k(v,{ref_key:"eForm",ref:S,rules:B.value,model:o.value,"label-width":"100px",class:"enterprise-form"},{default:c(()=>[r(l,{label:"LOGO",prop:"logoUrl"},{default:c(()=>[r(t,{class:"avatar-uploader",action:N.value,"show-file-list":!1,"before-upload":E,"on-success":T,headers:V.value},{default:c(()=>[o.value.logoUrl&&U(o.value.logoUrl)&&d.value?(u(),_("div",te,[(u(),k(z(L.value)))])):o.value.logoUrl?(u(),_("div",oe,[D("img",{src:o.value.logoUrl,class:"avatar",alt:"logo",style:{objectFit:"contain"}},null,8,le)])):(u(),_("div",se,[r(C(K),{icon:"ep:plus"})]))]),_:1},8,["action","headers"])]),_:1}),r(l,{label:"首屏页面",prop:"firstScreen"},{default:c(()=>[r(i,{modelValue:o.value.firstScreen,"onUpdate:modelValue":a[0]||(a[0]=w=>o.value.firstScreen=w),data:C(h).menuSourse,"default-expand-all":!0,filterable:!0,"check-strictly":!0,clearable:!0,props:{label:"label",children:"children",disabled:"disabled",isLeaf:"isLeaf",class:"class",value:"value"},"filter-node-method":I},null,8,["modelValue","data"])]),_:1}),W("",!0),r(l,{label:""},{default:c(()=>[r(p,{type:"primary",onClick:A},{default:c(()=>a[4]||(a[4]=[$(" 保存 ")])),_:1})]),_:1})]),_:1},8,["rules","model"])}}}),ge=ae(re,[["__scopeId","data-v-931210e4"]]);export{ge as default};
