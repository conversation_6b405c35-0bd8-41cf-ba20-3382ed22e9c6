package org.thingsboard.server.dao.model.sql.statistic;

import org.thingsboard.server.dao.util.imodel.response.Responsible;
import org.thingsboard.server.dao.util.imodel.response.model.ReturnHelper;

import java.util.Date;
import java.util.Map;

import static org.thingsboard.server.dao.util.TimeUtils.formatDateTime;

public class TimedStatisticLongWrapper implements Responsible, Statisticable {
    private final Date from;
    private final Date to;

    private final StatisticLong statisticLong;

    public TimedStatisticLongWrapper(StatisticLong statisticLong, Date from, Date to) {
        this.from = from;
        this.to = to;
        this.statisticLong = statisticLong;
    }

    public Date getFrom() {
        return from;
    }

    public Date getTo() {
        return to;
    }

    @Override
    public long getValue() {
        return statisticLong.getValue();
    }

    @Override
    public Object postProcess(ReturnHelper returnHelper, Object arg) {
        @SuppressWarnings("unchecked")
        Map<String, Object> result = (Map<String, Object>) statisticLong.postProcess(returnHelper, arg);
        result.remove("key");
        result.put("from", formatDateTime(from));
        result.put("to", formatDateTime(to));
        return result;
    }

}
