"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[7202],{64830:(e,t,r)=>{r.d(t,{Z:()=>o});var s=r(70586);class o{constructor(e=(e=>e.values().next().value)){this._peeker=e,this._items=new Set}get length(){return this._items.size}clear(){this._items.clear()}last(){if(0===this._items.size)return;let e;for(e of this._items);return e}peek(){if(0!==this._items.size)return this._peeker(this._items)}push(e){this.contains(e)||this._items.add(e)}contains(e){return this._items.has(e)}pop(){if(0===this.length)return;const e=this.peek();return this._items.delete((0,s.j0)(e)),e}popLast(){if(0===this.length)return;const e=this.last();return this._items.delete((0,s.j0)(e)),e}remove(e){this._items.delete(e)}filter(e){return this._items.forEach((t=>{e(t)||this._items.delete(t)})),this}}},80903:(e,t,r)=>{r.d(t,{Z:()=>l});var s=r(50758),o=r(92604),n=r(95330),i=r(64830),a=r(25045);class l{constructor(){this._inUseClients=new Array,this._clients=new Array,this._clientPromises=new Array,this._ongoingJobsQueue=new i.Z}destroy(){this.close()}get closed(){return!this._clients||!this._clients.length}open(e,t){return new Promise(((r,s)=>{let o=!0;const i=e=>{(0,n.k_)(t.signal),o&&(o=!1,e())};this._clients.length=e.length,this._clientPromises.length=e.length,this._inUseClients.length=e.length;for(let o=0;o<e.length;++o){const l=e[o];(0,n.y8)(l)?this._clientPromises[o]=l.then((e=>(this._clients[o]=new a.default(e,t,(()=>this._ongoingJobsQueue.pop()??null)),i(r),this._clients[o])),(()=>(i(s),null))):(this._clients[o]=new a.default(l,t,(()=>this._ongoingJobsQueue.pop()??null)),this._clientPromises[o]=Promise.resolve(this._clients[o]),i(r))}}))}broadcast(e,t,r){const s=new Array(this._clientPromises.length);for(let o=0;o<this._clientPromises.length;++o){const n=this._clientPromises[o];s[o]=n.then((s=>s?.invoke(e,t,r)))}return s}close(){let e;for(;e=this._ongoingJobsQueue.pop();)e.deferred.reject((0,n.zE)(`Worker closing, aborting job calling '${e.methodName}'`));for(const e of this._clientPromises)e.then((e=>e?.close()));this._clients.length=0,this._clientPromises.length=0}invoke(e,t,r){let s;Array.isArray(r)?(o.Z.getLogger("esri.core.workers.Connection").warn("invoke()","The transferList parameter is deprecated, use the options object instead"),s={transferList:r}):s=r;const i=(0,n.dD)();this._ongoingJobsQueue.push({methodName:e,data:t,invokeOptions:s,deferred:i});for(let e=0;e<this._clientPromises.length;e++){const t=this._clients[e];t?t.jobAdded():this._clientPromises[e].then((e=>e?.jobAdded()))}return i.promise}on(e,t){return Promise.all(this._clientPromises).then((()=>(0,s.AL)(this._clients.map((r=>r.on(e,t))))))}openPorts(){return new Promise((e=>{const t=new Array(this._clientPromises.length);let r=t.length;for(let s=0;s<this._clientPromises.length;++s)this._clientPromises[s].then((o=>{o&&(t[s]=o.openPort()),0==--r&&e(t)}))}))}get test(){return{numClients:this._clients.length}}}},78346:(e,t,r)=>{r.d(t,{bA:()=>q});var s=r(20102),o=r(80442),n=r(95330),i=r(80903),a=r(25045),l=r(40330),u=r(92604),p=r(70586),d=r(94362),c=r(99880),h=r(68773),y=(r(2587),r(17452));const f={};function m(e){const t={async:e.async,isDebug:e.isDebug,locale:e.locale,baseUrl:e.baseUrl,has:{...e.has},map:{...e.map},packages:e.packages&&e.packages.concat()||[],paths:{...e.paths}};return e.hasOwnProperty("async")||(t.async=!0),e.hasOwnProperty("isDebug")||(t.isDebug=!1),e.baseUrl||(t.baseUrl=f.baseUrl),t}var g=r(41213);class b{constructor(){const e=document.createDocumentFragment();["addEventListener","dispatchEvent","removeEventListener"].forEach((t=>{this[t]=(...r)=>e[t](...r)}))}}class w{constructor(){this._dispatcher=new b,this._workerPostMessage({type:d.Cs.HANDSHAKE})}terminate(){}get onmessage(){return this._onmessageHandler}set onmessage(e){this._onmessageHandler&&this.removeEventListener("message",this._onmessageHandler),this._onmessageHandler=e,e&&this.addEventListener("message",e)}get onmessageerror(){return this._onmessageerrorHandler}set onmessageerror(e){this._onmessageerrorHandler&&this.removeEventListener("messageerror",this._onmessageerrorHandler),this._onmessageerrorHandler=e,e&&this.addEventListener("messageerror",e)}get onerror(){return this._onerrorHandler}set onerror(e){this._onerrorHandler&&this.removeEventListener("error",this._onerrorHandler),this._onerrorHandler=e,e&&this.addEventListener("error",e)}postMessage(e){(0,g.Y)((()=>{this._workerMessageHandler(new MessageEvent("message",{data:e}))}))}dispatchEvent(e){return this._dispatcher.dispatchEvent(e)}addEventListener(e,t,r){this._dispatcher.addEventListener(e,t,r)}removeEventListener(e,t,r){this._dispatcher.removeEventListener(e,t,r)}_workerPostMessage(e){(0,g.Y)((()=>{this.dispatchEvent(new MessageEvent("message",{data:e}))}))}async _workerMessageHandler(e){const t=(0,d.QM)(e);if(t&&t.type===d.Cs.OPEN){const{modulePath:e,jobId:r}=t;let s=await a.default.loadWorker(e);s||(s=await import(e));const o=a.default.connect(s);this._workerPostMessage({type:d.Cs.OPENED,jobId:r,data:o})}}}var _=r(70171),v=r(17202);const S=u.Z.getLogger("esri.core.workers.workerFactory"),{HANDSHAKE:C}=d.Cs;let k,E;const P="Failed to create Worker. Fallback to execute module in main thread";async function F(e){return new Promise((t=>{function r(o){const n=(0,d.QM)(o);n&&n.type===C&&(e.removeEventListener("message",r),e.removeEventListener("error",s),t(e))}function s(t){t.preventDefault(),e.removeEventListener("message",r),e.removeEventListener("error",s),S.warn("Failed to create Worker. Fallback to execute module in main thread",t),(e=new w).addEventListener("message",r),e.addEventListener("error",s)}e.addEventListener("message",r),e.addEventListener("error",s)}))}function O(){let e;if(null!=h.Z.default){const t={...h.Z};delete t.default,e=JSON.parse(JSON.stringify(t))}else e=JSON.parse(JSON.stringify(h.Z));e.assetsPath=(0,y.hF)(e.assetsPath),e.defaultAssetsPath=e.defaultAssetsPath?(0,y.hF)(e.defaultAssetsPath):void 0,e.request.interceptors=[],e.log.interceptors=[],e.locale=(0,_.Kd)(),e.has={"esri-csp-restrictions":(0,o.Z)("esri-csp-restrictions"),"esri-2d-debug":!1,"esri-2d-update-debug":(0,o.Z)("esri-2d-update-debug"),"featurelayer-pbf":(0,o.Z)("featurelayer-pbf"),"featurelayer-simplify-thresholds":(0,o.Z)("featurelayer-simplify-thresholds"),"featurelayer-simplify-payload-size-factors":(0,o.Z)("featurelayer-simplify-payload-size-factors"),"featurelayer-simplify-mobile-factor":(0,o.Z)("featurelayer-simplify-mobile-factor"),"esri-atomics":(0,o.Z)("esri-atomics"),"esri-shared-array-buffer":(0,o.Z)("esri-shared-array-buffer"),"esri-tiles-debug":(0,o.Z)("esri-tiles-debug"),"esri-workers-arraybuffer-transfer":(0,o.Z)("esri-workers-arraybuffer-transfer"),"feature-polyline-generalization-factor":(0,o.Z)("feature-polyline-generalization-factor"),"host-webworker":1,"polylabel-placement-enabled":(0,o.Z)("polylabel-placement-enabled")},e.workers.loaderUrl&&(e.workers.loaderUrl=(0,y.hF)(e.workers.loaderUrl)),e.workers.workerPath?e.workers.workerPath=(0,y.hF)(e.workers.workerPath):e.workers.workerPath=(0,y.hF)((0,c.V)("esri/core/workers/RemoteClient.js")),e.workers.useDynamicImport=!1;const t=h.Z.workers.loaderConfig,r=m({baseUrl:t?.baseUrl,locale:(0,_.Kd)(),has:{"csp-restrictions":1,"dojo-test-sniff":0,"host-webworker":1,...t?.has},map:{...t?.map},paths:{...t?.paths},packages:t?.packages||[]}),s={version:l.i8,buildDate:v.r,revision:v.$};return JSON.stringify({esriConfig:e,loaderConfig:r,kernelInfo:s})}let x=0;const{ABORT:j,INVOKE:R,OPEN:I,OPENED:N,RESPONSE:Z}=d.Cs;class A{static async create(e){const t=await async function(){if(!(0,o.Z)("esri-workers")||((0,o.Z)("mozilla"),0))return F(new w);if(!k&&!E)try{const e='let globalId=0;const outgoing=new Map,configuration=JSON.parse("{CONFIGURATION}");self.esriConfig=configuration.esriConfig;const workerPath=self.esriConfig.workers.workerPath,HANDSHAKE=0,OPEN=1,OPENED=2,RESPONSE=3,INVOKE=4,ABORT=5;function createAbortError(){const e=new Error("Aborted");return e.name="AbortError",e}function receiveMessage(e){return e&&e.data?"string"==typeof e.data?JSON.parse(e.data):e.data:null}function invokeStaticMessage(e,o,r){const t=r&&r.signal,n=globalId++;return new Promise(((r,i)=>{if(t){if(t.aborted)return i(createAbortError());t.addEventListener("abort",(()=>{outgoing.get(n)&&(outgoing.delete(n),self.postMessage({type:5,jobId:n}),i(createAbortError()))}))}outgoing.set(n,{resolve:r,reject:i}),self.postMessage({type:4,jobId:n,methodName:e,abortable:null!=t,data:o})}))}let workerRevisionChecked=!1;function checkWorkerRevision(e){if(!workerRevisionChecked&&e.kernelInfo){workerRevisionChecked=!0;const{revision:o,version:r}=configuration.kernelInfo,{revision:t,version:n}=e.kernelInfo;esriConfig.assetsPath!==esriConfig.defaultAssetsPath&&o!==t&&console.warn(`Version mismatch detected between ArcGIS API for JavaScript modules and assets. For more information visit https://bit.ly/3QnsuSo.\\nModules version: ${r}\\nAssets version: ${n}`)}}function messageHandler(e){const o=receiveMessage(e);if(!o)return;const r=o.jobId;switch(o.type){case 1:let n;function t(e){const o=n.connect(e);self.postMessage({type:2,jobId:r,data:o},[o])}"function"==typeof define&&define.amd?require([workerPath],(e=>{n=e.default||e,checkWorkerRevision(n),n.loadWorker(o.modulePath).then((e=>e||new Promise((e=>{require([o.modulePath],e)})))).then(t)})):"System"in self&&"function"==typeof System.import?System.import(workerPath).then((e=>(n=e.default,checkWorkerRevision(n),n.loadWorker(o.modulePath)))).then((e=>e||System.import(o.modulePath))).then(t):esriConfig.workers.useDynamicImport?import(workerPath).then((e=>{n=e.default||e,checkWorkerRevision(n),n.loadWorker(o.modulePath).then((e=>e||import(o.modulePath))).then(t)})):(self.RemoteClient||importScripts(workerPath),n=self.RemoteClient.default||self.RemoteClient,checkWorkerRevision(n),n.loadWorker(o.modulePath).then(t));break;case 3:if(outgoing.has(r)){const i=outgoing.get(r);outgoing.delete(r),o.error?i.reject(JSON.parse(o.error)):i.resolve(o.data)}}}self.dojoConfig=configuration.loaderConfig,esriConfig.workers.loaderUrl&&(self.importScripts(esriConfig.workers.loaderUrl),"function"==typeof require&&"function"==typeof require.config&&require.config(configuration.loaderConfig)),self.addEventListener("message",messageHandler),self.postMessage({type:0});'.split('"{CONFIGURATION}"').join(`'${O()}'`);k=URL.createObjectURL(new Blob([e],{type:"text/javascript"}))}catch(e){E=e||{}}let e;if(k)try{e=new Worker(k,{name:"esri-worker-"+x++})}catch(t){S.warn(P,E),e=new w}else S.warn(P,E),e=new w;return F(e)}();return new A(t,e)}constructor(e,t){this._outJobs=new Map,this._inJobs=new Map,this.worker=e,this.id=t,e.addEventListener("message",this._onMessage.bind(this)),e.addEventListener("error",(e=>{e.preventDefault(),u.Z.getLogger("esri.core.workers.WorkerOwner").error(e)}))}terminate(){this.worker.terminate()}async open(e,t={}){const{signal:r}=t,s=(0,d.jt)();return new Promise(((t,o)=>{const i={resolve:t,reject:o,abortHandle:(0,n.$F)(r,(()=>{this._outJobs.delete(s),this._post({type:j,jobId:s})}))};this._outJobs.set(s,i),this._post({type:I,jobId:s,modulePath:e})}))}_onMessage(e){const t=(0,d.QM)(e);if(t)switch(t.type){case N:this._onOpenedMessage(t);break;case Z:this._onResponseMessage(t);break;case j:this._onAbortMessage(t);break;case R:this._onInvokeMessage(t)}}_onAbortMessage(e){const t=this._inJobs,r=e.jobId,s=t.get(r);s&&(s.controller&&s.controller.abort(),t.delete(r))}_onInvokeMessage(e){const{methodName:t,jobId:r,data:s,abortable:o}=e,i=o?new AbortController:null,a=this._inJobs,u=l.Nv[t];let p;try{if("function"!=typeof u)throw new TypeError(`${t} is not a function`);p=u.call(null,s,{signal:i?i.signal:null})}catch(e){return void this._post({type:Z,jobId:r,error:(0,d.AB)(e)})}(0,n.y8)(p)?(a.set(r,{controller:i,promise:p}),p.then((e=>{a.has(r)&&(a.delete(r),this._post({type:Z,jobId:r},e))}),(e=>{a.has(r)&&(a.delete(r),e||(e={message:"Error encountered at method"+t}),(0,n.D_)(e)||this._post({type:Z,jobId:r,error:(0,d.AB)(e||{message:`Error encountered at method ${t}`})}))}))):this._post({type:Z,jobId:r},p)}_onOpenedMessage(e){const{jobId:t,data:r}=e,s=this._outJobs.get(t);s&&(this._outJobs.delete(t),(0,p.hw)(s.abortHandle),s.resolve(r))}_onResponseMessage(e){const{jobId:t,error:r,data:o}=e,n=this._outJobs.get(t);n&&(this._outJobs.delete(t),(0,p.hw)(n.abortHandle),r?n.reject(s.Z.fromJSON(JSON.parse(r))):n.resolve(o))}_post(e,t,r){return(0,d.oi)(this.worker,e,t,r)}}let J=(0,o.Z)("esri-workers-debug")?1:(0,o.Z)("esri-mobile")?Math.min(navigator.hardwareConcurrency-1,3):(0,o.Z)("host-browser")?navigator.hardwareConcurrency-1:0;J||(J=(0,o.Z)("safari")&&(0,o.Z)("mac")?7:2);let T=0;const L=[];async function M(e,t){const r=new i.Z;return await r.open(e,t),r}async function q(e,t={}){if("string"!=typeof e)throw new s.Z("workers:undefined-module","modulePath is missing");let r=t.strategy||"distributed";if((0,o.Z)("host-webworker")&&!(0,o.Z)("esri-workers")&&(r="local"),"local"===r){let r=await a.default.loadWorker(e);r||(r=await import(e)),(0,n.k_)(t.signal);const s=t.client||r;return M([a.default.connect(r)],{...t,client:s})}if(await async function(){if(U)return U;D=new AbortController;const e=[];for(let t=0;t<J;t++){const r=A.create(t).then((e=>(L[t]=e,e)));e.push(r)}return U=Promise.all(e),U}(),(0,n.k_)(t.signal),"dedicated"===r){const r=T++%J;return M([await L[r].open(e,t)],t)}if(t.maxNumWorkers&&t.maxNumWorkers>0){const r=Math.min(t.maxNumWorkers,J);if(r<J){const s=new Array(r);for(let o=0;o<r;++o){const r=T++%J;s[o]=L[r].open(e,t)}return M(s,t)}}return M(L.map((r=>r.open(e,t))),t)}let D,U=null},2587:(e,t,r)=>{r(90344),r(18848),r(940),r(70171);var s=r(94443),o=r(3172),n=r(20102),i=r(70586);async function a(e){if((0,i.pC)(l.fetchBundleAsset))return l.fetchBundleAsset(e);const t=await(0,o.default)(e,{responseType:"text"});return JSON.parse(t.data)}const l={};var u,p=r(99880);(0,s.tz)((u={pattern:"esri/",location:p.V},new class{constructor({base:e="",pattern:t,location:r=new URL(window.location.href)}){let s;s="string"==typeof r?e=>new URL(e,new URL(r,window.location.href)).href:r instanceof URL?e=>new URL(e,r).href:r,this.pattern="string"==typeof t?new RegExp(`^${t}`):t,this.getAssetUrl=s,e=e?e.endsWith("/")?e:e+"/":"",this.matcher=new RegExp(`^${e}(?:(.*)/)?(.*)$`)}fetchMessageBundle(e,t){return async function(e,t,r,o){const i=t.exec(r);if(!i)throw new n.Z("esri-intl:invalid-bundle",`Bundle id "${r}" is not compatible with the pattern "${t}"`);const l=i[1]?`${i[1]}/`:"",u=i[2],p=(0,s.Su)(o),d=`${l}${u}.json`,c=p?`${l}${u}_${p}.json`:d;let h;try{h=await a(e(c))}catch(t){if(c===d)throw new n.Z("intl:unknown-bundle",`Bundle "${r}" cannot be loaded`,{error:t});try{h=await a(e(d))}catch(e){throw new n.Z("intl:unknown-bundle",`Bundle "${r}" cannot be loaded`,{error:e})}}return h}(this.getAssetUrl,this.matcher,e,t)}}(u)))},940:(e,t,r)=>{r.d(t,{n:()=>u});var s=r(92604),o=r(78286),n=r(19153),i=r(90344),a=r(18848);const l=s.Z.getLogger("esri.intl.substitute");function u(e,t,r={}){const{format:s={}}=r;return(0,n.gx)(e,(e=>function(e,t,r){let s,n;const i=e.indexOf(":");if(-1===i?s=e.trim():(s=e.slice(0,i).trim(),n=e.slice(i+1).trim()),!s)return"";const a=(0,o.hS)(s,t);if(null==a)return"";const l=(n?r?.[n]:null)??r?.[s];return l?p(a,l):n?d(a,n):c(a)}(e,t,s)))}function p(e,t){switch(t.type){case"date":return(0,i.p6)(e,t.intlOptions);case"number":return(0,a.uf)(e,t.intlOptions);default:return l.warn("missing format descriptor for key {key}"),c(e)}}function d(e,t){switch(t.toLowerCase()){case"dateformat":return(0,i.p6)(e);case"numberformat":return(0,a.uf)(e);default:return l.warn(`inline format is unsupported since 4.12: ${t}`),/^(dateformat|datestring)/i.test(t)?(0,i.p6)(e):/^numberformat/i.test(t)?(0,a.uf)(e):c(e)}}function c(e){switch(typeof e){case"string":return e;case"number":return(0,a.uf)(e);case"boolean":return""+e;default:return e instanceof Date?(0,i.p6)(e):""}}},23477:(e,t,r)=>{r.r(t),r.d(t,{default:()=>B});var s=r(43697),o=(r(66577),r(51773)),n=(r(16050),r(12501),r(28756),r(92271),r(72529),r(5499),r(84382),r(81571),r(91423),r(32400)),i=r(70586),a=r(16453),l=r(95330),u=r(17452),p=r(5600),d=(r(75215),r(67676),r(52011)),c=r(86973),h=r(87085),y=r(20102),f=r(80442),m=r(83379),g=r(92604),b=r(78346),w=r(25278),_=r(74889),v=r(6570),S=r(38913);const C="esri.layers.graphics.sources.GeoJSONSource",k=g.Z.getLogger(C);let E=class extends m.Z{constructor(){super(...arguments),this.type="geojson",this.refresh=(0,l.Ds)((async e=>{await this.load();const{extent:t,timeExtent:r}=await this._connection.invoke("refresh",e);return this.sourceJSON.extent=t,r&&(this.sourceJSON.timeInfo.timeExtent=[r.start,r.end]),{dataChanged:!0,updates:{extent:this.sourceJSON.extent,timeInfo:this.sourceJSON.timeInfo}}}))}load(e){const t=(0,i.pC)(e)?e.signal:null;return this.addResolvingPromise(this._startWorker(t)),Promise.resolve(this)}destroy(){this._connection?.close(),this._connection=null}applyEdits(e){return this.load().then((()=>this._applyEdits(e)))}openPorts(){return this.load().then((()=>this._connection.openPorts()))}queryFeatures(e,t={}){return this.load(t).then((()=>this._connection.invoke("queryFeatures",e?e.toJSON():null,t))).then((e=>_.Z.fromJSON(e)))}queryFeaturesJSON(e,t={}){return this.load(t).then((()=>this._connection.invoke("queryFeatures",e?e.toJSON():null,t)))}queryFeatureCount(e,t={}){return this.load(t).then((()=>this._connection.invoke("queryFeatureCount",e?e.toJSON():null,t)))}queryObjectIds(e,t={}){return this.load(t).then((()=>this._connection.invoke("queryObjectIds",e?e.toJSON():null,t)))}queryExtent(e,t={}){return this.load(t).then((()=>this._connection.invoke("queryExtent",e?e.toJSON():null,t))).then((e=>({count:e.count,extent:v.Z.fromJSON(e.extent)})))}querySnapping(e,t={}){return this.load(t).then((()=>this._connection.invoke("querySnapping",e,t)))}_applyEdits(e){if(!this._connection)throw new y.Z("geojson-layer-source:edit-failure","Memory source not loaded");const t=this.layer.objectIdField,r=[],s=[],o=[];if(e.addFeatures)for(const t of e.addFeatures)r.push(this._serializeFeature(t));if(e.deleteFeatures)for(const r of e.deleteFeatures)"objectId"in r&&null!=r.objectId?s.push(r.objectId):"attributes"in r&&null!=r.attributes[t]&&s.push(r.attributes[t]);if(e.updateFeatures)for(const t of e.updateFeatures)o.push(this._serializeFeature(t));return this._connection.invoke("applyEdits",{adds:r,updates:o,deletes:s}).then((({extent:e,timeExtent:t,featureEditResults:r})=>(this.sourceJSON.extent=e,t&&(this.sourceJSON.timeInfo.timeExtent=[t.start,t.end]),this._createEditsResult(r))))}_createEditsResult(e){return{addFeatureResults:e.addResults?e.addResults.map(this._createFeatureEditResult,this):[],updateFeatureResults:e.updateResults?e.updateResults.map(this._createFeatureEditResult,this):[],deleteFeatureResults:e.deleteResults?e.deleteResults.map(this._createFeatureEditResult,this):[],addAttachmentResults:[],updateAttachmentResults:[],deleteAttachmentResults:[]}}_createFeatureEditResult(e){const t=!0===e.success?null:e.error||{code:void 0,description:void 0};return{objectId:e.objectId,globalId:e.globalId,error:t?new y.Z("geojson-layer-source:edit-failure",t.description,{code:t.code}):null}}_serializeFeature(e){const{attributes:t}=e,r=this._geometryForSerialization(e);return r?{geometry:r.toJSON(),attributes:t}:{attributes:t}}_geometryForSerialization(e){const{geometry:t}=e;return(0,i.Wi)(t)?null:"mesh"===t.type||"extent"===t.type?S.Z.fromExtent(t.extent):t}async _startWorker(e){this._connection=await(0,b.bA)("GeoJSONSourceWorker",{strategy:(0,f.Z)("feature-layers-workers")?"dedicated":"local",signal:e});const{fields:t,spatialReference:r,hasZ:s,geometryType:o,objectIdField:n,url:i,timeInfo:a,customParameters:l}=this.layer,u="defaults"===this.layer.originOf("spatialReference"),p={url:i,customParameters:l,fields:t&&t.map((e=>e.toJSON())),geometryType:c.M.toJSON(o),hasZ:s,objectIdField:n,timeInfo:a?a.toJSON():null,spatialReference:u?null:r&&r.toJSON()},d=await this._connection.invoke("load",p,{signal:e});for(const e of d.warnings)k.warn(e.message,{layer:this.layer,warning:e});d.featureErrors.length&&k.warn(`Encountered ${d.featureErrors.length} validation errors while loading features`,d.featureErrors),this.sourceJSON=d.layerDefinition,this.capabilities=(0,w.MS)(this.sourceJSON.hasZ,!0)}};(0,s._)([(0,p.Cb)()],E.prototype,"capabilities",void 0),(0,s._)([(0,p.Cb)()],E.prototype,"type",void 0),(0,s._)([(0,p.Cb)({constructOnly:!0})],E.prototype,"layer",void 0),(0,s._)([(0,p.Cb)()],E.prototype,"sourceJSON",void 0),E=(0,s._)([(0,d.j)(C)],E);var P=r(71612),F=r(17017),O=r(69637),x=r(6404),j=r(38009),R=r(68825),I=r(16859),N=r(34760),Z=r(72965),A=r(28294),J=r(21506),T=r(70082),L=r(16451),M=r(1231),q=r(53518),D=r(35671),U=r(54306),z=r(30707),G=r(14165),H=r(32163),Q=r(82971);const $=(0,q.v)();let W=class extends((0,R.c)((0,F.N)((0,x.M)((0,O.b)((0,P.h)((0,A.n)((0,Z.M)((0,N.Q)((0,j.q)((0,I.I)((0,a.R)(h.Z)))))))))))){constructor(e){super(e),this.copyright=null,this.definitionExpression=null,this.displayField=null,this.editingEnabled=!1,this.elevationInfo=null,this.fields=null,this.fieldsIndex=null,this.fullExtent=null,this.geometryType=null,this.hasZ=void 0,this.labelsVisible=!0,this.labelingInfo=null,this.legendEnabled=!0,this.objectIdField=null,this.operationalLayerType="GeoJSON",this.popupEnabled=!0,this.popupTemplate=null,this.screenSizePerspectiveEnabled=!0,this.source=new E({layer:this}),this.spatialReference=Q.Z.WGS84,this.templates=null,this.title="GeoJSON",this.type="geojson",this.typeIdField=null,this.types=null}destroy(){this.source?.destroy()}load(e){const t=this.loadFromPortal({supportedTypes:["GeoJson"],supportsData:!1},e).catch(l.r9).then((()=>this.source.load(e))).then((()=>{this.read(this.source.sourceJSON,{origin:"service",url:this.parsedUrl}),this.revert(["objectIdField","fields","timeInfo"],"service"),(0,D.YN)(this.renderer,this.fieldsIndex),(0,D.UF)(this.timeInfo,this.fieldsIndex)}));return this.addResolvingPromise(t),Promise.resolve(this)}get capabilities(){return this.source?this.source.capabilities:null}get createQueryVersion(){return this.commitProperty("definitionExpression"),this.commitProperty("timeExtent"),this.commitProperty("timeOffset"),this.commitProperty("geometryType"),this.commitProperty("capabilities"),(this._get("createQueryVersion")||0)+1}get defaultPopupTemplate(){return this.createPopupTemplate()}get isTable(){return this.loaded&&null==this.geometryType}get parsedUrl(){return this.url?(0,u.mN)(this.url):null}set renderer(e){(0,D.YN)(e,this.fieldsIndex),this._set("renderer",e)}set url(e){if(!e)return void this._set("url",e);const t=(0,u.mN)(e);this._set("url",t.path),t.query&&(this.customParameters={...this.customParameters,...t.query})}async applyEdits(e,t){const s=await r.e(7269).then(r.bind(r,87269));await this.load();const o=await s.applyEdits(this,this.source,e,t);return this.read({extent:this.source.sourceJSON.extent,timeInfo:this.source.sourceJSON.timeInfo},{origin:"service",ignoreDefaults:!0}),o}on(e,t){return super.on(e,t)}createPopupTemplate(e){return(0,H.eZ)(this,e)}createQuery(){const e=new G.Z,t=this.get("capabilities.data");e.returnGeometry=!0,t&&t.supportsZ&&(e.returnZ=!0),e.outFields=["*"],e.where=this.definitionExpression||"1=1";const{timeOffset:r,timeExtent:s}=this;return e.timeExtent=null!=r&&null!=s?s.offset(-r.value,r.unit):s||null,e}getFieldDomain(e,t){let r,s=!1;const o=t&&t.feature,n=o&&o.attributes,i=this.typeIdField&&n&&n[this.typeIdField];return null!=i&&this.types&&(s=this.types.some((t=>t.id==i&&(r=t.domains&&t.domains[e],r&&"inherited"===r.type&&(r=this._getLayerDomain(e)),!0)))),s||r||(r=this._getLayerDomain(e)),r}getField(e){return this.fieldsIndex.get(e)}queryFeatures(e,t){return this.load().then((()=>this.source.queryFeatures(G.Z.from(e)||this.createQuery(),t))).then((e=>{if(e?.features)for(const t of e.features)t.layer=t.sourceLayer=this;return e}))}queryObjectIds(e,t){return this.load().then((()=>this.source.queryObjectIds(G.Z.from(e)||this.createQuery(),t)))}queryFeatureCount(e,t){return this.load().then((()=>this.source.queryFeatureCount(G.Z.from(e)||this.createQuery(),t)))}queryExtent(e,t){return this.load().then((()=>this.source.queryExtent(G.Z.from(e)||this.createQuery(),t)))}async hasDataChanged(){try{const{dataChanged:e,updates:t}=await this.source.refresh(this.customParameters);return(0,i.pC)(t)&&this.read(t,{origin:"service",url:this.parsedUrl,ignoreDefaults:!0}),e}catch{}return!1}_getLayerDomain(e){if(!this.fields)return null;let t=null;return this.fields.some((r=>(r.name===e&&(t=r.domain),!!t))),t}};(0,s._)([(0,p.Cb)({readOnly:!0,json:{read:!1,write:!1}})],W.prototype,"capabilities",null),(0,s._)([(0,p.Cb)({type:String})],W.prototype,"copyright",void 0),(0,s._)([(0,p.Cb)({readOnly:!0})],W.prototype,"createQueryVersion",null),(0,s._)([(0,p.Cb)({readOnly:!0})],W.prototype,"defaultPopupTemplate",null),(0,s._)([(0,p.Cb)({type:String,json:{name:"layerDefinition.definitionExpression",write:{enabled:!0,allowNull:!0}}})],W.prototype,"definitionExpression",void 0),(0,s._)([(0,p.Cb)({type:String})],W.prototype,"displayField",void 0),(0,s._)([(0,p.Cb)({type:Boolean})],W.prototype,"editingEnabled",void 0),(0,s._)([(0,p.Cb)(J.PV)],W.prototype,"elevationInfo",void 0),(0,s._)([(0,p.Cb)({type:[M.Z],json:{name:"layerDefinition.fields",write:{ignoreOrigin:!0,isRequired:!0},origins:{service:{name:"fields"}}}})],W.prototype,"fields",void 0),(0,s._)([(0,p.Cb)($.fieldsIndex)],W.prototype,"fieldsIndex",void 0),(0,s._)([(0,p.Cb)({type:v.Z,json:{name:"extent"}})],W.prototype,"fullExtent",void 0),(0,s._)([(0,p.Cb)({type:["point","polygon","polyline","multipoint"],json:{read:{reader:c.M.read}}})],W.prototype,"geometryType",void 0),(0,s._)([(0,p.Cb)({type:Boolean})],W.prototype,"hasZ",void 0),(0,s._)([(0,p.Cb)(J.id)],W.prototype,"id",void 0),(0,s._)([(0,p.Cb)({type:Boolean,readOnly:!0})],W.prototype,"isTable",null),(0,s._)([(0,p.Cb)(J.iR)],W.prototype,"labelsVisible",void 0),(0,s._)([(0,p.Cb)({type:[U.Z],json:{name:"layerDefinition.drawingInfo.labelingInfo",read:{reader:z.r},write:!0}})],W.prototype,"labelingInfo",void 0),(0,s._)([(0,p.Cb)(J.rn)],W.prototype,"legendEnabled",void 0),(0,s._)([(0,p.Cb)({type:["show","hide"]})],W.prototype,"listMode",void 0),(0,s._)([(0,p.Cb)({type:String,json:{name:"layerDefinition.objectIdField",write:{ignoreOrigin:!0,isRequired:!0},origins:{service:{name:"objectIdField"}}}})],W.prototype,"objectIdField",void 0),(0,s._)([(0,p.Cb)(J.Oh)],W.prototype,"opacity",void 0),(0,s._)([(0,p.Cb)({type:["GeoJSON"]})],W.prototype,"operationalLayerType",void 0),(0,s._)([(0,p.Cb)({readOnly:!0})],W.prototype,"parsedUrl",null),(0,s._)([(0,p.Cb)(J.C_)],W.prototype,"popupEnabled",void 0),(0,s._)([(0,p.Cb)({type:o.Z,json:{name:"popupInfo",write:!0}})],W.prototype,"popupTemplate",void 0),(0,s._)([(0,p.Cb)({types:n.A,json:{name:"layerDefinition.drawingInfo.renderer",write:!0,origins:{service:{name:"drawingInfo.renderer"},"web-scene":{types:n.o}}}})],W.prototype,"renderer",null),(0,s._)([(0,p.Cb)(J.YI)],W.prototype,"screenSizePerspectiveEnabled",void 0),(0,s._)([(0,p.Cb)({readOnly:!0})],W.prototype,"source",void 0),(0,s._)([(0,p.Cb)({type:Q.Z})],W.prototype,"spatialReference",void 0),(0,s._)([(0,p.Cb)({type:[T.Z]})],W.prototype,"templates",void 0),(0,s._)([(0,p.Cb)()],W.prototype,"title",void 0),(0,s._)([(0,p.Cb)({json:{read:!1},readOnly:!0})],W.prototype,"type",void 0),(0,s._)([(0,p.Cb)({type:String,readOnly:!0})],W.prototype,"typeIdField",void 0),(0,s._)([(0,p.Cb)({type:[L.Z]})],W.prototype,"types",void 0),(0,s._)([(0,p.Cb)(J.HQ)],W.prototype,"url",null),W=(0,s._)([(0,d.j)("esri.layers.GeoJSONLayer")],W);const B=W},61159:(e,t,r)=>{r.d(t,{g:()=>s});const s={supportsStatistics:!0,supportsPercentileStatistics:!0,supportsSpatialAggregationStatistics:!1,supportedSpatialAggregationStatistics:{envelope:!1,centroid:!1,convexHull:!1},supportsCentroid:!0,supportsCacheHint:!1,supportsDistance:!0,supportsDistinct:!0,supportsExtent:!0,supportsGeometryProperties:!1,supportsHavingClause:!0,supportsOrderBy:!0,supportsPagination:!0,supportsQuantization:!0,supportsQuantizationEditMode:!1,supportsQueryGeometry:!0,supportsResultType:!1,supportsSqlExpression:!0,supportsMaxRecordCountFactor:!1,supportsStandardizedQueriesOnly:!0,supportsTopFeaturesQuery:!1,supportsQueryByOthers:!0,supportsHistoricMoment:!1,supportsFormatPBF:!1,supportsDisjointSpatialRelationship:!0,supportsDefaultSpatialReference:!1,supportsFullTextSearch:!1,supportsCompactGeometry:!1,maxRecordCountFactor:void 0,maxRecordCount:void 0,standardMaxRecordCount:void 0,tileMaxRecordCount:void 0}},25278:(e,t,r)=>{r.d(t,{Dm:()=>p,Hq:()=>d,MS:()=>c,bU:()=>a});var s=r(80442),o=r(22974),n=r(61159),i=r(58333);function a(e){return{renderer:{type:"simple",symbol:"esriGeometryPoint"===e||"esriGeometryMultipoint"===e?i.I4:"esriGeometryPolyline"===e?i.ET:i.lF}}}const l=/^[_$a-zA-Z][_$a-zA-Z0-9]*$/;let u=1;function p(e,t){if((0,s.Z)("esri-csp-restrictions"))return()=>({[t]:null,...e});try{let r=`this.${t} = null;`;for(const t in e)r+=`this${l.test(t)?`.${t}`:`["${t}"]`} = ${JSON.stringify(e[t])};`;const s=new Function(`\n      return class AttributesClass$${u++} {\n        constructor() {\n          ${r};\n        }\n      }\n    `)();return()=>new s}catch(r){return()=>({[t]:null,...e})}}function d(e={}){return[{name:"New Feature",description:"",prototype:{attributes:(0,o.d9)(e)}}]}function c(e,t){return{analytics:{supportsCacheHint:!1},attachment:null,data:{isVersioned:!1,supportsAttachment:!1,supportsM:!1,supportsZ:e},metadata:{supportsAdvancedFieldProperties:!1},operations:{supportsCalculate:!1,supportsTruncate:!1,supportsValidateSql:!1,supportsAdd:t,supportsDelete:t,supportsEditing:t,supportsChangeTracking:!1,supportsQuery:!0,supportsQueryAnalytics:!1,supportsQueryAttachments:!1,supportsQueryTopFeatures:!1,supportsResizeAttachments:!1,supportsSync:!1,supportsUpdate:t,supportsExceedsLimitStatistics:!0},query:n.g,queryRelated:{supportsCount:!0,supportsOrderBy:!0,supportsPagination:!0,supportsCacheHint:!1},queryTopFeatures:{supportsCacheHint:!1},editing:{supportsGeometryUpdate:t,supportsGlobalId:!1,supportsReturnServiceEditsInSourceSpatialReference:!1,supportsRollbackOnFailure:!1,supportsUpdateWithoutM:!1,supportsUploadWithItemId:!1,supportsDeleteByAnonymous:!1,supportsDeleteByOthers:!1,supportsUpdateByAnonymous:!1,supportsUpdateByOthers:!1}}}},68825:(e,t,r)=>{r.d(t,{c:()=>f});var s,o=r(43697),n=r(78286),i=r(5600),a=(r(75215),r(67676),r(52011)),l=r(35454),u=r(96674);const p=new l.X({asc:"ascending",desc:"descending"});let d=s=class extends u.wq{constructor(e){super(e),this.field=null,this.valueExpression=null,this.order="ascending"}clone(){return new s({field:this.field,valueExpression:this.valueExpression,order:this.order})}};(0,o._)([(0,i.Cb)({type:String,json:{write:!0}})],d.prototype,"field",void 0),(0,o._)([(0,i.Cb)({type:String,json:{write:!0}})],d.prototype,"valueExpression",void 0),(0,o._)([(0,i.Cb)({type:p.apiValues,json:{read:p.read,write:p.write}})],d.prototype,"order",void 0),d=s=(0,o._)([(0,a.j)("esri.layers.support.OrderByInfo")],d);const c=d;function h(e,t,r){if(!e)return null;const s=e.find((e=>!!e.field));if(!s)return null;const o=new c;return o.read(s,r),[o]}function y(e,t,r,s){const o=e.find((e=>!!e.field));o&&(0,n.RB)(r,[o.toJSON()],t)}const f=e=>{let t=class extends e{constructor(){super(...arguments),this.orderBy=null}};return(0,o._)([(0,i.Cb)({type:[c],json:{origins:{"web-scene":{write:!1,read:!1}},read:{source:"layerDefinition.orderBy",reader:h},write:{target:"layerDefinition.orderBy",writer:y}}})],t.prototype,"orderBy",void 0),t=(0,o._)([(0,a.j)("esri.layers.mixins.OrderedLayer")],t),t}},70082:(e,t,r)=>{r.d(t,{Z:()=>d});var s=r(43697),o=r(2368),n=r(35454),i=r(96674),a=r(5600),l=(r(75215),r(67676),r(52011));const u=new n.X({esriFeatureEditToolAutoCompletePolygon:"auto-complete-polygon",esriFeatureEditToolCircle:"circle",esriFeatureEditToolEllipse:"ellipse",esriFeatureEditToolFreehand:"freehand",esriFeatureEditToolLine:"line",esriFeatureEditToolNone:"none",esriFeatureEditToolPoint:"point",esriFeatureEditToolPolygon:"polygon",esriFeatureEditToolRectangle:"rectangle",esriFeatureEditToolArrow:"arrow",esriFeatureEditToolTriangle:"triangle",esriFeatureEditToolLeftArrow:"left-arrow",esriFeatureEditToolRightArrow:"right-arrow",esriFeatureEditToolUpArrow:"up-arrow",esriFeatureEditToolDownArrow:"down-arrow"});let p=class extends((0,o.J)(i.wq)){constructor(e){super(e),this.name=null,this.description=null,this.drawingTool=null,this.prototype=null,this.thumbnail=null}};(0,s._)([(0,a.Cb)({json:{write:!0}})],p.prototype,"name",void 0),(0,s._)([(0,a.Cb)({json:{write:!0}})],p.prototype,"description",void 0),(0,s._)([(0,a.Cb)({json:{read:u.read,write:u.write}})],p.prototype,"drawingTool",void 0),(0,s._)([(0,a.Cb)({json:{write:!0}})],p.prototype,"prototype",void 0),(0,s._)([(0,a.Cb)({json:{write:!0}})],p.prototype,"thumbnail",void 0),p=(0,s._)([(0,l.j)("esri.layers.support.FeatureTemplate")],p);const d=p},16451:(e,t,r)=>{r.d(t,{Z:()=>h});var s=r(43697),o=r(2368),n=r(96674),i=r(5600),a=(r(75215),r(67676),r(71715)),l=r(52011),u=r(30556),p=r(72729),d=r(70082);let c=class extends((0,o.J)(n.wq)){constructor(e){super(e),this.id=null,this.name=null,this.domains=null,this.templates=null}readDomains(e){const t={};for(const r of Object.keys(e))t[r]=(0,p.im)(e[r]);return t}writeDomains(e,t){const r={};for(const t of Object.keys(e))e[t]&&(r[t]=e[t]?.toJSON());t.domains=r}};(0,s._)([(0,i.Cb)({json:{write:!0}})],c.prototype,"id",void 0),(0,s._)([(0,i.Cb)({json:{write:!0}})],c.prototype,"name",void 0),(0,s._)([(0,i.Cb)({json:{write:!0}})],c.prototype,"domains",void 0),(0,s._)([(0,a.r)("domains")],c.prototype,"readDomains",null),(0,s._)([(0,u.c)("domains")],c.prototype,"writeDomains",null),(0,s._)([(0,i.Cb)({type:[d.Z],json:{write:!0}})],c.prototype,"templates",void 0),c=(0,s._)([(0,l.j)("esri.layers.support.FeatureType")],c);const h=c},51706:(e,t,r)=>{var s,o;function n(e){return e&&"esri.renderers.visualVariables.SizeVariable"===e.declaredClass}function i(e){return null!=e&&!isNaN(e)&&isFinite(e)}function a(e){return e.valueExpression?s.Expression:e.field&&"string"==typeof e.field?s.Field:s.Unknown}function l(e,t){const r=t||a(e),n=e.valueUnit||"unknown";return r===s.Unknown?o.Constant:e.stops?o.Stops:null!=e.minSize&&null!=e.maxSize&&null!=e.minDataValue&&null!=e.maxDataValue?o.ClampedLinear:"unknown"===n?null!=e.minSize&&null!=e.minDataValue?e.minSize&&e.minDataValue?o.Proportional:o.Additive:o.Identity:o.RealWorldSize}r.d(t,{PS:()=>a,QW:()=>l,RY:()=>s,hL:()=>o,iY:()=>n,qh:()=>i}),function(e){e.Unknown="unknown",e.Expression="expression",e.Field="field"}(s||(s={})),function(e){e.Unknown="unknown",e.Stops="stops",e.ClampedLinear="clamped-linear",e.Proportional="proportional",e.Additive="additive",e.Constant="constant",e.Identity="identity",e.RealWorldSize="real-world-size"}(o||(o={}))},74889:(e,t,r)=>{r.d(t,{Z:()=>_});var s,o=r(43697),n=r(66577),i=r(38171),a=r(35454),l=r(96674),u=r(22974),p=r(70586),d=r(5600),c=(r(75215),r(71715)),h=r(52011),y=r(30556),f=r(82971),m=r(33955),g=r(1231);const b=new a.X({esriGeometryPoint:"point",esriGeometryMultipoint:"multipoint",esriGeometryPolyline:"polyline",esriGeometryPolygon:"polygon",esriGeometryEnvelope:"extent",mesh:"mesh","":null});let w=s=class extends l.wq{constructor(e){super(e),this.displayFieldName=null,this.exceededTransferLimit=!1,this.features=[],this.fields=null,this.geometryType=null,this.hasM=!1,this.hasZ=!1,this.queryGeometry=null,this.spatialReference=null}readFeatures(e,t){const r=f.Z.fromJSON(t.spatialReference),s=[];for(let t=0;t<e.length;t++){const o=e[t],n=i.Z.fromJSON(o),a=o.geometry&&o.geometry.spatialReference;(0,p.pC)(n.geometry)&&!a&&(n.geometry.spatialReference=r);const l=o.aggregateGeometries,u=n.aggregateGeometries;if(l&&(0,p.pC)(u))for(const e in u){const t=u[e],s=l[e]?.spatialReference;(0,p.pC)(t)&&!s&&(t.spatialReference=r)}s.push(n)}return s}writeGeometryType(e,t,r,s){if(e)return void b.write(e,t,r,s);const{features:o}=this;if(o)for(const e of o)if(e&&(0,p.pC)(e.geometry))return void b.write(e.geometry.type,t,r,s)}readQueryGeometry(e,t){if(!e)return null;const r=!!e.spatialReference,s=(0,m.im)(e);return s&&!r&&t.spatialReference&&(s.spatialReference=f.Z.fromJSON(t.spatialReference)),s}writeSpatialReference(e,t){if(e)return void(t.spatialReference=e.toJSON());const{features:r}=this;if(r)for(const e of r)if(e&&(0,p.pC)(e.geometry)&&e.geometry.spatialReference)return void(t.spatialReference=e.geometry.spatialReference.toJSON())}clone(){return new s(this.cloneProperties())}cloneProperties(){return(0,u.d9)({displayFieldName:this.displayFieldName,exceededTransferLimit:this.exceededTransferLimit,features:this.features,fields:this.fields,geometryType:this.geometryType,hasM:this.hasM,hasZ:this.hasZ,queryGeometry:this.queryGeometry,spatialReference:this.spatialReference,transform:this.transform})}toJSON(e){const t=this.write();if(t.features&&Array.isArray(e)&&e.length>0)for(let r=0;r<t.features.length;r++){const s=t.features[r];if(s.geometry){const t=e&&e[r];s.geometry=t&&t.toJSON()||s.geometry}}return t}quantize(e){const{scale:[t,r],translate:[s,o]}=e,n=this.features,i=this._getQuantizationFunction(this.geometryType,(e=>Math.round((e-s)/t)),(e=>Math.round((o-e)/r)));for(let e=0,t=n.length;e<t;e++)i?.((0,p.Wg)(n[e].geometry))||(n.splice(e,1),e--,t--);return this.transform=e,this}unquantize(){const{geometryType:e,features:t,transform:r}=this;if(!r)return this;const{translate:[s,o],scale:[n,i]}=r,a=this._getHydrationFunction(e,(e=>e*n+s),(e=>o-e*i));for(const{geometry:e}of t)(0,p.pC)(e)&&a&&a(e);return this.transform=null,this}_quantizePoints(e,t,r){let s,o;const n=[];for(let i=0,a=e.length;i<a;i++){const a=e[i];if(i>0){const e=t(a[0]),i=r(a[1]);e===s&&i===o||(n.push([e-s,i-o]),s=e,o=i)}else s=t(a[0]),o=r(a[1]),n.push([s,o])}return n.length>0?n:null}_getQuantizationFunction(e,t,r){return"point"===e?e=>(e.x=t(e.x),e.y=r(e.y),e):"polyline"===e||"polygon"===e?e=>{const s=(0,m.oU)(e)?e.rings:e.paths,o=[];for(let e=0,n=s.length;e<n;e++){const n=s[e],i=this._quantizePoints(n,t,r);i&&o.push(i)}return o.length>0?((0,m.oU)(e)?e.rings=o:e.paths=o,e):null}:"multipoint"===e?e=>{const s=this._quantizePoints(e.points,t,r);return s&&s.length>0?(e.points=s,e):null}:"extent"===e?e=>e:null}_getHydrationFunction(e,t,r){return"point"===e?e=>{e.x=t(e.x),e.y=r(e.y)}:"polyline"===e||"polygon"===e?e=>{const s=(0,m.oU)(e)?e.rings:e.paths;let o,n;for(let e=0,i=s.length;e<i;e++){const i=s[e];for(let e=0,s=i.length;e<s;e++){const s=i[e];e>0?(o+=s[0],n+=s[1]):(o=s[0],n=s[1]),s[0]=t(o),s[1]=r(n)}}}:"extent"===e?e=>{e.xmin=t(e.xmin),e.ymin=r(e.ymin),e.xmax=t(e.xmax),e.ymax=r(e.ymax)}:"multipoint"===e?e=>{const s=e.points;let o,n;for(let e=0,i=s.length;e<i;e++){const i=s[e];e>0?(o+=i[0],n+=i[1]):(o=i[0],n=i[1]),i[0]=t(o),i[1]=r(n)}}:null}};(0,o._)([(0,d.Cb)({type:String,json:{write:!0}})],w.prototype,"displayFieldName",void 0),(0,o._)([(0,d.Cb)({type:Boolean,json:{write:{overridePolicy:e=>({enabled:e})}}})],w.prototype,"exceededTransferLimit",void 0),(0,o._)([(0,d.Cb)({type:[i.Z],json:{write:!0}})],w.prototype,"features",void 0),(0,o._)([(0,c.r)("features")],w.prototype,"readFeatures",null),(0,o._)([(0,d.Cb)({type:[g.Z],json:{write:!0}})],w.prototype,"fields",void 0),(0,o._)([(0,d.Cb)({type:["point","multipoint","polyline","polygon","extent","mesh"],json:{read:{reader:b.read}}})],w.prototype,"geometryType",void 0),(0,o._)([(0,y.c)("geometryType")],w.prototype,"writeGeometryType",null),(0,o._)([(0,d.Cb)({type:Boolean,json:{write:{overridePolicy:e=>({enabled:e})}}})],w.prototype,"hasM",void 0),(0,o._)([(0,d.Cb)({type:Boolean,json:{write:{overridePolicy:e=>({enabled:e})}}})],w.prototype,"hasZ",void 0),(0,o._)([(0,d.Cb)({types:n.qM,json:{write:!0}})],w.prototype,"queryGeometry",void 0),(0,o._)([(0,c.r)("queryGeometry")],w.prototype,"readQueryGeometry",null),(0,o._)([(0,d.Cb)({type:f.Z,json:{write:!0}})],w.prototype,"spatialReference",void 0),(0,o._)([(0,y.c)("spatialReference")],w.prototype,"writeSpatialReference",null),(0,o._)([(0,d.Cb)({json:{write:!0}})],w.prototype,"transform",void 0),w=s=(0,o._)([(0,h.j)("esri.rest.support.FeatureSet")],w),w.prototype.toJSON.isDefaultToJSON=!0;const _=w},58333:(e,t,r)=>{r.d(t,{ET:()=>n,I4:()=>o,eG:()=>l,lF:()=>i,lj:()=>p,qP:()=>a,wW:()=>u});const s=[252,146,31,255],o={type:"esriSMS",style:"esriSMSCircle",size:6,color:s,outline:{type:"esriSLS",style:"esriSLSSolid",width:.75,color:[153,153,153,255]}},n={type:"esriSLS",style:"esriSLSSolid",width:.75,color:s},i={type:"esriSFS",style:"esriSFSSolid",color:[252,146,31,196],outline:{type:"esriSLS",style:"esriSLSSolid",width:.75,color:[255,255,255,191]}},a={type:"esriTS",color:[255,255,255,255],font:{family:"arial-unicode-ms",size:10,weight:"bold"},horizontalAlignment:"center",kerning:!0,haloColor:[0,0,0,255],haloSize:1,rotated:!1,text:"",xoffset:0,yoffset:0,angle:0},l={type:"esriSMS",style:"esriSMSCircle",color:[0,0,0,255],outline:null,size:10.5},u={type:"esriSLS",style:"esriSLSSolid",color:[0,0,0,255],width:1.5},p={type:"esriSFS",style:"esriSFSSolid",color:[0,0,0,255],outline:null}}}]);