package org.thingsboard.server.dao.feignService;


import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.dao.model.sql.ProjectEntity;
import org.thingsboard.server.dao.model.sql.ProjectRelationEntity;

import java.util.List;

@Component
@FeignClient("base-service")
public interface ProjectRelationApi {

    /**
     * 查询项目指定类型的资源详情
     *
     * @param entityType
     * @param projectId
     * @return
     * @throws ThingsboardException
     */
    @GetMapping("api/project/relation/{entityType}/{projectId}")
    List<ProjectRelationEntity> findProjectRelationByEntityTypeAndProjectId(@PathVariable(value = "entityType") String entityType,
                                                                            @PathVariable(value = "projectId") String projectId) throws ThingsboardException;

    /**
     * 查询指定资源类型以及资源ID的项目
     *
     * @param entityType
     * @param entityId
     * @return
     * @throws ThingsboardException
     */
    @GetMapping("api/project/relation/{entityType}/{entityId}")
    List<ProjectEntity> findProjectRelationByEntityTypeAndEntityId(@PathVariable(value = "entityType") String entityType,
                                                                   @PathVariable(value = "entityId") String entityId) throws ThingsboardException;

    /**
     * 挂载资源到指定项目
     *
     * @param entityType
     * @param projectId
     * @param entityIdStringList
     * @return
     */
    @PostMapping("api/project/relation/mount/{entityType}/{projectId}")
    Boolean mountEntityToProject(@PathVariable(value = "entityType") String entityType,
                                        @PathVariable(value = "projectId") String projectId,
                                        @RequestBody List<String> entityIdStringList);

}
