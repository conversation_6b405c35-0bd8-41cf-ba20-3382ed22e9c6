import{_ as T}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as k}from"./CardTable-rdWOL4_6.js";import{_ as P}from"./CardSearch-CB_HNR-Q.js";import{z as p,C as w,c as C,r as f,b as r,S as L,o as A,g as v,n as B,q as h}from"./index-r0dFAfgr.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";function M(c){return p({url:"/api/base/scheme/management/list",method:"get",params:c})}function q(c){return p({url:"/api/base/scheme/management/getDetail",method:"get",params:{id:c}})}function x(c){return p({url:"/api/base/scheme/management/add",method:"post",data:c})}function I(c){return p({url:"/api/base/scheme/management/edit",method:"post",data:c})}function E(c){return p({url:"/api/base/scheme/management/deleteIds",method:"delete",data:c})}const F={class:"wrapper"},V={__name:"schemeManagement",setup(c){const b=C(),d=C(),D=f({labelWidth:"100px",filters:[{type:"input",label:"底图配置ID",field:"mapConfId",placeholder:"请输入底图配置ID",onChange:()=>u()},{type:"btn-group",btns:[{type:"primary",perm:!0,text:"查询",click:()=>u()},{perm:!0,type:"primary",text:"新增",click:()=>y()},{perm:!0,type:"danger",text:"批量删除",click:()=>_()}]}],defaultParams:{}}),a=f({columns:[{label:"方案名称",prop:"name"},{label:"底图配置ID",prop:"mapConfId"},{label:"管网配置ID",prop:"pipeConfId"},{label:"状态",prop:"status",render:t=>t.status===1?"启用":"禁用"},{label:"备注",prop:"remark"}],dataList:[],operations:[{perm:!0,type:"primary",isTextBtn:!0,text:"查看详情",click:t=>S(t)},{perm:!0,type:"primary",isTextBtn:!0,text:"编辑",click:t=>y(t)},{perm:!0,type:"danger",isTextBtn:!0,text:"删除",click:t=>_(t)}],pagination:{total:0,page:1,align:"right",limit:20,handlePage:t=>{a.pagination.page=t,u()},handleSize:t=>{a.pagination.limit=t,u()}},handleSelectChange:t=>{a.selectList=t||[]}}),e=f({title:"新增方案管理",group:[{fields:[{type:"input",label:"方案名称",field:"name",placeholder:"请输入方案名称",rules:[{required:!0,message:"请输入方案名称"}]},{type:"input",label:"底图配置ID",field:"mapConfId",rules:[{required:!0,message:"请输入底图配置ID"}]},{type:"input",label:"管网配置ID",field:"pipeConfId",rules:[{required:!0,message:"请输入管网配置ID"}]},{type:"select",label:"状态",field:"status",options:[{label:"启用",value:1},{label:"禁用",value:0}],rules:[{required:!0,message:"请选择状态"}]},{type:"textarea",label:"备注",field:"remark",placeholder:"请输入备注信息"}]}],labelPosition:"top",defaultValue:{},dialogWidth:600,draggable:!0,showSubmit:!0,showCancel:!0,cancelText:"取消",submitText:"确定",submit:async t=>{var o;try{t.id?(await I(t),r.success("修改成功")):(await x(t),r.success("新增成功")),(o=d.value)==null||o.closeDialog(),u()}catch{r.error("操作失败")}}}),g=()=>{e.group[0].fields.forEach(t=>{t.disabled=!1,t.readonly=!1}),e.showSubmit=!0,e.showCancel=!0,e.cancelText="取消",e.submitText="确定",e.submit=async t=>{var o;try{t.id?(await I(t),r.success("修改成功")):(await x(t),r.success("新增成功")),(o=d.value)==null||o.closeDialog(),u()}catch{r.error("操作失败")}},e.footerBtns=void 0},y=t=>{var o;g(),e.title=t?"编辑方案管理":"新增方案管理",e.defaultValue={...t||{}},(o=d.value)==null||o.openDialog()},S=async t=>{var l,n;const o={id:t.id||"1",name:t.name||"测试方案",mapConfId:t.mapConfId||"MAP001",pipeConfId:t.pipeConfId||"PIPE001",status:t.status!==void 0?t.status:1,remark:t.remark||"这是方案管理的详情数据"};try{console.log("获取详情，行数据:",t);const i=await q(t.id);console.log("详情API响应:",i);let s=null;i.data?i.data.data?s=i.data.data:s=i.data:i&&(s=i),console.log("解析后的详情数据:",s),s||(console.log("使用模拟详情数据"),s=o),g(),e.title="方案管理详情",e.defaultValue={...s},console.log("设置的详情数据:",e.defaultValue),e.group[0].fields.forEach(m=>{m.type==="select"&&(m.readonly=!0),m.disabled=!0}),e.showSubmit=!1,e.showCancel=!0,e.cancel=!0,e.cancelText="关闭",e.submitText=void 0,e.submit=void 0,e.submitting=!1,e.footerBtns=[{text:"关闭",type:"default",click:()=>{var m;(m=d.value)==null||m.closeDialog()}}],console.log("详情模式DialogFormConfig配置:",{showSubmit:e.showSubmit,showCancel:e.showCancel,cancel:e.cancel,cancelText:e.cancelText,submitText:e.submitText,submit:e.submit,footerBtns:e.footerBtns}),(l=d.value)==null||l.openDialog()}catch(i){console.error("获取详情失败:",i),console.log("API调用失败，使用模拟详情数据"),g(),e.title="方案管理详情",e.defaultValue={...o},e.group[0].fields.forEach(s=>{s.type==="select"&&(s.readonly=!0),s.disabled=!0}),e.showSubmit=!1,e.showCancel=!0,e.cancel=!0,e.cancelText="关闭",e.submitText=void 0,e.submit=void 0,e.submitting=!1,e.footerBtns=[{text:"关闭",type:"default",click:()=>{var s;(s=d.value)==null||s.closeDialog()}}],console.log("详情模式DialogFormConfig配置:",{showSubmit:e.showSubmit,showCancel:e.showCancel,cancel:e.cancel,cancelText:e.cancelText,submitText:e.submitText,submit:e.submit,footerBtns:e.footerBtns}),(n=d.value)==null||n.openDialog(),r.error("API调用失败，当前显示模拟数据")}},_=t=>{L("确定删除？","删除提示").then(async()=>{var o;try{const l=t?[t.id]:((o=a.selectList)==null?void 0:o.map(i=>i.id))||[];if(!l.length){r.warning("请选择要删除的数据");return}(await E(l)).data?(r.success("删除成功"),u()):r.error("删除失败")}catch{r.error("删除失败")}}).catch(()=>{})},u=async()=>{var o;const t=[{id:"1",name:"主城区水务方案",mapConfId:"MAP001",pipeConfId:"PIPE001",status:1,remark:"主要方案配置"},{id:"2",name:"郊区水务方案",mapConfId:"MAP002",pipeConfId:"PIPE002",status:0,remark:"备用方案配置"}];try{const l=(o=b.value)==null?void 0:o.queryParams;console.log("请求参数:",{page:a.pagination.page,size:a.pagination.limit,...l||{}});const n=await M({page:a.pagination.page,size:a.pagination.limit,...l||{}});console.log("API响应数据:",n),n.data?n.data.records?(a.dataList=n.data.records||[],a.pagination.total=n.data.total||0):n.data.data&&n.data.data.records?(a.dataList=n.data.data.records||[],a.pagination.total=n.data.data.total||0):Array.isArray(n.data)?(a.dataList=n.data,a.pagination.total=n.data.length):Array.isArray(n.data.data)?(a.dataList=n.data.data,a.pagination.total=n.data.data.length):(console.warn("未知的数据结构:",n.data),a.dataList=[],a.pagination.total=0):Array.isArray(n)?(a.dataList=n,a.pagination.total=n.length):(console.warn("无法解析的响应格式:",n),a.dataList=[],a.pagination.total=0),console.log("解析后的数据:",a.dataList),console.log("总数:",a.pagination.total),a.dataList.length===0&&(console.log("使用模拟数据进行测试"),a.dataList=t,a.pagination.total=t.length)}catch(l){console.error("获取数据失败:",l),console.log("API调用失败，使用模拟数据"),a.dataList=t,a.pagination.total=t.length,r.error("API调用失败，当前显示模拟数据")}};return A(()=>{u()}),(t,o)=>{const l=P,n=k,i=T;return v(),B("div",F,[h(l,{ref_key:"refSearch",ref:b,config:D},null,8,["config"]),h(n,{class:"card-table",config:a},null,8,["config"]),h(i,{ref_key:"refDialogForm",ref:d,config:e},null,8,["config"])])}}},H=w(V,[["__scopeId","data-v-3aaef61f"]]);export{H as default};
