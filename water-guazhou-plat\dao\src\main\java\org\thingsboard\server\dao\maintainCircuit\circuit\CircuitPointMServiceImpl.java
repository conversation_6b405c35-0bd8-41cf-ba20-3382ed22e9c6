package org.thingsboard.server.dao.maintainCircuit.circuit;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.common.data.utils.DateUtils;
import org.thingsboard.server.dao.model.sql.UserEntity;
import org.thingsboard.server.dao.model.sql.deviceManage.DeviceType;
import org.thingsboard.server.dao.model.sql.maintainCircuit.circuit.*;
import org.thingsboard.server.dao.sql.deviceType.DeviceTypeMapper;
import org.thingsboard.server.dao.sql.maintainCircuit.circuit.CircuitPointCMapper;
import org.thingsboard.server.dao.sql.maintainCircuit.circuit.CircuitPointMMapper;
import org.thingsboard.server.dao.sql.maintainCircuit.circuit.CircuitTaskCMapper;
import org.thingsboard.server.dao.sql.maintainCircuit.circuit.CircuitTaskMMapper;
import org.thingsboard.server.dao.sql.user.UserRepository;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-08-24
 */
@Slf4j
@Service
@Transactional
public class CircuitPointMServiceImpl implements CircuitPointMService {

    @Autowired
    private CircuitPointMMapper circuitPointMMapper;

    @Autowired
    private CircuitPointCMapper circuitPointCMapper;

    @Autowired
    private DeviceTypeMapper deviceTypeMapper;

    @Autowired
    private CircuitTaskMMapper circuitTaskMMapper;

    @Autowired
    private CircuitTaskCMapper circuitTaskCMapper;

    @Autowired
    private UserRepository userRepository;

    @Override
    public PageData getList(String name, int page, int size, String tenantId) {

        List<CircuitPointM> circuitPointMList = circuitPointMMapper.getList(name, page, size, tenantId);
        if (circuitPointMList.size() > 0) {
            List<String> midList = circuitPointMList.stream().map(a -> a.getId()).collect(Collectors.toList());
            QueryWrapper<CircuitPointC> circuitPointCQueryWrapper = new QueryWrapper<>();
            List<CircuitPointC> circuitPointCS = circuitPointCMapper.getListByMainIdIn(midList);
            Map<String, List> circuitMap = new HashMap<>();
            for (CircuitPointC circuitPointC : circuitPointCS) {
                if (circuitMap.get(circuitPointC.getMainId()) == null) {
                    circuitMap.put(circuitPointC.getMainId(), new ArrayList());
                }
                circuitMap.get(circuitPointC.getMainId()).add(circuitPointC);
            }

            for (CircuitPointM circuitPointM : circuitPointMList) {
                circuitPointM.setCircuitPointCList(circuitMap.get(circuitPointM.getId()));
            }
        }

        int total = circuitPointMMapper.getListCount(name, tenantId);

        return new PageData(total, circuitPointMList);
    }

    @Override
    public CircuitPointM getDetail(String mainId) {
        CircuitPointM circuitPointM = circuitPointMMapper.getById(mainId);
        List<CircuitPointC> circuitPointCList = circuitPointCMapper.getList(circuitPointM.getId());
        // 所属分类链表
        for (CircuitPointC circuitPointC : circuitPointCList) {
            this.setType(circuitPointC);
        }

        circuitPointM.setCircuitPointCList(circuitPointCList);

        return circuitPointM;
    }


    @Override
    public CircuitPointM save(CircuitPointM circuitPointM) {
        circuitPointM.setUpdateTime(new Date());

        if (StringUtils.isBlank(circuitPointM.getId())) {
            circuitPointM.setCreateTime(new Date());

            circuitPointMMapper.insert(circuitPointM);
        } else {
            circuitPointMMapper.updateById(circuitPointM);
        }

        Map deleteMap = new HashMap<>();
        deleteMap.put("main_id", circuitPointM.getId());
        circuitPointCMapper.deleteByMap(deleteMap);

        if (circuitPointM.getCircuitPointCList() != null) {
            for (CircuitPointC circuitPointC : circuitPointM.getCircuitPointCList()) {
                circuitPointC.setMainId(circuitPointM.getId());
                circuitPointC.setTenantId(circuitPointM.getTenantId());
                circuitPointC.setCreateTime(new Date());

                circuitPointCMapper.insert(circuitPointC);
            }
        }

        return circuitPointM;
    }

    @Override
    public IstarResponse delete(List<String> ids) {
        circuitPointMMapper.deleteBatchIds(ids);

        // 删除子表
        QueryWrapper<CircuitPointC> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("main_id", ids);
        circuitPointCMapper.delete(queryWrapper);

        return IstarResponse.ok("删除成功");
    }


    @Override
    public List<CircuitPointM> findAll() {
        return circuitPointMMapper.selectByMap(new HashMap<>());
    }

    @Override
    public CircuitPointM getById(String pointId) {
        CircuitPointM circuitPointM = circuitPointMMapper.selectById(pointId);
        if (circuitPointM != null) {
            // 查找用户
            UserEntity user = userRepository.findOne(circuitPointM.getCreator());
            if (user != null) {
                circuitPointM.setCreatorName(user.getFirstName());
            }
            List<CircuitPointC> pointCMapperList = circuitPointCMapper.getList(circuitPointM.getId());
            for (CircuitPointC c : pointCMapperList) {
                this.setType(c);
            }
            circuitPointM.setCircuitPointCList(pointCMapperList);

            return circuitPointM;
        }
        return null;
    }


    private CircuitTaskM saveCircuit(CircuitPlanM circuitPlanM, List<CircuitPlanC> circuitPlanCList, String type) {
        // 保养任务主表
        CircuitTaskM circuitTaskM = new CircuitTaskM();
        BeanUtils.copyProperties(circuitPlanM, circuitTaskM);
        circuitTaskM.setId(null);
        // 新建
        circuitTaskM.setCode("BY" + new SimpleDateFormat("yyyyMMddHHmmssSSS").format(new Date()));
        circuitTaskM.setStatus("0");
        circuitTaskM.setType(type);
        circuitTaskM.setAuditStatus("0");
        circuitTaskM.setCreateTime(new Date());
        circuitTaskM.setTenantId(circuitPlanM.getTenantId());
        circuitTaskM.setStartTime(circuitPlanM.getStartTime());
        circuitTaskM.setEndTime(circuitPlanM.getEndTime());

        circuitTaskMMapper.insert(circuitTaskM);
        // 保养任务子表
        if (circuitPlanCList != null && !circuitPlanCList.isEmpty()) {
            for (CircuitPlanC circuitPlanC : circuitPlanCList) {
                CircuitTaskC circuitTaskC = new CircuitTaskC();
                BeanUtils.copyProperties(circuitPlanC, circuitTaskC);
                circuitTaskC.setMainId(circuitTaskM.getId());
                circuitTaskC.setStatus("0");
                circuitTaskC.setCreateTime(new Date());
                circuitTaskCMapper.insert(circuitTaskC);
            }
        }

        return circuitTaskM;
    }

    private void setType(CircuitPointC circuitPointC) {
        if (StringUtils.isBlank(circuitPointC.getTypeId())) {
            circuitPointC.setLinkedType("-");
            return;
        }
        String linkedType = "-";
        String topType = "-";
        DeviceType deviceType = null;
        String parentId = circuitPointC.getTypeId();
        for (int i = 0; i < 5; i++) {
            deviceType = deviceTypeMapper.selectById(parentId);
            if (deviceType == null) {
                break;
            }
            linkedType = deviceType.getName() + ">" + linkedType;
            if (StringUtils.isBlank(deviceType.getParentId())) {
                topType = deviceType.getName();
                break;
            }
            parentId = deviceType.getParentId();
        }
        if (linkedType.length() >= 2) {
            linkedType = linkedType.substring(0, linkedType.length() - 2);
        }

        circuitPointC.setLinkedType(linkedType);
        circuitPointC.setTopType(topType);
        circuitPointC.setType(linkedType);
        try {
            if (linkedType.contains(">")) {
                circuitPointC.setType(linkedType.substring(linkedType.lastIndexOf(">") + 1));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

    }


    private boolean checkTimeIsToday(Date nextExecuteTime) throws ParseException {
        String string = DateUtils.date2Str(new Date(), DateUtils.DATE_FORMATE_DAY);
        SimpleDateFormat dateFormat = new SimpleDateFormat(DateUtils.DATE_FORMATE_DEFAULT);
//        Date todayStart = dateFormat.parse(string + " 00:00:00");
        Date todayEnd = dateFormat.parse(string + " 23:59:59");

        if (/*nextExecuteTime.getTime() > todayStart.getTime() && */nextExecuteTime.getTime() < todayEnd.getTime()) {
            return true;
        }

        return false;
    }

}
