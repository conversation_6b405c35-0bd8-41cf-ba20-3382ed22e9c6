<template>
  <div ref="refContainer" :style="computedStyle">
    <VChart ref="refChart" :option="option"></VChart>
  </div>
</template>
<script lang="ts" setup>
import { useDetector } from '@/hooks/echarts';
import { IECharts } from '@/plugins/echart';
import { hexToRgba } from '@/utils/GlobalHelper';

const refContainer = ref<HTMLDivElement>();
const refChart = ref<IECharts>();
const props = defineProps<{
  width?: number;
  height?: number;
  data: { name: string; color: string; values: any[] }[];
  xdata: string[];
  unit?: string;
}>();
const option = ref<any>();
const computedStyle = computed(() => {
  return {
    width: props.width ? props.width + 'px' : '100%',
    height: props.height ? props.height + 'px' : '100%'
  };
});
const detector = useDetector();
watch(
  () => props.data,
  () => {
    refreshChart();
  },
  { deep: true }
);
const refreshChart = () => {
  option.value = {
    legend: {
      right: 10,
      top: 10,
      color: '#B8D2FF'
    },
    grid: {
      left: 20,
      right: 20,
      top: 40,
      bottom: 5,
      containLabel: true
    },
    dataZoom: [
      {
        show: true,
        type: 'inside',
        start: 0,
        end: 100
      },
      {
        show: false,
        start: 0,
        end: 100
      }
    ],
    xAxis: {
      type: 'category',
      axisLabel: {
        color: '#B8D2FF',
        fontSize: 10
      },
      axisLine: {
        lineStyle: {
          color: '#548BD2'
        }
      },
      axisTick: {
        show: false
      },
      data: props.xdata
    },
    yAxis: [
      {
        type: 'value',
        name: props.unit,
        namePosition: 'top',
        axisLabel: {
          color: '#B8D2FF',
          fontSize: 10
        },
        splitLine: {
          lineStyle: {
            type: 'dashed',
            color: '#B8D2FF'
          }
        },
        axisTick: {
          show: false
        },
        axisLine: {
          show: false
        }
      }
    ],
    series: props.data.map((item) => {
      return {
        name: item.name,
        type: 'line',
        smooth: true,
        symbol: 'none',
        itemStyle: {
          color: hexToRgba(item.color, 1)
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: hexToRgba(item.color, 0.5) // 0% 处的颜色
              },
              {
                offset: 1,
                color: hexToRgba(item.color, 0.1) // 100% 处的颜色
              }
            ]
          }
        },
        data: item.values
      };
    })
  };
};
onMounted(() => {
  refreshChart();
  detector.listenTo(refContainer.value, refChart);
});
</script>
<style lang="scss" scoped></style>
