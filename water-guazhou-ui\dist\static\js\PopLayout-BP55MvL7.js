import{d as $,c as S,r as E,a8 as L,am as M,bo as N,bR as R,i as n,g as p,n as z,p as d,aw as b,bh as D,h as y,F as h,q as f,d5 as I,bw as c,an as k,cy as P,d6 as q,ax as F,av as H,cE as W,C as j}from"./index-r0dFAfgr.js";import{w as A}from"./Point-WxyopZva.js";const G={class:"arc-infowindow-wrapper"},J={class:"content-wrapper"},K=$({__name:"PopLayout",props:{offsetx:{},offsety:{},title:{},width:{},height:{},maxHeight:{},maxWidth:{},showClose:{type:Boolean},highLight:{type:Boolean},visible:{type:Boolean},backgroundColor:{},status:{},showMore:{type:Boolean},showBack:{type:Boolean},x:{},y:{}},emits:["highlight","more","back","toggled","refreshPosition"],setup(m,{expose:C,emit:w}){const a=S(),r=m,o=E({visible:!!r.visible}),i=w,B=()=>{o.visible=!0,u(),i("toggled",!0)},_=e=>{o.visible=e??!o.visible,o.visible&&u(),i("toggled",o.visible)},v=()=>{o.visible=!1,i("toggled",!1)},u=()=>{var e,s;(s=(e=a.value)==null?void 0:e.parentElement)==null||s.appendChild(a.value),i("highlight")},x=(e,s)=>{if(!a.value||!e||(s=s||g.value,!s))return;const l=new A({x:s==null?void 0:s.x,y:s==null?void 0:s.y,longitude:s==null?void 0:s.longitude,latitude:s==null?void 0:s.latitude,spatialReference:e==null?void 0:e.spatialReference}),t=e==null?void 0:e.toScreen(l);a.value.style.left=((t==null?void 0:t.x)||-1e4)+(r.offsetx||0)+"px",a.value.style.top=((t==null?void 0:t.y)||-1e4)+(r.offsety||-10)+"px"},g=L(()=>({x:r.x,y:r.y}));return M(()=>g.value,e=>{e&&i("refreshPosition")},{immediate:!0}),C({open:B,close:v,toggle:_,setPosition:x}),(e,s)=>{const l=W;return N((p(),z("div",{ref_key:"refContainer",ref:a,class:b(["arc-infowindow",{active:e.highLight}]),style:H({backgroundColor:e.backgroundColor})},[d("div",G,[d("div",{class:"title-wrapper",onClick:u},[d("span",{class:b(["title",e.status])},D(e.title),3),e.showMore?(p(),y(l,{key:0,class:"btn btn-more",onClick:s[0]||(s[0]=c(t=>e.$emit("more"),["stop"]))},{default:h(()=>[f(n(I))]),_:1})):k("",!0),e.showBack?(p(),y(l,{key:1,class:"btn btn-back",onClick:s[1]||(s[1]=c(t=>e.$emit("back"),["stop"]))},{default:h(()=>[f(n(P))]),_:1})):k("",!0),f(l,{class:"btn btn-close",onClick:c(v,["stop"])},{default:h(()=>[f(n(q))]),_:1})]),d("div",J,[F(e.$slots,"default",{},void 0,!0)])])],6)),[[R,n(o).visible]])}}}),T=j(K,[["__scopeId","data-v-cc847c5b"]]);export{T as default};
