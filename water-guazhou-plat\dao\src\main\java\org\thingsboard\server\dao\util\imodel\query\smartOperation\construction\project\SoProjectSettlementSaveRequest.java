package org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoProjectSettlement;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
public class SoProjectSettlementSaveRequest extends SaveRequest<SoProjectSettlement> {

    // 所属项目编号
    @NotNullOrEmpty
    private String projectCode;

    // 总结算时间
    @NotNullOrEmpty
    private Date acceptTime;

    // 总结算金额
    @NotNullOrEmpty
    private BigDecimal cost;

    // 总结算说明
    private String remark;

    // 附件信息
    private String attachments;

    @Override
    protected SoProjectSettlement build() {
        SoProjectSettlement entity = new SoProjectSettlement();
        entity.setProjectCode(projectCode);
        entity.setCreator(currentUserUUID());
        entity.setCreateTime(createTime());
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    @Override
    protected SoProjectSettlement update(String id) {
        SoProjectSettlement entity = new SoProjectSettlement();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(SoProjectSettlement entity) {
        entity.setAcceptTime(acceptTime);
        entity.setCost(cost);
        entity.setRemark(remark);
        entity.setAttachments(attachments);
        entity.setUpdateUser(currentUserUUID());
        entity.setUpdateTime(createTime());
    }
}