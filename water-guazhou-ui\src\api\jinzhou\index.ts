// DMA 成本管理
import { request } from '@/plugins/axios';

// 大屏信息查询
export const getCostList = () =>
  request({
    url: `/api/jinzhou/scadaConfig/list`,
    method: 'get'
  });

//   保存大屏数据
export const saveCost = (params: {
  id?: string;
  code: string;
  jsonData: string;
}) =>
  request({
    url: `/api/jinzhou/scadaConfig/save`,
    method: 'post',
    data: params
  });

//   大屏信息查询Code
export const getCostListByCode = (code) =>
  request({
    url: `/api/jinzhou/scadaConfig/${code}`,
    method: 'get'
  });
