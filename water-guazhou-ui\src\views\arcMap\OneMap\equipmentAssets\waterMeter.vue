<!-- gis水表 -->
<template>
  <div>
    <Cards v-model="cardsvalue" :span="24"></Cards>
    <Form ref="refForm" :config="FormConfig"></Form>
  </div>
</template>
<script lang="ts" setup>
import { Cards } from '../../components';
import { ring, oneHistogram } from '../../components/components/chart';
import {
  excuteQueryForCount,
  getPipeLineLayerOption,
  getSubLayerIds,
  initQueryParams
} from '@/utils/MapHelper';
import { PipeStatistics } from '@/api/mapservice/pipe';
import { useAppStore, useGisStore } from '@/store';

const emit = defineEmits(['highlightMark', 'addMarks']);
const props = defineProps<{
  view?: __esri.MapView;
  menu: IMenuItem;
}>();
const state = reactive<{
  pipeLayerOption: NormalOption[];
}>({
  pipeLayerOption: []
});

const refForm = ref<IFormIns>();

const cardsvalue = ref([{ label: '0个', value: '水表总数' }]);
const TableConfig = reactive<ITable>({
  height: 250,
  dataList: [],
  pagination: {
    hide: true
  },
  columns: [
    {
      label: '名称',
      prop: 'LANEWAY',
      sortable: true,
      formatter: (row) => row.LANEWAY || '未知'
    },
    {
      label: '数量',
      prop: 'ObjectId'
    }
  ],
  handleRowClick: (row) => {
    emit(
      'highlightMark',
      props.menu,
      " LANEWAY like '%" + row.LANEWAY + "%' ",
      row.LANEWAY
    );
    // queryMeter(
    //   row.LANEWAY === null ? '' : " LANEWAY like '%" + row.LANEWAY + "%' "
    // )
  }
});
const FormConfig = reactive<IFormConfig>({
  group: [
    {
      id: 'meterstatus',
      fieldset: {
        type: 'underline',
        desc: '水表状态占比图'
      },
      fields: [
        {
          type: 'vchart',
          option: ring(),
          style: {
            width: '100%',
            height: '150px'
          },
          itemContainerStyle: {
            marginBottom: 0
          },
          handleClick: (params: any) => {
            emit(
              'highlightMark',
              props.menu,
              params.data.name === '未知'
                ? ''
                : ' OPENCLOSE="' + (params.data.name || '') + '" ',
              params.data.nameAlias
            );
            // queryMeter(
            //   params.data.name === '未知'
            //     ? ''
            //     : ' OPENCLOSE=' + (params.data.name || '') + ' '
            // )
          }
        }
      ]
    },
    {
      id: 'diameter',
      fieldset: {
        type: 'underline',
        desc: '按口径统计水表数量'
      },
      fields: [
        {
          type: 'vchart',
          option: oneHistogram(),
          style: {
            width: '100%',
            height: '150px'
          },
          itemContainerStyle: {
            marginBottom: 0
          },
          handleClick: (params: any) => {
            emit(
              'highlightMark',
              props.menu,
              params.data.name === '未知'
                ? ''
                : ' DIAMETER=' + (params.data.name || '') + ' ',
              params.data.nameAlias
            );
            // queryMeter(
            //   params.data.name === '未知'
            //     ? ''
            //     : ' DIAMETER=' + (params.data.name || '') + ' '
            // )
          }
        }
      ]
    },
    {
      id: 'METERTYPE',
      fieldset: {
        type: 'underline',
        desc: '按类型统计水表数量'
      },
      fields: [
        {
          type: 'vchart',
          option: oneHistogram(),
          style: {
            width: '100%',
            height: '150px'
          },
          handleClick: (params: any) => {
            emit(
              'highlightMark',
              props.menu,
              params.data.name === '未知'
                ? ''
                : ' METERTYPE="' + (params.data.name || '') + '" ',
              params.data.nameAlias
            );
            //   queryMeter(
            //   params.data.name === '未知'
            //     ? ''
            //     : ' METERTYPE=' + (params.data.name || '') + ' '
            // )
          }
        }
      ]
    },
    {
      id: 'LANEWAY',
      fieldset: {
        type: 'underline',
        desc: '按所在道路统计水表数量'
      },
      fields: [
        {
          type: 'input',
          field: 'LANEWAY',
          appendBtns: [
            { perm: true, text: '刷新', click: () => refreshMeterTable() }
          ],
          onChange: () => refreshMeterTable()
        },
        {
          type: 'table',
          config: TableConfig
        }
      ]
    }
  ],
  labelPosition: 'top',
  gutter: 12
});

const queryMeterCount = async (sql?: string) => {
  if (!props.view) return;
  const layerIds = getSubLayerIds(props.view, undefined, undefined, '水表');
  if (!layerIds.length) return;
  const res = await excuteQueryForCount(
    window.SITE_CONFIG.GIS_CONFIG.gisService +
      window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService +
      '/' +
      layerIds[0],
    initQueryParams({
      returnGeometry: false,
      where: sql || ''
    })
  );
  return res;
};
const refreshMeterStatusChart = async () => {
  if (!props.view) return;
  const fields = FormConfig.group.find(
    (item) => item.id === 'meterstatus'
  )?.fields;
  const field = fields?.length && (fields[0] as IFormVChart);
  const layerIds = getSubLayerIds(props.view, undefined, undefined, '水表');
  const res = await PipeStatistics({
    usertoken: useGisStore().gToken,
    layerids: JSON.stringify(layerIds),
    group_fields: JSON.stringify(['OPENCLOSE']),
    statistic_field: 'ObjectId',
    statistic_type: '1',
    where: '',
    f: 'pjson'
  });
  const data = res.data.result?.rows?.[0]?.rows.map((item) => {
    return {
      name: item.OPENCLOSE || '未知',
      nameAlias: item.OPENCLOSE || '未知',
      value: item.ObjectId || 0
    };
  });
  field && (field.option = ring(data, '个', '', 0));
};

const refreshDiameterMeterChart = async () => {
  if (!props.view) return;
  const fields = FormConfig.group.find(
    (item) => item.id === 'diameter'
  )?.fields;
  const field = fields?.length && (fields[0] as IFormVChart);
  const layerIds = getSubLayerIds(props.view, undefined, undefined, '水表');
  const res = await PipeStatistics({
    usertoken: useGisStore().gToken,
    layerids: JSON.stringify(layerIds),
    group_fields: JSON.stringify(['DIAMETER']),
    statistic_field: 'ObjectId',
    statistic_type: '1',
    where: '',
    f: 'pjson'
  });
  const data = res.data.result.rows?.[0]?.rows?.map((item) => {
    return {
      name: item.DIAMETER || '未知',
      nameAlias: 'DN' + item.DIAMETER || '未知',
      value: item.ObjectId || 0
    };
  });
  field && (field.option = oneHistogram(data, '个'));
};
const refreshTypeMeterChart = async () => {
  if (!props.view) return;
  const fields = FormConfig.group.find(
    (item) => item.id === 'METERTYPE'
  )?.fields;
  const field = fields?.length && (fields[0] as IFormVChart);
  const layerIds = getSubLayerIds(props.view, undefined, undefined, '水表');
  const res = await PipeStatistics({
    usertoken: useGisStore().gToken,
    layerids: JSON.stringify(layerIds),
    group_fields: JSON.stringify(['METERTYPE']),
    statistic_field: 'ObjectId',
    statistic_type: '1',
    where: '',
    f: 'pjson'
  });
  const data =
    res.data.result.rows[0]?.rows?.map((item) => {
      return {
        name: item.METERTYPE || '未知',
        nameAlias: item.METERTYPE || '未知',
        value: item.ObjectId || 0
      };
    }) || [];
  field && (field.option = oneHistogram(data, '个'));
};
const refreshMeterTable = async () => {
  if (!props.view) return;
  const fieldValue = refForm.value?.dataForm.LANEWAY;
  const sql = fieldValue ? " LANEWAY like '%" + fieldValue + "%' " : '';
  const layerIds = getSubLayerIds(props.view, undefined, undefined, '水表');
  const res = await PipeStatistics({
    usertoken: useGisStore().gToken,
    layerids: JSON.stringify(layerIds),
    group_fields: JSON.stringify(['LANEWAY']),
    statistic_field: 'ObjectId',
    statistic_type: '1',
    where: sql || '',
    f: 'pjson'
  });
  TableConfig.dataList = res.data.result.rows[0]?.rows || [];
};
const refreshData = () => {
  refreshMeterStatusChart();
  refreshDiameterMeterChart();
  refreshTypeMeterChart();
  refreshMeterTable();
};

onMounted(async () => {
  refreshData();
  state.pipeLayerOption = await getPipeLineLayerOption(props.view);
  const meterCount = await queryMeterCount();
  cardsvalue.value[0].label = (meterCount || '0') + ' 个';
});
watch(
  () => useAppStore().isDark,
  () => refreshData()
);
</script>

<style lang="scss" scoped>
.onemap-panel-wrapper {
  min-height: 610px;
  height: 100%;
}
</style>
