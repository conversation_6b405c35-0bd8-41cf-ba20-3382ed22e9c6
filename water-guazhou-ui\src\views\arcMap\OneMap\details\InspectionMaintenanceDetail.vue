<template>
  <PatrolDetail
    v-if="state.mounted"
    :layer-info="state.layerInfo"
    :view="staticsState.view"
    :row="state.row"
  ></PatrolDetail>
</template>
<script lang="ts" setup>
import PatrolDetail from '../../Inspection/Plans/components/PatrolDetail.vue';

const emit = defineEmits(['refresh']);
const state = reactive<{
  row?: any;
  layerInfo?: any[];
  mounted: boolean;
}>({
  mounted: false
});
const staticsState: {
  view?: __esri.MapView;
} = {};
const refreshDetail = async (
  row: any,
  view: __esri.MapView,
  layerInfo: any[]
) => {
  emit('refresh', { title: row.name });
  state.mounted = false;
  staticsState.view = view;
  state.row = row;
  state.layerInfo = layerInfo;
  await nextTick();
  state.mounted = true;
};
defineExpose({
  refreshDetail
});
onBeforeMount(() => {
  state.mounted = false;
});
</script>
<style lang="scss" scoped></style>
