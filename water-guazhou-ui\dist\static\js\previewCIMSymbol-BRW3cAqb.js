import{cF as w,cy as b,cG as D,cx as G,cD as V}from"./MapView-DaoQedLH.js";import{s as q,i as I}from"./cimAnalyzer-CMgqZsaO.js";import{GeometryStyle as P,CIMSymbolRasterizer as E}from"./CIMSymbolRasterizer-CqVpHbJI.js";import"./index-r0dFAfgr.js";import"./Point-WxyopZva.js";import"./widget-BcWKanF2.js";import"./pe-B8dP0-Ut.js";import"./fontUtils-BuXIMW9g.js";import"./BidiEngine-CsUYIMdL.js";import"./GeometryUtils-B7ExOJII.js";import"./enums-B5k73o5q.js";import"./alignmentUtils-CkNI7z7C.js";import"./definitions-826PWLuy.js";import"./number-CoJp78Rz.js";import"./Rect-CUzevAry.js";import"./callExpressionWithFeature-DgtD4TSq.js";import"./quantizationUtils-DtI9CsYu.js";import"./floatRGBA-PQQNbO39.js";import"./CIMResourceManager-FPmRy-nM.js";import"./Rasterizer-CuAuGNQK.js";import"./_commonjsHelpers-DCkdB7M8.js";import"./rasterizingUtils-BGZonnNf.js";import"./imageutils-KgbVacIV.js";const g=new E(null,!0),m=w(b.size),F=w(b.maxSize),L=w(b.lineWidth),k=1;function A(e){const t=e==null?void 0:e.size;return typeof t=="number"?{width:t,height:t}:{width:t!=null&&typeof t=="object"&&"width"in t?t.width:null,height:t!=null&&typeof t=="object"&&"height"in t?t.height:null}}async function mt(e,t={}){var z;const{node:M,opacity:v,symbolConfig:p}=t,j=typeof p=="object"&&"isSquareFill"in p&&p.isSquareFill,S=t.cimOptions||t,o=S.geometryType||D((z=e==null?void 0:e.data)==null?void 0:z.symbol),i=A(t),{feature:C,fieldMap:x}=S;if(i.width==null||i.height==null){const r=await q.resolveSymbolOverrides(e.data,C,null,x,o);if(!r)return null;(e=e.clone()).data={type:"CIMSymbolReference",symbol:r},e.data.primitiveOverrides=void 0;const y=[];I.fetchResources(r,g.resourceManager,y),y.length>0&&await Promise.all(y);const n=I.getEnvelope(r,null,g.resourceManager),f=n==null?void 0:n.width,d=n==null?void 0:n.height;i.width=o==="esriGeometryPolygon"?m:o==="esriGeometryPolyline"?L:f!=null&&isFinite(f)?Math.min(f,F):m,i.height=o==="esriGeometryPolygon"?m:d!=null&&isFinite(d)?Math.max(Math.min(d,F),k):m}const h=await g.rasterizeCIMSymbolAsync(e,C,i,j||o!=="esriGeometryPolygon"?P.Preview:P.Legend,x,o);if(!h)return null;const{width:O,height:R}=h,l=document.createElement("canvas");l.width=O,l.height=R,l.getContext("2d").putImageData(h,0,0);const c=G(i.width),u=G(i.height),s=new Image(c,u);s.src=l.toDataURL(),v!=null&&(s.style.opacity=`${v}`);let a=s;if(t.effectView!=null){const r={shape:{type:"image",x:0,y:0,width:c,height:u,src:s.src},fill:null,stroke:null,offset:[0,0]};a=V([[r]],[c,u],{effectView:t.effectView})}return M&&a&&M.appendChild(a),a}export{mt as previewCIMSymbol};
