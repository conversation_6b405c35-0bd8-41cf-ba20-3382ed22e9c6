package org.thingsboard.server.controller.fault;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.fault.FaultTaskCService;
import org.thingsboard.server.dao.model.sql.fault.FaultTaskC;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-09-08
 */
@RestController
@RequestMapping("api/fault/task/c")
public class FaultTaskCController extends BaseController {

    @Autowired
    private FaultTaskCService faultTaskCService;

    @PostMapping
    public IstarResponse reviewer(@RequestBody FaultTaskC faultTaskC) throws ThingsboardException {
        String userId = UUIDConverter.fromTimeUUID(getCurrentUser().getUuidId());
        boolean checkUser = faultTaskCService.checkUser(faultTaskC.getMainId(), userId);
        if (!checkUser) {
            return IstarResponse.error("您没有该任务的执行权限");
        }
        faultTaskCService.save(faultTaskC);
        return IstarResponse.ok("保存成功");
    }
}
