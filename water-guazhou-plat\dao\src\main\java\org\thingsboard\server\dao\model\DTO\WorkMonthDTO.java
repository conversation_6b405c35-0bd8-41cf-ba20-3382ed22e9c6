package org.thingsboard.server.dao.model.DTO;

import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 工作月报表
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-12-20
 */
@Data
public class WorkMonthDTO {
    private String type;

    private List<WorkMonthSubDTO> subList = new ArrayList();

    private Map<String, WorkMonthSubDTO> workMonthSubDTOMap = new HashMap();

    private Integer subTotal;

    private Integer percent;
}
