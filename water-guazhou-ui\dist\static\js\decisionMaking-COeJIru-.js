import{d as c}from"./smartDecisionData-BKd4shVG.js";import l from"./PieCharts-NNEssywu.js";import n from"./SCGK-B6e13iTU.js";import p from"./GWGK-RDqceU8T.js";import _ from"./YSGK-DRcN3WJi.js";import d from"./KFGK-DT0HQbhN.js";import f from"./SZHGL-7HClM3CJ.js";import u from"./LSGC-BqEnfUC2.js";import b from"./SSL-Dw-WIvc5.js";import x from"./FWLX-B_vUrYkQ.js";import o from"./TitleCard-BgReUNwX.js";import{d as h,g as s,n as a,p as r,aB as g,aJ as C,h as G,i as k,q as t,F as e,C as S}from"./index-r0dFAfgr.js";import"./ScrollList-C0VFzhoB.js";import"./index-BlG8PIOK.js";import"./TargetItem-MDYkIAGN.js";import"./decisionMaking_center_left-CLFn6qN_.js";import"./compound-uu7Zq9wL.js";import"./RoundTargetItem-BwABRiCQ.js";import"./charts-m9UAsyIq.js";import"./useDetector-BRcb7GRN.js";import"./TitleHeader-CBWfLOPA.js";const v={class:"content"},B={class:"content_top"},K={class:"content_center"},L={class:"content_bottom"},F=h({__name:"decisionMaking",setup(M){return(N,V)=>(s(),a("div",v,[r("div",B,[(s(!0),a(g,null,C(k(c),(i,m)=>(s(),G(l,{key:m,config:i},null,8,["config"]))),128))]),r("div",K,[t(o,{title:"生产概况",class:"box-top"},{default:e(()=>[t(n)]),_:1}),t(o,{title:"管网概况",class:"box-top"},{default:e(()=>[t(p)]),_:1}),t(o,{title:"营收概况",class:"box-top"},{default:e(()=>[t(_)]),_:1}),t(o,{title:"客服概况",class:"box-top"},{default:e(()=>[t(d)]),_:1})]),r("div",L,[t(o,{title:"水质合格北",class:"box-bottom"},{default:e(()=>[t(f)]),_:1}),t(o,{title:"漏损构成",class:"box-bottom"},{default:e(()=>[t(u)]),_:1}),t(o,{title:"售水量",class:"box-bottom"},{default:e(()=>[t(b)]),_:1}),t(o,{title:"服务类型",class:"box-bottom"},{default:e(()=>[t(x)]),_:1})])]))}}),$=S(F,[["__scopeId","data-v-694836d0"]]);export{$ as default};
