import{s as t}from"./sphere-NgXH-gLx.js";import{aN as a,af as r,aW as c,aT as w,ac as b}from"./MapView-DaoQedLH.js";import{v as h}from"./lineSegment-DQ0q5UHF.js";function j(p){return p?{p0:a(p.p0),p1:a(p.p1),p2:a(p.p2)}:{p0:r(),p1:r(),p2:r()}}function N(p,n,o){const e=n[0]-p[0],i=n[1]-p[1],m=o[0]-p[0],u=o[1]-p[1];return .5*Math.abs(e*u-i*m)}function O(p,n,o){return c(s,n,p),c(f,o,p),w(b(s,s,f))/2}new t(h);new t(()=>j());const s=r(),f=r();export{N as S,O as w};
