<template>
  <div
    class="subsidebar-item"
    :class="[
      router.currentRoute.value.path === props.menu?.path ? 'is-active' : ''
    ]"
    @click="goto"
  >
    <div class="submenu-icon">
      <span :class="menu.meta?.icon"></span>
    </div>
    <div class="submenu-text">
      {{ menu.meta?.title }}
      <Icon
        class="submenu-follow"
        icon="ep:star"
      ></Icon>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { Icon } from '@iconify/vue'
import { useAppStore } from '@/store'

const props = defineProps<{
  menu: any
}>()
const router = useRouter()
const goto = () => {
  useAppStore().TOGGLE_menuShow(false)
  router.push({ ...props.menu })
}

// 添加默认导出
defineOptions({
  name: 'SubSideBarItem'
});
</script>
<style lang="scss" scoped>
.subsidebar-item {
  padding: 8px;
  border-radius: 6px;
  margin-right: 8px;
  margin-bottom: 8px;
  cursor: pointer;
  color: #fff;
  background-image: linear-gradient(136.79deg, #5ecfff 0%, #0d8ce2 71.59%);
  &.lightblue {
    background-image: linear-gradient(136.79deg, #5ecfff 0%, #0d8ce2 71.59%);
  }
  &.darkblue {
    background-image: linear-gradient(131.36deg, #5499e9 0%, #1b68ae 73.41%);
  }
  &.lightgreen {
    background-image: linear-gradient(131.36deg, #5ccdcd 0%, #00a9a9 73.41%);
  }
  &.darkgreen {
    background-image: linear-gradient(131.36deg, #5ccd75 0%, #00a96c 73.41%);
  }
  &.long {
    width: calc(100% - 8px);
  }
  &.short {
    width: calc(50% - 8px);
  }
  &.is-active,
  &:hover {
    box-shadow: 8px 8px 16px rgba(0, 0, 0, 0.25);
  }
  .submenu-icon {
    margin: 8px 0;
  }
  .submenu-text {
    line-height: 1.5em;
    display: flex;
    align-items: center;
    font-family: 'PingFang SC';
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    word-break: break-all;
  }
  .submenu-follow {
    margin-left: auto;
    min-width: 20px;
  }
}
</style>
