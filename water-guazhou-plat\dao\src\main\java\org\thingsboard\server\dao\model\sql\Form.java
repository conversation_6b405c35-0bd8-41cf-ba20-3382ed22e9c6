package org.thingsboard.server.dao.model.sql;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 水务-表单
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-10-13
 */
@TableName("tb_form")
@Data
public class Form {
    @TableId
    private String id;

    private String code;

    private String name;

    private String content;

    private Boolean status;

    private String remark;

    private String tenantId;

}
