import{d as j,a6 as E,r as C,c as m,bF as P,s as H,j as x,bB as Q,o as X,ay as $,g as D,n as O,q as u,i as n,p as T,F as k,bo as R,br as G,b7 as K,C as Z}from"./index-r0dFAfgr.js";import{_ as ee}from"./index-C9hz-UZb.js";import{_ as te}from"./CardTable-rdWOL4_6.js";import{_ as ae}from"./InlineForm.vue_vue_type_style_index_0_lang-s-ANlzyw.js";import{_ as oe}from"./CardSearch-CB_HNR-Q.js";import{l as Y}from"./echart-DkOzaNjN.js";import{u as re}from"./useStation-DJgnSZIA.js";import{a as se,b as le}from"./waterIndicators-BJSzKLY_.js";import"./Search-NSrhrIa_.js";import"./zhandian-YaGuQZe6.js";const ne={class:"wrapper"},ie={class:"main"},ce={class:"left"},pe={class:"table1"},ue={class:"table2"},fe={class:"right"},me=j({__name:"index",setup(de){const{getAllStationOption:q,getStationAllAttrs:z}=re(),A=E(),l=C({compareType:2,chartOption1:null,chartOption2:null,chartOption3:null,storedStationName:"",storedStationList:[],attrTypeList:[{label:"浊度",value:"turbidity",unit:"(NTU)"},{label:"余氯",value:"remainder",unit:"(NTU)"},{label:"PH",value:"ph",unit:"(NTU)"}]}),B=m(),M=m(),w=m(),I=m(),L=m(),b=m(!1),h=m(),y=m(),_=C({defaultParams:{attrType:"turbidity",time:P().format("YYYY-MM-DD"),queryType:"15m"},filters:[{type:"select",label:"水厂",field:"stationId",options:[],clearable:!1,onChange:e=>{l.storedStationName=l.storedStationList.find(s=>s.id===e).label}},{type:"select",field:"attributeId",options:[],clearable:!1,label:"压力点"},{type:"date",label:"日期",field:"time",clearable:!1},{type:"select",width:"100px",field:"queryType",clearable:!1,options:[{label:"1 m",value:"1m"},{label:"5 m",value:"5m"},{label:"10 m",value:"10m"},{label:"15 m",value:"15m"},{label:"小时",value:"60m"}],label:"时间间隔"},{type:"btn-group",btns:[{perm:!0,text:"查询",click:()=>N(),icon:"iconfont icon-chaxun"},{perm:!0,type:"default",text:"重置",svgIcon:H(K),click:()=>{var e;(e=h.value)==null||e.resetForm()}},{text:"导出",perm:!0,type:"warning",icon:"iconfont icon-xiazai",click:()=>W()}]}]}),F=m({group:[{fields:[{type:"radio-button",field:"compareType",options:[{label:"环比",value:2},{label:"同比",value:1},{label:"定基比",value:3}],label:"",clearable:!1,onChange:e=>{l.compareType=e,N()}}]}],defaultValue:{compareType:2}}),g=C({loading:!0,dataList:[],highlightCurrentRow:!0,columns:[{prop:"name1",label:"时间"},{prop:"name2",label:"差值率",unit:"(%)"},{prop:"name3",label:"变化系数"}],operations:[],showSummary:!1,operationWidth:"150px",pagination:{hide:!0}}),S=C({loading:!0,dataList:[],highlightCurrentRow:!0,columns:[{prop:"ts",label:"时间"},{prop:"max",label:"最大值"},{prop:"maxTs",label:"最大值发生时间"},{prop:"min",label:"最小值"},{prop:"minTs",label:"最小值发生时间"},{prop:"avg",label:"平均值"}],operations:[],showSummary:!1,operationWidth:"150px",pagination:{hide:!0}}),N=async()=>{var r;b.value=!0,g.loading=!0,S.loading=!0;const e=((r=h.value)==null?void 0:r.queryParams)||{},s={stationId:e.stationId,attributeId:e.attributeId,queryType:e.queryType,time:P(e.time).format("YYYY-MM-DD"),compareType:l.compareType},a=(await se(s)).data.data;g.columns=a==null?void 0:a.baseTable.tableInfo.map(t=>({prop:t.columnValue,label:t.columnName,unit:t.unit?"("+t.unit+")":""})),g.dataList=a==null?void 0:a.baseTable.tableDataList,g.loading=!1,S.dataList=a==null?void 0:a.countTable,S.loading=!1,V(a==null?void 0:a.baseTable,s)},V=(e,s)=>{J();const o={name:"",smooth:!0,data:[],type:"line",markPoint:{data:[{type:"max",name:"最大值",label:{fontSize:12,color:x().isDark?"#ffffff":"#000000"}},{type:"min",name:"最小值",label:{color:x().isDark?"#ffffff":"#000000"}}]}},a=e==null?void 0:e.tableDataList.map(c=>c.ts),r=Y();r.series=[],r.yAxis[0].name="压力(Mpa)",r.xAxis.data=a,e==null||e.tableInfo.filter(c=>{if(!["differenceRate","ts","changeRate"].includes(c.columnValue)){const d=JSON.parse(JSON.stringify(o));d.name=c.columnName,d.data=e==null?void 0:e.tableDataList.map(p=>p[c.columnValue]),r.series.push(d)}});const t=Y();t.series=[],t.yAxis[0].name="压力(Mpa)",t.xAxis.data=a;const f=JSON.parse(JSON.stringify(o));f.data=e==null?void 0:e.tableDataList.map(c=>c[s.time]),t.series.push(f);const i=Y();i.series=[],i.yAxis[0].name="系数(%)",i.xAxis.data=a;const v=JSON.parse(JSON.stringify(o));v.data=e==null?void 0:e.tableDataList.map(c=>c.changeRate),i.series.push(v),Q(()=>{y.value&&A.listenTo(y.value,()=>{l.chartOption1=r,l.chartOption2=t,l.chartOption3=i,U()})}),b.value=!1},J=()=>{var e,s,o;(e=w.value)==null||e.clear(),(s=I.value)==null||s.clear(),(o=L.value)==null||o.clear()},U=()=>{var e,s,o;(e=w.value)==null||e.resize(),(s=I.value)==null||s.resize(),(o=L.value)==null||o.resize()},W=()=>{var o;const e=((o=h.value)==null?void 0:o.queryParams)||{},s={stationId:e.stationId,attributeId:e.attributeId,queryType:e.queryType,time:P(e.time).format("YYYY-MM-DD"),compareType:l.compareType};le(s).then(a=>{const r=window.URL.createObjectURL(a.data),t=document.createElement("a");t.style.display="none",t.href=r,t.setAttribute("download","出水压力报表.xlsx"),document.body.appendChild(t),t.click()})};return X(async()=>{var t,f,i,v,c,d;const e=await q("水厂");l.storedStationList=e,console.log("state.storedStations",e);const s=(t=_.filters)==null?void 0:t.find(p=>p.field==="stationId");s.options=e;const o=await z(e[0].id);console.log("attrOptions",o);const a=(f=_.filters)==null?void 0:f.find(p=>p.field==="attributeId");let r=o==null?void 0:o.filter(p=>p.data.attr==="pressure");r=r.map(p=>({label:p.data.type+""+p.data.name,value:p.id})),a.options=r,console.log(a.options),_.defaultParams={..._.defaultParams,stationId:(i=e[0])==null?void 0:i.id,attributeId:(v=r[0])==null?void 0:v.value},l.storedStationName=(c=e[0])==null?void 0:c.label,(d=h.value)==null||d.resetForm(),N()}),(e,s)=>{const o=oe,a=ae,r=te,t=ee,f=$("VChart"),i=G;return D(),O("div",ne,[u(o,{ref_key:"refSearch",ref:h,config:n(_)},null,8,["config"]),T("div",ie,[T("div",ce,[u(t,{class:"left-card",title:" "},{right:k(()=>[u(a,{ref:"refForm",class:"left",config:n(F)},null,8,["config"])]),default:k(()=>[T("div",pe,[u(r,{ref_key:"refCardTable",ref:B,class:"card-table",config:n(g)},null,8,["config"])]),T("div",ue,[u(r,{ref_key:"refCardTable2",ref:M,class:"card-table",config:n(S)},null,8,["config"])])]),_:1})]),T("div",fe,[u(t,{title:n(l).compareType===1?" 同比":n(l).compareType===2?" 环比":" 定基比分析",class:"card-chart"},{default:k(()=>[R((D(),O("div",{ref_key:"zxDiv",ref:y,class:"chart-box"},[u(f,{ref_key:"refChart1",ref:w,theme:n(x)().isDark?"blackBackground":"whiteBackground",option:n(l).chartOption1},null,8,["theme","option"])])),[[i,n(b)]])]),_:1},8,["title"]),u(t,{title:"阈值分析",class:"card-chart"},{default:k(()=>[R((D(),O("div",{ref_key:"zxDiv",ref:y,class:"chart-box"},[u(f,{ref_key:"refChart2",ref:I,theme:n(x)().isDark?"blackBackground":"whiteBackground",option:n(l).chartOption2},null,8,["theme","option"])])),[[i,n(b)]])]),_:1}),u(t,{title:"系数分析",class:"card-chart"},{default:k(()=>[R((D(),O("div",{ref_key:"zxDiv",ref:y,class:"chart-box"},[u(f,{ref_key:"refChart3",ref:L,theme:n(x)().isDark?"blackBackground":"whiteBackground",option:n(l).chartOption3},null,8,["theme","option"])])),[[i,n(b)]])]),_:1})])])])}}}),Ce=Z(me,[["__scopeId","data-v-96d18d9a"]]);export{Ce as default};
