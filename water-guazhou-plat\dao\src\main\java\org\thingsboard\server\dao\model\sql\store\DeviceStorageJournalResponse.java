package org.thingsboard.server.dao.model.sql.store;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.sql.area.AreaMapper;
import org.thingsboard.server.dao.util.imodel.response.annotations.InfoViaMapper;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseTenantName;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.util.Date;

@Getter
@Setter
@ResponseEntity
public class DeviceStorageJournalResponse {
    // id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    // 设备编码
    private String serialId;

    // 设备标签
    private String deviceLabelCode;

    // 供应商ID
    private String supplierId;

    // 供应商
    private String supplierName;

    // 报废时间
    private Date scrappedTime;

    // 所在仓库
    private String storehouseId;

    // 所在仓库
    private String storehouseName;

    // 所在货架
    private String shelvesId;

    // 对应的出库到的出库单条目
    private String storeOutItemId;

    // 出库时间
    private Date outTime;

    // 最后保养时间
    private Date lastMaintainanceTime;

    // 最后巡检时间
    private Date lastInspectionTime;

    // 租户ID
    @ParseTenantName
    private String tenantId;


    // 设备型号
    private String model;

    // 设备名称
    private String name;

    // 设备类型
    private String typeId;

    // 设备类型
    private String type;

    // 设备类型
    private String topTypeId;

    // 设备类型
    private String topType;

    // 安装区域Id
    @InfoViaMapper(name = "treePath", mapper = AreaMapper.class)
    private String installAddressId;

    // 安装区域名称
    private String installAddressName;

    // 安装地址明细
    private String detailInstallAddressName;

    // 施工项目
    private String projectId;

    // 施工项目
    private String projectName;

    // 使用部门
    private String departmentId;

    // 使用部门
    private String departmentName;

    private String isCollect;

}
