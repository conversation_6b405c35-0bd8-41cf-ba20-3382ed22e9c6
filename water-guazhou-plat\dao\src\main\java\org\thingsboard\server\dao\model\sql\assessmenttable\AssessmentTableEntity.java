package org.thingsboard.server.dao.model.sql.assessmenttable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.thingsboard.server.common.data.assessmenttable.AssessmentTableDto;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.utils.StringUtils;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import java.util.UUID;

/**
 * 考核表实体类
 */
@Data
@Entity
@Table(name = "assessment_table")
@TableName("assessment_table")
@NoArgsConstructor
@AllArgsConstructor
public class AssessmentTableEntity {

    /**
     * id
     */
    @TableId(type = IdType.INPUT)
    @Column(name = "id")
    @Id
    private String id;

    /**
     * 租户ID
     */
    @Column(name = "tenant_id")
    private String tenantId;

    /**
     * 考核表名称
     */
    @Column(name = "name")
    private String name;

    /**
     * 区域
     */
    @Column(name = "region")
    private String region;

    /**
     * 所在分区
     */
    @Column(name = "partition")
    private String partition;

    /**
     * 考核周期（年月，格式：YYYY-MM）
     */
    @Column(name = "period")
    private String period;
    
    /**
     * 考核类型
     * 1: 水质考核
     * 2: 供水安全考核
     * 3: 管网漏损考核
     * 4: 能耗考核
     * 5: 设备运行考核
     * 6: 综合评估
     */
    @Column(name = "assessment_type")
    private String assessmentType;
    
    /**
     * 考核等级
     * A: 优秀
     * B: 良好
     * C: 合格
     * D: 不合格
     */
    @Column(name = "assessment_level")
    private String assessmentLevel;
    
    /**
     * 考核状态
     * 0: 草稿
     * 1: 已提交
     * 2: 审核中
     * 3: 已通过
     * 4: 已驳回
     */
    @Column(name = "status")
    private String status;
    
    /**
     * 总分
     */
    @Column(name = "total_score")
    private Double totalScore;
    
    /**
     * 审核人
     */
    @Column(name = "reviewer")
    private String reviewer;
    
    /**
     * 审核时间
     */
    @Column(name = "review_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date reviewTime;

    /**
     * 备注
     */
    @Column(name = "remark")
    private String remark;

    /**
     * 创建人
     */
    @Column(name = "creator")
    private String creator;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    
    /**
     * 搜索文本
     */
    @Column(name = "search_text")
    private String searchText;

    public AssessmentTableEntity(AssessmentTableDto assessmentTableDto) {
        if (StringUtils.checkNotNull(assessmentTableDto.getId())) {
            this.id = assessmentTableDto.getId();
        }
        this.tenantId = assessmentTableDto.getTenantId() != null ? assessmentTableDto.getTenantId().getId().toString() : null;
        this.name = assessmentTableDto.getName();
        this.region = assessmentTableDto.getRegion();
        this.partition = assessmentTableDto.getPartition();
        this.period = assessmentTableDto.getPeriod();
        this.assessmentType = assessmentTableDto.getAssessmentType();
        this.assessmentLevel = assessmentTableDto.getAssessmentLevel();
        this.status = assessmentTableDto.getStatus();
        this.totalScore = assessmentTableDto.getTotalScore();
        this.reviewer = assessmentTableDto.getReviewer();
        this.reviewTime = assessmentTableDto.getReviewTime();
        this.remark = assessmentTableDto.getRemark();
        this.creator = assessmentTableDto.getCreator();
        this.createTime = assessmentTableDto.getCreateTime();
        this.searchText = getSearchTextSource();
    }

    public AssessmentTableDto toData() {
        AssessmentTableDto dto = new AssessmentTableDto();
        dto.setId(id);
        dto.setTenantId(tenantId != null ? new TenantId(UUID.fromString(tenantId)) : null);
        dto.setName(name);
        dto.setRegion(region);
        dto.setPartition(partition);
        dto.setPeriod(period);
        dto.setAssessmentType(assessmentType);
        dto.setAssessmentLevel(assessmentLevel);
        dto.setStatus(status);
        dto.setTotalScore(totalScore);
        dto.setReviewer(reviewer);
        dto.setReviewTime(reviewTime);
        dto.setRemark(remark);
        dto.setCreator(creator);
        dto.setCreateTime(createTime);
        return dto;
    }
    
    public String getSearchTextSource() {
        return name + " " + region + " " + partition + " " + period;
    }
} 