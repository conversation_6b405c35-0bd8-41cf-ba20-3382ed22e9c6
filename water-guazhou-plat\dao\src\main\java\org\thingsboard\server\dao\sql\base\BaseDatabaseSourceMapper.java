package org.thingsboard.server.dao.sql.base;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.base.BaseDatabaseSource;
import org.thingsboard.server.dao.util.imodel.query.base.BaseDatabaseSourcePageRequest;

import java.util.List;

/**
 * 平台管理-多数据源Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
@Mapper
public interface BaseDatabaseSourceMapper {
    /**
     * 查询平台管理-多数据源
     *
     * @param id 平台管理-多数据源主键
     * @return 平台管理-多数据源
     */
    public BaseDatabaseSource selectBaseDatabaseSourceById(String id);

    /**
     * 查询平台管理-多数据源列表
     *
     * @param baseDatabaseSource 平台管理-多数据源
     * @return 平台管理-多数据源集合
     */
    public IPage<BaseDatabaseSource> selectBaseDatabaseSourceList(BaseDatabaseSourcePageRequest baseDatabaseSource);

    /**
     * 新增平台管理-多数据源
     *
     * @param baseDatabaseSource 平台管理-多数据源
     * @return 结果
     */
    public int insertBaseDatabaseSource(BaseDatabaseSource baseDatabaseSource);

    /**
     * 修改平台管理-多数据源
     *
     * @param baseDatabaseSource 平台管理-多数据源
     * @return 结果
     */
    public int updateBaseDatabaseSource(BaseDatabaseSource baseDatabaseSource);

    /**
     * 删除平台管理-多数据源
     *
     * @param id 平台管理-多数据源主键
     * @return 结果
     */
    public int deleteBaseDatabaseSourceById(String id);

    /**
     * 批量删除平台管理-多数据源
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBaseDatabaseSourceByIds(@Param("array") List<String> ids);
}
