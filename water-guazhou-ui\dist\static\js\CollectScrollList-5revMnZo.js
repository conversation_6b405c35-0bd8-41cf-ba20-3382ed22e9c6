import{d as S,e as y,bo as w,g as i,n as r,aB as L,aJ as D,p as l,q as o,aw as I,i as d,cs as N,F as a,bh as n,G as p,b as V,J as $,d8 as z,c6 as F,e$ as J,<PERSON> as T}from"./index-r0dFAfgr.js";const q={class:"collect-list"},G=["onClick"],M=["title"],P={class:"item-title"},j={class:"contextmenu"},A={class:"item-main"},H=S({__name:"CollectScrollList",emits:["click","scroll-end"],setup(K,{expose:_,emit:m}){const c=y();c.text.value;const u=m,f=(t,e=!1)=>{},C=t=>{t&&c.copy(t).then(()=>{V.success("文本已复制")})},v=t=>{u("click",t)},k=()=>{u("scroll-end")};return _({refreshData:f}),(t,e)=>{const h=$,b=z,g=F,x=J;return w((i(),r("ul",q,[(i(!0),r(L,null,D(t.collect.list.value,(s,B)=>(i(),r("li",{key:B,class:"collect-item",onClick:E=>v(s)},[l("div",{class:"item-header",title:s.title},[o(d(N),{class:I(s.status),icon:"ep:document"},null,8,["class"]),o(b,{trigger:"contextmenu"},{reference:a(()=>[l("span",P,n(s.title),1)]),default:a(()=>[l("ul",j,[l("li",null,[o(h,{style:{width:"100%"},onClick:E=>C(s.title)},{default:a(()=>e[0]||(e[0]=[p(" 复制 ")])),_:2},1032,["onClick"])])])]),_:2},1024),o(g,{type:"success",size:"small"},{default:a(()=>[p(n(s.status),1)]),_:2},1024)],8,M),l("ul",A,[l("li",null,[e[1]||(e[1]=l("label",null,"工程编号：",-1)),l("span",null,n(s.code),1)]),l("li",null,[e[2]||(e[2]=l("label",null,"提交时间：",-1)),l("span",null,n(s.time),1)]),l("li",null,[e[3]||(e[3]=l("label",null,"提交人：",-1)),l("span",null,n(s.person)+n(d(c).text.value),1)])])],8,G))),128))])),[[x,k]])}}}),Q=T(H,[["__scopeId","data-v-26e795a6"]]);export{Q as default};
