<!--
  管径
 -->
<template>
  <RightDrawerMap :title="'管道口径'">
    <HydraulicPanel
      :header="['口径分级(mm)', '图层控制', '定位']"
      :legends="[
        { label: '>800mm', value: 14, checked: true },
        { label: '700~800mm', value: 32, checked: true },
        { label: '600~700mm', value: 431, checked: true },
        { label: '500~600mm', value: 431, checked: true },
        { label: '400~500mm', value: 431, checked: true },
        { label: '300~400mm', value: 431, checked: true },
        { label: '200~300mm', value: 431, checked: true },
        { label: '110~200mm', value: 4211, checked: true },
        { label: '60~110mm', value: 4211, checked: true },
        { label: '0~60mm', value: 41, checked: true }
      ]"
      :unit="'个'"
    ></HydraulicPanel>
  </RightDrawerMap>
</template>
<script lang="ts" setup>
import RightDrawerMap from '../../components/common/RightDrawerMap.vue'
import HydraulicPanel from '../components/HydraulicPanel.vue'
</script>
<style lang="scss" scoped></style>
