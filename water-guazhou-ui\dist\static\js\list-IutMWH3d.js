import{_ as d}from"./CardTable-rdWOL4_6.js";import{_ as m}from"./CardSearch-CB_HNR-Q.js";import{d as f,c as p,s as u,r as _,o as g,g as b,n as h,q as l,i,b7 as y}from"./index-r0dFAfgr.js";import{I as C}from"./common-CvK_P_ao.js";import{h as v}from"./device-DWHb0XjG.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";const x={class:"wrapper"},w=f({__name:"list",setup(I){const r=p(),s=p({filters:[{label:"设备编码",field:"serialId",type:"input"},{label:"设备名称",field:"name",type:"input"}],operations:[{type:"btn-group",btns:[{type:"default",perm:!0,text:"重置",svgIcon:u(y),click:()=>{var a;(a=r.value)==null||a.resetForm(),o()}},{perm:!0,text:"查询",icon:C.QUERY,click:()=>o()}]}]}),e=_({defaultExpandAll:!0,indexVisible:!0,columns:[{label:"设备编码",prop:"serialId"},{label:"设备名称",prop:"deviceName"},{label:"所属大类",prop:"deviceTopTypeName"},{label:"所属类别",prop:"deviceType"},{label:"计量单位",prop:"unit"},{label:"申请数量",prop:"amount"},{label:"项目编号",prop:"projectCode"},{label:"工程编号",prop:"constructionCode"},{label:"合同编号",prop:"contractCode"},{label:"实施编号",prop:"applyCode"}],dataList:[],pagination:{page:1,limit:20,total:0,refreshData:({page:a,size:t})=>{e.pagination.page=a,e.pagination.limit=t,o()}}}),o=async()=>{var t;const a={size:e.pagination.limit||20,page:e.pagination.page||1,...((t=r.value)==null?void 0:t.queryParams)||{}};v(a).then(n=>{e.dataList=n.data.data.data||[],e.pagination.total=n.data.data.total||0})};return g(()=>{o()}),(a,t)=>{const n=m,c=d;return b(),h("div",x,[l(n,{ref_key:"refSearch",ref:r,config:i(s)},null,8,["config"]),l(c,{config:i(e),class:"card-table"},null,8,["config"])])}}});export{w as default};
