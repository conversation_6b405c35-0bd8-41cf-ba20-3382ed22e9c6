package org.thingsboard.server.dao.model.sql.store;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

@Getter
@Setter
@ResponseEntity
public class DeviceInfoResponse {
    // id
    private String id;

    // 序列号
    private String serialId;

    // 设备类型
    private String typeId;

    // 具体类型
    private String type;

    // 设备型号
    private String model;

    // 设备名称
    private String name;

    // 货架ID
    private String shelvesId;

    // 货架Code
    private String shelvesCode;

    // 货架名称
    private String shelvesName;

    // 根类型
    private String topType;

    // 单位
    private String unit;
}
