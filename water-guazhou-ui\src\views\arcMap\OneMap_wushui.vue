<template>
  <RightDrawerMap
    ref="refMap"
    :title="''"
    :hide-coords="false"
    :pops="state.pops"
    :windows="state.windows"
    :right-drawer-absolute="rightDrawer.drawerAbsolute.value"
    :right-drawer-width="rightDrawer.dialogWidth.value"
    :before-collapse="rightDrawer.beforeCollapse"
    :right-drawer-min-width="rightDrawer.drawerMinWidth.value"
    @map-loaded="onMapLoaded"
    @pop-toggle="(pop, flag) => (pop.visible = flag)"
  >
    <template #map-bars>
      <div class="custom-menubar">
        <OneMapMenuBar
          :menus="state.menus"
          @change="handleVerticalBarChange"
          @item-click="handleHorizontalBarClick"
        ></OneMapMenuBar>
        <!-- <VerticalBar
          :collapse-on-click="true"
          :menus="state.menus"
          @change="handleVerticalBarChange"
        /> -->
      </div>
      <ArcPipePick
        v-if="state.hMenu?.path === 'sbzc'"
        :add-to-view="true"
        :auto-start="true"
        :layer-ids="state.pipePickLayerid"
        @picked="handlePicked"
      ></ArcPipePick>
      <!-- <div class="custom-submenubar">
        <HorizontalBar
          :menu="state.hMenu"
          @click="handleHorizontalBarClick"
        />
      </div> -->
    </template>
    <div class="one-map-wrapper">
      <div
        class="left overlay-y"
        :class="rightDrawer.drawerLevel.value"
      >
        <div
          v-if="rightDrawer.drawerLevel.value === 'closed'"
          class="one-map-wrapper__closed"
        >
          {{ state.title }}
        </div>
        <template v-else>
          <FieldSet
            :size="'large'"
            :type="'simple'"
            class="left-title"
          >
            {{ state.title }}
          </FieldSet>
          <div class="left-main">
            <template
              v-for="item in state.menus"
              :key="item.path"
            >
              <template
                v-for="cItem in item.children"
                :key="cItem.path"
              >
                <template v-if="cItem.path === state.curPath && cItem.component">
                  <component
                    :is="cItem.component"
                    :menu="cItem"
                    :view="staticState.view"
                    @highlight-mark="highlightMark"
                    @add-marks="addMarks"
                  ></component>
                </template>
              </template>
            </template>
          </div>
        </template>
      </div>
      <div
        v-if="rightDrawer.drawerLevel.value === 'more'"
        class="right"
      >
        <div class="right-title">
          <span>{{ state.detailTitle }}</span>
          <Icon
            icon="mdi:close"
            class="more-close"
            @click="rightDrawer.drawerLevel.value = 'opened'"
          ></Icon>
        </div>
        <div class="right-main overlay-y">
          <template
            v-for="item in state.menus"
            :key="item.path"
          >
            <template
              v-for="cItem in item.children"
              :key="cItem.path"
            >
              <component
                :is="cItem.detailComponent"
                v-if="state.detailType === cItem.path && cItem.detailComponent"
                :ref="'refDetail' + cItem.path"
                @mounted="handleMounted(cItem)"
                @refresh="row => (state.detailTitle = row.title)"
              ></component>
            </template>
          </template>
        </div>
      </div>
    </div>

    <template #detail-header>
      <span>{{ state.detailTitle }}</span>
    </template>
    <template #detail-default>
    </template>
  </RightDrawerMap>
</template>
<script lang="ts" setup>
import { Icon } from '@iconify/vue'
import Graphic from '@arcgis/core/Graphic'
import Point from '@arcgis/core/geometry/Point'
import TextSymbol from '@arcgis/core/symbols/TextSymbol'
import {
  bindViewClick,
  createPictureMarker,
  getGeometryCenter,
  getGraphicLayer,
  getSubLayerIds,
  gotoAndHighLight,
  setMapCursor
} from '@/utils/MapHelper'
import RightDrawerMap from '@/views/arcMap/components/common/RightDrawerMap.vue'
import { useHighLight } from '@/hooks/arcgis'
import { delay } from '@/utils/GlobalHelper'
// import VerticalBar from './components/menus/VerticalBar.vue'
// import HorizontalBar from './components/menus/HorizontalBar.vue'
import { getOneMapMenus } from './config_wushui'
import useRightDrawer from '@/hooks/drawer/useRightDrawer'
import { SLMessage } from '@/utils/Message'
import OneMapMenuBar from './components/menus/OneMapMenuBar.vue'
import { queryLayerClassName } from '@/api/mapservice'
import { getMapLocationImageUrl } from '@/utils/URLHelper'
import { StationTypeEnums } from '../basicDataManage/stationManage/data'

const { proxy }: any = getCurrentInstance()
const rightDrawer = useRightDrawer({
  widthOption: {
    opend: 540,
    more: 1540
  }
})
const refMap = ref<InstanceType<typeof RightDrawerMap>>()
const highLight = useHighLight()
const state = reactive<{
  title: string
  detailTitle: string
  menus: IMenuItem[]
  hMenu?: IMenuItem
  curPath: string
  detailType: string
  layerIds: number[]
  layerInfos: ILayerInfo[]
  pageConfig: { path: string; layerIds: string[] }[]
  pops: IArcPopConfig[]
  windows: IArcMarkerProps[]
  pipePickLayerid: number[]
}>({
  title: '',
  curPath: '',
  detailTitle: '',
  pageConfig: [],
  layerIds: [],
  layerInfos: [],
  menus: [],
  pops: [],
  windows: [],
  detailType: '',
  pipePickLayerid: []
})
const staticState: {
  view?: __esri.MapView
  layers: __esri.GraphicsLayer[]
  highlightGraphic?: __esri.Graphic
} = {
  layers: []
}
const handleVerticalBarChange = (menu: IMenuItem & { meta: { url: string } }) => {
  // 视频会议打开新窗口
  if (menu.path === 'sphy') {
    window.open(menu.meta.url)
    return
  }
  /** 多次点击同一个菜单直接返回 */
  if (state.hMenu?.path === menu.path) return
  if (menu.path === state.hMenu?.path) return
  staticState.view?.map.removeMany(staticState.layers)
  highLight.destroy()
  staticState.layers.length = 0
  state.pops.length = 0
  // refMap.value?.toggleCustomDetail(false)
  state.hMenu = menu
  menu.children?.length && handleHorizontalBarClick(menu.children[0], true)
}
const handleHorizontalBarClick = (menu: IMenuItem, isChecked: boolean) => {
  state.curPath = menu.path
  state.title = menu.meta.title
  state.pipePickLayerid = state.layerInfos.filter(item => item.layername === state.title).map(item => item.layerid) || []
  let layer = staticState.layers.find(item => item.id === menu.path)
  if (!layer) {
    const isSSJK = menu.path.startsWith('ssjk')
    layer = getGraphicLayer(staticState.view, {
      id: isSSJK ? 'ssjk' : menu.path,
      title: isSSJK ? '站点' : menu.meta.title
    })
    layer && staticState.layers.push(layer)
    bindHighlightMark()
  }
  layer && (layer.visible = isChecked)
  refMap.value?.closeAllPop()
  /**
   * 修复bug:
   * 该bug导致当drawer是closed状态时，直接修改rightDrawer.drawerLvel.value为'open'会无法弹出drawer
   * 处理办法： 调用RightDrawerMap的toggleDrawer方法来切换状态
   */
  if (rightDrawer.drawerLevel.value === 'closed') {
    refMap.value?.toggleDrawer(true)
  } else {
    rightDrawer.drawerLevel.value = 'opened'
  }
}

const hightCallback = async (graphic?: __esri.Graphic) => {
  if (!graphic) return
  staticState.highlightGraphic = graphic
  const id = graphic?.attributes.id || ''
  state.detailType = graphic?.attributes?.path || ''
  await nextTick()
  if (!state.detailType) return
  // refMap.value?.toggleCustomDetail(true)
  refMap.value?.openPop(id)
  // 触发具体详情页面的刷新方法
  // 相同id表示是同一个要素，不多次进行数据刷新
  // if (staticState.highlightGraphic?.attributes.id === graphic?.attributes.id) return
  if (rightDrawer.drawerLevel.value === 'more') {
    refreshDetail()
  }
}
const refreshPipeDetail = async (tab: string, sql?: string) => {
  rightDrawer.drawerLevel.value = 'more'
  const opend = refMap.value?.isCustomOpened()
  if (!opend) {
    // refMap.value?.toggleCustomDetail(true)
    await delay(600)
  }
  const refDetail = proxy.$refs['refDetail' + state.detailType]
  refDetail?.[0].refreshDetail(staticState.view, tab, {
    where: sql
  })
}
const handleMounted = (cMenu: any) => {
  const refDetail = proxy.$refs['refDetail' + cMenu.path]
  refDetail?.length && refDetail[0].refreshDetail(staticState.highlightGraphic?.attributes?.row)
}
const refreshDetail = async (row?: any, ...args: any[]) => {
  const curRow = row || staticState.highlightGraphic?.attributes?.row
  if (state.detailType === 'ssjk-quanbu') {
    resolveSSJKAllClick(curRow)
  }
  rightDrawer.drawerLevel.value = 'more'
  await delay(600)
  await nextTick()
  const refDetail = proxy.$refs['refDetail' + state.detailType]

  refDetail?.length && refDetail[0].refreshDetail(curRow, ...args)
}
const bindHighlightMark = () => {
  highLight.bindHoverHighLight(staticState.view, staticState.layers, hightCallback, 0)
}
const resolveSSJKAllClick = (row: any) => {
  if (state.detailType === 'ssjk-quanbu') {
    const stationType = row?.type
    switch (stationType) {
      case StationTypeEnums.BENGZHAN:
        state.detailType = 'ssjk-ecgs'
        break
      case StationTypeEnums.CHELIUYAZHAN:
      case StationTypeEnums.LIULIANGJIANCEZHAN:
      case StationTypeEnums.YALIJIANCEZHAN:
        state.detailType = 'ssjk-znsb'
        break
      case StationTypeEnums.DAYONGHU:
        state.detailType = 'ssjk-dyhjcd'
        break
      case StationTypeEnums.SHUICHANGE:
        state.detailType = 'ssjk-sc'
        break
      case StationTypeEnums.SHUIYUANDI:
        state.detailType = 'ssjk-sy'
        break
      case StationTypeEnums.SHUIZHIJIANCEZHAN:
        state.detailType = 'ssjk-szjcd'
        break
      // case StationTypeEnums.WUSHUICHULICHANGE:
      //   state.detailType = ''
      default:
        break
    }
  }
}
const handlePicked = async (res: { results: any[] }) => {
  refMap.value?.closeAllPop()
  if (res.results.length) {
    const result = res.results[0]
    const feature = result.feature
    const id = feature.attributes.OBJECTID
    const center = getGeometryCenter(feature.geometry) || []
    state.windows = [
      {
        visible: false,
        x: center[0],
        y: center[1],
        title: result.layerName + '(' + feature.attributes['新编号'] + ')',
        attributes: {
          row: feature.attributes,
          id
        }
      }
    ]

    await gotoAndHighLight(staticState.view, feature, {
      avoidHighlight: true,
      zoom: 15
    })
    refMap.value?.openPop(id)
  } else {
    SLMessage.warning('没有查询到设备')
  }
}
const bindClickMark = () => {
  bindViewClick(staticState.view, async res => {
    if (res.results?.[0]?.type === 'graphic') {
      setMapCursor('pointer')
      // const graphic = res.results[0].graphic
      // staticState.highlightGraphic = graphic
      // const id = graphic?.attributes.id || ''
      // state.detailType = graphic?.attributes?.path || ''
      if (!state.detailType) {
        refMap.value?.closeAllPop()
        return
      }
      await nextTick()

      // refMap.value?.toggleCustomDetail(true)
      // refMap.value?.openPop(id)
      // 触发具体详情页面的刷新方法
      // 相同id表示是同一个要素，不多次进行数据刷新
      // if (staticState.highlightGraphic?.attributes.id === graphic?.attributes.id) return
      refreshDetail()
    } else {
      setMapCursor('')
      refMap.value?.closeAllPop()
    }
  })
}
const highlightMark = async (menu: IMenuItem, id: string, customTime?: string, zoom?: number) => {
  // 如果是管网服务来的由显示数据table
  if (menu.path.startsWith('sbzc')) {
    state.detailTitle = (menu.meta.title || '') + (customTime ? '(' + customTime + ')' : '')
    state.detailType = menu.path || ''
    refreshPipeDetail(menu.meta.title, id)
  } else if (menu.path === 'ywlc-xjyh') {
    // 处理巡检
    state.detailType = menu.path || ''
    refreshDetail(id, staticState.view, state.layerInfos)
    /**
     * toDo: 传递id给详情页，再查询相关信息
     */
  } else {
    const layer = staticState.layers.find(item => {
      return menu.path.startsWith('ssjk') ? item.id === 'ssjk' : item.id === menu.path
    })
    const graphic = layer?.graphics.find(item => item.attributes?.id === id)
    if (!layer || !graphic || !staticState.view) {
      SLMessage.warning('暂无位置信息')
      return
    }
    await gotoAndHighLight(staticState.view, graphic, {
      avoidHighlight: true,
      zoom: zoom || 15
    })
    highLight.highlight(staticState.view, graphic, hightCallback, 0)
  }
}
/**
 * 增加气泡
 * @param menu
 * @param data
 */
const addMarks = (options?: { windows: IArcPopConfig[] }) => {
  const path = state.curPath
  if (!path) return
  state.pops = state.pops.filter(item => item.attributes.customConfig?.path !== path)
  const data = options?.windows || []
  if (path.startsWith('ssjk') || path.startsWith('sbzc')) {
    state.pops = data
  } else {
    state.pops.push(...data)
  }
  const layer = getGraphicLayer(staticState.view, {
    id: path.startsWith('ssjk') ? 'ssjk' : path
  })
  if (!layer) return
  layer.removeAll()
  const pdata = data.filter(item => item.symbolConfig)
  const graphics = pdata.map((item: any) => {
    const graphic = createPictureMarker(item.x, item.y, {
      picUrl: item.symbolConfig?.url || getMapLocationImageUrl(),
      spatialReference: staticState.view?.spatialReference,
      attributes: item.attributes,
      picSize: [item.symbolConfig?.width || 20, item.symbolConfig?.height || 25],
      xOffset: item.symbolConfig?.xoffset || 0,
      yOffset: item.symbolConfig?.yoffset || 13
    })
    return graphic
  })
  const texts = pdata.map(item => {
    const g = new Graphic({
      geometry: new Point({
        x: item.x,
        y: item.y,
        spatialReference: staticState.view?.spatialReference
      }),
      symbol: new TextSymbol({
        font: {
          size: 8,
          weight: 'bold'
        },
        yoffset: -15,
        color: '#ffffff',
        text: item.title,
        lineHeight: 0.5,
        backgroundColor: 'rgba(213,123,11,0.3)'
      }),
      attributes: {
        notHighlight: true
      }
    })
    return g
  })
  layer.addMany(graphics)
  layer.addMany(texts)
}
const getLayerInfo = async () => {
  state.layerIds = getSubLayerIds(staticState.view)
  const layerInfo = await queryLayerClassName(state.layerIds)
  state.layerInfos = layerInfo.data?.result?.rows || []
}
const onMapLoaded = (view?: __esri.MapView) => {
  staticState.view = view
  console.log(view?.spatialReference)

  state.menus = getOneMapMenus()?.children || []
  state.hMenu = state.menus[0]
  const hChildren = state.hMenu.children || []
  handleHorizontalBarClick(hChildren[0], true)
  bindClickMark()
  getLayerInfo()
}
</script>
<style lang="scss" scoped>
.custom-menubar {
  position: absolute;
  // width: 120px;
  top: 15px;
  left: 15px;
}
.custom-submenubar {
  position: absolute;
  left: 150px;
  top: 15px;
}
// .dark,
// .darkblue {
//   .one-map-wrapper {
//     & > .right {
//       background-color: transparent;
//     }
//     :deep(.el-input-group__append),
//     :deep(.el-input-group__prepend) {
//       background-color: rgba(21, 45, 68, 1);
//     }
//   }
// }
.one-map-wrapper {
  height: 100%;
  width: 100%;
  display: flex;
  padding: 15px;
  & > .left {
    height: 100%;
    width: 510px;
    padding-right: 8px;
    &.closed {
      width: 48px;
    }
    &.opened {
      width: 4;
    }
    .left-title {
      margin: 0;
    }
    .one-map-wrapper__closed {
      height: 100%;
      display: grid;
      place-items: center;
    }
    .left-main {
      height: calc(100% - var(--el-component-size-large));
    }
  }
  & > .right {
    background-color: var(--el-bg-color);
    width: calc(100% - 510px);
    height: 100%;
    padding: 15px;
    .right-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      word-break: keep-all;
      font-family: 'PingFang SC';
      font-style: normal;
      font-weight: 600;
      font-size: 20px;
      line-height: 28px;
      height: 28px;
      color: var(--el-text-color-regular);
      margin-bottom: 30px;
    }
    .right-main {
      height: calc(100% - 58px);
    }
  }
}
.more-close {
  cursor: pointer;
}
</style>
