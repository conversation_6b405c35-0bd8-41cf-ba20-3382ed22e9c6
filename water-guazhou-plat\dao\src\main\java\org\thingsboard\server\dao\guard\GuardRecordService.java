package org.thingsboard.server.dao.guard;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.smartProduction.guard.GuardRecord;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.guard.GuardRecordPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.guard.GuardRecordSaveRequest;

public interface GuardRecordService {
    GuardRecord findById(String id);

    IPage<GuardRecord> findAllConditional(GuardRecordPageRequest request);

    GuardRecord save(GuardRecordSaveRequest entity);

    boolean delete(String id);

}
