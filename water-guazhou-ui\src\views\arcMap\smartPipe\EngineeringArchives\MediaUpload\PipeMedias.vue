<template>
  <el-tabs
    type="border-card"
    class="media-tabs"
  >
    <el-tab-pane label="图片">
      <el-carousel
        v-if="state.imgs.length"
        height="200px"
      >
        <el-carousel-item
          v-for="(item, i) in state.imgs"
          :key="i"
        >
          <el-image
            :src="item.file"
            style="width: 100%; height: 100%"
            :fit="'contain'"
          ></el-image>
        </el-carousel-item>
      </el-carousel>
      <el-empty
        v-else
        :image-size="50"
      />
    </el-tab-pane>
    <el-tab-pane label="视频">
      <Videor
        v-for="(item, i) in state.videos"
        :key="i"
        :url="item.file"
        :height="90"
        :width="160"
        style="margin-right: 10px;"
      ></Videor>

      <el-empty
        v-if="!state.videos.length"
        :image-size="50"
      />
    </el-tab-pane>
    <el-tab-pane label="音频">
      <Voicer
        v-for="(item, i) in state.audios"
        :key="i"
        :name="item.name"
        :url="item.file"
        :show-url="true"
      ></Voicer>
      <el-empty
        v-if="!state.audios.length"
        :image-size="50"
      />
    </el-tab-pane>
    <el-tab-pane label="文档">
      <Voicer
        v-for="(item, i) in state.files"
        :key="i"
        :url="item.file"
        :name="item.name"
        :download="true"
        :hide-btn="true"
        :show-url="true"
      ></Voicer>
      <el-empty
        v-if="!state.files.length"
        :image-size="50"
      />
    </el-tab-pane>
  </el-tabs>
</template>
<script lang="ts" setup>
import { GetGisFileList } from '@/api/mapservice/engineeringDocuments'
import { EPipeFileStatus, EPipeFileType } from '../config'

const props = defineProps<{
  objectid: number 
  layerid: string | number 
}>()
const state = reactive<{
  imgs: { name: string; file: string }[]
  videos: { name: string; file: string }[]
  audios: { name: string; file: string }[]
  files: { name: string; file: string }[]
}>({
  imgs: [],
  videos: [],
  audios: [],
  files: []
})
watch(
  () => props.objectid,
  () => refreshData()
)
const refreshData = () => {
  debugger

  GetGisFileList({
    page: 1,
    size: 999,
    deviceType: props.layerid,
    deviceCode: props.objectid,
    status: EPipeFileStatus.正常
  }).then(res => {
    const data = res.data.data.data || []
    state.imgs = data.filter(item => {
      return item.fileType === EPipeFileType.image
    })
    state.videos = data.filter(item => {
      return item.fileType === EPipeFileType.video
    })
    state.audios = data.filter(item => item.fileType === EPipeFileType.audio)
    state.files = data.filter(item => item.fileType === EPipeFileType.file)
  })
}
defineExpose({
  refreshData
})
</script>
<style lang="scss" scoped>
.media-tabs {
  height: 100%;
  :deep(.el-tabs__content) {
    height: calc(100% - 40px);
    .el-tab-pane {
      height: 100%;
      overflow-y: auto;
    }
  }
}
</style>
