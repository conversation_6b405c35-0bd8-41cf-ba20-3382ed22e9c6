package org.thingsboard.server.dao.smartService.kpi;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.dao.model.sql.smartService.kpi.KpiNormSystemConfig;
import org.thingsboard.server.dao.sql.smartService.kpi.KpiNormSystemConfigMapper;

import java.util.Date;
import java.util.List;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-10-27
 */
@Slf4j
@Service
@Transactional
public class KpiNormSystemConfigServiceImpl implements KpiNormSystemConfigService {
    @Autowired
    private KpiNormSystemConfigMapper kpiNormSystemConfigMapper;

    @Override
    public List<KpiNormSystemConfig> getList(String mainId, String tenantId) {

        QueryWrapper<KpiNormSystemConfig> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("main_id", mainId);
        queryWrapper.orderByAsc("code");
        List<KpiNormSystemConfig> kpiNormSystemConfigList = kpiNormSystemConfigMapper.selectList(queryWrapper);


        return kpiNormSystemConfigList;

    }

    @Override
    public KpiNormSystemConfig save(KpiNormSystemConfig kpiNormSystemConfig) {
        kpiNormSystemConfig.setUpdateTime(new Date());
        if (StringUtils.isBlank(kpiNormSystemConfig.getId())) {
            kpiNormSystemConfigMapper.insert(kpiNormSystemConfig);
        } else {
            kpiNormSystemConfigMapper.updateById(kpiNormSystemConfig);
        }

        return kpiNormSystemConfig;
    }

    @Override
    public boolean check(KpiNormSystemConfig kpiNormSystemConfig) {
        QueryWrapper<KpiNormSystemConfig> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("main_id", kpiNormSystemConfig.getMainId());
        queryWrapper.eq("name", kpiNormSystemConfig.getName());
        List<KpiNormSystemConfig> list = kpiNormSystemConfigMapper.selectList(queryWrapper);
        for (KpiNormSystemConfig kpiNormSystemConfig1 : list) {
            if (!kpiNormSystemConfig1.getId().equals(kpiNormSystemConfig.getId())) {
                return false;
            }
        }
        return true;
    }
}
