import{d as x,c as d,r as f,o as y,g as M,n as P,q as s,i as m,t as V,p as F,_ as R,aq as T,C as B}from"./index-r0dFAfgr.js";import{C as D}from"./index-CcDafpIP.js";import{a as L,j as N}from"./plan-BLf3nu6_.js";import{P as q}from"./config-C9CMv0E7.js";import{u as z}from"./useDistrict-B4Fis32p.js";import"./Point-WxyopZva.js";import"./area-Bpl-8n1R.js";import"./FeatureHelper-Da16o0mu.js";import"./MapView-DaoQedLH.js";import"./widget-BcWKanF2.js";import"./pe-B8dP0-Ut.js";import"./geometryEngine-OGzB5MRq.js";import"./geometryEngineBase-BhsKaODW.js";import"./hydrated-DLkO5ZPr.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./TileLayer-B5vQ99gG.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./commonProperties-DqNQ4F00.js";import"./project-DUuzYgGl.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./IdentifyResult-4DxLVhTm.js";import"./LayerHelper-Cn-iiqxI.js";import"./pipe-nogVzCHG.js";import"./useWaterPoint-Bv0z6ym6.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";const G={class:"onemap-panel-wrapper"},I={class:"table-box"},U=x({__name:"inspectionMaintenance",props:{view:{},menu:{}},emits:["highlightMark","addMarks"],setup(g,{emit:_}){const h=_,l=g,v=d(),a=d([{label:"0",value:"任务总数"},{label:"0 %",value:"完成率"}]),i=f({indexVisible:!0,dataList:[],pagination:{pagerCount:5,layout:"total,sizes,pager",refreshData:({page:t,size:o})=>{i.pagination.page=t,i.pagination.limit=o,n()}},columns:[{label:"任务编号",prop:"code",width:90},{label:"巡检人",prop:"receiveUserName"},{label:"当前状态",prop:"status",width:90,formatter:t=>{var o;return t.status&&((o=q[t.status])==null?void 0:o.text)}}],handleRowClick:t=>{h("highlightMark",l.menu,t),i.currentRow=t,k()}}),b=f({group:[{fields:[{type:"input",field:"layer",appendBtns:[{perm:!0,text:"刷新",click:()=>n()}]}]}],labelPosition:"top",gutter:12}),C=z("viewDiv"),k=async()=>{i.currentRow&&C.add(l.view,i.currentRow.districtAreaId,{goto:!0,ratio:1,showKeyPoint:!0})},n=async()=>{L({page:i.pagination.page||1,size:i.pagination.limit||20}).then(t=>{var o,r,e,p,c,u;i.dataList=((r=(o=t.data)==null?void 0:o.data)==null?void 0:r.data)||[],i.pagination.total=((p=(e=t.data)==null?void 0:e.data)==null?void 0:p.total)||0,a.value[0].label=(((u=(c=t==null?void 0:t.data)==null?void 0:c.data)==null?void 0:u.total)||0)+""})},w=()=>{N().then(t=>{var o,r;a.value[1].label=(((r=(o=t.data)==null?void 0:o.data)==null?void 0:r.percent)||"0.0")+" %"})};return y(()=>{n(),w()}),(t,o)=>{const r=R,e=T;return M(),P("div",G,[s(m(D),{modelValue:m(a),"onUpdate:modelValue":o[0]||(o[0]=p=>V(a)?a.value=p:null),span:12,style:{"margin-bottom":"10px"}},null,8,["modelValue"]),s(r,{ref_key:"refForm",ref:v,config:m(b)},null,8,["config"]),F("div",I,[s(e,{config:m(i)},null,8,["config"])])])}}}),eo=B(U,[["__scopeId","data-v-15ac8bcf"]]);export{eo as default};
