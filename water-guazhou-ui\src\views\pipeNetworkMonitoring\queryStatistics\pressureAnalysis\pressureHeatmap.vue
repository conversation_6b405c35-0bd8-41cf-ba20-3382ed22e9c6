<!--
修复说明：
1. 修复Slider组件的min/max错误：使用Math.max确保max值不小于min值
2. 暂时移除CardSearch组件使用，避免组件导入问题
3. 添加条件渲染v-if，只有在有数据时才显示时间轴
4. 增强错误处理和边界检查
5. 优化初始化流程，确保数据安全加载
6. 参照index.vue实现ArcGIS地图组件
-->
<template>
  <div class="pressure-heatmap-wrapper">
    <!-- 搜索区域暂时移除，避免组件导入问题 -->
    <!-- <CardSearch
      ref="refSearch"
      :config="SearchConfig"
    ></CardSearch> -->
    
    <RightDrawerMap
      ref="refMap"
      :title="'管网压力分布热力图'"
      :windows="state.windows"
      :hide-detail-close="true"
      :hide-layer-list="true"
      :right-drawer-width="450"
      :enableCluster="false"
      @map-loaded="onMapLoaded"
    >
      <div class="heatmap-content">
        <!-- 控制面板 -->
        <div class="control-panel">
          <!-- 时间范围选择 -->
          <div class="time-range-selector">
            <el-date-picker
              v-model="selectedTimeRange"
              type="datetimerange"
              :shortcuts="timeRangeShortcuts"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="MM-DD HH:mm"
              value-format="YYYY-MM-DD HH:mm:ss"
              @change="onTimeRangeChange"
              size="small"
              style="width: 300px"
            />
            <el-button 
              :icon="RefreshRight" 
              @click="refreshTimeRange" 
              size="small"
              type="primary"
            >
              刷新数据
            </el-button>
          </div>
          
          <div class="time-controls">
            <el-button 
              :icon="isPlaying ? VideoPause : VideoPlay"
              :type="isPlaying ? 'danger' : 'primary'"
              @click="togglePlayback"
              size="small"
            >
              {{ isPlaying ? '暂停' : '播放' }}
            </el-button>
            <el-button :icon="RefreshRight" @click="resetTimeSlider" size="small">
              重置
            </el-button>
            <el-select v-model="playbackSpeed" size="small" style="width: 100px">
              <el-option label="0.5x" :value="2000" />
              <el-option label="1x" :value="1000" />
              <el-option label="2x" :value="500" />
              <el-option label="4x" :value="250" />
            </el-select>
          </div>
          
          <div class="display-controls">
            <el-switch
              v-model="state.showHeatmap"
              @change="toggleHeatmapDisplay"
              size="small"
              active-text="热力图"
              inactive-text="热力图"
            />
            <el-switch
              v-model="state.showPoints"
              @change="togglePointsDisplay"
              size="small"
              active-text="监测点"
              inactive-text="监测点"
            />
          </div>
          
          <div class="legend-panel">
            <span class="legend-title">压力值(MPa)：</span>
            <div class="legend-items">
              <div 
                v-for="item in pressureLegend" 
                :key="item.value"
                class="legend-item"
              >
                <div class="legend-color" :style="{ backgroundColor: item.color }"></div>
                <span class="legend-text">{{ item.label }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 统计信息面板 -->
        <div class="stats-panel">
          <div class="current-time">{{ currentDisplayTime }}</div>
          <div class="stats-grid">
            <div class="stat-item">
              <div class="stat-label">平均压力</div>
              <div class="stat-value">{{ currentStats.avgPressure }}MPa</div>
            </div>
            <div class="stat-item">
              <div class="stat-label">最高压力</div>
              <div class="stat-value high">{{ currentStats.maxPressure }}MPa</div>
            </div>
            <div class="stat-item">
              <div class="stat-label">最低压力</div>
              <div class="stat-value low">{{ currentStats.minPressure }}MPa</div>
            </div>
            <div class="stat-item">
              <div class="stat-label">异常点位</div>
              <div class="stat-value warning">{{ currentStats.abnormalCount }}个</div>
            </div>
          </div>
        </div>
        
        <!-- 时间轴控制 -->
        <div class="time-slider-container" v-if="timeSliderData.length > 0">
          <div class="slider-wrapper">
            <div class="time-label">{{ timeRange.start }}</div>
            <el-slider
              v-model="currentTimeIndex"
              :min="0"
              :max="Math.max(0, timeSliderData.length - 1)"
              :step="1"
              :show-tooltip="false"
              @change="onTimeSliderChange"
              class="time-slider"
            />
            <div class="time-label">{{ timeRange.end }}</div>
          </div>
          <div class="time-markers">
            <div 
              v-for="(marker, index) in timeMarkers" 
              :key="index"
              class="time-marker"
              :style="{ left: marker.position + '%' }"
            >
              {{ marker.label }}
            </div>
          </div>
        </div>
      </div>
      <template #detail-header></template>
      <template #detail-default></template>
    </RightDrawerMap>
  </div>
</template>

<script setup>
import { onMounted, reactive, ref, nextTick, computed, onUnmounted } from 'vue'
import { VideoPlay, VideoPause, RefreshRight } from '@element-plus/icons-vue'
import moment from 'moment'
import Point from '@arcgis/core/geometry/Point.js'
import Graphic from '@arcgis/core/Graphic.js'
import PictureMarkerSymbol from '@arcgis/core/symbols/PictureMarkerSymbol.js'
import TextSymbol from '@arcgis/core/symbols/TextSymbol.js'
import SimpleMarkerSymbol from '@arcgis/core/symbols/SimpleMarkerSymbol.js'
import FeatureLayer from '@arcgis/core/layers/FeatureLayer.js'
import HeatmapRenderer from '@arcgis/core/renderers/HeatmapRenderer.js'
import Field from '@arcgis/core/layers/support/Field.js'
import { useBusinessStore } from '@/store'
import RightDrawerMap from '@/views/arcMap/components/common/RightDrawerMap.vue'
import { bindViewClick, gotoAndHighLight } from '@/utils/MapHelper'
import { GetStationList, GetStationRealTimeDetail } from '@/api/shuiwureports/zhandian'
import { getStationImageUrl } from '@/utils/URLHelper'
import useStation from '@/hooks/station/useStation'

const refMap = ref()
let mapView = null
let heatmapLayer = null // 热力图图层
let pointsLayer = null // 监测点图层

const { getAllStationOption } = useStation()
const SearchConfig = reactive({
  defaultParams: {
    stationIds: [],
    alarmStatus: '',
    time: moment().format('YYYY-MM-DD'),
    reportType: 'day',
    timeRange: [
      moment().subtract(7, 'days').format('YYYY-MM-DD'),
      moment().format('YYYY-MM-DD')
    ]
  },
})
// 响应式状态管理
const state = reactive({
  stationList: [],
  selectedStations: [],
  isPlaying: false,
  currentTimeIndex: 0,
  playbackSpeed: 1000,
  playbackTimer: null,
  timeSliderData: [], // 初始化为空数组，后续在initTimeSlider中填充
  currentDisplayTime: moment().format('YYYY-MM-DD HH:mm:ss'),
  timeRange: {
    start: moment().subtract(1, 'days').format('MM-DD HH:mm'),
    end: moment().format('MM-DD HH:mm')
  },
  currentStats: {
    avgPressure: '0.25',
    maxPressure: '0.35',
    minPressure: '0.15',
    abnormalCount: 3
  },
  pressureLegend: [
    { value: 0.1, color: '#0066CC', label: '< 0.1' },
    { value: 0.2, color: '#00CCFF', label: '0.1-0.2' },
    { value: 0.3, color: '#00FF99', label: '0.2-0.3' },
    { value: 0.4, color: '#FFFF00', label: '0.3-0.4' },
    { value: 0.5, color: '#FF6600', label: '0.4-0.5' },
    { value: 0.6, color: '#FF0000', label: '> 0.5' }
  ],
  timeMarkers: [],
  pressurePoints: [],
  windows: [],
  mapGraphics: [], // 存储地图上的图形对象
  showHeatmap: true, // 控制热力图显示
  showPoints: true,   // 控制监测点显示
  fixedMonitoringPoints: [], // 存储固定的监测点位置
  selectedTimeRange: [
    moment().subtract(7, 'days').format('YYYY-MM-DD HH:mm:ss'),
    moment().format('YYYY-MM-DD HH:mm:ss')
  ],
  timeRangeShortcuts: [
    {
      text: '最近一周',
      value: [moment().subtract(7, 'days'), moment()]
    },
    {
      text: '最近一个月',
      value: [moment().subtract(30, 'days'), moment()]
    },
    {
      text: '最近三个月',
      value: [moment().subtract(90, 'days'), moment()]
    },
    {
      text: '最近一年',
      value: [moment().subtract(365, 'days'), moment()]
    }
  ]
})

// 计算属性
const isPlaying = computed(() => state.isPlaying)
const currentTimeIndex = computed({
  get: () => state.currentTimeIndex,
  set: (val) => {
    // 确保值在有效范围内
    if (state.timeSliderData.length > 0) {
      state.currentTimeIndex = Math.max(0, Math.min(val, state.timeSliderData.length - 1))
    }
  }
})
const timeSliderData = computed(() => state.timeSliderData)
const currentDisplayTime = computed(() => state.currentDisplayTime)
const timeRange = computed(() => state.timeRange)
const currentStats = computed(() => state.currentStats)
const pressureLegend = computed(() => state.pressureLegend)
const timeMarkers = computed(() => state.timeMarkers)
const playbackSpeed = computed({
  get: () => state.playbackSpeed,
  set: (val) => state.playbackSpeed = val
})

// 时间范围选择器的选项
const selectedTimeRange = computed({
  get: () => state.selectedTimeRange,
  set: (val) => state.selectedTimeRange = val
})

// 时间范围快捷选择选项
const timeRangeShortcuts = computed(() => [
  {
    text: '最近一周',
    value: [
      moment().subtract(7, 'days').format('YYYY-MM-DD HH:mm:ss'),
      moment().format('YYYY-MM-DD HH:mm:ss')
    ]
  },
  {
    text: '最近一个月',
    value: [
      moment().subtract(30, 'days').format('YYYY-MM-DD HH:mm:ss'),
      moment().format('YYYY-MM-DD HH:mm:ss')
    ]
  },
  {
    text: '最近三个月',
    value: [
      moment().subtract(90, 'days').format('YYYY-MM-DD HH:mm:ss'),
      moment().format('YYYY-MM-DD HH:mm:ss')
    ]
  },
  {
    text: '最近一年',
    value: [
      moment().subtract(365, 'days').format('YYYY-MM-DD HH:mm:ss'),
      moment().format('YYYY-MM-DD HH:mm:ss')
    ]
  }
])

// 更新地图显示
const updateMapDisplay = async () => {
  if (!mapView || !state.timeSliderData.length) return
  
  const currentData = state.timeSliderData[state.currentTimeIndex]
  state.currentDisplayTime = currentData.time
  
  // 生成新的压力数据
  const pressureData = generatePressurePointsData()
  
  // 更新统计数据
  const pressures = pressureData.map(p => parseFloat(p.pressure))
  state.currentStats.avgPressure = (pressures.reduce((a, b) => a + b, 0) / pressures.length).toFixed(2)
  state.currentStats.maxPressure = Math.max(...pressures).toFixed(2)
  state.currentStats.minPressure = Math.min(...pressures).toFixed(2)
  state.currentStats.abnormalCount = pressures.filter(p => p > 0.4 || p < 0.15).length
  
  // 更新热力图和监测点标记
  await updateHeatmapLayer(pressureData)
  updateMapMarkers(pressureData)
}

// 创建热力图图层
const createHeatmapLayer = () => {
  try {
    if (!mapView || !mapView.spatialReference) {
      console.warn('地图视图未准备好，延迟创建热力图图层')
      return
    }

    // 定义字段结构
    const fields = [
      new Field({
        name: 'ObjectID',
        alias: 'ObjectID',
        type: 'oid'
      }),
      new Field({
        name: 'pressure',
        alias: '压力值',
        type: 'double'
      }),
      new Field({
        name: 'name',
        alias: '监测点名称',
        type: 'string'
      })
    ]

    // 创建热力图渲染器
    const heatmapRenderer = new HeatmapRenderer({
      field: 'pressure',
      colorStops: [
        { color: [0, 102, 204, 0], ratio: 0 },
        { color: [0, 204, 255, 0.8], ratio: 0.2 },
        { color: [0, 255, 153, 0.8], ratio: 0.4 },
        { color: [255, 255, 0, 0.8], ratio: 0.6 },
        { color: [255, 102, 0, 0.8], ratio: 0.8 },
        { color: [255, 0, 0, 1], ratio: 1 }
      ],
      radius: 25,
      maxDensity: 0.008,
      minDensity: 0,
      blurRadius: 15
    })

    // 创建图层
    heatmapLayer = new FeatureLayer({
      source: [],
      fields: fields,
      objectIdField: 'ObjectID',
      geometryType: 'point',
      spatialReference: mapView.spatialReference,
      renderer: heatmapRenderer,
      popupEnabled: false,
      title: '管网压力热力图'
    })

    mapView.map.add(heatmapLayer)
    console.log('热力图图层创建成功')
  } catch (error) {
    console.error('创建热力图图层失败:', error)
  }
}

// 更新热力图数据
const updateHeatmapLayer = async (pressureData) => {
  if (!state.showHeatmap) return
  
  // 如果热力图图层不存在或未加载，尝试重新创建
  if (!heatmapLayer) {
    console.warn('热力图图层不存在，重新创建')
    createHeatmapLayer()
    // 等待图层创建完成
    await new Promise(resolve => setTimeout(resolve, 500))
    if (!heatmapLayer) {
      console.error('热力图图层创建失败，跳过更新')
      return
    }
  }

  // 生成更密集的热力图数据点
  const heatmapFeatures = []
  let objectId = 1

  // 基于现有监测点扩展热力图数据
  pressureData.forEach(point => {
    const baseLng = point.lng
    const baseLat = point.lat
    
    // 根据压力值动态调整热力点数量，压力越高生成越多热力点
    const heatPointCount = Math.max(15, Math.min(35, Math.floor(point.pressure * 50)))
    
    // 在每个监测点周围生成数据点，数量基于压力值
    for (let i = 0; i < heatPointCount; i++) {
      // 根据压力值调整分布范围，压力高的点分布更集中
      const spreadFactor = Math.max(0.008, 0.02 - point.pressure * 0.015)
      const offsetLng = (Math.random() - 0.5) * spreadFactor
      const offsetLat = (Math.random() - 0.5) * spreadFactor
      
      // 热力强度基于原始压力值，增加时间相关的波动
      const timeIndex = state.currentTimeIndex
      const timeVariation = Math.sin((timeIndex + point.id * 2) / 12) * 0.05
      const pressureIntensity = Math.max(0.05, Math.min(0.8, point.pressure + timeVariation))
      
      heatmapFeatures.push(new Graphic({
        geometry: new Point({
          longitude: baseLng + offsetLng,
          latitude: baseLat + offsetLat,
          spatialReference: mapView.spatialReference
        }),
        attributes: {
          ObjectID: objectId++,
          pressure: pressureIntensity,
          name: `${point.name}_热力点_${i + 1}`
        }
      }))
    }
    
    // 为高压力区域额外增加热力强度
    if (point.pressure > 0.35) {
      for (let i = 0; i < 10; i++) {
        const offsetLng = (Math.random() - 0.5) * 0.005 // 更紧密的分布
        const offsetLat = (Math.random() - 0.5) * 0.005
        
        const highPressureIntensity = point.pressure + Math.random() * 0.1
        
        heatmapFeatures.push(new Graphic({
          geometry: new Point({
            longitude: baseLng + offsetLng,
            latitude: baseLat + offsetLat,
            spatialReference: mapView.spatialReference
          }),
          attributes: {
            ObjectID: objectId++,
            pressure: Math.min(0.8, highPressureIntensity),
            name: `${point.name}_高压热力_${i + 1}`
          }
        }))
      }
    }
  })

  // 在区域中心增加基础热力背景，让整体热力图更连续
  const centerLng = (95.739 + 95.830) / 2
  const centerLat = (40.491 + 40.539) / 2
  const timeIndex = state.currentTimeIndex
  
  for (let i = 0; i < 20; i++) {
    const offsetLng = (Math.random() - 0.5) * 0.08
    const offsetLat = (Math.random() - 0.5) * 0.04
    
    // 背景热力值随时间轻微波动
    const backgroundPressure = 0.18 + Math.sin((timeIndex + i) / 15) * 0.08
    
    heatmapFeatures.push(new Graphic({
      geometry: new Point({
        longitude: centerLng + offsetLng,
        latitude: centerLat + offsetLat,
        spatialReference: mapView.spatialReference
      }),
      attributes: {
        ObjectID: objectId++,
        pressure: Math.max(0.05, backgroundPressure),
        name: `背景热力_${i + 1}`
      }
    }))
  }

  // 更新图层数据源
  try {
    // 使用applyEdits方法更新数据，避免MemorySource错误
    await heatmapLayer.applyEdits({
      deleteFeatures: heatmapLayer.source.items.toArray(), // 删除所有现有要素
      addFeatures: heatmapFeatures // 添加新要素
    })
  } catch (error) {
    console.warn('热力图数据更新失败:', error)
    // 如果applyEdits失败，尝试重新创建图层
    if (mapView.map.findLayerById(heatmapLayer.id)) {
      mapView.map.remove(heatmapLayer)
    }
    createHeatmapLayer()
    
    // 重新添加数据到新图层
    setTimeout(async () => {
      try {
        await heatmapLayer.applyEdits({
          addFeatures: heatmapFeatures
        })
      } catch (retryError) {
        console.warn('重试热力图数据添加失败:', retryError)
      }
    }, 100)
  }
}

// 更新地图标记
const updateMapMarkers = (pressureData) => {
  if (!mapView) return
  
  // 清除现有的图形
  mapView.graphics.removeAll()
  
  // 如果不显示监测点，直接返回
  if (!state.showPoints) return
  
  pressureData.forEach((point, index) => {
    const location = new Point({
      longitude: point.lng, // 直接使用生成的经度
      latitude: point.lat,  // 直接使用生成的纬度
      spatialReference: mapView.spatialReference
    })
    
    // 根据压力值选择颜色和大小
    const color = getPressureColor(point.pressure)
    const size = Math.max(point.pressure * 40, 12)
    
    // 创建监测点标记
    const markG = new Graphic({
      geometry: location,
      symbol: new SimpleMarkerSymbol({
        style: 'circle',
        color: color,
        size: size,
        outline: {
          color: [255, 255, 255],
          width: 2
        }
      }),
      attributes: {
        name: point.name,
        pressure: point.pressure,
        status: point.pressure > 0.4 ? '偏高' : point.pressure < 0.15 ? '偏低' : '正常'
      }
    })
    
    // 创建文本标签
    const markText = new Graphic({
      geometry: location,
      symbol: new TextSymbol({
        text: `${point.name}\n${point.pressure}MPa`,
        color: '#333',
        font: {
          size: 8,
          family: 'sans-serif'
        },
        yoffset: -20,
        haloColor: 'white',
        haloSize: 1
      })
    })
    
    mapView.graphics.addMany([markG, markText])
  })
}

// 处理地图标记点击
const handleMarkClick = async (attributes) => {
  if (!attributes) return
  
  const values = [
    { label: '监测点名称', value: attributes.name },
    { label: '压力值', value: `${attributes.pressure}MPa` },
    { label: '状态', value: attributes.status },
    { label: '更新时间', value: state.currentDisplayTime }
  ]
  
  state.windows.length = 0
  state.windows.push({
    visible: false,
    x: 0, // 这些会在点击时设置
    y: 0,
    offsetY: -30,
    title: attributes.name,
    attributes: {
      values,
      id: attributes.name
    }
  })
  
  await nextTick()
  refMap.value?.openPop(attributes.name)
}

// 刷新数据
const refreshData = async () => {
  console.log('刷新压力分布数据')
  await updateMapDisplay()
}

// 导出功能
const handleExport = () => {
  console.log('导出压力分布图')
}

// 注意：暂时移除搜索配置，因为CardSearch组件导入存在问题
// 如果需要搜索功能，可以后续重新启用CardSearch组件

// 控制热力图显示
const toggleHeatmapDisplay = async (show) => {
  if (heatmapLayer) {
    heatmapLayer.visible = show
    
    // 如果开启热力图且当前没有数据，则生成数据
    if (show && state.timeSliderData.length > 0) {
      const pressureData = generatePressurePointsData()
      await updateHeatmapLayer(pressureData)
    }
  }
}

// 控制监测点显示
const togglePointsDisplay = (show) => {
  if (!mapView) return
  
  if (show) {
    // 如果需要显示，重新生成监测点
    const pressureData = generatePressurePointsData()
    updateMapMarkers(pressureData)
  } else {
    // 如果不需要显示，清除所有图形
    mapView.graphics.removeAll()
  }
}

// 初始化地图
const initPressureMap = () => {
  if (!mapView) return
  
  // 初始化固定监测点位置
  initFixedMonitoringPoints()
  
  // 确保地图视图完全加载后再创建热力图图层
  if (mapView.ready) {
    createHeatmapLayer()
  } else {
    // 如果地图还没准备好，等待ready事件
    mapView.when(() => {
      createHeatmapLayer()
    }).catch(error => {
      console.error('地图初始化失败:', error)
    })
  }
  
  // 初始化时间轴
  initTimeSlider()
  
  // 绑定地图点击事件
  bindViewClick(mapView, (res) => {
    const result = res.results?.[0]
    if (!result) return
    if (result.type === 'graphic') {
      const attributes = result.graphic?.attributes
      handleMarkClick(attributes)
    }
  })
}

// 生成管网背景数据
const generatePipeNetworkData = () => {
  const data = []
  for (let i = 0; i < 80; i++) {
    data.push([Math.random() * 100, Math.random() * 100, 0])
  }
  return data
}

// 初始化固定的监测点位置
const initFixedMonitoringPoints = () => {
  if (state.fixedMonitoringPoints.length > 0) return // 已经初始化过了
  
  // 定义地理坐标范围
  const geoRange = {
    minLng: 95.739,  // 左下角经度
    maxLng: 95.830,  // 右上角经度
    minLat: 40.491,  // 左下角纬度
    maxLat: 40.539   // 右上角纬度
  }
  
  // 生成30个固定位置的监测点
  for (let i = 0; i < 30; i++) {
    const randomLng = geoRange.minLng + Math.random() * (geoRange.maxLng - geoRange.minLng)
    const randomLat = geoRange.minLat + Math.random() * (geoRange.maxLat - geoRange.minLat)
    
    state.fixedMonitoringPoints.push({
      id: i + 1,
      name: `监测点${i + 1}`,
      lng: parseFloat(randomLng.toFixed(6)),
      lat: parseFloat(randomLat.toFixed(6))
    })
  }
}

// 生成压力监测点数据
const generatePressurePointsData = () => {
  const data = []
  
  // 确保固定监测点已初始化
  if (state.fixedMonitoringPoints.length === 0) {
    initFixedMonitoringPoints()
  }
  
  // 基于固定位置生成带时间变化的压力数据
  state.fixedMonitoringPoints.forEach((point, index) => {
    // 基于时间生成动态压力值
    const timeIndex = state.currentTimeIndex
    const baseValue = 0.25 + Math.sin((timeIndex + index * 3) / 10) * 0.15
    const randomVariation = (Math.random() - 0.5) * 0.1
    const pressure = Math.max(0.05, Math.min(0.6, baseValue + randomVariation))
    
    data.push({
      id: point.id,
      name: point.name,
      lng: point.lng,
      lat: point.lat,
      pressure: parseFloat(pressure.toFixed(2))
    })
  })
  
  return data
}

// 根据压力值获取颜色
const getPressureColor = (pressure) => {
  if (pressure < 0.1) return '#0066CC'
  if (pressure < 0.2) return '#00CCFF'  
  if (pressure < 0.3) return '#00FF99'
  if (pressure < 0.4) return '#FFFF00'
  if (pressure < 0.5) return '#FF6600'
  return '#FF0000'
}

// 初始化时间轴
const initTimeSlider = () => {
  try {
    const timeData = []
    const markers = []
    
    // 解析选择的时间范围
    const startTime = moment(state.selectedTimeRange[0])
    const endTime = moment(state.selectedTimeRange[1])
    
    // 计算时间间隔，根据总时长决定间隔
    const totalHours = endTime.diff(startTime, 'hours')
    let intervalHours = 1 // 默认1小时间隔
    
    // 根据总时长调整间隔
    if (totalHours > 168) { // 超过7天
      intervalHours = 6 // 6小时间隔
    } else if (totalHours > 72) { // 超过3天
      intervalHours = 3 // 3小时间隔
    } else if (totalHours > 24) { // 超过1天
      intervalHours = 2 // 2小时间隔
    }
    
    // 生成时间数据点
    const timePoints = Math.min(Math.ceil(totalHours / intervalHours), 48) // 最多48个时间点
    
    for (let i = 0; i < timePoints; i++) {
      const currentTime = startTime.clone().add(i * intervalHours, 'hours')
      
      // 确保不超过结束时间
      if (currentTime.isAfter(endTime)) {
        break
      }
      
      timeData.push({
        time: currentTime.format('YYYY-MM-DD HH:mm:ss'),
        timeLabel: currentTime.format('MM-DD HH:mm')
      })
      
      // 每隔几个数据点添加一个时间标记
      const markerInterval = Math.max(1, Math.floor(timePoints / 6))
      if (i % markerInterval === 0) {
        markers.push({
          position: (i / (timePoints - 1)) * 100,
          label: currentTime.format('MM-DD HH:mm')
        })
      }
    }
    
    // 确保包含结束时间点
    if (timeData.length > 0 && !moment(timeData[timeData.length - 1].time).isSame(endTime, 'hour')) {
      timeData.push({
        time: endTime.format('YYYY-MM-DD HH:mm:ss'),
        timeLabel: endTime.format('MM-DD HH:mm')
      })
      
      markers.push({
        position: 100,
        label: endTime.format('MM-DD HH:mm')
      })
    }
    
    // 更新时间范围显示
    state.timeRange = {
      start: startTime.format('MM-DD HH:mm'),
      end: endTime.format('MM-DD HH:mm')
    }
    
    // 确保数据不为空
    if (timeData.length > 0) {
      state.timeSliderData = timeData
      state.timeMarkers = markers
      state.currentTimeIndex = timeData.length - 1
      
      // 停止当前播放
      stopPlayback()
      
      // 延迟更新地图，确保DOM已经渲染
      nextTick(async () => {
        await updateMapDisplay()
      })
    }
  } catch (error) {
    console.error('初始化时间轴失败:', error)
    // 如果初始化失败，设置默认值
    state.timeSliderData = []
    state.timeMarkers = []
    state.currentTimeIndex = 0
  }
}

// 播放控制
const togglePlayback = () => {
  state.isPlaying = !state.isPlaying
  if (state.isPlaying) {
    startPlayback()
  } else {
    stopPlayback()
  }
}

const startPlayback = () => {
  if (state.playbackTimer) clearInterval(state.playbackTimer)
  
  state.playbackTimer = setInterval(() => {
    if (state.currentTimeIndex < state.timeSliderData.length - 1) {
      state.currentTimeIndex++
      updateMapDisplay() // 不使用await避免阻塞定时器
    } else {
      stopPlayback()
    }
  }, state.playbackSpeed)
}

const stopPlayback = () => {
  state.isPlaying = false
  if (state.playbackTimer) {
    clearInterval(state.playbackTimer)
    state.playbackTimer = null
  }
}

const resetTimeSlider = async () => {
  stopPlayback()
  state.currentTimeIndex = 0
  await updateMapDisplay()
}

const onTimeSliderChange = async (value) => {
  // 确保值在有效范围内
  if (state.timeSliderData.length > 0) {
    state.currentTimeIndex = Math.max(0, Math.min(value, state.timeSliderData.length - 1))
    await updateMapDisplay()
  }
}

// 地图加载完成回调
const onMapLoaded = async (view) => {
  mapView = view
  refMap.value?.toggleCustomDetail(false)
  
  // 获取监测站列表
  try {
    const res = await GetStationList({
      page: 1,
      size: 999,
      type: '压力监测站',
      projectId: useBusinessStore().selectedProject?.value
    })
    
    const data = res.data.data || []
    state.stationList = data.map(d => ({
      label: d.name,
      value: d.id
    }))
  } catch (error) {
    console.error('获取监测站列表失败:', error)
  }
  
  // 延迟初始化地图和时间轴，确保地图完全加载
  setTimeout(() => {
    initPressureMap()
  }, 1000)
}

onMounted(() => {
  // 地图加载在 onMapLoaded 回调中处理
  window.addEventListener('resize', () => {
    if (mapView) {
      // ArcGIS地图自动处理resize
    }
  })
})

onUnmounted(() => {
  if (state.playbackTimer) {
    clearInterval(state.playbackTimer)
  }
  
  // 清理热力图图层
  if (heatmapLayer && mapView) {
    mapView.map?.remove(heatmapLayer)
    heatmapLayer = null
  }
  
  window.removeEventListener('resize', () => {})
})

const onTimeRangeChange = (value) => {
  if (!value || value.length !== 2) return
  
  state.selectedTimeRange = value
  
  // 重新初始化时间轴
  initTimeSlider()
}

const refreshTimeRange = async () => {
  // 重新初始化时间轴并更新显示
  initTimeSlider()
}
</script>

<style lang="scss" scoped>
.pressure-heatmap-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
}

.heatmap-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 15px;
  box-sizing: border-box;
  
  .control-panel {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #f0f0f0;
    margin-bottom: 15px;
    flex-wrap: wrap;
    gap: 10px;
    
    .time-range-selector {
      display: flex;
      align-items: center;
      gap: 8px;
      
      :deep(.el-date-editor) {
        --el-date-editor-width: 300px;
      }
      
      @media (max-width: 768px) {
        flex-direction: column;
        align-items: flex-start;
        gap: 6px;
        
        :deep(.el-date-editor) {
          --el-date-editor-width: 280px;
        }
      }
    }
    
    .time-controls {
      display: flex;
      align-items: center;
      gap: 8px;
    }
    
    .display-controls {
      display: flex;
      align-items: center;
      gap: 8px;
      
      :deep(.el-switch) {
        --el-switch-on-color: #667eea;
        --el-switch-off-color: #dcdfe6;
      }
      
      :deep(.el-switch__label) {
        font-size: 11px;
        color: #606266;
        font-weight: 500;
      }
    }
    
    .legend-panel {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .legend-title {
        font-size: 12px;
        font-weight: 600;
        color: #333;
      }
      
      .legend-items {
        display: flex;
        gap: 6px;
        
        .legend-item {
          display: flex;
          align-items: center;
          gap: 3px;
          
          .legend-color {
            width: 12px;
            height: 10px;
            border-radius: 2px;
            border: 1px solid rgba(0, 0, 0, 0.1);
          }
          
          .legend-text {
            font-size: 10px;
            color: #555;
          }
        }
      }
    }
  }
  
  .stats-panel {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    
    .current-time {
      font-size: 14px;
      font-weight: bold;
      color: #333;
      text-align: center;
      margin-bottom: 12px;
      padding-bottom: 8px;
      border-bottom: 1px solid #eee;
    }
    
    .stats-grid {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 10px;
      
      .stat-item {
        text-align: center;
        
        .stat-label {
          font-size: 10px;
          color: #666;
          margin-bottom: 3px;
          font-weight: 500;
        }
        
        .stat-value {
          font-size: 12px;
          font-weight: bold;
          color: #333;
          
          &.high {
            color: #ff6b6b;
          }
          
          &.low {
            color: #4ecdc4;
          }
          
          &.warning {
            color: #feca57;
          }
        }
      }
    }
  }
  
  .time-slider-container {
    margin-top: auto;
    padding-top: 15px;
    
    .slider-wrapper {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-bottom: 8px;
      
      .time-label {
        font-size: 10px;
        color: #666;
        font-weight: 500;
        min-width: 60px;
        text-align: center;
      }
      
      .time-slider {
        flex: 1;
        
        :deep(.el-slider__runway) {
          height: 6px;
          background: linear-gradient(90deg, #e4e7ed 0%, #f5f7fa 100%);
          border-radius: 3px;
        }
        
        :deep(.el-slider__bar) {
          background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
          border-radius: 3px;
          box-shadow: 0 2px 6px rgba(102, 126, 234, 0.3);
        }
        
        :deep(.el-slider__button) {
          width: 16px;
          height: 16px;
          background: white;
          border: 2px solid #667eea;
          box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
          transition: all 0.3s ease;
          
          &:hover {
            transform: scale(1.2);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
          }
        }
      }
    }
    
    .time-markers {
      position: relative;
      height: 16px;
      margin: 0 60px;
      
      .time-marker {
        position: absolute;
        font-size: 9px;
        color: #999;
        transform: translateX(-50%);
        white-space: nowrap;
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .heatmap-content {
    .control-panel {
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;
      
      .time-range-selector {
        width: 100%;
        
        :deep(.el-date-editor) {
          --el-date-editor-width: 100%;
          max-width: 350px;
        }
      }
      
      .time-controls,
      .display-controls {
        flex-wrap: wrap;
      }
      
      .legend-panel {
        .legend-items {
          flex-wrap: wrap;
        }
      }
    }
    
    .stats-panel {
      .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 8px;
      }
    }
  }
}

@media (max-width: 768px) {
  .heatmap-content {
    padding: 10px;
    
    .control-panel {
      .time-range-selector {
        flex-direction: column;
        align-items: stretch;
        
        :deep(.el-date-editor) {
          --el-date-editor-width: 100%;
        }
      }
      
      .time-controls,
      .display-controls {
        width: 100%;
        justify-content: space-between;
      }
      
      .legend-panel {
        width: 100%;
        
        .legend-items {
          gap: 4px;
          
          .legend-item {
            .legend-text {
              font-size: 9px;
            }
          }
        }
      }
    }
    
    .stats-panel {
      .stats-grid {
        grid-template-columns: 1fr 1fr;
      }
    }
    
    .time-slider-container {
      .time-markers {
        margin: 0 30px;
      }
    }
  }
}

// 地图弹窗样式优化
:deep(.esri-popup) {
  .esri-popup__header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
  }
  
  .esri-popup__content {
    font-size: 12px;
  }
}
</style> 