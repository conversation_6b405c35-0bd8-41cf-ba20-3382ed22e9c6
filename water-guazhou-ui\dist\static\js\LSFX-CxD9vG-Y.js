import{_ as x}from"./index-BlG8PIOK.js";import"./index-0NlGN6gS.js";import{d as y,r as b,bu as B,g as r,n as i,i as _,h as L,F as k,p as t,aB as m,aJ as f,aw as h,bh as u,C as S}from"./index-r0dFAfgr.js";import{c as w}from"./statistics-CeyexT_5.js";const C={class:"lsfx-wrapper overlay-y"},D={ref:"refTable"},F={class:"lsfx-item__inner"},O={class:"progress"},V={class:"text"},M={class:"percent"},N={class:"value"},X={class:"progress-bar"},z={key:1,class:"empty"},E=y({__name:"LSFX",props:{classOption:{},isDev:{type:Boolean},config:{}},setup(v){const n=v,o=b({list:[{sort:1,name:"百花村&二儿山",percent:99.7},{sort:2,name:"隆中山",percent:37.4},{sort:3,name:"钢铁街道",percent:29.1},{sort:4,name:"高新片区",percent:17.9},{sort:5,name:"百花片区",percent:9.4}],classOption:n.classOption??{step:.2,limitMoveNum:6}}),g=async()=>{n.isDev||w().then(c=>{var a;const s=((a=c.data)==null?void 0:a.data)||{};o.list=s.x.map((e,l)=>{var d;return{sort:l+1,name:e,percent:((d=s.y)==null?void 0:d[l])||0}})}).catch(()=>{})};return B(()=>{var c;if((c=n==null?void 0:n.config)!=null&&c.source){const s=n.config.config.defaultValue||{};s!=null&&s.List&&(o.list=s==null?void 0:s.List.sort((a,e)=>e.percent-a.percent).map((a,e)=>({sort:e+1,name:a.name,percent:a.percent})))}else g()}),(c,s)=>{const a=x;return r(),i("div",C,[_(o).list.length?(r(),L(a,{key:0,data:_(o).list,"class-option":_(o).classOption},{default:k(()=>[t("ul",D,[(r(!0),i(m,null,f(_(o).list,(e,l)=>(r(),i("li",{key:l,class:"lsfx-item"},[t("div",F,[t("div",{class:h(["lsfx-item__icon","sort"+l])},[t("span",null,u(e.sort),1)],2),t("div",O,[t("div",V,[t("span",null,u(e.name),1),t("div",M,[t("span",N,u(e.percent),1),s[0]||(s[0]=t("span",{class:"unit"},"%",-1))])]),t("div",X,[(r(),i(m,null,f(50,(d,p)=>t("div",{key:p,class:h(["progress-bar__item",[e.percent<p*2?"":p<17?"lightblue":p<33?"yellow":"red"]])},null,2)),64))])])])]))),128))],512)]),_:1},8,["data","class-option"])):(r(),i("div",z,"暂无信息"))])}}}),R=S(E,[["__scopeId","data-v-12a1f134"]]);export{R as default};
