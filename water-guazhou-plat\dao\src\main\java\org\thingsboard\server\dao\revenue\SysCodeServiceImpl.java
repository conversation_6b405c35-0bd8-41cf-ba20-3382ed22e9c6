package org.thingsboard.server.dao.revenue;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.DTO.SysCodeDTO;
import org.thingsboard.server.dao.model.sql.revenue.SysCode;
import org.thingsboard.server.dao.model.sql.revenue.SysCodeDetail;
import org.thingsboard.server.dao.sql.revenue.SysCodeDetailRepository;
import org.thingsboard.server.dao.sql.revenue.SysCodeRepository;

import java.util.*;

@Slf4j
@Service(value = "SysCodeServiceParent")
public class SysCodeServiceImpl implements SysCodeService {

    @Autowired
    private SysCodeRepository sysCodeRepository;

    @Autowired
    private SysCodeDetailRepository sysCodeDetailRepository;

    @Override
    public Object detailList(String mainCodes, TenantId tenantId) {
        List<String> mainCodeList = Arrays.asList(mainCodes.split(","));
        JSONObject result = new JSONObject();

        for (String mainCode : mainCodeList) {
            // 查询主表
            SysCode sysCode = sysCodeRepository.findByCode(mainCode);
            if (sysCode == null) {
                return null;
            }

            List<SysCodeDetail> details = sysCodeDetailRepository.findByCode(mainCode);

            SysCodeDTO dto = new SysCodeDTO();
            dto.setCode(mainCode);
            dto.setName(sysCode.getName());
            dto.setDetails(details);

            result.put(mainCode, dto);
        }

        return result;
    }

    @Override
    public Map<String, List<String>> getCustParams(String tenantId) {
        // 用水类别 行业分类  用户分类 水表厂家 水表类型 水表型号 水表口径 水费结算方式 代扣银行 水表安装位置
        String[] mainCodeArr = {"WaterCategoryType", "IndustryCategoryType", "UserType", "MeterBrand", "MeterType", "MeterCaliber", "PaymentMethod", "BankType", "MeterPositionType"};
//        List<SysCodeDetail> details = sysCodeDetailRepository.findByCodeInAndTenantIdOrderByOrderNumber(mainCodeArr, tenantId);
        List<SysCodeDetail> details = sysCodeDetailRepository.findByCodeInOrderByOrderNumber(mainCodeArr);
        Map<String, List<String>> result = new HashMap<>();
        for (SysCodeDetail sysCodeDetail : details) {
            if (result.get(sysCodeDetail.getCode()) == null) {
                result.put(sysCodeDetail.getCode(), new ArrayList<>());
            }
            result.get(sysCodeDetail.getCode()).add(sysCodeDetail.getName());
        }

        return result;
    }

    @Override
    public Map<String, Map<String, String>> getCustNameMap(String tenantId) {
        // 用水类别 行业分类  用户分类 水表厂家 水表类型 水表型号 水表口径 水费结算方式 代扣银行 水表安装位置
        String[] mainCodeArr = {"WaterCategoryType", "IndustryCategoryType", "UserType", "MeterBrand", "MeterType", "MeterCaliber", "PaymentMethod", "BankType", "MeterPositionType"};
//        List<SysCodeDetail> details = sysCodeDetailRepository.findByCodeInAndTenantIdOrderByOrderNumber(mainCodeArr, tenantId);
        List<SysCodeDetail> details = sysCodeDetailRepository.findByCodeInOrderByOrderNumber(mainCodeArr);
        Map<String, Map<String, String>> result =new HashMap<>();
        for (SysCodeDetail sysCodeDetail : details) {
            if (result.get(sysCodeDetail.getCode()) == null) {
                result.put(sysCodeDetail.getCode(), new HashMap<>());
            }
            result.get(sysCodeDetail.getCode()).put(sysCodeDetail.getName(), sysCodeDetail.getKey());
        }

        return result;
    }

    @Override
    public List<SysCode> findAll(TenantId tenantId) {
        return sysCodeRepository.findByTenantIdOrderByOrderNumber(UUIDConverter.fromTimeUUID(tenantId.getId()));
    }

    @Override
    public void saveOption(SysCodeDetail option) {
        sysCodeDetailRepository.save(option);
    }

    @Override
    public PageData<SysCodeDetail> findOptions(int page, int size, String mainCode, String name, TenantId tenantId) {
        PageRequest pageRequest = new PageRequest(page - 1, size, new Sort("orderNumber"));
        Page<SysCodeDetail> pageResult = sysCodeDetailRepository.findOptions(mainCode, name, UUIDConverter.fromTimeUUID(tenantId.getId()), pageRequest);
        return new PageData<>(pageResult.getTotalElements(), pageResult.getContent());
    }

    @Override
    public void deleteOption(String optionId) {
        sysCodeDetailRepository.delete(optionId);
    }

    @Override
    public List<SysCodeDetail> detailList(String code) {
        return sysCodeDetailRepository.findByCode(code);
    }
}
