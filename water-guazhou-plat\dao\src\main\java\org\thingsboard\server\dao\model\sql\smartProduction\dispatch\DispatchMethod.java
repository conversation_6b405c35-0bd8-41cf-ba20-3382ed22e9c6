package org.thingsboard.server.dao.model.sql.smartProduction.dispatch;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.sql.project.ProjectMapper;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseUsername;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseViaMapper;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.util.Date;

@Getter
@Setter
@ResponseEntity
@TableName("sp_dispatch_method")
public class DispatchMethod {
    // id
    @TableId
    private String id;

    // 方案编号，自动生成
    private String code;

    // 方案名称
    private String name;

    // 方案类型
    private String type;

    // 日期标签。工作日/节假日
    private String dateLabel;

    // 站点ID
//    @ParseViaMapper(ProjectMapper.class)
    private String stationId;

    @TableField(exist = false)
    private String stationName;

    // 方案日期
    private Date time;

    // 方案描述
    private String remark;

    // 天气类型
    private String weatherType;

    // 最高温度
    private Double maxTemperature;

    // 最低温度
    private Double minTemperature;

    // 降雨量
    private Double rainfall;

    // 相对湿度
    private Double relativeHumidity;

    // 供水量
    private Double waterSupply;

    // 耗电量
    private Double powerConsumption;

    // 清水池液位
    private Double waterLevel;

    // 编辑人
    @ParseUsername
    private String editUser;

    // 编辑时间
    private Date editTime;

    // 是否启用
    private Boolean isEnabled;

    // 创建人
    private String creator;

    // 创建时间
    private Date createTime;

    // 租户ID
    private String tenantId;

}
