package org.thingsboard.server.dao.dataMonitor;

import com.google.common.util.concurrent.ListenableFuture;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.dataMonitor.DataMonitor;
import org.thingsboard.server.common.data.id.DeviceId;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.entity.AbstractEntityService;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/1/11 14:55
 */
@Service
public class DataMonitorServiceImpl extends AbstractEntityService implements DataMonitorService {

    @Autowired
    private DataMonitorDao dataMonitorDao;

    @Override
    public DataMonitor saveDataMonitor(DataMonitor dataMonitor) {
        return dataMonitorDao.save(dataMonitor);
    }

    @Override
    public ListenableFuture<List<DataMonitor>> findDataMonitorByTenant(TenantId tenantId) {
        return dataMonitorDao.findDataMonitorByTenant(tenantId);
    }

    @Override
    public ListenableFuture<List<DataMonitor>> findDataMonitorByTenantAndTime(TenantId tenantId, long start, long end) {
        return dataMonitorDao.findDataMonitorByTenantAndTime(tenantId,start,end);
    }

    @Override
    public ListenableFuture<List<DataMonitor>> findDataMonitorByDeviceAndTime(DeviceId deviceId, long start, long end) {
        return dataMonitorDao.findDataMonitorByDeviceAndTime(deviceId,start,end);
    }
}
