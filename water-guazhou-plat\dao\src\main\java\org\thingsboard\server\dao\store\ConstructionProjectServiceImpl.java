package org.thingsboard.server.dao.store;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.store.ConstructionProject;
import org.thingsboard.server.dao.sql.department.ConstructionProjectMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.store.ConstructionProjectPageRequest;
import org.thingsboard.server.dao.util.imodel.query.store.ConstructionProjectSaveRequest;

@Service
public class ConstructionProjectServiceImpl implements ConstructionProjectService {
    @Autowired
    private ConstructionProjectMapper mapper;

    @Override
    public IPage<ConstructionProject> findAllConditional(ConstructionProjectPageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    public ConstructionProject save(ConstructionProjectSaveRequest entity) {
        return QueryUtil.saveOrUpdateOneByRequest(entity, mapper::insert, mapper::update);
    }

    @Override
    public boolean update(ConstructionProject entity) {
        return mapper.update(entity);
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }

}
