"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[1612],{10937:(t,e,r)=>{function n(){const t=new Float32Array(16);return t[0]=1,t[5]=1,t[10]=1,t[15]=1,t}r.d(e,{c:()=>n});const a=n();Object.freeze(Object.defineProperty({__proto__:null,IDENTITY:a,clone:function(t){const e=new Float32Array(16);return e[0]=t[0],e[1]=t[1],e[2]=t[2],e[3]=t[3],e[4]=t[4],e[5]=t[5],e[6]=t[6],e[7]=t[7],e[8]=t[8],e[9]=t[9],e[10]=t[10],e[11]=t[11],e[12]=t[12],e[13]=t[13],e[14]=t[14],e[15]=t[15],e},create:n,createView:function(t,e){return new Float32Array(t,e,16)},fromValues:function(t,e,r,n,a,o,u,i,s,c,l,h,f,p,d,g){const m=new Float32Array(16);return m[0]=t,m[1]=e,m[2]=r,m[3]=n,m[4]=a,m[5]=o,m[6]=u,m[7]=i,m[8]=s,m[9]=c,m[10]=l,m[11]=h,m[12]=f,m[13]=p,m[14]=d,m[15]=g,m}},Symbol.toStringTag,{value:"Module"}))},35270:(t,e,r)=>{r.d(e,{B7:()=>s,St:()=>a,VL:()=>u,h$:()=>o,rW:()=>c});const n={transparent:[0,0,0,0],black:[0,0,0,1],silver:[192,192,192,1],gray:[128,128,128,1],white:[255,255,255,1],maroon:[128,0,0,1],red:[255,0,0,1],purple:[128,0,128,1],fuchsia:[255,0,255,1],green:[0,128,0,1],lime:[0,255,0,1],olive:[128,128,0,1],yellow:[255,255,0,1],navy:[0,0,128,1],blue:[0,0,255,1],teal:[0,128,128,1],aqua:[0,255,255,1],aliceblue:[240,248,255,1],antiquewhite:[250,235,215,1],aquamarine:[127,255,212,1],azure:[240,255,255,1],beige:[245,245,220,1],bisque:[255,228,196,1],blanchedalmond:[255,235,205,1],blueviolet:[138,43,226,1],brown:[165,42,42,1],burlywood:[222,184,135,1],cadetblue:[95,158,160,1],chartreuse:[127,255,0,1],chocolate:[210,105,30,1],coral:[255,127,80,1],cornflowerblue:[100,149,237,1],cornsilk:[255,248,220,1],crimson:[220,20,60,1],cyan:[0,255,255,1],darkblue:[0,0,139,1],darkcyan:[0,139,139,1],darkgoldenrod:[184,134,11,1],darkgray:[169,169,169,1],darkgreen:[0,100,0,1],darkgrey:[169,169,169,1],darkkhaki:[189,183,107,1],darkmagenta:[139,0,139,1],darkolivegreen:[85,107,47,1],darkorange:[255,140,0,1],darkorchid:[153,50,204,1],darkred:[139,0,0,1],darksalmon:[233,150,122,1],darkseagreen:[143,188,143,1],darkslateblue:[72,61,139,1],darkslategray:[47,79,79,1],darkslategrey:[47,79,79,1],darkturquoise:[0,206,209,1],darkviolet:[148,0,211,1],deeppink:[255,20,147,1],deepskyblue:[0,191,255,1],dimgray:[105,105,105,1],dimgrey:[105,105,105,1],dodgerblue:[30,144,255,1],firebrick:[178,34,34,1],floralwhite:[255,250,240,1],forestgreen:[34,139,34,1],gainsboro:[220,220,220,1],ghostwhite:[248,248,255,1],gold:[255,215,0,1],goldenrod:[218,165,32,1],greenyellow:[173,255,47,1],grey:[128,128,128,1],honeydew:[240,255,240,1],hotpink:[255,105,180,1],indianred:[205,92,92,1],indigo:[75,0,130,1],ivory:[255,255,240,1],khaki:[240,230,140,1],lavender:[230,230,250,1],lavenderblush:[255,240,245,1],lawngreen:[124,252,0,1],lemonchiffon:[255,250,205,1],lightblue:[173,216,230,1],lightcoral:[240,128,128,1],lightcyan:[224,255,255,1],lightgoldenrodyellow:[250,250,210,1],lightgray:[211,211,211,1],lightgreen:[144,238,144,1],lightgrey:[211,211,211,1],lightpink:[255,182,193,1],lightsalmon:[255,160,122,1],lightseagreen:[32,178,170,1],lightskyblue:[135,206,250,1],lightslategray:[119,136,153,1],lightslategrey:[119,136,153,1],lightsteelblue:[176,196,222,1],lightyellow:[255,255,224,1],limegreen:[50,205,50,1],linen:[250,240,230,1],magenta:[255,0,255,1],mediumaquamarine:[102,205,170,1],mediumblue:[0,0,205,1],mediumorchid:[186,85,211,1],mediumpurple:[147,112,219,1],mediumseagreen:[60,179,113,1],mediumslateblue:[123,104,238,1],mediumspringgreen:[0,250,154,1],mediumturquoise:[72,209,204,1],mediumvioletred:[199,21,133,1],midnightblue:[25,25,112,1],mintcream:[245,255,250,1],mistyrose:[255,228,225,1],moccasin:[255,228,181,1],navajowhite:[255,222,173,1],oldlace:[253,245,230,1],olivedrab:[107,142,35,1],orange:[255,165,0,1],orangered:[255,69,0,1],orchid:[218,112,214,1],palegoldenrod:[238,232,170,1],palegreen:[152,251,152,1],paleturquoise:[175,238,238,1],palevioletred:[219,112,147,1],papayawhip:[255,239,213,1],peachpuff:[255,218,185,1],peru:[205,133,63,1],pink:[255,192,203,1],plum:[221,160,221,1],powderblue:[176,224,230,1],rebeccapurple:[102,51,153,1],rosybrown:[188,143,143,1],royalblue:[65,105,225,1],saddlebrown:[139,69,19,1],salmon:[250,128,114,1],sandybrown:[244,164,96,1],seagreen:[46,139,87,1],seashell:[255,245,238,1],sienna:[160,82,45,1],skyblue:[135,206,235,1],slateblue:[106,90,205,1],slategray:[112,128,144,1],slategrey:[112,128,144,1],snow:[255,250,250,1],springgreen:[0,255,127,1],steelblue:[70,130,180,1],tan:[210,180,140,1],thistle:[216,191,216,1],tomato:[255,99,71,1],turquoise:[64,224,208,1],violet:[238,130,238,1],wheat:[245,222,179,1],whitesmoke:[245,245,245,1],yellowgreen:[154,205,50,1]};function a(t){return n[t]||n[t.toLowerCase()]}function o(t){return n[t]??n[t.toLowerCase()]}function u(t){return[...o(t)]}function i(t,e,r){r<0&&++r,r>1&&--r;const n=6*r;return n<1?t+(e-t)*n:2*r<1?e:3*r<2?t+(e-t)*(2/3-r)*6:t}function s(t,e,r,n=1){const a=(t%360+360)%360/360,o=r<=.5?r*(e+1):r+e-r*e,u=2*r-o;return[Math.round(255*i(u,o,a+1/3)),Math.round(255*i(u,o,a)),Math.round(255*i(u,o,a-1/3)),n]}function c(t){const e=t.length>5,r=e?8:4,n=(1<<r)-1,a=e?1:17,o=e?9===t.length:5===t.length;let u=Number("0x"+t.substr(1));if(isNaN(u))return null;const i=[0,0,0,1];let s;return o&&(s=u&n,u>>=r,i[3]=a*s/255),s=u&n,u>>=r,i[2]=a*s,s=u&n,u>>=r,i[1]=a*s,s=u&n,u>>=r,i[0]=a*s,i}},62357:(t,e,r)=>{r.d(e,{F2:()=>a,Wz:()=>o,t_:()=>u,vW:()=>i});const n=/^-?(\d+(\.\d+)?)\s*((px)|(pt))?$/i;function a(t){return t?t/72*96:0}function o(t){return t?72*t/96:0}function u(t){if("string"==typeof t){const e=t.match(n);if(e){const r=Number(e[1]),n=e[3]&&e[3].toLowerCase(),a="-"===t.charAt(0),u="px"===n?o(r):r;return a?-u:u}return console.warn("screenUtils.toPt: input not recognized!"),null}return t}function i(t=0,e=0){return{x:t,y:e}}},57520:(t,e,r)=>{r.d(e,{ij:()=>E,cW:()=>z});var n=r(20102),a=r(78286),o=r(35270),u=r(62357),i=r(10937),s=r(52138);class c{constructor(t,e,r){this.strength=t,this.radius=e,this.threshold=r,this.type="bloom"}interpolate(t,e,r){this.strength=g(t.strength,e.strength,r),this.radius=g(t.radius,e.radius,r),this.threshold=g(t.threshold,e.threshold,r)}clone(){return new c(this.strength,this.radius,this.threshold)}toJSON(){return{type:"bloom",radius:m(this.radius),strength:this.strength,threshold:this.threshold}}}class l{constructor(t){this.radius=t,this.type="blur"}interpolate(t,e,r){this.radius=Math.round(g(t.radius,e.radius,r))}clone(){return new l(this.radius)}toJSON(){return{type:"blur",radius:m(this.radius)}}}class h{constructor(t,e){this.type=t,this.amount=e,"invert"!==this.type&&"grayscale"!==this.type&&"sepia"!==this.type||(this.amount=Math.min(this.amount,1))}get colorMatrix(){return this._colorMatrix||this._updateMatrix(),this._colorMatrix}interpolate(t,e,r){this.amount=g(t.amount,e.amount,r),this._updateMatrix()}clone(){return new h(this.type,this.amount)}toJSON(){return{type:this.type,amount:this.amount}}_updateMatrix(){const t=this._colorMatrix||(0,i.c)();switch(this.type){case"brightness":this._colorMatrix=((t,e)=>{const r=(0,s.s)(t,e,0,0,0,0,e,0,0,0,0,e,0,0,0,0,1);return(0,s.t)(r,r)})(t,this.amount);break;case"contrast":this._colorMatrix=((t,e)=>{const r=(0,s.s)(t,e,0,0,.5-.5*e,0,e,0,.5-.5*e,0,0,e,.5-.5*e,0,0,0,1);return(0,s.t)(r,r)})(t,this.amount);break;case"grayscale":this._colorMatrix=((t,e)=>{const r=1-this.amount,n=(0,s.s)(t,.2126+.7874*r,.7152-.7152*r,.0722-.0722*r,0,.2126-.2126*r,.7152+.2848*r,.0722-.0722*r,0,.2126-.2126*r,.7152-.7152*r,.0722+.9278*r,0,0,0,0,1);return(0,s.t)(n,n)})(t);break;case"invert":this._colorMatrix=((t,e)=>{const r=1-2*e,n=(0,s.s)(t,r,0,0,e,0,r,0,e,0,0,r,e,0,0,0,1);return(0,s.t)(n,n)})(t,this.amount);break;case"saturate":this._colorMatrix=((t,e)=>{const r=(0,s.s)(t,.213+.787*e,.715-.715*e,.072-.072*e,0,.213-.213*e,.715+.285*e,.072-.072*e,0,.213-.213*e,.715-.715*e,.072+.928*e,0,0,0,0,1);return(0,s.t)(r,r)})(t,this.amount);break;case"sepia":this._colorMatrix=((t,e)=>{const r=1-this.amount,n=(0,s.s)(t,.393+.607*r,.769-.769*r,.189-.189*r,0,.349-.349*r,.686+.314*r,.168-.168*r,0,.272-.272*r,.534-.534*r,.131+.869*r,0,0,0,0,1);return(0,s.t)(n,n)})(t)}}}class f{constructor(t,e,r,n){this.offsetX=t,this.offsetY=e,this.blurRadius=r,this.color=n,this.type="drop-shadow"}interpolate(t,e,r){this.offsetX=g(t.offsetX,e.offsetX,r),this.offsetY=g(t.offsetY,e.offsetY,r),this.blurRadius=g(t.blurRadius,e.blurRadius,r),this.color[0]=Math.round(g(t.color[0],e.color[0],r)),this.color[1]=Math.round(g(t.color[1],e.color[1],r)),this.color[2]=Math.round(g(t.color[2],e.color[2],r)),this.color[3]=g(t.color[3],e.color[3],r)}clone(){return new f(this.offsetX,this.offsetY,this.blurRadius,[...this.color])}toJSON(){const t=[...this.color];return t[3]*=255,{type:"drop-shadow",xoffset:m(this.offsetX),yoffset:m(this.offsetY),blurRadius:m(this.blurRadius),color:t}}}class p{constructor(t){this.angle=t,this.type="hue-rotate"}get colorMatrix(){return this._colorMatrix||this._updateMatrix(),this._colorMatrix}interpolate(t,e,r){this.angle=g(t.angle,e.angle,r),this._updateMatrix()}clone(){return new p(this.angle)}toJSON(){return{type:"hue-rotate",angle:this.angle}}_updateMatrix(){const t=this._colorMatrix||(0,i.c)();this._colorMatrix=((t,e)=>{const r=Math.sin(e*Math.PI/180),n=Math.cos(e*Math.PI/180),a=(0,s.s)(t,.213+.787*n-.213*r,.715-.715*n-.715*r,.072-.072*n+.928*r,0,.213-.213*n+.143*r,.715+.285*n+.14*r,.072-.072*n-.283*r,0,.213-.213*n-.787*r,.715-.715*n+.715*r,.072+.928*n+.072*r,0,0,0,0,1);return(0,s.t)(a,a)})(t,this.angle)}}class d{constructor(t){this.amount=t,this.type="opacity",this.amount=Math.min(this.amount,1)}interpolate(t,e,r){this.amount=g(t.amount,e.amount,r)}clone(){return new d(this.amount)}toJSON(){return{type:"opacity",amount:this.amount}}}function g(t,e,r){return t+(e-t)*r}function m(t){return Math.round(1e3*(0,u.Wz)(t))/1e3}function y(t){switch(t.type){case"grayscale":case"sepia":case"invert":return new h(t.type,0);case"saturate":case"brightness":case"contrast":return new h(t.type,1);case"opacity":return new d(1);case"hue-rotate":return new p(0);case"blur":return new l(0);case"drop-shadow":return new f(0,0,0,[...(0,o.h$)("transparent")]);case"bloom":return new c(0,0,1)}}function w(t,e){const r=t.length>e.length?t:e;return(t.length>e.length?e:t).every(((t,e)=>t.type===r[e].type))}function b(t,e){const r=t.length>e.length?t:e,n=t.length>e.length?e:t;for(let t=n.length;t<r.length;t++)n.push(y(r[t]))}r(5732);var v,x,A={};function k(t){if(!t||0===t.length)return null;if("string"==typeof t){const e=M(t);return e&&0!==e.length?e:null}const e=t.map((t=>{if(!Number.isFinite(t.scale)||t.scale<=0)throw new n.Z("effect:invalid-scale","scale must be finite and greater than 0",{stop:t});return{scale:t.scale,effects:M(t.value)}}));e.sort(((t,e)=>e.effects.length-t.effects.length));for(let t=0;t<e.length-1;t++){if(!w(e[t].effects,e[t+1].effects))throw new n.Z("effect:interpolation-impossible","Cannot interpolate by scale between 2 lists of mixed effects",{a:e[t].effects,b:e[t+1].effects});b(e[t].effects,e[t+1].effects)}return e.sort(((t,e)=>e.scale-t.scale)),e}function M(t){let e;if(!t)return[];try{e=A.parse(t)}catch(e){throw new n.Z("effect:invalid-syntax","Invalid effect syntax",{value:t,error:e})}return e.map((t=>function(t){try{switch(t.name){case"grayscale":case"sepia":case"saturate":case"invert":case"brightness":case"contrast":return function(t){let e=1;return _(t.parameters,1),1===t.parameters.length&&(e=S(t.parameters[0])),new h(t.name,e)}(t);case"opacity":return function(t){let e=1;return _(t.parameters,1),1===t.parameters.length&&(e=S(t.parameters[0])),new d(e)}(t);case"hue-rotate":return function(t){let e=0;return _(t.parameters,1),1===t.parameters.length&&(e=function(t){return function(t){if("quantity"!==t.type||!(0===t.value&&null===t.unit||t.unit&&null!=F[t.unit]))throw new n.Z("effect:type-error",`Expected <angle>, Actual: ${C(t)}`,{term:t})}(t),t.value*F[t.unit]||0}(t.parameters[0])),new p(e)}(t);case"blur":return function(t){let e=0;return _(t.parameters,1),1===t.parameters.length&&(e=Z(t.parameters[0]),$(e,t.parameters[0])),new l(e)}(t);case"drop-shadow":return function(t){const e=[];let r=null;for(const a of t.parameters)if("color"===a.type){if(e.length&&Object.freeze(e),r)throw new n.Z("effect:type-error","Accepts only one color",{});r=j(a)}else{const t=Z(a);if(Object.isFrozen(e))throw new n.Z("effect:type-error","<length> parameters not consecutive",{lengths:e});e.push(t),3===e.length&&$(t,a)}if(e.length<2||e.length>3)throw new n.Z("effect:type-error",`Expected <length>{2,3}, Actual: <length>{${e.length}}`,{lengths:e});return new f(e[0],e[1],e[2]||0,r||O("black"))}(t);case"bloom":return function(t){let e=1,r=0,n=0;return _(t.parameters,3),t.parameters[0]&&(e=S(t.parameters[0])),t.parameters[1]&&(r=Z(t.parameters[1]),$(r,t.parameters[1])),t.parameters[2]&&(n=S(t.parameters[2])),new c(e,r,n)}(t)}}catch(e){throw e.details.filter=t,e}throw new n.Z("effect:unknown-effect",`Effect '${t.name}' is not supported`,{effect:t})}(t)))}function _(t,e){if(t.length>e)throw new n.Z("effect:type-error",`Function supports up to ${e} parameters, Actual: ${t.length}`,{parameters:t})}function C(t){if("color"===t.type)return"<color>";if(t.unit){if(N[t.unit])return"<length>";if(F[t.unit])return"<angle>";if("%"===t.unit)return"<percentage>"}return"<double>"}function $(t,e){if(t<0)throw new n.Z("effect:type-error",`Negative values are not allowed, Actual: ${t}`,{term:e})}x=function(){function t(e,r,n,a){var o=Error.call(this,e);return Object.setPrototypeOf&&Object.setPrototypeOf(o,t.prototype),o.expected=r,o.found=n,o.location=a,o.name="SyntaxError",o}function e(t,e,r){return r=r||" ",t.length>e?t:(e-=t.length,t+(r+=r.repeat(e)).slice(0,e))}return function(t,e){function r(){this.constructor=t}r.prototype=e.prototype,t.prototype=new r}(t,Error),t.prototype.format=function(t){var r="Error: "+this.message;if(this.location){var n,a=null;for(n=0;n<t.length;n++)if(t[n].source===this.location.source){a=t[n].text.split(/\r\n|\n|\r/g);break}var o=this.location.start,u=this.location.source+":"+o.line+":"+o.column;if(a){var i=this.location.end,s=e("",o.line.toString().length," "),c=a[o.line-1],l=(o.line===i.line?i.column:c.length+1)-o.column||1;r+="\n --\x3e "+u+"\n"+s+" |\n"+o.line+" | "+c+"\n"+s+" | "+e("",o.column-1," ")+e("",l,"^")}else r+="\n at "+u}return r},t.buildMessage=function(t,e){var r={literal:function(t){return'"'+a(t.text)+'"'},class:function(t){var e=t.parts.map((function(t){return Array.isArray(t)?o(t[0])+"-"+o(t[1]):o(t)}));return"["+(t.inverted?"^":"")+e.join("")+"]"},any:function(){return"any character"},end:function(){return"end of input"},other:function(t){return t.description}};function n(t){return t.charCodeAt(0).toString(16).toUpperCase()}function a(t){return t.replace(/\\/g,"\\\\").replace(/"/g,'\\"').replace(/\0/g,"\\0").replace(/\t/g,"\\t").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/[\x00-\x0F]/g,(function(t){return"\\x0"+n(t)})).replace(/[\x10-\x1F\x7F-\x9F]/g,(function(t){return"\\x"+n(t)}))}function o(t){return t.replace(/\\/g,"\\\\").replace(/\]/g,"\\]").replace(/\^/g,"\\^").replace(/-/g,"\\-").replace(/\0/g,"\\0").replace(/\t/g,"\\t").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/[\x00-\x0F]/g,(function(t){return"\\x0"+n(t)})).replace(/[\x10-\x1F\x7F-\x9F]/g,(function(t){return"\\x"+n(t)}))}function u(t){return r[t.type](t)}return"Expected "+function(t){var e,r,n=t.map(u);if(n.sort(),n.length>0){for(e=1,r=1;e<n.length;e++)n[e-1]!==n[e]&&(n[r]=n[e],r++);n.length=r}switch(n.length){case 1:return n[0];case 2:return n[0]+" or "+n[1];default:return n.slice(0,-1).join(", ")+", or "+n[n.length-1]}}(t)+" but "+function(t){return t?'"'+a(t)+'"':"end of input"}(e)+" found."},{SyntaxError:t,parse:function(e,r){var n,a={},o=(r=void 0!==r?r:{}).grammarSource,u={start:it},i=it,s="none",c="grad",l="turn",h=/^[ \t\n\r]/,f=/^[a-z\-]/,p=/^[0-9a-fA-F]/,d=/^[+\-]/,g=/^[0-9]/,m=nt("none"),y=et("none",!1),w=et(")",!1),b=et(",",!1),v=nt("whitespace"),x=rt([" ","\t","\n","\r"],!1,!1),A=nt("function"),k=et("(",!1),M=nt("identifier"),_=rt([["a","z"],"-"],!1,!1),C=nt("percentage"),$=et("%",!1),F=nt("length"),N=et("px",!1),S=et("cm",!1),Z=et("mm",!1),j=et("in",!1),O=et("pt",!1),R=et("pc",!1),q=nt("angle"),E=et("deg",!1),z=et("rad",!1),J=et("grad",!1),I=et("turn",!1),P=nt("number"),W=nt("color"),Y=et("#",!1),L=rt([["0","9"],["a","f"],["A","F"]],!1,!1),X=rt(["+","-"],!1,!1),B=rt([["0","9"]],!1,!1),T=et(".",!1),V=et("e",!1),U=0,D=0,G=[{line:1,column:1}],H=0,K=[],Q=0;if("startRule"in r){if(!(r.startRule in u))throw new Error("Can't start parsing from rule \""+r.startRule+'".');i=u[r.startRule]}function tt(){return e.substring(D,U)}function et(t,e){return{type:"literal",text:t,ignoreCase:e}}function rt(t,e,r){return{type:"class",parts:t,inverted:e,ignoreCase:r}}function nt(t){return{type:"other",description:t}}function at(t){var r,n=G[t];if(n)return n;for(r=t-1;!G[r];)r--;for(n={line:(n=G[r]).line,column:n.column};r<t;)10===e.charCodeAt(r)?(n.line++,n.column=1):n.column++,r++;return G[t]=n,n}function ot(t,e){var r=at(t),n=at(e);return{source:o,start:{offset:t,line:r.line,column:r.column},end:{offset:e,line:n.line,column:n.column}}}function ut(t){U<H||(U>H&&(H=U,K=[]),K.push(t))}function it(){var t;return(t=st())===a&&(t=function(){var t,e;if(t=[],(e=ct())!==a)for(;e!==a;)t.push(e),e=ct();else t=a;return t}()),t}function st(){var t,r;return Q++,t=U,ht(),e.substr(U,4)===s?(r=s,U+=4):(r=a,0===Q&&ut(y)),r!==a?(ht(),D=t,t=[]):(U=t,t=a),Q--,t===a&&0===Q&&ut(m),t}function ct(){var t,r,n,o;return t=U,ht(),(r=function(){var t,r,n;return Q++,t=U,(r=ft())!==a?(40===e.charCodeAt(U)?(n="(",U++):(n=a,0===Q&&ut(k)),n!==a?(D=t,t=r):(U=t,t=a)):(U=t,t=a),Q--,t===a&&(r=a,0===Q&&ut(A)),t}())!==a?(ht(),(n=function(){var t,r,n,o,u,i,s,c;if(t=U,(r=lt())!==a){for(n=[],o=U,u=ht(),44===e.charCodeAt(U)?(i=",",U++):(i=a,0===Q&&ut(b)),i===a&&(i=null),s=ht(),(c=lt())!==a?o=u=[u,i,s,c]:(U=o,o=a);o!==a;)n.push(o),o=U,u=ht(),44===e.charCodeAt(U)?(i=",",U++):(i=a,0===Q&&ut(b)),i===a&&(i=null),s=ht(),(c=lt())!==a?o=u=[u,i,s,c]:(U=o,o=a);D=t,t=function(t,e){return e.length>0?function(t,e,r){return[t].concat(function(t,e){return t.map((function(t){return t[3]}))}(e))}(t,e):[t]}(r,n)}else U=t,t=a;return t}())===a&&(n=null),ht(),41===e.charCodeAt(U)?(o=")",U++):(o=a,0===Q&&ut(w)),o!==a?(ht(),D=t,t=function(t,e){return{type:"function",name:t,parameters:e||[]}}(r,n)):(U=t,t=a)):(U=t,t=a),t}function lt(){var t,e;return t=U,(e=pt())===a&&(e=dt())===a&&(e=gt())===a&&(e=function(){var t,e;return Q++,t=U,ht(),(e=yt())!==a?(D=t,t=function(t){return{value:t,unit:null}}(e)):(U=t,t=a),Q--,t===a&&0===Q&&ut(P),t}()),e!==a&&(D=t,e=function(t){return{type:"quantity",value:t.value,unit:t.unit}}(e)),(t=e)===a&&(t=U,(e=mt())!==a&&(D=t,e=function(t){return{type:"color",colorType:t.type,value:t.value}}(e)),t=e),t}function ht(){var t,r;for(Q++,t=[],h.test(e.charAt(U))?(r=e.charAt(U),U++):(r=a,0===Q&&ut(x));r!==a;)t.push(r),h.test(e.charAt(U))?(r=e.charAt(U),U++):(r=a,0===Q&&ut(x));return Q--,r=a,0===Q&&ut(v),t}function ft(){var t,r,n;if(Q++,t=U,r=[],f.test(e.charAt(U))?(n=e.charAt(U),U++):(n=a,0===Q&&ut(_)),n!==a)for(;n!==a;)r.push(n),f.test(e.charAt(U))?(n=e.charAt(U),U++):(n=a,0===Q&&ut(_));else r=a;return r!==a&&(D=t,r=tt()),Q--,(t=r)===a&&(r=a,0===Q&&ut(M)),t}function pt(){var t,r,n;return Q++,t=U,ht(),(r=yt())!==a?(37===e.charCodeAt(U)?(n="%",U++):(n=a,0===Q&&ut($)),n!==a?(D=t,t=function(t){return{value:t,unit:"%"}}(r)):(U=t,t=a)):(U=t,t=a),Q--,t===a&&0===Q&&ut(C),t}function dt(){var t,r,n;return Q++,t=U,ht(),(r=yt())!==a?("px"===e.substr(U,2)?(n="px",U+=2):(n=a,0===Q&&ut(N)),n!==a?(D=t,t=function(t){return{value:t,unit:"px"}}(r)):(U=t,t=a)):(U=t,t=a),t===a&&(t=U,ht(),(r=yt())!==a?("cm"===e.substr(U,2)?(n="cm",U+=2):(n=a,0===Q&&ut(S)),n!==a?(D=t,t=function(t){return{value:t,unit:"cm"}}(r)):(U=t,t=a)):(U=t,t=a),t===a&&(t=U,ht(),(r=yt())!==a?("mm"===e.substr(U,2)?(n="mm",U+=2):(n=a,0===Q&&ut(Z)),n!==a?(D=t,t=function(t){return{value:t,unit:"mm"}}(r)):(U=t,t=a)):(U=t,t=a),t===a&&(t=U,ht(),(r=yt())!==a?("in"===e.substr(U,2)?(n="in",U+=2):(n=a,0===Q&&ut(j)),n!==a?(D=t,t=function(t){return{value:t,unit:"in"}}(r)):(U=t,t=a)):(U=t,t=a),t===a&&(t=U,ht(),(r=yt())!==a?("pt"===e.substr(U,2)?(n="pt",U+=2):(n=a,0===Q&&ut(O)),n!==a?(D=t,t=function(t){return{value:t,unit:"pt"}}(r)):(U=t,t=a)):(U=t,t=a),t===a&&(t=U,ht(),(r=yt())!==a?("pc"===e.substr(U,2)?(n="pc",U+=2):(n=a,0===Q&&ut(R)),n!==a?(D=t,t=function(t){return{value:t,unit:"pc"}}(r)):(U=t,t=a)):(U=t,t=a)))))),Q--,t===a&&0===Q&&ut(F),t}function gt(){var t,r,n;return Q++,t=U,(r=yt())!==a?("deg"===e.substr(U,3)?(n="deg",U+=3):(n=a,0===Q&&ut(E)),n!==a?(D=t,t=function(t){return{value:t,unit:"deg"}}(r)):(U=t,t=a)):(U=t,t=a),t===a&&(t=U,(r=yt())!==a?("rad"===e.substr(U,3)?(n="rad",U+=3):(n=a,0===Q&&ut(z)),n!==a?(D=t,t=function(t){return{value:t,unit:"rad"}}(r)):(U=t,t=a)):(U=t,t=a),t===a&&(t=U,(r=yt())!==a?(e.substr(U,4)===c?(n=c,U+=4):(n=a,0===Q&&ut(J)),n!==a?(D=t,t=function(t){return{value:t,unit:"grad"}}(r)):(U=t,t=a)):(U=t,t=a),t===a&&(t=U,(r=yt())!==a?(e.substr(U,4)===l?(n=l,U+=4):(n=a,0===Q&&ut(I)),n!==a?(D=t,t=function(t){return{value:t,unit:"turn"}}(r)):(U=t,t=a)):(U=t,t=a)))),Q--,t===a&&(r=a,0===Q&&ut(q)),t}function mt(){var t,r,n,o;if(Q++,t=U,35===e.charCodeAt(U)?(r="#",U++):(r=a,0===Q&&ut(Y)),r!==a){if(n=[],p.test(e.charAt(U))?(o=e.charAt(U),U++):(o=a,0===Q&&ut(L)),o!==a)for(;o!==a;)n.push(o),p.test(e.charAt(U))?(o=e.charAt(U),U++):(o=a,0===Q&&ut(L));else n=a;n!==a?(D=t,t={type:"hex",value:tt()}):(U=t,t=a)}else U=t,t=a;return t===a&&(t=U,(r=ct())!==a&&(D=t,r=function(t){return{type:"function",value:t}}(r)),(t=r)===a&&(t=U,(r=ft())!==a&&(D=t,r={type:"named",value:tt()}),t=r)),Q--,t===a&&(r=a,0===Q&&ut(W)),t}function yt(){var t,r,n,o,u,i,s;for(t=U,d.test(e.charAt(U))?(e.charAt(U),U++):0===Q&&ut(X),r=U,n=[],g.test(e.charAt(U))?(o=e.charAt(U),U++):(o=a,0===Q&&ut(B));o!==a;)n.push(o),g.test(e.charAt(U))?(o=e.charAt(U),U++):(o=a,0===Q&&ut(B));if(46===e.charCodeAt(U)?(o=".",U++):(o=a,0===Q&&ut(T)),o!==a){if(u=[],g.test(e.charAt(U))?(i=e.charAt(U),U++):(i=a,0===Q&&ut(B)),i!==a)for(;i!==a;)u.push(i),g.test(e.charAt(U))?(i=e.charAt(U),U++):(i=a,0===Q&&ut(B));else u=a;u!==a?r=n=[n,o,u]:(U=r,r=a)}else U=r,r=a;if(r===a)if(r=[],g.test(e.charAt(U))?(n=e.charAt(U),U++):(n=a,0===Q&&ut(B)),n!==a)for(;n!==a;)r.push(n),g.test(e.charAt(U))?(n=e.charAt(U),U++):(n=a,0===Q&&ut(B));else r=a;if(r!==a){if(n=U,101===e.charCodeAt(U)?(o="e",U++):(o=a,0===Q&&ut(V)),o!==a){if(d.test(e.charAt(U))?(u=e.charAt(U),U++):(u=a,0===Q&&ut(X)),u===a&&(u=null),i=[],g.test(e.charAt(U))?(s=e.charAt(U),U++):(s=a,0===Q&&ut(B)),s!==a)for(;s!==a;)i.push(s),g.test(e.charAt(U))?(s=e.charAt(U),U++):(s=a,0===Q&&ut(B));else i=a;i!==a?n=o=[o,u,i]:(U=n,n=a)}else U=n,n=a;n===a&&(n=null),D=t,t=parseFloat(tt())}else U=t,t=a;return t}if((n=i())!==a&&U===e.length)return n;throw n!==a&&U<e.length&&ut({type:"end"}),function(e,r,n){return new t(t.buildMessage(e,r),e,r,n)}(K,H<e.length?e.charAt(H):null,H<e.length?ot(H,H+1):ot(H,H))}}},(v={get exports(){return A},set exports(t){A=t}}).exports&&(v.exports=x());const F={deg:1,grad:.9,rad:180/Math.PI,turn:360},N={px:1,cm:96/2.54,mm:96/2.54/10,in:96,pc:16,pt:96/72};function S(t){!function(t){if("quantity"!==t.type||null!==t.unit&&"%"!==t.unit)throw new n.Z("effect:type-error",`Expected <double> or <percentage>, Actual: ${C(t)}`,{term:t})}(t);const e=t.value;return $(e,t),"%"===t.unit?.01*e:e}function Z(t){return function(t){if("quantity"!==t.type||!(0===t.value&&null===t.unit||t.unit&&null!=N[t.unit]))throw new n.Z("effect:type-error",`Expected <length>, Actual: ${C(t)}`,{term:t})}(t),t.value*N[t.unit]||0}function j(t){switch(t.colorType){case"hex":return(0,o.rW)(t.value);case"named":return O(t.value);case"function":return function(t){if(_(t.parameters,4),R.test(t.name))return[S(t.parameters[0]),S(t.parameters[1]),S(t.parameters[2]),t.parameters[3]?S(t.parameters[3]):1];if(q.test(t.name))return(0,o.B7)(function(t){return function(t){if("quantity"!==t.type||null!==t.unit)throw new n.Z("effect:type-error",`Expected <double>, Actual: ${C(t)}`,{term:t})}(t),$(t.value,t),t.value}(t.parameters[0]),S(t.parameters[1]),S(t.parameters[2]),t.parameters[3]?S(t.parameters[3]):1);throw new n.Z("effect:syntax-error",`Invalid color function '${t.name}'`,{colorFunction:t})}(t.value)}}function O(t){if(!(0,o.St)(t))throw new n.Z("effect:unknown-color",`color '${t}' isn't valid`,{namedColor:t});return(0,o.VL)(t)}const R=/^rgba?/i,q=/^hsla?/i;function E(t,e,r){try{return function(t){if(!t||0===t.length)return null;if(function(t){const e=t[0];return!!e&&"scale"in e}(t)){const e=[];for(const r of t)e.push({scale:r.scale,value:J(r.value)});return e}return J(t)}(t)}catch(t){r?.messages?.push(t)}return null}function z(t,e,r,n){try{const n=function(t){const e=k(t);return e?function(t){const e=t[0];return!!e&&"type"in e}(e)?e.map((t=>t.toJSON())):e.map((({scale:t,effects:e})=>({scale:t,value:e.map((t=>t.toJSON()))}))):null}(t);(0,a.RB)(r,n,e)}catch(t){n.messages&&n.messages.push(t)}}function J(t){if(!t||!t.length)return"";const e=[];for(const r of t){let t=[];switch(r.type){case"grayscale":case"sepia":case"saturate":case"invert":case"brightness":case"contrast":case"opacity":t=[I(r,"amount")];break;case"blur":t=[I(r,"radius","pt")];break;case"hue-rotate":t=[I(r,"angle","deg")];break;case"drop-shadow":t=[I(r,"xoffset","pt"),I(r,"yoffset","pt"),I(r,"blurRadius","pt"),P(r,"color")];break;case"bloom":t=[I(r,"strength"),I(r,"radius","pt"),I(r,"threshold")]}const n=`${r.type}(${t.filter(Boolean).join(" ")})`;k(n),e.push(n)}return e.join(" ")}function I(t,e,r){if(null==t[e])throw new n.Z("effect:missing-parameter",`Missing parameter '${e}' in ${t.type} effect`,{effect:t});return r?t[e]+r:""+t[e]}function P(t,e){if(null==t[e])throw new n.Z("effect:missing-parameter",`Missing parameter '${e}' in ${t.type} effect`,{effect:t});const r=t[e];return`rgba(${r[0]||0}, ${r[1]||0}, ${r[2]||0}, ${r[3]/255||0})`}},71612:(t,e,r)=>{r.d(e,{h:()=>s});var n=r(43697),a=r(5600),o=(r(75215),r(67676),r(52011)),u=r(57520);const i={read:{reader:u.ij},write:{allowNull:!0,writer:u.cW}},s=t=>{let e=class extends t{constructor(){super(...arguments),this.blendMode="normal",this.effect=null}};return(0,n._)([(0,a.Cb)({type:["average","color-burn","color-dodge","color","darken","destination-atop","destination-in","destination-out","destination-over","difference","exclusion","hard-light","hue","invert","lighten","lighter","luminosity","minus","multiply","normal","overlay","plus","reflect","saturation","screen","soft-light","source-atop","source-in","source-out","vivid-light","xor"],nonNullable:!0,json:{read:!1,write:!1,origins:{"web-map":{read:!0,write:!0},"portal-item":{read:!0,write:!0}}}})],e.prototype,"blendMode",void 0),(0,n._)([(0,a.Cb)({json:{read:!1,write:!1,origins:{"web-map":i,"portal-item":i}}})],e.prototype,"effect",void 0),e=(0,n._)([(0,o.j)("esri.layers.mixins.BlendLayer")],e),e}}}]);