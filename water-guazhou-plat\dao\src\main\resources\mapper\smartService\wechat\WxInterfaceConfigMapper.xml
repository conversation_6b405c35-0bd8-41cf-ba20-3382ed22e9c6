<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartService.wechat.WxInterfaceConfigMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->id,
                           user_info_api,
                           bill_info_api,
                           pay_api,
                           check_api,
                           payment_record_api,
                           message_send_api,
                           tenant_id<!--@sql from wx_interface_config -->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.smartService.wechat.WxInterfaceConfig">
        <result column="id" property="id"/>
        <result column="user_info_api" property="userInfoApi"/>
        <result column="bill_info_api" property="billInfoApi"/>
        <result column="pay_api" property="payApi"/>
        <result column="check_api" property="checkApi"/>
        <result column="payment_record_api" property="paymentRecordApi"/>
        <result column="message_send_api" property="messageSendApi"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <select id="findByTenantId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wx_interface_config
        where tenant_id = #{tenantId}
    </select>

    <select id="getIdByTenantId" resultType="java.lang.String">
        select id
        from wx_interface_config
        where tenant_id = #{tenantId}
    </select>
</mapper>