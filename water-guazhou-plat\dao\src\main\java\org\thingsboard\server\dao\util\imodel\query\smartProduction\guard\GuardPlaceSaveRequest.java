package org.thingsboard.server.dao.util.imodel.query.smartProduction.guard;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartProduction.guard.GuardPlace;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

@Getter
@Setter
public class GuardPlaceSaveRequest extends SaveRequest<GuardPlace> {
    // 值班地点
    @NotNullOrEmpty
    private String address;

    // 排序编号
    private Integer orderNum;


    @Override
    protected GuardPlace build() {
        GuardPlace entity = new GuardPlace();
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    @Override
    protected GuardPlace update(String id) {
        GuardPlace entity = new GuardPlace();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(GuardPlace entity) {
        entity.setAddress(address);
        entity.setOrderNum(orderNum);
    }

}