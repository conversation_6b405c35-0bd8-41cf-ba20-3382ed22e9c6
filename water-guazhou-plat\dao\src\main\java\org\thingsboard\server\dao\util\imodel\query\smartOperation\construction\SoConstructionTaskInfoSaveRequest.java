package org.thingsboard.server.dao.util.imodel.query.smartOperation.construction;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartOperation.SoGeneralTaskStatus;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionTaskInfo;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemScope;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;

@Getter
@Setter
public class SoConstructionTaskInfoSaveRequest extends SaveRequest<SoConstructionTaskInfo> {
    // 作用域
    private SoGeneralSystemScope scope;

    // 所属工程编号
    private String constructionCode;

    @Override
    protected SoConstructionTaskInfo build() {
        SoConstructionTaskInfo entity = new SoConstructionTaskInfo();
        entity.setScope(scope);
        entity.setConstructionCode(constructionCode);
        entity.setStatus(SoGeneralTaskStatus.PROCESSING);
        entity.setCreator(currentUserUUID());
        entity.setCreateTime(createTime());
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    @Override
    protected SoConstructionTaskInfo update(String id) {
        SoConstructionTaskInfo entity = new SoConstructionTaskInfo();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(SoConstructionTaskInfo entity) {
    }

    public static SoConstructionTaskInfoSaveRequest of(SaveRequest<?> fromRequest,SoGeneralSystemScope scope, String constructionCode) {
        SoConstructionTaskInfoSaveRequest soConstructionTaskInfo = new SoConstructionTaskInfoSaveRequest();
        fromRequest.assimilation(soConstructionTaskInfo);
        soConstructionTaskInfo.setScope(scope);
        soConstructionTaskInfo.setConstructionCode(constructionCode);
        return soConstructionTaskInfo;

    }
}