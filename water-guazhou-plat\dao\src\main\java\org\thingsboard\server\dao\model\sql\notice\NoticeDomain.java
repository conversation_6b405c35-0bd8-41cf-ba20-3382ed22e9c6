package org.thingsboard.server.dao.model.sql.notice;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@TableName("tb_notice")
@NoArgsConstructor
@AllArgsConstructor
@Data
public class NoticeDomain {
    //公告id
    public String Id;

    //公告类型 1停水通知 2公告板
    public String type;

    //附件URL
    public String other_file;

    //公告内容
    public String content;

    //租户id
    public String tenant_id;

    //发布时间
    public Date sub_time;

    //发布人性命
    public String sub_user_name;
}
