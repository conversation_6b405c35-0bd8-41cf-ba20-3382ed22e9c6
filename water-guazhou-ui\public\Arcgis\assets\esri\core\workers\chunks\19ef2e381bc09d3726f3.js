"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[1785],{22303:(t,e,r)=>{r.d(e,{Z:()=>p});var i,n,s=r(35270),a=r(22021),o=r(70586),l=r(75215);function u(t){return(0,a.uZ)((0,l.vU)(t),0,255)}function h(t,e,r){return t=Number(t),isNaN(t)?r:t<e?e:t>r?r:t}class c{static blendColors(t,e,r,i=new c){return i.r=Math.round(t.r+(e.r-t.r)*r),i.g=Math.round(t.g+(e.g-t.g)*r),i.b=Math.round(t.b+(e.b-t.b)*r),i.a=t.a+(e.a-t.a)*r,i._sanitize()}static fromRgb(t,e){const r=t.toLowerCase().match(/^(rgba?|hsla?)\(([\s\.\-,%0-9]+)\)/);if(r){const t=r[2].split(/\s*,\s*/),i=r[1];if("rgb"===i&&3===t.length||"rgba"===i&&4===t.length){const r=t[0];if("%"===r.charAt(r.length-1)){const r=t.map((t=>2.56*parseFloat(t)));return 4===t.length&&(r[3]=parseFloat(t[3])),c.fromArray(r,e)}return c.fromArray(t.map((t=>parseFloat(t))),e)}if("hsl"===i&&3===t.length||"hsla"===i&&4===t.length)return c.fromArray((0,s.B7)(parseFloat(t[0]),parseFloat(t[1])/100,parseFloat(t[2])/100,parseFloat(t[3])),e)}return null}static fromHex(t,e=new c){if(4!==t.length&&7!==t.length||"#"!==t[0])return null;const r=4===t.length?4:8,i=(1<<r)-1;let n=Number("0x"+t.substr(1));return isNaN(n)?null:(["b","g","r"].forEach((t=>{const s=n&i;n>>=r,e[t]=4===r?17*s:s})),e.a=1,e)}static fromArray(t,e=new c){return e._set(Number(t[0]),Number(t[1]),Number(t[2]),Number(t[3])),isNaN(e.a)&&(e.a=1),e._sanitize()}static fromString(t,e){const r=(0,s.St)(t)?(0,s.h$)(t):null;return r&&c.fromArray(r,e)||c.fromRgb(t,e)||c.fromHex(t,e)}static fromJSON(t){return t&&new c([t[0],t[1],t[2],t[3]/255])}static toUnitRGB(t){return(0,o.pC)(t)?[t.r/255,t.g/255,t.b/255]:null}static toUnitRGBA(t){return(0,o.pC)(t)?[t.r/255,t.g/255,t.b/255,null!=t.a?t.a:1]:null}constructor(t){this.r=255,this.g=255,this.b=255,this.a=1,t&&this.setColor(t)}get isBright(){return.299*this.r+.587*this.g+.114*this.b>=127}setColor(t){return"string"==typeof t?c.fromString(t,this):Array.isArray(t)?c.fromArray(t,this):(this._set(t.r??0,t.g??0,t.b??0,t.a??1),t instanceof c||this._sanitize()),this}toRgb(){return[this.r,this.g,this.b]}toRgba(){return[this.r,this.g,this.b,this.a]}toHex(){const t=this.r.toString(16),e=this.g.toString(16),r=this.b.toString(16);return`#${t.length<2?"0"+t:t}${e.length<2?"0"+e:e}${r.length<2?"0"+r:r}`}toCss(t=!1){const e=this.r+", "+this.g+", "+this.b;return t?`rgba(${e}, ${this.a})`:`rgb(${e})`}toString(){return this.toCss(!0)}toJSON(){return this.toArray()}toArray(t=c.AlphaMode.ALWAYS){const e=u(this.r),r=u(this.g),i=u(this.b);return t===c.AlphaMode.ALWAYS||1!==this.a?[e,r,i,u(255*this.a)]:[e,r,i]}clone(){return new c(this.toRgba())}hash(){return this.r<<24|this.g<<16|this.b<<8|255*this.a}equals(t){return(0,o.pC)(t)&&t.r===this.r&&t.g===this.g&&t.b===this.b&&t.a===this.a}_sanitize(){return this.r=Math.round(h(this.r,0,255)),this.g=Math.round(h(this.g,0,255)),this.b=Math.round(h(this.b,0,255)),this.a=h(this.a,0,1),this}_set(t,e,r,i){this.r=t,this.g=e,this.b=r,this.a=i}}c.prototype.declaredClass="esri.Color",(n=(i=c||(c={})).AlphaMode||(i.AlphaMode={}))[n.ALWAYS=0]="ALWAYS",n[n.UNLESS_OPAQUE=1]="UNLESS_OPAQUE";const p=c},3892:(t,e,r)=>{function i(t){return"h"in t&&"s"in t&&"v"in t}function n(t){return"l"in t&&"a"in t&&"b"in t}function s(t){return"l"in t&&"c"in t&&"h"in t}r.d(e,{Y3:()=>_,_Y:()=>f,sJ:()=>m,xr:()=>y});const a=[[.4124,.3576,.1805],[.2126,.7152,.0722],[.0193,.1192,.9505]],o=[[3.2406,-1.5372,-.4986],[-.9689,1.8758,.0415],[.0557,-.204,1.057]];function l(t,e){const r=[];let i,n;if(t[0].length!==e.length)throw new Error("dimensions do not match");const s=t.length,a=t[0].length;let o=0;for(i=0;i<s;i++){for(o=0,n=0;n<a;n++)o+=t[i][n]*e[n];r.push(o)}return r}function u(t){const e=[t.r/255,t.g/255,t.b/255].map((t=>t<=.04045?t/12.92:((t+.055)/1.055)**2.4)),r=l(a,e);return{x:100*r[0],y:100*r[1],z:100*r[2]}}function h(t){const e=l(o,[t.x/100,t.y/100,t.z/100]).map((t=>{const e=t<=.0031308?12.92*t:1.055*t**(1/2.4)-.055;return Math.min(1,Math.max(e,0))}));return{r:Math.round(255*e[0]),g:Math.round(255*e[1]),b:Math.round(255*e[2])}}function c(t){const e=[t.x/95.047,t.y/100,t.z/108.883].map((t=>t>(6/29)**3?t**(1/3):1/3*(29/6)**2*t+4/29));return{l:116*e[1]-16,a:500*(e[0]-e[1]),b:200*(e[1]-e[2])}}function p(t){const e=t.l,r=[(e+16)/116+t.a/500,(e+16)/116,(e+16)/116-t.b/200].map((t=>t>6/29?t**3:3*(6/29)**2*(t-4/29)));return{x:95.047*r[0],y:100*r[1],z:108.883*r[2]}}function y(t){return"r"in(e=t)&&"g"in e&&"b"in e?t:s(t)?function(t){return h(p(function(t){const e=t.l,r=t.c,i=t.h;return{l:e,a:r*Math.cos(i),b:r*Math.sin(i)}}(t)))}(t):n(t)?function(t){return h(p(t))}(t):function(t){return"x"in t&&"y"in t&&"z"in t}(t)?h(t):i(t)?function(t){const e=(t.h+360)%360/60,r=t.s/100,i=t.v/100*255,n=i*r,s=n*(1-Math.abs(e%2-1));let a;switch(Math.floor(e)){case 0:a={r:n,g:s,b:0};break;case 1:a={r:s,g:n,b:0};break;case 2:a={r:0,g:n,b:s};break;case 3:a={r:0,g:s,b:n};break;case 4:a={r:s,g:0,b:n};break;case 5:case 6:a={r:n,g:0,b:s};break;default:a={r:0,g:0,b:0}}return a.r=Math.round(a.r+i-n),a.g=Math.round(a.g+i-n),a.b=Math.round(a.b+i-n),a}(t):t;var e}function f(t){return i(t)?t:function(t){const e=t.r,r=t.g,i=t.b,n=Math.max(e,r,i),s=n-Math.min(e,r,i);let a=n,o=0===s?0:n===e?(r-i)/s%6:n===r?(i-e)/s+2:(e-r)/s+4,l=0===s?0:s/a;return o<0&&(o+=6),o*=60,l*=100,a*=100/255,{h:o,s:l,v:a}}(y(t))}function _(t){return n(t)?t:function(t){return c(u(t))}(y(t))}function m(t){return s(t)?t:function(t){return function(t){const e=t.l,r=t.a,i=t.b,n=Math.sqrt(r*r+i*i);let s=Math.atan2(i,r);return s=s>0?s:s+2*Math.PI,{l:e,c:n,h:s}}(c(u(t)))}(y(t))}},59255:(t,e,r)=>{var i,n,s;r.d(e,{E9:()=>a,I6:()=>u,Vl:()=>i,bN:()=>l}),(s=i||(i={}))[s.Unknown=0]="Unknown",s[s.Point=1]="Point",s[s.LineString=2]="LineString",s[s.Polygon=3]="Polygon";class a{constructor(t,e){this.x=t,this.y=e}clone(){return new a(this.x,this.y)}equals(t,e){return t===this.x&&e===this.y}isEqual(t){return t.x===this.x&&t.y===this.y}setCoords(t,e){this.x=t,this.y=e}normalize(){const t=this.x,e=this.y,r=Math.sqrt(t*t+e*e);this.x/=r,this.y/=r}rightPerpendicular(){const t=this.x;this.x=this.y,this.y=-t}move(t,e){this.x+=t,this.y+=e}assign(t){this.x=t.x,this.y=t.y}assignAdd(t,e){this.x=t.x+e.x,this.y=t.y+e.y}assignSub(t,e){this.x=t.x-e.x,this.y=t.y-e.y}rotate(t,e){const r=this.x,i=this.y;this.x=r*t-i*e,this.y=r*e+i*t}scale(t){this.x*=t,this.y*=t}length(){const t=this.x,e=this.y;return Math.sqrt(t*t+e*e)}static distance(t,e){const r=e.x-t.x,i=e.y-t.y;return Math.sqrt(r*r+i*i)}static add(t,e){return new a(t.x+e.x,t.y+e.y)}static sub(t,e){return new a(t.x-e.x,t.y-e.y)}}class o{constructor(t,e,r){this.ratio=t,this.x=e,this.y=r}}class l{constructor(t,e,r,i=8,n=8){this._lines=[],this._starts=[],this.validateTessellation=!0,this._pixelRatio=i,this._pixelMargin=n,this._tileSize=512*i,this._dz=t,this._yPos=e,this._xPos=r}setPixelMargin(t){t!==this._pixelMargin&&(this._pixelMargin=t,this.setExtent(this._extent))}setExtent(t){this._extent=t,this._finalRatio=this._tileSize/t*(1<<this._dz);let e=this._pixelRatio*this._pixelMargin;e/=this._finalRatio;const r=t>>this._dz;e>r&&(e=r),this._margin=e,this._xmin=r*this._xPos-e,this._ymin=r*this._yPos-e,this._xmax=this._xmin+r+2*e,this._ymax=this._ymin+r+2*e}reset(t){this._type=t,this._lines=[],this._starts=[],this._line=null,this._start=0}moveTo(t,e){this._pushLine(),this._prevIsIn=this._isIn(t,e),this._moveTo(t,e,this._prevIsIn),this._prevPt=new a(t,e),this._firstPt=new a(t,e),this._dist=0}lineTo(t,e){const r=this._isIn(t,e),i=new a(t,e),n=a.distance(this._prevPt,i);let s,l,u,h,c,p,y,f;if(r)this._prevIsIn?this._lineTo(t,e,!0):(s=this._prevPt,l=i,u=this._intersect(l,s),this._start=this._dist+n*(1-this._r),this._lineTo(u.x,u.y,!0),this._lineTo(l.x,l.y,!0));else if(this._prevIsIn)l=this._prevPt,s=i,u=this._intersect(l,s),this._lineTo(u.x,u.y,!0),this._lineTo(s.x,s.y,!1);else{const t=this._prevPt,e=i;if(t.x<=this._xmin&&e.x<=this._xmin||t.x>=this._xmax&&e.x>=this._xmax||t.y<=this._ymin&&e.y<=this._ymin||t.y>=this._ymax&&e.y>=this._ymax)this._lineTo(e.x,e.y,!1);else{const r=[];if((t.x<this._xmin&&e.x>this._xmin||t.x>this._xmin&&e.x<this._xmin)&&(h=(this._xmin-t.x)/(e.x-t.x),f=t.y+h*(e.y-t.y),f<=this._ymin?p=!1:f>=this._ymax?p=!0:r.push(new o(h,this._xmin,f))),(t.x<this._xmax&&e.x>this._xmax||t.x>this._xmax&&e.x<this._xmax)&&(h=(this._xmax-t.x)/(e.x-t.x),f=t.y+h*(e.y-t.y),f<=this._ymin?p=!1:f>=this._ymax?p=!0:r.push(new o(h,this._xmax,f))),(t.y<this._ymin&&e.y>this._ymin||t.y>this._ymin&&e.y<this._ymin)&&(h=(this._ymin-t.y)/(e.y-t.y),y=t.x+h*(e.x-t.x),y<=this._xmin?c=!1:y>=this._xmax?c=!0:r.push(new o(h,y,this._ymin))),(t.y<this._ymax&&e.y>this._ymax||t.y>this._ymax&&e.y<this._ymax)&&(h=(this._ymax-t.y)/(e.y-t.y),y=t.x+h*(e.x-t.x),y<=this._xmin?c=!1:y>=this._xmax?c=!0:r.push(new o(h,y,this._ymax))),0===r.length)c?p?this._lineTo(this._xmax,this._ymax,!0):this._lineTo(this._xmax,this._ymin,!0):p?this._lineTo(this._xmin,this._ymax,!0):this._lineTo(this._xmin,this._ymin,!0);else if(r.length>1&&r[0].ratio>r[1].ratio)this._start=this._dist+n*r[1].ratio,this._lineTo(r[1].x,r[1].y,!0),this._lineTo(r[0].x,r[0].y,!0);else{this._start=this._dist+n*r[0].ratio;for(let t=0;t<r.length;t++)this._lineTo(r[t].x,r[t].y,!0)}this._lineTo(e.x,e.y,!1)}}this._dist+=n,this._prevIsIn=r,this._prevPt=i}close(){if(this._line.length>2){const t=this._firstPt,e=this._prevPt;t.x===e.x&&t.y===e.y||this.lineTo(t.x,t.y);const r=this._line;let i=r.length;for(;i>=4&&(r[0].x===r[1].x&&r[0].x===r[i-2].x||r[0].y===r[1].y&&r[0].y===r[i-2].y);)r.pop(),r[0].x=r[i-2].x,r[0].y=r[i-2].y,--i}}result(t=!0){return this._pushLine(),0===this._lines.length?null:(this._type===i.Polygon&&t&&h.simplify(this._tileSize,this._margin*this._finalRatio,this._lines),this._lines)}resultWithStarts(){if(this._type!==i.LineString)throw new Error("Only valid for lines");this._pushLine();const t=this._lines,e=t.length;if(0===e)return null;const r=[];for(let i=0;i<e;i++)r.push({line:t[i],start:this._starts[i]||0});return r}_isIn(t,e){return t>=this._xmin&&t<=this._xmax&&e>=this._ymin&&e<=this._ymax}_intersect(t,e){let r,i,n;if(e.x>=this._xmin&&e.x<=this._xmax)i=e.y<=this._ymin?this._ymin:this._ymax,n=(i-t.y)/(e.y-t.y),r=t.x+n*(e.x-t.x);else if(e.y>=this._ymin&&e.y<=this._ymax)r=e.x<=this._xmin?this._xmin:this._xmax,n=(r-t.x)/(e.x-t.x),i=t.y+n*(e.y-t.y);else{i=e.y<=this._ymin?this._ymin:this._ymax,r=e.x<=this._xmin?this._xmin:this._xmax;const s=(r-t.x)/(e.x-t.x),a=(i-t.y)/(e.y-t.y);s<a?(n=s,i=t.y+s*(e.y-t.y)):(n=a,r=t.x+a*(e.x-t.x))}return this._r=n,new a(r,i)}_pushLine(){this._line&&(this._type===i.Point?this._line.length>0&&(this._lines.push(this._line),this._starts.push(this._start)):this._type===i.LineString?this._line.length>1&&(this._lines.push(this._line),this._starts.push(this._start)):this._type===i.Polygon&&this._line.length>3&&(this._lines.push(this._line),this._starts.push(this._start))),this._line=[],this._start=0}_moveTo(t,e,r){this._type!==i.Polygon?r&&(t=Math.round((t-(this._xmin+this._margin))*this._finalRatio),e=Math.round((e-(this._ymin+this._margin))*this._finalRatio),this._line.push(new a(t,e))):(r||(t<this._xmin&&(t=this._xmin),t>this._xmax&&(t=this._xmax),e<this._ymin&&(e=this._ymin),e>this._ymax&&(e=this._ymax)),t=Math.round((t-(this._xmin+this._margin))*this._finalRatio),e=Math.round((e-(this._ymin+this._margin))*this._finalRatio),this._line.push(new a(t,e)),this._isH=!1,this._isV=!1)}_lineTo(t,e,r){let n,s;if(this._type!==i.Polygon)if(r){if(t=Math.round((t-(this._xmin+this._margin))*this._finalRatio),e=Math.round((e-(this._ymin+this._margin))*this._finalRatio),this._line.length>0&&(n=this._line[this._line.length-1],n.equals(t,e)))return;this._line.push(new a(t,e))}else this._line&&this._line.length>0&&this._pushLine();else if(r||(t<this._xmin&&(t=this._xmin),t>this._xmax&&(t=this._xmax),e<this._ymin&&(e=this._ymin),e>this._ymax&&(e=this._ymax)),t=Math.round((t-(this._xmin+this._margin))*this._finalRatio),e=Math.round((e-(this._ymin+this._margin))*this._finalRatio),this._line&&this._line.length>0){n=this._line[this._line.length-1];const r=n.x===t,i=n.y===e;if(r&&i)return;this._isH&&r||this._isV&&i?(n.x=t,n.y=e,s=this._line[this._line.length-2],s.x===t&&s.y===e?(this._line.pop(),this._line.length<=1?(this._isH=!1,this._isV=!1):(s=this._line[this._line.length-2],this._isH=s.x===t,this._isV=s.y===e)):(this._isH=s.x===t,this._isV=s.y===e)):(this._line.push(new a(t,e)),this._isH=r,this._isV=i)}else this._line.push(new a(t,e))}}class u{setExtent(t){this._ratio=4096===t?1:4096/t}get validateTessellation(){return this._ratio<1}reset(t){this._lines=[],this._line=null}moveTo(t,e){this._line&&this._lines.push(this._line),this._line=[];const r=this._ratio;this._line.push(new a(t*r,e*r))}lineTo(t,e){const r=this._ratio;this._line.push(new a(t*r,e*r))}close(){const t=this._line;t&&!t[0].isEqual(t[t.length-1])&&t.push(t[0])}result(){return this._line&&this._lines.push(this._line),0===this._lines.length?null:this._lines}}!function(t){t[t.sideLeft=0]="sideLeft",t[t.sideRight=1]="sideRight",t[t.sideTop=2]="sideTop",t[t.sideBottom=3]="sideBottom"}(n||(n={}));class h{static simplify(t,e,r){if(!r)return;const i=-e,s=t+e,a=-e,o=t+e,l=[],u=[],c=r.length;for(let t=0;t<c;++t){const e=r[t];if(!e||e.length<2)continue;let h,c=e[0];const p=e.length;for(let r=1;r<p;++r)h=e[r],c.x===h.x&&(c.x<=i&&(c.y>h.y?(l.push(t),l.push(r),l.push(n.sideLeft),l.push(-1)):(u.push(t),u.push(r),u.push(n.sideLeft),u.push(-1))),c.x>=s&&(c.y<h.y?(l.push(t),l.push(r),l.push(n.sideRight),l.push(-1)):(u.push(t),u.push(r),u.push(n.sideRight),u.push(-1)))),c.y===h.y&&(c.y<=a&&(c.x<h.x?(l.push(t),l.push(r),l.push(n.sideTop),l.push(-1)):(u.push(t),u.push(r),u.push(n.sideTop),u.push(-1))),c.y>=o&&(c.x>h.x?(l.push(t),l.push(r),l.push(n.sideBottom),l.push(-1)):(u.push(t),u.push(r),u.push(n.sideBottom),u.push(-1)))),c=h}if(0===l.length||0===u.length)return;h.fillParent(r,u,l),h.fillParent(r,l,u);const p=[];h.calcDeltas(p,u,l),h.calcDeltas(p,l,u),h.addDeltas(p,r)}static fillParent(t,e,r){const i=r.length,s=e.length;for(let a=0;a<s;a+=4){const s=e[a],o=e[a+1],l=e[a+2],u=t[s][o-1],h=t[s][o];let p=8092,y=-1;for(let e=0;e<i;e+=4){if(r[e+2]!==l)continue;const i=r[e],s=r[e+1],a=t[i][s-1],o=t[i][s];switch(l){case n.sideLeft:case n.sideRight:if(c(u.y,a.y,o.y)&&c(h.y,a.y,o.y)){const t=Math.abs(o.y-a.y);t<p&&(p=t,y=e)}break;case n.sideTop:case n.sideBottom:if(c(u.x,a.x,o.x)&&c(h.x,a.x,o.x)){const t=Math.abs(o.x-a.x);t<p&&(p=t,y=e)}}}e[a+3]=y}}static calcDeltas(t,e,r){const i=e.length;for(let n=0;n<i;n+=4){const i=[],s=h.calcDelta(n,e,r,i);t.push(e[n]),t.push(e[n+1]),t.push(e[n+2]),t.push(s)}}static calcDelta(t,e,r,i){const n=e[t+3];if(-1===n)return 0;const s=i.length;return s>1&&i[s-2]===n?0:(i.push(n),h.calcDelta(n,r,e,i)+1)}static addDeltas(t,e){const r=t.length;let i=0;for(let e=0;e<r;e+=4){const r=t[e+3];r>i&&(i=r)}for(let s=0;s<r;s+=4){const r=e[t[s]],a=t[s+1],o=i-t[s+3];switch(t[s+2]){case n.sideLeft:r[a-1].x-=o,r[a].x-=o,1===a&&(r[r.length-1].x-=o),a===r.length-1&&(r[0].x-=o);break;case n.sideRight:r[a-1].x+=o,r[a].x+=o,1===a&&(r[r.length-1].x+=o),a===r.length-1&&(r[0].x+=o);break;case n.sideTop:r[a-1].y-=o,r[a].y-=o,1===a&&(r[r.length-1].y-=o),a===r.length-1&&(r[0].y-=o);break;case n.sideBottom:r[a-1].y+=o,r[a].y+=o,1===a&&(r[r.length-1].y+=o),a===r.length-1&&(r[0].y+=o)}}}}const c=(t,e,r)=>t>=e&&t<=r||t>=r&&t<=e},31363:(t,e,r)=>{r.d(e,{B1:()=>i,DQ:()=>u,DT:()=>o,JJ:()=>n,Or:()=>h,_U:()=>s,k3:()=>c,sX:()=>p});const i=Number.POSITIVE_INFINITY,n=Math.PI,s=2*n,a=128/n,o=n/180,l=1/Math.LN2;function u(t,e){return(t%=e)>=0?t:t+e}function h(t){return u(t*a,256)}function c(t){return Math.log(t)*l}function p(t,e,r){return t*(1-r)+e*r}},26084:(t,e,r)=>{r.d(e,{Z:()=>i});class i{constructor(t){this._array=[],t<=0&&console.error("strideInBytes must be positive!"),this._stride=t}get array(){return this._array}get index(){return 4*this._array.length/this._stride}get itemSize(){return this._stride}get sizeInBytes(){return 4*this._array.length}reset(){this.array.length=0}toBuffer(){return new Uint32Array(this._array).buffer}static i1616to32(t,e){return 65535&t|e<<16}static i8888to32(t,e,r,i){return 255&t|(255&e)<<8|(255&r)<<16|i<<24}static i8816to32(t,e,r){return 255&t|(255&e)<<8|r<<16}}},4215:(t,e,r)=>{var i,n,s;r.d(e,{Fr:()=>s,_K:()=>n,al:()=>i}),function(t){t[t.FILL=1]="FILL",t[t.LINE=2]="LINE",t[t.SYMBOL=3]="SYMBOL",t[t.CIRCLE=4]="CIRCLE"}(i||(i={})),function(t){t[t.BACKGROUND=0]="BACKGROUND",t[t.FILL=1]="FILL",t[t.OUTLINE=2]="OUTLINE",t[t.LINE=3]="LINE",t[t.ICON=4]="ICON",t[t.CIRCLE=5]="CIRCLE",t[t.TEXT=6]="TEXT",t[t.TILEINFO=7]="TILEINFO"}(n||(n={})),function(t){t[t.PAINTER_CHANGED=0]="PAINTER_CHANGED",t[t.LAYOUT_CHANGED=1]="LAYOUT_CHANGED",t[t.LAYER_CHANGED=2]="LAYER_CHANGED",t[t.LAYER_REMOVED=3]="LAYER_REMOVED",t[t.SPRITES_CHANGED=4]="SPRITES_CHANGED"}(s||(s={}))},21315:(t,e,r)=>{r.d(e,{R:()=>s,_5:()=>u,aF:()=>a,f2:()=>f,fR:()=>i,nR:()=>l,r1:()=>c,vL:()=>o});var i,n,s,a,o,l,u,h,c,p,y=r(95648);(p=i||(i={}))[p.BACKGROUND=0]="BACKGROUND",p[p.FILL=1]="FILL",p[p.LINE=2]="LINE",p[p.SYMBOL=3]="SYMBOL",p[p.CIRCLE=4]="CIRCLE",function(t){t[t.VISIBLE=0]="VISIBLE",t[t.NONE=1]="NONE"}(n||(n={})),function(t){t[t.POINT=0]="POINT",t[t.LINE=1]="LINE",t[t.LINE_CENTER=2]="LINE_CENTER"}(s||(s={})),function(t){t[t.MAP=0]="MAP",t[t.VIEWPORT=1]="VIEWPORT",t[t.AUTO=2]="AUTO"}(a||(a={})),function(t){t[t.AUTO=0]="AUTO",t[t.LEFT=1]="LEFT",t[t.CENTER=2]="CENTER",t[t.RIGHT=3]="RIGHT"}(o||(o={})),function(t){t[t.CENTER=0]="CENTER",t[t.LEFT=1]="LEFT",t[t.RIGHT=2]="RIGHT",t[t.TOP=3]="TOP",t[t.BOTTOM=4]="BOTTOM",t[t.TOP_LEFT=5]="TOP_LEFT",t[t.TOP_RIGHT=6]="TOP_RIGHT",t[t.BOTTOM_LEFT=7]="BOTTOM_LEFT",t[t.BOTTOM_RIGHT=8]="BOTTOM_RIGHT"}(l||(l={})),function(t){t[t.NONE=0]="NONE",t[t.UPPERCASE=1]="UPPERCASE",t[t.LOWERCASE=2]="LOWERCASE"}(u||(u={})),function(t){t[t.MAP=0]="MAP",t[t.VIEWPORT=1]="VIEWPORT"}(h||(h={})),function(t){t[t.HORIZONTAL=0]="HORIZONTAL",t[t.VERTICAL=1]="VERTICAL"}(c||(c={}));class f{}f.backgroundLayoutDefinition={visibility:{type:"enum",values:["visible","none"],default:n.VISIBLE}},f.fillLayoutDefinition={visibility:{type:"enum",values:["visible","none"],default:n.VISIBLE}},f.lineLayoutDefinition={visibility:{type:"enum",values:["visible","none"],default:n.VISIBLE},"line-cap":{type:"enum",values:["butt","round","square"],default:y.RL.BUTT},"line-join":{type:"enum",values:["bevel","round","miter"],default:y.AH.MITER},"line-miter-limit":{type:"number",default:2},"line-round-limit":{type:"number",default:1.05}},f.symbolLayoutDefinition={visibility:{type:"enum",values:["visible","none"],default:n.VISIBLE},"symbol-avoid-edges":{type:"boolean",default:!1},"symbol-placement":{type:"enum",values:["point","line","line-center"],default:s.POINT},"symbol-sort-key":{type:"number",default:-1},"symbol-spacing":{type:"number",minimum:1,default:250},"icon-allow-overlap":{type:"boolean",default:!1},"icon-anchor":{type:"enum",values:["center","left","right","top","bottom","top-left","top-right","bottom-left","bottom-right"],default:l.CENTER},"icon-ignore-placement":{type:"boolean",default:!1},"icon-image":{type:"string"},"icon-keep-upright":{type:"boolean",default:!1},"icon-offset":{type:"array",value:"number",length:2,default:[0,0]},"icon-optional":{type:"boolean",default:!1},"icon-padding":{type:"number",minimum:0,default:2},"icon-rotate":{type:"number",default:0},"icon-rotation-alignment":{type:"enum",values:["map","viewport","auto"],default:a.AUTO},"icon-size":{type:"number",minimum:0,default:1},"text-allow-overlap":{type:"boolean",default:!1},"text-anchor":{type:"enum",values:["center","left","right","top","bottom","top-left","top-right","bottom-left","bottom-right"],default:l.CENTER},"text-field":{type:"string"},"text-font":{type:"array",value:"string",default:["Open Sans Regular","Arial Unicode MS Regular"]},"text-ignore-placement":{type:"boolean",default:!1},"text-justify":{type:"enum",values:["auto","left","center","right"],default:o.CENTER},"text-keep-upright":{type:"boolean",default:!0},"text-letter-spacing":{type:"number",default:0},"text-line-height":{type:"number",default:1.2},"text-max-angle":{type:"number",minimum:0,default:45},"text-max-width":{type:"number",minimum:0,default:10},"text-offset":{type:"array",value:"number",length:2,default:[0,0]},"text-optional":{type:"boolean",default:!1},"text-padding":{type:"number",minimum:0,default:2},"text-rotate":{type:"number",default:0},"text-rotation-alignment":{type:"enum",values:["map","viewport","auto"],default:a.AUTO},"text-size":{type:"number",minimum:0,default:16},"text-transform":{type:"enum",values:["none","uppercase","lowercase"],default:u.NONE},"text-writing-mode":{type:"array",value:"enum",values:["horizontal","vertical"],default:[c.HORIZONTAL]}},f.circleLayoutDefinition={visibility:{type:"enum",values:["visible","none"],default:n.VISIBLE}},f.backgroundPaintDefinition={"background-color":{type:"color",default:[0,0,0,1]},"background-opacity":{type:"number",minimum:0,maximum:1,default:1},"background-pattern":{type:"string"}},f.fillPaintDefinition={"fill-antialias":{type:"boolean",default:!0},"fill-color":{type:"color",default:[0,0,0,1]},"fill-opacity":{type:"number",minimum:0,maximum:1,default:1},"fill-outline-color":{type:"color",default:[0,0,0,0]},"fill-pattern":{type:"string"},"fill-translate":{type:"array",value:"number",length:2,default:[0,0]},"fill-translate-anchor":{type:"enum",values:["map","viewport"],default:h.MAP}},f.linePaintDefinition={"line-blur":{type:"number",minimum:0,default:0},"line-color":{type:"color",default:[0,0,0,1]},"line-dasharray":{type:"array",value:"number",default:[]},"line-gap-width":{type:"number",minimum:0,default:0},"line-offset":{type:"number",default:0},"line-opacity":{type:"number",minimum:0,maximum:1,default:1},"line-pattern":{type:"string"},"line-translate":{type:"array",value:"number",length:2,default:[0,0]},"line-translate-anchor":{type:"enum",values:["map","viewport"],default:h.MAP},"line-width":{type:"number",minimum:0,default:1}},f.symbolPaintDefinition={"icon-color":{type:"color",default:[0,0,0,1]},"icon-halo-blur":{type:"number",minimum:0,default:0},"icon-halo-color":{type:"color",default:[0,0,0,0]},"icon-halo-width":{type:"number",minimum:0,default:0},"icon-opacity":{type:"number",minimum:0,maximum:1,default:1},"icon-translate":{type:"array",value:"number",length:2,default:[0,0]},"icon-translate-anchor":{type:"enum",values:["map","viewport"],default:h.MAP},"text-color":{type:"color",default:[0,0,0,1]},"text-halo-blur":{type:"number",minimum:0,default:0},"text-halo-color":{type:"color",default:[0,0,0,0]},"text-halo-width":{type:"number",minimum:0,default:0},"text-opacity":{type:"number",minimum:0,maximum:1,default:1},"text-translate":{type:"array",value:"number",length:2,default:[0,0]},"text-translate-anchor":{type:"enum",values:["map","viewport"],default:h.MAP}},f.rasterPaintDefinition={"raster-opacity":{type:"number",minimum:0,maximum:1,default:1},"raster-hue-rotate":{type:"number",default:0},"raster-brightness-min":{type:"number",minimum:0,maximum:1,default:0},"raster-brightness-max":{type:"number",minimum:0,maximum:1,default:1},"raster-saturation":{type:"number",minimum:-1,maximum:1,default:0},"raster-contrast":{type:"number",minimum:-1,maximum:1,default:0},"raster-fade-duration":{type:"number",minimum:0,default:300}},f.circlePaintDefinition={"circle-blur":{type:"number",minimum:0,default:0},"circle-color":{type:"color",default:[0,0,0,1]},"circle-opacity":{type:"number",minimum:0,maximum:1,default:1},"circle-radius":{type:"number",minimum:0,default:5},"circle-stroke-color":{type:"color",default:[0,0,0,1]},"circle-stroke-opacity":{type:"number",minimum:0,maximum:1,default:1},"circle-stroke-width":{type:"number",minimum:0,default:0},"circle-translate":{type:"array",value:"number",length:2,default:[0,0]},"circle-translate-anchor":{type:"enum",values:["map","viewport"],default:h.MAP}}},7333:(t,e,r)=>{r.d(e,{Et:()=>Pt,sj:()=>Bt,Le:()=>St,_L:()=>Gt,gf:()=>Ot,jG:()=>Ut,nj:()=>Mt});var i,n,s=r(4215),a=r(70586),o=r(26084);!function(t){t[t.R8_SIGNED=0]="R8_SIGNED",t[t.R8_UNSIGNED=1]="R8_UNSIGNED",t[t.R16_SIGNED=2]="R16_SIGNED",t[t.R16_UNSIGNED=3]="R16_UNSIGNED",t[t.R8G8_SIGNED=4]="R8G8_SIGNED",t[t.R8G8_UNSIGNED=5]="R8G8_UNSIGNED",t[t.R16G16_SIGNED=6]="R16G16_SIGNED",t[t.R16G16_UNSIGNED=7]="R16G16_UNSIGNED",t[t.R8G8B8A8_SIGNED=8]="R8G8B8A8_SIGNED",t[t.R8G8B8A8_UNSIGNED=9]="R8G8B8A8_UNSIGNED",t[t.R8G8B8A8_COLOR=10]="R8G8B8A8_COLOR",t[t.R16G16B16A16_DASHARRAY=11]="R16G16B16A16_DASHARRAY",t[t.R16G16B16A16_PATTERN=12]="R16G16B16A16_PATTERN"}(i||(i={})),function(t){t[t.UNIFORM=0]="UNIFORM",t[t.DATA_DRIVEN=1]="DATA_DRIVEN",t[t.INTERPOLATED_DATA_DRIVEN=2]="INTERPOLATED_DATA_DRIVEN",t[t.UNUSED=3]="UNUSED"}(n||(n={}));var l=r(35371),u=r(21968);class h{constructor(t){this._locations=new Map,this._key=t}get key(){return this._key}get type(){return 7&this._key}defines(){return[]}getStride(){return this._layoutInfo||this._buildAttributesInfo(),this._stride}getAttributeLocations(){return 0===this._locations.size&&this._buildAttributesInfo(),this._locations}getLayoutInfo(){return this._layoutInfo||this._buildAttributesInfo(),this._layoutInfo}getEncodingInfos(){return this._propertyEncodingInfo||this._buildAttributesInfo(),this._propertyEncodingInfo}getUniforms(){return this._uniforms||this._buildAttributesInfo(),this._uniforms}getShaderHeader(){return this._shaderHeader||this._buildAttributesInfo(),this._shaderHeader}getShaderMain(){return this._shaderMain||this._buildAttributesInfo(),this._shaderMain}setDataUniforms(t,e,r,i,n){const s=this.getUniforms();for(const a of s){const{name:s,type:o,getValue:l}=a,u=l(r,e,i,n);if(null!==u)switch(o){case"float":t.setUniform1f(s,u);break;case"vec2":t.setUniform2fv(s,u);break;case"vec4":t.setUniform4fv(s,u)}}}encodeAttributes(t,e,r,n){const s=this.attributesInfo(),a=this.getEncodingInfos(),o=[];let l=0,u=0;for(const h of Object.keys(a)){const c=a[h],{type:p,precisionFactor:y,isLayout:f}=s[h],_=f?r.getLayoutProperty(h):r.getPaintProperty(h),m=_.interpolator?.getInterpolationRange(e);let g=0;for(const r of c){const{offset:s,bufferElementsToAdd:a}=r;if(a>0){for(let t=0;t<a;t++)o.push(0);l+=u,u=r.bufferElementsToAdd}const h=n??_.getValue(m?m[g]:e,t);switch(p){case i.R8_SIGNED:case i.R8_UNSIGNED:o[l]|=this._encodeByte(h*(y||1),8*s);break;case i.R16_SIGNED:case i.R16_UNSIGNED:o[l]|=this._encodeShort(h*(y||1),8*s);break;case i.R8G8_SIGNED:case i.R8G8_UNSIGNED:o[l]|=this._encodeByte(h*(y||1),8*s),o[l]|=this._encodeByte(h*(y||1),8*s+8);break;case i.R16G16_SIGNED:case i.R16G16_UNSIGNED:o[l]|=this._encodeShort(h*(y||1),8*s),o[l]|=this._encodeShort(h*(y||1),8*s+16);break;case i.R8G8B8A8_SIGNED:case i.R8G8B8A8_UNSIGNED:o[l]|=this._encodeByte(h*(y||1),8*s),o[l]|=this._encodeByte(h*(y||1),8*s+8),o[l]|=this._encodeByte(h*(y||1),8*s+16),o[l]|=this._encodeByte(h*(y||1),8*s+24);break;case i.R8G8B8A8_COLOR:o[l]=this._encodeColor(h);break;case i.R16G16B16A16_DASHARRAY:case i.R16G16B16A16_PATTERN:this._encodePattern(l,o,h);break;default:throw new Error("Unsupported encoding type")}g++}}return o}getAtributeState(t){let e=0;const r=3+2*t;return e|=this._bit(r),e|=this._bit(r+1)<<1,e}_buildAttributesInfo(){const t=[],e={},r={};let i=-1;const s=this.attributesInfo(),a=this.attributes();let o=-1;for(const l of a){o++;const a=this.getAtributeState(o);if(a===n.UNIFORM||a===n.UNUSED)continue;const u=s[l],c=[];e[l]=c;const p=u.type;for(let e=0;e<a;e++){const{dataType:e,bytesPerElement:n,count:s,normalized:a}=h._encodingInfo[p],o=n*s,l=`${e}-${!0===a}`;let u=r[l],y=0;if(!u||u.count+s>4)i++,u={dataIndex:i,count:0,offset:0},4!==s&&(r[l]=u),t.push({location:-1,name:"a_data_"+i,count:s,type:e,normalized:a}),y=Math.ceil(Math.max(o/4,1));else{const e=t[u.dataIndex];e.count+=s,y=Math.ceil(Math.max(e.count*n/4,1))-Math.ceil(Math.max(u.offset/4,1))}c.push({dataIndex:u.dataIndex,offset:u.offset,bufferElementsToAdd:y}),u.offset+=o,u.count+=s}}for(const e of t)switch(e.type){case l.g.BYTE:case l.g.UNSIGNED_BYTE:e.count=4;break;case l.g.SHORT:case l.g.UNSIGNED_SHORT:e.count+=e.count%2}this._buildVertexBufferLayout(t);let u=0;const c=this._layoutInfo.geometry;for(const t of c)this._locations.set(t.name,u++);const p=this._layoutInfo.opacity;if(p)for(const t of p)this._locations.set(t.name,u++);this._buildShaderInfo(t,e),this._propertyEncodingInfo=e}_buildVertexBufferLayout(t){const e={},r=this.geometryInfo();let i=r[0].stride;if(0===t.length)e.geometry=r;else{const n=[];let s=i;for(const e of t)i+=c(e.type)*e.count;for(const t of r)n.push(new u.G(t.name,t.count,t.type,t.offset,i,t.normalized));for(const e of t)n.push(new u.G(e.name,e.count,e.type,s,i,e.normalized)),s+=c(e.type)*e.count;e.geometry=n}this.opacityInfo()&&(e.opacity=this.opacityInfo()),this._layoutInfo=e,this._stride=i}_buildShaderInfo(t,e){let r="\n",s="\n";const o=[];for(const e of t)r+=`attribute ${this._getType(e.count)} ${e.name};\n`;const l=this.attributes(),u=this.attributesInfo();let c=-1;for(const t of l){c++;const{name:l,type:y,precisionFactor:f,isLayout:_}=u[t],m=f&&1!==f?" * "+1/f:"",{bytesPerElement:g,count:d}=h._encodingInfo[y],x=t=>`a_data_${t.dataIndex}${p(d,t.offset,g)}`;switch(this.getAtributeState(c)){case n.UNIFORM:{const e=this._getType(d),n=`u_${l}`;o.push({name:n,type:e,getValue:(e,r,n,s)=>{const o=_?e.getLayoutValue(t,r):e.getPaintValue(t,r);if(y===i.R16G16B16A16_DASHARRAY){const t=e.getDashKey(o,e.getLayoutValue("line-cap",r)),i=s.getMosaicItemPosition(t,!1);if((0,a.Wi)(i))return null;const{tl:n,br:l}=i;return[n[0],l[1],l[0],n[1]]}if(y===i.R16G16B16A16_PATTERN){const e=s.getMosaicItemPosition(o,!t.includes("line-"));if((0,a.Wi)(e))return null;const{tl:r,br:i}=e;return[r[0],i[1],i[0],r[1]]}if(y===i.R8G8B8A8_COLOR){const t=o[3];return[t*o[0],t*o[1],t*o[2],t]}return o}}),r+=`uniform ${e} ${n};\n`,s+=`${e} ${l} = ${n};\n`}break;case n.DATA_DRIVEN:{const r=x(e[t][0]);s+=`${this._getType(d)} ${l} = ${r}${m};\n`}break;case n.INTERPOLATED_DATA_DRIVEN:{const i=`u_t_${l}`;o.push({name:i,type:"float",getValue:(e,r,i,n)=>(_?e.getLayoutProperty(t):e.getPaintProperty(t)).interpolator.interpolationUniformValue(i,r)}),r+=`uniform float ${i};\n`;const n=x(e[t][0]),a=x(e[t][1]);s+=`${this._getType(d)} ${l} = mix(${n}${m}, ${a}${m}, ${i});\n`}}}this._shaderHeader=r,this._shaderMain=s,this._uniforms=o}_bit(t){return(this._key&1<<t)>>t}_getType(t){switch(t){case 1:return"float";case 2:return"vec2";case 3:return"vec3";case 4:return"vec4"}throw new Error("Invalid count")}_encodeColor(t){const e=255*t[3];return o.Z.i8888to32(t[0]*e,t[1]*e,t[2]*e,e)}_encodePattern(t,e,r){if(!r||!r.rect)return;const i=r.rect,n=r.width,s=r.height;e[t]=this._encodeShort(i.x+2,0),e[t]|=this._encodeShort(i.y+2+s,16),e[t+1]=this._encodeShort(i.x+2+n,0),e[t+1]|=this._encodeShort(i.y+2,16)}_encodeByte(t,e){return(255&t)<<e}_encodeShort(t,e){return(65535&t)<<e}}h._encodingInfo={[i.R8_SIGNED]:{dataType:l.g.BYTE,bytesPerElement:1,count:1,normalized:!1},[i.R8_UNSIGNED]:{dataType:l.g.UNSIGNED_BYTE,bytesPerElement:1,count:1,normalized:!1},[i.R16_SIGNED]:{dataType:l.g.SHORT,bytesPerElement:2,count:1,normalized:!1},[i.R16_UNSIGNED]:{dataType:l.g.UNSIGNED_SHORT,bytesPerElement:2,count:1,normalized:!1},[i.R8G8_SIGNED]:{dataType:l.g.BYTE,bytesPerElement:1,count:2,normalized:!1},[i.R8G8_UNSIGNED]:{dataType:l.g.UNSIGNED_BYTE,bytesPerElement:1,count:2,normalized:!1},[i.R16G16_SIGNED]:{dataType:l.g.SHORT,bytesPerElement:2,count:2,normalized:!1},[i.R16G16_UNSIGNED]:{dataType:l.g.UNSIGNED_SHORT,bytesPerElement:2,count:2,normalized:!1},[i.R8G8B8A8_SIGNED]:{dataType:l.g.BYTE,bytesPerElement:1,count:4,normalized:!1},[i.R8G8B8A8_UNSIGNED]:{dataType:l.g.UNSIGNED_BYTE,bytesPerElement:1,count:4,normalized:!1},[i.R8G8B8A8_COLOR]:{dataType:l.g.UNSIGNED_BYTE,bytesPerElement:1,count:4,normalized:!0},[i.R16G16B16A16_DASHARRAY]:{dataType:l.g.UNSIGNED_SHORT,bytesPerElement:2,count:4,normalized:!1},[i.R16G16B16A16_PATTERN]:{dataType:l.g.UNSIGNED_SHORT,bytesPerElement:2,count:4,normalized:!1}};const c=t=>{switch(t){case l.g.FLOAT:case l.g.INT:case l.g.UNSIGNED_INT:return 4;case l.g.SHORT:case l.g.UNSIGNED_SHORT:return 2;case l.g.BYTE:case l.g.UNSIGNED_BYTE:return 1}},p=(t,e,r)=>{const i=e/r;if(1===t)switch(i){case 0:return".x";case 1:return".y";case 2:return".z";case 3:return".w"}else if(2===t)switch(i){case 0:return".xy";case 1:return".yz";case 2:return".zw"}else if(3===t)switch(i){case 0:return".xyz";case 1:return".yzw"}return""};class y extends h{constructor(t){super(t)}geometryInfo(){return y.GEOMETRY_LAYOUT}opacityInfo(){return null}attributes(){return y.ATTRIBUTES}attributesInfo(){return y.ATTRIBUTES_INFO}}y.ATTRIBUTES=[],y.GEOMETRY_LAYOUT=[new u.G("a_pos",2,l.g.BYTE,0,2)],y.ATTRIBUTES_INFO={};class f extends h{constructor(t){super(t)}geometryInfo(){return f.GEOMETRY_LAYOUT}opacityInfo(){return null}attributes(){return f.ATTRIBUTES}attributesInfo(){return f.ATTRIBUTES_INFO}}f.ATTRIBUTES=["circle-radius","circle-color","circle-opacity","circle-stroke-width","circle-stroke-color","circle-stroke-opacity","circle-blur"],f.GEOMETRY_LAYOUT=[new u.G("a_pos",2,l.g.SHORT,0,4)],f.ATTRIBUTES_INFO={"circle-radius":{name:"radius",type:i.R8_UNSIGNED},"circle-color":{name:"color",type:i.R8G8B8A8_COLOR},"circle-opacity":{name:"opacity",type:i.R8_UNSIGNED,precisionFactor:100},"circle-stroke-width":{name:"stroke_width",type:i.R8_UNSIGNED,precisionFactor:4},"circle-stroke-color":{name:"stroke_color",type:i.R8G8B8A8_COLOR},"circle-stroke-opacity":{name:"stroke_opacity",type:i.R8_UNSIGNED,precisionFactor:100},"circle-blur":{name:"blur",type:i.R8_UNSIGNED,precisionFactor:32}};class _ extends h{constructor(t){super(t)}geometryInfo(){return _.GEOMETRY_LAYOUT}opacityInfo(){return null}attributes(){return _.ATTRIBUTES}attributesInfo(){return _.ATTRIBUTES_INFO}}_.ATTRIBUTES=["fill-color","fill-opacity","fill-pattern"],_.GEOMETRY_LAYOUT=[new u.G("a_pos",2,l.g.SHORT,0,4)],_.ATTRIBUTES_INFO={"fill-color":{name:"color",type:i.R8G8B8A8_COLOR},"fill-opacity":{name:"opacity",type:i.R8_UNSIGNED,precisionFactor:100},"fill-pattern":{name:"tlbr",type:i.R16G16B16A16_PATTERN,isOptional:!0}};class m extends h{constructor(t,e){super(t),this._usefillColor=e}geometryInfo(){return m.GEOMETRY_LAYOUT}opacityInfo(){return null}attributes(){return this._usefillColor?m.ATTRIBUTES_FILL:m.ATTRIBUTES_OUTLINE}attributesInfo(){return this._usefillColor?m.ATTRIBUTES_INFO_FILL:m.ATTRIBUTES_INFO_OUTLINE}}m.ATTRIBUTES_OUTLINE=["fill-outline-color","fill-opacity"],m.ATTRIBUTES_FILL=["fill-color","fill-opacity"],m.GEOMETRY_LAYOUT=[new u.G("a_pos",2,l.g.SHORT,0,8),new u.G("a_offset",2,l.g.BYTE,4,8),new u.G("a_xnormal",2,l.g.BYTE,6,8)],m.ATTRIBUTES_INFO_OUTLINE={"fill-outline-color":{name:"color",type:i.R8G8B8A8_COLOR},"fill-opacity":{name:"opacity",type:i.R8_UNSIGNED,precisionFactor:100}},m.ATTRIBUTES_INFO_FILL={"fill-color":{name:"color",type:i.R8G8B8A8_COLOR},"fill-opacity":{name:"opacity",type:i.R8_UNSIGNED,precisionFactor:100}};class g extends h{constructor(t){super(t)}geometryInfo(){return g.GEOMETRY_LAYOUT}opacityInfo(){return null}attributes(){return g.ATTRIBUTES}attributesInfo(){return g.ATTRIBUTES_INFO}}g.ATTRIBUTES=["line-blur","line-color","line-gap-width","line-offset","line-opacity","line-width","line-pattern","line-dasharray"],g.GEOMETRY_LAYOUT=[new u.G("a_pos",2,l.g.SHORT,0,16),new u.G("a_extrude_offset",4,l.g.BYTE,4,16),new u.G("a_dir_normal",4,l.g.BYTE,8,16),new u.G("a_accumulatedDistance",2,l.g.UNSIGNED_SHORT,12,16)],g.ATTRIBUTES_INFO={"line-width":{name:"width",type:i.R8_UNSIGNED,precisionFactor:2},"line-gap-width":{name:"gap_width",type:i.R8_UNSIGNED,precisionFactor:2},"line-offset":{name:"offset",type:i.R8_SIGNED,precisionFactor:2},"line-color":{name:"color",type:i.R8G8B8A8_COLOR},"line-opacity":{name:"opacity",type:i.R8_UNSIGNED,precisionFactor:100},"line-blur":{name:"blur",type:i.R8_UNSIGNED,precisionFactor:4},"line-pattern":{name:"tlbr",type:i.R16G16B16A16_PATTERN,isOptional:!0},"line-dasharray":{name:"tlbr",type:i.R16G16B16A16_DASHARRAY,isOptional:!0}};const d=[new u.G("a_pos",2,l.g.SHORT,0,16),new u.G("a_vertexOffset",2,l.g.SHORT,4,16),new u.G("a_texAngleRange",4,l.g.UNSIGNED_BYTE,8,16),new u.G("a_levelInfo",4,l.g.UNSIGNED_BYTE,12,16)],x=[new u.G("a_opacityInfo",1,l.g.UNSIGNED_BYTE,0,1)];class b extends h{constructor(t){super(t)}geometryInfo(){return d}opacityInfo(){return x}attributes(){return b.ATTRIBUTES}attributesInfo(){return b.ATTRIBUTES_INFO}}b.ATTRIBUTES=["icon-color","icon-opacity","icon-halo-blur","icon-halo-color","icon-halo-width","icon-size"],b.ATTRIBUTES_INFO={"icon-color":{name:"color",type:i.R8G8B8A8_COLOR},"icon-opacity":{name:"opacity",type:i.R8_UNSIGNED,precisionFactor:100},"icon-halo-color":{name:"halo_color",type:i.R8G8B8A8_COLOR},"icon-halo-width":{name:"halo_width",type:i.R8_UNSIGNED,precisionFactor:4},"icon-halo-blur":{name:"halo_blur",type:i.R8_UNSIGNED,precisionFactor:4},"icon-size":{name:"size",type:i.R8_UNSIGNED,precisionFactor:32,isLayout:!0}};class E extends h{constructor(t){super(t)}geometryInfo(){return d}opacityInfo(){return x}attributes(){return E.ATTRIBUTES}attributesInfo(){return E.ATTRIBUTES_INFO}}E.ATTRIBUTES=["text-color","text-opacity","text-halo-blur","text-halo-color","text-halo-width","text-size"],E.ATTRIBUTES_INFO={"text-color":{name:"color",type:i.R8G8B8A8_COLOR},"text-opacity":{name:"opacity",type:i.R8_UNSIGNED,precisionFactor:100},"text-halo-color":{name:"halo_color",type:i.R8G8B8A8_COLOR},"text-halo-width":{name:"halo_width",type:i.R8_UNSIGNED,precisionFactor:4},"text-halo-blur":{name:"halo_blur",type:i.R8_UNSIGNED,precisionFactor:4},"text-size":{name:"size",type:i.R8_UNSIGNED,isLayout:!0}};var T=r(22303),w=r(3892),I=r(59255);function v(t,e,r,i){const n=3*t,s=3*(r-t)-n,a=1-n-s,o=3*e,l=3*(i-e)-o,u=1-o-l;function h(t){return((a*t+s)*t+n)*t}function c(t){return(3*a*t+2*s)*t+n}return function(t,e=1e-6){return function(t){return((u*t+l)*t+o)*t}(function(t,e){let r,i,n,s,a,o;for(n=t,o=0;o<8;o++){if(s=h(n)-t,Math.abs(s)<e)return n;if(a=c(n),Math.abs(a)<1e-6)break;n-=s/a}if(r=0,i=1,n=t,n<r)return r;if(n>i)return i;for(;r<i;){if(s=h(n),Math.abs(s-t)<e)return n;t>s?r=n:i=n,n=.5*(i-r)+r}return n}(t,e))}}const R={};R.ease=v(.25,.1,.25,1),R.linear=v(0,0,1,1),R.easeIn=R["ease-in"]=v(.42,0,1,1),R.easeOut=R["ease-out"]=v(0,0,.58,1),R.easeInOut=R["ease-in-out"]=v(.42,0,.58,1);var N=r(31363);const D={kind:"null"},L={kind:"number"},A={kind:"string"},P={kind:"boolean"},S={kind:"color"},O={kind:"object"},U={kind:"value"};function B(t,e){return{kind:"array",itemType:t,n:e}}const G=[D,L,A,P,S,O,B(U)];function M(t){if("array"===t.kind){const e=M(t.itemType);return"number"==typeof t.n?`array<${e}, ${t.n}>`:"value"===t.itemType.kind?"array":`array<${e}>`}return t.kind}function k(t){if(null===t)return D;if("string"==typeof t)return A;if("boolean"==typeof t)return P;if("number"==typeof t)return L;if(t instanceof T.Z)return S;if(Array.isArray(t)){let e;for(const r of t){const t=k(r);if(e){if(e!==t){e=U;break}}else e=t}return B(e||U,t.length)}return"object"==typeof t?O:U}function C(t,e){if("array"===e.kind)return"array"===t.kind&&(0===t.n&&"value"===t.itemType.kind||C(t.itemType,e.itemType))&&("number"!=typeof e.n||e.n===t.n);if("value"===e.kind)for(const e of G)if(C(t,e))return!0;return e.kind===t.kind}function F(t){if(null===t)return"";const e=typeof t;return"string"===e?t:"number"===e||"boolean"===e?String(t):t instanceof T.Z?t.toString():JSON.stringify(t)}class z{constructor(t){this._parent=t,this._vars={}}add(t,e){this._vars[t]=e}get(t){return this._vars[t]?this._vars[t]:this._parent?this._parent.get(t):null}}class V{constructor(){this.type=U}static parse(t){if(t.length>1)throw new Error('"id" does not expect arguments');return new V}evaluate(t,e){return t?.id}}class ${constructor(){this.type=A}static parse(t){if(t.length>1)throw new Error('"geometry-type" does not expect arguments');return new $}evaluate(t,e){if(!t)return null;switch(t.type){case I.Vl.Point:return"Point";case I.Vl.LineString:return"LineString";case I.Vl.Polygon:return"Polygon";default:return null}}}class Y{constructor(){this.type=O}static parse(t){if(t.length>1)throw new Error('"properties" does not expect arguments');return new Y}evaluate(t,e){return t?.values}}class H{constructor(){this.type=L}static parse(t){if(t.length>1)throw new Error('"zoom" does not expect arguments');return new H}evaluate(t,e){return e}}class Z{constructor(t,e,r){this._lhs=t,this._rhs=e,this._compare=r,this.type=P}static parse(t,e,r){if(3!==t.length&&4!==t.length)throw new Error(`"${t[0]}" expects 2 or 3 arguments`);if(4===t.length)throw new Error(`"${t[0]}" collator not supported`);return new Z(wt(t[1],e),wt(t[2],e),r)}evaluate(t,e){return this._compare(this._lhs.evaluate(t,e),this._rhs.evaluate(t,e))}}class j{constructor(t){this._arg=t,this.type=P}static parse(t,e){if(2!==t.length)throw new Error('"!" expects 1 argument');return new j(wt(t[1],e))}evaluate(t,e){return!this._arg.evaluate(t,e)}}class K{constructor(t){this._args=t,this.type=P}static parse(t,e){const r=[];for(let i=1;i<t.length;i++)r.push(wt(t[i],e));return new K(r)}evaluate(t,e){for(const r of this._args)if(!r.evaluate(t,e))return!1;return!0}}class q{constructor(t){this._args=t,this.type=P}static parse(t,e){const r=[];for(let i=1;i<t.length;i++)r.push(wt(t[i],e));return new q(r)}evaluate(t,e){for(const r of this._args)if(r.evaluate(t,e))return!0;return!1}}class W{constructor(t){this._args=t,this.type=P}static parse(t,e){const r=[];for(let i=1;i<t.length;i++)r.push(wt(t[i],e));return new W(r)}evaluate(t,e){for(const r of this._args)if(r.evaluate(t,e))return!1;return!0}}class X{constructor(t,e,r){this.type=t,this._args=e,this._fallback=r}static parse(t,e,r){if(t.length<4)throw new Error('"case" expects at least 3 arguments');if(t.length%2==1)throw new Error('"case" expects an odd number of arguments');let i;const n=[];for(let s=1;s<t.length-1;s+=2){const a=wt(t[s],e),o=wt(t[s+1],e,r);i||(i=o.type),n.push({condition:a,output:o})}const s=wt(t[t.length-1],e,r);return i||(i=s.type),new X(i,n,s)}evaluate(t,e){for(const r of this._args)if(r.condition.evaluate(t,e))return r.output.evaluate(t,e);return this._fallback.evaluate(t,e)}}class J{constructor(t,e){this.type=t,this._args=e}static parse(t,e){if(t.length<2)throw new Error('"coalesce" expects at least 1 argument');let r;const i=[];for(let n=1;n<t.length;n++){const s=wt(t[n],e);r||(r=s.type),i.push(s)}return new J(r,i)}evaluate(t,e){for(const r of this._args){const i=r.evaluate(t,e);if(null!==i)return i}return null}}class Q{constructor(t,e,r,i,n){this.type=t,this._input=e,this._labels=r,this._outputs=i,this._fallback=n}static parse(t,e){if(t.length<3)throw new Error('"match" expects at least 3 arguments');if(t.length%2==0)throw new Error('"case" expects an even number of arguments');let r;const i=wt(t[1],e),n=[],s={};let a;for(let i=2;i<t.length-1;i+=2){let o=t[i];Array.isArray(o)||(o=[o]);for(const t of o){const e=typeof t;if("string"!==e&&"number"!==e)throw new Error('"match" requires string or number literal as labels');if(a){if(e!==a)throw new Error('"match" requires labels to have the same type')}else a=e;s[t]=n.length}const l=wt(t[i+1],e);r||(r=l.type),n.push(l)}return new Q(r,i,s,n,wt(t[t.length-1],e))}evaluate(t,e){const r=this._input.evaluate(t,e);return(this._outputs[this._labels[r]]||this._fallback).evaluate(t,e)}}class tt{constructor(t,e,r,i,n){this._operator=t,this.type=e,this.interpolation=r,this.input=i,this._stops=n}static parse(t,e,r){const i=t[0];if(t.length<5)throw new Error(`"${i}" expects at least 4 arguments`);const n=t[1];if(!Array.isArray(n)||0===n.length)throw new Error(`"${n}" is not a valid interpolation`);switch(n[0]){case"linear":if(1!==n.length)throw new Error("Linear interpolation cannot have parameters");break;case"exponential":if(2!==n.length||"number"!=typeof n[1])throw new Error("Exponential interpolation requires one numeric argument");break;case"cubic-bezier":if(5!==n.length)throw new Error("Cubic bezier interpolation requires four numeric arguments with values between 0 and 1");for(let t=1;t<5;t++){const e=n[t];if("number"!=typeof e||e<0||e>1)throw new Error("Cubic bezier interpolation requires four numeric arguments with values between 0 and 1")}break;default:throw new Error(`"${t[0]}" unknown interpolation type "${n[0]}"`)}if(t.length%2!=1)throw new Error(`"${i}" expects an even number of arguments`);const s=wt(t[2],e,L);let a;"interpolate-hcl"===i||"interpolate-lab"===i?a=S:r&&"value"!==r.kind&&(a=r);const o=[];for(let r=3;r<t.length;r+=2){const n=t[r];if("number"!=typeof n)throw new Error(`"${i}" requires stop inputs as literal numbers`);if(o.length&&o[o.length-1][0]>=n)throw new Error(`"${i}" requires strictly ascending stop inputs`);const s=wt(t[r+1],e,a);a||(a=s.type),o.push([n,s])}if(a&&a!==S&&a!==L&&("array"!==a.kind||a.itemType!==L))throw new Error(`"${i}" cannot interpolate type ${M(a)}`);return new tt(i,a,n,s,o)}evaluate(t,e){const r=this._stops;if(1===r.length)return r[0][1].evaluate(t,e);const i=this.input.evaluate(t,e);if(i<=r[0][0])return r[0][1].evaluate(t,e);if(i>=r[r.length-1][0])return r[r.length-1][1].evaluate(t,e);let n=0;for(;++n<r.length&&!(i<r[n][0]););const s=r[n-1][0],a=r[n][0],o=tt.interpolationRatio(this.interpolation,i,s,a),l=r[n-1][1].evaluate(t,e),u=r[n][1].evaluate(t,e);if("interpolate"===this._operator){if("array"===this.type.kind&&Array.isArray(l)&&Array.isArray(u))return l.map(((t,e)=>(0,N.sX)(t,u[e],o)));if("color"===this.type.kind&&l instanceof T.Z&&u instanceof T.Z){const t=new T.Z(l),e=new T.Z(u);return new T.Z([(0,N.sX)(t.r,e.r,o),(0,N.sX)(t.g,e.g,o),(0,N.sX)(t.b,e.b,o),(0,N.sX)(t.a,e.a,o)])}if("number"===this.type.kind&&"number"==typeof l&&"number"==typeof u)return(0,N.sX)(l,u,o);throw new Error(`"${this._operator}" cannot interpolate type ${M(this.type)}`)}if("interpolate-hcl"===this._operator){const t=(0,w.sJ)(l),e=(0,w.sJ)(u),r=e.h-t.h,i=(0,w.xr)({h:t.h+o*(r>180||r<-180?r-360*Math.round(r/360):r),c:(0,N.sX)(t.c,e.c,o),l:(0,N.sX)(t.l,e.l,o)});return new T.Z({a:(0,N.sX)(l.a,u.a,o),...i})}if("interpolate-lab"===this._operator){const t=(0,w.Y3)(l),e=(0,w.Y3)(u),r=(0,w.xr)({l:(0,N.sX)(t.l,e.l,o),a:(0,N.sX)(t.a,e.a,o),b:(0,N.sX)(t.b,e.b,o)});return new T.Z({a:(0,N.sX)(l.a,u.a,o),...r})}throw new Error(`Unexpected operator "${this._operator}"`)}interpolationUniformValue(t,e){const r=this._stops;if(1===r.length)return 0;if(t>=r[r.length-1][0])return 0;let i=0;for(;++i<r.length&&!(t<r[i][0]););const n=r[i-1][0],s=r[i][0];return tt.interpolationRatio(this.interpolation,e,n,s)}getInterpolationRange(t){const e=this._stops;if(1===e.length){const t=e[0][0];return[t,t]}const r=e[e.length-1][0];if(t>=r)return[r,r];let i=0;for(;++i<e.length&&!(t<e[i][0]););return[e[i-1][0],e[i][0]]}static interpolationRatio(t,e,r,i){let n=0;return"linear"===t[0]?n=tt._exponentialInterpolationRatio(e,1,r,i):"exponential"===t[0]?n=tt._exponentialInterpolationRatio(e,t[1],r,i):"cubic-bezier"===t[0]&&(n=v(t[1],t[2],t[3],t[4])(tt._exponentialInterpolationRatio(e,1,r,i),1e-5)),n<0?n=0:n>1&&(n=1),n}static _exponentialInterpolationRatio(t,e,r,i){const n=i-r;if(0===n)return 0;const s=t-r;return 1===e?s/n:(e**s-1)/(e**n-1)}}class et{constructor(t,e,r){this.type=t,this._input=e,this._stops=r}static parse(t,e){if(t.length<5)throw new Error('"step" expects at least 4 arguments');if(t.length%2!=1)throw new Error('"step" expects an even number of arguments');const r=wt(t[1],e,L);let i;const n=[];n.push([-1/0,wt(t[2],e)]);for(let r=3;r<t.length;r+=2){const s=t[r];if("number"!=typeof s)throw new Error('"step" requires stop inputs as literal numbers');if(n.length&&n[n.length-1][0]>=s)throw new Error('"step" requires strictly ascending stop inputs');const a=wt(t[r+1],e);i||(i=a.type),n.push([s,a])}return new et(i,r,n)}evaluate(t,e){const r=this._stops;if(1===r.length)return r[0][1].evaluate(t,e);const i=this._input.evaluate(t,e);let n=0;for(;++n<r.length&&!(i<r[n][0]););return this._stops[n-1][1].evaluate(t,e)}}class rt{constructor(t,e){this.type=t,this._output=e}static parse(t,e,r){if(t.length<4)throw new Error('"let" expects at least 3 arguments');if(t.length%2==1)throw new Error('"let" expects an odd number of arguments');const i=new z(e);for(let r=1;r<t.length-1;r+=2){const n=t[r];if("string"!=typeof n)throw new Error(`"let" requires a string to define variable names - found ${n}`);i.add(n,wt(t[r+1],e))}const n=wt(t[t.length-1],i,r);return new rt(n.type,n)}evaluate(t,e){return this._output.evaluate(t,e)}}class it{constructor(t,e){this.type=t,this.output=e}static parse(t,e,r){if(2!==t.length||"string"!=typeof t[1])throw new Error('"var" requires just one literal string argument');const i=e.get(t[1]);if(!i)throw new Error(`${t[1]} must be defined before being used in a "var" expression`);return new it(r||U,i)}evaluate(t,e){return this.output.evaluate(t,e)}}class nt{constructor(t,e,r){this.type=t,this._index=e,this._array=r}static parse(t,e){if(3!==t.length)throw new Error('"at" expects 2 arguments');const r=wt(t[1],e,L),i=wt(t[2],e);return new nt(i.type.itemType,r,i)}evaluate(t,e){const r=this._index.evaluate(t,e),i=this._array.evaluate(t,e);if(r<0||r>=i.length)throw new Error('"at" index out of bounds');if(r!==Math.floor(r))throw new Error('"at" index must be an integer');return i[r]}}class st{constructor(t,e){this._key=t,this._obj=e,this.type=U}static parse(t,e){let r,i;switch(t.length){case 2:return r=wt(t[1],e),new st(r);case 3:return r=wt(t[1],e),i=wt(t[2],e),new st(r,i);default:throw new Error('"get" expects 1 or 2 arguments')}}evaluate(t,e){const r=this._key.evaluate(t,e);return this._obj?this._obj.evaluate(t,e)[r]:t?.values[r]}}class at{constructor(t,e){this._key=t,this._obj=e,this.type=P}static parse(t,e){let r,i;switch(t.length){case 2:return r=wt(t[1],e),new at(r);case 3:return r=wt(t[1],e),i=wt(t[2],e),new at(r,i);default:throw new Error('"has" expects 1 or 2 arguments')}}evaluate(t,e){const r=this._key.evaluate(t,e);return this._obj?r in this._obj.evaluate(t,e):!!t?.values[r]}}class ot{constructor(t,e){this._key=t,this._vals=e,this.type=P}static parse(t,e){if(3!==t.length)throw new Error('"in" expects 2 arguments');return new ot(wt(t[1],e),wt(t[2],e))}evaluate(t,e){const r=this._key.evaluate(t,e);return this._vals.evaluate(t,e).includes(r)}}class lt{constructor(t,e,r){this._item=t,this._array=e,this._from=r,this.type=L}static parse(t,e){if(t.length<3||t.length>4)throw new Error('"index-of" expects 3 or 4 arguments');const r=wt(t[1],e),i=wt(t[2],e);if(4===t.length){const n=wt(t[3],e,L);return new lt(r,i,n)}return new lt(r,i)}evaluate(t,e){const r=this._item.evaluate(t,e),i=this._array.evaluate(t,e);if(this._from){const n=this._from.evaluate(t,e);if(n!==Math.floor(n))throw new Error('"index-of" index must be an integer');return i.indexOf(r,n)}return i.indexOf(r)}}class ut{constructor(t){this._arg=t,this.type=L}static parse(t,e){if(2!==t.length)throw new Error('"length" expects 2 arguments');const r=wt(t[1],e);return new ut(r)}evaluate(t,e){const r=this._arg.evaluate(t,e);if("string"==typeof r)return r.length;if(Array.isArray(r))return r.length;throw new Error('"length" expects string or array')}}class ht{constructor(t,e,r,i){this.type=t,this._array=e,this._from=r,this._to=i}static parse(t,e){if(t.length<3||t.length>4)throw new Error('"slice" expects 2 or 3 arguments');const r=wt(t[1],e),i=wt(t[2],e,L);if(i.type!==L)throw new Error('"slice" index must return a number');if(4===t.length){const n=wt(t[3],e,L);if(n.type!==L)throw new Error('"slice" index must return a number');return new ht(r.type,r,i,n)}return new ht(r.type,r,i)}evaluate(t,e){const r=this._array.evaluate(t,e);if(!Array.isArray(r)&&"string"!=typeof r)throw new Error('"slice" input must be an array or a string');const i=this._from.evaluate(t,e);if(i<0||i>=r.length)throw new Error('"slice" index out of bounds');if(i!==Math.floor(i))throw new Error('"slice" index must be an integer');if(this._to){const n=this._to.evaluate(t,e);if(n<0||n>=r.length)throw new Error('"slice" index out of bounds');if(n!==Math.floor(n))throw new Error('"slice" index must be an integer');return r.slice(i,n)}return r.slice(i)}}class ct{constructor(){this.type=P}static parse(t){if(1!==t.length)throw new Error('"has-id" expects no arguments');return new ct}evaluate(t,e){return t&&void 0!==t.id}}class pt{constructor(t,e){this._args=t,this._calculate=e,this.type=L}static parse(t,e,r){const i=t.slice(1).map((t=>wt(t,e)));return new pt(i,r)}evaluate(t,e){let r;return this._args&&(r=this._args.map((r=>r.evaluate(t,e)))),this._calculate(r)}}class yt{constructor(t,e){this._args=t,this._calculate=e,this.type=L}static parse(t,e){const r=t.slice(1).map((t=>wt(t,e)));return new yt(r,yt.ops[t[0]])}evaluate(t,e){let r;return this._args&&(r=this._args.map((r=>r.evaluate(t,e)))),this._calculate(r)}}yt.ops={abs:t=>Math.abs(t[0]),acos:t=>Math.acos(t[0]),asin:t=>Math.asin(t[0]),atan:t=>Math.atan(t[0]),ceil:t=>Math.ceil(t[0]),cos:t=>Math.cos(t[0]),e:()=>Math.E,floor:t=>Math.floor(t[0]),ln:t=>Math.log(t[0]),ln2:()=>Math.LN2,log10:t=>Math.log(t[0])/Math.LN10,log2:t=>Math.log(t[0])/Math.LN2,max:t=>Math.max(...t),min:t=>Math.min(...t),pi:()=>Math.PI,round:t=>Math.round(t[0]),sin:t=>Math.sin(t[0]),sqrt:t=>Math.sqrt(t[0]),tan:t=>Math.tan(t[0])};class ft{constructor(t){this._args=t,this.type=A}static parse(t,e){return new ft(t.slice(1).map((t=>wt(t,e))))}evaluate(t,e){return this._args.map((r=>r.evaluate(t,e))).join("")}}class _t{constructor(t,e){this._arg=t,this._calculate=e,this.type=A}static parse(t,e){if(2!==t.length)throw new Error(`${t[0]} expects 1 argument`);const r=wt(t[1],e);return new _t(r,_t.ops[t[0]])}evaluate(t,e){return this._calculate(this._arg.evaluate(t,e))}}_t.ops={downcase:t=>t.toLowerCase(),upcase:t=>t.toUpperCase()};class mt{constructor(t){this._args=t,this.type=S}static parse(t,e){if(4!==t.length)throw new Error('"rgb" expects 3 arguments');const r=t.slice(1).map((t=>wt(t,e)));return new mt(r)}evaluate(t,e){const r=this._validate(this._args[0].evaluate(t,e)),i=this._validate(this._args[1].evaluate(t,e)),n=this._validate(this._args[2].evaluate(t,e));return new T.Z({r,g:i,b:n})}_validate(t){if("number"!=typeof t||t<0||t>255)throw new Error(`${t}: invalid color component`);return Math.round(t)}}class gt{constructor(t){this._args=t,this.type=S}static parse(t,e){if(5!==t.length)throw new Error('"rgba" expects 4 arguments');const r=t.slice(1).map((t=>wt(t,e)));return new gt(r)}evaluate(t,e){const r=this._validate(this._args[0].evaluate(t,e)),i=this._validate(this._args[1].evaluate(t,e)),n=this._validate(this._args[2].evaluate(t,e)),s=this._validateAlpha(this._args[3].evaluate(t,e));return new T.Z({r,g:i,b:n,a:s})}_validate(t){if("number"!=typeof t||t<0||t>255)throw new Error(`${t}: invalid color component`);return Math.round(t)}_validateAlpha(t){if("number"!=typeof t||t<0||t>1)throw new Error(`${t}: invalid alpha color component`);return t}}class dt{constructor(t){this._color=t,this.type=B(L,4)}static parse(t,e){if(2!==t.length)throw new Error('"to-rgba" expects 1 argument');const r=wt(t[1],e);return new dt(r)}evaluate(t,e){return new T.Z(this._color.evaluate(t,e)).toRgba()}}class xt{constructor(t,e){this.type=t,this._args=e}static parse(t,e){const r=t[0];if(t.length<2)throw new Error(`${r} expects at least one argument`);let i,n=1;if("array"===r){if(t.length>2){switch(t[1]){case"string":i=A;break;case"number":i=L;break;case"boolean":i=P;break;default:throw new Error('"array" type argument must be string, number or boolean')}n++}else i=U;let e;if(t.length>3){if(e=t[2],null!==e&&("number"!=typeof e||e<0||e!==Math.floor(e)))throw new Error('"array" length argument must be a positive integer literal');n++}i=B(i,e)}else switch(r){case"string":i=A;break;case"number":i=L;break;case"boolean":i=P;break;case"object":i=O}const s=[];for(;n<t.length;n++){const r=wt(t[n],e);s.push(r)}return new xt(i,s)}evaluate(t,e){let r;for(const i of this._args){const n=i.evaluate(t,e);if(r=k(n),C(r,this.type))return n}throw new Error(`Expected ${M(this.type)} but got ${M(r)}`)}}class bt{constructor(t,e){this.type=t,this._args=e}static parse(t,e){const r=t[0],i=bt.types[r];if(i===P||i===A){if(2!==t.length)throw new Error(`${r} expects one argument`)}else if(t.length<2)throw new Error(`${r} expects at least one argument`);const n=[];for(let r=1;r<t.length;r++){const i=wt(t[r],e);n.push(i)}return new bt(i,n)}evaluate(t,e){if(this.type===P)return Boolean(this._args[0].evaluate(t,e));if(this.type===A)return F(this._args[0].evaluate(t,e));if(this.type===L){for(const r of this._args){const i=Number(r.evaluate(t,e));if(!isNaN(i))return i}return null}if(this.type===S){for(const r of this._args)try{const i=bt.toColor(r.evaluate(t,e));if(i instanceof T.Z)return i}catch{}return null}}static toBoolean(t){return Boolean(t)}static toString(t){return F(t)}static toNumber(t){const e=Number(t);if(isNaN(e))throw new Error(`"${t}" is not a number`);return e}static toColor(t){if(t instanceof T.Z)return t;if("string"==typeof t){const e=T.Z.fromString(t);if(e)return e;throw new Error(`"${t}" is not a color`)}if(Array.isArray(t))return T.Z.fromArray(t);throw new Error(`"${t}" is not a color`)}}bt.types={"to-boolean":P,"to-color":S,"to-number":L,"to-string":A};class Et{constructor(t){this._val=t,this.type=k(t)}static parse(t){if(2!==t.length)throw new Error('"literal" expects 1 argument');return new Et(t[1])}evaluate(t,e){return this._val}}class Tt{constructor(t){this._arg=t,this.type=A}static parse(t,e){if(2!==t.length)throw new Error('"typeof" expects 1 argument');return new Tt(wt(t[1],e))}evaluate(t,e){return M(k(this._arg.evaluate(t,e)))}}function wt(t,e,r){const i=typeof t;if("string"===i||"boolean"===i||"number"===i||null===t){if(r)switch(r.kind){case"string":"string"!==i&&(t=bt.toString(t));break;case"number":"number"!==i&&(t=bt.toNumber(t));break;case"color":t=bt.toColor(t)}t=["literal",t]}if(!Array.isArray(t)||0===t.length)throw new Error("Expression must be a non empty array");const n=t[0];if("string"!=typeof n)throw new Error("First element of expression must be a string");const s=It[n];if(void 0===s)throw new Error(`Invalid expression operator "${n}"`);if(!s)throw new Error(`Unimplemented expression operator "${n}"`);return s.parse(t,e,r)}const It={array:xt,boolean:xt,collator:null,format:null,image:null,literal:Et,number:xt,"number-format":null,object:xt,string:xt,"to-boolean":bt,"to-color":bt,"to-number":bt,"to-string":bt,typeof:Tt,accumulated:null,"feature-state":null,"geometry-type":$,id:V,"line-progress":null,properties:Y,at:nt,get:st,has:at,in:ot,"index-of":lt,length:ut,slice:ht,"!":j,"!=":class extends Z{static parse(t,e){return Z.parse(t,e,((t,e)=>t!==e))}},"<":class extends Z{static parse(t,e){return Z.parse(t,e,((t,e)=>t<e))}},"<=":class extends Z{static parse(t,e){return Z.parse(t,e,((t,e)=>t<=e))}},"==":class extends Z{static parse(t,e){return Z.parse(t,e,((t,e)=>t===e))}},">":class extends Z{static parse(t,e){return Z.parse(t,e,((t,e)=>t>e))}},">=":class extends Z{static parse(t,e){return Z.parse(t,e,((t,e)=>t>=e))}},all:K,any:q,case:X,coalesce:J,match:Q,within:null,interpolate:tt,"interpolate-hcl":tt,"interpolate-lab":tt,step:et,let:rt,var:it,concat:ft,downcase:_t,"is-supported-script":null,"resolved-locale":null,upcase:_t,rgb:mt,rgba:gt,"to-rgba":dt,"-":class extends pt{static parse(t,e){switch(t.length){case 2:return pt.parse(t,e,(t=>-t[0]));case 3:return pt.parse(t,e,(t=>t[0]-t[1]));default:throw new Error('"-" expects 1 or 2 arguments')}}},"*":class extends pt{static parse(t,e){return pt.parse(t,e,(t=>{let e=1;for(const r of t)e*=r;return e}))}},"/":class extends pt{static parse(t,e){if(3===t.length)return pt.parse(t,e,(t=>t[0]/t[1]));throw new Error('"/" expects 2 arguments')}},"%":class extends pt{static parse(t,e){if(3===t.length)return pt.parse(t,e,(t=>t[0]%t[1]));throw new Error('"%" expects 2 arguments')}},"^":class extends pt{static parse(t,e){if(3===t.length)return pt.parse(t,e,(t=>t[0]**t[1]));throw new Error('"^" expects 1 or 2 arguments')}},"+":class extends pt{static parse(t,e){return pt.parse(t,e,(t=>{let e=0;for(const r of t)e+=r;return e}))}},abs:yt,acos:yt,asin:yt,atan:yt,ceil:yt,cos:yt,e:yt,floor:yt,ln:yt,ln2:yt,log10:yt,log2:yt,max:yt,min:yt,pi:yt,round:yt,sin:yt,sqrt:yt,tan:yt,zoom:H,"heatmap-density":null,"has-id":ct,none:W};class vt{constructor(t){this._expression=t}filter(t,e){if(!this._expression)return!0;try{return this._expression.evaluate(t,e)}catch(t){return console.log(t.message),!0}}static createFilter(t){if(!t)return null;this.isLegacyFilter(t)&&(t=this.convertLegacyFilter(t));try{const e=wt(t,null,P);return new vt(e)}catch(t){return console.log(t.message),null}}static isLegacyFilter(t){if(!Array.isArray(t))return!0;if(0===t.length)return!0;switch(t[0]){case"==":case"!=":case">":case"<":case">=":case"<=":return 3===t.length&&"string"==typeof t[1]&&!Array.isArray(t[2]);case"in":return t.length>=3&&"string"==typeof t[1]&&!Array.isArray(t[2]);case"!in":case"none":case"!has":return!0;case"any":case"all":for(let e=1;e<t.length;e++)if(this.isLegacyFilter(t[e]))return!0;return!1;case"has":return 2===t.length&&("$id"===t[1]||"$type"===t[1]);default:return!1}}static convertLegacyFilter(t){if(!Array.isArray(t)||0===t.length)return!0;const e=t[0];if(1===t.length)return"any"!==e;switch(e){case"==":return vt.convertComparison("==",t[1],t[2]);case"!=":return vt.negate(vt.convertComparison("==",t[1],t[2]));case">":case"<":case">=":case"<=":return vt.convertComparison(e,t[1],t[2]);case"in":return vt.convertIn(t[1],t.slice(2));case"!in":return vt.negate(vt.convertIn(t[1],t.slice(2)));case"any":case"all":case"none":return vt.convertCombining(e,t.slice(1));case"has":return vt.convertHas(t[1]);case"!has":return vt.negate(vt.convertHas(t[1]));default:throw new Error("Unexpected legacy filter.")}}static convertComparison(t,e,r){switch(e){case"$type":return[t,["geometry-type"],r];case"$id":return[t,["id"],r];default:return[t,["get",e],r]}}static convertIn(t,e){switch(t){case"$type":return["in",["geometry-type"],["literal",e]];case"$id":return["in",["id"],["literal",e]];default:return["in",["get",t],["literal",e]]}}static convertHas(t){switch(t){case"$type":return!0;case"$id":return["has-id"];default:return["has",t]}}static convertCombining(t,e){return[t].concat(e.map(this.convertLegacyFilter))}static negate(t){return["!",t]}}var Rt=r(21315);class Nt{constructor(t,e){let r;switch(this.isDataDriven=!1,this.interpolator=null,t.type){case"number":case"color":r=!0;break;case"array":r="number"===t.value;break;default:r=!1}if(null==e&&(e=t.default),Array.isArray(e)&&e.length>0&&It[e[0]]){const r={number:L,color:S,string:A,boolean:P,enum:A};try{const i=wt(e,null,"array"===t.type?B(r[t.value]||U,t.length):r[t.type]);this.getValue=this._buildExpression(i,t),this.isDataDriven=!0,i instanceof tt&&i.input instanceof H&&(this.interpolator=i)}catch(e){console.log(e.message),this.getValue=this._buildSimple(t.default)}return}r&&"interval"===e.type&&(r=!1);const i=e&&e.stops&&e.stops.length>0;if(i)for(const r of e.stops)r[1]=this._validate(r[1],t);if(this.isDataDriven=!!e&&!!e.property,this.isDataDriven)if(void 0!==e.default&&(e.default=this._validate(e.default,t)),i)switch(e.type){case"identity":this.getValue=this._buildIdentity(e,t);break;case"categorical":this.getValue=this._buildCategorical(e,t);break;default:this.getValue=r?this._buildInterpolate(e,t):this._buildInterval(e,t)}else this.getValue=this._buildIdentity(e,t);else i?this.getValue=r?this._buildZoomInterpolate(e):this._buildZoomInterval(e):(e=this._validate(e,t),this.getValue=this._buildSimple(e))}_validate(t,e){if("number"===e.type){if(t<e.minimum)return e.minimum;if(t>e.maximum)return e.maximum}else"color"===e.type?t=Nt._parseColor(t):"enum"===e.type?"string"==typeof t&&(t=e.values.indexOf(t)):"array"===e.type&&"enum"===e.value?t=t.map((t=>"string"==typeof t?e.values.indexOf(t):t)):"string"===e.type&&(t=F(t));return t}_buildSimple(t){return()=>t}_buildExpression(t,e){return(r,i)=>{try{const n=t.evaluate(i,r);return void 0===n?e.default:this._validate(n,e)}catch(t){return console.log(t.message),e.default}}}_buildIdentity(t,e){return(r,i)=>{let n;return i&&(n=i.values[t.property]),void 0!==n&&(n=this._validate(n,e)),null!=n?n:void 0!==t.default?t.default:e.default}}_buildCategorical(t,e){return(r,i)=>{let n;return i&&(n=i.values[t.property]),n=this._categorical(n,t.stops),void 0!==n?n:void 0!==t.default?t.default:e.default}}_buildInterval(t,e){return(r,i)=>{let n;return i&&(n=i.values[t.property]),"number"==typeof n?this._interval(n,t.stops):void 0!==t.default?t.default:e.default}}_buildInterpolate(t,e){return(r,i)=>{let n;return i&&(n=i.values[t.property]),"number"==typeof n?this._interpolate(n,t.stops,t.base||1):void 0!==t.default?t.default:e.default}}_buildZoomInterpolate(t){return e=>this._interpolate(e,t.stops,t.base||1)}_buildZoomInterval(t){return e=>this._interval(e,t.stops)}_categorical(t,e){const r=e.length;for(let i=0;i<r;i++)if(e[i][0]===t)return e[i][1]}_interval(t,e){const r=e.length;let i=0;for(let n=0;n<r&&e[n][0]<=t;n++)i=n;return e[i][1]}_interpolate(t,e,r){let i,n;const s=e.length;for(let r=0;r<s;r++){const s=e[r];if(!(s[0]<=t)){n=s;break}i=s}if(i&&n){const e=n[0]-i[0],s=t-i[0],a=1===r?s/e:(r**s-1)/(r**e-1);if(Array.isArray(i[1])){const t=i[1],e=n[1],r=[];for(let i=0;i<t.length;i++)r.push((0,N.sX)(t[i],e[i],a));return r}return(0,N.sX)(i[1],n[1],a)}return i?i[1]:n?n[1]:void 0}static _isEmpty(t){for(const e in t)if(t.hasOwnProperty(e))return!1;return!0}static _parseColor(t){return Array.isArray(t)?t:("string"==typeof t&&(t=new T.Z(t)),t instanceof T.Z&&!this._isEmpty(t)?T.Z.toUnitRGBA(t):void 0)}}var Dt,Lt=r(16534);!function(t){t[t.BUTT=0]="BUTT",t[t.ROUND=1]="ROUND",t[t.SQUARE=2]="SQUARE",t[t.UNKNOWN=4]="UNKNOWN"}(Dt||(Dt={}));class At{constructor(t,e,r,i){switch(this.type=t,this.typeName=e.type,this.id=e.id,this.source=e.source,this.sourceLayer=e["source-layer"],this.minzoom=e.minzoom,this.maxzoom=e.maxzoom,this.filter=e.filter,this.layout=e.layout,this.paint=e.paint,this.z=r,this.uid=i,t){case Rt.fR.BACKGROUND:this._layoutDefinition=Rt.f2.backgroundLayoutDefinition,this._paintDefinition=Rt.f2.backgroundPaintDefinition;break;case Rt.fR.FILL:this._layoutDefinition=Rt.f2.fillLayoutDefinition,this._paintDefinition=Rt.f2.fillPaintDefinition;break;case Rt.fR.LINE:this._layoutDefinition=Rt.f2.lineLayoutDefinition,this._paintDefinition=Rt.f2.linePaintDefinition;break;case Rt.fR.SYMBOL:this._layoutDefinition=Rt.f2.symbolLayoutDefinition,this._paintDefinition=Rt.f2.symbolPaintDefinition;break;case Rt.fR.CIRCLE:this._layoutDefinition=Rt.f2.circleLayoutDefinition,this._paintDefinition=Rt.f2.circlePaintDefinition}this._layoutProperties=this._parseLayout(this.layout),this._paintProperties=this._parsePaint(this.paint)}getFeatureFilter(){return void 0!==this._featureFilter?this._featureFilter:this._featureFilter=vt.createFilter(this.filter)}getLayoutProperty(t){return this._layoutProperties[t]}getPaintProperty(t){return this._paintProperties[t]}getLayoutValue(t,e,r){let i;const n=this._layoutProperties[t];return n&&(i=n.getValue(e,r)),void 0===i&&(i=this._layoutDefinition[t].default),i}getPaintValue(t,e,r){let i;const n=this._paintProperties[t];return n&&(i=n.getValue(e,r)),void 0===i&&(i=this._paintDefinition[t].default),i}isPainterDataDriven(){const t=this._paintProperties;if(t)for(const e in t)if(t[e].isDataDriven)return!0;return!1}_parseLayout(t){const e={};for(const r in t){const i=this._layoutDefinition[r];i&&(e[r]=new Nt(i,t[r]))}return e}_parsePaint(t){const e={};for(const r in t){const i=this._paintDefinition[r];i&&(e[r]=new Nt(i,t[r]))}return e}computeAttributesKey(t,e,r,i){let n=0,s=0;for(const t of e){let e=3;if(!t||t!==i){const i=r[t],{isLayout:n,isOptional:s}=i,a=n?this.getLayoutProperty(t):this.getPaintProperty(t);e=a?.interpolator?2:a?.isDataDriven?1:s&&!a?3:0}s|=e<<n,n+=2}return s<<3|t}}class Pt extends At{constructor(t,e,r,i){super(t,e,r,i),this.backgroundMaterial=new y(this.computeAttributesKey(s._K.BACKGROUND,y.ATTRIBUTES,y.ATTRIBUTES_INFO))}}class St extends At{constructor(t,e,r,i){super(t,e,r,i);const n=this.getPaintProperty("fill-color"),a=this.getPaintProperty("fill-opacity"),o=this.getPaintProperty("fill-pattern");this.hasDataDrivenColor=n?.isDataDriven,this.hasDataDrivenOpacity=a?.isDataDriven,this.hasDataDrivenFill=this.hasDataDrivenColor||this.hasDataDrivenOpacity||o?.isDataDriven;const l=this.getPaintProperty("fill-outline-color");this.outlineUsesFillColor=!l,this.hasDataDrivenOutlineColor=l?.isDataDriven,this.hasDataDrivenOutline=l?l.isDataDriven:!!n&&n.isDataDriven,this.hasDataDrivenOutline=(l?this.hasDataDrivenOutlineColor:this.hasDataDrivenColor)||this.hasDataDrivenOpacity,this.fillMaterial=new _(this.computeAttributesKey(s._K.FILL,_.ATTRIBUTES,_.ATTRIBUTES_INFO)),this.outlineMaterial=new m(this.computeAttributesKey(s._K.OUTLINE,this.outlineUsesFillColor?m.ATTRIBUTES_FILL:m.ATTRIBUTES_OUTLINE,this.outlineUsesFillColor?m.ATTRIBUTES_INFO_FILL:m.ATTRIBUTES_INFO_OUTLINE),this.outlineUsesFillColor)}}class Ot extends At{constructor(t,e,r,i){super(t,e,r,i);const n=this.getPaintProperty("line-pattern");if(this.lineMaterial=new g(this.computeAttributesKey(s._K.LINE,g.ATTRIBUTES,g.ATTRIBUTES_INFO,n?"line-dasharray":"")),this.hasDataDrivenLine=this.getPaintProperty("line-blur")?.isDataDriven||this.getPaintProperty("line-color")?.isDataDriven||this.getPaintProperty("line-gap-width")?.isDataDriven||this.getPaintProperty("line-offset")?.isDataDriven||this.getPaintProperty("line-opacity")?.isDataDriven||this.getPaintProperty("line-pattern")?.isDataDriven||this.getPaintProperty("line-dasharray")?.isDataDriven||this.getLayoutProperty("line-cap")?.isDataDriven||this.getPaintProperty("line-width")?.isDataDriven,this.canUseThinTessellation=!1,!this.hasDataDrivenLine){const t=this.getPaintProperty("line-width");if(!t||"number"==typeof t&&.5*t<Lt.tQ){const t=this.getPaintProperty("line-offset");(!t||"number"==typeof t&&0===t)&&(this.canUseThinTessellation=!0)}}}getDashKey(t,e){let r;switch(e){case Dt.BUTT:r="Butt";break;case Dt.ROUND:r="Round";break;case Dt.SQUARE:r="Square";break;default:r="Butt"}return`dasharray-[${t.toString()}]-${r}`}}class Ut extends At{constructor(t,e,r,i){super(t,e,r,i),this.iconMaterial=new b(this.computeAttributesKey(s._K.ICON,b.ATTRIBUTES,b.ATTRIBUTES_INFO)),this.textMaterial=new E(this.computeAttributesKey(s._K.TEXT,E.ATTRIBUTES,E.ATTRIBUTES_INFO)),this.hasDataDrivenIcon=this.getPaintProperty("icon-color")?.isDataDriven||this.getPaintProperty("icon-halo-blur")?.isDataDriven||this.getPaintProperty("icon-halo-color")?.isDataDriven||this.getPaintProperty("icon-halo-width")?.isDataDriven||this.getPaintProperty("icon-opacity")?.isDataDriven||this.getLayoutProperty("icon-size")?.isDataDriven,this.hasDataDrivenText=this.getPaintProperty("text-color")?.isDataDriven||this.getPaintProperty("text-halo-blur")?.isDataDriven||this.getPaintProperty("text-halo-color")?.isDataDriven||this.getPaintProperty("text-halo-width")?.isDataDriven||this.getPaintProperty("text-opacity")?.isDataDriven||this.getLayoutProperty("text-size")?.isDataDriven}}class Bt extends At{constructor(t,e,r,i){super(t,e,r,i),this.circleMaterial=new f(this.computeAttributesKey(s._K.CIRCLE,f.ATTRIBUTES,f.ATTRIBUTES_INFO))}}class Gt{constructor(t,e,r){let i;this.allowOverlap=t.getLayoutValue("icon-allow-overlap",e),this.ignorePlacement=t.getLayoutValue("icon-ignore-placement",e),this.keepUpright=t.getLayoutValue("icon-keep-upright",e),this.optional=t.getLayoutValue("icon-optional",e),this.rotationAlignment=t.getLayoutValue("icon-rotation-alignment",e),this.rotationAlignment===Rt.aF.AUTO&&(this.rotationAlignment=r?Rt.aF.MAP:Rt.aF.VIEWPORT),i=t.getLayoutProperty("icon-anchor"),i?.isDataDriven?this._anchorProp=i:this.anchor=t.getLayoutValue("icon-anchor",e),i=t.getLayoutProperty("icon-offset"),i?.isDataDriven?this._offsetProp=i:this.offset=t.getLayoutValue("icon-offset",e),i=t.getLayoutProperty("icon-padding"),i?.isDataDriven?this._paddingProp=i:this.padding=t.getLayoutValue("icon-padding",e),i=t.getLayoutProperty("icon-rotate"),i?.isDataDriven?this._rotateProp=i:this.rotate=t.getLayoutValue("icon-rotate",e),i=t.getLayoutProperty("icon-size"),i?.isDataDriven?this._sizeProp=i:this.size=t.getLayoutValue("icon-size",e)}update(t,e){this._anchorProp&&(this.anchor=this._anchorProp.getValue(t,e)),this._offsetProp&&(this.offset=this._offsetProp.getValue(t,e)),this._paddingProp&&(this.padding=this._paddingProp.getValue(t,e)),this._rotateProp&&(this.rotate=this._rotateProp.getValue(t,e)),this._sizeProp&&(this.size=this._sizeProp.getValue(t,e))}}class Mt{constructor(t,e,r){let i;this.allowOverlap=t.getLayoutValue("text-allow-overlap",e),this.ignorePlacement=t.getLayoutValue("text-ignore-placement",e),this.keepUpright=t.getLayoutValue("text-keep-upright",e),this.optional=t.getLayoutValue("text-optional",e),this.rotationAlignment=t.getLayoutValue("text-rotation-alignment",e),this.rotationAlignment===Rt.aF.AUTO&&(this.rotationAlignment=r?Rt.aF.MAP:Rt.aF.VIEWPORT),i=t.getLayoutProperty("text-anchor"),i?.isDataDriven?this._anchorProp=i:this.anchor=t.getLayoutValue("text-anchor",e),i=t.getLayoutProperty("text-justify"),i?.isDataDriven?this._justifyProp=i:this.justify=t.getLayoutValue("text-justify",e),i=t.getLayoutProperty("text-letter-spacing"),i?.isDataDriven?this._letterSpacingProp=i:this.letterSpacing=t.getLayoutValue("text-letter-spacing",e),i=t.getLayoutProperty("text-line-height"),i?.isDataDriven?this._lineHeightProp=i:this.lineHeight=t.getLayoutValue("text-line-height",e),i=t.getLayoutProperty("text-max-angle"),i?.isDataDriven?this._maxAngleProp=i:this.maxAngle=t.getLayoutValue("text-max-angle",e),i=t.getLayoutProperty("text-max-width"),i?.isDataDriven?this._maxWidthProp=i:this.maxWidth=t.getLayoutValue("text-max-width",e),i=t.getLayoutProperty("text-offset"),i?.isDataDriven?this._offsetProp=i:this.offset=t.getLayoutValue("text-offset",e),i=t.getLayoutProperty("text-padding"),i?.isDataDriven?this._paddingProp=i:this.padding=t.getLayoutValue("text-padding",e),i=t.getLayoutProperty("text-rotate"),i?.isDataDriven?this._rotateProp=i:this.rotate=t.getLayoutValue("text-rotate",e),i=t.getLayoutProperty("text-size"),i?.isDataDriven?this._sizeProp=i:this.size=t.getLayoutValue("text-size",e),i=t.getLayoutProperty("text-writing-mode"),i?.isDataDriven?this._writingModeProp=i:this.writingMode=t.getLayoutValue("text-writing-mode",e)}update(t,e){this._anchorProp&&(this.anchor=this._anchorProp.getValue(t,e)),this._justifyProp&&(this.justify=this._justifyProp.getValue(t,e)),this._letterSpacingProp&&(this.letterSpacing=this._letterSpacingProp.getValue(t,e)),this._lineHeightProp&&(this.lineHeight=this._lineHeightProp.getValue(t,e)),this._maxAngleProp&&(this.maxAngle=this._maxAngleProp.getValue(t,e)),this._maxWidthProp&&(this.maxWidth=this._maxWidthProp.getValue(t,e)),this._offsetProp&&(this.offset=this._offsetProp.getValue(t,e)),this._paddingProp&&(this.padding=this._paddingProp.getValue(t,e)),this._rotateProp&&(this.rotate=this._rotateProp.getValue(t,e)),this._sizeProp&&(this.size=this._sizeProp.getValue(t,e)),this._writingModeProp&&(this.writingMode=this._writingModeProp.getValue(t,e))}}},51785:(t,e,r)=>{r.d(e,{Z:()=>s});var i=r(21315),n=r(7333);class s{constructor(t){if(this._style=t,this.backgroundBucketIds=[],this._uidToLayer=new Map,this._layerByName={},this._runningId=0,t.layers||(t.layers=[]),this.version=parseFloat(t.version),this.layers=t.layers.map(((t,e,r)=>this._create(t,e,r))).filter((t=>!!t)),this.layers){let t;for(let e=0;e<this.layers.length;e++)t=this.layers[e],this._layerByName[t.id]=t,this._uidToLayer.set(t.uid,t),t.type===i.fR.BACKGROUND&&this.backgroundBucketIds.push(t.id)}this._identifyRefLayers()}isPainterDataDriven(t){const e=this._layerByName[t];return!!e&&e.isPainterDataDriven()}getStyleLayerId(t){return t>=this.layers.length?null:this.layers[t].id}getStyleLayerByUID(t){return this._uidToLayer.get(t)??null}getStyleLayerIndex(t){const e=this._layerByName[t];return e?this.layers.indexOf(e):-1}setStyleLayer(t,e){if(!t||!t.id)return;const r=this._style;null!=e&&e>=this.layers.length&&(e=this.layers.length-1);let i,n=!0;const a=this._layerByName[t.id];if(a){const o=this.layers.indexOf(a);e||(e=o),e===o?(n=!1,i=s._recreateLayer(t,a),this.layers[e]=i,r.layers[e]=t):(this.layers.splice(o,1),r.layers.splice(o,1),i=this._create(t,e,this.layers),this.layers.splice(e,0,i),r.layers.splice(e,0,t))}else i=this._create(t,e,this.layers),!e||e>=this.layers.length?(this.layers.push(i),r.layers.push(t)):(this.layers.splice(e,0,i),r.layers.splice(e,0,t));this._layerByName[t.id]=i,this._uidToLayer.set(i.uid,i),n&&this._recomputeZValues(),this._identifyRefLayers()}getStyleLayer(t){const e=this._layerByName[t];return e?{type:e.typeName,id:e.id,source:e.source,"source-layer":e.sourceLayer,minzoom:e.minzoom,maxzoom:e.maxzoom,filter:e.filter,layout:e.layout,paint:e.paint}:null}deleteStyleLayer(t){const e=this._layerByName[t];if(e){delete this._layerByName[t],this._uidToLayer.delete(e.uid);const r=this.layers.indexOf(e);this.layers.splice(r,1),this._style.layers.splice(r,1),this._recomputeZValues(),this._identifyRefLayers()}}getLayerById(t){return this._layerByName[t]}getLayoutProperties(t){const e=this._layerByName[t];return e?e.layout:null}getPaintProperties(t){const e=this._layerByName[t];return e?e.paint:null}setPaintProperties(t,e){const r=this._layerByName[t];if(!r)return;const i={type:r.typeName,id:r.id,source:r.source,"source-layer":r.sourceLayer,minzoom:r.minzoom,maxzoom:r.maxzoom,filter:r.filter,layout:r.layout,paint:e},n=s._recreateLayer(i,r),a=this.layers.indexOf(r);this.layers[a]=n,this._style.layers[a].paint=e,this._layerByName[r.id]=n,this._uidToLayer.set(r.uid,n)}setLayoutProperties(t,e){const r=this._layerByName[t];if(!r)return;const i={type:r.typeName,id:r.id,source:r.source,"source-layer":r.sourceLayer,minzoom:r.minzoom,maxzoom:r.maxzoom,filter:r.filter,layout:e,paint:r.paint},n=s._recreateLayer(i,r),a=this.layers.indexOf(r);this.layers[a]=n,this._style.layers[a].layout=e,this._layerByName[r.id]=n,this._uidToLayer.set(r.uid,n)}setStyleLayerVisibility(t,e){const r=this._layerByName[t];if(!r)return;const i=r.layout||{};i.visibility=e;const n={type:r.typeName,id:r.id,source:r.source,"source-layer":r.sourceLayer,minzoom:r.minzoom,maxzoom:r.maxzoom,filter:r.filter,layout:i,paint:r.paint},a=s._recreateLayer(n,r),o=this.layers.indexOf(r);this.layers[o]=a,this._style.layers[o].layout=i,this._layerByName[r.id]=a,this._uidToLayer.set(r.uid,a)}getStyleLayerVisibility(t){const e=this._layerByName[t];return e?e.layout?.visibility??"visible":"none"}_recomputeZValues(){const t=this.layers,e=1/(t.length+1);for(let r=0;r<t.length;r++)t[r].z=1-(1+r)*e}_identifyRefLayers(){const t=[],e=[];let r=0;for(const n of this.layers){const s=n.layout;if(n.type===i.fR.FILL){const e=n;let i=n.source+"|"+n.sourceLayer;i+="|"+(s?.visibility??""),i+="|"+n.minzoom,i+="|"+n.maxzoom,i+="|"+JSON.stringify(n.filter),(e.hasDataDrivenFill||e.hasDataDrivenOutline)&&(i+="|"+r),t.push({key:i,layer:n})}else if(n.type===i.fR.LINE){const t=n,i=n.paint,a=null!=i&&(null!=i["line-pattern"]||null!=i["line-dasharray"]);let o=n.source+"|"+n.sourceLayer;o+="|"+(s?.visibility??""),o+="|"+n.minzoom,o+="|"+n.maxzoom,o+="|"+JSON.stringify(n.filter),o+="|"+(void 0!==s?s["line-cap"]:""),o+="|"+(void 0!==s?s["line-join"]:""),(t.hasDataDrivenLine||a)&&(o+="|"+r),e.push({key:o,layer:n})}++r}this._assignRefLayers(t),this._assignRefLayers(e)}_assignRefLayers(t){let e,r;t.sort(((t,e)=>t.key<e.key?-1:t.key>e.key?1:0));const n=t.length;for(let s=0;s<n;s++){const a=t[s];if(a.key===e)a.layer.refLayerId=r;else if(e=a.key,r=a.layer.id,a.layer.type===i.fR.FILL){if(!a.layer.getPaintProperty("fill-outline-color"))for(let i=s+1;i<n;i++){const n=t[i];if(n.key!==e)break;if(n.layer.getPaintProperty("fill-outline-color")){t[s]=n,t[i]=a,r=n.layer.id;break}}}else if(a.layer.type===i.fR.LINE){let i=a.layer;for(let o=s+1;o<n;o++){const n=t[o];if(n.key!==e)break;const l=n.layer;(i.canUseThinTessellation&&!l.canUseThinTessellation||!i.canUseThinTessellation&&(l.getPaintProperty("line-pattern")||l.getPaintProperty("line-dasharray")))&&(i=l,t[s]=n,t[o]=a,r=n.layer.id)}}}}_create(t,e,r){const s=1-(1+e)*(1/(r.length+1)),a=this._runningId++;switch(t.type){case"background":return new n.Et(i.fR.BACKGROUND,t,s,a);case"fill":return new n.Le(i.fR.FILL,t,s,a);case"line":return new n.gf(i.fR.LINE,t,s,a);case"symbol":return new n.jG(i.fR.SYMBOL,t,s,a);case"raster":return console.warn(`Unsupported vector tile raster layer ${t.id}`),null;case"circle":return new n.sj(i.fR.CIRCLE,t,s,a)}return null}static _recreateLayer(t,e){switch(t.type){case"background":return new n.Et(i.fR.BACKGROUND,t,e.z,e.uid);case"fill":return new n.Le(i.fR.FILL,t,e.z,e.uid);case"line":return new n.gf(i.fR.LINE,t,e.z,e.uid);case"symbol":return new n.jG(i.fR.SYMBOL,t,e.z,e.uid);case"raster":return console.warn(`Unsupported vector tile raster layer ${t.id}`),null;case"circle":return new n.sj(i.fR.CIRCLE,t,e.z,e.uid)}return null}}},21968:(t,e,r)=>{r.d(e,{G:()=>i});class i{constructor(t,e,r,i,n,s=!1,a=0){this.name=t,this.count=e,this.type=r,this.offset=i,this.stride=n,this.normalized=s,this.divisor=a}}}}]);