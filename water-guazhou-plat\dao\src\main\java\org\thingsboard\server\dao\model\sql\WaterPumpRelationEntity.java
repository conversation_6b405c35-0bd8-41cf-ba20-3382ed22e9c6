package org.thingsboard.server.dao.model.sql;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;
import java.util.Date;

@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.WATER_PUMP_RELATION_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class WaterPumpRelationEntity {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.WATER_PUMP_RELATION_WATER_PUMP_ID)
    private String waterPumpId;

    @Column(name = ModelConstants.WATER_PUMP_RELATION_DEVICE_ID)
    private String deviceId;

    @Column(name = ModelConstants.WATER_PUMP_RELATION_ATTR)
    private String attr;

    @Column(name = ModelConstants.TYPE)
    private String type;

    @Column(name = ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;


}
