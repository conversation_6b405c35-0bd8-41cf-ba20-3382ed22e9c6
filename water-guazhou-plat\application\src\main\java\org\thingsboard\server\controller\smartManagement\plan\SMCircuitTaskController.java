package org.thingsboard.server.controller.smartManagement.plan;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.sql.department.Organization;
import org.thingsboard.server.dao.model.sql.smartManagement.plan.*;
import org.thingsboard.server.dao.model.sql.statistic.GeneralTaskStatusStatistic;
import org.thingsboard.server.dao.model.sql.statistic.StatisticItem;
import org.thingsboard.server.dao.plan.SMCircuitTaskService;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.plan.*;
import org.thingsboard.server.dao.util.reflection.ExceptionUtils;
import org.thingsboard.server.utils.imodel.annotations.IStarController;

import java.util.List;

import static org.thingsboard.server.dao.model.sql.smartProduction.circuit.GeneralTaskStatus.APPROVED;

@IStarController
@RequestMapping("/api/sm/circuitTask")
public class SMCircuitTaskController extends BaseController {
    @Autowired
    private SMCircuitTaskService service;

    @GetMapping("/{id}")
    public SMCircuitTaskResponse findAllConditional(@PathVariable String id) {
        return service.findById(id);
    }

    @GetMapping
    public IPage<SMCircuitTaskResponse> findAllConditional(SMCircuitTaskPageRequest request) {
        return service.findAllConditional(request);
    }

    @GetMapping("/my")
    public IPage<SMCircuitTaskResponse> findMyConditional(SMCircuitTaskPageRequest request) throws ThingsboardException {
        String uuid = UUIDConverter.fromTimeUUID(getCurrentUser().getId().getId());
        request.setReceiveUserId(uuid);
        return findAllConditional(request);
    }

    @GetMapping("/workOrder/pitfall")
    public IPage<SMCircuitTaskWorkOrderPitfallResponse> pitfallWorkOrderList(SMCircuitTaskWorkOrderPitfallPageRequest request) {
        return service.findPitfallWorkorderInfo(request);
    }

    @GetMapping("/workOrder/pitfall/{taskCode}/{pointId}")
    public SMCircuitTaskWorkOrderPitfallResponse pitfallWorkOrder(@PathVariable String taskCode, @PathVariable String pointId) throws ThingsboardException {
        return service.findPitfallWorkorderInfoByPointId(taskCode, pointId, UUIDConverter.fromTimeUUID(getTenantId().getId()));
    }

    @GetMapping("/workOrder/trend")
    public StatisticItem findWorkOrderTrend(SMCircuitTaskWorkOrderTrendRequest req) {
        return service.findWorkOrderTrend(req);
    }

    @GetMapping("/workOrder/countByUser")
    public StatisticItem countByUser(SMCircuitTaskWorkOrderStatisticUserRequest req) {
        return service.countByUser(req);
    }

    @GetMapping("/completeCount")
    public GeneralTaskStatusStatistic completeCount() throws ThingsboardException {
        String userId = UUIDConverter.fromTimeUUID(getCurrentUser().getId().getId());
        return service.countStatusByUser(userId, APPROVED);
    }

    @GetMapping("/completeCount/global")
    public GeneralTaskStatusStatistic globalCompleteCount() throws ThingsboardException {
        return service.countStatusByUser(null, APPROVED);
    }

    @GetMapping("/processingAndCompleteCount")
    public GeneralTaskProcessingAndCompleteCount processingAndCompleteCount() throws ThingsboardException {
        String userId = UUIDConverter.fromTimeUUID(getCurrentUser().getId().getId());
        return service.SMCircuitTaskProcessingAndCompleteCount(userId);
    }

    // @PostMapping
    public SMCircuitTask save(@RequestBody SMCircuitTaskSaveRequest req) {
        return service.save(req);
    }

    @PostMapping("/{id}/assign")
    public boolean assign(@RequestBody SMCircuitTaskAssignRequest req, @PathVariable String id) {
        req.setTaskId(id);
        return service.assign(req);
    }

    @PostMapping("/{id}/complete")
    public boolean complete(@RequestBody SMCircuitTaskCompleteRequest req, @PathVariable String id) {
        if (!service.canBeComplete(id)) {
            ExceptionUtils.silentThrow("请先完成所有报告");
        }
        req.setTaskId(id);
        return service.complete(req);
    }

    @PostMapping("/assign")
    public boolean assign(@RequestBody SMCircuitTaskBatchAssignRequest req) {
        return service.assignBatch(req);
    }

    //批量审核分派任务
    @PostMapping("/audit")
    public boolean audit(@RequestBody SMCircuitTaskBatchAuditRequest req) {
        return service.auditBatch(req);
    }

    @PostMapping("/workOrder")
    public boolean reportWorkOrder(@RequestBody SMCircuitTaskReportRequest req) throws ThingsboardException {
        if (service.isComplete(req.getCode(), UUIDConverter.fromTimeUUID(getTenantId().getId()))) {
            ExceptionUtils.silentThrow("任务已完成，不允许继续提交结果");
        }
        return service.report(req);
    }

    @PatchMapping("/{id}")
    public boolean edit(@RequestBody SMCircuitTaskSaveRequest req, @PathVariable String id) {
        return service.update(req.unwrap(id));
    }

    @DeleteMapping("/{id}")
    public boolean delete(@PathVariable String id) {
        return service.delete(id);
    }

    @DeleteMapping
    public boolean deleteAll(@RequestBody List<String> idList) {
        return service.deleteAll(idList);
    }

    // region 巡检用户
    @GetMapping("/user/group/{type}")
    public List<Organization> groupUser(@PathVariable String type) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return service.findGroupedUser(tenantId, DataConstants.PEOPLE_TYPE.fromWord(type));
    }

    @GetMapping("/user/statistic/{type}")
    public SMCircuitUserStatistic statisticUser(@PathVariable String type) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return service.statisticUser(tenantId, DataConstants.PEOPLE_TYPE.fromWord(type));
    }
    // endregion


    @GetMapping("/completeTotal")
    public Integer completeTotal() throws ThingsboardException {
        return service.completeTotal();
    }

    @GetMapping("/arrivalRate")
    public String arrivalRate() throws ThingsboardException {
        return service.arrivalRate();
    }

    @GetMapping("/feedbackRate")
    public String feedbackRate() throws ThingsboardException {
        return service.feedbackRate();
    }
}