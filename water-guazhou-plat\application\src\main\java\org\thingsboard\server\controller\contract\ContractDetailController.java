package org.thingsboard.server.controller.contract;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.sql.deviceManage.Device;
import org.thingsboard.server.dao.model.sql.purchase.ContractDetail;
import org.thingsboard.server.dao.purchase.ContractDetailService;
import org.thingsboard.server.dao.sql.deviceType.DeviceMapper;
import org.thingsboard.server.dao.util.imodel.query.purchase.ContractDetailPageRequest;
import org.thingsboard.server.dao.util.imodel.query.purchase.ContractDetailSaveRequest;
import org.thingsboard.server.utils.ExcelUtil;
import org.thingsboard.server.utils.imodel.annotations.IStarController;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@IStarController
@RequestMapping("/api/contractDetail")
public class ContractDetailController extends BaseController {
    @Autowired
    private ContractDetailService service;

    @Autowired
    private DeviceMapper deviceMapper;

    @GetMapping
    public IPage<ContractDetail> findAllConditional(ContractDetailPageRequest request) {
        return service.findAllConditional(request);
    }

    @GetMapping("/templateFile")
    public void templateFile(HttpServletResponse response) throws ThingsboardException {
        Map headMap = new LinkedHashMap();
        headMap.put("设备编号", "设备编号");
        headMap.put("货品名称", "货品名称");
        headMap.put("规格型号", "规格型号");
        headMap.put("单位", "单位");
        headMap.put("数量", "数量");
        headMap.put("单价", "单价");
        headMap.put("税率", "税率");
        headMap.put("备注", "备注");
        SXSSFWorkbook workbook = new SXSSFWorkbook();
        ExcelUtil.exportExcelXManySheet("", headMap, new JSONArray(), "yyyy-MM-dd", 20, "合同材料信息导入模板", workbook);

        // 设备信息
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        List<Device> list = deviceMapper.getList(null, "", "", "", 1, Integer.MAX_VALUE, tenantId);
        headMap = new LinkedHashMap();
        headMap.put("serialId", "设备编号");
        headMap.put("name", "货品名称");
        headMap.put("model", "规格型号");
        headMap.put("unit", "单位");
        JSONArray jsonArray = JSONArray.parseArray(JSONObject.toJSONString(list));
        ExcelUtil.exportExcelXManySheet("设备信息对照表", headMap, jsonArray, "yyyy-MM-dd", 20, "设备信息对照表", workbook);
        ByteArrayOutputStream os = new ByteArrayOutputStream();

        try {
            workbook.write(os);
            workbook.close();
            workbook.dispose();

            ExcelUtil.exportExcel("合同材料信息导入模板", os, response);

        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    // @PostMapping
    public List<ContractDetail> save(@RequestBody List<ContractDetailSaveRequest> req) throws ThingsboardException {
        return service.saveAll(req);
    }

    // @PatchMapping("/{id}")
    public boolean edit(@RequestBody ContractDetailSaveRequest req, @PathVariable String id) {
        return service.update(req.update(id));
    }

    // @DeleteMapping("/{id}")
    public boolean delete(@PathVariable String id) {
        return service.delete(id);
    }
}