/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.virtual;

import com.google.common.util.concurrent.ListenableFuture;
import org.springframework.stereotype.Component;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.virtual.Virtual;
import org.thingsboard.server.dao.Dao;

import java.util.List;

@Component
public interface VirtualDao extends Dao<Virtual> {
    ListenableFuture<List<Virtual>> findVirtualByTenant(TenantId tenantId);

    ListenableFuture<Virtual> findByVirtualId(String virtualId);

    ListenableFuture<List<Virtual>> findAll();

    ListenableFuture<Virtual> virtualSave(TenantId tenantId,Virtual virtual);

    ListenableFuture<List<Virtual>> findBySerialNumber(String serialNumber);

    ListenableFuture<List<Virtual>> findVirtualByTenantAndType(TenantId tenantId, String type);


}
