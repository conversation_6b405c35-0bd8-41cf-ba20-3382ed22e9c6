package org.thingsboard.server.dao.model.sql.maintainCircuit.circuit;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 巡检
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-09-08
 */
@TableName("tb_device_circuit_point_m")
@Data
public class CircuitPointM {
    @TableId
    private String id;

    private String name;

    private String remark;

    private String creator;

    private transient String creatorName;

    private transient List<CircuitPointC> circuitPointCList;

    private Date createTime;

    private Date updateTime;

    private String tenantId;

}
