package org.thingsboard.server.dao.smartService.call;

import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.smartService.call.Blacklist;

import java.util.List;

/**
 * 黑名单
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-10-27
 */
public interface BlacklistService {
    PageData getList(String keywords, int page, int size, String tenantId);

    Blacklist save(Blacklist blacklist);

    int delete(List<String> ids);

    boolean checkPhone(Blacklist blacklist);
}
