package org.thingsboard.server.dao.model.sql;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.thingsboard.server.dao.model.ModelConstants;

import javax.persistence.*;

/**
 * <AUTHOR>
 * @date 2020/4/26 10:51
 */
@Data
@Entity
@Table(name = ModelConstants.VIDEO_TABLE_NAME)
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
public class VideoEntity {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    /**
     * 名称
     */
    @Column(name = ModelConstants.DATASOURCE_NAME)
    private String name;

    /**
     *序列号
     */
    @Column(name = ModelConstants.VIDEO_SERIAL_NUMBER)
    private String serialNumber;
    /**
     * 通道ID
     */
    @Column(name = ModelConstants.VIDEO_CHANNEL_ID)
    private String channelId;
    /**
     * 验证码
     */
    @Column(name = ModelConstants.VIDEO_CAPTCHA)
    private String captcha;
    /**
     * AppKey
     */
    @Column(name = ModelConstants.VIDEO_APP_KEY)
    private String appKey;
    /**
     * AppSecret
     */
    @Column(name = ModelConstants.VIDEO_APP_SECRET)
    private String appSecret;
    /**
     * 创建时间
     */
    @Column(name = ModelConstants.DATASOURCE_UPDATE_TIME)
    private long updateTime;
    /**
     * 视频流地址
     */
    @Column(name = ModelConstants.URL)
    private String url;
    /**
     * 视频类别 1海康  2大华
     */
    @Column(name = ModelConstants.VIDEO_TYPE)
    private String videoType;
    /**
     * 企业ID
     */
    @Column(name = ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;
    /**
     * 项目ID
     */
    @Column(name = ModelConstants.PROJECT_RELATION_PROJECT_ID)
    private String projectId;

    @Column(name = ModelConstants.ADDITIONAL_INFO)
    private String additionalInfo;

    @Column(name = ModelConstants.TO_ORACLE)
    private String toOracle;

    @Column(name = ModelConstants.GROUP_ID)
    private String groupId;

    @Column(name = ModelConstants.ORDER_NUM)
    private Integer orderNum;

    /**
     * 经度
     */
    @Column(name = ModelConstants.VIDEO_LONGITUDE)
    private String longitude;

    /**
     * 纬度
     */
    @Column(name = ModelConstants.VIDEO_LATITUDE)
    private String latitude;

    /**
     * 位置
     */
    @Column(name = ModelConstants.VIDEO_LOCATION)
    private String location;

    @Transient
    private String status;

}
