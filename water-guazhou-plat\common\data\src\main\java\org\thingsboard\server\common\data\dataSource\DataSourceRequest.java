package org.thingsboard.server.common.data.dataSource;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/2/27 13:50
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DataSourceRequest {
    /**
     * 数据源ID列表
     */
    private List<String> dataSourceIds;
    /**
     * 查询开始时间
     */
    private long startTime;
    /**
     * 查询结束时间
     */
    private long endTime;
    /**
     * 数据间隔
     */
    private String interval;
}
