package org.thingsboard.server.dao.util.imodel.query.smartManagement;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartManagement.UserCoordinate;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;

@Getter
@Setter
public class UserCoordinateSaveRequest extends SaveRequest<UserCoordinate> {
    // 用户id
    private String userId;

    // 坐标
    private String coordinate;

    @Override
    protected UserCoordinate build() {
        UserCoordinate entity = new UserCoordinate();
        entity.setCreateTime(createTime());
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    @Override
    protected UserCoordinate update(String id) {
        disallowUpdate();
        return null;
    }

    @Override
    protected boolean allowUpdate() {
        return false;
    }

    private void commonSet(UserCoordinate entity) {
        entity.setUserId(userId);
        entity.setCoordinate(coordinate);
    }
}