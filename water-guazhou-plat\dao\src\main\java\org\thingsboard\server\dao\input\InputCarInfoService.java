package org.thingsboard.server.dao.input;

import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.request.InputCarInfoListRequest;
import org.thingsboard.server.dao.model.sql.input.InputCarInfo;

import java.util.List;

public interface InputCarInfoService {
    void save(InputCarInfo inputCarInfo);

    PageData<InputCarInfo> findList(InputCarInfoListRequest request);

    void remove(List<String> ids);
}
