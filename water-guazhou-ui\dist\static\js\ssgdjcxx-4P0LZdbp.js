import{d as c,r,a8 as i,g as l,h as n,F as d,q as p,i as m,bz as f,C as _}from"./index-r0dFAfgr.js";import{d as u}from"./index-CCFuhOrs.js";/* empty css                             */import"./DPlayer-Be2AurQX.js";import"./DPlayer.min-_HMH7IVX.js";const x=c({__name:"ssgdjcxx",props:{config:{}},setup(e){const t=e,a=r({defaultValue:i(()=>t.config),border:!0,direction:"horizontal",column:2,title:"所属归档基础信息",fields:[{type:"text",label:"归档备注:",field:"remark"},{type:"text",label:"创建人:",field:"creatorName"},{type:"text",label:"创建时间:",field:"createTimeName"},{type:"text",label:"最后更新人:",field:"updateUserName"},{type:"text",label:"最后更新时间:",field:"updateTimeName"}]});return(b,g)=>{const o=u,s=f;return l(),n(s,{class:"card"},{default:d(()=>[p(o,{config:m(a)},null,8,["config"])]),_:1})}}}),j=_(x,[["__scopeId","data-v-3e958774"]]);export{j as default};
