package org.thingsboard.server.controller.base;

import com.datastax.driver.core.utils.UUIDs;
import org.springframework.data.repository.query.Param;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardErrorCode;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.dao.extraUser.ExtraUserService;
import org.thingsboard.server.dao.model.sql.ExtraUser;
import org.thingsboard.server.service.aspect.annotation.SysLog;

import javax.annotation.Resource;
import java.util.List;

/**
 * (ExtraUser)表控制层
 *
 * <AUTHOR>
 * @since 2020-05-09 11:30:36
 */
@RestController
@RequestMapping("/api/extraUser")
public class ExtraUserController extends BaseController {
    /**
     * 服务对象
     */
    @Resource
    private ExtraUserService extraUserService;

    /**
     * 保存外部联系人数据
     *
     * @param extraUser 外部联系人实体
     * @return 单条数据
     */
    @PostMapping("/save")
    @SysLog(options = DataConstants.OPERATING_TYPE.OPERATING_TYPE_ADD_EXTRA_USER)
    public ExtraUser save(@RequestBody ExtraUser extraUser) throws ThingsboardException {
        if (extraUser.getId() == null) {
            if (extraUserService.checkName(extraUser.getName(), UUIDConverter.fromTimeUUID(getTenantId().getId())) > 0) {
                throw new ThingsboardException("该用户已存在！", ThingsboardErrorCode.GENERAL);
            }
            extraUser.setId(UUIDConverter.fromTimeUUID(UUIDs.timeBased()));
            extraUser.setTenantId(UUIDConverter.fromTimeUUID(getTenantId().getId()));
        }
        extraUser.setUpdateTime(System.currentTimeMillis());
        return extraUserService.save(extraUser);
    }


    /**
     * 获取当前企业下的外部联系人列表
     *
     * @return 外部联系人列表
     * @throws ThingsboardException 异常捕获
     */
    @GetMapping("/list")
    public List<ExtraUser> findExtraUserById() throws ThingsboardException {
        return extraUserService.findByTenant(UUIDConverter.fromTimeUUID(getTenantId().getId()));
    }


    /**
     * 删除单个外部联系人
     *
     * @param id id
     * @return 删除结果
     */
    @DeleteMapping("/delete")
    @SysLog(options = DataConstants.OPERATING_TYPE.OPERATING_TYPE_DELETE_EXTRA_USER)
    public boolean delete(@Param("id") String id) {
        return extraUserService.delete(id);
    }

    /**
     * 批量删除外部联系人
     *
     * @param ids 联系人id数组
     * @return 删除结果
     */
    @PostMapping("/delete/all")
    @SysLog(options = DataConstants.OPERATING_TYPE.OPERATING_TYPE_DELETE_EXTRA_USER)
    public boolean deleteAll(@RequestBody List<String> ids) {
        return extraUserService.deleteAll(ids);
    }


}