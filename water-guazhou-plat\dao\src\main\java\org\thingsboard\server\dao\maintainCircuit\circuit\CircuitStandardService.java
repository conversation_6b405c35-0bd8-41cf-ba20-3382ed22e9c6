package org.thingsboard.server.dao.maintainCircuit.circuit;

import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.maintainCircuit.circuit.CircuitStandard;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.List;

/**
 * 班组
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-08-24
 */
public interface CircuitStandardService {

    PageData getList(String deviceTypeId, String keywords, int page, int size, String tenantId);

    CircuitStandard save(CircuitStandard circuitStandard);

    IstarResponse delete(List<String> ids);
}
