<template>
  <DialogForm ref="refDialog" :config="DialogConfig">
    <template #fieldSlot="{ config, row }">
      <div
        v-if="config.field === 'coordinate'"
        style="width: 100%; height: 250px"
      >
        <FormMap
          ref="refMap"
          v-model="state.coordinate"
          :row="row"
          :show-input="(config as IFormMap).showInput"
          :disabled="(config as IFormMap).disabled"
          :readonly="config.readonly"
          @change="config.onChange"
          @loaded="handleLoaded"
        ></FormMap>
      </div>
    </template>
  </DialogForm>
</template>
<script lang="ts" setup>
import { PostLossWorkOrder } from '@/api/mapservice/dma';
import { InverseGeocoding } from '@/api/mapservice/utils';
import { usePartition } from '@/hooks/arcgis';
import useUser from '@/hooks/user/useUser';
import { useUserStore } from '@/store';
import { SLMessage } from '@/utils/Message';
import {
  formatWorkOrderDealLevelMinutes,
  getEmergencyLevelOpetions,
  getOrderTypeOptions,
  WorkOrderDealLevel
} from '@/views/workorder/config';

const refMap =
  ref<
    InstanceType<(typeof import('@/components/arcMap/FormMap.vue'))['default']>
  >();
const { getUserOptions } = useUser();
const emit = defineEmits(['success', 'fail']);
const refDialog = ref<IDialogFormIns>();
const state = reactive<{ coordinate: any }>({ coordinate: undefined });
const props = defineProps<{
  /**
   * 当需要选择分区时，此值设置为true
   */
  needPartition?: boolean;
  defaultValues?: Record<string, any>;
}>();
const partition = usePartition();
const partitionConfig: ISelectTree[] = props.needPartition
  ? [
      {
        type: 'select-tree',
        label: '分区',
        field: 'partitionId',
        placeholder: '请选择分区',
        checkStrictly: true,
        autoFillOptions(config) {
          partition.getTree().then(() => {
            config.options = partition.Tree.value || [];
          });
        },
        onChange(value) {
          initMapCenter(value);
        },
        rules: [{ required: true, message: '请选择分区' }]
      }
    ]
  : [];
const initMapCenter = (value: string) => {
  partition
    .getPartitionInfo(value)
    .then((res) => {
      try {
        const geomJson = res.data?.geom;
        if (!geomJson) return;
        const geom = JSON.parse(geomJson);
        const view = refMap.value?.getView();
        const g = partition.createPartitionGraphic(view, geom);
        if (!g) return;
        const center = g.geometry.extent;
        view?.goTo(center);
      } catch (error) {
        //
      }
    })
    .catch(() => {
      //
    });
};
const DialogConfig = reactive<IDialogFormConfig>({
  dialogWidth: 600,
  title: '新建工单',
  labelPosition: 'right',
  group: [
    {
      fields: [
        {
          type: 'input',
          label: '标题',
          field: 'title',
          rules: [{ required: true, message: '请填写标题' }],
          placeholder: '请填写标题'
        },
        {
          type: 'select',
          label: '接收人',
          field: 'stepProcessUserId',
          rules: [{ required: true, message: '请选择接收人' }],
          placeholder: '请选择',
          async autoFillOptions(config) {
            const res = await getUserOptions(false, {
              authority: 'CUSTOMER_USER'
            });
            config.options = res;
          }
        },
        // {
        //   type: 'select',
        //   label: '部门',
        //   field: 'departmentId',
        //   handleHidden: (params: any, query: any, formItem: IFormItem) => {
        //     console.log(params)
        //     formItem.hidden = params.radio1 === 'person'
        //   },
        //   rules: [{ required: true, message: '请填写巡检方法' }],
        //   placeholder: '请选择'
        // },
        {
          field: 'processLevelLabel',
          label: '处理级别',
          type: 'select',
          options: WorkOrderDealLevel()
        },
        // {
        //   type: 'datetime',
        //   label: '完成时间',
        //   field: 'date',
        //   textFormat: 'YYYY-MM-DD HH:mm',
        //   rules: [{ required: true, message: '请选择完成时间' }],
        //   disabledDate(date) {
        //     return moment().startOf('day').valueOf() > date.valueOf()
        //   }
        // },
        {
          type: 'select',
          label: '紧急程度',
          field: 'level',
          options: getEmergencyLevelOpetions(),
          rules: [{ required: true, message: '请选择紧急程度' }]
        },
        {
          type: 'select-tree',
          label: '事件类型',
          field: 'type',
          options: getOrderTypeOptions(),
          rules: [{ required: true, message: '请选择事件类型' }]
        },
        ...partitionConfig,
        {
          field: 'coordinate',
          label: '坐标',
          type: 'form-map',
          showInput: true,
          onChange: (val) => {
            InverseGeocoding({
              lon: val?.[0] || '0',
              lat: val?.[1] || '0'
            }).then((res) => {
              if (!refDialog.value?.refForm?.dataForm) return;
              refDialog.value.refForm.dataForm.coordinate = val?.join(',');
              refDialog.value.refForm.dataForm.address =
                res.data.result.formatted_address || '';
            });
          }
        },
        {
          field: 'address',
          label: '地址',
          type: 'input'
        },
        {
          type: 'textarea',
          label: '事件描述',
          field: 'remark',
          placeholder: '请输入事件描述'
        }
      ]
    }
  ],
  defaultValue: {
    isDirectDispatch: true,
    source: '漏损控制',
    organizerId: useUserStore().user?.id?.id
  },
  async submit(params) {
    DialogConfig.submitting = true;
    try {
      const subMitParams = {
        ...params,
        ...(props.defaultValues || {}),
        processLevel: formatWorkOrderDealLevelMinutes(params.processLevelLabel)
      };
      const res = await PostLossWorkOrder(subMitParams);
      if (res.data.code === 200) {
        SLMessage.success('提交成功');
        emit('success');
        refDialog.value?.closeDialog();
      } else {
        SLMessage.error(res.data.message);
        emit('fail');
      }
    } catch (error) {
      SLMessage.error('提交失败');
    }
    DialogConfig.submitting = false;
  }
});
watch(
  () => refDialog.value?.refForm?.dataForm?.partitionId,
  (value) => {
    if (!value) return;
    initMapCenter(value);
  }
);
const openDialog = () => refDialog.value?.openDialog();
defineExpose({
  openDialog,
  refDialog
});
const handleLoaded = () => {
  if (props.defaultValues?.partitionId) {
    initMapCenter(props.defaultValues.partitionId);
  }
};
</script>
<style lang="scss" scoped></style>
