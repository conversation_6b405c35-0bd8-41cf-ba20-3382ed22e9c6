package org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoProjectArchive;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

import java.util.Date;

@Getter
@Setter
public class SoProjectArchiveSaveRequest extends SaveRequest<SoProjectArchive> {
    // 所属项目编号
    @NotNullOrEmpty
    private String projectCode;

    // 总归档时间
    @NotNullOrEmpty
    private Date archiveTime;

    // 总归档说明
    private String remark;

    // 附件信息
    private String attachments;

    @Override
    protected SoProjectArchive build() {
        SoProjectArchive entity = new SoProjectArchive();
        entity.setProjectCode(projectCode);
        entity.setCreator(currentUserUUID());
        entity.setCreateTime(createTime());
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    @Override
    protected SoProjectArchive update(String id) {
        SoProjectArchive entity = new SoProjectArchive();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(SoProjectArchive entity) {
        entity.setArchiveTime(archiveTime);
        entity.setRemark(remark);
        entity.setAttachments(attachments);
        entity.setUpdateUser(currentUserUUID());
        entity.setUpdateTime(createTime());
    }
}