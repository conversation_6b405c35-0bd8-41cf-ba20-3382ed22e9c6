import{_ as F}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as q}from"./CardTable-rdWOL4_6.js";import{_ as B}from"./CardSearch-CB_HNR-Q.js";import{d as L,M as O,c as x,r as g,a8 as b,s as w,bT as A,D as I,S as R,o as V,g as N,n as E,q as D,i as y,al as $,ak as P}from"./index-r0dFAfgr.js";import{u as H}from"./useStation-DJgnSZIA.js";import{f as p}from"./DateFormatter-Bm9a68Ax.js";import{r as j,e as z,f as G,h as J,i as K,j as Q}from"./stationCircuit-CGrX5qR4.js";import{u as X}from"./useDepartment-BkP08hh6.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";import"./zhandian-YaGuQZe6.js";const Z={class:"wrapper"},me=L({__name:"index",setup(ee){const{getAllStationOption:S}=H(),{getDepartmentTree:T}=X(),{$messageSuccess:c,$messageError:n,$messageWarning:te,$confirm:v}=O(),u=x(),Y=x(),_=x(),i=g({optionUsers:[],auditUsers:[],searchOptionUsers:[],searchAuditUsers:[],stationOptionList:[],departmentTree:[],currentTask:{},code:"",counter:"000100"}),m=g({filters:[{label:"任务编号",field:"code",type:"input",placeholder:"请输入任务编号"},{label:"业务类型",field:"type",type:"select",placeholder:"请选择业务类型",options:[{label:"维修业务",value:"维修业务"},{label:"保养业务",value:"保养业务"},{label:"清洗业务",value:"清洗业务"}]},{label:"维保部门",field:"optionDep",type:"select-tree",options:b(()=>i.departmentTree),placeholder:"请选择维保部门",onChange:async e=>{i.searchOptionUsers=await h(e)}},{label:"维保人员",field:"optionUserId",type:"select",placeholder:"请选择维保人员",options:b(()=>i.searchOptionUsers)},{label:"审核部门",field:"auditDep",type:"select-tree",options:b(()=>i.departmentTree),placeholder:"请选审核部门",onChange:async e=>{console.log(e),i.searchAuditUsers=await h(e)}},{label:"审核人员",field:"auditUserId",type:"select",placeholder:"请选择审核人",options:b(()=>i.searchAuditUsers)},{label:"预计开始时间",field:"startTime",type:"date",format:"YYYY-MM-DD",placeholder:"请选择预计开始时间",onChange:e=>{var l;const t=(l=m.filters)==null?void 0:l.find(a=>a.field==="endTime");t.disabledDate=function(a){return a<new Date(e)}}},{label:"预计结束时间",field:"endTime",type:"date",format:"YYYY-MM-DD",placeholder:"请选择预计结束时间",onChange:e=>{var l;const t=(l=m.filters)==null?void 0:l.find(a=>a.field==="startTime");t.disabledDate=function(a){return a>new Date(e)}}},{label:"实际开始时间",field:"realStartTime",type:"date",format:"YYYY-MM-DD",placeholder:"请选择实际开始时间",onChange:e=>{var l;const t=(l=m.filters)==null?void 0:l.find(a=>a.field==="realEndTime");t.disabledDate=function(a){return a<new Date(e)}}},{label:"实际结束时间",field:"realEndTime",type:"date",format:"YYYY-MM-DD",placeholder:"请选择实际结束时间",onChange:e=>{var l;const t=(l=m.filters)==null?void 0:l.find(a=>a.field==="realStartTime");t.disabledDate=function(a){return a>new Date(e)}}},{label:"状态",field:"status",type:"select",placeholder:"请选择状态",options:[{label:"待接收",value:"待接收"},{label:"执行中",value:"执行中"},{label:"待审核",value:"待审核"},{label:"完成",value:"完成"},{label:"按时完成",value:"按时完成"},{label:"超期完成",value:"超期完成"},{label:"已作废",value:"已作废"}]},{label:"审核结果",field:"auditResult",type:"select",placeholder:"请选择审核结果",options:[{label:"未审核",value:"未审核"},{label:"合格",value:"合格"},{label:"不合格",value:"不合格"}]}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",svgIcon:w($),click:()=>d()},{text:"新增",perm:!0,svgIcon:w(P),type:"success",click:()=>{var e;o.defaultValue={},o.title="新增",(e=u.value)==null||e.openDialog()}}]}]}),s=g({loading:!1,dataList:[],indexVisible:!0,columns:[{prop:"name",label:"任务名称",minWidth:"200px"},{prop:"type",label:"业务类型",minWidth:"200px"},{prop:"optionDepName",label:"维保部门",minWidth:"200px"},{prop:"optionUserName",label:"维保人员",minWidth:"200px"},{prop:"days",label:"消耗天数",minWidth:"200px",formatter:(e,t)=>e.endTime?Math.floor((e.endTime-e.startTime)/(1e3*60*60*24)):"-"},{prop:"realStartTime",label:"任务开始时间",minWidth:"200px",formatter:(e,t)=>p(t,"YYYY-MM-DD")},{prop:"realEndTime",label:"任务结束时间",minWidth:"200px",formatter:(e,t)=>p(t,"YYYY-MM-DD")},{prop:"startTime",label:"预计开始时间",minWidth:"200px",formatter:(e,t)=>p(t,"YYYY-MM-DD")},{prop:"endTime",label:"预计结束时间",minWidth:"200px",formatter:(e,t)=>p(t,"YYYY-MM-DD")},{prop:"auditDepName",label:"审核部门",minWidth:"200px"},{prop:"auditUserName",label:"审核人员",minWidth:"200px"},{prop:"auditResult",label:"审核结果",minWidth:"200px"},{prop:"auditRemark",label:"审核备注",minWidth:"200px"},{prop:"status",label:"状态",minWidth:"200px"},{prop:"createTime",label:"添加时间",minWidth:"200px",formatter:(e,t)=>p(t,"YYYY-MM-DD HH:mm:ss")}],operationFixed:"right",operationWidth:300,operations:[{text:"接收",isTextBtn:!0,perm:!0,type:"warning",disabled:e=>e.status!=="未开始",click:e=>{v("确定接收此任务?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{j({id:e.id}).then(t=>{t.data.code===200?(c("接收成功"),d()):n(t.data.message)})})}},{text:"完成",isTextBtn:!0,perm:!0,type:"success",disabled:e=>e.status!=="处理中",click:e=>{v("确定完成此任务?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{z({id:e.id}).then(t=>{t.data.code===200?(c("提交成功"),d()):n(t.data.message)})})}},{text:"审核",isTextBtn:!0,perm:!0,disabled:e=>e.status!=="处理完成",click:e=>{var t;i.currentTask=e,(t=Y.value)==null||t.openDialog()}},{text:"编辑",isTextBtn:!0,perm:!0,disabled:e=>!!e.auditResult,click:e=>{var t;o.title="编辑",o.defaultValue={...e,startTime:e.startTime?p(e.startTime,"YYYY-MM-DD"):"",endTime:e.endTime?p(e.endTime,"YYYY-MM-DD"):""},(t=u.value)==null||t.openDialog()}},{perm:!0,text:"删除",isTextBtn:!0,type:"danger",icon:"iconfont icon-shanchu",click:e=>U(e)}],pagination:{refreshData:({page:e,size:t})=>{s.pagination.limit=t,s.pagination.page=e,d()}}}),o=g({title:"新增",defaultValue:{},dialogWidth:900,group:[{fields:[{xs:16,type:"input",label:"任务编号",readonly:!0,clearable:!1,field:"code",rules:[{required:!0,message:"请输入任务名称"}]},{xs:8,type:"btn-group",field:"codeButton",btns:[{text:"获取编号",perm:!0,click:()=>{var l,a,r,f;const e=W(),t=(a=(l=u.value)==null?void 0:l.refForm)==null?void 0:a.dataForm;o.defaultValue={...t,code:e},(f=(r=u.value)==null?void 0:r.refForm)==null||f.resetForm()}}]},{xs:12,type:"input",label:"任务名称",field:"name",rules:[{required:!0,message:"请填写任务名称"}]},{xs:12,label:"业务类型",field:"type",type:"select",placeholder:"请选择业务类型",options:[{label:"维修业务",value:"维修业务"},{label:"保养业务",value:"保养业务"},{label:"清洗业务",value:"清洗业务"}]},{xs:12,type:"date",label:"开始时间",field:"startTime",format:"YYYY-MM-DD",rules:[{required:!0,message:"请选择开始时间"}],disabledDate:e=>new Date>e,onChange:e=>{var l;const t=(l=o.group[0].fields)==null?void 0:l.find(a=>a.field==="endTime");t.disabledDate=function(a){const r=e;return a<new Date(r)}}},{xs:12,type:"date",label:"结束时间",field:"endTime",format:"YYYY-MM-DD",rules:[{required:!0,message:"请选择结束时间"}],onChange:e=>{var l;const t=(l=o.group[0].fields)==null?void 0:l.find(a=>a.field==="startTime");t.disabledDate=function(a){const r=e;return a>new Date(r)}}},{xs:12,type:"select-tree",label:"维保部门",field:"optionDep",rules:[{required:!0,message:"请选择维保部门"}],autoFillOptions:async e=>{e.options=await T(2)},onChange:async e=>{i.optionUsers=await h(e)}},{xs:12,type:"department-user",label:"维保人员",field:"optionUserId",rules:[{required:!0,message:"请选择维保人员"}]},{xs:12,type:"select-tree",label:"审核部门",field:"auditDep",rules:[{required:!0,message:"请选择审核部门"}],autoFillOptions:async e=>{e.options=await T(2)},onChange:async e=>{i.auditUsers=await h(e)}},{xs:12,type:"department-user",label:"审核人员",field:"auditUserId",rules:[{required:!0,message:"请选择审核人员"}]},{xs:12,type:"select",field:"stationId",label:"养护站点",rules:[{required:!0,message:"请选择站点"}],options:b(()=>i.stationOptionList)}]}],submit:e=>{G({...e,stationType:"泵站"}).then(t=>{var l,a;((l=t.data)==null?void 0:l.code)===200?c("保存成功"):n("保存失败"),d(),(a=u.value)==null||a.closeDialog()}).catch(t=>{n(t)})}}),C=g({title:"审核",defaultValue:{},dialogWidth:900,group:[{fields:[{xs:24,type:"select",label:"审核结果",field:"auditResult",rules:[{required:!0,message:"请选择维保人员"}],options:[{label:"合格",value:"合格"},{label:"不合格",value:"不合格"}]},{xs:24,type:"textarea",field:"auditRemark",label:"审核备注"}]}],submit:e=>{J({id:i.currentTask.id,...e}).then(t=>{var l,a;((l=t.data)==null?void 0:l.code)===200?c("保存成功"):n("保存失败"),d(),(a=Y.value)==null||a.closeDialog()}).catch(t=>{n(t)})}}),W=()=>{const e=new Date,t=e.getFullYear().toString()+(e.getMonth()+1).toString().padStart(2,"0")+e.getDate().toString().padStart(2,"0");i.counter||(i.counter=100);const l=i.counter.toString().padStart(6,"0");return i.counter++,t+l},h=async e=>((await A({pid:e})).data.data.data||[]).map(a=>({label:a.firstName,value:I(a.id.id)})),d=async()=>{var l;s.loading=!0;const t={...((l=_.value)==null?void 0:l.queryParams)||{},size:s.pagination.limit||20,page:s.pagination.page||1};K(t).then(a=>{var r,f,M,k;s.dataList=((f=(r=a.data)==null?void 0:r.data)==null?void 0:f.data)||[],s.pagination.total=((k=(M=a.data)==null?void 0:M.data)==null?void 0:k.total)||0,s.loading=!1}).catch(a=>{n(a),s.loading=!1})},U=e=>{R("确定删除指定养护任务","删除提示").then(()=>{Q([e.id]).then(t=>{var l;((l=t.data)==null?void 0:l.code)===200?(c("删除成功"),d()):n("删除失败")}).catch(t=>{n(t)})})};return V(async()=>{i.stationOptionList=await S("泵站"),i.departmentTree=await T(2),d()}),(e,t)=>{const l=B,a=q,r=F;return N(),E("div",Z,[D(l,{ref_key:"refSearch",ref:_,config:y(m)},null,8,["config"]),D(a,{config:y(s),class:"card-table"},null,8,["config"]),D(r,{ref_key:"refForm",ref:u,config:y(o)},null,8,["config"]),D(r,{ref_key:"refAuditForm",ref:Y,config:y(C)},null,8,["config"])])}}});export{me as default};
