package org.thingsboard.server.controller;

import com.alibaba.fastjson.JSONObject;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.VO.DeviceDataTableInfoVO;
import org.thingsboard.server.dao.model.VO.DynamicTableVO;
import org.thingsboard.server.dao.stationData.StationDataService;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;
import org.thingsboard.server.utils.ExcelUtil;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("api/station/data")
public class StationDataController extends BaseController {

    @Autowired
    private StationDataService stationDataService;

    @GetMapping("detail/{stationId}")
    public Object stationDataDetail(@PathVariable String stationId, @RequestParam(required = false) String type,
                                    @RequestParam(required = false, defaultValue = "true") boolean customName) throws ThingsboardException {
        return stationDataService.getStationDataDetail(stationId, type, customName, getTenantId());
    }

    @GetMapping("detailByGroup/{stationId}")
    public Object stationDataDetailByGroup(@PathVariable String stationId) throws ThingsboardException {
        return stationDataService.getStationDataDetailByGroup(stationId, getTenantId());
    }

    @GetMapping("list")
    public Object dataList(@RequestParam long start, @RequestParam long end,
                           @RequestParam String stationId,
                           @RequestParam String queryType) throws ThingsboardException {
        Date startTime = new Date(start);
        Date endTime = new Date(end);

        return stationDataService.findStationDataList(stationId, queryType, startTime, endTime, getTenantId());
    }

    @GetMapping("detailList")
    public Object stationDetailList(@RequestParam String stationType, @RequestParam(required = false) String projectId) throws ThingsboardException {
        return stationDataService.getStationDataDetailList(stationType, projectId, getTenantId());
    }

    @GetMapping("detailList/view")
    public Object stationDetailListToMa(@RequestParam String stationType, @RequestParam(required = false) String projectId,
                                        @RequestParam(required = false, defaultValue = "true") boolean customName) throws ThingsboardException {
        return stationDataService.getStationDataDetailListView(stationType, customName, projectId, getTenantId());
    }

    @GetMapping("getStationDataByList")
    public IstarResponse getStationDataByList(@RequestParam String stationIds) throws ThingsboardException {
        return IstarResponse.ok(stationDataService.getStationDataByList(stationIds, getTenantId()));
    }


    /**
     * 查询近三日的数据曲线
     */
    @GetMapping("getThreeDaysData")
    public IstarResponse getThreeDaysData(@RequestParam String deviceId, @RequestParam String attr) throws ThingsboardException {
        return IstarResponse.ok(stationDataService.getThreeDaysData(deviceId, attr, getTenantId()));
    }

    /**
     * 查询指定站点的指定属性分组的数据列表
     * 当attribute不为空时，则查询指定属性的数据
     */
    @GetMapping("stationDayDataQuery")
    public IstarResponse stationDayDataQuery(@RequestParam String stationId, @RequestParam Long start, @RequestParam Long end,
                                          @RequestParam Integer filterStart, @RequestParam Integer filterEnd,
                                          @RequestParam String queryType,
                                          @RequestParam(required = false, defaultValue = "0") String reserve,
                                          @RequestParam(required = false) String group,
                                          @RequestParam(required = false) String attributeId) {
        try {
            DynamicTableVO dynamicTableVO = stationDataService.stationDayDataQuery(stationId, start, end, filterStart, filterEnd, queryType, group, attributeId, getTenantId());
            if ("1".equals(reserve)) {
                if (dynamicTableVO.getTableDataList() != null && dynamicTableVO.getTableDataList().size() > 0) {
                    Collections.reverse(dynamicTableVO.getTableDataList());
                }
            }
            return IstarResponse.ok(dynamicTableVO);
        } catch (ThingsboardException e) {
            return IstarResponse.error(e.getMessage());
        }
    }

    /**
     * 查询指定站点的指定属性的数据列表, 返回结果按天分组
     */
    @GetMapping("stationAttrDataQueryGroupByDay")
    public IstarResponse stationAttrDataQueryGroupByDay(@RequestParam String stationId, @RequestParam Long start, @RequestParam Long end,
                                          @RequestParam Integer filterStart, @RequestParam Integer filterEnd,
                                          @RequestParam String queryType,
                                          @RequestParam String attributeId) {
        try {
            return IstarResponse.ok(stationDataService.stationAttrDataQueryGroupByDay(stationId, start, end, filterStart, filterEnd, queryType, attributeId, getTenantId()));
        } catch (ThingsboardException e) {
            return IstarResponse.error(e.getMessage());
        }
    }


    /**
     * 查询指定站点的所有数据点的今日数据曲线
     */
    @GetMapping("todayDataList")
    public IstarResponse todayDataList(@RequestParam String stationId) throws ThingsboardException {
        return IstarResponse.ok(stationDataService.todayDataList(stationId, getTenantId()));
    }


    /**
     * 数据对比
     */
    @GetMapping("getDataCompare")
    public IstarResponse getDataCompare(@RequestParam String attributes, @RequestParam String queryType,
                                        @RequestParam Long start, @RequestParam Long end,
                                        @RequestParam(required = false) Integer filterStart,
                                        @RequestParam(required = false) Integer filterEnd) {
        try {
            return IstarResponse.ok(stationDataService.getDataCompare(attributes, queryType, start, end, filterStart, filterEnd, getTenantId()));
        } catch (ThingsboardException e) {
            return IstarResponse.error(e.getMessage());
        }
    }

    /**
     * 数据对比
     */
    @GetMapping("getDataCompare/export")
    public IstarResponse getDataCompareExport(@RequestParam String attributes, @RequestParam String queryType,
                                     @RequestParam Long start, @RequestParam Long end,
                                     @RequestParam(required = false) Integer filterStart,
                                     @RequestParam(required = false) Integer filterEnd,
                                     HttpServletResponse response) {
        try {
            DynamicTableVO dataCompare = stationDataService.getDataCompare(attributes, queryType, start, end, filterStart, filterEnd, getTenantId());
            // 数据
            List<JSONObject> tableDataList = dataCompare.getTableDataList();
            // 表头
            List<DeviceDataTableInfoVO> tableInfo = dataCompare.getTableInfo();

            // 创建Workbook对象
            Workbook workbook = new SXSSFWorkbook();
            // 创建Sheet对象
            Sheet sheet = workbook.createSheet("Sheet1");
            List<String> tableValues = new ArrayList<>();
            // 创建表头行对象
            Row headerRow = sheet.createRow(0);
            for (int i = 0; i < tableInfo.size(); i++) {
                DeviceDataTableInfoVO infoVO = tableInfo.get(i);

                // 创建表头单元格对象
                Cell headerCell = headerRow.createCell(i);
                // 设置表头的值
                headerCell.setCellValue(infoVO.getColumnName());
                tableValues.add(infoVO.getColumnValue());

                // 创建表头样式对象
                CellStyle headerCellStyle = workbook.createCellStyle();
                // 设置水平居中
                headerCellStyle.setAlignment(HorizontalAlignment.CENTER);
                // 设置垂直居中
                headerCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                headerCell.setCellStyle(headerCellStyle);
            }

            for (int i = 0; i < tableDataList.size(); i++) {
                JSONObject obj = tableDataList.get(i);
                // 创建行对象
                Row row = sheet.createRow(i + 1);
                for (int i1 = 0; i1 < tableValues.size(); i1++) {
                    // 创建单元格对象
                    Cell cell1 = row.createCell(i1);
                    if (obj.get(tableValues.get(i1)) instanceof BigDecimal) {
                        cell1.setCellValue(obj.getBigDecimal(tableValues.get(i1)).toString());
                    } else {
                        cell1.setCellValue(obj.getString(tableValues.get(i1)));
                    }

                }
            }

            ByteArrayOutputStream os = new ByteArrayOutputStream();

            try {
                workbook.write(os);
                workbook.close();

                ExcelUtil.exportExcel("数据对比导出", os, response);

            } catch (IOException e) {
                e.printStackTrace();
            }

        } catch (ThingsboardException e) {

        }

        return IstarResponse.ok();
    }

    /**
     * 周期对比
     */
    @GetMapping("getCycleCompare")
    public IstarResponse getCycleCompare(@RequestParam String attributeId, @RequestParam String queryType,
                                         @RequestParam Long start, @RequestParam Long end) {
        try {
            return IstarResponse.ok(stationDataService.getCycleCompare(attributeId, queryType, start, end, getTenantId()));
        } catch (ThingsboardException e) {
            return IstarResponse.error(e.getMessage());
        }
    }

    @GetMapping("getStationOriginal")
    public IstarResponse getStationOriginal(@RequestParam int page, @RequestParam int size,
                                            @RequestParam String stationId, @RequestParam String group,
                                            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date beginTime,
                                            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {
        try {
            DynamicTableVO dynamicTableVO = stationDataService.getStationOriginal(stationId, group, beginTime, endTime, getTenantId());

            JSONObject result = new JSONObject();
            result.put("tableInfo", dynamicTableVO.getTableInfo());
            result.put("pageData", PageData.page(dynamicTableVO.getTableDataList(), page, size));

            return IstarResponse.ok(result);
        } catch (ThingsboardException e) {
            return IstarResponse.error(e.getMessage());
        }
    }


    /**
     * 第三方取点用
     */
    @GetMapping("detail/{stationId}/{type}/{property}")
    public Object getDataByDetail(@PathVariable String stationId, @PathVariable String type, @PathVariable String property) throws ThingsboardException {
        return stationDataService.getStationDataDetail(stationId, type, property, getTenantId());
    }
}
