import request from '@/plugins/axios';

// 6.报警确认,参数与强制解除一致
export function alarmConfirm(params) {
  return request({
    url: '/api/noAuthAlarm/confirm',
    method: 'post',
    data: params
  });
}

// 获取项目实时告警
export function getAlarmRealByProjectId(params, projectId?) {
  return request({
    url: `/api/alarm/getAlarmRealTime${projectId ? `/${projectId}` : ''}`,
    method: 'get',
    params
  });
}

// 获取项目实时告警 分页
export function getAlarmRealTimeList(params, projectId?) {
  return request({
    url: `/api/alarm/getAlarmRealTime/page${projectId ? `/${projectId}` : ''}`,
    method: 'get',
    params
  });
}

// 查询项目历史告警
export function getAlarmByProjectId(params) {
  return request({
    url: '/api/alarm/getAlarm/projectId',
    method: 'post',
    data: params
  });
}

// 查询项目历史告警 分页
export function getHistoryAlarmList(data) {
  return request({
    url: '/api/alarm/getAlarm/projectId/page',
    method: 'post',
    data
  });
}

// 报警确认,参数与强制解除一致
export function alarmConfirmByAuth(params) {
  return request({
    url: '/api/alarm/confirm',
    method: 'post',
    data: params
  });
}

export function clearAlarmByAuth(params) {
  return request({
    url: '/api/alarm/clear',
    method: 'post',
    data: params
  });
}

export function getAlarmHistoryByDeviceAndAuth(params) {
  return request({
    url: `/api/alarm/getAlarmHistoryByDevice?deviceId=${params.deviceId}&alarmType=${params.type}`,
    method: 'get'
  });
}

export function connectAlarm(params, alarmJsonId) {
  return request({
    url: `/api/alarmJson/linkedUser/${alarmJsonId}`,
    method: 'post',
    data: params
  });
}

export function getAlarmType(alarmJsonId) {
  return request({
    url: `/api/alarmJson/linkedUser/${alarmJsonId}`,
    method: 'get'
  });
}
// 获取外部联系人
export function getOuterConnect(alarmJsonId) {
  return request({
    url: `/api/alarmJson/linkedExtraUser/${alarmJsonId}`,
    method: 'get'
  });
}

export function connectOuter(params, alarmJsonId) {
  return request({
    url: `/api/alarmJson/linkedExtraUser/${alarmJsonId}`,
    method: 'post',
    data: params
  });
}

/**
 *  get告警 两种方法
 * 根据tenantID获取
 * @param params
 * @returns
 */
export function getAlarmList(params?: any) {
  if (params) {
    return request({
      url: `/api/noAuthAlarm/alarmJson?secret=${params}`,
      method: 'get'
    });
  }
  return request({
    url: '/api/alarmJson',
    method: 'get'
  });
}

export function getAlarmListByProjectId(projectId, params) {
  return request({
    url: `/api/alarmJson/project/${projectId}`,
    method: 'get',
    params
  });
}

// 报警设置分页
export function getAlarmSettingList(projectId, params) {
  return request({
    url: `/api/alarmJson/project/page/${projectId}`,
    method: 'get',
    params
  });
}

// 保存告警设置
export function saveAlarm(params) {
  return request({
    url: '/api/alarmJson/save',
    method: 'post',
    data: params
  });
}

// 删除告警设置
export function deleteAlarm(alarmId) {
  return request({
    url: `/api/alarmJson/delete?id=${alarmId}`,
    method: 'delete'
  });
}
