import{d as T,c as u,r as c,l as n,bH as p,s as D,o as L,g as S,n as W,q as f,i as d,F as E,a9 as M,b6 as q}from"./index-r0dFAfgr.js";import{_ as B}from"./CardTable-rdWOL4_6.js";import{_ as I}from"./CardSearch-CB_HNR-Q.js";import P from"./detail-CU6-qhMl.js";import R from"./OrderStepTags-CClNfq4j.js";import{h as F,i as N,a as V,j}from"./config-DqqM5K5L.js";import{h as z,a as A}from"./index-CpGhZCTT.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";/* empty css                         */import"./detailSteps-BqRp_Y4m.js";/* empty css                */import"./index-CCFuhOrs.js";/* empty css                             */import"./DPlayer-Be2AurQX.js";import"./DPlayer.min-_HMH7IVX.js";import"./DateFormatter-Bm9a68Ax.js";import"./OutsideWorkOrder-C6s8joBt.js";const G={class:"wrapper"},pe=T({__name:"MyJoined",setup(H){const g=u(),m=u(),_=u(),b=c({WorkOrderEmergencyLevelList:[]});function O(){A("1").then(e=>{b.WorkOrderEmergencyLevelList=M(e.data.data||[],"children",{label:"name",value:"name"})})}const k=c({filters:[{type:"input",label:"标题",field:"title",onChange:()=>o()},{xl:16,type:"radio-button",label:"工单状态",field:"status",options:F(!0),formatter:N},{type:"daterange",label:"发起时间",field:"date"},{type:"select-tree",label:"类型",field:"type",options:V()}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",iconifyIcon:"ep:search",click:()=>o()},{perm:!0,text:"重置",type:"default",iconifyIcon:"ep:refresh",click:()=>{var e;(e=m.value)==null||e.resetForm(),o()}},{perm:!0,text:"导出",type:"warning",iconifyIcon:"ep:download",click:()=>{var e;(e=_.value)==null||e.exportTable()}}]}],defaultParams:{date:[n().subtract(1,"M").format(p),n().format(p)],status:""},handleSearch:()=>o()}),t=c({expandable:!0,expandComponent:D(R),columns:j(),defaultExpandAll:!0,dataList:[],pagination:{refreshData:({page:e,size:a})=>{t.pagination.page=e,t.pagination.limit=a,o()}},operations:[{perm:!0,isTextBtn:!0,text:"详情",click:e=>v(e)}]}),v=e=>{var a;t.currentRow=e,(a=g.value)==null||a.openDrawer()},x=c({title:"流程明细",cancel:!1,className:"lightColor",group:[]}),o=async()=>{var e,a,i,s,l;t.loading=!0;try{const r=((e=m.value)==null?void 0:e.queryParams)||{},[w,C]=((a=r.date)==null?void 0:a.length)===2?[n(r.date[0],p).valueOf(),n(r.date[1],p).endOf("D").valueOf()]:[n().subtract(1,"M").startOf("D").valueOf(),n().endOf("D").valueOf()],h={page:t.pagination.page||1,size:t.pagination.limit||20,...r,fromTime:w,toTime:C};delete h.date;const y=await z(h);t.dataList=((s=(i=y.data)==null?void 0:i.data)==null?void 0:s.data)||[],t.pagination.total=((l=y.data)==null?void 0:l.data.total)||0}catch{}t.loading=!1};return L(()=>{o(),O()}),(e,a)=>{const i=I,s=B,l=q;return S(),W("div",G,[f(i,{ref_key:"refSearch",ref:m,config:d(k)},null,8,["config"]),f(s,{ref_key:"refTable",ref:_,config:d(t),class:"card-table"},null,8,["config"]),f(l,{ref_key:"refdetail",ref:g,config:d(x)},{default:E(()=>{var r;return[f(P,{id:(r=d(t).currentRow)==null?void 0:r.id},null,8,["id"])]}),_:1},8,["config"])])}}});export{pe as default};
