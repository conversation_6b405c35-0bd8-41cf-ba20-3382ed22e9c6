import{_ as A}from"./ArcView-DpMnCY82.js";import{_ as C}from"./ArcPipe.vue_vue_type_script_setup_true_lang-ClsVvV2P.js";import N from"./DeviceGroup-DfMgqr6q.js";import s from"./TitleCard-BgReUNwX.js";import S from"./TitleHeader-CBWfLOPA.js";import H from"./LightPlat-BpCirFph.js";import k from"./DeviceStatic_wudang-BJtrH5pg.js";import E from"./CXCFX-D3VS1APB.js";import J from"./GSGWKJTJ-Bf5Vp2iZ.js";import x from"./FQTJ-bBdN7OyF.js";import B from"./LLJK-DMALGaHM.js";import P from"./YLJC-CfYIPw6m.js";import U from"./LSPH-DmSHTeYD.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import{d as F,c as G,r as Z,o as T,Q as O,g as _,n as u,p as l,q as e,F as i,i as n,aB as R,aJ as V,h as Y,X as Q,C as X}from"./index-r0dFAfgr.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import{a as q}from"./LayerHelper-Cn-iiqxI.js";import"./project-DUuzYgGl.js";import{s as h,E as K}from"./StatisticsHelper-D-s_6AyQ.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import{u as M}from"./useStation-DJgnSZIA.js";import{S as m}from"./data-CLo2TII-.js";import"./ViewHelper-BGCZjxXH.js";import"./fieldconfig-Bk3o1wi7.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./6-4nR55Xef.js";import"./useDetector-BRcb7GRN.js";import"./echart-C9Tas6tA.js";import"./statistics-CeyexT_5.js";import"./pipe-nogVzCHG.js";import"./usePartition-DkcY9fQ2.js";import"./dma-SMxrzG7b.js";import"./hookupDevice-Bcbk7s68.js";import"./index-BlG8PIOK.js";import"./zhandian-YaGuQZe6.js";import"./index-BggOjNGp.js";const z={class:"content"},W={class:"map"},$={class:"toprow"},j={class:"loss-rate"},tt={class:"loss-rate-items"},et={class:"toprow-right"},ot={class:"bottomrow"},rt=F({__name:"pipeNetwork_wudang",setup(at){const w=G({zoom:12,defaultFilter:"grayscale(0%) invert(100%) opacity(100%)",defaultFilterColor:"rgb(255 218 189)",defaultCenter:[106.954069,26.573514]}),y={},t=Z({lossRates:[{type:"up",delta:11.8,value:23.8,title:"产销差率"},{type:"down",delta:11.8,value:13.8,title:"综合漏损率"},{type:"up",delta:13.8,value:3.8,title:"漏损率"}],pipeData:{pipeLength:0,valve:0,meter:0,drayAirValve:0,hydrant:0,threeCorss:0},deviceData:{pipeLength:0,waterQuality:0,pressure:0,bigUser:0,secondary:0,flow:0},layerInfos:[],layerIds:[]}),g=async()=>{var p,d,f;const r=await h("length",{layerIds:t.layerInfos.filter(a=>a.geometrytype==="esriGeometryPolyline").map(a=>a.layerid)});t.pipeData.pipeLength=(f=(d=(p=r[0])==null?void 0:p.rows)==null?void 0:d[0])==null?void 0:f[K.ShapeLen],t.deviceData.pipeLength=t.pipeData.pipeLength,(await h("count",{layerIds:t.layerIds})).map(a=>{const c=a.rows[0].OBJECTID;switch(a.layername){case"阀门":t.pipeData.valve=c;break;case"水表":t.pipeData.meter=c;break;case"排气阀":t.pipeData.drayAirValve=c;break;case"消防栓":t.pipeData.hydrant=c;break;case"三通":t.pipeData.threeCorss=c;break}})},v=async()=>{var o,p;t.layerIds=q(y.view);const r=await Q(t.layerIds);t.layerInfos=((p=(o=r.data)==null?void 0:o.result)==null?void 0:p.rows)||[]},I=async()=>{await v(),await g()},D=M(),L=async()=>{const r=await D.getAllStationOption();t.deviceData.waterQuality=r.filter(o=>o.data.type===m.SHUIZHIJIANCEZHAN).length,t.deviceData.pressure=r.filter(o=>[m.YALIJIANCEZHAN,m.CHELIUYAZHAN].indexOf(o.data.type)!==-1).length,t.deviceData.bigUser=r.filter(o=>o.data.type===m.DAYONGHU).length,t.deviceData.secondary=r.filter(o=>o.data.type===m.BENGZHAN).length,t.deviceData.flow=r.filter(o=>[m.CHELIUYAZHAN,m.LIULIANGJIANCEZHAN].indexOf(o.data.type)!==-1).length},b=r=>{y.view=r};return T(async()=>{L()}),O(()=>{}),(r,o)=>{const p=C,d=A;return _(),u("div",z,[l("div",W,[e(d,{"map-config":n(w),onLoaded:b},{default:i(()=>[e(p,{onPipeLoaded:I})]),_:1},8,["map-config"])]),l("div",$,[e(N,{"devie-data":n(t).deviceData,class:"device-group"},null,8,["devie-data"]),l("div",j,[e(S,{title:"漏损指标",type:"simple","title-width":180,style:{"border-radius":"18px 0 0 18px"}}),l("div",tt,[(_(!0),u(R,null,V(n(t).lossRates,(f,a)=>(_(),Y(H,{key:a,data:f,class:"loss-rate-items__item"},null,8,["data"]))),128))])]),l("div",et,[e(s,{class:"toprow-right__item",title:"设备统计"},{default:i(()=>[e(k,{"pipe-data":n(t).pipeData},null,8,["pipe-data"])]),_:1}),e(s,{class:"toprow-right__item",title:"产销差分析"},{default:i(()=>[e(E)]),_:1}),e(s,{class:"toprow-right__item",title:"供水管网口径统计","title-width":240},{default:i(()=>[e(J,{layerids:n(t).layerIds,layerinfos:n(t).layerInfos},null,8,["layerids","layerinfos"])]),_:1}),e(s,{class:"toprow-right__item",title:"分区统计"},{default:i(()=>[e(x)]),_:1})])]),l("div",ot,[e(s,{class:"bottomrow-item bottomrow-left",title:"流量监测"},{default:i(()=>[e(B)]),_:1}),e(s,{class:"bottomrow-item bottomrow-center",title:"压力监测"},{default:i(()=>[e(P)]),_:1}),e(s,{class:"bottomrow-item bottomrow-right",title:"漏损排行"},{default:i(()=>[e(U)]),_:1})])])}}}),Xe=X(rt,[["__scopeId","data-v-87dbfbad"]]);export{Xe as default};
