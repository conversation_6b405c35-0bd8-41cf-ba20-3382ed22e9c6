function s(i,r,e){r.trim()!==""?e():e(new Error("请输入有效字符 空格无效"))}function n(i,r,e){/^[0-9]*$/.test(r)?e():e(new Error("只能输入数字"))}function o(i,r,e){/^((25[0-5]|2[0-4]\\d|[1]{1}\\d{1}\\d{1}|[1-9]{1}\\d{1}|\\d{1})($|(?!\\.$)\\.)){4}$/.test(r)?e():e(new Error("请输入正确的ip地址"))}function u(i,r,e){/^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{4,30}$/.test(r)?e():e(new Error("请输入数字和英文的组合"))}const g={text:[{required:!0,message:"请输入信息",trigger:"blur"},{min:2,max:18,message:"输入字符长度在2到18之间",trigger:"blur"},{validator:s,trigger:"blur"}],port:[{required:!0,message:"请输入tcp端口号",trigger:"blur"},{validator:n,trigger:"blur"}],portName:[{required:!0,message:"请输入RTU串口名",trigger:"blur"},{validator:u,trigger:"blur"}],host:[{required:!0,message:"请输入TCP 地址",trigger:"blur"},{validator:o,trigger:"blur"}]},d=(i,r,e)=>{r===""||r===null||/^1\d{10}$/.test(r)?e():e(new Error("请输入正确手机号"))},f=(i,r,e)=>{r===""||r===null||/^[1-9][0-9]{5}$/.test(r)?e():e(new Error("请输入正确邮政编码"))},l=(i,r,e)=>{const t=/^1[1234567890]\d{9}$$/;if(!r)return e(new Error("电话号码不能为空"));setTimeout(()=>{Number.isInteger(+r)?t.test(r)?e():e(new Error("电话号码格式不正确")):e(new Error("请输入数字值"))},100)},m=(i,r,e)=>{const t=/^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+(.[a-zA-Z0-9_-])+/;if(!r)return e(new Error("邮箱不能为空"));setTimeout(()=>{t.test(r)?e():e(new Error("请输入正确的邮箱格式"))},100)};export{l as a,m as b,g as c,f as p,d as v};
