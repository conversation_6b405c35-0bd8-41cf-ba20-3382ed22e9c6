"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[1227,5159],{3920:(e,t,r)=>{r.d(t,{p:()=>p,r:()=>u});var o=r(43697),s=r(15923),i=r(61247),n=r(5600),a=r(52011),l=r(72762);const p=e=>{let t=class extends e{destroy(){this.destroyed||(this._get("handles")?.destroy(),this._get("updatingHandles")?.destroy())}get handles(){return this._get("handles")||new i.Z}get updatingHandles(){return this._get("updatingHandles")||new l.t}};return(0,o._)([(0,n.Cb)({readOnly:!0})],t.prototype,"handles",null),(0,o._)([(0,n.Cb)({readOnly:!0})],t.prototype,"updatingHandles",null),t=(0,o._)([(0,a.j)("esri.core.HandleOwner")],t),t};let u=class extends(p(s.Z)){};u=(0,o._)([(0,a.j)("esri.core.HandleOwner")],u)},42033:(e,t,r)=>{r.d(t,{E:()=>s,_:()=>i});var o=r(70586);async function s(e,t){const{WhereClause:o}=await r.e(1534).then(r.bind(r,41534));return o.create(e,t)}function i(e,t){return(0,o.pC)(e)?(0,o.pC)(t)?`(${e}) AND (${t})`:e:t}},72762:(e,t,r)=>{r.d(t,{t:()=>y});var o=r(43697),s=r(15923),i=r(61247),n=r(70586),a=r(17445),l=r(1654),p=r(5600),u=r(52011);let y=class extends s.Z{constructor(){super(...arguments),this.updating=!1,this._handleId=0,this._handles=new i.Z,this._scheduleHandleId=0,this._pendingPromises=new Set}destroy(){this.removeAll(),this._handles.destroy()}add(e,t,r={}){return this._installWatch(e,t,r,a.YP)}addWhen(e,t,r={}){return this._installWatch(e,t,r,a.gx)}addOnCollectionChange(e,t,{initial:r=!1,final:o=!1}={}){const s=++this._handleId;return this._handles.add([(0,a.on)(e,"after-changes",this._createSyncUpdatingCallback(),a.Z_),(0,a.on)(e,"change",t,{onListenerAdd:r?e=>t({added:e.toArray(),removed:[]}):void 0,onListenerRemove:o?e=>t({added:[],removed:e.toArray()}):void 0})],s),{remove:()=>this._handles.remove(s)}}addPromise(e){if((0,n.Wi)(e))return e;const t=++this._handleId;this._handles.add({remove:()=>{this._pendingPromises.delete(e)&&(0!==this._pendingPromises.size||this._handles.has(d)||this._set("updating",!1))}},t),this._pendingPromises.add(e),this._set("updating",!0);const r=()=>this._handles.remove(t);return e.then(r,r),e}removeAll(){this._pendingPromises.clear(),this._handles.removeAll(),this._set("updating",!1)}_installWatch(e,t,r={},o){const s=++this._handleId;r.sync||this._installSyncUpdatingWatch(e,s);const i=o(e,t,r);return this._handles.add(i,s),{remove:()=>this._handles.remove(s)}}_installSyncUpdatingWatch(e,t){const r=this._createSyncUpdatingCallback(),o=(0,a.YP)(e,r,{sync:!0,equals:()=>!1});return this._handles.add(o,t),o}_createSyncUpdatingCallback(){return()=>{this._handles.remove(d),++this._scheduleHandleId;const e=this._scheduleHandleId;this._get("updating")||this._set("updating",!0),this._handles.add((0,l.Os)((()=>{e===this._scheduleHandleId&&(this._set("updating",this._pendingPromises.size>0),this._handles.remove(d))})),d)}}};(0,o._)([(0,p.Cb)({readOnly:!0})],y.prototype,"updating",void 0),y=(0,o._)([(0,u.j)("esri.core.support.WatchUpdatingTracking")],y);const d=-42},16306:(e,t,r)=>{r.d(t,{aX:()=>v});var o=r(68773),s=r(20102),i=r(92604),n=r(70586),a=r(38913),l=r(58901),p=r(73913),u=r(8744),y=r(40488),d=(r(66577),r(3172)),c=r(33955),h=r(11282),f=r(17452);async function m(e,t,r){const o="string"==typeof e?(0,f.mN)(e):e,s=t[0].spatialReference,i=(0,c.Ji)(t[0]),n={...r,query:{...o.query,f:"json",sr:s.wkid?s.wkid:JSON.stringify(s),geometries:JSON.stringify((l=t,{geometryType:(0,c.Ji)(l[0]),geometries:l.map((e=>e.toJSON()))}))}},{data:a}=await(0,d.default)(o.path+"/simplify",n);var l;return function(e,t,r){const o=(0,c.q9)(t);return e.map((e=>{const t=o.fromJSON(e);return t.spatialReference=r,t}))}(a.geometries,i,s)}const g=i.Z.getLogger("esri.geometry.support.normalizeUtils");function S(e){return"polygon"===e[0].type}function w(e){return"polyline"===e[0].type}function b(e,t,r){if(t){const t=function(e,t){if(!(e instanceof l.Z||e instanceof a.Z)){const e="straightLineDensify: the input geometry is neither polyline nor polygon";throw g.error(e),new s.Z(e)}const r=(0,p.x3)(e),o=[];for(const e of r){const r=[];o.push(r),r.push([e[0][0],e[0][1]]);for(let o=0;o<e.length-1;o++){const s=e[o][0],i=e[o][1],n=e[o+1][0],a=e[o+1][1],l=Math.sqrt((n-s)*(n-s)+(a-i)*(a-i)),p=(a-i)/l,u=(n-s)/l,y=l/t;if(y>1){for(let e=1;e<=y-1;e++){const o=e*t,n=u*o+s,a=p*o+i;r.push([n,a])}const e=(l+Math.floor(y-1)*t)/2,o=u*e+s,n=p*e+i;r.push([o,n])}r.push([n,a])}}return function(e){return"polygon"===e.type}(e)?new a.Z({rings:o,spatialReference:e.spatialReference}):new l.Z({paths:o,spatialReference:e.spatialReference})}(e,1e6);e=(0,y.Sx)(t,!0)}return r&&(e=(0,p.Sy)(e,r)),e}function _(e,t,r){if(Array.isArray(e)){const o=e[0];if(o>t){const r=(0,p.XZ)(o,t);e[0]=o+r*(-2*t)}else if(o<r){const t=(0,p.XZ)(o,r);e[0]=o+t*(-2*r)}}else{const o=e.x;if(o>t){const r=(0,p.XZ)(o,t);e=e.clone().offset(r*(-2*t),0)}else if(o<r){const t=(0,p.XZ)(o,r);e=e.clone().offset(t*(-2*r),0)}}return e}function C(e,t){let r=-1;for(let o=0;o<t.cutIndexes.length;o++){const s=t.cutIndexes[o],i=t.geometries[o],n=(0,p.x3)(i);for(let e=0;e<n.length;e++){const t=n[e];t.some((r=>{if(r[0]<180)return!0;{let r=0;for(let e=0;e<t.length;e++){const o=t[e][0];r=o>r?o:r}r=Number(r.toFixed(9));const o=-360*(0,p.XZ)(r,180);for(let r=0;r<t.length;r++){const t=i.getPoint(e,r);i.setPoint(e,r,t.clone().offset(o,0))}return!0}}))}if(s===r){if(S(e))for(const t of(0,p.x3)(i))e[s]=e[s].addRing(t);else if(w(e))for(const t of(0,p.x3)(i))e[s]=e[s].addPath(t)}else r=s,e[s]=i}return e}async function v(e,t,r){if(!Array.isArray(e))return v([e],t);t&&"string"!=typeof t&&g.warn("normalizeCentralMeridian()","The url object is deprecated, use the url string instead");const s="string"==typeof t?t:t?.url??o.Z.geometryServiceUrl;let i,f,S,w,x,O,T,R,N=0;const Z=[],j=[];for(const t of e)if((0,n.Wi)(t))j.push(t);else if(i||(i=t.spatialReference,f=(0,u.C5)(i),S=i.isWebMercator,O=S?102100:4326,w=p.UZ[O].maxX,x=p.UZ[O].minX,T=p.UZ[O].plus180Line,R=p.UZ[O].minus180Line),f)if("mesh"===t.type)j.push(t);else if("point"===t.type)j.push(_(t.clone(),w,x));else if("multipoint"===t.type){const e=t.clone();e.points=e.points.map((e=>_(e,w,x))),j.push(e)}else if("extent"===t.type){const e=t.clone()._normalize(!1,!1,f);j.push(e.rings?new a.Z(e):e)}else if(t.extent){const e=t.extent,r=(0,p.XZ)(e.xmin,x)*(2*w);let o=0===r?t.clone():(0,p.Sy)(t.clone(),r);e.offset(r,0),e.intersects(T)&&e.xmax!==w?(N=e.xmax>N?e.xmax:N,o=b(o,S),Z.push(o),j.push("cut")):e.intersects(R)&&e.xmin!==x?(N=e.xmax*(2*w)>N?e.xmax*(2*w):N,o=b(o,S,360),Z.push(o),j.push("cut")):j.push(o)}else j.push(t.clone());else j.push(t);let M=(0,p.XZ)(N,w),A=-90;const F=M,L=new l.Z;for(;M>0;){const e=360*M-180;L.addPath([[e,A],[e,-1*A]]),A*=-1,M--}if(Z.length>0&&F>0){const t=C(Z,await async function(e,t,r,o){const s=(0,h.en)(e),i=t[0].spatialReference,n={...o,query:{...s.query,f:"json",sr:JSON.stringify(i),target:JSON.stringify({geometryType:(0,c.Ji)(t[0]),geometries:t}),cutter:JSON.stringify(r)}},a=await(0,d.default)(s.path+"/cut",n),{cutIndexes:l,geometries:p=[]}=a.data;return{cutIndexes:l,geometries:p.map((e=>{const t=(0,c.im)(e);return t.spatialReference=i,t}))}}(s,Z,L,r)),o=[],i=[];for(let r=0;r<j.length;r++){const s=j[r];if("cut"!==s)i.push(s);else{const s=t.shift(),a=e[r];(0,n.pC)(a)&&"polygon"===a.type&&a.rings&&a.rings.length>1&&s.rings.length>=a.rings.length?(o.push(s),i.push("simplify")):i.push(S?(0,y.$)(s):s)}}if(!o.length)return i;const a=await m(s,o,r),l=[];for(let e=0;e<i.length;e++){const t=i[e];"simplify"!==t?l.push(t):l.push(S?(0,y.$)(a.shift()):a.shift())}return l}const I=[];for(let e=0;e<j.length;e++){const t=j[e];if("cut"!==t)I.push(t);else{const e=Z.shift();I.push(!0===S?(0,y.$)(e):e)}}return I}},73913:(e,t,r)=>{r.d(t,{Sy:()=>l,UZ:()=>n,XZ:()=>a,x3:()=>p});var o=r(58901),s=r(82971),i=r(33955);const n={102100:{maxX:20037508.342788905,minX:-20037508.342788905,plus180Line:new o.Z({paths:[[[20037508.342788905,-20037508.342788905],[20037508.342788905,20037508.342788905]]],spatialReference:s.Z.WebMercator}),minus180Line:new o.Z({paths:[[[-20037508.342788905,-20037508.342788905],[-20037508.342788905,20037508.342788905]]],spatialReference:s.Z.WebMercator})},4326:{maxX:180,minX:-180,plus180Line:new o.Z({paths:[[[180,-180],[180,180]]],spatialReference:s.Z.WGS84}),minus180Line:new o.Z({paths:[[[-180,-180],[-180,180]]],spatialReference:s.Z.WGS84})}};function a(e,t){return Math.ceil((e-t)/(2*t))}function l(e,t){const r=p(e);for(const e of r)for(const r of e)r[0]+=t;return e}function p(e){return(0,i.oU)(e)?e.rings:e.paths}},62128:(e,t,r)=>{r.r(t),r.d(t,{default:()=>Y});var o=r(43697),s=(r(66577),r(38171)),i=(r(9790),r(46791)),n=r(20102),a=r(22974),l=r(70586),p=r(16453),u=r(78286),y=r(20941),d=r(5600),c=r(75215),h=r(71715),f=r(52011),m=r(30556),g=r(44547),S=r(24470),w=r(16306),b=r(8744),_=r(19238),C=(r(67676),r(70921)),v=r(3920);r(80442),r(92604);let x=class extends((0,v.p)(i.Z)){constructor(e){super(e),this.handles.add([this.on("before-add",(e=>{(0,l.Wi)(e.item)&&e.preventDefault()})),this.on("after-add",(e=>this._own(e.item))),this.on("after-remove",(e=>this._release(e.item)))])}get owner(){return this._get("owner")}set owner(e){e!==this._get("owner")&&(this._releaseAll(),this._set("owner",e),this._ownAll())}_ownAll(){for(const e of this.items)this._own(e)}_releaseAll(){for(const e of this.items)this._release(e)}_createNewInstance(e){return this.itemType?new(i.Z.ofType(this.itemType.Type))(e):new i.Z(e)}};(0,o._)([(0,d.Cb)()],x.prototype,"owner",null),x=(0,o._)([(0,f.j)("esri.core.support.OwningCollection")],x);var O=r(87085),T=r(71612),R=r(72965),N=r(52421);let Z=class extends x{_own(e){e.layer&&"remove"in e.layer&&e.layer!==this.owner&&e.layer.remove(e),e.layer=this.owner}_release(e){e.layer===this.owner&&(e.layer=null)}};(0,o._)([(0,N.c)({Type:s.Z,ensureType:(0,c.se)(s.Z)})],Z.prototype,"itemType",void 0),Z=(0,o._)([(0,f.j)("esri.support.GraphicsCollection")],Z);var j=r(86787);let M=class extends((0,T.h)((0,R.M)(O.Z))){constructor(e){super(e),this.elevationInfo=null,this.graphics=new Z,this.screenSizePerspectiveEnabled=!0,this.type="graphics",this.internal=!1}destroy(){this.removeAll(),this.graphics.destroy()}add(e){return this.graphics.add(e),this}addMany(e){return this.graphics.addMany(e),this}removeAll(){return this.graphics.removeAll(),this}remove(e){this.graphics.remove(e)}removeMany(e){this.graphics.removeMany(e)}on(e,t){return super.on(e,t)}graphicChanged(e){this.emit("graphic-update",e)}};var A,F;(0,o._)([(0,d.Cb)({type:j.Z})],M.prototype,"elevationInfo",void 0),(0,o._)([(0,d.Cb)((A=Z,F="graphics",{type:A,cast:C.R,set(e){const t=(0,C.Z)(e,this._get(F),A);t.owner=this,this._set(F,t)}}))],M.prototype,"graphics",void 0),(0,o._)([(0,d.Cb)({type:["show","hide"]})],M.prototype,"listMode",void 0),(0,o._)([(0,d.Cb)()],M.prototype,"screenSizePerspectiveEnabled",void 0),(0,o._)([(0,d.Cb)({readOnly:!0})],M.prototype,"type",void 0),(0,o._)([(0,d.Cb)({constructOnly:!0})],M.prototype,"internal",void 0),M=(0,o._)([(0,f.j)("esri.layers.GraphicsLayer")],M);const L=M;var I=r(92908),E=r(38009),P=r(16859),J=r(1231),z=r(20256),D=r(4095),W=r(77987),G=r(78724),Q=r(82971),q=r(6570);function k(e){return"markup"===e.featureCollectionType||e.layers.some((e=>null!=e.layerDefinition.visibilityField||!U(e)))}function U({layerDefinition:e,featureSet:t}){const r=e.geometryType??t.geometryType;return K.find((t=>r===t.geometryTypeJSON&&e.drawingInfo?.renderer?.symbol?.type===t.identifyingSymbol.type))}function H(){return new q.Z({xmin:-180,ymin:-90,xmax:180,ymax:90})}const B=new J.Z({name:"OBJECTID",alias:"OBJECTID",type:"oid",nullable:!1,editable:!1}),X=new J.Z({name:"title",alias:"Title",type:"string",nullable:!0,editable:!0});let V=class extends L{constructor(e){super(e),this.visibilityMode="inherited"}initialize(){for(const e of this.graphics)e.sourceLayer=this.layer;this.graphics.on("after-add",(e=>{e.item.sourceLayer=this.layer})),this.graphics.on("after-remove",(e=>{e.item.sourceLayer=null}))}get fullExtent(){const e=this.layer?.spatialReference,t=this.fullBounds;return e?(0,l.Wi)(t)?(0,g.dz)(H(),e).geometry:(0,S.HH)(t,e):null}get fullBounds(){const e=this.layer?.spatialReference;if(!e)return null;const t=(0,S.cS)();return this.graphics.forEach((r=>{const o=(0,l.pC)(r.geometry)?(0,g.dz)(r.geometry,e).geometry:null;(0,l.pC)(o)&&(0,S.jn)(t,"point"===o.type?o:o.extent,t)})),(0,S.fS)(t,S.Gv)?null:t}get sublayers(){return this.graphics}};(0,o._)([(0,d.Cb)({readOnly:!0})],V.prototype,"fullExtent",null),(0,o._)([(0,d.Cb)({readOnly:!0})],V.prototype,"fullBounds",null),(0,o._)([(0,d.Cb)({readOnly:!0})],V.prototype,"sublayers",null),(0,o._)([(0,d.Cb)()],V.prototype,"layer",void 0),(0,o._)([(0,d.Cb)()],V.prototype,"layerId",void 0),(0,o._)([(0,d.Cb)({readOnly:!0})],V.prototype,"visibilityMode",void 0),V=(0,o._)([(0,f.j)("esri.layers.MapNotesLayer.MapNotesSublayer")],V);const K=[{geometryType:"polygon",geometryTypeJSON:"esriGeometryPolygon",id:"polygonLayer",layerId:0,title:"Polygons",identifyingSymbol:(new z.Z).toJSON()},{geometryType:"polyline",geometryTypeJSON:"esriGeometryPolyline",id:"polylineLayer",layerId:1,title:"Polylines",identifyingSymbol:(new D.Z).toJSON()},{geometryType:"multipoint",geometryTypeJSON:"esriGeometryMultipoint",id:"multipointLayer",layerId:2,title:"Multipoints",identifyingSymbol:(new W.Z).toJSON()},{geometryType:"point",geometryTypeJSON:"esriGeometryPoint",id:"pointLayer",layerId:3,title:"Points",identifyingSymbol:(new W.Z).toJSON()},{geometryType:"point",geometryTypeJSON:"esriGeometryPoint",id:"textLayer",layerId:4,title:"Text",identifyingSymbol:(new G.Z).toJSON()}];let $=class extends((0,T.h)((0,R.M)((0,E.q)((0,P.I)((0,p.R)(O.Z)))))){constructor(e){super(e),this.capabilities={operations:{supportsMapNotesEditing:!0}},this.featureCollections=null,this.featureCollectionJSON=null,this.featureCollectionType="notes",this.legendEnabled=!1,this.listMode="hide-children",this.minScale=0,this.maxScale=0,this.spatialReference=Q.Z.WGS84,this.sublayers=new i.Z(K.map((e=>new V({id:e.id,layerId:e.layerId,title:e.title,layer:this})))),this.title="Map Notes",this.type="map-notes",this.visibilityMode="inherited"}readCapabilities(e,t,r){return{operations:{supportsMapNotesEditing:!k(t)&&"portal-item"!==r?.origin}}}readFeatureCollections(e,t,r){if(!k(t))return null;const o=t.layers.map((e=>{const t=new _.default;return t.read(e,r),t}));return new i.Z({items:o})}readLegacyfeatureCollectionJSON(e,t){return k(t)?(0,a.d9)(t.featureCollection):null}get fullExtent(){const e=this.spatialReference,t=(0,S.cS)();return(0,l.pC)(this.sublayers)?this.sublayers.forEach((({fullBounds:e})=>(0,l.pC)(e)?(0,S.jn)(t,e,t):t),t):this.featureCollectionJSON?.layers.some((e=>e.layerDefinition.extent))&&this.featureCollectionJSON.layers.forEach((r=>{const o=(0,g.dz)(r.layerDefinition.extent,e).geometry;(0,l.pC)(o)&&(0,S.jn)(t,o,t)})),(0,S.fS)(t,S.Gv)?(0,g.dz)(H(),e).geometry:(0,S.HH)(t,e)}readMinScale(e,t){for(const e of t.layers)if(null!=e.layerDefinition.minScale)return e.layerDefinition.minScale;return 0}readMaxScale(e,t){for(const e of t.layers)if(null!=e.layerDefinition.maxScale)return e.layerDefinition.maxScale;return 0}get multipointLayer(){return this._findSublayer("multipointLayer")}get pointLayer(){return this._findSublayer("pointLayer")}get polygonLayer(){return this._findSublayer("polygonLayer")}get polylineLayer(){return this._findSublayer("polylineLayer")}readSpatialReference(e,t){return t.layers.length?Q.Z.fromJSON(t.layers[0].layerDefinition.spatialReference):Q.Z.WGS84}readSublayers(e,t,r){if(k(t))return null;const o=[];let n=t.layers.reduce(((e,t)=>Math.max(e,t.layerDefinition.id??-1)),-1)+1;for(const e of t.layers){const{layerDefinition:t,featureSet:r}=e,i=t.id??n++,a=U(e);if((0,l.pC)(a)){const e=new V({id:a.id,title:t.name,layerId:i,layer:this,graphics:r.features.map((({geometry:e,symbol:t,attributes:r,popupInfo:o})=>s.Z.fromJSON({attributes:r,geometry:e,symbol:t,popupTemplate:o})))});o.push(e)}}return new i.Z(o)}writeSublayers(e,t,r,o){const{minScale:s,maxScale:i}=this;if((0,l.Wi)(e))return;const a=e.some((e=>e.graphics.length>0));if(!this.capabilities.operations.supportsMapNotesEditing)return void(a&&o?.messages?.push(new n.Z("map-notes-layer:editing-not-supported","New map notes cannot be added to this layer")));const p=[];let y=this.spatialReference.toJSON();e:for(const t of e)for(const e of t.graphics)if((0,l.pC)(e.geometry)){y=e.geometry.spatialReference.toJSON();break e}for(const t of K){const r=e.find((e=>t.id===e.id));this._writeMapNoteSublayer(p,r,t,s,i,y,o)}(0,u.RB)("featureCollection.layers",p,t)}get textLayer(){return this._findSublayer("textLayer")}load(e){return this.addResolvingPromise(this.loadFromPortal({supportedTypes:["Feature Collection"]},e)),Promise.resolve(this)}read(e,t){"featureCollection"in e&&(e=(0,a.d9)(e),Object.assign(e,e.featureCollection)),super.read(e,t)}async beforeSave(){if((0,l.Wi)(this.sublayers))return;let e=null;const t=[];for(const r of this.sublayers)for(const o of r.graphics)if((0,l.pC)(o.geometry)){const r=o.geometry;e?(0,b.fS)(r.spatialReference,e)||((0,g.Up)(r.spatialReference,e)||(0,g.kR)()||await(0,g.zD)(),o.geometry=(0,g.iV)(r,e)):e=r.spatialReference,t.push(o)}const r=await(0,w.aX)(t.map((e=>e.geometry)));t.forEach(((e,t)=>e.geometry=r[t]))}_findSublayer(e){return(0,l.Wi)(this.sublayers)?null:this.sublayers?.find((t=>t.id===e))??null}_writeMapNoteSublayer(e,t,r,o,s,i,n){const p=[];if(!(0,l.Wi)(t)){for(const e of t.graphics)this._writeMapNote(p,e,r.geometryType,n);this._normalizeObjectIds(p,B),e.push({layerDefinition:{name:t.title,drawingInfo:{renderer:{type:"simple",symbol:(0,a.d9)(r.identifyingSymbol)}},id:t.layerId,geometryType:r.geometryTypeJSON,minScale:o,maxScale:s,objectIdField:"OBJECTID",fields:[B.toJSON(),X.toJSON()],spatialReference:i},featureSet:{features:p,geometryType:r.geometryTypeJSON}})}}_writeMapNote(e,t,r,o){if((0,l.Wi)(t))return;const{geometry:s,symbol:i,popupTemplate:n}=t;if((0,l.Wi)(s))return;if(s.type!==r)return void o?.messages?.push(new y.Z("map-notes-layer:invalid-geometry-type",`Geometry "${s.type}" cannot be saved in "${r}" layer`,{graphic:t}));if((0,l.Wi)(i))return void o?.messages?.push(new y.Z("map-notes-layer:no-symbol","Skipping map notes with no symbol",{graphic:t}));const a={attributes:{...t.attributes},geometry:s.toJSON(),symbol:i.toJSON()};(0,l.pC)(n)&&(a.popupInfo=n.toJSON()),e.push(a)}_normalizeObjectIds(e,t){const r=t.name;let o=(0,I.S)(r,e)+1;const s=new Set;for(const t of e){t.attributes||(t.attributes={});const{attributes:e}=t;(null==e[r]||s.has(e[r]))&&(e[r]=o++),s.add(e[r])}}};(0,o._)([(0,d.Cb)({readOnly:!0})],$.prototype,"capabilities",void 0),(0,o._)([(0,h.r)(["portal-item","web-map"],"capabilities",["layers"])],$.prototype,"readCapabilities",null),(0,o._)([(0,d.Cb)({readOnly:!0})],$.prototype,"featureCollections",void 0),(0,o._)([(0,h.r)(["web-map","portal-item"],"featureCollections",["layers"])],$.prototype,"readFeatureCollections",null),(0,o._)([(0,d.Cb)({readOnly:!0,json:{origins:{"web-map":{write:{enabled:!0,target:"featureCollection"}}}}})],$.prototype,"featureCollectionJSON",void 0),(0,o._)([(0,h.r)(["web-map","portal-item"],"featureCollectionJSON",["featureCollection"])],$.prototype,"readLegacyfeatureCollectionJSON",null),(0,o._)([(0,d.Cb)({readOnly:!0,json:{read:!0,write:{enabled:!0,ignoreOrigin:!0}}})],$.prototype,"featureCollectionType",void 0),(0,o._)([(0,d.Cb)({readOnly:!0})],$.prototype,"fullExtent",null),(0,o._)([(0,d.Cb)({readOnly:!0,json:{origins:{"web-map":{write:{target:"featureCollection.showLegend",overridePolicy(){return{enabled:null!=this.featureCollectionJSON}}}}}}})],$.prototype,"legendEnabled",void 0),(0,o._)([(0,d.Cb)({type:["show","hide","hide-children"]})],$.prototype,"listMode",void 0),(0,o._)([(0,d.Cb)({type:Number,nonNullable:!0,json:{write:!1}})],$.prototype,"minScale",void 0),(0,o._)([(0,h.r)(["web-map","portal-item"],"minScale",["layers"])],$.prototype,"readMinScale",null),(0,o._)([(0,d.Cb)({type:Number,nonNullable:!0,json:{write:!1}})],$.prototype,"maxScale",void 0),(0,o._)([(0,h.r)(["web-map","portal-item"],"maxScale",["layers"])],$.prototype,"readMaxScale",null),(0,o._)([(0,d.Cb)({readOnly:!0})],$.prototype,"multipointLayer",null),(0,o._)([(0,d.Cb)({value:"ArcGISFeatureLayer",type:["ArcGISFeatureLayer"]})],$.prototype,"operationalLayerType",void 0),(0,o._)([(0,d.Cb)({readOnly:!0})],$.prototype,"pointLayer",null),(0,o._)([(0,d.Cb)({readOnly:!0})],$.prototype,"polygonLayer",null),(0,o._)([(0,d.Cb)({readOnly:!0})],$.prototype,"polylineLayer",null),(0,o._)([(0,d.Cb)({type:Q.Z})],$.prototype,"spatialReference",void 0),(0,o._)([(0,h.r)(["web-map","portal-item"],"spatialReference",["layers"])],$.prototype,"readSpatialReference",null),(0,o._)([(0,d.Cb)({readOnly:!0,json:{origins:{"web-map":{write:{ignoreOrigin:!0}}}}})],$.prototype,"sublayers",void 0),(0,o._)([(0,h.r)("web-map","sublayers",["layers"])],$.prototype,"readSublayers",null),(0,o._)([(0,m.c)("web-map","sublayers")],$.prototype,"writeSublayers",null),(0,o._)([(0,d.Cb)({readOnly:!0})],$.prototype,"textLayer",null),(0,o._)([(0,d.Cb)()],$.prototype,"title",void 0),(0,o._)([(0,d.Cb)({readOnly:!0,json:{read:!1}})],$.prototype,"type",void 0),$=(0,o._)([(0,f.j)("esri.layers.MapNotesLayer")],$);const Y=$},92908:(e,t,r)=>{r.d(t,{S:()=>s,X:()=>o});const o=1;function s(e,t){let r=0;for(const o of t){const t=o.attributes?.[e];"number"==typeof t&&isFinite(t)&&(r=Math.max(r,t))}return r}},54295:(e,t,r)=>{r.d(t,{V:()=>n});var o=r(43697),s=r(5600),i=(r(75215),r(67676),r(52011));const n=e=>{let t=class extends e{get apiKey(){return this._isOverridden("apiKey")?this._get("apiKey"):"portalItem"in this?this.portalItem?.apiKey:null}set apiKey(e){null!=e?this._override("apiKey",e):(this._clearOverride("apiKey"),this.clear("apiKey","user"))}};return(0,o._)([(0,s.Cb)({type:String})],t.prototype,"apiKey",null),t=(0,o._)([(0,i.j)("esri.layers.mixins.APIKeyMixin")],t),t}},17287:(e,t,r)=>{r.d(t,{Y:()=>p});var o=r(43697),s=r(92604),i=r(70586),n=r(5600),a=(r(75215),r(67676),r(52011)),l=r(66677);const p=e=>{let t=class extends e{get title(){if(this._get("title")&&"defaults"!==this.originOf("title"))return this._get("title");if(this.url){const e=(0,l.Qc)(this.url);if((0,i.pC)(e)&&e.title)return e.title}return this._get("title")||""}set title(e){this._set("title",e)}set url(e){this._set("url",(0,l.Nm)(e,s.Z.getLogger(this.declaredClass)))}};return(0,o._)([(0,n.Cb)()],t.prototype,"title",null),(0,o._)([(0,n.Cb)({type:String})],t.prototype,"url",null),t=(0,o._)([(0,a.j)("esri.layers.mixins.ArcGISService")],t),t}},70082:(e,t,r)=>{r.d(t,{Z:()=>y});var o=r(43697),s=r(2368),i=r(35454),n=r(96674),a=r(5600),l=(r(75215),r(67676),r(52011));const p=new i.X({esriFeatureEditToolAutoCompletePolygon:"auto-complete-polygon",esriFeatureEditToolCircle:"circle",esriFeatureEditToolEllipse:"ellipse",esriFeatureEditToolFreehand:"freehand",esriFeatureEditToolLine:"line",esriFeatureEditToolNone:"none",esriFeatureEditToolPoint:"point",esriFeatureEditToolPolygon:"polygon",esriFeatureEditToolRectangle:"rectangle",esriFeatureEditToolArrow:"arrow",esriFeatureEditToolTriangle:"triangle",esriFeatureEditToolLeftArrow:"left-arrow",esriFeatureEditToolRightArrow:"right-arrow",esriFeatureEditToolUpArrow:"up-arrow",esriFeatureEditToolDownArrow:"down-arrow"});let u=class extends((0,s.J)(n.wq)){constructor(e){super(e),this.name=null,this.description=null,this.drawingTool=null,this.prototype=null,this.thumbnail=null}};(0,o._)([(0,a.Cb)({json:{write:!0}})],u.prototype,"name",void 0),(0,o._)([(0,a.Cb)({json:{write:!0}})],u.prototype,"description",void 0),(0,o._)([(0,a.Cb)({json:{read:p.read,write:p.write}})],u.prototype,"drawingTool",void 0),(0,o._)([(0,a.Cb)({json:{write:!0}})],u.prototype,"prototype",void 0),(0,o._)([(0,a.Cb)({json:{write:!0}})],u.prototype,"thumbnail",void 0),u=(0,o._)([(0,l.j)("esri.layers.support.FeatureTemplate")],u);const y=u},16451:(e,t,r)=>{r.d(t,{Z:()=>c});var o=r(43697),s=r(2368),i=r(96674),n=r(5600),a=(r(75215),r(67676),r(71715)),l=r(52011),p=r(30556),u=r(72729),y=r(70082);let d=class extends((0,s.J)(i.wq)){constructor(e){super(e),this.id=null,this.name=null,this.domains=null,this.templates=null}readDomains(e){const t={};for(const r of Object.keys(e))t[r]=(0,u.im)(e[r]);return t}writeDomains(e,t){const r={};for(const t of Object.keys(e))e[t]&&(r[t]=e[t]?.toJSON());t.domains=r}};(0,o._)([(0,n.Cb)({json:{write:!0}})],d.prototype,"id",void 0),(0,o._)([(0,n.Cb)({json:{write:!0}})],d.prototype,"name",void 0),(0,o._)([(0,n.Cb)({json:{write:!0}})],d.prototype,"domains",void 0),(0,o._)([(0,a.r)("domains")],d.prototype,"readDomains",null),(0,o._)([(0,p.c)("domains")],d.prototype,"writeDomains",null),(0,o._)([(0,n.Cb)({type:[y.Z],json:{write:!0}})],d.prototype,"templates",void 0),d=(0,o._)([(0,l.j)("esri.layers.support.FeatureType")],d);const c=d},56765:(e,t,r)=>{r.d(t,{Z:()=>u});var o,s=r(43697),i=r(46791),n=r(96674),a=r(5600),l=(r(75215),r(67676),r(52011));let p=o=class extends n.wq{constructor(e){super(e),this.floorField=null,this.viewAllMode=!1,this.viewAllLevelIds=new i.Z}clone(){return new o({floorField:this.floorField,viewAllMode:this.viewAllMode,viewAllLevelIds:this.viewAllLevelIds})}};(0,s._)([(0,a.Cb)({type:String,json:{write:!0}})],p.prototype,"floorField",void 0),(0,s._)([(0,a.Cb)({json:{read:!1,write:!1}})],p.prototype,"viewAllMode",void 0),(0,s._)([(0,a.Cb)({json:{read:!1,write:!1}})],p.prototype,"viewAllLevelIds",void 0),p=o=(0,s._)([(0,l.j)("esri.layers.support.LayerFloorInfo")],p);const u=p},72064:(e,t,r)=>{r.d(t,{h:()=>y});var o=r(80442),s=r(70586),i=r(66677);const n={name:"supportsName",size:"supportsSize",contentType:"supportsContentType",keywords:"supportsKeywords",exifInfo:"supportsExifInfo"};function a(e,t,r){return!!(e&&e.hasOwnProperty(t)?e[t]:r)}function l(e,t,r){return e&&e.hasOwnProperty(t)?e[t]:r}function p(e){const t=e?.supportedSpatialAggregationStatistics?.map((e=>e.toLowerCase()));return{envelope:!!t?.includes("envelopeaggregate"),centroid:!!t?.includes("centroidaggregate"),convexHull:!!t?.includes("convexhullaggregate")}}function u(e,t){const r=e?.supportedOperationsWithCacheHint?.map((e=>e.toLowerCase()));return!!r?.includes(t.toLowerCase())}function y(e,t){return{analytics:d(e),attachment:c(e),data:h(e),metadata:f(e),operations:m(e.capabilities,e,t),query:g(e,t),queryRelated:S(e),queryTopFeatures:w(e),editing:b(e)}}function d(e){return{supportsCacheHint:u(e.advancedQueryCapabilities,"queryAnalytics")}}function c(e){const t=e.attachmentProperties,r={supportsName:!1,supportsSize:!1,supportsContentType:!1,supportsKeywords:!1,supportsExifInfo:!1,supportsCacheHint:u(e.advancedQueryCapabilities,"queryAttachments"),supportsResize:a(e,"supportsAttachmentsResizing",!1)};return t&&Array.isArray(t)&&t.forEach((e=>{const t=n[e.name];t&&(r[t]=!!e.isEnabled)})),r}function h(e){return{isVersioned:a(e,"isDataVersioned",!1),supportsAttachment:a(e,"hasAttachments",!1),supportsM:a(e,"hasM",!1),supportsZ:a(e,"hasZ",!1)}}function f(e){return{supportsAdvancedFieldProperties:a(e,"supportsFieldDescriptionProperty",!1)}}function m(e,t,r){const o=e?e.toLowerCase().split(",").map((e=>e.trim())):[],n=r?(0,i.Qc)(r):null,l=o.includes((0,s.pC)(n)&&"MapServer"===n.serverType?"data":"query"),p=o.includes("editing")&&!t.datesInUnknownTimezone;let u=p&&o.includes("create"),y=p&&o.includes("delete"),d=p&&o.includes("update");const c=o.includes("changetracking"),h=t.advancedQueryCapabilities;return p&&!(u||y||d)&&(u=y=d=!0),{supportsCalculate:a(t,"supportsCalculate",!1),supportsTruncate:a(t,"supportsTruncate",!1),supportsValidateSql:a(t,"supportsValidateSql",!1),supportsAdd:u,supportsDelete:y,supportsEditing:p,supportsChangeTracking:c,supportsQuery:l,supportsQueryAnalytics:a(h,"supportsQueryAnalytic",!1),supportsQueryAttachments:a(h,"supportsQueryAttachments",!1),supportsQueryTopFeatures:a(h,"supportsTopFeaturesQuery",!1),supportsResizeAttachments:a(t,"supportsAttachmentsResizing",!1),supportsSync:o.includes("sync"),supportsUpdate:d,supportsExceedsLimitStatistics:a(t,"supportsExceedsLimitStatistics",!1)}}function g(e,t){const r=e.advancedQueryCapabilities,s=e.ownershipBasedAccessControlForFeatures,n=e.archivingInfo,y=e.currentVersion,d=t?.includes("MapServer"),c=!d||y>=(0,o.Z)("mapserver-pbf-version-support"),h=(0,i.M8)(t),f=new Set((e.supportedQueryFormats??"").split(",").map((e=>e.toLowerCase().trim())));return{supportsStatistics:a(r,"supportsStatistics",e.supportsStatistics),supportsPercentileStatistics:a(r,"supportsPercentileStatistics",!1),supportsSpatialAggregationStatistics:a(r,"supportsSpatialAggregationStatistics",!1),supportedSpatialAggregationStatistics:p(r),supportsCentroid:a(r,"supportsReturningGeometryCentroid",!1),supportsDistance:a(r,"supportsQueryWithDistance",!1),supportsDistinct:a(r,"supportsDistinct",e.supportsAdvancedQueries),supportsExtent:a(r,"supportsReturningQueryExtent",!1),supportsGeometryProperties:a(r,"supportsReturningGeometryProperties",!1),supportsHavingClause:a(r,"supportsHavingClause",!1),supportsOrderBy:a(r,"supportsOrderBy",e.supportsAdvancedQueries),supportsPagination:a(r,"supportsPagination",!1),supportsQuantization:a(e,"supportsCoordinatesQuantization",!1),supportsQuantizationEditMode:a(e,"supportsQuantizationEditMode",!1),supportsQueryGeometry:a(e,"supportsReturningQueryGeometry",!1),supportsResultType:a(r,"supportsQueryWithResultType",!1),supportsMaxRecordCountFactor:a(r,"supportsMaxRecordCountFactor",!1),supportsSqlExpression:a(r,"supportsSqlExpression",!1),supportsStandardizedQueriesOnly:a(e,"useStandardizedQueries",!1),supportsTopFeaturesQuery:a(r,"supportsTopFeaturesQuery",!1),supportsQueryByOthers:a(s,"allowOthersToQuery",!0),supportsHistoricMoment:a(n,"supportsQueryWithHistoricMoment",!1),supportsFormatPBF:c&&f.has("pbf"),supportsDisjointSpatialRelationship:a(r,"supportsDisjointSpatialRel",!1),supportsCacheHint:a(r,"supportsQueryWithCacheHint",!1)||u(r,"query"),supportsDefaultSpatialReference:a(r,"supportsDefaultSR",!1),supportsCompactGeometry:h,supportsFullTextSearch:a(r,"supportsFullTextSearch",!1),maxRecordCountFactor:l(e,"maxRecordCountFactor",void 0),maxRecordCount:l(e,"maxRecordCount",void 0),standardMaxRecordCount:l(e,"standardMaxRecordCount",void 0),tileMaxRecordCount:l(e,"tileMaxRecordCount",void 0)}}function S(e){const t=e.advancedQueryCapabilities,r=a(t,"supportsAdvancedQueryRelated",!1);return{supportsPagination:a(t,"supportsQueryRelatedPagination",!1),supportsCount:r,supportsOrderBy:r,supportsCacheHint:u(t,"queryRelated")}}function w(e){return{supportsCacheHint:u(e.advancedQueryCapabilities,"queryTopFilter")}}function b(e){const t=e.ownershipBasedAccessControlForFeatures;return{supportsGeometryUpdate:a(e,"allowGeometryUpdates",!0),supportsGlobalId:a(e,"supportsApplyEditsWithGlobalIds",!1),supportsReturnServiceEditsInSourceSpatialReference:a(e,"supportsReturnServiceEditsInSourceSR",!1),supportsRollbackOnFailure:a(e,"supportsRollbackOnFailureParameter",!1),supportsUpdateWithoutM:a(e,"allowUpdateWithoutMValues",!1),supportsUploadWithItemId:a(e,"supportsAttachmentsByUploadId",!1),supportsDeleteByAnonymous:a(t,"allowAnonymousToDelete",!0),supportsDeleteByOthers:a(t,"allowOthersToDelete",!0),supportsUpdateByAnonymous:a(t,"allowAnonymousToUpdate",!0),supportsUpdateByOthers:a(t,"allowOthersToUpdate",!0)}}},51706:(e,t,r)=>{var o,s;function i(e){return e&&"esri.renderers.visualVariables.SizeVariable"===e.declaredClass}function n(e){return null!=e&&!isNaN(e)&&isFinite(e)}function a(e){return e.valueExpression?o.Expression:e.field&&"string"==typeof e.field?o.Field:o.Unknown}function l(e,t){const r=t||a(e),i=e.valueUnit||"unknown";return r===o.Unknown?s.Constant:e.stops?s.Stops:null!=e.minSize&&null!=e.maxSize&&null!=e.minDataValue&&null!=e.maxDataValue?s.ClampedLinear:"unknown"===i?null!=e.minSize&&null!=e.minDataValue?e.minSize&&e.minDataValue?s.Proportional:s.Additive:s.Identity:s.RealWorldSize}r.d(t,{PS:()=>a,QW:()=>l,RY:()=>o,hL:()=>s,iY:()=>i,qh:()=>n}),function(e){e.Unknown="unknown",e.Expression="expression",e.Field="field"}(o||(o={})),function(e){e.Unknown="unknown",e.Stops="stops",e.ClampedLinear="clamped-linear",e.Proportional="proportional",e.Additive="additive",e.Constant="constant",e.Identity="identity",e.RealWorldSize="real-world-size"}(s||(s={}))},28694:(e,t,r)=>{r.d(t,{p:()=>i});var o=r(70586),s=r(69285);function i(e,t,r){if(!r||!r.features||!r.hasZ)return;const i=(0,s.k)(r.geometryType,t,e.outSpatialReference);if(!(0,o.Wi)(i))for(const e of r.features)i(e.geometry)}},56545:(e,t,r)=>{r.d(t,{Z:()=>d});var o,s=r(43697),i=r(96674),n=r(22974),a=r(5600),l=r(75215),p=r(52011),u=r(30556);let y=o=class extends i.wq{constructor(e){super(e),this.attachmentTypes=null,this.attachmentsWhere=null,this.cacheHint=void 0,this.keywords=null,this.globalIds=null,this.name=null,this.num=null,this.objectIds=null,this.returnMetadata=!1,this.size=null,this.start=null,this.where=null}writeStart(e,t){t.resultOffset=this.start,t.resultRecordCount=this.num||10}clone(){return new o((0,n.d9)({attachmentTypes:this.attachmentTypes,attachmentsWhere:this.attachmentsWhere,cacheHint:this.cacheHint,keywords:this.keywords,where:this.where,globalIds:this.globalIds,name:this.name,num:this.num,objectIds:this.objectIds,returnMetadata:this.returnMetadata,size:this.size,start:this.start}))}};(0,s._)([(0,a.Cb)({type:[String],json:{write:!0}})],y.prototype,"attachmentTypes",void 0),(0,s._)([(0,a.Cb)({type:String,json:{read:{source:"attachmentsDefinitionExpression"},write:{target:"attachmentsDefinitionExpression"}}})],y.prototype,"attachmentsWhere",void 0),(0,s._)([(0,a.Cb)({type:Boolean,json:{write:!0}})],y.prototype,"cacheHint",void 0),(0,s._)([(0,a.Cb)({type:[String],json:{write:!0}})],y.prototype,"keywords",void 0),(0,s._)([(0,a.Cb)({type:[Number],json:{write:!0}})],y.prototype,"globalIds",void 0),(0,s._)([(0,a.Cb)({json:{write:!0}})],y.prototype,"name",void 0),(0,s._)([(0,a.Cb)({type:Number,json:{read:{source:"resultRecordCount"}}})],y.prototype,"num",void 0),(0,s._)([(0,a.Cb)({type:[Number],json:{write:!0}})],y.prototype,"objectIds",void 0),(0,s._)([(0,a.Cb)({type:Boolean,json:{default:!1,write:!0}})],y.prototype,"returnMetadata",void 0),(0,s._)([(0,a.Cb)({type:[Number],json:{write:!0}})],y.prototype,"size",void 0),(0,s._)([(0,a.Cb)({type:Number,json:{read:{source:"resultOffset"}}})],y.prototype,"start",void 0),(0,s._)([(0,u.c)("start"),(0,u.c)("num")],y.prototype,"writeStart",null),(0,s._)([(0,a.Cb)({type:String,json:{read:{source:"definitionExpression"},write:{target:"definitionExpression"}}})],y.prototype,"where",void 0),y=o=(0,s._)([(0,p.j)("esri.rest.support.AttachmentQuery")],y),y.from=(0,l.se)(y);const d=y},74889:(e,t,r)=>{r.d(t,{Z:()=>b});var o,s=r(43697),i=r(66577),n=r(38171),a=r(35454),l=r(96674),p=r(22974),u=r(70586),y=r(5600),d=(r(75215),r(71715)),c=r(52011),h=r(30556),f=r(82971),m=r(33955),g=r(1231);const S=new a.X({esriGeometryPoint:"point",esriGeometryMultipoint:"multipoint",esriGeometryPolyline:"polyline",esriGeometryPolygon:"polygon",esriGeometryEnvelope:"extent",mesh:"mesh","":null});let w=o=class extends l.wq{constructor(e){super(e),this.displayFieldName=null,this.exceededTransferLimit=!1,this.features=[],this.fields=null,this.geometryType=null,this.hasM=!1,this.hasZ=!1,this.queryGeometry=null,this.spatialReference=null}readFeatures(e,t){const r=f.Z.fromJSON(t.spatialReference),o=[];for(let t=0;t<e.length;t++){const s=e[t],i=n.Z.fromJSON(s),a=s.geometry&&s.geometry.spatialReference;(0,u.pC)(i.geometry)&&!a&&(i.geometry.spatialReference=r);const l=s.aggregateGeometries,p=i.aggregateGeometries;if(l&&(0,u.pC)(p))for(const e in p){const t=p[e],o=l[e]?.spatialReference;(0,u.pC)(t)&&!o&&(t.spatialReference=r)}o.push(i)}return o}writeGeometryType(e,t,r,o){if(e)return void S.write(e,t,r,o);const{features:s}=this;if(s)for(const e of s)if(e&&(0,u.pC)(e.geometry))return void S.write(e.geometry.type,t,r,o)}readQueryGeometry(e,t){if(!e)return null;const r=!!e.spatialReference,o=(0,m.im)(e);return o&&!r&&t.spatialReference&&(o.spatialReference=f.Z.fromJSON(t.spatialReference)),o}writeSpatialReference(e,t){if(e)return void(t.spatialReference=e.toJSON());const{features:r}=this;if(r)for(const e of r)if(e&&(0,u.pC)(e.geometry)&&e.geometry.spatialReference)return void(t.spatialReference=e.geometry.spatialReference.toJSON())}clone(){return new o(this.cloneProperties())}cloneProperties(){return(0,p.d9)({displayFieldName:this.displayFieldName,exceededTransferLimit:this.exceededTransferLimit,features:this.features,fields:this.fields,geometryType:this.geometryType,hasM:this.hasM,hasZ:this.hasZ,queryGeometry:this.queryGeometry,spatialReference:this.spatialReference,transform:this.transform})}toJSON(e){const t=this.write();if(t.features&&Array.isArray(e)&&e.length>0)for(let r=0;r<t.features.length;r++){const o=t.features[r];if(o.geometry){const t=e&&e[r];o.geometry=t&&t.toJSON()||o.geometry}}return t}quantize(e){const{scale:[t,r],translate:[o,s]}=e,i=this.features,n=this._getQuantizationFunction(this.geometryType,(e=>Math.round((e-o)/t)),(e=>Math.round((s-e)/r)));for(let e=0,t=i.length;e<t;e++)n?.((0,u.Wg)(i[e].geometry))||(i.splice(e,1),e--,t--);return this.transform=e,this}unquantize(){const{geometryType:e,features:t,transform:r}=this;if(!r)return this;const{translate:[o,s],scale:[i,n]}=r,a=this._getHydrationFunction(e,(e=>e*i+o),(e=>s-e*n));for(const{geometry:e}of t)(0,u.pC)(e)&&a&&a(e);return this.transform=null,this}_quantizePoints(e,t,r){let o,s;const i=[];for(let n=0,a=e.length;n<a;n++){const a=e[n];if(n>0){const e=t(a[0]),n=r(a[1]);e===o&&n===s||(i.push([e-o,n-s]),o=e,s=n)}else o=t(a[0]),s=r(a[1]),i.push([o,s])}return i.length>0?i:null}_getQuantizationFunction(e,t,r){return"point"===e?e=>(e.x=t(e.x),e.y=r(e.y),e):"polyline"===e||"polygon"===e?e=>{const o=(0,m.oU)(e)?e.rings:e.paths,s=[];for(let e=0,i=o.length;e<i;e++){const i=o[e],n=this._quantizePoints(i,t,r);n&&s.push(n)}return s.length>0?((0,m.oU)(e)?e.rings=s:e.paths=s,e):null}:"multipoint"===e?e=>{const o=this._quantizePoints(e.points,t,r);return o&&o.length>0?(e.points=o,e):null}:"extent"===e?e=>e:null}_getHydrationFunction(e,t,r){return"point"===e?e=>{e.x=t(e.x),e.y=r(e.y)}:"polyline"===e||"polygon"===e?e=>{const o=(0,m.oU)(e)?e.rings:e.paths;let s,i;for(let e=0,n=o.length;e<n;e++){const n=o[e];for(let e=0,o=n.length;e<o;e++){const o=n[e];e>0?(s+=o[0],i+=o[1]):(s=o[0],i=o[1]),o[0]=t(s),o[1]=r(i)}}}:"extent"===e?e=>{e.xmin=t(e.xmin),e.ymin=r(e.ymin),e.xmax=t(e.xmax),e.ymax=r(e.ymax)}:"multipoint"===e?e=>{const o=e.points;let s,i;for(let e=0,n=o.length;e<n;e++){const n=o[e];e>0?(s+=n[0],i+=n[1]):(s=n[0],i=n[1]),n[0]=t(s),n[1]=r(i)}}:null}};(0,s._)([(0,y.Cb)({type:String,json:{write:!0}})],w.prototype,"displayFieldName",void 0),(0,s._)([(0,y.Cb)({type:Boolean,json:{write:{overridePolicy:e=>({enabled:e})}}})],w.prototype,"exceededTransferLimit",void 0),(0,s._)([(0,y.Cb)({type:[n.Z],json:{write:!0}})],w.prototype,"features",void 0),(0,s._)([(0,d.r)("features")],w.prototype,"readFeatures",null),(0,s._)([(0,y.Cb)({type:[g.Z],json:{write:!0}})],w.prototype,"fields",void 0),(0,s._)([(0,y.Cb)({type:["point","multipoint","polyline","polygon","extent","mesh"],json:{read:{reader:S.read}}})],w.prototype,"geometryType",void 0),(0,s._)([(0,h.c)("geometryType")],w.prototype,"writeGeometryType",null),(0,s._)([(0,y.Cb)({type:Boolean,json:{write:{overridePolicy:e=>({enabled:e})}}})],w.prototype,"hasM",void 0),(0,s._)([(0,y.Cb)({type:Boolean,json:{write:{overridePolicy:e=>({enabled:e})}}})],w.prototype,"hasZ",void 0),(0,s._)([(0,y.Cb)({types:i.qM,json:{write:!0}})],w.prototype,"queryGeometry",void 0),(0,s._)([(0,d.r)("queryGeometry")],w.prototype,"readQueryGeometry",null),(0,s._)([(0,y.Cb)({type:f.Z,json:{write:!0}})],w.prototype,"spatialReference",void 0),(0,s._)([(0,h.c)("spatialReference")],w.prototype,"writeSpatialReference",null),(0,s._)([(0,y.Cb)({json:{write:!0}})],w.prototype,"transform",void 0),w=o=(0,s._)([(0,c.j)("esri.rest.support.FeatureSet")],w),w.prototype.toJSON.isDefaultToJSON=!0;const b=w},11282:(e,t,r)=>{r.d(t,{cv:()=>a,en:()=>n,lA:()=>i}),r(68773),r(40330);var o=r(22974),s=r(17452);function i(e,t){return t?{...t,query:{...e??{},...t.query}}:{query:e}}function n(e){return"string"==typeof e?(0,s.mN)(e):(0,o.d9)(e)}function a(e,t,r){const o={};for(const s in e){if("declaredClass"===s)continue;const i=e[s];if(null!=i&&"function"!=typeof i)if(Array.isArray(i)){o[s]=[];for(let e=0;e<i.length;e++)o[s][e]=a(i[e])}else if("object"==typeof i)if(i.toJSON){const e=i.toJSON(r&&r[s]);o[s]=t?e:JSON.stringify(e)}else o[s]=t?i:JSON.stringify(i);else o[s]=i}return o}r(71058)},58333:(e,t,r)=>{r.d(t,{ET:()=>i,I4:()=>s,eG:()=>l,lF:()=>n,lj:()=>u,qP:()=>a,wW:()=>p});const o=[252,146,31,255],s={type:"esriSMS",style:"esriSMSCircle",size:6,color:o,outline:{type:"esriSLS",style:"esriSLSSolid",width:.75,color:[153,153,153,255]}},i={type:"esriSLS",style:"esriSLSSolid",width:.75,color:o},n={type:"esriSFS",style:"esriSFSSolid",color:[252,146,31,196],outline:{type:"esriSLS",style:"esriSLSSolid",width:.75,color:[255,255,255,191]}},a={type:"esriTS",color:[255,255,255,255],font:{family:"arial-unicode-ms",size:10,weight:"bold"},horizontalAlignment:"center",kerning:!0,haloColor:[0,0,0,255],haloSize:1,rotated:!1,text:"",xoffset:0,yoffset:0,angle:0},l={type:"esriSMS",style:"esriSMSCircle",color:[0,0,0,255],outline:null,size:10.5},p={type:"esriSLS",style:"esriSLSSolid",color:[0,0,0,255],width:1.5},u={type:"esriSFS",style:"esriSFSSolid",color:[0,0,0,255],outline:null}}}]);