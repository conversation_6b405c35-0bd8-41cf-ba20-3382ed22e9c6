package org.thingsboard.server.dao.optionLog;

import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.DTO.CommandLogDTO;
import org.thingsboard.server.dao.model.request.CommandListRequest;
import org.thingsboard.server.dao.model.sql.CommandLog;

public interface CommandLogService {
    PageData<CommandLogDTO> findList(CommandListRequest request, TenantId tenantId);

    void save(CommandLog commandLog);
}
