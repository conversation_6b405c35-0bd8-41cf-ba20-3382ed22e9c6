import{_ as F}from"./index-C9hz-UZb.js";import{_ as H}from"./CardTable-rdWOL4_6.js";import{d as G,j as M,M as U,a6 as J,r as v,c as x,am as $,bF as c,a8 as K,s as C,bB as W,bu as Q,ay as X,g as O,n as S,q as y,i as m,F as _,cs as q,an as w,dF as Y,dA as Z,al as ee,b7 as te,aj as ae,C as re}from"./index-r0dFAfgr.js";import{_ as ne}from"./CardSearch-CB_HNR-Q.js";import{e as le,a as oe,l as se}from"./queryStatistics-HnNdnFZW.js";import{u as ie}from"./useStation-DJgnSZIA.js";import{f as ce}from"./formartColumn-D5r7JJ2G.js";import{b as de}from"./zhandian-YaGuQZe6.js";import"./Search-NSrhrIa_.js";const ue={class:"wrapper"},me={key:1},pe=G({__name:"index",setup(ye){const g=M(),{$messageWarning:D,$messageError:N}=U(),I=J(),{getStationTree:A,getStationTreeByDisabledType:P}=ie(),l=v({type:"date",chartOption:null,activeName:"echarts",data:null,stationTree:[],checkedKeys:[]}),V=x(),T=x(),k=x(),f=x();let h=v([]);$(()=>l.activeName,()=>{l.activeName==="echarts"&&L()});const j=v({defaultParams:{type:"day",queryType:"15m",year:[c().format(),c().format()],month:[c().format(),c().format()],day:[c().startOf("day").format(),c().format()]},filters:[{type:"select-tree",label:"监测点:",multiple:!0,field:"attributeId",clearable:!1,showCheckbox:!0,lazy:!0,options:K(()=>l.stationTree),lazyLoad:(e,t)=>{var a,s;if(e.level===0)return t([]);if(((a=e.data.children)==null?void 0:a.length)>0)return t(e.data.children);if(e.isLeaf)return t([]);if((s=e.data)!=null&&s.isLeaf)return t([]);de({stationId:e.data.id}).then(i=>{var o;const n=(o=i.data)==null?void 0:o.map(d=>({label:d.type,value:"",id:"",children:d.attrList.map(r=>({label:r.name,value:r.id,id:r.id,isLeaf:!0}))}));return t(n)})}},{type:"radio-button",field:"type",options:[{label:"日",value:"day"},{label:"月",value:"month"},{label:"年",value:"year"}],label:"时间频率"},{type:"datetimerange",label:"选择日期",field:"day",clearable:!1,handleHidden:(e,t,a)=>{a.hidden=e.type==="year"||e.type==="month"}},{type:"monthrange",label:"选择日期",field:"month",clearable:!1,handleHidden:(e,t,a)=>{console.log(e.type),a.hidden=e.type==="year"||e.type==="day"}},{type:"yearrange",label:"选择日期",field:"year",clearable:!1,handleHidden:(e,t,a)=>{a.hidden=e.type==="month"||e.type==="day"}},{type:"select",label:"时间间隔:",field:"queryType",clearable:!1,options:[{label:"1 m",value:"1m"},{label:"5 m",value:"5m"},{label:"10 m",value:"10m"},{label:"15 m",value:"15m"},{label:"1小时",value:"hour"}],handleHidden:(e,t,a)=>{a.hidden=e.type==="month"||e.type==="year"},itemContainerStyle:{width:"180px"}}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",click:()=>{var t;p.pagination.page=1;const e=((t=f.value)==null?void 0:t.queryParams)||{};e.attributeId&&e.attributeId.length>0?z():D("选择监测点")},svgIcon:C(ee)},{type:"default",perm:!0,text:"重置",svgIcon:C(te),click:()=>{var e;(e=f.value)==null||e.resetForm()}},{perm:!0,type:"warning",text:"导出",hide:()=>l.activeName!=="list",svgIcon:C(ae),click:()=>E()}]}]}),E=()=>{var o;const e=((o=f.value)==null?void 0:o.queryParams)||{};console.log(e);const[t,a]=e[e.type]||[];let s=0,i=0;e.type==="day"?(s=t?c(t).valueOf():"",i=a?c(a).valueOf():""):(s=t?c(t).startOf(e.type).valueOf():"",i=a?c(a).endOf(e.type).valueOf():"");const n={attributes:e.attributeId.join(","),queryType:e.type==="month"?"day":e.type==="year"?"month":e.queryType,start:s,end:i};le(n).then(d=>{const r=window.URL.createObjectURL(d.data),u=document.createElement("a");u.style.display="none",u.href=r,u.setAttribute("download","数据对比表.xlsx"),document.body.appendChild(u),u.click()})},p=v({loading:!1,dataList:[],columns:[],operations:[],pagination:{refreshData:({page:e,size:t})=>{p.pagination.page=e,p.pagination.limit=t,p.dataList=h==null?void 0:h.slice((e-1)*t,e*t)}}}),z=()=>{var o;p.loading=!0;const e=((o=f.value)==null?void 0:o.queryParams)||{};console.log(e);const[t,a]=e[e.type]||[];let s=0,i=0;e.type==="day"?(s=t?c(t).valueOf():"",i=a?c(a).valueOf():""):(s=t?c(t).startOf(e.type).valueOf():"",i=a?c(a).endOf(e.type).valueOf():"");const n={attributes:e.attributeId.join(","),queryType:e.type==="month"?"day":e.type==="year"?"month":e.queryType,start:s,end:i};oe(n).then(d=>{var b;const r=(b=d.data)==null?void 0:b.data;l.data=r;const u=ce(r==null?void 0:r.tableInfo);h=r==null?void 0:r.tableDataList,p.columns=u,p.dataList=h.slice(0*20,20),p.pagination.total=r==null?void 0:r.tableDataList.length,p.loading=!1,L()}).catch(d=>{N(d)})},B=()=>{var e;(e=T.value)==null||e.resize()},L=()=>{var a,s,i;const e=se();e.series=[];const t={name:"",smooth:!0,data:[],type:"line",markPoint:{data:[{type:"max",name:"最大值",label:{fontSize:12,color:g.isDark?"#ffffff":"#000000"}},{type:"min",name:"最小值",label:{color:g.isDark?"#ffffff":"#000000"}}]},markLine:{data:[{type:"average",name:"平均值"}]}};e.xAxis.data=(a=l.data)==null?void 0:a.tableDataList.map(n=>n.ts),(s=l.data)==null||s.tableInfo.map((n,o)=>{var d;if(n.columnValue!=="ts"){const r=JSON.parse(JSON.stringify(t));r.name=n.columnName,r.data=(d=l.data)==null?void 0:d.tableDataList.map(b=>b[n.columnValue]);const u=n.columnName.split("--")[2]+(n.unit?"("+n.unit+")":"");o===1?(e.yAxis[0].name=u,e.yAxis[0].axisLabel.formatter="{value} "+n.unit?n.unit:""):o>1&&(e.yAxis.find(R=>R.name===u)||(console.log("ddd",o-1),r.yAxisIndex=o-1,e.grid.right=70*(o-1),e.yAxis.push({position:"right",alignTicks:!0,type:"value",name:u,offset:70*(o-2),axisLine:{show:!0,lineStyle:{types:"solid"}},axisLabel:{show:!0,textStyle:{color:"#656b84"},formatter:"{value} "+n.unit?n.unit:""},splitLine:{lineStyle:{color:g.isDark?"#303958":"#ccc",type:[5,10],dashOffset:5}}}))),e.series.push(r)}}),(i=T.value)==null||i.clear(),W(()=>{k.value&&I.listenTo(k.value,()=>{l.chartOption=e,B()})})};return Q(async()=>{const e=["流量监测站,测流压站"].join(","),t=await A(e);await P(t,["Project","Station"],!1,"Station"),l.stationTree=t,console.log(" state.stationTree ",l.stationTree)}),(e,t)=>{const a=ne,s=Y,i=Z,n=X("VChart"),o=H,d=F;return O(),S("div",ue,[y(a,{ref_key:"cardSearch",ref:f,config:m(j)},null,8,["config"]),y(d,{class:"card-table",title:m(l).activeName==="list"?"分析列表":"分析曲线"},{right:_(()=>[y(i,{modelValue:m(l).activeName,"onUpdate:modelValue":t[0]||(t[0]=r=>m(l).activeName=r)},{default:_(()=>[y(s,{label:"echarts"},{default:_(()=>[y(m(q),{style:{"margin-right":"1px","font-size":"16px"},icon:"clarity:line-chart-line"})]),_:1}),y(s,{label:"list"},{default:_(()=>[y(m(q),{style:{"margin-right":"1px","font-size":"16px"},icon:"material-symbols:table"})]),_:1})]),_:1},8,["modelValue"])]),default:_(()=>[m(l).activeName==="echarts"?(O(),S("div",{key:0,ref_key:"agriEcoDev",ref:k,class:"chart-box"},[y(n,{ref_key:"refChart",ref:T,theme:m(g).isDark?"dark":"",option:m(l).chartOption},null,8,["theme","option"])],512)):w("",!0),m(l).activeName==="list"?(O(),S("div",me,[y(o,{ref_key:"refCardTable",ref:V,class:"cardTable",config:m(p)},null,8,["config"])])):w("",!0)]),_:1},8,["title"])])}}}),Ce=re(pe,[["__scopeId","data-v-2170b639"]]);export{Ce as default};
