/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.maintain;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.id.MaintainId;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.maintain.Maintain;
import org.thingsboard.server.common.data.repair.Repair;
import org.thingsboard.server.common.data.repair.RepairType;
import org.thingsboard.server.dao.repair.RepairDao;

import java.util.List;
import java.util.UUID;

@Slf4j
@Service
@Transactional
public class MaintainServiceImpl implements MaintainService {

    @Autowired
    private MaintainDao maintainDao;

    @Override
    public Maintain addMaintain(Maintain maintain) {
        maintain.setCreateTime(System.currentTimeMillis());
        return maintainDao.save(maintain);
    }

    @Override
    public List<Maintain> findByTenantId(TenantId tenantId) {
        return maintainDao.findByTenantId(tenantId);
    }

    @Override
    public List<Maintain> findByProjectId(String projectId) {
        return maintainDao.findByProjectId(projectId);
    }

    @Override
    public boolean delete(MaintainId maintainId) {
        return maintainDao.removeById(maintainId.getId());
    }

}
