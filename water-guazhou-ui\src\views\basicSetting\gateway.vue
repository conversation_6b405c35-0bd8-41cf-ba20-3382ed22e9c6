<template>
  <!-- 终端管理 -->
  <TreeBox>
    <template #tree>
      <SLTree :tree-data="treeData" />
    </template>
    <CardSearch
      ref="cardSearch"
      :config="cardSearchConfig"
      class="card-search"
    />
    <CardTable :config="cardTableConfig" class="card-table" />
    <AddOrUpdateDialog
      v-if="addOrUpdateConfig.visible"
      dialog-width="560px"
      :config="addOrUpdateConfig"
      @refresh-data="refreshData"
    />
    <AddOrUpdateDialog
      v-if="projectDialog.visible"
      dialog-width="560px"
      :config="projectDialog"
      @refresh-data="refreshData"
    />
  </TreeBox>
</template>

<script>
import { shallowRef } from 'vue';
import { Search } from '@element-plus/icons-vue';
import { delProject, getProjectRoot } from '@/api/project';
import { removeSlash } from '@/utils/removeIdSlash';
import {
  deleteGatewayAndDevice,
  copyGateway,
  getHostOrDeviceByType,
  getTemplateListByType,
  getDeviceModelList
} from '@/api/device'; // getHostOrDevice,

import useGlobal from '@/hooks/global/useGlobal';
import { getDeviceType } from '@/api/ledger/ledger2';
import TreeBox from '../layout/treeOrDetailFrame/TreeBox.vue';
import { traverse } from '@/utils/GlobalHelper';

const { $messageInfo, $messageError, $messageSuccess, $confirm, $btnPerms } =
  useGlobal();

export default {
  components: { TreeBox },

  data() {
    return {
      deviceModel: [],
      cardSearchConfig: {
        filters: [
          { label: '搜索', field: 'name', type: 'input' },
          {
            type: 'btn-group',
            btns: [
              {
                perm: true,
                text: '查询',
                svgIcon: shallowRef(Search),
                click: () => this.refreshData()
              },
              {
                text: '添加终端',
                perm: $btnPerms('MQTTGateWayAdd'),
                icon: 'iconfont icon-jia',
                click: () => {
                  this.addOrUpdateConfig.defaultValue = {
                    type: 'MQTT',
                    dropJudgement: '15m',
                    longitude: '',
                    latitude: '',
                    gateway: true
                  };
                  this.addOrUpdateConfig.externalParams = {
                    projectId: this.treeData.currentProject.id
                  };
                  this.addOrUpdateConfig.title = '添加终端';
                  this.addOrUpdateConfig.visible = true;
                }
              }
            ]
          }
        ]
      },
      cardTableConfig: {
        loading: false,
        dataList: [],
        indexVisible: true,
        columns: [
          { prop: 'name', label: '终端名称' },
          // { prop: 'foreignKey', label: '编码' },
          {
            prop: 'status',
            label: '在线状态',
            formatter: (row) => (row.status ? '在线' : '离线')
          }
        ],
        operations: [
          {
            text: '编辑',
            isTextBtn: true,
            perm: true,
            icon: 'iconfont icon-bianji',
            click: (row) => {
              const additionInfo = JSON.parse(row.additionalInfo);
              this.addOrUpdateConfig.title = '编辑终端';
              
              // 处理位置信息
              let longitude = '';
              let latitude = '';
              if (additionInfo.location && Array.isArray(additionInfo.location) && additionInfo.location.length >= 2) {
                longitude = additionInfo.location[0];
                latitude = additionInfo.location[1];
              }
              
              this.addOrUpdateConfig.defaultValue = {
                ...row,
                ...additionInfo,
                longitude: longitude,
                latitude: latitude,
                gateway: true
              };
              this.addOrUpdateConfig.visible = true;
            }
          },
          {
            text: '复制',
            isTextBtn: true,
            perm: true,
            icon: 'iconfont icon-icon_fuzhi',
            click: (row) => this.copyGateway(row)
          },
          {
            text: '删除',
            isTextBtn: true,
            perm: true,
            type: 'danger',
            icon: 'iconfont icon-bianji',
            click: (row) => this.handleDelete(row)
          }
        ],
        operationWidth: '220px',
        pagination: {
          page: 1,
          limit: 20,
          total: 0,
          handleSize: (val) => {
            this.cardTableConfig.pagination.limit = val;
            this.refreshData();
          },
          handlePage: (val) => {
            this.cardTableConfig.pagination.page = val;
            this.refreshData();
          }
        }
      },
      addOrUpdateConfig: {
        visible: false,
        title: '添加终端',
        close: () => (this.addOrUpdateConfig.visible = false),
        open: () => {
          this.getTemplateListByType();
          this.getDeviceModelList();
        },
        addUrl: '/api/device',
        editUrl: '/api/device',
        defaultValue: { type: 'MQTT', gateway: true },
        setSubmitParams: (params) => {
          // 处理经纬度数据
          if (params.additionalInfo) {
            const aInfo = JSON.parse(params.additionalInfo);
            if (aInfo.longitude !== undefined && aInfo.latitude !== undefined) {
              // 确保使用数字类型的经纬度值
              const longitude = parseFloat(aInfo.longitude);
              const latitude = parseFloat(aInfo.latitude);
              
              // 只有当经纬度都是有效数字时才设置location
              if (!isNaN(longitude) && !isNaN(latitude)) {
                // additionalInfo中的location使用数组格式
                aInfo.location = [longitude, latitude];
                // 单独的location字段使用逗号分隔的字符串格式
                params.location = `${longitude},${latitude}`;
              } else {
                // 如果输入无效，则不设置location
                delete aInfo.location;
              }
              
              // 删除单独的经纬度字段
              delete aInfo.longitude;
              delete aInfo.latitude;
              params.additionalInfo = JSON.stringify(aInfo);
            }
          }
          return params;
        },
        columns: [
          {
            type: 'input',
            label: '终端名称:',
            key: 'name',
            rules: [{ required: true, message: '请填写终端名称' }]
          },
          {
            type: 'input',
            label: '终端编码:',
            key: 'foreignKey',
            rules: [{ required: true, message: '请填写终端编码' }]
          },
          {
            type: 'input',
            label: '传输协议:',
            key: 'type',
            disabled: true
            // aInfo: true
          },
          {
            type: 'select',
            label: '协议模板:',
            key: 'templateId',
            options: []
          },
          {
            type: 'select',
            allowCreate: true,
            key: 'deviceTypeName',
            search: true,
            label: '终端类型',
            rules: [{ required: true, message: '请输入终端类型' }]
          },
          {
            type: 'select',
            allowCreate: true,
            key: 'hardwareId',
            search: true,
            label: '设备型号',
            rules: [{ required: true, message: '请输入设备型号' }],
            options: computed(() => this.deviceModel)
          },
          {
            type: 'input-number',
            allowCreate: true,
            label: '经度:',
            key: 'longitude',
            aInfo: true,
            rules: [{ required: true, message: '请输入经度' }]
          },
          {
            type: 'input-number',
            label: '纬度:',
            allowCreate: true,
            key: 'latitude',
            aInfo: true,
            rules: [{ required: true, message: '请输入纬度' }]
          },
          {
            type: 'textarea',
            label: '备注:',
            key: 'introduction',
            aInfo: true,
            rows: 3
          },
          {
            type: 'select',
            label: '掉线判断:',
            aInfo: true,
            key: 'dropJudgement',
            options: [
              { value: '1m', label: '1分钟' },
              { value: '5m', label: '5分钟' },
              { value: '10m', label: '10分钟' },
              { value: '15m', label: '15分钟' },
              { value: '24h', label: '24小时' }
            ]
          },
          {
            type: 'none',
            aInfo: true,
            key: 'gateway'
          }
        ]
      },
      projectDialog: {
        visible: false,
        title: '新建项目',
        close: () => {
          this.projectDialog.visible = false;
        },
        addUrl: 'api/project',
        editUrl: 'api/project/edit',
        defaultValue: {},
        externalParams: {},
        columns: [
          {
            type: 'input',
            label: '项目名称:',
            key: 'name',
            rules: [{ required: true, message: '请填写项目名称' }]
          },
          {
            type: 'input',
            label: 'WIFI名称:',
            aInfo: true,
            key: 'WIFIName',
            rules: [{ required: true, message: '请填写WIFI名称' }]
          },
          {
            type: 'textarea',
            label: '项目简介:',
            key: 'introduction',
            aInfo: true,
            rows: 3
          },
          {
            type: 'image',
            aInfo: true,
            label: '图片:',
            key: 'imageUrl'
          },
          {
            type: 'input',
            label: '项目地址:',
            aInfo: true,
            key: 'address',
            rules: [{ required: true, message: '请填写项目地址' }]
          },
          {
            type: 'location',
            label: '项目定位:',
            aInfo: true,
            key: 'location',
            rules: [{ required: true, message: '请输入项目定位' }]
          }
        ]
      },
      treeData: {
        that: this,
        title: '区域划分',
        data: [],
        loading: false,
        isFilterTree: true,
        currentId: '',
        currentProject: {},
        btnPerms: {
          addBtn: true,
          editBtn: true,
          delBtn: true
        },
        allowCreate: false,
        allowNew: false,
        clickAddOrEdit: (node, current) => {
          console.log(node);
          this.projectDialog.externalParams = {};
          if (current === 'edit') {
            if (node.additionalInfo) {
              this.projectDialog.defaultValue = {
                ...node,
                ...JSON.parse(node.additionalInfo)
              };
            }
            this.projectDialog.title = '编辑项目';
          } else {
            this.projectDialog.title = '新建项目';
            this.projectDialog.defaultValue = { location: [116.4, 39.91] };
            if (node) {
              this.projectDialog.defaultValue = { location: [116.4, 39.91] };
              this.projectDialog.externalParams = { parentId: node.id };
            }
          }
          this.projectDialog.visible = true;
        },
        expandNodeId: [],
        defaultProps: {
          children: 'children',
          label: 'name'
        },
        treeNodeHandleClick: (data) => {
          // 设置当前选中项目信息
          this.treeData.currentProject = data;
          this.refreshData();
        },
        allowAdd: true,
        allowEdit: true,
        allowDelete: true,
        projectDelete(id) {
          delProject(id).then(() => {
            $messageSuccess('操作成功');
            this.that.refreshTree();
          });
        }
      }
    };
  },
  created() {
    this.refreshTree(true);
    this.initDeviceType();
    // this.refreshData(true)
  },

  methods: {
    initDeviceType() {
      //    调用获取设备类型接口
      getDeviceType().then((res) => {
        if (res.data) {
          this.addOrUpdateConfig.columns[4].options = res.data.map((item) => ({
            label: item,
            value: item
          }));
        }
      });
    },
    async refreshData(isFirst) {
      this.cardTableConfig.loading = true;
      const paramsObj = {
        page: this.cardTableConfig.pagination.page,
        size: this.cardTableConfig.pagination.limit
        // projectId: this.treeData.currentProject.id,
        // type: 'MQTT'
      };
      if (!isFirst) Object.assign(paramsObj, this.$refs.cardSearch.queryParams);
      try {
        const res = await getHostOrDeviceByType(
          this.treeData.currentProject.id,
          'MQTT',
          paramsObj
        );
        if (res.status === 200) {
          this.cardTableConfig.dataList = res.data.data;
          this.cardTableConfig.pagination.total = res.data.total;
        } else {
          $messageInfo('暂无终端');
        }
        this.cardTableConfig.loading = false;
      } catch (err) {
        $messageError(err.data.message);
        this.cardTableConfig.loading = false;
      }
    },
    refreshTree(isFirst) {
      getProjectRoot()
        .then((res) => {
          this.totalLoading = false;
          if (res.data) {
            this.treeData.data = res.data;
            const fTData = this.treeData.data.filter((v) => !v.disabled);
            this.treeData.currentProject = fTData[0];
            this.refreshData(isFirst);
          } else {
            $messageInfo('暂无项目 不可操作，请创建项目');
          }
        })
        .catch((err) => {
          console.log(err);
          $messageInfo('暂无项目 不可操作，请创建项目');
        });
    },
    async edit() {
      const params = {
        type: this.editParams.type,
        name: this.editParams.name,
        timeRange: this.editParams.timeRange.join('-')
      };
      try {
        const res = await editYxscgl(params);
        if (res.status === 200) {
          $messageSuccess('修改成功！');
          this.refreshData();
        } else {
          $messageError('修改失败！');
        }
      } catch (error) {
        $messageError('修改失败！');
      }
    },
    handleDelete(row) {
      $confirm('确定删除指定终端?', '删除提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 删除角色方法
        deleteGatewayAndDevice(removeSlash(row.id.id)).then(() => {
          $messageSuccess('操作成功');
          this.refreshData();
        });
      });
    },

    // 复制终端
    async copyGateway(row) {
      try {
        await copyGateway(
          removeSlash(row.id.id),
          this.treeData.currentProject.id
        );
        $messageSuccess('复制成功');
      } catch (err) {
        $messageError(err.message || err.data.message);
      }
    },

    // 获取协议模板
    getTemplateListByType() {
      getTemplateListByType('MQTT').then((res) => {
        this.addOrUpdateConfig.columns[3].options = res.data.map((item) => ({
          value: item.id,
          label: item.name
        }));
      });
    },

    // 获取设备型号
    getDeviceModelList() {
      getDeviceModelList({
        page: 1,
        size: 10000
      }).then((res) => {
        this.deviceModel = traverse(res.data.data.data || [], 'children', {
          label: 'name',
          value: 'id'
        });
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.card-search {
  margin-bottom: 20px;
}

input {
  height: 48px;
}

.card-table {
  height: calc(100% - 80px);
}
</style>
