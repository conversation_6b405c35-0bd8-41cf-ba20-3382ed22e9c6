 <!--压力分析 -->
<template>
  <div class="wrapper">
    <CardSearch
      ref="cardSearch"
      :config="cardSearchConfig"
    />
    <SLCard
      class="card-table"
      :title="state.activeName === 'list' ? '压力分析列表' : '压力分析曲线'"
    >
      <template #right>
        <el-radio-group v-model="state.activeName">
          <el-radio-button label="echarts">
            <Icon
              style="margin-right: 1px;font-size: 16px"
              icon="clarity:line-chart-line"
            />
          </el-radio-button>
          <el-radio-button label="list">
            <Icon
              style="margin-right: 1px;font-size: 16px"
              icon="material-symbols:table"
            />
          </el-radio-button>
        </el-radio-group>
      </template>
      <!-- 列表模式 -->
      <FormTable
        v-if="state.activeName === 'list'"
        ref="refTable"
        :config="cardTableConfig"
      />
      <!-- 图表模式 -->
      <div
        v-if="state.activeName === 'echarts'"
        ref="echartsDiv"
        class="card-ehcarts"
      >
        <VChart
          ref="refChart"
          :theme="useAppStore().isDark ? 'dark' : 'light'"
          class="card-ehcarts"
          :option="state.chartOption"
        ></VChart>
      </div>
    </SLCard>
  </div>
</template>
<script lang="ts" setup>
import dayjs from 'dayjs'
import elementResizeDetectorMaker from 'element-resize-detector'
import { Download, Refresh, Search as SearchIcon } from '@element-plus/icons-vue'
import { Icon } from '@iconify/vue'
import { lineOption } from '../echartsData/echart'
import { IECharts } from '@/plugins/echart'
import { getFormatTreeNodeDeepestChild } from '@/utils/GlobalHelper'
import { getPressureRatio } from '@/api/pipeNetworkMonitoring/flowMonitoring'
import useStation from '@/hooks/station/useStation'
import { useAppStore } from '@/store'
import useGlobal from '@/hooks/global/useGlobal'

const { $messageWarning } = useGlobal()
const { getStationTree } = useStation()
const today = dayjs().date()
const state = reactive<{
  type: 'date' | 'week' | 'year',
  chartOption: any,
  activeName: string,
  data: any,
  dataX: any,
  stationTree: any,
  chartName: string
}>({
  type: 'date',
  chartOption: null,
  activeName: 'echarts',
  data: null,
  dataX: [],
  stationTree: [],
  chartName: ''
})
const cardSearch = ref<ICardSearchIns>()
const compareType = ref<string>('day')
const refChart = ref<IECharts>()
const echartsDiv = ref<any>()
const refTable = ref<ICardTableIns>()
const tableData = reactive<any[]>([])
// 获取左边树
// const TreeData = reactive<SLTreeConfig>({
//   data: [],
//   title: '区域划分',
//   defaultExpandAll: false,
//   nodeExpand: async (params: any, node?: any) => {
//     if (params.data?.type === 'Station') {
//       const attrs = await GetStationAttrs({ stationId: params.id })
//       console.log(attrs.data)
//       const newAttrs = attrs.data?.map(attr => {
//         return {
//           label: attr.type,
//           value: '',
//           id: '',
//           disabled: true,
//           children: attr.attrList
//         }
//       })
//       node.data.children = newAttrs as any
//     }
//   },
//   treeNodeHandleClick: async (data: NormalOption) => {
//     TreeData.loading = true
//     TreeData.currentProject = data
//     await refreshData()
//   }
// })
//----------------------------------------------------

// 搜索栏初始化配置
const cardSearchConfig = reactive<ISearch>({
  defaultParams: {
    queryType: 'day',
    day: [
      dayjs().date(today - 2),
      dayjs().date(today)
    ],
    month: [dayjs(), dayjs()]
  },
  filters: [{
    type: 'select-tree',
    label: '监测点:',
    defaultExpandAll: true,
    field: 'stationId',
    clearable: false,
    width: '200px',
    options: computed(() => state.stationTree) as any,
    nodeClick: data => {
      state.chartName = data.label
    }
  },
  {
    type: 'select',
    label: '比较类型:',
    field: 'queryType',
    clearable: false,
    width: '200px',
    options: [
      { label: '日分时(时间段)', value: 'day' },
      { label: '月分日(时间段)', value: 'month' }
      // { label: '日分时(时间点)', value: 'v3' },
      // { label: '月分日(时间点)', value: 'v4' }
    ],
    itemContainerStyle: {
      width: '240px'
    },
    onChange: (val: any) => {
      selectQueryType(val)
    }
  },
  { type: 'daterange',
    label: '日期',
    field: 'day',
    clearable: false,
    handleHidden: (params: any, query: any, formItem: IFormItem) => {
      formItem.hidden = params.queryType === 'month'
    }
  },
  { type: 'monthrange',
    label: '日期',
    field: 'month',
    clearable: false,
    handleHidden: (params: any, query: any, formItem: IFormItem) => {
      formItem.hidden = params.queryType === 'day'
    }
  }

  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        // {
        //   perm: true,
        //   type: 'success',
        //   text: '添加',
        //   hide: (row?: any) => { return ['v3', 'v4'].indexOf(compareType.value) === -1 },
        //   click: () => refreshData(),
        //   icon: ICONS.ADD
        // },
        {
          perm: true,
          text: '查询',
          svgIcon: shallowRef(SearchIcon),
          click: () => {
            cardTableConfig.pagination.page = 1
            const queryParams = cardSearch.value?.queryParams as any || {}
            if (queryParams.stationId) {
              refreshData()
            } else $messageWarning('选择监测点')
          }
        },
        {
          type: 'default',
          perm: true,
          text: '重置',
          svgIcon: shallowRef(Refresh),
          click: () => {
            cardSearch.value?.resetForm()
          }
        },
        {
          perm: true,
          type: 'warning',
          text: '导出',
          hide: () => { return state.activeName !== 'list' },
          svgIcon: shallowRef(Download),
          click: () => handleExport()
        }
      ]
    }
  ]
})

const compareTypes = {
  day: 'daterange',
  month: 'monthrange'
}
// 选择比较类型
const selectQueryType = (val: any) => {
  compareType.value = val
  const date = cardSearchConfig.filters?.find(filter => filter.field === 'date') as any
  date.type = compareTypes[val]
  if (val === 'monthrange') {
    cardSearchConfig.defaultParams = {
      ...cardSearchConfig.defaultParams,
      date: [dayjs().add(-1, 'month').startOf('month'), dayjs().endOf('month')]
    }
    cardSearch.value?.resetForm()
  }
}
// 初始化列表配置数据
const cardTableConfig = reactive<ITable>({
  loading: false,
  dataList: [],
  columns: [],
  operations: [],
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    layout: 'total, prev, pager, next, sizes, jumper',
    handleSize: val => {
      cardTableConfig.pagination.limit = val
      // cardTableConfig.dataList = tableData.slice((currentPage-1)*pagesize,currentPage*pagesize)
    },
    refreshData: ({ page, size }) => {
      cardTableConfig.pagination.page = page
      cardTableConfig.pagination.limit = size
      cardTableConfig.dataList = tableData.slice((page - 1) * size, page * size)
    }
  }
})

// 刷新列表 模拟数据
const refreshData = () => {
  const queryParams = cardSearch.value?.queryParams as any || {}
  const [start, end] = queryParams[queryParams.queryType] || []
  const params: any = {
    stationId: queryParams.stationId,
    queryType: queryParams.queryType,
    start: start ? dayjs(start).startOf(queryParams.queryType).valueOf() : null,
    end: end ? dayjs(end).endOf(queryParams.queryType).valueOf() : null
  }

  getPressureRatio(params).then(res => {
    const data = res.data?.data
    if (data) {
      state.data = data
      const result: any = []
      // if (queryParams.queryType === 'day') {
      //   for (let i = 0; i < 24; i++) {
      //     columns.push({
      //       prop: i + '',
      //       label: i + '',
      //       align: 'center'
      //     })
      //   }
      // } else {
      //   //
      //   for (let i = 0; i < 31; i++) {
      //     columns.push({
      //       prop: i + '',
      //       label: i + '',
      //       align: 'center'
      //     })
      //   }
      // }
      const columns: any = [
        {
          prop: 'name',
          label: '监测点名称',
          align: 'center',
          minWidth: 120
        }, {
          prop: 'ts',
          label: '时间',
          align: 'center',
          minWidth: 120
        }
      ]
      let currentKey = ''
      state.dataX = []
      for (const key in data) {
        const nd = data[key]?.map((d, index) => {
          if (!currentKey) {
            state.dataX.push(index)
            columns.push({
              prop: index + '',
              label: index + '',
              align: 'center'
            })
          }
          return d.value
        })
        currentKey = key
        const ndd = {
          ts: key,
          ...nd,
          name: state.chartName
        }
        console.log(ndd)
        result.push(ndd)
      }
      cardTableConfig.columns = columns
      cardTableConfig.dataList = result
      cardTableConfig.pagination.total = result.length
      refuseChart()
    } else {
      $messageWarning('无数据')
    }
  })
}

const resizeChart = () => {
  refChart.value?.resize()
}
const refuseChart = () => {
  const chartOption = lineOption(600)
  chartOption.series = []
  // TODO 根据当前选中的属性显示
  chartOption.xAxis.data = state.dataX
  for (const key in state.data) {
    const data = state.data[key]?.map(d => d.value)
    const serie: any = {
      name: key,
      smooth: true,
      data,
      type: 'line',
      markPoint: {
        data: [
          { type: 'max', name: '最大值' },
          { type: 'min', name: '最小值' }
        ]
      },
      markLine: {
        data: [{ type: 'average', name: '平均值' }]
      }
    }
    chartOption.series.push(serie)
  }
  refChart.value?.clear()
  const options: any = { callOnAdd: true }
  const erd = elementResizeDetectorMaker(options)
  nextTick(() => {
    if (echartsDiv.value) {
      erd.listenTo(echartsDiv.value, () => {
        state.chartOption = chartOption
        resizeChart()
      })
    }
  })
}
// 导出
const handleExport = () => {
  refTable.value?.exportTable()
}
onMounted(async () => {
  // const treeData = await getStationTree('水源地') as any[]
  // getStationTreeByDisabledType(treeData, ['Project', 'Station'], true, 'Station')
  // console.log('newTreeData', treeData)
  // TreeData.data = treeData as any
  // TreeData.currentProject = getFormatTreeNode(
  //   TreeData.data
  // )
  // TreeData.expandNodeId = [treeData[0].id]

})

onBeforeMount(async () => {
  const treeData = await getStationTree('压力监测站,测流压站')
  state.stationTree = treeData
  const currentStation = getFormatTreeNodeDeepestChild(
    treeData
  ) as any
  cardSearchConfig.defaultParams = {
    ...cardSearchConfig.defaultParams,
    stationId: currentStation.id
  }
  // console.log(currentStation)
  state.chartName = currentStation.label
  cardSearch.value?.resetForm()
  refreshData()
})

</script>

<style lang="scss" scoped>
.card-ehcarts {
  height: 100%;
  width: 100%;
}
</style>
