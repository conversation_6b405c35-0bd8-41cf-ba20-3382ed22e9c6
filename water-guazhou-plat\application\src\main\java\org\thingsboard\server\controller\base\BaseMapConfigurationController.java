package org.thingsboard.server.controller.base;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.thingsboard.server.dao.model.sql.base.BaseMapConfiguration;
import org.thingsboard.server.dao.base.IBaseMapConfigurationService;
import org.thingsboard.server.dao.util.imodel.query.base.BaseMapConfigurationPageRequest;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;
import org.thingsboard.server.service.aspect.annotation.MonitorPerformance;

import java.util.List;

/**
 * 平台管理-底图配置Controller
 *
 * <AUTHOR>
 * @date 2025-05-22
 */
@Api(tags = "平台管理-底图配置")
@RestController
@RequestMapping("api/base/map/configuration")
public class BaseMapConfigurationController extends BaseController {

    @Autowired
    private IBaseMapConfigurationService baseMapConfigurationService;

    /**
     * 查询平台管理-底图配置列表
     */
    @MonitorPerformance(description = "平台管理-底图配置列表接口")
    @ApiOperation(value = "查询底图配置列表")
    @GetMapping("/list")
    public IstarResponse list(BaseMapConfigurationPageRequest baseMapConfiguration) {
        return IstarResponse.ok(baseMapConfigurationService.selectBaseMapConfigurationList(baseMapConfiguration));
    }

    /**
     * 获取平台管理-底图配置详细信息
     */
    @MonitorPerformance(description = "平台管理-获取底图配置详细信息接口")
    @ApiOperation(value = "获取底图配置详细信息")
    @GetMapping(value = "/getDetail")
    public IstarResponse getInfo(String id) {
        return IstarResponse.ok(baseMapConfigurationService.selectBaseMapConfigurationById(id));
    }

    /**
     * 新增平台管理-底图配置
     */
    @MonitorPerformance(description = "平台管理-新增底图配置接口")
    @ApiOperation(value = "新增底图配置")
    @PostMapping("/add")
    public IstarResponse add(@RequestBody BaseMapConfiguration baseMapConfiguration) {
        return IstarResponse.ok(baseMapConfigurationService.insertBaseMapConfiguration(baseMapConfiguration));
    }

    /**
     * 修改平台管理-底图配置
     */
    @MonitorPerformance(description = "平台管理-修改底图配置接口")
    @ApiOperation(value = "修改底图配置")
    @PostMapping("/edit")
    public IstarResponse edit(@RequestBody BaseMapConfiguration baseMapConfiguration) {
        return IstarResponse.ok(baseMapConfigurationService.updateBaseMapConfiguration(baseMapConfiguration));
    }

    /**
     * 删除平台管理-底图配置
     */
    @MonitorPerformance(description = "平台管理-删除底图配置接口")
    @ApiOperation(value = "删除底图配置")
    @DeleteMapping("/deleteIds")
    public IstarResponse remove(@RequestBody List<String> ids) {
        return IstarResponse.ok(baseMapConfigurationService.deleteBaseMapConfigurationByIds(ids));
    }
}
