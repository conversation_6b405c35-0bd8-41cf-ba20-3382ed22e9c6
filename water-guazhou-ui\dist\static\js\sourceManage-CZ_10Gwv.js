import{_ as y}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as b}from"./CardTable-rdWOL4_6.js";import{_ as x}from"./CardSearch-CB_HNR-Q.js";import{d as S,c as _,r as p,ev as C,b as l,S as k,ew as D,ex as v,o as M,g as L,n as q,q as u,C as P}from"./index-r0dFAfgr.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";const B={class:"wrapper"},F=S({__name:"sourceManage",setup(T){const g=_(),s=_(),h=p({labelWidth:"100px",filters:[{type:"input",label:"名称",field:"name",onChange:()=>n()},{type:"input",label:"路径",field:"url",onChange:()=>n()},{type:"btn-group",btns:[{type:"primary",perm:!0,text:"查询",click:()=>n()},{perm:!0,type:"primary",text:"添加",click:()=>m()},{perm:!0,type:"danger",text:"批量删除",click:()=>d()}]}],defaultParams:{}}),a=p({columns:[{label:"资源名称",prop:"name"},{label:"资源路径",prop:"url"},{label:"资源预览图",prop:"img",image:!0}],dataList:[],operations:[{perm:!0,type:"primary",isTextBtn:!0,text:"编辑",click:e=>m(e)},{perm:!0,type:"danger",isTextBtn:!0,text:"删除",click:e=>d(e)}],pagination:{total:0,page:1,align:"right",limit:20,handlePage:e=>{a.pagination.page=e,n()},handleSize:e=>{a.pagination.limit=e,n()}},handleSelectChange:e=>{a.selectList=e||[]}}),c=p({title:"添加资源",group:[{fields:[{type:"input",label:"资源名称",field:"name",rules:[{required:!0,message:"请输入资源名称"}]},{type:"input",label:"资源路径",field:"url",rules:[{required:!0,message:"请输入资源路径"}]},{type:"avatar",label:"资源预览图",field:"img"}]}],labelPosition:"top",defaultValue:{},dialogWidth:450,draggable:!0,submit:async e=>{var t;console.log(e);try{(await C(e)).data&&l.success("操作成功"),(t=s.value)==null||t.closeDialog(),n()}catch{l.error("系统错误")}}}),m=e=>{var t;console.log(e),c.title=e?"编辑资源":"添加资源",c.defaultValue={...e||{}},(t=s.value)==null||t.openDialog()},d=e=>{k("确定删除？","删除提示").then(async()=>{var t,r,o;try{const i=e?(t=e.id)==null?void 0:t.split(","):((r=a.selectList)==null?void 0:r.map(f=>f.id))||[];console.log(i),i.length?(o=(await D(i)).data)!=null&&o.length?(l.success("删除成功"),n()):l.error("删除失败"):l.warning("请选择要删除的数据")}catch{l.error("系统错误")}}).catch(()=>{})},n=async()=>{var r,o;const e=(r=g.value)==null?void 0:r.queryParams,t=await v({page:a.pagination.page,size:a.pagination.limit,...e||{}});a.dataList=((o=t.data)==null?void 0:o.data)||[],a.pagination.total=t.data.total||0};return M(()=>{n()}),(e,t)=>{const r=x,o=b,i=y;return L(),q("div",B,[u(r,{ref_key:"refSearch",ref:g,config:h},null,8,["config"]),u(o,{class:"card-table",config:a},null,8,["config"]),u(i,{ref_key:"refDialogForm",ref:s,config:c},null,8,["config"])])}}}),G=P(F,[["__scopeId","data-v-3b79ecc3"]]);export{G as default};
