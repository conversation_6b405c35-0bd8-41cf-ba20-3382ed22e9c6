package org.thingsboard.server.dao.util.imodel.query;

import java.util.Collection;

public interface Requestible {
    static RequestAssistantType getType(Object o) {

        if (o instanceof Requestible) {
            return RequestAssistantType.OBJECT;
        } else if (isRequestAssistantCollection(o)) {
            return RequestAssistantType.COLLECTION;
        } else if (o != null && o.getClass().isArray() && Requestible.class.isAssignableFrom(o.getClass().getComponentType()) &&
                ((Requestible[]) o).length > 0) {
            return RequestAssistantType.ARRAY;
        }
        return RequestAssistantType.NONE;
    }

    static boolean isRequestAssistantCollection(Object arg) {
        if (!(arg instanceof Collection) || ((Collection<?>) arg).isEmpty())
            return false;
        Object o = ((Collection<?>) arg).stream().findFirst().get();
        return o instanceof Requestible;
    }

    default String valid(IStarHttpRequest request) {
        return null;
    }
}
