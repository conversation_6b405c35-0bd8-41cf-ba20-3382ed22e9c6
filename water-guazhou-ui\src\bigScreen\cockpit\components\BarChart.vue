<template>
  <div class="bar-chart-container" ref="chartContainer"></div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  // 图表数据
  data: {
    type: Array,
    default: () => [
      {
        name: '类别1',
        value1: 75,
        value2: 50
      },
      {
        name: '类别2',
        value1: 75,
        value2: 50
      },
      {
        name: '类别3',
        value1: 75,
        value2: 50
      },
      {
        name: '类别4',
        value1: 75,
        value2: 50
      },
      {
        name: '类别5',
        value1: 75,
        value2: 50
      }
    ]
  },
  // 图表高度
  height: {
    type: String,
    default: '100%'
  },
  // 图表宽度
  width: {
    type: String,
    default: '100%'
  },
  // 第一个数据系列颜色
  color1: {
    type: String,
    default: '#00E5BD'
  },
  // 第二个数据系列颜色
  color2: {
    type: String,
    default: '#FFC61A'
  },
  // 单位
  unit: {
    type: String,
    default: ''
  },
  // 系列1名称
  series1Name: {
    type: String,
    default: '系列1'
  },
  // 系列2名称
  series2Name: {
    type: String,
    default: '系列2'
  },
  // Y轴最大值
  maxValue: {
    type: Number,
    default: 100
  },
  // 是否显示装饰器（顶部菱形点和虚线）
  showDecorators: {
    type: Boolean,
    default: false
  },
  // 是否显示完成率线
  showRateLine: {
    type: Boolean,
    default: false
  },
  // 完成率线颜色
  rateLineColor: {
    type: String,
    default: '#00A3FF'
  },
  // 是否显示均表率线
  showAvgLine: {
    type: Boolean,
    default: false
  },
  // 均表率线颜色
  avgLineColor: {
    type: String,
    default: '#00E5BD'
  },
  // 均表率数据
  avgRateData: {
    type: Array,
    default: () => [80, 75, 60, 70, 85]
  }
})

const chartContainer = ref(null)
let chartInstance = null

// 初始化图表
const initChart = () => {
  if (!chartContainer.value) return
  
  // 销毁之前的实例
  if (chartInstance) {
    chartInstance.dispose()
  }

  // 创建echarts实例
  chartInstance = echarts.init(chartContainer.value)
  
  // 更新图表
  updateChart()
  
  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)
}

// 更新图表
const updateChart = () => {
  if (!chartInstance) return
  
  // 准备X轴数据
  const xAxisData = props.data.map(item => item.name)
  
  // 准备系列数据
  const series1Data = props.data.map(item => item.value1)
  const series2Data = props.data.map(item => item.value2)
  
  // 创建渐变色
  const createGradient = (color) => {
    const colorRgb = hexToRgb(color);
    return {
      type: 'linear',
      x: 0,
      y: 0,
      x2: 0,
      y2: 1,
      colorStops: [
        {
          offset: 0,
          color: `rgba(${colorRgb.r}, ${colorRgb.g}, ${colorRgb.b}, 0.75)` // 起始颜色（最上方，最深）
        },
        {
          offset: 1,
          color: `rgba(${colorRgb.r}, ${colorRgb.g}, ${colorRgb.b}, 0)` // 中间颜色（半透明）
        },
      ]
    }
  }
  
  // 创建系列配置
  const createSeriesConfig = (name, data, color, seriesIndex) => {
    const config = {
      name: name,
      type: 'bar',
      barWidth: '24%',
      barGap: seriesIndex === 0 ? '40%' : undefined,
      data: data,
      itemStyle: {
        color: (params) => {
          return createGradient(color)
        },
      }
    };
    
    // 仅当showDecorators为true时添加装饰器
    if (props.showDecorators) {
      config.markPoint = {
        symbol: 'diamond',
        symbolSize: 8,
        animation: false,
        itemStyle: {
          color: color
        },
        data: data.map((value, index) => {
          return {
            xAxis: xAxisData[index],
            yAxis: value,
            value: ''
          }
        })
      };
      
      config.markLine = {
        symbol: ['none', 'none'],
        animation: false,
        lineStyle: {
          type: 'dashed',
          color: color,
          width: 1,
          opacity: 0.5,
          cap: 'round'
        },
        data: data.map((value, index) => {
          return [
            {
              xAxis: index,
              yAxis: value
            },
            {
              xAxis: index,
              yAxis: 0
            }
          ]
        })
      };
    }
    
    return config;
  };
  
  const option = {
    grid: {
      top: '25%',
      left: '0%',
      right: '0%',
      bottom: '0%',
      containLabel: true
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      backgroundColor: 'rgba(0, 19, 40, 0.8)',
      borderColor: 'rgba(26, 198, 255, 0.2)',
      borderWidth: 1,
      textStyle: {
        color: '#fff'
      },
      formatter: function(params) {
        let value1 = params[0].value
        let value2 = params[1].value
        return `${params[0].name}<br/>
                <span style="display:inline-block;margin-right:5px;border-radius:50%;width:10px;height:10px;background-color:${props.color1};"></span>
                ${props.series1Name}: ${value1}${props.unit}<br/>
                <span style="display:inline-block;margin-right:5px;border-radius:50%;width:10px;height:10px;background-color:${props.color2};"></span>
                ${props.series2Name}: ${value2}${props.unit}`
      }
    },
    legend: {
      data: [{
        name: props.series1Name,
        icon: 'rect'
      }, {
        name: props.series2Name,
        icon: 'rect'
      }],
      textStyle: {
        color: '#fff'
      },
      right: 10,
      top: 0,
      itemWidth: 12,
      itemHeight: 12,
      itemGap: 25
    },
    xAxis: {
      type: 'category',
      data: xAxisData,
      axisLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.3)'
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: 'rgba(255, 255, 255, 0.8)',
        fontSize: 12,
        interval: 0,
        rotate: 0
      }
    },
    yAxis: {
      type: 'value',
      name: '',
      nameTextStyle: {
        color: 'rgba(255, 255, 255, 0.8)',
        fontSize: 12,
        align: 'left',
        padding: [0, 0, 5, -5]
      },
      max: props.maxValue,
      interval: 20,
      axisLine: {
        show: true,
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.3)'
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: 'rgba(255, 255, 255, 0.8)',
        fontSize: 12
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)',
          type: 'dashed'
        }
      }
    },
    series: [
      createSeriesConfig(props.series1Name, series1Data, props.color1, 0),
      createSeriesConfig(props.series2Name, series2Data, props.color2, 1)
    ]
  };
  
  // 如果需要显示完成率线
  if (props.showRateLine) {
    // 计算每个区域的完成率
    const completionRates = props.data.map(item => {
      const total = item.value1 + item.value2;
      return total > 0 ? Math.round((item.value1 / total) * 100) : 0;
    });
    
    option.series.push({
      name: '完成率',
      type: 'line',
      yAxisIndex: 0,
      symbol: 'circle',
      symbolSize: 6,
      itemStyle: {
        color: props.rateLineColor
      },
      lineStyle: {
        color: props.rateLineColor,
        width: 2
      },
      data: completionRates,
      label: {
        show: true,
        position: 'top',
        formatter: '{c}%',
        color: props.rateLineColor,
        fontSize: 12
      }
    });
  }
  
  // 如果需要显示均表率线
  if (props.showAvgLine) {
    option.series.push({
      name: '均表率',
      type: 'line',
      yAxisIndex: 0,
      symbol: 'circle',
      symbolSize: 6,
      itemStyle: {
        color: props.avgLineColor
      },
      lineStyle: {
        color: props.avgLineColor,
        width: 2
      },
      data: props.avgRateData, // 使用传入的均表率数据
      label: {
        show: true,
        position: 'top',
        formatter: '{c}%',
        color: props.avgLineColor,
        fontSize: 12
      }
    });
  }
  
  chartInstance.setOption(option)
}

// 处理窗口大小变化
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

// 监听数据变化
watch(() => props.data, () => {
  updateChart()
}, { deep: true })

// 监听颜色变化
watch(() => [props.color1, props.color2], () => {
  updateChart()
})

// 组件挂载后初始化图表
onMounted(() => {
  initChart()
})

// 组件卸载前销毁图表
onUnmounted(() => {
  if (chartInstance) {
    window.removeEventListener('resize', handleResize)
    chartInstance.dispose()
    chartInstance = null
  }
})

// 十六进制颜色转RGB
function hexToRgb(hex) {
  // 处理简写形式，如 #03F 转为 #0033FF
  const shorthandRegex = /^#?([a-f\d])([a-f\d])([a-f\d])$/i;
  hex = hex.replace(shorthandRegex, (m, r, g, b) => {
    return r + r + g + g + b + b;
  });

  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : {r: 0, g: 0, b: 0};
}
</script>

<style lang="scss" scoped>
.bar-chart-container {
  width: v-bind('width');
  height: v-bind('height');
  background-color: transparent;
}
</style> 