<!-- 任务管理 -->
<template>
  <RightDrawerMap
    ref="refMap"
    title="任务管理"
    :hide-right-drawer="true"
    :detail-max-min="true"
    :hide-detail-close="true"
    @map-loaded="onMaploaded"
  >
    <template #detail-header>
      <span>任务管理</span>
    </template>
    <template #detail-default>
      <div class="detail-page">
        <div
          v-show="state.curPage === 'table'"
          class="page-wrapper"
        >
          <!-- <InlineForm
            ref="refSearch"
            :config="FormConfig"
          ></InlineForm> -->
          <Search
            ref="refSearch"
            :config="FormConfig"
            style="margin-bottom: 8px"
          ></Search>
          <div class="table-box">
            <FormTable :config="TableConfig"></FormTable>
          </div>
        </div>
        <div
          v-if="state.curPage === 'detail'"
          class="page-wrapper"
        >
          <div class="detail-header">
            <el-icon @click="handleBack">
              <Back />
            </el-icon>
            <div class="detail-header-divider"></div>
            <span>养护任务详情</span>
          </div>
          <div class="detail-main overlay-y">
            <MaintainDetail
              :view="staticState.view"
              :row="TableConfig.currentRow"
              @row-click="refMap?.toggleCustomDetailMaxmin('normal')"
            ></MaintainDetail>
          </div>
        </div>
      </div>
    </template>
  </RightDrawerMap>
  <DialogForm
    ref="refDialogForm"
    :config="DialogFormConfig"
  ></DialogForm>
</template>
<script lang="ts" setup>
import {
  Delete,
  InfoFilled,
  // Plus,
  Promotion,
  Refresh,
  Search as SearchIcon,
  Back
} from '@element-plus/icons-vue'
import { queryLayerClassName } from '@/api/mapservice'
import { IDialogFormIns, ISearchIns } from '@/components/type'
import { getSubLayerIds } from '@/utils/MapHelper'
import RightDrawerMap from '../../components/common/RightDrawerMap.vue'
import { SLConfirm, SLMessage } from '@/utils/Message'
import {
  DeleteMaintainTasks,
  DispatchMaintainTask,
  GetMaintainTasks
} from '@/api/patrol'
import { formatterDateTime } from '@/utils/GlobalHelper'
import MaintainDetail from './components/MaintainDetail.vue'
import { PatrolTaskStatusConfig } from '../config'
import { PostGisOperateLog } from '@/api/system/gisSetting'
import { EGigLogFunc, EGisLogApp, EGisLogOperateType } from '../../config'

const refSearch = ref<ISearchIns>()
const refDialogForm = ref<IDialogFormIns>()
const refMap = ref<InstanceType<typeof RightDrawerMap>>()
const staticState: {
  view?: __esri.MapView
  graphicsLayer?: __esri.GraphicsLayer
} = {}
const state = reactive<{
  tabs: any[]
  loading: boolean
  layerIds: number[]
  layerInfos: any[]
  curPage: 'table' | 'detail'
}>({
  tabs: [],
  loading: false,
  layerIds: [],
  layerInfos: [],
  curPage: 'table'
})
const FormConfig = reactive<ISearch>({
  scrollBarGradientColor: '#fafafa',
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          svgIcon: shallowRef(SearchIcon),
          click: () => refreshData()
        },
        {
          perm: true,
          text: '重置',
          type: 'default',
          svgIcon: shallowRef(Refresh),
          click: () => {
            refSearch.value?.resetForm()
            refreshData()
          }
        },
        // {
        //   perm: true,
        //   text: '新建',
        //   type: 'default',
        //   svgIcon: shallowRef(Plus)
        // },
        {
          perm: true,
          text: '批量删除',
          type: 'danger',
          disabled: (): boolean => !TableConfig.selectList?.length,
          svgIcon: shallowRef(Delete),
          click: () => handleDelete()
        }
      ]
    }
  ],
  filters: [
    {
      type: 'radio-button',
      label: '派发状态',
      field: 'isAssigned',
      options: [
        { label: '全部', value: '' },
        { label: '已分派', value: 'true' },
        { label: '未分派', value: 'false' }
      ]
    },
    {
      type: 'radio-button',
      label: '完成状态',
      field: 'isComplete',
      options: [
        { label: '全部', value: '' },
        { label: '已完成', value: 'true' },
        { label: '未完成', value: 'false' }
      ]
    },
    {
      type: 'datetimerange',
      label: '创建时间',
      field: 'fromTime'
    },
    // {
    //   type: 'user-select',
    //   label: '创建人员',
    //   field: 'creator'
    // },
    {
      type: 'user-select',
      label: '养护人员',
      field: 'maintainUser'
    }
  ],
  defaultParams: {
    dispatchStatus: '',
    compStatus: ''
  }
})
const TableConfig = reactive<ITable>({
  dataList: [],
  indexVisible: true,
  columns: [
    { minWidth: 120, label: '任务名称', prop: 'name' },
    { minWidth: 120, label: '养护设备类型', prop: 'deviceName' },
    { minWidth: 120, label: '开始时间', prop: 'beginTime' },
    { minWidth: 120, label: '截止时间', prop: 'endTime' },
    { minWidth: 120, label: '创建人', prop: 'creatorName' },
    { minWidth: 120, label: '创建时间', prop: 'createTime' },
    { minWidth: 120, label: '养护人员', prop: 'maintainUserName' },
    {
      minWidth: 100,
      align: 'center',
      label: '任务状态',
      prop: 'status',
      tag: true,
      tagColor: (row): string => PatrolTaskStatusConfig[row.status]?.color,
      formatter: row => {
        return PatrolTaskStatusConfig[row.status]?.text || row.status
      }
    }
  ],
  handleSelectChange: rows => {
    TableConfig.selectList = rows
  },
  operationWidth: 220,
  operations: [
    {
      perm: true,
      text: '分派',
      svgIcon: shallowRef(Promotion),
      disabled: row => row.status !== 'PENDING',
      click: row => dispatchTask(row)
    },
    {
      perm: true,
      text: '详情',
      type: 'info',
      svgIcon: shallowRef(InfoFilled),
      click: row => {
        TableConfig.currentRow = row
        state.curPage = 'detail'
      }
    },
    {
      perm: true,
      text: '删除',
      type: 'danger',
      svgIcon: shallowRef(Delete),
      click: row => handleDelete(row)
    }
  ],
  pagination: {
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page || 1
      TableConfig.pagination.limit = size || 20
      refreshData()
    }
  }
})
const handleBack = () => {
  state.curPage = 'table'
}
const handleDelete = (row?: any) => {
  const rows = (row ? [row] : TableConfig.selectList) || []
  const ids = rows.map(item => item.id)
  if (!ids.length) {
    SLMessage.warning('请先选择要删除的数据')
    return
  }
  SLConfirm('确定删除？', '提示信息')
    .then(() => {
      DeleteMaintainTasks(ids)
        .then(res => {
          if (res.data.code === 200) {
            SLMessage.success(res.data.message)
            PostGisOperateLog({
              optionName: EGigLogFunc.XUNJIANRENWU,
              type: EGisLogApp.INSPECT,
              content: `${EGisLogOperateType.DELETE}养护任务：${rows
                .map(
                  item => item.name + (item.code ? '【' + item.code + '】' : '')
                )
                .join('、')}`,
              optionType: EGisLogOperateType.DELETE
            }).catch(() => {
              console.log('生成gis操作日志失败')
            })
            refreshData()
          } else {
            SLMessage.error(res.data.message)
          }
        })
        .catch(error => {
          console.log(error)

          SLMessage.error('删除失败')
        })
    })
    .catch(() => {
      //
    })
}
const DialogFormConfig = reactive<IDialogFormConfig>({
  title: '任务派发',
  dialogWidth: 500,
  labelPosition: 'right',
  group: [
    {
      fields: [
        {
          type: 'input',
          readonly: true,
          label: '任务名称',
          placeholder: ' ',
          field: 'name'
        },
        {
          type: 'datetimerange',
          label: '起止时间',
          field: 'beginTime',
          rules: [{ required: true, message: '请选择起止时间' }]
        },
        {
          type: 'user-select',
          label: '养护人员',
          field: 'maintainUser',
          departField: 'maintainUserDepartmentId',
          rules: [{ required: true, message: '请选择养护人员' }]
        },
        {
          type: 'textarea',
          label: '备注',
          field: 'remark'
        }
      ]
    }
  ],
  submit: params => {
    DispatchMaintainTask(TableConfig.currentRow.id, {
      ...params,
      maintainUser: params.maintainUser?.join(','),
      beginTime: params.beginTime?.[0],
      endTime: params.beginTime?.[1]
    })
      .then(res => {
        if (res.data.code === 200) {
          SLMessage.success(res.data.message)
          refreshData()
          refDialogForm.value?.closeDialog()
        } else {
          SLMessage.error(res.data.message)
        }
      })
      .catch(error => {
        console.log(error)
        SLMessage.error('系统错误')
      })
  }
})
const dispatchTask = async row => {
  TableConfig.currentRow = row
  DialogFormConfig.defaultValue = {
    name: row.name,
    beginTime: [
      moment().startOf('D').format(formatterDateTime),
      moment().endOf('D').format(formatterDateTime)
    ],
    maintainUserDepartmentId: row.maintainUserDepartmentId,
    maintainUser: row.maintainUser
  }
  refDialogForm.value?.openDialog()
  await nextTick()
  refDialogForm.value?.resetForm()
}
const refreshData = () => {
  const query = refSearch.value?.queryParams || {}
  GetMaintainTasks({
    page: TableConfig.pagination.page || 1,
    size: TableConfig.pagination.limit || 20,
    ...query,
    fromTime: query.fromTime?.[0],
    toTime: query.fromTime?.[1],
    creator: query.creator?.join(','),
    maintainUser: query.maintainUser?.join(',')
  }).then(res => {
    const data = res.data?.data
    TableConfig.dataList = data?.data || []
    TableConfig.pagination.total = data?.total || 0
  })
}
const getLayerInfo = async () => {
  state.layerIds = getSubLayerIds(staticState.view)
  const layerInfo = await queryLayerClassName(state.layerIds)
  state.layerInfos = layerInfo.data?.result?.rows || []
}

const onMaploaded = async view => {
  staticState.view = view
  refMap.value?.toggleCustomDetail(true)
  await nextTick()
  refMap.value?.toggleCustomDetailMaxmin('max')
  await getLayerInfo()
}
onMounted(() => {
  refreshData()
})
</script>
<style lang="scss" scoped>
.detail-page,
.page-wrapper {
  height: 100%;
}
.table-box {
  height: calc(100% - 36px);
}

.detail-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;

  .el-icon {
    cursor: pointer;
  }
  .detail-header-divider {
    width: 1px;
    height: 1em;
    border: none;
    background-color: var(--el-border-color);
    margin: 0 20px;
  }
}
.detail-main {
  padding-right: 8px;
  height: calc(100% - 36px);
}
</style>
