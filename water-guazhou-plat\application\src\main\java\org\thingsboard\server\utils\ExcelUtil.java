package org.thingsboard.server.utils;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.io.IOUtils;
import org.apache.poi.hpsf.SummaryInformation;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.streaming.SXSSFCell;
import org.apache.poi.xssf.streaming.SXSSFRow;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFDataValidation;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;
import org.thingsboard.server.dao.model.sql.install.GanRepair;
import org.thingsboard.server.dao.util.AesUtil;
import org.thingsboard.server.dimain.smartproduct.totalreport.primeCost.PrimeCost;
import org.thingsboard.server.service.utils.StringUtils;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * excelutil
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2021-06-28
 */
public class ExcelUtil {
    public static String NO_DEFINE = "no_define";// 未定义的字段

    public static String DEFAULT_DATE_PATTERN = "yyyy年MM月dd日HH时mm分ss秒";// 默认日期格式

    public static int DEFAULT_COLOUMN_WIDTH = 17;

    /**
     * 导出Excel 97(.xls)格式 ，少量数据
     *
     * @param title       标题行
     * @param headMap     属性-列名
     * @param jsonArray   数据集
     * @param datePattern 日期格式，null则用默认日期格式
     * @param colWidth    列宽 默认 至少17个字节
     * @param out         输出流
     */
    public static void exportExcel(String title, Map<String, String> headMap, JSONArray jsonArray, String datePattern, int colWidth, OutputStream out) {
        if (datePattern == null) datePattern = DEFAULT_DATE_PATTERN;
        // 声明一个工作薄
        HSSFWorkbook workbook = new HSSFWorkbook();
        workbook.createInformationProperties();
        workbook.getDocumentSummaryInformation().setCompany("*****公司");
        SummaryInformation si = workbook.getSummaryInformation();
        si.setAuthor("JACK");  // 填加xls文件作者信息
        si.setApplicationName("导出程序"); // 填加xls文件创建程序信息
        si.setLastAuthor("最后保存者信息"); // 填加xls文件最后保存者信息
        si.setComments("JACK is a programmer!"); // 填加xls文件作者信息
        si.setTitle("POI导出Excel"); // 填加xls文件标题信息
        si.setSubject("POI导出Excel");// 填加文件主题信息
        si.setCreateDateTime(new Date());
        // 表头样式
        HSSFCellStyle titleStyle = workbook.createCellStyle();
        HSSFFont titleFont = workbook.createFont();
        titleFont.setFontHeightInPoints((short) 20);
        titleStyle.setFont(titleFont);
        // 列头样式
        HSSFCellStyle headerStyle = workbook.createCellStyle();
        HSSFFont headerFont = workbook.createFont();
        headerFont.setFontHeightInPoints((short) 12);
        headerStyle.setFont(headerFont);
        // 单元格样式
        HSSFCellStyle cellStyle = workbook.createCellStyle();
        HSSFFont cellFont = workbook.createFont();
        cellStyle.setFont(cellFont);
        // 生成一个(带标题)表格
        HSSFSheet sheet = workbook.createSheet();
        // 声明一个画图的顶级管理器
        HSSFPatriarch patriarch = sheet.createDrawingPatriarch();
        // 定义注释的大小和位置,详见文档
        HSSFComment comment = patriarch.createComment(new HSSFClientAnchor(0,
                0, 0, 0, (short) 4, 2, (short) 6, 5));
        // 设置注释内容
        comment.setString(new HSSFRichTextString("可以在POI中添加注释！"));
        // 设置注释作者，当鼠标移动到单元格上是可以在状态栏中看到该内容.
        comment.setAuthor("JACK");
        // 设置列宽
        int minBytes = colWidth < DEFAULT_COLOUMN_WIDTH ? DEFAULT_COLOUMN_WIDTH : colWidth;// 至少字节数
        int[] arrColWidth = new int[headMap.size()];
        // 产生表格标题行,以及设置列宽
        String[] properties = new String[headMap.size()];
        String[] headers = new String[headMap.size()];
        int ii = 0;
        for (Iterator<String> iter = headMap.keySet().iterator(); iter
                .hasNext(); ) {
            String fieldName = iter.next();

            properties[ii] = fieldName;
            headers[ii] = fieldName;

            int bytes = fieldName.getBytes().length;
            arrColWidth[ii] = bytes < minBytes ? minBytes : bytes;
            sheet.setColumnWidth(ii, arrColWidth[ii] * 256);
            ii++;
        }
        // 遍历集合数据，产生数据行
        int rowIndex = 0;
        for (Object obj : jsonArray) {
            if (rowIndex == 65535 || rowIndex == 0) {
                if (rowIndex != 0) sheet = workbook.createSheet();// 如果数据超过了，则在第二页显示

                HSSFRow titleRow = sheet.createRow(0);// 表头 rowIndex=0
                titleRow.createCell(0).setCellValue(title);
                titleRow.getCell(0).setCellStyle(titleStyle);
                sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, headMap.size() - 1));

                HSSFRow headerRow = sheet.createRow(1); // 列头 rowIndex =1
                for (int i = 0; i < headers.length; i++) {
                    headerRow.createCell(i).setCellValue(headers[i]);
                    headerRow.getCell(i).setCellStyle(headerStyle);

                }
                rowIndex = 2;// 数据内容从 rowIndex=2开始
            }
            JSONObject jo = (JSONObject) JSONObject.toJSON(obj);
            HSSFRow dataRow = sheet.createRow(rowIndex);
            for (int i = 0; i < properties.length; i++) {
                HSSFCell newCell = dataRow.createCell(i);

                Object o = jo.get(properties[i]);
                String cellValue = "";
                if (o == null) cellValue = "";
                else if (o instanceof Date) cellValue = new SimpleDateFormat(datePattern).format(o);
                else cellValue = o.toString();

                newCell.setCellValue(cellValue);
                newCell.setCellStyle(cellStyle);
            }
            rowIndex++;
        }
        // 自动调整宽度
        /*for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
        }*/
        try {
            workbook.write(out);
            workbook.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 导出Excel 2007 OOXML (.xlsx)格式
     *
     * @param title        标题行
     * @param headMap      属性-列头
     * @param jsonArray    数据集
     * @param datePattern  日期格式，传null值则默认 年月日
     * @param colWidth     列宽 默认 至少17个字节
     * @param withoutTitle
     */
    public static void exportExcelX(String title, Map<String, String> headMap, JSONArray jsonArray, String datePattern, int colWidth, boolean withoutTitle, HttpServletResponse response) {
        if (datePattern == null) datePattern = DEFAULT_DATE_PATTERN;
        // 声明一个工作薄
        SXSSFWorkbook workbook = new SXSSFWorkbook(1000);// 缓存
        workbook.setCompressTempFiles(true);
        // 表头样式
        CellStyle titleStyle = workbook.createCellStyle();
        titleStyle.setBorderBottom(BorderStyle.MEDIUM);
        titleStyle.setBorderTop(BorderStyle.MEDIUM);
        titleStyle.setBorderLeft(BorderStyle.MEDIUM);
        titleStyle.setBorderRight(BorderStyle.MEDIUM);
        ;
        titleStyle.setAlignment(HorizontalAlignment.CENTER);
        Font titleFont = workbook.createFont();
        titleFont.setFontHeightInPoints((short) 20);
        titleStyle.setFont(titleFont);
        // 列头样式
        CellStyle headerStyle = workbook.createCellStyle();
        headerStyle.setBorderBottom(BorderStyle.MEDIUM);
        headerStyle.setBorderTop(BorderStyle.MEDIUM);
        headerStyle.setBorderLeft(BorderStyle.MEDIUM);
        headerStyle.setBorderRight(BorderStyle.MEDIUM);
        Font headerFont = workbook.createFont();
        headerFont.setFontHeightInPoints((short) 12);
        headerStyle.setFont(headerFont);
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        // 单元格样式
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        Font cellFont = workbook.createFont();
        cellStyle.setFont(cellFont);
        // 生成一个(带标题)表格
        SXSSFSheet sheet = workbook.createSheet();
        // 设置列宽
        int minBytes = colWidth < DEFAULT_COLOUMN_WIDTH ? DEFAULT_COLOUMN_WIDTH : colWidth;// 至少字节数
        int[] arrColWidth = new int[headMap.size()];
        // 产生表格标题行,以及设置列宽
        String[] properties = new String[headMap.size()];
        String[] headers = new String[headMap.size()];
        int ii = 0;
        for (Iterator<String> iter = headMap.keySet().iterator(); iter
                .hasNext(); ) {
            String fieldName = iter.next();

            properties[ii] = fieldName;
            headers[ii] = headMap.get(fieldName);

            int bytes = fieldName.getBytes().length;
            arrColWidth[ii] = bytes < minBytes ? minBytes : bytes;
            sheet.setColumnWidth(ii, arrColWidth[ii] * 256);
            ii++;
        }
        // 遍历集合数据，产生数据行
        int rowIndex = 0;
        if (!withoutTitle) {
            SXSSFRow titleRow = sheet.createRow(0);// 表头 rowIndex=0
            titleRow.createCell(0).setCellValue(title);
            titleRow.getCell(0).setCellStyle(titleStyle);
            if (headMap.size() > 1) {
                sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, headMap.size() - 1));
            }
        }

        SXSSFRow headerRow = sheet.createRow(withoutTitle ? 0 : 1); // 列头 rowIndex =1
        for (int i = 0; i < headers.length; i++) {
            headerRow.createCell(i).setCellValue(headers[i]);
            headerRow.getCell(i).setCellStyle(headerStyle);

        }
        rowIndex = withoutTitle ? 1 : 2;// 数据内容从 rowIndex=2开始
        for (Object obj : jsonArray) {
            if (rowIndex == 65535) {
                sheet = workbook.createSheet();// 如果数据超过了，则在第二页显示

                if (!withoutTitle) {
                    SXSSFRow titleRow = sheet.createRow(0);// 表头 rowIndex=0
                    titleRow.createCell(0).setCellValue(title);
                    titleRow.getCell(0).setCellStyle(titleStyle);
                    sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, headMap.size() - 1));
                }

                headerRow = sheet.createRow(withoutTitle ? 0 : 1); // 列头 rowIndex =1
                for (int i = 0; i < headers.length; i++) {
                    headerRow.createCell(i).setCellValue(headers[i]);
                    headerRow.getCell(i).setCellStyle(headerStyle);

                }
                rowIndex = withoutTitle ? 1 : 2;// 数据内容从 rowIndex=2开始
            }
            JSONObject jo = (JSONObject) JSONObject.toJSON(obj);
            SXSSFRow dataRow = sheet.createRow(rowIndex);
            for (int i = 0; i < properties.length; i++) {
                SXSSFCell newCell = dataRow.createCell(i);

                Object o = jo.get(properties[i]);
                String cellValue = "";
                if (o == null) cellValue = "";
                else if (o instanceof Date) cellValue = new SimpleDateFormat(datePattern).format(o);
                else if (o instanceof Long) cellValue = new SimpleDateFormat(datePattern).format(new Date((Long) o));
                else if (o instanceof Float || o instanceof Double)
                    cellValue = new BigDecimal(o.toString()).setScale(2, BigDecimal.ROUND_HALF_UP).toString();
                else cellValue = o.toString();

                newCell.setCellValue(cellValue);
                newCell.setCellStyle(cellStyle);
            }
            rowIndex++;
        }
        // 自动调整宽度
        /*for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
        }*/
        try {
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            workbook.write(out);
            workbook.close();
            workbook.dispose();

            exportExcel(title, out, response);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    /**
     * 导出Excel 2007 OOXML (.xlsx)格式
     * <p>
     * 产销差报表
     *
     * @param jsonArray   数据集
     * @param datePattern 日期格式，传null值则默认 年月日
     * @param colWidth    列宽 默认 至少17个字节
     */
    public static void totalDifferenceExport(JSONArray jsonArray, String datePattern, int colWidth, HttpServletResponse response) {

        if (datePattern == null) datePattern = DEFAULT_DATE_PATTERN;
        // 数据表头
        // 声明一个工作薄
        SXSSFWorkbook workbook = new SXSSFWorkbook(1000);// 缓存
        workbook.setCompressTempFiles(true);
        // 表头样式
        CellStyle titleStyle = workbook.createCellStyle();
        titleStyle.setBorderBottom(BorderStyle.MEDIUM);
        titleStyle.setBorderTop(BorderStyle.MEDIUM);
        titleStyle.setBorderLeft(BorderStyle.MEDIUM);
        titleStyle.setBorderRight(BorderStyle.MEDIUM);
        ;
        titleStyle.setAlignment(HorizontalAlignment.CENTER);
        Font titleFont = workbook.createFont();
        titleFont.setFontHeightInPoints((short) 20);
        titleStyle.setFont(titleFont);
        // 列头样式
        CellStyle headerStyle = workbook.createCellStyle();
        headerStyle.setBorderBottom(BorderStyle.MEDIUM);
        headerStyle.setBorderTop(BorderStyle.MEDIUM);
        headerStyle.setBorderLeft(BorderStyle.MEDIUM);
        headerStyle.setBorderRight(BorderStyle.MEDIUM);
        Font headerFont = workbook.createFont();
        headerFont.setFontHeightInPoints((short) 12);
        headerStyle.setFont(headerFont);
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 单元格样式
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        Font cellFont = workbook.createFont();
        cellStyle.setFont(cellFont);

        Map<String, String> headMap = new LinkedHashMap<>();
        headMap.put("partitionName", "分区名称");
        headMap.put("needCopyNum", "应抄户");
        headMap.put("realCopyNum", "实抄户");
        headMap.put("notCopyNum", "未抄户");
        headMap.put("copyRate", "抄表率(%)");
        headMap.put("supplyTotal", "供水量");
        headMap.put("realCopyWater", "实抄水量");
        headMap.put("nrwWater", "产销差水量");
        headMap.put("nrwRate", "产销差");
        headMap.put("correctSupplyTotal", "校准供水量");
        headMap.put("correctCopyWater", "校准用水量");
        headMap.put("correctNrwWater", "校准产销差水量");
        headMap.put("correctNrwRate", "校准产销差");
        headMap.put("referenceLossWater", "参考漏失水量");
        headMap.put("faceLossWater", "表观漏失水量");

        // 生成一个(带标题)表格
        SXSSFSheet sheet = workbook.createSheet();
        // 设置列宽
        int minBytes = colWidth < DEFAULT_COLOUMN_WIDTH ? DEFAULT_COLOUMN_WIDTH : colWidth;// 至少字节数
        int[] arrColWidth = new int[headMap.size()];
        // 产生表格标题行,以及设置列宽
        String[] properties = new String[headMap.size()];
        String[] headers = new String[headMap.size()];
        int ii = 0;
        for (Iterator<String> iter = headMap.keySet().iterator(); iter.hasNext(); ) {
            String fieldName = iter.next();

            properties[ii] = fieldName;
            headers[ii] = headMap.get(fieldName);

            int bytes = fieldName.getBytes().length;
            arrColWidth[ii] = bytes < minBytes ? minBytes : bytes;
            sheet.setColumnWidth(ii, arrColWidth[ii] * 256);
            ii++;
        }
        // 遍历集合数据，产生数据行
        int rowIndex = 0;
        String[] oneHeader = {"抄表详情", "实抄水量分析", "校准水量分析", "表观漏失分析"};
        if (rowIndex != 0) sheet = workbook.createSheet();// 如果数据超过了，则在第二页显示
        SXSSFRow titleRow = sheet.createRow(0);// 表头 rowIndex=0
        titleRow.createCell(0).setCellValue("总分差评估表");
        titleRow.getCell(0).setCellStyle(titleStyle);
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, headMap.size() - 1));

        SXSSFRow row = sheet.createRow(1);
        for (int i = 0; i < 15; i++) {
            row.createCell(i);
            row.getCell(i).setCellStyle(headerStyle);
        }
        row.getCell(0).setCellValue("分区名称");
        row.getCell(1).setCellValue(oneHeader[0]);
        sheet.addMergedRegion(new CellRangeAddress(1, 1, 1, 4));
        row.getCell(5).setCellValue(oneHeader[1]);
        sheet.addMergedRegion(new CellRangeAddress(1, 1, 5, 8));
        row.getCell(9).setCellValue(oneHeader[2]);
        sheet.addMergedRegion(new CellRangeAddress(1, 1, 9, 12));
        row.getCell(13).setCellValue(oneHeader[3]);
        sheet.addMergedRegion(new CellRangeAddress(1, 1, 13, 14));

        SXSSFRow headerRow = sheet.createRow(2); // 列头 rowIndex =1
        for (int i = 0; i < headers.length; i++) {
            headerRow.createCell(i).setCellValue(headers[i]);
            headerRow.getCell(i).setCellStyle(headerStyle);

        }
        sheet.addMergedRegion(new CellRangeAddress(1, 2, 0, 0));
        rowIndex = 3;
        for (Object obj : jsonArray) {
            if (rowIndex == 65535) {
                sheet = workbook.createSheet();// 如果数据超过了，则在第二页显示
                titleRow = sheet.createRow(0);// 表头 rowIndex=0
                titleRow.createCell(0).setCellValue("总分差评估表");
                titleRow.getCell(0).setCellStyle(titleStyle);
                sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, headMap.size() - 1));

                row.createCell(0).setCellValue("分区名称");
                row.getCell(0).setCellStyle(headerStyle);
                row.createCell(1).setCellValue(oneHeader[0]);
                sheet.addMergedRegion(new CellRangeAddress(1, 1, 1, 4));
                row.getCell(1).setCellStyle(headerStyle);
                row.getCell(2).setCellStyle(headerStyle);
                row.getCell(3).setCellStyle(headerStyle);
                row.getCell(4).setCellStyle(headerStyle);
                row.createCell(5).setCellValue(oneHeader[1]);
                sheet.addMergedRegion(new CellRangeAddress(1, 1, 5, 8));
                row.getCell(5).setCellStyle(headerStyle);
                row.getCell(6).setCellStyle(headerStyle);
                row.getCell(7).setCellStyle(headerStyle);
                row.getCell(8).setCellStyle(headerStyle);
                row.createCell(9).setCellValue(oneHeader[2]);
                sheet.addMergedRegion(new CellRangeAddress(1, 1, 9, 12));
                row.getCell(9).setCellStyle(headerStyle);
                row.getCell(10).setCellStyle(headerStyle);
                row.getCell(11).setCellStyle(headerStyle);
                row.getCell(12).setCellStyle(headerStyle);
                row.createCell(13).setCellValue(oneHeader[3]);
                row.getCell(13).setCellStyle(headerStyle);
                row.getCell(14).setCellStyle(headerStyle);
                sheet.addMergedRegion(new CellRangeAddress(1, 1, 13, 14));

                headerRow = sheet.createRow(2); // 列头 rowIndex =1
                for (int i = 0; i < headers.length; i++) {
                    headerRow.createCell(i).setCellValue(headers[i]);
                    headerRow.getCell(i).setCellStyle(headerStyle);

                }
                sheet.addMergedRegion(new CellRangeAddress(0, 1, 0, 0));

                rowIndex = 3;// 数据内容从 rowIndex=2开始
            }
            JSONObject jo = (JSONObject) JSONObject.toJSON(obj);
            SXSSFRow dataRow = sheet.createRow(rowIndex);
            for (int i = 0; i < properties.length; i++) {
                SXSSFCell newCell = dataRow.createCell(i);

                Object o = jo.get(properties[i]);
                String cellValue = "";
                if (o == null) cellValue = "";
                else if (o instanceof Date) cellValue = new SimpleDateFormat(datePattern).format(o);
                else if (o instanceof Float || o instanceof Double)
                    cellValue = new BigDecimal(o.toString()).setScale(2, BigDecimal.ROUND_HALF_UP).toString();
                else cellValue = o.toString();

                newCell.setCellValue(cellValue);
                newCell.setCellStyle(cellStyle);
            }
            rowIndex++;
        }
        // 自动调整宽度
        /*for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
        }*/
        try {
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            workbook.write(os);
            workbook.close();
            workbook.dispose();

            ExcelUtil.exportExcel("总分差评估表", os, response);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 导出Excel 2007 OOXML (.xlsx)格式  多sheet
     *
     * @param title       标题行
     * @param headMap     属性-列头
     * @param jsonArray   数据集
     * @param datePattern 日期格式，传null值则默认 年月日
     * @param colWidth    列宽 默认 至少17个字节
     */
    public static void exportExcelXManySheet(String title, Map<String, String> headMap, JSONArray jsonArray, String datePattern, int colWidth, String sheetName, SXSSFWorkbook workbook) {
        if (datePattern == null) datePattern = DEFAULT_DATE_PATTERN;
        // 声明一个工作薄
        if (workbook == null) {
            workbook = new SXSSFWorkbook(1000);// 缓存
        }
        workbook.setCompressTempFiles(true);
        // 表头样式
        CellStyle titleStyle = workbook.createCellStyle();
        titleStyle.setBorderBottom(BorderStyle.MEDIUM);
        titleStyle.setBorderTop(BorderStyle.MEDIUM);
        titleStyle.setBorderLeft(BorderStyle.MEDIUM);
        titleStyle.setBorderRight(BorderStyle.MEDIUM);
        ;
        titleStyle.setAlignment(HorizontalAlignment.CENTER);
        Font titleFont = workbook.createFont();
        titleFont.setFontHeightInPoints((short) 20);
        titleStyle.setFont(titleFont);
        // 列头样式
        CellStyle headerStyle = workbook.createCellStyle();
        headerStyle.setBorderBottom(BorderStyle.MEDIUM);
        headerStyle.setBorderTop(BorderStyle.MEDIUM);
        headerStyle.setBorderLeft(BorderStyle.MEDIUM);
        headerStyle.setBorderRight(BorderStyle.MEDIUM);
        Font headerFont = workbook.createFont();
        headerFont.setFontHeightInPoints((short) 12);
        headerStyle.setFont(headerFont);
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        // 单元格样式
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        Font cellFont = workbook.createFont();
        cellStyle.setFont(cellFont);
        // 生成一个(带标题)表格
        SXSSFSheet sheet = workbook.createSheet(sheetName);
        // 设置列宽
        int minBytes = colWidth < DEFAULT_COLOUMN_WIDTH ? DEFAULT_COLOUMN_WIDTH : colWidth;// 至少字节数
        int[] arrColWidth = new int[headMap.size()];
        // 产生表格标题行,以及设置列宽
        String[] properties = new String[headMap.size()];
        String[] headers = new String[headMap.size()];
        int ii = 0;
        for (Iterator<String> iter = headMap.keySet().iterator(); iter.hasNext(); ) {
            String fieldName = iter.next();

            properties[ii] = fieldName;
            headers[ii] = headMap.get(fieldName);

            int bytes = fieldName.getBytes().length;
            arrColWidth[ii] = bytes < minBytes ? minBytes : bytes;
            sheet.setColumnWidth(ii, arrColWidth[ii] * 256);
            ii++;
        }
        SXSSFRow titleRow;
        SXSSFRow headerRow;
        if (StringUtils.checkNotNull(title)) {
            titleRow = sheet.createRow(0);// 表头 rowIndex=0
            titleRow.createCell(0).setCellValue(title);
            titleRow.getCell(0).setCellStyle(titleStyle);
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, headMap.size() - 1));

            headerRow = sheet.createRow(1); // 列头 rowIndex =1
            for (int i = 0; i < headers.length; i++) {
                headerRow.createCell(i).setCellValue(headers[i]);
                headerRow.getCell(i).setCellStyle(headerStyle);

            }
        } else {
            headerRow = sheet.createRow(0); // 列头 rowIndex =1
            for (int i = 0; i < headers.length; i++) {
                headerRow.createCell(i).setCellValue(headers[i]);
                headerRow.getCell(i).setCellStyle(headerStyle);

            }
        }

        // 遍历集合数据，产生数据行
        int rowIndex = 0;
        for (Object obj : jsonArray) {
            if (rowIndex == 65535) {
                if (rowIndex != 0) sheet = workbook.createSheet();// 如果数据超过了，则在第二页显示
                if (StringUtils.checkNotNull(title)) {
                    titleRow = sheet.createRow(0);// 表头 rowIndex=0
                    titleRow.createCell(0).setCellValue(title);
                    titleRow.getCell(0).setCellStyle(titleStyle);
                    sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, headMap.size() - 1));

                    headerRow = sheet.createRow(1); // 列头 rowIndex =1
                    for (int i = 0; i < headers.length; i++) {
                        headerRow.createCell(i).setCellValue(headers[i]);
                        headerRow.getCell(i).setCellStyle(headerStyle);

                    }
                    rowIndex = 2;// 数据内容从 rowIndex=2开始
                } else {
                    headerRow = sheet.createRow(0); // 列头 rowIndex =1
                    for (int i = 0; i < headers.length; i++) {
                        headerRow.createCell(i).setCellValue(headers[i]);
                        headerRow.getCell(i).setCellStyle(headerStyle);

                    }
                    rowIndex = 1;// 数据内容从 rowIndex=2开始
                }

            }
            if (rowIndex == 0) {
                rowIndex = 2;
            }
            JSONObject jo = (JSONObject) JSONObject.toJSON(obj);
            SXSSFRow dataRow = sheet.createRow(rowIndex);
            for (int i = 0; i < properties.length; i++) {
                SXSSFCell newCell = dataRow.createCell(i);

                Object o = jo.get(properties[i]);
                String cellValue = "";
                if (o == null) cellValue = "";
                else if (o instanceof Date) cellValue = new SimpleDateFormat(datePattern).format(o);
                else if (o instanceof Float || o instanceof Double)
                    cellValue = new BigDecimal(o.toString()).setScale(2, BigDecimal.ROUND_HALF_UP).toString();
                else cellValue = o.toString();

                newCell.setCellValue(cellValue);
                newCell.setCellStyle(cellStyle);
            }
            rowIndex++;
        }
        // 自动调整宽度
        /*for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
        }*/

    }

    public static void downloadExcelFile(String title, Map<String, String> headMap, JSONArray ja, HttpServletResponse response) {
        downloadExcelFile(title, headMap, ja, response, false);
    }

    // Web 导出excel
    public static void downloadExcelFile(String title, Map<String, String> headMap, JSONArray ja, HttpServletResponse response, boolean withoutTitle) {
        try {
            ExcelUtil.exportExcelX(title, headMap, ja, null, 0, withoutTitle, response);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void exportExcel(String title, ByteArrayOutputStream os, HttpServletResponse response) {
        try {
            byte[] content = os.toByteArray();
            InputStream is = new ByteArrayInputStream(content);
            // 设置response参数，可以打开下载页面
            response.reset();

            title = title + "_" + new SimpleDateFormat(DEFAULT_DATE_PATTERN).format(new Date());

            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(title + ".xlsx", "utf-8"));
            response.setHeader("Access-Control-Allow-Origin", "*");
            response.setContentLength(content.length);
            ServletOutputStream outputStream = response.getOutputStream();
            BufferedInputStream bis = new BufferedInputStream(is);
            BufferedOutputStream bos = new BufferedOutputStream(outputStream);
            byte[] buff = new byte[8192];
            int bytesRead;
            while (-1 != (bytesRead = bis.read(buff, 0, buff.length))) {
                bos.write(buff, 0, bytesRead);

            }
            bis.close();
            bos.close();
            outputStream.flush();
            outputStream.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void exportXJDetail(String templatePath, String title, Map<String, Object> detail, HttpServletResponse response) throws IOException {
        // 数据处理
        List<Map<String, Object>> fileMapList = new ArrayList<>();

        RestTemplate restTemplate = new RestTemplate();

        // 任务类型
        if (detail.get("type") == null) {
            detail.put("type", "未设置");
        }
        if ("1".equals(detail.get("type"))) {
            detail.put("type", "人工巡检");
        }
        if ("2".equals(detail.get("type"))) {
            detail.put("type", "自动巡检");
        }

        // 执行时间
        if (detail.get("executeTime") != null) {
            detail.put("executeTime", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date(Long.valueOf(String.valueOf(detail.get("executeTime"))))));
        } else {
            detail.put("executeTime", "未设置");
        }

        // 限制时间
        // 执行提醒
        if (detail.get("limitTime") == null) {
            detail.put("limitTime", "-");
        }

        // 执行提醒
        if (detail.get("isNotice") == null) {
            detail.put("isNotice", "未设置");
        }
        switch (String.valueOf(detail.get("isNotice"))) {
            case "0":
                detail.put("isNotice", "不提醒");
                break;
            case "1":
                detail.put("isNotice", "30分钟");
                break;
            case "2":
                detail.put("isNotice", "60分钟");
                break;
            case "3":
                detail.put("isNotice", "90分钟");
                break;
        }

        // 任务列表
        List<Map> jobList = (List<Map>) detail.get("jobList");

        Map fileMap;
        for (Map map : jobList) {
            if (map.get("deviceName") == null) {
                map.put("deviceName", "");
            }
            // 完成状态 状态 0：未完成；1：已完成；2：处理中；3：记录故障
            if (map.get("status") == null) {
                map.put("status", "-");
            }
            String status = String.valueOf(map.get("status"));
            switch (status) {
                case "0":
                    map.put("status", "未完成");
                    break;
                case "1":
                    map.put("status", "已完成");
                    break;
                case "2":
                    map.put("status", "处理中");
                    break;
                case "3":
                    map.put("status", "记录故障");
                    break;
            }

            // 任务人
            if (map.get("userName") == null) {
                map.put("userName", "");
            }

            // 完工时间
            if (map.get("endTime") == null) {
                map.put("endTime", "-");
            } else {
                map.put("endTime", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date(((Timestamp) map.get("endTime")).getTime())));
            }

            // 工作时间
            if (map.get("duration") == null) {
                map.put("duration", "-");
            }

            // 所属区域
            if (map.get("rangeName") == null) {
                map.put("rangeName", "-");
            }
            // 所属硐室
            if (map.get("projectName") == null) {
                map.put("projectName", "-");
            }

            // 上传图片
            String dirName = (String) map.get("deviceName");
            FileOutputStream fileOutputStream = null;
            if (map.get("imgs") != null) {
                String[] imgArr = String.valueOf(map.get("imgs")).split(",");
                for (String img : imgArr) {
                    if (!StringUtils.checkNotNull(img)) {
                        continue;
                    }
                    try {
                        fileMap = new HashMap();
                        fileMap.put("dir", dirName);
                        File file = File.createTempFile(System.currentTimeMillis() + "", img.substring(img.lastIndexOf(".")));
                        ResponseEntity<byte[]> exchange = restTemplate.exchange(img, HttpMethod.GET, null, byte[].class);
                        fileOutputStream = new FileOutputStream(file);
                        fileOutputStream.write(exchange.getBody());
                        fileMap.put("file", file);
                        fileMapList.add(fileMap);

                        fileOutputStream.close();
                    } catch (Exception e) {
                        if (fileOutputStream != null) {

                            fileOutputStream.close();
                        }
                        e.printStackTrace();
                    }
                }
            }
            // 声音
            if (map.get("voiceFile") != null) {
                String[] voiceFiles = String.valueOf(map.get("voiceFile")).split(",");
                for (String voice : voiceFiles) {
                    if (!StringUtils.checkNotNull(voice)) {
                        continue;
                    }
                    try {
                        fileMap = new HashMap();
                        fileMap.put("dir", dirName);
                        File file = File.createTempFile(System.currentTimeMillis() + "", voice.substring(voice.lastIndexOf(".")));
                        ResponseEntity<byte[]> exchange = restTemplate.exchange(voice, HttpMethod.GET, null, byte[].class);
                        fileOutputStream = new FileOutputStream(file);
                        fileOutputStream.write(exchange.getBody());
                        fileMap.put("file", file);
                        fileMapList.add(fileMap);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }

            // 步骤列表
            List<Map> stepList = (List<Map>) map.get("stepList");

            if (stepList == null) {
                map.put("stepList", new ArrayList<>());
            }

            for (Map stepMap : stepList) {
                if (stepMap.get("orderNum") == null) {
                    stepMap.put("orderNum", "-");
                }
                if (stepMap.get("jobContent") == null) {
                    stepMap.put("jobContent", "-");
                }

                stepMap.put("jobContent", map.get("deviceName") + "(编码：" + map.get("deviceCode") + ") 指令：");

            }
        }
        // 导出
        ClassPathResource resource = new ClassPathResource(templatePath);
        File excelTempleteFile = File.createTempFile(title + "-", ".xlsx");

        IOUtils.copy(resource.getInputStream(), new FileOutputStream(excelTempleteFile));

        TemplateExportParams exportParams = new TemplateExportParams(excelTempleteFile.getAbsolutePath());

        // Workbook workbook = ExcelExportUtil.exportExcel(exportParams, detail);
        Map<Integer, List<Map<String, Object>>> realMap = new HashMap<>();

        int i = 0;
        Map tempMap;
        for (Map map : jobList) {
            detail.putAll(map);
            tempMap = new HashMap();
            tempMap.putAll(detail);
            List list = new ArrayList();
            list.add(tempMap);
            realMap.put(i, list);
            i++;
        }
        Workbook workbook = ExcelExportUtil.exportExcelClone(realMap, exportParams);


        for (int j = 0; j < i; j++) {
            Sheet sheetAt = workbook.getSheetAt(j);
            if (sheetAt != null) {
                workbook.setSheetName(j, realMap.get(j).get(0).get("deviceName") + "报告");
            }
        }
        // 输出到文件
        workbook.write(new FileOutputStream(excelTempleteFile));

        fileMap = new HashMap();
        fileMap.put("dir", "");
        fileMap.put("file", excelTempleteFile);
        fileMapList.add(fileMap);

        CompressFilesUtil.compressToZipWithDir(fileMapList, title, response, title);
    }

    /**
     * 营收系统批量立户模板
     *
     * @param custParams
     * @param response
     */
    public static void getCustUploadTemplate(Map<String, List<String>> custParams, HttpServletResponse response) {
        XSSFWorkbook workbook = null;
        try {
            workbook = new XSSFWorkbook(new ExcelUtil().getClass().getResourceAsStream("/templates/营收系统批量立户模板.xlsx"));
        } catch (IOException e) {
            e.printStackTrace();
            return;
        }
        String[] mainCodeArr = {"WaterCategoryType", "IndustryCategoryType", "UserType", "MeterBrand", "MeterType", "MeterCaliber", "price",
                "PaymentMethod", "BankType", "additionalPrice", "liquidated", "ladder", "MeterPositionType", "meterWell"};
        int[] indexArr = {7, 8, 9, 13, 14, 17, 19, 20, 21, 23, 26, 27, 31, 32};
        XSSFSheet sheet = workbook.getSheetAt(0);
        // 设置下拉框格式
        CellRangeAddressList regions;

        int sheetIndex = 1;
        for (int i = 0; i < indexArr.length; i++) {
            regions = new CellRangeAddressList(2, 998, indexArr[i], indexArr[i]);
            String[] paramArr = new String[custParams.get(mainCodeArr[i]).size()];
            if (paramArr.length == 0 || paramArr.length < 26) {
                dropDownBox(sheet, custParams.get(mainCodeArr[i]), regions);
                continue;
            }
            setLongHSSFValidation(workbook, custParams.get(mainCodeArr[i]).toArray(paramArr), sheet, 3, paramArr.length, indexArr[i], sheetIndex++, regions);
        }


        try {
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            workbook.write(os);
            workbook.close();
            byte[] content = os.toByteArray();
            InputStream is = new ByteArrayInputStream(content);
            // 设置response参数，可以打开下载页面
            response.reset();

            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("营收系统批量立户模板" + ".xlsx", "utf-8"));
            response.setHeader("Access-Control-Allow-Origin", "*");
            response.setContentLength(content.length);
            ServletOutputStream outputStream = response.getOutputStream();
            BufferedInputStream bis = new BufferedInputStream(is);
            BufferedOutputStream bos = new BufferedOutputStream(outputStream);
            byte[] buff = new byte[8192];
            int bytesRead;
            while (-1 != (bytesRead = bis.read(buff, 0, buff.length))) {
                bos.write(buff, 0, bytesRead);

            }
            bis.close();
            bos.close();
            outputStream.flush();
            outputStream.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 单元格设置下拉框格式
     */
    public static void dropDownBox(XSSFSheet sheet, List<String> datas, CellRangeAddressList regions) {
        DataValidationHelper dataValidationHelper = sheet.getDataValidationHelper();
        if (datas.size() == 0) {
            datas.add("暂无");
        }
        DataValidationConstraint createExplicitListConstraint = dataValidationHelper.createExplicitListConstraint(datas.toArray(new String[datas.size()]));
        DataValidation createValidation = dataValidationHelper.createValidation(createExplicitListConstraint, regions);

        if (createValidation instanceof XSSFDataValidation) {
            createValidation.setSuppressDropDownArrow(true);
            createValidation.setShowErrorBox(true);
        } else {
            createValidation.setSuppressDropDownArrow(false);
        }
        sheet.addValidationData(createValidation);
    }

    /**
     * 解决下拉框过长不显示问题
     *
     * @param workbook
     * @param deptList   下拉数据数组
     * @param sheet
     * @param firstRow   开始行
     * @param endRow     结束行
     * @param cellNum    下拉框所在的列
     * @param sheetIndex 隐藏sheet名称
     */
    public static void setLongHSSFValidation(XSSFWorkbook workbook, String[] deptList, XSSFSheet sheet, int firstRow, int endRow, int cellNum, int sheetIndex, CellRangeAddressList regions) {
        String hiddenName = "hidden" + cellNum;
        // 1.创建隐藏的sheet页。
        XSSFSheet hidden = workbook.createSheet(hiddenName);
        // 2.循环赋值（为了防止下拉框的行数与隐藏域的行数相对应，将隐藏域加到结束行之后）
        for (int i = 0, length = deptList.length; i < length; i++) {
            hidden.createRow(endRow + i).createCell(cellNum).setCellValue(deptList[i]);
        }
        Name category1Name = workbook.createName();
        category1Name.setNameName(hiddenName);
        // 3 A1:A代表隐藏域创建第N列createCell(N)时。以A1列开始A行数据获取下拉数组
        category1Name.setRefersToFormula(hiddenName + "!A1:A" + (deptList.length + endRow));
        //
        DataValidationHelper helper = sheet.getDataValidationHelper();
        DataValidationConstraint constraint = helper.createFormulaListConstraint(hiddenName);
        DataValidation dataValidation = helper.createValidation(constraint, regions);
        if (dataValidation instanceof XSSFDataValidation) {
            // 数据校验
            dataValidation.setSuppressDropDownArrow(true);
            dataValidation.setShowErrorBox(true);
        } else {
            dataValidation.setSuppressDropDownArrow(false);
        }
        // 作用在目标sheet上
        sheet.addValidationData(dataValidation);
        // 设置hiddenSheet隐藏
        workbook.setSheetHidden(sheetIndex, true);
    }

    public static void getPartitionCustTemplate(HttpServletResponse response) {
        XSSFWorkbook workbook = null;
        try {
            workbook = new XSSFWorkbook(new ExcelUtil().getClass().getResourceAsStream("/templates/分区用户挂接模板.xlsx"));
        } catch (IOException e) {
            e.printStackTrace();
            return;
        }
        try {
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            workbook.write(os);
            workbook.close();
            byte[] content = os.toByteArray();
            InputStream is = new ByteArrayInputStream(content);
            // 设置response参数，可以打开下载页面
            response.reset();

            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("分区用户挂接模板" + ".xlsx", "utf-8"));
            response.setHeader("Access-Control-Allow-Origin", "*");
            response.setContentLength(content.length);
            ServletOutputStream outputStream = response.getOutputStream();
            BufferedInputStream bis = new BufferedInputStream(is);
            BufferedOutputStream bos = new BufferedOutputStream(outputStream);
            byte[] buff = new byte[8192];
            int bytesRead;
            while (-1 != (bytesRead = bis.read(buff, 0, buff.length))) {
                bos.write(buff, 0, bytesRead);

            }
            bis.close();
            bos.close();
            outputStream.flush();
            outputStream.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void getNrwReport(String title, JSONObject nrwReport, HttpServletResponse response) {
        String datePattern = "yyyyMMddHHmmss";
        // 数据表头
        // 声明一个工作薄
        SXSSFWorkbook workbook = new SXSSFWorkbook(1000);// 缓存
        workbook.setCompressTempFiles(true);
        // 表头样式
        CellStyle titleStyle = workbook.createCellStyle();
        titleStyle.setBorderBottom(BorderStyle.MEDIUM);
        titleStyle.setBorderTop(BorderStyle.MEDIUM);
        titleStyle.setBorderLeft(BorderStyle.MEDIUM);
        titleStyle.setBorderRight(BorderStyle.MEDIUM);
        ;
        titleStyle.setAlignment(HorizontalAlignment.CENTER);
        Font titleFont = workbook.createFont();
        titleFont.setFontHeightInPoints((short) 20);
        titleStyle.setFont(titleFont);
        // 列头样式
        CellStyle headerStyle = workbook.createCellStyle();
        headerStyle.setBorderBottom(BorderStyle.MEDIUM);
        headerStyle.setBorderTop(BorderStyle.MEDIUM);
        headerStyle.setBorderLeft(BorderStyle.MEDIUM);
        headerStyle.setBorderRight(BorderStyle.MEDIUM);
        Font headerFont = workbook.createFont();
        headerFont.setFontHeightInPoints((short) 12);
        headerStyle.setFont(headerFont);
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 单元格样式
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        Font cellFont = workbook.createFont();
        cellStyle.setFont(cellFont);

        Map<String, String> headMap = new LinkedHashMap<>();
        headMap.put("partitionName", "分区名称");
        if (nrwReport != null && nrwReport.get("header") != null) {
            JSONArray headerArray = nrwReport.getJSONArray("header");

            for (Object header : headerArray) {
                headMap.put(String.valueOf(header), String.valueOf(header));
            }
        }

        // 生成一个(带标题)表格
        SXSSFSheet sheet = workbook.createSheet();
        // 设置列宽
        int minBytes = 20 < DEFAULT_COLOUMN_WIDTH ? DEFAULT_COLOUMN_WIDTH : 20;// 至少字节数
        int[] arrColWidth = new int[headMap.size()];
        // 产生表格标题行,以及设置列宽
        String[] properties = new String[headMap.size()];
        String[] headers = new String[headMap.size()];
        int ii = 0;
        SXSSFRow row = sheet.createRow(1);
        for (Iterator<String> iter = headMap.keySet().iterator(); iter.hasNext(); ) {
            String fieldName = iter.next();

            properties[ii] = fieldName;
            headers[ii] = headMap.get(fieldName);

            int bytes = fieldName.getBytes().length;
            arrColWidth[ii] = bytes < minBytes ? minBytes : bytes;
            sheet.setColumnWidth(ii, arrColWidth[ii] * 256);

            row.createCell(ii);
            row.getCell(ii).setCellValue(headers[ii]);
            row.getCell(ii).setCellStyle(headerStyle);
            ii++;
        }
        // 遍历集合数据，产生数据行
        int rowIndex = 0;
        if (rowIndex != 0) sheet = workbook.createSheet();// 如果数据超过了，则在第二页显示
        SXSSFRow titleRow = sheet.createRow(0);// 表头 rowIndex=0
        titleRow.createCell(0).setCellValue(title);
        titleRow.getCell(0).setCellStyle(titleStyle);
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, headMap.size() - 1));

        rowIndex = 2;
        for (Object obj : nrwReport.getJSONArray("data")) {
            if (rowIndex == 65535) {
                sheet = workbook.createSheet();// 如果数据超过了，则在第二页显示
                titleRow = sheet.createRow(0);// 表头 rowIndex=0
                titleRow.createCell(0).setCellValue("总分差评估表");
                titleRow.getCell(0).setCellStyle(titleStyle);
                sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, headMap.size() - 1));

                rowIndex = 2;// 数据内容从 rowIndex=2开始
            }
            SXSSFRow dataRow = sheet.createRow(rowIndex);
            SXSSFCell newCell = dataRow.createCell(0);
            newCell.setCellValue(((JSONObject) obj).getString("name"));
            newCell.setCellStyle(cellStyle);

            JSONArray dataJSONArray = ((JSONObject) obj).getJSONArray("data");
            for (int j = 0; j < dataJSONArray.size(); j++) {
                newCell = dataRow.createCell(j + 1);
                Object o = dataJSONArray.get(j);
                String cellValue = "";
                if (o == null) cellValue = "";
                else if (o instanceof Date) cellValue = new SimpleDateFormat(datePattern).format(o);
                else if (o instanceof Float || o instanceof Double)
                    cellValue = new BigDecimal(o.toString()).setScale(2, BigDecimal.ROUND_HALF_UP).toString();
                else cellValue = o.toString();

                newCell.setCellValue(cellValue);
                newCell.setCellStyle(cellStyle);
            }

            rowIndex++;
        }
        // 自动调整宽度
        /*for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
        }*/
        try {
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            workbook.write(os);
            workbook.close();
            workbook.dispose();

            ExcelUtil.exportExcel(title, os, response);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static void getReportExport(List<JSONObject> headerList, List<JSONObject> dataList, HttpServletResponse response, String type) {

        String datePattern = "yyyyMMddHHmmss";
        // 数据表头
        // 声明一个工作薄
        SXSSFWorkbook workbook = new SXSSFWorkbook(1000);// 缓存
        workbook.setCompressTempFiles(true);
        // 表头样式
        CellStyle titleStyle = workbook.createCellStyle();
        titleStyle.setBorderBottom(BorderStyle.MEDIUM);
        titleStyle.setBorderTop(BorderStyle.MEDIUM);
        titleStyle.setBorderLeft(BorderStyle.MEDIUM);
        titleStyle.setBorderRight(BorderStyle.MEDIUM);
        ;
        titleStyle.setAlignment(HorizontalAlignment.CENTER);
        Font titleFont = workbook.createFont();
        titleFont.setFontHeightInPoints((short) 20);
        titleStyle.setFont(titleFont);
        // 列头样式
        CellStyle headerStyle = workbook.createCellStyle();
        headerStyle.setBorderBottom(BorderStyle.MEDIUM);
        headerStyle.setBorderTop(BorderStyle.MEDIUM);
        headerStyle.setBorderLeft(BorderStyle.MEDIUM);
        headerStyle.setBorderRight(BorderStyle.MEDIUM);
        Font headerFont = workbook.createFont();
        headerFont.setFontHeightInPoints((short) 12);
        headerStyle.setFont(headerFont);
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 单元格样式
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        Font cellFont = workbook.createFont();
        cellStyle.setFont(cellFont);

        Map<String, String> headMap = new LinkedHashMap<>();

        // 生成一个(带标题)表格
        SXSSFSheet sheet = workbook.createSheet();
        // 设置列宽
        int minBytes = 20 < DEFAULT_COLOUMN_WIDTH ? DEFAULT_COLOUMN_WIDTH : 20;// 至少字节数
        int ii = 0;
        headMap.put("date", "日期");
        for (JSONObject object : headerList) {
            headMap.put("in" + object.getString("title"), "in" + object.getString("title"));
            headMap.put("out" + object.getString("title"), "out" + object.getString("title"));
            headMap.put("difference" + object.getString("title"), "difference" + object.getString("title"));
            JSONArray titleList = object.getJSONArray("titleList");
            for (Object subTitle : titleList) {
                headMap.put(((JSONObject) subTitle).getString("value"), ((JSONObject) subTitle).getString("value"));
            }
        }
        headMap.put("sum", "总供水");
        // 产生表格标题行,以及设置列宽
        int[] arrColWidth = new int[headMap.size()];
        String[] properties = new String[headMap.size()];
        String[] headers = new String[headMap.size()];
        for (Iterator<String> iter = headMap.keySet().iterator(); iter.hasNext(); ) {
            String fieldName = iter.next();

            properties[ii] = fieldName;
            headers[ii] = headMap.get(fieldName);

            int bytes = fieldName.getBytes().length;
            arrColWidth[ii] = bytes < minBytes ? minBytes : bytes;
            sheet.setColumnWidth(ii, arrColWidth[ii] * 256);
            ii++;
        }
        // 遍历集合数据，产生数据行
        int rowIndex = 0;
        if (rowIndex != 0) sheet = workbook.createSheet();// 如果数据超过了，则在第二页显示
        SXSSFRow titleRow = sheet.createRow(0);// 表头 rowIndex=0
        titleRow.createCell(0).setCellValue("水厂供水报表");
        titleRow.getCell(0).setCellStyle(titleStyle);
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, headMap.size() - 1));

        SXSSFRow towRow = sheet.createRow(1);
        towRow.createCell(0).setCellValue("日期");
        towRow.getCell(0).setCellStyle(cellStyle);
        sheet.addMergedRegion(new CellRangeAddress(1, 2, 1, 1));

        towRow = sheet.createRow(2);

        JSONObject oneHeader;
        JSONObject towHeader;
        JSONArray jsonArray;
        int tempJ = 0;
        int lastJ = 1;
        SXSSFRow oneRow = sheet.createRow(1);
        String[] fixedTowHeader = {"水厂总进水", "水厂总出水", "差值"};
        for (int i = 0; i < headerList.size(); i++) {
            oneHeader = headerList.get(i);
            jsonArray = oneHeader.getJSONArray("titleList");
            for (int j = 0; j < jsonArray.size(); j++) {

                towHeader = ((JSONObject) jsonArray.get(j));

                towRow.createCell(j + 1);
                towRow.getCell(j + 1).setCellValue(towHeader.getString("label"));
                towRow.getCell(j + 1).setCellStyle(headerStyle);
                tempJ = tempJ + j + 1;
            }
            for (int j = 0; j < fixedTowHeader.length; j++) {
                towRow.createCell(tempJ + 1);
                towRow.getCell(tempJ + 1).setCellValue(fixedTowHeader[i]);
                towRow.getCell(tempJ + 1).setCellStyle(headerStyle);
            }

            tempJ = tempJ + 1;

            // 一级标题
            for (int j = lastJ; j < tempJ; j++) {
                oneRow.createCell(j);
                oneRow.getCell(j).setCellStyle(cellStyle);
            }
            oneRow.getCell(lastJ).setCellValue(oneHeader.getString("title"));
            sheet.addMergedRegion(new CellRangeAddress(1, 1, lastJ, tempJ));
            lastJ = tempJ;
        }

        SXSSFRow headerRow = sheet.createRow(2); // 列头 rowIndex =1
        for (int i = 0; i < headers.length; i++) {
            headerRow.createCell(i).setCellValue(headers[i]);
            headerRow.getCell(i).setCellStyle(headerStyle);

        }
        sheet.addMergedRegion(new CellRangeAddress(1, 2, 0, 0));
        rowIndex = 3;
        for (Object obj : dataList) {
            if (rowIndex == 65535) {
                for (int i = 0; i < headerList.size(); i++) {
                    oneHeader = headerList.get(i);
                    jsonArray = oneHeader.getJSONArray("titleList");
                    for (int j = 0; j < jsonArray.size(); j++) {

                        towHeader = ((JSONObject) jsonArray.get(j));
                        int bytes = towHeader.getString("label").getBytes().length;
                        arrColWidth[ii] = bytes < minBytes ? minBytes : bytes;
                        sheet.setColumnWidth(ii, arrColWidth[ii] * 256);

                        towRow.createCell(j + 1);
                        towRow.getCell(j + 1).setCellValue(towHeader.getString("label"));
                        towRow.getCell(j + 1).setCellStyle(headerStyle);
                        tempJ = tempJ + j + 1;
                    }
                    for (int j = 0; j < fixedTowHeader.length; j++) {
                        towRow.createCell(tempJ + 1);
                        towRow.getCell(tempJ + 1).setCellValue(fixedTowHeader[i]);
                        towRow.getCell(tempJ + 1).setCellStyle(headerStyle);
                    }

                    tempJ = tempJ + 1;

                    // 一级标题
                    for (int j = lastJ; j < tempJ; j++) {
                        oneRow.createCell(j);
                        oneRow.getCell(j).setCellStyle(cellStyle);
                    }
                    oneRow.getCell(lastJ).setCellValue(oneHeader.getString("title"));
                    sheet.addMergedRegion(new CellRangeAddress(1, 1, lastJ, tempJ));
                    lastJ = tempJ;
                }

                rowIndex = 3;// 数据内容从 rowIndex=2开始
            }
            JSONObject jo = (JSONObject) JSONObject.toJSON(obj);
            SXSSFRow dataRow = sheet.createRow(rowIndex);
            for (int i = 0; i < properties.length; i++) {
                SXSSFCell newCell = dataRow.createCell(i);

                Object o = jo.get(properties[i]);
                String cellValue = "";
                if (o == null) cellValue = "";
                else if (o instanceof Date) cellValue = new SimpleDateFormat(datePattern).format(o);
                else if (o instanceof Float || o instanceof Double)
                    cellValue = new BigDecimal(o.toString()).setScale(2, BigDecimal.ROUND_HALF_UP).toString();
                else cellValue = o.toString();

                newCell.setCellValue(cellValue);
                newCell.setCellStyle(cellStyle);
            }
            rowIndex++;
        }
    }

    // 自动调整宽度
        /*for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
        }*/
    public static void getImportMeterDataTemplate(HttpServletResponse response) {
        XSSFWorkbook workbook = null;
        try {
            workbook = new XSSFWorkbook(new ExcelUtil().getClass().getResourceAsStream("/templates/抄表数据模板.xlsx"));
        } catch (IOException e) {
            e.printStackTrace();
            return;
        }
        try {
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            workbook.write(os);
            workbook.close();
            byte[] content = os.toByteArray();
            InputStream is = new ByteArrayInputStream(content);
            // 设置response参数，可以打开下载页面
            response.reset();

            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("抄表数据模板" + ".xlsx", "utf-8"));
            response.setHeader("Access-Control-Allow-Origin", "*");
            response.setContentLength(content.length);
            ServletOutputStream outputStream = response.getOutputStream();
            BufferedInputStream bis = new BufferedInputStream(is);
            BufferedOutputStream bos = new BufferedOutputStream(outputStream);
            byte[] buff = new byte[8192];
            int bytesRead;
            while (-1 != (bytesRead = bis.read(buff, 0, buff.length))) {
                bos.write(buff, 0, bytesRead);

            }
            bis.close();
            bos.close();
            outputStream.flush();
            outputStream.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void downloadExcelFile(String fileName, XSSFWorkbook workbook, HttpServletResponse response) {

        try {
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            workbook.write(os);
            workbook.close();
            byte[] content = os.toByteArray();
            InputStream is = new ByteArrayInputStream(content);
            // 设置response参数，可以打开下载页面
            response.reset();

            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "utf-8"));
            response.setHeader("Access-Control-Allow-Origin", "*");
            response.setContentLength(content.length);
            ServletOutputStream outputStream = response.getOutputStream();
            BufferedInputStream bis = new BufferedInputStream(is);
            BufferedOutputStream bos = new BufferedOutputStream(outputStream);
            byte[] buff = new byte[8192];
            int bytesRead;
            while (-1 != (bytesRead = bis.read(buff, 0, buff.length))) {
                bos.write(buff, 0, bytesRead);

            }
            bis.close();
            bos.close();
            outputStream.flush();
            outputStream.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static List<PrimeCost> excelToCost(MultipartFile file) throws IOException {
        List<PrimeCost> list = new ArrayList<>();
        InputStream inputStream = file.getInputStream();
        try {
            XSSFWorkbook workbook = new XSSFWorkbook(inputStream);

            for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
                Sheet sheet = workbook.getSheetAt(0);
                for (Row row : sheet) {
                    if (row.getRowNum() < 1) {
                        continue;
                    }
                    System.out.println();
                    if (row.getCell(0) == null) {
                        continue;
                    }
                    SimpleDateFormat format = new SimpleDateFormat("yyyy年MM月dd日");
                    PrimeCost primeCost = new PrimeCost();
                    primeCost.setRecordTime(format.parse(row.getCell(0).getStringCellValue()));
                    primeCost.setFactory(String.valueOf(row.getCell(1)));
                    primeCost.setPowerCost(BigDecimal.valueOf(row.getCell(2).getNumericCellValue()));
                    primeCost.setWaterAmount(BigDecimal.valueOf(row.getCell(3).getNumericCellValue()));
                    primeCost.setProduct(String.valueOf(row.getCell(4)));
                    primeCost.setMedicalAmount(BigDecimal.valueOf(row.getCell(5).getNumericCellValue()));
                    primeCost.setMedicalPrice(BigDecimal.valueOf(row.getCell(6).getNumericCellValue()));
                    list.add(primeCost);
                }
                workbook.close();
            }
        } catch (IOException e) {
            e.printStackTrace();
        } catch (ParseException e) {
            e.printStackTrace();
        } finally {
            inputStream.close();
        }
        return list;
    }

    public static void exportGanRepairList(List<GanRepair> data, HttpServletResponse response, String firstName) throws IOException {
        // 声明一个工作薄
        XSSFWorkbook workbook = new XSSFWorkbook(new ExcelUtil().getClass().getResourceAsStream("/templates/水务公司办件基本情况.xlsx"));// 缓存
        // 表头样式
        CellStyle titleStyle = workbook.createCellStyle();
        titleStyle.setBorderBottom(BorderStyle.MEDIUM);
        titleStyle.setBorderTop(BorderStyle.MEDIUM);
        titleStyle.setBorderLeft(BorderStyle.MEDIUM);
        titleStyle.setBorderRight(BorderStyle.MEDIUM);
        ;
        titleStyle.setAlignment(HorizontalAlignment.CENTER);
        Font titleFont = workbook.createFont();
        titleFont.setFontHeightInPoints((short) 20);
        titleStyle.setFont(titleFont);
        // 列头样式
        CellStyle headerStyle = workbook.createCellStyle();
        headerStyle.setBorderBottom(BorderStyle.MEDIUM);
        headerStyle.setBorderTop(BorderStyle.MEDIUM);
        headerStyle.setBorderLeft(BorderStyle.MEDIUM);
        headerStyle.setBorderRight(BorderStyle.MEDIUM);
        Font headerFont = workbook.createFont();
        headerFont.setFontHeightInPoints((short) 12);
        headerStyle.setFont(headerFont);
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        // 单元格样式
        List<CellStyle> cellStyles = new ArrayList<>();
        for (int i = 0; i < 8; i++) {
            CellStyle cellStyle = workbook.getSheetAt(0).getRow(3).getCell(i).getCellStyle();
            cellStyles.add(cellStyle);
        }

        Calendar instance = Calendar.getInstance();
        instance.setTime(new Date());
        int month = instance.get(Calendar.MONTH) + 1;
        Sheet sheet = workbook.getSheetAt(0);
        String title = "全南县公用水务有限公司" + month + "月办件（报修）基本情况";
        sheet.getRow(0).getCell(0).setCellValue(title);
        String date = new SimpleDateFormat("yyyy年MM月dd日").format(new Date());
        sheet.getRow(1).getCell(0).setCellValue("单位（盖章）：全南县公用水务有限公司     " + "填表人：" + firstName + "     主要负责人签字：          " + date);
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        for (int i = 0; i < data.size(); i++) {
            if (sheet.getRow(i + 3) == null) {
                sheet.createRow(i + 3);
            }
            for (int j = 0; j < 8; j++) {
                if (sheet.getRow(i + 3).getCell(j) == null) {
                    sheet.getRow(i + 3).createCell(j);
                    sheet.getRow(i + 3).getCell(j).setCellStyle(cellStyles.get(j));
                }
            }

            sheet.getRow(i + 3).getCell(0).setCellValue(i + 1);
            sheet.getRow(i + 3).getCell(1).setCellValue(data.get(i).getName());
            String phone = data.get(i).getPhone();
            if (StringUtils.checkNotNull(phone)) {
                phone = AesUtil.decrypt(phone, "912USxw18864X1jB");
            }
            sheet.getRow(i + 3).getCell(2).setCellValue(phone);
            sheet.getRow(i + 3).getCell(3).setCellValue("个人报修");
            sheet.getRow(i + 3).getCell(4).setCellValue(format.format(data.get(i).getCreateTime()));
            sheet.getRow(i + 3).getCell(5).setCellValue(data.get(i).getProcessTime());
            sheet.getRow(i + 3).getCell(6).setCellValue(data.get(i).getProcessName());
            String source = "微信公众号";//1赣服通 2微信公众号 3PC端
            if (data.get(i).getSource() != null) {
                switch (data.get(i).getSource()) {
                    case "2":
                        source = "微信公众号";
                        break;
                    case "3":
                        source = "PC端";
                }
            }

            sheet.getRow(i + 3).getCell(7).setCellValue(source);
        }
        try {
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            workbook.write(out);
            workbook.close();

            exportExcel(title, out, response);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

}