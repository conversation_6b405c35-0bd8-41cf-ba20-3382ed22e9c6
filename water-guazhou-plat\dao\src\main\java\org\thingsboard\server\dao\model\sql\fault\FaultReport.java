package org.thingsboard.server.dao.model.sql.fault;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrder;

import java.util.Date;
import java.util.List;

/**
 * 保养规范
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-09-12
 */
@TableName("tb_device_fault_report")
@Data
public class FaultReport {
    @TableId
    private String id;

    private String title;

    private String faultProject;

    private String faultRemark;

    private String faultAddress;

    private String workOrderId;

    private String creator;

    private transient String creatorName;

    private transient WorkOrder workOrder;

    private transient List<FaultReportC> faultReportCList;

    private Date createTime;

    private Date startTime;

    private String tenantId;

}
