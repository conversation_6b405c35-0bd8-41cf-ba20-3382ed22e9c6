import{d as y,c as s,r as d,a8 as v,o as b,ay as C,g as n,n as k,h as f,i as a,p as u,q as V,j as D,_ as w,C as F}from"./index-r0dFAfgr.js";import{折 as m}from"./charts-CPgX3ymz.js";import{m as B,n as L}from"./ledgerManagement-CkhtRd8m.js";const N={style:{width:"calc(100% - 20px)"}},S={class:"tcharts"},T={class:"charts"},I=y({__name:"patrolinfo",props:{id:{}},setup(_){const r=_,i=s(new Date().toString()),l=s(m([],"巡检次数")),c=d({title:"",labelWidth:"100px",defaultValue:{},group:[{fields:[{type:"divider",text:"巡检信息"},{xl:8,type:"text",label:"巡检次数:",field:"count"},{xl:8,type:"text",label:"最近巡检:",field:"latestMaintainTime"}]}]}),h=d({title:"",labelWidth:"100px",defaultValue:{},group:[{fields:[{type:"divider",text:"巡检计划"},{type:"table",field:"faultReportCList",config:{indexVisible:!0,height:"350px",dataList:v(()=>p.value),columns:[{label:"计划名称",prop:"name"},{label:"限制时间",prop:"limitDays"},{label:"循环周期",prop:"cycleDays"},{label:"下一次巡检时间",prop:"nextTime"},{label:"任务人",prop:"userName"}],pagination:{hide:!0}}}]}]}),p=s([]),g=async()=>{B(r.id).then(o=>{const e=o.data.data||{};for(const t in e)(e[t]===void 0||e[t]===null)&&(e[t]=" ");c.defaultValue={...e},l.value=m(e.nowYearCircuit||[],"巡检次数"),setTimeout(()=>{i.value=new Date().toString()},1e3)}),L(r.id).then(o=>{p.value=o.data.data.data||[]})};return b(()=>{g()}),(o,e)=>{const t=w,x=C("VChart");return n(),k("div",N,[(n(),f(t,{key:a(i),ref:"refForm",config:a(c)},null,8,["config"])),u("div",S,[u("div",T,[V(x,{ref:"refChart2",autoresize:"",theme:a(D)().isDark?"dark":"light",option:a(l)},null,8,["theme","option"])])]),(n(),f(t,{key:a(i),ref:"refForm",config:a(h)},null,8,["config"]))])}}}),q=F(I,[["__scopeId","data-v-43876caa"]]);export{q as default};
