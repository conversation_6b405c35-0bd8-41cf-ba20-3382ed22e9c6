// 智慧生产=水源管理-水源监测 api
import request from '@/plugins/axios';

/**
 * 查询水源地列表以及各个水源地的今日、昨日、本月供水量
 * @param params
 * @returns
 */
export function getWaterSupplyInfo(params?: {
  /** 不传则查全部 */
  projectId?: string;
  /** 站点名称 模糊 */
  name?: string;
}) {
  return request({
    url: '/istar/api/water/source/getWaterSupplyInfo',
    method: 'get',
    params
  });
}

// 查询单个水源地的详细供水数据（今日、昨日、本月）、出水压力、瞬时流量曲线数据
export function getWaterSupplyDetail(stationId: string) {
  return request({
    url: '/istar/api/water/source/getWaterSupplyDetail',
    method: 'get',
    params: {
      stationId: stationId || ''
    }
  });
}

// 查询水源地供水量总览数据。包含：今日、昨日、本月、上月、总供水、报警数
export function getWaterSupplyInfoView(params: { projectId: string }) {
  return request({
    url: '/istar/api/water/source/getWaterSupplyInfoView',
    method: 'get',
    params
  });
}

// 查询水源地的实时数据列表
export function getWaterSupplyInfoDetail(params: { projectId: string }) {
  return request({
    url: '/istar/api/water/source/getWaterSupplyInfoDetail',
    method: 'get',
    params
  });
}

// 查询指定属性的近三天数据曲线
export function getThreeDaysData(params: { deviceId: string; attr: string }) {
  return request({
    url: '/istar/api/station/data/getThreeDaysData',
    method: 'get',
    params
  });
}

// 查询指定站点的指定类型的历史数据
export function stationDayDataQuery(params: {
  stationId: string;
  start: any;
  end: any;
  filterStart: string;
  filterEnd: string;
  queryType: string;
  group?: string;
  attributeId?: string;
}) {
  return request({
    url: '/istar/api/station/data/stationDayDataQuery',
    method: 'get',
    params
  });
}

/* 梓莲达水务驾驶舱 */
export function getWaterSupplyTotal(params?: { name?: string }) {
  return request({
    url: '/istar/api/water/source/getWaterSupplyTotal',
    method: 'get',
    params
  });
}

export function getWaterSupplyInfoTotal() {
  return request({
    url: '/istar/api/boosterPumpStation/getWaterSupplyInfoTotal',
    method: 'get'
  });
}

/**
 *
 * @param params
 */
export function getWaterSupplyAllTotal(params: { name: string }) {
  return request({
    url: '/istar/api/water/source/getWaterSupplyAllTotal',
    method: 'get',
    params
  });
}

export function getWaterSupplyDetailTotal(params: { name?: string }) {
  return request({
    url: '/istar/api/boosterPumpStation/getWaterSupplyDetailTotal',
    method: 'get',
    params
  });
}
