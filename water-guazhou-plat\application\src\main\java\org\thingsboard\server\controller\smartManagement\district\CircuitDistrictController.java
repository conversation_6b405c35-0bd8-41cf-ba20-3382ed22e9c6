package org.thingsboard.server.controller.smartManagement.district;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;;
import org.thingsboard.server.dao.model.sql.smartManagement.district.SMCircuitDistrict;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.district.CircuitDistrictSaveRequest;
import org.thingsboard.server.dao.district.CircuitDistrictService;
import org.thingsboard.server.utils.imodel.annotations.IStarController;

import java.util.List;

@IStarController
@RequestMapping("/api/sm/circuitDistrict")
public class CircuitDistrictController extends BaseController {
    @Autowired
    private CircuitDistrictService service;

    @GetMapping("/tree")
    public SMCircuitDistrict findTree() throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return service.findGlobalTree(tenantId);
    }

    @GetMapping("/roots")
    public List<SMCircuitDistrict> findRoots() throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return service.findRoots(tenantId);
    }

    @GetMapping("/{id}/tree")
    public SMCircuitDistrict findTreeByParentId(@PathVariable String id) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return service.findTreeByParentId(id, tenantId);
    }

    @GetMapping("/{id}/tree/children")
    public List<SMCircuitDistrict> findChildrenTreeByParentId(@PathVariable String id) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return service.findChildrenTreeByParentId(id, tenantId);
    }

    @PostMapping
    public SMCircuitDistrict save(@RequestBody CircuitDistrictSaveRequest req) {
        return service.save(req);
    }

    @PatchMapping("/{id}")
    public boolean edit(@RequestBody CircuitDistrictSaveRequest req, @PathVariable String id) {
        return service.update(req.unwrap(id));
    }

    @DeleteMapping("/{id}")
    public boolean delete(@PathVariable String id) {
        return service.delete(id);
    }
}