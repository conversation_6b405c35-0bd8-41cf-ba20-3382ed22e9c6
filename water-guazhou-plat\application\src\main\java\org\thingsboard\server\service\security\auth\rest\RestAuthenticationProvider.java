/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.service.security.auth.rest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.*;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.thingsboard.server.common.data.*;
import org.thingsboard.server.common.data.id.CustomerId;
import org.thingsboard.server.common.data.id.EntityId;
import org.thingsboard.server.common.data.id.UserId;
import org.thingsboard.server.common.data.security.Authority;
import org.thingsboard.server.common.data.security.UserCredentials;
import org.thingsboard.server.dao.customer.CustomerService;
import org.thingsboard.server.dao.user.UserService;
import org.thingsboard.server.service.security.model.SecurityUser;
import org.thingsboard.server.service.security.model.UserPrincipal;
import org.thingsboard.server.service.utils.IPUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.ExecutionException;

@Component
public class RestAuthenticationProvider implements AuthenticationProvider {

    private final BCryptPasswordEncoder encoder;
    private final UserService userService;
    private final CustomerService customerService;

    @Autowired
    public RestAuthenticationProvider(final UserService userService, final CustomerService customerService, final BCryptPasswordEncoder encoder) {
        this.userService = userService;
        this.customerService = customerService;
        this.encoder = encoder;
    }

    @Value("${istar-ipc.ip}")
    private String ip;

    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        Assert.notNull(authentication, "No authentication data provided");

        Object principal = authentication.getPrincipal();
        if (!(principal instanceof UserPrincipal)) {
            throw new BadCredentialsException("Authentication Failed. Bad user principal.");
        }

        UserPrincipal userPrincipal =  (UserPrincipal) principal;
        if (userPrincipal.getType() == UserPrincipal.Type.USER_NAME) {
            String username = userPrincipal.getValue();
            String password = (String) authentication.getCredentials();
            return authenticateByUsernameAndPassword(userPrincipal, username, password);
        } else if(userPrincipal.getType() == UserPrincipal.Type.SERIAL_NO) {
            String serialNo = userPrincipal.getValue();
            return authenticateBySerialNo(userPrincipal, serialNo);
        } else {
            String publicId = userPrincipal.getValue();
            return authenticateByPublicId(userPrincipal, publicId);
        }
    }

    /**
     * 账号密码登录
     *
     * @param userPrincipal
     * @param username
     * @param password
     * @return
     */
    private Authentication authenticateByUsernameAndPassword(UserPrincipal userPrincipal, String username, String password) {
        User user = userService.findByAccountKey(username);
//        User user = userService.findUserByEmail(username);
        if (user == null) {
            throw new BadCredentialsException("用户名或密码错误");
        }

        /*if (user.getEmail().equals("<EMAIL>")) {
            //获取request
            HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
            //IP地址
            String ipAddr = IPUtils.getIpAddr(request);
            String[] ips = ip.split(",");
            boolean flag = true;
            for (String s : ips) {
                if (s.equals(ipAddr)) {
                    flag = false;
                    break;
                }
            }

            if (!ip.contains("*") && flag) {
                throw new BadCredentialsException("非法IP登录!");
            }

        }*/

        UserCredentials userCredentials = userService.findUserCredentialsByUserId(user.getId());
        if (userCredentials == null) {
            throw new BadCredentialsException("用户名或密码错误");
        }

        if (!userCredentials.isEnabled()) {
            changeLoginTime(userCredentials);
            throw new BadCredentialsException("账号已被冻结, 请联系管理员解冻!");
        }

        if (!encoder.matches(password, userCredentials.getPassword())) {
            changeLoginTime(userCredentials);
            throw new BadCredentialsException("用户名或密码错误");
        }

        if (user.getAuthority() == null) throw new InsufficientAuthenticationException("User has no authority assigned");

        // 查询用户的企业权限
        if (user.getAuthority().equals(Authority.TENANT_PROMOTE) || user.getAuthority().equals(Authority.TENANT_SUPPORT)) {
            // 查询tenantId列表
            List<Tenant> tenantList = userService.findUserTenantByUserId(UUIDConverter.fromTimeUUID(user.getUuidId()));
            user.setTenantList(tenantList);
        }

        SecurityUser securityUser = new SecurityUser(user, userCredentials.isEnabled(), userPrincipal);

        DataConstants.LOGIN_TIME_CHECK.remove(userCredentials.getUserId().getId().toString());
        return new UsernamePasswordAuthenticationToken(securityUser, null, securityUser.getAuthorities());
    }

    /**
     * serial no登录
     *
     * @param userPrincipal
     * @param serialNo
     * @return
     */
    private Authentication authenticateBySerialNo(UserPrincipal userPrincipal, String serialNo) {
        User user = userService.findUserBySerialNo(serialNo);
        if (user == null) {
            throw new BadCredentialsException("该卡未绑定用户");
        }

        UserCredentials userCredentials = userService.findUserCredentialsByUserId(user.getId());
        if (userCredentials == null) {
            throw new BadCredentialsException("该卡未绑定用户");
        }

        if (!userCredentials.isEnabled()) {
            changeLoginTime(userCredentials);
            throw new BadCredentialsException("账号已被冻结, 请联系管理员解冻!");
        }

        SecurityUser securityUser = new SecurityUser(user, userCredentials.isEnabled(), userPrincipal);

        DataConstants.LOGIN_TIME_CHECK.remove(userCredentials.getUserId().getId().toString());
        return new UsernamePasswordAuthenticationToken(securityUser, null, securityUser.getAuthorities());
    }

    private Authentication authenticateByPublicId(UserPrincipal userPrincipal, String publicId) {
        CustomerId customerId;
        try {
            customerId = new CustomerId(UUID.fromString(publicId));
        } catch (Exception e) {
            throw new BadCredentialsException("Authentication Failed. Public Id is not valid.");
        }
        Customer publicCustomer = null;
        try {
            publicCustomer = customerService.findCustomerByIdAsync(customerId).get();
        } catch (InterruptedException e) {
            e.printStackTrace();
        } catch (ExecutionException e) {
            e.printStackTrace();
        }
        if (publicCustomer == null) {
            throw new UsernameNotFoundException("Public entity not found: " + publicId);
        }
        if (!publicCustomer.isPublic()) {
            throw new BadCredentialsException("Authentication Failed. Public Id is not valid.");
        }
        User user = new User(new UserId(EntityId.NULL_UUID));
        user.setTenantId(publicCustomer.getTenantId());
        user.setCustomerId(publicCustomer.getId());
        user.setEmail(publicId);
        user.setAuthority(Authority.CUSTOMER_USER);
        user.setFirstName("Public");
        user.setLastName("Public");

        SecurityUser securityUser = new SecurityUser(user, true, userPrincipal);

        return new UsernamePasswordAuthenticationToken(securityUser, null, securityUser.getAuthorities());
    }

    private void changeLoginTime(UserCredentials userCredentials) {
        if (userCredentials == null) {
            return;
        }

        if (DataConstants.LOGIN_TIME_CHECK.containsKey(userCredentials.getUserId().getId().toString())) {
            Integer integer = DataConstants.LOGIN_TIME_CHECK.get(userCredentials.getUserId().getId().toString());
            if (integer < 10) {
                integer ++;
                DataConstants.LOGIN_TIME_CHECK.put(userCredentials.getUserId().getId().toString(), integer);
            } else {
                userCredentials.setEnabled(false);
                DataConstants.LOGIN_TIME_CHECK.remove(userCredentials.getUserId().getId().toString());
            }
        }
    }

    @Override
    public boolean supports(Class<?> authentication) {
        return (UsernamePasswordAuthenticationToken.class.isAssignableFrom(authentication));
    }
}
