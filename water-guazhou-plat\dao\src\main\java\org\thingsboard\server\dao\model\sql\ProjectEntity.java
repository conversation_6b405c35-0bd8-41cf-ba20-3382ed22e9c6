package org.thingsboard.server.dao.model.sql;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;

@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.PROJECT_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class ProjectEntity {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.PROJECT_PARENT_ID)
    private String parentId;

    @Column(name = ModelConstants.PROJECT_CREATE_TIME)
    private Long createTime;

    @Column(name = ModelConstants.PROJECT_TENANT_ID)
    private String tenantId;

    @Column(name = ModelConstants.PROJECT_NAME)
    private String name;

    @Column(name = ModelConstants.PROJECT_ADDITIONAL_INFO)
    private String additionalInfo;

    @Column(name = ModelConstants.TYPE)
    private String type;

    public ProjectEntity(String id, String name, Long createTime, String tenantId, String additionalInfo) {
        this.id = id;
        this.createTime = createTime;
        this.tenantId = tenantId;
        this.name = name;
        this.additionalInfo = additionalInfo;
    }

    public ProjectEntity(String id, String parentId, String name, Long createTime, String tenantId, String additionalInfo) {
        this.id = id;
        this.createTime = createTime;
        this.tenantId = tenantId;
        this.name = name;
        this.additionalInfo = additionalInfo;
        this.parentId = parentId;
    }

    public ProjectEntity(String id, String parentId, String name, Long createTime, String tenantId, String additionalInfo, String type) {
        this.id = id;
        this.createTime = createTime;
        this.tenantId = tenantId;
        this.name = name;
        this.additionalInfo = additionalInfo;
        this.parentId = parentId;
        this.type = type;
    }
}
