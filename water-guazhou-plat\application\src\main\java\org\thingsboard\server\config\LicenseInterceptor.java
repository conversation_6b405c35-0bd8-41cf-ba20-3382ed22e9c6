package org.thingsboard.server.config;


import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;
import org.thingsboard.server.utils.LicenseAuth;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.io.PrintWriter;

public class LicenseInterceptor implements HandlerInterceptor {

    private String LICENSE_PATH;

    public LicenseInterceptor() {
    }

    public LicenseInterceptor(String license_path) {
        this.LICENSE_PATH = license_path;
    }

    /**
     * 在请求处理之前进行调用（Controller方法调用之前）
     */
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        ClassPathResource publicKeyResource = new ClassPathResource("keys/istarcloud.crt");
        File publicKey = File.createTempFile(System.currentTimeMillis() + "", "_istarcloud.crt");
        IOUtils.copy(publicKeyResource.getInputStream(), new FileOutputStream(publicKey));
        boolean result = false;
        int i = 5;
        while (i > 0) {
            try {
                result = LicenseAuth.authLicense(LICENSE_PATH, publicKey.getAbsolutePath());
                break;
            } catch (Exception e) {
                i--;
            }
        }


        if (!result) {
            response.setStatus(401);
            response.setContentType("text/html; charset=utf-8");
            PrintWriter writer = response.getWriter();
            writer.print("License文件验证失败!");
            writer.close();
            response.flushBuffer();
        }

        publicKey.delete();

        return result;
    }
 
    /**
     * 请求处理之后进行调用，但是在视图被渲染之前（Controller方法调用之后）
     */
    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) {
    }
 
    /**
     * 在整个请求结束之后被调用，也就是在DispatcherServlet 渲染了对应的视图之后执行（主要是用于进行资源清理工作）
     */
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
    }
    
}