package org.thingsboard.server.controller.base;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.RequestEntity;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.exception.ThingsboardErrorCode;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.dao.model.sql.ProjectEntity;
import org.thingsboard.server.dao.model.sql.ProjectRelationEntity;
import org.thingsboard.server.dao.project.ProjectRelationService;
import org.thingsboard.server.service.aspect.annotation.SysLog;

import java.util.List;

@RestController
@RequestMapping("api/project/relation")
public class ProjectRelationController {

    @Autowired
    private ProjectRelationService projectRelationService;

    /**
     * 查询项目指定类型的资源详情
     *
     * @param entityType
     * @param projectId
     * @return
     * @throws ThingsboardException
     */
    @GetMapping("{entityType}/{projectId}")
    @SysLog(detail = DataConstants.OPERATING_TYPE_LOG_INFO)
    public List<ProjectRelationEntity> findProjectRelationByEntityTypeAndProjectId(@PathVariable String entityType, @PathVariable String projectId) throws ThingsboardException {
        // 检查类型是否合法
        try {
            DataConstants.ProjectRelationEntityType.valueOf(entityType);
        } catch (IllegalArgumentException e) {
            throw new ThingsboardException("entity_type 不合法: [" + entityType + "]", ThingsboardErrorCode.INVALID_ARGUMENTS);
        }

        return projectRelationService.findProjectRelationByEntityTypeAndProjectId(entityType, projectId);
    }

    /**
     * 查询指定资源类型以及资源ID的项目
     *
     * @param entityType
     * @param entityId
     * @return
     * @throws ThingsboardException
     */
    @GetMapping("project/{entityType}/{entityId}")
    @SysLog(detail = DataConstants.OPERATING_TYPE_LOG_INFO)
    public List<ProjectEntity> findProjectRelationByEntityTypeAndEntityId(@PathVariable String entityType, @PathVariable String entityId) throws ThingsboardException {
        // 检查类型是否合法
        try {
            DataConstants.ProjectRelationEntityType.valueOf(entityType);
        } catch (IllegalArgumentException e) {
            throw new ThingsboardException("entity_type 不合法: [" + entityType + "]", ThingsboardErrorCode.INVALID_ARGUMENTS);
        }

        return projectRelationService.findProjectRelationByEntityTypeAndEntityId(entityType, entityId);
    }

    /**
     * 挂载资源到指定项目
     *
     * @param entityType
     * @param projectId
     * @param entityIdStringList
     * @return
     */
    @PostMapping("mount/{entityType}/{projectId}")
    @SysLog(options = DataConstants.OPERATING_TYPE.OPERATING_TYPE_OPTIONS_PROJECT_MOUNT)
    public Boolean mountEntityToProject(@PathVariable String entityType,
                                        @PathVariable String projectId,
                                        @RequestBody List<String> entityIdStringList) {
        try {
            return projectRelationService.mountEntityToProject(entityType, projectId, entityIdStringList);
        } catch (Exception e) {
            return false;
        }
    }



}
