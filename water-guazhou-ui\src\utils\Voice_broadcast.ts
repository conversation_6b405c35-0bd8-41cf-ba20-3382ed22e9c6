/**
 * 谷歌浏览器实现网页页面语音播报功能
 * 谷歌89版本后使用线上服务实现语音播报功能：window.speechSynthesis方法
 * 引入：import _SpeechSynthesisUtterance from '@/utils/speechSynthesisUtterance'
 * 开启声音：_SpeechSynthesisUtterance.handleSpeak('播报信息')
 * 关闭声音：_SpeechSynthesisUtterance.handleStop()
 *------------------------------------------------------------------------------------
 * text – 要合成的文字内容，字符串。
 * lang – 使用的语言，字符串， 例如："zh-cn"
 * voiceURI – 指定希望使用的声音和服务，字符串。
 * volume – 声音的音量，区间范围是0到1，默认是1。
 * rate – 语速，数值，默认值是1，范围是0.1到10，表示语速的倍数，例如2表示正常语速的两倍。
 * pitch – 表示说话的音高，数值，范围从0（最小）到2（最大）。默认值为1。
 */

class _SpeechSynthesisUtterance {
  msg: any;

  constructor() {
    this.msg = new SpeechSynthesisUtterance();
  }

  handleSpeak = (text) => {
    this.msg.text = text; // 语音播报文字内容
    this.msg.lang = 'zh-CN'; // 使用的语言:中文
    this.msg.volume = 1; // 声音音量：1
    this.msg.rate = 1; // 语速：1
    this.msg.pitch = 1; // 音高：1
    window.speechSynthesis.speak(this.msg); // 播放
  };

  handleStop = (e) => {
    this.msg.text = e;
    this.msg.lang = 'zh-CN';
    window.speechSynthesis.cancel(); // 停止
  };
}
export default new _SpeechSynthesisUtterance();
