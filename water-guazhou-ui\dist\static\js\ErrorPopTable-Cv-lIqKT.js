import{d as z,c as B,a8 as A,am as G,g as l,n as p,p as t,q as m,i as a,F as u,G as D,bh as P,an as U,h as f,t as N,j as H,aw as F,aq as J,cY as M,cU as R,e_ as X,H as K,J as Q,C as W}from"./index-r0dFAfgr.js";const Z={0:"#e6a23c",1:"#67c23a",2:"#f56c6c"},ee={1:"同意",2:"驳回",0:"待处理"},pe=["START_SID","END_SID","SID","ID","OBJECTID","PIPELENGTH","X","Y","SUBTYPE"],oe={class:"table-box"},te={class:"extrainfo"},ae={key:0,class:"extrainfo-item"},ne={class:"extrainfo-item"},le={class:"extrainfo-item remark"},se={key:0,id:"remark"},re={key:0,class:"table-footer"},ie=z({__name:"ErrorPopTable",props:{config:{}},emits:["report"],setup(L,{emit:j}){const O=j,o=L,i=B(""),c=B(""),_=A(()=>{var s;return{pagination:{hide:!0},dataList:((s=o.config)==null?void 0:s.dataList.filter(e=>e.name!=="img"))||[],spanMethod:e=>{let d=1;return e.row.alias==="上报备注"&&(d=e.columnIndex===1?2:e.columnIndex===2?0:1),{rowspan:1,colspan:d}},columns:[{label:"属性",prop:"alias"},{label:"原始值",prop:"oldvalue"},{label:"上报值",prop:"newvalue",formItemConfig:{type:"input",placeholder:(e,r)=>r.editable?"请输入上报值":" ",disabled:(e,r)=>!r.editable}}]}});return G(()=>o.config,()=>{i.value="",c.value=""}),(s,e)=>{var g,b,v,k,w,T,y,E,x,I,C,S;const r=J,d=M,Y=R,$=X,h=K,q=Q;return l(),p("div",{class:F(["error-pop-table",[(g=o.config)!=null&&g.row?"editable":""]])},[t("div",{class:F(["content-box",[(b=s.config)!=null&&b.hideFooter?"table-full":""]])},[t("div",oe,[m(r,{config:a(_)},null,8,["config"])]),t("div",te,[(v=o.config)!=null&&v.row?(l(),p("div",ae,[e[3]||(e[3]=t("label",{for:"status"}," 当前状态： ",-1)),m(d,{color:a(Z)[(w=(k=o.config)==null?void 0:k.row)==null?void 0:w.status]},{default:u(()=>{var n,V;return[D(P(a(ee)[(V=(n=o.config)==null?void 0:n.row)==null?void 0:V.status]||"待处理"),1)]}),_:1},8,["color"])])):U("",!0),t("div",ne,[e[5]||(e[5]=t("label",{for:"remark"}," 现场图片： ",-1)),(T=o.config)!=null&&T.row?(l(),f(Y,{key:0,class:"img-box",src:(y=o.config)==null?void 0:y.img,"preview-src-list":[(E=o.config)==null?void 0:E.img],"initial-index":0,"preview-teleported":!0,fit:"contain","hide-on-click-modal":!0},{error:u(()=>e[4]||(e[4]=[t("div",{class:"image-slot"},[t("span",null,"暂无图片")],-1)])),_:1},8,["src","preview-src-list"])):(l(),f($,{key:1,modelValue:a(i),"onUpdate:modelValue":e[0]||(e[0]=n=>N(i)?i.value=n:null),limit:1,url:a(H)().actionUrl+"file/api/upload/file"},null,8,["modelValue","url"]))]),t("div",le,[e[6]||(e[6]=t("label",{for:"remark"}," 备注信息： ",-1)),(x=o.config)!=null&&x.row?(l(),p("span",se,P((C=(I=o.config)==null?void 0:I.row)==null?void 0:C.remark),1)):(l(),f(h,{key:1,id:"remark",modelValue:a(c),"onUpdate:modelValue":e[1]||(e[1]=n=>N(c)?c.value=n:null)},null,8,["modelValue"]))])])],2),(S=s.config)!=null&&S.hideFooter?U("",!0):(l(),p("div",re,[m(q,{type:"primary",onClick:e[2]||(e[2]=n=>O("report",a(_).dataList,s.config,{img:a(i),remark:a(c)}))},{default:u(()=>e[7]||(e[7]=[D(" 上报 ")])),_:1})]))],2)}}}),ce=W(ie,[["__scopeId","data-v-d1b6f279"]]),me=Object.freeze(Object.defineProperty({__proto__:null,default:ce},Symbol.toStringTag,{value:"Module"}));export{ce as E,me as a,pe as d,ee as r,Z as t};
