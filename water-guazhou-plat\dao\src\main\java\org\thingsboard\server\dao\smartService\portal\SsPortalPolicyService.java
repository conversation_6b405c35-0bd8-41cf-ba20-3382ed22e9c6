package org.thingsboard.server.dao.smartService.portal;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.smartService.portal.SsPortalPolicy;
import org.thingsboard.server.dao.util.imodel.query.smartService.portal.SsPortalPolicyPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartService.portal.SsPortalPolicySaveRequest;

public interface SsPortalPolicyService {
    SsPortalPolicy findById(String id);

    IPage<SsPortalPolicy> findAllConditional(SsPortalPolicyPageRequest request);

    SsPortalPolicy save(SsPortalPolicySaveRequest entity);

    boolean update(SsPortalPolicy entity);

    boolean delete(String id);

    boolean canBeDelete(String id);

}
