import{e as s,y as r,a as l}from"./Point-WxyopZva.js";import{c_ as a}from"./MapView-DaoQedLH.js";import"./index-r0dFAfgr.js";let t=class extends a{constructor(e){super(e),this.tiles=new Map}destroy(){this.tiles.clear(),this.layer=this.layerView=this.tileInfoView=this.tiles=null}get updating(){return this.isUpdating()}acquireTile(e){const i=this.createTile(e);return i.once("isReady",()=>this.notifyChange("updating")),this.tiles.set(e.id,i),i}forceAttributeTextureUpload(){}forEachTile(e){this.tiles.forEach(e)}releaseTile(e){this.tiles.delete(e.key.id),this.disposeTile(e)}isUpdating(){let e=!0;return this.tiles.forEach(i=>{e=e&&i.isReady}),!e}setHighlight(){}invalidateLabels(){}requestUpdate(){this.layerView.requestUpdate()}};s([r()],t.prototype,"layer",void 0),s([r()],t.prototype,"layerView",void 0),s([r()],t.prototype,"tileInfoView",void 0),s([r()],t.prototype,"updating",null),t=s([l("esri.views.2d.layers.features.tileRenderers.BaseTileRenderer")],t);const p=t;export{p as o};
