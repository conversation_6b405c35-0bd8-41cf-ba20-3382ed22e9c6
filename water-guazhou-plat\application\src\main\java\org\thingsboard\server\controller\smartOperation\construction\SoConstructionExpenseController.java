package org.thingsboard.server.controller.smartOperation.construction;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionExpense;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionExpenseContainer;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionExpensePageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionExpenseSaveRequest;
import org.thingsboard.server.dao.util.imodel.response.ExcelFileInfo;
import org.thingsboard.server.dao.util.reflection.ExceptionUtils;
import org.thingsboard.server.dao.construction.SoConstructionExpenseService;
import org.thingsboard.server.utils.imodel.annotations.IStarController2;

import java.util.List;

@IStarController2
@RequestMapping("/api/so/constructionExpense")
public class SoConstructionExpenseController extends BaseController {
    @Autowired
    private SoConstructionExpenseService service;


    @GetMapping
    public IPage<SoConstructionExpenseContainer> findAllConditional(SoConstructionExpensePageRequest request) {
        return service.findAllConditional(request);
    }

    @GetMapping("/export/excel")
    public ExcelFileInfo exportExcel(SoConstructionExpensePageRequest request) {
        List<SoConstructionExpenseContainer> records = findAllConditional(request).getRecords();
        if (request.getConstructionCode() == null) {
            ExceptionUtils.silentThrow("所属工程编号未传入");
        }
        List<SoConstructionExpense> items = null;
        if (records.size() > 0) {
            items = records.get(0).getItems();
        }
        return ExcelFileInfo.of("工程" + request.getConstructionCode() + "-支付信息", items)
                .nextTitle("code", "资金编号")
                .nextTitle("contractName", "所属合同")
                .nextTitle("contractType", "合同类别")
                .nextTitle("payeeOrganization", "收款单位")
                .nextTitle("contractCost", "合同金额(万元)")
                .nextTitle("cost", "支付金额(万元)")
                .nextTitle("type", "费用类型")
                .nextTitle("remark", "说明")
                .nextTitle("creatorName", "创建人")
                .nextTitle("createTimeName", "创建时间");
    }

    @GetMapping("/export/global/excel")
    public ExcelFileInfo exportGlobalExcel(SoConstructionExpensePageRequest request) {
        return ExcelFileInfo.of("工程费用管理列表", findAllConditional(request).getRecords())
                .nextTitle("constructionCode", "工程编号")
                .nextTitle("constructionName", "工程名称")
                .nextTitle("firstpartOrganization", "业主单位")
                // .nextTitle("estimate", "施工单位")
                .nextTitle("secondpartOrganization", "设计单位")
                // .nextTitle("expectEndTimeStr", "监理单位")
                .nextTitle("contractTotalCost", "合同总金额(万元)");
    }

    @PostMapping
    public SoConstructionExpense save(@RequestBody SoConstructionExpenseSaveRequest req) {
        req.preCheckUpdate(() -> !service.isComplete(req.getConstructionCode(), req.tenantId()), "已完成不可更改");
        return service.save(req);
    }

    @PostMapping("/{constructionCode}/complete")
    public boolean complete(@PathVariable String constructionCode) throws ThingsboardException {
        String userId = UUIDConverter.fromTimeUUID(getCurrentUser().getUuidId());
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return service.complete(constructionCode,userId, tenantId);
    }

    // @PatchMapping("/{id}")
    public boolean edit(@RequestBody SoConstructionExpenseSaveRequest req, @PathVariable String id) {
        if (StringUtils.isNotEmpty(req.getCode()) && service.isCodeExists(req.getCode(), req.tenantId(), req.getId())) {
            ExceptionUtils.silentThrow("编码重复");
        }
        return service.update(req.unwrap(id));
    }

    @DeleteMapping("/{id}")
    public boolean delete(@PathVariable String id) {
        if (service.isComplete(id)) {
            ExceptionUtils.silentThrow("已完成，不可删除");
        }
        return service.delete(id);
    }
}