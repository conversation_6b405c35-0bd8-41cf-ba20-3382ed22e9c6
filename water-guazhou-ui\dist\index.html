<!doctype html><html lang="en"><head><meta charset="utf-8"><meta http-equiv="Expires" content="0"><meta http-equiv="Pragma" content="no-cache"><meta http-equiv="Cache-control" content="no-cache"><meta http-equiv="Cache" content="no-cache"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1"><link rel="icon" href="/favicon.ico"><title>智慧水务建设平台</title><link rel="stylesheet" href="/Arcgis/assets/esri/themes/dark/main.css"><script src="/LodopFuncs/LodopFuncs.js"></script><script src="/video/jsencrypt.min.js"></script><script src="/video/jsWebControl-1.0.0.min.js"></script><script>window.SITE_CONFIG = {};
      window._AMapSecurityConfig = {
        securityJsCode: '867bc2b0e2a2d117b1251f4aac7d74fb'
      };</script><script src="/config.js"></script><script src="/config.prod.yanshi.js"></script><script src="/config.prod.js"></script><script type="module" crossorigin src="/static/js/index-r0dFAfgr.js"></script><link rel="stylesheet" crossorigin href="/static/css/index-ByiGXvnH.css"></head><body><noscript><strong>We're sorry but 智慧水务建设平台 doesn't work properly without JavaScript enabled. Please enable it to continue.</strong></noscript><svg id="svgfilters" aria-hidden="true" style="position:absolute;width:0;height:0;overflow:hidden" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><defs><filter id="teal-white" x="-10%" y="-10%" width="120%" height="120%" filterUnits="objectBoundingBox" primitiveUnits="userSpaceOnUse" color-interpolation-filters="sRGB"><feColorMatrix type="matrix" values=".33 .33 .33 0 0
          .33 .33 .33 0 0
          .33 .33 .33 0 0
          0 0 0 1 0" in="SourceGraphic" result="colormatrix"></feColorMatrix><feComponentTransfer in="colormatrix" result="componentTransfer"><feFuncR type="table" tableValues="0.03 1"></feFuncR><feFuncG type="table" tableValues="0.57 1"></feFuncG><feFuncB type="table" tableValues="0.49 1"></feFuncB><feFuncA type="table" tableValues="0 1"></feFuncA></feComponentTransfer><feBlend mode="normal" in="componentTransfer" in2="SourceGraphic" result="blend"></feBlend></filter><filter id="teal-lightgreen" x="-10%" y="-10%" width="120%" height="120%" filterUnits="objectBoundingBox" primitiveUnits="userSpaceOnUse" color-interpolation-filters="sRGB"><feColorMatrix type="matrix" values=".33 .33 .33 0 0
          .33 .33 .33 0 0
          .33 .33 .33 0 0
          0 0 0 1 0" in="SourceGraphic" result="colormatrix"></feColorMatrix><feComponentTransfer in="colormatrix" result="componentTransfer"><feFuncR type="table" tableValues="0.03 0.8"></feFuncR><feFuncG type="table" tableValues="0.57 1"></feFuncG><feFuncB type="table" tableValues="0.49 0.53"></feFuncB><feFuncA type="table" tableValues="0 1"></feFuncA></feComponentTransfer><feBlend mode="normal" in="componentTransfer" in2="SourceGraphic" result="blend"></feBlend></filter><filter id="sepia" x="-10%" y="-10%" width="120%" height="120%" filterUnits="objectBoundingBox" primitiveUnits="userSpaceOnUse" color-interpolation-filters="sRGB"><feColorMatrix type="matrix" values=".33 .33 .33 0 0
          .33 .33 .33 0 0
          .33 .33 .33 0 0
          0 0 0 1 0" in="SourceGraphic" result="colormatrix"></feColorMatrix><feComponentTransfer in="colormatrix" result="componentTransfer"><feFuncR type="table" tableValues="0.26 0.95"></feFuncR><feFuncG type="table" tableValues="0.19 0.78"></feFuncG><feFuncB type="table" tableValues="0.11 0.59"></feFuncB><feFuncA type="table" tableValues="0 1"></feFuncA></feComponentTransfer><feBlend mode="normal" in="componentTransfer" in2="SourceGraphic" result="blend"></feBlend></filter><filter id="purple-sepia" x="-10%" y="-10%" width="120%" height="120%" filterUnits="objectBoundingBox" primitiveUnits="userSpaceOnUse" color-interpolation-filters="sRGB"><feColorMatrix type="matrix" values=".33 .33 .33 0 0
          .33 .33 .33 0 0
          .33 .33 .33 0 0
          0 0 0 1 0" in="SourceGraphic" result="colormatrix"></feColorMatrix><feComponentTransfer in="colormatrix" result="componentTransfer"><feFuncR type="table" tableValues="0.43 0.97"></feFuncR><feFuncG type="table" tableValues="0.06 0.88"></feFuncG><feFuncB type="table" tableValues="0.37 0.79"></feFuncB><feFuncA type="table" tableValues="0 1"></feFuncA></feComponentTransfer><feBlend mode="normal" in="componentTransfer" in2="SourceGraphic" result="blend"></feBlend></filter><filter id="cherry-icecream" x="-10%" y="-10%" width="120%" height="120%" filterUnits="objectBoundingBox" primitiveUnits="userSpaceOnUse" color-interpolation-filters="sRGB"><feColorMatrix type="matrix" values=".33 .33 .33 0 0
          .33 .33 .33 0 0
          .33 .33 .33 0 0
          0 0 0 1 0" in="SourceGraphic" result="colormatrix"></feColorMatrix><feComponentTransfer in="colormatrix" result="componentTransfer"><feFuncR type="table" tableValues="0.84 1"></feFuncR><feFuncG type="table" tableValues="0.05 0.94"></feFuncG><feFuncB type="table" tableValues="0.37 0.61"></feFuncB><feFuncA type="table" tableValues="0 1"></feFuncA></feComponentTransfer><feBlend mode="normal" in="componentTransfer" in2="SourceGraphic" result="blend"></feBlend></filter><filter id="blackCurrant-and-mint" x="-10%" y="-10%" width="120%" height="120%" filterUnits="objectBoundingBox" primitiveUnits="userSpaceOnUse" color-interpolation-filters="sRGB"><feColorMatrix type="matrix" values=".33 .33 .33 0 0
          .33 .33 .33 0 0
          .33 .33 .33 0 0
          0 0 0 1 0" in="SourceGraphic" result="colormatrix"></feColorMatrix><feComponentTransfer in="colormatrix" result="componentTransfer"><feFuncR type="table" tableValues="0.75 0.53"></feFuncR><feFuncG type="table" tableValues="0.25 0.97"></feFuncG><feFuncB type="table" tableValues="0.64 0.77"></feFuncB><feFuncA type="table" tableValues="0 1"></feFuncA></feComponentTransfer><feBlend mode="normal" in="componentTransfer" in2="SourceGraphic" result="blend"></feBlend></filter><filter id="sea" x="-10%" y="-10%" width="120%" height="120%" filterUnits="objectBoundingBox" primitiveUnits="userSpaceOnUse" color-interpolation-filters="sRGB"><feColorMatrix type="matrix" values=".33 .33 .33 0 0
          .33 .33 .33 0 0
          .33 .33 .33 0 0
          0 0 0 1 0" in="SourceGraphic" result="colormatrix"></feColorMatrix><feComponentTransfer in="colormatrix" result="componentTransfer"><feFuncR type="table" tableValues="0.02 0.13 0.8"></feFuncR><feFuncG type="table" tableValues="0.02 0.47 0.97"></feFuncG><feFuncB type="table" tableValues="0.26 0.52 0.48"></feFuncB><feFuncA type="table" tableValues="0 1"></feFuncA></feComponentTransfer><feBlend mode="normal" in="componentTransfer" in2="SourceGraphic" result="blend"></feBlend></filter><filter id="warm-sea" x="-10%" y="-10%" width="120%" height="120%" filterUnits="objectBoundingBox" primitiveUnits="userSpaceOnUse" color-interpolation-filters="sRGB"><feColorMatrix type="matrix" values=".33 .33 .33 0 0
          .33 .33 .33 0 0
          .33 .33 .33 0 0
          0 0 0 1 0" in="SourceGraphic" result="colormatrix"></feColorMatrix><feComponentTransfer in="colormatrix" result="componentTransfer"><feFuncR type="table" tableValues="0.29 0.01 0.97"></feFuncR><feFuncG type="table" tableValues="0.12 0.52 0.94"></feFuncG><feFuncB type="table" tableValues="0.37 0.59 0.47"></feFuncB><feFuncA type="table" tableValues="0 1"></feFuncA></feComponentTransfer><feBlend mode="normal" in="componentTransfer" in2="SourceGraphic" result="blend"></feBlend></filter><filter id="spring-grass" x="-10%" y="-10%" width="120%" height="120%" filterUnits="objectBoundingBox" primitiveUnits="userSpaceOnUse" color-interpolation-filters="sRGB"><feColorMatrix type="matrix" values=".33 .33 .33 0 0
          .33 .33 .33 0 0
          .33 .33 .33 0 0
          0 0 0 1 0" in="SourceGraphic" result="colormatrix"></feColorMatrix><feComponentTransfer in="colormatrix" result="componentTransfer"><feFuncR type="table" tableValues="0 0.38 0.92"></feFuncR><feFuncG type="table" tableValues="0.5 0.8 1"></feFuncG><feFuncB type="table" tableValues="0.5 0.56 0.74"></feFuncB><feFuncA type="table" tableValues="0 1"></feFuncA></feComponentTransfer><feBlend mode="normal" in="componentTransfer" in2="SourceGraphic" result="blend"></feBlend></filter><filter id="red-sunset-with-purple" x="-10%" y="-10%" width="120%" height="120%" filterUnits="objectBoundingBox" primitiveUnits="userSpaceOnUse" color-interpolation-filters="sRGB"><feColorMatrix type="matrix" values=".33 .33 .33 0 0
          .33 .33 .33 0 0
          .33 .33 .33 0 0
          0 0 0 1 0" in="SourceGraphic" result="colormatrix"></feColorMatrix><feComponentTransfer in="colormatrix" result="componentTransfer"><feFuncR type="table" tableValues="0.52 0.86 0.97"></feFuncR><feFuncG type="table" tableValues="0 0.08 0.81"></feFuncG><feFuncB type="table" tableValues="0.51 0.24 0.05"></feFuncB><feFuncA type="table" tableValues="0 1"></feFuncA></feComponentTransfer><feBlend mode="normal" in="componentTransfer" in2="SourceGraphic" result="blend"></feBlend></filter><filter id="red-sunset" x="-10%" y="-10%" width="120%" height="120%" filterUnits="objectBoundingBox" primitiveUnits="userSpaceOnUse" color-interpolation-filters="sRGB"><feColorMatrix type="matrix" values=".33 .33 .33 0 0
          .33 .33 .33 0 0
          .33 .33 .33 0 0
          0 0 0 1 0" in="SourceGraphic" result="colormatrix"></feColorMatrix><feComponentTransfer in="colormatrix" result="componentTransfer"><feFuncR type="table" tableValues="0.27 0.86 0.97"></feFuncR><feFuncG type="table" tableValues="0.01 0.08 0.81"></feFuncG><feFuncB type="table" tableValues="0.02 0.24 0.05"></feFuncB><feFuncA type="table" tableValues="0 1"></feFuncA></feComponentTransfer><feBlend mode="normal" in="componentTransfer" in2="SourceGraphic" result="blend"></feBlend></filter><filter id="gold-sunset" x="-10%" y="-10%" width="120%" height="120%" filterUnits="objectBoundingBox" primitiveUnits="userSpaceOnUse" color-interpolation-filters="sRGB"><feColorMatrix type="matrix" values=".33 .33 .33 0 0
          .33 .33 .33 0 0
          .33 .33 .33 0 0
          0 0 0 1 0" in="SourceGraphic" result="colormatrix"></feColorMatrix><feComponentTransfer in="colormatrix" result="componentTransfer"><feFuncR type="table" tableValues="0.27 0.86 1"></feFuncR><feFuncG type="table" tableValues="0.01 0.31 0.95"></feFuncG><feFuncB type="table" tableValues="0.02 0.02 0.02"></feFuncB><feFuncA type="table" tableValues="0 1"></feFuncA></feComponentTransfer><feBlend mode="normal" in="componentTransfer" in2="SourceGraphic" result="blend"></feBlend></filter><filter id="dark-crimson-sepia" x="-10%" y="-10%" width="120%" height="120%" filterUnits="objectBoundingBox" primitiveUnits="userSpaceOnUse" color-interpolation-filters="sRGB"><feColorMatrix type="matrix" values=".33 .33 .33 0 0
          .33 .33 .33 0 0
          .33 .33 .33 0 0
          0 0 0 1 0" in="SourceGraphic" result="colormatrix"></feColorMatrix><feComponentTransfer in="colormatrix" result="componentTransfer"><feFuncR type="table" tableValues="0.01 0.52 0.97"></feFuncR><feFuncG type="table" tableValues="0 0.05 0.81"></feFuncG><feFuncB type="table" tableValues="0.02 0.29 0.61"></feFuncB><feFuncA type="table" tableValues="0 1"></feFuncA></feComponentTransfer><feBlend mode="normal" in="componentTransfer" in2="SourceGraphic" result="blend"></feBlend></filter><filter id="dark-blue-sepia" x="-10%" y="-10%" width="120%" height="120%" filterUnits="objectBoundingBox" primitiveUnits="userSpaceOnUse" color-interpolation-filters="sRGB"><feColorMatrix type="matrix" values=".33 .33 .33 0 0
          .33 .33 .33 0 0
          .33 .33 .33 0 0
          0 0 0 1 0" in="SourceGraphic" result="colormatrix"></feColorMatrix><feComponentTransfer in="colormatrix" result="componentTransfer"><feFuncR type="table" tableValues="0.29 0.15 0.97"></feFuncR><feFuncG type="table" tableValues="0.04 0.39 0.93"></feFuncG><feFuncB type="table" tableValues="0.32 0.52 0.73"></feFuncB><feFuncA type="table" tableValues="0 1"></feFuncA></feComponentTransfer><feBlend mode="normal" in="componentTransfer" in2="SourceGraphic" result="blend"></feBlend></filter><filter id="dark-green-sepia" x="-10%" y="-10%" width="120%" height="120%" filterUnits="objectBoundingBox" primitiveUnits="userSpaceOnUse" color-interpolation-filters="sRGB"><feColorMatrix type="matrix" values=".33 .33 .33 0 0
          .33 .33 .33 0 0
          .33 .33 .33 0 0
          0 0 0 1 0" in="SourceGraphic" result="colormatrix"></feColorMatrix><feComponentTransfer in="colormatrix" result="componentTransfer"><feFuncR type="table" tableValues="0.25 0.39 0.96"></feFuncR><feFuncG type="table" tableValues="0.16 0.52 0.97"></feFuncG><feFuncB type="table" tableValues="0.06 0.39 0.78"></feFuncB><feFuncA type="table" tableValues="0 1"></feFuncA></feComponentTransfer><feBlend mode="normal" in="componentTransfer" in2="SourceGraphic" result="blend"></feBlend></filter><filter id="x-rays" x="-10%" y="-10%" width="120%" height="120%" filterUnits="objectBoundingBox" primitiveUnits="userSpaceOnUse" color-interpolation-filters="sRGB"><feColorMatrix type="matrix" values=".33 .33 .33 0 0
          .33 .33 .33 0 0
          .33 .33 .33 0 0
          0 0 0 1 0" in="SourceGraphic" result="colormatrix"></feColorMatrix><feComponentTransfer in="colormatrix" result="componentTransfer"><feFuncR type="table" tableValues="0.98 0.3 0.25"></feFuncR><feFuncG type="table" tableValues="1 0.44 0.24"></feFuncG><feFuncB type="table" tableValues="0.91 0.62 0.39"></feFuncB><feFuncA type="table" tableValues="0 1"></feFuncA></feComponentTransfer><feBlend mode="normal" in="componentTransfer" in2="SourceGraphic" result="blend"></feBlend></filter><filter id="warm-x-rays" x="-10%" y="-10%" width="120%" height="120%" filterUnits="objectBoundingBox" primitiveUnits="userSpaceOnUse" color-interpolation-filters="sRGB"><feColorMatrix type="matrix" values=".33 .33 .33 0 0
          .33 .33 .33 0 0
          .33 .33 .33 0 0
          0 0 0 1 0" in="SourceGraphic" result="colormatrix"></feColorMatrix><feComponentTransfer in="colormatrix" result="componentTransfer"><feFuncR type="table" tableValues="0.98 0.75 0.51"></feFuncR><feFuncG type="table" tableValues="1 0.45 0.11"></feFuncG><feFuncB type="table" tableValues="0.91 0.39 0.29"></feFuncB><feFuncA type="table" tableValues="0 1"></feFuncA></feComponentTransfer><feBlend mode="normal" in="componentTransfer" in2="SourceGraphic" result="blend"></feBlend></filter><filter id="golden-x-rays" x="-10%" y="-10%" width="120%" height="120%" filterUnits="objectBoundingBox" primitiveUnits="userSpaceOnUse" color-interpolation-filters="sRGB"><feColorMatrix type="matrix" values=".33 .33 .33 0 0
          .33 .33 .33 0 0
          .33 .33 .33 0 0
          0 0 0 1 0" in="SourceGraphic" result="colormatrix"></feColorMatrix><feComponentTransfer in="colormatrix" result="componentTransfer"><feFuncR type="table" tableValues="0.98 1 0.94"></feFuncR><feFuncG type="table" tableValues="1 0.98 0.44"></feFuncG><feFuncB type="table" tableValues="0.91 0.43 0.02"></feFuncB><feFuncA type="table" tableValues="0 1"></feFuncA></feComponentTransfer><feBlend mode="normal" in="componentTransfer" in2="SourceGraphic" result="blend"></feBlend></filter><filter id="purple-warm" x="-10%" y="-10%" width="120%" height="120%" filterUnits="objectBoundingBox" primitiveUnits="userSpaceOnUse" color-interpolation-filters="sRGB"><feColorMatrix type="matrix" values=".33 .33 .33 0 0
          .33 .33 .33 0 0
          .33 .33 .33 0 0
          0 0 0 1 0" in="SourceGraphic" result="colormatrix"></feColorMatrix><feComponentTransfer in="colormatrix" result="componentTransfer"><feFuncR type="table" tableValues="0.52 0.97 1"></feFuncR><feFuncG type="table" tableValues="0 0.62 1"></feFuncG><feFuncB type="table" tableValues="0.51 0.39 0.89"></feFuncB><feFuncA type="table" tableValues="0 1"></feFuncA></feComponentTransfer><feBlend mode="normal" in="componentTransfer" in2="SourceGraphic" result="blend"></feBlend></filter><filter id="green-pink-acid" x="-10%" y="-10%" width="120%" height="120%" filterUnits="objectBoundingBox" primitiveUnits="userSpaceOnUse" color-interpolation-filters="sRGB"><feColorMatrix type="matrix" values=".33 .33 .33 0 0
          .33 .33 .33 0 0
          .33 .33 .33 0 0
          0 0 0 1 0" in="SourceGraphic" result="colormatrix"></feColorMatrix><feComponentTransfer in="colormatrix" result="componentTransfer"><feFuncR type="table" tableValues="1 0.98 0.1"></feFuncR><feFuncG type="table" tableValues="0.17 1 0.82"></feFuncG><feFuncB type="table" tableValues="0.7 0.84 0.67"></feFuncB><feFuncA type="table" tableValues="0 1"></feFuncA></feComponentTransfer><feBlend mode="normal" in="componentTransfer" in2="SourceGraphic" result="blend"></feBlend></filter><filter id="yellow-blue-acid" x="-10%" y="-10%" width="120%" height="120%" filterUnits="objectBoundingBox" primitiveUnits="userSpaceOnUse" color-interpolation-filters="sRGB"><feColorMatrix type="matrix" values=".33 .33 .33 0 0
          .33 .33 .33 0 0
          .33 .33 .33 0 0
          0 0 0 1 0" in="SourceGraphic" result="colormatrix"></feColorMatrix><feComponentTransfer in="colormatrix" result="componentTransfer"><feFuncR type="table" tableValues="0.01 0.97 0.89"></feFuncR><feFuncG type="table" tableValues="0.38 1 1"></feFuncG><feFuncB type="table" tableValues="1 0.89 0.01"></feFuncB><feFuncA type="table" tableValues="0 1"></feFuncA></feComponentTransfer><feBlend mode="normal" in="componentTransfer" in2="SourceGraphic" result="blend"></feBlend></filter><filter id="noise" x="0%" y="0%" width="100%" height="100%"><feTurbulence baseFrequency="0.01 0.4" result="NOISE" numOctaves="2"></feTurbulence><feDisplacementMap in="SourceGraphic" in2="NOISE" scale="20" xChannelSelector="R" yChannelSelector="R"></feDisplacementMap></filter><filter id="squiggly-0"><feTurbulence id="turbulence1" baseFrequency="0.02" numOctaves="3" result="noise" seed="0"></feTurbulence><feDisplacementMap id="displacement" in="SourceGraphic" in2="noise" scale="6"></feDisplacementMap></filter><filter id="squiggly-1"><feTurbulence id="turbulence2" baseFrequency="0.02" numOctaves="3" result="noise" seed="1"></feTurbulence><feDisplacementMap in="SourceGraphic" in2="noise" scale="8"></feDisplacementMap></filter><filter id="squiggly-2"><feTurbulence id="turbulence3" baseFrequency="0.02" numOctaves="3" result="noise" seed="2"></feTurbulence><feDisplacementMap in="SourceGraphic" in2="noise" scale="6"></feDisplacementMap></filter><filter id="squiggly-3"><feTurbulence id="turbulence4" baseFrequency="0.02" numOctaves="3" result="noise" seed="3"></feTurbulence><feDisplacementMap in="SourceGraphic" in2="noise" scale="8"></feDisplacementMap></filter><filter id="squiggly-4"><feTurbulence id="turbulence5" baseFrequency="0.02" numOctaves="3" result="noise" seed="4"></feTurbulence><feDisplacementMap in="SourceGraphic" in2="noise" scale="6"></feDisplacementMap></filter><filter id="posterize"><feComponentTransfer><feFuncR type="discrete" tableValues="0 .5 1"></feFuncR></feComponentTransfer></filter><filter id="dancing" x="-20%" y="-20%" width="140%" height="140%" filterUnits="objectBoundingBox" primitiveUnits="userSpaceOnUse" color-interpolation-filters="linearRGB"><feMorphology operator="dilate" radius="4 4" in="SourceAlpha" result="morphology"></feMorphology><feFlood flood-color="#30597E" flood-opacity="1" result="flood"></feFlood><feComposite in="flood" in2="morphology" operator="in" result="composite"></feComposite><feComposite in="composite" in2="SourceAlpha" operator="out" result="composite1"></feComposite><feTurbulence type="fractalNoise" baseFrequency="0.01 0.02" numOctaves="1" seed="0" stitchTiles="stitch" result="turbulence"></feTurbulence><feDisplacementMap in="composite1" in2="turbulence" scale="17" xChannelSelector="A" yChannelSelector="A" result="displacementMap"></feDisplacementMap><feMerge result="merge"><feMergeNode in="SourceGraphic"></feMergeNode><feMergeNode in="displacementMap"></feMergeNode></feMerge></filter><filter id="drops" x="-20%" y="-20%" width="140%" height="140%" filterUnits="objectBoundingBox" primitiveUnits="userSpaceOnUse" color-interpolation-filters="sRGB"><feTurbulence type="turbulence" baseFrequency="0.05 0.05" numOctaves="1" seed="3" stitchTiles="stitch" result="turbulence"></feTurbulence><feComposite in="turbulence" in2="SourceGraphic" operator="in" result="composite"></feComposite><feColorMatrix type="matrix" values="1 0 0 0 0
          0 1 0 0 0
          0 0 1 0 0
          0 0 0 25 -2" in="composite" result="colormatrix"></feColorMatrix><feComposite in="SourceGraphic" in2="colormatrix" operator="in" result="composite1"></feComposite><feGaussianBlur stdDeviation="3 3" in="composite1" result="blur"></feGaussianBlur><feSpecularLighting surfaceScale="2" specularConstant="1" specularExponent="20" lighting-color="#fffffd" in="blur" result="specularLighting"><feDistantLight azimuth="-90" elevation="150"></feDistantLight></feSpecularLighting><feSpecularLighting surfaceScale="2" specularConstant="1" specularExponent="20" lighting-color="#cae1fe" in="blur" result="specularLighting1"><feDistantLight azimuth="90" elevation="150"></feDistantLight></feSpecularLighting><feSpecularLighting surfaceScale="7" specularConstant="1" specularExponent="35" lighting-color="#fcfeff" in="blur" result="specularLighting2"><fePointLight x="150" y="50" z="300"></fePointLight></feSpecularLighting><feComposite in="specularLighting" in2="composite1" operator="in" result="composite2"></feComposite><feComposite in="specularLighting2" in2="composite1" operator="in" result="composite3"></feComposite><feComposite in="specularLighting1" in2="composite1" operator="in" result="composite4"></feComposite><feBlend mode="multiply" in="composite4" in2="SourceGraphic" result="blend"></feBlend><feBlend in="composite2" in2="blend" result="blend1"></feBlend><feBlend in="composite3" in2="blend1" result="blend2"></feBlend></filter><filter id="grain"><feTurbulence baseFrequency="0.60,0.90" result="colorNoise"></feTurbulence><feColorMatrix in="colorNoise" type="matrix" values=".33 .33 .33 0 0 .33 .33 .33 0 0 .33 .33 .33 0 0 0 0 0 1 0"></feColorMatrix><feComposite operator="in" in2="SourceGraphic" result="monoNoise"></feComposite><feBlend in="SourceGraphic" in2="monoNoise" mode="multiply"></feBlend></filter><filter id="fluffy" x="0%" y="0%" width="100%" height="100%"><feTurbulence type="fractalNoise" baseFrequency="0.04" result="fluffyNoise" numOctaves="5"></feTurbulence><feColorMatrix in="fluffyNoise" type="matrix" values=".33 .33 .33 0 0 .33 .33 .33 0 0 .33 .33 .33 0 0 0 0 0 1 0"></feColorMatrix><feComposite operator="in" in2="SourceGraphic" result="monoFluffyNoise"></feComposite><feBlend in="SourceGraphic" in2="monoFluffyNoise" mode="screen"></feBlend></filter></defs></svg><div id="app"></div></body></html>