import SketchViewModel from '@arcgis/core/widgets/Sketch/SketchViewModel.js'
import { setSymbol } from '@/utils/MapHelper'

export const useSketch = () => {
  let createHandler: any
  let updateHandler: any
  let deleteHandler: any
  let undoHandler: any
  let redoHandler: any
  const sketch: {
    value: __esri.SketchViewModel | undefined
  } = {
    value: undefined
  }
  /**
   * 初始化画笔
   * @param view
   * @param graphicsLayer
   * @param options
   * @returns
   */
  const initSketch = (
    view?: __esri.MapView,
    graphicsLayer?: __esri.GraphicsLayer,
    /** create or update callback, more callbacks please view  the attributes of param nameed operationCallbacks */
    // callBack?: (graphic: Graphic) => any,
    operationCallbacks?: {
      createCallBack?: (result: ISketchHandlerParameter) => any
      updateCallBack?: (result: ISketchHandlerParameter) => any
      delCallBack?: (result: ISketchHandlerParameter) => any
      undoCallBack?: (result: ISketchHandlerParameter) => any
      redoCallBack?: (result: ISketchHandlerParameter) => any
      snappingOptions?: __esri.SnappingOptionsProperties
    }
  ) => {
    sketch.value = new SketchViewModel({
      view,
      layer: graphicsLayer,
      polygonSymbol: setSymbol('polygon') as any,
      polylineSymbol: setSymbol('polyline') as any,
      pointSymbol: setSymbol('point') as any,
      snappingOptions: operationCallbacks?.snappingOptions
    })
    createHandler = sketch.value?.on('create', result => {
      const res = {
        graphics: (result.graphic && [result.graphic]) || [],
        state: result.state,
        tool: result.tool,
        toolEventInfo: result.toolEventInfo,
        type: result.type
      }
      operationCallbacks?.createCallBack && operationCallbacks.createCallBack(res)
    })
    updateHandler = sketch.value?.on('update', result => {
      const res = {
        graphics: result.graphics,
        state: result.state,
        tool: result.tool,
        toolEventInfo: result.toolEventInfo,
        type: result.type,
        aborted: result.aborted
      }
      operationCallbacks?.updateCallBack && operationCallbacks.updateCallBack(res)
    })
    deleteHandler = sketch.value?.on('delete', result => {
      const res = {
        graphics: result.graphics,
        tool: result.tool,
        type: result.type
      }
      operationCallbacks?.delCallBack && operationCallbacks.delCallBack(res)
    })
    undoHandler = sketch.value?.on('undo', result => {
      const res = {
        graphics: result.graphics,
        tool: result.tool,
        type: result.type
      }
      operationCallbacks?.undoCallBack && operationCallbacks.undoCallBack(res)
    })
    redoHandler = sketch.value?.on('redo', result => {
      const res = {
        graphics: result.graphics,
        tool: result.tool,
        type: result.type
      }
      operationCallbacks?.redoCallBack && operationCallbacks.redoCallBack(res)
    })
    return sketch.value
  }
  const destroySketch = () => {
    createHandler?.remove()
    updateHandler?.remove()
    deleteHandler?.remove()
    undoHandler?.remove()
    redoHandler?.remove()
    sketch.value?.destroy()
  }
  return {
    initSketch,
    destroySketch,
    sketch
  }
}

export default useSketch
