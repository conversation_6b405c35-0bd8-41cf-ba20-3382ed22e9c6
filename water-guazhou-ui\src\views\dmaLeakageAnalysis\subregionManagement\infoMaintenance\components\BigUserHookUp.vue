<template>
  <div class="device-hookup">
    <div class="left">
      <SLTree :tree-data="Tree"></SLTree>
    </div>
    <div class="right overlay-y">
      <Search
        ref="refSearch_All"
        class="right-search"
        :config="Search_All"
      ></Search>
      <FormTable
        class="right-table"
        :config="Table_All"
      ></FormTable>
      <Search
        ref="refSearch_New"
        class="right-search"
        :config="Search_New"
      ></Search>
      <FormTable
        class="right-table"
        :config="Table_New"
      ></FormTable>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { EditPen, Search as SearchIcon } from '@element-plus/icons-vue'
import { CirclePlus, Delete } from '@element-plus/icons-vue'
import { Action, ElMessageBox } from 'element-plus'
import {
  DeleteDMADevices,
  DMADirection,
  GetDMADeviceList,
  GetDMAHookedDevices,
  HookupDMADevice,
  PostDMADeviceDirection
} from '@/api/mapservice/dma'
import { ISearchIns } from '@/components/type'
import { SLConfirm, SLMessage } from '@/utils/Message'
import { formatDate } from '@/utils/DateFormatter'

const refSearch_All = ref<ISearchIns>()
const refSearch_New = ref<ISearchIns>()
const props = defineProps<{
  tree: NormalOption[]
  currentTreeNode: NormalOption
}>()
const Tree = reactive<SLTreeConfig>({
  data: props.tree,
  currentProject: props.currentTreeNode,
  treeNodeHandleClick: data => {
    Tree.currentProject = data
    refreshAllData()
    refreshNewData()
  }
})
const Search_All = reactive<ISearch>({
  filters: [
    // { type: 'input', label: '编号', field: 'code' },
    { type: 'input', label: '名称', field: 'name' }
    // { type: 'input', label: 'RTU编号', field: 'code' },
    // {
    //   type: 'select',
    //   label: '设备类型',
    //   field: 'type',
    //   options: [
    //     { label: '流量计', value: '1' },
    //     { label: '压力计', value: '2' }
    //     // { label: '大用户', value: '3' }
    //   ]
    // }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          svgIcon: shallowRef(SearchIcon),
          click: () => refreshAllData()
        },
        {
          perm: true,
          text: '添加',
          svgIcon: shallowRef(CirclePlus),
          click: () => handleAdd(),
          loading: computed(() => Table_All.loading) as any
        }
      ]
    }
  ],
  defaultParams: {
    type: '1'
  }
})
const Table_All = reactive<ITable>({
  indexVisible: true,
  dataList: [],
  pagination: {
    refreshData: ({ page, size }) => {
      Table_All.pagination.page = page
      Table_All.pagination.limit = size
      refreshAllData()
    }
  },
  columns: [
    { label: '设备', prop: 'name' },
    { label: '类型', prop: 'deviceTypeName' },
    {
      label: '创建时间',
      prop: 'createTime',
      formatter: (row, val) => formatDate(val)
    }
    // { label: '设备来源', prop: 'code' }
  ],
  handleSelectChange: rows => {
    Table_All.selectList = rows || []
  }
})
const refreshAllData = async () => {
  try {
    Table_All.loading = true
    const res = await GetDMADeviceList({
      page: Table_All.pagination.page || 1,
      size: Table_All.pagination.limit || 20,
      ...(refSearch_All.value?.queryParams || {}),
      type: '3'
    })
    const data = res.data.data || {}
    Table_All.dataList = data.data
    Table_All.pagination.total = data.total || 0
  } catch (error) {
    SLMessage.error('查询失败')
  }
  Table_All.loading = false
}
const Search_New = reactive<ISearch>({
  filters: [
    // { type: 'input', label: '编号', field: 'code' },
    { type: 'input', label: '名称', field: 'name' }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          svgIcon: shallowRef(SearchIcon),
          click: () => refreshNewData()
        },
        {
          perm: true,
          text: '移出',
          svgIcon: shallowRef(Delete),
          type: 'danger',
          loading: computed(() => Table_New.loading) as any,
          click: () => handleRemove()
        }
      ]
    }
  ]
})
const Table_New = reactive<ITable>({
  indexVisible: true,
  dataList: [],
  pagination: {
    refreshData: ({ page, size }) => {
      Table_New.pagination.page = page
      Table_New.pagination.limit = size
      refreshNewData()
    }
  },
  columns: [
    // { label: '编号', prop: 'code' },
    { label: '名称', prop: 'name' },
    {
      label: '类型',
      prop: 'type',
      formatter: (row, val) => {
        return val === '3' ? '大用户' : val
      }
    },
    {
      label: '水流方向',
      prop: 'direction',
      tag: true,
      tagColor: (row: any, val: string) => {
        return val === '1' ? '#318DFF' : '#f56c6c'
      },
      formatter: (row, val) => DMADirection[val]
    },
    {
      label: '是否核算',
      prop: 'isAccount',
      formatter: (row, val) => {
        return val === '1' ? '是' : '否'
      }
    }
  ],
  handleSelectChange: rows => {
    Table_New.selectList = rows || []
  },
  operations: [
    {
      perm: true,
      text: '变更方向',
      svgIcon: shallowRef(EditPen),
      click: row => handleDirectionChange(row.id)
    }
  ]
})
const handleDirectionChange = id => {
  SLConfirm('确定变更方向？', '提示信息').then(async () => {
    try {
      const res = await PostDMADeviceDirection(id)
      if (res.data.code === 200) {
        SLMessage.success('操作成功')
        refreshNewData()
      } else {
        SLMessage.error(res.data.message)
      }
    } catch (error) {
      SLMessage.error('操作失败')
    }
  })
}
const refreshNewData = async () => {
  if (!Tree.currentProject) {
    // SLMessage.warning('请选择一个分区')
    Table_New.dataList = []
    Table_New.pagination.total = 0
    return
  }
  try {
    Table_New.loading = true
    const res = await GetDMAHookedDevices({
      page: Table_New.pagination.page || 1,
      size: Table_New.pagination.limit || 20,
      partitionId: Tree.currentProject?.id,
      ...(refSearch_All.value?.queryParams || {}),
      type: '3'
    })
    const data = res.data.data || {}
    Table_New.dataList = data.data
    Table_New.pagination.total = data.total || 0
  } catch (error) {
    SLMessage.error('查询失败')
  }
  Table_New.loading = false
}
const handleAddComfirmed = async (isAccount: string) => {
  const selected = Table_All.selectList || []
  if (!selected.length) {
    SLMessage.warning('请选择要添加的设备')
    return
  }
  try {
    Table_All.loading = true
    Table_New.loading = true
    const res = await HookupDMADevice(
      selected.map(item => {
        return {
          partitionId: Tree.currentProject?.id,
          deviceId: item.id,
          type: '3',
          isAccount
        }
      })
    )
    if (res.data.code === 200) {
      refreshAllData()
      refreshNewData()
    } else {
      SLMessage.error(res.data.message)
    }
  } catch (error) {
    SLMessage.error('添加失败')
  }
  Table_All.loading = false
  Table_New.loading = false
}
const handleAdd = async () => {
  ElMessageBox.confirm('是否核算？', '提示信息', {
    distinguishCancelAndClose: true,
    confirmButtonText: '是',
    cancelButtonText: '否'
  })
    .then(() => {
      console.log('confirm')
      handleAddComfirmed('1')
    })
    .catch((action: Action) => {
      if (action === 'cancel') {
        handleAddComfirmed('0')
      }
    })
}

const handleRemove = async () => {
  const selected = Table_New.selectList || []
  if (!selected.length) {
    SLMessage.warning('请选择要移出的设备')
    return
  }
  SLConfirm('确定移出？', '提示信息')
    .then(async () => {
      try {
        Table_All.loading = true
        Table_New.loading = true
        const res = await DeleteDMADevices(selected.map(item => item.id))
        if (res.data.code === 200) {
          refreshAllData()
          refreshNewData()
        } else {
          SLMessage.error(res.data.message)
        }
      } catch (error) {
        SLMessage.error('移出失败')
      }
      Table_All.loading = false
      Table_New.loading = false
    })
    .catch(() => {
      //
    })
}
onMounted(() => {
  refreshAllData()
  refreshNewData()
})
</script>
<style lang="scss" scoped>
.device-hookup {
  display: flex;
  border-collapse: collapse;
  .left,
  .right,
  .right-table {
    border-collapse: collapse;
    border: 1px solid var(--el-border-color);
  }
  .left {
    width: 300px;
  }
  .right {
    width: calc(100% - 300px);
    border-left: none;
    .right-search {
      margin: 8px 0;
    }
    .right-table {
      height: 260px;
      border-left: none;
      border-right: none;
      &:last-child {
        border-bottom: none;
      }
    }
  }
}
</style>
