package org.thingsboard.server.dao.util.imodel.query.smartService.wechat;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartService.wechat.WxMessageRecord;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;

@Getter
@Setter
public class WxMessageRecordSaveRequest extends SaveRequest<WxMessageRecord> {

    @Override
    protected WxMessageRecord build() {
        WxMessageRecord entity = new WxMessageRecord();
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    @Override
    protected WxMessageRecord update(String id) {
        WxMessageRecord entity = new WxMessageRecord();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(WxMessageRecord entity) {
    }
}