package org.thingsboard.server.dao.model.VO;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 站点进出水数据
 */
@Data
public class StationWaterOutletAndInletDataVO {

    private String stationId;

    private String name;

    // 出水总流量
    private BigDecimal outletTotalFlow;

    // 进水总流量
    private BigDecimal inletTotalFlow;

    // 进出水差值
    private BigDecimal differenceTotalFlow;

    // 差值率
    private BigDecimal differenceRate;
}
