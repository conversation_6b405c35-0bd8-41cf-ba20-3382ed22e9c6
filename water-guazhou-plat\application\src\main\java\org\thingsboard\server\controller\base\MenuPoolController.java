/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.controller.base;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.exception.ThingsboardErrorCode;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.MenuPoolId;
import org.thingsboard.server.common.data.menu.Menu;
import org.thingsboard.server.common.data.menu.MenuMeta;
import org.thingsboard.server.common.data.menu.MenuPool;
import org.thingsboard.server.common.data.menu.MenuPoolVO;
import org.thingsboard.server.dao.menu.MenuPoolService;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.service.aspect.annotation.SysLog;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/menu")
public class MenuPoolController extends BaseController {

    public static final String PARENT_ID = "parentId";
    public static final String MENU_POOL_ID = "id";


    @Autowired
    private MenuPoolService menuPoolService;

    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS','CUSTOMER_USER')")
    @RequestMapping("/pool/{id}")
    public Menu getById(@PathVariable(MENU_POOL_ID) String strMenuPoolId) throws ThingsboardException {
        checkParameter(MENU_POOL_ID, strMenuPoolId);
        try {
            MenuPoolId id = new MenuPoolId(toUUID(strMenuPoolId));
            return checkNotNull(menuPoolService.findById(id));
        } catch (Exception e) {
            throw handleException(e);
        }
    }

    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS','CUSTOMER_USER')")
    @RequestMapping(value = "/pool/getRootId", method = RequestMethod.GET)
    public MenuPoolId getRootId() {
        return new MenuPoolId(ModelConstants.MENU_POOL_ROOT);
    }


    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS','CUSTOMER_USER')")
    @RequestMapping(value = "/pool/children/{parentId}", method = RequestMethod.GET)
    public List<Menu> getByParentId(@PathVariable(PARENT_ID) String strParentId) throws ThingsboardException {
        checkParameter(PARENT_ID, strParentId);
        try {
            MenuPoolId parentId = new MenuPoolId(toUUID(strParentId));

            return checkNotNull(menuPoolService.findMenuByParentId(parentId));
        } catch (Exception e) {
            throw handleException(e);
        }
    }


    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS','CUSTOMER_USER')")
    @RequestMapping(value = "/pool", method = RequestMethod.POST)
    @SysLog(detail = DataConstants.OPERATING_TYPE_MENU_POOL_ADD)
    public MenuPool saveMenuPool(@RequestBody Map map) throws ThingsboardException {
        checkNotNull(map);
        MenuPoolId parentId = null;
        if (StringUtils.isNotBlank((String) map.get("parentId"))) {
            parentId = new MenuPoolId(toUUID((String) map.get("parentId")));
        }

        Menu menu = buildMenu((Map) map.get("data"));

        return menuPoolService.saveMenu(menu, parentId);
    }

    /**
     * 构建menu数据
     *
     * @param data
     * @return
     */
    private Menu buildMenu(Map data) throws ThingsboardException {
        Menu menu = new Menu();
        if (StringUtils.isNotBlank((String) data.get("id"))) {
            menu.setId((String) data.get("id"));
        }

        if (StringUtils.isBlank((String) data.get("type"))) {
            throw new ThingsboardException("type属性为必传属性",
                    ThingsboardErrorCode.BAD_REQUEST_PARAMS);
        }
        menu.setType(Integer.valueOf((String) data.get("type")));

        if (StringUtils.isNotBlank((String) data.get("orderNum"))) {
            menu.setOrderNum(Integer.valueOf((String) data.get("orderNum")));
        }

        if (StringUtils.isNotBlank((String) data.get("path"))) {
            menu.setPath((String) data.get("path"));
        }

        if (StringUtils.isNotBlank((String) data.get("name"))) {
            menu.setName((String) data.get("name"));
        }
        if (StringUtils.isNotBlank((String) data.get("component"))) {
            menu.setComponent((String) data.get("component"));
        }
        if (data.get("meta") != null) {
            MenuMeta meta = new MenuMeta();
            if (StringUtils.isBlank((String) ((Map) data.get("meta")).get("title"))) {
                throw new ThingsboardException("type属性为必传属性",
                        ThingsboardErrorCode.BAD_REQUEST_PARAMS);
            }
            meta.setTitle((String) ((Map) data.get("meta")).get("title"));
            meta.setIcon((String) ((Map) data.get("meta")).get("icon"));

            menu.setMeta(meta);
        }

        return menu;
    }

    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS','CUSTOMER_USER')")
    @RequestMapping(value = "/pool/findAll", method = RequestMethod.GET)
    public List<Menu> findAll() throws ThingsboardException {
        MenuPoolId rootId = getRootId();

        return checkNotNull(menuPoolService.findAll(rootId));
    }

    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS','CUSTOMER_USER')")
    @RequestMapping(value = "/pool/getSelectableTree", method = RequestMethod.GET)
    public List<MenuPoolVO> getSelectableTree() {
        return menuPoolService.getSelectableTree();
    }
}
