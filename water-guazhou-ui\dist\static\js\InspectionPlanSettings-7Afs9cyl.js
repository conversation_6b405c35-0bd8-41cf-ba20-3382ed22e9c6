import{d as k,r as g,s as m,c as p,x as d,y as q,o as C,g as D,n as I,q as f,p as B,aq as S,bL as V,bM as R,bq as W,C as Y}from"./index-r0dFAfgr.js";import{_ as P}from"./Search-NSrhrIa_.js";import{g as w,d as N,I as E}from"./InspectionPlanDialog-HwehfEHd.js";import{f as L}from"./DateFormatter-Bm9a68Ax.js";/* empty css                         */const z={class:"inspection-plan-settings"},A={class:"table-box"},H=k({__name:"InspectionPlanSettings",setup(j){const _=g({labelWidth:80,filters:[{type:"input",label:"巡检计划名称",field:"planName"},{type:"select",label:"巡检类型",field:"inspectionType",options:[{label:"管网巡检",value:"pipeline"},{label:"泵站巡检",value:"pumpStation"},{label:"其他巡检",value:"other"}]},{type:"select",label:"巡检周期",field:"inspectionCycle",options:[{label:"每周一、三、五日执行",value:"weekly_135"},{label:"每月5、15、25日执行",value:"monthly_51525"},{label:"每季度第一个月10日执行",value:"quarterly_10"}]},{type:"select",label:"执行角色",field:"executionRole",options:[{label:"运维组长、设备专员",value:"maintenance_equipment"},{label:"巡检员、设备专员",value:"inspector_equipment"},{label:"其他",value:"other"}]},{type:"select",label:"状态",field:"status",options:[{label:"启用",value:"1"},{label:"停用",value:"0"}]},{type:"daterange",label:"创建时间",field:"createTime",format:"YYYY-MM-DD"},{type:"btn-group",btns:[{perm:!0,text:"查询",type:"primary",click:()=>l()},{perm:!0,text:"重置",type:"default",click:()=>{var e;(e=u.value)==null||e.resetForm(),l()}}]}]}),a=g({title:"巡检计划列表",height:"calc(100vh - 180px)",indexVisible:!0,columns:[{minWidth:150,label:"巡检计划名称",prop:"planName"},{minWidth:120,label:"巡检类型",prop:"inspectionType",formatter:e=>({pipeline:"管网巡检",pumpStation:"泵站巡检",other:"其他巡检"})[e.inspectionType]||e.inspectionType},{minWidth:180,label:"巡检周期",prop:"inspectionCycle",formatter:e=>({weekly_135:"每周一、三、五日执行",monthly_51525:"每月5、15、25日执行",quarterly_10:"每季度第一个月10日执行"})[e.inspectionCycle]||e.inspectionCycle},{minWidth:150,label:"执行角色",prop:"executionRole",formatter:e=>({maintenance_equipment:"运维组长、设备专员",inspector_equipment:"巡检员、设备专员",other:"其他"})[e.executionRole]||e.executionRole},{minWidth:150,label:"检查表模板",prop:"checklistTemplate",formatter:e=>({equipment_status:"设备运行状态检查表",pipeline_pressure:"管网压力检查表",pump_station:"泵站运行检查表",water_quality:"水质检测检查表",safety_hazard:"安全隐患检查表"})[e.checklistTemplate]||e.checklistTemplate},{minWidth:160,label:"创建时间",prop:"createTime",formatter:e=>L(e.createTime,"YYYY-MM-DD HH:mm:ss")},{minWidth:100,label:"状态",prop:"status",formatter:e=>e.status==="1"?"启用":"停用"}],dataList:[],loading:!1,pagination:{page:1,limit:20,total:0,refreshData:({page:e,size:t})=>{a.pagination.page=e,a.pagination.limit=t,l()}},operations:[{perm:!0,text:"查看",isTextBtn:!0,svgIcon:m(V),click:e=>b(e)},{perm:!0,text:"编辑",isTextBtn:!0,svgIcon:m(R),click:e=>h(e)},{perm:!0,text:"删除",isTextBtn:!0,svgIcon:m(W),click:e=>v(e)}],titleRight:[{style:{justifyContent:"flex-end"},items:[{type:"btn-group",btns:[{perm:!0,text:"新增计划",type:"primary",click:()=>x()}]}]}]}),u=p(),n=p(!1),s=p(""),r=p(!1),l=async()=>{var e;try{a.loading=!0;const t=((e=u.value)==null?void 0:e.queryParams)||{};let c="",y="";t.createTime&&Array.isArray(t.createTime)&&(c=t.createTime[0],y=t.createTime[1]);const M={page:a.pagination.page||1,size:a.pagination.limit||20,planName:t.planName,inspectionType:t.inspectionType,inspectionCycle:t.inspectionCycle,executionRole:t.executionRole,status:t.status,fromTime:c,toTime:y},i=await w(M);if(i!=null&&i.data){const o=i.data.data||i.data;a.dataList=o.data||o.records||o.content||[],a.pagination.total=o.total||o.totalElements||0}}catch(t){console.error(t),d.error("获取数据失败")}finally{a.loading=!1}},b=e=>{s.value=e.id,r.value=!0,n.value=!0},h=e=>{s.value=e.id,r.value=!1,n.value=!0},v=e=>{q.confirm("确定要删除该巡检计划吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{await N(e.id),d.success("删除成功"),l()}catch(t){console.error(t),d.error("删除失败")}}).catch(()=>{})},x=()=>{s.value="",r.value=!1,n.value=!0},T=()=>{l()};return C(()=>{l()}),(e,t)=>(D(),I("div",z,[f(P,{ref_key:"refSearch",ref:u,config:_,style:{"margin-bottom":"8px",padding:"12px","background-color":"#fff","border-radius":"4px"}},null,8,["config"]),B("div",A,[f(S,{config:a},null,8,["config"])]),f(E,{modelValue:n.value,"onUpdate:modelValue":t[0]||(t[0]=c=>n.value=c),"edit-id":s.value,readonly:r.value,onSuccess:T},null,8,["modelValue","edit-id","readonly"])]))}}),O=Y(H,[["__scopeId","data-v-2f7ca164"]]);export{O as default};
