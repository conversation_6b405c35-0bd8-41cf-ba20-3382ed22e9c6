package org.thingsboard.server.dao.model.sql.smartService.call;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 号码归属地
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2021-11-01
 */
@TableName("tb_service_work_order")
@Data
public class CallWorkOrder {

    @TableId
    private String id;

    private String workOrderId;

    private String serialNo;

    private transient String status;

    private transient String statusName;

    private String name;

    private String phone;

    // 用户编号
    @TableField(exist = false)
    private String custCode = "无";

    private String source;

    private transient String sourceName;

    private String area;

    @TableField(exist = false)
    private String areaName;

    private String address;

    private String type;

    @TableField(exist = false)
    private String typeName;

    private String topic;

    @TableField(exist = false)
    private String topicName;

    private String homeName;

    private String creator;

    private String creatorName;

    private String seatsId;

    @TableField(exist = false)
    private String seatsName;

    @TableField(exist = false)
    private String departmentName;

    @TableField(exist = false)
    private String level;

    @TableField(exist = false)
    private Integer processLevel;

    @TableField(exist = false)
    private String processLevelLabel;

    @TableField(exist = false)
    private String processUserId;

    @TableField(exist = false)
    private String receiveDepartmentId;

    @TableField(exist = false)
    private String receiveDepartmentName;

    private String tenantId;

    private String isDispatch;

    private Date createTime;

    private String callId;

    private String reply;

    private String remark;

    @TableField(exist = false)
    private Date callTime;

    @TableField(exist = false)
    private String fromPhone;

    @TableField(exist = false)
    private String meterBookName;

    @TableField(exist = false)
    private String chaoShi;
}
