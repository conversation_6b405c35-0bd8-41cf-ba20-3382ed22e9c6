<template>
  <ArcView
    ref="refMap"
    v-model="location"
    class="location-map"
    :map-config="defaultMapConfig"
    @loaded="(view) => emit('loaded', view)"
  >
    <ArcPipe></ArcPipe>
    <ArcLocatePicker
      v-if="showInput"
      v-model="location"
      class="locate-search-box"
    ></ArcLocatePicker>
  </ArcView>
</template>
<script lang="ts" setup>
import { InverseGeocoding } from '@/api/mapservice/utils';

const refMap = ref<IArcViewIns>();
const emit = defineEmits(['change', 'update:modelValue', 'loaded']);
const props = defineProps<{
  /**
   * 坐标： 绑定值格式： [lon,lat]
   */
  modelValue?: number[];
  showInput?: boolean;
  row?: any;
  disabled?: boolean | ((value: any, row: any, formItem: IFormItem) => boolean);
  readonly?: boolean | ((value: any, row: any, formItem: IFormItem) => boolean);
  handleInverseGeocodeing?: (res?: any, row?: any) => any;
  mapConfig?: IFormGisConfig;
}>();
const defaultMapConfig = reactive<IFormGisConfig>({
  ...window.SITE_CONFIG.FORM.GIS_CONFIG,
  ...props.mapConfig
});
const location = ref<number[] | undefined>(props.modelValue);
watch(
  () => props.modelValue,
  (newVal) => {
    location.value = newVal;
  }
);
watch(
  () => location.value,
  (newVal) => {
    if (newVal === props.modelValue) return;
    emit('update:modelValue', newVal);
    emit('change', newVal);
    InverseGeocoding({
      lon: (newVal?.[0] || '0')?.toString(),
      lat: (newVal?.[1] || '0')?.toString()
    }).then((res) => {
      props.handleInverseGeocodeing?.(res, props.row);
    });
  }
);
const getView = () => {
  return refMap.value?.getView();
};
defineExpose({
  getView
});
</script>
<style lang="scss" scoped>
.location-map {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;
  .location-search-box {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
  }
}

.message-text {
  margin: 10px 0 12px 20px;
  color: #39b01c;
  line-height: initial;
}
</style>
