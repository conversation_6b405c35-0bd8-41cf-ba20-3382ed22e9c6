package org.thingsboard.server.controller.maintainCircuit.maintain;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.maintainCircuit.maintain.MaintainPlanMService;
import org.thingsboard.server.dao.model.sql.maintainCircuit.maintain.MaintainPlanM;
import org.thingsboard.server.dao.util.imodel.StringUtils;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.Date;
import java.util.List;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-09-08
 */
@RestController
@RequestMapping("api/maintain/plan/m")
public class MaintainPlanMController extends BaseController {

    @Autowired
    private MaintainPlanMService maintainPlanMService;

    @GetMapping
    public IstarResponse getList(@RequestParam(required = false, defaultValue = "") String planName,
                                 @RequestParam(required = false, defaultValue = "") String teamName,
                                 @RequestParam(required = false, defaultValue = "") String userName,
                                 Long startStartTime,
                                 Long startEndTime,
                                 Long endStartTime,
                                 Long endEndTime,
                                 int page, int size) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());

        return IstarResponse.ok(maintainPlanMService.getList(planName, teamName, userName, startStartTime == null ? null : new Date(startStartTime), startEndTime == null ? null : new Date(startEndTime), endStartTime == null ? null : new Date(endStartTime), endEndTime == null ? null : new Date(endEndTime), page, size, tenantId));
    }

    @GetMapping("detail/{mainId}")
    public IstarResponse getDetail(@PathVariable String mainId) {
        return IstarResponse.ok(maintainPlanMService.getDetail(mainId));
    }

    @PostMapping
    public IstarResponse save(@RequestBody MaintainPlanM maintainPlanM) throws ThingsboardException {
        // 审核过不许修改
        if (!StringUtils.isNullOrEmpty(maintainPlanM.getId()) && !"0".equals(maintainPlanM.getStatus())) {
            return IstarResponse.error("改计划已审核，不允许修改！");
        }
        maintainPlanM.setCreator(UUIDConverter.fromTimeUUID(getCurrentUser().getUuidId()));
        maintainPlanM.setTenantId(UUIDConverter.fromTimeUUID(getTenantId().getId()));
        return IstarResponse.ok(maintainPlanMService.save(maintainPlanM));
    }

    @PostMapping("reviewer")
    public IstarResponse reviewer(@RequestBody MaintainPlanM maintainPlanM) throws ThingsboardException {
        maintainPlanM.setReviewer(UUIDConverter.fromTimeUUID(getCurrentUser().getUuidId()));
        maintainPlanMService.reviewer(maintainPlanM);

        return IstarResponse.ok("审核成功");
    }

    @DeleteMapping
    public IstarResponse delete(@RequestBody List<String> ids) {
        return maintainPlanMService.delete(ids);
    }


    /**
     * 保养计划
     */
    @GetMapping("list/{deviceLabelCode}")
    public IstarResponse getList(@PathVariable String deviceLabelCode, int page, int size) {
        return IstarResponse.ok(maintainPlanMService.getMaintainList(deviceLabelCode, page, size));
    }
}
