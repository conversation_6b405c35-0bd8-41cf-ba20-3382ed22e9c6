<template>
  <div class="table_right">
    <div class="radio">
      <el-radio-group
        v-if="newValue.radio"
        v-model="newValue.radio"
        class="ml-4"
      >
        <el-radio
          v-for="(item,index) in newValue.radioOption"
          :key="index"
          :label="item.value"
          size="large"
        >
          {{ item.label }}
        </el-radio>
      </el-radio-group>
    </div>
    <div style="display:flex;height: 100%;width: 100%;">
      <div class="table_right_left">
        <el-button type="primary">
          水泵运行状态
        </el-button>
        <el-button type="primary">
          压力
        </el-button>
        <el-button type="primary">
          瞬时流量
        </el-button>
      </div>
      <div class="table_right_right">
        <VChart
          ref="refChart"
          autoresize
          theme="dark"
          :option="newValue.option"
        ></VChart>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { IECharts } from '@/plugins/echart'

const props = defineProps<{ modelValue: any }>()
const emit = defineEmits(['update:modelValue'])
const newValue = computed({
  get: () => props.modelValue,
  set: nv => {
    emit('update:modelValue', nv)
  }
})

const refChart = ref<IECharts>()

onMounted(() => {
  window.addEventListener('resize', resizeChart)
})

function resizeChart() {
  refChart.value?.resize()
}
</script>

<style lang="scss" scoped>
.radio {
  margin: -10px;
  padding: 10px 20px 10px;
}

.table_right {
  flex: 1;
  height: 100%;
  padding-left: 10px;
  border-left: 2px solid var(--el-border-color-lighter);
  display: flex;
  flex-direction: column;

  .table_right_left {
    padding-left: 10px;
    width: 200px;

    .el-button {
      width: 190px;
      margin-left: 0;
      margin-top: 15px;
    }
  }

  .table_right_right {
    width: 100%;
    height: 100%;

    .chart-box {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
