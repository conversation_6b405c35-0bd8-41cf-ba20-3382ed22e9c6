package org.thingsboard.server.dao.model.sql;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;
import java.util.Date;

@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.WATER_PUMP_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class WaterPumpEntity {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.WATER_PUMP_NAME)
    private String name;

    @Column(name = ModelConstants.WATER_PUMP_LOCATION)
    private String location;

    @Column(name = ModelConstants.ALARM_CREATE_TIME)
    private Date createTime;

    @Column(name = ModelConstants.PROJECT_RELATION_PROJECT_ID)
    private String projectId;

    @Column(name = ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;


}
