import{m as x,c as i,d as C,r as j,Q as q,o as O,bo as k,g as N,h as A,F as L,q as I,i as M,br as R,D as S}from"./index-r0dFAfgr.js";import{_ as z}from"./CardTable-rdWOL4_6.js";import{_ as B}from"./CardSearch-CB_HNR-Q.js";import{_ as W}from"./index-BJ-QPYom.js";import{t as E,u as F,a as Q}from"./index-BggOjNGp.js";import{I as w}from"./common-CvK_P_ao.js";import{g as U,a as V}from"./ledger2-CVFjtR6o.js";import{d as X}from"./useHooks-BeUnknpc.js";import Y from"./TreeBox-mfOmxwZJ.js";import{f as $}from"./DateFormatter-Bm9a68Ax.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";import"./index-cIaXVz1R.js";const G=u=>x({url:"/api/maintenance/standard/all",method:"get",params:{name:u}}),H=u=>x({url:"/api/repair/standard/all",method:"get",params:{name:u}}),J=()=>{const u=i([]),D=async()=>{const t=(await U().catch(a=>{console.log(a.message)})).data.map(a=>({label:a,value:a}));return u.value=t,t},f=i([]),b=async s=>{const t=await V(s).catch(a=>{console.log(a.message)});return f.value=t.data.map(a=>({label:a.nodeName,value:a.nodeId,children:a.children.map(d=>({label:d.nodeName,value:d.nodeId,data:d.data}))})),f.value},g=(s,t)=>{const a=f.value.find(d=>d.value===s);return a&&a.children?a.children.find(d=>d.data.id===t):void 0},h=i([]),l=async s=>{const t=await E(s).catch(a=>{console.log(a.message)});t.data?h.value=t.data.map(a=>({label:a.label,value:a.value,data:a.unit})):console.log(t)},c=i([]),o=async()=>{const s=await H("").catch(t=>{console.log(t.message)});s.data?c.value=s.data.map(t=>({label:t.name,value:t.id,data:t})):console.log(s)},T=i([]);return{getDeviceTypeTree:D,DeviceTypes:u,getDeviceTree:b,getDeviceFromTree:g,DeviceTree:f,getAttrList:l,AttrList:h,RepaireStandardTree:c,getRepaireStandardTree:o,MaintenanceStandardTree:T,getMaintenanceStandardTress:async()=>{const s=await G("").catch(t=>{console.log(t.message)});s.data?T.value=s.data.map(t=>({label:t.name,value:t.id,data:t})):console.log(s)}}},pe=C({__name:"index",setup(u){const{getDeviceTypeTree:D}=J(),f=i(!1),b=i([]),g=i(),h=i(),l=i(),c=j({defaultParams:{group:""},filters:[{type:"select",field:"deviceType",labelWidth:80,label:"设备类型",options:[],onChange:e=>t(e)},{label:"采集器",labelWidth:60,field:"sensorId",type:"select",options:[]},{label:"设备",labelWidth:40,field:"group",type:"select",options:[{label:"全部",value:""}]},{type:"btn-group",label:"",btns:[{perm:!0,text:"查询",icon:w.QUERY,click:()=>{a(),g.value&&clearInterval(g.value),g.value=setInterval(()=>a(),30*1e3)}},{perm:!0,text:"导出",type:"warning",icon:w.EXPORT,click:()=>P(),disabled:()=>!o.selectList||o.selectList.length===0}]}]}),o=j({loading:!1,dataList:[],indexVisible:!0,columns:[{prop:"collectionTime",label:"最后更新时间",icon:"iconfont icon-shijian",formatter:e=>$(e.collectionTime),iconStyle:{color:"#69e850"}},{prop:"propertyName",label:"监测量"},{prop:"value",label:"监测值"}],pagination:{page:1,limit:20,total:0,handleSize:e=>{o.pagination.limit=e,a()},handlePage:e=>{o.pagination.page=e,a()}},handleSelectChange:e=>{o.selectList=e||[]}}),T=e=>{y.currentProject=e,Q(e.id).then(n=>{var m,v,p;const r=(m=c.filters)==null?void 0:m.find(_=>_.field==="sensorId");n.data&&n.data.length>0?(b.value=(v=n.data)==null?void 0:v.map(_=>({label:_.name,value:S(_.id.id),type:_.deviceTypeName})),r.options=b.value,l.value?l.value.queryParams&&(l.value.queryParams.sensorId=S(n.data[0].id.id)):c.defaultParams={group:"",sensorId:S(n.data[0].id.id)},a()):(r&&r.type==="select"&&(r.options=[]),o.dataList=[],(p=l.value)!=null&&p.queryParams&&(l.value.queryParams.sensorId=""))})},P=()=>{var e;(e=h.value)==null||e.exportTable()},s=async()=>{var n;const e=(n=c.filters)==null?void 0:n.find(r=>r.field==="deviceType");(e==null?void 0:e.type)==="select"&&(e.options=await D())},t=e=>{var r,m,v;const n=(r=c.filters)==null?void 0:r.find(p=>p.field==="sensorId");(n==null?void 0:n.type)==="select"&&(e?n.options=b.value.filter(p=>p.type===e):n.options=b.value||[],(m=l.value)!=null&&m.queryParams&&(l.value.queryParams.sensorId=((v=n==null?void 0:n.options)==null?void 0:v.length)&&n.options[0].value||"")),a()},a=async()=>{o.selectList=[],o.loading=!0;const e={page:o.pagination.page,size:o.pagination.limit,projectId:y.currentProject.id,sensorId:"",group:""};if(l.value?Object.assign(e,l.value.queryParams):Object.assign(e,c.defaultParams),!e.sensorId)return o.loading=!1;const n=await F(e.sensorId,{group:e.group,page:e.page,size:e.size});o.loading=!1,o.dataList=n.data.data,o.pagination.total=n.data.total},{TreeData:y,refreshProject:d}=X(async e=>{await T(e)});return q(()=>{g.value&&clearInterval(g.value)}),O(()=>{d(),s()}),(e,n)=>{const r=W,m=B,v=z,p=R;return k((N(),A(Y,null,{tree:L(()=>[I(r,{"tree-data":M(y)},null,8,["tree-data"])]),default:L(()=>[I(m,{ref_key:"cardSearch",ref:l,config:c},null,8,["config"]),I(v,{ref_key:"cardTable",ref:h,class:"card-table",config:o},null,8,["config"])]),_:1})),[[p,f.value]])}}});export{pe as default};
