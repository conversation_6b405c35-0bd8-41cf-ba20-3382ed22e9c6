package org.thingsboard.server.dao.smartPipe;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.Partition;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.PipeMinFlowConfig;
import org.thingsboard.server.dao.sql.smartPipe.PipeMinFlowConfigMapper;

import java.math.BigDecimal;
import java.util.*;

/**
 *
 */
@Service
public class PipeMinFlowConfigServiceImpl implements PipeMinFlowConfigService {

    @Autowired
    private PartitionService partitionService;

    @Autowired
    private PipeMinFlowConfigMapper pipeMinFlowConfigMapper;

    @Override
    public List<PipeMinFlowConfig> getListByPartition(String partitionId) {
        if (StringUtils.isBlank(partitionId)) {
            return new ArrayList<>();
        }
        // 获取所有下级DMA分区
        List<String> partitionIdList = new ArrayList<>();
        Partition partition = partitionService.getById(partitionId);
        partitionService.getDMAChildId(partition, partitionIdList);
        if (partitionIdList.size() == 0) {
            partitionIdList.add("-");
        }
        return pipeMinFlowConfigMapper.getListByPartitionIdIn(partitionIdList);
    }

    @Override
    public PipeMinFlowConfig save(PipeMinFlowConfig pipeMinFlowConfig) {
        pipeMinFlowConfig.setUpdateTime(new Date());

        // 夜间最小值 = 夜间最小流 * (采集频率（分钟） / 60(分钟))
        BigDecimal scale;
        if (pipeMinFlowConfig.getCollectRate() != null) {
            scale = BigDecimal.valueOf(pipeMinFlowConfig.getCollectRate()).divide(BigDecimal.valueOf(60L), 2, BigDecimal.ROUND_HALF_UP);
            if (pipeMinFlowConfig.getNightFlowMin() != null) {
                pipeMinFlowConfig.setNightValueMin(pipeMinFlowConfig.getNightFlowMin().multiply(scale));
            }
            if (pipeMinFlowConfig.getNightFlowMax() != null) {
                pipeMinFlowConfig.setNightValueMax(pipeMinFlowConfig.getNightFlowMax().multiply(scale));
            }
        }

        // 增量设置
        if ("2".equals(pipeMinFlowConfig.getIncrType()) && pipeMinFlowConfig.getIncrBase() != null) {
            if (pipeMinFlowConfig.getMainLineLength() == null) {
                pipeMinFlowConfig.setMainLineLength(BigDecimal.ZERO);
            }
            // 黄色预警值 = 基准值 + 0.9336 * 管线长度 + 0.1408
            pipeMinFlowConfig.setIncrWarn(pipeMinFlowConfig.getIncrBase().add(BigDecimal.valueOf(0.9336).multiply(pipeMinFlowConfig.getMainLineLength())).add(BigDecimal.valueOf(0.1408)));

            // 红色预警值 = 基准值 + 0.9336 * 管线长度 + 0.1408 + 0.5 * 管线长度
            pipeMinFlowConfig.setIncrError(pipeMinFlowConfig.getIncrWarn().add(BigDecimal.valueOf(0.5).multiply(pipeMinFlowConfig.getMainLineLength())));
        }

        if (StringUtils.isBlank(pipeMinFlowConfig.getId())) {
            pipeMinFlowConfig.setCreateTime(new Date());
            pipeMinFlowConfigMapper.insert(pipeMinFlowConfig);
        } else {
            pipeMinFlowConfigMapper.updateById(pipeMinFlowConfig);
        }
        return pipeMinFlowConfig;
    }

    @Override
    public Map<String, PipeMinFlowConfig> getByPartitionIdList(List<String> partitionIdList) {
        List<PipeMinFlowConfig> pipeMinFlowConfigList = pipeMinFlowConfigMapper.getListByPartitionIdIn(partitionIdList);
        Map result = new HashMap();
        for (PipeMinFlowConfig pipeMinFlowConfig : pipeMinFlowConfigList) {
            result.put(pipeMinFlowConfig.getPartitionId(), pipeMinFlowConfig);
        }

        return result;
    }
}
