package org.thingsboard.server.dao.sql.menu;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.repository.CrudRepository;
import org.thingsboard.server.dao.model.sql.MenuButtonRole;

import java.util.List;

public interface MenuButtonRoleRepository extends CrudRepository<MenuButtonRole, String>, JpaRepository<MenuButtonRole, String> {
    void deleteByRoleId(String roleId);

    List<MenuButtonRole> findByRoleId(String roleId);
}
