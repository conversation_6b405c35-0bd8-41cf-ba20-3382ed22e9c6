<template>
  <div class="wrapper">
    <CardSearch
      ref="refSearch"
      :config="SearchConfig"
    ></CardSearch>
    <CardTable
      class="card-table"
      :config="TableConfig"
    ></CardTable>
    <DialogForm
      ref="refDialogForm"
      :config="DialogFormConfig"
    ></DialogForm>
  </div>
</template>
<script lang="ts" setup>
import { onMounted, reactive, ref } from 'vue'
import { DeleteMenuSource, GetMenuSources, PostMenuSource } from '@/api/menu/source'
import { SLConfirm, SLMessage } from '@/utils/Message'
import { ICardSearchIns, IDialogFormIns } from '@/components/type'

const refSearch = ref<ICardSearchIns>()
const refDialogForm = ref<IDialogFormIns>()
const SearchConfig = reactive<ISearch>({
  labelWidth: '100px',
  filters: [
    { type: 'input', label: '名称', field: 'name', onChange: () => refreshData() },
    { type: 'input', label: '路径', field: 'url', onChange: () => refreshData() },
    {
      type: 'btn-group',
      btns: [
        { type: 'primary', perm: true, text: '查询', click: () => refreshData() },
        { perm: true, type: 'primary', text: '添加', click: () => handleAdd() },
        { perm: true, type: 'danger', text: '批量删除', click: () => handleDelete() }
      ]
    }
  ],
  defaultParams: {}
})
const TableConfig = reactive<ICardTable>({
  columns: [
    { label: '资源名称', prop: 'name' },
    { label: '资源路径', prop: 'url' },
    { label: '资源预览图', prop: 'img', image: true }
  ],
  dataList: [],
  operations: [
    {
      perm: true,
      type: 'primary',
      isTextBtn: true,
      text: '编辑',
      click: (row: any) => handleAdd(row)
    },
    {
      perm: true,
      type: 'danger',
      isTextBtn: true,
      text: '删除',
      click: (row: any) => handleDelete(row)
    }
  ],
  pagination: {
    total: 0,
    page: 1,
    align: 'right',
    limit: 20,
    handlePage: (page: number) => {
      TableConfig.pagination.page = page
      refreshData()
    },
    handleSize: (size: number) => {
      TableConfig.pagination.limit = size
      refreshData()
    }
  },
  handleSelectChange: (rows?: any[]) => {
    TableConfig.selectList = rows || []
  }
})
const DialogFormConfig = reactive<IDialogFormConfig>({
  title: '添加资源',
  group: [
    {
      fields: [
        {
          type: 'input',
          label: '资源名称',
          field: 'name',
          rules: [{ required: true, message: '请输入资源名称' }]
        },
        {
          type: 'input',
          label: '资源路径',
          field: 'url',
          rules: [{ required: true, message: '请输入资源路径' }]
        },
        { type: 'avatar', label: '资源预览图', field: 'img' }
      ]
    }
  ],
  labelPosition: 'top',
  defaultValue: {},
  dialogWidth: 450,
  draggable: true,
  submit: async (params: any) => {
    console.log(params)
    try {
      const res = await PostMenuSource(params)
      if (res.data) SLMessage.success('操作成功')
      refDialogForm.value?.closeDialog()
      refreshData()
    } catch (error) {
      SLMessage.error('系统错误')
    }
  }
})
const handleAdd = (row?: any) => {
  console.log(row)
  DialogFormConfig.title = row ? '编辑资源' : '添加资源'
  DialogFormConfig.defaultValue = { ...(row || {}) }
  refDialogForm.value?.openDialog()
}
const handleDelete = (row?: any) => {
  SLConfirm('确定删除？', '删除提示')
    .then(async () => {
      try {
        const ids: any[] = row
          ? row.id?.split(',')
          : TableConfig.selectList?.map(item => item.id) || []
        console.log(ids)

        if (!ids.length) SLMessage.warning('请选择要删除的数据')
        else {
          const res = await DeleteMenuSource(ids)
          if (res.data?.length) {
            SLMessage.success('删除成功')
            refreshData()
          } else SLMessage.error('删除失败')
        }
      } catch (error) {
        SLMessage.error('系统错误')
      }
    })
    .catch(() => {
      //
    })
}
const refreshData = async () => {
  const query = refSearch.value?.queryParams
  const res = await GetMenuSources({
    page: TableConfig.pagination.page,
    size: TableConfig.pagination.limit,
    ...(query || {})
  })
  TableConfig.dataList = res.data?.data || []
  TableConfig.pagination.total = res.data.total || 0
}
onMounted(() => {
  refreshData()
})
</script>
<style lang="scss" scoped>
.table-box {
  height: calc(100% - 100px);
}
</style>
