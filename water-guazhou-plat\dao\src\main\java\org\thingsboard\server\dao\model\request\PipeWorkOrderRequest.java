package org.thingsboard.server.dao.model.request;

import lombok.Data;

import java.time.LocalDate;

/**
 * 漏损工单
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2023-05-31
 */
@Data
public class PipeWorkOrderRequest {

    private int page;

    private int size;

    private String status;

    private String type;

    private String month;

    private String year;

    private String start;

    private String end;

    private String level;

    private String source;

    private LocalDate startTime;

    private LocalDate endTime;

    private String organizerName;

    private String partitionName;

    private String processUserName;

    private String receiveDepartmentName;

    private String tenantId;
}
