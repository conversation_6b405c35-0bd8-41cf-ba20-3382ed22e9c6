package org.thingsboard.server.dao.sql.shuiwu.assets;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.repository.CrudRepository;
import org.thingsboard.server.dao.model.sql.shuiwu.assets.AssetsInventoryEntity;

/**
 * 设备盘点
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2021-10-19
 */
public interface AssetsInventoryRepository extends CrudRepository<AssetsInventoryEntity, String> {
    Page<AssetsInventoryEntity> findAllByInventoryNoLikeAndInventoryPersonIdsLikeAndTenantIdLikeAndCreateTimeBetween(String inventoryNo, String reviewerId, String tenantId, Long start, Long end, Pageable pageable);
}
