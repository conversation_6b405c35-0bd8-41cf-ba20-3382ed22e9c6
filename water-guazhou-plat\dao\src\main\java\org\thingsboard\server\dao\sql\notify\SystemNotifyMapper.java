package org.thingsboard.server.dao.sql.notify;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.DTO.CountObjDTO;
import org.thingsboard.server.dao.model.request.SystemNotifyListRequest;
import org.thingsboard.server.dao.model.sql.notify.SystemNotify;

import java.util.List;

@Mapper
public interface SystemNotifyMapper extends BaseMapper<SystemNotify> {

    IPage<SystemNotify> findList(IPage<SystemNotify> pageRequest, @Param("param") SystemNotifyListRequest request);

    List<CountObjDTO> notifyCount(@Param("to") String to, @Param("status") String status, @Param("tenantId") String tenantId);

    void readAll(@Param("type") String type, @Param("to") String to, @Param("tenantId") String tenantId);
}
