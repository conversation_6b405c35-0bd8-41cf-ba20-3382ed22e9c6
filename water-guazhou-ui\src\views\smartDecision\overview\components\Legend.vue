<template>
  <div class="legend">
    <div v-for="(item, i) in state.legends" :key="i" class="legend-item">
      <img :src="item.img" alt="图例" />
      <span>{{ item.name }}</span>
    </div>
  </div>
</template>
<script lang="ts" setup>
const getMapLegendImage = (name: string | URL) => {
  const href = new URL(`../../imgs/legends/${name}`, import.meta.url)?.href;
  return href;
};
const state = reactive<{ legends: { img: string; name: string }[] }>({
  legends: [
    { img: getMapLegendImage('syd.png'), name: '水源地' },
    { img: getMapLegendImage('sc.png'), name: '水厂' },
    { img: getMapLegendImage('lljcd.png'), name: '流量监测点' },
    { img: getMapLegendImage('yljcd.png'), name: '压力监测点' },
    { img: getMapLegendImage('szjcd.png'), name: '水质监测点' },
    { img: getMapLegendImage('bf.png'), name: '泵房' },
    { img: getMapLegendImage('gsgx.png'), name: '供水管线' }
  ]
});
</script>
<style lang="scss" scoped>
.legend {
  background-color: rgba(77, 148, 255, 0.2);
  padding: 20px;
  border: 2px solid rgba(8, 74, 174, 0.8);
}
.legend-item {
  display: flex;
  align-items: center;
  img {
    width: 28px;
    height: 28px;
    object-fit: contain;
    margin-right: 4px;
  }
  span {
    font-size: 14px;
    color: #fff;
    margin-bottom: 6px;
  }
}
</style>
