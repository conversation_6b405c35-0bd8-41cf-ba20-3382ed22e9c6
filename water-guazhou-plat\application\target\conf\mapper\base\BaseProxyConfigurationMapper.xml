<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.base.BaseProxyConfigurationMapper">
    
    <resultMap type="org.thingsboard.server.dao.model.sql.base.BaseProxyConfiguration" id="BaseProxyConfigurationResult">
        <result property="id"    column="id"    />
        <result property="proxyConfig"    column="proxy_config"    />
        <result property="version"    column="version"    />
        <result property="status"    column="status"    />
        <result property="workerProcesses"    column="worker_processes"    />
        <result property="workerConnections"    column="worker_connections"    />
        <result property="keepAliveTimeout"    column="keep_alive_timeout"    />
        <result property="logStatus"    column="log_status"    />
        <result property="cacheStatus"    column="cache_status"    />
    </resultMap>

    <sql id="selectBaseProxyConfigurationVo">
        select id, proxy_config, version, status, worker_processes, worker_connections, keep_alive_timeout, log_status, cache_status from base_proxy_configuration
    </sql>

    <select id="selectBaseProxyConfigurationList" parameterType="org.thingsboard.server.dao.model.sql.base.BaseProxyConfiguration" resultMap="BaseProxyConfigurationResult">
        <include refid="selectBaseProxyConfigurationVo"/>
        <where>  
            <if test="proxyConfig != null  and proxyConfig != ''"> and proxy_config = #{proxyConfig}</if>
            <if test="version != null  and version != ''"> and version = #{version}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="workerProcesses != null  and workerProcesses != ''"> and worker_processes = #{workerProcesses}</if>
            <if test="workerConnections != null  and workerConnections != ''"> and worker_connections = #{workerConnections}</if>
            <if test="keepAliveTimeout != null  and keepAliveTimeout != ''"> and keep_alive_timeout = #{keepAliveTimeout}</if>
            <if test="logStatus != null  and logStatus != ''"> and log_status = #{logStatus}</if>
            <if test="cacheStatus != null  and cacheStatus != ''"> and cache_status = #{cacheStatus}</if>
        </where>
    </select>
    
    <select id="selectBaseProxyConfigurationById" parameterType="String" resultMap="BaseProxyConfigurationResult">
        <include refid="selectBaseProxyConfigurationVo"/>
        where id = #{id}
    </select>

    <insert id="insertBaseProxyConfiguration" parameterType="org.thingsboard.server.dao.model.sql.base.BaseProxyConfiguration">
        insert into base_proxy_configuration
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="proxyConfig != null">proxy_config,</if>
            <if test="version != null">version,</if>
            <if test="status != null">status,</if>
            <if test="workerProcesses != null">worker_processes,</if>
            <if test="workerConnections != null">worker_connections,</if>
            <if test="keepAliveTimeout != null">keep_alive_timeout,</if>
            <if test="logStatus != null">log_status,</if>
            <if test="cacheStatus != null">cache_status,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="proxyConfig != null">#{proxyConfig},</if>
            <if test="version != null">#{version},</if>
            <if test="status != null">#{status},</if>
            <if test="workerProcesses != null">#{workerProcesses},</if>
            <if test="workerConnections != null">#{workerConnections},</if>
            <if test="keepAliveTimeout != null">#{keepAliveTimeout},</if>
            <if test="logStatus != null">#{logStatus},</if>
            <if test="cacheStatus != null">#{cacheStatus},</if>
         </trim>
    </insert>

    <update id="updateBaseProxyConfiguration" parameterType="org.thingsboard.server.dao.model.sql.base.BaseProxyConfiguration">
        update base_proxy_configuration
        <trim prefix="SET" suffixOverrides=",">
            <if test="proxyConfig != null">proxy_config = #{proxyConfig},</if>
            <if test="version != null">version = #{version},</if>
            <if test="status != null">status = #{status},</if>
            <if test="workerProcesses != null">worker_processes = #{workerProcesses},</if>
            <if test="workerConnections != null">worker_connections = #{workerConnections},</if>
            <if test="keepAliveTimeout != null">keep_alive_timeout = #{keepAliveTimeout},</if>
            <if test="logStatus != null">log_status = #{logStatus},</if>
            <if test="cacheStatus != null">cache_status = #{cacheStatus},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBaseProxyConfigurationById" parameterType="String">
        delete from base_proxy_configuration where id = #{id}
    </delete>

    <delete id="deleteBaseProxyConfigurationByIds" parameterType="String">
        delete from base_proxy_configuration where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>