package org.thingsboard.server.dao.model.sql.smartOperation.construction;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartOperation.SoGeneralTaskStatus;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseUsername;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.math.BigDecimal;
import java.util.Date;


@Getter
@Setter
@ResponseEntity
public class SoConstructionEstimate implements SoConstructionRelatedEntity {
    // id
    private String id;

    // 所属工程编号
    private String constructionCode;

    // 所属工程名称
    @TableField(exist = false)
    private String constructionName;

    // 所属工程类型id
    @TableField(exist = false)
    private String constructionTypeId;

    // 所属工程类型名称
    @TableField(exist = false)
    private String constructionTypeName;

    // 预算人
    private String budgeter;

    // 预算金额，万元
    private BigDecimal cost;

    // 地址
    private String address;

    // 当前状态
    @TableField(exist = false)
    private SoGeneralTaskStatus status;

    // 备注
    private String remark;

    // 附件信息
    private String attachments;

    // 创建人
    @ParseUsername
    private String creator;

    // 创建时间
    private Date createTime;

    // 最后更新时间
    private Date updateTime;

    // 最后更新用户
    @ParseUsername
    private String updateUser;

    // 客户id
    private String tenantId;

}

