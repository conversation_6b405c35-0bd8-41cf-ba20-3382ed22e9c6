package org.thingsboard.server.dao.util.imodel.query.purchase;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.purchase.ContractDetail;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

@Getter
@Setter
public class ContractDetailSaveRequest extends SaveRequest<ContractDetail> {

    // 合同主表ID
    @NotNullOrEmpty(refTable = "contract", parentIgnore = true)
    private String mainId;

    // 设备编码
    @NotNullOrEmpty
    private String serialId;

    // 单位
    private String unit;

    // 数量
    @NotNullOrEmpty
    private String num;

    // 价格
    @NotNullOrEmpty
    private String price;

    // 税率
    @NotNullOrEmpty
    private String taxRate;

    // 备注
    private String remark;

    public ContractDetail build() {
        ContractDetail entity = new ContractDetail();
        entity.setTenantId(tenantId());
        entity.setMainId(mainId);
        commonSet(entity);
        return entity;
    }

    public ContractDetail update(String id) {
        ContractDetail entity = new ContractDetail();
        entity.setId(id);
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    private void commonSet(ContractDetail entity) {
        entity.setSerialId(serialId);
        entity.setUnit(unit);
        entity.setNum(num);
        entity.setPrice(price);
        entity.setTaxRate(taxRate);
        entity.setRemark(remark);
    }
}