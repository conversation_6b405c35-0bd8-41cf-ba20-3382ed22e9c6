package org.thingsboard.server.dao.smartService.knowledge;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.smartService.knowledge.KnowledgeDocument;
import org.thingsboard.server.dao.sql.smartService.knowledge.KnowledgeDocumentMapper;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-10-27
 */
@Slf4j
@Service
@Transactional
public class KnowledgeDocumentServiceImpl implements KnowledgeDocumentService {

    @Autowired
    private KnowledgeDocumentMapper knowledgeDocumentMapper;

    @Autowired
    private KnowledgeBaseTypeService knowledgeBaseTypeService;

    @Override
    public PageData getList(String typeId, String name, int page, int size, String tenantId) {
        List<String> typeIds = new ArrayList<>();
        knowledgeBaseTypeService.getAllIdByPid(typeId, typeIds);
        IPage<KnowledgeDocument> iPage = new Page<>(page, size);
        Page<KnowledgeDocument> baseList = knowledgeDocumentMapper.getList(iPage, name, typeIds, tenantId);

        return new PageData(baseList.getTotal(), baseList.getRecords());

    }

    @Override
    public KnowledgeDocument save(KnowledgeDocument knowledgeDocument) {
        if (StringUtils.isBlank(knowledgeDocument.getId())) {
            knowledgeDocument.setCreateTime(new Date());
            knowledgeDocumentMapper.insert(knowledgeDocument);
        } else {
            knowledgeDocumentMapper.updateById(knowledgeDocument);
        }

        return knowledgeDocument;
    }

    @Override
    public int delete(List<String> ids) {
        return knowledgeDocumentMapper.deleteBatchIds(ids);
    }
}
