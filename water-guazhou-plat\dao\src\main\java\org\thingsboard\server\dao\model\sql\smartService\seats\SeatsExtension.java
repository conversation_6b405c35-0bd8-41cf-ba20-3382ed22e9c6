package org.thingsboard.server.dao.model.sql.smartService.seats;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * kpi指标配置
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2021-11-15
 */
@TableName("tb_service_seats_extension")
@Data
public class SeatsExtension {

    @TableId
    private String id;

    private String extension;

    private String ip;

    private String pcIp;

    private String departmentId;

    private transient String departmentName;

    private Date createTime;

    private Date updateTime;

    private String tenantId;

}
