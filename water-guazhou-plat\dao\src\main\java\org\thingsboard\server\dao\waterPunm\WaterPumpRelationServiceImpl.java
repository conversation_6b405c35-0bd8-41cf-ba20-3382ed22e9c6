package org.thingsboard.server.dao.waterPunm;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.model.sql.WaterPumpRelationEntity;
import org.thingsboard.server.dao.sql.waterPump.WaterPumpRelationRepository;

import java.util.List;

@Slf4j
@Service
public class WaterPumpRelationServiceImpl implements WaterPumpRelationService {

    @Autowired
    private WaterPumpRelationRepository waterPumpRelationRepository;

    @Override
    public List<WaterPumpRelationEntity> findByWaterPumpId(String waterPumpId) {
        return waterPumpRelationRepository.findByWaterPumpId(waterPumpId);
    }

    @Override
    public List<WaterPumpRelationEntity> findByTenantId(TenantId tenantId) {
        return waterPumpRelationRepository.findByTenantId(UUIDConverter.fromTimeUUID(tenantId.getId()));
    }

    @Override
    public List<WaterPumpRelationEntity> findByType(String type, TenantId tenantId) {
        return waterPumpRelationRepository.findByTypeAndTenantId(type, UUIDConverter.fromTimeUUID(tenantId.getId()));
    }
}
