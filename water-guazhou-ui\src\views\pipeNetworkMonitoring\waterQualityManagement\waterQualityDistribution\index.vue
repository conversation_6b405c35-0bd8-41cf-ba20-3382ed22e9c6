<template>
  <RightDrawerMap
    ref="refMap" :title="'水质分布'" :windows="state.windows" :hide-detail-close="true"
    :hide-layer-list="true" :right-drawer-width="540" @map-loaded="onMapLoaded"
  >
    <div class="content">
      <Form ref="refForm" :config="FormConfig">
      </Form>
      <FieldSet type="underline" title="水质采样查询"></FieldSet>
      <div class="search-box">
        <el-form :inline="true" class="demo-form-inline">
          <el-form-item label="采样月份">
            <el-date-picker
              v-model="state.searchForm.month"
              type="month"
              placeholder="选择月份"
              format="YYYY-MM"
              value-format="YYYY-MM"
              @change="handleMonthChange"
            />
          </el-form-item>
          <el-form-item label="水质指标">
            <el-select v-model="state.searchForm.indicator" placeholder="请选择水质指标" @change="handleIndicatorChange">
              <el-option
                v-for="item in state.indicatorOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="searchData">查询</el-button>
          </el-form-item>
        </el-form>
      </div>
      <FieldSet type="underline" title="水质采样结果"></FieldSet>
      <div class="right-box">
        <FormTable ref="refTable" class="table-box" :config="tableConfig"></FormTable>
      </div>
      <FieldSet type="underline" :title="(state.curRow?.name || '') + '水质指标趋势分析'"></FieldSet>
      <div ref="echartsDiv" class="right-box bottom">
        <Tabs v-model="state.activeName" :config="TabsConfig"></Tabs>
        <VChart ref="refChart" :theme="useAppStore().isDark ? 'dark' : 'light'" :option="state.lineOption" />
      </div>
    </div>
    <template #detail-default>
      <div v-if="state.reportDetail.id" class="report-detail">
        <h3>化验报告详情</h3>
        <div class="report-info">
          <p><span class="label">采样点:</span> {{ state.reportDetail.name }}</p>
          <p><span class="label">采样时间:</span> {{ state.reportDetail.sampleTime }}</p>
          <p><span class="label">化验时间:</span> {{ state.reportDetail.testTime }}</p>
          <p><span class="label">化验单位:</span> {{ state.reportDetail.testUnit }}</p>
        </div>
        <div class="report-indicators">
          <h4>检测指标</h4>
          <el-table :data="state.reportDetail.indicators" border style="width: 100%">
            <el-table-column prop="name" label="指标名称" />
            <el-table-column prop="value" label="检测值" />
            <el-table-column prop="unit" label="单位" />
            <el-table-column prop="standard" label="标准值" />
            <el-table-column prop="status" label="状态">
              <template #default="scope">
                <el-tag :type="scope.row.status === '合格' ? 'success' : 'danger'">{{ scope.row.status }}</el-tag>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="report-actions">
          <el-button type="primary" @click="downloadReport">下载报告</el-button>
          <el-button @click="closeReportDetail">关闭</el-button>
        </div>
      </div>
    </template>
  </RightDrawerMap>
</template>
<script setup>
import { reactive, ref, nextTick } from 'vue'
import Point from '@arcgis/core/geometry/Point.js'
import Graphic from '@arcgis/core/Graphic.js'
import PictureMarkerSymbol from '@arcgis/core/symbols/PictureMarkerSymbol.js'
import elementResizeDetectorMaker from 'element-resize-detector'
import TextSymbol from '@arcgis/core/symbols/TextSymbol.js'
import { useAppStore, useBusinessStore } from '@/store'
import RightDrawerMap from '@/views/arcMap/components/common/RightDrawerMap.vue'
import { bindViewClick, gotoAndHighLight } from '@/utils/MapHelper'
import { ring } from '@/views/arcMap/components/components/chart'
import { getStationImageUrl } from '@/utils/URLHelper'
import { lineOption } from '../waterQualityMonitoring/echartsData/echart'

// 模拟API调用，实际开发中需替换为真实API
const getWaterQualitySamplingList = () => {
  return Promise.resolve({
    data: [
      {
        id: 'sample1',
        name: '东区采样点1',
        location: '116.405285,39.904989',
        sampleTime: '2023-05-10',
        testTime: '2023-05-12',
        testUnit: '水务局水质检测中心',
        reportId: 'report001',
        indicators: [
          { name: '浑浊度', property: 'turbidity', value: '0.8', unit: 'NTU', standard: '≤1', status: '合格' },
          { name: 'pH值', property: 'ph', value: '7.2', unit: '', standard: '6.5-8.5', status: '合格' },
          { name: '余氯', property: 'chlorine', value: '0.3', unit: 'mg/L', standard: '0.3-0.5', status: '合格' },
          { name: '总硬度', property: 'hardness', value: '280', unit: 'mg/L', standard: '≤450', status: '合格' }
        ]
      },
      {
        id: 'sample2',
        name: '西区采样点1',
        location: '116.315285,39.894989',
        sampleTime: '2023-05-15',
        testTime: '2023-05-17',
        testUnit: '水务局水质检测中心',
        reportId: 'report002',
        indicators: [
          { name: '浑浊度', property: 'turbidity', value: '1.2', unit: 'NTU', standard: '≤1', status: '超标' },
          { name: 'pH值', property: 'ph', value: '7.5', unit: '', standard: '6.5-8.5', status: '合格' },
          { name: '余氯', property: 'chlorine', value: '0.4', unit: 'mg/L', standard: '0.3-0.5', status: '合格' },
          { name: '总硬度', property: 'hardness', value: '320', unit: 'mg/L', standard: '≤450', status: '合格' }
        ]
      },
      {
        id: 'sample3',
        name: '南区采样点1',
        location: '116.405285,39.854989',
        sampleTime: '2023-05-20',
        testTime: '2023-05-22',
        testUnit: '水务局水质检测中心',
        reportId: 'report003',
        indicators: [
          { name: '浑浊度', property: 'turbidity', value: '0.7', unit: 'NTU', standard: '≤1', status: '合格' },
          { name: 'pH值', property: 'ph', value: '7.8', unit: '', standard: '6.5-8.5', status: '合格' },
          { name: '余氯', property: 'chlorine', value: '0.28', unit: 'mg/L', standard: '0.3-0.5', status: '不足' },
          { name: '总硬度', property: 'hardness', value: '310', unit: 'mg/L', standard: '≤450', status: '合格' }
        ]
      }
    ]
  })
}

// 模拟获取历史趋势数据
const getHistoryTrendData = (sampleId, indicator) => {
  const generateData = (base) => {
    return Array.from({ length: 6 }, (_, i) => {
      const month = new Date().getMonth() - 5 + i
      const date = new Date()
      date.setMonth(month)
      return {
        time: date.toISOString().substring(0, 7),
        value: (base + Math.random() * 0.5 - 0.25).toFixed(2)
      }
    })
  }

  const data = {
    turbidity: generateData(0.8),
    ph: generateData(7.2),
    chlorine: generateData(0.35),
    hardness: generateData(300)
  }

  return Promise.resolve({
    data: data[indicator] || []
  })
}

const erd = elementResizeDetectorMaker()
const refTable = ref()
const refMap = ref()
const refChart = ref()
const echartsDiv = ref()

const state = reactive({
  lineOption: null,
  curRow: {},
  activeName: null,
  windows: [],
  tableData: [],
  samplingData: [],
  searchForm: {
    month: new Date().toISOString().substring(0, 7),
    indicator: 'turbidity'
  },
  indicatorOptions: [
    { value: 'turbidity', label: '浑浊度' },
    { value: 'ph', label: 'pH值' },
    { value: 'chlorine', label: '余氯' },
    { value: 'hardness', label: '总硬度' }
  ],
  reportDetail: {}
})

const staticState = {
  view: null
}

const FormConfig = reactive({
  group: [
    {
      id: 'chart',
      fieldset: {
        desc: '水质采样统计',
        type: 'underline',
        style: {
          marginTop: 0
        }
      },
      fields: [
        {
          type: 'vchart',
          option: ring(),
          style: {
            height: '150px'
          }
        }
      ]
    }
  ],
  labelPosition: 'top',
  gutter: 12,
  defaultValue: {
    type: 'all'
  }
})

const tableConfig = reactive({
  loading: false,
  dataList: [],
  columns: [
    { prop: 'name', label: '采样点名称', minWidth: 120 },
    { prop: 'sampleTime', label: '采样时间', minWidth: 100 },
    { prop: 'testTime', label: '化验时间', minWidth: 100 },
    { prop: 'testUnit', label: '化验单位', minWidth: 160 },
    { 
      prop: 'report', 
      label: '化验报告', 
      minWidth: 100,
      slot: {
        default: ({ row }) => {
          return h('el-button', {
            type: 'primary',
            size: 'small',
            onClick: () => viewReport(row)
          }, '查看')
        }
      }
    }
  ],
  highlightCurrentRow: true,
  currentRowKey: 'id',
  handleRowClick: async (row) => {
    const g = staticState.view?.graphics.find(
      item => item.attributes?.row?.id === row.id
    )
    g && (await gotoAndHighLight(staticState.view, g, {
      zoom: 15,
      avoidHighlight: true
    }))

    state.curRow = row
    handleMarkClick(row.id)
    loadTrendData(row)
  },
  pagination: { hide: true }
})

const TabsConfig = reactive({
  type: 'tabs',
  tabType: 'simple',
  tabs: [
    { label: '近6个月趋势', value: 'trend' }
  ],
  handleTabClick: (tab) => {
    // 切换tab时的操作
  }
})

// 根据月份和指标筛选数据
const searchData = async () => {
  tableConfig.loading = true
  const res = await getWaterQualitySamplingList()
  state.samplingData = res.data
  
  // 根据月份筛选数据，实际应用中应该在API中进行筛选
  const filteredData = res.data.filter(item => 
    item.sampleTime.substring(0, 7) === state.searchForm.month
  )
  
  tableConfig.dataList = filteredData
  tableConfig.loading = false
  
  // 更新地图标记
  addMarks(filteredData)
  
  // 更新统计图表
  updateStatisticsPieChart(filteredData)
}

// 查看报告详情
const viewReport = (row) => {
  state.reportDetail = row
  refMap.value?.toggleCustomDetail(true)
}

// 关闭报告详情
const closeReportDetail = () => {
  refMap.value?.toggleCustomDetail(false)
  state.reportDetail = {}
}

// 下载报告
const downloadReport = () => {
  // 实际应用中需调用下载API或生成PDF
  alert('下载报告: ' + state.reportDetail.reportId)
}

// 加载趋势数据
const loadTrendData = async (row) => {
  if (!row) return
  
  const res = await getHistoryTrendData(row.id, state.searchForm.indicator)
  const trendData = res.data
  
  const times = trendData.map(item => item.time)
  const values = trendData.map(item => item.value)
  
  const indicator = state.indicatorOptions.find(item => item.value === state.searchForm.indicator)
  const selectedIndicator = row.indicators.find(item => item.property === state.searchForm.indicator)
  
  const options = lineOption(200, times, 40, 40)
  options.yAxis[0].name = indicator?.label + (selectedIndicator?.unit ? '(' + selectedIndicator.unit + ')' : '')
  options.series = [
    {
      name: row.name,
      smooth: true,
      data: values,
      type: 'line',
      markPoint: {
        data: [
          { type: 'max', name: '最大值' },
          { type: 'min', name: '最小值' }
        ]
      },
      markLine: {
        data: [{ type: 'average', name: '平均值' }]
      }
    }
  ]
  
  state.lineOption = options
  
  await nextTick(() => {
    if (echartsDiv.value) {
      erd.listenTo(echartsDiv.value, () => {
        state.lineOption = options
        refChart.value?.resize()
      })
    }
  })
}

// 更新统计饼图
const updateStatisticsPieChart = (data) => {
  const total = data.length
  const qualifiedCount = data.reduce((acc, item) => {
    const allQualified = item.indicators.every(ind => ind.status === '合格')
    return acc + (allQualified ? 1 : 0)
  }, 0)
  
  const statusData = [
    {
      name: '合格',
      nameAlias: '合格',
      value: qualifiedCount,
      scale: total ? (qualifiedCount / total * 100).toFixed(1) + '%' : '0%'
    },
    {
      name: '不合格',
      nameAlias: '不合格',
      value: total - qualifiedCount,
      scale: total ? ((total - qualifiedCount) / total * 100).toFixed(1) + '%' : '0%'
    }
  ]
  
  const field = FormConfig.group[0].fields[0]
  field && (field.option = ring(statusData, '个'))
}

// 处理月份变化
const handleMonthChange = () => {
  searchData()
}

// 处理指标变化
const handleIndicatorChange = () => {
  if (state.curRow?.id) {
    loadTrendData(state.curRow)
  }
}

// 添加地图标记
const addMarks = (data) => {
  staticState.view?.graphics?.removeAll()
  
  data.forEach(item => {
    const location = item.location.split(',')
    const point = new Point({
      longitude: location[0],
      latitude: location[1],
      spatialReference: staticState.view?.spatialReference
    })
    
    // 根据水质情况确定标记颜色
    const allQualified = item.indicators.every(ind => ind.status === '合格')
    const url = allQualified 
      ? getStationImageUrl('水质监测站.png') 
      : getStationImageUrl('水质监测站-警告.png')
    
    const markG = new Graphic({
      geometry: point,
      symbol: new PictureMarkerSymbol({
        width: 25,
        height: 30,
        yoffset: 15,
        url
      }),
      attributes: {
        row: item
      }
    })
    
    const markText = new Graphic({
      geometry: point,
      symbol: new TextSymbol({
        yoffset: -15,
        color: allQualified ? '#00ff33' : '#ff3300',
        text: item.name
      })
    })
    
    staticState.view?.graphics?.addMany([markG, markText])
  })
  
  if (data.length > 0) {
    handleMarkClick(data[0].id)
  }
}

// 地图标记点击事件
const handleMarkClick = async (sampleId) => {
  const sample = state.samplingData.find(item => item.id === sampleId)
  if (!sample) return
  
  const tableRow = tableConfig.dataList.find(item => item.id === sampleId)
  tableConfig.currentRow = tableRow
  state.curRow = sample
  
  // 加载趋势数据
  loadTrendData(sample)
  
  // 设置地图弹窗
  const graphic = staticState.view?.graphics.find(
    item => item.attributes?.row?.id === sampleId
  )
  
  if (!graphic) return
  
  const point = graphic?.geometry
  state.windows.length = 0
  state.windows.push({
    visible: false,
    x: point.x,
    y: point.y,
    offsetY: -30,
    title: sample.name,
    attributes: {
      values: sample.indicators.map(item => ({
        label: item.name,
        value: item.value + ' ' + item.unit,
        status: item.status
      })),
      id: sample.id
    }
  })
  
  await nextTick()
  refMap.value?.openPop(sample.id)
}

// 地图加载完成
const onMapLoaded = async (view) => {
  staticState.view = view
  refMap.value?.toggleCustomDetail(false)
  await searchData()
  bindViewClick(staticState.view, res => {
    const result = res.results?.[0]
    if (!result) return
    if (result.type === 'graphic') {
      const row = result.graphic?.attributes?.row
      handleMarkClick(row?.id)
    }
  })
}
</script>
<style lang="scss" scoped>
.content {
  padding: 10px;
}

.search-box {
  margin-bottom: 10px;
}

.right-box {
  width: 100%;
  height: 250px;

  .table-box {
    height: 90%
  }
}

.bottom {
  height: calc(33vh - 20px);
}

.report-detail {
  padding: 15px;
  
  .report-info {
    margin: 15px 0;
    
    .label {
      font-weight: bold;
      display: inline-block;
      width: 80px;
    }
  }
  
  .report-indicators {
    margin: 20px 0;
  }
  
  .report-actions {
    margin-top: 20px;
    text-align: center;
  }
}
</style> 