package org.thingsboard.server.dao.util.imodel.query.smartOperation.construction;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartOperation.SoGeneralTaskStatus;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionAccept;
import org.thingsboard.server.dao.util.imodel.query.IStarHttpRequest;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

import java.util.Date;

@Getter
@Setter
public class SoConstructionAcceptSaveRequest extends SaveRequest<SoConstructionAccept> {
    // 所属工程编号
    private String constructionCode;

    // 开始时间
    private Date beginTime;

    // 完成时间
    private Date endTime;

    // 申请单位
    @NotNullOrEmpty
    private String applicantOrganization;

    // 申请人
    @NotNullOrEmpty
    private String applicant;

    // 申请人电话
    private String applicantPhone;

    // 施工单位
    private String constructOrganization;

    // 监理单位
    private String supervisorOrganization;

    // 审计单位
    private String auditOrganization;

    // 设计单位
    private String designOrganization;

    // 验收说明
    private String remark;

    // 附件信息
    private String attachments;

    @Override
    public String valid(IStarHttpRequest request) {
        if (beginTime != null && endTime != null && beginTime.after(endTime)) {
            return "开始时间不可以晚于结束时间";
        }

        return super.valid(request);
    }

    @Override
    protected SoConstructionAccept build() {
        SoConstructionAccept entity = new SoConstructionAccept();
        entity.setStatus(SoGeneralTaskStatus.PROCESSING);
        entity.setConstructionCode(constructionCode);
        entity.setCreator(currentUserUUID());
        entity.setCreateTime(createTime());
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    @Override
    protected SoConstructionAccept update(String id) {
        SoConstructionAccept entity = new SoConstructionAccept();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(SoConstructionAccept entity) {
        entity.setBeginTime(beginTime);
        entity.setEndTime(endTime);
        entity.setApplicantOrganization(applicantOrganization);
        entity.setApplicant(applicant);
        entity.setApplicantPhone(applicantPhone);
        entity.setConstructOrganization(constructOrganization);
        entity.setSupervisorOrganization(supervisorOrganization);
        entity.setAuditOrganization(auditOrganization);
        entity.setDesignOrganization(designOrganization);
        entity.setRemark(remark);
        entity.setAttachments(attachments);
        entity.setUpdateUser(currentUserUUID());
        entity.setUpdateTime(createTime());
    }
}