package org.thingsboard.server.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.sql.store.GoodsShelf;
import org.thingsboard.server.dao.model.sql.store.Store;
import org.thingsboard.server.dao.util.imodel.query.store.GoodsShelfSaveRequest;
import org.thingsboard.server.dao.util.imodel.query.store.StorePageRequest;
import org.thingsboard.server.dao.util.imodel.response.tree.PagedTreeResponse;
import org.thingsboard.server.dao.util.reflection.ExceptionUtils;
import org.thingsboard.server.dao.store.GoodsShelfService;
import org.thingsboard.server.utils.imodel.annotations.IStarController;

@IStarController
@RequestMapping("/api/goodsShelf")
public class GoodsShelfController extends BaseController {
    @Autowired
    private GoodsShelfService service;

    @GetMapping
    public PagedTreeResponse<Store> findTreeConditional(StorePageRequest request) {
        // 第一层是 Store 其余层是 GoodsShelf
        return service.findTreeConditional(request);
    }

    @PostMapping
    public GoodsShelf save(@RequestBody GoodsShelfSaveRequest req) throws ThingsboardException {
        if (service.isCodeExists(req.getCode(), req.getId(), req.tenantId())) {
            ExceptionUtils.silentThrow("编码重复");
        }
        return service.save(req);
    }

    @PatchMapping("/{id}")
    public boolean edit(@RequestBody GoodsShelfSaveRequest req, @PathVariable String id) {
        return service.update(req.unwrap(id));
    }

    @DeleteMapping("/{id}")
    public boolean delete(@PathVariable String id) {
        if (!service.canBeDelete(id)) {
            ExceptionUtils.silentThrow("当前货架或其下级货架有关联数据，无法删除");
        }
        return service.delete(id);
    }
}