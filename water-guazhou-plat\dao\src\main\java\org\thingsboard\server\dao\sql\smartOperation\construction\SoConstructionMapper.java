package org.thingsboard.server.dao.sql.smartOperation.construction;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.smartOperation.ConstructionWorkflow;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstruction;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionCompositeProject;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionPageRequest;

import java.util.List;

@Mapper
public interface SoConstructionMapper extends BaseMapper<SoConstruction> {
    IPage<SoConstruction> findByPage(SoConstructionPageRequest request);

    String generateCode(String tenantId);

    boolean update(SoConstruction entity);

    boolean updateFully(SoConstruction entity);

    boolean isCodeExists(@Param("code") String code,
                         @Param("tenantId") String tenantId,
                         @Param("id") String id);

    List<ConstructionWorkflow> completionInfo(@Param("constructionCode") String constructionCode,
                                              @Param("tenantId") String tenantId);

    List<String> getAllCodeByProject(@Param("projectCode") String projectCode, @Param("tenantId") String tenantId);

    String getCodeById(String id);

    SoConstructionCompositeProject getCompositeByConstructionCode(@Param("constructionCode") String constructionCode, @Param("tenantId") String tenantId);

    boolean canBeDelete(String id);

}
