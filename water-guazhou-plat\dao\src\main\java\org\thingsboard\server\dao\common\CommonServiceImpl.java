package org.thingsboard.server.dao.common;

import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.circuit.CircuitTaskService;
import org.thingsboard.server.dao.maintain.SMMaintainTaskService;
import org.thingsboard.server.dao.model.sql.CommonTodoStatistic;
import org.thingsboard.server.dao.model.sql.SoStatistic;
import org.thingsboard.server.dao.model.sql.smartProduction.circuit.GeneralTaskStatus;
import org.thingsboard.server.dao.model.sql.statistic.GeneralTaskStatusStatistic;
import org.thingsboard.server.dao.orderWork.NewlyWorkOrderService;
import org.thingsboard.server.dao.plan.SMCircuitTaskService;
import org.thingsboard.server.dao.sql.department.DepartmentMapper;
import org.thingsboard.server.dao.sql.deviceType.DeviceTypeMapper;
import org.thingsboard.server.dao.sql.maintainCircuit.circuit.CircuitTaskMMapper;
import org.thingsboard.server.dao.sql.maintainCircuit.maintain.MaintainTaskMMapper;
import org.thingsboard.server.dao.sql.workOrder.NewlyWorkOrderMapper;

import java.util.Arrays;
import java.util.List;

import static org.thingsboard.server.dao.model.sql.smartProduction.circuit.GeneralTaskStatus.PENDING;
import static org.thingsboard.server.dao.model.sql.smartProduction.circuit.GeneralTaskStatus.RECEIVED;
import static org.thingsboard.server.dao.model.sql.workOrder.WorkOrderStage.ACTIVE1;

@Service
public class CommonServiceImpl implements CommonService {
    @Autowired
    private CircuitTaskService circuitTaskService; // 二供巡检

    @Autowired
    private SMCircuitTaskService smCircuitTaskService; // 巡检任务

    @Autowired
    private SMMaintainTaskService smMaintainTaskService; // 养护任务

    @Autowired
    private NewlyWorkOrderService newlyWorkOrderService; // 工单

    @Autowired
    private DeviceTypeMapper deviceTypeMapper; // 设备类型

    @Autowired
    private DepartmentMapper departmentMapper; // 部门

    @Autowired
    private CircuitTaskMMapper circuitTaskMMapper; // 巡检

    @Autowired
    private MaintainTaskMMapper maintainTaskMMapper; // 巡检

    @Autowired
    private NewlyWorkOrderMapper newlyWorkOrderMapper; // 工单





    @Override
    public CommonTodoStatistic getCommonTodoStatistic(String userId) {
        int circuitTaskStatistic = circuitTaskMMapper.coutDaijieshou(userId);
        int maintainTaskCount = maintainTaskMMapper.coutDaijieshou(userId);


        List<GeneralTaskStatus> statusList = Arrays.asList(PENDING, RECEIVED);
        GeneralTaskStatusStatistic smCircuitTaskStatistic = smCircuitTaskService.countStatusByUser(userId, statusList);
        GeneralTaskStatusStatistic smMaintainTaskStatistic = smMaintainTaskService.countStatusByUser(userId, statusList);
        GeneralTaskStatusStatistic newlyWorkOrderStatistic = newlyWorkOrderService.countStatusByUser(userId, ACTIVE1.getStatusBetween());

        return new CommonTodoStatistic(
                circuitTaskStatistic,
                maintainTaskCount,
                smCircuitTaskStatistic.getCount(),
                smMaintainTaskStatistic.getCount(),
                newlyWorkOrderStatistic.getCount()
        );
    }

    @Override
    public SoStatistic soStatistic(String tenantId) {
        String deviceTypeStatisticJson = deviceTypeMapper.countDeviceByType(tenantId);
        String departmentTypeStatisticJson = departmentMapper.countUserByDepartment(tenantId);
        String deviceCircuitCompletionStatisticJson = circuitTaskMMapper.completeStatistic(tenantId);
        String onTimeWorkOrderCompleteStatisticJson = newlyWorkOrderMapper.onTimeCompleteStatistic(tenantId);
        String WorkOrderTypeStatisticJson = newlyWorkOrderMapper.byTimeStatistic(tenantId);

        return new SoStatistic(
                JSONObject.parseObject(deviceTypeStatisticJson),
                JSONObject.parseObject(departmentTypeStatisticJson),
                JSONObject.parseObject(deviceCircuitCompletionStatisticJson),
                JSONObject.parseObject(onTimeWorkOrderCompleteStatisticJson),
                JSONObject.parseObject(WorkOrderTypeStatisticJson)
        );
    }


}
