<!--

    Copyright © 2016-2019 The Thingsboard Authors

    Licensed under the Apache License, Version 2.0 (the "License");
    you may not use this file except in compliance with the License.
    You may obtain a copy of the License at

        http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.

-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.thingsboard</groupId>
        <version>2.3.0</version>
        <artifactId>msa</artifactId>
    </parent>
    <groupId>org.thingsboard.msa</groupId>
    <artifactId>black-box-tests</artifactId>

    <name>ThingsBoard Black Box Tests</name>
    <url>https://thingsboard.io</url>
    <description>Project for ThingsBoard black box testing with using Docker</description>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <main.dir>${basedir}/../..</main.dir>
        <blackBoxTests.skip>true</blackBoxTests.skip>
        <testcontainers.version>1.9.1</testcontainers.version>
        <zeroturnaround.version>1.10</zeroturnaround.version>
        <java-websocket.version>1.3.9</java-websocket.version>
        <httpclient.version>4.5.6</httpclient.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.testcontainers</groupId>
            <artifactId>testcontainers</artifactId>
            <version>${testcontainers.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zeroturnaround</groupId>
            <artifactId>zt-exec</artifactId>
            <version>${zeroturnaround.version}</version>
        </dependency>
        <dependency>
            <groupId>org.java-websocket</groupId>
            <artifactId>Java-WebSocket</artifactId>
            <version>${java-websocket.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>${httpclient.version}</version>
        </dependency>
        <dependency>
            <groupId>io.takari.junit</groupId>
            <artifactId>takari-cpsuite</artifactId>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>
        <dependency>
            <groupId>org.thingsboard</groupId>
            <artifactId>netty-mqtt</artifactId>
        </dependency>
        <dependency>
            <groupId>org.thingsboard</groupId>
            <artifactId>tools</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <includes>
                        <include>**/*TestSuite.java</include>
                    </includes>
                    <skipTests>${blackBoxTests.skip}</skipTests>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
