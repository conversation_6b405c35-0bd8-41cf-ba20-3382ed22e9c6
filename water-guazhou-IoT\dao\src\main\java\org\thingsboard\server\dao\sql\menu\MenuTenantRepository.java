/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.sql.menu;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.dao.model.sql.MenuTenantEntity;

import java.util.List;

public interface MenuTenantRepository extends CrudRepository<MenuTenantEntity, String> {

    @Transactional
    @Modifying
    @Query("DELETE FROM MenuTenantEntity mt " +
            "WHERE mt.tenantId = :tenantId AND (mt.isExtensionMenu = :isExtensionMenu OR mt.isExtensionMenu IS NULL)")
    void deleteMenuTenantEntityByTenantIdAndIsExtensionMenu(@Param("tenantId") String tenantId,
                                                               @Param("isExtensionMenu") String isExtensionMenu);

    List<MenuTenantEntity> findByTenantIdAndIsExtensionMenu(String tenantId, String isExtensionMenu);

    @Query("SELECT mt.menuPoolId FROM MenuTenantEntity mt, MenuPoolEntity mp " +
            "WHERE mp.id = mt.menuPoolId AND mt.tenantId = :tenantId " +
            "AND mp.id NOT IN (" +
            "SELECT DISTINCT mp1.parentId FROM MenuPoolEntity mp1)")
    List<String> findMenuPoolIdByTenantId(@Param("tenantId") String tenantId);

    List<MenuTenantEntity> findByTenantId(String tenantId);

    @Query("SELECT mt.id FROM MenuTenantEntity mt, MenuPoolEntity mp " +
            "WHERE mp.id = mt.menuPoolId AND mp.type = :type AND mt.tenantId = :tenantId")
    List<String> getIdByType(@Param("type") Integer type, @Param("tenantId") String tenantId);

    @Query("SELECT mp.additionalInfo FROM MenuTenantEntity mt, MenuPoolEntity mp " +
            "WHERE mp.id = mt.menuPoolId AND mp.type = :type")
    List<String> getAdditionalInfoByType(Integer type);

    MenuTenantEntity findByTenantIdAndMenuPoolId(String tenantId, String menuPoolId);
}
