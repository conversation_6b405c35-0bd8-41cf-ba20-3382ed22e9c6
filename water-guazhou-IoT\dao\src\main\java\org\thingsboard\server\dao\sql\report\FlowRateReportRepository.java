package org.thingsboard.server.dao.sql.report;

import org.springframework.data.jpa.repository.JpaRepository;
import org.thingsboard.server.dao.model.sql.FlowRateReport;

import java.util.Date;
import java.util.List;

public interface FlowRateReportRepository extends JpaRepository<FlowRateReport, String> {

    List<FlowRateReport> findByStationIdAndTimeBetweenOrderByTimeDesc(String stationId, Date startTime, Date endTime);

    List<FlowRateReport> findByStationIdInAndTimeBetweenOrderByTimeDesc(List<String> stationId, Date startTime, Date endTime);

}
