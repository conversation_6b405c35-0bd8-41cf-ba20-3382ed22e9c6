<!-- gis泵站 -->
<template>
  <div class="onemap-panel-wrapper">
    <Form ref="refForm" :config="FormConfig"> </Form>
    <div class="table-box">
      <FormTable :config="TableConfig"></FormTable>
    </div>
  </div>
</template>
<script lang="ts" setup>
import Point from '@arcgis/core/geometry/Point.js';
import { transNumberUnit } from '@/utils/GlobalHelper';
import { PopImage } from '@/views/arcMap/components';
import { ring } from '../../components/components/chart';
import { useAppStore } from '@/store';
import { getWaterSupplyInfo } from '@/api/secondSupplyManage/monitoringOverview';
import { getStationImageUrl } from '@/utils/URLHelper';

const emit = defineEmits(['highlightMark', 'addMarks']);
const props = defineProps<{
  view?: __esri.MapView;
  menu: IMenuItem;
}>();
const refForm = ref<IFormIns>();
const FormConfig = reactive<IFormConfig>({
  group: [
    {
      id: 'chart',
      fieldset: {
        desc: '今日供水量泵站占比',
        type: 'underline',
        style: {
          marginTop: 0
        }
      },
      fields: [
        {
          type: 'vchart',
          option: ring(),
          style: {
            height: '150px'
          }
        }
      ]
    },
    {
      id: 'tab',
      fields: [
        {
          type: 'input',
          field: 'name',
          appendBtns: [
            { perm: true, text: '刷新', click: () => refreshData() }
          ],
          onChange: () => refreshData()
        }
      ]
    }
  ],
  labelPosition: 'top',
  gutter: 12,
  defaultValue: {
    type: 'all'
  }
});

const TableConfig = reactive<ITable>({
  indexVisible: true,
  dataList: [],
  pagination: {
    hide: true,
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page;
      TableConfig.pagination.limit = size;
    },
    layout: 'total,sizes, jumper'
  },
  handleRowClick: (row) => handleLocate(row),
  columns: [
    {
      minWidth: 120,
      label: '名称',
      prop: 'name',
      sortable: true
    },
    {
      minWidth: 150,
      label: '今日送水量(m³)',
      prop: 'todayWaterSupply',
      sortable: true
    },
    {
      minWidth: 160,
      label: '更新时间',
      prop: 'lastTime',
      sortable: true
    }
  ]
});
const trans = (value: number) => {
  const val = transNumberUnit(value);
  return (val.value?.toFixed(2) || '--') + val.unit;
};
const refreshData = async (all?: boolean) => {
  TableConfig.loading = true;
  try {
    const res = await getWaterSupplyInfo({
      // projectId: store.business.selectedProject?.value,
      name: refForm.value?.dataForm.name
      // status: status === 'all' ? '' : status
    });
    TableConfig.dataList = res.data?.data || [];
    const field = FormConfig.group[0].fields[0] as IFormVChart;
    let total = 0;
    res.data?.data?.map((item) => (total += item.todayWaterSupply || 0));
    const windows: IArcPopConfig[] = [];
    const chartData =
      res.data?.data?.map((item) => {
        // const transValue = transNumberUnit(item.todayWaterSupply || 0)
        const location = item.location?.split(',');
        if (location?.length === 2) {
          const point = new Point({
            longitude: location[0],
            latitude: location[1],
            spatialReference: props.view?.spatialReference
          });
          windows.push({
            visible: false,
            id: item.stationId,
            x: point.x,
            y: point.y,
            offsetY: -40,
            title: item.name,
            customComponent: shallowRef(PopImage),
            customConfig: {
              info: {
                type: 'attrs',
                imageUrl: item.imgs,
                stationId: item.stationId
              }
            },
            attributes: {
              path: props.menu.path,
              id: item.stationId,
              row: item
            },
            symbolConfig: {
              url: getStationImageUrl('泵站.png')
            }
          });
        }
        return {
          name: item.name,
          nameAlias: item.name,
          value: item.todayWaterSupply,
          valueAlias: trans(item.todayWaterSupply),
          scale: '0%'
        };
      }) || [];
    chartData.map((item) => {
      item.scale =
        total === 0
          ? '0%'
          : ((Number(item.value) / total) * 100).toFixed(2) + '%';
      return item;
    });
    if (all) {
      field && (field.option = ring(chartData, 'm³'));
    }
    emit('addMarks', {
      windows,
      customWinComp: shallowRef(PopImage)
    });
  } catch (error) {
    console.dir(error);
  }
  TableConfig.loading = false;
};
const handleLocate = async (row) => {
  emit('highlightMark', props.menu, row?.stationId);
};
onMounted(() => {
  refreshData(true);
});
watch(
  () => useAppStore().isDark,
  () => refreshData(true)
);
</script>

<style lang="scss" scoped>
.onemap-panel-wrapper {
  min-height: 610px;
  height: 100%;
}
.table-box {
  height: calc(100% - 265px);
  min-height: 200px;
}
</style>
