import p from"./TitleCard-BgReUNwX.js";import c from"./GWJK-D5ckGRs7.js";import _ from"./SCDD_zilianda-DAeJlhcp.js";import u from"./YSGY_zilianda-Bpgb0VBC.js";import d from"./ZGS-Qo4tRa49.js";import{_ as f}from"./ThreeMap.vue_vue_type_script_setup_true_lang-DUXb8phz.js";import y from"./Legend-B_ToteFS.js";import w from"./StationStatistic-DVvE1CRo.js";import g from"./TotalStatistic_zilianda-DP8k11Cn.js";import{d as h,c as a,g as S,n as v,p as s,q as t,F as i,i as m,C}from"./index-r0dFAfgr.js";import"./TitleHeader-CBWfLOPA.js";import"./index-BlG8PIOK.js";import"./bengzhan-Dc7fbek7.js";import"./monitoringOverview-DvKhtmcR.js";import"./wing_light-VdA2SB0B.js";import"./headwaterMonitoring-BgK7jThW.js";import"./padStart-BKfyZZDO.js";import"./Map3D-CdvMh5hC.js";import"./yljcd-ChNaQtoa.js";import"./useStation-DJgnSZIA.js";import"./zhandian-YaGuQZe6.js";import"./pipe-nogVzCHG.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./widget-BcWKanF2.js";import"./pe-B8dP0-Ut.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./TileLayer-B5vQ99gG.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./commonProperties-DqNQ4F00.js";import"./project-DUuzYgGl.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./IdentifyResult-4DxLVhTm.js";import"./StatisticsHelper-D-s_6AyQ.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./data-CLo2TII-.js";import"./index-BggOjNGp.js";import"./shuichangzonglan-HwbtusbI.js";const x={class:"content"},b={class:"content_left"},B={class:"content_center"},F={class:"content_left content_right"},G=h({__name:"overview_yanting",setup(N){const r=a(null),e=a(null),l=o=>{console.log(o),r.value=o},n=o=>{console.log(o),e.value=o};return(o,V)=>(S(),v("div",x,[s("div",b,[t(p,{title:"原水供应",class:"ysgy m-b-12"},{default:i(()=>[t(u,{onChangeFactory:l})]),_:1}),t(p,{title:"生产调度",class:"scdd m-b-12"},{default:i(()=>[t(_,{"water-supply":m(r),onShowWaterSupply:n},null,8,["water-supply"])]),_:1}),t(p,{title:"总供水",class:"zgs"},{default:i(()=>[t(d,{"water-supply":m(r)},null,8,["water-supply"])]),_:1})]),s("div",B,[t(f),t(g,{class:"total-statistic","water-supply":m(r),"water-supply-num":m(e)},null,8,["water-supply","water-supply-num"]),t(w,{class:"station-statistic"}),t(y,{class:"legend"})]),s("div",F,[t(p,{title:"管网监控",class:"egzl"},{default:i(()=>[t(c,{height:870})]),_:1})])]))}}),lo=C(G,[["__scopeId","data-v-8b710481"]]);export{lo as default};
