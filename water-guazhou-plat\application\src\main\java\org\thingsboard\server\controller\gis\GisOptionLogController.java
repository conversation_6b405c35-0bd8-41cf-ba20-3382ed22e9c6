package org.thingsboard.server.controller.gis;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.gis.GisOptionLogService;
import org.thingsboard.server.dao.model.request.GisOptionLogListRequest;
import org.thingsboard.server.dao.model.sql.gis.GisOptionLog;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.List;

/**
 * gis日志
 */
@RestController
@RequestMapping("api/gis/optionLog")
public class GisOptionLogController extends BaseController {

    @Autowired
    private GisOptionLogService gisOptionLogService;

    @GetMapping("list")
    public IstarResponse findList(GisOptionLogListRequest request) throws ThingsboardException {
        return IstarResponse.ok(gisOptionLogService.findList(request, getTenantId()));
    }

    @PostMapping("save")
    public IstarResponse save(@RequestBody GisOptionLog entity) throws ThingsboardException {
        gisOptionLogService.save(entity, getCurrentUser());
        return IstarResponse.ok();
    }

    @DeleteMapping("remove")
    public IstarResponse remove(@RequestBody List<String> ids) {
        gisOptionLogService.remove(ids);
        return IstarResponse.ok();
    }

}
