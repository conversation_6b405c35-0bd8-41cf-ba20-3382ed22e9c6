"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[8833],{88764:(e,t,s)=>{s.d(t,{q:()=>o});var i,r,n,a={},u={get exports(){return a},set exports(e){a=e}};i=u,r=function(){function e(s,i,r,n,a){for(;n>r;){if(n-r>600){var u=n-r+1,o=i-r+1,l=Math.log(u),h=.5*Math.exp(2*l/3),c=.5*Math.sqrt(l*h*(u-h)/u)*(o-u/2<0?-1:1);e(s,i,Math.max(r,Math.floor(i-o*h/u+c)),Math.min(n,Math.floor(i+(u-o)*h/u+c)),a)}var d=s[i],_=r,p=n;for(t(s,r,i),a(s[n],d)>0&&t(s,r,n);_<p;){for(t(s,_,p),_++,p--;a(s[_],d)<0;)_++;for(;a(s[p],d)>0;)p--}0===a(s[r],d)?t(s,r,p):t(s,++p,n),p<=i&&(r=p+1),i<=p&&(n=p-1)}}function t(e,t,s){var i=e[t];e[t]=e[s],e[s]=i}function s(e,t){return e<t?-1:e>t?1:0}return function(t,i,r,n,a){e(t,i,r||0,n||t.length-1,a||s)}},void 0!==(n=r())&&(i.exports=n);const o=a},32448:(e,t,s)=>{s.d(t,{Z:()=>o});var i=s(43697),r=s(15923),n=s(50758),a=s(52011);class u{constructor(){this._emitter=new u.EventEmitter(this)}emit(e,t){return this._emitter.emit(e,t)}on(e,t){return this._emitter.on(e,t)}once(e,t){return this._emitter.once(e,t)}hasEventListener(e){return this._emitter.hasEventListener(e)}}!function(e){class t{constructor(e=null){this._target=e,this._listenersMap=null}clear(){this._listenersMap&&this._listenersMap.clear(),this._listenersMap=null}emit(e,t){const s=this._listenersMap&&this._listenersMap.get(e);if(!s)return!1;const i=this._target||this;return[...s].forEach((e=>{e.call(i,t)})),s.length>0}on(e,t){if(Array.isArray(e)){const s=e.map((e=>this.on(e,t)));return(0,n.AL)(s)}if(e.includes(","))throw new TypeError("Evented.on() with a comma delimited string of event types is not supported");this._listenersMap||(this._listenersMap=new Map);const s=this._listenersMap.get(e)||[];return s.push(t),this._listenersMap.set(e,s),{remove:()=>{const s=this._listenersMap&&this._listenersMap.get(e)||[],i=s.indexOf(t);i>=0&&s.splice(i,1)}}}once(e,t){const s=this.on(e,(e=>{s.remove(),t.call(null,e)}));return s}hasEventListener(e){const t=this._listenersMap&&this._listenersMap.get(e);return null!=t&&t.length>0}}e.EventEmitter=t,e.EventedMixin=e=>{let s=class extends e{constructor(){super(...arguments),this._emitter=new t}destroy(){this._emitter.clear()}emit(e,t){return this._emitter.emit(e,t)}on(e,t){return this._emitter.on(e,t)}once(e,t){return this._emitter.once(e,t)}hasEventListener(e){return this._emitter.hasEventListener(e)}};return s=(0,i._)([(0,a.j)("esri.core.Evented")],s),s};let s=class extends r.Z{constructor(){super(...arguments),this._emitter=new u.EventEmitter(this)}destroy(){this._emitter.clear()}emit(e,t){return this._emitter.emit(e,t)}on(e,t){return this._emitter.on(e,t)}once(e,t){return this._emitter.once(e,t)}hasEventListener(e){return this._emitter.hasEventListener(e)}};s=(0,i._)([(0,a.j)("esri.core.Evented")],s),e.EventedAccessor=s}(u||(u={}));const o=u},29730:(e,t,s)=>{s.d(t,{Y:()=>n});var i=s(70586);function r(e,t){return e?t?4:3:t?3:2}function n(e,t,s,n,o){if((0,i.Wi)(t)||!t.lengths.length)return null;const l="upperLeft"===o?.originPosition?-1:1;e.lengths.length&&(e.lengths.length=0),e.coords.length&&(e.coords.length=0);const h=e.coords,c=[],d=s?[Number.POSITIVE_INFINITY,Number.NEGATIVE_INFINITY,Number.POSITIVE_INFINITY,Number.NEGATIVE_INFINITY,Number.POSITIVE_INFINITY,Number.NEGATIVE_INFINITY]:[Number.POSITIVE_INFINITY,Number.NEGATIVE_INFINITY,Number.POSITIVE_INFINITY,Number.NEGATIVE_INFINITY],{lengths:_,coords:p}=t,f=r(s,n);let m=0;for(const e of _){const t=a(d,p,m,e,s,n,l);t&&c.push(t),m+=e*f}if(c.sort(((e,t)=>{let i=l*e[2]-l*t[2];return 0===i&&s&&(i=e[4]-t[4]),i})),c.length){let e=6*c[0][2];h[0]=c[0][0]/e,h[1]=c[0][1]/e,s&&(e=6*c[0][4],h[2]=0!==e?c[0][3]/e:0),(h[0]<d[0]||h[0]>d[1]||h[1]<d[2]||h[1]>d[3]||s&&(h[2]<d[4]||h[2]>d[5]))&&(h.length=0)}if(!h.length){const e=t.lengths[0]?u(p,0,_[0],s,n):null;if(!e)return null;h[0]=e[0],h[1]=e[1],s&&e.length>2&&(h[2]=e[2])}return e}function a(e,t,s,i,n,a,u=1){const o=r(n,a);let l=s,h=s+o,c=0,d=0,_=0,p=0,f=0;for(let s=0,r=i-1;s<r;s++,l+=o,h+=o){const s=t[l],i=t[l+1],r=t[l+2],a=t[h],u=t[h+1],o=t[h+2];let m=s*u-a*i;p+=m,c+=(s+a)*m,d+=(i+u)*m,n&&(m=s*o-a*r,_+=(r+o)*m,f+=m),s<e[0]&&(e[0]=s),s>e[1]&&(e[1]=s),i<e[2]&&(e[2]=i),i>e[3]&&(e[3]=i),n&&(r<e[4]&&(e[4]=r),r>e[5]&&(e[5]=r))}if(p*u>0&&(p*=-1),f*u>0&&(f*=-1),!p)return null;const m=[c,d,.5*p];return n&&(m[3]=_,m[4]=.5*f),m}function u(e,t,s,i,n){const a=r(i,n);let u=t,d=t+a,_=0,p=0,f=0,m=0;for(let t=0,r=s-1;t<r;t++,u+=a,d+=a){const t=e[u],s=e[u+1],r=e[u+2],n=e[d],a=e[d+1],g=e[d+2],y=i?l(t,s,r,n,a,g):o(t,s,n,a);if(y)if(_+=y,i){const e=c(t,s,r,n,a,g);p+=y*e[0],f+=y*e[1],m+=y*e[2]}else{const e=h(t,s,n,a);p+=y*e[0],f+=y*e[1]}}return _>0?i?[p/_,f/_,m/_]:[p/_,f/_]:s>0?i?[e[t],e[t+1],e[t+2]]:[e[t],e[t+1]]:null}function o(e,t,s,i){const r=s-e,n=i-t;return Math.sqrt(r*r+n*n)}function l(e,t,s,i,r,n){const a=i-e,u=r-t,o=n-s;return Math.sqrt(a*a+u*u+o*o)}function h(e,t,s,i){return[e+.5*(s-e),t+.5*(i-t)]}function c(e,t,s,i,r,n){return[e+.5*(i-e),t+.5*(r-t),s+.5*(n-s)]}},50245:(e,t,s)=>{s.d(t,{q:()=>ue});var i=s(67676),r=s(20102),n=s(22974),a=s(70586),u=s(69801),o=s(95330),l=s(67900),h=s(60437),c=s(24470),d=s(20322),_=s(33955),p=s(16306),f=s(8744),m=s(98732),g=s(62432),y=s(37427),E=s(61159),I=s(26993),S=s(10402),T=s(17590),R=s(11490),F=s(99514),w=s(59266),A=s(61247),b=(s(80442),s(92604)),x=s(54790),N=s(44553),C=s(17445),O=s(76882),k=s(58971),v=s(10661);class L{constructor(e){this._observable=new v.s,this._value=e}get(){return(0,k.it)(this._observable),this._value}set(e){e!==this._value&&(this._value=e,this._observable.notify())}}class P{constructor(){this._tasks=new Array,this._running=new L(!1)}get length(){return this._tasks.length}get running(){return this._running.get()}destroy(){this.cancelAll()}runTask(e){for(;!e.done&&this._process(e);)e.madeProgress()}push(e,t,s){return this._running.set(!0),new Promise(((i,r)=>this._tasks.push(new Q(i,r,e,t,s))))}unshift(e,t,s){return this._running.set(!0),new Promise(((i,r)=>this._tasks.unshift(new Q(i,r,e,t,s))))}_process(e){if(0===this._tasks.length)return!1;const t=this._tasks.shift();try{const s=(0,o.Hc)(t.signal);if(s&&!t.abortCallback)t.reject((0,o.zE)());else{const i=s?t.abortCallback?.((0,o.zE)()):t.callback(e);(0,o.y8)(i)?i.then(t.resolve,t.reject):t.resolve(i)}}catch(e){t.reject(e)}return this._running.set(this._tasks.length>0),!0}cancelAll(){const e=(0,o.zE)();for(const t of this._tasks)if(t.abortCallback){const s=t.abortCallback(e);t.resolve(s)}else t.reject(e);this._tasks.length=0,this._running.set(!1)}}class Q{constructor(e,t,s,i,r){this.resolve=e,this.reject=t,this.callback=s,this.signal=i,this.abortCallback=r}}var U=s(43697),M=s(15923),G=s(5600),D=(s(75215),s(52011));let q=class extends M.Z{constructor(){super(...arguments),this.SCHEDULER_LOG_SLOW_TASKS=!1,this.FEATURE_SERVICE_SNAPPING_SOURCE_TILE_TREE_SHOW_TILES=!1}};(0,U._)([(0,G.Cb)()],q.prototype,"SCHEDULER_LOG_SLOW_TASKS",void 0),(0,U._)([(0,G.Cb)()],q.prototype,"FEATURE_SERVICE_SNAPPING_SOURCE_TILE_TREE_SHOW_TILES",void 0),q=(0,U._)([(0,D.j)("esri.views.support.DebugFlags")],q);const B=new q;var Y,H,j,V;!function(e){e[e.ANIMATING=0]="ANIMATING",e[e.INTERACTING=1]="INTERACTING",e[e.IDLE=2]="IDLE"}(Y||(Y={})),(V=H||(H={}))[V.YIELD=1]="YIELD",function(e){e.RESOURCE_CONTROLLER_IMMEDIATE="immediate",e.RESOURCE_CONTROLLER="schedule",e.SLIDE="slide",e.STREAM_DATA_LOADER="stream loader",e.ELEVATION_QUERY="elevation query",e.TERRAIN_SURFACE="terrain",e.SURFACE_GEOMETRY_UPDATES="surface geometry updates",e.LOD_RENDERER="LoD renderer",e.GRAPHICS_CORE="Graphics3D",e.I3S_CONTROLLER="I3S",e.POINT_CLOUD_LAYER="point cloud",e.FEATURE_TILE_FETCHER="feature fetcher",e.OVERLAY="overlay",e.STAGE="stage",e.GRAPHICS_DECONFLICTOR="graphics deconflictor",e.FILTER_VISIBILITY="Graphics3D filter visibility",e.SCALE_VISIBILITY="Graphics3D scale visibility",e.FRUSTUM_VISIBILITY="Graphics3D frustum visibility",e.POINT_OF_INTEREST_FREQUENT="POI frequent",e.POINT_OF_INTEREST_INFREQUENT="POI infrequent",e.LABELER="labeler",e.FEATURE_QUERY_ENGINE="feature query",e.FEATURE_TILE_TREE="feature tile tree",e.FEATURE_TILE_TREE_ACTIVE="fast feature tile tree",e.ELEVATION_ALIGNMENT="elevation alignment",e.TEXT_TEXTURE_ATLAS="text texture atlas",e.TEXTURE_UNLOAD="texture unload",e.LINE_OF_SIGHT_TOOL="line of sight tool",e.LINE_OF_SIGHT_TOOL_INTERACTIVE="interactive line of sight tool",e.ELEVATION_PROFILE="elevation profile",e.SNAPPING="snapping",e.SHADOW_ACCUMULATOR="shadow accumulator",e.CLOUDS_GENERATOR="clouds generator",e[e.TEST_PRIO=1]="TEST_PRIO"}(j||(j={}));const Z=new Map([[j.RESOURCE_CONTROLLER_IMMEDIATE,0],[j.RESOURCE_CONTROLLER,4],[j.SLIDE,0],[j.STREAM_DATA_LOADER,0],[j.ELEVATION_QUERY,0],[j.TERRAIN_SURFACE,1],[j.SURFACE_GEOMETRY_UPDATES,1],[j.LOD_RENDERER,2],[j.GRAPHICS_CORE,2],[j.I3S_CONTROLLER,2],[j.POINT_CLOUD_LAYER,2],[j.FEATURE_TILE_FETCHER,2],[j.OVERLAY,4],[j.STAGE,4],[j.GRAPHICS_DECONFLICTOR,4],[j.FILTER_VISIBILITY,4],[j.SCALE_VISIBILITY,4],[j.FRUSTUM_VISIBILITY,4],[j.CLOUDS_GENERATOR,4],[j.POINT_OF_INTEREST_FREQUENT,6],[j.POINT_OF_INTEREST_INFREQUENT,30],[j.LABELER,8],[j.FEATURE_QUERY_ENGINE,8],[j.FEATURE_TILE_TREE,16],[j.FEATURE_TILE_TREE_ACTIVE,0],[j.ELEVATION_ALIGNMENT,12],[j.TEXT_TEXTURE_ATLAS,12],[j.TEXTURE_UNLOAD,12],[j.LINE_OF_SIGHT_TOOL,16],[j.LINE_OF_SIGHT_TOOL_INTERACTIVE,0],[j.SNAPPING,0],[j.SHADOW_ACCUMULATOR,30]]);function W(e){return Z.has(e)?Z.get(e):"number"==typeof e?e:1}const z=(0,O.HA)(6.5),X=(0,O.HA)(1),J=(0,O.HA)(30),$=(0,O.HA)(1e3/30),K=(0,O.HA)(100);var ee,te;!function(e){e.Scheduler=class{get updating(){return this._updating.get()}_updatingChanged(){this._updating.set(this._tasks.some((e=>e.needsUpdate)))}constructor(){this._updating=new L(!0),this._microTaskQueued=!1,this._frameNumber=0,this.performanceInfo={total:new x.Z("total"),tasks:new Map},this._frameTaskTimes=new Map,this._budget=new s,this._state=Y.INTERACTING,this._tasks=new N.Z,this._runQueue=new N.Z,this._load=0,this._idleStateCallbacks=new N.Z,this._idleUpdatesStartFired=!1,this._forceTask=!1,this._debug=!1,this._debugHandle=(0,C.YP)((()=>B.SCHEDULER_LOG_SLOW_TASKS),(e=>this._debug=e),C.nn);for(const e of Object.keys(j))this.performanceInfo.tasks.set(j[e],new x.Z(j[e]));const e=this;this._test={FRAME_SAFETY_BUDGET:z,INTERACTING_BUDGET:$,IDLE_BUDGET:K,get availableBudget(){return e._budget.budget},usedBudget:0,getBudget:()=>e._budget,setBudget:t=>e._budget=t,updateTask:e=>this._updateTask(e),getState:e=>this._getState(e),getRuntime:e=>this._getRuntime(e),frameTaskTimes:this._frameTaskTimes,resetRuntimes:()=>this._resetRuntimes(),getRunning:()=>this._getRunning()}}destroy(){this._tasks.toArray().forEach((e=>e.remove())),this._tasks.clear(),(0,a.hw)(this._debugHandle),this._microTaskQueued=!1,this._updatingChanged()}taskRunningChanged(e){this._updatingChanged(),e&&this._budget.remaining>0&&!this._microTaskQueued&&(this._microTaskQueued=!0,queueMicrotask((()=>{this._microTaskQueued&&(this._microTaskQueued=!1,this._budget.remaining>0&&this._schedule()&&this.frame())})))}registerTask(e,s){const i=W(e),r=new t(this,e,s,i);return this._tasks.push(r),this._updatingChanged(),this.performanceInfo.tasks.has(e)||this.performanceInfo.tasks.set(e,new x.Z(e)),r}registerIdleStateCallbacks(e,t){const s={idleBegin:e,idleEnd:t};this._idleStateCallbacks.push(s),this.state===Y.IDLE&&this._idleUpdatesStartFired&&s.idleBegin();const i=this;return{remove:()=>this._removeIdleStateCallbacks(s),set idleBegin(e){i._idleUpdatesStartFired&&(s.idleEnd(),i._state===Y.IDLE&&e()),s.idleBegin=e},set idleEnd(e){s.idleEnd=e}}}get load(){return this._load}set state(e){this._state!==e&&(this._state=e,this.state!==Y.IDLE&&this._idleUpdatesStartFired&&(this._idleUpdatesStartFired=!1,this._idleStateCallbacks.forAll((e=>e.idleEnd()))))}get state(){return this._state}updateBudget(e){this._test.usedBudget=0,++this._frameNumber;let t=z,s=e.frameDuration,i=X;switch(this.state){case Y.IDLE:t=(0,O.HA)(0),s=(0,O.HA)(Math.max(K,e.frameDuration)),i=J;break;case Y.INTERACTING:s=(0,O.HA)(Math.max($,e.frameDuration));case Y.ANIMATING:}return s=(0,O.HA)(s-e.elapsedFrameTime-t),this.state!==Y.IDLE&&s<X&&!this._forceTask?(this._forceTask=!0,!1):(s=(0,O.HA)(Math.max(s,i)),this._budget.reset(s,this.state),this._updateLoad(),this._schedule())}frame(){switch(this._forceTask=!1,this._microTaskQueued=!1,this.state){case Y.IDLE:this._idleUpdatesStartFired||(this._idleUpdatesStartFired=!0,this._idleStateCallbacks.forAll((e=>e.idleBegin()))),this._runIdle();break;case Y.INTERACTING:this._runInteracting();break;default:this._runAnimating()}this._test.usedBudget=this._budget.elapsed}stopFrame(){this._budget.reset((0,O.HA)(0),this._state),this._budget.madeProgress()}_removeIdleStateCallbacks(e){this._idleUpdatesStartFired&&e.idleEnd(),this._idleStateCallbacks.removeUnordered(e)}removeTask(e){this._tasks.removeUnordered(e),this._runQueue.removeUnordered(e),this._updatingChanged()}_updateTask(e){this._tasks.forAll((t=>{t.name===e&&t.setPriority(e)}))}_getState(e){if(this._runQueue.some((t=>t.name===e)))return te.SCHEDULED;let t=te.IDLE;return this._tasks.forAll((s=>{s.name===e&&s.needsUpdate&&(s.schedulePriority<=1?t=te.READY:t!==te.READY&&(t=te.WAITING))})),t}_getRuntime(e){let t=0;return this._tasks.forAll((s=>{s.name===e&&(t+=s.runtime)})),t}_resetRuntimes(){this._tasks.forAll((e=>e.runtime=0))}_getRunning(){const e=new Map;if(this._tasks.forAll((t=>{t.needsUpdate&&e.set(t.name,(e.get(t.name)||0)+1)})),0===e.size)return null;let t="";return e.forEach(((e,s)=>{t+=e>1?` ${e}x ${s}`:` ${s}`})),t}_runIdle(){this._run()}_runInteracting(){this._run()}_runAnimating(){this._run()}_updateLoad(){const e=this._tasks.reduce(((e,t)=>t.needsUpdate?++e:e),0);this._load=.9*this._load+e*(1-.9)}_schedule(){for(this._runQueue.filterInPlace((e=>!!e.needsUpdate||(e.schedulePriority=e.basePriority,!1))),this._tasks.forAll((e=>{0===e.basePriority&&e.needsUpdate&&!this._runQueue.includes(e)&&e.blockFrame!==this._frameNumber&&this._runQueue.unshift(e)}));0===this._runQueue.length;){let e=!1,t=0;if(this._tasks.forAll((s=>{s.needsUpdate&&0!==s.schedulePriority&&0!==s.basePriority&&s.blockFrame!==this._frameNumber&&(e=!0,t=Math.max(t,s.basePriority),1===s.schedulePriority?(s.schedulePriority=0,this._runQueue.push(s)):--s.schedulePriority)})),!e)return this._updatingChanged(),!1}return this._updatingChanged(),!0}_run(){const e=this._budget.now();this._startFrameTaskTimes();do{for(;this._runQueue.length>0;){const t=this._budget.now(),s=this._runQueue.pop();this._budget.resetProgress();try{s.task.runTask(this._budget)===H.YIELD&&(s.blockFrame=this._frameNumber)}catch(e){b.Z.getLogger("esri.views.support.Scheduler").error(`Exception in task "${s.name}"`,e)}!this._budget.hasProgressed&&s.blockFrame!==this._frameNumber&&s.needsUpdate&&(s.name,j.I3S_CONTROLLER,s.blockFrame=this._frameNumber),s.schedulePriority=s.basePriority;const i=this._budget.now()-t;if(s.runtime+=i,this._frameTaskTimes.set(s.priority,this._frameTaskTimes.get(s.priority)+i),this._debug&&i>2*this._budget.budget&&console.log("Task",s.name,"used",i,"of max",this._budget.budget,"ms"),this._budget.remaining<=0)return this._updatingChanged(),void this._recordFrameTaskTimes(this._budget.now()-e)}}while(this._schedule());this._updatingChanged(),this._recordFrameTaskTimes(this._budget.now()-e)}_startFrameTaskTimes(){for(const e of Object.keys(j))this._frameTaskTimes.set(j[e],0)}_recordFrameTaskTimes(e){this._frameTaskTimes.forEach(((e,t)=>this.performanceInfo.tasks.get(t).record(e))),this.performanceInfo.total.record(e)}get test(){return this._test}};class t{get task(){return this._task.get()}get updating(){return this._queue.running}constructor(e,t,s,i){this._scheduler=e,this.name=t,this._basePriority=i,this.blockFrame=0,this.runtime=0,this._queue=new P,this._handles=new A.Z,this.schedulePriority=this._basePriority,this._task=new L((0,a.pC)(s)?s:this._queue),this._handles.add((0,C.gx)((()=>this.task.running),(t=>e.taskRunningChanged(t))))}remove(){this.processQueue(se),this._scheduler.removeTask(this),this.schedule=ie.schedule,this.reschedule=ie.reschedule,this._handles.destroy()}get basePriority(){return this._basePriority}setPriority(e){this.name=e;const t=W(e);0!==this._basePriority&&0===this.schedulePriority||(this.schedulePriority=t),this._basePriority=t}get priority(){return this.name}set priority(e){this.setPriority(e)}get needsUpdate(){return this.updating||this.task.running}schedule(e,t,s){return this._queue.push(e,t,s)}reschedule(e,t,s){return this._queue.unshift(e,t,s)}processQueue(e){this._queue.runTask(e)}}class s{constructor(){this._begin="undefined"!=typeof performance?performance.now():0,this._budget=0,this._state=Y.IDLE,this._done=!1,this._progressed=!1,this._enabled=!0}run(e){return!this.done&&(!0===e()&&this.madeProgress(),!0)}get done(){return this._done}get budget(){return this._budget}madeProgress(){return this._progressed=!0,this._done=this.elapsed>=this._budget&&this._enabled,this._done}get state(){return this._state}get enabled(){return this._enabled}set enabled(e){this._enabled=e}reset(e,t){this._begin=this.now(),this._budget=e,this._state=t,this.resetProgress()}get remaining(){return Math.max(this._budget-this.elapsed,0)}now(){return performance.now()}get elapsed(){return performance.now()-this._begin}resetProgress(){this._progressed=!1,this._done=!1}get hasProgressed(){return this._progressed}}e.Budget=s}(ee||(ee={})),function(e){e.SCHEDULED="s",e.READY="r",e.WAITING="w",e.IDLE="i"}(te||(te={}));const se=(()=>{const e=new ee.Budget;return e.enabled=!1,e})(),ie=new class{remove(){}processQueue(){}schedule(e,t,s){try{if((0,o.Hc)(t)){const e=(0,o.zE)();return s?Promise.resolve(s(e)):Promise.reject(e)}return(0,o.gx)(e(se))}catch(e){return Promise.reject(e)}}reschedule(e,t,s){return this.schedule(e,t,s)}},re="feature-store:unsupported-query",ne=new u.WJ(2e6);let ae=0;class ue{constructor(e){this._geometryQueryCache=null,this._changeHandle=null,this.capabilities={query:E.g},this.geometryType=e.geometryType,this.hasM=!!e.hasM,this.hasZ=!!e.hasZ,this.objectIdField=e.objectIdField,this.spatialReference=e.spatialReference,this.definitionExpression=e.definitionExpression,this.featureStore=e.featureStore,this.aggregateAdapter=e.aggregateAdapter,this._changeHandle=this.featureStore.events.on("changed",(()=>this.clearCache())),this.timeInfo=e.timeInfo,e.cacheSpatialQueries&&(this._geometryQueryCache=new u.Xq(ae+++"$$",ne)),this.fieldsIndex=new F.Z(e.fields),e.scheduler&&e.priority&&(this._frameTask=e.scheduler.registerTask(e.priority))}destroy(){this._frameTask=(0,a.hw)(this._frameTask),this.clearCache(),(0,a.SC)(this._geometryQueryCache),this._changeHandle=(0,a.hw)(this._changeHandle),(0,a.SC)(this.fieldsIndex)}get featureAdapter(){return this.featureStore.featureAdapter}clearCache(){this._geometryQueryCache?.clear(),this._allFeaturesPromise=null,this._timeExtentPromise=null}async executeQuery(e,t){try{return(await this._executeQuery(e,{},t)).createQueryResponse()}catch(t){if(t!==R.vF)throw t;return new I.y([],e,this).createQueryResponse()}}async executeQueryForCount(e={},t){try{return(await this._executeQuery(e,{returnGeometry:!1,returnCentroid:!1,outSR:null},t)).createQueryResponseForCount()}catch(e){if(e!==R.vF)throw e;return 0}}async executeQueryForExtent(e,t){const s=e.outSR;try{const i=await this._executeQuery(e,{returnGeometry:!0,returnCentroid:!1,outSR:null},t),r=i.size;return r?{count:r,extent:await this._getBounds(i.items,i.spatialReference,s||this.spatialReference)}:{count:0,extent:null}}catch(e){if(e===R.vF)return{count:0,extent:null};throw e}}async executeQueryForIds(e,t){return this.executeQueryForIdSet(e,t).then((e=>Array.from(e)))}async executeQueryForIdSet(e,t){try{const s=await this._executeQuery(e,{returnGeometry:!0,returnCentroid:!1,outSR:null},t),i=s.items,r=new Set;return await this._reschedule((()=>{for(const e of i)r.add(s.featureAdapter.getObjectId(e))}),t),r}catch(e){if(e===R.vF)return new Set;throw e}}async executeQueryForSnapping(e,t){const{point:s,distance:i,types:r}=e;if(r===I.r.NONE)return{candidates:[]};const n=await this._reschedule((()=>this._checkQuerySupport(e.query)),t),u=!(0,f.fS)(s.spatialReference,this.spatialReference);u&&await(0,y._W)(s.spatialReference,this.spatialReference);const o="number"==typeof i?i:i.x,l="number"==typeof i?i:i.y,h={xmin:s.x-o,xmax:s.x+o,ymin:s.y-l,ymax:s.y+l,spatialReference:s.spatialReference},c=u?(0,y.iV)(h,this.spatialReference):h;if(!c)return{candidates:[]};const d=(await(0,p.aX)((0,_.im)(s),null,{signal:t}))[0],m=(await(0,p.aX)((0,_.im)(c),null,{signal:t}))[0];if((0,a.Wi)(d)||(0,a.Wi)(m))return{candidates:[]};const g=new I.y(await this._reschedule((()=>this._searchFeatures(this._getQueryBBoxes(m.toJSON()))),t),n,this);await this._reschedule((()=>this._executeObjectIdsQuery(g)),t),await this._reschedule((()=>this._executeTimeQuery(g)),t),await this._reschedule((()=>this._executeAttributesQuery(g)),t);const E=d.toJSON(),S=u?(0,y.iV)(E,this.spatialReference):E,T=u?Math.max(c.xmax-c.xmin,c.ymax-c.ymin)/2:i;return g.createSnappingResponse({...e,point:S,distance:T},s.spatialReference)}async executeQueryForLatestObservations(e,t){if(!this.timeInfo||!this.timeInfo.trackIdField)throw new r.Z(re,"Missing timeInfo or timeInfo.trackIdField",{query:e,timeInfo:this.timeInfo});try{const s=await this._executeQuery(e,{},t);return await this._reschedule((()=>this._filterLatest(s)),t),s.createQueryResponse()}catch(t){if(t!==R.vF)throw t;return new I.y([],e,this).createQueryResponse()}}async executeQueryForSummaryStatistics(e={},t,s){const{field:i,normalizationField:r,valueExpression:n}=t;return(await this._getQueryEngineResultForStats(e,{field:i,normalizationField:r,valueExpression:n},s)).createSummaryStatisticsResponse(t)}async executeQueryForUniqueValues(e={},t,s){const{field:i,field2:r,field3:n,valueExpression:a}=t;return(await this._getQueryEngineResultForStats(e,{field:i,field2:r,field3:n,valueExpression:a},s)).createUniqueValuesResponse(t)}async executeQueryForClassBreaks(e={},t,s){const{field:i,normalizationField:r,valueExpression:n}=t;return(await this._getQueryEngineResultForStats(e,{field:i,normalizationField:r,valueExpression:n},s)).createClassBreaksResponse(t)}async executeQueryForHistogram(e={},t,s){const{field:i,normalizationField:r,valueExpression:n}=t;return(await this._getQueryEngineResultForStats(e,{field:i,normalizationField:r,valueExpression:n},s)).createHistogramResponse(t)}async fetchRecomputedExtents(e){const[t,s]=await Promise.all(["getFullExtent"in this.featureStore&&this.featureStore.getFullExtent?Promise.resolve(this.featureStore.getFullExtent(this.spatialReference)):this._getBounds(await this._getAllFeatures(),this.spatialReference,this.spatialReference),(0,a.pC)(this._timeExtentPromise)?this._timeExtentPromise:this._timeExtentPromise=(0,T.R)(this.timeInfo,this.featureStore)]);return(0,o.k_)(e),{fullExtent:t,timeExtent:s}}async _getBounds(e,t,s){const i=(0,h.t8)((0,h.Ue)(),h.Gv);await this.featureStore.forEachBounds(e,(e=>(0,h.TC)(i,e)));const r={xmin:i[0],ymin:i[1],xmax:i[3],ymax:i[4],spatialReference:(0,R.S2)(this.spatialReference)};this.hasZ&&isFinite(i[2])&&isFinite(i[5])&&(r.zmin=i[2],r.zmax=i[5]);const n=(0,y.iV)(r,t,s);if(n.spatialReference=(0,R.S2)(s),n.xmax-n.xmin==0){const e=(0,l.c9)(n.spatialReference);n.xmin-=e,n.xmax+=e}if(n.ymax-n.ymin==0){const e=(0,l.c9)(n.spatialReference);n.ymin-=e,n.ymax+=e}if(this.hasZ&&null!=n.zmin&&null!=n.zmax&&n.zmax-n.zmin==0){const e=(0,l.c9)(n.spatialReference);n.zmin-=e,n.zmax+=e}return n}async _schedule(e,t){return(0,a.pC)(this._frameTask)?this._frameTask.schedule(e,t):e(se)}async _reschedule(e,t){return(0,a.pC)(this._frameTask)?this._frameTask.reschedule(e,t):e(se)}async _getAllFeaturesQueryEngineResult(e){return new I.y(await this._getAllFeatures(),e,this)}async _getAllFeatures(){if((0,a.Wi)(this._allFeaturesPromise)){const e=[];this._allFeaturesPromise=(async()=>{await this.featureStore.forEach((t=>e.push(t)))})().then((()=>e))}const e=this._allFeaturesPromise,t=await e;return e===this._allFeaturesPromise?t.slice():this._getAllFeatures()}async _executeQuery(e,t,s){e=(0,n.d9)(e),e=await this._schedule((()=>(0,R.Up)(e,this.definitionExpression,this.spatialReference)),s),e=await this._reschedule((()=>this._checkQuerySupport(e)),s),e={...e,...t};const i=await this._reschedule((()=>this._executeSceneFilterQuery(e,s)),s),r=await this._reschedule((()=>this._executeGeometryQuery(e,i,s)),s);return await this._reschedule((()=>this._executeAggregateIdsQuery(r)),s),await this._reschedule((()=>this._executeObjectIdsQuery(r)),s),await this._reschedule((()=>this._executeTimeQuery(r)),s),await this._reschedule((()=>this._executeAttributesQuery(r)),s),r}async _executeSceneFilterQuery(e,t){if((0,a.Wi)(e.sceneFilter))return null;const{outSR:s,returnGeometry:i,returnCentroid:r}=e,n=this.featureStore.featureSpatialReference,u=e.sceneFilter.geometry,o=(0,a.Wi)(n)||(0,f.fS)(n,u.spatialReference)?u:(0,y.iV)(u,n);if(!o)return null;const l=i||r,h=(0,f.JY)(s)&&!(0,f.fS)(this.spatialReference,s)&&l?async e=>this._project(e,s):e=>e,c=this.featureAdapter,d=await this._reschedule((()=>this._searchFeatures(this._getQueryBBoxes(o))),t);if("disjoint"===e.sceneFilter.spatialRelationship){if(!d.length)return null;const s=new Set;for(const e of d)s.add(c.getObjectId(e));const i=await this._reschedule((()=>this._getAllFeatures()),t);return h(await this._reschedule((async()=>{const r=await(0,S.cW)("esriSpatialRelDisjoint",o,this.geometryType,this.hasZ,this.hasM),n=await this._runSpatialFilter(i,(e=>!s.has(c.getObjectId(e))||r(c.getGeometry(e))),t);return new I.y(n,e,this)}),t))}if(!d.length)return new I.y([],e,this);if(this._canExecuteSinglePass(o,e))return h(new I.y(d,e,this));const _=await(0,S.cW)("esriSpatialRelContains",o,this.geometryType,this.hasZ,this.hasM),p=await this._runSpatialFilter(d,(e=>_(c.getGeometry(e))),t);return h(new I.y(p,e,this))}async _executeGeometryQuery(e,t,s){if((0,a.pC)(t)&&0===t.items.length)return t;e=(0,a.pC)(t)?t.query:e;const{geometry:r,outSR:n,spatialRel:u,returnGeometry:o,returnCentroid:l}=e,h=this.featureStore.featureSpatialReference,c=!r||(0,a.Wi)(h)||(0,f.fS)(h,r.spatialReference)?r:(0,y.iV)(r,h),d=o||l,_=(0,f.JY)(n)&&!(0,f.fS)(this.spatialReference,n),p=this._geometryQueryCache&&(0,a.Wi)(t)?_&&d?JSON.stringify({originalFilterGeometry:r,spatialRelationship:u,outSpatialReference:n}):JSON.stringify({originalFilterGeometry:r,spatialRelationship:u}):null,m=p?this._geometryQueryCache.get(p):null;if((0,a.pC)(m))return new I.y(m,e,this);const g=async e=>(_&&d&&await this._project(e,n),p&&this._geometryQueryCache.put(p,e.items,e.items.length+1),e);if(!c)return g((0,a.pC)(t)?t:await this._getAllFeaturesQueryEngineResult(e));const E=this.featureAdapter;let T=await this._reschedule((()=>this._searchFeatures(this._getQueryBBoxes(r))),s);if("esriSpatialRelDisjoint"===u){if(!T.length)return g((0,a.pC)(t)?t:await this._getAllFeaturesQueryEngineResult(e));const i=new Set;for(const e of T)i.add(E.getObjectId(e));const r=(0,a.pC)(t)?t.items:await this._reschedule((()=>this._getAllFeatures()),s),n=await this._reschedule((async()=>{const t=await(0,S.cW)(u,c,this.geometryType,this.hasZ,this.hasM),n=await this._runSpatialFilter(r,(e=>!i.has(E.getObjectId(e))||t(E.getGeometry(e))),s);return new I.y(n,e,this)}),s);return g(n)}if((0,a.pC)(t)){const e=new i.SO;T=T.filter((s=>(0,i.cq)(t.items,s,t.items.length,e)>=0))}if(!T.length){const t=new I.y([],e,this);return p&&this._geometryQueryCache.put(p,t.items,1),t}if(this._canExecuteSinglePass(c,e))return g(new I.y(T,e,this));const R=await(0,S.cW)(u,c,this.geometryType,this.hasZ,this.hasM),F=await this._runSpatialFilter(T,(e=>R(E.getGeometry(e))),s);return g(new I.y(F,e,this))}_executeAggregateIdsQuery(e){if(0===e.items.length||!e.query.aggregateIds||!e.query.aggregateIds.length||(0,a.Wi)(this.aggregateAdapter))return;const t=new Set;for(const s of e.query.aggregateIds)this.aggregateAdapter.getFeatureObjectIds(s).forEach((e=>t.add(e)));const s=this.featureAdapter.getObjectId;e.items=e.items.filter((e=>t.has(s(e))))}_executeObjectIdsQuery(e){if(0===e.items.length||!e.query.objectIds||!e.query.objectIds.length)return;const t=new Set(e.query.objectIds),s=this.featureAdapter.getObjectId;e.items=e.items.filter((e=>t.has(s(e))))}_executeTimeQuery(e){if(0===e.items.length)return;const t=(0,T.y)(this.timeInfo,e.query.timeExtent,this.featureAdapter);(0,a.Wi)(t)||(e.items=e.items.filter(t))}_executeAttributesQuery(e){if(0===e.items.length)return;const t=(0,g.Jc)(e.query.where,this.fieldsIndex);if(t){if(!t.isStandardized)throw new TypeError("Where clause is not standardized");e.items=e.items.filter((e=>t.testFeature(e,this.featureAdapter)))}}async _runSpatialFilter(e,t,s){if(!t)return e;if((0,a.Wi)(this._frameTask))return e.filter((e=>t(e)));let i=0;const r=new Array,n=async a=>{for(;i<e.length;){const u=e[i++];t(u)&&(r.push(u),a.madeProgress()),a.done&&await this._reschedule((e=>n(e)),s)}};return this._reschedule((e=>n(e)),s).then((()=>r))}_filterLatest(e){const{trackIdField:t,startTimeField:s,endTimeField:i}=this.timeInfo,r=i||s,n=new Map,a=this.featureAdapter.getAttribute;for(const s of e.items){const e=a(s,t),i=a(s,r),u=n.get(e);(!u||i>a(u,r))&&n.set(e,s)}e.items=Array.from(n.values())}_canExecuteSinglePass(e,t){const{spatialRel:s}=t;return(0,S.hN)(e)&&("esriSpatialRelEnvelopeIntersects"===s||"esriGeometryPoint"===this.geometryType&&("esriSpatialRelIntersects"===s||"esriSpatialRelContains"===s||"esriSpatialRelWithin"===s))}async _project(e,t){if(!t||(0,f.fS)(this.spatialReference,t))return e;const s=this.featureAdapter,i=await(0,y.oj)(e.items.map((e=>(0,R.Op)(this.geometryType,this.hasZ,this.hasM,s.getGeometry(e)))),this.spatialReference,t);return e.items=i.map(((t,i)=>s.cloneWithGeometry(e.items[i],(0,m.GH)(t,this.hasZ,this.hasM)))),e}_getQueryBBoxes(e){if((0,S.hN)(e)){if((0,_.YX)(e))return[(0,c.al)(e.xmin,e.ymin,e.xmax,e.ymax)];if((0,_.oU)(e))return e.rings.map((e=>(0,c.al)(Math.min(e[0][0],e[2][0]),Math.min(e[0][1],e[2][1]),Math.max(e[0][0],e[2][0]),Math.max(e[0][1],e[2][1]))))}return[(0,d.$P)((0,c.Ue)(),e)]}async _searchFeatures(e){const t=new Set;await Promise.all(e.map((e=>this.featureStore.forEachInBounds(e,(e=>t.add(e))))));const s=Array.from(t.values());return t.clear(),s}async _checkStatisticsSupport(e,t){if((e.distance??0)<0||null!=e.geometryPrecision||e.multipatchOption||e.pixelSize||e.relationParam||e.text||e.outStatistics||e.groupByFieldsForStatistics||e.having||e.orderByFields)throw new r.Z(re,"Unsupported query options",{query:e});return this._checkAttributesQuerySupport(e),Promise.all([this._checkStatisticsParamsSupport(t),(0,S.P0)(e,this.geometryType,this.spatialReference),(0,y._W)(this.spatialReference,e.outSR)]).then((()=>e))}async _checkStatisticsParamsSupport(e){let t=[];if(e.valueExpression){const{arcadeUtils:s}=await(0,w.LC)();t=s.extractFieldNames(e.valueExpression)}if(e.field&&t.push(e.field),e.field2&&t.push(e.field2),e.field3&&t.push(e.field3),e.normalizationField&&t.push(e.normalizationField),!t.length)throw new r.Z(re,"params should have at least a field or valueExpression",{params:e});(0,g.Of)(this.fieldsIndex,t,"params contains missing fields")}async _checkQuerySupport(e){if((e.distance??0)<0||null!=e.geometryPrecision||e.multipatchOption||e.pixelSize||e.relationParam||e.text)throw new r.Z(re,"Unsupported query options",{query:e});return this._checkAttributesQuerySupport(e),this._checkStatisticsQuerySupport(e),Promise.all([(0,S.P0)(e,this.geometryType,this.spatialReference),(0,y._W)(this.spatialReference,e.outSR)]).then((()=>e))}_checkAttributesQuerySupport(e){const{outFields:t,orderByFields:s,returnDistinctValues:i,outStatistics:n}=e,a=n?n.map((e=>e.outStatisticFieldName&&e.outStatisticFieldName.toLowerCase())).filter(Boolean):[];if(s&&s.length>0){const e=" asc",t=" desc",i=s.map((s=>{const i=s.toLowerCase();return i.includes(e)?i.split(e)[0]:i.includes(t)?i.split(t)[0]:s})).filter((e=>!a.includes(e)));(0,g.Of)(this.fieldsIndex,i,"orderByFields contains missing fields")}if(t&&t.length>0)(0,g.Of)(this.fieldsIndex,t,"outFields contains missing fields");else if(i)throw new r.Z(re,"outFields should be specified for returnDistinctValues",{query:e});(0,g.hO)(this.fieldsIndex,e.where)}_checkStatisticsQuerySupport(e){const{outStatistics:t,groupByFieldsForStatistics:s,having:i}=e,n=s&&s.length,a=t&&t.length;if(i){if(!n||!a)throw new r.Z(re,"outStatistics and groupByFieldsForStatistics should be specified with having",{query:e});(0,g.z4)(this.fieldsIndex,i,t)}if(a){if(!function(e){return null!=e&&e.every((e=>"exceedslimit"!==e.statisticType))}(t))return;const i=t.map((e=>e.onStatisticField)).filter(Boolean);(0,g.Of)(this.fieldsIndex,i,"onStatisticFields contains missing fields"),n&&(0,g.Of)(this.fieldsIndex,s,"groupByFieldsForStatistics contains missing fields");for(const s of t){const{onStatisticField:t,statisticType:i}=s;if("percentile_disc"!==i&&"percentile_cont"!==i||!("statisticParameters"in s)){if("count"!==i&&t&&(0,g.G3)(t,this.fieldsIndex))throw new r.Z(re,"outStatistics contains non-numeric fields",{definition:s,query:e})}else{const{statisticParameters:t}=s;if(!t)throw new r.Z(re,"statisticParamters should be set for percentile type",{definition:s,query:e})}}}}async _getQueryEngineResultForStats(e,t,s){e=(0,n.d9)(e);try{e=await this._schedule((()=>(0,R.Up)(e,this.definitionExpression,this.spatialReference)),s),e=await this._reschedule((()=>this._checkStatisticsSupport(e,t)),s);const i=await this._reschedule((()=>this._executeSceneFilterQuery(e,s)),s),r=await this._reschedule((()=>this._executeGeometryQuery(e,i,s)),s);return await this._reschedule((()=>this._executeAggregateIdsQuery(r)),s),await this._reschedule((()=>this._executeObjectIdsQuery(r)),s),await this._reschedule((()=>this._executeTimeQuery(r)),s),await this._reschedule((()=>this._executeAttributesQuery(r)),s),r}catch(t){if(t!==R.vF)throw t;return new I.y([],e,this)}}}},61159:(e,t,s)=>{s.d(t,{g:()=>i});const i={supportsStatistics:!0,supportsPercentileStatistics:!0,supportsSpatialAggregationStatistics:!1,supportedSpatialAggregationStatistics:{envelope:!1,centroid:!1,convexHull:!1},supportsCentroid:!0,supportsCacheHint:!1,supportsDistance:!0,supportsDistinct:!0,supportsExtent:!0,supportsGeometryProperties:!1,supportsHavingClause:!0,supportsOrderBy:!0,supportsPagination:!0,supportsQuantization:!0,supportsQuantizationEditMode:!1,supportsQueryGeometry:!0,supportsResultType:!1,supportsSqlExpression:!0,supportsMaxRecordCountFactor:!1,supportsStandardizedQueriesOnly:!0,supportsTopFeaturesQuery:!1,supportsQueryByOthers:!0,supportsHistoricMoment:!1,supportsFormatPBF:!1,supportsDisjointSpatialRelationship:!0,supportsDefaultSpatialReference:!1,supportsFullTextSearch:!1,supportsCompactGeometry:!1,maxRecordCountFactor:void 0,maxRecordCount:void 0,standardMaxRecordCount:void 0,tileMaxRecordCount:void 0}},10402:(e,t,s)=>{s.d(t,{hN:()=>R,P0:()=>T,cW:()=>S});var i=s(20102),r=s(54102),n=s(87416),a=s(33955),u=s(8744);function o(e,t){return e?t?4:3:t?3:2}function l(e,t,s,i,r,n){const a=o(r,n),{coords:u,lengths:l}=i;if(!l)return!1;for(let i=0,r=0;i<l.length;i++,r+=a)if(!h(e,t,s,u[r],u[r+1]))return!1;return!0}function h(e,t,s,i,r){if(!e)return!1;const n=o(t,s),{coords:a,lengths:u}=e;let l=!1,h=0;for(const e of u)l=c(l,a,n,h,e,i,r),h+=e*n;return l}function c(e,t,s,i,r,n,a){let u=e,o=i;for(let e=i,l=i+r*s;e<l;e+=s){o=e+s,o===l&&(o=i);const r=t[e],h=t[e+1],c=t[o],d=t[o+1];(h<a&&d>=a||d<a&&h>=a)&&r+(a-h)/(d-h)*(c-r)<n&&(u=!u)}return u}var d=s(98732),_=s(5428),p=s(37427),f=s(11490);const m="feature-store:unsupported-query",g={esriSpatialRelIntersects:"intersects",esriSpatialRelContains:"contains",esriSpatialRelCrosses:"crosses",esriSpatialRelDisjoint:"disjoint",esriSpatialRelEnvelopeIntersects:"intersects",esriSpatialRelIndexIntersects:null,esriSpatialRelOverlaps:"overlaps",esriSpatialRelTouches:"touches",esriSpatialRelWithin:"within",esriSpatialRelRelation:null},y={esriSpatialRelIntersects:!0,esriSpatialRelContains:!0,esriSpatialRelWithin:!0,esriSpatialRelCrosses:!0,esriSpatialRelDisjoint:!0,esriSpatialRelTouches:!0,esriSpatialRelOverlaps:!0,esriSpatialRelEnvelopeIntersects:!0,esriSpatialRelIndexIntersects:!1,esriSpatialRelRelation:!1},E={esriGeometryPoint:!0,esriGeometryMultipoint:!0,esriGeometryPolyline:!0,esriGeometryPolygon:!0,esriGeometryEnvelope:!0},I={esriGeometryPoint:!0,esriGeometryMultipoint:!0,esriGeometryPolyline:!0,esriGeometryPolygon:!0,esriGeometryEnvelope:!1};function S(e,t,i,u,o){if((0,a.oU)(t)&&"esriGeometryPoint"===i&&("esriSpatialRelIntersects"===e||"esriSpatialRelContains"===e)){const e=(0,d.Uy)(new _.Z,t,!1,!1);return Promise.resolve((t=>function(e,t,s,i){return h(e,!1,!1,i.coords[0],i.coords[1])}(e,0,0,t)))}if((0,a.oU)(t)&&"esriGeometryMultipoint"===i){const s=(0,d.Uy)(new _.Z,t,!1,!1);if("esriSpatialRelContains"===e)return Promise.resolve((e=>l(s,!1,!1,e,u,o)))}if((0,a.YX)(t)&&"esriGeometryPoint"===i&&("esriSpatialRelIntersects"===e||"esriSpatialRelContains"===e))return Promise.resolve((e=>(0,r.aV)(t,(0,f.Op)(i,u,o,e))));if((0,a.YX)(t)&&"esriGeometryMultipoint"===i&&"esriSpatialRelContains"===e)return Promise.resolve((e=>(0,r.lQ)(t,(0,f.Op)(i,u,o,e))));if((0,a.YX)(t)&&"esriSpatialRelIntersects"===e){const e="mesh"===(c=i)?n.h_:(0,n.IY)(c);return Promise.resolve((s=>e(t,(0,f.Op)(i,u,o,s))))}var c;return Promise.all([s.e(5837),s.e(247)]).then(s.bind(s,30247)).then((s=>{const r=s[g[e]].bind(null,t.spatialReference,t);return e=>r((0,f.Op)(i,u,o,e))}))}async function T(e,t,s){const{spatialRel:r,geometry:n}=e;if(n){if(!function(e){return null!=e&&!0===y[e]}(r))throw new i.Z(m,"Unsupported query spatial relationship",{query:e});if((0,u.JY)(n.spatialReference)&&(0,u.JY)(s)){if(!function(e){return null!=e&&!0===E[(0,a.Ji)(e)]}(n))throw new i.Z(m,"Unsupported query geometry type",{query:e});if(!function(e){return null!=e&&!0===I[e]}(t))throw new i.Z(m,"Unsupported layer geometry type",{query:e});if(e.outSR)return(0,p._W)(e.geometry&&e.geometry.spatialReference,e.outSR)}}}function R(e){if((0,a.YX)(e))return!0;if((0,a.oU)(e)){for(const t of e.rings){if(5!==t.length)return!1;if(t[0][0]!==t[1][0]||t[0][0]!==t[4][0]||t[2][0]!==t[3][0]||t[0][1]!==t[3][1]||t[0][1]!==t[4][1]||t[1][1]!==t[2][1])return!1}return!0}return!1}},17590:(e,t,s)=>{async function i(e,t){if(!e)return null;const s=t.featureAdapter,{startTimeField:i,endTimeField:r}=e;let n=Number.POSITIVE_INFINITY,a=Number.NEGATIVE_INFINITY;if(i&&r)await t.forEach((e=>{const t=s.getAttribute(e,i),u=s.getAttribute(e,r);null==t||isNaN(t)||(n=Math.min(n,t)),null==u||isNaN(u)||(a=Math.max(a,u))}));else{const e=i||r;await t.forEach((t=>{const i=s.getAttribute(t,e);null==i||isNaN(i)||(n=Math.min(n,i),a=Math.max(a,i))}))}return{start:n,end:a}}function r(e,t,s){if(!t||!e)return null;const{startTimeField:i,endTimeField:r}=e;if(!i&&!r)return null;const{start:n,end:a}=t;return null===n&&null===a?null:void 0===n&&void 0===a?()=>!1:i&&r?function(e,t,s,i,r){return null!=i&&null!=r?n=>{const a=e.getAttribute(n,t),u=e.getAttribute(n,s);return(null==a||a<=r)&&(null==u||u>=i)}:null!=i?t=>{const r=e.getAttribute(t,s);return null==r||r>=i}:null!=r?s=>{const i=e.getAttribute(s,t);return null==i||i<=r}:void 0}(s,i,r,n,a):function(e,t,s,i){return null!=s&&null!=i&&s===i?i=>e.getAttribute(i,t)===s:null!=s&&null!=i?r=>{const n=e.getAttribute(r,t);return n>=s&&n<=i}:null!=s?i=>e.getAttribute(i,t)>=s:null!=i?s=>e.getAttribute(s,t)<=i:void 0}(s,i||r,n,a)}s.d(t,{R:()=>i,y:()=>r})},99514:(e,t,s)=>{s.d(t,{Z:()=>a});var i=s(35671);function r(e){return"oid"===e.type||"esriFieldTypeOID"===e.type}function n(e){return"global-id"===e.type||"esriFieldTypeGlobalID"===e.type}class a{constructor(e=[]){if(this.fields=[],this._fieldsMap=new Map,this._normalizedFieldsMap=new Map,this._dateFieldsSet=new Set,this._numericFieldsSet=new Set,this.dateFields=[],this.numericFields=[],this._requiredFields=null,!e)return;this.fields=e;const t=[];for(const a of e){const e=a?.name,l=o(a?.name);if(e&&l){const o=u(e);this._fieldsMap.set(e,a),this._fieldsMap.set(o,a),this._normalizedFieldsMap.set(l,a),t.push(o),"date"===(s=a).type||"esriFieldTypeDate"===s.type?(this.dateFields.push(a),this._dateFieldsSet.add(a)):(0,i.H7)(a)&&(this._numericFieldsSet.add(a),this.numericFields.push(a)),r(a)||n(a)||(a.editable=null==a.editable||!!a.editable,a.nullable=null==a.nullable||!!a.nullable)}}var s;t.sort(),this.uid=t.join(",")}destroy(){this._fieldsMap.clear()}get requiredFields(){if(!this._requiredFields){this._requiredFields=[];for(const e of this.fields)r(e)||n(e)||e.nullable||void 0!==(0,i.os)(e)||this._requiredFields.push(e)}return this._requiredFields}has(e){return null!=this.get(e)}get(e){if(!e)return;let t=this._fieldsMap.get(e);return t||(t=this._fieldsMap.get(u(e))??this._normalizedFieldsMap.get(o(e)),t&&this._fieldsMap.set(e,t),t)}isDateField(e){return this._dateFieldsSet.has(this.get(e))}isNumericField(e){return this._numericFieldsSet.has(this.get(e))}normalizeFieldName(e){const t=this.get(e);if(t)return t.name??void 0}}function u(e){return e.trim().toLowerCase()}function o(e){return(0,i.q6)(e)?.toLowerCase()??""}}}]);