package org.thingsboard.server.dao.smartService.knowledge;

import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.smartService.knowledge.KnowledgeBase;

import java.util.List;

/**
 * 知识库
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-10-27
 */
public interface KnowledgeBaseService {
    PageData getList(String typeId, String title, String content, Long startTime, Long endTime, int page, int size, String tenantId);

    KnowledgeBase save(KnowledgeBase knowledgeBase);

    int delete(List<String> ids);
}
