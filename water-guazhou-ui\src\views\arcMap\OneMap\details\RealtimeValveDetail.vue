<template>
  <div class="one-map-detail">
    <div v-loading="state.detailLoading" class="row1">
      <FieldSet
        :size="'default'"
        :title="'阀门开度'"
        :type="'simple'"
        class="row-title"
      ></FieldSet>
      <div class="pie-charts">
        <div ref="refChartDiv" class="pie-chart">
          <VChart ref="refChart1" :option="state.pieChart1"></VChart>
        </div>
      </div>
    </div>
    <div class="row2">
      <div class="detail-attrgrou-radio">
        <el-radio-group v-model="state.curRadio" @change="refreshRealtimeList">
          <el-radio
            v-for="(item, i) in state.radioGroup"
            :key="i"
            :label="item"
          >
            {{ item }}
          </el-radio>
        </el-radio-group>
      </div>
      <div class="detail-right">
        <div v-loading="state.detailLoading" class="list-items overlay-y">
          <div
            v-for="(item, i) in state.stationRealTimeData"
            :key="i"
            class="list-item"
          >
            <div class="item-label">
              {{ item.propertyName }}
            </div>
            <div class="item-content">
              {{ item.value || '--' }} {{ item.unit }}
            </div>
          </div>
        </div>
        <div v-loading="state.detailLoading" class="chart-box">
          <VChart ref="refChart4" :option="state.lineChartOption"></VChart>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { GetValveStationDetail } from '@/api/mapservice/onemap';
import {
  GetStationAttrGroupNames,
  GetStationRealTimeDetail
} from '@/api/shuiwureports/zhandian';
import { useDetector } from '@/hooks/echarts';
import { useAppStore } from '@/store';
import { transNumberUnit } from '@/utils/GlobalHelper';
import { gaugeOption } from '../../echarts';

type LineItem = {
  data?: any[];
  unit?: string;
  name?: string;
  yAxisIndex?: number;
};
const hourlyLine = (config?: {
  line1: LineItem;
  line2?: LineItem;
  line3?: LineItem;
}) => {
  const option = {
    title: {
      text: '24小时运行曲线',
      left: '30',
      textStyle: {
        color: '#fff'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    grid: {
      left: 70,
      right: 90,
      bottom: 30,
      top: 70
    },
    legend: {
      textStyle: {
        color: useAppStore().isDark ? '#fff' : '#7C8295'
      }
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: [
        '00',
        '01',
        '02',
        '03',
        '04',
        '05',
        '06',
        '07',
        '08',
        '09',
        '10',
        '11',
        '12',
        '13',
        '14',
        '15',
        '16',
        '17',
        '18',
        '19',
        '20',
        '21',
        '22',
        '23'
      ]
    },
    yAxis: [
      {
        name: '压力(MPa)',
        type: 'value',
        // axisLabel: {
        //   formatter: '{value} °C'
        // },
        splitLine: {
          lineStyle: {
            color: '#ffffff',
            opacity: 0.2,
            type: 'dashed'
          }
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: '#666'
          }
        },
        position: 'left'
      },
      {
        name: '瞬时流量(m³/h)',
        type: 'value',
        // axisLabel: {
        //   formatter: '{value} °C'
        // },
        splitLine: {
          show: false,
          lineStyle: {
            color: '#ffffff',
            opacity: 0.2,
            type: 'dashed'
          }
        },
        position: 'right',
        alignTicks: true,
        axisLine: {
          show: true,
          lineStyle: {
            color: '#666'
          }
        }
      }
    ],
    series: [
      {
        name: config?.line1.name || '',
        type: 'line',
        data: config?.line1.data || [],
        markPoint: {
          data: [
            { type: 'max', name: 'Max' },
            { type: 'min', name: 'Min' }
          ]
        }
      },
      {
        yAxisIndex: 0,
        name: config?.line2?.name || '',
        type: 'line',
        data: config?.line2?.data || [],
        markPoint: {
          data: [
            { type: 'max', name: 'Max' },
            { type: 'min', name: 'Min' }
          ]
        }
      },
      {
        yAxisIndex: 1,
        name: config?.line3?.name || '',
        type: 'line',
        data: config?.line3?.data || [],
        markPoint: {
          data: [
            { type: 'max', name: 'Max' },
            { type: 'min', name: 'Min' }
          ]
        }
      }
    ]
  };
  return option;
};

const emit = defineEmits(['refresh', 'mounted']);
const { proxy }: any = getCurrentInstance();
const state = reactive<{
  curRow?: any;
  curRadio: string;
  radioGroup: any[];
  pieChart1: any;
  mapClick?: any;
  lineChartOption: any;
  stationRealTimeData: any[];
  detailLoading: boolean;
}>({
  curRadio: '',
  radioGroup: [],
  pieChart1: gaugeOption(0, { max: 5, title: '压力(Mpa)' }),
  lineChartOption: null,
  stationRealTimeData: [],
  detailLoading: false
});

const refreshDetail = async (row) => {
  emit('refresh', { title: row.name });
  state.detailLoading = true;
  state.curRow = row;
  try {
    const trans = (value) => {
      const val = transNumberUnit(value);
      return { value: +val.value.toFixed(2), unit: val.unit };
    };

    const p1 = GetValveStationDetail({
      stationId: row.stationId
    })
      .then((res) => {
        const pressure_front =
          res.data.data.pressure_front?.map((item) => item.value?.toFixed(2)) ||
          [];
        const pressure_backend =
          res.data.data.pressure_backend?.map((item) =>
            item.value?.toFixed(2)
          ) || [];
        const Instantaneous_flow =
          res.data.data.Instantaneous_flow?.map(
            (item) => item.value?.toFixed2
          ) || [];
        state.lineChartOption = hourlyLine({
          line1: {
            data: pressure_front,
            unit: 'MPa',
            name: '前端压力'
          },
          line2: {
            data: pressure_backend,
            unit: 'MPa',
            name: '后端压力',
            yAxisIndex: 0
          },
          line3: {
            data: Instantaneous_flow,
            unit: 'm³/h',
            name: '瞬时流量',
            yAxisIndex: 1
          }
        });
        proxy.$refs['refChart4']?.resize();
        const value1 = trans(res.data.data?.valveOpening || 0);
        state.pieChart1 = gaugeOption(value1.value, {
          max: 100,
          title: '开度(' + (value1.unit || '') + '%)'
        });
      })
      .finally(() => {
        resize();
      });

    const p2 = GetStationAttrGroupNames({ stationId: row.stationId }).then(
      (res) => {
        state.radioGroup = res.data || [];
        state.curRadio = state.radioGroup[0];
        refreshRealtimeList(state.radioGroup[0]);
      }
    );
    Promise.all([p1, p2]).finally(() => {
      state.detailLoading = false;
    });
  } catch (error) {
    state.detailLoading = false;
  }
};
const refreshRealtimeList = async (type) => {
  const real = await GetStationRealTimeDetail(state.curRow?.stationId, type);
  state.stationRealTimeData = real.data || [];
};
defineExpose({
  refreshDetail
});
const resize = () => {
  Array.from({ length: 2 }).map((item, i) => {
    proxy.$refs['refChart' + (i + 1)]?.resize();
  });
};
const resizer = useDetector();
const refChartDiv = ref<HTMLDivElement>();
onMounted(() => {
  emit('mounted');
  resizer.listenToMush(refChartDiv.value, resize);
});
</script>
<style lang="scss" scoped>
// .dark,
// .darkblue {
//   .one-map-detail {
//     .row1,
//     .row2 {
//       background-color: rgb(16, 39, 60);
//     }
//   }
// }
.one-map-detail {
  .row1 {
    background-color: var(--el-bg-color);
    height: 370px;
    align-items: center;
    padding: 8px 8px 0 8px;
    margin-bottom: 20px;

    .row-title {
      margin: 0;
    }

    .pie-charts {
      display: flex;
      height: 346px;
    }
  }

  .row2 {
    background-color: var(--el-bg-color);
    height: 480px;
  }

  .pie-chart {
    width: 33.33%;
    height: 260px;
  }
}

.detail-attrgrou-radio {
  // background-color: rgba(21, 45, 68, 1);
  padding: 0 12px;
  height: 40px;
  align-items: center;
  display: flex;
}

.detail-right {
  display: flex;
  width: 100%;
  height: calc(100% - 40px);

  .list-items {
    min-width: 260px;
    height: 100%;
    width: 260px;
    padding: 8px 12px;

    .list-item {
      width: 100%;
      height: 38px;
      color: var(--el-color-primary);
      font-size: 14px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border: 1px solid var(--el-color-primary);
      margin-bottom: 20px;
      padding: 8px;

      :last-child {
        margin-bottom: 0;
      }
    }
  }

  .chart-box {
    height: 100%;
    width: 100%;
  }
}
</style>
