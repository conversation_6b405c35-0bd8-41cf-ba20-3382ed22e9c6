<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartService.portal.SsPortalNewsMapper">
    <sql id="Base_Column_List">
        <!--@sql select-->
        id,
        title,
        summary,
        cover,
        content,
        active,
        is_recommend,
        is_hot,
        create_time,
        package_id,
        (select name from ss_portal_site_package where id = package_id) package_name,
        tenant_id
        <!--@sql from ss_portal_news -->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.smartService.portal.SsPortalNews">
        <result column="id" property="id"/>
        <result column="title" property="title"/>
        <result column="summary" property="summary"/>
        <result column="cover" property="cover"/>
        <result column="content" property="content"/>
        <result column="active" property="active"/>
        <result column="is_recommend" property="isRecommend"/>
        <result column="is_hot" property="isHot"/>
        <result column="package_id" property="packageId"/>
        <result column="package_name" property="packageName"/>
        <result column="create_time" property="createTime"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>
    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ss_portal_news
        <where>
            <if test="packageId != null and packageId != ''">
                and package_id = #{packageId}
            </if>
            <if test="title != null and title != ''">
                and title like '%'|| #{title} ||'%'
            </if>
            <if test="active != null">
                and active = #{active}
            </if>
            <if test="type != null">
                <choose>
                    <when test="type == 1">
                        and is_hot = true
                    </when>
                    <when test="type == 2">
                        and is_recommend = true
                    </when>
                </choose>
            </if>
            <if test="fromTime != null">
                and create_time >= #{fromTime}
            </if>
            <if test="toTime != null">
                and create_time &lt;= #{toTime}
            </if>
            and tenant_id = #{tenantId}
        </where>
        order by create_time desc
    </select>

    <update id="updateFully">
        update ss_portal_news
        set title        = #{title},
            summary      = #{summary},
            cover        = #{cover},
            content      = #{content},
            attachment   = #{attachment}
        where id = #{id}
    </update>

    <update id="active">
        update ss_portal_news
        set active = #{active}
        where id = #{id}
    </update>

    <update id="setHot">
        update ss_portal_news
        set is_hot = true
        where id = #{id}
    </update>

    <update id="setRecommend">
        update ss_portal_news
        set is_recommend = true
        where id = #{id}
    </update>

    <select id="canSave" resultType="boolean">
        select count(1) > 0
        from ss_portal_site_package
        where id = #{packageId}
    </select>
</mapper>