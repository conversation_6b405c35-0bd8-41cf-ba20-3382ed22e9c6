import{_ as v}from"./index-C9hz-UZb.js";import{d as x,c as u,r as b,l as n,bJ as C,bI as D,bH as y,o as k,g as w,h as W,F as S,q as _,i as h,aq as T,C as q}from"./index-r0dFAfgr.js";import{_ as I}from"./CardSearch-CB_HNR-Q.js";import"./index-0NlGN6gS.js";import{G as L}from"./index-DfMgLJKp.js";import"./Search-NSrhrIa_.js";const B=x({__name:"index",setup(F){const c=u(),s=u(),i=(e,o,t)=>t.hidden=e.type!==t.field,g=b({defaultParams:{grade:"1",type:"month",year:n().format(C),month:n().format(D),day:[n().format(y),n().format(y)]},filters:[{type:"select",field:"grade",label:"分区等级",clearable:!1,options:[{label:"一级分区",value:"1"},{label:"二级分区",value:"2"}]},{type:"radio-button",field:"type",options:[{label:"按年",value:"year"},{label:"按月",value:"month"},{label:"按时间段",value:"day"}],label:"选择方式"},{handleHidden:i,type:"year",label:"",field:"year",clearable:!1,disabledDate(e){return new Date<e}},{handleHidden:i,type:"month",label:"",field:"month",clearable:!1,disabledDate(e){return new Date<e}},{handleHidden:i,type:"daterange",label:"",field:"day",clearable:!1,disabledDate(e){return new Date<e}},{type:"btn-group",btns:[{perm:!0,text:"查询",iconifyIcon:"ep:search",click:()=>p()},{perm:!0,text:"重置",type:"default",iconifyIcon:"ep:refresh",click:()=>{var e;(e=s.value)==null||e.resetForm()}},{perm:!0,text:"导出",type:"success",iconifyIcon:"ep:download",click:()=>{var e;(e=c.value)==null||e.exportTable()}}]}]}),d=(e=[])=>[{prop:"title",label:"2022-10月大用户用水量报表",align:"center",subColumns:[{prop:"date",label:"日期",minWidth:120},...e,{prop:"totalSupplyWater",label:"供水量",minWidth:120},{prop:"totalUseWater",label:"大用户用水量",minWidth:120},{prop:"rate",label:"占比(%)",minWidth:120}]}],r=b({dataList:[],columns:d(),pagination:{hide:!0}}),p=async()=>{var e,o,t,l;r.loading=!0;try{const a=((e=s.value)==null?void 0:e.queryParams)||{},f=(await L({type:a.type,date:a.type==="year"?a.year:a.type==="month"?a.month:void 0,start:((o=a.day)==null?void 0:o[0])??void 0,end:((t=a.day)==null?void 0:t[1])??void 0,grade:a.grade})).data.data||{};r.dataList=f.dataList||[],r.columns=d((l=f.header)==null?void 0:l.map(m=>({label:m.label,prop:m.value,minWidth:160})))}catch{}r.loading=!1};return k(async()=>{await p()}),(e,o)=>{const t=I,l=T,a=v;return w(),W(a,{class:"wrapper-content",title:"",overlay:""},{default:S(()=>[_(t,{ref_key:"refSearch",ref:s,config:h(g)},null,8,["config"]),_(l,{ref_key:"refTable",ref:c,class:"card-table",config:h(r)},null,8,["config"])]),_:1})}}}),R=q(B,[["__scopeId","data-v-9c07bed8"]]);export{R as default};
