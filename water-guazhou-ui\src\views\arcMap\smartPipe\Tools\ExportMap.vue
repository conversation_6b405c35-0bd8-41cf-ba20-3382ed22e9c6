<!-- 地图导出 -->
<template>
  <RightDrawerMap
    ref="refMap"
    title="地图导出"
    :detail-max-min="true"
    :hide-detail-close="true"
    @map-loaded="onMaploaded"
  >
    <Form
      ref="refForm"
      :config="FormConfig"
    ></Form>
    <template #detail-header>
      <span>地图导出任务列表</span>
    </template>
    <template #detail-default>
      <InlineForm
        ref="refSearch"
        :config="SearchConfig"
      ></InlineForm>
      <div class="table-box">
        <FormTable :config="TableConfig"></FormTable>
      </div>
    </template>
  </RightDrawerMap>
</template>
<script lang="ts" setup>
import { Brush, Download, Refresh } from '@element-plus/icons-vue'
// import Graphic from '@arcgis/core/Graphic.js'
// import SketchViewModel from '@arcgis/core/widgets/Sketch/SketchViewModel.js'
import { EditPen } from '@element-plus/icons-vue'
import RightDrawerMap from '../../components/common/RightDrawerMap.vue'
import {
  // createRect,
  getGraphicLayer
  // setSymbol
} from '@/utils/MapHelper'
import { ExportMap, ListPrintMapTasks } from '@/api/mapservice/tools'
import { SLMessage } from '@/utils/Message'
import { downloadUrl } from '@/utils/fileHelper'
import { IFormIns, IInlineFormIns } from '@/components/type'
import { useSketch } from '@/hooks/arcgis'

const refSearch = ref<IInlineFormIns>()
const refForm = ref<IFormIns>()
const state = reactive<{
  loading: boolean
  timer?: any
  refreshCount: number
}>({
  loading: false,
  refreshCount: 0
})
const FormConfig = reactive<IFormConfig>({
  labelPosition: 'top',
  group: [
    {
      fieldset: {
        desc: '设置范围'
      },
      fields: [
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              svgIcon: shallowRef(EditPen),
              styles: {
                width: '100%'
              },
              text: '点击绘制范围',
              click: () => initDraw()
            },
            {
              perm: true,
              text: '清除',
              styles: {
                width: '100%'
              },
              svgIcon: shallowRef(Brush),
              type: 'default',
              click: () => clearDraw()
            }
          ]
        }
      ]
    },
    {
      fieldset: {
        desc: '比例尺'
      },
      fields: [
        {
          type: 'input-number',
          field: 'scale',
          // onChange: () => initDraw(),
          appendBtns: [
            {
              perm: true,
              iconifyIcon: 'ep:refresh',
              title: '更新比例尺',
              click: () => {
                if (!refForm.value || !staticState.view) return
                staticState.view.scale = refForm.value.dataForm.scale || 1128
                // refForm.value.dataForm.scale = staticState.view?.scale || 500
              }
            }
          ]
        }
      ]
    },
    {
      fieldset: {
        desc: '参数配置'
      },
      fields: [
        {
          type: 'input',
          field: 'taskname',
          label: '文件名称',
          rules: [{ required: true, message: '请输入文件名' }]
        },
        {
          type: 'select',
          field: 'exporttype',
          label: '文件格式',
          clearable: false,
          options: [
            { label: 'PDF', value: '0' }
            // { label: 'PNG32', value: 'PNG32' },
            // { label: 'PNG8', value: 'PNG8' },
            // { label: 'JPG', value: 'JPG' },
            // { label: 'GIF', value: 'GIF' },
            // { label: 'EPS', value: 'EPS' },
            // { label: 'SVG', value: 'SVG' },
            // { label: 'SVGZ', value: 'SVGZ' }
          ]
        },
        {
          type: 'select',
          field: 'papersize',
          label: '纸张大小',
          clearable: false,
          options: [
            { label: 'A0', value: '0' },
            { label: 'A1', value: '1' },
            { label: 'A2', value: '2' },
            { label: 'A3', value: '3' },
            { label: 'A4', value: '4' }
          ]
          // onChange: () => initDraw()
        },
        {
          type: 'radio',
          field: 'paperrotation',
          label: '纸张方向',
          options: [
            { label: '纵向', value: '0' },
            { label: '横向', value: '1' }
          ]
          // onChange: () => initDraw()
        },
        {
          type: 'number',
          field: 'resolution',
          label: '分辨率',
          controlPosition: 'right',
          rules: [{ required: true, message: '请输入分辨率' }]
        },
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: '导出',
              styles: {
                width: '100%'
              },
              loading: () => state.loading,
              // disabled: () => state.activeIndex < 2,
              svgIcon: shallowRef(Download),
              click: () => refForm.value?.Submit()
            },
            {
              perm: true,
              text: '重置',
              type: 'default',
              styles: {
                width: '100%'
              },
              svgIcon: shallowRef(Refresh),
              click: () => {
                clearDraw()
                resetForm()
              }
            }
          ]
        }
      ]
    }
  ],
  defaultValue: {
    scale: 500,
    exporttype: '0',
    papersize: '0',
    paperrotation: '0',
    resolution: 96
  },
  submit: async params => {
    if (!staticState.graphic) {
      SLMessage.warning('请先绘制范围')
      return
    }
    state.loading = true
    try {
      const res = await ExportMap({
        geomjson: JSON.stringify(staticState.graphic?.geometry.toJSON()),
        scale: params.scale,
        taskname: params.taskname,
        exporttype: params.exporttype,
        papersize: params.papersize,
        paperrotation: params.paperrotation,
        resolution: params.resolution
      })
      if (res.data.code === 10000) {
        SLMessage.success('任务已生成')
        refSearch.value && (refSearch.value.dataForm.taskname = params.taskname)
        clearTimeout(state.timer)
        state.refreshCount = 0
        await refreshData()
        await refreshRow(res.data.result)
      } else {
        SLMessage.error(res.data.message || '任务生成失败')
      }
    } catch (error: any) {
      console.log(error.message)

      SLMessage.error('任务生成失败')
    }

    state.loading = false
  }
})
const staticState: {
  view?: __esri.MapView
  drawer?: __esri.Draw
  drawAction?: __esri.DrawAction
  graphic?: __esri.Graphic
  graphicsLayer?: __esri.GraphicsLayer
  sketch?: __esri.SketchViewModel
} = {}
const TableConfig = reactive<ITable>({
  dataList: [],
  columns: [
    { label: '任务名称', prop: 'taskname' },
    // { label: '创建人', prop: 'createuser' },
    { label: '创建时间', prop: 'createtime' },
    // {
    //   label: '删除状态',
    //   prop: 'del_status',
    //   tag: true,
    //   tagColor: row => (row.del_status === '可用' ? '#00aa00' : '#aa0000')
    // },
    {
      label: '导出状态',
      prop: 'exp_status',
      tag: true,
      tagColor: row => (row.exp_status === '导出失败' ? '#aa0000' : row.exp_status === '导出成功' ? '#00aa00' : '#0000aa')
    }
  ],
  // operationWidth: 150,
  operations: [
    {
      perm: (row: any): boolean => row.exp_status === '导出成功',
      text: '下载',
      svgIcon: shallowRef(Download),
      disabled: row => row.exp_status !== '导出成功',
      click: row => handleDownload(row)
    },

    {
      perm: (row: any): boolean => row.exp_status !== '导出成功',
      text: '刷新',
      loading: (row: any): boolean => row.exp_status === '导出中',
      svgIcon: shallowRef(Refresh),
      click: row => {
        clearTimeout(state.timer)
        state.refreshCount = 0
        refreshRow(row.task_id)
      }
    }
    // {
    //   perm: true,
    //   text: '使用',
    //   type: 'success',
    //   svgIcon: shallowRef(Pointer),
    //   click: () => handleUseTask()
    // },
    // {
    //   perm: false,
    //   text: '删除',
    //   type: 'danger',
    //   disabled: row => row.del_status !== '可用',
    //   svgIcon: shallowRef(Delete),
    //   click: () => handleDelete()
    // }
  ],
  pagination: {
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page
      TableConfig.pagination.limit = size
      refreshData()
    }
  }
})

const refreshRow = async task_id => {
  if (!task_id) return
  clearTimeout(state.timer)
  const row = TableConfig.dataList.find(item => item.task_id === task_id)
  try {
    const res = await ListPrintMapTasks({
      pageindex: 1,
      pagesize: 1,
      exporttype: '0',
      taskid: row.task_id
    })
    if (res.data.result.rows.length) {
      const resRow = res.data.result.rows[0]
      row.exp_status = resRow.exp_status
      row.del_status = resRow.del_status
      if (row.exp_status === '导出中') {
        state.timer = setTimeout(() => {
          refreshRow(task_id)
        }, 1000)
      } else if (row.exp_status === '未开始') {
        if (state.refreshCount < 10) {
          state.timer = setTimeout(() => {
            state.refreshCount++
            refreshRow(task_id)
          }, 1000)
        }
      }
    }
    return row
  } catch (error) {
    row.exp_status = '导出失败'
    return row
  }
}
const handleDownload = row => {
  downloadUrl(window.SITE_CONFIG.GIS_CONFIG.gisApi + '/ExportResult/' + row.task_id + '.pdf', row.taskname)
}
const SearchConfig = reactive<IFormConfig>({
  labelPosition: 'right',
  group: [
    {
      fields: [
        {
          labelWidth: 60,
          type: 'input',
          label: '任务名',
          field: 'taskname',
          itemContainerStyle: { marginBottom: '8px' }
        },
        {
          itemContainerStyle: { marginBottom: '8px' },
          type: 'btn-group',
          btns: [{ perm: true, text: '搜索', click: () => refreshData() }]
        }
      ]
    }
  ]
})
const refreshData = async () => {
  try {
    const res = await ListPrintMapTasks({
      pageindex: TableConfig.pagination.page || 1,
      pagesize: TableConfig.pagination.limit || 20,
      taskname: refSearch.value?.dataForm.taskname,
      taskid: refSearch.value?.dataForm.taskid
    })
    TableConfig.dataList = res.data.result?.rows || []
    TableConfig.pagination.total = res.data.result.total || 0
  } catch (error: any) {
    console.log(error.message)
  }
}
const clearDraw = () => {
  staticState.graphicsLayer?.removeAll()
  staticState.graphic = undefined
}
const initDraw = () => {
  clearDraw()
  staticState.graphicsLayer?.removeAll()
  staticState.sketch?.create('rectangle')
  // staticState.graphic = generateGraphic()
  // staticState.graphicsLayer?.add(staticState.graphic)
  // staticState.view?.goTo(staticState.graphic)
}

const resetForm = () => {
  clearDraw()
  refForm.value?.resetForm()
}
const refMap = ref<InstanceType<typeof RightDrawerMap>>()
const { initSketch, destroySketch } = useSketch()
// const initSketch = () => {
//   staticState.graphicsLayer = getGraphicLayer(staticState.view, {
//     id: 'export-map',
//     title: '地图导出'
//   })
//   staticState.sketch = new SketchViewModel({
//     view: staticState.view,
//     layer: staticState.graphicsLayer,
//     defaultCreateOptions: {
//       mode: 'freehand'
//     },
//     defaultUpdateOptions: {
//       tool: 'move'
//     },

//     polygonSymbol: setSymbol('polygon', {
//       color: [255, 0, 0, 0],
//       outlineColor: [255, 0, 0, 1],
//       outlineWidth: 1
//     }) as any
//   })
//   staticState.sketch?.on('create', (result: any) => {
//     if (result.state === 'complete') {
//       staticState.graphic = result.graphic
//     }
//   })
//   staticState.sketch?.on('update', (result: any) => {
//     if (result.state === 'complete') {
//       console.log(result)

//       staticState.graphic = result.graphics[0]
//     }
//   })
// }
// const generateGraphic = (): __esri.Graphic => {
//   const PaperSpects = {
//     A0: [841, 1189],
//     A1: [594, 841],
//     A2: [420, 594],
//     A3: [297, 420],
//     A4: [210, 297]
//   }

//   const paperrotation = refForm.value?.dataForm.paperrotation || '0'
//   const papersize = refForm.value?.dataForm.papersize || '0'
//   const scale = refForm.value?.dataForm.scale || 500
//   const width = paperrotation === '0'
//     ? PaperSpects['A' + papersize][0]
//     : PaperSpects['A' + papersize][1]
//   const height = paperrotation === '0'
//     ? PaperSpects['A' + papersize][1]
//     : PaperSpects['A' + papersize][0]
//   const rect = CalcRect(width, height, scale)
//   return new Graphic({
//     geometry: rect,
//     symbol: setSymbol('polygon')
//   })
// }
/**
 * 计算纸张的矩形
 * @param center 地图中心屏幕坐标
 * @param width 纸张宽度（mm)
 * @param height 纸张长度（mm)
 */
// const CalcRect = (width?: number, height?: number, scale?: number) => {
//   if (width === undefined || height === undefined || !staticState.view) {
//     return
//   }
//   const radio = 1 // 投影坐标系下值取1,地理坐标系下为:180 / (Math.PI * 6378137);//实际距离换算到地图距离的参数
//   width = ((width * (scale || 500)) / 1000) * radio
//   height = ((height * (scale || 500)) / 1000) * radio
//   const mapCenter = staticState.view?.center
//   const vertices: number[][] = []
//   vertices.push([mapCenter.x - width / 2, mapCenter.y - height / 2])
//   vertices.push([mapCenter.x + width / 2, mapCenter.y + height / 2])
//   const rect = createRect(vertices, staticState.view?.spatialReference)
//   return rect
// }
const resolveDrawEnd = (result: ISketchHandlerParameter) => {
  if (result.state === 'complete') {
    staticState.graphic = result.graphics[0]
  }
}
const onMaploaded = (view: __esri.MapView) => {
  staticState.view = view
  staticState.graphicsLayer = getGraphicLayer(staticState.view, {
    id: 'export-map',
    title: '地图导出'
  })
  staticState.sketch = initSketch(staticState.view, staticState.graphicsLayer, {
    updateCallBack: resolveDrawEnd,
    createCallBack: resolveDrawEnd
  })
  staticState.view.watch('stationary', () => {
    if (!refForm.value) return
    refForm.value.dataForm.scale = staticState.view?.scale || 500
  })
  refMap.value?.toggleCustomDetail(true)
  resetForm()
}
onMounted(() => {
  refreshData()
})
onBeforeUnmount(() => {
  staticState.sketch?.cancel()
  staticState.sketch?.destroy()
  staticState.drawAction?.destroy()
  staticState.drawer?.destroy()
  staticState.graphicsLayer?.removeAll()
  destroySketch()
})
</script>
<style lang="scss" scoped>
.table-box {
  height: calc(100% - 40px);
}
</style>
