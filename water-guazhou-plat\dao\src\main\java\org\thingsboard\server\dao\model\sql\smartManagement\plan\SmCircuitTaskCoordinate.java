package org.thingsboard.server.dao.model.sql.smartManagement.plan;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseUsername;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.util.Date;

@Getter
@Setter
@ResponseEntity
@TableName("sm_circuit_task_coordinate")
public class SmCircuitTaskCoordinate {
    // id
    private String id;

    // 任务code
    private String taskCode;

    // 坐标x
    private Float x;

    // 坐标y
    private Float y;

    // 创建人id
    @ParseUsername
    private String creator;

    // 创建时间/上报时间
    private Date createTime;

    // 租户Id
    private String tenantId;

}
