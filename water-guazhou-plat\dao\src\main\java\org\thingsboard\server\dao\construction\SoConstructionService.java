package org.thingsboard.server.dao.construction;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.smartOperation.ConstructionWorkflow;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstruction;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionCompositeProject;
import org.thingsboard.server.dao.model.sql.smartOperation.device.SoDeviceItem;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionSaveRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoDeviceItemPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoDeviceItemSaveRequest;

import java.util.List;

public interface SoConstructionService {
    /**
     * 获取自动生成的编号
     *
     * @param tenantId 客户id
     * @return 自动生成的编码
     */
    String generateCode(String tenantId);

    /**
     * 分页条件查询工程
     *
     * @param request 分页查询条件
     * @return 分页查询结果
     */
    IPage<SoConstruction> findAllConditional(SoConstructionPageRequest request);

    /**
     * 保存工程
     *
     * @param entity 实体信息
     * @return 保存好的实体
     */
    SoConstruction save(SoConstructionSaveRequest entity);

    /**
     * 编号是否已存在
     *
     * @param code     编号
     * @param tenantId 客户id
     * @param id       自身id（更新时不为null）
     * @return 是否已存在
     */
    boolean isCodeExists(String code, String tenantId, String id);

    /**
     * 增量修改
     *
     * @param entity 实体信息
     * @return 是否修改成功
     */
    boolean update(SoConstruction entity);

    /**
     * 删除
     *
     * @param id 唯一标识
     * @return 是否删除成功
     */
    boolean delete(String id);

    /**
     * 分页条件查询设备项
     *
     * @param request 分页请求
     * @return 设备项
     */
    IPage<SoDeviceItem> getDevices(SoDeviceItemPageRequest request);

    /**
     * 保存设备项
     *
     * @param request 明细
     * @return 保存好的设备项
     */
    List<SoDeviceItem> saveDevice(List<SoDeviceItemSaveRequest> request);

    /**
     * 工程的完成信息
     *
     * @param constructionCode 编号
     * @param tenantId         客户id
     * @return 完成信息
     */
    List<ConstructionWorkflow> completionInfo(String constructionCode, String tenantId);

    /**
     * 获取指定项目下的所有工程编号
     *
     * @param projectCode 项目编号
     * @param tenantId    客户id
     * @return 工程编号集合
     */
    List<String> getAllCodeByProject(String projectCode, String tenantId);

    /**
     * 通过工程编号获取工程和项目
     * @param constructionCode 工程编号
     * @param tenantId 客户id
     * @return 工程和相关项目
     */
    SoConstructionCompositeProject getCompositeByConstructionCode(String constructionCode, String tenantId);

    /**
     * 是否允许被删除
     * @param id 唯一标识
     * @return 是否允许
     */
    boolean canBeDelete(String id);

}
