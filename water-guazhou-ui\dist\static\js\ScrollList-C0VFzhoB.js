import{_ as r}from"./index-BlG8PIOK.js";import{d as p,g as a,n as e,p as s,q as m,F as u,aB as f,aJ as x,bh as o,C as h}from"./index-r0dFAfgr.js";const v={class:"card"},j={class:"item_view"},g={class:"jcdfx-item__child jcdfx-item__desc"},B={class:"jcdfx-item__child jcdfx-item__value"},S={class:"jcdfx-item__child jcdfx-item__date"},k=p({__name:"ScrollList",props:{data:{}},setup(l){const c=l,_={step:.2,limitMoveNum:5};return(w,n)=>{const d=r;return a(),e("div",v,[n[0]||(n[0]=s("div",{class:"title"},[s("span",null,"指标项"),s("span",null,"年度累计值"),s("span",null,"同比")],-1)),m(d,{data:c.data,"class-option":_,class:"warp"},{default:u(()=>[s("ul",j,[(a(!0),e(f,null,x(c.data,(t,i)=>(a(),e("li",{key:i,class:"title"},[s("span",g,o(t.name),1),s("span",B,o(t.value),1),s("span",S,o(t.scale),1)]))),128))])]),_:1},8,["data"])])}}}),N=h(k,[["__scopeId","data-v-5d2af4f8"]]);export{N as default};
