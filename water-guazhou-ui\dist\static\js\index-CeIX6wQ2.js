import{d as Q,j as X,c as S,r as W,b as Z,ay as tt,g as j,h as ot,F as I,p as l,bh as L,i as p,G as y,q as v,bo as it,n as at,bB as q,bt as et,aq as rt,br as st,C as nt}from"./index-r0dFAfgr.js";import{w as pt}from"./Point-WxyopZva.js";import{g as mt,n as lt}from"./MapView-DaoQedLH.js";import{b as M,p as dt,l as ct}from"./echart-BHaUp-st.js";import{u as ut}from"./useStation-DJgnSZIA.js";import{d as ft}from"./zhandian-YaGuQZe6.js";import{e as ht,a as gt}from"./monitoringOverview-DvKhtmcR.js";import bt from"./RightDrawerMap-D5PhmGFO.js";import{g as z}from"./FeatureHelper-Da16o0mu.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./project-DUuzYgGl.js";import{b as wt}from"./ViewHelper-BGCZjxXH.js";import{g as _t}from"./URLHelper-B9aplt5w.js";import"./widget-BcWKanF2.js";import"./ArcView-DpMnCY82.js";import"./geometryEngineBase-BhsKaODW.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./ArcStationWarning-BF9YrSzF.js";/* empty css                         */import"./useWaterPoint-Bv0z6ym6.js";import"./TileLayer-B5vQ99gG.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./DateFormatter-Bm9a68Ax.js";import"./DrawerBox-CLde5xC8.js";import"./SideDrawer-CBntChyn.js";import"./PipeDetail-CTBPYFJW.js";import"./Search-NSrhrIa_.js";import"./FormTableColumnFilter-BT7pLXIC.js";import"./fieldconfig-Bk3o1wi7.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./LayerHelper-Cn-iiqxI.js";import"./pipe-nogVzCHG.js";import"./QueryHelper-ILO3qZqg.js";import"./config-fy91bijz.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./ArcBRTools-BO92yznB.js";import"./PipeLength.vue_vue_type_script_setup_true_lang-BZfUsvDf.js";import"./arcWidgetButton-0glIxrt7.js";import"./StatisticsHelper-D-s_6AyQ.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./wfsUtils-DXofo3da.js";import"./usePipelineGroup-Ba1pmWz_.js";import"./GroupLayer-DhhN3FyN.js";import"./lazyLayerLoader-DbM9sT1W.js";import"./WFSLayer-1TtJp3nx.js";import"./clientSideDefaults-VQhQaYxh.js";import"./QueryEngineCapabilities-Dk3NJwmm.js";import"./wfsUtils-Du031XaC.js";import"./geojson-MBFu2-HZ.js";import"./xmlUtils-CtUoQO7q.js";import"./ListWindow-WS05QqV0.js";import"./PopLayout-BP55MvL7.js";import"./PoiSearchV2-D7yNeLuv.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./useLayerList-DmEwJ-ws.js";import"./useScaleBar-Beed-z91.js";const R=["#49de35","#4acffb","#f44c04","#A524DE","#FF7F40"],vt={class:"content"},yt={class:"right-box"},kt={class:"right-box bottom"},Ot={class:"title"},Dt={class:"bottom-content"},xt={class:"bottom-box"},Ct={class:"top"},St={class:"value"},Lt={class:"bottom-box"},Rt={class:"top"},Bt={class:"value"},Ft={ref:"refBottom",class:"bottom-box"},Tt={class:"top"},Wt={class:"value"},It=Q({__name:"index",setup(Mt){const D=X(),E=S(),B=S(),{getAllStationOption:P}=ut(),F=S(),T=S(!1);let h=W([]);const a=W({pieOption1:null,barOption1:null,pieOption2:null,barOption2:null,pieOption3:null,barOption3:null,lineOption:null,curRow:{},data:null,stationLocation:[],windows:[]}),c={},x=async i=>{var f,g,b,w,_,C;const o=u.dataList.find(d=>d.stationId===i);a.curRow=o,U(o);let t;if(i?(t=(b=c.view)==null?void 0:b.graphics.find(d=>{var k,O;return((O=(k=d.attributes)==null?void 0:k.row)==null?void 0:O.id)===i}),t&&await z(c.view,t,{zoom:15,avoidHighlight:!0})):t=(g=(f=c.view)==null?void 0:f.graphics)==null?void 0:g.getItemAt(0),!t)return;const s=((w=t.attributes)==null?void 0:w.row)||{},e=((_=(await ft(s.id)).data)==null?void 0:_.map(d=>(d.label=d.propertyName,d.value,d)))||[],m=t==null?void 0:t.geometry;a.windows.length=0,a.windows.push({visible:!1,x:m.x,y:m.y,offsetY:-30,title:s.name,attributes:{values:e,id:s.id}}),await q(),(C=B.value)==null||C.openPop(s.id)},u=W({loading:!0,dataList:[],columns:[{prop:"name",label:"名称"},{prop:"todayWaterSupply",label:"今日供水量",minWidth:95,unit:"(m³)"},{prop:"yesterdayWaterSupply",label:"昨日供水量",minWidth:95,unit:"(m³)"},{prop:"monthWaterSupply",label:"本月供水量",minWidth:95,unit:"(m³)"}],highlightCurrentRow:!0,currentRowKey:"stationId",handleRowClick:async i=>{var t;a.curRow=i;const o=(t=c.view)==null?void 0:t.graphics.find(s=>{var r,e;return((e=(r=s.attributes)==null?void 0:r.row)==null?void 0:e.id)===i.stationId});o?await z(c.view,o,{zoom:15,avoidHighlight:!0}):Z.warning("该站点暂无位置信息"),x(i.stationId)},pagination:{page:1,limit:20,total:0,layout:"total, sizes, jumper",refreshData:({page:i,size:o})=>{u.pagination.page=i,u.pagination.limit=o,u.dataList=h.slice((i-1)*o,i*o)}}}),K=()=>{gt().then(i=>{var o,t;h=(o=i.data)==null?void 0:o.data,u.dataList=h==null?void 0:h.slice(0,20),u.pagination.total=h.length,u.currentRow=h[0],u.loading=!1,x((t=h[0])==null?void 0:t.stationId)}),Y()},U=async i=>{var s;if(!i)return;T.value=!0,u.currentRow=i;const t=(await ht(i.stationId||i.id)).data.data;(s=F.value)==null||s.clear(),q(()=>{var d,k,O,A,V,N,G,H,$;const r=(d=t.todayTotalFlowDataList)==null?void 0:d.map(n=>n.ts.substring(8,20)),e=(k=t.todayTotalFlowDataList)==null?void 0:k.map(n=>n.value);a.barOption1=M("",R[0],r,e,70,70),a.pieOption1=i.todayWaterSupply;const m=(O=t.yesterdayTotalFlowDataList)==null?void 0:O.map(n=>n.ts.substring(8,20)),f=(A=t.yesterdayTotalFlowDataList)==null?void 0:A.map(n=>n.value);a.pieOption2=dt("昨日供水量",R[1],i.yesterdayWaterSupply),a.barOption2=M("",R[1],m,f,70,70),a.pieOption2=i.yesterdayWaterSupply;const g=(V=t.monthTotalFlowDataList)==null?void 0:V.map(n=>n.ts.substring(8,20)),b=(N=t.monthTotalFlowDataList)==null?void 0:N.map(n=>n.value);a.barOption3=M("",R[2],g,b,70,70),a.pieOption3=i.monthWaterSupply;const w=t.todayTotalFlowDataList.map(n=>n.ts),_=(G=t.pressure)==null?void 0:G.map(n=>n.value),C=(H=t.Instantaneous_flow)==null?void 0:H.map(n=>n.value);a.lineOption=ct(w,_,C,50,50),($=F.value)==null||$.resize()}),T.value=!1},Y=async()=>{var o,t;const i=await P("泵站");(t=(o=c.view)==null?void 0:o.graphics)==null||t.removeAll(),i.map(s=>{var g,b,w,_;const r=s.data,e=(g=r==null?void 0:r.location)==null?void 0:g.split(",");if((e==null?void 0:e.length)!==2)return;const m=new pt({longitude:e==null?void 0:e[0],latitude:e==null?void 0:e[1],spatialReference:(b=c.view)==null?void 0:b.spatialReference}),f=new mt({geometry:m,symbol:new lt({width:25,height:30,yoffset:15,url:_t("泵站.png")}),attributes:{row:r}});(_=(w=c.view)==null?void 0:w.graphics)==null||_.add(f)}),x()},J=i=>{var o;c.view=i,(o=B.value)==null||o.toggleCustomDetail(!0),K(),wt(c.view,t=>{var r,e,m;const s=(r=t.results)==null?void 0:r[0];if(s&&s.type==="graphic"){const f=(m=(e=s.graphic)==null?void 0:e.attributes)==null?void 0:m.row;x(f==null?void 0:f.id)}})};return(i,o)=>{const t=et,s=rt,r=tt("VChart"),e=st;return j(),ot(bt,{ref_key:"refMap",ref:B,title:"泵站总览",windows:p(a).windows,"hide-detail-close":!0,"hide-layer-list":!0,"right-drawer-width":600,onMapLoaded:J},{"detail-header":I(()=>{var m;return[l("span",Ot,"泵站总览-"+L((m=p(a).curRow)==null?void 0:m.name),1)]}),"detail-default":I(()=>[l("div",Dt,[l("div",xt,[l("div",Ct,[o[0]||(o[0]=y(" 今日供水量")),l("span",St,L(p(a).pieOption1),1),o[1]||(o[1]=y(" m³ "))]),v(r,{ref:"refChart2",class:"bottom-chart-box",theme:p(D).isDark?"blackBackground":"whiteBackground",option:p(a).barOption1},null,8,["theme","option"])]),l("div",Lt,[l("div",Rt,[o[2]||(o[2]=y(" 昨日供水量")),l("span",Bt,L(p(a).pieOption2),1),o[3]||(o[3]=y(" m³ "))]),v(r,{ref:"refChart3",class:"bottom-chart-box",theme:p(D).isDark?"blackBackground":"whiteBackground",option:p(a).barOption2},null,8,["theme","option"])]),l("div",Ft,[l("div",Tt,[o[4]||(o[4]=y(" 本月供水量")),l("span",Wt,L(p(a).pieOption3),1),o[5]||(o[5]=y(" m³ "))]),v(r,{ref:"refChart4",class:"bottom-chart-box",theme:p(D).isDark?"blackBackground":"whiteBackground",option:p(a).barOption3},null,8,["theme","option"])],512)])]),default:I(()=>{var m;return[l("div",vt,[v(t,{type:"underline",title:"二供泵房监测"}),l("div",yt,[v(s,{ref_key:"refCard",ref:E,class:"table-box",config:p(u)},null,8,["config"])]),v(t,{type:"underline",title:(((m=p(a).curRow)==null?void 0:m.name)||"")+"24小时运行曲线"},null,8,["title"]),it((j(),at("div",kt,[v(r,{ref_key:"refChart",ref:F,option:p(a).lineOption,theme:p(D).isDark?"blackBackground":"whiteBackground"},null,8,["option","theme"])])),[[e,p(T)]])])]}),_:1},8,["windows"])}}}),$i=nt(It,[["__scopeId","data-v-97583c8f"]]);export{$i as default};
