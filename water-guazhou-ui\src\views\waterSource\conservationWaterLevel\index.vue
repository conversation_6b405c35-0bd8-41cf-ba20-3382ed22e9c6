<template>
  <div class="wrapper">
    <CardSearch
      ref="refSearch"
      :config="searchConfig"
    />
    <CardTable
      :config="tableConfig"
      class="card-table"
    />



    <!-- 新增/编辑对话框 -->
    <WaterLevelDialog
      v-model:visible="dialogVisible"
      :form-data="formData"
      :dialog-type="dialogType"
      @confirm="handleDialogConfirm"
    />

    <!-- 导入对话框 -->
    <ImportDialog
      v-model:visible="importVisible"
      @confirm="handleImportConfirm"
    />

    <!-- 智能分析对话框 -->
    <AnalysisDialog
      v-model:visible="analysisVisible"
      @confirm="handleAnalysisConfirm"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, shallowRef } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Plus, Upload, Download, DataAnalysis, View, Edit, Delete, Refresh } from '@element-plus/icons-vue'
import { ICONS } from '@/common/constans/common'
import {
  getWaterLevelList,
  deleteWaterLevel,
  type ConservationWaterLevel,
  type WaterLevelQueryParams
} from '@/api/waterSource/conservationWaterLevel'
import { getAllStations } from '@/api/station'
import CardSearch from '@/components/Form/CardSearch.vue'
import CardTable from '@/components/Form/CardTable.vue'
import WaterLevelDialog from './components/WaterLevelDialog.vue'
import ImportDialog from './components/ImportDialog.vue'
import AnalysisDialog from './components/AnalysisDialog.vue'
import { formatDate } from '@/utils/DateFormatter'
import { dwnai } from '@/utils/processNumber'


// 格式化函数
const formatDateTime = (date: string | number | Date | undefined) => {
  return formatDate(date, 'YYYY-MM-DD HH:mm:ss')
}

const formatNumber = (value: number | string | undefined, precision = 2) => {
  if (value === undefined || value === null || value === '') return '--'
  return dwnai(value)
}

// 响应式数据
const loading = ref(false)
const waterLevelList = ref<ConservationWaterLevel[]>([])
const total = ref(0)
const dialogVisible = ref(false)
const importVisible = ref(false)
const analysisVisible = ref(false)
const dialogType = ref<'add' | 'edit' | 'view'>('add')
const formData = ref<ConservationWaterLevel>({} as ConservationWaterLevel)

// 站点数据
const stationsMap = ref<Map<string, any>>(new Map())
const stationList = ref<any[]>([])

// 查询表单引用
const refSearch = ref<ICardSearchIns>()

// 搜索配置
const searchConfig = ref<ISearch>({
  labelWidth: '100px',
  filters: [
    {
      type: 'select',
      label: '测点名称',
      field: 'stationId',
      options: computed(() => [
        { label: '全部测点', value: '' },
        ...stationList.value.map(station => ({
          label: station.name,
          value: station.id
        }))
      ]) as any
    },
    {
      type: 'select',
      label: '数据来源',
      field: 'dataSource',
      options: [
        { label: '手动录入', value: 1 },
        { label: '设备采集', value: 2 }
      ]
    },
    {
      type: 'daterange',
      label: '记录时间',
      field: 'recordTime'
    }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        { type: 'primary', perm: true, text: '查询', icon: ICONS.QUERY, click: () => refreshData() },
        { type: 'default', perm: true, text: '重置', svgIcon: shallowRef(Refresh), click: () => resetQuery() },
        { type: 'primary', perm: true, text: '新增数据', svgIcon: shallowRef(Plus), click: () => handleAdd() },
        // { type: 'success', perm: true, text: '批量导入', svgIcon: shallowRef(Upload), click: () => handleImport() },
        // { type: 'info', perm: true, text: '导出数据', svgIcon: shallowRef(Download), click: () => handleExport() },
        { type: 'warning', perm: true, text: '智能分析', svgIcon: shallowRef(DataAnalysis), click: () => handleAnalysis() }
      ]
    }
  ]
})

// 表格配置
const tableConfig = reactive<ICardTable>({
  title: '涵养水位数据列表',
  columns: [
    {
      label: '测点名称',
      prop: 'stationName',
      minWidth: 140,
      formatter: (row: ConservationWaterLevel) => getStationName(row.stationId)
    },
    {
      label: '测点位置',
      prop: 'stationLocation',
      minWidth: 180,
      showOverflowTooltip: true,
      formatter: (row: ConservationWaterLevel) => getStationLocation(row.stationId)
    },
    {
      label: '原水液位(m)',
      prop: 'rawWaterLevel',
      minWidth: 130,
      align: 'center',
      formatter: (row: ConservationWaterLevel) => formatNumber(row.rawWaterLevel)
    },
    {
      label: '地下水位(m)',
      prop: 'groundwaterLevel',
      minWidth: 130,
      align: 'center',
      formatter: (row: ConservationWaterLevel) => formatNumber(row.groundwaterLevel)
    },
    {
      label: '液位变化(m)',
      prop: 'levelChange',
      minWidth: 130,
      align: 'center',
      formatter: (row: ConservationWaterLevel) => formatNumber(row.levelChange)
    },
    {
      label: '降雨量(mm)',
      prop: 'rainfallAmount',
      minWidth: 120,
      align: 'center',
      formatter: (row: ConservationWaterLevel) => formatNumber(row.rainfallAmount)
    },
    {
      label: '蒸发量(mm)',
      prop: 'evaporationAmount',
      minWidth: 120,
      align: 'center',
      formatter: (row: ConservationWaterLevel) => formatNumber(row.evaporationAmount)
    },
    {
      label: '开采量(m³)',
      prop: 'extractionAmount',
      minWidth: 130,
      align: 'center',
      formatter: (row: ConservationWaterLevel) => formatNumber(row.extractionAmount)
    },
    {
      label: '数据来源',
      prop: 'dataSource',
      minWidth: 110,
      align: 'center',
      formatter: (row: ConservationWaterLevel) => row.dataSource === 1 ? '手动录入' : '设备采集'
    },
    {
      label: '记录时间',
      prop: 'recordTime',
      minWidth: 170,
      align: 'center',
      formatter: (row: ConservationWaterLevel) => formatDateTime(row.recordTime)
    },
    {
      label: '创建人',
      prop: 'creatorName',
      minWidth: 100,
      align: 'center',
      formatter: (row: ConservationWaterLevel) => row.creatorName || row.creator || '--'
    }
  ],
  dataList: waterLevelList as any,
  operations: [
    {
      perm: true,
      type: 'primary',
      isTextBtn: true,
      text: '查看',
      icon: 'iconfont icon-chakan',
      click: (row: ConservationWaterLevel) => handleView(row)
    },
    {
      perm: true,
      type: 'success',
      isTextBtn: true,
      text: '编辑',
      icon: 'iconfont icon-bianji',
      click: (row: ConservationWaterLevel) => handleEdit(row)
    },
    {
      perm: true,
      type: 'danger',
      isTextBtn: true,
      text: '删除',
      icon: 'iconfont icon-shanchu',
      click: (row: ConservationWaterLevel) => handleDelete(row)
    }
  ],
  pagination: {
    total: total.value,
    page: 1,
    limit: 10,
    handlePage: (page: number) => {
      if (tableConfig.pagination) {
        tableConfig.pagination.page = page
        refreshData()
      }
    },
    handleSize: (size: number) => {
      if (tableConfig.pagination) {
        tableConfig.pagination.limit = size
        refreshData()
      }
    }
  }
})

// 获取液位变化样式类
const getLevelChangeClass = (levelChange: number | undefined) => {
  if (!levelChange) return ''
  if (levelChange > 0) return 'level-increase'
  if (levelChange < 0) return 'level-decrease'
  return ''
}

// 获取站点名称
const getStationName = (stationId: string) => {
  const station = stationsMap.value.get(stationId)
  return station?.name || stationId || '--'
}

// 获取站点位置
const getStationLocation = (stationId: string) => {
  const station = stationsMap.value.get(stationId)
  return station?.location || '--'
}

// 生命周期
onMounted(() => {
  loadStations()
  refreshData()
})

// 加载站点数据
const loadStations = async () => {
  try {
    const response = await getAllStations({ type: '水源地' })
    if (response.status === 200) {
      // 后端返回的是PageData格式，数据在data字段中
      const stations = response.data.data || []
      stationsMap.value.clear()
      stationList.value = stations
      stations.forEach((station: any) => {
        stationsMap.value.set(station.id, station)
      })
    }
  } catch (error) {
    console.error('获取站点数据失败:', error)
  }
}

// 获取列表数据
const refreshData = async () => {
  loading.value = true
  try {
    const query = refSearch.value?.queryParams || {}
    const params: WaterLevelQueryParams = {
      pageNum: tableConfig.pagination?.page || 1,
      pageSize: tableConfig.pagination?.limit || 10,
      stationId: query.stationId || '',
      dataSource: query.dataSource,
      startTime: query.recordTime?.[0],
      endTime: query.recordTime?.[1]
    }
    const response = await getWaterLevelList(params)
    if (response.data.code === 200) {
      waterLevelList.value = response.data.data.list
      total.value = response.data.data.total
      if (tableConfig.pagination) {
        tableConfig.pagination.total = response.data.data.total
      }
    }
  } catch (error) {
    console.error('获取涵养水位数据失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 重置查询
const resetQuery = () => {
  refSearch.value?.resetForm()
  tableConfig.pagination.page = 1
  refreshData()
}

// 新增
const handleAdd = () => {
  dialogType.value = 'add'
  formData.value = {
    dataSource: 1 // 默认手动录入
  } as ConservationWaterLevel
  dialogVisible.value = true
}

// 编辑
const handleEdit = (row: ConservationWaterLevel) => {
  dialogType.value = 'edit'
  formData.value = { ...row }
  dialogVisible.value = true
}

// 查看
const handleView = (row: ConservationWaterLevel) => {
  dialogType.value = 'view'
  formData.value = { ...row }
  dialogVisible.value = true
}

// 删除
const handleDelete = async (row: ConservationWaterLevel) => {
  try {
    await ElMessageBox.confirm('确定要删除这条涵养水位数据吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const response = await deleteWaterLevel(row.id!)
    if (response.data.code === 200) {
      ElMessage.success('删除成功')
      refreshData()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 对话框确认
const handleDialogConfirm = () => {
  refreshData()
}

// 导入
const handleImport = () => {
  importVisible.value = true
}

// 导入确认
const handleImportConfirm = () => {
  refreshData()
}

// 智能分析
const handleAnalysis = () => {
  analysisVisible.value = true
}

// 分析确认
const handleAnalysisConfirm = () => {
  // 可以跳转到分析结果页面或刷新数据
  ElMessage.success('分析任务已启动')
}

// 导出
const handleExport = () => {
  ElMessage.info('导出功能开发中...')
}
</script>

<style scoped>
.conservation-water-level {
  padding: 20px;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 16px 0;
  border-bottom: 1px solid #ebeef5;
}

.table-title h3 {
  margin: 0 0 4px 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  line-height: 1.4;
}

.table-title p {
  margin: 0;
  font-size: 14px;
  color: #909399;
  line-height: 1.4;
}

.table-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  align-items: center;
}

.table-actions .el-button {
  border-radius: 6px;
  font-weight: 500;
  padding: 8px 16px;
  transition: all 0.3s ease;
}

.table-actions .el-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #ebeef5;
}

.pagination-container :deep(.el-pagination) {
  --el-pagination-font-size: 14px;
  --el-pagination-button-width: 32px;
  --el-pagination-button-height: 32px;
}

.pagination-container :deep(.el-pagination .btn-prev),
.pagination-container :deep(.el-pagination .btn-next),
.pagination-container :deep(.el-pagination .el-pager li) {
  border-radius: 6px;
  margin: 0 2px;
  transition: all 0.2s ease;
}

.pagination-container :deep(.el-pagination .btn-prev:hover),
.pagination-container :deep(.el-pagination .btn-next:hover),
.pagination-container :deep(.el-pagination .el-pager li:hover) {
  transform: translateY(-1px);
}

.level-value {
  font-weight: 600;
}

.level-increase {
  color: #67c23a;
  font-weight: 600;
}

.level-decrease {
  color: #f56c6c;
  font-weight: 600;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}

.action-buttons .el-button {
  margin: 0;
  padding: 4px 8px;
  font-size: 12px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.action-buttons .el-button:hover {
  transform: translateY(-1px);
}

/* 表格样式优化 */
:deep(.el-table) {
  font-size: 14px;
}

:deep(.el-table .el-table__header-wrapper) {
  background-color: #fafafa;
}

:deep(.el-table .el-table__header th) {
  background-color: #fafafa;
  color: #606266;
  font-weight: 600;
}

:deep(.el-table .el-table__body-wrapper) {
  overflow-x: auto;
}

:deep(.el-table .cell) {
  padding: 0 8px;
  word-break: break-word;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  :deep(.el-table .el-table__body-wrapper) {
    overflow-x: scroll;
  }

  .table-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .table-actions {
    width: 100%;
    justify-content: flex-start;
  }
}

@media (max-width: 768px) {
  .conservation-water-level {
    padding: 12px;
  }

  .table-header {
    padding: 12px 0;
  }

  .table-actions {
    gap: 8px;
  }

  .table-actions .el-button {
    padding: 6px 12px;
    font-size: 13px;
  }

  .action-buttons {
    gap: 4px;
  }

  .action-buttons .el-button {
    padding: 2px 6px;
    font-size: 11px;
  }

  .pagination-container {
    justify-content: center;
    margin-top: 16px;
  }
}

/* 卡片样式 */
:deep(.el-card__body) {
  padding: 20px;
}

/* 表格容器 */
.table-card {
  min-height: 600px;
}

.table-card :deep(.el-card__body) {
  padding: 20px;
  height: 100%;
}
</style>
