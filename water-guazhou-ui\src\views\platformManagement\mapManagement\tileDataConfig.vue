<template>
  <div class="wrapper">
    <CardSearch
      ref="refSearch"
      :config="SearchConfig"
    ></CardSearch>
    <CardTable
      class="card-table"
      :config="TableConfig"
    ></CardTable>
    <DialogForm
      ref="refDialogForm"
      :config="DialogFormConfig"
    ></DialogForm>
  </div>
</template>

<script setup>
import { onMounted, reactive, ref } from 'vue'
import { 
  getTileDataConfigList, 
  addTileDataConfig, 
  editTileDataConfig, 
  deleteTileDataConfig,
  getTileDataConfigDetail 
} from '@/api/platformManagement/tileDataConfig'
import { SLConfirm, SLMessage } from '@/utils/Message'

const refSearch = ref()
const refDialogForm = ref()

// 整数验证规则
const integerValidator = (rule, value, callback) => {
  if (!value) {
    callback()
    return
  }
  const regex = /^[0-9]+$/
  if (!regex.test(value)) {
    callback(new Error('只能输入整数'))
  } else {
    callback()
  }
}

// 透明度验证规则 (0-1)
const opacityValidator = (rule, value, callback) => {
  if (!value) {
    callback()
    return
  }
  const num = parseFloat(value)
  if (isNaN(num) || num < 0 || num > 1) {
    callback(new Error('透明度范围应为0-1'))
  } else {
    callback()
  }
}

// 亮度验证规则 (0-2)
const brightnessValidator = (rule, value, callback) => {
  if (!value) {
    callback()
    return
  }
  const num = parseFloat(value)
  if (isNaN(num) || num < 0 || num > 2) {
    callback(new Error('亮度范围应为0-2'))
  } else {
    callback()
  }
}

const SearchConfig = reactive({
  labelWidth: '100px',
  filters: [
    { 
      type: 'input', 
      label: '瓦片名称', 
      field: 'name', 
      placeholder: '请输入瓦片名称',
      onChange: () => refreshData() 
    },
    {
      type: 'btn-group',
      btns: [
        { type: 'primary', perm: true, text: '查询', click: () => refreshData() },
        { perm: true, type: 'primary', text: '新增', click: () => handleAdd() },
        { perm: true, type: 'danger', text: '批量删除', click: () => handleDelete() }
      ]
    }
  ],
  defaultParams: {}
})

const TableConfig = reactive({
  columns: [
    { label: '瓦片名称', prop: 'name' },
    { label: '瓦片类型', prop: 'type' },
    { label: '瓦片地址', prop: 'url', showOverflowTooltip: true },
    { label: '格式', prop: 'format' },
    { label: '最小缩放', prop: 'minZoomLevel' },
    { label: '最大缩放', prop: 'maxZoomLevel' },
    { label: '透明度', prop: 'opacity' },
    { label: '亮度', prop: 'brightness' },
    { label: '描述', prop: 'description', showOverflowTooltip: true }
  ],
  dataList: [],
  operations: [
    {
      perm: true,
      type: 'primary',
      isTextBtn: true,
      text: '查看详情',
      click: (row) => handleDetail(row)
    },
    {
      perm: true,
      type: 'primary',
      isTextBtn: true,
      text: '编辑',
      click: (row) => handleAdd(row)
    },
    {
      perm: true,
      type: 'danger',
      isTextBtn: true,
      text: '删除',
      click: (row) => handleDelete(row)
    }
  ],
  pagination: {
    total: 0,
    page: 1,
    align: 'right',
    limit: 20,
    handlePage: (page) => {
      TableConfig.pagination.page = page
      refreshData()
    },
    handleSize: (size) => {
      TableConfig.pagination.limit = size
      refreshData()
    }
  },
  handleSelectChange: (rows) => {
    TableConfig.selectList = rows || []
  }
})

const DialogFormConfig = reactive({
  title: '新增瓦片数据配置',
  group: [
    {
      fields: [
        {
          type: 'input',
          label: '瓦片名称',
          field: 'name',
          rules: [{ required: true, message: '请输入瓦片名称' }]
        },
        {
          type: 'select',
          label: '瓦片类型',
          field: 'type',
          options: [
            { label: 'WMTS', value: 'WMTS' },
            { label: 'TMS', value: 'TMS' },
            { label: 'XYZ', value: 'XYZ' },
            { label: 'WMS', value: 'WMS' }
          ],
          rules: [{ required: true, message: '请选择瓦片类型' }]
        },
        {
          type: 'input',
          label: '瓦片地址',
          field: 'url',
          rules: [{ required: true, message: '请输入瓦片地址' }]
        },
        {
          type: 'select',
          label: '格式',
          field: 'format',
          options: [
            { label: 'PNG', value: 'image/png' },
            { label: 'JPEG', value: 'image/jpeg' },
            { label: 'WebP', value: 'image/webp' },
            { label: 'GIF', value: 'image/gif' }
          ],
          rules: [{ required: true, message: '请选择格式' }]
        },
        {
          type: 'input',
          label: '最小缩放级别',
          field: 'minZoomLevel',
          placeholder: '请输入整数',
          rules: [
            { required: true, message: '请输入最小缩放级别' },
            { validator: integerValidator, trigger: 'blur' }
          ]
        },
        {
          type: 'input',
          label: '最大缩放级别',
          field: 'maxZoomLevel',
          placeholder: '请输入整数',
          rules: [
            { required: true, message: '请输入最大缩放级别' },
            { validator: integerValidator, trigger: 'blur' }
          ]
        },
        {
          type: 'input',
          label: '透明度',
          field: 'opacity',
          placeholder: '请输入0-1之间的数值',
          rules: [
            { required: true, message: '请输入透明度' },
            { validator: opacityValidator, trigger: 'blur' }
          ]
        },
        {
          type: 'input',
          label: '亮度',
          field: 'brightness',
          placeholder: '请输入0-2之间的数值',
          rules: [
            { required: true, message: '请输入亮度' },
            { validator: brightnessValidator, trigger: 'blur' }
          ]
        },
        {
          type: 'input',
          label: '瓦片大小',
          field: 'tileSize',
          placeholder: '例如：256',
          rules: [
            { required: true, message: '请输入瓦片大小' },
            { validator: integerValidator, trigger: 'blur' }
          ]
        },
        {
          type: 'input',
          label: '矩阵集',
          field: 'matrixSet',
          placeholder: '请输入矩阵集标识'
        },
        {
          type: 'textarea',
          label: '描述',
          field: 'description',
          placeholder: '请输入描述信息'
        }
      ]
    }
  ],
  labelPosition: 'top',
  defaultValue: {},
  dialogWidth: 600,
  draggable: true,
  showSubmit: true,
  showCancel: true,
  cancelText: '取消',
  submitText: '确定',
  submit: async (params) => {
    try {
      // 验证缩放级别逻辑：最小值应该小于等于最大值
      if (parseInt(params.minZoomLevel) > parseInt(params.maxZoomLevel)) {
        SLMessage.error('最小缩放级别应该小于等于最大缩放级别')
        return
      }
      
      if (params.id) {
        await editTileDataConfig(params)
        SLMessage.success('修改成功')
      } else {
        await addTileDataConfig(params)
        SLMessage.success('新增成功')
      }
      refDialogForm.value?.closeDialog()
      refreshData()
    } catch (error) {
      SLMessage.error('操作失败')
    }
  }
})

// 重置对话框配置
const resetDialogConfig = () => {
  // 重置所有表单字段为可编辑状态
  DialogFormConfig.group[0].fields.forEach(field => {
    field.disabled = false
    field.readonly = false
  })
  
  // 恢复默认按钮配置
  DialogFormConfig.showSubmit = true
  DialogFormConfig.showCancel = true
  DialogFormConfig.cancelText = '取消'
  DialogFormConfig.submitText = '确定'
  
  // 恢复提交函数
  DialogFormConfig.submit = async (params) => {
    try {
      // 验证缩放级别逻辑：最小值应该小于等于最大值
      if (parseInt(params.minZoomLevel) > parseInt(params.maxZoomLevel)) {
        SLMessage.error('最小缩放级别应该小于等于最大缩放级别')
        return
      }
      
      if (params.id) {
        await editTileDataConfig(params)
        SLMessage.success('修改成功')
      } else {
        await addTileDataConfig(params)
        SLMessage.success('新增成功')
      }
      refDialogForm.value?.closeDialog()
      refreshData()
    } catch (error) {
      SLMessage.error('操作失败')
    }
  }
  
  // 清除自定义按钮配置
  DialogFormConfig.footerBtns = undefined
}

// 新增/编辑
const handleAdd = (row) => {
  resetDialogConfig()
  
  DialogFormConfig.title = row ? '编辑瓦片数据配置' : '新增瓦片数据配置'
  DialogFormConfig.defaultValue = { ...(row || {}) }
  refDialogForm.value?.openDialog()
}

// 查看详情
const handleDetail = async (row) => {
  // 模拟详情数据
  const mockDetailData = {
    id: row.id || '1',
    name: row.name || '测试瓦片详情',
    type: row.type || 'WMTS',
    url: row.url || 'https://example.com/wmts/{z}/{x}/{y}.png',
    format: row.format || 'image/png',
    minZoomLevel: row.minZoomLevel || '0',
    maxZoomLevel: row.maxZoomLevel || '18',
    opacity: row.opacity || '1.0',
    brightness: row.brightness || '1.0',
    tileSize: row.tileSize || '256',
    matrixSet: row.matrixSet || 'EPSG:3857',
    description: row.description || '这是瓦片数据配置的详情数据'
  }

  try {
    console.log('获取详情，行数据:', row)
    
    const res = await getTileDataConfigDetail(row.id)
    console.log('详情API响应:', res)
    
    let detailData = null
    
    // 处理不同的数据结构
    if (res.data) {
      if (res.data.data) {
        detailData = res.data.data
      } else {
        detailData = res.data
      }
    } else if (res) {
      detailData = res
    }
    
    console.log('解析后的详情数据:', detailData)
    
    // 如果没有获取到详情数据，使用模拟数据
    if (!detailData) {
      console.log('使用模拟详情数据')
      detailData = mockDetailData
    }
    
    // 先重置配置
    resetDialogConfig()
    
    // 设置详情模式
    DialogFormConfig.title = '瓦片数据配置详情'
    DialogFormConfig.defaultValue = { ...detailData }
    
    console.log('设置的详情数据:', DialogFormConfig.defaultValue)
    
    // 设置为只读模式 - 对不同类型字段采用不同的禁用方式
    DialogFormConfig.group[0].fields.forEach(field => {
      if (field.type === 'select') {
        // 对于select字段，使用readonly而不是disabled
        field.readonly = true
        field.disabled = true
      } else {
        field.disabled = true
      }
    })
    
    // 隐藏提交按钮，只显示关闭按钮
    DialogFormConfig.showSubmit = false
    DialogFormConfig.showCancel = true
    DialogFormConfig.cancel = true
    DialogFormConfig.cancelText = '关闭' 
    DialogFormConfig.submitText = undefined
    DialogFormConfig.submit = undefined
    DialogFormConfig.submitting = false
    DialogFormConfig.footerBtns = [
      {
        text: '关闭',
        type: 'default',
        click: () => {
          refDialogForm.value?.closeDialog()
        }
      }
    ]
    
    console.log('详情模式DialogFormConfig配置:', {
      showSubmit: DialogFormConfig.showSubmit,
      showCancel: DialogFormConfig.showCancel,
      cancel: DialogFormConfig.cancel,
      cancelText: DialogFormConfig.cancelText,
      submitText: DialogFormConfig.submitText,
      submit: DialogFormConfig.submit,
      footerBtns: DialogFormConfig.footerBtns
    })
    
    refDialogForm.value?.openDialog()
    
  } catch (error) {
    console.error('获取详情失败:', error)
    console.log('API调用失败，使用模拟详情数据')
    
    // 先重置配置
    resetDialogConfig()
    
    // 设置详情模式
    DialogFormConfig.title = '瓦片数据配置详情'
    DialogFormConfig.defaultValue = { ...mockDetailData }
    
    // 设置为只读模式
    DialogFormConfig.group[0].fields.forEach(field => {
      if (field.type === 'select') {
        field.readonly = true
        field.disabled = true
      } else {
        field.disabled = true
      }
    })
    
    // 隐藏提交按钮，只显示关闭按钮
    DialogFormConfig.showSubmit = false
    DialogFormConfig.showCancel = true
    DialogFormConfig.cancel = true
    DialogFormConfig.cancelText = '关闭' 
    DialogFormConfig.submitText = undefined
    DialogFormConfig.submit = undefined
    DialogFormConfig.submitting = false
    DialogFormConfig.footerBtns = [
      {
        text: '关闭',
        type: 'default',
        click: () => {
          refDialogForm.value?.closeDialog()
        }
      }
    ]
    
    console.log('详情模式DialogFormConfig配置:', {
      showSubmit: DialogFormConfig.showSubmit,
      showCancel: DialogFormConfig.showCancel,
      cancel: DialogFormConfig.cancel,
      cancelText: DialogFormConfig.cancelText,
      submitText: DialogFormConfig.submitText,
      submit: DialogFormConfig.submit,
      footerBtns: DialogFormConfig.footerBtns
    })
    
    refDialogForm.value?.openDialog()
    
    SLMessage.error('API调用失败，当前显示模拟数据')
  }
}

// 删除
const handleDelete = (row) => {
  SLConfirm('确定删除？', '删除提示')
    .then(async () => {
      try {
        const ids = row
          ? [row.id]
          : TableConfig.selectList?.map(item => item.id) || []

        if (!ids.length) {
          SLMessage.warning('请选择要删除的数据')
          return
        }

        const res = await deleteTileDataConfig(ids)
        if (res.data) {
          SLMessage.success('删除成功')
          refreshData()
        } else {
          SLMessage.error('删除失败')
        }
      } catch (error) {
        SLMessage.error('删除失败')
      }
    })
    .catch(() => {
      // 取消删除
    })
}

// 刷新数据
const refreshData = async () => {
  // 模拟数据 - 用于测试显示
  const mockData = [
    {
      id: '1',
      name: '测试瓦片配置1',
      type: 'WMTS',
      url: 'https://example.com/wmts/{z}/{x}/{y}.png',
      format: 'image/png',
      minZoomLevel: '0',
      maxZoomLevel: '18',
      opacity: '1.0',
      brightness: '1.0',
      tileSize: '256',
      matrixSet: 'EPSG:3857',
      description: '这是一个测试瓦片配置'
    },
    {
      id: '2', 
      name: '测试瓦片配置2',
      type: 'XYZ',
      url: 'https://example.com/xyz/{z}/{x}/{y}.jpg',
      format: 'image/jpeg',
      minZoomLevel: '1',
      maxZoomLevel: '16',
      opacity: '0.8',
      brightness: '1.2',
      tileSize: '512',
      matrixSet: 'EPSG:4326',
      description: '另一个测试瓦片配置'
    }
  ]

  try {
    const query = refSearch.value?.queryParams
    console.log('请求参数:', {
      page: TableConfig.pagination.page,
      size: TableConfig.pagination.limit,
      ...(query || {})
    })
    
    const res = await getTileDataConfigList({
      page: TableConfig.pagination.page,
      size: TableConfig.pagination.limit,
      ...(query || {})
    })
    
    console.log('API响应数据:', res)
    
    // 处理不同的数据结构
    if (res.data) {
      // 情况1: res.data.records 和 res.data.total
      if (res.data.records) {
        TableConfig.dataList = res.data.records || []
        TableConfig.pagination.total = res.data.total || 0
      }
      // 情况2: res.data.data.records 和 res.data.data.total  
      else if (res.data.data && res.data.data.records) {
        TableConfig.dataList = res.data.data.records || []
        TableConfig.pagination.total = res.data.data.total || 0
      }
      // 情况3: res.data 直接是数组
      else if (Array.isArray(res.data)) {
        TableConfig.dataList = res.data
        TableConfig.pagination.total = res.data.length
      }
      // 情况4: res.data.data 是数组
      else if (Array.isArray(res.data.data)) {
        TableConfig.dataList = res.data.data
        TableConfig.pagination.total = res.data.data.length
      }
      else {
        console.warn('未知的数据结构:', res.data)
        TableConfig.dataList = []
        TableConfig.pagination.total = 0
      }
    }
    // 情况5: 直接是数组格式
    else if (Array.isArray(res)) {
      TableConfig.dataList = res
      TableConfig.pagination.total = res.length
    }
    else {
      console.warn('无法解析的响应格式:', res)
      TableConfig.dataList = []
      TableConfig.pagination.total = 0
    }
    
    console.log('解析后的数据:', TableConfig.dataList)
    console.log('总数:', TableConfig.pagination.total)
    
    // 如果没有数据，使用模拟数据进行测试
    if (TableConfig.dataList.length === 0) {
      console.log('使用模拟数据进行测试')
      TableConfig.dataList = mockData
      TableConfig.pagination.total = mockData.length
    }
    
  } catch (error) {
    console.error('获取数据失败:', error)
    console.log('API调用失败，使用模拟数据')
    
    // API调用失败时使用模拟数据
    TableConfig.dataList = mockData
    TableConfig.pagination.total = mockData.length
    
    SLMessage.error('API调用失败，当前显示模拟数据')
  }
}

onMounted(() => {
  refreshData()
})
</script>

<style lang="scss" scoped>
.wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.card-table {
  flex: 1;
  margin-top: 16px;
}
</style> 