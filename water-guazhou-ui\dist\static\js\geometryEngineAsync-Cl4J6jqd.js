import{ar as f,bY as w}from"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";function i(t){var r;return Array.isArray(t)?(r=t[0])==null?void 0:r.spatialReference:t==null?void 0:t.spatialReference}function l(t){return t&&(Array.isArray(t)?t.map(l):t.toJSON?t.toJSON():t)}function u(t){return Array.isArray(t)?t.map(r=>f(r)):f(t)}function m(t,r){let n;return Array.isArray(t)?n=t:(n=[],n.push(t),r!=null&&n.push(r)),n}let s;async function g(){return s||(s=w("geometryEngineWorker",{strategy:"distributed"})),s}async function e(t,r){return(await g()).invoke("executeGEOperation",{operation:t,parameters:l(r)})}async function x(t,r){return u(await e("clip",[i(t),t,r]))}async function v(t,r){return u(await e("cut",[i(t),t,r]))}function E(t,r){return e("contains",[i(t),t,r])}function O(t,r){return e("crosses",[i(t),t,r])}function R(t,r,n){return e("distance",[i(t),t,r,n])}function S(t,r){return e("equals",[i(t),t,r])}function b(t,r){return e("intersects",[i(t),t,r])}function J(t,r){return e("touches",[i(t),t,r])}function N(t,r){return e("within",[i(t),t,r])}function k(t,r){return e("disjoint",[i(t),t,r])}function D(t,r){return e("overlaps",[i(t),t,r])}function L(t,r,n){return e("relate",[i(t),t,r,n])}function j(t){return e("isSimple",[i(t),t])}async function B(t){return u(await e("simplify",[i(t),t]))}async function G(t,r=!1){return u(await e("convexHull",[i(t),t,r]))}async function H(t,r){return u(await e("difference",[i(t),t,r]))}async function W(t,r){return u(await e("symmetricDifference",[i(t),t,r]))}async function $(t,r){return u(await e("intersect",[i(t),t,r]))}async function q(t,r=null){const n=m(t,r);return u(await e("union",[i(n),n]))}async function z(t,r,n,a,c,o){return u(await e("offset",[i(t),t,r,n,a,c,o]))}async function C(t,r,n,a=!1){const c=[i(t),t,r,n,a];return u(await e("buffer",c))}async function F(t,r,n,a,c,o){const p=[i(t),t,r,n,a,c,o];return u(await e("geodesicBuffer",p))}function d(t){var r;return"xmin"in t?t.center:"x"in t?t:(r=t.extent)==null?void 0:r.center}async function I(t,r,n){if(t==null)throw new y;const a=t.spatialReference;if((n=n??d(t))==null)throw new y;const c=t.constructor.fromJSON(await e("rotate",[a,t,r,n]));return c.spatialReference=a,c}async function K(t,r,n,a){return u(await e("generalize",[i(t),t,r,n,a]))}async function M(t,r,n){return u(await e("densify",[i(t),t,r,n]))}async function P(t,r,n,a=0){return u(await e("geodesicDensify",[i(t),t,r,n,a]))}function U(t,r){return e("planarArea",[i(t),t,r])}function Y(t,r){return e("planarLength",[i(t),t,r])}function Z(t,r,n){return e("geodesicArea",[i(t),t,r,n])}function Q(t,r,n){return e("geodesicLength",[i(t),t,r,n])}class y extends Error{constructor(){super("Illegal Argument Exception")}}export{k as A,K as B,M as C,$ as D,H as E,Y as F,I as H,j as J,Z as K,C as L,Q as M,B as N,D as O,F as P,L as R,J as S,P as U,U as W,q as b,R as d,S as g,b as h,G as j,W as k,O as m,E as p,z as v,v as w,N as x,x as y};
