package org.thingsboard.server.dao.sql.gis;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.request.GisFileListRequest;
import org.thingsboard.server.dao.model.sql.gis.GisFile;

@Mapper
public interface GisFileMapper extends BaseMapper<GisFile> {
    IPage<GisFile> findList(IPage<GisFile> pageRequest, @Param("param") GisFileListRequest request);
}
