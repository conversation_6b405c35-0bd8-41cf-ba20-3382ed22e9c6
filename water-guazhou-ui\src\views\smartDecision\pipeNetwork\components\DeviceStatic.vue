<template>
  <div class="wheeling-items">
    <div
      v-for="(item, i) in list"
      :key="i"
      class="wheeling-item"
      :class="item.color"
    >
      <el-image class="img" :fit="'contain'" :src="item.img"></el-image>
      <div class="info">
        <div class="text">
          {{ item.label }}
        </div>
        <div class="value">{{ item.value }} {{ item.unit }}</div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { transNumberUnit } from '@/utils/GlobalHelper';
import img_1 from '../imgs/1.png';
import img_2 from '../imgs/2.png';
import img_3 from '../imgs/3.png';
import img_4 from '../imgs/4.png';
import img_5 from '../imgs/5.png';
import img_6 from '../imgs/6.png';
import { getCostListByCode } from '@/api/jinzhou';

const props = defineProps<{
  pipeData: {
    pipeLength: number;
    valve: number;
    meter: number;
    drayAirValve: number;
    hydrant: number;
    threeCorss: number;
  };
}>();
const state = computed(() => {
  const pipeLength = transNumberUnit(props.pipeData?.pipeLength ?? 0);
  return {
    data: [
      {
        label: '供水管长',
        value: pipeLength.value.toFixed(2) + pipeLength.unit,
        unit: '米',
        color: 'lightblue',
        img: img_6
      },
      {
        label: '阀门',
        value: props.pipeData?.valve || 0,
        unit: '个',
        color: 'lightblue',
        img: img_1
      },
      {
        label: '水表',
        value: props.pipeData?.meter || 0,
        unit: '个',
        color: 'orange',
        img: img_2
      },
      {
        label: '排气阀',
        value: props.pipeData?.drayAirValve || 0,
        unit: '个',
        color: 'orange',
        img: img_3
      },
      {
        label: '消防栓',
        value: props.pipeData?.hydrant || 0,
        unit: '个',
        color: 'seablue',
        img: img_4
      },
      {
        label: '三通',
        value: props.pipeData?.threeCorss || 0,
        unit: '个',
        color: 'seablue',
        img: img_5
      }
    ]
  };
});

const data = ref<any>({});

const list = ref([
  {
    label: '供水管长',
    value: 0,
    unit: '米',
    color: 'lightblue',
    img: img_6
  },
  {
    label: '阀门',
    value: 0,
    unit: '个',
    color: 'lightblue',
    img: img_1
  },
  {
    label: '水表',
    value: 0,
    unit: '个',
    color: 'orange',
    img: img_2
  },
  {
    label: '排气阀',
    value: 0,
    unit: '个',
    color: 'orange',
    img: img_3
  },
  {
    label: '消防栓',
    value: 0,
    unit: '个',
    color: 'seablue',
    img: img_4
  },
  {
    label: '三通',
    value: 0,
    unit: '个',
    color: 'seablue',
    img: img_5
  }
]);

onMounted(async () => {
  getCostListByCode('智慧管网').then((res) => {
    data.value = { ...JSON.parse(res.data.data.jsonData) };
    data.value.list.forEach((element, index) => {
      list.value[index] = { ...list.value[index], ...element };
    });
  });
});
</script>
<style lang="scss" scoped>
.wheeling-items {
  display: flex;
  flex-wrap: wrap;
  flex-direction: row;
  padding: 0 12px;
  .wheeling-item {
    margin: 0;
    padding: 0;
    width: 50%;
    height: 48px;
    margin: 16px 0;
    display: flex;
    border-radius: 24px;
    &.lightblue {
      background: linear-gradient(
        89.98deg,
        rgba(43, 166, 249, 0.55) 8.85%,
        rgba(43, 166, 249, 0) 99.97%
      );
    }
    &.orange {
      background: linear-gradient(
        89.98deg,
        rgba(249, 142, 43, 0.55) 8.85%,
        rgba(249, 142, 43, 0) 99.97%
      );
    }
    &.seablue {
      background: linear-gradient(
        89.98deg,
        rgba(47, 43, 249, 0.55) 8.85%,
        rgba(47, 43, 249, 0) 99.97%
      );
    }
    .img {
      width: 48px;
      height: 48px;
      transform: scale(1.3);
    }
    .info {
      padding: 8px;
      font-size: 14px;
      line-height: 16px;
    }
  }
}
</style>
