import { GetStationAttrs } from '@/api/shuiwureports/zhandian'
import { AttrGroupName_none } from '../data'

export const useStationAttrGroup = () => {
  const group = ref<{ type: string; attrList: any[] }[]>([])
  const initAttrGroupData = async (stationId?: string) => {
    if (stationId === undefined) {
      group.value = []
    } else {
      const res = await GetStationAttrs({
        stationId: stationId || ''
      })
      group.value = res.data || []
    }
    group.value.length === 0
      && group.value.push({ type: AttrGroupName_none, attrList: [] })
    return group.value
  }
  const removeAttrGroup = (type?: string) => {
    const index = group.value.findIndex(item => item.type === type)
    if (index > -1) {
      group.value.splice(index, 1)
    }
  }
  return {
    removeAttrGroup,
    initAttrGroupData,
    group
  }
}
