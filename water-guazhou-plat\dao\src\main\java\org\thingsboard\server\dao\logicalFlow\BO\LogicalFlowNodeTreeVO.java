package org.thingsboard.server.dao.logicalFlow.BO;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.thingsboard.server.dao.model.sql.LogicalFlowNode;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class LogicalFlowNodeTreeVO extends LogicalFlowNode {

    private List<LogicalFlowNodeTreeVO> children;

    public LogicalFlowNodeTreeVO(LogicalFlowNode logicalFlowNode) {
        this.setCreateTime(logicalFlowNode.getCreateTime());
        this.setName(logicalFlowNode.getName());
        this.setOrder(logicalFlowNode.getOrder());
        this.setScript(logicalFlowNode.getScript());
        this.setTenantId(logicalFlowNode.getTenantId());
        this.setType(logicalFlowNode.getType());
        this.setLogicalFlowId(logicalFlowNode.getLogicalFlowId());
        this.setParentId(logicalFlowNode.getParentId());
        this.setParam(logicalFlowNode.getParam());
        this.setAdditionalInfo(logicalFlowNode.getAdditionalInfo());
        this.setId(logicalFlowNode.getId());
        this.setRemark(logicalFlowNode.getRemark());
    }
}
