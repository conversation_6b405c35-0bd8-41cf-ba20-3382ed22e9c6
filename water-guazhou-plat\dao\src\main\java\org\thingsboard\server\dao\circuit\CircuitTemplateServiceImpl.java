package org.thingsboard.server.dao.circuit;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.smartProduction.circuit.CircuitTemplate;
import org.thingsboard.server.dao.sql.smartProduction.circuit.CircuitTemplateMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.circuit.CircuitTemplatePageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.circuit.CircuitTemplateSaveRequest;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@Service
public class CircuitTemplateServiceImpl implements CircuitTemplateService {
    @Autowired
    private CircuitTemplateMapper mapper;

    @Override
    public IPage<CircuitTemplate> findAllConditional(CircuitTemplatePageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    public CircuitTemplate save(CircuitTemplateSaveRequest entity) {
        return QueryUtil.saveOrUpdateOneByRequest(entity, mapper);
    }

    @Override
    public boolean update(CircuitTemplate entity) {
        return mapper.update(entity);
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }

    @Override
    public List<String> getSettings(String templateId) {
        String settings = mapper.getSettings(templateId);
        if (settings == null) {
            return Collections.emptyList();
        }
        return Arrays.asList(settings.split(","));
    }
}
