package org.thingsboard.server.dao.sql.department;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.thingsboard.server.dao.model.sql.store.DeviceSettleJournal;
import org.thingsboard.server.dao.util.imodel.query.store.DeviceSettleJournalPageRequest;

import java.util.List;

@Mapper
public interface DeviceSettleJournalMapper extends BaseMapper<DeviceSettleJournal> {
    IPage<DeviceSettleJournal> findByPage(DeviceSettleJournalPageRequest request);

    boolean update(DeviceSettleJournal attr);

    List<String> findSettleNames(String tenantId);
}
