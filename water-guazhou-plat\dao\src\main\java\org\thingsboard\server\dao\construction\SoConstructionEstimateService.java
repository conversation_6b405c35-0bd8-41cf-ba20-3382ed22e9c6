package org.thingsboard.server.dao.construction;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionEstimate;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionEstimatePageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionEstimateSaveRequest;

public interface SoConstructionEstimateService {
    /**
     * 分页条件查询工程预算
     *
     * @param request 分页查询条件
     * @return 分页查询结果
     */
    IPage<SoConstructionEstimate> findAllConditional(SoConstructionEstimatePageRequest request);

    /**
     * 保存工程预算
     *
     * @param entity 实体信息
     * @return 保存好的实体
     */
    SoConstructionEstimate save(SoConstructionEstimateSaveRequest entity);

    /**
     * 增量修改
     *
     * @param entity 实体信息
     * @return 是否修改成功
     */
    boolean update(SoConstructionEstimate entity);

    /**
     * 删除
     *
     * @param id 唯一标识
     * @return 是否删除成功
     */
    boolean delete(String id);

    /**
     * 完成
     *
     * @param constructionCode 工程编号
     * @param userId           请求发起人id
     * @param tenantId         客户id
     * @return 是否成功
     */
    boolean complete(String constructionCode, String userId, String tenantId);

    /**
     * 是否已完成
     *
     * @param id 唯一标识
     * @return 是否已完成
     */
    boolean isComplete(String id);

    /**
     * 是否已完成
     *
     * @param constructionCode 编号
     * @param tenantId         客户id
     * @return 是否已完成
     */
    boolean isComplete(String constructionCode, String tenantId);

}
