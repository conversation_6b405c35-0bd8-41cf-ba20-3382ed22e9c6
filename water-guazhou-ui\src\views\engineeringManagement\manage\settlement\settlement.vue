<!-- 工程管理-工程管理-工程结算 -->
<template>
  <div class="wrapper">
    <CardSearch ref="refSearch" :config="cardSearchConfig" />
    <CardTable :config="TableConfig" class="card-table"></CardTable>
    <DialogForm ref="refForm" :config="addOrUpdateConfig"></DialogForm>
    <SLDrawer ref="refDetail" :config="detailConfig">
      <detail :config="data.selected" :show="12"></detail>
    </SLDrawer>
  </div>
</template>

<script lang="ts" setup>
import { Refresh } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { ICONS } from '@/common/constans/common';
import {
  getProjectType,
  getConstructionSettlementList,
  postConstructionSettlement,
  postConstructionSettlementComplete,
  getConstructionSettlementExport
} from '@/api/engineeringManagement/manage';
import { getProjectList } from '@/api/engineeringManagement/projectManagement';
import useGlobal from '@/hooks/global/useGlobal';
import { StatusType } from '../../data';
import detail from '../../components/detail.vue';
import { formatDate } from '@/utils/DateFormatter';
import { traverse } from '@/utils/GlobalHelper';

const { $btnPerms } = useGlobal();
const refSearch = ref<ICardSearchIns>();
const refForm = ref<IDialogFormIns>();
const refDetail = ref<ISLDrawerIns>();

const cardSearchConfig = ref<ISearch>({
  filters: [
    { label: '工程编号', field: 'constructionCode', type: 'input' },
    { label: '工程名称', field: 'constructionName', type: 'input' },
    {
      label: '工程类别',
      field: 'constructionTypeId',
      type: 'select',
      options: computed(() => data.projectType) as any
    },
    { label: '创建时间', field: 'time', type: 'daterange', format: 'x' }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          type: 'default',
          perm: true,
          text: '导出',
          icon: ICONS.DOWNLOAD,
          click: () => {
            getConstructionSettlementExport().then((res) => {
              const url = window.URL.createObjectURL(res.data);
              const link = document.createElement('a');
              link.style.display = 'none';
              link.href = url;
              link.setAttribute('download', `工程结算.xlsx`);
              document.body.appendChild(link);
              link.click();
            });
          }
        },
        {
          type: 'default',
          perm: true,
          text: '重置',
          svgIcon: shallowRef(Refresh),
          click: () => {
            refSearch.value?.resetForm();
            refreshData();
          }
        },
        {
          perm: true,
          text: '查询',
          icon: ICONS.QUERY,
          click: () => refreshData()
        }
      ]
    }
  ]
});

const TableConfig = reactive<ICardTable>({
  defaultExpandAll: true,
  indexVisible: true,
  columns: [
    { label: '工程编号', prop: 'constructionCode' },
    { label: '工程名称', prop: 'constructionName' },
    { label: '工程类别', prop: 'constructionTypeName' },
    { label: '预算金额', prop: 'estimateCost' },
    { label: '结算金额', prop: 'cost' },
    { label: '创建人', prop: 'creatorName' },
    {
      label: '创建时间',
      prop: 'createTime',
      formatter: (row) => formatDate(row.createTime, 'YYYY-MM-DD HH:mm:ss')
    },
    {
      label: '工作状态',
      prop: 'status',
      tag: true,
      tagColor: (row): string =>
        StatusType.find((item) => item.value === row.status)?.color || '',
      formatter: (row) =>
        StatusType.find((item) => item.value === row.status)?.label
    }
  ],
  operationWidth: '360px',
  operations: [
    {
      disabled: (val) => !val.status,
      isTextBtn: false,
      text: '详情',
      perm: $btnPerms('RoleManageEdit'),
      click: (row) => {
        data.selected = row;
        refDetail.value?.openDrawer();
      }
    },
    {
      disabled: (val) => !(val.status === null),
      isTextBtn: false,
      type: 'primary',
      text: '添加结算',
      perm: $btnPerms('RoleManageEdit'),
      click: (row) => {
        clickAdd(row);
      }
    },
    {
      disabled: (val) => val.status === 'COMPLETED' || val.status === null,
      isTextBtn: false,
      type: 'success',
      text: '编辑结算',
      perm: $btnPerms('RoleManageEdit'),
      click: (row) => clickEdit(row)
    },
    {
      disabled: (val) => !(val.status === 'PROCESSING'),
      isTextBtn: false,
      text: '完成',
      perm: $btnPerms('RoleManageEdit'),
      click: (row) => {
        postConstructionSettlementComplete(row.constructionCode).then((res) => {
          if (res.data.code === 200) {
            ElMessage.success('已完成');
          } else {
            ElMessage.warning('完成失败');
          }
          refreshData();
        });
      }
    }
  ],
  dataList: [],
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page;
      TableConfig.pagination.limit = size;
      refreshData();
    }
  }
});

const addOrUpdateConfig = reactive<IDialogFormConfig>({
  title: '添加工程预算信息',
  labelWidth: '130px',
  dialogWidth: '1000px',
  submitting: false,
  submit: (params: any) => {
    addOrUpdateConfig.submitting = true;
    let text = '新增';
    if (params.id) text = '修改';
    params.pipLengthDesign = JSON.stringify(params.pipLengthDesign);
    postConstructionSettlement(params)
      .then((res) => {
        addOrUpdateConfig.submitting = false;
        if (res.data.code === 200) {
          ElMessage.success(text + '成功');
          refForm.value?.closeDialog();
          refreshData();
        } else {
          ElMessage.warning(text + '失败');
        }
      })
      .catch((error) => {
        addOrUpdateConfig.submitting = false;
        ElMessage.warning(error);
      });
  },
  defaultValue: {},
  group: [
    {
      fields: [
        {
          xs: 12,
          type: 'input',
          label: '工程编号',
          field: 'constructionCode',
          disabled: true
        },
        {
          xs: 12,
          type: 'input',
          label: '工程名称',
          field: 'constructionName',
          disabled: true
        },
        {
          xs: 12,
          type: 'input',
          label: '工程类别',
          field: 'constructionTypeName',
          disabled: true
        },
        {
          xs: 12,
          type: 'input',
          label: '结算人',
          field: 'processUser',
          rules: [{ required: true, message: '请输入结算人' }]
        },
        {
          xs: 12,
          type: 'number',
          label: '结算金额(万元)',
          field: 'cost',
          min: 0
        },
        {
          type: 'textarea',
          label: '地址',
          field: 'address'
        },
        {
          type: 'textarea',
          label: '备注',
          field: 'remark'
        },
        {
          type: 'file',
          label: '附件',
          field: 'attachments'
        }
      ]
    }
  ]
});

// 详情
const detailConfig = reactive<IDrawerConfig>({
  title: '详情',
  group: [],
  width: '80%',
  modalClass: 'lightColor',
  cancel: false
});

const clickAdd = (row: { [x: string]: any }) => {
  addOrUpdateConfig.title = '添加工程结算';
  data.DesignTubeLength = [];
  addOrUpdateConfig.defaultValue = { ...(row || {}), cost: 0 };
  refForm.value?.openDialog();
};

const clickEdit = (row: { [x: string]: any }) => {
  addOrUpdateConfig.title = '编辑工程结算';
  data.DesignTubeLength = [];
  addOrUpdateConfig.defaultValue = { ...(row || {}) };
  refForm.value?.openDialog();
};

const data = reactive({
  // 项目
  projectList: [],
  // 项目类别
  projectType: [],
  selected: {},
  // 预算管长
  DesignTubeLength: [] as any[],
  getOptions: () => {
    getProjectList({ page: 1, size: -1 }).then((res) => {
      data.projectList = traverse(res.data.data.data || [], 'children', {
        label: 'name',
        value: 'code'
      });
    });
    getProjectType({ page: 1, size: -1 }).then((res) => {
      data.projectType = traverse(res.data.data.data || [], 'children');
    });
  }
});

const refreshData = async () => {
  const params: any = {
    size: TableConfig.pagination.limit || 20,
    page: TableConfig.pagination.page || 1,
    ...(refSearch.value?.queryParams || {})
  };
  if (params?.time) {
    params.fromTime = params.time[0];
    params.toTime = params.time[1];
    delete params.time;
  }
  getConstructionSettlementList(params).then((res) => {
    TableConfig.dataList = res.data.data.data || [];
    TableConfig.pagination.total = res.data.data.total || 0;
  });
};

onMounted(() => {
  refreshData();
  data.getOptions();
});
</script>

<style>
.cs {
  margin-top: 10px;
  padding-top: 20px;
}
</style>
