package org.thingsboard.server.dao.model.VO;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class StationWaterSupplyVO implements Serializable {

    // 站点ID
    private String stationId;

    // 站点名称
    private String name;

    // 今日供水量
    private BigDecimal todayWaterSupply;

    // 昨日供水量
    private BigDecimal yesterdayWaterSupply;

    // 本月供水量
    private BigDecimal monthWaterSupply;

    // 数据最后更新时间
    private String lastTime;

    private String location;

    private String imgs;

    private String scadaUrl;

}
