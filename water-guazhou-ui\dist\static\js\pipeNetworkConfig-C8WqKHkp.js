import{_ as w}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as I}from"./CardTable-rdWOL4_6.js";import{_ as P}from"./CardSearch-CB_HNR-Q.js";import{z as g,C as L,c as x,r as m,b as s,S,o as A,g as B,n as v,q as h}from"./index-r0dFAfgr.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";function N(c){return g({url:"/api/base/pipe/configuration/list",method:"get",params:c})}function q(c){return g({url:"/api/base/pipe/configuration/getDetail",method:"get",params:{id:c}})}function D(c){return g({url:"/api/base/pipe/configuration/add",method:"post",data:c})}function k(c){return g({url:"/api/base/pipe/configuration/edit",method:"post",data:c})}function E(c){return g({url:"/api/base/pipe/configuration/deleteIds",method:"delete",data:c})}const F={class:"wrapper"},V={__name:"pipeNetworkConfig",setup(c){const b=x(),d=x(),C=m({labelWidth:"100px",filters:[{type:"input",label:"管网ID",field:"pipeId",placeholder:"请输入管网ID",onChange:()=>u()},{type:"btn-group",btns:[{type:"primary",perm:!0,text:"查询",click:()=>u()},{perm:!0,type:"primary",text:"新增",click:()=>y()},{perm:!0,type:"danger",text:"批量删除",click:()=>_()}]}],defaultParams:{}}),a=m({columns:[{label:"管网ID",prop:"pipeId"},{label:"运行规则",prop:"rule"},{label:"报警阈值",prop:"alarmThreshold"},{label:"备注",prop:"remark"}],dataList:[],operations:[{perm:!0,type:"primary",isTextBtn:!0,text:"查看详情",click:t=>T(t)},{perm:!0,type:"primary",isTextBtn:!0,text:"编辑",click:t=>y(t)},{perm:!0,type:"danger",isTextBtn:!0,text:"删除",click:t=>_(t)}],pagination:{total:0,page:1,align:"right",limit:20,handlePage:t=>{a.pagination.page=t,u()},handleSize:t=>{a.pagination.limit=t,u()}},handleSelectChange:t=>{a.selectList=t||[]}}),e=m({title:"新增管网配置",group:[{fields:[{type:"input",label:"管网ID",field:"pipeId",rules:[{required:!0,message:"请输入管网ID"}]},{type:"input",label:"运行规则",field:"rule",rules:[{required:!0,message:"请输入运行规则"}]},{type:"input",label:"报警阈值",field:"alarmThreshold",rules:[{required:!0,message:"请输入报警阈值"}]},{type:"textarea",label:"备注",field:"remark",placeholder:"请输入备注信息"}]}],labelPosition:"top",defaultValue:{},dialogWidth:600,draggable:!0,showSubmit:!0,showCancel:!0,cancelText:"取消",submitText:"确定",submit:async t=>{var i;try{t.id?(await k(t),s.success("修改成功")):(await D(t),s.success("新增成功")),(i=d.value)==null||i.closeDialog(),u()}catch{s.error("操作失败")}}}),f=()=>{e.group[0].fields.forEach(t=>{t.disabled=!1,t.readonly=!1}),e.showSubmit=!0,e.showCancel=!0,e.cancelText="取消",e.submitText="确定",e.submit=async t=>{var i;try{t.id?(await k(t),s.success("修改成功")):(await D(t),s.success("新增成功")),(i=d.value)==null||i.closeDialog(),u()}catch{s.error("操作失败")}},e.footerBtns=void 0},y=t=>{var i;f(),e.title=t?"编辑管网配置":"新增管网配置",e.defaultValue={...t||{}},(i=d.value)==null||i.openDialog()},T=async t=>{var l,o;const i={id:t.id||"1",pipeId:t.pipeId||"PIPE001",rule:t.rule||"正常运行规则",alarmThreshold:t.alarmThreshold||"80%",remark:t.remark||"这是管网配置的详情数据"};try{console.log("获取详情，行数据:",t);const n=await q(t.id);console.log("详情API响应:",n);let r=null;n.data?n.data.data?r=n.data.data:r=n.data:n&&(r=n),console.log("解析后的详情数据:",r),r||(console.log("使用模拟详情数据"),r=i),f(),e.title="管网配置详情",e.defaultValue={...r},console.log("设置的详情数据:",e.defaultValue),e.group[0].fields.forEach(p=>{p.type==="select"&&(p.readonly=!0),p.disabled=!0}),e.showSubmit=!1,e.showCancel=!0,e.cancel=!0,e.cancelText="关闭",e.submitText=void 0,e.submit=void 0,e.submitting=!1,e.footerBtns=[{text:"关闭",type:"default",click:()=>{var p;(p=d.value)==null||p.closeDialog()}}],console.log("详情模式DialogFormConfig配置:",{showSubmit:e.showSubmit,showCancel:e.showCancel,cancel:e.cancel,cancelText:e.cancelText,submitText:e.submitText,submit:e.submit,footerBtns:e.footerBtns}),(l=d.value)==null||l.openDialog()}catch(n){console.error("获取详情失败:",n),console.log("API调用失败，使用模拟详情数据"),f(),e.title="管网配置详情",e.defaultValue={...i},e.group[0].fields.forEach(r=>{r.type==="select"&&(r.readonly=!0),r.disabled=!0}),e.showSubmit=!1,e.showCancel=!0,e.cancel=!0,e.cancelText="关闭",e.submitText=void 0,e.submit=void 0,e.submitting=!1,e.footerBtns=[{text:"关闭",type:"default",click:()=>{var r;(r=d.value)==null||r.closeDialog()}}],console.log("详情模式DialogFormConfig配置:",{showSubmit:e.showSubmit,showCancel:e.showCancel,cancel:e.cancel,cancelText:e.cancelText,submitText:e.submitText,submit:e.submit,footerBtns:e.footerBtns}),(o=d.value)==null||o.openDialog(),s.error("API调用失败，当前显示模拟数据")}},_=t=>{S("确定删除？","删除提示").then(async()=>{var i;try{const l=t?[t.id]:((i=a.selectList)==null?void 0:i.map(n=>n.id))||[];if(!l.length){s.warning("请选择要删除的数据");return}(await E(l)).data?(s.success("删除成功"),u()):s.error("删除失败")}catch{s.error("删除失败")}}).catch(()=>{})},u=async()=>{var i;const t=[{id:"1",pipeId:"PIPE001",rule:"正常运行规则",alarmThreshold:"80%",remark:"主要供水管网配置"},{id:"2",pipeId:"PIPE002",rule:"备用运行规则",alarmThreshold:"75%",remark:"备用供水管网配置"}];try{const l=(i=b.value)==null?void 0:i.queryParams;console.log("请求参数:",{page:a.pagination.page,size:a.pagination.limit,...l||{}});const o=await N({page:a.pagination.page,size:a.pagination.limit,...l||{}});console.log("API响应数据:",o),o.data?o.data.records?(a.dataList=o.data.records||[],a.pagination.total=o.data.total||0):o.data.data&&o.data.data.records?(a.dataList=o.data.data.records||[],a.pagination.total=o.data.data.total||0):Array.isArray(o.data)?(a.dataList=o.data,a.pagination.total=o.data.length):Array.isArray(o.data.data)?(a.dataList=o.data.data,a.pagination.total=o.data.data.length):(console.warn("未知的数据结构:",o.data),a.dataList=[],a.pagination.total=0):Array.isArray(o)?(a.dataList=o,a.pagination.total=o.length):(console.warn("无法解析的响应格式:",o),a.dataList=[],a.pagination.total=0),console.log("解析后的数据:",a.dataList),console.log("总数:",a.pagination.total),a.dataList.length===0&&(console.log("使用模拟数据进行测试"),a.dataList=t,a.pagination.total=t.length)}catch(l){console.error("获取数据失败:",l),console.log("API调用失败，使用模拟数据"),a.dataList=t,a.pagination.total=t.length,s.error("API调用失败，当前显示模拟数据")}};return A(()=>{u()}),(t,i)=>{const l=P,o=I,n=w;return B(),v("div",F,[h(l,{ref_key:"refSearch",ref:b,config:C},null,8,["config"]),h(o,{class:"card-table",config:a},null,8,["config"]),h(n,{ref_key:"refDialogForm",ref:d,config:e},null,8,["config"])])}}},H=L(V,[["__scopeId","data-v-63d92726"]]);export{H as default};
