import{d as w,M as F,c as p,s as R,r as u,x as C,a8 as b,bS as W,a9 as N,o as z,g as A,n as U,q as d,i as c,b6 as B,b7 as O}from"./index-r0dFAfgr.js";import{_ as $}from"./CardTable-rdWOL4_6.js";import{_ as j}from"./CardSearch-CB_HNR-Q.js";import{I as g}from"./common-CvK_P_ao.js";import{d as G,b as k,f as Q,h as H,i as J}from"./equipmentPurchase-KOqzaoYr.js";import{r as K}from"./equipmentManage-DuoY00aj.js";import{d as T}from"./equipmentAssetsData-B4Olvyjd.js";import{f as V}from"./DateFormatter-Bm9a68Ax.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";import"./xlsx-rVJkW9yq.js";const X={class:"wrapper"},de=w({__name:"index",setup(Z){const{$btnPerms:f}=F(),m=p(),h=p(),y=p(),x=p(),I=p(),v=p("0"),E=p({filters:[{label:"设备编号",field:"serialId",type:"input"},{label:"设备名称",field:"deviceName",type:"input"},{label:"设备型号",field:"deviceModel",type:"input"},{label:"采购单编号",field:"purchaseCode",type:"input",labelWidth:"90px"},{type:"department-user",label:"请购人",field:"userId"},{label:"采购单标题",field:"purchaseTitle",type:"input",labelWidth:"90px"},{label:"请购单创建时间",field:"time",type:"daterange",labelWidth:"120px",format:"YYYY-MM-DD"}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",icon:g.QUERY,click:()=>s()},{type:"default",perm:!0,text:"重置",svgIcon:R(O),click:()=>{var e;(e=m.value)==null||e.resetForm(),s()}}]}]}),o=u({defaultExpandAll:!0,indexVisible:!0,indexAccumulative:!0,columns:[{label:"采购设备名称",prop:"name"},{label:"设备编号",prop:"serialId"},{label:"设备型号",prop:"model"},{label:"所属大类",prop:"topType"},{label:"所属类别",prop:"type"},{label:"采购数量",prop:"num"},{label:"计划完成时间",prop:"inquiryEndTime",formatter:e=>V(e.inquiryEndTime,"YYYY-MM-DD")},{label:"所属采购单",prop:"mainName"},{label:"询价进度",prop:"status",tag:!0,formatter:e=>{var t;return((t=T.find(i=>i.value===e.status))==null?void 0:t.label)||""},tagColor:e=>{var t;return((t=T.find(i=>i.value===e.status))==null?void 0:t.color)||""}}],operationWidth:"200px",operations:[{hide:e=>e.status===2,text:"询价",icon:g.EDIT,perm:f("RoleManageDelete"),click:e=>P(e)},{hide:e=>e.status!==2,type:"primary",color:"#4195f0",text:"详情",perm:f("RoleManageEdit"),icon:"iconfont icon-xiangqing",click:e=>q(e)}],dataList:[],pagination:{page:1,limit:20,total:0,refreshData:({page:e,size:t})=>{o.pagination.page=e,o.pagination.limit=t,s()}}}),D=u({title:"询价",labelWidth:"130px",submit:e=>{if(!e.supplier.some(t=>t.supplierId&&t.contact&&t.contactPhone&&t.price&&t.inquiryTime)){C.warning("请完整填写供应商信息");return}e.supplier.forEach(t=>{t.intentionSupplier===!0&&Y(e.id)}),G(e.supplier).then(()=>{var t;s(),C.success("询价成功"),(t=h.value)==null||t.closeDrawer()})},defaultValue:{},group:[{fields:[{xl:8,disabled:!0,type:"input",label:"采购单标题",field:"title"},{xl:8,disabled:!0,type:"input",label:"采购单编码",field:"code"},{xl:8,readonly:!0,type:"date",label:"计划采购时间",field:"preTime"},{xl:8,disabled:!0,type:"input",label:"用途",field:"useWay"},{xl:8,disabled:!0,type:"input",label:"请购部门",field:"userDepartmentName"},{xl:8,disabled:!0,type:"input",label:"请购人",field:"userName"},{type:"divider",text:"供应商信息"},{type:"table",field:"supplier",config:{indexVisible:!0,height:"350px",dataList:b(()=>l.selectList),titleRight:[{style:{justifyContent:"flex-end"},items:[{type:"btn-group",btns:[{text:"添加供应商",perm:!0,click:()=>{l.selectList.push({numId:M(),intentionSupplier:!1,purchaseDetailId:l.selectedForm.id})}}]}]}],columns:[{label:"供应商",prop:"supplierId",formItemConfig:{type:"select",options:b(()=>l.SupplierList),onChange:e=>{console.log(e);const t=l.SupplierList.find(i=>i.id===e);console.log(t),l.selectList=l.selectList.map(i=>{if(e===i.supplierId)return{...i,...t}})}}},{label:"联系人",prop:"contact",formItemConfig:{type:"input"}},{label:"联系方式",prop:"contactPhone",formItemConfig:{type:"input"}},{label:"单价",prop:"price",formItemConfig:{type:"input-number"}},{label:"询价时间",prop:"inquiryTime",formItemConfig:{type:"datetime",format:"x"}},{label:"意向供应商",prop:"intentionSupplier",formItemConfig:{type:"switch",onChange:()=>{l.selectList.filter(t=>t.intentionSupplier).length>1&&(l.selectList[v.value].intentionSupplier=!1),l.selectList.filter((t,i)=>(t.intentionSupplier&&(v.value=i),t.intentionSupplier))}}}],operations:[{text:"附件",perm:f("RoleManageEdit"),icon:g.DETAIL,click:e=>{var t;_.defaultValue={...e||{}},(t=y.value)==null||t.openDrawer()}},{text:"移除",type:"danger",icon:g.DELETE,perm:f("RoleManageDelete"),click:e=>{l.selectList=l.selectList.filter(t=>t.numId!==e.numId)}}],pagination:{hide:!0}}}]}]}),_=u({title:"附件",width:"500px",labelWidth:"100px",submit:e=>{var t;l.selectList.map(i=>{i.id===e.id&&(i.file=e.file)}),(t=y.value)==null||t.closeDrawer()},defaultValue:{},group:[{fields:[{type:"file",label:"附件",field:"file"}]}]}),S=u({title:"附件信息",width:"500px",labelWidth:"100px",defaultValue:{},group:[{fields:[{type:"table",field:"file",config:{indexVisible:!0,height:"350px",dataList:b(()=>l.files),columns:[{label:"附件",prop:"file",download:!0,cellStyle:{fontStyle:"italic",width:"100px",cursor:"pointer"},handleClick:e=>{W(e.file)}}],pagination:{hide:!0}}}]}]}),L=u({title:"详情",labelWidth:"130px",defaultValue:{},group:[{fields:[{xl:8,disabled:!0,type:"input",label:"采购单标题",field:"title"},{xl:8,disabled:!0,type:"input",label:"采购单编码",field:"code"},{xl:8,readonly:!0,type:"date",label:"计划采购时间",field:"preTime"},{xl:8,disabled:!0,type:"input",label:"用途",field:"useWay"},{xl:8,readonly:!0,type:"select",label:"请购部门",field:"userDepartmentName"},{xl:8,readonly:!0,type:"select",label:"请购人",field:"userName"},{type:"divider",text:"供应商信息"},{type:"table",field:"key7",config:{indexVisible:!0,height:"350px",dataList:b(()=>l.selectList),columns:[{label:"供应商",prop:"supplierName"},{label:"联系人",prop:"contact"},{label:"联系方式",prop:"contactPhone"},{label:"单价",prop:"price"},{label:"询价时间",prop:"inquiryTime",formatter:e=>V(e.inquiryTime,"YYYY-MM-DD")},{label:"意向供应商",prop:"intentionSupplier",formItemConfig:{readonly:!0,type:"switch"}}],operations:[{text:"附件",perm:f("RoleManageEdit"),icon:g.DETAIL,click:e=>{var i;S.defaultValue={...e||{}};const t=e.file&&e.file.length!==0&&e.file.split(",")||[];l.files=t.map(a=>({file:a})),(i=x.value)==null||i.openDrawer()}}],pagination:{hide:!0}}}]}]});function M(){function e(){return((1+Math.random())*65536|0).toString(16).substring(1)}return e()+e()+"-"+e()+"-"+e()+"-"+e()+"-"+e()+e()+e()}const q=e=>{const t={page:1,size:99999,Id:e.mainId};k(t).then(i=>{var n;const a=i.data.data.data[0]||{};for(const r in a)(a[r]===void 0||a[r]===null)&&(a[r]=" ");L.defaultValue={deviceId:a.id,...a},(n=I.value)==null||n.openDrawer()}),l.getDevicePurchaseItemValue(e.id)},P=e=>{l.selectedForm=e;const t={page:1,size:99999,Id:e.mainId};k(t).then(i=>{var a;D.defaultValue={deviceId:e.id,...i.data.data.data[0]},(a=h.value)==null||a.openDrawer()}),l.getDevicePurchaseItemValue(e.id)};function Y(e){Q(e.id).then(()=>{s()})}const l=u({selectList:[],selectedForm:{},files:[],SupplierList:[],getDevicePurchaseItemValue:e=>{H({purchaseDetailId:e,page:1,size:20}).then(i=>{const a=i.data.data.data||[];l.selectList=a.map((n,r)=>(n.numId=r,n))})},getSupplierValue:()=>{K({page:1,size:99999}).then(t=>{l.SupplierList=N(t.data.data.data||[])})}}),s=async()=>{var t,i,a,n;const e={size:o.pagination.limit,page:o.pagination.page,...((t=m.value)==null?void 0:t.queryParams)||{}};e.time&&((i=e.time)==null?void 0:i.length)>1&&(e.fromTime=((a=m.value)==null?void 0:a.queryParams).time[0]||"",e.toTime=((n=m.value)==null?void 0:n.queryParams).time[1]||""),delete e.time,J(e).then(r=>[o.dataList=r.data.data.data||[],o.pagination.total=r.data.data.total||0])};return z(()=>{s(),l.getSupplierValue()}),(e,t)=>{const i=j,a=$,n=B;return A(),U("div",X,[d(i,{ref_key:"refSearch",ref:m,config:c(E)},null,8,["config"]),d(a,{config:c(o),class:"card-table"},null,8,["config"]),d(n,{ref_key:"refForm",ref:h,config:c(D)},null,8,["config"]),d(n,{ref_key:"detailRef",ref:I,config:c(L)},null,8,["config"]),d(n,{ref_key:"fileForm",ref:y,config:c(_)},null,8,["config"]),d(n,{ref_key:"filedetailForm",ref:x,config:c(S)},null,8,["config"])])}}});export{de as default};
