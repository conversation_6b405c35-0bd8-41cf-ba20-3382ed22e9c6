import{d as A,a0 as y,c as v,o as b,Q as I,g as p,n as m,p as s,i as a,dy as r,aw as G,C as M}from"./index-r0dFAfgr.js";import{r as i}from"./水泵_在线-2IzRX-2K.js";const u="data:image/png;base64,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",N={class:"main"},Z={class:"card zutai-card"},D={class:"card-content ysc-tsb1",style:{}},f={class:"row"},g={class:"status"},j=["src"],w={class:"card-content ysc-tsb2",style:{}},z={class:"row"},U={class:"status"},O=["src"],S={class:"card-content ysc-tsb3",style:{}},h={class:"row"},L={class:"status"},B=["src"],Y={class:"card-content ysc-ddf3",style:{}},C={class:"row"},E=A({__name:"ystsb_overview",setup(T){const e=y(),d=v({}),l=v(),c=()=>{console.log(e.projectList),e.projectList[0].id};return b(()=>{c(),l.value=setInterval(()=>{c()},3e4)}),I(()=>{clearInterval(l.value)}),(W,t)=>{var n,o;return p(),m("div",N,[s("div",Z,[s("div",D,[t[1]||(t[1]=s("div",{class:"card-title"},[s("span",{style:{color:"#d8feff","text-align":"center"}},"1#提升泵")],-1)),s("div",f,[t[0]||(t[0]=s("div",{class:"label"},"状态：",-1)),s("div",g,[s("img",{src:a(i),style:{width:"15px",height:"15px"}},null,8,j)])])]),t[8]||(t[8]=r('<div class="card-content ysc-ddf1" style="" data-v-a5e1ba8c><div class="card-title" data-v-a5e1ba8c><span style="color:#d8feff;text-align:center;" data-v-a5e1ba8c>1#电动阀</span></div><div class="row" data-v-a5e1ba8c><div class="label" data-v-a5e1ba8c>状态：</div><div class="status online" data-v-a5e1ba8c></div></div></div>',1)),s("div",w,[t[3]||(t[3]=s("div",{class:"card-title"},[s("span",{style:{color:"#d8feff","text-align":"center"}},"2#提升泵")],-1)),s("div",z,[t[2]||(t[2]=s("div",{class:"label"},"状态：",-1)),s("div",U,[s("img",{src:a(i),style:{width:"15px",height:"15px"}},null,8,O)])])]),t[9]||(t[9]=r('<div class="card-content ysc-ddf2" data-v-a5e1ba8c><div class="card-title" data-v-a5e1ba8c><span style="color:#d8feff;text-align:center;" data-v-a5e1ba8c>2#电动阀</span></div><div class="row" data-v-a5e1ba8c><div class="label" data-v-a5e1ba8c>状态：</div><div class="status online" data-v-a5e1ba8c></div></div></div>',1)),s("div",S,[t[5]||(t[5]=s("div",{class:"card-title"},[s("span",{style:{color:"#d8feff","text-align":"center"}},"3#提升泵")],-1)),s("div",h,[t[4]||(t[4]=s("div",{class:"label"},"状态：",-1)),s("div",L,[s("img",{src:((n=a(d).原水池)==null?void 0:n.ysbs_csb_status3)=="运行"?a(i):a(u),style:{width:"15px",height:"15px"}},null,8,B)])])]),s("div",Y,[t[7]||(t[7]=s("div",{class:"card-title"},[s("span",{style:{color:"#d8feff","text-align":"center"}},"3#电动阀")],-1)),s("div",C,[t[6]||(t[6]=s("div",{class:"label"},"状态：",-1)),s("div",{class:G(["status",((o=a(d).原水池)==null?void 0:o.ysbs_csf_status3)=="开"?"online":"unline"])},null,2)])])])])}}}),J=M(E,[["__scopeId","data-v-a5e1ba8c"]]);export{J as default};
