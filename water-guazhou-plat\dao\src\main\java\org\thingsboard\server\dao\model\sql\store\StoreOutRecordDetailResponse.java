package org.thingsboard.server.dao.model.sql.store;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.sql.department.GoodsShelfMapper;
import org.thingsboard.server.dao.sql.department.StoreMapper;
import org.thingsboard.server.dao.util.imodel.response.annotations.Flatten;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseTenantName;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseViaMapper;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

@Getter
@Setter
@ResponseEntity
public class StoreOutRecordDetailResponse {
    // id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    // 设备序列号
    private String serialId;

    // 出库单主表ID
    private String mainId;

    // 目标出货数量
    private Integer num;

    // 货架Id
    @ParseViaMapper(GoodsShelfMapper.class)
    private String shelvesId;

    // 货架Id
    @ParseViaMapper(StoreMapper.class)
    private String storeId;

    // 已出货数量
    private Integer amount;

    // 在货架中的数量
    private Integer count;

    // 租户ID
    @ParseTenantName
    private String tenantId;

    @Flatten
    @TableField(exist = false)
    private DeviceInfoResponse deviceInfoResponse;
}
