<template>
  <div class="audio-wrapper">
    <template v-if="!hideBtn">
      <el-icon
        v-if="state.paused"
        class="voice-icon"
      >
        <VideoPlay @click="togglePlay" />
      </el-icon>
      <el-icon
        v-else
        class="voice-icon"
      >
        <VideoPause @click="togglePlay" />
      </el-icon>
    </template>

    <span
      v-if="showUrl"
      class="voice-url"
      :class="[download ? 'download' : '']"
      @click="() => download && downloadUrl(url)"
    >{{ name || url }}</span>
    <audio
      ref="refAudio"
      :class="size || 'default'"
      :src="url"
    ></audio>
  </div>
</template>
<script lang="ts" setup>
import { VideoPlay, VideoPause } from '@element-plus/icons-vue'
import { downloadUrl } from '@/utils/fileHelper'

const emit = defineEmits(['playVoice'])
const refAudio = ref<HTMLAudioElement>()
const props = defineProps<{
  url?: string
  name?: string
  size?: ISize
  showUrl?: boolean
  download?: boolean
  hideBtn?: boolean
  paused?: boolean
}>()
const state = reactive<{
  paused: boolean
}>({
  paused: true
})
const togglePlay = () => {
  state.paused ? refAudio.value?.play() : refAudio.value?.pause()
  state.paused = !state.paused
  emit('playVoice', state.paused)
}
watch(
  () => props.paused,
  (val: boolean) => {
    val ? refAudio.value?.pause() : refAudio.value?.play()
    state.paused = val
  }
)
</script>
<style lang="scss" scoped>
.audio-wrapper {
  display: flex;
  align-items: center;
}

.voice-icon {
  cursor: pointer;
  margin-right: 8px;
}

.voice-url {
  cursor: pointer;
  text-overflow: ellipsis;
  overflow: hidden;

  .download {
    text-decoration: underline;
    color: cadetblue;

    &:hover {
      color: var(--el-color-primary);
    }
  }
}
</style>
