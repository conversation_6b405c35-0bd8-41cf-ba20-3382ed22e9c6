package org.thingsboard.server.dao.sql.componentStorage;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.thingsboard.server.dao.model.sql.ComponentOptionEntity;

public interface ComponentOptionRepository extends JpaRepository<ComponentOptionEntity, String> {

    @Query("SELECT co FROM ComponentOptionEntity co " +
            "WHERE co.tenantId = ?2 AND co.code LIKE %?1%")
    Page<ComponentOptionEntity> findList(String code, String tenantId, Pageable pageable);
}
