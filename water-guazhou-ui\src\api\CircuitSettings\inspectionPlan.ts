// 巡检计划配置
import request from '@/plugins/axios';

// 分页查询巡检计划列表
export function getInspectionPlanList(params: {
  page: number;
  size: number;
  planName?: string;
  inspectionType?: string;
  inspectionCycle?: string;
  executionRole?: string;
  status?: string;
  fromTime?: string;
  toTime?: string;
}) {
  return request({
    url: '/api/inspection/plan',
    method: 'get',
    params
  });
}

// 根据ID查询巡检计划详情
export function getInspectionPlanById(id: string) {
  return request({
    url: `/api/inspection/plan/${id}`,
    method: 'get'
  });
}

// 新增巡检计划
export function addInspectionPlan(params: {
  planName: string;
  inspectionType: string;
  inspectionCycle: string;
  executionRole: string;
  checklistTemplate: string;
  status?: string;
  remark?: string;
}) {
  return request({
    url: '/api/inspection/plan',
    method: 'post',
    data: params
  });
}

// 修改巡检计划
export function updateInspectionPlan(id: string, params: {
  planName?: string;
  inspectionType?: string;
  inspectionCycle?: string;
  executionRole?: string;
  checklistTemplate?: string;
  status?: string;
  remark?: string;
}) {
  return request({
    url: `/api/inspection/plan/${id}`,
    method: 'post',
    data: params
  });
}

// 删除巡检计划
export function deleteInspectionPlan(id: string) {
  return request({
    url: `/api/inspection/plan/${id}`,
    method: 'delete'
  });
}

// 启用/停用巡检计划
export function toggleInspectionPlanStatus(id: string, status: string) {
  return request({
    url: `/api/inspection/plan/${id}/status`,
    method: 'post',
    data: { status }
  });
}

// 获取检查表模板列表
export function getChecklistTemplateList() {
  return request({
    url: '/api/inspection/checklist-template',
    method: 'get'
  });
}

// 获取执行角色列表
export function getExecutionRoleList() {
  return request({
    url: '/api/inspection/execution-role',
    method: 'get'
  });
}
