<template>
  <div class="one-map-detail">
    <div v-loading="state.detailLoading" class="row1">
      <FieldSet
        :size="'default'"
        :title="'大用户'"
        :type="'simple'"
        class="row-title"
      ></FieldSet>
      <div class="pie-charts">
        <div ref="refChartDiv" class="pie-chart">
          <VChart ref="refChart1" :option="state.lineChartOption1"></VChart>
        </div>
      </div>
    </div>
    <div v-loading="state.detailLoading" class="row2">
      <div class="detail-right">
        <VChart ref="refChart2" :option="state.lineChartOption2"></VChart>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { padStart } from 'lodash-es';
import { areaChart } from '../../components/components/chart';
import { GetBigUserDetail } from '@/api/mapservice/onemap';
import { useDetector } from '@/hooks/echarts';

const emit = defineEmits(['refresh', 'mounted']);
const { proxy }: any = getCurrentInstance();
const state = reactive<{
  curRow?: any;
  lineChartOption1: any;
  lineChartOption2: any;
  mapClick?: any;
  stationRealTimeData: any[];
  detailLoading: boolean;
}>({
  lineChartOption1: areaChart(
    Array.from({ length: 24 }).map((item, i) => padStart(i.toString(), 2, '0')),
    [],
    {}
  ),
  lineChartOption2: areaChart(
    Array.from({ length: 30 }).map((item, i) => padStart(i.toString(), 2, '0')),
    [],
    {}
  ),
  stationRealTimeData: [],
  detailLoading: false
});

const refreshDetail = async (row) => {
  emit('refresh', { title: row.name });
  state.detailLoading = true;
  state.curRow = row;
  Array.from({ length: 2 }).map((item, i) => {
    proxy.$refs['refChart' + i]?.resize();
  });
  GetBigUserDetail({
    stationId: row.stationId
  })
    .then((res) => {
      state.detailLoading = false;
      const todayData =
        res.data.data.todayData?.map((item) => item.value) || [];
      const todayXData = res.data.data.todayData?.map((item) => item.ts) || [];
      state.lineChartOption1 = areaChart(todayXData, todayData, {
        unit: 'm³',
        name: '日供水量',
        color1: '#ff0000'
      });

      const monthData =
        res.data.data.monthData?.map((item) => item.value) || [];
      const monthXData = res.data.data.monthData?.map((item) => item.ts) || [];
      state.lineChartOption2 = areaChart(monthXData, monthData, {
        unit: 'm³',
        name: '月供水量',
        color1: '#ff0000'
      });
    })
    .catch((error) => {
      console.log(error);
      state.detailLoading = false;
    });
};
defineExpose({
  refreshDetail
});
const resize = () => {
  Array.from({ length: 2 }).map((item, i) => {
    proxy.$refs['refChart' + (i + 1)]?.resize();
  });
};
const resizer = useDetector();
const refChartDiv = ref<HTMLDivElement>();
onMounted(() => {
  emit('mounted');
  resizer.listenToMush(refChartDiv.value, resize);
});
</script>
<style lang="scss" scoped>
// .dark,
// .darkblue {
//   .one-map-detail {
//     .row1,
//     .row2 {
//       background-color: var(--el-bg-color);
//     }
//   }
// }
.one-map-detail {
  .row1 {
    background-color: var(--el-bg-color);
    height: 370px;
    align-items: center;
    padding: 8px 8px 0 8px;
    margin-bottom: 20px;
    .row-title {
      margin: 0;
    }
    .pie-charts {
      display: flex;
      height: 315px;
    }
  }
  .row2 {
    background-color: var(--el-bg-color);
    height: 480px;
    width: 100%;
  }
  .pie-chart {
    width: 100%;
    height: 100%;
  }
}
.detail-right {
  display: flex;
  width: 100%;
  height: calc(100% - 40px);
  .chart-box {
    height: 100%;
    width: 100%;
  }
}
</style>
