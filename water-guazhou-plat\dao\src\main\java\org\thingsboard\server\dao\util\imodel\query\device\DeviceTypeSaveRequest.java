package org.thingsboard.server.dao.util.imodel.query.device;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.deviceManage.DeviceType;
import org.thingsboard.server.dao.util.imodel.query.GeneralDeviceType;
import org.thingsboard.server.dao.util.imodel.query.IStarHttpRequest;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

import java.util.Date;

@Getter
@Setter
public class DeviceTypeSaveRequest extends SaveRequest<DeviceType> implements GeneralDeviceType {
    // 序列号
    @NotNullOrEmpty
    private String serialId;

    // 类别名称
    @NotNullOrEmpty
    private String name;

    // 排序，升序
    @NotNullOrEmpty
    private Integer orderNum;

    // 备注
    private String remark;

    // 父ID
    private String parentId;

    // region 类别属性
    // 设备型号
    private String model;

    // 设备名称
    private String deviceName;

    // 设备标签
    private String label;

    // 计量单位
    private String unit;

    // 保养周期，单位：天
    private Integer maintenanceCycle;

    // 最小库存
    private Double minStock;

    // 设备图片，多张用逗号分隔
    private String images;

    // 设备附件，多张用逗号分隔
    private String files;
    // endregion

    // 节点级别
    private String level;

    @Override
    public String valid(IStarHttpRequest request) {
        if (serialId.length() != 14) {
            return "编码长度应为14位";
        }
        return super.valid(request);
    }

    @Override
    protected DeviceType build() {
        Date now = new Date();
        DeviceType deviceType = new DeviceType();
        deviceType.setCreateTime(now);
        deviceType.setCreator(currentUserUUID());
        deviceType.setTenantId(tenantId());
        commonSet(deviceType, now);
        return deviceType;
    }

    @Override
    protected DeviceType update(String id) {
        DeviceType deviceType = new DeviceType();
        deviceType.setId(id);
        commonSet(deviceType, new Date());
        return deviceType;
    }

    private void commonSet(DeviceType deviceType, Date now) {
        deviceType.setName(name);
        deviceType.setSerialId(serialId);
        deviceType.setOrderNum(orderNum);
        deviceType.setRemark(remark);
        deviceType.setParentId(parentId);
        deviceType.setModel(model);
        deviceType.setLabel(label);
        deviceType.setUnit(unit);
        deviceType.setMaintenanceCycle(maintenanceCycle);
        deviceType.setMinStock(minStock);
        deviceType.setImages(images);
        deviceType.setFiles(files);
        deviceType.setUpdateTime(now);
        deviceType.setDeviceName(deviceName);
        deviceType.setLevel(level);
    }
}
