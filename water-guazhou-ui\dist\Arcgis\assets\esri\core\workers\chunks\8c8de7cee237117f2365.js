"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[4982],{95401:(t,e,r)=>{r.d(e,{e:()=>s});var n,i,o,s={},a={get exports(){return s},set exports(t){s=t}};n=a,i=function(){function t(t,r,i){i=i||2;var o,s,a,u,x,h,y,c=r&&r.length,f=c?r[0]*i:t.length,d=e(t,0,f,i,!0),v=[];if(!d||d.next===d.prev)return v;if(c&&(d=l(t,r,d,i)),t.length>80*i){o=a=t[0],s=u=t[1];for(var g=i;g<f;g+=i)(x=t[g])<o&&(o=x),(h=t[g+1])<s&&(s=h),x>a&&(a=x),h>u&&(u=h);y=0!==(y=Math.max(a-o,u-s))?1/y:0}return n(d,v,i,o,s,y),v}function e(t,e,r,n,i){var o,s;if(i===B(t,e,r,n)>0)for(o=e;o<r;o+=n)s=L(o,t[o],t[o+1],s);else for(o=r-n;o>=e;o-=n)s=L(o,t[o],t[o+1],s);if(s&&_(s,s.next)){var a=s.next;R(s),s=a}return s}function r(t,e){if(!t)return t;e||(e=t);var r,n=t;do{if(r=!1,n.steiner||!_(n,n.next)&&0!==p(n.prev,n,n.next))n=n.next;else{var i=n.prev;if(R(n),(n=e=i)===n.next)break;r=!0}}while(r||n!==e);return e}function n(t,e,l,u,x,h,y){if(t){!y&&h&&c(t,u,x,h);for(var f,d,v=t;t.prev!==t.next;)if(f=t.prev,d=t.next,h?o(t,u,x,h):i(t))e.push(f.i/l),e.push(t.i/l),e.push(d.i/l),R(t),t=d.next,v=d.next;else if((t=d)===v){y?1===y?n(t=s(r(t),e,l),e,l,u,x,h,2):2===y&&a(t,e,l,u,x,h):n(r(t),e,l,u,x,h,1);break}}}function i(t){var e=t.prev,r=t,n=t.next;if(p(e,r,n)>=0)return!1;for(var i=t.next.next;i!==t.prev;){if(v(e.x,e.y,r.x,r.y,n.x,n.y,i.x,i.y)&&p(i.prev,i,i.next)>=0)return!1;i=i.next}return!0}function o(t,e,r,n){var i=t.prev,o=t,s=t.next;if(p(i,o,s)>=0)return!1;for(var a=i.x<o.x?i.x<s.x?i.x:s.x:o.x<s.x?o.x:s.x,l=i.y<o.y?i.y<s.y?i.y:s.y:o.y<s.y?o.y:s.y,u=i.x>o.x?i.x>s.x?i.x:s.x:o.x>s.x?o.x:s.x,x=i.y>o.y?i.y>s.y?i.y:s.y:o.y>s.y?o.y:s.y,h=f(a,l,e,r,n),y=f(u,x,e,r,n),c=t.prevZ,d=t.nextZ;c&&c.z>=h&&d&&d.z<=y;){if(c!==t.prev&&c!==t.next&&v(i.x,i.y,o.x,o.y,s.x,s.y,c.x,c.y)&&p(c.prev,c,c.next)>=0)return!1;if(c=c.prevZ,d!==t.prev&&d!==t.next&&v(i.x,i.y,o.x,o.y,s.x,s.y,d.x,d.y)&&p(d.prev,d,d.next)>=0)return!1;d=d.nextZ}for(;c&&c.z>=h;){if(c!==t.prev&&c!==t.next&&v(i.x,i.y,o.x,o.y,s.x,s.y,c.x,c.y)&&p(c.prev,c,c.next)>=0)return!1;c=c.prevZ}for(;d&&d.z<=y;){if(d!==t.prev&&d!==t.next&&v(i.x,i.y,o.x,o.y,s.x,s.y,d.x,d.y)&&p(d.prev,d,d.next)>=0)return!1;d=d.nextZ}return!0}function s(t,e,n){var i=t;do{var o=i.prev,s=i.next.next;!_(o,s)&&T(o,i,i.next,s)&&b(o,s)&&b(s,o)&&(e.push(o.i/n),e.push(i.i/n),e.push(s.i/n),R(i),R(i.next),i=t=s),i=i.next}while(i!==t);return r(i)}function a(t,e,i,o,s,a){var l=t;do{for(var u=l.next.next;u!==l.prev;){if(l.i!==u.i&&g(l,u)){var x=A(l,u);return l=r(l,l.next),x=r(x,x.next),n(l,e,i,o,s,a),void n(x,e,i,o,s,a)}u=u.next}l=l.next}while(l!==t)}function l(t,n,i,o){var s,a,l,x=[];for(s=0,a=n.length;s<a;s++)(l=e(t,n[s]*o,s<a-1?n[s+1]*o:t.length,o,!1))===l.next&&(l.steiner=!0),x.push(d(l));for(x.sort(u),s=0;s<x.length;s++)i=r(i=h(x[s],i),i.next);return i}function u(t,e){return t.x-e.x}function x(t){if(t.next.prev===t)return t;let e=t;for(;;){const r=e.next;if(r.prev===e||r===e||r===t)break;e=r}return e}function h(t,e){var n=function(t,e){var r,n=e,i=t.x,o=t.y,s=-1/0;do{if(o<=n.y&&o>=n.next.y&&n.next.y!==n.y){var a=n.x+(o-n.y)*(n.next.x-n.x)/(n.next.y-n.y);if(a<=i&&a>s){if(s=a,a===i){if(o===n.y)return n;if(o===n.next.y)return n.next}r=n.x<n.next.x?n:n.next}}n=n.next}while(n!==e);if(!r)return null;if(i===s)return r;var l,u=r,x=r.x,h=r.y,c=1/0;n=r;do{i>=n.x&&n.x>=x&&i!==n.x&&v(o<h?i:s,o,x,h,o<h?s:i,o,n.x,n.y)&&(l=Math.abs(o-n.y)/(i-n.x),b(n,t)&&(l<c||l===c&&(n.x>r.x||n.x===r.x&&y(r,n)))&&(r=n,c=l)),n=n.next}while(n!==u);return r}(t,e);if(!n)return e;var i=A(n,t),o=r(n,n.next);let s=x(i);return r(s,s.next),o=x(o),x(e===n?o:e)}function y(t,e){return p(t.prev,t,e.prev)<0&&p(e.next,t,t.next)<0}function c(t,e,r,n){var i=t;do{null===i.z&&(i.z=f(i.x,i.y,e,r,n)),i.prevZ=i.prev,i.nextZ=i.next,i=i.next}while(i!==t);i.prevZ.nextZ=null,i.prevZ=null,function(t){var e,r,n,i,o,s,a,l,u=1;do{for(r=t,t=null,o=null,s=0;r;){for(s++,n=r,a=0,e=0;e<u&&(a++,n=n.nextZ);e++);for(l=u;a>0||l>0&&n;)0!==a&&(0===l||!n||r.z<=n.z)?(i=r,r=r.nextZ,a--):(i=n,n=n.nextZ,l--),o?o.nextZ=i:t=i,i.prevZ=o,o=i;r=n}o.nextZ=null,u*=2}while(s>1)}(i)}function f(t,e,r,n,i){return(t=1431655765&((t=858993459&((t=252645135&((t=16711935&((t=32767*(t-r)*i)|t<<8))|t<<4))|t<<2))|t<<1))|(e=1431655765&((e=858993459&((e=252645135&((e=16711935&((e=32767*(e-n)*i)|e<<8))|e<<4))|e<<2))|e<<1))<<1}function d(t){var e=t,r=t;do{(e.x<r.x||e.x===r.x&&e.y<r.y)&&(r=e),e=e.next}while(e!==t);return r}function v(t,e,r,n,i,o,s,a){return(i-s)*(e-a)-(t-s)*(o-a)>=0&&(t-s)*(n-a)-(r-s)*(e-a)>=0&&(r-s)*(o-a)-(i-s)*(n-a)>=0}function g(t,e){return t.next.i!==e.i&&t.prev.i!==e.i&&!function(t,e){var r=t;do{if(r.i!==t.i&&r.next.i!==t.i&&r.i!==e.i&&r.next.i!==e.i&&T(r,r.next,t,e))return!0;r=r.next}while(r!==t);return!1}(t,e)&&(b(t,e)&&b(e,t)&&function(t,e){var r=t,n=!1,i=(t.x+e.x)/2,o=(t.y+e.y)/2;do{r.y>o!=r.next.y>o&&r.next.y!==r.y&&i<(r.next.x-r.x)*(o-r.y)/(r.next.y-r.y)+r.x&&(n=!n),r=r.next}while(r!==t);return n}(t,e)&&(p(t.prev,t,e.prev)||p(t,e.prev,e))||_(t,e)&&p(t.prev,t,t.next)>0&&p(e.prev,e,e.next)>0)}function p(t,e,r){return(e.y-t.y)*(r.x-e.x)-(e.x-t.x)*(r.y-e.y)}function _(t,e){return t.x===e.x&&t.y===e.y}function T(t,e,r,n){var i=w(p(t,e,r)),o=w(p(t,e,n)),s=w(p(r,n,t)),a=w(p(r,n,e));return i!==o&&s!==a||!(0!==i||!m(t,r,e))||!(0!==o||!m(t,n,e))||!(0!==s||!m(r,t,n))||!(0!==a||!m(r,e,n))}function m(t,e,r){return e.x<=Math.max(t.x,r.x)&&e.x>=Math.min(t.x,r.x)&&e.y<=Math.max(t.y,r.y)&&e.y>=Math.min(t.y,r.y)}function w(t){return t>0?1:t<0?-1:0}function b(t,e){return p(t.prev,t,t.next)<0?p(t,e,t.next)>=0&&p(t,t.prev,e)>=0:p(t,e,t.prev)<0||p(t,t.next,e)<0}function A(t,e){var r=new U(t.i,t.x,t.y),n=new U(e.i,e.x,e.y),i=t.next,o=e.prev;return t.next=e,e.prev=t,r.next=i,i.prev=r,n.next=r,r.prev=n,o.next=n,n.prev=o,n}function L(t,e,r,n){var i=new U(t,e,r);return n?(i.next=n.next,i.prev=n,n.next.prev=i,n.next=i):(i.prev=i,i.next=i),i}function R(t){t.next.prev=t.prev,t.prev.next=t.next,t.prevZ&&(t.prevZ.nextZ=t.nextZ),t.nextZ&&(t.nextZ.prevZ=t.prevZ)}function U(t,e,r){this.i=t,this.x=e,this.y=r,this.prev=null,this.next=null,this.z=null,this.prevZ=null,this.nextZ=null,this.steiner=!1}function B(t,e,r,n){for(var i=0,o=e,s=r-n;o<r;o+=n)i+=(t[s]-t[o])*(t[o+1]+t[s+1]),s=o;return i}return t.deviation=function(t,e,r,n){var i=e&&e.length,o=i?e[0]*r:t.length,s=Math.abs(B(t,0,o,r));if(i)for(var a=0,l=e.length;a<l;a++){var u=e[a]*r,x=a<l-1?e[a+1]*r:t.length;s-=Math.abs(B(t,u,x,r))}var h=0;for(a=0;a<n.length;a+=3){var y=n[a]*r,c=n[a+1]*r,f=n[a+2]*r;h+=Math.abs((t[y]-t[f])*(t[c+1]-t[y+1])-(t[y]-t[c])*(t[f+1]-t[y+1]))}return 0===s&&0===h?0:Math.abs((h-s)/s)},t.flatten=function(t){for(var e=t[0][0].length,r={vertices:[],holes:[],dimensions:e},n=0,i=0;i<t.length;i++){for(var o=0;o<t[i].length;o++)for(var s=0;s<e;s++)r.vertices.push(t[i][o][s]);i>0&&(n+=t[i-1].length,r.holes.push(n))}return r},t},void 0!==(o=i())&&(n.exports=o)},35270:(t,e,r)=>{r.d(e,{B7:()=>l,St:()=>i,VL:()=>s,h$:()=>o,rW:()=>u});const n={transparent:[0,0,0,0],black:[0,0,0,1],silver:[192,192,192,1],gray:[128,128,128,1],white:[255,255,255,1],maroon:[128,0,0,1],red:[255,0,0,1],purple:[128,0,128,1],fuchsia:[255,0,255,1],green:[0,128,0,1],lime:[0,255,0,1],olive:[128,128,0,1],yellow:[255,255,0,1],navy:[0,0,128,1],blue:[0,0,255,1],teal:[0,128,128,1],aqua:[0,255,255,1],aliceblue:[240,248,255,1],antiquewhite:[250,235,215,1],aquamarine:[127,255,212,1],azure:[240,255,255,1],beige:[245,245,220,1],bisque:[255,228,196,1],blanchedalmond:[255,235,205,1],blueviolet:[138,43,226,1],brown:[165,42,42,1],burlywood:[222,184,135,1],cadetblue:[95,158,160,1],chartreuse:[127,255,0,1],chocolate:[210,105,30,1],coral:[255,127,80,1],cornflowerblue:[100,149,237,1],cornsilk:[255,248,220,1],crimson:[220,20,60,1],cyan:[0,255,255,1],darkblue:[0,0,139,1],darkcyan:[0,139,139,1],darkgoldenrod:[184,134,11,1],darkgray:[169,169,169,1],darkgreen:[0,100,0,1],darkgrey:[169,169,169,1],darkkhaki:[189,183,107,1],darkmagenta:[139,0,139,1],darkolivegreen:[85,107,47,1],darkorange:[255,140,0,1],darkorchid:[153,50,204,1],darkred:[139,0,0,1],darksalmon:[233,150,122,1],darkseagreen:[143,188,143,1],darkslateblue:[72,61,139,1],darkslategray:[47,79,79,1],darkslategrey:[47,79,79,1],darkturquoise:[0,206,209,1],darkviolet:[148,0,211,1],deeppink:[255,20,147,1],deepskyblue:[0,191,255,1],dimgray:[105,105,105,1],dimgrey:[105,105,105,1],dodgerblue:[30,144,255,1],firebrick:[178,34,34,1],floralwhite:[255,250,240,1],forestgreen:[34,139,34,1],gainsboro:[220,220,220,1],ghostwhite:[248,248,255,1],gold:[255,215,0,1],goldenrod:[218,165,32,1],greenyellow:[173,255,47,1],grey:[128,128,128,1],honeydew:[240,255,240,1],hotpink:[255,105,180,1],indianred:[205,92,92,1],indigo:[75,0,130,1],ivory:[255,255,240,1],khaki:[240,230,140,1],lavender:[230,230,250,1],lavenderblush:[255,240,245,1],lawngreen:[124,252,0,1],lemonchiffon:[255,250,205,1],lightblue:[173,216,230,1],lightcoral:[240,128,128,1],lightcyan:[224,255,255,1],lightgoldenrodyellow:[250,250,210,1],lightgray:[211,211,211,1],lightgreen:[144,238,144,1],lightgrey:[211,211,211,1],lightpink:[255,182,193,1],lightsalmon:[255,160,122,1],lightseagreen:[32,178,170,1],lightskyblue:[135,206,250,1],lightslategray:[119,136,153,1],lightslategrey:[119,136,153,1],lightsteelblue:[176,196,222,1],lightyellow:[255,255,224,1],limegreen:[50,205,50,1],linen:[250,240,230,1],magenta:[255,0,255,1],mediumaquamarine:[102,205,170,1],mediumblue:[0,0,205,1],mediumorchid:[186,85,211,1],mediumpurple:[147,112,219,1],mediumseagreen:[60,179,113,1],mediumslateblue:[123,104,238,1],mediumspringgreen:[0,250,154,1],mediumturquoise:[72,209,204,1],mediumvioletred:[199,21,133,1],midnightblue:[25,25,112,1],mintcream:[245,255,250,1],mistyrose:[255,228,225,1],moccasin:[255,228,181,1],navajowhite:[255,222,173,1],oldlace:[253,245,230,1],olivedrab:[107,142,35,1],orange:[255,165,0,1],orangered:[255,69,0,1],orchid:[218,112,214,1],palegoldenrod:[238,232,170,1],palegreen:[152,251,152,1],paleturquoise:[175,238,238,1],palevioletred:[219,112,147,1],papayawhip:[255,239,213,1],peachpuff:[255,218,185,1],peru:[205,133,63,1],pink:[255,192,203,1],plum:[221,160,221,1],powderblue:[176,224,230,1],rebeccapurple:[102,51,153,1],rosybrown:[188,143,143,1],royalblue:[65,105,225,1],saddlebrown:[139,69,19,1],salmon:[250,128,114,1],sandybrown:[244,164,96,1],seagreen:[46,139,87,1],seashell:[255,245,238,1],sienna:[160,82,45,1],skyblue:[135,206,235,1],slateblue:[106,90,205,1],slategray:[112,128,144,1],slategrey:[112,128,144,1],snow:[255,250,250,1],springgreen:[0,255,127,1],steelblue:[70,130,180,1],tan:[210,180,140,1],thistle:[216,191,216,1],tomato:[255,99,71,1],turquoise:[64,224,208,1],violet:[238,130,238,1],wheat:[245,222,179,1],whitesmoke:[245,245,245,1],yellowgreen:[154,205,50,1]};function i(t){return n[t]||n[t.toLowerCase()]}function o(t){return n[t]??n[t.toLowerCase()]}function s(t){return[...o(t)]}function a(t,e,r){r<0&&++r,r>1&&--r;const n=6*r;return n<1?t+(e-t)*n:2*r<1?e:3*r<2?t+(e-t)*(2/3-r)*6:t}function l(t,e,r,n=1){const i=(t%360+360)%360/360,o=r<=.5?r*(e+1):r+e-r*e,s=2*r-o;return[Math.round(255*a(s,o,i+1/3)),Math.round(255*a(s,o,i)),Math.round(255*a(s,o,i-1/3)),n]}function u(t){const e=t.length>5,r=e?8:4,n=(1<<r)-1,i=e?1:17,o=e?9===t.length:5===t.length;let s=Number("0x"+t.substr(1));if(isNaN(s))return null;const a=[0,0,0,1];let l;return o&&(l=s&n,s>>=r,a[3]=i*l/255),l=s&n,s>>=r,a[2]=i*l,l=s&n,s>>=r,a[1]=i*l,l=s&n,s>>=r,a[0]=i*l,a}},79087:(t,e,r)=>{r.d(e,{Z:()=>L});const n=[["(",")"],[")","("],["<",">"],[">","<"],["[","]"],["]","["],["{","}"],["}","{"],["«","»"],["»","«"],["‹","›"],["›","‹"],["⁽","⁾"],["⁾","⁽"],["₍","₎"],["₎","₍"],["≤","≥"],["≥","≤"],["〈","〉"],["〉","〈"],["﹙","﹚"],["﹚","﹙"],["﹛","﹜"],["﹜","﹛"],["﹝","﹞"],["﹞","﹝"],["﹤","﹥"],["﹥","﹤"]],i=["آ","أ","إ","ا"],o=["ﻵ","ﻷ","ﻹ","ﻻ"],s=["ﻶ","ﻸ","ﻺ","ﻼ"],a=["ا","ب","ت","ث","ج","ح","خ","د","ذ","ر","ز","س","ش","ص","ض","ط","ظ","ع","غ","ف","ق","ك","ل","م","ن","ه","و","ي","إ","أ","آ","ة","ى","ل","م","ن","ه","و","ي","إ","أ","آ","ة","ى","ی","ئ","ؤ"],l=["ﺍ","ﺏ","ﺕ","ﺙ","ﺝ","ﺡ","ﺥ","ﺩ","ﺫ","ﺭ","ﺯ","ﺱ","ﺵ","ﺹ","ﺽ","ﻁ","ﻅ","ﻉ","ﻍ","ﻑ","ﻕ","ﻙ","ﻝ","ﻡ","ﻥ","ﻩ","ﻭ","ﻱ","ﺇ","ﺃ","ﺁ","ﺓ","ﻯ","ﯼ","ﺉ","ﺅ","ﹰ","ﹲ","ﹴ","ﹶ","ﹸ","ﹺ","ﹼ","ﹾ","ﺀ","ﺉ","ﺅ"],u=["ﺎ","ﺐ","ﺖ","ﺚ","ﺞ","ﺢ","ﺦ","ﺪ","ﺬ","ﺮ","ﺰ","ﺲ","ﺶ","ﺺ","ﺾ","ﻂ","ﻆ","ﻊ","ﻎ","ﻒ","ﻖ","ﻚ","ﻞ","ﻢ","ﻦ","ﻪ","ﻮ","ﻲ","ﺈ","ﺄ","ﺂ","ﺔ","ﻰ","ﯽ","ﺊ","ﺆ","ﹰ","ﹲ","ﹴ","ﹶ","ﹸ","ﹺ","ﹼ","ﹾ","ﺀ","ﺊ","ﺆ"],x=["ﺎ","ﺒ","ﺘ","ﺜ","ﺠ","ﺤ","ﺨ","ﺪ","ﺬ","ﺮ","ﺰ","ﺴ","ﺸ","ﺼ","ﻀ","ﻄ","ﻈ","ﻌ","ﻐ","ﻔ","ﻘ","ﻜ","ﻠ","ﻤ","ﻨ","ﻬ","ﻮ","ﻴ","ﺈ","ﺄ","ﺂ","ﺔ","ﻰ","ﯿ","ﺌ","ﺆ","ﹱ","ﹲ","ﹴ","ﹷ","ﹹ","ﹻ","ﹽ","ﹿ","ﺀ","ﺌ","ﺆ"],h=["ﺍ","ﺑ","ﺗ","ﺛ","ﺟ","ﺣ","ﺧ","ﺩ","ﺫ","ﺭ","ﺯ","ﺳ","ﺷ","ﺻ","ﺿ","ﻃ","ﻇ","ﻋ","ﻏ","ﻓ","ﻗ","ﻛ","ﻟ","ﻣ","ﻧ","ﻫ","ﻭ","ﻳ","ﺇ","ﺃ","ﺁ","ﺓ","ﻯ","ﯾ","ﺋ","ﺅ","ﹰ","ﹲ","ﹴ","ﹶ","ﹸ","ﹺ","ﹼ","ﹾ","ﺀ","ﺋ","ﺅ"],y=["ء","آ","أ","ؤ","إ","ا","ة","د","ذ","ر","ز","و","ى"],c=["ً","ً","ٌ","؟","ٍ","؟","َ","َ","ُ","ُ","ِ","ِ","ّ","ّ","ْ","ْ","ء","آ","آ","أ","أ","ؤ","ؤ","إ","إ","ئ","ئ","ئ","ئ","ا","ا","ب","ب","ب","ب","ة","ة","ت","ت","ت","ت","ث","ث","ث","ث","ج","ج","ج","ج","ح","ح","ح","ح","خ","خ","خ","خ","د","د","ذ","ذ","ر","ر","ز","ز","س","س","س","س","ش","ش","ش","ش","ص","ص","ص","ص","ض","ض","ض","ض","ط","ط","ط","ط","ظ","ظ","ظ","ظ","ع","ع","ع","ع","غ","غ","غ","غ","ف","ف","ف","ف","ق","ق","ق","ق","ك","ك","ك","ك","ل","ل","ل","ل","م","م","م","م","ن","ن","ن","ن","ه","ه","ه","ه","و","و","ى","ى","ي","ي","ي","ي","ﻵ","ﻶ","ﻷ","ﻸ","ﻹ","ﻺ","ﻻ","ﻼ","؟","؟","؟"],f=["ء","ف"],d=["غ","ي"],v=[[0,3,0,1,0,0,0],[0,3,0,1,2,2,0],[0,3,0,17,2,0,1],[0,3,5,5,4,1,0],[0,3,21,21,4,0,1],[0,3,5,5,4,2,0]],g=[[2,0,1,1,0,1,0],[2,0,1,1,0,2,0],[2,0,2,1,3,2,0],[2,0,2,33,3,1,1]],p=10,_=11,T=12,m=18,w=["UBAT_L","UBAT_R","UBAT_EN","UBAT_AN","UBAT_ON","UBAT_B","UBAT_S","UBAT_AL","UBAT_WS","UBAT_CS","UBAT_ES","UBAT_ET","UBAT_NSM","UBAT_LRE","UBAT_RLE","UBAT_PDF","UBAT_LRO","UBAT_RLO","UBAT_BN"],b=[100,0,0,0,0,101,102,103,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,104,4,4,4,0,4,0,4,0,4,4,4,0,0,4,4,0,0,0,0,0,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,0,0,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,0,0,0,0,0,0,0,0,0,0,0,0,0,0,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,0,0,4,4,0,0,4,4,0,0,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,0,0,0,105,7,7,106,107],A=[[m,m,m,m,m,m,m,m,m,6,5,6,8,5,m,m,m,m,m,m,m,m,m,m,m,m,m,m,5,5,5,6,8,4,4,_,_,_,4,4,4,4,4,p,9,p,9,9,2,2,2,2,2,2,2,2,2,2,9,4,4,4,4,4,4,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,4,4,4,4,4,4,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,4,4,4,4,m,m,m,m,m,m,5,m,m,m,m,m,m,m,m,m,m,m,m,m,m,m,m,m,m,m,m,m,m,m,m,m,m,9,4,_,_,_,_,4,4,4,4,0,4,4,m,4,4,_,_,2,2,4,0,4,4,4,2,0,4,4,4,4,4,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,4,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,4,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,4,4,4,4,4,4,4,4,4,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,4,4,0,0,0,0,0,0,0,4,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,4,0,4,4,4,4,4,4,4,T,T,T,T,T,T,T,T,T,T,T,T,T,T,T,T,T,T,T,T,T,T,T,T,T,T,T,T,T,T,T,T,T,T,T,T,T,T,T,T,T,T,T,T,T,1,T,1,T,T,1,T,T,1,T,4,4,4,4,4,4,4,4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,4,4,4,4,4,1,1,1,1,1,4,4,4,4,4,4,4,4,4,4,4],[3,3,3,3,4,4,4,4,7,_,_,7,9,7,4,4,T,T,T,T,T,T,T,T,T,T,T,7,4,4,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,T,T,T,T,T,T,T,T,T,T,T,T,T,T,T,T,T,T,T,T,T,3,3,3,3,3,3,3,3,3,3,_,3,3,7,7,7,T,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,T,T,T,T,T,T,T,3,4,T,T,T,T,T,T,7,7,T,T,4,T,T,T,T,7,7,2,2,2,2,2,2,2,2,2,2,7,7,7,7,7,7],[7,7,7,7,7,7,7,7,7,7,7,7,7,7,4,7,7,T,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,T,T,T,T,T,T,T,T,T,T,T,T,T,T,T,T,T,T,T,T,T,T,T,T,T,T,T,4,4,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,T,T,T,T,T,T,T,T,T,T,T,7,4,4,4,4,4,4,4,4,4,4,4,4,4,4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,T,T,T,T,T,T,T,T,T,1,1,4,4,4,4,1,4,4,4,4,4],[8,8,8,8,8,8,8,8,8,8,8,m,m,m,0,1,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,8,5,13,14,15,16,17,9,_,_,_,_,_,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,9,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,8,m,m,m,m,m,4,4,4,4,4,m,m,m,m,m,m,2,0,4,4,2,2,2,2,2,2,p,p,4,4,4,0,2,2,2,2,2,2,2,2,2,2,p,p,4,4,4,4,0,0,0,0,0,0,0,0,0,0,0,0,0,4,4,4,_,_,_,_,_,_,_,_,_,_,_,_,_,_,_,_,_,_,_,_,_,_,_,_,_,_,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,T,T,T,T,T,T,T,T,T,T,T,T,T,T,T,T,T,T,T,T,T,T,T,T,T,T,T,T,T,T,T,T,T,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4],[0,0,0,0,0,0,0,4,4,4,4,4,4,4,4,4,4,4,4,0,0,0,0,0,4,4,4,4,4,1,T,1,1,1,1,1,1,1,1,1,1,p,1,1,1,1,1,1,1,1,1,1,1,1,1,4,1,1,1,1,1,4,1,4,1,1,4,1,1,4,1,1,1,1,1,1,1,1,1,1,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7],[T,T,T,T,T,T,T,T,T,T,T,T,T,T,T,T,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,T,T,T,T,T,T,T,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,9,4,9,4,4,9,4,4,4,4,4,4,4,4,4,_,4,4,p,p,4,4,4,4,4,_,_,4,4,4,4,4,7,7,7,7,7,4,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,4,4,m],[4,4,4,_,_,_,4,4,4,4,4,p,9,p,9,9,2,2,2,2,2,2,2,2,2,2,9,4,4,4,4,4,4,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,4,4,4,4,4,4,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,4,4,4,4,4,4,4,4,4,4,4,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,4,4,4,0,0,0,0,0,0,4,4,0,0,0,0,0,0,4,4,0,0,0,0,0,0,4,4,0,0,0,4,4,4,_,_,4,4,4,_,_,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4]];class L{constructor(){this.inputFormat="ILYNN",this.outputFormat="VLNNN",this.sourceToTarget=[],this.targetToSource=[],this.levels=[]}bidiTransform(t,e,r){if(this.sourceToTarget=[],this.targetToSource=[],!t)return"";if(function(t,e,r){Y=[],$=[];for(let n=0;n<r;n++)t[n]=n,e[n]=n,Y[n]=n}(this.sourceToTarget,this.targetToSource,t.length),!this.checkParameters(e,r))return t;e=this.inputFormat,r=this.outputFormat;let n=t;const a=X,x=q(e.charAt(1)),y=q(r.charAt(1)),f=("I"===e.charAt(0)?"L":e.charAt(0))+x,d=("I"===r.charAt(0)?"L":r.charAt(0))+y,v=e.charAt(2)+r.charAt(2);a.defInFormat=f,a.defOutFormat=d,a.defSwap=v;const g=R(t,f,d,v,a);let p=!1;return"R"===r.charAt(1)?p=!0:"C"!==r.charAt(1)&&"D"!==r.charAt(1)||(p="rtl"===this.checkContextual(g)),this.sourceToTarget=Y,this.targetToSource=function(t){const e=new Array(t.length);for(let r=0;r<t.length;r++)e[t[r]]=r;return e}(this.sourceToTarget),Q=this.targetToSource,n=e.charAt(3)===r.charAt(3)?g:"S"===r.charAt(3)?function(t,e,r){if(0===e.length)return"";void 0===t&&(t=!0),void 0===r&&(r=!0);const n=(e=String(e)).split("");let i=0,a=1,x=n.length;t||(i=n.length-1,a=-1,x=1);const y=function(t,e,r,n,i){let a=0;const x=[];let y=0;for(let c=e;c*r<n;c+=r)if(S(t[c])||j(t[c])){if("ل"===t[c]&&M(t,c+r,r,n)){t[c]=P(t[c+r],0===a?o:s),c+=r,H(t,c,r,n),i&&(x[y]=c,y++),a=0;continue}const e=t[c];1===a?t[c]=E(t,c+r,r,n)?z(t[c]):I(t[c],u):!0===E(t,c+r,r,n)?t[c]=I(t[c],h):t[c]=I(t[c],l),j(e)||(a=1),!0===O(e)&&(a=0)}else a=0;return x}(n,i,a,x,r);let c="";for(let e=0;e<n.length;e++)r&&V(y,y.length,e)>-1?(W(Q,e,!t,-1),Y.splice(e,1)):c+=n[e];return c}(p,g,!0):function(t,e,r){if(0===t.length)return"";void 0===r&&(r=!0),void 0===e&&(e=!0);let n="";const o=(t=String(t)).split("");for(let s=0;s<t.length;s++){let a=!1;if(o[s]>="ﹰ"&&o[s]<"\ufeff"){const l=t.charCodeAt(s);o[s]>="ﻵ"&&o[s]<="ﻼ"?(e?(s>0&&r&&" "===o[s-1]?n=n.substring(0,n.length-1)+"ل":(n+="ل",a=!0),n+=i[(l-65269)/2]):(n+=i[(l-65269)/2],n+="ل",s+1<t.length&&r&&" "===o[s+1]?s++:a=!0),a&&(W(Q,s,!0,1),Y.splice(s,0,Y[s]))):n+=c[l-65136]}else n+=o[s]}return n}(g,p,!0),this.sourceToTarget=Y,this.targetToSource=Q,this.levels=$,n}_inputFormatSetter(t){if(!et.test(t))throw new Error("dojox/string/BidiEngine: the bidi layout string is wrong!");this.inputFormat=t}_outputFormatSetter(t){if(!et.test(t))throw new Error("dojox/string/BidiEngine: the bidi layout string is wrong!");this.outputFormat=t}checkParameters(t,e){return t?this._inputFormatSetter(t):t=this.inputFormat,e?this._outputFormatSetter(e):e=this.outputFormat,t!==e}checkContextual(t){let e=U(t);if("ltr"!==e&&"rtl"!==e){try{e=document.dir.toLowerCase()}catch(t){}"ltr"!==e&&"rtl"!==e&&(e="ltr")}return e}hasBidiChar(t){return rt.test(t)}}function R(t,e,r,n,i){const o=function(t,e,r){if(void 0===e.inFormat&&(e.inFormat=r.defInFormat),void 0===e.outFormat&&(e.outFormat=r.defOutFormat),void 0===e.swap&&(e.swap=r.defSwap),e.inFormat===e.outFormat)return e;const n=e.inFormat.substring(0,1),i=e.outFormat.substring(0,1);let o,s=e.inFormat.substring(1,4),a=e.outFormat.substring(1,4);return"C"===s.charAt(0)&&(o=U(t),s="ltr"===o||"rtl"===o?o.toUpperCase():"L"===e.inFormat.charAt(2)?"LTR":"RTL",e.inFormat=n+s),"C"===a.charAt(0)&&(o=U(t),"rtl"===o?a="RTL":"ltr"===o?(o=function(t){const e=t.split("");return e.reverse(),U(e.join(""))}(t),a=o.toUpperCase()):a="L"===e.outFormat.charAt(2)?"LTR":"RTL",e.outFormat=i+a),e}(t,{inFormat:e,outFormat:r,swap:n},i);if(o.inFormat===o.outFormat)return t;e=o.inFormat,r=o.outFormat,n=o.swap;const s=e.substring(0,1),a=e.substring(1,4),l=r.substring(0,1),u=r.substring(1,4);if(i.inFormat=e,i.outFormat=r,i.swap=n,"L"===s&&"VLTR"===r){if("LTR"===a)return i.dir=K,B(t,i);if("RTL"===a)return i.dir=tt,B(t,i)}if("V"===s&&"V"===l)return i.dir="RTL"===a?tt:K,F(t,i);if("L"===s&&"VRTL"===r)return"LTR"===a?(i.dir=K,t=B(t,i)):(i.dir=tt,t=B(t,i)),F(t);if("VLTR"===e&&"LLTR"===r)return i.dir=K,B(t,i);if("V"===s&&"L"===l&&a!==u)return t=F(t),"RTL"===a?R(t,"LLTR","VLTR",n,i):R(t,"LRTL","VRTL",n,i);if("VRTL"===e&&"LRTL"===r)return R(t,"LRTL","VRTL",n,i);if("L"===s&&"L"===l){const e=i.swap;return i.swap=e.substr(0,1)+"N","RTL"===a?(i.dir=tt,t=B(t,i),i.swap="N"+e.substr(1,2),i.dir=K,t=B(t,i)):(i.dir=K,t=B(t,i),i.swap="N"+e.substr(1,2),t=R(t,"VLTR","LRTL",i.swap,i)),t}return t}function U(t){const e=/[A-Za-z\u05d0-\u065f\u066a-\u06ef\u06fa-\u07ff\ufb1d-\ufdff\ufe70-\ufefc]/.exec(t);return e?e[0]<="z"?"ltr":"rtl":""}function B(t,e){const r=t.split(""),n=[];return k(r,n,e),function(t,e,r){if(0!==r.hiLevel&&r.swap.substr(0,1)!==r.swap.substr(1,2))for(let r=0;r<t.length;r++)1===e[r]&&(t[r]=D(t[r]))}(r,n,e),Z(2,r,n,e),Z(1,r,n,e),$=n,r.join("")}function k(t,e,r){const n=t.length,i=r.dir?g:v;let o=0,s=-1;const a=[],l=[];r.hiLevel=r.dir,r.lastArabic=!1,r.hasUbatAl=!1,r.hasUbatB=!1,r.hasUbatS=!1;for(let e=0;e<n;e++)a[e]=N(t[e]);for(let u=0;u<n;u++){const n=o,x=C(t,a,l,u,r);l[u]=x,o=i[n][x];const h=240&o;o&=15;const y=i[o][G];if(e[u]=y,h>0)if(16===h){for(let t=s;t<u;t++)e[t]=1;s=-1}else s=-1;if(i[o][J])-1===s&&(s=u);else if(s>-1){for(let t=s;t<u;t++)e[t]=y;s=-1}5===a[u]&&(e[u]=0),r.hiLevel|=y}r.hasUbatS&&function(t,e,r,n){for(let i=0;i<r;i++)if(6===t[i]){e[i]=n.dir;for(let r=i-1;r>=0&&8===t[r];r--)e[r]=n.dir}}(a,e,n,r)}function N(t){const e=t.charCodeAt(0),r=b[e>>8];return r<100?r:A[r-100][255&e]}function F(t,e){const r=t.split("");if(e){const t=[];k(r,t,e),$=t}return r.reverse(),Y.reverse(),r.join("")}function V(t,e,r){for(let n=0;n<e;n++)if(t[n]===r)return n;return-1}function S(t){for(let e=0;e<f.length;e++)if(t>=f[e]&&t<=d[e])return!0;return!1}function E(t,e,r,n){for(;e*r<n&&j(t[e]);)e+=r;return!!(e*r<n&&S(t[e]))}function M(t,e,r,n){for(;e*r<n&&j(t[e]);)e+=r;let o=" ";if(!(e*r<n))return!1;o=t[e];for(let t=0;t<i.length;t++)if(i[t]===o)return!0;return!1}function Z(t,e,r,n){if(n.hiLevel<t)return;if(1===t&&n.dir===tt&&!n.hasUbatB)return e.reverse(),void Y.reverse();const i=e.length;let o,s,a,l,u,x=0;for(;x<i;){if(r[x]>=t){for(o=x+1;o<i&&r[o]>=t;)o++;for(s=x,a=o-1;s<a;s++,a--)l=e[s],e[s]=e[a],e[a]=l,u=Y[s],Y[s]=Y[a],Y[a]=u;x=o}x++}}function C(t,e,r,n,i){const o=e[n];return{UBAT_L:()=>(i.lastArabic=!1,0),UBAT_R:()=>(i.lastArabic=!1,1),UBAT_ON:()=>4,UBAT_AN:()=>3,UBAT_EN:()=>i.lastArabic?3:2,UBAT_AL:()=>(i.lastArabic=!0,i.hasUbatAl=!0,1),UBAT_WS:()=>4,UBAT_CS:()=>{let t,o;return n<1||n+1>=e.length||2!==(t=r[n-1])&&3!==t||2!==(o=e[n+1])&&3!==o?4:(i.lastArabic&&(o=3),o===t?o:4)},UBAT_ES:()=>2===(n>0?r[n-1]:5)&&n+1<e.length&&2===e[n+1]?2:4,UBAT_ET:()=>{if(n>0&&2===r[n-1])return 2;if(i.lastArabic)return 4;let t=n+1;const o=e.length;for(;t<o&&e[t]===_;)t++;return t<o&&2===e[t]?2:4},UBAT_NSM:()=>{if("VLTR"===i.inFormat){const r=e.length;let i=n+1;for(;i<r&&e[i]===T;)i++;if(i<r){const r=t[n].charCodeAt[0],o=r>=1425&&r<=2303||64286===r,s=e[i];if(o&&(1===s||7===s))return 1}}return n<1||5===e[n-1]?4:r[n-1]},UBAT_B:()=>(i.lastArabic=!0,i.hasUbatB=!0,i.dir),UBAT_S:()=>(i.hasUbatS=!0,4),UBAT_LRE:()=>(i.lastArabic=!1,4),UBAT_RLE:()=>(i.lastArabic=!1,4),UBAT_LRO:()=>(i.lastArabic=!1,4),UBAT_RLO:()=>(i.lastArabic=!1,4),UBAT_PDF:()=>(i.lastArabic=!1,4),UBAT_BN:()=>4}[w[o]]()}function D(t){let e,r=0,i=n.length-1;for(;r<=i;)if(e=Math.floor((r+i)/2),t<n[e][0])i=e-1;else{if(!(t>n[e][0]))return n[e][1];r=e+1}return t}function O(t){for(let e=0;e<y.length;e++)if(y[e]===t)return!0;return!1}function z(t){for(let e=0;e<a.length;e++)if(t===a[e])return x[e];return t}function I(t,e){for(let r=0;r<a.length;r++)if(t===a[r])return e[r];return t}function j(t){return t>="ً"&&t<="ٕ"}function q(t){return"L"===t?"LTR":"R"===t?"RTL":"C"===t?"CLR":"D"===t?"CRL":""}function H(t,e,r,n){for(;e*r<n&&j(t[e]);)e+=r;return e*r<n&&(t[e]=" ",!0)}function P(t,e){for(let r=0;r<i.length;r++)if(t===i[r])return e[r];return t}function W(t,e,r,n){for(let i=0;i<t.length;i++)(t[i]>e||!r&&t[i]===e)&&(t[i]+=n)}let Y=[],Q=[],$=[];const X={dir:0,defInFormat:"LLTR",defoutFormat:"VLTR",defSwap:"YN",inFormat:"LLTR",outFormat:"VLTR",swap:"YN",hiLevel:0,lastArabic:!1,hasUbatAl:!1,hasBlockSep:!1,hasSegSep:!1,defOutFormat:""},G=5,J=6,K=0,tt=1,et=/^[(I|V)][(L|R|C|D)][(Y|N)][(S|N)][N]$/,rt=/[\u0591-\u06ff\ufb1d-\ufefc]/},65390:(t,e,r)=>{r.d(e,{b:()=>l,j:()=>a});var n=r(99880),i=r(80442);let o=null,s=null;async function a(){return o||(o=async function(){const t=(0,i.Z)("esri-csp-restrictions")?await r.e(3852).then(r.bind(r,43852)).then((t=>t.l)):await r.e(4358).then(r.bind(r,84358)).then((t=>t.l));s=await t.load({locateFile:t=>(0,n.V)(`esri/core/libs/libtess/${t}`)})}()),o}function l(t,e){const r=Math.max(t.length,128e3);return s.triangulate(t,e,r)}},87893:(t,e,r)=>{r.d(e,{Z:()=>n});class n{constructor(t=0,e=0,r=0,n=0){this.x=t,this.y=e,this.width=r,this.height=n}get isEmpty(){return this.width<=0||this.height<=0}union(t){this.x=Math.min(this.x,t.x),this.y=Math.min(this.y,t.y),this.width=Math.max(this.width,t.width),this.height=Math.max(this.height,t.height)}}},67327:(t,e,r)=>{r.d(e,{z:()=>c});var n=r(95648),i=r(16534);function o(t,e){return t.x===e.x&&t.y===e.y}function s(t,e){return t.x=e.y,t.y=-e.x,t}function a(t,e){return t.x=-e.y,t.y=e.x,t}function l(t,e){return t.x=e.x,t.y=e.y,t}function u(t,e){return t.x=-e.x,t.y=-e.y,t}function x(t){return Math.sqrt(t.x*t.x+t.y*t.y)}function h(t,e){return t.x*e.x+t.y*e.y}function y(t,e,r,n){return t.x=e.x*r+e.y*n,t.y=e.x*n-e.y*r,t}class c{constructor(t,e,r){this._writeVertex=t,this._writeTriangle=e,this._canUseThinTessellation=r,this._prevNormal={x:void 0,y:void 0},this._nextNormal={x:void 0,y:void 0},this._textureNormalLeft={x:0,y:1},this._textureNormalRight={x:0,y:-1},this._textureNormal={x:void 0,y:void 0},this._joinNormal={x:void 0,y:void 0},this._inner={x:void 0,y:void 0},this._outer={x:void 0,y:void 0},this._roundStart={x:void 0,y:void 0},this._roundEnd={x:void 0,y:void 0},this._startBreak={x:void 0,y:void 0},this._endBreak={x:void 0,y:void 0},this._innerPrev={x:void 0,y:void 0},this._innerNext={x:void 0,y:void 0},this._bevelStart={x:void 0,y:void 0},this._bevelEnd={x:void 0,y:void 0},this._bevelMiddle={x:void 0,y:void 0}}tessellate(t,e){(function(t){if(!t)return;const e=t.length;if(e<=1)return;let r=0;for(let n=1;n<e;n++)o(t[n],t[r])||++r===n||(t[r]=t[n]);t.length=r+1})(t),this._canUseThinTessellation&&e.halfWidth<i.tQ&&!e.offset?this._tessellateThin(t,e):this._tessellate(t,e)}_tessellateThin(t,e){if(t.length<2)return;const r=e.wrapDistance||65535;let n=e.initialDistance||0,i=!1,o=t[0].x,s=t[0].y;const a=t.length;for(let e=1;e<a;++e){i&&(i=!1,n=0);let a=t[e].x,l=t[e].y,u=a-o,x=l-s,h=Math.sqrt(u*u+x*x);if(u/=h,x/=h,n+h>r){i=!0;const t=(r-n)/h;h=r-n,a=(1-t)*o+t*a,l=(1-t)*s+t*l,--e}const y=this._writeVertex(o,s,0,0,u,x,x,-u,0,-1,n),c=this._writeVertex(o,s,0,0,u,x,-x,u,0,1,n);n+=h;const f=this._writeVertex(a,l,0,0,u,x,x,-u,0,-1,n),d=this._writeVertex(a,l,0,0,u,x,-x,u,0,1,n);this._writeTriangle(y,c,f),this._writeTriangle(c,f,d),o=a,s=l}}_tessellate(t,e){const r=t[0],i=t[t.length-1],c=o(r,i),f=c?3:2;if(t.length<f)return;const d=e.pixelCoordRatio,v=null!=e.capType?e.capType:n.RL.BUTT,g=null!=e.joinType?e.joinType:n.AH.MITER,p=null!=e.miterLimit?Math.min(e.miterLimit,4):2,_=null!=e.roundLimit?Math.min(e.roundLimit,1.05):1.05,T=null!=e.halfWidth?e.halfWidth:2,m=!!e.textured;let w,b,A,L=null;const R=this._prevNormal,U=this._nextNormal;let B=-1,k=-1;const N=this._joinNormal;let F,V;const S=this._textureNormalLeft,E=this._textureNormalRight,M=this._textureNormal;let Z=-1,C=-1;const D=e.wrapDistance||65535;let O=e.initialDistance||0;const z=this._writeVertex,I=this._writeTriangle,j=(t,e,r,n,i,o)=>{const s=z(b,A,F,V,r,n,t,e,i,o,O);return Z>=0&&C>=0&&s>=0&&I(Z,C,s),Z=C,C=s,s};c&&(w=t[t.length-2],U.x=i.x-w.x,U.y=i.y-w.y,k=x(U),U.x/=k,U.y/=k);let q=!1;for(let e=0;e<t.length;++e){if(q&&(q=!1,O=0),w&&(R.x=-U.x,R.y=-U.y,B=k,O+B>D&&(q=!0)),q){const r=(D-O)/B;B=D-O,w={x:(1-r)*w.x+r*t[e].x,y:(1-r)*w.y+r*t[e].y},--e}else w=t[e];b=w.x,A=w.y;const r=e<=0&&!q,i=e===t.length-1;if(r||(O+=B),L=i?c?t[1]:null:t[e+1],L?(U.x=L.x-b,U.y=L.y-A,k=x(U),U.x/=k,U.y/=k):(U.x=void 0,U.y=void 0),!c){if(r){a(N,U),F=N.x,V=N.y,v===n.RL.SQUARE&&(j(-U.y-U.x,U.x-U.y,U.x,U.y,0,-1),j(U.y-U.x,-U.x-U.y,U.x,U.y,0,1)),v===n.RL.ROUND&&(j(-U.y-U.x,U.x-U.y,U.x,U.y,-1,-1),j(U.y-U.x,-U.x-U.y,U.x,U.y,-1,1)),v!==n.RL.ROUND&&v!==n.RL.BUTT||(j(-U.y,U.x,U.x,U.y,0,-1),j(U.y,-U.x,U.x,U.y,0,1));continue}if(i){s(N,R),F=N.x,V=N.y,v!==n.RL.ROUND&&v!==n.RL.BUTT||(j(R.y,-R.x,-R.x,-R.y,0,-1),j(-R.y,R.x,-R.x,-R.y,0,1)),v===n.RL.SQUARE&&(j(R.y-R.x,-R.x-R.y,-R.x,-R.y,0,-1),j(-R.y-R.x,R.x-R.y,-R.x,-R.y,0,1)),v===n.RL.ROUND&&(j(R.y-R.x,-R.x-R.y,-R.x,-R.y,1,-1),j(-R.y-R.x,R.x-R.y,-R.x,-R.y,1,1));continue}}let o,f,z=(P=U,-((H=R).x*P.y-H.y*P.x));if(Math.abs(z)<.01)h(R,U)>0?(N.x=R.x,N.y=R.y,z=1,o=Number.MAX_VALUE,f=!0):(a(N,U),z=1,o=1,f=!1);else{N.x=(R.x+U.x)/z,N.y=(R.y+U.y)/z,o=x(N);const t=(o-1)*T*d;f=o>4||t>B&&t>k}F=N.x,V=N.y;let I=g;switch(g){case n.AH.BEVEL:o<1.05&&(I=n.AH.MITER);break;case n.AH.ROUND:o<_&&(I=n.AH.MITER);break;case n.AH.MITER:o>p&&(I=n.AH.BEVEL)}switch(I){case n.AH.MITER:if(j(N.x,N.y,-R.x,-R.y,0,-1),j(-N.x,-N.y,-R.x,-R.y,0,1),i)break;if(m){const t=q?0:O;Z=this._writeVertex(b,A,F,V,U.x,U.y,N.x,N.y,0,-1,t),C=this._writeVertex(b,A,F,V,U.x,U.y,-N.x,-N.y,0,1,t)}break;case n.AH.BEVEL:{const t=z<0;let e,r,n,o;if(t){const t=Z;Z=C,C=t,e=S,r=E}else e=E,r=S;if(f)n=t?a(this._innerPrev,R):s(this._innerPrev,R),o=t?s(this._innerNext,U):a(this._innerNext,U);else{const e=t?u(this._inner,N):l(this._inner,N);n=e,o=e}const x=t?s(this._bevelStart,R):a(this._bevelStart,R);j(n.x,n.y,-R.x,-R.y,e.x,e.y);const h=j(x.x,x.y,-R.x,-R.y,r.x,r.y);if(i)break;const c=t?a(this._bevelEnd,U):s(this._bevelEnd,U);if(f){const t=this._writeVertex(b,A,F,V,-R.x,-R.y,0,0,0,0,O);Z=this._writeVertex(b,A,F,V,U.x,U.y,o.x,o.y,e.x,e.y,O),C=this._writeVertex(b,A,F,V,U.x,U.y,c.x,c.y,r.x,r.y,O),this._writeTriangle(h,t,C)}else{if(m){const t=this._bevelMiddle;t.x=(x.x+c.x)/2,t.y=(x.y+c.y)/2,y(M,t,-R.x,-R.y),j(t.x,t.y,-R.x,-R.y,M.x,M.y),y(M,t,U.x,U.y),Z=this._writeVertex(b,A,F,V,U.x,U.y,t.x,t.y,M.x,M.y,O),C=this._writeVertex(b,A,F,V,U.x,U.y,o.x,o.y,e.x,e.y,O)}else{const t=Z;Z=C,C=t}j(c.x,c.y,U.x,U.y,r.x,r.y)}if(t){const t=Z;Z=C,C=t}break}case n.AH.ROUND:{const t=z<0;let e,r;if(t){const t=Z;Z=C,C=t,e=S,r=E}else e=E,r=S;const n=t?u(this._inner,N):l(this._inner,N);let x,c;f?(x=t?a(this._innerPrev,R):s(this._innerPrev,R),c=t?s(this._innerNext,U):a(this._innerNext,U)):(x=n,c=n);const d=t?s(this._roundStart,R):a(this._roundStart,R),v=t?a(this._roundEnd,U):s(this._roundEnd,U),g=j(x.x,x.y,-R.x,-R.y,e.x,e.y),p=j(d.x,d.y,-R.x,-R.y,r.x,r.y);if(i)break;const _=this._writeVertex(b,A,F,V,-R.x,-R.y,0,0,0,0,O);f||this._writeTriangle(Z,C,_);const T=u(this._outer,n),w=this._writeVertex(b,A,F,V,U.x,U.y,v.x,v.y,r.x,r.y,O);let L,B;const k=o>2;if(k){let e;o!==Number.MAX_VALUE?(T.x/=o,T.y/=o,e=h(R,T),e=(o*(e*e-1)+1)/e):e=-1,L=t?s(this._startBreak,R):a(this._startBreak,R),L.x+=R.x*e,L.y+=R.y*e,B=t?a(this._endBreak,U):s(this._endBreak,U),B.x+=U.x*e,B.y+=U.y*e}y(M,T,-R.x,-R.y);const D=this._writeVertex(b,A,F,V,-R.x,-R.y,T.x,T.y,M.x,M.y,O);y(M,T,U.x,U.y);const I=m?this._writeVertex(b,A,F,V,U.x,U.y,T.x,T.y,M.x,M.y,O):D,q=_,H=m?this._writeVertex(b,A,F,V,U.x,U.y,0,0,0,0,O):_;let P=-1,W=-1;if(k&&(y(M,L,-R.x,-R.y),P=this._writeVertex(b,A,F,V,-R.x,-R.y,L.x,L.y,M.x,M.y,O),y(M,B,U.x,U.y),W=this._writeVertex(b,A,F,V,U.x,U.y,B.x,B.y,M.x,M.y,O)),m?k?(this._writeTriangle(q,p,P),this._writeTriangle(q,P,D),this._writeTriangle(H,I,W),this._writeTriangle(H,W,w)):(this._writeTriangle(q,p,D),this._writeTriangle(H,I,w)):k?(this._writeTriangle(_,p,P),this._writeTriangle(_,P,W),this._writeTriangle(_,W,w)):(this._writeTriangle(_,p,D),this._writeTriangle(_,I,w)),f?(Z=this._writeVertex(b,A,F,V,U.x,U.y,c.x,c.y,e.x,e.y,O),C=w):(Z=m?this._writeVertex(b,A,F,V,U.x,U.y,c.x,c.y,e.x,e.y,O):g,this._writeTriangle(Z,H,w),C=w),t){const t=Z;Z=C,C=t}break}}}var H,P}}}}]);