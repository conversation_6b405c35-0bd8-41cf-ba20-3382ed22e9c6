import request from '@/plugins/axios'
import type { 
  WaterQualityStation, 
  WaterQualityStationDetail, 
  WaterQualityQueryParams, 
  WaterQualityStatistics,
  ApiResponse,
  PageResponse
} from './types'

/**
 * 获取水质监测站点列表
 * @param params 查询参数
 * @returns 
 */
export function getWaterQualityStationList(params?: WaterQualityQueryParams) {
  return request<ApiResponse<PageResponse<WaterQualityStation>>>({
    url: '/istar/api/waterQualityStation/getList',
    method: 'get',
    params: {
      status: '1', // 默认查询正常状态的站点
      ...params
    }
  })
}

/**
 * 获取水质监测站点详细信息
 * @param stationId 站点ID
 * @returns 
 */
export function getWaterQualityStationDetail(stationId: string) {
  return request<ApiResponse<WaterQualityStationDetail>>({
    url: '/istar/api/waterQualityStation/gis/getDataDetail',
    method: 'get',
    params: { stationId }
  })
}

/**
 * 获取水质监测站点实时数据
 * @param params 查询参数
 * @returns 
 */
export function getWaterQualityRealtimeData(params?: WaterQualityQueryParams) {
  return request<ApiResponse<WaterQualityStation[]>>({
    url: '/istar/api/waterQualityStation/getInfoDetail',
    method: 'get',
    params
  })
}

/**
 * 获取水质监测统计数据
 * @param params 查询参数
 * @returns 
 */
export function getWaterQualityStatistics(params?: WaterQualityQueryParams) {
  return request<ApiResponse<WaterQualityStatistics>>({
    url: '/istar/api/waterQualityStation/getStatistics',
    method: 'get',
    params
  })
}

/**
 * 获取水质监测曲线数据
 * @param params 查询参数
 * @returns 
 */
export function getWaterQualityTrendData(params: {
  stationIds: string
  time: string
  queryType: string
  attr?: string
  stationType?: string
}) {
  return request({
    url: '/istar/api/waterQualityStation/getPointMonitor',
    method: 'get',
    params
  })
}

/**
 * 获取水质报表数据
 * @param params 查询参数
 * @returns 
 */
export function getWaterQualityReport(params: {
  stationIds: string
  time: string
  queryType: string
  groupType?: string
  stationType?: string
}) {
  return request({
    url: '/istar/api/waterQualityStation/getReport',
    method: 'get',
    params
  })
}

/**
 * 导出水质报表
 * @param params 查询参数
 * @returns 
 */
export function exportWaterQualityReport(params: {
  stationId: string
  time: string
  queryType: string
  groupType?: string
  stationType?: string
}) {
  return request({
    url: '/istar/api/waterQualityStation/getReport/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 模拟数据生成函数（用于开发测试）
export function generateMockWaterQualityData(): WaterQualityStation[] {
  const mockStations: WaterQualityStation[] = [
    {
      id: 'wq-station-1',
      stationId: 'wq-station-1',
      name: '污水处理厂出水口1',
      stationName: '污水处理厂出水口1',
      location: '116.405285,39.904989',
      longitude: 116.405285,
      latitude: 39.904989,
      status: 1,
      projectId: 'project-1',
      stationType: '水质监测站'
    },
    {
      id: 'wq-station-2',
      stationId: 'wq-station-2',
      name: '污水处理厂出水口2',
      stationName: '污水处理厂出水口2',
      location: '116.407285,39.906989',
      longitude: 116.407285,
      latitude: 39.906989,
      status: 1,
      projectId: 'project-1',
      stationType: '水质监测站'
    },
    {
      id: 'wq-station-3',
      stationId: 'wq-station-3',
      name: '净水厂进水口1',
      stationName: '净水厂进水口1',
      location: '116.409285,39.908989',
      longitude: 116.409285,
      latitude: 39.908989,
      status: 0,
      projectId: 'project-1',
      stationType: '水质监测站'
    }
  ]
  
  return mockStations
}

/**
 * 生成模拟水质指标数据
 * @param stationId 站点ID
 * @returns 
 */
export function generateMockWaterQualityIndicators(stationId: string) {
  const baseValues = {
    'wq-station-1': { cod: 15.2, bod5: 8.5, nh3n: 2.1, tn: 8.7, tp: 0.8, ph: 7.2, ss: 12.3, turbidity: 0.8 },
    'wq-station-2': { cod: 18.7, bod5: 9.2, nh3n: 2.5, tn: 9.3, tp: 0.9, ph: 7.1, ss: 14.1, turbidity: 1.2 },
    'wq-station-3': { cod: 22.1, bod5: 11.8, nh3n: 3.2, tn: 10.5, tp: 1.1, ph: 6.9, ss: 16.7, turbidity: 1.5 }
  }
  
  const values = baseValues[stationId] || baseValues['wq-station-1']
  
  return [
    { stationId, propertyName: 'COD', propertyCode: 'cod', value: values.cod, unit: 'mg/L', status: values.cod <= 50 },
    { stationId, propertyName: 'BOD5', propertyCode: 'bod5', value: values.bod5, unit: 'mg/L', status: values.bod5 <= 10 },
    { stationId, propertyName: '氨氮', propertyCode: 'nh3n', value: values.nh3n, unit: 'mg/L', status: values.nh3n <= 5 },
    { stationId, propertyName: '总氮', propertyCode: 'tn', value: values.tn, unit: 'mg/L', status: values.tn <= 15 },
    { stationId, propertyName: '总磷', propertyCode: 'tp', value: values.tp, unit: 'mg/L', status: values.tp <= 0.5 },
    { stationId, propertyName: 'pH值', propertyCode: 'ph', value: values.ph, unit: '', status: values.ph >= 6.5 && values.ph <= 8.5 },
    { stationId, propertyName: '悬浮物', propertyCode: 'ss', value: values.ss, unit: 'mg/L', status: values.ss <= 10 },
    { stationId, propertyName: '浊度', propertyCode: 'turbidity', value: values.turbidity, unit: 'NTU', status: values.turbidity <= 1 }
  ]
}
