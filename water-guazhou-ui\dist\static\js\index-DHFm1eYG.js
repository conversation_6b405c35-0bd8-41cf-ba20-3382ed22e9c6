import{_ as R}from"./index-C9hz-UZb.js";import{d as G,j as M,M as H,bF as d,r as b,c as y,am as J,a8 as Q,bB as O,s as D,a6 as U,bu as W,ay as $,g as k,n as q,q as u,i as l,F as _,cs as w,an as I,dF as K,dA as X,aq as Y,b7 as Z,aj as ee,C as ae}from"./index-r0dFAfgr.js";import{_ as te}from"./CardSearch-CB_HNR-Q.js";import{l as ne}from"./echart-DP2Lcm9i.js";import{g as re}from"./queryStatistics-CQ9DBM08.js";import{b as oe}from"./zhandian-YaGuQZe6.js";import{u as se}from"./useStation-DJgnSZIA.js";import{f as ie}from"./formartColumn-D5r7JJ2G.js";import"./Search-NSrhrIa_.js";const le={class:"wrapper"},ce={key:1},me=G({__name:"index",setup(de){const v=M(),{$messageWarning:P}=H(),{getStationTree:V}=se();d().date();const t=b({type:"date",chartOption:null,activeName:"echarts",chartName:"",data:null,stationTree:[]}),f=y(),z=y("day"),x=y(),T=y(),N=y();let h=b([]);J(()=>t.activeName,()=>{t.activeName==="echarts"&&L()});const g=b({defaultParams:{queryType:"day",day:[d().format(),d().format()],month:[d().subtract(1,"month"),d().format()]},filters:[{type:"select-tree",label:"监测点:",field:"attributeId",clearable:!1,lazy:!0,options:Q(()=>t.stationTree),lazyLoad:(e,a)=>{var r,s;if(e.level===0)return a([]);if(((r=e.data.children)==null?void 0:r.length)>0)return a(e.data.children);if(e.isLeaf)return a([]);if((s=e.data)!=null&&s.isLeaf)return a([]);oe({stationId:e.data.id}).then(m=>{var n;const p=(n=m.data)==null?void 0:n.map(i=>({label:i.type,value:"",id:"",children:i.attrList.map(o=>({label:o.name+(o.unit?"("+o.unit+")":""),value:o.id,id:o.id,isLeaf:!0}))}));return a(p)})},nodeClick:(e,a)=>{a.isLeaf&&(t.chartName=e.label,O(()=>{S()}))}},{type:"select",label:"比较类型:",field:"queryType",clearable:!1,width:"200px",options:[{label:"日分时(时间段)",value:"day"},{label:"月分日(时间段)",value:"month"}],itemContainerStyle:{width:"240px"},onChange:e=>{F(e)}},{type:"daterange",label:"日期",field:"day",clearable:!1,handleHidden:(e,a,r)=>{r.hidden=e.queryType==="month"}},{type:"monthrange",label:"日期",field:"month",clearable:!1,handleHidden:(e,a,r)=>{r.hidden=e.queryType==="day"}}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",click:()=>{var a;c.pagination.page=1,(((a=f.value)==null?void 0:a.queryParams)||{}).attributeId?S():P("选择监测点")},icon:"iconfont icon-chaxun"},{type:"default",perm:!0,text:"重置",svgIcon:D(Z),click:()=>{var e;(e=f.value)==null||e.resetForm()}},{perm:!0,type:"warning",text:"导出",hide:()=>t.activeName!=="list",svgIcon:D(ee),click:()=>j()}]}]}),A={day:"daterange",month:"monthrange"},F=e=>{var r,s;z.value=e;const a=(r=g.filters)==null?void 0:r.find(m=>m.field==="date");a.type=A[e],e==="monthrange"&&(g.defaultParams={...g.defaultParams,date:[d().add(-1,"month").startOf("month"),d().endOf("month")]},(s=f.value)==null||s.resetForm())},c=b({loading:!1,dataList:[],columns:[],operations:[],pagination:{layout:"total, prev, pager, next,  jumper",handleSize:e=>{c.pagination.limit=e},refreshData:({page:e,size:a})=>{c.pagination.page=e,c.pagination.limit=a,c.dataList=h.slice((e-1)*a,e*a)}}}),S=()=>{var m;c.loading=!0;const e=((m=f.value)==null?void 0:m.queryParams)||{},[a,r]=e[e.queryType]||[],s={attributeId:e.attributeId,queryType:e.queryType,start:a?d(a).startOf(e.queryType).valueOf():"",end:r?d(r).endOf(e.queryType).valueOf():""};re(s).then(p=>{var o;const n=(o=p.data)==null?void 0:o.data;t.data=n;const i=ie(n==null?void 0:n.tableInfo);h=n==null?void 0:n.tableDataList,c.columns=i,c.dataList=h==null?void 0:h.slice(0,20),c.pagination.total=n==null?void 0:n.tableDataList.length,c.loading=!1,L()})},B=()=>{var e;(e=x.value)==null||e.resize()},L=()=>{var m,p,n;const e=ne();e.series=[];const a={name:"",smooth:!0,data:[],type:"line",markPoint:{data:[{type:"max",name:"最大值",label:{fontSize:12,color:v.isDark?"#ffffff":"#000000"}},{type:"min",name:"最小值",label:{color:v.isDark?"#ffffff":"#000000"}}]},markLine:{data:[{type:"average",name:"平均值"}]}};e.xAxis.data=(m=t.data)==null?void 0:m.tableDataList.map(i=>i.ts),(p=t.data)==null||p.tableInfo.map(i=>{var o;if(i.columnValue!=="ts"){const C=JSON.parse(JSON.stringify(a));C.name=i.columnName,e.yAxis[0].name=t.chartName,C.data=(o=t.data)==null?void 0:o.tableDataList.map(E=>E[i.columnValue]),e.series.push(C)}}),(n=x.value)==null||n.clear();const s=U({callOnAdd:!0});O(()=>{T.value&&s.listenTo(T.value,()=>{t.chartOption=e,B()})})},j=()=>{var e;(e=N.value)==null||e.exportTable()};return W(async()=>{const e=await V("水源地");t.stationTree=e,console.log(" state.stationTree ",t.stationTree)}),(e,a)=>{const r=te,s=K,m=X,p=$("VChart"),n=Y,i=R;return k(),q("div",le,[u(r,{ref_key:"cardSearch",ref:f,config:l(g)},null,8,["config"]),u(i,{class:"card",title:l(t).chartName+(l(t).activeName==="list"?"周期对比列表":"周期对比曲线")},{right:_(()=>[u(m,{modelValue:l(t).activeName,"onUpdate:modelValue":a[0]||(a[0]=o=>l(t).activeName=o)},{default:_(()=>[u(s,{label:"echarts"},{default:_(()=>[u(l(w),{style:{"margin-right":"1px","font-size":"16px"},icon:"clarity:line-chart-line"})]),_:1}),u(s,{label:"list"},{default:_(()=>[u(l(w),{style:{"margin-right":"1px","font-size":"16px"},icon:"material-symbols:table"})]),_:1})]),_:1},8,["modelValue"])]),default:_(()=>[l(t).activeName==="echarts"?(k(),q("div",{key:0,ref_key:"echartsDiv",ref:T,class:"chart-box"},[u(p,{ref_key:"refChart",ref:x,theme:l(v).isDark?"dark":"light",option:l(t).chartOption},null,8,["theme","option"])],512)):I("",!0),l(t).activeName==="list"?(k(),q("div",ce,[u(n,{ref_key:"refCardTable",ref:N,class:"card-table",config:l(c)},null,8,["config"])])):I("",!0)]),_:1},8,["title"])])}}}),xe=ae(me,[["__scopeId","data-v-39965090"]]);export{xe as default};
