<template>
  <div class="slide-bar">
    <span class="title">历时24小时 当前时间：{{ curr }}h</span>
    <Icon
      :icon="'ep:arrow-left'"
      :color="'#318DFF'"
      @click="prev"
    ></Icon>
    <Icon
      :icon="playing ? 'ep:video-pause' : 'ep:video-play'"
      :color="'#318DFF'"
      @click="toggle"
    ></Icon>
    <Icon
      :icon="'ep:arrow-right'"
      :color="'#318DFF'"
      @click="next"
    ></Icon>
  </div>
  <el-slider
    v-model="curr"
    :max="23"
    :min="0"
  />
</template>
<script lang="ts" setup>
import { Icon } from '@iconify/vue'

const emit = defineEmits(['progress'])
const playing = ref<boolean>(false)
const curr = ref<number>(0)
const prev = () => {
  if (curr.value > 0) {
    curr.value -= 1
    emit('progress', curr.value)
  }
}
const next = () => {
  if (curr.value < 23) {
    curr.value += 1
    emit('progress', curr.value)
  } else {
    stop()
  }
}
const toggle = () => {
  playing.value = !playing.value
  if (playing.value) {
    start()
  } else {
    stop()
  }
}
let timer = -1
const start = () => {
  timer = setInterval(() => next(), 3000)
}
const stop = () => {
  clearInterval(timer)
}
</script>
<style lang="scss" scoped>
.slide-bar {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 10px 0 0;
  .iconify {
    cursor: pointer;
  }
  .title {
    line-height: 2;
    margin-right: auto;
  }
}
</style>
