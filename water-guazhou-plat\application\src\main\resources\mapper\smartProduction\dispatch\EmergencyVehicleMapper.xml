<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartProduction.dispatch.EmergencyVehicleMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->id,
                           number_plate,
                           car_brand,
                           use_period,
                           car_user_id,
                           status,
                           sim_num,
                           server_time,
                           gprs_time,
                           now_speed,
                           direction,
                           mileage_day,
                           mileage_total,
                           creator,
                           create_time,
                           tenant_id<!--@sql from sp_emergency_vehicle -->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.smartProduction.dispatch.EmergencyVehicle">
        <result column="id" property="id"/>
        <result column="number_plate" property="numberPlate"/>
        <result column="car_brand" property="carBrand"/>
        <result column="use_period" property="usePeriod"/>
        <result column="car_user_id" property="carUserId"/>
        <result column="status" property="status"/>
        <result column="sim_num" property="simNum"/>
        <result column="server_time" property="serverTime"/>
        <result column="gprs_time" property="gprsTime"/>
        <result column="now_speed" property="nowSpeed"/>
        <result column="direction" property="direction"/>
        <result column="mileage_day" property="mileageDay"/>
        <result column="mileage_total" property="mileageTotal"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sp_emergency_vehicle
        <where>
            <if test="numberPlate != null and numberPlate != ''">
                and number_plate like '%'||#{numberPlate}||'%'
            </if>
            <if test="fromTime != null">
                and create_time >= #{fromTime}
            </if>
            <if test="toTime != null">
                and create_time &lt;= #{toTime}
            </if>
            and tenant_id = #{tenantId}
        </where>
        order by create_time desc
    </select>

    <update id="update">
        update sp_emergency_vehicle
        <set>
            <if test="numberPlate != null">
                number_plate = #{numberPlate},
            </if>
            <if test="carBrand != null">
                car_brand = #{carBrand},
            </if>
            <if test="usePeriod != null">
                use_period = #{usePeriod},
            </if>
            <if test="carUserId != null">
                car_user_id = #{carUserId},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="simNum != null">
                sim_num = #{simNum},
            </if>
            <if test="serverTime != null">
                server_time = #{serverTime},
            </if>
            <if test="gprsTime != null">
                gprs_time = #{gprsTime},
            </if>
            <if test="nowSpeed != null">
                now_speed = #{nowSpeed},
            </if>
            <if test="direction != null">
                direction = #{direction},
            </if>
            <if test="mileageDay != null">
                mileage_day = #{mileageDay},
            </if>
            <if test="mileageTotal != null">
                mileage_total = #{mileageTotal},
            </if>
        </set>
        where id = #{id}
    </update>
</mapper>