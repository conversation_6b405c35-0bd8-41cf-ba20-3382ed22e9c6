<template>
  <div class="scroll-table">
    <div class="table-header">
      <div v-for="(column, index) in resolvedColumns" :key="index" class="table-col" :style="{ flex: column.width || 1 }">
        {{ column.title }}
      </div>
    </div>
    <div class="table-body" ref="scrollContainer">
      <div class="table-content" :style="{ transform: `translateY(${scrollTop}px)` }">
        <div class="table-row" v-for="(item, index) in list" :key="index">
          <div v-for="(column, colIndex) in resolvedColumns" :key="colIndex" class="table-col" :style="{ flex: column.width || 1 }">
            <template v-if="column.slot">
              <slot :name="column.slot" :row="item" :index="index"></slot>
            </template>
            <template v-else-if="column.key === 'action'">
              <button class="detail-btn" @click="handleViewDetail(item)">{{ column.btnText || '查看' }}</button>
            </template>
            <template v-else>
              {{ item[column.key] }}
            </template>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch, computed } from 'vue'

const props = defineProps({
  // 数据列表
  list: {
    type: Array,
    default: () => []
  },
  // 列配置（可选）
  columns: {
    type: Array,
    default: () => []
  },
  // 是否自动生成表头（当columns为空时）
  autoGenerateColumns: {
    type: Boolean,
    default: true
  },
  // 操作列配置
  actionColumn: {
    type: Object,
    default: () => ({ key: 'action', title: '详情', btnText: '查看', width: 1 })
  },
  // 每行停留时间(毫秒)
  rowStayTime: {
    type: Number,
    default: 3000
  },
  // 滚动动画时间(毫秒)
  scrollAnimationTime: {
    type: Number,
    default: 500
  },
  // 是否启用自动滚动
  autoScroll: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['view-detail'])

// 自动生成或使用传入的列配置
const resolvedColumns = computed(() => {
  // 如果有传入columns且不为空，直接使用
  if (props.columns && props.columns.length > 0) {
    return props.columns
  }
  
  // 否则自动生成
  if (props.autoGenerateColumns && props.list.length > 0) {
    const firstItem = props.list[0]
    const generatedColumns = Object.keys(firstItem).map(key => {
      // 将字段名转换为更友好的标题（首字母大写）
      const title = key.charAt(0).toUpperCase() + key.slice(1)
      
      // event字段给1.5倍宽度
      const width = key === 'event' ? 1.5 : 1
      
      return { key, title, width }
    })
    
    // 添加操作列
    if (props.actionColumn) {
      generatedColumns.push(props.actionColumn)
    }
    
    return generatedColumns
  }
  
  // 默认的空列表
  return []
})

const scrollContainer = ref(null)
const scrollTop = ref(0)
let scrollTimer = null
let isHovering = false
let rowHeight = 40 // 默认行高
let isAnimating = false

// 处理查看详情事件
const handleViewDetail = (item) => {
  emit('view-detail', item)
}

// 计算行高
const calculateRowHeight = () => {
  if (scrollContainer.value && props.list.length > 0) {
    const rows = scrollContainer.value.querySelectorAll('.table-row')
    if (rows.length > 0) {
      rowHeight = rows[0].offsetHeight
    }
  }
}

// 滚动到下一行
const scrollToNextRow = () => {
  if (isHovering || isAnimating || props.list.length <= 3) return
  
  isAnimating = true
  
  // 设置CSS过渡动画时间
  const contentEl = scrollContainer.value.querySelector('.table-content')
  contentEl.style.transition = `transform ${props.scrollAnimationTime}ms ease-in-out`
  
  // 滚动一行的高度
  scrollTop.value = -rowHeight
  
  // 动画结束后重置位置并移动数据
  setTimeout(() => {
    // 临时禁用过渡动画
    contentEl.style.transition = 'none'
    
    // 将第一行数据移到末尾
    const firstItem = props.list.shift()
    props.list.push(firstItem)
    
    // 重置滚动位置
    scrollTop.value = 0
    
    // 重新启用过渡动画（下一帧）
    setTimeout(() => {
      contentEl.style.transition = `transform ${props.scrollAnimationTime}ms ease-in-out`
      isAnimating = false
    }, 50)
  }, props.scrollAnimationTime)
}

// 启动自动滚动
const startAutoScroll = () => {
  if (!props.autoScroll) return
  
  scrollTimer = setInterval(() => {
    scrollToNextRow()
  }, props.rowStayTime + props.scrollAnimationTime)
}

// 鼠标悬停时暂停滚动
const handleMouseEnter = () => {
  isHovering = true
}

// 鼠标离开时恢复滚动
const handleMouseLeave = () => {
  isHovering = false
}

// 组件挂载时启动自动滚动
onMounted(() => {
  if (scrollContainer.value) {
    scrollContainer.value.addEventListener('mouseenter', handleMouseEnter)
    scrollContainer.value.addEventListener('mouseleave', handleMouseLeave)
    
    // 等待DOM渲染完成后计算行高并启动滚动
    setTimeout(() => {
      calculateRowHeight()
      startAutoScroll()
    }, 500)
  }
})

// 组件卸载前清除定时器和事件监听
onBeforeUnmount(() => {
  if (scrollTimer) {
    clearInterval(scrollTimer)
  }
  if (scrollContainer.value) {
    scrollContainer.value.removeEventListener('mouseenter', handleMouseEnter)
    scrollContainer.value.removeEventListener('mouseleave', handleMouseLeave)
  }
})

// 监听列表变化，重新启动滚动
watch(() => props.list, () => {
  if (scrollTimer) {
    clearInterval(scrollTimer)
  }
  scrollTop.value = 0
  isAnimating = false
  
  // 等待DOM渲染完成后重新计算并启动滚动
  setTimeout(() => {
    calculateRowHeight()
    startAutoScroll()
  }, 500)
}, { deep: true })
</script>

<style lang="scss" scoped>
.scroll-table {
  width: 100%;
  height: 100%;
  color: #fff;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  font-size: 14px;
  .table-header {
    display: flex;
    background: rgba(26, 159, 255, 0.3);
    padding: 10px 0;
    border-radius: 4px 4px 0 0;
    color: #47EBEB;
  }
  
  .table-body {
    flex: 1;
    overflow: hidden;
    position: relative;
  }
  
  .table-content {
    position: absolute;
    width: 100%;
    will-change: transform;
  }
  
  .table-row {
    display: flex;
    // background: rgba(0, 40, 80, 0.5);
    background: transparent;
    border-bottom: 1px solid rgba(26, 159, 255, 0.3);
    padding: 8px 0;
    
    &:hover {
    //   background: rgba(0, 60, 100, 0.7);
    }
  }
  
  .table-col {
    padding: 0 10px;
    text-align: center;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    
    &:first-child {
      text-align: left;
    }
  }
  
  .detail-btn {
    background: transparent;
    border: none;
    cursor: pointer;
    padding: 2px 10px;
    border-radius: 2px;
    color: #fff;
    &:hover {
      background: rgba(56, 182, 255, 0.2);
    }
  }
}
</style> 