package org.thingsboard.server.dao.model.sql.store;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.sql.department.StoreMapper;
import org.thingsboard.server.dao.sql.supplier.SupplierMapper;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseTenantName;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseViaMapper;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.util.Date;

@Getter
@Setter
@ResponseEntity
public class DeviceStorageJournal {
    // id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    // 设备编码
    private String serialId;

    // 设备标签
    private String deviceLabelCode;

    // 入库单编码
    private String storeInCode;

    // 供应商ID
    @ParseViaMapper(SupplierMapper.class)
    private String supplierId;

    // 报废时间
    private Date scrappedTime;

    // 所在仓库
    @ParseViaMapper(StoreMapper.class)
    private String storehouseId;

    // 所在货架
    private String shelvesId;

    // 对应的出库到的出库单条目
    private String storeOutItemId;

    // 最后保养时间
    private Date lastMaintainanceTime;

    // 最后巡检时间
    private Date lastInspectionTime;

    // 出库时间
    private Date outTime;

    private String isCollect;

    // 租户ID
    @ParseTenantName
    private String tenantId;
}
