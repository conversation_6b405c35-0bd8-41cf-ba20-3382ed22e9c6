<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartProduction.dispatch.EmergencyPlanMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->id,
                           name,
                           station_id,
                           (select name from tb_station where id = station_id) station_name,
                           remark,
                           content,
                           creator,
                           create_time,
                           tenant_id<!--@sql from sp_emergency_plan -->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.smartProduction.dispatch.EmergencyPlan">
        <result column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="station_id" property="stationId"/>
        <result column="station_name" property="stationName"/>
        <result column="remark" property="remark"/>
        <result column="content" property="content"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sp_emergency_plan
        <where>
            <if test="name != null and name != ''">
                and "name" like '%' || #{name} || '%'
            </if>
            <if test="stationId != null and stationId != ''">
                and (station_id = #{stationId} or tb_station_is_station_project(station_id, #{stationId}))
            </if>
            <if test="fromTime != null">
                and create_time >= #{fromTime}
            </if>
            <if test="toTime != null">
                and create_time &lt;= #{toTime}
            </if>
            and tenant_id = #{tenantId}
        </where>
        order by create_time desc
    </select>

    <update id="update">
        update sp_emergency_plan
        <set>
            <if test="name != null">
                name = #{name},
            </if>
            <if test="stationId != null">
                station_id = #{stationId},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="content != null">
                content = #{content},
            </if>
        </set>
        where id = #{id}
    </update>
</mapper>