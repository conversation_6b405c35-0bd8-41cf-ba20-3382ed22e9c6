package org.thingsboard.server.dao.sql.shuiwu.assets;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.repository.CrudRepository;
import org.thingsboard.server.dao.model.sql.shuiwu.assets.AssetsTransferEntity;

/**
 * 设备转移
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2021-10-19
 */
public interface AssetsTransferRepository extends CrudRepository<AssetsTransferEntity, String> {
    Page<AssetsTransferEntity> findAllByTransferNoLikeAndProjectIdLikeAndPositionLikeAndDirectorLikeAndApplicantIdLikeAndReviewerIdLikeAndStatusLikeAndTenantIdLikeAndDeviceIdsLikeAndCreateTimeBetween(String transferNo, String projectId, String position, String director, String applicantId, String reviewerId, String status, String tenantId, String deviceId, Long start, Long end, Pageable pageable);
}
