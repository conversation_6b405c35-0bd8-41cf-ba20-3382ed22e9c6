let i=[];const p=t=>(i=[],t.map(s=>{if(s.columnName.indexOf("--")>-1){const o=s.columnName.split("--");o.forEach((e,a)=>{const l={prop:s.columnValue,label:e,align:"center",unit:s.unit?"("+s.unit+")":""},u=r(o[0]);if(u)a===1?(l.unit="",u.subColumns?u.subColumns.find(b=>b.label===e)||u.subColumns.push(l):u.subColumns=[l]):a===2&&u.subColumns.map(n=>{n.subColumns?n.subColumns.find(f=>f.label===e)||n.subColumns.push(l):n.subColumns=[l]});else{const n={prop:s.columnValue,label:e,align:"center",unit:""};i.push(n)}})}else{const o={prop:s.columnValue,label:s.columnName,align:"center",unit:s.unit?"("+s.unit+")":""};i.push(o)}}),i),r=t=>i.find(s=>s.label===t);export{p as f};
