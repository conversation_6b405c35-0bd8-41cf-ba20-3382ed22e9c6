/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.service.obtain;

import com.influxdb.query.FluxTable;
import org.thingsboard.server.common.data.Utils.DateUtils;
import org.thingsboard.server.common.data.Utils.TimeDiff;
import org.thingsboard.server.common.data.constantsAttribute.LoocalMap;
import org.thingsboard.server.common.data.inputKv.InputKv;
import org.thingsboard.server.common.data.telemetryAttribute.ResponseTs;

import java.math.BigDecimal;
import java.util.*;

import static org.thingsboard.server.common.data.Utils.DateUtils.DAY;
import static org.thingsboard.server.common.data.Utils.DateUtils.MONTH;
import static org.thingsboard.server.common.data.Utils.DateUtils.YEAR;

public class ResponseUtil {
    /**
     * 根据换表时间筛选数据，返回一个时间-数据的集合
     *
     * @param responseTs
     * @param changemeterMap
     * @return
     */
    public static ArrayList<ArrayList<LoocalMap>> handleResponseTs(ResponseTs responseTs, Map<Long, Long> changemeterMap) {
        ArrayList<ArrayList<LoocalMap>> result = new ArrayList<>();
        if (changemeterMap.isEmpty()) {
            ArrayList<LoocalMap> loocalMaps = new ArrayList<>();
            responseTs.getDps().entrySet().forEach(entry -> loocalMaps.add(new LoocalMap(Long.parseLong(entry.getKey()), new BigDecimal(entry.getValue()))));
            result.add(loocalMaps);
        } else {
            ArrayList<LoocalMap> loocalMaps = new ArrayList<>();
            final int[] count = {0};
            responseTs.getDps().entrySet().forEach(entry -> {
                boolean flag = false;
                count[0]++;
                for (Long j : changemeterMap.keySet()) {
                    if (TimeDiff.betweenTwoTimes(Long.parseLong(entry.getKey()), j, changemeterMap.get(j)))
                        flag = true;
                }
                if (!flag)
                    loocalMaps.add(new LoocalMap(Long.parseLong(entry.getKey()), new BigDecimal(entry.getValue())));
                //此处添加当循环到最后一条的时候同样把map添加到返回结果集中
                if ((flag && !loocalMaps.isEmpty()) || (count[0] == responseTs.getDps().size() && !loocalMaps.isEmpty())) {
                    ArrayList<LoocalMap> loocalMapArrayList = new ArrayList<>();
                    loocalMapArrayList.addAll(loocalMaps);
                    result.add(loocalMapArrayList);
                    loocalMaps.clear();
                }
            });
        }
        return result;
    }

    /**
     * 根据换表时间筛选数据，返回一个时间-数据的集合
     *
     * @param fluxTable
     * @param changemeterMap
     * @return
     */
    public static ArrayList<ArrayList<LoocalMap>> handleResponseTs(FluxTable fluxTable, Map<Long, Long> changemeterMap) {
        ArrayList<ArrayList<LoocalMap>> result = new ArrayList<>();
        if (changemeterMap==null||changemeterMap.isEmpty()) {
            ArrayList<LoocalMap> loocalMaps = new ArrayList<>();
            fluxTable.getRecords().forEach(entry -> loocalMaps.add(new LoocalMap((entry.getTime().toEpochMilli()), new BigDecimal(String.valueOf(entry.getValue())))));
            result.add(loocalMaps);
        } else {
            ArrayList<LoocalMap> loocalMaps = new ArrayList<>();
            final int[] count = {0};
            fluxTable.getRecords().forEach(entry -> {
                boolean flag = false;
                count[0]++;
                for (Long j : changemeterMap.keySet()) {
                    if (TimeDiff.betweenTwoTimes(entry.getTime().toEpochMilli(), j, changemeterMap.get(j))) {
                        flag = true;
                    }
                }
                if (!flag) {
                    loocalMaps.add(new LoocalMap(entry.getTime().toEpochMilli(), new BigDecimal(String.valueOf(entry.getValue()))));
                }
                //此处添加当循环到最后一条的时候同样把map添加到返回结果集中
                if ((flag && !loocalMaps.isEmpty()) || (count[0] == fluxTable.getRecords().size() && !loocalMaps.isEmpty())) {
                    ArrayList<LoocalMap> loocalMapArrayList = new ArrayList<>();
                    loocalMapArrayList.addAll(loocalMaps);
                    result.add(loocalMapArrayList);
                    loocalMaps.clear();
                }
            });
        }
        return result;
    }


    /**
     * 获取输入类型的值（从数据库查询）
     *
     * @param inputKvs
     * @param type
     * @return
     */
    public static LinkedHashMap<String, BigDecimal> getInputKvData(List<InputKv> inputKvs, String type) {
        LinkedHashMap<String, BigDecimal> map = new LinkedHashMap<>();
        inputKvs.forEach(inputKv -> {
            switch (type) {
                case DAY: {
                    map.put(DateUtils.date2Str(new Date(inputKv.getTs()), DateUtils.DATE_FORMATE_DAY), new BigDecimal(inputKv.getValue()));
                    break;
                }
                case MONTH: {
                    String month = DateUtils.date2Str(new Date(inputKv.getTs()), DateUtils.DATE_FORMATE_MONTH);
                    if (map.get(month) != null)
                        map.put(month, map.get(month).add(new BigDecimal(inputKv.getValue())));
                    else
                        map.put(month, new BigDecimal(inputKv.getValue()));
                    break;
                }
                case YEAR: {
                    String year = DateUtils.date2Str(new Date(inputKv.getTs()), DateUtils.DATE_FORMATE_YEAR);
                    if (map.get(year) != null)
                        map.put(year, map.get(year).add(new BigDecimal(inputKv.getValue())));
                    else
                        map.put(year, new BigDecimal(inputKv.getValue()));
                    break;
                }
            }
        });
        return map;

    }
}
