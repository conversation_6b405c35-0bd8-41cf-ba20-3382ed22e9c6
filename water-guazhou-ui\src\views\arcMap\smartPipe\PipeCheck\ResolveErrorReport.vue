<!-- **错误属性上报处理** -->
<template>
  <DrawerBox
    ref="refDrawer"
    :left-drawer="true"
    :right-drawer="true"
    :right-drawer-title="'属性上报处理'"
    :right-drawer-absolute="false"
    :left-drawer-absolute="false"
    :left-drawer-bar-hide="false"
    :right-drawer-width="600"
    :left-drawer-width="400"
    :left-drawer-title="' '"
  >
    <ArcLayout
      @map-loaded="onMapLoaded"
      @pipe-loaded="onPipeLoaded"
    ></ArcLayout>
    <template #right>
      <InlineForm ref="refForm" :config="FormConfig"></InlineForm>
      <div class="attr-table-box">
        <FormTable ref="refTable" :config="TableConfig"></FormTable>
      </div>
    </template>
    <template #left-title>
      <span>{{ customConfig.title }}</span>
    </template>
    <template #left>
      <ErrorPopTable :config="customConfig"></ErrorPopTable>
    </template>
  </DrawerBox>
  <DialogForm ref="refDialogForm" :config="DialogFormConfig"></DialogForm>
</template>
<script lang="ts" setup>
import Graphic from '@arcgis/core/Graphic';
import {
  excuteQuery,
  // from4548Graphic,
  getGraphicLayer,
  gotoAndHighLight,
  initQueryParams,
  setSymbol
} from '@/utils/MapHelper';
import { convertGeoJSONToArcGIS, queryFeatureByIdGeoserver } from '@/utils/geoserver/geoserverUtils';
import { SLConfirm, SLMessage } from '@/utils/Message';
import { GetAllFieldConfig } from '@/api/mapservice/fieldconfig';
import {
  GetErrorReportList,
  PorcessMapErrorUpload
} from '@/api/mapservice/errorReport';
// @ts-ignore
import ErrorPopTable from './components/ErrorPopTable.vue';
import { formatterDateTime } from '@/utils/GlobalHelper';
import { formatDate } from '@/utils/DateFormatter';
import { useGisStore } from '@/store';

// @ts-ignore
const refDrawer = ref();
const refDialogForm = ref<IDialogFormIns>();
const state = reactive<{
  fieldConfig: IGISFieldConfig[];
}>({
  fieldConfig: []
});
const customConfig = ref<{
  hideFooter: boolean;
  row?: any;
  fid?: string;
  layer?: number;
  dataList: any[];
  title: string;
  img?: string;
}>({
  hideFooter: true,
  row: undefined,
  dataList: [],
  title: ''
});
const staticState: {
  view?: __esri.MapView;
  graphicsLayer?: __esri.GraphicsLayer;
  identifyResults: any[];
} = {
  identifyResults: []
};
const refForm = ref<IFormIns>();
const FormConfig = reactive<IFormConfig>({
  labelWidth: 70,
  group: [
    {
      fields: [
        // { type: 'select', label: '上报人', field: 'reporter', options: [] },
        {
          type: 'daterange',
          itemContainerStyle: { width: '350px' },
          label: '上报时间',
          field: 'reportTime'
        },
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: '查询',
              iconifyIcon: 'ep:search',
              click: () => refreshData()
            },
            {
              perm: true,
              text: '重置',
              iconifyIcon: 'ep:refresh',
              type: 'default',
              click: () => resetForm()
            }
          ]
        }
      ]
    }
  ],
  defaultValue: {}
});
const TableConfig = reactive<ITable>({
  dataList: [],
  columns: [
    { minWidth: 100, label: '设备类别', prop: 'layer' },
    { minWidth: 100, label: '上报人', prop: 'uploadUserName' },
    {
      minWidth: 160,
      label: '上报时间',
      prop: 'uploadTime',
      formatter(row, value) {
        return formatDate(value, formatterDateTime);
      }
    },
    {
      minWidth: 90,
      label: '上报内容',
      prop: 'uploadContent',
      align: 'center',
      formItemConfig: {
        type: 'btn-group',
        btns: [
          {
            perm: true,
            text: '查看',
            isTextBtn: true,
            size: 'small',
            styles: {
              width: '100%'
            },
            click: (row) => handleViewDetail(row)
          }
        ]
      }
    }
  ],
  operations: [
    {
      perm: true,
      text: '同意',
      type: 'success',
      disabled: (row) => row.status === '同意' || row.status === '驳回',
      click: (row) => handleResolve(row)
    },
    {
      perm: true,
      text: '驳回',
      type: 'danger',
      disabled: (row) => row.status === '同意' || row.status === '驳回',
      click: (row) => handleReject(row)
    }
  ],
  pagination: {
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page;
      TableConfig.pagination.limit = size;
      refreshData();
    }
  }
});
const handleResolve = (row: any) => {
  SLConfirm('确定同意？', '提示信息')
    .then(async () => {
      try {
        const res = await PorcessMapErrorUpload({
          id: row.id,
          status: '1'
        });
        if (res.data.code === 200) {
          
          SLMessage.success('已同意');
          refreshData();
        } else {
          SLMessage.error('操作失败');
        }
      } catch (error: any) {
        SLMessage.error('操作失败');
      }
    })
    .catch(() => {
      //
    });
};
const handleReject = (row: any) => {
  TableConfig.currentRow = row;
  refDialogForm.value?.openDialog();
};
const refreshData = () => {
  const { reportTime } = refForm.value?.dataForm || {};
  const start = reportTime?.[0];
  let end = reportTime?.[1];
  end && (end = moment(end, 'YYYY-MM-DD').add(1, 'd').format('YYYY-MM-DD'));
  GetErrorReportList({
    page: TableConfig.pagination.page || 1,
    size: TableConfig.pagination.limit || 20,
    beginTime: start,
    endTime: end,
    status: '0'
  })
    .then((res) => {
      const data = res.data.data;
      TableConfig.dataList = data?.data || [];
      TableConfig.pagination.total = data?.total || 0;
    })
    .catch(() => {
      TableConfig.dataList = [];
      TableConfig.pagination.total = 0;
    });
};
const resetForm = () => {
  refForm.value?.resetForm();
};

/**
 * 查看错误上报详情
 * @param row 上报记录行数据
 */
const handleViewDetail = async (row: any) => {
  if (!row.fid) {
    SLMessage.warning('缺少设备ID信息');
    return;
  }

  try {
    // 解析上报内容
    const content: any = row.uploadContent ? JSON.parse(row.uploadContent) : [];

    // 查找图层信息
    let feature: __esri.Graphic | null = null;

    // 判断是否使用GeoServer
    if (window.GIS_SERVER_SWITCH) {
      // GeoServer模式 - 使用封装好的queryFeatureByIdGeoserver函数
      try {
        debugger
        // 调用封装好的函数查询要素
        const result = await queryFeatureByIdGeoserver({
          typeName: row.layer,  // 图层名称
          id: row.fid,          // 要素ID
          workspace: 'guazhou',  // 工作空间名称
          tryObjectId: true     // 尝试使用OBJECTID查询
        });

        // 处理查询结果
        if (result.success && result.feature) {
          // 查询成功，使用返回的要素
          const geoJsonFeature = result.feature;
          const geometry = convertGeoJSONToArcGIS(geoJsonFeature.geometry);

          // 创建Graphic对象
          feature = new Graphic({
            geometry: geometry,
            attributes: geoJsonFeature.properties || {}
          });

          // 设置符号
          feature.symbol = setSymbol(geometry.type);
        } else {
          SLMessage.warning('未找到设备信息');
          return;
        }
      } catch (error) {
        console.error('GeoServer查询失败:', error);
        SLMessage.error('查询设备信息失败');
        return;
      }
    } else {
      // ArcGIS模式 - 使用ArcGIS REST API查询要素
      const layerInfo = useGisStore().gLayerInfos?.find(
        (item) => item.layername === row.layer
      );
      const layerId = layerInfo?.layerid;

      if (!layerInfo) {
        SLMessage.warning('未找到图层信息');
        return;
      }

      // 执行查询
      const res = await excuteQuery(
        window.SITE_CONFIG.GIS_CONFIG.gisService +
          window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService +
          '/' +
          layerId,
        initQueryParams({
          where: 'OBJECTID = ' + row.fid,
          outFields: ['*']
        })
      );

      if (res.features && res.features.length > 0) {
        feature = res.features[0];
        feature.symbol = setSymbol(feature.geometry.type);
      } else {
        SLMessage.warning('未找到设备信息');
        return;
      }
    }

    // 如果找到了要素，显示详情
    if (feature) {
      // 清除之前的图形并添加新的
      staticState.graphicsLayer?.removeAll();
      staticState.graphicsLayer?.add(feature);

      // 高亮显示要素
      await gotoAndHighLight(staticState.view, feature);

      // 获取字段信息
      const field = state.fieldConfig.find((item) => item.layername === row.layer)?.fields || [];

      // 构建数据列表
      const dataList = content
        .filter((item: any) => item.name !== 'img')
        .map((item: any) => {
          const fieldInfo = field.find(
            (o) => o.name.toLocaleLowerCase() === item.name?.toLocaleLowerCase()
          );
          return {
            ...(fieldInfo || {}),
            oldvalue: item.oldvalue,
            newvalue: item.newvalue
          };
        });

      // 更新UI
      staticState.identifyResults = [{ feature, layerName: row.layer, layerId: row.fid }];

      // 设置自定义配置
      customConfig.value = {
        hideFooter: true,
        row,
        img: content.find((item: any) => item.name === 'img')?.newvalue,
        dataList,
        title: row.layer + '(' + (feature.attributes['SID'] || feature.attributes['OBJECTID'] || row.fid) + ')'
      };

      // 打开左侧抽屉
      refDrawer.value?.toggleDrawer('ltr', true);
    }
  } catch (error) {
    console.error('处理错误上报详情失败:', error);
    SLMessage.error('数据错误');
  }
};

const DialogFormConfig = reactive<IDialogFormConfig>({
  group: [
    {
      fields: [{ type: 'input', label: '请输入驳回原因', field: 'remark' }]
    }
  ],
  labelPosition: 'top',
  dialogWidth: 500,
  title: '驳回',
  submit: (params) => {
    SLConfirm('确定驳回吗？', '提示信息')
      .then(async () => {
        DialogFormConfig.submitting = true;
        try {
          const res = await PorcessMapErrorUpload({
            id: TableConfig.currentRow.id,
            status: '2',
            remark: params.remark
          });
          if (res.data.code === 200) {
            SLMessage.success('已驳回');
            refreshData();
            refDialogForm.value?.closeDialog();
          } else {
            SLMessage.error('操作失败');
          }
        } catch (error: any) {
          SLMessage.error('操作失败');
        }
        DialogFormConfig.submitting = false;
      })
      .catch(() => {
        //
      });
  }
});
const initFieldConfig = async () => {
  const res = await GetAllFieldConfig();
  state.fieldConfig = res.data.result || [];
};
const onMapLoaded = async (view: __esri.MapView) => {
  staticState.view = view;
  await initFieldConfig();
  refreshData();
};
const onPipeLoaded = () => {
  staticState.graphicsLayer = getGraphicLayer(staticState.view, {
    id: 'error-report',
    title: '错误属性设备'
  });
};
onMounted(() => {
  initFieldConfig();
  refDrawer.value?.toggleDrawer('rtl', true);
});
</script>
<style lang="scss" scoped>
.attr-table-box {
  height: calc(100% - 50px);
}
</style>
