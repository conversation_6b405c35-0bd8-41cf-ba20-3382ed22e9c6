package org.thingsboard.server.dao.store;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.store.Store;
import org.thingsboard.server.dao.sql.department.StoreMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.store.StorePageRequest;
import org.thingsboard.server.dao.util.imodel.query.store.StoreSaveRequest;

@Service
public class StoreServiceImpl implements StoreService {
    @Autowired
    private StoreMapper mapper;

    @Override
    public IPage<Store> findAllConditional(StorePageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    public Store save(StoreSaveRequest entity) {
        return QueryUtil.saveOrUpdateOneByRequest(entity, mapper::insert, mapper::update);
    }

    @Override
    public boolean update(Store entity) {
        return mapper.update(entity);
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }

    @Override
    public boolean canBeDelete(String id) {
        return mapper.canBeDelete(id);
    }

}
