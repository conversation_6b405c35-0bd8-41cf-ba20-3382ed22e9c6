package org.thingsboard.server.dao.util.imodel.query.smartService.wechat;

import com.alibaba.fastjson.JSON;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartService.wechat.WxMessageRecord;
import org.thingsboard.server.dao.util.imodel.query.AwareCurrentUserUUID;
import org.thingsboard.server.dao.util.imodel.query.AwareTenantUUID;
import org.thingsboard.server.dao.util.imodel.query.IStarHttpRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
@Setter
public class WxTemplateMessageSendRequest implements AwareTenantUUID, AwareCurrentUserUUID {
    public static final String TITLE_FIELD_NAME = "first";

    public static final String TITLE_FIELD_NAME2 = "title";

    public static final String REMARK_FIELD_NAME = "remark";

    public static final String REASON_FIELD_NAME = "reason";

    // 同步停水通知
    @NotNullOrEmpty
    private Boolean syncWaterOff;

    // 点击消息卡片跳转的地址
    private String url = "";

    // 页面模板Id
    @NotNullOrEmpty
    private String templateId;

    // 模板变量键值映射信息JSON
    @NotNullOrEmpty
    private Map<String, ValueColorPair> variables;


    private String sendUserId;

    private String tenantId;

    private List<String> openIdList;

    private String meterBookIds;

    private String dmaIds;

    @Override
    public String valid(IStarHttpRequest request) {
        // if (!variables.containsKey(TITLE_FIELD_NAME)) {
        //     return "标题未传入";
        // }

        ValueColorPair remark = variables.get(REMARK_FIELD_NAME);
        if (remark != null && remark.equals(variables.get(REASON_FIELD_NAME))) {
            return "备注和原因不能相同";
        }

        return null;
    }

    @Override
    public void currentUserId(String uuid) {
        sendUserId = uuid;
    }

    @Override
    public void tenantId(String uuid) {
        tenantId = uuid;
    }

    public WxMessageRecord build() {
        WxMessageRecord wxMessageRecord = new WxMessageRecord();
        wxMessageRecord.setTitle(variables.getOrDefault(TITLE_FIELD_NAME,
                variables.getOrDefault(TITLE_FIELD_NAME2,
                        new ValueColorPair())).getValue());
        wxMessageRecord.setSyncWaterOff(syncWaterOff);
        wxMessageRecord.setTemplate(templateId);
        wxMessageRecord.setUrl(url);
        wxMessageRecord.setDmaIds(dmaIds);
        wxMessageRecord.setMeterBookIds(meterBookIds);
        ValueColorPair reasonData = variables.get(REASON_FIELD_NAME);
        if (reasonData != null) {
            wxMessageRecord.setReason(reasonData.getValue());
        }
        ValueColorPair remarkData = variables.get(REMARK_FIELD_NAME);
        if (remarkData != null) {
            wxMessageRecord.setRemark(remarkData.getValue());
        }
        wxMessageRecord.setSendUserId(sendUserId);
        wxMessageRecord.setTenantId(tenantId);

        Map<String, String> dataMap = variables.keySet().stream()
                .filter(s -> !s.equals(TITLE_FIELD_NAME) && !s.equals(REASON_FIELD_NAME) && !s.equals(REMARK_FIELD_NAME))
                .collect(Collectors.toMap(s -> s, s -> variables.get(s).getValue()));
        wxMessageRecord.setVariables(JSON.toJSONString(dataMap));
        return wxMessageRecord;
    }

}
