package org.thingsboard.server.dao.util.imodel.query.workOrder;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrder;
import org.thingsboard.server.dao.util.imodel.query.IStarHttpRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.UUID;

import static org.thingsboard.server.dao.model.sql.workOrder.WorkOrderStatus.PENDING;

@Getter
@Setter
public class WorkOrderSaveRequest extends WorkOrderAssignRequest {
    // 标题
    @NotNullOrEmpty
    private String title;

    // 来源
    @NotNullOrEmpty
    private String source;

    // 发起人
    // @NotNullOrEmpty
    // private String organizerId;

    // 紧急程度
    @NotNullOrEmpty
    private String level;

    private Integer processLevel;

    private String processLevelLabel;

    // 工单类型
    @NotNullOrEmpty
    private String type;

    // 地址
    @NotNullOrEmpty
    private String address;

    // 接收部门
    private String receiveDepartmentId;

    // 描述/备注
    private String remark;

    // 现场视频，多个用逗号分隔（最多2）
    private String videoUrl;

    // 现场音频，多个用逗号分隔（最多2）
    private String audioUrl;

    // 现场图片，多个用逗号分隔
    private String imgUrl;

    // 其他附件，多个用逗号分隔（最多2）
    private String otherFileUrl;

    // 上报人
    private String uploadUserId;

    // 上报人电话
    private String uploadPhone;

    // 上报人户号
    private String uploadNo;

    // 上报人地址
    private String uploadAddress;

    // 是否直接`派发`
    private String isDirectDispatch;

    // 项目ID
    private String projectId;

    // 抄送人，多个用逗号隔开
    private String ccUserId;

    // 地理位置，目前规则为经纬度使用逗号隔开
    private String coordinate;

    // 地理位置名称
    private String coordinateName;

    @Override
    public String valid(IStarHttpRequest request) {
        if (countComma(videoUrl) > 1)
            return "[videoUrl]最多只能有两个视频";
        if (countComma(audioUrl) > 1)
            return "[audioUrl]最多只能有两个音频";
        if (countComma(otherFileUrl) > 1)
            return "[otherFileUrl]最多只能有两个附件";


        if (parseBoolean(isDirectDispatch))
            return super.valid(request);

        return null;
    }

    private int countComma(String str) {
        if (str == null) {
            return 0;
        }
        byte[] bytes = str.getBytes(StandardCharsets.UTF_8);
        int count = 0;
        for (byte b : bytes) {
            if (b == ',')
                count++;
        }
        return count;
    }


    public WorkOrder buildOrder() {
        WorkOrder order = new WorkOrder();
        // order.setId(genUUID());
        Date current = new Date();
        order.setTitle(title);
        order.setSource(source);
        order.setOrganizerId(currentUserUUID());
        order.setLevel(level);
        order.setType(type);
        order.setAddress(address);
        order.setRemark(remark);
        order.setVideoUrl(videoUrl);
        order.setAudioUrl(audioUrl);
        order.setImgUrl(imgUrl);
        order.setOtherFileUrl(otherFileUrl);
        order.setUploadUserId(uploadUserId == null ? currentUserUUID() : uploadUserId);
        order.setUploadPhone(uploadPhone);
        order.setUploadNo(uploadNo);
        order.setUploadAddress(uploadAddress);
        order.setDirectDispatch(parseBoolean(isDirectDispatch));
        order.setStatus(PENDING);
        order.setCreateTime(current);
        order.setUpdateTime(current);
        order.setProjectId(projectId);
        order.setCcUserId(ccUserId);
        order.setReceiveDepartmentId(receiveDepartmentId);
        order.setCoordinate(coordinate);
        order.setCoordinateName(coordinateName);
        order.setProcessLevel(processLevel);
        order.setProcessLevelLabel(processLevelLabel);

        order.setTenantId(tenantId());
        return order;
    }

}
