import { request } from '@/plugins/axios';

// 分页查询设备采购信息
export const getDevicePurchaseSearch = (params?: {
  page?: number | undefined;
  size?: number | undefined;
  fromTime?: string;
  toTime?: string;
  code?: string;
  title?: string;
  userId?: string;
}) =>
  request({
    url: `/api/devicePurchase`,
    method: 'get',
    params
  });

// 分页查询采购询价
export const getDevicePurchaseItemSearch = (params?: {
  page?: number | undefined;
  size?: number | undefined;
  fromTime?: string;
  toTime?: string;
  code?: string;
  title?: string;
  userId?: string;
}) =>
  request({
    url: `/api/devicePurchaseItem`,
    method: 'get',
    params
  });

// 添加/编辑采购单
export const postDevicePurchase = (params?: {
  id?: string;
  code: string;
  title: string;
  userId: string;
  useWay: string;
  uploadTime: string;
  preTime: string;
  budget: string;
  addRecord: string;
}) =>
  request({
    url: `/api/devicePurchase`,
    method: 'post',
    data: params
  });

// 删除采购单
export const deleteDevicePurchase = (id: string) =>
  request({
    url: `/api/devicePurchase/${id}`,
    method: 'delete'
  });

// 查询设备采购条目
export const getDevicePurchaseItem = (params?: {
  id?: string;
  page?: number | undefined;
  size?: number | undefined;
  fromTime?: string;
  toTime?: string;
}) =>
  request({
    url: `/api/devicePurchaseItem`,
    method: 'get',
    params
  });

// 编辑采购供应商
export const postDevicePurchaseInquiry = (params?: {
  id?: string;
  supplierId?: string;
  contact?: string;
  contactPhone?: string;
  price?: string;
  intentionSupplier?: string;
  file?: string;
}) =>
  request({
    url: `/api/devicePurchaseInquiry`,
    method: 'post',
    data: params
  });

// 完成询价
export const postCompleteInquiry = (id: string) =>
  request({
    url: `/api/devicePurchaseItem/${id}/completeInquiry`,
    method: 'post'
  });

// 分页获取采购询价列表
export const getDevicePurchaseInquiry = (params?: {
  purchaseDetailId?: string;
  page?: number | undefined;
  size?: number | undefined;
  fromTime?: string;
  toTime?: string;
}) =>
  request({
    url: `/api/devicePurchaseInquiry`,
    method: 'get',
    params
  });

// 分页获取设备资产合同列表
export const getDeviceapiContract = (params?: {
  title?: string;
  code?: string;
  page?: number | undefined;
  size?: number | undefined;
  fromTime?: string;
  toTime?: string;
}) =>
  request({
    url: `/api/contract`,
    method: 'get',
    params
  });

// 分页获取设备资产合同详情列表
export const getDeviceapiContractDetail = (params?: {
  page?: number | undefined;
  size?: number | undefined;
  fromTime?: string;
  toTime?: string;
  mainId?: string;
}) =>
  request({
    url: `/api/contractDetail`,
    method: 'get',
    params
  });

// 添加合同
export const postContract = (params?: {
  purchaseId: string;
  code: string;
  title: string;
  file?: string;
}) =>
  request({
    url: `/api/contract`,
    method: 'post',
    data: params
  });

// 导出设备模板
export const exporttemplateFile = () =>
  request({
    url: `/api/contractDetail/templateFile`,
    method: 'get',
    responseType: 'blob'
  });
