package org.thingsboard.server.dao.model.DTO;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 流量分析
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2023-05-04
 */
@Data
public class PartitionFlowAnalysisDTO {

    private String time;

    private List<JSONObject> data = new ArrayList<>();

    private String minPoint;

    private BigDecimal minValue;

    private String maxPoint;

    private BigDecimal maxValue;

    private BigDecimal avgValue = BigDecimal.ZERO;

    private BigDecimal sumValue = BigDecimal.ZERO;

}
