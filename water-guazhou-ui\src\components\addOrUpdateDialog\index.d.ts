interface AOUCloumn {
  type: string
  label: string
  key: string
  rules?: any[]
  disabled?: boolean
  options?: SelectOption[]
  cascaderOptions?: NormalOption[]
  aInfo?: boolean
  search?: boolean
  unit?: string
  multiple?: boolean
  rows?: number | string
  message?: string
  messageStyle?: {
    [propName: string]: string
  }
  allowCreate?: boolean
  replaceKey?: string
  multipleLimit?: number
  defaultFirst?: boolean
  handleChange?: (val: any) => void
  // 文件上传路径
  fileActionUrl?: string
  // 图片上传路径
  imgActionUrl?: string
  value?: tree[]
  props?: any
  returnType?: 'str' | 'arrStr'
  required?: boolean // 仅用来针对特殊自定义表单元素的是否必选，比如地图组件的输入
  limit?: number
  url?: string
  dateType?: any
  range?: boolean
  format?: string
  valFormat?: string
  headers?: any
  activeColor?: string
  inActiveColor?: string
  activeText?: string
  inActiveText?: string
  activeValue?: string | boolean
  inActiveValue?: string | boolean
  disabledDate?: (date: any) => boolean
}

interface AOUConfig {
  visible: boolean
  title: string
  labelWidth?: string
  setSubmitParams?: (params: any) => any
  close: () => void
  open?: () => void
  submit?: (params: any) => AxiosPromise<any>
  baseUrl?: string // 用于其它域的接口请求
  requestHeaders?: { [propName: string]: string }
  addUrl?: string
  editUrl?: string
  externalParams?: {
    [propName: string]: boolean | number | number[] | string | string[] | Date[] | SubParams
  }
  defaultValue?: Record<string, any>
  // {
  //   [propName: string]: boolean | number | number[] | string | string[] | Date[] | SubParams
  // }
  width?: string
  height?: string
  maxHeight?: string
  columns: AOUCloumn[]
}
