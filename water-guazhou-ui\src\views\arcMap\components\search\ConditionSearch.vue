<!-- 条件查询 -->
<template>
  <RightDrawerMap
    ref="refMap"
    title="条件查询"
    @map-loaded="onMaploaded"
  >
    <template #right-title>
      <SchemeHeader
        :title="'条件查询'"
        @scheme-click="scheme.openManagerDialog"
      ></SchemeHeader>
    </template>
    <Form
      ref="refForm"
      :config="FormConfig"
    ></Form>
    <ArcSqlGenerator
      ref="refSql"
      @submit="handleSqlGenerated"
    ></ArcSqlGenerator>
    <SchemeManage
      :ref="scheme.getSchemeManageRef"
      :type="scheme.schemeType.value"
      @row-click="handleUseScheme"
    ></SchemeManage>
    <SaveScheme
      :ref="scheme.getSaveSchemeRef"
      @submit="handleSchemeSubmit"
    ></SaveScheme>
  </RightDrawerMap>
</template>
<script lang="ts" setup>
import Graphic from '@arcgis/core/Graphic'
import { cloneDeep } from 'lodash-es'
import { getGraphicLayer, getSubLayerIds } from '@/utils/MapHelper'
import { queryLayerClassName } from '@/api/mapservice'
import { SLMessage } from '@/utils/Message'
import RightDrawerMap from '../common/RightDrawerMap.vue'
import { OCalcOperator, OGisConditionFields, OGisConditionLogic } from '@/utils/arcgis/utils/SqlHelper'
import { queryMultiLayerForIds } from '@/utils/arcgis/utils/QueryHelper'
import SchemeHeader from './Scheme/SchemeHeader.vue'
import SchemeManage from './Scheme/SchemeManage.vue'
import SaveScheme from './Scheme/SaveScheme.vue'
import { useScheme, useSketch } from '@/hooks/arcgis'

const refSql = ref<IArcSqlGeneratorIns>()
const refMap = ref<InstanceType<typeof RightDrawerMap>>()
const refForm = ref<IFormIns>()
const state = reactive<{
  tabs: any[]
  curOperate: string
  layerInfos: any[]
  layerIds: any[]
  curFieldNode?: any
}>({
  tabs: [],
  curOperate: '',
  layerInfos: [],
  layerIds: []
})
const staticState: {
  view?: __esri.MapView
  graphics?: __esri.Graphic
  graphicsLayer?: __esri.GraphicsLayer
} = {}

const TableConfig = reactive<ITable>({
  dataList: [],
  height: 170,
  columns: [
    {
      minWidth: 50,
      label: '逻辑',
      prop: 'logic',
      formatter(row, value) {
        return OGisConditionLogic[value]
      }
    },
    {
      minWidth: 110,
      label: '字段',
      prop: 'field',
      formatter(row, value) {
        return OGisConditionFields[value]
      }
    },
    {
      minWidth: 70,
      label: '运行符',
      prop: 'calc',
      formatter(row, value) {
        return OCalcOperator[value]
      }
    },
    {
      minWidth: 80,
      label: '值',
      prop: 'value'
    }
  ],
  pagination: {
    hide: true
  }
})
const FormConfig = reactive<IFormConfig>({
  group: [
    {
      fieldset: {
        desc: '绘制工具'
      },
      fields: [
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              title: '绘制多边形',
              iconifyIcon: 'mdi:shape-polygon-plus',
              click: () => initDraw('polygon')
            },
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              title: '绘制矩形',
              iconifyIcon: 'ep:crop',
              click: () => initDraw('rectangle')
            },
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              title: '绘制圆形',
              iconifyIcon: 'mdi:ellipse-outline',
              click: () => initDraw('circle')
            },
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              title: '清除图形',
              iconifyIcon: 'ep:delete',
              click: () => clearGraphicsLayer()
            }
          ]
        }
      ]
    },
    {
      fieldset: {
        desc: '选择图层'
      },
      fields: [
        {
          type: 'tree',
          options: [],
          checkStrictly: true,
          showCheckbox: true,
          field: 'layerid',
          nodeKey: 'value'
          // handleCheckChange: (data, isChecked) => {
          // if (isChecked) {
          //   refForm.value && (refForm.value.dataForm.layerid = [data.value])
          // }
          // }
        }
      ]
    },
    {
      id: 'field-construct',
      fieldset: {
        desc: '属性过滤'
      },
      fields: [
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              styles: { width: '100%' },
              text: '添加条件',
              iconifyIcon: 'ep:plus',
              click: () => {
                if (!refSql.value) return
                refSql.value.openDialog()
                refSql.value.TableConfig.dataList = cloneDeep(TableConfig.dataList || [])
              }
            },
            {
              perm: true,
              text: '清除条件',
              type: 'danger',
              iconifyIcon: 'ep:delete',
              disabled: () => state.curOperate === 'detailing',
              click: () => clear(),
              styles: {
                width: '100%'
              }
            }
          ]
        },
        {
          type: 'table',
          config: TableConfig
        }
      ]
    },
    {
      fields: [
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: () => (state.curOperate === 'detailing' ? '正在查询' : '查询'),
              disabled: () => state.curOperate === 'detailing',
              loading: () => state.curOperate === 'detailing',
              click: () => startSearch(),
              styles: {
                width: '100%'
              }
            },
            {
              perm: window.SITE_CONFIG.GIS_CONFIG.gisSaveScheme,
              text: '保存方案',
              styles: {
                width: '100%'
              },
              click: () => scheme.openSaveDialog()
            }
          ]
        }
      ]
    }
  ],
  labelPosition: 'top',
  gutter: 12
})
const { initSketch, destroySketch, sketch } = useSketch()
const initDraw = (type: any) => {
  if (!staticState.view) return
  sketch.value?.create(type)
  staticState.graphicsLayer?.removeAll()
}
const clearGraphicsLayer = () => {
  staticState.graphicsLayer?.removeAll()
  staticState.graphics = undefined
}

const getLayerInfo = async () => {
  state.layerIds = getSubLayerIds(staticState.view)
  const layerInfo = await queryLayerClassName(state.layerIds)
  state.layerInfos = layerInfo.data?.result?.rows || []
  const field = FormConfig.group[1].fields[0] as IFormTree
  const points = state.layerInfos
    .filter(item => item.geometrytype === 'esriGeometryPoint')
    .map(item => {
      return {
        label: item.layername,
        value: item.layerid,
        data: item
      }
    })
  const lines = state.layerInfos
    .filter(item => item.geometrytype === 'esriGeometryPolyline')
    .map(item => {
      return {
        label: item.layername,
        value: item.layerid,
        data: item
      }
    })
  field
    && (field.options = [
      { label: '管点类', value: -1, children: points, disabled: true },
      { label: '管线类', value: -2, children: lines, disabled: true }
    ])
  refForm.value && (refForm.value.dataForm.layerid = [])
}

const clear = () => {
  refForm.value?.dataForm && (refForm.value.dataForm.sql = '')
  TableConfig.dataList = []
  refSql.value?.clear()
}
const handleSqlGenerated = params => {
  console.log(params)
  TableConfig.dataList = cloneDeep(params.list || [])
  refForm.value?.dataForm && (refForm.value.dataForm.sql = params.sql)
}
const startSearch = async () => {
  try {
    state.tabs.length = 0
    state.curOperate = 'detailing'
    const layerIds = refForm.value?.dataForm.layerid
    if (!layerIds?.length) {
      SLMessage.warning('请选择图层')
    } else {
      state.tabs = await queryMultiLayerForIds(
        state.layerInfos.filter(item => layerIds.indexOf(item.layerid) !== -1),
        {
          where: refForm.value?.dataForm?.sql || '1=1',
          geometry: staticState.graphics?.geometry
        }
      )
      refMap.value?.refreshDetail(state.tabs)
    }
  } catch (error: any) {
    SLMessage.error(error.message)
  }
  state.curOperate = ''
}

const scheme = useScheme('condition')
const handleUseScheme = async (row: any) => {
  const detail = scheme.parseScheme(row)
  if (refForm.value?.dataForm) {
    refForm.value.dataForm.layerid = detail.layerid || []
    // refForm.value.dataForm.sql = detail.sql
  }

  if (detail.graphic) {
    staticState.graphics = Graphic.fromJSON(detail.graphic)
    sketch.value?.cancel()
    staticState.graphicsLayer?.removeAll()
    staticState.graphicsLayer?.add(staticState.graphics)
  }
  if (refSql.value) {
    refSql.value.TableConfig.dataList = detail.sqlList || []
    refSql.value.Submit()
  }
  startSearch()
}
const handleSchemeSubmit = params => {
  scheme.submitScheme({
    ...params,
    type: scheme.schemeType.value,
    detail: JSON.stringify({
      layerid: refForm.value?.dataForm.layerid || [],
      graphic: staticState.graphics,
      sqlList: refSql.value?.TableConfig.dataList
    })
  })
}
const resolveDrawEnd = (res: ISketchHandlerParameter) => {
  if (res.state === 'complete') {
    staticState.graphics = res.graphics[0]
    console.log(JSON.stringify(staticState.graphics))
  }
}
const onMaploaded = async (view: __esri.MapView) => {
  staticState.view = view
  staticState.graphicsLayer = getGraphicLayer(staticState.view, {
    id: 'search-manual',
    title: '高级查询'
  })
  initSketch(staticState.view, staticState.graphicsLayer, {
    updateCallBack: resolveDrawEnd,
    createCallBack: resolveDrawEnd
  })
  await getLayerInfo()
}
onBeforeUnmount(() => {
  staticState.graphicsLayer?.removeAll()
  staticState.graphicsLayer?.destroy()
  destroySketch()
})
</script>
<style lang="scss" scoped>
:deep(.el-table__empty-block) {
  min-height: 40px;
  .el-table__empty-text {
    line-height: 40px;
  }
}
</style>
<style>
.sql-btns-wrapper,
.sql-list-wrapper {
  box-shadow: 0 0 0 1px var(--el-border-color);
}
</style>
