/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.menu;

import com.google.common.util.concurrent.ListenableFuture;
import org.thingsboard.server.common.data.id.MenuPoolId;
import org.thingsboard.server.common.data.id.MenuTenantId;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.menu.MenuPool;
import org.thingsboard.server.common.data.menu.MenuTenant;
import org.thingsboard.server.dao.Dao;

import java.util.List;
import java.util.UUID;

public interface MenuTenantDao extends Dao<MenuTenant> {
    @Override
    List<MenuTenant> find();

    @Override
    MenuTenant findById(UUID id);

//    @Override
//    ListenableFuture<MenuTenant> findByIdAsync(UUID id);

    @Override
    MenuTenant save(MenuTenant menuTenant);

    @Override
    boolean removeById(UUID id);

    List<MenuPool> findMenuByTenantId(TenantId tenantId, MenuPoolId parentId);

    List<MenuPool> findMenuByTenantId(TenantId tenantId);

    void deleteByTenantId(TenantId tenantId);

    MenuPool findById(MenuTenantId id);

    List<String> getTreeByTenantId(TenantId tenantId);

    List<MenuTenant> findByTenantId(TenantId tenantId);

    /**
     * 按菜单类型查询id
     * @param type
     * @param tenantId
     * @return
     */
    String getIdByType(Integer type, TenantId tenantId);

    /**
     * 按菜单类型查询path信息
     * @param type
     * @return
     */
    String getAdditionalInfoByType(Integer type);

    List<MenuTenant> findByExtensionMenu(TenantId tenantId);

    MenuTenant findByTenantIdAndMenuPoolId(TenantId tenantId, MenuPoolId id);

    List<MenuTenant> findByTenantIdAndMenuPoolIdIn(TenantId tenantId, List<String> menuIdList);
}
