package org.thingsboard.server.dao.project;

import org.thingsboard.server.common.data.project.ProjectTreeVO;
import org.thingsboard.server.dao.model.sql.ProjectEntity;
import org.thingsboard.server.dao.model.sql.ProjectRelationEntity;

import java.util.List;

public interface ProjectRelationService {

    List<ProjectRelationEntity> findProjectRelationByEntityTypeAndProjectId(String entityType, String projectId);

    boolean mountEntityToProject(String entityType, String projectId, List<String> entityIdStringList);

    boolean mountEntityToProject(String entityType, List<String> projectId, List<String> entityIdStringList);

    List<ProjectEntity> findProjectRelationByEntityTypeAndEntityId(String entityType, String entityId);

    List<ProjectTreeVO> findProjectTreeVOByEntityTypeAndEntityId(String entityType, String entityId);

    void removeProjectRelation(String projectId);

    List<ProjectRelationEntity> findByProjectIdInAndEntityType(List<String> projectList, String entityType);

    List<ProjectRelationEntity> findByEntityTypeAndEntityIdIn(String entityType, List<String> entityIdList);
}
