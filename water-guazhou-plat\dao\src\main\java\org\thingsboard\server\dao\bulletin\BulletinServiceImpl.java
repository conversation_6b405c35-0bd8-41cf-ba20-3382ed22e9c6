package org.thingsboard.server.dao.bulletin;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.model.sql.BulletinDataEntity;
import org.thingsboard.server.dao.model.sql.OriginDataEntity;
import org.thingsboard.server.dao.sql.bulletin.JpaBulletinDao;
import org.thingsboard.server.dao.sql.orginData.JpaOriginDataDao;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/3 14:05
 */
@Service
public class BulletinServiceImpl implements BulletinService {

    @Autowired
    private JpaBulletinDao bulletinDao;


    @Override
    public BulletinDataEntity save(BulletinDataEntity bulletinDataEntity) {
       return bulletinDao.save(bulletinDataEntity);
    }

    @Override
    public BulletinDataEntity findLastByTenantId(TenantId tenantId) {
        return bulletinDao.findLastByTenantId(tenantId);
    }

    @Override
    public List<BulletinDataEntity> findByTenant(TenantId tenantId) {
        return bulletinDao.findByTenant(tenantId);
    }
}
