import { computed, watch, onMounted, onBeforeMount } from 'vue';
import { useRoute } from 'vue-router';
import { useAppStore } from '@/store';

const { body } = document;
const WIDTH = 877;
const RATIO = 3;

export const useResize = () => {
  const appStore = useAppStore();
  // const { sidebar, device } = mapGetters(['sidebar', 'device'])
  // console.log(sidebar, device)

  const route = useRoute();
  const sidebar = computed(() => appStore.sidebar);
  const device = computed(() => appStore.device);

  const _isMobile = () => {
    const rect = body.getBoundingClientRect();
    return rect.width - RATIO < WIDTH;
  };
  const resizeHandler = () => {
    if (!document.hidden) {
      const isMobile = _isMobile();
      appStore.ToggleDevice(isMobile ? 'mobile' : 'desktop');

      if (isMobile) {
        appStore.CloseSideBar({ withoutAnimation: true });
      }
    }
  };
  watch(
    () => route,
    () => {
      if (device.value === 'mobile' && sidebar.value.opened) {
        appStore.CloseSideBar({ withoutAnimation: false });
      }
    }
  );
  onMounted(() => {
    const isMobile = _isMobile();
    if (isMobile) {
      appStore.ToggleDevice('mobile');
      appStore.CloseSideBar({ withoutAnimation: true });
    }
  });
  onBeforeMount(() => window.addEventListener('resize', resizeHandler));

  return { sidebar, device };
};
