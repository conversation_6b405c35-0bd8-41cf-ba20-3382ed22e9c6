function i(t){return{title:{text:"",textStyle:{fontSize:"14px"},top:10},grid:{left:80,right:80,top:50,bottom:20},legend:{top:0,width:"500",type:"scroll",textStyle:{fontSize:12}},tooltip:{trigger:"axis"},xAxis:{type:"category",data:t},yAxis:[{position:"left",alignTicks:!0,type:"value",name:"流量",offset:0,axisLine:{show:!0,lineStyle:{types:"solid"}},axisLabel:{show:!0},splitLine:{lineStyle:{type:[5,10],dashOffset:5}}}],series:[{}]}}export{i as l};
