package org.thingsboard.server.dao.model.sql.smartOperation.construction;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseUsername;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.util.Date;

@Getter
@Setter
@ResponseEntity
public class SoConstructionContractAmend {
    // id
    private String id;

    // 所属合同编号
    private String contractCode;

    // 所属工程编号
    private String constructionCode;

    // 变更日期
    private Date amendDate;

    // 变更原因
    private String remark;

    // 附件信息
    private String attachments;

    // 添加人
    @ParseUsername
    private String creator;

    // 添加时间
    private Date createTime;

    // 客户id
    private String tenantId;

}
