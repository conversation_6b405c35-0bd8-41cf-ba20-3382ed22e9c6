package org.thingsboard.server.dao.sql.project;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.thingsboard.server.dao.model.sql.ProjectEntity;
import org.thingsboard.server.dao.model.sql.ProjectRelationEntity;

import java.util.List;

public interface ProjectRelationRepository extends CrudRepository<ProjectRelationEntity, String> {

    List<ProjectRelationEntity> findByEntityTypeAndProjectId(String entityType, String projectId);

    int deleteByEntityTypeAndProjectIdAndEntityIdIn(String entityType, String projectId, List<String> entityIds);

    @Query("SELECT new ProjectEntity(p.id, p.parentId, p.name, p.createTime, p.tenantId, p.additionalInfo, p.type) " +
            "FROM ProjectRelationEntity pr, ProjectEntity p " +
            "WHERE pr.projectId = p.id AND pr.entityType = ?1 AND pr.entityId = ?2")
    List<ProjectEntity> findProjectRelationByEntityTypeAndEntityId(String entityType, String entityId);

    int deleteByEntityTypeAndEntityIdIn(String entityType, List<String> entityIdStringList);

    void deleteByProjectId(String projectId);

    List<ProjectRelationEntity> findByProjectIdInAndEntityType(List<String> projectList, String entityType);

    ProjectRelationEntity findFirstByEntityId(String fromTimeUUID);

    List<ProjectRelationEntity> findByEntityTypeAndEntityIdIn(String entityType, List<String> entityIdList);
}
