<template>
  <el-dialog
    v-model="dialogVisible"
    title="编辑泵组方案"
    width="800px"
    :before-close="handleClose"
  >
    <div class="scheme-dialog">
      <el-form :model="form" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="方案名称:">
              <el-input v-model="form.schemeName" placeholder="请输入..." />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="方案编码:">
              <el-input v-model="form.schemeCode" readonly />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <div class="pump-config-section">
        <div class="section-title">泵组配置</div>
        <el-table :data="pumpConfigs" border style="width: 100%;">
          <el-table-column prop="pumpName" label="启用水泵" width="200">
            <template #default="{ row }">
              <el-select v-model="row.pumpName" placeholder="请选择泵机" @change="handlePumpChange(row, $event)">
                <el-option label="请选择" value="" />
                <el-option 
                  v-for="option in pumpOptions" 
                  :key="option.value" 
                  :label="option.label" 
                  :value="option.value" 
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="fixedPower" label="额定功率" width="150">
            <template #default="{ row }">
              <el-input v-model="row.fixedPower" placeholder="请输入..." />
            </template>
          </el-table-column>
          <el-table-column prop="fixedFlow" label="额定流量" width="150">
            <template #default="{ row }">
              <el-input v-model="row.fixedFlow" placeholder="请输入..." />
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100">
            <template #default="{ $index }">
              <el-button type="primary" link size="small" @click="deletePumpConfig($index)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <div class="add-pump-btn">
          <el-button type="primary" @click="addPumpConfig">添加水泵</el-button>
        </div>
      </div>

      <div class="scheme-description">
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="description-item">
              <label>方案描述:</label>
              <el-input
                v-model="form.schemeDescription"
                type="textarea"
                :rows="3"
                placeholder="通过实时水泵运行计算组团"
              />
            </div>
          </el-col>
          <el-col :span="12">
            <div class="description-item">
              <label>方案备注:</label>
              <el-input
                v-model="form.schemeRemark"
                type="textarea"
                :rows="3"
                placeholder="通过实时水泵运行计算组团"
              />
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm">保存</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { pumpManageList } from '@/api/secondSupplyManage/pumpRoomInfo'
import { ElMessage } from 'element-plus'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  pumpRoomId: {
    type: String,
    default: ''
  },
  schemeData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:visible', 'confirm'])

const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 表单数据
const form = reactive({
  id: '',
  schemeName: '',
  schemeCode: '',
  schemeDescription: '',
  schemeRemark: ''
})

// 泵机选项数据
const pumpOptions = ref<Array<{label: string, value: string, data: any}>>([])

// 泵组配置数据
const pumpConfigs = ref([
  {
    pumpName: '',
    fixedPower: '',
    fixedFlow: '',
    pumpDisplayName: ''
  }
])

// 获取泵机列表
const getPumpList = async () => {
  if (!props.pumpRoomId) {
    pumpOptions.value = []
    return
  }

  try {
    const res = await pumpManageList({
      page: 1,
      size: 1000,
      pumpRoomId: props.pumpRoomId
    })

    console.log('泵机接口返回数据:', res)

    let pumps: any[] = []

    // 根据实际返回的数据结构解析
    if (res && res.data && res.data.code === 200) {
      if (res.data.data && Array.isArray(res.data.data.data)) {
        pumps = res.data.data.data
      } else if (res.data.data && Array.isArray(res.data.data.records)) {
        pumps = res.data.data.records
      }
    }

    // 转换为下拉选项格式
    pumpOptions.value = pumps.map((item: any) => ({
      label: `${item.name} (${item.code})`, // 显示设备名称和编码
      value: item.id,
      data: item // 保存完整的泵机数据
    }))

    console.log('解析后的泵机选项:', pumpOptions.value)
  } catch (error) {
    console.error('获取泵机列表失败:', error)
    ElMessage.error('获取泵机列表失败')
    pumpOptions.value = []
  }
}

// 处理泵机选择变化
const handlePumpChange = (row: any, pumpId: string) => {
  if (pumpId) {
    // 根据选择的泵机ID找到对应的泵机数据
    const selectedPump = pumpOptions.value.find(option => option.value === pumpId)
    if (selectedPump && selectedPump.data) {
      // 自动填充功率和流量信息（如果泵机数据中有的话）
      if (selectedPump.data.performanceParameters) {
        try {
          const params = JSON.parse(selectedPump.data.performanceParameters)
          row.fixedPower = params.power || ''
          row.fixedFlow = params.flow || ''
        } catch (error) {
          console.log('解析性能参数失败:', error)
        }
      }
      // 设置泵机名称为实际的设备名称
      row.pumpDisplayName = selectedPump.label
    }
  } else {
    // 清空相关字段
    row.fixedPower = ''
    row.fixedFlow = ''
    row.pumpDisplayName = ''
  }
}

// 添加泵组配置
const addPumpConfig = () => {
  pumpConfigs.value.push({
    pumpName: '',
    fixedPower: '',
    fixedFlow: '',
    pumpDisplayName: ''
  })
}

// 删除泵组配置
const deletePumpConfig = (index: number) => {
  if (pumpConfigs.value.length > 1) {
    pumpConfigs.value.splice(index, 1)
  }
}

// 加载方案数据
const loadSchemeData = () => {
  if (props.schemeData && Object.keys(props.schemeData).length > 0) {
    // 填充基本信息
    form.id = props.schemeData.id || ''
    form.schemeName = props.schemeData.schemeName || ''
    form.schemeCode = props.schemeData.schemeCode || ''
    form.schemeDescription = props.schemeData.schemeDescription || ''
    form.schemeRemark = props.schemeData.schemeRemark || ''

    // 解析泵组配置
    try {
      const configStr = props.schemeData.pumpGroupConfig || '[]'
      const configs = JSON.parse(configStr)
      if (configs.length > 0) {
        pumpConfigs.value = configs.map((config: any) => ({
          pumpName: config.pumpName || '',
          fixedPower: config.fixedPower || '',
          fixedFlow: config.fixedFlow || '',
          pumpDisplayName: config.pumpDisplayName || ''
        }))
      }
    } catch (error) {
      console.error('解析泵组配置失败:', error)
    }
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  resetForm()
}

// 确认提交
const handleConfirm = () => {
  // 表单验证
  if (!form.schemeName.trim()) {
    ElMessage.warning('请输入方案名称')
    return
  }
  
  // 验证泵组配置
  const validConfigs = pumpConfigs.value.filter(config => 
    config.pumpName && config.fixedPower && config.fixedFlow
  )
  
  if (validConfigs.length === 0) {
    ElMessage.warning('请至少配置一个泵组')
    return
  }
  
  const schemeData = {
    ...form,
    pumpConfigs: validConfigs // 只提交有效的配置
  }
  
  console.log('提交编辑数据:', schemeData)
  emit('confirm', schemeData)
  resetForm()
}

// 重置表单
const resetForm = () => {
  form.id = ''
  form.schemeName = ''
  form.schemeCode = ''
  form.schemeDescription = ''
  form.schemeRemark = ''
  pumpConfigs.value = [
    {
      pumpName: '',
      fixedPower: '',
      fixedFlow: '',
      pumpDisplayName: ''
    }
  ]
}

// 监听对话框显示状态
watch(() => props.visible, (newVal) => {
  if (newVal) {
    loadSchemeData()
    // 获取泵机列表
    getPumpList()
  }
})

// 监听泵房ID变化
watch(() => props.pumpRoomId, (newVal) => {
  if (newVal && props.visible) {
    getPumpList()
  }
})

// 监听方案数据变化
watch(() => props.schemeData, () => {
  if (props.visible) {
    loadSchemeData()
  }
}, { deep: true })
</script>

<style scoped lang="scss">
.scheme-dialog {
  .pump-config-section {
    margin: 20px 0;
    
    .section-title {
      font-size: 14px;
      font-weight: bold;
      margin-bottom: 10px;
    }
    
    .add-pump-btn {
      margin-top: 10px;
      text-align: center;
    }
  }
  
  .scheme-description {
    margin-top: 20px;
    
    .description-item {
      label {
        display: block;
        margin-bottom: 5px;
        font-size: 14px;
      }
    }
  }
}

.dialog-footer {
  text-align: center;
}
</style>
