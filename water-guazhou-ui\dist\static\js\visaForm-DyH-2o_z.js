import{_ as O}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as T}from"./CardTable-rdWOL4_6.js";import{_ as L}from"./CardSearch-CB_HNR-Q.js";import{d as k,c as u,a8 as v,s as g,r as d,x as l,a9 as b,o as D,g as z,n as E,q as m,i as f,b7 as w}from"./index-r0dFAfgr.js";import{I as y}from"./common-CvK_P_ao.js";import{a2 as N,a3 as j,a4 as V,a1 as q,g as S,a5 as R}from"./manage-BReaEVJk.js";import{g as B}from"./projectManagement-CDcrrCQ1.js";import P from"./detail-kCbFTIbC.js";import{S as x}from"./data-Dv9-Tstw.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";import"./detail-DEo1RlcF.js";import"./index-CCFuhOrs.js";/* empty css                             */import"./DPlayer-Be2AurQX.js";import"./DPlayer.min-_HMH7IVX.js";import"./xmwcqk-Cxfq91Sa.js";import"./xmgc-Czrw1pVN.js";import"./cytbgs-WJxYGJyW.js";import"./gcwcqk-CV4EMT8B.js";import"./ssxmwcqk-BJgrXy2o.js";import"./gcsjjcxx-lLqauOhu.js";import"./sjbg-L9B2uWB9.js";import"./data-DDQ4eWNr.js";import"./gcysjcxx-BB9DfF9W.js";import"./qzjbxx-D98fv1p0.js";import"./htjbxx-CcjVPiVa.js";import"./htbg-CJ8T-1F4.js";import"./fygl-BCgGpKLc.js";import"./ssxq-C8LIbr3S.js";import"./ysqgcjcxx-5zZQS7XS.js";import"./ssgcjsjcxx-BD3tZw0Z.js";import"./ssgdjcxx-4P0LZdbp.js";import"./xmzysjcxx-DxVVq7LT.js";import"./xmzjsjcxx-C3UxQ9jk.js";import"./xmzgdjcxx-LKGnYC4Q.js";import"./DateFormatter-Bm9a68Ax.js";const U={class:"wrapper"},Lt=k({__name:"visaForm",setup(A){const p=u(),c=u(),h=u({filters:[{label:"工程编号",field:"constructionCode",type:"input"},{label:"工程名称",field:"constructionName",type:"input"},{label:"业主单位",field:"firstPartOrganization",type:"input"},{label:"监理单位",field:"supervisorOrganization",type:"input"},{label:"工程类别",field:"constructionTypeId",type:"select",options:v(()=>s.projectType)}],operations:[{type:"btn-group",btns:[{type:"default",perm:!0,text:"导出",icon:y.DOWNLOAD,click:()=>{N().then(t=>{const e=window.URL.createObjectURL(t.data),a=document.createElement("a");a.style.display="none",a.href=e,a.setAttribute("download","签证单.xlsx"),document.body.appendChild(a),a.click()})}},{type:"default",perm:!0,text:"重置",svgIcon:g(w),click:()=>{var t;(t=p.value)==null||t.resetForm(),n()}},{perm:!0,text:"查询",icon:y.QUERY,click:()=>n()}]}]}),i=d({defaultExpandAll:!1,indexVisible:!0,expandable:!0,expandComponent:g(P),extendedReturn:()=>{n()},rowKey:"constructionCode",columns:[{label:"工程编号",prop:"constructionCode"},{label:"工程名称",prop:"constructionName"},{label:"工程类别",prop:"constructionTypeName"},{label:"业主单位",prop:"firstpartOrganization"},{label:"施工单位",prop:"constructionOrganization"},{label:"设计单位",prop:"secondpartOrganization"},{label:"监理单位",prop:"supervisorOrganization"},{label:"合同总金额(万元)",prop:"contractTotalCost"},{label:"工作状态",prop:"status",tag:!0,tagColor:t=>{var e;return((e=x.find(a=>a.value===t.status))==null?void 0:e.color)||""},formatter:t=>{var e;return(e=x.find(a=>a.value===t.status))==null?void 0:e.label}}],operationWidth:"360px",operations:[{disabled:t=>t.status==="COMPLETED",isTextBtn:!1,type:"primary",text:"添加工程签证",perm:!0,click:t=>{_(t)}},{disabled:t=>t.status==="COMPLETED"||t.items.length===0,isTextBtn:!1,type:"success",text:"完成",perm:!0,click:t=>{j(t.constructionCode).then(e=>{e.data.code===200?l.success("已完成"):l.warning("完成失败"),n()})}},{isTextBtn:!1,text:"导出签证情况",perm:!0,click:t=>{V(t.constructionCode).then(e=>{const a=window.URL.createObjectURL(e.data),o=document.createElement("a");o.style.display="none",o.href=a,o.setAttribute("download",`${t.constructionName}工程签证.xlsx`),document.body.appendChild(o),o.click()})}}],dataList:[],pagination:{page:1,limit:20,total:0,refreshData:({page:t,size:e})=>{i.pagination.page=t,i.pagination.limit=e,n()}}}),r=d({title:"编辑签证",appendToBody:!0,labelWidth:"130px",dialogWidth:"1000px",submitting:!1,submit:t=>{r.submitting=!0;let e="新增";t.id&&(e="修改"),t.pipLengthDesign=JSON.stringify(t.pipLengthDesign),q(t).then(a=>{var o;r.submitting=!1,a.data.code===200?(l.success(e+"成功"),(o=c.value)==null||o.closeDialog()):l.warning(e+"失败")}).catch(a=>{r.submitting=!1,l.warning(a)})},defaultValue:{},group:[{fields:[{xs:12,type:"input",label:"签证编号",field:"code",rules:[{required:!0,message:"请输入签证编号"}]},{xs:12,type:"input",label:"工程编号",field:"constructionCode",disabled:!0},{xs:12,type:"input",label:"工程名称",field:"constructionName",disabled:!0},{xs:12,type:"input",label:"施工单位",field:"constructOrganization",rules:[{required:!0,message:"请输入施工单位"}]},{xs:12,type:"input",label:"施工地点",field:"address",rules:[{required:!0,message:"请输入施工地点"}]},{xs:12,type:"date",label:"施工时间",field:"constructTime",format:"x"},{xs:12,type:"input",label:"建设单位",field:"buildOrganization",rules:[{required:!0,message:"请输入建设单位"}]},{xs:12,type:"input",label:"监理单位",field:"supervisorOrganization",rules:[{required:!0,message:"请输入监理单位"}]},{xs:12,type:"input",label:"审计单位",field:"auditOrganization",rules:[{required:!0,message:"请输入审计单位"}]},{type:"textarea",label:"备注",field:"remark"},{type:"file",label:"附件",field:"attachments"}]}]}),_=t=>{var e;r.title="添加签证",s.DesignTubeLength=[],r.defaultValue={constructionCode:t.constructionCode,constructionName:t.constructionName,constructTime:new Date},(e=c.value)==null||e.openDialog()},s=d({projectList:[],projectType:[],DesignTubeLength:[],getOptions:()=>{B({page:1,size:-1}).then(t=>{s.projectList=b(t.data.data.data||[],"children",{label:"name",value:"code"})}),S({page:1,size:-1}).then(t=>{s.projectType=b(t.data.data.data||[],"children")})}}),n=async()=>{var e;const t={size:i.pagination.limit||20,page:i.pagination.page||1,...((e=p.value)==null?void 0:e.queryParams)||{}};R(t).then(a=>{i.dataList=a.data.data.data||[],i.pagination.total=a.data.data.total||0})};return D(()=>{n(),s.getOptions()}),(t,e)=>{const a=L,o=T,C=O;return z(),E("div",U,[m(a,{ref_key:"refSearch",ref:p,config:f(h)},null,8,["config"]),m(o,{config:f(i),class:"card-table"},null,8,["config"]),m(C,{ref_key:"refForm",ref:c,config:f(r)},null,8,["config"])])}}});export{Lt as default};
