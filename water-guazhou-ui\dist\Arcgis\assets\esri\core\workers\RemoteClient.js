var RemoteClient;(()=>{var e={88277:(e,t,r)=>{r.p=self.esriConfig.assetsPath+"/esri/core/workers/"},68773:(e,t,r)=>{"use strict";r.d(t,{Z:()=>o}),r(80442);var n=r(78286);const o={analysisTheme:{accentColor:[255,128,0],textColor:"white"},apiKey:void 0,applicationUrl:globalThis.location?.href,assetsPath:"",fontsUrl:"https://static.arcgis.com/fonts",geometryServiceUrl:"https://utility.arcgisonline.com/arcgis/rest/services/Geometry/GeometryServer",geoRSSServiceUrl:"https://utility.arcgis.com/sharing/rss",kmlServiceUrl:"https://utility.arcgis.com/sharing/kml",userPrivilegesApplied:!1,portalUrl:"https://www.arcgis.com",routeServiceUrl:"https://route-api.arcgis.com/arcgis/rest/services/World/Route/NAServer/Route_World",workers:{loaderConfig:{has:{},paths:{},map:{},packages:[]}},request:{crossOriginNoCorsDomains:null,httpsDomains:["arcgis.com","arcgisonline.com","esrikr.com","premiumservices.blackbridge.com","esripremium.accuweather.com","gbm.digitalglobe.com","firstlook.digitalglobe.com","msi.digitalglobe.com"],interceptors:[],maxUrlLength:2e3,priority:"high",proxyRules:[],proxyUrl:null,timeout:6e4,trustedServers:[],useIdentity:!0},log:{interceptors:[],level:null}};if(globalThis.esriConfig&&((0,n.RH)(o,globalThis.esriConfig,!0),delete o.has),!o.assetsPath){{const e="4.26.5";o.assetsPath=`https://js.arcgis.com/${e.slice(0,-2)}/@arcgis/core/assets`}o.defaultAssetsPath=o.assetsPath}},20102:(e,t,r)=>{"use strict";r.d(t,{Z:()=>i});var n=r(22974),o=r(92604),s=r(58896);class i extends s.Z{constructor(e,t,r){if(super(e,t,r),!(this instanceof i))return new i(e,t,r)}toJSON(){if(null!=this.details)try{return{name:this.name,message:this.message,details:JSON.parse(JSON.stringify(this.details,((e,t)=>{if(t&&"object"==typeof t&&"function"==typeof t.toJSON)return t;try{return(0,n.d9)(t)}catch(e){return"[object]"}})))}}catch(e){throw o.Z.getLogger("esri.core.Error").error(e),e}return{name:this.name,message:this.message,details:this.details}}static fromJSON(e){return new i(e.name,e.message,e.details)}}i.prototype.type="error"},92604:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});var n=r(68773),o=(r(80442),r(70586)),s=r(19153);const i={info:0,warn:1,error:2,none:3};class a{constructor(e){this.level=null,this._module="",this._parent=null,this.writer=null,this._loggedMessages={error:new Map,warn:new Map,info:new Map},null!=e.level&&(this.level=e.level),null!=e.writer&&(this.writer=e.writer),this._module=e.module,a._loggers[this.module]=this;const t=this.module.lastIndexOf(".");-1!==t&&(this._parent=a.getLogger(this.module.slice(0,t)))}get module(){return this._module}get parent(){return this._parent}error(...e){this._log("error","always",...e)}warn(...e){this._log("warn","always",...e)}info(...e){this._log("info","always",...e)}errorOnce(...e){this._log("error","once",...e)}warnOnce(...e){this._log("warn","once",...e)}infoOnce(...e){this._log("info","once",...e)}errorOncePerTick(...e){this._log("error","oncePerTick",...e)}warnOncePerTick(...e){this._log("warn","oncePerTick",...e)}infoOncePerTick(...e){this._log("info","oncePerTick",...e)}get test(){const e=this;return{loggedMessages:e._loggedMessages,clearLoggedWarnings:()=>e._loggedMessages.warn.clear()}}static get testSingleton(){return{resetLoggers(e={}){const t=a._loggers;return a._loggers=e,t},set throttlingDisabled(e){a._throttlingDisabled=e}}}static getLogger(e){let t=a._loggers[e];return t||(t=new a({module:e})),t}_log(e,t,...r){if(this._matchLevel(e)){if("always"!==t&&!a._throttlingDisabled){const n=this._argsToKey(r),o=this._loggedMessages[e].get(n);if("once"===t&&null!=o||"oncePerTick"===t&&o&&o>=a._tickCounter)return;this._loggedMessages[e].set(n,a._tickCounter),a._scheduleTickCounterIncrement()}for(const t of n.Z.log.interceptors)if(t(e,this.module,...r))return;this._inheritedWriter()(e,this.module,...r)}}_parentWithMember(e,t){let r=this;for(;(0,o.pC)(r);){const t=r[e];if((0,o.pC)(t))return t;r=r.parent}return t}_inheritedWriter(){return this._parentWithMember("writer",this._consoleWriter)}_consoleWriter(e,t,...r){console[e](`[${t}]`,...r)}_matchLevel(e){const t=n.Z.log.level?n.Z.log.level:"warn";return i[this._parentWithMember("level",t)]<=i[e]}_argsToKey(...e){return(0,s.hP)(JSON.stringify(e,((e,t)=>"object"!=typeof t||Array.isArray(t)?t:"[Object]")))}static _scheduleTickCounterIncrement(){a._tickCounterScheduled||(a._tickCounterScheduled=!0,Promise.resolve().then((()=>{a._tickCounter++,a._tickCounterScheduled=!1})))}}a._loggers={},a._tickCounter=0,a._tickCounterScheduled=!1,a._throttlingDisabled=!1},58896:(e,t,r)=>{"use strict";r.d(t,{Z:()=>o});var n=r(78286);class o{constructor(e,t,r){this.name=e,this.details=r,this instanceof o&&(this.message=(t&&function(e,t){return e.replace(/\$\{([^\s\:\}]*)(?:\:([^\s\:\}]+))?\}/g,((e,r)=>{if(""===r)return"$";const o=(0,n.hS)(r,t)??"";if(void 0===o)throw new Error(`could not find key "${r}" in template`);return o.toString()}))}(t,r))??"")}toString(){return"["+this.name+"]: "+this.message}}},77734:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});class n{constructor(e=1){this._seed=e}set seed(e){this._seed=e??Math.random()*n._m}getInt(){return this._seed=(n._a*this._seed+n._c)%n._m,this._seed}getFloat(){return this.getInt()/(n._m-1)}getIntRange(e,t){return Math.round(this.getFloatRange(e,t))}getFloatRange(e,t){const r=t-e;return e+this.getInt()/n._m*r}}n._m=2147483647,n._a=48271,n._c=0},67676:(e,t,r)=>{"use strict";r.d(t,{FY:()=>m,Od:()=>y,SO:()=>d,Vx:()=>i,a9:()=>u,cq:()=>p,e$:()=>b,e5:()=>a,fS:()=>s,w6:()=>f,zG:()=>c});var n=r(70586),o=r(77734);function s(e,t,r){if((0,n.Wi)(e)&&(0,n.Wi)(t))return!0;if((0,n.Wi)(e)||(0,n.Wi)(t)||e.length!==t.length)return!1;if(r){for(let n=0;n<e.length;n++)if(!r(e[n],t[n]))return!1}else for(let r=0;r<e.length;r++)if(e[r]!==t[r])return!1;return!0}function i(e,t){let r=e.length!==t.length;r&&(e.length=t.length);for(let n=0;n<t.length;++n)e[n]!==t[n]&&(e[n]=t[n],r=!0);return r}function a(e,t,r){let n,o;return r?(n=t.filter((t=>!e.some((e=>r(e,t))))),o=e.filter((e=>!t.some((t=>r(t,e)))))):(n=t.filter((t=>!e.includes(t))),o=e.filter((e=>!t.includes(e)))),{added:n,removed:o}}function c(e){return e&&"number"==typeof e.length}const l=!!Array.prototype.fill;function u(e,t){if(l)return new Array(e).fill(t);const r=new Array(e);for(let n=0;n<e;n++)r[n]=t;return r}function f(e,t){void 0===t&&(t=e,e=0);const r=new Array(t-e);for(let n=e;n<t;n++)r[n-e]=n;return r}class d{constructor(){this.last=0}}const h=new d;function p(e,t,r,n){n=n||h;const o=Math.max(0,n.last-10);for(let s=o;s<r;++s)if(e[s]===t)return n.last=s,s;const s=Math.min(o,r);for(let r=0;r<s;++r)if(e[r]===t)return n.last=r,r;return-1}function b(e,t,r,n){const o=r??e.length,s=p(e,t,o,n);if(-1!==s)return e[s]=e[o-1],null==r&&e.pop(),t}const g=new Set;function m(e,t,r=e.length,n=t.length,o,s){if(0===n||0===r)return r;g.clear();for(let e=0;e<n;++e)g.add(t[e]);o=o||h;const i=Math.max(0,o.last-10);for(let t=i;t<r;++t)if(g.has(e[t])&&(s&&s.push(e[t]),g.delete(e[t]),e[t]=e[r-1],--r,--t,0===g.size||0===r))return g.clear(),r;for(let t=0;t<i;++t)if(g.has(e[t])&&(s&&s.push(e[t]),g.delete(e[t]),e[t]=e[r-1],--r,--t,0===g.size||0===r))return g.clear(),r;return g.clear(),r}function y(e,t){const r=e.indexOf(t);return-1!==r?(e.splice(r,1),t):null}new o.Z},60235:(e,t,r)=>{"use strict";r.d(t,{Mr:()=>i,x9:()=>a});var n=r(80442);const o=new Set;function s(e,t,r=!1){r&&o.has(t)||(r&&o.add(t),e.warn(`🛑 DEPRECATED - ${t}`))}function i(e,t,r={}){if((0,n.Z)("esri-deprecation-warnings")){const{moduleName:n}=r;a(e,"Property: "+(n?n+"::":"")+t,r)}}function a(e,t,r={}){if((0,n.Z)("esri-deprecation-warnings")){const{replacement:n,version:o,see:i,warnOnce:a}=r;let c=t;n&&(c+=`\n\t🛠️ Replacement: ${n}`),o&&(c+=`\n\t⚙️ Version: ${o}`),i&&(c+=`\n\t🔗 See ${i} for more details.`),s(e,c,a)}}},91460:(e,t,r)=>{"use strict";function n(e){return e&&("function"==typeof e.on||"function"==typeof e.addEventListener)}function o(e,t,r){if(!n(e))throw new TypeError("target is not a Evented or EventTarget object");if("on"in e)return e.on(t,r);if(Array.isArray(t)){const n=t.slice();for(const t of n)e.addEventListener(t,r);return{remove(){for(const t of n)e.removeEventListener(t,r)}}}return e.addEventListener(t,r),{remove(){e.removeEventListener(t,r)}}}function s(e,t,r){if(!n(e))throw new TypeError("target is not a Evented or EventTarget object");if("once"in e)return e.once(t,r);const s=o(e,t,(t=>{s.remove(),r.call(e,t)}));return{remove(){s.remove()}}}r.d(t,{IH:()=>s,on:()=>o,vT:()=>n})},80442:(e,t,r)=>{"use strict";let n;function o(e){return"function"==typeof n[e]?n[e]=n[e](globalThis):n[e]}r.d(t,{Z:()=>o}),n=globalThis.dojoConfig?.has||globalThis.esriConfig?.has?{...globalThis.dojoConfig?.has,...globalThis.esriConfig?.has}:{},o.add=(e,t,r,s)=>((s||void 0===n[e])&&(n[e]=t),r&&o(e)),o.cache=n,o.add("esri-deprecation-warnings",!0),(()=>{o.add("host-webworker",void 0!==globalThis.WorkerGlobalScope&&self instanceof globalThis.WorkerGlobalScope);const e="undefined"!=typeof window&&"undefined"!=typeof location&&"undefined"!=typeof document&&window.location===location&&window.document===document;if(o.add("host-browser",e),o.add("host-node","object"==typeof globalThis.process&&globalThis.process.versions?.node&&globalThis.process.versions.v8),o.add("dom",e),o("host-browser")){const e=navigator,t=e.userAgent,r=e.appVersion,n=parseFloat(r);if(o.add("wp",parseFloat(t.split("Windows Phone")[1])||void 0),o.add("msapp",parseFloat(t.split("MSAppHost/")[1])||void 0),o.add("khtml",r.includes("Konqueror")?n:void 0),o.add("edge",parseFloat(t.split("Edge/")[1])||void 0),o.add("opr",parseFloat(t.split("OPR/")[1])||void 0),o.add("webkit",!o("wp")&&!o("edge")&&parseFloat(t.split("WebKit/")[1])||void 0),o.add("chrome",!o("edge")&&!o("opr")&&parseFloat(t.split("Chrome/")[1])||void 0),o.add("android",!o("wp")&&parseFloat(t.split("Android ")[1])||void 0),o.add("safari",!r.includes("Safari")||o("wp")||o("chrome")||o("android")||o("edge")||o("opr")?void 0:parseFloat(r.split("Version/")[1])),o.add("mac",r.includes("Macintosh")),!o("wp")&&t.match(/(iPhone|iPod|iPad)/)){const e=RegExp.$1.replace(/P/,"p"),r=t.match(/OS ([\d_]+)/)?RegExp.$1:"1",n=parseFloat(r.replace(/_/,".").replace(/_/g,""));o.add(e,n),o.add("ios",n)}o("webkit")||(!t.includes("Gecko")||o("wp")||o("khtml")||o("edge")||o.add("mozilla",n),o("mozilla")&&o.add("ff",parseFloat(t.split("Firefox/")[1]||t.split("Minefield/")[1])||void 0))}})(),(()=>{if(globalThis.navigator){const e=navigator.userAgent,t=/Android|webOS|iPhone|iPad|iPod|BlackBerry|Opera Mini|IEMobile/i.test(e),r=/iPhone/i.test(e);t&&o.add("esri-mobile",t),r&&o.add("esri-iPhone",r),o.add("esri-geolocation",!!navigator.geolocation)}o.add("esri-wasm","WebAssembly"in globalThis),o.add("esri-shared-array-buffer",(()=>{const e="SharedArrayBuffer"in globalThis,t=!1===globalThis.crossOriginIsolated;return e&&!t})),o.add("wasm-simd",(()=>WebAssembly.validate(new Uint8Array([0,97,115,109,1,0,0,0,1,5,1,96,0,1,123,3,2,1,0,10,10,1,8,0,65,0,253,15,253,98,11])))),o.add("esri-atomics","Atomics"in globalThis),o.add("esri-workers","Worker"in globalThis),o.add("web-feat:cache","caches"in globalThis),o.add("esri-workers-arraybuffer-transfer",!o("safari")||Number(o("safari"))>=12),o.add("featurelayer-simplify-thresholds",[.5,.5,.5,.5]),o.add("featurelayer-simplify-payload-size-factors",[1,1,4]),o.add("featurelayer-snapshot-enabled",!0),o.add("featurelayer-snapshot-point-min-threshold",8e4),o.add("featurelayer-snapshot-point-max-threshold",4e5),o.add("featurelayer-snapshot-point-coverage",.1),o.add("featurelayer-advanced-symbols",!1),o.add("featurelayer-pbf",!0),o.add("featurelayer-pbf-statistics",!1),o.add("feature-layers-workers",!0),o.add("feature-polyline-generalization-factor",1),o.add("mapview-transitions-duration",200),o.add("mapview-srswitch-adjust-rotation-scale-threshold",24e6),o.add("mapserver-pbf-version-support",10.81),o.add("mapservice-popup-identify-max-tolerance",20),o.add("heatmap-allow-raster-fallback",!0),o.add("heatmap-force-raster",!1),o("host-webworker")||o("host-browser")&&(o.add("esri-csp-restrictions",(()=>{try{new Function}catch{return!0}return!1})),o.add("esri-image-decode",(()=>{if("decode"in new Image){const e=new Image;return e.src='data:image/svg+xml;charset=UTF-8,<svg version="1.1" xmlns="http://www.w3.org/2000/svg"></svg>',void e.decode().then((()=>{o.add("esri-image-decode",!0,!0,!0)})).catch((()=>{o.add("esri-image-decode",!1,!0,!0)}))}return!1})),o.add("esri-url-encodes-apostrophe",(()=>{const e=window.document.createElement("a");return e.href="?'",e.href.includes("?%27")})))})()},22974:(e,t,r)=>{"use strict";r.d(t,{Vo:()=>c,d9:()=>a,fS:()=>p,y7:()=>b,yd:()=>i});var n=r(67676),o=r(70586),s=r(1533);function i(e,t){let r;if(t)for(r in e)e.hasOwnProperty(r)&&(void 0===e[r]?delete e[r]:e[r]instanceof Object&&i(e[r],!0));else for(r in e)e.hasOwnProperty(r)&&void 0===e[r]&&delete e[r];return e}function a(e){if(!e||"object"!=typeof e||"function"==typeof e)return e;const t=h(e);if((0,o.pC)(t))return t;if(l(e))return e.clone();if(u(e))return e.map(a);if(f(e))return e.clone();const r={};for(const t of Object.getOwnPropertyNames(e))r[t]=a(e[t]);return r}function c(e){if(!e||"object"!=typeof e||"function"==typeof e||"HTMLElement"in globalThis&&e instanceof HTMLElement)return e;const t=h(e);if((0,o.pC)(t))return t;if(u(e)){let t=!0;const r=e.map((e=>{const r=c(e);return null!=e&&null==r&&(t=!1),r}));return t?r:null}if(l(e))return e.clone();if(!f(e)){const t=new(0,Object.getPrototypeOf(e).constructor);for(const r of Object.getOwnPropertyNames(e)){const n=e[r],o=c(n);if(null!=n&&null==o)return null;t[r]=o}return t}return null}function l(e){return"function"==typeof e.clone}function u(e){return"function"==typeof e.map&&"function"==typeof e.forEach}function f(e){return"function"==typeof e.notifyChange&&"function"==typeof e.watch}function d(e){if("[object Object]"!==Object.prototype.toString.call(e))return!1;const t=Object.getPrototypeOf(e);return null===t||t===Object.prototype}function h(e){if((0,s.W0)(e)||(0,s.lq)(e)||(0,s.KZ)(e)||(0,s.z3)(e)||(0,s.Uc)(e)||(0,s.Hx)(e)||(0,s.ZY)(e)||(0,s.xZ)(e)||(0,s.fS)(e))return e.slice();if(e instanceof Date)return new Date(e.getTime());if(e instanceof ArrayBuffer)return e.slice(0,e.byteLength);if(e instanceof Map){const t=new Map;for(const[r,n]of e)t.set(r,a(n));return t}if(e instanceof Set){const t=new Set;for(const r of e)t.add(a(r));return t}return null}function p(e,t){return e===t||"number"==typeof e&&isNaN(e)&&"number"==typeof t&&isNaN(t)||"function"==typeof(e||{}).getTime&&"function"==typeof(t||{}).getTime&&e.getTime()===t.getTime()||!1}function b(e,t){return e===t||(null==e||"string"==typeof e?e===t:"number"==typeof e?e===t||"number"==typeof t&&isNaN(e)&&isNaN(t):e instanceof Date?t instanceof Date&&e.getTime()===t.getTime():Array.isArray(e)?Array.isArray(t)&&(0,n.fS)(e,t):e instanceof Set?t instanceof Set&&function(e,t){if(e.size!==t.size)return!1;for(const r of e)if(!t.has(r))return!1;return!0}(e,t):e instanceof Map?t instanceof Map&&function(e,t){if(e.size!==t.size)return!1;for(const[r,n]of e){const e=t.get(r);if(e!==n||void 0===e&&!t.has(r))return!1}return!0}(e,t):!!d(e)&&d(t)&&function(e,t){if(null===e||null===t)return!1;const r=Object.keys(e);if(null===t||Object.keys(t).length!==r.length)return!1;for(const n of r)if(e[n]!==t[n]||!Object.prototype.hasOwnProperty.call(t,n))return!1;return!0}(e,t))}},70586:(e,t,r)=>{"use strict";function n(e){return null!=e}function o(e){return null==e}function s(e,t){return n(e)?t(e):null}function i(e){return e}function a(e,t){return c(e,t),e}function c(e,t){if(o(e))throw new Error(t??"value is None")}function l(e,t){return n(e)?e:"function"==typeof t?t():t}function u(e,t){return n(e)?e:t}function f(e){return n(e)&&e.destroy(),null}function d(e){return n(e)&&e.dispose(),null}function h(e){return n(e)&&e.remove(),null}function p(e){return n(e)&&e.abort(),null}function b(e){return n(e)&&e.release(),null}function g(e,t,r){return n(e)&&n(t)?n(r)?r(e,t):e.equals(t):e===t}function m(e){return null}function y(e,t){const r=new Array;for(const n of e)r.push(v(n,null,t));return r}function w(e,t){for(const r of e)s(r,t)}function v(e,t,r){return n(e)?r(e):t}function _(e){return e.filter((e=>n(e)))}function O(e,...t){let r=e;for(let e=0;e<t.length&&r;++e)r=r[t[e]];return r}function k(e){return e}r.d(t,{Fd:()=>y,IM:()=>p,JR:()=>w,M2:()=>d,O3:()=>c,Pt:()=>l,R2:()=>v,RY:()=>b,SC:()=>f,U2:()=>O,Wg:()=>i,Wi:()=>o,_W:()=>g,hw:()=>h,j0:()=>k,lV:()=>_,pC:()=>n,s3:()=>a,wN:()=>m,yl:()=>u,yw:()=>s})},78286:(e,t,r)=>{"use strict";r.d(t,{RB:()=>i,RH:()=>o,hS:()=>s});var n=r(22974);function o(e,t,r=!1){return c(e,t,r)}function s(e,t){if(null!=t)return t[e]||a(e.split("."),!1,t)}function i(e,t,r){const n=e.split("."),o=n.pop(),s=a(n,!0,r);s&&o&&(s[o]=t)}function a(e,t,r){let n=r;for(const r of e){if(null==n)return;if(!(r in n)){if(!t)return;n[r]={}}n=n[r]}return n}function c(e,t,r){return t?Object.keys(t).reduce(((e,o)=>{let s=e[o],i=t[o];return s===i?e:void 0===s?(e[o]=(0,n.d9)(i),e):(Array.isArray(i)||Array.isArray(e)?(s=s?Array.isArray(s)?e[o]=s.concat():e[o]=[s]:e[o]=[],i&&(Array.isArray(i)||(i=[i]),r?i.forEach((e=>{s.includes(e)||s.push(e)})):e[o]=i.concat())):i&&"object"==typeof i?e[o]=c(s,i,r):e.hasOwnProperty(o)&&!t.hasOwnProperty(o)||(e[o]=i),e)}),e||{}):e}},95330:(e,t,r)=>{"use strict";r.d(t,{e4:()=>O,zE:()=>c,dD:()=>w,hh:()=>C,Ds:()=>P,as:()=>v,WW:()=>_,R8:()=>y,D_:()=>m,Hc:()=>f,y8:()=>k,fu:()=>p,$F:()=>b,r9:()=>d,k_:()=>l,H9:()=>h,Yn:()=>x,gx:()=>S,Hl:()=>g});const n=(o=globalThis,{setTimeout:(e,t)=>{const r=o.setTimeout(e,t);return{remove:()=>o.clearTimeout(r)}}});var o;r(60235);var s=r(20102),i=r(91460),a=(r(92604),r(70586));function c(e="Aborted"){return new s.Z("AbortError",e)}function l(e,t="Aborted"){if(f(e))throw c(t)}function u(e){return(0,a.pC)(e)?"aborted"in e?e:e.signal:e}function f(e){const t=u(e);return(0,a.pC)(t)&&t.aborted}function d(e){if(m(e))throw e}function h(e){if(!m(e))throw e}function p(e,t){const r=u(e);if(!(0,a.Wi)(r)){if(!r.aborted)return(0,i.IH)(r,"abort",(()=>t()));t()}}function b(e,t){const r=u(e);if(!(0,a.Wi)(r))return l(r),(0,i.IH)(r,"abort",(()=>t(c())))}function g(e,t){const r=u(t);return(0,a.Wi)(r)?e:new Promise(((r,n)=>{let o=p(t,(()=>n(c())));const s=()=>o=(0,a.hw)(o);e.then(s,s),e.then(r,n)}))}function m(e){return"AbortError"===e?.name}async function y(e){try{return await e}catch(e){if(!m(e))throw e;return}}function w(){let e=null;const t=new Promise(((t,r)=>{e={promise:void 0,resolve:t,reject:r}}));return e.promise=t,e}async function v(e){if(!e)return;if("function"!=typeof e.forEach){const t=Object.keys(e),r=t.map((t=>e[t])),n=await v(r),o={};return t.map(((e,t)=>o[e]=n[t])),o}const t=e;return new Promise((e=>{const r=[];let n=t.length;0===n&&e(r),t.forEach((t=>{const o={promise:t||Promise.resolve(t)};r.push(o),o.promise.then((e=>{o.value=e})).catch((e=>{o.error=e})).then((()=>{--n,0===n&&e(r)}))}))}))}async function _(e){return(await v(e)).filter((e=>!!e.value)).map((e=>e.value))}function O(e,t,r){const n=new AbortController;return p(r,(()=>n.abort())),new Promise(((r,o)=>{let s=setTimeout((()=>{s=0,r(t)}),e);p(n,(()=>{s&&(clearTimeout(s),o(c()))}))}))}function k(e){return e&&"function"==typeof e.then}function S(e){return k(e)?e:Promise.resolve(e)}function P(e,t=-1){let r,n,o,s,i=null;const l=(...u)=>{if(r){n=u,s&&s.reject(c()),s=w();const e=(0,a.j0)(s.promise);if(i){const e=i;i=null,e.abort()}return e}if(o=s||w(),s=null,t>0){const n=new AbortController;r=S(e(...u,n.signal));const o=r;O(t).then((()=>{r===o&&(s?n.abort():i=n)}))}else r=1,r=S(e(...u));const f=()=>{const e=n;n=o=r=i=null,null!=e&&l(...e)},d=r,h=o;return d.then(f,f),d.then(h.resolve,h.reject),(0,a.j0)(h.promise)};return l}function C(){let e,t;const r=new Promise(((r,n)=>{e=r,t=n})),o=t=>{e(t)};return o.resolve=t=>e(t),o.reject=e=>t(e),o.timeout=(e,t)=>n.setTimeout((()=>o.reject(t)),e),o.promise=r,o}async function x(e){await Promise.resolve(),l(e)}},19153:(e,t,r)=>{"use strict";r.d(t,{Cb:()=>l,Qs:()=>a,gx:()=>i,hP:()=>c});var n=r(78286);const o=/\{([^\}]+)\}/g;function s(e){return e??""}function i(e,t){return e.replace(o,"object"==typeof t?(e,r)=>s((0,n.hS)(r,t)):(e,r)=>s(t(r)))}function a(e,t){return e.replace(/([\.$?*|{}\(\)\[\]\\\/\+\-^])/g,(e=>t&&t.includes(e)?e:`\\${e}`))}function c(e){let t=0;for(let r=0;r<e.length;r++)t=(t<<5)-t+e.charCodeAt(r),t|=0;return t}function l(e){return(new DOMParser).parseFromString(e||"","text/html").body.innerText||""}},1533:(e,t,r)=>{"use strict";function n(e){return e instanceof ArrayBuffer}function o(e){return e&&e.constructor&&"Int8Array"===e.constructor.name}function s(e){return e&&e.constructor&&"Uint8Array"===e.constructor.name}function i(e){return e&&e.constructor&&"Uint8ClampedArray"===e.constructor.name}function a(e){return e&&e.constructor&&"Int16Array"===e.constructor.name}function c(e){return e&&e.constructor&&"Uint16Array"===e.constructor.name}function l(e){return e&&e.constructor&&"Int32Array"===e.constructor.name}function u(e){return e&&e.constructor&&"Uint32Array"===e.constructor.name}function f(e){return e&&e.constructor&&"Float32Array"===e.constructor.name}function d(e){return e&&e.constructor&&"Float64Array"===e.constructor.name}r.d(t,{DB:()=>h,Hx:()=>l,KZ:()=>i,Uc:()=>c,W0:()=>o,ZY:()=>u,eP:()=>n,fS:()=>d,lq:()=>s,xZ:()=>f,z3:()=>a});const h=1024},17452:(e,t,r)=>{"use strict";r.d(t,{$U:()=>ie,AH:()=>G,B7:()=>j,D6:()=>J,Fv:()=>F,HK:()=>Q,Hu:()=>be,Ie:()=>he,L:()=>S,Ml:()=>ye,P$:()=>z,PF:()=>Z,Qj:()=>de,R9:()=>w,TI:()=>k,YP:()=>K,Yd:()=>ue,ZN:()=>ge,Zo:()=>R,_l:()=>te,b7:()=>E,ed:()=>I,fZ:()=>we,fl:()=>me,fw:()=>re,hF:()=>H,hO:()=>ae,io:()=>ne,jc:()=>V,kl:()=>q,mN:()=>P,oC:()=>oe,oh:()=>L,qg:()=>T,rS:()=>Y,sJ:()=>ee,tD:()=>$,tm:()=>W,u0:()=>C,v_:()=>D,vt:()=>fe});var n=r(68773),o=r(20102),s=r(92604),i=r(70586),a=r(19745);const c=s.Z.getLogger("esri.core.urlUtils"),l=n.Z.request,u="esri/config: esriConfig.request.proxyUrl is not set.",f=/^\s*[a-z][a-z0-9-+.]*:(?![0-9])/i,d=/^\s*http:/i,h=/^\s*https:/i,p=/^\s*file:/i,b=/:\d+$/,g=/^https?:\/\/[^/]+\.arcgis.com\/sharing(\/|$)/i,m=new RegExp("^(([^:/?#]+):)?(//([^/?#]*))?([^?#]*)(\\?([^#]*))?(#(.*))?$"),y=new RegExp("^((([^\\[:]+):)?([^@]+)@)?(\\[([^\\]]+)\\]|([^\\[:]*))(:([0-9]+))?$");class w{constructor(e=""){this.uri=e,this.scheme=null,this.authority=null,this.path=null,this.query=null,this.fragment=null,this.user=null,this.password=null,this.host=null,this.port=null;let t=(0,i.j0)(this.uri.match(m));this.scheme=t[2]||(t[1]?"":null),this.authority=t[4]||(t[3]?"":null),this.path=t[5],this.query=t[7]||(t[6]?"":null),this.fragment=t[9]||(t[8]?"":null),null!=this.authority&&(t=(0,i.j0)(this.authority.match(y)),this.user=t[3]||null,this.password=t[4]||null,this.host=t[6]||t[7],this.port=t[9]||null)}toString(){return this.uri}}const v={};let _=new w(n.Z.applicationUrl);let O=function(){const e=(0,i.j0)(_.path),t=e.substring(0,e.lastIndexOf(e.split("/")[e.split("/").length-1]));return`${_.scheme}://${_.host}${null!=_.port?`:${_.port}`:""}${t}`}();const k=()=>_,S=()=>O;function P(e){if(!e)return null;const t={path:null,query:null},r=new w(e),n=e.indexOf("?");return null===r.query?t.path=e:(t.path=e.substring(0,n),t.query=C(r.query)),r.fragment&&(t.hash=r.fragment,null===r.query&&(t.path=t.path.substring(0,t.path.length-(r.fragment.length+1)))),t}function C(e){const t=e.split("&"),r={};for(const e of t){if(!e)continue;const t=e.indexOf("=");let n,o;t<0?(n=decodeURIComponent(e),o=""):(n=decodeURIComponent(e.slice(0,t)),o=decodeURIComponent(e.slice(t+1)));let s=r[n];"string"==typeof s&&(s=r[n]=[s]),Array.isArray(s)?s.push(o):r[n]=o}return r}function x(e){return e&&"object"==typeof e&&"toJSON"in e&&"function"==typeof e.toJSON}function j(e,t){return e?t&&"function"==typeof t?Object.keys(e).map((r=>encodeURIComponent(r)+"="+encodeURIComponent(t(r,e[r])))).join("&"):Object.keys(e).map((r=>{const n=e[r];if(null==n)return"";const o=encodeURIComponent(r)+"=",s=t&&t[r];return s?o+encodeURIComponent(s(n)):Array.isArray(n)?n.map((e=>x(e)?o+encodeURIComponent(JSON.stringify(e)):o+encodeURIComponent(e))).join("&"):x(n)?o+encodeURIComponent(JSON.stringify(n)):o+encodeURIComponent(n)})).filter((e=>e)).join("&"):""}function E(e=!1){let t,r=l.proxyUrl;if("string"==typeof e){t=ie(e);const n=I(e);n&&(r=n.proxyUrl)}else t=!!e;if(!r)throw c.warn(u),new o.Z("urlutils:proxy-not-set",u);return t&&ce()&&(r=ae(r)),P(r)}function T(e){const t=I(e);let r,n;if(t){const e=M(t.proxyUrl);r=e.path,n=e.query?C(e.query):null}if(r){const t=P(e);e=r+"?"+t.path;const o=j({...n,...t.query});o&&(e=`${e}?${o}`)}return e}const A={path:"",query:""};function M(e){const t=e.indexOf("?");return-1!==t?(A.path=e.slice(0,t),A.query=e.slice(t+1)):(A.path=e,A.query=null),A}function N(e){return(e=le(e=function(e){return e&&"/"===e[e.length-1]?e:`${e}/`}(e=M(e).path),!0)).toLowerCase()}function $(e){const t={proxyUrl:e.proxyUrl,urlPrefix:N(e.urlPrefix)},r=l.proxyRules,n=t.urlPrefix;let o=r.length;for(let e=0;e<r.length;e++){const t=r[e].urlPrefix;if(0===n.indexOf(t)){if(n.length===t.length)return-1;o=e;break}0===t.indexOf(n)&&(o=e+1)}return r.splice(o,0,t),o}function I(e){const t=l.proxyRules,r=N(e);for(let e=0;e<t.length;e++)if(0===r.indexOf(t[e].urlPrefix))return t[e]}function R(e,t){if(!e||!t)return!1;e=U(e),t=U(t);const r=(0,a.a)(e),n=(0,a.a)(t);return(0,i.pC)(r)&&(0,i.pC)(n)?r.portalHostname===n.portalHostname:!(0,i.pC)(r)&&!(0,i.pC)(n)&&J(e,t,!0)}function W(e,t){return e=U(e),t=U(t),le(e)===le(t)}function U(e){const t=(e=F(e)).indexOf("/sharing");return t>0?e.substring(0,t):e.replace(/\/+$/,"")}function L(e){const t=t=>null==t||t instanceof RegExp&&t.test(e)||"string"==typeof t&&e.startsWith(t),r=l.interceptors;if(r)for(const e of r)if(Array.isArray(e.urls)){if(e.urls.some(t))return e}else if(t(e.urls))return e;return null}function J(e,t,r=!1){if(!e||!t)return!1;const n=pe(e),o=pe(t);return!(!r&&n.scheme!==o.scheme)&&null!=n.host&&null!=o.host&&n.host.toLowerCase()===o.host.toLowerCase()&&n.port===o.port}function q(e){if("string"==typeof e){if(!K(e))return!0;e=pe(e)}if(J(e,_))return!0;const t=l.trustedServers||[];for(let r=0;r<t.length;r++){const n=B(t[r]);for(let t=0;t<n.length;t++)if(J(e,n[t]))return!0}return!1}function B(e){return v[e]||(se(e)||oe(e)?v[e]=[new w(H(e))]:v[e]=[new w(`http://${e}`),new w(`https://${e}`)]),v[e]}function H(e,t=O,r){return oe(e)?r&&r.preserveProtocolRelative?e:"http"===_.scheme&&_.authority===z(e).slice(2)?`http:${e}`:`https:${e}`:se(e)?e:(0,i.j0)(D("/"===e[0]?function(e){const t=e.indexOf("//"),r=e.indexOf("/",t+2);return-1===r?e:e.slice(0,r)}(t):t,e))}function Z(e,t=O,r){if(null==e||!K(e))return e;const n=F(e),o=n.toLowerCase(),s=F(t).toLowerCase().replace(/\/+$/,""),i=r?F(r).toLowerCase().replace(/\/+$/,""):null;if(i&&0!==s.indexOf(i))return e;const a=(e,t,r)=>-1===(r=e.indexOf(t,r))?e.length:r;let c=a(o,"/",o.indexOf("//")+2),l=-1;for(;o.slice(0,c+1)===s.slice(0,c)+"/"&&(l=c+1,c!==o.length);)c=a(o,"/",c+1);if(-1===l)return e;if(i&&l<i.length)return e;e=n.slice(l);const u=s.slice(l-1).replace(/[^/]+/g,"").length;if(u>0)for(let t=0;t<u;t++)e=`../${e}`;else e=`./${e}`;return e}function F(e){return function(e){const t=l.httpsDomains;if(!function(e){return null!=e&&d.test(e)||"http"===_.scheme&&oe(e)}(e))return e;const r=e.indexOf("/",7);let n;if(n=-1===r?e:e.slice(0,r),n=n.toLowerCase().slice(7),b.test(n)){if(!n.endsWith(":80"))return e;n=n.slice(0,-3),e=e.replace(":80","")}return"http"===_.scheme&&n===_.authority&&!g.test(e)||(ce()&&n===_.authority||t&&t.some((e=>n===e||n.endsWith(`.${e}`)))||ce()&&!I(e))&&(e=ae(e)),e}(e=function(e){return e.replace(/^(https?:\/\/)(arcgis\.com)/i,"$1www.$2")}(e=function(e){if(/^https?:\/\//i.test(e)){const t=M(e);e=(e=t.path.replace(/\/{2,}/g,"/")).replace("/","//"),t.query&&(e+=`?${t.query}`)}return e}(e=H(e=e.trim()))))}function D(...e){const t=e.filter(i.pC);if(!t||!t.length)return;const r=[];if(K(t[0])){const e=t[0],n=e.indexOf("//");-1!==n&&(r.push(e.slice(0,n+1)),function(e){return null!=e&&p.test(e)}(t[0])&&(r[0]+="/"),t[0]=e.slice(n+2))}else"/"===t[0][0]&&r.push("");const n=t.reduce(((e,t)=>t?e.concat(t.split("/")):e),[]);for(let e=0;e<n.length;e++){const t=n[e];".."===t&&r.length>0&&".."!==r[r.length-1]?r.pop():(!t&&e===n.length-1||t&&("."!==t||0===r.length))&&r.push(t)}return r.join("/")}function z(e,t=!1){if(null==e||V(e)||Q(e))return null;let r=e.indexOf("://");if(-1===r&&oe(e))r=2;else{if(-1===r)return null;r+=3}const n=e.indexOf("/",r);return-1!==n&&(e=e.slice(0,n)),t&&(e=le(e,!0)),e}function K(e){return oe(e)||se(e)}function V(e){return null!=e&&"blob:"===e.slice(0,5)}function Q(e){return null!=e&&"data:"===e.slice(0,5)}function G(e){const t=ee(e);if(!t||!t.isBase64)return null;const r=atob(t.data),n=new Uint8Array(r.length);for(let e=0;e<r.length;e++)n[e]=r.charCodeAt(e);return n.buffer}function Y(e){return btoa(String.fromCharCode.apply(null,e)).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}const X=/^data:(.*?)(;base64)?,(.*)$/;function ee(e){const t=e.match(X);if(!t)return null;const[,r,n,o]=t;return{mediaType:r,isBase64:!!n,data:o}}function te(e){return e.isBase64?`data:${e.mediaType};base64,${e.data}`:`data:${e.mediaType},${e.data}`}function re(e){const t=G(e);if(!t)return null;const r=ee(e);return new Blob([t],{type:r.mediaType})}function ne(e,t){(function(e,t){if(!e)return!1;const r=document.createElement("a");if(!("download"in r))return!1;const n=URL.createObjectURL(e);return r.download=t,r.href=n,r.style.display="none",document.body.appendChild(r),r.click(),document.body.removeChild(r),URL.revokeObjectURL(n),!0})(e,t)||function(e,t){!!window.navigator.msSaveOrOpenBlob&&window.navigator.msSaveOrOpenBlob(e,t)}(e,t)}function oe(e){return null!=e&&"/"===e[0]&&"/"===e[1]}function se(e){return null!=e&&f.test(e)}function ie(e){return null!=e&&h.test(e)||"https"===_.scheme&&oe(e)}function ae(e){return oe(e)?`https:${e}`:e.replace(d,"https:")}function ce(){return"https"===_.scheme}function le(e,t=!1){return oe(e)?e.slice(2):(e=e.replace(f,""),t&&e.length>1&&"/"===e[0]&&"/"===e[1]&&(e=e.slice(2)),e)}function ue(e){let t=0;if(K(e)){const r=e.indexOf("//");-1!==r&&(t=r+2)}const r=e.lastIndexOf("/");return r<t?e:e.slice(0,r+1)}function fe(e,t){if(!e)return"";const r=P(e).path.replace(/\/+$/,""),n=r.substring(r.lastIndexOf("/")+1);if(!t?.length)return n;const o=new RegExp(`.(${t.join("|")})$`,"ig");return n.replace(o,"")}function de(e){return e.replace(/\/+$/,"")}function he(e,t,r){if(!(t&&r&&e&&K(e)))return e;const n=e.indexOf("//"),o=e.indexOf("/",n+2),s=e.indexOf(":",n+2),i=Math.min(o<0?e.length:o,s<0?e.length:s);return e.slice(n+2,i).toLowerCase()!==t.toLowerCase()?e:`${e.slice(0,n+2)}${r}${e.slice(i)}`}function pe(e){return"string"==typeof e?new w(H(e)):(e.scheme||(e.scheme=_.scheme),e)}function be(e,t){const r=P(e),n=Object.keys(r.query||{});return n.length>0&&t&&t.warn("removeQueryParameters()",`Url query parameters are not supported, the following parameters have been removed: ${n.join(", ")}.`),r.path}function ge(e,t,r){const n=P(e),o=n.query||{};return o[t]=String(r),`${n.path}?${j(o)}`}function me(e,t){const r=P(e),n=r.query||{};for(const e in t)n[e]=t[e];const o=j(n);return o?`${r.path}?${o}`:r.path}function ye(e){if((0,i.Wi)(e))return null;const t=e.match(ve);return t?t[2]:null}function we(e){if((0,i.Wi)(e))return null;const t=e.match(ve);return t?{path:t[1],extension:t[2]}:{path:e,extension:null}}const ve=/([^.]*)\.([^\/]*)$/},25045:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>y});var n=r(40330),o=r(20102),s=r(91460),i=r(70586),a=r(95330),c=r(94362),l=r(17202);const u={statsWorker:()=>Promise.all([r.e(1400),r.e(1562),r.e(3847),r.e(6841)]).then(r.bind(r,36841)),geometryEngineWorker:()=>Promise.all([r.e(5837),r.e(8227)]).then(r.bind(r,48227)),CSVSourceWorker:()=>Promise.all([r.e(7126),r.e(4547),r.e(1400),r.e(1534),r.e(1562),r.e(8732),r.e(3847),r.e(3298),r.e(8833),r.e(142)]).then(r.bind(r,27793)),EdgeProcessingWorker:()=>Promise.all([r.e(6481),r.e(212),r.e(2062)]).then(r.bind(r,49594)),ElevationSamplerWorker:()=>Promise.all([r.e(4547),r.e(1400),r.e(1562),r.e(6481),r.e(6459),r.e(9936)]).then(r.bind(r,61787)),FeatureServiceSnappingSourceWorker:()=>Promise.all([r.e(7126),r.e(4165),r.e(4547),r.e(1400),r.e(1534),r.e(1562),r.e(4599),r.e(8732),r.e(3847),r.e(3298),r.e(8833),r.e(3660)]).then(r.bind(r,65967)),GeoJSONSourceWorker:()=>Promise.all([r.e(7126),r.e(4547),r.e(1400),r.e(1534),r.e(1562),r.e(8732),r.e(3847),r.e(3298),r.e(8833),r.e(3594),r.e(7845)]).then(r.bind(r,97845)),LercWorker:()=>r.e(3027).then(r.bind(r,23027)),MemorySourceWorker:()=>Promise.all([r.e(7126),r.e(4547),r.e(1400),r.e(1534),r.e(1562),r.e(8732),r.e(3847),r.e(3298),r.e(8833),r.e(3594),r.e(639)]).then(r.bind(r,30639)),PBFDecoderWorker:()=>Promise.all([r.e(1400),r.e(1562),r.e(8732),r.e(1916)]).then(r.bind(r,61916)),Pipeline:()=>Promise.all([r.e(7126),r.e(1223),r.e(4165),r.e(4547),r.e(1400),r.e(1534),r.e(1562),r.e(4599),r.e(8732),r.e(3847),r.e(3298),r.e(8833),r.e(4325),r.e(1482),r.e(7014)]).then(r.bind(r,92885)),PointCloudWorker:()=>Promise.all([r.e(4547),r.e(1400),r.e(1562),r.e(8643),r.e(9634)]).then(r.bind(r,90447)),RasterWorker:()=>Promise.all([r.e(4547),r.e(1400),r.e(1562),r.e(6610),r.e(8104),r.e(6239)]).then(r.bind(r,61576)),SceneLayerSnappingSourceWorker:()=>Promise.all([r.e(7126),r.e(4547),r.e(1400),r.e(1534),r.e(1562),r.e(8732),r.e(3847),r.e(3298),r.e(6481),r.e(212),r.e(2472)]).then(r.bind(r,73248)),SceneLayerWorker:()=>r.e(1412).then(r.bind(r,31412)),WFSSourceWorker:()=>Promise.all([r.e(7126),r.e(4547),r.e(1400),r.e(1534),r.e(1562),r.e(8732),r.e(3847),r.e(3298),r.e(8833),r.e(3594),r.e(3230)]).then(r.bind(r,43230)),WorkerTileHandler:()=>Promise.all([r.e(4325),r.e(1785),r.e(4982),r.e(6188)]).then(r.bind(r,56456))},{CLOSE:f,ABORT:d,INVOKE:h,RESPONSE:p,OPEN_PORT:b,ON:g}=c.Cs;class m{constructor(e){this._timer=null,this._cancelledJobIds=new Set,this._invokeMessages=[],this._invoke=e,this._timer=null,this._process=this._process.bind(this)}push(e){e.type===c.Cs.ABORT?this._cancelledJobIds.add(e.jobId):(this._invokeMessages.push(e),null===this._timer&&(this._timer=setTimeout(this._process,0)))}clear(){this._invokeMessages.length=0,this._cancelledJobIds.clear(),this._timer=null}_process(){this._timer=null;for(const e of this._invokeMessages)this._cancelledJobIds.has(e.jobId)||this._invoke(e);this._cancelledJobIds.clear(),this._invokeMessages.length=0}}class y{static connect(e){const t=new MessageChannel;let r;r="function"==typeof e?new e:"default"in e&&"function"==typeof e.default?new e.default:e;const n=new y(t.port1,{channel:t,client:r},(()=>null));return"object"==typeof r&&"remoteClient"in r&&(r.remoteClient=n),y.clients.set(n,r),t.port2}static loadWorker(e){const t=u[e];return t?t():Promise.resolve(null)}constructor(e,t,r){this._port=e,this._getNextJob=r,this._outJobs=new Map,this._inJobs=new Map,this._invokeQueue=new m((e=>this._onInvokeMessage(e))),this._client=t.client,this._onMessage=this._onMessage.bind(this),this._channel=t.channel,this._schedule=t.schedule,this._port.addEventListener("message",this._onMessage),this._port.start()}close(){this._post({type:f}),this._close()}isBusy(){return this._outJobs.size>0}invoke(e,t,r){const n=r&&r.signal,s=r&&r.transferList;if(!this._port)return Promise.reject(new o.Z("worker:port-closed",`Cannot call invoke('${e}'), port is closed`,{methodName:e,data:t}));const l=(0,c.jt)();return new Promise(((r,o)=>{if((0,a.Hc)(n))return this._processWork(),void o((0,a.zE)());const c=(0,a.fu)(n,(()=>{const e=this._outJobs.get(l);e&&(this._outJobs.delete(l),this._processWork(),(0,i.hw)(e.abortHandle),this._post({type:d,jobId:l}),o((0,a.zE)()))})),u={resolve:r,reject:o,abortHandle:c,debugInfo:e};this._outJobs.set(l,u),this._post({type:h,jobId:l,methodName:e,abortable:null!=n},t,s)}))}on(e,t){const r=new MessageChannel;function n(e){t(e.data)}return this._port.postMessage({type:c.Cs.ON,eventType:e,port:r.port2},[r.port2]),r.port1.addEventListener("message",n),r.port1.start(),{remove(){r.port1.postMessage({type:c.Cs.CLOSE}),r.port1.close(),r.port1.removeEventListener("message",n)}}}jobAdded(){this._processWork()}openPort(){const e=new MessageChannel;return this._post({type:b,port:e.port2}),e.port1}_processWork(){if(this._outJobs.size>=2)return;const e=this._getNextJob();if(!e)return;const{methodName:t,data:r,invokeOptions:n,deferred:o}=e;this.invoke(t,r,n).then((e=>o.resolve(e))).catch((e=>o.reject(e)))}_close(){this._channel&&(this._channel=void 0),this._port.removeEventListener("message",this._onMessage),this._port.close(),this._outJobs.forEach((e=>{(0,i.hw)(e.abortHandle),e.reject((0,a.zE)(`Worker closing, aborting job calling '${e.debugInfo}'`))})),this._inJobs.clear(),this._outJobs.clear(),this._invokeQueue.clear(),this._port=this._client=this._schedule=null}_onMessage(e){(0,i.pC)(this._schedule)?this._schedule((()=>this._processMessage(e))):this._processMessage(e)}_processMessage(e){const t=(0,c.QM)(e);if(t)switch(t.type){case p:this._onResponseMessage(t);break;case h:this._invokeQueue.push(t);break;case d:this._onAbortMessage(t);break;case f:this._onCloseMessage();break;case b:this._onOpenPortMessage(t);break;case g:this._onOnMessage(t)}}_onAbortMessage(e){const t=this._inJobs,r=e.jobId,n=t.get(r);this._invokeQueue.push(e),n&&(n.controller&&n.controller.abort(),t.delete(r))}_onCloseMessage(){const e=this._client;this._close(),e&&"destroy"in e&&y.clients.get(this)===e&&e.destroy(),y.clients.delete(this),e&&e.remoteClient&&(e.remoteClient=null)}_onInvokeMessage(e){const{methodName:t,jobId:r,data:n,abortable:o}=e,s=o?new AbortController:null,i=this._inJobs;let l,u=this._client,f=u[t];try{if(!f&&t&&t.includes(".")){const e=t.split(".");for(let t=0;t<e.length-1;t++)u=u[e[t]],f=u[e[t+1]]}if("function"!=typeof f)throw new TypeError(`${t} is not a function`);l=f.call(u,n,{client:this,signal:s?s.signal:null})}catch(e){return void this._post({type:p,jobId:r,error:(0,c.AB)(e)})}(0,a.y8)(l)?(i.set(r,{controller:s,promise:l}),l.then((e=>{i.has(r)&&(i.delete(r),this._post({type:p,jobId:r},e))}),(e=>{i.has(r)&&(i.delete(r),(0,a.D_)(e)||this._post({type:p,jobId:r,error:(0,c.AB)(e||{message:`Error encountered at method ${t}`})}))}))):this._post({type:p,jobId:r},l)}_onOpenPortMessage(e){new y(e.port,{client:this._client},(()=>null))}_onOnMessage(e){const{port:t}=e,r=this._client.on(e.eventType,(e=>{t.postMessage(e)})),n=(0,s.on)(e.port,"message",(e=>{(0,c.QM)(e)?.type===c.Cs.CLOSE&&(n.remove(),r.remove(),t.close())}))}_onResponseMessage(e){const{jobId:t,error:r,data:n}=e,s=this._outJobs;if(!s.has(t))return;const a=s.get(t);s.delete(t),this._processWork(),(0,i.hw)(a.abortHandle),r?a.reject(o.Z.fromJSON(JSON.parse(r))):a.resolve(n)}_post(e,t,r){return(0,c.oi)(this._port,e,t,r)}}y.kernelInfo={revision:l.$,version:n.i8,buildDate:l.r},y.clients=new Map},94362:(e,t,r)=>{"use strict";r.d(t,{AB:()=>a,Cs:()=>n,QM:()=>l,jt:()=>i,oi:()=>c});var n,o=r(80442);!function(e){e[e.HANDSHAKE=0]="HANDSHAKE",e[e.OPEN=1]="OPEN",e[e.OPENED=2]="OPENED",e[e.RESPONSE=3]="RESPONSE",e[e.INVOKE=4]="INVOKE",e[e.ABORT=5]="ABORT",e[e.CLOSE=6]="CLOSE",e[e.OPEN_PORT=7]="OPEN_PORT",e[e.ON=8]="ON"}(n||(n={}));let s=0;function i(){return s++}function a(e){return e?"string"==typeof e?JSON.stringify({name:"message",message:e}):e.toJSON?JSON.stringify(e):JSON.stringify({name:e.name,message:e.message,details:e.details||{stack:e.stack}}):null}function c(e,t,r,s){if(t.type===n.OPEN_PORT)return void e.postMessage(t,[t.port]);if(t.type!==n.INVOKE&&t.type!==n.RESPONSE)return void e.postMessage(t);let i;if(function(e){return e&&"object"==typeof e&&("result"in e||"transferList"in e)}(r)?(i=u(r.transferList),t.data=r.result):(i=u(s),t.data=r),i){if((0,o.Z)("ff"))for(const r of i)if("byteLength"in r&&r.byteLength>267386880){const r="Worker call with large ArrayBuffer would crash Firefox";switch(t.type){case n.INVOKE:throw r;case n.RESPONSE:return void c(e,{type:n.RESPONSE,jobId:t.jobId,error:a(r)})}}e.postMessage(t,i)}else e.postMessage(t)}function l(e){if(!e)return null;const t=e.data;return t?"string"==typeof t?JSON.parse(t):t:null}function u(e){if(!e||!e.length)return null;if((0,o.Z)("esri-workers-arraybuffer-transfer"))return e;const t=e.filter((e=>!function(e){return e instanceof ArrayBuffer||e&&e.constructor&&"ArrayBuffer"===e.constructor.name}(e)));return t.length?t:null}},40330:(e,t,r)=>{"use strict";r.d(t,{Dp:()=>l,Nv:()=>i,i8:()=>s,id:()=>a,qh:()=>c});var n=r(80442),o=r(17452);const s="4.26",i={async request(e,t){const{default:n}=await r.e(3172).then(r.bind(r,3172)),s=e.options,i=s.responseType;s.signal=t?.signal,s.responseType="native"===i||"native-request-init"===i?"native-request-init":i&&["blob","json","text"].includes(i)&&(0,o.oh)(e.url)?.after?i:"array-buffer";const a=await n(e.url,s),c={data:a.data,httpStatus:a.httpStatus,ssl:a.ssl};switch(a.requestOptions?.responseType){case"native-request-init":return delete c.data.signal,c;case"blob":c.data=await c.data.arrayBuffer();break;case"json":c.data=(new TextEncoder).encode(JSON.stringify(c.data)).buffer;break;case"text":c.data=(new TextEncoder).encode(c.data).buffer}return{result:c,transferList:[c.data]}}};let a;function c(e){a=e}function l(e){const t=a&&a.findCredential(e);return t&&t.token?(0,o.ZN)(e,"token",t.token):e}(0,n.Z)("host-webworker")},19745:(e,t,r)=>{"use strict";r.d(t,{P:()=>i,a:()=>s});const n=/^https:\/\/([a-z\d-]+)(\.maps([^.]*))?\.arcgis\.com/i,o={devext:{customBaseUrl:"mapsdevext.arcgis.com",portalHostname:"devext.arcgis.com"},qaext:{customBaseUrl:"mapsqa.arcgis.com",portalHostname:"qaext.arcgis.com"},www:{customBaseUrl:"maps.arcgis.com",portalHostname:"www.arcgis.com"}};function s(e){const t=e?.match(n);if(!t)return null;const[,r,s,i]=t;if(!r)return null;let a=null,c=null,l=null;const{devext:u,qaext:f,www:d}=o;if(s)if(a=r,i)switch(i.toLowerCase()){case"devext":({customBaseUrl:c,portalHostname:l}=u);break;case"qa":({customBaseUrl:c,portalHostname:l}=f);break;default:return null}else({customBaseUrl:c,portalHostname:l}=d);else switch(r.toLowerCase()){case"devext":({customBaseUrl:c,portalHostname:l}=u);break;case"qaext":({customBaseUrl:c,portalHostname:l}=f);break;case"www":({customBaseUrl:c,portalHostname:l}=d);break;default:return null}return{customBaseUrl:c,isPortal:!1,portalHostname:l,urlKey:a}}function i(e){return/\/(sharing|usrsvcs)\/(appservices|servers)\//i.test(e)}},17202:(e,t,r)=>{"use strict";r.d(t,{$:()=>o,r:()=>n});const n="20230301",o="2657e728c1857e6d94c324181c0788310bb0958a"}},t={};function r(n){var o=t[n];if(void 0!==o)return o.exports;var s=t[n]={exports:{}};return e[n](s,s.exports,r),s.exports}r.m=e,r.d=(e,t)=>{for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.f={},r.e=e=>Promise.all(Object.keys(r.f).reduce(((t,n)=>(r.f[n](e,t),t)),[])),r.u=e=>"chunks/"+{24:"6ecf0bb8e16159160cde",81:"999698bb5c1babff2edd",142:"d3498d28fd01a0ad3899",212:"f3f2759b39ab76cde66c",223:"1d5b600c2ebab2a5fa29",247:"a9f83bbccf3b5f229262",450:"80fd99a584669ce225f7",540:"96536f507bc6e501a4f7",565:"969bb94d5bdc9da114e2",576:"5ec4fedbbcbb0053cf74",639:"1186a56dacaf269d5271",665:"18feb69bb066f529f4a5",690:"f91551e2c5cda83c8fa1",719:"0219da87e51708416bc9",739:"5983c9f28b7f63c7e1e0",819:"d1ead6e8aa7e8e21fbc6",908:"413046749019790fd00f",911:"2187672a72e776b9b153",1073:"7bc7b6cf75771687d81f",1074:"1b5dce4654bf7a2b54d3",1158:"efade11c5dc55465a95e",1223:"4125b10b1d009f35e75e",1227:"b8b6df646ecf5482c5b4",1400:"09ac51aeb0f6fe16aa30",1412:"cd1c8a333faa3a18ddba",1421:"106329846470141953ad",1423:"d00bd1c0f003417d97c7",1433:"4d5bdf18feeb9c8cb6ff",1482:"47195afa66f640cb4b64",1534:"9dcbecdc884175c417db",1537:"29422efdd90961f52120",1562:"2ad66950b345bc8b5fba",1612:"ae9f84da87c2d035f5bb",1785:"19ef2e381bc09d3726f3",1790:"724cfd0ca8e7e0476f99",1916:"208e24c056f90ac78f88",1932:"c423c1cf02fb419604ba",1984:"03081e28fe59ba2e8040",2062:"f9e0b8eaf99f48393a6e",2134:"0867268dbc626bd6aa62",2156:"253a3e9ce0521a005134",2462:"bcdd71e7475777f0f1bb",2472:"d940b285d08b3b7f2a8b",2664:"670880e249ff43fcd485",2710:"786acd608d8e4499b4b4",2855:"1fd48420dfbbfdb3b051",3011:"bf5c34d81724e9bdc182",3027:"fce9f487437d3c5ef080",3055:"a51c2c5d3e7f764f44d5",3172:"e2d8b09ed32d3e43b104",3230:"dde60d0978da029863ee",3298:"00e59229bab0f3b96919",3529:"84c0a8d41eec91bb5142",3594:"a2e5b67c1c65a93f3c75",3660:"722e06dc25299d5f11b0",3668:"0d0bca75414f15371baf",3847:"3808f33563757fad703d",3852:"d4338bc4a99b71c44dc2",3974:"009cd8f27d2357ec9317",3975:"7a863dfc7be412d5bf4e",3992:"bbd8c877224b1d56e125",4165:"9054fd0707a15d037bb9",4166:"2db1fcfe09c871f3fefe",4242:"8f3262f33d635cf9f046",4286:"144a0b86d5b3db7db2f6",4325:"e95e449c3fab1a03498c",4358:"eab47a18717478014e03",4371:"1756e2395cd5f83deac9",4475:"a84ad71a80c3f84869a7",4499:"f28cd2654583aebe2c03",4547:"f7a57c012fb9f864d28f",4599:"a6e9bbcfb2990e2af8db",4695:"cdf7b4b87132af9a8ca8",4729:"034c2ffa3fe2234dfd58",4982:"8c8de7cee237117f2365",5004:"592e9834aa896cdd0e86",5103:"5a5e4983c526bf08c7f1",5115:"38ebf5d20576609566f9",5132:"17ce4b146825adcca307",5159:"fbe981e42e92a32d922d",5235:"979d5e22dbdcec90029f",5329:"0e21e5b4d065567a6a91",5546:"07236a5f324bbd6d7d01",5587:"dfbe4f2092d2f99024d7",5590:"e6d10bbb3b6e875bf217",5638:"5598996bcf60741aa734",5642:"348a3321fc757ca1f948",5660:"f30128d41339cf6991c1",5837:"c419a26e2b7d20d99cef",5853:"19ed87aaf7b63ea62fae",5935:"60a08ce29d09cb07ef06",6188:"41d4257861ac41190da1",6233:"acfde5a4cce3a4589f15",6237:"1aaf284ce0ee24053bd1",6239:"deb2feece014a44c6486",6261:"14b113a6fdcf81e56674",6368:"00363fca52417a6dec64",6459:"a935803e70a75e04d0de",6481:"f6c39edd04cb96d98240",6502:"d363d6dda0043430bc69",6565:"226bfa115acb8542f21c",6584:"c999e9dd748ae449d66e",6610:"bdeb15d8c536cc64f8da",6695:"5e3079b3666df3a9d235",6710:"c860891552d6d886394b",6748:"bdc01fbee999fb848db2",6772:"dd698ada6b7c03cfd25c",6774:"22677d246a80195507e6",6841:"beb8a9ceaee0a1a5441a",7014:"81949c9d14a8d75c45b6",7035:"61cdba0e7382d176510b",7126:"d6842acbfa7a39f3ca30",7202:"611c27efa10e4e04d8b6",7269:"1693ccb22ddb0e075cb7",7277:"7b9bdc287ab1bbe0bad6",7374:"48d35993b6ffb76e56e9",7476:"a9caab077293ba882dc5",7483:"29cffde9317695328693",7541:"8440b51d07263ad5bd50",7845:"2680392cef77d98ad097",7873:"21f7d3400c070aa45596",8008:"eca1b840c009e5477b80",8062:"ebd9f923cef87ddbcb14",8092:"f1b26b73101210b8f29c",8096:"22a990a8d4d4587866a5",8104:"c24df96ab71c44dac45c",8153:"2f0dd66dbc9bf850ed76",8227:"1c9fff56d6e1cc3df63f",8239:"5022862c5658e7bb0e04",8244:"674d4a032e6db9a86f74",8636:"ec6621ff3a5d300e00f9",8643:"685302b6a325d881ac8e",8732:"99e16173584702577b80",8758:"4e08816d1614a14cd38a",8828:"b8ed81e6b3eda25064d6",8833:"bce1d4d6449a646834a1",8915:"6ad060f99d29063d3675",9070:"76df52cb37efaba366d1",9169:"ac4c10194e904840a4ad",9230:"fc2177397ec09e9c16d4",9238:"b4946205951f40f68357",9243:"6c519c5a474f51f37af0",9291:"179efcf889111d2576c5",9296:"752488db46d9b79d99f8",9327:"55782608bee89fa0d6e1",9393:"995ea389fb778b25d927",9538:"5f8acce073b8da5ac6ae",9634:"876130daea9b49017099",9675:"85c6a2c7239d0cf73a44",9717:"e587adb29ea2209d5a58",9746:"a7e63b6c6d462fb9cff0",9771:"cb80cf1c614ae3b2f5d6",9790:"7acc061da63714d388da",9880:"1b5bdd80db9a1a410ff8",9884:"a6866c770e341d26d265",9904:"22e0a27e1751418d9b50",9936:"96bac640033936ab4689",9942:"39c8dd60ae7b517d664c",9971:"f255d3633b1ca30a2f94"}[e]+".js",r.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},(()=>{var e;r.g.importScripts&&(e=r.g.location+"");var t=r.g.document;if(!e&&t&&(t.currentScript&&(e=t.currentScript.src),!e)){var n=t.getElementsByTagName("script");n.length&&(e=n[n.length-1].src)}if(!e)throw new Error("Automatic publicPath is not supported in this browser");e=e.replace(/#.*$/,"").replace(/\?.*$/,"").replace(/\/[^\/]+$/,"/"),r.p=e})(),(()=>{var e={134:1};r.f.i=(t,n)=>{e[t]||importScripts(r.p+r.u(t))};var t=self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[],n=t.push.bind(t);t.push=t=>{var[o,s,i]=t;for(var a in s)r.o(s,a)&&(r.m[a]=s[a]);for(i&&i(r);o.length;)e[o.pop()]=1;n(t)}})(),r(88277);var n=r(25045);RemoteClient=n})();