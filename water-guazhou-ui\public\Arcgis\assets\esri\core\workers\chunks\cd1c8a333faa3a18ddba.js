"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[1412,3172,9880],{99880:(t,e,n)=>{n.d(e,{V:()=>u});var r=n(68773),o=(n(3172),n(20102)),a=n(92604),s=n(17452);const i=a.Z.getLogger("esri.assets");function u(t){if(!r.Z.assetsPath)throw i.errorOnce("The API assets location needs to be set using config.assetsPath. More information: https://arcg.is/1OzLe50"),new o.Z("assets:path-not-set","config.assetsPath is not set");return(0,s.v_)(r.Z.assetsPath,t)}},46851:(t,e,n)=>{n.d(e,{R:()=>a,a:()=>c,g:()=>o,t:()=>u});let r=1e-6;function o(){return r}const a=Math.random,s=Math.PI/180,i=180/Math.PI;function u(t){return t*s}function c(t){return t*i}Object.freeze(Object.defineProperty({__proto__:null,RANDOM:a,equals:function(t,e){return Math.abs(t-e)<=r*Math.max(1,Math.abs(t),Math.abs(e))},getEpsilon:o,setEpsilon:function(t){r=t},toDegree:c,toRadian:u},Symbol.toStringTag,{value:"Module"}))},46521:(t,e,n)=>{function r(){return[1,0,0,0,1,0,0,0,1]}function o(t,e,n,r,o,a,s,i,u){return[t,e,n,r,o,a,s,i,u]}function a(t,e){return new Float64Array(t,e,9)}n.d(e,{a:()=>a,c:()=>r,f:()=>o}),Object.freeze(Object.defineProperty({__proto__:null,clone:function(t){return[t[0],t[1],t[2],t[3],t[4],t[5],t[6],t[7],t[8]]},create:r,createView:a,fromValues:o},Symbol.toStringTag,{value:"Module"}))},52138:(t,e,n)=>{n.d(e,{A:()=>M,a:()=>u,d:()=>m,e:()=>h,f:()=>d,g:()=>g,h:()=>_,i:()=>s,j:()=>w,k:()=>l,m:()=>c,s:()=>a,t:()=>i,w:()=>f,y:()=>T,z:()=>E});var r=n(65617),o=n(46851);function a(t,e,n,r,o,a,s,i,u,c,f,l,h,d,m,M,b){return t[0]=e,t[1]=n,t[2]=r,t[3]=o,t[4]=a,t[5]=s,t[6]=i,t[7]=u,t[8]=c,t[9]=f,t[10]=l,t[11]=h,t[12]=d,t[13]=m,t[14]=M,t[15]=b,t}function s(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=1,t[6]=0,t[7]=0,t[8]=0,t[9]=0,t[10]=1,t[11]=0,t[12]=0,t[13]=0,t[14]=0,t[15]=1,t}function i(t,e){if(t===e){const n=e[1],r=e[2],o=e[3],a=e[6],s=e[7],i=e[11];t[1]=e[4],t[2]=e[8],t[3]=e[12],t[4]=n,t[6]=e[9],t[7]=e[13],t[8]=r,t[9]=a,t[11]=e[14],t[12]=o,t[13]=s,t[14]=i}else t[0]=e[0],t[1]=e[4],t[2]=e[8],t[3]=e[12],t[4]=e[1],t[5]=e[5],t[6]=e[9],t[7]=e[13],t[8]=e[2],t[9]=e[6],t[10]=e[10],t[11]=e[14],t[12]=e[3],t[13]=e[7],t[14]=e[11],t[15]=e[15];return t}function u(t,e){const n=e[0],r=e[1],o=e[2],a=e[3],s=e[4],i=e[5],u=e[6],c=e[7],f=e[8],l=e[9],h=e[10],d=e[11],m=e[12],M=e[13],b=e[14],p=e[15],g=n*i-r*s,y=n*u-o*s,w=n*c-a*s,_=r*u-o*i,E=r*c-a*i,T=o*c-a*u,S=f*M-l*m,P=f*b-h*m,v=f*p-d*m,O=l*b-h*M,q=l*p-d*M,x=h*p-d*b;let I=g*x-y*q+w*O+_*v-E*P+T*S;return I?(I=1/I,t[0]=(i*x-u*q+c*O)*I,t[1]=(o*q-r*x-a*O)*I,t[2]=(M*T-b*E+p*_)*I,t[3]=(h*E-l*T-d*_)*I,t[4]=(u*v-s*x-c*P)*I,t[5]=(n*x-o*v+a*P)*I,t[6]=(b*w-m*T-p*y)*I,t[7]=(f*T-h*w+d*y)*I,t[8]=(s*q-i*v+c*S)*I,t[9]=(r*v-n*q-a*S)*I,t[10]=(m*E-M*w+p*g)*I,t[11]=(l*w-f*E-d*g)*I,t[12]=(i*P-s*O-u*S)*I,t[13]=(n*O-r*P+o*S)*I,t[14]=(M*y-m*_-b*g)*I,t[15]=(f*_-l*y+h*g)*I,t):null}function c(t,e,n){const r=e[0],o=e[1],a=e[2],s=e[3],i=e[4],u=e[5],c=e[6],f=e[7],l=e[8],h=e[9],d=e[10],m=e[11],M=e[12],b=e[13],p=e[14],g=e[15];let y=n[0],w=n[1],_=n[2],E=n[3];return t[0]=y*r+w*i+_*l+E*M,t[1]=y*o+w*u+_*h+E*b,t[2]=y*a+w*c+_*d+E*p,t[3]=y*s+w*f+_*m+E*g,y=n[4],w=n[5],_=n[6],E=n[7],t[4]=y*r+w*i+_*l+E*M,t[5]=y*o+w*u+_*h+E*b,t[6]=y*a+w*c+_*d+E*p,t[7]=y*s+w*f+_*m+E*g,y=n[8],w=n[9],_=n[10],E=n[11],t[8]=y*r+w*i+_*l+E*M,t[9]=y*o+w*u+_*h+E*b,t[10]=y*a+w*c+_*d+E*p,t[11]=y*s+w*f+_*m+E*g,y=n[12],w=n[13],_=n[14],E=n[15],t[12]=y*r+w*i+_*l+E*M,t[13]=y*o+w*u+_*h+E*b,t[14]=y*a+w*c+_*d+E*p,t[15]=y*s+w*f+_*m+E*g,t}function f(t,e,n){const r=n[0],o=n[1],a=n[2];if(e===t)t[12]=e[0]*r+e[4]*o+e[8]*a+e[12],t[13]=e[1]*r+e[5]*o+e[9]*a+e[13],t[14]=e[2]*r+e[6]*o+e[10]*a+e[14],t[15]=e[3]*r+e[7]*o+e[11]*a+e[15];else{const n=e[0],s=e[1],i=e[2],u=e[3],c=e[4],f=e[5],l=e[6],h=e[7],d=e[8],m=e[9],M=e[10],b=e[11];t[0]=n,t[1]=s,t[2]=i,t[3]=u,t[4]=c,t[5]=f,t[6]=l,t[7]=h,t[8]=d,t[9]=m,t[10]=M,t[11]=b,t[12]=n*r+c*o+d*a+e[12],t[13]=s*r+f*o+m*a+e[13],t[14]=i*r+l*o+M*a+e[14],t[15]=u*r+h*o+b*a+e[15]}return t}function l(t,e,n){const r=n[0],o=n[1],a=n[2];return t[0]=e[0]*r,t[1]=e[1]*r,t[2]=e[2]*r,t[3]=e[3]*r,t[4]=e[4]*o,t[5]=e[5]*o,t[6]=e[6]*o,t[7]=e[7]*o,t[8]=e[8]*a,t[9]=e[9]*a,t[10]=e[10]*a,t[11]=e[11]*a,t[12]=e[12],t[13]=e[13],t[14]=e[14],t[15]=e[15],t}function h(t,e,n,r){let a,s,i,u,c,f,l,h,d,m,M,b,p,g,y,w,_,E,T,S,P,v,O,q,x=r[0],I=r[1],A=r[2],C=Math.sqrt(x*x+I*I+A*A);return C<(0,o.g)()?null:(C=1/C,x*=C,I*=C,A*=C,a=Math.sin(n),s=Math.cos(n),i=1-s,u=e[0],c=e[1],f=e[2],l=e[3],h=e[4],d=e[5],m=e[6],M=e[7],b=e[8],p=e[9],g=e[10],y=e[11],w=x*x*i+s,_=I*x*i+A*a,E=A*x*i-I*a,T=x*I*i-A*a,S=I*I*i+s,P=A*I*i+x*a,v=x*A*i+I*a,O=I*A*i-x*a,q=A*A*i+s,t[0]=u*w+h*_+b*E,t[1]=c*w+d*_+p*E,t[2]=f*w+m*_+g*E,t[3]=l*w+M*_+y*E,t[4]=u*T+h*S+b*P,t[5]=c*T+d*S+p*P,t[6]=f*T+m*S+g*P,t[7]=l*T+M*S+y*P,t[8]=u*v+h*O+b*q,t[9]=c*v+d*O+p*q,t[10]=f*v+m*O+g*q,t[11]=l*v+M*O+y*q,e!==t&&(t[12]=e[12],t[13]=e[13],t[14]=e[14],t[15]=e[15]),t)}function d(t,e){return t[0]=1,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=1,t[6]=0,t[7]=0,t[8]=0,t[9]=0,t[10]=1,t[11]=0,t[12]=e[0],t[13]=e[1],t[14]=e[2],t[15]=1,t}function m(t,e,n){if(0===e)return s(t);let r,a,i,u=n[0],c=n[1],f=n[2],l=Math.sqrt(u*u+c*c+f*f);return l<(0,o.g)()?null:(l=1/l,u*=l,c*=l,f*=l,r=Math.sin(e),a=Math.cos(e),i=1-a,t[0]=u*u*i+a,t[1]=c*u*i+f*r,t[2]=f*u*i-c*r,t[3]=0,t[4]=u*c*i-f*r,t[5]=c*c*i+a,t[6]=f*c*i+u*r,t[7]=0,t[8]=u*f*i+c*r,t[9]=c*f*i-u*r,t[10]=f*f*i+a,t[11]=0,t[12]=0,t[13]=0,t[14]=0,t[15]=1,t)}function M(t,e){const n=Math.sin(e),r=Math.cos(e);return t[0]=1,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=r,t[6]=n,t[7]=0,t[8]=0,t[9]=-n,t[10]=r,t[11]=0,t[12]=0,t[13]=0,t[14]=0,t[15]=1,t}function b(t,e,n){const r=e[0],o=e[1],a=e[2],s=e[3],i=r+r,u=o+o,c=a+a,f=r*i,l=r*u,h=r*c,d=o*u,m=o*c,M=a*c,b=s*i,p=s*u,g=s*c;return t[0]=1-(d+M),t[1]=l+g,t[2]=h-p,t[3]=0,t[4]=l-g,t[5]=1-(f+M),t[6]=m+b,t[7]=0,t[8]=h+p,t[9]=m-b,t[10]=1-(f+d),t[11]=0,t[12]=n[0],t[13]=n[1],t[14]=n[2],t[15]=1,t}const p=(0,r.c)();function g(t,e,n,r){const o=e[0],a=e[1],s=e[2],i=e[3],u=o+o,c=a+a,f=s+s,l=o*u,h=o*c,d=o*f,m=a*c,M=a*f,b=s*f,p=i*u,g=i*c,y=i*f,w=r[0],_=r[1],E=r[2];return t[0]=(1-(m+b))*w,t[1]=(h+y)*w,t[2]=(d-g)*w,t[3]=0,t[4]=(h-y)*_,t[5]=(1-(l+b))*_,t[6]=(M+p)*_,t[7]=0,t[8]=(d+g)*E,t[9]=(M-p)*E,t[10]=(1-(l+m))*E,t[11]=0,t[12]=n[0],t[13]=n[1],t[14]=n[2],t[15]=1,t}function y(t,e,n){return t[0]=e[0]-n[0],t[1]=e[1]-n[1],t[2]=e[2]-n[2],t[3]=e[3]-n[3],t[4]=e[4]-n[4],t[5]=e[5]-n[5],t[6]=e[6]-n[6],t[7]=e[7]-n[7],t[8]=e[8]-n[8],t[9]=e[9]-n[9],t[10]=e[10]-n[10],t[11]=e[11]-n[11],t[12]=e[12]-n[12],t[13]=e[13]-n[13],t[14]=e[14]-n[14],t[15]=e[15]-n[15],t}function w(t,e){return t[0]===e[0]&&t[1]===e[1]&&t[2]===e[2]&&t[3]===e[3]&&t[4]===e[4]&&t[5]===e[5]&&t[6]===e[6]&&t[7]===e[7]&&t[8]===e[8]&&t[9]===e[9]&&t[10]===e[10]&&t[11]===e[11]&&t[12]===e[12]&&t[13]===e[13]&&t[14]===e[14]&&t[15]===e[15]}function _(t,e){if(t===e)return!0;const n=t[0],r=t[1],a=t[2],s=t[3],i=t[4],u=t[5],c=t[6],f=t[7],l=t[8],h=t[9],d=t[10],m=t[11],M=t[12],b=t[13],p=t[14],g=t[15],y=e[0],w=e[1],_=e[2],E=e[3],T=e[4],S=e[5],P=e[6],v=e[7],O=e[8],q=e[9],x=e[10],I=e[11],A=e[12],C=e[13],L=e[14],k=e[15],R=(0,o.g)();return Math.abs(n-y)<=R*Math.max(1,Math.abs(n),Math.abs(y))&&Math.abs(r-w)<=R*Math.max(1,Math.abs(r),Math.abs(w))&&Math.abs(a-_)<=R*Math.max(1,Math.abs(a),Math.abs(_))&&Math.abs(s-E)<=R*Math.max(1,Math.abs(s),Math.abs(E))&&Math.abs(i-T)<=R*Math.max(1,Math.abs(i),Math.abs(T))&&Math.abs(u-S)<=R*Math.max(1,Math.abs(u),Math.abs(S))&&Math.abs(c-P)<=R*Math.max(1,Math.abs(c),Math.abs(P))&&Math.abs(f-v)<=R*Math.max(1,Math.abs(f),Math.abs(v))&&Math.abs(l-O)<=R*Math.max(1,Math.abs(l),Math.abs(O))&&Math.abs(h-q)<=R*Math.max(1,Math.abs(h),Math.abs(q))&&Math.abs(d-x)<=R*Math.max(1,Math.abs(d),Math.abs(x))&&Math.abs(m-I)<=R*Math.max(1,Math.abs(m),Math.abs(I))&&Math.abs(M-A)<=R*Math.max(1,Math.abs(M),Math.abs(A))&&Math.abs(b-C)<=R*Math.max(1,Math.abs(b),Math.abs(C))&&Math.abs(p-L)<=R*Math.max(1,Math.abs(p),Math.abs(L))&&Math.abs(g-k)<=R*Math.max(1,Math.abs(g),Math.abs(k))}function E(t){const e=(0,o.g)(),n=t[0],r=t[1],a=t[2],s=t[4],i=t[5],u=t[6],c=t[8],f=t[9],l=t[10];return Math.abs(1-(n*n+s*s+c*c))<=e&&Math.abs(1-(r*r+i*i+f*f))<=e&&Math.abs(1-(a*a+u*u+l*l))<=e}function T(t){return 1===t[0]&&0===t[1]&&0===t[2]&&0===t[4]&&1===t[5]&&0===t[6]&&0===t[8]&&0===t[9]&&1===t[10]}const S=c,P=y;Object.freeze(Object.defineProperty({__proto__:null,add:function(t,e,n){return t[0]=e[0]+n[0],t[1]=e[1]+n[1],t[2]=e[2]+n[2],t[3]=e[3]+n[3],t[4]=e[4]+n[4],t[5]=e[5]+n[5],t[6]=e[6]+n[6],t[7]=e[7]+n[7],t[8]=e[8]+n[8],t[9]=e[9]+n[9],t[10]=e[10]+n[10],t[11]=e[11]+n[11],t[12]=e[12]+n[12],t[13]=e[13]+n[13],t[14]=e[14]+n[14],t[15]=e[15]+n[15],t},adjoint:function(t,e){const n=e[0],r=e[1],o=e[2],a=e[3],s=e[4],i=e[5],u=e[6],c=e[7],f=e[8],l=e[9],h=e[10],d=e[11],m=e[12],M=e[13],b=e[14],p=e[15];return t[0]=i*(h*p-d*b)-l*(u*p-c*b)+M*(u*d-c*h),t[1]=-(r*(h*p-d*b)-l*(o*p-a*b)+M*(o*d-a*h)),t[2]=r*(u*p-c*b)-i*(o*p-a*b)+M*(o*c-a*u),t[3]=-(r*(u*d-c*h)-i*(o*d-a*h)+l*(o*c-a*u)),t[4]=-(s*(h*p-d*b)-f*(u*p-c*b)+m*(u*d-c*h)),t[5]=n*(h*p-d*b)-f*(o*p-a*b)+m*(o*d-a*h),t[6]=-(n*(u*p-c*b)-s*(o*p-a*b)+m*(o*c-a*u)),t[7]=n*(u*d-c*h)-s*(o*d-a*h)+f*(o*c-a*u),t[8]=s*(l*p-d*M)-f*(i*p-c*M)+m*(i*d-c*l),t[9]=-(n*(l*p-d*M)-f*(r*p-a*M)+m*(r*d-a*l)),t[10]=n*(i*p-c*M)-s*(r*p-a*M)+m*(r*c-a*i),t[11]=-(n*(i*d-c*l)-s*(r*d-a*l)+f*(r*c-a*i)),t[12]=-(s*(l*b-h*M)-f*(i*b-u*M)+m*(i*h-u*l)),t[13]=n*(l*b-h*M)-f*(r*b-o*M)+m*(r*h-o*l),t[14]=-(n*(i*b-u*M)-s*(r*b-o*M)+m*(r*u-o*i)),t[15]=n*(i*h-u*l)-s*(r*h-o*l)+f*(r*u-o*i),t},copy:function(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4],t[5]=e[5],t[6]=e[6],t[7]=e[7],t[8]=e[8],t[9]=e[9],t[10]=e[10],t[11]=e[11],t[12]=e[12],t[13]=e[13],t[14]=e[14],t[15]=e[15],t},determinant:function(t){const e=t[0],n=t[1],r=t[2],o=t[3],a=t[4],s=t[5],i=t[6],u=t[7],c=t[8],f=t[9],l=t[10],h=t[11],d=t[12],m=t[13],M=t[14],b=t[15];return(e*s-n*a)*(l*b-h*M)-(e*i-r*a)*(f*b-h*m)+(e*u-o*a)*(f*M-l*m)+(n*i-r*s)*(c*b-h*d)-(n*u-o*s)*(c*M-l*d)+(r*u-o*i)*(c*m-f*d)},equals:_,exactEquals:w,frob:function(t){return Math.sqrt(t[0]**2+t[1]**2+t[2]**2+t[3]**2+t[4]**2+t[5]**2+t[6]**2+t[7]**2+t[8]**2+t[9]**2+t[10]**2+t[11]**2+t[12]**2+t[13]**2+t[14]**2+t[15]**2)},fromQuat:function(t,e){const n=e[0],r=e[1],o=e[2],a=e[3],s=n+n,i=r+r,u=o+o,c=n*s,f=r*s,l=r*i,h=o*s,d=o*i,m=o*u,M=a*s,b=a*i,p=a*u;return t[0]=1-l-m,t[1]=f+p,t[2]=h-b,t[3]=0,t[4]=f-p,t[5]=1-c-m,t[6]=d+M,t[7]=0,t[8]=h+b,t[9]=d-M,t[10]=1-c-l,t[11]=0,t[12]=0,t[13]=0,t[14]=0,t[15]=1,t},fromQuat2:function(t,e){const n=p,r=-e[0],o=-e[1],a=-e[2],s=e[3],i=e[4],u=e[5],c=e[6],f=e[7],l=r*r+o*o+a*a+s*s;return l>0?(n[0]=2*(i*s+f*r+u*a-c*o)/l,n[1]=2*(u*s+f*o+c*r-i*a)/l,n[2]=2*(c*s+f*a+i*o-u*r)/l):(n[0]=2*(i*s+f*r+u*a-c*o),n[1]=2*(u*s+f*o+c*r-i*a),n[2]=2*(c*s+f*a+i*o-u*r)),b(t,e,n),t},fromRotation:m,fromRotationTranslation:b,fromRotationTranslationScale:g,fromRotationTranslationScaleOrigin:function(t,e,n,r,o){const a=e[0],s=e[1],i=e[2],u=e[3],c=a+a,f=s+s,l=i+i,h=a*c,d=a*f,m=a*l,M=s*f,b=s*l,p=i*l,g=u*c,y=u*f,w=u*l,_=r[0],E=r[1],T=r[2],S=o[0],P=o[1],v=o[2],O=(1-(M+p))*_,q=(d+w)*_,x=(m-y)*_,I=(d-w)*E,A=(1-(h+p))*E,C=(b+g)*E,L=(m+y)*T,k=(b-g)*T,R=(1-(h+M))*T;return t[0]=O,t[1]=q,t[2]=x,t[3]=0,t[4]=I,t[5]=A,t[6]=C,t[7]=0,t[8]=L,t[9]=k,t[10]=R,t[11]=0,t[12]=n[0]+S-(O*S+I*P+L*v),t[13]=n[1]+P-(q*S+A*P+k*v),t[14]=n[2]+v-(x*S+C*P+R*v),t[15]=1,t},fromScaling:function(t,e){return t[0]=e[0],t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=e[1],t[6]=0,t[7]=0,t[8]=0,t[9]=0,t[10]=e[2],t[11]=0,t[12]=0,t[13]=0,t[14]=0,t[15]=1,t},fromTranslation:d,fromXRotation:M,fromYRotation:function(t,e){const n=Math.sin(e),r=Math.cos(e);return t[0]=r,t[1]=0,t[2]=-n,t[3]=0,t[4]=0,t[5]=1,t[6]=0,t[7]=0,t[8]=n,t[9]=0,t[10]=r,t[11]=0,t[12]=0,t[13]=0,t[14]=0,t[15]=1,t},fromZRotation:function(t,e){const n=Math.sin(e),r=Math.cos(e);return t[0]=r,t[1]=n,t[2]=0,t[3]=0,t[4]=-n,t[5]=r,t[6]=0,t[7]=0,t[8]=0,t[9]=0,t[10]=1,t[11]=0,t[12]=0,t[13]=0,t[14]=0,t[15]=1,t},frustum:function(t,e,n,r,o,a,s){const i=1/(n-e),u=1/(o-r),c=1/(a-s);return t[0]=2*a*i,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=2*a*u,t[6]=0,t[7]=0,t[8]=(n+e)*i,t[9]=(o+r)*u,t[10]=(s+a)*c,t[11]=-1,t[12]=0,t[13]=0,t[14]=s*a*2*c,t[15]=0,t},getRotation:function(t,e){const n=e[0]+e[5]+e[10];let r=0;return n>0?(r=2*Math.sqrt(n+1),t[3]=.25*r,t[0]=(e[6]-e[9])/r,t[1]=(e[8]-e[2])/r,t[2]=(e[1]-e[4])/r):e[0]>e[5]&&e[0]>e[10]?(r=2*Math.sqrt(1+e[0]-e[5]-e[10]),t[3]=(e[6]-e[9])/r,t[0]=.25*r,t[1]=(e[1]+e[4])/r,t[2]=(e[8]+e[2])/r):e[5]>e[10]?(r=2*Math.sqrt(1+e[5]-e[0]-e[10]),t[3]=(e[8]-e[2])/r,t[0]=(e[1]+e[4])/r,t[1]=.25*r,t[2]=(e[6]+e[9])/r):(r=2*Math.sqrt(1+e[10]-e[0]-e[5]),t[3]=(e[1]-e[4])/r,t[0]=(e[8]+e[2])/r,t[1]=(e[6]+e[9])/r,t[2]=.25*r),t},getScaling:function(t,e){const n=e[0],r=e[1],o=e[2],a=e[4],s=e[5],i=e[6],u=e[8],c=e[9],f=e[10];return t[0]=Math.sqrt(n*n+r*r+o*o),t[1]=Math.sqrt(a*a+s*s+i*i),t[2]=Math.sqrt(u*u+c*c+f*f),t},getTranslation:function(t,e){return t[0]=e[12],t[1]=e[13],t[2]=e[14],t},hasIdentityRotation:T,identity:s,invert:u,invertOrIdentity:function(t,e){return u(t,e)||s(t),t},isOrthoNormal:E,lookAt:function(t,e,n,r){let a,i,u,c,f,l,h,d,m,M;const b=e[0],p=e[1],g=e[2],y=r[0],w=r[1],_=r[2],E=n[0],T=n[1],S=n[2],P=(0,o.g)();return Math.abs(b-E)<P&&Math.abs(p-T)<P&&Math.abs(g-S)<P?s(t):(h=b-E,d=p-T,m=g-S,M=1/Math.sqrt(h*h+d*d+m*m),h*=M,d*=M,m*=M,a=w*m-_*d,i=_*h-y*m,u=y*d-w*h,M=Math.sqrt(a*a+i*i+u*u),M?(M=1/M,a*=M,i*=M,u*=M):(a=0,i=0,u=0),c=d*u-m*i,f=m*a-h*u,l=h*i-d*a,M=Math.sqrt(c*c+f*f+l*l),M?(M=1/M,c*=M,f*=M,l*=M):(c=0,f=0,l=0),t[0]=a,t[1]=c,t[2]=h,t[3]=0,t[4]=i,t[5]=f,t[6]=d,t[7]=0,t[8]=u,t[9]=l,t[10]=m,t[11]=0,t[12]=-(a*b+i*p+u*g),t[13]=-(c*b+f*p+l*g),t[14]=-(h*b+d*p+m*g),t[15]=1,t)},mul:S,multiply:c,multiplyScalar:function(t,e,n){return t[0]=e[0]*n,t[1]=e[1]*n,t[2]=e[2]*n,t[3]=e[3]*n,t[4]=e[4]*n,t[5]=e[5]*n,t[6]=e[6]*n,t[7]=e[7]*n,t[8]=e[8]*n,t[9]=e[9]*n,t[10]=e[10]*n,t[11]=e[11]*n,t[12]=e[12]*n,t[13]=e[13]*n,t[14]=e[14]*n,t[15]=e[15]*n,t},multiplyScalarAndAdd:function(t,e,n,r){return t[0]=e[0]+n[0]*r,t[1]=e[1]+n[1]*r,t[2]=e[2]+n[2]*r,t[3]=e[3]+n[3]*r,t[4]=e[4]+n[4]*r,t[5]=e[5]+n[5]*r,t[6]=e[6]+n[6]*r,t[7]=e[7]+n[7]*r,t[8]=e[8]+n[8]*r,t[9]=e[9]+n[9]*r,t[10]=e[10]+n[10]*r,t[11]=e[11]+n[11]*r,t[12]=e[12]+n[12]*r,t[13]=e[13]+n[13]*r,t[14]=e[14]+n[14]*r,t[15]=e[15]+n[15]*r,t},ortho:function(t,e,n,r,o,a,s){const i=1/(e-n),u=1/(r-o),c=1/(a-s);return t[0]=-2*i,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=-2*u,t[6]=0,t[7]=0,t[8]=0,t[9]=0,t[10]=2*c,t[11]=0,t[12]=(e+n)*i,t[13]=(o+r)*u,t[14]=(s+a)*c,t[15]=1,t},perspective:function(t,e,n,r,o){const a=1/Math.tan(e/2);let s;return t[0]=a/n,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=a,t[6]=0,t[7]=0,t[8]=0,t[9]=0,t[11]=-1,t[12]=0,t[13]=0,t[15]=0,null!=o&&o!==1/0?(s=1/(r-o),t[10]=(o+r)*s,t[14]=2*o*r*s):(t[10]=-1,t[14]=-2*r),t},perspectiveFromFieldOfView:function(t,e,n,r){const o=Math.tan(e.upDegrees*Math.PI/180),a=Math.tan(e.downDegrees*Math.PI/180),s=Math.tan(e.leftDegrees*Math.PI/180),i=Math.tan(e.rightDegrees*Math.PI/180),u=2/(s+i),c=2/(o+a);return t[0]=u,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=c,t[6]=0,t[7]=0,t[8]=-(s-i)*u*.5,t[9]=(o-a)*c*.5,t[10]=r/(n-r),t[11]=-1,t[12]=0,t[13]=0,t[14]=r*n/(n-r),t[15]=0,t},rotate:h,rotateX:function(t,e,n){const r=Math.sin(n),o=Math.cos(n),a=e[4],s=e[5],i=e[6],u=e[7],c=e[8],f=e[9],l=e[10],h=e[11];return e!==t&&(t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[12]=e[12],t[13]=e[13],t[14]=e[14],t[15]=e[15]),t[4]=a*o+c*r,t[5]=s*o+f*r,t[6]=i*o+l*r,t[7]=u*o+h*r,t[8]=c*o-a*r,t[9]=f*o-s*r,t[10]=l*o-i*r,t[11]=h*o-u*r,t},rotateY:function(t,e,n){const r=Math.sin(n),o=Math.cos(n),a=e[0],s=e[1],i=e[2],u=e[3],c=e[8],f=e[9],l=e[10],h=e[11];return e!==t&&(t[4]=e[4],t[5]=e[5],t[6]=e[6],t[7]=e[7],t[12]=e[12],t[13]=e[13],t[14]=e[14],t[15]=e[15]),t[0]=a*o-c*r,t[1]=s*o-f*r,t[2]=i*o-l*r,t[3]=u*o-h*r,t[8]=a*r+c*o,t[9]=s*r+f*o,t[10]=i*r+l*o,t[11]=u*r+h*o,t},rotateZ:function(t,e,n){const r=Math.sin(n),o=Math.cos(n),a=e[0],s=e[1],i=e[2],u=e[3],c=e[4],f=e[5],l=e[6],h=e[7];return e!==t&&(t[8]=e[8],t[9]=e[9],t[10]=e[10],t[11]=e[11],t[12]=e[12],t[13]=e[13],t[14]=e[14],t[15]=e[15]),t[0]=a*o+c*r,t[1]=s*o+f*r,t[2]=i*o+l*r,t[3]=u*o+h*r,t[4]=c*o-a*r,t[5]=f*o-s*r,t[6]=l*o-i*r,t[7]=h*o-u*r,t},scale:l,set:a,str:function(t){return"mat4("+t[0]+", "+t[1]+", "+t[2]+", "+t[3]+", "+t[4]+", "+t[5]+", "+t[6]+", "+t[7]+", "+t[8]+", "+t[9]+", "+t[10]+", "+t[11]+", "+t[12]+", "+t[13]+", "+t[14]+", "+t[15]+")"},sub:P,subtract:y,targetTo:function(t,e,n,r){const o=e[0],a=e[1],s=e[2],i=r[0],u=r[1],c=r[2];let f=o-n[0],l=a-n[1],h=s-n[2],d=f*f+l*l+h*h;d>0&&(d=1/Math.sqrt(d),f*=d,l*=d,h*=d);let m=u*h-c*l,M=c*f-i*h,b=i*l-u*f;return d=m*m+M*M+b*b,d>0&&(d=1/Math.sqrt(d),m*=d,M*=d,b*=d),t[0]=m,t[1]=M,t[2]=b,t[3]=0,t[4]=l*b-h*M,t[5]=h*m-f*b,t[6]=f*M-l*m,t[7]=0,t[8]=f,t[9]=l,t[10]=h,t[11]=0,t[12]=o,t[13]=a,t[14]=s,t[15]=1,t},translate:f,transpose:i},Symbol.toStringTag,{value:"Module"}))},13598:(t,e,n)=>{function r(){return[1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1]}function o(t){return[t[0],t[1],t[2],t[3],t[4],t[5],t[6],t[7],t[8],t[9],t[10],t[11],t[12],t[13],t[14],t[15]]}function a(t,e){return new Float64Array(t,e,16)}n.d(e,{I:()=>s,a:()=>a,b:()=>o,c:()=>r});const s=[1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1];Object.freeze(Object.defineProperty({__proto__:null,IDENTITY:s,clone:o,create:r,createView:a,fromValues:function(t,e,n,r,o,a,s,i,u,c,f,l,h,d,m,M){return[t,e,n,r,o,a,s,i,u,c,f,l,h,d,m,M]}},Symbol.toStringTag,{value:"Module"}))},94961:(t,e,n)=>{function r(){return[0,0,0,1]}function o(t){return[t[0],t[1],t[2],t[3]]}function a(t,e){return new Float64Array(t,e,4)}n.d(e,{I:()=>s,a:()=>r,b:()=>o,c:()=>a});const s=[0,0,0,1];Object.freeze(Object.defineProperty({__proto__:null,IDENTITY:s,clone:o,create:r,createView:a,fromValues:function(t,e,n,r){return[t,e,n,r]}},Symbol.toStringTag,{value:"Module"}))},29268:(t,e,n)=>{n.d(e,{a:()=>g,c:()=>b,g:()=>y,h:()=>p,j:()=>E,m:()=>x}),n(80442);var r,o,a=n(92604),s=n(22021),i=n(70586),u=n(52138),c=n(17896),f=n(65617),l=n(98766),h=n(88669);(o=r||(r={}))[o.X=0]="X",o[o.Y=1]="Y",o[o.Z=2]="Z";var d=n(78341),m=n(61277),M=n(12981);function b(){return(0,h.c)()}function p(t,e=b()){return(0,l.c)(e,t)}function g(t){return t[3]}function y(t){return t}function w(t,e,n){if((0,i.Wi)(e))return!1;const{origin:r,direction:o}=e,a=_;a[0]=r[0]-t[0],a[1]=r[1]-t[1],a[2]=r[2]-t[2];const s=o[0]*o[0]+o[1]*o[1]+o[2]*o[2];if(0===s)return!1;const u=2*(o[0]*a[0]+o[1]*a[1]+o[2]*a[2]),c=u*u-4*s*(a[0]*a[0]+a[1]*a[1]+a[2]*a[2]-t[3]*t[3]);if(c<0)return!1;const f=Math.sqrt(c);let l=(-u-f)/(2*s);const h=(-u+f)/(2*s);return(l<0||h<l&&h>0)&&(l=h),!(l<0||(n&&(n[0]=r[0]+o[0]*l,n[1]=r[1]+o[1]*l,n[2]=r[2]+o[2]*l),0))}const _=(0,f.c)();function E(t,e){return w(t,e,null)}function T(t,e,n){const r=M.WM.get(),o=M.MP.get();(0,c.f)(r,e.origin,e.direction);const a=g(t);(0,c.f)(n,r,e.origin),(0,c.g)(n,n,1/(0,c.l)(n)*a);const s=P(t,e.origin),i=(0,m.EU)(e.origin,n);return(0,u.d)(o,i+s,r),(0,c.m)(n,n,o),n}function S(t,e,n){const r=(0,c.b)(M.WM.get(),e,t),o=(0,c.g)(M.WM.get(),r,t[3]/(0,c.l)(r));return(0,c.a)(n,o,t)}function P(t,e){const n=(0,c.b)(M.WM.get(),e,t),r=(0,c.l)(n),o=g(t),a=o+Math.abs(o-r);return(0,s.ZF)(o/a)}const v=(0,f.c)();function O(t,e,n,o){const a=(0,c.b)(v,e,t);switch(n){case r.X:{const t=(0,s.jE)(a,v)[2];return(0,c.s)(o,-Math.sin(t),Math.cos(t),0)}case r.Y:{const t=(0,s.jE)(a,v),e=t[1],n=t[2],r=Math.sin(e);return(0,c.s)(o,-r*Math.cos(n),-r*Math.sin(n),Math.cos(e))}case r.Z:return(0,c.n)(o,a);default:return}}function q(t,e){const n=(0,c.b)(I,e,t);return(0,c.l)(n)-t[3]}function x(t,e){const n=(0,c.d)(t,e),r=g(t);return n<=r*r}const I=(0,f.c)(),A=b();Object.freeze(Object.defineProperty({__proto__:null,altitudeAt:q,angleToSilhouette:P,axisAt:O,clear:function(t){t[0]=t[1]=t[2]=t[3]=0},closestPoint:function(t,e,n){return w(t,e,n)?n:((0,d.JI)(e,t,n),S(t,n,n))},closestPointOnSilhouette:T,containsPoint:x,copy:p,create:b,distanceToSilhouette:function(t,e){const n=(0,c.b)(M.WM.get(),e,t),r=(0,c.p)(n),o=t[3]*t[3];return Math.sqrt(Math.abs(r-o))},elevate:function(t,e,n){return t!==n&&(0,c.c)(n,t),n[3]=t[3]+e,n},fromCenterAndRadius:function(t,e){return(0,h.f)(t[0],t[1],t[2],e)},fromRadius:function(t,e){return t[0]=t[1]=t[2]=0,t[3]=e,t},fromValues:function(t,e,n,r){return(0,h.f)(t,e,n,r)},getCenter:y,getRadius:g,intersectRay:w,intersectRayClosestSilhouette:function(t,e,n){if(w(t,e,n))return n;const r=T(t,e,M.WM.get());return(0,c.a)(n,e.origin,(0,c.g)(M.WM.get(),e.direction,(0,c.i)(e.origin,r)/(0,c.l)(e.direction))),n},intersectsRay:E,projectPoint:S,setAltitudeAt:function(t,e,n,o){const a=q(t,e),s=O(t,e,r.Z,I),i=(0,c.g)(I,s,n-a);return(0,c.a)(o,e,i)},setExtent:function(t,e,n){return a.Z.getLogger("esri.geometry.support.sphere").error("sphere.setExtent is not yet supported"),t===n?n:p(t,n)},tmpSphere:A,wrap:function(t){return t}},Symbol.toStringTag,{value:"Module"}))},97323:(t,e,n)=>{function r(){return[0,0]}function o(t,e){return[t,e]}function a(t,e){return new Float64Array(t,e,2)}function s(){return o(1,1)}function i(){return o(1,0)}function u(){return o(0,1)}n.d(e,{Z:()=>c,a:()=>r,c:()=>a,f:()=>o});const c=[0,0],f=s(),l=i(),h=u();Object.freeze(Object.defineProperty({__proto__:null,ONES:f,UNIT_X:l,UNIT_Y:h,ZEROS:c,clone:function(t){return[t[0],t[1]]},create:r,createView:a,fromArray:function(t){const e=[0,0],n=Math.min(2,t.length);for(let r=0;r<n;++r)e[r]=t[r];return e},fromValues:o,ones:s,unitX:i,unitY:u,zeros:function(){return[0,0]}},Symbol.toStringTag,{value:"Module"}))},17896:(t,e,n)=>{n.d(e,{B:()=>f,C:()=>l,a:()=>u,b:()=>c,c:()=>s,d:()=>M,e:()=>g,f:()=>y,g:()=>h,h:()=>w,i:()=>m,j:()=>A,k:()=>v,l:()=>a,m:()=>_,n:()=>p,p:()=>b,q:()=>T,r:()=>O,s:()=>i,t:()=>E,u:()=>L,z:()=>d});var r=n(65617),o=n(46851);function a(t){const e=t[0],n=t[1],r=t[2];return Math.sqrt(e*e+n*n+r*r)}function s(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t}function i(t,e,n,r){return t[0]=e,t[1]=n,t[2]=r,t}function u(t,e,n){return t[0]=e[0]+n[0],t[1]=e[1]+n[1],t[2]=e[2]+n[2],t}function c(t,e,n){return t[0]=e[0]-n[0],t[1]=e[1]-n[1],t[2]=e[2]-n[2],t}function f(t,e,n){return t[0]=e[0]*n[0],t[1]=e[1]*n[1],t[2]=e[2]*n[2],t}function l(t,e,n){return t[0]=e[0]/n[0],t[1]=e[1]/n[1],t[2]=e[2]/n[2],t}function h(t,e,n){return t[0]=e[0]*n,t[1]=e[1]*n,t[2]=e[2]*n,t}function d(t,e,n,r){return t[0]=e[0]+n[0]*r,t[1]=e[1]+n[1]*r,t[2]=e[2]+n[2]*r,t}function m(t,e){const n=e[0]-t[0],r=e[1]-t[1],o=e[2]-t[2];return Math.sqrt(n*n+r*r+o*o)}function M(t,e){const n=e[0]-t[0],r=e[1]-t[1],o=e[2]-t[2];return n*n+r*r+o*o}function b(t){const e=t[0],n=t[1],r=t[2];return e*e+n*n+r*r}function p(t,e){const n=e[0],r=e[1],o=e[2];let a=n*n+r*r+o*o;return a>0&&(a=1/Math.sqrt(a),t[0]=e[0]*a,t[1]=e[1]*a,t[2]=e[2]*a),t}function g(t,e){return t[0]*e[0]+t[1]*e[1]+t[2]*e[2]}function y(t,e,n){const r=e[0],o=e[1],a=e[2],s=n[0],i=n[1],u=n[2];return t[0]=o*u-a*i,t[1]=a*s-r*u,t[2]=r*i-o*s,t}function w(t,e,n,r){const o=e[0],a=e[1],s=e[2];return t[0]=o+r*(n[0]-o),t[1]=a+r*(n[1]-a),t[2]=s+r*(n[2]-s),t}function _(t,e,n){const r=e[0],o=e[1],a=e[2];return t[0]=n[0]*r+n[4]*o+n[8]*a+n[12],t[1]=n[1]*r+n[5]*o+n[9]*a+n[13],t[2]=n[2]*r+n[6]*o+n[10]*a+n[14],t}function E(t,e,n){const r=e[0],o=e[1],a=e[2];return t[0]=r*n[0]+o*n[3]+a*n[6],t[1]=r*n[1]+o*n[4]+a*n[7],t[2]=r*n[2]+o*n[5]+a*n[8],t}function T(t,e,n){const r=n[0],o=n[1],a=n[2],s=n[3],i=e[0],u=e[1],c=e[2];let f=o*c-a*u,l=a*i-r*c,h=r*u-o*i,d=o*h-a*l,m=a*f-r*h,M=r*l-o*f;const b=2*s;return f*=b,l*=b,h*=b,d*=2,m*=2,M*=2,t[0]=i+f+d,t[1]=u+l+m,t[2]=c+h+M,t}const S=(0,r.c)(),P=(0,r.c)();function v(t,e){return t[0]===e[0]&&t[1]===e[1]&&t[2]===e[2]}function O(t,e,n){const r=n[0]-e[0],o=n[1]-e[1],a=n[2]-e[2];let s=r*r+o*o+a*a;return s>0?(s=1/Math.sqrt(s),t[0]=r*s,t[1]=o*s,t[2]=a*s,t):(t[0]=0,t[1]=0,t[2]=0,t)}const q=c,x=f,I=l,A=m,C=M,L=a,k=b;Object.freeze(Object.defineProperty({__proto__:null,abs:function(t,e){return t[0]=Math.abs(e[0]),t[1]=Math.abs(e[1]),t[2]=Math.abs(e[2]),t},add:u,angle:function(t,e){s(S,t),s(P,e),p(S,S),p(P,P);const n=g(S,P);return n>1?0:n<-1?Math.PI:Math.acos(n)},bezier:function(t,e,n,r,o,a){const s=1-a,i=s*s,u=a*a,c=i*s,f=3*a*i,l=3*u*s,h=u*a;return t[0]=e[0]*c+n[0]*f+r[0]*l+o[0]*h,t[1]=e[1]*c+n[1]*f+r[1]*l+o[1]*h,t[2]=e[2]*c+n[2]*f+r[2]*l+o[2]*h,t},ceil:function(t,e){return t[0]=Math.ceil(e[0]),t[1]=Math.ceil(e[1]),t[2]=Math.ceil(e[2]),t},copy:s,cross:y,direction:O,dist:A,distance:m,div:I,divide:l,dot:g,equals:function(t,e){if(t===e)return!0;const n=t[0],r=t[1],a=t[2],s=e[0],i=e[1],u=e[2],c=(0,o.g)();return Math.abs(n-s)<=c*Math.max(1,Math.abs(n),Math.abs(s))&&Math.abs(r-i)<=c*Math.max(1,Math.abs(r),Math.abs(i))&&Math.abs(a-u)<=c*Math.max(1,Math.abs(a),Math.abs(u))},exactEquals:v,floor:function(t,e){return t[0]=Math.floor(e[0]),t[1]=Math.floor(e[1]),t[2]=Math.floor(e[2]),t},hermite:function(t,e,n,r,o,a){const s=a*a,i=s*(2*a-3)+1,u=s*(a-2)+a,c=s*(a-1),f=s*(3-2*a);return t[0]=e[0]*i+n[0]*u+r[0]*c+o[0]*f,t[1]=e[1]*i+n[1]*u+r[1]*c+o[1]*f,t[2]=e[2]*i+n[2]*u+r[2]*c+o[2]*f,t},inverse:function(t,e){return t[0]=1/e[0],t[1]=1/e[1],t[2]=1/e[2],t},len:L,length:a,lerp:w,max:function(t,e,n){return t[0]=Math.max(e[0],n[0]),t[1]=Math.max(e[1],n[1]),t[2]=Math.max(e[2],n[2]),t},min:function(t,e,n){return t[0]=Math.min(e[0],n[0]),t[1]=Math.min(e[1],n[1]),t[2]=Math.min(e[2],n[2]),t},mul:x,multiply:f,negate:function(t,e){return t[0]=-e[0],t[1]=-e[1],t[2]=-e[2],t},normalize:p,random:function(t,e){e=e||1;const n=o.R,r=2*n()*Math.PI,a=2*n()-1,s=Math.sqrt(1-a*a)*e;return t[0]=Math.cos(r)*s,t[1]=Math.sin(r)*s,t[2]=a*e,t},rotateX:function(t,e,n,r){const o=[],a=[];return o[0]=e[0]-n[0],o[1]=e[1]-n[1],o[2]=e[2]-n[2],a[0]=o[0],a[1]=o[1]*Math.cos(r)-o[2]*Math.sin(r),a[2]=o[1]*Math.sin(r)+o[2]*Math.cos(r),t[0]=a[0]+n[0],t[1]=a[1]+n[1],t[2]=a[2]+n[2],t},rotateY:function(t,e,n,r){const o=[],a=[];return o[0]=e[0]-n[0],o[1]=e[1]-n[1],o[2]=e[2]-n[2],a[0]=o[2]*Math.sin(r)+o[0]*Math.cos(r),a[1]=o[1],a[2]=o[2]*Math.cos(r)-o[0]*Math.sin(r),t[0]=a[0]+n[0],t[1]=a[1]+n[1],t[2]=a[2]+n[2],t},rotateZ:function(t,e,n,r){const o=[],a=[];return o[0]=e[0]-n[0],o[1]=e[1]-n[1],o[2]=e[2]-n[2],a[0]=o[0]*Math.cos(r)-o[1]*Math.sin(r),a[1]=o[0]*Math.sin(r)+o[1]*Math.cos(r),a[2]=o[2],t[0]=a[0]+n[0],t[1]=a[1]+n[1],t[2]=a[2]+n[2],t},round:function(t,e){return t[0]=Math.round(e[0]),t[1]=Math.round(e[1]),t[2]=Math.round(e[2]),t},scale:h,scaleAndAdd:d,set:i,sign:function(t,e){return t[0]=Math.sign(e[0]),t[1]=Math.sign(e[1]),t[2]=Math.sign(e[2]),t},sqrDist:C,sqrLen:k,squaredDistance:M,squaredLength:b,str:function(t){return"vec3("+t[0]+", "+t[1]+", "+t[2]+")"},sub:q,subtract:c,transformMat3:E,transformMat4:_,transformQuat:T},Symbol.toStringTag,{value:"Module"}))},65617:(t,e,n)=>{function r(){return[0,0,0]}function o(t){return[t[0],t[1],t[2]]}function a(t,e,n){return[t,e,n]}function s(t){const e=[0,0,0],n=Math.min(3,t.length);for(let r=0;r<n;++r)e[r]=t[r];return e}function i(t,e){return new Float64Array(t,e,3)}function u(){return a(1,1,1)}function c(){return a(1,0,0)}function f(){return a(0,1,0)}function l(){return a(0,0,1)}n.d(e,{O:()=>d,Z:()=>h,a:()=>o,b:()=>i,c:()=>r,d:()=>s,f:()=>a});const h=[0,0,0],d=u(),m=c(),M=f(),b=l();Object.freeze(Object.defineProperty({__proto__:null,ONES:d,UNIT_X:m,UNIT_Y:M,UNIT_Z:b,ZEROS:h,clone:o,create:r,createView:i,fromArray:s,fromValues:a,ones:u,unitX:c,unitY:f,unitZ:l,zeros:function(){return[0,0,0]}},Symbol.toStringTag,{value:"Module"}))},98766:(t,e,n)=>{n.d(e,{a:()=>s,b:()=>f,c:()=>o,d:()=>b,e:()=>d,f:()=>m,g:()=>g,h:()=>y,l:()=>p,n:()=>M,s:()=>a});var r=n(46851);function o(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t}function a(t,e,n,r,o){return t[0]=e,t[1]=n,t[2]=r,t[3]=o,t}function s(t,e,n){return t[0]=e[0]+n[0],t[1]=e[1]+n[1],t[2]=e[2]+n[2],t[3]=e[3]+n[3],t}function i(t,e,n){return t[0]=e[0]-n[0],t[1]=e[1]-n[1],t[2]=e[2]-n[2],t[3]=e[3]-n[3],t}function u(t,e,n){return t[0]=e[0]*n[0],t[1]=e[1]*n[1],t[2]=e[2]*n[2],t[3]=e[3]*n[3],t}function c(t,e,n){return t[0]=e[0]/n[0],t[1]=e[1]/n[1],t[2]=e[2]/n[2],t[3]=e[3]/n[3],t}function f(t,e,n){return t[0]=e[0]*n,t[1]=e[1]*n,t[2]=e[2]*n,t[3]=e[3]*n,t}function l(t,e){const n=e[0]-t[0],r=e[1]-t[1],o=e[2]-t[2],a=e[3]-t[3];return Math.sqrt(n*n+r*r+o*o+a*a)}function h(t,e){const n=e[0]-t[0],r=e[1]-t[1],o=e[2]-t[2],a=e[3]-t[3];return n*n+r*r+o*o+a*a}function d(t){const e=t[0],n=t[1],r=t[2],o=t[3];return Math.sqrt(e*e+n*n+r*r+o*o)}function m(t){const e=t[0],n=t[1],r=t[2],o=t[3];return e*e+n*n+r*r+o*o}function M(t,e){const n=e[0],r=e[1],o=e[2],a=e[3];let s=n*n+r*r+o*o+a*a;return s>0&&(s=1/Math.sqrt(s),t[0]=n*s,t[1]=r*s,t[2]=o*s,t[3]=a*s),t}function b(t,e){return t[0]*e[0]+t[1]*e[1]+t[2]*e[2]+t[3]*e[3]}function p(t,e,n,r){const o=e[0],a=e[1],s=e[2],i=e[3];return t[0]=o+r*(n[0]-o),t[1]=a+r*(n[1]-a),t[2]=s+r*(n[2]-s),t[3]=i+r*(n[3]-i),t}function g(t,e){return t[0]===e[0]&&t[1]===e[1]&&t[2]===e[2]&&t[3]===e[3]}function y(t,e){const n=t[0],o=t[1],a=t[2],s=t[3],i=e[0],u=e[1],c=e[2],f=e[3],l=(0,r.g)();return Math.abs(n-i)<=l*Math.max(1,Math.abs(n),Math.abs(i))&&Math.abs(o-u)<=l*Math.max(1,Math.abs(o),Math.abs(u))&&Math.abs(a-c)<=l*Math.max(1,Math.abs(a),Math.abs(c))&&Math.abs(s-f)<=l*Math.max(1,Math.abs(s),Math.abs(f))}const w=i,_=u,E=c,T=l,S=h,P=d,v=m;Object.freeze(Object.defineProperty({__proto__:null,add:s,ceil:function(t,e){return t[0]=Math.ceil(e[0]),t[1]=Math.ceil(e[1]),t[2]=Math.ceil(e[2]),t[3]=Math.ceil(e[3]),t},copy:o,dist:T,distance:l,div:E,divide:c,dot:b,equals:y,exactEquals:g,floor:function(t,e){return t[0]=Math.floor(e[0]),t[1]=Math.floor(e[1]),t[2]=Math.floor(e[2]),t[3]=Math.floor(e[3]),t},inverse:function(t,e){return t[0]=1/e[0],t[1]=1/e[1],t[2]=1/e[2],t[3]=1/e[3],t},len:P,length:d,lerp:p,max:function(t,e,n){return t[0]=Math.max(e[0],n[0]),t[1]=Math.max(e[1],n[1]),t[2]=Math.max(e[2],n[2]),t[3]=Math.max(e[3],n[3]),t},min:function(t,e,n){return t[0]=Math.min(e[0],n[0]),t[1]=Math.min(e[1],n[1]),t[2]=Math.min(e[2],n[2]),t[3]=Math.min(e[3],n[3]),t},mul:_,multiply:u,negate:function(t,e){return t[0]=-e[0],t[1]=-e[1],t[2]=-e[2],t[3]=-e[3],t},normalize:M,random:function(t,e){const n=r.R;let o,a,s,i,u,c;e=e||1;do{o=2*n()-1,a=2*n()-1,u=o*o+a*a}while(u>=1);do{s=2*n()-1,i=2*n()-1,c=s*s+i*i}while(c>=1);const f=Math.sqrt((1-u)/c);return t[0]=e*o,t[1]=e*a,t[2]=e*s*f,t[3]=e*i*f,t},round:function(t,e){return t[0]=Math.round(e[0]),t[1]=Math.round(e[1]),t[2]=Math.round(e[2]),t[3]=Math.round(e[3]),t},scale:f,scaleAndAdd:function(t,e,n,r){return t[0]=e[0]+n[0]*r,t[1]=e[1]+n[1]*r,t[2]=e[2]+n[2]*r,t[3]=e[3]+n[3]*r,t},set:a,sqrDist:S,sqrLen:v,squaredDistance:h,squaredLength:m,str:function(t){return"vec4("+t[0]+", "+t[1]+", "+t[2]+", "+t[3]+")"},sub:w,subtract:i,transformMat4:function(t,e,n){const r=e[0],o=e[1],a=e[2],s=e[3];return t[0]=n[0]*r+n[4]*o+n[8]*a+n[12]*s,t[1]=n[1]*r+n[5]*o+n[9]*a+n[13]*s,t[2]=n[2]*r+n[6]*o+n[10]*a+n[14]*s,t[3]=n[3]*r+n[7]*o+n[11]*a+n[15]*s,t},transformQuat:function(t,e,n){const r=e[0],o=e[1],a=e[2],s=n[0],i=n[1],u=n[2],c=n[3],f=c*r+i*a-u*o,l=c*o+u*r-s*a,h=c*a+s*o-i*r,d=-s*r-i*o-u*a;return t[0]=f*c+d*-s+l*-u-h*-i,t[1]=l*c+d*-i+h*-s-f*-u,t[2]=h*c+d*-u+f*-i-l*-s,t[3]=e[3],t}},Symbol.toStringTag,{value:"Module"}))},88669:(t,e,n)=>{function r(){return[0,0,0,0]}function o(t,e,n,r){return[t,e,n,r]}function a(t,e){return new Float64Array(t,e,4)}function s(){return o(1,1,1,1)}function i(){return o(1,0,0,0)}function u(){return o(0,1,0,0)}function c(){return o(0,0,1,0)}function f(){return o(0,0,0,1)}n.d(e,{a:()=>a,c:()=>r,f:()=>o});const l=s(),h=i(),d=u(),m=c(),M=f();Object.freeze(Object.defineProperty({__proto__:null,ONES:l,UNIT_W:M,UNIT_X:h,UNIT_Y:d,UNIT_Z:m,ZEROS:[0,0,0,0],clone:function(t){return[t[0],t[1],t[2],t[3]]},create:r,createView:a,fromArray:function(t){const e=[0,0,0,0],n=Math.min(4,t.length);for(let r=0;r<n;++r)e[r]=t[r];return e},fromValues:o,ones:s,unitW:f,unitX:i,unitY:u,unitZ:c,zeros:function(){return[0,0,0,0]}},Symbol.toStringTag,{value:"Module"}))},22807:(t,e,n)=>{n.d(e,{x:()=>o});var r=n(41213);class o{constructor(t){this._allocator=t,this._items=[],this._itemsPtr=0,this._grow()}get(){return 0===this._itemsPtr&&(0,r.Y)((()=>this._reset())),this._itemsPtr===this._items.length&&this._grow(),this._items[this._itemsPtr++]}_reset(){const t=Math.min(3*Math.max(8,this._itemsPtr),this._itemsPtr+3*a);this._items.length=Math.min(t,this._items.length),this._itemsPtr=0}_grow(){for(let t=0;t<Math.max(8,Math.min(this._items.length,a));t++)this._items.push(this._allocator())}}const a=1024},43090:(t,e,n)=>{function r(t){return 32+t.length}function o(t){if(!t)return 0;let e=u;for(const n in t)if(t.hasOwnProperty(n)){const o=t[n];switch(typeof o){case"string":e+=r(o);break;case"number":e+=16;break;case"boolean":e+=4}}return e}function a(t){if(!t)return 0;if(Array.isArray(t))return function(t){const e=t.length;if(0===e||"number"==typeof t[0])return 32+8*e;let n=c;for(let r=0;r<e;r++)n+=s(t[r]);return n}(t);let e=u;for(const n in t)t.hasOwnProperty(n)&&(e+=s(t[n]));return e}function s(t){switch(typeof t){case"object":return a(t);case"string":return r(t);case"number":return 16;case"boolean":return 4;default:return 8}}function i(t,e){return c+t.length*e}n.d(e,{Ul:()=>a,Y8:()=>f,do:()=>i,f2:()=>o});const u=32,c=32;var f;!function(t){t[t.KILOBYTES=1024]="KILOBYTES",t[t.MEGABYTES=1048576]="MEGABYTES",t[t.GIGABYTES=1073741824]="GIGABYTES"}(f||(f={}))},22021:(t,e,n)=>{n.d(e,{BV:()=>f,Kt:()=>h,Sf:()=>a,Vl:()=>c,ZF:()=>l,_3:()=>w,jE:()=>g,oK:()=>b,oc:()=>y,t7:()=>u,uZ:()=>s,wt:()=>i});var r=n(17896);n(98766);const o=new Float32Array(1);function a(t){--t;for(let e=1;e<32;e<<=1)t|=t>>e;return t+1}function s(t,e,n){return Math.min(Math.max(t,e),n)}function i(t){return 0==(t&t-1)}function u(t,e,n){return t+(e-t)*n}function c(t){return t*Math.PI/180}function f(t){return 180*t/Math.PI}function l(t){return Math.acos(s(t,-1,1))}function h(t){return Math.asin(s(t,-1,1))}function d(t,e,n=1e-6){return t===e||!(!Number.isFinite(t)||!Number.isFinite(e))&&(t>e?t-e:e-t)<=n}const m=new DataView(new ArrayBuffer(Float64Array.BYTES_PER_ELEMENT));const M=BigInt("1000000");function b(t){return p(Math.max(-w,Math.min(t,w)))}function p(t){return o[0]=t,o[0]}function g(t,e){const n=(0,r.l)(t),o=h(t[2]/n),a=Math.atan2(t[1]/n,t[0]/n);return(0,r.s)(e,n,o,a),e}function y(t){const e=t[0]*t[0]+t[1]*t[1]+t[2]*t[2],n=t[3]*t[3]+t[4]*t[4]+t[5]*t[5],r=t[6]*t[6]+t[7]*t[7]+t[8]*t[8];return!(d(e,1)&&d(n,1)&&d(r,1))}!function(t){const e=function(t){return m.setFloat64(0,t),m.getBigInt64(0)}(t=Math.abs(t)),n=function(t){return m.setBigInt64(0,t),m.getFloat64(0)}(e<=M?M:e-M);Math.abs(t-n)}(1);const w=p(34028234663852886e22)},41213:(t,e,n)=>{n.d(e,{Y:()=>o});const r=[];function o(t){r.push(t),1===r.length&&queueMicrotask((()=>{const t=r.slice();r.length=0;for(const e of t)e()}))}},78341:(t,e,n)=>{n.d(e,{JI:()=>c,Ue:()=>s,re:()=>u}),n(67676);var r=n(22807),o=n(17896),a=n(65617);function s(t){return t?i((0,a.a)(t.origin),(0,a.a)(t.direction)):i((0,a.c)(),(0,a.c)())}function i(t,e){return{origin:t,direction:e}}function u(t,e){const n=f.get();return n.origin=t,n.direction=e,n}function c(t,e,n){const r=(0,o.e)(t.direction,(0,o.b)(n,e,t.origin));return(0,o.a)(n,t.origin,(0,o.g)(n,t.direction,r)),n}n(12981);const f=new r.x((()=>s()))},61277:(t,e,n)=>{n.d(e,{EU:()=>s});var r=n(22021),o=n(17896),a=n(65617);function s(t,e){const n=(0,o.e)(t,e)/((0,o.l)(t)*(0,o.l)(e));return-(0,r.ZF)(n)}(0,a.c)(),(0,a.c)()},12981:(t,e,n)=>{n.d(e,{MP:()=>m,WM:()=>d});var r=n(43090),o=n(41213),a=n(46521),s=n(13598),i=n(94961),u=n(97323),c=n(65617),f=n(88669);class l{constructor(t,e,n){this._itemByteSize=t,this._itemCreate=e,this._buffers=new Array,this._items=new Array,this._itemsPtr=0,this._itemsPerBuffer=Math.ceil(n/this._itemByteSize)}get(){0===this._itemsPtr&&(0,o.Y)((()=>this._reset()));const t=Math.floor(this._itemsPtr/this._itemsPerBuffer);for(;this._buffers.length<=t;){const t=new ArrayBuffer(this._itemsPerBuffer*this._itemByteSize);for(let e=0;e<this._itemsPerBuffer;++e)this._items.push(this._itemCreate(t,e*this._itemByteSize));this._buffers.push(t)}return this._items[this._itemsPtr++]}_reset(){const t=2*(Math.floor(this._itemsPtr/this._itemsPerBuffer)+1);for(;this._buffers.length>t;)this._buffers.pop(),this._items.length=this._buffers.length*this._itemsPerBuffer;this._itemsPtr=0}static createVec2f64(t=h){return new l(16,u.c,t)}static createVec3f64(t=h){return new l(24,c.b,t)}static createVec4f64(t=h){return new l(32,f.a,t)}static createMat3f64(t=h){return new l(72,a.a,t)}static createMat4f64(t=h){return new l(128,s.a,t)}static createQuatf64(t=h){return new l(32,i.c,t)}get test(){return{size:this._buffers.length*this._itemsPerBuffer*this._itemByteSize}}}const h=4*r.Y8.KILOBYTES,d=(l.createVec2f64(),l.createVec3f64()),m=(l.createVec4f64(),l.createMat3f64(),l.createMat4f64());l.createQuatf64()},3172:(t,e,n)=>{n.r(e),n.d(e,{default:()=>m});var r=n(68773),o=n(40330),a=n(20102),s=n(80442),i=n(22974),u=n(70586),c=n(95330),f=n(17452),l=n(19745),h=n(71058),d=n(85958);async function m(t,e){const i=(0,f.HK)(t),l=(0,f.jc)(t);l||i||(t=(0,f.Fv)(t));const g={url:t,requestOptions:{...(0,u.Wg)(e)}};let y=(0,f.oh)(t);if(y){const t=await async function(t,e){if(null!=t.responseData)return t.responseData;if(t.headers&&(e.requestOptions.headers={...e.requestOptions.headers,...t.headers}),t.query&&(e.requestOptions.query={...e.requestOptions.query,...t.query}),t.before){let n,r;try{r=await t.before(e)}catch(t){n=S("request:interceptor",t,e)}if((r instanceof Error||r instanceof a.Z)&&(n=S("request:interceptor",r,e)),n)throw t.error&&t.error(n),n;return r}}(y,g);if(null!=t)return{data:t,getHeader:_,httpStatus:200,requestOptions:g.requestOptions,url:g.url};y.after||y.error||(y=null)}if(t=g.url,"image"===(e=g.requestOptions).responseType){if((0,s.Z)("host-webworker")||(0,s.Z)("host-node"))throw S("request:invalid-parameters",new Error("responseType 'image' is not supported in Web Workers or Node environment"),g)}else if(i)throw S("request:invalid-parameters",new Error("Data URLs are not supported for responseType = "+e.responseType),g);if("head"===e.method){if(e.body)throw S("request:invalid-parameters",new Error("body parameter cannot be set when method is 'head'"),g);if(i||l)throw S("request:invalid-parameters",new Error("data and blob URLs are not supported for method 'head'"),g)}if(await async function(){(0,s.Z)("host-webworker")?M||(M=await n.e(9884).then(n.bind(n,29884))):m._abortableFetch||(m._abortableFetch=globalThis.fetch.bind(globalThis))}(),M)return M.execute(t,e);const w=new AbortController;(0,c.fu)(e,(()=>w.abort()));const E={controller:w,credential:void 0,credentialToken:void 0,fetchOptions:void 0,hasToken:!1,interceptor:y,params:g,redoRequest:!1,useIdentity:b.useIdentity,useProxy:!1,useSSL:!1,withCredentials:!1},T=await async function(t){let e,n;await async function(t){const e=t.params.url,n=t.params.requestOptions,a=t.controller.signal,s=n.body;let i=null,u=null;if(p&&"HTMLFormElement"in globalThis&&(s instanceof FormData?i=s:s instanceof HTMLFormElement&&(i=new FormData(s))),"string"==typeof s&&(u=s),t.fetchOptions={cache:n.cacheBust&&!m._abortableFetch.polyfill?"no-cache":"default",credentials:"same-origin",headers:n.headers||{},method:"head"===n.method?"HEAD":"GET",mode:"cors",priority:b.priority,redirect:"follow",signal:a},(i||u)&&(t.fetchOptions.body=i||u),"anonymous"===n.authMode&&(t.useIdentity=!1),t.hasToken=!!(/token=/i.test(e)||n.query?.token||i?.get("token")),!t.hasToken&&r.Z.apiKey&&(0,h.r)(e)&&(n.query||(n.query={}),n.query.token=r.Z.apiKey,t.hasToken=!0),t.useIdentity&&!t.hasToken&&!t.credentialToken&&!v(e)&&!(0,c.Hc)(a)){let r;"immediate"===n.authMode?(await P(),r=await o.id.getCredential(e,{signal:a}),t.credential=r):"no-prompt"===n.authMode?(await P(),r=await o.id.getCredential(e,{prompt:!1,signal:a}).catch((()=>{})),t.credential=r):o.id&&(r=o.id.findCredential(e)),r&&(t.credentialToken=r.token,t.useSSL=!!r.ssl)}}(t);try{do{[e,n]=await O(t)}while(!await x(t,e,n))}catch(n){const r=S("request:server",n,t.params,e);throw r.details.ssl=t.useSSL,t.interceptor&&t.interceptor.error&&t.interceptor.error(r),r}const a=t.params.url;if(n&&/\/sharing\/rest\/(accounts|portals)\/self/i.test(a)){if(!t.hasToken&&!t.credentialToken&&n.user?.username&&!(0,f.kl)(a)){const t=(0,f.P$)(a,!0);t&&b.trustedServers.push(t)}Array.isArray(n.authorizedCrossOriginNoCorsDomains)&&(0,d.Hu)(n.authorizedCrossOriginNoCorsDomains)}const s=t.credential;if(s&&o.id){const t=o.id.findServerInfo(s.server);let e=t&&t.owningSystemUrl;if(e){e=e.replace(/\/?$/,"/sharing");const t=o.id.findCredential(e,s.userId);t&&-1===o.id._getIdenticalSvcIdx(e,t)&&t.resources.unshift(e)}}return{data:n,getHeader:e?t=>e?.headers.get(t):_,httpStatus:e?.status??200,requestOptions:t.params.requestOptions,ssl:t.useSSL,url:t.params.url}}(E);return y?.after?.(T),T}let M;const b=r.Z.request,p="FormData"in globalThis,g=[499,498,403,401],y=["COM_0056","COM_0057","SB_0008"],w=[/\/arcgis\/tokens/i,/\/sharing(\/rest)?\/generatetoken/i,/\/rest\/info/i],_=()=>null,E=Symbol();function T(t){const e=(0,f.P$)(t);return!e||e.endsWith(".arcgis.com")||m._corsServers.includes(e)||(0,f.kl)(e)}function S(t,e,n,r){let o="Error";const s={url:n.url,requestOptions:n.requestOptions,getHeader:_,ssl:!1};if(e instanceof a.Z)return e.details?(e.details=(0,i.d9)(e.details),e.details.url=n.url,e.details.requestOptions=n.requestOptions):e.details=s,e;if(e){const t=r&&(t=>r.headers.get(t)),n=r&&r.status,a=e.message;a&&(o=a),t&&(s.getHeader=t),s.httpStatus=(null!=e.httpCode?e.httpCode:e.code)||n||0,s.subCode=e.subcode,s.messageCode=e.messageCode,"string"==typeof e.details?s.messages=[e.details]:s.messages=e.details,s.raw=E in e?e[E]:e}return(0,c.D_)(e)?(0,c.zE)():new a.Z(t,o,s)}async function P(){o.id||await Promise.all([n.e(6261),n.e(1400),n.e(450)]).then(n.bind(n,73660))}function v(t){return w.some((e=>e.test(t)))}async function O(t){let e=t.params.url;const n=t.params.requestOptions,r=t.fetchOptions??{},a=(0,f.jc)(e)||(0,f.HK)(e),i=n.responseType||"json",u=a?0:null!=n.timeout?n.timeout:b.timeout;let h=!1;if(!a){t.useSSL&&(e=(0,f.hO)(e)),n.cacheBust&&"default"===r.cache&&(e=(0,f.ZN)(e,"request.preventCache",Date.now()));let a={...n.query};t.credentialToken&&(a.token=t.credentialToken);let i=(0,f.B7)(a);(0,s.Z)("esri-url-encodes-apostrophe")&&(i=i.replace(/'/g,"%27"));const u=e.length+1+i.length;let c;h="delete"===n.method||"post"===n.method||"put"===n.method||!!n.body||u>b.maxUrlLength;const m=n.useProxy||!!(0,f.ed)(e);if(m){const t=(0,f.b7)(e);c=t.path,!h&&c.length+1+u>b.maxUrlLength&&(h=!0),t.query&&(a={...t.query,...a})}if("HEAD"===r.method&&(h||m)){if(h){if(u>b.maxUrlLength)throw S("request:invalid-parameters",new Error("URL exceeds maximum length"),t.params);throw S("request:invalid-parameters",new Error("cannot use POST request when method is 'head'"),t.params)}if(m)throw S("request:invalid-parameters",new Error("cannot use proxy when method is 'head'"),t.params)}if(h?(r.method="delete"===n.method?"DELETE":"put"===n.method?"PUT":"POST",n.body?e=(0,f.fl)(e,a):(r.body=(0,f.B7)(a),r.headers||(r.headers={}),r.headers["Content-Type"]="application/x-www-form-urlencoded")):e=(0,f.fl)(e,a),m&&(t.useProxy=!0,e=`${c}?${e}`),a.token&&p&&r.body instanceof FormData&&!(0,l.P)(e)&&r.body.set("token",a.token),n.hasOwnProperty("withCredentials"))t.withCredentials=n.withCredentials;else if(!(0,f.D6)(e,(0,f.TI)()))if((0,f.kl)(e))t.withCredentials=!0;else if(o.id){const n=o.id.findServerInfo(e);n&&n.webTierAuth&&(t.withCredentials=!0)}t.withCredentials&&(r.credentials="include",(0,d.jH)(e)&&await(0,d.jz)(h?(0,f.fl)(e,a):e))}let M,g,y=0,w=!1;u>0&&(y=setTimeout((()=>{w=!0,t.controller.abort()}),u));try{if("native-request-init"===n.responseType)g=r,g.url=e;else if("image"!==n.responseType||"default"!==r.cache||"GET"!==r.method||h||function(t){if(t)for(const e of Object.getOwnPropertyNames(t))if(t[e])return!0;return!1}(n.headers)||!a&&!t.useProxy&&b.proxyUrl&&!T(e)){if(M=await m._abortableFetch(e,r),t.useProxy||function(t){const e=(0,f.P$)(t);e&&!m._corsServers.includes(e)&&m._corsServers.push(e)}(e),"native"===n.responseType)g=M;else if("HEAD"!==r.method)if(M.ok){switch(i){case"array-buffer":g=await M.arrayBuffer();break;case"blob":case"image":g=await M.blob();break;default:g=await M.text()}if(y&&(clearTimeout(y),y=0),"json"===i||"xml"===i||"document"===i)if(g)switch(i){case"json":g=JSON.parse(g);break;case"xml":g=q(g,"application/xml");break;case"document":g=q(g,"text/html")}else g=null;if(g){if("array-buffer"===i||"blob"===i){const t=M.headers.get("Content-Type");if(t&&/application\/json|text\/plain/i.test(t)&&g["blob"===i?"size":"byteLength"]<=750)try{const t=await new Response(g).json();t.error&&(g=t)}catch{}}"image"===i&&g instanceof Blob&&(g=await I(URL.createObjectURL(g),t,!0))}}else g=await M.text()}else g=await I(e,t)}catch(r){if("AbortError"===r.name){if(w)throw new Error("Timeout exceeded");throw(0,c.zE)("Request canceled")}if(!(!M&&r instanceof TypeError&&b.proxyUrl)||n.body||"delete"===n.method||"head"===n.method||"post"===n.method||"put"===n.method||t.useProxy||T(e))throw r;t.redoRequest=!0,(0,f.tD)({proxyUrl:b.proxyUrl,urlPrefix:(0,f.P$)(e)??""})}finally{y&&clearTimeout(y)}return[M,g]}function q(t,e){let n;try{n=(new DOMParser).parseFromString(t,e)}catch{}if(!n||n.getElementsByTagName("parsererror").length)throw new SyntaxError("XML Parse error");return n}async function x(t,e,n){if(t.redoRequest)return t.redoRequest=!1,!1;const r=t.params.requestOptions;if(!e||"native"===r.responseType||"native-request-init"===r.responseType)return!0;let a,s;if(!e.ok)throw a=new Error(`Unable to load ${e.url} status: ${e.status}`),a[E]=n,a;n&&(n.error?a=n.error:"error"===n.status&&Array.isArray(n.messages)&&(a={...n},a[E]=n,a.details=n.messages));let i,u=null;a&&(s=Number(a.code),u=a.hasOwnProperty("subcode")?Number(a.subcode):null,i=a.messageCode,i=i&&i.toUpperCase());const c=r.authMode;if(403===s&&(4===u||a.message&&a.message.toLowerCase().includes("ssl")&&!a.message.toLowerCase().includes("permission"))){if(!t.useSSL)return t.useSSL=!0,!1}else if(!t.hasToken&&t.useIdentity&&("no-prompt"!==c||498===s)&&void 0!==s&&g.includes(s)&&!v(t.params.url)&&(403!==s||i&&!y.includes(i)&&(null==u||2===u&&t.credentialToken))){await P();try{const e=await o.id.getCredential(t.params.url,{error:S("request:server",a,t.params),prompt:"no-prompt"!==c,signal:t.controller.signal,token:t.credentialToken});return t.credential=e,t.credentialToken=e.token,t.useSSL=t.useSSL||e.ssl,!1}catch(e){if("no-prompt"===c)return t.credential=void 0,t.credentialToken=void 0,!1;a=e}}if(a)throw a;return!0}function I(t,e,n=!1){const r=e.controller.signal,o=new Image;return e.withCredentials?o.crossOrigin="use-credentials":o.crossOrigin="anonymous",o.alt="",o.fetchPriority=b.priority,o.src=t,(0,d.fY)(o,t,n,r)}m._abortableFetch=null,m._corsServers=["https://server.arcgisonline.com","https://services.arcgisonline.com"]},71058:(t,e,n)=>{n.d(e,{r:()=>a});var r=n(17452);const o=["elevation3d.arcgis.com","js.arcgis.com","jsdev.arcgis.com","jsqa.arcgis.com","static.arcgis.com"];function a(t){const e=(0,r.P$)(t,!0);return!!e&&e.endsWith(".arcgis.com")&&!o.includes(e)&&!t.endsWith("/sharing/rest/generateToken")}},85958:(t,e,n)=>{n.d(e,{Hu:()=>f,fY:()=>u,jH:()=>l,jz:()=>h});var r=n(68773),o=n(80442),a=n(70586),s=n(95330),i=n(17452);function u(t,e,n=!1,r){return new Promise(((i,u)=>{if((0,s.Hc)(r))return void u(c());let f=()=>{d(),u(new Error(`Unable to load ${e}`))},l=()=>{const e=t;d(),i(e)},h=()=>{if(!t)return;const e=t;d(),e.src="",u(c())};const d=()=>{(0,o.Z)("esri-image-decode")||(t.removeEventListener("error",f),t.removeEventListener("load",l)),f=null,l=null,t=null,(0,a.pC)(r)&&r.removeEventListener("abort",h),h=null,n&&URL.revokeObjectURL(e)};(0,a.pC)(r)&&r.addEventListener("abort",h),(0,o.Z)("esri-image-decode")?t.decode().then(l,f):(t.addEventListener("error",f),t.addEventListener("load",l))}))}function c(){try{return new DOMException("Aborted","AbortError")}catch{const t=new Error;return t.name="AbortError",t}}function f(t){r.Z.request.crossOriginNoCorsDomains||(r.Z.request.crossOriginNoCorsDomains={});const e=r.Z.request.crossOriginNoCorsDomains;for(let n of t)n=n.toLowerCase(),/^https?:\/\//.test(n)?e[(0,i.P$)(n)??""]=0:(e[(0,i.P$)("http://"+n)??""]=0,e[(0,i.P$)("https://"+n)??""]=0)}function l(t){const e=r.Z.request.crossOriginNoCorsDomains;if(e){let n=(0,i.P$)(t);if(n)return n=n.toLowerCase(),!(0,i.D6)(n,(0,i.TI)())&&e[n]<Date.now()-36e5}return!1}async function h(t){const e=r.Z.request.crossOriginNoCorsDomains,n=(0,i.P$)(t);e&&n&&(e[n.toLowerCase()]=Date.now());const o=(0,i.mN)(t);t=o.path,"json"===o.query?.f&&(t+="?f=json");try{await fetch(t,{mode:"no-cors",credentials:"include"})}catch{}}},31412:(t,e,n)=>{n.r(e),n.d(e,{destroyContext:()=>w,dracoDecompressPointCloudData:()=>b,filterObbsForModifications:()=>p,filterObbsForModificationsSync:()=>v,initialize:()=>x,interpretObbModificationResults:()=>P,process:()=>M,setLegacySchema:()=>y,setModifications:()=>g,setModificationsSync:()=>T,test:()=>I});var r,o,a=n(70586);!function(t){t[t.None=0]="None",t[t.Int16=1]="Int16",t[t.Int32=2]="Int32"}(r||(r={})),function(t){t[t.Replace=0]="Replace",t[t.Outside=1]="Outside",t[t.Inside=2]="Inside",t[t.Finished=3]="Finished"}(o||(o={}));var s=n(99880);function i(t){return(0,s.V)(`esri/libs/i3s/${t}`)}let u;var c,f,l,h,d,m;async function M(t){await x();const e=[t.geometryBuffer];return{result:S(t,e),transferList:e}}async function b(t){await x();const e=[t.geometryBuffer],{geometryBuffer:n}=t,r=n.byteLength,o=E._malloc(r),a=new Uint8Array(E.HEAPU8.buffer,o,r);a.set(new Uint8Array(n));const s=E.dracoDecompressPointCloudData(o,a.byteLength);if(E._free(o),s.error.length>0)throw new Error(`i3s.wasm: ${s.error}`);const i=s.featureIds?.length>0?s.featureIds.slice():null,u=s.positions.slice();return i&&e.push(i.buffer),e.push(u.buffer),{result:{positions:u,featureIds:i},transferList:e}}async function p(t){await x(),v(t);const e={buffer:t.buffer};return{result:e,transferList:[e.buffer]}}async function g(t){await x(),T(t)}async function y(t){await x(),E.setLegacySchema(t.context,t.jsonSchema)}function w(t){O(t)}let _,E;function T(t){const e=t.modifications,n=E._malloc(8*e.length),r=new Float64Array(E.HEAPU8.buffer,n,e.length);for(let t=0;t<e.length;++t)r[t]=e[t];E.setModifications(t.context,n,e.length,t.isGeodetic),E._free(n)}function S(t,e){if(!E)return null;const{context:n,localOrigin:o,globalTrafo:s,mbs:i,obb:u,elevationOffset:c,geometryBuffer:f,geometryDescriptor:l,indexToVertexProjector:h,vertexToRenderProjector:d}=t,m=E._malloc(f.byteLength),M=E._malloc(33*Float64Array.BYTES_PER_ELEMENT),b=new Uint8Array(E.HEAPU8.buffer,m,f.byteLength);b.set(new Uint8Array(f));const p=new Float64Array(E.HEAPU8.buffer,M,33);q(p,o);let g=p.byteOffset+3*p.BYTES_PER_ELEMENT,y=new Float64Array(p.buffer,g);q(y,s),g+=16*p.BYTES_PER_ELEMENT,y=new Float64Array(p.buffer,g),q(y,i),g+=4*p.BYTES_PER_ELEMENT,(0,a.pC)(u)&&(y=new Float64Array(p.buffer,g),q(y,u.center),g+=3*p.BYTES_PER_ELEMENT,y=new Float64Array(p.buffer,g),q(y,u.halfSize),g+=3*p.BYTES_PER_ELEMENT,y=new Float64Array(p.buffer,g),q(y,u.quaternion));const w=l,_={isDraco:!1,isLegacy:!1,color:t.layouts.some((t=>t.some((t=>"color"===t.name)))),normal:t.needNormals&&t.layouts.some((t=>t.some((t=>"normalCompressed"===t.name)))),uv0:t.layouts.some((t=>t.some((t=>"uv0"===t.name)))),uvRegion:t.layouts.some((t=>t.some((t=>"uvRegion"===t.name)))),featureIndex:w.featureIndex},T=E.process(n,!!t.obb,m,b.byteLength,w,_,M,c,h,d,t.normalReferenceFrame);if(E._free(M),E._free(m),T.error.length>0)throw new Error(`i3s.wasm: ${T.error}`);if(T.discarded)return null;const S=T.componentOffsets.length>0?T.componentOffsets.slice():null,P=T.featureIds.length>0?T.featureIds.slice():null,v=T.interleavedVertedData.slice().buffer,O=T.indicesType===r.Int16?new Uint16Array(T.indices.buffer,T.indices.byteOffset,T.indices.byteLength/2).slice():new Uint32Array(T.indices.buffer,T.indices.byteOffset,T.indices.byteLength/4).slice(),x=T.positions.slice(),I=T.positionIndicesType===r.Int16?new Uint16Array(T.positionIndices.buffer,T.positionIndices.byteOffset,T.positionIndices.byteLength/2).slice():new Uint32Array(T.positionIndices.buffer,T.positionIndices.byteOffset,T.positionIndices.byteLength/4).slice(),A={layout:t.layouts[0],interleavedVertexData:v,indices:O,hasColors:T.hasColors,hasModifications:T.hasModifications,positionData:{data:x,indices:I}};return P&&e.push(P.buffer),S&&e.push(S.buffer),e.push(v),e.push(O.buffer),e.push(x.buffer),e.push(I.buffer),{componentOffsets:S,featureIds:P,transformedGeometry:A,obb:T.obb}}function P(t){return 0===t?f.Unmodified:1===t?f.PotentiallyModified:2===t?f.Culled:f.Unknown}function v(t){const{context:e,buffer:n}=t,r=E._malloc(n.byteLength),o=n.byteLength/Float64Array.BYTES_PER_ELEMENT,a=new Float64Array(E.HEAPU8.buffer,r,o),s=new Float64Array(n);a.set(s),E.filterOBBs(e,r,o),s.set(a),E._free(r)}function O(t){E&&E.destroy(t)}function q(t,e){for(let n=0;n<e.length;++n)t[n]=e[n]}function x(){return E?Promise.resolve():(_||(_=(u||(u=new Promise((t=>n.e(6710).then(n.bind(n,26710)).then((t=>t.i)).then((({default:e})=>{const n=e({locateFile:i,onRuntimeInitialized:()=>t(n)});delete n.then})))).catch((t=>{throw t}))),u).then((t=>{E=t,_=null}))),_)}n(29268),(m=c||(c={}))[m.Unmodified=0]="Unmodified",m[m.Culled=1]="Culled",m[m.NotChecked=2]="NotChecked",function(t){t[t.Unmodified=0]="Unmodified",t[t.PotentiallyModified=1]="PotentiallyModified",t[t.Culled=2]="Culled",t[t.Unknown=3]="Unknown",t[t.NotChecked=4]="NotChecked"}(f||(f={})),function(t){t[t.Unknown=0]="Unknown",t[t.Uncached=1]="Uncached",t[t.Cached=2]="Cached"}(l||(l={})),function(t){t[t.None=0]="None",t[t.MaxScreenThreshold=1]="MaxScreenThreshold",t[t.ScreenSpaceRelative=2]="ScreenSpaceRelative",t[t.RemovedFeatureDiameter=3]="RemovedFeatureDiameter",t[t.DistanceRangeFromDefaultCamera=4]="DistanceRangeFromDefaultCamera"}(h||(h={})),function(t){t[t.Hole=0]="Hole",t[t.Leaf=1]="Leaf"}(d||(d={}));const I={transform:S,destroy:O}}}]);