import{h as y}from"./GraphicsLayer-DTrBRwJQ.js";import{g as l,v as d,S as v,l as a,m as _}from"./MapView-DaoQedLH.js";import{d as k,ad as R,r as B,o as C,bu as M,Q as N,g as S,n as x,ac as A}from"./index-r0dFAfgr.js";import"./Point-WxyopZva.js";import"./geometryEngineBase-BhsKaODW.js";import{s as f}from"./ToolHelper-BiiInOzB.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./project-DUuzYgGl.js";import{b as E}from"./ViewHelper-BGCZjxXH.js";import"./index-0NlGN6gS.js";import{G,b as P}from"./dma-SMxrzG7b.js";import"./widget-BcWKanF2.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./TileLayer-B5vQ99gG.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./fieldconfig-Bk3o1wi7.js";import"./LayerHelper-Cn-iiqxI.js";import"./pipe-nogVzCHG.js";/* empty css                                                                      */const Yt=k({__name:"Neighbourhood",emits:["click"],setup(T,{emit:u}){const n=u,p=R("view"),m=new y({id:"neighbourhood",title:"小区边界"});p.map.add(m);const h=(t,r,i,o)=>{m.addMany([new l({geometry:new d({rings:i,spatialReference:p.spatialReference}),symbol:new v({color:new a(A(o,.4)),outline:{width:1,color:new a(o)}}),attributes:{id:t,title:r}}),new l({geometry:new d({rings:i,spatialReference:p.spatialReference}),symbol:new _({text:r,font:{size:12},haloSize:1,haloColor:new a("#ffffff"),color:new a(o)}),attributes:{id:t,title:r}})])},s=B([]),c=()=>{m.removeAll(),s.map(t=>{h(t.id,t.title,t.boundary,t.color)})};p.when().then(()=>{c()}),C(()=>{n("click",{id:"1",title:"文庭雅苑"}),E(p,t=>{var r,i,o,e;if(((i=(r=t.results)==null?void 0:r[0])==null?void 0:i.type)==="graphic"){const g=(e=(o=t.results)==null?void 0:o[0])==null?void 0:e.graphic.attributes;n("click",g)}}),p.on("pointer-move",t=>{p.hitTest(t).then(r=>{var i,o;((o=(i=r.results)==null?void 0:i[0])==null?void 0:o.type)==="graphic"?f("pointer"):f("")})})});const b=t=>{let r=[];function i(o){o.map(e=>{e.children&&e.children.length>0?i(e.children):r.push(e)})}return i(t),r},w=()=>{G().then(t=>{const r=b(t.data),i=[];r.forEach(o=>{i.push(P(o.id))}),Promise.all(i).then(o=>{o.forEach(e=>{s.push({id:e.data.id,title:e.data.name,color:"#eb7953",boundary:JSON.parse(e.data.geom.slice(1,-1))})}),c()})})};return M(()=>{w()}),N(()=>{m.removeAll(),p.map.remove(m)}),(t,r)=>(S(),x("div"))}});export{Yt as default};
