import{d as F,c as k,r as L,b as _,Q as q,g as z,h as G,F as v,p as N,q as S,i as n,_ as O,X as P}from"./index-r0dFAfgr.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import{g as Q,a as V}from"./LayerHelper-Cn-iiqxI.js";import{g as $}from"./QueryHelper-ILO3qZqg.js";import{E as j}from"./StatisticsHelper-D-s_6AyQ.js";import{u as U}from"./arcWidgetButton-0glIxrt7.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import X from"./RightDrawerMap-D5PhmGFO.js";import H from"./StatisticsCharts-CyK-dNnC.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./project-DUuzYgGl.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./pipe-nogVzCHG.js";import"./executeForIds-BLdIsxvI.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./ArcView-DpMnCY82.js";import"./ViewHelper-BGCZjxXH.js";import"./fieldconfig-Bk3o1wi7.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./ArcStationWarning-BF9YrSzF.js";/* empty css                         */import"./useWaterPoint-Bv0z6ym6.js";import"./DateFormatter-Bm9a68Ax.js";import"./zhandian-YaGuQZe6.js";import"./useStation-DJgnSZIA.js";import"./DrawerBox-CLde5xC8.js";import"./SideDrawer-CBntChyn.js";import"./PipeDetail-CTBPYFJW.js";import"./Search-NSrhrIa_.js";import"./FormTableColumnFilter-BT7pLXIC.js";import"./config-fy91bijz.js";import"./ArcBRTools-BO92yznB.js";import"./PipeLength.vue_vue_type_script_setup_true_lang-BZfUsvDf.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./wfsUtils-DXofo3da.js";import"./usePipelineGroup-Ba1pmWz_.js";import"./GroupLayer-DhhN3FyN.js";import"./lazyLayerLoader-DbM9sT1W.js";import"./WFSLayer-1TtJp3nx.js";import"./clientSideDefaults-VQhQaYxh.js";import"./QueryEngineCapabilities-Dk3NJwmm.js";import"./wfsUtils-Du031XaC.js";import"./geojson-MBFu2-HZ.js";import"./xmlUtils-CtUoQO7q.js";import"./ListWindow-WS05QqV0.js";import"./PopLayout-BP55MvL7.js";import"./PoiSearchV2-D7yNeLuv.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./URLHelper-B9aplt5w.js";import"./useLayerList-DmEwJ-ws.js";import"./useScaleBar-Beed-z91.js";import"./useDetector-BRcb7GRN.js";import"./useHighLight-DPevRAc5.js";import"./ToolHelper-BiiInOzB.js";import"./geoserverUtils-wjOSMa7E.js";import"./echart-BoVIcYbV.js";import"./config-DqqM5K5L.js";import"./OutsideWorkOrder-C6s8joBt.js";import"./index-CpGhZCTT.js";const me=F({__name:"PipeMaterial",setup(J){const c=k(),d=k(),A=k(),e=L({tabs:[],curType:"",layerInfos:[],layerIds:[],loading:!1}),i={},R=L({group:[{fieldset:{desc:"绘制工具"},fields:[{type:"btn-group",btns:[{perm:!0,text:"",type:"default",size:"large",title:"绘制多边形",disabled:()=>e.loading,iconifyIcon:"mdi:shape-polygon-plus",click:()=>y("polygon")},{perm:!0,text:"",type:"default",size:"large",title:"绘制矩形",disabled:()=>e.loading,iconifyIcon:"ep:crop",click:()=>y("rectangle")},{perm:!0,text:"",type:"default",size:"large",title:"绘制圆形",disabled:()=>e.loading,iconifyIcon:"mdi:ellipse-outline",click:()=>y("circle")},{perm:!0,text:"",type:"default",size:"large",title:"清除图形",disabled:()=>e.loading,iconifyIcon:"ep:delete",click:()=>b()}]},{type:"btn-group",btns:[{perm:!0,text:"统计",styles:{width:"100%"},loading:()=>e.loading,click:()=>C()}]}]}],labelPosition:"top",gutter:12}),M=(t,r)=>{const o=t.label;f(o,r,!0)},x=(t,r)=>{const o=t.label;f(o,r)},w=(t,r)=>{const o=t==null?void 0:t.name;f(o,r)},f=async(t,r,o)=>{var l,p;if(t===void 0)return;const a=t==="合计"?"1=1":t==="--"?" MATERIAL is null ":"MATERIAL='"+t+"'";await C(a,r);const m=(l=c.value)==null?void 0:l.getCurLayer();(p=c.value)==null||p.refreshDetail(m,o??!0)},y=t=>{var r;i.view&&(b(),(r=i.sketch)==null||r.create(t))},b=()=>{var t;(t=i.graphicsLayer)==null||t.removeAll(),i.graphics=void 0},T=async()=>{var r,o;e.layerIds=V(i.view);const t=await P(e.layerIds);e.layerInfos=((o=(r=t.data)==null?void 0:r.result)==null?void 0:o.rows)||[],e.layerIds=e.layerInfos.filter(a=>a.geometrytype==="esriGeometryPolyline").map(a=>a.layerid)},C=async(t,r)=>{var o,a,m,l;_.info("正在统计，请稍候...");try{e.loading=!0;const p=r===void 0?e.layerIds:e.layerInfos.filter(s=>s.layername===r).map(s=>s.layerid),u=await $(p,e.layerInfos,{where:t||"1=1",geometry:(o=i.graphics)==null?void 0:o.geometry});if(r!==void 0)if(e.tabs.length){const s=e.tabs.find(h=>h.name===r),g=u.find(h=>h.name===r);s&&(s.data=g==null?void 0:g.data),(a=c.value)==null||a.refreshTable(r,p==null?void 0:p[0])}else e.tabs=u;else e.tabs=u;((m=d.value)==null?void 0:m.isCustomOpened())||(l=d.value)==null||l.toggleCustomDetail(!0)}catch(p){console.log(p),e.loading=!1,_.error("统计失败")}},{initSketch:B,destroySketch:D}=U(),I=t=>{i.graphics=t.graphics[0]},E=t=>{i.view=t,i.graphicsLayer=Q(i.view,{id:"search-pipe-materail",title:"按材质统计管长"}),i.sketch=B(i.view,i.graphicsLayer,{createCallBack:I,updateCallBack:I}),T()};return q(()=>{var t,r;D(),(t=i.graphicsLayer)==null||t.removeAll(),(r=i.graphicsLayer)==null||r.destroy()}),(t,r)=>{const o=O;return z(),G(X,{ref_key:"refMap",ref:d,title:"按材质统计管长","full-content":!0,onMapLoaded:E},{"detail-header":v(()=>r[1]||(r[1]=[N("span",null,"统计结果",-1)])),"detail-default":v(()=>{var a;return[S(H,{ref_key:"refStatisticsCharts",ref:c,view:i.view,"layer-ids":n(e).layerIds,"query-params":{where:"1=1",geometry:(a=i.graphics)==null?void 0:a.geometry},"statistics-params":{group_fields:["MATERIAL"],statistic_field:n(j).ShapeLen,statistic_type:"2"},tabs:n(e).tabs,unit:"m",onDetailRefreshed:r[0]||(r[0]=m=>n(e).loading=!1),onAttrRowClick:x,onBarClick:w,onRingClick:w,onTotalRowClick:M},null,8,["view","layer-ids","query-params","statistics-params","tabs"])]}),default:v(()=>[S(o,{ref_key:"refForm",ref:A,config:n(R)},null,8,["config"])]),_:1},512)}}});export{me as default};
