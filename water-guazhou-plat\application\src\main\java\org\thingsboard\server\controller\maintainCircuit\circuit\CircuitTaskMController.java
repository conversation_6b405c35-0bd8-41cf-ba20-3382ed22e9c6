package org.thingsboard.server.controller.maintainCircuit.circuit;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.maintainCircuit.circuit.CircuitTaskMService;
import org.thingsboard.server.dao.model.sql.maintainCircuit.circuit.CircuitTaskM;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.Date;
import java.util.List;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-09-08
 */
@RestController
@RequestMapping("api/circuit/task/m")
public class CircuitTaskMController extends BaseController {

    @Autowired
    private CircuitTaskMService circuitTaskMService;

    @GetMapping
    public IstarResponse getList(@RequestParam(required = false, defaultValue = "") String code,
                                 @RequestParam(required = false, defaultValue = "") String name,
                                 @RequestParam(required = false, defaultValue = "") String deviceLabelCode,
                                 @RequestParam(required = false, defaultValue = "") String deviceId,
                                 @RequestParam(required = false, defaultValue = "") String userId,
                                 @RequestParam(required = false, defaultValue = "") String planName,
                                 @RequestParam(required = false, defaultValue = "") String teamName,
                                 @RequestParam(required = false, defaultValue = "") String userName,
                                 @RequestParam(required = false, defaultValue = "") String status,
                                 @RequestParam(required = false, defaultValue = "") String auditStatus,
                                 Long startStartTime,
                                 Long startEndTime,
                                 Long endStartTime,
                                 Long endEndTime,
                                 int page, int size) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());

        return IstarResponse.ok(circuitTaskMService.getList(code, name, planName, teamName, userName, status, auditStatus, deviceLabelCode, deviceId, userId, startStartTime == null ? null : new Date(startStartTime), startEndTime == null ? null : new Date(startEndTime), endStartTime == null ? null : new Date(endStartTime), endEndTime == null ? null : new Date(endEndTime), page, size, tenantId));
    }

    @GetMapping("detail/{mainId}")
    public IstarResponse getDetail(@PathVariable String mainId) {
        return IstarResponse.ok(circuitTaskMService.getDetail(mainId));
    }

    @PostMapping
    public IstarResponse save(@RequestBody CircuitTaskM circuitTaskM) throws ThingsboardException {
        circuitTaskM.setCreator(UUIDConverter.fromTimeUUID(getCurrentUser().getUuidId()));
        circuitTaskM.setTenantId(UUIDConverter.fromTimeUUID(getTenantId().getId()));
        return IstarResponse.ok(circuitTaskMService.save(circuitTaskM));
    }

    @DeleteMapping
    public IstarResponse delete(@RequestBody List<String> ids) {
        return circuitTaskMService.delete(ids);
    }


    @PostMapping("changeStatus")
    public IstarResponse changeStatus(@RequestBody CircuitTaskM circuitTaskM) throws ThingsboardException {
        circuitTaskMService.reviewer(circuitTaskM);

        return IstarResponse.ok("操作成功");
    }

    @PostMapping("reviewer")
    public IstarResponse reviewer(@RequestBody CircuitTaskM circuitTaskM) throws ThingsboardException {
        boolean checkAuditor = this.checkAuditor(circuitTaskM.getId(), UUIDConverter.fromTimeUUID(getCurrentUser().getUuidId()));
        if (!checkAuditor) {
            return IstarResponse.error("您没有审核权限");
        }
        circuitTaskM.setAuditor(UUIDConverter.fromTimeUUID(getCurrentUser().getUuidId()));
        circuitTaskMService.reviewer(circuitTaskM);

        return IstarResponse.ok("审核成功");
    }

    @GetMapping("receive/{id}")
    public IstarResponse receive(@PathVariable String id) throws ThingsboardException {
        String userId = UUIDConverter.fromTimeUUID(getCurrentUser().getUuidId());
        return circuitTaskMService.receive(id, userId);
    }

    @GetMapping("notCompleteNum")
    public IstarResponse getNotCompleteNum() throws ThingsboardException {
        String userId = UUIDConverter.fromTimeUUID(getCurrentUser().getUuidId());
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return IstarResponse.ok(circuitTaskMService.getNotCompleteNum(userId, tenantId));
    }

    private boolean checkAuditor(String id, String userId) {
        return circuitTaskMService.checkAuditor(id, userId);
    }

    /**
     * 统计
     * @return
     * @throws ThingsboardException
     */
    @GetMapping("statistics")
    public IstarResponse statistics(Long startTime, Long endTime) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());

        return IstarResponse.ok(circuitTaskMService.statistics(startTime, endTime, tenantId));
    }
}
