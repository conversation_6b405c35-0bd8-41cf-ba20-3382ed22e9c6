package org.thingsboard.server.dao.model.sql.workOrder;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("work_order_emergency_level")
public class WorkOrderEmergencyLevel {

    private String id;

    private String name;

    private String color;

    private Integer orderNumber;

    private String status;

    private Date createTime;

    private String tenantId;

}
