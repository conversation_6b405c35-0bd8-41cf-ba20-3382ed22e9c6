import{_ as x}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{z as g,d as D,c as _,r as d,b as o,S as k,o as S,g as I,n as q,q as f,i as m,aB as C,aq as F,C as M}from"./index-r0dFAfgr.js";import{_ as P}from"./Search-NSrhrIa_.js";const V=r=>g({url:"/api/spp/dma/partition/valve/list",method:"get",params:r}),L=r=>g({url:"/api/spp/dma/partition/valve",method:"post",data:r}),T=r=>g({url:"/api/spp/dma/partition/valve",method:"delete",data:r}),X=D({__name:"MoreDetail_FMXX",props:{partition:{}},setup(r){const b=r,c=_(),u=_(),v=d({filters:[{type:"select",label:"类型",field:"type",options:[{label:"边界阀门",value:"1"},{label:"分支管线阀门",value:"2"}]},{type:"input",label:"编号",field:"code",clearable:!1}],operations:[{type:"btn-group",btns:[{perm:!0,iconifyIcon:"ep:search",text:"查询",type:"primary",click:()=>n()},{perm:!0,iconifyIcon:"ep:refresh",text:"重置",type:"default",click:()=>{var e;(e=c.value)==null||e.resetForm()}},{perm:!0,iconifyIcon:"ep:circle-plus",text:"新增",type:"success",click:()=>y()}]}]}),l=d({dataList:[],columns:[{label:"类型",prop:"typeName"},{label:"编号",prop:"code"},{label:"口径",prop:"caliber"},{label:"阀门类型",prop:"valveType"},{label:"地址",prop:"address"},{label:"运行状态",prop:"runStatus"},{label:"备注",prop:"remark"},{label:"现场图片",prop:"img",image:!0}],pagination:{refreshData:({page:e,size:a})=>{l.pagination.page=e,l.pagination.limit=a,n()}},operations:[{perm:!0,text:"编辑",iconifyIcon:"ep:edit",click:e=>y(e)},{perm:!0,text:"删除",iconifyIcon:"ep:delete",type:"danger",click:e=>h(e)}]}),n=async()=>{var e,a;l.loading=!0;try{const t=((e=c.value)==null?void 0:e.queryParams)||{},p=(await V({...t,partitionId:(a=b.partition)==null?void 0:a.value,page:l.pagination.page||1,size:l.pagination.limit||20})).data.data||{};l.dataList=p.data||[],l.pagination.total=p.total||0}catch{}l.loading=!1},y=e=>{var a;s.defaultValue={...e||{}},s.title=e?"编辑阀门表":"添加阀门表",(a=u.value)==null||a.openDialog()},h=e=>{const a=e?[e.id]:[];if(!a.length){o.error("请选择要删除的数据");return}k("确定删除?","提示信息").then(async()=>{try{const t=await T(a);t.data.code===200?(o.success("删除成功"),n()):o.error(t.data.message)}catch{o.error("删除失败")}}).catch(()=>{})},s=d({title:"添加流量表",dialogWidth:600,labelPosition:"right",group:[{fields:[{lg:24,xl:12,type:"select",label:"类型",field:"type",options:[{label:"边界阀门",value:"1"},{label:"分支管线阀门",value:"2"}],rules:[{required:!0,message:"请输入类型"}]},{lg:24,xl:12,type:"input",label:"编号",field:"code"},{lg:24,xl:12,type:"input",label:"阀门类型",field:"valveType",rules:[{required:!0,message:"请输入阀门类型"}]},{lg:24,xl:12,type:"input-number",label:"口径",field:"caliber"},{lg:24,xl:12,type:"textarea",label:"地址",field:"address"},{lg:24,xl:12,type:"input",label:"运行状态",field:"runStatus"},{type:"textarea",label:"备注",field:"remark"},{type:"image",label:"现场图片",field:"img"}]}],submit:async e=>{var a,t;s.submitting=!0;try{const i=await L({...e,partitionId:(a=b.partition)==null?void 0:a.value});i.data.code===200?(o.success("提交成功"),n(),(t=u.value)==null||t.closeDialog()):o.error(i.data.message)}catch{o.error("提交失败")}s.submitting=!1}});return S(()=>{n()}),(e,a)=>{const t=P,i=F,p=x;return I(),q(C,null,[f(t,{ref_key:"refSearch",ref:c,config:m(v),class:"search"},null,8,["config"]),f(i,{config:m(l),class:"table-box"},null,8,["config"]),f(p,{ref_key:"refDialog",ref:u,config:m(s)},null,8,["config"])],64)}}}),N=M(X,[["__scopeId","data-v-03a63179"]]);export{N as default};
