package org.thingsboard.server.dao.dataSource;

import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.model.sql.RestApiEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/2/26 15:03
 */
public interface RestApiDao {

    RestApiEntity save(RestApiEntity restApiEntity);

    List<RestApiEntity> findByTenantId(TenantId tenantId);

    List<RestApiEntity> findByProjectId(String projectId);

    RestApiEntity findById(String id);

    boolean deleteById(String id);

    List<RestApiEntity> findAll();

    int countRestApiByName(String tenantId,String name);
}
