<!-- 供水量分析 -->
<template>
  <div class="wrapper">
    <CardSearch ref="cardSearch" :config="cardSearchConfig" />
    <SLCard class="card" title=" ">
      <template #right>
        <el-radio-group v-model="state.activeName">
          <el-radio-button label="echarts">
            <Icon
              style="margin-right: 1px; font-size: 16px"
              icon="clarity:line-chart-line"
            />
          </el-radio-button>
          <el-radio-button label="list">
            <Icon
              style="margin-right: 1px; font-size: 16px"
              icon="material-symbols:table"
            />
          </el-radio-button>
        </el-radio-group>
      </template>
      <div
        v-show="state.activeName === 'echarts'"
        ref="agriEcoDev"
        class="chart-box"
      >
        <!-- 图表模式 -->
        <VChart
          ref="refChart"
          :theme="appStore.isDark ? 'dark' : 'light'"
          :option="state.chartOption"
        ></VChart>
      </div>
      <!-- 列表模式 -->
      <div v-show="state.activeName === 'list'">
        <FormTable
          ref="refCardTable"
          class="card-table"
          :config="cardTableConfig"
        />
      </div>
    </SLCard>
  </div>
</template>
<script lang="ts" setup>
import dayjs from 'dayjs';
import elementResizeDetectorMaker from 'element-resize-detector';
import { Download, Refresh } from '@element-plus/icons-vue';
import { onUnmounted } from 'vue';
import { Icon } from '@iconify/vue';
import { useAppStore } from '@/store';
import {
  getFormatTreeNodeDeepestChild,
  objectLookup
} from '@/utils/GlobalHelper';
import {
  getWaterSupplyDetailReport,
  exportWaterSupplyDetailReport
} from '@/api/headwatersManage/statisticalAnalysis';
import { lineOption } from '../../echartsData/echart';
import { IECharts } from '@/plugins/echart';
import useStation from '@/hooks/station/useStation';
import useGlobal from '@/hooks/global/useGlobal';

const appStore = useAppStore();
const { $messageWarning } = useGlobal();
const erd = elementResizeDetectorMaker();
const { getStationTree } = useStation();
const state = reactive<{
  type: 'day' | 'month' | 'year';
  chartOption: any;
  activeName: string;
  dataList: any;
}>({
  type: 'day',
  chartOption: null,
  activeName: 'echarts',
  dataList: {}
});
//

const today = dayjs().date();
// const totalLoading = ref<boolean>(false)
const cardSearch = ref<ICardSearchIns>();
const refChart = ref<IECharts>();
const agriEcoDev = ref<any>();

// 水源站点树
const TreeData = reactive<SLTreeConfig>({
  data: [],
  checkedKeys: [],
  checkedNodes: []
});

// 搜索栏初始化配置
const cardSearchConfig = reactive<ISearch>({
  defaultParams: {
    type: 'day',
    year: dayjs().format(),
    month: dayjs().format(),
    day: dayjs().date(today)
  },
  filters: [
    {
      type: 'select-tree',
      field: 'treeData',
      defaultExpandAll: true,
      multiple: true,
      options: computed(() => TreeData.data) as any,
      label: '站点选择',
      onChange: (key) => {
        TreeData.checkedKeys = key || [];
        TreeData.checkedNodes = [];
        for (const i in key) {
          const val = objectLookup(TreeData.data, 'children', 'id', i);
          TreeData.checkedNodes.push(val);
        }
        refreshData();
      }
    },
    {
      type: 'radio-button',
      field: 'type',
      options: [
        { label: '日报', value: 'day' },
        { label: '月报', value: 'month' },
        { label: '年报', value: 'year' }
      ],
      label: '报告类型'
    },
    {
      type: 'date',
      label: '选择时间',
      field: 'day',
      clearable: false,
      handleHidden: (params: any, query: any, formItem: IFormItem) => {
        formItem.hidden = params.type === 'month' || params.type === 'year';
      }
    },
    {
      type: 'month',
      label: '选择时间',
      field: 'month',
      clearable: false,
      handleHidden: (params: any, query: any, formItem: IFormItem) => {
        formItem.hidden = params.type === 'day' || params.type === 'year';
      }
    },
    {
      type: 'year',
      label: '选择时间',
      field: 'year',
      clearable: false,
      handleHidden: (params: any, query: any, formItem: IFormItem) => {
        formItem.hidden = params.type === 'month' || params.type === 'day';
      }
    },
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          click: () => {
            if ((TreeData.checkedNodes || []).length > 0) {
              refreshData();
            } else {
              $messageWarning('请选择站点');
            }
          },
          icon: 'iconfont icon-chaxun'
        },
        {
          type: 'default',
          perm: true,
          text: '重置',
          svgIcon: shallowRef(Refresh),
          click: () => {
            cardSearch.value?.resetForm();
          }
        },
        {
          type: 'warning',
          perm: true,
          text: '导出',
          svgIcon: shallowRef(Download),
          hide: () => {
            return state.activeName !== 'list';
          },
          click: () => exportExcel()
        }
      ]
    }
  ]
});

// 导出报表
const exportExcel = () => {
  const queryParams = (cardSearch.value?.queryParams as any) || {};
  const stationIdList = TreeData.checkedKeys as any[];
  const params: any = {
    stationIdList: stationIdList.join(','),
    queryType: queryParams.type,
    start: dayjs(queryParams.date).startOf(queryParams.type).valueOf(),
    end: dayjs(queryParams.date).endOf(queryParams.type).valueOf()
  };

  exportWaterSupplyDetailReport(params).then((res) => {
    const url = window.URL.createObjectURL(res.data);
    const link = document.createElement('a');
    link.style.display = 'none';
    link.href = url;
    link.setAttribute('download', `供水量明细表.xlsx`);
    document.body.appendChild(link);
    link.click();
  });
};
// 定义动态表头初始化数据
// let weekDate = reactive<IFormTableColumn[]>([])

// 初始化列表配置数据
const cardTableConfig = reactive<ITable>({
  loading: false,
  dataList: [],
  columns: [],
  operations: [],
  // showSummary: true,
  // summaryMethod: (clumns: any, data: any) => getSummaries(clumns, data),
  pagination: {
    hide: true
  }
});

// 刷新列表 模拟数据
const refreshData = () => {
  cardTableConfig.loading = true;
  const queryParams = (cardSearch.value?.queryParams as any) || {};
  const stationIdList = TreeData.checkedKeys as any[];
  const params: any = {
    stationIdList: stationIdList.join(','),
    queryType: queryParams.type,
    start: dayjs(queryParams.date).startOf(queryParams.type).valueOf(),
    end: dayjs(queryParams.date).endOf(queryParams.type).valueOf()
  };

  getWaterSupplyDetailReport(params).then((res) => {
    const data = res.data?.data;
    state.dataList = data;
    const columns = data?.tableInfo.map((item: any) => {
      return {
        prop: item.columnValue,
        label: item.columnName,
        minWidth: 120,
        unit: item.unit ? '(' + item.unit + ')' : ''
      };
    });
    console.log(columns);

    cardTableConfig.columns = columns;
    cardTableConfig.dataList = data?.tableDataList;
    cardTableConfig.loading = false;
    refuseChart();
  });
};

const resizeChart = () => {
  refChart.value?.resize();
};
// 加载图表
const refuseChart = () => {
  nextTick(() => {
    const chartOption = lineOption();
    const tableDataList = state.dataList?.tableDataList;
    const data = tableDataList?.slice(0, tableDataList.length - 6);
    chartOption.xAxis.data = data?.map((item) => item.ts);
    chartOption.series = [];
    chartOption.yAxis[0].name = '流量(m³)';

    const legend = state.dataList?.tableInfo?.filter((item) => {
      if (!['数据时间', '合计'].includes(item.columnName)) {
        return item.columnName;
      }
    });

    console.log(legend);
    for (const i in legend) {
      const serie: any = {
        name: legend[i].columnName,
        smooth: true,
        data: data.map((item) => item[legend[i].columnValue]),
        type: 'line',
        markPoint: {
          data: [
            {
              type: 'max',
              name: '最大值',
              label: {
                fontSize: 12,
                color: appStore.isDark ? '#ffffff' : '#000000'
              }
            },
            {
              type: 'min',
              name: '最小值',
              label: {
                color: appStore.isDark ? '#ffffff' : '#000000'
              }
            }
          ]
        },
        markLine: {
          data: [{ type: 'average', name: '平均值' }]
        }
      };
      chartOption.series.push(serie);
    }
    console.log(chartOption.series);
    refChart.value?.clear();
    // resizeChart()
    nextTick(() => {
      if (agriEcoDev.value) {
        erd.listenTo(agriEcoDev.value, () => {
          state.chartOption = chartOption;
          resizeChart();
        });
      }
    });
  });
};

watch(
  () => state.activeName,
  () => {
    if (state.activeName === 'echarts') {
      refuseChart();
    }
  }
);

onMounted(async () => {
  const treevalue = await getStationTree('水源地');
  TreeData.data = treevalue;
  TreeData.currentProject = getFormatTreeNodeDeepestChild(TreeData.data);
  cardSearchConfig.defaultParams = {
    ...cardSearchConfig.defaultParams,
    treeData: [TreeData.currentProject.id]
  };
  cardSearch.value?.resetForm();
  TreeData.checkedKeys = [TreeData.currentProject.id];
  TreeData.checkedNodes?.push(TreeData.currentProject.id);
  refreshData();
});

onUnmounted(async () => {
  // erd.uninstall(agriEcoDev.value);
});
</script>

<style lang="scss" scoped>
.card-table {
  height: calc(100vh - 250px);
  width: 100%;
}

.chart-box {
  width: 100%;
  height: calc(100vh - 250px);
}
</style>
