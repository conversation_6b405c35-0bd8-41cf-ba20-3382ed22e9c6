<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartOperation.construction.project.SoDeviceItemMapper">
    <sql id="Base_Column_List">
        id,
        scope,
        identifier,
        serial_id,
        amount,
        tenant_id
    </sql>
    <sql id="Project_Column_List">
        <!--@formatter:off-->
        <!--@sql select -->
        item.id,
        item.scope,
        item.identifier,
        item.serial_id,
        device.name device_name,
        so_device_type_get_top_type_serial_id(device.type_serial_id, device.tenant_id)   device_top_type_serial_id,
        top_type_serial_id,
        top_type_name,
        type_serial_id                                                         device_type_serial_id,
        type_map.type_name                                                     device_type,
        type_map.type_name                                                     device_type_name,
        top_type_serial_id                                                     device_top_type_serial_id,
        top_type_name                                                          device_top_type_name,
        device.model,
        device.mark,
        device.unit,
        amount,
        <if test="scope != null">
            <choose>
                <!--查询项目设备余量-->
                <when test="scope.isProject">
                    <!--@ignoreSql-->
                    (select amount -
                    (select if(sum(inneritem.amount) is null, 0, sum(inneritem.amount)::integer) from so_device_item inneritem
                    where inneritem.tenant_id = #{tenantId} and
                    inneritem.serial_id = item.serial_id and
                    inneritem.identifier in
                    (select code from so_construction construction
                    where construction.project_code = #{identifier}
                    and construction.tenant_id = #{tenantId})
                    )
                    ) rest,
                </when>
                <!--查询工程设备余量-->
                <when test="scope.isConstruction">
                    <!--@ignoreSql-->
                    (select amount -
                    (select if(sum(inneritem.amount) is null, 0, sum(inneritem.amount)::integer) from so_device_item inneritem
                    where inneritem.tenant_id = #{tenantId} and
                    inneritem.serial_id = item.serial_id and
                    inneritem.identifier in
                    (select code from so_construction_contract contract
                    where contract.construction_code = #{identifier}
                    and contract.tenant_id = #{tenantId})
                    )
                    ) rest,
                </when>
                <!--查询合同设备余量-->
                <when test="scope.isConstructionContract">
                    <!--@ignoreSql-->
                    (select amount -
                    (select if(sum(inneritem.amount) is null, 0, sum(inneritem.amount)::integer) from so_device_item inneritem
                    where inneritem.tenant_id = #{tenantId} and
                    inneritem.serial_id = item.serial_id and
                    inneritem.identifier in
                    (select code from so_construction_apply apply
                    where apply.contract_code = #{identifier}
                    and apply.tenant_id = #{tenantId})
                    )
                    ) rest,
                </when>
                <!--查询合同实施设备数量-->
                <otherwise>
                    amount as rest,
                </otherwise>
            </choose>
        </if>
        <!--project_code-->
        case
        when scope = #{projectScopeIdentifier} then
            item.identifier
        when scope = #{constructionScopeIdentifier} then
            (select project_code from so_construction where tenant_id = item.tenant_id and code = item.identifier)
        when scope = #{contractScopeIdentifier} then
            (select project_code from so_construction where tenant_id = item.tenant_id and code =
                    (select construction_code from so_construction_contract where tenant_id = item.tenant_id and code =
                        item.identifier))
        when scope = #{applyScopeIdentifier} then
            (select project_code from so_construction where tenant_id = item.tenant_id and code =
                    (select construction_code from so_construction_contract where tenant_id = item.tenant_id and code =
                        (select contract_code from so_construction_apply where tenant_id = item.tenant_id and code =
                            item.identifier)))
        end project_code,
        <!--construction_code-->
        <choose>
            <when test="withCode">
                case
                when scope = #{projectScopeIdentifier} then
                    null
                when scope = #{constructionScopeIdentifier} then
                    item.identifier
                when scope = #{contractScopeIdentifier} then
                    (select construction_code from so_construction_contract where tenant_id = item.tenant_id and code =
                            item.identifier)
                when scope = #{applyScopeIdentifier} then
                    (select construction_code from so_construction_contract where tenant_id = item.tenant_id and code =
                                (select contract_code from so_construction_apply where tenant_id = item.tenant_id and code =
                                    item.identifier))
                end construction_code,
                <!--contract_code-->
                case
                when scope = #{projectScopeIdentifier} then
                    null
                when scope = #{constructionScopeIdentifier} then
                    null
                when scope = #{contractScopeIdentifier} then
                    item.identifier
                when scope = #{applyScopeIdentifier} then
                    (select contract_code from so_construction_apply where tenant_id = item.tenant_id and code =
                            item.identifier)
                end contract_code,
                <!--apply_code-->
                case
                when scope = #{projectScopeIdentifier} then
                    null
                when scope = #{constructionScopeIdentifier} then
                    null
                when scope = #{contractScopeIdentifier} then
                    null
                when scope = #{applyScopeIdentifier} then
                    item.identifier
                end apply_code,
            </when>
            <otherwise>
                <!--@ignoreSql-->
                null as construction_code,
                null as contract_code,
                null as apply_code,
            </otherwise>
        </choose>
        item.tenant_id
        <!--@sql from so_device_item item, so_device device, so_device_type_map type_map -->
        <!--@formatter:on-->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.smartOperation.device.SoDeviceItem">
        <result column="id" property="id"/>
        <result column="scope" property="scope"/>
        <result column="identifier" property="identifier"/>
        <result column="serial_id" property="serialId"/>
        <result column="device_name" property="deviceName"/>
        <result column="device_top_type_serial_id" property="deviceTopTypeSerialId"/>
        <result column="device_top_type_name" property="deviceTopTypeName"/>
        <result column="device_type_serial_id" property="deviceTypeSerialId"/>
        <result column="device_type_name" property="deviceTypeName"/>
        <result column="device_type" property="deviceType"/>
        <result column="model" property="model"/>
        <result column="mark" property="mark"/>
        <result column="unit" property="unit"/>
        <result column="amount" property="amount"/>
        <result column="rest" property="rest"/>
        <result column="project_code" property="projectCode"/>
        <result column="construction_code" property="constructionCode"/>
        <result column="contract_code" property="contractCode"/>
        <result column="apply_code" property="applyCode"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Project_Column_List"/>
        from so_device_item item
                 left join so_device device
                           on item.serial_id = device.serial_id and item.tenant_id = device.tenant_id
                 left join so_device_type_map type_map
                           on device.serial_id = current_type_serial_id
        <where>
            <if test="scope != null">
                and scope = #{scope}
            </if>
            <if test="identifier != null and identifier != ''">
                and identifier = #{identifier}
            </if>
            <if test="serialId != null and serialId != ''">
                and item.serial_id like '%' || #{serialId} || '%'
            </if>
            <if test="name != null and name != ''">
                and device.name like '%' || #{name} || '%'
            </if>
            <if test="model != null and model != ''">
                and device.model like '%' || #{model} || '%'
            </if>
            and item.tenant_id = #{tenantId}
        </where>
    </select>

    <update id="update">
        update so_device_item
        <set>
            <if test="amount != null">
                amount = #{amount},
            </if>
        </set>
        where id = #{id}
    </update>

    <update id="updateFully">
        update so_device_item
        set amount = #{amount}
        where id = #{id}
    </update>


    <update id="updateAllFully">
        update so_device_item
        set amount = so_device_item.amount +  valueTable.amount
        FROM (
        VALUES
        <foreach collection="list" item="element" separator=",">
            (#{element.scope},
             #{element.identifier},
             #{element.serialId},
             #{element.amount},
             #{element.tenantId})
        </foreach>
        ) as valueTable(scope, identifier, serialId, amount, tenantId)
        where so_device_item.scope = valueTable.scope
          and so_device_item.identifier = valueTable.identifier
          and so_device_item.serial_id = valueTable.serialId
          and so_device_item.tenant_id = valueTable.tenantId
    </update>

    <insert id="saveAll">
        <bind name="maxValue" value="@java.lang.Integer@MAX_VALUE"/>
        INSERT INTO so_device_item(id,
                                   scope,
                                   identifier,
                                   serial_id,
                                   amount,
                                   tenant_id)VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (#{element.id},
             #{element.scope},
             #{element.identifier},
             #{element.serialId},
             #{element.amount},
             #{element.tenantId})
        </foreach>
        on conflict (scope, identifier, serial_id, tenant_id) do
        update set amount = so_device_item.amount + excluded.amount
    </insert>
    <!--@formatter:off-->
    <delete id="remove">
        delete
        from so_device_item item
        where id = #{id}
    </delete>
    <!--@formatter:on-->
    <select id="canBeDelete" resultType="boolean">
        <bind name="projectScope"
              value="@org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemScope@SO_PROJECT"/>
        <bind name="constructionScope"
              value="@org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemScope@SO_CONSTRUCTION"/>
        <bind name="contractScope"
              value="@org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemScope@SO_CONSTRUCTION_CONTRACT"/>
        <bind name="applyScope"
              value="@org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemScope@SO_CONSTRUCTION_APPLY"/>

        select case
                   when scope = #{projectScope} then
                       (select count(1) = 0
                        from so_project main
                                 join so_construction sub
                                      on sub.project_code = main.code
                                 join so_device_item iitem
                                      on sub.code = iitem.identifier and sub.tenant_id = iitem.tenant_id
                        where main.code = item.identifier
                          and main.tenant_id = item.tenant_id)
                   when scope = #{constructionScope} then
                       (select count(1) = 0
                        from so_construction main
                                 join so_construction_contract sub
                                      on sub.construction_code = main.code
                                 join so_device_item iitem
                                      on sub.code = iitem.identifier and sub.tenant_id = iitem.tenant_id
                        where main.code = item.identifier
                          and main.tenant_id = item.tenant_id)
                   when scope = #{contractScope} then
                       (select count(1) = 0
                        from so_construction_contract main
                                 join so_construction_apply sub
                                      on sub.construction_code = main.code
                                 join so_device_item iitem
                                      on sub.code = iitem.identifier and sub.tenant_id = iitem.tenant_id
                        where main.code = item.identifier
                          and main.tenant_id = item.tenant_id)
                   when scope = #{applyScope} then
                       true
                   else
                       false
                   end
        from so_device_item item
        where id = #{id}
    </select>

    <insert id="save">
        INSERT INTO so_device_item(id,
                                   scope,
                                   identifier,
                                   serial_id,
                                   amount,
                                   tenant_id)
        VALUES (#{id},
                #{scope},
                #{identifier},
                #{serialId},
                #{amount},
                #{tenantId})
    </insert>

    <select id="selectBatchMaxRestCount"
            resultType="org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoDeviceItemSaveRequest">
        <bind name="projectScope"
              value="@org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemScope@SO_PROJECT"/>
        <bind name="constructionScope"
              value="@org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemScope@SO_CONSTRUCTION"/>
        <bind name="contractScope"
              value="@org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemScope@SO_CONSTRUCTION_CONTRACT"/>
        <bind name="applyScope"
              value="@org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemScope@SO_CONSTRUCTION_APPLY"/>
        select v_identifier identifier,
               v_serial_id  serialId,
        <choose>
            <!--查询工程可用最大设备量-->
            <when test="scope.isConstruction">
                    (select amount
                     from so_project
                              left join so_construction
                                        on so_project.code = so_construction.project_code and
                                           so_project.tenant_id = so_construction.tenant_id
                              left join so_device_item item
                                        on item.identifier = so_project.code
                     where so_construction.code = v_identifier
                       and item.serial_id = v_serial_id
                       and item.tenant_id = v_tenant_id
                       and item.scope = #{projectScope})
                    -
                    (select coalesce(sum(inneritem.amount), 0)
                     from so_device_item inneritem
                     where inneritem.tenant_id = v_tenant_id
                       and inneritem.serial_id = v_serial_id
                       and inneritem.scope = #{constructionScope}
                       and inneritem.identifier in
                           (select code
                            from so_construction construction
                            where construction.project_code = (select project_code
                                                               from so_construction construction
                                                               where construction.code = v_identifier
                                                                 and construction.tenant_id = v_tenant_id)
                              and construction.tenant_id = v_tenant_id)) amount
            </when>
            <!--查询合同可用最大设备量-->
            <when test="scope.isConstructionContract">
                <!--@ignoreSql-->
                    (select coalesce(amount, 0)
                     from so_construction
                              left join so_construction_contract
                                        on so_construction.code = so_construction_contract.construction_code and
                                           so_construction.tenant_id = so_construction_contract.tenant_id
                              left join so_device_item item
                                        on item.identifier = so_construction.code
                     where so_construction_contract.code = v_identifier
                       and item.serial_id = v_serial_id
                       and item.tenant_id = v_tenant_id
                       and item.scope = #{constructionScope})
                    -
                    (select coalesce(sum(inneritem.amount), 0)
                     from so_device_item inneritem
                     where inneritem.tenant_id = v_tenant_id
                       and inneritem.serial_id = v_serial_id
                       and inneritem.scope = #{contractScope}
                       and inneritem.identifier in
                           (select code
                            from so_construction_contract contract
                            where contract.construction_code = (select construction_code
                                                                from so_construction_contract contract
                                                                where contract.code = v_identifier
                                                                  and contract.tenant_id = v_tenant_id)
                              and contract.tenant_id = v_tenant_id)) amount
            </when>
            <!--查询实施可用最大设备量-->
            <when test="scope.isConstructionApply">
                <!--@ignoreSql-->
                    (select coalesce(amount, 0)
                     from so_construction_contract
                              left join so_construction_apply
                                        on so_construction_contract.code = so_construction_apply.contract_code
                                            and so_construction_contract.construction_code =
                                                so_construction_apply.construction_code
                                            and so_construction_contract.tenant_id = so_construction_apply.tenant_id
                              left join so_device_item item
                                        on item.identifier = so_construction_contract.code
                     where so_construction_apply.code = v_identifier
                       and item.serial_id = v_serial_id
                       and item.tenant_id = v_tenant_id
                       and item.scope = #{contractScope})
                    -
                    (select coalesce(sum(inneritem.amount), 0)
                     from so_device_item inneritem
                     where inneritem.tenant_id = v_tenant_id
                       and inneritem.serial_id = v_serial_id
                       and inneritem.scope = #{applyScope}
                       and inneritem.identifier in
                           (select code
                            from so_construction_apply apply
                            where (apply.construction_code, apply.contract_code) =
                                  (select construction_code, contract_code
                                   from so_construction_apply apply
                                   where apply.code = v_identifier
                                     and apply.tenant_id = v_tenant_id)
                              and apply.tenant_id = v_tenant_id)) amount
            </when>
        </choose>
        from (values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.identifier}, #{item.serialId}, #{item.tenantId})
        </foreach>
        ) info_table(v_identifier, v_serial_id, v_tenant_id)
    </select>
</mapper>