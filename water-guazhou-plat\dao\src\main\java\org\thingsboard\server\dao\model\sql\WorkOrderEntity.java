package org.thingsboard.server.dao.model.sql;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;
import java.util.Date;
import java.util.List;

@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.WORK_ORDER_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class WorkOrderEntity {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.WORK_ORDER_TYPE)
    private String type;

    @Column(name = ModelConstants.WORK_ORDER_CODE)
    private String code;

    @Column(name = ModelConstants.NAME)
    private String name;

    @Column(name = ModelConstants.WORK_ORDER_QUESTION_REMARK)
    private String questionRemark;

    @Column(name = ModelConstants.WORK_ORDER_QUESTION_FILE)
    private String questionFile;

    @Column(name = ModelConstants.WORK_ORDER_STATUS)
    private String status;

    @Column(name = ModelConstants.WORK_ORDER_IS_OUTSIDER)
    private String isOutsider;

    @Column(name = ModelConstants.WORK_ORDER_EXECUTOR)
    private String executor;

    @Column(name = ModelConstants.CREATOR_PROPERTY)
    private String creator;

    @Column(name = ModelConstants.CREATE_TIME)
    private Date createTime;

    @Column(name = ModelConstants.WORK_ORDER_DEADLINE_TIME)
    private Date deadlineTime;

    @Column(name = ModelConstants.WORK_ORDER_REMARK)
    private String remark;

    @Column(name = ModelConstants.WORK_ORDER_IS_END)
    private String isEnd;

    @Column(name = ModelConstants.WORK_ORDER_REAL_END_TIME)
    private Date realEndTime;

    @Column(name = ModelConstants.WORK_ORDER_REAL_START_TIME)
    private Date realStartTime;

    @Column(name = ModelConstants.WORK_ORDER_PROCESS_IMGS)
    private String processImgs;

    @Column(name = ModelConstants.WORK_ORDER_PROCESS_VOICE_FILE)
    private String processVoiceFile;

    @Column(name = ModelConstants.WORK_ORDER_ACCEPTANCE_TIME)
    private Date acceptanceTime;

    @Column(name = ModelConstants.WORK_ORDER_ACCEPTANCE_REMARK)
    private String acceptanceRemark;

    @Column(name = ModelConstants.WORK_ORDER_ACCEPTANCE_IMGS)
    private String acceptanceImgs;

    @Column(name = ModelConstants.WORK_ORDER_ACCEPTANCE_VOICE_FILE)
    private String acceptanceVoiceFile;

    @Column(name = ModelConstants.WORK_ORDER_PRIORITY)
    private String priority;

    @Column(name = ModelConstants.WORK_ORDER_CONTENT_ID)
    private String contentId;

    @Column(name = ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;

    @Transient
    private Object contentDetail;

    @Transient
    private String executorName;

    @Transient
    private String creatorName;

    @Transient
    private boolean follow;
}
