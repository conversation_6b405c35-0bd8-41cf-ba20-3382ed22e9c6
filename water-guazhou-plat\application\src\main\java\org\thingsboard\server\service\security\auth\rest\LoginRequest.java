/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.service.security.auth.rest;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;

public class LoginRequest {
    private String username;
    private String password;
/*    private String verifyCode;
    private String requestId;*/
    private String serialNo;

    @JsonCreator
    public LoginRequest(@JsonProperty("username") String username, @JsonProperty("password") String password,/*
                        @JsonProperty("verifyCode") String verifyCode, @JsonProperty("requestId") String requestId*/
                        @JsonProperty("serialNo") String serialNo) {
        this.username = username;
        this.password = password;
        /*this.verifyCode = verifyCode;
        this.requestId = requestId;*/
        this.serialNo = serialNo;
    }

    public String getUsername() {
        return username;
    }

    public String getPassword() {
        return password;
    }
/*
    public String getVerifyCode() {
        return verifyCode;
    }

    public String getRequestId() {
        return requestId;
    }*/

    public String getSerialNo() {
        return serialNo;
    }
}
