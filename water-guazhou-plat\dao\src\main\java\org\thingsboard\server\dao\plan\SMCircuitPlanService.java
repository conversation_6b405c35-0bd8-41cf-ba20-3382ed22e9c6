package org.thingsboard.server.dao.plan;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.smartManagement.plan.SMCircuitPlan;
import org.thingsboard.server.dao.model.sql.smartManagement.plan.SMCircuitPlanResponse;
import org.thingsboard.server.dao.model.sql.smartManagement.plan.SMCircuitTaskPlanRequest;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.plan.SMCircuitPlanPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.plan.SMCircuitPlanSaveRequest;

import java.util.Date;
import java.util.List;

public interface SMCircuitPlanService {
    /**
     * 分页条件查询巡检计划
     *
     * @param request 分页查询条件
     * @return 分页查询结果
     */
    IPage<SMCircuitPlanResponse> findAllConditional(SMCircuitPlanPageRequest request);

    SMCircuitPlan save(SMCircuitPlanSaveRequest entity);

    /**
     * 增量修改
     *
     * @param entity 实体信息
     * @return 是否修改成功
     */
    boolean update(SMCircuitPlan entity);

    /**
     * 删除
     *
     * @param id 唯一标识
     * @return 是否删除成功
     */
    boolean delete(String id);

    /**
     * 批量删除
     *
     * @param idList id列表
     * @return 是否成功
     */
    boolean deleteAll(List<String> idList);

    /**
     * 计划任务
     *
     * @param req 计划任务请求
     * @return 创建的任务条数
     */
    int plan(SMCircuitTaskPlanRequest req);

    /**
     * 此段时间内计划是否已被安排
     *
     * @param planId    计划id
     * @param beginTime 计划开始时间
     * @param endTime   计划结束时间
     * @return 是否成功
     */
    boolean isPlanArranged(String planId, Date beginTime, Date endTime);

}
