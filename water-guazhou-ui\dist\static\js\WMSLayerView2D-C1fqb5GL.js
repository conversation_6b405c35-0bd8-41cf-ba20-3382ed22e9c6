import{e as o,y as n,a as b,s as g,j as F,i as M}from"./Point-WxyopZva.js";import{aW as S}from"./index-r0dFAfgr.js";import{l as E}from"./widget-BcWKanF2.js";import{j as R,w as q}from"./MapView-DaoQedLH.js";import{a as I}from"./BitmapContainer-ziwQ7v9F.js";import{f as U,u as V}from"./LayerView-BSt9B8Gh.js";import{v as W}from"./ExportStrategy-BadISnDs.js";import{i as j}from"./RefreshableLayerView-DUeNHzrW.js";import{l as L}from"./ExportWMSImageParameters-CGwvCiFd.js";import"./pe-B8dP0-Ut.js";import"./WGLContainer-Dyx9110G.js";import"./definitions-826PWLuy.js";import"./FramebufferObject-8j9PRuxE.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./VertexElementDescriptor-BOD-G50G.js";import"./vec4f32-CjrfB-0a.js";import"./color-DAS1c3my.js";import"./enums-B5k73o5q.js";import"./enums-L38xj_2E.js";import"./number-CoJp78Rz.js";import"./ProgramTemplate-tdUBoAol.js";import"./MaterialKey-BYd7cMLJ.js";import"./alignmentUtils-CkNI7z7C.js";import"./utils-DPUVnAXL.js";import"./StyleDefinition-Bnnz5uyC.js";import"./config-MDUrh2eL.js";import"./GeometryUtils-BRRfazic.js";import"./Container-BwXq1a-x.js";import"./earcut-BJup91r2.js";import"./Bitmap-CraE42_6.js";const H=e=>{let t=class extends e{initialize(){this.exportImageParameters=new L({layer:this.layer})}destroy(){this.exportImageParameters=S(this.exportImageParameters)}get exportImageVersion(){var r;return(r=this.exportImageParameters)==null||r.commitProperty("version"),this.commitProperty("timeExtent"),(this._get("exportImageVersion")||0)+1}fetchPopupFeatures(r){const{layer:a}=this;if(!r)return Promise.reject(new g("wmslayerview:fetchPopupFeatures","Nothing to fetch without area",{layer:a}));const{popupEnabled:p}=a;if(!p)return Promise.reject(new g("wmslayerview:fetchPopupFeatures","popupEnabled should be true",{popupEnabled:p}));const u=this.createFetchPopupFeaturesQuery(r);if(!u)return Promise.resolve([]);const{extent:i,width:s,height:m,x:d,y:c}=u;if(!(i&&s&&m))throw new g("wmslayerview:fetchPopupFeatures","WMSLayer does not support fetching features.",{extent:i,width:s,height:m});return a.fetchFeatureInfo(i,s,m,d,c)}};return o([n()],t.prototype,"exportImageParameters",void 0),o([n({readOnly:!0})],t.prototype,"exportImageVersion",null),o([n()],t.prototype,"layer",void 0),o([n(R)],t.prototype,"timeExtent",void 0),t=o([b("esri.layers.mixins.WMSLayerView")],t),t};let h=class extends H(j(U(V))){constructor(){super(...arguments),this.bitmapContainer=new I}supportsSpatialReference(e){return this.layer.serviceSupportsSpatialReference(e)}update(e){this.strategy.update(e).catch(t=>{F(t)||M.getLogger(this.declaredClass).error(t)})}attach(){const{layer:e}=this,{imageMaxHeight:t,imageMaxWidth:r}=e;this.bitmapContainer=new I,this.container.addChild(this.bitmapContainer),this.strategy=new W({container:this.bitmapContainer,fetchSource:this.fetchImage.bind(this),requestUpdate:this.requestUpdate.bind(this),imageMaxHeight:t,imageMaxWidth:r,imageRotationSupported:!1,imageNormalizationSupported:!1,hidpi:!1}),this.addAttachHandles(E(()=>this.exportImageVersion,()=>this.requestUpdate()))}detach(){this.strategy=S(this.strategy),this.container.removeAllChildren()}moveStart(){}viewChange(){}moveEnd(){this.requestUpdate()}createFetchPopupFeaturesQuery(e){const{view:t,bitmapContainer:r}=this,{x:a,y:p}=e,{spatialReference:u}=t;let i,s=0,m=0;if(r.children.some(C=>{const{width:f,height:x,resolution:w,x:l,y}=C,v=l+w*f,P=y-w*x;return a>=l&&a<=v&&p<=y&&p>=P&&(i=new q({xmin:l,ymin:P,xmax:v,ymax:y,spatialReference:u}),s=f,m=x,!0)}),!i)return null;const d=i.width/s,c=Math.round((a-i.xmin)/d),$=Math.round((i.ymax-p)/d);return{extent:i,width:s,height:m,x:c,y:$}}async doRefresh(){this.requestUpdate()}isUpdating(){return this.strategy.updating||this.updateRequested}fetchImage(e,t,r,a){return this.layer.fetchImageBitmap(e,t,r,{timeExtent:this.timeExtent,...a})}};o([n()],h.prototype,"strategy",void 0),o([n()],h.prototype,"updating",void 0),h=o([b("esri.views.2d.layers.WMSLayerView2D")],h);const yt=h;export{yt as default};
