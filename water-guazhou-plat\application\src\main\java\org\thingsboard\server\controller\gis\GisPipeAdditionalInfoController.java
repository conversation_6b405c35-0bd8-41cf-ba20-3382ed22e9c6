package org.thingsboard.server.controller.gis;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.gis.GisPipeAdditionalInfoService;
import org.thingsboard.server.dao.model.sql.gis.GisPipeAdditionalInfo;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

/**
 * Gis附加信息
 */
@RestController
@RequestMapping("api/gis/pipeAdditionalInfo")
public class GisPipeAdditionalInfoController extends BaseController {

    @Autowired
    private GisPipeAdditionalInfoService gisPipeAdditionalInfoService;

    @GetMapping("{layerid}/{objectid}")
    public IstarResponse get(@PathVariable String layerid, @PathVariable String objectid) throws ThingsboardException {
        return IstarResponse.ok(gisPipeAdditionalInfoService.findOne(layerid, objectid, getTenantId()));
    }

    @PostMapping("save")
    public IstarResponse save(@RequestBody GisPipeAdditionalInfo entity) throws ThingsboardException {
        entity.setTenantId(UUIDConverter.fromTimeUUID(getTenantId().getId()));
        gisPipeAdditionalInfoService.save(entity);

        return IstarResponse.ok();
    }


}
