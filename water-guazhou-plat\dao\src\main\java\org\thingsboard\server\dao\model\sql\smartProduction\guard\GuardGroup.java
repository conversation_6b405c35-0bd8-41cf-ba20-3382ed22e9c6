package org.thingsboard.server.dao.model.sql.smartProduction.guard;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseUsername;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.util.Date;

@Getter
@Setter
@ResponseEntity
@TableName("guard_group")
public class GuardGroup {
    // 班组id
    private String id;

    // 地点id
    private String placeId;

    // 地点名称
    @TableField(exist = false)
    private String placeName;

    // 班组名称
    private String name;

    // 班组编号
    private Integer serialNo;

    // 班组id
    private String groupId;

    // 班组名称
    private String groupName;

    // 班组部门id
    private String departmentId;

    // 班组部门名称
    @TableField(exist = false)
    private String departmentName;

    @ParseUsername
    // 值班长id
    private String head;

    // 创建人id
    @ParseUsername
    private String creator;

    // 创建时间
    private Date createTime;

    // 租户id
    private String tenantId;

    // 用、隔开的成员名称
    @TableField(exist = false)
    private String partnerNames;


}
