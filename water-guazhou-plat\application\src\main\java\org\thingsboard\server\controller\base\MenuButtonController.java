package org.thingsboard.server.controller.base;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.User;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.security.Authority;
import org.thingsboard.server.dao.menu.MenuButtonService;
import org.thingsboard.server.dao.model.sql.MenuButtonEntity;
import org.thingsboard.server.service.security.model.SecurityUser;

import java.util.List;

@RestController
@RequestMapping("api/menuButton")
public class MenuButtonController extends BaseController {

    @Autowired
    private MenuButtonService menuButtonService;

    @GetMapping("list/{menuId}")
    public List<MenuButtonEntity> findList(@PathVariable String menuId) {
        return menuButtonService.findList(menuId);
    }

    @GetMapping("tree")
    public Object tree() throws ThingsboardException {
        // 获取当前用户信息
        User currentUser = getCurrentUser();
        return menuButtonService.tree(currentUser);
    }

    @PostMapping("save")
    public MenuButtonEntity save(@RequestBody MenuButtonEntity buttonEntity) {
        return menuButtonService.save(buttonEntity);
    }

    @DeleteMapping
    public void delete(@RequestBody List<String> ids) {
        menuButtonService.deleteBatch(ids);
    }

    @PostMapping("role/setMenuButtonList/{roleId}")
    public void setMenuButtonList(@PathVariable String roleId,
                                  @RequestBody List<String> menuButtonIdList) {
        menuButtonService.setMenuButtonList(roleId, menuButtonIdList);
    }

    @GetMapping("getCurrentUserMenuButtonList")
    public List<MenuButtonEntity> getCurrentUserMenuButtonList() throws ThingsboardException {
        SecurityUser currentUser = getCurrentUser();

        return menuButtonService.getCurrentUserMenuButtonList(currentUser.getId(), currentUser.getAuthority());
    }

    @GetMapping("getRoleButtonList/{roleId}")
    public List<MenuButtonEntity> getRoleButtonList(@PathVariable String roleId) throws ThingsboardException {
        return menuButtonService.getRoleButtonList(roleId);
    }

}
