<!-- gis视频监控 -->
<template>
  <div class="onemap-panel-wrapper">
    <Form
      ref="refForm"
      :config="FormConfig"
    >
    </Form>
  </div>
</template>
<script lang="ts" setup>
const emit = defineEmits(['highlightMark', 'addMarks'])
const props = defineProps<{
  view?: __esri.MapView
  menu: IMenuItem
}>()

const FormConfig = reactive<IFormConfig>({
  group: [
    {
      fields: [
        {
          type: 'input',
          field: 'layer',
          append: '刷新'
        },
        {
          type: 'tree',
          checkStrictly: true,
          options: [
            // {
            //   label: 'cs',
            //   value: '3434',
            //   children: [{ label: 'cs1', value: '343422' }]
            // }
          ],
          nodeClick: data => {
            emit('highlightMark', props.menu, data?.value)
          }
        }
      ]
    }
  ],
  labelPosition: 'top',
  gutter: 12
})
const refreshData = () => {
  //
}
onMounted(() => {
  refreshData()
})
</script>

<style lang="scss" scoped>
.onemap-panel-wrapper {
  min-height: 610px;
  height: 100%;
}
</style>
