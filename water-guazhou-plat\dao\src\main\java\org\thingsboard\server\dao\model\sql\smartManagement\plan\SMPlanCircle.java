package org.thingsboard.server.dao.model.sql.smartManagement.plan;

import org.joda.time.DateTime;
import org.thingsboard.server.dao.util.TimeUtils;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;

import java.util.*;

public enum SMPlanCircle {
    TWICE_A_DAY("一天两次", 0) {
        @Override
        protected <T> List<T> generate(DateTime from, DateTime to, QueryUtil.NativeSequenceSupplier<T> supplier) {
            // return QueryUtil.generateSequence(from, to, 12, HOURS, supplier);
            return QueryUtil.generateSequence(from, to, dateTime -> {
                return dateTime.plusHours(12).minusMillis(1);
            }, dateTime -> {
                return dateTime.plusMillis(1);
            }, supplier);
        }
    }, ONCE_A_DAY("一天一次", 1) {
        @Override
        protected <T> List<T> generate(DateTime from, DateTime to, QueryUtil.NativeSequenceSupplier<T> supplier) {
            // return QueryUtil.generateSequence(from, to, 1, DAYS, supplier);
            return QueryUtil.generateSequence(from, to, dateTime -> {
                return dateTime.plusDays(1).minusMillis(1);
            }, dateTime -> {
                return dateTime.plusMillis(1);
            }, supplier);
        }
    }, ONCE_A_DAY_SKIP_A_DAY("隔日一次", 2) {
        @Override
        protected <T> List<T> generate(DateTime from, DateTime to, QueryUtil.NativeSequenceSupplier<T> supplier) {
            // return QueryUtil.generateSequence(from, to, 1, DAYS, 1, DAYS, supplier);
            return QueryUtil.generateSequence(from, to, dateTime -> {
                return dateTime.plusDays(2).minusMillis(1);
            }, dateTime -> {
                return dateTime.plusMillis(1);
            }, supplier);
        }
    }, ONCE_A_DAY_SKIP_Three_DAY("三天一次", 3) {
        @Override
        protected <T> List<T> generate(DateTime from, DateTime to, QueryUtil.NativeSequenceSupplier<T> supplier) {
            // return QueryUtil.generateSequence(from, to, 1, DAYS, 2, DAYS, supplier);

            return QueryUtil.generateSequence(from, to, dateTime -> {
                return dateTime.plusDays(3).minusMillis(1);
            }, dateTime -> {
                return dateTime.plusMillis(1);
            }, supplier);
        }
    }, ONCE_A_DAY_SKIP_A_WEEK("一周一次", 4) {
        @Override
        protected <T> List<T> generate(DateTime from, DateTime to, QueryUtil.NativeSequenceSupplier<T> supplier) {
            // return QueryUtil.generateSequence(from, to, 1, DAYS, 6, DAYS, supplier);
            return QueryUtil.generateSequence(from, to, dateTime -> {
                return dateTime.plusDays(7).minusMillis(1);
            }, dateTime -> {
                return dateTime.plusMillis(1);
            }, supplier);
        }
    }, ONCE_A_DAY_SKIP_A_WEEK_TWO("一周二次", 11) {
        @Override
        protected <T> List<T> generate(DateTime from, DateTime to, QueryUtil.NativeSequenceSupplier<T> supplier) {
            // return QueryUtil.generateSequence(from, to, 1, DAYS, 6, DAYS, supplier);
            return QueryUtil.generateSequence(from, to, dateTime -> {
                return dateTime.plusHours(84).minusMillis(1);
            }, dateTime -> {
                return dateTime.plusMillis(1);
            }, supplier);
        }
    }, ONCE_A_DAY_SKIP_HALF_MONTH("半月一次", 5) {
        @Override
        protected <T> List<T> generate(DateTime from, DateTime to, QueryUtil.NativeSequenceSupplier<T> supplier) {
            return QueryUtil.generateSequence(from, to, dateTime -> {
                if (dateTime.getDayOfMonth() > 2) {
                    return dateTime.dayOfMonth().withMaximumValue().minusMillis(1);
                }
                //                       修改当月的day     调整到最后一天         获取日数
                int dayAmount = dateTime.dayOfMonth().withMaximumValue().getDayOfMonth();
                return dateTime.plus(dayAmount / 2).minus(1);
            }, dateTime -> {
                return dateTime.plusMillis(1);
            }, supplier);
        }
    }, ONCE_A_MONTH("一月一次", 6) {
        @Override
        protected <T> List<T> generate(DateTime from, DateTime to, QueryUtil.NativeSequenceSupplier<T> supplier) {
            from = from.dayOfMonth().get() != 1 ?
                    from.plusMonths(1).dayOfMonth().withMinimumValue() : from.dayOfMonth().withMinimumValue();
            return QueryUtil.generateSequence(from, to, dateTime -> {
                return dateTime.dayOfMonth().withMaximumValue().minusMillis(1);
            }, dateTime -> {
                return dateTime.plusMillis(1);
            }, supplier);
        }
    }, ONCE_A_SEASON("一季一次", 7) {
        @Override
        protected <T> List<T> generate(DateTime from, DateTime to, QueryUtil.NativeSequenceSupplier<T> supplier) {
            from = TimeUtils.ceilSeason(from);
            return QueryUtil.generateSequence(from, to, dateTime -> {
                return dateTime.plusMonths(3).dayOfMonth().withMinimumValue().minusMillis(1);
            }, dateTime -> {
                return dateTime.plusMillis(1);
            }, supplier);
        }
    }, ONCE_A_YEAR("一年一次", 8) {
        @Override
        protected <T> List<T> generate(DateTime from, DateTime to, QueryUtil.NativeSequenceSupplier<T> supplier) {
            from = from.monthOfYear().withMinimumValue();
            return QueryUtil.generateSequence(from, to, dateTime -> {
                return dateTime.plusYears(1).minusMillis(1);
            }, dateTime -> {
                return dateTime.plusMillis(1);
            }, supplier);
        }
    }, TWICE_A_YEAR("半年一次", 9) {
        @Override
        protected <T> List<T> generate(DateTime from, DateTime to, QueryUtil.NativeSequenceSupplier<T> supplier) {
            from = from.monthOfYear().withMinimumValue();
            return QueryUtil.generateSequence(from, to, dateTime -> {
                return dateTime.plusMonths(6).minusMillis(1);
            }, dateTime -> {
                return dateTime.plusMillis(1);
            }, supplier);
        }
    }, THIRDTH_A_MONTH("一月三次", 10) {
        @Override
        protected <T> List<T> generate(DateTime from, DateTime to, QueryUtil.NativeSequenceSupplier<T> supplier) {
            return QueryUtil.generateSequence(from, to, dateTime -> {
                int dayAmount = to.dayOfMonth().withMaximumValue().getDayOfMonth();
                return dateTime.plus(dayAmount / 3).minusMillis(1);
            }, dateTime -> {
                return dateTime.plusMillis(1);
            }, supplier);
        }
    };

    private final String name;

    private final int id;

    private static final SMPlanCircle[] planCircles;

    public static final List<String> nameList;


    static {
        planCircles = Arrays.stream(values()).toArray(SMPlanCircle[]::new);
        Arrays.sort(planCircles, Comparator.comparingInt(a -> a.id));

        nameList = Collections.unmodifiableList(Arrays.asList(Arrays.stream(planCircles).map(x -> x.name).toArray(String[]::new)));
    }

    SMPlanCircle(String name, int id) {
        this.name = name;
        this.id = id;
    }

    public static SMPlanCircle indexOf(Integer i) {
        return planCircles[i];
    }

    public <T> List<T> generate(Date from, Date to, QueryUtil.NativeSequenceSupplier<T> supplier) {
        return generate(new DateTime(from).withMillisOfDay(0), new DateTime(to).withHourOfDay(23).withMinuteOfHour(59).withSecondOfMinute(59), supplier);
    }

    protected abstract <T> List<T> generate(DateTime from, DateTime to, QueryUtil.NativeSequenceSupplier<T> supplier);

    public String getName() {
        return name;
    }
}
