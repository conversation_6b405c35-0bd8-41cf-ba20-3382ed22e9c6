package org.thingsboard.server.dao.sql.alarmV2;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.alarmV2.AlarmRuleUser;

import java.util.List;

@Mapper
public interface AlarmRuleUserMapper extends BaseMapper<AlarmRuleUser> {
    void batchInsert(@Param("param") List<AlarmRuleUser> msgList);
}
