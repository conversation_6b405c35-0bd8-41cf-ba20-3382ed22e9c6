package org.thingsboard.server.dao.repair;

import org.thingsboard.server.common.data.User;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.MaintenanceStandardEntity;

import java.util.List;

public interface MaintenanceStandardService {

    MaintenanceStandardEntity findById(String id);

    PageData<MaintenanceStandardEntity> findList(int page, int size, String name, String deviceType, User currentUser);

    MaintenanceStandardEntity save(MaintenanceStandardEntity entity);

    void remove(List<String> ids);

    List<MaintenanceStandardEntity> findAll(String name, TenantId tenantId);

    List<MaintenanceStandardEntity> findAll(String name, String tenantId);

}
