import{d as F,M as N,c as S,r as f,bu as D,j as v,u as w,e0 as K,e1 as L,a8 as k,am as M,D as U,e2 as V,C as G,g as c,h,F as n,p as C,q as t,G as _,n as A,an as B,H as P,I as T,bK as $,aK as z,aL as j,bb as J,K as O,J as q,L as H}from"./index-r0dFAfgr.js";import{c as X}from"./index-CaaU9niG.js";const Q=F({props:{config:{type:Object,default(){return{}}}},setup(e){const{$messageError:a}=N(),i=S(""),r=f({imgActionUrl:"",fileActionUrl:"",uploadFileName:"",headers:{},zoom:11,center:[116.4,39.91],dataForm:{id:null},value:[]}),E=()=>{X().then(o=>{o.status===200&&(r.value=o.data)})};D(()=>{r.imgActionUrl=v().actionUrl+"/file/api/upload/image",r.fileActionUrl=v().actionUrl+"/file/api/upload/file",r.headers["X-Authorization"]="Bearer "+w().token,E()});const p=f({defaultProps:{children:"children",label:"label"},getreturnrouting:()=>{K(e.config.appform.id).then(o=>{if(o.status===200){console.log(i.value,o.data);const s=[];o.data.forEach(m=>{s.push(L(m))}),e.config.type===!1?i.value.setCheckedKeys(s):e.config.visible===!0&&i.value.getCheckedKeys().length>0&&i.value.setCheckedKeys([])}})}}),d=f({visible:k(()=>e.config.visible),title:k(()=>e.config.title),handleClose:()=>{const o={name:"",type:"1",remark:"",orderNum:0,img:"",applicationUrl:"",menuIdList:[],tenantId:""};for(const s in o)l[s]=o[s];e.config.close()}});M(e.config,()=>{l.tenantId=e.config.value.id.id||"";for(const o in e.config.appform)l[o]=e.config.appform[o];p.getreturnrouting()});const l=f({name:"",type:"1",remark:"",orderNum:0,img:"",applicationUrl:"",menuIdList:[],tenantId:""}),g=f({handleUploadSuccess:o=>{console.log(o,"handleUploadSuccess"),l.img=o},beforeAvatarUpload:o=>{const s=o.type==="image/jpeg"||o.type==="image/png",m=o.size/1024/1024<2;return s||a("上传图片只能是 JPG/PNG 格式!"),m||a("上传图片大小不能超过 2MB!"),s&&m}});return{dialog:d,form:l,state:r,image:g,submit:()=>{l.menuIdList=[],l.type==="1"&&i.value.getCheckedKeys().forEach(s=>{l.menuIdList.push(U(s))}),e.config.type===!0?(l.tenantId=U(l.tenantId),V(l).then(o=>{console.log(o),e.config.close()})):V(l).then(o=>{console.log(o),e.config.close()})},menuTree:i,returnrouting:p}}}),R=["src"],W={key:1,class:"el-icon-plus avatar-uploader-icon"},Y={class:"tree"},Z={class:"dialog-footer"};function x(e,a,i,r,E,p){const d=P,l=T,g=$,b=z,o=j,s=J,m=O,y=q,I=H;return c(),h(I,{modelValue:e.dialog.visible,"onUpdate:modelValue":a[5]||(a[5]=u=>e.dialog.visible=u),title:e.dialog.title,width:"35%","before-close":e.dialog.handleClose},{footer:n(()=>[C("span",Z,[t(y,{onClick:e.dialog.handleClose},{default:n(()=>a[6]||(a[6]=[_("取消")])),_:1},8,["onClick"]),t(y,{type:"primary",onClick:e.submit},{default:n(()=>a[7]||(a[7]=[_("确认")])),_:1},8,["onClick"])])]),default:n(()=>[t(m,{model:e.form,"label-position":"top"},{default:n(()=>[t(l,{label:"应用名称"},{default:n(()=>[t(d,{modelValue:e.form.name,"onUpdate:modelValue":a[0]||(a[0]=u=>e.form.name=u)},null,8,["modelValue"])]),_:1}),t(l,{label:"应用介绍"},{default:n(()=>[t(d,{modelValue:e.form.remark,"onUpdate:modelValue":a[1]||(a[1]=u=>e.form.remark=u),type:"textarea"},null,8,["modelValue"])]),_:1}),t(l,{label:"应用序号"},{default:n(()=>[t(d,{modelValue:e.form.orderNum,"onUpdate:modelValue":a[2]||(a[2]=u=>e.form.orderNum=u),onkeyup:"value=value.replace(/[^\\d]/g,'')",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),t(l,{label:"应用图标"},{default:n(()=>[t(g,{class:"avatar-uploader",action:e.state.imgActionUrl,headers:e.state.headers,"show-file-list":!1,"on-success":u=>e.image.handleUploadSuccess(u),"before-upload":e.image.beforeAvatarUpload},{default:n(()=>[e.form.img?(c(),A("img",{key:0,src:e.form.img,class:"avatar"},null,8,R)):(c(),A("i",W))]),_:1},8,["action","headers","on-success","before-upload"])]),_:1}),t(l,{label:"应用类型"},{default:n(()=>[t(o,{modelValue:e.form.type,"onUpdate:modelValue":a[3]||(a[3]=u=>e.form.type=u),placeholder:"please select your zone",style:{width:"100%"}},{default:n(()=>[t(b,{label:"基础应用",value:"1"}),t(b,{label:"其他应用",value:"2"})]),_:1},8,["modelValue"])]),_:1}),e.form.type==="1"?(c(),h(l,{key:0,label:"应用菜单"},{default:n(()=>[C("div",Y,[t(s,{ref:"menuTree",data:e.state.value,"show-checkbox":"","node-key":"id","default-expand-all":"",props:e.returnrouting.defaultProps},null,8,["data","props"])])]),_:1})):B("",!0),e.form.type==="2"?(c(),h(l,{key:1,label:"应用链接"},{default:n(()=>[t(d,{modelValue:e.form.applicationUrl,"onUpdate:modelValue":a[4]||(a[4]=u=>e.form.applicationUrl=u)},null,8,["modelValue"])]),_:1})):B("",!0)]),_:1},8,["model"])]),_:1},8,["modelValue","title","before-close"])}const le=G(Q,[["render",x]]);export{le as default};
