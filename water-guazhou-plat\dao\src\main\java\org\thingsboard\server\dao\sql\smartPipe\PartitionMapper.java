package org.thingsboard.server.dao.sql.smartPipe;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.DTO.DMAOverviewDTO;
import org.thingsboard.server.dao.model.DTO.PartitionTreeDTO;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.Partition;

import java.util.List;
import java.util.Map;

/**
 * userMybatis
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-08-23
 */
@Mapper
public interface PartitionMapper extends BaseMapper<Partition> {

    List<PartitionTreeDTO> getRootIdNameMapList(@Param("tenantId") String tenantId);

    List<Partition> getRootIdNameList(@Param("tenantId") String tenantId);

    List<Partition> getAllIdNameByPid(@Param("pid") String pid);

    List<PartitionTreeDTO> getIdNameById(@Param("partitionId") String partitionId, @Param("tenantId") String tenantId);

    List<Partition> getAll(@Param("param") Map param);

    Partition getDetail(@Param("id") String id);

    List<String> getAllId(@Param("tenantId") String tenantId);

    List<Partition> getAllByType(@Param("name") String name, @Param("type") String type, @Param("status") String status, @Param("tenantId") String tenantId);

    List<Partition> getAllIdNameByPidIn(@Param("partitionIdList") List<String> partitionIdList, @Param("type") String type);

    List<JSONObject> getUserNum(@Param("partitionIdList") List<String> partitionIdList);

    List<JSONObject> getCopiedNum(@Param("partitionIdList") List<String> partitionIdList, @Param("ymList") List<String> ymList);

    List<Partition> getAllByCondition(@Param("type") String type, @Param("status") String status, @Param("name") String partitionName, @Param("tenantId") String tenantId);

    List<DMAOverviewDTO> getDMAOverview(@Param("status") String status, @Param("name") String partitionName, @Param("tenantId") String tenantId);

    List<JSONObject> getMountNum(@Param("partitionIdList") List<String> partitionIdList, @Param("mountType") String mountType, @Param("direction") String direction);
}
