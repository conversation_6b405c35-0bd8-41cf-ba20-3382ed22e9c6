package org.thingsboard.server.controller.smartPipe;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.request.PartitionMountRequest;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.PartitionMaintainRecords;
import org.thingsboard.server.dao.smartPipe.PartitionMaintainRecordsService;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.List;

/**
 * 智慧管网-维护记录
 */
@RestController
@RequestMapping("api/spp/dma/partition/maintainRecords")
public class PartitionMaintainRecordsController extends BaseController {

    @Autowired
    private PartitionMaintainRecordsService partitionMaintainRecordsService;

    @PostMapping
    public IstarResponse save(@RequestBody PartitionMaintainRecords partitionMaintainRecords) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        partitionMaintainRecords.setTenantId(tenantId);

        return IstarResponse.ok(partitionMaintainRecordsService.save(partitionMaintainRecords));
    }

    @GetMapping("list")
    public IstarResponse getList(PartitionMountRequest request) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        request.setTenantId(tenantId);
        return IstarResponse.ok(partitionMaintainRecordsService.getList(request));
    }

    @DeleteMapping
    public IstarResponse delete(@RequestBody List<String> ids) {
        partitionMaintainRecordsService.delete(ids);
        return IstarResponse.ok("删除成功");
    }
}
