<!--
  水力模型-模型总览
 -->
<template>
  <div class="wrapper overlay-y">
    <el-row :gutter="20">
      <el-col :lg="12">
        <SLCard :height="630" :padding="'20px'">
          <ArcView>
            <ArcPipe></ArcPipe>
          </ArcView>
        </SLCard>
        <SLCard :title="'设备概况'" :padding="'50px 20px 20px 20px'">
          <DeviceTotal :device-total="deviceTotal"></DeviceTotal>
        </SLCard>
        <SLCard :title="'按管径统计管长'" :padding="'50px 20px 20px 20px'">
          <ArcPipeBar
            :prefix="'DN'"
            :height="200"
            :layer-ids="
              useGisStore()
                .gLayerInfos?.filter(
                  (item) => item.geometrytype === 'esriGeometryPolyline'
                )
                .map((item) => item.layerid) || []
            "
            :statistic_field="EStatisticField.ShapeLen"
            :statistic_type="EStatisticType.LENGTH"
            :group_fields="[EStatisticGroup.DIAMETER]"
          ></ArcPipeBar>
        </SLCard>
        <SLCard :title="'按管材统计管长'" :padding="'50px 20px 20px 20px'">
          <ArcPipeBar
            :height="200"
            :layer-ids="
              useGisStore()
                .gLayerInfos?.filter(
                  (item) => item.geometrytype === 'esriGeometryPolyline'
                )
                .map((item) => item.layerid) || []
            "
            :statistic_field="EStatisticField.ShapeLen"
            :statistic_type="EStatisticType.LENGTH"
            :group_fields="[EStatisticGroup.MATERIAL]"
          ></ArcPipeBar>
        </SLCard>
      </el-col>

      <el-col :lg="12">
        <SLCard :title="'管网运行参数'" :padding="'50px 20px 20px 20px'">
          <el-row>
            <el-col v-for="(item, i) in RuningParams" :key="i" :span="6">
              <ValueItem
                :label="item.label"
                :value="item.value"
                :icon="item.icon"
                :rate="item.rate"
                :unit="item.unit"
              ></ValueItem>
            </el-col>
          </el-row>
        </SLCard>
        <SLCard title="管网压力变化曲线" :padding="'50px 20px 20px 20px'">
          <SimpleLines
            :xdata="
              Array.from({ length: 24 }).map((item, i) =>
                padStart((i + 1).toString(), 2, '0')
              )
            "
            :data="[
              {
                name: '最高压力',
                color: '#318DFF',
                values: Array.from({ length: 24 }).map(() =>
                  (Math.random() * 0.1 + 5.4).toFixed(2)
                )
              },
              {
                name: '最低压力',
                color: '#67c23a',
                values: Array.from({ length: 24 }).map(() =>
                  (Math.random() * 0.1 + 5.0).toFixed(2)
                )
              },
              {
                name: '平均压力',
                color: '#e6a23c',
                values: Array.from({ length: 24 }).map(() =>
                  (Math.random() * 0.1 + 5.2).toFixed(2)
                )
              }
            ]"
            :unit="'压力(MPa)'"
            :height="384"
          ></SimpleLines>
        </SLCard>
        <SLCard title="24时供水变化曲线" :padding="'50px 20px 20px 20px'">
          <SimpleLines
            :xdata="
              Array.from({ length: 24 }).map((item, i) =>
                padStart((i + 1).toString(), 2, '0')
              )
            "
            :data="[
              {
                name: 'R1',
                color: '#318DFF',
                values: Array.from({ length: 24 }).map(() =>
                  (Math.random() * 200 + 900).toFixed(2)
                )
              },
              {
                name: 'R2',
                color: '#67c23a',
                values: Array.from({ length: 24 }).map(() =>
                  (Math.random() * 200 + 300).toFixed(2)
                )
              },
              {
                name: 'R3',
                color: '#e6a23c',
                values: Array.from({ length: 24 }).map(() =>
                  (Math.random() * 200 + 100).toFixed(2)
                )
              },
              {
                name: 'R3',
                color: '#e6a23c',
                values: Array.from({ length: 24 }).map(() =>
                  (Math.random() * 200 + 90).toFixed(2)
                )
              }
            ]"
            :unit="'流量(m³/h)'"
            :height="380"
          ></SimpleLines>
        </SLCard>
        <SLCard title="模型精度" :padding="'50px 20px 20px 20px'">
          <el-row>
            <el-col v-for="(item, i) in Accuracy" :key="i" :span="8">
              <ValueItem
                :label="item.label"
                :value="item.value"
                :icon="item.icon"
                :rate="item.rate"
                :unit="item.unit"
              ></ValueItem>
            </el-col>
          </el-row>
          <SimpleLines
            :xdata="
              Array.from({ length: 24 }).map((item, i) =>
                padStart((i + 1).toString(), 2, '0')
              )
            "
            :data="[
              {
                name: 'R1',
                color: '#318DFF',
                values: Array.from({ length: 24 }).map(() =>
                  (Math.random() * 200 + 900).toFixed(2)
                )
              },
              {
                name: 'R2',
                color: '#67c23a',
                values: Array.from({ length: 24 }).map(() =>
                  (Math.random() * 200 + 300).toFixed(2)
                )
              },
              {
                name: 'R3',
                color: '#e6a23c',
                values: Array.from({ length: 24 }).map(() =>
                  (Math.random() * 200 + 100).toFixed(2)
                )
              },
              {
                name: 'R3',
                color: '#e6a23c',
                values: Array.from({ length: 24 }).map(() =>
                  (Math.random() * 200 + 90).toFixed(2)
                )
              }
            ]"
            :unit="'流量(m³/h)'"
            :height="404"
          ></SimpleLines>
        </SLCard>
      </el-col>
    </el-row>
  </div>
</template>
<script lang="ts" setup>
import { padStart } from 'lodash-es';
import {
  EStatisticField,
  EStatisticGroup,
  EStatisticType
} from '@/utils/MapHelper';
import DeviceTotal from './components/DeviceTotal.vue';
import { useGisStore } from '@/store';
import ValueItem from './components/ValueItem.vue';

const deviceTotal = reactive<any[]>([
  {
    span: 8,
    label: '管线总长',
    value: '221.53',
    unit: 'km',
    bgColor: 'rgb(55, 210, 212)'
  },
  {
    span: 8,
    label: '管线总数',
    value: '11915',
    unit: '个',
    bgColor: 'rgb(25, 202, 136)'
  },
  {
    span: 8,
    label: '管点总数',
    value: '11901',
    unit: '个',
    bgColor: 'rgb(133, 143, 248)'
  },
  {
    span: 6,
    label: '水泵总数',
    value: '8',
    unit: '个',
    bgColor: 'rgb(253, 145, 51)'
  },
  {
    span: 6,
    label: '水池总数',
    value: '3',
    unit: '个',
    bgColor: 'rgb(246, 209, 14)'
  },
  {
    span: 6,
    label: '水厂总数',
    value: '2',
    unit: '个',
    bgColor: 'rgb(46, 140, 255)'
  },
  {
    span: 6,
    label: '阀门总数',
    value: '0',
    unit: '个',
    bgColor: 'rgb(246, 88, 14)'
  }
]);
const RuningParams = reactive<any[]>([
  {
    label: '最大流量',
    value: '781.20',
    unit: 'm³/h',
    rate: 11.24,
    icon: 'ep:sort-up'
  },
  {
    label: '最大流速',
    value: '4.81',
    unit: 'm/s',
    rate: 11.24,
    icon: 'ep:sort-up'
  },
  {
    label: '最高压力',
    value: '535.29',
    unit: 'm',
    rate: 11.24,
    icon: 'ep:sort-up'
  },
  {
    label: '最低压力',
    value: '-107.14',
    unit: 'm',
    rate: 11.24,
    icon: 'ep:sort-up'
  }
]);
const Accuracy = reactive<any[]>([
  {
    label: '综合精度',
    value: '100.00',
    unit: '%',
    rate: 3.23,
    icon: 'ep:sort-up'
  },
  {
    label: '压力精度',
    value: '100.00',
    unit: '%',
    rate: 2.44,
    icon: 'ep:sort-up'
  },
  {
    label: '流量精度',
    value: '100.00',
    unit: '%',
    rate: 4.76,
    icon: 'ep:sort-up'
  }
]);
</script>
<style lang="scss" scoped>
.sl-card {
  margin-bottom: 20px;
}
</style>
