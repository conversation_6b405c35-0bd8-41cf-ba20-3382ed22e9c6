package org.thingsboard.server.dao.smartPipe;

import org.thingsboard.server.dao.model.sql.smartPipe.dma.Partition;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.PipeMinFlowConfig;

import java.util.List;
import java.util.Map;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-04-25
 */
public interface PipeMinFlowConfigService {

    List<PipeMinFlowConfig> getListByPartition(String partitionId);

    PipeMinFlowConfig save(PipeMinFlowConfig pipeMinFlowConfig);

    Map<String, PipeMinFlowConfig> getByPartitionIdList(List<String> partitionIdList);
}
