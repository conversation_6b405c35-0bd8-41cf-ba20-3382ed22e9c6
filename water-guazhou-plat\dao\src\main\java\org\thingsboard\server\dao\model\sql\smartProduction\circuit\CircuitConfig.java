package org.thingsboard.server.dao.model.sql.smartProduction.circuit;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseTenantName;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseUsername;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.util.Date;
@Getter
@Setter
@ResponseEntity
@TableName("sp_circuit_config")
public class CircuitConfig {
    // id
    @TableId
    private String id;

    // 配置类型，用于数据隔离。三种类型：水源、水厂、二供泵房。
    private String type;

    // 巡检项目分类
    private String itemType;

    // 巡检配置名称
    private String name;

    // 巡检方法
    private String method;

    // 巡检要求
    private String require;

    // 创建人
    @ParseUsername
    private String creator;

    // 创建时间
    private Date createTime;

    // 租户ID
    @ParseTenantName
    private String tenantId;

}
