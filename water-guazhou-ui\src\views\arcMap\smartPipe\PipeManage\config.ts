import { getPipeFeatureLayerInfo } from '@/api/mapservice'
import { excuteQueryForIds, initQueryParams } from '@/utils/MapHelper'

export const skipedFields: string[] = [
  'ENABLED',
  'LINKID',
  'AUDITEDDATE',
  'AUDITEDISPASS',
  'REP<PERSON><PERSON>NUMBER',
  'PROJECTNAME',
  // 'FINISHDATE',
  'COMPLETIONDATE',
  'HASATTACHFILE',
  'UPDATEDBY',
  'UPDATEDDATE',
  'PERCENTOPEN',
  'CURRENTLYOPEN',
  'OPENCLOSE',
  'OUTFILE',
  // 'START_SID',
  '所属水厂',
  '所属片区',
  'ANCILLARY<PERSON>LE',
  'USERCOUNT',
  'CREATEDDATE',
  'ATTACH_SID',
  'BYPASSVALVE',
  'NORMALLYOPEN',
  'OPERABLE',
  'HYDRANTVALVE',
  'WATERTYPE',
  'LIFECYCLESTATUS',
  'ADMINISTRATIVEAREA',
  'SHAPE.LEN'
]
export const initPipeEditForm = async (layerid?: number, row?: any) => {
  if (layerid === undefined) return []
  const requiredFields = ['BURYTYPE', 'SID', 'DIAMETER', 'MATERIAL', 'PIPELENGTH', 'LANEWAY']
  const topFields = [...requiredFields]
  const res = await getPipeFeatureLayerInfo(layerid)
  const data = res.data
  const fields: IFormItem[] = []
  const formFields: IFormItem[] = []
  data.fields.map(item => {
    if (skipedFields.indexOf(item.name) !== -1) return
    if (item.type === 'esriFieldTypeOID') {
      const config: IFormInput = {
        type: 'input',
        label: item.alias,
        field: item.name,
        disabled: true,
        placeholder: '自动填充'
      }
      formFields.push(config)
    }
    if (item.type === 'esriFieldTypeString') {
      const config: IFormInput = {
        type: 'input',
        label: item.alias,
        field: item.name,
        disabled: item.editable !== true,
        placeholder: '请输入' + item.alias,
        maxlength: item.length,
        rules:
          !item.nullable || requiredFields.includes(item.name)
            ? [{ required: true, message: '请输入' + item.alias }]
            : []
      }
      formFields.push(config)
    }
    if (item.type === 'esriFieldTypeDouble') {
      const config: IFormInputNumber = {
        type: 'input-number',
        label: item.alias,
        field: item.name,
        readonly: item.editable !== true,
        placeholder: '请输入' + item.alias,
        rules:
          !item.nullable || requiredFields.includes(item.name)
            ? [{ required: true, message: '请输入' + item.alias }]
            : []
      }
      formFields.push(config)
    }
    if (item.type === 'esriFieldTypeDate') {
      const config: IFormDate = {
        type: 'date',
        label: item.alias,
        field: item.name,
        readonly: item.editable !== true,
        placeholder: '请选择' + item.alias,
        disabledDate: date => {
          return Date.now() < date.valueOf()
        },
        formatter: (val, row, config) => {
          console.log(val, row, config)

          return moment(val, 'YYYY-MM-DD').format('YYYYMMDD')
        },
        rules:
          !item.nullable || requiredFields.includes(item.name)
            ? [{ required: true, message: '请选择' + item.alias }]
            : []
      }
      formFields.push(config)
    }
    if (item.type === 'esriFieldTypeInteger') {
      const config: IFormNumber = {
        type: 'number',
        label: item.alias,
        field: item.name,
        controlPosition: 'right',
        min: 0,
        readonly: item.editable !== true,
        placeholder: '请输入' + item.alias,
        rules:
          !item.nullable || requiredFields.includes(item.name)
            ? [{ required: true, message: '请输入' + item.alias }]
            : []
      }
      formFields.push(config)
    }
    if (item.type === 'esriFieldTypeSmallInteger') {
      const config: IFormRadio = {
        type: 'radio',
        label: item.alias,
        field: item.name,
        readonly: item.editable !== true,
        placeholder: '请选择' + item.alias,
        rules:
          !item.nullable || requiredFields.includes(item.name)
            ? [{ required: true, message: '请选择' + item.alias }]
            : [],
        options: []
      }
      if (item.domain?.codedValues?.length && item.domain.type === 'codeValue') {
        config.options = item.domain.codeValues.map(item => {
          return {
            label: item.name,
            value: item.code
          }
        })
      } else {
        config.options = [
          { label: '是', value: 1 },
          { label: '否', value: 0 }
        ]
      }
      formFields.push(config)
    }
  })
  formFields.map(item => {
    if (!item.field) return

    if (item.field.toLowerCase() === 'sid') {
      item.rules = [
        { required: true, message: '请输入编号' },
        {
          asyncValidator: (rule, value, callback) => {
            isExistSid(value, layerid, row, callback)
          }
        }
      ]
    }
    const index = topFields.indexOf(item.field)
    if (index !== -1) {
      fields.splice(1, 0, item)
    } else {
      fields.push(item)
    }
  })
  return fields
}
const isExistSid = async (value, layerid, row, callBack) => {
  const oidUrl = window.SITE_CONFIG.GIS_CONFIG.gisService + window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService
  let alloids: any
  try {
    alloids = await excuteQueryForIds(
      `${oidUrl}/${layerid}`,
      initQueryParams({
        returnGeometry: false,
        where: "SID='" + value + "'"
      })
    )
  } catch (error) {
    alloids = null
  }

  // 当查询到存在相同编号则进一步判断： 是编辑还是增加
  if (alloids?.length) {
    // 当传入了row参数并且在查询结果中存在与row.OBJECTID相同的值,则表示是编辑，否则是新增，
    // 新增时只要alloids长度不为0则不通过，编辑时需要判断存在是否只有一条并且此条是不是自身
    const hasObjectid = row?.OBJECTID !== undefined && row?.OBJECTID !== null
    const hasSelf = hasObjectid && alloids.includes(row.OBJECTID)
    if (!hasObjectid) {
      // 是新增
      return callBack(new Error('当前编号已存在'))
    }
    // 存在自身
    if (hasSelf) {
      // 且有其它的
      if (alloids.length > 1) {
        return callBack(new Error('当前编号重复'))
      }
      // 没有其它的，随便编辑
      return callBack()
    }
    // 没有自身
    return callBack('当前编号已存在')
  }
  return callBack()
}
