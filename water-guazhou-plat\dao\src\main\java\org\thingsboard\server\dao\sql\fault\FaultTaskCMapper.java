package org.thingsboard.server.dao.sql.fault;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.fault.FaultTaskC;

import java.util.Date;
import java.util.List;

/**
 * userMybatis
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-08-23
 */
@Mapper
public interface FaultTaskCMapper extends BaseMapper<FaultTaskC> {

    List<FaultTaskC> getList(String mainId);

    FaultTaskC selectFirstByDeviceLabelCode(@Param("deviceLabelCode") String deviceLabelCode, @Param("thisTime") Date time);
}
