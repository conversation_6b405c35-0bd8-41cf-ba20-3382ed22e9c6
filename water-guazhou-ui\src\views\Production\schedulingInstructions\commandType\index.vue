<!-- 指令类型 -->
<template>
  <div class="wrapper">
    <CardSearch ref="refSearch" :config="cardSearchConfig" />
    <CardTable :key="temporaryId" class="card-table" :config="TableConfig" />
    <SLDrawer ref="refForm" :config="addOrUpdateConfig"></SLDrawer>
  </div>
</template>

<script lang="ts" setup>
import { Refresh } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { ICONS } from '@/common/constans/common';
import useGlobal from '@/hooks/global/useGlobal';
import { SLConfirm } from '@/utils/Message';
import {
  getorderRecordType,
  postorderRecordType,
  deleteorderRecordType
} from '@/api/productionScheduling/schedulingInstructions';
import { getWaterSupplyTree } from '@/api/company_org';
import { traverse, setDisable } from '@/utils/GlobalHelper';

const { $btnPerms } = useGlobal();

const temporaryId = ref(0);

const refForm = ref<ISLDrawerIns>();

const refSearch = ref<ICardSearchIns>();

const cardSearchConfig = ref<ISearch>({
  filters: [{ label: '指令类型名称', field: 'name', type: 'input' }],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          icon: ICONS.QUERY,
          click: () => refreshData()
        },
        {
          type: 'default',
          perm: true,
          text: '重置',
          svgIcon: shallowRef(Refresh),
          click: () => {
            refSearch.value?.resetForm();
            refreshData();
          }
        },
        {
          perm: true,
          text: '新建',
          icon: ICONS.ADD,
          type: 'success',
          click: () => clickCreatedRole()
        }
      ]
    }
  ]
});

const TableConfig = reactive<ICardTable>({
  defaultExpandAll: true,
  indexVisible: true,
  rowKey: 'id',
  columns: [
    { label: '指令类型名称', prop: 'name' },
    { label: '指定部门名称', prop: 'deptNameList' }
  ],
  operationWidth: '160px',
  operations: [
    {
      type: 'primary',
      text: '编辑',
      icon: ICONS.EDIT,
      perm: $btnPerms('RoleManageEdit'),
      click: (row) => clickEdit(row)
    },
    {
      type: 'danger',
      text: '删除',
      perm: $btnPerms('RoleManageDelete'),
      icon: ICONS.DELETE,
      click: (row) => handleDelete(row)
    }
  ],
  dataList: [],
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page;
      TableConfig.pagination.limit = size;
      refreshData();
    }
  }
});

const addOrUpdateConfig = reactive<IDrawerConfig>({
  title: '新增',
  width: '500px',
  labelWidth: '120px',
  submitting: false,
  submit: (params: any) => {
    addOrUpdateConfig.submitting = true;
    let text = '新增成功';
    if (params.id) text = '修改成功';
    params.deptIdList = params.deptIdList.join(',');
    postorderRecordType(params)
      .then(() => {
        addOrUpdateConfig.submitting = false;
        refForm.value?.closeDrawer();
        ElMessage.success(text);
        refreshData();
      })
      .catch((error) => {
        addOrUpdateConfig.submitting = false;
        ElMessage.warning(error);
      });
  },
  defaultValue: {},
  group: [
    {
      fields: [
        {
          type: 'input',
          label: '指令类型名称1',
          field: 'name',
          rules: [{ required: true, message: '请输入指令类型名称' }]
        },
        {
          type: 'select-tree',
          label: '指定部门',
          field: 'deptIdList',
          checkStrictly: false,
          multiple: true,
          defaultExpandAll: true,
          rules: [{ required: true, message: '请选择部门' }],
          options: computed(() => data.WaterSupplyTree) as any
        }
      ]
    }
  ]
});

const clickCreatedRole = () => {
  addOrUpdateConfig.title = '新增';
  addOrUpdateConfig.defaultValue = { deptIdList: [] };
  refForm.value?.openDrawer();
};

const clickEdit = (row: { [x: string]: any }) => {
  addOrUpdateConfig.title = '编辑';
  const deptIdList = row.deptIdList.split(',');
  addOrUpdateConfig.defaultValue = {
    category: row.parentId,
    ...(row || {}),
    deptIdList
  };
  refForm.value?.openDrawer();
};

const handleDelete = (row?: any) => {
  SLConfirm('确定删除该指令类型', '删除提示').then(() => {
    deleteorderRecordType(row.id)
      .then(() => {
        ElMessage.success('删除成功');
        temporaryId.value += 1;
        refreshData();
      })
      .catch((error) => {
        ElMessage.error(error);
      });
  });
};

const data = reactive({
  // 部门
  WaterSupplyTree: [],
  getWaterSupplyTreeValue: () => {
    const depth = 2;
    getWaterSupplyTree(depth).then((res) => {
      data.WaterSupplyTree = traverse(res.data.data || []);
      data.WaterSupplyTree = setDisable(data.WaterSupplyTree, {
        label: 'layer',
        value: 1
      });
    });
  }
});

const refreshData = async () => {
  const params = {
    size: TableConfig.pagination.limit,
    page: TableConfig.pagination.page,
    ...(refSearch.value?.queryParams || {})
  };
  getorderRecordType(params)
    .then((res) => {
      TableConfig.dataList = res.data.data.data || [];
      TableConfig.pagination.total = res.data.data.total || 0;
    })
    .catch(() => {
      ElMessage.warning('请求遇到错误');
    });
};

onMounted(async () => {
  refreshData();
  data.getWaterSupplyTreeValue();
});
</script>
