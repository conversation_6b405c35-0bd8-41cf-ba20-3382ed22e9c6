// 智慧生产=水源管理-水源巡检 /api
import request from '@/plugins/axios';

// 删除巡检模板
export function delCircuitTemplate(id?: string) {
  return request({
    url: `/api/sp/circuitTemplate/${id}`,
    method: 'delete'
  });
}

// 巡检模板分页查询
export function circuitTemplateList(params: any) {
  return request({
    url: '/api/sp/circuitTemplate',
    method: 'get',
    params
  });
}

// 添加巡检模板
export function addCircuitTemplate(params?: {
  type: any;
  settings?: any;
  name: string;
  require?: string;
}) {
  return request({
    url: '/api/sp/circuitTemplate',
    method: 'post',
    data: params
  });
}

// 查询巡检配置
export function circuitConfigList(params: {
  page: any;
  size: any;
  type?: string;
  itemType?: string;
  name?: string;
}) {
  return request({
    url: '/api/sp/circuitConfig',
    method: 'get',
    params
  });
}

// 新增巡检配置
export function addCircuitConfig(params: {
  type: any;
  itemType?: any;
  name: string;
  method: string;
  require: string;
}) {
  return request({
    url: '/api/sp/circuitConfig',
    method: 'post',
    data: params
  });
}

// 删除巡检配置
export function delCircuitConfig(id: string) {
  return request({
    url: `/api/sp/circuitConfig/${id}`,
    method: 'delete'
  });
}

// 巡检计划分页查询
export function getCircuitPlan(params?: any) {
  return request({
    url: '/api/sp/circuitPlan',
    method: 'get',
    params
  });
}

// 新增巡检计划
export function addCircuitPlan(params?: any) {
  return request({
    url: '/api/sp/circuitPlan',
    method: 'post',
    data: params
  });
}

// 删除巡检计划
export function delCircuitPlan(id: string) {
  return request({
    url: `/api/sp/circuitPlan/${id}`,
    method: 'delete'
  });
}

// 删除巡检计划
export function batchDelCircuitPlan(ids: string[]) {
  return request({
    url: `/api/sp/circuitPlan`,
    method: 'delete',
    data: ids
  });
}

// 新增巡检任务
export function circuitTaskList(params?: any) {
  return request({
    url: '/api/sp/circuitTask',
    method: 'get',
    params
  });
}

// 新增巡检任务
export function addCircuitTask(params?: any) {
  return request({
    url: '/api/sp/circuitTask',
    method: 'post',
    data: params
  });
}

// 新增巡检任务
export function delCircuitTask(id: string) {
  return request({
    url: `/api/sp/circuitTask/${id}`,
    method: 'delete'
  });
}

// 新增巡检任务
export function receiveCircuitTask(id: string) {
  return request({
    url: `/api/sp/circuitTask/${id}/receive`,
    method: 'post'
  });
}

// 申请审核巡检任务
export function sendVerifyCircuitTask(id: string, auditUserId: string) {
  return request({
    url: `/api/sp/circuitTask/${id}/sendVerify/${auditUserId}`,
    method: 'post'
  });
}

// 审核巡检任务
export function verifyCircuitTask(id: string, allow: boolean) {
  return request({
    url: `/api/sp/circuitTask/${id}/verify/${allow}`,
    method: 'post'
  });
}
//
export function circuitTaskItemList(params?: any) {
  return request({
    url: '/api/circuitTaskItem',
    method: 'get',
    params
  });
}
