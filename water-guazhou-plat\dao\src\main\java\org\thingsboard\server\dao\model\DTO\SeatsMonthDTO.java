package org.thingsboard.server.dao.model.DTO;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 工单区域-业务主题
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-12-21
 */
@Data
public class SeatsMonthDTO {

    private String seatsName;

    private List<Integer> monthList = new ArrayList();

    public SeatsMonthDTO() {
    }

    public SeatsMonthDTO(String seatsName) {
        this.seatsName = seatsName;
        for (int i = 1; i <= 13; i++) {
            monthList.add(0);
        }
    }
}
