package org.thingsboard.server.dao.sql.repair;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.dao.model.sql.MaintenanceJobCEntity;

import java.util.List;

public interface MaintenanceJobCRepository extends JpaRepository<MaintenanceJobCEntity, String> {
    List<MaintenanceJobCEntity> findByMainId(String mainId);

    @Modifying
    @Transactional
    void removeByMainId(String id);
}
