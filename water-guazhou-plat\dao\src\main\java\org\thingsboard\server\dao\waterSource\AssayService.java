package org.thingsboard.server.dao.waterSource;

import org.springframework.web.multipart.MultipartFile;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.assay.Assay;

import java.util.List;
import java.util.Map;

/**
 * 化验记录服务接口
 */
public interface AssayService {

    /**
     * 获取化验记录列表
     */
    PageData<Assay> getList(Map<String, Object> params, String tenantId);

    /**
     * 保存化验记录
     */
    Assay save(Assay entity);

    /**
     * 批量删除化验记录
     */
    void delete(List<String> idList);

    /**
     * 获取单个化验记录
     */
    Assay getById(String id);

    /**
     * 上传报告文件
     */
    Assay uploadReport(Assay assay);

}
