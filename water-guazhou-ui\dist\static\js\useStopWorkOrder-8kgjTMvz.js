import{r as o,S as s,b as a}from"./index-r0dFAfgr.js";const i=r=>{const e=o({width:500,labelPosition:"top",title:"终止",group:[{fields:[{type:"textarea",label:"终止原因：",field:"processRemark",rules:[{required:!0,message:"请输入终止原因"}]}]}],submit:t=>{s("确定终止？","提示信息").then(async()=>{e.submitting=!0;try{await r(t)}catch{a.error("系统错误")}e.submitting=!1}).catch(()=>{})},defaultValue:{processAdditionalInfo:void 0}});return{FormConfig_Stop:e}},l=i;export{l as u};
