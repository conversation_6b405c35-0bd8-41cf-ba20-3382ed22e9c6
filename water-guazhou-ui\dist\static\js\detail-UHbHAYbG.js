import{d as R,M as G,c as w,r as I,am as H,bB as A,bF as U,o as W,ay as z,g as k,n as b,p as v,q as l,F as c,G as u,bh as g,i as r,av as K,aB as B,aJ as J,bU as P,bW as Q,c5 as X,aq as Z,C as ee}from"./index-r0dFAfgr.js";import{_ as te}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{j as ae,k as oe,l as ne}from"./process-DWVjEFpZ.js";import{s as se,c as re}from"./applyInstall-D-IustB3.js";import{d as le}from"./printUtils-C-AxhDcd.js";const ie={class:"top"},fe={style:{"margin-top":"20px"}},de={style:{"margin-top":"20px"}},pe=["onClick"],ce=R({__name:"detail",props:{taskInfo:{},taskId:{}},setup(h){const{$messageError:S,$messageSuccess:L,$messageWarning:O}=G(),_=w(null),D=w(),V=w(),T=h,e=I({activeName:"",formJson:{},optionData:{},formData:{},currentStepId:"",contractList:[],flowDetail:[],fileTypes:[],fileDetail:{},fileArray:[],taskInfo:{},currentContent:null});H(()=>T.taskId,a=>{console.log(a)});const N=I({type:"tabs",tabType:"inset",width:"100%",tabs:[],handleTabClick:a=>{var t,o,d;e.currentContent=(t=e.flowDetail)==null?void 0:t.find(i=>i.id===a.props.name),console.log(e.currentContent),(d=_.value)==null||d.setFormJson(JSON.parse((o=e.currentContent)==null?void 0:o.content)),A(()=>{var i,p;(p=_.value)==null||p.setFormData(JSON.parse((i=e.currentContent)==null?void 0:i.formData))}),e.fileTypes=[],F()}}),x=I({title:"资料上传",group:[{fields:[{type:"select",label:"文档类型",field:"fileType",options:[],rules:[{required:!0,message:"请选择文档类型"}],onChange:a=>{var i,p,m,y,n,s;e.fileDetail=(i=e.fileTypes)==null?void 0:i.find(f=>f.id===a);const t=(p=x.group[0].fields)==null?void 0:p.find(f=>f.field==="hint");t.text=e.fileDetail.num;const o=(y=(m=D.value)==null?void 0:m.refForm)==null?void 0:y.dataForm;console.log((s=(n=D.value)==null?void 0:n.refForm)==null?void 0:s.dataForm),e.fileArray.find(f=>f.fileType===a)||e.fileArray.push(o)}},{type:"input",label:"文档编号",field:"code"},{type:"hint",label:"上传文件个数",field:"hint",text:"0"},{type:"file",label:"选中文件",field:"file",returnType:"arrStr",rules:[{required:!0,message:"选中文件"}]}]}],submit:a=>{var t;((t=a.file)==null?void 0:t.split(",").length)>=e.fileDetail.num?(e.fileArray=e.fileArray.map(o=>({...e.fileDetail,file:o.file})),console.log(e.fileArray),se(e.fileArray).then(o=>{var d,i,p,m;((d=o.data)==null?void 0:d.code)===200?((i=D.value)==null||i.closeDialog(),(m=(p=D.value)==null?void 0:p.refForm)==null||m.resetForm(),F(),L("上传成功！")):S("上传失败！")}).catch(o=>{S(o)})):O("请上传指定数量文件")}}),M=I({title:"流程明细",desTroyOnClose:!0,group:[],submit:void 0}),$=I({columns:[{label:"流程",prop:"title",cellStyle:a=>{var t;return{color:a.status==="已完成"?"#409eff":a.id===((t=e.taskInfo)==null?void 0:t.currentStepId)?"red":""}}},{label:"开始时间",prop:"startTime",formatter:a=>C(a.startTime)},{label:"结束时间",prop:"endTime",formatter:a=>C(a.endTime)},{label:"工作人员",prop:"operatorName"},{label:"说明",prop:"status",formatter:a=>a.status==="已完成"?"√":" "}],dataList:[],pagination:{hide:!0}}),C=a=>a?U(a).format("YYYY-MM-DD HH:mm:ss"):"",j=async a=>{var o,d;const t=await ne(a);e.contractList=(o=t.data)==null?void 0:o.data.contractTemplateList,console.log((d=t.data)==null?void 0:d.data)},F=async()=>{var t;const a=await ae(T.taskInfo.id,e.currentContent.id);e.fileTypes=(t=a.data)==null?void 0:t.data},q=async()=>{var t;const a=await re(T.taskInfo.id);e.taskInfo=(t=a.data)==null?void 0:t.data},E=async()=>{var d,i,p,m,y,n;await q();const a=(d=e.taskInfo)==null?void 0:d.currentStepId,o=(p=(await oe((i=e.taskInfo)==null?void 0:i.id)).data)==null?void 0:p.data;e.flowDetail=o,N.tabs=o.map(s=>{var f;return{label:s.title,value:s.id,disabled:s.status!=="已完成"&&((f=e.taskInfo)==null?void 0:f.currentStepId)!==s.id,data:{content:s.content,formData:s.formData,id:s.id}}}),e.activeName=a,e.currentContent=o.find(s=>s.id===a),(y=_.value)==null||y.setFormJson(JSON.parse((m=e.currentContent)==null?void 0:m.content)),await A(()=>{var s,f;(f=_.value)==null||f.setFormData(JSON.parse((s=e.currentContent)==null?void 0:s.formData))}),await j((n=e.taskInfo)==null?void 0:n.type),await F()};return W(async()=>{await E()}),(a,t)=>{const o=P,d=Q,i=X,p=z("v-form-render"),m=te,y=Z;return k(),b("div",null,[v("div",ie,[l(d,{gutter:20},{default:c(()=>[l(o,{span:2},{default:c(()=>t[1]||(t[1]=[u(" 申请编号： ")])),_:1}),l(o,{span:4},{default:c(()=>{var n;return[u(g((n=r(e).taskInfo)==null?void 0:n.code),1)]}),_:1}),l(o,{span:2},{default:c(()=>t[2]||(t[2]=[u(" 登记日期： ")])),_:1}),l(o,{span:16},{default:c(()=>{var n;return[u(g(C((n=r(e).taskInfo)==null?void 0:n.createTime)),1)]}),_:1}),l(o,{span:2},{default:c(()=>t[3]||(t[3]=[u(" 工程地址： ")])),_:1}),l(o,{span:4},{default:c(()=>{var n;return[u(g((n=r(e).taskInfo)==null?void 0:n.address),1)]}),_:1}),l(o,{span:2},{default:c(()=>t[4]||(t[4]=[u(" 工程类型： ")])),_:1}),l(o,{span:16},{default:c(()=>{var n;return[u(g((n=r(e).taskInfo)==null?void 0:n.typeName),1)]}),_:1}),l(o,{span:2},{default:c(()=>t[5]||(t[5]=[u(" 当前状态： ")])),_:1}),l(o,{span:6},{default:c(()=>{var n,s;return[v("span",{style:K({color:((n=r(e).taskInfo)==null?void 0:n.status)==="已完成"?"red":"green"})},g((s=r(e).taskInfo)==null?void 0:s.status),5)]}),_:1})]),_:1})]),l(i,{modelValue:r(e).activeName,"onUpdate:modelValue":t[0]||(t[0]=n=>r(e).activeName=n),config:r(N)},null,8,["modelValue","config"]),v("div",fe,[l(p,{ref_key:"vFormRef",ref:_,"form-json":r(e).formJson,"form-data":r(e).formData,"option-data":r(e).optionData},null,8,["form-json","form-data","option-data"]),v("div",de,[(k(!0),b(B,null,J(r(e).fileTypes,(n,s)=>(k(),b("div",{key:s},[(k(!0),b(B,null,J(JSON.parse(n.file),(f,Y)=>(k(),b("div",{key:Y,style:{color:"#409eff",padding:"10px"}},[v("a",{style:{"border-bottom":"1px solid #409eff"},onClick:me=>r(le)(f.url,f.name)},g(f.name),9,pe)]))),128))]))),128))])]),l(m,{ref_key:"refDialog",ref:D,config:r(x)},null,8,["config"]),l(m,{ref_key:"refDetailDialog",ref:V,config:r(M)},{default:c(()=>[l(y,{config:r($)},null,8,["config"])]),_:1},8,["config"])])}}}),Ie=ee(ce,[["__scopeId","data-v-01f7d1a6"]]);export{Ie as default};
