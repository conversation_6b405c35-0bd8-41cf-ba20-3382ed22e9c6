package org.thingsboard.server.dao.maintainCircuit.circuit;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.User;
import org.thingsboard.server.common.data.exception.ThingsboardErrorCode;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.request.StationCircuitTaskListRequest;
import org.thingsboard.server.dao.model.sql.department.Department;
import org.thingsboard.server.dao.model.sql.maintainCircuit.circuit.StationCircuitPlan;
import org.thingsboard.server.dao.model.sql.maintainCircuit.circuit.StationCircuitTask;
import org.thingsboard.server.dao.organization.DepartmentService;
import org.thingsboard.server.dao.sql.maintainCircuit.circuit.StationCircuitTaskMapper;
import org.thingsboard.server.dao.user.UserService;
import org.thingsboard.server.dao.util.RedisUtil;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class StationCircuitTaskServiceImpl implements StationCircuitTaskService {

    @Autowired
    private StationCircuitTaskMapper stationCircuitTaskMapper;

    @Autowired
    private UserService userService;

    @Autowired
    private DepartmentService departmentService;

    @Override
    public void save(List<StationCircuitTask> taskList) {
        for (StationCircuitTask task : taskList) {
            stationCircuitTaskMapper.insert(task);
        }
    }

    @Override
    public PageData<StationCircuitTask> findList(StationCircuitTaskListRequest request) {
        // 处理结束时间
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        try {
            if (StringUtils.isNotBlank(request.getStartTime())) {
                request.setBeginStartTime(dateFormat.parse(request.getStartTime()));
                request.setEndStartTime(new Date(request.getBeginStartTime().getTime() + (24 * 60 * 60 * 1000) - 1000));
            }
            if (StringUtils.isNotBlank(request.getEndTime())) {
                request.setBeginEndTime(dateFormat.parse(request.getEndTime()));
                request.setEndEndTime(new Date(request.getBeginEndTime().getTime() + (24 * 60 * 60 * 1000) - 1000));
            }
            if (StringUtils.isNotBlank(request.getRealStartTime())) {
                request.setBeginRealStartTime(dateFormat.parse(request.getRealStartTime()));
                request.setEndRealStartTime(new Date(request.getBeginRealStartTime().getTime() + (24 * 60 * 60 * 1000) - 1000));
            }
            if (StringUtils.isNotBlank(request.getRealEndTime())) {
                request.setBeginRealEndTime(dateFormat.parse(request.getRealEndTime()));
                request.setEndRealEndTime(new Date(request.getBeginRealEndTime().getTime() + (24 * 60 * 60 * 1000) - 1000));
            }
        } catch (ParseException e) {
        }

        Page<StationCircuitTask> pageRequest = new Page<>(request.getPage(), request.getSize());

        IPage<StationCircuitTask> pageResult = stationCircuitTaskMapper.findList(pageRequest, request);
        // 查询部门
        List<Department> departmentList = departmentService.findByTenantId(request.getTenantId());
        Map<String, Department> depMap = departmentList.stream().collect(Collectors.toMap(Department::getId, user -> user));

        // 查询用户
        List<User> userList = userService.findUserByTenant(new TenantId(UUIDConverter.fromString(request.getTenantId())));
        Map<String, User> userMap = userList.stream()
                .collect(Collectors.toMap(user -> UUIDConverter.fromTimeUUID(user.getUuidId()), user -> user));

        List<StationCircuitTask> records = pageResult.getRecords();
        for (StationCircuitTask record : records) {
            // 设置部门名称
            Department auditDep = depMap.get(record.getAuditDep());
            if (auditDep != null) {
                record.setAuditDepName(auditDep.getName());
            }
            Department optionDep = depMap.get(record.getOptionDep());
            if (optionDep != null) {
                record.setOptionDepName(optionDep.getName());
            }
            // 设置人员名称
            User auditUser = userMap.get(record.getAuditUserId());
            if (auditUser != null) {
                record.setAuditUserName(auditUser.getFirstName());
            }
            User optionUser = userMap.get(record.getOptionUserId());
            if (optionUser != null) {
                record.setOptionUserName(optionUser.getFirstName());
            }
        }

        return new PageData<>(pageResult.getTotal(), records);
    }

    @Override
    public void save(StationCircuitTask entity) {
        if (StringUtils.isBlank(entity.getId())) {
            // 新增
            entity.setCreateTime(new Date());
            entity.setStatus(DataConstants.STATION_CIRCUIT_TASK_STATUS.NEW.getValue());
            entity.setCode(RedisUtil.nextId(DataConstants.REDIS_KEY.EXPANDING, "YH"));

            stationCircuitTaskMapper.insert(entity);
        } else {
            // 更新
            stationCircuitTaskMapper.updateById(entity);
        }
    }

    @Override
    public void remove(List<String> ids) {
        stationCircuitTaskMapper.deleteBatchIds(ids);
    }

    @Override
    public void receive(String id, User current) throws ThingsboardException {
        // 查询任务
        StationCircuitTask task = stationCircuitTaskMapper.selectById(id);
        if (task == null) {
            throw new ThingsboardException("任务不存在!", ThingsboardErrorCode.GENERAL);
        }
        // 状态是否为待分派
        if (!DataConstants.STATION_CIRCUIT_TASK_STATUS.NEW.getValue().equals(task.getStatus())) {
            throw new ThingsboardException("任务已不处于待分派状态", ThingsboardErrorCode.GENERAL);
        }
        // 是否有权限接收
        if (StringUtils.isBlank(task.getOptionUserId())) {
            if (!task.getOptionDep().equals(current.getDepartmentId())) {
                throw new ThingsboardException("您无权限接收该任务!", ThingsboardErrorCode.GENERAL);
            }
        } else {
            if (!UUIDConverter.fromTimeUUID(current.getUuidId()).equals(task.getOptionUserId())) {
                throw new ThingsboardException("您无权限接收该任务!", ThingsboardErrorCode.GENERAL);
            }
        }
        // 修改任务状态
        task.setStatus(DataConstants.STATION_CIRCUIT_TASK_STATUS.PROCESSING.getValue());
        task.setRealStartTime(new Date());

        stationCircuitTaskMapper.updateById(task);
    }

    @Override
    public void complete(String id, User currentUser) throws ThingsboardException {
        // 查询任务
        StationCircuitTask task = stationCircuitTaskMapper.selectById(id);
        if (task == null) {
            throw new ThingsboardException("任务不存在!", ThingsboardErrorCode.GENERAL);
        }
        // 状态是否为执行中
        if (!DataConstants.STATION_CIRCUIT_TASK_STATUS.PROCESSING.getValue().equals(task.getStatus())) {
            throw new ThingsboardException("任务已不处于执行中状态", ThingsboardErrorCode.GENERAL);
        }
        // 是否有权限
        if (!UUIDConverter.fromTimeUUID(currentUser.getUuidId()).equals(task.getOptionUserId())) {
            throw new ThingsboardException("您无权限完成该任务!", ThingsboardErrorCode.GENERAL);
        }
        // 修改任务状态
        task.setStatus(DataConstants.STATION_CIRCUIT_TASK_STATUS.PROCESS_COMPLETE.getValue());
        task.setRealEndTime(new Date());

        stationCircuitTaskMapper.updateById(task);
    }

    @Override
    public void audit(StationCircuitTask param, User currentUser) throws ThingsboardException {
        // 查询任务
        StationCircuitTask task = stationCircuitTaskMapper.selectById(param.getId());
        if (task == null) {
            throw new ThingsboardException("任务不存在!", ThingsboardErrorCode.GENERAL);
        }
        // 状态是否为待审核
        if (!DataConstants.STATION_CIRCUIT_TASK_STATUS.PROCESS_COMPLETE.getValue().equals(task.getStatus())) {
            throw new ThingsboardException("任务已不处于待审核状态", ThingsboardErrorCode.GENERAL);
        }
        // 是否有权限接收
        if (StringUtils.isBlank(task.getAuditDep())) {
            if (!task.getAuditDep().equals(currentUser.getDepartmentId())) {
                throw new ThingsboardException("您无权限审核该任务!", ThingsboardErrorCode.GENERAL);
            }
        } else {
            if (!UUIDConverter.fromTimeUUID(currentUser.getUuidId()).equals(task.getAuditUserId())) {
                throw new ThingsboardException("您无权限审核该任务!", ThingsboardErrorCode.GENERAL);
            }
        }
        // 修改任务状态
        task.setAuditResult(param.getAuditResult());
        task.setAuditRemark(param.getAuditRemark());
        task.setAuditTime(new Date());
        if (task.getRealEndTime().getTime() > task.getStartTime().getTime()) {
            task.setStatus(DataConstants.STATION_CIRCUIT_TASK_STATUS.TIMEOUT_COMPLETE.getValue());
        } else {
            task.setStatus(DataConstants.STATION_CIRCUIT_TASK_STATUS.COMPLETE.getValue());
        }

        stationCircuitTaskMapper.updateById(task);
    }

    @Override
    public void resend(StationCircuitTask param, User currentUser) throws ThingsboardException {
        // 查询任务
        StationCircuitTask task = stationCircuitTaskMapper.selectById(param.getId());
        if (task == null) {
            throw new ThingsboardException("任务不存在!", ThingsboardErrorCode.GENERAL);
        }
        // 状态是否为待分派
        if (!DataConstants.STATION_CIRCUIT_TASK_STATUS.COMPLETE.getValue().equals(task.getStatus()) || !DataConstants.STATION_CIRCUIT_TASK_STATUS.TIMEOUT_COMPLETE.getValue().equals(task.getStatus())) {
            throw new ThingsboardException("任务已完成", ThingsboardErrorCode.GENERAL);
        }
        // 是否有权限接收
        if (StringUtils.isBlank(task.getOptionUserId())) {
            if (!task.getOptionDep().equals(currentUser.getDepartmentId())) {
                throw new ThingsboardException("您无权限接收该任务!", ThingsboardErrorCode.GENERAL);
            }
        } else {
            if (!UUIDConverter.fromTimeUUID(currentUser.getUuidId()).equals(task.getOptionUserId())) {
                throw new ThingsboardException("您无权限接收该任务!", ThingsboardErrorCode.GENERAL);
            }
        }
        // 修改任务信息
        task.setOptionDep(param.getOptionDep());
        task.setOptionUserId(param.getOptionUserId());

        stationCircuitTaskMapper.updateById(task);
    }
}
