<template>
  <div class="team_table">
    <FormTable :config="TableConfig"></FormTable>
    <DialogForm
      ref="refForm"
      class="dialogForm"
      :config="addOrUpdateConfig"
    ></DialogForm>
    <DialogForm ref="refDeviceForm" :config="deviceConfig"></DialogForm>
    <DialogForm ref="refAddDeviceForm" :config="AdddeviceConfig"></DialogForm>
    <SLDrawer ref="refDetail" :config="detailConfig">
      <detail :config="data.selected" :show="7"></detail>
    </SLDrawer>
  </div>
</template>

<script lang="ts" setup>
import { ElMessage } from 'element-plus';
import {
  postConstructionContract,
  getConstructionContractType,
  getConstructionDevice
} from '@/api/engineeringManagement/manage';
import {
  postConstructionContractDevice,
  getConstructionContractDevice
} from '@/api/engineeringManagement/device';
import { deleteDeviceItemExport } from '@/api/engineeringManagement/projectManagement';
import detail from '../../../components/detail.vue';
import { formatDate } from '@/utils/DateFormatter';
import { traverse } from '@/utils/GlobalHelper';
import { ICONS } from '@/common/constans/common';
import { SLConfirm } from '@/utils/Message';

const refForm = ref<IDialogFormIns>();
const refDetail = ref<ISLDrawerIns>();
const refDeviceForm = ref<IDialogFormIns>();
const refAddDeviceForm = ref<IDialogFormIns>();

const props = defineProps<{
  config: {
    items: any;
  };
}>();
const emit = defineEmits(['extendedReturn']);

const TableConfig = reactive<ITable>({
  loading: false,
  indexVisible: true,
  dataList: computed(() => props.config.items) as any,
  columns: [
    { prop: 'code', label: '合同编号' },
    { prop: 'name', label: '合同名称' },
    { prop: 'typeName', label: '合同类型' },
    { prop: 'firstpartOrganization', label: '甲方单位' },
    { prop: 'secondpartOrganization', label: '乙方单位' },
    { prop: 'cost', label: '合同总金额(万元)' },
    {
      prop: 'signTime',
      label: '签署时间',
      formatter: (row) => formatDate(row.signTime, 'YYYY-MM-DD')
    },
    {
      prop: 'workTimeBegin',
      label: '合同工期(开始时间)',
      minWidth: '100px',
      formatter: (row) => formatDate(row.workTimeBegin, 'YYYY-MM-DD')
    },
    {
      prop: 'workTimeEnd',
      label: '合同工期(完成时间)',
      minWidth: '100px',
      formatter: (row) => formatDate(row.workTimeEnd, 'YYYY-MM-DD')
    },
    { prop: 'creatorName', label: '创建人' },
    {
      prop: 'createTime',
      label: '创建时间',
      formatter: (row) => formatDate(row.createTime, 'YYYY-MM-DD HH:mm:ss')
    }
  ],
  operationWidth: '260px',
  operations: [
    {
      text: '详情',
      perm: true,
      click: (row) => {
        data.selected = row;
        refDetail.value?.openDrawer();
      }
    },
    {
      text: '查看设备',
      perm: true,
      click: (row) => {
        deviceConfig.defaultValue = {
          code: row.code,
          constructionCode: row.constructionCode
        };
        data.getProjectDeviceValue();
        refDeviceForm.value?.openDialog();
      }
    },
    {
      text: '编辑',
      perm: true,
      click: (row) => clickEdit(row)
    },
    {
      text: '添加设备',
      perm: true,
      click: (row) => {
        deviceConfig.defaultValue = {
          code: row.code,
          constructionCode: row.constructionCode
        };
        const table = AdddeviceConfig.group[0].fields.find(
          (item) => item.type === 'table'
        ) as IFormTable;
        table.config.selectList = [];
        AdddeviceConfig.defaultValue = { code: row.code };
        data.geDeviceListValue();
        refAddDeviceForm.value?.openDialog();
      }
    }
  ],
  pagination: {
    hide: true
  }
});

const addOrUpdateConfig = reactive<IDialogFormConfig>({
  title: '添加工程合同',
  appendToBody: true,
  labelWidth: '130px',
  dialogWidth: '1000px',
  submitting: false,
  submit: (params: any) => {
    addOrUpdateConfig.submitting = true;
    let text = '新增';
    if (params.id) text = '修改';
    if (params.time) {
      params.workTimeBegin = params.time[0];
      params.workTimeEnd = params.time[1];
      delete params.time;
    }
    postConstructionContract(params)
      .then((res) => {
        addOrUpdateConfig.submitting = false;
        if (res.data.code === 200) {
          ElMessage.success(text + '成功');
          refForm.value?.closeDialog();
        } else {
          ElMessage.warning(text + '失败');
        }
        emit('extendedReturn', {});
      })
      .catch((error) => {
        addOrUpdateConfig.submitting = false;
        ElMessage.warning(error);
      });
  },
  defaultValue: {},
  group: [
    {
      fields: [
        {
          xs: 12,
          type: 'input',
          label: '合同编号',
          field: 'code',
          rules: [{ required: true, message: '请输入合同编号' }],
          disabled: true
        },
        {
          xs: 12,
          type: 'select',
          label: '合同分类',
          field: 'type',
          options: computed(() => data.ConstructionContractType) as any
        },
        {
          xs: 12,
          type: 'input',
          label: '合同名称',
          field: 'name',
          rules: [{ required: true, message: '请输入合同名称' }]
        },
        {
          xs: 12,
          type: 'input',
          label: '工程名称',
          field: 'constructionName',
          disabled: true
        },
        {
          xs: 12,
          type: 'input',
          label: '工程编号',
          field: 'constructionCode',
          disabled: true
        },
        {
          xs: 12,
          type: 'input',
          label: '甲方单位',
          field: 'firstpartOrganization',
          rules: [{ required: true, message: '请输入甲方单位' }]
        },
        {
          xs: 12,
          type: 'input',
          label: '乙方单位',
          field: 'secondpartOrganization',
          rules: [{ required: true, message: '请输入乙方单位' }]
        },
        {
          xs: 12,
          type: 'input',
          label: '甲方代表',
          field: 'firstpartRepresentative',
          rules: [{ required: true, message: '请输入甲方代表' }]
        },
        {
          xs: 12,
          type: 'input',
          label: '乙方联系人',
          field: 'secondpartRepresentative'
        },
        {
          xs: 12,
          type: 'input',
          label: '甲方代表联系电话',
          field: 'firstpartPhone'
        },
        {
          xs: 12,
          type: 'input',
          label: '乙方联系电话',
          field: 'secondpartPhone'
        },
        {
          xs: 12,
          type: 'number',
          label: '合同金额',
          field: 'cost',
          rules: [{ required: true, message: '请输入合同金额' }]
        },
        {
          xs: 12,
          type: 'daterange',
          label: '合同工期',
          field: 'time',
          format: 'x'
        },
        {
          xs: 12,
          type: 'date',
          label: '签署时间',
          field: 'signTime',
          format: 'x'
        },
        {
          type: 'textarea',
          label: '详细说明',
          field: 'remark'
        },
        {
          type: 'file',
          label: '附件',
          field: 'attachments'
        }
      ]
    }
  ]
});

const deviceConfig = reactive<IDialogFormConfig>({
  title: '查看设备',
  labelWidth: '130px',
  appendToBody: true,
  dialogWidth: '1000px',
  defaultValue: {},
  group: [
    {
      fields: [
        {
          type: 'table',
          field: 'deviceTable',
          config: {
            indexVisible: true,
            height: '350px',
            dataList: computed(() => data.deviceInformation) as any,
            columns: [
              { label: '设备名称', prop: 'deviceName' },
              { label: '设备编码', prop: 'serialId' },
              { label: '设备型号', prop: 'model' },
              { label: '所属大类', prop: 'deviceTopTypeName' },
              { label: '所属分类', prop: 'deviceType' },
              { label: '设备标识', prop: 'mark' },
              { label: '计量单位', prop: 'unit' },
              { label: '清单总量', prop: 'amount' }
            ],
            operations: [
              {
                perm: true,
                text: '删除',
                icon: ICONS.DELETE,
                click: (row) => {
                  SLConfirm('确定删除该设备', '删除提示').then(() => {
                    deleteDeviceItemExport(row.id)
                      .then(() => {
                        ElMessage.success('删除成功');
                        data.getProjectDeviceValue();
                      })
                      .catch((error) => {
                        ElMessage.warning(error);
                      });
                  });
                }
              }
            ],
            pagination: {
              hide: true
            }
          }
        }
      ]
    }
  ]
});

const AdddeviceConfig = reactive<IDialogFormConfig>({
  title: '添加设备',
  labelWidth: '80px',
  appendToBody: true,
  dialogWidth: '1000px',
  defaultValue: {},
  submitting: false,
  submit: (params: any, status: boolean) => {
    if (status) {
      data.geDeviceListValue(params);
    } else {
      let status = false;
      const table = AdddeviceConfig.group[0].fields.find(
        (item) => item.type === 'table'
      ) as IFormTable;
      if (table.config.selectList?.length === 0) {
        ElMessage.warning('请选中设备');
        status = true;
      }
      const val: any = table.config.selectList?.map((item) => {
        if (item.number === 0 || !item.number) {
          ElMessage.warning('数量最少为1台');
          status = true;
        }
        if (item.number > item.rest) {
          ElMessage.warning('申请数量超过剩余数量');
          status = true;
        }
        return {
          serialId: item.serialId,
          amount: item.number || 0
        };
      });
      if (status) {
        return;
      }
      AdddeviceConfig.submitting = true;
      postConstructionContractDevice(params.code, val)
        .then((res) => {
          AdddeviceConfig.submitting = false;
          if (res.data.code === 200) {
            ElMessage.success('添加成功');
            refAddDeviceForm.value?.closeDialog();
          } else {
            ElMessage.warning('添加失败');
          }
        })
        .catch((error) => {
          AdddeviceConfig.submitting = false;
          ElMessage.warning(error);
        });
    }
  },
  group: [
    {
      fields: [
        {
          xs: 6,
          type: 'input',
          field: 'serialId',
          label: '设备编码'
        },
        {
          xs: 6,
          type: 'input',
          field: 'name',
          label: '设备名称'
        },
        {
          xs: 6,
          type: 'input',
          field: 'model',
          label: '设备型号'
        },
        {
          xs: 6,
          type: 'btn-group',
          btns: [
            {
              text: '查询',
              perm: true,
              click: () => {
                refAddDeviceForm.value?.Submit(true);
              }
            },
            {
              text: '重置',
              perm: true,
              type: 'default',
              click: () => {
                refAddDeviceForm.value?.resetForm();
                data.geDeviceListValue();
              }
            }
          ]
        },
        {
          type: 'table',
          config: {
            indexVisible: true,
            height: '350px',
            dataList: computed(() => data.deviceInformation) as any,
            handleSelectChange: (val) => {
              const table = AdddeviceConfig.group[0].fields.find(
                (item) => item.type === 'table'
              ) as IFormTable;
              table.config.selectList = val;
            },
            selectList: [],
            columns: [
              { label: '设备名称', prop: 'deviceName' },
              { label: '设备编码', prop: 'serialId' },
              { label: '设备型号', prop: 'model' },
              { label: '所属大类', prop: 'deviceTopTypeName' },
              { label: '所属分类', prop: 'deviceType' },
              { label: '设备标识', prop: 'mark' },
              { label: '计量单位', prop: 'unit' },
              { label: '清单总量', prop: 'rest' },
              {
                label: '申请数量',
                prop: 'number',
                minWidth: '120px',
                formItemConfig: {
                  type: 'number',
                  field: 'number',
                  min: 0
                }
              }
            ],
            pagination: {
              page: 1,
              limit: 20,
              total: 0,
              refreshData: ({ page, size }) => {
                const table = AdddeviceConfig.group[0].fields.find(
                  (item) => item.type === 'table'
                ) as IFormTable;
                table.config.pagination.page = page;
                table.config.pagination.limit = size;
                data.geDeviceListValue();
              }
            }
          }
        }
      ]
    }
  ]
});

// 详情
const detailConfig = reactive<IDrawerConfig>({
  title: '详情',
  group: [],
  width: '80%',
  modalClass: 'lightColor',
  appendToBody: true,
  cancel: false
});

const clickEdit = (row: { [x: string]: any }) => {
  addOrUpdateConfig.title = '编辑签证';
  addOrUpdateConfig.defaultValue = {
    ...(row || {}),
    time: [row.workTimeBegin, row.workTimeEnd]
  };
  refForm.value?.openDialog();
};

const data = reactive({
  // 合同类型
  ConstructionContractType: [],
  // 设备信息
  deviceInformation: [],
  selected: {},
  getOptions: () => {
    getConstructionContractType({ page: 1, size: -1 }).then((res) => {
      data.ConstructionContractType = traverse(
        res.data.data.data || [],
        'children'
      );
    });
  },
  geDeviceListValue: (val?: any) => {
    data.deviceInformation = [];
    const table = AdddeviceConfig.group[0].fields.find(
      (item) => item.type === 'table'
    ) as IFormTable;
    const params = {
      page: table.config.pagination.page || 1,
      size: table.config.pagination.limit || 20,
      ...val
    };
    getConstructionDevice(
      deviceConfig.defaultValue?.constructionCode,
      params
    ).then((res) => {
      data.deviceInformation = res.data.data.data || [];
      table.config.pagination.total = res.data.data.total || 0;
    });
  },
  getProjectDeviceValue: () => {
    data.deviceInformation = [];
    const table = deviceConfig.group[0].fields.find(
      (item) => item.type === 'table'
    ) as IFormTable;
    const params = {
      page: table.config.pagination.page || 1,
      size: table.config.pagination.limit || 20
    };
    getConstructionContractDevice(deviceConfig.defaultValue?.code, params).then(
      (res) => {
        data.deviceInformation = res.data.data.data || [];
        table.config.pagination.total = res.data.data.total || 0;
      }
    );
  }
});

onMounted(() => {
  data.getOptions();
  data.geDeviceListValue();
});
</script>

<style lang="scss" scoped>
.team_table {
  width: 100%;
  padding: 5px 15px;
}
</style>
