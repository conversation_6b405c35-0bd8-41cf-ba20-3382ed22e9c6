import{d as _,g as f,n as g,p as s,bh as e,aw as w,q as l,F as p,G as d,J as b,C as h}from"./index-r0dFAfgr.js";const y={class:"water-plant-popup"},C={class:"popup-header"},k={class:"update-time"},B={class:"popup-content"},P={class:"data-grid"},N={class:"data-item"},S={class:"value"},z={class:"data-item"},D={class:"value"},V={class:"data-item"},E={class:"value"},T={class:"data-item"},W={class:"value"},q={class:"data-item"},F={class:"data-item"},G={class:"value"},H={class:"popup-actions"},I=_({__name:"WaterPlantPopup",props:{visible:{type:Boolean},config:{}},emits:["view-details","view-history"],setup(r,{emit:c}){const o=r,n=c,u=a=>{switch(a){case"正常":return"status-normal";case"异常":return"status-error";case"维护":return"status-warning";default:return"status-unknown"}},v=()=>{n("view-details",o.config)},m=()=>{n("view-history",o.config)};return(a,t)=>{const i=b;return f(),g("div",y,[s("div",C,[s("h3",null,e(a.config.stationName||"水厂信息"),1),s("span",k,"更新时间: "+e(a.config.lastUpdateTime),1)]),s("div",B,[s("div",P,[s("div",N,[t[0]||(t[0]=s("span",{class:"label"},"今日出水量:",-1)),s("span",S,e(a.config.waterOutput||"--"),1)]),s("div",z,[t[1]||(t[1]=s("span",{class:"label"},"出水压力:",-1)),s("span",D,e(a.config.waterPressure||"--"),1)]),s("div",V,[t[2]||(t[2]=s("span",{class:"label"},"清水池液位:",-1)),s("span",E,e(a.config.waterLevel||"--"),1)]),s("div",T,[t[3]||(t[3]=s("span",{class:"label"},"用电量:",-1)),s("span",W,e(a.config.powerConsumption||"--"),1)]),s("div",q,[t[4]||(t[4]=s("span",{class:"label"},"运行状态:",-1)),s("span",{class:w(["value",u(a.config.operationStatus)])},e(a.config.operationStatus||"未知"),3)]),s("div",F,[t[5]||(t[5]=s("span",{class:"label"},"上报日期:",-1)),s("span",G,e(a.config.reportDate||"--"),1)])]),s("div",H,[l(i,{type:"primary",size:"small",onClick:v},{default:p(()=>t[6]||(t[6]=[d(" 查看详情 ")])),_:1}),l(i,{type:"default",size:"small",onClick:m},{default:p(()=>t[7]||(t[7]=[d(" 历史数据 ")])),_:1})])])])}}}),L=h(I,[["__scopeId","data-v-6a24154a"]]);export{L as default};
