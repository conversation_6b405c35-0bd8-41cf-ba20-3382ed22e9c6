<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.dma.DmaJinzhouAnalysisMapper">

    <select id="getByCodesAndTime" resultType="org.thingsboard.server.dao.model.sql.dma.DmaJinzhouAnalysisEntity">
        select month, sum(supply) as supply, sum(use) as use, max(user_num) as user_num from dma_jinzhou_analysis
        where
              month &gt;= #{start} and month &lt; #{end}
              and codes in
              <foreach collection="code.split(',')" item="code" open="(" separator="," close=")">
                      #{code}
                  </foreach>
              group by month order by month
    </select>
</mapper>