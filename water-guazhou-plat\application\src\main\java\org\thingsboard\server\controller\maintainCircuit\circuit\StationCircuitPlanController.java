//package org.thingsboard.server.controller.maintainCircuit.circuit;
//
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.*;
//import org.thingsboard.server.common.data.UUIDConverter;
//import org.thingsboard.server.common.data.exception.ThingsboardException;
//import org.thingsboard.server.controller.base.BaseController;
//import org.thingsboard.server.dao.maintainCircuit.circuit.StationCircuitPlanService;
//import org.thingsboard.server.dao.model.request.StationCircuitPlanListRequest;
//import org.thingsboard.server.dao.model.sql.maintainCircuit.circuit.StationCircuitPlan;
//import org.thingsboard.server.dao.util.imodel.response.IstarResponse;
//
//import java.util.List;
//
//@RestController
//@RequestMapping("api/stationCircuitPlan")
//public class StationCircuitPlanController extends BaseController {
//
//    @Autowired
//    private StationCircuitPlanService stationCircuitPlanService;
//
//    @PostMapping
//    public IstarResponse save(@RequestBody StationCircuitPlan entity) throws ThingsboardException {
//        entity.setTenantId(UUIDConverter.fromTimeUUID(getTenantId().getId()));
//        stationCircuitPlanService.save(entity);
//        return IstarResponse.ok();
//    }
//
//    @GetMapping("list")
//    public IstarResponse findList(StationCircuitPlanListRequest request) throws ThingsboardException {
//        request.setTenantId(UUIDConverter.fromTimeUUID(getTenantId().getId()));
//
//        return IstarResponse.ok(stationCircuitPlanService.findList(request));
//    }
//
//    @DeleteMapping("remove")
//    public IstarResponse remove(@RequestBody List<String> ids) {
//        stationCircuitPlanService.remove(ids);
//        return IstarResponse.ok();
//    }
//
//}
