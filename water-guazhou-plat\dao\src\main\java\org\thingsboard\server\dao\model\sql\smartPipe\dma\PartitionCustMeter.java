package org.thingsboard.server.dao.model.sql.smartPipe.dma;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import java.util.Date;

/**
 *
 */
@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@TableName(ModelConstants.PIPE_PARTITION_CUST_METER_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class PartitionCustMeter {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @TableField(ModelConstants.PIPE_PARTITION_CUST_METER_PARTITION_ID)
    private String partitionId;

    @TableField(ModelConstants.PIPE_PARTITION_CUST_METER_CALIBER)
    private String caliber;

    @TableField(ModelConstants.PIPE_PARTITION_CUST_METER_NUM)
    private Integer num;

    @TableField(ModelConstants.PIPE_PARTITION_CUST_METER_BRAND)
    private String brand;

    @TableField(ModelConstants.PIPE_PARTITION_CUST_METER_TYPE)
    private String type;

    @TableField(ModelConstants.PIPE_PARTITION_CUST_METER_IS_COLLECT_COPY)
    private String isCollectCopy;

    @TableField(ModelConstants.PIPE_PARTITION_CUST_METER_REMARK)
    private String remark;

    @TableField(ModelConstants.PIPE_PARTITION_CUST_METER_IMG)
    private String img;

    @TableField(ModelConstants.CREATE_TIME)
    private Date createTime;

    @TableField(ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;

}
