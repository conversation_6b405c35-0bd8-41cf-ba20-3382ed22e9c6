package org.thingsboard.server.dao.util;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.client.RestTemplate;
import org.thingsboard.server.dao.util.imodel.query.smartService.wechat.ValueColorPair;
import org.thingsboard.server.dao.util.reflection.ExceptionUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Slf4j
public class WechatUtils {
    private final static String wxTokenQueryURL = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s";
    private final static String userOpenIdListQueryURL = "https://api.weixin.qq.com/cgi-bin/user/get?access_token=%s";
    private final static String nextUserOpenIdListQueryURL = "https://api.weixin.qq.com/cgi-bin/user/get?access_token=%s&next_openid=%s";
    private final static String sendTemplateMessageURL = "https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=%s";


    public static TokenResponse getTokenResponse(RestTemplate restTemplate, String appId, String secret) {
        TokenResponse response = restTemplate.getForObject(
                String.format(wxTokenQueryURL, appId, secret)
                , TokenResponse.class);
        if (response.getErrcode() != null) {
            ExceptionUtils.silentThrow("获取Token失败,code:%s\n%s", response.getErrcode(), response.getErrmsg());
        }
        return response;
    }

    public static Long sendTemplateMessage(RestTemplate restTemplate, String accessToken, String url, String toUserOpenId, String wxTemplateId, Map<String, ValueColorPair> data) {
        SendTemplateMessageRequest body = new SendTemplateMessageRequest(toUserOpenId, url, wxTemplateId, data);
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<SendTemplateMessageRequest> http = new HttpEntity<>(body, headers);
        SendTemplateMessageResponse response = restTemplate.postForObject(
                String.format(sendTemplateMessageURL, accessToken)
                , http, SendTemplateMessageResponse.class);

        if (response.getErrcode() != null && response.getErrcode() != 0) {
            log.error("[wechat] unable to send message code: {} msg: {}", response.getErrcode(), response.getErrmsg());
            return -1L;
        }

        return response.getMsgid();
    }

    public static UserOpenIdListResponse getUserList(RestTemplate restTemplate, String accessToken) {
        UserOpenIdListResponse response = restTemplate.getForObject(
                String.format(userOpenIdListQueryURL, accessToken)
                , UserOpenIdListResponse.class);
        if (response.getErrcode() != null) {
            ExceptionUtils.silentThrow("请求用户列表失败,code:%s\n%s", response.getErrcode(), response.getErrmsg());
        }
        return response;
    }

    public static UserOpenIdListResponse getNextUserList(RestTemplate restTemplate, String accessToken, String nextOpenId) {
        UserOpenIdListResponse response = restTemplate.getForObject(
                String.format(nextUserOpenIdListQueryURL, accessToken, nextOpenId)
                , UserOpenIdListResponse.class);
        if (response.getErrcode() != null) {
            ExceptionUtils.silentThrow("请求用户列表失败,code:%s\n%s", response.getErrcode(), response.getErrmsg());
        }
        return response;
    }


    // region response
    @Getter
    @Setter
    public static class TokenResponse extends WechatResponseEntity {
        private String access_token;

        private Integer expires_in;

        public Date getExpireTime() {
            return new DateTime().plusSeconds(expires_in).minusMinutes(5).toDate();
        }
    }

    @Setter
    public static class UserOpenIdListResponse extends WechatResponseEntity {
        @Getter
        private Integer total;

        @Getter
        private Integer count;

        private UserOpenIdListWrapper data;

        @Getter
        private String next_openid;

        public boolean isEmpty() {
            return data == null || data.isEmpty();
        }

        public List<String> getOpenIdList() {
            return data.getOpenid();
        }

    }

    @Getter
    @Setter
    public static class UserOpenIdListWrapper {
        List<String> openid;

        public boolean isEmpty() {
            return openid.isEmpty();
        }
    }

    @Getter
    @Setter
    public static class SendTemplateMessageResponse extends WechatResponseEntity {
        private Long msgid;

    }

    @Getter
    @Setter
    public static class WechatResponseEntity {
        private Integer errcode;

        private String errmsg;

    }
    // endregion


    // region request
    @Getter
    @Setter
    public static class SendTemplateMessageRequest {
        private String touser;

        private String url;

        private String template_id;

        private String topcolor = "#FF0000";

        private Map<String, ValueColorPair> data;

        public SendTemplateMessageRequest(String touser, String url, String template_id, Map<String, ValueColorPair> data) {
            this.url = url;
            this.touser = touser;
            this.template_id = template_id;
            this.data = data;
        }
    }
    // endregion
}
