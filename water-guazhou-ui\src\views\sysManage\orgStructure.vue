<!-- 供水单位 | 组织架构 -->
<template>
  <div class="wrapper">
    <CardSearch
      ref="refSearch"
      :config="cardSearchConfig"
    />
    <CardTable
      :config="TableConfig"
      class="card-table"
    ></CardTable>
    <!-- 添加下级 -->
    <DialogForm
      ref="refForm1"
      :config="addLowerLevel"
    ></DialogForm>
  </div>
</template>

<script lang="ts" setup>
import { ICONS } from '@/common/constans/common'
import { ICardSearchIns, IDialogFormIns } from '@/components/type'
import useGlobal from '@/hooks/global/useGlobal'
import { SLConfirm, SLMessage } from '@/utils/Message'
import { patchDepartment, getDepartment, deleteDepartment, postDepartment, getWaterSupplyTree } from '@/api/company_org'

const { $btnPerms } = useGlobal()

const refSearch = ref<ICardSearchIns>()

const refForm1 = ref<IDialogFormIns>()

const cardSearchConfig = ref<ISearch>({
  filters: [
    { label: '供水单位/部门名称', field: 'name', type: 'input', labelWidth: '140px' },
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          icon: ICONS.QUERY,
          click: () => refreshData()
        }
      ]
    }
  ]
})

const TableConfig = reactive<ICardTable>({
  defaultExpandAll: true,
  rowKey: 'id',
  columns: [
    { label: '供水单位/部门标号', prop: 'id' },
    {
      label: '供水单位名称',
      prop: 'name',
      formatter: row => {
        if (row.layer === 1) return row.name
        return ' '
      }
    },
    {
      label: '部门名称',
      prop: 'name',
      formatter: row => {
        if (row.layer === '2' || row.layer === 2) return row.name
        return ' '
      }
    },
    { label: '部门类型', prop: 'type' },
    { label: '排序编号', prop: 'orderNum' }
  ],
  operationWidth: '200px',
  operations: [
    {
      type: 'success',
      isTextBtn: true,
      color: '#4195f0',
      text: '增下级',
      perm: $btnPerms('RoleManageEdit'),
      icon: ICONS.ADD,
      click: row => clickaddLowerLevel(row)
    },
    {
      type: 'primary',
      isTextBtn: true,
      color: '#4195f0',
      text: '编辑',
      perm: $btnPerms('RoleManageEdit'),
      hide: row => row.layer === 1,
      icon: ICONS.DETAIL,
      click: row => clickaddLowerLevel(row, true)
    },
    {
      isTextBtn: true,
      type: 'danger',
      text: '删除',
      hide: row => row.layer === 1,
      icon: ICONS.DELETE,
      perm: $btnPerms('RoleManageDelete'),
      click: row => haneleDelete(row)
    }
  ],
  dataList: [],
  pagination: {
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page
      TableConfig.pagination.limit = size
      // refreshData()
    }
  }
})

// 添加下级
const addLowerLevel = reactive<IDialogFormConfig>({
  title: '部门信息',
  dialogWidth: '500px',
  submit: (params: any) => {
    if (params.id) {
      patchDepartment(params.id, params).then(res => {
        if (res.data.code === 200) {
          refreshData()
          refForm1.value?.closeDialog()
          return
        }
        SLMessage.error(res.data.message)
      }).catch(err => {
        SLMessage.error(err.data.message)
      })
    } else {
      postDepartment(params).then(res => {
        if (res.data.code === 200) {
          refreshData()
          refForm1.value?.closeDialog()
          return
        }
        SLMessage.error(res.data.message)
      }).catch(err => {
        SLMessage.error(err.data.message)
      })
    }
  },

  defaultValue: {},
  group: [
    {
      fields: [
        {
          type: 'input',
          label: '部门名称',
          field: 'name',
          rules: [{ required: true, message: '请输入部门名称' }]
        }, {
          type: 'select',
          label: '上级部门',
          field: 'parentId',
          options: [],
          readonly: true,
          rules: [{ required: true, message: '请选择上级部门' }]
        }, {
          type: 'select',
          label: '部门类型',
          field: 'type',
          options: [
            { label: '财务', value: '财务' },
            { label: '报装', value: '报装' },
            { label: '设备安装', value: '设备安装' },
            { label: '客服', value: '客服' },
            { label: '供水', value: '供水' },
            { label: '单位管理', value: '单位管理' },
            { label: '营业', value: '营业' },
            { label: '办公室', value: '办公室' },
            { label: '企划部', value: '企划部' },
            { label: '技术开发', value: '技术开发' },
            { label: '人力资源', value: '人力资源' }
          ],
          rules: [{ required: true, message: '请选择部门类型' }]
        }, {
          type: 'number',
          label: '排序',
          field: 'orderNum',
          min: 0
        }
      ]
    }
  ]
})

const clickaddLowerLevel = (row: { [x: string]: any }, state = false) => {
  if (state) {
    addLowerLevel.defaultValue = { ...(row) || {} };
    (addLowerLevel.group[0].fields[1] as any).options = [{ label: row.parentName || '' || '', value: row.parentId }]
  } else {
    addLowerLevel.defaultValue = { orderNum: '0', parentId: row.id || '' || {} };
    (addLowerLevel.group[0].fields[1] as any).options = [{ label: row.name || '', value: row.id }]
  }
  refForm1.value?.openDialog()
}

const haneleDelete = (row: { id: any }) => {
  SLConfirm('确定删除该部门, 是否继续?', '删除提示').then(() => {
    deleteDepartment(row.id)
      .then(() => {
        refreshData()
        SLMessage.success('删除成功')
      })
      .catch(err => {
        console.log(err, 'err')
        SLMessage.error(err.data.message)
      })
  })
}

const refreshData = async () => {
  const depth = 2
  let res: any
  if (refSearch.value?.queryParams?.name) {
    res = await getDepartment({ name: refSearch.value?.queryParams?.name || '' })
  } else {
    res = await getWaterSupplyTree(depth)
  }
  console.log(res.data)
  TableConfig.dataList = res.data.data || []
  TableConfig.pagination.total = res.data.total || 0
}

onMounted(() => {
  refreshData()
})
</script>

<style lang="scss">
.el-table__placeholder {
  display: none;
}
</style>
