import{d as k,c as m,g as l,n as c,aJ as d,aB as o,h as v,F as a,q as s,i,cs as b,J as h,ct as x,C as y}from"./index-r0dFAfgr.js";const C={key:0,class:"interval"},w=k({__name:"btns",emits:["changeFullScreen","setView","CloseAll"],setup(B,{emit:r}){const t=r,p=m([{title:"全部关闭",onclick:n=>{t("CloseAll")},icon:"material-symbols:tab-close"},{type:"interval",icon:""},{title:"一分屏",onclick:n=>{t("setView",24,1)},icon:"ph:number-square-one-fill"},{title:"四分屏",onclick:n=>{t("setView",12,4)},icon:"ph:number-square-four-fill"},{title:"九分屏",onclick:n=>{t("setView",8,9)},icon:"ph:number-square-nine-fill"},{type:"interval",icon:""},{title:"最大化",onclick:n=>{t("changeFullScreen")},icon:"tabler:arrows-maximize"}]);return(n,V)=>{const u=h,_=x;return l(!0),c(o,null,d(i(p),(e,f)=>(l(),c(o,{key:f},[e.type==="interval"?(l(),c("div",C)):(l(),v(_,{key:1,class:"box-item",effect:"dark",content:e.title,placement:"top"},{default:a(()=>[s(u,{text:"",onClick:g=>e.onclick&&e.onclick(e)},{default:a(()=>[s(i(b),{icon:e.icon,style:{"font-size":"20px"}},null,8,["icon"])]),_:2},1032,["onClick"])]),_:2},1032,["content"]))],64))),128)}}}),F=y(w,[["__scopeId","data-v-e3dc2dff"]]);export{F as default};
