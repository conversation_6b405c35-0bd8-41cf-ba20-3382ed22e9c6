package org.thingsboard.server.dao.util.imodel.query.cache;

import org.thingsboard.server.dao.util.imodel.query.annotations.QueryEquals;
import org.thingsboard.server.dao.util.imodel.query.annotations.QueryIgnore;
import org.thingsboard.server.dao.util.imodel.query.cache.info.QueryEqualFieldInfo;
import org.thingsboard.server.dao.util.imodel.query.cache.info.QueryLikeFieldInfo;
import org.thingsboard.server.dao.util.imodel.query.CriteriaBuilderWrapper;
import org.thingsboard.server.dao.util.imodel.query.annotations.QueryLike;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class QueryCacheContainer {
    private final Map<Class<?>, PageQueryCache> pageQueryCacheMap = new HashMap<>();

    public void resolve(Class<?> clazz, Object entity, CriteriaBuilderWrapper wrapper) {
        PageQueryCache cache = get(clazz);
        cache.process(entity, wrapper);
    }

    private PageQueryCache get(Class<?> clazz) {
        return pageQueryCacheMap.computeIfAbsent(clazz, key -> {
            // 自定义查询过滤
            List<QueryFieldInfo<?>> infoList = new ArrayList<>();
            for (Field field : clazz.getDeclaredFields()) {
                if (field.isAnnotationPresent(QueryIgnore.class))
                    continue;

                Class<?> type = field.getType();
                // String分模糊匹配和直接相等
                if (String.class.isAssignableFrom(type)) {
                    if (field.isAnnotationPresent(QueryLike.class)) {
                        // like查询缓存
                        QueryLike queryLike = field.getAnnotation(QueryLike.class);
                        boolean or = queryLike.value();
                        String prefix = queryLike.prefix();
                        String suffix = queryLike.suffix();
                        infoList.add(new QueryLikeFieldInfo(or, field, prefix, suffix));
                    } else if (field.isAnnotationPresent(QueryEquals.class)) {
                        // equals查询缓存
                        boolean or = field.getAnnotation(QueryEquals.class).value();
                        infoList.add(new QueryEqualFieldInfo(or, field));
                    } else {
                        // equals查询 使用默认值
                        infoList.add(new QueryEqualFieldInfo(false, field));
                    }
                } else {
                    // 其它的直接判断相等 使用and操作
                    infoList.add(new QueryEqualFieldInfo(false, field));
                }
            }
            return new PageQueryCache(infoList);
        });
    }

}
