package org.thingsboard.server.dao.sql.medicineManage;

import org.springframework.data.jpa.repository.JpaRepository;
import org.thingsboard.server.dao.medicineManage.WuniService;
import org.thingsboard.server.dao.model.sql.WuniInput;

import java.util.Date;
import java.util.List;

public interface WuniRepository extends JpaRepository<WuniInput, String> {

    List<WuniInput> findByTimeBetweenOrderByTimeAsc(Date start, Date end);

}
