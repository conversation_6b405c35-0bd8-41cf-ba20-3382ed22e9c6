import{u as $}from"./IdentifyResult-4DxLVhTm.js";import{f as p}from"./identify-4SBo5EZk.js";import{U as h}from"./pe-B8dP0-Ut.js";import{t as n}from"./Point-WxyopZva.js";const G=e=>new $({layerIds:[1,2,3,4,5],layerOption:"all",tolerance:1,returnGeometry:!0,returnFieldName:!1,...e||{}}),j=(e,r)=>p(e,r),M=async(e,r,a,y)=>{const t=e.extent.toJSON(),o=e.width,i=e.height,m=Math.floor(y.x),c=Math.floor(y.y),s={xmin:n({x:t.xmin,y:t.ymin}).x,ymin:n({x:t.xmin,y:t.ymin}).y,xmax:n({x:t.xmax,y:t.ymax}).x,ymax:n({x:t.xmax,y:t.ymax}).y},x=`${r}?service=WMS&version=1.1.1&request=GetFeatureInfo&layers=${a}&bbox=${s.xmin},${s.ymin},${s.xmax},${s.ymax}&width=${o}&height=${i}&query_layers=${a}&info_format=application/json&x=${m}&y=${c}&feature_count=10&srs=EPSG:4326`;return await h(x,{responseType:"json"}).catch(function(u){console.error("查询失败：",u)})},S=async(e,r,a)=>{const y=r.url,t=r.sublayers.map(l=>l.name).join(","),o=e.extent.toJSON(),i=e.width,m=e.height,c=Math.floor(a.x),s=Math.floor(a.y),x={xmin:n({x:o.xmin,y:o.ymin}).x,ymin:n({x:o.xmin,y:o.ymin}).y,xmax:n({x:o.xmax,y:o.ymax}).x,ymax:n({x:o.xmax,y:o.ymax}).y},f=`${y}?service=WMS&version=1.1.1&request=GetFeatureInfo&layers=${t}&bbox=${x.xmin},${x.ymin},${x.xmax},${x.ymax}&width=${i}&height=${m}&query_layers=${t}&info_format=application/json&x=${c}&y=${s}&srs=EPSG:4326`;return await h(f,{responseType:"json"}).catch(function(l){console.error("查询失败：",l)})};export{M as a,S as b,j as e,G as i};
