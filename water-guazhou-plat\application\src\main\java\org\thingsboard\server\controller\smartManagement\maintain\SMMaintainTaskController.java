package org.thingsboard.server.controller.smartManagement.maintain;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;;
import org.thingsboard.server.dao.model.sql.smartManagement.maintaince.SMMaintainTask;
import org.thingsboard.server.dao.model.sql.statistic.GeneralTaskStatusStatistic;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.maintain.SMMaintainTaskAssignRequest;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.maintain.SMMaintainTaskCompleteRequest;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.maintain.SMMaintainTaskPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.maintain.SMMaintainTaskSaveRequest;
import org.thingsboard.server.dao.maintain.SMMaintainTaskService;
import org.thingsboard.server.utils.imodel.annotations.IStarController;

import java.util.List;

import static org.thingsboard.server.dao.model.sql.smartProduction.circuit.GeneralTaskStatus.APPROVED;

@IStarController
@RequestMapping("/api/sm/maintainTask")
public class SMMaintainTaskController extends BaseController {
    @Autowired
    private SMMaintainTaskService service;


    @GetMapping
    public IPage<SMMaintainTask> findAllConditional(SMMaintainTaskPageRequest request) {
        return service.findAllConditional(request);
    }

    @PostMapping
    public SMMaintainTask save(@RequestBody SMMaintainTaskSaveRequest req) {
        return service.save(req);
    }

    @PostMapping("/{id}/assign")
    public boolean assign(@RequestBody SMMaintainTaskAssignRequest req, @PathVariable String id) {
        req.setTaskId(id);
        return service.assign(req);
    }

    @PostMapping("/{id}/complete")
    public boolean assign(@RequestBody SMMaintainTaskCompleteRequest req, @PathVariable String id) {
        req.setTaskId(id);
        return service.complete(req);
    }

    @PostMapping("/countComplete")
    public GeneralTaskStatusStatistic countComplete() throws ThingsboardException {
        String useId = UUIDConverter.fromTimeUUID(getCurrentUser().getId().getId());
        return service.countStatusByUser(useId, APPROVED);
    }

    // @PatchMapping("/{id}")
    public boolean edit(@RequestBody SMMaintainTaskSaveRequest req, @PathVariable String id) {
        return service.update(req.unwrap(id));
    }

    @DeleteMapping("/{id}")
    public boolean delete(@PathVariable String id) {
        return service.delete(id);
    }

    @DeleteMapping
    public boolean deleteAll(@RequestBody List<String> idList) {
        return service.deleteAll(idList);
    }


    @GetMapping("statusCount")
    public JSONObject statusCount() throws ThingsboardException {
        return service.statusCount(getTenantId());
    }
}