import{j as t}from"./index-r0dFAfgr.js";const l=(i,e)=>({title:{text:e.title,textStyle:{fontSize:14,color:t().isDark?"#ddd":"#7C8295"},left:"center",top:"bottom"},series:[{type:"gauge",axisLine:{lineStyle:{width:20,color:[[.3,"#67e0e3"],[.7,"#37a2da"],[1,"#fd666d"]]}},pointer:{itemStyle:{color:"inherit"},length:"40%"},axisTick:{distance:15,length:4,lineStyle:{color:t().isDark?"#ddd":"#7C8295",width:1}},splitLine:{distance:15,length:8,lineStyle:{color:t().isDark?"#ddd":"#7C8295",width:2}},axisLabel:{color:"inherit",distance:30,fontSize:10},detail:{valueAnimation:!0,formatter:"{value}",color:"inherit",fontSize:20,offsetCenter:[0,"90%"]},max:e.max||100,min:0,data:[{value:i||0}]}]});export{l as g};
