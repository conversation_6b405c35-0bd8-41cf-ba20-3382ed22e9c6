package org.thingsboard.server.dao.util.imodel.response.model;


import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.id.UserId;
import org.thingsboard.server.dao.util.imodel.response.exception.CustomFailureException;
import org.thingsboard.server.dao.util.imodel.response.exception.ViewChangeException;
import org.thingsboard.server.dao.util.imodel.response.handler.CustomFailureHandler;
import org.thingsboard.server.dao.util.imodel.response.handler.ValidationFailureHandler;

import java.util.Set;
import java.util.UUID;

@Deprecated
public interface IModel {
    // region 数据操作
    Set<String> keySet();

    Object put(String key, Object value);

    Object get(Object key);

    void clear();

    default IModel add(String key, Object value) {
        put(key, value);
        return this;
    }

    default IModel code(Integer code) {
        put("code", code);
        return this;
    }

    default IModel err(String msg) {
        code(500);
        put("err", msg);
        return this;
    }

    default IModel info(String msg) {
        code(200);
        put("info", msg);
        return this;
    }

    default IModel warn(String msg) {
        code(200);
        put("warn", msg);
        return this;
    }

    default IModel success() {
        code(200);
        return this;
    }

    default IModel success(Object o) {
        code(200);
        data(o);
        return this;
    }

    default IModel data(Object o) {
        put("data", o);
        return this;
    }
    // endregion

    // region 框架功能
    default void failure(String pattern, Object... args) throws CustomFailureException {
        clear();
        err(String.format(pattern, args));
        throw new CustomFailureException(pattern);
    }

    default void failureIf(String message, boolean condition, Object... args) throws CustomFailureException {
        if (condition)
            failure(message, args);
    }

    default void failureIfNot(String message, boolean condition, Object... args) throws CustomFailureException {
        failureIf(message, !condition, args);
    }

    void valid(String valid);

    boolean valid();

    void redirectTo(String viewName) throws ViewChangeException;

    void forwardTo(String viewName) throws ViewChangeException;

    String currentPath();

    void invalidCallback(ValidationFailureHandler callback);

    void failureCallback(CustomFailureHandler callback);

    CustomFailureHandler failureCallback();

    ValidationFailureHandler validationFailureDelegate();

    void reclaim();
    // endregion

    // region 工具方法
    default String resolveUUID(UUID rawUUID) {
        return UUIDConverter.fromTimeUUID(rawUUID);
    }

    default String resolveUUID(TenantId tenant) {
        return resolveUUID(tenant.getId());
    }

    default String resolveUUID(UserId user) {
        return resolveUUID(user.getId());
    }

    // endregion
}
