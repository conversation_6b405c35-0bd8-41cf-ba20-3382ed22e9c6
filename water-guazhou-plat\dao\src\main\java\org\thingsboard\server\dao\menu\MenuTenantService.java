/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.menu;

import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.MenuPoolId;
import org.thingsboard.server.common.data.id.MenuTenantId;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.menu.Menu;
import org.thingsboard.server.common.data.menu.MenuPoolVO;
import org.thingsboard.server.common.data.menu.MenuTenant;

import java.util.List;

public interface MenuTenantService {

    /**
     * 保存
     * @param menuPoolIdList
     * @param tenantId
     * @return
     */
    List<MenuTenant> saveMenuTenant(List<MenuPoolId> menuPoolIdList, TenantId tenantId) throws ThingsboardException;

    /**
     * 按ID查询
     * @param menuTenantId
     * @return
     */
    Menu findMenuById(MenuTenantId menuTenantId);

    /**
     * 按TenantID查询
     * @param tenantId
     * @return
     */
    List<Menu> findMenuByTenantId(TenantId tenantId);

    /**
     * 按TenantID查询
     * @param tenantId
     * @return
     */
    List<MenuPoolVO> findMenuPoolVOByTenantId(TenantId tenantId);

    /**
     * 获取指定租户的菜单树
     * @param tenantId
     * @return
     */
    List<String> getTreeByTenantId(TenantId tenantId);

    List<MenuTenant> findAll(TenantId tenantId);
}
