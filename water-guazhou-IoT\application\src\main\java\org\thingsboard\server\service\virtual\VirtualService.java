/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.service.virtual;

import com.google.common.util.concurrent.ListenableFuture;
import org.thingsboard.server.common.data.device.DeviceRealTime;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.DeviceId;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.virtual.Virtual;

import java.math.BigDecimal;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

public interface VirtualService {
    ListenableFuture<List<Virtual>> findVirtualByTenant(TenantId tenantId);

    ListenableFuture<List<Virtual>> findVirtualByTenantAndType(TenantId tenantId, String type);

    ListenableFuture<Virtual> findByVirtualId(String virtualId);

    ListenableFuture<List<Virtual>> findAll();

    boolean delVirtual(TenantId tenantId, List<String> virtuals);

    LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> getFormulaData(List<String> formula, long start, long end, String type, TenantId tenantId) throws ThingsboardException;

    LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> getFormulaLastData(List<String> formula, long start, long end, String type, TenantId tenantId) throws ThingsboardException;

    LinkedHashMap<String, LinkedHashMap<String, BigDecimal>> getVirtualData(long start, long end, List<String> virtualId, String type, String readmeterTime, TenantId tenantId) throws ThingsboardException;

    LinkedHashMap<String, BigDecimal> getDataFromTsdb(TenantId tenantId, long start, long end, String formal, String type, String readmeterTime);

    String handleDataByFormula(LinkedHashMap<String, BigDecimal> data, List<String> formulaList);

    List<Virtual> virtualAdd(List<Virtual> virtuals) throws ThingsboardException;

    ListenableFuture<List<Virtual>> findBySerialNumber(String serialNumber);

    LinkedHashMap<String, DeviceRealTime> getRealTimeData(List<String> formula, long start, long end, TenantId tenantId, String timeLimit) throws ThingsboardException;

    Object getLogicReading(List<String> formula, long start, long end, String type, TenantId tenantId) throws ThingsboardException;

    String getLastDataVirtual(String virtualId, Map<String, String> data, DeviceId deviceId);

    List<Virtual> findVirtualByProjectId(String projectId);
}
