{"widgetsBundle": {"alias": "charts", "title": "Charts", "image": null}, "widgetTypes": [{"alias": "bars", "name": "Bars - Chart.js", "descriptor": {"type": "latest", "sizeX": 7, "sizeY": 5, "resources": [{"url": "https://cdnjs.cloudflare.com/ajax/libs/Chart.js/2.3.0/Chart.min.js"}], "templateHtml": "<canvas id=\"barChart\"></canvas>\n", "templateCss": "", "controllerScript": "self.onInit = function() {\n    var barData = {\n        labels: [],\n        datasets: []\n    };\n    \n    for (var i = 0; i < self.ctx.datasources.length; i++) {\n        var datasource = self.ctx.datasources[i];\n        for (var d = 0; d < datasource.dataKeys.length; d++) {\n            var dataset = {\n                label: datasource.dataKeys[d].label,\n                data: [0],\n                backgroundColor: [datasource.dataKeys[d].color],\n                borderColor: [datasource.dataKeys[d].color],\n                borderWidth: 1\n            }\n            barData.datasets.push(dataset);\n        }\n    }\n\n    var ctx = $('#barChart', self.ctx.$container);\n    self.ctx.chart = new Chart(ctx, {\n        type: 'bar',\n        data: barData,\n        options: {\n            responsive: false,\n            maintainAspectRatio: false,\n            scales: {\n                yAxes: [{\n                    ticks: {\n                        beginAtZero:true\n                    }\n                }]\n            }\n        }\n    });\n    \n    self.onResize();\n}\n\nself.onDataUpdated = function() {\n    var c = 0;\n    for (var i = 0; i < self.ctx.chart.data.datasets.length; i++) {\n        var dataset = self.ctx.chart.data.datasets[i];\n        var cellData = self.ctx.data[i]; \n        if (cellData.data.length > 0) {\n             var tvPair = cellData.data[cellData.data.length - 1];\n             var value = tvPair[1];\n             dataset.data[0] = parseFloat(value);\n        }\n    }\n    self.ctx.chart.update();\n}\n\nself.onResize = function() {\n    self.ctx.chart.resize();\n}\n\n", "settingsSchema": "{}", "dataKeySettingsSchema": "{}\n", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"First\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.15479322438769105,\"funcBody\":\"var value = (prevValue-50) + Math.random() * 2 - 1;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 100) {\\n\\tvalue = 100;\\n}\\nreturn value+50;\"},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Second\",\"color\":\"#4caf50\",\"settings\":{},\"_hash\":0.545701115289893,\"funcBody\":\"var value = (prevValue-20) + Math.random() * 2 - 1;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 100) {\\n\\tvalue = 100;\\n}\\nreturn value+20;\"},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Third\",\"color\":\"#f44336\",\"settings\":{},\"_hash\":0.2592906835158064,\"funcBody\":\"var value = (prevValue-40) + Math.random() * 2 - 1;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 100) {\\n\\tvalue = 100;\\n}\\nreturn value+40;\"},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Fourth\",\"color\":\"#ffc107\",\"settings\":{},\"_hash\":0.12880275585455747,\"funcBody\":\"var value = (prevValue-50) + Math.random() * 2 - 1;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 100) {\\n\\tvalue = 100;\\n}\\nreturn value+50;\"}]}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":true,\"backgroundColor\":\"#fff\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"8px\",\"settings\":{},\"title\":\"Bars - Chart.js\"}"}}, {"alias": "basic_timeseries", "name": "Timeseries - Flot", "descriptor": {"type": "timeseries", "sizeX": 8, "sizeY": 5, "resources": [], "templateHtml": "", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.flot = new TbFlot(self.ctx);    \n}\n\nself.onDataUpdated = function() {\n    self.ctx.flot.update();\n}\n\nself.onResize = function() {\n    self.ctx.flot.resize();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.flot.checkMouseEvents();\n}\n\nself.onMobileModeChanged = function() {\n    self.ctx.flot.checkMouseEvents();\n}\n\nself.getSettingsSchema = function() {\n    return TbFlot.settingsSchema('graph');\n}\n\nself.getDataKeySettingsSchema = function() {\n    return TbFlot.datakeySettingsSchema(true);\n}\n\nself.onDestroy = function() {\n    self.ctx.flot.destroy();\n}\n", "settingsSchema": "{}", "dataKeySettingsSchema": "{}", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"First\",\"color\":\"#2196f3\",\"settings\":{\"showLines\":true,\"fillLines\":true,\"showPoints\":false},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 100 - 50;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < -1000) {\\n\\tvalue = -1000;\\n} else if (value > 1000) {\\n\\tvalue = 1000;\\n}\\nreturn value;\"},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Second\",\"color\":\"#ffc107\",\"settings\":{\"showLines\":true,\"fillLines\":false,\"showPoints\":false},\"_hash\":0.12775350966079668,\"funcBody\":\"var value = prevValue + Math.random() * 100 - 50;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < -1000) {\\n\\tvalue = -1000;\\n} else if (value > 1000) {\\n\\tvalue = 1000;\\n}\\nreturn value;\"}]}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":true,\"backgroundColor\":\"#fff\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"8px\",\"settings\":{\"shadowSize\":4,\"fontColor\":\"#545454\",\"fontSize\":10,\"xaxis\":{\"showLabels\":true,\"color\":\"#545454\"},\"yaxis\":{\"showLabels\":true,\"color\":\"#545454\"},\"grid\":{\"color\":\"#545454\",\"tickColor\":\"#DDDDDD\",\"verticalLines\":true,\"horizontalLines\":true,\"outlineWidth\":1},\"legend\":{\"show\":true,\"position\":\"nw\",\"backgroundColor\":\"#f0f0f0\",\"backgroundOpacity\":0.85,\"labelBoxBorderColor\":\"rgba(1, 1, 1, 0.45)\"},\"decimals\":1,\"stack\":false,\"tooltipIndividual\":false},\"title\":\"Timeseries - Flot\",\"dropShadow\":true,\"enableFullscreen\":true,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"mobileHeight\":null}"}}, {"alias": "doughnut_chart_js", "name": "Doughnut - Chart.js", "descriptor": {"type": "latest", "sizeX": 7, "sizeY": 5, "resources": [{"url": "https://cdnjs.cloudflare.com/ajax/libs/Chart.js/2.3.0/Chart.min.js"}], "templateHtml": "<canvas id=\"pieChart\"></canvas>\n", "templateCss": "", "controllerScript": "self.onInit = function() {\n    var pieData = {\n        labels: [],\n        datasets: []\n    };\n\n    var dataset = {\n        data: [],\n        backgroundColor: [],\n        borderColor: [],\n        borderWidth: [],\n        hoverBackgroundColor: []\n    }\n    \n    var borderColor = self.ctx.settings.borderColor || '#fff';\n    var borderWidth = angular.isDefined(self.ctx.settings.borderWidth) ? self.ctx.settings.borderWidth : 5;\n    \n    pieData.datasets.push(dataset);\n    \n    for (var i=0; i < self.ctx.data.length; i++) {\n        var dataKey = self.ctx.data[i].dataKey;\n        pieData.labels.push(dataKey.label);\n        dataset.data.push(0);\n        var hoverBackgroundColor = tinycolor(dataKey.color).lighten(15);\n        dataset.backgroundColor.push(dataKey.color);\n        dataset.borderColor.push(borderColor);\n        dataset.borderWidth.push(borderWidth);\n        dataset.hoverBackgroundColor.push(hoverBackgroundColor.toRgbString());\n    }\n\n    var options = {\n        responsive: false,\n        maintainAspectRatio: false,\n        legend: {\n            display: true,\n            labels: {\n                fontColor: '#666'\n            }\n        },\n        tooltips: {\n            callbacks: {\n                label: function(tooltipItem, data) {\n                    var label = data.labels[tooltipItem.index];\n                    var value = data.datasets[tooltipItem.datasetIndex].data[tooltipItem.index];\n                    var content = label + ': ' + value;\n                    var units = self.ctx.settings.units ? self.ctx.settings.units : self.ctx.units;\n                    if (units) {\n                        content += ' ' + units;\n                    } \n                    return content;\n                }\n            }\n        }\n    };\n\n    if (self.ctx.settings.legend) {\n        options.legend.display = self.ctx.settings.legend.display !== false;\n        options.legend.labels.fontColor = self.ctx.settings.legend.labelsFontColor || '#666';\n    }\n\n    var ctx = $('#pieChart', self.ctx.$container);\n    self.ctx.chart = new Chart(ctx, {\n        type: 'doughnut',\n        data: pieData,\n        options: options\n    });\n    \n    self.onResize();\n}\n\nself.onDataUpdated = function() {\n    for (var i = 0; i < self.ctx.data.length; i++) {\n        var cellData = self.ctx.data[i];\n        if (cellData.data.length > 0) {\n             var tvPair = cellData.data[cellData.data.length - 1];\n             var value = tvPair[1];\n             self.ctx.chart.data.datasets[0].data[i] = parseFloat(value);\n        }\n    }\n    self.ctx.chart.update();\n}\n\nself.onResize = function() {\n    self.ctx.chart.resize();\n}\n\n", "settingsSchema": "{\n    \"schema\": {\n        \"type\": \"object\",\n        \"title\": \"Settings\",\n        \"properties\": {\n            \"borderWidth\": {\n                \"title\": \"Border width\",\n                \"type\": \"number\",\n                \"default\": 5\n            },\n            \"borderColor\": {\n                \"title\": \"Border color\",\n                \"type\": \"string\",\n                \"default\": \"#fff\"\n            },\n            \"legend\": {\n                \"title\": \"Legend settings\",\n                \"type\": \"object\",\n                \"properties\": {\n                    \"display\": {\n                        \"title\": \"Display legend\",\n                        \"type\": \"boolean\",\n                        \"default\": true\n                    },\n                    \"labelsFontColor\": {\n                        \"title\": \"Labels font color\",\n                        \"type\": \"string\",\n                        \"default\": \"#666\"\n                    }\n                }\n            }\n        },\n        \"required\": []\n    },\n    \"form\": [\n        \"borderWidth\", \n        {\n            \"key\": \"borderColor\",\n            \"type\": \"color\"\n        }, \n        {\n            \"key\": \"legend\",\n            \"items\": [\n                \"legend.display\",\n                {\n                    \"key\": \"legend.labelsFontColor\",\n                    \"type\": \"color\"\n                }\n            ]\n        }\n    ]\n}", "dataKeySettingsSchema": "{}\n", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"First\",\"color\":\"#26a69a\",\"settings\":{},\"_hash\":0.15479322438769105,\"funcBody\":\"var value = (prevValue-50) + Math.random() * 2 - 1;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 100) {\\n\\tvalue = 100;\\n}\\nreturn value+50;\"},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Second\",\"color\":\"#f57c00\",\"settings\":{},\"_hash\":0.545701115289893,\"funcBody\":\"var value = (prevValue-20) + Math.random() * 2 - 1;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 100) {\\n\\tvalue = 100;\\n}\\nreturn value+20;\"},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Third\",\"color\":\"#afb42b\",\"settings\":{},\"_hash\":0.2592906835158064,\"funcBody\":\"var value = (prevValue-40) + Math.random() * 2 - 1;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 100) {\\n\\tvalue = 100;\\n}\\nreturn value+40;\"},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Fourth\",\"color\":\"#673ab7\",\"settings\":{},\"_hash\":0.12880275585455747,\"funcBody\":\"var value = (prevValue-50) + Math.random() * 2 - 1;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 100) {\\n\\tvalue = 100;\\n}\\nreturn value+50;\"}]}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":true,\"backgroundColor\":\"#fff\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"8px\",\"settings\":{\"borderWidth\":5,\"borderColor\":\"#fff\",\"legend\":{\"display\":true,\"labelsFontColor\":\"#666666\"}},\"title\":\"Doughnut - Chart.js\",\"dropShadow\":true,\"enableFullscreen\":true,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400}}"}}, {"alias": "pie", "name": "Pie - Flot", "descriptor": {"type": "latest", "sizeX": 8, "sizeY": 5, "resources": [], "templateHtml": "", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.pie-label {\n    font-size: 12px;\n    font-family: '<PERSON>o';\n    font-weight: bold;\n    text-align: center;\n    padding: 2px;\n    color: white;\n}\n", "controllerScript": "self.onInit = function() {\n    self.ctx.flot = new TbFlot(self.ctx, 'pie');    \n}\n\nself.onDataUpdated = function() {\n    self.ctx.flot.update();\n}\n\nself.onResize = function() {\n    self.ctx.flot.resize();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.flot.checkMouseEvents();\n}\n\nself.onMobileModeChanged = function() {\n    self.ctx.flot.checkMouseEvents();\n}\n\nself.getSettingsSchema = function() {\n    return TbFlot.pieSettingsSchema;\n}\n\nself.getDataKeySettingsSchema = function() {\n    return TbFlot.pieDatakeySettingsSchema;\n}\n\nself.onDestroy = function() {\n    self.ctx.flot.destroy();\n}\n", "settingsSchema": "{}\n", "dataKeySettingsSchema": "{}\n", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"First\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.15479322438769105,\"funcBody\":\"var value = (prevValue-50) + Math.random() * 2 - 1;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 100) {\\n\\tvalue = 100;\\n}\\nreturn value+50;\"},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Second\",\"color\":\"#4caf50\",\"settings\":{},\"_hash\":0.6114638304362894,\"funcBody\":\"var value = (prevValue-20) + Math.random() * 2 - 1;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 100) {\\n\\tvalue = 100;\\n}\\nreturn value+20;\"},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Third\",\"color\":\"#f44336\",\"settings\":{},\"_hash\":0.9955906536344441,\"funcBody\":\"var value = (prevValue-40) + Math.random() * 2 - 1;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 100) {\\n\\tvalue = 100;\\n}\\nreturn value+40;\"},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Fourth\",\"color\":\"#ffc107\",\"settings\":{},\"_hash\":0.9430835931647599,\"funcBody\":\"var value = (prevValue-50) + Math.random() * 2 - 1;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 100) {\\n\\tvalue = 100;\\n}\\nreturn value+50;\"}]}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":true,\"backgroundColor\":\"#fff\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"8px\",\"settings\":{\"radius\":1,\"fontColor\":\"#545454\",\"fontSize\":10,\"decimals\":1,\"legend\":{\"show\":true,\"position\":\"nw\",\"labelBoxBorderColor\":\"#CCCCCC\",\"backgroundColor\":\"#F0F0F0\",\"backgroundOpacity\":0.85},\"innerRadius\":0,\"showLabels\":true,\"stroke\":{\"width\":5},\"tilt\":1,\"animatedPie\":false},\"title\":\"Pie - Flot\",\"dropShadow\":true,\"enableFullscreen\":true,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400}}"}}, {"alias": "pie_chart_js", "name": "Pie - Chart.js", "descriptor": {"type": "latest", "sizeX": 8, "sizeY": 5, "resources": [{"url": "https://cdnjs.cloudflare.com/ajax/libs/Chart.js/2.3.0/Chart.min.js"}], "templateHtml": "<canvas id=\"pieChart\"></canvas>\n", "templateCss": "", "controllerScript": "self.onInit = function() {\n    \n    var pieData = {\n        labels: [],\n        datasets: []\n    };\n\n    var dataset = {\n        data: [],\n        backgroundColor: [],\n        borderColor: [],\n        borderWidth: [],\n        hoverBackgroundColor: []\n    }\n    \n    pieData.datasets.push(dataset);\n    \n    for (var i=0; i < self.ctx.data.length; i++) {\n        var dataKey = self.ctx.data[i].dataKey;\n        pieData.labels.push(dataKey.label);\n        dataset.data.push(0);\n        var hoverBackgroundColor = tinycolor(dataKey.color).lighten(15);\n        var borderColor = tinycolor(dataKey.color).darken();\n        dataset.backgroundColor.push(dataKey.color);\n        dataset.borderColor.push('#fff');\n        dataset.borderWidth.push(5);\n        dataset.hoverBackgroundColor.push(hoverBackgroundColor.toRgbString());\n    }\n\n    var ctx = $('#pieChart', self.ctx.$container);\n    self.ctx.chart = new Chart(ctx, {\n        type: 'pie',\n        data: pieData,\n        options: {\n            responsive: false,\n            maintainAspectRatio: false\n        }\n    });    \n    \n    self.onResize();\n}\n\nself.onDataUpdated = function() {\n    for (var i = 0; i < self.ctx.data.length; i++) {\n        var cellData = self.ctx.data[i];\n        if (cellData.data.length > 0) {\n             var tvPair = cellData.data[cellData.data.length - 1];\n             var value = tvPair[1];\n             self.ctx.chart.data.datasets[0].data[i] = parseFloat(value);\n        }\n    }\n    self.ctx.chart.update();\n}\n\nself.onResize = function() {\n    self.ctx.chart.resize();\n}\n", "settingsSchema": "{}", "dataKeySettingsSchema": "{}\n", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"First\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.15479322438769105,\"funcBody\":\"var value = (prevValue-50) + Math.random() * 2 - 1;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 100) {\\n\\tvalue = 100;\\n}\\nreturn value+50;\"},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Second\",\"color\":\"#4caf50\",\"settings\":{},\"_hash\":0.545701115289893,\"funcBody\":\"var value = (prevValue-20) + Math.random() * 2 - 1;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 100) {\\n\\tvalue = 100;\\n}\\nreturn value+20;\"},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Third\",\"color\":\"#f44336\",\"settings\":{},\"_hash\":0.2592906835158064,\"funcBody\":\"var value = (prevValue-40) + Math.random() * 2 - 1;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 100) {\\n\\tvalue = 100;\\n}\\nreturn value+40;\"},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Fourth\",\"color\":\"#ffc107\",\"settings\":{},\"_hash\":0.12880275585455747,\"funcBody\":\"var value = (prevValue-50) + Math.random() * 2 - 1;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 100) {\\n\\tvalue = 100;\\n}\\nreturn value+50;\"}]}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":true,\"backgroundColor\":\"#fff\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"8px\",\"settings\":{},\"title\":\"Pie - Chart.js\"}"}}, {"alias": "polar_area_chart_js", "name": "Polar Area - Chart.js", "descriptor": {"type": "latest", "sizeX": 7, "sizeY": 5, "resources": [{"url": "https://cdnjs.cloudflare.com/ajax/libs/Chart.js/2.3.0/Chart.min.js"}], "templateHtml": "<canvas id=\"pieChart\"></canvas>\n", "templateCss": "", "controllerScript": "self.onInit = function() {\n    var pieData = {\n        labels: [],\n        datasets: []\n    };\n\n    var dataset = {\n        data: [],\n        backgroundColor: [],\n        borderColor: [],\n        borderWidth: [],\n        hoverBackgroundColor: []\n    }\n    \n    pieData.datasets.push(dataset);\n    \n    for (var i = 0; i < self.ctx.data.length; i++) {\n        var dataKey = self.ctx.data[i].dataKey;\n        pieData.labels.push(dataKey.label);\n        dataset.data.push(0);\n        var hoverBackgroundColor = tinycolor(dataKey.color).lighten(15);\n        var borderColor = tinycolor(dataKey.color).darken();\n        dataset.backgroundColor.push(dataKey.color);\n        dataset.borderColor.push('#fff');\n        dataset.borderWidth.push(5);\n        dataset.hoverBackgroundColor.push(hoverBackgroundColor.toRgbString());\n    }\n\n    var ctx = $('#pieChart', self.ctx.$container);\n    self.ctx.chart = new Chart(ctx, {\n        type: 'polarArea',\n        data: pieData,\n        options: {\n            responsive: false,\n            maintainAspectRatio: false\n        }\n    });\n    \n    self.onResize();\n}\n\nself.onDataUpdated = function() {\n    for (var i = 0; i < self.ctx.data.length; i++) {\n        var cellData = self.ctx.data[i];\n        if (cellData.data.length > 0) {\n             var tvPair = cellData.data[cellData.data.length - 1];\n             var value = tvPair[1];\n             self.ctx.chart.data.datasets[0].data[i] = parseFloat(value);\n        }\n    }\n  \n    self.ctx.chart.update();\n}\n\nself.onResize = function() {\n    if (self.ctx.height >= 70) {\n        try {\n            self.ctx.chart.resize();\n        } catch (e) {}\n    }\n}\n", "settingsSchema": "{}", "dataKeySettingsSchema": "{}\n", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"First\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.15479322438769105,\"funcBody\":\"var value = (prevValue-50) + Math.random() * 2 - 1;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 100) {\\n\\tvalue = 100;\\n}\\nreturn value+50;\"},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Second\",\"color\":\"#4caf50\",\"settings\":{},\"_hash\":0.545701115289893,\"funcBody\":\"var value = (prevValue-20) + Math.random() * 2 - 1;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 100) {\\n\\tvalue = 100;\\n}\\nreturn value+20;\"},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Third\",\"color\":\"#f44336\",\"settings\":{},\"_hash\":0.2592906835158064,\"funcBody\":\"var value = (prevValue-40) + Math.random() * 2 - 1;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 100) {\\n\\tvalue = 100;\\n}\\nreturn value+40;\"},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Fourth\",\"color\":\"#ffc107\",\"settings\":{},\"_hash\":0.12880275585455747,\"funcBody\":\"var value = (prevValue-50) + Math.random() * 2 - 1;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 100) {\\n\\tvalue = 100;\\n}\\nreturn value+50;\"},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Fifth\",\"color\":\"#607d8b\",\"settings\":{},\"_hash\":0.2074391823443591,\"funcBody\":\"var value = (prevValue-50) + Math.random() * 2 - 1;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 100) {\\n\\tvalue = 100;\\n}\\nreturn value+50;\"}]}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":true,\"backgroundColor\":\"#fff\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"8px\",\"settings\":{},\"title\":\"Polar Area - Chart.js\"}"}}, {"alias": "radar_chart_js", "name": "Radar - Chart.js", "descriptor": {"type": "latest", "sizeX": 7, "sizeY": 5, "resources": [{"url": "https://cdnjs.cloudflare.com/ajax/libs/Chart.js/2.3.0/Chart.min.js"}], "templateHtml": "<canvas id=\"radarChart\"></canvas>\n", "templateCss": "", "controllerScript": "self.onInit = function() {\n    var barData = {\n        labels: [],\n        datasets: []\n    };\n\n    var backgroundColor = tinycolor(self.ctx.data[0].dataKey.color);\n    backgroundColor.setAlpha(0.2);\n    var borderColor = tinycolor(self.ctx.data[0].dataKey.color);\n    borderColor.setAlpha(1);\n    var dataset = {\n        label: self.ctx.datasources[0].name,\n        data: [],\n        backgroundColor: backgroundColor.toRgbString(),\n        borderColor: borderColor.toRgbString(),\n        pointBackgroundColor: borderColor.toRgbString(),\n        pointBorderColor: borderColor.darken().toRgbString(),\n        borderWidth: 1\n    }\n    \n    barData.datasets.push(dataset);\n    \n    for (var i = 0; i < self.ctx.data.length; i++) {\n        var dataKey = self.ctx.data[i].dataKey;\n        barData.labels.push(dataKey.label);\n        dataset.data.push(0);\n    }\n\n    var ctx = $('#radarChart', self.ctx.$container);\n    self.ctx.chart = new Chart(ctx, {\n        type: 'radar',\n        data: barData,\n        options: {\n            responsive: false,\n            maintainAspectRatio: false\n        }\n    });\n    \n    self.onResize();\n}\n\nself.onDataUpdated = function() {\n    for (var i = 0; i < self.ctx.data.length; i++) {\n        var cellData = self.ctx.data[i];\n        if (cellData.data.length > 0) {\n             var tvPair = cellData.data[cellData.data.length - 1];\n             var value = tvPair[1];\n             self.ctx.chart.data.datasets[0].data[i] = parseFloat(value);\n        }\n    }    \n    self.ctx.chart.update();\n}\n\nself.onResize = function() {\n    if (self.ctx.height >= 70) {\n        self.ctx.chart.resize();\n    }\n}\n", "settingsSchema": "{}", "dataKeySettingsSchema": "{}\n", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"First\",\"color\":\"#2196f3\",\"settings\":{},\"_hash\":0.15479322438769105,\"funcBody\":\"var value = (prevValue-50) + Math.random() * 2 - 1;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 100) {\\n\\tvalue = 100;\\n}\\nreturn value+50;\"},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Second\",\"color\":\"#4caf50\",\"settings\":{},\"_hash\":0.545701115289893,\"funcBody\":\"var value = (prevValue-20) + Math.random() * 2 - 1;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 100) {\\n\\tvalue = 100;\\n}\\nreturn value+20;\"},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Third\",\"color\":\"#f44336\",\"settings\":{},\"_hash\":0.2592906835158064,\"funcBody\":\"var value = (prevValue-40) + Math.random() * 2 - 1;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 100) {\\n\\tvalue = 100;\\n}\\nreturn value+40;\"},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Fourth\",\"color\":\"#ffc107\",\"settings\":{},\"_hash\":0.12880275585455747,\"funcBody\":\"var value = (prevValue-50) + Math.random() * 2 - 1;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 100) {\\n\\tvalue = 100;\\n}\\nreturn value+50;\"}]}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":true,\"backgroundColor\":\"#fff\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"8px\",\"settings\":{},\"title\":\"Radar - Chart.js\"}"}}, {"alias": "timeseries_bars_flot", "name": "Timeseries Bars - Flot", "descriptor": {"type": "timeseries", "sizeX": 8, "sizeY": 5, "resources": [], "templateHtml": "", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.flot = new TbFlot(self.ctx, 'bar');    \n}\n\nself.onDataUpdated = function() {\n    self.ctx.flot.update();\n}\n\nself.onResize = function() {\n    self.ctx.flot.resize();\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.flot.checkMouseEvents();\n}\n\nself.onMobileModeChanged = function() {\n    self.ctx.flot.checkMouseEvents();\n}\n\nself.getSettingsSchema = function() {\n    return TbFlot.settingsSchema('bar');\n}\n\nself.getDataKeySettingsSchema = function() {\n    return TbFlot.datakeySettingsSchema(false);\n}\n\nself.onDestroy = function() {\n    self.ctx.flot.destroy();\n}\n", "settingsSchema": "{}", "dataKeySettingsSchema": "{}", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"First\",\"color\":\"#2196f3\",\"settings\":{\"showLines\":false,\"fillLines\":false,\"showPoints\":false},\"_hash\":0.8587686344902596,\"funcBody\":\"var value = prevValue + Math.random() * 100 - 50;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 1000) {\\n\\tvalue = 1000;\\n}\\nreturn value;\"},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Second\",\"color\":\"#ffc107\",\"settings\":{\"showLines\":false,\"fillLines\":false,\"showPoints\":false},\"_hash\":0.12775350966079668,\"funcBody\":\"var value = prevValue + Math.random() * 100 - 50;\\nvar multiplier = Math.pow(10, 2 || 0);\\nvar value = Math.round(value * multiplier) / multiplier;\\nif (value < 0) {\\n\\tvalue = 0;\\n} else if (value > 1000) {\\n\\tvalue = 1000;\\n}\\nreturn value;\"}]}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000},\"aggregation\":{\"limit\":200,\"type\":\"AVG\"}},\"showTitle\":true,\"backgroundColor\":\"#fff\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"8px\",\"settings\":{\"shadowSize\":4,\"fontColor\":\"#545454\",\"fontSize\":10,\"xaxis\":{\"showLabels\":true,\"color\":\"#545454\"},\"yaxis\":{\"showLabels\":true,\"color\":\"#545454\"},\"grid\":{\"color\":\"#545454\",\"tickColor\":\"#DDDDDD\",\"verticalLines\":true,\"horizontalLines\":true,\"outlineWidth\":1},\"stack\":true,\"tooltipIndividual\":false,\"defaultBarWidth\":600},\"title\":\"Timeseries Bars - Flot\",\"dropShadow\":true,\"enableFullscreen\":true,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"mobileHeight\":null,\"widgetStyle\":{},\"useDashboardTimewindow\":true,\"showLegend\":true,\"actions\":{}}"}}, {"alias": "state_chart", "name": "State Chart", "descriptor": {"type": "timeseries", "sizeX": 8, "sizeY": 5, "resources": [], "templateHtml": "", "templateCss": ".legend {\n    font-size: 13px;\n    line-height: 10px;\n}\n\n.legend table { \n    border-spacing: 0px;\n    border-collapse: separate;\n}\n\n.mouse-events .flot-overlay {\n    cursor: crosshair; \n}\n\n", "controllerScript": "self.onInit = function() {\n    self.ctx.flot = new TbFlot(self.ctx, 'state');    \n}\n\nself.onDataUpdated = function() {\n    self.ctx.flot.update();\n}\n\nself.onResize = function() {\n    self.ctx.flot.resize();\n}\n\nself.typeParameters = function() {\n    return {\n        stateData: true\n    };\n}\n\nself.onEditModeChanged = function() {\n    self.ctx.flot.checkMouseEvents();\n}\n\nself.onMobileModeChanged = function() {\n    self.ctx.flot.checkMouseEvents();\n}\n\nself.getSettingsSchema = function() {\n    return TbFlot.settingsSchema('graph');\n}\n\nself.getDataKeySettingsSchema = function() {\n    return TbFlot.datakeySettingsSchema(true);\n}\n\nself.onDestroy = function() {\n    self.ctx.flot.destroy();\n}\n", "settingsSchema": "{}", "dataKeySettingsSchema": "{}", "defaultConfig": "{\"datasources\":[{\"type\":\"function\",\"name\":\"function\",\"dataKeys\":[{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Switch 1\",\"color\":\"#2196f3\",\"settings\":{\"showLines\":true,\"fillLines\":true,\"showPoints\":false,\"axisPosition\":\"left\",\"showSeparateAxis\":false},\"_hash\":0.8587686344902596,\"funcBody\":\"return Math.random() > 0.5 ? 1 : 0;\"},{\"name\":\"f(x)\",\"type\":\"function\",\"label\":\"Switch 2\",\"color\":\"#ffc107\",\"settings\":{\"showLines\":true,\"fillLines\":false,\"showPoints\":false,\"axisPosition\":\"left\"},\"_hash\":0.12775350966079668,\"funcBody\":\"return Math.random() <= 0.5 ? 1 : 0;\"}]}],\"timewindow\":{\"realtime\":{\"timewindowMs\":60000}},\"showTitle\":true,\"backgroundColor\":\"#fff\",\"color\":\"rgba(0, 0, 0, 0.87)\",\"padding\":\"8px\",\"settings\":{\"shadowSize\":4,\"fontColor\":\"#545454\",\"fontSize\":10,\"xaxis\":{\"showLabels\":true,\"color\":\"#545454\"},\"yaxis\":{\"showLabels\":true,\"color\":\"#545454\",\"ticksFormatter\":\"if (value > 0 && value <= 1) {\\n    return 'On';\\n} else if (value === 0) {\\n    return 'Off';\\n} else {\\n    return '';\\n}\"},\"grid\":{\"color\":\"#545454\",\"tickColor\":\"#DDDDDD\",\"verticalLines\":true,\"horizontalLines\":true,\"outlineWidth\":1},\"stack\":false,\"tooltipIndividual\":false,\"tooltipValueFormatter\":\"if (value > 0 && value <= 1) {\\n    return 'On';\\n} else if (value === 0) {\\n    return 'Off';\\n} else {\\n    return '';\\n}\",\"smoothLines\":false},\"title\":\"State Chart\",\"dropShadow\":true,\"enableFullscreen\":true,\"titleStyle\":{\"fontSize\":\"16px\",\"fontWeight\":400},\"mobileHeight\":null,\"widgetStyle\":{},\"useDashboardTimewindow\":true,\"showLegend\":true,\"actions\":{},\"legendConfig\":{\"position\":\"bottom\",\"showMin\":false,\"showMax\":false,\"showAvg\":false,\"showTotal\":false}}"}}]}