package org.thingsboard.server.dao.util.imodel.query.smartService.portal;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartService.portal.SsPortalComment;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

@Getter
@Setter
public class SsPortalCommentSaveRequest extends SaveRequest<SsPortalComment> {
    // 姓名
    @NotNullOrEmpty
    private String name;

    // 手机号
    private String phone;

    // 邮箱
    private String email;

    // 内容
    @NotNullOrEmpty
    private String content;

    @Override
    protected SsPortalComment build() {
        SsPortalComment entity = new SsPortalComment();
        entity.setCreateTime(createTime());
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    @Override
    protected SsPortalComment update(String id) {
        SsPortalComment entity = new SsPortalComment();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(SsPortalComment entity) {
        entity.setName(name);
        entity.setPhone(phone);
        entity.setEmail(email);
        entity.setContent(content);
    }

}