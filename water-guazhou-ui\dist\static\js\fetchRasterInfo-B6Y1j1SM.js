import{h as Ie,e as s,y as l,a as B,W as z,a6 as lt,o as Z,m as Re,w as Ee,a0 as ct,aG as Pe,a2 as Mt,s as T,b as dt}from"./Point-WxyopZva.js";import{a5 as S,T as ee,R as g,aO as O}from"./index-r0dFAfgr.js";import{h2 as Ge,l as A,dN as re,cN as E,h3 as Ve,gY as ut,h4 as We,h5 as Je,h6 as Oe,h7 as _e,aq as gt,g as pt,y as It,a as mt,h8 as yt,h9 as wt,ha as ht,hb as At,eV as te,n as ne,hc as Ne,ei as me,ar as ft,w as se,v as Dt,bY as bt,hd as Fe,he as je,cU as Ct,b$ as Tt,dx as vt,d9 as St,bH as Lt}from"./MapView-DaoQedLH.js";import{p as Nt}from"./multidimensionalUtils-BqWBjmd-.js";import{c as x,l as jt,d as xt,g as Bt,m as U}from"./dataUtils-DovfQoP5.js";import{_ as Ke,n as X,a as zt,d as Ut,u as $e}from"./RasterSymbolizer-BF_flzvK.js";import"./generateRendererUtils-Bt0vqUD2.js";import{U as k}from"./pe-B8dP0-Ut.js";var le;const K=new Ie({flow_from:"flow-from",flow_to:"flow-to"});let w=le=class extends Ge(z){constructor(e){super(e),this.density=.8,this.color=new A([255,255,255,1]),this.maxPathLength=200,this.trailWidth=1.5,this.flowSpeed=10,this.trailLength=100,this.smoothing=0,this.flowRepresentation="flow-from",this.type="flow",this.authoringInfo=null,this.legendOptions=null,this.trailCap="butt",this.background="none"}clone(){var C,d;const{density:e,maxPathLength:t,trailWidth:i,flowSpeed:a,trailLength:r,smoothing:o,flowRepresentation:n,trailCap:c,background:m}=this,M=this.color.clone(),y=(this.visualVariables||[]).map(u=>u.clone()),I=(C=this.authoringInfo)==null?void 0:C.clone(),L=(d=this.legendOptions)==null?void 0:d.clone();return new le({density:e,color:M,maxPathLength:t,trailWidth:i,flowSpeed:a,trailLength:r,trailCap:c,background:m,smoothing:o,flowRepresentation:n,visualVariables:y,authoringInfo:I,legendOptions:L})}getSymbol(e,t){}async getSymbolAsync(e,t){}getSymbols(){return[]}};s([l({type:Number,json:{write:!0}})],w.prototype,"density",void 0),s([l({type:A,json:{write:{allowNull:!0}}})],w.prototype,"color",void 0),s([l({type:Number,cast:re,json:{write:!0}})],w.prototype,"maxPathLength",void 0),s([l({type:Number,cast:re,json:{write:!0}})],w.prototype,"trailWidth",void 0),s([l({type:Number,json:{write:!0}})],w.prototype,"flowSpeed",void 0),s([l({type:Number,json:{write:!0}})],w.prototype,"trailLength",void 0),s([l({type:Number,cast:re,json:{write:!1}})],w.prototype,"smoothing",void 0),s([l({type:K.apiValues,json:{type:K.jsonValues,read:{reader:K.read},write:{writer:K.write}}})],w.prototype,"flowRepresentation",void 0),s([E({flowRenderer:"flow"})],w.prototype,"type",void 0),s([l({type:Ve,json:{write:!0}})],w.prototype,"authoringInfo",void 0),s([l({type:ut,json:{write:!0}})],w.prototype,"legendOptions",void 0),s([l({type:String,json:{write:!0}})],w.prototype,"trailCap",void 0),s([l({type:String,json:{write:!0}})],w.prototype,"background",void 0),w=le=s([B("esri.renderers.FlowRenderer")],w);const Qe=w;let R=class extends z{constructor(){super(...arguments),this.value=null,this.label=null,this.color=null}};s([l({type:Number,json:{write:!0}})],R.prototype,"value",void 0),s([l({type:String,json:{write:!0}})],R.prototype,"label",void 0),s([l({type:A,json:{type:[lt],write:!0}})],R.prototype,"color",void 0),R=s([B("esri.renderers.support.ColormapInfo")],R);const Xe=R;var V;let W=V=class extends z{constructor(e){super(e),this.colormapInfos=null,this.type="raster-colormap"}static createFromColormap(e,t){if(!e)return null;const i=e[0].length===5,a=[...e].sort(r=>r[0][0]-r[1][0]).map(r=>Xe.fromJSON({value:r[0],color:i?r.slice(1,5):r.slice(1,4).concat([255]),label:t?t[r[0]]??"":r[0]}));return new V({colormapInfos:a})}static createFromColorramp(e){const t=Ke(e);return V.createFromColormap(t)}clone(){return new V({colormapInfos:this.colormapInfos.map(e=>e.toJSON())})}extractColormap(){return this.colormapInfos.map(({value:e,color:t})=>[e,t.r,t.g,t.b,t.a>1?t.a:255*t.a&255]).sort((e,t)=>e[0]-t[0])}};s([l({type:[Xe],json:{write:!0}})],W.prototype,"colormapInfos",void 0),s([E({rasterColormap:"raster-colormap"})],W.prototype,"type",void 0),W=V=s([B("esri.renderers.RasterColormapRenderer")],W);const ye=W;var ce;let b=ce=class extends z{constructor(e){super(e),this.altitude=45,this.azimuth=315,this.colorRamp=null,this.hillshadeType="traditional",this.pixelSizePower=.664,this.pixelSizeFactor=.024,this.scalingType="none",this.type="raster-shaded-relief",this.zFactor=1}readColorRamp(e){return Je(e)}clone(){return new ce({hillshadeType:this.hillshadeType,altitude:this.altitude,azimuth:this.azimuth,zFactor:this.zFactor,scalingType:this.scalingType,pixelSizeFactor:this.pixelSizeFactor,pixelSizePower:this.pixelSizePower,colorRamp:S(this.colorRamp)})}};s([l({type:Number,json:{write:!0}})],b.prototype,"altitude",void 0),s([l({type:Number,json:{write:!0}})],b.prototype,"azimuth",void 0),s([l({types:We,json:{write:!0}})],b.prototype,"colorRamp",void 0),s([Z("colorRamp")],b.prototype,"readColorRamp",null),s([l({type:["traditional","multi-directional"],json:{write:!0}})],b.prototype,"hillshadeType",void 0),s([l({type:Number,json:{write:!0}})],b.prototype,"pixelSizePower",void 0),s([l({type:Number,json:{write:!0}})],b.prototype,"pixelSizeFactor",void 0),s([l({type:["none","adjusted"],json:{write:!0}})],b.prototype,"scalingType",void 0),s([E({rasterShadedRelief:"raster-shaded-relief"})],b.prototype,"type",void 0),s([l({type:Number,json:{write:!0}})],b.prototype,"zFactor",void 0),b=ce=s([B("esri.renderers.RasterShadedReliefRenderer")],b);const qe=b;var Me;let p=Me=class extends z{constructor(e){super(e),this.colorRamp=null,this.computeGamma=!1,this.dynamicRangeAdjustment=!1,this.gamma=[],this.maxPercent=null,this.minPercent=null,this.numberOfStandardDeviations=null,this.outputMax=null,this.outputMin=null,this.sigmoidStrengthLevel=null,this.statistics=[],this.histograms=null,this.useGamma=!1,this.stretchType="none",this.type="raster-stretch"}readColorRamp(e){if(e)return Je(e)}writeStatistics(e,t,i){e!=null&&e.length&&(Array.isArray(e[0])||(e=e.map(a=>[a.min,a.max,a.avg,a.stddev])),t[i]=e)}readStretchType(e,t){let i=t.stretchType;return typeof i=="number"&&(i=zt[i]),X.read(i)}clone(){return new Me({stretchType:this.stretchType,outputMin:this.outputMin,outputMax:this.outputMax,useGamma:this.useGamma,computeGamma:this.computeGamma,statistics:S(this.statistics),gamma:S(this.gamma),sigmoidStrengthLevel:this.sigmoidStrengthLevel,numberOfStandardDeviations:this.numberOfStandardDeviations,minPercent:this.minPercent,maxPercent:this.maxPercent,colorRamp:S(this.colorRamp),histograms:S(this.histograms),dynamicRangeAdjustment:this.dynamicRangeAdjustment})}};s([l({types:We,json:{write:!0}})],p.prototype,"colorRamp",void 0),s([Z("colorRamp")],p.prototype,"readColorRamp",null),s([l({type:Boolean,json:{write:!0}})],p.prototype,"computeGamma",void 0),s([l({type:Boolean,json:{write:{target:"dra"},read:{source:"dra"}}})],p.prototype,"dynamicRangeAdjustment",void 0),s([l({type:[Number],json:{write:!0}})],p.prototype,"gamma",void 0),s([l({type:Number,json:{write:!0}})],p.prototype,"maxPercent",void 0),s([l({type:Number,json:{write:!0}})],p.prototype,"minPercent",void 0),s([l({type:Number,json:{write:!0}})],p.prototype,"numberOfStandardDeviations",void 0),s([l({type:Number,json:{read:{source:"max"},write:{target:"max"}}})],p.prototype,"outputMax",void 0),s([l({type:Number,json:{read:{source:"min"},write:{target:"min"}}})],p.prototype,"outputMin",void 0),s([l({type:Number,json:{write:!0}})],p.prototype,"sigmoidStrengthLevel",void 0),s([l({json:{type:[[Number]],write:!0}})],p.prototype,"statistics",void 0),s([l()],p.prototype,"histograms",void 0),s([Re("statistics")],p.prototype,"writeStatistics",null),s([l({type:Boolean,json:{write:!0}})],p.prototype,"useGamma",void 0),s([l({type:X.apiValues,json:{type:X.jsonValues,write:X.write}})],p.prototype,"stretchType",void 0),s([Z("stretchType",["stretchType"])],p.prototype,"readStretchType",null),s([E({rasterStretch:"raster-stretch"})],p.prototype,"type",void 0),p=Me=s([B("esri.renderers.RasterStretchRenderer")],p);const we=p;var de;const xe=new Set(["esriMetersPerSecond","esriKilometersPerHour","esriKnots","esriFeetPerSecond","esriMilesPerHour"]),$=new Ie({beaufort_ft:"beaufort-ft",beaufort_km:"beaufort-km",beaufort_kn:"beaufort-kn",beaufort_m:"beaufort-m",beaufort_mi:"beaufort-mi",classified_arrow:"classified-arrow",ocean_current_kn:"ocean-current-kn",ocean_current_m:"ocean-current-m",simple_scalar:"simple-scalar",single_arrow:"single-arrow",wind_speed:"wind-barb"}),Q=new Ie({flow_from:"flow-from",flow_to:"flow-to"});let h=de=class extends Ge(z){constructor(e){super(e),this.attributeField="Magnitude",this.flowRepresentation="flow-from",this.rotationType="arithmetic",this.style="single-arrow",this.symbolTileSize=50,this.type="vector-field"}readInputUnit(e,t){return xe.has(e)?x.fromJSON(e):null}readOutputUnit(e,t){return xe.has(e)?x.fromJSON(e):null}get styleRenderer(){const e=this.style,t=this.attributeField,i=this._createStyleRenderer(e);return i.field=t,i}get sizeVariables(){const e=[];if(this.visualVariables)for(const t of this.visualVariables)t.type==="size"&&e.push(t);if(e.length===0){const t=new Oe({field:"Magnitude",minSize:.2*this.symbolTileSize,maxSize:.8*this.symbolTileSize});this.visualVariables?this.visualVariables.push(t):this._set("visualVariables",[t]),e.push(t)}return e}get rotationVariables(){const e=[];if(this.visualVariables)for(const t of this.visualVariables)t.type==="rotation"&&e.push(t);if(e.length===0){const t=new _e({field:"Direction",rotationType:this.rotationType});this.visualVariables?this.visualVariables.push(t):this._set("visualVariables",[t]),e.push(t)}return e}clone(){return new de({attributeField:this.attributeField,flowRepresentation:this.flowRepresentation,rotationType:this.rotationType,symbolTileSize:this.symbolTileSize,style:this.style,visualVariables:S(this.visualVariables),inputUnit:this.inputUnit,outputUnit:this.outputUnit})}async getGraphicsFromPixelData(e,t=!1,i=[]){var L;const a=new Array,r=jt(this.inputUnit,this.outputUnit),o=((L=this.rotationVariables[0])==null?void 0:L.rotationType)||this.rotationType,n=t?xt(e.pixelBlock,"vector-uv",o,r):Bt(e.pixelBlock,"vector-magdir",r);if(ee(n))return a;const c=e.extent,m=g(n.mask)&&n.mask.length>0;let M=0;const y=(c.xmax-c.xmin)/n.width,I=(c.ymax-c.ymin)/n.height;for(let C=0;C<n.height;C++)for(let d=0;d<n.width;d++,M++){let u=new Ee({x:c.xmin+d*y+y/2,y:c.ymax-C*I-I/2,spatialReference:c.spatialReference});u=(await gt(u))[0];const N=i.some(v=>v.intersects(u));if((!m||n.mask[M])&&!N){const v={Magnitude:n.pixels[0][M],Direction:n.pixels[1][M]},j=new pt({geometry:{type:"point",x:u.x,y:u.y,spatialReference:c.spatialReference},attributes:v});j.symbol=this._getVisualVariablesAppliedSymbol(j),a.push(j)}}return a}getSymbol(e,t){}async getSymbolAsync(e,t){}getSymbols(){return[]}getClassBreakInfos(){var e;return(e=this.styleRenderer)==null?void 0:e.classBreakInfos}getDefaultSymbol(){var e;return(e=this.styleRenderer)==null?void 0:e.defaultSymbol}_getDefaultSymbol(e){return new It({path:"M14,32 14,18 9,23 16,3 22,23 17,18 17,32 z",outline:new mt({width:0}),size:20,color:e||new A([0,92,230])})}_getVisualVariablesAppliedSymbol(e){var r,o;if(!e)return;const t=(o=(r=this.styleRenderer)==null?void 0:r.getSymbol(e))==null?void 0:o.clone(),i=this.sizeVariables,a=this.rotationVariables;if(i&&i.length&&this.sizeVariables.forEach(n=>yt(t,wt([n],e))),a&&a.length){const n=this.flowRepresentation==="flow-to"==(this.style==="ocean-current-kn"||this.style==="ocean-current-m")?0:180;e.attributes.Direction=e.attributes.Direction+n,this.rotationVariables.forEach(c=>ht(t,At(c,e),c.axis))}return t}_createStyleRenderer(e){let t={defaultSymbol:this._getDefaultSymbol(),classBreakInfos:[]};switch(e){case"single-arrow":t=this._createSingleArrowRenderer();break;case"beaufort-kn":t=this._createBeaufortKnotsRenderer();break;case"beaufort-m":t=this._createBeaufortMeterRenderer();break;case"beaufort-ft":t=this._createBeaufortFeetRenderer();break;case"beaufort-mi":t=this._createBeaufortMilesRenderer();break;case"beaufort-km":t=this._createBeaufortKilometersRenderer();break;case"ocean-current-m":t=this._createCurrentMeterRenderer();break;case"ocean-current-kn":t=this._createCurrentKnotsRenderer();break;case"simple-scalar":t=this._createSimpleScalarRenderer();break;case"wind-barb":t=this._createWindBarbsRenderer();break;case"classified-arrow":t=this._createClassifiedArrowRenderer()}return new te(t)}_createSingleArrowRenderer(){return{defaultSymbol:this._getDefaultSymbol()}}_createBeaufortKnotsRenderer(){const e=[0,1,3,6,10,16,21,27,33,40,47,55,63],t=[[40,146,199],[89,162,186],[129,179,171],[160,194,155],[191,212,138],[218,230,119],[250,250,100],[252,213,83],[252,179,102],[250,141,52],[247,110,42],[240,71,29]];return{defaultSymbol:this._getDefaultSymbol(new A([214,47,39])),classBreakInfos:this._getClassBreaks(e,t)}}_createBeaufortMeterRenderer(){const e=[0,.2,1.8,3.3,5.4,8.5,11,14.1,17.2,20.8,24.4,28.6,32.7],t=[[69,117,181],[101,137,184],[132,158,186],[162,180,189],[192,204,190],[222,227,191],[255,255,191],[255,220,161],[250,185,132],[245,152,105],[237,117,81],[232,21,21]];return{defaultSymbol:this._getDefaultSymbol(new A([214,47,39])),classBreakInfos:this._getClassBreaks(e,t)}}_createBeaufortFeetRenderer(){const e=this._getDefaultSymbol(new A([214,47,39]));let t=[0,.2,1.8,3.3,5.4,8.5,11,14.1,17.2,20.8,24.4,28.6,32.7];const i=[[69,117,181],[101,137,184],[132,158,186],[162,180,189],[192,204,190],[222,227,191],[255,255,191],[255,220,161],[250,185,132],[245,152,105],[237,117,81],[232,21,21]],a=3.28084;return t=t.map(r=>r*a),{defaultSymbol:e,classBreakInfos:this._getClassBreaks(t,i)}}_createBeaufortMilesRenderer(){const e=this._getDefaultSymbol(new A([214,47,39]));let t=[0,.2,1.8,3.3,5.4,8.5,11,14.1,17.2,20.8,24.4,28.6,32.7];const i=[[69,117,181],[101,137,184],[132,158,186],[162,180,189],[192,204,190],[222,227,191],[255,255,191],[255,220,161],[250,185,132],[245,152,105],[237,117,81],[232,21,21]],a=2.23694;return t=t.map(r=>r*a),{defaultSymbol:e,classBreakInfos:this._getClassBreaks(t,i)}}_createBeaufortKilometersRenderer(){const e=this._getDefaultSymbol(new A([214,47,39]));let t=[0,.2,1.8,3.3,5.4,8.5,11,14.1,17.2,20.8,24.4,28.6,32.7];const i=[[69,117,181],[101,137,184],[132,158,186],[162,180,189],[192,204,190],[222,227,191],[255,255,191],[255,220,161],[250,185,132],[245,152,105],[237,117,81],[232,21,21]],a=3.6;return t=t.map(r=>r*a),{defaultSymbol:e,classBreakInfos:this._getClassBreaks(t,i)}}_createCurrentMeterRenderer(){const e=[0,.5,1,1.5,2],t=[[78,26,153],[179,27,26],[202,128,26],[177,177,177]];return{defaultSymbol:this._getDefaultSymbol(new A([177,177,177])),classBreakInfos:this._getClassBreaks(e,t)}}_createCurrentKnotsRenderer(){const e=[0,.25,.5,1,1.5,2,2.5,3,3.5,4],t=[[0,0,0],[0,37,100],[78,26,153],[151,0,100],[179,27,26],[177,78,26],[202,128,26],[177,179,52],[177,177,177]];return{defaultSymbol:this._getDefaultSymbol(new A([177,177,177])),classBreakInfos:this._getClassBreaks(e,t)}}_createClassifiedArrowRenderer(){var a;const e=this._getDefaultSymbol(new A([56,168,0]));let t=[0,1e-6,3.5,7,10.5,14];if((a=this.sizeVariables)!=null&&a.length){const r=this.sizeVariables[0].minDataValue,o=this.sizeVariables[0].maxDataValue;if(r&&o){const n=(o-r)/5;t=Array.from(Array(6).keys()).map(c=>r+n*c)}}const i=[[56,168,0],[139,309,0],[255,255,0],[255,128,0],[255,0,0]];return{defaultSymbol:e,classBreakInfos:this._getClassBreaks(t,i)}}_createSimpleScalarRenderer(){return{defaultSymbol:ne.fromJSON({imageData:"iVBORw0KGgoAAAANSUhEUgAAACsAAAArCAQAAABLVLlLAAAABGdBTUEAAYagMeiWXwAAAAJiS0dEAACqjSMyAAAACXBIWXMAAABIAAAASABGyWs+AAAC3ElEQVRIx9XXvW4cVRQH8N982btpsIREJECyiCXsxX4DKh6AliqGKCBBE2SlwlHgAbBD/AKmyEYUeQ1KahPZSZQvBCkQLTHZ7KGY8Xodz4w3a1NwbzVzz/znfJ//zbStVC5q3icKak9GAs2QIdDx3PtW/S011NW3p+M5Eomh11ipTIKe6+4LQzHaQ+G+63pIZNJJQXMpljwTwj1brpgx5w1zZlyx5Z4QnllEIm2xeeSUHBf0hV0bejo1Uh09G3aFvgXk7cCJFBc9EdaRVuHJJaOdKyTV2TVhYLMduNR0Q9gxL5GaaTDw8GzejrDRBpxWoGsySRW0dttKuattwNkIlFw2YXgzOdYq4Ox49PlM+JrKd5OusjTWhBuVxUfMX/KXXZ3WEmkuqa67wspR4BTbwtKr/5u4fFgStse/T7EifFPnnYl9zPq4vmUOPrRndgoHjDti1gOPqlyXoifcRNGQzUd31lDyfHmob1Gp35vSr+P6vilcQ5Egtyd8YF/ySg9NhPM+9M/IOaHwp5+PSZayXTvCogEUwlatC3J8LLwYtcWB8EuDXQVuCkV5/B4eNHb7wGBs87LBDS+xjdVSn09wq1G8dFM+9tSUhIGneLvUdniKxKpTYljCpu3j7rVWlHj/P23v4NPGUEyeCQnexe9lJjzEQqMjJs+EzNAX6B98dBZVRmroJx95x/A/6gln18EyfCUsl+qdXb/tjvfbw+mwforpUOBz4XLVoBwAn3aWnfeH246NyBXhrq7TTN5lNSP9RkU+puUJm3W2Tsdq0nZWM07srk7MwQrZSRysjjGWBLRJNsNbfj2JMR4AbxpU1XLAb9Mxfpsq5EjMuuiR8L0JiHOOBX3hiUvOmavN0nMueSzcceFk0BK4pMqLo7vDD1Z0qrtDx7Itt4Xwm9UqbMmk8S0Dtuzb2pvOU99Z1nLTOfleNmvfZfP2pYZmPfajwosKdDBNpacNpVGGsWX9CyDI8Xq/Sj6QAAAAJXRFWHRkYXRlOmNyZWF0ZQAyMDE0LTExLTEwVDAzOjE3OjU4LTA1OjAwF+tHyQAAACV0RVh0ZGF0ZTptb2RpZnkAMjAxNC0xMS0xMFQwMzoxNzo1OC0wNTowMGa2/3UAAAAASUVORK5CYII=",height:20,width:20,type:"esriPMS",angle:0})}}_createWindBarbsRenderer(){const e=Array.from(Array(31).keys()).map(r=>5*r),t=[{range:"0-5",path:"M20 20 M5 20 A15 15 0 1 0 35 20 A15 15 0 1 0 5 20 M20 20 M10 20 A10 10 0 1 0 30 20 A10 10 0 1 0 10 20",imageData:"PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGhlaWdodD0iMzJweCIgd2lkdGg9IjMycHgiIHZpZXdCb3g9IjkgMCAyNiA0MSI+CiAgPHBhdGggZD0iTTIwIDIwIE01IDIwIEExNSAxNSAwIDEgMCAzNSAyMCBBMTUgMTUgMCAxIDAgNSAyMCBNMjAgMjAgTTEwIDIwIEExMCAxMCAwIDEgMCAzMCAyMCBBMTAgMTAgMCAxIDAgMTAgMjAiIHN0eWxlPSJzdHJva2U6cmdiKDAsMCwwKTtzdHJva2Utd2lkdGg6MS41Ii8+CiA8L3N2Zz4="},{range:"5-10",path:"M25 0 L25 40 M25 35 L17.5 37.5",imageData:"PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjkgMCAyNyA0NiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cGF0aCBkPSJNMjUgMCBMMjUgNDAgTTI1IDM1IEwxNy41IDM3LjUiIHN0eWxlPSJzdHJva2U6cmdiKDAsMCwwKTtzdHJva2Utd2lkdGg6MS41Ii8+CiA8L3N2Zz4="},{range:"10-15",path:"M25 0 L25 40 L10 45 L25 40",imageData:"PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjkgMCAyNyA0NiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cGF0aCBkPSJNMjUgMCBMMjUgNDAgTDEwIDQ1IEwyNSA0MCIgc3R5bGU9InN0cm9rZTpyZ2IoMCwwLDApO3N0cm9rZS13aWR0aDoxLjUiLz4KIDwvc3ZnPg=="},{range:"15-20",path:"M25 0 L25 40 L10 45 L25 40 M25 35 L17.5 37.5",imageData:"PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjEyIDAgMTUgNDUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CiAgPHBhdGggZD0iTTI1IDAgTDI1IDQwIEwxMCA0NSBMMjUgNDAgTTI1IDM1IEwxNy41IDM3LjUiIHN0eWxlPSJzdHJva2U6cmdiKDAsMCwwKTtzdHJva2Utd2lkdGg6MS41Ii8+CiA8L3N2Zz4="},{range:"20-25",path:"M25 0 L25 40 L10 45 L25 40 M25 35 L10 40",imageData:"PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjkgMCAyNiA0NiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cGF0aCBkPSJNMjUgMCBMMjUgNDAgTDEwIDQ1IEwyNSA0MCBNMjUgMzUgTDEwIDQwIiBzdHlsZT0ic3Ryb2tlOnJnYigwLDAsMCk7c3Ryb2tlLXdpZHRoOjEuNSIvPgogPC9zdmc+"},{range:"25-30",path:"M25 0 L25 40 L10 45 L25 40 M25 35 L10 40 L25 35 M25 30 L17.5 32.5",imageData:"PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjkgMCAyNiA0NiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cGF0aCBkPSJNMjUgMCBMMjUgNDAgTDEwIDQ1IEwyNSA0MCBNMjUgMzUgTDEwIDQwIEwyNSAzNSBNMjUgMzAgTDE3LjUgMzIuNSIgc3R5bGU9InN0cm9rZTpyZ2IoMCwwLDApO3N0cm9rZS13aWR0aDoxLjUiLz4KIDwvc3ZnPg=="},{range:"30-35",path:"M25 0 L25 40 L10 45 L25 40 M25 35 L10 40 L25 35 M25 30 L10 35",imageData:"PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMHB4IiBoZWlnaHQ9IjIwcHgiIHZpZXdCb3g9IjkgMCAyNiA0NiI+CiAgPHBhdGggZD0iTTI1IDAgTDI1IDQwIEwxMCA0NSBMMjUgNDAgTTI1IDM1IEwxMCA0MCBMMjUgMzUgTTI1IDMwIEwxMCAzNSIgc3R5bGU9InN0cm9rZTpyZ2IoMCwwLDApO3N0cm9rZS13aWR0aDoxLjUiLz4KIDwvc3ZnPg=="},{range:"35-40",path:"M25 0 L25 40 L10 45 L25 40 M25 35 L10 40 L25 35 M25 30 L10 35 L25 30 M25 25 L17.5 27.5",imageData:"PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMHB4IiBoZWlnaHQ9IjIwcHgiIHZpZXdCb3g9IjkgMCAyNiA0NiI+CiAgPHBhdGggZD0iTTI1IDAgTDI1IDQwIEwxMCA0NSBMMjUgNDAgTTI1IDM1IEwxMCA0MCBMMjUgMzUgTTI1IDMwIEwxMCAzNSBMMjUgMzAgTTI1IDI1IEwxNy41IDI3LjUiIHN0eWxlPSJzdHJva2U6cmdiKDAsMCwwKTtzdHJva2Utd2lkdGg6MS41Ii8+CiA8L3N2Zz4="},{range:"40-45",path:"M25 0 L25 40 L10 45 L25 40 M25 35 L10 40 L25 35 M25 30 L10 35 L25 30 M25 25 L10 30",imageData:"PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMHB4IiBoZWlnaHQ9IjIwcHgiIHZpZXdCb3g9IjkgMCAyNiA0NiI+CiAgPHBhdGggZD0iTTI1IDAgTDI1IDQwIEwxMCA0NSBMMjUgNDAgTTI1IDM1IEwxMCA0MCBMMjUgMzUgTTI1IDMwIEwxMCAzNSBMMjUgMzAgTTI1IDI1IEwxMCAzMCIgc3R5bGU9InN0cm9rZTpyZ2IoMCwwLDApO3N0cm9rZS13aWR0aDoxLjUiLz4KIDwvc3ZnPg=="},{range:"45-50",path:"M25 0 L25 40 L10 45 L25 40 M25 35 L10 40 L25 35 M25 30 L10 35 L25 30 M25 25 L10 30 L25 25 M25 20 L17.5 22.5",imageData:"PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMHB4IiBoZWlnaHQ9IjIwcHgiIHZpZXdCb3g9IjkgMCAyNiA0NiI+CiAgPHBhdGggZD0iTTI1IDAgTDI1IDQwIEwxMCA0NSBMMjUgNDAgTTI1IDM1IEwxMCA0MCBMMjUgMzUgTTI1IDMwIEwxMCAzNSBMMjUgMzAgTTI1IDI1IEwxMCAzMCBMMjUgMjUgTTI1IDIwIEwxNy41IDIyLjUiIHN0eWxlPSJzdHJva2U6cmdiKDAsMCwwKTtzdHJva2Utd2lkdGg6MS41Ii8+CiA8L3N2Zz4="},{range:"50-55",path:"M25 0 L25 40 L10 40 L25 35",imageData:"PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMHB4IiBoZWlnaHQ9IjIwcHgiIHZpZXdCb3g9IjkgMCAyNiA0MSI+CiAgPHBhdGggZD0iTTI1IDAgTDI1IDQwIEwxMCA0MCBMMjUgMzUiIHN0eWxlPSJzdHJva2U6cmdiKDAsMCwwKTtzdHJva2Utd2lkdGg6MS41Ii8+CiA8L3N2Zz4="},{range:"55-60",path:"M25 0 L25 40 L10 40 L25 35 M25 30 L17.5 32.5",imageData:"PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMHB4IiBoZWlnaHQ9IjIwcHgiIHZpZXdCb3g9IjkgMCAyNiA0MSI+CiAgPHBhdGggZD0iTTI1IDAgTDI1IDQwIEwxMCA0MCBMMjUgMzUgTTI1IDMwIEwxNy41IDMyLjUiIHN0eWxlPSJzdHJva2U6cmdiKDAsMCwwKTtzdHJva2Utd2lkdGg6MS41Ii8+CiA8L3N2Zz4="},{range:"60-65",path:"M25 0 L25 40 L10 40 L25 35 M25 30 L10 35",imageData:"PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMnB4IiBoZWlnaHQ9IjMycHgiIHZpZXdCb3g9IjkgMCAyNiA0MSI+CiAgPHBhdGggZD0iTTI1IDAgTDI1IDQwIEwxMCA0MCBMMjUgMzUgTTI1IDMwIEwxMCAzNSIgc3R5bGU9InN0cm9rZTpyZ2IoMCwwLDApO3N0cm9rZS13aWR0aDoxLjUiLz4KIDwvc3ZnPg=="},{range:"65-70",path:"M25 0 L25 40 L10 40 L25 35 M25 30 L10 35 L25 30 M25 25 L17.5 27.5",imageData:"PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMnB4IiBoZWlnaHQ9IjMycHgiIHZpZXdCb3g9IjkgMCAyNiA0MSI+CiAgPHBhdGggZD0iTTI1IDAgTDI1IDQwIEwxMCA0MCBMMjUgMzUgTTI1IDMwIEwxMCAzNSBMMjUgMzAgTTI1IDI1IEwxNy41IDI3LjUiIHN0eWxlPSJzdHJva2U6cmdiKDAsMCwwKTtzdHJva2Utd2lkdGg6MS41Ii8+CiA8L3N2Zz4="},{range:"70-75",path:"M25 0 L25 40 L10 40 L25 35 M25 30 L10 35 L25 30 M25 25 L10 30",imageData:"PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMnB4IiBoZWlnaHQ9IjMycHgiIHZpZXdCb3g9IjkgMCAyNiA0MSI+CiAgPHBhdGggZD0iTTI1IDAgTDI1IDQwIEwxMCA0MCBMMjUgMzUgTTI1IDMwIEwxMCAzNSBMMjUgMzAgTTI1IDI1IEwxMCAzMCIgc3R5bGU9InN0cm9rZTpyZ2IoMCwwLDApO3N0cm9rZS13aWR0aDoxLjUiLz4KIDwvc3ZnPg=="},{range:"75-80",path:"M25 0 L25 40 L10 40 L25 35 M25 30 L10 35 L25 30 M25 25 L10 30 L25 25 M25 20 L17.5 22.5",imageData:"PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMnB4IiBoZWlnaHQ9IjMycHgiIHZpZXdCb3g9IjkgMCAyNiA0MSI+CiAgPHBhdGggZD0iTTI1IDAgTDI1IDQwIEwxMCA0MCBMMjUgMzUgTTI1IDMwIEwxMCAzNSBMMjUgMzAgTTI1IDI1IEwxMCAzMCBMMjUgMjUgTTI1IDIwIEwxNy41IDIyLjUiIHN0eWxlPSJzdHJva2U6cmdiKDAsMCwwKTtzdHJva2Utd2lkdGg6MS41Ii8+CiA8L3N2Zz4="},{range:"80-85",path:"M25 0 L25 40 L10 40 L25 35 M25 30 L10 35 L25 30 M25 25 L10 30 L25 25 M25 20 L10 25",imageData:"PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMnB4IiBoZWlnaHQ9IjMycHgiIHZpZXdCb3g9IjkgMCAyNiA0MSI+CiAgPHBhdGggZD0iTTI1IDAgTDI1IDQwIEwxMCA0MCBMMjUgMzUgTTI1IDMwIEwxMCAzNSBMMjUgMzAgTTI1IDI1IEwxMCAzMCBMMjUgMjUgTTI1IDIwIEwxMCAyNSIgc3R5bGU9InN0cm9rZTpyZ2IoMCwwLDApO3N0cm9rZS13aWR0aDoxLjUiLz4KIDwvc3ZnPg=="},{range:"85-90",path:"M25 0 L25 40 L10 40 L25 35 M25 30 L10 35 L25 30 M25 25 L10 30 L25 25 M25 20 L10 25 L25 20 M25 15 L17.5 17.5",imageData:"PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMnB4IiBoZWlnaHQ9IjMycHgiIHZpZXdCb3g9IjkgMCAyNiA0MSI+CiAgPHBhdGggZD0iTTI1IDAgTDI1IDQwIEwxMCA0MCBMMjUgMzUgTTI1IDMwIEwxMCAzNSBMMjUgMzAgTTI1IDI1IEwxMCAzMCBMMjUgMjUgTTI1IDIwIEwxMCAyNSBMMjUgMjAgTTI1IDE1IEwxNy41IDE3LjUiIHN0eWxlPSJzdHJva2U6cmdiKDAsMCwwKTtzdHJva2Utd2lkdGg6MS41Ii8+CiA8L3N2Zz4="},{range:"90-95",path:"M25 0 L25 40 L10 40 L25 35 M25 30 L10 35 L25 30 M25 25 L10 30 L25 25 M25 20 L10 25 L25 20 M25 15 L10 20",imageData:"PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMnB4IiBoZWlnaHQ9IjMycHgiIHZpZXdCb3g9IjkgMCAyNiA0MSI+CiAgPHBhdGggZD0iTTI1IDAgTDI1IDQwIEwxMCA0MCBMMjUgMzUgTTI1IDMwIEwxMCAzNSBMMjUgMzAgTTI1IDI1IEwxMCAzMCBMMjUgMjUgTTI1IDIwIEwxMCAyNSBMMjUgMjAgTTI1IDE1IEwxMCAyMCIgc3R5bGU9InN0cm9rZTpyZ2IoMCwwLDApO3N0cm9rZS13aWR0aDoxLjUiLz4KIDwvc3ZnPg=="},{range:"95-100",path:"M25 0 L25 40 L10 40 L25 35 M25 30 L10 35 L25 30 M25 25 L10 30 L25 25 M25 20 L10 25 L25 20 M25 15 L10 20 L25 15 M25 10 L17.5 12.5",imageData:"PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMnB4IiBoZWlnaHQ9IjMycHgiIHZpZXdCb3g9IjkgMCAyNiA0MSI+CiAgPHBhdGggZD0iTTI1IDAgTDI1IDQwIEwxMCA0MCBMMjUgMzUgTTI1IDMwIEwxMCAzNSBMMjUgMzAgTTI1IDI1IEwxMCAzMCBMMjUgMjUgTTI1IDIwIEwxMCAyNSBMMjUgMjAgTTI1IDE1IEwxMCAyMCBMMjUgMTUgTTI1IDEwIEwxNy41IDEyLjUiIHN0eWxlPSJzdHJva2U6cmdiKDAsMCwwKTtzdHJva2Utd2lkdGg6MS41Ii8+CiA8L3N2Zz4="},{range:"100-105",path:"M25 0 L25 40 L10 40 L25 35 L10 35 L25 30",imageData:"PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMnB4IiBoZWlnaHQ9IjMycHgiIHZpZXdCb3g9IjkgMCAyNiA0MSI+CiAgPHBhdGggZD0iTTI1IDAgTDI1IDQwIEwxMCA0MCBMMjUgMzUgTDEwIDM1IEwyNSAzMCIgc3R5bGU9InN0cm9rZTpyZ2IoMCwwLDApO3N0cm9rZS13aWR0aDoxLjUiLz4KIDwvc3ZnPg=="},{range:"105-110",path:"M25 0 L25 40 L10 40 L25 35 L10 35 L25 30 M25 25 L17.5 27.5",imageData:"PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGhlaWdodD0iMzJweCIgd2lkdGg9IjMycHgiIHZpZXdCb3g9IjkgMCAyNiA0MSI+CiAgPHBhdGggZD0iTTI1IDAgTDI1IDQwIEwxMCA0MCBMMjUgMzUgTDEwIDM1IEwyNSAzMCBNMjUgMjUgTDE3LjUgMjcuNSIgc3R5bGU9InN0cm9rZTpyZ2IoMCwwLDApO3N0cm9rZS13aWR0aDoxLjUiLz4KIDwvc3ZnPg=="},{range:"110-115",path:"M25 0 L25 40 L10 40 L25 35 L10 35 L25 30 M25 25 L10 30",imageData:"PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGhlaWdodD0iMzJweCIgd2lkdGg9IjMycHgiIHZpZXdCb3g9IjkgMCAyNiA0MSI+CiAgPHBhdGggZD0iTTI1IDAgTDI1IDQwIEwxMCA0MCBMMjUgMzUgTDEwIDM1IEwyNSAzMCBNMjUgMjUgTDEwIDMwIiBzdHlsZT0ic3Ryb2tlOnJnYigwLDAsMCk7c3Ryb2tlLXdpZHRoOjEuNSIvPgogPC9zdmc+"},{range:"115-120",path:"M25 0 L25 40 L10 40 L25 35 L10 35 L25 30 M25 25 L10 30 M25 25 M25 20 L17.5 22.5",imageData:"PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGhlaWdodD0iMzJweCIgd2lkdGg9IjMycHgiIHZpZXdCb3g9IjkgMCAyNiA0MSI+CiAgPHBhdGggZD0iTTI1IDAgTDI1IDQwIEwxMCA0MCBMMjUgMzUgTDEwIDM1IEwyNSAzMCBNMjUgMjUgTDEwIDMwIE0yNSAyNSBNMjUgMjAgTDE3LjUgMjIuNSIgc3R5bGU9InN0cm9rZTpyZ2IoMCwwLDApO3N0cm9rZS13aWR0aDoxLjUiLz4KIDwvc3ZnPg=="},{range:"120-125",path:"M25 0 L25 40 L10 40 L25 35 L10 35 L25 30 M25 25 L10 30 M25 25 M25 20 L10 25",imageData:"PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGhlaWdodD0iMzJweCIgd2lkdGg9IjMycHgiIHZpZXdCb3g9IjkgMCAyNiA0MSI+CiAgPHBhdGggZD0iTTI1IDAgTDI1IDQwIEwxMCA0MCBMMjUgMzUgTDEwIDM1IEwyNSAzMCBNMjUgMjUgTDEwIDMwIE0yNSAyNSBNMjUgMjAgTDEwIDI1IiBzdHlsZT0ic3Ryb2tlOnJnYigwLDAsMCk7c3Ryb2tlLXdpZHRoOjEuNSIvPgogPC9zdmc+"},{range:"125-130",path:"M25 0 L25 40 L10 40 L25 35 L10 35 L25 30 M25 25 L10 30 M25 25 M25 20 L10 25 M25 20 M25 15 L17.5 17.5",imageData:"PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGhlaWdodD0iMzJweCIgd2lkdGg9IjMycHgiIHZpZXdCb3g9IjkgMCAyNiA0MSI+CiAgPHBhdGggZD0iTTI1IDAgTDI1IDQwIEwxMCA0MCBMMjUgMzUgTDEwIDM1IEwyNSAzMCBNMjUgMjUgTDEwIDMwIE0yNSAyNSBNMjUgMjAgTDEwIDI1IE0yNSAyMCBNMjUgMTUgTDE3LjUgMTcuNSIgc3R5bGU9InN0cm9rZTpyZ2IoMCwwLDApO3N0cm9rZS13aWR0aDoxLjUiLz4KIDwvc3ZnPg=="},{range:"130-135",path:"M25 0 L25 40 L10 40 L25 35 L10 35 L25 30 M25 25 L10 30 M25 25 M25 20 L10 25 M25 20 M25 15 L10 20",imageData:"PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGhlaWdodD0iMzJweCIgd2lkdGg9IjMycHgiIHZpZXdCb3g9IjkgMCAyNiA0MSI+CiAgPHBhdGggZD0iTTI1IDAgTDI1IDQwIEwxMCA0MCBMMjUgMzUgTDEwIDM1IEwyNSAzMCBNMjUgMjUgTDEwIDMwIE0yNSAyNSBNMjUgMjAgTDEwIDI1IE0yNSAyMCBNMjUgMTUgTDEwIDIwIiBzdHlsZT0ic3Ryb2tlOnJnYigwLDAsMCk7c3Ryb2tlLXdpZHRoOjEuNSIvPgogPC9zdmc+"},{range:"135-140",path:"M25 0 L25 40 L10 40 L25 35 L10 35 L25 30 M25 25 L10 30 M25 25 M25 20 L10 25 M25 20 M25 15 L10 20 M25 15 M25 10 L17.5 12.5",imageData:"PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGhlaWdodD0iMzJweCIgd2lkdGg9IjMycHgiIHZpZXdCb3g9IjkgMCAyNiA0MSI+CiAgPHBhdGggZD0iTTI1IDAgTDI1IDQwIEwxMCA0MCBMMjUgMzUgTDEwIDM1IEwyNSAzMCBNMjUgMjUgTDEwIDMwIE0yNSAyNSBNMjUgMjAgTDEwIDI1IE0yNSAyMCBNMjUgMTUgTDEwIDIwIE0yNSAxNSBNMjUgMTAgTDE3LjUgMTIuNSIgc3R5bGU9InN0cm9rZTpyZ2IoMCwwLDApO3N0cm9rZS13aWR0aDoxLjUiLz4KIDwvc3ZnPg=="},{range:"140-145",path:"M25 0 L25 40 L10 40 L25 35 L10 35 L25 30 M25 25 L10 30 M25 25 M25 20 L10 25 M25 20 M25 15 L10 20 M25 15 M25 10 L17.5 12.5",imageData:"PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGhlaWdodD0iMzJweCIgd2lkdGg9IjMycHgiIHZpZXdCb3g9IjkgMCAyNiA0MSI+CiAgPHBhdGggZD0iTTI1IDAgTDI1IDQwIEwxMCA0MCBMMjUgMzUgTDEwIDM1IEwyNSAzMCBNMjUgMjUgTDEwIDMwIE0yNSAyNSBNMjUgMjAgTDEwIDI1IE0yNSAyMCBNMjUgMTUgTDEwIDIwIE0yNSAxNSBNMjUgMTAgTDEwIDE1IiBzdHlsZT0ic3Ryb2tlOnJnYigwLDAsMCk7c3Ryb2tlLXdpZHRoOjEuNSIvPgogPC9zdmc+"},{range:"145-150",path:"M25 0 L25 40 L10 40 L25 35 L10 35 L25 30 M25 25 L10 30 M25 25 M25 20 L10 25 M25 20 M25 15 L10 20 M25 15 M25 10 L17.5 12.5",imageData:"PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGhlaWdodD0iMzJweCIgd2lkdGg9IjMycHgiIHZpZXdCb3g9IjkgMCAyNiA0MSI+CiAgPHBhdGggZD0iTTI1IDAgTDI1IDQwIEwxMCA0MCBMMjUgMzUgTDEwIDM1IEwyNSAzMCBNMjUgMjUgTDEwIDMwIE0yNSAyNSBNMjUgMjAgTDEwIDI1IE0yNSAyMCBNMjUgMTUgTDEwIDIwIE0yNSAxNSBNMjUgMTAgTDEwIDE1IE0yNSAxMCBNMjUgNSBMMTcuNSA3LjUiIHN0eWxlPSJzdHJva2U6cmdiKDAsMCwwKTtzdHJva2Utd2lkdGg6MS41Ii8+CiA8L3N2Zz4="}],i=ne.fromJSON({imageData:"iVBORw0KGgoAAAANSUhEUgAAACgAAAApCAQAAADtq6NDAAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAAgY0hSTQAAeiYAAICEAAD6AAAAgOgAAHUwAADqYAAAOpgAABdwnLpRPAAAAAJiS0dEAP+Hj8y/AAAACXBIWXMAAA7DAAAOwwHHb6hkAAAEY0lEQVRIx5XXWWxWRRQH8N+d+31tUdGAVjGglYJABFEBY91jfDAg7piYaFTccA++uMQEFRcSXlATtxiXqMQt4G4iisYl0ai4sIQYtVFZ1KIFKdTS0l4f7vRCS5fPebozc+bM/2z/Mzcx0AgSiUxXnKfIdMn875FIhX53U2n/B/s+kKM4UINTjTBZImixxnrv+9a2iL6zEoUBXcoudrWj/OtHm3wt02lfU9Qao9OnHvIhgmww84MEl1qnxfNmGrqHxAizLdPpC6chGcAxKGGcL+30gOERf1BSpUqVslQSV8d5ReZFe8VQ9avufJn31cWwlJV7iafKStGOE/1qvfH9qUxxu8ydUdmuSKbGO8YUdT2inKLG69pM70tliktl5qIkCAJGmusDG7Vqsc0WjZa4UBlBiA5YZIcjYzB7qDtH5kaUJFLs7RGZTZ42W4PRRmtwvbdt1+wGiaS4drEtDttdZYIDNVuAclR3vA3+dI3qHqmVSy7U6Tv1MScCPvPR7nIpFlsdCy3FdTLPGhK92e2CUITjMJ9ocwKxnsZqc3O3JwMma3d6UVLnyVxB4aXemZqvPqLdpJhW3KVVbY4yYImPo6M5Urv50fj+0z/FG9YaEiENs8UtMfXUaTeTePNHlhXfA1UU+2lyD1Il3Gtt9+adfpNG7dNlpg2U/T3KYLZ2dUWFdTgp3/rQ4sK973qnInV5TIf40x3dhvrJPBiqyWUo4wAtLqhQYS71qK+QKOFRywmGK/kpikzV6WMKhh58vGWs4TIJNjiEYLIuP8Tt4/zmLyqk+AyrJSbF+Qq1DgqRUPMxyl+9q3IQhX/rMCJ6tEunriDs1oSyQZKlr9AkhT2ZIARbJfaJS1vtVbHB+Rgi0RK/y1q1BWsEEyLoz40xtGKcARPVWB1BTPO7f4LNtpkUl1aoMbViLyZo0GRjPD3BxnxjqXeLYlvhqYrzMMG3HoyJXa3JjfnGlbYYFlP7Jh3qKsKY4hQ7TY0nG+xwRL61n63mxHtqNHosigyMLmClNwvuecFnOZB88nNBDzNkzhxEZaKMBVoKapggMzvHHXBEpNSSFAvtcFRsVn0bW8LlMmcXs+c0Kne3gRR32+zg4uXwjC6zit6Wt4a8LXVfcp/MtQXHn2ynGbuCmb8GvvFeJLEE82ReU9/n6+dkq2x3buG9Wn94smcgAw631RPR7BTH+kbmHReZoEpOdEe7zWqZl40s0JWs9Hmv7hjBHqPDwsjGKVJnWWqjbdZp1KhJi0aPmxYZsIRhlttgeF+Jlke41QcOQKoqilSb6HJzSvNG3G/UoWnxwsmt+sVaYwd63dRbqdnMyCPVeyRPvpYgdavM22oGKoMUVRbJfOWMwidJ8Zzb1UvmWK/VVUXzHaTjjrVYh1897HT7xxYEVUaa5SWb/WO+YUWa9SrwvigzM8YlzlYv2GSdVCYxxlBtVnnFq5olwp5/BEk/OLsf5LUmG2+inRJdVvjZ97ZH9/zP34ug1O91pf4p+D+JYBpvrKxfbwAAACV0RVh0ZGF0ZTpjcmVhdGUAMjAxNC0xMS0xMFQwMzoxMjowOS0wNTowMB9ViV0AAAAldEVYdGRhdGU6bW9kaWZ5ADIwMTQtMTEtMTBUMDM6MTI6MDktMDU6MDBuCDHhAAAAAElFTkSuQmCC",height:20,width:20,type:"esriPMS",angle:0}),a=e.map((r,o)=>{let n;if(o!==e.length-1)if(o===0)n={minValue:r,maxValue:e[o+1],symbol:i};else{const c=ne.fromJSON({type:"esriPMS",imageData:t[o].imageData,contentType:"image/svg+xml",height:32,width:32,angle:0});n={minValue:r,maxValue:e[o+1],symbol:c}}return new Ne(n)});return{defaultSymbol:i,classBreakInfos:a}}_getClassBreaks(e,t){return t.map((i,a)=>new Ne({minValue:e[a],maxValue:e[a+1],symbol:this._getDefaultSymbol(new A(i))}))}};s([l({type:String,json:{write:!0}})],h.prototype,"attributeField",void 0),s([l({type:Q.apiValues,json:{type:Q.jsonValues,read:{reader:Q.read},write:{writer:Q.write}}})],h.prototype,"flowRepresentation",void 0),s([l({type:["geographic","arithmetic"],json:{write:!0}})],h.prototype,"rotationType",void 0),s([l({type:$.apiValues,json:{type:$.jsonValues,read:{reader:$.read},write:{writer:$.write}}})],h.prototype,"style",void 0),s([l({json:{write:!0}})],h.prototype,"symbolTileSize",void 0),s([l({type:x.apiValues,json:{type:x.jsonValues,write:{writer:x.write}}})],h.prototype,"inputUnit",void 0),s([Z("inputUnit")],h.prototype,"readInputUnit",null),s([l({type:x.apiValues,json:{type:x.jsonValues,read:{reader:x.read},write:{writer:x.write}}})],h.prototype,"outputUnit",void 0),s([Z("outputUnit")],h.prototype,"readOutputUnit",null),s([E({vectorField:"vector-field"})],h.prototype,"type",void 0),s([l({type:te})],h.prototype,"styleRenderer",null),s([l({type:Oe})],h.prototype,"sizeVariables",null),s([l({type:_e})],h.prototype,"rotationVariables",null),h=de=s([B("esri.renderers.VectorFieldRenderer")],h);const he=h,Be={key:"type",base:null,typeMap:{"unique-value":me,"class-breaks":te,"raster-colormap":ye,"raster-stretch":we,"vector-field":he,"raster-shaded-relief":qe,flow:Qe}},ze={...Be,typeMap:{...Be.typeMap}};delete ze.typeMap["vector-field"],delete ze.typeMap.flow;const Ht={uniqueValue:me,classBreaks:te,rasterStretch:we,rasterColormap:ye,vectorField:he,rasterShadedRelief:qe,flowRenderer:Qe};function Zt(e){return e&&Ht[e.type]||null}function da(e,t){if(!e)return null;if(e.type==="classBreaks"&&e.classificationMethod){const a=e.authoringInfo||{classificationMethod:""};a.classificationMethod=e.classificationMethod,e.authoringInfo=a}e.type==="vectorField"&&e.visualVariables&&!Array.isArray(e.visualVariables)&&(e.visualVariables=[e.visualVariables]);const i=Zt(e);if(i){const a=new i;return a.read(e,t),a}return t&&t.messages&&e&&t.messages.push(new ct("renderer:unsupported","Renderers of type '"+(e.type||"unknown")+"' are not supported",{definition:e,context:t})),null}var ue;const q=new Set(["raster","raster2","dem","fillraster"]),Y=new Set(["rasters"]),Ue=e=>e&&e.rasterFunction?f.fromJSON(e):e,oe=e=>e&&e instanceof f?e.toJSON():e,ge=e=>(e==null?void 0:e.functionName)&&!e.declaredClass,He=e=>ge(e)?new f(e):e,kt=e=>{if(e==null)return null;e=S(e);const t={};for(const i of Object.keys(e))q.has(i.toLowerCase())?t[i]=Ue(e[i]):Y.has(i.toLowerCase())&&Array.isArray(e[i])?t[i]=e[i].map(Ue):t[i]=e[i];return t};let f=ue=class extends z{constructor(e){super(e),this.functionName=null,this.outputPixelType="unknown",this.variableName=null,this.rasterFunctionDefinition=null}set functionArguments(e){if(e){const t=Object.keys(e);if(t.some(i=>q.has(i.toLowerCase())&&ge(e[i]))||t.some(i=>Y.has(i.toLowerCase())&&Array.isArray(e[i])&&e[i].some(a=>ge(a)))){e=S(e);for(const i of t)q.has(i.toLowerCase())?e[i]=He(e[i]):Y.has(i.toLowerCase())&&Array.isArray(e[i])&&(e[i]=e[i].map(a=>He(a)))}}this._set("functionArguments",e)}readFunctionArguments(e){return kt(e)}writeFunctionArguments(e,t,i){const a={};for(const r of Object.keys(e))q.has(r.toLowerCase())?a[r]=oe(e[r]):Y.has(r.toLowerCase())&&Array.isArray(e[r])?a[r]=e[r].map(oe):a[r]=oe(e[r]);t[i]=a}readFunctionName(e,t){const i=t.rasterFunctionInfos;return t.name||(i&&i.length&&i[0].name!=="None"?i[0].name:t.rasterFunctionDefinition?t.rasterFunctionDefinition.name:t.rasterFunction)}clone(){return new ue({functionName:this.functionName,functionArguments:S(this.functionArguments),outputPixelType:this.outputPixelType,variableName:this.variableName,rasterFunctionDefinition:S(this.rasterFunctionDefinition)})}};s([l({json:{type:Object,name:"rasterFunctionArguments"}})],f.prototype,"functionArguments",null),s([Z("functionArguments")],f.prototype,"readFunctionArguments",null),s([Re("functionArguments")],f.prototype,"writeFunctionArguments",null),s([l({json:{type:String,write:{target:"rasterFunction"}}})],f.prototype,"functionName",void 0),s([Z("functionName",["rasterFunction","rasterFunctionInfos","rasterFunctionDefinition"])],f.prototype,"readFunctionName",null),s([E({C128:"c128",C64:"c64",F32:"f32",F64:"f64",S16:"s16",S32:"s32",S8:"s8",U1:"u1",U16:"u16",U2:"u2",U32:"u32",U4:"u4",U8:"u8",UNKNOWN:"unknown"},{ignoreUnknown:!1}),l({json:{default:"unknown"}})],f.prototype,"outputPixelType",void 0),s([l({type:String,json:{read:!0,write:!0}})],f.prototype,"variableName",void 0),s([l({type:Object,json:{name:"rasterFunctionDefinition"}})],f.prototype,"rasterFunctionDefinition",void 0),f=ue=s([B("esri.layers.support.RasterFunction")],f);const ua=f,ga=Pe()({RSP_NearestNeighbor:"nearest",RSP_BilinearInterpolation:"bilinear",RSP_CubicConvolution:"cubic",RSP_Majority:"majority"}),pa=Pe()({esriNoDataMatchAny:"any",esriNoDataMatchAll:"all"});var pe;const Rt={base:Mt,key:"type",typeMap:{extent:se,polygon:Dt}};let H=pe=class extends z{constructor(e){super(e),this.areaOfInterest=null,this.subsetDefinitions=null}get dimensions(){const{subsetDefinitions:e}=this;if(e==null||e.length===0)return[];const t=new Map;e.forEach(a=>{if(!a.dimensionName)return;let r,o;if(Array.isArray(a.values[0])){const n=a.values;r=n[0][0],o=n[a.values.length-1][1]}else{const n=a.values;r=n[0],o=n[a.values.length-1]}if(t.has(a.dimensionName)){const n=t.get(a.dimensionName);n[0]=Math.min(r,n[0]),n[1]=Math.max(o,n[1])}else t.set(a.dimensionName,[r,o])});const i=[];for(const a of t)i.push({name:a[0],extent:a[1]});return i}get variables(){const{subsetDefinitions:e}=this;if(e==null||e.length===0)return[];const t=new Set;return e.forEach(i=>{i.variableName&&t.add(i.variableName)}),[...t]}clone(){var i;const e=(i=this.subsetDefinitions)==null?void 0:i.map(a=>a.clone()),t=this.areaOfInterest?this.areaOfInterest.clone():this.areaOfInterest;return new pe({areaOfInterest:t,subsetDefinitions:e})}};s([l({types:Rt,json:{read:ft,write:!0}})],H.prototype,"areaOfInterest",void 0),s([l({readOnly:!0})],H.prototype,"dimensions",null),s([l({readOnly:!0})],H.prototype,"variables",null),s([l({type:[Nt],json:{write:!0}})],H.prototype,"subsetDefinitions",void 0),H=pe=s([B("esri.layers.support.MultidimensionalSubset")],H);const Ia=H;class ma{constructor(){this._workerThread=null,this._destroyed=!1}async initialize(){const t=await bt("RasterWorker");this._destroyed?t.close():this._workerThread=t}destroy(){this._destroyed=!0,this._workerThread&&(this._workerThread.close(),this._workerThread=null)}async convertVectorFieldData(t,i){if(!this._workerThread)throw new T("raster-jobhandler:no-connection","no available worker connection");const a=await this._workerThread.invoke("convertVectorFieldData",{pixelBlock:t.pixelBlock.toJSON(),type:t.dataType},i);return a?new U(a):null}async decode(t,i){if(!this._workerThread)throw new T("raster-jobhandler:no-connection","no available worker connection");const a=await this._workerThread.invoke("decode",t,i);return a?new U(a):null}async symbolize(t,i){if(!this._workerThread)throw new T("raster-jobhandler:no-connection","no available worker connection");const a={extent:t.extent&&t.extent.toJSON(),pixelBlock:g(t.pixelBlock)&&t.pixelBlock.toJSON(),simpleStretchParams:t.simpleStretchParams,bandIds:t.bandIds},r=await this._workerThread.invoke("symbolize",a,i);return r?new U(r):null}async updateSymbolizer(t,i){var r;if(!this._workerThread)throw new T("raster-jobhandler:no-connection","no available worker connection");const a=(r=t==null?void 0:t.rendererJSON)==null?void 0:r.histograms;await Promise.all(this._workerThread.broadcast("updateSymbolizer",{symbolizerJSON:t.toJSON(),histograms:a},i))}async updateRasterFunction(t,i){if(!this._workerThread)throw new T("raster-jobhandler:no-connection","no available worker connection");await Promise.all(this._workerThread.broadcast("updateRasterFunction",{rasterFunctionJSON:t.toJSON()},i))}async process(t,i){var r;if(!this._workerThread)throw new T("raster-jobhandler:no-connection","no available worker connection");const a=await this._workerThread.invoke("process",{extent:(r=t.extent)==null?void 0:r.toJSON(),primaryPixelBlocks:t.primaryPixelBlocks.map(o=>g(o)?o.toJSON():null),primaryRasterIds:t.primaryRasterIds},i);return a?new U(a):null}async stretch(t,i){if(!this._workerThread)throw new T("raster-jobhandler:no-connection","no available worker connection");if(!(t!=null&&t.pixelBlock))return null;const a={srcPixelBlock:t.pixelBlock.toJSON(),stretchParams:t.stretchParams},r=await this._workerThread.invoke("stretch",a,i);return r?new U(r):null}async split(t,i){if(!this._workerThread)throw new T("raster-jobhandler:no-connection","no available worker connection");if(!(t!=null&&t.pixelBlock))return null;const a={srcPixelBlock:t.pixelBlock.toJSON(),tileSize:t.tileSize,maximumPyramidLevel:t.maximumPyramidLevel},r=await this._workerThread.invoke("split",a,i);return r&&r.forEach((o,n)=>{r.set(n,o?U.fromJSON(o):null)}),r}async estimateStatisticsHistograms(t,i){if(!this._workerThread)throw new T("raster-jobhandler:no-connection","no available worker connection");if(!(t!=null&&t.pixelBlock))return null;const a={srcPixelBlock:t.pixelBlock.toJSON()};return await this._workerThread.invoke("estimateStatisticsHistograms",a,i)}async mosaicAndTransform(t,i){var o;if(!this._workerThread)throw new T("raster-jobhandler:no-connection","no available worker connection");if(!((o=t==null?void 0:t.srcPixelBlocks)!=null&&o.length))return{pixelBlock:null};const a={...t,srcPixelBlocks:t.srcPixelBlocks.map(n=>g(n)?n.toJSON():null)},r=await this._workerThread.invoke("mosaicAndTransform",a,i);return{pixelBlock:r.pixelBlock?new U(r.pixelBlock):null,localNorthDirections:r.localNorthDirections}}async createFlowMesh(t,i){if(!this._workerThread)throw new T("raster-jobhandler:no-connection","no available worker connection");const a={buffer:t.flowData.data.buffer,maskBuffer:t.flowData.mask.buffer,width:t.flowData.width,height:t.flowData.height},{meshType:r,simulationSettings:o}=t,n=await this._workerThread.invoke("createFlowMesh",{meshType:r,flowData:a,simulationSettings:o},{...i,transferList:[a.buffer,a.maskBuffer]});return{vertexData:new Float32Array(n.vertexBuffer),indexData:new Uint32Array(n.indexBuffer)}}getProjectionOffsetGrid(t,i){if(!this._workerThread)throw new T("raster-jobhandler:no-connection","no available worker connection");const a=g(t.datumTransformation)?t.datumTransformation.steps.map(n=>({wkid:n.wkid,wkt:n.wkt,isInverse:n.isInverse})):null,r=g(t.rasterTransform)?t.rasterTransform.toJSON():null,o={projectedExtent:t.projectedExtent.toJSON(),srcBufferExtent:t.srcBufferExtent.toJSON(),pixelSize:t.pixelSize,hasWrapAround:t.hasWrapAround,spacing:t.spacing,datumTransformationSteps:a,rasterTransform:r,isAdaptive:t.isAdaptive,includeGCSGrid:t.includeGCSGrid};return this._workerThread.invoke("getProjectionOffsetGrid",o,i)}}const Et=.25,Pt=Fe.fromJSON({type:"multipart",colorRamps:[{fromColor:[0,0,255],toColor:[0,255,255]},{fromColor:[0,255,255],toColor:[255,255,0]},{fromColor:[255,255,0],toColor:[255,0,0]}]}),Ze=Fe.fromJSON(Ut[0]),Ye=new Set(["scientific","standard-time","vector-uv","vector-magdir","vector-u","vector-v","vector-magnitude","vector-direction"]);function ya(e,t){const{attributeTable:i,colormap:a}=e;if(Ae(e)){const r=ea(e);if(g(r))return r}if(g(a)){const r=Kt(e);if(g(r))return r}if(g(i)){const r=_t(e);if(g(r))return r}return Gt(e,t)}function wa(e,t=!1){const i=["raster-stretch"];return at(e)&&i.push("raster-colormap"),tt(e)&&i.push("unique-value"),Qt(e,t)&&i.push("class-breaks"),$t(e)&&i.push("raster-shaded-relief"),Ae(e)&&i.push("vector-field"),qt(e)&&i.push("flow"),i}function ha(e,t,i){const a=["nearest","bilinear","cubic","majority"].find(r=>r===(i==null?void 0:i.toLowerCase()));return t==="Map"?a??"bilinear":e.dataType==="standard-time"?a??"nearest":e.dataType==="thematic"||e.attributeTable||e.colormap?a==="nearest"||a==="majority"?a:"nearest":a??"bilinear"}function Gt(e,t){e=Vt(e,t==null?void 0:t.variableName);const{bandCount:i}=e;let{bandIds:a,stretchType:r}=t||{};a!=null&&a.some(I=>I>=i)&&(a=null);let o=O(e.statistics),n=O(e.histograms);i>1?(a=a!=null&&a.length?a:Wt(e),o=o==null?null:a==null?void 0:a.map(I=>o[I]),n=n==null?null:a==null?void 0:a.map(I=>n[I])):a=[0],r==null&&(r=Ot(e));let c=!1;switch(r){case"none":c=!1;break;case"percent-clip":c=!(n!=null&&n.length);break;default:c=!(o!=null&&o.length)}const{dataType:m}=e,M=(a==null?void 0:a.length)===1&&Ye.has(m)?Pt:null,y=new we({stretchType:r,dynamicRangeAdjustment:c,colorRamp:M,outputMin:0,outputMax:255,gamma:(a==null?void 0:a.length)===1?[1]:[1,1,1],useGamma:!1});return r==="percent-clip"?y.maxPercent=y.minPercent=Et:r==="standard-deviation"&&(y.numberOfStandardDeviations=2),c||!g(e.multidimensionalInfo)&&!(t!=null&&t.includeStatisticsInStretch)||(r==="percent-clip"?y.histograms=n:r!=="min-max"&&r!=="standard-deviation"||(y.statistics=o)),y}function Vt(e,t){if(!t)return e;let i=O(e.statistics),a=O(e.histograms);const{multidimensionalInfo:r}=e;if(t&&g(r)){const o=r.variables.find(n=>n.name===t);if(o){const{statistics:n,histograms:c}=o;n!=null&&n.length&&(i=n),c!=null&&c.length&&(a=c)}}return $e.fromJSON({...e.toJSON(),statistics:i,histograms:a})}function Wt(e){const t=e.bandCount;if(t===1)return null;if(t===2)return[0];const i=e.keyProperties&&e.keyProperties.BandProperties;let a;if(i&&i.length===t){const{red:r,green:o,blue:n,nir:c}=Jt(i);r!=null&&o!=null&&n!=null?a=[r,o,n]:c!=null&&r!=null&&o!=null&&(a=[c,r,o])}return!a&&t>=3&&(a=[0,1,2]),a}function Jt(e){var i;const t={};for(let a=0;a<e.length;a++){const r=e[a],o=(i=r.BandName)==null?void 0:i.toLowerCase();if(o==="red")t.red=a;else if(o==="green")t.green=a;else if(o==="blue")t.blue=a;else if(o==="nearinfrared"||o==="nearinfrared_1"||o==="nir")t.nir=a;else if(r.WavelengthMax&&r.WavelengthMin){const n=r.WavelengthMin,c=r.WavelengthMax;t.blue==null&&n>=410&&n<=480&&c>=480&&c<=540?t.blue=a:t.green==null&&n>=490&&n<=560&&c>=560&&c<=610?t.green=a:t.red==null&&n>=595&&n<=670&&c>=660&&c<=730?t.red=a:t.nir==null&&n>=700&&n<=860&&c>=800&&c<=950&&(t.nir=a)}}return t}function Ot(e){let t="percent-clip";const{pixelType:i,dataType:a,histograms:r,statistics:o,multidimensionalInfo:n}=e,c=Ye.has(a)||a==="generic"&&g(n);return i!=="u8"||a!=="processed"&&g(r)&&g(o)?i==="u8"||a==="elevation"||c?t="min-max":g(r)?t="percent-clip":g(o)&&(t="min-max"):t="none",t}function _t(e,t,i,a){if(!tt(e))return null;const{attributeTable:r,statistics:o}=e,n=et(r,t),c=J(r,"red"),m=J(r,"green"),M=J(r,"blue"),y=new Ve,I=[],L=new Set,C=!!(c&&m&&M);if(g(r))r.features.forEach(d=>{const u=d.attributes[n.name];if(!L.has(d.attributes[n.name])&&u!=null){L.add(u);const N=C&&(c.type==="single"||c.type==="double")&&(m.type==="single"||m.type==="double")&&(M.type==="single"||M.type==="double")&&!r.features.some(j=>j.attributes[c.name]>1||j.attributes[m.name]>1||j.attributes[M.name]>1),v=N?255:1;I.push(new je({value:d.attributes[n.name],label:d.attributes[n.name]+"",symbol:{type:"simple-fill",style:"solid",outline:null,color:new A(C?[d.attributes[c.name]*v,d.attributes[m.name]*v,d.attributes[M.name]*v,1]:[0,0,0,0])}}))}});else if(o!=null&&o[0])for(let d=o[0].min;d<=o[0].max;d++)I.push(new je({value:d,label:d.toString(),symbol:{type:"simple-fill",style:"solid",outline:null,color:new A([0,0,0,0])}}));if(I.sort((d,u)=>d.value&&typeof d.value.valueOf()=="string"?0:d.value>u.value?1:-1),!C){const d=Ke(Ze,{numColors:I.length});I.forEach((u,N)=>u.symbol.color=new A(d[N].slice(1,4))),y.colorRamp=Ze}return new me({field:n.name,uniqueValueInfos:I,authoringInfo:y})}function et(e,t,i){let a;return g(e)?(a=t?e.fields.find(r=>t.toLowerCase()===r.name.toLowerCase()):Ft(e.fields),a||(i||(a=e.fields.find(r=>r.type==="string")),a||(a=J(e,"value")))):a=new Ct({name:"value"}),a}function Ft(e){let t;for(let i=0;i<e.length;i++){const a=e[i].name.toLowerCase();if(e[i].type==="string"){if(a.startsWith("class")){t=e[i];break}t==null&&(a.endsWith("name")||a.endsWith("type"))&&(t=e[i])}}return t}function J(e,t){return ee(e)?null:e.fields.find(i=>i.name.toLowerCase()===t)}function tt(e,t){const{attributeTable:i,bandCount:a}=e;return ee(i)&&Xt(e)?!0:!(ee(i)||a>1)}function at(e){const{bandCount:t,colormap:i}=e;return g(i)&&i.length>0&&t===1}function Kt(e){if(!at(e))return null;let t;const{attributeTable:i,colormap:a}=e;if(g(i)){const r=J(i,"value"),o=et(i,null,!0);o.type==="string"&&(t={},i.features.forEach(n=>{const c=n.attributes;t[c[r.name]]=o?c[o.name]:c[r.name]}))}return ye.createFromColormap(O(a),t)}function $t(e){const{bandCount:t,dataType:i,pixelType:a}=e;return i==="elevation"||i==="generic"&&t===1&&(a==="s16"||a==="s32"||a==="f32"||a==="f64")}function Qt(e,t=!1){const{attributeTable:i,bandCount:a}=e;return a===1&&(!t||g(i)||g(e.histograms))}function Xt(e){var t,i,a;return["u8","s8"].includes(e.pixelType)&&((i=(t=e.statistics)==null?void 0:t[0])==null?void 0:i.min)!=null&&((a=e.statistics[0])==null?void 0:a.max)!=null&&e.bandCount===1}function Ae(e){const{dataType:t}=e;return t==="vector-uv"||t==="vector-magdir"}function qt(e){const{dataType:t}=e;return t==="vector-uv"||t==="vector-magdir"}const Yt=new Map([["m/s","meter-per-second"],["km/h","kilometer-per-hour"],["knots","knots"],["ft/s","feet-per-second"],["mph","mile-per-hour"]]);function ea(e){if(!Ae(e))return null;let t;if(g(e.statistics)&&e.statistics.length&&(e.dataType==="vector-magdir"||e.dataType==="vector-uv")){const{minMagnitude:r,maxMagnitude:o}=ta(e.dataType,e.statistics);t=[{type:"size",field:"Magnitude",minSize:10,maxSize:40,minDataValue:r,maxDataValue:o}]}const i=g(e.multidimensionalInfo)?Yt.get(e.multidimensionalInfo.variables[0].unit):null,a=new he({visualVariables:t,inputUnit:i,rotationType:"geographic"});return a.visualVariables=[...a.sizeVariables,...a.rotationVariables],a}function ke(e){var t;return{color:(t=e.symbolLayers[0].material)==null?void 0:t.color,type:"esriSFS",style:"esriSFSSolid"}}function Aa(e){var t,i,a;if(e.type==="uniqueValue"){const r=e.uniqueValueInfos,o=r==null?void 0:r[0].symbol;return(t=o==null?void 0:o.symbolLayers)!=null&&t.length&&(e.uniqueValueInfos=r==null?void 0:r.map(n=>({value:n.value,label:n.label,symbol:n.symbol?ke(n.symbol):null}))),e}if(e.type==="classBreaks"){const r=e.classBreakInfos;return(a=(i=r[0].symbol)==null?void 0:i.symbolLayers)!=null&&a.length&&(e.classBreakInfos=r.map(o=>({classMinValue:o.classMinValue,classMaxValue:o.classMaxValue,label:o.label,symbol:o.symbol?ke(o.symbol):null}))),e}return e}function ta(e,t){let i,a;if(e==="vector-magdir")i=t[0].min,a=t[0].max;else{const r=t[0].min,o=t[0].max,n=t[1].min,c=t[1].max;i=0,a=Math.max(Math.abs(r),Math.abs(n),Math.abs(o),Math.abs(c))}return{minMagnitude:i,maxMagnitude:a}}async function it(e,t,i){var De,be,Ce,Te,ve,Se;const a=Tt(e),{renderingRule:r,sourceJSON:o}=t||{},n=r?JSON.stringify(r.rasterFunctionDefinition||r):null,c=vt({...a.query,renderingRule:n,f:"json"}),m=St(c,i);e=a.path;const M=o||await k(e,m).then(D=>D.data),y=M.hasRasterAttributeTable?k(`${e}/rasterAttributeTable`,m):null,I=M.hasColormap?k(`${e}/colormap`,m):null,L=M.hasHistograms?k(`${e}/histograms`,m):null,C=M.currentVersion>=10.3?k(`${e}/keyProperties`,m):null,d=M.hasMultidimensions?k(`${e}/multidimensionalInfo`,m):null,u=await Promise.allSettled([y,I,L,C,d]);let N=null;if(M.minValues&&M.minValues.length===M.bandCount){N=[];for(let D=0;D<M.minValues.length;D++)N.push({min:M.minValues[D],max:M.maxValues[D],avg:M.meanValues[D],stddev:M.stdvValues[D]})}const v=se.fromJSON(M.extent),j=Math.ceil(v.width/M.pixelSizeX-.1),rt=Math.ceil(v.height/M.pixelSizeY-.1),fe=dt.fromJSON(M.spatialReference||M.extent.spatialReference),nt=u[0].status==="fulfilled"&&u[0].value?Lt.fromJSON(u[0].value.data):null,ot=u[1].status==="fulfilled"?(De=u[1].value)==null?void 0:De.data.colormap:null,st=u[2].status==="fulfilled"?(be=u[2].value)==null?void 0:be.data.histograms:null,P=u[3].status==="fulfilled"?((Ce=u[3].value)==null?void 0:Ce.data)??{}:{},G=u[4].status==="fulfilled"?(Te=u[4].value)==null?void 0:Te.data.multidimensionalInfo:null;(ve=G==null?void 0:G.variables)!=null&&ve.length&&G.variables.forEach(D=>{var Le;(Le=D.statistics)!=null&&Le.length&&D.statistics.forEach(F=>{F.avg=F.mean,F.stddev=F.standardDeviation})});const{defaultVariable:ae,serviceDataType:_}=M;ae&&ae!==P.DefaultVariable&&(P.DefaultVariable=ae),_&&_.includes("esriImageServiceDataTypeVector")&&!_.includes(P.DataType)&&(P.DataType=_.replace("esriImageServiceDataType",""));let ie=M.noDataValue;return(Se=M.noDataValues)!=null&&Se.length&&M.noDataValues.some(D=>D!==ie)&&(ie=M.noDataValues),new $e({width:j,height:rt,bandCount:M.bandCount,extent:se.fromJSON(M.extent),spatialReference:fe,pixelSize:new Ee({x:M.pixelSizeX,y:M.pixelSizeY,spatialReference:fe}),pixelType:M.pixelType.toLowerCase(),statistics:N,attributeTable:nt,colormap:ot,histograms:st,keyProperties:P,noDataValue:ie,multidimensionalInfo:G})}function fa(e,t,i){return it(e,{sourceJSON:t},i)}function Da(e,t,i){return it(e,{renderingRule:t},i)}export{Aa as $,Wt as L,Vt as S,ha as V,da as a,Ia as c,ze as d,Da as f,pa as i,ya as j,wa as k,Be as l,fa as m,ma as n,ga as o,ua as w};
