package org.thingsboard.server.dao.util.imodel.query.department;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.query.TimeableQueryEntity;
import org.thingsboard.server.dao.util.imodel.query.annotations.QueryAcquired;
import org.thingsboard.server.dao.util.imodel.query.annotations.QueryLike;

@Getter
@Setter
public class DepartmentPageRequest extends TimeableQueryEntity {
    @QueryAcquired
    private String parentId;

    // "部门名称"
    @QueryLike
    private String name;

    // 部门id，多个用逗号隔开
    private String deptIdList;

}
