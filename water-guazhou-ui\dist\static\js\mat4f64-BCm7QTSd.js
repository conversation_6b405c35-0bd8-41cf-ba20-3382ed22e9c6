function n(){return[1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1]}function g(r){return[r[0],r[1],r[2],r[3],r[4],r[5],r[6],r[7],r[8],r[9],r[10],r[11],r[12],r[13],r[14],r[15]]}function j(r,t,o,u,c,e,l,a,f,i,_,b,p,y,T,d){return[r,t,o,u,c,e,l,a,f,i,_,b,p,y,T,d]}function m(r,t){return new Float64Array(r,t,16)}const s=n();Object.freeze(Object.defineProperty({__proto__:null,IDENTITY:s,clone:g,create:n,createView:m,fromValues:j},Symbol.toStringTag,{value:"Module"}));export{n as e,m as n,s as o,g as r,j as t};
