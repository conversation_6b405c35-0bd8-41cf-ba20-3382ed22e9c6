package org.thingsboard.server.dao.smartService.kpi;

import org.thingsboard.server.dao.model.sql.smartService.kpi.KpiNormSystemConfig;

import java.util.List;

/**
 * 黑名单
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-10-27
 */
public interface KpiNormSystemConfigService {
    List<KpiNormSystemConfig> getList(String mainId, String tenantId);

    KpiNormSystemConfig save(KpiNormSystemConfig kpiNormSystemConfig);

    boolean check(KpiNormSystemConfig kpiNormSystemConfig);
}
