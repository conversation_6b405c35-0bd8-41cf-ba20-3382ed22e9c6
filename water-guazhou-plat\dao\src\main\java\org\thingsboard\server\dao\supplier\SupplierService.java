package org.thingsboard.server.dao.supplier;

import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.SupplierEntity;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.List;

/**
 * 区域
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-08-24
 */
public interface SupplierService {
    PageData getList(String name, String address, String status, String importance, int page, int size, String tenantId);

    SupplierEntity save(SupplierEntity supplierEntity);

    IstarResponse delete(List<String> ids);
}
