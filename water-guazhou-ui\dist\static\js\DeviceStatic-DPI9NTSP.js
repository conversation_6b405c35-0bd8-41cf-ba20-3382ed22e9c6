import{d as C,a8 as B,ab as x,c as v,o as L,g as n,n as r,aB as N,aJ as S,aw as k,q as w,p as c,bh as u,i as E,cU as V,C as F}from"./index-r0dFAfgr.js";import{i as d,a as _,b,c as h,d as f,e as D}from"./6-4nR55Xef.js";import{g as I}from"./index-CknacZq4.js";const J={class:"wheeling-items"},U={class:"info"},j={class:"text"},q={class:"value"},z=C({__name:"DeviceStatic",props:{pipeData:{}},setup(y){const l=y;B(()=>{var i,e,a,o,m,p;const t=x(((i=l.pipeData)==null?void 0:i.pipeLength)??0);return{data:[{label:"供水管长",value:t.value.toFixed(2)+t.unit,unit:"米",color:"lightblue",img:d},{label:"阀门",value:((e=l.pipeData)==null?void 0:e.valve)||0,unit:"个",color:"lightblue",img:_},{label:"水表",value:((a=l.pipeData)==null?void 0:a.meter)||0,unit:"个",color:"orange",img:b},{label:"排气阀",value:((o=l.pipeData)==null?void 0:o.drayAirValve)||0,unit:"个",color:"orange",img:h},{label:"消防栓",value:((m=l.pipeData)==null?void 0:m.hydrant)||0,unit:"个",color:"seablue",img:f},{label:"三通",value:((p=l.pipeData)==null?void 0:p.threeCorss)||0,unit:"个",color:"seablue",img:D}]}});const g=v({}),s=v([{label:"供水管长",value:0,unit:"米",color:"lightblue",img:d},{label:"阀门",value:0,unit:"个",color:"lightblue",img:_},{label:"水表",value:0,unit:"个",color:"orange",img:b},{label:"排气阀",value:0,unit:"个",color:"orange",img:h},{label:"消防栓",value:0,unit:"个",color:"seablue",img:f},{label:"三通",value:0,unit:"个",color:"seablue",img:D}]);return L(async()=>{I("智慧管网").then(t=>{g.value={...JSON.parse(t.data.data.jsonData)},g.value.list.forEach((i,e)=>{s.value[e]={...s.value[e],...i}})})}),(t,i)=>{const e=V;return n(),r("div",J,[(n(!0),r(N,null,S(E(s),(a,o)=>(n(),r("div",{key:o,class:k(["wheeling-item",a.color])},[w(e,{class:"img",fit:"contain",src:a.img},null,8,["src"]),c("div",U,[c("div",j,u(a.label),1),c("div",q,u(a.value)+" "+u(a.unit),1)])],2))),128))])}}}),G=F(z,[["__scopeId","data-v-74176826"]]);export{G as default};
