/**
 * Copyright © 2017 The Thingsboard Authors
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.sql.orginData;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.thingsboard.server.dao.model.sql.OriginDataEntity;
import org.thingsboard.server.dao.util.SqlDao;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/3/12 17:46
 */

@SqlDao
public interface OriginRepository extends CrudRepository<OriginDataEntity, String> {


    @Query(" SELECT o FROM OriginDataEntity o where  o.dataSourceId =:id and o.updateTime >=:startTime and o.updateTime <=:endTime order by o.updateTime")
    List<OriginDataEntity> find(@Param("id") String dataSourceId,
                                                            @Param("startTime") long startTime,
                                                            @Param("endTime") long end);

    OriginDataEntity findFirstByDataSourceIdOrderByUpdateTimeDesc(@Param("data_source_id") String dataSourceId);

    List<OriginDataEntity> findByDataSourceId(@Param("data_source_id") String dataSourceId);


}
