<template>
  <div class="flex">
    <div
      v-for="(item,index) in props.progressvalue"
      :key="index"
      class="progress"
    >
      <el-progress
        :color="item.color"
        type="circle"
        :percentage="100"
        status="success"
      >
        <div class="span_text">
          <span style="font-size: 20px;">{{ item.value }}</span>
          <span>{{ item.unit }}</span>
        </div>
      </el-progress>
      <span>{{ item.name }}</span>
    </div>
  </div>
</template>

<script lang="ts" setup>

const props = defineProps<{progressvalue: any}>()

</script>

<style lang="scss" scoped>
.span_text {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.progress{
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
  justify-content: space-evenly;
  margin-right: 20px;
}
.flex{
    height: 286px;
}
</style>
