<template>
  <div style="width: calc(100% + 20px); height: 400px; overflow-y: auto; margin: -10px -10px 0px">
    <el-card v-for="(item, i) in state" :key="i" shadow="never" style="margin-bottom: 5px" @click="read(item)">
      <div class="item list">
        <el-button class="icon" :type="setType()[0] as any" :icon="setType()[1]" circle />
        <div>
          <span class="title">{{ item.topic }}</span>
          <span class="time">{{ dayjs(item.time).format('YYYY-MM-DD HH:mm:ss') }}</span>
        </div>
      </div>
    </el-card>
  </div>
  <div class="btn">
    <el-button class="btns_btn" type="primary" @click="toInformation"> 查看更多 </el-button>
    <el-button class="btns_btn" type="success" @click="readAll"> 一键已读 </el-button>
  </div>
  <!-- <div class="item btn">

  </div> -->
</template>

<script lang="ts" setup>
import { ElMessage, dayjs } from 'element-plus';
import { useRouter } from 'vue-router';
import { Bell, ChatDotRound, WarnTriangleFilled } from '@element-plus/icons-vue';
import { getSystemNotify, postReadAll, postRead } from '@/api/admin';
import { removeSlash } from '@/utils/removeIdSlash';
import { useUserStore } from '@/store';

const props = defineProps<{ type: number }>();
const emit = defineEmits(['refreshData']);
const router = useRouter();
const state = ref<any[]>([]);

function toInformation() {
  console.log(props.type, 'asdasd');

  router.push({
    path: '/accountManage/news/' + props.type
  });
}

function read(params: any) {
  postRead({ id: params.id }).then((res) => {
    if (res.data.code === 200) {
      toInformation();
    }
  });
}

function readAll() {
  const params = {
    type: props.type,
    to: removeSlash(useUserStore().id)
  };
  postReadAll(params).then((res) => {
    if (res.data.code === 200) {
      ElMessage.success('全部已读');
      refreshData();
      emit('refreshData');
    } else {
      ElMessage.warning('全部已读失败');
    }
  });
}

// 类型
function setType() {
  switch (props.type) {
    case 0:
      return ['success', ChatDotRound];
    case 1:
      return ['danger', WarnTriangleFilled];
    case 2:
      return ['primary', Bell];
    default:
      return ['success', WarnTriangleFilled];
  }
}

onMounted(() => {
  refreshData();
});

const refreshData = () => {
  const params = {
    page: 1,
    size: 99,
    type: props.type,
    to: removeSlash(useUserStore().id),
    status: 0
  };
  getSystemNotify(params).then((res) => {
    state.value = res.data.data.data || [];
  });
};
</script>

<style lang="scss" scoped>
.item {
  margin: -10px;
}

.btn {
  height: 40px;
  line-height: 40px;
  text-align: center;
  display: flex;
  margin-bottom: -10px;
}

.list {
  display: flex;
  align-items: center;
}

.icon {
  margin-right: 20px;
}

.title {
  font-size: 14px;
  font-weight: bolder;
  font-weight: 800;
}

.time {
  font-size: 12px;
}

.btns_btn {
  width: 50%;
}
</style>
