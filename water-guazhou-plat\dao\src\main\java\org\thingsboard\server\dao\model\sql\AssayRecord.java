package org.thingsboard.server.dao.model.sql;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.TB_ASSAY_RECORD_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class AssayRecord {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.TB_ASSAY_RECORD_STATION_ID)
    private String stationId;

    @Column(name = ModelConstants.TB_ASSAY_RECORD_DISSOLVED_OXYGEN)
    private BigDecimal dissolvedOxygen;

    @Column(name = ModelConstants.TB_ASSAY_RECORD_CONDUCTIVITY)
    private BigDecimal conductivity;

    @Column(name = ModelConstants.TB_ASSAY_RECORD_TEMPERATURE)
    private BigDecimal temperature;

    @Column(name = ModelConstants.TB_ASSAY_RECORD_CHLOROPHYL)
    private BigDecimal chlorophyl;

    @Column(name = ModelConstants.TB_ASSAY_RECORD_AMMONIA_NITROGEN)
    private BigDecimal ammoniaNitrogen;

    @Column(name = ModelConstants.TB_ASSAY_RECORD_NITRATE)
    private BigDecimal nitrate;

    @Column(name = ModelConstants.TB_ASSAY_RECORD_DISSOLVED_ORGANIC_CARBON)
    private BigDecimal dissolvedOrganicCarbon;

    @Column(name = ModelConstants.TB_ASSAY_RECORD_ORGANIC_CARBON)
    private BigDecimal organicCarbon;

    @Column(name = ModelConstants.TB_ASSAY_RECORD_TURBIDITY)
    private BigDecimal turbidity;

    @Column(name = ModelConstants.TB_ASSAY_RECORD_PH)
    private BigDecimal ph;

    @Column(name = ModelConstants.TB_ASSAY_RECORD_CHROMATICITY)
    private BigDecimal chromaticity;

    @Column(name = ModelConstants.TB_ASSAY_RECORD_UV254)
    private BigDecimal uv254;

    @Column(name = ModelConstants.TB_ASSAY_RECORD_STATUS)
    private String status;

    @Column(name = ModelConstants.TB_ASSAY_RECORD_STATION_NAME)
    private String stationName;

    @Column(name = ModelConstants.TIME_SHARING_TIME)
    private Date time;

    @Column(name = ModelConstants.TB_ASSAY_RECORD_FILE)
    private String file;

    @Column(name = ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;


}
