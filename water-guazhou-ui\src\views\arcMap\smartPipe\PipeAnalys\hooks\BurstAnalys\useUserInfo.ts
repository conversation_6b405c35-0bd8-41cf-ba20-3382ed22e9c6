import { getUserListByValveNumberList } from '@/api/mapservice/gisUser'
import { excuteQuery, getSubLayerIds, initQueryParams } from '@/utils/MapHelper'
import { SLMessage } from '@/utils/Message'

export const useUserInfo = () => {
  const loading = ref<boolean>(false)
  const staticState: {
    devicelayers: string[]
    view?: __esri.MapView
    deviceids: any[]
  } = {
    devicelayers: ['阀门', '计量装置'],
    deviceids: []
  }

  const TableConfig_User = ref<ITable>({
    columns: [
      { prop: 'yhbh', label: '用户编号' },
      { prop: 'yhxm', label: '用户姓名' },
      { prop: 'yhdz', label: '用户地址' },
      { prop: 'lxdh', label: '联系电话' },
      { prop: 'dxdh', label: '短信电话' },
      { prop: 'ysxz', label: '用水性质' },
      { prop: 'vnum', label: '阀门编号' },
      { prop: 'sbbh', label: '水表编号' }
    ],
    dataList: [],
    pagination: {
      hide: true
    }
  })
  const init = async (
    mapView?: __esri.MapView,
    tabs?: any,
    callBack?: () => any
  ) => {
    if (!mapView) return
    loading.value = true
    try {
      staticState.deviceids = []
      await getDeviceIds(0, tabs)
      const res = await getUserListByValveNumberList(staticState.deviceids)
      TableConfig_User.value.dataList = res.data.Data.rows
      callBack?.()
    } catch (error) {
      SLMessage.error('暂无相关用户信息')
    }
    loading.value = false
  }
  /** 通过OBJECTID查询SID */
  const getDeviceIds = async (index, tabs) => {
    const tabName = staticState.devicelayers[index]
    if (!staticState.view || !tabName) {
      return
    }
    try {
      const layerId = await getSubLayerIds(
        staticState.view,
        undefined,
        undefined,
        tabName
      )
      const oids = tabs.find(item => item.name === tabName)?.data
      const res = await excuteQuery(
        window.SITE_CONFIG.GIS_CONFIG.gisService + window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService + '/' + layerId,
        initQueryParams({
          outFields: ['SID'],
          objectIds: oids,
          returnGeometry: false
        })
      )
      res.features.map(item => {
        staticState.deviceids.push(item.attributes['SID'])
      })
      if (index < staticState.devicelayers.length - 1) {
        await getDeviceIds(++index, tabs)
      }
    } catch (error) {
      console.dir(error)
    }
  }
  const destroy = () => {
    staticState.view = undefined
    loading.value = false
  }
  onBeforeUnmount(() => {
    destroy()
  })
  return {
    init,
    destroy,
    loading,
    TableConfig_User
  }
}
export default useUserInfo
