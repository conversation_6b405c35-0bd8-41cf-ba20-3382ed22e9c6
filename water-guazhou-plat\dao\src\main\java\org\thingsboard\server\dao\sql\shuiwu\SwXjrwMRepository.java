package org.thingsboard.server.dao.sql.shuiwu;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.thingsboard.server.dao.model.sql.shuiwu.SwXjrwMEntity;

import java.util.Date;
import java.util.List;

public interface SwXjrwMRepository extends JpaRepository<SwXjrwMEntity, String> {

    @Query("SELECT DISTINCT m FROM SwXjrwMEntity m , SwXjrwCEntity c " +
            "WHERE m.id = c.mainId AND m.tenantId = ?3 AND m.content LIKE %?1% AND m.status IN ?2 AND m.status <> '3' AND c.deviceId LIKE %?4%")
    Page<SwXjrwMEntity> findList(String content, List<String> statusList, String tenantId, String deviceId, Pageable pageable);

    @Query("SELECT DISTINCT m FROM SwXjrwMEntity m, SwXjrwCEntity c " +
            "WHERE m.id = c.mainId AND m.tenantId = ?2 AND m.content LIKE %?1% AND m.status = '3' AND m.createTime BETWEEN ?3 AND ?4 AND c.deviceId LIKE %?5%")
    Page<SwXjrwMEntity> findHistoryList(String content, String tenantId, Date createTime1, Date createTime2, String deviceId, Pageable pageable);

    List<SwXjrwMEntity> findAllByTenantId(String tenantId);

    @Query("SELECT DISTINCT m.status, count(m.status) FROM SwXjrwMEntity m, SwXjrwCEntity c " +
            "WHERE m.id = c.mainId AND c.deviceId LIKE %?1% " +
            "GROUP BY m.status")
    List groupByStatus(String deviceId);

    @Query("SELECT DISTINCT m.status, count(m.status) FROM SwXjrwMEntity m " +
            "WHERE m.tenantId = ?1 " +
            "GROUP BY m.status")
    List groupByStatusAll(String tenantId);

    @Query("SELECT DISTINCT m.status, count(m.status) FROM SwXjrwMEntity m " +
            "WHERE m.tenantId = ?3 AND m.executeTime BETWEEN ?1 AND ?2 " +
            "GROUP BY m.status")
    List groupByStatus(Date startTime, Date endTime, String tenantId);

    List<SwXjrwMEntity> findByTenantIdAndExecuteTimeBetweenOrderByExecuteTime(String tenantId, Date startTime, Date endTime);
}
