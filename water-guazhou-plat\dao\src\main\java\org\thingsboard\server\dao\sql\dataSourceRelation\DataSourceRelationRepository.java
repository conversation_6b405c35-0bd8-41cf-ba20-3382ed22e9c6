package org.thingsboard.server.dao.sql.dataSourceRelation;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.thingsboard.server.dao.data_source_relation.DataSourceRelationDao;
import org.thingsboard.server.dao.model.sql.DataSourceEntity;
import org.thingsboard.server.dao.model.sql.DataSourceRelationEntity;
import org.thingsboard.server.dao.util.SqlDao;

import java.util.List;

@SqlDao
public interface DataSourceRelationRepository extends CrudRepository<DataSourceRelationEntity, String> {

    DataSourceRelationEntity findByDataSourceId(String dataSourceId);


}
