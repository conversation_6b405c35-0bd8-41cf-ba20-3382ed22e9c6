import { request } from '@/plugins/axios';

/**
 * 消息类型列表
 * @param params
 * @returns
 */
export const getSystemNotifyTypeList = (params: {
  page: string;
  size: string;
  type: string;
  menuName: string;
  status: string;
}) =>
  request({
    url: '/api/systemNotifyType/list',
    method: 'get',
    params
  });

/**
 * 新增消息类型
 * @param params
 * @returns
 */
export const postSystemNotifyType = (params: {
  model: string;
  type: string;
  menuName: string;
  path: string;
  sendType: string;
  status: string;
}) =>
  request({
    url: '/api/systemNotifyType',
    method: 'post',
    data: params
  });
