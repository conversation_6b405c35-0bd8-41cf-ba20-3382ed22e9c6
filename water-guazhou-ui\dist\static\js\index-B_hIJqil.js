import{d as A,M as K,c,s as X,r as f,x as m,bQ as H,a8 as w,S as Z,o as ee,g as te,n as ae,q as l,i as d,F as y,p as ie,t as le,bo as ne,G as oe,_ as se,aq as re,bl as pe,bm as ce,b6 as de,J as ue,bK as fe,b7 as me,bR as ge}from"./index-r0dFAfgr.js";import{_ as be}from"./CardTable-rdWOL4_6.js";import{_ as ye}from"./CardSearch-CB_HNR-Q.js";import{I as g}from"./common-CvK_P_ao.js";import{m as he,n as _e,o as xe,q as Le,l as ve,r as De}from"./equipmentManage-DuoY00aj.js";import{s as I,i as F,c as z,p as Se,r as ke,a as Ee}from"./equipmentAssetsData-B4Olvyjd.js";import{f as Ve}from"./DateFormatter-Bm9a68Ax.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";import"./xlsx-rVJkW9yq.js";const Ce={class:"wrapper"},Re={class:"item"},Pe=A({__name:"index",setup(Te){const{$btnPerms:h}=K(),D=c(),L=c(),N=c(),M=c(),_=c(),S=c(),k=c([]),U=c({filters:[{label:"供应商名称",field:"name",type:"input",labelWidth:"90px"},{label:"地址关键字",field:"address",type:"input",labelWidth:"90px"},{label:"供应商状态",field:"status",type:"select",labelWidth:"90px",options:I},{label:"重要程度",field:"importance",type:"select",options:F}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",icon:g.QUERY,click:()=>b()},{type:"default",perm:!0,text:"重置",svgIcon:X(me),click:()=>{var e;(e=D.value)==null||e.resetForm(),b()}},{perm:!0,text:"新增",icon:g.ADD,type:"success",click:()=>O()}]}]}),r=f({defaultExpandAll:!0,indexVisible:!0,columns:[{label:"供应商",prop:"name"},{label:"地址",prop:"address"},{label:"联系人",prop:"contact"},{label:"联系电话",prop:"contactPhone"},{label:"网址",prop:"website"},{label:"重要程度",prop:"importance",formatter:e=>{const t=F.find(a=>a.value===e.importance);return t==null?void 0:t.label}},{label:"邮箱",prop:"email"},{label:"发票信息",prop:"invoice"},{label:"供货类别",prop:"deviceTypeId"},{label:"状态",prop:"status",formatter:e=>{const t=I.find(a=>a.value===e.status);return t==null?void 0:t.label}},{label:"备注",prop:"remark"},{label:"创建人",prop:"creatorName"},{label:"创建时间",prop:"createTime",formatter:e=>Ve(e.createTime,"YYYY-MM-DD")},{label:"规模",prop:"companySize",formatter:e=>{const t=z.find(a=>a.value===e.companySize);return t==null?void 0:t.label}}],operationWidth:"200px",operations:[{type:"primary",text:"编辑",icon:g.EXPORT,perm:h("RoleManageEdit"),click:e=>Q(e)},{type:"danger",text:"删除",perm:h("RoleManageDelete"),icon:g.DELETE,click:e=>Y(e)}],dataList:[],pagination:{page:1,limit:20,total:0,refreshData:({page:e,size:t})=>{r.pagination.page=e,r.pagination.limit=t,b()}}}),o=f({indexVisible:!0,height:"350px",titleRight:[{style:{justifyContent:"flex-end"},items:[{type:"btn-group",btns:[{text:"添加",perm:!0,click:()=>{o.dataList.push({id:W()})}}]}]}],dataList:[],columns:[{label:"资质名称",prop:"name",formItemConfig:{type:"input"}},{label:"颁发机构",prop:"organization",formItemConfig:{type:"input"}},{label:"获取时间",prop:"time",formItemConfig:{type:"date",format:"x"}}],operations:[{text:"图片",perm:h("RoleManageEdit"),icon:g.DETAIL,click:e=>{var t;q.defaultValue={...e||{}},(t=S.value)==null||t.openDrawer()}},{type:"danger",text:"移除",icon:g.DELETE,perm:h("RoleManageDelete"),click:e=>{o.dataList=o.dataList.filter(t=>t.id!==e.id)}}],pagination:{hide:!0}}),i=f({indexVisible:!0,height:"350px",titleRight:[{style:{justifyContent:"flex-end"},items:[{type:"btn-group",btns:[{text:"增加材料",perm:!0,click:()=>{var e;n.getDevice(),(e=_.value)==null||e.openDrawer()}}]}]}],dataList:[],columns:[{label:"设备编码",prop:"serialId"},{label:"设备名称",prop:"name"},{label:"规格型号",prop:"model"},{label:"计算单位",prop:"unit"},{label:"采购数量",prop:"num",formItemConfig:{type:"input-number"}},{label:"单价(元)",prop:"price",formItemConfig:{type:"input"}},{label:"税率(%)",prop:"taxRate",formItemConfig:{type:"input"}}],operations:[{type:"danger",text:"移除",icon:g.DELETE,perm:h("RoleManageDelete"),click:e=>{i.dataList=i.dataList.filter(t=>t.id!==e.id)}}],pagination:{hide:!0}}),E=f({title:"新增",labelWidth:"100px",submit:()=>{var e;(e=M.value)==null||e.Submit()},defaultValue:{},group:[]}),V=f({title:"新增",labelWidth:"100px",submit:(e,t)=>{var s;if(t===!0){(s=_.value)==null||s.openDrawer(),n.getDevice();return}let a="添加成功";e.id&&(a="修改成功"),e.supplierQualificationsList=o.dataList,e.supplierGoodsList=i.dataList,he(e).then(()=>{var u;(u=L.value)==null||u.closeDrawer(),m.success(a),b()}).catch(u=>{m.warning(u)})},defaultValue:{},group:[{fields:[{xl:18,type:"input",label:"供应商名称",field:"name",rules:[{required:!0,message:"请输入供应商名称"}]},{xl:6,type:"select",label:"供应商状态",field:"status",options:I,rules:[{required:!0,message:"请选择供应商状态"}]},{xl:6,type:"select",label:"重要程度",field:"importance",options:F,rules:[{required:!0,message:"请选择重要程度"}]},{xl:6,type:"input",label:"邮箱",field:"email",rules:[{type:"email",message:"请输入正确邮箱地址",trigger:"blur"}]},{xl:6,type:"input",label:"供货类别",field:"deviceTypeId"},{xl:6,type:"select",label:"公司规模",field:"companySize",options:z},{xl:6,type:"input",label:"发票信息",field:"invoice"},{xl:6,type:"input",label:"联系人",field:"contact",rules:[{required:!0,message:"请输入联系人"}]},{xl:6,type:"input",label:"联系电话",field:"contactPhone",rules:Se},{xl:6,type:"input",label:"网址",field:"website"},{xl:18,type:"input",label:"地址",field:"address",rules:[{required:!0,message:"请输入地址"}]},{xl:24,type:"textarea",label:"备注",field:"remark"},{type:"divider",text:"其他信息"}]}]}),q=f({title:"图片",width:"500px",labelWidth:"100px",submit:e=>{var t;o.dataList.map(a=>{a.id===e.id&&(a.files=e.files)}),(t=S.value)==null||t.closeDrawer()},defaultValue:{},group:[{fields:[{type:"image",label:"资质图像",field:"files"}]}]}),C=f({title:"设备选择",submit:(e,t)=>{var a;t?n.getDevice(e):(i.dataList=[...i.dataList,...k.value],i.dataList=H(i.dataList,"id"),i.dataList=i.dataList.map(s=>((!s.num||s.num===null)&&(s.num="0"),s)),(a=_.value)==null||a.closeDrawer())},defaultValue:{},group:[{fields:[{xl:8,type:"input",label:"设备编码",field:"serialId"},{xl:8,type:"input",label:"设备名称",field:"name"},{xl:8,type:"input",label:"设备型号",field:"model"},{type:"table",field:"device",config:{indexVisible:!0,height:"350px",dataList:w(()=>n.deviceValue),selectList:[],handleSelectChange:e=>{k.value=e},titleRight:[{style:{justifyContent:"flex-end"},items:[{type:"btn-group",btns:[{text:"搜索",perm:!0,click:()=>{var e;(e=_.value)==null||e.Submit(!0)}}]}]}],columns:[{label:"材料编码",prop:"serialId"},{label:"材料名称",prop:"name"},{label:"型号/规格",prop:"model"},{label:"所属类别",prop:"linkedType"},{label:"单位",prop:"unit"}],pagination:{page:1,limit:20,total:w(()=>n.total),refreshData:({page:e,size:t})=>{C.group[0].fields[3].config.pagination.page=e,C.group[0].fields[3].config.pagination.limit=t,n.getDevice()}}}}]}]});function W(){function e(){return((1+Math.random())*65536|0).toString(16).substring(1)}return e()+e()+e()+e()}const O=()=>{var e;E.title="新增",k.value=[],V.defaultValue={},i.dataList=[],o.dataList=[],(e=L.value)==null||e.openDrawer()};async function P(e){console.log(e),ke(e).then(t=>{let a=1;t&&t.forEach(p=>{const x={number:""+a++};for(const v in p)x[Ee[v]]=p[v];n.selectList.push(x)});const s=n.selectList.map(p=>JSON.stringify(p)),u=[...new Set(s)];n.selectList=u.map(p=>JSON.parse(p)),i.dataList=n.selectList})}const Q=e=>{var t;E.title="编辑",V.defaultValue={...e||{}},(t=L.value)==null||t.openDrawer(),i.dataList=(e==null?void 0:e.supplierGoodsList)||[],o.dataList=(e==null?void 0:e.supplierQualificationsList)||[],o.dataList=o.dataList.map(a=>{a.time/=1e3,a.id.length===16&&delete a.id}),B(e),G(e)},Y=e=>{Z("确定删除该供应商, 是否继续?","删除提示").then(()=>{_e([e.id]).then(()=>{b(),m.success("删除成功")}).catch(t=>{console.log(t,"err"),m.error(t.data.message)})}).catch(t=>{m.warning(t)})},R=c("first");function B(e){xe(e.id).then(t=>{i.dataList=t.data.data||[]})}function G(e){Le(e.id).then(t=>{o.dataList=t.data.data||[]})}const n=f({deviceValue:[],total:0,selectList:[],getDevice:e=>{const t={size:r.pagination.limit,page:r.pagination.page,...e};delete t.device,ve(t).then(a=>{n.deviceValue=a.data.data.data||[],n.total=a.data.data.total||0}).catch(a=>{m.warning(a)})}}),b=async()=>{var t;const e={size:r.pagination.limit,page:r.pagination.page,name:"",address:"",status:"",importance:"",...((t=D.value)==null?void 0:t.queryParams)||{}};De(e).then(a=>{r.dataList=a.data.data.data||[],r.pagination.total=a.data.data.total||0}).catch(a=>{m.warning(a)})};return ee(()=>{b()}),(e,t)=>{const a=ye,s=be,u=se,p=re,x=pe,v=ce,T=de,J=ue,$=fe;return te(),ae("div",Ce,[l(a,{ref_key:"refSearch",ref:D,config:d(U)},null,8,["config"]),l(s,{config:d(r),class:"card-table"},null,8,["config"]),l(T,{ref_key:"refForm",ref:L,config:d(E)},{default:y(()=>[ie("div",Re,[l(u,{ref_key:"refForm1",ref:M,config:d(V)},null,8,["config"]),l(v,{modelValue:d(R),"onUpdate:modelValue":t[0]||(t[0]=j=>le(R)?R.value=j:null),type:"card",class:"demo-tabs"},{default:y(()=>[l(x,{label:"资质信息",name:"first"},{default:y(()=>[l(p,{config:d(o)},null,8,["config"])]),_:1}),l(x,{label:"商品信息",name:"second"},{default:y(()=>[l(p,{config:d(i)},null,8,["config"])]),_:1})]),_:1},8,["modelValue"])])]),_:1},8,["config"]),l(T,{ref_key:"imageForm",ref:S,config:d(q)},null,8,["config"]),l(T,{ref_key:"refFormDetail",ref:_,config:d(C)},null,8,["config"]),ne(l($,{ref:"upload",action:"action",accept:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","show-file-list":!1,"on-change":P},{default:y(()=>[l(J,{ref_key:"fileRef",ref:N,size:"small",type:"primary"},{default:y(()=>t[1]||(t[1]=[oe(" 点击上传 ")])),_:1},512)]),_:1},512),[[ge,!1]])])}}});export{Pe as default};
