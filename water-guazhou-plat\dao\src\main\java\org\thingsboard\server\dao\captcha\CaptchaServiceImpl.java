package org.thingsboard.server.dao.captcha;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.CaptchaEntity;
import org.thingsboard.server.dao.sql.captcha.CaptchaDao;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/5/28 14:20
 */

@Service
public class CaptchaServiceImpl implements CaptchaService{


    @Autowired
    private CaptchaDao captchaDao;

    @Override
    public List<CaptchaEntity> getCaptchaByTenant(String tenantId) {
        return captchaDao.getCaptchaByTenant(tenantId);
    }

    @Override
    public List<CaptchaEntity> getCaptchaByPhone(String phone) {
        return captchaDao.getCaptchaByPhone(phone);
    }

    @Override
    public CaptchaEntity saveCaptcha(CaptchaEntity captchaEntity) {
        return captchaDao.saveCaptcha(captchaEntity);
    }

    @Override
    public CaptchaEntity getCaptchaByPhoneAndInvalid(String phone, String invalid) {
        return captchaDao.getCaptchaByPhoneAndInvalid(phone,invalid);
    }

    @Override
    public CaptchaEntity getCaptchaByCaptchaAndInvalid(String captcha, String invalid) {
        return captchaDao.getCaptchaByCaptchaAndValid(captcha, invalid);
    }

    @Override
    public CaptchaEntity getCaptchaByCaptchaAndInvalidAndPhone(String captcha, String invalid, String phone) {
        return captchaDao.getCaptchaByCaptchaAndValidAndPhone(captcha, invalid, phone);
    }
}
