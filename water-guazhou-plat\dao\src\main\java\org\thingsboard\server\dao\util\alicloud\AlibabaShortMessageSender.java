package org.thingsboard.server.dao.util.alicloud;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.aliyun.auth.credentials.Credential;
import com.aliyun.auth.credentials.provider.StaticCredentialProvider;
import com.aliyun.sdk.gateway.pop.exception.PopClientException;
import com.aliyun.sdk.service.dysmsapi20170525.AsyncClient;
import com.aliyun.sdk.service.dysmsapi20170525.models.*;
import com.google.gson.Gson;
import darabonba.core.client.ClientOverrideConfiguration;
import lombok.extern.slf4j.Slf4j;
import org.thingsboard.server.dao.util.reflection.ExceptionUtils;

import java.time.Duration;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CancellationException;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

@Slf4j
public class AlibabaShortMessageSender {

    private String accessKey;

    private String accessKeySecret;

    public AlibabaShortMessageSender(String accessKey, String accessKeySecret) {
        this.accessKey = accessKey;
        this.accessKeySecret = accessKeySecret;
    }

    /**
     * 批量发送不同同一模板不同参数的短信（注：一次最多100条）
     *
     * @param TemplateCode 短信模板的code
     * @param phoneNumbers 电话号码
     * @param signs        签名
     * @param params       模板参数
     * @throws CancellationException 在发送短信时突然被取消(理论上不会抛出这个异常)
     * @throws ExecutionException    发送短信出现错误时
     * @throws InterruptedException  在等待响应时当前线程被打断(interrupt)
     */
    public void sendBatch(String TemplateCode, List<String> phoneNumbers, List<String> signs, List<Map<String, String>> params) throws ExecutionException, InterruptedException {
        if (phoneNumbers.size() > 1000) {
            ExceptionUtils.silentThrow("一次最多允许发送100条短信");
        }

        if (phoneNumbers.size() != signs.size() || signs.size() != params.size()) {
            ExceptionUtils.silentThrow("短信发送参数长度不匹配");
        }


        StaticCredentialProvider provider = StaticCredentialProvider.create(Credential.builder()
                .accessKeyId("accessKey")
                .accessKeySecret("accessKeySecret")
                //.securityToken("token") // use STS token
                .build());

        // Configure the Client
        AsyncClient client = AsyncClient.builder()
                .region("cn-chengdu")
                .credentialsProvider(provider)
                //.serviceConfiguration(Configuration.create()) // Service-level configuration
                // Client-level configuration rewrite, can set Endpoint, Http request parameters, etc.
                .overrideConfiguration(
                        ClientOverrideConfiguration.create()
                                .setEndpointOverride("dysmsapi.aliyuncs.com")
                                .setConnectTimeout(Duration.ofSeconds(30))
                )
                .build();

        // Parameter settings for API request
        SendBatchSmsRequest bathMessageRequest = SendBatchSmsRequest.builder()
                .phoneNumberJson(JSONArray.toJSONString(phoneNumbers))
                .templateCode(TemplateCode)
                .signNameJson(JSONArray.toJSONString(signs))
                .templateParamJson(JSONArray.toJSONString(params))
                .build();


        CompletableFuture<SendBatchSmsResponse> responses = client.sendBatchSms(bathMessageRequest);
        SendBatchSmsResponse batchResponse = responses.get();
        log.info(new Gson().toJson(batchResponse));
        client.close();
    }

    /**
     * 批量发送相同的短信
     *
     * @param TemplateCode  短信模板的code
     * @param phoneNumber   电话号码
     * @param signName      签名
     * @param templateParam 模板参数
     * @throws CancellationException 在发送短信时突然被取消(理论上不会抛出这个异常)
     */
    public String trySend(String TemplateCode, String phoneNumber, String signName, String templateParam) {
        try {
            send(TemplateCode, phoneNumber, signName, templateParam);
            return null;
        } catch (ExecutionException | InterruptedException e) {
            if (log.isTraceEnabled()) {
                log.trace("发送短信失败，TemplateCode:{}，phoneNumber:{}，signName:{}，templateParam:{}，错误信息:{}",
                        TemplateCode, phoneNumber, signName, templateParam, e.getMessage(), e);
            }
            Throwable cause = e.getCause();
            if (cause instanceof PopClientException) {
                return ((PopClientException) cause).getErrCode();
            } else {
                log.error("[严重] 发送短信失败，TemplateCode:{}，phoneNumber:{}，signName:{}，templateParam:{}，错误信息:{}",
                        TemplateCode, phoneNumber, signName, templateParam, e.getMessage(), e);
            }
        }
        return null;
    }

    /**
     * 批量发送相同的短信
     *
     * @param TemplateCode  短信模板的code
     * @param phoneNumber   电话号码
     * @param signName      签名
     * @param templateParam 模板参数
     * @throws CancellationException 在发送短信时突然被取消(理论上不会抛出这个异常)
     * @throws ExecutionException    发送短信出现错误时
     * @throws InterruptedException  在等待响应时当前线程被打断(interrupt)
     */
    public SendSmsResponseBody send(String TemplateCode, String phoneNumber, String signName, Map<String, String> templateParam) throws ExecutionException, InterruptedException {
        return sendBatch(TemplateCode, Collections.singletonList(phoneNumber), signName, JSON.toJSONString(templateParam));
    }

    /**
     * 批量发送相同的短信
     *
     * @param TemplateCode  短信模板的code
     * @param phoneNumber   电话号码
     * @param signName      签名
     * @param templateParam 模板参数
     * @throws CancellationException 在发送短信时突然被取消(理论上不会抛出这个异常)
     * @throws ExecutionException    发送短信出现错误时
     * @throws InterruptedException  在等待响应时当前线程被打断(interrupt)
     */
    public SendSmsResponseBody send(String TemplateCode, String phoneNumber, String signName, String templateParam) throws ExecutionException, InterruptedException {
        return sendBatch(TemplateCode, Collections.singletonList(phoneNumber), signName, templateParam);
    }

    /**
     * 批量发送相同的短信
     *
     * @param TemplateCode  短信模板的code
     * @param phoneNumbers  电话号码
     * @param signName      签名
     * @param templateParam 模板参数
     * @throws CancellationException 在发送短信时突然被取消(理论上不会抛出这个异常)
     * @throws ExecutionException    发送短信出现错误时
     * @throws InterruptedException  在等待响应时当前线程被打断(interrupt)
     */
    public SendSmsResponseBody sendBatch(String TemplateCode, List<String> phoneNumbers, String signName, Map<String, String> templateParam) throws ExecutionException, InterruptedException {
        return sendBatch(TemplateCode, phoneNumbers, signName, JSON.toJSONString(templateParam));
    }

    /**
     * 批量发送相同的短信
     *
     * @param TemplateCode 短信模板的code
     * @param phoneNumbers 电话号码
     * @param signName     签名
     * @param variables    模板参数JSON
     * @throws CancellationException 在发送短信时突然被取消(理论上不会抛出这个异常)
     * @throws ExecutionException    发送短信出现错误时
     * @throws InterruptedException  在等待响应时当前线程被打断(interrupt)
     */
    public SendSmsResponseBody sendBatch(String TemplateCode, List<String> phoneNumbers, String signName, String variables) throws ExecutionException, InterruptedException {
        if (phoneNumbers.size() > 1000) {
            throw new RuntimeException("一次最多允许发送1000条短信");
        }


        StaticCredentialProvider provider = StaticCredentialProvider.create(Credential.builder()
                .accessKeyId(accessKey)
                .accessKeySecret(accessKeySecret)
                //.securityToken("token") // use STS token
                .build());

        // Configure the Client
        AsyncClient client = AsyncClient.builder()
                .region("cn-chengdu")
                .credentialsProvider(provider)
                //.serviceConfiguration(Configuration.create()) // Service-level configuration
                // Client-level configuration rewrite, can set Endpoint, Http request parameters, etc.
                .overrideConfiguration(
                        ClientOverrideConfiguration.create()
                                .setEndpointOverride("dysmsapi.aliyuncs.com")
                                .setConnectTimeout(Duration.ofSeconds(30))
                )
                .build();

        // Parameter settings for API request
        SendSmsRequest messageRequest = SendSmsRequest.builder()
                .phoneNumbers(String.join(",", phoneNumbers))
                .templateCode(TemplateCode)
                .signName(signName)
                .templateParam(variables)
                .build();


        CompletableFuture<SendSmsResponse> response = client.sendSms(messageRequest);
        SendSmsResponse batchResponse = response.get();
        log.info(new Gson().toJson(batchResponse));
        client.close();

        return batchResponse.getBody();
    }
}
