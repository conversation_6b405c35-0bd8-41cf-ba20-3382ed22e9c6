import request from '@/plugins/axios'

// 水质分析数据查询参数接口
export interface WaterQualityAnalysisParams {
  pageNum: number
  pageSize: number
  treatmentLevel?: string
  community?: string
  dateRange?: string[]
  month?: string
}

// 水质分析数据项接口
export interface WaterQualityAnalysisItem {
  id?: string
  sampleTime: string
  sampleLocation: string
  weather: string
  outletCod: number
  outletBod: number
  suspendedSolids: number
  ammoniaNitrogen: number
  totalNitrogen: number
  totalPhosphorus: number
  outletPh: number
  fecalColiform: number
  createTime?: string
  updateTime?: string
}

// 水质分析数据列表响应接口
export interface WaterQualityAnalysisResponse {
  list: WaterQualityAnalysisItem[]
  total: number
}

/**
 * 获取水质分析数据列表
 * @param params 查询参数
 * @returns 
 */
export function getWaterQualityAnalysisData(params: WaterQualityAnalysisParams) {
  return request({
    url: '/api/sewagePlant/waterQualityAnalysis/list',
    method: 'get',
    params
  })
}

/**
 * 导出水质分析数据
 * @param params 查询参数
 * @returns 
 */
export function exportWaterQualityAnalysisData(params: WaterQualityAnalysisParams) {
  return request({
    url: '/api/sewagePlant/waterQualityAnalysis/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

/**
 * 获取水质分析统计数据
 * @param params 查询参数
 * @returns 
 */
export function getWaterQualityStatistics(params: WaterQualityAnalysisParams) {
  return request({
    url: '/api/sewagePlant/waterQualityAnalysis/statistics',
    method: 'get',
    params
  })
}

/**
 * 获取水质分析对比数据
 * @param params 查询参数
 * @returns 
 */
export function getWaterQualityCompareData(params: WaterQualityAnalysisParams & {
  compareType: 'month' | 'year' | 'custom'
}) {
  return request({
    url: '/api/sewagePlant/waterQualityAnalysis/compare',
    method: 'get',
    params
  })
}
