package org.thingsboard.server.dao.smartProduction.safeProduction;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.request.SafeProductionRequest;
import org.thingsboard.server.dao.model.sql.smartProduction.safeProduction.ProductionHiddenDander;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrder;
import org.thingsboard.server.dao.orderWork.NewlyWorkOrderService;
import org.thingsboard.server.dao.sql.smartProduction.safeProduction.ProductionHiddenDangerMapper;
import org.thingsboard.server.dao.util.RedisUtil;
import org.thingsboard.server.dao.util.imodel.query.workOrder.PipeWorkOrderSaveRequest;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class ProductionHiddenDangerServiceImpl implements ProductionHiddenDangerService {

    @Autowired
    private ProductionHiddenDangerMapper productionHiddenDangerMapper;

    @Autowired
    private NewlyWorkOrderService newlyWorkOrderService;

    @Override
    public ProductionHiddenDander save(ProductionHiddenDander productionHiddenDander) {
        if (StringUtils.isBlank(productionHiddenDander.getId())) {
            productionHiddenDander.setCode(RedisUtil.nextId(DataConstants.REDIS_KEY.PRODUCTION_HIDDEN_DANGER, ""));
            productionHiddenDander.setCreateTime(new Date());
            productionHiddenDander.setStatus("0");
            productionHiddenDangerMapper.insert(productionHiddenDander);
        } else {
            if ("1".equals(productionHiddenDander.getStatus()) || "2".equals(productionHiddenDander.getStatus())) {
                productionHiddenDander.setReviewTime(new Date());
            }
            productionHiddenDander.setCreator(null);
            productionHiddenDangerMapper.updateById(productionHiddenDander);
        }

        return productionHiddenDander;
    }

    @Override
    public PageData<ProductionHiddenDander> findList(SafeProductionRequest request) {
        IPage<ProductionHiddenDander> ipage = new Page<>(request.getPage(), request.getSize());
        ipage = productionHiddenDangerMapper.findList(ipage, request);
        return new PageData<>(ipage.getTotal(), ipage.getRecords());
    }

    @Override
    public void delete(List<String> ids) {
        productionHiddenDangerMapper.deleteBatchIds(ids);
    }

    @Override
    public IstarResponse toWorkOrder(PipeWorkOrderSaveRequest pipeWorkOrderSaveRequest, String userId) {
        ProductionHiddenDander productionHiddenDander = productionHiddenDangerMapper.selectById(pipeWorkOrderSaveRequest.getDangerId());
        if (productionHiddenDander == null) {
            return IstarResponse.error("该隐患不存在");
        }

        pipeWorkOrderSaveRequest.setSource("安全隐患");
        WorkOrder workOrder = newlyWorkOrderService.save(pipeWorkOrderSaveRequest);
        productionHiddenDander.setProcessUser(userId);
        productionHiddenDander.setProcessTime(new Date());
        productionHiddenDander.setProcessRemark("转工单");
        productionHiddenDander.setWorkOrderId(workOrder.getId());
        productionHiddenDander.setStatus("3");

        productionHiddenDangerMapper.updateById(productionHiddenDander);
        return IstarResponse.ok();
    }
}

