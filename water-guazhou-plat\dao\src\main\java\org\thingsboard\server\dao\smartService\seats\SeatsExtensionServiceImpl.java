package org.thingsboard.server.dao.smartService.seats;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.dao.model.sql.smartService.seats.SeatsExtension;
import org.thingsboard.server.dao.model.sql.smartService.seats.SeatsUser;
import org.thingsboard.server.dao.sql.smartService.seats.SeatsExtensionMapper;
import org.thingsboard.server.dao.sql.smartService.seats.SeatsUserMapper;

import java.util.Date;
import java.util.List;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-10-27
 */
@Slf4j
@Service
@Transactional
public class SeatsExtensionServiceImpl implements SeatsExtensionService {
    @Autowired
    private SeatsExtensionMapper seatsExtensionMapper;
    @Autowired
    private SeatsUserMapper seatsUserMapper;

    @Override
    public List getList(String keywords, String tenantId) {
        List<SeatsExtension> extensionList = seatsExtensionMapper.getList(keywords, tenantId);

        return extensionList;

    }

    @Override
    public SeatsExtension save(SeatsExtension seatsExtension) {
        seatsExtension.setUpdateTime(new Date());
        if (StringUtils.isBlank(seatsExtension.getId())) {
            seatsExtension.setCreateTime(new Date());
            seatsExtensionMapper.insert(seatsExtension);
        } else {
            seatsExtensionMapper.updateById(seatsExtension);
        }

        return seatsExtension;
    }

    @Override
    public String delete(List<String> ids) {
        // 是否有分机被绑定
        QueryWrapper<SeatsUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("extension_id", ids);
        List list = seatsUserMapper.selectList(queryWrapper);
        if (list.size() > 0) {
            return "有分机被绑定，请先解绑后删除";
        }
        seatsExtensionMapper.deleteBatchIds(ids);
        return "";
    }

    @Override
    public boolean check(SeatsExtension seatsExtension) {
        QueryWrapper<SeatsExtension> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("extension", seatsExtension.getExtension());
        List<SeatsExtension> list = seatsExtensionMapper.selectList(queryWrapper);
        for (SeatsExtension seatsExtension1 : list) {
            if (!seatsExtension1.getId().equals(seatsExtension.getId())) {
                return false;
            }
        }
        return true;
    }

    @Override
    public List getNotBindList(String tenantId) {
        List<SeatsExtension> extensionList = seatsExtensionMapper.getNotBindList(tenantId);

        return extensionList;
    }

    @Override
    public SeatsExtension getById(String extensionId) {
        return seatsExtensionMapper.selectById(extensionId);
    }
}
