package org.thingsboard.server.dao.orderWork;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.OrderFollowEntity;
import org.thingsboard.server.dao.sql.workOrder.OrderFollowRepository;

import java.util.List;

@Slf4j
@Service
@Deprecated
public class OrderFollowServiceImpl implements OrderFollowService {

    @Autowired
    private OrderFollowRepository orderFollowRepository;

    @Override
    public List<OrderFollowEntity> findByUserId(String userId) {
        return orderFollowRepository.findByUserId(userId);
    }

    @Override
    public OrderFollowEntity findByUserIdAndOrderId(String userId, String orderId) {
        return orderFollowRepository.findByUserIdAndOrderId(userId, orderId);
    }

    @Override
    public void save(OrderFollowEntity follow) {
        orderFollowRepository.save(follow);
    }

    @Override
    public void deleteById(String id) {
        orderFollowRepository.delete(id);
    }
}
