package org.thingsboard.server.dao.base.impl;

import java.util.List;
import java.util.UUID;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.base.IBaseTemplateTypeService;
import org.thingsboard.server.dao.model.sql.base.BaseTemplateType;
import org.thingsboard.server.dao.sql.base.BaseTemplateTypeMapper;
import org.thingsboard.server.dao.util.imodel.query.base.BaseTemplateTypePageRequest;

/**
 * 平台管理-模型类型Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
@Service
public class BaseTemplateTypeServiceImpl implements IBaseTemplateTypeService {

    @Autowired
    private BaseTemplateTypeMapper baseTemplateTypeMapper;

    /**
     * 查询平台管理-模型类型
     *
     * @param id 平台管理-模型类型主键
     * @return 平台管理-模型类型
     */
    @Override
    public BaseTemplateType selectBaseTemplateTypeById(String id) {
        return baseTemplateTypeMapper.selectBaseTemplateTypeById(id);
    }

    /**
     * 查询平台管理-模型类型列表
     *
     * @param baseTemplateType 平台管理-模型类型
     * @return 平台管理-模型类型
     */
    @Override
    public IPage<BaseTemplateType> selectBaseTemplateTypeList(BaseTemplateTypePageRequest baseTemplateType) {
        return baseTemplateTypeMapper.selectBaseTemplateTypeList(baseTemplateType);
    }

    /**
     * 新增平台管理-模型类型
     *
     * @param baseTemplateType 平台管理-模型类型
     * @return 结果
     */
    @Override
    public int insertBaseTemplateType(BaseTemplateType baseTemplateType) {
        baseTemplateType.setId(UUID.randomUUID().toString().replace("-", ""));
        return baseTemplateTypeMapper.insertBaseTemplateType(baseTemplateType);
    }

    /**
     * 修改平台管理-模型类型
     *
     * @param baseTemplateType 平台管理-模型类型
     * @return 结果
     */
    @Override
    public int updateBaseTemplateType(BaseTemplateType baseTemplateType) {
        return baseTemplateTypeMapper.updateBaseTemplateType(baseTemplateType);
    }

    /**
     * 批量删除平台管理-模型类型
     *
     * @param ids 需要删除的平台管理-模型类型主键
     * @return 结果
     */
    @Override
    public int deleteBaseTemplateTypeByIds(List<String> ids) {
        return baseTemplateTypeMapper.deleteBaseTemplateTypeByIds(ids);
    }

    /**
     * 删除平台管理-模型类型信息
     *
     * @param id 平台管理-模型类型主键
     * @return 结果
     */
    @Override
    public int deleteBaseTemplateTypeById(String id) {
        return baseTemplateTypeMapper.deleteBaseTemplateTypeById(id);
    }
}
