<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.smartPipe.PartitionMountMapper">

    <select id="getList" resultType="org.thingsboard.server.dao.model.sql.smartPipe.dma.PartitionMount">
        select a.*, b.name as name
        from tb_pipe_partition_mount a
        left join device b on a.device_id = b.id
        <where>
            <if test="param.partitionId != null and param.partitionId != ''">
                and a.partition_id = #{param.partitionId}
            </if>
            <if test="param.name != null and param.name != ''">
                and b.name like '%' || #{param.name} || '%'
            </if>
            <if test="param.type != null and param.type != ''">
                and a.type = #{param.type}
            </if>
            and a.tenant_id = #{param.tenantId}
            order by a.create_time desc
        </where>

    </select>
    <select id="getAllByTenantId" resultType="org.thingsboard.server.dao.model.sql.smartPipe.dma.PartitionMount">
        select mount.*
        from tb_pipe_partition_mount mount
        left join tb_pipe_partition partition on mount.partition_id = partition.id
        where partition.pid is not null and partition.type = '1' and mount.tenant_id = #{tenantId}
        <if test="direction != null">
            <if test="direction == 'in'.toString()">
                and mount.direction != '4'
            </if>
        </if>
    </select>
    <select id="getAllByPartitionId" resultType="org.thingsboard.server.dao.model.sql.smartPipe.dma.PartitionMount">
        select mount.*, d.name
        from tb_pipe_partition_mount mount
        left join tb_pipe_partition partition on mount.partition_id = partition.id
        left join device d on mount.device_id = d.id
        where mount.tenant_id = #{tenantId}
          <if test="partitionIdList != null and partitionIdList.size() > 0">
              and mount.partition_id in
              <foreach collection="partitionIdList" open="(" separator="," close=")" item="item">
                  #{item}
              </foreach>
          </if>
        <if test="direction != null">
            <if test="direction == 'in'.toString()">
                and mount.direction != '4'
            </if>
        </if>
        <if test="mountType != null and mountType != ''">
            and mount.type = #{mountType}
        </if>
    </select>
</mapper>