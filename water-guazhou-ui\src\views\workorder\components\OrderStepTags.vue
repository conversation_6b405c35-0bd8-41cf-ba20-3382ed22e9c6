<template>
  <div class="step-wrapper">
    <div
      v-for="(step, index) in state.OrderStatus"
      :key="index"
      class="step-item"
    >
      <Button
        :config="step"
        :type="initType(step, index)"
      />
      <el-icon
        v-if="index !== state.OrderStatus.length - 1"
        style="width: 40px; height: 100%; font-size: 24px"
      >
        <Icon
          icon="ep:more"
          :style="{
            color: initType(step, index) === 'primary' ? '#79bbff' : '#b1b3b8'
          }"
        ></Icon>
      </el-icon>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { Icon } from '@iconify/vue'
import { onMounted, reactive } from 'vue'
import { WorkOrderStep } from '../config'

const props = defineProps<{
  config: {
    status: string
    statusName: string
  }
}>()
const state = reactive<{
  OrderStatus: IButton[]
  CurStatus?: NormalOption
}>({
  OrderStatus: [],
  CurStatus: undefined
})
const initList = () => {
  let statusOptions = WorkOrderStep()
  state.CurStatus = statusOptions.find(
    item => item.value === props.config.status
  )
  if (state.CurStatus?.value === 'REJECTED') {
    statusOptions = statusOptions.filter(item => item.value !== 'APPROVED')
  } else {
    statusOptions = statusOptions.filter(item => item.value !== 'REJECTED')
  }
  const curIndex = statusOptions.findIndex(
    item => item.value === props.config.status
  )
  if (curIndex === -1) {
    state.OrderStatus = [
      { perm: true, text: props.config.statusName, size: 'small' }
    ]
  } else {
    state.OrderStatus = statusOptions.map(item => {
      return {
        perm: true,
        text: item.label,
        size: 'small'
      }
    })
  }
}
onMounted(() => {
  initList()
})
const initType = (button: IButton, index: number): IButtonType => {
  const dealingIndex = state.OrderStatus.findIndex(
    item => item.text === state.CurStatus?.label
  )
  if (button.text === state.CurStatus?.label) {
    return 'success'
  }
  if (index <= dealingIndex) {
    return 'primary'
  }
  return 'info'
}
</script>
<style lang="scss" scoped>
.step-wrapper {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  background-color: var(--el-fill-color-extra-light);
  margin: -8px 0;
  padding: 8px 20px;
  .step-item {
    display: flex;
    align-items: center;
  }
}
</style>
