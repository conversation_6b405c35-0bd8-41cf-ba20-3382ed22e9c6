package org.thingsboard.server.dao.smartService.portal;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.smartService.portal.SsPortalPolicyContent;
import org.thingsboard.server.dao.util.imodel.query.smartService.portal.SsPortalPolicyContentPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartService.portal.SsPortalPolicyContentSaveRequest;

public interface SsPortalPolicyContentService {
    SsPortalPolicyContent findById(String id);

    IPage<SsPortalPolicyContent> findAllConditional(SsPortalPolicyContentPageRequest request);

    SsPortalPolicyContent save(SsPortalPolicyContentSaveRequest entity);

    boolean update(SsPortalPolicyContent entity);

    boolean delete(String id);

}
