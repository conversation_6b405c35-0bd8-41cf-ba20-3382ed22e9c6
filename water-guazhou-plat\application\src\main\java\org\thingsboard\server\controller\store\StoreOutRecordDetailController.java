package org.thingsboard.server.controller.store;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.sql.store.StoreOutRecordDetail;
import org.thingsboard.server.dao.model.sql.store.StoreOutRecordDetailResponse;
import org.thingsboard.server.dao.util.imodel.query.store.StoreOutRecordDetailPageRequest;
import org.thingsboard.server.dao.util.imodel.query.store.StoreOutRecordDetailSaveRequest;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;
import org.thingsboard.server.dao.store.StoreOutRecordDetailService;
import org.thingsboard.server.utils.imodel.annotations.IStarController;

import java.util.List;

@IStarController
@RequestMapping("/api/StoreOutRecordDetail")
public class StoreOutRecordDetailController extends BaseController {
    @Autowired
    private StoreOutRecordDetailService service;

    @GetMapping
    public IPage<StoreOutRecordDetailResponse> findAllConditional(StoreOutRecordDetailPageRequest request) {
        return service.findAllConditional(request);
    }

    @PostMapping
    public List<StoreOutRecordDetail> save(@RequestBody List<StoreOutRecordDetailSaveRequest> req) throws ThingsboardException {
        return service.saveAll(req);
    }

    // @PatchMapping("/{id}")
    public boolean edit(@RequestBody StoreOutRecordDetailSaveRequest req, @PathVariable String id) {
        return service.update(req.update(id));
    }

    // @DeleteMapping("/{id}")
    public IstarResponse delete(@PathVariable String id) {
        return IstarResponse.ok(service.delete(id));
    }

}