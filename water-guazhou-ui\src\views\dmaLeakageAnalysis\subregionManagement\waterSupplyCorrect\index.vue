<!-- 供水量修正 -->
<template>
  <TreeBox>
    <template #tree>
      <SLTree
        ref="refTree"
        :tree-data="TreeData"
      ></SLTree>
    </template>
    <CardSearch
      ref="refSearch"
      :config="SearchConfig"
    ></CardSearch>
    <CardTable
      :config="TableConfig"
      class="card-table"
    ></CardTable>
    <RecordList ref="refRecord"></RecordList>
  </TreeBox>
</template>
<script lang="ts" setup>
import {
  CorrectDMASupplyTotalFlow,
  GetDMASupplyTotalFlowList,
  ExportDMASupplyTotalFlowList
} from '@/api/mapservice/dma'
import { ICardSearchIns } from '@/components/type'
import { usePartition } from '@/hooks/arcgis'
import { formatterDate, formatterMonth } from '@/utils/GlobalHelper'
import { SLMessage } from '@/utils/Message'
import RecordList from './components/RecordList.vue'
import { saveAs } from '@/utils/printUtils'

const refRecord = ref<InstanceType<typeof RecordList>>()
const refSearch = ref<ICardSearchIns>()
// 分区树配置
const TreeData = reactive<SLTreeConfig>({
  data: [],
  loading: true,
  title: '选择分区',
  expandOnClickNode: false,
  defaultExpandAll: true,
  treeNodeHandleClick: async (data: NormalOption) => {
    if (TreeData.currentProject !== data) {
      // TreeData.loading = true
      TreeData.currentProject = data
      await refreshData()
    }
  }
})
// 查询条件配置
const SearchConfig = reactive<ISearch>({
  defaultParams: {
    type: '1',
    date: [moment().subtract(1, 'M').format(), moment().format()],
    month: moment().format(formatterMonth)
  },
  filters: [
    {
      type: 'radio-button',
      field: 'type',
      width: 90,
      clearable: false,
      options: [
        { label: '按时', value: '1' },
        { label: '按日', value: '2' },
        { label: '按月', value: '3' }
      ],
      label: '选择方式',
      placeholder: '请选择'
    },
    {
      type: 'daterange',
      field: 'date',
      clearable: false,
      handleHidden: (params: any, query: any, formItem: IFormItem) => {
        formItem.hidden = params.type === '3'
      }
    },
    {
      type: 'month',
      field: 'month',
      clearable: false,
      handleHidden: (params: any, query: any, formItem: IFormItem) => {
        formItem.hidden = params.type !== '3'
      }
    },
    {
      type: 'input',
      label: '水表名称',
      field: 'meterName'
    },
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          iconifyIcon: 'ep:search',
          click: () => refreshData()
        },
        {
          perm: true,
          text: '重置',
          type: 'default',
          iconifyIcon: 'ep:refresh',
          click: () => {
            refSearch.value?.resetForm()
          }
        },
        {
          perm: true,
          text: '导出',
          type: 'primary',
          iconifyIcon: 'ep:download',
          click: () => {
            // exportTable()
            refreshData(true)
          }
        },
        {
          perm: true,
          text: '修正记录',
          type: 'default',
          iconifyIcon: 'mdi:eye',
          click: () => handleRecord()
        }
      ]
    }
  ]
})
// 数据列表配置
const TableConfig = reactive<ICardTable>({
  indexVisible: true,
  dataList: [],
  columns: [
    { label: '分区名称', minWidth: 120, prop: 'partitionName' },
    { label: '水表名称', minWidth: 120, prop: 'deviceName' },
    { label: 'RTU编号', minWidth: 120, prop: 'deviceId' },
    {
      label: '日期',
      minWidth: 120,
      prop: 'collectTimeStr'
    },
    { label: '供水量', minWidth: 120, prop: 'value' },
    {
      label: '追加水量',
      minWidth: 120,
      prop: 'correctWater'
    }
    // { label: '数据来源', minWidth: 120, prop: 'require' }
  ],
  operationWidth: 200,
  operations: [
    {
      perm: true,
      text: '编辑',
      isTextBtn: false,
      iconifyIcon: 'ep:edit',
      click: row => editRow(row)
    },
    {
      perm: true,
      text: '保存',
      loading: row => row.isLoading === true,
      isTextBtn: false,
      iconifyIcon: 'ep:success-filled',
      click: row => saveRow(row)
    }
  ],
  pagination: {
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page
      TableConfig.pagination.limit = size
      refreshData()
    }
  }
})
const handleRecord = () => {
  refRecord.value?.refRecord?.openDialog()
}
const editRow = (row: any) => {
  row.correctWaterFormItemConfig = {
    type: 'input-number'
  }
}
// 保存当条数据
const saveRow = async (row: any) => {
  row.isLoading = true
  try {
    const res = await CorrectDMASupplyTotalFlow({
      id: row.id,
      correctWater: row.correctWaterF
    })
    if (res.data.code === 200) {
      SLMessage.success('保存成功')
      row.correctWaterFormItemConfig = undefined
    } else {
      SLMessage.error(res.data.message)
    }
  } catch (error) {
    SLMessage.error('保存失败')
  }
  row.isLoading = false
}

// 获取配置数据列表
const refreshData = async (isExport?: boolean) => {
  const query = refSearch.value?.queryParams || {}

  const params: any = {
    page: TableConfig.pagination.page || 1,
    size: TableConfig.pagination.limit || 20,
    type: query.type,
    partitionId: TreeData.currentProject?.value
  }
  try {
    if (query.type === '3' && query.month) {
      params.month = query.month
    } else if (query.type === '1' || (query.type === '2' && query.date)) {
      params.start = moment(query.date[0], formatterDate).startOf('D').valueOf()
      params.end = moment(query.date[1], formatterDate).endOf('D').valueOf()
    }
    if (isExport) {
      const res = await ExportDMASupplyTotalFlowList(params)
      saveAs(res.data, '供水量')
    } else {
      const res = await GetDMASupplyTotalFlowList(params)
      TableConfig.dataList = res.data?.data?.data || []
      TableConfig.pagination.total = res.data?.data?.total || 0
    }
  } catch (error) {
    //
  }
}
const refreshTree = async () => {
  await partition.getTree()
  TreeData.data = partition.Tree.value
  TreeData.currentProject = TreeData.data?.[0]
  refreshData()
}
const partition = usePartition()
onMounted(async () => {
  refreshTree()
})
</script>
<style lang="scss" scoped>
.table-box {
  height: calc(100% - 100px);
  width: 100%;
}
</style>
