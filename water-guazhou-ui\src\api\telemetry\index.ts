import request from '@/plugins/axios';

export function getAttributes(entityType, entityId) {
  if (Array.isArray(entityType)) {
    const type = entityType[0];
    const Id = entityType[1];
    const url = `/plugins/telemetry/${type}/${Id}/values/attributes`;
    return request({
      url,
      method: 'get'
    });
  }
  return request({
    url: `/api/plugins/telemetry/${entityType}/${entityId}/values/attributes`,
    method: 'get'
  });
}
/** save asset formula:  */
export function saveEntityAttributesV1(entityType, entityId, params) {
  return request({
    url: `/api/plugins/telemetry/${entityType}/${entityId}/SERVER_SCOPE`,
    method: 'post',
    data: params
  });
}
