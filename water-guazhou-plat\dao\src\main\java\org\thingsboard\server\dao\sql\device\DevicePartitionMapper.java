/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.sql.device;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.thingsboard.server.dao.model.DTO.DevicePartitionDTO;
import org.thingsboard.server.dao.model.sql.DeviceEntity;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 5/6/2017.
 */
@Mapper
public interface DevicePartitionMapper extends BaseMapper<DeviceEntity> {

    @Select(value = "SELECT id, name, device_type_name as deviceTypeName, create_time as createTime, tenant_id as tenantId " +
            "FROM device d " +
            "WHERE d.is_delete != '1' AND d.device_type_name like #{deviceTypeName}  AND d.name like #{name} AND d.tenant_id = #{tenantId} " +
            "AND d.id not in (select b.device_id from tb_pipe_partition_mount b where b.type = #{type} and b.partition_id = #{partitionId} and b.tenant_id = #{tenantId}) " +
            "ORDER BY d.create_time ASC offset (#{page} * #{size}) limit #{size}")
    List<DevicePartitionDTO> getPartitionDevice(@Param("deviceTypeName") String deviceTypeName, @Param("name") String name, @Param("tenantId") String tenantId, @Param("type") String type, @Param("page") int page, @Param("size") int size, @Param("partitionId") String partitionId);

    @Select(value = "SELECT count(*) " +
            "FROM device d " +
            "WHERE d.is_delete != '1' AND d.device_type_name like #{deviceTypeName}  AND d.name like #{name} AND d.tenant_id = #{tenantId} " +
            "AND d.id not in (select b.device_id from tb_pipe_partition_mount b where b.type = #{type} and b.partition_id = #{partitionId} and b.tenant_id = #{tenantId}) ")
    int getPartitionDeviceCount(@Param("deviceTypeName") String deviceTypeName, @Param("name") String name, @Param("tenantId") String tenantId, @Param("type") String type, @Param("partitionId") String partitionId);

    @Select(value = "SELECT id, name, device_type_name as deviceTypeName, create_time as createTime, tenant_id as tenantId " +
            "FROM device d " +
            "WHERE d.is_delete != '1' AND d.device_type_name like #{deviceTypeName}  AND d.name like #{name} AND d.tenant_id = #{tenantId} " +
            "AND d.id not in (select b.device_id from tb_pipe_water_factory_monitor_point b where b.tenant_id = #{tenantId}) " +
            "ORDER BY d.create_time ASC offset (#{page} * #{size}) limit #{size}")
    List<DevicePartitionDTO> getWaterFactoryDevice(@Param("deviceTypeName") String deviceTypeName, @Param("name") String name, @Param("tenantId") String tenantId, @Param("page") int page, @Param("size") int size);

    @Select(value = "SELECT count(*) " +
            "FROM device d " +
            "WHERE d.is_delete != '1' AND d.device_type_name like #{deviceTypeName}  AND d.name like #{name} AND d.tenant_id = #{tenantId} " +
            "AND d.id not in (select b.device_id from tb_pipe_water_factory_monitor_point b where b.tenant_id = #{tenantId}) ")
    int getWaterFactoryDeviceCount(@Param("deviceTypeName") String deviceTypeName, @Param("name") String name, @Param("tenantId") String tenantId);

}
