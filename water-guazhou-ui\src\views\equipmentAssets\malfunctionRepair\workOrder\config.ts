import { Clock } from '@element-plus/icons-vue';
import { useUserStore } from '@/store';
import { formatDate } from '@/utils/DateFormatter';
// import { formatTelNum } from '@/utils/GlobalHelper'

const Users: { id: string; name: string; tel: string; address: string }[] = [
  {
    id: 'U20220103001',
    name: useUserStore().user?.name || '张华',
    tel: '18854022223',
    address: '西街可行大饭店'
  },
  {
    id: 'U20220103001',
    name: '李珊珊',
    tel: '13789076543',
    address: '金沙街1807号5栋'
  },
  {
    id: 'U20220103001',
    name: '王梓兰',
    tel: '18998787874',
    address: '同心园小区15号楼'
  },
  {
    id: 'U20220103001',
    name: '何少峰',
    tel: '13243533343',
    address: '财富双楠写字楼1701'
  },
  {
    id: 'U20220103001',
    name: '胡国华',
    tel: '13897009090',
    address: '东坡体育管'
  },
  {
    id: 'U20220103001',
    name: '何向钱',
    tel: '13390933333',
    address: '锦翠南苑10㠉'
  },
  {
    id: 'U20220103001',
    name: '李萱',
    tel: '18490800099',
    address: '维港西区丽都帝景北区'
  },
  {
    id: 'U20220103001',
    name: '赵国江',
    tel: '18190871452',
    address: '南新逸苑2号楼'
  },
  {
    id: 'U20220103001',
    name: '赵四海',
    tel: '18408080088',
    address: '怡馨家园25号楼'
  },
  {
    id: 'U20220103001',
    name: '任进',
    tel: '18408282021',
    address: '金地天府城12栋'
  },
  {
    id: 'U20220103001',
    name: '陈程',
    tel: '13789345111',
    address: '古城小区南苑北区12栋'
  },
  {
    id: 'U20220103001',
    name: '张熙西',
    tel: '13890985432',
    address: '华阳大道1895号A座'
  },
  {
    id: 'U20220103001',
    name: '孙耀杨',
    tel: '18888002211',
    address: '华阳大道1895号A座'
  },
  {
    id: 'U20220103001',
    name: '李进国',
    tel: '13809014321',
    address: '华阳大道1895号A座'
  },
  {
    id: 'U20220103001',
    name: '康少韩',
    tel: '13389072222',
    address: '麓山印象35号楼3单元'
  },
  {
    id: 'U20220103001',
    name: '毛豆豆',
    tel: '13390909999',
    address: '安镇幸福院'
  },
  {
    id: 'U20220103001',
    name: '刘国强',
    tel: '19981098901',
    address: '云顶路正兴小学'
  }
];
export const UserOptions = () =>
  Users.map((item) => ({ label: item.name, value: item.name }));
export const initOrderTableColumns = (): IFormTableColumn[] => [
  { minWidth: 180, prop: 'serialNo', label: '工单编号' },
  { minWidth: 100, prop: 'title', label: '标题' },
  { minWidth: 120, prop: 'source', label: '来源' },
  { minWidth: 120, prop: 'type', label: '类型' },
  // { minWidth: 120, prop: 'createDepart', label: '创建部门' },
  {
    minWidth: 120,
    prop: 'level',
    label: '紧急程度',
    svgIcon: shallowRef(Clock),
    cellStyle: (row) => {
      return {
        color: formatEmergencyLevelColor(row.level)
      };
    }
  },
  // { minWidth: 120, prop: 'statusName', label: '当前状态' },
  { minWidth: 100, prop: 'organizerName', label: '发起人' },
  {
    minWidth: 160,
    prop: 'createTime',
    label: '发起时间',
    formatter: (row) => formatDate(row.createTime)
  },
  // { minWidth: 180, prop: 'uploadUserId', label: '上报人' },
  // { minWidth: 120, prop: 'uploadPhone', label: '上报人电话', formatter: row => formatTelNum(row.uploadPhone) },

  // { minWidth: 200, prop: 'uploadAddress', label: '上报人地址' },
  // { minWidth: 120, prop: 'dispatchDepart', label: '分派部门' },
  // { minWidth: 120, prop: 'organizerId', label: '分派人' },
  // { minWidth: 160, prop: 'dispatchTime', label: '分派时间', formatter: row => formatDate(row.dispatchTime) },
  { minWidth: 120, prop: 'processUserName', label: '处理人' },
  // { minWidth: 120, prop: 'dealDepart', label: '处理部门' },
  {
    minWidth: 120,
    prop: 'processLevel',
    label: '处理级别',
    formatter: (row) => formatWorkOrderDealLevel(row.processLevel)
  },
  {
    minWidth: 160,
    prop: 'estimatedFinishTime',
    label: '预计完成时间',
    formatter: (row) => formatDate(row.estimatedFinishTime)
  },
  {
    minWidth: 160,
    prop: 'completeTime',
    label: '完成时间',
    formatter: (row) => formatDate(row.completeTime)
  },
  {
    minWidth: 160,
    prop: 'updateTime',
    label: '最后更新时间',
    formatter: (row) => formatDate(row.updateTime)
  }
  // { minWidth: 120, prop: 'desc', label: '摘要' },
  // { minWidth: 120, prop: 'outerCode', label: '外部单号' },
];

export const initSteps = (): IButton[] => [
  { perm: true, text: '发起', type: 'primary' },
  { perm: true, text: '分派', type: 'primary' },
  { perm: true, text: '接收', type: 'primary' },
  { perm: true, text: '到场', type: 'primary' },
  { perm: true, text: '处理', type: 'primary' },
  { perm: true, text: '完成', type: 'primary' },
  { perm: true, text: '审核通过', type: 'primary' }
];
const FromOption = [
  { value: '电话上报', label: '电话上报' },
  { value: '客服热线', label: '客服热线' },
  { value: '巡检养护', label: '巡检养护' },
  { value: '计划上报', label: '计划上报' },
  { value: '异常监测', label: '异常监测' },
  { value: '漏损控制', label: '漏损控制' },
  { value: '设备资产', label: '设备资产' },
  { value: '工单协作', label: '工单协作' },
  { value: '感知预警', label: '感知预警' }
];
export const getFromOptions = (): NormalOption[] => FromOption;
const EmergencyLevelOpetions = [
  { value: '紧急', label: '紧急' },
  { value: '非常紧急', label: '非常紧急' },
  { value: '一般', label: '一般' }
];
export const getEmergencyLevelOpetions = (): NormalOption[] =>
  EmergencyLevelOpetions;
export const formatEmergencyLevelColor = (val: string) => {
  return val === '紧急' ? 'orange' : val === '非常紧急' ? 'red' : 'green';
};
const OrderTypeOptions = [
  {
    value: '当场答复',
    label: '当场答复',
    children: [
      { value: '咨询', label: '咨询' },
      { value: '其他', label: '其他' }
    ]
  },
  {
    value: '水压问题',
    label: '水压问题',
    children: [{ value: '水压偏低', label: '水压偏低' }]
  },
  {
    value: '水管破裂',
    label: '水管破裂',
    children: [
      { value: '屋外', label: '屋外' },
      { value: '表井', label: '表井' },
      { value: '管线', label: '管线' }
    ]
  },
  {
    value: '水质问题',
    label: '水质问题',
    children: [
      { value: '水质异味', label: '水质异味' },
      { value: '水质变色', label: '水质变色' },
      { value: '水质浑浊', label: '水质浑浊' }
    ]
  },
  {
    value: '设备故障',
    label: '设备故障',
    children: [{ value: '设备故障', label: '设备故障' }]
  },
  {
    value: '停水',
    label: '停水',
    children: [{ value: '停水', label: '停水' }]
  },
  { value: '其他', label: '其他', children: [{ value: '其他', label: '其他' }] }
];
export const getOrderTypeOptions = (): NormalOption[] => OrderTypeOptions;
export const mockUser = (): NormalOption[] => [
  { value: '郑艳丽', label: '郑艳丽' },
  { value: '胡天一', label: '胡天一' }
];
/**
 * 工单状态
 */
export const WorkOrderStatus = (isAll?: boolean): NormalOption[] => {
  const options = [
    // { label: '待派单', value: 'PENDING' },
    // { label: '待接单', value: 'ASSIGN' },
    // { label: '处理中', value: 'RESOLVING' },
    // { label: '审核中', value: 'SUBMIT' },
    // { label: '审核通过', value: 'APPROVED' },
    // { label: '审核退回', value: 'REJECTED' }

    { value: 'PENDING', label: '待派单' },
    { value: 'ASSIGN', label: '待接单' },
    { value: 'RESOLVING', label: '处理中' },
    { value: 'ARRIVING', label: '到场' },
    { value: 'PROCESSING', label: '处理' },
    { value: 'SUBMIT', label: '待审核' },
    { value: 'CHARGEBACK_REVIEW', label: '审核中' },
    { value: 'REVIEW', label: '复审中' },
    { value: 'REJECTED', label: '审核退回' },
    { value: 'APPROVED', label: '审核通过' },
    { value: 'CHARGEBACK', label: '已退单' },
    { value: 'TERMINATED', label: '已终止' }
  ];
  isAll && options.unshift({ label: '全部', value: '' });
  return options;
};
export const formatWorkOrderStatus = (val: string) => {
  return WorkOrderStatus().find((item) => item.value === val)?.label;
};
export const StatusForDisableStop = ['APPROVED', 'CHARGEBACK', 'TERMINATED'];
/**
 * 工单步骤
 */
export const WorkOrderStep = (isAll?: boolean): NormalOption[] => {
  const options = [
    { label: '发起', value: 'PENDING' },
    { label: '派单', value: 'ASSIGN' },
    { label: '接单', value: 'RESOLVING' },
    { label: '到场', value: 'ARRIVING' },
    { label: '处理', value: 'PROCESSING' },
    { label: '完成', value: 'SUBMIT' },
    { label: '审核通过', value: 'APPROVED' },
    { label: '审核退回', value: 'REJECTED' }
  ];
  isAll && options.unshift({ label: '全部', value: '' });
  return options;
};

/**
 * 投诉状态
 */
export const complaint = (isAll?: boolean): NormalOption[] => {
  const options = [
    { label: '待签收', value: 'PENDING' },
    { label: '已签收', value: 'ASSIGN' },
    { label: '处理中', value: 'RESOLVING' },
    { label: '提交上报', value: 'SUBMIT' },
    { label: '已完成', value: 'APPROVED' }
  ];
  isAll && options.unshift({ label: '全部', value: '' });
  return options;
};
const WorkOrderDealLevelOption: NormalOption[] = [
  { label: 'A级别（12小时）', value: 'A' },
  { label: 'B级别（1天）', value: 'B' },
  { label: 'C级别（3天）', value: 'C' },
  { label: 'D级别（7天）', value: 'D' }
];
/**
 * 工单处理级别
 * @param isAll
 * @returns
 */
export const WorkOrderDealLevel = (isAll?: boolean) => {
  const options = [...WorkOrderDealLevelOption];
  isAll && options.unshift({ label: '全部', value: '' });
  return options;
};
export const formatWorkOrderDealLevel = (val: string) => {
  return (
    WorkOrderDealLevelOption.find((item) => item.value === val)?.label || val
  );
};
// const initOrderList = () => {
//   Array.from({ length: 145 }).map((item, i) => {
//     const date = moment().subtract(i * 104 * 59, 's').valueOf()
//     const creator = Users[Math.floor(Math.random() * Users.length)]
//     const dealer = Users[(i + 5) % Users.length]
//     const dispatcher = Users[(i + 7) % Users.length]
//     const reporter = Users[(i + 3) % Users.length]
//     OrderList.push({
//       id: `GD${date}${i}`,
//       from: FromOption[i % FromOption.length].label,
//       orderType: OrderTypeOptions[i % OrderTypeOptions.length].label,
//       // createDepart: '',
//       creator: creator.name,
//       applyTime: date + 30000,
//       createTime: date,
//       reporterTel: reporter.tel,
//       // dispatchDepart: ,
//       dispatcher: dispatcher.name,
//       dispatchTime: date,
//       // dealDepart: dealer.name,
//       dealer: dealer.name,
//       preCompeleteTime: date + (60 * 60 * 24 * 1000),
//       compeleteTime: date + (60 * 60 * 22 * 1000),
//       title: OrderTypeOptions[i % OrderTypeOptions.length].label,
//       desc: OrderTypeOptions[i % OrderTypeOptions.length].label,
//       address: reporter.address,
//       outerCode: '',
//       reason: '请尽快处理',
//       emergency: '紧急',
//       currentStatus: StatusList[i % 11 + 1].label
//     })
//   })
// }
/** 工单数组 */
// const OrderList: any[] = []
// initOrderList()

// export const getOrderList = (page: number, size: number, params: { key: string; value: string; contain?: boolean }[]) => {
//   let orders = OrderList || []
//   params.forEach(item => {
//     if (item.value) {
//       if (item.contain) {
//         orders = orders.filter(order => order[item.key].indexOf(item.value) !== -1)
//       } else {
//         orders = orders.filter(order => order[item.key] === item.value)
//       }
//     }
//   })
//   return {
//     data: orders.slice((page - 1) * size, page * size),
//     total: orders.length
//   }
// }

// export const getUserEventCount = () => Users.map(item => {
//   const count = OrderList.filter(order => order.creator === item.name).length
//   return ({
//     name: item.name,
//     count
//   })
// }).sort((a, b) => (a.count <= b.count ? 1 : -1))
export const initWorkOrderDetailStepInfo = (
  status: string
): IAttrTableRow[][] => {
  switch (status) {
    case 'CREATE':
      return [
        [{ label: '标题', prop: 'title', cols: 5 }],
        [
          { label: '发起人员', prop: 'organizerName' },
          { label: '紧急程度', prop: 'level' },
          { label: '来源', prop: 'source' }
        ],
        [
          { label: '类型', prop: 'type' },
          { label: '处理级别', prop: 'processLevel' },
          { label: '上报人电话', prop: 'uploadPhone' }
        ],
        [{ label: '地址', prop: 'address', cols: 5 }],
        [{ label: '描述', prop: 'remark', cols: 5 }],
        [
          {
            label: '直接分派',
            prop: 'directDispatch',
            formatter: (row) => (row.directDispatch === true ? '是' : '否')
          },
          { label: '预计完成时间', prop: 'estimatedFinishTime', cols: 3 }
        ],
        [{ label: '图片', prop: 'imgUrl', image: true, cols: 5 }],
        [{ label: '录音', prop: 'audioUrl', audio: true, cols: 5 }],
        [{ label: '视频', prop: 'videoUrl', video: true, cols: 5 }],
        [{ label: '附件', prop: 'otherFileUrl', file: true, cols: 5 }]
      ];
    case 'ARRIVING':
    case 'PROCESSING':
    case 'TERMINATED':
      return [
        [{ label: '处理人', prop: 'nextProcessUserName' }],
        [{ label: '备注', prop: 'processRemark' }]
      ];
    case 'SUBMIT':
      return [
        [
          { label: '完成人', prop: 'processUserName' },
          { label: '指定审核人', prop: 'nextProcessUserName' }
        ],
        [{ label: '备注', prop: 'processRemark', cols: 3 }]
      ];
    case 'HANDOVER_REVIEW':
      return [
        [
          { label: '申请人', prop: 'processUserName' },
          { label: '转发至', prop: 'nextProcessUserName' },
          { label: '指定审核人', prop: 'expectUsername' }
        ],
        [{ label: '备注', prop: 'processRemark', cols: 5 }]
      ];
    case 'CHARGEBACK_REVIEW':
      return [
        [{ label: '申请人', prop: 'processUserName' }],
        [{ label: '审核人', prop: 'nextProcessUserName' }]
      ];
    case 'REVIEW':
      return [
        [{ label: '指定复审人', prop: 'nextProcessUserName' }],
        [{ label: '备注', prop: 'processRemark' }]
      ];
    case 'APPROVED':
    case 'REJECTED':
    case 'CHARGEBACK':
      return [
        [{ label: '审核人', prop: 'processUserName' }],
        [{ label: '审核备注', prop: 'processRemark' }]
      ];
    case 'REASSIGN':
      return [[{ label: '处理人', prop: 'nextProcessUserName' }]];
    default:
      return [];
  }
};
