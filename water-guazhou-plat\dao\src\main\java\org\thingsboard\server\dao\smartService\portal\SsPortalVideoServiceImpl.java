package org.thingsboard.server.dao.smartService.portal;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.smartService.portal.SsPortalVideo;
import org.thingsboard.server.dao.sql.smartService.portal.SsPortalVideoMapper;
import org.thingsboard.server.dao.util.imodel.query.smartService.portal.SsPortalVideoPageRequest;

import java.util.Date;

@Service
public class SsPortalVideoServiceImpl implements SsPortalVideoService {

    @Autowired
    private SsPortalVideoMapper ssPortalVideoMapper;

    @Override
    public SsPortalVideo save(SsPortalVideo ssPortalVideo) {
        if (StringUtils.isBlank(ssPortalVideo.getId())) {
            ssPortalVideo.setCreateTime(new Date());
            ssPortalVideoMapper.insert(ssPortalVideo);
        } else {
            ssPortalVideoMapper.updateById(ssPortalVideo);
        }
        return ssPortalVideo;
    }

    @Override
    public boolean delete(String id) {
        return ssPortalVideoMapper.deleteById(id) > 0;
    }

    @Override
    public PageData<SsPortalVideo> getList(SsPortalVideoPageRequest request) {
        IPage<SsPortalVideo> iPage = ssPortalVideoMapper.getList(request);
        PageData<SsPortalVideo> pageData = new PageData<>(iPage.getTotal(), iPage.getRecords());
        return pageData;
    }
}
