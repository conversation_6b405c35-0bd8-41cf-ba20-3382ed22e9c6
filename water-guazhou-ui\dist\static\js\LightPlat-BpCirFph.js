import{d,g as e,n as o,p as s,an as n,aw as i,q as l,i as r,cs as c,bh as t,C as p}from"./index-r0dFAfgr.js";const _={class:"light-plat"},u={class:"info"},h={class:"sync-ratio"},m={key:0,class:"sync-ratio__label"},v={class:"value"},y={class:"unit"},f={class:"footer"},g={class:"title"},b=d({__name:"LightPlat",props:{data:{}},setup(k){return(a,w)=>(e(),o("div",_,[s("div",u,[s("div",h,[a.data.delta!==void 0?(e(),o("span",m,"同比")):n("",!0),a.data.delta!==void 0?(e(),o("div",{key:1,class:i(["sync-ratio__info",a.data.type])},[l(r(c),{icon:a.data.type==="up"?"material-symbols:arrow-drop-up":"material-symbols:arrow-drop-down"},null,8,["icon"]),s("span",null,t(a.data.delta)+"%",1)],2)):n("",!0)]),s("div",v,[s("span",null,t(a.data.value),1),s("span",y,t(a.data.unit||"%"),1)])]),s("div",f,[s("span",g,t(a.data.title),1)])]))}}),B=p(b,[["__scopeId","data-v-11a36082"]]);export{B as default};
