import{w as d,b as l,s as S}from"./Point-WxyopZva.js";import{_ as G,c as _,E as w,s as P,g as s,T as E,f as u}from"./pe-B8dP0-Ut.js";function M(){return G()}function v(e,n){const t=[];e=(e=(e=e.replace(/[\u00B0\u00BA]/g,"^")).replace(/\u2032/g,"'")).replace(/\u2033/g,'"');const o=c(n);return _.dmsToGeog(o,1,[e],t)?new d(t[0][0],t[0][1],n||l.WGS84):null}function p(e,n,t){const o=[],r=g(t);if(r===-1)return console.warn(`invalid conversionMode: ${t}`),null;const i=c(n);return s.mgrsToGeogExtended(i,1,[e],r,o)?new d(o[0][0],o[0][1],n||l.WGS84):null}function R(e,n){const t=[];!n&&/\(.+27/.test(e)&&(n=l.GCS_NAD_1927);const o=c(n);return E.usngToGeog(o,1,[e],t)?new d(t[0][0],t[0][1],n||l.WGS84):null}function $(e,n,t){const o=[],r=T(t);if(r===-1)return console.warn(`invalid conversionMode: ${t}`),null;const i=c(n);return u.utmToGeog(i,1,[e],r,o)?new d(o[0][0],o[0][1],n||l.WGS84):null}function b(e,n,t=0){const o=[[e.x,e.y]],r=[],i=c(e.spatialReference);let a=0;switch(n){case"dd":a=_.geogToDd(i,1,o,t,r);break;case"ddm":a=_.geogToDdm(i,1,o,t,r);break;case"dms":a=_.geogToDms(i,1,o,t,r);break;default:return console.warn(`invalid format: ${n}`),null}return a?r[0]:null}function D(e,n,t=0,o=!1){const r=[[e.x,e.y]],i=[],a=c(e.spatialReference);let f=g(n);return f===-1?(console.warn(`invalid conversionMode: ${n}`),null):(o&&(f|=s.PE_MGRS_ADD_SPACES),s.geogToMgrsExtended(a,1,r,t,!1,f,i)?i[0]:null)}function O(e,n=0,t=!1){const o=[[e.x,e.y]],r=[],i=c(e.spatialReference);return E.geogToUsng(i,1,o,n,!1,t,r)?r[0]:null}function L(e,n,t=!1){const o=[[e.x,e.y]],r=[],i=c(e.spatialReference);let a=T(n);return a===-1?(console.warn(`invalid conversionMode: ${n}`),null):(t&&(a|=u.PE_UTM_OPTS_ADD_SPACES),u.geogToUtm(i,1,o,a,r)?r[0]:null)}function c(e){let n=null;if(e||(e=l.WGS84),e.wkid){if(n=w.geogcs(e.wkid),!n)throw new S("coordinate-formatter:invalid-spatial-reference","wkid is not valid")}else{if(!e.wkt)throw new S("coordinate-formatter:invalid-spatial-reference","wkid and wkt are missing");if(n=w.fromString(P.PE_TYPE_GEOGCS,e.wkt),!n)throw new S("coordinate-formatter:invalid-spatial-reference","wkt is not valid")}return n}function g(e){let n=-1;switch(e){case"automatic":n=s.PE_MGRS_STYLE_AUTO;break;case"new-180-in-zone-01":n=s.PE_MGRS_STYLE_NEW|s.PE_MGRS_180_ZONE_1_PLUS;break;case"new-180-in-zone-60":n=s.PE_MGRS_STYLE_NEW;break;case"old-180-in-zone-01":n=s.PE_MGRS_STYLE_OLD|s.PE_MGRS_180_ZONE_1_PLUS;break;case"old-180-in-zone-60":n=s.PE_MGRS_STYLE_OLD}return n}function T(e){let n=-1;switch(e){case"latitude-band-indicators":n=u.PE_UTM_OPTS_NONE;break;case"north-south-indicators":n=u.PE_UTM_OPTS_NS}return n}export{v as E,O as G,D as P,M as S,b as T,R as g,L as k,$ as m,p as w};
