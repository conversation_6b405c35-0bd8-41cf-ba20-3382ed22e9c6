package org.thingsboard.server.dao.model.sql.workOrder;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.Info;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseUsername;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.util.Date;

@Getter
@Setter
@TableName("work_order_details")
@ResponseEntity
public class WorkOrderDetail {
    @TableId
    // 子表记录ID
    private String id;

    // 主表工单ID
    private String mainId;

    // 当前步骤类型
    @Info(name = "typeName")
    private WorkOrderStatus type;

    // 处理人
    @ParseUsername(withDepartment = true, withOrganization = true)
    private String processUserId;

    // 处理时间
    private Date processTime;

    // 处理备注
    private String processRemark;

    // 处理详情。json格式的处理详情，用于保存处理流转中的除备注外的字段的数据
    private String processAdditionalInfo;

    // 下一步处理人
    @ParseUsername(withDepartment = true, withOrganization = true)
    private String nextProcessUserId;

    private String typeName() {
        return type.getDetailName();
    }
}

