import{d as F,c as p,g as B,n as k,p as u,bh as x,i as o,q as a,cs as l,t as C,aB as V,c3 as h,C as D}from"./index-r0dFAfgr.js";/* empty css                  */const I={class:"slide-bar"},S={class:"title"},y=F({__name:"SliderBar",emits:["progress"],setup(b,{emit:d}){const t=d,s=p(!1),e=p(0),m=()=>{e.value>0&&(e.value-=1,t("progress",e.value))},n=()=>{e.value<23?(e.value+=1,t("progress",e.value)):c()},v=()=>{s.value=!s.value,s.value?_():c()};let r=-1;const _=()=>{r=setInterval(()=>n(),3e3)},c=()=>{clearInterval(r)};return(w,i)=>{const f=h;return B(),k(V,null,[u("div",I,[u("span",S,"历时24小时 当前时间："+x(o(e))+"h",1),a(o(l),{icon:"ep:arrow-left",color:"#318DFF",onClick:m}),a(o(l),{icon:o(s)?"ep:video-pause":"ep:video-play",color:"#318DFF",onClick:v},null,8,["icon"]),a(o(l),{icon:"ep:arrow-right",color:"#318DFF",onClick:n})]),a(f,{modelValue:o(e),"onUpdate:modelValue":i[0]||(i[0]=g=>C(e)?e.value=g:null),max:23,min:0},null,8,["modelValue"])],64)}}}),q=D(y,[["__scopeId","data-v-3142ac9e"]]);export{q as default};
