<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.msg.MsgTemplateMapper">

    <select id="getList" resultType="org.thingsboard.server.dao.model.sql.MsgTemplateEntity">
        select a.*,b.sign_name as signName
        from tb_msg_template a
        left join tb_msg_config b on a.config_id = b.id

        <where>
            a.tenant_id = #{tenantId}
            <if test="configId != null and configId != ''">
                and a.config_id = #{configId}
            </if>
            <if test="name != null and name != ''">
                and a.name like '%'||#{name}||'%'
            </if>
        </where>
        order by a.create_time desc
    </select>

    <select id="selectByConfigId" resultType="org.thingsboard.server.dao.model.sql.MsgTemplateEntity">
        select * from tb_msg_template where config_id = #{configId}
    </select>

</mapper>