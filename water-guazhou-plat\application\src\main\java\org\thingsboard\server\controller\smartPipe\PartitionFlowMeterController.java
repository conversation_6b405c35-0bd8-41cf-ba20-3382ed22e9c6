package org.thingsboard.server.controller.smartPipe;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.request.PartitionMountRequest;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.PartitionFlowMeter;
import org.thingsboard.server.dao.smartPipe.PartitionFlowMeterService;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.List;

/**
 * 智慧管网-流量表信息
 */
@RestController
@RequestMapping("api/spp/dma/partition/flowMeter")
public class PartitionFlowMeterController extends BaseController {

    @Autowired
    private PartitionFlowMeterService partitionFlowMeterService;

    @PostMapping
    public IstarResponse save(@RequestBody PartitionFlowMeter partitionFlowMeter) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        partitionFlowMeter.setTenantId(tenantId);

        return IstarResponse.ok(partitionFlowMeterService.save(partitionFlowMeter));
    }

    @GetMapping("list")
    public IstarResponse getList(PartitionMountRequest request) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        request.setTenantId(tenantId);
        return IstarResponse.ok(partitionFlowMeterService.getList(request));
    }

    @DeleteMapping
    public IstarResponse delete(@RequestBody List<String> ids) {
        partitionFlowMeterService.delete(ids);
        return IstarResponse.ok("删除成功");
    }
}
