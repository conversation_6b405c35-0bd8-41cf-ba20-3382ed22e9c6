{"ruleChain": {"additionalInfo": null, "name": "过滤后上报到TSdb", "firstRuleNodeId": null, "root": true, "debugMode": false, "configuration": null}, "metadata": {"firstNodeIndex": 0, "nodes": [{"additionalInfo": {"layoutX": 287, "layoutY": 150}, "type": "org.thingsboard.rule.engine.filter.TbMsgTypeSwitchNode", "name": "数据类型选择", "debugMode": false, "configuration": {"version": 0}}, {"additionalInfo": {"layoutX": 276, "layoutY": 304}, "type": "org.thingsboard.rule.engine.telemetry.TbMsgTelemetryAttributeFilterNode", "name": "数据过滤", "debugMode": false, "configuration": {"hitsdb_IP": "ts-2ze1h2k5t31p45153.hitsdb.rds.aliyuncs.com", "hitsdb_PORT": 3242, "defaultTTL": 100}}, {"additionalInfo": {"layoutX": 585, "layoutY": 305}, "type": "org.thingsboard.rule.engine.action.TbMsgTelemetryAttributeAlarmNode", "name": "数据过滤成功后触发报警", "debugMode": false, "configuration": {"hitsdb_IP": "ts-2ze1h2k5t31p45153.hitsdb.rds.aliyuncs.com", "hitsdb_PORT": 3242, "defaultTTL": 100}}, {"additionalInfo": {"layoutX": 586, "layoutY": 406}, "type": "org.thingsboard.rule.engine.action.TbLogNode", "name": "数据过滤失败日志", "debugMode": false, "configuration": {"jsScript": "return '数据过滤失败！:\\n' + JSON.stringify(msg) + '\\nIncoming metadata:\\n' + JSON.stringify(metadata);"}}, {"additionalInfo": {"layoutX": 912, "layoutY": 302}, "type": "org.thingsboard.rule.engine.telemetry.TbMsgTsDBNode", "name": "保存数据到数据库", "debugMode": false, "configuration": {"hitsdb_IP": "ts-2ze1h2k5t31p45153.hitsdb.rds.aliyuncs.com", "hitsdb_PORT": 3242, "defaultTTL": 1000}}, {"additionalInfo": {"layoutX": 755, "layoutY": 147}, "type": "org.thingsboard.rule.engine.action.TbLogNode", "name": "数据触发报警日志", "debugMode": false, "configuration": {"jsScript": "return '数据触发报警:\\n' + JSON.stringify(msg) + '\\nIncoming metadata:\\n' + JSON.stringify(metadata);"}}, {"additionalInfo": {"layoutX": 517, "layoutY": 52}, "type": "org.thingsboard.rule.engine.telemetry.TbMsgTimeseriesNode", "name": "保存原始数据", "debugMode": false, "configuration": {"defaultTTL": 0}}], "connections": [{"fromIndex": 0, "toIndex": 1, "type": "Post telemetry"}, {"fromIndex": 0, "toIndex": 6, "type": "Post telemetry"}, {"fromIndex": 1, "toIndex": 2, "type": "Success"}, {"fromIndex": 1, "toIndex": 3, "type": "Failure"}, {"fromIndex": 2, "toIndex": 4, "type": "Failure"}, {"fromIndex": 2, "toIndex": 5, "type": "Success"}, {"fromIndex": 5, "toIndex": 4, "type": "Success"}], "ruleChainConnections": null}}