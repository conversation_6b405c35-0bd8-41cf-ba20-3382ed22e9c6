package org.thingsboard.server.controller.dispatch;

import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.UserId;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.dispatch.EmergencyUserService;
import org.thingsboard.server.dao.model.sql.department.Organization;
import org.thingsboard.server.dao.model.sql.smartProduction.dispatch.EmergencyUser;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch.EmergencyUserPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch.EmergencyUserSaveRequest;
import org.thingsboard.server.utils.imodel.annotations.IStarController;

@IStarController
@RequestMapping({"/api/sp/emergencyUser"})
public class EmergencyUserController extends BaseController {
    @Autowired
    private EmergencyUserService service;

    public EmergencyUserController() {
    }

    @GetMapping
    public IPage<EmergencyUser> findAllConditional(EmergencyUserPageRequest request) {
        return this.service.findAllConditional(request);
    }

    @GetMapping({"/tree/{depth}"})
    public List<Organization> treeUser(@PathVariable Integer depth) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(this.getTenantId().getId());
        return this.service.treeUser(tenantId, depth);
    }

    @PostMapping
    public EmergencyUser save(@RequestBody EmergencyUserSaveRequest req) {
        return this.service.save(req);
    }

    @PostMapping({"/sync"})
    public boolean sync() throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(this.getTenantId().getId());
        String userId = UUIDConverter.fromTimeUUID(((UserId) this.getCurrentUser().getId()).getId());
        return this.service.sync(userId, tenantId);
    }

    @PatchMapping({"/{id}"})
    public boolean edit(@RequestBody EmergencyUserSaveRequest req, @PathVariable String id) {
        return this.service.update((EmergencyUser) req.unwrap(id));
    }

    @DeleteMapping({"/{id}"})
    public boolean delete(@PathVariable String id) {
        return this.service.delete(id);
    }
}
