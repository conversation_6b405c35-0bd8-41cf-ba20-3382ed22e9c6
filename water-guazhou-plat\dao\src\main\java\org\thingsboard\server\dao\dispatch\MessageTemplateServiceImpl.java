package org.thingsboard.server.dao.dispatch;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.smartProduction.dispatch.MessageTemplate;
import org.thingsboard.server.dao.sql.smartProduction.dispatch.MessageTemplateMapper;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch.MessageTemplatePageRequest;

@Service
public class MessageTemplateServiceImpl implements MessageTemplateService {
    @Autowired
    private MessageTemplateMapper mapper;

    @Override
    public IPage<MessageTemplate> findAllConditional(MessageTemplatePageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    public MessageTemplate save(MessageTemplate entity) {
        mapper.insert(entity);
        return entity;
    }

    @Override
    public boolean update(MessageTemplate entity) {
        return mapper.update(entity);
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }
}
