import{_ as P}from"./TreeBox-DDD2iwoR.js";import{_ as $}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as R}from"./CardTable-rdWOL4_6.js";import{_ as V}from"./CardSearch-CB_HNR-Q.js";import{_ as j}from"./index-BJ-QPYom.js";import{d as q,M as A,c as x,s as B,r as _,x as n,a8 as D,a9 as C,bN as N,S as O,o as W,g as U,h as G,F as S,q as h,i as y,b7 as H}from"./index-r0dFAfgr.js";import{I as p}from"./common-CvK_P_ao.js";import{e as K,f as Q,h as Y,c as z,i as J}from"./equipmentManage-DuoY00aj.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";const ce=q({__name:"index",setup(X){const{$btnPerms:I}=A(),o=x(),b=x(),E=x({filters:[{label:"类别编码",field:"serialId",type:"input"},{label:"类别名称",field:"name",type:"input"}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",icon:p.QUERY,click:()=>v(!0)},{type:"default",perm:!0,text:"重置",svgIcon:B(H),click:()=>{var e;(e=b.value)==null||e.resetForm(),v()}},{perm:!0,text:"新建顶级类别",icon:p.ADD,type:"success",click:()=>T("新建顶级类别")}]}]}),u=_({defaultExpandAll:!0,indexVisible:!0,rowKey:"id",columns:[{label:"类别编码",prop:"serialId",formatter:e=>e.serialId.replace(/(\d{2})(\d{3})(\d{3})(\d{6})/,"$1-$2-$3-$4")},{label:"类别名称(层级)",prop:"treePath"},{label:"节点级别",prop:"level"},{label:"排序编号",prop:"orderNum"},{label:"备注",prop:"remark"},{label:"创建时间",prop:"createTime"}],operationWidth:"240px",operations:[{disabled:e=>e.level==="3",type:"success",text:"新建子集",icon:p.ADD,perm:I("RoleManageAdd"),click:e=>T("新建子集",e)},{disabled:e=>e.level==="0",type:"primary",text:"编辑",icon:p.EDIT,perm:I("RoleManageEdit"),click:e=>L(e)},{disabled:e=>e.level==="0",type:"danger",text:"删除",perm:I("RoleManageDelete"),icon:p.DELETE,click:e=>M(e)}],dataList:[],pagination:{hide:!0}}),c=_({title:"新增",dialogWidth:"500px",labelWidth:"100px",submit:e=>{const a=e.serialId;e.serialId=l.prepend+e.serialIdNum+l.append,!(a!==e.serialId&&F(e))&&(e.id?K(e.id,e).then(()=>{var t;n.success("修改成功"),(t=o.value)==null||t.closeDialog(),f()}).catch(t=>{n.warning(t)}):Q(e).then(()=>{var t;n.success("添加成功"),(t=o.value)==null||t.closeDialog(),f()}).catch(t=>{n.warning(t)}))},defaultValue:{},group:[{fields:[{type:"select-tree",label:"所属类别",field:"category",readonly:!0,checkStrictly:!0,options:D(()=>C(i.data))},{type:"input",label:"类别编码",field:"serialIdNum",rules:[{required:!0,message:"请输入类别编码"}],prepend:D(()=>l.prepend),append:D(()=>l.append)},{type:"hint",text:"注：编码规则(长度14)>=12(级别1)+001(级别2)+001(级别3)+000000(设备编码)"},{type:"input",label:"类别名称",field:"name",rules:[{required:!0,message:"请输入类别名称"}]},{type:"input",label:"节点级别",field:"level",readonly:!0},{type:"input-number",label:"排序编号",field:"orderNum",rules:[{required:!0,message:"请输入排序编号"}]},{type:"textarea",label:"备注/说明",field:"remark"}]}]});function F(e){if(e.serialId.length!==14)return n.warning("类别编码格式错误"),!0;if(N(i.data||[],"children","serialId").includes(e.serialId))return n.warning("类别编码重复"),!0}function k(e,a){const t=N(e.children||[e]," ","serialId")||[];let r=Math.max(...t);return a===1&&(r+=1e12),a===2&&(r+=1e9),a===3&&(r+=1e6),(r+"").length===13?"0"+r:r+""}const i=_({title:" ",data:[],currentProject:{},expandOnClickNode:!1,isFilterTree:!0,treeNodeHandleClick:e=>{i.currentProject=e,v()}}),T=(e,a)=>{var m,g;if(e==="新建顶级类别"&&!i.data.length){n.warning("请等待结构加载完成");return}c.title=e;let t=1;const r=k(i.data[0],t);let s="";if(t===1&&(l.prepend="",l.append=r.slice(2,14),s=r.slice(0,2)),c.defaultValue={category:((m=i.data[0])==null?void 0:m.id)||"",level:"1",serialIdNum:s},e==="新建子集"){t=a.level*1+1;const d=k(a,t);if(t===2&&(l.prepend=d.slice(0,2),l.append=d.slice(5,14),s=d.slice(2,5)),t===3&&(l.prepend=d.slice(0,5),l.append=d.slice(8,14),s=d.slice(5,8)),t>3){n.warning("最多三级");return}c.defaultValue={parentId:a.id,category:a.id,level:t,serialIdNum:s}}(g=o.value)==null||g.openDialog()},L=e=>{var t;c.title="编辑";const a=e.level;a==="1"&&(l.prepend="",l.append=e.serialId.slice(2,14),e.serialIdNum=e.serialId.slice(0,2)),a==="2"&&(l.prepend=e.serialId.slice(0,2),l.append=e.serialId.slice(5,14),e.serialIdNum=e.serialId.slice(2,5)),a==="3"&&(l.prepend=e.serialId.slice(0,5),l.append=e.serialId.slice(8,14),e.serialIdNum=e.serialId.slice(5,8)),c.defaultValue={category:e.parentId,...e||{}},(t=o.value)==null||t.openDialog()},M=e=>{O("确定删除该设备类别","删除提示").then(()=>{Y(e.id).then(()=>{n.success("删除成功"),f()}).catch(a=>{n.error(a.toString())})})};function f(){z().then(e=>{i.data=C(e.data.data||[]),i.currentProject=e.data.data[0],u.dataList=i.data})}const l=_({prepend:"",append:""}),v=async e=>{var a;if(e){const t=((a=b.value)==null?void 0:a.queryParams)||{};J(t).then(r=>{u.dataList=r.data.data.data})}else u.dataList=[i.currentProject]};return W(async()=>{f()}),(e,a)=>{const t=j,r=V,s=R,m=$,g=P;return U(),G(g,null,{tree:S(()=>[h(t,{"tree-data":y(i)},null,8,["tree-data"])]),default:S(()=>[h(r,{ref_key:"refSearch",ref:b,config:y(E)},null,8,["config"]),h(s,{class:"card-table",config:y(u)},null,8,["config"]),h(m,{ref_key:"refForm",ref:o,config:y(c)},null,8,["config"])]),_:1})}}});export{ce as default};
