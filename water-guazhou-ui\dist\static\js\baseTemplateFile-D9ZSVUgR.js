import{_ as C}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as S}from"./CardTable-rdWOL4_6.js";import{_ as F}from"./CardSearch-CB_HNR-Q.js";import{z as p,C as k,c as y,r as d,b as r,S as q,o as v,g as L,n as P,q as m}from"./index-r0dFAfgr.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";function B(i){return p({url:"/api/base/template/file/list",method:"get",params:i})}function V(i){return p({url:"/api/base/template/file/getDetail",method:"get",params:{id:i}})}function _(i){return p({url:"/api/base/template/file/add",method:"post",data:i})}function x(i){return p({url:"/api/base/template/file/edit",method:"post",data:i})}function z(i){return p({url:"/api/base/template/file/deleteIds",method:"delete",data:i})}const E={class:"wrapper"},I={__name:"baseTemplateFile",setup(i){const f=y(),c=y(),T=d({labelWidth:"100px",filters:[{type:"input",label:"模板名称",field:"name",placeholder:"请输入模板名称",onChange:()=>n()},{type:"btn-group",btns:[{type:"primary",perm:!0,text:"查询",click:()=>n()},{perm:!0,type:"primary",text:"新增",click:()=>h()},{perm:!0,type:"danger",text:"批量删除",click:()=>b()}]}],defaultParams:{}}),s=d({columns:[{label:"模板名称",prop:"name"},{label:"模板编码",prop:"code"},{label:"分类",prop:"type"},{label:"文件类型",prop:"fileType"},{label:"文件路径",prop:"filePath"},{label:"描述",prop:"description"}],dataList:[],operations:[{perm:!0,type:"primary",isTextBtn:!0,text:"查看详情",click:e=>D(e)},{perm:!0,type:"primary",isTextBtn:!0,text:"编辑",click:e=>h(e)},{perm:!0,type:"danger",isTextBtn:!0,text:"删除",click:e=>b(e)}],pagination:{total:0,page:1,align:"right",limit:20,handlePage:e=>{s.pagination.page=e,n()},handleSize:e=>{s.pagination.limit=e,n()}},handleSelectChange:e=>{s.selectList=e||[]}}),a=d({title:"新增模板文件",group:[{fields:[{type:"input",label:"模板名称",field:"name",rules:[{required:!0,message:"请输入模板名称"}]},{type:"input",label:"模板编码",field:"code",rules:[{required:!0,message:"请输入模板编码"}]},{type:"input",label:"分类",field:"type",rules:[{required:!0,message:"请输入分类"}]},{type:"input",label:"文件类型",field:"fileType",rules:[{required:!0,message:"请输入文件类型"}]},{type:"input",label:"文件路径",field:"filePath",rules:[{required:!0,message:"请输入文件路径"}]},{type:"textarea",label:"描述",field:"description",placeholder:"请输入描述信息"}]}],labelPosition:"top",defaultValue:{},dialogWidth:600,draggable:!0,showSubmit:!0,showCancel:!0,cancelText:"取消",submitText:"确定",submit:async e=>{var t;try{e.id?(await x(e),r.success("修改成功")):(await _(e),r.success("新增成功")),(t=c.value)==null||t.closeDialog(),n()}catch{r.error("操作失败")}}}),g=()=>{a.group[0].fields.forEach(e=>{e.disabled=!1,e.readonly=!1}),a.showSubmit=!0,a.showCancel=!0,a.cancelText="取消",a.submitText="确定",a.submit=async e=>{var t;try{e.id?(await x(e),r.success("修改成功")):(await _(e),r.success("新增成功")),(t=c.value)==null||t.closeDialog(),n()}catch{r.error("操作失败")}}},D=async e=>{var t,l;try{const o=await V(e.id),u=((t=o.data)==null?void 0:t.data)||o;g(),a.title="变更记录详情",a.defaultValue={...u},a.group[0].fields.forEach(w=>{w.disabled=!0}),a.showSubmit=!1,a.cancelText="关闭",(l=c.value)==null||l.openDialog()}catch{r.error("获取详情失败")}},h=e=>{var t;g(),e?(a.title="编辑模板文件",a.defaultValue={...e}):(a.title="新增模板文件",a.defaultValue={}),(t=c.value)==null||t.openDialog()},b=async e=>{try{const t=e?[e.id]:s.selectList.map(l=>l.id);if(!t.length){r.warning("请选择要删除的数据");return}await q("确定要删除选中的数据吗？"),await z(t),r.success("删除成功"),n()}catch(t){t!=="cancel"&&r.error("删除失败")}},n=async()=>{var e,t;try{const l=await B({page:s.pagination.page,size:s.pagination.limit,...((e=f.value)==null?void 0:e.queryParams)||{}}),o=((t=l.data)==null?void 0:t.data)||l;s.dataList=o.records||o,s.pagination.total=o.total||o.length||0}catch{r.error("数据加载失败")}};return v(()=>{n()}),(e,t)=>{const l=F,o=S,u=C;return L(),P("div",E,[m(l,{ref_key:"refSearch",ref:f,config:T},null,8,["config"]),m(o,{class:"card-table",config:s},null,8,["config"]),m(u,{ref_key:"refDialogForm",ref:c,config:a},null,8,["config"])])}}},G=k(I,[["__scopeId","data-v-d406bb29"]]);export{G as default};
