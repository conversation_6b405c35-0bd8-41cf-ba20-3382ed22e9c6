.esri-view {
  display: flex;
  margin: 0;
  padding: 0;

  .esri-view-user-storage {
    overflow: hidden;
  }

  .esri-view-root {
    position: relative;
    flex: 1 1 100%;
    border: none;
    padding: 0;
    margin: 0;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    -webkit-tap-highlight-color: transparent;
  }

  .esri-view-surface {
    position: absolute;
    border: none;
    padding: 0;
    margin: 0;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    user-select: none;

    /*
     * TODO
     * Layers should probably manage this on their own
     */
    direction: ltr;

    &--touch {
      &-none {
        touch-action: none;
      }
      &-pan {
        touch-action: pan-x pan-y;
      }
    }
  }

  .esri-view-surface--inset-outline {
    outline: 0;
  }

  .esri-view-surface--inset-outline:focus::after {
    content: "";
    box-sizing: border-box;
    position: absolute;
    z-index: 999;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    outline: auto 2px Highlight;
    outline: auto 5px -webkit-focus-ring-color;
    outline-offset: -3px;
    pointer-events: none;
    overflow: hidden;
  }

  .esri-view-surface[data-interacting="true"] {
    cursor: move;
    cursor: grabbing;
    cursor: -moz-grabbing;
    cursor: -webkit-grabbing;
  }

  /*
   * CSS2 cursors:
   * auto, inherit, crosshair, default, help, move, pointer , progress, text, wait,
   * e-resize, ne-resize, nw-resize, n-resize, se-resize, sw-resize, s-resize, w-resize
   *
   * CSS3 cursors:
   * vertical-text, no-drop, not-allowed,
   * ew-resize, ns-resize, nesw-resize, nwse-resize, col-resize, row-resize, all-scroll
   *
   * CSS3 with limited support:
   * - cursor: none (not IE, Safari, Opera)
   * - cursor: context-menu (not Firefox, Chrome)
   * - cursor: cell (not Safari)
   * - cursor: alias (not Safari)
   * - cursor: copy (not Safari)
   *
   * Browser specific:
   * - cursor: -webkit-grab; cursor: -moz-grab;
   * - cursor: -webkit-grabbing; cursor: -moz-grabbing;
   * - cursor: -webkit-zoom-in; cursor: -moz-zoom-in;
   * - cursor: -webkit-zoom-out; cursor: -moz-zoom-out;
   *
   * Supported cursors:
   * default, crosshair, help, move, pointer, progress, grab, grabbing
   */
  .esri-view-surface[data-cursor="copy"] {
    cursor: copy;
  }
  .esri-view-surface[data-cursor="crosshair"] {
    cursor: crosshair;
  }
  .esri-view-surface[data-cursor="help"] {
    cursor: help;
  }
  .esri-view-surface[data-cursor="move"] {
    cursor: move;
  }
  .esri-view-surface[data-cursor="pointer"] {
    cursor: pointer;
  }
  .esri-view-surface[data-cursor="progress"] {
    cursor: progress;
  }
  .esri-view-surface[data-cursor="grab"] {
    cursor: move;
    cursor: grab;
    cursor: -moz-grab;
    cursor: -webkit-grab;
  }
  .esri-view-surface[data-cursor="grabbing"] {
    cursor: move;
    cursor: grabbing;
    cursor: -moz-grabbing;
    cursor: -webkit-grabbing;
  }

  .esri-view-surface[data-cursor="n-resize"] {
    cursor: n-resize;
  }

  .esri-view-surface[data-cursor="e-resize"] {
    cursor: e-resize;
  }

  .esri-view-surface[data-cursor="s-resize"] {
    cursor: s-resize;
  }

  .esri-view-surface[data-cursor="w-resize"] {
    cursor: w-resize;
  }

  .esri-view-surface[data-cursor="ne-resize"] {
    cursor: ne-resize;
  }

  .esri-view-surface[data-cursor="nw-resize"] {
    cursor: nw-resize;
  }

  .esri-view-surface[data-cursor="se-resize"] {
    cursor: se-resize;
  }

  .esri-view-surface[data-cursor="sw-resize"] {
    cursor: sw-resize;
  }

  .esri-view-surface[data-cursor="ew-resize"] {
    cursor: ew-resize;
  }

  .esri-view-surface[data-cursor="ns-resize"] {
    cursor: ns-resize;
  }

  .esri-view-surface[data-cursor="nesw-resize"] {
    cursor: nesw-resize;
  }

  .esri-view-surface[data-cursor="nwse-resize"] {
    cursor: nwse-resize;
  }

  .esri-display-object,
  .esri-bitmap {
    position: absolute;
    border: none;
    margin: 0;
    padding: 0; // @dris0000 @juan6600
    // Keep these prefixes?
    -webkit-user-drag: none;
    -webkit-user-select: none;
  }

  .esri-bitmap {
    max-width: none;
    transform-origin: top left;
  }
}
