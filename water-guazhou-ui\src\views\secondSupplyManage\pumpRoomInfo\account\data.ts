import dayjs from 'dayjs';
import XLSX from 'xlsx';
// 只能输入英文,数字,下划线,横线
export const inputControl = (rule, value, callback) => {
  const valid = /^[5A-Za-z0-9_]+$/;
  if (valid.test(value)) {
    callback();
  } else {
    callback(new Error('只能输入英文,数字,下划线字符'));
  }
};

export const waterSupplyTypes = [
  { label: '箱式', value: '箱式' },
  { label: '罐式', value: '罐式' },
  { label: '微型', value: '微型' },
  { label: '其他', value: '其他' }
];

const defalutFields: any = [
  {
    type: 'input',
    label: '泵房编码',
    field: 'code',
    prop: 'code',
    placeholder: '请输入泵房编码',
    rules: [
      { required: true, message: '请输入泵房编码', trigger: 'blur' },
      { validator: inputControl, trigger: 'blur' }
    ]
  },
  {
    type: 'input',
    label: '泵房名称',
    field: 'name',
    prop: 'name',
    placeholder: '请输入泵房名称',
    rules: [{ required: true, message: '请输入泵房名称', trigger: 'blur' }]
  },
  {
    type: 'input',
    label: '泵房简称',
    field: 'nickname',
    prop: 'nickname',
    placeholder: '请输入泵房简称',
    rules: [{ required: true, message: '请输入泵房简称', trigger: 'blur' }]
  },
  {
    type: 'input',
    label: '厂家名称',
    field: 'companyName',
    prop: 'companyName',
    placeholder: '请输入厂家名称',
    rules: [{ required: true, message: '请输入厂家名称', trigger: 'blur' }]
  }
];

const formFields: any = [
  {
    type: 'select',
    label: '供水类型',
    field: 'supplyMethod',
    prop: 'supplyMethod',
    placeholder: '请选择供水类型',
    options: waterSupplyTypes,
    rules: [{ required: true, message: '请选择供水类型', trigger: 'blur' }]
  },
  {
    type: 'input-number',
    label: '水箱个数',
    field: 'waterBoxNum',
    prop: 'supplyMethod',
    placeholder: '请输入水箱个数',
    rules: [{ required: true, message: '请输入水箱个数', trigger: 'blur' }]
  },
  {
    type: 'textarea',
    label: '地址',
    field: 'address',
    prop: 'address',
    placeholder: '请输入地址',
    rules: [{ required: true, message: '请输入地址', trigger: 'blur' }]
  },
  {
    type: 'date',
    label: '安装日期',
    field: 'installDate',
    prop: 'installDate',
    rules: [{ required: true, message: '请输入录入日期', trigger: 'blur' }]
  },
  {
    type: 'input',
    label: '安装人',
    field: 'installUserName',
    prop: 'installUserName',
    placeholder: '请输入安装人',
    rules: [{ required: true, message: '请输入安装人', trigger: 'blur' }]
  },
  {
    type: 'input-number',
    label: '采集频率(分钟)',
    labelWidth: '150px',
    field: 'collectionFrequency',
    prop: 'collectionFrequency',
    placeholder: '请输入采集频率(分钟)',
    rules: [
      { required: true, message: '请输入采集频率(分钟)', trigger: 'blur' }
    ]
  },
  {
    type: 'input-number',
    label: '存储频率(分钟)',
    labelWidth: '120px',
    field: 'storageFrequency',
    prop: 'storageFrequency',
    placeholder: '请输入存储频率(分钟)',
    rules: [
      { required: true, message: '请输入存储频率(分钟)', trigger: 'blur' }
    ]
  }
];

export const moreFormDataFields: any = [
  {
    type: 'select',
    label: '供水类型',
    field: 'supplyMethod',
    placeholder: '请选择泵房编码',
    options: waterSupplyTypes
  },
  {
    type: 'input-number',
    label: '水箱个数',
    field: 'waterBoxNum',
    placeholder: '请输入水箱个数'
  },
  {
    type: 'input',
    label: '安装人',
    field: 'installUserName',
    placeholder: '请输入安装人'
  },
  { type: 'daterange', label: '安装日期', field: 'installDateFrom' },
  {
    type: 'daterange',
    label: '录入日期',
    field: 'fromTime',
    format: 'YYYY-MM-DD'
  },
  {
    type: 'input-number',
    label: '采集频率（分钟）',
    labelWidth: '150px',
    field: 'collectionFrequency',
    placeholder: '请输入采集频率(分钟)'
  },
  {
    type: 'input-number',
    label: '存储频率（分钟）',
    labelWidth: '120px',
    field: 'storageFrequency',
    placeholder: '请输入存储频率(分钟)'
  }
];

export const formDataFields: any = () => {
  let fields = defalutFields;
  fields = fields.concat(formFields) as IFormItem[];
  fields.push({
    type: 'textarea',
    label: '备注',
    field: 'remark',
    prop: 'remark',
    placeholder: '请输入备注'
  });
  return fields;
};

export const accountColumns: any = [
  { label: '泵房编码', prop: 'code', minWidth: 120 },
  { label: '泵房名称', prop: 'name', minWidth: 120 },
  { label: '泵房简称', prop: 'nickname', minWidth: 120 },
  { label: '厂家名称', prop: 'companyName', minWidth: 120 },
  { label: '供水类型', prop: 'supplyMethod', minWidth: 120 },
  { label: '水箱个数', prop: 'waterBoxNum', minWidth: 120 },
  { label: '地址', prop: 'address', minWidth: 120 },
  { label: '安装人', prop: 'installUserName', minWidth: 120 },
  {
    label: '安装日期',
    prop: 'installDate',
    minWidth: 120,
    formatter: (row: any, value: any) => {
      return dayjs(value).format('YYYY-MM-DD');
    }
  },
  {
    label: '录入日期',
    prop: 'createTime',
    minWidth: 120,
    formatter: (row: any, value: any) => {
      return dayjs(value).format('YYYY-MM-DD HH:mm:ss');
    }
  },
  { label: '采集频率（分钟）', prop: 'collectionFrequency', minWidth: 140 },
  { label: '存储频率（分钟）', prop: 'storageFrequency', minWidth: 140 },
  { label: '备注', prop: 'remark', minWidth: 120 }
  // {
  //   minWidth: 120,
  //   label: '施工图纸',
  //   prop: 'constructionFile',
  //   formItemConfig: {
  //     type: 'btn-group',
  //     btns: [
  //       {
  //         perm: true,
  //         text: '查询',
  //         type: 'default',
  //         svgIcon: shallowRef(Edit),
  //         click: row => {
  //           console.log(row)
  //         }
  //       }
  //     ]
  //   }
  // },
  // {
  //   minWidth: 120,
  //   label: '其他文件',
  //   prop: 'otherFile',
  //   formItemConfig: {
  //     type: 'btn-group',
  //     btns: [
  //       {
  //         perm: true,
  //         text: '查询',
  //         type: 'default',
  //         svgIcon: shallowRef(Edit),
  //         click: row => {
  //           console.log('dddd')
  //           refFileUpload.value?.openDialog()
  //         }
  //       }
  //     ]
  //   }
  // }
];

// 供应商上传对应
export const pumpRoomUpload = {
  泵房编码: 'code',
  泵房名称: 'name',
  泵房简称: 'nickname',
  厂家名称: 'companyName',
  水箱个数: 'waterBoxNum',
  供水类型: 'supplyMethod',
  地址: 'address',
  安装日期: 'installDate',
  安装人: 'installUserName',
  '采集频率（分钟）': 'collectionFrequency',
  '存储频率（分钟）': 'storageFrequency',
  备注: 'remark'
};

export const readExcelToJson = (file) => {
  return new Promise((resolve: any) => {
    const reader = new FileReader();
    reader.onload = (e: any) => {
      const data = new Uint8Array(e.target.result);
      const workbook = XLSX.read(data, { type: 'array' });
      // console.log('workbook: ', workbook)
      // 将Excel 第一个sheet内容转为json格式
      const worksheet = workbook.Sheets[workbook.SheetNames[0]];
      const json = XLSX.utils.sheet_to_json(worksheet);
      // console.log('jsonExcel:', json)
      resolve(json);
    };

    reader.readAsArrayBuffer(file.raw);
  });
};
