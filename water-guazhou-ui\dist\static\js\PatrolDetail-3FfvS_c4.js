import{_ as ae}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{d as se,c as F,u as le,r as g,s as ne,a8 as b,b as v,bB as U,a1 as pe,am as ce,o as me,Q as de,g as m,n as d,p as r,q as p,i,bh as G,aB as x,aJ as T,h as ue,F as q,dq as fe,aj as ge,bC as _e,an as H,aq as he,c5 as be,cU as ye,dd as ve,cE as we,bM as Re,C as ke}from"./index-r0dFAfgr.js";import{_ as De}from"./Videor.vue_vue_type_script_setup_true_lang-EsHlP83o.js";import{_ as Ce}from"./InlineForm.vue_vue_type_style_index_0_lang-s-ANlzyw.js";import{c as Fe,P as xe}from"./plan-BLf3nu6_.js";import{g as Te,u as Ie}from"./circuitTaskFormRecord-CjbtiPXk.js";import{P as We}from"./config-C9CMv0E7.js";import{s as Le,b as Pe,g as Oe}from"./FeatureHelper-Da16o0mu.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./MapView-DaoQedLH.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./Point-WxyopZva.js";import"./GraphicsLayer-DTrBRwJQ.js";import{a as Ne,i as Be}from"./QueryHelper-ILO3qZqg.js";import{u as Se}from"./useWaterPoint-Bv0z6ym6.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./geometryEngineBase-BhsKaODW.js";import{u as Ae}from"./useDistrict-B4Fis32p.js";import{g as Ve}from"./config-DqqM5K5L.js";import{w as Ee,u as Me}from"./useCTI-CrDoUkpT.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./project-DUuzYgGl.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./executeForIds-BLdIsxvI.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./area-Bpl-8n1R.js";import"./LayerHelper-Cn-iiqxI.js";import"./pipe-nogVzCHG.js";import"./DateFormatter-Bm9a68Ax.js";import"./OutsideWorkOrder-C6s8joBt.js";import"./index-CpGhZCTT.js";const Ue={class:"detail-basic"},Ge={class:"detail-taskinfo"},qe={class:"detail-response"},He={key:0,class:"detail-response-tabcontent"},$e={class:"left"},je={class:"right"},Qe={class:"right-header"},Je={class:"right-item"},Ke={class:"box overlay-y"},ze={class:"right-item"},Xe={class:"box videos overlay-y"},Ye={class:"right-item"},Ze={class:"box overlay-y"},et={class:"right-item"},tt={class:"box overlay-y"},ot={class:"url"},rt={key:1,class:"detail-form-content"},it=se({__name:"PatrolDetail",props:{row:{},view:{},layerInfo:{}},emits:["row-click"],setup($,{emit:j}){const N=F(),R=F(),k=F(),I=F(),Q=le(),s=$,J=j,a=g({curResponse:"关键点",responses:[],orderTypes:[],formRecords:[]}),K=g({group:[{fields:[{type:"tag",label:"任务名称：",field:"name"},{type:"text",label:"巡检员：",field:"inspector"},{type:"text",label:"共同处理人：",field:"collaborateUserName"},{type:"text",label:"到位距离：",field:"distance",unit:"米"},{type:"text",label:"任务描述：",field:"remark"},{type:"btn-group",btns:[{perm:!1,svgIcon:ne(Re),isTextBtn:!0,click:()=>te()}]}]}],defaultValue:{},labelWidth:100,labelPosition:"right"}),B=g({dataList:[],columns:[{minWidth:120,label:"任务编号",prop:"code"},{minWidth:120,label:"任务名称",prop:"name"},{minWidth:160,label:"开始时间",prop:"beginTime"},{minWidth:160,label:"结束时间",prop:"endTime"},{minWidth:120,label:"关键设备数目",prop:"deviceCount"},{minWidth:120,label:"关键点数目",prop:"keyPointCount"},{minWidth:120,label:"任务状态",prop:"status",formatter:(t,e)=>{var o;return(o=We[e])==null?void 0:o.text}},{minWidth:120,label:"到位状况",prop:"presentState"},{minWidth:120,label:"反馈状况",prop:"fallbackState"}],pagination:{hide:!0}}),l=g({dataList:[],columns:[{label:"设备类型",prop:"name",hidden:b(()=>a.curResponse==="关键点")},{label:"设备编号",prop:"device_type",hidden:b(()=>a.curResponse==="关键点")},{label:"名称",prop:"name",hidden:b(()=>a.curResponse!=="关键点")},{label:"是否到位",prop:"is_settle",formatter:(t,e)=>e==="true"?"是":"否"},{label:"到位日期",prop:"settle_time"},{label:"是否反馈",prop:"is_fallback",formatter:(t,e)=>e==="true"?"是":"否"},{label:"反馈日期",prop:"fallback_time"}],pagination:{align:"right",refreshData:({page:t,size:e})=>{l.pagination.page=t||1,l.pagination.limit=e||20,L()}},handleRowClick:async t=>{l.currentRow=t,J("row-click",t),setTimeout(()=>{z()},500)}}),D=g({dataList:[],columns:[{minWidth:120,label:"巡检地点",prop:"location"},{minWidth:120,label:"巡检位置",prop:"position"},{minWidth:200,label:"检验内容",prop:"content"},{minWidth:100,label:"检验结果",prop:"result",formatter:(t,e)=>({NORMAL:"正常",ABNORMAL:"异常",NOT_CHECKED:"未检查"})[e]||e},{minWidth:150,label:"结果描述",prop:"resultDescription"},{minWidth:120,label:"检验时间",prop:"checkTime",formatter:(t,e)=>e?new Date(e).toLocaleString():"-"}],operationWidth:120,operations:[{perm:!0,text:"填写",type:"primary",click:t=>ee(t)}],pagination:{hide:!0}}),W=Se("viewDiv"),z=async()=>{var e,o,n,_;if(!a.curResponse)return;const t=l.currentRow.deviceType;if(t!==void 0){if(a.curResponse==="关键点")w.extentTo("point",t),W.removeAll();else if(a.curResponse==="设备"){const h=(o=(e=s.layerInfo)==null?void 0:e.find(C=>C.layername===l.currentRow.name))==null?void 0:o.layerid;if(h===void 0)return;const u=await Ne(window.SITE_CONFIG.GIS_CONFIG.gisService+window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService+"/"+h,Be({where:"OBJECTID="+t})),y=(n=u==null?void 0:u.features)==null?void 0:n[0];if(!y)return;y.symbol=Le(y.geometry.type,{color:[0,0,0,0],outlineWidth:2,outlineColor:[0,0,0,0]}),W.removeAll(),w.removeHighlight(),W.add(s.view,{id:t,point:Pe(y.geometry)}),Oe(s.view,(_=u==null?void 0:u.features)==null?void 0:_[0],{avoidHighlight:!0})}}},S=b(()=>{var t,e;return((e=(t=l.currentRow)==null?void 0:t.image)==null?void 0:e.split(","))||[]}),X=b(()=>{var t,e;return((e=(t=l.currentRow)==null?void 0:t.image)==null?void 0:e.split(","))||[]}),Y=b(()=>{var t,e;return((e=(t=l.currentRow)==null?void 0:t.audio)==null?void 0:e.split(","))||[]}),Z=b(()=>{var t,e;return((e=(t=l.currentRow)==null?void 0:t.file)==null?void 0:e.split(","))||[]}),L=()=>{a.curResponse==="巡检表单"?P():Fe({taskCode:s.row.code,page:l.pagination.page||1,size:l.pagination.limit||20,type:a.curResponse}).then(t=>{var e,o,n,_,h;a.responses=(e=t.data.data)==null?void 0:e.data,l.dataList=((n=(o=t.data)==null?void 0:o.data)==null?void 0:n.data)||[],l.pagination.total=((h=(_=t.data)==null?void 0:_.data)==null?void 0:h.total)||0})},P=()=>{var t;(t=s.row)!=null&&t.id&&Te(s.row.id).then(e=>{var n;const o=((n=e.data)==null?void 0:n.data)||[];a.formRecords=Array.isArray(o)?o:[],D.dataList=a.formRecords,o.length>0?console.log("成功获取表单记录:",o.length,"条"):console.log("未获取到表单记录数据")}).catch(e=>{console.error("获取表单记录失败:",e),a.formRecords=[],D.dataList=[],v.error("获取表单记录失败")})},ee=t=>{var e;V.defaultValue={id:t.id,location:t.location,position:t.position,content:t.content,result:t.result,resultDescription:t.resultDescription,attachments:t.attachments},(e=k.value)==null||e.openDialog(),U(()=>{var o;(o=k.value)==null||o.resetForm()})},te=async()=>{var t,e;(t=R.value)==null||t.openDialog(),A.defaultValue={...s.row||{}},await U(),(e=R.value)==null||e.resetForm()},A=g({dialogWidth:500,title:"描述",labelPosition:"top",group:[{fields:[{type:"textarea",field:"remark",maxRow:10,minRow:10}]}],submit:t=>{var e;console.log(t),(e=R.value)==null||e.closeDialog()}}),oe=g({dialogWidth:450,labelPosition:"right",title:"事件上报",group:[{fields:[{rules:[{required:!0,message:"请输入工单标题"}],type:"input",label:"工单标题",field:"title"},{rules:[{required:!0,message:"请选择紧急程度"}],type:"select",label:"紧急程度",field:"level",options:Ve()},{rules:[{required:!0,message:"请选择工单类型"}],type:"select-tree",label:"工单类型",field:"type",async autoFillOptions(t){var e;try{const o=await Ee({isDel:0});t.options=pe(((e=o==null?void 0:o.data)==null?void 0:e.data)||[],{label:"name",value:"id",children:"children",id:"id"})}catch{}}},{type:"textarea",label:"描述",field:"remark"}]}],defaultValue:{},submit:t=>{xe({...t,pointId:l.currentRow.id}).then(e=>{var o;e.data.code===200?(v.success(e.data.message),(o=N.value)==null||o.closeDialog()):v.error(e.data.message)}).catch(e=>{console.log(e),v.error("系统错误")})}}),V=g({dialogWidth:600,title:"填写巡检表单",labelPosition:"right",labelWidth:100,group:[{fields:[{type:"input",label:"巡检地点",field:"location",readonly:!0},{type:"input",label:"巡检位置",field:"position",readonly:!0},{type:"textarea",label:"检验内容",field:"content",readonly:!0,maxRow:3,minRow:3},{type:"radio",label:"检验结果",field:"result",rules:[{required:!0,message:"请选择检验结果"}],options:[{label:"正常",value:"NORMAL"},{label:"异常",value:"ABNORMAL"}]},{type:"textarea",label:"结果描述",field:"resultDescription",maxRow:4,minRow:4},{type:"upload",label:"附件",field:"attachments",multiple:!0,accept:".jpg,.jpeg,.png,.pdf,.doc,.docx"}]}],defaultValue:{},submit:async t=>{var e,o;try{await Ie(t.id,{result:t.result,resultDescription:t.resultDescription,attachments:t.attachments,checkUserId:((e=Q.userInfo)==null?void 0:e.id)||""}),v.success("保存成功"),(o=k.value)==null||o.closeDialog(),P()}catch(n){console.error("保存失败:",n),v.error("保存失败")}}}),w=Ae("viewDiv");ce(()=>s.row,()=>{E()});const E=()=>{var t;s.row&&(a.formRecords=[],D.dataList=[],L(),P(),B.dataList=[s.row],I.value&&(I.value.dataForm={name:s.row.name,inspector:s.row.receiveUserName,collaborateUserName:s.row.collaborateUserName,presentDistance:s.row.presentDistance,remark:s.row.remark},w.removeAll(),w.add(s.view,(t=s.row)==null?void 0:t.districtAreaId,{ratio:1,highlight:!1,showKeyPoint:!0})))},{getOrderTypeOption:re}=Me(),ie=async()=>{a.orderTypes=await re()};return me(()=>{E(),ie()}),de(()=>{w.destroy()}),(t,e)=>{var M;const o=Ce,n=he,_=be,h=ye,u=De,y=ve,C=we,O=ae;return m(),d("div",null,[r("div",Ue,[p(o,{ref_key:"refFormBasic",ref:I,config:i(K)},null,8,["config"])]),r("div",Ge,[e[1]||(e[1]=r("div",{class:"detail-group-title"}," 任务详情 ",-1)),p(n,{config:i(B)},null,8,["config"])]),r("div",qe,[e[6]||(e[6]=r("p",{class:"detail-group-title"}," 反馈信息 ",-1)),p(_,{modelValue:i(a).curResponse,"onUpdate:modelValue":e[0]||(e[0]=c=>i(a).curResponse=c),config:{type:"tabs",tabs:[{label:"关键点",value:"关键点"},{label:"设备",value:"设备"},{label:"专项设备",value:"专项设备"},{label:"巡检表单",value:"巡检表单"}]},onChange:L},null,8,["modelValue"]),i(a).curResponse!=="巡检表单"?(m(),d("div",He,[r("div",$e,[p(n,{config:i(l)},null,8,["config"])]),r("div",je,[r("div",Qe,[r("span",null,"媒体信息 - "+G((M=i(l).currentRow)==null?void 0:M.name),1)]),r("div",Je,[e[2]||(e[2]=r("span",null,"图片",-1)),r("div",Ke,[(m(!0),d(x,null,T(i(S),(c,f)=>(m(),ue(h,{key:f,fit:"cover",src:c,alt:c,"preview-src-list":i(S),"initial-index":f},null,8,["src","alt","preview-src-list","initial-index"]))),128))])]),r("div",ze,[e[3]||(e[3]=r("span",null,"视频",-1)),r("div",Xe,[(m(!0),d(x,null,T(i(X),(c,f)=>(m(),d("div",{key:f,class:"video-box"},[p(u,{class:"video",url:c},null,8,["url"])]))),128))])]),r("div",Ye,[e[4]||(e[4]=r("span",null,"音频",-1)),r("div",Ze,[(m(!0),d(x,null,T(i(Y),(c,f)=>(m(),d("div",{key:f,class:"audio-item"},[p(y,{url:c,"show-url":!0,download:!1},null,8,["url"])]))),128))])]),r("div",et,[e[5]||(e[5]=r("span",null,"附件",-1)),r("div",tt,[(m(!0),d(x,null,T(i(Z),(c,f)=>(m(),d("div",{key:f,class:"file-item"},[p(C,null,{default:q(()=>[p(i(fe))]),_:1}),r("span",ot,G(c),1),p(C,{class:"download-icon"},{default:q(()=>[p(i(ge),{onClick:at=>i(_e)(c)},null,8,["onClick"])]),_:2},1024)]))),128))])])])])):H("",!0),i(a).curResponse==="巡检表单"?(m(),d("div",rt,[p(n,{config:i(D)},null,8,["config"])])):H("",!0)]),p(O,{ref_key:"refDialogForm",ref:R,config:i(A)},null,8,["config"]),p(O,{ref_key:"refDialogFormReport",ref:N,config:i(oe)},null,8,["config"]),p(O,{ref_key:"refDialogFormRecord",ref:k,config:i(V)},null,8,["config"])])}}}),Bo=ke(it,[["__scopeId","data-v-807a1e36"]]);export{Bo as default};
