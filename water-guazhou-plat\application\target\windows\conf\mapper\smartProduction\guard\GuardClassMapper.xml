<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartProduction.guard.GuardClassMapper">
    <sql id="Base_Column_List">
        <!--@sql select-->
        a.id,
        a.name,
        a.serial_no,
        a.begin_time,
        a.end_time,
        a.place_id,
        a.update_time,
        a.update_user_id,
        a.creator,
        a.create_time,
        a.tenant_id
        <!--@sql from guard_class -->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.smartProduction.guard.GuardClass">
        <result column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="serial_no" property="serialNo"/>
        <result column="begin_time" property="beginTime"/>
        <result column="end_time" property="endTime"/>
        <result column="place_id" property="placeId"/>
        <result column="placeName" property="placeName"/>
        <result column="update_user_id" property="updateUserId"/>
        <result column="update_time" property="updateTime"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>
    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>, b.address as placeName
        from guard_class a
        left join guard_place b on a.place_id = b.id
        <where>
            <if test="name != null and name != ''">
                and a.name like '%'|| #{name} ||'%'
            </if>
            <if test="placeId != null and placeId != ''">
                and a.place_id = #{placeId}
            </if>
            and a.tenant_id = #{tenantId}
        </where>
        ORDER BY serial_no
    </select>

    <update id="updateFully">
        update guard_class
        set name           = #{name},
            serial_no      = #{serialNo},
            begin_time     = #{beginTime},
            end_time       = #{endTime},
            place_id       = #{placeId},
            update_user_id = #{updateUserId},
            update_time    = #{updateTime}
        where id = #{id}
    </update>
</mapper>