package org.thingsboard.server.dao.stationSetting;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.request.GisProjectListRequest;
import org.thingsboard.server.dao.model.request.StationCategoryListRequest;
import org.thingsboard.server.dao.model.sql.gis.GisProject;
import org.thingsboard.server.dao.model.sql.stationSetting.StationCategory;
import org.thingsboard.server.dao.sql.stationSetting.StationCategoryMapper;

import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class StationCategoryServiceImpl implements StationCategoryService {

    @Autowired
    private StationCategoryMapper stationCategoryMapper;


    @Override
    public void save(StationCategory entity) {
        if (StringUtils.isBlank(entity.getId())) {
            entity.setCreateTime(new Date());
            stationCategoryMapper.insert(entity);
        } else {
            stationCategoryMapper.updateById(entity);
        }
    }

    @Override
    public PageData<StationCategory> findList(StationCategoryListRequest request, TenantId tenantId) {
        Page<StationCategory> pageRequest = new Page<>(request.getPage(), request.getSize());
        request.setTenantId(UUIDConverter.fromTimeUUID(tenantId.getId()));

        IPage<StationCategory> pageResult = stationCategoryMapper.findList(pageRequest, request);

        return new PageData<>(pageResult.getTotal(), pageResult.getRecords());
    }

    @Override
    public void remove(List<String> ids) {
        stationCategoryMapper.deleteBatchIds(ids);
    }

    @Override
    public List<StationCategory> findAll(TenantId tenantId) {
        QueryWrapper<StationCategory> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("tenant_id", UUIDConverter.fromTimeUUID(tenantId.getId()));
        return stationCategoryMapper.selectList(queryWrapper);
    }

}
