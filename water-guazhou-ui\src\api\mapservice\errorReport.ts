import { request } from '@/plugins/axios'

/**
 * 查询上报列表
 * @param params
 * @returns
 */
export const GetErrorReportList = (
  params: any|IQueryPagerParams & {
    status?: string
    uploadUser?: string
    beginTime?: string
    endTime?: string
  }
) => {
  return request({
    url: '/api/gis/exceptionUpload/list',
    method: 'get',
    params
  })
}

/**
 * 错误属性上报
 * @param params
 * @returns
 */
export const AddMapErrorUpload = (params: {
  layer: string
  fid: string
  remark: string
  uploadContent: string
}) => {
  return request({
    url: '/api/gis/exceptionUpload/save',
    method: 'post',
    data: params
  })
}
/**
 * 处理错误
 * @param params
 * @returns
 */
export const PorcessMapErrorUpload = (params: {
  id: string
  status: string
  remark?: string
}) => {
  return request({
    url: '/api/gis/exceptionUpload/approval',
    method: 'post',
    data: params
  })
}

/**
 * 删除管网错误属性上报记录
 * @param ids
 * @returns
 */
export const DeleteMapErrorRecords = (ids: string[]) => {
  return request({
    url: '/api/gis/exceptionUpload/remove',
    method: 'delete',
    data: ids
  })
}
