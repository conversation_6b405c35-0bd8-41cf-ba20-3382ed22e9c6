package org.thingsboard.server.controller.smartManagement;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.gis.UserCoordinateService;
import org.thingsboard.server.dao.model.sql.smartManagement.UserCoordinate;
import org.thingsboard.server.dao.model.sql.smartManagement.UserCoordinateGroup;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.DestUserCoordinatePageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.UserCoordinatePageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.UserCoordinateSaveRequest;
import org.thingsboard.server.utils.imodel.annotations.IStarController;

/**
 * 用户坐标
 */
@IStarController
@RequestMapping("/api/userCoordinate")
public class UserCoordinateController extends BaseController {
    @Autowired
    private UserCoordinateService service;

    @GetMapping
    public IPage<UserCoordinate> findDestUserAllConditional(DestUserCoordinatePageRequest request) {
        return service.findDestUserAllConditional(request);
    }

    @GetMapping("/multiUser")
    public IPage<UserCoordinateGroup> findDestUserAllConditional(UserCoordinatePageRequest request) {
        return service.findAllConditional(request);
    }

    @GetMapping("/newest")
    public IPage<UserCoordinate> newest(UserCoordinatePageRequest request) {
        return service.findNewest(request.ignorePage());
    }

    // @GetMapping
    // public IPage<UserCoordinateGroup> findAllConditional(UserCoordinatePageRequest request) {
    //     return service.findAllConditional(request);
    // }

    @PostMapping
    public UserCoordinate save(@RequestBody UserCoordinateSaveRequest req) {
        return service.save(req);
    }

    // @PatchMapping("/{id}")
    public boolean edit(@RequestBody UserCoordinateSaveRequest req, @PathVariable String id) {
        return service.update(req.unwrap(id));
    }

    // @DeleteMapping("/{id}")
    public boolean delete(@PathVariable String id) {
        return service.delete(id);
    }
}