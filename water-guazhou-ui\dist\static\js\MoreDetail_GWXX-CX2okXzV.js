import{_ as P}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{z as g,d as x,c as _,r as c,b as r,S as k,o as S,g as v,n as C,q as d,i as f,aB as I,aq as L,C as M}from"./index-r0dFAfgr.js";import{_ as W}from"./Search-NSrhrIa_.js";const X=o=>g({url:"/api/spp/dma/partition/partitionPipe/list",method:"get",params:o}),q=o=>g({url:"/api/spp/dma/partition/partitionPipe",method:"post",data:o}),B=o=>g({url:"/api/spp/dma/partition/partitionPipe",method:"delete",data:o}),F=x({__name:"MoreDetail_GWXX",props:{partition:{}},setup(o){const u=o,b=_(),p=_(),h=c({filters:[],operations:[{type:"btn-group",labelWidth:1,btns:[{perm:!0,iconifyIcon:"ep:circle-plus",text:"新增",type:"success",click:()=>m()}]}]}),i=c({dataList:[],columns:[{label:"进水主管",prop:"mainPipe"},{label:"管材",prop:"pipe"},{label:"管长",prop:"length"},{label:"铺设年限",prop:"year"},{label:"备注",prop:"remark"},{label:"管网文件",prop:"file"}],pagination:{refreshData:({page:e,size:t})=>{i.pagination.page=e,i.pagination.limit=t,s()}},operations:[{perm:!0,text:"编辑",iconifyIcon:"ep:edit",click:e=>m(e)},{perm:!0,text:"删除",iconifyIcon:"ep:delete",type:"danger",click:e=>y(e)}]}),s=async()=>{var e;i.loading=!0;try{const a=(await X({partitionId:(e=u.partition)==null?void 0:e.value,page:i.pagination.page||1,size:i.pagination.limit||20})).data.data||{};i.dataList=a.data||[],i.pagination.total=a.total||0}catch{}i.loading=!1},m=e=>{var t;n.defaultValue={...e||{}},n.title=e?"编辑管网信息":"添加管网信息",(t=p.value)==null||t.openDialog()},y=e=>{const t=e?[e.id]:[];if(!t.length){r.error("请选择要删除的数据");return}k("确定删除?","提示信息").then(async()=>{try{const a=await B(t);a.data.code===200?(r.success("删除成功"),s()):r.error(a.data.message)}catch{r.error("删除失败")}}).catch(()=>{})},n=c({title:"添加流量表",dialogWidth:600,labelPosition:"right",group:[{fields:[{lg:24,xl:12,type:"input",label:"进水主管",field:"mainPipe"},{lg:24,xl:12,type:"input",label:"管材",field:"pipe"},{lg:24,xl:12,type:"input-number",label:"管长",field:"length"},{lg:24,xl:12,type:"year",label:"铺设年限",field:"year"},{type:"textarea",label:"备注",field:"remark"},{type:"file",label:"管网文件",field:"file"}]}],submit:async e=>{var t,a;n.submitting=!0;try{const l=await q({...e,partitionId:(t=u.partition)==null?void 0:t.value});l.data.code===200?(r.success("提交成功"),s(),(a=p.value)==null||a.closeDialog()):r.error(l.data.message)}catch{r.error("提交失败")}n.submitting=!1}});return S(()=>{s()}),(e,t)=>{const a=W,l=L,D=P;return v(),C(I,null,[d(a,{ref_key:"refSearch",ref:b,config:f(h),class:"search"},null,8,["config"]),d(l,{config:f(i),class:"table-box"},null,8,["config"]),d(D,{ref_key:"refDialog",ref:p,config:f(n)},null,8,["config"])],64)}}}),T=M(F,[["__scopeId","data-v-821aa6c6"]]);export{T as default};
