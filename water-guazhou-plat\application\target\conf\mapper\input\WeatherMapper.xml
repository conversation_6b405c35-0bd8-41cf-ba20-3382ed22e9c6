<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.input.WeatherMapper">

    <select id="findList" resultType="org.thingsboard.server.dao.model.sql.Weather">
        SELECT
        A.*
        FROM
        tb_weather A
        <where>
            <if test="tenantId != null and tenantId != ''">
                AND a.tenant_id = #{tenantId}
            </if>
            <if test="time != null and time != ''">
                AND a.time = #{time}
            </if>
        </where>
        ORDER BY a.create_time DESC
    </select>


</mapper>