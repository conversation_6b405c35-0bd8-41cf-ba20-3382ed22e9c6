{"compilerOptions": {"forceConsistentCasingInFileNames": true, "target": "ESNext", "useDefineForClassFields": true, "module": "ESNext", "moduleResolution": "Node", "strict": true, "jsx": "preserve", "sourceMap": true, "resolveJsonModule": true, "isolatedModules": true, "esModuleInterop": true, "skipLibCheck": true, "allowSyntheticDefaultImports": true, "noImplicitAny": false, "importHelpers": true, "allowJs": true, "baseUrl": ".", "experimentalDecorators": true, "outDir": "dist", "types": ["vite/client", "unplugin-icons/types/vue"], "lib": ["ESNext", "DOM"], "paths": {"@/*": ["./src/*"], "~@/*": ["./src/*"]}}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "./auto-imports.d.ts", "./components.d.ts"], "exclude": ["src/assets", "node_modules"]}