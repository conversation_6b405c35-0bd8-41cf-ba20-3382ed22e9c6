<template>
  <div
    id="gis-overview"
    class="esri-widget esri-expand esri-component esri-widget--button custom-toolbar"
    :title="'鹰眼图'"
    @click="() => (state.showOverViewMap = !state.showOverViewMap)"
  >
    <el-icon
      :size="16"
      class="tool-icon"
    >
      <Icon
        :icon="state.showOverViewMap ? 'ep:d-arrow-right' : 'mdi:earth'"
      ></Icon>
    </el-icon>
    <teleport to="body">
      <div
        v-show="state.showOverViewMap"
        id="overviewmap"
        class="overviewmap"
      ></div>
    </teleport>
  </div>
</template>
<script lang="ts" setup>
import { Icon } from '@iconify/vue'
import { onBeforeRouteLeave } from 'vue-router'
import { useOverViewMap, useWidgets } from '@/hooks/arcgis'

const { addCustomWidget } = useWidgets()
const view: __esri.MapView | undefined = inject('view')
const state = reactive<{ showOverViewMap: boolean; mounted: boolean }>({
  showOverViewMap: false,
  mounted: false
})
const overViewMap = useOverViewMap()
onMounted(() => {
  view?.when(() => {
    overViewMap.init(view, 'overviewmap')
    addCustomWidget(view, 'gis-overview', 'bottom-right')
  })
})
onBeforeRouteLeave(() => {
  state.showOverViewMap = false
})
</script>
<style lang="scss" scoped>
.overviewmap {
  width: 240px;
  height: 120px;
  position: absolute;
  right: 70px;
  bottom: 15px;
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.5);
}
</style>
