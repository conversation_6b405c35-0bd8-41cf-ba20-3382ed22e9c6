export const PayMeTypes = [
  { label: '柜台现金', value: 53 },
  { label: '微信扫码', value: 54 },
  { label: '微信公众号', value: 55 },
  { label: '微信付款码', value: 56 },
  { label: '支付宝付款码', value: 57 },
  { label: '支付宝扫码', value: 58 },
  { label: 'Pos机刷卡', value: 59 },
  { label: '现金支付', value: 60 }
]
/**
 * 导出报表
 * @param data
 */
export const ExportReport = (data: any, name?: string) => {
  const url = window.URL.createObjectURL(data)
  const link = document.createElement('a')
  link.style.display = 'none'
  link.href = url
  link.setAttribute('download', `${name || '统计报表'}.xlsx`)
  document.body.appendChild(link)
  link.click()
}
/**
 * 下载指定路径
 * @param url
 * @param name
 */
export const downloadUrl = (url?: string, name?: string) => {
  if (!url) return
  const link = document.createElement('a')
  link.style.display = 'none'
  link.href = url
  // link.href = 'http://124.112.49.66:9950/user_template.xlsx'
  // link.href = 'http://139.155.92.140:8999/group1/M00/00/00/rBsADGDxGLOAH0F1AAAnsQ1RlwA63.xlsx'
  // link.href = 'http://139.155.92.140:29999/group1/M00/00/00/rBsADGDxGLOAH0F1AAAnsQ1RlwA63.xlsx'
  link.setAttribute('download', name || '下载')
  document.body.appendChild(link)
  link.click()
}
