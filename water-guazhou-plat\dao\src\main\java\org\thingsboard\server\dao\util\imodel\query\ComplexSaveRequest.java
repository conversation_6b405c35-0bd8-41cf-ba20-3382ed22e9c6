package org.thingsboard.server.dao.util.imodel.query;

import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

public abstract class ComplexSaveRequest<T, U extends SaveRequest<?>> extends SaveRequest<T> {
    private List<U> items;

    @JsonIgnore
    @Deprecated
    private List<String> remove;

    public List<U> getItems(String parentId) {
        boolean isInsert = getId() == null;
        if (items != null)
            for (U item : items) {
                if (isInsert)
                    item.setId(null);

                parentSetter().accept(item, parentId);
            }

        return items;
    }

    public List<String> getItemIdList() {
        if (items.isEmpty())
            return Collections.emptyList();
        return items.stream().map(x -> x.getId()).collect(Collectors.toList());
    }

    protected boolean hasItems() {
        return items != null && items.size() > 0;
    }

    protected String checkItemExistence(String errTips) {
        if (hasItems())
            return null;
        return errTips;
    }

    protected abstract StringSetter<U> parentSetter();

    public void setItems(List<U> items) {
        this.items = items;
    }

    @Deprecated
    public List<String> getRemove() {
        if (getId() == null)
            return null;
        return remove;
    }

    @Deprecated
    public void setRemove(List<String> remove) {
        this.remove = remove;
    }

}
