package org.thingsboard.server.dao.sql.smartService.portal;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.smartService.portal.SsPortalVideo;
import org.thingsboard.server.dao.util.imodel.query.smartService.portal.SsPortalVideoPageRequest;

@Mapper
public interface SsPortalVideoMapper extends BaseMapper<SsPortalVideo> {

    IPage<SsPortalVideo> getList(@Param("param") SsPortalVideoPageRequest request);
}
