package org.thingsboard.server.dao.model.sql.dma;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;
import java.util.Date;

/**
 * DMA分析
 */
@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.DMA_NIGHT_MIN_FLOW_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class DmaNightMinFlowEntity {
    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.DMA_NIGHT_MIN_FLOW_PARTITION_ID)
    private String partitionId;

    private transient String partitionName;

    @Column(name = ModelConstants.DMA_NIGHT_MIN_FLOW_IN_FLOW)
    private Double inFlow;

    @Column(name = ModelConstants.DMA_NIGHT_MIN_FLOW_OUT_FLOW)
    private Double outFlow;

    @Column(name = ModelConstants.DMA_NIGHT_MIN_FLOW_BACKGROUND_WATER_LEAKAGE)
    private Double backgroundWaterLeakage;

    @Column(name = ModelConstants.DMA_NIGHT_MIN_FLOW_NIGHT_MIN_FLOW)
    private Double nightMinFlow;

    @Column(name = ModelConstants.DMA_NIGHT_MIN_FLOW_NIGHT_LAWFUL_USE_TOTAL)
    private Double nightLawfulUseTotal;

    @Column(name = ModelConstants.DMA_NIGHT_MIN_FLOW_BROKEN_PIPE_LEAKAGE_TOTAL)
    private Double brokenPipeLeakageTotal;

    @Column(name = ModelConstants.DMA_NIGHT_MIN_FLOW_CREATE_TIME)
    private Date createTime;

    public DmaNightMinFlowEntity(DmaNightMinFlowEntity a, OldDmaPartitionEntity b) {
        this.id = a.id;
        this.partitionId = a.partitionId;
        this.partitionName = b.getName();
        this.inFlow = a.inFlow;
        this.outFlow = a.outFlow;
        this.backgroundWaterLeakage = a.backgroundWaterLeakage;
        this.nightMinFlow = a.nightMinFlow;
        this.nightLawfulUseTotal = a.nightLawfulUseTotal;
        this.brokenPipeLeakageTotal = a.brokenPipeLeakageTotal;
        this.createTime = a.createTime;
    }
}
