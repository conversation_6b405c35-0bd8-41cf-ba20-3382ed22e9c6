package org.thingsboard.server.controller.smartPipe;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.request.PartitionMountRequest;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.PartitionMount;
import org.thingsboard.server.dao.smartPipe.PartitionMountService;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.List;

/**
 * 智慧管网-DMA分区挂接
 */
@RestController
@RequestMapping("api/spp/dma/partition/mount")
public class PartitionMountController extends BaseController {

    @Autowired
    private PartitionMountService partitionMountService;

    @PostMapping
    public IstarResponse save(@RequestBody PartitionMount partitionMount) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        partitionMount.setTenantId(tenantId);

        return IstarResponse.ok(partitionMountService.save(partitionMount));
    }

    @GetMapping("list")
    public IstarResponse getList(PartitionMountRequest request) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        request.setTenantId(tenantId);
        return IstarResponse.ok(partitionMountService.getList(request));
    }

    @PostMapping("batchSave")
    public IstarResponse batchSave(@RequestBody List<PartitionMount> partitionMountList) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());

        return IstarResponse.ok(partitionMountService.batchSave(partitionMountList, tenantId));
    }

    @DeleteMapping
    public IstarResponse delete(@RequestBody List<String> ids) {
        partitionMountService.delete(ids);
        return IstarResponse.ok("移出成功");
    }

    @PostMapping("changeDirection/{id}")
    public IstarResponse changeDirection(@PathVariable String id) {
        return IstarResponse.ok(partitionMountService.changeDirection(id));
    }

}
