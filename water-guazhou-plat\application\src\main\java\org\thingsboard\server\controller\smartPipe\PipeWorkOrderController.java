package org.thingsboard.server.controller.smartPipe;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.request.PipeWorkOrderRequest;
import org.thingsboard.server.dao.smartPipe.PipeWorkOrderService;
import org.thingsboard.server.dao.util.imodel.query.workOrder.PipeWorkOrderSaveRequest;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;
import org.thingsboard.server.utils.imodel.annotations.IStarController;

import java.util.List;

/**
 * 智慧管网-漏损工单
 */
@IStarController
@RequestMapping("api/spp/dma/workOrder")
public class PipeWorkOrderController extends BaseController {

    @Autowired
    private PipeWorkOrderService pipeWorkOrderService;

    @PostMapping
    public IstarResponse save(@RequestBody PipeWorkOrderSaveRequest pipeWorkOrderSaveRequest) throws ThingsboardException {
        return IstarResponse.ok(pipeWorkOrderService.save(pipeWorkOrderSaveRequest));
    }

    @GetMapping("list")
    public IstarResponse getList(PipeWorkOrderRequest request) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        request.setTenantId(tenantId);
        return IstarResponse.ok(pipeWorkOrderService.getList(request));
    }

    @DeleteMapping
    public IstarResponse delete(@RequestBody List<String> ids) {
        pipeWorkOrderService.delete(ids);
        return IstarResponse.ok("删除成功");
    }
}
