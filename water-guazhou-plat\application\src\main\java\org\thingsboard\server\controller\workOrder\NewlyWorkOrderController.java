package org.thingsboard.server.controller.workOrder;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.fault.FaultReportService;
import org.thingsboard.server.dao.model.DTO.WorkOrderVisitMsgDTO;
import org.thingsboard.server.dao.model.sql.workOrder.*;
import org.thingsboard.server.dao.orderWork.NewlyWorkOrderService;
import org.thingsboard.server.dao.util.imodel.StringUtils;
import org.thingsboard.server.dao.util.imodel.query.smartService.SendVisitMsgRequest;
import org.thingsboard.server.dao.util.imodel.query.smartService.WorkOrderListMsgRequest;
import org.thingsboard.server.dao.util.imodel.query.workOrder.*;
import org.thingsboard.server.dao.util.imodel.response.model.IModel;
import org.thingsboard.server.utils.ExcelUtil;
import org.thingsboard.server.utils.imodel.annotations.IStarController;

import javax.servlet.http.HttpServletResponse;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import static org.thingsboard.server.dao.model.sql.workOrder.WorkOrderStage.PROCESSED;
import static org.thingsboard.server.dao.model.sql.workOrder.WorkOrderStatus.*;

@RequestMapping("api/workOrder")
@IStarController
@SuppressWarnings("deprecation")
public class NewlyWorkOrderController extends BaseController {
    @Autowired
    private NewlyWorkOrderService workOrderService;

    @Autowired
    private FaultReportService faultReportService;

    @GetMapping
    public IPage<WorkOrder> getByPage(IModel model, WorkOrderPageRequest request) {
        return workOrderService.findWorkOrderByPage(request);
    }

    @GetMapping("visit/list")
    public IPage<WorkOrderVisitMsgDTO> getByVisitList(IModel model, WorkOrderListMsgRequest request) {
        return workOrderService.getVisitList(request);
    }

    @GetMapping("visit/listExport")
    public IModel getByVisitListExport(IModel model, SendVisitMsgRequest request, HttpServletResponse response) {
        List<WorkOrderVisitMsgDTO> workOrderMsgList = workOrderService.getWorkOrderMsgList(request);

        // 数据列表
        Map headMap = new LinkedHashMap();
        headMap.put("serialNo", "工单编号");
        headMap.put("title", "标题");
        headMap.put("source", "来源");
        headMap.put("name", "类型");
        headMap.put("priceName", "紧急程度");
        headMap.put("organizerName", "发起人");
        headMap.put("createTime", "发起时间");
        headMap.put("processUserName", "处理人");
        headMap.put("processLevelDisplayName", "处理级别");
        headMap.put("estimatedFinishTime", "预计完成时间");
        headMap.put("completeTime", "完成时间");
        headMap.put("updateTime", "最后更新时间");
        headMap.put("submitRemark", "完工备注");
        headMap.put("approvedRemark", "审核结果");
        headMap.put("sendTime", "发送时间");
        headMap.put("sendStatusName", "发送状态");
        headMap.put("visitTime", "回复时间");
        headMap.put("evaluateName", "满意度");
        headMap.put("sendRemark", "备注");
        headMap.put("sendTime", "发送时间");
        String title = "工单回访列表";


        JSONArray dataList = JSONArray.parseArray(JSONObject.toJSONString(workOrderMsgList));
        for (Object o : dataList) {
            JSONObject o1 = (JSONObject) o;
            switch (o1.getString("status")) {
                case "0":
                    o1.put("status", "未发送");
                    break;
                case "1":
                    o1.put("status", "发送成功");
                    break;
                case "2":
                    o1.put("status", "发送失败");
                    break;
            }
        }

        ExcelUtil.exportExcelX(title, headMap, dataList, "yyyy-MM-dd HH:mm:ss", 20, false, response);

        return model.success();
    }

    @GetMapping("/my")
    public IPage<WorkOrder> getByPageOfMe(IModel model, WorkOrderPageRequest request) {
        String ownerId = request.currentUserUUID();
        if (ownerId == null) {
            model.failure("无效的验证信息");
        }
        request.setOwnerId(ownerId);
        return workOrderService.findWorkOrderByPage(request);
    }

    @GetMapping("/{id}")
    public WorkOrder getOrder(IModel model, @PathVariable String id) {
        WorkOrder workOrder = workOrderService.find(id);
        return workOrder;
    }

    @GetMapping("/{id}/stages")
    public List<WorkOrderDetail> getAllStage(IModel model, @PathVariable String id) {
        List<WorkOrderDetail> stages = workOrderService.findStages(id);
        return stages;
    }

    @PostMapping("/{id}/assign")
    public IModel assign(IModel model, @PathVariable String id, @RequestBody WorkOrderAssignRequest request) {
        model.failureIfNot("工单不存在或无法指派非派单状态的工单", workOrderService.isStatus(id, PENDING));
        workOrderService.assign(id, request);
        return model.success();
    }

    @PostMapping("/{id}/reassign")
    public IModel reassign(IModel model, @PathVariable String id, @RequestBody WorkOrderReassignRequest request) {
        WorkOrderStatus status = workOrderService.getStatus(id);
        model.failureIf("正在退单中，无法重新指派", status.equals(CHARGEBACK_REVIEW));
        model.failureIfNot("工单正在处理中或正在审核中，当前状态不可重新指派", PROCESSED.contains(status) || WorkOrderStage.REVIEW.contains(status));
        model.failureIf("当前用户即目标用户", workOrderService.isUserProcess(id, request.getStepProcessUserId()));

        workOrderService.reassign(id, request);
        return model.success();
    }

    @PostMapping("/{id}/handoverRequest")
    public IModel handoverRequest(IModel model, @PathVariable String id, @RequestBody WorkOrderHandoverStageRequest request) {

        WorkOrderStatus status = workOrderService.getStatus(id);
        model.failureIfNot("工单当前状态不可进行转单操作", status.between(RESOLVING, REJECTED));

        String stepProcessUserId = workOrderService.getStepProcessUserId(id);
        try {
            model.failureIfNot("仅当前步骤处理人可申请转单", stepProcessUserId.equals(model.resolveUUID(getCurrentUser().getId())));
        } catch (ThingsboardException e) {
            model.failure(e.getMessage());
        }

        request.setStage(HANDOVER_REVIEW.name());
        workOrderService.addStage(id, request);
        return model.success();
    }

    @PostMapping("/{id}/handover")
    public IModel handoverVerify(IModel model, @PathVariable String id, @RequestBody WorkOrderStageRequest request) {

        // 限制审核人
        model.failureIfNot("非法的审核人", workOrderService.isUserProcess(id, request.currentUserUUID()));

        // 开始状态需要为 转单审核
        model.failureIfNot("非转单审核的工单", workOrderService.isStatus(id, HANDOVER_REVIEW));

        // 目标状态为 处理中 或 审核退回
        WorkOrderStatus stage = request.stage();
        model.failureIfNot("错误的目标状态" + request.getStage(),
                stage.equalsAny(RESOLVING, REJECTED)
        );

        if (stage.equals(REJECTED)) {
            // 审核拒绝 恢复下一步处理人为 【倒数第二步】 的 【操作人】
            // 不能处于 HANDOVER REASSIGN
            request.setNextProcessUserId(workOrderService.getPrevDetailProcessUserId(id, 2));
        } else {
            // 审核通过 恢复下一步处理人为 【倒数第一步】 的 【下一步操作人】
            request.setNextProcessUserId(workOrderService.getPrevDetailNextProcessUserId(id, 1));
        }

        workOrderService.addStage(id, request);
        return model.success();
    }

    @PostMapping
    public WorkOrder save(IModel model, @RequestBody WorkOrderSaveRequest request) {
        WorkOrder workOrder = workOrderService.save(request);
        return workOrder;
    }

    @PostMapping("/{id}/receive")
    public IModel receiveOrder(IModel model, @PathVariable String id, @RequestBody WorkOrderStageRequest request) {
        // 需要在 派单状态 且 有当前理人
        model.failureIfNot("工单不存在或无法接收未指派的工单",
                workOrderService.isStatus(id, ASSIGN) && workOrderService.isAssigned(id));

        request.setStage(RESOLVING.name());
        workOrderService.addStage(id, request);

        // 是否是故障工单
        faultReportService.isFault(id);

        return model.success();
    }

    @PostMapping("/{id}/stage")
    public IModel stage(IModel model, @PathVariable String id, @RequestBody WorkOrderDetailStageRequest request) {
        WorkOrderStatus stage = workOrderService.getStatus(id);
        // 当前状态需要在 处理中
        model.failureIfNot("操作非法，工单处于状态[%s]",
                PROCESSED.contains(stage),
                stage.name());

        // 没有传入 目标状态
        String tarStage = request.getStage();
        model.failureIf("未传入目标状态", StringUtils.isNullOrBlank(tarStage));
        // 目标状态需要在 到场 处理 完成
        WorkOrderStatus parsedTarStage = request.stage();
        model.failureIfNot("非法的目标状态，无法从当前状态[%s]转换到[%s]",
                parsedTarStage.equalsAny(ARRIVING, PROCESSING, SUBMIT),
                stage.getStageName(), tarStage
        );

        // 目标状态为提交完成审核时，需要验证协作工单是否完成
        if (parsedTarStage.equals(SUBMIT) && !workOrderService.canSubmit(id)) {
            model.failure("当前不允许提交审核，请检查协作任务是否完成。");
        }

        workOrderService.addStage(id, request, stage.equalsAny(ARRIVING, PROCESSING));
        return model.success();
    }

    @PostMapping("/{id}/verify")
    public IModel verify(IModel model, @PathVariable String id, @RequestBody WorkOrderStageRequest request) {

        // 限制审核人
        model.failureIfNot("非法的审核人", workOrderService.isUserProcess(id, request.currentUserUUID()));

        // 状态未在 审核
        WorkOrderStatus stage = workOrderService.getStatus(id);
        model.failureIfNot("不可审核的工单",
                stage.equalsAny(SUBMIT, REVIEW));

        // 没有传入 目标状态
        String tarStage = request.getStage();
        model.failureIf("未传入目标状态", StringUtils.isNullOrBlank(tarStage));
        // 目标状态需要在 通过 拒绝 或 重审
        WorkOrderStatus parsedTarStage = request.stage();
        model.failureIfNot("非法的目标状态，无法从当前状态[%s]转换到[%s]",
                parsedTarStage.equalsAny(REJECTED, APPROVED, REVIEW),
                stage.name(), tarStage
        );

        if (parsedTarStage.equals(REJECTED)) {
            // 审核退回时 变更当前步骤执行人为原有处理人
            request.setNextProcessUserId(workOrderService.getPrevDetailProcessUserId(id, 1));
        }

        workOrderService.addStage(id, request, !parsedTarStage.equals(REVIEW));
        return model.success();
    }

    @PostMapping("/{id}/chargebackRequest")
    public IModel chargebackRequest(IModel model, @PathVariable String id, @RequestBody WorkOrderStageRequest request) {
        // 已在最终状态
        WorkOrderStatus stage = workOrderService.getStatus(id);
        model.failureIf("工单刚创建，无法申请退单", stage.equals(PENDING));
        model.failureIf("正在退单中，无法申请退单", stage.equals(CHARGEBACK_REVIEW));
        model.failureIf("工单已终止，无法申请退单", stage.between(APPROVED, TERMINATED));

        workOrderService.chargebackRequest(id, request);
        return model.success();
    }

    @PostMapping("/{id}/chargeback")
    public IModel chargeBackReview(IModel model, @PathVariable String id, @RequestBody WorkOrderStageRequest request) {

        // 限制审核人
        model.failureIfNot("非法的审核人", workOrderService.isUserProcess(id, request.currentUserUUID()));

        // 状态未在 审核
        model.failureIfNot("非申请退单的工单", workOrderService.isStatus(id, CHARGEBACK_REVIEW));

        // 目标状态为 退单 和 审核退回
        WorkOrderStatus stage = request.stage();
        model.failureIfNot("错误的目标状态" + request.getStage(),
                stage.equalsAny(CHARGEBACK, REJECTED)
        );


        workOrderService.chargeback(id, request);
        return model.success();
    }

    @PostMapping("/{id}/terminate")
    public IModel terminate(IModel model, @PathVariable String id, @RequestBody WorkOrderStageRequest request) {
        // 已在最终状态
        WorkOrderStatus stage = workOrderService.getStatus(id);
        model.failureIf("工单已终止，无法终止", stage.between(APPROVED, TERMINATED));

        request.setStage(TERMINATED.name());
        workOrderService.addStage(id, request);
        return model.success();
    }

    @GetMapping("/count")
    public WorkOrderStatisticEntity count(IModel model, WorkOrderCountRequest request) {
        return workOrderService.countWorkOrder(request);
    }

    @GetMapping("/completeCount")
    public WorkOrderCompleteCountResponse completeCount(IModel model, WorkOrderCompleteCountRequest request) {
        return workOrderService.countWorkOrderComplete(request);
    }

    @GetMapping("fault/count")
    public Object faultCount(int page, int size,
                                    @RequestParam(required = false, defaultValue = "") String level,
                                    @RequestParam(required = false, defaultValue = "") String source,
                                    @RequestParam(required = false) Long startTime,
                                    @RequestParam(required = false) Long endTime) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return workOrderService.faultCount(page, size, level, source, startTime, endTime, tenantId);
    }


    @GetMapping("fault/workTime/count")
    public Object faultCount(int page, int size,
                                    @RequestParam(required = false) Long startTime,
                                    @RequestParam(required = false, defaultValue = "") String source,
                                    @RequestParam(required = false) Long endTime) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return workOrderService.workTimeCount(page, size, source, startTime, endTime, tenantId);
    }


    @PostMapping("/{id}/collaborate")
    public IModel collaborate(IModel model, @PathVariable String id, @RequestBody WorkOrderCollaborateRequest request) {
        // 需要在处理流程中
        WorkOrderStatus stage = workOrderService.getStatus(id);
        model.failureIfNot("工单需要在处理流程中", stage.between(RESOLVING, REJECTED));

        workOrderService.collaborate(id, request, request.tenantId());
        return model.success();
    }

    /**
     * 审核协作申请
     *
     * @param id 协作申请Id
     */
    @PostMapping("/{id}/collaborateVerify")
    public IModel collaborateVerify(IModel model, @PathVariable String id, @RequestBody WorkOrderCollaborateVerifyRequest request) {

        // 限制审核人
        model.failureIfNot("非法的审核人", workOrderService.collaborIsUserProcess(id, request.currentUserUUID()));

        // 需要在处理流程中
        model.failureIfNot("协作申请已审核", workOrderService.isStatus(id, WorkOrderCollaborationStatus.PENDING));


        workOrderService.collaborateVerify(id, request, request.tenantId());

        return model.success();
    }

    @GetMapping("/collaborations")
    public IPage<WorkOrderCollaboration> collaborations(IModel model, WorkOrderCollaborationPageRequest request) {
        return workOrderService.collaborations(request);
    }

    @GetMapping("/{id}/children")
    public List<WorkOrder> children(IModel model, @PathVariable String id) {
        return workOrderService.children(id);
    }

    @GetMapping("/countOfStatus")
    public WorkOrderCountOfStatusResponse countOfStatus(IModel model, MutableWorkOrderCountOfStatusRequest req) {
        return workOrderService.countOfStatus(req);
    }

}
