package org.thingsboard.server.dao.smartService.portal;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.smartService.portal.SsPortalHomeInfo;
import org.thingsboard.server.dao.sql.smartService.portal.SsPortalHomeInfoMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.smartService.portal.SsPortalHomeInfoSaveRequest;

@Service
public class SsPortalHomeInfoServiceImpl implements SsPortalHomeInfoService {
    @Autowired
    private SsPortalHomeInfoMapper mapper;

    @Override
    public SsPortalHomeInfo get(String tenantId) {
        return mapper.getByTenantId(tenantId);
    }

    @Override
    public SsPortalHomeInfo save(SsPortalHomeInfoSaveRequest entity) {
        return QueryUtil.saveOrUpdateOneByRequest(entity, mapper::save, mapper::updateFully);
    }

    @Override
    public boolean update(SsPortalHomeInfo entity) {
        return mapper.update(entity);
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }

}
