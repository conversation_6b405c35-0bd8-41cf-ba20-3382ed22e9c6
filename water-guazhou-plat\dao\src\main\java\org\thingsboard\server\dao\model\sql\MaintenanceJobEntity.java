package org.thingsboard.server.dao.model.sql;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;
import java.util.Date;
import java.util.List;

@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.MAINTENANCE_JOB_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class MaintenanceJobEntity {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.MAINTENANCE_JOB_NAME)
    private String name;

    @Column(name = ModelConstants.TYPE)
    private String type;

    @Column(name = ModelConstants.MAINTENANCE_JOB_STATUS)
    private String status;

    @Column(name = ModelConstants.MAINTENANCE_JOB_EXECUTE_TIME)
    private Date executeTime;

    @Column(name = ModelConstants.MAINTENANCE_JOB_COMPLETE_TIME)
    private Date completeTime;

    @Column(name = ModelConstants.MAINTENANCE_JOB_ACCEPTANCE_TIME)
    private Date acceptanceTime;

    @Column(name = ModelConstants.MAINTENANCE_JOB_END_TIME)
    private Date endTime;

    @Column(name = ModelConstants.CREATE_TIME)
    private Date createTime;

    @Column(name = ModelConstants.CREATOR_PROPERTY)
    private String creator;

    @Column(name = ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;

    @Transient
    private List<MaintenanceJobCEntity> jobList;

    @Transient
    private MaintenanceJobTriggerEntity trigger;

    @Transient
    private String code;

    @Transient
    private String orderId;
}
