import { request as gisRequest } from '@/plugins/axios/gis'
import { request } from '@/plugins/axios'
/**
 * 删除疑问标识
 * @param id
 * @returns
 */
export const DeleteQuestionMark = (id: string) => {
  return gisRequest({
    url: '/api/webapp/questionmarks/delete',
    method: 'post',
    data: {
      id
    }
  })
}
/**
 * 查询疑问标识
 * @param params
 * @returns
 */
export const GetQuestionMarks = (params: {
  pagenumber: number
  pagesize: number
}) => {
  return gisRequest({
    url: '/api/webapp/questionmarks',
    method: 'post',
    data: params
  })
}
/**
 * 保存疑问标识
 * @param params
 * @returns
 */
export const SaveQuestionMarks = (params: {
  x: number
  y: number
  cation: any
  fileurl: string
  content: string
  wkid: number
  scale: number
}) => {
  return gisRequest({
    url: '/api/webapp/questionmarks/add',
    method: 'post',
    data: params
  })
}

/**
 * 添加标签
 * @param params
 * @returns
 */
export const AddMapLabel = (params: {
  id?: string
  name?: string
  createuser?: string
  createtime?: string
  updatetime?: string
  description?: string
  available?: string
  geomtype?: string
  geom?: string
  style?: string
  pointcolor?: string
  pointsize?: number
  linecolor?: string
  linewidth?: number
  fillcolor?: string
}) => {
  return request({
    // url: '/api/gis/AddMapLabel',
    url: '/api/gis/label/save',
    method: 'post',
    data: params
  })
}
/**
 * 查询列表
 * @param params
 * @returns
 */
export const GetMapLables = (
  params: IQueryPagerParams & {
    // id?: number
    name?: string
    createuser?: string
    beginTime?: string
    endTime?: string
    // available?: string
    // geomtype?: string
    // startdate?: string
    // enddate?: string
    // pageindex?: number
    // pagesize?: number
  }
) => {
  return request({
    // url: '/api/gis/ListMapLabels',
    url: '/api/gis/label/list',
    method: 'get',
    params
  })
}
/**
 * 更新标签
 * @param params
 * @returns
 */
// export const UpdateMapLabel = (params: {
//   id?: number
//   name?: string
//   description?: string
//   available?: string
//   geomtype?: string
//   geom?: string
//   style?: string
//   pointcolor?: string
//   pointsize?: number
//   linecolor?: string
//   linewidth?: number
//   fillcolor?: string
// }) => {
//   return gisRequest({
//     url: '/api/gis/UpdateMapLabel',
//     method: 'post',
//     data: params
//   })
// }
/**
 * 删除标签
 * @param ids
 * @returns
 */
export const DeleteMapLabels = (ids: any) => {
  return request({
    // url: `/api/gis/DeleteMapLabels?ids=${ids}`,
    url: '/api/gis/label/remove',
    method: 'delete',
    data: ids
  })
}
