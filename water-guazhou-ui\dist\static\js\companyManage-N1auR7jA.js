import{_ as I}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as M}from"./CardTable-rdWOL4_6.js";import{_ as C}from"./CardSearch-CB_HNR-Q.js";import{I as g}from"./common-CvK_P_ao.js";import{d as L,M as W,c,r as b,ea as x,b as t,eb as q,S as E,ec as N,ed as R,ar as V,o as w,g as B,n as F,q as d,i as f}from"./index-r0dFAfgr.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";const U={class:"wrapper"},j=L({__name:"companyManage",setup(A){const{$btnPerms:u}=W(),m=c(),y=c(),r=c(),D=c({filters:[{label:"单位名称",field:"name",type:"input"},{type:"btn-group",btns:[{perm:!0,text:"查询",icon:g.QUERY,click:()=>n()},{perm:u("RoleManageAdd"),text:"编辑供水单位",icon:g.EDIT,click:()=>k()}]}]}),o=b({defaultExpandAll:!0,rowKey:"id",columns:[{label:"单位ID",prop:"id"},{label:"供水单位名称",prop:"name"},{label:"单位类别",prop:"type"},{label:"排序编号",prop:"orderNum"},{label:"联系方式",prop:"phone"},{label:"位置",prop:"location"},{label:"风貌信息",prop:"styleInformation"},{label:"水厂类型",prop:"orgType",formatter:a=>({0:"水源地",1:"供水厂",2:"污水厂",3:"其他"})[a.orgType]||a.orgType}],operationWidth:"200px",operations:[{type:"success",isTextBtn:!0,color:"#4195f0",text:"增下级",perm:u("RoleManageEdit"),icon:g.ADD,click:a=>_(a)},{type:"primary",isTextBtn:!0,color:"#4195f0",text:"编辑",perm:u("RoleManageEdit"),icon:"iconfont icon-xiangqing",click:a=>_(a,!0)},{isTextBtn:!0,type:"danger",text:"删除",icon:"iconfont icon-shanchu",perm:u("RoleManageDelete"),click:a=>T(a)}],dataList:[],pagination:{refreshData:({page:a,size:e})=>{o.pagination.page=a,o.pagination.limit=e}}}),v=b({title:"修改",labelWidth:"130px",dialogWidth:"500px",submit:a=>{x(a.id,a).then(e=>{var l;if(e.data.code===200){n(),(l=r.value)==null||l.closeDialog();return}t.error(e.data.message)}).catch(e=>{t.warning(e)})},defaultValue:{},group:[{fields:[{type:"input",label:"单位名称",field:"name",rules:[{required:!0,message:"请输入单位名称"}]},{type:"select",label:"单位类别",field:"type",options:[{label:"国家级集团公司",value:"国家级集团公司"},{label:"省级集团公司",value:"省级集团公司"},{label:"地市公司",value:"地市公司"},{label:"区县公司",value:"区县公司"},{label:"供水营业厅",value:"供水营业厅"},{label:"末级区域",value:"末级区域"}],rules:[{required:!0,message:"请选择单位类别"}]},{type:"select",label:"水厂类型",field:"orgType",options:[{label:"水源地",value:"0"},{label:"供水厂",value:"1"},{label:"污水厂",value:"2"},{label:"其他",value:"3"}]},{type:"input",label:"位置",field:"location"},{type:"input",label:"风貌信息",field:"styleInformation"}]}]}),i=b({title:"供水单位信息",dialogWidth:"500px",submit:a=>{a.id?x(a.id,a).then(e=>{var l;if(e.data.code===200){n(),(l=r.value)==null||l.closeDialog();return}t.error(e.data.message)}).catch(e=>{t.warning(e)}):q(a).then(e=>{var l;if(e.data.code===200){n(),(l=r.value)==null||l.closeDialog();return}t.error(e.data.message)}).catch(e=>{t.warning(e)})},defaultValue:{},group:[{fields:[{type:"input",label:"单位名称",field:"name",rules:[{required:!0,message:"请输入单位名称"}]},{type:"select",label:"上级供水单位",field:"parentId",options:[],readonly:!0},{type:"select",label:"单位类别",field:"type",options:[{label:"国家级集团公司",value:"国家级集团公司"},{label:"省级集团公司",value:"省级集团公司"},{label:"地市公司",value:"地市公司"},{label:"区县公司",value:"区县公司"},{label:"供水营业厅",value:"供水营业厅"},{label:"末级区域",value:"末级区域"}],rules:[{required:!0,message:"请选择单位类别"}]},{type:"input-number",label:"联系电话",field:"phone",rules:[{validator:S}]},{type:"number",label:"排序",field:"orderNum",min:0},{type:"select",label:"水厂类型",field:"orgType",options:[{label:"水源地",value:"0"},{label:"供水厂",value:"1"},{label:"污水厂",value:"2"},{label:"其他",value:"3"}]},{type:"input",label:"位置",field:"location"},{type:"input",label:"风貌信息",field:"styleInformation"}]}]});function S(a,e,l){e===void 0||e===""||/^1\d{10}$/.test(e)?l():l(new Error("电话号码格式错误"))}const k=()=>{var a;v.defaultValue={...o.dataList[0]},(a=y.value)==null||a.openDialog()},_=(a,e=!1)=>{var l;e?(i.defaultValue={...a||{}},i.group[0].fields[1].options=[{label:a.parentName||""||"",value:a.parentId}]):(i.defaultValue={orderNum:"0",parentId:a.id||""||{}},i.group[0].fields[1].options=[{label:a.name||"",value:a.id}]),(l=r.value)==null||l.openDialog()},T=a=>{E("确定删除该供水单位, 是否继续?","删除提示").then(()=>{N(a.id).then(e=>{if(e.data.code!==200){t.warning(e.data.err);return}n(),t.success("删除成功")}).catch(e=>{t.error(e.data.message)})})},n=async()=>{var l,p,s,h;let e;(p=(l=m.value)==null?void 0:l.queryParams)!=null&&p.name?e=await R({name:((h=(s=m.value)==null?void 0:s.queryParams)==null?void 0:h.name)||""}):e=await V(1),console.log(e.data),o.dataList=e.data.data||[],o.pagination.total=e.data.total||0};return w(()=>{n()}),(a,e)=>{const l=C,p=M,s=I;return B(),F("div",U,[d(l,{ref_key:"refSearch",ref:m,config:f(D)},null,8,["config"]),d(p,{config:f(o),class:"card-table"},null,8,["config"]),d(s,{ref_key:"refForm",ref:y,config:f(v)},null,8,["config"]),d(s,{ref_key:"refForm1",ref:r,config:f(i)},null,8,["config"])])}}});export{j as default};
