package org.thingsboard.server.dao.sql.base;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.base.BasePushTemplateConfiguration;
import org.thingsboard.server.dao.util.imodel.query.base.BasePushTemplateConfigurationPageRequest;

import java.util.List;

/**
 * 平台管理-推送模板配置Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-18
 */
@Mapper
public interface BasePushTemplateConfigurationMapper {
    /**
     * 查询平台管理-推送模板配置
     *
     * @param id 平台管理-推送模板配置主键
     * @return 平台管理-推送模板配置
     */
    public BasePushTemplateConfiguration selectBasePushTemplateConfigurationById(String id);

    /**
     * 查询平台管理-推送模板配置列表
     *
     * @param basePushTemplateConfiguration 平台管理-推送模板配置
     * @return 平台管理-推送模板配置集合
     */
    public IPage<BasePushTemplateConfiguration> selectBasePushTemplateConfigurationList(BasePushTemplateConfigurationPageRequest basePushTemplateConfiguration);

    /**
     * 新增平台管理-推送模板配置
     *
     * @param basePushTemplateConfiguration 平台管理-推送模板配置
     * @return 结果
     */
    public int insertBasePushTemplateConfiguration(BasePushTemplateConfiguration basePushTemplateConfiguration);

    /**
     * 修改平台管理-推送模板配置
     *
     * @param basePushTemplateConfiguration 平台管理-推送模板配置
     * @return 结果
     */
    public int updateBasePushTemplateConfiguration(BasePushTemplateConfiguration basePushTemplateConfiguration);

    /**
     * 删除平台管理-推送模板配置
     *
     * @param id 平台管理-推送模板配置主键
     * @return 结果
     */
    public int deleteBasePushTemplateConfigurationById(String id);

    /**
     * 批量删除平台管理-推送模板配置
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBasePushTemplateConfigurationByIds(@Param("array") List<String> ids);
}
