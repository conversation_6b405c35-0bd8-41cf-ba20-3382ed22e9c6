<template>
  <div class="pop-image">
    <!-- <el-tabs
      :tab-position="'right'"
      @tab-change="handleTabChange"
    >
      <el-tab-pane label="信息"> -->
    <div v-loading="state.loading" class="pop-image-content overlay-y">
      <AttrTable
        v-if="config.info.type === 'custom-attrs'"
        class="attr-table overlay-y"
        :columns="config.info.columns"
        :attributes="config.info.attributes"
        :data="config.info.attrData"
        :rows="config.info.rows"
        :theme="props.theme"
      >
      </AttrTable>
      <template v-else-if="config.info.type === 'attrs'">
        <el-carousel
          v-if="images.length"
          height="200px"
          :indicator-position="'none'"
          motion-blur
          style="margin-bottom: 20px"
        >
          <el-carousel-item v-for="(item, i) in images" :key="i">
            <el-image
              :preview-teleported="true"
              :close-on-press-escape="true"
              :hide-on-click-modal="true"
              style="width: 100%; height: 100%"
              :src="item"
              :initial-index="i"
              :preview-src-list="images"
              fit="contain"
            ></el-image>
          </el-carousel-item>
        </el-carousel>
        <div class="attr-table">
          <AttrTable :attributes="state.attributes" :theme="props.theme"></AttrTable>
        </div>
      </template>
      <div v-else class="attr-table">
        <el-image
          :preview-teleported="true"
          :close-on-press-escape="true"
          :hide-on-click-modal="true"
          style="width: 100%; height: 100%"
          :src="images[0]"
          :preview-src-list="images"
          fit="cover"
        />
      </div>
    </div>
    <!-- </el-tab-pane> -->
    <!-- <el-tab-pane
        label="报警"
        :lazy="true"
      >
        <div class="warning-box">
          <FormTable :config="TableConfig"></FormTable>
        </div>
      </el-tab-pane> -->
    <!-- </el-tabs> -->
  </div>
</template>
<script lang="ts" setup>
import { GetStationAlarmList, GetStationRealTimeDetail } from '@/api/shuiwureports/zhandian'
import { formatDate } from '@/utils/DateFormatter'

const props = defineProps<{
  visible: boolean
  config: {
    info: {
      type?: 'image' | 'attrs' | 'custom-attrs'
      imageUrl?: string
      stationId: string
      columns?: IAttrTableRow[][]
      attributes?: { label: string; value: any }[]
      attrData?: any
      rows?: IAttrTableRow[][]
    }
    warnings: {}
  }
  theme?: ITheme
}>()
const state = reactive<{
  loading: boolean
  attributes: { label: string; value: string; data?: any }[]
}>({
  loading: false,
  attributes: []
})
const images = computed(() => {
  return props.config.info?.imageUrl?.split(',') || []
})
const TableConfig = reactive<ITable>({
  maxHeight: 220,
  dataList: [],
  columns: [
    { minWidth: 180, label: '设备', prop: 'deviceName' },
    {
      minWidth: 120,
      label: '状态',
      prop: 'type',
      formatter(row, value) {
        const record = row.details?.record ?? []
        return record.length === 0 ? value : record[record.length - 1]?.info
      }
    },
    {
      minWidth: 160,
      label: '报警时间',
      prop: 'startTs',
      formatter: (row) => formatDate(row.startTs)
    }
  ],
  pagination: {
    hide: true
  }
})
const handleTabChange = async (index: any) => {
  try {
    if (index === '1') {
      await getAlarmList()
    } else if (index === '0') {
      state.loading = true
      if (props.config.info.type === 'attrs') {
        await getAttrList()
      }
    }
  } catch (error) {
    console.log(error)
  }
  state.loading = false
}
const getAlarmList = async () => {
  try {
    const res = await GetStationAlarmList(
      props.config.info.stationId,
      moment().subtract(1, 'y').valueOf(),
      moment().valueOf()
    )
    TableConfig.dataList = res.data || []
  } catch (error) {
    console.log(error)
  }
}
const getAttrList = async () => {
  const real = await GetStationRealTimeDetail(props.config.info.stationId)
  state.attributes = real.data?.map((item) => {
    return {
      label: item.propertyName,
      value: (item.value || '--') + ' ' + (item.unit || ''),
      data: item
    }
  })
}
watch(
  () => props.visible,
  () => {
    if (props.visible === true) handleTabChange('0')
  }
)
onActivated(() => {
  console.log('active')
})
// onMounted(() => {
//   handleTabChange('0')
// })
</script>
<style lang="scss" scoped>
.pop-image {
  width: 340px;
}

.pop-image-content {
  padding-right: 8px;
  min-width: 150px;
  min-height: 200px;
}
.attr-table{
  height: 200px;
  overflow-y: auto;
}
.warning-box {
  height: 165px;
  width: 100%;
}
</style>
