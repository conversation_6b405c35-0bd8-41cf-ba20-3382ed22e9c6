package org.thingsboard.server.dao.util.imodel.query.smartService.wechat;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartService.wechat.WxCustomModule;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;

@Getter
@Setter
public class WxCustomModulePageRequest extends AdvancedPageableQueryEntity<WxCustomModule, WxCustomModulePageRequest> {
    // 模块名
    private String name;

    // 是否启用
    private Boolean isEnabled;


}
