<template>
  <div id="tool-search-poi" class="esri-widget">
    <el-select
      v-model="state.queryType"
      placeholder="Select"
      class="poi-selector"
    >
      <el-option label="地名" value="7" />
      <el-option label="poi" value="1" />
    </el-select>
    <el-select
      v-model="state.value"
      filterable
      remote
      reserve-keyword
      placeholder="请输入地名"
      remote-show-suffix
      :remote-method="remoteMethod"
      :loading="state.loading"
      :class="'poi-search'"
      @change="handleChange"
    >
      <el-option
        v-for="item in state.options"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      />
    </el-select>
    <el-button
      :icon="SearchIcon"
      type="primary"
      class="poi-button"
      @click="handleChange"
    >
    </el-button>
  </div>
</template>
<script lang="ts" setup>
import { Search as SearchIcon } from '@element-plus/icons-vue';
import { queryTdt } from '@/api/mapservice/utils';

const emit = defineEmits(['change']);
const state = reactive<{
  value: string;
  loading: boolean;
  options: NormalOption[];
  queryType: string;
}>({
  value: '',
  loading: false,
  options: [],
  queryType: '7'
});
const remoteMethod = (query: string) => {
  if (query) {
    state.loading = true;
    queryTdt({ keyWord: query, queryType: state.queryType })
      .then((res) => {
        if (state.queryType === '7') {
          state.options =
            res.data.pois?.map((item) => {
              return {
                label: item.name,
                value: item.hotPointID,
                data: item
              };
            }) || [];
        } else if (state.queryType === '1') {
          state.options =
            res.data.pois?.map((item) => {
              return {
                label: item.name,
                value: item.hotPointID,
                data: item
              };
            }) || [];
        }
      })
      .finally(() => {
        state.loading = false;
      });
  } else {
    state.options = [];
  }
};
const handleChange = () => {
  const data = state.options.find((item) => item.value === state.value);
  if (!data) return;
  console.log(data);

  const location = data.data.lonlat?.split(',');
  emit('change', location);
  // const point = new Point({
  //   longitude: location?.[0],
  //   latitude: location?.[1],
  //   spatialReference: props.view?.spatialReference
  // })
  // const mark = new Graphic({
  //   geometry: point,
  //   symbol: new PictureMarkerSymbol({
  //     url: getMapLocationImageUrl()
  //   })
  // })
  // props.view?.graphics.removeAll()
  // props.view?.graphics.add(mark)
  // gotoAndHighLight(props.view, mark, {
  //   avoidHighlight: true,
  //   zoom: 16
  // })
};
// const init = async (view: __esri.MapView) => {
//   if (!view) return
//   view.ui?.add('tool-search-poi', 'top-right')
// }
// defineExpose({
//   init
// })
</script>
<style lang="scss" scoped>
#tool-search-poi {
  display: flex;
  align-items: center;
  border-radius: var(--el-border-radius-base);
  border-radius: 4px;
}
.custom-toolbar {
  line-height: 32px;
  text-align: center;
  display: flex;

  .tool-icon {
    margin: auto;
  }
}
.poi-selector {
  width: 80px;
  :deep(.el-input__wrapper) {
    border-radius: 4px 0 0 4px;
  }
}
.poi-search {
  :deep(.el-input__wrapper) {
    border-radius: 0;
  }
}
.poi-button {
  margin: 0;
  border-radius: 0 4px 4px 0;
}
</style>
