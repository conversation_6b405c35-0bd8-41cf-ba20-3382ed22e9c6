<!-- 工程管理-详情-工程设计基础信息 -->
<template>
  <el-card shadow="hover" class="card">
    <descriptions :config="basicConfig"></descriptions>
  </el-card>
</template>

<script lang="ts" setup>
const props = defineProps<{ config: any }>();

const basicConfig = reactive<IDescriptionsConfig>({
  defaultValue: computed(() => props.config) as any,
  border: true,
  direction: 'horizontal',
  column: 2,
  title: '工程设计基础信息',
  fields: [
    { type: 'text', label: '设计编号:', field: 'code' },
    { type: 'text', label: '设计分类:', field: 'type' },
    { type: 'text', label: '设计费用:', field: 'cost' },
    {
      type: 'text',
      label: '设计管长:',
      field: 'pipLengthDesign',
      formatter: (row) => {
        const value = (row && JSON.parse(row || '')) || [];
        let text = '';
        value.forEach((item) => {
          text += `DN${item.dn}设计管长${item.length},`;
        });
        return text;
      }
    },
    { type: 'text', label: '设计备注:', field: 'remark' },
    { type: 'text', label: '创建人:', field: 'creatorName' },
    { type: 'text', label: '创建时间:', field: 'createTimeName' },
    { type: 'text', label: '最后更新人:', field: 'updateUserName' },
    { type: 'text', label: '最后更新时间:', field: 'updateTimeName' }
  ]
});
</script>

<style lang="scss" scoped>
.card {
  margin-bottom: 20px;
}
</style>
