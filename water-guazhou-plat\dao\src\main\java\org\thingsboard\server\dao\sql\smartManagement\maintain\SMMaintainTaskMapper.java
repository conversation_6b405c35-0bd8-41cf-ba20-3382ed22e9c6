package org.thingsboard.server.dao.sql.smartManagement.maintain;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.smartManagement.maintaince.SMMaintainTask;
import org.thingsboard.server.dao.model.sql.smartProduction.circuit.GeneralTaskStatus;
import org.thingsboard.server.dao.model.sql.statistic.GeneralTaskStatusStatistic;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.maintain.SMMaintainTaskAssignRequest;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.maintain.SMMaintainTaskCompleteRequest;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.maintain.SMMaintainTaskPageRequest;

import java.util.List;

@Mapper
public interface SMMaintainTaskMapper extends BaseMapper<SMMaintainTask> {
    IPage<SMMaintainTask> findByPage(SMMaintainTaskPageRequest request);

    boolean update(SMMaintainTask entity);

    boolean assign(SMMaintainTaskAssignRequest req);

    boolean complete(SMMaintainTaskCompleteRequest req);

    GeneralTaskStatusStatistic countStatus(@Param("userId") String userId, @Param("status") List<GeneralTaskStatus> status);

    Integer totalOfUser(String userId);

    Integer totalStatusOfUser(@Param("userId") String userId, @Param("status") List<GeneralTaskStatus> status);
}
