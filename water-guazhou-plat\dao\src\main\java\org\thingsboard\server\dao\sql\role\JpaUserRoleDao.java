/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.sql.role;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Component;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.id.CustomerId;
import org.thingsboard.server.common.data.id.RoleId;
import org.thingsboard.server.common.data.id.UserId;
import org.thingsboard.server.common.data.role.UserRole;
import org.thingsboard.server.dao.DaoUtil;
import org.thingsboard.server.dao.model.sql.UserRoleEntity;
import org.thingsboard.server.dao.role.UserRoleDao;
import org.thingsboard.server.dao.sql.JpaAbstractDao;
import org.thingsboard.server.dao.util.SqlDao;

import java.util.List;


@Component
@SqlDao
@Slf4j
public class JpaUserRoleDao extends JpaAbstractDao<UserRoleEntity, UserRole> implements UserRoleDao {

    @Autowired
    private UserRoleRepository userRoleRepository;


    @Override
    protected Class<UserRoleEntity> getEntityClass() {
        return UserRoleEntity.class;
    }

    @Override
    protected CrudRepository<UserRoleEntity, String> getCrudRepository() {
        return userRoleRepository;
    }

    @Override
    public void deleteByUserId(UserId userId) {
        userRoleRepository.deleteByUserId(UUIDConverter.fromTimeUUID(userId.getId()));
    }

    @Override
    public List<UserRole> findByRole(RoleId roleId) {
        return DaoUtil.convertDataList(userRoleRepository.
                findByRoleIdEquals(UUIDConverter.fromTimeUUID(roleId.getId())));
    }

    @Override
    public List<String> getRoleIdByUserId(UserId userId) {
        List<String> roleIds = userRoleRepository.getRoleIdByUserId(UUIDConverter.fromTimeUUID(userId.getId()));
        return roleIds;
    }

    @Override
    public void deleteByUserId(CustomerId customerId) {
        userRoleRepository.deleteByUserId(UUIDConverter.fromTimeUUID(customerId.getId()));
    }
}
