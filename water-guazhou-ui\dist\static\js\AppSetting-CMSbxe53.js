import{_ as W}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{d as w,c as g,r as b,cO as K,bC as G,ee as L,b as i,S as T,ef as O,g as q,n as z,q as o,F as r,i as l,j as H,u as R,G as _,p as C,eg as j,eh as J,J as M,bK as X,I as Y,H as $,K as Q,aq as Z,C as ee}from"./index-r0dFAfgr.js";import{f as ae}from"./DateFormatter-Bm9a68Ax.js";const oe={class:"app-setting"},te={class:"el-upload el-upload--text",style:{"margin-left":"8px"}},le=w({__name:"AppSetting",setup(ne){const V=g(),c=g(),m=g(!1),n=b({appKey:window.SITE_CONFIG.LOGIN_CONFIG.APPKEY}),N=a=>{var t,s;(t=c.value)==null||t.clearFiles();const e=a[0];e.uid=j(),(s=c.value)==null||s.handleStart(e)},U=()=>{m.value=!0},h=a=>{console.log(a),m.value=!1},k=()=>{var a;(a=c.value)==null||a.submit()},A=(a,e,t)=>{console.log(a,e,t),n.url=a+"?name="+e.name,m.value=!1},x=()=>{n.url=""},y=g(),F=()=>{if(!n.appKey){i.error("保存失败，请联系管理员"),console.log("appKey不存在");return}if(!n.url){i.error("请先选择文件并上传");return}y.value.validate(async a=>{if(a)try{const e=await J(n);e.data.code===200?i.success("保存成功"):i.error(e.data.message||"保存失败")}catch{i.error("保存失败")}else return i.warning("输入内容有误，请检查后再试"),!1})},E=b({group:[],title:"App历史版本",labelPosition:"right",labelWidth:"100px",defaultValue:{}}),S=()=>{var a;(a=V.value)==null||a.openDialog(),v()},d=b({columns:[{minWidth:120,label:"软件名",prop:"appName"},{minWidth:220,label:"下载路径",isUrl:!0,download:!0,prop:"url",handleClick(a,e){var s;const t=((s=K(a.url,"name"))==null?void 0:s.value)??"App";e.download&&G(a.url,t)}},{minWidth:120,label:"版本号",prop:"versionCode"},{minWidth:120,label:"版本信息",prop:"versionContent"},{minWidth:120,label:"版本名称",prop:"versionName"},{minWidth:160,label:"上传时间",prop:"createTime",formatter(a,e){return ae(e)}}],dataList:[],operations:[{perm:!0,text:"删除",type:"danger",click:a=>D(a)}],pagination:{refreshData:({page:a,size:e})=>{d.pagination.page=a||1,d.pagination.limit=e||20,v()}}}),v=async()=>{var a,e,t;try{const s=await L({page:d.pagination.page||1,size:d.pagination.limit||20});d.dataList=((e=(a=s.data)==null?void 0:a.data)==null?void 0:e.data)||[],d.pagination.total=((t=s.data)==null?void 0:t.data.total)||0}catch{}},D=a=>{const e=a==null?void 0:a.id;if(!e){i.error("请先选择一个要删除的版本");return}T("确定删除？","删除提示").then(()=>{try{O([e]).then(t=>{var s;((s=t.data)==null?void 0:s.code)===200?(i.success("删除成功"),v()):i.error("删除失败")}).catch(t=>{i.error(t.message??"删除失败")})}catch{}}).catch(()=>{})};return(a,e)=>{const t=M,s=X,u=Y,f=$,I=Q,P=Z,B=W;return q(),z("div",oe,[o(I,{ref_key:"eForm",ref:y,model:l(n),"label-width":"100px"},{default:r(()=>[o(u,{label:"App上传",prop:"logoUrl"},{default:r(()=>[o(s,{ref_key:"refUpload",ref:c,class:"upload-demo",action:l(H)().actionUrl+"file/api/upload/file",limit:1,headers:{"X-Authorization":"Bearer "+l(R)().token},"auto-upload":!1,"on-exceed":N,"before-upload":U,"on-success":A,"on-error":h,onRemove:x},{trigger:r(()=>[o(t,{type:"primary",loading:l(m)},{default:r(()=>e[5]||(e[5]=[_(" 选择上传文件 ")])),_:1},8,["loading"])]),tip:r(()=>e[7]||(e[7]=[C("div",{class:"el-upload__tip"}," 点击按钮选择要上传的安装包文件 ",-1)])),default:r(()=>[C("div",te,[o(t,{class:"ml-3",type:"success",disabled:l(m),onClick:k},{default:r(()=>e[6]||(e[6]=[_(" 点击上传 ")])),_:1},8,["disabled"])])]),_:1},8,["action","headers"])]),_:1}),o(u,{label:"版本号",prop:"versionCode"},{default:r(()=>[o(f,{modelValue:l(n).versionCode,"onUpdate:modelValue":e[0]||(e[0]=p=>l(n).versionCode=p)},null,8,["modelValue"])]),_:1}),o(u,{label:"版本名称",prop:"versionName"},{default:r(()=>[o(f,{modelValue:l(n).versionName,"onUpdate:modelValue":e[1]||(e[1]=p=>l(n).versionName=p)},null,8,["modelValue"])]),_:1}),o(u,{label:"APP名称",prop:"appName"},{default:r(()=>[o(f,{modelValue:l(n).appName,"onUpdate:modelValue":e[2]||(e[2]=p=>l(n).appName=p)},null,8,["modelValue"])]),_:1}),o(u,{label:"APP备注信息",prop:"remark"},{default:r(()=>[o(f,{modelValue:l(n).remark,"onUpdate:modelValue":e[3]||(e[3]=p=>l(n).remark=p)},null,8,["modelValue"])]),_:1}),o(u,{label:"更新内容明细",prop:"versionContent"},{default:r(()=>[o(f,{modelValue:l(n).versionContent,"onUpdate:modelValue":e[4]||(e[4]=p=>l(n).versionContent=p),type:"textarea"},null,8,["modelValue"])]),_:1}),o(u,{label:""},{default:r(()=>[o(t,{type:"primary",disabled:l(m),onClick:F},{default:r(()=>e[8]||(e[8]=[_(" 保存 ")])),_:1},8,["disabled"]),o(t,{type:"default",onClick:S},{default:r(()=>e[9]||(e[9]=[_(" 历史版本 ")])),_:1})]),_:1})]),_:1},8,["model"]),o(B,{ref_key:"refDialog",ref:V,config:l(E)},{default:r(()=>[o(P,{config:l(d)},null,8,["config"])]),_:1},8,["config"])])}}}),pe=ee(le,[["__scopeId","data-v-58d635ed"]]);export{pe as default};
