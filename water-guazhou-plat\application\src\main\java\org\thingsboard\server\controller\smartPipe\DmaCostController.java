package org.thingsboard.server.controller.smartPipe;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.DmaCost;
import org.thingsboard.server.dao.smartPipe.DmaCostService;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.List;
import java.util.Map;

/**
 * 智慧管网-DMA分区
 */
@RestController
@RequestMapping("api/spp/dma/cost")
public class DmaCostController extends BaseController {
    @Autowired
    private DmaCostService dmaCostService;

    @GetMapping("list")
    public IstarResponse getList(@RequestParam Map<String, Object> params) {
        return IstarResponse.ok(dmaCostService.getList(params));
    }

    @PostMapping
    public IstarResponse save(@RequestBody DmaCost dmaCost) throws ThingsboardException {
        dmaCost.setTenantId(UUIDConverter.fromTimeUUID(getTenantId().getId()));
        dmaCost.setCreator(UUIDConverter.fromTimeUUID(getCurrentUser().getUuidId()));
        return IstarResponse.ok(dmaCostService.save(dmaCost));
    }

    @DeleteMapping
    public IstarResponse delete(@RequestBody List<String> idList) throws ThingsboardException {
        dmaCostService.delete(idList);
        return IstarResponse.ok();
    }
}
