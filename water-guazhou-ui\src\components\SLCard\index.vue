<template>
  <div
    class="sl-card"
    :class="{ hastitle: !!title, dark: useAppStore().isDark }"
    :style="{
      height: height ? height + 'px' : '',
      'box-shadow': noBorder ? 'none' : 'var(--el-box-shadow-lighter)',
      padding: padding
    }"
  >
    <div v-if="title" class="sl-card-title">
      <slot name="title">
        <span class="title">{{ typeof title === 'string' ? title : '' }}</span>
      </slot>
      <slot name="query"></slot>

      <slot name="right"></slot>
    </div>
    <div
      class="sl-card-content"
      :style="contentStyle"
      :class="{ 'overlay-y': overlay }"
    >
      <slot></slot>
    </div>
    <div v-if="bottom" class="sl-card-bottom">
      <slot name="bottom"></slot>
    </div>
  </div>
</template>

<script lang="ts" setup name="SLCard">
import { useAppStore } from '@/store';

defineProps<{
  overlay?: boolean;
  title?: any;
  height?: number;
  bottom?: boolean;
  noBorder?: boolean;
  contentStyle?: any;
  padding?: string;
}>();
</script>

<style lang="scss" scoped>
.dark {
  .sl-card {
    background-color: var(--el-bg-color);
  }
}

.sl-card {
  position: relative;
  width: 100%;
  border-radius: 4px;
  box-shadow: var(--el-box-shadow-lighter);
  background-color: #fff;

  .sl-card-content {
    width: 100%;
    height: 100%;
  }

  // 有标题的块
  &.hastitle {
    padding: 50px 8px 8px 8px;

    .sl-card-title {
      color: var(--el-text-color-secondary);
      font-size: 18px;
      height: 50px;
      margin-top: -50px;
      display: flex;
      align-content: center;
      align-items: center;

      .title {
        margin-right: auto;
        font-weight: 700;
      }
    }
  }
}

// .overlay-y {
//   padding-right: 0;
// }

.sl-card-bottom {
  height: 40px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
</style>
