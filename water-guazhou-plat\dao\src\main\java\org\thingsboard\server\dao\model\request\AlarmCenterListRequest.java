package org.thingsboard.server.dao.model.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class AlarmCenterListRequest extends IstarPageRequest {

    private String alarmType;

    private String alarmLevel;

    private String processStatus;

    private String alarmStatus;

    private String stationId;

    private String stationType;

    private List<String> stationTypeList;

    private List<String> stationIdList;

    private String userId;

    private String type;

    private String areaId;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 报表查询类型
     * 1:告警类型
     * 2:告警等级
     * 3:站点类型
     */
    private String queryType;

    /**
     * 分析类型
     * 1:按区域统计
     * 2:按站点类型统计
     * 3:按站点统计
     */
    private String analysisType;


}
