package org.thingsboard.server.dao.smartService.portal;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.smartService.portal.SsPortalNews;
import org.thingsboard.server.dao.util.imodel.query.smartService.portal.SsPortalActiveRequest;
import org.thingsboard.server.dao.util.imodel.query.smartService.portal.SsPortalNewsPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartService.portal.SsPortalNewsSaveRequest;

public interface SsPortalNewsService {
    SsPortalNews findById(String id);

    IPage<SsPortalNews> findAllConditional(SsPortalNewsPageRequest request);

    SsPortalNews save(SsPortalNewsSaveRequest entity);

    boolean update(SsPortalNews entity);

    boolean delete(String id);

    boolean active(SsPortalActiveRequest req);

    boolean setHot(SsPortalActiveRequest request);

    boolean setRecommend(SsPortalActiveRequest request);

    boolean canSave(SsPortalNewsSaveRequest request);

}
