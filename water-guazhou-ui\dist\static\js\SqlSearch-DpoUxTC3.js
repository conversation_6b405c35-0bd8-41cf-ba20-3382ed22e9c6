import{d as q,c as g,r as x,W as I,b as d,bB as C,o as S,Q as _,g as L,n as N,q as w,i as c,_ as B,C as D}from"./index-r0dFAfgr.js";import{GetFieldConfig as G,GetFieldUniqueValue as T}from"./fieldconfig-Bk3o1wi7.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./GraphicsLayer-DTrBRwJQ.js";import{e as E,i as R}from"./QueryHelper-ILO3qZqg.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import V from"./PipeDetail-CTBPYFJW.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./project-DUuzYgGl.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./Search-NSrhrIa_.js";import"./FormTableColumnFilter-BT7pLXIC.js";import"./DateFormatter-Bm9a68Ax.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./LayerHelper-Cn-iiqxI.js";import"./pipe-nogVzCHG.js";import"./config-fy91bijz.js";const P=q({__name:"SqlSearch",props:{view:{},telport:{}},setup(F){const f=g(),u=F,t=x({pipeLayerOption:[],curOperate:"",tabs:[]}),a=g(),y=x({group:[{fieldset:{desc:"图层名称"},fields:[{type:"select",clearable:!1,field:"layer",options:[],onChange:()=>{var e;(e=a.value)!=null&&e.dataForm&&(a.value.dataForm.sql="")}}]},{fieldset:{desc:"图层字段"},fields:[{type:"list",data:[],className:"sql-list-wrapper",setData:async(e,r)=>{var s,i;if(!r.layer)return;const o=await G(r.layer);e.data=(i=(s=o.data)==null?void 0:s.result)==null?void 0:i.rows},setDataBy:"layer",displayField:"alias",valueField:"name",highlightCurrentRow:!0,nodeClick:e=>{t.curNode=e,l(e.name)}}]},{id:"field-construct",fieldset:{desc:"构建查询语句"},fields:[{type:"btn-group",size:"small",style:{width:"40%",display:"flex",flexWrap:"wrap"},className:"sql-btns-wrapper",btns:[{perm:!0,text:"=",styles:{margin:"6px",width:"50px"},click:()=>{l("=")}},{perm:!0,text:"模糊",styles:{margin:"6px",width:"50px"},click:()=>{l("like '%替换此处%'")}},{perm:!0,text:">",styles:{margin:"6px",width:"50px"},click:()=>{l(">")}},{perm:!0,text:"<",styles:{margin:"6px",width:"50px"},click:()=>{l("<")}},{perm:!0,text:"非",styles:{margin:"6px",width:"50px"},click:()=>{l("<>")}},{perm:!0,text:"并且",styles:{margin:"6px",width:"50px"},click:()=>{l("and")}},{perm:!0,text:"或者",styles:{margin:"6px",width:"50px"},click:()=>{l("or")}},{perm:!0,text:"%",styles:{margin:"6px",width:"50px"},click:()=>{l("%")}}],extraFormItem:[{type:"list",wrapperStyle:{width:"60%",height:"144px"},className:"sql-list-wrapper",field:"uniqueValue",data:[],nodeClick:e=>{l("'"+e+"'")},filters:[{type:"btn-group",btns:[{perm:!0,text:()=>t.curOperate==="uniqueing"?"正在获取唯一值":"获取唯一值",loading:()=>t.curOperate==="uniqueing",disabled:()=>t.curOperate==="detailing",styles:{width:"100%",borderRadius:"0"},click:()=>v()}]}]}]}]},{fieldset:{desc:"组合查询条件"},fields:[{type:"textarea",field:"sql",placeholder:"OBJECTID > 0"},{type:"btn-group",btns:[{perm:!0,text:"清除",type:"danger",disabled:()=>t.curOperate==="detailing",click:()=>h(),styles:{width:"100%"}},{perm:!0,text:()=>t.curOperate==="detailing"?"正在查询":"查询",disabled:()=>t.curOperate==="detailing",loading:()=>t.curOperate==="detailing",click:()=>k(),styles:{width:"100%"}}]}]}],labelPosition:"top",gutter:12,defaultValue:{}}),b=()=>{var o,s,i,p;if(!u.view)return;const e=(o=u.view)==null?void 0:o.map.findLayerById("pipelayer");t.pipeLayerOption=[],(s=e==null?void 0:e.sublayers)==null||s.map(n=>{var m;(m=t.pipeLayerOption)==null||m.push({label:n.title,value:n.title,id:n.id})});const r=y.group[0].fields[0];r&&(r.options=t.pipeLayerOption),(i=a.value)!=null&&i.dataForm&&(a.value.dataForm.layer=t.pipeLayerOption&&((p=t.pipeLayerOption[0])==null?void 0:p.value))},v=async()=>{var e,r;if(t.curNode){t.curOperate="uniqueing";try{const o=(e=t.pipeLayerOption.find(n=>{var m;return n.label===((m=a.value)==null?void 0:m.dataForm.layer)}))==null?void 0:e.id,s=await T({usertoken:I().gToken,layerid:o,f:"pjson",field_name:t.curNode.name}),i=(r=y.group.find(n=>n.id==="field-construct"))==null?void 0:r.fields[0].extraFormItem,p=i&&i[0];p&&(p.data=s.data.result.rows)}catch{d.error("获取唯一值失败")}t.curOperate=""}},l=e=>{var o;if(!a.value)return;(o=a.value)!=null&&o.dataForm||(a.value.dataForm={});const r=a.value.dataForm.sql||" ";a.value.dataForm.sql=r+e+" "},O=async()=>{var e;u.view&&((e=f.value)==null||e.extentTo(u.view))},h=()=>{var e;(e=a.value)!=null&&e.dataForm&&(a.value.dataForm.sql=" ")},k=async()=>{var o,s;const e=t.pipeLayerOption.find(i=>{var p;return i.label===((p=a.value)==null?void 0:p.dataForm.layer)});if(!e){d.warning("请选择图层");return}const r=(s=(o=a.value)==null?void 0:o.dataForm)==null?void 0:s.sql;if(!r||r.trim&&r.trim()===""){d.warning("组合条件不能为空");return}try{t.curOperate="detailing";const i=await E(window.SITE_CONFIG.GIS_CONFIG.gisService+window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService+"/"+e.id,R({returnGeometry:!1,where:r,orderByFields:["OBJECTID asc"]}));console.log(i),i!=null&&i.length?(t.tabs=[{label:e.label,name:e.label,id:e.id,data:i}],C(()=>{var p;(p=f.value)==null||p.openDialog()})):(d.info("查询结果为空"),t.tabs=[])}catch{d.error("查询失败，请检查查询条件是否正确"),t.curOperate=""}};return S(()=>{b()}),_(()=>{t.curOperate=""}),(e,r)=>{const o=B;return L(),N("div",null,[w(o,{ref_key:"refForm",ref:a,config:c(y)},null,8,["config"]),w(V,{ref_key:"refDetail",ref:f,tabs:c(t).tabs,telport:e.telport,onClose:r[0]||(r[0]=()=>c(t).curOperate=""),onRefreshed:r[1]||(r[1]=()=>c(t).curOperate="viewingDetail"),onRefreshing:r[2]||(r[2]=()=>c(t).curOperate="detailing"),onRowdblclick:O},null,8,["tabs","telport"])])}}}),st=D(P,[["__scopeId","data-v-34da73b2"]]);export{st as default};
