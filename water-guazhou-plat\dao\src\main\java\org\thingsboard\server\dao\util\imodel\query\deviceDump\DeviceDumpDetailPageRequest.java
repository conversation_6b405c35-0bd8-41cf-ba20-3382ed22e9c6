package org.thingsboard.server.dao.util.imodel.query.deviceDump;

import org.thingsboard.server.dao.model.sql.deviceDump.DeviceDumpDetail;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class DeviceDumpDetailPageRequest extends AdvancedPageableQueryEntity<DeviceDumpDetail, DeviceDumpDetailPageRequest> {
    // 主表ID
    private String mainId;
}
