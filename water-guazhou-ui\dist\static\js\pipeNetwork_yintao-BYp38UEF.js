import{d as A,c as C,r as N,o as b,Q as S,g as _,n as h,q as e,p as r,i as y,aB as k,aJ as H,h as E,F as p,X as x,C as B}from"./index-r0dFAfgr.js";import{M as J}from"./Map-BtiwaWSD.js";import{u as U}from"./useStation-DJgnSZIA.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import{a as Z}from"./LayerHelper-Cn-iiqxI.js";import"./project-DUuzYgGl.js";import{s as v,E as G}from"./StatisticsHelper-D-s_6AyQ.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import{S as s}from"./data-CLo2TII-.js";import m from"./TitleCard-BgReUNwX.js";import P from"./TitleHeader-CBWfLOPA.js";import{_ as Y}from"./GDLXSLTJ.vue_vue_type_script_setup_true_lang-DM08ggXp.js";import{_ as F}from"./QXGZL.vue_vue_type_script_setup_true_lang-BtNd_TOq.js";import M from"./XJWCL-BWRNvTUh.js";import O from"./SCGY_yintao-aR2fjUFb.js";import R from"./DeviceGroup-DfMgqr6q.js";import T from"./DeviceStatic-DPI9NTSP.js";import V from"./LLJK-DMALGaHM.js";import Q from"./LightPlat-BpCirFph.js";import j from"./YLJC-CfYIPw6m.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./PipeLength.vue_vue_type_script_setup_true_lang-BZfUsvDf.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./widget-BcWKanF2.js";import"./arcWidgetButton-0glIxrt7.js";import"./pipe-nogVzCHG.js";import"./PoiSearchV2-D7yNeLuv.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./ViewHelper-BGCZjxXH.js";import"./fieldconfig-Bk3o1wi7.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./useWidgets-BRE-VQU9.js";import"./URLHelper-B9aplt5w.js";import"./useLayerList-DmEwJ-ws.js";import"./useScaleBar-Beed-z91.js";import"./zhandian-YaGuQZe6.js";import"./index-BggOjNGp.js";import"./index-CpGhZCTT.js";import"./onemap-CEunQziB.js";/* empty css                         */import"./img_8-BTVxQQlz.js";import"./6-4nR55Xef.js";import"./index-CknacZq4.js";import"./usePartition-DkcY9fQ2.js";import"./dma-SMxrzG7b.js";import"./hookupDevice-Bcbk7s68.js";import"./statistics-CeyexT_5.js";import"./index-BlG8PIOK.js";const q={class:"content"},X={class:"columns"},$={class:"left"},z={class:"toprow"},K={class:"loss-rate"},W={class:"loss-rate-items"},tt={class:"toprow-right"},et={class:"bottomrow"},it={class:"right"},ot=A({__name:"pipeNetwork_yintao",setup(rt){const d=C(),f={},t=N({lossRates:[{type:"up",delta:11.8,value:1.44,unit:"万m³",title:"今日供水量"},{type:"down",delta:11.8,value:2.64,unit:"万m³",title:"昨日供水量"},{type:"up",delta:13.8,value:813.92,unit:"万m³",title:"本月供水量"}],pipeData:{pipeLength:0,valve:0,meter:0,drayAirValve:0,hydrant:0,threeCorss:0},deviceData:{pipeLength:0,waterQuality:0,pressure:0,bigUser:0,secondary:0,flow:0},layerInfos:[],layerIds:[]}),w=async()=>{var a,c,u;const i=await v("length",{layerIds:t.layerInfos.filter(l=>l.geometrytype==="esriGeometryPolyline").map(l=>l.layerid)});t.pipeData.pipeLength=(u=(c=(a=i[0])==null?void 0:a.rows)==null?void 0:c[0])==null?void 0:u[G.ShapeLen],t.deviceData.pipeLength=t.pipeData.pipeLength,(await v("count",{layerIds:t.layerIds})).map(l=>{const n=l.rows[0].OBJECTID;switch(l.layername){case"阀门":t.pipeData.valve=n;break;case"水表":t.pipeData.meter=n;break;case"排气阀":t.pipeData.drayAirValve=n;break;case"消防栓":t.pipeData.hydrant=n;break;case"三通":t.pipeData.threeCorss=n;break}})},g=async()=>{var o,a;t.layerIds=Z(f.view);const i=await x(t.layerIds);t.layerInfos=((a=(o=i.data)==null?void 0:o.result)==null?void 0:a.rows)||[]},I=async()=>{await g(),await w()},D=U(),L=async()=>{const i=await D.getAllStationOption();t.deviceData.waterQuality=i.filter(o=>o.data.type===s.SHUIZHIJIANCEZHAN).length,t.deviceData.pressure=i.filter(o=>[s.YALIJIANCEZHAN,s.CHELIUYAZHAN].indexOf(o.data.type)!==-1).length,t.deviceData.bigUser=i.filter(o=>o.data.type===s.DAYONGHU).length,t.deviceData.secondary=i.filter(o=>o.data.type===s.BENGZHAN).length,t.deviceData.flow=i.filter(o=>[s.CHELIUYAZHAN,s.LIULIANGJIANCEZHAN].indexOf(o.data.type)!==-1).length};return b(async()=>{var i;L(),f.view=await((i=d.value)==null?void 0:i.init({zoom:11,defaultFilter:"grayscale(0%) invert(100%) opacity(100%)",defaultFilterColor:"rgb(255 218 189)"}))}),S(()=>{var i;(i=d.value)==null||i.destroy(),f.view=void 0}),(i,o)=>(_(),h("div",q,[e(J,{ref_key:"refMap",ref:d,class:"map",tools:["pipe"],onPipeLoaded:I},null,512),r("div",X,[r("div",$,[r("div",z,[e(R,{"devie-data":y(t).deviceData,class:"device-group"},null,8,["devie-data"]),r("div",K,[e(P,{title:"供水量",type:"simple","title-width":180,style:{"border-radius":"18px 0 0 18px"}}),r("div",W,[(_(!0),h(k,null,H(y(t).lossRates,(a,c)=>(_(),E(Q,{key:c,data:a,class:"loss-rate-items__item"},null,8,["data"]))),128))])]),r("div",tt,[e(m,{class:"toprow-right__item",title:"设备统计"},{default:p(()=>[e(T,{"pipe-data":y(t).pipeData},null,8,["pipe-data"])]),_:1}),e(m,{class:"toprow-right__item",title:"抢修工作量","title-width":240},{default:p(()=>[e(F)]),_:1})])]),r("div",et,[e(m,{class:"bottomrow-item bottomrow-left",title:"流量监测"},{default:p(()=>[e(V)]),_:1}),e(m,{class:"bottomrow-item bottomrow-center",title:"压力监测"},{default:p(()=>[e(j)]),_:1})])]),r("div",it,[e(m,{class:"right__item xjwcl",title:"巡检与工单"},{default:p(()=>[e(M)]),_:1}),e(m,{class:"right__item gdlxsltj",title:"工单类型数量统计"},{default:p(()=>[e(Y)]),_:1}),e(m,{class:"right__item scgy",title:"水厂工艺"},{default:p(()=>[e(O)]),_:1})])])]))}}),ai=B(ot,[["__scopeId","data-v-6d4d5a87"]]);export{ai as default};
