<template>
  <div class="one-map-detail">
    <div v-loading="state.detailLoading" class="left">
      <div>
        <OrderStepTags
          :config="OrderStepTagsVale"
          style="margin-bottom: 20px"
        />
        <el-descriptions
          title=""
          :border="true"
          :column="2"
          style="margin-left: 20px"
        >
          <el-descriptions-item label="来电号码">
            13323344233
          </el-descriptions-item>
          <el-descriptions-item label="来电事件">
            2022-08-12 12：11：44
          </el-descriptions-item>
          <el-descriptions-item label="户号"> 53 </el-descriptions-item>
          <el-descriptions-item label="客户姓名"> 高文军 </el-descriptions-item>
          <el-descriptions-item label="联系电话">
            13323344233
          </el-descriptions-item>
          <el-descriptions-item label="联系地址">
            民安小区
          </el-descriptions-item>
          <el-descriptions-item label="区域"> 民安区 </el-descriptions-item>
          <el-descriptions-item label="工单类型"> 咨询 </el-descriptions-item>
          <el-descriptions-item label="业务主题">
            计划停水
          </el-descriptions-item>
          <el-descriptions-item label="紧急程度"> 一般 </el-descriptions-item>
        </el-descriptions>
      </div>
    </div>
    <div class="right">
      <div style="border-left: 2px solid #232323; margin-left: 20px">
        <el-tabs type="border-card">
          <el-tab-pane label="图片"> 图片 </el-tab-pane>
          <el-tab-pane label="视频"> 视频 </el-tab-pane>
          <el-tab-pane label="音频"> 音频 </el-tab-pane>
          <el-tab-pane label="附件"> 附件 </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import OrderStepTags from '../../components/components/OrderStepTags.vue';

const emit = defineEmits(['refresh', 'mounted']);
const OrderStepTagsVale = {
  status: 'SUBMIT',
  statusName: '提交上报',
  colum: false
};
const state = reactive<{
  detailLoading: boolean;
  curRow?: any;
}>({
  detailLoading: false
});
const refreshDetail = async (row) => {
  emit('refresh', { title: row.name });
  state.detailLoading = true;
  state.curRow = row;

  state.detailLoading = false;
};
defineExpose({
  refreshDetail
});
onMounted(() => {
  emit('mounted');
});
</script>
<style lang="scss" scoped>
.one-map-detail {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: row;
  .left {
    height: 100%;
    align-items: center;
    padding-right: 8px;
    display: flex;
  }
  .right {
    flex: 1;
    height: 100%;
    padding-left: 8px;
    border-left: 2px solid var(--el-border-color-lighter);
  }
}
</style>
