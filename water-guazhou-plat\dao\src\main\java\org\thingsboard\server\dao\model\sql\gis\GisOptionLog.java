package org.thingsboard.server.dao.model.sql.gis;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;
import java.util.Date;

@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.GIS_OPTION_LOG_TABLE)
@TableName(ModelConstants.GIS_OPTION_LOG_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class GisOptionLog {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.GIS_OPTION_LOG_TYPE)
    private String type;

    @Column(name = ModelConstants.GIS_OPTION_LOG_CONTENT)
    private String content;

    @Column(name = ModelConstants.GIS_OPTION_LOG_OPTION_USER)
    private String optionUser;

    @Column(name = ModelConstants.GIS_OPTION_LOG_OPTION_USER_NAME)
    private String optionUserName;

    @Column(name = ModelConstants.GIS_OPTION_LOG_OPTION_TYPE)
    private String optionType;

    @Column(name = ModelConstants.GIS_OPTION_LOG_OPTION_NAME)
    private String optionName;

    @Column(name = ModelConstants.GIS_OPTION_LOG_OPTION_TIME)
    private Date optionTime;

    @Column(name = ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;


}
