import{_ as C}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as S}from"./CardTable-rdWOL4_6.js";import{_ as k}from"./CardSearch-CB_HNR-Q.js";import{z as c,C as q,c as h,r as d,b as l,S as v,o as L,g as B,n as E,q as m}from"./index-r0dFAfgr.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";function V(i){return c({url:"/api/base/template/type/list",method:"get",params:i})}function A(i){return c({url:"/api/base/template/type/getDetail",method:"get",params:{id:i}})}function _(i){return c({url:"/api/base/template/type/add",method:"post",data:i})}function T(i){return c({url:"/api/base/template/type/edit",method:"post",data:i})}function F(i){return c({url:"/api/base/template/type/deleteIds",method:"delete",data:i})}const P={class:"wrapper"},z={__name:"baseTemplateType",setup(i){const g=h(),p=h(),x=d({labelWidth:"100px",filters:[{type:"input",label:"模板名称",field:"name",placeholder:"请输入模板名称",onChange:()=>o()},{type:"btn-group",btns:[{type:"primary",perm:!0,text:"查询",click:()=>o()},{perm:!0,type:"primary",text:"新增",click:()=>y()},{perm:!0,type:"danger",text:"批量删除",click:()=>b()}]}],defaultParams:{}}),s=d({columns:[{label:"模板名称",prop:"name"},{label:"模板编码",prop:"code"},{label:"分类",prop:"type"},{label:"绑定动画",prop:"bindingAnimation"},{label:"默认图元",prop:"defaultGraphicElement"},{label:"描述",prop:"description"}],dataList:[],operations:[{perm:!0,type:"primary",isTextBtn:!0,text:"查看详情",click:e=>D(e)},{perm:!0,type:"primary",isTextBtn:!0,text:"编辑",click:e=>y(e)},{perm:!0,type:"danger",isTextBtn:!0,text:"删除",click:e=>b(e)}],pagination:{total:0,page:1,align:"right",limit:20,handlePage:e=>{s.pagination.page=e,o()},handleSize:e=>{s.pagination.limit=e,o()}},handleSelectChange:e=>{s.selectList=e||[]}}),a=d({title:"新增模板类型",group:[{fields:[{type:"input",label:"模板名称",field:"name",rules:[{required:!0,message:"请输入模板名称"}]},{type:"input",label:"模板编码",field:"code",rules:[{required:!0,message:"请输入模板编码"}]},{type:"input",label:"分类",field:"type",rules:[{required:!0,message:"请输入分类"}]},{type:"input",label:"绑定动画",field:"bindingAnimation",rules:[{required:!0,message:"请输入绑定动画"}]},{type:"input",label:"默认图元",field:"defaultGraphicElement",rules:[{required:!0,message:"请输入默认图元"}]},{type:"textarea",label:"描述",field:"description",placeholder:"请输入描述信息"},{type:"input",label:"样式配置",field:"styleConfiguration",rules:[{required:!0,message:"请输入样式配置"}]},{type:"input",label:"分辨率适配",field:"resolutionAdaptation",rules:[{required:!0,message:"请输入分辨率适配"}]}]}],labelPosition:"top",defaultValue:{},dialogWidth:600,draggable:!0,showSubmit:!0,showCancel:!0,cancelText:"取消",submitText:"确定",submit:async e=>{var t;try{e.id?(await T(e),l.success("修改成功")):(await _(e),l.success("新增成功")),(t=p.value)==null||t.closeDialog(),o()}catch{l.error("操作失败")}}}),f=()=>{a.group[0].fields.forEach(e=>{e.disabled=!1,e.readonly=!1}),a.showSubmit=!0,a.showCancel=!0,a.cancelText="取消",a.submitText="确定",a.submit=async e=>{var t;try{e.id?(await T(e),l.success("修改成功")):(await _(e),l.success("新增成功")),(t=p.value)==null||t.closeDialog(),o()}catch{l.error("操作失败")}}},D=async e=>{var t,r;try{const n=await A(e.id),u=((t=n.data)==null?void 0:t.data)||n;f(),a.title="变更记录详情",a.defaultValue={...u},a.group[0].fields.forEach(w=>{w.disabled=!0}),a.showSubmit=!1,a.cancelText="关闭",(r=p.value)==null||r.openDialog()}catch{l.error("获取详情失败")}},y=e=>{var t;f(),e?(a.title="编辑模板类型",a.defaultValue={...e}):(a.title="新增模板类型",a.defaultValue={}),(t=p.value)==null||t.openDialog()},b=async e=>{try{const t=e?[e.id]:s.selectList.map(r=>r.id);if(!t.length){l.warning("请选择要删除的数据");return}await v("确定要删除选中的数据吗？"),await F(t),l.success("删除成功"),o()}catch(t){t!=="cancel"&&l.error("删除失败")}},o=async()=>{var e,t;try{const r=await V({page:s.pagination.page,size:s.pagination.limit,...((e=g.value)==null?void 0:e.queryParams)||{}}),n=((t=r.data)==null?void 0:t.data)||r;s.dataList=n.records||n,s.pagination.total=n.total||n.length||0}catch{l.error("数据加载失败")}};return L(()=>{o()}),(e,t)=>{const r=k,n=S,u=C;return B(),E("div",P,[m(r,{ref_key:"refSearch",ref:g,config:x},null,8,["config"]),m(n,{class:"card-table",config:s},null,8,["config"]),m(u,{ref_key:"refDialogForm",ref:p,config:a},null,8,["config"])])}}},j=q(z,[["__scopeId","data-v-54a49bcd"]]);export{j as default};
