/**
 * Copyright © 2016-2019 The Thingsboard Authors
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.service.transport;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.util.concurrent.Futures;
import com.google.common.util.concurrent.ListenableFuture;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.Device;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.dataSource.DataSourceType;
import org.thingsboard.server.common.data.id.DeviceId;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.security.DeviceCredentials;
import org.thingsboard.server.common.data.security.DeviceCredentialsType;
import org.thingsboard.server.dao.dashChart.DashChartService;
import org.thingsboard.server.dao.dataSource.DataSourceService;
import org.thingsboard.server.dao.device.DeviceCredentialsService;
import org.thingsboard.server.dao.device.DeviceService;
import org.thingsboard.server.dao.logicalFlow.BO.LogicFlowGateway;
import org.thingsboard.server.dao.logicalFlow.LogicalFlowService;
import org.thingsboard.server.dao.model.sql.*;
import org.thingsboard.server.dao.origin.OriginDataService;
import org.thingsboard.server.dao.project.ProjectRelationService;
import org.thingsboard.server.dao.relation.RelationService;
import org.thingsboard.server.gen.transport.TransportProtos.*;
import org.thingsboard.server.service.executors.DbCallbackExecutorService;
import org.thingsboard.server.service.rpc.DeviceRpcService;
import org.thingsboard.server.service.state.DeviceStateService;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

/**
 * Created by ashvayka on 05.10.18.
 */
@Slf4j
@Service
public class LocalTransportApiService implements TransportApiService {

    private static final ObjectMapper mapper = new ObjectMapper();

    @Autowired
    private DeviceService deviceService;

    @Autowired
    private RelationService relationService;

    @Autowired
    private DeviceCredentialsService deviceCredentialsService;

    @Autowired
    private DeviceStateService deviceStateService;

    @Autowired
    private DbCallbackExecutorService dbCallbackExecutorService;

    @Autowired
    private DashChartService dashChartService;

    @Autowired
    private DataSourceService dataSourceService;

    @Autowired
    private OriginDataService originDataService;

    @Autowired
    private DeviceRpcService deviceRpcService;

    @Autowired
    private LogicalFlowService logicalFlowService;

    @Autowired
    private ProjectRelationService projectRelationService;

    private ReentrantLock deviceCreationLock = new ReentrantLock();

    @Override
    public ListenableFuture<TransportApiResponseMsg> handle(TransportApiRequestMsg transportApiRequestMsg) {
        if (transportApiRequestMsg.hasValidateTokenRequestMsg()) {
            ValidateDeviceTokenRequestMsg msg = transportApiRequestMsg.getValidateTokenRequestMsg();
            return validateCredentials(msg.getToken(), DeviceCredentialsType.ACCESS_TOKEN);
        } else if (transportApiRequestMsg.hasValidateX509CertRequestMsg()) {
            ValidateDeviceX509CertRequestMsg msg = transportApiRequestMsg.getValidateX509CertRequestMsg();
            return validateCredentials(msg.getHash(), DeviceCredentialsType.X509_CERTIFICATE);
        } else if (transportApiRequestMsg.hasGetOrCreateDeviceRequestMsg()) {
            return handle(transportApiRequestMsg.getGetOrCreateDeviceRequestMsg());
        }
        return getEmptyTransportApiResponseFuture();
    }

    private ListenableFuture<TransportApiResponseMsg> validateCredentials(String credentialsId, DeviceCredentialsType credentialsType) {
        //TODO: Make async and enable caching
        DeviceCredentials credentials = deviceCredentialsService.findDeviceCredentialsByCredentialsId(credentialsId);
        if (credentials != null && credentials.getCredentialsType() == credentialsType) {
            return getDeviceInfo(credentials.getDeviceId(), credentials);
        } else {
            return getEmptyTransportApiResponseFuture();
        }
    }

    //todo 是否禁止检测到上传数据为未知设备时不允许创建
    private ListenableFuture<TransportApiResponseMsg> handle(GetOrCreateDeviceFromGatewayRequestMsg requestMsg) {
        DeviceId gatewayId = new DeviceId(new UUID(requestMsg.getGatewayIdMSB(), requestMsg.getGatewayIdLSB()));
        ListenableFuture<Device> gatewayFuture = deviceService.findDeviceByIdAsync(TenantId.SYS_TENANT_ID, gatewayId);
        return Futures.transform(gatewayFuture, gateway -> {
            deviceCreationLock.lock();
            try {
                Device device;
                device = deviceService.findDeviceByTenantIdAndName(gateway.getTenantId(), requestMsg.getDeviceName());
                if (device == null) {
                    device = deviceService.findDeviceById(new DeviceId(UUIDConverter.fromString(requestMsg.getDeviceName())));

//                    device = new Device();
//                    device.setTenantId(gateway.getTenantId());
//                    device.setName(requestMsg.getDeviceName());
//                    device.setType(requestMsg.getDeviceType());
//                    device.setCustomerId(gateway.getCustomerId());
//                    device = deviceService.saveDevice(device);
//                    relationService.saveRelationAsync(TenantId.SYS_TENANT_ID, new EntityRelation(gateway.getId(), device.getId(), "Created"));
//                    deviceStateService.onDeviceAdded(device);
//                    return null;
                }
                log.info("连接设备网关ID" + UUIDConverter.fromTimeUUID(gateway.getId().getId()) + "设备名称" + requestMsg.getDeviceName());
                return TransportApiResponseMsg.newBuilder()
                        .setGetOrCreateDeviceResponseMsg(GetOrCreateDeviceFromGatewayResponseMsg.newBuilder().setDeviceInfo(getDeviceInfoProto(device)).build()).build();

            } catch (JsonProcessingException e) {
                log.warn("[{}] Failed to lookup device by gateway id and name", gatewayId, requestMsg.getDeviceName(), e);
                throw new RuntimeException(e);
            } finally {
                deviceCreationLock.unlock();
            }
        }, dbCallbackExecutorService);
    }


    private ListenableFuture<TransportApiResponseMsg> getDeviceInfo(DeviceId deviceId, DeviceCredentials credentials) {
        return Futures.transform(deviceService.findDeviceByIdAsync(TenantId.SYS_TENANT_ID, deviceId), device -> {
            if (device == null) {
                log.trace("[{}] Failed to lookup device by id", deviceId);
                return getEmptyTransportApiResponse();
            }
            try {
                ValidateDeviceCredentialsResponseMsg.Builder builder = ValidateDeviceCredentialsResponseMsg.newBuilder();
                builder.setDeviceInfo(getDeviceInfoProto(device));
                if (!StringUtils.isEmpty(credentials.getCredentialsValue())) {
                    builder.setCredentialsBody(credentials.getCredentialsValue());
                }
                return TransportApiResponseMsg.newBuilder()
                        .setValidateTokenResponseMsg(builder.build()).build();
            } catch (JsonProcessingException e) {
                log.warn("[{}] Failed to lookup device by id", deviceId, e);
                return getEmptyTransportApiResponse();
            }
        });
    }


    private DeviceInfoProto getDeviceInfoProto(Device device) throws JsonProcessingException {
        return DeviceInfoProto.newBuilder()
                .setTenantIdMSB(device.getTenantId().getId().getMostSignificantBits())
                .setTenantIdLSB(device.getTenantId().getId().getLeastSignificantBits())
                .setDeviceIdMSB(device.getId().getId().getMostSignificantBits())
                .setDeviceIdLSB(device.getId().getId().getLeastSignificantBits())
                .setDeviceName(device.getName())
                .setDeviceType(device.getType())
                .setAdditionalInfo(mapper.writeValueAsString(device.getAdditionalInfo()))
                .build();
    }

    private ListenableFuture<TransportApiResponseMsg> getEmptyTransportApiResponseFuture() {
        return Futures.immediateFuture(getEmptyTransportApiResponse());
    }

    private TransportApiResponseMsg getEmptyTransportApiResponse() {
        return TransportApiResponseMsg.newBuilder()
                .setValidateTokenResponseMsg(ValidateDeviceCredentialsResponseMsg.getDefaultInstance()).build();
    }

    /**
     * 工控机定时拉取数据
     *
     * @param type
     * @param deviceId
     * @return
     */
    @Override
    public void getData(String type, DeviceId deviceId) {
        switch (type) {
            case DataConstants.DATA_TYPE_DASH_CHART: {
                List<DashChartEntity> dashChartEntities = dashChartService.findByOriginatorIdAndType(UUIDConverter.fromTimeUUID(deviceId.getId()), "local");
                deviceRpcService.sendDataToMqttDevice(UUIDConverter.fromTimeUUID(deviceId.getId()), dashChartEntities, DataConstants.DATA_TYPE_DASH_CHART);
                break;
            }
            case DataConstants.DATA_TYPE_DATA_SOURCE: {
                List<DataSourceEntity> dataSourceEntities = dataSourceService.findDataSourceByOriginator(UUIDConverter.fromTimeUUID(deviceId.getId()));
                List<Device> devices = deviceService.findDeviceByGateWayId(deviceId);
                if (devices != null) {
                    devices.forEach(device -> {
                        // REST API数据源
                        List<ProjectEntity> relations = projectRelationService.findProjectRelationByEntityTypeAndEntityId(
                                DataConstants.ProjectRelationEntityType.DEVICE.name(), UUIDConverter.fromTimeUUID(deviceId.getId()));
                        if (relations != null && !relations.isEmpty()) {
                            List<RestApiEntity> apiDataSourceList = dataSourceService.getRestApiByProjectId(relations.get(0).getId());
                            // 下发API数据源数据
                            for (RestApiEntity restApiEntity : apiDataSourceList) {
                                List<DataSourceEntity> apiDatasourceList = dataSourceService.findByProjectIdAndType(restApiEntity.getId(), DataSourceType.RESTAPI_SOURCE);
                                dataSourceEntities.addAll(apiDatasourceList);
                            }
                        }
                        dataSourceEntities.addAll(dataSourceService.findDataSourceByOriginator(UUIDConverter.fromTimeUUID(device.getUuidId())));
                    });
                }
                deviceRpcService.sendDataToMqttDevice(UUIDConverter.fromTimeUUID(deviceId.getId()), dataSourceEntities, DataConstants.DATA_TYPE_DATA_SOURCE);
                break;
            }
            case DataConstants.DATA_TYPE_LOGIC: {
                List<LogicFlowGateway> logicFlowGateways = new ArrayList<>();
                List<LogicalFlow> logicalFlow = logicalFlowService.findAll("LOCAL", UUIDConverter.fromTimeUUID(deviceId.getId()))
                        // 2020-09-30 由工控机判断是否更新版本
                        /*.stream().filter(logical ->
                            "enable".equalsIgnoreCase(logical.getStatus())
                        ).collect(Collectors.toList())*/;
                logicalFlow.forEach(logicalFlow1 -> {
                    List<LogicalFlowNode> nodes = logicalFlowService.findLogicalFlowNodeByLogicalFlowId(logicalFlow1.getId());
                    logicFlowGateways.add(LogicFlowGateway.builder().logicalFlow(logicalFlow1)
                            .logicalFlowNodes(nodes).build());
                });
                deviceRpcService.sendDataToMqttDevice(UUIDConverter.fromTimeUUID(deviceId.getId()), logicFlowGateways, DataConstants.DATA_TYPE_LOGIC);
                break;
            }
            //中间变量/内置/REST API 数据源数据同步
            case DataConstants.DATA_TYPE_MID_DATA_SOURCE: {
                // 中间变量
                List<DataSourceEntity> dataSourceEntities = dataSourceService.findByProjectIdAndType(UUIDConverter.fromTimeUUID(deviceId.getId()), DataSourceType.MID_SOURCE);
                List<OriginDataEntity> originDataEntities = new ArrayList<>();
                dataSourceEntities.forEach(dataSourceEntity -> {
                    originDataEntities.addAll(originDataService.getOriginDataFormId(dataSourceEntity.getId()));
                });
                deviceRpcService.sendDataToMqttDevice(UUIDConverter.fromTimeUUID(deviceId.getId()), originDataEntities, DataConstants.DATA_TYPE_MID_DATA_SOURCE);

                // 内置变量
                List<DataSourceEntity> dataSourceEntities1 = dataSourceService.findByProjectIdAndType(UUIDConverter.fromTimeUUID(deviceId.getId()), DataSourceType.SYSTEM_SOURCE);
                List<OriginDataEntity> originDataEntities1 = new ArrayList<>();
                dataSourceEntities1.forEach(dataSourceEntity -> {
                    if ("no data".equals(dataSourceEntity.getProperty())) {
                        originDataEntities1.add(
                                OriginDataEntity.builder()
                                        .value("no data")
                                        .updateTime(System.currentTimeMillis())
                                        .dataSourceId(dataSourceEntity.getId())
                                        .build()
                        );
                    } else {
                        originDataEntities1.addAll(originDataService.getOriginDataFormId(dataSourceEntity.getId()));
                    }
                });
                deviceRpcService.sendDataToMqttDevice(UUIDConverter.fromTimeUUID(deviceId.getId()), originDataEntities1, DataConstants.DATA_TYPE_MID_DATA_SOURCE);

                // REST API数据源
                List<ProjectEntity> relations = projectRelationService.findProjectRelationByEntityTypeAndEntityId(
                        DataConstants.ProjectRelationEntityType.DEVICE.name(), UUIDConverter.fromTimeUUID(deviceId.getId()));
                if (relations != null && !relations.isEmpty()) {
                    List<RestApiEntity> apiDataSourceList = dataSourceService.getRestApiByProjectId(relations.get(0).getId());
                    List<OriginDataEntity> apiOriginDataEntities = new ArrayList<>();
                    // 下发API数据源以及API数据源数据
                    for (RestApiEntity restApiEntity : apiDataSourceList) {
                        List<DataSourceEntity> apiDatasourceList = dataSourceService.findByProjectIdAndType(restApiEntity.getId(), DataSourceType.RESTAPI_SOURCE);
                        apiDatasourceList.forEach(dataSourceEntity -> {
                            apiOriginDataEntities.add(originDataService.getLastOriginDataFormId(dataSourceEntity.getId()));
                        });
                    }
                    deviceRpcService.sendDataToMqttDevice(UUIDConverter.fromTimeUUID(deviceId.getId()), apiOriginDataEntities, DataConstants.DATA_TYPE_MID_DATA_SOURCE);
                }
                break;
            }
        }
    }
}
