<template>
  <div class="home-bg" :class="[hasFirstScreen ? 'none-bg' : '']">
    <div class="home-main overlay-y">
      <component :is="firstScreenComponent" v-if="hasFirstScreen"></component>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { useUserStore } from '@/store';

const modules = import.meta.glob('../**/*.vue');
const firstScreen = useUserStore().tenantInfo?.additionalInfo?.firstScreen;
const hasFirstScreen = ref(!!(firstScreen ?? false));
const firstScreenComponent = ref<any>(null);
const backToDefault = () => {
  try {
    firstScreenComponent.value = markRaw(
      defineAsyncComponent(() => import(/* @vite-ignore */ './index-bak.vue'))
    );
  } catch (error) {
    hasFirstScreen.value = false;
  }
};
const changeComponent = () => {
  if (hasFirstScreen) {
    const link: any = modules[/* @vite-ignore */ `../${firstScreen}.vue`];
    try {
      firstScreenComponent.value = markRaw(defineAsyncComponent(link));
    } catch (error) {
      backToDefault();
    }
  }
};
onMounted(() => {
  changeComponent();
});
</script>
<style lang="scss" scoped>
.home-bg {
  height: 100%;
  width: 100%;
  background-image: url('../../assets/images/首页.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  &.none-bg {
    background: none;
    height: 100%;
    width: 100%;
  }
}
.home-main {
  width: 100%;
  height: 100%;
}
home-main::-webkit-scrollbar {
  display: none;
}
</style>
