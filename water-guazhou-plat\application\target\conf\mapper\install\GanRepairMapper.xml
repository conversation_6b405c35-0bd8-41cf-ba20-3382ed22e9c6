<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.install.GanRepairMapper">

    <select id="getList" resultType="org.thingsboard.server.dao.model.sql.install.GanRepair">
        select a.*, b.creator  as auditor,
        b.remark as auditorRemark,
        b.create_time as auditTime,
        d.first_name as processName,
        case when a.status = '2' then '申请被驳回' when c.create_time is null then '办理中'
        when date_part('hour', c.create_time - a.create_time) &lt; 1 then '&lt;1小时'
        else date_part('hour', c.create_time - a.create_time)||'小时' end as processTime,


        case when a.status = '2' then b.create_time else c.create_time end as completeTime
        from tb_gan_repair a
        left join tb_gan_repair_c b on a.id = b.pid and b.step_no = 2
        left join tb_gan_repair_c c on a.id = c.pid and c.step_no = 3
        left join tb_user d on b.creator = d.id
        <where>
            <if test="param.userId != null and param.userId != ''">
                and a.user_id = #{param.userId}
            </if>
            <if test="param.status != null and param.status != ''">
                and a.status in
                <foreach collection="param.status.split(',')" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="param.name != null and param.name != ''">
                 and a.name like '%' || #{param.name} || '%'
            </if>
            <if test="param.code != null and param.code != ''">
                 and code like '%' || #{param.code} || '%'
            </if>
            <if test="param.idCard != null and param.idCard != ''">
                 and id_card like '%' || #{param.idCard} || '%'
            </if>
            <if test="param.phone != null and param.phone != ''">
                 and a.phone like '%' || #{param.phone} || '%'
            </if>
            <if test="param.address != null and param.address != ''">
                 and a.address like '%' || #{param.address} || '%'
            </if>
            <if test="param.startTime != null">
                and a.create_time &gt;= to_timestamp(#{param.startTime} / 1000)
            </if>
            <if test="param.endTime != null">
                and a.create_time &lt;= to_timestamp(#{param.endTime} / 1000)
            </if>
        </where>
        order by a.create_time desc
    </select>

    <select id="getMyComplete" resultType="org.thingsboard.server.dao.model.sql.install.GanRepair">
        select distinct a.*  , b.create_time as completeTime, b.step_no as stepNo
        from tb_gan_repair a
         left join tb_gan_repair_c b on a.id = b.pid
        where  b.creator = #{param.userId}
    </select>

    <select id="getNotCompleteList" resultType="org.thingsboard.server.dao.model.sql.install.GanRepair">
        select * from tb_gan_repair where status = '5' and is_send_msg = '0'
    </select>
</mapper>