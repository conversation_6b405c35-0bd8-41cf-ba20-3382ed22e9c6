import{U as v}from"./pe-B8dP0-Ut.js";import{c2 as V,h as D,b$ as T,aq as U,dx as k,d9 as q}from"./MapView-DaoQedLH.js";import{R as J,$ as A}from"./index-r0dFAfgr.js";import{i as F}from"./scaleUtils-DgkF6NQH.js";import{n as R}from"./floorFilterUtils-DZ5C6FQv.js";import{i as G}from"./sublayerUtils-bmirCD0I.js";import{y as P,u as Z}from"./IdentifyResult-4DxLVhTm.js";const N=r=>r.spatialReference.wkid||JSON.stringify(r.spatialReference);function B(r,e){const{dpi:a,gdbVersion:l,geometry:t,geometryPrecision:s,height:p,layerOption:g,mapExtent:u,maxAllowableOffset:$,returnFieldName:c,returnGeometry:b,returnUnformattedValues:y,returnZ:O,spatialReference:x,timeExtent:h,tolerance:n,width:E}=r.toJSON(),{dynamicLayers:f,layerDefs:i,layerIds:m}=M(r),S=e&&J(e.geometry)?e.geometry:null,o={geometryPrecision:s,maxAllowableOffset:$,returnFieldName:c,returnGeometry:b,returnUnformattedValues:y,returnZ:O,tolerance:n},d=S&&S.toJSON()||t;if(o.imageDisplay=`${E},${p},${a}`,l&&(o.gdbVersion=l),d&&(delete d.spatialReference,o.geometry=JSON.stringify(d),o.geometryType=V(d)),x?o.sr=x.wkid||JSON.stringify(x):d&&d.spatialReference?o.sr=N(d):u&&u.spatialReference&&(o.sr=N(u)),o.time=h?[h.start,h.end].join(","):null,u){const{xmin:w,ymin:I,xmax:L,ymax:j}=u;o.mapExtent=`${w},${I},${L},${j}`}return i&&(o.layerDefs=i),f&&!i&&(o.dynamicLayers=f),o.layers=g==="popup"?"visible":g,m&&!f&&(o.layers+=`:${m.join(",")}`),o}function M(r){var x,h;const{mapExtent:e,floors:a,width:l,sublayers:t,layerIds:s,layerOption:p,gdbVersion:g}=r,u=(h=(x=t==null?void 0:t.find(n=>n.layer!=null))==null?void 0:x.layer)==null?void 0:h.serviceSublayers,$=p==="popup",c={},b=F({extent:e,width:l,spatialReference:e==null?void 0:e.spatialReference}),y=[],O=n=>{const E=b===0,f=n.minScale===0||b<=n.minScale,i=n.maxScale===0||b>=n.maxScale;if(n.visible&&(E||f&&i))if(n.sublayers)n.sublayers.forEach(O);else{if((s==null?void 0:s.includes(n.id))===!1||$&&(!n.popupTemplate||!n.popupEnabled))return;y.unshift(n)}};if(t==null||t.forEach(O),t&&!y.length)c.layerIds=[];else{const n=G(y,u,g),E=y.map(f=>{const i=R(a,f);return f.toExportImageJSON(i)});if(n)c.dynamicLayers=JSON.stringify(E);else{if(t){let i=y.map(({id:m})=>m);s&&(i=i.filter(m=>s.includes(m))),c.layerIds=i}else s!=null&&s.length&&(c.layerIds=s);const f=z(a,y);if(J(f)&&f.length){const i={};for(const m of f)m.definitionExpression&&(i[m.id]=m.definitionExpression);Object.keys(i).length&&(c.layerDefs=JSON.stringify(i))}}}return c}function z(r,e){const a=!!(r!=null&&r.length),l=e.filter(t=>t.definitionExpression!=null||a&&t.floorInfo!=null);return l.length?l.map(t=>{const s=R(r,t),p=D(s,t.definitionExpression);return{id:t.id,definitionExpression:A(p,void 0)}}):null}async function re(r,e,a){const l=(e=H(e)).geometry?[e.geometry]:[],t=T(r);return t.path+="/identify",U(l).then(s=>{const p=B(e,{geometry:s&&s[0]}),g=k({...t.query,f:"json",...p}),u=q(g,a);return v(t.path,u).then(C).then($=>K($,e.sublayers))})}function C(r){const e=r.data;return e.results=e.results||[],e.exceededTransferLimit=!!e.exceededTransferLimit,e.results=e.results.map(a=>P.fromJSON(a)),e}function H(r){return r=Z.from(r)}function K(r,e){if(!(e!=null&&e.length))return r;const a=new Map;function l(t){a.set(t.id,t),t.sublayers&&t.sublayers.forEach(l)}e.forEach(l);for(const t of r.results)t.feature.sourceLayer=a.get(t.layerId);return r}export{re as f};
