<template>
  <DialogForm ref="refDialog" :config="DialogFormConfig">
    <div class="add-more">
      <Button
        class="add-more-btn"
        :config="{
          perm: true,
          text: '添加过滤条件',
          iconifyIcon: 'ep:plus',
          isTextBtn: true,
          click: () => addTableRow()
        }"
      ></Button>
    </div>
    <div class="form-list">
      <div v-for="(row, i) in TableConfig.dataList" :key="i" class="form-row">
        <div v-if="i > 0" class="logic">
          <label class="label"> 逻辑 </label>
          <FormItem
            v-model="row['logic']"
            :config="{ type: 'radio', options: state.logicOption }"
          ></FormItem>
        </div>
        <div class="form-items">
          <FormItem
            v-model="row['field']"
            :config="{
              type: 'select',
              placeholder: '请选择字段',
              options: state.fieldConfig
            }"
          ></FormItem>
          <FormItem
            v-model="row['calc']"
            :config="{
              type: 'select',
              placeholder: '请选择计算方式',
              options: state.calcOptions
            }"
          ></FormItem>
          <FormItem
            v-model="row['value']"
            :config="{
              type: 'input',
              placeholder: '请输入内容',
              disabled: () =>
                [ECalcOperator.BUWEIKONG, ECalcOperator.WEIKONG].includes(
                  row['calc']
                )
            }"
          ></FormItem>
          <div class="button" @click="() => removeRow(row, i)">x</div>
        </div>
      </div>
    </div>
  </DialogForm>
</template>
<script lang="ts" setup>
import {
  ECalcOperator,
  EGisConditionLogic,
  OCalcOperatorVal,
  OGisConditionLogicVal
} from '@/utils/arcgis/utils/SqlHelper';
import { GetFieldConfig } from '@/api/mapservice/fieldconfig';
import { formatTree } from '@/utils/GlobalHelper';
// 导入GeoServer相关工具函数
import { GetFieldConfig as GetFieldConfigByGeoserver } from '@/utils/geoserver/wfsUtils';

// 定义全局变量，判断是否使用GeoServer模式
const isGeoServerMode = (window as any).GIS_SERVER_SWITCH === true;

type IGISGeneratorRow = {
  logic: string;
  field: string;
  calc: string;
  value: string;
};
interface ISQLGeneratorSubmit {
  (e: 'submit', data: { list: IGISGeneratorRow[]; sql: string }): any;
}

const props = defineProps<{
  layerName?: string; // 添加图层名称属性
}>();

const emit = defineEmits<ISQLGeneratorSubmit>();
const refDialog = ref<IDialogFormIns>();
const state = reactive<{
  logicOption: any[];
  fieldConfig: any[];
  calcOptions: NormalOption[];
}>({
  logicOption: [
    { label: '或', value: EGisConditionLogic.OR },
    { label: '且', value: EGisConditionLogic.AND }
  ],
  fieldConfig: [
    // 初始为空，将通过 loadFieldConfig 方法根据选择的图层动态加载
  ],
  calcOptions: [
    { label: '等于', value: ECalcOperator.DENGYU },
    { label: '不等于', value: ECalcOperator.BUDENGYU },
    { label: '大于', value: ECalcOperator.DAYU },
    { label: '大于等于', value: ECalcOperator.DAYUDENGYU },
    { label: '小于等于', value: ECalcOperator.XIAOYUDENGYU },
    { label: '小于', value: ECalcOperator.XIAOYU },
    { label: '为空', value: ECalcOperator.WEIKONG },
    { label: '不为空', value: ECalcOperator.BUWEIKONG },
    { label: '包含', value: ECalcOperator.BAOHAN },
    { label: '不包含', value: ECalcOperator.BUBAOHAN }
  ]
});
const TableConfig = reactive<ITable>({
  dataList: [],
  height: 'none',
  columns: [
    {
      label: '字段',
      prop: 'field',
      formItemConfig: {
        type: 'select',
        options: state.fieldConfig
      }
    },
    {
      label: '运行符',
      prop: 'calc',
      formItemConfig: {
        type: 'select',
        options: state.calcOptions
      }
    },
    {
      label: '值',
      prop: 'value',
      formItemConfig: {
        type: 'input'
      }
    }
  ],
  pagination: {
    hide: true
  },
  operations: [
    { perm: true, click: (row, index) => removeRow(row, index), text: 'x' }
  ]
});

// 根据图层名称加载字段配置
const loadFieldConfig = async (layerName: string) => {
  if (!layerName) return;
  
  try {
    console.log('加载图层字段:', layerName);
    
    // 根据不同的模式选择不同的API
    if (isGeoServerMode) {
      // 使用GeoServer API - 基于EditPipeAttrs.vue的实现
      const fields = await GetFieldConfigByGeoserver(layerName);
      console.log('GeoServer字段响应:', fields);
      
      if (fields && fields.data && fields.data.featureTypes && fields.data.featureTypes[0]) {
        // 获取字段列表
        const properties = fields.data.featureTypes[0].properties || [];
        
        // 过滤出可用的字段
        const filteredFields = properties.filter(item => {
          const type = item.type.split(':')[1]?.toLowerCase() || '';
          // 排除几何字段和不支持的类型
          return item.name !== 'the_geom' && 
                 item.name !== 'geom' &&
                 ['int', 'long', 'double', 'float', 'string', 'date'].indexOf(type) !== -1;
        });
        
        // 格式化为下拉选项
        state.fieldConfig = filteredFields.map(item => ({
          label: item.name,
          value: item.name
        }));
      } else {
        console.warn('GeoServer字段响应格式无效');
        state.fieldConfig = [];
      }
    } else {
      // 原始的ArcGIS API
      const res = await GetFieldConfig(layerName);
      
      // 将字段配置格式化为下拉选项格式
      const fields = res.data?.result?.rows || [];
      state.fieldConfig = formatTree(fields, {
        id: 'name',
        label: 'alias',
        value: 'name'
      });
    }
    
    console.log('字段配置已加载:', state.fieldConfig);
  } catch (error) {
    console.error('获取字段配置失败:', error);
    state.fieldConfig = [
      { label: 'OBJECTID', value: 'OBJECTID' }
    ];
  }
};

// 监听图层名称变化
watch(
  () => props.layerName,
  async (newVal) => {
    if (newVal) {
      await loadFieldConfig(newVal);
    } else {
      state.fieldConfig = [];
    }
  },
  { immediate: true }
);

const addTableRow = () => {
  const logic = TableConfig.dataList.length > 0 ? '1' : '';
  const defaultField = state.fieldConfig[0]?.value || 'OBJECTID';
  
  TableConfig.dataList.push({
    logic,
    field: defaultField,
    calc: ECalcOperator.DENGYU,
    value: ''
  });
};

const removeRow = (row, index) => {
  TableConfig.dataList.splice(index, 1);
};

const genSql = () => {
  let sql = '';
  TableConfig.dataList.map((item: IGISGeneratorRow, i: number) => {
    if (item.logic && i > 0) {
      // GeoServer模式下，AND和OR需要大写
      const logicOperator = isGeoServerMode ? 
        OGisConditionLogicVal[item.logic].toUpperCase() : 
        OGisConditionLogicVal[item.logic];
      sql += ` ${logicOperator} `;
    }

    // 根据不同的操作符生成SQL语句
    if (isGeoServerMode) {
      // GeoServer模式下的CQL过滤表达式
      // 在GeoServer中，字段名可能需要用双引号括起来
      const fieldName = isGeoServerMode ? `"${item.field}"` : item.field;
      
      switch (item.calc) {
        case ECalcOperator.DENGYU:
          sql += `${fieldName} = '${item.value ?? ''}'`;
          break;
        case ECalcOperator.BUDENGYU:
          sql += `${fieldName} <> '${item.value ?? ''}'`;
          break;
        case ECalcOperator.DAYU:
          sql += `${fieldName} > '${item.value ?? ''}'`;
          break;
        case ECalcOperator.DAYUDENGYU:
          sql += `${fieldName} >= '${item.value ?? ''}'`;
          break;
        case ECalcOperator.XIAOYUDENGYU:
          sql += `${fieldName} <= '${item.value ?? ''}'`;
          break;
        case ECalcOperator.XIAOYU:
          sql += `${fieldName} < '${item.value ?? ''}'`;
          break;
        case ECalcOperator.WEIKONG:
          sql += `${fieldName} IS NULL`;
          break;
        case ECalcOperator.BUWEIKONG:
          sql += `${fieldName} IS NOT NULL`;
          break;
        case ECalcOperator.BAOHAN:
          sql += `${fieldName} LIKE '%${item.value ?? ''}%'`;
          break;
        case ECalcOperator.BUBAOHAN:
          sql += `${fieldName} NOT LIKE '%${item.value ?? ''}%'`;
          break;
        default:
          break;
      }
    } else {
      // ArcGIS模式下的SQL表达式
      switch (item.calc) {
        case ECalcOperator.DENGYU:
        case ECalcOperator.BUDENGYU:
        case ECalcOperator.DAYU:
        case ECalcOperator.DAYUDENGYU:
        case ECalcOperator.XIAOYUDENGYU:
        case ECalcOperator.XIAOYU:
          sql += `${item.field} ${OCalcOperatorVal[item.calc]} '${item.value ?? ''}'`;
          break;
        case ECalcOperator.WEIKONG:
        case ECalcOperator.BUWEIKONG:
          sql += `${item.field} ${OCalcOperatorVal[item.calc]}`;
          break;
        case ECalcOperator.BAOHAN:
        case ECalcOperator.BUBAOHAN:
          sql += `${item.field} ${OCalcOperatorVal[item.calc]} '%${item.value ?? ''}%'`;
          break;
        default:
          break;
      }
    }
  });
  return sql;
};

const DialogFormConfig = reactive<IDialogFormConfig>({
  dialogWidth: 800,
  title: '属性过滤',
  group: [],
  submit: () => {
    emit('submit', { list: TableConfig.dataList, sql: genSql() });
    refDialog.value?.closeDialog();
  }
});

const clear = () => {
  TableConfig.dataList = [];
};

const openDialog = () => {
  // 打开对话框前重新加载字段配置
  if (props.layerName) {
    loadFieldConfig(props.layerName);
  }
  refDialog.value?.openDialog();
};

const closeDialog = () => {
  refDialog.value?.closeDialog();
};

const Submit = () => {
  emit('submit', { list: TableConfig.dataList, sql: genSql() });
};

defineExpose({
  openDialog,
  closeDialog,
  genSql,
  refDialog,
  TableConfig,
  clear,
  Submit,
  loadFieldConfig
});
</script>
<style lang="scss" scoped>
.sql-generator {
  width: 100%;
}
.add-more {
  width: 100%;
  display: flex;
  .add-more-btn {
    margin-left: auto;
  }
}
.form-list {
  .form-row {
    .logic {
      display: flex;
      width: 100%;
      align-items: center;
      .label {
        word-break: keep-all;
        padding-right: 12px;
      }
    }
    .form-items {
      display: flex;
      .button {
        cursor: pointer;
        width: 80px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
}
</style>
