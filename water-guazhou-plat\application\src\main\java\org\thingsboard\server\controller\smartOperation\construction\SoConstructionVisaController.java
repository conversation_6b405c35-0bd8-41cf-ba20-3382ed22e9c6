package org.thingsboard.server.controller.smartOperation.construction;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionVisa;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionVisaContainer;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionVisaPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionVisaSaveRequest;
import org.thingsboard.server.dao.util.imodel.response.ExcelFileInfo;
import org.thingsboard.server.dao.util.reflection.ExceptionUtils;
import org.thingsboard.server.dao.construction.SoConstructionVisaService;
import org.thingsboard.server.utils.imodel.annotations.IStarController2;

import java.util.List;

@IStarController2
@RequestMapping("/api/so/constructionVisa")
public class SoConstructionVisaController extends BaseController {
    @Autowired
    private SoConstructionVisaService service;


    @GetMapping
    public IPage<SoConstructionVisaContainer> findAllConditional(SoConstructionVisaPageRequest request) {
        return service.findAllConditional(request);
    }

    @GetMapping("/export/excel")
    public ExcelFileInfo exportExcel(SoConstructionVisaPageRequest request) throws ThingsboardException {
        if (request.getConstructionCode() == null) {
            ExceptionUtils.silentThrow("所属工程编号未传入");
        }
        List<SoConstructionVisaContainer> records = findAllConditional(request).getRecords();
        List<SoConstructionVisa> items = null;
        if (records.size() > 0) {
            items = records.get(0).getItems();
        }
        return ExcelFileInfo.of(
                        String.format("工程%s-签证信息", request.getConstructionCode()), items
                ).
                nextTitle("code", "签证编号").
                nextTitle("constructionName", "工程名称").
                nextTitle("address", "施工地点").
                nextTitle("constructOrganization", "施工单位").
                nextTitle("constructTimeName", "施工日期").
                nextTitle("buildOrganization", "建设单位").
                nextTitle("supervisorOrganization", "监理单位").
                nextTitle("auditOrganization", "审计单位").
                nextTitle("creatorName", "添加人").
                nextTitle("createTimeName", "添加时间");
    }

    @GetMapping("/export/global/excel")
    public ExcelFileInfo exportGlobalExcel(SoConstructionVisaPageRequest request) {
        return ExcelFileInfo.of("工程签证管理列表", findAllConditional(request).getRecords())
                .nextTitle("constructionCode", "工程编号")
                .nextTitle("constructionName", "工程名称")
                .nextTitle("firstpartOrganization", "业主单位")
                // .nextTitle("estimate", "施工单位")
                .nextTitle("secondpartOrganization", "设计单位")
                // .nextTitle("expectEndTimeStr", "监理单位")
                .nextTitle("contractTotalCost", "合同总金额(万元)");
    }

    @PostMapping
    public SoConstructionVisa save(@RequestBody SoConstructionVisaSaveRequest req) {
        req.preCheckUpdate(() -> !service.isComplete(req.getConstructionCode(), req.tenantId()), "已完成不可更改");
        return service.save(req);
    }

    @PostMapping("/{constructionCode}/complete")
    public boolean complete(@PathVariable String constructionCode) throws ThingsboardException {
        String userId = UUIDConverter.fromTimeUUID(getCurrentUser().getUuidId());
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return service.complete(constructionCode,userId, tenantId);
    }

    // @PatchMapping("/{id}")
    public boolean edit(@RequestBody SoConstructionVisaSaveRequest req, @PathVariable String id) {
        if (StringUtils.isNotEmpty(req.getCode()) && service.isCodeExists(req.getCode(), req.tenantId(), req.getId())) {
            ExceptionUtils.silentThrow("编码重复");
        }
        return service.update(req.unwrap(id));
    }

    @DeleteMapping("/{id}")
    public boolean delete(@PathVariable String id) {
        if (service.isComplete(id)) {
            ExceptionUtils.silentThrow("已完成，不可删除");
        }
        return service.delete(id);
    }
}