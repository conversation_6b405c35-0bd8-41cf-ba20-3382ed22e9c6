import { requestPipe } from '@/plugins/axios/gisService';
import { request } from '@/plugins/axios/gis';
import { useGisStore } from '@/store';
/**
 * 执行gis的login
 * @returns
 */
export const AutoLogin = () => {
  return request({
    url: '/api/webapp/login',
    method: 'post',
    data: {
      username: '0000303',
      password: '111111'
    }
  });
};
/**
 * 查询操作日志
 * @param params
 * @returns
 */
export const GetGisLogs = () => {
  return request({
    url: '/api/webapp/log',
    method: 'get'
  });
};
/**
 * (方法说明)获取图层的要素类别名
 * @method (方法名)
 * @for (所属类名)
 * @param {(参数类型)} (参数名) (参数说明)
 * @return {(返回值类型)} (返回值说明)
 */
export const queryLayerClassName = (layerid: number | number[]) => {
  const ids = layerid instanceof Array ? layerid : [layerid];
  return requestPipe({
    url:
      window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService +
      '/exts/TFGeoAPISOE/getLayerInfor',
    method: 'post',
    params: {
      usertoken: useGisStore().gToken,
      layerids: JSON.stringify(ids),
      f: 'json'
    }
  });
};
/**
 * 查询明细
 * @param id
 * @returns
 */
export const GetPipeLayerIds = () => {
  return requestPipe({
    url: `${window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService}/exts/TFGeoAPISOE/findLayers`,
    method: 'post',
    params: {
      usertoken: useGisStore().gToken,
      fieldnames: JSON.stringify(['OBJECTID']),
      f: 'json'
    }
  });
};
/**
 * FeatureServer图层信息
 * @returns
 */
export const getPipeFeatureLayerInfo = (layerId: number) => {
  return requestPipe({
    url: `${window.SITE_CONFIG.GIS_CONFIG.gisPipeFeatureServiceFeatureServer}/${layerId}?f=json`,
    method: 'get'
  });
};
