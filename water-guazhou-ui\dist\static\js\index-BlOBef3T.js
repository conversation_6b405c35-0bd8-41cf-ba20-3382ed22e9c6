import{d as b,b3 as k,c as x,r as M,am as C,a8 as R,ch as i,o as B,g as a,n,q as V,F as d,h as f,aB as m,aJ as g,an as _,i as p,aw as y,j as E,cV as S,bV as q,C as D}from"./index-r0dFAfgr.js";import{_ as F}from"./SidebarItem.vue_vue_type_style_index_0_lang-B_vIMwLB.js";const N=b({__name:"index",setup(j){const t=k(),h=x(),e=M({activePath:t.currentRoute.value.fullPath,showMenu:!1});C(()=>t.currentRoute.value.fullPath,()=>{e.activePath!==t.currentRoute.value.fullPath&&(e.activePath=t.currentRoute.value.fullPath)});const o=R(()=>{var r;return[(r=i().routers.filter(u=>u.hidden!==!0)[0])==null?void 0:r.path]});return B(()=>{e.showMenu=!0}),(r,u)=>{const v=S,w=q;return a(),n("div",{class:y(["sidebar-container",{dark:p(E)().isDark}])},[V(w,{class:"side-menu-scrollbar","wrap-class":"scrollbar-wrapper"},{default:d(()=>[e.showMenu?(a(),f(v,{key:o.value[0],ref_key:"refMenu",ref:h,mode:"vertical",router:"","show-timeout":200,"default-active":e.activePath,"unique-opened":!0,"default-openeds":o.value},{default:d(()=>[(a(!0),n(m,null,g(p(i)().routers,(s,P)=>{var l,c;return a(),n(m,null,[(l=s.children)!=null&&l.length?(a(),f(F,{key:P,item:s,"is-nest":!1,icon:(c=s.meta)==null?void 0:c.icon},null,8,["item","icon"])):_("",!0)],64)}),256))]),_:1},8,["default-active","default-openeds"])):_("",!0)]),_:1})],2)}}}),I=D(N,[["__scopeId","data-v-e663fb11"]]);export{I as default};
