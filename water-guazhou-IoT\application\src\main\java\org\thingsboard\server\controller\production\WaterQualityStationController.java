package org.thingsboard.server.controller.production;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.Utils.DateUtils;
import org.thingsboard.server.common.data.device.DeviceFullData;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.client.StationFeignClient;
import org.thingsboard.server.dao.model.VO.DeviceDataTableInfoVO;
import org.thingsboard.server.dao.model.VO.DynamicTableVO;
import org.thingsboard.server.dao.model.sql.StationEntity;
import org.thingsboard.server.dao.production.ProductionService;
import org.thingsboard.server.dao.stationData.StationDataService;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;
import org.thingsboard.server.utils.ExcelUtil;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("api/waterQualityStation")
public class WaterQualityStationController extends BaseController {

    @Autowired
    private ProductionService productionService;

    @Autowired
    private StationFeignClient stationFeignClient;

    @Autowired
    private StationDataService stationDataService;

    @GetMapping("getList")
    public IstarResponse getList(@RequestParam(required = false, defaultValue = "") String projectId,
                                 @RequestParam(required = false, defaultValue = "") String name,
                                 @RequestParam String status) throws ThingsboardException {
        return IstarResponse.ok(productionService.getList(DataConstants.StationType.WATER_QUALITY_MONITORING.getValue(), projectId, name, status, getTenantId()));
    }

    /**
     * 查询指定站点的流量监测详情数据
     */
    @GetMapping("gis/getDataDetail")
    public IstarResponse getDataDetail(@RequestParam String stationId) throws ThingsboardException {
        JSONObject waterSupplyDetail = new JSONObject();

        // 浊度、余氯、ph
        List<String> attrList = new ArrayList<>();
        attrList.add(DataConstants.DeviceAttrType.TURBIDITY.getValue());
        attrList.add(DataConstants.DeviceAttrType.REMAINDER.getValue());
        attrList.add(DataConstants.DeviceAttrType.PH.getValue());
        // 今日时间
        Calendar instance = Calendar.getInstance();
        instance.set(Calendar.HOUR_OF_DAY, 0);
        instance.set(Calendar.MINUTE, 0);
        instance.set(Calendar.SECOND, 0);
        Date todayStart = instance.getTime();

        Map<String, List<JSONObject>> stationDataMap = productionService.getStationData(
                stationId, "", attrList, DateUtils.HOUR, todayStart, new Date(), getTenantId());

        for (Map.Entry<String, List<JSONObject>> entry : stationDataMap.entrySet()) {
            waterSupplyDetail.put(entry.getKey(), entry.getValue());
        }

        // 最后PH、浊度、余氯
        List<DeviceFullData> dataList = stationDataService.getStationDataDetail(stationId, "", true, getTenantId());
        BigDecimal currentPH = null;
        BigDecimal currentTurbidity = null;
        BigDecimal currentRemainder = null;
        if (dataList != null && dataList.size() > 0) {
            for (DeviceFullData deviceFullData : dataList) {
                if (deviceFullData.getProperty().equals(DataConstants.DeviceAttrType.PH.getValue())) {
                    currentPH = new BigDecimal(deviceFullData.getValue());
                }
                if (deviceFullData.getProperty().equals(DataConstants.DeviceAttrType.TURBIDITY.getValue())) {
                    currentTurbidity = new BigDecimal(deviceFullData.getValue());
                }
                if (deviceFullData.getProperty().equals(DataConstants.DeviceAttrType.REMAINDER.getValue())) {
                    currentRemainder = new BigDecimal(deviceFullData.getValue());
                }
            }
        }
        waterSupplyDetail.put("currentPH", currentPH);
        waterSupplyDetail.put("currentTurbidity", currentTurbidity);
        waterSupplyDetail.put("currentRemainder", currentRemainder);

        return IstarResponse.ok(waterSupplyDetail);
    }

    /**
     * 水质曲线
     */
    @GetMapping("getPointMonitor")
    public IstarResponse getPointMonitor(@RequestParam String stationIds, @RequestParam String attr, @RequestParam String stationType,
                                         @RequestParam String time, @RequestParam String queryType) {
        try {
            return IstarResponse.ok(productionService.getPointMonitor(
                    stationType, Arrays.stream(stationIds.split(",")).collect(Collectors.toList()), attr, queryType, time, getTenantId()));
        } catch (Exception e) {
            return IstarResponse.error(e.getMessage());
        }
    }

    /**
     * 查询单个水质监测站的供水信息
     * 包含：今日PH、今日温度
     */
    @GetMapping("getWaterSupplyDetail")
    public IstarResponse getWaterSupplyDetail(@RequestParam String stationId) throws ThingsboardException {
        StationEntity station = stationFeignClient.get(stationId);
        if (station == null) {
            return IstarResponse.error("[查询单个水源地供水信息异常] 要查询的水源地不存在, 水源地ID: " + stationId);
        }
        // 供水曲线数据
        JSONObject waterSupplyDetail = new JSONObject();

        // 今日ph、今日温度
        List<String> attrList = new ArrayList<>();
        attrList.add(DataConstants.DeviceAttrType.PH.getValue());
        attrList.add(DataConstants.DeviceAttrType.TEMPERATURE.getValue());
        // 今日时间
        Calendar instance = Calendar.getInstance();
        instance.set(Calendar.HOUR_OF_DAY, 0);
        instance.set(Calendar.MINUTE, 0);
        instance.set(Calendar.SECOND, 0);
        Date todayStart = instance.getTime();

        Map<String, List<JSONObject>> stationDataMap = productionService.getStationData(
                stationId, "", attrList, DateUtils.HOUR, todayStart, new Date(), getTenantId());

        for (Map.Entry<String, List<JSONObject>> entry : stationDataMap.entrySet()) {
            waterSupplyDetail.put(entry.getKey(), entry.getValue());
        }

        return IstarResponse.ok(waterSupplyDetail);
    }

    /**
     * 查询水源地监测数据项实时数据
     */
    @GetMapping("getInfoDetail")
    public IstarResponse getWaterSupplyInfoDetail(@RequestParam(required = false, defaultValue = "") String projectId) throws ThingsboardException {
        return IstarResponse.ok(stationDataService.getStationDataDetailListView(DataConstants.StationType.WATER_QUALITY_MONITORING.getValue(), true, projectId, getTenantId()));
    }

    /**
     * 水质报表
     */
    @GetMapping("getReport")
    public IstarResponse getReport(@RequestParam String stationId, @RequestParam String groupType, @RequestParam String stationType,
                                   @RequestParam String time, @RequestParam String queryType) {
        try {
            return IstarResponse.ok(stationDataService.getStationDataReport(stationId, groupType, stationType, time, queryType, getTenantId()));
        } catch (Exception e) {
            return IstarResponse.error("数据查询异常");
        }
    }

    /**
     * 水质报表
     */
    @GetMapping("getReport/export")
    public IstarResponse getReportExport(@RequestParam String stationId, @RequestParam String groupType, @RequestParam String stationType,
                                         @RequestParam String time, @RequestParam String queryType, HttpServletResponse response) {
        try {
            DynamicTableVO dynamicTableVO = stationDataService.getStationDataReport(stationId, groupType, stationType, time, queryType, getTenantId());
            List<DeviceDataTableInfoVO> tableInfo = dynamicTableVO.getTableInfo();
            List<JSONObject> data = dynamicTableVO.getTableDataList();

            // 数据列表
            Map headMap = new LinkedHashMap();
            for (DeviceDataTableInfoVO infoVO : tableInfo) {
                if (StringUtils.isNotBlank(infoVO.getUnit())) {
                    headMap.put(infoVO.getColumnValue(), infoVO.getColumnName() + "("+infoVO.getUnit()+")");
                } else {
                    headMap.put(infoVO.getColumnValue(), infoVO.getColumnName());
                }
            }

            // 水质报表
            String title = "水质报表";

            JSONArray dataList = JSONArray.parseArray(JSONObject.toJSONString(data));
            ExcelUtil.exportExcelX(title, headMap, dataList, "yyyy-MM-dd HH:mm:ss", 30, false, response);

            return IstarResponse.ok();
        } catch (Exception e) {
            return IstarResponse.error("暂无数据");
        }
    }

    /**
     * 水质报表-获取最大最小值报表
     */
    @GetMapping("getMaxAndMinReport")
    public IstarResponse getMaxAndMinReport(@RequestParam String stationId, @RequestParam String groupType, @RequestParam String stationType,
                                            @RequestParam String time, @RequestParam String queryType) {
        try {
            return IstarResponse.ok(stationDataService.getMaxAndMinReport(stationId, groupType, stationType, time, queryType, getTenantId()));
        } catch (ThingsboardException e) {
            return IstarResponse.error(e.getMessage());
        } catch (Exception e) {
            return IstarResponse.error("数据查询异常");
        }
    }


}
