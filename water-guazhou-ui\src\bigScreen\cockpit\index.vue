<template>
  <div class="holoIndex">
    <top-bar class="top-bar" :active="1" :active-menu="currentMenu" @change-view="handleViewChange"></top-bar>
    <div class="background-map">
      <map-container 
        :interactive="true" 
        :center="[95.782306, 40.520545]" 
        :zoom="13.5"
        :points="dmaPoints"
        :areas="dmaAreas"
        @point-click="handlePointClick"
        @area-click="handleAreaClick"
        ref="mapContainerRef"
      />
    </div>
    
    <!-- 底部DMA监测抽屉 -->
    <div class="dma-drawer" :class="{ 'drawer-open': isDrawerOpen }">
      <div class="drawer-handle" @click="toggleDrawer">
        <div class="handle-icon">
          <span class="handle-line"></span>
          <span class="handle-line"></span>
          <span class="handle-line"></span>
        </div>
        <span class="handle-text">{{ isDrawerOpen ? '收起监测面板' : '展开监测面板' }}</span>
      </div>
      
      <div class="drawer-content">
        <div class="panel-header">
          <h3>DMA漏损实时监测</h3>
          <div class="status-indicator">
            <span class="status-dot active"></span>
            <span>实时监测中</span>
          </div>
        </div>
        
        <div class="monitor-stats">
          <div class="stat-item">
            <div class="stat-number">{{ dmaStats.totalDMA }}</div>
            <div class="stat-label">监测分区</div>
          </div>
          <div class="stat-item">
            <div class="stat-number warning">{{ dmaStats.abnormalCount }}</div>
            <div class="stat-label">异常分区</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">{{ dmaStats.leakRate }}%</div>
            <div class="stat-label">漏损率</div>
          </div>
        </div>
        
        <div class="abnormal-list">
          <h4>异常监测点</h4>
          <div class="list-container">
            <div 
              v-for="point in abnormalPoints" 
              :key="point.id"
              class="abnormal-item"
              :class="{ active: selectedPoint && selectedPoint.id === point.id }"
              @click="selectPoint(point)"
            >
              <div class="point-info">
                <div class="point-name">{{ point.name }}</div>
                <div class="point-status" :class="point.status">{{ point.statusText }}</div>
              </div>
              <div class="point-metrics">
                <div class="metric">
                  <span class="label">流量:</span>
                  <span class="value" :class="point.flowStatus">{{ point.flow }}m³/h</span>
                </div>
                <div class="metric">
                  <span class="label">压力:</span>
                  <span class="value" :class="point.pressureStatus">{{ point.pressure }}MPa</span>
                </div>
              </div>
              
              <!-- 快速操作按钮 -->
              <div class="quick-actions">
                <button class="quick-btn assign" @click.stop="quickAssignWorkOrder(point)">
                  <span class="btn-icon">🚀</span>
                  <span class="btn-text">指派工单</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 异常点位弹窗 -->
    <div v-if="selectedPoint" class="point-detail-popup" :style="popupStyle">
      <div class="popup-header">
        <h4>{{ selectedPoint.name }}</h4>
        <button class="close-btn" @click="closePopup">×</button>
      </div>
      <div class="popup-content">
        <div class="detail-section">
          <h5>基本信息</h5>
          <div class="detail-grid">
            <div class="detail-item">
              <span class="label">监测点ID:</span>
              <span class="value">{{ selectedPoint.id }}</span>
            </div>
            <div class="detail-item">
              <span class="label">DMA分区:</span>
              <span class="value">{{ selectedPoint.dmaZone }}</span>
            </div>
            <div class="detail-item">
              <span class="label">监测时间:</span>
              <span class="value">{{ selectedPoint.time }}</span>
            </div>
          </div>
        </div>
        
        <div class="detail-section">
          <h5>监测数据</h5>
          <div class="metrics-grid">
            <div class="metric-card" :class="selectedPoint.flowStatus">
              <div class="metric-title">流量监测</div>
              <div class="metric-value">{{ selectedPoint.flow }} m³/h</div>
              <div class="metric-threshold">阈值: {{ selectedPoint.flowThreshold }} m³/h</div>
            </div>
            <div class="metric-card" :class="selectedPoint.pressureStatus">
              <div class="metric-title">压力监测</div>
              <div class="metric-value">{{ selectedPoint.pressure }} MPa</div>
              <div class="metric-threshold">阈值: {{ selectedPoint.pressureThreshold }} MPa</div>
            </div>
          </div>
        </div>
        
        <div class="detail-section">
          <h5>异常分析</h5>
          <div class="analysis-content">
            <div class="analysis-item">
              <span class="analysis-icon">⚠️</span>
              <span class="analysis-text">{{ selectedPoint.analysis }}</span>
            </div>
          </div>
        </div>
        
        <div class="detail-section">
          <h5>建议措施</h5>
          <div class="action-list">
            <div v-for="action in selectedPoint.actions" :key="action" class="action-item">
              <span class="action-icon">🔧</span>
              <span class="action-text">{{ action }}</span>
            </div>
          </div>
          
          <!-- 工单指派按钮 -->
          <div class="work-order-section">
            <div class="work-order-header">
              <span class="work-order-icon">📋</span>
              <span class="work-order-title">工单管理</span>
            </div>
            <div class="work-order-actions">
              <button class="assign-btn primary" @click="assignWorkOrder">
                <span class="btn-icon">🚀</span>
                <span class="btn-text">立即指派工单</span>
              </button>
              <button class="assign-btn secondary" @click="viewWorkOrders">
                <span class="btn-icon">📊</span>
                <span class="btn-text">查看相关工单</span>
              </button>
            </div>
            <div class="work-order-status" v-if="workOrderStatus">
              <span class="status-icon" :class="workOrderStatus.type">●</span>
              <span class="status-text">{{ workOrderStatus.message }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <transition name="fade" mode="out-in">
      <component class="changePanel" :is="viewModel"></component>
    </transition>

    <div class="left-shadow"></div>
    <div class="right-shadow"></div>
  </div>
</template>

<script>
import { defineComponent, watch, reactive, computed, onMounted, onUnmounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router';
import TopBar from "./components/common/TopBar.vue";
import SmartProduction from "./smartProduction/index.vue";
import SmartOperations from "./smartOperations/index.vue";
import MapContainer from "./components/MapContainer.vue";
import { PostWorkOrder, GetWorkOrderPage } from '@/api/workorder';

export default defineComponent({
  components: {
    TopBar, SmartProduction, SmartOperations, MapContainer
  },
  props: {
    view: {
      type: String,
      default: 'production'
    }
  },
  setup() {
    const route = useRoute();
    const router = useRouter();
    
    // DMA监测数据
    const dmaStats = reactive({
      totalDMA: 12,
      abnormalCount: 3,
      leakRate: 8.5
    });
    
    // 异常监测点数据
    const abnormalPoints = reactive([
      {
        id: 'DMA001',
        name: '东城区监测点A',
        dmaZone: 'DMA-01',
        status: 'warning',
        statusText: '流量异常',
        flow: 125.6,
        flowThreshold: 100,
        flowStatus: 'abnormal',
        pressure: 0.35,
        pressureThreshold: 0.4,
        pressureStatus: 'normal',
        time: '2024-01-15 14:30:25',
        analysis: '流量超出正常阈值25.6%，可能存在管道漏损或用户用水异常',
        actions: [
          '立即派遣巡检人员现场检查',
          '检查周边管道连接处是否有渗漏',
          '分析用户用水模式变化'
        ],
        position: { x: 200, y: 150 },
        longitude: 95.782306,
        latitude: 40.520545
      },
      {
        id: 'DMA002',
        name: '西城区监测点B',
        dmaZone: 'DMA-02',
        status: 'warning',
        statusText: '压力异常',
        flow: 85.2,
        flowThreshold: 90,
        flowStatus: 'normal',
        pressure: 0.25,
        pressureThreshold: 0.35,
        pressureStatus: 'abnormal',
        time: '2024-01-15 14:28:15',
        analysis: '压力低于正常阈值28.6%，可能存在管道破裂或阀门异常',
        actions: [
          '检查压力传感器是否正常工作',
          '排查管道是否有破裂点',
          '检查阀门开度是否异常'
        ],
        position: { x: 400, y: 200 },
        longitude: 95.777863,
        latitude: 40.518753
      },
      {
        id: 'DMA003',
        name: '南城区监测点C',
        dmaZone: 'DMA-03',
        status: 'danger',
        statusText: '双重异常',
        flow: 156.8,
        flowThreshold: 120,
        flowStatus: 'abnormal',
        pressure: 0.22,
        pressureThreshold: 0.35,
        pressureStatus: 'abnormal',
        time: '2024-01-15 14:25:45',
        analysis: '流量和压力同时异常，可能存在严重管道漏损或爆管',
        actions: [
          '立即启动应急响应预案',
          '派遣专业抢修队伍',
          '通知相关用户做好停水准备'
        ],
        position: { x: 300, y: 350 },
        longitude: 95.355895,
        latitude: 40.432522
      }
    ]);
    
    // DMA区域数据 - 用于绘制区域边界
    const dmaAreas = reactive([
      {
        id: 'DMA-01',
        name: '东城区DMA分区',
        coordinates: [
          [95.775, 40.518],
          [95.785, 40.518],
          [95.785, 40.523],
          [95.775, 40.523],
          [95.775, 40.518]
        ],
        status: 'warning',
        color: '#FFC61A'
      },
      {
        id: 'DMA-02',
        name: '西城区DMA分区',
        coordinates: [
          [95.775, 40.517],
          [95.780, 40.517],
          [95.780, 40.520],
          [95.775, 40.520],
          [95.775, 40.517]
        ],
        status: 'critical',
        color: '#FF5252'
      },
      {
        id: 'DMA-03',
        name: '南城区DMA分区',
        coordinates: [
          [95.353, 40.430],
          [95.358, 40.430],
          [95.358, 40.435],
          [95.353, 40.435],
          [95.353, 40.430]
        ],
        status: 'critical',
        color: '#FF5252'
      }
    ]);
    
    // 转换为antvL7地图点位数据格式
    const dmaPoints = computed(() => {
      const points = abnormalPoints.map(point => ({
        name: point.name,
        status: point.status,
        longitude: point.longitude,
        latitude: point.latitude,
        id: point.id,
        dmaZone: point.dmaZone,
        flow: point.flow,
        pressure: point.pressure,
        statusText: point.statusText,
        analysis: point.analysis,
        actions: point.actions,
        time: point.time
      }));
      console.log('dmaPoints计算属性结果:', points);
      return points;
    });
    
    const selectedPoint = ref(null);
    const popupPosition = reactive({ x: 0, y: 0 });
    const isDrawerOpen = ref(false);
    const mapContainerRef = ref(null);
    const workOrderStatus = ref(null);
    
    // 计算弹窗位置
    const popupStyle = computed(() => {
      if (!selectedPoint.value) return {};
      return {
        left: `${popupPosition.x}px`,
        top: `${popupPosition.y}px`
      };
    });
    
    // 处理地图点位点击事件
    const handlePointClick = (pointData) => {
      console.log('地图点位被点击:', pointData);
      const point = abnormalPoints.find(p => p.id === pointData.id);
      if (point) {
        selectPoint(point);
      }
    };

    // 处理地图区域点击事件
    const handleAreaClick = (areaData) => {
      console.log('地图区域被点击:', areaData);
      // 可以根据areaData.id来过滤或高亮异常点
      const areaPoints = abnormalPoints.filter(point => point.dmaZone === areaData.id);
      if (areaPoints.length > 0) {
        selectPoint(areaPoints[0]); // 选择第一个异常点作为弹窗内容
      }
    };
    
    // 选择监测点
    const selectPoint = (point) => {
      selectedPoint.value = point;
      popupPosition.x = point.position.x;
      popupPosition.y = point.position.y;
      // 清除之前的工单状态
      workOrderStatus.value = null;
    };
    
    // 关闭弹窗
    const closePopup = () => {
      selectedPoint.value = null;
      workOrderStatus.value = null;
    };
    
    // 立即指派工单
    const assignWorkOrder = async () => {
      if (!selectedPoint.value) return;
      
      try {
        // 显示指派中状态
        workOrderStatus.value = {
          type: 'warning',
          message: '正在指派工单...'
        };
        
        // 构建工单数据
        const workOrderData = {
          title: `${selectedPoint.value.name}异常处理工单`,
          description: selectedPoint.value.analysis,
          emergencyLevel: selectedPoint.value.status === 'danger' ? 'high' : 'medium',
          location: {
            name: selectedPoint.value.name,
            longitude: selectedPoint.value.longitude,
            latitude: selectedPoint.value.latitude,
            dmaZone: selectedPoint.value.dmaZone
          },
          metrics: {
            flow: selectedPoint.value.flow,
            pressure: selectedPoint.value.pressure,
            flowThreshold: selectedPoint.value.flowThreshold,
            pressureThreshold: selectedPoint.value.pressureThreshold
          },
          actions: selectedPoint.value.actions,
          status: 'pending',
          createdAt: new Date().toISOString(),
          type: 'dma_monitoring' // 工单类型：DMA监测异常
        };
        
        console.log('指派工单数据:', workOrderData);
        
        // 调用现有的工单API
        const response = await PostWorkOrder(workOrderData);
        
        console.log('工单创建响应:', response);
        
        // 显示成功状态
        workOrderStatus.value = {
          type: 'success',
          message: `工单已成功创建，工单号: ${response.data?.id || 'WO' + Date.now()}`
        };
        
        // 3秒后清除状态
        setTimeout(() => {
          workOrderStatus.value = null;
        }, 3000);
        
      } catch (error) {
        console.error('指派工单失败:', error);
        workOrderStatus.value = {
          type: 'danger',
          message: '工单指派失败，请重试'
        };
      }
    };
    
    // 查看相关工单
    const viewWorkOrders = async () => {
      if (!selectedPoint.value) return;
      
      try {
        // 调用现有的工单API查询相关工单
        const response = await GetWorkOrderPage({
          page: 1,
          size: 10,
          dmaZone: selectedPoint.value.dmaZone,
          status: 'pending,in_progress'
        });
        
        console.log('相关工单列表:', response);
        
        const workOrders = response.data?.list || [];
        
        if (workOrders.length > 0) {
          alert(`查看 ${selectedPoint.value.dmaZone} 相关工单\n共找到 ${workOrders.length} 个工单\n最新工单: ${workOrders[0].title}`);
        } else {
          alert(`暂无 ${selectedPoint.value.dmaZone} 相关工单`);
        }
        
      } catch (error) {
        console.error('查询工单失败:', error);
        alert('查询工单失败，请重试');
      }
    };

    // 快速指派工单 (新增)
    const quickAssignWorkOrder = (point) => {
      selectedPoint.value = point;
      popupPosition.x = point.position.x;
      popupPosition.y = point.position.y;
      assignWorkOrder(); // 调用原有的指派工单逻辑
    };
    
    // 切换抽屉
    const toggleDrawer = () => {
      isDrawerOpen.value = !isDrawerOpen.value;
    };
    
    // 测试标记点击
    const testMarker = () => {
      console.log('测试标记被点击');
      alert('地图标记系统正常工作！');
    };
    
    // 模拟实时数据更新
    let updateTimer = null;
    const updateData = () => {
      // 随机更新一些数据
      abnormalPoints.forEach(point => {
        if (Math.random() > 0.7) {
          point.flow += (Math.random() - 0.5) * 10;
          point.pressure += (Math.random() - 0.5) * 0.05;
          point.time = new Date().toLocaleString();
        }
      });
    };
    
    onMounted(() => {
      // 启动实时数据更新
      updateTimer = setInterval(updateData, 5000);
      
      // 调试信息
      console.log('地图标记数据:', abnormalPoints);
      console.log('地图标记数量:', abnormalPoints.length);
    });
    
    onUnmounted(() => {
      if (updateTimer) {
        clearInterval(updateTimer);
      }
    });
    
    return {
      dmaStats,
      abnormalPoints,
      dmaAreas,
      dmaPoints,
      selectedPoint,
      popupStyle,
      isDrawerOpen,
      mapContainerRef,
      selectPoint,
      closePopup,
      toggleDrawer,
      handlePointClick,
      handleAreaClick,
      assignWorkOrder,
      viewWorkOrders,
      workOrderStatus,
      quickAssignWorkOrder // 暴露给模板
    };
  },
  
  data() {
    return {
      viewModel: 'SmartProduction',
      currentMenu: 'production',
      viewMap: {
        'production': 'SmartProduction',
        'operations': 'SmartOperations',
        'quality': 'SmartProduction',
        'revenue': 'SmartOperations',
        'assets': 'SmartProduction',
        'emergency': 'SmartOperations'
      }
    }
  },

  created() {
    if (this.view) {
      console.log('从路径参数接收到view:', this.view);
      this.handleViewChange(this.view);
    }
    else if (this.$route && this.$route.query) {
      const viewParam = this.$route.query.view;
      if (viewParam && typeof viewParam === 'string') {
        console.log('从URL查询参数加载视图:', viewParam);
        this.handleViewChange(viewParam);
      }
    }
    else {
      console.log('使用默认视图: production');
      this.handleViewChange('production');
    }
  },

  mounted(){
    console.log('Cockpit页面已加载');
  },

  watch: {
    '$route.query.view': {
      handler(newView) {
        if (newView) {
          console.log('路由参数变化，切换视图:', newView);
          this.handleViewChange(newView);
        }
      },
      immediate: true
    }
  },

  methods: {
    handleViewChange(view) {
      this.currentMenu = view;
      if (this.viewMap[view]) {
        this.viewModel = this.viewMap[view];
      } else {
        console.log(`未实现的视图类型: ${view}`);
      }
    }
  }
})
</script>

<style lang="scss" scoped>
.holoIndex {
  position: relative;
  height: 100vh;
  width: 100%;
  overflow: hidden;
  
  .top-bar {
    position: relative;
    z-index: 20;
  }
  
  .background-map {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
  }
  
  // 底部DMA监测抽屉
  .dma-drawer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(5, 29, 46, 0.95);
    backdrop-filter: blur(15px);
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    z-index: 10;
    transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateY(100%);
    
    &.drawer-open {
      transform: translateY(0);
    }
    
    .drawer-handle {
      position: absolute;
      top: -40px;
      left: 50%;
      transform: translateX(-50%);
      background: rgba(5, 29, 46, 0.95);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-bottom: none;
      border-radius: 8px 8px 0 0;
      padding: 8px 20px;
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 8px;
      transition: all 0.3s ease;
      
      &:hover {
        background: rgba(5, 29, 46, 0.98);
        border-color: rgba(255, 255, 255, 0.3);
      }
      
      .handle-icon {
        display: flex;
        flex-direction: column;
        gap: 2px;
        
        .handle-line {
          width: 20px;
          height: 2px;
          background: #fff;
          border-radius: 1px;
          transition: all 0.3s ease;
        }
      }
      
      .handle-text {
        color: #fff;
        font-size: 12px;
        font-weight: 500;
      }
    }
    
    .drawer-content {
      padding: 20px;
      max-height: 400px;
      overflow-y: auto;
      
      .panel-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        
        h3 {
          color: #fff;
          margin: 0;
          font-size: 18px;
          font-weight: 600;
        }
        
        .status-indicator {
          display: flex;
          align-items: center;
          gap: 8px;
          color: #00ff88;
          font-size: 12px;
          
          .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #00ff88;
            animation: pulse 2s infinite;
            
            &.active {
              background: #00ff88;
            }
          }
        }
      }
      
      .monitor-stats {
        display: flex;
        justify-content: space-around;
        margin-bottom: 20px;
        
        .stat-item {
          text-align: center;
          
          .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #fff;
            margin-bottom: 4px;
            
            &.warning {
              color: #ffaa00;
            }
          }
          
          .stat-label {
            font-size: 12px;
            color: #ccc;
          }
        }
      }
      
      .abnormal-list {
        h4 {
          color: #fff;
          margin: 0 0 15px 0;
          font-size: 14px;
        }
        
        .list-container {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
          gap: 12px;
          max-height: 200px;
          overflow-y: auto;
          
          .abnormal-item {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 6px;
            padding: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
            
            &:hover {
              background: rgba(255, 255, 255, 0.1);
              border-left-color: #ffaa00;
            }
            
            &.active {
              background: rgba(255, 170, 0, 0.2);
              border-left-color: #ffaa00;
            }
            
            .point-info {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 8px;
              
              .point-name {
                color: #fff;
                font-weight: 500;
                font-size: 13px;
              }
              
              .point-status {
                padding: 2px 8px;
                border-radius: 12px;
                font-size: 11px;
                font-weight: 500;
                
                &.warning {
                  background: rgba(255, 170, 0, 0.2);
                  color: #ffaa00;
                }
                
                &.danger {
                  background: rgba(255, 0, 0, 0.2);
                  color: #ff4444;
                }
              }
            }
            
            .point-metrics {
              display: flex;
              gap: 15px;
              
              .metric {
                display: flex;
                align-items: center;
                gap: 4px;
                
                .label {
                  color: #ccc;
                  font-size: 11px;
                }
                
                .value {
                  color: #fff;
                  font-size: 11px;
                  font-weight: 500;
                  
                  &.abnormal {
                    color: #ff4444;
                  }
                  
                  &.normal {
                    color: #00ff88;
                  }
                }
              }
            }

            .quick-actions {
              margin-top: 10px;
              display: flex;
              gap: 8px;

              .quick-btn {
                flex: 1;
                padding: 6px 10px;
                border-radius: 6px;
                border: none;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 6px;
                font-size: 11px;
                font-weight: 500;
                transition: all 0.3s ease;

                &.assign {
                  background: #00ff88;
                  color: #051D2E;
                  border: 1px solid #00ff88;

                  &:hover {
                    background: #00cc66;
                    border-color: #00cc66;
                  }
                }

                .btn-icon {
                  font-size: 14px;
                }
              }
            }
          }
        }
      }
    }
  }
  
  // 弹窗样式
  .point-detail-popup {
    position: absolute;
    width: 420px;
    background: rgba(5, 29, 46, 0.95);
    border-radius: 8px;
    z-index: 15;
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    
    .popup-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 15px 20px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      
      h4 {
        color: #fff;
        margin: 0;
        font-size: 16px;
        font-weight: 600;
      }
      
      .close-btn {
        background: none;
        border: none;
        color: #ccc;
        font-size: 20px;
        cursor: pointer;
        padding: 0;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px;
        transition: all 0.3s ease;
        
        &:hover {
          background: rgba(255, 255, 255, 0.1);
          color: #fff;
        }
      }
    }
    
    .popup-content {
      padding: 20px;
      
      .detail-section {
        margin-bottom: 20px;
        
        h5 {
          color: #fff;
          margin: 0 0 12px 0;
          font-size: 14px;
          font-weight: 600;
        }
        
        .detail-grid {
          display: grid;
          gap: 8px;
          
          .detail-item {
            display: flex;
            justify-content: space-between;
            
            .label {
              color: #ccc;
              font-size: 12px;
            }
            
            .value {
              color: #fff;
              font-size: 12px;
              font-weight: 500;
            }
          }
        }
        
        .metrics-grid {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 12px;
          
          .metric-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 6px;
            padding: 12px;
            border-left: 3px solid transparent;
            
            &.abnormal {
              border-left-color: #ff4444;
              background: rgba(255, 68, 68, 0.1);
            }
            
            &.normal {
              border-left-color: #00ff88;
              background: rgba(0, 255, 136, 0.1);
            }
            
            .metric-title {
              color: #ccc;
              font-size: 11px;
              margin-bottom: 4px;
            }
            
            .metric-value {
              color: #fff;
              font-size: 16px;
              font-weight: bold;
              margin-bottom: 4px;
            }
            
            .metric-threshold {
              color: #999;
              font-size: 10px;
            }
          }
        }
        
        .analysis-content {
          .analysis-item {
            display: flex;
            align-items: flex-start;
            gap: 8px;
            background: rgba(255, 170, 0, 0.1);
            border-radius: 6px;
            padding: 12px;
            
            .analysis-icon {
              font-size: 16px;
            }
            
            .analysis-text {
              color: #fff;
              font-size: 12px;
              line-height: 1.4;
            }
          }
        }
        
        .action-list {
          .action-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
            
            &:last-child {
              border-bottom: none;
            }
            
            .action-icon {
              font-size: 14px;
            }
            
            .action-text {
              color: #fff;
              font-size: 12px;
            }
          }
        }

        .work-order-section {
          margin-top: 20px;
          padding-top: 15px;
          border-top: 1px solid rgba(255, 255, 255, 0.1);

          .work-order-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 10px;
            color: #fff;
            font-size: 13px;
            font-weight: 500;

            .work-order-icon {
              font-size: 16px;
            }
          }

          .work-order-actions {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;

            .assign-btn {
              flex: 1;
              padding: 8px 12px;
              border-radius: 6px;
              border: none;
              cursor: pointer;
              display: flex;
              align-items: center;
              justify-content: center;
              gap: 8px;
              font-size: 12px;
              font-weight: 500;
              transition: all 0.3s ease;

              &.primary {
                background: #00ff88;
                color: #051D2E;
                border: 1px solid #00ff88;

                &:hover {
                  background: #00cc66;
                  border-color: #00cc66;
                }
              }

              &.secondary {
                background: #ffaa00;
                color: #051D2E;
                border: 1px solid #ffaa00;

                &:hover {
                  background: #ff8800;
                  border-color: #ff8800;
                }
              }

              .btn-icon {
                font-size: 14px;
              }
            }
          }

          .work-order-status {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            border-radius: 6px;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            color: #fff;
            font-size: 12px;

            .status-icon {
              font-size: 14px;

              &.warning {
                color: #ffaa00;
              }
              
              &.success {
                color: #00ff88;
              }
              
              &.danger {
                color: #ff4444;
              }
            }
          }
        }
      }
    }
  }
  
  .changePanel {
    position: relative;
    width: 100%;
    height: calc(100vh - 96px);
    z-index: 4;
    pointer-events: none;
    
    .panel-vertical {
      pointer-events: auto;
    }
  }
  
  .left-shadow {
    position: absolute;
    top: 0;
    left: 0;
    width: 480px;
    height: 100%;
    background: -webkit-linear-gradient(right, transparent 0%, #051D2E 100%);
    pointer-events: none;
    z-index: 3;
  }

  .right-shadow {
    position: absolute;
    top: 0;
    right: 0;
    width: 480px;
    height: 100%;
    background: -webkit-linear-gradient(left, transparent 0%, #051D2E 100%);
    pointer-events: none;
    z-index: 3;
  }
}

// 动画效果
@keyframes pulse {
  0% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    opacity: 0.5;
    transform: translate(-50%, -50%) scale(1.2);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(1.4);
  }
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

// 滚动条样式
.dma-drawer .drawer-content::-webkit-scrollbar,
.abnormal-list .list-container::-webkit-scrollbar {
  width: 4px;
}

.dma-drawer .drawer-content::-webkit-scrollbar-track,
.abnormal-list .list-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
}

.dma-drawer .drawer-content::-webkit-scrollbar-thumb,
.abnormal-list .list-container::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
}

.dma-drawer .drawer-content::-webkit-scrollbar-thumb:hover,
.abnormal-list .list-container::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}
</style>
