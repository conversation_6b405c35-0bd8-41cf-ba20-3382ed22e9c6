<template>
  <div class="holoIndex">
    <top-bar class="top-bar" :active="1" :active-menu="currentMenu" @change-view="handleViewChange"></top-bar>
    <div class="background-map">
      <map-container :interactive="true" :center="[95.782306, 40.520545]" :zoom="13.5" />
    </div>
    <transition name="fade" mode="out-in">
      <component class="changePanel" :is="viewModel"></component>
    </transition>

    <div class="left-shadow"></div>
    <div class="right-shadow"></div>
  </div>
</template>

<script>
import { defineComponent, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router';
import TopBar from "./components/common/TopBar.vue";
import SmartProduction from "./smartProduction/index.vue";
import SmartOperations from "./smartOperations/index.vue";
import MapContainer from "./components/MapContainer.vue";

export default defineComponent({
  components: {
    TopBar, SmartProduction, SmartOperations, MapContainer
  },
  props: {
    view: {
      type: String,
      default: 'production'
    }
  },
  setup() {
    const route = useRoute();
    const router = useRouter();
    
    // 检查URL查询参数
    const viewParam = route.query.view;
    if (viewParam && typeof viewParam === 'string') {
      console.log('从URL参数加载视图:', viewParam);
      // 在setup中无法直接调用methods，需要在mounted或onMounted中处理
    }
    
    return { route, router };
  },
  data() {
    return {
      viewModel: 'SmartProduction',
      currentMenu: 'production', // 当前激活的菜单
      // 视图类型与组件名称的映射关系
      viewMap: {
        'production': 'SmartProduction', // 生产一张图
        'operations': 'SmartOperations', // 运维一张图
        'quality': 'SmartProduction',    // 水质一张图（暂用生产组件代替）
        'revenue': 'SmartOperations',    // 营收一张图（暂用运维组件代替）
        'assets': 'SmartProduction',     // 资产一张图（暂用生产组件代替）
        'emergency': 'SmartOperations'   // 应急一张图（暂用运维组件代替）
      }
    }
  },

  created() {
    // 检查props中的view参数（路径参数）
    if (this.view) {
      console.log('从路径参数接收到view:', this.view);
      this.handleViewChange(this.view);
    }
    // 检查URL查询参数，根据参数切换视图
    else if (this.$route && this.$route.query) {
      const viewParam = this.$route.query.view;
      if (viewParam && typeof viewParam === 'string') {
        console.log('从URL查询参数加载视图:', viewParam);
        this.handleViewChange(viewParam);
      }
    }
    // 默认使用production视图
    else {
      console.log('使用默认视图: production');
      this.handleViewChange('production');
    }
  },

  mounted(){
    // 初始化时设置默认激活的菜单
    console.log('Cockpit页面已加载');
  },

  // 监听路由变化
  watch: {
    '$route.query.view': {
      handler(newView) {
        if (newView) {
          console.log('路由参数变化，切换视图:', newView);
          this.handleViewChange(newView);
        }
      },
      immediate: true
    }
  },

  methods: {
    // 处理视图切换
    handleViewChange(view) {
      // 更新当前激活的菜单
      this.currentMenu = view;
      
      // 根据映射关系切换对应的组件
      if (this.viewMap[view]) {
        this.viewModel = this.viewMap[view];
      } else {
        console.log(`未实现的视图类型: ${view}`);
      }
    }
  }
})
</script>

<style lang="scss" scoped>
.holoIndex {
  position: relative;
  height: 100vh;
  width: 100%;
  overflow: hidden;
  
  .top-bar {
    position: relative;
    z-index: 20;
  }
  
  .background-map {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2; /* 调整z-index确保地图可以交互 */
  
  }
  
  .changePanel {
    position: relative;
    width: 100%;
    height: calc(100vh - 96px); /* 设置明确的高度，减去顶部栏的高度 */
    z-index: 4;
    pointer-events: none; /* 让面板元素默认不接收指针事件，以允许地图交互 */
    
    /* 让面板中的内容可以接收指针事件 */
    .panel-vertical {
      pointer-events: auto;
    }
  }
  
  .left-shadow {
    position: absolute;
    top: 0;
    left: 0;
    width: 480px;
    height: 100%;
    background: -webkit-linear-gradient(right, transparent 0%, #051D2E 100%);
    pointer-events: none;
    z-index: 3;
  }

  .right-shadow {
    position: absolute;
    top: 0;
    right: 0;
    width: 480px;
    height: 100%;
    background: -webkit-linear-gradient(left, transparent 0%, #051D2E 100%);
    pointer-events: none;
    z-index: 3;
  }
}

// 添加过渡效果
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
