package org.thingsboard.server.dao.sql.smartService.wechat;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.smartService.wechat.TokenInfo;
import org.thingsboard.server.dao.model.sql.smartService.wechat.WxAccountConfig;

import java.util.Date;

@Mapper
public interface WxAccountConfigMapper extends BaseMapper<WxAccountConfig> {
    WxAccountConfig findByTenantId(String tenantId);

    String getIdByTenantId(String tenantId);

    boolean refreshTokenInfo(@Param("accessToken") String accessToken, @Param("expireTime") Date expireTime, @Param("tenantId") String tenantId);

    TokenInfo getTokenInfo(String tenantId);
}
