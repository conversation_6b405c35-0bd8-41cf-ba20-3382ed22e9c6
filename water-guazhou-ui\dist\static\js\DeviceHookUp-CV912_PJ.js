import{d as x,c as D,r as g,s as u,a8 as h,b as n,S as b,o as I,g as T,n as U,p as k,q as f,i as m,aq as C,al as w,dO as M,bq as q,dG as j,C as G}from"./index-r0dFAfgr.js";import{_ as V}from"./Search-NSrhrIa_.js";import{_ as B}from"./index-BJ-QPYom.js";import{a as F,D as z}from"./index-0NlGN6gS.js";import{f as R}from"./DateFormatter-Bm9a68Ax.js";import{b as E,P as O,G as $,H as J,D as K}from"./hookupDevice-Bcbk7s68.js";const Q={class:"device-hookup"},W={class:"left"},X={class:"right overlay-y"},Y=x({__name:"DeviceHookUp",props:{tree:{},currentTreeNode:{}},setup(H){const _=D(),v=D(),y=H,l=g({data:y.tree,currentProject:y.currentTreeNode,treeNodeHandleClick:a=>{l.currentProject=a,d(),i()}}),L=g({filters:[{type:"input",label:"名称",field:"name"},{type:"select",label:"设备类型",field:"type",options:[{label:"流量计",value:"1"},{label:"压力计",value:"2"}]}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",svgIcon:u(w),click:()=>{d(),i()},loading:h(()=>r.loading)},{perm:!0,text:"添加",svgIcon:u(M),click:()=>A(),disabled:h(()=>r.loading)}]}],defaultParams:{type:"1"}}),r=g({indexVisible:!0,dataList:[],pagination:{refreshData:({page:a,size:e})=>{r.pagination.page=a,r.pagination.limit=e,d()}},columns:[{label:"设备",prop:"name"},{label:"类型",prop:"deviceTypeName"},{label:"创建时间",prop:"createTime",formatter:(a,e)=>R(e)}],handleSelectChange:a=>{r.selectList=a||[]}}),d=async()=>{var a;try{r.loading=!0;const o=(await E({page:r.pagination.page||1,size:r.pagination.limit||20,partitionId:l.currentProject.value,...((a=_.value)==null?void 0:a.queryParams)||{}})).data.data||{};r.dataList=o.data,r.pagination.total=o.total||0}catch{n.error("查询失败")}r.loading=!1},P=g({filters:[{type:"input",label:"名称",field:"name"}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",svgIcon:u(w),loading:h(()=>t.loading),click:()=>i()},{perm:!0,text:"移出",svgIcon:u(q),type:"danger",disabled:h(()=>t.loading),click:()=>N()}]}]}),t=g({indexVisible:!0,dataList:[],pagination:{refreshData:({page:a,size:e})=>{t.pagination.page=a,t.pagination.limit=e,i()}},columns:[{label:"名称",prop:"name"},{label:"类型",prop:"type",formatter:(a,e)=>F[e]||e},{label:"水流方向",prop:"direction",tag:!0,tagColor:(a,e)=>e==="1"||e==="3"?"#318DFF":"#f56c6c",formatter:(a,e)=>z[e]||"- -"}],handleSelectChange:a=>{t.selectList=a||[]},operations:[{perm:!0,text:"变更方向",svgIcon:u(j),click:a=>S(a.id)}]}),S=a=>{b("确定变更方向？","提示信息").then(async()=>{try{const e=await O(a);e.data.code===200?(n.success("操作成功"),i()):n.error(e.data.message)}catch{n.error("操作失败")}})},i=async()=>{var a,e,o,s;if(!l.currentProject){t.dataList=[],t.pagination.total=0;return}try{t.loading=!0;const p=(await $({page:t.pagination.page||1,size:t.pagination.limit||20,partitionId:(a=l.currentProject)==null?void 0:a.id,...((e=v.value)==null?void 0:e.queryParams)||{},type:(s=(o=_.value)==null?void 0:o.queryParams)==null?void 0:s.type})).data.data||{};t.dataList=p.data,t.pagination.total=p.total||0}catch{n.error("查询失败")}t.loading=!1},A=async()=>{const a=r.selectList||[];if(!a.length){n.warning("请选择要添加的设备");return}try{r.loading=!0,t.loading=!0;const e=await J(a.map(o=>{var s,c,p;return{partitionId:(s=l.currentProject)==null?void 0:s.id,deviceId:o.id,type:(p=(c=_.value)==null?void 0:c.queryParams)==null?void 0:p.type}}));e.data.code===200?(d(),i()):n.error(e.data.message)}catch{n.error("添加失败")}r.loading=!1,t.loading=!1},N=async()=>{const a=t.selectList||[];if(!a.length){n.warning("请选择要移出的设备");return}b("确定移出？","提示信息").then(async()=>{try{r.loading=!0,t.loading=!0;const e=await K(a.map(o=>o.id));e.data.code===200?(d(),i()):n.error(e.data.message)}catch{n.error("移出失败")}r.loading=!1,t.loading=!1}).catch(()=>{})};return I(()=>{d(),i()}),(a,e)=>{const o=B,s=V,c=C;return T(),U("div",Q,[k("div",W,[f(o,{"tree-data":m(l)},null,8,["tree-data"])]),k("div",X,[f(s,{ref_key:"refSearch_All",ref:_,config:m(L)},null,8,["config"]),f(c,{class:"right-table",config:m(r)},null,8,["config"]),f(s,{ref_key:"refSearch_New",ref:v,config:m(P)},null,8,["config"]),f(c,{class:"right-table",config:m(t)},null,8,["config"])])])}}}),ne=G(Y,[["__scopeId","data-v-6b96cc26"]]);export{ne as default};
