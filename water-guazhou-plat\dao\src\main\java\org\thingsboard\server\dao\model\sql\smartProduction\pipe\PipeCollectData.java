package org.thingsboard.server.dao.model.sql.smartProduction.pipe;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 管网采集
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2023-12-07
 */

@Data
@TableName("tb_pipe_collect_data")
public class PipeCollectData {

    private String id;

    private String sid;

    private String type;

    private String start_sid;

    private String end_sid;

    private Double start_depth;

    private Double end_depth;

    private String material;

    private String laneway;

    private String address;

    private String burytype;

    private Integer diameter;

    private String subtype;

    private Double pipelength;

    private Double depth;

    private Double x;

    private Double y;

    private Double z;

    private Double end_x;

    private Double end_y;

    private Double end_z;

    private String name;

    private String mainId;

    private String layerId;

    private Date createTime;

    private String tenantId;

    private String creator;

    private String isStorage;

    @TableField(exist = false)
    private String creatorName;


}
