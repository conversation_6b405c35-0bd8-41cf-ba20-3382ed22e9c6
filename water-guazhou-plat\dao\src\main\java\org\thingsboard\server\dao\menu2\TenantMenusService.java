package org.thingsboard.server.dao.menu2;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.codehaus.jackson.map.ObjectMapper;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.id.UserId;
import org.thingsboard.server.common.data.menu.Menu;
import org.thingsboard.server.common.data.menu.MenuMeta;
import org.thingsboard.server.common.data.menu.MenuPoolVO;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.model.sql.TenantMenus;
import org.thingsboard.server.dao.util.mapping.JacksonUtil;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public interface TenantMenusService {
    org.thingsboard.server.dao.model.sql.TenantMenus saveMenu(Menu menu, String parentId, TenantId tenantId);

    /**
     * menu转换为menupool
     *
     * @param menu
     * @return
     */
    default TenantMenus MenuToTenantMenus(Menu menu) {
        org.thingsboard.server.dao.model.sql.TenantMenus tenantMenus = new org.thingsboard.server.dao.model.sql.TenantMenus();
        // 设置扩展信息
        Map<String, Object> map = new HashMap<>();
        map.put("path", menu.getPath());
        map.put("name", menu.getName());
        map.put("component", menu.getComponent());
        map.put("hidden", menu.getMeta().getHidden());
        tenantMenus.setAdditionalInfo(JacksonUtil.toString(map));
        // 设置基本信息
        if (menu.getId() != null) {
            tenantMenus.setId(menu.getId());
        }
        if (menu.getParentId() != null && menu.getParentId().trim().length() > 0) {
            tenantMenus.setId(menu.getParentId());
        } else {
            tenantMenus.setParentId(ModelConstants.MENU_POOL_ROOT_STR);
        }
        tenantMenus.setOrderNum(menu.getOrderNum());
        tenantMenus.setTenantId(menu.getTenantId());
        tenantMenus.setUrl(menu.getUrl());
        try {
            String icon = menu.getMeta().getIcon();
            tenantMenus.setIcon(icon);
            String title = menu.getMeta().getTitle();
            tenantMenus.setName(title);
        } catch (Exception ignored) {
        }

        return tenantMenus;
    }

    default List<Menu> tenantMenusListToMenuList(List<TenantMenus> tenantMenusList) {
        List<Menu> list = new ArrayList<>();
        for (TenantMenus tenantMenus : tenantMenusList) {
            try {
                list.add(tenantMenusToMenu(tenantMenus));
            } catch (IOException ignored) {
            }
        }

        return list;
    }

    default Menu tenantMenusToMenu(TenantMenus tenantMenus) throws IOException {
        Menu menu = new Menu();
        menu.setId(tenantMenus.getId());
        menu.setParentId(tenantMenus.getParentId());
        menu.setMeta(buildMeta(tenantMenus));
        menu.setOrderNum(tenantMenus.getOrderNum());
        String additionalInfo = tenantMenus.getAdditionalInfo();
        Map map = new ObjectMapper().readValue(additionalInfo, Map.class);
        menu.setPath((String) map.get("path"));
        menu.setComponent((String) map.get("component"));
        menu.setName((String) map.get("name"));
        menu.getMeta().setHidden((Boolean) map.get("hidden"));
        menu.setUrl(tenantMenus.getUrl());

        return menu;
    }

    default MenuMeta buildMeta(TenantMenus tenantMenus){
        MenuMeta meta = new MenuMeta();
        String icon = "form";
        if (StringUtils.isNotBlank(tenantMenus.getIcon())) {
            icon = tenantMenus.getIcon();
        }
        meta.setIcon(icon);
        meta.setTitle(tenantMenus.getName());
        meta.setRoles(new String[]{"TENANT_ADMIN","TENANT_SYS","CUSTOMER_USER"});

        return meta;
    }

    List<MenuPoolVO> getSelectableTree(TenantId tenantId);

    JSONObject remove(String id, TenantId tenantId);

    List<Menu> findByTenantApplication(UserId id, String tenantApplicationId, TenantId tenantId);

    List<Menu> findCustomerMenuByTenantApplication(UserId userId, String tenantApplicationId, TenantId tenantId);

    Menu findById(String id) throws IOException;

    List<TenantMenus> findTree(TenantId tenantId);

    void importMenu(List<TenantMenus> menuList, TenantId tenantId);

    List<TenantMenus> findByTenantId(TenantId id);

    void menuPoolToTenantMenus(TenantId tenantId);

    List<TenantMenus> findByParentId(String parentId, TenantId tenantId);

    List<MenuPoolVO> findTreeByTenantApplication(UserId id, String tenantApplicationId, TenantId tenantId);
}
