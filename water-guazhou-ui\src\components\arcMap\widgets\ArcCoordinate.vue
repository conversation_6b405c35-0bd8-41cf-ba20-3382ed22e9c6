<template>
  <div class="wrapper"></div>
</template>
<script lang="ts" setup>
import CoordinateConversion from '@arcgis/core/widgets/CoordinateConversion';
const props = defineProps<{
  position?: string | __esri.UIAddPosition;
}>();
const view: __esri.MapView | undefined = inject('view');
const ccWidget = new CoordinateConversion({
  view
});
view?.ui.add(ccWidget, props.position || 'bottom-left');
onBeforeUnmount(() => {
  ccWidget?.destroy();
  view?.ui.remove(ccWidget);
});
</script>
<style lang="scss" scoped></style>
