// 智慧生产=水厂管理-出厂指标 api
import request from '@/plugins/axios'

// 出水流量
export function getWaterSupplyFlowReport(params?: {
    stationId?: string
    queryType?: string
    time?: string
    compareType?: string
}) {
  return request({
    url: '/istar/api/production/waterPlant/getWaterSupplyFlowReport',
    method: 'get',
    params
  })
}

// 导出出水流量
export function exportWaterSupplyFlowReport(params?: {
  stationId?: string
  queryType?: string
  time?: string
  compareType?: string
}) {
  return request({
    url: '/istar/api/production/waterPlant/getWaterSupplyFlowReport/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 出水压力
export function getWaterSupplyPressureReport(params?: {
    stationId?: string
    attributeId?: string
    queryType?: string
    time?: string
    compareType?: string
}) {
  return request({
    url: '/istar/api/production/waterPlant/getWaterSupplyPressureReport',
    method: 'get',
    params
  })
}

// 导出出水压力
export function exportWaterSupplyPressureReport(params?: {
  stationId?: string
  queryType?: string
  time?: string
  compareType?: string
}) {
  return request({
    url: '/istar/api/production/waterPlant/getWaterSupplyPressureReport/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 出水水质
export function getWaterSupplyQualityReport(params?: {
    stationId?: string
    attrType?: string
    queryType?: string
    time?: string
    compareType?: string
}) {
  return request({
    url: '/istar/api/production/waterPlant/getWaterSupplyQualityReport',
    method: 'get',
    params
  })
}

// 导出出水水质
export function exportWaterSupplyQualityReport(params?: {
  stationId?: string
  attrType?: string
  queryType?: string
  time?: string
  compareType?: string
}) {
  return request({
    url: '/istar/api/production/waterPlant/getWaterSupplyQualityReport/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
