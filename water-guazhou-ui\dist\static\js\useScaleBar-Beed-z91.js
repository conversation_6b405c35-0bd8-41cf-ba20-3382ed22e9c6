import{A as m,m as a}from"./AnimatedLinesLayer-B2VbV4jv.js";import{Q as u}from"./index-r0dFAfgr.js";const l=()=>{let t;const e=(o,s)=>{var r;return t=new m({view:o}),t&&((r=o.ui)==null||r.add(t,s||"bottom-left")),t},n=()=>{t==null||t.destroy()};return u(()=>{n()}),{init:e}},y=()=>{let t;const e=(o,s)=>{var r;return t=new a({view:o,unit:"metric",style:"ruler"}),(r=o.ui)==null||r.add(t,s||"bottom-left"),{scaleBar:t}},n=()=>{t==null||t.destroy()};return u(()=>{n()}),{init:e}};export{l as a,y as u};
