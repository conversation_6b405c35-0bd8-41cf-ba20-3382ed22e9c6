package org.thingsboard.server.dao.model.DTO;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2023-01-31
 */
@Data
public class CustExpandingDTO {

    private String orgId;

    private String orgName;

    private String meterBookCode;

    private String meterBookName;

    private String custCode;

    private String custName;

    private String address;

    private String population;

    private String waterCategoryName;

    private String industryCategoryName;

    private String userTypeName;

    private String contractNumber;

    private String steelSealNumber;

    private String meterCode;

    private String brandName;

    private String typeName;

    private Integer readBit;

    private BigDecimal currentRead;

    private String caliberName;

    private Integer meterCopyOrderNumber;

    private String priceCode;

    private String priceName;

    private BigDecimal price;

    private String bankName;

    private String bankNumber;

    private String additionalPriceName;

    private String phone;

    private String idNumber;

    private String liquidatedDamagesName;

    private String isLadder;

    private String remoteMeterAddress;

    private String installAddress;

    private BigDecimal balance;

    private BigDecimal frozenBalance;

    private String installPositionName;

    private String meterWell;

    private String acceptanceLeadSealNumber;

    private String installLeadSealNumber;

    private String measureLeadSealNumber;

    private String tenantId;
}
