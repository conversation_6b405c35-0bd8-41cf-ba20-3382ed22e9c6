package org.thingsboard.server.dao.sql.deviceType;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.thingsboard.server.dao.model.sql.deviceManage.DeviceType;
import org.thingsboard.server.dao.util.imodel.query.device.DeviceTypePageRequest;

import java.util.Collections;
import java.util.List;
import java.util.Map;

@Mapper
public interface DeviceTypeMapper extends BaseMapper<DeviceType> {
    IPage<DeviceType> findAllConditional(DeviceTypePageRequest req);

    List<DeviceType> findRoots(String tenantId);

    List<DeviceType> findChildren(String parentId);

    boolean update(DeviceType deviceType);

    @Select("select level from m_device_type where id = #{id}")
    Integer getDepth(String id);

    boolean canBeDelete(@Param("id") String id, @Param("tenantId") String tenantId);

    @Select("select serial_id from m_device_type where id = #{id}")
    String getSerialId(String id);

    @Select("select count(1) > 0 from m_device_type where serial_id = #{serialId} and tenant_id = #{tenantId}")
    boolean existsBySerialId(@Param("serialId") String serialId, @Param("tenantId") String tenantId);

    @Select("select parent_id from m_device_type where id = #{id}")
    String getParentId(String id);

    String getNameById(String id);

    @SuppressWarnings("unused")
    default String getTreePath(String id) {
        return (String) getTreePathMap().getOrDefault(id, Collections.emptyMap()).get("path");
    }

    @MapKey("id")
    Map<String, Map<String, Object>> getTreePathMap();

    String getMainTypeName(@Param("serialId") String serialId, @Param("tenantId") String tenantId);

    boolean isSerialIdExists(@Param("serialId") String serialId, @Param("id") String id, @Param("tenantId") String tenantId);

    int getDepthBySerialId(@Param("serialId") String serialId, @Param("tenantId") String tenantId);

    String countDeviceByType(String tenantId);

}
