<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.purchase.DevicePurchaseItemMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->id,
                           main_id,
                           serial_id,
                           inquiry_end_time,
                           num,
                           device_purchase_get_intention_price(id) as intentionPrice,
                           inquired,
                           tenant_id<!--@sql from device_purchase_item -->
    </sql>
    <sql id="Named_Base_Column_List">
        <!--@sql select -->item.id,
                           item.main_id,
                           item.serial_id,
                           item.inquiry_end_time,
                           item.num,
                           device_purchase_get_intention_price(item.id) as intentionPrice,
                           item.inquired,
                           item.tenant_id<!--@sql from device_purchase_item item -->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.purchase.DevicePurchaseItem">
        <result column="id" property="id"/>
        <result column="main_id" property="mainId"/>
        <result column="serial_id" property="serialId"/>
        <result column="inquiry_end_time" property="inquiryEndTime"/>
        <result column="intentionPrice" property="price"/>
        <result column="num" property="num"/>
        <result column="inquired" property="inquired"/>
        <result column="tenant_id" property="tenantId"/>
        <association property="deviceInfoResponse"
                     javaType="org.thingsboard.server.dao.model.sql.store.DeviceInfoResponse"
                     column="{serialId=serial_id,tenantId=tenant_id}"
                     select="org.thingsboard.server.dao.sql.deviceType.DeviceMapper.getInfoBySerialId"/>
    </resultMap>
    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Named_Base_Column_List"/>
        from device_purchase_item item,
             device_purchase main ,
             m_device device
        <where>
            item.main_id = main.id
            and device.serial_id = item.serial_id AND device.tenant_id = main.tenant_id
            <if test="mainId != null and mainId != ''">
                and main_id = #{mainId}
            </if>
            <if test="serialId != null and serialId != ''">
                and serial_id = #{serialId}
            </if>
            <if test="deviceName != null and deviceName != ''">
                <!--@formatter:off-->
                and device.name LIKE '%' || #{deviceName} || '%'
                <!--@formatter:on-->
            </if>
            <if test="deviceModel != null and deviceModel != ''">
                and device_is_model_like(serial_id, #{deviceModel})
            </if>
            <if test="purchaseCode != null and purchaseCode != ''">
                and device_purchase_is_purchase_code_like(main_id, #{purchaseCode})
            </if>
            <if test="purchaseTitle != null and purchaseTitle != ''">
                and device_purchase_is_purchase_title_like(main_id, #{purchaseTitle})
            </if>
            <if test="(userId == null or userId == '') and userDepartmentId != null and userDepartmentId != ''">
                and device_purchase_is_user_at_department(main_id, #{userDepartmentId})
            </if>
            <if test="userId != null and userId != ''">
                and device_purchase_is_user(main_id, #{userId})
            </if>
            <if test="fromTime != null">
                and inquiry_end_time >= #{fromTime}
            </if>
            <if test="toTime != null">
                and inquiry_end_time &lt;= #{toTime}
            </if>
            and item.tenant_id = #{tenantId}
        </where>
        order by main.create_time desc, main.id, item.inquiry_end_time desc
    </select>

    <update id="update">
        update device_purchase_item
        <set>
            <if test="serialId != null">
                serial_id = #{serialId},
            </if>
            <if test="inquiryEndTime != null">
                inquiry_end_time = #{inquiryEndTime},
            </if>
            <if test="num != null">
                num = #{num},
            </if>
        </set>
        where id = #{id}
    </update>

    <insert id="saveAll">
        INSERT INTO device_purchase_item(id,
                                         main_id,
                                         serial_id,
                                         inquiry_end_time,
                                         num,
                                         inquired,
                                         tenant_id)VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (#{element.id},
             #{element.mainId},
             #{element.serialId},
             #{element.inquiryEndTime},
             #{element.num},
             #{element.inquired},
             #{element.tenantId})
        </foreach>
        on conflict(id) do update set serial_id        = excluded.serial_id,
                                      inquiry_end_time = excluded.inquiry_end_time,
                                      num              = excluded.num,
                                      inquired         = excluded.inquired
    </insert>

    <select id="getNameByItemId" resultType="java.lang.String">
        select purchase.title
        from device_purchase_item item,
             device_purchase purchase
        where item.main_id = purchase.id
          and item.id = #{id}
    </select>

    <delete id="deleteByMainIdOnIdNotIn">
        delete
        from device_purchase_item
        where main_id = #{id}
        <if test="idList != null and idList.size() != 0">
            and id not in
            <foreach item="item" index="index" collection="idList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </delete>

    <update id="submitInquiry">
        update device_purchase_item
        set inquired = true
        where id = #{id}
    </update>

    <select id="getStatus" resultType="int">
        select device_purchase_item_status(#{id});
    </select>
</mapper>