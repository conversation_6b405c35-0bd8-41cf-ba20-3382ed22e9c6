package org.thingsboard.server.dao.sql.assay;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.assay.AssayReportItem;

import java.util.List;

@Mapper
public interface AssayReportItemMapper extends BaseMapper<AssayReportItem> {
    void batchInsert(List<AssayReportItem> entityList);

    List<AssayReportItem> getList(@Param("reportId") String reportId);
}
