<template>
  <el-form
    ref="eForm"
    :rules="rulesE"
    :model="enterprise"
    label-width="100px"
    class="enterprise-form"
  >
    <!-- <el-form-item label="企业名称" prop="name">
        <el-input v-model="enterprise.name" placeholder="请输入企业名称" style='width:400px'></el-input>
      </el-form-item> -->
    <el-form-item
      label="LOGO"
      prop="logoUrl"
    >
      <el-upload
        class="avatar-uploader"
        :action="actionUrl"
        :show-file-list="false"
        :before-upload="beforeAvatarUpload"
        :on-success="handleAvatarSuccess"
        :headers="headers"
      >
        <!-- 判断是否是 SVG 图片 -->
        <div
          v-if="enterprise.logoUrl && isSvgUrl(enterprise.logoUrl) && svgContent"
          class="avatar-wrapper svg-container"
        >
          <!-- 使用动态组件显示 SVG 内容 -->
          <component :is="svgComponent" />
        </div>
        <!-- 非 SVG 或者 SVG 内容未加载完成时使用普通 img 标签 -->
        <div
          v-else-if="enterprise.logoUrl"
          class="avatar-wrapper"
        >
          <img
            :src="enterprise.logoUrl"
            class="avatar"
            alt="logo"
            :style="{ objectFit: 'contain' }"
          />
        </div>
        <div
          v-else
          class="avatar"
        >
          <Icon :icon="'ep:plus'"></Icon>
        </div>
      </el-upload>
    </el-form-item>

    <el-form-item
      label="首屏页面"
      prop="firstScreen"
    >
      <el-tree-select
        v-model="enterprise.firstScreen"
        :data="state.menuSourse"
        :default-expand-all="true"
        :filterable="true"
        :check-strictly="true"
        :clearable="true"
        :props="{
          label: 'label',
          children: 'children',
          disabled: 'disabled',
          isLeaf: 'isLeaf',
          class: 'class',
          value: 'value'
        }"
        :filter-node-method="filterNodeMethod"
      />
    </el-form-item>
    <el-form-item
      v-if="false"
      label="APP上传"
      prop="app"
    >
      <el-upload
        v-model="appList"
        :action="appUploadUrl"
        :before-upload="beforeAppUpload"
        :on-success="handleAppUploadSuccess"
        :headers="headers"
      >
        <el-button
          v-loading="appUploading"
          type="primary"
        >
          选择上传文件
        </el-button>
        <template #tip>
          <div class="el-upload__tip">
            点击按钮选择要上传的安装包文件
          </div>
        </template>
      </el-upload>
    </el-form-item>
    <el-form-item label="">
      <el-button
        type="primary"
        @click="saveEnterprise"
      >
        保存
      </el-button>
    </el-form-item>
  </el-form>
</template>
<script lang="ts" setup>
import { Icon } from '@iconify/vue'
import { GetMenuSourceAll } from '@/api/menu/source'
import { getTenantInfo, saveTenant } from '@/api/tenant'
import { useAppStore, useUserStore } from '@/store'
import { formatTree } from '@/utils/GlobalHelper'
import { SLMessage } from '@/utils/Message'
import { ref, computed, h } from 'vue'

const state = reactive<{
  menuSourse: NormalOption[]
}>({
  menuSourse: []
})

const filterNodeMethod = (value, data) => data.label?.includes(value)

// 企业log
const headers = ref({
  'X-Authorization': 'Bearer ' + useUserStore().token
})
const actionUrl = ref<string>(useAppStore().actionUrl + '/file/api/upload/image')
const appList = ref<{ url: string; name: string }[]>([])
const appUploadUrl = ref<string>(
  window.SITE_CONFIG.imgUrl + 'api/fileRegistry/upload'
)
const validateText = (rule, value, callback) => {
  if (value.trim() !== '') {
    callback()
  } else {
    callback(new Error('请输入有效字符 空格无效'))
  }
}
const rulesE = ref({
  name: [
    { required: true, message: '请输入企业名称', trigger: 'blur' },
    { validator: validateText, trigger: 'blur' }
  ],
  imageUrl: [{ required: true, message: '请上传企业logo', trigger: 'blur' }]
})
const enterprise = ref<{
  name: string
  logoUrl: string
  app: string
  firstScreen: string
}>({
  name: '',
  logoUrl: '',
  app: '',
  firstScreen: ''
})

const beforeAvatarUpload = (file: any) => {
  let isImg = true
  // 将图片大小限制从 1MB 增加到 5MB
  const isLt5M = file.size / 1024 / 1024 < 5

  // 添加 SVG 格式的支持
  if (
    file.type === 'image/jpeg'
    || file.type === 'image/png'
    || file.type === 'image/bmp'
    || file.type === 'image/x-bmp'
    || file.type === 'image/svg+xml' // 添加 SVG 格式
  ) {
    isImg = true
  } else {
    isImg = false
    // 更新错误提示消息，包含 SVG 格式
    SLMessage.error('上传logo图片只能是 JPG,JPEG,PNG,BMP,SVG 格式!')
  }
  if (!isLt5M) {
    // 更新错误提示消息，将大小限制从 1MB 改为 5MB
    SLMessage.error('上传logo图片大小不能超过 5MB!')
  }
  return isImg && isLt5M
}
// 判断 URL 是否是 SVG 文件
const isSvgUrl = (url: string): boolean => {
  if (!url) return false
  return url.toLowerCase().endsWith('.svg')
}

// SVG 相关变量和函数
// 存储 SVG 内容
const svgContent = ref('')

// 动态创建 SVG 组件
const svgComponent = computed(() => {
  if (!svgContent.value) return null

  // 使用 h 函数创建组件
  return h('div', {
    innerHTML: svgContent.value,
    class: 'svg-content'
  })
})

// 加载 SVG 内容
const loadSvgContent = async (url: string) => {
  try {
    const response = await fetch(url)
    if (response.ok) {
      const text = await response.text()
      // 检查是否是有效的 SVG
      if (text.includes('<svg')) {
        svgContent.value = text
        return true
      }
    }
    return false
  } catch (error) {
    console.error('Failed to load SVG:', error)
    return false
  }
}

const handleAvatarSuccess = async (res: any, file: any) => {
  // 判断是否是 SVG 文件
  const isSvg = file.type === 'image/svg+xml' || (typeof res === 'string' && res.toLowerCase().endsWith('.svg'))

  // 设置 URL
  enterprise.value.logoUrl = res

  // 如果是 SVG 文件，加载 SVG 内容
  if (isSvg) {
    // 尝试加载 SVG 内容
    await loadSvgContent(res)
    SLMessage.success('SVG 图片已上传 请保存操作')
  } else {
    // 非 SVG 文件
    SLMessage.success('图片已上传 请保存操作')
  }

  console.log(res, file, '图片上传')
}

const beforeAppUpload = () => {
  return true
}
const appUploading = ref<boolean>(false)
const handleAppUploadSuccess = async (res: any, files: any) => {
  appUploading.value = true
  try {
    console.log(res, files)
    const data = res.data || {}
    appList.value = [{ name: data.fileName, url: data.fileAddress }]
    enterprise.value.app = JSON.stringify(appList.value)
    // const res = await uploadSaticFiles(file.raw)
    // enterprise.value.app = res.data.fileAddress
    // appList.value = [{ name: 'app下载地址', url: res.data.fileAddress }]
  } catch (error) {
    SLMessage.warning('上传失败')
  }
  appUploading.value = false
}

const eForm = ref()
const tenantInfo = ref<Record<string, any>>({})
// 企业配置
const saveEnterprise = () => {
  eForm.value.validate(valid => {
    if (valid) {
      tenantInfo.value.additionalInfo = enterprise.value
      saveTenant(tenantInfo.value)
        .then(async res => {
          SLMessage.success('修改成功')
          useUserStore().InitTenantInfo()
          useAppStore().ToggleLogo(res.data?.additionalInfo?.logoUrl)
        })
        .catch(err => console.log(err))
    } else {
      SLMessage.warning('请按提示输入')
      return false
    }
  })
}
onMounted(async () => {
  try {
    const res = await getTenantInfo(useUserStore().user?.tenantId?.id || '')
    tenantInfo.value = res.data || {}
    const additionalInfo = res.data?.additionalInfo
    const ainfo = typeof additionalInfo === 'string'
      ? JSON.parse(additionalInfo)
      : additionalInfo || {}
    const app = ainfo.app
    enterprise.value = {
      ...ainfo
    }
    appList.value = app ? JSON.parse(app) : []

    // 检查 logoUrl 是否是 SVG 文件
    if (enterprise.value.logoUrl && isSvgUrl(enterprise.value.logoUrl)) {
      // 尝试加载 SVG 内容
      await loadSvgContent(enterprise.value.logoUrl)
    }
  } catch (error) {
    console.error('Failed to load tenant info:', error)
  }
  GetMenuSourceAll().then(res => {
    state.menuSourse = formatTree(res.data, {
      id: 'id',
      label: 'name',
      value: 'url',
      children: 'children'
    }) || []
  })
})
</script>
<style lang="scss" scoped>
.avatar-wrapper {
  width: 280px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  cursor: pointer;
  border: 1px solid #eee;
}

.avatar {
  width: 280px;
  height: 50px;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

/* SVG 相关样式 */
.svg-container {
  cursor: pointer;
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;

  :deep(svg) {
    height: 100%;
    width: 100%;
  }
}

.svg-content {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 上传组件样式调整 */
:deep(.el-upload) {
  display: block;
  width: 100%;
  cursor: pointer;
}

:deep(.avatar-uploader .el-upload) {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

:deep(.avatar-uploader .el-upload:hover) {
  border-color: #409EFF;
}
</style>
