import{d as he,j as _e,a6 as ye,bF as f,r as _,c as y,am as ee,f1 as ke,a8 as te,s as E,bB as R,D as ae,o as xe,ay as Ne,g as v,n as k,p as u,q as n,av as Ie,i as O,F as m,h as le,aB as Te,aJ as Se,f2 as oe,an as I,cs as se,c5 as we,aq as Ce,cE as Le,cU as De,dB as Ae,dC as Ye,dF as Oe,dA as Me,al as ne,aj as re,C as Ge}from"./index-r0dFAfgr.js";import{_ as Fe}from"./CardSearch-CB_HNR-Q.js";import{_ as Ee}from"./index-B69llYYW.js";import{_ as Re}from"./index-C9hz-UZb.js";/* empty css                         */import{_ as je}from"./Search-NSrhrIa_.js";import{l as ie}from"./echart-D5stWtDc.js";import{u as Ve}from"./useStation-DJgnSZIA.js";import{s as Be,g as Pe,d as ze}from"./zhandian-YaGuQZe6.js";import{s as He,a as Je}from"./headwaterMonitoring-BgK7jThW.js";import{g as Ue}from"./URLHelper-B9aplt5w.js";import"./useAmap-D6DJ1T90.js";import"./index-BI1vGJja.js";const We=[{label:"0时",value:0},{label:"1时",value:1},{label:"2时",value:2},{label:"3时",value:3},{label:"4时",value:4},{label:"5时",value:5},{label:"6时",value:6},{label:"7时",value:7},{label:"8时",value:8},{label:"9时",value:9},{label:"10时",value:10},{label:"11时",value:11},{label:"12时",value:12},{label:"13时",value:13},{label:"14时",value:14},{label:"15时",value:15},{label:"16时",value:16},{label:"17时",value:17},{label:"18时",value:18},{label:"19时",value:19},{label:"20时",value:20},{label:"21时",value:21},{label:"22时",value:22},{label:"23时",value:23}],Qe={class:"view"},$e={key:0,class:"content"},qe={class:"top"},Ke={style:{width:"100%",height:"100%"},class:"card-table"},Xe={style:{display:"flex","justify-content":"space-between",height:"85%"}},Ze={class:"card-table"},et={class:"bottom"},tt={style:{width:"32%",overflow:"hidden"}},at={class:"image-slot"},lt={key:1,class:"bottom-1"},ot={class:"no-pictures"},st={style:{width:"32%",overflow:"hidden"}},nt={style:{width:"32%",height:"90%"}},rt={key:1,class:"content1"},it={key:0},ct={key:2,class:"content1"},ut={key:3,class:"content"},dt=he({__name:"stationDetailMonitoring",props:{stationId:{},monitor:{}},setup(ce){const S=_e(),{getStationAttrGroups:ue}=Ve(),z=ye(),x=ce,M=f().date(),a=_({activeName:"status",chartOption:null,chartOption1:null,searchActiveName:"echarts",groupTab:"",currentGroupTabs:[],dataList:[],stationInfo:null,imgs:[],stationInfoColumns:[]}),j=y(),H=y(),J=y(),U=y(),w=y(),V=y(),B=y(),C=y(),W=y();let L=_([]),c=_([]);ee(()=>a.activeName,()=>{a.activeName==="echarts"&&G()}),ee(()=>[x.stationId,x.monitor,a.searchActiveName],async(e,t)=>{var l,o,s,d;if(e[0]&&(t[0]!==e[0]||((l=t[1])==null?void 0:l.title)!==((o=e[1])==null?void 0:o.title))){const r=await Be(x.stationId);a.stationInfo=r.data;const p=await ke((s=r.data)==null?void 0:s.projectId);a.stationInfo.projectName=(d=p==null?void 0:p.data)==null?void 0:d.name,a.imgs=a.stationInfo.imgs?a.stationInfo.imgs.split(","):[],console.log(" state.imgs",a.imgs),ge(),setTimeout(async()=>{de(a.stationInfo)},1e3)}t[2]!==e[2]&&a.searchActiveName==="echarts"&&await G()});const de=async e=>{var l,o;const t=(l=e.location)==null?void 0:l.split(",");(t==null?void 0:t.length)===2&&((o=j.value)==null||o.setMarker(t,{icon:Ue("泵站.png")},()=>{Q(e)})),Q(e)},Q=async e=>{var o,s;const t=[{label:"名称",value:e.name},{label:"类型",value:e.type},{label:"所属项目",value:e.projectName},{label:"地址",value:e.address},{label:"经纬度",value:e.location},{label:"备注",value:e.remark}],l=(o=e.location)==null?void 0:o.split(",");(l==null?void 0:l.length)===2&&((s=j.value)==null||s.setListInfoWindow({point:l,values:t,title:e.name}))},N=_({loading:!0,currentRow:[],currentRowKey:"property",highlightCurrentRow:!0,dataList:[],columns:[{prop:"propertyName",label:"检测项名称"},{prop:"value",label:"检测项数据"},{prop:"collectionTime",label:"采集时间",formatter:e=>e.collectionTime>0?f(e.collectionTime).format("YYYY-MM-DD HH:mm:ss"):"-"}],operations:[],pagination:{hide:!0},handleRowClick:e=>{N.currentRow=e,G()}}),b=_({loading:!0,dataList:[],indexVisible:!0,columns:[{prop:"alarmJsonName",label:"报警描述",formatter:e=>e.type==="offline"?e.deviceName+"_离线":e.deviceName+"_"+e.alarmJsonName},{prop:"createdTime",label:"报警时间",formatter:(e,t)=>t?f(t).format("YYYY-MM-DD HH:mm:ss"):""}],operations:[],pagination:{page:1,limit:20,total:0,layout:"total, prev, pager, next,  jumper",handleSize:e=>{b.pagination.limit=e},refreshData:({page:e,size:t})=>{b.pagination.page=e,b.pagination.limit=t,b.dataList=L.slice((e-1)*t,e*t)}}}),h=_({loading:!0,dataList:[],indexVisible:!0,columns:[],operations:[],pagination:{page:1,limit:20,total:0,layout:"total, prev, pager, next, jumper",handleSize:e=>{h.pagination.limit=e},refreshData:({page:e,size:t})=>{var l;h.pagination.page=e,h.pagination.limit=t,h.dataList=(l=c==null?void 0:c.tableDataList)==null?void 0:l.slice((e-1)*t,e*t)}}}),D=_({defaultParams:{date:[f().date(M-2).format("YYYY-MM-DD"),f().date(M).format("YYYY-MM-DD")],filterStart:[0,23],group:"",attributeId:""},filters:[{type:"daterange",label:"选中日期",field:"date",clearable:!1},{label:"时间",type:"range",rangeType:"select",field:"filterStart",options:JSON.parse(JSON.stringify(We)),startPlaceHolder:"0时",endPlaceHolder:"23时",startOptionDisabled:(e,t)=>t&&Number(t)<e.value,endOptionDisabled:(e,t)=>t&&e.value<=Number(t)},{label:"监测组",labelWidth:60,field:"group",type:"select",clearable:!1,options:[],onChange:e=>$(e)},{label:"曲线类型",labelWidth:70,field:"attributeId",type:"select",clearable:!1,options:[],hidden:te(()=>a.searchActiveName==="list")}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",click:()=>{h.pagination.page=1,q()},svgIcon:E(ne)},{perm:!0,type:"warning",text:"导出",svgIcon:E(re),hide:()=>a.searchActiveName!=="list",click:()=>{var e;(e=H.value)==null||e.exportTable()}}]}]}),me=_({defaultParams:{date:[f().date(M-2).format("YYYY-MM-DD"),f().date(M).format("YYYY-MM-DD")]},filters:[{type:"daterange",label:"选择时间",field:"date",clearable:!1},{type:"btn-group",btns:[{perm:!0,text:"查询",click:()=>P(),svgIcon:E(ne)},{perm:!0,type:"warning",text:"导出",svgIcon:E(re),click:()=>{var e;(e=U.value)==null||e.exportTable()}}]}]}),pe=_({type:"tabs",tabType:"simple",width:"100%",tabs:[{label:"当前状态",value:"status"},{label:"数据查询",value:"search"},{label:"报警信息",value:"alarm"}],handleTabClick:e=>{console.log(e.props.name),a.activeName=e.props.name,a.activeName==="search"?(console.log(a.currentGroupTabs),R(()=>{var l,o,s;const t=(l=D.filters)==null?void 0:l.find(d=>d.field==="group");t.options=a.currentGroupTabs,D.defaultParams={...(o=C.value)==null?void 0:o.queryParams,group:a.currentGroupTabs[0].value},(s=C.value)==null||s.resetForm(),$(a.currentGroupTabs[0].value)})):a.activeName==="alarm"&&R(()=>{P("range")})}}),$=e=>{var o,s,d;const t=a.currentGroupTabs.find(r=>r.value===e),l=(o=D.filters)==null?void 0:o.find(r=>(r==null?void 0:r.field)==="attributeId");l.options=t.children.map(r=>({label:r.name,value:r.id,data:ae(r.deviceId)+"."+r.attr,unit:r.unit?"("+r.unit+")":""})),D.defaultParams={...(s=C.value)==null?void 0:s.queryParams,attributeId:t.children[0].id},(d=C.value)==null||d.resetForm(),console.log(e),q()},q=async()=>{var i,A,g,F;const e=((i=C.value)==null?void 0:i.queryParams)||{},[t,l]=e.date||[],[o,s]=e.filterStart||[],d={filterStart:o||0,filterEnd:s||23,queryType:"10m",stationId:x.stationId,group:e==null?void 0:e.group,start:t?f(t).startOf("day").valueOf():"",end:l?f(l).endOf("day").valueOf():""};c=(A=(await He(d)).data)==null?void 0:A.data;const p=c==null?void 0:c.tableInfo.map(T=>({prop:T.columnValue,label:T.columnName,unit:T.unit?"("+T.unit+")":""}));console.log(p),h.columns=p,h.dataList=(g=c==null?void 0:c.tableDataList)==null?void 0:g.slice(0*20,20),h.pagination.total=(F=c==null?void 0:c.tableDataList)==null?void 0:F.length,h.loading=!1,fe(e==null?void 0:e.attributeId)},fe=e=>{var d,r,p;const t=ie(),o=(r=((d=D.filters)==null?void 0:d.find(i=>i.field==="attributeId")).options)==null?void 0:r.find(i=>i.value===e);t.yAxis[0].name=o.label+(o.unit?o.unit:""),t.xAxis.data=c==null?void 0:c.tableDataList.map(i=>i.ts),console.log(e+"."+o.data,c==null?void 0:c.tableDataList);const s={name:o.label,smooth:!0,data:c==null?void 0:c.tableDataList.map(i=>i[o.data]),type:"line",markPoint:{data:[{type:"max",name:"最大值",label:{fontSize:12,color:S.isDark?"#ffffff":"#000000"}},{type:"min",name:"最小值",label:{color:S.isDark?"#ffffff":"#000000"}}]}};(p=B.value)==null||p.clear(),t.series=[s],R(()=>{w.value&&z.listenTo(w.value,()=>{var i;a.chartOption1=t,(i=B.value)==null||i.resize()})})},K=_({filters:[{type:"radio-button",label:"",field:"groupTab",options:te(()=>a.currentGroupTabs),onChange:e=>{e&&X(e)}}]}),ge=()=>{a.activeName="status",console.log("refreshData"),ve()},ve=async()=>{var t,l;const e=await ue(x.stationId);a.currentGroupTabs=e,K.defaultParams={groupTab:(t=e[0])==null?void 0:t.id},(l=W.value)==null||l.resetForm(),await X(x.monitor.name),await P()},P=async e=>{var o;b.loading=!0;let t=f().startOf("month").valueOf(),l=f().endOf("month").valueOf();if(e==="range"){const s=(o=J.value)==null?void 0:o.queryParams;t=f(s==null?void 0:s.date[0]).startOf("day").valueOf(),l=f(s==null?void 0:s.date[1]).endOf("day").valueOf()}Pe(x.stationId,t,l).then(s=>{console.log("res",s),L=s.data,b.dataList=L==null?void 0:L.slice(0,20),b.pagination.total=L.length,b.loading=!1})},X=async e=>{N.loading=!0;const t=await ze(x.stationId,e);N.dataList=t.data;const l=t==null?void 0:t.data[0];N.currentRow=l,G(),console.log(N.currentRow),N.loading=!1},G=async()=>{var r,p;const e=N.currentRow,l=(r=(await Je({deviceId:ae(e.deviceId),attr:e.property})).data)==null?void 0:r.data,o=ie(),s=[{name:"前天",key:"beforeYesterdayDataList"},{name:"昨天",key:"yesterdayDataList"},{name:"今天",key:"todayDataList"}];o.xAxis.data=l.todayDataList.map(i=>i.ts),o.yAxis[0].name=e.propertyName.concat(e.unit?"("+e.unit+")":"");const d=s.map(i=>{const A=l[i.key].map(g=>g.value);return{name:i.name,smooth:!0,data:A,type:"line",markPoint:{data:[{type:"max",name:"最大值",label:{fontSize:12,color:S.isDark?"#ffffff":"#000000"}},{type:"min",name:"最小值",label:{color:S.isDark?"#ffffff":"#000000"}}]},markLine:{data:[{type:"average",name:"平均值"}]}}});o.series=d,(p=V.value)==null||p.clear(),await R(()=>{w.value&&z.listenTo(w.value,()=>{var i;a.chartOption=o,(i=V.value)==null||i.resize()})})};return xe(async()=>{console.log("onMounted")}),(e,t)=>{const l=we,o=je,s=Ce,d=Ne("VChart"),r=Le,p=De,i=Ae,A=Ye,g=Re,F=Ee,T=Fe,Z=Oe,be=Me;return v(),k("div",null,[u("div",{style:Ie({background:O(S).isDark?"#222536":"#FFFFFF"})},[n(l,{modelValue:a.activeName,"onUpdate:modelValue":t[0]||(t[0]=Y=>a.activeName=Y),config:pe},null,8,["modelValue","config"])],4),u("div",Qe,[a.activeName==="status"?(v(),k("div",$e,[u("div",qe,[u("div",Ke,[n(o,{ref_key:"refGroup",ref:W,config:K},null,8,["config"]),u("div",Xe,[u("div",Ze,[n(s,{config:N,class:"left-table"},null,8,["config"])]),u("div",{ref_key:"echartsDiv",ref:w,class:"chart-box"},[n(d,{ref_key:"refChart",ref:V,theme:O(S).isDark?"dark":"light",option:a.chartOption},null,8,["theme","option"])],512)])])]),u("div",et,[u("div",tt,[t[3]||(t[3]=u("div",{class:"title"},"现场实景",-1)),n(g,{class:"chart-box",overlay:""},{default:m(()=>[a.imgs.length>0?(v(),le(A,{key:0,trigger:"click",height:"40vh"},{default:m(()=>[n(i,null,{default:m(()=>[(v(!0),k(Te,null,Se(a.imgs,Y=>(v(),le(p,{key:Y,src:Y,style:{height:"100%",width:"100%"}},{error:m(()=>[u("div",at,[n(r,null,{default:m(()=>[n(O(oe))]),_:1})])]),_:2},1032,["src"]))),128))]),_:1})]),_:1})):I("",!0),a.imgs.length===0?(v(),k("div",lt,[u("div",ot,[u("div",null,[n(r,{size:"110px",color:"#E4E7F1"},{default:m(()=>[n(O(oe))]),_:1})]),t[2]||(t[2]=u("div",{style:{width:"70%",margin:"20px auto"}},[u("span",{style:{color:"#54728f"}},"请前往"),u("span",{style:{color:"#54728f"}},"“数据平台”>“档案基础数据”>“现场实品图”"),u("span",{style:{color:"#54728f"}},"界面上传实景图")],-1))])])):I("",!0)]),_:1})]),u("div",st,[t[4]||(t[4]=u("div",{class:"title"},"水源信息",-1)),n(g,{class:"chart-box",title:"",overlay:""},{default:m(()=>[n(F,{ref_key:"refAmap",ref:j,"hide-input":!0,"init-center-mark":!1},null,512)]),_:1})]),u("div",nt,[t[5]||(t[5]=u("div",{class:"title"},"报警信息",-1)),n(g,{class:"chart-box",title:"",overlay:""},{default:m(()=>[n(s,{config:b},null,8,["config"])]),_:1})])])])):I("",!0),a.activeName==="search"?(v(),k("div",rt,[n(g,{title:" ",overlay:""},{title:m(()=>[n(T,{ref_key:"cardSearch",ref:C,style:{"margin-top":"5px"},config:D},null,8,["config"])]),default:m(()=>[n(g,{title:" ",class:"card-table"},{right:m(()=>[n(be,{modelValue:a.searchActiveName,"onUpdate:modelValue":t[1]||(t[1]=Y=>a.searchActiveName=Y)},{default:m(()=>[n(Z,{label:"echarts"},{default:m(()=>[n(O(se),{style:{"margin-right":"1px","font-size":"16px"},icon:"clarity:line-chart-line"})]),_:1}),n(Z,{label:"list"},{default:m(()=>[n(O(se),{style:{"margin-right":"1px","font-size":"16px"},icon:"material-symbols:table"})]),_:1})]),_:1},8,["modelValue"])]),default:m(()=>[a.searchActiveName==="list"?(v(),k("div",it,[n(s,{ref_key:"refTable",ref:H,config:h,class:"chart-box"},null,8,["config"])])):I("",!0),a.searchActiveName==="echarts"?(v(),k("div",{key:1,ref_key:"echartsDiv",ref:w,class:"chart-box"},[n(d,{ref_key:"refChart1",ref:B,option:a.chartOption1},null,8,["option"])],512)):I("",!0)]),_:1})]),_:1})])):I("",!0),a.activeName==="alarm"?(v(),k("div",ct,[n(g,{class:"search-card",title:" ",overlay:""},{title:m(()=>[n(T,{ref_key:"alarmCardSearch",ref:J,config:me},null,8,["config"])]),default:m(()=>[n(s,{ref_key:"refAlarmTable",ref:U,config:b,class:"chart-box"},null,8,["config"])]),_:1})])):I("",!0),a.activeName==="control"?(v(),k("div",ut)):I("",!0)])])}}}),Tt=Ge(dt,[["__scopeId","data-v-2a7e7499"]]);export{Tt as default};
