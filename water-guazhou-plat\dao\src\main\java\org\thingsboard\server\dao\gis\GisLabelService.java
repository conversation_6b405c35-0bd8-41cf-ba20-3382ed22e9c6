package org.thingsboard.server.dao.gis;

import org.thingsboard.server.common.data.User;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.request.GisLabelListRequest;
import org.thingsboard.server.dao.model.sql.gis.GisLabel;

import java.util.List;

public interface GisLabelService {
    PageData<GisLabel> findList(GisLabelListRequest request, TenantId tenantId);

    void save(GisLabel entity, User currentUser);

    void remove(List<String> ids);
}
