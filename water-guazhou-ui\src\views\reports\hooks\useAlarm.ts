import { ref } from 'vue'
import {
  GetAlarmOverview,
  GetAlarmPriority,
  GetAlarmRank,
  GetAlarmRankV2,
  GetAlarmTrend
} from '@/api/reports/alarm'
import {
  IAlarmOverview,
  IAlarmPriority,
  IAlarmRank,
  IAlarmTrend,
  QueryListParam
} from '@/common/types/reports'

const useAlarm = () => {
  const AlarmRank = ref<IAlarmRank[]>([])
  const getAlarmRank = async (params: QueryListParam) => {
    const res = await GetAlarmRankV2(params)
    AlarmRank.value = res.data
  }
  const AlarmTrend = ref<IAlarmTrend[]>([])
  const getAlarmTrend = async (params: QueryListParam) => {
    const res = await GetAlarmTrend(params)
    AlarmTrend.value = res.data
  }
  const AlarmOverview = ref<IAlarmOverview>({
    alarmTotal: 0,
    alarmDeviceTotal: 0,
    unconfirmTotal: 0,
    unremoveTotal: 0
  })
  const getAlarmOverview = async (params: QueryListParam) => {
    const res = await GetAlarmOverview(params)
    AlarmOverview.value = res.data
  }
  const AlarmPriority = ref<IAlarmPriority>({
    提示: 0,
    次要: 0,
    重要: 0,
    紧急: 0
  })
  const getAlarmPriority = async (params: QueryListParam) => {
    const res = await GetAlarmPriority(params)
    AlarmPriority.value = res.data
  }

  return {
    AlarmRank,
    getAlarmRank,
    AlarmTrend,
    getAlarmTrend,
    AlarmOverview,
    getAlarmOverview,
    AlarmPriority,
    getAlarmPriority
  }
}
export default useAlarm
