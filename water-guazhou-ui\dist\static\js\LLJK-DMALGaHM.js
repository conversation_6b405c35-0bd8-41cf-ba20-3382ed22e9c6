import"./index-0NlGN6gS.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import{d as x,c as y,r as I,o as _,ay as w,g as A,n as b,q as T,i as C,l as h,C as z}from"./index-r0dFAfgr.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./project-DUuzYgGl.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import{u as D}from"./usePartition-DkcY9fQ2.js";import{g as L}from"./statistics-CeyexT_5.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./LayerHelper-Cn-iiqxI.js";import"./pipe-nogVzCHG.js";import"./dma-SMxrzG7b.js";import"./hookupDevice-Bcbk7s68.js";const S={class:"chart"},O=x({__name:"LLJK",setup(k){const e=y(),f=(t=[],i,m,l=2)=>({tooltip:{trigger:"item",formatter:o=>""+o.name+": "+Number(o.value).toFixed(l)+" "+i},legend:{type:"scroll",icon:"circle",orient:"vertical",right:40,top:"center",align:"left",itemGap:10,itemWidth:10,itemHeight:10,symbolKeepAspect:!0,textStyle:{color:"#fff",rich:{name:{align:"left",width:120,fontSize:12,color:"#fff"},value:{align:"left",width:80,fontSize:12,color:"#00ff00"}}},data:t.map(o=>o.name),formatter(o){const a=(t==null?void 0:t.length)??0;for(let r=0;r<a;r++)if(o===t[r].name)return"{name| "+o+"}{value| "+t[r].value+"}"}},series:[{type:"pie",radius:["35%","50%"],center:["30%","50%"],data:t,hoverAnimation:!0,label:{show:!1,position:"center"},emphasis:{label:{show:!0,fontSize:"30",fontWeight:"bold",formatter:["{a|{d}%}","{b|{b}}"].join(`
`),rich:{a:{color:"#0AE79A",fontSize:20,lineHeight:30,verticalAlign:"bottom"},b:{color:"#fff",fontSize:"40%",lineHeight:24}}}}}]}),p=I({ring:f(void 0,"m³/h"),mTime:-1}),v=async()=>{var o,a,r;await u.getDeviceTree({type:"flow"});const t=u.DeviceTree.value,i=[];for(let n=0;n<t.length;n++){const d=t[n].children;for(let c=0;c<d.length;c++)d[c].label==="区域供水"&&i.push(...d[c].children||[])}const m=await L({page:1,size:20,deviceIdList:i==null?void 0:i.map(n=>n.data.deviceId),type:"time",interval:"1h",start:h().startOf("D").valueOf(),end:h().endOf("D").add(1,"d").valueOf(),queryType:"flow"}),l=h().get("h");console.log(l);const s=((r=(a=(o=m.data)==null?void 0:o.data)==null?void 0:a[l])==null?void 0:r.data)||i.map(n=>({name:n.label,value:0}));p.ring=f(s,"m³/h")},u=D(),g=()=>{var m,l,s,o;let t=-1;const i=p.ring.series[0].data.length;p.mTime=setInterval(()=>{e.value&&e.value.dispatchAction({type:"downplay",seriesIndex:0,dataIndex:t}),t=(t+1)%i,e.value&&e.value.dispatchAction({type:"highlight",seriesIndex:0,dataIndex:t}),t>i&&(t=0)},3e3),(l=(m=e.value)==null?void 0:m.chart)==null||l.on("mouseover",a=>{var r,n;clearInterval(p.mTime),(r=e.value)==null||r.dispatchAction({type:"downplay",seriesIndex:0}),(n=e.value)==null||n.dispatchAction({type:"highlight",seriesIndex:0,dataIndex:a.dataIndex})}),(o=(s=e.value)==null?void 0:s.chart)==null||o.on("mouseout",a=>{var r;clearInterval(p.mTime),(r=e.value)==null||r.dispatchAction({type:"downplay",seriesIndex:0,dataIndex:a.dataIndex}),p.mTime=setInterval(()=>{e.value&&(e.value.dispatchAction({type:"downplay",seriesIndex:0,dataIndex:t%i}),t++,e.value&&e.value.dispatchAction({type:"highlight",seriesIndex:0,dataIndex:t%i}))},3e3)})};return _(()=>{v(),g()}),(t,i)=>{const m=w("VChart");return A(),b("div",S,[T(m,{ref_key:"refChart",ref:e,option:C(p).ring},null,8,["option"])])}}}),Xt=z(O,[["__scopeId","data-v-f16fbd78"]]);export{Xt as default};
