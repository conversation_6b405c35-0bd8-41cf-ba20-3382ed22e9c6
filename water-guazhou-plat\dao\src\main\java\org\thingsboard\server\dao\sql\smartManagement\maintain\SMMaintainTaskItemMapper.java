package org.thingsboard.server.dao.sql.smartManagement.maintain;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.thingsboard.server.dao.model.sql.smartManagement.maintaince.SMMaintainTaskItem;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.maintain.SMMaintainTaskItemPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.maintain.SMMaintainTaskItemReportRequest;

import java.util.List;

@Mapper
public interface SMMaintainTaskItemMapper extends BaseMapper<SMMaintainTaskItem> {
    IPage<SMMaintainTaskItem> findByPage(SMMaintainTaskItemPageRequest request);

    boolean update(SMMaintainTaskItem entity);

    int saveAll(List<SMMaintainTaskItem> list);

    boolean report(SMMaintainTaskItemReportRequest req);

    int removeAllByTaskId(String id);
}
