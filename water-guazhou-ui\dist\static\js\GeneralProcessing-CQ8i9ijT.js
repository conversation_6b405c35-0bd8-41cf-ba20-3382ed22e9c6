import{x as d,y as h}from"./index-r0dFAfgr.js";const w=(s,b,t,m,u)=>new Promise(e=>{m||(m={submitting:!0}),m.submitting=!0,s.id?t(s).then(a=>{m.submitting=!1,a.code===200||a.data.code==200?(d.success("编辑成功"),e(a)):d.warning(a.data.msg)}).catch(a=>{m.submitting=!1,d.warning(a)}):b(s).then(a=>{m.submitting=!1,a.code===200||a.data.code==200?(d.success("新增成功"),e(a)):d.warning(a.data.msg)}).catch(a=>{m.submitting=!1,d.warning(a)})}),x=(s,b,t,m)=>new Promise((u,e)=>{t&&(t.submitting=!0),b(s).then(a=>{t&&(t.submitting=!1),a.data.code===200?(d.success(m||"操作成功"),u(a)):a.data.status===200?(d.success(m||"操作成功"),u(a)):(d.error(a.data.msg),e(a))}).catch(a=>{d.warning(a),t&&(t.submitting=!1),e(a)})}),P=(s,b,t,m)=>new Promise((u,e)=>{h.confirm((t||"确认删除该记录")+",是否继续?","删除提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{b(s).then(a=>{a.code===200||a.data.code===200?(d.success("删除成功"),u(a)):a.data.status===200?(d.success("删除成功"),u(a)):(d.error(a.data.msg),e(a))}).catch(a=>{d.warning(a),e(a)})}).catch(a=>{})}),T=(s,b,t)=>new Promise((m,u)=>{t!=null&&t.table&&(t.table.loading=!0),b(s).then(e=>{if(e.status===200||e.data.code===200||e.code===200){t!=null&&t.msg&&d.success(t.msg);const a=l(e.data);t!=null&&t.table&&!(t!=null&&t.n_assignment)&&(t.table.dataList=(a==null?void 0:a.data)||[]),t!=null&&t.table&&!(t!=null&&t.n_assignment)&&(t.table.pagination.total=(a==null?void 0:a.total)||0),t!=null&&t.table&&(t.table.loading=!1),m(a)}else d.warning(e.data.msg),t!=null&&t.table&&!(t!=null&&t.n_assignment)&&(t.table.dataList=[]),t!=null&&t.table&&(t.table.loading=!1),u(e)}).catch(e=>{t!=null&&t.table&&(t.table.loading=!1),d.warning(e),u(e),console.log(e)})}),l=(s,b)=>s!=null&&s.total?s:s!=null&&s.data?l(s==null?void 0:s.data):{data:s!=null&&s.length?s:[],total:s!=null&&s.length?s==null?void 0:s.length:0};export{x as C,w as G,P as U,T as a};
