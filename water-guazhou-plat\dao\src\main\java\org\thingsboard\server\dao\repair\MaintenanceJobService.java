package org.thingsboard.server.dao.repair;

import org.thingsboard.server.common.data.User;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.MaintenanceJobCEntity;
import org.thingsboard.server.dao.model.sql.MaintenanceJobEntity;
import org.thingsboard.server.dao.model.sql.MaintenanceJobTriggerEntity;

import java.util.List;

public interface MaintenanceJobService {
    MaintenanceJobEntity detail(String id, TenantId tenantId);

    PageData<MaintenanceJobEntity> findList(int page, int size, String name, String deviceId, User currentUser);

    void remove(List<String> ids);

    MaintenanceJobEntity save(MaintenanceJobEntity job);

    void save(List<MaintenanceJobCEntity> childList);

    List<MaintenanceJobEntity> findJobByType(String type);

    void save(MaintenanceJobTriggerEntity jobC);

    MaintenanceJobEntity findById(String contentId);

}
