package org.thingsboard.server.controller.base;

import java.util.List;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.thingsboard.server.dao.base.IBaseDatabaseUpgradeService;
import org.thingsboard.server.dao.model.sql.base.BaseDatabaseUpgrade;
import org.thingsboard.server.dao.util.imodel.query.base.BaseDatabaseUpgradePageRequest;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;
import org.thingsboard.server.service.aspect.annotation.MonitorPerformance;

/**
 * 平台管理-数据库升级记录Controller
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
@Api(tags = "平台管理-数据库升级记录")
@RestController
@RequestMapping("api/base/database/upgrade")
public class BaseDatabaseUpgradeController extends BaseController {

    @Autowired
    private IBaseDatabaseUpgradeService baseDatabaseUpgradeService;

    /**
     * 查询平台管理-数据库升级记录列表
     */
    @MonitorPerformance(description = "平台管理-查询数据库升级记录列表")
    @ApiOperation(value = "查询数据库升级记录列表")
    @GetMapping("/list")
    public IstarResponse list(BaseDatabaseUpgradePageRequest baseDatabaseUpgrade) {
        return IstarResponse.ok(baseDatabaseUpgradeService.selectBaseDatabaseUpgradeList(baseDatabaseUpgrade));
    }

    /**
     * 获取平台管理-数据库升级记录详细信息
     */
    @MonitorPerformance(description = "平台管理-查询数据库升级记录详情")
    @ApiOperation(value = "查询数据库升级记录详情")
    @GetMapping(value = "/getDetail")
    public IstarResponse getInfo(String id) {
        return IstarResponse.ok(baseDatabaseUpgradeService.selectBaseDatabaseUpgradeById(id));
    }

    /**
     * 新增平台管理-数据库升级记录
     */
    @MonitorPerformance(description = "平台管理-新增数据库升级记录")
    @ApiOperation(value = "新增数据库升级记录")
    @PostMapping("/add")
    public IstarResponse add(@RequestBody BaseDatabaseUpgrade baseDatabaseUpgrade) {
        return IstarResponse.ok(baseDatabaseUpgradeService.insertBaseDatabaseUpgrade(baseDatabaseUpgrade));
    }

    /**
     * 修改平台管理-数据库升级记录
     */
    @MonitorPerformance(description = "平台管理-修改数据库升级记录")
    @ApiOperation(value = "修改数据库升级记录")
    @PostMapping("/edit")
    public IstarResponse edit(@RequestBody BaseDatabaseUpgrade baseDatabaseUpgrade) {
        return IstarResponse.ok(baseDatabaseUpgradeService.updateBaseDatabaseUpgrade(baseDatabaseUpgrade));
    }

    /**
     * 删除平台管理-数据库升级记录
     */
    @MonitorPerformance(description = "平台管理-删除数据库升级记录")
    @ApiOperation(value = "删除数据库升级记录")
    @DeleteMapping("/deleteIds")
    public IstarResponse remove(@RequestBody List<String> ids) {
        return IstarResponse.ok(baseDatabaseUpgradeService.deleteBaseDatabaseUpgradeByIds(ids));
    }
}
