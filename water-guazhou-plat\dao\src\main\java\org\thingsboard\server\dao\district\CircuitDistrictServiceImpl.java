package org.thingsboard.server.dao.district;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.smartManagement.district.SMCircuitDistrict;
import org.thingsboard.server.dao.sql.smartManagement.district.CircuitDistrictAreaMapper;
import org.thingsboard.server.dao.sql.smartManagement.district.CircuitDistrictMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.TreeQuery;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.district.CircuitDistrictPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.district.CircuitDistrictSaveRequest;

import java.util.Arrays;
import java.util.List;

@Service
public class CircuitDistrictServiceImpl implements CircuitDistrictService {
    @Autowired
    private CircuitDistrictMapper mapper;

    @Autowired
    private CircuitDistrictAreaMapper areaMapper;

    @Override
    public IPage<SMCircuitDistrict> findAllConditional(CircuitDistrictPageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    public SMCircuitDistrict findGlobalTree(String tenantId) {
        //noinspection ArraysAsListWithZeroOrOneArgument
        return QueryUtil.buildMixTree(
                Arrays.asList(mapper.getTopRoot()),
                TreeQuery.of(parentId -> mapper.findChildren(parentId, tenantId)),
                TreeQuery.of(areaMapper::findAllByDistrictId, 1)
        ).stream().findFirst().orElse(null);
    }

    @Override
    public SMCircuitDistrict findTreeByParentId(String id, String tenantId) {
        //noinspection ArraysAsListWithZeroOrOneArgument
        return QueryUtil.buildMixTree(
                Arrays.asList(mapper.selectById(id)),
                parentId -> mapper.findChildren(parentId, tenantId)
        ).stream().findFirst().orElse(null);
    }

    @Override
    public List<SMCircuitDistrict> findChildrenTreeByParentId(String id, String tenantId) {
        return QueryUtil.buildMixTree(
                mapper.findChildren(id, tenantId),
                parentId -> mapper.findChildren(parentId, tenantId)
        );
    }

    @Override
    public List<SMCircuitDistrict> findRoots(String tenantId) {
        return mapper.findActualRoots(tenantId);
    }

    @Override
    public SMCircuitDistrict save(CircuitDistrictSaveRequest entity) {
        return QueryUtil.saveOrUpdateOneByRequest(entity, mapper::insert, mapper::updateFully);
    }

    @Override
    public boolean update(SMCircuitDistrict entity) {
        return mapper.update(entity);
    }

    @Override
    public boolean delete(String id) {
        return mapper.delete(id) > 0;
    }
}
