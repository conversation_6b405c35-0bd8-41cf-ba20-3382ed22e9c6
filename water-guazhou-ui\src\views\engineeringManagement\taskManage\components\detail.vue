<template>
  <div>
    <div class="top">
      <el-row :gutter="20">
        <el-col :span="2"> 申请编号： </el-col>
        <el-col :span="4">
          {{ state.taskInfo?.code }}
        </el-col>
        <el-col :span="2"> 登记日期： </el-col>
        <el-col :span="16">
          {{ formatTime(state.taskInfo?.createTime) }}
        </el-col>
        <el-col :span="2"> 工程地址： </el-col>
        <el-col :span="4">
          {{ state.taskInfo?.address }}
        </el-col>
        <el-col :span="2"> 工程类型： </el-col>
        <el-col :span="16">
          {{ state.taskInfo?.typeName }}
        </el-col>
        <el-col :span="2"> 当前状态： </el-col>
        <el-col :span="6">
          <span
            :style="{
              color: state.taskInfo?.status === '已完成' ? 'red' : 'green'
            }"
          >
            {{ state.taskInfo?.status=='1'?'进行中': state.taskInfo?.status }}
          </span>
        </el-col>
        <el-col :span="16">
          <el-dropdown trigger="click">
            <el-button type="primary" split-button>
              下载合同
              <el-icon class="el-icon--right">
                <ArrowDown />
              </el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item
                  v-for="(item, index) in state.contractList"
                  :key="index"
                  @click="downloadContract(item)"
                >
                  {{ item.name }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </el-col>
        <el-col :span="16">
          <el-button
            type="primary"
            :disabled="
              state.taskInfo?.status === '已完成' ||
              state.currentContent?.status === '进行中'
            "
            @click="submitForm"
          >
            提交
          </el-button>
          <!-- <el-button type="success">
            备注
          </el-button> -->
          <el-button type="warning" @click="showDetailDialog">
            流程明细
          </el-button>
          <el-button
            type="primary"
            :disabled="
              state.taskInfo?.status === '已完成' ||
              state.currentContent?.status === '进行中'
            "
            @click="showUploadDialog"
          >
            上传资料
          </el-button>
        </el-col>
      </el-row>
    </div>
    <Tabs v-model="state.activeName" :config="tabsConfig"></Tabs>
    <div style="margin-top: 20px">
      <v-form-render
        ref="vFormRef"
        :form-json="state.formJson"
        :form-data="state.formData"
        :option-data="state.optionData"
      >
      </v-form-render>
      <div style="margin-top: 20px">
        <div v-for="(item, index) in state.fileTypes" :key="index">
          <div
            v-for="(href, i) in JSON.parse(item.file)"
            :key="i"
            style="color: #409eff; padding: 10px"
          >
            <a
              style="border-bottom: 1px solid #409eff"
              @click="downLoadFile(href.url, href.name)"
              >{{ href.name }}</a
            >
          </div>
        </div>
      </div>
    </div>
    <DialogForm ref="refDialog" :config="uploadFileConfig"> </DialogForm>
    <DialogForm ref="refDetailDialog" :config="detailConfig">
      <FormTable :config="detailFormConfig"> </FormTable>
    </DialogForm>
  </div>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs';
import { ArrowDown } from '@element-plus/icons-vue';
import {
  getStepByMainId,
  getFileByTypeId,
  getAttachmentByStepId
} from '@/api/engineeringManagement/process';
import {
  saveInstallStep,
  saveAttachmentFile,
  applyInstallById
} from '@/api/engineeringManagement/applyInstall';
import useGlobal from '@/hooks/global/useGlobal';
import { SLConfirm } from '@/utils/Message';
import { downLoadFile } from '@/utils/printUtils';

const { $messageError, $messageSuccess, $messageWarning } = useGlobal();
const vFormRef = ref<any>(null);
const refDialog = ref<IDialogFormIns>();
const refDetailDialog = ref<IDialogFormIns>();
const props = defineProps<{
  taskInfo: any;
  taskId: string;
}>();
const state = reactive<{
  formData: any;
  formJson: any;
  optionData: any;
  activeName: string;
  currentStepId: string;
  contractList: any[];
  flowDetail: any[];
  fileTypes: any[];
  fileDetail: any;
  fileArray: any[];
  taskInfo: any;
  currentContent: any;
}>({
  activeName: '',
  formJson: {},
  optionData: {},
  formData: {},
  currentStepId: '',
  contractList: [],
  flowDetail: [],
  fileTypes: [],
  fileDetail: {},
  fileArray: [],
  taskInfo: {},
  currentContent: null
});

watch(
  () => props.taskId,
  (val) => {
    console.log(val);
  }
);
// 步骤tabs
const tabsConfig = reactive<ITabs>({
  type: 'tabs',
  tabType: 'inset',
  width: '100%',
  tabs: [],
  handleTabClick: (tab: any) => {
    state.currentContent = state.flowDetail?.find(
      (flow) => flow.id === tab.props.name
    );
    console.log(state.currentContent);
    vFormRef.value?.setFormJson(JSON.parse(state.currentContent?.content));
    nextTick(() => {
      vFormRef.value?.setFormData(JSON.parse(state.currentContent?.formData));
    });
    state.fileTypes = [];
    getFileTypes();
  }
});
const uploadFileConfig = reactive<IDialogFormConfig>({
  title: '资料上传',
  group: [
    {
      fields: [
        {
          type: 'select',
          label: '文档类型',
          field: 'fileType',
          options: [],
          rules: [{ required: true, message: '请选择文档类型' }],
          onChange: (val: any) => {
            state.fileDetail = state.fileTypes?.find(
              (f) => f.id === val
            ) as any;
            const hint = uploadFileConfig.group[0].fields?.find(
              (field) => field.field === 'hint'
            ) as IFormAnnex;
            hint.text = state.fileDetail.num;
            const formData = refDialog.value?.refForm?.dataForm;
            console.log(refDialog.value?.refForm?.dataForm);
            const file = state.fileArray.find((f) => f.fileType === val) as any;
            if (!file) {
              state.fileArray.push(formData);
            }
          }
        },
        { type: 'input', label: '文档编号', field: 'code' },
        { type: 'hint', label: '上传文件个数', field: 'hint', text: '0' },
        {
          type: 'file',
          label: '选中文件',
          field: 'file',
          returnType: 'arrStr',
          rules: [{ required: true, message: '选中文件' }]
        }
      ]
    }
  ],
  submit: (params: any) => {
    if (params.file?.split(',').length >= state.fileDetail.num) {
      state.fileArray = state.fileArray.map((data) => {
        return {
          ...state.fileDetail,
          file: data.file
        };
      });
      console.log(state.fileArray);
      saveAttachmentFile(state.fileArray)
        .then((res) => {
          if (res.data?.code === 200) {
            refDialog.value?.closeDialog();
            refDialog.value?.refForm?.resetForm();
            getFileTypes();
            $messageSuccess('上传成功！');
          } else {
            $messageError('上传失败！');
          }
        })
        .catch((error) => {
          $messageError(error);
        });
    } else {
      $messageWarning('请上传指定数量文件');
    }
  }
});

// 明细弹框
const detailConfig = reactive<IDialogFormConfig>({
  title: '流程明细',
  desTroyOnClose: true,
  group: [],
  submit: undefined
});
// 明细弹框配置
const detailFormConfig = reactive<ITable>({
  columns: [
    {
      label: '流程',
      prop: 'title',
      cellStyle: (row) => {
        return {
          color:
            row.status === '已完成'
              ? '#409eff'
              : row.id === state.taskInfo?.currentStepId
                ? 'red'
                : ''
        };
      }
    },
    {
      label: '开始时间',
      prop: 'startTime',
      formatter: (row) => formatTime(row.startTime)
    },
    {
      label: '结束时间',
      prop: 'endTime',
      formatter: (row) => formatTime(row.endTime)
    },
    { label: '工作人员', prop: 'operatorName' },
    {
      label: '说明',
      prop: 'status',
      formatter: (row) => (row.status === '已完成' ? '√' : ' ')
    }
  ],
  dataList: [],
  pagination: { hide: true }
});
// 显示流程明细
const showDetailDialog = () => {
  refDetailDialog.value?.openDialog();
  // const table = detailConfig.group[0]?.fields.find(field => field.type === 'table') as any
  console.log(state.flowDetail);
  detailFormConfig.dataList = state.flowDetail;
};
// 获取附件上传弹框
const showUploadDialog = async () => {
  if (state.fileTypes.length > 0) {
    refDialog.value?.openDialog();
    const fileType = uploadFileConfig.group[0].fields.find(
      (field) => field.field === 'fileType'
    ) as IFormSelect;
    fileType.options = state.fileTypes.map((data) => {
      return { label: data.name, value: data.id, data: { num: data.num } };
    });
  } else {
    $messageWarning('无需上传文件');
  }
};
// 下载合同
const downloadContract = async (item) => {
  const suffix = item.file.split('/').pop().split('.').pop();
  downLoadFile(item.file, `${item.name}.${suffix}`);
};

// 提交表单
const submitForm = () => {
  SLConfirm('确定提交吗?', '提示').then(() => {
    const uploadFiles = state.fileTypes.filter(
      (file) =>
        (!file.file || file.num > file.file.split(',').length) && file.num > 0
    );
    console.log(uploadFiles);
    if (uploadFiles.length === 0) {
      vFormRef.value
        ?.getFormData()
        .then((formData) => {
          // Form Validation OK
          const params = {
            mainId: props.taskInfo.id,
            id: state.currentContent.id,
            status: '已完成',
            formData: JSON.stringify(formData)
          };
          saveInstallStep(params)
            .then((res) => {
              if (res.data?.code === 200) {
                $messageSuccess('提交成功');
                initData();
              } else {
                $messageError(res.data?.message);
              }
            })
            .catch((err) => {
              $messageError(err);
            });
        })
        .catch((error) => {
          // Form Validation failed
          $messageError(error);
        });
    } else {
      $messageWarning('请上传所需资料');
    }
  });
};

// 格式化时间
const formatTime = (val: string) => {
  return val ? dayjs(val).format('YYYY-MM-DD HH:mm:ss') : '';
};
//
const getContractFileList = async (id: string) => {
  const res = await getFileByTypeId(id);
  state.contractList = res.data?.data.contractTemplateList;
  console.log(res.data?.data);
};

// 获取步骤需要上传的资料类型列表
const getFileTypes = async () => {
  const fileTypes = await getAttachmentByStepId(
    props.taskInfo.id,
    state.currentContent.id
  );
  state.fileTypes = fileTypes.data?.data;
};

// 获取工程任务的详情
const getApplyInstallById = async () => {
  const res = await applyInstallById(props.taskInfo.id);
  state.taskInfo = res.data?.data;
};

// 获取详情信息
const initData = async () => {
  await getApplyInstallById();
  const currentStepId = state.taskInfo?.currentStepId;

  const res = await getStepByMainId(state.taskInfo?.id);
  const result = res.data?.data;
  state.flowDetail = result;
  tabsConfig.tabs = result.map((data) => {
    return {
      label: data.title,
      value: data.id,
      disabled:
        data.status !== '已完成' && state.taskInfo?.currentStepId !== data.id,
      data: { content: data.content, formData: data.formData, id: data.id }
    };
  });

  state.activeName = currentStepId;
  state.currentContent = result.find((data) => data.id === currentStepId);
  vFormRef.value?.setFormJson(JSON.parse(state.currentContent?.content));
  await nextTick(() => {
    vFormRef.value?.setFormData(JSON.parse(state.currentContent?.formData));
  });
  await getContractFileList(state.taskInfo?.type);
  await getFileTypes();
};

onMounted(async () => {
  await initData();
});
</script>

<style scoped>
.top {
  line-height: 30px;
  margin-bottom: 15px;
}
</style>
