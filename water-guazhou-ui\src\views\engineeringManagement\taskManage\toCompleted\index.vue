<template>
  <div class="wrapper">
    <CardSearch ref="refSearch" :config="searchConfig"> </CardSearch>
    <CardTable class="card-table" :config="tableConfig"> </CardTable>
    <SLDrawer ref="refDetail" :config="detailDrawerConfig">
      <detail :task-info="state.taskInfo" :task-id="'1'"></detail>
    </SLDrawer>
  </div>
</template>

<script lang="ts" setup>
import { View } from '@element-plus/icons-vue';
import detail from '../../applyInstallManagement/AITask/components/detail.vue';
import { ICONS } from '@/common/constans/common';
import { processTypeList } from '@/api/engineeringManagement/process';
import { projectManageList } from '@/api/engineeringManagement/applyInstall';
import { formatDate } from '@/utils/DateFormatter';

const state = reactive<{
  typeList: any;
  taskInfo: any;
}>({
  typeList: [],
  taskInfo: {}
});

const refDetail = ref<ISLDrawerIns>();
const refSearch = ref<ICardSearchIns>();
const searchConfig = reactive<ISearch>({
  filters: [
    { label: '工程编号', field: 'code', type: 'input' },
    {
      label: '工程类型',
      field: 'type',
      type: 'select',
      options: computed(() => state.typeList) as any
    },
    { label: '步骤名称', field: 'currentStep', type: 'input' }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          icon: ICONS.QUERY,
          click: () => refreshData()
        }
      ]
    }
  ]
});
// 查看流程详情
const detailDrawerConfig = reactive<IDrawerConfig>({
  title: '',
  cancel: false,
  width: '80%',
  group: []
});
const tableConfig = reactive<ICardTable>({
  indexVisible: true,
  columns: [
    { label: '工程编号', prop: 'code' },
    { label: '任务类型', prop: 'typeName' },
    { label: '当前步骤', prop: 'currentStep' },
    { label: '任务地址', prop: 'address' },
    {
      label: '创建时间',
      prop: 'createTime',
      formatter: (row: any, val: any) => {
        return formatDate(val);
      }
    }
  ],
  dataList: [],
  operationWidth: 100,
  operations: [
    {
      perm: true,
      text: '查看',
      isTextBtn: false,
      type: 'success',
      svgIcon: shallowRef(View),
      click: (row) => {
        detailDrawerConfig.title = row.code;
        refDetail.value?.openDrawer();
        state.taskInfo = row;
      }
    }
  ],
  pagination: {
    refreshData: ({ page, size }) => {
      tableConfig.pagination.page = page;
      tableConfig.pagination.limit = size;
      refreshData();
    }
  }
});

onBeforeMount(async () => {
  const result = await processTypeList({ page: 1, size: 9999 });
  state.typeList = result.data?.data.data;
  const data = result.data?.data.data;
  state.typeList = data.map((d) => {
    return {
      label: d.name,
      value: d.id
    };
  });
});

const refreshData = async () => {
  //
  const query = refSearch.value?.queryParams || {};
  const params = {
    ...query,
    status: '已完成',
    page: tableConfig.pagination.page || 1,
    size: tableConfig.pagination.limit || 20
  };
  const res = await projectManageList(params);
  tableConfig.pagination.total = res.data?.data?.total;
  tableConfig.dataList = res.data?.data?.data;
};

onMounted(async () => {
  await refreshData();
});
</script>

<style scoped></style>
