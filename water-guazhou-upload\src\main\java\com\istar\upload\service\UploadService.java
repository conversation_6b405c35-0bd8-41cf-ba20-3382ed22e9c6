package com.istar.upload.service;

import com.alibaba.fastjson.JSONObject;
import com.istar.upload.config.UploadConfigProperty;
import com.istar.upload.constants.FileTypeConstants;
import com.istar.upload.util.MinioClient;
import com.istar.upload.util.ZipUtil;
import io.minio.ObjectWriteResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileInputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Service
@Slf4j
public class UploadService {

    @Autowired
    private UploadConfigProperty uploadConfigProperty;

    @Autowired
    private MinioClient minioClient;

    @Value("${minio.bucketName}")
    private String bucketName;

    /**
     * 文件上传
     *
     * @param file 文件
     * @return 文件url
     */
    public String uploadFile(MultipartFile file, String fileType) {
        String url = null;
        switch (fileType) {
            case FileTypeConstants.IMAGE:
                url = uploadImage(file);
                break;
            case FileTypeConstants.OTHER:
                url = uploadFile(file);
                break;
            default:
                throw new RuntimeException("非法文件类型! fileType: [{" + fileType + "}]");
        }
        return url;
    }

    public List<JSONObject> uploadZipFile(MultipartFile zipFile, String fileType) {
        String pathName = System.getProperty("server.tomcat.basedir") + "/shpFileCache/";
//        String pathName = "C:/Users/<USER>/Desktop/tmp/shpFileCache/";
        String dec = System.getProperty("server.tomcat.basedir") + "/shpFileCache/";
//        String dec = "C:/Users/<USER>/Desktop/tmp/shpFileCache/";
        File file = new File(pathName);
        //如果文件夹不存在  创建文件夹
        if (!file.exists()) {
            file.mkdir();
        }
        //获取文件名（包括后缀）
        String pname = zipFile.getOriginalFilename();
        pathName = pathName + UUID.randomUUID().toString().replaceAll("-", "") + "-" + pname;
        List<JSONObject> resultList = new ArrayList<>();
        try {
            File dest = new File(pathName);
            zipFile.transferTo(dest);
            // 获取解压出来的文件名 不带后缀
            List<String> fileNames = ZipUtil.unZip(dest, dec);
            // 读取文件夹
            List<File> files = ZipUtil.getAllFile(dec);
            if (files != null && files.size() > 0) {
                for (File img : files) {
                    try {
                        String filename = img.getName().substring(0, img.getName().lastIndexOf("."));
                        JSONObject result = new JSONObject();
                        result.put("id", filename);
                        String url = this.uploadImage(img);
                        result.put("usr", url);

                        resultList.add(result);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }

            //解析完成   删除本次解析中生成的文件  删除此目录下的所有文件
            ZipUtil.deleteFile(dec);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return resultList;
    }

    /**
     * 图片上传
     *
     * @param file 图片
     * @return 图片url
     */
    private String uploadImage(MultipartFile file) {
        try {
            // 获取文件后缀名
            String filename = file.getOriginalFilename();
            String suffix = filename.substring(filename.indexOf(".") + 1);

//            // 对于SVG文件不进行图片验证
//            String contentType = file.getContentType();
//            boolean isSvg = contentType != null && contentType.equals("image/svg+xml");
//            // 非SVG文件需要进行图片验证
//            if (!isSvg) {
//                try {
//                    // 校验图片内容，防止修改后缀名恶意上传
//                    BufferedImage image = ImageIO.read(file.getInputStream());
//                    if (image == null || image.getWidth() == 0 || image.getHeight() == 0) {
//                        throw new RuntimeException("非法文件!");
//                    }
//                } catch (Exception e) {
//                    log.error("读取图片数据失败! e = ", e);
//                    throw new RuntimeException("非法文件!");
//                }
//            }

            // 上传文件
            String objectName = UUID.randomUUID().toString().replaceAll("-", "") + "." + suffix;
            ObjectWriteResponse writeResponse =
                    this.minioClient.uploadFile(null, objectName, file.getInputStream());
            // 返回文件路径
            return uploadConfigProperty.getImageServer() + bucketName + "/" + objectName;
        } catch (Exception e) {
            log.error("读取文件内容发生IO异常.  e = ", e);
            throw new RuntimeException("读取图片数据失败!");
        }
    }

    /**
     * 图片上传
     *
     * @param file 图片
     * @return 图片url
     */
    private String uploadImage(File file) {
        try {
            // 获取文件后缀名
            String filename = file.getName();
            String suffix = filename.substring(filename.lastIndexOf(".") + 1);

            // 对于SVG文件不进行图片验证
            boolean isSvg = suffix.equalsIgnoreCase("svg");

            // 非SVG文件需要进行图片验证
            if (!isSvg) {
                try {
                    // 校验图片内容，防止修改后缀名恶意上传
                    BufferedImage image = ImageIO.read(file);
                    if (image == null || image.getWidth() == 0 || image.getHeight() == 0) {
                        throw new RuntimeException("非法文件!");
                    }
                } catch (Exception e) {
                    log.error("读取图片数据失败! e = ", e);
                    throw new RuntimeException("非法文件!");
                }
            }

            // 上传文件
            FileInputStream input = new FileInputStream(file);
            String objectName = UUID.randomUUID().toString().replaceAll("-", "") + "." + suffix;
            ObjectWriteResponse writeResponse =
                    this.minioClient.uploadFile(null, objectName, input);
            // 返回文件路径
            return uploadConfigProperty.getImageServer() + bucketName + "/" + objectName;
        } catch (Exception e) {
            log.error("读取文件内容发生IO异常.  e = ", e);
            throw new RuntimeException("读取图片数据失败!");
        }
    }

    public String uploadFile(MultipartFile file) {
        try {
            // 获取文件后缀名
            String filename = file.getOriginalFilename();
            String suffix = filename.substring(filename.indexOf(".") + 1);

            // 上传文件
            String objectName = UUID.randomUUID().toString().replaceAll("-", "") + "." + suffix;
            ObjectWriteResponse writeResponse =
                    this.minioClient.uploadFile(null, objectName, file.getInputStream());
            // 返回文件路径
            return uploadConfigProperty.getImageServer() + bucketName + "/" + objectName;
        } catch (Exception e) {
            log.error("读取文件内容发生IO异常.  e = ", e);
            throw new RuntimeException("读取图片数据失败!");
        }
    }
}
