package org.thingsboard.server.dao.util.imodel.query.smartProduction.guard;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartProduction.guard.GuardEventRecord;
import org.thingsboard.server.dao.util.imodel.query.PageableQueryEntity;

@Getter
@Setter
public class GuardEventRecordPageRequest extends PageableQueryEntity<GuardEventRecord> {
    // 所属日志id
    private String recordId;

}
