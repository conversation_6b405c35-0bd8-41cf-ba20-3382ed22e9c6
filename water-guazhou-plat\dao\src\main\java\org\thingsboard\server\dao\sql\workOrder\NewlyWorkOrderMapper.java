package org.thingsboard.server.dao.sql.workOrder;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.thingsboard.server.dao.model.DTO.WorkOrderVisitMsgDTO;
import org.thingsboard.server.dao.model.sql.statistic.StatisticCountAndPercent;
import org.thingsboard.server.dao.model.sql.statistic.StatisticLong;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrder;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrderStatus;
import org.thingsboard.server.dao.util.imodel.query.smartService.WorkOrderListMsgRequest;
import org.thingsboard.server.dao.util.imodel.query.workOrder.WorkOrderCompleteCountRequest;
import org.thingsboard.server.dao.util.imodel.query.workOrder.WorkOrderCountOfStatusRequest;
import org.thingsboard.server.dao.util.imodel.query.workOrder.WorkOrderPageRequest;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Set;

import static org.thingsboard.server.dao.model.sql.workOrder.WorkOrderStage.COMPLETED;

@Mapper
public interface NewlyWorkOrderMapper extends BaseMapper<WorkOrder> {
    String getNameById(String id);

    @Select("select count(id) from work_order wo where wo.id = #{id} and wo.status = #{stage}")
    boolean isStatus(@Param("id") String id, @Param("stage") String stage);

    @Select("select status from work_order where id = #{orderId}")
    String getStatus(String orderId);

    int save(WorkOrder workOrder);

    @Override
    default int insert(WorkOrder entity) {
        return save(entity);
    }

    @Select("select step_process_user_id from work_order where id = #{orderId}")
    String getStepProcessUserId(String orderId);

    @Select("select count(id) from work_order wo where wo.id = #{id} and step_process_user_id is not null")
    boolean isAssigned(String id);

    @Select("select serial_no from work_order where id = #{id}")
    String getSerialNo(String id);

    List<StatisticLong> countByOrganizerTimed(@Param("from") Date from, @Param("to") Date to, @Param("tenantId") String tenantId);

    List<StatisticLong> countByTypeTimed(@Param("from") Date from, @Param("to") Date to, @Param("tenantId") String tenantId);

    // 获取上一工单细节的type
    String getPrevProcessUseStage(@Param("orderId") String orderId, @Param("order") int order);

    // 获取上一工单细节的ProcessUserId
    String getPrevProcessUserId(@Param("orderId") String orderId, @Param("order") int order);

    // 获取上一工单细节的NextProcessUserId
    String getPrevNextProcessUserId(@Param("orderId") String orderId, @Param("order") int order);

    // 获取首个操作的操作人 (分派人)
    String getAssignProcessUser(String orderId);

    @Select("select * from work_order where parent_id = #{id}")
    List<WorkOrder> children(String id);

    @Select("select count(id) from work_order wo where wo.id = #{id} and step_process_user_id = #{userId}")
    boolean isUserProcess(@Param("id") String id, @Param("userId") String userId);

    List<WorkOrder> findByPage(@Param("offset") long offset, @Param("size") long size, @Param("req") WorkOrderPageRequest request, @Param("stages") Collection<WorkOrderStatus> stages);

    long countByPageRequest(@Param("req") WorkOrderPageRequest request, @Param("stages") Collection<WorkOrderStatus> stages);

    WorkOrder findById(String id);

    List faultCount(@Param("page") int page, @Param("size") int size, @Param("level") String level, @Param("source") String source, @Param("start") Date start, @Param("end") Date end, @Param("tenantId") String tenantId);

    List workTimeCount(@Param("page") int page, @Param("size") int size, @Param("source") String source, @Param("start") Date start, @Param("end") Date end, @Param("tenantId") String tenantId);

    int faultCountCount(@Param("level") String level, @Param("source") String source, @Param("start") Date start, @Param("end") Date end, @Param("tenantId") String tenantId);

    int workTimeCountCount(@Param("source") String source, @Param("start") Date start, @Param("end") Date end, @Param("tenantId") String tenantId);

    default StatisticCountAndPercent countCompleteBetweenTime(WorkOrderCompleteCountRequest req) {
        return new StatisticCountAndPercent(countBetweenStage(req.loadSet(COMPLETED)), countBetweenStage(req.unloadSet()));
    }

    List<StatisticLong> countByTypeTimedLimitedByProcessingUser(WorkOrderCompleteCountRequest req);

    int countBetweenStage(WorkOrderCountOfStatusRequest req);

    Integer totalStatusOfUser(@Param("userId") String userId, @Param("status") Set<WorkOrderStatus> status);

    Integer totalOfUser(String userId);

    boolean canSubmit(String id);

    String onTimeCompleteStatistic(String tenantId);

    String byTimeStatistic(String tenantId);

    Page<WorkOrderVisitMsgDTO> getVisitList(Page<WorkOrderVisitMsgDTO> page, @Param("req") WorkOrderListMsgRequest request);
}
