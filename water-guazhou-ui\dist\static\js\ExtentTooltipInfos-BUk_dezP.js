import{e as t,y as o,a as s}from"./Point-WxyopZva.js";import{t as l,j as r}from"./automaticLengthMeasurementUtils-DljoUgEz.js";let i=class extends l{constructor(p){super(p),this.type="extent-rotate",this.angle=0}};t([o()],i.prototype,"type",void 0),t([o()],i.prototype,"angle",void 0),i=t([s("esri.views.interactive.tooltip.ExtentRotateTooltipInfo")],i);let e=class extends l{constructor(p){super(p),this.type="extent-scale",this.xScale=0,this.yScale=0,this.xSize=r,this.ySize=r}};t([o()],e.prototype,"type",void 0),t([o()],e.prototype,"xScale",void 0),t([o()],e.prototype,"yScale",void 0),t([o()],e.prototype,"xSize",void 0),t([o()],e.prototype,"ySize",void 0),e=t([s("esri.views.interactive.tooltip.ExtentScaleTooltipInfo")],e);export{i,e as p};
