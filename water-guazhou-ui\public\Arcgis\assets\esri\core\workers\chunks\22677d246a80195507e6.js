"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[6774],{66774:(t,e,_)=>{_.r(e),_.d(e,{p:()=>i});var n,r,o,p={};n={get exports(){return p},set exports(t){p=t}},r="undefined"!=typeof document&&document.currentScript?document.currentScript.src:void 0,"undefined"!=typeof __filename&&(r=r||__filename),o=function(t){var e,_;(t=void 0!==(t=t||{})?t:{}).ready=new Promise((function(t,n){e=t,_=n}));var n,o,p,i=Object.assign({},t),c="./this.program",s="object"==typeof window,a="function"==typeof importScripts,u="object"==typeof process&&"object"==typeof process.versions&&"string"==typeof process.versions.node,P="";if(u){var y=require("fs"),g=require("path");P=a?g.dirname(P)+"/":__dirname+"/",n=(t,e)=>(t=B(t)?new URL(t):g.normalize(t),y.readFileSync(t,e?void 0:"utf8")),p=t=>{var e=n(t,!0);return e.buffer||(e=new Uint8Array(e)),e},o=(t,e,_)=>{t=B(t)?new URL(t):g.normalize(t),y.readFile(t,(function(t,n){t?_(t):e(n.buffer)}))},process.argv.length>1&&(c=process.argv[1].replace(/\\/g,"/")),process.argv.slice(2),process.on("uncaughtException",(function(t){if(!(t instanceof V))throw t})),process.on("unhandledRejection",(function(t){throw t})),t.inspect=function(){return"[Emscripten Module object]"}}else(s||a)&&(a?P=self.location.href:"undefined"!=typeof document&&document.currentScript&&(P=document.currentScript.src),r&&(P=r),P=0!==P.indexOf("blob:")?P.substr(0,P.replace(/[?#].*/,"").lastIndexOf("/")+1):"",n=t=>{var e=new XMLHttpRequest;return e.open("GET",t,!1),e.send(null),e.responseText},a&&(p=t=>{var e=new XMLHttpRequest;return e.open("GET",t,!1),e.responseType="arraybuffer",e.send(null),new Uint8Array(e.response)}),o=(t,e,_)=>{var n=new XMLHttpRequest;n.open("GET",t,!0),n.responseType="arraybuffer",n.onload=()=>{200==n.status||0==n.status&&n.response?e(n.response):_()},n.onerror=_,n.send(null)});var f,l,m=t.print||console.log.bind(console),d=t.printErr||console.warn.bind(console);Object.assign(t,i),i=null,t.arguments&&t.arguments,t.thisProgram&&(c=t.thisProgram),t.quit&&t.quit,t.wasmBinary&&(f=t.wasmBinary),t.noExitRuntime,"object"!=typeof WebAssembly&&z("no native wasm support detected");var E=!1;function b(t,e){t||z(e)}var O,T,S,N,h,M,v,D,R="undefined"!=typeof TextDecoder?new TextDecoder("utf8"):void 0;function A(t,e,_){for(var n=e+_,r=e;t[r]&&!(r>=n);)++r;if(r-e>16&&t.buffer&&R)return R.decode(t.subarray(e,r));for(var o="";e<r;){var p=t[e++];if(128&p){var i=63&t[e++];if(192!=(224&p)){var c=63&t[e++];if((p=224==(240&p)?(15&p)<<12|i<<6|c:(7&p)<<18|i<<12|c<<6|63&t[e++])<65536)o+=String.fromCharCode(p);else{var s=p-65536;o+=String.fromCharCode(55296|s>>10,56320|1023&s)}}else o+=String.fromCharCode((31&p)<<6|i)}else o+=String.fromCharCode(p)}return o}function G(t,e){return t?A(S,t,e):""}function C(t,e,_,n){if(!(n>0))return 0;for(var r=_,o=_+n-1,p=0;p<t.length;++p){var i=t.charCodeAt(p);if(i>=55296&&i<=57343&&(i=65536+((1023&i)<<10)|1023&t.charCodeAt(++p)),i<=127){if(_>=o)break;e[_++]=i}else if(i<=2047){if(_+1>=o)break;e[_++]=192|i>>6,e[_++]=128|63&i}else if(i<=65535){if(_+2>=o)break;e[_++]=224|i>>12,e[_++]=128|i>>6&63,e[_++]=128|63&i}else{if(_+3>=o)break;e[_++]=240|i>>18,e[_++]=128|i>>12&63,e[_++]=128|i>>6&63,e[_++]=128|63&i}}return e[_]=0,_-r}function I(t){for(var e=0,_=0;_<t.length;++_){var n=t.charCodeAt(_);n<=127?e++:n<=2047?e+=2:n>=55296&&n<=57343?(e+=4,++_):e+=3}return e}function j(e){O=e,t.HEAP8=T=new Int8Array(e),t.HEAP16=N=new Int16Array(e),t.HEAP32=h=new Int32Array(e),t.HEAPU8=S=new Uint8Array(e),t.HEAPU16=new Uint16Array(e),t.HEAPU32=M=new Uint32Array(e),t.HEAPF32=v=new Float32Array(e),t.HEAPF64=D=new Float64Array(e)}t.INITIAL_MEMORY;var L=[],U=[],Y=[];function w(t){L.unshift(t)}function F(t){Y.unshift(t)}var x,H=0,X=null;function z(e){t.onAbort&&t.onAbort(e),d(e="Aborted("+e+")"),E=!0,e+=". Build with -sASSERTIONS for more info.";var n=new WebAssembly.RuntimeError(e);throw _(n),n}function Z(t){return t.startsWith("data:application/octet-stream;base64,")}function B(t){return t.startsWith("file://")}function W(t){try{if(t==x&&f)return new Uint8Array(f);if(p)return p(t);throw"both async and sync fetching of the wasm failed"}catch(t){z(t)}}function k(){if(!f&&(s||a)){if("function"==typeof fetch&&!B(x))return fetch(x,{credentials:"same-origin"}).then((function(t){if(!t.ok)throw"failed to load wasm binary file at '"+x+"'";return t.arrayBuffer()})).catch((function(){return W(x)}));if(o)return new Promise((function(t,e){o(x,(function(e){t(new Uint8Array(e))}),e)}))}return Promise.resolve().then((function(){return W(x)}))}function V(t){this.name="ExitStatus",this.message="Program terminated with exit("+t+")",this.status=t}function q(e){for(;e.length>0;)e.shift()(t)}Z(x="pe-wasm.wasm")||(x=function(e){return t.locateFile?t.locateFile(e,P):P+e}(x));var J=[0,31,60,91,121,152,182,213,244,274,305,335],K=[0,31,59,90,120,151,181,212,243,273,304,334];function $(t){var e=I(t)+1,_=C_(e);return _&&C(t,T,_,e),_}function Q(t){try{return l.grow(t-O.byteLength+65535>>>16),j(l.buffer),1}catch(t){}}var tt={};function et(){if(!et.strings){var t={USER:"web_user",LOGNAME:"web_user",PATH:"/",PWD:"/",HOME:"/home/<USER>",LANG:("object"==typeof navigator&&navigator.languages&&navigator.languages[0]||"C").replace("-","_")+".UTF-8",_:c||"./this.program"};for(var e in tt)void 0===tt[e]?delete t[e]:t[e]=tt[e];var _=[];for(var e in t)_.push(e+"="+t[e]);et.strings=_}return et.strings}var _t=[null,[],[]];function nt(t,e){var _=_t[t];0===e||10===e?((1===t?m:d)(A(_,0)),_.length=0):_.push(e)}var rt={c:function(t,e,_){return 0},p:function(t,e,_){},f:function(t,e,_){return 0},d:function(t,e,_,n){},n:function(t){},m:function(t,e){},o:function(t,e,_){},h:function(t,e){var _=new Date(1e3*function(t){return M[t>>2]+4294967296*h[t+4>>2]}(t));h[e>>2]=_.getSeconds(),h[e+4>>2]=_.getMinutes(),h[e+8>>2]=_.getHours(),h[e+12>>2]=_.getDate(),h[e+16>>2]=_.getMonth(),h[e+20>>2]=_.getFullYear()-1900,h[e+24>>2]=_.getDay();var n=0|function(t){return(function(t){return t%4==0&&(t%100!=0||t%400==0)}(t.getFullYear())?J:K)[t.getMonth()]+t.getDate()-1}(_);h[e+28>>2]=n,h[e+36>>2]=-60*_.getTimezoneOffset();var r=new Date(_.getFullYear(),0,1),o=new Date(_.getFullYear(),6,1).getTimezoneOffset(),p=r.getTimezoneOffset(),i=0|(o!=p&&_.getTimezoneOffset()==Math.min(p,o));h[e+32>>2]=i},i:function(t,e,_){var n=(new Date).getFullYear(),r=new Date(n,0,1),o=new Date(n,6,1),p=r.getTimezoneOffset(),i=o.getTimezoneOffset(),c=Math.max(p,i);function s(t){var e=t.toTimeString().match(/\(([A-Za-z ]+)\)$/);return e?e[1]:"GMT"}M[t>>2]=60*c,h[e>>2]=Number(p!=i);var a=s(r),u=s(o),P=$(a),y=$(u);i<p?(M[_>>2]=P,M[_+4>>2]=y):(M[_>>2]=y,M[_+4>>2]=P)},k:function(){z("")},g:function(){return Date.now()},s:function(t,e,_){S.copyWithin(t,e,e+_)},l:function(t){var e=S.length,_=2147483648;if((t>>>=0)>_)return!1;let n=(t,e)=>t+(e-t%e)%e;for(var r=1;r<=4;r*=2){var o=e*(1+.2/r);if(o=Math.min(o,t+100663296),Q(Math.min(_,n(Math.max(t,o),65536))))return!0}return!1},q:function(t,e){var _=0;return et().forEach((function(n,r){var o=e+_;M[t+4*r>>2]=o,function(t,e,_){for(var n=0;n<t.length;++n)T[e++>>0]=t.charCodeAt(n);T[e>>0]=0}(n,o),_+=n.length+1})),0},r:function(t,e){var _=et();M[t>>2]=_.length;var n=0;return _.forEach((function(t){n+=t.length+1})),M[e>>2]=n,0},a:function(t){return 52},e:function(t,e,_,n){return 52},j:function(t,e,_,n,r){return 70},b:function(t,e,_,n){for(var r=0,o=0;o<_;o++){var p=M[e>>2],i=M[e+4>>2];e+=8;for(var c=0;c<i;c++)nt(t,S[p+c]);r+=i}return M[n>>2]=r,0}};(function(){var e={a:rt};function n(e,_){var n=e.exports;t.asm=n,j((l=t.asm.t).buffer),t.asm.Yb,function(t){U.unshift(t)}(t.asm.u),function(e){if(H--,t.monitorRunDependencies&&t.monitorRunDependencies(H),0==H&&X){var _=X;X=null,_()}}()}function r(t){n(t.instance)}function o(t){return k().then((function(t){return WebAssembly.instantiate(t,e)})).then((function(t){return t})).then(t,(function(t){d("failed to asynchronously prepare wasm: "+t),z(t)}))}if(H++,t.monitorRunDependencies&&t.monitorRunDependencies(H),t.instantiateWasm)try{return t.instantiateWasm(e,n)}catch(t){d("Module.instantiateWasm callback failed with error: "+t),_(t)}(f||"function"!=typeof WebAssembly.instantiateStreaming||Z(x)||B(x)||u||"function"!=typeof fetch?o(r):fetch(x,{credentials:"same-origin"}).then((function(t){return WebAssembly.instantiateStreaming(t,e).then(r,(function(t){return d("wasm streaming compile failed: "+t),d("falling back to ArrayBuffer instantiation"),o(r)}))}))).catch(_)})(),t.___wasm_call_ctors=function(){return(t.___wasm_call_ctors=t.asm.u).apply(null,arguments)};var ot=t._emscripten_bind_PeObject_getCode_0=function(){return(ot=t._emscripten_bind_PeObject_getCode_0=t.asm.v).apply(null,arguments)},pt=t._emscripten_bind_PeObject_getName_1=function(){return(pt=t._emscripten_bind_PeObject_getName_1=t.asm.w).apply(null,arguments)},it=t._emscripten_bind_PeObject_getType_0=function(){return(it=t._emscripten_bind_PeObject_getType_0=t.asm.x).apply(null,arguments)},ct=t._emscripten_bind_PeCoordsys_getCode_0=function(){return(ct=t._emscripten_bind_PeCoordsys_getCode_0=t.asm.y).apply(null,arguments)},st=t._emscripten_bind_PeCoordsys_getName_1=function(){return(st=t._emscripten_bind_PeCoordsys_getName_1=t.asm.z).apply(null,arguments)},at=t._emscripten_bind_PeCoordsys_getType_0=function(){return(at=t._emscripten_bind_PeCoordsys_getType_0=t.asm.A).apply(null,arguments)},ut=t._emscripten_bind_VoidPtr___destroy___0=function(){return(ut=t._emscripten_bind_VoidPtr___destroy___0=t.asm.B).apply(null,arguments)},Pt=t._emscripten_bind_PeDatum_getSpheroid_0=function(){return(Pt=t._emscripten_bind_PeDatum_getSpheroid_0=t.asm.C).apply(null,arguments)},yt=t._emscripten_bind_PeDatum_getCode_0=function(){return(yt=t._emscripten_bind_PeDatum_getCode_0=t.asm.D).apply(null,arguments)},gt=t._emscripten_bind_PeDatum_getName_1=function(){return(gt=t._emscripten_bind_PeDatum_getName_1=t.asm.E).apply(null,arguments)},ft=t._emscripten_bind_PeDatum_getType_0=function(){return(ft=t._emscripten_bind_PeDatum_getType_0=t.asm.F).apply(null,arguments)},lt=t._emscripten_bind_PeDefs_get_PE_BUFFER_MAX_0=function(){return(lt=t._emscripten_bind_PeDefs_get_PE_BUFFER_MAX_0=t.asm.G).apply(null,arguments)},mt=t._emscripten_bind_PeDefs_get_PE_NAME_MAX_0=function(){return(mt=t._emscripten_bind_PeDefs_get_PE_NAME_MAX_0=t.asm.H).apply(null,arguments)},dt=t._emscripten_bind_PeDefs_get_PE_MGRS_MAX_0=function(){return(dt=t._emscripten_bind_PeDefs_get_PE_MGRS_MAX_0=t.asm.I).apply(null,arguments)},Et=t._emscripten_bind_PeDefs_get_PE_USNG_MAX_0=function(){return(Et=t._emscripten_bind_PeDefs_get_PE_USNG_MAX_0=t.asm.J).apply(null,arguments)},bt=t._emscripten_bind_PeDefs_get_PE_DD_MAX_0=function(){return(bt=t._emscripten_bind_PeDefs_get_PE_DD_MAX_0=t.asm.K).apply(null,arguments)},Ot=t._emscripten_bind_PeDefs_get_PE_DMS_MAX_0=function(){return(Ot=t._emscripten_bind_PeDefs_get_PE_DMS_MAX_0=t.asm.L).apply(null,arguments)},Tt=t._emscripten_bind_PeDefs_get_PE_DDM_MAX_0=function(){return(Tt=t._emscripten_bind_PeDefs_get_PE_DDM_MAX_0=t.asm.M).apply(null,arguments)},St=t._emscripten_bind_PeDefs_get_PE_UTM_MAX_0=function(){return(St=t._emscripten_bind_PeDefs_get_PE_UTM_MAX_0=t.asm.N).apply(null,arguments)},Nt=t._emscripten_bind_PeDefs_get_PE_PARM_MAX_0=function(){return(Nt=t._emscripten_bind_PeDefs_get_PE_PARM_MAX_0=t.asm.O).apply(null,arguments)},ht=t._emscripten_bind_PeDefs_get_PE_TYPE_NONE_0=function(){return(ht=t._emscripten_bind_PeDefs_get_PE_TYPE_NONE_0=t.asm.P).apply(null,arguments)},Mt=t._emscripten_bind_PeDefs_get_PE_TYPE_GEOGCS_0=function(){return(Mt=t._emscripten_bind_PeDefs_get_PE_TYPE_GEOGCS_0=t.asm.Q).apply(null,arguments)},vt=t._emscripten_bind_PeDefs_get_PE_TYPE_PROJCS_0=function(){return(vt=t._emscripten_bind_PeDefs_get_PE_TYPE_PROJCS_0=t.asm.R).apply(null,arguments)},Dt=t._emscripten_bind_PeDefs_get_PE_TYPE_GEOGTRAN_0=function(){return(Dt=t._emscripten_bind_PeDefs_get_PE_TYPE_GEOGTRAN_0=t.asm.S).apply(null,arguments)},Rt=t._emscripten_bind_PeDefs_get_PE_TYPE_COORDSYS_0=function(){return(Rt=t._emscripten_bind_PeDefs_get_PE_TYPE_COORDSYS_0=t.asm.T).apply(null,arguments)},At=t._emscripten_bind_PeDefs_get_PE_TYPE_UNIT_0=function(){return(At=t._emscripten_bind_PeDefs_get_PE_TYPE_UNIT_0=t.asm.U).apply(null,arguments)},Gt=t._emscripten_bind_PeDefs_get_PE_TYPE_LINUNIT_0=function(){return(Gt=t._emscripten_bind_PeDefs_get_PE_TYPE_LINUNIT_0=t.asm.V).apply(null,arguments)},Ct=t._emscripten_bind_PeDefs_get_PE_STR_OPTS_NONE_0=function(){return(Ct=t._emscripten_bind_PeDefs_get_PE_STR_OPTS_NONE_0=t.asm.W).apply(null,arguments)},It=t._emscripten_bind_PeDefs_get_PE_STR_AUTH_NONE_0=function(){return(It=t._emscripten_bind_PeDefs_get_PE_STR_AUTH_NONE_0=t.asm.X).apply(null,arguments)},jt=t._emscripten_bind_PeDefs_get_PE_STR_AUTH_TOP_0=function(){return(jt=t._emscripten_bind_PeDefs_get_PE_STR_AUTH_TOP_0=t.asm.Y).apply(null,arguments)},Lt=t._emscripten_bind_PeDefs_get_PE_STR_NAME_CANON_0=function(){return(Lt=t._emscripten_bind_PeDefs_get_PE_STR_NAME_CANON_0=t.asm.Z).apply(null,arguments)},Ut=t._emscripten_bind_PeDefs_get_PE_PARM_X0_0=function(){return(Ut=t._emscripten_bind_PeDefs_get_PE_PARM_X0_0=t.asm._).apply(null,arguments)},Yt=t._emscripten_bind_PeDefs_get_PE_PARM_ND_0=function(){return(Yt=t._emscripten_bind_PeDefs_get_PE_PARM_ND_0=t.asm.$).apply(null,arguments)},wt=t._emscripten_bind_PeDefs_get_PE_TRANSFORM_1_TO_2_0=function(){return(wt=t._emscripten_bind_PeDefs_get_PE_TRANSFORM_1_TO_2_0=t.asm.aa).apply(null,arguments)},Ft=t._emscripten_bind_PeDefs_get_PE_TRANSFORM_2_TO_1_0=function(){return(Ft=t._emscripten_bind_PeDefs_get_PE_TRANSFORM_2_TO_1_0=t.asm.ba).apply(null,arguments)},xt=t._emscripten_bind_PeDefs_get_PE_TRANSFORM_P_TO_G_0=function(){return(xt=t._emscripten_bind_PeDefs_get_PE_TRANSFORM_P_TO_G_0=t.asm.ca).apply(null,arguments)},Ht=t._emscripten_bind_PeDefs_get_PE_TRANSFORM_G_TO_P_0=function(){return(Ht=t._emscripten_bind_PeDefs_get_PE_TRANSFORM_G_TO_P_0=t.asm.da).apply(null,arguments)},Xt=t._emscripten_bind_PeDefs_get_PE_HORIZON_RECT_0=function(){return(Xt=t._emscripten_bind_PeDefs_get_PE_HORIZON_RECT_0=t.asm.ea).apply(null,arguments)},zt=t._emscripten_bind_PeDefs_get_PE_HORIZON_POLY_0=function(){return(zt=t._emscripten_bind_PeDefs_get_PE_HORIZON_POLY_0=t.asm.fa).apply(null,arguments)},Zt=t._emscripten_bind_PeDefs_get_PE_HORIZON_LINE_0=function(){return(Zt=t._emscripten_bind_PeDefs_get_PE_HORIZON_LINE_0=t.asm.ga).apply(null,arguments)},Bt=t._emscripten_bind_PeDefs_get_PE_HORIZON_DELTA_0=function(){return(Bt=t._emscripten_bind_PeDefs_get_PE_HORIZON_DELTA_0=t.asm.ha).apply(null,arguments)},Wt=t._emscripten_bind_PeFactory_initialize_1=function(){return(Wt=t._emscripten_bind_PeFactory_initialize_1=t.asm.ia).apply(null,arguments)},kt=t._emscripten_bind_PeFactory_factoryByType_2=function(){return(kt=t._emscripten_bind_PeFactory_factoryByType_2=t.asm.ja).apply(null,arguments)},Vt=t._emscripten_bind_PeFactory_fromString_2=function(){return(Vt=t._emscripten_bind_PeFactory_fromString_2=t.asm.ka).apply(null,arguments)},qt=t._emscripten_bind_PeFactory_getCode_1=function(){return(qt=t._emscripten_bind_PeFactory_getCode_1=t.asm.la).apply(null,arguments)},Jt=t._emscripten_bind_PeGCSExtent_PeGCSExtent_6=function(){return(Jt=t._emscripten_bind_PeGCSExtent_PeGCSExtent_6=t.asm.ma).apply(null,arguments)},Kt=t._emscripten_bind_PeGCSExtent_getLLon_0=function(){return(Kt=t._emscripten_bind_PeGCSExtent_getLLon_0=t.asm.na).apply(null,arguments)},$t=t._emscripten_bind_PeGCSExtent_getSLat_0=function(){return($t=t._emscripten_bind_PeGCSExtent_getSLat_0=t.asm.oa).apply(null,arguments)},Qt=t._emscripten_bind_PeGCSExtent_getRLon_0=function(){return(Qt=t._emscripten_bind_PeGCSExtent_getRLon_0=t.asm.pa).apply(null,arguments)},te=t._emscripten_bind_PeGCSExtent_getNLat_0=function(){return(te=t._emscripten_bind_PeGCSExtent_getNLat_0=t.asm.qa).apply(null,arguments)},ee=t._emscripten_bind_PeGCSExtent___destroy___0=function(){return(ee=t._emscripten_bind_PeGCSExtent___destroy___0=t.asm.ra).apply(null,arguments)},_e=t._emscripten_bind_PeGeogcs_getDatum_0=function(){return(_e=t._emscripten_bind_PeGeogcs_getDatum_0=t.asm.sa).apply(null,arguments)},ne=t._emscripten_bind_PeGeogcs_getPrimem_0=function(){return(ne=t._emscripten_bind_PeGeogcs_getPrimem_0=t.asm.ta).apply(null,arguments)},re=t._emscripten_bind_PeGeogcs_getUnit_0=function(){return(re=t._emscripten_bind_PeGeogcs_getUnit_0=t.asm.ua).apply(null,arguments)},oe=t._emscripten_bind_PeGeogcs_getCode_0=function(){return(oe=t._emscripten_bind_PeGeogcs_getCode_0=t.asm.va).apply(null,arguments)},pe=t._emscripten_bind_PeGeogcs_getName_1=function(){return(pe=t._emscripten_bind_PeGeogcs_getName_1=t.asm.wa).apply(null,arguments)},ie=t._emscripten_bind_PeGeogcs_getType_0=function(){return(ie=t._emscripten_bind_PeGeogcs_getType_0=t.asm.xa).apply(null,arguments)},ce=t._emscripten_bind_PeGeogtran_isEqual_1=function(){return(ce=t._emscripten_bind_PeGeogtran_isEqual_1=t.asm.ya).apply(null,arguments)},se=t._emscripten_bind_PeGeogtran_getGeogcs1_0=function(){return(se=t._emscripten_bind_PeGeogtran_getGeogcs1_0=t.asm.za).apply(null,arguments)},ae=t._emscripten_bind_PeGeogtran_getGeogcs2_0=function(){return(ae=t._emscripten_bind_PeGeogtran_getGeogcs2_0=t.asm.Aa).apply(null,arguments)},ue=t._emscripten_bind_PeGeogtran_getParameters_0=function(){return(ue=t._emscripten_bind_PeGeogtran_getParameters_0=t.asm.Ba).apply(null,arguments)},Pe=t._emscripten_bind_PeGeogtran_loadConstants_0=function(){return(Pe=t._emscripten_bind_PeGeogtran_loadConstants_0=t.asm.Ca).apply(null,arguments)},ye=t._emscripten_bind_PeGeogtran_getCode_0=function(){return(ye=t._emscripten_bind_PeGeogtran_getCode_0=t.asm.Da).apply(null,arguments)},ge=t._emscripten_bind_PeGeogtran_getName_1=function(){return(ge=t._emscripten_bind_PeGeogtran_getName_1=t.asm.Ea).apply(null,arguments)},fe=t._emscripten_bind_PeGeogtran_getType_0=function(){return(fe=t._emscripten_bind_PeGeogtran_getType_0=t.asm.Fa).apply(null,arguments)},le=t._emscripten_bind_PeGTlistExtended_getGTlist_6=function(){return(le=t._emscripten_bind_PeGTlistExtended_getGTlist_6=t.asm.Ga).apply(null,arguments)},me=t._emscripten_bind_PeGTlistExtended_get_PE_GTLIST_OPTS_COMMON_0=function(){return(me=t._emscripten_bind_PeGTlistExtended_get_PE_GTLIST_OPTS_COMMON_0=t.asm.Ha).apply(null,arguments)},de=t._emscripten_bind_PeGTlistExtendedEntry_getEntries_0=function(){return(de=t._emscripten_bind_PeGTlistExtendedEntry_getEntries_0=t.asm.Ia).apply(null,arguments)},Ee=t._emscripten_bind_PeGTlistExtendedEntry_getSteps_0=function(){return(Ee=t._emscripten_bind_PeGTlistExtendedEntry_getSteps_0=t.asm.Ja).apply(null,arguments)},be=t._emscripten_bind_PeGTlistExtendedEntry_Delete_1=function(){return(be=t._emscripten_bind_PeGTlistExtendedEntry_Delete_1=t.asm.Ka).apply(null,arguments)},Oe=t._emscripten_bind_PeGTlistExtendedGTs_getDirection_0=function(){return(Oe=t._emscripten_bind_PeGTlistExtendedGTs_getDirection_0=t.asm.La).apply(null,arguments)},Te=t._emscripten_bind_PeGTlistExtendedGTs_getGeogtran_0=function(){return(Te=t._emscripten_bind_PeGTlistExtendedGTs_getGeogtran_0=t.asm.Ma).apply(null,arguments)},Se=t._emscripten_bind_PeHorizon_getNump_0=function(){return(Se=t._emscripten_bind_PeHorizon_getNump_0=t.asm.Na).apply(null,arguments)},Ne=t._emscripten_bind_PeHorizon_getKind_0=function(){return(Ne=t._emscripten_bind_PeHorizon_getKind_0=t.asm.Oa).apply(null,arguments)},he=t._emscripten_bind_PeHorizon_getInclusive_0=function(){return(he=t._emscripten_bind_PeHorizon_getInclusive_0=t.asm.Pa).apply(null,arguments)},Me=t._emscripten_bind_PeHorizon_getSize_0=function(){return(Me=t._emscripten_bind_PeHorizon_getSize_0=t.asm.Qa).apply(null,arguments)},ve=t._emscripten_bind_PeHorizon_getCoord_0=function(){return(ve=t._emscripten_bind_PeHorizon_getCoord_0=t.asm.Ra).apply(null,arguments)},De=t._emscripten_bind_PeInteger_PeInteger_1=function(){return(De=t._emscripten_bind_PeInteger_PeInteger_1=t.asm.Sa).apply(null,arguments)},Re=t._emscripten_bind_PeInteger_get_val_0=function(){return(Re=t._emscripten_bind_PeInteger_get_val_0=t.asm.Ta).apply(null,arguments)},Ae=t._emscripten_bind_PeInteger_set_val_1=function(){return(Ae=t._emscripten_bind_PeInteger_set_val_1=t.asm.Ua).apply(null,arguments)},Ge=t._emscripten_bind_PeInteger___destroy___0=function(){return(Ge=t._emscripten_bind_PeInteger___destroy___0=t.asm.Va).apply(null,arguments)},Ce=t._emscripten_bind_PeNotationMgrs_get_PE_MGRS_STYLE_NEW_0=function(){return(Ce=t._emscripten_bind_PeNotationMgrs_get_PE_MGRS_STYLE_NEW_0=t.asm.Wa).apply(null,arguments)},Ie=t._emscripten_bind_PeNotationMgrs_get_PE_MGRS_STYLE_OLD_0=function(){return(Ie=t._emscripten_bind_PeNotationMgrs_get_PE_MGRS_STYLE_OLD_0=t.asm.Xa).apply(null,arguments)},je=t._emscripten_bind_PeNotationMgrs_get_PE_MGRS_STYLE_AUTO_0=function(){return(je=t._emscripten_bind_PeNotationMgrs_get_PE_MGRS_STYLE_AUTO_0=t.asm.Ya).apply(null,arguments)},Le=t._emscripten_bind_PeNotationMgrs_get_PE_MGRS_180_ZONE_1_PLUS_0=function(){return(Le=t._emscripten_bind_PeNotationMgrs_get_PE_MGRS_180_ZONE_1_PLUS_0=t.asm.Za).apply(null,arguments)},Ue=t._emscripten_bind_PeNotationMgrs_get_PE_MGRS_ADD_SPACES_0=function(){return(Ue=t._emscripten_bind_PeNotationMgrs_get_PE_MGRS_ADD_SPACES_0=t.asm._a).apply(null,arguments)},Ye=t._emscripten_bind_PeNotationUtm_get_PE_UTM_OPTS_NONE_0=function(){return(Ye=t._emscripten_bind_PeNotationUtm_get_PE_UTM_OPTS_NONE_0=t.asm.$a).apply(null,arguments)},we=t._emscripten_bind_PeNotationUtm_get_PE_UTM_OPTS_NS_0=function(){return(we=t._emscripten_bind_PeNotationUtm_get_PE_UTM_OPTS_NS_0=t.asm.ab).apply(null,arguments)},Fe=t._emscripten_bind_PeNotationUtm_get_PE_UTM_OPTS_NS_STRICT_0=function(){return(Fe=t._emscripten_bind_PeNotationUtm_get_PE_UTM_OPTS_NS_STRICT_0=t.asm.bb).apply(null,arguments)},xe=t._emscripten_bind_PeNotationUtm_get_PE_UTM_OPTS_ADD_SPACES_0=function(){return(xe=t._emscripten_bind_PeNotationUtm_get_PE_UTM_OPTS_ADD_SPACES_0=t.asm.cb).apply(null,arguments)},He=t._emscripten_bind_PeParameter_getValue_0=function(){return(He=t._emscripten_bind_PeParameter_getValue_0=t.asm.db).apply(null,arguments)},Xe=t._emscripten_bind_PeParameter_getCode_0=function(){return(Xe=t._emscripten_bind_PeParameter_getCode_0=t.asm.eb).apply(null,arguments)},ze=t._emscripten_bind_PeParameter_getName_1=function(){return(ze=t._emscripten_bind_PeParameter_getName_1=t.asm.fb).apply(null,arguments)},Ze=t._emscripten_bind_PeParameter_getType_0=function(){return(Ze=t._emscripten_bind_PeParameter_getType_0=t.asm.gb).apply(null,arguments)},Be=t._emscripten_bind_PePCSInfo_getCentralMeridian_0=function(){return(Be=t._emscripten_bind_PePCSInfo_getCentralMeridian_0=t.asm.hb).apply(null,arguments)},We=t._emscripten_bind_PePCSInfo_getDomainMinx_0=function(){return(We=t._emscripten_bind_PePCSInfo_getDomainMinx_0=t.asm.ib).apply(null,arguments)},ke=t._emscripten_bind_PePCSInfo_getDomainMiny_0=function(){return(ke=t._emscripten_bind_PePCSInfo_getDomainMiny_0=t.asm.jb).apply(null,arguments)},Ve=t._emscripten_bind_PePCSInfo_getDomainMaxx_0=function(){return(Ve=t._emscripten_bind_PePCSInfo_getDomainMaxx_0=t.asm.kb).apply(null,arguments)},qe=t._emscripten_bind_PePCSInfo_getDomainMaxy_0=function(){return(qe=t._emscripten_bind_PePCSInfo_getDomainMaxy_0=t.asm.lb).apply(null,arguments)},Je=t._emscripten_bind_PePCSInfo_getNorthPoleLocation_0=function(){return(Je=t._emscripten_bind_PePCSInfo_getNorthPoleLocation_0=t.asm.mb).apply(null,arguments)},Ke=t._emscripten_bind_PePCSInfo_getNorthPoleGeometry_0=function(){return(Ke=t._emscripten_bind_PePCSInfo_getNorthPoleGeometry_0=t.asm.nb).apply(null,arguments)},$e=t._emscripten_bind_PePCSInfo_getSouthPoleLocation_0=function(){return($e=t._emscripten_bind_PePCSInfo_getSouthPoleLocation_0=t.asm.ob).apply(null,arguments)},Qe=t._emscripten_bind_PePCSInfo_getSouthPoleGeometry_0=function(){return(Qe=t._emscripten_bind_PePCSInfo_getSouthPoleGeometry_0=t.asm.pb).apply(null,arguments)},t_=t._emscripten_bind_PePCSInfo_isDensificationNeeded_0=function(){return(t_=t._emscripten_bind_PePCSInfo_isDensificationNeeded_0=t.asm.qb).apply(null,arguments)},e_=t._emscripten_bind_PePCSInfo_isGcsHorizonMultiOverlap_0=function(){return(e_=t._emscripten_bind_PePCSInfo_isGcsHorizonMultiOverlap_0=t.asm.rb).apply(null,arguments)},__=t._emscripten_bind_PePCSInfo_isPannableRectangle_0=function(){return(__=t._emscripten_bind_PePCSInfo_isPannableRectangle_0=t.asm.sb).apply(null,arguments)},n_=t._emscripten_bind_PePCSInfo_generate_2=function(){return(n_=t._emscripten_bind_PePCSInfo_generate_2=t.asm.tb).apply(null,arguments)},r_=t._emscripten_bind_PePCSInfo_get_PE_PCSINFO_OPTION_NONE_0=function(){return(r_=t._emscripten_bind_PePCSInfo_get_PE_PCSINFO_OPTION_NONE_0=t.asm.ub).apply(null,arguments)},o_=t._emscripten_bind_PePCSInfo_get_PE_PCSINFO_OPTION_DOMAIN_0=function(){return(o_=t._emscripten_bind_PePCSInfo_get_PE_PCSINFO_OPTION_DOMAIN_0=t.asm.vb).apply(null,arguments)},p_=t._emscripten_bind_PePCSInfo_get_PE_POLE_OUTSIDE_BOUNDARY_0=function(){return(p_=t._emscripten_bind_PePCSInfo_get_PE_POLE_OUTSIDE_BOUNDARY_0=t.asm.wb).apply(null,arguments)},i_=t._emscripten_bind_PePCSInfo_get_PE_POLE_POINT_0=function(){return(i_=t._emscripten_bind_PePCSInfo_get_PE_POLE_POINT_0=t.asm.xb).apply(null,arguments)},c_=t._emscripten_bind_PePrimem_getLongitude_0=function(){return(c_=t._emscripten_bind_PePrimem_getLongitude_0=t.asm.yb).apply(null,arguments)},s_=t._emscripten_bind_PePrimem_getCode_0=function(){return(s_=t._emscripten_bind_PePrimem_getCode_0=t.asm.zb).apply(null,arguments)},a_=t._emscripten_bind_PePrimem_getName_1=function(){return(a_=t._emscripten_bind_PePrimem_getName_1=t.asm.Ab).apply(null,arguments)},u_=t._emscripten_bind_PePrimem_getType_0=function(){return(u_=t._emscripten_bind_PePrimem_getType_0=t.asm.Bb).apply(null,arguments)},P_=t._emscripten_bind_PeProjcs_getGeogcs_0=function(){return(P_=t._emscripten_bind_PeProjcs_getGeogcs_0=t.asm.Cb).apply(null,arguments)},y_=t._emscripten_bind_PeProjcs_getParameters_0=function(){return(y_=t._emscripten_bind_PeProjcs_getParameters_0=t.asm.Db).apply(null,arguments)},g_=t._emscripten_bind_PeProjcs_getUnit_0=function(){return(g_=t._emscripten_bind_PeProjcs_getUnit_0=t.asm.Eb).apply(null,arguments)},f_=t._emscripten_bind_PeProjcs_loadConstants_0=function(){return(f_=t._emscripten_bind_PeProjcs_loadConstants_0=t.asm.Fb).apply(null,arguments)},l_=t._emscripten_bind_PeProjcs_horizonGcsGenerate_0=function(){return(l_=t._emscripten_bind_PeProjcs_horizonGcsGenerate_0=t.asm.Gb).apply(null,arguments)},m_=t._emscripten_bind_PeProjcs_horizonPcsGenerate_0=function(){return(m_=t._emscripten_bind_PeProjcs_horizonPcsGenerate_0=t.asm.Hb).apply(null,arguments)},d_=t._emscripten_bind_PeProjcs_getCode_0=function(){return(d_=t._emscripten_bind_PeProjcs_getCode_0=t.asm.Ib).apply(null,arguments)},E_=t._emscripten_bind_PeProjcs_getName_1=function(){return(E_=t._emscripten_bind_PeProjcs_getName_1=t.asm.Jb).apply(null,arguments)},b_=t._emscripten_bind_PeProjcs_getType_0=function(){return(b_=t._emscripten_bind_PeProjcs_getType_0=t.asm.Kb).apply(null,arguments)},O_=t._emscripten_bind_PeSpheroid_getAxis_0=function(){return(O_=t._emscripten_bind_PeSpheroid_getAxis_0=t.asm.Lb).apply(null,arguments)},T_=t._emscripten_bind_PeSpheroid_getFlattening_0=function(){return(T_=t._emscripten_bind_PeSpheroid_getFlattening_0=t.asm.Mb).apply(null,arguments)},S_=t._emscripten_bind_PeSpheroid_getCode_0=function(){return(S_=t._emscripten_bind_PeSpheroid_getCode_0=t.asm.Nb).apply(null,arguments)},N_=t._emscripten_bind_PeSpheroid_getName_1=function(){return(N_=t._emscripten_bind_PeSpheroid_getName_1=t.asm.Ob).apply(null,arguments)},h_=t._emscripten_bind_PeSpheroid_getType_0=function(){return(h_=t._emscripten_bind_PeSpheroid_getType_0=t.asm.Pb).apply(null,arguments)},M_=t._emscripten_bind_PeUnit_getUnitFactor_0=function(){return(M_=t._emscripten_bind_PeUnit_getUnitFactor_0=t.asm.Qb).apply(null,arguments)},v_=t._emscripten_bind_PeUnit_getCode_0=function(){return(v_=t._emscripten_bind_PeUnit_getCode_0=t.asm.Rb).apply(null,arguments)},D_=t._emscripten_bind_PeUnit_getName_1=function(){return(D_=t._emscripten_bind_PeUnit_getName_1=t.asm.Sb).apply(null,arguments)},R_=t._emscripten_bind_PeUnit_getType_0=function(){return(R_=t._emscripten_bind_PeUnit_getType_0=t.asm.Tb).apply(null,arguments)},A_=t._emscripten_bind_PeVersion_version_string_0=function(){return(A_=t._emscripten_bind_PeVersion_version_string_0=t.asm.Ub).apply(null,arguments)};t._pe_getPeGTlistExtendedEntrySize=function(){return(t._pe_getPeGTlistExtendedEntrySize=t.asm.Vb).apply(null,arguments)},t._pe_getPeGTlistExtendedGTsSize=function(){return(t._pe_getPeGTlistExtendedGTsSize=t.asm.Wb).apply(null,arguments)},t._pe_getPeHorizonSize=function(){return(t._pe_getPeHorizonSize=t.asm.Xb).apply(null,arguments)},t._pe_geog_to_geog=function(){return(t._pe_geog_to_geog=t.asm.Zb).apply(null,arguments)},t._pe_geog_to_proj=function(){return(t._pe_geog_to_proj=t.asm._b).apply(null,arguments)},t._pe_geog_to_dd=function(){return(t._pe_geog_to_dd=t.asm.$b).apply(null,arguments)},t._pe_dd_to_geog=function(){return(t._pe_dd_to_geog=t.asm.ac).apply(null,arguments)},t._pe_geog_to_ddm=function(){return(t._pe_geog_to_ddm=t.asm.bc).apply(null,arguments)},t._pe_ddm_to_geog=function(){return(t._pe_ddm_to_geog=t.asm.cc).apply(null,arguments)},t._pe_geog_to_dms=function(){return(t._pe_geog_to_dms=t.asm.dc).apply(null,arguments)},t._pe_dms_to_geog=function(){return(t._pe_dms_to_geog=t.asm.ec).apply(null,arguments)},t._pe_geog_to_mgrs_extended=function(){return(t._pe_geog_to_mgrs_extended=t.asm.fc).apply(null,arguments)},t._pe_mgrs_to_geog_extended=function(){return(t._pe_mgrs_to_geog_extended=t.asm.gc).apply(null,arguments)},t._pe_geog_to_usng=function(){return(t._pe_geog_to_usng=t.asm.hc).apply(null,arguments)},t._pe_usng_to_geog=function(){return(t._pe_usng_to_geog=t.asm.ic).apply(null,arguments)},t._pe_geog_to_utm=function(){return(t._pe_geog_to_utm=t.asm.jc).apply(null,arguments)},t._pe_utm_to_geog=function(){return(t._pe_utm_to_geog=t.asm.kc).apply(null,arguments)},t._pe_object_to_string_ext=function(){return(t._pe_object_to_string_ext=t.asm.lc).apply(null,arguments)},t._pe_proj_to_geog_center=function(){return(t._pe_proj_to_geog_center=t.asm.mc).apply(null,arguments)};var G_,C_=t._malloc=function(){return(C_=t._malloc=t.asm.nc).apply(null,arguments)};function I_(_){function n(){G_||(G_=!0,t.calledRun=!0,E||(q(U),e(t),t.onRuntimeInitialized&&t.onRuntimeInitialized(),function(){if(t.postRun)for("function"==typeof t.postRun&&(t.postRun=[t.postRun]);t.postRun.length;)F(t.postRun.shift());q(Y)}()))}H>0||(function(){if(t.preRun)for("function"==typeof t.preRun&&(t.preRun=[t.preRun]);t.preRun.length;)w(t.preRun.shift());q(L)}(),H>0||(t.setStatus?(t.setStatus("Running..."),setTimeout((function(){setTimeout((function(){t.setStatus("")}),1),n()}),1)):n()))}if(t._free=function(){return(t._free=t.asm.oc).apply(null,arguments)},t.___start_em_js=1970140,t.___stop_em_js=1970238,t.UTF8ToString=G,t.getValue=function(t,e="i8"){switch(e.endsWith("*")&&(e="*"),e){case"i1":case"i8":return T[t>>0];case"i16":return N[t>>1];case"i32":case"i64":return h[t>>2];case"float":return v[t>>2];case"double":return D[t>>3];case"*":return M[t>>2];default:z("invalid type for getValue: "+e)}return null},X=function t(){G_||I_(),G_||(X=t)},t.preInit)for("function"==typeof t.preInit&&(t.preInit=[t.preInit]);t.preInit.length>0;)t.preInit.pop()();function j_(){}function L_(t){return(t||j_).__cache__}function U_(t,e){var _=L_(e),n=_[t];return n||((n=Object.create((e||j_).prototype)).ptr=t,_[t]=n)}I_(),j_.prototype=Object.create(j_.prototype),j_.prototype.constructor=j_,j_.prototype.__class__=j_,j_.__cache__={},t.WrapperObject=j_,t.getCache=L_,t.wrapPointer=U_,t.castObject=function(t,e){return U_(t.ptr,e)},t.NULL=U_(0),t.destroy=function(t){if(!t.__destroy__)throw"Error: Cannot destroy object. (Did you create it yourself?)";t.__destroy__(),delete L_(t.__class__)[t.ptr]},t.compare=function(t,e){return t.ptr===e.ptr},t.getPointer=function(t){return t.ptr},t.getClass=function(t){return t.__class__};var Y_={buffer:0,size:0,pos:0,temps:[],needed:0,prepare:function(){if(Y_.needed){for(var e=0;e<Y_.temps.length;e++)t._free(Y_.temps[e]);Y_.temps.length=0,t._free(Y_.buffer),Y_.buffer=0,Y_.size+=Y_.needed,Y_.needed=0}Y_.buffer||(Y_.size+=128,Y_.buffer=t._malloc(Y_.size),b(Y_.buffer)),Y_.pos=0},alloc:function(e,_){b(Y_.buffer);var n,r=_.BYTES_PER_ELEMENT,o=e.length*r;return o=o+7&-8,Y_.pos+o>=Y_.size?(b(o>0),Y_.needed+=o,n=t._malloc(o),Y_.temps.push(n)):(n=Y_.buffer+Y_.pos,Y_.pos+=o),n},copy:function(t,e,_){switch(_>>>=0,e.BYTES_PER_ELEMENT){case 2:_>>>=1;break;case 4:_>>>=2;break;case 8:_>>>=3}for(var n=0;n<t.length;n++)e[_+n]=t[n]}};function w_(t){if("string"==typeof t){var e=function(t,e,_){var n=I(t)+1,r=new Array(n);C(t,r,0,r.length);return r}(t),_=Y_.alloc(e,T);return Y_.copy(e,T,_),_}return t}function F_(t){if("object"==typeof t){var e=Y_.alloc(t,T);return Y_.copy(t,T,e),e}return t}function x_(){throw"cannot construct a PeObject, no constructor in IDL"}function H_(){throw"cannot construct a PeCoordsys, no constructor in IDL"}function X_(){throw"cannot construct a VoidPtr, no constructor in IDL"}function z_(){throw"cannot construct a PeDatum, no constructor in IDL"}function Z_(){throw"cannot construct a PeDefs, no constructor in IDL"}function B_(){throw"cannot construct a PeFactory, no constructor in IDL"}function W_(t,e,_,n,r,o){t&&"object"==typeof t&&(t=t.ptr),e&&"object"==typeof e&&(e=e.ptr),_&&"object"==typeof _&&(_=_.ptr),n&&"object"==typeof n&&(n=n.ptr),r&&"object"==typeof r&&(r=r.ptr),o&&"object"==typeof o&&(o=o.ptr),this.ptr=Jt(t,e,_,n,r,o),L_(W_)[this.ptr]=this}function k_(){throw"cannot construct a PeGeogcs, no constructor in IDL"}function V_(){throw"cannot construct a PeGeogtran, no constructor in IDL"}function q_(){throw"cannot construct a PeGTlistExtended, no constructor in IDL"}function J_(){throw"cannot construct a PeGTlistExtendedEntry, no constructor in IDL"}function K_(){throw"cannot construct a PeGTlistExtendedGTs, no constructor in IDL"}function $_(){throw"cannot construct a PeHorizon, no constructor in IDL"}function Q_(t){t&&"object"==typeof t&&(t=t.ptr),this.ptr=De(t),L_(Q_)[this.ptr]=this}function tn(){throw"cannot construct a PeNotationMgrs, no constructor in IDL"}function en(){throw"cannot construct a PeNotationUtm, no constructor in IDL"}function _n(){throw"cannot construct a PeParameter, no constructor in IDL"}function nn(){throw"cannot construct a PePCSInfo, no constructor in IDL"}function rn(){throw"cannot construct a PePrimem, no constructor in IDL"}function on(){throw"cannot construct a PeProjcs, no constructor in IDL"}function pn(){throw"cannot construct a PeSpheroid, no constructor in IDL"}function cn(){throw"cannot construct a PeUnit, no constructor in IDL"}function sn(){throw"cannot construct a PeVersion, no constructor in IDL"}return x_.prototype=Object.create(j_.prototype),x_.prototype.constructor=x_,x_.prototype.__class__=x_,x_.__cache__={},t.PeObject=x_,x_.prototype.getCode=x_.prototype.getCode=function(){var t=this.ptr;return ot(t)},x_.prototype.getName=x_.prototype.getName=function(t){var e=this.ptr;return Y_.prepare(),"object"==typeof t&&(t=F_(t)),G(pt(e,t))},x_.prototype.getType=x_.prototype.getType=function(){var t=this.ptr;return it(t)},H_.prototype=Object.create(x_.prototype),H_.prototype.constructor=H_,H_.prototype.__class__=H_,H_.__cache__={},t.PeCoordsys=H_,H_.prototype.getCode=H_.prototype.getCode=function(){var t=this.ptr;return ct(t)},H_.prototype.getName=H_.prototype.getName=function(t){var e=this.ptr;return Y_.prepare(),"object"==typeof t&&(t=F_(t)),G(st(e,t))},H_.prototype.getType=H_.prototype.getType=function(){var t=this.ptr;return at(t)},X_.prototype=Object.create(j_.prototype),X_.prototype.constructor=X_,X_.prototype.__class__=X_,X_.__cache__={},t.VoidPtr=X_,X_.prototype.__destroy__=X_.prototype.__destroy__=function(){var t=this.ptr;ut(t)},z_.prototype=Object.create(x_.prototype),z_.prototype.constructor=z_,z_.prototype.__class__=z_,z_.__cache__={},t.PeDatum=z_,z_.prototype.getSpheroid=z_.prototype.getSpheroid=function(){var t=this.ptr;return U_(Pt(t),pn)},z_.prototype.getCode=z_.prototype.getCode=function(){var t=this.ptr;return yt(t)},z_.prototype.getName=z_.prototype.getName=function(t){var e=this.ptr;return Y_.prepare(),"object"==typeof t&&(t=F_(t)),G(gt(e,t))},z_.prototype.getType=z_.prototype.getType=function(){var t=this.ptr;return ft(t)},Z_.prototype=Object.create(j_.prototype),Z_.prototype.constructor=Z_,Z_.prototype.__class__=Z_,Z_.__cache__={},t.PeDefs=Z_,Z_.prototype.get_PE_BUFFER_MAX=Z_.prototype.get_PE_BUFFER_MAX=function(){var t=this.ptr;return lt(t)},Object.defineProperty(Z_.prototype,"PE_BUFFER_MAX",{get:Z_.prototype.get_PE_BUFFER_MAX}),Z_.prototype.get_PE_NAME_MAX=Z_.prototype.get_PE_NAME_MAX=function(){var t=this.ptr;return mt(t)},Object.defineProperty(Z_.prototype,"PE_NAME_MAX",{get:Z_.prototype.get_PE_NAME_MAX}),Z_.prototype.get_PE_MGRS_MAX=Z_.prototype.get_PE_MGRS_MAX=function(){var t=this.ptr;return dt(t)},Object.defineProperty(Z_.prototype,"PE_MGRS_MAX",{get:Z_.prototype.get_PE_MGRS_MAX}),Z_.prototype.get_PE_USNG_MAX=Z_.prototype.get_PE_USNG_MAX=function(){var t=this.ptr;return Et(t)},Object.defineProperty(Z_.prototype,"PE_USNG_MAX",{get:Z_.prototype.get_PE_USNG_MAX}),Z_.prototype.get_PE_DD_MAX=Z_.prototype.get_PE_DD_MAX=function(){var t=this.ptr;return bt(t)},Object.defineProperty(Z_.prototype,"PE_DD_MAX",{get:Z_.prototype.get_PE_DD_MAX}),Z_.prototype.get_PE_DMS_MAX=Z_.prototype.get_PE_DMS_MAX=function(){var t=this.ptr;return Ot(t)},Object.defineProperty(Z_.prototype,"PE_DMS_MAX",{get:Z_.prototype.get_PE_DMS_MAX}),Z_.prototype.get_PE_DDM_MAX=Z_.prototype.get_PE_DDM_MAX=function(){var t=this.ptr;return Tt(t)},Object.defineProperty(Z_.prototype,"PE_DDM_MAX",{get:Z_.prototype.get_PE_DDM_MAX}),Z_.prototype.get_PE_UTM_MAX=Z_.prototype.get_PE_UTM_MAX=function(){var t=this.ptr;return St(t)},Object.defineProperty(Z_.prototype,"PE_UTM_MAX",{get:Z_.prototype.get_PE_UTM_MAX}),Z_.prototype.get_PE_PARM_MAX=Z_.prototype.get_PE_PARM_MAX=function(){var t=this.ptr;return Nt(t)},Object.defineProperty(Z_.prototype,"PE_PARM_MAX",{get:Z_.prototype.get_PE_PARM_MAX}),Z_.prototype.get_PE_TYPE_NONE=Z_.prototype.get_PE_TYPE_NONE=function(){var t=this.ptr;return ht(t)},Object.defineProperty(Z_.prototype,"PE_TYPE_NONE",{get:Z_.prototype.get_PE_TYPE_NONE}),Z_.prototype.get_PE_TYPE_GEOGCS=Z_.prototype.get_PE_TYPE_GEOGCS=function(){var t=this.ptr;return Mt(t)},Object.defineProperty(Z_.prototype,"PE_TYPE_GEOGCS",{get:Z_.prototype.get_PE_TYPE_GEOGCS}),Z_.prototype.get_PE_TYPE_PROJCS=Z_.prototype.get_PE_TYPE_PROJCS=function(){var t=this.ptr;return vt(t)},Object.defineProperty(Z_.prototype,"PE_TYPE_PROJCS",{get:Z_.prototype.get_PE_TYPE_PROJCS}),Z_.prototype.get_PE_TYPE_GEOGTRAN=Z_.prototype.get_PE_TYPE_GEOGTRAN=function(){var t=this.ptr;return Dt(t)},Object.defineProperty(Z_.prototype,"PE_TYPE_GEOGTRAN",{get:Z_.prototype.get_PE_TYPE_GEOGTRAN}),Z_.prototype.get_PE_TYPE_COORDSYS=Z_.prototype.get_PE_TYPE_COORDSYS=function(){var t=this.ptr;return Rt(t)},Object.defineProperty(Z_.prototype,"PE_TYPE_COORDSYS",{get:Z_.prototype.get_PE_TYPE_COORDSYS}),Z_.prototype.get_PE_TYPE_UNIT=Z_.prototype.get_PE_TYPE_UNIT=function(){var t=this.ptr;return At(t)},Object.defineProperty(Z_.prototype,"PE_TYPE_UNIT",{get:Z_.prototype.get_PE_TYPE_UNIT}),Z_.prototype.get_PE_TYPE_LINUNIT=Z_.prototype.get_PE_TYPE_LINUNIT=function(){var t=this.ptr;return Gt(t)},Object.defineProperty(Z_.prototype,"PE_TYPE_LINUNIT",{get:Z_.prototype.get_PE_TYPE_LINUNIT}),Z_.prototype.get_PE_STR_OPTS_NONE=Z_.prototype.get_PE_STR_OPTS_NONE=function(){var t=this.ptr;return Ct(t)},Object.defineProperty(Z_.prototype,"PE_STR_OPTS_NONE",{get:Z_.prototype.get_PE_STR_OPTS_NONE}),Z_.prototype.get_PE_STR_AUTH_NONE=Z_.prototype.get_PE_STR_AUTH_NONE=function(){var t=this.ptr;return It(t)},Object.defineProperty(Z_.prototype,"PE_STR_AUTH_NONE",{get:Z_.prototype.get_PE_STR_AUTH_NONE}),Z_.prototype.get_PE_STR_AUTH_TOP=Z_.prototype.get_PE_STR_AUTH_TOP=function(){var t=this.ptr;return jt(t)},Object.defineProperty(Z_.prototype,"PE_STR_AUTH_TOP",{get:Z_.prototype.get_PE_STR_AUTH_TOP}),Z_.prototype.get_PE_STR_NAME_CANON=Z_.prototype.get_PE_STR_NAME_CANON=function(){var t=this.ptr;return Lt(t)},Object.defineProperty(Z_.prototype,"PE_STR_NAME_CANON",{get:Z_.prototype.get_PE_STR_NAME_CANON}),Z_.prototype.get_PE_PARM_X0=Z_.prototype.get_PE_PARM_X0=function(){var t=this.ptr;return Ut(t)},Object.defineProperty(Z_.prototype,"PE_PARM_X0",{get:Z_.prototype.get_PE_PARM_X0}),Z_.prototype.get_PE_PARM_ND=Z_.prototype.get_PE_PARM_ND=function(){var t=this.ptr;return Yt(t)},Object.defineProperty(Z_.prototype,"PE_PARM_ND",{get:Z_.prototype.get_PE_PARM_ND}),Z_.prototype.get_PE_TRANSFORM_1_TO_2=Z_.prototype.get_PE_TRANSFORM_1_TO_2=function(){var t=this.ptr;return wt(t)},Object.defineProperty(Z_.prototype,"PE_TRANSFORM_1_TO_2",{get:Z_.prototype.get_PE_TRANSFORM_1_TO_2}),Z_.prototype.get_PE_TRANSFORM_2_TO_1=Z_.prototype.get_PE_TRANSFORM_2_TO_1=function(){var t=this.ptr;return Ft(t)},Object.defineProperty(Z_.prototype,"PE_TRANSFORM_2_TO_1",{get:Z_.prototype.get_PE_TRANSFORM_2_TO_1}),Z_.prototype.get_PE_TRANSFORM_P_TO_G=Z_.prototype.get_PE_TRANSFORM_P_TO_G=function(){var t=this.ptr;return xt(t)},Object.defineProperty(Z_.prototype,"PE_TRANSFORM_P_TO_G",{get:Z_.prototype.get_PE_TRANSFORM_P_TO_G}),Z_.prototype.get_PE_TRANSFORM_G_TO_P=Z_.prototype.get_PE_TRANSFORM_G_TO_P=function(){var t=this.ptr;return Ht(t)},Object.defineProperty(Z_.prototype,"PE_TRANSFORM_G_TO_P",{get:Z_.prototype.get_PE_TRANSFORM_G_TO_P}),Z_.prototype.get_PE_HORIZON_RECT=Z_.prototype.get_PE_HORIZON_RECT=function(){var t=this.ptr;return Xt(t)},Object.defineProperty(Z_.prototype,"PE_HORIZON_RECT",{get:Z_.prototype.get_PE_HORIZON_RECT}),Z_.prototype.get_PE_HORIZON_POLY=Z_.prototype.get_PE_HORIZON_POLY=function(){var t=this.ptr;return zt(t)},Object.defineProperty(Z_.prototype,"PE_HORIZON_POLY",{get:Z_.prototype.get_PE_HORIZON_POLY}),Z_.prototype.get_PE_HORIZON_LINE=Z_.prototype.get_PE_HORIZON_LINE=function(){var t=this.ptr;return Zt(t)},Object.defineProperty(Z_.prototype,"PE_HORIZON_LINE",{get:Z_.prototype.get_PE_HORIZON_LINE}),Z_.prototype.get_PE_HORIZON_DELTA=Z_.prototype.get_PE_HORIZON_DELTA=function(){var t=this.ptr;return Bt(t)},Object.defineProperty(Z_.prototype,"PE_HORIZON_DELTA",{get:Z_.prototype.get_PE_HORIZON_DELTA}),B_.prototype=Object.create(j_.prototype),B_.prototype.constructor=B_,B_.prototype.__class__=B_,B_.__cache__={},t.PeFactory=B_,B_.prototype.initialize=B_.prototype.initialize=function(t){var e=this.ptr;Y_.prepare(),t=t&&"object"==typeof t?t.ptr:w_(t),Wt(e,t)},B_.prototype.factoryByType=B_.prototype.factoryByType=function(t,e){var _=this.ptr;return t&&"object"==typeof t&&(t=t.ptr),e&&"object"==typeof e&&(e=e.ptr),U_(kt(_,t,e),x_)},B_.prototype.fromString=B_.prototype.fromString=function(t,e){var _=this.ptr;return Y_.prepare(),t&&"object"==typeof t&&(t=t.ptr),e=e&&"object"==typeof e?e.ptr:w_(e),U_(Vt(_,t,e),x_)},B_.prototype.getCode=B_.prototype.getCode=function(t){var e=this.ptr;return t&&"object"==typeof t&&(t=t.ptr),qt(e,t)},W_.prototype=Object.create(j_.prototype),W_.prototype.constructor=W_,W_.prototype.__class__=W_,W_.__cache__={},t.PeGCSExtent=W_,W_.prototype.getLLon=W_.prototype.getLLon=function(){var t=this.ptr;return Kt(t)},W_.prototype.getSLat=W_.prototype.getSLat=function(){var t=this.ptr;return $t(t)},W_.prototype.getRLon=W_.prototype.getRLon=function(){var t=this.ptr;return Qt(t)},W_.prototype.getNLat=W_.prototype.getNLat=function(){var t=this.ptr;return te(t)},W_.prototype.__destroy__=W_.prototype.__destroy__=function(){var t=this.ptr;ee(t)},k_.prototype=Object.create(H_.prototype),k_.prototype.constructor=k_,k_.prototype.__class__=k_,k_.__cache__={},t.PeGeogcs=k_,k_.prototype.getDatum=k_.prototype.getDatum=function(){var t=this.ptr;return U_(_e(t),z_)},k_.prototype.getPrimem=k_.prototype.getPrimem=function(){var t=this.ptr;return U_(ne(t),rn)},k_.prototype.getUnit=k_.prototype.getUnit=function(){var t=this.ptr;return U_(re(t),cn)},k_.prototype.getCode=k_.prototype.getCode=function(){var t=this.ptr;return oe(t)},k_.prototype.getName=k_.prototype.getName=function(t){var e=this.ptr;return Y_.prepare(),"object"==typeof t&&(t=F_(t)),G(pe(e,t))},k_.prototype.getType=k_.prototype.getType=function(){var t=this.ptr;return ie(t)},V_.prototype=Object.create(x_.prototype),V_.prototype.constructor=V_,V_.prototype.__class__=V_,V_.__cache__={},t.PeGeogtran=V_,V_.prototype.isEqual=V_.prototype.isEqual=function(t){var e=this.ptr;return t&&"object"==typeof t&&(t=t.ptr),!!ce(e,t)},V_.prototype.getGeogcs1=V_.prototype.getGeogcs1=function(){var t=this.ptr;return U_(se(t),k_)},V_.prototype.getGeogcs2=V_.prototype.getGeogcs2=function(){var t=this.ptr;return U_(ae(t),k_)},V_.prototype.getParameters=V_.prototype.getParameters=function(){var t=this.ptr;return ue(t)},V_.prototype.loadConstants=V_.prototype.loadConstants=function(){var t=this.ptr;return!!Pe(t)},V_.prototype.getCode=V_.prototype.getCode=function(){var t=this.ptr;return ye(t)},V_.prototype.getName=V_.prototype.getName=function(t){var e=this.ptr;return Y_.prepare(),"object"==typeof t&&(t=F_(t)),G(ge(e,t))},V_.prototype.getType=V_.prototype.getType=function(){var t=this.ptr;return fe(t)},q_.prototype=Object.create(j_.prototype),q_.prototype.constructor=q_,q_.prototype.__class__=q_,q_.__cache__={},t.PeGTlistExtended=q_,q_.prototype.getGTlist=q_.prototype.getGTlist=function(t,e,_,n,r,o){var p=this.ptr;return t&&"object"==typeof t&&(t=t.ptr),e&&"object"==typeof e&&(e=e.ptr),_&&"object"==typeof _&&(_=_.ptr),n&&"object"==typeof n&&(n=n.ptr),r&&"object"==typeof r&&(r=r.ptr),o&&"object"==typeof o&&(o=o.ptr),U_(le(p,t,e,_,n,r,o),J_)},q_.prototype.get_PE_GTLIST_OPTS_COMMON=q_.prototype.get_PE_GTLIST_OPTS_COMMON=function(){var t=this.ptr;return me(t)},Object.defineProperty(q_.prototype,"PE_GTLIST_OPTS_COMMON",{get:q_.prototype.get_PE_GTLIST_OPTS_COMMON}),J_.prototype=Object.create(j_.prototype),J_.prototype.constructor=J_,J_.prototype.__class__=J_,J_.__cache__={},t.PeGTlistExtendedEntry=J_,J_.prototype.getEntries=J_.prototype.getEntries=function(){var t=this.ptr;return U_(de(t),K_)},J_.prototype.getSteps=J_.prototype.getSteps=function(){var t=this.ptr;return Ee(t)},J_.prototype.Delete=J_.prototype.Delete=function(t){var e=this.ptr;t&&"object"==typeof t&&(t=t.ptr),be(e,t)},K_.prototype=Object.create(j_.prototype),K_.prototype.constructor=K_,K_.prototype.__class__=K_,K_.__cache__={},t.PeGTlistExtendedGTs=K_,K_.prototype.getDirection=K_.prototype.getDirection=function(){var t=this.ptr;return Oe(t)},K_.prototype.getGeogtran=K_.prototype.getGeogtran=function(){var t=this.ptr;return U_(Te(t),V_)},$_.prototype=Object.create(j_.prototype),$_.prototype.constructor=$_,$_.prototype.__class__=$_,$_.__cache__={},t.PeHorizon=$_,$_.prototype.getNump=$_.prototype.getNump=function(){var t=this.ptr;return Se(t)},$_.prototype.getKind=$_.prototype.getKind=function(){var t=this.ptr;return Ne(t)},$_.prototype.getInclusive=$_.prototype.getInclusive=function(){var t=this.ptr;return he(t)},$_.prototype.getSize=$_.prototype.getSize=function(){var t=this.ptr;return Me(t)},$_.prototype.getCoord=$_.prototype.getCoord=function(){var t=this.ptr;return ve(t)},Q_.prototype=Object.create(j_.prototype),Q_.prototype.constructor=Q_,Q_.prototype.__class__=Q_,Q_.__cache__={},t.PeInteger=Q_,Q_.prototype.get_val=Q_.prototype.get_val=function(){var t=this.ptr;return Re(t)},Q_.prototype.set_val=Q_.prototype.set_val=function(t){var e=this.ptr;t&&"object"==typeof t&&(t=t.ptr),Ae(e,t)},Object.defineProperty(Q_.prototype,"val",{get:Q_.prototype.get_val,set:Q_.prototype.set_val}),Q_.prototype.__destroy__=Q_.prototype.__destroy__=function(){var t=this.ptr;Ge(t)},tn.prototype=Object.create(j_.prototype),tn.prototype.constructor=tn,tn.prototype.__class__=tn,tn.__cache__={},t.PeNotationMgrs=tn,tn.prototype.get_PE_MGRS_STYLE_NEW=tn.prototype.get_PE_MGRS_STYLE_NEW=function(){var t=this.ptr;return Ce(t)},Object.defineProperty(tn.prototype,"PE_MGRS_STYLE_NEW",{get:tn.prototype.get_PE_MGRS_STYLE_NEW}),tn.prototype.get_PE_MGRS_STYLE_OLD=tn.prototype.get_PE_MGRS_STYLE_OLD=function(){var t=this.ptr;return Ie(t)},Object.defineProperty(tn.prototype,"PE_MGRS_STYLE_OLD",{get:tn.prototype.get_PE_MGRS_STYLE_OLD}),tn.prototype.get_PE_MGRS_STYLE_AUTO=tn.prototype.get_PE_MGRS_STYLE_AUTO=function(){var t=this.ptr;return je(t)},Object.defineProperty(tn.prototype,"PE_MGRS_STYLE_AUTO",{get:tn.prototype.get_PE_MGRS_STYLE_AUTO}),tn.prototype.get_PE_MGRS_180_ZONE_1_PLUS=tn.prototype.get_PE_MGRS_180_ZONE_1_PLUS=function(){var t=this.ptr;return Le(t)},Object.defineProperty(tn.prototype,"PE_MGRS_180_ZONE_1_PLUS",{get:tn.prototype.get_PE_MGRS_180_ZONE_1_PLUS}),tn.prototype.get_PE_MGRS_ADD_SPACES=tn.prototype.get_PE_MGRS_ADD_SPACES=function(){var t=this.ptr;return Ue(t)},Object.defineProperty(tn.prototype,"PE_MGRS_ADD_SPACES",{get:tn.prototype.get_PE_MGRS_ADD_SPACES}),en.prototype=Object.create(j_.prototype),en.prototype.constructor=en,en.prototype.__class__=en,en.__cache__={},t.PeNotationUtm=en,en.prototype.get_PE_UTM_OPTS_NONE=en.prototype.get_PE_UTM_OPTS_NONE=function(){var t=this.ptr;return Ye(t)},Object.defineProperty(en.prototype,"PE_UTM_OPTS_NONE",{get:en.prototype.get_PE_UTM_OPTS_NONE}),en.prototype.get_PE_UTM_OPTS_NS=en.prototype.get_PE_UTM_OPTS_NS=function(){var t=this.ptr;return we(t)},Object.defineProperty(en.prototype,"PE_UTM_OPTS_NS",{get:en.prototype.get_PE_UTM_OPTS_NS}),en.prototype.get_PE_UTM_OPTS_NS_STRICT=en.prototype.get_PE_UTM_OPTS_NS_STRICT=function(){var t=this.ptr;return Fe(t)},Object.defineProperty(en.prototype,"PE_UTM_OPTS_NS_STRICT",{get:en.prototype.get_PE_UTM_OPTS_NS_STRICT}),en.prototype.get_PE_UTM_OPTS_ADD_SPACES=en.prototype.get_PE_UTM_OPTS_ADD_SPACES=function(){var t=this.ptr;return xe(t)},Object.defineProperty(en.prototype,"PE_UTM_OPTS_ADD_SPACES",{get:en.prototype.get_PE_UTM_OPTS_ADD_SPACES}),_n.prototype=Object.create(x_.prototype),_n.prototype.constructor=_n,_n.prototype.__class__=_n,_n.__cache__={},t.PeParameter=_n,_n.prototype.getValue=_n.prototype.getValue=function(){var t=this.ptr;return He(t)},_n.prototype.getCode=_n.prototype.getCode=function(){var t=this.ptr;return Xe(t)},_n.prototype.getName=_n.prototype.getName=function(t){var e=this.ptr;return Y_.prepare(),"object"==typeof t&&(t=F_(t)),G(ze(e,t))},_n.prototype.getType=_n.prototype.getType=function(){var t=this.ptr;return Ze(t)},nn.prototype=Object.create(j_.prototype),nn.prototype.constructor=nn,nn.prototype.__class__=nn,nn.__cache__={},t.PePCSInfo=nn,nn.prototype.getCentralMeridian=nn.prototype.getCentralMeridian=function(){var t=this.ptr;return Be(t)},nn.prototype.getDomainMinx=nn.prototype.getDomainMinx=function(){var t=this.ptr;return We(t)},nn.prototype.getDomainMiny=nn.prototype.getDomainMiny=function(){var t=this.ptr;return ke(t)},nn.prototype.getDomainMaxx=nn.prototype.getDomainMaxx=function(){var t=this.ptr;return Ve(t)},nn.prototype.getDomainMaxy=nn.prototype.getDomainMaxy=function(){var t=this.ptr;return qe(t)},nn.prototype.getNorthPoleLocation=nn.prototype.getNorthPoleLocation=function(){var t=this.ptr;return Je(t)},nn.prototype.getNorthPoleGeometry=nn.prototype.getNorthPoleGeometry=function(){var t=this.ptr;return Ke(t)},nn.prototype.getSouthPoleLocation=nn.prototype.getSouthPoleLocation=function(){var t=this.ptr;return $e(t)},nn.prototype.getSouthPoleGeometry=nn.prototype.getSouthPoleGeometry=function(){var t=this.ptr;return Qe(t)},nn.prototype.isDensificationNeeded=nn.prototype.isDensificationNeeded=function(){var t=this.ptr;return!!t_(t)},nn.prototype.isGcsHorizonMultiOverlap=nn.prototype.isGcsHorizonMultiOverlap=function(){var t=this.ptr;return!!e_(t)},nn.prototype.isPannableRectangle=nn.prototype.isPannableRectangle=function(){var t=this.ptr;return!!__(t)},nn.prototype.generate=nn.prototype.generate=function(t,e){var _=this.ptr;return t&&"object"==typeof t&&(t=t.ptr),e&&"object"==typeof e&&(e=e.ptr),U_(n_(_,t,e),nn)},nn.prototype.get_PE_PCSINFO_OPTION_NONE=nn.prototype.get_PE_PCSINFO_OPTION_NONE=function(){var t=this.ptr;return r_(t)},Object.defineProperty(nn.prototype,"PE_PCSINFO_OPTION_NONE",{get:nn.prototype.get_PE_PCSINFO_OPTION_NONE}),nn.prototype.get_PE_PCSINFO_OPTION_DOMAIN=nn.prototype.get_PE_PCSINFO_OPTION_DOMAIN=function(){var t=this.ptr;return o_(t)},Object.defineProperty(nn.prototype,"PE_PCSINFO_OPTION_DOMAIN",{get:nn.prototype.get_PE_PCSINFO_OPTION_DOMAIN}),nn.prototype.get_PE_POLE_OUTSIDE_BOUNDARY=nn.prototype.get_PE_POLE_OUTSIDE_BOUNDARY=function(){var t=this.ptr;return p_(t)},Object.defineProperty(nn.prototype,"PE_POLE_OUTSIDE_BOUNDARY",{get:nn.prototype.get_PE_POLE_OUTSIDE_BOUNDARY}),nn.prototype.get_PE_POLE_POINT=nn.prototype.get_PE_POLE_POINT=function(){var t=this.ptr;return i_(t)},Object.defineProperty(nn.prototype,"PE_POLE_POINT",{get:nn.prototype.get_PE_POLE_POINT}),rn.prototype=Object.create(x_.prototype),rn.prototype.constructor=rn,rn.prototype.__class__=rn,rn.__cache__={},t.PePrimem=rn,rn.prototype.getLongitude=rn.prototype.getLongitude=function(){var t=this.ptr;return c_(t)},rn.prototype.getCode=rn.prototype.getCode=function(){var t=this.ptr;return s_(t)},rn.prototype.getName=rn.prototype.getName=function(t){var e=this.ptr;return Y_.prepare(),"object"==typeof t&&(t=F_(t)),G(a_(e,t))},rn.prototype.getType=rn.prototype.getType=function(){var t=this.ptr;return u_(t)},on.prototype=Object.create(H_.prototype),on.prototype.constructor=on,on.prototype.__class__=on,on.__cache__={},t.PeProjcs=on,on.prototype.getGeogcs=on.prototype.getGeogcs=function(){var t=this.ptr;return U_(P_(t),k_)},on.prototype.getParameters=on.prototype.getParameters=function(){var t=this.ptr;return y_(t)},on.prototype.getUnit=on.prototype.getUnit=function(){var t=this.ptr;return U_(g_(t),cn)},on.prototype.loadConstants=on.prototype.loadConstants=function(){var t=this.ptr;return!!f_(t)},on.prototype.horizonGcsGenerate=on.prototype.horizonGcsGenerate=function(){var t=this.ptr;return U_(l_(t),$_)},on.prototype.horizonPcsGenerate=on.prototype.horizonPcsGenerate=function(){var t=this.ptr;return U_(m_(t),$_)},on.prototype.getCode=on.prototype.getCode=function(){var t=this.ptr;return d_(t)},on.prototype.getName=on.prototype.getName=function(t){var e=this.ptr;return Y_.prepare(),"object"==typeof t&&(t=F_(t)),G(E_(e,t))},on.prototype.getType=on.prototype.getType=function(){var t=this.ptr;return b_(t)},pn.prototype=Object.create(x_.prototype),pn.prototype.constructor=pn,pn.prototype.__class__=pn,pn.__cache__={},t.PeSpheroid=pn,pn.prototype.getAxis=pn.prototype.getAxis=function(){var t=this.ptr;return O_(t)},pn.prototype.getFlattening=pn.prototype.getFlattening=function(){var t=this.ptr;return T_(t)},pn.prototype.getCode=pn.prototype.getCode=function(){var t=this.ptr;return S_(t)},pn.prototype.getName=pn.prototype.getName=function(t){var e=this.ptr;return Y_.prepare(),"object"==typeof t&&(t=F_(t)),G(N_(e,t))},pn.prototype.getType=pn.prototype.getType=function(){var t=this.ptr;return h_(t)},cn.prototype=Object.create(x_.prototype),cn.prototype.constructor=cn,cn.prototype.__class__=cn,cn.__cache__={},t.PeUnit=cn,cn.prototype.getUnitFactor=cn.prototype.getUnitFactor=function(){var t=this.ptr;return M_(t)},cn.prototype.getCode=cn.prototype.getCode=function(){var t=this.ptr;return v_(t)},cn.prototype.getName=cn.prototype.getName=function(t){var e=this.ptr;return Y_.prepare(),"object"==typeof t&&(t=F_(t)),G(D_(e,t))},cn.prototype.getType=cn.prototype.getType=function(){var t=this.ptr;return R_(t)},sn.prototype=Object.create(j_.prototype),sn.prototype.constructor=sn,sn.prototype.__class__=sn,sn.__cache__={},t.PeVersion=sn,sn.prototype.version_string=sn.prototype.version_string=function(){var t=this.ptr;return G(A_(t))},t.ensureCache=Y_,t.ensureString=w_,t.ensureInt8=F_,t.ensureInt16=function(t){if("object"==typeof t){var e=Y_.alloc(t,N);return Y_.copy(t,N,e),e}return t},t.ensureInt32=function(t){if("object"==typeof t){var e=Y_.alloc(t,h);return Y_.copy(t,h,e),e}return t},t.ensureFloat32=function(t){if("object"==typeof t){var e=Y_.alloc(t,v);return Y_.copy(t,v,e),e}return t},t.ensureFloat64=function(t){if("object"==typeof t){var e=Y_.alloc(t,D);return Y_.copy(t,D,e),e}return t},t.ready},n.exports=o;const i=function(t,e){for(var _=0;_<e.length;_++){const n=e[_];if("string"!=typeof n&&!Array.isArray(n))for(const e in n)if("default"!==e&&!(e in t)){const _=Object.getOwnPropertyDescriptor(n,e);_&&Object.defineProperty(t,e,_.get?_:{enumerable:!0,get:()=>n[e]})}}return Object.freeze(Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}))}({__proto__:null,default:p},[p])}}]);