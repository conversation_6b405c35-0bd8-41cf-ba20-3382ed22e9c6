package org.thingsboard.server.dao.model.sql.gis;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName(value = "tb_gis_project")
public class GisProject {

    private String id;

    private String name;

    private String geo;

    private String createDept;

    private String designDept;

    private String buildDept;

    private Date buildDate;

    private Date completeDate;

    private String mainMaterial;

    private String status;

    private String caliber;

    private BigDecimal length;

    private String file;

    private Date createTime;

    private String tenantId;



}
