package org.thingsboard.server.dao.model.DTO;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 增量漏失评估
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2023-04-23
 */
@Data
@NoArgsConstructor
public class PartitionNotNormalLossEvaluateDTO extends PartitionNormalLossEvaluateDTO {

    private BigDecimal unitPipeNightFlowMin;

    private BigDecimal avgDayFlow;

    private BigDecimal mnfDivideAvgDayFlow;

    private BigDecimal legalUseWater;

    private BigDecimal netNightFlow;

    private BigDecimal lossWater;

    private BigDecimal lossIndex;

    private String lossValuation;

}
