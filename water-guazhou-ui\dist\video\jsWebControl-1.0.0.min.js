var WebControl=function(){"use strict";function e(t){return(e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(t)}function t(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function n(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}function o(e,t,o){return t&&n(e.prototype,t),o&&n(e,o),e}function i(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=new Array(t);n<t;n++)o[n]=e[n];return o}var r="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function a(e,t,n){return e(n={path:t,exports:{},require:function(e,t){return function(){throw new Error("Dynamic requires are not currently supported by @rollup/plugin-commonjs")}(null==t&&n.path)}},n.exports),n.exports}var s=function(e){return e&&e.Math==Math&&e},u=s("object"==("undefined"==typeof globalThis?"undefined":e(globalThis))&&globalThis)||s("object"==("undefined"==typeof window?"undefined":e(window))&&window)||s("object"==("undefined"==typeof self?"undefined":e(self))&&self)||s("object"==e(r)&&r)||Function("return this")(),c=function(e){try{return!!e()}catch(e){return!0}},l=!c((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),d={}.propertyIsEnumerable,f=Object.getOwnPropertyDescriptor,h={f:f&&!d.call({1:2},1)?function(e){var t=f(this,e);return!!t&&t.enumerable}:d},p=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}},v={}.toString,b=function(e){return v.call(e).slice(8,-1)},m="".split,g=c((function(){return!Object("z").propertyIsEnumerable(0)}))?function(e){return"String"==b(e)?m.call(e,""):Object(e)}:Object,y=function(e){if(null==e)throw TypeError("Can't call method on "+e);return e},_=function(e){return g(y(e))},w=function(t){return"object"===e(t)?null!==t:"function"==typeof t},k=function(e,t){if(!w(e))return e;var n,o;if(t&&"function"==typeof(n=e.toString)&&!w(o=n.call(e)))return o;if("function"==typeof(n=e.valueOf)&&!w(o=n.call(e)))return o;if(!t&&"function"==typeof(n=e.toString)&&!w(o=n.call(e)))return o;throw TypeError("Can't convert object to primitive value")},C={}.hasOwnProperty,S=function(e,t){return C.call(e,t)},R=u.document,q=w(R)&&w(R.createElement),I=function(e){return q?R.createElement(e):{}},P=!l&&!c((function(){return 7!=Object.defineProperty(I("div"),"a",{get:function(){return 7}}).a})),x=Object.getOwnPropertyDescriptor,z={f:l?x:function(e,t){if(e=_(e),t=k(t,!0),P)try{return x(e,t)}catch(e){}if(S(e,t))return p(!h.f.call(e,t),e[t])}},E=function(e){if(!w(e))throw TypeError(String(e)+" is not an object");return e},O=Object.defineProperty,T={f:l?O:function(e,t,n){if(E(e),t=k(t,!0),E(n),P)try{return O(e,t,n)}catch(e){}if("get"in n||"set"in n)throw TypeError("Accessors not supported");return"value"in n&&(e[t]=n.value),e}},W=l?function(e,t,n){return T.f(e,t,p(1,n))}:function(e,t,n){return e[t]=n,e},D=function(e,t){try{W(u,e,t)}catch(n){u[e]=t}return t},A="__core-js_shared__",U=u[A]||D(A,{}),M=Function.toString;"function"!=typeof U.inspectSource&&(U.inspectSource=function(e){return M.call(e)});var F,B,L,J=U.inspectSource,j=u.WeakMap,N="function"==typeof j&&/native code/.test(J(j)),Z=a((function(e){(e.exports=function(e,t){return U[e]||(U[e]=void 0!==t?t:{})})("versions",[]).push({version:"3.6.5",mode:"global",copyright:"© 2020 Denis Pushkarev (zloirock.ru)"})})),H=0,G=Math.random(),V=function(e){return"Symbol("+String(void 0===e?"":e)+")_"+(++H+G).toString(36)},K=Z("keys"),X=function(e){return K[e]||(K[e]=V(e))},Y={},$=u.WeakMap;if(N){var Q=new $,ee=Q.get,te=Q.has,ne=Q.set;F=function(e,t){return ne.call(Q,e,t),t},B=function(e){return ee.call(Q,e)||{}},L=function(e){return te.call(Q,e)}}else{var oe=X("state");Y[oe]=!0,F=function(e,t){return W(e,oe,t),t},B=function(e){return S(e,oe)?e[oe]:{}},L=function(e){return S(e,oe)}}var ie={set:F,get:B,has:L,enforce:function(e){return L(e)?B(e):F(e,{})},getterFor:function(e){return function(t){var n;if(!w(t)||(n=B(t)).type!==e)throw TypeError("Incompatible receiver, "+e+" required");return n}}},re=a((function(e){var t=ie.get,n=ie.enforce,o=String(String).split("String");(e.exports=function(e,t,i,r){var a=!!r&&!!r.unsafe,s=!!r&&!!r.enumerable,c=!!r&&!!r.noTargetGet;"function"==typeof i&&("string"!=typeof t||S(i,"name")||W(i,"name",t),n(i).source=o.join("string"==typeof t?t:"")),e!==u?(a?!c&&e[t]&&(s=!0):delete e[t],s?e[t]=i:W(e,t,i)):s?e[t]=i:D(t,i)})(Function.prototype,"toString",(function(){return"function"==typeof this&&t(this).source||J(this)}))})),ae=u,se=function(e){return"function"==typeof e?e:void 0},ue=function(e,t){return arguments.length<2?se(ae[e])||se(u[e]):ae[e]&&ae[e][t]||u[e]&&u[e][t]},ce=Math.ceil,le=Math.floor,de=function(e){return isNaN(e=+e)?0:(e>0?le:ce)(e)},fe=Math.min,he=function(e){return e>0?fe(de(e),9007199254740991):0},pe=Math.max,ve=Math.min,be=function(e){return function(t,n,o){var i,r=_(t),a=he(r.length),s=function(e,t){var n=de(e);return n<0?pe(n+t,0):ve(n,t)}(o,a);if(e&&n!=n){for(;a>s;)if((i=r[s++])!=i)return!0}else for(;a>s;s++)if((e||s in r)&&r[s]===n)return e||s||0;return!e&&-1}},me={includes:be(!0),indexOf:be(!1)}.indexOf,ge=function(e,t){var n,o=_(e),i=0,r=[];for(n in o)!S(Y,n)&&S(o,n)&&r.push(n);for(;t.length>i;)S(o,n=t[i++])&&(~me(r,n)||r.push(n));return r},ye=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],_e=ye.concat("length","prototype"),we={f:Object.getOwnPropertyNames||function(e){return ge(e,_e)}},ke={f:Object.getOwnPropertySymbols},Ce=ue("Reflect","ownKeys")||function(e){var t=we.f(E(e)),n=ke.f;return n?t.concat(n(e)):t},Se=function(e,t){for(var n=Ce(t),o=T.f,i=z.f,r=0;r<n.length;r++){var a=n[r];S(e,a)||o(e,a,i(t,a))}},Re=/#|\.prototype\./,qe=function(e,t){var n=Pe[Ie(e)];return n==ze||n!=xe&&("function"==typeof t?c(t):!!t)},Ie=qe.normalize=function(e){return String(e).replace(Re,".").toLowerCase()},Pe=qe.data={},xe=qe.NATIVE="N",ze=qe.POLYFILL="P",Ee=qe,Oe=z.f,Te=function(t,n){var o,i,r,a,s,c=t.target,l=t.global,d=t.stat;if(o=l?u:d?u[c]||D(c,{}):(u[c]||{}).prototype)for(i in n){if(a=n[i],r=t.noTargetGet?(s=Oe(o,i))&&s.value:o[i],!Ee(l?i:c+(d?".":"#")+i,t.forced)&&void 0!==r){if(e(a)===e(r))continue;Se(a,r)}(t.sham||r&&r.sham)&&W(a,"sham",!0),re(o,i,a,t)}},We=Object.keys||function(e){return ge(e,ye)},De=function(e){return Object(y(e))},Ae=Object.assign,Ue=Object.defineProperty,Me=!Ae||c((function(){if(l&&1!==Ae({b:1},Ae(Ue({},"a",{enumerable:!0,get:function(){Ue(this,"b",{value:3,enumerable:!1})}}),{b:2})).b)return!0;var e={},t={},n=Symbol(),o="abcdefghijklmnopqrst";return e[n]=7,o.split("").forEach((function(e){t[e]=e})),7!=Ae({},e)[n]||We(Ae({},t)).join("")!=o}))?function(e,t){for(var n=De(e),o=arguments.length,i=1,r=ke.f,a=h.f;o>i;)for(var s,u=g(arguments[i++]),c=r?We(u).concat(r(u)):We(u),d=c.length,f=0;d>f;)s=c[f++],l&&!a.call(u,s)||(n[s]=u[s]);return n}:Ae;Te({target:"Object",stat:!0,forced:Object.assign!==Me},{assign:Me});ae.Object.assign;var Fe=!!Object.getOwnPropertySymbols&&!c((function(){return!String(Symbol())})),Be=Fe&&!Symbol.sham&&"symbol"==e(Symbol.iterator),Le=Z("wks"),Je=u.Symbol,je=Be?Je:Je&&Je.withoutSetter||V,Ne=function(e){return S(Le,e)||(Fe&&S(Je,e)?Le[e]=Je[e]:Le[e]=je("Symbol."+e)),Le[e]},Ze={};Ze[Ne("toStringTag")]="z";var He="[object z]"===String(Ze),Ge=Ne("toStringTag"),Ve="Arguments"==b(function(){return arguments}()),Ke=He?b:function(e){var t,n,o;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(n=function(e,t){try{return e[t]}catch(e){}}(t=Object(e),Ge))?n:Ve?b(t):"Object"==(o=b(t))&&"function"==typeof t.callee?"Arguments":o},Xe=He?{}.toString:function(){return"[object "+Ke(this)+"]"};He||re(Object.prototype,"toString",Xe,{unsafe:!0});var Ye,$e,Qe,et=function(e){return function(t,n){var o,i,r=String(y(t)),a=de(n),s=r.length;return a<0||a>=s?e?"":void 0:(o=r.charCodeAt(a))<55296||o>56319||a+1===s||(i=r.charCodeAt(a+1))<56320||i>57343?e?r.charAt(a):o:e?r.slice(a,a+2):i-56320+(o-55296<<10)+65536}},tt={codeAt:et(!1),charAt:et(!0)},nt=!c((function(){function e(){}return e.prototype.constructor=null,Object.getPrototypeOf(new e)!==e.prototype})),ot=X("IE_PROTO"),it=Object.prototype,rt=nt?Object.getPrototypeOf:function(e){return e=De(e),S(e,ot)?e[ot]:"function"==typeof e.constructor&&e instanceof e.constructor?e.constructor.prototype:e instanceof Object?it:null},at=Ne("iterator"),st=!1;[].keys&&("next"in(Qe=[].keys())?($e=rt(rt(Qe)))!==Object.prototype&&(Ye=$e):st=!0),null==Ye&&(Ye={}),S(Ye,at)||W(Ye,at,(function(){return this}));var ut,ct={IteratorPrototype:Ye,BUGGY_SAFARI_ITERATORS:st},lt=l?Object.defineProperties:function(e,t){E(e);for(var n,o=We(t),i=o.length,r=0;i>r;)T.f(e,n=o[r++],t[n]);return e},dt=ue("document","documentElement"),ft=X("IE_PROTO"),ht=function(){},pt=function(e){return"<script>"+e+"</"+"script>"},vt=function(){try{ut=document.domain&&new ActiveXObject("htmlfile")}catch(e){}var e,t;vt=ut?function(e){e.write(pt("")),e.close();var t=e.parentWindow.Object;return e=null,t}(ut):((t=I("iframe")).style.display="none",dt.appendChild(t),t.src=String("javascript:"),(e=t.contentWindow.document).open(),e.write(pt("document.F=Object")),e.close(),e.F);for(var n=ye.length;n--;)delete vt.prototype[ye[n]];return vt()};Y[ft]=!0;var bt=Object.create||function(e,t){var n;return null!==e?(ht.prototype=E(e),n=new ht,ht.prototype=null,n[ft]=e):n=vt(),void 0===t?n:lt(n,t)},mt=T.f,gt=Ne("toStringTag"),yt=function(e,t,n){e&&!S(e=n?e:e.prototype,gt)&&mt(e,gt,{configurable:!0,value:t})},_t={},wt=ct.IteratorPrototype,kt=function(){return this},Ct=Object.setPrototypeOf||("__proto__"in{}?function(){var e,t=!1,n={};try{(e=Object.getOwnPropertyDescriptor(Object.prototype,"__proto__").set).call(n,[]),t=n instanceof Array}catch(e){}return function(n,o){return E(n),function(e){if(!w(e)&&null!==e)throw TypeError("Can't set "+String(e)+" as a prototype")}(o),t?e.call(n,o):n.__proto__=o,n}}():void 0),St=ct.IteratorPrototype,Rt=ct.BUGGY_SAFARI_ITERATORS,qt=Ne("iterator"),It="keys",Pt="values",xt="entries",zt=function(){return this},Et=function(e,t,n,o,i,r,a){!function(e,t,n){var o=t+" Iterator";e.prototype=bt(wt,{next:p(1,n)}),yt(e,o,!1),_t[o]=kt}(n,t,o);var s,u,c,l=function(e){if(e===i&&b)return b;if(!Rt&&e in h)return h[e];switch(e){case It:case Pt:case xt:return function(){return new n(this,e)}}return function(){return new n(this)}},d=t+" Iterator",f=!1,h=e.prototype,v=h[qt]||h["@@iterator"]||i&&h[i],b=!Rt&&v||l(i),m="Array"==t&&h.entries||v;if(m&&(s=rt(m.call(new e)),St!==Object.prototype&&s.next&&(rt(s)!==St&&(Ct?Ct(s,St):"function"!=typeof s[qt]&&W(s,qt,zt)),yt(s,d,!0))),i==Pt&&v&&v.name!==Pt&&(f=!0,b=function(){return v.call(this)}),h[qt]!==b&&W(h,qt,b),_t[t]=b,i)if(u={values:l(Pt),keys:r?b:l(It),entries:l(xt)},a)for(c in u)(Rt||f||!(c in h))&&re(h,c,u[c]);else Te({target:t,proto:!0,forced:Rt||f},u);return u},Ot=tt.charAt,Tt="String Iterator",Wt=ie.set,Dt=ie.getterFor(Tt);Et(String,"String",(function(e){Wt(this,{type:Tt,string:String(e),index:0})}),(function(){var e,t=Dt(this),n=t.string,o=t.index;return o>=n.length?{value:void 0,done:!0}:(e=Ot(n,o),t.index+=e.length,{value:e,done:!1})}));var At={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0},Ut=Ne("unscopables"),Mt=Array.prototype;null==Mt[Ut]&&T.f(Mt,Ut,{configurable:!0,value:bt(null)});var Ft=function(e){Mt[Ut][e]=!0},Bt="Array Iterator",Lt=ie.set,Jt=ie.getterFor(Bt),jt=Et(Array,"Array",(function(e,t){Lt(this,{type:Bt,target:_(e),index:0,kind:t})}),(function(){var e=Jt(this),t=e.target,n=e.kind,o=e.index++;return!t||o>=t.length?(e.target=void 0,{value:void 0,done:!0}):"keys"==n?{value:o,done:!1}:"values"==n?{value:t[o],done:!1}:{value:[o,t[o]],done:!1}}),"values");_t.Arguments=_t.Array,Ft("keys"),Ft("values"),Ft("entries");var Nt=Ne("iterator"),Zt=Ne("toStringTag"),Ht=jt.values;for(var Gt in At){var Vt=u[Gt],Kt=Vt&&Vt.prototype;if(Kt){if(Kt[Nt]!==Ht)try{W(Kt,Nt,Ht)}catch(e){Kt[Nt]=Ht}if(Kt[Zt]||W(Kt,Zt,Gt),At[Gt])for(var Xt in jt)if(Kt[Xt]!==jt[Xt])try{W(Kt,Xt,jt[Xt])}catch(e){Kt[Xt]=jt[Xt]}}}var Yt=u.Promise,$t=Ne("species"),Qt=function(e){if("function"!=typeof e)throw TypeError(String(e)+" is not a function");return e},en=Ne("iterator"),tn=Array.prototype,nn=function(e,t,n){if(Qt(e),void 0===t)return e;switch(n){case 0:return function(){return e.call(t)};case 1:return function(n){return e.call(t,n)};case 2:return function(n,o){return e.call(t,n,o)};case 3:return function(n,o,i){return e.call(t,n,o,i)}}return function(){return e.apply(t,arguments)}},on=Ne("iterator"),rn=function(e,t,n,o){try{return o?t(E(n)[0],n[1]):t(n)}catch(t){var i=e.return;throw void 0!==i&&E(i.call(e)),t}},an=a((function(t){var n=function(e,t){this.stopped=e,this.result=t};(t.exports=function(t,o,i,r,a){var s,u,c,l,d,f,h,p,v=nn(o,i,r?2:1);if(a)s=t;else{if("function"!=typeof(u=function(e){if(null!=e)return e[on]||e["@@iterator"]||_t[Ke(e)]}(t)))throw TypeError("Target is not iterable");if(void 0!==(p=u)&&(_t.Array===p||tn[en]===p)){for(c=0,l=he(t.length);l>c;c++)if((d=r?v(E(h=t[c])[0],h[1]):v(t[c]))&&d instanceof n)return d;return new n(!1)}s=u.call(t)}for(f=s.next;!(h=f.call(s)).done;)if("object"==e(d=rn(s,v,h.value,r))&&d&&d instanceof n)return d;return new n(!1)}).stop=function(e){return new n(!0,e)}})),sn=Ne("iterator"),un=!1;try{var cn=0,ln={next:function(){return{done:!!cn++}},return:function(){un=!0}};ln[sn]=function(){return this},Array.from(ln,(function(){throw 2}))}catch(e){}var dn,fn,hn,pn=Ne("species"),vn=function(e,t){var n,o=E(e).constructor;return void 0===o||null==(n=E(o)[pn])?t:Qt(n)},bn=ue("navigator","userAgent")||"",mn=/(iphone|ipod|ipad).*applewebkit/i.test(bn),gn=u.location,yn=u.setImmediate,_n=u.clearImmediate,wn=u.process,kn=u.MessageChannel,Cn=u.Dispatch,Sn=0,Rn={},qn="onreadystatechange",In=function(e){if(Rn.hasOwnProperty(e)){var t=Rn[e];delete Rn[e],t()}},Pn=function(e){return function(){In(e)}},xn=function(e){In(e.data)},zn=function(e){u.postMessage(e+"",gn.protocol+"//"+gn.host)};yn&&_n||(yn=function(e){for(var t=[],n=1;arguments.length>n;)t.push(arguments[n++]);return Rn[++Sn]=function(){("function"==typeof e?e:Function(e)).apply(void 0,t)},dn(Sn),Sn},_n=function(e){delete Rn[e]},"process"==b(wn)?dn=function(e){wn.nextTick(Pn(e))}:Cn&&Cn.now?dn=function(e){Cn.now(Pn(e))}:kn&&!mn?(hn=(fn=new kn).port2,fn.port1.onmessage=xn,dn=nn(hn.postMessage,hn,1)):!u.addEventListener||"function"!=typeof postMessage||u.importScripts||c(zn)||"file:"===gn.protocol?dn=qn in I("script")?function(e){dt.appendChild(I("script")).onreadystatechange=function(){dt.removeChild(this),In(e)}}:function(e){setTimeout(Pn(e),0)}:(dn=zn,u.addEventListener("message",xn,!1)));var En,On,Tn,Wn,Dn,An,Un,Mn,Fn={set:yn,clear:_n},Bn=z.f,Ln=Fn.set,Jn=u.MutationObserver||u.WebKitMutationObserver,jn=u.process,Nn=u.Promise,Zn="process"==b(jn),Hn=Bn(u,"queueMicrotask"),Gn=Hn&&Hn.value;Gn||(En=function(){var e,t;for(Zn&&(e=jn.domain)&&e.exit();On;){t=On.fn,On=On.next;try{t()}catch(e){throw On?Wn():Tn=void 0,e}}Tn=void 0,e&&e.enter()},Zn?Wn=function(){jn.nextTick(En)}:Jn&&!mn?(Dn=!0,An=document.createTextNode(""),new Jn(En).observe(An,{characterData:!0}),Wn=function(){An.data=Dn=!Dn}):Nn&&Nn.resolve?(Un=Nn.resolve(void 0),Mn=Un.then,Wn=function(){Mn.call(Un,En)}):Wn=function(){Ln.call(u,En)});var Vn,Kn,Xn=Gn||function(e){var t={fn:e,next:void 0};Tn&&(Tn.next=t),On||(On=t,Wn()),Tn=t},Yn=function(e){var t,n;this.promise=new e((function(e,o){if(void 0!==t||void 0!==n)throw TypeError("Bad Promise constructor");t=e,n=o})),this.resolve=Qt(t),this.reject=Qt(n)},$n={f:function(e){return new Yn(e)}},Qn=function(e,t){if(E(e),w(t)&&t.constructor===e)return t;var n=$n.f(e);return(0,n.resolve)(t),n.promise},eo=function(e){try{return{error:!1,value:e()}}catch(e){return{error:!0,value:e}}},to=u.process,no=to&&to.versions,oo=no&&no.v8;oo?Kn=(Vn=oo.split("."))[0]+Vn[1]:bn&&(!(Vn=bn.match(/Edge\/(\d+)/))||Vn[1]>=74)&&(Vn=bn.match(/Chrome\/(\d+)/))&&(Kn=Vn[1]);var io,ro,ao,so,uo=Kn&&+Kn,co=Fn.set,lo=Ne("species"),fo="Promise",ho=ie.get,po=ie.set,vo=ie.getterFor(fo),bo=Yt,mo=u.TypeError,go=u.document,yo=u.process,_o=ue("fetch"),wo=$n.f,ko=wo,Co="process"==b(yo),So=!!(go&&go.createEvent&&u.dispatchEvent),Ro="unhandledrejection",qo=Ee(fo,(function(){if(!(J(bo)!==String(bo))){if(66===uo)return!0;if(!Co&&"function"!=typeof PromiseRejectionEvent)return!0}if(uo>=51&&/native code/.test(bo))return!1;var e=bo.resolve(1),t=function(e){e((function(){}),(function(){}))};return(e.constructor={})[lo]=t,!(e.then((function(){}))instanceof t)})),Io=qo||!function(e,t){if(!t&&!un)return!1;var n=!1;try{var o={};o[sn]=function(){return{next:function(){return{done:n=!0}}}},e(o)}catch(e){}return n}((function(e){bo.all(e).catch((function(){}))})),Po=function(e){var t;return!(!w(e)||"function"!=typeof(t=e.then))&&t},xo=function(e,t,n){if(!t.notified){t.notified=!0;var o=t.reactions;Xn((function(){for(var i=t.value,r=1==t.state,a=0;o.length>a;){var s,u,c,l=o[a++],d=r?l.ok:l.fail,f=l.resolve,h=l.reject,p=l.domain;try{d?(r||(2===t.rejection&&To(e,t),t.rejection=1),!0===d?s=i:(p&&p.enter(),s=d(i),p&&(p.exit(),c=!0)),s===l.promise?h(mo("Promise-chain cycle")):(u=Po(s))?u.call(s,f,h):f(s)):h(i)}catch(e){p&&!c&&p.exit(),h(e)}}t.reactions=[],t.notified=!1,n&&!t.rejection&&Eo(e,t)}))}},zo=function(e,t,n){var o,i;So?((o=go.createEvent("Event")).promise=t,o.reason=n,o.initEvent(e,!1,!0),u.dispatchEvent(o)):o={promise:t,reason:n},(i=u["on"+e])?i(o):e===Ro&&function(e,t){var n=u.console;n&&n.error&&(1===arguments.length?n.error(e):n.error(e,t))}("Unhandled promise rejection",n)},Eo=function(e,t){co.call(u,(function(){var n,o=t.value;if(Oo(t)&&(n=eo((function(){Co?yo.emit("unhandledRejection",o,e):zo(Ro,e,o)})),t.rejection=Co||Oo(t)?2:1,n.error))throw n.value}))},Oo=function(e){return 1!==e.rejection&&!e.parent},To=function(e,t){co.call(u,(function(){Co?yo.emit("rejectionHandled",e):zo("rejectionhandled",e,t.value)}))},Wo=function(e,t,n,o){return function(i){e(t,n,i,o)}},Do=function(e,t,n,o){t.done||(t.done=!0,o&&(t=o),t.value=n,t.state=2,xo(e,t,!0))},Ao=function e(t,n,o,i){if(!n.done){n.done=!0,i&&(n=i);try{if(t===o)throw mo("Promise can't be resolved itself");var r=Po(o);r?Xn((function(){var i={done:!1};try{r.call(o,Wo(e,t,i,n),Wo(Do,t,i,n))}catch(e){Do(t,i,e,n)}})):(n.value=o,n.state=1,xo(t,n,!1))}catch(e){Do(t,{done:!1},e,n)}}};qo&&(bo=function(e){!function(e,t,n){if(!(e instanceof t))throw TypeError("Incorrect "+(n?n+" ":"")+"invocation")}(this,bo,fo),Qt(e),io.call(this);var t=ho(this);try{e(Wo(Ao,this,t),Wo(Do,this,t))}catch(e){Do(this,t,e)}},(io=function(e){po(this,{type:fo,done:!1,notified:!1,parent:!1,reactions:[],rejection:!1,state:0,value:void 0})}).prototype=function(e,t,n){for(var o in t)re(e,o,t[o],n);return e}(bo.prototype,{then:function(e,t){var n=vo(this),o=wo(vn(this,bo));return o.ok="function"!=typeof e||e,o.fail="function"==typeof t&&t,o.domain=Co?yo.domain:void 0,n.parent=!0,n.reactions.push(o),0!=n.state&&xo(this,n,!1),o.promise},catch:function(e){return this.then(void 0,e)}}),ro=function(){var e=new io,t=ho(e);this.promise=e,this.resolve=Wo(Ao,e,t),this.reject=Wo(Do,e,t)},$n.f=wo=function(e){return e===bo||e===ao?new ro(e):ko(e)},"function"==typeof Yt&&(so=Yt.prototype.then,re(Yt.prototype,"then",(function(e,t){var n=this;return new bo((function(e,t){so.call(n,e,t)})).then(e,t)}),{unsafe:!0}),"function"==typeof _o&&Te({global:!0,enumerable:!0,forced:!0},{fetch:function(e){return Qn(bo,_o.apply(u,arguments))}}))),Te({global:!0,wrap:!0,forced:qo},{Promise:bo}),yt(bo,fo,!1),function(e){var t=ue(e),n=T.f;l&&t&&!t[$t]&&n(t,$t,{configurable:!0,get:function(){return this}})}(fo),ao=ue(fo),Te({target:fo,stat:!0,forced:qo},{reject:function(e){var t=wo(this);return t.reject.call(void 0,e),t.promise}}),Te({target:fo,stat:!0,forced:qo},{resolve:function(e){return Qn(this,e)}}),Te({target:fo,stat:!0,forced:Io},{all:function(e){var t=this,n=wo(t),o=n.resolve,i=n.reject,r=eo((function(){var n=Qt(t.resolve),r=[],a=0,s=1;an(e,(function(e){var u=a++,c=!1;r.push(void 0),s++,n.call(t,e).then((function(e){c||(c=!0,r[u]=e,--s||o(r))}),i)})),--s||o(r)}));return r.error&&i(r.value),n.promise},race:function(e){var t=this,n=wo(t),o=n.reject,i=eo((function(){var i=Qt(t.resolve);an(e,(function(e){i.call(t,e).then(n.resolve,o)}))}));return i.error&&o(i.value),n.promise}}),Te({target:"Promise",stat:!0},{allSettled:function(e){var t=this,n=$n.f(t),o=n.resolve,i=n.reject,r=eo((function(){var n=Qt(t.resolve),i=[],r=0,a=1;an(e,(function(e){var s=r++,u=!1;i.push(void 0),a++,n.call(t,e).then((function(e){u||(u=!0,i[s]={status:"fulfilled",value:e},--a||o(i))}),(function(e){u||(u=!0,i[s]={status:"rejected",reason:e},--a||o(i))}))})),--a||o(i)}));return r.error&&i(r.value),n.promise}});var Uo=!!Yt&&c((function(){Yt.prototype.finally.call({then:function(){}},(function(){}))}));Te({target:"Promise",proto:!0,real:!0,forced:Uo},{finally:function(e){var t=vn(this,ue("Promise")),n="function"==typeof e;return this.then(n?function(n){return Qn(t,e()).then((function(){return n}))}:e,n?function(n){return Qn(t,e()).then((function(){throw n}))}:e)}}),"function"!=typeof Yt||Yt.prototype.finally||re(Yt.prototype,"finally",ue("Promise").prototype.finally);ae.Promise;var Mo="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)||"undefined"!=typeof msCrypto&&"function"==typeof msCrypto.getRandomValues&&msCrypto.getRandomValues.bind(msCrypto),Fo=new Uint8Array(16);function Bo(){if(!Mo)throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return Mo(Fo)}var Lo=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i;function Jo(e){return"string"==typeof e&&Lo.test(e)}for(var jo=[],No=0;No<256;++No)jo.push((No+256).toString(16).substr(1));function Zo(e,t,n){var o=(e=e||{}).random||(e.rng||Bo)();if(o[6]=15&o[6]|64,o[8]=63&o[8]|128,t){n=n||0;for(var i=0;i<16;++i)t[n+i]=o[i];return t}return function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=(jo[e[t+0]]+jo[e[t+1]]+jo[e[t+2]]+jo[e[t+3]]+"-"+jo[e[t+4]]+jo[e[t+5]]+"-"+jo[e[t+6]]+jo[e[t+7]]+"-"+jo[e[t+8]]+jo[e[t+9]]+"-"+jo[e[t+10]]+jo[e[t+11]]+jo[e[t+12]]+jo[e[t+13]]+jo[e[t+14]]+jo[e[t+15]]).toLowerCase();if(!Jo(n))throw TypeError("Stringified UUID is invalid");return n}(o)}var Ho,Go,Vo="3.4.5",Ko="function"==typeof atob,Xo="function"==typeof btoa,Yo="function"==typeof Buffer,$o=function(e){if(Array.isArray(e))return i(e)}(Ho="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=")||function(e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}(Ho)||function(e,t){if(e){if("string"==typeof e)return i(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?i(e,t):void 0}}(Ho)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),Qo=(Go={},$o.forEach((function(e,t){return Go[e]=t})),Go),ei=/^(?:[A-Za-z\d+\/]{4})*?(?:[A-Za-z\d+\/]{2}(?:==)?|[A-Za-z\d+\/]{3}=?)?$/,ti=String.fromCharCode.bind(String),ni="function"==typeof Uint8Array.from?Uint8Array.from.bind(Uint8Array):function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:function(e){return e};return new Uint8Array(Array.prototype.slice.call(e,0).map(t))},oi=function(e){return e.replace(/[+\/]/g,(function(e){return"+"==e?"-":"_"})).replace(/=+$/m,"")},ii=function(e){return e.replace(/[^A-Za-z0-9\+\/]/g,"")},ri=function(e){for(var t,n,o,i,r="",a=e.length%3,s=0;s<e.length;){if((n=e.charCodeAt(s++))>255||(o=e.charCodeAt(s++))>255||(i=e.charCodeAt(s++))>255)throw new TypeError("invalid character found");r+=$o[(t=n<<16|o<<8|i)>>18&63]+$o[t>>12&63]+$o[t>>6&63]+$o[63&t]}return a?r.slice(0,a-3)+"===".substring(a):r},ai=Xo?function(e){return btoa(e)}:Yo?function(e){return Buffer.from(e,"binary").toString("base64")}:ri,si=Yo?function(e){return Buffer.from(e).toString("base64")}:function(e){for(var t=[],n=0,o=e.length;n<o;n+=4096)t.push(ti.apply(null,e.subarray(n,n+4096)));return ai(t.join(""))},ui=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return t?oi(si(e)):si(e)},ci=function(e){return unescape(encodeURIComponent(e))},li=Yo?function(e){return Buffer.from(e,"utf8").toString("base64")}:function(e){return ai(ci(e))},di=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return t?oi(li(e)):li(e)},fi=function(e){return di(e,!0)},hi=function(e){return decodeURIComponent(escape(e))},pi=function(e){if(e=e.replace(/\s+/g,""),!ei.test(e))throw new TypeError("malformed base64.");e+="==".slice(2-(3&e.length));for(var t,n,o,i="",r=0;r<e.length;)t=Qo[e.charAt(r++)]<<18|Qo[e.charAt(r++)]<<12|(n=Qo[e.charAt(r++)])<<6|(o=Qo[e.charAt(r++)]),i+=64===n?ti(t>>16&255):64===o?ti(t>>16&255,t>>8&255):ti(t>>16&255,t>>8&255,255&t);return i},vi=Ko?function(e){return atob(ii(e))}:Yo?function(e){return Buffer.from(e,"base64").toString("binary")}:pi,bi=Yo?function(e){return Buffer.from(e,"base64").toString("utf8")}:function(e){return hi(vi(e))},mi=function(e){return ii(e.replace(/[-_]/g,(function(e){return"-"==e?"+":"/"})))},gi=function(e){return bi(mi(e))},yi=Yo?function(e){return ni(Buffer.from(mi(e),"base64"))}:function(e){return ni(vi(mi(e)),(function(e){return e.charCodeAt(0)}))},_i=function(e){return{value:e,enumerable:!1,writable:!0,configurable:!0}},wi=function(){var e=function(e,t){return Object.defineProperty(String.prototype,e,_i(t))};e("fromBase64",(function(){return gi(this)})),e("toBase64",(function(e){return di(this,e)})),e("toBase64URI",(function(){return di(this,!0)})),e("toBase64URL",(function(){return di(this,!0)})),e("toUint8Array",(function(){return yi(this)}))},ki=function(){var e=function(e,t){return Object.defineProperty(Uint8Array.prototype,e,_i(t))};e("toBase64",(function(e){return ui(this,e)})),e("toBase64URI",(function(){return ui(this,!0)})),e("toBase64URL",(function(){return ui(this,!0)}))},Ci={version:Vo,VERSION:"3.4.5",atob:vi,atobPolyfill:pi,btoa:ai,btoaPolyfill:ri,fromBase64:gi,toBase64:di,encode:di,encodeURI:fi,encodeURL:fi,utob:ci,btou:hi,decode:gi,fromUint8Array:ui,toUint8Array:yi,extendString:wi,extendUint8Array:ki,extendBuiltins:function(){wi(),ki()}},Si=new(function(){function e(){t(this,e),this.oBase64=Ci}return o(e,[{key:"browser",value:function(){var e=navigator.userAgent.toLowerCase(),t=/(edge)[/]([\w.]+)/.exec(e)||/(chrome)[/]([\w.]+)/.exec(e)||/(safari)[/]([\w.]+)/.exec(e)||/(opera)(?:.*version)?[/]([\w.]+)/.exec(e)||/(trident.*rv:)([\w.]+)/.exec(e)||/(msie) ([\w.]+)/.exec(e)||e.indexOf("compatible")<0&&/(mozilla)(?:.*? rv:([\w.]+))?/.exec(e)||["unknow","0"];t.length>0&&t[1].indexOf("trident")>-1&&(t[1]="msie");var n={};return n[t[1]]=!0,n.version=t[2],n}},{key:"getCreateWndMode",value:function(){var e=navigator.userAgent,t=navigator.platform,n="Win64"===t||"Win32"===t||"Windows"===t,o=this.browser(),i=!0;return window.top!==window?i=!1:n?(e.indexOf("Windows NT 10.0")>-1&&o.mozilla&&(i=!1),o.edge&&(i=!1)):i=!1,i}},{key:"getWndPostion",value:function(e,t,n){var o=0,i=0,r=e.getBoundingClientRect(),a=e.ownerDocument.defaultView,s=r.top+a.pageYOffset,u=r.left+a.pageXOffset,c=this.getDevicePixelRatio(),l=window.getComputedStyle(e),d=parseInt(l["border-left-width"].slice(0,-2),10),f=parseInt(l["border-top-width"].slice(0,-2),10);if(t)if(this.browser().msie){var h=window,p=h.outerWidth-h.innerWidth-(h.screenLeft-h.screenX);o=u+(h.screenLeft-h.screenX)+d-p,i=s+(h.screenTop-h.screenY)+f}else{var v=0,b=0,m=Math.round((window.outerWidth-window.innerWidth)/2);this.isWindows()&&this.browser().chrome&&(-8===m||window.screen.height-window.outerHeight==0?-8===m&&(v=8,b=8):8===m?v=-5:0===m&&(b=8)),this.browser().mozilla&&(7===m||6===m?v=-6:8===m&&(v=-8)),o=u+m+d+v,i=s+(window.outerHeight-window.innerHeight-m)+f+b}else{var g=window.top,y=0,_=0,w=0,k=0;try{y=g.outerWidth-g.innerWidth,_=g.outerHeight-g.innerHeight,w=g.screenLeft-g.screenX,k=g.screenTop-g.screenY}catch(e){y=n.outerWidth-n.innerWidth,_=n.outerHeight-n.innerHeight,w=n.screenLeft-n.screenX,k=n.screenTop-n.screenY}if(this.browser().msie){0,o=u+w+d-0,i=s+k+f}else{var C=y/2;o=u+C+d,i=s+(_-C)+f,this.browser().chrome&&0===C&&(o+=8,i+=8)}}this.isWindows()&&(this.browser().chrome||this.browser().safari)&&(o=u+d,i=s+f);var S=0,R=0;return(!this.browser().msie||this.browser().msie&&"11.0"===this.browser().version)&&(S=window.scrollX||window.pageXOffset,R=window.scrollY||window.pageYOffset),{left:o=Math.round((o-S)*c),top:i=Math.round((i-R)*c)}}},{key:"detectPort",value:function(e,t,n){var o="HikCentralWebControlPort:".concat(e,"-").concat(t),i=this,r=0,a=!1,s=null;sessionStorage&&null!==(s=sessionStorage.getItem(o))&&(s=parseInt(s,10));for(var u=[],c=e;c<=t;c++)c!==s&&u.push(c);null!==s&&u.unshift(s);for(var l=[],d=function(){r>0&&clearTimeout(r)},f=function(){for(var e=0,t=l.length;e<t;e++)delete l[e]},h=0,p=(new Date).getTime(),v=function(e,t){setTimeout((function(){l.push(i.createImageHttp(u[t],{timeStamp:p+t,success:function(e){!function(e){sessionStorage&&sessionStorage.setItem(o,e),!a&&n.success&&(d(),f(),n.success(e))}(e)},error:function(){h++,u.length===h&&!a&&n.error&&(d(),f(),n.error())}}))}),100)},b=0,m=u.length;b<m;b++)v(0,b);r=setTimeout((function(){a=!0,n.error&&(f(),n.error())}),6e4)}},{key:"createImageHttp",value:function(e,t){var n=new Image;return n.onload=function(){t.success&&t.success(e)},n.onerror=function(){t.error&&t.error()},n.onabort=function(){t.abort&&t.abort()},n.src="http://127.0.0.1:".concat(e,"/imghttp/local?update=").concat(t.timeStamp),n}},{key:"utf8to16",value:function(e){for(var t,n,o,i="",r=0,a=e.length;r<a;)switch((t=e.charCodeAt(r++))>>4){case 0:case 1:case 2:case 3:case 4:case 5:case 6:case 7:i+=e.charAt(r-1);break;case 12:case 13:n=e.charCodeAt(r++),i+=String.fromCharCode((31&t)<<6|63&n);break;case 14:n=e.charCodeAt(r++),o=e.charCodeAt(r++),i+=String.fromCharCode((15&t)<<12|(63&n)<<6|(63&o)<<0)}return i}},{key:"createEventScript",value:function(e,t,n){var o=document.createElement("script");o.htmlFor=e,o.event=t,o.innerHTML=n,document.getElementById(e).appendChild(o)}},{key:"isMacOS",value:function(){return"MacIntel"===navigator.platform}},{key:"isWindows",value:function(){return navigator.platform.indexOf("Win")>-1}},{key:"getDevicePixelRatio",value:function(){var e=1;return this.isMacOS()||(e=window.devicePixelRatio||window.screen.deviceXDPI/window.screen.logicalXDPI),e}},{key:"Base64",value:function(){return this.oBase64||{}}}]),e}()),Ri=function(){function e(n){t(this,e),this.oOptions=Object.assign({iPort:-1,cbConnectSuccess:null,cbConnectError:null,cbConnectClose:null},n),this.oWebSocket=null,this.szUUID="",this.szVersion="",this.oRequestList={},this.bNormalClose=!1,this.oWindowControlCallback={},this.oSadpCallback={},this.oSliceCallback={},this.oSerialCallback={},this.oUIControlCallback={},this.oUpgradeCallback={},this.init()}return o(e,[{key:"init",value:function(){var e=this,t=function(){e.oOptions.cbConnectClose&&e.oOptions.cbConnectClose(e.bNormalClose),e.bNormalClose=!1};e.oWebSocket=new WebSocket("ws://127.0.0.1:".concat(e.oOptions.iPort)),e.oWebSocket.onerror=function(){},e.oWebSocket.onopen=function(){var t={sequence:Zo(),cmd:"system.connect"},n=JSON.stringify(t);e.oWebSocket.send(n)},e.oWebSocket.onmessage=function(t){var n=t.data,o=JSON.parse(n),i=o.sequence;void 0===i&&void 0===o.cmd?(e.szUUID=o.uuid,e.szVersion=o.version,e.oOptions.cbConnectSuccess&&e.oOptions.cbConnectSuccess()):void 0!==o.cmd?e.parseCmd(o):void 0!==e.oRequestList[i]&&(0===o.errorModule&&0===o.errorCode?e.oRequestList[i].resolve(o):e.oRequestList[i].reject(o),delete e.oRequestList[i])},e.oWebSocket.onclose=function(){e.oWebSocket=null,Si.browser().mozilla?setTimeout((function(){t()}),100):t()}}},{key:"setWindowControlCallback",value:function(e){this.oWindowControlCallback=e}},{key:"setSadpCallback",value:function(e){this.oSadpCallback=e}},{key:"setSliceCallback",value:function(e){this.oSliceCallback=e}},{key:"setSerialCallback",value:function(e){this.oSerialCallback=e}},{key:"setUIControlCallback",value:function(e){this.oUIControlCallback=e}},{key:"setUpgradeCallback",value:function(e){this.oUpgradeCallback=e}},{key:"getServiceVersion",value:function(){return this.szVersion}},{key:"getRequestUUID",value:function(){return this.szUUID}},{key:"disconnect",value:function(){this.bNormalClose=!0,this.oWebSocket&&WebSocket.OPEN===this.oWebSocket.readyState&&(this.oWebSocket.close(),delete this.oWebSocket)}},{key:"sendRequest",value:function(e){var t=this;return new Promise((function(n,o){var i=Zo();e.sequence=i,t.oRequestList[i]={resolve:n,reject:o},e.uuid=t.szUUID,e.timestamp="".concat((new Date).getTime());var r=JSON.stringify(e);t.oWebSocket&&WebSocket.OPEN===t.oWebSocket.readyState?t.oWebSocket.send(r):o()}))}},{key:"parseCmd",value:function(e){var t=e.cmd.split("."),n=t[1].replace(/^[a-z]{1}/g,(function(e){return e.toUpperCase()}));"window"===t[0]||"play"===t[0]?this.oWindowControlCallback["cb".concat(n)]&&this.oWindowControlCallback["cb".concat(n)](e):"sadp"===t[0]?this.oSadpCallback["cb".concat(n)]&&this.oSadpCallback["cb".concat(n)](e):"serial"===t[0]?this.oSerialCallback["cb".concat(n)]&&this.oSerialCallback["cb".concat(n)](e):"slice"===t[0]?this.oSliceCallback["cb".concat(n)]&&this.oSliceCallback["cb".concat(n)](e):"ui"===t[0]?this.oUIControlCallback["cb".concat(n)]&&this.oUIControlCallback["cb".concat(n)](e):"upgrade"===t[0]&&this.oUpgradeCallback["cb".concat(n)]&&this.oUpgradeCallback["cb".concat(n)](e)}}]),e}(),qi=a((function(t,n){var o="undefined"!=typeof Uint8Array&&"undefined"!=typeof Uint16Array&&"undefined"!=typeof Int32Array;function i(e,t){return Object.prototype.hasOwnProperty.call(e,t)}n.assign=function(t){for(var n=Array.prototype.slice.call(arguments,1);n.length;){var o=n.shift();if(o){if("object"!==e(o))throw new TypeError(o+"must be non-object");for(var r in o)i(o,r)&&(t[r]=o[r])}}return t},n.shrinkBuf=function(e,t){return e.length===t?e:e.subarray?e.subarray(0,t):(e.length=t,e)};var r={arraySet:function(e,t,n,o,i){if(t.subarray&&e.subarray)e.set(t.subarray(n,n+o),i);else for(var r=0;r<o;r++)e[i+r]=t[n+r]},flattenChunks:function(e){var t,n,o,i,r,a;for(o=0,t=0,n=e.length;t<n;t++)o+=e[t].length;for(a=new Uint8Array(o),i=0,t=0,n=e.length;t<n;t++)r=e[t],a.set(r,i),i+=r.length;return a}},a={arraySet:function(e,t,n,o,i){for(var r=0;r<o;r++)e[i+r]=t[n+r]},flattenChunks:function(e){return[].concat.apply([],e)}};n.setTyped=function(e){e?(n.Buf8=Uint8Array,n.Buf16=Uint16Array,n.Buf32=Int32Array,n.assign(n,r)):(n.Buf8=Array,n.Buf16=Array,n.Buf32=Array,n.assign(n,a))},n.setTyped(o)}));function Ii(e){for(var t=e.length;--t>=0;)e[t]=0}var Pi=256,xi=286,zi=30,Ei=15,Oi=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0],Ti=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13],Wi=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,3,7],Di=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],Ai=new Array(576);Ii(Ai);var Ui=new Array(60);Ii(Ui);var Mi=new Array(512);Ii(Mi);var Fi=new Array(256);Ii(Fi);var Bi=new Array(29);Ii(Bi);var Li,Ji,ji,Ni=new Array(zi);function Zi(e,t,n,o,i){this.static_tree=e,this.extra_bits=t,this.extra_base=n,this.elems=o,this.max_length=i,this.has_stree=e&&e.length}function Hi(e,t){this.dyn_tree=e,this.max_code=0,this.stat_desc=t}function Gi(e){return e<256?Mi[e]:Mi[256+(e>>>7)]}function Vi(e,t){e.pending_buf[e.pending++]=255&t,e.pending_buf[e.pending++]=t>>>8&255}function Ki(e,t,n){e.bi_valid>16-n?(e.bi_buf|=t<<e.bi_valid&65535,Vi(e,e.bi_buf),e.bi_buf=t>>16-e.bi_valid,e.bi_valid+=n-16):(e.bi_buf|=t<<e.bi_valid&65535,e.bi_valid+=n)}function Xi(e,t,n){Ki(e,n[2*t],n[2*t+1])}function Yi(e,t){var n=0;do{n|=1&e,e>>>=1,n<<=1}while(--t>0);return n>>>1}function $i(e,t,n){var o,i,r=new Array(16),a=0;for(o=1;o<=Ei;o++)r[o]=a=a+n[o-1]<<1;for(i=0;i<=t;i++){var s=e[2*i+1];0!==s&&(e[2*i]=Yi(r[s]++,s))}}function Qi(e){var t;for(t=0;t<xi;t++)e.dyn_ltree[2*t]=0;for(t=0;t<zi;t++)e.dyn_dtree[2*t]=0;for(t=0;t<19;t++)e.bl_tree[2*t]=0;e.dyn_ltree[512]=1,e.opt_len=e.static_len=0,e.last_lit=e.matches=0}function er(e){e.bi_valid>8?Vi(e,e.bi_buf):e.bi_valid>0&&(e.pending_buf[e.pending++]=e.bi_buf),e.bi_buf=0,e.bi_valid=0}function tr(e,t,n,o){var i=2*t,r=2*n;return e[i]<e[r]||e[i]===e[r]&&o[t]<=o[n]}function nr(e,t,n){for(var o=e.heap[n],i=n<<1;i<=e.heap_len&&(i<e.heap_len&&tr(t,e.heap[i+1],e.heap[i],e.depth)&&i++,!tr(t,o,e.heap[i],e.depth));)e.heap[n]=e.heap[i],n=i,i<<=1;e.heap[n]=o}function or(e,t,n){var o,i,r,a,s=0;if(0!==e.last_lit)do{o=e.pending_buf[e.d_buf+2*s]<<8|e.pending_buf[e.d_buf+2*s+1],i=e.pending_buf[e.l_buf+s],s++,0===o?Xi(e,i,t):(Xi(e,(r=Fi[i])+Pi+1,t),0!==(a=Oi[r])&&Ki(e,i-=Bi[r],a),Xi(e,r=Gi(--o),n),0!==(a=Ti[r])&&Ki(e,o-=Ni[r],a))}while(s<e.last_lit);Xi(e,256,t)}function ir(e,t){var n,o,i,r=t.dyn_tree,a=t.stat_desc.static_tree,s=t.stat_desc.has_stree,u=t.stat_desc.elems,c=-1;for(e.heap_len=0,e.heap_max=573,n=0;n<u;n++)0!==r[2*n]?(e.heap[++e.heap_len]=c=n,e.depth[n]=0):r[2*n+1]=0;for(;e.heap_len<2;)r[2*(i=e.heap[++e.heap_len]=c<2?++c:0)]=1,e.depth[i]=0,e.opt_len--,s&&(e.static_len-=a[2*i+1]);for(t.max_code=c,n=e.heap_len>>1;n>=1;n--)nr(e,r,n);i=u;do{n=e.heap[1],e.heap[1]=e.heap[e.heap_len--],nr(e,r,1),o=e.heap[1],e.heap[--e.heap_max]=n,e.heap[--e.heap_max]=o,r[2*i]=r[2*n]+r[2*o],e.depth[i]=(e.depth[n]>=e.depth[o]?e.depth[n]:e.depth[o])+1,r[2*n+1]=r[2*o+1]=i,e.heap[1]=i++,nr(e,r,1)}while(e.heap_len>=2);e.heap[--e.heap_max]=e.heap[1],function(e,t){var n,o,i,r,a,s,u=t.dyn_tree,c=t.max_code,l=t.stat_desc.static_tree,d=t.stat_desc.has_stree,f=t.stat_desc.extra_bits,h=t.stat_desc.extra_base,p=t.stat_desc.max_length,v=0;for(r=0;r<=Ei;r++)e.bl_count[r]=0;for(u[2*e.heap[e.heap_max]+1]=0,n=e.heap_max+1;n<573;n++)(r=u[2*u[2*(o=e.heap[n])+1]+1]+1)>p&&(r=p,v++),u[2*o+1]=r,o>c||(e.bl_count[r]++,a=0,o>=h&&(a=f[o-h]),s=u[2*o],e.opt_len+=s*(r+a),d&&(e.static_len+=s*(l[2*o+1]+a)));if(0!==v){do{for(r=p-1;0===e.bl_count[r];)r--;e.bl_count[r]--,e.bl_count[r+1]+=2,e.bl_count[p]--,v-=2}while(v>0);for(r=p;0!==r;r--)for(o=e.bl_count[r];0!==o;)(i=e.heap[--n])>c||(u[2*i+1]!==r&&(e.opt_len+=(r-u[2*i+1])*u[2*i],u[2*i+1]=r),o--)}}(e,t),$i(r,c,e.bl_count)}function rr(e,t,n){var o,i,r=-1,a=t[1],s=0,u=7,c=4;for(0===a&&(u=138,c=3),t[2*(n+1)+1]=65535,o=0;o<=n;o++)i=a,a=t[2*(o+1)+1],++s<u&&i===a||(s<c?e.bl_tree[2*i]+=s:0!==i?(i!==r&&e.bl_tree[2*i]++,e.bl_tree[32]++):s<=10?e.bl_tree[34]++:e.bl_tree[36]++,s=0,r=i,0===a?(u=138,c=3):i===a?(u=6,c=3):(u=7,c=4))}function ar(e,t,n){var o,i,r=-1,a=t[1],s=0,u=7,c=4;for(0===a&&(u=138,c=3),o=0;o<=n;o++)if(i=a,a=t[2*(o+1)+1],!(++s<u&&i===a)){if(s<c)do{Xi(e,i,e.bl_tree)}while(0!=--s);else 0!==i?(i!==r&&(Xi(e,i,e.bl_tree),s--),Xi(e,16,e.bl_tree),Ki(e,s-3,2)):s<=10?(Xi(e,17,e.bl_tree),Ki(e,s-3,3)):(Xi(e,18,e.bl_tree),Ki(e,s-11,7));s=0,r=i,0===a?(u=138,c=3):i===a?(u=6,c=3):(u=7,c=4)}}Ii(Ni);var sr=!1;function ur(e,t,n,o){Ki(e,0+(o?1:0),3),function(e,t,n,o){er(e),o&&(Vi(e,n),Vi(e,~n)),qi.arraySet(e.pending_buf,e.window,t,n,e.pending),e.pending+=n}(e,t,n,!0)}var cr={_tr_init:function(e){sr||(!function(){var e,t,n,o,i,r=new Array(16);for(n=0,o=0;o<28;o++)for(Bi[o]=n,e=0;e<1<<Oi[o];e++)Fi[n++]=o;for(Fi[n-1]=o,i=0,o=0;o<16;o++)for(Ni[o]=i,e=0;e<1<<Ti[o];e++)Mi[i++]=o;for(i>>=7;o<zi;o++)for(Ni[o]=i<<7,e=0;e<1<<Ti[o]-7;e++)Mi[256+i++]=o;for(t=0;t<=Ei;t++)r[t]=0;for(e=0;e<=143;)Ai[2*e+1]=8,e++,r[8]++;for(;e<=255;)Ai[2*e+1]=9,e++,r[9]++;for(;e<=279;)Ai[2*e+1]=7,e++,r[7]++;for(;e<=287;)Ai[2*e+1]=8,e++,r[8]++;for($i(Ai,287,r),e=0;e<zi;e++)Ui[2*e+1]=5,Ui[2*e]=Yi(e,5);Li=new Zi(Ai,Oi,257,xi,Ei),Ji=new Zi(Ui,Ti,0,zi,Ei),ji=new Zi(new Array(0),Wi,0,19,7)}(),sr=!0),e.l_desc=new Hi(e.dyn_ltree,Li),e.d_desc=new Hi(e.dyn_dtree,Ji),e.bl_desc=new Hi(e.bl_tree,ji),e.bi_buf=0,e.bi_valid=0,Qi(e)},_tr_stored_block:ur,_tr_flush_block:function(e,t,n,o){var i,r,a=0;e.level>0?(2===e.strm.data_type&&(e.strm.data_type=function(e){var t,n=4093624447;for(t=0;t<=31;t++,n>>>=1)if(1&n&&0!==e.dyn_ltree[2*t])return 0;if(0!==e.dyn_ltree[18]||0!==e.dyn_ltree[20]||0!==e.dyn_ltree[26])return 1;for(t=32;t<Pi;t++)if(0!==e.dyn_ltree[2*t])return 1;return 0}(e)),ir(e,e.l_desc),ir(e,e.d_desc),a=function(e){var t;for(rr(e,e.dyn_ltree,e.l_desc.max_code),rr(e,e.dyn_dtree,e.d_desc.max_code),ir(e,e.bl_desc),t=18;t>=3&&0===e.bl_tree[2*Di[t]+1];t--);return e.opt_len+=3*(t+1)+5+5+4,t}(e),i=e.opt_len+3+7>>>3,(r=e.static_len+3+7>>>3)<=i&&(i=r)):i=r=n+5,n+4<=i&&-1!==t?ur(e,t,n,o):4===e.strategy||r===i?(Ki(e,2+(o?1:0),3),or(e,Ai,Ui)):(Ki(e,4+(o?1:0),3),function(e,t,n,o){var i;for(Ki(e,t-257,5),Ki(e,n-1,5),Ki(e,o-4,4),i=0;i<o;i++)Ki(e,e.bl_tree[2*Di[i]+1],3);ar(e,e.dyn_ltree,t-1),ar(e,e.dyn_dtree,n-1)}(e,e.l_desc.max_code+1,e.d_desc.max_code+1,a+1),or(e,e.dyn_ltree,e.dyn_dtree)),Qi(e),o&&er(e)},_tr_tally:function(e,t,n){return e.pending_buf[e.d_buf+2*e.last_lit]=t>>>8&255,e.pending_buf[e.d_buf+2*e.last_lit+1]=255&t,e.pending_buf[e.l_buf+e.last_lit]=255&n,e.last_lit++,0===t?e.dyn_ltree[2*n]++:(e.matches++,t--,e.dyn_ltree[2*(Fi[n]+Pi+1)]++,e.dyn_dtree[2*Gi(t)]++),e.last_lit===e.lit_bufsize-1},_tr_align:function(e){Ki(e,2,3),Xi(e,256,Ai),function(e){16===e.bi_valid?(Vi(e,e.bi_buf),e.bi_buf=0,e.bi_valid=0):e.bi_valid>=8&&(e.pending_buf[e.pending++]=255&e.bi_buf,e.bi_buf>>=8,e.bi_valid-=8)}(e)}};var lr=function(e,t,n,o){for(var i=65535&e|0,r=e>>>16&65535|0,a=0;0!==n;){n-=a=n>2e3?2e3:n;do{r=r+(i=i+t[o++]|0)|0}while(--a);i%=65521,r%=65521}return i|r<<16|0};var dr=function(){for(var e,t=[],n=0;n<256;n++){e=n;for(var o=0;o<8;o++)e=1&e?3988292384^e>>>1:e>>>1;t[n]=e}return t}();var fr,hr=function(e,t,n,o){var i=dr,r=o+n;e^=-1;for(var a=o;a<r;a++)e=e>>>8^i[255&(e^t[a])];return-1^e},pr={2:"need dictionary",1:"stream end",0:"","-1":"file error","-2":"stream error","-3":"data error","-4":"insufficient memory","-5":"buffer error","-6":"incompatible version"},vr=-2,br=258,mr=262,gr=103,yr=113,_r=666;function wr(e,t){return e.msg=pr[t],t}function kr(e){return(e<<1)-(e>4?9:0)}function Cr(e){for(var t=e.length;--t>=0;)e[t]=0}function Sr(e){var t=e.state,n=t.pending;n>e.avail_out&&(n=e.avail_out),0!==n&&(qi.arraySet(e.output,t.pending_buf,t.pending_out,n,e.next_out),e.next_out+=n,t.pending_out+=n,e.total_out+=n,e.avail_out-=n,t.pending-=n,0===t.pending&&(t.pending_out=0))}function Rr(e,t){cr._tr_flush_block(e,e.block_start>=0?e.block_start:-1,e.strstart-e.block_start,t),e.block_start=e.strstart,Sr(e.strm)}function qr(e,t){e.pending_buf[e.pending++]=t}function Ir(e,t){e.pending_buf[e.pending++]=t>>>8&255,e.pending_buf[e.pending++]=255&t}function Pr(e,t){var n,o,i=e.max_chain_length,r=e.strstart,a=e.prev_length,s=e.nice_match,u=e.strstart>e.w_size-mr?e.strstart-(e.w_size-mr):0,c=e.window,l=e.w_mask,d=e.prev,f=e.strstart+br,h=c[r+a-1],p=c[r+a];e.prev_length>=e.good_match&&(i>>=2),s>e.lookahead&&(s=e.lookahead);do{if(c[(n=t)+a]===p&&c[n+a-1]===h&&c[n]===c[r]&&c[++n]===c[r+1]){r+=2,n++;do{}while(c[++r]===c[++n]&&c[++r]===c[++n]&&c[++r]===c[++n]&&c[++r]===c[++n]&&c[++r]===c[++n]&&c[++r]===c[++n]&&c[++r]===c[++n]&&c[++r]===c[++n]&&r<f);if(o=br-(f-r),r=f-br,o>a){if(e.match_start=t,a=o,o>=s)break;h=c[r+a-1],p=c[r+a]}}}while((t=d[t&l])>u&&0!=--i);return a<=e.lookahead?a:e.lookahead}function xr(e){var t,n,o,i,r,a,s,u,c,l,d=e.w_size;do{if(i=e.window_size-e.lookahead-e.strstart,e.strstart>=d+(d-mr)){qi.arraySet(e.window,e.window,d,d,0),e.match_start-=d,e.strstart-=d,e.block_start-=d,t=n=e.hash_size;do{o=e.head[--t],e.head[t]=o>=d?o-d:0}while(--n);t=n=d;do{o=e.prev[--t],e.prev[t]=o>=d?o-d:0}while(--n);i+=d}if(0===e.strm.avail_in)break;if(a=e.strm,s=e.window,u=e.strstart+e.lookahead,c=i,l=void 0,(l=a.avail_in)>c&&(l=c),n=0===l?0:(a.avail_in-=l,qi.arraySet(s,a.input,a.next_in,l,u),1===a.state.wrap?a.adler=lr(a.adler,s,l,u):2===a.state.wrap&&(a.adler=hr(a.adler,s,l,u)),a.next_in+=l,a.total_in+=l,l),e.lookahead+=n,e.lookahead+e.insert>=3)for(r=e.strstart-e.insert,e.ins_h=e.window[r],e.ins_h=(e.ins_h<<e.hash_shift^e.window[r+1])&e.hash_mask;e.insert&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[r+3-1])&e.hash_mask,e.prev[r&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=r,r++,e.insert--,!(e.lookahead+e.insert<3)););}while(e.lookahead<mr&&0!==e.strm.avail_in)}function zr(e,t){for(var n,o;;){if(e.lookahead<mr){if(xr(e),e.lookahead<mr&&0===t)return 1;if(0===e.lookahead)break}if(n=0,e.lookahead>=3&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+3-1])&e.hash_mask,n=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart),0!==n&&e.strstart-n<=e.w_size-mr&&(e.match_length=Pr(e,n)),e.match_length>=3)if(o=cr._tr_tally(e,e.strstart-e.match_start,e.match_length-3),e.lookahead-=e.match_length,e.match_length<=e.max_lazy_match&&e.lookahead>=3){e.match_length--;do{e.strstart++,e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+3-1])&e.hash_mask,n=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart}while(0!=--e.match_length);e.strstart++}else e.strstart+=e.match_length,e.match_length=0,e.ins_h=e.window[e.strstart],e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+1])&e.hash_mask;else o=cr._tr_tally(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++;if(o&&(Rr(e,!1),0===e.strm.avail_out))return 1}return e.insert=e.strstart<2?e.strstart:2,4===t?(Rr(e,!0),0===e.strm.avail_out?3:4):e.last_lit&&(Rr(e,!1),0===e.strm.avail_out)?1:2}function Er(e,t){for(var n,o,i;;){if(e.lookahead<mr){if(xr(e),e.lookahead<mr&&0===t)return 1;if(0===e.lookahead)break}if(n=0,e.lookahead>=3&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+3-1])&e.hash_mask,n=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart),e.prev_length=e.match_length,e.prev_match=e.match_start,e.match_length=2,0!==n&&e.prev_length<e.max_lazy_match&&e.strstart-n<=e.w_size-mr&&(e.match_length=Pr(e,n),e.match_length<=5&&(1===e.strategy||3===e.match_length&&e.strstart-e.match_start>4096)&&(e.match_length=2)),e.prev_length>=3&&e.match_length<=e.prev_length){i=e.strstart+e.lookahead-3,o=cr._tr_tally(e,e.strstart-1-e.prev_match,e.prev_length-3),e.lookahead-=e.prev_length-1,e.prev_length-=2;do{++e.strstart<=i&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+3-1])&e.hash_mask,n=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart)}while(0!=--e.prev_length);if(e.match_available=0,e.match_length=2,e.strstart++,o&&(Rr(e,!1),0===e.strm.avail_out))return 1}else if(e.match_available){if((o=cr._tr_tally(e,0,e.window[e.strstart-1]))&&Rr(e,!1),e.strstart++,e.lookahead--,0===e.strm.avail_out)return 1}else e.match_available=1,e.strstart++,e.lookahead--}return e.match_available&&(o=cr._tr_tally(e,0,e.window[e.strstart-1]),e.match_available=0),e.insert=e.strstart<2?e.strstart:2,4===t?(Rr(e,!0),0===e.strm.avail_out?3:4):e.last_lit&&(Rr(e,!1),0===e.strm.avail_out)?1:2}function Or(e,t,n,o,i){this.good_length=e,this.max_lazy=t,this.nice_length=n,this.max_chain=o,this.func=i}function Tr(){this.strm=null,this.status=0,this.pending_buf=null,this.pending_buf_size=0,this.pending_out=0,this.pending=0,this.wrap=0,this.gzhead=null,this.gzindex=0,this.method=8,this.last_flush=-1,this.w_size=0,this.w_bits=0,this.w_mask=0,this.window=null,this.window_size=0,this.prev=null,this.head=null,this.ins_h=0,this.hash_size=0,this.hash_bits=0,this.hash_mask=0,this.hash_shift=0,this.block_start=0,this.match_length=0,this.prev_match=0,this.match_available=0,this.strstart=0,this.match_start=0,this.lookahead=0,this.prev_length=0,this.max_chain_length=0,this.max_lazy_match=0,this.level=0,this.strategy=0,this.good_match=0,this.nice_match=0,this.dyn_ltree=new qi.Buf16(1146),this.dyn_dtree=new qi.Buf16(122),this.bl_tree=new qi.Buf16(78),Cr(this.dyn_ltree),Cr(this.dyn_dtree),Cr(this.bl_tree),this.l_desc=null,this.d_desc=null,this.bl_desc=null,this.bl_count=new qi.Buf16(16),this.heap=new qi.Buf16(573),Cr(this.heap),this.heap_len=0,this.heap_max=0,this.depth=new qi.Buf16(573),Cr(this.depth),this.l_buf=0,this.lit_bufsize=0,this.last_lit=0,this.d_buf=0,this.opt_len=0,this.static_len=0,this.matches=0,this.insert=0,this.bi_buf=0,this.bi_valid=0}function Wr(e){var t;return e&&e.state?(e.total_in=e.total_out=0,e.data_type=2,(t=e.state).pending=0,t.pending_out=0,t.wrap<0&&(t.wrap=-t.wrap),t.status=t.wrap?42:yr,e.adler=2===t.wrap?0:1,t.last_flush=0,cr._tr_init(t),0):wr(e,vr)}function Dr(e){var t,n=Wr(e);return 0===n&&((t=e.state).window_size=2*t.w_size,Cr(t.head),t.max_lazy_match=fr[t.level].max_lazy,t.good_match=fr[t.level].good_length,t.nice_match=fr[t.level].nice_length,t.max_chain_length=fr[t.level].max_chain,t.strstart=0,t.block_start=0,t.lookahead=0,t.insert=0,t.match_length=t.prev_length=2,t.match_available=0,t.ins_h=0),n}function Ar(e,t,n,o,i,r){if(!e)return vr;var a=1;if(-1===t&&(t=6),o<0?(a=0,o=-o):o>15&&(a=2,o-=16),i<1||i>9||8!==n||o<8||o>15||t<0||t>9||r<0||r>4)return wr(e,vr);8===o&&(o=9);var s=new Tr;return e.state=s,s.strm=e,s.wrap=a,s.gzhead=null,s.w_bits=o,s.w_size=1<<s.w_bits,s.w_mask=s.w_size-1,s.hash_bits=i+7,s.hash_size=1<<s.hash_bits,s.hash_mask=s.hash_size-1,s.hash_shift=~~((s.hash_bits+3-1)/3),s.window=new qi.Buf8(2*s.w_size),s.head=new qi.Buf16(s.hash_size),s.prev=new qi.Buf16(s.w_size),s.lit_bufsize=1<<i+6,s.pending_buf_size=4*s.lit_bufsize,s.pending_buf=new qi.Buf8(s.pending_buf_size),s.d_buf=1*s.lit_bufsize,s.l_buf=3*s.lit_bufsize,s.level=t,s.strategy=r,s.method=n,Dr(e)}fr=[new Or(0,0,0,0,(function(e,t){var n=65535;for(n>e.pending_buf_size-5&&(n=e.pending_buf_size-5);;){if(e.lookahead<=1){if(xr(e),0===e.lookahead&&0===t)return 1;if(0===e.lookahead)break}e.strstart+=e.lookahead,e.lookahead=0;var o=e.block_start+n;if((0===e.strstart||e.strstart>=o)&&(e.lookahead=e.strstart-o,e.strstart=o,Rr(e,!1),0===e.strm.avail_out))return 1;if(e.strstart-e.block_start>=e.w_size-mr&&(Rr(e,!1),0===e.strm.avail_out))return 1}return e.insert=0,4===t?(Rr(e,!0),0===e.strm.avail_out?3:4):(e.strstart>e.block_start&&(Rr(e,!1),e.strm.avail_out),1)})),new Or(4,4,8,4,zr),new Or(4,5,16,8,zr),new Or(4,6,32,32,zr),new Or(4,4,16,16,Er),new Or(8,16,32,32,Er),new Or(8,16,128,128,Er),new Or(8,32,128,256,Er),new Or(32,128,258,1024,Er),new Or(32,258,258,4096,Er)];var Ur={deflateInit:function(e,t){return Ar(e,t,8,15,8,0)},deflateInit2:Ar,deflateReset:Dr,deflateResetKeep:Wr,deflateSetHeader:function(e,t){return e&&e.state?2!==e.state.wrap?vr:(e.state.gzhead=t,0):vr},deflate:function(e,t){var n,o,i,r;if(!e||!e.state||t>5||t<0)return e?wr(e,vr):vr;if(o=e.state,!e.output||!e.input&&0!==e.avail_in||o.status===_r&&4!==t)return wr(e,0===e.avail_out?-5:vr);if(o.strm=e,n=o.last_flush,o.last_flush=t,42===o.status)if(2===o.wrap)e.adler=0,qr(o,31),qr(o,139),qr(o,8),o.gzhead?(qr(o,(o.gzhead.text?1:0)+(o.gzhead.hcrc?2:0)+(o.gzhead.extra?4:0)+(o.gzhead.name?8:0)+(o.gzhead.comment?16:0)),qr(o,255&o.gzhead.time),qr(o,o.gzhead.time>>8&255),qr(o,o.gzhead.time>>16&255),qr(o,o.gzhead.time>>24&255),qr(o,9===o.level?2:o.strategy>=2||o.level<2?4:0),qr(o,255&o.gzhead.os),o.gzhead.extra&&o.gzhead.extra.length&&(qr(o,255&o.gzhead.extra.length),qr(o,o.gzhead.extra.length>>8&255)),o.gzhead.hcrc&&(e.adler=hr(e.adler,o.pending_buf,o.pending,0)),o.gzindex=0,o.status=69):(qr(o,0),qr(o,0),qr(o,0),qr(o,0),qr(o,0),qr(o,9===o.level?2:o.strategy>=2||o.level<2?4:0),qr(o,3),o.status=yr);else{var a=8+(o.w_bits-8<<4)<<8;a|=(o.strategy>=2||o.level<2?0:o.level<6?1:6===o.level?2:3)<<6,0!==o.strstart&&(a|=32),a+=31-a%31,o.status=yr,Ir(o,a),0!==o.strstart&&(Ir(o,e.adler>>>16),Ir(o,65535&e.adler)),e.adler=1}if(69===o.status)if(o.gzhead.extra){for(i=o.pending;o.gzindex<(65535&o.gzhead.extra.length)&&(o.pending!==o.pending_buf_size||(o.gzhead.hcrc&&o.pending>i&&(e.adler=hr(e.adler,o.pending_buf,o.pending-i,i)),Sr(e),i=o.pending,o.pending!==o.pending_buf_size));)qr(o,255&o.gzhead.extra[o.gzindex]),o.gzindex++;o.gzhead.hcrc&&o.pending>i&&(e.adler=hr(e.adler,o.pending_buf,o.pending-i,i)),o.gzindex===o.gzhead.extra.length&&(o.gzindex=0,o.status=73)}else o.status=73;if(73===o.status)if(o.gzhead.name){i=o.pending;do{if(o.pending===o.pending_buf_size&&(o.gzhead.hcrc&&o.pending>i&&(e.adler=hr(e.adler,o.pending_buf,o.pending-i,i)),Sr(e),i=o.pending,o.pending===o.pending_buf_size)){r=1;break}r=o.gzindex<o.gzhead.name.length?255&o.gzhead.name.charCodeAt(o.gzindex++):0,qr(o,r)}while(0!==r);o.gzhead.hcrc&&o.pending>i&&(e.adler=hr(e.adler,o.pending_buf,o.pending-i,i)),0===r&&(o.gzindex=0,o.status=91)}else o.status=91;if(91===o.status)if(o.gzhead.comment){i=o.pending;do{if(o.pending===o.pending_buf_size&&(o.gzhead.hcrc&&o.pending>i&&(e.adler=hr(e.adler,o.pending_buf,o.pending-i,i)),Sr(e),i=o.pending,o.pending===o.pending_buf_size)){r=1;break}r=o.gzindex<o.gzhead.comment.length?255&o.gzhead.comment.charCodeAt(o.gzindex++):0,qr(o,r)}while(0!==r);o.gzhead.hcrc&&o.pending>i&&(e.adler=hr(e.adler,o.pending_buf,o.pending-i,i)),0===r&&(o.status=gr)}else o.status=gr;if(o.status===gr&&(o.gzhead.hcrc?(o.pending+2>o.pending_buf_size&&Sr(e),o.pending+2<=o.pending_buf_size&&(qr(o,255&e.adler),qr(o,e.adler>>8&255),e.adler=0,o.status=yr)):o.status=yr),0!==o.pending){if(Sr(e),0===e.avail_out)return o.last_flush=-1,0}else if(0===e.avail_in&&kr(t)<=kr(n)&&4!==t)return wr(e,-5);if(o.status===_r&&0!==e.avail_in)return wr(e,-5);if(0!==e.avail_in||0!==o.lookahead||0!==t&&o.status!==_r){var s=2===o.strategy?function(e,t){for(var n;;){if(0===e.lookahead&&(xr(e),0===e.lookahead)){if(0===t)return 1;break}if(e.match_length=0,n=cr._tr_tally(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++,n&&(Rr(e,!1),0===e.strm.avail_out))return 1}return e.insert=0,4===t?(Rr(e,!0),0===e.strm.avail_out?3:4):e.last_lit&&(Rr(e,!1),0===e.strm.avail_out)?1:2}(o,t):3===o.strategy?function(e,t){for(var n,o,i,r,a=e.window;;){if(e.lookahead<=br){if(xr(e),e.lookahead<=br&&0===t)return 1;if(0===e.lookahead)break}if(e.match_length=0,e.lookahead>=3&&e.strstart>0&&(o=a[i=e.strstart-1])===a[++i]&&o===a[++i]&&o===a[++i]){r=e.strstart+br;do{}while(o===a[++i]&&o===a[++i]&&o===a[++i]&&o===a[++i]&&o===a[++i]&&o===a[++i]&&o===a[++i]&&o===a[++i]&&i<r);e.match_length=br-(r-i),e.match_length>e.lookahead&&(e.match_length=e.lookahead)}if(e.match_length>=3?(n=cr._tr_tally(e,1,e.match_length-3),e.lookahead-=e.match_length,e.strstart+=e.match_length,e.match_length=0):(n=cr._tr_tally(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++),n&&(Rr(e,!1),0===e.strm.avail_out))return 1}return e.insert=0,4===t?(Rr(e,!0),0===e.strm.avail_out?3:4):e.last_lit&&(Rr(e,!1),0===e.strm.avail_out)?1:2}(o,t):fr[o.level].func(o,t);if(3!==s&&4!==s||(o.status=_r),1===s||3===s)return 0===e.avail_out&&(o.last_flush=-1),0;if(2===s&&(1===t?cr._tr_align(o):5!==t&&(cr._tr_stored_block(o,0,0,!1),3===t&&(Cr(o.head),0===o.lookahead&&(o.strstart=0,o.block_start=0,o.insert=0))),Sr(e),0===e.avail_out))return o.last_flush=-1,0}return 4!==t?0:o.wrap<=0?1:(2===o.wrap?(qr(o,255&e.adler),qr(o,e.adler>>8&255),qr(o,e.adler>>16&255),qr(o,e.adler>>24&255),qr(o,255&e.total_in),qr(o,e.total_in>>8&255),qr(o,e.total_in>>16&255),qr(o,e.total_in>>24&255)):(Ir(o,e.adler>>>16),Ir(o,65535&e.adler)),Sr(e),o.wrap>0&&(o.wrap=-o.wrap),0!==o.pending?0:1)},deflateEnd:function(e){var t;return e&&e.state?42!==(t=e.state.status)&&69!==t&&73!==t&&91!==t&&t!==gr&&t!==yr&&t!==_r?wr(e,vr):(e.state=null,t===yr?wr(e,-3):0):vr},deflateSetDictionary:function(e,t){var n,o,i,r,a,s,u,c,l=t.length;if(!e||!e.state)return vr;if(2===(r=(n=e.state).wrap)||1===r&&42!==n.status||n.lookahead)return vr;for(1===r&&(e.adler=lr(e.adler,t,l,0)),n.wrap=0,l>=n.w_size&&(0===r&&(Cr(n.head),n.strstart=0,n.block_start=0,n.insert=0),c=new qi.Buf8(n.w_size),qi.arraySet(c,t,l-n.w_size,n.w_size,0),t=c,l=n.w_size),a=e.avail_in,s=e.next_in,u=e.input,e.avail_in=l,e.next_in=0,e.input=t,xr(n);n.lookahead>=3;){o=n.strstart,i=n.lookahead-2;do{n.ins_h=(n.ins_h<<n.hash_shift^n.window[o+3-1])&n.hash_mask,n.prev[o&n.w_mask]=n.head[n.ins_h],n.head[n.ins_h]=o,o++}while(--i);n.strstart=o,n.lookahead=2,xr(n)}return n.strstart+=n.lookahead,n.block_start=n.strstart,n.insert=n.lookahead,n.lookahead=0,n.match_length=n.prev_length=2,n.match_available=0,e.next_in=s,e.input=u,e.avail_in=a,n.wrap=r,0},deflateInfo:"pako deflate (from Nodeca project)"},Mr=!0,Fr=!0;try{String.fromCharCode.apply(null,[0])}catch(e){Mr=!1}try{String.fromCharCode.apply(null,new Uint8Array(1))}catch(e){Fr=!1}for(var Br=new qi.Buf8(256),Lr=0;Lr<256;Lr++)Br[Lr]=Lr>=252?6:Lr>=248?5:Lr>=240?4:Lr>=224?3:Lr>=192?2:1;Br[254]=Br[254]=1;function Jr(e,t){if(t<65534&&(e.subarray&&Fr||!e.subarray&&Mr))return String.fromCharCode.apply(null,qi.shrinkBuf(e,t));for(var n="",o=0;o<t;o++)n+=String.fromCharCode(e[o]);return n}var jr=function(e){var t,n,o,i,r,a=e.length,s=0;for(i=0;i<a;i++)55296==(64512&(n=e.charCodeAt(i)))&&i+1<a&&56320==(64512&(o=e.charCodeAt(i+1)))&&(n=65536+(n-55296<<10)+(o-56320),i++),s+=n<128?1:n<2048?2:n<65536?3:4;for(t=new qi.Buf8(s),r=0,i=0;r<s;i++)55296==(64512&(n=e.charCodeAt(i)))&&i+1<a&&56320==(64512&(o=e.charCodeAt(i+1)))&&(n=65536+(n-55296<<10)+(o-56320),i++),n<128?t[r++]=n:n<2048?(t[r++]=192|n>>>6,t[r++]=128|63&n):n<65536?(t[r++]=224|n>>>12,t[r++]=128|n>>>6&63,t[r++]=128|63&n):(t[r++]=240|n>>>18,t[r++]=128|n>>>12&63,t[r++]=128|n>>>6&63,t[r++]=128|63&n);return t},Nr=function(e){return Jr(e,e.length)},Zr=function(e){for(var t=new qi.Buf8(e.length),n=0,o=t.length;n<o;n++)t[n]=e.charCodeAt(n);return t},Hr=function(e,t){var n,o,i,r,a=t||e.length,s=new Array(2*a);for(o=0,n=0;n<a;)if((i=e[n++])<128)s[o++]=i;else if((r=Br[i])>4)s[o++]=65533,n+=r-1;else{for(i&=2===r?31:3===r?15:7;r>1&&n<a;)i=i<<6|63&e[n++],r--;r>1?s[o++]=65533:i<65536?s[o++]=i:(i-=65536,s[o++]=55296|i>>10&1023,s[o++]=56320|1023&i)}return Jr(s,o)},Gr=function(e,t){var n;for((t=t||e.length)>e.length&&(t=e.length),n=t-1;n>=0&&128==(192&e[n]);)n--;return n<0||0===n?t:n+Br[e[n]]>t?n:t};var Vr=function(){this.input=null,this.next_in=0,this.avail_in=0,this.total_in=0,this.output=null,this.next_out=0,this.avail_out=0,this.total_out=0,this.msg="",this.state=null,this.data_type=2,this.adler=0},Kr=Object.prototype.toString;function Xr(e){if(!(this instanceof Xr))return new Xr(e);this.options=qi.assign({level:-1,method:8,chunkSize:16384,windowBits:15,memLevel:8,strategy:0,to:""},e||{});var t=this.options;t.raw&&t.windowBits>0?t.windowBits=-t.windowBits:t.gzip&&t.windowBits>0&&t.windowBits<16&&(t.windowBits+=16),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new Vr,this.strm.avail_out=0;var n=Ur.deflateInit2(this.strm,t.level,t.method,t.windowBits,t.memLevel,t.strategy);if(0!==n)throw new Error(pr[n]);if(t.header&&Ur.deflateSetHeader(this.strm,t.header),t.dictionary){var o;if(o="string"==typeof t.dictionary?jr(t.dictionary):"[object ArrayBuffer]"===Kr.call(t.dictionary)?new Uint8Array(t.dictionary):t.dictionary,0!==(n=Ur.deflateSetDictionary(this.strm,o)))throw new Error(pr[n]);this._dict_set=!0}}function Yr(e,t){var n=new Xr(t);if(n.push(e,!0),n.err)throw n.msg||pr[n.err];return n.result}Xr.prototype.push=function(e,t){var n,o,i=this.strm,r=this.options.chunkSize;if(this.ended)return!1;o=t===~~t?t:!0===t?4:0,"string"==typeof e?i.input=jr(e):"[object ArrayBuffer]"===Kr.call(e)?i.input=new Uint8Array(e):i.input=e,i.next_in=0,i.avail_in=i.input.length;do{if(0===i.avail_out&&(i.output=new qi.Buf8(r),i.next_out=0,i.avail_out=r),1!==(n=Ur.deflate(i,o))&&0!==n)return this.onEnd(n),this.ended=!0,!1;0!==i.avail_out&&(0!==i.avail_in||4!==o&&2!==o)||("string"===this.options.to?this.onData(Nr(qi.shrinkBuf(i.output,i.next_out))):this.onData(qi.shrinkBuf(i.output,i.next_out)))}while((i.avail_in>0||0===i.avail_out)&&1!==n);return 4===o?(n=Ur.deflateEnd(this.strm),this.onEnd(n),this.ended=!0,0===n):2!==o||(this.onEnd(0),i.avail_out=0,!0)},Xr.prototype.onData=function(e){this.chunks.push(e)},Xr.prototype.onEnd=function(e){0===e&&("string"===this.options.to?this.result=this.chunks.join(""):this.result=qi.flattenChunks(this.chunks)),this.chunks=[],this.err=e,this.msg=this.strm.msg};var $r={Deflate:Xr,deflate:Yr,deflateRaw:function(e,t){return(t=t||{}).raw=!0,Yr(e,t)},gzip:function(e,t){return(t=t||{}).gzip=!0,Yr(e,t)}},Qr=function(e,t){var n,o,i,r,a,s,u,c,l,d,f,h,p,v,b,m,g,y,_,w,k,C,S,R,q;n=e.state,o=e.next_in,R=e.input,i=o+(e.avail_in-5),r=e.next_out,q=e.output,a=r-(t-e.avail_out),s=r+(e.avail_out-257),u=n.dmax,c=n.wsize,l=n.whave,d=n.wnext,f=n.window,h=n.hold,p=n.bits,v=n.lencode,b=n.distcode,m=(1<<n.lenbits)-1,g=(1<<n.distbits)-1;e:do{p<15&&(h+=R[o++]<<p,p+=8,h+=R[o++]<<p,p+=8),y=v[h&m];t:for(;;){if(h>>>=_=y>>>24,p-=_,0===(_=y>>>16&255))q[r++]=65535&y;else{if(!(16&_)){if(0==(64&_)){y=v[(65535&y)+(h&(1<<_)-1)];continue t}if(32&_){n.mode=12;break e}e.msg="invalid literal/length code",n.mode=30;break e}w=65535&y,(_&=15)&&(p<_&&(h+=R[o++]<<p,p+=8),w+=h&(1<<_)-1,h>>>=_,p-=_),p<15&&(h+=R[o++]<<p,p+=8,h+=R[o++]<<p,p+=8),y=b[h&g];n:for(;;){if(h>>>=_=y>>>24,p-=_,!(16&(_=y>>>16&255))){if(0==(64&_)){y=b[(65535&y)+(h&(1<<_)-1)];continue n}e.msg="invalid distance code",n.mode=30;break e}if(k=65535&y,p<(_&=15)&&(h+=R[o++]<<p,(p+=8)<_&&(h+=R[o++]<<p,p+=8)),(k+=h&(1<<_)-1)>u){e.msg="invalid distance too far back",n.mode=30;break e}if(h>>>=_,p-=_,k>(_=r-a)){if((_=k-_)>l&&n.sane){e.msg="invalid distance too far back",n.mode=30;break e}if(C=0,S=f,0===d){if(C+=c-_,_<w){w-=_;do{q[r++]=f[C++]}while(--_);C=r-k,S=q}}else if(d<_){if(C+=c+d-_,(_-=d)<w){w-=_;do{q[r++]=f[C++]}while(--_);if(C=0,d<w){w-=_=d;do{q[r++]=f[C++]}while(--_);C=r-k,S=q}}}else if(C+=d-_,_<w){w-=_;do{q[r++]=f[C++]}while(--_);C=r-k,S=q}for(;w>2;)q[r++]=S[C++],q[r++]=S[C++],q[r++]=S[C++],w-=3;w&&(q[r++]=S[C++],w>1&&(q[r++]=S[C++]))}else{C=r-k;do{q[r++]=q[C++],q[r++]=q[C++],q[r++]=q[C++],w-=3}while(w>2);w&&(q[r++]=q[C++],w>1&&(q[r++]=q[C++]))}break}}break}}while(o<i&&r<s);o-=w=p>>3,h&=(1<<(p-=w<<3))-1,e.next_in=o,e.next_out=r,e.avail_in=o<i?i-o+5:5-(o-i),e.avail_out=r<s?s-r+257:257-(r-s),n.hold=h,n.bits=p},ea=15,ta=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,0,0],na=[16,16,16,16,16,16,16,16,17,17,17,17,18,18,18,18,19,19,19,19,20,20,20,20,21,21,21,21,16,72,78],oa=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,0,0],ia=[16,16,16,16,17,17,18,18,19,19,20,20,21,21,22,22,23,23,24,24,25,25,26,26,27,27,28,28,29,29,64,64],ra=function(e,t,n,o,i,r,a,s){var u,c,l,d,f,h,p,v,b,m=s.bits,g=0,y=0,_=0,w=0,k=0,C=0,S=0,R=0,q=0,I=0,P=null,x=0,z=new qi.Buf16(16),E=new qi.Buf16(16),O=null,T=0;for(g=0;g<=ea;g++)z[g]=0;for(y=0;y<o;y++)z[t[n+y]]++;for(k=m,w=ea;w>=1&&0===z[w];w--);if(k>w&&(k=w),0===w)return i[r++]=20971520,i[r++]=20971520,s.bits=1,0;for(_=1;_<w&&0===z[_];_++);for(k<_&&(k=_),R=1,g=1;g<=ea;g++)if(R<<=1,(R-=z[g])<0)return-1;if(R>0&&(0===e||1!==w))return-1;for(E[1]=0,g=1;g<ea;g++)E[g+1]=E[g]+z[g];for(y=0;y<o;y++)0!==t[n+y]&&(a[E[t[n+y]]++]=y);if(0===e?(P=O=a,h=19):1===e?(P=ta,x-=257,O=na,T-=257,h=256):(P=oa,O=ia,h=-1),I=0,y=0,g=_,f=r,C=k,S=0,l=-1,d=(q=1<<k)-1,1===e&&q>852||2===e&&q>592)return 1;for(;;){p=g-S,a[y]<h?(v=0,b=a[y]):a[y]>h?(v=O[T+a[y]],b=P[x+a[y]]):(v=96,b=0),u=1<<g-S,_=c=1<<C;do{i[f+(I>>S)+(c-=u)]=p<<24|v<<16|b|0}while(0!==c);for(u=1<<g-1;I&u;)u>>=1;if(0!==u?(I&=u-1,I+=u):I=0,y++,0==--z[g]){if(g===w)break;g=t[n+a[y]]}if(g>k&&(I&d)!==l){for(0===S&&(S=k),f+=_,R=1<<(C=g-S);C+S<w&&!((R-=z[C+S])<=0);)C++,R<<=1;if(q+=1<<C,1===e&&q>852||2===e&&q>592)return 1;i[l=I&d]=k<<24|C<<16|f-r|0}}return 0!==I&&(i[f+I]=g-S<<24|64<<16|0),s.bits=k,0},aa=-2,sa=12,ua=30;function ca(e){return(e>>>24&255)+(e>>>8&65280)+((65280&e)<<8)+((255&e)<<24)}function la(){this.mode=0,this.last=!1,this.wrap=0,this.havedict=!1,this.flags=0,this.dmax=0,this.check=0,this.total=0,this.head=null,this.wbits=0,this.wsize=0,this.whave=0,this.wnext=0,this.window=null,this.hold=0,this.bits=0,this.length=0,this.offset=0,this.extra=0,this.lencode=null,this.distcode=null,this.lenbits=0,this.distbits=0,this.ncode=0,this.nlen=0,this.ndist=0,this.have=0,this.next=null,this.lens=new qi.Buf16(320),this.work=new qi.Buf16(288),this.lendyn=null,this.distdyn=null,this.sane=0,this.back=0,this.was=0}function da(e){var t;return e&&e.state?(t=e.state,e.total_in=e.total_out=t.total=0,e.msg="",t.wrap&&(e.adler=1&t.wrap),t.mode=1,t.last=0,t.havedict=0,t.dmax=32768,t.head=null,t.hold=0,t.bits=0,t.lencode=t.lendyn=new qi.Buf32(852),t.distcode=t.distdyn=new qi.Buf32(592),t.sane=1,t.back=-1,0):aa}function fa(e){var t;return e&&e.state?((t=e.state).wsize=0,t.whave=0,t.wnext=0,da(e)):aa}function ha(e,t){var n,o;return e&&e.state?(o=e.state,t<0?(n=0,t=-t):(n=1+(t>>4),t<48&&(t&=15)),t&&(t<8||t>15)?aa:(null!==o.window&&o.wbits!==t&&(o.window=null),o.wrap=n,o.wbits=t,fa(e))):aa}function pa(e,t){var n,o;return e?(o=new la,e.state=o,o.window=null,0!==(n=ha(e,t))&&(e.state=null),n):aa}var va,ba,ma=!0;function ga(e){if(ma){var t;for(va=new qi.Buf32(512),ba=new qi.Buf32(32),t=0;t<144;)e.lens[t++]=8;for(;t<256;)e.lens[t++]=9;for(;t<280;)e.lens[t++]=7;for(;t<288;)e.lens[t++]=8;for(ra(1,e.lens,0,288,va,0,e.work,{bits:9}),t=0;t<32;)e.lens[t++]=5;ra(2,e.lens,0,32,ba,0,e.work,{bits:5}),ma=!1}e.lencode=va,e.lenbits=9,e.distcode=ba,e.distbits=5}function ya(e,t,n,o){var i,r=e.state;return null===r.window&&(r.wsize=1<<r.wbits,r.wnext=0,r.whave=0,r.window=new qi.Buf8(r.wsize)),o>=r.wsize?(qi.arraySet(r.window,t,n-r.wsize,r.wsize,0),r.wnext=0,r.whave=r.wsize):((i=r.wsize-r.wnext)>o&&(i=o),qi.arraySet(r.window,t,n-o,i,r.wnext),(o-=i)?(qi.arraySet(r.window,t,n-o,o,0),r.wnext=o,r.whave=r.wsize):(r.wnext+=i,r.wnext===r.wsize&&(r.wnext=0),r.whave<r.wsize&&(r.whave+=i))),0}var _a={inflateReset:fa,inflateReset2:ha,inflateResetKeep:da,inflateInit:function(e){return pa(e,15)},inflateInit2:pa,inflate:function(e,t){var n,o,i,r,a,s,u,c,l,d,f,h,p,v,b,m,g,y,_,w,k,C,S,R,q=0,I=new qi.Buf8(4),P=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15];if(!e||!e.state||!e.output||!e.input&&0!==e.avail_in)return aa;(n=e.state).mode===sa&&(n.mode=13),a=e.next_out,i=e.output,u=e.avail_out,r=e.next_in,o=e.input,s=e.avail_in,c=n.hold,l=n.bits,d=s,f=u,C=0;e:for(;;)switch(n.mode){case 1:if(0===n.wrap){n.mode=13;break}for(;l<16;){if(0===s)break e;s--,c+=o[r++]<<l,l+=8}if(2&n.wrap&&35615===c){n.check=0,I[0]=255&c,I[1]=c>>>8&255,n.check=hr(n.check,I,2,0),c=0,l=0,n.mode=2;break}if(n.flags=0,n.head&&(n.head.done=!1),!(1&n.wrap)||(((255&c)<<8)+(c>>8))%31){e.msg="incorrect header check",n.mode=ua;break}if(8!=(15&c)){e.msg="unknown compression method",n.mode=ua;break}if(l-=4,k=8+(15&(c>>>=4)),0===n.wbits)n.wbits=k;else if(k>n.wbits){e.msg="invalid window size",n.mode=ua;break}n.dmax=1<<k,e.adler=n.check=1,n.mode=512&c?10:sa,c=0,l=0;break;case 2:for(;l<16;){if(0===s)break e;s--,c+=o[r++]<<l,l+=8}if(n.flags=c,8!=(255&n.flags)){e.msg="unknown compression method",n.mode=ua;break}if(57344&n.flags){e.msg="unknown header flags set",n.mode=ua;break}n.head&&(n.head.text=c>>8&1),512&n.flags&&(I[0]=255&c,I[1]=c>>>8&255,n.check=hr(n.check,I,2,0)),c=0,l=0,n.mode=3;case 3:for(;l<32;){if(0===s)break e;s--,c+=o[r++]<<l,l+=8}n.head&&(n.head.time=c),512&n.flags&&(I[0]=255&c,I[1]=c>>>8&255,I[2]=c>>>16&255,I[3]=c>>>24&255,n.check=hr(n.check,I,4,0)),c=0,l=0,n.mode=4;case 4:for(;l<16;){if(0===s)break e;s--,c+=o[r++]<<l,l+=8}n.head&&(n.head.xflags=255&c,n.head.os=c>>8),512&n.flags&&(I[0]=255&c,I[1]=c>>>8&255,n.check=hr(n.check,I,2,0)),c=0,l=0,n.mode=5;case 5:if(1024&n.flags){for(;l<16;){if(0===s)break e;s--,c+=o[r++]<<l,l+=8}n.length=c,n.head&&(n.head.extra_len=c),512&n.flags&&(I[0]=255&c,I[1]=c>>>8&255,n.check=hr(n.check,I,2,0)),c=0,l=0}else n.head&&(n.head.extra=null);n.mode=6;case 6:if(1024&n.flags&&((h=n.length)>s&&(h=s),h&&(n.head&&(k=n.head.extra_len-n.length,n.head.extra||(n.head.extra=new Array(n.head.extra_len)),qi.arraySet(n.head.extra,o,r,h,k)),512&n.flags&&(n.check=hr(n.check,o,h,r)),s-=h,r+=h,n.length-=h),n.length))break e;n.length=0,n.mode=7;case 7:if(2048&n.flags){if(0===s)break e;h=0;do{k=o[r+h++],n.head&&k&&n.length<65536&&(n.head.name+=String.fromCharCode(k))}while(k&&h<s);if(512&n.flags&&(n.check=hr(n.check,o,h,r)),s-=h,r+=h,k)break e}else n.head&&(n.head.name=null);n.length=0,n.mode=8;case 8:if(4096&n.flags){if(0===s)break e;h=0;do{k=o[r+h++],n.head&&k&&n.length<65536&&(n.head.comment+=String.fromCharCode(k))}while(k&&h<s);if(512&n.flags&&(n.check=hr(n.check,o,h,r)),s-=h,r+=h,k)break e}else n.head&&(n.head.comment=null);n.mode=9;case 9:if(512&n.flags){for(;l<16;){if(0===s)break e;s--,c+=o[r++]<<l,l+=8}if(c!==(65535&n.check)){e.msg="header crc mismatch",n.mode=ua;break}c=0,l=0}n.head&&(n.head.hcrc=n.flags>>9&1,n.head.done=!0),e.adler=n.check=0,n.mode=sa;break;case 10:for(;l<32;){if(0===s)break e;s--,c+=o[r++]<<l,l+=8}e.adler=n.check=ca(c),c=0,l=0,n.mode=11;case 11:if(0===n.havedict)return e.next_out=a,e.avail_out=u,e.next_in=r,e.avail_in=s,n.hold=c,n.bits=l,2;e.adler=n.check=1,n.mode=sa;case sa:if(5===t||6===t)break e;case 13:if(n.last){c>>>=7&l,l-=7&l,n.mode=27;break}for(;l<3;){if(0===s)break e;s--,c+=o[r++]<<l,l+=8}switch(n.last=1&c,l-=1,3&(c>>>=1)){case 0:n.mode=14;break;case 1:if(ga(n),n.mode=20,6===t){c>>>=2,l-=2;break e}break;case 2:n.mode=17;break;case 3:e.msg="invalid block type",n.mode=ua}c>>>=2,l-=2;break;case 14:for(c>>>=7&l,l-=7&l;l<32;){if(0===s)break e;s--,c+=o[r++]<<l,l+=8}if((65535&c)!=(c>>>16^65535)){e.msg="invalid stored block lengths",n.mode=ua;break}if(n.length=65535&c,c=0,l=0,n.mode=15,6===t)break e;case 15:n.mode=16;case 16:if(h=n.length){if(h>s&&(h=s),h>u&&(h=u),0===h)break e;qi.arraySet(i,o,r,h,a),s-=h,r+=h,u-=h,a+=h,n.length-=h;break}n.mode=sa;break;case 17:for(;l<14;){if(0===s)break e;s--,c+=o[r++]<<l,l+=8}if(n.nlen=257+(31&c),c>>>=5,l-=5,n.ndist=1+(31&c),c>>>=5,l-=5,n.ncode=4+(15&c),c>>>=4,l-=4,n.nlen>286||n.ndist>30){e.msg="too many length or distance symbols",n.mode=ua;break}n.have=0,n.mode=18;case 18:for(;n.have<n.ncode;){for(;l<3;){if(0===s)break e;s--,c+=o[r++]<<l,l+=8}n.lens[P[n.have++]]=7&c,c>>>=3,l-=3}for(;n.have<19;)n.lens[P[n.have++]]=0;if(n.lencode=n.lendyn,n.lenbits=7,S={bits:n.lenbits},C=ra(0,n.lens,0,19,n.lencode,0,n.work,S),n.lenbits=S.bits,C){e.msg="invalid code lengths set",n.mode=ua;break}n.have=0,n.mode=19;case 19:for(;n.have<n.nlen+n.ndist;){for(;m=(q=n.lencode[c&(1<<n.lenbits)-1])>>>16&255,g=65535&q,!((b=q>>>24)<=l);){if(0===s)break e;s--,c+=o[r++]<<l,l+=8}if(g<16)c>>>=b,l-=b,n.lens[n.have++]=g;else{if(16===g){for(R=b+2;l<R;){if(0===s)break e;s--,c+=o[r++]<<l,l+=8}if(c>>>=b,l-=b,0===n.have){e.msg="invalid bit length repeat",n.mode=ua;break}k=n.lens[n.have-1],h=3+(3&c),c>>>=2,l-=2}else if(17===g){for(R=b+3;l<R;){if(0===s)break e;s--,c+=o[r++]<<l,l+=8}l-=b,k=0,h=3+(7&(c>>>=b)),c>>>=3,l-=3}else{for(R=b+7;l<R;){if(0===s)break e;s--,c+=o[r++]<<l,l+=8}l-=b,k=0,h=11+(127&(c>>>=b)),c>>>=7,l-=7}if(n.have+h>n.nlen+n.ndist){e.msg="invalid bit length repeat",n.mode=ua;break}for(;h--;)n.lens[n.have++]=k}}if(n.mode===ua)break;if(0===n.lens[256]){e.msg="invalid code -- missing end-of-block",n.mode=ua;break}if(n.lenbits=9,S={bits:n.lenbits},C=ra(1,n.lens,0,n.nlen,n.lencode,0,n.work,S),n.lenbits=S.bits,C){e.msg="invalid literal/lengths set",n.mode=ua;break}if(n.distbits=6,n.distcode=n.distdyn,S={bits:n.distbits},C=ra(2,n.lens,n.nlen,n.ndist,n.distcode,0,n.work,S),n.distbits=S.bits,C){e.msg="invalid distances set",n.mode=ua;break}if(n.mode=20,6===t)break e;case 20:n.mode=21;case 21:if(s>=6&&u>=258){e.next_out=a,e.avail_out=u,e.next_in=r,e.avail_in=s,n.hold=c,n.bits=l,Qr(e,f),a=e.next_out,i=e.output,u=e.avail_out,r=e.next_in,o=e.input,s=e.avail_in,c=n.hold,l=n.bits,n.mode===sa&&(n.back=-1);break}for(n.back=0;m=(q=n.lencode[c&(1<<n.lenbits)-1])>>>16&255,g=65535&q,!((b=q>>>24)<=l);){if(0===s)break e;s--,c+=o[r++]<<l,l+=8}if(m&&0==(240&m)){for(y=b,_=m,w=g;m=(q=n.lencode[w+((c&(1<<y+_)-1)>>y)])>>>16&255,g=65535&q,!(y+(b=q>>>24)<=l);){if(0===s)break e;s--,c+=o[r++]<<l,l+=8}c>>>=y,l-=y,n.back+=y}if(c>>>=b,l-=b,n.back+=b,n.length=g,0===m){n.mode=26;break}if(32&m){n.back=-1,n.mode=sa;break}if(64&m){e.msg="invalid literal/length code",n.mode=ua;break}n.extra=15&m,n.mode=22;case 22:if(n.extra){for(R=n.extra;l<R;){if(0===s)break e;s--,c+=o[r++]<<l,l+=8}n.length+=c&(1<<n.extra)-1,c>>>=n.extra,l-=n.extra,n.back+=n.extra}n.was=n.length,n.mode=23;case 23:for(;m=(q=n.distcode[c&(1<<n.distbits)-1])>>>16&255,g=65535&q,!((b=q>>>24)<=l);){if(0===s)break e;s--,c+=o[r++]<<l,l+=8}if(0==(240&m)){for(y=b,_=m,w=g;m=(q=n.distcode[w+((c&(1<<y+_)-1)>>y)])>>>16&255,g=65535&q,!(y+(b=q>>>24)<=l);){if(0===s)break e;s--,c+=o[r++]<<l,l+=8}c>>>=y,l-=y,n.back+=y}if(c>>>=b,l-=b,n.back+=b,64&m){e.msg="invalid distance code",n.mode=ua;break}n.offset=g,n.extra=15&m,n.mode=24;case 24:if(n.extra){for(R=n.extra;l<R;){if(0===s)break e;s--,c+=o[r++]<<l,l+=8}n.offset+=c&(1<<n.extra)-1,c>>>=n.extra,l-=n.extra,n.back+=n.extra}if(n.offset>n.dmax){e.msg="invalid distance too far back",n.mode=ua;break}n.mode=25;case 25:if(0===u)break e;if(h=f-u,n.offset>h){if((h=n.offset-h)>n.whave&&n.sane){e.msg="invalid distance too far back",n.mode=ua;break}h>n.wnext?(h-=n.wnext,p=n.wsize-h):p=n.wnext-h,h>n.length&&(h=n.length),v=n.window}else v=i,p=a-n.offset,h=n.length;h>u&&(h=u),u-=h,n.length-=h;do{i[a++]=v[p++]}while(--h);0===n.length&&(n.mode=21);break;case 26:if(0===u)break e;i[a++]=n.length,u--,n.mode=21;break;case 27:if(n.wrap){for(;l<32;){if(0===s)break e;s--,c|=o[r++]<<l,l+=8}if(f-=u,e.total_out+=f,n.total+=f,f&&(e.adler=n.check=n.flags?hr(n.check,i,f,a-f):lr(n.check,i,f,a-f)),f=u,(n.flags?c:ca(c))!==n.check){e.msg="incorrect data check",n.mode=ua;break}c=0,l=0}n.mode=28;case 28:if(n.wrap&&n.flags){for(;l<32;){if(0===s)break e;s--,c+=o[r++]<<l,l+=8}if(c!==(4294967295&n.total)){e.msg="incorrect length check",n.mode=ua;break}c=0,l=0}n.mode=29;case 29:C=1;break e;case ua:C=-3;break e;case 31:return-4;case 32:default:return aa}return e.next_out=a,e.avail_out=u,e.next_in=r,e.avail_in=s,n.hold=c,n.bits=l,(n.wsize||f!==e.avail_out&&n.mode<ua&&(n.mode<27||4!==t))&&ya(e,e.output,e.next_out,f-e.avail_out),d-=e.avail_in,f-=e.avail_out,e.total_in+=d,e.total_out+=f,n.total+=f,n.wrap&&f&&(e.adler=n.check=n.flags?hr(n.check,i,f,e.next_out-f):lr(n.check,i,f,e.next_out-f)),e.data_type=n.bits+(n.last?64:0)+(n.mode===sa?128:0)+(20===n.mode||15===n.mode?256:0),(0===d&&0===f||4===t)&&0===C&&(C=-5),C},inflateEnd:function(e){if(!e||!e.state)return aa;var t=e.state;return t.window&&(t.window=null),e.state=null,0},inflateGetHeader:function(e,t){var n;return e&&e.state?0==(2&(n=e.state).wrap)?aa:(n.head=t,t.done=!1,0):aa},inflateSetDictionary:function(e,t){var n,o=t.length;return e&&e.state?0!==(n=e.state).wrap&&11!==n.mode?aa:11===n.mode&&lr(1,t,o,0)!==n.check?-3:ya(e,t,o,o)?(n.mode=31,-4):(n.havedict=1,0):aa},inflateInfo:"pako inflate (from Nodeca project)"},wa={Z_NO_FLUSH:0,Z_PARTIAL_FLUSH:1,Z_SYNC_FLUSH:2,Z_FULL_FLUSH:3,Z_FINISH:4,Z_BLOCK:5,Z_TREES:6,Z_OK:0,Z_STREAM_END:1,Z_NEED_DICT:2,Z_ERRNO:-1,Z_STREAM_ERROR:-2,Z_DATA_ERROR:-3,Z_BUF_ERROR:-5,Z_NO_COMPRESSION:0,Z_BEST_SPEED:1,Z_BEST_COMPRESSION:9,Z_DEFAULT_COMPRESSION:-1,Z_FILTERED:1,Z_HUFFMAN_ONLY:2,Z_RLE:3,Z_FIXED:4,Z_DEFAULT_STRATEGY:0,Z_BINARY:0,Z_TEXT:1,Z_UNKNOWN:2,Z_DEFLATED:8};var ka=function(){this.text=0,this.time=0,this.xflags=0,this.os=0,this.extra=null,this.extra_len=0,this.name="",this.comment="",this.hcrc=0,this.done=!1},Ca=Object.prototype.toString;function Sa(e){if(!(this instanceof Sa))return new Sa(e);this.options=qi.assign({chunkSize:16384,windowBits:0,to:""},e||{});var t=this.options;t.raw&&t.windowBits>=0&&t.windowBits<16&&(t.windowBits=-t.windowBits,0===t.windowBits&&(t.windowBits=-15)),!(t.windowBits>=0&&t.windowBits<16)||e&&e.windowBits||(t.windowBits+=32),t.windowBits>15&&t.windowBits<48&&0==(15&t.windowBits)&&(t.windowBits|=15),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new Vr,this.strm.avail_out=0;var n=_a.inflateInit2(this.strm,t.windowBits);if(n!==wa.Z_OK)throw new Error(pr[n]);if(this.header=new ka,_a.inflateGetHeader(this.strm,this.header),t.dictionary&&("string"==typeof t.dictionary?t.dictionary=jr(t.dictionary):"[object ArrayBuffer]"===Ca.call(t.dictionary)&&(t.dictionary=new Uint8Array(t.dictionary)),t.raw&&(n=_a.inflateSetDictionary(this.strm,t.dictionary))!==wa.Z_OK))throw new Error(pr[n])}function Ra(e,t){var n=new Sa(t);if(n.push(e,!0),n.err)throw n.msg||pr[n.err];return n.result}Sa.prototype.push=function(e,t){var n,o,i,r,a,s=this.strm,u=this.options.chunkSize,c=this.options.dictionary,l=!1;if(this.ended)return!1;o=t===~~t?t:!0===t?wa.Z_FINISH:wa.Z_NO_FLUSH,"string"==typeof e?s.input=Zr(e):"[object ArrayBuffer]"===Ca.call(e)?s.input=new Uint8Array(e):s.input=e,s.next_in=0,s.avail_in=s.input.length;do{if(0===s.avail_out&&(s.output=new qi.Buf8(u),s.next_out=0,s.avail_out=u),(n=_a.inflate(s,wa.Z_NO_FLUSH))===wa.Z_NEED_DICT&&c&&(n=_a.inflateSetDictionary(this.strm,c)),n===wa.Z_BUF_ERROR&&!0===l&&(n=wa.Z_OK,l=!1),n!==wa.Z_STREAM_END&&n!==wa.Z_OK)return this.onEnd(n),this.ended=!0,!1;s.next_out&&(0!==s.avail_out&&n!==wa.Z_STREAM_END&&(0!==s.avail_in||o!==wa.Z_FINISH&&o!==wa.Z_SYNC_FLUSH)||("string"===this.options.to?(i=Gr(s.output,s.next_out),r=s.next_out-i,a=Hr(s.output,i),s.next_out=r,s.avail_out=u-r,r&&qi.arraySet(s.output,s.output,i,r,0),this.onData(a)):this.onData(qi.shrinkBuf(s.output,s.next_out)))),0===s.avail_in&&0===s.avail_out&&(l=!0)}while((s.avail_in>0||0===s.avail_out)&&n!==wa.Z_STREAM_END);return n===wa.Z_STREAM_END&&(o=wa.Z_FINISH),o===wa.Z_FINISH?(n=_a.inflateEnd(this.strm),this.onEnd(n),this.ended=!0,n===wa.Z_OK):o!==wa.Z_SYNC_FLUSH||(this.onEnd(wa.Z_OK),s.avail_out=0,!0)},Sa.prototype.onData=function(e){this.chunks.push(e)},Sa.prototype.onEnd=function(e){e===wa.Z_OK&&("string"===this.options.to?this.result=this.chunks.join(""):this.result=qi.flattenChunks(this.chunks)),this.chunks=[],this.err=e,this.msg=this.strm.msg};var qa={Inflate:Sa,inflate:Ra,inflateRaw:function(e,t){return(t=t||{}).raw=!0,Ra(e,t)},ungzip:Ra},Ia={};(0,qi.assign)(Ia,$r,qa,wa);var Pa,xa=Ia,za=function(){function e(n){t(this,e),this.oOptions=Object.assign({iPort:-1,cbConnectSuccess:null,cbConnectError:null,cbConnectClose:null},n),this.szHost="http://127.0.0.1",this.szUUID="",this.szVersion="",this.bNormalClose=!1,this.bConnected=!1,this.bInitConnect=!0,this.iGetErrorCount=0,this.oWindowControlCallback={},this.oSadpCallback={},this.oSliceCallback={},this.oSerialCallback={},this.oUIControlCallback={},this.oUpgradeCallback={},this.init()}return o(e,[{key:"init",value:function(){var e=this,t=Zo(),n={sequence:t,cmd:"system.connect"},o=JSON.stringify(n);e.sendImageHttp("".concat(e.szHost,":").concat(e.oOptions.iPort,"/imghttp/local"),o,t,{success:function(t){var n=JSON.parse(t);e.szUUID=n.uuid,e.szVersion=n.version,e.bConnected=!0,e.bInitConnect=!1,setTimeout((function(){e.imageHttpPolling()}),100),e.oOptions.cbConnectSuccess&&e.oOptions.cbConnectSuccess()},error:function(){}})}},{key:"sendImageHttp",value:function(e,t,n,o){var i=this;o=Object.assign({success:null,error:null,abort:null},o);var r=xa.deflate(t);""!==(new Uint8Array).toString()&&(Si.isMacOS()||Si.browser().msie)&&(r=Array.prototype.slice.call(r));for(var a=encodeURIComponent(btoa(r)),s=this.splitStr(a),u=[],c="",l=0,d=s.length;l<d;l++)c=l===d-1?"update=".concat((new Date).getTime(),"&isLast=true&data=").concat(s[l],"&sequence=").concat(n):"update=".concat((new Date).getTime(),"&isLast=false&data=").concat(s[l],"&sequence=").concat(n),u.push(c);u.length>0&&function t(){i.imageHttp("".concat(e,"?").concat(u[0]),{success:function(e){u.shift(),u.length>0?(i.bInitConnect||i.bConnected)&&t():o.success&&o.success(e)},error:function(){o.error&&o.error()},abort:function(){o.abort&&o.abort()}})}()}},{key:"splitStr",value:function(e){for(var t=this.getByteLen(e),n=[],o=1500,i=0,r=Math.ceil(t/o);i<r;i++)n[i]=e.slice(o*i,o*(i+1));return n}},{key:"getByteLen",value:function(e){for(var t=0,n="",o=0,i=e.length;o<i;o++)n=e.charAt(o),/[^\x00-\xff]/.test(n)?t+=2:t+=1;return t}},{key:"imageHttp",value:function(e,t){t=Object.assign({success:null,error:null,abort:null},t);var n=new Image;n.onload=function(){if(t.success){var e=document.createElement("canvas"),o=e.getContext("2d"),i=n.width,r=n.height;e.width=i,e.height=r;try{o.drawImage(n,0,0);for(var a=o.getImageData(0,0,i,r).data,s="",u=-1,c=r-1;c>=0;c--)for(var l=0;l<4*i&&0!==a[u=c*i*4+l];l++)255!==a[u]&&(s+=String.fromCharCode(a[u]));t.success(Si.utf8to16(s))}catch(e){t.error&&t.error()}}},n.onerror=function(){t.error&&t.error()},n.onabort=function(){t.abort&&t.abort()},n.crossOrigin="anonymous",n.src=e}},{key:"setWindowControlCallback",value:function(e){this.oWindowControlCallback=e}},{key:"setSadpCallback",value:function(e){this.oSadpCallback=e}},{key:"setSliceCallback",value:function(e){this.oSliceCallback=e}},{key:"setSerialCallback",value:function(e){this.oSerialCallback=e}},{key:"setUIControlCallback",value:function(e){this.oUIControlCallback=e}},{key:"setUpgradeCallback",value:function(e){this.oUpgradeCallback=e}},{key:"getServiceVersion",value:function(){return this.szVersion}},{key:"getRequestUUID",value:function(){return this.szUUID}},{key:"disconnect",value:function(){var e=this,t=Zo(),n={sequence:t,uuid:e.szUUID,cmd:"system.disconnect"},o=JSON.stringify(n);e.bConnected&&e.sendImageHttp("".concat(e.szHost,":").concat(e.oOptions.iPort,"/imghttp/local"),o,t,{success:function(){e.bNormalClose=!0,e.bConnected=!1,e.oOptions.cbConnectClose&&e.oOptions.cbConnectClose(e.bNormalClose)},error:function(){e.bConnected=!1}})}},{key:"imageHttpPolling",value:function(){var e=this,t=Zo(),n={sequence:t,uuid:e.szUUID,cmd:"system.get"},o=JSON.stringify(n);e.bConnected&&e.sendImageHttp("".concat(e.szHost,":").concat(e.oOptions.iPort,"/imghttp/local"),o,t,{success:function(t){if(e.iGetErrorCount=0,"timeout"===t)setTimeout((function(){e.imageHttpPolling()}),100);else if("invalid"===t)e.bConnected=!1,e.oOptions.cbConnectError&&e.oOptions.cbConnectError();else if("closed"===t)console.log("connected is disconnected");else{var n=JSON.parse(t);void 0!==n.cmd?e.parseCmd(n):console.log("[jsWebControl]imgHttpPolling push message error:".concat(t)),setTimeout((function(){e.imageHttpPolling()}),100)}},error:function(){5===e.iGetErrorCount?(console.log("[jsWebControl]imageHttpPolling get polling finished"),e.bNormalClose=!1,e.bConnected=!1,e.oOptions.cbConnectClose&&e.oOptions.cbConnectClose(e.bNormalClose)):setTimeout((function(){console.log("[jsWebControl]imgHttpPolling get polling failed"),e.iGetErrorCount++,e.imageHttpPolling()}),100)}})}},{key:"sendRequest",value:function(e){var t=this;return new Promise((function(n,o){var i=e.cmd.split("."),r="";i.length>1?r="laputa"===i[0]?"laputa":"local":o();var a=Zo();e.sequence=a,e.uuid=t.szUUID,e.timestamp="".concat((new Date).getTime());var s=JSON.stringify(e);t.bConnected?t.sendImageHttp("".concat(t.szHost,":").concat(t.oOptions.iPort,"/imghttp/").concat(r),s,a,{success:function(e){var t=JSON.parse(e);0===t.errorModule&&0===t.errorCode?n(t):o(t)},error:function(){o()}}):o()}))}},{key:"parseCmd",value:function(e){var t=e.cmd.split("."),n=t[1].replace(/^[a-z]{1}/g,(function(e){return e.toUpperCase()}));"window"===t[0]||"play"===t[0]?this.oWindowControlCallback["cb".concat(n)]&&this.oWindowControlCallback["cb".concat(n)](e):"sadp"===t[0]?this.oSadpCallback["cb".concat(n)]&&this.oSadpCallback["cb".concat(n)](e):"serial"===t[0]?this.oSerialCallback["cb".concat(n)]&&this.oSerialCallback["cb".concat(n)](e):"slice"===t[0]?this.oSliceCallback["cb".concat(n)]&&this.oSliceCallback["cb".concat(n)](e):"ui"===t[0]?this.oUIControlCallback["cb".concat(n)]&&this.oUIControlCallback["cb".concat(n)](e):"upgrade"===t[0]&&this.oUpgradeCallback["cb".concat(n)]&&this.oUpgradeCallback["cb".concat(n)](e)}}]),e}(),Ea=function(){function e(n){t(this,e),this.oOptions=Object.assign({szPluginContainer:"",iPort:-1,cbConnectSuccess:null,cbConnectError:null,cbConnectClose:null,szClassId:""},n),this.oPlugin=null,this.szPluginId="",this.szUUID="",this.szVersion="",this.oRequestList={},this.bNormalClose=!1,this.aMessage=[],this.oWindowControlCallback={},this.oSadpCallback={},this.oSliceCallback={},this.oSerialCallback={},this.oUIControlCallback={},this.oUpgradeCallback={},this.init()}return o(e,[{key:"init",value:function(){var e=this;e.initPlugin(),e.oPlugin.object&&e.oPlugin.createSocket("ws://127.0.0.1:".concat(e.oOptions.iPort))}},{key:"initPlugin",value:function(){var e=this;this.szPluginId="webActiveX_".concat((new Date).getTime());var t="<object id='".concat(this.szPluginId,"' classid='clsid:").concat(e.oOptions.szClassId,"' codebase='' standby='Waiting...' width='100%' height='100%' align='center' ></object>"),n=e.oOptions.szPluginContainer;if(""===n){n="".concat(this.szPluginId,"_div");var o=document.createElement("div");o.id=n,document.body.parentNode.appendChild(o)}document.getElementById(n).innerHTML=t,e.oPlugin=document.getElementById(this.szPluginId),window.onConnectMessage=function(t,n){n?(e.aMessage.push(t),e.onConnectMessage(e.aMessage.join("")),e.aMessage.length=0):e.aMessage.push(t)},window.onConnectClose=function(){e.onConnectClose()},window.onConnectError=function(){e.onConnectError()},window.onConnectCloseException=function(){e.onConnectCloseException()},window.onConnectOpen=function(){e.onConnectOpen()},Si.createEventScript(this.szPluginId,"onConnectMessage(szData, bLast)","onConnectMessage(szData, bLast);"),Si.createEventScript(this.szPluginId,"onConnectClose()","onConnectClose();"),Si.createEventScript(this.szPluginId,"onConnectError()","onConnectError();"),Si.createEventScript(this.szPluginId,"onConnectCloseException()","onConnectCloseException();"),Si.createEventScript(this.szPluginId,"onConnectOpen()","onConnectOpen();")}},{key:"onConnectMessage",value:function(e){var t=this;if(e){var n=JSON.parse(e),o=n.sequence;void 0===o&&void 0===n.cmd?(t.szUUID=n.uuid,t.szVersion=n.version,t.oOptions.cbConnectSuccess&&t.oOptions.cbConnectSuccess()):void 0!==n.cmd?t.parseCmd(n):void 0!==t.oRequestList[o]&&(0===n.errorModule&&0===n.errorCode?t.oRequestList[o].resolve(n):t.oRequestList[o].reject(n),delete t.oRequestList[o])}}},{key:"onConnectClose",value:function(){if(this.oPlugin=null,""!==this.szPluginId){var e=document.getElementById(this.szPluginId);e.parentNode.removeChild(e);var t=document.getElementById("".concat(this.szPluginId,"_div"));null!==t&&t.parentNode.removeChild(t)}this.oOptions.cbConnectClose&&this.oOptions.cbConnectClose(this.bNormalClose)}},{key:"onConnectCloseException",value:function(){var e=this;setTimeout((function(){e.oPlugin.object&&e.oPlugin.closeSocket()}),1e3)}},{key:"onConnectOpen",value:function(){var e={sequence:Zo(),cmd:"system.connect"},t=JSON.stringify(e);this.oPlugin.object&&this.oPlugin.sendRequest(t)}},{key:"onConnectError",value:function(){}},{key:"setWindowControlCallback",value:function(e){this.oWindowControlCallback=e}},{key:"setSadpCallback",value:function(e){this.oSadpCallback=e}},{key:"setSliceCallback",value:function(e){this.oSliceCallback=e}},{key:"setSerialCallback",value:function(e){this.oSerialCallback=e}},{key:"setUIControlCallback",value:function(e){this.oUIControlCallback=e}},{key:"setUpgradeCallback",value:function(e){this.oUpgradeCallback=e}},{key:"getServiceVersion",value:function(){return this.szVersion}},{key:"getRequestUUID",value:function(){return this.szUUID}},{key:"disconnect",value:function(){this.bNormalClose=!0,this.oPlugin&&this.oPlugin.object&&this.oPlugin.closeSocket()}},{key:"sendRequest",value:function(e){var t=this;return"window.hideWnd"===e.cmd?t.oPlugin&&t.oPlugin.object&&(t.oPlugin.style.visibility="hidden"):"window.showWnd"===e.cmd&&t.oPlugin&&t.oPlugin.object&&(t.oPlugin.style.visibility="visible"),new Promise((function(n,o){var i=Zo();e.sequence=i,t.oRequestList[i]={resolve:n,reject:o},e.uuid=t.szUUID,e.timestamp="".concat((new Date).getTime());var r=JSON.stringify(e);t.oPlugin&&t.oPlugin.object?t.oPlugin.sendRequest(r):o()}))}},{key:"parseCmd",value:function(e){var t=e.cmd.split("."),n=t[1].replace(/^[a-z]{1}/g,(function(e){return e.toUpperCase()}));"window"===t[0]||"play"===t[0]?this.oWindowControlCallback["cb".concat(n)]&&this.oWindowControlCallback["cb".concat(n)](e):"sadp"===t[0]?this.oSadpCallback["cb".concat(n)]&&this.oSadpCallback["cb".concat(n)](e):"serial"===t[0]?this.oSerialCallback["cb".concat(n)]&&this.oSerialCallback["cb".concat(n)](e):"slice"===t[0]?this.oSliceCallback["cb".concat(n)]&&this.oSliceCallback["cb".concat(n)](e):"ui"===t[0]?this.oUIControlCallback["cb".concat(n)]&&this.oUIControlCallback["cb".concat(n)](e):"upgrade"===t[0]&&this.oUpgradeCallback["cb".concat(n)]&&this.oUpgradeCallback["cb".concat(n)](e)}}]),e}(),Oa=function(){function e(n){t(this,e),this.oOptions=Object.assign({szPluginContainer:"",cbConnectSuccess:null,cbConnectError:null,cbConnectClose:null,iServicePortStart:-1,iServicePortEnd:-1,szClassId:""},n),this.iPort=-1,this.oRequest=null,this.bInit=!1,this.oCallbacks={},this.init()}return o(e,[{key:"init",value:function(){var e=this;Si.detectPort(e.oOptions.iServicePortStart,e.oOptions.iServicePortEnd,{success:function(t){if(e.iPort=t,Si.browser().msie)"11.0"===Si.browser().version?"https:"===window.location.protocol?e.oRequest=new za({iPort:e.iPort,cbConnectSuccess:e.oOptions.cbConnectSuccess,cbConnectError:e.oOptions.cbConnectError,cbConnectClose:e.oOptions.cbConnectClose}):e.oRequest=new Ri({iPort:e.iPort,cbConnectSuccess:e.oOptions.cbConnectSuccess,cbConnectError:e.oOptions.cbConnectError,cbConnectClose:e.oOptions.cbConnectClose}):e.oRequest=new Ea({szPluginContainer:e.oOptions.szPluginContainer,iPort:e.iPort,cbConnectSuccess:e.oOptions.cbConnectSuccess,cbConnectError:e.oOptions.cbConnectError,cbConnectClose:e.oOptions.cbConnectClose,szClassId:e.oOptions.szClassId});else if("https:"===window.location.protocol)if(Si.browser().chrome)try{e.oRequest=new Ri({iPort:e.iPort,cbConnectSuccess:e.oOptions.cbConnectSuccess,cbConnectError:e.oOptions.cbConnectError,cbConnectClose:e.oOptions.cbConnectClose})}catch(t){e.oRequest=new za({iPort:e.iPort,cbConnectSuccess:e.oOptions.cbConnectSuccess,cbConnectError:e.oOptions.cbConnectError,cbConnectClose:e.oOptions.cbConnectClose})}else e.oRequest=new za({iPort:e.iPort,cbConnectSuccess:e.oOptions.cbConnectSuccess,cbConnectError:e.oOptions.cbConnectError,cbConnectClose:e.oOptions.cbConnectClose});else"WebSocket"in window&&(e.oRequest=new Ri({iPort:e.iPort,cbConnectSuccess:e.oOptions.cbConnectSuccess,cbConnectError:e.oOptions.cbConnectError,cbConnectClose:e.oOptions.cbConnectClose}));for(var n in e.bInit=!0,e.oCallbacks)e.oRequest[n](e.oCallbacks[n])},error:function(){e.oOptions.cbConnectError&&e.oOptions.cbConnectError()}})}},{key:"setWindowControlCallback",value:function(e){this.bInit?this.oRequest.setWindowControlCallback(e):this.oCallbacks.setWindowControlCallback=e}},{key:"setSadpCallback",value:function(e){this.bInit?this.oRequest.setSadpCallback(e):this.oCallbacks.setSadpCallback=e}},{key:"setSliceCallback",value:function(e){this.bInit?this.oRequest.setSliceCallback(e):this.oCallbacks.setSliceCallback=e}},{key:"setSerialCallback",value:function(e){this.bInit?this.oRequest.setSerialCallback(e):this.oCallbacks.setSerialCallback=e}},{key:"setUIControlCallback",value:function(e){this.bInit?this.oRequest.setUIControlCallback(e):this.oCallbacks.setUIControlCallback=e}},{key:"setUpgradeCallback",value:function(e){this.bInit?this.oRequest.setUpgradeCallback(e):this.oCallbacks.setUpgradeCallback=e}},{key:"getServiceVersion",value:function(){return this.oRequest.getServiceVersion()}},{key:"getRequestUUID",value:function(){return this.oRequest.getRequestUUID()}},{key:"startService",value:function(e,t){var n={cmd:"system.startService",type:e};return void 0!==t&&(n.options=t),this.oRequest.sendRequest(n)}},{key:"stopService",value:function(e){var t=this;return new Promise((function(n,o){null!==t.oRequest?t.oRequest.sendRequest({cmd:"system.stopService",type:e}).then((function(e){n(e)}),(function(e){o(e)})):o()}))}},{key:"disconnect",value:function(){var e=this;return new Promise((function(t,n){null!==e.oRequest?(e.oRequest.disconnect(),t("cbConnectClose callback is really success")):n()}))}},{key:"openDirectory",value:function(e){return this.oRequest.sendRequest({cmd:"system.openDirectory",path:e})}},{key:"openFile",value:function(e,t,n){return this.oRequest.sendRequest({cmd:"system.openFile",path:e,relative:t,version:n})}},{key:"selectDirectory",value:function(e,t){var n=this;return new Promise((function(o,i){null!==n.oRequest?n.oRequest.sendRequest({cmd:"system.selectDirectory",caption:void 0!==e&&""!==e?Si.Base64().encode(e):"",dir:void 0!==t&&""!==t?Si.Base64().encode(t):""}).then((function(e){""!==e.path&&(e.path=Si.Base64().decode(e.path)),o(e)}),(function(e){i(e)})):i()}))}},{key:"selectFile",value:function(e,t,n){var o=this;return new Promise((function(i,r){null!==o.oRequest?o.oRequest.sendRequest({cmd:"system.selectFile",caption:""!==e?Si.Base64().encode(e):"",dir:""!==t?Si.Base64().encode(t):"",filter:n}).then((function(e){""!==e.path&&(e.path=Si.Base64().decode(e.path)),i(e)}),(function(e){r(e)})):r()}))}},{key:"getLocalConfig",value:function(e){return this.oRequest.sendRequest({cmd:"system.getLocalConfig",default:e})}},{key:"setLocalConfig",value:function(e){return e.cmd="system.setLocalConfig",this.oRequest.sendRequest(e)}},{key:"createWnd",value:function(e,t,n,o,i,r,a){return this.oRequest.sendRequest({cmd:"window.createWnd",rect:{left:e,top:t,width:n,height:o},className:i,embed:r,activeXParentWnd:a})}},{key:"showWnd",value:function(){return this.oRequest.sendRequest({cmd:"window.showWnd"})}},{key:"hideWnd",value:function(){return this.oRequest.sendRequest({cmd:"window.hideWnd"})}},{key:"destroyWnd",value:function(){var e=this;return new Promise((function(t,n){null!==e.oRequest?e.oRequest.sendRequest({cmd:"window.destroyWnd"}).then((function(e){t(e)}),(function(e){n(e)})):n()}))}},{key:"setWndGeometry",value:function(e,t,n,o){return this.oRequest.sendRequest({cmd:"window.setWndGeometry",rect:{left:e,top:t,width:n,height:o}})}},{key:"setWndCover",value:function(e,t){var n=this;return new Promise((function(o,i){null!==n.oRequest?n.oRequest.sendRequest({cmd:"window.setWndCover",position:e,size:t}).then((function(e){o(e)}),(function(e){i(e)})):i()}))}},{key:"cuttingPartWindow",value:function(e,t,n,o,i){var r=this;return new Promise((function(a,s){null!==r.oRequest?r.oRequest.sendRequest({cmd:"window.cuttingPartWindow",rect:{left:e,top:t,width:n,height:o},round:i}).then((function(e){a(e)}),(function(e){s(e)})):s()}))}},{key:"repairPartWindow",value:function(e,t,n,o,i){var r=this;return new Promise((function(a,s){null!==r.oRequest?r.oRequest.sendRequest({cmd:"window.repairPartWindow",rect:{left:e,top:t,width:n,height:o},round:i}).then((function(e){a(e)}),(function(e){s(e)})):s()}))}},{key:"setWndZOrder",value:function(e){return this.oRequest.sendRequest({cmd:"window.setWndZOrder",flag:e})}},{key:"changePlayMode",value:function(e){return this.oRequest.sendRequest({cmd:"window.changePlayMode",type:e})}},{key:"setLanguageType",value:function(e){return this.oRequest.sendRequest({cmd:"window.setLanguageType",type:e})}},{key:"initLoginInfo",value:function(e){return this.oRequest.sendRequest({cmd:"window.initLoginInfo",vsmAddress:e.vsmAddress,vsmPort:e.vsmPort,sessionID:e.sessionID,loginModel:e.loginModel,userType:e.userType,networkType:e.networkType})}},{key:"setTranslateFile",value:function(e){return this.oRequest.sendRequest({cmd:"window.setTranslateFile",url:e})}},{key:"switchToSimple",value:function(e){return this.oRequest.sendRequest({cmd:"window.switchToSimple",simple:e})}},{key:"setVsmToken",value:function(e){return this.oRequest.sendRequest({cmd:"play.setVsmToken",token:e})}},{key:"startPlay",value:function(e,t,n,o,i,r,a,s,u){var c={cmd:"play.startPlay",url:e,username:t,password:n,siteID:o,areaName:Si.Base64().encode(i),cameraName:Si.Base64().encode(r),permission:a,wndIndex:s};return void 0!==u&&(c.options=u,void 0!==c.options.siteName&&(c.options.siteName=Si.Base64().encode(c.options.siteName))),this.oRequest.sendRequest(c)}},{key:"setPreview3DPosition",value:function(e){return this.oRequest.sendRequest({cmd:"play.setPreview3DPosition",open:e})}},{key:"stopTotal",value:function(){var e=this;return new Promise((function(t,n){null!==e.oRequest?e.oRequest.sendRequest({cmd:"play.stopTotal"}).then((function(e){t(e)}),(function(e){n(e)})):n()}))}},{key:"setDragMode",value:function(e){return this.oRequest.sendRequest({cmd:"play.setDragMode",drag:e})}},{key:"showErrorInfoInFullScreen",value:function(e){return this.oRequest.sendRequest({cmd:"play.showErrorInfoInFullScreen",error:Si.Base64().encode(e)})}},{key:"setNumberOfWindows",value:function(e){return this.oRequest.sendRequest({cmd:"play.setNumberOfWindows",number:e})}},{key:"initCardReader",value:function(e){return this.oRequest.sendRequest({cmd:"serial.ACSInitCardReader",param:e})}},{key:"unInitCardReader",value:function(){return this.oRequest.sendRequest({cmd:"serial.ACSUnInitCardReader"})}},{key:"startAutoMode",value:function(){return this.oRequest.sendRequest({cmd:"serial.ACSStartAutoMode"})}},{key:"stopAutoMode",value:function(){return this.oRequest.sendRequest({cmd:"serial.ACSStopAutoMode"})}},{key:"initFingerprint",value:function(e){return this.oRequest.sendRequest({cmd:"serial.ACSInitFingerprint",param:e})}},{key:"unInitFingerprint",value:function(){return this.oRequest.sendRequest({cmd:"serial.ACSUnInitFingerprint"})}},{key:"startCollectFingerprint",value:function(){return this.oRequest.sendRequest({cmd:"serial.ACSStartCollectFingerprint"})}},{key:"stopCollectFingerprint",value:function(){return this.oRequest.sendRequest({cmd:"serial.ACSStopCollectFingerprint"})}},{key:"isCollectingFingerprint",value:function(){return this.oRequest.sendRequest({cmd:"serial.ACSIsCollectingFingerprint"})}},{key:"initVideocapture",value:function(e){return e.majorTitle=Si.Base64().encode(e.majorTitle),e.tip=Si.Base64().encode(e.tip),e.captureBtnTxt=Si.Base64().encode(e.captureBtnTxt),e.USBRemovedTip=Si.Base64().encode(e.USBRemovedTip),this.oRequest.sendRequest({cmd:"serial.ACSStartCollectImage",param:e})}},{key:"unInitVideocapture",value:function(){return this.oRequest.sendRequest({cmd:"serial.ACSStopCollectImage"})}},{key:"registerDeviceType",value:function(e){return this.oRequest.sendRequest({cmd:"sadp.registDeviceType",deviceType:e})}},{key:"activeOnlineDevice",value:function(e,t){return this.oRequest.sendRequest({cmd:"sadp.activeDevice",serialNumber:e,password:t})}},{key:"refreshDeviceList",value:function(){return this.oRequest.sendRequest({cmd:"sadp.refreshDeviceList"})}},{key:"modifyDeviceNetParam",value:function(e,t,n,o,i,r,a){return this.oRequest.sendRequest({cmd:"sadp.modifyDeviceParam",macAddress:e,password:t,ipv4Address:n,ipv4Gateway:o,ipv4SubnetMask:i,port:r,httpPort:a})}},{key:"exportKeyFile",value:function(e){return this.oRequest.sendRequest({cmd:"sadp.exportKeyFile",serialNumber:e})}},{key:"importKeyFile",value:function(){return this.oRequest.sendRequest({cmd:"sadp.importKeyFile"})}},{key:"resetPassword",value:function(e,t,n,o){return this.oRequest.sendRequest({cmd:"sadp.resetPassword",serialNumber:e,password:t,importFileData:n,szCode:o})}},{key:"uploadPicture",value:function(e){return this.oRequest.sendRequest({cmd:"slice.uploadPicture",path:Si.Base64().encode(e)})}},{key:"showSelectMenu",value:function(e,t,n,o,i){return this.oRequest.sendRequest({cmd:"ui.showSelectMenu",items:i,rect:{left:e,top:t,width:n,height:o}})}},{key:"hideSelectMenu",value:function(){return this.oRequest.sendRequest({cmd:"ui.hideSelectMenu"})}},{key:"destroySelectMenu",value:function(){var e=this;return new Promise((function(t,n){null!==e.oRequest?e.oRequest.sendRequest({cmd:"ui.destroySelectMenu"}).then((function(e){t(e)}),(function(e){n(e)})):n()}))}},{key:"deviceConfig",value:function(e){return this.oRequest.sendRequest({cmd:"laputa.encodingDevice",param:e})}},{key:"cloudStorageConfig",value:function(e){return this.oRequest.sendRequest({cmd:"laputa.cloudStorage",param:e})}},{key:"ezvizRemoteConfig",value:function(e){return this.oRequest.sendRequest({cmd:"laputa.ezvizRemote",param:e})}},{key:"showAlarmInfoInFullScreen",value:function(e,t,n){return this.oRequest.sendRequest({cmd:"window.showAlarmInfoInFullScreen",alarmTitle:e,alarmMessage:t,alarmId:n})}},{key:"updateParentWnd",value:function(){return this.oRequest.sendRequest({cmd:"window.updateParentWnd"})}},{key:"restoreWnd",value:function(){return this.oRequest.sendRequest({cmd:"window.restoreWnd"})}},{key:"setImmediatePlaybackTime",value:function(e){return this.oRequest.sendRequest({cmd:"play.setImmediatePlaybackTime",specifyTime:e})}},{key:"setDrawStatus",value:function(e){return this.oRequest.sendRequest({cmd:"draw.setDrawStatus",enable:e})}},{key:"clearRegion",value:function(){return this.oRequest.sendRequest({cmd:"draw.clearRegion"})}},{key:"setDrawShapeInfo",value:function(e,t){return this.oRequest.sendRequest({cmd:"draw.setDrawShapeInfo",drawType:e,drawInfo:t})}},{key:"setGridInfo",value:function(e){return this.oRequest.sendRequest({cmd:"draw.setGridInfo",gridInfo:e})}},{key:"getGridInfo",value:function(){return this.oRequest.sendRequest({cmd:"draw.getGridInfo"})}},{key:"setPolygonInfo",value:function(e){return this.oRequest.sendRequest({cmd:"draw.setPolygonInfo",polygonInfo:e})}},{key:"getPolygonInfo",value:function(){return this.oRequest.sendRequest({cmd:"draw.getPolygonInfo"})}},{key:"setLineInfo",value:function(e){return this.oRequest.sendRequest({cmd:"draw.setLineInfo",lineInfo:e})}},{key:"getLineInfo",value:function(){return this.oRequest.sendRequest({cmd:"draw.getLineInfo"})}},{key:"setRectInfo",value:function(e){return this.oRequest.sendRequest({cmd:"draw.setRectInfo",rectInfo:e})}},{key:"getRectInfo",value:function(){return this.oRequest.sendRequest({cmd:"draw.getRectInfo"})}},{key:"clearShapeByType",value:function(e){return this.oRequest.sendRequest({cmd:"draw.clearShapeByType",type:e})}},{key:"sensitiveEncrypt",value:function(e,t,n){var o={cmd:"laputa.sensitiveEncrypt",encryptType:e,encryptField:t};return void 0!==n&&(o.options=n),this.oRequest.sendRequest(o)}},{key:"sendRequest",value:function(e){return this.oRequest.sendRequest(e)}},{key:"requestInterface",value:function(e){var t={cmd:"window.requestInterface"};return t.requestParams=e,this.oRequest.sendRequest(t)}},{key:"stopPlay",value:function(e){return void 0===e&&(e=-1),this.oRequest.sendRequest({cmd:"play.stopPlay",wndIndex:e})}},{key:"showRemoteConfig",value:function(e){var t=this;return e.cmd="config.showRemoteConfig",new Promise((function(n,o){null!==t.oRequest?t.oRequest.sendRequest(e).then((function(e){n(e)}),(function(e){o(e)})):o()}))}},{key:"video2Picture",value:function(){var e={cmd:"window.video2Picture"};return this.oRequest.sendRequest(e)}},{key:"picture2Video",value:function(){var e={cmd:"window.picture2Video"};return this.oRequest.sendRequest(e)}},{key:"ptzControl",value:function(e){return this.oRequest.sendRequest({cmd:"laputa.ptzControl",param:e})}},{key:"simMouseClickEvent",value:function(e,t){return this.oRequest.sendRequest({cmd:"window.simMouseClickEvent",pointX:e,pointY:t})}},{key:"us_SetMaxJobCount",value:function(e){return this.oRequest.sendRequest({cmd:"upgrade.setMaxJobCount",xml:e})}},{key:"us_GetMaxJobCount",value:function(){return this.oRequest.sendRequest({cmd:"upgrade.getMaxJobCount"})}},{key:"us_AddSchedule",value:function(e){return this.oRequest.sendRequest({cmd:"upgrade.addSchedule",xml:Si.Base64().encode(e)})}},{key:"us_DelSchedule",value:function(e){return this.oRequest.sendRequest({cmd:"upgrade.delSchedule",scheduleId:e})}},{key:"us_GetScheduleList",value:function(e){var t=this;return new Promise((function(n,o){null!==t.oRequest?t.oRequest.sendRequest({cmd:"upgrade.getScheduleList",xml:e}).then((function(e){""!==e.xml&&(e.xml=Si.Base64().decode(e.xml)),n(e)}),(function(e){o(e)})):o()}))}},{key:"us_GetSchedule",value:function(e,t){var n=this;return new Promise((function(o,i){null!==n.oRequest?n.oRequest.sendRequest({cmd:"upgrade.getSchedule",xml:t,scheduleId:e}).then((function(e){""!==e.xml&&(e.xml=Si.Base64().decode(e.xml)),o(e)}),(function(e){i(e)})):i()}))}},{key:"us_UpgradeAction",value:function(e,t){return this.oRequest.sendRequest({cmd:"upgrade.upgradeAction",xml:t,scheduleId:e})}},{key:"us_CheckUpgradeableDevice",value:function(e){return this.oRequest.sendRequest({cmd:"upgrade.checkUpgradeableDevice",param:e})}},{key:"us_CheckUpgradeableDeviceList",value:function(e){return this.oRequest.sendRequest({cmd:"upgrade.checkUpgradeableDeviceList",param:e})}},{key:"us_IsRunningAsyCheckUpgradeable",value:function(){return this.oRequest.sendRequest({cmd:"upgrade.isRunningAsyCheckUpgradeable"})}},{key:"us_StopAsyCheckUpgradeable",value:function(){return this.oRequest.sendRequest({cmd:"upgrade.stopAsyCheckUpgradeable"})}},{key:"getFishEyePTZPreset",value:function(e){return this.oRequest.sendRequest({cmd:"play.getFishEyePTZPreset",wndIndex:e})}},{key:"setFishEyePTZPreset",value:function(e,t,n){return this.oRequest.sendRequest({cmd:"play.setFishEyePTZPreset",wndIndex:e,command:t,presetInfo:n})}},{key:"controlFishEyePTZ",value:function(e,t,n,o){return this.oRequest.sendRequest({cmd:"play.controlFishEyePTZ",wndIndex:e,command:t,stop:n,speed:o})}},{key:"controlFishEyeParol",value:function(e,t,n){return this.oRequest.sendRequest({cmd:"play.controlFishEyeParol",wndIndex:e,command:t,cruisePointList:n})}},{key:"setFirstDayOfWeek",value:function(e){return this.oRequest.sendRequest({cmd:"window.setFirstDayOfWeek",firstDay:e})}},{key:"setEhomePlayInfo",value:function(e,t,n,o,i,r){return this.oRequest.sendRequest({cmd:"play.setEhomePlayInfo",guid:e,protocal:t,session:n,token:o,ip:i,port:r})}},{key:"startPlayPatch",value:function(e){if(e.length>0)for(var t=0,n=e.length;t<n;t++)e[t].areaName=Si.Base64().encode(e[t].areaName),e[t].cameraName=Si.Base64().encode(e[t].cameraName);return this.oRequest.sendRequest({cmd:"play.startPlayPatch",params:e})}},{key:"grabOpen",value:function(){var e=this;return new Promise((function(t,n){null!==e.oRequest?e.oRequest.sendRequest({cmd:"window.grabOpen"}).then((function(e){t(e)}),(function(e){n(e)})):n()}))}},{key:"setWndAutoPanState",value:function(e,t){return this.oRequest.sendRequest({cmd:"play.setWndAutoPanState",wndIndex:e,open:t})}},{key:"enablePrivileges",value:function(){return this.oRequest.sendRequest({cmd:"system.enablePrivileges"})}}]),e}();return Pa="　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　",function(){function e(n){t(this,e);var o=this;this.oOptions=Object.assign({szPluginContainer:"",cbConnectSuccess:null,cbConnectError:null,cbConnectClose:null,iServicePortStart:16960,iServicePortEnd:16969,szClassId:"55A7329E-FAAD-439a-87BC-75BAB3332E7C"},n),this.bFreeze=!1,this.bFocus=!0,this.bEmbed=Si.getCreateWndMode(),this.szWndId="",this.iCreateWndTimer=-1,this.iUpdateParentWndTimer=-1,this.bDevTool=!1,this.iVCTimeStart=-1,this.iVCTimeEnd=-1,this.oWndCover={left:0,top:0,right:0,bottom:0},this.oDocOffset={left:0,top:0},this.szTitle="",this.oWindowAttr={outerWidth:0,innerWidth:0,outerHeight:0,innerHeight:0,screenTop:0,screenLeft:0,screenX:0,screenY:0},this.iFixedResizeTimer=-1,this.fVisibilityChange=function(){if(Si.isMacOS())document.hidden?o.fHideWnd():o.fShowWnd();else if(document.hidden)o.iVCTimeStart=(new Date).getTime(),o.fHideWnd();else{o.iVCTimeEnd=(new Date).getTime();var e=Si.browser();e.chrome||e.mozilla?(o.iUpdateParentWndTimer>0&&(clearTimeout(o.iUpdateParentWndTimer),o.iUpdateParentWndTimer=-1),o.iVCTimeEnd-o.iVCTimeStart<100?o.iUpdateParentWndTimer=setTimeout((function(){o.oRequest.updateParentWnd().then((function(){o.bFreeze||o.bDevTool||o.fShowWnd()}),(function(){}))}),100):o.bFreeze||o.bDevTool||o.fShowWnd()):o.bFreeze||o.bDevTool||o.fShowWnd()}},this.fHideWnd=function(){o.oRequest.hideWnd().then((function(){}),(function(){}))},this.fShowWnd=function(){o.oRequest.showWnd().then((function(){}),(function(){}))},this.fFocus=function(){o.bFocus=!0,setTimeout((function(){o.removeGrabImage(),document.hidden||o.bFreeze||o.bDevTool||o.fShowWnd()}),200)},this.fBlur=function(){o.bFocus=!1},this.removeGrabImage=function(){if(!Si.isMacOS()){var e=null;if(""!==o.szWndId&&(e=document.getElementById(o.szWndId))){var t=e.querySelectorAll('[data-name="wc-grab-open-image"]');Array.prototype.slice.call(t).forEach((function(e){e.parentNode.removeChild(e)}))}}},this.oRequest=new Oa({szPluginContainer:this.oOptions.szPluginContainer,cbConnectSuccess:this.oOptions.cbConnectSuccess,cbConnectError:this.oOptions.cbConnectError,cbConnectClose:function(e){o.iCreateWndTimer>0&&(clearTimeout(o.iCreateWndTimer),o.iCreateWndTimer=-1),o.removeGrabImage(),o.oOptions.cbConnectClose&&o.oOptions.cbConnectClose(e)},iServicePortStart:this.oOptions.iServicePortStart,iServicePortEnd:this.oOptions.iServicePortEnd,szClassId:this.oOptions.szClassId})}return o(e,[{key:"JS_SetWindowControlCallback",value:function(e){var t=this,n={cbSelectWnd:function(t){e.cbSelectWnd&&e.cbSelectWnd(parseInt(t.wndIndex,10),t.cameraID,t.siteID,t.opendFisheye)},cbTogglePTZ:function(t){e.cbTogglePTZ&&e.cbTogglePTZ(t.cameraID,t.siteID)},cbUpdateCameraIcon:function(t){e.cbUpdateCameraIcon&&e.cbUpdateCameraIcon(t.cameraID,parseInt(t.playing,10),t.siteID)},cbGetLastError:function(t){e.cbGetLastError&&e.cbGetLastError(t.error,parseInt(t.type,10))},cbTalkUrlEmpty:function(t){e.cbTalkUrlEmpty&&e.cbTalkUrlEmpty(t.cameraID)},cbGotoPlayback:function(t){e.cbGotoPlayback&&e.cbGotoPlayback(t.cameraID,t.siteID)},cbShowDisplayInfo:function(t){e.cbShowDisplayInfo&&e.cbShowDisplayInfo(parseInt(t.videoWidth,10),parseInt(t.videoHeight,10),parseInt(t.frameRate,10))},cbPreviewWnd3DPostion:function(t){e.cbPreviewWnd3DPostion&&e.cbPreviewWnd3DPostion(parseInt(t.startX,10),parseInt(t.startY,10),parseInt(t.endX,10),parseInt(t.endY,10))},cbStopPlayAll:function(){e.cbStopPlayAll&&e.cbStopPlayAll()},cbWheelEvent:function(t){e.cbWheelEvent&&e.cbWheelEvent(parseInt(t.delta,10))},cbAlarmDetail:function(t){e.cbAlarmDetail&&e.cbAlarmDetail(t.alarmId)},cbQuitedFullScreen:function(){setTimeout((function(){t.fShowWnd()}),100)},cbManuallyClose:function(t){e.cbManuallyClose&&e.cbManuallyClose(t.cameraID,t.siteID,parseInt(t.wndIndex,10))},cbIntegrationCallBack:function(t){e.cbIntegrationCallBack&&e.cbIntegrationCallBack(t)},cbChangeStorage:function(t){e.cbChangeStorage&&e.cbChangeStorage(parseInt(t.storageType,10),t.cameraID,t.siteID)},cbFisheyeExpandChanged:function(t){e.cbFisheyeExpandChanged&&e.cbFisheyeExpandChanged(t.cameraID,t.siteID,parseInt(t.wndIndex,10),t.open)},cbGetEhomePlayInfo:function(t){e.cbGetEhomePlayInfo&&e.cbGetEhomePlayInfo(t.siteID,t.guid)},cbWndPtzControl:function(t){e.cbWndPtzControl&&e.cbWndPtzControl(parseInt(t.wndIndex,10),t.cameraID,t.command,t.speed,t.stop)},cbMessageCallBack:function(n){"menuOpen"===(n=n.data).type?""!==t.szWndId&&(document.getElementById(t.szWndId).innerHTML="<img data-name='wc-grab-open-image' src='data:image/png;base64,".concat(n.message.image,"' width='100%' height='100%' />")):"changeTitle"===n.type?-1===document.title.indexOf(t.oRequest.getRequestUUID())&&(t.szTitle=document.title,document.title=t.szTitle+Pa+t.oRequest.getRequestUUID(),setTimeout((function(){"updateParentWnd"===n.message?t.oRequest.updateParentWnd():"restoreWnd"===n.message&&t.oRequest.restoreWnd()}),300)):"changeTitleDone"===n.type?""!==t.szTitle&&(document.title=document.title.replace(Pa+t.oRequest.getRequestUUID(),"")):"splitChange"===n.type?e.cbSplitChange&&e.cbSplitChange(n.message.splitType):"showMaximized"===n.type&&e.cbShowMaximized&&e.cbShowMaximized(n.message.showMax)}};this.oRequest.setWindowControlCallback(n)}},{key:"JS_SetSadpCallback",value:function(e){var t={cbDeviceFind:null};Object.assign(t,e),this.oRequest.setSadpCallback(t)}},{key:"JS_SetSliceCallback",value:function(e){var t={cbImageSliced:function(t){e.cbImageSliced&&(""!==t.picName&&(t.picName=Si.Base64().decode(t.picName)),e.cbImageSliced(t))}};this.oRequest.setSliceCallback(t)}},{key:"JS_SetSerialCallback",value:function(e){var t={cbCardFind:function(t){e.cbCardFind&&e.cbCardFind(t)},cbFingerFind:function(t){e.cbFingerFind&&e.cbFingerFind(t.fingerPrint,t.fingerQuality)},cbImageFind:function(t){e.cbImageFind&&e.cbImageFind(t.image)},cbImageErrorFind:function(t){e.cbImageErrorFind&&e.cbImageErrorFind(t.errorModule,t.errorCode)},cbImageWndVisibleFind:function(t){e.cbImageWndVisibleFind&&e.cbImageWndVisibleFind(t.visible)}};this.oRequest.setSerialCallback(t)}},{key:"JS_SetUIControlCallback",value:function(e){var t={cbClickMenuItem:function(t){e.cbClickMenuItem&&e.cbClickMenuItem(t.itemIndex)},cbMenuMouseIn:function(){e.cbMenuMouseIn&&e.cbMenuMouseIn()},cbMenuMouseOut:function(){e.cbMenuMouseOut&&e.cbMenuMouseOut()}};this.oRequest.setUIControlCallback(t)}},{key:"JS_SetUpgradeCallback",value:function(e){var t={cbCheckUpgrade:function(t){e.cbCheckUpgrade&&e.cbCheckUpgrade(t)}};this.oRequest.setUpgradeCallback(t)}},{key:"JS_CheckVersion",value:function(e){var t=this.oRequest.getServiceVersion(),n=[],o=[];""!==t&&(n=(t=t.replace(/,[\s]*/g,".")).split(".")),""!==e&&(o=(e=e.replace(/,[\s]*/g,".")).split("."));var i=!1;if(o.length===n.length)for(var r=0,a=n.length;r<a;r++)if(parseInt(o[r],10)!==parseInt(n[r],10)){if(parseInt(o[r],10)>parseInt(n[r],10)){i=!0;break}break}return i}},{key:"JS_StartService",value:function(e,t){return this.oRequest.startService(e,t)}},{key:"JS_StopService",value:function(e){return this.oRequest.stopService(e)}},{key:"JS_Disconnect",value:function(){return this.oRequest.disconnect()}},{key:"JS_OpenDirectory",value:function(e){return this.oRequest.openDirectory(e)}},{key:"JS_OpenFile",value:function(e,t,n){return this.oRequest.openFile(e,t,n)}},{key:"JS_SelectDirectory",value:function(e,t){return this.oRequest.selectDirectory(e,t)}},{key:"JS_SelectFile",value:function(e,t,n){return this.oRequest.selectFile(e,t,n)}},{key:"JS_GetLocalConfig",value:function(e){return this.oRequest.getLocalConfig(e)}},{key:"JS_SetLocalConfig",value:function(e){return this.oRequest.setLocalConfig(e)}},{key:"JS_SetDocOffset",value:function(e){return e&&(this.oDocOffset=e),!0}},{key:"JS_SetWindowAttr",value:function(e){return e&&(this.oWindowAttr=e),!0}},{key:"JS_CreateWnd",value:function(e,t,n,o){var i=this;this.szWndId=e,void 0!==(o=o||{}).bEmbed&&(this.bEmbed=o.bEmbed);var r=!0;return void 0!==o.bActiveXParentWnd&&(r=o.bActiveXParentWnd),new Promise((function(a,s){var u=document.getElementById(e);if(u){var c=null,l="",d=i.oRequest.getRequestUUID();void 0===o.cbSetDocTitle?(c=window.top,l=c.document.title,c.document.title=l+Pa+d):o.cbSetDocTitle(d),i.iCreateWndTimer=setTimeout((function(){var e="";if(Si.browser().msie?e="IEFrame":Si.browser().chrome?e="Chrome":Si.browser().safari&&(e=l),!i.bDevTool){var f=Si.getDevicePixelRatio(),h=Si.getWndPostion(u,i.bEmbed,i.oWindowAttr);h.left+=Math.round(i.oDocOffset.left*f),h.top+=Math.round(i.oDocOffset.top*f),t=Math.round(t*f),n=Math.round(n*f),i.oRequest.createWnd(h.left,h.top,t,n,e,i.bEmbed,r).then((function(){void 0===o.cbSetDocTitle&&(c.document.title=c.document.title.replace(Pa+d,"")),a()}),(function(e){void 0===o.cbSetDocTitle&&(c.document.title=c.document.title.replace(Pa+d,"")),5001===e.errorCode?(document.hidden||i.bFreeze||!i.bFocus||i.fShowWnd(),a()):s(e)}))}}),300),document.addEventListener("visibilitychange",i.fVisibilityChange,!1),window.addEventListener("focus",i.fFocus),window.addEventListener("blur",i.fBlur)}else s()}))}},{key:"JS_ShowWnd",value:function(){this.bFreeze=!1,document.hidden||this.bDevTool||this.fShowWnd()}},{key:"JS_HideWnd",value:function(){this.bFreeze=!0,this.fHideWnd()}},{key:"JS_DestroyWnd",value:function(){return document.removeEventListener("visibilitychange",this.fVisibilityChange,!1),window.removeEventListener("focus",this.fFocus),window.removeEventListener("blur",this.fBlur),this.oRequest.destroyWnd()}},{key:"JS_Resize",value:function(e,t,n){var o=this,i=null,r=e,a=t;if(""!==this.szWndId&&(i=document.getElementById(this.szWndId)),i){var s=Si.getWndPostion(i,this.bEmbed,this.oWindowAttr),u=Si.getDevicePixelRatio();s.left+=Math.round(this.oDocOffset.left*u),s.top+=Math.round(this.oDocOffset.top*u),(!Si.browser().msie||Si.browser().msie&&"11.0"===Si.browser().version)&&(this.oWndCover.left>0&&(s.left+=Math.round(this.oWndCover.left*u),e-=this.oWndCover.left),this.oWndCover.top>0&&(s.top+=Math.round(this.oWndCover.top*u),t-=this.oWndCover.top),this.oWndCover.right>0&&(e-=this.oWndCover.right),this.oWndCover.bottom>0&&(t-=this.oWndCover.bottom)),e=Math.round(e*u),t=Math.round(t*u),this.oRequest.setWndGeometry(s.left,s.top,e,t),(Si.browser().msie&&"11.0"===Si.browser().version||!Si.isWindows())&&(n&&n.bFixed?this.iFixedResizeTimer=-1:(this.iFixedResizeTimer>-1&&(clearTimeout(this.iFixedResizeTimer),this.iFixedResizeTimer=-1),this.iFixedResizeTimer=setTimeout((function(){o.JS_Resize(r,a,{bFixed:!0})}),300)))}}},{key:"JS_SetWndCover",value:function(e,t){var n=Si.getDevicePixelRatio();return(!Si.browser().msie||Si.browser().msie&&"11.0"===Si.browser().version)&&("left"===e?this.oWndCover.left=t:"top"===e?this.oWndCover.top=t:"right"===e?this.oWndCover.right=t:"bottom"===e&&(this.oWndCover.bottom=t)),t=Math.round(t*n),this.oRequest.setWndCover(e,t)}},{key:"JS_CuttingPartWindow",value:function(e,t,n,o,i){var r=Si.getDevicePixelRatio();return e=Math.round(e*r),t=Math.round(t*r),n=Math.round(n*r),o=Math.round(o*r),i=Math.round(i*r),this.oRequest.cuttingPartWindow(e,t,n,o,i)}},{key:"JS_RepairPartWindow",value:function(e,t,n,o,i){var r=Si.getDevicePixelRatio();return e=Math.round(e*r),t=Math.round(t*r),n=Math.round(n*r),o=Math.round(o*r),i=Math.round(i*r),this.oRequest.repairPartWindow(e,t,n,o,i)}},{key:"JS_ChangePlayMode",value:function(e){return this.oRequest.changePlayMode(e)}},{key:"JS_SetLanguageType",value:function(e){return this.oRequest.setLanguageType(e)}},{key:"JS_InitLoginInfo",value:function(e){return this.oRequest.initLoginInfo(e)}},{key:"JS_SetTranslateFile",value:function(e){return this.oRequest.setTranslateFile(e)}},{key:"JS_SwitchToSimple",value:function(e){return this.oRequest.switchToSimple(e)}},{key:"JS_SetVsmToken",value:function(e){return this.oRequest.setVsmToken(e)}},{key:"JS_Play",value:function(e,t,n,o,i,r,a,s,u){return this.oRequest.startPlay(e,t,n,o,i,r,a,s,u)}},{key:"JS_Enable3DZoom",value:function(e){return this.oRequest.setPreview3DPosition(e)}},{key:"JS_StopTotal",value:function(){return this.oRequest.stopTotal()}},{key:"JS_SetDragMode",value:function(e){return this.oRequest.setDragMode(e)}},{key:"JS_ShowErrorInfoInFullScreen",value:function(e){return this.oRequest.showErrorInfoInFullScreen(e)}},{key:"JS_SetNumberOfWindows",value:function(e){return this.oRequest.setNumberOfWindows(e)}},{key:"JS_InitCardReader",value:function(e){return this.oRequest.initCardReader(e)}},{key:"JS_UnInitCardReader",value:function(){return this.oRequest.unInitCardReader()}},{key:"JS_StartAutoMode",value:function(){return this.oRequest.startAutoMode()}},{key:"JS_StopAutoMode",value:function(){return this.oRequest.stopAutoMode()}},{key:"JS_InitFingerprint",value:function(e){return this.oRequest.initFingerprint(e)}},{key:"JS_UnInitFingerprint",value:function(){return this.oRequest.unInitFingerprint()}},{key:"JS_StartCollectFingerprint",value:function(){return this.oRequest.startCollectFingerprint()}},{key:"JS_StopCollectFingerprint",value:function(){return this.oRequest.stopCollectFingerprint()}},{key:"JS_IsCollectingFingerprint",value:function(){return this.oRequest.isCollectingFingerprint()}},{key:"JS_InitVideocapture",value:function(e){return this.oRequest.initVideocapture(e)}},{key:"JS_UnInitVideocapture",value:function(){return this.oRequest.unInitVideocapture()}},{key:"JS_RegisterDeviceType",value:function(e){return this.oRequest.registerDeviceType(e)}},{key:"JS_ActiveOnlineDevice",value:function(e,t){return this.oRequest.activeOnlineDevice(e,t)}},{key:"JS_RefreshDeviceList",value:function(){return this.oRequest.refreshDeviceList()}},{key:"JS_ModifyDeviceNetParam",value:function(e,t,n,o,i,r,a){return this.oRequest.modifyDeviceNetParam(e,t,n,o,i,r,a)}},{key:"JS_ExportKeyFile",value:function(e){return this.oRequest.exportKeyFile(e)}},{key:"JS_ImportKeyFile",value:function(){return this.oRequest.importKeyFile()}},{key:"JS_ResetPassword",value:function(e,t,n,o){return this.oRequest.resetPassword(e,t,n,o)}},{key:"JS_UploadPicture",value:function(e){return this.oRequest.uploadPicture(e)}},{key:"JS_ShowSelectMenu",value:function(e,t,n,o,i){var r=document.getElementById(e);if(r){var a=Si.getWndPostion(r,!1,this.oWindowAttr);"center"===i?a.left-=Math.round((t-r.offsetWidth)/2):"right"===i&&(a.left-=Math.round(t-r.offsetWidth));var s=Si.getDevicePixelRatio();t=Math.round(t*s),n=Math.round(n*s);var u=1*window.getComputedStyle(r).height.slice(0,-2),c=Math.round(u*s);this.oRequest.showSelectMenu(a.left,a.top+c,t,n,o)}}},{key:"JS_HideSelectMenu",value:function(){this.oRequest.hideSelectMenu()}},{key:"JS_DestroySelectMenu",value:function(){return this.oRequest.destroySelectMenu()}},{key:"JS_DeviceConfig",value:function(e){return this.oRequest.deviceConfig(e)}},{key:"JS_CloudStorageConfig",value:function(e){return this.oRequest.cloudStorageConfig(e)}},{key:"JS_EzvizRemoteConfig",value:function(e){return this.oRequest.ezvizRemoteConfig(e)}},{key:"JS_ShowAlarmInfoInFullScreen",value:function(e,t,n){return this.oRequest.showAlarmInfoInFullScreen(e,t,n)}},{key:"JS_SetImmediatePlaybackTime",value:function(e){return this.oRequest.setImmediatePlaybackTime(e)}},{key:"JS_SetDrawStatus",value:function(e){return this.oRequest.setDrawStatus(e)}},{key:"JS_ClearRegion",value:function(){return this.oRequest.clearRegion()}},{key:"JS_SetDrawShapeInfo",value:function(e,t){return this.oRequest.setDrawShapeInfo(e,t)}},{key:"JS_SetGridInfo",value:function(e){return this.oRequest.setGridInfo(e)}},{key:"JS_GetGridInfo",value:function(){return this.oRequest.getGridInfo()}},{key:"JS_SetPolygonInfo",value:function(e){return this.oRequest.setPolygonInfo(e)}},{key:"JS_GetPolygonInfo",value:function(){return this.oRequest.getPolygonInfo()}},{key:"JS_SetLineInfo",value:function(e){return this.oRequest.setLineInfo(e)}},{key:"JS_GetLineInfo",value:function(){return this.oRequest.getLineInfo()}},{key:"JS_SetRectInfo",value:function(e){return this.oRequest.setRectInfo(e)}},{key:"JS_GetRectInfo",value:function(){return this.oRequest.getRectInfo()}},{key:"JS_ClearShapeByType",value:function(e){return this.oRequest.clearShapeByType(e)}},{key:"JS_SensitiveEncrypt",value:function(e,t,n){return this.oRequest.sensitiveEncrypt(e,t,n)}},{key:"JS_SendRequest",value:function(e){return this.oRequest.sendRequest(e)}},{key:"JS_RequestInterface",value:function(e){return this.oRequest.requestInterface(e)}},{key:"JS_StopPlay",value:function(e){return this.oRequest.stopPlay(e)}},{key:"JS_ShowRemoteConfig",value:function(e){return this.oRequest.showRemoteConfig(e)}},{key:"JS_Video2Picture",value:function(){return this.oRequest.video2Picture()}},{key:"JS_Picture2Video",value:function(){return this.oRequest.picture2Video()}},{key:"JS_PtzControl",value:function(e){return this.oRequest.ptzControl(e)}},{key:"JS_SimMouseClickEvent",value:function(e,t){return this.oRequest.simMouseClickEvent(e,t)}},{key:"JS_US_SetMaxJobCount",value:function(e){return this.oRequest.us_SetMaxJobCount(e)}},{key:"JS_US_GetMaxJobCount",value:function(){return this.oRequest.us_GetMaxJobCount()}},{key:"JS_US_AddSchedule",value:function(e){return this.oRequest.us_AddSchedule(e)}},{key:"JS_US_DelSchedule",value:function(e){return this.oRequest.us_DelSchedule(e)}},{key:"JS_US_GetScheduleList",value:function(e){return this.oRequest.us_GetScheduleList(e)}},{key:"JS_US_GetSchedule",value:function(e,t){return this.oRequest.us_GetSchedule(e,t)}},{key:"JS_US_UpgradeAction",value:function(e,t){return this.oRequest.us_UpgradeAction(e,t)}},{key:"JS_US_CheckUpgradeableDevice",value:function(e){return this.oRequest.us_CheckUpgradeableDevice(e)}},{key:"JS_US_CheckUpgradeableDeviceList",value:function(e){return this.oRequest.us_CheckUpgradeableDeviceList(e)}},{key:"JS_US_IsRunningAsyCheckUpgradeable",value:function(){return this.oRequest.us_IsRunningAsyCheckUpgradeable()}},{key:"JS_US_StopAsyCheckUpgradeable",value:function(){return this.oRequest.us_StopAsyCheckUpgradeable()}},{key:"JS_GetFishEyePTZPreset",value:function(e){return this.oRequest.getFishEyePTZPreset(e)}},{key:"JS_SetFishEyePTZPreset",value:function(e,t,n){return this.oRequest.setFishEyePTZPreset(e,t,n)}},{key:"JS_ControlFishEyePTZ",value:function(e,t,n,o){return this.oRequest.controlFishEyePTZ(e,t,n,o)}},{key:"JS_ControlFishEyeParol",value:function(e,t,n){return this.oRequest.controlFishEyeParol(e,t,n)}},{key:"JS_SetFirstDayOfWeek",value:function(e){return this.oRequest.setFirstDayOfWeek(e)}},{key:"JS_SetEhomePlayInfo",value:function(e,t,n,o,i,r){return this.oRequest.setEhomePlayInfo(e,t,n,o,i,r)}},{key:"JS_PlayPatch",value:function(e){return this.oRequest.startPlayPatch(e)}},{key:"JS_SetWndAutoPanState",value:function(e,t){return this.oRequest.setWndAutoPanState(e,t)}},{key:"JS_EnablePrivileges",value:function(){return this.oRequest.enablePrivileges()}}],[{key:"JS_WakeUp",value:function(e){var t=document.createElement("iframe");t.style.display="none",t.src=e,document.body.appendChild(t),setTimeout((function(){document.body.removeChild(t)}),3e3)}}]),e}()}();
