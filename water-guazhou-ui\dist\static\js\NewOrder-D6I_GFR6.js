import{d as U,c as F,r as u,S as f,b as d,u as L,o as O,g as y,n as h,p as k,q as g,F as w,an as T,a9 as I,e6 as C,D,_ as q,C as S}from"./index-r0dFAfgr.js";import{_ as P}from"./FormMap-BGaXSqQF.js";import{b as M,g as V,a as W,W as B}from"./config-DqqM5K5L.js";import{I as N}from"./utils-D5nxoMq3.js";import{P as E,b as j}from"./index-CpGhZCTT.js";import"./ArcView-DpMnCY82.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./widget-BcWKanF2.js";import"./pe-B8dP0-Ut.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./TileLayer-B5vQ99gG.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./commonProperties-DqNQ4F00.js";import"./project-DUuzYgGl.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./IdentifyResult-4DxLVhTm.js";import"./ViewHelper-BGCZjxXH.js";import"./fieldconfig-Bk3o1wi7.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./LayerHelper-Cn-iiqxI.js";import"./pipe-nogVzCHG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./URLHelper-B9aplt5w.js";import"./ArcPipe.vue_vue_type_script_setup_true_lang-ClsVvV2P.js";import"./DateFormatter-Bm9a68Ax.js";import"./OutsideWorkOrder-C6s8joBt.js";const H={class:"wrapper",style:{"overflow-y":"auto"}},R={class:"form-box"},z={key:0,style:{width:"100%",height:"250px"}},A=U({__name:"NewOrder",setup(G){var n,c;const o=F();let m;const p=u({coordinate:void 0,WorkOrderProcessLevelList:[]});function b(){j("1").then(e=>{p.WorkOrderProcessLevelList=I(e.data.data||[],"children",{label:"name",value:"name"})})}const l=u({group:[{fields:[{type:"input",label:"标题",field:"title",rules:[{required:!0,message:"请输入标题"}]},{xs:6,type:"select",label:"来源",field:"source",options:M(),rules:[{required:!0,message:"请选择来源"}]},{xs:6,type:"select",label:"紧急程度",field:"level",options:V(),rules:[{required:!0,message:"请选择紧急程度"}]},{xs:6,type:"select-tree",label:"工单类型",field:"type",options:W(),rules:[{required:!0,message:"请选择工单类型"}]},{field:"address",label:"地址",type:"textarea",rules:[{required:!0,message:"请输入地址"}]},{field:"coordinate",label:"坐标",type:"form-map",showInput:!0,rules:[{required:!0,message:"请输入地址"}],onChange:e=>{const t={lon:e[0]||"0",lat:e[1]||"0"};o.value&&(o.value.dataForm.coordinate=e,v(()=>{N(t).then(r=>{o.value&&(o.value.dataForm.address=r.data.result.formatted_address||"")})},500))}},{field:"remark",label:"描述",type:"textarea"}],fieldset:{desc:"工单信息",type:"underline"}},{fieldset:{desc:"分派信息",type:"underline"},fields:[{type:"switch",label:"是否直接派发",field:"isDirectDispatch"},{handleHidden:(e,t,r)=>{r.hidden=!e.isDirectDispatch},label:"处理人",xs:6,field:"stepProcessUserId",type:"select",options:[]},{handleHidden:(e,t,r)=>{r.hidden=!e.isDirectDispatch},xs:6,field:"processLevelLabel",label:"处理级别",type:"select",options:B(),onChange:e=>{var r;const t=p.WorkOrderProcessLevelList.find(a=>a.value===e);(r=o.value)!=null&&r.dataForm&&(o.value.dataForm={...o.value.dataForm,processLevel:t.dayTime*1440+t.hourTime*60+t.minuteTime})}}]},{fields:[{field:"ccUserId",label:"抄送人",type:"select",multiple:!0,options:[]}],fieldset:{desc:"抄送",type:"underline"}},{fields:[{field:"imgUrl",label:"现场图片",type:"image",returnType:"comma",limit:2,multiple:!0,accept:"image/*"},{field:"videoUrl",label:"现场视频",type:"file",limit:2,returnType:"comma",accept:"video/*",tips:"只能上传视频文件,最多上传2个，大小不能超过100M"},{field:"audioUrl",label:"现场录音",type:"file",limit:2,returnType:"comma",accept:"audio/*",tips:"只能上传音频文件,最多上传2个，大小不能超过4M"},{field:"otherFileUrl",label:"其它附件",type:"file",limit:2,returnType:"comma",tips:"只能上传文件,最多上传2个，大小不能超过4M"}]},{fields:[{type:"btn-group",btns:[{perm:!0,type:"primary",isTextBtn:!1,loading:()=>l.submitting===!0,text:"保存",click:()=>{var e;(e=o.value)==null||e.Submit()}},{perm:!0,type:"default",isTextBtn:!1,loading:()=>l.submitting===!0,text:"重置",click:()=>{f("确定重置表单？","提示信息").then(()=>{var e;(e=o.value)==null||e.resetForm()}).catch(()=>{})}}]}]}],submit:e=>{f("确定提交？","提示信息").then(async()=>{var t,r,a,i;l.submitting=!0;try{e.ccUserId=e.ccUserId.join(",");const s=await E({processLevel:0,...e,coordinate:(t=p.coordinate)==null?void 0:t.join(",")});((r=s.data)==null?void 0:r.code)===200?(d.success("提交成功"),(a=o.value)==null||a.resetForm()):d.error(((i=s.data)==null?void 0:i.err)||"提交失败")}catch(s){d.error(s.message||"提交失败")}l.submitting=!1}).catch(()=>{})},defaultValue:{isDirectDispatch:!0,organizerId:(c=(n=L().user)==null?void 0:n.id)==null?void 0:c.id}}),v=(e,t)=>{m&&clearTimeout(m),m=setTimeout(()=>{e()},t)},_=async()=>{const e=await C({authType:"CUSTOMER_USER"}),t=["uploadUserId","stepProcessUserId","ccUserId"];l.group.map(r=>{r.fields.filter(i=>i.field&&t.indexOf(i.field)!==-1).map(i=>{i.options=e.data.map(s=>({label:s.firstName,value:D(s.id.id)}))})})};return O(()=>{_(),b()}),(e,t)=>{const r=P,a=q;return y(),h("div",H,[k("div",R,[g(a,{ref_key:"refForm",ref:o,config:l},{fieldSlot:w(({config:i,row:s})=>[i.field==="coordinate"?(y(),h("div",z,[g(r,{modelValue:p.coordinate,"onUpdate:modelValue":t[0]||(t[0]=x=>p.coordinate=x),row:s,"show-input":i.showInput,disabled:i.disabled,readonly:i.readonly,onChange:i.onChange},null,8,["modelValue","row","show-input","disabled","readonly","onChange"])])):T("",!0)]),_:1},8,["config"])])])}}}),gt=S(A,[["__scopeId","data-v-a2102b13"]]);export{gt as default};
