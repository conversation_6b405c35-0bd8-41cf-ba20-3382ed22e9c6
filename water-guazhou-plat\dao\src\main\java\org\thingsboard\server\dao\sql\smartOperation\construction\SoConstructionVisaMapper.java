package org.thingsboard.server.dao.sql.smartOperation.construction;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionVisa;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionVisaContainer;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionVisaPageRequest;

import java.util.List;

@Mapper
public interface SoConstructionVisaMapper extends BaseMapper<SoConstructionVisa> {
    List<SoConstructionVisaContainer> findByPage(SoConstructionVisaPageRequest request);

    boolean update(SoConstructionVisa entity);

    boolean updateFully(SoConstructionVisa entity);

    boolean isCodeExists(@Param("code") String code, @Param("tenantId") String tenantId, @Param("id") String id);

    long countByPage(SoConstructionVisaPageRequest request);

    String getConstructionCodeById(String id);

}
