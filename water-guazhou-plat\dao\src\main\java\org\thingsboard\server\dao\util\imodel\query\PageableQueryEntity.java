package org.thingsboard.server.dao.util.imodel.query;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class PageableQueryEntity<T> extends TimeableQueryEntity implements IPage<T> {

    private PageableQueryEntityType pageableQueryEntityQueryType = PageableQueryEntityType.AUTO;

    public enum PageableQueryEntityType {
        AUTO,
        MANUAL
    }

    /**
     * 查询数据列表
     */
    private List<T> records = Collections.emptyList();

    /**
     * 总数
     */
    private long total = 0;

    /**
     * 每页显示条数，默认 10
     */
    private long size = 10;

    /**
     * 当前页
     */
    private long page = 1;

    /**
     * 自定义排序字段信息
     */
    private final List<OrderItem> orders = new ArrayList<>();

    /**
     * 排序表达式
     */
    private String orderExpression = "";

    /**
     * 是否存在上一页
     *
     * @return true / false
     */
    public boolean hasPrevious() {
        return this.page > 1;
    }

    /**
     * 是否存在下一页
     *
     * @return true / false
     */
    public boolean hasNext() {
        return this.page < this.getPages();
    }

    @Override
    public List<T> getRecords() {
        return this.records;
    }

    @Override
    public IPage<T> setRecords(List<T> records) {
        this.records = records;
        return this;
    }

    @Override
    public long getTotal() {
        return this.total;
    }

    @Override
    public IPage<T> setTotal(long total) {
        this.total = total;
        return this;
    }

    @Override
    public long getSize() {
        return this.size;
    }

    @Override
    public IPage<T> setSize(long size) {
        this.size = size;
        return this;
    }

    @Override
    public long getCurrent() {
        return size == 0 ? 0 : this.page;
    }

    @Override
    public IPage<T> setCurrent(long current) {
        this.page = current;
        return this;
    }

    public long getPage() {
        return this.page;
    }

    public IPage<T> setPage(long current) {
        this.page = current;
        return this;
    }

    @Override
    public String countId() {
        return null;
    }

    @Override
    public Long maxLimit() {
        return null;
    }

    /**
     * 查找 order 中正序排序的字段数组
     *
     * @param filter 过滤器
     * @return 返回正序排列的字段数组
     */
    private String[] mapOrderToArray(Predicate<OrderItem> filter) {
        List<String> columns = new ArrayList<>(orders.size());
        orders.forEach(i -> {
            if (filter.test(i)) {
                columns.add(i.getColumn());
            }
        });
        return columns.toArray(new String[0]);
    }

    /**
     * 移除符合条件的条件
     *
     * @param filter 条件判断
     */
    private void removeOrder(Predicate<OrderItem> filter) {
        for (int i = orders.size() - 1; i >= 0; i--) {
            if (filter.test(orders.get(i))) {
                orders.remove(i);
            }
        }
    }

    /**
     * 添加新的排序条件，构造条件可以使用工厂：{@link OrderItem#OrderItem(String, boolean)}
     *
     * @param items 条件
     * @return 返回分页参数本身
     */
    public IPage<T> addOrder(OrderItem... items) {
        orders.addAll(Arrays.asList(items));
        return this;
    }

    /**
     * 添加新的排序条件，构造条件可以使用工厂：{@link OrderItem#OrderItem(String, boolean)}
     *
     * @param items 条件
     * @return 返回分页参数本身
     */
    public IPage<T> addOrder(List<OrderItem> items) {
        orders.addAll(items);
        return this;
    }

    @Override
    public List<OrderItem> orders() {
        return Stream.concat(
                orders.stream(),
                Arrays.stream(getOrderExpression().split(";"))
                        .filter(x -> x.length() > 0)
                        .map(segment -> {
                            int lastIndex = segment.length() - 1;
                            boolean decrease = segment.charAt(lastIndex) == '-';
                            String fieldName = segment.replaceAll("[^\\w.]", "");
                            return new OrderItem(StringUtils.camelToUnderline(fieldName), !decrease);
                        })
        ).collect(Collectors.toList());
    }

    @Override
    public boolean optimizeCountSql() {
        return true;
    }

    public boolean isOptimizeCountSql() {
        return optimizeCountSql();
    }

    @Override
    public boolean isSearchCount() {
        return total == 0 && size > -1;
    }

    public long getOffset() {
        return offset();
    }

    public String getOrderExpression() {
        return orderExpression;
    }

    public void setOrderExpression(String orderExpression) {
        this.orderExpression = orderExpression;
    }

    public PageableQueryEntityType type() {
        return pageableQueryEntityQueryType;
    }

    public void type(PageableQueryEntityType type) {
        this.pageableQueryEntityQueryType = type;
    }
}
