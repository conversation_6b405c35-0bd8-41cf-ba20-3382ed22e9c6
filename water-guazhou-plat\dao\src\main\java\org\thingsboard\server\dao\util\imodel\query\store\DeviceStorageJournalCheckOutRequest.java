package org.thingsboard.server.dao.util.imodel.query.store;


import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.query.AwareCurrentUserUUID;
import org.thingsboard.server.dao.util.imodel.query.AwareTenantUUID;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

import java.util.List;

@Getter
@Setter
public class DeviceStorageJournalCheckOutRequest implements AwareTenantUUID, AwareCurrentUserUUID {
    // 出库单id
    @NotNullOrEmpty
    private String storeOutId;

    // 出库台账
    @NotNullOrEmpty
    private List<String> checkouts;

    private String tenantId;

    private String currentUserId;

    @Override
    public void tenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public boolean valid() {
        return checkouts != null;
    }

    @Override
    public void currentUserId(String uuid) {
        currentUserId = uuid;
    }

}
