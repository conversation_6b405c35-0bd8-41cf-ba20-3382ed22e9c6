package org.thingsboard.server.dao.util.imodel.query.smartProduction.pumpHouse;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartProduction.pumpHouse.PumpManage;
import org.thingsboard.server.dao.util.TimeUtils;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

@Getter
@Setter
public class PumpManageSaveRequest extends SaveRequest<PumpManage> {
    // 所属泵房ID
    @NotNullOrEmpty
    private String pumpRoomId;

    // 设备编码
    @NotNullOrEmpty
    private String code;

    // 设备名称
    @NotNullOrEmpty
    private String name;

    // 设备简称
    @NotNullOrEmpty
    private String nickname;

    // 泵个数
    @NotNullOrEmpty
    private Integer pumpNum;

    // 厂家名称
    @NotNullOrEmpty
    private String companyName;

    // 型号
    @NotNullOrEmpty
    private String model;

    // 安装人名称
    @NotNullOrEmpty
    private String installUserName;

    // 安装时间
    @NotNullOrEmpty
    private String installDate;

    // 备注
    private String remark;

    // 性能参数
    @NotNullOrEmpty
    private String performanceParameters;

    @Override
    protected PumpManage build() {
        PumpManage entity = new PumpManage();
        entity.setCreator(currentUserUUID());
        entity.setCreateTime(createTime());
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    @Override
    protected PumpManage update(String id) {
        PumpManage entity = new PumpManage();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(PumpManage entity) {
        entity.setPumpRoomId(pumpRoomId);
        entity.setCode(code);
        entity.setName(name);
        entity.setNickname(nickname);
        entity.setPumpNum(pumpNum);
        entity.setCompanyName(companyName);
        entity.setModel(model);
        entity.setInstallUserName(installUserName);
        entity.setInstallDate(TimeUtils.defaultIfNull(installDate, null));
        entity.setRemark(remark);
        entity.setPerformanceParameters(performanceParameters);
    }
}