<template>
  <div style="width: calc(100% - 20px)">
    <Form :key="key" ref="refForm" :config="Form1"></Form>
    <div class="tcharts">
      <div class="charts">
        <VChart
          ref="refChart2"
          autoresize
          :theme="useAppStore().isDark ? 'dark' : 'light'"
          :option="option1"
        ></VChart>
      </div>
    </div>
    <Form :key="key" ref="refForm" :config="Form2"></Form>
  </div>
</template>

<script lang="ts" setup>
import { 折线图 } from './charts';
import { useAppStore } from '@/store';
import {
  getCircuit,
  getCircuitList
} from '@/api/equipment_assets/ledgerManagement';

const props = defineProps<{ id: string }>();

const key = ref(new Date().toString());

const option1 = ref(折线图([], '巡检次数'));

const Form1 = reactive<IDialogFormConfig>({
  title: '',
  labelWidth: '100px',

  defaultValue: {},
  group: [
    {
      fields: [
        {
          type: 'divider',
          text: '巡检信息'
        },
        {
          xl: 8,
          type: 'text',
          label: '巡检次数:',
          field: 'count'
        },
        {
          xl: 8,
          type: 'text',
          label: '最近巡检:',
          field: 'latestMaintainTime'
        }
      ]
    }
  ]
});

const Form2 = reactive<IDialogFormConfig>({
  title: '',
  labelWidth: '100px',

  defaultValue: {},
  group: [
    {
      fields: [
        {
          type: 'divider',
          text: '巡检计划'
        },
        {
          type: 'table',
          field: 'faultReportCList',
          config: {
            indexVisible: true,
            height: '350px',
            dataList: computed(() => data.value) as any,
            columns: [
              { label: '计划名称', prop: 'name' },
              { label: '限制时间', prop: 'limitDays' },
              { label: '循环周期', prop: 'cycleDays' },
              { label: '下一次巡检时间', prop: 'nextTime' },
              { label: '任务人', prop: 'userName' }
            ],
            pagination: {
              hide: true
            }
          }
        }
      ]
    }
  ]
});

const data = ref([]);

const refreshData = async () => {
  // 获取巡检信息
  getCircuit(props.id).then((res) => {
    const row = res.data.data || {};
    for (const i in row) {
      if (row[i] === undefined || row[i] === null) row[i] = ' ';
    }
    Form1.defaultValue = { ...row };
    option1.value = 折线图(row.nowYearCircuit || [], '巡检次数');
    setTimeout(() => {
      key.value = new Date().toString();
    }, 1000);
  });
  // 获取巡检记录
  getCircuitList(props.id).then((res) => {
    data.value = res.data.data.data || [];
  });
};

onMounted(() => {
  refreshData();
});
</script>

<style lang="scss" scoped>
.tcharts {
  display: flex;
  .charts {
    height: 300px;
    flex: 1;
  }
}
</style>
