/**
 * Copyright © 2016-2019 The Thingsboard Authors
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.service.mail;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.yunpian.sdk.YunpianClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.velocity.app.VelocityEngine;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.MessageSource;
import org.springframework.core.NestedRuntimeException;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.ui.velocity.VelocityEngineUtils;
import org.thingsboard.rule.engine.api.MailService;
import org.thingsboard.server.common.data.*;
import org.thingsboard.server.common.data.VO.AlarmLinkedUser;
import org.thingsboard.server.common.data.VO.TenantSmsKey;
import org.thingsboard.server.common.data.alarm.AlarmReport;
import org.thingsboard.server.common.data.constantsAttribute.PropAttribute;
import org.thingsboard.server.common.data.exception.ThingsboardErrorCode;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.EntityId;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.kv.AttributeKvEntry;
import org.thingsboard.server.common.data.kv.TsKvEntry;
import org.thingsboard.server.common.data.utils.DateUtils;
import org.thingsboard.server.dao.attributes.AttributesService;
import org.thingsboard.server.dao.exception.IncorrectParameterException;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.model.sql.MsgLogEntity;
import org.thingsboard.server.dao.msgLog.MsgLogService;
import org.thingsboard.server.dao.settings.AdminSettingsService;
import org.thingsboard.server.dao.user.UserCredentialsDao;
import org.thingsboard.server.dao.user.UserService;
import org.thingsboard.server.dao.util.mapping.JacksonUtil;

import javax.annotation.PostConstruct;
import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.*;

// @Service
@Slf4j
public class DefaultMailService implements MailService {

    public static final String MAIL_PROP = "mail.";
    public static final String TARGET_EMAIL = "targetEmail";
    public static final String UTF_8 = "UTF-8";
    private static final String ENCODING = "UTF-8";
    @Autowired
    private MessageSource messages;

    @Autowired
    @Qualifier("velocityEngine")
    private VelocityEngine engine;

    @Autowired
    private UserService userService;
    @Autowired
    private UserCredentialsDao userCredentialsDao;

    @Autowired
    private MsgLogService msgLogService;

    @Autowired
    private AttributesService attributesService;

    private JavaMailSenderImpl mailSender;

    private String mailFrom;

    @Autowired
    private AdminSettingsService adminSettingsService;

    @PostConstruct
    private void init() {
        updateMailConfiguration();
    }

    @Override
    public void updateMailConfiguration() {
        AdminSettings settings = adminSettingsService.findAdminSettingsByKey(new TenantId(EntityId.NULL_UUID), "mail");
        if (settings != null) {
            JsonNode jsonConfig = settings.getJsonValue();
            mailSender = createMailSender(jsonConfig);
            mailFrom = jsonConfig.get("mailFrom").asText();
        } else {
            throw new IncorrectParameterException("Failed to date mail configuration. Settings not found!");
        }
    }

    private JavaMailSenderImpl createMailSender(JsonNode jsonConfig) {
        JavaMailSenderImpl mailSender = new JavaMailSenderImpl();
        mailSender.setHost(jsonConfig.get("smtpHost").asText());
        mailSender.setPort(parsePort(jsonConfig.get("smtpPort").asText()));
        mailSender.setUsername(jsonConfig.get("username").asText());
        mailSender.setPassword(jsonConfig.get("password").asText());
        mailSender.setJavaMailProperties(createJavaMailProperties(jsonConfig));
        return mailSender;
    }

    private Properties createJavaMailProperties(JsonNode jsonConfig) {
        Properties javaMailProperties = new Properties();
        String protocol = jsonConfig.get("smtpProtocol").asText();
        javaMailProperties.put("mail.transport.protocol", protocol);
        javaMailProperties.put(MAIL_PROP + protocol + ".host", jsonConfig.get("smtpHost").asText());
        javaMailProperties.put(MAIL_PROP + protocol + ".port", jsonConfig.get("smtpPort").asText());
        javaMailProperties.put(MAIL_PROP + protocol + ".timeout", jsonConfig.get("timeout").asText());
        javaMailProperties.put(MAIL_PROP + protocol + ".auth", String.valueOf(StringUtils.isNotEmpty(jsonConfig.get("username").asText())));
        javaMailProperties.put(MAIL_PROP + protocol + ".starttls.enable", jsonConfig.has("enableTls") ? jsonConfig.get("enableTls").asText() : "false");
        return javaMailProperties;
    }

    private int parsePort(String strPort) {
        try {
            return Integer.valueOf(strPort);
        } catch (NumberFormatException e) {
            throw new IncorrectParameterException(String.format("Invalid smtp port value: %s", strPort));
        }
    }

    @Override
    public void sendEmail(AlarmLinkedUser user, String subject, String message, String logType) {
        try {
            sendMail(mailSender, mailFrom, user.getEmail(), subject, message);
            createLog(user, true, DataConstants.ALARM_SEND_EMAIL, DataConstants.SEND_MSG_SUCCESS, message, logType);
        } catch (Exception e) {
            createLog(user, false, DataConstants.ALARM_SEND_EMAIL, e.getMessage(), message, logType);
        }
    }

    @Override
    public void sendTestMail(JsonNode jsonConfig, String email) throws ThingsboardException {
        JavaMailSenderImpl testMailSender = createMailSender(jsonConfig);
        String mailFrom = jsonConfig.get("mailFrom").asText();
        String subject = messages.getMessage("test.message.subject", null, Locale.US);

        Map<String, Object> model = new HashMap<String, Object>();
        model.put(TARGET_EMAIL, email);

        String message = VelocityEngineUtils.mergeTemplateIntoString(this.engine,
                "test.vm", UTF_8, model);

        sendMail(testMailSender, mailFrom, email, subject, message);
    }

    @Override
    public void sendActivationEmail(String activationLink, String email) throws ThingsboardException {

        String subject = messages.getMessage("activation.subject", null, Locale.US);

        Map<String, Object> model = new HashMap<String, Object>();
        model.put("activationLink", activationLink);
        model.put(TARGET_EMAIL, email);

        String message = VelocityEngineUtils.mergeTemplateIntoString(this.engine,
                "activation.vm", UTF_8, model);

        sendMail(mailSender, mailFrom, email, subject, message);
    }

    @Override
    public void sendAccountActivatedEmail(String loginLink, String email) throws ThingsboardException {

        String subject = messages.getMessage("account.activated.subject", null, Locale.US);

        Map<String, Object> model = new HashMap<String, Object>();
        model.put("loginLink", loginLink);
        model.put(TARGET_EMAIL, email);

        String message = VelocityEngineUtils.mergeTemplateIntoString(this.engine,
                "account.activated.vm", UTF_8, model);

        sendMail(mailSender, mailFrom, email, subject, message);
    }

    @Override
    public void sendResetPasswordEmail(String passwordResetLink, String email) throws ThingsboardException {

        String subject = messages.getMessage("reset.password.subject", null, Locale.US);

        Map<String, Object> model = new HashMap<String, Object>();
        model.put("passwordResetLink", passwordResetLink);
        model.put(TARGET_EMAIL, email);

        String message = VelocityEngineUtils.mergeTemplateIntoString(this.engine,
                "reset.password.vm", UTF_8, model);

        sendMail(mailSender, mailFrom, email, subject, message);
    }

    @Override
    public void sendPasswordWasResetEmail(String loginLink, String email) throws ThingsboardException {

        String subject = messages.getMessage("password.was.reset.subject", null, Locale.US);

        Map<String, Object> model = new HashMap<String, Object>();
        model.put("loginLink", loginLink);
        model.put(TARGET_EMAIL, email);

        String message = VelocityEngineUtils.mergeTemplateIntoString(this.engine,
                "password.was.reset.vm", UTF_8, model);

        sendMail(mailSender, mailFrom, email, subject, message);
    }

    @Override
    public void send(String from, String to, String cc, String bcc, String subject, String body) throws MessagingException {
        MimeMessage mailMsg = mailSender.createMimeMessage();
        MimeMessageHelper helper = new MimeMessageHelper(mailMsg, "UTF-8");
        helper.setFrom(StringUtils.isBlank(from) ? mailFrom : from);
        helper.setTo(to.split("\\s*,\\s*"));
        if (!StringUtils.isBlank(cc)) {
            helper.setCc(cc.split("\\s*,\\s*"));
        }
        if (!StringUtils.isBlank(bcc)) {
            helper.setBcc(bcc.split("\\s*,\\s*"));
        }
        helper.setSubject(subject);
        helper.setText(body);
        mailSender.send(helper.getMimeMessage());
    }

    private void sendMail(JavaMailSenderImpl mailSender,
                          String mailFrom, String email,
                          String subject, String message) throws ThingsboardException {
        try {
            MimeMessage mimeMsg = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(mimeMsg, UTF_8);
            helper.setFrom(mailFrom);
            helper.setTo(email);
            helper.setSubject(subject);
            helper.setText(message, true);
            mailSender.send(helper.getMimeMessage());
        } catch (Exception e) {
            throw handleException(e);
        }
    }

    protected ThingsboardException handleException(Exception exception) {
        String message;
        if (exception instanceof NestedRuntimeException) {
            message = ((NestedRuntimeException) exception).getMostSpecificCause().getMessage();
        } else {
            message = exception.getMessage();
        }
        return new ThingsboardException(String.format("Unable to send mail: %s", message),
                ThingsboardErrorCode.GENERAL);
    }

    @Override
    public void sendSMS(AlarmLinkedUser user, AlarmReport alarmReport) {
        String body = alarmReport.getDeviceName() + "的" + alarmReport.getProp() + "在" + alarmReport.getTime() + "的数值为" + alarmReport.getAlarmValue() + ",预警设定值为" + alarmReport.getSetValue();
        sendToUser(user, body, getTenantSmsKey(user.getTenantId()));
    }

    @Override
    public boolean sendToUser(AlarmLinkedUser user, String body) {
       return sendToUser(user,body,getTenantSmsKey(user.getTenantId()));
    }

    /**
     * 发送验证码类短信
     * @param user
     * @param body
     * @return
     */
    @Override
    public boolean sendCaptcha(AlarmLinkedUser user, String body) {
        TenantSmsKey tenantSmsKey = getTenantSmsKey(user.getTenantId());
        YunpianClient clnt = YunpianFactory.getYunpianClient(tenantSmsKey.getSmsAppKey());
        Map<String, String> param = new HashMap<String, String>();
        String tpl_value = null;
        try {
            tpl_value = URLEncoder.encode("#code#", ENCODING) + "=" + URLEncoder.encode(body, ENCODING);
            param.put("tpl_id", tenantSmsKey.getCaptchaKey());
            param.put("tpl_value", tpl_value);
            param.put("mobile", user.getPhone());
            com.yunpian.sdk.model.Result sendResult = clnt.sms().tpl_single_send(param);
            createLog(user, sendResult.isSucc(), DataConstants.ALARM_SEND_SMS, sendResult.getMsg(), body, DataConstants.LOG_TYPE_DEVICE_ALARM);
            return sendResult.isSucc();
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return false;
    }

    @Override
    public boolean sendLogicalFlowAlarmToUser(AlarmLinkedUser user, String body) {
        TenantSmsKey tenantSmsKey = getTenantSmsKey(user.getTenantId());
        YunpianClient clnt = YunpianFactory.getYunpianClient(tenantSmsKey.getSmsAppKey());
        Map<String, String> param = new HashMap<String, String>();
        String tpl_value = null;
        try {
            tpl_value = URLEncoder.encode("#value#", ENCODING) + "=" + URLEncoder.encode(body, ENCODING);
            param.put("tpl_id", tenantSmsKey.getSmsModelKey());
            param.put("tpl_value", tpl_value);
            param.put("mobile", user.getPhone());
            com.yunpian.sdk.model.Result sendResult = clnt.sms().tpl_single_send(param);
            createLog(user, sendResult.isSucc(), DataConstants.ALARM_SEND_SMS, sendResult.getMsg(), body, DataConstants.LOG_TYPE_LOGICAL_FLOW_ALARM);
            System.out.println("----------------------- send email ----------------------");
            log.info("api_key : [{}], tpl_id : [{}], tpl_value : [{}], mobile : [{}]",
                    tenantSmsKey.getSmsAppKey(), tenantSmsKey.getSmsModelKey(), tpl_value, user.getPhone());
            return sendResult.isSucc();
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return false;
    }

    @Override
    public boolean sendToUser(AlarmLinkedUser user, String body, TenantSmsKey tenantSmsKey) {
        YunpianClient clnt = YunpianFactory.getYunpianClient(tenantSmsKey.getSmsAppKey());
        Map<String, String> param = new HashMap<String, String>();
        String tpl_value = null;
        try {
            tpl_value = URLEncoder.encode("#value#", ENCODING) + "=" + URLEncoder.encode(body, ENCODING);
            param.put("tpl_id", tenantSmsKey.getSmsDeviceKey());
            param.put("tpl_value", tpl_value);
            param.put("mobile", user.getPhone());
            com.yunpian.sdk.model.Result sendResult = clnt.sms().tpl_single_send(param);
            createLog(user, sendResult.isSucc(), DataConstants.ALARM_SEND_SMS, sendResult.getMsg(), body, DataConstants.LOG_TYPE_DEVICE_ALARM);
            return sendResult.isSucc();
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 生成日志
     *
     * @param user   用户
     * @param result 发送结果
     * @param body   消息体
     */
    private void createLog(AlarmLinkedUser user, boolean result, String msgType, String resultMsg, String body, String logType) {
        MsgLogEntity msgLogEntity = MsgLogEntity.builder()
                .phone(user.getPhone())
                .email(user.getEmail())
                .msgType(msgType)
                .status(result ? DataConstants.SEND_MSG_SUCCESS : DataConstants.SEND_MSG_FAILED)
                .msgBody(body)
                .format(resultMsg)
                .tenantId(UUIDConverter.fromTimeUUID(user.getTenantId().getId()))
                .userName(user.getUserName()+"("+(user.getEmail()==null?"":user.getEmail())+")")
                .updateTime(System.currentTimeMillis())
                .logType(logType)
                .build();
        msgLogService.save(msgLogEntity);
    }

    /**
     * 发送设备离线短信
     *
     * @param user        用户
     * @param alarmReport 报警内容
     */
    @Override
    public void sendOfflineSMS(User user, AlarmReport alarmReport) {
        AlarmLinkedUser alarmLinkedUser = AlarmLinkedUser.builder()
                .userName(user.getFirstName() + "(" + user.getName() + ")")
                .email(user.getEmail())
                .phone(user.getPhone())
                .tenantId(user.getTenantId())
                .build();
        //构造发送设备掉线消息日志
        String body = "#device#于#time#掉线";
        body = body.replace("#device#", alarmReport.getDeviceName()).replace("#time#", alarmReport.getTime());
        sendToUser(alarmLinkedUser, body, getTenantSmsKey(user.getTenantId()));
    }


    /**
     * 发送量程短信
     *
     * @param user
     */
    public void sendRangeSMS(Device device, User user, TsKvEntry kvEntry, TenantSmsKey tenantSmsKey) {
        AlarmLinkedUser alarmLinkedUser = AlarmLinkedUser.builder()
                .userName(user.getFirstName() + "(" + user.getName() + ")")
                .email(user.getEmail())
                .phone(user.getPhone())
                .tenantId(user.getTenantId())
                .build();
        String deviceName = device.getName();
        String body = "#device#的#prop#于#time#触发量程归零，触发值为：#value#";
        body = body.replace("#device#", deviceName)
                .replace("#prop#", kvEntry.getKey())
                .replace("#time#", DateUtils.date2Str(new Date(System.currentTimeMillis()), DateUtils.DATE_FORMATE_DEFAULT))
                .replace("#value#", kvEntry.getValueAsString());
        sendToUser(alarmLinkedUser, body, tenantSmsKey);
    }

    @Override
    public void sendRangeMsg(Device device, TsKvEntry kvEntry, PropAttribute propAttribute) {
        List<User> users = userService.findUserByTenant(device.getTenantId());
        List<User> sendEmailUser = new ArrayList<>();
        List<User> sendSmsUser = new ArrayList<>();
        users.forEach(user -> {
            //新增在进行报警时，先判断该用户是否已经激活
            if (userCredentialsDao.findByUserId(user.getUuidId()) == null ||
                    !userCredentialsDao.findByUserId(user.getUuidId()).isEnabled()) {
                return;
            }
            Map info = JacksonUtil.fromString(user.getAdditionalInfo().asText(), Map.class);
            if (info.get(ModelConstants.ALARM_FORM_EMAIL) != null && info.get(ModelConstants.ALARM_FORM_EMAIL).equals(ModelConstants.ALARM_RELEASE_NOT)) {
                sendEmailUser.add(user);
            }
            if (info.get(ModelConstants.ALARM_FORM_SMS) != null && info.get(ModelConstants.ALARM_FORM_SMS).equals(ModelConstants.ALARM_RELEASE_NOT)) {
                sendSmsUser.add(user);
            }
        });
        String subject = "设备量程变更提醒";
        String emailBody = "您的设备" + device.getName() + "于" + DateUtils.date2Str(new Date(System.currentTimeMillis()), DateUtils.DATE_FORMATE_DEFAULT) + "," + kvEntry.getKey() + "的数据变化到" + kvEntry.getValueAsString() + "触发量程归零，请及时前往查看";

        sendSmsUser.forEach(user -> {
            if (user.getPhone() != null) {
                sendRangeSMS(device, user, kvEntry,getTenantSmsKey(device.getTenantId()));
            }
        });
        try {
            sendEmailUser.forEach(user -> {
                if (user.getEmail() != null) {

                    AlarmLinkedUser alarmLinkedUser = AlarmLinkedUser.builder()
                            .userName(user.getFirstName() + "(" + user.getName() + ")")
                            .email(user.getEmail())
                            .phone(user.getPhone())
                            .tenantId(user.getTenantId())
                            .build();
                    sendEmail(alarmLinkedUser, subject, emailBody, DataConstants.LOG_TYPE_DEVICE_REANGE);
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }

    }


    /**
     * 获取企业下的短信key
     * @param tenantId
     * @return
     */
    public TenantSmsKey getTenantSmsKey(TenantId tenantId) {
        TenantSmsKey tenantSmsKey = new TenantSmsKey();
        try {
            AttributeKvEntry attributeKvEntry = attributesService.findNotFuture(tenantId, DataConstants.SHARED_SCOPE, DataConstants.ALARM_SEND_SMS);
            if (attributeKvEntry != null) {
                JSONObject jsonObject = JSONObject.parseObject(attributeKvEntry.getValueAsString());
                tenantSmsKey = TenantSmsKey.builder().smsAppKey(jsonObject.getString("smsAppKey"))
                        .smsDeviceKey(jsonObject.getString("smsDeviceKey"))
                        .smsModelKey(jsonObject.getString("smsModelKey"))
                        .captchaKey(jsonObject.getString("captchaKey"))
                        .build();
            } else {
                tenantSmsKey = TenantSmsKey.builder().smsAppKey(DataConstants.YUNPIAN_APIKEY)
                        .smsDeviceKey(DataConstants.YUNPIAN_DEVICE_KEY)
                        .smsModelKey(DataConstants.YUNPIAN_MODEL_KEY)
                        .captchaKey(DataConstants.YUNPIAN_CAPTCHA_KEY)
                        .build();
            }
        } catch (Exception e) {
            return TenantSmsKey.builder().smsAppKey(DataConstants.YUNPIAN_APIKEY)
                    .smsDeviceKey(DataConstants.YUNPIAN_DEVICE_KEY)
                    .smsModelKey(DataConstants.YUNPIAN_MODEL_KEY)
                    .captchaKey(DataConstants.YUNPIAN_CAPTCHA_KEY)
                    .build();
        }
        return tenantSmsKey;
    }

}
