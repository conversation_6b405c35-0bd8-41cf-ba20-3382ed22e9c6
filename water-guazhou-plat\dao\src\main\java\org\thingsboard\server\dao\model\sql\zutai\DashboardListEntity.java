package org.thingsboard.server.dao.model.sql.zutai;

import lombok.Data;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;
import java.util.Date;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2021-06-15
 */
@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.ZUTAI_DASHBOARD_LIST_TABLE)
public class DashboardListEntity {
    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.ZUTAI_DASHBOARD_LIST_DASHBOARD_ID)
    private String dashboardId;

    @Column(name = ModelConstants.ZUTAI_DASHBOARD_LIST_NAME)
    private String name;

    @Column(name = ModelConstants.ZUTAI_DASHBOARD_LIST_DETAIL)
    private String detail;

    @Column(name = ModelConstants.ZUTAI_DASHBOARD_LIST_PROTECT)
    private Boolean protect;

    @Column(name = ModelConstants.ZUTAI_DASHBOARD_LIST_PROTECT_PWD)
    private String protectPwd;

    @Column(name = ModelConstants.ZUTAI_DASHBOARD_LIST_CHECKED)
    private Boolean checked;

    @Column(name = ModelConstants.ZUTAI_DASHBOARD_LIST_TYPE)
    private String type;

    @Column(name = ModelConstants.ZUTAI_DASHBOARD_LIST_CREATE_TIME)
    private Date createTime;

    @Column(name = ModelConstants.ZUTAI_DASHBOARD_LIST_PROJECT_ID)
    private String projectId;

    @Column(name = ModelConstants.ZUTAI_DASHBOARD_LIST_TENANT_ID)
    private String tenantId;
}
