package org.thingsboard.server.dao.menu2;

import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.MenuResource;

import java.util.List;

public interface MenuResourceService {
    PageData<MenuResource> findList(int page, int size, String name, String url);

    MenuResource save(MenuResource menuResource);

    void remove(List<String> ids);

    List<MenuResource> findAll();

    void menuPoolToMenuResource();

}
