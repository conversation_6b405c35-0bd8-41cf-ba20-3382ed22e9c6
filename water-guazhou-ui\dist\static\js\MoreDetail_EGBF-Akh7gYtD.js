import{_ as D}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{z as g,d as k,c as _,r as d,b as l,S as v,o as I,g as M,n as P,q as m,i as f,aB as q,aq as L,C as S}from"./index-r0dFAfgr.js";import{_ as C}from"./Search-NSrhrIa_.js";const F=i=>g({url:"/api/spp/dma/partition/pumpHouse/list",method:"get",params:i}),W=i=>g({url:"/api/spp/dma/partition/pumpHouse",method:"post",data:i}),B=i=>g({url:"/api/spp/dma/partition/pumpHouse",method:"delete",data:i}),E=k({__name:"MoreDetail_EGBF",props:{partition:{}},setup(i){const b=i,c=_(),u=_(),h=d({filters:[{type:"input",label:"管径",field:"caliber",clearable:!1},{type:"input",label:"厂家",field:"brand",clearable:!1}],operations:[{type:"btn-group",btns:[{perm:!0,iconifyIcon:"ep:search",text:"查询",type:"primary",click:()=>n()},{perm:!0,iconifyIcon:"ep:refresh",text:"重置",type:"default",click:()=>{var e;(e=c.value)==null||e.resetForm()}},{perm:!0,iconifyIcon:"ep:circle-plus",text:"新增",type:"success",click:()=>y()}]}]}),r=d({dataList:[],columns:[{label:"泵房类型",prop:"type"},{label:"位置",prop:"position"},{label:"模式",prop:"mode"},{label:"厂家",prop:"brand"},{label:"进水口计量",prop:"inWaterMetering"},{label:"出水口计量",prop:"outWaterMetering"},{label:"管径",prop:"caliber"},{label:"备注",prop:"remark"},{label:"现场图片",prop:"img",image:!0}],pagination:{refreshData:({page:e,size:t})=>{r.pagination.page=e,r.pagination.limit=t,n()}},operations:[{perm:!0,text:"编辑",iconifyIcon:"ep:edit",click:e=>y(e)},{perm:!0,text:"删除",iconifyIcon:"ep:delete",type:"danger",click:e=>x(e)}]}),n=async()=>{var e,t;r.loading=!0;try{const a=((e=c.value)==null?void 0:e.queryParams)||{},p=(await F({...a,partitionId:(t=b.partition)==null?void 0:t.value,page:r.pagination.page||1,size:r.pagination.limit||20})).data.data||{};r.dataList=p.data||[],r.pagination.total=p.total||0}catch{}r.loading=!1},y=e=>{var t;s.defaultValue={...e||{}},s.title=e?"编辑泵站":"添加泵站",(t=u.value)==null||t.openDialog()},x=e=>{const t=e?[e.id]:[];if(!t.length){l.error("请选择要删除的数据");return}v("确定删除?","提示信息").then(async()=>{try{const a=await B(t);a.data.code===200?(l.success("删除成功"),n()):l.error(a.data.message)}catch{l.error("删除失败")}}).catch(()=>{})},s=d({title:"添加流量表",dialogWidth:650,labelWidth:130,labelPosition:"right",group:[{fields:[{lg:24,xl:12,type:"input",label:"泵房类型",field:"type",rules:[{required:!0,message:"请输入泵房类型"}]},{lg:24,xl:12,type:"input",label:"位置",field:"position"},{lg:24,xl:12,type:"input",label:"模式",field:"mode",rules:[{required:!0,message:"请输入模式"}]},{lg:24,xl:12,type:"input",label:"厂家",field:"brand"},{lg:24,xl:12,type:"textarea",label:"进水口计量情况",field:"inWaterMetering"},{lg:24,xl:12,type:"textarea",label:"出水口计量情况",field:"outWaterMetering"},{lg:24,xl:12,type:"input-number",label:"管径",field:"caliber"},{type:"textarea",label:"备注",field:"remark"},{type:"image",label:"现场图片",field:"img"}]}],submit:async e=>{var t,a;s.submitting=!0;try{const o=await W({...e,partitionId:(t=b.partition)==null?void 0:t.value});o.data.code===200?(l.success("提交成功"),n(),(a=u.value)==null||a.closeDialog()):l.error(o.data.message)}catch{l.error("提交失败")}s.submitting=!1}});return I(()=>{n()}),(e,t)=>{const a=C,o=L,p=D;return M(),P(q,null,[m(a,{ref_key:"refSearch",ref:c,config:f(h),class:"search"},null,8,["config"]),m(o,{config:f(r),class:"table-box"},null,8,["config"]),m(p,{ref_key:"refDialog",ref:u,config:f(s)},null,8,["config"])],64)}}}),A=S(E,[["__scopeId","data-v-bc7f066f"]]);export{A as default};
