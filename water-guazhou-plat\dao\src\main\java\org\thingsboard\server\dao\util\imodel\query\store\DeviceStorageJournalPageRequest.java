package org.thingsboard.server.dao.util.imodel.query.store;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.store.DeviceStorageJournal;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;

import java.util.Date;

@Getter
@Setter
public class DeviceStorageJournalPageRequest extends AdvancedPageableQueryEntity<DeviceStorageJournal, DeviceStorageJournalPageRequest> {
    // 设备编码
    private String serialId;

    // 标签码
    private String deviceLabelCode;

    // 设备名称
    private String name;

    // 设备型号
    private String model;

    // 设备类别Id
    private String deviceTypeId;

    // 供应商ID
    private String supplierId;

    // 报废时间
    private String scrappedTime;

    // 所在仓库
    private String storehouseId;

    // 所在货架
    private String shelvesId;

    // 最后保养时间
    private String lastMaintainanceTime;

    // 最后保养时间
    private String lastMaintainanceTimeFrom;

    // 最后保养时间
    private String lastMaintainanceTimeTo;

    // 最后巡检时间
    private String lastInspectionTime;

    // 对应的出库到的出库单Id
    private String storeOutId;

    // 对应的出库到的出库单条目，多个逗号隔开
    private String storeOutItemId;

    // 安装区域Id
    private String areaId;

    // 安装位置
    private String address;

    // 所属项目
    private String projectId;

    // 仓库编码
    private String storehouseCode;

    // 仓库名称
    private String storehouseName;

    // 货架编号
    private String shelfCode;

    // 货架名称
    private String shelfName;

    // 是否已出库
    private String isOut;

    // 为true时包含已报废设备
    private Boolean scrapped;

    private String isCollect;

    public Date getScrappedTime() {
        return toDate(scrappedTime);
    }

    public Date getLastMaintainanceTime() {
        return toDate(lastMaintainanceTime);
    }

    public Date getLastMaintainanceTimeFrom() {
        return toDate(lastMaintainanceTimeFrom);
    }

    public Date getLastMaintainanceTimeTo() {
        return toDate(lastMaintainanceTimeTo);
    }

    public Date getLastInspectionTime() {
        return toDate(lastInspectionTime);
    }
}
