<template>
  <div class="detailSteps">
    <el-steps
      class="steps"
      :active="active"
      finish-status="finish"
      simple
      process-status="success"
    >
      <el-step
        v-for="(step, index) in state.OrderStatus"
        :key="index"
        :title="(step.text as string)||''"
      />
    </el-steps>
    <!-- <div class="status">
      <el-progress
        type="circle"
        :width="25"
        :stroke-width="5"
        :show-text="false"
        :percentage="100"
      />
      <span>尚未完成</span>
    </div> -->
  </div>
</template>
<script lang="ts" setup>
import { reactive } from 'vue'
import { WorkOrderStep } from '../config'

const props = defineProps<{
    config: {
        status: string
        statusName: string
    }
}>()

const active = ref(0)
const state = reactive<{
    OrderStatus: IButton[]
    CurStatus?: NormalOption
}>({
  OrderStatus: [],
  CurStatus: undefined
})
const initList = () => {
  let statusOptions = WorkOrderStep()
  state.CurStatus = statusOptions.find(
    item => item.value === props.config.status
  )
  if (state.CurStatus?.value === 'REJECTED') {
    statusOptions = statusOptions.filter(item => item.value !== 'APPROVED')
  } else {
    statusOptions = statusOptions.filter(item => item.value !== 'REJECTED')
  }
  const curIndex = statusOptions.findIndex(
    item => item.value === props.config.status
  )
  if (curIndex === -1) {
    state.OrderStatus = [{ perm: true, text: props.config.statusName }]
  } else {
    state.OrderStatus = statusOptions.map(item => {
      return {
        perm: true,
        text: item.label
      }
    })
  }
  status()
}

function status() {
  active.value = 0
  for (let i = 0; i < state.OrderStatus.length; i++) {
    if (state.CurStatus?.label === state.OrderStatus[i].text) { return }
    active.value += 1
  }
}

watch(() => props.config.status, () => {
  initList()
})
</script>
<style lang="scss" scoped>
.detailSteps{
  // display: flex;
}
.step-wrapper {
    display: flex;
    justify-content: flex-start;
    align-items: cecenter;
    background: var(--el-bg-color);
    margin: -10px 0px;
    padding: 10px 0px 10px 20px;
}

.step {
    width: 100px;
    height: 30px;
    left: 960px;
    top: 176px;

background: #46B85F;

    :first-child {
        width: 150px;
    }
}

.el-steps--simple{
  padding: 13px 2%;
}

.steps{
  // width: calc(100% - 100px);
}

.status{
  width: 100px;
  height: 46px;
  display: flex;
  align-items: center;
  background-color: var(--el-fill-color-light);
  border-radius: 4px;
  margin-left: 10px;
  justify-content: space-around;
  color: var(--el-text-color-placeholder);
}
</style>
