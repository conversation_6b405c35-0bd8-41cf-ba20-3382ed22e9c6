import{d as J,c as f,r as D,b as I,bB as O,Q,g as G,h as N,F as y,p as U,q as C,i as m,_ as j}from"./index-r0dFAfgr.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import{g as H}from"./LayerHelper-Cn-iiqxI.js";import"./project-DUuzYgGl.js";import{E as K}from"./StatisticsHelper-D-s_6AyQ.js";import{e as W}from"./ViewHelper-BGCZjxXH.js";import{u as X}from"./arcWidgetButton-0glIxrt7.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import Y from"./RightDrawerMap-D5PhmGFO.js";import Z from"./StatisticsCharts-CyK-dNnC.js";import{QueryByPolygon as tt}from"./wfsUtils-DXofo3da.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./pipe-nogVzCHG.js";import"./fieldconfig-Bk3o1wi7.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./ArcView-DpMnCY82.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./ArcStationWarning-BF9YrSzF.js";/* empty css                         */import"./useWaterPoint-Bv0z6ym6.js";import"./DateFormatter-Bm9a68Ax.js";import"./zhandian-YaGuQZe6.js";import"./useStation-DJgnSZIA.js";import"./DrawerBox-CLde5xC8.js";import"./SideDrawer-CBntChyn.js";import"./PipeDetail-CTBPYFJW.js";import"./Search-NSrhrIa_.js";import"./FormTableColumnFilter-BT7pLXIC.js";import"./QueryHelper-ILO3qZqg.js";import"./config-fy91bijz.js";import"./ArcBRTools-BO92yznB.js";import"./PipeLength.vue_vue_type_script_setup_true_lang-BZfUsvDf.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./usePipelineGroup-Ba1pmWz_.js";import"./GroupLayer-DhhN3FyN.js";import"./lazyLayerLoader-DbM9sT1W.js";import"./WFSLayer-1TtJp3nx.js";import"./clientSideDefaults-VQhQaYxh.js";import"./QueryEngineCapabilities-Dk3NJwmm.js";import"./wfsUtils-Du031XaC.js";import"./geojson-MBFu2-HZ.js";import"./xmlUtils-CtUoQO7q.js";import"./ListWindow-WS05QqV0.js";import"./PopLayout-BP55MvL7.js";import"./PoiSearchV2-D7yNeLuv.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./URLHelper-B9aplt5w.js";import"./useLayerList-DmEwJ-ws.js";import"./useScaleBar-Beed-z91.js";import"./useDetector-BRcb7GRN.js";import"./useHighLight-DPevRAc5.js";import"./ToolHelper-BiiInOzB.js";import"./geoserverUtils-wjOSMa7E.js";import"./echart-BoVIcYbV.js";import"./config-DqqM5K5L.js";import"./OutsideWorkOrder-C6s8joBt.js";import"./index-CpGhZCTT.js";const de=J({__name:"AllDevStatistics",setup(rt){const p=f(),g=f(),L=f(),i=D({tabs:[],curType:"",layerInfos:[],layerIds:[],attributes:[],loading:!1}),t={queryParams:{geometry:void 0,where:"1=1"}},S=D({group:[{fieldset:{desc:"绘制工具"},fields:[{type:"btn-group",btns:[{perm:!0,text:"",type:"default",size:"large",title:"绘制多边形",disabled:()=>i.loading,iconifyIcon:"mdi:shape-polygon-plus",click:()=>u("polygon")},{perm:!0,text:"",type:"default",size:"large",title:"绘制矩形",disabled:()=>i.loading,iconifyIcon:"ep:crop",click:()=>u("rectangle")},{perm:!0,text:"",type:"default",size:"large",title:"绘制圆形",disabled:()=>i.loading,iconifyIcon:"mdi:ellipse-outline",click:()=>u("circle")},{perm:!0,text:"",type:"default",size:"large",title:"清除图形",disabled:()=>i.loading,iconifyIcon:"ep:delete",click:()=>b()}]},{type:"btn-group",btns:[{perm:!0,text:"统计",styles:{width:"100%"},loading:()=>i.loading,click:()=>E()}]}]}],labelPosition:"top",gutter:12}),h=o=>{var a;const e=o.name;(a=p.value)==null||a.setCurLayer(e)},u=o=>{var e;b(),(e=t.sketch)==null||e.create(o)},b=()=>{var o;(o=t.graphicsLayer)==null||o.removeAll(),t.queryParams.geometry=void 0},F=async()=>{},E=async()=>{var o,e,a;I.info("正在统计，请稍候...");try{i.loading=!0,i.tabs.length=0;const s=t.queryParams.where||"1=1";let l;if(t.queryParams.geometry&&t.queryParams.geometry.type==="polygon"){const r=t.queryParams.geometry.rings;Array.isArray(r)&&r.length>0&&(l=r[0])}const n=[{name:"管线",label:"管线",statisticsFields:["管材","管径"]},{name:"测点",label:"测点",statisticsFields:["备注"],filterValues:["三通","四通","消防","排气","水表"]}],v=[];n.forEach(r=>{const $=(async()=>{var _;try{console.log(`开始查询${r.name}图层数据进行前端统计`);const q=((_=(await tt(r.name,l,s)).data)==null?void 0:_.features)||[];console.log(`查询到${r.name}图层数据${q.length}条，开始统计`);const B=[];return q.forEach((P,M)=>{const c=P.properties||{};let d=!1;r.filterValues?d=r.statisticsFields.some(z=>{const R=c[z]||"未知";return r.filterValues.includes(R)}):d=!0,d&&B.push({OBJECTID:c.OBJECTID||c.fid||M+1,...c,geometry:P.geometry})}),{name:r.name,label:r.label,data:B}}catch(x){return console.error(`查询和统计${r.name}失败:`,x),{name:r.name,label:r.label,data:[]}}})();v.push($)});const w=await Promise.all(v);i.tabs=w.filter(r=>r.data.length>0),console.log("统计结果:",w),console.log("最终state.tabs:",i.tabs),(o=g.value)==null||o.toggleCustomDetail(!0),O(()=>{var r;(r=p.value)==null||r.refreshChar()}),W(t.view,((e=t.queryParams.geometry)==null?void 0:e.extent)||((a=t.view)==null?void 0:a.extent),!0)}catch(s){console.log(s),i.loading=!1,I.error("统计失败")}i.loading=!1},{initSketch:A,destroySketch:T}=X(),k=o=>{var e;t.queryParams.geometry=(e=o.graphics[0])==null?void 0:e.geometry},V=o=>{t.view=o,t.graphicsLayer=H(t.view,{id:"search-alldevice",title:"全设备统计"}),t.sketch=A(t.view,t.graphicsLayer,{createCallBack:k,updateCallBack:k}),F()};return Q(()=>{T(),t.sketch=void 0}),(o,e)=>{const a=j;return G(),N(Y,{ref_key:"refMap",ref:g,title:"全设备统计","full-content":!0,onMapLoaded:V},{"detail-header":y(()=>e[2]||(e[2]=[U("span",null,"统计结果",-1)])),"detail-default":y(()=>[C(Z,{ref_key:"refStatistic",ref:p,tabs:m(i).tabs,view:t.view,"all-device":!0,"query-params":t.queryParams,"statistics-params":{group_fields:["备注"],statistic_field:m(K).OBJECTID,statistic_type:"1"},unit:"个",prefix:"",onDetailRefreshed:e[0]||(e[0]=s=>m(i).loading=!1),onAttrRowClick:e[1]||(e[1]=s=>{var l,n;(l=m(p))==null||l.setCurLayer(s.label),(n=m(p))==null||n.refreshDetail(s.label,!0)}),onBarClick:h,onRingClick:h},null,8,["tabs","view","query-params","statistics-params"])]),default:y(()=>[C(a,{ref_key:"refForm",ref:L,config:m(S)},null,8,["config"])]),_:1},512)}}});export{de as default};
