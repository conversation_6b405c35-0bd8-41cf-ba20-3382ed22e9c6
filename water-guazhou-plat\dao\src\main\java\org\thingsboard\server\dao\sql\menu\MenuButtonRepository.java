package org.thingsboard.server.dao.sql.menu;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.repository.CrudRepository;
import org.thingsboard.server.dao.model.sql.MenuButtonEntity;

import java.util.List;

public interface MenuButtonRepository extends CrudRepository<MenuButtonEntity, String>, JpaRepository<MenuButtonEntity, String> {
    List<MenuButtonEntity> findByMenuIdOrderByOrderNum(String menuId);

    List<MenuButtonEntity> findByIdIn(List<String> ids);

    List<MenuButtonEntity> findByMenuIdIn(List<String> menuIdList);
}
