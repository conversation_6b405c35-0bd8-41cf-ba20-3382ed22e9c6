/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.sql.optionLog;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.thingsboard.server.dao.model.sql.OptionLogEntity;

import java.util.List;

public interface OptionLogRepository extends CrudRepository<OptionLogEntity, String> {

    @Query("FROM OptionLogEntity WHERE tenantId = ?1 AND options = ?2 AND createTime between ?3 AND ?4 ORDER BY createTime DESC")
    List<OptionLogEntity> findByTenantIdAndOptions(String tenantId, String options, long startTime, long endTime);

    @Query("FROM OptionLogEntity " +
            "WHERE authority = ?1 AND tenantId = ?3 AND options = ?2 AND createTime between ?4 AND ?5 " +
            "ORDER BY createTime DESC")
    List<OptionLogEntity> findByAuthorityAndOptionsAndTenantId(String authority, String options, String tenantId, long startTime, long endTime);

    Page<OptionLogEntity> findByAuthorityInAndTenantIdAndOptionsNotAndCreateTimeBetween(List<String> authority, String tenantId, String options, long startTime, long endTime, Pageable pageable);

    Page<OptionLogEntity> findByAuthorityInAndTenantIdAndOptionsNotAndCreateTimeBetweenAndFirstNameLikeAndAdditionalInfoLike(List<String> authority, String tenantId, String options, long startTime, long endTime, String keyword, String additionalInfo, Pageable pageable);

    @Query("FROM OptionLogEntity " +
            "WHERE userId = ?1 AND type = '2' ORDER BY createTime DESC")
    List<OptionLogEntity> findByUserId(String id, Pageable pageable);

    @Query("FROM OptionLogEntity " +
            "WHERE authority = ?1 AND tenantId = ?3 AND options = ?2 AND createTime between ?4 AND ?5 AND additionalInfo like ?6 " +
            "ORDER BY createTime DESC")
    List<OptionLogEntity> findByAuthorityAndOptionsAndTenantIdAndIp(String authorityParam, String options, String fromTimeUUID, Long startTime, Long endTime, String ip);

    @Query("FROM OptionLogEntity WHERE tenantId = ?1 AND options = ?2 AND createTime between ?3 AND ?4 AND additionalInfo like ?5 ORDER BY createTime DESC")
    List<OptionLogEntity> findByTenantIdAndOptionsAndIp(String fromTimeUUID, String options, Long startTime, Long endTime, String ip);

    @Modifying
    @Query("update OptionLogEntity set status = '1', examineId = ?2, examineName = ?3, examineTenantId = ?4, examineTime = ?5 where id = ?1 and (status <> '1' or status is null)")
    int examine(String id, String examineId, String examineName, String examineTenantId, Long examineTime);

    List<OptionLogEntity> findByTenantIdAndOptionsAndCreateTimeBetweenAndAdditionalInfoLikeAndFirstNameLike(String fromTimeUUID, String options, Long startTime, Long endTime, String ip, String keyword);

    List<OptionLogEntity> findByAuthorityAndOptionsAndTenantIdAndCreateTimeBetweenAndAdditionalInfoLikeAndFirstNameLike(String authorityParam, String options, String fromTimeUUID, Long startTime, Long endTime, String ip, String keyword);

    Page<OptionLogEntity> findByTenantIdAndOptionsAndCreateTimeBetweenAndAdditionalInfoLikeAndFirstNameLike(String tenantId, String options, Long startTime, Long endTime, String ip, String keyword, Pageable pageable);

    Page<OptionLogEntity> findByAuthorityAndOptionsAndTenantIdAndCreateTimeBetweenAndAdditionalInfoLikeAndFirstNameLike(String authorityParam, String options, String fromTimeUUID, Long startTime, Long endTime, String ip, String keyword, Pageable pageable);
}
