import{i as r,s as a}from"./Point-WxyopZva.js";import{L as s}from"./enums-L38xj_2E.js";import{$ as i}from"./color-DAS1c3my.js";function u(e){return i(e.minDataValue)&&i(e.maxDataValue)&&e.minSize!=null&&e.maxSize!=null?s.SIZE_MINMAX_VALUE:(e.expression&&e.expression==="view.scale"||e.valueExpression&&e.valueExpression==="$view.scale")&&Array.isArray(e.stops)?s.SIZE_SCALE_STOPS:(e.field!=null||e.expression&&e.expression!=="view.scale"||e.valueExpression&&e.valueExpression!=="$view.scale")&&(Array.isArray(e.stops)||"levels"in e&&e.levels)?s.SIZE_FIELD_STOPS:(e.field!=null||e.expression&&e.expression!=="view.scale"||e.valueExpression&&e.valueExpression!=="$view.scale")&&e.valueUnit!=null?s.SIZE_UNIT_VALUE:(r.getLogger("esri.views.2d.engine.webgl").error(new a("mapview-bad-type","Found invalid size VisualVariable",e)),s.NONE)}export{u as l};
