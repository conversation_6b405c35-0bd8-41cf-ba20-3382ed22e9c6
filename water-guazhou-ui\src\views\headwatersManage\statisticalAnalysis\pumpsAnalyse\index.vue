<!-- 泵机分析 -->
<template>
  <div class="wrapper">
    <CardSearch
      ref="cardSearch"
      :config="cardSearchConfig"
    />
    <CardTable
      class="card-table"
      :config="cardTableConfig"
    />
  </div>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs'
import { getWaterOutletAndInletReport } from '@/api/headwatersManage/statisticalAnalysis'
import { ICardSearchIns } from '@/components/type'
import {
  getFormatTreeNodeDeepestChild,
  objectLookup
} from '@/utils/GlobalHelper'
import useStation from '@/hooks/station/useStation'

const { getStationTree } = useStation()
const today = dayjs().date()
const cardSearch = ref<ICardSearchIns>()

// 水源站点树
const TreeData = reactive<SLTreeConfig>({
  data: [],
  checkedKeys: [],
  checkedNodes: []
})

const totalLoading = ref<boolean>(false)
// 搜索栏初始化配置
const cardSearchConfig = reactive<ISearch>({
  defaultParams: {
    date: [
      dayjs()
        .date(today - 3)
        .format('YYYY-MM-DD'),
      dayjs().date(today).format('YYYY-MM-DD')
    ]
  },
  filters: [
    {
      type: 'select-tree',
      field: 'treeData',
      defaultExpandAll: true,
      multiple: true,
      options: computed(() => TreeData.data) as any,
      label: '站点选择',
      onChange: key => {
        TreeData.checkedKeys = key || []
        TreeData.checkedNodes = []
        for (const i in key) {
          const val = objectLookup(TreeData.data, 'children', 'id', i)
          TreeData.checkedNodes.push(val)
        }
        refreshData()
      }
    },
    { type: 'daterange', label: '选择时间', field: 'date' },
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          click: () => refreshData(),
          icon: 'iconfont icon-chaxun'
        }
      ]
    }
  ]
})

// 定义动态表头初始化数据
// let weekDate = reactive<IFormTableColumn[]>([])

// 初始化列表配置数据
const cardTableConfig = reactive<ITable>({
  loading: false,
  dataList: [],
  columns: [
    { prop: 'name', label: '泵房名称' },
    { prop: 'outletTotalFlow', label: '出水' },
    { prop: 'inletTotalFlow', label: '进水' },
    { prop: 'differenceTotalFlow', label: '进出水差值' },
    { prop: 'differenceRate', label: '差值率' }
  ],
  operations: [],
  showSummary: false,
  operationWidth: '150px',
  indexVisible: true,
  pagination: {
    hide: true
  }
})

// 刷新列表 模拟数据
const refreshData = () => {
  cardTableConfig.loading = true
  const queryParams = cardSearch.value?.queryParams as any || {}
  const stationIdList = TreeData.checkedKeys as any[]
  const params: any = {
    stationIdList: stationIdList.join(','),
    start: dayjs(queryParams.date[0]).valueOf(),
    end: dayjs(queryParams.date[1]).valueOf()
  }

  getWaterOutletAndInletReport(params).then(res => {
    const data = res.data.data
    cardTableConfig.dataList = data
    cardTableConfig.loading = false
  })
}

onMounted(async () => {
  const treeData = await getStationTree('水源地')
  TreeData.data = treeData
  TreeData.currentProject = getFormatTreeNodeDeepestChild(TreeData.data)
  cardSearchConfig.defaultParams = { ...cardSearchConfig.defaultParams, treeData: [TreeData.currentProject.id] }
  cardSearch.value?.resetForm()
  TreeData.checkedKeys = [TreeData.currentProject.id]
  refreshData()
})

</script>
