const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["static/js/FeedbackDetailDialog-k-dnQi76.js","static/js/index-r0dFAfgr.js","static/css/index-ByiGXvnH.css","static/js/DateFormatter-Bm9a68Ax.js","static/js/circuitTaskFormRecord-CjbtiPXk.js","static/js/config-C9CMv0E7.js","static/css/FeedbackDetailDialog-CuOQSAyH.css"])))=>i.map(i=>d[i]);
import{d as V,r as h,s as v,c as d,x as n,o as A,g as I,n as P,q as f,p as M,aq as W,i as w,aa as F,a3 as B,bL as H,aj as S,C as U}from"./index-r0dFAfgr.js";import{_ as L}from"./Search-NSrhrIa_.js";import{f as u}from"./DateFormatter-Bm9a68Ax.js";import{a as N}from"./plan-BLf3nu6_.js";import{P as q}from"./config-C9CMv0E7.js";const j={class:"inspection-feedback"},O={class:"table-box"},J=V({__name:"InspectionFeedback",setup(z){const k=F(()=>B(()=>import("./FeedbackDetailDialog-k-dnQi76.js"),__vite__mapDeps([0,1,2,3,4,5,6]))),T=h({labelWidth:80,filters:[{type:"input",label:"任务编号",field:"keyword"},{type:"input",label:"任务名称",field:"taskName"},{type:"select",label:"任务状态",field:"status",options:[{label:"已完成",value:"APPROVED"},{label:"待审核",value:"VERIFY"},{label:"已驳回",value:"REJECTED"}]},{type:"input",label:"执行人员",field:"receiveUserId"},{type:"daterange",label:"完成时间",field:"completeTime",format:"YYYY-MM-DD"},{type:"btn-group",btns:[{perm:!0,text:"查询",type:"primary",click:()=>l()},{perm:!0,text:"重置",type:"default",click:()=>{var e;(e=c.value)==null||e.resetForm(),l()}}]}]}),a=h({title:"巡检反馈列表",height:"calc(100vh - 180px)",indexVisible:!0,columns:[{minWidth:120,label:"任务编号",prop:"code"},{minWidth:150,label:"任务名称",prop:"name"},{minWidth:120,label:"执行人员",prop:"receiveUserName"},{minWidth:120,label:"协作人员",prop:"collaborateUserName"},{minWidth:160,label:"开始时间",prop:"beginTime",formatter:e=>u(e.beginTime,"YYYY-MM-DD HH:mm:ss")},{minWidth:160,label:"结束时间",prop:"endTime",formatter:e=>u(e.endTime,"YYYY-MM-DD HH:mm:ss")},{minWidth:100,label:"任务状态",prop:"status",formatter:e=>{const t=q[e.status];return t?t.text:e.status}},{minWidth:120,label:"反馈完成率",prop:"formCompletionRate",formatter:e=>e.formCompletionRate!==void 0&&e.formCompletionRate!==null?`${e.formCompletionRate}%`:"待计算"},{minWidth:160,label:"创建时间",prop:"createTime",formatter:e=>u(e.createTime,"YYYY-MM-DD HH:mm:ss")}],dataList:[],loading:!1,pagination:{page:1,limit:20,total:0,refreshData:({page:e,size:t})=>{a.pagination.page=e,a.pagination.limit=t,l()}},operations:[{perm:!0,text:"查看反馈",isTextBtn:!0,svgIcon:v(H),click:e=>x(e)},{perm:!0,text:"导出",isTextBtn:!0,svgIcon:v(S),click:e=>D()}],titleRight:[{style:{justifyContent:"flex-end"},items:[{type:"btn-group",btns:[{perm:!0,text:"批量导出",type:"primary",click:()=>Y()}]}]}]}),c=d(),p=d(!1),g=d({}),l=async()=>{var e,t,s;try{a.loading=!0;const r=((e=c.value)==null?void 0:e.queryParams)||{};let b="",y="";r.completeTime&&Array.isArray(r.completeTime)&&(b=r.completeTime[0],y=r.completeTime[1]);const R={page:a.pagination.page||1,size:a.pagination.limit||20,keyword:r.keyword,receiveUserId:r.receiveUserId,fromTime:b,toTime:y,isReceived:"1"},i=await N(R);if(((t=i==null?void 0:i.data)==null?void 0:t.code)===200){let o=i.data.data||i.data||[];o&&typeof o=="object"&&!Array.isArray(o)&&(o=o.data||o.records||o.content||[]);const _=Array.isArray(o)?o:[],m=_.filter(C=>["APPROVED","VERIFY","REJECTED"].includes(C.status));a.dataList=m,a.pagination.total=m.length,console.log("API响应数据:",i.data),console.log("处理后的数据:",_),console.log("过滤后的数据:",m)}else n.error(((s=i.data)==null?void 0:s.message)||"获取数据失败")}catch(r){console.error(r),n.error("获取数据失败")}finally{a.loading=!1}},x=e=>{g.value=e,p.value=!0},D=e=>{n.info("导出功能开发中...")},Y=()=>{n.info("批量导出功能开发中...")},E=()=>{l()};return A(()=>{l()}),(e,t)=>(I(),P("div",j,[f(L,{ref_key:"refSearch",ref:c,config:T,style:{"margin-bottom":"8px",padding:"12px","background-color":"#fff","border-radius":"4px"}},null,8,["config"]),M("div",O,[f(W,{config:a},null,8,["config"])]),f(w(k),{modelValue:p.value,"onUpdate:modelValue":t[0]||(t[0]=s=>p.value=s),task:g.value,onSuccess:E},null,8,["modelValue","task"])]))}}),Z=U(J,[["__scopeId","data-v-8c0d92b1"]]);export{Z as default};
