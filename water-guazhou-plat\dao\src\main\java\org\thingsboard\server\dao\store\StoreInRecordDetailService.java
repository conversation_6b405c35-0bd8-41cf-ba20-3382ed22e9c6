package org.thingsboard.server.dao.store;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.store.StoreInRecord;
import org.thingsboard.server.dao.model.sql.store.StoreInRecordDetail;
import org.thingsboard.server.dao.util.imodel.query.store.StoreInRecordDetailPageRequest;
import org.thingsboard.server.dao.util.imodel.query.store.StoreInRecordDetailSaveRequest;

import java.util.List;

public interface StoreInRecordDetailService {
    /**
     * 分页条件查询入库单条目
     *
     * @param request 分页查询条件
     * @return 分页查询结果
     */
    IPage<StoreInRecordDetail> findAllConditional(StoreInRecordDetailPageRequest request);

    /**
     * 保存入库单条目
     *
     * @param entity 实体信息
     * @return 保存好的实体
     */
    List<StoreInRecordDetail> save(List<StoreInRecordDetailSaveRequest> entity, StoreInRecord result);

    /**
     * 增量修改
     *
     * @param entity 实体信息
     * @return 是否修改成功
     */
    boolean update(StoreInRecordDetail entity);

    /**
     * 删除
     *
     * @param id 唯一标识
     * @return 是否删除成功
     */
    boolean delete(String id);

    /**
     * 批量删除
     *
     * @param idList id列表
     * @return 是否删除成功
     */
    boolean deleteAll(List<String> idList);

}
