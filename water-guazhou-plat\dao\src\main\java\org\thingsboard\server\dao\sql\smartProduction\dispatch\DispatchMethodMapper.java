package org.thingsboard.server.dao.sql.smartProduction.dispatch;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.smartProduction.dispatch.DispatchMethod;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch.DispatchMethodPageRequest;

@Mapper
public interface DispatchMethodMapper extends BaseMapper<DispatchMethod> {
    IPage<DispatchMethod> findByPage(DispatchMethodPageRequest request);

    int save(DispatchMethod entity);

    @Override
    default int insert(DispatchMethod entity) {
        return save(entity);
    }

    boolean update(DispatchMethod entity);

    int updateFully(DispatchMethod updated);


    boolean switchEnabled(@Param("id") String id, @Param("enabled") boolean enabled);

}
