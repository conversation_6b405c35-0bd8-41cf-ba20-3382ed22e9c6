<template>
  <div class="app-container">
    <div class="device-icon-container">
      <!-- 内容区 -->
      <div class="device-list-container">
        <!-- 搜索表单 -->
        <el-form :model="queryParams" ref="queryForm" :inline="true" class="search-form">
          <el-form-item label="设备类型：">
            <el-select v-model="queryParams.deviceType" placeholder="请选择" clearable style="width: 180px">
              <el-option v-for="item in deviceTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="设备状态：">
            <el-select v-model="queryParams.deviceStatus" placeholder="请选择" clearable style="width: 180px">
              <el-option label="正常" value="正常"></el-option>
              <el-option label="离线" value="离线"></el-option>
              <el-option label="故障" value="故障"></el-option>
              <el-option label="维修中" value="维修中"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">查询</el-button>
            <el-button @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <!-- 操作按钮区 -->
        <div class="action-bar">
          <el-button type="primary" size="default" @click="handleAdd">新增</el-button>
        </div>

        <!-- 表格区域 -->
        <el-table
          v-loading="loading"
          :data="tableData"
          border
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="50"></el-table-column>
          <el-table-column label="设备类型" min-width="120" align="center" prop="deviceType"></el-table-column>
          <el-table-column label="设备图标" min-width="120" align="center">
            <template #default="scope">
              <el-image
                style="width: 40px; height: 40px;"
                :src="scope.row.iconUrl"
                fit="contain"
                :preview-src-list="[scope.row.iconUrl]"
              ></el-image>
            </template>
          </el-table-column>
          <el-table-column label="设备状态" min-width="120" align="center" prop="deviceStatus"></el-table-column>
          <el-table-column label="状态颜色" min-width="120" align="center">
            <template #default="scope">
              <div class="color-box" :style="{ backgroundColor: scope.row.statusColor }"></div>
            </template>
          </el-table-column>
          <el-table-column label="创建时间" min-width="160" align="center">
            <template #default="scope">
              {{ formatDate(scope.row.createTime, 'YYYY-MM-DD HH:mm:ss') }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" align="center">
            <template #default="scope">
              <div style="display: flex; justify-content: center; gap: 10px;">
                <el-button type="primary" size="small" @click="handleEdit(scope.row)">编辑</el-button>
                <el-button type="danger" size="small" @click="handleDelete(scope.row)">删除</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="queryParams.page"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="queryParams.size"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          ></el-pagination>
        </div>
      </div>
    </div>

    <!-- 添加或修改设备图标对话框 -->
    <el-dialog :title="dialogTitle" v-model="dialogVisible" width="500px" append-to-body destroy-on-close>
      <el-form ref="form" :model="formData" :rules="rules" label-width="100px" class="form-container">
        <el-form-item label="设备类型" prop="deviceType">
          <el-select v-model="formData.deviceType" placeholder="请选择" style="width: 100%">
            <el-option v-for="item in deviceTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="设备图标" prop="iconUrl">
          <el-upload
            class="avatar-uploader"
            :action="uploadUrl"
            :show-file-list="false"
            :on-success="handleIconSuccess"
            :before-upload="beforeIconUpload"
            :headers="{ 'X-Authorization': 'Bearer ' + userToken }"
          >
            <img v-if="formData.iconUrl" :src="formData.iconUrl" class="avatar" />
            <div v-else class="avatar-uploader-icon">
              <el-icon><Plus /></el-icon>
            </div>
          </el-upload>
          <div class="el-upload__tip">仅支持jpg/gif/png/ico格式文件，且文件大小不超过10MB</div>
        </el-form-item>
        <el-form-item label="设备状态" prop="deviceStatus">
          <el-select v-model="formData.deviceStatus" placeholder="请选择" style="width: 100%">
            <el-option label="正常" value="正常"></el-option>
            <el-option label="离线" value="离线"></el-option>
            <el-option label="故障" value="故障"></el-option>
            <el-option label="维修中" value="维修中"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="状态颜色" prop="statusColor">
          <el-color-picker v-model="formData.statusColor" show-alpha></el-color-picker>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelDialog">取 消</el-button>
          <el-button type="primary" @click="submitForm" :loading="submitLoading">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Delete } from '@element-plus/icons-vue'
import { getDeviceTypeTree } from '@/api/equipment_assets/equipmentManage'
import { getDeviceIconList, getDeviceIconById, getDeviceIconByDeviceType, saveDeviceIcon, deleteDeviceIcon, batchSaveDeviceIcon, deleteDeviceIconByDeviceType } from '@/api/deviceIcon'
import { formatDate } from '@/utils/DateFormatter'
import { useUserStore, useAppStore } from '@/store'
import useGlobal from '@/hooks/global/useGlobal'

const { $messageSuccess, $messageError, $messageWarning } = useGlobal()
const userStore = useUserStore()
const appStore = useAppStore()

// 上传URL
const uploadUrl = computed(() => appStore.actionUrl + 'file/api/upload/image')
// 用户Token
const userToken = computed(() => userStore.token)

// 设备类型选项
const deviceTypeOptions = ref([])

// 表单引用
const form = ref(null)

// 遮罩层
const loading = ref(false)
// 提交按钮加载状态
const submitLoading = ref(false)
// 选中数组
const selectedIds = ref([])
// 总条数
const total = ref(0)
// 表格数据
const tableData = ref([])
// 是否显示弹出层
const dialogVisible = ref(false)
// 弹出层标题
const dialogTitle = ref('')
// 跳转页码
const gotoPage = ref(1)

// 查询参数
const queryParams = reactive({
  page: 1,
  size: 20,
  deviceType: undefined,
  deviceStatus: undefined,
  fromTime: undefined,
  toTime: undefined
})

// 表单参数
const formData = reactive({
  id: undefined,
  deviceType: undefined,
  iconUrl: undefined,
  deviceStatus: '正常',
  statusColor: '#67C23A'
})

// 表单校验规则
const rules = reactive({
  deviceType: [
    { required: true, message: '设备类型不能为空', trigger: 'change' }
  ],
  iconUrl: [
    { required: true, message: '设备图标不能为空', trigger: 'change' }
  ],
  deviceStatus: [
    { required: true, message: '设备状态不能为空', trigger: 'change' }
  ],
  statusColor: [
    { required: true, message: '状态颜色不能为空', trigger: 'change' }
  ]
})

// 这里移除了与树形菜单相关的函数

// 获取设备类型选项
const getDeviceTypes = async () => {
  try {
    const response = await getDeviceTypeTree()
    if (response.data && response.data.data) {
      // 提取设备类型选项
      const options = []
      const extractOptions = (nodes) => {
        nodes.forEach(node => {
          if (node.level === '3') { // 只取末级节点作为设备类型选项
            options.push({
              label: node.name,
              value: node.name
            })
          }
          if (node.children && node.children.length > 0) {
            extractOptions(node.children)
          }
        })
      }
      extractOptions(response.data.data)
      deviceTypeOptions.value = options
    }
  } catch (error) {
    console.error('获取设备类型选项失败', error)
    $messageError('获取设备类型选项失败')
  }
}

// 获取列表数据
const getList = async () => {
  loading.value = true
  try {
    const response = await getDeviceIconList({
      page: queryParams.page,
      size: queryParams.size,
      deviceType: queryParams.deviceType,
      deviceStatus: queryParams.deviceStatus,
      fromTime: queryParams.fromTime,
      toTime: queryParams.toTime
    })

    if (response.data && response.data.data) {
      tableData.value = response.data.data.records || []
      total.value = response.data.data.total || 0
    } else {
      tableData.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('获取设备图标列表失败', error)
    $messageError('获取设备图标列表失败')
  } finally {
    loading.value = false
  }
}

// 查询
const handleQuery = () => {
  queryParams.page = 1
  getList()
}

// 重置
const resetQuery = () => {
  queryParams.page = 1
  queryParams.size = 20
  queryParams.deviceType = undefined
  queryParams.deviceStatus = undefined
  queryParams.fromTime = undefined
  queryParams.toTime = undefined
  handleQuery()
}

// 多选框选中数据
const handleSelectionChange = (selection) => {
  selectedIds.value = selection.map(item => item.id)
}

// 新增
const handleAdd = () => {
  reset()
  dialogVisible.value = true
  dialogTitle.value = '添加设备图标'
}

// 编辑
const handleEdit = (row) => {
  reset()
  Object.assign(formData, row)
  dialogVisible.value = true
  dialogTitle.value = '修改设备图标'
}

// 删除
const handleDelete = (row) => {
  ElMessageBox.confirm('是否确认删除该设备图标?', '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await deleteDeviceIcon(row.id)
      $messageSuccess('删除成功')
      getList()
    } catch (error) {
      console.error('删除设备图标失败', error)
      $messageError('删除设备图标失败')
    }
  }).catch(() => {})
}

// 表单重置
const reset = () => {
  Object.assign(formData, {
    id: undefined,
    deviceType: undefined,
    iconUrl: undefined,
    deviceStatus: '正常',
    statusColor: '#67C23A'
  })
  if (form.value) {
    form.value.resetFields()
  }
}

// 取消按钮
const cancelDialog = () => {
  dialogVisible.value = false
  reset()
}

// 上传图标前的钩子
const beforeIconUpload = (file) => {
  const isImage = file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/gif' || file.type === 'image/x-icon'
  const isLt10M = file.size / 1024 / 1024 < 10

  if (!isImage) {
    $messageError('上传图标只能是JPG/PNG/GIF/ICO格式!')
  }
  if (!isLt10M) {
    $messageError('上传图标大小不能超过10MB!')
  }
  return isImage && isLt10M
}

// 图标上传成功处理
const handleIconSuccess = (res, file) => {
  formData.iconUrl = res.data || res
}

// 提交按钮
const submitForm = async () => {
  if (!form.value) return

  await form.value.validate(async (valid) => {
    if (valid) {
      submitLoading.value = true
      try {
        const data = {
          deviceType: formData.deviceType,
          iconUrl: formData.iconUrl,
          deviceStatus: formData.deviceStatus,
          statusColor: formData.statusColor
        }

        if (formData.id) {
          // 修改
          data.id = formData.id
        }

        // 保存
        await saveDeviceIcon(data)
        $messageSuccess(formData.id ? '修改成功' : '新增成功')

        dialogVisible.value = false
        getList()
      } catch (error) {
        console.error('保存设备图标失败', error)
        $messageError('保存设备图标失败')
      } finally {
        submitLoading.value = false
      }
    }
  })
}

// 分页页码改变
const handleCurrentChange = (val) => {
  queryParams.page = val
  getList()
}

// 每页条数变化
const handleSizeChange = (val) => {
  queryParams.size = val
  queryParams.page = 1
  getList()
}

// 监听跳转页码变化
watch(gotoPage, (newVal) => {
  if (newVal && !isNaN(Number(newVal))) {
    const page = parseInt(newVal.toString())
    if (page > 0 && page <= Math.ceil(total.value / queryParams.size)) {
      queryParams.page = page
      getList()
    }
  }
})

// 页面加载时获取列表数据
onMounted(() => {
  getDeviceTypes()
  getList()
})
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.device-icon-container {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

/* 内容区样式 */
.device-list-container {
  padding: 20px;
}

/* 搜索表单样式 */
.search-form {
  margin-bottom: 20px;
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
}

/* 操作按钮区样式 */
.action-bar {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
}

/* 移除特定的按钮样式，使用全局按钮样式 */

/* 表格样式 */
:deep(.el-table) {
  margin-bottom: 20px;
  border-radius: 4px;
  overflow: hidden;
}

:deep(.el-table th) {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 500;
  padding: 12px 0;
  font-size: 14px;
}

:deep(.el-table td) {
  padding: 12px 0;
  font-size: 14px;
}

/* 分页样式 */
.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

/* 对话框样式 */
:deep(.el-dialog) {
  border-radius: 4px;
}

:deep(.el-dialog__header) {
  padding: 15px 20px;
  background-color: #f5f7fa;
  margin-right: 0;
  border-bottom: 1px solid #e6e6e6;
}

:deep(.el-dialog__title) {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 15px 20px;
  border-top: 1px solid #e6e6e6;
}

/* 表单样式 */
.form-container {
  padding: 10px;
}

.form-container :deep(.el-form-item__label) {
  font-weight: 400;
  color: #606266;
}

/* 上传组件样式 */
.avatar-uploader {
  width: 100%;
  text-align: center;
}

.avatar-uploader .avatar {
  width: 100px;
  height: 100px;
  display: block;
  object-fit: contain;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  margin: 0 auto;
}

.avatar-uploader .avatar-uploader-icon {
  width: 100px;
  height: 100px;
  line-height: 100px;
  font-size: 28px;
  color: #8c939d;
  text-align: center;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  margin: 0 auto;
}

.avatar-uploader .avatar:hover,
.avatar-uploader .avatar-uploader-icon:hover {
  border-color: #409eff;
}

.el-upload__tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
  text-align: center;
}

/* 颜色显示框 */
.color-box {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  margin: 0 auto;
  border: 1px solid #dcdfe6;
}

/* 按钮样式 */
:deep(.el-button) {
  border-radius: 4px;
}

:deep(.el-button--primary) {
  background-color: #409eff;
  border-color: #409eff;
}

:deep(.el-button--danger) {
  background-color: #f56c6c;
  border-color: #f56c6c;
}

:deep(.el-button--small) {
  padding: 8px 15px;
  font-size: 12px;
  border-radius: 3px;
}

/* 对话框按钮样式 */
.dialog-footer .el-button {
  width: 100px;
  height: 40px;
  margin-left: 10px;
}
</style>
