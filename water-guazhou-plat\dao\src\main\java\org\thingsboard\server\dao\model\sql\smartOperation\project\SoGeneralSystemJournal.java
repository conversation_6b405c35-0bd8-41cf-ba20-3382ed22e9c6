package org.thingsboard.server.dao.model.sql.smartOperation.project;

import static org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemJournal.SoGeneralSystemJournalDetail.*;

public enum SoGeneralSystemJournal {
    SO_PROJECT_JOURNAL(SO_PROJECT_CREATE, null),
    SO_CONSTRUCTION_JOURNAL(SO_CONSTRUCTION_CREATE, null),
    SO_CONSTRUCTION_DESIGN_JOURNAL(SO_CONSTRUCTION_DESIGN_CREATE, SO_CONSTRUCTION_DESIGN_COMPLETE),
    SO_CONSTRUCTION_ESTIMATE_JOURNAL(SO_CONSTRUCTION_ESTIMATE_CREATE, SO_CONSTRUCTION_ESTIMATE_COMPLETE),
    SO_CONSTRUCTION_VISA_JOURNAL(SO_CONSTRUCTION_VISA_CREATE, SO_CONSTRUCTION_VISA_COMPLETE),
    SO_CONSTRUCTION_CONTRACT_JOURNAL(SO_CONSTRUCTION_CONTRACT_CREATE, SO_CONSTRUCTION_CONTRACT_COMPLETE),
    SO_CONSTRUCTION_EXPENSE_JOURNAL(SO_CONSTRUCTION_EXPENSE_CREATE, SO_CONSTRUCTION_EXPENSE_COMPLETE),
    SO_CONSTRUCTION_APPLY_JOURNAL(SO_CONSTRUCTION_APPLY_CREATE, SO_CONSTRUCTION_APPLY_COMPLETE),
    SO_CONSTRUCTION_ACCEPT_JOURNAL(SO_CONSTRUCTION_ACCEPT_CREATE, SO_CONSTRUCTION_ACCEPT_COMPLETE),
    SO_CONSTRUCTION_SETTLEMENT_JOURNAL(SO_CONSTRUCTION_SETTLEMENT_CREATE, SO_CONSTRUCTION_SETTLEMENT_COMPLETE),
    ;

    public enum SoGeneralSystemJournalDetail {
        SO_PROJECT_CREATE("项目%s启动", "启动"),
        SO_CONSTRUCTION_CREATE(null, null),
        SO_CONSTRUCTION_DESIGN_CREATE(null, null),
        SO_CONSTRUCTION_DESIGN_COMPLETE("完成%s设计", "设计"),
        SO_CONSTRUCTION_ESTIMATE_CREATE(null, null),
        SO_CONSTRUCTION_ESTIMATE_COMPLETE("完成%s预算", "预算"),
        SO_CONSTRUCTION_VISA_CREATE("签订%s合同", "合同"),
        SO_CONSTRUCTION_VISA_COMPLETE("完成%s合同", "合同"),
        SO_CONSTRUCTION_CONTRACT_CREATE("签订%s合同", "合同"),
        SO_CONSTRUCTION_CONTRACT_COMPLETE("完成%s合同", "合同"),
        SO_CONSTRUCTION_EXPENSE_CREATE(null, null),
        SO_CONSTRUCTION_EXPENSE_COMPLETE("完成%s实施", "实施"),
        SO_CONSTRUCTION_APPLY_CREATE("实施期项目%s已受理", "受理"),
        SO_CONSTRUCTION_APPLY_COMPLETE("完成%s实施", "合同"),
        SO_CONSTRUCTION_ACCEPT_CREATE(null, null),
        SO_CONSTRUCTION_ACCEPT_COMPLETE("完成%s验收", "验收"),
        SO_CONSTRUCTION_SETTLEMENT_CREATE(null, null),
        SO_CONSTRUCTION_SETTLEMENT_COMPLETE(null, null),
        ;

        private final String pattern;
        private final String remark;

        SoGeneralSystemJournalDetail(String pattern, String remark) {
            this.pattern = pattern;
            this.remark = remark;
        }

        public String getPattern() {
            return pattern;
        }

        public String getRemark() {
            return remark;
        }

        public boolean disabled() {
            return pattern == null;
        }
    }

    private final SoGeneralSystemJournalDetail onCreateJournal;
    private final SoGeneralSystemJournalDetail onUpdateJournal;

    SoGeneralSystemJournal(SoGeneralSystemJournalDetail onCreateJournal, SoGeneralSystemJournalDetail onUpdateJournal) {
        this.onCreateJournal = onCreateJournal;
        this.onUpdateJournal = onUpdateJournal;
    }

    public SoGeneralSystemJournalDetail getOnCreateJournal() {
        return onCreateJournal;
    }

    public SoGeneralSystemJournalDetail getOnCompleteJournal() {
        return onUpdateJournal;
    }
}
