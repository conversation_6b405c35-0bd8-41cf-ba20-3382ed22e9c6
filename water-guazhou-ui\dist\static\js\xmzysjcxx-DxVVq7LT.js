import{_ as f}from"./CardTable-rdWOL4_6.js";import{d as u,r as l,a8 as _,bF as b,o as x,g,n as y,q as t,F as C,i as s,aB as N,bz as h,C as j}from"./index-r0dFAfgr.js";import{d as z}from"./index-CCFuhOrs.js";import{c as T}from"./manage-BReaEVJk.js";import"./index-C9hz-UZb.js";/* empty css                             */import"./DPlayer-Be2AurQX.js";import"./DPlayer.min-_HMH7IVX.js";const Y=u({__name:"xmzysjcxx",props:{config:{}},setup(n){const a=n,c=l({defaultValue:_(()=>a.config),border:!0,direction:"horizontal",column:2,title:"项目总验收基础信息",fields:[{type:"text",label:"总验时间:",field:"acceptTime",formatter:e=>b(e).format("YYYY-MM-DD")},{type:"text",label:"总验说明:",field:"remark"},{type:"text",label:"创建人:",field:"creatorName"},{type:"text",label:"创建时间:",field:"createTimeName"},{type:"text",label:"最后更新人:",field:"updateUserName"},{type:"text",label:"最后更新时间:",field:"updateTimeName"}]}),o=l({defaultExpandAll:!0,indexVisible:!0,columns:[{label:"工程编号",prop:"code"},{label:"工程名称",prop:"name"},{label:"工程地址",prop:"address"},{label:"工程类别",prop:"typeName"},{label:"工程预算(万元)",prop:"estimate"},{label:"申请单位",prop:"fitstpartName"}],dataList:[],pagination:{hide:!0}}),i=async()=>{const e={page:-1,size:20,projectCode:a.config.projectCode};T(e).then(r=>{o.dataList=r.data.data.data||[]})};return x(()=>{i()}),(e,r)=>{const p=z,d=h,m=f;return g(),y(N,null,[t(d,{class:"card"},{default:C(()=>[t(p,{config:s(c)},null,8,["config"])]),_:1}),t(m,{title:"项目工程",config:s(o),class:"card-table"},null,8,["config"])],64)}}}),v=j(Y,[["__scopeId","data-v-085cbc0b"]]);export{v as default};
