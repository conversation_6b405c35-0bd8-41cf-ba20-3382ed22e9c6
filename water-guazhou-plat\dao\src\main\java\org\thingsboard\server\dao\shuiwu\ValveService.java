package org.thingsboard.server.dao.shuiwu;

import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.shuiwu.ValveEntity;

import java.util.List;

public interface ValveService {
    ValveEntity findById(String id);

    PageData<ValveEntity> findList(int page, int size, String name, TenantId tenantId);

    ValveEntity save(ValveEntity valveEntity);

    void delete(List<String> ids);
}
