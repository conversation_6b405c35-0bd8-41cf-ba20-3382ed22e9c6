package org.thingsboard.server.dao.sql.smartPipe;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.request.PipeCollectDataRequest;
import org.thingsboard.server.dao.model.sql.smartProduction.pipe.PipeCollectData;

import java.util.Date;
import java.util.List;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2023-12-07
 */
@Mapper
public interface PipeCollectDataMapper extends BaseMapper<PipeCollectData> {

    List<PipeCollectData> getList(@Param("param") PipeCollectDataRequest request);

    List<String> getLayerIdList(@Param("mainId") String mainId);

    void batchInsert(@Param("list") List<PipeCollectData> pipeCollectDataList, @Param("tenantId") String tenantId, @Param("userId") String userId, @Param("date") Date date);
}