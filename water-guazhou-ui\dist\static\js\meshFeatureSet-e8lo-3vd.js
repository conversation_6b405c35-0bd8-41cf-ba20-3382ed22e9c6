const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["static/js/loadGLTFMesh-SBgYQU1a.js","static/js/MapView-DaoQedLH.js","static/js/index-r0dFAfgr.js","static/css/index-ByiGXvnH.css","static/js/Point-WxyopZva.js","static/js/widget-BcWKanF2.js","static/js/pe-B8dP0-Ut.js","static/js/mat3f64-BVJGbF0t.js","static/js/MeshComponent-CfWisxg8.js","static/js/imageUtils-IEWq71TJ.js","static/js/BufferView-BcX1hwIm.js","static/js/vec33-BEptSvzS.js","static/js/DefaultMaterial_COLOR_GAMMA-BI6mUG8I.js","static/js/types-Cezv0Yl1.js","static/js/mat4f64-BCm7QTSd.js","static/js/enums-BDQrMlcz.js","static/js/Version-Q4YOKegY.js","static/js/quat-CM9ioDFt.js","static/js/quatf64-QCogZAoR.js","static/js/resourceUtils-CLKdXOwM.js","static/js/basicInterfaces-Dc_Mm1a-.js","static/js/Indices-iFKW8TWb.js","static/js/georeference-GB1Pt0mj.js","static/js/spatialReferenceEllipsoidUtils-j_kxMN-4.js","static/js/axisAngleDegrees-CVgmQKGQ.js","static/js/projection-oyk5Uk7v.js","static/js/gltfexport-C8QDGHrE.js","static/js/imageutils-KgbVacIV.js"])))=>i.map(i=>d[i]);
import{dC as C,af as y,aQ as fe,be as Oe,bf as Ee,bh as X,ae as Be,aR as Ie,aW as Y,a$ as Ve,a_ as Ze,fT as Ce,aP as _e,f as We,aB as Je,w as se,v as Xe,ag as re,bH as Ye,g as He}from"./MapView-DaoQedLH.js";import{R as c,a3 as H,T as K}from"./index-r0dFAfgr.js";import{i as x,U as pe,w as L,V as Ke,am as Qe,S as qe,s as ie,a2 as et,e as A,y as F,a as tt,b as nt}from"./Point-WxyopZva.js";import{K as rt,l as ot}from"./widget-BcWKanF2.js";import{a as V,v as ae,g as le,x as st,k as J}from"./axisAngleDegrees-CVgmQKGQ.js";import{g as U,p as Q}from"./MeshComponent-CfWisxg8.js";import{r as ne,b as it,x as at,_ as lt,L as q}from"./georeference-GB1Pt0mj.js";import{i as ct}from"./triangulationUtils-Da3LiW_b.js";import{C as ft,G as pt}from"./pe-B8dP0-Ut.js";import{e as ue}from"./mat3f64-BVJGbF0t.js";import{e as ze}from"./mat4f64-BCm7QTSd.js";import{c as he}from"./spatialReferenceEllipsoidUtils-j_kxMN-4.js";import{M as ge,j as me,k as de,R as xe,h as ye,L as we}from"./projection-oyk5Uk7v.js";import"./quat-CM9ioDFt.js";import"./quatf64-QCogZAoR.js";import"./imageUtils-IEWq71TJ.js";import"./BufferView-BcX1hwIm.js";import"./vec33-BEptSvzS.js";import"./earcut-BJup91r2.js";import"./deduplicate-Clsym5GM.js";import"./Indices-iFKW8TWb.js";const ce=x.getLogger("esri.geometry.support.meshUtils.centerAt");function ut(e,n,t){if(!e.vertexAttributes||!e.vertexAttributes.position)return;const r=(t==null?void 0:t.origin)??e.origin;c(e.transform)?((t==null?void 0:t.geographic)!=null&&t.geographic!==e.transform.geographic&&ce.warn(`Specifying the 'geographic' parameter (${t.geographic}) different from the Mesh transform setting (${e.transform.geographic}) is not supported`),ht(e.transform,n,r)):ne(e.spatialReference,t)?gt(e,n,r):mt(e,n,r)}function ht(e,n,t){const r=n.x-t.x,o=n.y-t.y,i=n.hasZ&&t.hasZ?n.z-t.z:0,s=e.origin;e.origin=[s[0]+r,s[1]+o,s[2]+i]}function gt(e,n,t){const r=it(e.vertexAttributes,t,{geographic:!0}),{position:o,normal:i,tangent:s}=at(r,n,{geographic:!0});e.vertexAttributes.position=o,e.vertexAttributes.normal=i,e.vertexAttributes.tangent=s,e.vertexAttributesChanged()}function mt(e,n,t){const r=yt,o=xt;if(C(n,o,e.spatialReference)){if(!C(t,r,e.spatialReference)){const i=e.origin;r[0]=i.x,r[1]=i.y,r[2]=i.z,ce.error(`Failed to project specified origin (wkid:${t.spatialReference.wkid}) to mesh spatial reference (wkid:${e.spatialReference.wkid}).`)}dt(e.vertexAttributes.position,o,r),e.vertexAttributesChanged()}else ce.error(`Failed to project centerAt location (wkid:${n.spatialReference.wkid}) to mesh spatial reference (wkid:${e.spatialReference.wkid})`)}function dt(e,n,t){if(e)for(let r=0;r<e.length;r+=3)for(let o=0;o<3;o++)e[r+o]+=n[o]-t[o]}const xt=y(),yt=y();async function wt(e,n,t){const{loadGLTFMesh:r}=await pe(H(()=>import("./loadGLTFMesh-SBgYQU1a.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25])),t),o=await Se(n,t),i=r(new L({x:0,y:0,z:0,spatialReference:e.spatialReference}),o.url,{resolveFile:vt(o),useTransform:!0,signal:c(t)?t.signal:null});i.then(()=>o.dispose(),()=>o.dispose());const{vertexAttributes:s,components:a}=await i;e.vertexAttributes=s,e.components=a}function vt(e){const n=ft(e.url);return t=>{const r=pt(t,n,n),o=r?r.replace(/^ *\.\//,""):null;return(o?e.files.get(o):null)??t}}async function Se(e,n){return e instanceof Blob?ee.fromBlob(e):typeof e=="string"?new ee(e):Array.isArray(e)?bt(e,n):At(e,n)}async function bt(e,n){const t=new Map;let r=null;const o=await Ke(e.map(async s=>({name:s.name,source:await Se(s instanceof Blob?s:s.source,n)}))),i=[];for(const s of o)s&&(Qe(n)?s.source.dispose():i.push(s));qe(n);for(const{name:s,source:a}of i)(K(r)||/\.(gltf|glb)/i.test(s))&&(r=a.url),t.set(s,a.url),a.files&&a.files.forEach((l,p)=>t.set(p,l));if(K(r))throw new ie("mesh-load-external:missing-files","Missing files to load external mesh source");return new ee(r,()=>i.forEach(({source:s})=>s.dispose()),t)}async function At(e,n){const{default:t}=await pe(H(()=>import("./pe-B8dP0-Ut.js").then(o=>o.a6),__vite__mapDeps([6,2,3,4])),n),r=typeof e.multipart[0]=="string"?await Promise.all(e.multipart.map(async o=>(await t(o,{responseType:"array-buffer"})).data)):e.multipart;return ee.fromBlob(new Blob(r))}let ee=class ke{constructor(n,t=()=>{},r=new Map){this.url=n,this.dispose=t,this.files=r}static fromBlob(n){const t=URL.createObjectURL(n);return new ke(t,()=>URL.revokeObjectURL(t))}};function $t(e,n,t){e.vertexAttributes&&e.vertexAttributes.position&&(c(e.transform)?((t==null?void 0:t.geographic)!=null&&t.geographic!==e.transform.geographic&&x.getLogger("esri.geometry.support.meshUtils.offset").warn(`Specifying the 'geographic' parameter (${t.geographic}) different from the Mesh transform setting (${e.transform.geographic}) is not supported`),Rt(e.transform,n)):ne(e.spatialReference,t)?Ft(e,n):Mt(e,n))}function Rt(e,n){const t=e.origin;e.origin=fe(y(),t,n)}function Ft(e,n){const t=e.spatialReference,r=e.vertexAttributes.position,o=e.vertexAttributes.normal,i=e.vertexAttributes.tangent,s=new Float64Array(r.length),a=c(o)?new Float32Array(o.length):null,l=c(i)?new Float32Array(i.length):null,p=e.extent.center,f=Lt;Oe(t,[p.x,p.y,p.z],ve,he(t)),Ee(be,ve),X(f,n,be),ge(r,t,s),c(o)&&c(a)&&me(o,r,s,t,a),c(i)&&c(l)&&de(i,r,s,t,l),De(s,f),xe(s,r,t),c(o)&&c(a)&&ye(a,r,s,t,o),c(i)&&c(l)&&we(l,r,s,t,i),e.vertexAttributesChanged()}function Mt(e,n){De(e.vertexAttributes.position,n),e.vertexAttributesChanged()}function De(e,n){if(e)for(let t=0;t<e.length;t+=3)for(let r=0;r<3;r++)e[t+r]+=n[r]}const Lt=y(),ve=ze(),be=ue();function Pt(){const{faceDescriptions:e,faceVertexOffsets:n,uvScales:t}=kt,r=4*e.length,o=new Float64Array(3*r),i=new Float32Array(3*r),s=new Float32Array(2*r),a=new Uint32Array(2*e.length*3);let l=0,p=0,f=0,u=0;for(let g=0;g<e.length;g++){const h=e[g],v=l/3;for(const d of n)a[u++]=v+d;const P=h.corners;for(let d=0;d<4;d++){const $=P[d];let b=0;s[f++]=.25*t[d][0]+h.uvOrigin[0],s[f++]=h.uvOrigin[1]-.25*t[d][1];for(let R=0;R<3;R++)h.axis[R]!==0?(o[l++]=.5*h.axis[R],i[p++]=h.axis[R]):(o[l++]=.5*$[b++],i[p++]=0)}}return{position:o,normal:i,uv:s,faces:a}}function Ot(e,n){const t=e.components[0],r=t.faces,o=Dt[n],i=6*o,s=new Array(6),a=new Array(r.length-6);let l=0,p=0;for(let f=0;f<r.length;f++)f>=i&&f<i+6?s[l++]=r[f]:a[p++]=r[f];if(c(e.vertexAttributes.uv)){const f=new Float32Array(e.vertexAttributes.uv),u=4*o*2,g=[0,1,1,1,1,0,0,0];for(let h=0;h<g.length;h++)f[u+h]=g[h];e.vertexAttributes.uv=f}return e.components=[new U({faces:s,material:t.material}),new U({faces:a})],e}function Et(e=0){const n=Math.round(8*2**e),t=2*n,r=(n-1)*(t+1)+2*t,o=new Float64Array(3*r),i=new Float32Array(3*r),s=new Float32Array(2*r),a=new Uint32Array(3*((n-1)*t*2));let l=0,p=0,f=0,u=0;for(let g=0;g<=n;g++){const h=g/n*Math.PI+.5*Math.PI,v=Math.cos(h),P=Math.sin(h);m[2]=P;const d=g===0||g===n,$=d?t-1:t;for(let b=0;b<=$;b++){const R=b/$*2*Math.PI;m[0]=-Math.sin(R)*v,m[1]=Math.cos(R)*v;for(let O=0;O<3;O++)o[l]=.5*m[O],i[l]=m[O],++l;s[p++]=(b+(d?.5:0))/t,s[p++]=g/n,g!==0&&b!==t&&(g!==n&&(a[f++]=u,a[f++]=u+1,a[f++]=u-t),g!==1&&(a[f++]=u,a[f++]=u-t,a[f++]=u-t-1)),u++}}return{position:o,normal:i,uv:s,faces:a}}function It(e=0){const t=Math.round(16*2**e),r=4*(t+1)+2*t,o=new Float64Array(3*r),i=new Float32Array(3*r),s=new Float32Array(2*r),a=new Uint32Array(3*(4*t));let l=0,p=0,f=0,u=0,g=0;for(let h=0;h<=5;h++){const v=h===0||h===5,P=h<=1||h>=4,d=h===2||h===4,$=v?t-1:t;for(let b=0;b<=$;b++){const R=b/$*2*Math.PI,O=v?0:.5;m[0]=O*Math.sin(R),m[1]=O*-Math.cos(R),m[2]=h<=2?.5:-.5;for(let z=0;z<3;z++)o[l++]=m[z],i[p++]=P?z===2?h<=1?1:-1:0:z===2?0:m[z]/O;s[f++]=(b+(v?.5:0))/t,s[f++]=h<=1?1*h/3:h<=3?1*(h-2)/3+1/3:1*(h-4)/3+2/3,d||h===0||b===t||(h!==5&&(a[u++]=g,a[u++]=g+1,a[u++]=g-t),h!==1&&(a[u++]=g,a[u++]=g-t,a[u++]=g-t-1)),g++}}return{position:o,normal:i,uv:s,faces:a}}function Ct(e,n){const t=typeof n=="number"?n:n!=null?n.width:1,r=typeof n=="number"?n:n!=null?n.height:1;switch(e){case"up":case"down":return{width:t,depth:r};case"north":case"south":return{width:t,height:r};case"east":case"west":return{depth:t,height:r}}}function _t(e){const n=B.facingAxisOrderSwap[e],t=B.position,r=B.normal,o=new Float64Array(t.length),i=new Float32Array(r.length);let s=0;for(let a=0;a<4;a++){const l=s;for(let p=0;p<3;p++){const f=n[p],u=Math.abs(f)-1,g=f>=0?1:-1;o[s]=t[l+u]*g,i[s]=r[l+u]*g,s++}}return{position:o,normal:i,uv:new Float32Array(B.uv),faces:new Uint32Array(B.faces),isPlane:!0}}const S=1,k=2,D=3,B={position:[-.5,-.5,0,.5,-.5,0,.5,.5,0,-.5,.5,0],normal:[0,0,1,0,0,1,0,0,1,0,0,1],uv:[0,1,1,1,1,0,0,0],faces:[0,1,2,0,2,3],facingAxisOrderSwap:{east:[D,S,k],west:[-D,-S,k],north:[-S,D,k],south:[S,-D,k],up:[S,k,D],down:[S,-k,-D]}};function Z(e,n,t){e.isPlane||zt(e),St(e,t==null?void 0:t.size);const{vertexAttributes:r,transform:o}=lt(e,n,t);return{vertexAttributes:new Q({...r,uv:e.uv}),transform:o,components:[new U({faces:e.faces,material:t&&t.material||null})],spatialReference:n.spatialReference}}function zt(e){for(let n=0;n<e.position.length;n+=3)e.position[n+2]+=.5}function St(e,n){if(n==null)return;const t=typeof n=="number"?[n,n,n]:[n.width!=null?n.width:1,n.depth!=null?n.depth:1,n.height!=null?n.height:1];E[0]=t[0],E[4]=t[1],E[8]=t[2];for(let r=0;r<e.position.length;r+=3){for(let o=0;o<3;o++)m[o]=e.position[r+o];X(m,m,E);for(let o=0;o<3;o++)e.position[r+o]=m[o]}if(t[0]!==t[1]||t[1]!==t[2]){E[0]=1/t[0],E[4]=1/t[1],E[8]=1/t[2];for(let r=0;r<e.normal.length;r+=3){for(let o=0;o<3;o++)m[o]=e.normal[r+o];X(m,m,E),Be(m,m);for(let o=0;o<3;o++)e.normal[r+o]=m[o]}}}const kt={faceDescriptions:[{axis:[0,-1,0],uvOrigin:[0,.625],corners:[[-1,-1],[1,-1],[1,1],[-1,1]]},{axis:[1,0,0],uvOrigin:[.25,.625],corners:[[-1,-1],[1,-1],[1,1],[-1,1]]},{axis:[0,1,0],uvOrigin:[.5,.625],corners:[[1,-1],[-1,-1],[-1,1],[1,1]]},{axis:[-1,0,0],uvOrigin:[.75,.625],corners:[[1,-1],[-1,-1],[-1,1],[1,1]]},{axis:[0,0,1],uvOrigin:[0,.375],corners:[[-1,-1],[1,-1],[1,1],[-1,1]]},{axis:[0,0,-1],uvOrigin:[0,.875],corners:[[-1,1],[1,1],[1,-1],[-1,-1]]}],uvScales:[[0,0],[1,0],[1,1],[0,1]],faceVertexOffsets:[0,1,2,0,2,3]},Dt={south:0,east:1,north:2,west:3,up:4,down:5},m=y(),E=ue(),Te=x.getLogger("esri.geometry.support.meshUtils.rotate");function Tt(e,n,t){if(!e.vertexAttributes||!e.vertexAttributes.position||n[3]===0)return;const r=e.spatialReference;if(c(e.transform)){(t==null?void 0:t.geographic)!=null&&t.geographic!==e.transform.geographic&&Te.warn(`Specifying the 'geographic' parameter (${t.geographic}) different from the Mesh transform setting (${e.transform.geographic}) is not supported`);const o=(t==null?void 0:t.origin)??e.transform.getOriginPoint(r);jt(e.transform,n,o)}else{const o=(t==null?void 0:t.origin)??e.origin;ne(e.spatialReference,t)?Nt(e,n,o):Gt(e,n,o)}}function jt(e,n,t){const r=Ie(j,t.x,t.y,t.z),o=Y(j,r,e.origin);e.applyLocalInverse(o,Ae),e.rotation=ae(e.rotation,n,V()),e.applyLocalInverse(o,o),Y(o,o,Ae),e.translation=fe(y(),e.translation,o)}function Nt(e,n,t){const r=e.spatialReference,o=he(r),i=je;C(t,i,o)||C(e.origin,i,o);const s=e.vertexAttributes.position,a=e.vertexAttributes.normal,l=e.vertexAttributes.tangent,p=new Float64Array(s.length),f=c(a)?new Float32Array(a.length):null,u=c(l)?new Float32Array(l.length):null;Oe(o,i,te,o),Ee(Re,te);const g=$e;X(le($e),le(n),Re),g[3]=n[3],ge(s,r,p),c(a)&&c(f)&&me(a,s,p,r,f),c(l)&&c(u)&&de(l,s,p,r,u),G(p,g,3,i),xe(p,s,r),c(a)&&c(f)&&(G(f,g,3),ye(f,s,p,r,a)),c(l)&&c(u)&&(G(u,g,4),we(u,s,p,r,l)),e.vertexAttributesChanged()}function Gt(e,n,t){const r=je;if(!C(t,r,e.spatialReference)){const o=e.origin;r[0]=o.x,r[1]=o.y,r[2]=o.z,Te.error(`Failed to project specified origin (wkid:${t.spatialReference.wkid}) to mesh spatial reference (wkid:${e.spatialReference.wkid}).`)}G(e.vertexAttributes.position,n,3,r),G(e.vertexAttributes.normal,n,3),G(e.vertexAttributes.tangent,n,4),e.vertexAttributesChanged()}function G(e,n,t,r=Ce){if(!K(e)){Ve(te,st(n),le(n));for(let o=0;o<e.length;o+=t){for(let i=0;i<3;i++)j[i]=e[o+i]-r[i];Ze(j,j,te);for(let i=0;i<3;i++)e[o+i]=j[i]+r[i]}}}const j=y(),Ae=y(),$e=V(),te=ze(),Re=ue(),je=y(),Ne=x.getLogger("esri.geometry.support.meshUtils.scale");function Ut(e,n,t){if(!e.vertexAttributes||!e.vertexAttributes.position)return;const r=e.spatialReference;if(c(e.transform)){(t==null?void 0:t.geographic)!=null&&t.geographic!==e.transform.geographic&&Ne.warn(`Specifying the 'geographic' parameter (${t.geographic}) different from the Mesh transform setting (${e.transform.geographic}) is not supported`);const o=(t==null?void 0:t.origin)??e.transform.getOriginPoint(r);Bt(e.transform,n,o)}else{const o=ne(e.spatialReference,t),i=t&&t.origin||e.origin;o?Vt(e,n,i):Zt(e,n,i)}}function Bt(e,n,t){const r=Ie(N,t.x,t.y,t.z),o=Y(N,r,e.origin);e.applyLocalInverse(o,Fe);const i=_e(y(),e.scale,n);e.scale=i,e.applyLocalInverse(o,o),Y(o,o,Fe),e.translation=fe(y(),e.translation,o)}function Vt(e,n,t){const r=e.spatialReference,o=he(r),i=Ue;C(t,i,o)||C(e.origin,i,o);const s=e.vertexAttributes.position,a=e.vertexAttributes.normal,l=e.vertexAttributes.tangent,p=new Float64Array(s.length),f=c(a)?new Float32Array(a.length):null,u=c(l)?new Float32Array(l.length):null;ge(s,r,p),c(a)&&c(f)&&me(a,s,p,r,f),c(l)&&c(u)&&de(l,s,p,r,u),Ge(p,n,i),xe(p,s,r),c(a)&&c(f)&&ye(f,s,p,r,a),c(l)&&c(u)&&we(u,s,p,r,l),e.vertexAttributesChanged()}function Zt(e,n,t){const r=Ue;if(!C(t,r,e.spatialReference)){const o=e.origin;r[0]=o.x,r[1]=o.y,r[2]=o.z,Ne.error(`Failed to project specified origin (wkid:${t.spatialReference.wkid}) to mesh spatial reference (wkid:${e.spatialReference.wkid}).`)}Ge(e.vertexAttributes.position,n,r),e.vertexAttributesChanged()}function Ge(e,n,t=Ce){if(e)for(let r=0;r<e.length;r+=3){for(let o=0;o<3;o++)N[o]=e[r+o]-t[o];_e(N,N,n);for(let o=0;o<3;o++)e[r+o]=N[o]+t[o]}}const N=y(),Fe=y(),Ue=y();var M;const _="esri.geometry.Mesh";let w=M=class extends We(Je.LoadableMixin(rt(et))){constructor(e){super(e),this.components=null,this.transform=null,this.external=null,this.hasZ=!0,this.hasM=!1,this.vertexAttributes=new Q,this.type="mesh"}initialize(){(K(this.external)||this.vertexAttributes.position.length)&&(this.loadStatus="loaded"),this.when(()=>{this.handles.add(ot(()=>{var e;return{vertexAttributes:this.vertexAttributes,components:(e=this.components)==null?void 0:e.map(n=>n.clone())}},()=>this._set("external",null),{once:!0,sync:!0}))})}get hasExtent(){return!this.loaded&&c(this.external)&&c(this.external.extent)||this.loaded&&this.vertexAttributes.position.length>0&&(!this.components||this.components.length>0)}get _boundingInfo(){const e=this.vertexAttributes.position,n=this.spatialReference;if(e.length===0||this.components&&this.components.length===0)return{extent:new se({xmin:0,ymin:0,zmin:0,xmax:0,ymax:0,zmax:0,spatialReference:n}),center:new L({x:0,y:0,z:0,spatialReference:n})};const t=c(this.transform)?this.transform.project(e,n):e;let r=1/0,o=1/0,i=1/0,s=-1/0,a=-1/0,l=-1/0,p=0,f=0,u=0;const g=t.length,h=1/(g/3);let v=0;for(;v<g;){const P=t[v++],d=t[v++],$=t[v++];r=Math.min(r,P),o=Math.min(o,d),i=Math.min(i,$),s=Math.max(s,P),a=Math.max(a,d),l=Math.max(l,$),p+=h*P,f+=h*d,u+=h*$}return{extent:new se({xmin:r,ymin:o,zmin:i,xmax:s,ymax:a,zmax:l,spatialReference:n}),center:new L({x:p,y:f,z:u,spatialReference:n})}}get anchor(){if(c(this.transform))return this.transform.getOriginPoint(this.spatialReference);const e=this._boundingInfo;return new L({x:e.center.x,y:e.center.y,z:e.extent.zmin,spatialReference:this.spatialReference})}get origin(){return c(this.transform)?this.transform.getOriginPoint(this.spatialReference):this._boundingInfo.center}get extent(){return!this.loaded&&c(this.external)&&c(this.external.extent)?this.external.extent.clone():this._boundingInfo.extent}addComponent(e){this.loaded?(this.components||(this.components=[]),this.components.push(U.from(e)),this.notifyChange("components")):x.getLogger(this.declaredClass).error("addComponent()","Mesh must be loaded before applying operations")}removeComponent(e){if(this.loaded){if(this.components){const n=this.components.indexOf(e);if(n!==-1)return this.components.splice(n,1),void this.notifyChange("components")}x.getLogger(this.declaredClass).error("removeComponent()","Provided component is not part of the list of components")}else x.getLogger(this.declaredClass).error("removeComponent()","Mesh must be loaded before applying operations")}rotate(e,n,t,r){return J(oe.x,e,T),J(oe.y,n,Me),J(oe.z,t,Le),ae(T,Me,T),ae(T,Le,T),Tt(this,T,r),this}offset(e,n,t,r){return this.loaded?(W[0]=e,W[1]=n,W[2]=t,$t(this,W,r),this):(x.getLogger(this.declaredClass).error("offset()","Mesh must be loaded before applying operations"),this)}scale(e,n){return this.loaded?(Ut(this,e,n),this):(x.getLogger(this.declaredClass).error("scale()","Mesh must be loaded before applying operations"),this)}centerAt(e,n){return this.loaded?(ut(this,e,n),this):(x.getLogger(this.declaredClass).error("centerAt()","Mesh must be loaded before applying operations"),this)}load(e){return c(this.external)&&this.addResolvingPromise(wt(this,this.external.source,e)),Promise.resolve(this)}updateExternalSource(e){this._set("external",e)}clone(){let e=null;if(this.components){const t=new Map,r=new Map;e=this.components.map(o=>o.cloneWithDeduplication(t,r))}const n={components:e,spatialReference:this.spatialReference,vertexAttributes:this.vertexAttributes.clone(),transform:c(this.transform)?this.transform.clone():null,external:c(this.external)?{source:this.external.source,extent:c(this.external.extent)?this.external.extent.clone():null}:null};return new M(n)}vertexAttributesChanged(){this.notifyChange("vertexAttributes")}async toBinaryGLTF(e){const n=H(()=>import("./gltfexport-C8QDGHrE.js"),__vite__mapDeps([26,6,2,3,4,17,7,18,1,5,8,9,22,14,23,24,10,11,25,15,27,19,20])),t=this.load(),r=await Promise.all([n,t]),{toBinaryGLTF:o}=r[0];return o(this,e)}static createBox(e,n){if(!(e instanceof L))return x.getLogger(_).error(".createBox()","expected location to be a Point instance"),null;const t=new M(Z(Pt(),e,n));return n&&n.imageFace&&n.imageFace!=="all"?Ot(t,n.imageFace):t}static createSphere(e,n){return e instanceof L?new M(Z(Et(n&&n.densificationFactor||0),e,n)):(x.getLogger(_).error(".createSphere()","expected location to be a Point instance"),null)}static createCylinder(e,n){return e instanceof L?new M(Z(It(n&&n.densificationFactor||0),e,n)):(x.getLogger(_).error(".createCylinder()","expected location to be a Point instance"),null)}static createPlane(e,n){if(!(e instanceof L))return x.getLogger(_).error(".createPlane()","expected location to be a Point instance"),null;const t=(n==null?void 0:n.facing)??"up",r=Ct(t,n==null?void 0:n.size);return new M(Z(_t(t),e,{...n,size:r}))}static createFromPolygon(e,n){if(!(e instanceof Xe))return x.getLogger(_).error(".createFromPolygon()","expected polygon to be a Polygon instance"),null;const t=ct(e);return new M({vertexAttributes:new Q({position:t.position}),components:[new U({faces:t.faces,shading:"flat",material:(n==null?void 0:n.material)??null})],spatialReference:e.spatialReference})}static async createFromGLTF(e,n,t){if(!(e instanceof L))throw x.getLogger(_).error(".createfromGLTF()","expected location to be a Point instance"),new ie("invalid-input","Expected location to be a Point instance");const{loadGLTFMesh:r}=await pe(H(()=>import("./loadGLTFMesh-SBgYQU1a.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25])),t);return new M(await r(e,n,t))}static createWithExternalSource(e,n,t){var s;const r=(t==null?void 0:t.extent)??null,o=((s=t==null?void 0:t.transform)==null?void 0:s.clone())??new q;o.origin=[e.x,e.y,e.z??0];const i=e.spatialReference;return new M({external:{source:n,extent:r},transform:o,spatialReference:i})}static createIncomplete(e,n){var i;const t=((i=n==null?void 0:n.transform)==null?void 0:i.clone())??new q;t.origin=[e.x,e.y,e.z??0];const r=e.spatialReference,o=new M({transform:t,spatialReference:r});return o.addResolvingPromise(Promise.reject(new ie("mesh-incomplete","Mesh resources are not complete"))),o}};A([F({type:[U],json:{write:!0}})],w.prototype,"components",void 0),A([F({type:q,json:{write:!0}})],w.prototype,"transform",void 0),A([F({constructOnly:!0})],w.prototype,"external",void 0),A([F({readOnly:!0})],w.prototype,"hasExtent",null),A([F({readOnly:!0})],w.prototype,"_boundingInfo",null),A([F({readOnly:!0})],w.prototype,"anchor",null),A([F({readOnly:!0})],w.prototype,"origin",null),A([F({readOnly:!0,json:{read:!1}})],w.prototype,"extent",null),A([F({readOnly:!0,json:{read:!1,write:!0,default:!0}})],w.prototype,"hasZ",void 0),A([F({readOnly:!0,json:{read:!1,write:!0,default:!1}})],w.prototype,"hasM",void 0),A([F({type:Q,nonNullable:!0,json:{write:!0}})],w.prototype,"vertexAttributes",void 0),w=M=A([tt(_)],w);const oe={x:re(1,0,0),y:re(0,1,0),z:re(0,0,1)},T=V(),Me=V(),Le=V(),W=y(),Pe=w;function vn(e,n,t){const r=t.features;t.features=[],delete t.geometryType;const o=Ye.fromJSON(t);if(o.geometryType="mesh",!t.assetMaps)return o;const i=Ht(n,t.assetMaps),s=o.spatialReference??nt.WGS84,a=t.globalIdFieldName,{outFields:l}=e,p=c(l)&&l.length>0?Wt(l.includes("*")?null:new Set(l)):()=>({});for(const f of r){const u=Jt(f,a,s,n,i);c(u)&&o.features.push(new He({geometry:u,attributes:p(f)}))}return o}function Wt(e){return({attributes:n})=>{if(!n)return{};if(!e)return n;for(const t in n)e.has(t)||delete n[t];return n}}function Jt(e,n,t,r,o){const i=e.attributes[n],s=o.get(i);if(s==null||s.status===I.FAILED||s.url==null)return null;const a=Xt(e,t,r),l=se.fromJSON(e.geometry);l.spatialReference=t;const p=Yt(e.attributes,r,s.projectVertices);return s.status===I.PENDING?Pe.createIncomplete(a,{extent:l,transform:p}):Pe.createWithExternalSource(a,[{name:s.name,source:s.url}],{extent:l,transform:p})}function Xt({attributes:e},n,{transformFieldRoles:t}){return new L({x:e[t.originX],y:e[t.originY],z:e[t.originZ],spatialReference:n})}function Yt(e,{transformFieldRoles:n},t){return new q({translation:[e[n.translationX],-e[n.translationZ],e[n.translationY]],rotation:J([e[n.rotationX],e[n.rotationZ],e[n.rotationY]],e[n.rotationDeg]),scale:[e[n.scaleX],e[n.scaleY],e[n.scaleZ]],geographic:t})}var I;function Ht(e,n){const t=new Map;for(const r of n){const o=r.parentGlobalId;if(o==null)continue;const i=r.assetName,s=r.assetURL,a=r.conversionStatus;let l=t.get(o);if(l==null)switch(l={name:i,status:I.FAILED,url:s,projectVertices:Kt(r.flags).projectVertices},t.set(o,l),a){case"COMPLETED":case"SUBMITTED":l.status=I.COMPLETED;break;case"INPROGRESS":l.status=I.PENDING;break;default:l.status=I.FAILED}else console.warn(`Multiple asset parts not expected. Ignoring additional parts. conflicting assetname: ${r.assetName}`)}return t}function Kt(e){return{projectVertices:e.includes("PROJECT_VERTICES")}}(function(e){e[e.FAILED=0]="FAILED",e[e.PENDING=1]="PENDING",e[e.COMPLETED=2]="COMPLETED"})(I||(I={}));export{vn as meshFeatureSetFromJSON};
