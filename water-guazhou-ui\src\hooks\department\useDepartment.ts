import { ref } from 'vue'
import { getWaterSupplyTree } from '@/api/company_org'
import { formatTree } from '@/utils/GlobalHelper'

const useDepartment = () => {
  const DepartmentTree = ref<NormalOption[]>([])

  // 通过类型查询站点树
  const getDepartmentTree = async (
    depth: string | number
  ): Promise<NormalOption[]> => {
    const res = await getWaterSupplyTree(depth)
    const data = formatTree(
      res.data.data || [],
      {
        label: 'name',
        value: 'id',
        id: 'id',
        children: 'children'
      },
      undefined,
      'department',
      data => {
        return data
      }
    )
    return data
  }
  const init = async (depth = 2) => {
    DepartmentTree.value = await getDepartmentTree(depth)
  }
  return {
    getDepartmentTree,
    DepartmentTree,
    init
  }
}

export default useDepartment
