package org.thingsboard.server.dao.sql.smartService.seats;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.smartService.seats.SeatsUser;

import java.util.List;
import java.util.Map;

/**
 * userMybatis
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-08-27
 */
@Mapper
public interface SeatsUserMapper extends BaseMapper<SeatsUser> {

    List<SeatsUser> getList(@Param("keywords") String keywords, @Param("page") int page, @Param("size") int size);

    int getListCount(@Param("keywords") String keywords);

    Integer getUserNo();

    List<Map<String, String>> selectSeatsIdList(@Param("seatsId") String seatsId, @Param("tenantId") String tenantId);
}
