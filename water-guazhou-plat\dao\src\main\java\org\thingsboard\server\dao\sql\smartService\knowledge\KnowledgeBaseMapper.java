package org.thingsboard.server.dao.sql.smartService.knowledge;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.smartService.knowledge.KnowledgeBase;

import java.util.List;

/**
 * userMybatis
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-08-27
 */
@Mapper
public interface KnowledgeBaseMapper extends BaseMapper<KnowledgeBase> {

    List<KnowledgeBase> getList(@Param("typeIds") List<String> typeIds, @Param("title") String title, @Param("content") String content, @Param("startTime") Long startTime, @Param("endTime") Long endTime, @Param("page") int page, @Param("size") int size, @Param("tenantId") String tenantId);

    int getListCount(@Param("typeIds")  List<String> typeIds, @Param("title") String title, @Param("content") String content, @Param("startTime") Long startTime, @Param("endTime") Long endTime, @Param("tenantId") String tenantId);
}
