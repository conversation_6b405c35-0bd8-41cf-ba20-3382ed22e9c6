<!-- 统一工单-我的工单-我参与的 -->
<template>
  <div class="wrapper">
    <CardSearch
      ref="refSearch"
      :config="SearchConfig"
    ></CardSearch>
    <CardTable
      ref="refTable"
      :config="TableConfig"
      class="card-table"
    ></CardTable>
    <SLDrawer
      ref="refdetail"
      :config="DrawerConfig"
    >
      <detail :id="TableConfig.currentRow?.id"></detail>
    </SLDrawer>
  </div>
</template>
<script lang="ts" setup>
import detail from './components/detail.vue'
import { ICardSearchIns, ICardTableIns, ISLDrawerIns } from '@/components/type'
import OrderStepTagsVue from './components/OrderStepTags.vue'
import {
  formatWorkOrderStatus,
  getOrderTypeOptions,
  initOrderTableColumns,
  WorkOrderStatus
} from './config'
import { GetWorkOrderPageMine, getWorkOrderEmergencyLevelList } from '@/api/workorder'
import { formatterDate, traverse } from '@/utils/GlobalHelper'

const refdetail = ref<ISLDrawerIns>()
const refSearch = ref<ICardSearchIns>()
const refTable = ref<ICardTableIns>()

const state = reactive<{
  WorkOrderEmergencyLevelList: any[],
}>({
  WorkOrderEmergencyLevelList: []
})

function initOptions() {
  // 紧急程度
  getWorkOrderEmergencyLevelList('1').then(res => {
    state.WorkOrderEmergencyLevelList = traverse(res.data.data || [], 'children', { label: 'name', value: 'name' })
  })
}

const SearchConfig = reactive<ISearch>({
  filters: [
    { type: 'input', label: '标题', field: 'title', onChange: () => refreshData() },
    {
      xl: 16,
      type: 'radio-button',
      label: '工单状态',
      field: 'status',
      options: WorkOrderStatus(true),
      formatter: formatWorkOrderStatus
    },
    { type: 'daterange', label: '发起时间', field: 'date' },
    {
      type: 'select-tree',
      label: '类型',
      field: 'type',
      options: getOrderTypeOptions()
    }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          iconifyIcon: 'ep:search',
          click: () => refreshData()
        },
        {
          perm: true,
          text: '重置',
          type: 'default',
          iconifyIcon: 'ep:refresh',
          click: () => {
            // SearchConfig.defaultParams = {}
            refSearch.value?.resetForm()
            refreshData()
          }
        },
        {
          perm: true,
          text: '导出',
          type: 'warning',
          iconifyIcon: 'ep:download',
          click: () => {
            refTable.value?.exportTable()
          }
        }
      ]
    }
  ],
  defaultParams: {
    date: [moment().subtract(1, 'M').format(formatterDate), moment().format(formatterDate)],
    status: ''
  },
  handleSearch: () => refreshData()
})
const TableConfig = reactive<ICardTable>({
  expandable: true,
  expandComponent: shallowRef(OrderStepTagsVue),
  columns: initOrderTableColumns(),
  defaultExpandAll: true,
  dataList: [],
  pagination: {
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page
      TableConfig.pagination.limit = size
      refreshData()
    }
  },
  operations: [
    {
      perm: true,
      isTextBtn: true,
      text: '详情',
      click: row => handleDetail(row)
    }
  ]
})
const handleDetail = (row: any) => {
  TableConfig.currentRow = row
  refdetail.value?.openDrawer()
  // router.push({
  //   name: 'WorkOrderDetail',
  //   query: {
  //     id: row.id
  //   }
  // })
}
// 明细弹框
const DrawerConfig = reactive<IDrawerConfig>({
  title: '流程明细',
  cancel: false,
  className: 'lightColor',
  group: []
})
const refreshData = async () => {
  TableConfig.loading = true
  try {
    const query = refSearch.value?.queryParams || {}
    const [fromTime, toTime] = query.date?.length === 2
      ? [moment(query.date[0], formatterDate).valueOf(), moment(query.date[1], formatterDate).endOf('D').valueOf()]
      : [moment().subtract(1, 'M').startOf('D').valueOf(), moment().endOf('D').valueOf()]
    const params: any = {
      page: TableConfig.pagination.page || 1,
      size: TableConfig.pagination.limit || 20,
      ...query,
      fromTime,
      toTime
    }
    delete params.date
    const res = await GetWorkOrderPageMine(params)
    TableConfig.dataList = res.data?.data?.data || []
    TableConfig.pagination.total = res.data?.data.total || 0
  } catch (error) {
    //
  }
  TableConfig.loading = false
}
onMounted(() => {
  refreshData()
  initOptions()
})
</script>
<style lang="scss" scoped></style>
