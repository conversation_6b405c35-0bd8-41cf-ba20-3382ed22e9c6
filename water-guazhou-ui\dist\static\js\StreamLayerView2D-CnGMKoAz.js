import{e as p,y as m,a as f,s as y,O as v}from"./Point-WxyopZva.js";import{T as u,R as d}from"./index-r0dFAfgr.js";import{l}from"./widget-BcWKanF2.js";import{bH as _}from"./MapView-DaoQedLH.js";import S from"./FeatureLayerView2D-B3SklxDn.js";import{e as g}from"./util-DPgA-H2V.js";import"./pe-B8dP0-Ut.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./LayerView-BSt9B8Gh.js";import"./schemaUtils-DLXXqxNF.js";import"./enums-L38xj_2E.js";import"./color-DAS1c3my.js";import"./enums-B5k73o5q.js";import"./VertexElementDescriptor-BOD-G50G.js";import"./number-CoJp78Rz.js";import"./utils-DPUVnAXL.js";import"./MaterialKey-BYd7cMLJ.js";import"./alignmentUtils-CkNI7z7C.js";import"./visualVariablesUtils-7_6yXvXo.js";import"./cimAnalyzer-CMgqZsaO.js";import"./fontUtils-BuXIMW9g.js";import"./BidiEngine-CsUYIMdL.js";import"./GeometryUtils-B7ExOJII.js";import"./Rect-CUzevAry.js";import"./callExpressionWithFeature-DgtD4TSq.js";import"./quantizationUtils-DtI9CsYu.js";import"./floatRGBA-PQQNbO39.js";import"./ExpandedCIM-C1laM-_7.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./popupUtils-BjdidZV3.js";import"./RefreshableLayerView-DUeNHzrW.js";function h(e,t){if(u(e)&&u(t))return null;const r={};return d(t)&&(r.geometry=t.toJSON()),d(e)&&(r.where=e),r}let o=class extends S{constructor(){super(...arguments),this._enabledEventTypes=new Set,this._isUserPaused=!1,this.errorString=null,this.connectionStatus="disconnected"}initialize(){this.addHandles([l(()=>this.layer.customParameters,e=>this._proxy.updateCustomParameters(e)),this.layer.on("send-message-to-socket",e=>this._proxy.sendMessageToSocket(e)),this.layer.on("send-message-to-client",e=>this._proxy.sendMessageToClient(e)),l(()=>this.layer.purgeOptions,()=>this._update()),l(()=>this.suspended,e=>{e?this._proxy.pauseStream():this._isUserPaused||this._proxy.resumeStream()})],"constructor")}get connectionError(){if(this.errorString)return new y("stream-controller",this.errorString)}pause(){this._isUserPaused=!0,this._proxy.pauseStream()}resume(){this._isUserPaused=!1,this._proxy.resumeStream()}on(e,t){if(Array.isArray(e))return v(e.map(n=>this.on(n,t)));const r=["data-received","message-received"].includes(e);r&&(this._enabledEventTypes.add(e),this._proxy.enableEvent(e,!0));const i=super.on(e,t),s=this;return{remove(){i.remove(),r&&(s._proxy.closed||s.hasEventListener(e)||s._proxy.enableEvent(e,!1))}}}queryLatestObservations(e,t){var r,i,s;if(!((r=this.layer.timeInfo)!=null&&r.endField||(i=this.layer.timeInfo)!=null&&i.startField||(s=this.layer.timeInfo)!=null&&s.trackIdField))throw new y("streamlayer-no-timeField","queryLatestObservation can only be used with services that define a TrackIdField");return this._proxy.queryLatestObservations(this._cleanUpQuery(e),t).then(n=>{const a=_.fromJSON(n);return a.features.forEach(c=>{c.layer=this.layer,c.sourceLayer=this.layer}),a})}detach(){super.detach(),this.connectionStatus="disconnected"}_createClientOptions(){return{...super._createClientOptions(),setProperty:e=>{this.set(e.propertyName,e.value)}}}_createTileRendererHash(e){const t=`${JSON.stringify(this.layer.purgeOptions)}.${JSON.stringify(h(this.layer.definitionExpression,this.layer.geometryDefinition))})`;return super._createTileRendererHash(e)+t}async _createServiceOptions(){const e=this.layer,{objectIdField:t}=e,r=e.fields.map(a=>a.toJSON()),i=g(e.geometryType),s=e.timeInfo&&e.timeInfo.toJSON()||null,n=e.spatialReference?e.spatialReference.toJSON():null;return{type:"stream",fields:r,geometryType:i,objectIdField:t,timeInfo:s,source:this.layer.parsedUrl,serviceFilter:h(this.layer.definitionExpression,this.layer.geometryDefinition),purgeOptions:this.layer.purgeOptions.toJSON(),enabledEventTypes:Array.from(this._enabledEventTypes.values()),spatialReference:n,maxReconnectionAttempts:this.layer.maxReconnectionAttempts,maxReconnectionInterval:this.layer.maxReconnectionInterval,updateInterval:this.layer.updateInterval,customParameters:e.customParameters}}};p([m()],o.prototype,"errorString",void 0),p([m({readOnly:!0})],o.prototype,"connectionError",null),p([m()],o.prototype,"connectionStatus",void 0),o=p([f("esri.views.2d.layers.StreamLayerView2D")],o);const te=o;export{te as default};
