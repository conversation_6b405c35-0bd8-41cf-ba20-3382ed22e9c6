package org.thingsboard.server.dao.base;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.base.BaseScopeConfiguration;
import org.thingsboard.server.dao.util.imodel.query.base.BaseScopeConfigurationPageRequest;

import java.util.List;

/**
 * 公共管理平台-范围设置Service接口
 *
 * <AUTHOR>
 * @date 2025-06-06
 */
public interface IBaseScopeConfigurationService {
    /**
     * 查询公共管理平台-范围设置
     *
     * @param id 公共管理平台-范围设置主键
     * @return 公共管理平台-范围设置
     */
    public BaseScopeConfiguration selectBaseScopeConfigurationById(String id);

    /**
     * 查询公共管理平台-范围设置列表
     *
     * @param baseScopeConfiguration 公共管理平台-范围设置
     * @return 公共管理平台-范围设置集合
     */
    public IPage<BaseScopeConfiguration> selectBaseScopeConfigurationList(BaseScopeConfigurationPageRequest baseScopeConfiguration);

    /**
     * 新增公共管理平台-范围设置
     *
     * @param baseScopeConfiguration 公共管理平台-范围设置
     * @return 结果
     */
    public int insertBaseScopeConfiguration(BaseScopeConfiguration baseScopeConfiguration);

    /**
     * 修改公共管理平台-范围设置
     *
     * @param baseScopeConfiguration 公共管理平台-范围设置
     * @return 结果
     */
    public int updateBaseScopeConfiguration(BaseScopeConfiguration baseScopeConfiguration);

    /**
     * 批量删除公共管理平台-范围设置
     *
     * @param ids 需要删除的公共管理平台-范围设置主键集合
     * @return 结果
     */
    public int deleteBaseScopeConfigurationByIds(List<String> ids);

    /**
     * 删除公共管理平台-范围设置信息
     *
     * @param id 公共管理平台-范围设置主键
     * @return 结果
     */
    public int deleteBaseScopeConfigurationById(String id);
}
