package org.thingsboard.server.dao.model.sql.smartPipe.dma;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 抄核数据副本-抄表数据
 */
@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@TableName(ModelConstants.PIPE_COPY_DATA_READ_METER_DATA_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class PipeCopyDataReadMeterData {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @TableField(ModelConstants.COPY_DATA_READ_METER_DATA_CODE)
    private String code;

    @TableField(ModelConstants.COPY_DATA_READ_METER_DATA_YM)
    private String ym;

    @TableField(ModelConstants.COPY_DATA_READ_METER_DATA_USER_CODE)
    private String userCode;

    @TableField(ModelConstants.COPY_DATA_READ_METER_DATA_ORG_ID)
    private String orgId;

    @TableField(exist = false)
    private String partitionId;

    @TableField(exist = false)
    private String partitionName;

    @TableField(exist = false)
    private String custCode;

    @TableField(exist = false)
    private String custName;

    @TableField(exist = false)
    private String phone;

    @TableField(ModelConstants.COPY_DATA_READ_METER_DATA_METER_ID)
    private String meterId;

    @TableField(ModelConstants.COPY_DATA_READ_METER_DATA_POINT_ID)
    private String pointId;

    @TableField(ModelConstants.COPY_DATA_READ_METER_DATA_METER_COPY_NUMBER)
    private Integer meterCopyNumber;

    @TableField(ModelConstants.COPY_DATA_READ_METER_DATA_LAST_READ_NUM)
    private BigDecimal lastReadNum;

    @TableField(ModelConstants.COPY_DATA_READ_METER_DATA_LAST_READ_WATER)
    private BigDecimal lastReadWater;

    @TableField(ModelConstants.COPY_DATA_READ_METER_DATA_THIS_READ_NUM)
    private BigDecimal thisReadNum;

    @TableField(ModelConstants.COPY_DATA_READ_METER_DATA_THIS_READ_WATER)
    private BigDecimal thisReadWater;

    @TableField(ModelConstants.COPY_DATA_READ_METER_DATA_APPEND_WATER)
    private BigDecimal appendWater;

    @TableField(ModelConstants.COPY_DATA_READ_METER_DATA_TOTAL_WATER)
    private BigDecimal totalWater;

    @TableField(ModelConstants.PIPE_COPY_DATA_READ_METER_DATA_CORRECT_WATER)
    private BigDecimal correctWater;

    @Column(name = ModelConstants.COPY_DATA_READ_METER_DATA_ORIGIN_WATER)
    private BigDecimal originWater;

    @TableField(ModelConstants.COPY_DATA_READ_METER_DATA_READ_STATUS)
    private String readStatus;

    @TableField(ModelConstants.COPY_DATA_READ_METER_DATA_EXCEPTION_TYPE)
    private String exceptionType;

    @TableField(ModelConstants.COPY_DATA_READ_METER_DATA_LAST_READ_DATE)
    private Date lastReadDate;

    @TableField(ModelConstants.COPY_DATA_READ_METER_DATA_THIS_READ_DATE)
    private Date thisReadDate;

    @TableField(ModelConstants.COPY_DATA_READ_METER_DATA_RECORD_TYPE)
    private String recordType;

    @TableField(ModelConstants.COPY_DATA_READ_METER_DATA_METER_ADDRESS)
    private String meterAddress;

    @TableField(ModelConstants.COPY_DATA_READ_METER_DATA_REMARK)
    private String remark;

    @TableField(ModelConstants.COPY_DATA_READ_METER_DATA_EXECUTE_USER)
    private String executeUser;

    @TableField(ModelConstants.COPY_DATA_READ_METER_DATA_UPDATE_USER)
    private String updateUser;

    @TableField(ModelConstants.READ_METER_DATA_IMGS)
    private String imgs;

    @TableField(ModelConstants.READ_METER_DATA_TYPE)
    private String type;

    @TableField(ModelConstants.READ_METER_DATA_DATA_TIME)
    private Date dataTime;

    @TableField(ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;

}
