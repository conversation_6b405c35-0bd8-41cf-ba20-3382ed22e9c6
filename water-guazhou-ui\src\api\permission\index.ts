import request from '@/plugins/axios';

// 查询当前用户的按钮权限
export function getCurUserBtnPerms() {
  return request({
    url: '/api/menuButton/getCurrentUserMenuButtonList',
    method: 'get'
  });
}

/**
 * 查询按钮权限tree
 * @param menuId
 * @returns
 */
export const getMenuBtnTree = () =>
  request({
    url: `/api/menuButton/tree`,
    method: 'get'
  });

/**
 * 设置角色的按钮权限
 * @param roleId
 * @param data
 * @returns
 */
export const setRoleMenuBtn = (roleId, data) =>
  request({
    url: `/api/menuButton/role/setMenuButtonList/${roleId}`,
    method: 'post',
    data
  });
/**
 * 查询角色的按钮权限
 * @param roleId
 * @returns
 */
export function getRoleButtonList(roleId) {
  return request({
    url: `/api/menuButton/getRoleButtonList/${roleId}`,
    method: 'get'
  });
}
