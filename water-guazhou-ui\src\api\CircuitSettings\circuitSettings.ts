// 智慧管理-巡检配置
import request from '@/plugins/axios';

// 分页查询巡检配置列表
export function getCircuitSettingsList(params: {
  page: number;
  size: number;
  name?: string;
  code?: string;
  type?: string;
  status?: string;
  fromTime?: string;
  toTime?: string;
}) {
  return request({
    url: '/api/sm/circuitSettings',
    method: 'get',
    params
  });
}

// 根据ID查询巡检配置详情
export function getCircuitSettingsById(id: string) {
  return request({
    url: `/api/sm/circuitSettings/${id}`,
    method: 'get'
  });
}

// 新增巡检配置
export function addCircuitSettings(params: {
  name: string;
  code: string;
  type: string;
  status?: string;
  formConfig?: string;
}) {
  return request({
    url: '/api/sm/circuitSettings',
    method: 'post',
    data: params
  });
}

// 修改巡检配置
export function updateCircuitSettings(id: string, params: {
  name?: string;
  code?: string;
  type?: string;
  status?: string;
  formConfig?: string;
}) {
  return request({
    url: `/api/sm/circuitSettings/${id}`,
    method: 'post',
    data: params
  });
}

// 删除巡检配置
export function deleteCircuitSettings(id: string) {
  return request({
    url: `/api/sm/circuitSettings/${id}`,
    method: 'delete'
  });
}
