import{c as N,r as T,l as b,s as I,a1 as O,D as q,ag as R,ah as V,ai as W,b as A,aj as z,ak as H,al as Z}from"./index-r0dFAfgr.js";import{a as J,g as K}from"./index-BggOjNGp.js";import{q as Q}from"./index-cIaXVz1R.js";const te=()=>{const t=N([]);return{getDeviceData:async s=>{var p;const a=await J(s);t.value=O(((p=a.data)==null?void 0:p.map(o=>(o.rId=q(o.id.id),o)))||[],{id:"rId",value:"rId",label:"name"})},deviceList:t}},se=t=>{const i=N();return{chartOption:i,refreshChart:a=>{var p;(p=t.value)==null||p.clear(),i.value={grid:{left:10,right:70,bottom:"20%",top:40,containLabel:!0},dataZoom:[{show:!0,start:0,end:100,dataBackground:{areaStyle:{color:"rgba(189, 210, 225, 0.75)"}},left:10,right:70,bottom:10},{type:"inside",start:0,end:100},{show:!0,yAxisIndex:0,width:30,showDataShadow:!1,right:10,bottom:"20%",top:"15%",filterMode:"none"}],color:["#9E87FF","#73DDFF","#fe9a8b","#F56948","#9E87FF","#0090FF","#36CE9E","#FFC005","#FF515A","#8B5CFF","#00CA69"],tooltip:{trigger:"axis",formatter:o=>{var C;let $=o[0].name;for(let f=0;f<o.length;f++)$+=`<div class="circle" ><span style="background:${o[f].color}"></span>${o[f].seriesName} : ${o[f].value} ${((C=a==null?void 0:a.units.find(k=>k.label===o[f].seriesName))==null?void 0:C.value)||""}</div>`;return $},axisPointer:{type:"cross"}},legend:{type:"scroll",left:10},xAxis:{data:(a==null?void 0:a.xData)||[],boundaryGap:!1,type:"category",axisLine:{},axisLabel:{show:!0},splitLine:{show:!1}},yAxis:{splitLine:{lineStyle:{type:"dashed",color:"#666"}},axisLine:{},min:null,max:null},series:(a==null?void 0:a.series)||[]}}}},ae=t=>{const i=async()=>{const a=await R();s.data=O(a.data||[]),t(V(s.data))},s=T({title:"区域列表",data:[],isFilterTree:!0,treeNodeHandleClick:a=>{s.currentProject=a,t(a)}});return{TreeData:s,refreshProject:i}},le=()=>{const t=T({columns:[],pagination:{hide:!0},dataList:[]});return{refreshTable:s=>{t.dataList=s.data||[],t.columns=s.columns||[]},TableConfig:t}},oe=t=>{let i=null;const s=b().valueOf(),a=()=>{i&&clearInterval(i)};let p=[];const o=[],$=async(e,r)=>{if(!e.setOptionBy)return[];const n=r[e.setOptionBy];if(!n)return[];const y=await K(n),g=[];for(const m in y.data){g.push({label:m,value:m});const w=o.find(d=>d.key===e.field);w?w.props=O(y.data[m]||[],{label:"value",value:"label",id:"label"}):o.push({key:e.field||"",props:O(y.data[m]||[],{label:"value",value:"label",id:"label"})})}e.options=g,e.field&&(r[e.field]=void 0)},C=async(e,r)=>{var g;if(!e.setOptionBy)return[];const n=e.setOptionBy,y=((g=o.find(m=>m.key===n))==null?void 0:g.props)||[];e.options=y,e.field&&(r[e.field]=void 0)},f=e=>{if(!e)return;const r=x.group.findIndex(n=>n.id===e);x.group.splice(r,1)},k=()=>{const e=b().valueOf();x.group.push({id:e,styles:{width:"100%"},fields:[{itemContainerStyle:{width:"180px"},type:"select",label:"",field:`deviceId${e}`,options:p},{itemContainerStyle:{width:"100px"},type:"select",label:"",field:`group${e}`,options:[],setOptionBy:`deviceId${e}`,setOptionMethod:$},{itemContainerStyle:{width:"150px"},type:"select",label:"",field:`prop${e}`,multiple:!0,options:[],setOptionBy:`group${e}`,setOptionMethod:C},{type:"btn-group",btns:[{perm:!0,isTextBtn:!0,text:"",click:()=>f(e),svgIcon:I(W)}]}]})},B=async()=>{var Y,G;const e=(Y=t.refSearch.value)==null?void 0:Y.dataForm,r=[],n=[],y=[];if(x.group.map(l=>{var F;const c=e[`prop${l.id}`]||[],v=e[`deviceId${l.id}`],u=l.fields.find(h=>h.field===`prop${l.id}`),D=u&&((F=u.options)==null?void 0:F.filter(h=>c.indexOf(h.value.toString())!==-1))||[];n.push(...D),c.map(h=>{r.push(`${v}.${h}`)})}),!r.length)return A.warning("请先选择查询项");const[g,m]=t.searchTime?[b(e.date[0]).valueOf(),b(e.date[1]).valueOf()]:[b().subtract(2,"hours").valueOf(),b().valueOf()],w=await Q({start:g,end:m,type:e.type,attributes:Array.from(new Set(r))}),d=[];for(const l in w.data){const c={date:l};for(const v in w.data[l])c[v]=w.data[l][v];d.push(c)}if(!d.length)return A.warning("无数据");const L=[],M=[],P=[];if(d.length)for(const l in d[0])if(l==="date")L.push({iconStyle:{color:"#69e850"},minWidth:150,icon:"iconfont icon-shijian",label:"日期",prop:l}),M.push(...d.map(c=>c[l]));else{const c=l.split("."),v=(G=p.find(S=>S.value===c[0]))==null?void 0:G.label,u=n.find(S=>S.value===c[1]),D=u==null?void 0:u.data.unit,F=D?`（${D}）`:"",h=u==null?void 0:u.label;L.push({minWidth:180,label:`${v}.${h}${F}`,prop:l}),P.push({name:`${v}.${h}`,type:"line",areaStyle:{opacity:.3},data:d.map(S=>S[l])}),y.push({label:`${v}.${h}`,value:D})}t.refreshCall&&t.refreshCall({data:d,columns:L,xData:M,series:P,units:y})},_=e=>{p=e;const r=x.group[0].fields.find(n=>n.field===`deviceId${s}`);r&&(r.options=p)},E=t.searchTime?[{type:"datetimerange",label:"",field:"date",clearable:!1,itemContainerStyle:{width:"350px"}}]:[],j=t.export?[{perm:!0,text:"导出",type:"warning",svgIcon:I(z),click:()=>{t.exportCall&&t.exportCall()}}]:[],x=T({defaultValue:{type:"15m",date:[b().subtract(1,"day").format("YYYY-MM-DD hh:mm:ss"),b().format("YYYY-MM-DD hh:mm:ss")]},size:"small",group:[{id:s,styles:{width:"100%",display:"flex"},fields:[{itemContainerStyle:{width:"180px"},type:"select",label:"",field:`deviceId${s}`,options:[],placeholder:"请选择采集器"},{itemContainerStyle:{width:"100px"},type:"select",label:"",field:`group${s}`,options:[],setOptionBy:`deviceId${s}`,setOptionMethod:$,placeholder:"请选择设备"},{itemContainerStyle:{width:"150px"},type:"select",label:"",field:`prop${s}`,options:[],setOptionBy:`group${s}`,multiple:!0,setOptionMethod:C,placeholder:"请选择变量"},{type:"btn-group",btns:[{perm:!0,type:"primary",text:"",svgIcon:I(H),iconStyles:{marginRight:0},click:k}]},{type:"select",label:"采集间隔:",field:"type",labelWidth:65,clearable:!1,options:[{label:"1 m",value:"1m"},{label:"5 m",value:"5m"},{label:"10 m",value:"10m"},{label:"15 m",value:"15m"}],itemContainerStyle:{width:"180px",marginLeft:"auto"}},...E,{type:"btn-group",btns:[{perm:!0,type:"primary",text:"查询",svgIcon:I(Z),click:()=>{B(),t.withInterval&&(i!==null?clearInterval(i):i=setInterval(()=>{B()},30*1e3))}},...j]}]}]});return{FormConfig:x,clearTimer:a,initFirstLineOfFilters:_}};export{le as a,oe as b,te as c,ae as d,se as u};
