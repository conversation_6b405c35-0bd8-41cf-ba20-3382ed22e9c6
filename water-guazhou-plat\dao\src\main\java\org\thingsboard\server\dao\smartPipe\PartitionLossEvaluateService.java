package org.thingsboard.server.dao.smartPipe;

import com.alibaba.fastjson.JSONObject;
import org.thingsboard.server.dao.model.DTO.PartitionNormalLossEvaluateDTO;
import org.thingsboard.server.dao.model.DTO.PartitionNotNormalLossEvaluateDTO;
import org.thingsboard.server.dao.model.DTO.PartitionTotalDifferenceDTO;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.Partition;

import java.util.List;
import java.util.Map;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2023-05-11
 */
public interface PartitionLossEvaluateService {

    List<PartitionNormalLossEvaluateDTO> getNormalList(String date, String name, String partitionId, String tenantId);

    List<PartitionNotNormalLossEvaluateDTO> getNotNormalList(String date, String name, String partitionId, String tenantId, String type);

    List<PartitionTotalDifferenceDTO> getTotalDifference(String partitionId, String type, String date, String start, String end);

    List<PartitionNotNormalLossEvaluateDTO> getNormalListDetail(String date, String type, String start, String end, String partitionId, String tenantId);

    List<JSONObject> getDayFlowAnalysis(String partitionId, String date, String tenantId);

    /**
     * 获取漏失水量
     * @param partitionIdList
     * @param partitionMap
     * @param startTime
     * @param endTime
     * @param userNumMap
     * @param tenantId
     * @param minHour
     * @param maxHour
     * @return
     */
    Map<String, PartitionNotNormalLossEvaluateDTO> getLossWater(List<String> partitionIdList, Map<String, Partition> partitionMap, Long startTime, Long endTime, Map<String, Integer> userNumMap, String tenantId, int minHour, int maxHour);
}
