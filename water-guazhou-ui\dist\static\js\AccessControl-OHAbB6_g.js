import{_ as le}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as se}from"./CardTable-rdWOL4_6.js";import{_ as ne}from"./CardSearch-CB_HNR-Q.js";import{z as F,d as Z,c as T,a8 as w,am as ie,o as ee,g as h,n as N,q as m,F as p,G as I,bh as D,p as s,i as z,al as re,h as P,fC as de,di as ce,fD as ue,aB as W,aJ as H,aw as pe,bp as me,an as te,x as K,J as fe,cE as _e,H as ve,bb as he,c6 as ge,L as ye,C as ae,a0 as be,r as G,bX as Ce,S as X,l as Ve,b as k}from"./index-r0dFAfgr.js";import{f as Q}from"./DateFormatter-Bm9a68Ax.js";import{o as ke,k as xe}from"./index-D9ERhRP6.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";const De=g=>F({url:"/api/accessControl",method:"post",data:g}),Te=g=>F({url:"/api/accessControl/list",method:"get",params:g}),Se=g=>F({url:"/api/accessControl/remove",method:"delete",data:g}),Le=g=>F({url:"/api/accessControlVideo/batch",method:"post",data:g}),O=g=>F({url:`/api/accessControlVideo/videos/${g}`,method:"get"}),Ae={class:"video-selector"},Ie={class:"video-dialog-content"},Pe={class:"left-panel"},je={class:"search-box"},Ne={class:"tree-container"},Ee={key:0,class:"empty-tree"},Be={class:"tree-node"},ze={class:"node-label"},Fe={class:"right-panel"},$e={class:"video-list-header"},qe={class:"video-list"},Me=["onClick"],Re={class:"video-info"},Ge={class:"video-name"},Ue={class:"video-details"},Ye={class:"video-status"},Je={class:"selection-indicator"},Oe={class:"selected-videos-panel"},We={class:"panel-header"},He={class:"selected-list"},Ke={class:"dialog-footer"},Xe=Z({__name:"VideoSelector",props:{modelValue:{}},emits:["update:modelValue","change"],setup(g,{emit:E}){const A=g,$=E,x=T(!1),b=T(""),S=T([]),r=T([]),o=T([]),q=T(null),M={children:"children",label:"label"},V=w(()=>b.value?r.value.filter(t=>t.name.toLowerCase().includes(b.value.toLowerCase())):r.value);ie(()=>A.modelValue,t=>{t&&(o.value=[...t])},{immediate:!0});const U=()=>{x.value=!0,Y()},Y=async()=>{try{const t=await ke();let a=[];t.data&&(Array.isArray(t.data)?a=t.data:t.data.code===200&&t.data.data||t.data.data?a=t.data.data:a=t.data);const u=e(a);S.value=u}catch(t){K.error("加载视频树失败"),console.error("Load video tree error:",t)}},e=t=>Array.isArray(t)?t.map(a=>({...a,label:a.name||a.label||"未命名",children:a.children&&Array.isArray(a.children)?e(a.children):void 0})):[],i=async t=>{var a,u,f;if(q.value=t,t.type==="group")try{const v=d(t);if(v){const L=await xe({projectId:v,groupId:t.id});r.value=L.data||[]}else r.value=[]}catch{K.error("加载视频列表失败"),r.value=[]}else if(t.type==="video"){const v={id:t.id,name:t.name||t.label,serialNumber:(a=t.nodeDetail)==null?void 0:a.serialNumber,channelId:(u=t.nodeDetail)==null?void 0:u.channelId,online:((f=t.nodeDetail)==null?void 0:f.status)==="1",...t.nodeDetail};_(v)}else r.value=[]},d=t=>{const a=(f,v)=>{for(const L of f)if(L.children){for(const J of L.children)if(J.id===v)return L;const B=a(L.children,v);if(B)return B}return null},u=f=>{if(f.type==="project")return f.id;const v=a(S.value,f.id);if(v)return u(v)};return u(t)},l=()=>{},c=t=>o.value.some(a=>a.id===t),_=t=>{const a=o.value.findIndex(u=>u.id===t.id);a>-1?o.value.splice(a,1):o.value.push(t)},y=()=>{V.value.forEach(t=>{c(t.id)||o.value.push(t)})},C=t=>{const a=o.value.findIndex(u=>u.id===t);a>-1&&o.value.splice(a,1)},R=()=>{o.value=[]},oe=()=>{$("update:modelValue",[...o.value]),$("change",[...o.value]),x.value=!1};return ee(()=>{A.modelValue&&(o.value=[...A.modelValue])}),(t,a)=>{const u=fe,f=_e,v=ve,L=he,B=ge,J=ye;return h(),N("div",Ae,[m(u,{type:"primary",onClick:U},{default:p(()=>[I(" 选择视频 ("+D(o.value.length)+") ",1)]),_:1}),m(J,{modelValue:x.value,"onUpdate:modelValue":a[2]||(a[2]=n=>x.value=n),title:"选择关联视频",width:"80%","close-on-click-modal":!1},{footer:p(()=>[s("div",Ke,[m(u,{onClick:a[1]||(a[1]=n=>x.value=!1)},{default:p(()=>a[6]||(a[6]=[I("取消")])),_:1}),m(u,{type:"primary",onClick:oe},{default:p(()=>[I(" 确定 ("+D(o.value.length)+") ",1)]),_:1})])]),default:p(()=>[s("div",Ie,[s("div",Pe,[s("div",je,[m(v,{modelValue:b.value,"onUpdate:modelValue":a[0]||(a[0]=n=>b.value=n),placeholder:"搜索视频名称",clearable:"",onInput:l},{prefix:p(()=>[m(f,null,{default:p(()=>[m(z(re))]),_:1})]),_:1},8,["modelValue"])]),s("div",Ne,[S.value.length?(h(),P(L,{key:1,ref:"videoTree",data:S.value,props:M,"node-key":"id","expand-on-click-node":!1,"default-expand-all":"",onNodeClick:i},{default:p(({node:n,data:j})=>[s("div",Be,[j.type==="project"?(h(),P(f,{key:0},{default:p(()=>[m(z(de))]),_:1})):j.type==="group"?(h(),P(f,{key:1},{default:p(()=>[m(z(ce))]),_:1})):(h(),P(f,{key:2},{default:p(()=>[m(z(ue))]),_:1})),s("span",ze,D(n.label||j.name||j.id),1)])]),_:1},8,["data"])):(h(),N("div",Ee," 暂无视频数据 "))])]),s("div",Fe,[s("div",$e,[a[4]||(a[4]=s("span",null,"视频列表",-1)),m(u,{type:"primary",size:"small",disabled:!r.value.length,onClick:y},{default:p(()=>a[3]||(a[3]=[I(" 全选当前 ")])),_:1},8,["disabled"])]),s("div",qe,[(h(!0),N(W,null,H(V.value,n=>(h(),N("div",{key:n.id,class:pe(["video-item",{selected:c(n.id)}]),onClick:j=>_(n)},[s("div",Re,[s("div",Ge,D(n.name),1),s("div",Ue,[s("span",null,"设备号: "+D(n.serialNumber||"无"),1),s("span",null,"通道: "+D(n.channelId||"无"),1)])]),s("div",Ye,[m(B,{type:n.online?"success":"danger",size:"small"},{default:p(()=>[I(D(n.online?"在线":"离线"),1)]),_:2},1032,["type"])]),s("div",Je,[c(n.id)?(h(),P(f,{key:0,color:"#409EFF"},{default:p(()=>[m(z(me))]),_:1})):te("",!0)])],10,Me))),128))])])]),s("div",Oe,[s("div",We,[s("span",null,"已选择的视频 ("+D(o.value.length)+")",1),m(u,{type:"danger",size:"small",disabled:!o.value.length,onClick:R},{default:p(()=>a[5]||(a[5]=[I(" 清空选择 ")])),_:1},8,["disabled"])]),s("div",He,[(h(!0),N(W,null,H(o.value,n=>(h(),P(B,{key:n.id,closable:"",onClose:j=>C(n.id)},{default:p(()=>[I(D(n.name),1)]),_:2},1032,["onClose"]))),128))])])]),_:1},8,["modelValue"])])}}}),Qe=ae(Xe,[["__scopeId","data-v-508b7de7"]]),Ze={class:"wrapper"},we=Z({__name:"AccessControl",setup(g){const E=be(),A=T(),$=T(),x=T(),b=G({data:E.projectList,currentProject:E.selectedProject}),S=G({defaultParams:{},filters:[{type:"select-tree",field:"treeData",checkStrictly:!0,defaultExpandAll:!0,options:w(()=>b.data),label:"站点选择",onChange:e=>{const i=Ce(b.data,"children","id",e);E.SET_selectedProject(i),b.currentProject=i,V()}},{label:"门禁名称",type:"input",field:"name"},{type:"btn-group",btns:[{perm:!0,text:"查询",click:()=>V(),icon:"iconfont icon-chaxun"}]}]}),r=G({title:"门禁监控",titleRight:[{style:{marginLeft:"auto"},items:[{type:"btn-group",btns:[{perm:!0,type:"primary",text:"新增",icon:"iconfont icon-jia",click:()=>q()},{perm:!0,type:"danger",text:"批量删除",disabled:()=>{var e;return!((e=r.selectList)!=null&&e.length)},icon:"iconfont icon-shanchu",click:()=>{M()}}]}]}],columns:[{label:"门禁名称",prop:"name"},{label:"门名称",prop:"doorName"},{label:"设备号",prop:"deviceId"},{label:"安装位置",prop:"installAddress"},{label:"安装人员",prop:"installerName"},{label:"安装时间",prop:"installTime",formatter:e=>Q(e.installTime)},{label:"关联视频",prop:"videoCount",formatter:e=>e.videoCount?`${e.videoCount}个`:"无"},{label:"安装说明",prop:"remark"}],dataList:[],operationWidth:250,operations:[{perm:!0,type:"success",isTextBtn:!0,text:"查看视频",size:"small",disabled:e=>!e.videoCount,click:e=>Y(e)},{perm:!0,type:"primary",isTextBtn:!0,text:"编辑",size:"small",click:e=>q(e)},{perm:!0,type:"danger",isTextBtn:!0,text:"删除",size:"small",icon:"iconfont icon-shanchu",click:e=>M(e)}],pagination:{total:0,page:1,align:"right",limit:20,handlePage:e=>{r.pagination.page=e,V()},handleSize:e=>{r.pagination.limit=e,V()}},handleSelectChange:e=>{r.selectList=e||[]}}),o=G({title:"添加门禁监控",dialogWidth:800,group:[{fields:[{type:"input",field:"name",label:"门禁名称",rules:[{required:!0,message:"请输入门禁名称"}]},{type:"input",field:"deviceId",label:"设备号",rules:[{required:!0,message:"请输入设备号"}]},{type:"input",field:"doorName",label:"所在门"},{type:"input",field:"installAddress",label:"安装位置"},{type:"input",field:"installerName",label:"安装人"},{type:"datetime",field:"installTime",label:"安装时间"},{type:"textarea",field:"remark",label:"安装说明"},{type:"slot",field:"videos",label:"关联视频",span:24}]}],submit:e=>{X("确定提交","提示信息").then(async()=>{var i,d;try{o.submitting=!0;const l={...e||{}};l.installTime&&(l.installTime=Ve(l.installTime,"YYYY-MM-DD hh:mm:ss").valueOf());const c=l.videos||[];delete l.videos;const _=await De(l);if((i=_.data)!=null&&i.id){if(c.length>0){const y=c.map(C=>C.id);await Le({accessControlId:_.data.id,videoIds:y})}k.success("操作成功"),(d=x.value)==null||d.closeDialog(),V()}else k.error("操作失败")}catch(l){k.error("操作失败："+(l==null?void 0:l.message)||"未知错误")}o.submitting=!1}).catch(()=>{})}}),q=async e=>{var l,c,_;const i=(c=(l=b.currentProject)==null?void 0:l.data)==null?void 0:c.id;if(!i)return k.warning("范围信息不存在，无法进行添加操作");let d={...e||{projectId:i},installTime:Q(e==null?void 0:e.installTime),videos:[]};if(e!=null&&e.id)try{const y=await O(e.id);d.videos=y.data||[]}catch{}o.defaultValue=d,o.title=e?"编辑门禁监控":"添加门禁监控",(_=x.value)==null||_.openDialog()},M=e=>{X("确定删除？","提示信息").then(async()=>{var i;try{const d=e!=null&&e.id?[e.id]:r.selectList.map(c=>c.id);(i=(await Se(d)).data)!=null&&i.length?(k.success("删除成功"),V()):k.error("删除失败")}catch{k.error("系统错误")}}).catch(()=>{})},V=async()=>{var l,c,_,y;const e=((l=A.value)==null?void 0:l.queryParams)||{},i=await Te({page:r.pagination.page||1,size:r.pagination.limit||20,projectId:(c=b.currentProject)==null?void 0:c.value});let d=(((_=i.data)==null?void 0:_.data)||[]).filter(C=>C.name.indexOf(e.name||"")!==-1);for(const C of d)try{const R=await O(C.id);C.videoCount=(R.data||[]).length}catch{C.videoCount=0}r.dataList=d,r.pagination.total=((y=i.data)==null?void 0:y.total)||0},U=e=>{},Y=async e=>{try{const d=(await O(e.id)).data||[];if(d.length===0){k.info("该门禁暂无关联视频");return}const l=d.map(c=>c.name).join("、");k.info(`关联视频：${l}`)}catch{k.error("获取关联视频失败")}};return ee(async()=>{var e;V(),S.defaultParams={...S.defaultParams,treeData:b.currentProject},(e=A.value)==null||e.resetForm()}),(e,i)=>{const d=ne,l=se,c=le;return h(),N("div",Ze,[m(d,{ref_key:"cardSearch",ref:A,config:S},null,8,["config"]),m(l,{ref_key:"refTable",ref:$,class:"card-table",title:" ",config:r},null,8,["config"]),m(c,{ref_key:"refDialogForm",ref:x,config:o},{fieldSlot:p(({config:_,row:y})=>[_.field==="videos"?(h(),P(Qe,{key:0,modelValue:y.videos,"onUpdate:modelValue":C=>y.videos=C,onChange:U},null,8,["modelValue","onUpdate:modelValue"])):te("",!0)]),_:1},8,["config"])])}}}),rt=ae(we,[["__scopeId","data-v-12ae82f6"]]);export{rt as default};
