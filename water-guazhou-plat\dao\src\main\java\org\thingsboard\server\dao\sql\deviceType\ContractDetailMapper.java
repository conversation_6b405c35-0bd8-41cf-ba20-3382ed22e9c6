package org.thingsboard.server.dao.sql.deviceType;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.purchase.ContractDetail;
import org.thingsboard.server.dao.util.imodel.query.purchase.ContractDetailPageRequest;

import java.util.List;

@Mapper
public interface ContractDetailMapper extends BaseMapper<ContractDetail> {
    IPage<ContractDetail> findByPage(ContractDetailPageRequest request);

    boolean update(ContractDetail entity);

    int saveAll(List<ContractDetail> list);

    int updateAll(List<ContractDetail> list);

    int removeAllByMainOnIdNotIn(@Param("id") String id, @Param("idList") List<String> idList);
}
