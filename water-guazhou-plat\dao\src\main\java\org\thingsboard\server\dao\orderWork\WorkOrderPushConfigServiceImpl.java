package org.thingsboard.server.dao.orderWork;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrderPushConfig;
import org.thingsboard.server.dao.sql.workOrder.WorkOrderPushConfigMapper;

import java.util.Date;
import java.util.List;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2023-12-21
 */

@Service
public class WorkOrderPushConfigServiceImpl implements WorkOrderPushConfigService {

    @Autowired
    private WorkOrderPushConfigMapper workOrderPushConfigMapper;

    @Override
    public WorkOrderPushConfig save(WorkOrderPushConfig workOrderPushConfig) {
        if (StringUtils.isBlank(workOrderPushConfig.getId())) {
            workOrderPushConfig.setCreateTime(new Date());
            workOrderPushConfigMapper.insert(workOrderPushConfig);
        } else {
            workOrderPushConfigMapper.updateById(workOrderPushConfig);
        }

        return workOrderPushConfig;
    }

    @Override
    public List<WorkOrderPushConfig> getList(String tenantId) {
        return workOrderPushConfigMapper.getList(tenantId);
    }

    @Override
    public void delete(String id) {
        workOrderPushConfigMapper.deleteById(id);
    }
}
