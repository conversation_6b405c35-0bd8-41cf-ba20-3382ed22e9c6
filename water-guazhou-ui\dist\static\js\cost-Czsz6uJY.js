import{_ as E}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as k}from"./CardTable-rdWOL4_6.js";import{_ as D}from"./CardSearch-CB_HNR-Q.js";import{d as L,c as d,a8 as g,s as y,r as u,x as l,dP as O,a9 as b,o as v,g as w,n as V,q as m,i as f,b7 as S}from"./index-r0dFAfgr.js";import{I as x}from"./common-CvK_P_ao.js";import{E as F,F as N,G as R,C as q,g as P,D as j,H as B}from"./manage-BReaEVJk.js";import I from"./detail-B0xcRuVt.js";import{S as C,T as U,P as z}from"./data-Dv9-Tstw.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";import"./DateFormatter-Bm9a68Ax.js";import"./detail-DEo1RlcF.js";import"./index-CCFuhOrs.js";/* empty css                             */import"./DPlayer-Be2AurQX.js";import"./DPlayer.min-_HMH7IVX.js";import"./projectManagement-CDcrrCQ1.js";import"./xmwcqk-Cxfq91Sa.js";import"./xmgc-Czrw1pVN.js";import"./cytbgs-WJxYGJyW.js";import"./gcwcqk-CV4EMT8B.js";import"./ssxmwcqk-BJgrXy2o.js";import"./gcsjjcxx-lLqauOhu.js";import"./sjbg-L9B2uWB9.js";import"./data-DDQ4eWNr.js";import"./gcysjcxx-BB9DfF9W.js";import"./qzjbxx-D98fv1p0.js";import"./htjbxx-CcjVPiVa.js";import"./htbg-CJ8T-1F4.js";import"./fygl-BCgGpKLc.js";import"./ssxq-C8LIbr3S.js";import"./ysqgcjcxx-5zZQS7XS.js";import"./ssgcjsjcxx-BD3tZw0Z.js";import"./ssgdjcxx-4P0LZdbp.js";import"./xmzysjcxx-DxVVq7LT.js";import"./xmzjsjcxx-C3UxQ9jk.js";import"./xmzgdjcxx-LKGnYC4Q.js";const A={class:"wrapper"},Ot=L({__name:"cost",setup(M){const p=d(),c=d(),h=d({filters:[{label:"工程编号",field:"constructionCode",type:"input"},{label:"工程名称",field:"constructionName",type:"input"},{label:"工程类别",field:"constructionTypeId",type:"select",options:g(()=>r.projectType)}],operations:[{type:"btn-group",btns:[{type:"default",perm:!0,text:"导出",icon:x.DOWNLOAD,click:()=>{F().then(t=>{const e=window.URL.createObjectURL(t.data),o=document.createElement("a");o.style.display="none",o.href=e,o.setAttribute("download","费用管理.xlsx"),document.body.appendChild(o),o.click()})}},{type:"default",perm:!0,text:"重置",svgIcon:y(S),click:()=>{var t;(t=p.value)==null||t.resetForm(),n()}},{perm:!0,text:"查询",icon:x.QUERY,click:()=>n()}]}]}),i=u({defaultExpandAll:!1,indexVisible:!0,expandable:!0,expandComponent:y(I),extendedReturn:()=>{n()},rowKey:"constructionCode",columns:[{label:"工程编号",prop:"constructionCode"},{label:"工程名称",prop:"constructionName"},{label:"合同总金额(万元)",prop:"contractTotalCost"},{label:"结算总金额(万元)",prop:"finalReportCost"},{label:"已付总金额(万元)",prop:"totalCost"},{label:"工作状态",prop:"status",tag:!0,tagColor:t=>{var e;return((e=C.find(o=>o.value===t.status))==null?void 0:e.color)||""},formatter:t=>{var e;return(e=C.find(o=>o.value===t.status))==null?void 0:e.label}}],operationWidth:"360px",operations:[{disabled:t=>t.status==="COMPLETED",isTextBtn:!1,type:"primary",text:"新增费用明细",perm:!0,click:t=>{_(t)}},{disabled:t=>t.status==="COMPLETED",isTextBtn:!1,type:"success",text:"完成",perm:!0,click:t=>{if(t.items.length===0){l.warning("无费用明细");return}N(t.constructionCode).then(e=>{e.data.code===200?l.success("已完成"):l.warning("完成失败"),n()})}},{isTextBtn:!1,text:"导出支付情况",perm:!0,click:t=>{R(t.constructionCode).then(e=>{const o=window.URL.createObjectURL(e.data),a=document.createElement("a");a.style.display="none",a.href=o,a.setAttribute("download",`${t.constructionName}费用管理.xlsx`),document.body.appendChild(a),a.click()})}}],dataList:[],pagination:{page:1,limit:20,total:0,refreshData:({page:t,size:e})=>{i.pagination.page=t,i.pagination.limit=e,n()}}}),s=u({title:"编辑签证",appendToBody:!0,labelWidth:"150px",dialogWidth:"1000px",submitting:!1,submit:t=>{s.submitting=!0;let e="新增";t.id&&(e="修改"),t.pipLengthDesign=JSON.stringify(t.pipLengthDesign),q(t).then(o=>{var a;s.submitting=!1,o.data.code===200?(l.success(e+"成功"),(a=c.value)==null||a.closeDialog(),n()):l.warning(e+"失败")}).catch(o=>{s.submitting=!1,l.warning(o)})},defaultValue:{},group:[{fields:[{xs:12,type:"input",label:"资金编号",field:"code",disabled:!0},{xs:12,type:"select",label:"费用类型",field:"type",rules:[{required:!0,message:"请选择费用类型"}],options:U},{xs:12,type:"select",label:"所属合同名称",field:"contractCode",options:g(()=>r.contractList),rules:[{required:!0,message:"请选择所属合同名称"}]},{xs:12,type:"number",label:"合同金额(万元)",field:"contractTotalCost",readonly:!0,min:0},{xs:12,type:"select",label:"支付方式",field:"paymentType",options:z},{xs:12,type:"number",label:"金额(万元)",field:"cost",rules:[{required:!0,message:"请输入金额"}],min:0},{xs:12,type:"date",label:"报批时间",field:"approvalTime",format:"x",rules:[{required:!0,message:"请输入报批时间"}]},{xs:12,type:"date",label:"提交财务处理时间",field:"submitFinanceTime",format:"x",rules:[{required:!0,message:"请输入提交财务处理时间"}]},{xs:12,type:"input",label:"一审审核金额(万元)",field:"firstVerifyCost"},{xs:12,type:"input",label:"一审结果单位",field:"firstVerifyOrganization"},{xs:12,type:"input",label:"二审审核金额(万元)",field:"secondVerifyCost"},{xs:12,type:"input",label:"二审结果单位",field:"secondVerifyOrganization"},{xs:12,type:"input",label:"代收款信息",field:"payeeInfo"},{xs:12,type:"input",label:"收款单位",field:"payeeOrganization"},{type:"textarea",label:"说明",field:"remark"},{type:"file",label:"附件",field:"attachments"}]}]}),_=t=>{var e;s.title="新增费用明细",s.defaultValue={constructionCode:t.constructionCode,code:`${t.constructionCode.replace("S","Z")}-${O()}`,type:"预付款",contractTotalCost:t.contractTotalCost,paymentType:"其它方式",cost:0,approvalTime:new Date,submitFinanceTime:new Date,firstVerifyCost:0,secondVerifyCost:0},r.getConstructionContract(t.constructionCode),(e=c.value)==null||e.openDialog()},r=u({projectType:[],contractList:[],getOptions:()=>{P({page:1,size:-1}).then(t=>{r.projectType=b(t.data.data.data||[],"children")})},getConstructionContract:t=>{j(t).then(e=>{r.contractList=b(e.data.data.data||[],"children",{label:"name",value:"code"})})}}),n=async()=>{var e;const t={size:i.pagination.limit||20,page:i.pagination.page||1,...((e=p.value)==null?void 0:e.queryParams)||{}};B(t).then(o=>{i.dataList=o.data.data.data||[],i.pagination.total=o.data.data.total||0})};return v(()=>{n(),r.getOptions()}),(t,e)=>{const o=D,a=k,T=E;return w(),V("div",A,[m(o,{ref_key:"refSearch",ref:p,config:f(h)},null,8,["config"]),m(a,{config:f(i),class:"card-table"},null,8,["config"]),m(T,{ref_key:"refForm",ref:c,config:f(s)},null,8,["config"])])}}});export{Ot as default};
