<template>
  <div class="horizontal-bar-chart-container" ref="chartContainer"></div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, computed } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  // 图表数据
  data: {
    type: Array,
    default: () => [
      {
        name: '类别1',
        value1: 75,
        value2: 50
      },
      {
        name: '类别2',
        value1: 75,
        value2: 50
      },
      {
        name: '类别3',
        value1: 75,
        value2: 50
      },
      {
        name: '类别4',
        value1: 75,
        value2: 50
      },
      {
        name: '类别5',
        value1: 75,
        value2: 50
      }
    ]
  },
  // 图表高度
  height: {
    type: String,
    default: '100%'
  },
  // 图表宽度
  width: {
    type: String,
    default: '100%'
  },
  // 第一个数据系列颜色
  color1: {
    type: String,
    default: '#FFC61A'
  },
  // 第二个数据系列颜色
  color2: {
    type: String,
    default: '#33FFFF'
  },
  // 单位标签
  unitLabel: {
    type: String,
    default: '百户'
  },
  // 是否显示单位标签
  showUnitLabel: {
    type: Boolean,
    default: true
  },
  // 是否显示图例
  showLegend: {
    type: Boolean,
    default: true
  },
  // 系列1名称
  series1Name: {
    type: String,
    default: '系列1'
  },
  // 系列2名称
  series2Name: {
    type: String,
    default: '系列2'
  },
  // Y轴最大值
  maxValue: {
    type: Number,
    default: 100
  }
})

// 计算图例项
const legendItems = computed(() => {
  const items = [
    { name: props.series1Name, color: props.color1 },
    { name: props.series2Name, color: props.color2 }
  ]
  
  return items
})

const chartContainer = ref(null)
let chartInstance = null

// 初始化图表
const initChart = () => {
  if (!chartContainer.value) return
  
  // 销毁之前的实例
  if (chartInstance) {
    chartInstance.dispose()
  }

  // 创建echarts实例
  chartInstance = echarts.init(chartContainer.value)
  
  // 更新图表
  updateChart()
  
  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)
}

// 更新图表
const updateChart = () => {
  if (!chartInstance) return
  
  // 准备Y轴数据 (Y轴是类别轴)
  const yAxisData = props.data.map(item => item.name).reverse()
  
  // 准备系列数据 (需要反转数据，使其与Y轴类别对应)
  const series1Data = props.data.map(item => {
    const total = item.value1 + item.value2;
    // 计算完成比例
    return total > 0 ? Math.round((item.value1 / total) * 100) : 0;
  }).reverse()
  
  const series2Data = props.data.map(item => {
    const total = item.value1 + item.value2;
    // 计算未完成比例
    return total > 0 ? Math.round((item.value2 / total) * 100) : 0;
  }).reverse()
  
  // 原始数值（用于标签显示）
  const originalSeries1Data = props.data.map(item => item.value1).reverse()
  const originalSeries2Data = props.data.map(item => item.value2).reverse()
  
  // 创建渐变色
  const createGradient = (color) => {
    const colorRgb = hexToRgb(color);
    return {
      type: 'linear',
      x: 0,
      y: 0,
      x2: 1,
      y2: 0,
      colorStops: [
        {
          offset: 0,
          color: `rgba(${colorRgb.r}, ${colorRgb.g}, ${colorRgb.b}, 0.1)` // 起始颜色（最左方，完全透明）
        },
        {
          offset: 1,
          color: `rgba(${colorRgb.r}, ${colorRgb.g}, ${colorRgb.b}, 0.8)` // 结束颜色（最右方，透明度0.8）
        },
      ]
    }
  }
  
  // 创建堆叠配置
  const option = {
    color: [props.color1, props.color2], // 设置全局颜色数组
    grid: {
      top: '15%', // 增加顶部空间以容纳图例和单位标签
      left: '0%',
      right: '5%',
      bottom: '0%',
      containLabel: true
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      backgroundColor: 'rgba(0, 19, 40, 0.8)',
      borderColor: 'rgba(26, 198, 255, 0.2)',
      borderWidth: 1,
      textStyle: {
        color: '#fff'
      },
      formatter: function(params) {
        // 找到原始数据
        const index = yAxisData.length - 1 - params[0].dataIndex;
        const item = props.data[index];
        const total = item.value1 + item.value2;
        
        return `${params[0].name}<br/>
                <span style="display:inline-block;margin-right:5px;border-radius:50%;width:10px;height:10px;background-color:${props.color1};"></span>
                ${props.series1Name}: ${item.value1}${props.unitLabel}<br/>
                <span style="display:inline-block;margin-right:5px;border-radius:50%;width:10px;height:10px;background-color:${props.color2};"></span>
                ${props.series2Name}: ${item.value2}${props.unitLabel}`
      }
    },
    title: props.showUnitLabel ? {
      text: '单位: ' + props.unitLabel,
      left: 15,
      top: 5,
      textStyle: {
        color: '#fff',
        fontSize: 12,
        fontWeight: 'normal'
      }
    } : undefined,
    legend: props.showLegend ? {
      data: [props.series1Name, props.series2Name],
      textStyle: {
        color: '#fff',
        fontSize: 12
      },
      right: 20,
      top: 5,
      itemWidth: 8,
      itemHeight: 8,
      itemGap: 15,
      icon: 'rect'
    } : undefined,
    xAxis: {
      type: 'value',
      max: 100,
      axisLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.3)'
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: 'rgba(255, 255, 255, 0.8)',
        fontSize: 12,
        formatter: '{value}'
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)',
          type: 'dashed'
        }
      }
    },
    yAxis: {
      type: 'category',
      data: yAxisData,
      axisLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.3)'
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: 'rgba(255, 255, 255, 0.8)',
        fontSize: 12,
        interval: 0
      }
    },
    series: [
      {
        name: props.series1Name,
        type: 'bar',
        stack: 'total',
        barWidth: '14px', // 设置固定宽度，比12号字体略高
        emphasis: {
          focus: 'series'
        },
        data: series1Data,
        itemStyle: {
          color: (params) => {
            return createGradient(props.color1) // 使用props中定义的颜色1
          }
        },
        markPoint: {
          symbol: 'rect',
          symbolSize: [4, 14], // 宽度4px，高度与柱子一致
          symbolOffset: [0, 0],
          itemStyle: {
            color: props.color1, // 使用props中定义的颜色1
            opacity: 1
          },
          data: series1Data.map((value, index) => {
            return {
              coord: [value, index], // 放在柱子终点位置
              value: ''
            }
          })
        },
        label: {
          show: true,
          position: 'inside',
          formatter: function(params) {
            // 显示原始数值
            return originalSeries1Data[params.dataIndex];
          },
          color: '#fff',
          fontSize: 12
        }
      },
      {
        name: props.series2Name,
        type: 'bar',
        stack: 'total',
        barWidth: '14px', // 设置固定宽度，比12号字体略高
        emphasis: {
          focus: 'series'
        },
        data: series2Data,
        itemStyle: {
          color: (params) => {
            return createGradient(props.color2) // 使用props中定义的颜色2
          }
        },
        markPoint: {
          symbol: 'rect',
          symbolSize: [4, 14], // 宽度4px，高度与柱子一致
          symbolOffset: [0, 0],
          itemStyle: {
            color: props.color2, // 使用props中定义的颜色2
            opacity: 1
          },
          data: series2Data.map((value, index) => {
            // 计算黄色部分的宽度
            const yellowWidth = series1Data[index];
            // 蓝色终点位置 = 黄色宽度 + 蓝色宽度
            const blueEndPoint = yellowWidth + value;
            return {
              coord: [blueEndPoint, index], // 放在蓝色柱子终点位置
              value: ''
            }
          })
        },
        label: {
          show: true,
          position: 'inside',
          formatter: function(params) {
            // 显示原始数值
            return originalSeries2Data[params.dataIndex];
          },
          color: '#fff',
          fontSize: 12
        }
      }
    ]
  };
  
  chartInstance.setOption(option)
}

// 处理窗口大小变化
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

// 监听数据变化
watch(() => props.data, () => {
  updateChart()
}, { deep: true })

// 监听颜色变化
watch(() => [props.color1, props.color2], () => {
  updateChart()
})

// 组件挂载后初始化图表
onMounted(() => {
  initChart()
})

// 组件卸载前销毁图表
onUnmounted(() => {
  if (chartInstance) {
    window.removeEventListener('resize', handleResize)
    chartInstance.dispose()
    chartInstance = null
  }
})

// 十六进制颜色转RGB
function hexToRgb(hex) {
  // 处理简写形式，如 #03F 转为 #0033FF
  const shorthandRegex = /^#?([a-f\d])([a-f\d])([a-f\d])$/i;
  hex = hex.replace(shorthandRegex, (m, r, g, b) => {
    return r + r + g + g + b + b;
  });

  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : {r: 0, g: 0, b: 0};
}
</script>

<style lang="scss" scoped>
.horizontal-bar-chart-container {
  position: relative;
  width: v-bind('width');
  height: v-bind('height');
  background-color: transparent;
}
</style> 