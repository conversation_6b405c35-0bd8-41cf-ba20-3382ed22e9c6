// 智慧生产=水厂管理-水厂报告 api
import request from '@/plugins/axios';

// 水厂报表
export function getWaterPlantFlowReport(params: {
  stationId?: string;
  queryType?: string;
  time?: string;
}) {
  return request({
    url: '/istar/api/production/waterPlant/getWaterPlantFlowReport',
    method: 'get',
    params
  });
}

// 生产报表
export function getWaterSupplyDetailReport(params?: {
  stationIdList: string;
  queryType?: string;
  start?: number | string;
  end?: string | number;
}) {
  return request({
    url: '/istar/api/production/waterPlant/getWaterSupplyDetailReport',
    method: 'get',
    params
  });
}
/**
 * 查询进水报表列表
 * @param params
 * @returns
 */
export const GetWaterProcessDetailReport = (params?: {
  stationIdList: string;
  queryType?: string;
  start?: number | string;
  end?: string | number;
}) => {
  return request({
    url: '/istar/api/production/waterPlant/getWaterProcessDetailReport',
    method: 'get',
    params
  });
};
