package org.thingsboard.server.dao.model.sql.smartOperation.project;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;


@Getter
@Setter
@ResponseEntity
public class SoGeneralType {
    // id
    private String id;

    // 类型名称
    private String name;

    // 类型作用域
    private SoGeneralSystemScope scope;

    // 排序编号
    private Integer orderNum;

    // 客户id
    private String tenantId;

}
