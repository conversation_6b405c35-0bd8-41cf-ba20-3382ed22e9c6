package org.thingsboard.server.dao.sql.smartProduction.guard;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.thingsboard.server.dao.model.sql.smartProduction.guard.GuardRearrangeRecord;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.guard.GuardArrangeSwitchRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.guard.GuardRearrangeRecordPageRequest;

@Mapper
public interface GuardRearrangeRecordMapper extends BaseMapper<GuardRearrangeRecord> {
    IPage<GuardRearrangeRecord> findByPage(GuardRearrangeRecordPageRequest request);

    boolean record(GuardArrangeSwitchRequest req);

}
