import{d as H,a0 as P,M as W,c as I,r as d,s as A,D as p,l as K,S as D,b as C,o as Y,bo as q,i as r,g,h as v,F as b,q as u,an as T,b6 as U,br as $,bp as J,bq as z}from"./index-r0dFAfgr.js";import{_ as G}from"./TreeBox-DDD2iwoR.js";import{_ as Q}from"./CardTable-rdWOL4_6.js";import{_ as X}from"./CardSearch-CB_HNR-Q.js";import{_ as Z}from"./index-BJ-QPYom.js";import{h as ee,i as te,c as ae,a as oe}from"./index-Bj5d3Vsu.js";import{b as ie}from"./index-BggOjNGp.js";import ne from"./confirmDialog-BXY-unA3.js";import re from"./historyTable-DndPxyOU.js";import se from"./infoTable-dSx3myJ7.js";import{f as L}from"./DateFormatter-Bm9a68Ax.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";const Te=H({__name:"RealTimeAlarm",setup(le){const h=P(),{$btnPerms:_}=W(),R=I(),S=I(),N=I(),k=[{label:"紧急",color:"#F56C6C"},{label:"重要",color:"#E6A23C"}],a=d({alarmState:"",deviceName:new Map,alarmInfo:new Map,listFilter:!1,filterItem:"",unconfirmed:0,unsolved:0,unconfirmedList:new Map,confirmInfo:{visible:!1,row:{},close:()=>{a.confirmInfo.visible=!1}},handleInfo:{visible:!1,row:{},close:()=>{a.handleInfo.visible=!1}},historyInfo:{visible:!1,project:{},row:{},close:()=>{a.historyInfo.visible=!1}},detailsInfo:{visible:!1,row:{},close:()=>{a.detailsInfo.visible=!1}},severityColor:{提示:"rgb(85,204,244)",次要:"rgb(255,216,0)",重要:"#f58717",紧急:"rgb(245,75,23)",严重:"#FF0000"},alarmTypes:{offline:"掉线报警",scope:"范围报警",change:"变动报警"}}),f=d({title:"区域划分",data:h.projectList,currentProject:h.selectedProject,isFilterTree:!0,treeNodeHandleClick:t=>{f.currentProject=t,h.SET_selectedProject(t),m()}}),x=d({filters:[{field:"type",type:"select",label:"报警类型",options:[{value:"scope",label:"范围报警"},{value:"change",label:"变动报警"},{value:"offline",label:"掉线报警"}]},{type:"btn-group",btns:[{text:"查询",perm:!0,icon:"iconfont icon-chaxun",click:()=>m()},{text:"批量确认",svgIcon:A(J),perm:_("RealTimeAlarmMultiConfirm"),disabled:()=>{var t;return!((t=n.selectList)!=null&&t.length)},click:()=>O()},{text:"批量解除",type:"danger",svgIcon:A(z),perm:_("RealTimeAlarmMultiRemove"),disabled:()=>{var t;return!((t=n.selectList)!=null&&t.length)},click:()=>w()}]}]}),n=d({loading:!1,dataList:[],selectList:[],handleSelectChange:t=>{n.selectList=t},columns:[{prop:"name",label:"报警名称",minWidth:160},{prop:"showDeviceN",label:"报警设备",minWidth:160},{prop:"showDeviceN",label:"报警类型",minWidth:120},{prop:"cycleName",label:"周期",minWidth:55},{prop:"alarmValue",label:"报警触发值",minWidth:120},{prop:"recoverSet",label:"恢复触发值",minWidth:120},{prop:"createdTime",label:"报警时间",minWidth:180,icon:"iconfont icon-shijian",formatter:t=>L(t.createdTime,"YYYY-MM-DD HH:mm"),iconStyle:{color:"#69e850"}},{prop:"severity",label:"报警级别",tag:!0,tagColor:t=>{var o;return((o=k.find(i=>i.label===t.severity))==null?void 0:o.color)||""},formatter:t=>{var o;return(o=k.find(i=>i.label===t.severity))==null?void 0:o.label}},{prop:"confirm",label:"报警状态",minWidth:240,cellStyle:t=>t.confirm!=="未恢复 | 未确认"?"#36a624":""}],operationFixed:"right",operations:[{text:"强制解除",isTextBtn:!0,perm:_("RealTimeAlarmRemove"),icon:"iconfont icon-xiangqing",click:t=>w(t)},{text:"历史",perm:!0,isTextBtn:!0,icon:"iconfont icon-xiangqing",click:t=>V(t)},{text:"详情",perm:!0,isTextBtn:!0,icon:"iconfont icon-xiangqing",click:t=>B(t)}],operationWidth:"260px",pagination:{refreshData:({page:t,size:o})=>{n.pagination.page=t,n.pagination.limit=o,m()}}}),M=d({title:"历史记录",group:[]}),F=d({title:"告警详情",group:[]}),m=async()=>{var t;if(!f.currentProject.disabled){const o=((t=R.value)==null?void 0:t.queryParams)||{};n.loading=!0,(await ee()).data.forEach(c=>a.alarmInfo.set(p(c.id.id),c.details));const s={keyword:"水质",start:K().subtract(1,"year").startOf("day").valueOf(),end:new Date().getTime(),page:n.pagination.page||1,size:n.pagination.limit||20,...o},e=await te(s,f.currentProject.id);n.loading=!1,j(e.data)}},j=t=>{const o=t.data,i=[];a.unconfirmed=0,a.unsolved=0;const s={day:"日",month:"月",year:"年"};for(const e of o){if(e.severityColor=a.severityColor[e.severity],(e.status==="CONFIRM_UNACK"||e.status==="ACTIVE_ACK")&&(e.confirm="未恢复 | 未确认",a.unconfirmed++,a.unsolved++,a.unconfirmedList.set(e.id.id,e.confirm)),e.status==="CONFIRM_ACK"&&(e.confirm="未恢复 | 已确认",a.unsolved++),e.status==="RESTORE_ACK"&&(e.confirm="已恢复 | 未确认",a.unconfirmed++,a.unconfirmedList.set(e.id.id,e.confirm)),e.alarmType=a.alarmTypes[e.type],e.details!==null){e.alarmRemarks=e.details.alarmRemarks;const l=a.alarmInfo.get(e.alarmJsonId);l?(e.alarmValue=l.attributeName+": "+l.alarmSetValue,e.recoverSet=l.recoverSetValue,e.alarmRemarks=l.alarmRemarks):(e.alarmValue="此条设置已删除",e.recoverSet="此条设置已删除",e.type==="offline"&&(e.alarmValue="-",e.recoverSet="-"))}if(e.alarmCycle?(e.cycleName=s[e.alarmCycle],e.recoverSet="-"):e.cycleName="",e.info=[],e.details){if(e.details.record)for(const l of e.details.record){const y={time:L(parseInt(l.ts),"YYYY-MM-DD HH:mm"),infoValue:l.info,status:l.status.toUpperCase()==="ALARM"?"触发报警":"恢复"};e.info.push(y)}e.activeRemarks=e.details.activeRemarks}const c=a.deviceName.get(e.originator.id)||"设备已删除";e.name=(e.alarmJsonName||"掉线 - ")+c,e.showDeviceN=c,a.listFilter?(a.filterItem==="unconfirmed"&&(e.status==="CONFIRM_UNACK"||e.status==="RESTORE_ACK")&&i.push(e),a.filterItem==="unsolved"&&(e.status==="CONFIRM_UNACK"||e.status==="CONFIRM_ACK")&&i.push(e)):i.push(e)}n.dataList=i,n.pagination.total=t.total},w=t=>{const o=t?"强制解除成功":"批量解除成功";D("确定解除告警, 是否继续?","解除提示").then(()=>{var s;let i=[];t?i=[p(t.id.id)]:i=((s=n.selectList)==null?void 0:s.map(e=>p(e.id.id)))||[],ae({alarmId:i}).then(()=>{C.success(o),m()})})},O=t=>{D("确定解除目标告警, 是否继续?","解除提示").then(()=>{var i;let o=[];{o=((i=n.selectList)==null?void 0:i.map(s=>p(s.id.id)))||[];for(const s of n.selectList||[])if(!a.unconfirmedList.get(s.id.id)){C.error("只能选择未确认数据，请重选");return}}oe({alarmId:o}).then(()=>{C.success("确认成功"),m()})})},V=t=>{var o;a.historyInfo.row=t,a.historyInfo.project=f.currentProject,a.historyInfo.visible=!0,(o=S.value)==null||o.openDrawer()},B=t=>{var o;a.detailsInfo.row=t,a.detailsInfo.visible=!0,(o=N.value)==null||o.openDrawer()},E=async()=>{const t=await ie("info");t.data&&t.data.length&&t.data.forEach(o=>a.deviceName.set(o.id.id,o.name))};return Y(()=>{E()}),(t,o)=>{const i=Z,s=X,e=Q,c=U,l=G,y=$;return q((g(),v(l,null,{tree:b(()=>[u(i,{"tree-data":r(f)},null,8,["tree-data"])]),default:b(()=>[u(s,{ref_key:"refSearch",ref:R,config:r(x)},null,8,["config"]),u(e,{config:r(n),class:"card-table"},null,8,["config"]),r(a).confirmInfo.visible?(g(),v(ne,{key:0,"dialog-info":r(a).confirmInfo,onRefresh:o[0]||(o[0]=ce=>m())},null,8,["dialog-info"])):T("",!0),u(c,{ref_key:"HistoryTableref",ref:S,config:r(M)},{default:b(()=>[r(a).historyInfo.visible?(g(),v(re,{key:0,"dialog-info":r(a).historyInfo,"device-name":r(a).deviceName},null,8,["dialog-info","device-name"])):T("",!0)]),_:1},8,["config"]),u(c,{ref_key:"InfoTableref",ref:N,config:r(F)},{default:b(()=>[r(a).detailsInfo.visible?(g(),v(se,{key:0,"dialog-info":r(a).detailsInfo,"device-name":r(a).deviceName},null,8,["dialog-info","device-name"])):T("",!0)]),_:1},8,["config"])]),_:1})),[[y,!!r(f).loading]])}}});export{Te as default};
