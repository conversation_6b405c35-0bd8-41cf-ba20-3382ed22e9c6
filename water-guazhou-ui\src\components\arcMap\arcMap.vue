<template>
  <div id="viewDiv" ref="refMap" class="viewDiv"></div>
</template>

<script lang="ts" setup>
import { initMap } from '@/utils/MapHelper';
import { AutoLogin } from '@/api/mapservice';
import { useGisStore } from '@/store';

const gisStore = useGisStore()
const refMap = ref<HTMLDivElement>();
const init = async (params?: {
  defaultCenter?: number[];
  zoom?: number;
  showPoi?: boolean;
  defaultBaseMap?:
    | 'vec_c'
    | 'vec_w'
    | 'img_c'
    | 'img_w'
    | 'ter_c'
    | 'ter_w'
    | 'ibo_c'
    | 'ibo_w';
  defaultFilter?: string;
  defaultFilterColor?: string;
}) => {
  if (!gisStore.gToken || !gisStore.gUserInfo) {
    try {
      const res = await AutoLogin();
      if (res.data?.code === 10000) {
        gisStore.SET_gToken(res.data?.result?.token);
        gisStore.SET_gUserInfo({
          ...(res.data?.result || {}),
          username: '0000303'
        });
      }
    } catch (error) {
      console.log('gis用户授权失败');
    }
  }
  return initMap({ el: refMap.value, ...params });
};
defineExpose({
  init
});
</script>

<style lang="scss" scoped>
.viewDiv {
  width: 100%;
  height: 100%;
  background-color: var(--el-bg-color);
  // background-image: radial-gradient(rgba(255, 255, 255, 1), rgba(255, 255, 255, 0.7), #fff);
}
// .dark {
//   .viewDiv {
//     background-color: rgba(43, 51, 73, 0.82);
//     background-image: radial-gradient(
//       rgba(0, 0, 0, 0),
//       rgba(0, 0, 0, 0.3),
//       #000
//     );
//   }
// }
</style>
<style lang="scss">
html {
  body {
    --esri-calcite-theme-name: 'light';
  }

  &.dark {
    body {
      --esri-calcite-theme-name: 'dark';
    }
  }
}
html.dark {
  .esri-input,
  .esri-widget--button,
  .esri-widget,
  .esri-select,
  .esri-menu,
  .esri-popup__pointer-direction,
  .esri-menu__list-item {
    background-color: #2e3449;
    &:hover {
      background-color: #2e3449;
    }
  }
  .esri-basemap-gallery__item:hover,
  .esri-basemap-gallery__item--selected,
  .esri-basemap-gallery__item.esri-basemap-gallery__item--selected:hover,
  .esri-basemap-gallery__item.esri-basemap-gallery__item--selected:focus {
    background-color: var(--el-bg-color);
  }
  .esri-widget--button {
    &.esri-compass,
    &.esri-expand,
    &.esri-home {
      background-color: #2e3449;
      &:hover {
        background-color: #2e3449;
      }
      &:focus-visible {
        outline: none;
      }
    }
  }
  .esri-legend,
  .esri-layer-list__item,
  .esri-layer-list__item-actions-menu-item,
  .esri-coordinate-conversion__select-row,
  .esri-coordinate-conversion__button {
    color: #adadad;
    background-color: var(--el-bg-color);
    &:hover {
      color: #fff;
      background-color: var(--el-bg-color);
    }
  }
  .esri-coordinate-conversion__row {
    .esri-select {
      background-color: var(--el-bg-color);
    }
  }
  .esri-coordinate-conversion__heading {
    background-color: var(--el-fill-color-lighter);
    .esri-coordinate-conversion__back-button {
      &,
      &:hover {
        background-color: transparent;
      }
    }
  }
  .esri-scale-bar {
    background-color: transparent;
  }
  .esri-coordinate-conversion__display {
    &,
    &:hover {
      background-color: transparent;
    }
  }
  .esri-widget--button {
    &,
    &:hover {
      background-color: transparent;
    }
  }
  // .esri-coordinate-conversion__tools{

  // }

  .esri-popup__header-container--button,
  .esri-popup__button,
  .esri-widget.esri-feature,
  .esri-widget.esri-search-result-renderer {
    &,
    &:hover {
      background-color: transparent;
    }
  }

  .esri-view .esri-view-surface--inset-outline:focus::after {
    outline: none;
  }
  .esri-print__layout-tab {
    &:hover,
    &:focus {
      background-color: var(--el-bg-color);
    }
  }
  .esri-print__layout-tab[aria-selected='true'] {
    background-color: var(--el-bg-color);
    border-bottom-color: var(--el-bg-color);
    &:hover {
      background-color: var(--el-bg-color);
    }
  }
  .esri-print__advanced-options-section {
    background-color: transparent;
  }
  .esri-coordinate-conversion--capture-mode {
    .esri-coordinate-conversion__mode-toggle {
      background-color: transparent;
      color: var(--el-text-color-regular);
    }
  }
}
html {
  .esri-widget {
    color: var(--el-text-color-regular);
    background-color: rgba(255, 255, 255, 0.9);
    &:hover {
      color: var(--el-text-color-regular);
    }
  }
  .esri-input,
  .esri-widget--button,
  .esri-select,
  .esri-menu,
  .esri-popup__pointer-direction,
  .esri-menu__list-item {
    color: var(--el-text-color-regular);
    background-color: rgba(255, 255, 255, 0.9);
    &:hover {
      color: var(--el-text-color-regular);
      background-color: rgba(255, 255, 255, 1);
    }
  }
  .esri-basemap-gallery__item:hover,
  .esri-basemap-gallery__item--selected,
  .esri-basemap-gallery__item.esri-basemap-gallery__item--selected:hover,
  .esri-basemap-gallery__item.esri-basemap-gallery__item--selected:focus {
    background-color: #fff;
    box-shadow: 0 0 1px #adadad;
    color: var(--el-text-color-regular);
    .esri-basemap-gallery__item-title {
      color: var(--el-text-color-regular);
    }
  }
  .esri-widget--button {
    &.esri-compass,
    &.esri-expand,
    &.esri-home {
      background-color: rgba(255, 255, 255, 0.9);
      &:hover {
        background-color: rgba(255, 255, 255, 1);
      }
      &:focus-visible {
        outline: none;
      }
    }
  }
  .esri-legend,
  .esri-layer-list__item,
  .esri-layer-list__item-actions-menu-item,
  .esri-coordinate-conversion__select-row,
  .esri-coordinate-conversion__button,
  .esri-layer-list__child-toggle,
  .esri-layer-list__item-toggle {
    color: var(--el-text-color-regular);
    background-color: var(--el-bg-color-page);
    &:hover {
      background-color: var(--el-bg-color-page);
    }
  }
  .esri-layer-list__item-toggle,
  .esri-layer-list__child-toggle {
    background-color: transparent;
    &:hover {
      background-color: transparent;
    }
  }
  .esri-coordinate-conversion__row {
    .esri-select {
      background-color: rgba(255, 255, 255, 1);
    }
  }
  .esri-coordinate-conversion__heading {
    background-color: rgba(255, 255, 255, 1);
    .esri-coordinate-conversion__back-button {
      &,
      &:hover {
        background-color: transparent;
      }
    }
  }
  .esri-coordinate-conversion--capture-mode {
    .esri-coordinate-conversion__mode-toggle {
      background-color: rgba(255, 255, 255, 1);
      color: var(--el-text-color-regular);
    }
  }
  .esri-coordinate-conversion__display {
    &,
    &:hover {
      background-color: transparent;
    }
  }
  .esri-widget--button {
    &,
    &:hover {
      background-color: transparent;
    }
  }
  // .esri-coordinate-conversion__tools{

  // }

  .esri-popup__header-container--button,
  .esri-popup__button,
  .esri-widget.esri-feature,
  .esri-widget.esri-search-result-renderer {
    &,
    &:hover {
      background-color: transparent;
    }
  }

  .esri-view .esri-view-surface--inset-outline:focus::after {
    outline: none;
  }
  .esri-print__layout-tab {
    color: var(--el-text-color-regular);
    &:hover,
    &:focus {
      color: var(--el-text-color-regular);
      background-color: rgba(255, 255, 255, 0.9);
    }
  }
  .esri-print__layout-tab[aria-selected='true'] {
    background-color: rgba(255, 255, 255, 0.9);
    border-bottom-color: rgba(255, 255, 255, 0.9);
    color: var(--el-text-color-regular);
    &:hover {
      color: var(--el-text-color-regular);
      background-color: rgba(255, 255, 255, 0.9);
    }
  }
  .esri-print__advanced-options-button-container,
  .esri-print__advanced-options-section,
  .esri-widget__heading,
  .esri-print__exported-file-link {
    color: var(--el-text-color-regular);
  }
  .esri-print__exported-file-link {
    &:hover {
      color: var(--el-text-color-regular);
    }
  }
  .esri-print__export-panel-container [class^='esri-icon-'] {
    margin: 0;
    margin-right: 4px;
  }
  .esri-print__scale-info-container {
    label {
      display: flex;
      align-items: center;
      input {
        margin-right: 4px;
      }
    }
  }
  .esri-print__advanced-options-section {
    background-color: transparent;
  }
  #tool-search-poi {
    border-radius: 4px;
  }

  .esri-ui-corner.esri-ui-bottom-right {
    .esri-component {
      &:first-child {
        border-radius: 12px 12px 0 0;
        & > .esri-widget--button {
          border-top: none;
        }
      }
      &:last-child {
        border-radius: 0 0 12px 12px;
      }
    }
  }
}
</style>
