@use "sass:color";

@mixin tooltip() {
  $item-cap-padding: $cap-spacing--eighth;

  .esri-tooltip {
    position: absolute;
    overflow: visible;
    width: fit-content;
    top: 0;
    left: 0;
    z-index: 1;

    &-content {
      position: relative;
      padding: $cap-spacing--half $side-spacing--half;
      background: color.change($background-color, $alpha: 0.8);
      backdrop-filter: blur(3px);
      box-shadow: 0 1px 2px rgb(0, 0, 0, 0.1);
      font-family: $font-family;
      font-size: $font-size--small;
      color: $font-color;

      // Hide the content altogether when there is nothing inside. Otherwise we'd
      // see a small empty square due to the tooltip padding.
      &:empty {
        opacity: 0;
      }
    }

    &-table {
      display: table;
      width: 100%;
    }

    &-field {
      display: table-row;

      &__title,
      &__value {
        display: table-cell;
        padding: $item-cap-padding 0;
        white-space: nowrap;
      }

      &__title {
        padding-inline-end: $side-spacing;
      }

      &__value {
        // Make numbers look nice as they change. See https://developer.mozilla.org/en-US/docs/Web/CSS/font-variant-numeric
        font-variant-numeric: tabular-nums;
      }
    }

    &-value-by-value {
      // Add some space between the left/right (e.g. width/height) values.
      span:nth-child(2) {
        margin-inline: $side-spacing--half;
      }
    }

    &-help-message {
      $help-message-border-spacing: $cap-spacing--half;

      width: min-content;
      min-width: 200px;
      margin-top: $help-message-border-spacing;
      padding-top: $help-message-border-spacing + $item-cap-padding;
      padding-bottom: $item-cap-padding;
      border-top: solid 1px $border-color;
      font-family: $font-family;
      font-size: $font-size--small;
      text-align: center;
      white-space: break-spaces;

      &:only-child {
        margin-top: 0;
        padding-top: $item-cap-padding;
        border-top: none;
      }
    }
  }
}

@if $include_Tooltip==true {
  @include tooltip();
}
