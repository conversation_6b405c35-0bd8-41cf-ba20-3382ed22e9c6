package org.thingsboard.server.controller.smartOperation.construction;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionDesignAmend;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionDesignAmendPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionDesignAmendSaveRequest;
import org.thingsboard.server.dao.construction.SoConstructionDesignAmendService;
import org.thingsboard.server.utils.imodel.annotations.IStarController2;

@IStarController2
@RequestMapping("/api/so/constructionDesignAmend")
public class SoConstructionDesignAmendController extends BaseController {
    @Autowired
    private SoConstructionDesignAmendService service;


    @GetMapping
    public IPage<SoConstructionDesignAmend> findAllConditional(SoConstructionDesignAmendPageRequest request) {
        return service.findAllConditional(request);
    }

    @PostMapping
    public SoConstructionDesignAmend save(@RequestBody SoConstructionDesignAmendSaveRequest req) {
        return service.save(req);
    }

    @PatchMapping("/{id}")
    public boolean edit(@RequestBody SoConstructionDesignAmendSaveRequest req, @PathVariable String id) {
        return service.update(req.unwrap(id));
    }

    @DeleteMapping("/{id}")
    public boolean delete(@PathVariable String id) {
        return service.delete(id);
    }
}