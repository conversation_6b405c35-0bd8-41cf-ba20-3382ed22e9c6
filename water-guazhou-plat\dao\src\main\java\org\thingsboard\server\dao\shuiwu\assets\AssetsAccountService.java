package org.thingsboard.server.dao.shuiwu.assets;

import com.alibaba.fastjson.JSONObject;
import org.thingsboard.server.common.data.VO.TreeNodeVO;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.shuiwu.assets.AssetsAccountEntity;

import java.util.List;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2021-10-20
 */
public interface AssetsAccountService {
    PageData getPage(JSONObject params);

    AssetsAccountEntity save(AssetsAccountEntity assetsAccountEntity);

    void delete(List<String> ids);

    AssetsAccountEntity getDetail(String id);

    AssetsAccountEntity findByDeviceId(String deviceId);

    List<String> getDeviceTypeList(String tenantId, String projectId);

    List<AssetsAccountEntity> getListByDeviceType(String deviceType, String tenantId, String projectId);

    List<AssetsAccountEntity> findByTenantId(String tenantId);

    PageData getUnionPage(JSONObject params);

    List<TreeNodeVO> getListTree(String projectId);

    AssetsAccountEntity findById(String id);
}
