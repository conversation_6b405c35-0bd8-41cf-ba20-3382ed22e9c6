import{d as z,M as A,c as p,r as _,s as m,S as D,o as K,bO as U,ay as w,g as H,n as Q,q as f,i as s,F as h,p as X,G as L,J as Y,L as Z,ak as ee,bq as S,al as te,bL as oe,bM as ae,bP as ne,C as se}from"./index-r0dFAfgr.js";import{_ as re}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as le}from"./CardTable-rdWOL4_6.js";import{_ as ie}from"./CardSearch-CB_HNR-Q.js";import{m as ce,n as I,o as F,f as fe}from"./process-DWVjEFpZ.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";const de={class:"wrapper"},me={class:"dialog-footer"},ue=z({__name:"index",setup(ge){const{$messageError:b,$messageSuccess:v,$messageWarning:C}=A(),y=p(),k=p(),B=p(),T=p(),d=p(null),x=p(null),o=_({editRow:null,formJson:{},optionData:{},formData:{},formVisible:!1,submitting:!1}),W=_({languageMenu:!1,externalLink:!1,formTemplates:!1,eventCollapse:!1,widgetNameReadonly:!1,clearDesignerButton:!0,previewFormButton:!0,importJsonButton:!0,exportJsonButton:!0,exportCodeButton:!0,generateSFCButton:!1,toolbarMaxWidth:420,toolbarMinWidth:300,presetCssCode:"",resetFormJson:!1}),q=_({defaultParams:{},filters:[{type:"input",label:"表单名称",field:"name"}],operations:[{type:"btn-group",btns:[{perm:!0,text:"新增",svgIcon:m(ee),click:()=>R()},{perm:!0,text:"删除",type:"danger",svgIcon:m(S),click:()=>{var e;if(console.log(r.selectList),(r.selectList||[]).length>0){const t=(e=r.selectList)==null?void 0:e.map(a=>a.id);J(t)}else C("请选择删除数据")}},{perm:!0,text:"查询",svgIcon:m(te),click:()=>g()}]}]}),r=_({loading:!0,indexVisible:!0,selectList:[],rowKey:"id",columns:[{label:"表单名称",prop:"name",minWidth:120,align:"center"},{prop:"status",label:"是否启用",minWidth:120,align:"center",iconStyle:{color:"#19a39e"},cellStyle:e=>({color:e.status?"#36a624":"#f56c6c"}),formatter:e=>e.status?"是":"否"},{label:"备注",prop:"remark",minWidth:120,align:"center"}],dataList:[{}],operationFixed:"right",operationWidth:320,operations:[{perm:!0,text:"预览",isTextBtn:!1,type:"success",svgIcon:m(oe),click:e=>E(e)},{perm:!0,text:"编辑",isTextBtn:!1,svgIcon:m(ae),click:e=>R(e)},{perm:!0,text:"删除",isTextBtn:!1,type:"danger",svgIcon:m(S),click:e=>J([e.id])},{perm:!0,text:"设计",isTextBtn:!1,type:"warning",svgIcon:m(ne),click:e=>M(e)}],pagination:{refreshData:({page:e,size:t})=>{r.pagination.page=e,r.pagination.limit=t,g()}},handleSelectChange:e=>{r.selectList=e}}),u=_({title:"新增",labelWidth:120,dialogWidth:500,group:[{fields:[{type:"input",label:"表单名",field:"name",rules:[{required:!0,message:"请填写表单名"}],placeholder:"请填写表单名"},{type:"switch",label:"是否启用",field:"status",rules:[{required:!0,message:"请选择是否启用"}]},{type:"textarea",label:"备注",field:"remark",placeholder:"请填写备注"}]}]}),N=_({title:"表单预览",desTroyOnClose:!0,group:[]}),J=e=>{D("确定删除？","提示信息").then(()=>{ce(e).then(()=>{v("删除成功"),g()}).catch(t=>{C(t)})})},R=async e=>{var t;u.defaultValue={...e||{status:!0}},u.submit=a=>{D("确定提交？","提示信息").then(()=>{u.submitting=!0,a={...a,id:e?e.id:null},I(a).then(()=>{var n;(n=y.value)==null||n.closeDialog(),u.submitting=!1,v("保存成功"),g()}).catch(n=>{b(n),u.submitting=!1})})},(t=y.value)==null||t.openDialog()},M=e=>{o.editRow=e,o.formVisible=!0,F(e.id).then(t=>{var n,l,c,i;const a=(n=t.data)==null?void 0:n.data;(l=d.value)==null||l.setFormJson(),a.content?(c=d.value)==null||c.setFormJson(JSON.parse(a.content)):(i=d.value)==null||i.clearDesigner()}).catch(t=>{b(t)})},E=e=>{var t;o.editRow=e,(t=k.value)==null||t.openDialog(),F(e.id).then(a=>{var l,c,i;const n=(l=a.data)==null?void 0:l.data;n.content?(c=x.value)==null||c.setFormJson(JSON.parse(n.content)):(i=x.value)==null||i.clearDesigner()}).catch(a=>{b(a)})},g=async()=>{var l,c,i;r.loading=!0;const t={...((l=B.value)==null?void 0:l.queryParams)||{},page:r.pagination.page||1,size:r.pagination.limit||20},n=(i=(c=(await fe(t)).data)==null?void 0:c.data)==null?void 0:i.data;r.dataList=n,r.loading=!1},O=async e=>{F(e).then(t=>{var n;const a=(n=t.data)==null?void 0:n.data;r.dataList=[a]}).catch(t=>{b(t)}),r.loading=!1},$=()=>{var e;(e=d.value)==null||e.refreshDesigner(),o.formVisible=!1},j=()=>{D("确定提交？","提示信息").then(()=>{var e;o.submitting=!0,o.editRow.content=JSON.stringify((e=d.value)==null?void 0:e.getFormJson()),I(o.editRow).then(()=>{var t;(t=d.value)==null||t.clearDesigner(),o.submitting=!1,o.formVisible=!1,o.editRow=null,v("保存成功"),g(),o.formJson={}}).catch(t=>{b(t),o.submitting=!1})})};return K(async()=>{console.log();const e=U.currentRoute.value.query.formId;e?await O(e):await g()}),(e,t)=>{const a=ie,n=le,l=re,c=w("v-form-designer"),i=Y,P=Z,G=w("v-form-render");return H(),Q("div",de,[f(a,{ref_key:"refSearch",ref:B,config:s(q)},null,8,["config"]),f(n,{ref_key:"refTable",ref:T,class:"card-table",config:s(r)},null,8,["config"]),f(l,{ref_key:"refForm",ref:y,config:s(u)},null,8,["config"]),f(P,{modelValue:s(o).formVisible,"onUpdate:modelValue":t[1]||(t[1]=V=>s(o).formVisible=V),title:"表单设计",fullscreen:!0,"close-on-press-escape":!1,onClose:$},{footer:h(()=>[X("span",me,[f(i,{onClick:t[0]||(t[0]=V=>s(o).formVisible=!1)},{default:h(()=>t[2]||(t[2]=[L("关闭")])),_:1}),f(i,{loading:s(o).submitting,type:"primary",onClick:j},{default:h(()=>t[3]||(t[3]=[L(" 确定 ")])),_:1},8,["loading"])])]),default:h(()=>[f(c,{ref_key:"vfDesigner",ref:d,"designer-config":s(W),"form-json":s(o).formJson,"form-data":s(o).formData,"option-data":s(o).optionData},null,8,["designer-config","form-json","form-data","option-data"])]),_:1},8,["modelValue"]),f(l,{ref_key:"refFormRender",ref:k,config:s(N)},{default:h(()=>[f(G,{ref_key:"vFormRef",ref:x,"form-json":s(o).formJson,"form-data":s(o).formData,"option-data":s(o).optionData},null,8,["form-json","form-data","option-data"])]),_:1},8,["config"])])}}}),De=se(ue,[["__scopeId","data-v-924ebc83"]]);export{De as default};
