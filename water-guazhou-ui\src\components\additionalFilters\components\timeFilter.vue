<template>
  <el-form-item label="选择时间">
    <div style="display: flex">
      <el-radio-group
        v-model="option"
        style="height: 48px; display: flex; align-items: center"
        @change="optionChange"
      >
        <el-radio
          style="margin-bottom: 0"
          :label="1"
        >
          最近一个月
        </el-radio>
        <el-radio
          style="margin-bottom: 0"
          :label="3"
        >
          最近三个月
        </el-radio>
        <el-radio
          style="margin-bottom: 0"
          :label="6"
        >
          最近六个月
        </el-radio>
        <el-radio
          style="margin-bottom: 0"
          :label="12"
        >
          最近一年
        </el-radio>
        <el-radio
          style="margin-bottom: 0"
          :label="'other'"
        >
          自定义时间
        </el-radio>
      </el-radio-group>
      <el-date-picker
        v-if="datePickerVisible"
        v-model="daterange"
        style="margin-left: 16px; height: 48px"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        @change="daterangeChange"
      >
      </el-date-picker>
    </div>
  </el-form-item>
</template>

<script>
import moment from 'moment'

export default {
  // eslint-disable-next-line vue/require-prop-types
  props: ['config'],
  emits: ['timerangeChange'],
  data() {
    return {
      option: 1,
      daterange: [moment().subtract(1, 'months'), moment()],
      datePickerVisible: false
    }
  },
  methods: {
    optionChange(val) {
      if (val === 'other') {
        this.datePickerVisible = true
      } else {
        this.datePickerVisible = false
        this.daterange = [moment().subtract(val, 'months'), moment()]
        this.$emit('timerangeChange', this.daterange, this.config.key)
        // this.config.handleChange(this.daterange)
      }
    },
    daterangeChange(val) {
      // this.config.handleChange(val)
      this.$emit('timerangeChange', val, this.config.key)
    }
  }
}
</script>

<style lang="scss" scoped></style>
