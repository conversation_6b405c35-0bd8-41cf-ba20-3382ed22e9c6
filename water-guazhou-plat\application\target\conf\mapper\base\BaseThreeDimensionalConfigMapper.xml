<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.base.BaseThreeDimensionalConfigMapper">
    
    <resultMap type="org.thingsboard.server.dao.model.sql.base.BaseThreeDimensionalConfig" id="BaseThreeDimensionalConfigResult">
        <result property="id"    column="id"    />
        <result property="tileId"    column="tile_id"    />
        <result property="vectorIds"    column="vector_ids"    />
        <result property="schemeId"    column="scheme_id"    />
    </resultMap>

    <sql id="selectBaseThreeDimensionalConfigVo">
        select id, tile_id, vector_ids, scheme_id from base_three_dimensional_config
    </sql>

    <select id="selectBaseThreeDimensionalConfigList" parameterType="org.thingsboard.server.dao.model.sql.base.BaseThreeDimensionalConfig" resultMap="BaseThreeDimensionalConfigResult">
        <include refid="selectBaseThreeDimensionalConfigVo"/>
        <where>  
            <if test="tileId != null  and tileId != ''"> and tile_id = #{tileId}</if>
            <if test="vectorIds != null  and vectorIds != ''"> and vector_ids = #{vectorIds}</if>
            <if test="schemeId != null  and schemeId != ''"> and scheme_id = #{schemeId}</if>
        </where>
    </select>
    
    <select id="selectBaseThreeDimensionalConfigById" parameterType="String" resultMap="BaseThreeDimensionalConfigResult">
        <include refid="selectBaseThreeDimensionalConfigVo"/>
        where id = #{id}
    </select>

    <insert id="insertBaseThreeDimensionalConfig" parameterType="org.thingsboard.server.dao.model.sql.base.BaseThreeDimensionalConfig">
        insert into base_three_dimensional_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="tileId != null">tile_id,</if>
            <if test="vectorIds != null">vector_ids,</if>
            <if test="schemeId != null">scheme_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="tileId != null">#{tileId},</if>
            <if test="vectorIds != null">#{vectorIds},</if>
            <if test="schemeId != null">#{schemeId},</if>
         </trim>
    </insert>

    <update id="updateBaseThreeDimensionalConfig" parameterType="org.thingsboard.server.dao.model.sql.base.BaseThreeDimensionalConfig">
        update base_three_dimensional_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="tileId != null">tile_id = #{tileId},</if>
            <if test="vectorIds != null">vector_ids = #{vectorIds},</if>
            <if test="schemeId != null">scheme_id = #{schemeId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBaseThreeDimensionalConfigById" parameterType="String">
        delete from base_three_dimensional_config where id = #{id}
    </delete>

    <delete id="deleteBaseThreeDimensionalConfigByIds" parameterType="String">
        delete from base_three_dimensional_config where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>