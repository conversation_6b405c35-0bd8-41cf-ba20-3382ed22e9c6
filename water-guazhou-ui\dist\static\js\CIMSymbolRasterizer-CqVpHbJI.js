import{cG as N,cH as j,cI as d,cx as A,cJ as K,l as q,cK as B,cL as W}from"./MapView-DaoQedLH.js";import{U as Q}from"./pe-B8dP0-Ut.js";import{a$ as X}from"./index-r0dFAfgr.js";import{S as V}from"./Point-WxyopZva.js";import{a as Z,s as $,i as J,W as ee,j as te,p as ae,O as ie}from"./cimAnalyzer-CMgqZsaO.js";import{o as re}from"./CIMResourceManager-FPmRy-nM.js";import{c as se}from"./Rasterizer-CuAuGNQK.js";import"./widget-BcWKanF2.js";import"./fontUtils-BuXIMW9g.js";import"./BidiEngine-CsUYIMdL.js";import"./GeometryUtils-B7ExOJII.js";import"./enums-B5k73o5q.js";import"./alignmentUtils-CkNI7z7C.js";import"./definitions-826PWLuy.js";import"./number-CoJp78Rz.js";import"./Rect-CUzevAry.js";import"./callExpressionWithFeature-DgtD4TSq.js";import"./quantizationUtils-DtI9CsYu.js";import"./floatRGBA-PQQNbO39.js";import"./imageutils-KgbVacIV.js";import"./_commonjsHelpers-DCkdB7M8.js";import"./rasterizingUtils-BGZonnNf.js";var L;(function(p){p.Legend="legend",p.Preview="preview"})(L||(L={}));const Y=p=>p&&p.scaleFactor?p.scaleFactor:1,oe=96/72;class ke{constructor(t,e){this._spatialReference=t,this._avoidSDF=e,this._resourceCache=new Map,this._imageDataCanvas=null,this._pictureMarkerCache=new Map,this._textRasterizer=new Z,this._cimResourceManager=new re,this._rasterizer=new se(this._cimResourceManager)}get resourceManager(){return this._cimResourceManager}async rasterizeCIMSymbolAsync(t,e,s,i,a,r,h,o){if(!t)return null;const{data:c}=t;if(!c||c.type!=="CIMSymbolReference"||!c.symbol)return null;const{symbol:_}=c;r||(r=N(_));const v=await $.resolveSymbolOverrides(c,e,this._spatialReference,a,r,h,o);this._imageDataCanvas||(this._imageDataCanvas=document.createElement("canvas"));const g=this._imageDataCanvas,l=this._cimResourceManager,f=[];J.fetchResources(v,l,f),f.length>0&&await Promise.all(f);const{width:m,height:u}=s,C=ne(r,m,u,i),n=J.getEnvelope(v,C,l);if(!n)return null;const D=(window.devicePixelRatio||1)*oe;let z=1,k=0,x=0;switch(_.type){case"CIMPointSymbol":case"CIMTextSymbol":{let M=1;n.width>m&&(M=m/n.width);let P=1;n.height>u&&(P=u/n.height),i==="preview"&&(n.width<m&&(M=m/n.width),n.height<u&&(P=u/n.height)),z=Math.min(M,P),k=n.x+n.width/2,x=n.y+n.height/2}break;case"CIMLineSymbol":{let M=1;n.height>u&&(M=u/n.height),z=M,x=n.y+n.height/2;const P=n.x*z+m/2,y=(n.x+n.width)*z+m/2;if(P<0){const{paths:w}=C;w[0][0][0]-=P}if(y>m){const{paths:w}=C;w[0][2][0]-=y-m}}break;case"CIMPolygonSymbol":{k=n.x+n.width/2,x=n.y+n.height/2;const M=n.x*z+m/2,P=(n.x+n.width)*z+m/2,y=n.y*z+u/2,w=(n.y+n.height)*z+u/2,{rings:R}=C;M<0&&(R[0][0][0]-=M,R[0][3][0]-=M,R[0][4][0]-=M),y<0&&(R[0][0][1]+=y,R[0][1][1]+=y,R[0][4][1]+=y),P>m&&(R[0][1][0]-=P-m,R[0][2][0]-=P-m),w>u&&(R[0][2][1]+=w-u,R[0][3][1]+=w-u)}}g.width=m*D,g.height=u*D;const I=1;g.width+=2*I,g.height+=2*I;const S=g.getContext("2d"),b=ie.createIdentity();return b.translate(-k,-x),b.scale(z*D,-z*D),b.translate(m*D/2+I,u*D/2+I),S.clearRect(0,0,g.width,g.height),new ee(S,l,b,!0).drawSymbol(v,C),S.getImageData(0,0,g.width,g.height)}async analyzeCIMSymbol(t,e,s,i,a){const r=[],h=e?{geometryType:i,spatialReference:this._spatialReference,fields:e}:null;let o;await te(t.data,h,this._cimResourceManager,r,this._avoidSDF),V(a);for(const c of r)c.cim.type!=="CIMPictureMarker"&&c.cim.type!=="CIMPictureFill"&&c.cim.type!=="CIMPictureStroke"||(o||(o=[]),o.push(this._fetchPictureMarkerResource(c,a))),s&&c.type==="text"&&typeof c.text=="string"&&c.text.includes("[")&&(c.text=j(s,c.text,c.cim.textCase));return o&&await Promise.all(o),r}rasterizeCIMSymbol3D(t,e,s,i,a,r){const h=[];for(const o of t){i&&typeof i.scaleFactor=="function"&&(i.scaleFactor=i.scaleFactor(e,a,r));const c=this._getRasterizedResource(o,e,s,i,a,r);if(!c)continue;let _=0,v=c.anchorX||0,g=c.anchorY||0,l=!1,f=0,m=0;if(s==="esriGeometryPoint"){const u=Y(i);if(f=d(o.offsetX,e,a,r)*u||0,m=d(o.offsetY,e,a,r)*u||0,o.type==="marker")_=d(o.rotation,e,a,r)||0,l=!!o.rotateClockwise&&o.rotateClockwise;else if(o.type==="text"){if(_=d(o.angle,e,a,r)||0,o.horizontalAlignment!==void 0)switch(o.horizontalAlignment){case"left":v=-.5;break;case"right":v=.5;break;default:v=0}if(o.verticalAlignment!==void 0)switch(o.verticalAlignment){case"top":g=.5;break;case"bottom":g=-.5;break;case"baseline":g=-.25;break;default:g=0}}}c!=null&&h.push({angle:_,rotateClockWise:l,anchorX:v,anchorY:g,offsetX:f,offsetY:m,rasterizedResource:c})}return this.getSymbolImage(h)}getSymbolImage(t){const e=document.createElement("canvas"),s=X(e.getContext("2d"));let i=0,a=0,r=0,h=0;const o=[];for(let g=0;g<t.length;g++){const l=t[g],f=l.rasterizedResource;if(!f)continue;const m=f.size,u=l.offsetX,C=l.offsetY,n=l.anchorX,D=l.anchorY,z=l.rotateClockWise||!1;let k=l.angle,x=A(u)-m[0]*(.5+n),I=A(C)-m[1]*(.5+D),S=x+m[0],b=I+m[1];if(k){z&&(k=-k);const y=Math.sin(k*Math.PI/180),w=Math.cos(k*Math.PI/180),R=x*w-I*y,F=x*y+I*w,O=x*w-b*y,T=x*y+b*w,H=S*w-b*y,E=S*y+b*w,G=S*w-I*y,U=S*y+I*w;x=Math.min(R,O,H,G),I=Math.min(F,T,E,U),S=Math.max(R,O,H,G),b=Math.max(F,T,E,U)}i=x<i?x:i,a=I<a?I:a,r=S>r?S:r,h=b>h?b:h;const M=s.createImageData(f.size[0],f.size[1]);M.data.set(new Uint8ClampedArray(f.image.buffer));const P={offsetX:u,offsetY:C,rotateClockwise:z,angle:k,rasterizedImage:M,anchorX:n,anchorY:D};o.push(P)}e.width=r-i,e.height=h-a;const c=-i,_=h;for(let g=0;g<o.length;g++){const l=o[g],f=this._imageDataToCanvas(l.rasterizedImage),m=l.rasterizedImage.width,u=l.rasterizedImage.height,C=c-m*(.5+l.anchorX),n=_-u*(.5-l.anchorY);if(l.angle){const D=(360-l.angle)*Math.PI/180;s.save(),s.translate(A(l.offsetX),-A(l.offsetY)),s.translate(c,_),s.rotate(D),s.translate(-c,-_),s.drawImage(f,C,n),s.restore()}else s.drawImage(f,C+A(l.offsetX),n-A(l.offsetY))}const v=new K({x:c/e.width-.5,y:_/e.height-.5});return{imageData:e.width!==0&&e.height!==0?s.getImageData(0,0,e.width,e.height):s.createImageData(1,1),anchorPosition:v}}async _fetchPictureMarkerResource(t,e){const s=t.materialHash;if(!this._pictureMarkerCache.get(s)){const i=(await Q(t.cim.url,{responseType:"image",signal:e&&e.signal})).data;this._pictureMarkerCache.set(s,i)}}_imageDataToCanvas(t){this._imageDataCanvas||(this._imageDataCanvas=document.createElement("canvas"));const e=this._imageDataCanvas,s=X(e.getContext("2d"));return e.width=t.width,e.height=t.height,s.putImageData(t,0,0),e}_imageTo32Array(t,e,s,i){this._imageDataCanvas||(this._imageDataCanvas=document.createElement("canvas"));const a=this._imageDataCanvas,r=X(a.getContext("2d"));if(a.width=e,a.height=s,r.drawImage(t,0,0,e,s),i){r.save();const h=new q(i);r.fillStyle=h.toHex(),r.globalCompositeOperation="multiply",r.fillRect(0,0,e,s),r.globalCompositeOperation="destination-atop",r.drawImage(t,0,0,e,s),r.restore()}return new Uint32Array(r.getImageData(0,0,e,s).data.buffer)}_getRasterizedResource(t,e,s,i,a,r){let h,o,c;if(t.type==="text")return this._rasterizeTextResource(t,e,i,a,r);({analyzedCIM:h,hash:o}=ce(t,e,a,r));const g=Y(i);if(t.cim.type==="CIMPictureMarker"){const m=d(t.size,e,a,r)*g,{image:u,width:C,height:n}=X(this._getPictureResource(t,m,d(t.color,e,a,r)));return c={image:u,size:[C,n],sdf:!1,simplePattern:!1,anchorX:t.anchorPoint?t.anchorPoint.x:0,anchorY:t.anchorPoint?t.anchorPoint.y:0},c}B(h,g,{preserveOutlineWidth:!1});const l=h;o+=s,i&&(o+=JSON.stringify(i));const f=this._resourceCache;return f.has(o)?f.get(o):(c=this._rasterizer.rasterizeJSONResource({cim:l,type:t.type,url:t.url,mosaicHash:o,size:null,path:null},window.devicePixelRatio||1,this._avoidSDF),f.set(o,c),c)}_rasterizeTextResource(t,e,s,i,a){const r=Y(s),h=d(t.text,e,i,a);if(!h||h.length===0)return null;const o=d(t.fontName,e,i,a),c=d(t.style,e,i,a),_=d(t.weight,e,i,a),v=d(t.decoration,e,i,a),g=d(t.size,e,i,a)*r,l=d(t.horizontalAlignment,e,i,a),f=d(t.verticalAlignment,e,i,a),m=W(d(t.color,e,i,a)),u=W(d(t.outlineColor,e,i,a)),C={color:m,size:g,horizontalAlignment:l,verticalAlignment:f,font:{family:o,style:c,weight:_,decoration:v},halo:{size:d(t.outlineSize,e,i,a)||0,color:u,style:c},pixelRatio:1,premultiplyColors:!this._avoidSDF};return this._textRasterizer.rasterizeText(h,C)}_getPictureResource(t,e,s){const i=this._pictureMarkerCache.get(t.materialHash);if(!i)return null;const a=i.height/i.width,r=e?a>1?A(e):A(e)/a:i.width,h=e?a>1?A(e)*a:A(e):i.height;return{image:this._imageTo32Array(i,r,h,s),width:r,height:h}}}function ne(p,t,e,s){const a=-t/2+1,r=t/2-1,h=e/2-1,o=-e/2+1;switch(p){case"esriGeometryPoint":return{x:0,y:0};case"esriGeometryPolyline":return{paths:[[[a,0],[0,0],[r,0]]]};default:return s==="legend"?{rings:[[[a,h],[r,0],[r,o],[a,o],[a,h]]]}:{rings:[[[a,h],[r,h],[r,o],[a,o],[a,h]]]}}}function ce(p,t,e,s){let i,a;return typeof p.materialHash=="function"?(i=(0,p.materialHash)(t,e,s),a=ae(p.cim,p.materialOverrides)):(i=p.materialHash,a=p.cim),{analyzedCIM:a,hash:i}}export{ke as CIMSymbolRasterizer,L as GeometryStyle};
