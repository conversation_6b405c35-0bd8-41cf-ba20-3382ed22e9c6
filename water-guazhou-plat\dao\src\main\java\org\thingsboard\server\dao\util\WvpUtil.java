package org.thingsboard.server.dao.util;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * 大华摄像头控制
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2023-06-30
 */
@Slf4j
@Component
public class WvpUtil {
    @Value("${wvp.url}")
    private String host;


    public String getToken() throws IOException {
        String url = host + "/api/user/login?username=admin&password=21232f297a57a5a743894a0e4a801fc3";
        HttpGet post = new HttpGet(url);

        post.setHeader("Content-Type", "application/x-www-form-urlencoded");
        CloseableHttpResponse execute = null;
        try {
            CloseableHttpClient httpClient = HttpClients.createDefault();
            execute = httpClient.execute(post);
        } catch (IOException e) {
            e.printStackTrace();
        }
        HttpEntity entity = execute.getEntity();

        String s = EntityUtils.toString(entity);
        log.info(s);

        return execute.getFirstHeader("Set-Cookie").getValue();
    }

    public String gerUrl(String deviceCode) throws IOException {
        String cookie = this.getToken();


        String url = host + "/api/play/start/" + deviceCode + "/" + deviceCode;
        HttpGet post = new HttpGet(url);
        post.setHeader("Content-Type", "application/x-www-form-urlencoded");
        post.setHeader("Cookie", cookie);
        CloseableHttpResponse execute = null;
        try {

            CloseableHttpClient httpClient = HttpClients.createDefault();
            execute = httpClient.execute(post);
        } catch (IOException e) {
            e.printStackTrace();
        }
        HttpEntity entity = execute.getEntity();

        String s = EntityUtils.toString(entity);
        JSONObject jsonObject = JSONObject.parseObject(s);
        return jsonObject.getJSONObject("data").getString("hls");
    }
}
