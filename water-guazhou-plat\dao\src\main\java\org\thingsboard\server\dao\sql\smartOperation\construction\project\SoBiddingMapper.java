package org.thingsboard.server.dao.sql.smartOperation.construction.project;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoBidding;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoBiddingPageRequest;

@Mapper
public interface SoBiddingMapper extends BaseMapper<SoBidding> {
    IPage<SoBidding> findByPage(SoBiddingPageRequest request);

    boolean update(SoBidding entity);

    boolean save(SoBidding entity);

    boolean updateFully(SoBidding entity);

    String getIdByProjectCodeAndTenantId(@Param("projectCode") String projectCode, @Param("tenantId") String tenantId);


}
