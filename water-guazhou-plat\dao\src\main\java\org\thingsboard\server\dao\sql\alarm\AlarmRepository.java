/**
 * Copyright © 2016-2019 The Thingsboard Authors
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.sql.alarm;

import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.thingsboard.server.common.data.EntityType;
import org.thingsboard.server.dao.model.sql.AlarmEntity;
import org.thingsboard.server.dao.util.SqlDao;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 5/21/2017.
 */
@SqlDao
public interface AlarmRepository extends CrudRepository<AlarmEntity, String> {

    @Query("SELECT a FROM AlarmEntity a WHERE a.tenantId = :tenantId AND a.originatorId = :originatorId " +
            "AND a.originatorType = :entityType AND a.type = :alarmType ORDER BY a.type ASC, a.id DESC")
    List<AlarmEntity> findLatestByOriginatorAndType(@Param("tenantId") String tenantId,
                                                    @Param("originatorId") String originatorId,
                                                    @Param("entityType") EntityType entityType,
                                                    @Param("alarmType") String alarmType,
                                                    Pageable pageable);

    //@Query("SELECT a FROM AlarmEntity a WHERE a.alarmJsonId = :alarmJsonId  order by a.startTs ASC ")
    List<AlarmEntity> findByAlarmJsonId(@Param("alarm_Json_id") String alarmJsonId);

    @Query("SELECT a FROM AlarmEntity a WHERE a.tenantId = :tenantId AND a.startTs > :start_ts and a.startTs<:end_ts order by a.startTs DESC ")
    List<AlarmEntity> findByTenantIdAndStartTsBetween(@Param("tenantId") String tenantId,
                                                      @Param("start_ts") long start,
                                                      @Param("end_ts") long end);

    @Query("SELECT new AlarmEntity(a, d.name) FROM AlarmEntity a, DeviceEntity d  " +
            "WHERE a.originatorId = d.id AND a.originatorId = :originator_id AND a.startTs > :start_ts and a.startTs<:end_ts order by a.startTs DESC ")
    List<AlarmEntity> findByOriginatorIdAndStartTsAndEndTs(@Param("originator_id") String originatorId,
                                                           @Param("start_ts") long start,
                                                           @Param("end_ts") long end);

    List<AlarmEntity> findByOriginatorIdAndType(@Param("originator_id") String originatorId,
                                                @Param("type") String alarmType);

    List<AlarmEntity> findByOriginatorIdAndLevel(@Param("originator_id") String originatorId,
                                                 @Param("level") String level);

    void deleteAllByOriginatorId(@Param("originator_id") String originatorId);

    @Query("SELECT new AlarmEntity(a, d.name) " +
            "FROM AlarmEntity a, DeviceEntity d,ProjectRelationEntity pr " +
            "WHERE a.originatorId = d.id AND d.id = pr.entityId AND pr.projectId = ?1 AND a.startTs > ?2 and a.startTs < ?3 order by a.startTs DESC ")
    List<AlarmEntity> findByProjectIddAndStartTsBetween(String projectId, long start, long end);

    @Query("SELECT a " +
            "FROM AlarmEntity a, DeviceEntity d,ProjectRelationEntity pr " +
            "WHERE a.originatorId = d.id AND d.id = pr.entityId AND d.id IS NOT NULL AND pr.projectId IN ?1 AND a.startTs > ?2 and a.startTs < ?3 order by a.startTs DESC ")
    List<AlarmEntity> findByProjectIdInAndStartTsBetween(List<String> projectId, long start, long end);


    @Query("select count(a.id) from AlarmEntity a where a.status ='CONFIRM_UNACK' or a.status ='RESTORE_ACK'")
    long countNoClearAlarm();

    @Query("select count(a.id) from AlarmEntity a where (a.status ='CONFIRM_UNACK' or a.status ='RESTORE_ACK') AND a.tenantId =:tenantId")
    long countNoClearAlarmByTenantId(@Param("tenantId") String tenantId);


    @Query("select count(a.id) from AlarmEntity a where (a.status ='CONFIRM_UNACK' or a.status ='RESTORE_ACK') AND a.originatorId in (SELECT d.id FROM DeviceEntity d, ProjectRelationEntity pr WHERE d.id = pr.entityId AND pr.projectId = ?1)")
    long countNoClearAlarmByProjectId(@Param("projectId") String projectId);


    long countByOriginatorId(@Param("originatorId") String originatorId);

    @Query("select count(a.id) from AlarmEntity a where (a.status ='CONFIRM_UNACK' or a.status ='CONFIRM_UNACK'or a.status ='RESTORE_ACK') AND a.originatorId =:originatorId")
    long countNoClearAlarmBydeviceId(@Param("originatorId") String originatorId);

    @Query("select count(a.id) from AlarmEntity a where (a.status ='CLEARED_ACK' or a.status ='CLEAR_FORCED') AND a.originatorId =:originatorId")
    long countClearAlarmBydeviceId(@Param("originatorId") String originatorId);

    @Query("select count(a.id) from AlarmEntity a")
    long countAllAlarm();

    @Query("select count(a.id) from AlarmEntity a where a.tenantId =:tenantId")
    long countAllAlarmByTenantId(@Param("tenantId") String tenantId);

    @Query("select count(a.id) from AlarmEntity a where a.tenantId =?1 and a.startTs > ?2 and a.startTs < ?3")
    long countAllAlarmByTenantIdAndTime(@Param("tenantId") String tenantId, @Param("startTime") long startTime, @Param("endTime") long endTime);


    @Query("select count(a.id) from AlarmEntity a where a.originatorId in (SELECT d.id FROM DeviceEntity d, ProjectRelationEntity pr WHERE d.id = pr.entityId AND pr.projectId = ?1)")
    long countAllAlarmByProjectId(@Param("projectId") String projectId);

    @Query("select count(a.id) from AlarmEntity a where a.startTs > ?2 and a.startTs < ?3 and a.originatorId in (SELECT d.id FROM DeviceEntity d, ProjectRelationEntity pr WHERE d.id = pr.entityId AND pr.projectId = ?1)")
    long countAllAlarmByProjectIdAndTime(@Param("projectId") String projectId, @Param("startTime") long startTime, @Param("endTime") long endTime);

    @Query("SELECT new AlarmEntity(a, d.name) FROM AlarmEntity a, DeviceEntity d  " +
            "WHERE a.originatorId = d.id AND a.originatorId IN ?1 AND a.startTs > ?2 and a.startTs < ?3 order by a.startTs DESC ")
    List<AlarmEntity> findByOriginatorIdInAndStartTsAndEndTs(List<String> deviceIdList, long start, long end);
}
