import MapView from '@arcgis/core/views/MapView.js'
import Map from '@arcgis/core/Map.js'
import Graphic from '@arcgis/core/Graphic.js'
import SimpleFillSymbol from '@arcgis/core/symbols/SimpleFillSymbol.js'
import { useLayer } from '..'

export const useOverViewMap = () => {
  let extentWatcher: __esri.WatchHandle | undefined
  let stationaryWatcher: __esri.WatchHandle | undefined
  let OverViewMap: __esri.MapView | undefined
  const init = (view: __esri.MapView, overviewDiv?: string) => {
    // Create another Map, to be used in the overview "view"
    const { createTdtLayer } = useLayer()
    const tiledLayer = createTdtLayer({
      type: window.SITE_CONFIG.GIS_CONFIG.gisDefaultBaseMap || 'vec_w'
    })
    const tiledLayer_Poi = createTdtLayer({
      type: window.SITE_CONFIG.GIS_CONFIG.gisDefaultPoi || 'cva_w'
    })
    const overviewMap = new Map({
      basemap: {
        baseLayers: [tiledLayer, tiledLayer_Poi],
        title: '鹰眼图'
      }
    })

    // Create the MapView for overview map
    OverViewMap = new MapView({
      container: overviewDiv,
      map: overviewMap,
      constraints: {
        rotationEnabled: false
      }
    }) as __esri.MapView

    // Remove the default widgets
    OverViewMap.ui.components = []

    OverViewMap.when(() => {
      view.when(() => {
        resetExtentGraphic(view)
        extentToGraphic(view)
        bindExtendWater(view)
      })
    })
    return OverViewMap
  }
  const resetExtentGraphic = (view: __esri.MapView) => {
    const extentGraphic = new Graphic({
      geometry: view?.extent,
      symbol: new SimpleFillSymbol({
        color: [0, 0, 0, 0.5],
        outline: undefined
      })
    })
    OverViewMap?.graphics.removeAll()
    OverViewMap?.graphics.add(extentGraphic)
  }
  const extentToGraphic = (view: __esri.MapView) => {
    OverViewMap?.goTo({
      center: view.center,
      scale:
        view.scale
        * 4
        * Math.max(
          view.width / OverViewMap.width,
          view.height / OverViewMap.height
        )
    })
  }
  const bindExtendWater = (view: __esri.MapView | undefined) => {
    if (!view) return
    extentWatcher = view.watch('extent', () => {
      resetExtentGraphic(view)
    })
    stationaryWatcher = view.watch('stationary', () => {
      extentToGraphic(view)
    })
  }
  const destroy = () => {
    extentWatcher?.remove()
    stationaryWatcher?.remove()
    OverViewMap?.destroy()
  }
  onBeforeUnmount(() => {
    destroy()
  })
  return {
    destroy,
    init
  }
}
export default useOverViewMap
