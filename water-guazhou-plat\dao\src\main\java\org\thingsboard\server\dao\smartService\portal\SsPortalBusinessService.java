package org.thingsboard.server.dao.smartService.portal;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.smartService.portal.SsPortalBusiness;
import org.thingsboard.server.dao.util.imodel.query.smartService.portal.SsPortalActiveRequest;
import org.thingsboard.server.dao.util.imodel.query.smartService.portal.SsPortalBusinessPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartService.portal.SsPortalBusinessSaveRequest;

public interface SsPortalBusinessService {
    SsPortalBusiness findById(String id);

    IPage<SsPortalBusiness> findAllConditional(SsPortalBusinessPageRequest request);

    SsPortalBusiness save(SsPortalBusinessSaveRequest entity);

    boolean update(SsPortalBusiness entity);

    boolean delete(String id);

    boolean active(SsPortalActiveRequest request);

}
