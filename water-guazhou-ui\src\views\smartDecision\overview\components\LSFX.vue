<template>
  <div class="lsfx-wrapper overlay-y">
    <template v-if="state.list.length">
      <VueScroll :data="state.list" :class-option="state.classOption">
        <ul ref="refTable">
          <li v-for="(item, i) in state.list" :key="i" class="lsfx-item">
            <div class="lsfx-item__inner">
              <div class="lsfx-item__icon" :class="'sort' + i">
                <span>{{ item.sort }}</span>
              </div>
              <div class="progress">
                <div class="text">
                  <span>{{ item.name }}</span>
                  <div class="percent">
                    <span class="value">{{ item.percent }}</span>
                    <span class="unit">%</span>
                  </div>
                </div>
                <div class="progress-bar">
                  <div
                    v-for="(bar, j) in 50"
                    :key="j"
                    class="progress-bar__item"
                    :class="[
                      item.percent < j * 2
                        ? ''
                        : j < 17
                          ? 'lightblue'
                          : j < 33
                            ? 'yellow'
                            : 'red'
                    ]"
                  ></div>
                </div>
              </div>
            </div>
          </li>
        </ul>
      </VueScroll>
    </template>
    <div v-else class="empty">暂无信息</div>
  </div>
</template>
<script lang="ts" setup>
import { GetPartitionRefLeakSort } from '@/api/mapservice/dma';

const props = defineProps<{
  classOption?: any;
  isDev?: boolean;
  config?: any;
}>();
const state = reactive<{
  list: { sort: number; name: string; percent: number }[];
  classOption: any;
}>({
  list: [
    { sort: 1, name: '百花村&二儿山', percent: 99.7 },
    { sort: 2, name: '隆中山', percent: 37.4 },
    { sort: 3, name: '钢铁街道', percent: 29.1 },
    { sort: 4, name: '高新片区', percent: 17.9 },
    { sort: 5, name: '百花片区', percent: 9.4 }
  ],
  classOption: props.classOption ?? {
    step: 0.2,
    limitMoveNum: 6
  }
});
const refreshData = async () => {
  if (props.isDev) return;
  GetPartitionRefLeakSort()
    .then((res) => {
      const data = res.data?.data || {};
      state.list = data.x.map((item, i) => {
        return {
          sort: i + 1,
          name: item,
          percent: data.y?.[i] || 0
        };
      });
    })
    .catch(() => {
      //
    });
};

onBeforeMount(() => {
  if (props?.config?.source) {
    const key = props.config.config.defaultValue || {};
    key?.List &&
      (state.list = key?.List.sort((a, b) => b.percent - a.percent).map(
        (item, i) => {
          return { sort: i + 1, name: item.name, percent: item.percent };
        }
      ));
  } else {
    refreshData();
  }
});
</script>
<style lang="scss" scoped>
ul,
li {
  padding: 0;
  margin: 0;
}
ul {
  list-style: none;
  padding: 0 12px;
}
.lsfx-wrapper {
  width: 100%;
  height: 100%;
  padding: 16px 20px;
}
.lsfx-item {
  width: 100%;
  height: 36px;
  margin-bottom: 10px;
  background-color: rgba(49, 174, 244, 0.1);
  transform: skewX(-30deg);
}
.lsfx-item__inner {
  transform: skewX(30deg);
  padding: 0 15px;
  display: flex;
  flex-wrap: nowrap;
  width: 100%;
}
.lsfx-item__icon {
  width: 37px;
  min-width: 37px;
  height: 41px;
  background-color: red;
  border-radius: 4px;
  text-align: center;
  padding: 6px 0;
  margin-right: 20px;
  transform: perspective(37px) rotateX(-30deg) translateY(3px);
  &.sort0 {
    background: linear-gradient(
      45deg,
      rgba(255, 134, 153, 1),
      rgba(221, 50, 77, 1)
    );
  }
  &.sort1 {
    background: linear-gradient(
      45deg,
      rgba(255, 207, 134, 1),
      rgba(221, 111, 50, 1)
    );
  }
  &.sort2 {
    background: linear-gradient(
      45deg,
      rgba(253, 255, 134, 1),
      rgba(221, 152, 50, 1)
    );
  }
  &.sort3 {
    background: linear-gradient(
      45deg,
      rgba(67, 255, 86, 1),
      rgba(32, 194, 68, 1)
    );
  }
  &.sort4 {
    background: linear-gradient(
      45deg,
      rgba(134, 233, 255, 1),
      rgba(50, 118, 221, 1)
    );
  }
}
.progress {
  padding: 4px;
  font-size: 12px;
  width: calc(100% - 40px);
  .text {
    color: #b8d2ff;
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    .percent {
      .value {
        font-family: font-lcd;
        font-size: 16px;
        color: #f68574;
      }
      .unit {
        margin-left: 4px;
      }
    }
  }
  .progress-bar {
    height: 10px;
    display: flex;
    justify-content: space-between;
    .progress-bar__item {
      background-color: transparent;
      border-radius: 2px;
      width: 4px;
      height: 100%;
      &.lightblue {
        background: linear-gradient(
          rgba(131, 242, 249, 1),
          rgba(114, 184, 240, 1)
        );
      }
      &.yellow {
        background: linear-gradient(
          rgba(247, 243, 128, 1),
          rgba(243, 180, 75, 1)
        );
      }
      &.red {
        background: linear-gradient(
          rgba(247, 149, 128, 1),
          rgba(243, 75, 75, 1)
        );
      }
    }
  }
}
.empty {
  height: 100%;
  color: #77c3ff;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
