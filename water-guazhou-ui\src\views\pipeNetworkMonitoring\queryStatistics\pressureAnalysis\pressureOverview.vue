<template>
  <div class="wrapper">
    <CardSearch
      ref="refSearch"
      :config="SearchConfig"
    ></CardSearch>
    <SLCard
      class="card"
      title=" "
    >
      <template #title>
        <div class="card-title">
          <span class="title">{{ getReportTitle() }}</span>
          <span class="date">报表时间：{{ getFormattedTime() }}</span>
        </div>
      </template>
      <div class="card-content">
        <!-- 统计概览 -->
        <div class="overview-section">
          <el-row :gutter="20">
            <el-col :span="6">
              <div class="stat-card abnormal">
                <div class="stat-number">{{ overviewData.currentStatus }}</div>
                <div class="stat-label">当前状态</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-card max-pressure">
                <div class="stat-number">{{ overviewData.maxPressure }}MPa</div>
                <div class="stat-label">最大压力</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-card min-pressure">
                <div class="stat-number">{{ overviewData.minPressure }}MPa</div>
                <div class="stat-label">最小压力</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-card alarm-count">
                <div class="stat-number">{{ overviewData.alarmCount }}次</div>
                <div class="stat-label">报警次数</div>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 图表区域 -->
        <div class="chart-section">
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="chart-container">
                <h3>管网压力{{ getChartTitle() }} - 管压趋势</h3>
                <div class="chart-date">日期 {{ getFormattedTime() }}</div>
                <div ref="pressureTrendChart" class="chart"></div>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="chart-container">
                <h3>报警次数</h3>
                <div ref="alarmChart" class="chart"></div>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 详细数据表格 -->
        <div class="table-section">
          <FormTable :config="DetailTableConfig"></FormTable>
        </div>

        <!-- 汇总数据表格 -->
        <div class="table-section">
          <FormTable :config="SummaryTableConfig"></FormTable>
        </div>
      </div>
    </SLCard>
  </div>
</template>

<script setup>
import { onMounted, reactive, ref, nextTick, computed, onUnmounted } from 'vue'
import {
  Delete,
  // Plus,
  Promotion,
  Refresh,
  Search as SearchIcon
} from '@element-plus/icons-vue'
import moment from 'moment'
import * as echarts from 'echarts'
import { useBusinessStore } from '@/store'
import SLCard from '@/components/SLCard/index.vue'
import FormTable from '@/components/Form/FormTable.vue'
import { GetStationList, GetStationAttrGroupNames } from '@/api/shuiwureports/zhandian'

const state = reactive({
  stationList: [],
  alarmStatusList: [
    { label: '全部', value: '' },
    { label: '正常', value: 'normal' },
    { label: '异常', value: 'abnormal' },
    { label: '报警', value: 'alarm' }
  ],
  selectedStations: [],
  alarmStatus: '',
  time: moment().format('YYYY-MM-DD'),
  reportType: 'day', // day, custom, month
  timeRange: [
    moment().subtract(7, 'days').format('YYYY-MM-DD'),
    moment().format('YYYY-MM-DD')
  ]
})

const overviewData = reactive({
  currentStatus: '异常',
  maxPressure: '0.30',
  minPressure: '0.18',
  alarmCount: 8
})

const refSearch = ref()
const pressureTrendChart = ref()
const alarmChart = ref()

let pressureTrendChartInstance = null
let alarmChartInstance = null

const SearchConfig = reactive({
  defaultParams: {
    stationIds: [],
    alarmStatus: '',
    time: moment().format('YYYY-MM-DD'),
    reportType: 'day',
    timeRange: [
      moment().subtract(7, 'days').format('YYYY-MM-DD'),
      moment().format('YYYY-MM-DD')
    ]
  },
  filters: [
    {
      type: 'select',
      label: '选择点位:',
      field: 'stationIds',
      multiple: true,
      options: computed(() => state.stationList),
      placeholder: '全部',
      onChange: () => {
        refreshData()
      }
    },
    {
      type: 'select',
      label: '报警状态:',
      field: 'alarmStatus',
      options: state.alarmStatusList,
      placeholder: '全部',
      onChange: () => {
        refreshData()
      }
    },
    {
      type: 'select',
      label: '报表类型:',
      field: 'reportType',
      options: [
        { label: '日报', value: 'day' },
        { label: '月报', value: 'month' },
        { label: '自定义', value: 'custom' }
      ],
      onChange: val => {
        state.reportType = val
        updateTimeFilter()
        refreshData()
      }
    },
    {
      type: 'date',
      label: '日期',
      field: 'time',
      hidden: computed(() => state.reportType !== 'day'),
      onChange: val => {
        state.time = val
      }
    },
    {
      type: 'daterange',
      label: '时间范围',
      field: 'timeRange',
      hidden: computed(() => state.reportType !== 'custom'),
      onChange: val => {
        state.timeRange = val
      }
    },
    {
      type: 'month',
      label: '月份',
      field: 'time',
      hidden: computed(() => state.reportType !== 'month'),
      onChange: val => {
        state.time = val
      }
    }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          icon: 'iconfont icon-chaxun',
          click: () => refreshData()
        },
        {
          perm: true,
          text: '重置',
          svgIcon: shallowRef(Refresh),
          click: () => resetForm()
        },
        {
          perm: true,
          iconifyIcon: 'ep:download',
          type: 'warning',
          text: '导出',
          click: () => handleExport()
        }
      ]
    }
  ]
})

// 详细数据表格配置
const DetailTableConfig = reactive({
  height: 300,
  highlightCurrentRow: false,
  tableTitle: '管网压力详细数据',
  showOverflowTooltip: true,
  stripe: true,
  columns: [
    { label: '时间', prop: 'time', fixed: 'left' },
    { label: '2025-06-25 (MPa)', prop: 'day_25',  align: 'center' },
    { label: '2025-06-24 (MPa)', prop: 'day_24',  align: 'center' },
    { label: '2025-06-23 (MPa)', prop: 'day_23',  align: 'center' },
    { label: '2025-06-22 (MPa)', prop: 'day_22',  align: 'center' },
    { label: '2025-06-21 (MPa)', prop: 'day_21',  align: 'center' },
    { label: '2025-06-20 (MPa)', prop: 'day_20',  align: 'center' },
    { label: '2025-06-19 (MPa)', prop: 'day_19',  align: 'center' },
    { label: '差值率 (%)', prop: 'variance', align: 'center' },
    { label: '变化系数', prop: 'coefficient', align: 'center' }
  ],
  dataList: [],
  pagination: {
    pageSize: 10,
    pageSizes: [10, 20, 50],
    layout: 'total, sizes, prev, pager, next, jumper'
  }
})

// 汇总数据表格配置
const SummaryTableConfig = reactive({
  height: 180,
  highlightCurrentRow: false,
  tableTitle: '管网压力汇总统计',
  showOverflowTooltip: true,
  stripe: true,
  columns: [
    { label: '日期', prop: 'date',  align: 'center' },
    { label: '最大值', prop: 'maxValue', align: 'center' },
    { label: '最大值发生时间', prop: 'maxTime', align: 'center' },
    { label: '最小值', prop: 'minValue', align: 'center' },
    { label: '最小值发生时间', prop: 'minTime', align: 'center' },
    { label: '平均值', prop: 'avgValue', align: 'center' }
  ],
  dataList: [],
  pagination: {
    hide: true
  }
})

// 获取报表标题
const getReportTitle = () => {
  const typeMap = {
    day: '管网压力日简报',
    custom: '管网压力自定义报表',
    month: '管网压力月报'
  }
  return typeMap[state.reportType] || '管网压力简报'
}

// 获取图表标题
const getChartTitle = () => {
  const typeMap = {
    day: '日报',
    month: '月报',
    custom: '自定义报表'
  }
  return typeMap[state.reportType] || '日报'
}

// 格式化时间显示
const getFormattedTime = () => {
  if (state.reportType === 'custom' && state.timeRange) {
    return `${moment(state.timeRange[0]).format('YYYY-MM-DD')} 至 ${moment(state.timeRange[1]).format('YYYY-MM-DD')}`
  }
  
  const formatMap = {
    day: 'YYYY-MM-DD',
    month: 'YYYY-MM'
  }
  return moment(state.time).format(formatMap[state.reportType] || 'YYYY-MM-DD')
}

// 更新时间筛选器
const updateTimeFilter = () => {
  const now = moment()
  switch (state.reportType) {
    case 'day':
      state.time = now.format('YYYY-MM-DD')
      break
    case 'custom':
      state.timeRange = [
        moment().subtract(7, 'days').format('YYYY-MM-DD'),
        moment().format('YYYY-MM-DD')
      ]
      break
    case 'month':
      state.time = now.format('YYYY-MM')
      break
  }
  
  SearchConfig.defaultParams.reportType = state.reportType
  
  if (state.reportType === 'custom') {
    SearchConfig.defaultParams.timeRange = state.timeRange
    delete SearchConfig.defaultParams.time
  } else {
    SearchConfig.defaultParams.time = state.time
    delete SearchConfig.defaultParams.timeRange
  }
  
  refSearch.value?.resetForm()
}

// 初始化图表
const initCharts = () => {
  nextTick(() => {
    if (pressureTrendChart.value && !pressureTrendChartInstance) {
      pressureTrendChartInstance = echarts.init(pressureTrendChart.value)
      updatePressureTrendChart()
    }
    
    if (alarmChart.value && !alarmChartInstance) {
      alarmChartInstance = echarts.init(alarmChart.value)
      updateAlarmChart()
    }
  })
}

// 更新压力趋势图表
const updatePressureTrendChart = () => {
  if (!pressureTrendChartInstance) return
  
  const timeLabels = generateTimeLabels()
  const maxPressureData = generatePressureData(timeLabels.length, 0.25, 0.4)
  const minPressureData = generatePressureData(timeLabels.length, 0.1, 0.3)
  
  const option = {
    tooltip: {
      trigger: 'axis',
      formatter: (params) => {
        let result = `${params[0].name}<br/>`
        params.forEach(param => {
          result += `${param.seriesName}: ${param.value}MPa<br/>`
        })
        return result
      }
    },
    legend: {
      data: ['最高管压/MPa', '最低管压/MPa', '告警阈值'],
      bottom: 10
    },
    xAxis: {
      type: 'category',
      data: timeLabels,
      axisLabel: {
        rotate: 45
      }
    },
    yAxis: {
      type: 'value',
      name: 'MPa',
      min: 0,
      max: 0.5,
      axisLabel: {
        formatter: '{value}'
      }
    },
    series: [
      {
        name: '最高管压/MPa',
        type: 'line',
        data: maxPressureData,
        smooth: true,
        lineStyle: {
          color: '#1890ff'
        },
        areaStyle: {
          color: 'rgba(24, 144, 255, 0.1)'
        }
      },
      {
        name: '最低管压/MPa',
        type: 'line',
        data: minPressureData,
        smooth: true,
        lineStyle: {
          color: '#52c41a'
        },
        areaStyle: {
          color: 'rgba(82, 196, 26, 0.1)'
        }
      },
      {
        name: '告警阈值',
        type: 'line',
        data: Array(timeLabels.length).fill(0.3),
        lineStyle: {
          color: '#ff4d4f',
          type: 'dashed'
        },
        symbol: 'none'
      }
    ]
  }
  
  pressureTrendChartInstance.setOption(option)
}

// 更新报警次数图表
const updateAlarmChart = () => {
  if (!alarmChartInstance) return
  
  const dates = ['6月19日', '6月20日', '6月21日', '6月22日', '6月23日', '6月24日', '6月25日']
  const alarmCounts = [17, 26, 11, 30, 1, 15, 11]
  
  const option = {
    tooltip: {
      trigger: 'axis',
      formatter: (params) => {
        return `${params[0].name}<br/>报警次数/次: ${params[0].value}`
      }
    },
    xAxis: {
      type: 'category',
      data: dates,
      axisLabel: {
        rotate: 45
      }
    },
    yAxis: {
      type: 'value',
      name: '次',
      min: 0,
      max: 35
    },
    series: [
      {
        name: '报警次数/次',
        type: 'bar',
        data: alarmCounts,
        itemStyle: {
          color: '#faad14'
        },
        barWidth: '60%'
      }
    ]
  }
  
  alarmChartInstance.setOption(option)
}

// 生成时间标签
const generateTimeLabels = () => {
  const labels = []
  let count = 7 // 默认7天
  
  if (state.reportType === 'custom' && state.timeRange) {
    // 计算自定义时间范围的天数
    const startDate = moment(state.timeRange[0])
    const endDate = moment(state.timeRange[1])
    count = endDate.diff(startDate, 'days') + 1
  } else if (state.reportType === 'month') {
    count = 12
  }
  
  for (let i = count - 1; i >= 0; i--) {
    let date
    switch (state.reportType) {
      case 'day':
        date = moment().subtract(i, 'days').format('6月DD日')
        break
      case 'custom':
        if (state.timeRange) {
          date = moment(state.timeRange[1]).subtract(i, 'days').format('6月DD日')
        } else {
          date = moment().subtract(i, 'days').format('6月DD日')
        }
        break
      case 'month':
        date = moment().subtract(i, 'months').format('MM月')
        break
      default:
        date = moment().subtract(i, 'days').format('6月DD日')
    }
    labels.push(date)
  }
  
  return labels
}

// 生成压力数据
const generatePressureData = (count, min, max) => {
  const data = []
  for (let i = 0; i < count; i++) {
    data.push((Math.random() * (max - min) + min).toFixed(2))
  }
  return data
}

// 生成详细数据
const generateDetailData = () => {
  const times = []
  const data = []
  
  // 生成时间点（每小时一个，一天24个数据点）
  for (let hour = 0; hour < 24; hour++) {
    const timeStr = `${hour.toString().padStart(2, '0')}:00`
    times.push(timeStr)
  }
  
  // 生成数据
  times.forEach((time, index) => {
    const row = { time }
    
    // 生成各日期的压力数据
    row.day_25 = (Math.random() * 0.25 + 0.15).toFixed(1)
    row.day_24 = (Math.random() * 0.25 + 0.15).toFixed(1)
    row.day_23 = (Math.random() * 0.25 + 0.15).toFixed(1)
    row.day_22 = (Math.random() * 0.25 + 0.15).toFixed(1)
    row.day_21 = (Math.random() * 0.25 + 0.15).toFixed(1)
    row.day_20 = (Math.random() * 0.25 + 0.15).toFixed(1)
    row.day_19 = (Math.random() * 0.25 + 0.15).toFixed(1)
    
    // 计算差值率和变化系数
    row.variance = (Math.random() * 10 + 1).toFixed(1) + '%'
    row.coefficient = (Math.random() * 0.5 + 0.1).toFixed(2)
    
    // 添加特殊样式的行 - 模拟异常数据
    if (time === '01:00') {
      row.day_24 = '0.1'
      row.day_25 = '0.4'
      row._rowClassName = 'danger-row'
    }
    
    // 添加更多异常数据点
    if (index === 6 || index === 18) {
      const randomDay = Math.random() > 0.5 ? 'day_23' : 'day_22'
      row[randomDay] = Math.random() > 0.5 ? '0.1' : '0.4'
      row._rowClassName = Math.random() > 0.5 ? 'warning-row' : 'danger-row'
    }
    
    data.push(row)
  })
  
  return data
}

// 生成汇总数据
const generateSummaryData = () => {
  const summaryData = []
  const dates = ['2025-06-25', '2025-06-24', '2025-06-23', '2025-06-22', '2025-06-21', '2025-06-20', '2025-06-19']
  
  dates.forEach(date => {
    // 生成模拟的汇总数据
    const maxValue = (Math.random() * 0.3 + 0.25).toFixed(1)
    const minValue = (Math.random() * 0.15 + 0.1).toFixed(1)
    const avgValue = ((parseFloat(maxValue) + parseFloat(minValue)) / 2).toFixed(2)
    
    // 随机生成时间
    const maxHour = Math.floor(Math.random() * 24).toString().padStart(2, '0')
    const maxMin = Math.floor(Math.random() * 60).toString().padStart(2, '0')
    const minHour = Math.floor(Math.random() * 24).toString().padStart(2, '0')
    const minMin = Math.floor(Math.random() * 60).toString().padStart(2, '0')
    
    summaryData.push({
      date,
      maxValue,
      maxTime: `${maxHour}:${maxMin}`,
      minValue,
      minTime: `${minHour}:${minMin}`,
      avgValue
    })
  })
  
  return summaryData
}

// 刷新数据
const refreshData = async () => {
  try {
    const queryParams = refSearch.value?.queryParams
    let params = {
      stationType: '压力监测站',
      queryType: state.reportType,
      ...queryParams
    }
    
    // 如果是自定义时间范围，特殊处理时间参数
    if (state.reportType === 'custom' && state.timeRange) {
      params.startTime = state.timeRange[0]
      params.endTime = state.timeRange[1]
    }
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 更新概览数据
    overviewData.currentStatus = Math.random() > 0.5 ? '正常' : '异常'
    overviewData.maxPressure = (Math.random() * 0.2 + 0.25).toFixed(2)
    overviewData.minPressure = (Math.random() * 0.15 + 0.1).toFixed(2)
    overviewData.alarmCount = Math.floor(Math.random() * 10 + 1)
    
    // 更新表格数据
    DetailTableConfig.dataList = generateDetailData()
    SummaryTableConfig.dataList = generateSummaryData()
    
    // 更新图表
    updatePressureTrendChart()
    updateAlarmChart()
    
  } catch (error) {
    console.error('获取数据失败:', error)
  }
}

// 重置表单
const resetForm = () => {
  refSearch.value?.resetForm()
  refreshData()
}

// 导出功能
const handleExport = () => {
  console.log('导出压力简报')
  // 这里实现导出逻辑
}

onMounted(async () => {
  // 获取监测站列表
  try {
    const res = await GetStationList({
      page: 1,
      size: 999,
      type: '压力监测站',
      projectId: useBusinessStore().selectedProject?.value
    })
    
    const data = res.data.data
    state.stationList = data.map(d => ({
      label: d.name,
      value: d.id
    }))
  } catch (error) {
    console.error('获取监测站列表失败:', error)
  }
  
  // 初始化图表
  initCharts()
  
  // 初始化数据
  refreshData()
  
  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    pressureTrendChartInstance?.resize()
    alarmChartInstance?.resize()
  })
})

onUnmounted(() => {
  pressureTrendChartInstance?.dispose()
  alarmChartInstance?.dispose()
  window.removeEventListener('resize', () => {})
})
</script>

<style lang="scss" scoped>
.card {
  height: calc(100% - 100px);

  .card-title {
    display: flex;
    justify-content: space-between;
    width: 100%;

    .title {
      font-size: 18px;
      font-weight: bold;
    }

    .date {
      font-size: 12px;
      color: #666;
    }
  }
}

.card-content {
  height: 100%;
  overflow-y: auto;
  overflow-y: overlay;
}

.overview-section {
  margin-bottom: 20px;
  
  .stat-card {
    color: white;
    padding: 20px;
    border-radius: 8px;
    text-align: center;
    
    &.abnormal {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    
    &.max-pressure {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    
    &.min-pressure {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    
    &.alarm-count {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    
    .stat-number {
      font-size: 28px;
      font-weight: bold;
      margin-bottom: 8px;
    }
    
    .stat-label {
      font-size: 14px;
      opacity: 0.9;
    }
  }
}

.chart-section {
  margin-bottom: 20px;
  
  .chart-container {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    
    h3 {
      margin: 0 0 8px 0;
      font-size: 16px;
      color: #333;
      font-weight: bold;
    }
    
    .chart-date {
      margin-bottom: 15px;
      font-size: 12px;
      color: #666;
    }
    
    .chart {
      height: 300px;
    }
  }
}

.table-section {
  margin-bottom: 20px;
  
  // 表格容器样式
  :deep(.el-card) {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  
  // 表格标题样式
  :deep(.card-title) {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    padding: 15px 20px;
    border-bottom: 1px solid #f0f0f0;
    background: #fafafa;
  }
  
  // 表格主体样式
  :deep(.el-table) {
    font-size: 12px;
    
    .el-table__header {
      background: #f8f9fa;
      
      th {
        background: #f8f9fa !important;
        color: #555;
        font-weight: 600;
        border-bottom: 2px solid #e9ecef;
      }
    }
    
    .el-table__body {
      tr {
        &:hover {
          background: #f5f7fa !important;
        }
        
        td {
          padding: 8px 0;
          border-bottom: 1px solid #f0f0f0;
        }
      }
    }
    
    // 固定列样式
    .el-table__fixed {
      box-shadow: 2px 0 6px rgba(0, 0, 0, 0.1);
    }
  }
  
  // 分页样式
  :deep(.el-pagination) {
    margin-top: 15px;
    text-align: right;
    
    .el-pagination__total {
      color: #666;
      font-size: 12px;
    }
    
    .el-pager li {
      font-size: 12px;
      min-width: 28px;
      height: 28px;
      line-height: 28px;
    }
    
    .btn-prev,
    .btn-next {
      height: 28px;
      line-height: 28px;
    }
  }
}

// 详细数据表格特殊样式
.table-section:first-of-type {
  :deep(.el-table) {
    .el-table__body tr {
      td:first-child {
        font-weight: 600;
        background: #fafafa;
      }
    }
  }
}

// 汇总数据表格特殊样式
.table-section:last-of-type {
  :deep(.el-table) {
    .el-table__body tr {
      td {
        font-weight: 500;
      }
      
      // 突出显示最大值和最小值列
      td:nth-child(2),
      td:nth-child(4) {
        background: #f0f9ff;
        color: #1d4ed8;
        font-weight: 600;
      }
    }
  }
}

// 表格特殊行样式
:deep(.warning-row) {
  background-color: #fffbe6 !important;
  
  td {
    background-color: #fffbe6 !important;
  }
}

:deep(.danger-row) {
  background-color: #fff2f0 !important;
  
  td {
    background-color: #fff2f0 !important;
  }
}

@media print {
  .card-content {
    overflow: visible !important;
  }
  
  .chart {
    break-inside: avoid;
  }
  
  .table-section {
    break-inside: avoid;
  }
}
</style> 