package org.thingsboard.server.dao.util.imodel.query.store;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.store.DeviceUsageJournal;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;

import java.util.Date;

@Getter
@Setter
public class DeviceUsageJournalPageRequest extends AdvancedPageableQueryEntity<DeviceUsageJournal, DeviceUsageJournalPageRequest> {
    // 使用部门
    private String departmentId;

    // 设备标签
    private String deviceLabelCode;

    // 使用人员
    private String userId;

    // 领用时间
    private String receiveTime;

    // 备注
    private String remark;

    public Date getReceiveTime() {
        return toDate(receiveTime);
    }
}
