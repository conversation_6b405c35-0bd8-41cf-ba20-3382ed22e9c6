<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.assay.AssayReportDataMapper">


    <select id="findList" resultType="org.thingsboard.server.dao.model.sql.assay.AssayReportData">
        select a.*, b.name as reportTypeName, c.name as waterSampleTypeName, d.name as waterSampleStateName
        from tb_assay_report_data a
        left join tb_assay_report_type b on a.report_type = b.id
        left join tb_assay_base_setting c on a.water_sample_type = c.id
        left join tb_assay_base_setting d on a.water_sample_state = d.id

        <where>
            a.tenant_id = #{param.tenantId}
            <if test="param.reportType != null and param.reportType != ''">
                and a.report_type = #{param.reportType}
            </if>
            <if test="param.title != null and param.title != ''">
                and a.title like '%' || #{param.title} || '%'
            </if>
            <if test="param.startTime != null">
                and a.create_time &gt;= to_timestamp(#{param.startTime} / 1000)
            </if>
            <if test="param.endTime != null">
                and a.create_time &lt;= to_timestamp(#{param.endTime} / 1000)
            </if>
            <if test="param.isQualified != null and param.isQualified != ''">
                and a.is_qualified = #{param.isQualified}
            </if>
            <if test="param.isComplete != null and param.isComplete != ''">
                and a.is_qualified = #{param.isComplete}
            </if>
        </where>
    </select>
</mapper>