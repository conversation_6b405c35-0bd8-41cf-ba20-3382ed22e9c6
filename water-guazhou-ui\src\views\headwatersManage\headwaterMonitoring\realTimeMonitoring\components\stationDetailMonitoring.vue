<template>
  <div>
    <div :style="{ background: appStore.isDark ? '#222536' : '#FFFFFF' }">
      <Tabs v-model="state.activeName" :config="tabsConfig"></Tabs>
    </div>
    <div class="view">
      <div v-if="state.activeName === 'status'" class="content">
        <div class="top">
          <div style="width: 100%; height: 100%" class="card-table">
            <Search ref="refGroup" :config="groupTabsConfig"></Search>
            <div
              style="display: flex; justify-content: space-between; height: 85%"
            >
              <div class="card-table">
                <FormTable
                  :config="baseTableConfig"
                  class="left-table"
                ></FormTable>
              </div>
              <div ref="echartsDiv" class="chart-box">
                <VChart
                  ref="refChart"
                  :theme="appStore.isDark ? 'dark' : 'light'"
                  :option="state.chartOption"
                ></VChart>
              </div>
            </div>
          </div>
        </div>
        <div class="bottom">
          <div style="width: 32%; overflow: hidden">
            <div class="title">现场实景</div>
            <SLCard class="chart-box" overlay>
              <el-carousel
                v-if="state.imgs.length > 0"
                trigger="click"
                height="40vh"
              >
                <el-carousel-item>
                  <el-image
                    v-for="item in state.imgs"
                    :key="item"
                    :src="item"
                    style="height: 100%; width: 100%"
                  >
                    <template #error>
                      <div class="image-slot">
                        <el-icon>
                          <Picture />
                        </el-icon>
                      </div>
                    </template>
                  </el-image>
                </el-carousel-item>
              </el-carousel>
              <div v-if="state.imgs.length === 0" class="bottom-1">
                <div class="no-pictures">
                  <div>
                    <el-icon size="110px" color="#E4E7F1">
                      <Picture />
                    </el-icon>
                  </div>
                  <div style="width: 70%; margin: 20px auto">
                    <span style="color: #54728f">请前往</span>
                    <span style="color: #54728f"
                      >“数据平台”>“档案基础数据”>“现场实品图”</span
                    >
                    <span style="color: #54728f">界面上传实景图</span>
                  </div>
                </div>
              </div>
            </SLCard>
          </div>
          <div style="width: 32%; overflow: hidden">
            <div class="title">水源信息</div>
            <SLCard class="chart-box" title="" overlay>
              <SLAmap
                ref="refAmap"
                :hide-input="true"
                :init-center-mark="false"
              ></SLAmap>
            </SLCard>
          </div>
          <div style="width: 32%; height: 90%">
            <div class="title">报警信息</div>
            <SLCard class="chart-box" title="" overlay>
              <FormTable :config="alarmTableConfig"></FormTable>
            </SLCard>
          </div>
        </div>
      </div>

      <div v-if="state.activeName === 'search'" class="content1">
        <SLCard title=" " overlay>
          <template #title>
            <CardSearch
              ref="cardSearch"
              style="margin-top: 5px"
              :config="cardSearchConfig"
            />
          </template>
          <SLCard title=" " class="card-table">
            <template #right>
              <el-radio-group v-model="state.searchActiveName">
                <el-radio-button label="echarts">
                  <Icon
                    style="margin-right: 1px; font-size: 16px"
                    icon="clarity:line-chart-line"
                  />
                </el-radio-button>
                <el-radio-button label="list">
                  <Icon
                    style="margin-right: 1px; font-size: 16px"
                    icon="material-symbols:table"
                  />
                </el-radio-button>
              </el-radio-group>
            </template>
            <!-- 列表模式 -->
            <div v-if="state.searchActiveName === 'list'">
              <FormTable
                ref="refTable"
                :config="dataTableConfig"
                class="chart-box"
              ></FormTable>
            </div>
            <!-- 图表模式 -->
            <div
              v-if="state.searchActiveName === 'echarts'"
              ref="echartsDiv"
              class="chart-box"
            >
              <VChart ref="refChart1" :option="state.chartOption1"></VChart>
            </div>
          </SLCard>
        </SLCard>
      </div>

      <div v-if="state.activeName === 'alarm'" class="content1">
        <SLCard class="search-card" title=" " overlay>
          <template #title>
            <CardSearch ref="alarmCardSearch" :config="alarmCardSearchConfig" />
          </template>
          <FormTable
            ref="refAlarmTable"
            :config="alarmTableConfig"
            class="chart-box"
          ></FormTable>
        </SLCard>
      </div>

      <div v-if="state.activeName === 'control'" class="content"></div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs';
import { reactive } from 'vue';
import elementResizeDetectorMaker from 'element-resize-detector';
import {
  Download,
  Search as SearchIcon,
  Picture
} from '@element-plus/icons-vue';
import { Icon } from '@iconify/vue';
import { lineOption } from '../../../echartsData/echart';
import { IECharts } from '@/plugins/echart';
import { filterTime } from '../../data/data';
import useStation from '@/hooks/station/useStation';
import {
  GetStationRealTimeDetail,
  GetStationAlarmList,
  GetStationDetail
} from '@/api/shuiwureports/zhandian';
import {
  getThreeDaysData,
  stationDayDataQuery
} from '@/api/headwatersManage/headwaterMonitoring';
import { removeSlash } from '@/utils/removeIdSlash';
import { getAppointProject } from '@/api/project';
import { useAppStore } from '@/store';
import { getStationImageUrl } from '@/utils/URLHelper';

const appStore = useAppStore();
const { getStationAttrGroups } = useStation();
const erd = elementResizeDetectorMaker();
const props = defineProps<{
  stationId: string;
  monitor: any;
}>();

const today = dayjs().date();
const state = reactive<{
  activeName: string;
  chartOption: any;
  chartOption1: any;
  searchActiveName: string;
  groupTab: string;
  currentGroupTabs: any;
  dataList: any;
  stationInfo: any;
  imgs: string[];
  stationInfoColumns: any;
}>({
  activeName: 'status',
  chartOption: null,
  chartOption1: null,
  searchActiveName: 'echarts',
  groupTab: '',
  currentGroupTabs: [],
  dataList: [],
  stationInfo: null,
  imgs: [],
  stationInfoColumns: []
});

const refAmap = ref<ISLAmapIns>();
const refTable = ref<ICardTableIns>();
const alarmCardSearch = ref<ISearchIns>();
const refAlarmTable = ref<ICardTableIns>();
const echartsDiv = ref<any>();
const refChart = ref<IECharts>();
const refChart1 = ref<IECharts>();
const cardSearch = ref<ISearchIns>();
const refGroup = ref<ISearchIns>();
let alarmTableData = reactive<any>([]);
let stationDayData = reactive<any>([]);
watch(
  () => state.activeName,
  () => {
    if (state.activeName === 'echarts') {
      refuseChart();
    }
  }
);
watch(
  () => [props.stationId, props.monitor, state.searchActiveName],
  async (newV, oldV) => {
    if (newV[0] && (oldV[0] !== newV[0] || oldV[1]?.title !== newV[1]?.title)) {
      const res = await GetStationDetail(props.stationId);
      state.stationInfo = res.data;
      const project = await getAppointProject(res.data?.projectId);
      state.stationInfo.projectName = project?.data?.name;
      state.imgs = state.stationInfo.imgs
        ? state.stationInfo.imgs.split(',')
        : [];
      console.log(' state.imgs', state.imgs);
      refreshData();
      setTimeout(async () => {
        addMarks(state.stationInfo);
      }, 1000);
    }
    if (oldV[2] !== newV[2] && state.searchActiveName === 'echarts') {
      await refuseChart();
    }
  }
);
// 添加地图图标
const addMarks = async (data?: any) => {
  const location = data.location?.split(',');
  location?.length === 2 &&
    refAmap.value?.setMarker(
      location,
      {
        icon: getStationImageUrl('泵站.png')
        // extData: {
        //   row: data
        // }
      },
      () => {
        addInfoWindow(data);
      }
    );
  addInfoWindow(data);
};
// 显示地图弹出层
const addInfoWindow = async (data: any) => {
  // const res = await GetStationRealTimeDetail(row.stationId || row.id)
  const values = [
    { label: '名称', value: data.name },
    { label: '类型', value: data.type },
    { label: '所属项目', value: data.projectName },
    { label: '地址', value: data.address },
    { label: '经纬度', value: data.location },
    { label: '备注', value: data.remark }
  ];
  const location = data.location?.split(',');
  location?.length === 2 &&
    refAmap.value?.setListInfoWindow({
      point: location,
      values,
      title: data.name
    });
};
// 出水列表
const baseTableConfig = reactive<ITable>({
  loading: true,
  currentRow: [],
  currentRowKey: 'property',
  highlightCurrentRow: true,
  dataList: [],
  columns: [
    { prop: 'propertyName', label: '检测项名称' },
    { prop: 'value', label: '检测项数据' },
    {
      prop: 'collectionTime',
      label: '采集时间',
      formatter: (value: any) => {
        return value.collectionTime > 0
          ? dayjs(value.collectionTime).format('YYYY-MM-DD HH:mm:ss')
          : '-';
      }
    }
  ],
  operations: [],
  pagination: {
    hide: true
  },
  handleRowClick: (row: any) => {
    baseTableConfig.currentRow = row;
    refuseChart();
  }
});
// 报警列表
const alarmTableConfig = reactive<ITable>({
  loading: true,
  dataList: [],
  indexVisible: true,
  columns: [
    {
      prop: 'alarmJsonName',
      label: '报警描述',
      formatter: (row) => {
        if (row.type === 'offline') {
          return row.deviceName + '_离线';
        }
        return row.deviceName + '_' + row.alarmJsonName;
      }
    },
    {
      prop: 'createdTime',
      label: '报警时间',
      formatter: (row: any, val: any) => {
        return val ? dayjs(val).format('YYYY-MM-DD HH:mm:ss') : '';
      }
    }
  ],
  operations: [],
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    layout: 'total, prev, pager, next,  jumper',
    handleSize: (val) => {
      alarmTableConfig.pagination.limit = val;
      // cardTableConfig.dataList = tableData.slice((currentPage-1)*pagesize,currentPage*pagesize)
    },
    refreshData: ({ page, size }) => {
      alarmTableConfig.pagination.page = page;
      alarmTableConfig.pagination.limit = size;
      alarmTableConfig.dataList = alarmTableData.slice(
        (page - 1) * size,
        page * size
      );
    }
  }
});
// 查询数据列表
const dataTableConfig = reactive<ITable>({
  loading: true,
  dataList: [],
  indexVisible: true,
  columns: [],
  operations: [],
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    layout: 'total, prev, pager, next, jumper',
    handleSize: (val) => {
      dataTableConfig.pagination.limit = val;
    },
    refreshData: ({ page, size }) => {
      dataTableConfig.pagination.page = page;
      dataTableConfig.pagination.limit = size;
      dataTableConfig.dataList = stationDayData?.tableDataList?.slice(
        (page - 1) * size,
        page * size
      );
    }
  }
});

// 搜索栏初始化配置
const cardSearchConfig = reactive<ISearch>({
  defaultParams: {
    date: [
      dayjs()
        .date(today - 2)
        .format('YYYY-MM-DD'),
      dayjs().date(today).format('YYYY-MM-DD')
    ],
    filterStart: [0, 23],
    group: '',
    attributeId: ''
  },
  filters: [
    { type: 'daterange', label: '选中日期', field: 'date', clearable: false },
    {
      label: '时间',
      type: 'range',
      rangeType: 'select',
      field: 'filterStart',
      options: JSON.parse(JSON.stringify(filterTime)),
      startPlaceHolder: '0时',
      endPlaceHolder: '23时',
      startOptionDisabled: (option, end) => {
        return end && Number(end) < option.value;
      },
      endOptionDisabled: (option, start) => {
        return start && option.value <= Number(start);
      }
    },
    {
      label: '监测组',
      labelWidth: 60,
      field: 'group',
      type: 'select',
      clearable: false,
      options: [],
      onChange: (val) => changeGroup(val)
    },
    {
      label: '曲线类型',
      labelWidth: 70,
      field: 'attributeId',
      type: 'select',
      clearable: false,
      options: [],
      hidden: computed(() => state.searchActiveName === 'list')
    }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        // {
        //   perm: true,
        //   isBlockBtn: true,
        //   text: '',
        //   svgIcon: shallowRef(Filter),
        //   click: () => {
        //     cardSearch.value?.toggleMore()
        //   }
        // },
        {
          perm: true,
          text: '查询',
          click: () => {
            dataTableConfig.pagination.page = 1;
            getStationDayDataQuery();
          },
          svgIcon: shallowRef(SearchIcon)
        },
        {
          perm: true,
          type: 'warning',
          text: '导出',
          svgIcon: shallowRef(Download),
          hide: () => {
            return state.searchActiveName !== 'list';
          },
          click: () => {
            refTable.value?.exportTable();
          }
        }
      ]
    }
  ]
});

// 报警搜索栏配置
const alarmCardSearchConfig = reactive<ISearch>({
  defaultParams: {
    date: [
      dayjs()
        .date(today - 2)
        .format('YYYY-MM-DD'),
      dayjs().date(today).format('YYYY-MM-DD')
    ]
  },
  filters: [
    { type: 'daterange', label: '选择时间', field: 'date', clearable: false },
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          click: () => getAlarmList(),
          svgIcon: shallowRef(SearchIcon)
        },
        {
          perm: true,
          type: 'warning',
          text: '导出',
          svgIcon: shallowRef(Download),
          click: () => {
            refAlarmTable.value?.exportTable();
          }
        }
      ]
    }
  ]
});

// 顶部分类tabs
const tabsConfig = reactive<ITabs>({
  type: 'tabs',
  tabType: 'simple',
  width: '100%',
  tabs: [
    { label: '当前状态', value: 'status' },
    { label: '数据查询', value: 'search' },
    { label: '报警信息', value: 'alarm' }
    // { label: '水源控制', value: 'control' }
  ],
  handleTabClick: (tab: any) => {
    console.log(tab.props.name);
    state.activeName = tab.props.name;
    if (state.activeName === 'search') {
      // state.currentGroupTabs.map(tab => {
      //   label: tab.type
      //   value: tab.type
      //   data: tab.children
      // })
      console.log(state.currentGroupTabs);
      nextTick(() => {
        const filter = cardSearchConfig.filters?.find(
          (filter) => filter.field === 'group'
        ) as IFormSelect;
        filter.options = state.currentGroupTabs;
        cardSearchConfig.defaultParams = {
          ...cardSearch.value?.queryParams,
          group: state.currentGroupTabs[0].value
        };
        cardSearch.value?.resetForm();
        changeGroup(state.currentGroupTabs[0].value);
      });
    } else if (state.activeName === 'alarm') {
      nextTick(() => {
        getAlarmList('range');
      });
    }
  }
});
// 选中属性组下的所有属性
const changeGroup = (value: any) => {
  const group = state.currentGroupTabs.find((t) => t.value === value);
  const filter = cardSearchConfig.filters?.find(
    (filter) => filter?.field === 'attributeId'
  ) as IFormSelect;
  filter.options = group.children.map((option: any) => {
    return {
      label: option.name,
      value: option.id,
      data: removeSlash(option.deviceId) + '.' + option.attr,
      unit: option.unit ? '(' + option.unit + ')' : ''
    };
  });
  cardSearchConfig.defaultParams = {
    ...cardSearch.value?.queryParams,
    attributeId: group.children[0].id
  };
  cardSearch.value?.resetForm();
  console.log(value);
  getStationDayDataQuery();
};
// 站点历史数据查询
const getStationDayDataQuery = async () => {
  const queryParams = cardSearch.value?.queryParams || {};
  const [start, end] = queryParams.date || [];
  const [filterStart, filterEnd] = queryParams.filterStart || [];
  const params = {
    filterStart: filterStart || 0,
    filterEnd: filterEnd || 23,
    queryType: '10m',
    stationId: props.stationId,
    group: queryParams?.group,
    start: start ? dayjs(start).startOf('day').valueOf() : '',
    end: end ? dayjs(end).endOf('day').valueOf() : ''
  };
  const res = await stationDayDataQuery(params);
  stationDayData = res.data?.data;
  // state.dataList = stationDayData
  const columns = stationDayData?.tableInfo.map((item: any) => {
    return {
      prop: item.columnValue,
      label: item.columnName,
      unit: item.unit ? '(' + item.unit + ')' : ''
    };
  });
  console.log(columns);
  dataTableConfig.columns = columns;
  dataTableConfig.dataList = stationDayData?.tableDataList?.slice(
    (1 - 1) * 20,
    20
  );
  // dataTableConfig.dataList = stationDayData?.tableDataList
  dataTableConfig.pagination.total = stationDayData?.tableDataList?.length;
  dataTableConfig.loading = false;
  refreshDataEchart(queryParams?.attributeId);
};
// 数据查询某属性值图表
const refreshDataEchart = (attributeId: string) => {
  // refuseChart()
  const dataEchart = lineOption();
  const attributeList = cardSearchConfig.filters?.find(
    (filter) => filter.field === 'attributeId'
  ) as IFormSelect;
  const attribute = attributeList.options?.find(
    (data) => data.value === attributeId
  ) as any;
  dataEchart.yAxis[0].name = (attribute.label +
    (attribute.unit ? attribute.unit : '')) as string;
  dataEchart.xAxis.data = stationDayData?.tableDataList.map((item) => item.ts);
  console.log(
    attributeId + '.' + attribute.data,
    stationDayData?.tableDataList
  );
  const serie = {
    name: attribute.label,
    smooth: true,
    data: stationDayData?.tableDataList.map((item) => item[attribute.data]),
    type: 'line',
    markPoint: {
      data: [
        {
          type: 'max',
          name: '最大值',
          label: {
            fontSize: 12,
            color: appStore.isDark ? '#ffffff' : '#000000'
          }
        },
        {
          type: 'min',
          name: '最小值',
          label: {
            color: appStore.isDark ? '#ffffff' : '#000000'
          }
        }
      ]
    }
  };
  refChart1.value?.clear();
  dataEchart.series = [serie];
  nextTick(() => {
    if (echartsDiv.value) {
      erd.listenTo(echartsDiv.value, () => {
        state.chartOption1 = dataEchart;
        refChart1.value?.resize();
      });
    }
  });
};

// 站点属性组
const groupTabsConfig = reactive<ISearch>({
  filters: [
    {
      type: 'radio-button',
      label: '',
      field: 'groupTab',
      options: computed(() => state.currentGroupTabs) as any,
      onChange: (val) => {
        if (val) getAttributes(val);
      }
    }
  ]
});

const refreshData = () => {
  state.activeName = 'status';
  console.log('refreshData');
  getStationGroup();
};

// 获取属性组
const getStationGroup = async () => {
  const groupStation = await getStationAttrGroups(props.stationId);
  state.currentGroupTabs = groupStation;
  groupTabsConfig.defaultParams = {
    groupTab: groupStation[0]?.id as string
  };
  refGroup.value?.resetForm();
  await getAttributes(props.monitor.name);
  await getAlarmList();
};
// 获取报警列表
const getAlarmList = async (dateType?: string) => {
  //
  alarmTableConfig.loading = true;
  let start = dayjs().startOf('month').valueOf();
  let end = dayjs().endOf('month').valueOf();
  if (dateType === 'range') {
    const queryParams = alarmCardSearch.value?.queryParams;
    start = dayjs(queryParams?.date[0]).startOf('day').valueOf();
    end = dayjs(queryParams?.date[1]).endOf('day').valueOf();
  }
  GetStationAlarmList(props.stationId, start, end).then((res) => {
    console.log('res', res);
    alarmTableData = res.data;
    alarmTableConfig.dataList = alarmTableData?.slice(0, 20);
    alarmTableConfig.pagination.total = alarmTableData.length;
    // alarmTableConfig.currentRow = alarmTableData[0]
    alarmTableConfig.loading = false;
  });
};

// 获取组属性数据
const getAttributes = async (name: any) => {
  baseTableConfig.loading = true;
  const valveData = await GetStationRealTimeDetail(props.stationId, name);
  baseTableConfig.dataList = valveData.data as any;
  const defaultRow = valveData?.data[0];
  baseTableConfig.currentRow = defaultRow;
  refuseChart();
  console.log(baseTableConfig.currentRow);
  baseTableConfig.loading = false;
};

// 加载图表
const refuseChart = async () => {
  const data = baseTableConfig.currentRow;
  const threeDaysData = await getThreeDaysData({
    deviceId: removeSlash(data.deviceId),
    attr: data.property
  });
  const tData = threeDaysData.data?.data;
  const options = lineOption();
  const dataMap = [
    { name: '前天', key: 'beforeYesterdayDataList' },
    { name: '昨天', key: 'yesterdayDataList' },
    { name: '今天', key: 'todayDataList' }
  ];
  options.xAxis.data = tData.todayDataList.map((item) => item.ts);
  options.yAxis[0].name = data.propertyName.concat(
    data.unit ? '(' + data.unit + ')' : ''
  );
  const series = dataMap.map((item) => {
    const data = tData[item.key].map((item) => item.value);
    return {
      name: item.name,
      smooth: true,
      data,
      type: 'line',
      markPoint: {
        data: [
          {
            type: 'max',
            name: '最大值',
            label: {
              fontSize: 12,
              color: appStore.isDark ? '#ffffff' : '#000000'
            }
          },
          {
            type: 'min',
            name: '最小值',
            label: {
              color: appStore.isDark ? '#ffffff' : '#000000'
            }
          }
        ]
      },
      markLine: {
        data: [{ type: 'average', name: '平均值' }]
      }
    };
  });
  options.series = series;
  refChart.value?.clear();
  await nextTick(() => {
    if (echartsDiv.value) {
      erd.listenTo(echartsDiv.value, () => {
        state.chartOption = options;
        refChart.value?.resize();
      });
    }
  });
  // emit('hiddenLoading')
};

onMounted(async () => {
  console.log('onMounted');
});
</script>
<style lang="scss" scoped>
.view {
  margin-top: 15px;
}

.content {
  height: calc(100vh - 150px);
  width: 100%;

  .top {
    height: 43%;
    width: 100%;
    display: flex;
    justify-content: space-between;

    .card-table {
      width: 40%;
      height: 100%;
    }

    .chart-box {
      width: 58%;
    }
  }

  .bottom {
    height: calc(50% - 10px);
    display: flex;
    justify-content: space-between;
    margin-top: 20px;

    .chart-box {
      height: 100%;
    }

    .title {
      border-left: 4px solid #318dff;
      padding-left: 10px;
      margin-bottom: 20px;
    }

    .bottom-1 {
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-around;
    }

    .no-pictures {
      font-size: 14px;
      text-align: center;
      color: #383f56;
      display: flex;
      flex-direction: column;
      justify-content: flex-end;
    }
  }
}

.content1 {
  height: 100%;
  width: 100%;
  overflow: hidden;

  .search-card {
    padding-top: 80px;
  }

  .card-table {
    width: 100%;
  }

  .chart-box {
    width: 100%;
    height: calc(100vh - 270px);
  }

  :deep(.sl-card.hastitle .sl-card-title) {
    height: auto !important;
  }
}
</style>
