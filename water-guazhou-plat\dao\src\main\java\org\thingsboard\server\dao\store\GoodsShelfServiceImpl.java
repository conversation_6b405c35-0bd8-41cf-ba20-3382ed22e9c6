package org.thingsboard.server.dao.store;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.store.GoodsShelf;
import org.thingsboard.server.dao.model.sql.store.Store;
import org.thingsboard.server.dao.sql.department.GoodsShelfMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.store.GoodsShelfSaveRequest;
import org.thingsboard.server.dao.util.imodel.query.store.StorePageRequest;
import org.thingsboard.server.dao.util.imodel.response.tree.PagedTreeResponse;

import java.util.List;

@Service
public class GoodsShelfServiceImpl implements GoodsShelfService {
    @Autowired
    private GoodsShelfMapper mapper;

    @Autowired
    private StoreService service;


    @Override
    public PagedTreeResponse<Store> findTreeConditional(StorePageRequest request) {
        IPage<Store> pagedStore = service.findAllConditional(request);
        List<Store> treeEntityNodes = QueryUtil.buildTree(pagedStore.getRecords(), mapper::findChild);
        return new PagedTreeResponse<>(pagedStore, treeEntityNodes);
    }

    @Override
    public GoodsShelf save(GoodsShelfSaveRequest entity) {
        return QueryUtil.saveOrUpdateOneByRequest(entity, mapper::insert, mapper::update);
    }

    @Override
    public boolean update(GoodsShelf entity) {
        return mapper.update(entity);
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteWithChildrenRecursive(id) > 0;
    }

    @Override
    public boolean isCodeExists(String code, String id, String tenantId) {
        return mapper.isCodeExists(code, id, tenantId);
    }

    @Override
    public boolean canBeDelete(String id) {
        return mapper.canBeDelete(id);
    }

}
