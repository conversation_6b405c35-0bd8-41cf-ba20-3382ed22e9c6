/**
 * Copyright © 2016-2019 The Thingsboard Authors
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.sql.device;

import com.google.common.util.concurrent.ListenableFuture;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Component;
import org.thingsboard.server.common.data.*;
import org.thingsboard.server.common.data.id.CustomerId;
import org.thingsboard.server.common.data.id.DeviceId;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.TextPageLink;
import org.thingsboard.server.dao.DaoUtil;
import org.thingsboard.server.dao.device.DeviceDao;
import org.thingsboard.server.dao.model.sql.DeviceEntity;
import org.thingsboard.server.dao.sql.JpaAbstractSearchTextDao;
import org.thingsboard.server.dao.util.SqlDao;
import org.thingsboard.server.dao.util.mapping.JacksonUtil;

import java.util.*;

import static org.thingsboard.server.common.data.UUIDConverter.*;
import static org.thingsboard.server.dao.model.ModelConstants.NULL_UUID_STR;

/**
 * Created by Valerii Sosliuk on 5/6/2017.
 */
@Component
@SqlDao
public class JpaDeviceDao extends JpaAbstractSearchTextDao<DeviceEntity, Device> implements DeviceDao {

    @Autowired
    private DeviceRepository deviceRepository;

    @Override
    protected Class<DeviceEntity> getEntityClass() {
        return DeviceEntity.class;
    }

    @Override
    protected CrudRepository<DeviceEntity, String> getCrudRepository() {
        return deviceRepository;
    }

    @Override
    public List<Device> findDevicesByTenantId(UUID tenantId, TextPageLink pageLink) {
        return DaoUtil.convertDataList(
                deviceRepository.findByTenantId(
                        fromTimeUUID(tenantId),
                        Objects.toString(pageLink.getTextSearch(), ""),
                        pageLink.getIdOffset() == null ? NULL_UUID_STR : fromTimeUUID(pageLink.getIdOffset()),
                        new PageRequest(0, pageLink.getLimit())));
    }

    @Override
    public ListenableFuture<List<Device>> findDevicesByTenantIdAndIdsAsync(UUID tenantId, List<UUID> deviceIds) {
        return service.submit(() -> DaoUtil.convertDataList(deviceRepository.findDevicesByTenantIdAndIdInOrderByCreateTimeAsc(UUIDConverter.fromTimeUUID(tenantId), fromTimeUUIDs(deviceIds))));
    }

    @Override
    public List<Device> findDevicesByTenantIdAndCustomerId(UUID tenantId, UUID customerId, TextPageLink pageLink) {
        return DaoUtil.convertDataList(
                deviceRepository.findByTenantIdAndCustomerId(
                        fromTimeUUID(tenantId),
                        fromTimeUUID(customerId),
                        Objects.toString(pageLink.getTextSearch(), ""),
                        pageLink.getIdOffset() == null ? NULL_UUID_STR : fromTimeUUID(pageLink.getIdOffset()),
                        new PageRequest(0, pageLink.getLimit())));
    }

    @Override
    public ListenableFuture<List<Device>> findDevicesByTenantIdCustomerIdAndIdsAsync(UUID tenantId, UUID customerId, List<UUID> deviceIds) {
        return service.submit(() -> DaoUtil.convertDataList(
                deviceRepository.findDevicesByTenantIdAndCustomerIdAndIdInOrderByCreateTimeAsc(fromTimeUUID(tenantId), fromTimeUUID(customerId), fromTimeUUIDs(deviceIds))));
    }

    @Override
    public Optional<Device> findDeviceByTenantIdAndName(UUID tenantId, String name) {
        Device device = DaoUtil.getData(deviceRepository.findByTenantIdAndNameOrderByCreateTimeAsc(fromTimeUUID(tenantId), name));
        return Optional.ofNullable(device);
    }

    @Override
    public List<Device> findDevicesByTenantIdAndType(UUID tenantId, String type, TextPageLink pageLink) {
        return DaoUtil.convertDataList(
                deviceRepository.findByTenantIdAndType(
                        fromTimeUUID(tenantId),
                        type,
                        Objects.toString(pageLink.getTextSearch(), ""),
                        pageLink.getIdOffset() == null ? NULL_UUID_STR : fromTimeUUID(pageLink.getIdOffset()),
                        new PageRequest(0, pageLink.getLimit())));
    }

    @Override
    public List<Device> findDevicesByTenantIdAndCustomerIdAndType(UUID tenantId, UUID customerId, String type, TextPageLink pageLink) {
        return DaoUtil.convertDataList(
                deviceRepository.findByTenantIdAndCustomerIdAndType(
                        fromTimeUUID(tenantId),
                        fromTimeUUID(customerId),
                        type,
                        Objects.toString(pageLink.getTextSearch(), ""),
                        pageLink.getIdOffset() == null ? NULL_UUID_STR : fromTimeUUID(pageLink.getIdOffset()),
                        new PageRequest(0, pageLink.getLimit())));
    }

    @Override
    public ListenableFuture<List<EntitySubtype>> findTenantDeviceTypesAsync(UUID tenantId) {
        return service.submit(() -> convertTenantDeviceTypesToDto(tenantId, deviceRepository.findTenantDeviceTypes(fromTimeUUID(tenantId))));
    }

    private List<EntitySubtype> convertTenantDeviceTypesToDto(UUID tenantId, List<String> types) {
        List<EntitySubtype> list = Collections.emptyList();
        if (types != null && !types.isEmpty()) {
            list = new ArrayList<>();
            for (String type : types) {
                list.add(new EntitySubtype(new TenantId(tenantId), EntityType.DEVICE, type));
            }
        }
        return list;
    }

    @Override
    public List<Device> findAllByTenantId(TenantId tenantId, String key) {
        List<String> keys = Arrays.asList(key.split(","));
        List ob = deviceRepository.findAllByTenantId(fromTimeUUID(tenantId.getId()), keys);
        List<Device> devices = new ArrayList<>();
        ob.forEach(o -> {
            Object[] data = (Object[]) o;
            Device device = new Device();
            device.setId(new DeviceId(fromString(String.valueOf(data[0]))));
            device.setCustomerId(new CustomerId(fromString(String.valueOf(data[1]))));
            device.setName(String.valueOf(data[3]));
            device.setType(String.valueOf(data[4]));
            device.setTenantId(new TenantId(fromString(String.valueOf(data[5]))));
            device.setAttribute(String.valueOf(data[6]));
            device.setIsDelete(String.valueOf(data[7]));
            device.setCreatedTime(Long.parseLong(String.valueOf(data[8])));
            if (data[9] != null) {
                device.setGateWayId(new DeviceId(fromString(String.valueOf(data[9]))));
            }
            device.setTemplateId(String.valueOf(data[10]));
            devices.add(device);
        });
        return devices;
    }

    @Override
    public List<Device> findAllByTenantId(TenantId tenantId) {
        return DaoUtil.convertDataList(deviceRepository.findByTenantIdOrderByCreateTimeAsc(UUIDConverter.fromTimeUUID(tenantId.getId())));
    }

    @Override
    public List<Device> findByGateway(DeviceId deviceId) {
        return DaoUtil.convertDataList(deviceRepository.findByGateWayIdOrderByCreateTimeAsc(UUIDConverter.fromTimeUUID(deviceId.getId())));
    }

    @Override
    public ListenableFuture<Device> findDeviceByIdAsync(DeviceId deviceId) {
        return service.submit(() -> findById(deviceId.getId()));
    }

    @Override
    public List<Device> findDeviceByAdditionalInfo(String additionalInfo) {
        return DaoUtil.convertDataList(deviceRepository.findByAdditionalInfo(JacksonUtil.toJsonNode(additionalInfo)));
    }

    @Override
    public List<Device> findDeviceByProjectId(String projectId) {
        return DaoUtil.convertDataList(deviceRepository.findDevicesByProjectId(projectId));
    }

    @Override
    public List<Device> findByTemplateId(String templateId) {
        return DaoUtil.convertDataList(deviceRepository.findByTemplateId(templateId));
    }

    @Override
    public List<Device> findGateWayByProjectId(String projectId) {
        return DaoUtil.convertDataList(deviceRepository.findDevicesByProjectId(projectId));
    }

    @Override
    public List<Device> findAllByProjectId(String projectId, String key) {
        List ob = deviceRepository.findAllByProjectId(projectId, key);
        List<Device> devices = new ArrayList<>();
        ob.forEach(o -> {
            Object[] data = (Object[]) o;
            Device device = new Device();
            device.setId(new DeviceId(fromString(String.valueOf(data[0]))));
            device.setCustomerId(new CustomerId(fromString(String.valueOf(data[1]))));
            device.setName(String.valueOf(data[3]));
            device.setType(String.valueOf(data[4]));
            device.setTenantId(new TenantId(fromString(String.valueOf(data[5]))));
            device.setAttribute(String.valueOf(data[6]));
            device.setIsDelete(String.valueOf(data[7]));
            device.setCreatedTime(Long.parseLong(String.valueOf(data[8])));
            if (data[9] != null) {
                device.setGateWayId(new DeviceId(fromString(String.valueOf(data[9]))));
            }
            device.setTemplateId(String.valueOf(data[10]));
            devices.add(device);
        });
        return devices;
    }

    @Override
    @Cacheable(cacheNames = CacheConstants.DEVICE_CACHE, key = "{#projectId,#type}")
    public List<Device> findDeviceByProjectIdAndType(String projectId, String type) {
        return DaoUtil.convertDataList(deviceRepository.findDevicesByProjectIdAndType(projectId, type));
    }

    @Override
    public List<Device> findAllByGatewayId(String gatewayId, String key) {
        List<String> keys = Arrays.asList(key.split(","));
        List ob = deviceRepository.findAllByGatewayId(gatewayId, keys);
        List<Device> devices = new ArrayList<>();
        ob.forEach(o -> {
            Object[] data = (Object[]) o;
            Device device = new Device();
            device.setId(new DeviceId(fromString(String.valueOf(data[0]))));
            device.setCustomerId(new CustomerId(fromString(String.valueOf(data[1]))));
            device.setName(String.valueOf(data[3]));
            device.setType(String.valueOf(data[4]));
            device.setTenantId(new TenantId(fromString(String.valueOf(data[5]))));
            device.setAttribute(String.valueOf(data[6]));
            device.setIsDelete(String.valueOf(data[7]));
            device.setCreatedTime(Long.parseLong(String.valueOf(data[8])));
            if (data[9] != null) {
                device.setGateWayId(new DeviceId(fromString(String.valueOf(data[9]))));
            }
            device.setAttrKey(String.valueOf(data[10]));
            device.setTemplateId(String.valueOf(data[11]));
            // data[12]
            device.setAdditionalInfo(JacksonUtil.toJsonNode(String.valueOf(data[12])));
            device.setLocation(String.valueOf(data[13]));
            if (data[14] != null) {
                device.setDeviceTypeName(String.valueOf(data[14]));
            }

            devices.add(device);
        });
        return devices;
    }

    @Override
    public long findAllModbusDevice() {
        return deviceRepository.countModbusDevice();
    }

    @Override
    public long findAllModbusDeviceByTenantId(TenantId tenantId) {
        return deviceRepository.countModbusDeviceByTenantId(UUIDConverter.fromTimeUUID(tenantId.getId()));
    }

    @Override
    public long findAllModbusDeviceByProjectId(String projectId) {
        return deviceRepository.countModbusDeviceByProjectId(projectId);
    }

    @Override
    public long findAllDtuDevice() {
        return deviceRepository.countDtuDevice();
    }

    @Override
    public long findAllDtuDeviceByTenantId(String tenantId) {
        return deviceRepository.countDtuDeviceByTenantId(tenantId);
    }

    @Override
    public long findAllDtuDeviceByProjectId(String projectId) {
        return deviceRepository.countDtuDeviceByProjectId(projectId);
    }

    @Override
    public List<Device> findByTenantId(TenantId tenantId) {
        List ob = deviceRepository.findByTenantId(UUIDConverter.fromTimeUUID(tenantId.getId()));
        List<Device> devices = new ArrayList<>();
        ob.forEach(o -> {
            Object[] data = (Object[]) o;
            Device device = new Device();
            device.setId(new DeviceId(fromString(String.valueOf(data[0]))));
            device.setCustomerId(new CustomerId(fromString(String.valueOf(data[1]))));
            device.setAdditionalInfo(JacksonUtil.toJsonNode(data[2]));
            device.setName(String.valueOf(data[3]));
            device.setType(String.valueOf(data[4]));
            device.setTenantId(new TenantId(fromString(String.valueOf(data[5]))));
            device.setIsDelete(String.valueOf(data[6]));
            device.setCreatedTime(Long.parseLong(String.valueOf(data[7])));
            if (data[8] != null) {
                device.setGateWayId(new DeviceId(fromString(String.valueOf(data[8]))));
            }
            if (data[9] != null) {
                device.setTemplateId(String.valueOf(data[9]));
            }
            device.setProjectId(String.valueOf(data[10]));

            devices.add(device);
        });
        return devices;
    }

    @Override
    public List<Device> findDevicesByProject(String projectId) {
        return DaoUtil.convertDataList(deviceRepository.findDevicesByProject(projectId));
    }

    @Override
    public List<Device> findByTemplateId(String id, String attribute) {
        List ob = deviceRepository.findByTemplateId(id, attribute);
        List<Device> devices = new ArrayList<>();
        ob.forEach(o -> {
            Object[] data = (Object[]) o;
            Device device = new Device();
            device.setId(new DeviceId(fromString(String.valueOf(data[0]))));
            device.setCustomerId(new CustomerId(fromString(String.valueOf(data[1]))));
            device.setName(String.valueOf(data[3]));
            device.setType(String.valueOf(data[4]));
            device.setTenantId(new TenantId(fromString(String.valueOf(data[5]))));
            device.setAttribute(String.valueOf(data[6]));
            device.setIsDelete(String.valueOf(data[7]));
            device.setCreatedTime(Long.parseLong(String.valueOf(data[8])));
            if (data[9] != null) {
                device.setGateWayId(new DeviceId(fromString(String.valueOf(data[9]))));
            }
            device.setAttrKey(String.valueOf(data[10]));
            device.setTemplateId(String.valueOf(data[11]));

            devices.add(device);
        });
        return devices;
    }

    @Override
    public List<Device> findAll() {
        return DaoUtil.convertDataList(deviceRepository.findCloudDeviceList());
    }

    @Override
    public List<Device> findByType(List<String> param, TenantId id) {
        return DaoUtil.convertDataList(deviceRepository.findByTenantIdAndTypeIn(UUIDConverter.fromTimeUUID(id.getId()), param));
    }

    @Override
    public List<Device> findByIdIn(String tenantId, List<String> deviceIdList) {
        return DaoUtil.convertDataList(deviceRepository.findByTenantIdAndIdInOrderByCreateTime(tenantId, deviceIdList));
    }

    @Override
    public List<Device> findDeviceByProjectIdAndTypeAndName(String projectId, String type, String name) {
        return DaoUtil.convertDataList(deviceRepository.findDevicesByProjectIdAndTypeAndName(projectId, type, "%" + name + "%"));
    }

    @Override
    public List<Device> findByForeignKey(String foreignKey) {
        return DaoUtil.convertDataList(deviceRepository.findByForeignKey(foreignKey));
    }

    @Override
    public List<Device> findByForeignKeyIn(List<String> deviceCodeList) {
        return DaoUtil.convertDataList(deviceRepository.findByForeignKeyIn(deviceCodeList));
    }

    @Override
    public List<Device> findByDeviceTypeName(String deviceTypeName, TenantId tenantId) {
        return DaoUtil.convertDataList(deviceRepository.findByDeviceTypeName(deviceTypeName, UUIDConverter.fromTimeUUID(tenantId.getId())));
    }

    @Override
    public List<Device> findDeviceByProjectId(List<String> projectIdList) {
        return DaoUtil.convertDataList(deviceRepository.findDevicesByProjectId(projectIdList));
    }


}
