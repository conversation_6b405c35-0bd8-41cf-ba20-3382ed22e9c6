<template>
  <div>
    <div class="item">
      <detailSteps :config="detailStep"></detailSteps>
    </div>
    <div class="item">
      <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
        <el-tab-pane label="基础信息" name="first">
          <descriptions
            ref="refbasicInformation"
            :config="basicInformation"
          ></descriptions>
        </el-tab-pane>
        <el-tab-pane label="处理信息" name="second">
          <descriptions
            ref="refprocessInformation"
            :config="processInformation"
          ></descriptions>
        </el-tab-pane>
      </el-tabs>
    </div>
    <div class="item">
      <el-timeline>
        <el-timeline-item
          v-for="(item, i) in state.timeline"
          :key="i"
          :hollow="true"
          type="primary"
          :hide-timestamp="true"
          placement="top"
        >
          <div class="timeline-item-wrapper">
            <div class="timeline-item-title">
              <span class="title">{{ item.data.typeName }}</span>
              <span class="time">{{ item.data.processTime }}</span>
            </div>
            <SLCard class="timeline-item-content">
              <template v-if="item.type === 'text'">
                <p v-if="item.data.type === 'ASSIGN'" class="text">
                  任务派发给了
                  <span class="text-name">{{
                    item.data?.nextProcessUserName
                  }}</span
                  >， 操作人：
                  <span class="text-name">{{
                    item.data?.processUserName
                  }}</span>
                </p>
                <p v-if="item.data.type === 'RESOLVING'" class="text">
                  {{ item.data?.nextProcessUserName }} 接收了工单
                </p>
              </template>

              <AttrTable
                v-if="item?.type === 'attr-table'"
                :data="item.data"
                :columns="item.columns"
              ></AttrTable>
            </SLCard>
          </div>
        </el-timeline-item>
      </el-timeline>
    </div>
  </div>
</template>

<script lang="ts" setup>
import type { TabsPaneContext } from 'element-plus';

import { reactive } from 'vue';
import { Icon } from '@iconify/vue';
import {
  GetWorkOrderDetail,
  GetWorkOrderStages,
  getWorkOrderEmergencyLevelList
} from '@/api/workorder';
import { SLMessage } from '@/utils/Message';
import { fileStrToArr, traverse } from '@/utils/GlobalHelper';
import { initWorkOrderDetailStepInfo } from '../config';
import detailSteps from './detailSteps.vue';
import descriptions from '@/components/Descriptions/index.vue';

const activeName = ref('first');

const props = defineProps<{
  id?: string;
}>();

const refbasicInformation = ref<IDescriptionsIns>();
const refprocessInformation = ref<IDescriptionsIns>();

function initOptions() {
  // 紧急程度
  getWorkOrderEmergencyLevelList('1').then((res) => {
    state.WorkOrderEmergencyLevelList = traverse(
      res.data.data || [],
      'children',
      { label: 'name', value: 'id' }
    );
  });
}

const handleClick = (tab: TabsPaneContext, event: Event) => {
  console.log(tab, event);
};

const state = reactive<{
  detail: Record<string, any>;
  timeline: {
    type: 'attr-table' | 'text';
    data: any;
    columns?: any[];
  }[];
  WorkOrderEmergencyLevelList: any[];
}>({
  detail: {},
  timeline: [],
  WorkOrderEmergencyLevelList: []
});

const detailStep = reactive({
  status: '',
  statusName: ''
});

const basicInformation = reactive<IDescriptionsConfig>({
  title: '',
  defaultValue: {},
  border: false,
  direction: 'horizontal',
  fields: [
    {
      xl: 8,
      type: 'text',
      label: '工单编号:',
      field: 'serialNo'
    },
    {
      xl: 8,
      type: 'text',
      label: '标题:',
      field: 'title'
    },
    {
      xl: 8,
      type: 'text',
      label: '来源:',
      field: 'source'
    },
    {
      xl: 8,
      type: 'text',
      label: '紧急程度:',
      field: 'level',
      tag: true,
      colorTag: (row): string =>
        state.WorkOrderEmergencyLevelList.find((item) => item.id === row)
          ?.color || '',
      formatter: (row) =>
        state.WorkOrderEmergencyLevelList.find((item) => item.id === row)?.label
    },
    {
      xl: 8,
      type: 'text',
      label: '类型:',
      field: 'type'
    },
    {
      xl: 8,
      type: 'text',
      label: '发起人:',
      field: 'organizerName'
    },
    {
      xl: 8,
      type: 'text',
      label: '业务单号:',
      field: 'serialNo'
    },
    {
      xl: 8,
      type: 'text',
      label: '地址:',
      field: 'address'
    }
  ]
});

const processInformation = reactive<IDescriptionsConfig>({
  title: '',
  defaultValue: {},
  border: false,
  direction: 'horizontal',
  fields: [
    {
      xl: 8,
      type: 'text',
      label: '处理人:',
      field: 'processUserName'
    },
    {
      xl: 8,
      type: 'text',
      label: '处理级别:',
      field: 'processLevelLabel'
    },
    {
      xl: 8,
      type: 'text',
      label: '预计完成时间:',
      field: 'estimatedFinishTime'
    },
    {
      xl: 8,
      type: 'text',
      label: '完成时间:',
      field: 'completeTime'
    }
  ]
});

const refreshData = async () => {
  const id = props.id || '';
  if (!id) return SLMessage.error('系统错误');
  state.timeline = [];
  const res = await GetWorkOrderDetail(id);
  state.detail = res.data?.data || {};
  detailStep.status = state.detail.status;
  const timeLineData = await GetWorkOrderStages(id);
  state.timeline.push({
    type: 'attr-table',
    data: {
      typeName: '发起',
      processUserId: state.detail.organizerId,
      processRemark: '发起工单',
      nextProcessUserId: '',
      processTime: res.data?.data?.createTime,
      ...state.detail
    },
    columns: initWorkOrderDetailStepInfo('CREATE')
  });

  const TableItems = [
    'CREATE',
    'ARRIVING',
    'PROCESSING',
    'SUBMIT',
    'CHARGEBACK_REVIEW',
    'REVIEW',
    'APPROVED',
    'REJECTED',
    'CHARGEBACK',
    'TERMINATED',
    'HANDOVER_REVIEW',
    'REASSIGN'
  ];
  timeLineData.data?.data.map((item) => {
    const addt =
      (item.processAdditionalInfo && JSON.parse(item.processAdditionalInfo)) ||
      {};
    addt?.audioUrl &&
      (addt.audioUrl =
        addt.audioUrl instanceof Array
          ? addt.audioUrl
          : fileStrToArr(addt.audioUrl));
    addt?.videoUrl &&
      (addt.videoUrl =
        addt.videoUrl instanceof Array
          ? addt.videoUrl
          : fileStrToArr(addt.videoUrl));
    addt?.otherFileUrl &&
      (addt.otherFileUrl =
        addt.otherFileUrl instanceof Array
          ? addt.otherFileUrl
          : fileStrToArr(addt.otherFileUrl));
    const isTable = TableItems.includes(item.type);
    if (addt.nextProcessUserId) {
      delete addt.nextProcessUserId;
    }
    state.timeline.unshift({
      type: isTable ? 'attr-table' : 'text',
      data: {
        ...item,
        ...addt
      },
      columns: initWorkOrderDetailStepInfo(item.type)
    });
  });
  basicInformation.defaultValue = { ...state.detail };
  processInformation.defaultValue = { ...state.detail };
};

onMounted(() => {
  initOptions();
  refreshData();
});

watch(props, () => {
  refreshData();
});
</script>

<style lang="scss" scoped>
.item {
  padding: 20px;
  background: var(--el-bg-color);
  border-radius: 8px;
  margin-bottom: 10px;
}

.el-timeline {
  margin-top: 10px;
}

.timeline-item-wrapper {
  padding-bottom: 8px;

  .timeline-item-title {
    margin-bottom: 12px;

    .title {
      color: #318dff;
      font-weight: bold;
      font-size: 14px;
      margin-right: 8px;
    }
  }

  .timeline-item-content {
    padding: 20px;
  }
}

.text {
  font-size: 12px;
}
</style>
