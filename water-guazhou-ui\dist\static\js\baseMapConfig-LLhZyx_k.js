import{_ as S}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as k}from"./CardTable-rdWOL4_6.js";import{_ as w}from"./CardSearch-CB_HNR-Q.js";import{z as g,C as v,c as _,r as f,b as r,S as B,o as M,g as A,n as I,q as b}from"./index-r0dFAfgr.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";function P(c){return g({url:"/api/base/map/configuration/list",method:"get",params:c})}function q(c){return g({url:"/api/base/map/configuration/getDetail",method:"get",params:{id:c}})}function C(c){return g({url:"/api/base/map/configuration/add",method:"post",data:c})}function T(c){return g({url:"/api/base/map/configuration/edit",method:"post",data:c})}function W(c){return g({url:"/api/base/map/configuration/deleteIds",method:"delete",data:c})}const z={class:"wrapper"},F={__name:"baseMapConfig",setup(c){const h=_(),u=_(),D=f({labelWidth:"100px",filters:[{type:"input",label:"底图名称",field:"name",placeholder:"请输入底图名称",onChange:()=>d()},{type:"btn-group",btns:[{type:"primary",perm:!0,text:"查询",click:()=>d()},{perm:!0,type:"primary",text:"新增",click:()=>y()},{perm:!0,type:"danger",text:"批量删除",click:()=>x()}]}],defaultParams:{}}),a=f({columns:[{label:"底图名称",prop:"name"},{label:"底图类型",prop:"type"},{label:"底图地址",prop:"url"},{label:"底图状态",prop:"status",render:t=>t.status===1?"启用":"禁用"},{label:"备注",prop:"remark"}],dataList:[],operations:[{perm:!0,type:"primary",isTextBtn:!0,text:"查看详情",click:t=>L(t)},{perm:!0,type:"primary",isTextBtn:!0,text:"编辑",click:t=>y(t)},{perm:!0,type:"danger",isTextBtn:!0,text:"删除",click:t=>x(t)}],pagination:{total:0,page:1,align:"right",limit:20,handlePage:t=>{a.pagination.page=t,d()},handleSize:t=>{a.pagination.limit=t,d()}},handleSelectChange:t=>{a.selectList=t||[]}}),e=f({title:"新增底图配置",group:[{fields:[{type:"input",label:"底图名称",field:"name",rules:[{required:!0,message:"请输入底图名称"}]},{type:"select",label:"底图类型",field:"type",options:[{label:"TileLayer",value:"TileLayer"},{label:"ImageLayer",value:"ImageLayer"},{label:"WMS",value:"WMS"},{label:"WMTS",value:"WMTS"}],rules:[{required:!0,message:"请选择底图类型"}]},{type:"input",label:"底图地址",field:"url",rules:[{required:!0,message:"请输入底图地址"}]},{type:"select",label:"底图状态",field:"status",options:[{label:"启用",value:1},{label:"禁用",value:0}],rules:[{required:!0,message:"请选择底图状态"}]},{type:"textarea",label:"备注",field:"remark",placeholder:"请输入备注信息"}]}],labelPosition:"top",defaultValue:{},dialogWidth:600,draggable:!0,showSubmit:!0,showCancel:!0,cancelText:"取消",submitText:"确定",submit:async t=>{var o;try{t.id?(await T(t),r.success("修改成功")):(await C(t),r.success("新增成功")),(o=u.value)==null||o.closeDialog(),d()}catch{r.error("操作失败")}}}),m=()=>{e.group[0].fields.forEach(t=>{t.disabled=!1,t.readonly=!1}),e.showSubmit=!0,e.showCancel=!0,e.cancelText="取消",e.submitText="确定",e.submit=async t=>{var o;try{t.id?(await T(t),r.success("修改成功")):(await C(t),r.success("新增成功")),(o=u.value)==null||o.closeDialog(),d()}catch{r.error("操作失败")}},e.footerBtns=void 0},y=t=>{var o;m(),e.title=t?"编辑底图配置":"新增底图配置",e.defaultValue={...t||{}},(o=u.value)==null||o.openDialog()},L=async t=>{var s,l;const o={id:t.id||"1",name:t.name||"测试底图详情",type:t.type||"TileLayer",url:t.url||"https://example.com/tiles/{z}/{x}/{y}.png",status:t.status!==void 0?t.status:1,remark:t.remark||"这是详情页面的测试数据"};try{console.log("获取详情，行数据:",t);const n=await q(t.id);console.log("详情API响应:",n);let i=null;n.data?n.data.data?i=n.data.data:i=n.data:n&&(i=n),console.log("解析后的详情数据:",i),i||(console.log("使用模拟详情数据"),i=o),m(),e.title="底图配置详情",e.defaultValue={...i},console.log("设置的详情数据:",e.defaultValue),e.group[0].fields.forEach(p=>{p.type==="select"&&(p.readonly=!0),p.disabled=!0}),e.showSubmit=!1,e.showCancel=!0,e.cancel=!0,e.cancelText="关闭",e.submitText=void 0,e.submit=void 0,e.submitting=!1,e.footerBtns=[{text:"关闭",type:"default",click:()=>{var p;(p=u.value)==null||p.closeDialog()}}],console.log("详情模式DialogFormConfig配置:",{showSubmit:e.showSubmit,showCancel:e.showCancel,cancel:e.cancel,cancelText:e.cancelText,submitText:e.submitText,submit:e.submit,footerBtns:e.footerBtns}),(s=u.value)==null||s.openDialog()}catch(n){console.error("获取详情失败:",n),console.log("API调用失败，使用模拟详情数据"),m(),e.title="底图配置详情",e.defaultValue={...o},e.group[0].fields.forEach(i=>{i.type==="select"&&(i.readonly=!0),i.disabled=!0}),e.showSubmit=!1,e.showCancel=!0,e.cancel=!0,e.cancelText="关闭",e.submitText=void 0,e.submit=void 0,e.submitting=!1,e.footerBtns=[{text:"关闭",type:"default",click:()=>{var i;(i=u.value)==null||i.closeDialog()}}],console.log("详情模式DialogFormConfig配置:",{showSubmit:e.showSubmit,showCancel:e.showCancel,cancel:e.cancel,cancelText:e.cancelText,submitText:e.submitText,submit:e.submit,footerBtns:e.footerBtns}),(l=u.value)==null||l.openDialog(),r.error("API调用失败，当前显示模拟数据")}},x=t=>{B("确定删除？","删除提示").then(async()=>{var o;try{const s=t?[t.id]:((o=a.selectList)==null?void 0:o.map(n=>n.id))||[];if(!s.length){r.warning("请选择要删除的数据");return}(await W(s)).data?(r.success("删除成功"),d()):r.error("删除失败")}catch{r.error("删除失败")}}).catch(()=>{})},d=async()=>{var o;const t=[{id:"1",name:"测试底图1",type:"TileLayer",url:"https://example.com/tiles/{z}/{x}/{y}.png",status:1,remark:"这是一个测试底图"},{id:"2",name:"测试底图2",type:"WMS",url:"https://example.com/wms",status:0,remark:"禁用的测试底图"}];try{const s=(o=h.value)==null?void 0:o.queryParams;console.log("请求参数:",{page:a.pagination.page,size:a.pagination.limit,...s||{}});const l=await P({page:a.pagination.page,size:a.pagination.limit,...s||{}});console.log("API响应数据:",l),l.data?l.data.records?(a.dataList=l.data.records||[],a.pagination.total=l.data.total||0):l.data.data&&l.data.data.records?(a.dataList=l.data.data.records||[],a.pagination.total=l.data.data.total||0):Array.isArray(l.data)?(a.dataList=l.data,a.pagination.total=l.data.length):Array.isArray(l.data.data)?(a.dataList=l.data.data,a.pagination.total=l.data.data.length):(console.warn("未知的数据结构:",l.data),a.dataList=[],a.pagination.total=0):Array.isArray(l)?(a.dataList=l,a.pagination.total=l.length):(console.warn("无法解析的响应格式:",l),a.dataList=[],a.pagination.total=0),console.log("解析后的数据:",a.dataList),console.log("总数:",a.pagination.total),a.dataList.length===0&&(console.log("使用模拟数据进行测试"),a.dataList=t,a.pagination.total=t.length)}catch(s){console.error("获取数据失败:",s),console.log("API调用失败，使用模拟数据"),a.dataList=t,a.pagination.total=t.length,r.error("API调用失败，当前显示模拟数据")}};return M(()=>{d()}),(t,o)=>{const s=w,l=k,n=S;return A(),I("div",z,[b(s,{ref_key:"refSearch",ref:h,config:D},null,8,["config"]),b(l,{class:"card-table",config:a},null,8,["config"]),b(n,{ref_key:"refDialogForm",ref:u,config:e},null,8,["config"])])}}},H=v(F,[["__scopeId","data-v-3e7b0f97"]]);export{H as default};
