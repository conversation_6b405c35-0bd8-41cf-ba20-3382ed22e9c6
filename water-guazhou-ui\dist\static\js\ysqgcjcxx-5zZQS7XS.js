import{d as n,r as c,a8 as p,bF as t,g as s,h as d,F as f,q as m,i as x,bz as b,C as u}from"./index-r0dFAfgr.js";import{d as y}from"./index-CCFuhOrs.js";/* empty css                             */import"./DPlayer-Be2AurQX.js";import"./DPlayer.min-_HMH7IVX.js";const _=n({__name:"ysqgcjcxx",props:{config:{}},setup(a){const i=a,l=c({defaultValue:p(()=>i.config),border:!0,direction:"horizontal",column:2,title:"验收期工程基础信息",fields:[{type:"text",label:"开始时间:",field:"beginTime",formatter:e=>t(e).format("YYYY-MM-DD")},{type:"text",label:"完成时间:",field:"endTimeName",formatter:e=>t(e).format("YYYY-MM-DD")},{type:"text",label:"申请单位:",field:"applicantOrganization"},{type:"text",label:"申请人:",field:"applicant"},{type:"text",label:"施工单位:",field:"constructOrganization"},{type:"text",label:"监理单位:",field:"supervisorOrganization"},{type:"text",label:"审计单位:",field:"auditOrganization"},{type:"text",label:"设计单位:",field:"designOrganization"},{type:"text",label:"申请电话:",field:"applicantPhone"},{type:"text",label:"验收说明:",field:"remark"},{type:"text",label:"创建人:",field:"creatorName"},{type:"text",label:"创建时间:",field:"createTimeName"},{type:"text",label:"最后更新人:",field:"updateUserName"},{type:"text",label:"最后更新时间:",field:"updateTimeName"}]});return(e,g)=>{const o=y,r=b;return s(),d(r,{class:"card"},{default:f(()=>[m(o,{config:x(l)},null,8,["config"])]),_:1})}}}),O=u(_,[["__scopeId","data-v-6602352a"]]);export{O as default};
