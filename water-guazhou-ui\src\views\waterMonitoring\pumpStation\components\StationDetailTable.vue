<template>
  <div class="station-detail">
    <div class="detail-header">
      <div class="title">泵站运行</div>
      <div class="actions">
        <el-form inline>
          <el-form-item label="当前方案:">
            <el-input v-model="currentScheme" disabled />
          </el-form-item>
          <el-form-item label="应用方案:">
            <el-select v-model="selectedScheme" placeholder="请选择方案">
              <el-option
                v-for="item in schemeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleApplyScheme">方案应用</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <div class="detail-content">
      <div v-for="(station, index) in detailData" :key="index" class="station-item">
        <div class="station-name">{{ station.stationName }}</div>
        <el-table :data="station.pumps" border stripe size="small">
          <el-table-column prop="pumpCode" label="泵机编号" width="100" />
          <el-table-column prop="pumpName" label="泵机名称" width="120" />
          <el-table-column prop="pumpType" label="泵机型号" width="120" />
          <el-table-column prop="pumpNum" label="泵数量" width="80" />
          <el-table-column prop="companyName" label="厂家名称" width="120" />
          <el-table-column label="性能参数" width="180">
            <template #default="{ row }">
              <div>{{ row.performanceParameters || '暂无数据' }}</div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  detailData: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['apply-scheme'])

const currentScheme = ref('')
const selectedScheme = ref('')
const schemeOptions = ref([
  { label: '方案A', value: 'scheme_a' },
  { label: '方案B', value: 'scheme_b' },
  { label: '方案C', value: 'scheme_c' }
])

// 获取所有泵站ID
const stationIds = computed(() => {
  return props.detailData.map(station => station.stationId)
})

// 应用方案
const handleApplyScheme = () => {
  if (!selectedScheme.value) {
    ElMessage.warning('请选择要应用的方案')
    return
  }

  emit('apply-scheme', {
    schemeId: selectedScheme.value,
    stationIds: stationIds.value
  })
}
</script>

<style scoped lang="scss">
.station-detail {
  height: 100%;
  display: flex;
  flex-direction: column;

  .detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .title {
      font-size: 16px;
      font-weight: bold;
    }
  }

  .detail-content {
    flex: 1;
    overflow-y: auto;

    .station-item {
      margin-bottom: 20px;

      .station-name {
        font-weight: bold;
        margin-bottom: 8px;
      }
    }
  }
}
</style>
