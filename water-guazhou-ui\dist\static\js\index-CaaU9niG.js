import{m as t}from"./index-r0dFAfgr.js";function r(){return t({url:"/api/menu/pool/getSelectableTree",method:"get"})}function s(e){return t({url:`/api/role/getTreeByRoleId/${e}`,method:"get"})}function n(){return t({url:"/api/menu/customer/getTree",method:"get"})}function a(e){return t({url:"/api/role/saveRole",method:"post",data:e})}function l(){return t({url:"/api/role/roles",method:"get"})}function u(e){return t({url:"/api/role/list",method:"get",params:e})}function i(e){return t({url:`/api/role/deleteRole/${e}`,method:"delete"})}function d(e){return t({url:"/api/role/assignRoleToUser",method:"post",data:e})}function g(e){return t({url:"/api/role/assignMenuToRole",method:"post",data:e})}function p(e){return t({url:`/api/role/getRoleIdByUserId/${e}`,method:"get"})}function m(e){return t({url:"/api/assign/users",method:"post",data:e})}const c=e=>t({url:"/api/tenantMenus/roleMenus",method:"get",params:e}),R=e=>t({url:"/api/tenantMenus/saveRoleMenus",method:"post",data:e});export{c as G,p as a,d as b,r as c,u as d,n as e,s as f,l as g,g as h,m as i,i as j,R as k,a as s};
