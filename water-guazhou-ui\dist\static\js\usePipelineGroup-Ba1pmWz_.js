import u from"./GroupLayer-DhhN3FyN.js";import{H as S,p as a,a as i,l as g,C as b}from"./MapView-DaoQedLH.js";import m from"./WFSLayer-1TtJp3nx.js";const C=d=>{const n=new u({title:d,visible:!1,layers:[]}),o=["rgb(125,0,255)","rgb(125,125,0)","rgb(125,125,255)","rgb(125,255,255)","rgb(255,0,0)","rgb(255,0,125)","rgb(255,0,255)","rgb(255,125,0)","rgb(255,125,125)","rgb(255,125,255)","rgb(125,0,0)","rgb(125,0,125)","rgb(125,125,0)","rgb(125,255,0)","rgb(125,255,125)"];return{init:e=>{e==null||e.when(()=>{e.map.add(n)})},addSubLayer:(e,r,s,t="1=1",l=o[0])=>{if(!e||r===void 0)return;const c=new S({url:window.SITE_CONFIG.GIS_CONFIG.gisService+window.SITE_CONFIG.GIS_CONFIG.gisPipeFeatureServiceFeatureServer+"/"+r,title:s||"其它",renderer:new a({symbol:new i({width:4,color:new g(l)})}),labelingInfo:[new b({labelPlacement:"center-along",labelExpression:"[MATERIAL] DN[DIAMETER]"})],definitionExpression:t});n.add(c)},addSubLayerByGeoServer:(e,r,s=o[0],t)=>{const l=new m({url:"/geoserver/anqing/wfs",id:"anqing:给水管线",name:"anqing:给水管线",title:e||"其它",renderer:new a({symbol:new i({width:4,color:new g(s)})}),labelingInfo:[new b({labelPlacement:"center-along",labelExpression:"[MATERIAL] DN[DIAMETER]"})],definitionExpression:t});n.add(l)},destroy:()=>{n.layers.removeAll(),n.destroy()},genSql:(e,r)=>r?`${e} = '${r}'`:`${e} is null`,genColor:e=>o[e%o.length]}};export{C as u};
