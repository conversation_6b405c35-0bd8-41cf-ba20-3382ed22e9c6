<template>
  <div class="gwgk-info">
    <RoundTargetItem
      v-for="(item, i) in state.info"
      :key="i"
      :config="item"
      :class="item.className"
    />
    <img
      width="220"
      class="circle-image"
      src="../../imgs/compound.png"
      alt=""
    />
    <div class="prosale-ratio">
      <div class="value">
        0.00 %
      </div>
      <div class="text">
        产销差率
      </div>
    </div>
  </div>
  <ScrollList :data="state.data"></ScrollList>
</template>
<script lang="ts" setup>
import RoundTargetItem from './RoundTargetItem.vue'
import ScrollList from './ScrollList.vue'

const state = reactive<{
  info: ITargetItem[]
  data: any[]
}>({
  data: [
    { name: '管线长度', value: '-', scale: '-' },
    { name: '巡检次数', value: '30', scale: '100%' },
    { name: '抢修次数', value: '1135', scale: '100%' },
    { name: '阀门数量', value: '-', scale: '-' }
  ],
  info: [
    {
      value: '0',
      label: '综合漏损率(%)',
      scale: '0%',
      text: '同比',
      status: 'down',
      rows: [1, 3],
      className: 'total-loss-rate'
    },
    {
      value: '100.00',
      label: '漏损率(%)',
      scale: '0%',
      text: '同比',
      status: 'up',
      rows: [1, 3],
      className: 'loss-rate'
    }
  ]
})
</script>
<style lang="scss" scoped>
.gwgk-info {
  position: relative;
  width: 100%;
  height: 290px;
  .total-loss-rate {
    position: absolute;
    top: 20px;
    left: 0;
  }
  .loss-rate {
    position: absolute;
    top: 20px;
    right: 0;
  }
  .circle-image {
    position: absolute;
    width: 40%;
    top: 80px;
    left: 50%;
    transform: translateX(-50%);
  }
  .prosale-ratio {
    position: absolute;
    top: 150px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-direction: column;
    .value {
      font-size: 22px;
      line-height: 30px;
    }
    .text {
      font-size: 14px;
    }
  }
}
</style>
