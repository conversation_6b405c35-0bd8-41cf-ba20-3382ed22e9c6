package org.thingsboard.server.dao.sql.alarmV2;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.alarmV2.AlarmRuleSmart;

import java.util.List;

@Mapper
public interface AlarmRuleSmartMapper extends BaseMapper<AlarmRuleSmart> {
    List<AlarmRuleSmart> getList(@Param("stationIdList") List<String> stationIdList, @Param("attrList") List<String> attr);
}
