package org.thingsboard.server.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.thingsboard.server.dao.util.alicloud.AlibabaShortMessageSender;

@Configuration
public class SmsConfig {

    @Bean
    public AlibabaShortMessageSender messageSender(
            @Value("${alibaba.cloud.sms.accessKeyId}") String key,
            @Value("${alibaba.cloud.sms.accessKeySecret}") String secret
    ) {
        return new AlibabaShortMessageSender(key, secret);
    }
}
