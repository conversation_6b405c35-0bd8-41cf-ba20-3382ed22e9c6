<template>
  <teleport v-if="telport" :to="telport">
    <div v-if="state.marks?.length" class="infowindow-container">
      <ListWindow
        v-for="(pop, j) in state.marks"
        :key="j"
        :ref="'refPop' + pop.attributes.id"
        :view="props.view"
        :config="pop"
        @highlight="highlightPop"
        @more="handleMore"
        @back="getParentAreas"
      >
      </ListWindow>
    </div>
  </teleport>
  <Form ref="refForm" :config="FormConfig"></Form>
</template>
<script lang="ts" setup>
import TextSymbol from '@arcgis/core/symbols/TextSymbol.js';
import Graphic from '@arcgis/core/Graphic.js';
import Polygon from '@arcgis/core/geometry/Polygon.js';
import Point from '@arcgis/core/geometry/Point.js';
import {
  bindViewClick,
  bindViewDblClick,
  extentTo,
  getGraphicLayer,
  setSymbol
} from '@/utils/MapHelper';
import CountCardVue from './components/CountCard.vue';
import CardGroupVue from './components/CardGroup.vue';
import {
  initLossRateAndTrendLineChartOption,
  initLossRateLineChartOption,
  initWholeDayLiuliangPercentLineChartOption
} from './DMAConfig';
import {
  GetDMAChildPartitions,
  GetDMADashboard,
  GetParentPartition,
  GetPartitionTree
} from '@/api/dma/dmaAreaSetting';
import ListWindow from '../popup/ListWindow.vue';
import { useAppStore } from '@/store';
import { getStationImageUrl } from '@/utils/URLHelper';

const { proxy }: any = getCurrentInstance();
const refForm = ref<IFormIns>();
const props = defineProps<{
  view?: __esri.MapView;
  telport?: any;
}>();
const state = reactive<{
  supply: {
    prop: string;
    title: string;
    count: number;
    unit: string;
    img: string;
  }[];
  areaScopes: any[];
  marks: IArcMarkerProps[];
  setToViewingDetail: boolean;
  areaTree: any[];
  parentExtentTo: boolean;
}>({
  supply: [
    {
      prop: 'supplyTotal',
      title: '供水量',
      count: 0,
      unit: 'm³',
      img: getStationImageUrl('供水量.png')
    },
    {
      prop: 'saleTotal',
      title: '售水量',
      count: 0,
      unit: 'm³',
      img: getStationImageUrl('供水量 2.png')
    },
    {
      prop: 'difference',
      title: '产销差',
      count: 0,
      unit: 'm³',
      img: getStationImageUrl('供水量 3.png')
    },
    {
      prop: 'differenceRate',
      title: '产销差率',
      count: 0,
      unit: '%',
      img: getStationImageUrl('供水量 4.png')
    }
  ],
  areaScopes: [],
  marks: [],
  setToViewingDetail: false,
  areaTree: [],
  parentExtentTo: false
});
const staticState: {
  graphicsLayer?: __esri.GraphicsLayer;
  textLayer?: __esri.GraphicsLayer;
} = {};
const TableConfig = reactive<ITable>({
  dataList: [],
  indexVisible: true,
  columns: [
    { label: '区域名称', prop: 'partitionName' },
    { label: '流量(m³/h)', prop: 'nightMinFlow' },
    { label: '上报时间', prop: 'createTime' }
  ],
  pagination: {
    hide: true
  }
});
const FormConfig = reactive<IFormConfig>({
  gutter: 12,
  labelPosition: 'top',
  group: [
    {
      fieldset: {
        desc: '总览'
      },
      fields: [
        {
          type: 'component',
          style: { width: '100%' },
          field: 'overview',
          component: shallowRef(CountCardVue)
        }
      ]
    },
    {
      fieldset: {
        desc: '供水情况'
      },
      fields: [
        {
          type: 'component',
          field: 'supply',
          style: { width: '100%' },
          component: shallowRef(CardGroupVue)
        }
      ]
    },
    {
      id: 'lossRate',
      fieldset: {
        desc: '漏失率统计'
      },
      fields: [
        {
          type: 'vchart',
          style: () => ({
            width: '100%',
            height: '200px',
            backgroundColor: useAppStore().isDark ? '#1A293C' : '#c7c7c7'
          }),
          option: null
        }
      ]
    },
    {
      id: 'lossRateAndProSale',
      fieldset: {
        desc: '漏损率和产销差趋势'
      },
      fields: [
        {
          type: 'vchart',
          option: null,
          style: () => ({
            width: '100%',
            height: '200px',
            backgroundColor: useAppStore().isDark ? '#1A293C' : '#c7c7c7'
          })
        }
      ]
    },
    {
      fieldset: {
        desc: '夜间最小流量'
      },
      fields: [{ type: 'table', config: TableConfig }]
    },
    {
      id: 'wholeDayLiuliangPercent',
      fieldset: {
        desc: '全天流量占比'
      },
      fields: [
        {
          type: 'vchart',
          style: () => ({
            width: '100%',
            height: '200px',
            backgroundColor: useAppStore().isDark ? '#1A293C' : '#c7c7c7'
          }),
          option: null
        }
      ]
    }
  ],
  defaultValue: {
    supply: state.supply
  }
});
const getLeftPanelData = async (partitionId: string) => {
  const res = await GetDMADashboard(partitionId);
  resetRightData(res.data);
};
const resetRightData = (data?: any) => {
  const supply = data?.supply || {};
  state.supply.map((item) => supply[item.prop]);
  if (refForm.value) {
    refForm.value.dataForm = {
      supply: state.supply,
      overview: data?.overview || {}
    };
  }
  TableConfig.dataList = data?.nightMinFlow || [];
  setChartOptions(data);
};
const initMapView = async () => {
  if (!props.view) return;
  staticState.graphicsLayer = getGraphicLayer(props.view, {
    id: 'dma-partition',
    title: 'DMA分区'
  });
  staticState.textLayer = getGraphicLayer(props.view, {
    id: 'dma-partition-poi',
    title: 'DMA分区注记'
  });
  staticState.graphicsLayer?.removeAll();
  staticState.textLayer?.removeAll();
  state.marks = [];
  getAreaTree();
  // 单击
  // 当单击文本时，弹窗，更新右侧数据
  // 当单击区域时，查看子级区域
  bindViewClick(props.view, async (res) => {
    if (!res.results.length) return;
    const item = res.results[0];
    if (item.type === 'graphic') {
      const attributes = item.graphic?.attributes;
      if (attributes.type === 'dma_text') {
        getLeftPanelData(attributes.result?.id);
        await extentTo(props.view, item.graphic.geometry.extent, true);

        openPop(attributes.result);
      } else if (attributes.type === 'dma_area') {
        getLeftPanelData(attributes.result?.id);
        await getChildAreas(attributes.result?.id);
        await extentTo(props.view, item.graphic.geometry.extent, true);
      }
    }
  });
  // 双击返回
  bindViewDblClick(props.view, (res) => {
    if (!res.results.length) return;
    res.results.map((item) => {
      if (item.type === 'graphic') {
        const attributes = item.graphic?.attributes;
        if (attributes.type === 'dma_text' || attributes.type === 'dma_area') {
          getParentAreas(attributes?.result);
        }
      }
    });
  });
};
const handleMore = (attributes: any) => {
  getChildAreas(attributes?.id, true);
};
const openPop = (data: any) => {
  const pop = proxy.$refs['refPop' + data.id];
  pop?.length && pop[0]?.open();
};

const getParentAreas = async (result) => {
  if (!result?.id) return;
  const res = await GetParentPartition(result.id);
  if (res.data?.length) {
    if (result.pid) {
      getLeftPanelData(result.pid);
    }
    state.marks = [];
    state.parentExtentTo = true;
    staticState.graphicsLayer?.removeAll();
    staticState.textLayer?.removeAll();
    res.data?.map((item) => {
      const graphic = addAreaGraphic(item);
      graphic &&
        state.parentExtentTo &&
        extentTo(props.view, graphic.geometry.extent, true);
    });
  }
};
const getChildAreas = async (pId, zoomTo?: boolean) => {
  if (!pId) return;
  const res = await GetDMAChildPartitions(pId);
  if (res.data?.length) {
    state.marks = [];
    staticState.graphicsLayer?.removeAll();
    staticState.textLayer?.removeAll();
    res.data?.map((item) => {
      const graphic = addAreaGraphic(item);
      zoomTo && graphic && extentTo(props.view, graphic.geometry.extent, true);
    });
  }

  return res.data || [];
};
const highlightPop = (pop: IArcMarkerProps) => {
  pop.highLight = true;
};
const addAreaGraphic = (result) => {
  if (!result) return;
  const ring =
    result.position instanceof Array
      ? result.position
      : typeof result.position === 'string'
        ? (result.position && JSON.parse(result.position)) || []
        : [];
  if (!ring) return;
  if (ring.length < 3) return;
  ring.push(ring[0]);
  const color = [
    Math.random() * 255,
    Math.random() * 255,
    Math.random() * 255,
    0.2
  ];
  const ringMoca = ring.map((item) => {
    const point = new Point({
      longitude: item[0],
      latitude: item[1],
      spatialReference: props.view?.spatialReference
    });
    return [point.x, point.y];
  });
  const geometry = new Polygon({
    rings: [ringMoca],
    spatialReference: props.view?.spatialReference
  });
  const symbol = setSymbol('polygon', {
    color,
    outlineColor: '#E3E709',
    outlineWidth: 1
  });
  const graphic = new Graphic({
    geometry,
    symbol,
    attributes: {
      type: 'dma_area',
      result
    }
  });
  staticState.graphicsLayer?.add(graphic);
  const textSymbol = new TextSymbol({
    text: result.name,
    color: '#333333',
    haloColor: '#ffffff',
    haloSize: 4,
    font: {
      size: '7px',
      weight: 'bold'
    }
  });

  const textGraphic = new Graphic({
    geometry,
    symbol: textSymbol,
    attributes: {
      type: 'dma_text',
      result
    }
  });
  staticState.textLayer?.add(textGraphic);
  const point = new Point(textGraphic?.geometry.extent.center);
  state.marks.push({
    showMore: true,
    showBack: true,
    title: result.name,
    visible: true,
    x: point.longitude || 0,
    y: point.latitude || 0,
    bgColor: '#1e2f44',
    attributes: {
      id: result.id,
      values: [
        {
          label: '昨日总供水量',
          value: result.supplyTotal || 0,
          unit: 'm³'
        },
        {
          label: '昨日流量占比',
          value: result.supplyRate || 0,
          unit: '%'
        },
        {
          label: '总用户数',
          value: result.households || 0,
          unit: '户'
        },
        {
          label: '夜间最小流量',
          value: result.nightMinFlow || 0,
          unit: 'm³/h'
        },
        {
          label: '产销差率',
          value: result.differenceRate || 0,
          unit: '%'
        },
        {
          label: '漏失率',
          value: result.lossRate || 0,
          unit: '%'
        }
      ]
    }
  });
  return graphic;
};
const getAreaTree = async () => {
  const res = await GetPartitionTree();
  state.areaTree = res.data || [];
  state.areaTree.map((item) => {
    getChildAreas(item.id, true);
  });
  getLeftPanelData(state.areaTree[0]?.id);
};
const setChartOptions = (data?: any) => {
  const lossRate = FormConfig.group.find((item) => item.id === 'lossRate')
    ?.fields[0] as IFormVChart;
  lossRate.option = initLossRateLineChartOption(data?.lossRate || []);
  const lossRateAndProSale = FormConfig.group.find(
    (item) => item.id === 'lossRateAndProSale'
  )?.fields[0] as IFormVChart;
  lossRateAndProSale.option = initLossRateAndTrendLineChartOption(
    data?.lossRate || []
  );
  const wholeDayLiuliangPercent = FormConfig.group.find(
    (item) => item.id === 'wholeDayLiuliangPercent'
  )?.fields[0] as IFormVChart;
  wholeDayLiuliangPercent.option = initWholeDayLiuliangPercentLineChartOption(
    data?.pressure || []
  );
};
onMounted(() => {
  nextTick(() => {
    resetRightData();
  });
  initMapView();
});
onBeforeUnmount(() => {
  staticState.graphicsLayer &&
    props.view?.map.remove(staticState.graphicsLayer);
  staticState.textLayer && props.view?.map.remove(staticState.textLayer);
});
</script>
<style lang="scss" scoped></style>
