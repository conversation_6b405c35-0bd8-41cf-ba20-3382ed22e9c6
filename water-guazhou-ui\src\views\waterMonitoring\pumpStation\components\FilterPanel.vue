<template>
  <div class="filter-panel">
    <el-form :model="form" inline>
      <el-form-item label="数据来源:">
        <el-select v-model="form.stationIds" placeholder="设备组" multiple>
          <el-option
            v-for="item in stationOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      
      <el-form-item label="分析周期:">
        <el-select v-model="form.timeGranularity" placeholder="日">
          <el-option label="小时" value="hour" />
          <el-option label="日" value="day" />
          <el-option label="周" value="week" />
          <el-option label="月" value="month" />
        </el-select>
      </el-form-item>
      
      <el-form-item label="泵站类型:">
        <el-radio-group v-model="form.pumpType">
          <el-radio label="electric">电水泵</el-radio>
          <el-radio label="water">中水泵</el-radio>
        </el-radio-group>
      </el-form-item>
      
      <el-form-item label="方案名称:">
        <el-input v-model="form.schemeName" placeholder="输入方案" />
      </el-form-item>
      
      <el-form-item label="方案名称:">
        <el-input v-model="form.schemeNameAlt" placeholder="输入方案" />
      </el-form-item>
      
      <el-form-item>
        <el-button type="primary" :loading="loading" @click="handleQuery">查询</el-button>
        <el-button @click="handleReset">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'

const props = defineProps({
  loading: {
    type: Boolean,
    default: false
  },
  stationOptions: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['query', 'reset'])

const form = reactive({
  stationIds: [],
  timeGranularity: 'day',
  pumpType: 'electric',
  schemeName: '',
  schemeNameAlt: ''
})

const handleQuery = () => {
  emit('query', { ...form })
}

const handleReset = () => {
  form.stationIds = []
  form.timeGranularity = 'day'
  form.pumpType = 'electric'
  form.schemeName = ''
  form.schemeNameAlt = ''
  emit('reset')
}
</script>

<style scoped lang="scss">
.filter-panel {
  .el-form {
    display: flex;
    flex-wrap: wrap;
    
    .el-form-item {
      margin-right: 20px;
      margin-bottom: 10px;
    }
  }
}
</style>
