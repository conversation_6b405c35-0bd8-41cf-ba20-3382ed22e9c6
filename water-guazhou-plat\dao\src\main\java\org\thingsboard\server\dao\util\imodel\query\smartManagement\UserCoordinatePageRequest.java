package org.thingsboard.server.dao.util.imodel.query.smartManagement;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartManagement.UserCoordinate;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;

@Getter
@Setter
public class UserCoordinatePageRequest extends AdvancedPageableQueryEntity<UserCoordinate, UserCoordinatePageRequest> {
    // 用户名，模糊
    private String userName;

    // 部门id
    private String departmentId;

    // 用户类型Id
    private String userTypeId;

    // 状态 0-未使用 1-离线 2-在线
    private Integer status;

    // 在线判定的阈值，当前是1小时内上报过坐标即视为在线
    private final int onlineThreshold = 1;

}
