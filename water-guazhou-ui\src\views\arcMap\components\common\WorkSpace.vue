<template>
  <div class="workspace">
    <Form
      ref="refForm"
      :config="FormConfig"
    />
    <div class="table-box">
      <FormTable :config="TableConfig"></FormTable>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { IFormIns } from '@/components/type'
import { AddBookMark, DeleteBookMarks, GetBookMarks } from '@/api/mapservice/workspace'
import { SLConfirm, SLMessage } from '@/utils/Message'
import { extentTo } from '@/utils/MapHelper'

const props = defineProps<{
  view?: __esri.MapView
}>()
const refForm = ref<IFormIns>()
const TableConfig = reactive<ITable>({
  dataList: [],
  rowKey: 'id',
  columns: [
    { label: '名称', prop: 'markname' },
    { label: '创建时间', prop: 'createdate' }
  ],
  // handleSelectChange: rows => {
  //   TableConfig.selectList = rows || []
  // },
  singleSelect: true,
  handleRowDbClick: extent => extentTo(props.view, {
    xmin: extent?.xmin,
    ymin: extent?.ymin,
    xmax: extent?.xmax,
    ymax: extent?.ymax
  }),
  select: (row:any, isChecked?:boolean) => {
    TableConfig.selectList = isChecked ? [row] : []
  },
  pagination: {
    layout: 'total,sizes, jumper',
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page
      TableConfig.pagination.limit = size
      refreshData()
    }
  }
})
const FormConfig = reactive<IFormConfig>({
  labelPosition: 'top',
  gutter: 12,
  group: [
    {
      fields: [
        { type: 'input', field: 'name', label: '', clearable: false, prepend: '名称', placeholder: '请输入当前工作空间的名称' },
        { type: 'btn-group',
          btns: [
            { perm: true,
              text: '添加工作空间',
              type: 'success',
              styles: {
                width: '100%'
              },
              click: async (row:any) => {
                const name = row?.name
                if (!name) {
                  SLMessage.warning('请输入名称')
                  return
                }
                const extent = props.view?.extent
                const res = await AddBookMark({
                  markname: name,
                  remark: name,
                  xmin: extent?.xmin || 0,
                  ymin: extent?.ymin || 0,
                  xmax: extent?.xmax || 0,
                  ymax: extent?.ymax || 0
                })
                if (res.data.code === 10000) {
                  SLMessage.success(res.data.message || '添加成功')
                  refreshData()
                } else {
                  SLMessage.error(res.data.message || '添加失败')
                }
              }
            }
          ]
        }
      ]
    },
    {
      fieldset: {
        desc: '我的工作空间',
        right: [
          {
            style: {
              marginLeft: 'auto'
            },
            items: [
              {
                type: 'btn-group',

                btns: [
                  { perm: true,
                    type: 'danger',
                    size: 'small',
                    disabled: () => !TableConfig.selectList?.length,
                    text: '删除',
                    click: () => {
                      SLConfirm('确定删除？', '提示信息').then(async () => {
                        try {
                          const id = TableConfig.selectList?.length && TableConfig.selectList[0].id
                          if (id) {
                            const res = await DeleteBookMarks(id)
                            if (res.data.code === 10000) {
                              SLMessage.success(res.data?.message)
                              refreshData()
                            } else {
                              SLMessage.error(res.data?.message || '删除失败')
                            }
                          } else {
                            SLMessage.error('参数错误')
                          }
                        } catch (error) {
                          SLMessage.error('系统错误')
                        }
                      }).catch(() => {
                      //
                      })
                    }
                  }
                ] }
            ]
          }
        ]
      },
      fields: [
        // {
        //   type: 'table',
        //   config: TableConfig,
        //   style: {
        //     height: '100%'
        //   } }
      ]
    }
  ]
})
const refreshData = async () => {
  const res = await GetBookMarks({
    pagenumber: TableConfig.pagination.page || 1,
    pagesize: TableConfig.pagination.limit || 20
  })
  TableConfig.dataList = res.data?.result?.rows || []
  TableConfig.pagination.total = res.data.result?.totalnumberofrecords || 0
}

onMounted(() => {
  refreshData()
})
</script>
<style lang="scss">
.workspace{
  height: 100%;
}
.table-box{
  height: calc(100% - 170px);
}
</style>
