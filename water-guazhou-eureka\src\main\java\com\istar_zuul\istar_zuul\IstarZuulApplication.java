package com.istar_zuul.istar_zuul;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.netflix.eureka.server.EnableEurekaServer;
import org.springframework.cloud.netflix.zuul.EnableZuulProxy;

@EnableEurekaServer
@SpringBootApplication
@EnableZuulProxy
@EnableDiscoveryClient
public class IstarZuulApplication {

    public static void main(String[] args) {
        SpringApplication.run(IstarZuulApplication.class, args);
    }



}
