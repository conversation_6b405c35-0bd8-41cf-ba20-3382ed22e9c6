package org.thingsboard.server.dao.model.sql.smartManagement.plan;

public class GeneralTaskProcessingAndCompleteCount {
    private final Integer processingCount;

    private final Integer completeCount;

    public GeneralTaskProcessingAndCompleteCount(Integer processingCount, Integer completeCount) {
        this.processingCount = processingCount;
        this.completeCount = completeCount;
    }

    public Integer getProcessingCount() {
        return processingCount;
    }

    public Integer getCompleteCount() {
        return completeCount;
    }
}
