import { RouteRecordRaw } from 'vue-router';

export const constantRoutes: RouteRecordRaw = {
  path: 'constantRoutes',
  name: 'constantRoutes',
  meta: { title: '默认路由', icon: 'iconfont icon-camera' },
  children: [
    {
      path: 'defaultRoute',
      name: 'defaultRoute',
      meta: { title: '默认路由', icon: 'iconfont icon-camera' },
      children: [
        {
          path: 'defaultRoute_GDFP',
          name: 'defaultRoute_GDFP',
          meta: { title: '工单分派', icon: 'iconfont icon-camera' },
          component: () => import('@/views/workorder/OrderDiapatch.vue')
        },
        {
          path: 'defaultRoute_YWCL',
          name: 'defaultRoute_YWCL',
          meta: { title: '由我处理', icon: 'iconfont icon-camera' },
          component: () => import('@/views/workorder/MyDeals.vue')
        },
        {
          path: 'defaultRoute_BJZX',
          name: 'defaultRoute_BJZX',
          meta: { title: '报警中心', icon: 'iconfont icon-camera' },
          component: () => import('@/views/shuichang/strategy/statistics.vue')
        },
        {
          path: 'defaultRoute_SYGK',
          name: 'defaultRoute_SYGK',
          meta: { title: '水源地运行概况', icon: 'iconfont icon-camera' },
          component: () => import('@/views/headwatersManage/statisticalAnalysis/operationOverview/index.vue')
        }
      ]
    }
  ]
};
