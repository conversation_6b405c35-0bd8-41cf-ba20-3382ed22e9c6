import Point from '@arcgis/core/geometry/Point.js'
import PictureMarkerSymbol from '@arcgis/core/symbols/PictureMarkerSymbol.js'
import { GetLatestUserCoords } from '@/api/patrol'
import { createGraphic, getGraphicLayer } from '@/utils/MapHelper'
import useHighLight from './useHighLight'
import UserLocatePop from '@/views/arcMap/components/UserLocatePop.vue'
import { getStationImageUrl } from '@/utils/URLHelper'

interface IUserCoordsParams {
  userName?: string
  departmentId: string
  userTypeId?: string
  status?: string
  page?: number
  size?: number
  fromTime?: string
  toTime?: string
}
export const useUserLocation = (
  highlightCallBack?: (graphic?: __esri.Graphic) => void
) => {
  let graphicsLayer: __esri.GraphicsLayer | undefined
  const highlight = useHighLight()
  const _getLatestUserCoords = async (query: IUserCoordsParams) => {
    const res = await GetLatestUserCoords({
      page: 1,
      size: 999999,
      ...query
    })
    return {
      data: res.data?.data?.data || [],
      total: res.data?.data?.total || 0
    }
  }
  const getLatestXunJianRenYuanCoords = (
    query: Omit<IUserCoordsParams, 'userTypeId'>
  ) => {
    return _getLatestUserCoords({
      ...query,
      userTypeId: 'XUNJIANRENYUAN'
    })
  }
  const getLatestCHAOBIAORENYUANCoords = (
    query: Omit<IUserCoordsParams, 'userTypeId'>
  ) => {
    return _getLatestUserCoords({
      ...query,
      userTypeId: 'CHAOBIAORENYUAN'
    })
  }
  const getLatestQIANGXIURENYUANCoords = (
    query: Omit<IUserCoordsParams, 'userTypeId'>
  ) => {
    return _getLatestUserCoords({
      ...query,
      userTypeId: 'QIANGXIURENYUAN'
    })
  }
  const generatePops = (
    view?: __esri.MapView,
    data?: {
      id: string
      userId: string
      coordinate?: string
      createTime?: string
      tenantId?: string
      userName: string
    }[],
    addMarks?: boolean,
    extentToMark?: (id: string) => void
  ) => {
    const pops: IArcPopConfig[] = []
    if (addMarks) {
      graphicsLayer = getGraphicLayer(view, {
        id: 'user-coords',
        title: '人员'
      })
      highlight.removeHoverHighLight()
      highlight.bindHoverHighLight(view, graphicsLayer, highlightCallBack)
    }

    const points = data?.map(item => {
      const coords = item.coordinate?.split(',')
      const lon = parseFloat(coords?.[0] || '0')
      const lat = parseFloat(coords?.[1] || '0')
      const pointGeometry = new Point({
        longitude: lon,
        latitude: lat,
        spatialReference: view?.spatialReference
      })
      const point = createGraphic({
        geometry: pointGeometry,
        symbol: new PictureMarkerSymbol({
          url: getStationImageUrl('大用户表.png'),
          width: 20,
          height: 25,
          yoffset: 12
        }),
        attributes: item
      })
      const pop: IArcPopConfig = {
        id: item.userId,
        visible: false,
        title: item.userName,
        x: pointGeometry.x,
        y: pointGeometry.y,
        offsetY: -40,
        attributes: {
          row: item,
          id: item.userId
        },
        customComponent: shallowRef(UserLocatePop),
        customConfig: {
          ...item,
          extentTo: extentToMark
        },
        symbolConfig: {
          url: getStationImageUrl('大用户表.png')
        }
      }
      pops.push(pop)
      return point
    }) || []
    if (!addMarks) return pops
    graphicsLayer?.removeAll()
    graphicsLayer?.addMany(points)
    return pops
  }
  /**
   * 从地图中查询人员点
   * @param userId
   * @returns
   */
  const getGraphic = (userId: string) => {
    const graphic = graphicsLayer?.graphics.find(
      item => item.attributes.userId === userId
    )
    return graphic
  }
  const destroy = () => {
    graphicsLayer?.removeAll()
  }
  onBeforeUnmount(() => {
    destroy()
  })
  return {
    destroy,
    highlight,
    generatePops,
    getGraphic,
    _getLatestUserCoords,
    getLatestCHAOBIAORENYUANCoords,
    getLatestXunJianRenYuanCoords,
    getLatestQIANGXIURENYUANCoords
  }
}
