package org.thingsboard.server.dao.notify;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.DTO.CountObjDTO;
import org.thingsboard.server.dao.model.request.SystemNotifyListRequest;
import org.thingsboard.server.dao.model.sql.notify.SystemNotify;
import org.thingsboard.server.dao.sql.notify.SystemNotifyMapper;
import org.thingsboard.server.dao.sql.notify.SystemNotifyRepository;
import org.thingsboard.server.dao.user.UserService;
import org.thingsboard.server.dao.util.RedisUtil;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class SystemNotifyServiceImpl implements SystemNotifyService {

    @Autowired
    private SystemNotifyRepository systemNotifyRepository;

    @Autowired
    private SystemNotifyMapper systemNotifyMapper;

    @Autowired
    private UserService userService;

    @Override
    public PageData<SystemNotify> findList(SystemNotifyListRequest request, TenantId tenantId) {
        Page<SystemNotify> pageRequest = new Page<>(request.getPage(), request.getSize());
        IPage<SystemNotify> pageResult = systemNotifyMapper.findList(pageRequest, request);

        return new PageData<>(pageResult.getTotal(), pageResult.getRecords());
    }

    @Override
    public void readAll(String type, String to, TenantId tenantId) {

        systemNotifyMapper.readAll(type, to, UUIDConverter.fromTimeUUID(tenantId.getId()));
    }

    @Override
    public Map<String, Integer> notifyCount(String to, String status, TenantId tenantId) {
        List<CountObjDTO> countList = systemNotifyMapper.notifyCount(to, status, UUIDConverter.fromTimeUUID(tenantId.getId()));

        Map<String, Integer> countMap = new HashMap<>();
        for (CountObjDTO count : countList) {
            countMap.put(count.getKey(), count.getCount());
        }

        return countMap;
    }

    @Override
    public void sendNotify(String type, DataConstants.SYSTEM_NOTIFY_TOPIC topic, String from, String to, String tenantId) {
        try {
            SystemNotify notify = new SystemNotify();
            notify.setCode(RedisUtil.nextId(DataConstants.REDIS_KEY.SYSTEM_NOTIFY, ""));
            notify.setType(type);
            notify.setTopic(topic.getValue());
            notify.setContent(topic.getMsg());
            notify.setTopicType(topic.getType());
            notify.setToUser(to);
            notify.setFromUser(from);
            notify.setTime(new Date());
            notify.setStatus(DataConstants.SYSTEM_NOTIFY_STATUS.UN_READ.getValue());
            notify.setTenantId(tenantId);

            // 保存通知
            systemNotifyMapper.insert(notify);

            // TODO 发送APP通知

        } catch (Exception e) {
            log.error("保存通知消息失败!");
        }
    }

    @Override
    public void readOne(String id, TenantId tenantId) {
        SystemNotify notify = systemNotifyMapper.selectById(id);
        if (notify != null && !DataConstants.SYSTEM_NOTIFY_STATUS.HAVE_READ.getValue().equals(notify.getStatus())) {
            notify.setStatus(DataConstants.SYSTEM_NOTIFY_STATUS.HAVE_READ.getValue());
            systemNotifyMapper.updateById(notify);
        }
    }
}

