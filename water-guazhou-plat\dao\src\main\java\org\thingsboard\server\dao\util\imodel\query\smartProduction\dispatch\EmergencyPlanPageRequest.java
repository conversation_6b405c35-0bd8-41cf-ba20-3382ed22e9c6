package org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartProduction.dispatch.EmergencyPlan;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;

@Getter
@Setter
public class EmergencyPlanPageRequest extends AdvancedPageableQueryEntity<EmergencyPlan, EmergencyPlanPageRequest> {
    // 预案名称，模糊
    private String name;

    // 所属站点或所属项目
    private String stationId;

}
