package org.thingsboard.server.dao.model.sql;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;
import java.util.Date;

@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.APP_BOTTOM_MENU_TABLE_NAME)
@NoArgsConstructor
@AllArgsConstructor
public class AppBottomMenu {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.APP_BOTTOM_MENU_NAME)
    private String name;

    @Column(name = ModelConstants.APP_BOTTOM_MENU_MENU_KEY)
    private String menuKey;

    @Column(name = ModelConstants.CREATE_TIME)
    private Date createTime;

    @Column(name = ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;
}
