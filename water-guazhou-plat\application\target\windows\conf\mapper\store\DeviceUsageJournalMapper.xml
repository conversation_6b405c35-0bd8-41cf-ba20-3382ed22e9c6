<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.department.DeviceUsageJournalMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->id,
                           department_id,
                           user_id,
                           receive_time,
                           device_label_code,
                           remark,
                           tenant_id<!--@sql from device_usage_journal -->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.store.DeviceUsageJournal">
        <result column="id" property="id"/>
        <result column="department_id" property="departmentId"/>
        <result column="device_label_code" property="deviceLabelCode"/>
        <result column="user_id" property="userId"/>
        <result column="receive_time" property="receiveTime"/>
        <result column="remark" property="remark"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>
    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from device_usage_journal
        <where>
            <if test="departmentId != null">
                and department_id = #{departmentId}
            </if>
            <if test="deviceLabelCode != null">
                and device_label_code = #{deviceLabelCode}
            </if>
            <if test="userId != null">
                and user_id = #{userId}
            </if>
            <if test="receiveTime != null">
                and receive_time = #{receiveTime}
            </if>
            <if test="remark != null">
                and remark = #{remark}
            </if>
            <if test="tenantId != null">
                and tenant_id = #{tenantId}
            </if>
            and tenant_id = #{tenantId}
        </where>
    </select>

    <update id="update">
        update device_usage_journal
        <set>
            <if test="departmentId != null">
                department_id = #{departmentId},
            </if>
            <if test="deviceLabelCode != null">
                device_label_code = #{deviceLabelCode},
            </if>
            <if test="userId != null">
                user_id = #{userId},
            </if>
            <if test="receiveTime != null">
                receive_time = #{receiveTime},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
        </set>
        where id = #{id}
    </update>
</mapper>