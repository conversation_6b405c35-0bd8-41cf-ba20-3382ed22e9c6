import{m as t}from"./index-r0dFAfgr.js";function c(){return t({url:"/api/so/deviceType/tree",method:"get"})}function n(e){return t({url:"/api/so/deviceType",method:"post",data:e})}function r(e){return t({url:"/api/so/deviceType",method:"get",params:e})}function s(e){return t({url:`/api/so/deviceType/${e}`,method:"delete"})}function u(e){return t({url:"/api/so/device",method:"get",params:e})}function a(e){return t({url:"/api/so/device",method:"post",data:e})}function p(e){return t({url:`/api/so/device/${e}`,method:"delete"})}function d(e){return t({url:"/api/so/device",method:"delete",data:e})}function v(e,o){return t({url:`/api/so/project/${e}/device`,method:"post",data:o})}function l(e,o){return t({url:`/api/so/project/${e}/device`,method:"get",params:o})}function m(e){return t({url:"/api/so/deviceItem",method:"get",params:e})}function f(e,o){return t({url:`/api/so/constructionContract/${e}/device`,method:"post",data:o})}function h(e,o){return t({url:`/api/so/constructionContract/${e}/device`,method:"get",params:o})}function g(e,o){return t({url:`/api/so/constructionApply/${e}/device`,method:"post",data:o})}function D(e,o){return t({url:`/api/so/constructionApply/${e}/device`,method:"get",params:o})}export{r as a,a as b,p as c,s as d,d as e,u as f,c as g,m as h,v as i,l as j,f as k,h as l,g as m,D as n,n as p};
