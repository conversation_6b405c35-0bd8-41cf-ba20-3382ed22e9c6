import{d as B,c as k,r as g,b as c,Q as E,g as J,h as Q,F as q,q as V,i as C,_ as $,X as z,C as U}from"./index-r0dFAfgr.js";import{s as X}from"./FeatureHelper-Da16o0mu.js";import{b as H}from"./GPHelper-fLrvVD-A.js";import{e as K,i as W}from"./IdentifyHelper-RJWmLn49.js";import{g as Y,a as _,e as Z}from"./LayerHelper-Cn-iiqxI.js";import{a as ee,i as te}from"./QueryHelper-ILO3qZqg.js";import{s as S}from"./ToolHelper-BiiInOzB.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./pe-B8dP0-Ut.js";import"./geometryEngineBase-BhsKaODW.js";import re from"./RightDrawerMap-D5PhmGFO.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./widget-BcWKanF2.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./identify-4SBo5EZk.js";import"./scaleUtils-DgkF6NQH.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./pipe-nogVzCHG.js";import"./executeForIds-BLdIsxvI.js";import"./project-DUuzYgGl.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./ExportImageParameters-BiedgHNY.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./ArcView-DpMnCY82.js";import"./ViewHelper-BGCZjxXH.js";import"./fieldconfig-Bk3o1wi7.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./ArcStationWarning-BF9YrSzF.js";/* empty css                         */import"./useWaterPoint-Bv0z6ym6.js";import"./DateFormatter-Bm9a68Ax.js";import"./zhandian-YaGuQZe6.js";import"./useStation-DJgnSZIA.js";import"./DrawerBox-CLde5xC8.js";import"./SideDrawer-CBntChyn.js";import"./PipeDetail-CTBPYFJW.js";import"./Search-NSrhrIa_.js";import"./FormTableColumnFilter-BT7pLXIC.js";import"./config-fy91bijz.js";import"./ArcBRTools-BO92yznB.js";import"./PipeLength.vue_vue_type_script_setup_true_lang-BZfUsvDf.js";import"./arcWidgetButton-0glIxrt7.js";import"./StatisticsHelper-D-s_6AyQ.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./wfsUtils-DXofo3da.js";import"./usePipelineGroup-Ba1pmWz_.js";import"./GroupLayer-DhhN3FyN.js";import"./lazyLayerLoader-DbM9sT1W.js";import"./WFSLayer-1TtJp3nx.js";import"./clientSideDefaults-VQhQaYxh.js";import"./QueryEngineCapabilities-Dk3NJwmm.js";import"./wfsUtils-Du031XaC.js";import"./geojson-MBFu2-HZ.js";import"./xmlUtils-CtUoQO7q.js";import"./ListWindow-WS05QqV0.js";import"./PopLayout-BP55MvL7.js";import"./PoiSearchV2-D7yNeLuv.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./URLHelper-B9aplt5w.js";import"./useLayerList-DmEwJ-ws.js";import"./useScaleBar-Beed-z91.js";const ae=B({__name:"ConnectAnalys",setup(ie){const w=k(),h=k(),a=g({layerIds:[],layerInfos:[],currentOperate:"",tabs:[]}),e={},d=g({columns:[{label:"管线类型",prop:"layerName"},{label:"管线编号",prop:"value",formatter:t=>{var r;return(r=t.attributes)==null?void 0:r.OBJECTID}}],dataList:[],pagination:{hide:!0}}),y=g({data:[],columns:[]}),x=g({labelPosition:"top",gutter:12,group:[{fieldset:{desc:"选取管线"},fields:[{type:"btn-group",btns:[{perm:!0,type:"warning",styles:{width:"100%"},text:()=>a.currentOperate==="picking"?"正在选取管线":"点击选择管线",loading:()=>a.currentOperate==="picking",disabled:()=>a.currentOperate==="analysing",click:()=>P()}]},{type:"table",label:"所选管线数据概览",style:{height:"80px"},config:d},{type:"checkbox",field:"usePreventLayer",options:[{label:"使用障碍图层",value:"usePreventLayer"}]}]},{fieldset:{desc:"执行分析"},fields:[{type:"btn-group",btns:[{perm:!0,styles:{width:"100%"},text:()=>a.currentOperate==="analysing"?"正在分析":"开始分析",loading:()=>a.currentOperate==="analysing",disabled:()=>a.currentOperate==="analysing"||a.currentOperate==="detailing"||a.currentOperate==="picking"||!d.dataList.length,click:()=>M()}]}]},{fieldset:{desc:"分析结果"},fields:[{type:"checkbox",label:"分析结果概览",field:"viewInMap",options:[{label:"地图显示",value:"viewInMap"}],onChange:t=>{e.resultLayer&&(e.resultLayer.visible=!!t.length)}},{type:"attr-table",config:y},{type:"btn-group",itemContainerStyle:{marginTop:"20px",marginBottom:"8px"},btns:[{perm:!0,text:()=>a.currentOperate==="detailing"?"正在查询":"查看详细结果",click:()=>G(),loading:()=>a.currentOperate==="detailing",disabled:()=>a.currentOperate!=="analysed"&&a.currentOperate!=="viewingdetail",styles:{width:"100%"}}]},{type:"btn-group",btns:[{perm:!0,type:"danger",text:"清除所有",disabled:()=>a.currentOperate==="analysing"||a.currentOperate==="detailing",click:()=>N(),styles:{width:"100%"}}]}]}],defaultValue:{viewInMap:["viewInMap"],usePreventLayer:["usePreventLayer"]}}),P=()=>{var t,r;e.view&&(S("crosshair"),a.currentOperate="picking",e.graphicLayer=Y(e.view,{id:"connect-analys",title:"连通性分析标注"}),(t=e.graphicLayer)==null||t.removeAll(),e.resultLayer&&e.view.map.remove(e.resultLayer),e.mapClick=(r=e.view)==null?void 0:r.on("click",async i=>{var p,s;(p=e.graphicLayer)==null||p.removeAll(),S("");try{await j(i)}catch{c.error("拾取失败")}(s=e.mapClick)!=null&&s.remove&&e.mapClick.remove(),e.mapClick=void 0,a.currentOperate="picked"}))},j=async t=>{var s,n;if(!e.view)return;const r=await K(window.SITE_CONFIG.GIS_CONFIG.gisService+window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService,W({tolerance:3,geometry:t.mapPoint,mapExtent:e.view.extent,width:e.view.width,layerIds:_(e.view)}));console.log(r.results);const i=(s=r.results)==null?void 0:s.find(o=>(console.log(o),o.feature.geometry.type==="polyline"));if(!i)return c.warning("没有查询到管线"),a.currentOperate="picked";const p=i&&[i]||[];if(d.dataList=p.map(o=>({attributes:o.feature.attributes,layerId:o.layerId,layerName:o.layerName})),e.identifyResult=i,i){const o=i.feature;o&&(o.symbol=X("polyline")),(n=e.graphicLayer)==null||n.add(o)}a.currentOperate="picked"},M=async()=>{var t,r,i,p,s,n,o,f;a.currentOperate="analysing",e.resultLayer&&((t=e.view)==null||t.map.remove(e.resultLayer));try{const b=(r=a.layerInfos.find(u=>u.layerid===e.identifyResult.layerId))==null?void 0:r.layerdbname,I=(i=e.identifyResult)==null?void 0:i.feature.attributes.OBJECTID,l=await H(b,I,!!((s=(p=w.value)==null?void 0:p.dataForm.usePreventLayer)!=null&&s.length));if(await l.waitForJobCompletion({statusCallback:u=>{e.jobid=u.jobId}}),l.jobStatus==="job-succeeded"){e.jobid=l.jobId;const u=await l.fetchResultMapImageLayer(l.jobId);e.resultLayer=u,e.resultLayer.title="连通性分析结果";const R=Z(e.view);(n=e.view)==null||n.map.add(e.resultLayer,R);const m=(await l.fetchResultData("summary")).value;(m==null?void 0:m.code)!==1e4?c.error(m.error):(e.resultSummary=(o=m==null?void 0:m.result)==null?void 0:o.summary,F((f=m==null?void 0:m.result)==null?void 0:f.summary));const L=e.resultSummary.layersummary.map(O=>({label:O.layername,name:O.layername}));await v(L,0),a.tabs=L}else l.jobStatus==="job-cancelled"?c.info("已取消分析"):l.jobStatus==="job-cancelling"?c.info("任务正在取消"):l.jobStatus==="job-failed"&&c.info("分析失败，请联系管理员")}catch{c.error("系统错误"),a.currentOperate="picked"}a.currentOperate="analysed"},F=t=>{var r,i;if((r=t==null?void 0:t.layersummary)!=null&&r.length&&(i=t==null?void 0:t.layersummary)!=null&&i.length){const p={},s=[];t.layersummary.forEach(n=>{var o;n.geometrytype==="esriGeometryPoint"?(p[n.layerdbname]=n.count+"个",s.push([{label:n.layername,prop:n.layerdbname}])):(p[n.layerdbname]=(((o=n.length)==null?void 0:o.toFixed(2))||0)+"米",s.push([{label:n.layername,prop:n.layerdbname}]))}),y.data=p,y.columns=s}},G=async()=>{var t;try{if(!a.tabs.length){c.warning("暂无详细信息");return}(t=h.value)==null||t.refreshDetail(a.tabs)}catch(r){c.error("查询失败"),console.log(r)}},v=async(t,r)=>{if(r<t.length){const i=t[r];i.data=await A(i.name),r<t.length-1&&await v(t,++r)}},A=async t=>{var r,i,p,s,n;try{const o=(p=(i=(r=e.resultSummary)==null?void 0:r.layersummary)==null?void 0:i.find(l=>l.layername===t))==null?void 0:p.geometrytype,f=o==="esriGeometryPolyline"?1:o==="esriGeometryPoint"?0:-1;return((n=(await ee((((s=e.resultLayer)==null?void 0:s.url)||"")+"/"+f,te({where:"layername='"+t+"'",outFields:["sourceoid"],returnGeometry:!1}))).features)==null?void 0:n.map(l=>l.attributes.sourceoid))||[]}catch(o){return console.log(o),[]}},N=()=>{var t,r,i,p;(t=e.graphicLayer)==null||t.removeAll(),e.resultLayer&&((r=e.view)==null||r.map.remove(e.resultLayer)),e.resultLayer&&((i=e.view)==null||i.map.remove(e.resultLayer)),e.identifyResult=[],y.data={},y.columns=[],d.dataList=[],(p=e.mapClick)==null||p.remove(),e.mapClick=void 0},T=async()=>{var r,i;a.layerIds=_(e.view,void 0,void 0,"消防栓");const t=await z(a.layerIds);a.layerInfos=((i=(r=t.data)==null?void 0:r.result)==null?void 0:i.rows)||[]},D=t=>{e.view=t,T()};return E(()=>{var t,r;(t=e.graphicLayer)==null||t.removeAll(),(r=e.mapClick)==null||r.remove(),e.mapClick=void 0}),(t,r)=>{const i=$;return J(),Q(re,{ref_key:"refMap",ref:h,title:"连通性分析","full-content":!0,onMapLoaded:D,onDetailRefreshed:r[0]||(r[0]=p=>C(a).currentOperate="viewingdetail")},{default:q(()=>[V(i,{ref_key:"refForm",ref:w,config:C(x)},null,8,["config"])]),_:1},512)}}}),yr=U(ae,[["__scopeId","data-v-5d5c677a"]]);export{yr as default};
