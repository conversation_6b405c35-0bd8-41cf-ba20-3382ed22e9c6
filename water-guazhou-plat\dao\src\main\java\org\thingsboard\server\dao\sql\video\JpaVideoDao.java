package org.thingsboard.server.dao.sql.video;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.model.sql.VideoEntity;
import org.thingsboard.server.dao.util.OracleUtil;
import org.thingsboard.server.dao.util.imodel.query.video.VideoMonitoringPageRequest;
import org.thingsboard.server.dao.video.VideoDao;

import javax.persistence.criteria.Predicate;
import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/2/26 15:05
 */
@Service
@Transactional
public class JpaVideoDao implements VideoDao {


    @Autowired
    private VideoRepository videoRepository;

    @Autowired
    private OracleUtil oracleUtil;

    @Override
    public VideoEntity save(VideoEntity videoEntity) {
        return videoRepository.save(videoEntity);
    }

    @Override
    public List<VideoEntity> findByProject(String projectId, String name, String isBind, String groupId) {
        if (StringUtils.isNotBlank(isBind) && "0".equals(isBind)) {
            return videoRepository.findByProjectIdAndNameLikeAndGroupIdIsNullOrGroupIdIsOrderByUpdateTimeDesc(projectId, name, "");
        } else {
            return videoRepository.findByProjectIdAndNameLikeAndGroupIdLikeOrderByUpdateTimeDesc(projectId, name, groupId);
        }
    }

    @Override
    public boolean delete(String id) {
        try {
            videoRepository.delete(id);
        } catch (Exception e) {
        }
        return true;
    }

    @Override
    public boolean deleteAll(List<String> ids) {
        ids.forEach(this::delete);
        return true;
    }

    @Override
    public List<VideoEntity> findByProjectAndType(String projectId, String type) {
        return videoRepository.findByProjectIdAndVideoTypeOrderByUpdateTimeDesc(projectId, type);
    }

    @Override
    public List<VideoEntity> findByVideoType(String type, TenantId tenantId) {
        return videoRepository.findByVideoTypeAndTenantIdOrderByUpdateTimeDesc(type, tenantId.getId().toString());
    }

    @Override
    public VideoEntity findById(String id) {
        return videoRepository.findOne(id);
    }

    @Override
    public List<VideoEntity> findAllByName(String name, String tenantId) {
        name = "%" + name + "%";
        return videoRepository.findAllByNameLikeAndTenantIdOrderByUpdateTimeDesc(name, tenantId);
    }

    @Override
    public List<VideoEntity> getAllBindList() {
        return videoRepository.findAllByGroupIdIsNotNullOrGroupIdEquals("");
    }

    @Override
    public Page<VideoEntity> findByPage(long page, long pageSize, VideoMonitoringPageRequest request) {
        // 创建 Spring Data JPA 的分页对象
        Sort sort = new Sort(Sort.Direction.DESC, "updateTime");
        PageRequest pageRequest = new PageRequest(
                (int) (page - 1),  // JPA 分页从 0 开始
                (int) pageSize,
                sort);

        // 使用 Specification 构建动态查询条件
        Specification<VideoEntity> spec = (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();

            // 添加租户ID条件（必须）
            if (StringUtils.isNotBlank(request.getTenantId())) {
                predicates.add(cb.equal(root.get("tenantId"), request.getTenantId()));
            }

            // 添加设备编号条件（精确匹配）
            if (StringUtils.isNotBlank(request.getSerialNumber())) {
                predicates.add(cb.equal(root.get("serialNumber"), request.getSerialNumber()));
            }

            // 添加设备名称条件（模糊匹配）
            if (StringUtils.isNotBlank(request.getName())) {
                predicates.add(cb.like(root.get("name"), "%" + request.getName() + "%"));
            }

            // 添加项目ID条件
            if (StringUtils.isNotBlank(request.getProjectId())) {
                predicates.add(cb.equal(root.get("projectId"), request.getProjectId()));
            }

            // 添加经度条件（精确匹配）
            if (StringUtils.isNotBlank(request.getLongitude())) {
                predicates.add(cb.equal(root.get("longitude"), request.getLongitude()));
            }

            // 添加纬度条件（精确匹配）
            if (StringUtils.isNotBlank(request.getLatitude())) {
                predicates.add(cb.equal(root.get("latitude"), request.getLatitude()));
            }

            // 添加位置条件（模糊匹配）
            if (StringUtils.isNotBlank(request.getLocation())) {
                predicates.add(cb.like(root.get("location"), "%" + request.getLocation() + "%"));
            }

            return cb.and(predicates.toArray(new Predicate[0]));
        };

        // 执行查询
        return videoRepository.findAll(spec, pageRequest);
    }
}
