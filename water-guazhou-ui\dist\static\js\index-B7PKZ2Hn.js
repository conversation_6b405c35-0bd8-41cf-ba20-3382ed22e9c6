import{_ as A}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as R}from"./CardTable-rdWOL4_6.js";import{_ as H}from"./CardSearch-CB_HNR-Q.js";import{m as $,d as G,c as h,s as U,r as m,o as J,g as b,n as S,p as y,q as n,F as l,i as s,G as x,aB as K,aJ as W,h as Q,bh as Y,aw as j,j as X,bF as Z,x as D,a9 as ee,J as te,c6 as ae,I as ne,H as se,c2 as le,K as oe,b7 as re,ak as ie,C as pe}from"./index-r0dFAfgr.js";import{_ as ce}from"./index-C9hz-UZb.js";import{_ as de}from"./index-BJ-QPYom.js";import{I as E}from"./common-CvK_P_ao.js";import{k as me,s as ue,b as _e}from"./emergencyDispatch-BKfr3jS6.js";import"./Search-NSrhrIa_.js";const fe={PENDING:"待发送",FAILURE:"失败",SUCCESS:"成功"},ge=v=>$({method:"get",url:"/api/msg/template/list",params:v}),he={class:"flex mag_bot_14"},be={class:"header-wrapper"},ye=G({__name:"index",setup(v){const _=h(),f=h(),I=h({filters:[{label:"发送内容",field:"content",type:"input"},{label:"开始时间",field:"fromTime",type:"date"},{label:"结束时间",field:"toTime",type:"date"},{type:"btn-group",btns:[{perm:!0,text:"查询",icon:E.QUERY,click:()=>d()},{type:"default",perm:!0,text:"重置",svgIcon:U(re),click:()=>{var e;(e=f.value)==null||e.resetForm(),d()}}]}]}),g=m({title:"内容模板",data:[],currentProject:{},isFilterTree:!0,treeNodeHandleClick:e=>{g.currentProject=e,a.content=e.content,a.templateId=e.id}}),c=m({title:"用户",data:[],currentProject:{},isFilterTree:!0,treeNodeHandleClick:e=>{if(e.layer!==3)return;c.currentProject=e,a.name.push({name:e.name,phone:e.phone});const t={};a.name=a.name.reduce((o,i)=>(t[i.name]||(t[i.name]=o.push(i)),o),[])}}),C=m({title:"添加收件人",dialogWidth:500,labelWidth:"100px",submitting:!1,submit:e=>{var t;a.name.push(e),(t=_.value)==null||t.closeDialog()},defaultValue:{},group:[{fields:[{type:"input",label:"用户名",field:"name",rules:[{required:!0,message:"请输入用户名"}]},{type:"input-number",label:"电话",field:"phone",rules:[{required:!0,message:"请输入电话"}]}]}]}),r=m({defaultExpandAll:!0,indexVisible:!0,rowKey:"id",columns:[{label:"短信接收人",prop:"receiveUserName"},{label:"消息内容",prop:"content"},{label:"接收人手机号码",prop:"receivePhone"},{label:"操作人组织",prop:"sendUserOrganizationName"},{label:"操作人部门",prop:"sendUserDepartmentName"},{label:"操作人",prop:"sendUserName"},{label:"发送时间",prop:"sendTime"},{label:"是否成功",prop:"status",formatter:e=>fe[e.status]||""}],dataList:[],pagination:{page:1,limit:20,total:0,refreshData:({page:e,size:t})=>{r.pagination.page=e,r.pagination.limit=t,d()}}}),a=m({name:[],content:"",templateId:"",sendTime:"",receiveUserIdList:[],receiveUserPhoneList:[]}),L=e=>{a.name.splice(a.name.indexOf(e),1)};function F(){var e;C.defaultValue={content:a.content},(e=_.value)==null||e.openDialog()}function N(){a.receiveUserIdList=[],a.receiveUserPhoneList=[],a.name.forEach(e=>{a.receiveUserIdList.push(e.name),a.receiveUserPhoneList.push(e.phone)}),a.sendTime=Z(a.sendTime).valueOf(),ue(a).then(()=>{D.success("发送成功"),d()}).catch(e=>{D.warning(e)})}function T(e){return e.map(t=>(t&&(t.children&&t.children.length?T(t.children):t.layer===3&&(t.icon=E.ADD)),t)),e}function V(){ge({page:1,size:9999}).then(e=>{g.data=e.data.data.data||[]}),_e(3).then(e=>{U(ie),c.data=ee(e.data.data||[],"children",{label:"name",value:"id"}),c.data=T(c.data)})}const d=async()=>{var t;const e={size:r.pagination.limit,page:r.pagination.page,...((t=f.value)==null?void 0:t.queryParams)||{}};me(e).then(o=>{r.dataList=o.data.data.data||[],r.pagination.total=o.data.data.total||0})};return J(async()=>{V(),d()}),(e,t)=>{const o=de,i=ce,k=te,w=ae,u=ne,P=se,B=le,M=oe,O=H,q=R,z=A;return b(),S("div",{class:j(["wrapper",{isDark:s(X)().isDark}])},[y("div",he,[n(i,{style:{height:"350px",width:"280px"}},{default:l(()=>[n(o,{"tree-data":s(g)},null,8,["tree-data"])]),_:1}),n(i,{style:{height:"350px",width:"300px"},class:"mag_left_14"},{default:l(()=>[n(o,{"tree-data":s(c)},null,8,["tree-data"])]),_:1}),n(i,{style:{height:"350px",width:"calc(100% - 600px)",padding:"10px"},class:"mag_left_14"},{default:l(()=>[y("div",be,[n(k,{type:"success",onClick:F},{default:l(()=>t[2]||(t[2]=[x(" 添加收件人 ")])),_:1})]),t[4]||(t[4]=y("br",null,null,-1)),n(M,{model:s(a),"label-width":"120px","label-position":"top"},{default:l(()=>[n(u,{label:"收件人"},{default:l(()=>[(b(!0),S(K,null,W(s(a).name,p=>(b(),Q(w,{key:p.name,class:"mr-4 mb-4",closable:"",onClose:xe=>L(p)},{default:l(()=>[x(Y(p.name),1)]),_:2},1032,["onClose"]))),128))]),_:1}),n(u,{label:"发送信息内容"},{default:l(()=>[n(P,{modelValue:s(a).content,"onUpdate:modelValue":t[0]||(t[0]=p=>s(a).content=p),type:"textarea"},null,8,["modelValue"])]),_:1}),n(u,{label:""},{default:l(()=>[n(B,{modelValue:s(a).sendTime,"onUpdate:modelValue":t[1]||(t[1]=p=>s(a).sendTime=p),type:"date",placeholder:"预计发送时间"},null,8,["modelValue"])]),_:1}),n(u,null,{default:l(()=>[n(k,{type:"success",onClick:N},{default:l(()=>t[3]||(t[3]=[x(" 发送信息 ")])),_:1})]),_:1})]),_:1},8,["model"])]),_:1})]),n(O,{ref_key:"refSearch",ref:f,config:s(I)},null,8,["config"]),n(q,{class:"card-table",config:s(r)},null,8,["config"]),n(z,{ref_key:"refForm",ref:_,config:s(C)},null,8,["config"])],2)}}}),Le=pe(ye,[["__scopeId","data-v-6db024fe"]]);export{Le as default};
