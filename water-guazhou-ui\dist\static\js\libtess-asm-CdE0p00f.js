function z4(M2,t0){for(var K2=0;K2<t0.length;K2++){const u2=t0[K2];if(typeof u2!="string"&&!Array.isArray(u2)){for(const C in u2)if(C!=="default"&&!(C in M2)){const j2=Object.getOwnPropertyDescriptor(u2,C);j2&&Object.defineProperty(M2,C,j2.get?j2:{enumerable:!0,get:()=>u2[C]})}}}return Object.freeze(Object.defineProperty(M2,Symbol.toStringTag,{value:"Module"}))}var H0,I1,S1,d0={},K4={get exports(){return d0},set exports(M2){d0=M2}};H0=K4,I1=function(){function M2(t0){const K2=t0.locateFile,u2={};var C=C!==void 0?C:{};const j2=(()=>{let d;return{resolve:y=>d(y),promise:new Promise(y=>d=y)}})(),_1=()=>j2.promise;C.locateFile=K2,C.onRuntimeInitialized=()=>{j2.resolve(u2)},u2.Module=C,u2.whenLoaded=_1;var w2,Y2={};for(w2 in C)C.hasOwnProperty(w2)&&(Y2[w2]=C[w2]);var h0,e0,o0,O2,D2,Q0=typeof window=="object",N2=typeof importScripts=="function",p0=typeof process=="object"&&typeof process.versions=="object"&&typeof process.versions.node=="string",A2="";function x1(d){return C.locateFile?C.locateFile(d,A2):A2+d}p0?(A2=N2?require("path").dirname(A2)+"/":__dirname+"/",h0=function(d,y){var I=W2(d);return I?y?I:I.toString():(O2||(O2=require("fs")),D2||(D2=require("path")),d=D2.normalize(d),O2.readFileSync(d,y?null:"utf8"))},o0=function(d){var y=h0(d,!0);return y.buffer||(y=new Uint8Array(y)),T1(y.buffer),y},e0=function(d,y,I){var x=W2(d);x&&y(x),O2||(O2=require("fs")),D2||(D2=require("path")),d=D2.normalize(d),O2.readFile(d,function(_,U){_?I(_):y(U.buffer)})},process.argv.length>1&&process.argv[1].replace(/\\/g,"/"),process.argv.slice(2),H0.exports=C,process.on("uncaughtException",function(d){if(!(d instanceof b4))throw d}),process.on("unhandledRejection",$2),C.inspect=function(){return"[Emscripten Module object]"}):(Q0||N2)&&(N2?A2=self.location.href:typeof document<"u"&&document.currentScript&&(A2=document.currentScript.src),A2=A2.indexOf("blob:")!==0?A2.substr(0,A2.lastIndexOf("/")+1):"",h0=function(d){try{var y=new XMLHttpRequest;return y.open("GET",d,!1),y.send(null),y.responseText}catch(x){var I=W2(d);if(I)return X1(I);throw x}},N2&&(o0=function(d){try{var y=new XMLHttpRequest;return y.open("GET",d,!1),y.responseType="arraybuffer",y.send(null),new Uint8Array(y.response)}catch(x){var I=W2(d);if(I)return I;throw x}}),e0=function(d,y,I){var x=new XMLHttpRequest;x.open("GET",d,!0),x.responseType="arraybuffer",x.onload=function(){if(x.status==200||x.status==0&&x.response)y(x.response);else{var _=W2(d);_?y(_.buffer):I()}},x.onerror=I,x.send(null)});var L1=C.print||console.log.bind(console),H2=C.printErr||console.warn.bind(console);for(w2 in Y2)Y2.hasOwnProperty(w2)&&(C[w2]=Y2[w2]);Y2=null,C.arguments&&C.arguments,C.thisProgram&&C.thisProgram,C.quit&&C.quit;var Q2,W0=0,P1=function(d){W0=d},B1=function(){return W0};C.wasmBinary&&(Q2=C.wasmBinary),C.noExitRuntime;var V2,g2={Memory:function(d){this.buffer=new ArrayBuffer(65536*d.initial)},Module:function(d){},Instance:function(d,y){this.exports=function(I){function x(Q){return Q.set=function(c2,N){this[c2]=N},Q.get=function(c2){return this[c2]},Q}for(var _,U=new Uint8Array(123),O=25;O>=0;--O)U[48+O]=52+O,U[65+O]=O,U[97+O]=26+O;function D(Q,c2,N){for(var h,r2,i=0,w=c2,P2=N.length,B2=c2+(3*P2>>2)-(N[P2-2]=="=")-(N[P2-1]=="=");i<P2;i+=4)h=U[N.charCodeAt(i+1)],r2=U[N.charCodeAt(i+2)],Q[w++]=U[N.charCodeAt(i)]<<2|h>>4,w<B2&&(Q[w++]=h<<4|r2>>2),w<B2&&(Q[w++]=r2<<6|U[N.charCodeAt(i+3)])}function a2(Q){D(_,1024,"LSsgICAwWDB4AC0wWCswWCAwWC0weCsweCAweABuYW4AaW5mAE5BTgBJTkYALgAobnVsbCkAR290IGVycm9yICVkCgAlZCAlZCAoJWYsJWYpLCglZiwlZiksKCVmLCVmKSAK"),D(_,1132,"BwAAAAAAAD8AAAA/AAAAAAAAAAB4Bg=="),D(_,1168,"EQAKABEREQAAAAAFAAAAAAAACQAAAAALAAAAAAAAAAARAA8KERERAwoHAAEACQsLAAAJBgsAAAsABhEAAAARERE="),D(_,1249,"CwAAAAAAAAAAEQAKChEREQAKAAACAAkLAAAACQALAAAL"),D(_,1307,"DA=="),D(_,1319,"DAAAAAAMAAAAAAkMAAAAAAAMAAAM"),D(_,1365,"Dg=="),D(_,1377,"DQAAAAQNAAAAAAkOAAAAAAAOAAAO"),D(_,1423,"EA=="),D(_,1435,"DwAAAAAPAAAAAAkQAAAAAAAQAAAQAAASAAAAEhIS"),D(_,1490,"EgAAABISEgAAAAAAAAk="),D(_,1539,"Cw=="),D(_,1551,"CgAAAAAKAAAAAAkLAAAAAAALAAAL"),D(_,1597,"DA=="),D(_,1609,"DAAAAAAMAAAAAAkMAAAAAAAMAAAMAAAwMTIzNDU2Nzg5QUJDREVG"),D(_,1648,"4I8AAAAAAAAF"),D(_,1668,"KA=="),D(_,1692,"KQAAACoAAACYCwAAAAQ="),D(_,1716,"AQ=="),D(_,1731,"Cv////8="),D(_,1972,"wA8=")}U[43]=62,U[47]=63;var t2=new ArrayBuffer(16),c0=new Int32Array(t2),r0=new Float64Array(t2);function l2(Q){return c0[Q]}function n1(Q,c2){c0[Q]=c2}function c4(){return r0[0]}function C0(Q){r0[0]=Q}function r4(Q){var c2=Q.a,N=c2.buffer;c2.grow=q4;var h=new Int8Array(N),r2=new Int16Array(N),i=new Int32Array(N),w=new Uint8Array(N),P2=new Uint16Array(N),B2=new Uint32Array(N),c=new Float32Array(N),X=new Float64Array(N),W=Math.imul,u=Math.fround,s4=Math.abs,s0=Math.clz32,C2=Math.max,o2=Q.abort,K=Q.b,V=Q.c,T2=Q.d,i0=Q.e,t1=Q.f,e1=Q.g,A4=Q.h,l4=Q.i,v4=Q.j,d4=Q.k,h4=Q.l,p4=Q.m,B=36832,v2=0;function H(n){var f=0,a=0,t=0,e=0,b=0,o=0,k=0,r=0,s=0,A=0,l=0,v=0,p=0,m=0;B=v=B-16|0;i:{f:{a:{n:{t:{b:{e:{c:{u:{o:{A:{s:{if((n|=0)>>>0<=244){if(3&(f=(b=i[614])>>>(a=(r=n>>>0<11?16:n+11&-8)>>>3|0)|0)){n=(e=i[2504+(f=(t=a+(1&(-1^f))|0)<<3)>>2])+8|0,(0|(a=i[e+8>>2]))!=(0|(f=f+2496|0))?(i[a+12>>2]=f,i[f+8>>2]=a):(p=2456,m=f2(t)&b,i[p>>2]=m),f=t<<3,i[e+4>>2]=3|f,i[4+(f=f+e|0)>>2]=1|i[f+4>>2];break i}if((A=i[616])>>>0>=r>>>0)break s;if(f){a=n=(f=(0-(n=(0-(n=2<<a)|n)&f<<a)&n)-1|0)>>>12&16,a|=n=(f=f>>>n|0)>>>5&8,a|=n=(f=f>>>n|0)>>>2&4,o=i[2504+(n=(a=((a|=n=(f=f>>>n|0)>>>1&2)|(n=(f=f>>>n|0)>>>1&1))+(f>>>n|0)|0)<<3)>>2],(0|(f=i[o+8>>2]))!=(0|(n=n+2496|0))?(i[f+12>>2]=n,i[n+8>>2]=f):(b=f2(a)&b,i[614]=b),n=o+8|0,i[o+4>>2]=3|r,e=(f=a<<3)-r|0,i[4+(t=o+r|0)>>2]=1|e,i[f+o>>2]=e,A&&(a=2496+((f=A>>>3|0)<<3)|0,o=i[619],(f=1<<f)&b?f=i[a+8>>2]:(i[614]=f|b,f=a),i[a+8>>2]=o,i[f+12>>2]=o,i[o+12>>2]=a,i[o+8>>2]=f),i[619]=t,i[616]=e;break i}if(!(k=i[615]))break s;for(a=n=(f=(k&0-k)-1|0)>>>12&16,a|=n=(f=f>>>n|0)>>>5&8,a|=n=(f=f>>>n|0)>>>2&4,f=i[2760+(((a|=n=(f=f>>>n|0)>>>1&2)|(n=(f=f>>>n|0)>>>1&1))+(f>>>n|0)<<2)>>2],t=(-8&i[f+4>>2])-r|0,a=f;(n=i[a+16>>2])||(n=i[a+20>>2]);)t=(e=(a=(-8&i[n+4>>2])-r|0)>>>0<t>>>0)?a:t,f=e?n:f,a=n;if((s=f+r|0)>>>0<=f>>>0)break A;if(l=i[f+24>>2],(0|(e=i[f+12>>2]))!=(0|f)){n=i[f+8>>2],i[n+12>>2]=e,i[e+8>>2]=n;break f}if(!(n=i[(a=f+20|0)>>2])){if(!(n=i[f+16>>2]))break o;a=f+16|0}for(;o=a,e=n,(n=i[(a=n+20|0)>>2])||(a=e+16|0,n=i[e+16>>2]););i[o>>2]=0;break f}if(r=-1,!(n>>>0>4294967231)&&(r=-8&(n=n+11|0),s=i[615])){t=0-r|0,b=0,r>>>0<256||(b=31,r>>>0>16777215||(n=n>>>8|0,n<<=o=n+1048320>>>16&8,b=28+((n=((n<<=a=n+520192>>>16&4)<<(f=n+245760>>>16&2)>>>15|0)-(f|a|o)|0)<<1|r>>>n+21&1)|0));k:{r:{if(a=i[2760+(b<<2)>>2])for(n=0,f=r<<((0|b)==31?0:25-(b>>>1|0)|0);;){if(!((o=(-8&i[a+4>>2])-r|0)>>>0>=t>>>0||(e=a,t=o))){t=0,n=a;break r}if(o=i[a+20>>2],a=i[16+((f>>>29&4)+a|0)>>2],n=o?(0|o)==(0|a)?n:o:n,f<<=1,!a)break}else n=0;if(!(n|e)){if(e=0,!(n=(0-(n=2<<b)|n)&s))break s;a=n=(f=(n&0-n)-1|0)>>>12&16,a|=n=(f=f>>>n|0)>>>5&8,a|=n=(f=f>>>n|0)>>>2&4,n=i[2760+(((a|=n=(f=f>>>n|0)>>>1&2)|(n=(f=f>>>n|0)>>>1&1))+(f>>>n|0)<<2)>>2]}if(!n)break k}for(;t=(a=(f=(-8&i[n+4>>2])-r|0)>>>0<t>>>0)?f:t,e=a?n:e,n=(f=i[n+16>>2])||i[n+20>>2];);}if(!(!e|i[616]-r>>>0<=t>>>0)){if((k=e+r|0)>>>0<=e>>>0)break A;if(b=i[e+24>>2],(0|e)!=(0|(f=i[e+12>>2]))){n=i[e+8>>2],i[n+12>>2]=f,i[f+8>>2]=n;break a}if(!(n=i[(a=e+20|0)>>2])){if(!(n=i[e+16>>2]))break u;a=e+16|0}for(;o=a,f=n,(n=i[(a=n+20|0)>>2])||(a=f+16|0,n=i[f+16>>2]););i[o>>2]=0;break a}}}if((a=i[616])>>>0>=r>>>0){t=i[619],(f=a-r|0)>>>0>=16?(i[616]=f,n=t+r|0,i[619]=n,i[n+4>>2]=1|f,i[a+t>>2]=f,i[t+4>>2]=3|r):(i[619]=0,i[616]=0,i[t+4>>2]=3|a,i[4+(n=a+t|0)>>2]=1|i[n+4>>2]),n=t+8|0;break i}if((k=i[617])>>>0>r>>>0){f=k-r|0,i[617]=f,n=(a=i[620])+r|0,i[620]=n,i[n+4>>2]=1|f,i[a+4>>2]=3|r,n=a+8|0;break i}if(n=0,f=s=r+47|0,i[732]?a=i[734]:(i[735]=-1,i[736]=-1,i[733]=4096,i[734]=4096,i[732]=v+12&-16^1431655768,i[737]=0,i[725]=0,a=4096),(a=(o=f+a|0)&(e=0-a|0))>>>0<=r>>>0||(t=i[724])&&t>>>0<(b=(f=i[722])+a|0)>>>0|f>>>0>=b>>>0)break i;if(4&w[2900])break b;s:{k:{if(t=i[620])for(n=2904;;){if(t>>>0<(f=i[n>>2])+i[n+4>>2]>>>0&&f>>>0<=t>>>0)break k;if(!(n=i[n+8>>2]))break}if((0|(f=F2(0)))==-1||(b=a,(n=(t=i[733])-1|0)&f&&(b=(a-f|0)+(n+f&0-t)|0),b>>>0<=r>>>0|b>>>0>2147483646)||(t=i[724])&&t>>>0<(e=(n=i[722])+b|0)>>>0|n>>>0>=e>>>0)break e;if((0|f)!=(0|(n=F2(b))))break s;break t}if((b=e&o-k)>>>0>2147483646)break e;if((0|(f=F2(b)))==(i[n>>2]+i[n+4>>2]|0))break c;n=f}if(!((0|n)==-1|r+48>>>0<=b>>>0)){if((f=(f=i[734])+(s-b|0)&0-f)>>>0>2147483646){f=n;break t}if((0|F2(f))!=-1){b=f+b|0,f=n;break t}F2(0-b|0);break e}if(f=n,(0|n)!=-1)break t;break e}o2()}e=0;break f}f=0;break a}if((0|f)!=-1)break t}i[725]=4|i[725]}if(a>>>0>2147483646||(0|(f=F2(a)))==-1|(0|(n=F2(0)))==-1|n>>>0<=f>>>0||(b=n-f|0)>>>0<=r+40>>>0)break n}n=i[722]+b|0,i[722]=n,n>>>0>B2[723]&&(i[723]=n);t:{b:{e:{if(o=i[620]){for(n=2904;;){if(((t=i[n>>2])+(a=i[n+4>>2])|0)==(0|f))break e;if(!(n=i[n+8>>2]))break}break b}for((n=i[618])>>>0<=f>>>0&&n||(i[618]=f),n=0,i[727]=b,i[726]=f,i[622]=-1,i[623]=i[732],i[729]=0;a=2496+(t=n<<3)|0,i[t+2504>>2]=a,i[t+2508>>2]=a,(0|(n=n+1|0))!=32;);a=(t=b-40|0)-(n=f+8&7?-8-f&7:0)|0,i[617]=a,n=n+f|0,i[620]=n,i[n+4>>2]=1|a,i[4+(f+t|0)>>2]=40,i[621]=i[736];break t}if(!(8&w[n+12|0]|t>>>0>o>>>0|f>>>0<=o>>>0)){i[n+4>>2]=a+b,a=(n=o+8&7?-8-o&7:0)+o|0,i[620]=a,n=(f=i[617]+b|0)-n|0,i[617]=n,i[a+4>>2]=1|n,i[4+(f+o|0)>>2]=40,i[621]=i[736];break t}}B2[618]>f>>>0&&(i[618]=f),a=f+b|0,n=2904;b:{e:{c:{u:{o:{A:{for(;;){if((0|a)!=i[n>>2]){if(n=i[n+8>>2])continue;break A}break}if(!(8&w[n+12|0]))break o}for(n=2904;;){if((a=i[n>>2])>>>0<=o>>>0&&(e=a+i[n+4>>2]|0)>>>0>o>>>0)break u;n=i[n+8>>2]}}if(i[n>>2]=f,i[n+4>>2]=i[n+4>>2]+b,i[4+(s=(f+8&7?-8-f&7:0)+f|0)>>2]=3|r,a=(b=a+(a+8&7?-8-a&7:0)|0)-(k=r+s|0)|0,(0|o)==(0|b)){i[620]=k,n=i[617]+a|0,i[617]=n,i[k+4>>2]=1|n;break e}if(i[619]==(0|b)){i[619]=k,n=i[616]+a|0,i[616]=n,i[k+4>>2]=1|n,i[n+k>>2]=n;break e}if((3&(n=i[b+4>>2]))==1){o=-8&n;o:if(n>>>0<=255){if(t=i[b+8>>2],n=n>>>3|0,(0|(f=i[b+12>>2]))==(0|t)){p=2456,m=i[614]&f2(n),i[p>>2]=m;break o}i[t+12>>2]=f,i[f+8>>2]=t}else{if(r=i[b+24>>2],(0|b)==(0|(f=i[b+12>>2])))if((t=i[(n=b+20|0)>>2])||(t=i[(n=b+16|0)>>2])){for(;e=n,(t=i[(n=(f=t)+20|0)>>2])||(n=f+16|0,t=i[f+16>>2]););i[e>>2]=0}else f=0;else n=i[b+8>>2],i[n+12>>2]=f,i[f+8>>2]=n;if(r){t=i[b+28>>2];A:{if(i[(n=2760+(t<<2)|0)>>2]==(0|b)){if(i[n>>2]=f,f)break A;p=2460,m=i[615]&f2(t),i[p>>2]=m;break o}if(i[r+(i[r+16>>2]==(0|b)?16:20)>>2]=f,!f)break o}i[f+24>>2]=r,(n=i[b+16>>2])&&(i[f+16>>2]=n,i[n+24>>2]=f),(n=i[b+20>>2])&&(i[f+20>>2]=n,i[n+24>>2]=f)}}b=o+b|0,a=a+o|0}if(i[b+4>>2]=-2&i[b+4>>2],i[k+4>>2]=1|a,i[a+k>>2]=a,a>>>0<=255){f=2496+((n=a>>>3|0)<<3)|0,(a=i[614])&(n=1<<n)?n=i[f+8>>2]:(i[614]=n|a,n=f),i[f+8>>2]=k,i[n+12>>2]=k,i[k+12>>2]=f,i[k+8>>2]=n;break e}if(n=31,a>>>0<=16777215&&(n=a>>>8|0,n<<=e=n+1048320>>>16&8,n=28+((n=((n<<=t=n+520192>>>16&4)<<(f=n+245760>>>16&2)>>>15|0)-(f|t|e)|0)<<1|a>>>n+21&1)|0),i[k+28>>2]=n,i[k+16>>2]=0,i[k+20>>2]=0,e=2760+(n<<2)|0,(t=i[615])&(f=1<<n)){for(n=a<<((0|n)==31?0:25-(n>>>1|0)|0),f=i[e>>2];;){if(t=f,(-8&i[f+4>>2])==(0|a))break c;if(f=n>>>29|0,n<<=1,!(f=i[16+(e=t+(4&f)|0)>>2]))break}i[e+16>>2]=k,i[k+24>>2]=t}else i[615]=f|t,i[e>>2]=k,i[k+24>>2]=e;i[k+12>>2]=k,i[k+8>>2]=k;break e}for(a=(t=b-40|0)-(n=f+8&7?-8-f&7:0)|0,i[617]=a,n=n+f|0,i[620]=n,i[n+4>>2]=1|a,i[4+(f+t|0)>>2]=40,i[621]=i[736],i[(a=(n=(e+(e-39&7?39-e&7:0)|0)-47|0)>>>0<o+16>>>0?o:n)+4>>2]=27,n=i[729],i[a+16>>2]=i[728],i[a+20>>2]=n,n=i[727],i[a+8>>2]=i[726],i[a+12>>2]=n,i[728]=a+8,i[727]=b,i[726]=f,i[729]=0,n=a+24|0;i[n+4>>2]=7,f=n+8|0,n=n+4|0,f>>>0<e>>>0;);if((0|a)==(0|o))break t;if(i[a+4>>2]=-2&i[a+4>>2],e=a-o|0,i[o+4>>2]=1|e,i[a>>2]=e,e>>>0<=255){f=2496+((n=e>>>3|0)<<3)|0,(a=i[614])&(n=1<<n)?n=i[f+8>>2]:(i[614]=n|a,n=f),i[f+8>>2]=o,i[n+12>>2]=o,i[o+12>>2]=f,i[o+8>>2]=n;break t}if(n=31,i[o+16>>2]=0,i[o+20>>2]=0,e>>>0<=16777215&&(n=e>>>8|0,n<<=t=n+1048320>>>16&8,n=28+((n=((n<<=a=n+520192>>>16&4)<<(f=n+245760>>>16&2)>>>15|0)-(f|a|t)|0)<<1|e>>>n+21&1)|0),i[o+28>>2]=n,t=2760+(n<<2)|0,(a=i[615])&(f=1<<n)){for(n=e<<((0|n)==31?0:25-(n>>>1|0)|0),f=i[t>>2];;){if(a=f,(0|e)==(-8&i[f+4>>2]))break b;if(f=n>>>29|0,n<<=1,!(f=i[16+(t=a+(4&f)|0)>>2]))break}i[t+16>>2]=o,i[o+24>>2]=a}else i[615]=f|a,i[t>>2]=o,i[o+24>>2]=t;i[o+12>>2]=o,i[o+8>>2]=o;break t}n=i[t+8>>2],i[n+12>>2]=k,i[t+8>>2]=k,i[k+24>>2]=0,i[k+12>>2]=t,i[k+8>>2]=n}n=s+8|0;break i}n=i[a+8>>2],i[n+12>>2]=o,i[a+8>>2]=o,i[o+24>>2]=0,i[o+12>>2]=a,i[o+8>>2]=n}if(!((n=i[617])>>>0<=r>>>0)){f=n-r|0,i[617]=f,n=(a=i[620])+r|0,i[620]=n,i[n+4>>2]=1|f,i[a+4>>2]=3|r,n=a+8|0;break i}}i[613]=48,n=0;break i}a:if(b){a=i[e+28>>2];n:{if(i[(n=2760+(a<<2)|0)>>2]==(0|e)){if(i[n>>2]=f,f)break n;s=f2(a)&s,i[615]=s;break a}if(i[b+(i[b+16>>2]==(0|e)?16:20)>>2]=f,!f)break a}i[f+24>>2]=b,(n=i[e+16>>2])&&(i[f+16>>2]=n,i[n+24>>2]=f),(n=i[e+20>>2])&&(i[f+20>>2]=n,i[n+24>>2]=f)}a:if(t>>>0<=15)n=t+r|0,i[e+4>>2]=3|n,i[4+(n=n+e|0)>>2]=1|i[n+4>>2];else if(i[e+4>>2]=3|r,i[k+4>>2]=1|t,i[t+k>>2]=t,t>>>0<=255)f=2496+((n=t>>>3|0)<<3)|0,(a=i[614])&(n=1<<n)?n=i[f+8>>2]:(i[614]=n|a,n=f),i[f+8>>2]=k,i[n+12>>2]=k,i[k+12>>2]=f,i[k+8>>2]=n;else{n=31,t>>>0<=16777215&&(n=t>>>8|0,n<<=o=n+1048320>>>16&8,n=28+((n=((n<<=a=n+520192>>>16&4)<<(f=n+245760>>>16&2)>>>15|0)-(f|a|o)|0)<<1|t>>>n+21&1)|0),i[k+28>>2]=n,i[k+16>>2]=0,i[k+20>>2]=0,a=2760+(n<<2)|0;n:{if((f=1<<n)&s){for(n=t<<((0|n)==31?0:25-(n>>>1|0)|0),r=i[a>>2];;){if((-8&i[(f=r)+4>>2])==(0|t))break n;if(a=n>>>29|0,n<<=1,!(r=i[16+(a=f+(4&a)|0)>>2]))break}i[a+16>>2]=k,i[k+24>>2]=f}else i[615]=f|s,i[a>>2]=k,i[k+24>>2]=a;i[k+12>>2]=k,i[k+8>>2]=k;break a}n=i[f+8>>2],i[n+12>>2]=k,i[f+8>>2]=k,i[k+24>>2]=0,i[k+12>>2]=f,i[k+8>>2]=n}n=e+8|0;break i}f:if(l){a=i[f+28>>2];a:{if(i[(n=2760+(a<<2)|0)>>2]==(0|f)){if(i[n>>2]=e,e)break a;p=2460,m=f2(a)&k,i[p>>2]=m;break f}if(i[(i[l+16>>2]==(0|f)?16:20)+l>>2]=e,!e)break f}i[e+24>>2]=l,(n=i[f+16>>2])&&(i[e+16>>2]=n,i[n+24>>2]=e),(n=i[f+20>>2])&&(i[e+20>>2]=n,i[n+24>>2]=e)}t>>>0<=15?(n=t+r|0,i[f+4>>2]=3|n,i[4+(n=n+f|0)>>2]=1|i[n+4>>2]):(i[f+4>>2]=3|r,i[s+4>>2]=1|t,i[t+s>>2]=t,A&&(a=2496+((n=A>>>3|0)<<3)|0,e=i[619],(n=1<<n)&b?n=i[a+8>>2]:(i[614]=n|b,n=a),i[a+8>>2]=e,i[n+12>>2]=e,i[e+12>>2]=a,i[e+8>>2]=n),i[619]=s,i[616]=t),n=f+8|0}return B=v+16|0,0|n}function m4(n,f,a,t,e,b){n|=0,f|=0,a|=0,t|=0,e|=0,b|=0;for(var o=0,k=0,r=0,s=0,A=0,l=u(0),v=0,p=u(0),m=u(0),g=0,E=0,R=0,S=0,L=0,P=0,F=0,j=0;(r=i[2032+(o=k<<2)>>2])&&(i[r>>2]=0),(r=i[2032+(4|o)>>2])&&(i[r>>2]=0),(r=i[2032+(8|o)>>2])&&(i[r>>2]=0),(o=i[2032+(12|o)>>2])&&(i[o>>2]=0),(0|(k=k+4|0))!=100;);if((k=i[608])||(k=H(16),i[608]=k),i[k+8>>2]=b,i[k+4>>2]=0,i[k+12>>2]=t,i[k>>2]=e,(k=i[609])||((e=H(1900))?(i[e+100>>2]=12,i[e+96>>2]=13,i[e+92>>2]=14,i[e+88>>2]=15,r2[e+80>>1]=0,i[e+52>>2]=0,i[e+56>>2]=100130,i[e+16>>2]=0,i[e+20>>2]=0,i[e>>2]=0,i[e+1896>>2]=0,i[e+1736>>2]=8,i[e+1732>>2]=11,i[e+1728>>2]=6,i[e+1724>>2]=5,i[e+1720>>2]=4,i[e+1716>>2]=3,i[e+104>>2]=16,i[e+76>>2]=17,i[e+12>>2]=18,i[e+24>>2]=0):e=0,i[609]=e,q2(e,100107,34),q2(i[609],100100,35),q2(i[609],100102,36),q2(i[609],100105,37),q2(i[609],100103,38),q2(i[609],100104,39),i[i[609]+56>>2]=100130,e=i[609],c[e+16>>2]=0,c[e+24>>2]=1,c[e+20>>2]=0,k=i[609]),e=0,b=i[608],i[k>>2]&&E2(k,0),i[k+112>>2]=0,i[k>>2]=1,h[k+108|0]=0,i[k+1896>>2]=b,i[k+8>>2]=0,(0|a)>0)for(b=0;;){if(g=i[(b<<2)+f>>2],o=i[609],i[o>>2]!=1&&E2(o,1),i[o>>2]=2,i[o+4>>2]=0,i[o+112>>2]>=1&&(h[o+108|0]=1),k=0,(0|g)>0)for(;;){A=E=(W(e+k|0,t)<<2)+n|0,r=i[609],i[r>>2]!=2&&E2(r,2);i:{f:{a:{if(w[r+108|0]){if(o=I0(),i[r+8>>2]=o,!o)break a;if((0|(o=i[r+112>>2]))>=1)for(R=116+(r+(o<<4)|0)|0,s=r+116|0,o=i[r+4>>2];;){j=i[s+12>>2];n:{if(!o){if(!(o=f0(i[r+8>>2])))break a;if(z(o,i[o+4>>2]))break n;break a}if(!n2(o))break a;o=i[o+12>>2]}if(v=i[o+16>>2],i[v+12>>2]=j,c[v+16>>2]=c[s>>2],l=c[s+4>>2],i[v+24>>2]=0,c[v+20>>2]=l,i[o+28>>2]=1,i[i[o+4>>2]+28>>2]=-1,i[r+4>>2]=o,!(R>>>0>(s=s+16|0)>>>0))break}h[r+108|0]=0,i[r+112>>2]=0,i[r+4>>2]=0}s=(L=+(l=(S=+(l=c[A+4>>2]))<-1e37?u(-9999999933815813e21):l))>1e37,v=(F=+(p=(P=+(p=c[A>>2]))<-1e37?u(-9999999933815813e21):p))>1e37,((o=+(m=c[A+8>>2])<-1e37)|(A=+(m=o?u(-9999999933815813e21):m)>1e37)|S<-1e37|L>1e37||F>1e37||P<-1e37)&&((0|(o=i[r+1732>>2]))==11?M[i[r+12>>2]](100155):M[0|o](100155,i[r+1896>>2])),l=s?u(9999999933815813e21):l,p=v?u(9999999933815813e21):p;n:{if(!i[r+8>>2]){if((0|(s=i[r+112>>2]))<=99){c[124+(o=r+(s<<4)|0)>>2]=A?u(9999999933815813e21):m,c[o+120>>2]=l,c[o+116>>2]=p,i[o+128>>2]=E,i[r+112>>2]=s+1;break i}if(o=I0(),i[r+8>>2]=o,!o)break n;if((0|(o=i[r+112>>2]))>=1)for(v=116+(r+(o<<4)|0)|0,s=r+116|0,o=i[r+4>>2];;){R=i[s+12>>2];t:{if(!o){if(!(o=f0(i[r+8>>2])))break n;if(z(o,i[o+4>>2]))break t;break n}if(!n2(o))break n;o=i[o+12>>2]}if(A=i[o+16>>2],i[A+12>>2]=R,c[A+16>>2]=c[s>>2],m=c[s+4>>2],i[A+24>>2]=0,c[A+20>>2]=m,i[o+28>>2]=1,i[i[o+4>>2]+28>>2]=-1,i[r+4>>2]=o,!(v>>>0>(s=s+16|0)>>>0))break}h[r+108|0]=0,i[r+112>>2]=0}t:{b:{if(!(o=i[r+4>>2])){if(!(o=f0(i[r+8>>2])))break t;if(z(o,i[o+4>>2]))break b;break t}if(!n2(o))break t;o=i[o+12>>2]}s=i[o+16>>2],c[s+16>>2]=p,i[s+12>>2]=E,i[s+24>>2]=0,c[s+20>>2]=l,i[o+28>>2]=1,i[i[o+4>>2]+28>>2]=-1,i[r+4>>2]=o;break i}if((0|(o=i[r+1732>>2]))!=11)break f;M[i[r+12>>2]](100902);break i}if((0|(o=i[r+1732>>2]))!=11)break f;M[i[r+12>>2]](100902);break i}if((0|(o=i[r+1732>>2]))==11){M[i[r+12>>2]](100902);break i}}M[0|o](100902,i[r+1896>>2])}if((0|g)==(0|(k=k+1|0)))break}if(o=i[609],i[o>>2]!=2&&E2(o,2),i[o>>2]=1,e=e+g|0,(0|(b=b+1|0))==(0|a))break}a=i[609],n=0,f=H(40),i[f>>2]=0,e=g1(r=a+1740|0,1,f,4),b=0|V();i:{f:{a:{n:{t:{b:for(;;){e:{c:{u:{o:{A:{s:{k:{if(n){if((0|(n=i[a+1732>>2]))!=11){if(f=i[a+1896>>2],i[611]=0,T2(0|n,100902,0|f),n=i[611],i[611]=0,f=-1,!n||!(t=i[612]))break s;if(f=J(i[n>>2],e,b))break k;break t}if(n=i[a+12>>2],i[611]=0,t1(0|n,100902),n=i[611],i[611]=0,f=-1,!n||!(t=i[612]))break o;if(f=J(i[n>>2],e,b))break A;break t}if(i[a>>2]==1)break e;if(i[611]=0,T2(19,0|a,1),n=i[611],i[611]=0,f=-1,!n||!(t=i[612]))break c;if(f=J(i[n>>2],e,b))break u;break t}K(0|t)}if(n=0|V(),(0|f)==1)continue;break i}K(0|t)}if(n=0|V(),(0|f)==1)continue;break i}K(0|t)}if(n=0|V(),(0|f)==1)continue}i[a>>2]=0;e:{c:{u:{if(!i[a+8>>2]){if(!(w[a+80|0]|i[a+104>>2]!=16)){if(i[611]=0,o=0|i0(20,0|a),n=i[611],i[611]=0,f=-1,n&&(t=i[612])){if(!(f=J(i[n>>2],e,b)))break t;K(0|t)}if(n=0|V(),(0|f)==1)continue;if(o){i[a+1896>>2]=0;break i}}if(i[611]=0,t=0|h4(21),n=i[611],i[611]=0,f=-1,n&&(k=i[612])){if(!(f=J(i[n>>2],e,b)))break n;K(0|k)}if(n=0|V(),(0|f)==1)continue;if(i[a+8>>2]=t,!t)break u;if((0|(n=i[a+112>>2]))>=1)for(s=116+(a+(n<<4)|0)|0,t=a+116|0,f=i[a+4>>2];;){g=i[t+12>>2];o:{A:{s:{k:{r:{l:{if(!f){if(n=i[a+8>>2],i[611]=0,f=0|i0(22,0|n),n=i[611],i[611]=0,k=-1,!n||!(o=i[612]))break r;if(k=J(i[n>>2],e,b))break l;break a}if(i[611]=0,A=0|i0(23,0|f),n=i[611],i[611]=0,k=-1,!n||!(o=i[612]))break s;if(k=J(i[n>>2],e,b))break k;break a}K(0|o)}if(n=0|V(),(0|k)!=1)break A;continue b}K(0|o)}if(n=0|V(),(0|k)==1)continue b;if(!A)break u;f=i[f+12>>2];break o}if(!f)break u;if(n=i[f+4>>2],i[611]=0,A=0|d4(24,0|f,0|n),n=i[611],i[611]=0,k=-1,n&&(o=i[612])){if(!(k=J(i[n>>2],e,b)))break a;K(0|o)}if(n=0|V(),(0|k)==1)continue b;if(!A)break u}if(n=i[f+16>>2],i[n+12>>2]=g,c[n+16>>2]=c[t>>2],l=c[t+4>>2],i[n+24>>2]=0,c[n+20>>2]=l,i[f+28>>2]=1,i[i[f+4>>2]+28>>2]=-1,i[a+4>>2]=f,!(s>>>0>(t=t+16|0)>>>0))break}h[a+108|0]=0,i[a+112>>2]=0}if(i[611]=0,b1(a),n=i[611],i[611]=0,f=-1,!n||!(t=i[612]))break e;if(f=J(i[n>>2],e,b))break c;break t}if(i[611]=0,T2(26,0|r,1),f=i[611],i[611]=0,f&&(n=i[612])){if(!J(i[f>>2],e,b))break f;K(0|n)}n=0|V();continue}K(0|t)}if(n=0|V(),(0|f)!=1){if(i[611]=0,o=0|i0(27,0|a),n=i[611],i[611]=0,f=-1,n&&(t=i[612])){if(!(f=J(i[n>>2],e,b)))break t;K(0|t)}if(n=0|V(),(0|f)!=1){e:{c:{u:{o:{A:{s:{k:{r:{l:{v:{d:{if(!o){if(i[611]=0,T2(26,0|r,1),f=i[611],i[611]=0,!f||!(n=i[612]))break v;if(J(i[f>>2],e,b))break d;break f}if(f=i[a+8>>2],w[a+60|0])break s;if(w[a+81|0]){if(i[611]=0,k=0|v4(28,0|f,1,1),n=i[611],i[611]=0,t=-1,!n||!(o=i[612]))break k;if(t=J(i[n>>2],e,b))break l;break a}if(i[611]=0,k=0|i0(29,0|f),n=i[611],i[611]=0,t=-1,!n||!(o=i[612]))break k;if(t=J(i[n>>2],e,b))break r;break a}K(0|n)}n=0|V();continue}K(0|o);break k}K(0|o)}if(n=0|V(),(0|t)==1)continue;k:{r:{l:{v:{d:{h:{p:{if(!k){if(i[611]=0,T2(26,0|r,1),f=i[611],i[611]=0,!f||!(n=i[612]))break h;if(J(i[f>>2],e,b))break p;break f}if(!(i[a+88>>2]!=15|i[a+100>>2]!=12|i[a+96>>2]!=13|i[a+92>>2]!=14||i[a+1716>>2]!=3|i[a+1728>>2]!=6|i[a+1724>>2]!=5||i[a+1720>>2]!=4))break k;if(w[a+81|0]){if(i[611]=0,T2(30,0|a,0|f),n=i[611],i[611]=0,t=-1,!n||!(k=i[612]))break v;if(t=J(i[n>>2],e,b))break d;break n}if(i[611]=0,T2(31,0|a,0|f),n=i[611],i[611]=0,t=-1,!n||!(k=i[612]))break r;if(t=J(i[n>>2],e,b))break l;break n}K(0|n)}n=0|V();continue}K(0|k)}if(n=0|V(),(0|t)==1)continue;break k}K(0|k)}if(n=0|V(),(0|t)==1)continue}if(i[a+104>>2]!=16){if(i[611]=0,r1(f),n=i[611],i[611]=0,t=-1,!n||!(k=i[612]))break o;if(t=J(i[n>>2],e,b))break A;break n}}if(i[611]=0,_0(f),n=i[611],i[611]=0,f=-1,!n||!(t=i[612]))break c;if(f=J(i[n>>2],e,b))break u;break t}K(0|k)}if(n=0|V(),(0|t)!=1)break e;continue}K(0|t)}if(n=0|V(),(0|f)==1)continue;i[a+8>>2]=0,i[a+1896>>2]=0;break i}if(n=i[a+104>>2],i[611]=0,t1(0|n,0|f),n=i[611],i[611]=0,f=-1,n&&(t=i[612])){if(!(f=J(i[n>>2],e,b)))break t;K(0|t)}if(n=0|V(),(0|f)!=1)break}}}i[a+1896>>2]=0,i[a+8>>2]=0;break i}b2(n,t),o2()}b2(n,k),o2()}b2(n,o),o2()}b2(f,n),o2()}return T(e),i[i[608]+4>>2]}function o1(n,f){var a=u(0),t=u(0),e=0,b=u(0),o=u(0),k=0,r=0,s=u(0),A=0,l=0,v=0,p=u(0),m=u(0),g=u(0),E=0,R=0,S=0,L=0,P=0,F=0,j=0,q=0,Y=0,k2=u(0),m2=0,n0=u(0);B=k=B-144|0,Y=i[i[i[f+4>>2]+8>>2]>>2],j=i[Y>>2],S=i[j+16>>2],P=i[i[j+4>>2]+16>>2],F=i[f>>2],L=i[i[F+4>>2]+16>>2],E=i[F+16>>2],G(L,i[n+72>>2],E)>u(0)&&(a=c[L+28>>2],t=c[L+32>>2],e=i[n+72>>2],b=c[e+28>>2],o=c[e+32>>2],s=c[E+28>>2],X[k+40>>3]=c[E+32>>2],X[k+32>>3]=s,X[k+24>>3]=o,X[k+16>>3]=b,X[k+8>>3]=t,X[k>>3]=a,R1(1098,k));i:{f:{a:if((0|E)!=(0|S)&&!((m=(a=c[E+32>>2])<=(t=c[L+32>>2])?a:t)>((t=c[S+32>>2])>=(b=c[P+32>>2])?t:b))){n:{if(!(!(a<=t)|(b=c[E+28>>2])!=(o=c[S+28>>2]))||b<o){if(!(G(P,E,S)>u(0)))break n;break a}if(G(L,S,E)<u(0))break a}v=P,e=S,l=E,(a=c[(A=L)+28>>2])<(t=c[E+28>>2])|(c[A+32>>2]<=c[l+32>>2]?a==t:0)?(r=l,l=A):r=A,(a=c[e+28>>2])>(t=c[v+28>>2])|(c[v+32>>2]<=c[e+32>>2]?a==t:0)?(a=t,A=e,e=v):A=v,a>(t=c[l+28>>2])|(c[l+32>>2]<=c[e+32>>2]?a==t:0)?(t=a,v=A,R=e,A=r,e=l):(v=r,R=l),r=k;n:if((o=c[A+28>>2])>t|(c[R+32>>2]<=c[A+32>>2]?t==o:0))if(p=c[v+28>>2],!(c[A+32>>2]<=c[v+32>>2])|o!=p&&!(p>o))if(t=G(e,R,A),a=G(e,v,A),l=u(t-a)<u(0),(b=u(C2(l?u(-t):t,u(0))))<=(t=u(C2(l?a:u(-a),u(0))))){if(o=c[R+28>>2],s=c[v+28>>2],a=u(u(o+s)*u(.5)),t==u(0))break n;a=u(o+u(u(b/u(b+t))*u(s-o)))}else a=c[v+28>>2],a=u(a+u(u(t/u(b+t))*u(c[R+28>>2]-a)));else{if(a=u(0),s=u(o-t),b=u(t-c[e+28>>2]),(m=u(s+b))>u(0)&&(a=c[((l=b<s)?e:A)+32>>2],a=u(u(c[R+32>>2]-a)+u(u((l?b:s)/m)*u(a-c[(l?A:e)+32>>2])))),k2=u(-a),m=a,p=u(p-o),(b=u(s+p))>u(0)&&(g=c[((l=s<p)?R:v)+32>>2],g=u(u(c[A+32>>2]-g)+u(u((l?s:p)/b)*u(g-c[(l?v:R)+32>>2])))),l=u(a+g)<u(0),(p=u(C2(l?k2:m,u(0))))<=(b=u(C2(l?u(-g):g,u(0))))){if(a=u(u(t+o)*u(.5)),b==u(0))break n;a=u(t+u(s*u(p/u(p+b))));break n}a=u(o+u(u(t-o)*u(b/u(p+b))))}else a=u(u(t+o)*u(.5));c[r+84>>2]=a,(a=c[e+32>>2])<(t=c[A+32>>2])|(c[e+28>>2]<=c[A+28>>2]?a==t:0)?(r=A,A=e):r=e,(t=c[v+32>>2])>(a=c[R+32>>2])|(c[R+28>>2]<=c[v+28>>2]?a==t:0)?(t=a,e=v,v=R):e=R,(a=c[A+32>>2])<t|(c[A+28>>2]<=c[v+28>>2]?a==t:0)?(R=e,l=v,e=r,v=A):(R=r,l=A);n:{t:if((o=c[l+32>>2])<(s=c[e+32>>2])|(c[l+28>>2]<=c[e+28>>2]?o==s:0)){if(g=c[R+32>>2],!(!(c[e+28>>2]<=c[R+28>>2])|s!=g)||g>s){if(a=u(0),t=u(0),p=u(s-o),b=u(o-c[v+32>>2]),(m=u(p+b))>u(0)&&(t=c[((r=b<p)?v:e)+28>>2],t=u(u(c[l+28>>2]-t)+u(u((r?b:p)/m)*u(t-c[(r?e:v)+28>>2])))),n0=u(-t),m=t,g=u(g-s),(b=u(p+g))>u(0)&&(k2=c[e+28>>2],a=c[((e=p<g)?l:R)+28>>2],a=u(u(k2-a)+u(u((e?p:g)/b)*u(a-c[(e?R:l)+28>>2])))),e=u(t+a)<u(0),(t=u(C2(e?n0:m,u(0))))<=(a=u(C2(e?u(-a):a,u(0))))){if(a==u(0))break t;c[k+88>>2]=o+u(p*u(t/u(t+a)));break n}c[k+88>>2]=s+u(u(o-s)*u(a/u(t+a)));break n}if(a=u(0),t=u(0),b=u(s-o),p=c[v+32>>2],m=u(o-p),u(b+m)>u(0)&&(t=c[l+28>>2],t=u(u(m*u(t-c[e+28>>2]))+u(b*u(t-c[v+28>>2])))),m=u(-t),b=t,s=u(s-g),p=u(g-p),u(s+p)>u(0)&&(a=c[R+28>>2],a=u(u(p*u(a-c[e+28>>2]))+u(s*u(a-c[v+28>>2])))),e=u(t-a)<u(0),(t=u(C2(e?m:b,u(0))))<=(a=u(C2(e?a:u(-a),u(0))))){if(a==u(0)){c[k+88>>2]=u(o+g)*u(.5);break n}c[k+88>>2]=o+u(u(g-o)*u(t/u(t+a)));break n}c[k+88>>2]=g+u(u(o-g)*u(a/u(t+a)));break n}c[k+88>>2]=u(o+s)*u(.5)}a=c[k+84>>2],r=i[n+72>>2];n:{if(a<(b=c[r+28>>2]))t=c[r+32>>2];else if(a!=b||!((t=c[r+32>>2])>=c[k+88>>2]))break n;c[k+88>>2]=t,c[k+84>>2]=b,a=b}A=e=E,(t=b=c[e+28>>2])<(o=c[S+28>>2])||b==o&&(t=b,A=E,c[e+32>>2]<=c[S+32>>2])||(t=o,A=S),e=A;n:{if(a>t)o=c[e+32>>2];else if(a!=t||!((o=c[e+32>>2])<=c[k+88>>2]))break n;c[k+88>>2]=o,c[k+84>>2]=t,b=c[E+28>>2],a=t}if(c[S+28>>2]!=a|c[k+88>>2]!=c[S+32>>2]&&(c[k+88>>2]!=c[E+32>>2]||a!=b)){n:{a=c[r+28>>2];t:{if(c[L+32>>2]!=c[r+32>>2]||a!=c[L+28>>2]){if(G(L,r,k+56|0)>=u(0))break t;r=i[n+72>>2],a=c[r+28>>2]}if(c[P+32>>2]==c[r+32>>2]&&a==c[P+28>>2]||!(G(P,r,k+56|0)<=u(0)))break n}if((0|(e=i[n+72>>2]))==(0|P)){if(!n2(i[F+4>>2])||!z(i[j+4>>2],F))break i;for(e=i[i[f>>2]+16>>2];f=i[i[i[f+4>>2]+4>>2]>>2],A=i[f>>2],(0|e)==i[A+16>>2];);if(r=f,w[f+15|0]&&(r=0,(e=d2(i[i[i[i[i[f+4>>2]+8>>2]>>2]>>2]+4>>2],i[A+12>>2]))&&$(i[f>>2])&&(i[f>>2]=e,h[f+15|0]=0,i[e+24>>2]=f,r=i[i[i[f+4>>2]+4>>2]>>2])),!r)break i;e=i[i[i[r+4>>2]+8>>2]>>2],f=i[e>>2],a0(n,e,Y),m2=1,U2(n,r,i[i[f+4>>2]+12>>2],f,f,1);break a}if((0|e)==(0|L)){if(!n2(i[j+4>>2])||!z(i[F+12>>2],i[i[j+4>>2]+12>>2]))break i;for(r=i[i[i[f>>2]+4>>2]+16>>2],e=f;e=i[i[i[e+4>>2]+4>>2]>>2],(0|r)==i[i[i[e>>2]+4>>2]+16>>2];);r=i[i[i[i[i[i[e+4>>2]+8>>2]>>2]>>2]+4>>2]+8>>2],i[f>>2]=i[i[j+4>>2]+12>>2],m2=1,U2(n,e,i[a0(n,f,0)+8>>2],i[i[F+4>>2]+8>>2],r,1);break a}if(G(L,e,k+56|0)>=u(0)){if(h[f+14|0]=1,h[i[i[i[f+4>>2]+4>>2]>>2]+14|0]=1,!n2(i[F+4>>2]))break i;r=i[F+16>>2],e=i[n+72>>2],c[r+28>>2]=c[e+28>>2],c[r+32>>2]=c[e+32>>2]}else e=i[n+72>>2];if(!(G(P,e,k+56|0)<=u(0)))break a;if(h[Y+14|0]=1,h[f+14|0]=1,!n2(i[j+4>>2]))break i;f=i[j+16>>2],n=i[n+72>>2],c[f+28>>2]=c[n+28>>2],c[f+32>>2]=c[n+32>>2];break a}if(!n2(i[F+4>>2])||!n2(i[j+4>>2])||!z(i[i[j+4>>2]+12>>2],F))break i;if(e=i[F+16>>2],c[e+28>>2]=c[k+84>>2],c[e+32>>2]=c[k+88>>2],r=s1(i[n+68>>2],e),e=i[F+16>>2],i[e+36>>2]=r,(0|r)==2147483647)break f;i[k+112>>2]=i[E+12>>2],i[k+116>>2]=i[L+12>>2],i[k+120>>2]=i[S+12>>2],i[k+124>>2]=i[P+12>>2],i[e+24>>2]=0,i[e+16>>2]=0,i[e+20>>2]=0,a=c[e+28>>2],m=(t=u(c[L+28>>2]-a))<u(0)?u(-t):t,t=c[e+32>>2],b=u(c[L+32>>2]-t),o=u(m+(b<u(0)?u(-b):b)),m=(b=u(c[E+28>>2]-a))<u(0)?u(-b):b,b=u(c[E+32>>2]-t),s=u(m+(b<u(0)?u(-b):b)),q=+u(s+o),b=u(.5*+o/q),c[k+96>>2]=b,o=u(.5*+s/q),c[k+100>>2]=o,s=u(u(u(c[E+16>>2]*b)+u(c[L+16>>2]*o))+u(0)),c[e+16>>2]=s,g=u(u(u(c[E+20>>2]*b)+u(c[L+20>>2]*o))+u(0)),c[e+20>>2]=g,o=u(u(u(c[E+24>>2]*b)+u(c[L+24>>2]*o))+u(0)),c[e+24>>2]=o,m=(b=u(c[S+28>>2]-a))<u(0)?u(-b):b,b=u(c[S+32>>2]-t),b=u(m+(b<u(0)?u(-b):b)),m=(a=u(c[P+28>>2]-a))<u(0)?u(-a):a,a=u(c[P+32>>2]-t),a=u(m+(a<u(0)?u(-a):a)),q=+u(b+a),a=u(.5*+a/q),c[k+104>>2]=a,t=u(.5*+b/q),c[k+108>>2]=t,b=u(s+u(u(c[S+16>>2]*a)+u(c[P+16>>2]*t))),c[e+16>>2]=b,s=u(g+u(u(c[S+20>>2]*a)+u(c[P+20>>2]*t))),c[e+20>>2]=s,a=u(o+u(u(c[S+24>>2]*a)+u(c[P+24>>2]*t))),c[e+24>>2]=a,c[k+140>>2]=a,c[k+136>>2]=s,c[k+132>>2]=b,i[e+12>>2]=0,e=e+12|0,(0|(r=i[n+1736>>2]))==8?M[i[n+76>>2]](k+132|0,k+112|0,k+96|0,e):M[0|r](k+132|0,k+112|0,k+96|0,e,i[n+1896>>2]),i[e>>2]|w[n+60|0]||((0|(e=i[n+1732>>2]))==11?M[i[n+12>>2]](100156):M[0|e](100156,i[n+1896>>2]),h[n+60|0]=1),h[Y+14|0]=1,h[f+14|0]=1,h[i[i[i[f+4>>2]+4>>2]>>2]+14|0]=1}else R0(n,f)}return B=k+144|0,m2}P0(i[n+68>>2]),i[n+68>>2]=0}b2(n+1740|0,1),o2()}function w4(n){n|=0;var f=0,a=0,t=0,e=0,b=0,o=0,k=0,r=0,s=u(0),A=u(0),l=0,v=0,p=0,m=0,g=0,E=0,R=0,S=0,L=0,P=0;B=b=B-48|0,h[n+60|0]=0;i:{if(a=i[n+8>>2],(0|(f=i[a+64>>2]))!=(0|(k=a- -64|0)))for(;;){a=i[f+12>>2],e=i[f>>2],o=i[f+16>>2],t=i[i[f+4>>2]+16>>2];f:{if(!(c[o+28>>2]!=c[t+28>>2]|c[o+32>>2]!=c[t+32>>2]|i[a+12>>2]==(0|f))){i[b+24>>2]=0,i[b+28>>2]=0,i[b+16>>2]=0,i[b+20>>2]=0,t=i[287],i[b+8>>2]=i[286],i[b+12>>2]=t,t=i[285],i[b>>2]=i[284],i[b+4>>2]=t,t=i[a+16>>2],i[b+16>>2]=i[t+12>>2],i[b+20>>2]=i[o+12>>2],c[b+36>>2]=c[t+16>>2],c[b+40>>2]=c[t+20>>2],c[b+44>>2]=c[t+24>>2],i[t+12>>2]=0,t=t+12|0,(0|(o=i[n+1736>>2]))==8?M[i[n+76>>2]](b+36|0,b+16|0,b,t):M[0|o](b+36|0,b+16|0,b,t,i[n+1896>>2]),i[t>>2]||(i[t>>2]=i[b+16>>2]);a:{if(z(a,f)){if(!$(f))break a;t=i[a+12>>2];break f}break i}break i}t=a,a=f}if(i[t+12>>2]==(0|a)){if((0|a)!=(0|t)&&(e=i[e+4>>2]!=(0|t)&&(0|t)!=(0|e)?e:i[e>>2],!$(t))||(f=(0|a)==(0|e)|i[e+4>>2]==(0|a)?i[e>>2]:e,!$(a)))break i}else f=e;if((0|f)==(0|k))break}e=n,(f=H(28))?(t=f,(a=H(28))?(i[a+8>>2]=0,i[a+12>>2]=32,o=H(132),i[a>>2]=o,o?(k=H(264),i[a+4>>2]=k,k?(i[a+24>>2]=9,i[a+16>>2]=0,i[a+20>>2]=0,i[o+4>>2]=1,i[k+8>>2]=0):(T(o),T(a),a=0)):(T(a),a=0)):a=0,i[t>>2]=a,a?(t=H(128),i[f+4>>2]=t,t?(i[f+24>>2]=9,i[f+20>>2]=0,i[f+12>>2]=0,i[f+16>>2]=32):(T(i[a+4>>2]),T(i[a>>2]),T(a),T(f),f=0)):(T(f),f=0)):f=0,p=f,i[e+68>>2]=f;f:if(f){a:{a=i[n+8>>2];n:{if((0|(f=i[a>>2]))!=(0|a))for(;;){if(e=s1(p,f),i[f+36>>2]=e,(0|e)==2147483647)break n;if((0|a)==(0|(f=i[f>>2])))break}if(B=E=B-400|0,e=H(4+(f=(g=i[p+12>>2])<<2)|0),i[p+8>>2]=e,B=E+400|0,e){if(!((o=(f+e|0)-4|0)>>>0<e>>>0)){if(a=i[p+4>>2],t=1+((k=(g<<2)-4|0)>>>2|0)&7)for(f=e;i[f>>2]=a,f=f+4|0,a=a+4|0,t=t-1|0;);else f=e;if(!(k>>>0<28))for(;i[f>>2]=a,i[f+28>>2]=a+28,i[f+24>>2]=a+24,i[f+20>>2]=a+20,i[f+16>>2]=a+16,i[f+12>>2]=a+12,i[f+8>>2]=a+8,i[f+4>>2]=a+4,a=a+32|0,o>>>0>=(f=f+32|0)>>>0;);}for(i[E+4>>2]=o,i[E>>2]=e,R=2016473283,r=1;;){if((l=i[((r<<3)+E|0)-4>>2])>>>0>(k=i[(m<<3)+E>>2])+40>>>0)for(;;){for(R=W(R,1539415821)+1|0,L=i[(f=((R>>>0)%(1+(l-k>>2)>>>0)<<2)+k|0)>>2],i[f>>2]=i[k>>2],i[k>>2]=L,t=l+4|0,a=k-4|0;;){e=t,v=i[a+4>>2],o=a,f=a+4|0,t=i[v>>2],s=c[t+28>>2],S=i[L>>2],r=f;t:if(!(s<(A=c[S+28>>2]))){for(;;){if(a=f,r=f,c[t+32>>2]<=c[S+32>>2]&&s==A)break t;if(f=a+4|0,o=a,v=i[a+4>>2],t=i[v>>2],A>(s=c[t+28>>2]))break}r=f}a=r,r=i[(t=e-4|0)>>2],f=i[r>>2];t:if(!(A<(s=c[f+28>>2])))for(;;){if(!(!(c[S+32>>2]<=c[f+32>>2])|s!=A))break t;if(e=t,r=i[(t=t-4|0)>>2],f=i[r>>2],A<(s=c[f+28>>2]))break}if(i[a>>2]=r,i[t>>2]=v,!(a>>>0<t>>>0))break}if(f=i[a>>2],i[a>>2]=v,i[t>>2]=f,(a-k|0)<(l-t|0)?(f=e,t=l,l=o):(f=k,t=o,k=e),i[4+(a=(m<<3)+E|0)>>2]=t,i[a>>2]=f,m=m+1|0,!(k+40>>>0<l>>>0))break}if(r=m,l>>>0>=(e=k+4|0)>>>0)for(;;){t=i[e>>2],a=f=e;t:if(!(k>>>0>=f>>>0))for(;;){if(o=i[t>>2],s=c[o+28>>2],m=i[(a=f-4|0)>>2],v=i[m>>2],s<(A=c[v+28>>2])){a=f;break t}if(!(!(c[o+32>>2]<=c[v+32>>2])|s!=A)){a=f;break t}if(i[f>>2]=m,!(k>>>0<(f=a)>>>0))break}if(i[a>>2]=t,!(l>>>0>=(e=e+4|0)>>>0))break}if(m=r-1|0,!((0|r)>=1))break}if(i[p+20>>2]=1,i[p+16>>2]=g,p=i[p>>2],(0|(e=i[p+8>>2]))>=1)for(l=i[p+4>>2],k=i[p>>2],a=e;;){for(t=a,r=l+((m=i[k+(a<<2)>>2])<<3)|0,f=a;(0|e)<=(0|(a=f<<1))||(v=i[l+(i[k+((o=1|a)<<2)>>2]<<3)>>2],s=c[v+28>>2],g=i[l+(i[k+(a<<2)>>2]<<3)>>2],A=c[g+28>>2],!(c[v+32>>2]<=c[g+32>>2])|s!=A&&!(s<A)||(a=o)),!((0|a)>(0|e)||(o=i[r>>2],s=c[o+28>>2],v=i[k+(a<<2)>>2],R=i[(g=l+(v<<3)|0)>>2],s<(A=c[R+28>>2])|(c[o+32>>2]<=c[R+32>>2]?s==A:0)));)i[k+(f<<2)>>2]=v,i[g+4>>2]=f,f=a;if(i[k+(f<<2)>>2]=m,i[r+4>>2]=f,a=t-1|0,!((0|t)>1))break}i[p+20>>2]=1,f=1}else f=0;if(f)break a}P0(i[n+68>>2]),i[n+68>>2]=0;break f}if(a=n- -64|0,(f=H(20))?(i[f+16>>2]=10,i[f+12>>2]=n,i[f>>2]=0,i[f+8>>2]=f,i[f+4>>2]=f):f=0,i[a>>2]=f,!f)break i;if(m1(n,u(-3999999973526325e22)),m1(n,u(3999999973526325e22)),a=S0(i[n+68>>2]))for(;;){a:if(f=w1(i[n+68>>2]))for(;;){if(c[f+28>>2]!=c[a+28>>2]|c[f+32>>2]!=c[a+32>>2])break a;if(e=i[S0(i[n+68>>2])+8>>2],t=i[a+8>>2],i[b+24>>2]=0,i[b+28>>2]=0,i[b+16>>2]=0,i[b+20>>2]=0,f=i[287],i[b+8>>2]=i[286],i[b+12>>2]=f,f=i[285],i[b>>2]=i[284],i[b+4>>2]=f,f=i[t+16>>2],i[b+16>>2]=i[f+12>>2],i[b+20>>2]=i[i[e+16>>2]+12>>2],c[b+36>>2]=c[f+16>>2],c[b+40>>2]=c[f+20>>2],c[b+44>>2]=c[f+24>>2],i[f+12>>2]=0,f=f+12|0,(0|(o=i[n+1736>>2]))==8?M[i[n+76>>2]](b+36|0,b+16|0,b,f):M[0|o](b+36|0,b+16|0,b,f,i[n+1896>>2]),i[f>>2]||(i[f>>2]=i[b+16>>2]),!z(t,e))break i;if(!(f=w1(i[n+68>>2])))break}if(A0(n,a),!(a=S0(i[n+68>>2])))break}if(f=i[i[i[(e=n- -64|0)>>2]+4>>2]>>2],a=i[f>>2],i[n+72>>2]=i[a+16>>2],i[a+24>>2]=0,p2(i[f+4>>2]),T(f),a=i[e>>2],f=i[i[a+4>>2]>>2])for(;i[i[f>>2]+24>>2]=0,p2(i[f+4>>2]),T(f),a=i[e>>2],f=i[i[a+4>>2]>>2];);if((0|a)!=(0|(f=i[a+4>>2])))for(;T(f),(0|a)!=(0|(f=i[f+4>>2])););if(T(a),P0(i[n+68>>2]),P=1,n=i[n+8>>2],(0|(a=i[n+40>>2]))!=(0|(e=n+40|0)))for(;;){if(n=i[a+8>>2],a=i[a>>2],(0|n)==i[i[n+12>>2]+12>>2]&&(f=i[n+8>>2],i[f+28>>2]=i[f+28>>2]+i[n+28>>2],f=i[f+4>>2],i[f+28>>2]=i[f+28>>2]+i[i[n+4>>2]+28>>2],!$(n))){P=0;break f}if((0|a)==(0|e))break}}return B=b+48|0,0|P}b2(n+1740|0,1),o2()}function E0(n,f,a,t,e,b){var o=0,k=0,r=0,s=0,A=0,l=0,v=0,p=0,m=0,g=0,E=0,R=0,S=0,L=0,P=0,F=0,j=0;B=k=B-80|0,i[k+76>>2]=f,P=k+55|0,S=k+56|0,f=0;i:{f:for(;;){(0|m)<0||((2147483647-m|0)<(0|f)?(i[613]=61,m=-1):m=f+m|0);a:{n:{t:{if(A=i[k+76>>2],o=w[0|(f=A)])for(;;){b:{e:if(o&=255){if((0|o)!=37)break b;for(o=f;;){if(w[f+1|0]!=37)break e;if(r=f+2|0,i[k+76>>2]=r,o=o+1|0,l=w[f+2|0],f=r,(0|l)!=37)break}}else o=f;if(f=o-A|0,n&&Z(n,A,f),f)continue f;E=-1,o=1,r=k,f=i[k+76>>2],w[f+2|0]!=36|h[i[k+76>>2]+1|0]-48>>>0>=10||(E=h[f+1|0]-48|0,L=1,o=3),f=o+f|0,i[r+76>>2]=f,g=0;e:if((r=(s=h[0|f])-32|0)>>>0>31)o=f;else if(o=f,75913&(r=1<<r))for(;;){if(o=f+1|0,i[k+76>>2]=o,g|=r,(r=(s=h[f+1|0])-32|0)>>>0>=32)break e;if(f=o,!(75913&(r=1<<r)))break}e:if((0|s)!=42){if((0|(v=E1(k+76|0)))<0)break t;f=i[k+76>>2]}else{if(r=k,h[o+1|0]-48>>>0>=10||(f=i[k+76>>2],w[f+2|0]!=36)){if(L)break t;L=0,v=0,n&&(f=i[a>>2],i[a>>2]=f+4,v=i[f>>2]),f=i[k+76>>2]+1|0}else i[((h[f+1|0]<<2)+e|0)-192>>2]=10,v=i[((h[f+1|0]<<3)+t|0)-384>>2],L=1,f=f+3|0;if(i[r+76>>2]=f,(0|v)>-1)break e;v=0-v|0,g|=8192}l=-1;e:if(w[0|f]==46)if(w[f+1|0]!=42)i[k+76>>2]=f+1,l=E1(k+76|0),f=i[k+76>>2];else{if(!(h[f+2|0]-48>>>0>=10)&&(f=i[k+76>>2],w[f+3|0]==36)){i[((h[f+2|0]<<2)+e|0)-192>>2]=10,l=i[((h[f+2|0]<<3)+t|0)-384>>2],f=f+4|0,i[k+76>>2]=f;break e}if(L)break t;n?(f=i[a>>2],i[a>>2]=f+4,l=i[f>>2]):l=0,f=i[k+76>>2]+2|0,i[k+76>>2]=f}for(o=0;;){if(R=o,p=-1,h[0|f]-65>>>0>57)break i;if(s=f+1|0,i[k+76>>2]=s,o=h[0|f],f=s,!((o=w[1103+(o+W(R,58)|0)|0])-1>>>0<8))break}e:{c:{if((0|o)!=19){if(!o)break i;if((0|E)>=0){i[(E<<2)+e>>2]=o,o=i[4+(f=(E<<3)+t|0)>>2],i[k+64>>2]=i[f>>2],i[k+68>>2]=o;break c}if(!n)break a;d1(k- -64|0,o,a),s=i[k+76>>2];break e}if((0|E)>-1)break i}if(f=0,!n)continue f}r=-65537&g,o=8192&g?r:g,p=0,E=1024,g=S;e:{c:{u:{o:{A:{s:{k:{r:{l:{v:{d:{h:{p:{m:{w:{switch(f=h[s-1|0],(f=R&&(15&f)==3?-33&f:f)-88|0){case 11:break e;case 9:case 13:case 14:case 15:break c;case 27:break k;case 12:case 17:break v;case 23:break d;case 0:case 32:break h;case 24:break p;case 22:break m;case 29:break w;case 1:case 2:case 3:case 4:case 5:case 6:case 7:case 8:case 10:case 16:case 18:case 19:case 20:case 21:case 25:case 26:case 28:case 30:case 31:break n}switch(f-65|0){case 0:case 4:case 5:case 6:break c;case 2:break A;case 1:case 3:break n}if((0|f)==83)break s;break n}s=i[k+64>>2],r=i[k+68>>2],E=1024;break l}f=0;m:switch(255&R){case 0:case 1:case 6:i[i[k+64>>2]>>2]=m;continue f;case 2:o=i[k+64>>2],i[o>>2]=m,i[o+4>>2]=m>>31;continue f;case 3:r2[i[k+64>>2]>>1]=m;continue f;case 4:h[i[k+64>>2]]=m;continue f;case 7:break m;default:continue f}o=i[k+64>>2],i[o>>2]=m,i[o+4>>2]=m>>31;continue f}l=l>>>0>8?l:8,o|=8,f=120}if(A=S,F=32&f,(r=R=i[k+68>>2])|(s=i[k+64>>2]))for(;h[0|(A=A-1|0)]=F|w[1632+(15&s)|0],j=!r&s>>>0>15|(0|r)!=0,R=r,r=r>>>4|0,s=(15&R)<<28|s>>>4,j;);if(!(i[k+64>>2]|i[k+68>>2])|!(8&o))break r;E=1024+(f>>>4|0)|0,p=2;break r}if(f=S,(r=A=i[k+68>>2])|(s=i[k+64>>2]))for(;h[0|(f=f-1|0)]=7&s|48,R=!r&s>>>0>7|(0|r)!=0,A=r,r=r>>>3|0,s=(7&A)<<29|s>>>3,R;);if(A=f,!(8&o))break r;l=(0|(f=S-A|0))<(0|l)?l:f+1|0;break r}r=f=i[k+68>>2],s=i[k+64>>2],(0|f)<-1||(0|f)<=-1?(r=0-(r+((0|s)!=0)|0)|0,s=0-s|0,i[k+64>>2]=s,i[k+68>>2]=r,p=1,E=1024):2048&o?(p=1,E=1025):E=(p=1&o)?1026:1024}A=z2(s,r,S)}if(o=(0|l)>-1?-65537&o:o,r=f=i[k+68>>2],!(l|(0|(s=i[k+64>>2]))!=0|(0|f)!=0)){l=0,A=S;break n}l=(0|(f=!(r|s)+(S-A|0)|0))<(0|l)?l:f;break n}g=(0|(f=l))!=0;k:{r:{l:{v:if(!(!(3&(o=A=(o=i[k+64>>2])||1071))|!f))for(;;){if(!w[0|o])break l;if(g=(0|(f=f-1|0))!=0,!(3&(o=o+1|0)))break v;if(!f)break}if(!g)break r}l:if(!(!w[0|o]|f>>>0<4))for(;;){if((-1^(s=i[o>>2]))&s-16843009&-2139062144)break l;if(o=o+4|0,!((f=f-4|0)>>>0>3))break}if(f)for(;;){if(s=o,!w[0|o])break k;if(o=o+1|0,!(f=f-1|0))break}}s=0}g=s||l+A|0,o=r,l=s?s-A|0:l;break n}if(r=i[k+64>>2],l)break o;f=0,i2(n,32,v,0,o);break u}i[k+12>>2]=0,i[k+8>>2]=i[k+64>>2],i[k+64>>2]=k+8,l=-1,r=k+8|0}f=0;o:{for(;;){if(!(A=i[r>>2]))break o;if(!((s=(0|(A=p1(k+4|0,A)))<0)|A>>>0>l-f>>>0)){if(r=r+4|0,l>>>0>(f=f+A|0)>>>0)continue;break o}break}if(p=-1,s)break i}if(i2(n,32,v,f,o),f)for(r=0,s=i[k+64>>2];;){if(!(A=i[s>>2])||(0|(r=(A=p1(k+4|0,A))+r|0))>(0|f))break u;if(Z(n,k+4|0,A),s=s+4|0,!(f>>>0>r>>>0))break}else f=0}i2(n,32,v,f,8192^o),f=(0|f)<(0|v)?v:f;continue f}f=0|M[0|b](n,X[k+64>>3],v,l,o,f);continue f}h[k+55|0]=i[k+64>>2],l=1,A=P,o=r;break n}r=f+1|0,i[k+76>>2]=r,o=w[f+1|0],f=r}if(p=m,n)break i;if(!L)break a;for(f=1;;){if(n=i[(f<<2)+e>>2]){if(d1((f<<3)+t|0,n,a),p=1,(0|(f=f+1|0))!=10)continue;break i}break}if(p=1,f>>>0>=10)break i;for(;;){if(i[(f<<2)+e>>2])break t;if((0|(f=f+1|0))==10)break}break i}p=-1;break i}i2(n,32,f=(0|(r=(l=(0|(s=g-A|0))>(0|l)?s:l)+p|0))>(0|v)?r:v,r,o),Z(n,E,p),i2(n,48,f,r,65536^o),i2(n,48,l,s,0),Z(n,A,s),i2(n,32,f,r,8192^o);continue}break}p=0}return B=k+80|0,p}function g4(n,f,a,t,e,b){n|=0,f=+f,a|=0,t|=0,e|=0,b|=0;var o=0,k=0,r=0,s=0,A=0,l=0,v=0,p=0,m=0,g=0,E=0,R=0,S=0,L=0,P=0,F=0,j=0,q=0,Y=0,k2=0,m2=0;B=s=B-560|0,i[s+44>>2]=0,C0(+f),o=0|l2(1),l2(0),(0|o)<-1||(0|o)<=-1?(P=1,F=1034,C0(+(f=-f)),o=0|l2(1),l2(0)):2048&e?(P=1,F=1037):(F=(P=1&e)?1040:1035,m2=!P);i:if((2146435072&o)!=2146435072){S=s+16|0;f:{a:{n:{if(f=y1(f,s+44|0),(f+=f)!=0){if(o=i[s+44>>2],i[s+44>>2]=o-1,(0|(j=32|b))!=97)break n;break f}if((0|(j=32|b))==97)break f;A=i[s+44>>2],v=(0|t)<0?6:t;break a}A=o-29|0,i[s+44>>2]=A,f*=268435456,v=(0|t)<0?6:t}for(k=E=(0|A)<0?s+48|0:s+336|0;o=f<4294967296&f>=0?~~f>>>0:0,i[(t=k)>>2]=o,k=k+4|0,(f=1e9*(f-+(o>>>0)))!=0;);if((0|A)<1)t=A,o=k,r=E;else for(r=E,t=A;;){if(R=(0|t)<29?t:29,!(r>>>0>(o=k-4|0)>>>0)){for(t=R,m=0;g=o,p=0,Y=m,m=i[o>>2],l=31&t,(63&t)>>>0>=32?(k2=m<<l,l=0):(k2=(1<<l)-1&m>>>32-l,l=m<<l),p=p+k2|0,p=l>>>0>(m=Y+l|0)>>>0?p+1|0:p,Y=g,g=L0(m=A1(l=m,p,1e9),v2,1e9,0),i[Y>>2]=l-g,r>>>0<=(o=o-4|0)>>>0;);(t=m)&&(i[(r=r-4|0)>>2]=t)}for(;r>>>0<(o=k)>>>0&&!i[(k=o-4|0)>>2];);if(t=i[s+44>>2]-R|0,i[s+44>>2]=t,k=o,!((0|t)>0))break}if(k=(v+25|0)/9|0,(0|t)<=-1)for(R=k+1|0,q=(0|j)==102;;){m=(0|t)<-9?9:0-t|0;a:if(o>>>0>r>>>0){for(g=1e9>>>m|0,l=-1<<m^-1,t=0,k=r;Y=t,t=i[k>>2],i[k>>2]=Y+(t>>>m|0),t=W(g,t&l),(k=k+4|0)>>>0<o>>>0;);if(r=i[r>>2]?r:r+4|0,!t)break a;i[o>>2]=t,o=o+4|0}else r=i[r>>2]?r:r+4|0;if(t=i[s+44>>2]+m|0,i[s+44>>2]=t,o=(0|R)<o-(k=q?E:r)>>2?k+(R<<2)|0:o,!((0|t)<0))break}if(k=0,!(o>>>0<=r>>>0||(k=W(E-r>>2,9),t=10,(l=i[r>>2])>>>0<10)))for(;k=k+1|0,l>>>0>=(t=W(t,10))>>>0;);if((0|(t=(v-((0|j)==102?0:k)|0)-((0|j)==103&(0|v)!=0)|0))<(W(o-E>>2,9)-9|0)){if(p=(((l=(0|(g=t+9216|0))/9|0)<<2)+((0|A)<0?s+48|4:s+340|0)|0)-4096|0,t=10,(0|(g=g-W(l,9)|0))<=7)for(;t=W(t,10),(0|(g=g+1|0))!=8;);if(R=(g=i[p>>2])-W(t,l=(g>>>0)/(t>>>0)|0)|0,((0|(A=p+4|0))!=(0|o)||R)&&(f=(0|o)==(0|A)?1:1.5,L=(A=t>>>1|0)>>>0>R>>>0?.5:(0|A)==(0|R)?f:1.5,f=1&l?9007199254740994:9007199254740992,w[0|F]!=45|m2||(L=-L,f=-f),A=g-R|0,i[p>>2]=A,f+L!=f)){if(t=t+A|0,i[p>>2]=t,t>>>0>=1e9)for(;i[p>>2]=0,(p=p-4|0)>>>0<r>>>0&&(i[(r=r-4|0)>>2]=0),t=i[p>>2]+1|0,i[p>>2]=t,t>>>0>999999999;);if(k=W(E-r>>2,9),t=10,!((A=i[r>>2])>>>0<10))for(;k=k+1|0,A>>>0>=(t=W(t,10))>>>0;);}o=(t=p+4|0)>>>0<o>>>0?t:o}for(;l=o,!(A=o>>>0<=r>>>0)&&!i[(o=l-4|0)>>2];);if((0|j)==103){if(v=((t=(0|(o=v||1))>(0|k)&(0|k)>-5)?-1^k:-1)+o|0,b=(t?-1:-2)+b|0,!(m=8&e)){if(o=-9,!A&&(A=i[l-4>>2])&&(g=10,o=0,!((A>>>0)%10|0))){for(;t=o,o=o+1|0,!((A>>>0)%((g=W(g,10))>>>0)|0););o=-1^t}t=W(l-E>>2,9),(-33&b)!=70?(m=0,v=(0|(t=(0|(t=((t+k|0)+o|0)-9|0))>0?t:0))>(0|v)?v:t):(m=0,v=(0|(t=(0|(t=(t+o|0)-9|0))>0?t:0))>(0|v)?v:t)}}else m=8&e;if(R=(v|m)!=0,t=n,A=a,(0|(g=-33&b))==70)b=(0|k)>0?k:0;else{if((S-(o=z2((o=k>>31)+k^o,0,S))|0)<=1)for(;h[0|(o=o-1|0)]=48,(S-o|0)<2;);h[0|(q=o-2|0)]=b,h[o-1|0]=(0|k)<0?45:43,b=S-q|0}i2(t,32,A,p=1+(b+(R+(v+P|0)|0)|0)|0,e),Z(n,F,P),i2(n,48,a,p,65536^e);a:{n:{t:{if((0|g)==70){for(t=s+16|8,k=s+16|9,r=b=r>>>0>E>>>0?E:r;;){o=z2(i[r>>2],0,k);b:if((0|b)==(0|r))(0|o)==(0|k)&&(h[s+24|0]=48,o=t);else{if(s+16>>>0>=o>>>0)break b;for(;h[0|(o=o-1|0)]=48,s+16>>>0<o>>>0;);}if(Z(n,o,k-o|0),!(E>>>0>=(r=r+4|0)>>>0))break}if(o=0,!R)break n;if(Z(n,1069,1),(0|v)<1|r>>>0>=l>>>0)break t;for(;;){if((o=z2(i[r>>2],0,k))>>>0>s+16>>>0)for(;h[0|(o=o-1|0)]=48,s+16>>>0<o>>>0;);if(Z(n,o,(0|v)<9?v:9),o=v-9|0,l>>>0<=(r=r+4|0)>>>0)break n;if(t=(0|v)>9,v=o,!t)break}break n}b:if(!((0|v)<0))for(b=r>>>0<l>>>0?l:r+4|0,A=s+16|9,t=s+16|8,k=r;;){(0|A)==(0|(o=z2(i[k>>2],0,A)))&&(h[s+24|0]=48,o=t);e:if((0|k)==(0|r))Z(n,o,1),o=o+1|0,!m&&(0|v)<=0||Z(n,1069,1);else{if(s+16>>>0>=o>>>0)break e;for(;h[0|(o=o-1|0)]=48,s+16>>>0<o>>>0;);}if(Z(n,l=o,(0|(o=A-o|0))<(0|v)?o:v),v=v-o|0,b>>>0<=(k=k+4|0)>>>0)break b;if(!((0|v)>-1))break}i2(n,48,v+18|0,18,0),Z(n,q,S-q|0);break a}o=v}i2(n,48,o+9|0,9,0)}break i}if(E=(A=32&b)?F+9|0:F,!(t>>>0>11)&&(o=12-t|0)){for(L=8;L*=16,o=o-1|0;);f=w[0|E]!=45?f+L-L:-(L+(-f-L))}for((0|S)==(0|(o=z2((k=(o=i[s+44>>2])>>31)^o+k,0,S)))&&(h[s+15|0]=48,o=s+15|0),v=2|P,k=i[s+44>>2],h[0|(l=o-2|0)]=b+15,h[o-1|0]=(0|k)<0?45:43,o=8&e,r=s+16|0;b=r,m=A,k=s4(f)<2147483648?~~f:-2147483648,h[0|r]=m|w[k+1632|0],f=16*(f-+(0|k)),!(o||(0|t)>0|f!=0)|((r=b+1|0)-(s+16|0)|0)!=1||(h[b+1|0]=46,r=b+2|0),f!=0;);i2(b=n,32,o=a,p=(A=!t|((r-s|0)-18|0)>=(0|t)?(S-(l+(s+16|0)|0)|0)+r|0:2+((t+S|0)-l|0)|0)+v|0,e),Z(n,E,v),i2(n,48,a,p,65536^e),Z(n,s+16|0,t=r-(s+16|0)|0),i2(n,48,A-((b=t)+(t=S-l|0)|0)|0,0,0),Z(n,l,t)}else i2(n,32,a,p=P+3|0,-65537&e),Z(n,F,P),t=32&b,Z(n,f!=f?t?1053:1061:t?1057:1065,3);return i2(n,32,a,p,8192^e),B=s+560|0,0|((0|a)>(0|p)?a:p)}function A0(n,f){var a=0,t=0,e=0,b=0,o=0,k=u(0),r=0,s=u(0),A=0,l=0,v=0;B=o=B+-64|0,i[n+72>>2]=f,a=e=i[f+8>>2];i:{f:{a:{for(;;){if(t=i[a+24>>2])break a;if((0|e)==(0|(a=i[a+8>>2])))break}for(i[o>>2]=i[e+4>>2],e=a=i[n- -64>>2];e=i[e+4>>2],(t=i[e>>2])&&!(0|M[i[a+16>>2]](i[a+12>>2],o,t)););if(t=i[e>>2],e=i[i[i[t+4>>2]+8>>2]>>2],r=i[e>>2],b=i[t>>2],G(i[i[b+4>>2]+16>>2],f,i[b+16>>2])==u(0)){if(k=c[f+28>>2],e=i[t>>2],a=i[e+16>>2],!(k!=c[a+28>>2]|c[a+32>>2]!=c[f+32>>2])){if(t=i[f+8>>2],i[o+40>>2]=0,i[o+44>>2]=0,i[o+32>>2]=0,i[o+36>>2]=0,f=i[287],i[o+24>>2]=i[286],i[o+28>>2]=f,f=i[285],i[o+16>>2]=i[284],i[o+20>>2]=f,i[o+32>>2]=i[a+12>>2],i[o+36>>2]=i[i[t+16>>2]+12>>2],c[o+52>>2]=c[a+16>>2],c[o+56>>2]=c[a+20>>2],c[o+60>>2]=c[a+24>>2],i[a+12>>2]=0,f=a+12|0,(0|(a=i[n+1736>>2]))==8?M[i[n+76>>2]](o+52|0,o+32|0,o+16|0,f):M[0|a](o+52|0,o+32|0,o+16|0,f,i[n+1896>>2]),i[f>>2]||(i[f>>2]=i[o+32>>2]),z(e,t))break f;break i}if(b=i[e+4>>2],a=i[b+16>>2],c[a+32>>2]!=c[f+32>>2]||k!=c[a+28>>2]){if(!n2(b))break i;if(w[t+15|0]){if(!$(i[e+8>>2]))break i;h[t+15|0]=0}if(!z(i[f+8>>2],e))break i;A0(n,f);break f}for(;t=i[i[i[t+4>>2]+4>>2]>>2],(0|a)==i[i[i[t>>2]+4>>2]+16>>2];);if(e=i[i[i[t+4>>2]+8>>2]>>2],r=i[e>>2],b=i[r+4>>2],a=i[b+8>>2],w[e+15|0]){if(i[r+24>>2]=0,p2(i[e+4>>2]),T(e),!$(b))break i;b=i[i[a+4>>2]+12>>2]}if(!z(i[f+8>>2],b))break i;A=i[b+8>>2],b=a,f=a,e=i[i[a+4>>2]+16>>2],k=c[e+28>>2],r=i[a+16>>2],k<(s=c[r+28>>2])|(c[e+32>>2]<=c[r+32>>2]?k==s:0)||(f=0),U2(n,t,A,b,f,1);break f}if(l=w[t+12|0],r=i[r+4>>2],A=i[r+16>>2],k=c[A+28>>2],v=i[i[b+4>>2]+16>>2],a=t,k<(s=c[v+28>>2])||k==s&&(a=t,c[A+32>>2]<=c[v+32>>2])||(a=e),w[a+15|0]||l){n:{if((0|a)==(0|t)){if(e=d2(i[i[f+8>>2]+4>>2],i[b+12>>2]))break n;break i}if(!(e=d2(i[i[r+8>>2]+4>>2],i[f+8>>2])))break i;e=i[e+4>>2]}if(w[a+15|0]){if($(i[a>>2])){i[a>>2]=e,h[a+15|0]=0,i[e+24>>2]=a,A0(n,f);break f}break i}if(!(a=H(16))||(i[a>>2]=e,t=x0(i[n- -64>>2],i[t+4>>2],a),i[a+4>>2]=t,!t))break i;h[a+13|0]=0,h[a+14|0]=0,h[a+15|0]=0,i[e+24>>2]=a,b=i[n+56>>2],e=i[i[a>>2]+28>>2]+i[i[i[t+4>>2]>>2]+8>>2]|0,i[a+8>>2]=e;n:{t:switch(b-100130|0){case 0:t=1&e;break n;case 1:t=(0|e)!=0;break n;case 2:t=(0|e)>0;break n;case 3:t=e>>>31|0;break n;case 4:break t;default:break n}t=e+1>>>0>2}h[a+12|0]=t,A0(n,f);break f}U2(a=n,t,n=i[f+8>>2],n,0,1);break f}for(f=i[i[t>>2]+16>>2];t=i[i[i[t+4>>2]+4>>2]>>2],a=i[t>>2],(0|f)==i[a+16>>2];);if(w[t+15|0]&&(!(f=d2(i[i[i[i[i[t+4>>2]+8>>2]>>2]>>2]+4>>2],i[a+12>>2]))||!$(i[t>>2])||(i[t>>2]=f,h[t+15|0]=0,i[f+24>>2]=t,!(t=i[i[i[t+4>>2]+4>>2]>>2]))))break i;if(f=i[i[i[t+4>>2]+8>>2]>>2],a=i[f>>2],f=a0(n,f,0),(0|a)!=(0|(e=i[f+8>>2])))U2(n,t,e,a,a,1);else{if(e=i[t>>2],A=i[i[i[t+4>>2]+8>>2]>>2],r=i[A>>2],i[i[e+4>>2]+16>>2]!=i[i[r+4>>2]+16>>2]&&o1(n,t),v=1,b=i[n+72>>2],k=c[b+28>>2],l=i[e+16>>2],!(k!=c[l+28>>2]|c[l+32>>2]!=c[b+32>>2])){if(!z(i[i[a+4>>2]+12>>2],e))break i;for(a=i[i[t>>2]+16>>2];t=i[i[i[t+4>>2]+4>>2]>>2],b=i[t>>2],(0|a)==i[b+16>>2];);if(w[t+15|0]&&(!(a=d2(i[i[i[i[i[t+4>>2]+8>>2]>>2]>>2]+4>>2],i[b+12>>2]))||!$(i[t>>2])||(i[t>>2]=a,h[t+15|0]=0,i[a+24>>2]=t,!(t=i[i[i[t+4>>2]+4>>2]>>2]))))break i;b=i[i[i[t+4>>2]+8>>2]>>2],a=i[b>>2],a0(n,b,A),b=i[n+72>>2],k=c[b+28>>2],v=0}a:{if(s=k,l=i[r+16>>2],s!=(k=c[l+28>>2])|c[l+32>>2]!=c[b+32>>2]){if(v)break a}else{if(!z(f,i[i[r+4>>2]+12>>2]))break i;f=a0(n,A,0)}U2(n,t,i[f+8>>2],a,a,1);break f}if(a=i[e+16>>2],s=c[a+28>>2],!(c[l+32>>2]<=c[a+32>>2])|k!=s&&!(s>k)||(e=i[i[r+4>>2]+12>>2]),!(f=d2(i[i[f+8>>2]+4>>2],e)))break i;U2(n,t,f,a=i[f+8>>2],a,0),h[i[i[f+4>>2]+24>>2]+15|0]=1,u1(n,t)}}return void(B=o- -64|0)}b2(n+1740|0,1),o2()}function b1(n){n|=0;var f=0,a=0,t=0,e=u(0),b=u(0),o=0,k=0,r=u(0),s=u(0),A=u(0),l=u(0),v=0,p=u(0),m=0,g=u(0),E=u(0),R=u(0),S=u(0),L=u(0),P=u(0),F=u(0),j=0,q=0,Y=u(0),k2=u(0),m2=0,n0=0,T0=0,U0=0,F0=0,j0=0,O0=0,D0=0;m=i[n+8>>2],f=B-80|0,b=c[n+16>>2],c[f+8>>2]=b,E=c[n+20>>2],c[f+12>>2]=E,R=c[n+24>>2],c[f+16>>2]=R;i:if(m2=b==u(0)&E==u(0)&R==u(0)){if(i[f+76>>2]=-42943038,i[f+68>>2]=-42943038,i[f+72>>2]=-42943038,i[f+64>>2]=2104540610,i[f+56>>2]=2104540610,i[f+60>>2]=2104540610,n0=(0|(v=i[m>>2]))==(0|m))g=u(-19999999867631625e21),r=u(19999999867631625e21),s=u(19999999867631625e21),p=u(-19999999867631625e21),A=u(19999999867631625e21),l=u(-19999999867631625e21);else{for(S=u(19999999867631625e21),L=u(-19999999867631625e21),P=u(-19999999867631625e21),F=u(19999999867631625e21),Y=u(-19999999867631625e21),k2=u(19999999867631625e21),g=u(-19999999867631625e21),r=u(19999999867631625e21),l=u(-19999999867631625e21),A=u(19999999867631625e21),p=u(-19999999867631625e21),s=u(19999999867631625e21),a=v;g=(t=(e=c[a+24>>2])>g)?e:g,L=t?e:L,r=(j=e<r)?e:r,S=j?e:S,l=(o=(e=c[a+20>>2])>l)?e:l,P=o?e:P,A=(q=e<A)?e:A,F=q?e:F,p=(k=(e=c[a+16>>2])>p)?e:p,Y=k?e:Y,T0=k?a:T0,s=(k=e<s)?e:s,k2=k?e:k2,U0=k?a:U0,F0=t?a:F0,j0=j?a:j0,O0=o?a:O0,D0=q?a:D0,(0|m)!=(0|(a=i[a>>2])););i[f+20>>2]=U0,c[f+56>>2]=k2,c[f+68>>2]=Y,i[f+32>>2]=T0,c[f+60>>2]=F,i[f+24>>2]=D0,c[f+72>>2]=P,i[f+36>>2]=O0,c[f+64>>2]=S,i[f+28>>2]=j0,c[f+76>>2]=L,i[f+40>>2]=F0}if(a=2,t=(o=u(l-A)>u(p-s))<<2,t=u(g-r)>u(c[t+(f+68|0)>>2]-c[t+(f+56|0)>>2])?2:o,c[(o=t<<2)+(f+56|0)>>2]>=c[o+(f+68|0)>>2])i[f+8>>2]=0,i[f+12>>2]=0;else{if(a=i[(t<<=2)+(f+20|0)>>2],t=i[t+(f+32|0)>>2],L=c[t+16>>2],A=u(c[a+16>>2]-L),c[f+44>>2]=A,P=c[t+20>>2],l=u(c[a+20>>2]-P),c[f+48>>2]=l,F=c[t+24>>2],e=u(c[a+24>>2]-F),c[f+52>>2]=e,!n0){for(S=u(0),a=v;r=u(c[a+20>>2]-P),s=u(c[a+16>>2]-L),g=u(u(A*r)-u(l*s)),p=u(c[a+24>>2]-F),r=u(u(l*p)-u(e*r)),s=u(u(e*s)-u(A*p)),(p=u(u(g*g)+u(u(r*r)+u(s*s))))>S&&(R=g,E=s,S=p,b=r),(0|m)!=(0|(a=i[a>>2])););if(c[f+16>>2]=R,c[f+12>>2]=E,c[f+8>>2]=b,!(S<=u(0)))break i}i[f+16>>2]=0,i[f+8>>2]=0,i[f+12>>2]=0,a=(l<u(0)?u(-l):l)>(A<u(0)?u(-A):A),b=c[(f+44|0)+(a<<2)>>2],a=(e<u(0)?u(-e):e)>(b<u(0)?u(-b):b)?2:a}i[(f+8|0)+(a<<2)>>2]=1065353216,R=c[f+16>>2],b=c[f+8>>2],E=c[f+12>>2]}else v=i[m>>2];if(t=(E<u(0)?u(-E):E)>(b<u(0)?u(-b):b),b=c[(f+8|0)+(t<<2)>>2],a=n+28|0,t=(R<u(0)?u(-R):R)>(b<u(0)?u(-b):b)?2:t,i[a+(o=t<<2)>>2]=0,i[(k=(t+1>>>0)%3<<2)+a>>2]=1065353216,i[(t=(t+2>>>0)%3<<2)+a>>2]=0,i[(a=n+40|0)+o>>2]=0,f=c[o+(f+8|0)>>2]>u(0),c[a+k>>2]=u(f?-0:0),c[a+t>>2]=u(f?1:-1),!(t=(0|m)==(0|v)))for(a=v;f=i[a+20>>2],i[a+28>>2]=i[a+16>>2],i[a+32>>2]=f,(0|m)!=(0|(a=i[a>>2])););if(m2&&(0|(f=i[m+40>>2]))!=(0|(o=m+40|0))){for(b=u(0);;){if(k=i[f+8>>2],i[(a=k)+28>>2]>=1)for(;j=i[a+16>>2],q=i[i[a+4>>2]+16>>2],b=u(b+u(u(c[j+28>>2]-c[q+28>>2])*u(c[j+32>>2]+c[q+32>>2]))),(0|k)!=(0|(a=i[a+12>>2])););if((0|o)==(0|(f=i[f>>2])))break}if(b<u(0)){if(!t)for(;c[v+32>>2]=-c[v+32>>2],(0|(v=i[v>>2]))!=(0|m););c[n+40>>2]=-c[n+40>>2],c[n+44>>2]=-c[n+44>>2],c[n+48>>2]=-c[n+48>>2]}}}function T(n){var f=0,a=0,t=0,e=0,b=0,o=0,k=0,r=0,s=0;i:if(n|=0){b=(t=n-8|0)+(n=-8&(f=i[n-4>>2]))|0;f:if(!(1&f)){if(!(3&f)||(t=t-(f=i[t>>2])|0)>>>0<B2[618])break i;if(n=n+f|0,i[619]==(0|t)){if((3&(f=i[b+4>>2]))==3)return i[616]=n,i[b+4>>2]=-2&f,i[t+4>>2]=1|n,void(i[n+t>>2]=n)}else{if(f>>>0<=255){if(e=i[t+8>>2],f=f>>>3|0,(0|(a=i[t+12>>2]))==(0|e)){r=2456,s=i[614]&f2(f),i[r>>2]=s;break f}i[e+12>>2]=a,i[a+8>>2]=e;break f}if(k=i[t+24>>2],(0|t)==(0|(f=i[t+12>>2])))if((a=i[(e=t+20|0)>>2])||(a=i[(e=t+16|0)>>2])){for(;o=e,(a=i[(e=(f=a)+20|0)>>2])||(e=f+16|0,a=i[f+16>>2]););i[o>>2]=0}else f=0;else a=i[t+8>>2],i[a+12>>2]=f,i[f+8>>2]=a;if(!k)break f;e=i[t+28>>2];a:{if(i[(a=2760+(e<<2)|0)>>2]==(0|t)){if(i[a>>2]=f,f)break a;r=2460,s=i[615]&f2(e),i[r>>2]=s;break f}if(i[k+(i[k+16>>2]==(0|t)?16:20)>>2]=f,!f)break f}if(i[f+24>>2]=k,(a=i[t+16>>2])&&(i[f+16>>2]=a,i[a+24>>2]=f),!(a=i[t+20>>2]))break f;i[f+20>>2]=a,i[a+24>>2]=f}}if(!(t>>>0>=b>>>0)&&1&(f=i[b+4>>2])){f:{if(!(2&f)){if(i[620]==(0|b)){if(i[620]=t,n=i[617]+n|0,i[617]=n,i[t+4>>2]=1|n,i[619]!=(0|t))break i;return i[616]=0,void(i[619]=0)}if(i[619]==(0|b))return i[619]=t,n=i[616]+n|0,i[616]=n,i[t+4>>2]=1|n,void(i[n+t>>2]=n);n=(-8&f)+n|0;a:if(f>>>0<=255){if(e=i[b+8>>2],f=f>>>3|0,(0|(a=i[b+12>>2]))==(0|e)){r=2456,s=i[614]&f2(f),i[r>>2]=s;break a}i[e+12>>2]=a,i[a+8>>2]=e}else{if(k=i[b+24>>2],(0|b)==(0|(f=i[b+12>>2])))if((a=i[(e=b+20|0)>>2])||(a=i[(e=b+16|0)>>2])){for(;o=e,(a=i[(e=(f=a)+20|0)>>2])||(e=f+16|0,a=i[f+16>>2]););i[o>>2]=0}else f=0;else a=i[b+8>>2],i[a+12>>2]=f,i[f+8>>2]=a;if(k){e=i[b+28>>2];n:{if(i[(a=2760+(e<<2)|0)>>2]==(0|b)){if(i[a>>2]=f,f)break n;r=2460,s=i[615]&f2(e),i[r>>2]=s;break a}if(i[k+(i[k+16>>2]==(0|b)?16:20)>>2]=f,!f)break a}i[f+24>>2]=k,(a=i[b+16>>2])&&(i[f+16>>2]=a,i[a+24>>2]=f),(a=i[b+20>>2])&&(i[f+20>>2]=a,i[a+24>>2]=f)}}if(i[t+4>>2]=1|n,i[n+t>>2]=n,i[619]!=(0|t))break f;return void(i[616]=n)}i[b+4>>2]=-2&f,i[t+4>>2]=1|n,i[n+t>>2]=n}if(n>>>0<=255)return f=2496+((n=n>>>3|0)<<3)|0,(a=i[614])&(n=1<<n)?n=i[f+8>>2]:(i[614]=n|a,n=f),i[f+8>>2]=t,i[n+12>>2]=t,i[t+12>>2]=f,void(i[t+8>>2]=n);e=31,i[t+16>>2]=0,i[t+20>>2]=0,n>>>0<=16777215&&(f=n>>>8|0,f<<=o=f+1048320>>>16&8,e=28+((f=((f<<=e=f+520192>>>16&4)<<(a=f+245760>>>16&2)>>>15|0)-(a|e|o)|0)<<1|n>>>f+21&1)|0),i[t+28>>2]=e,o=2760+(e<<2)|0;f:{a:{if((a=i[615])&(f=1<<e)){for(e=n<<((0|e)==31?0:25-(e>>>1|0)|0),f=i[o>>2];;){if(a=f,(-8&i[f+4>>2])==(0|n))break a;if(f=e>>>29|0,e<<=1,!(f=i[16+(o=a+(4&f)|0)>>2]))break}i[o+16>>2]=t,i[t+24>>2]=a}else i[615]=f|a,i[o>>2]=t,i[t+24>>2]=o;i[t+12>>2]=t,i[t+8>>2]=t;break f}n=i[a+8>>2],i[n+12>>2]=t,i[a+8>>2]=t,i[t+24>>2]=0,i[t+12>>2]=a,i[t+8>>2]=n}n=i[622]-1|0,i[622]=n||-1}}}function y4(n,f){f|=0;var a=0,t=0,e=0,b=0,o=0,k=0,r=0,s=0,A=0,l=0,v=0,p=0,m=0,g=0,E=0,R=0,S=0;if(B=k=B-16|0,i[84+(n|=0)>>2]=0,(0|(s=i[f+40>>2]))!=(0|(m=f+40|0)))for(f=s;h[f+20|0]=0,(0|m)!=(0|(f=i[f>>2])););if((0|s)!=(0|m)){for(;;){if(!(w[s+20|0]|!w[s+21|0])){if(o=i[s+8>>2],w[n+80|0])f=1,a=1;else{e=0,t=0,f=0,b=i[(a=o)+20>>2];i:if(w[b+21|0])for(;;){if(w[(f=b)+20|0]){f=t;break i}if(h[f+20|0]=1,i[f+16>>2]=t,e=e+1|0,t=f,a=i[a+8>>2],b=i[a+20>>2],!w[b+21|0])break}t=i[o+4>>2],b=i[t+20>>2];i:{f:if(!w[b+21|0]|w[b+20|0]){if(E=o,!f)break i}else for(a=f;;){if(h[(f=b)+20|0]=1,i[f+16>>2]=a,e=e+1|0,E=i[t+12>>2],t=i[E+4>>2],b=i[t+20>>2],!w[b+21|0])break f;if(a=f,w[b+20|0])break}for(;h[f+20|0]=0,f=i[f+16>>2];);}R=(0|e)>1,b=0,a=0,f=0,v=i[o+12>>2],t=i[(r=v)+20>>2];i:if(w[t+21|0])for(;;){if(w[(f=t)+20|0]){f=a;break i}if(h[f+20|0]=1,i[f+16>>2]=a,b=b+1|0,a=f,r=i[r+8>>2],t=i[r+20>>2],!w[t+21|0])break}g=R?e:1,t=i[v+4>>2],e=i[t+20>>2];i:{f:if(!w[e+21|0]|w[e+20|0]){if(!f)break i}else for(a=f;;){if(h[(f=e)+20|0]=1,i[f+16>>2]=a,b=b+1|0,v=i[t+12>>2],t=i[v+4>>2],e=i[t+20>>2],!w[e+21|0])break f;if(a=f,w[e+20|0])break}for(;h[f+20|0]=0,f=i[f+16>>2];);}S=(0|b)>(0|g),e=0,a=0,f=0,p=i[i[o+8>>2]+4>>2],t=i[(r=p)+20>>2];i:if(w[t+21|0])for(;;){if(w[(f=t)+20|0]){f=a;break i}if(h[f+20|0]=1,i[f+16>>2]=a,e=e+1|0,a=f,r=i[r+8>>2],t=i[r+20>>2],!w[t+21|0])break}A=S?b:g,t=i[p+4>>2],b=i[t+20>>2];i:{f:if(!w[b+21|0]|w[b+20|0]){if(!f)break i}else for(a=f;;){if(h[(f=b)+20|0]=1,i[f+16>>2]=a,e=e+1|0,p=i[t+12>>2],t=i[p+4>>2],b=i[t+20>>2],!w[b+21|0])break f;if(a=f,w[b+20|0])break}for(;h[f+20|0]=0,f=i[f+16>>2];);}M0(k,o),r=i[k+8>>2],g=i[k+4>>2],l=i[k>>2],M0(k,i[o+12>>2]),b=i[k+8>>2],t=i[k+4>>2],a=i[k>>2],M0(k,i[i[o+8>>2]+4>>2]),f=e,(0|(f=(l=(0|(f=(A=(0|(f=(e=(0|e)>(0|A))?f:A))<(0|l))?l:f))<(0|a))?a:f))>=(0|(a=i[k>>2]))?(o=l?t:A?g:e?p:S?v:R?E:o,a=l?b:A?r:e||R|S?2:1):(o=i[k+4>>2],f=a,a=i[k+8>>2])}M[0|a](n,o,f)}if((0|m)==(0|(s=i[s>>2])))break}if(e=i[n+84>>2]){for((0|(f=i[n+1716>>2]))==3?M[i[n+88>>2]](4):M[0|f](4,i[n+1896>>2]),b=-1;;){for(f=i[e+8>>2];w[n+80|0]&&(0|(a=!(o=w[i[i[f+4>>2]+20>>2]+21|0])))!=(0|b)&&((0|(t=i[n+1720>>2]))==4?M[i[n+92>>2]](!o):M[0|t](!o,i[n+1896>>2]),b=a),(0|(a=i[n+1724>>2]))==5?M[i[n+96>>2]](i[i[f+16>>2]+12>>2]):M[0|a](i[i[f+16>>2]+12>>2],i[n+1896>>2]),(0|(f=i[f+12>>2]))!=i[e+8>>2];);if(!(e=i[e+16>>2]))break}(0|(f=i[n+1728>>2]))==6?M[i[n+100>>2]]():M[0|f](i[n+1896>>2]),i[n+84>>2]=0}}B=k+16|0}function k1(n,f){var a=0,t=0,e=0,b=0,o=0,k=0,r=0,s=0;b=n+f|0;i:{f:if(!(1&(a=i[n+4>>2]))){if(!(3&a))break i;f=(a=i[n>>2])+f|0;a:{if((0|(n=n-a|0))!=i[619]){if(a>>>0<=255){if(e=i[n+8>>2],a=a>>>3|0,(0|(t=i[n+12>>2]))!=(0|e))break a;r=2456,s=i[614]&f2(a),i[r>>2]=s;break f}if(k=i[n+24>>2],(0|(a=i[n+12>>2]))==(0|n))if((t=i[(e=n+20|0)>>2])||(t=i[(e=n+16|0)>>2])){for(;o=e,(t=i[(e=(a=t)+20|0)>>2])||(e=a+16|0,t=i[a+16>>2]););i[o>>2]=0}else a=0;else t=i[n+8>>2],i[t+12>>2]=a,i[a+8>>2]=t;if(!k)break f;e=i[n+28>>2];n:{if(i[(t=2760+(e<<2)|0)>>2]==(0|n)){if(i[t>>2]=a,a)break n;r=2460,s=i[615]&f2(e),i[r>>2]=s;break f}if(i[k+(i[k+16>>2]==(0|n)?16:20)>>2]=a,!a)break f}if(i[a+24>>2]=k,(t=i[n+16>>2])&&(i[a+16>>2]=t,i[t+24>>2]=a),!(t=i[n+20>>2]))break f;i[a+20>>2]=t,i[t+24>>2]=a;break f}if((3&(a=i[b+4>>2]))!=3)break f;return i[616]=f,i[b+4>>2]=-2&a,i[n+4>>2]=1|f,void(i[b>>2]=f)}i[e+12>>2]=t,i[t+8>>2]=e}f:{if(!(2&(a=i[b+4>>2]))){if(i[620]==(0|b)){if(i[620]=n,f=i[617]+f|0,i[617]=f,i[n+4>>2]=1|f,i[619]!=(0|n))break i;return i[616]=0,void(i[619]=0)}if(i[619]==(0|b))return i[619]=n,f=i[616]+f|0,i[616]=f,i[n+4>>2]=1|f,void(i[n+f>>2]=f);f=(-8&a)+f|0;a:if(a>>>0<=255){if(e=i[b+8>>2],a=a>>>3|0,(0|(t=i[b+12>>2]))==(0|e)){r=2456,s=i[614]&f2(a),i[r>>2]=s;break a}i[e+12>>2]=t,i[t+8>>2]=e}else{if(k=i[b+24>>2],(0|b)==(0|(a=i[b+12>>2])))if((e=i[(t=b+20|0)>>2])||(e=i[(t=b+16|0)>>2])){for(;o=t,(e=i[(t=(a=e)+20|0)>>2])||(t=a+16|0,e=i[a+16>>2]););i[o>>2]=0}else a=0;else t=i[b+8>>2],i[t+12>>2]=a,i[a+8>>2]=t;if(k){e=i[b+28>>2];n:{if(i[(t=2760+(e<<2)|0)>>2]==(0|b)){if(i[t>>2]=a,a)break n;r=2460,s=i[615]&f2(e),i[r>>2]=s;break a}if(i[k+(i[k+16>>2]==(0|b)?16:20)>>2]=a,!a)break a}i[a+24>>2]=k,(t=i[b+16>>2])&&(i[a+16>>2]=t,i[t+24>>2]=a),(t=i[b+20>>2])&&(i[a+20>>2]=t,i[t+24>>2]=a)}}if(i[n+4>>2]=1|f,i[n+f>>2]=f,i[619]!=(0|n))break f;return void(i[616]=f)}i[b+4>>2]=-2&a,i[n+4>>2]=1|f,i[n+f>>2]=f}if(f>>>0<=255)return a=2496+((f=f>>>3|0)<<3)|0,(t=i[614])&(f=1<<f)?f=i[a+8>>2]:(i[614]=f|t,f=a),i[a+8>>2]=n,i[f+12>>2]=n,i[n+12>>2]=a,void(i[n+8>>2]=f);e=31,i[n+16>>2]=0,i[n+20>>2]=0,f>>>0<=16777215&&(a=f>>>8|0,a<<=o=a+1048320>>>16&8,e=28+((a=((a<<=e=a+520192>>>16&4)<<(t=a+245760>>>16&2)>>>15|0)-(t|e|o)|0)<<1|f>>>a+21&1)|0),i[n+28>>2]=e,o=2760+(e<<2)|0;f:{if((t=i[615])&(a=1<<e)){for(e=f<<((0|e)==31?0:25-(e>>>1|0)|0),a=i[o>>2];;){if(t=a,(-8&i[a+4>>2])==(0|f))break f;if(a=e>>>29|0,e<<=1,!(a=i[16+(o=t+(4&a)|0)>>2]))break}i[o+16>>2]=n,i[n+24>>2]=t}else i[615]=a|t,i[o>>2]=n,i[n+24>>2]=o;return i[n+12>>2]=n,void(i[n+8>>2]=n)}f=i[t+8>>2],i[f+12>>2]=n,i[t+8>>2]=n,i[n+24>>2]=0,i[n+12>>2]=t,i[n+8>>2]=f}}function R0(n,f){var a=0,t=0,e=0,b=u(0),o=0,k=0,r=u(0),s=0,A=0,l=0,v=0,p=0,m=0,g=0,E=0,R=0,S=0,L=0,P=0,F=0,j=0;B=e=B-48|0;i:{l=i[f>>2],k=i[l+16>>2],r=c[k+28>>2],a=i[i[i[f+4>>2]+8>>2]>>2],p=i[a>>2],t=i[p+16>>2],b=c[t+28>>2];f:{if(!(!(c[k+32>>2]<=c[t+32>>2])|r!=b)||r<b){if(G(i[i[p+4>>2]+16>>2],k,t)>u(0))break f;if(k=i[l+16>>2],t=i[p+16>>2],c[k+32>>2]!=c[t+32>>2]||c[k+28>>2]!=c[t+28>>2]){if(!n2(i[p+4>>2])||!z(l,i[i[p+4>>2]+12>>2]))break i;S=1,h[a+14|0]=1,h[f+14|0]=1;break f}if(S=1,(0|t)==(0|k))break f;if(o=i[n+68>>2],(0|(f=i[k+36>>2]))>=0){if(m=i[o>>2],s=i[m>>2],k=f,A=i[m+4>>2],f=i[4+(L=A+(f<<3)|0)>>2],R=i[m+8>>2],E=i[s+(R<<2)>>2],i[s+(f<<2)>>2]=E,i[4+(P=(E<<3)+A|0)>>2]=f,F=R-1|0,i[m+8>>2]=F,(0|f)<(0|R)){a:{if((0|f)<2||(a=i[(i[s+(f<<1&-4)>>2]<<3)+A>>2],b=c[a+28>>2],g=i[(E<<3)+A>>2],b<(r=c[g+28>>2])||!(!(c[a+32>>2]<=c[g+32>>2])|r!=b)))for(j=(E<<3)+A|0;;){if((0|F)<=(0|(a=f<<1))||(v=i[(i[s+((t=1|a)<<2)>>2]<<3)+A>>2],r=c[v+28>>2],o=i[(i[s+(a<<2)>>2]<<3)+A>>2],b=c[o+28>>2],!(c[v+32>>2]<=c[o+32>>2])|r!=b&&!(r<b)||(a=t)),(0|a)>=(0|R)){a=f;break a}if(g=i[j>>2],r=c[g+28>>2],v=i[s+(a<<2)>>2],t=i[(o=(v<<3)+A|0)>>2],r<(b=c[t+28>>2])){a=f;break a}if(!(!(c[g+32>>2]<=c[t+32>>2])|r!=b)){a=f;break a}i[s+(f<<2)>>2]=v,i[o+4>>2]=f,f=a}for(;;){if(v=i[s+((a=f>>1)<<2)>>2],t=i[(o=(v<<3)+A|0)>>2],(b=c[t+28>>2])<r){a=f;break a}if(!(!(c[t+32>>2]<=c[g+32>>2])|r!=b)){a=f;break a}if(i[s+(f<<2)>>2]=v,i[o+4>>2]=f,!((f=a)>>>0>1))break}}i[s+(a<<2)>>2]=E,i[P+4>>2]=a}i[L>>2]=0,i[L+4>>2]=i[m+16>>2],i[m+16>>2]=k}else{i[i[o+4>>2]+((-1^f)<<2)>>2]=0;a:if(!((0|(f=i[o+12>>2]))<1))for(k=i[o+8>>2];;){if(i[i[k+((a=f-1|0)<<2)>>2]>>2])break a;if(i[o+12>>2]=a,t=(0|f)>1,f=a,!t)break}}if(a=i[i[p+4>>2]+12>>2],i[e+24>>2]=0,i[e+28>>2]=0,i[e+16>>2]=0,i[e+20>>2]=0,f=i[287],i[e+8>>2]=i[286],i[e+12>>2]=f,f=i[285],i[e>>2]=i[284],i[e+4>>2]=f,f=i[a+16>>2],i[e+16>>2]=i[f+12>>2],i[e+20>>2]=i[i[l+16>>2]+12>>2],c[e+36>>2]=c[f+16>>2],c[e+40>>2]=c[f+20>>2],c[e+44>>2]=c[f+24>>2],i[f+12>>2]=0,t=f+12|0,(0|(f=i[n+1736>>2]))==8?M[i[n+76>>2]](e+36|0,e+16|0,e,t):M[0|f](e+36|0,e+16|0,e,t,i[n+1896>>2]),i[t>>2]||(i[t>>2]=i[e+16>>2]),z(a,l))break f;break i}if(!(G(i[i[l+4>>2]+16>>2],t,k)<u(0))&&(S=1,h[f+14|0]=1,h[i[i[i[f+4>>2]+4>>2]>>2]+14|0]=1,!n2(i[l+4>>2])||!z(i[i[p+4>>2]+12>>2],l)))break i}return B=e+48|0,S}b2(n+1740|0,1),o2()}function C4(n){n|=0;var f=0,a=0,t=u(0),e=u(0),b=u(0),o=u(0),k=u(0),r=u(0),s=0,A=0,l=0,v=u(0),p=u(0),m=0,g=u(0),E=u(0),R=u(0),S=u(0),L=u(0),P=0,F=0,j=u(0),q=u(0),Y=u(0);i:{f:if(!((0|(l=i[n+112>>2]))<3)){if(s=(F=n+116|0)+(l<<4)|0,o=c[n+24>>2],k=c[n+16>>2],r=c[n+20>>2],o!=u(0)||k!=u(0)|r!=u(0))g=c[n+124>>2],b=u(c[n+140>>2]-g),E=c[n+120>>2],v=u(c[n+136>>2]-E),R=c[n+116>>2],p=u(c[n+132>>2]-R);else{for(a=n+148|0,o=u(0),r=u(0),k=u(0),f=n+132|0,R=c[n+116>>2],t=p=u(c[f>>2]-R),E=c[n+120>>2],e=v=u(c[n+136>>2]-E),g=c[n+124>>2],S=b=u(c[n+140>>2]-g);L=u(c[f+20>>2]-E),j=u(c[a>>2]-R),q=u(u(t*L)-u(e*j)),Y=u(c[f+24>>2]-g),e=u(u(e*Y)-u(S*L)),t=u(u(S*j)-u(t*Y)),u(u(o*q)+u(u(k*e)+u(r*t)))>=u(0)?(r=u(r+t),k=u(k+e),o=u(o+q)):(r=u(r-t),k=u(k-e),o=u(o-q)),t=j,e=L,S=Y,s>>>0>(a=(f=a)+16|0)>>>0;);if((0|l)<3)break f}for(a=n+148|0,f=m=n+132|0;;){t=b,b=v,P=f,e=p,v=u(c[f+20>>2]-E),p=u(c[(f=a)>>2]-R),L=u(o*u(u(e*v)-u(b*p))),S=b,b=u(c[P+24>>2]-g);a:if((t=u(L+u(u(k*u(u(S*b)-u(t*v)))+u(r*u(u(t*p)-u(e*b))))))!=u(0)){if(t>u(0)){if(a=0,P=(0|A)<0,A=1,!P)break a;break i}if(a=0,P=(0|A)>0,A=-1,P)break i}if(!(s>>>0>(a=f+16|0)>>>0))break}switch(a=0,0|A){case 2:break i;case 0:break f}a=1;a:{n:switch(i[n+56>>2]-100132|0){case 0:if((0|A)>=0)break a;break f;case 2:break i;case 1:break n;default:break a}if((0|A)>0)break f}(0|(f=i[n+1716>>2]))==3?M[i[n+88>>2]](w[n+81|0]?2:(0|l)<4?4:6):M[0|f](w[n+81|0]?2:(0|l)<4?4:6,i[n+1896>>2]),(0|(f=i[n+1724>>2]))==5?M[i[n+96>>2]](i[n+128>>2]):M[0|f](i[n+128>>2],i[n+1896>>2]);a:if((0|A)<=0){if(F>>>0>=(f=s-16|0)>>>0)break a;for(;(0|(a=i[n+1724>>2]))==5?M[i[n+96>>2]](i[s-4>>2]):M[0|a](i[s-4>>2],i[n+1896>>2]),s=f,F>>>0<(f=f-16|0)>>>0;);}else if(!((0|l)<2))for(;(0|(f=i[n+1724>>2]))==5?M[i[n+96>>2]](i[m+12>>2]):M[0|f](i[m+12>>2],i[n+1896>>2]),(m=m+16|0)>>>0<s>>>0;);(0|(f=i[n+1728>>2]))==6?M[i[n+100>>2]]():M[0|f](i[n+1896>>2])}a=1}return 0|a}function l0(n,f){var a=0,t=0,e=0,b=0,o=0,k=0,r=0,s=0,A=0,l=0,v=0,p=0,m=0;if(!n)return H(f);if(f>>>0>=4294967232)return i[613]=48,0;o=f>>>0<11?16:f+11&-8,e=-8&(s=i[4+(b=n-8|0)>>2]);i:if(3&s){k=e+b|0;f:if(e>>>0>=o>>>0){if((t=e-o|0)>>>0<16)break f;i[b+4>>2]=1&s|o|2,i[4+(a=b+o|0)>>2]=3|t,i[k+4>>2]=1|i[k+4>>2],k1(a,t)}else if(i[620]!=(0|k))if(i[619]!=(0|k)){if(2&(t=i[k+4>>2])||(A=e+(-8&t)|0)>>>0<o>>>0)break i;v=A-o|0;a:if(t>>>0<=255){if(e=i[k+8>>2],a=t>>>3|0,(0|(t=i[k+12>>2]))==(0|e)){p=2456,m=i[614]&f2(a),i[p>>2]=m;break a}i[e+12>>2]=t,i[t+8>>2]=e}else{if(l=i[k+24>>2],(0|(r=i[k+12>>2]))==(0|k))if((a=i[(e=k+20|0)>>2])||(a=i[(e=k+16|0)>>2])){for(;t=e,r=a,(a=i[(e=a+20|0)>>2])||(e=r+16|0,a=i[r+16>>2]););i[t>>2]=0}else r=0;else a=i[k+8>>2],i[a+12>>2]=r,i[r+8>>2]=a;if(l){t=i[k+28>>2];n:{if(i[(a=2760+(t<<2)|0)>>2]==(0|k)){if(i[a>>2]=r,r)break n;p=2460,m=i[615]&f2(t),i[p>>2]=m;break a}if(i[(i[l+16>>2]==(0|k)?16:20)+l>>2]=r,!r)break a}i[r+24>>2]=l,(a=i[k+16>>2])&&(i[r+16>>2]=a,i[a+24>>2]=r),(a=i[k+20>>2])&&(i[r+20>>2]=a,i[a+24>>2]=r)}}v>>>0<=15?(i[b+4>>2]=1&s|A|2,i[4+(a=b+A|0)>>2]=1|i[a+4>>2]):(i[b+4>>2]=1&s|o|2,i[4+(t=b+o|0)>>2]=3|v,i[4+(a=b+A|0)>>2]=1|i[a+4>>2],k1(t,v))}else{if((t=e+i[616]|0)>>>0<o>>>0)break i;(a=t-o|0)>>>0>=16?(i[b+4>>2]=1&s|o|2,i[4+(e=b+o|0)>>2]=1|a,i[(t=t+b|0)>>2]=a,i[t+4>>2]=-2&i[t+4>>2]):(i[b+4>>2]=t|1&s|2,i[4+(a=t+b|0)>>2]=1|i[a+4>>2],a=0,e=0),i[619]=e,i[616]=a}else{if((e=e+i[617]|0)>>>0<=o>>>0)break i;i[b+4>>2]=1&s|o|2,a=e-o|0,i[4+(t=b+o|0)>>2]=1|a,i[617]=a,i[620]=t}a=b}else{if(o>>>0<256||e>>>0>=o+4>>>0&&(a=b,e-o>>>0<=i[734]<<1>>>0))break i;a=0}return a?a+8|0:(b=H(f))?(c1(b,n,f>>>0>(a=(3&(a=i[n-4>>2])?-4:-8)+(-8&a)|0)>>>0?a:f),T(n),b):0}function u1(n,f){var a=0,t=0,e=0,b=0,o=0,k=0,r=u(0),s=0,A=u(0);for(t=i[i[i[f+4>>2]+8>>2]>>2];;){i:{if(w[t+14|0])for(;t=i[i[i[(f=t)+4>>2]+8>>2]>>2],w[t+14|0];);f:{a:{n:{t:{if(w[f+14|0])a=f;else if(!(a=i[i[i[f+4>>2]+4>>2]>>2])||(t=f,!w[a+14|0]))break t;h[a+14|0]=0,e=i[a>>2],f=i[i[e+4>>2]+16>>2],o=i[t>>2];b:if((0|f)!=i[i[o+4>>2]+16>>2]){r=c[f+28>>2],s=i[i[i[a+4>>2]+8>>2]>>2],b=i[s>>2],k=i[i[b+4>>2]+16>>2],A=c[k+28>>2];e:{if(!(!(c[f+32>>2]<=c[k+32>>2])|r!=A)||r<A){if(G(f,k,i[e+16>>2])<u(0)){f=a;break b}if(h[a+14|0]=1,h[i[i[i[a+4>>2]+4>>2]>>2]+14|0]=1,!(f=n2(e)))break f;if(z(i[b+4>>2],f))break e;break i}if(G(k,f,i[b+16>>2])>u(0)){f=a;break b}if(h[s+14|0]=1,h[a+14|0]=1,!(f=n2(b))||!z(i[e+12>>2],i[b+4>>2]))break i;f=i[f+4>>2]}if(h[i[f+20>>2]+21|0]=w[a+12|0],w[t+15|0]){if(i[i[t>>2]+24>>2]=0,p2(i[t+4>>2]),T(t),!$(o))break i;t=i[i[i[a+4>>2]+8>>2]>>2],o=i[t>>2],f=a}else if(w[a+15|0]){if(i[i[a>>2]+24>>2]=0,p2(i[a+4>>2]),T(a),!$(e))break i;f=i[i[i[t+4>>2]+4>>2]>>2],e=i[f>>2]}else f=a}else f=a;if(i[e+16>>2]==i[o+16>>2])break a;if(a=i[i[e+4>>2]+16>>2],b=i[i[o+4>>2]+16>>2],w[t+15|0]|w[f+15|0]|(0|a)==(0|b)||(k=a,(0|(a=i[n+72>>2]))!=(0|b)&&(0|k)!=(0|a)))break n;if(!o1(n,f))break a}return}R0(n,f)}if(i[e+16>>2]!=i[o+16>>2]||(b=i[e+4>>2],a=i[o+4>>2],i[b+16>>2]!=i[a+16>>2]))continue;if(i[o+28>>2]=i[o+28>>2]+i[e+28>>2],i[a+28>>2]=i[a+28>>2]+i[b+28>>2],i[i[f>>2]+24>>2]=0,p2(i[f+4>>2]),T(f),!$(e))break i;f=i[i[i[t+4>>2]+4>>2]>>2];continue}}break}b2(n+1740|0,1),o2()}function E4(n){n|=0;var f=0,a=0,t=0,e=u(0),b=u(0),o=0,k=0,r=0,s=0;if((0|(f=i[n+40>>2]))!=(0|(r=n+40|0)))for(;;){if(n=i[f>>2],w[f+21|0]){for(f=f+8|0;f=i[f>>2],a=i[i[f+4>>2]+16>>2],e=c[a+28>>2],t=i[f+16>>2],b=c[t+28>>2],!(!(c[a+32>>2]<=c[t+32>>2])|e!=b)||e<b;)f=i[f+8>>2]+4|0;for(;!(!(c[t+32>>2]<=c[a+32>>2])|e!=b)||e>b;)f=i[f+12>>2],t=i[f+16>>2],b=c[t+28>>2],a=i[i[f+4>>2]+16>>2],e=c[a+28>>2];i:{f:if((0|(t=i[i[f+8>>2]+4>>2]))!=i[f+12>>2])for(;;){if(o=i[t+16>>2],b=c[o+28>>2],!(c[a+32>>2]<=c[o+32>>2])|e!=b&&!(b>e)){a:if(i[t+12>>2]!=(0|f))for(;;){if(a=i[i[f+8>>2]+4>>2],o=i[a+16>>2],e=c[o+28>>2],k=i[i[a+4>>2]+16>>2],!(e<(b=c[k+28>>2])|(c[o+32>>2]<=c[k+32>>2]?e==b:0))){if(!(G(i[i[f+4>>2]+16>>2],i[f+16>>2],o)>=u(0)))break a;a=i[i[f+8>>2]+4>>2]}if(f=d2(f,a),a=0,!f)break i;if((0|(f=i[f+4>>2]))==i[t+12>>2])break}f=i[f+12>>2]}else{a:if((0|(a=i[t+12>>2]))!=(0|f))for(o=t+12|0;;){if(k=i[i[a+4>>2]+16>>2],e=c[k+28>>2],s=i[a+16>>2],!(e<(b=c[s+28>>2])|(c[k+32>>2]<=c[s+32>>2]?e==b:0))){if(!(G(i[t+16>>2],i[i[t+4>>2]+16>>2],k)<=u(0)))break a;a=i[o>>2]}if(t=d2(a,t),a=0,!t)break i;if(o=(t=i[t+4>>2])+12|0,(0|(a=i[t+12>>2]))==(0|f))break}t=i[i[t+8>>2]+4>>2]}if(i[f+12>>2]==(0|t))break f;a=i[i[f+4>>2]+16>>2],e=c[a+28>>2]}if(a=i[t+12>>2],i[a+12>>2]!=(0|f))for(;;){if(t=d2(a,t),a=0,!t)break i;if(t=i[t+4>>2],a=i[t+12>>2],i[a+12>>2]==(0|f))break}a=1}if(!a)return 0}if((0|r)==(0|(f=n)))break}return 1}function $(n){var f=0,a=0,t=0,e=0,b=0,o=0;if(e=i[n+4>>2],(0|(o=i[e+20>>2]))!=(0|(a=i[n+20>>2]))){for(f=t=i[a+8>>2];i[f+20>>2]=o,(0|t)!=(0|(f=i[f+12>>2])););f=i[a>>2],t=i[a+4>>2],i[f+4>>2]=t,i[t>>2]=f,T(a)}if((0|(t=i[n+8>>2]))!=(0|n)){if(b=i[n+4>>2],f=i[b+12>>2],i[i[b+20>>2]+8>>2]=f,i[i[n+16>>2]+8>>2]=t,b=i[f+8>>2],i[i[t+4>>2]+12>>2]=f,i[i[b+4>>2]+12>>2]=n,i[n+8>>2]=b,i[f+8>>2]=t,(0|a)==(0|o)){if(!(a=H(24)))return 0;for(f=i[n+20>>2],t=i[f+4>>2],i[a+4>>2]=t,i[t>>2]=a,i[a>>2]=f,i[f+4>>2]=a,i[a+12>>2]=0,i[a+16>>2]=0,i[a+8>>2]=n,h[a+20|0]=0,h[a+21|0]=w[f+21|0],f=n;i[f+20>>2]=a,(0|(f=i[f+12>>2]))!=(0|n););}}else{for(a=i[n+16>>2],f=t=i[a+8>>2];i[f+16>>2]=0,(0|t)!=(0|(f=i[f+8>>2])););f=i[a>>2],t=i[a+4>>2],i[f+4>>2]=t,i[t>>2]=f,T(a)}if((0|(f=i[e+8>>2]))!=(0|e))a=i[i[e+4>>2]+12>>2],i[i[n+20>>2]+8>>2]=a,i[i[e+16>>2]+8>>2]=f,t=i[a+8>>2],i[i[f+4>>2]+12>>2]=a,i[i[t+4>>2]+12>>2]=e,i[e+8>>2]=t,i[a+8>>2]=f;else{for(a=i[e+16>>2],f=t=i[a+8>>2];i[f+16>>2]=0,(0|t)!=(0|(f=i[f+8>>2])););for(f=i[a>>2],t=i[a+4>>2],i[f+4>>2]=t,i[t>>2]=f,T(a),a=i[e+20>>2],f=e=i[a+8>>2];i[f+20>>2]=0,(0|e)!=(0|(f=i[f+12>>2])););f=i[a>>2],e=i[a+4>>2],i[f+4>>2]=e,i[e>>2]=f,T(a)}return f=i[n+4>>2],f=i[(n=n>>>0>f>>>0?f:n)>>2],a=i[i[n+4>>2]>>2],i[i[f+4>>2]>>2]=a,i[i[a+4>>2]>>2]=f,T(n),1}function d2(n,f){var a=0,t=0,e=0,b=0,o=0,k=0,r=0,s=0;if(t=0,a=H(64)){if(k=i[n+4>>2],b=i[(t=n>>>0>k>>>0?k:n)+4>>2],e=i[b>>2],i[a+32>>2]=e,i[i[e+4>>2]>>2]=a,i[a>>2]=t,o=a+32|0,i[b>>2]=o,i[a+16>>2]=0,i[a+20>>2]=0,i[a+12>>2]=o,i[a+4>>2]=o,i[a+24>>2]=0,i[a+28>>2]=0,i[a+48>>2]=0,i[a+52>>2]=0,i[a+44>>2]=a,i[a+40>>2]=o,i[a+36>>2]=a,i[a+56>>2]=0,i[a+60>>2]=0,i[a+8>>2]=a,(0|(r=i[n+20>>2]))!=(0|(b=i[f+20>>2]))){for(t=e=i[b+8>>2];i[t+20>>2]=r,(0|e)!=(0|(t=i[t+12>>2])););t=i[b>>2],e=i[b+4>>2],i[t+4>>2]=e,i[e>>2]=t,T(b),k=i[n+4>>2],e=i[a+8>>2],t=i[n+20>>2]}else e=a,t=b;if(n=i[n+12>>2],s=i[n+8>>2],i[i[e+4>>2]+12>>2]=n,i[i[s+4>>2]+12>>2]=a,i[a+8>>2]=s,i[n+8>>2]=e,n=i[f+8>>2],e=i[a+40>>2],i[i[e+4>>2]+12>>2]=f,i[i[n+4>>2]+12>>2]=o,i[a+40>>2]=n,i[f+8>>2]=e,i[a+16>>2]=i[k+16>>2],f=i[f+16>>2],n=t,i[a+52>>2]=n,i[a+48>>2]=f,i[a+20>>2]=n,i[n+8>>2]=o,t=a,(0|b)==(0|r)&&(t=0,f=H(24))){for(t=i[n+4>>2],i[f+4>>2]=t,i[t>>2]=f,i[f>>2]=n,i[n+4>>2]=f,i[f+12>>2]=0,i[f+16>>2]=0,i[f+8>>2]=a,h[f+20|0]=0,h[f+21|0]=w[n+21|0],t=a;i[t+20>>2]=f,(0|(t=i[t+12>>2]))!=(0|a););t=a}}return t}function c1(n,f,a){var t=0,e=0;if(a>>>0>=512)p4(0|n,0|f,0|a);else{t=n+a|0;i:if(3&(n^f))if(t>>>0<4)a=n;else if((e=t-4|0)>>>0<n>>>0)a=n;else for(a=n;h[0|a]=w[0|f],h[a+1|0]=w[f+1|0],h[a+2|0]=w[f+2|0],h[a+3|0]=w[f+3|0],f=f+4|0,e>>>0>=(a=a+4|0)>>>0;);else{f:if(3&n)if((0|a)<1)a=n;else for(a=n;;){if(h[0|a]=w[0|f],f=f+1|0,!(3&(a=a+1|0)))break f;if(!(a>>>0<t>>>0))break}else a=n;if(!((n=-4&t)>>>0<64||(e=n+-64|0)>>>0<a>>>0))for(;i[a>>2]=i[f>>2],i[a+4>>2]=i[f+4>>2],i[a+8>>2]=i[f+8>>2],i[a+12>>2]=i[f+12>>2],i[a+16>>2]=i[f+16>>2],i[a+20>>2]=i[f+20>>2],i[a+24>>2]=i[f+24>>2],i[a+28>>2]=i[f+28>>2],i[a+32>>2]=i[f+32>>2],i[a+36>>2]=i[f+36>>2],i[a+40>>2]=i[f+40>>2],i[a+44>>2]=i[f+44>>2],i[a+48>>2]=i[f+48>>2],i[a+52>>2]=i[f+52>>2],i[a+56>>2]=i[f+56>>2],i[a+60>>2]=i[f+60>>2],f=f- -64|0,e>>>0>=(a=a- -64|0)>>>0;);if(n>>>0<=a>>>0)break i;for(;i[a>>2]=i[f>>2],f=f+4|0,n>>>0>(a=a+4|0)>>>0;);}if(a>>>0<t>>>0)for(;h[0|a]=w[0|f],f=f+1|0,(0|t)!=(0|(a=a+1|0)););}}function n2(n){var f=0,a=0,t=0,e=0,b=0,o=0;if(b=n|=0,n=0,(f=H(64))&&(t=i[b+4>>2],e=i[(a=t>>>0<b>>>0?t:b)+4>>2],o=i[e>>2],i[f+32>>2]=o,i[i[o+4>>2]>>2]=f,i[f>>2]=a,a=f+32|0,i[e>>2]=a,i[f+16>>2]=0,i[f+20>>2]=0,i[f+12>>2]=a,i[f+4>>2]=a,i[f+24>>2]=0,i[f+28>>2]=0,i[f+48>>2]=0,i[f+52>>2]=0,i[f+40>>2]=a,i[f+36>>2]=f,i[f+56>>2]=0,i[f+60>>2]=0,i[f+8>>2]=f,e=i[b+12>>2],o=i[e+8>>2],i[f+44>>2]=e,i[i[o+4>>2]+12>>2]=f,i[f+8>>2]=o,i[e+8>>2]=f,e=i[t+16>>2],i[f+16>>2]=e,t=H(40))){for(n=i[e+4>>2],i[t+4>>2]=n,i[n>>2]=t,i[t>>2]=e,i[e+4>>2]=t,i[t+12>>2]=0,i[t+8>>2]=a,n=a;i[n+16>>2]=t,(0|a)!=(0|(n=i[n+8>>2])););n=i[b+20>>2],i[f+20>>2]=n,i[f+52>>2]=n,n=f}return n?(f=i[n+4>>2],n=i[b+4>>2],a=i[i[n+4>>2]+12>>2],t=i[a+8>>2],e=i[n+8>>2],i[i[e+4>>2]+12>>2]=a,i[i[t+4>>2]+12>>2]=n,i[n+8>>2]=t,i[a+8>>2]=e,a=i[f+8>>2],t=i[n+8>>2],i[i[t+4>>2]+12>>2]=f,i[i[a+4>>2]+12>>2]=n,i[n+8>>2]=a,i[f+8>>2]=t,i[n+16>>2]=i[f+16>>2],a=i[f+4>>2],i[i[a+16>>2]+8>>2]=a,i[a+20>>2]=i[n+20>>2],i[f+28>>2]=i[b+28>>2],i[a+28>>2]=i[n+28>>2],0|f):0}function U2(n,f,a,t,e,b){var o=0,k=0,r=0,s=0,A=0;s=f+4|0,r=n- -64|0;i:{for(;;){if(k=i[a+4>>2],!(o=H(16))||(i[o>>2]=k,A=x0(i[r>>2],i[f+4>>2],o),i[o+4>>2]=A,!A))break i;if(h[o+13|0]=0,h[o+14|0]=0,h[o+15|0]=0,i[k+24>>2]=o,(0|t)==(0|(a=i[a+8>>2])))break}if(o=i[i[i[f+4>>2]+8>>2]>>2],a=i[i[o>>2]+4>>2],e=e||i[a+8>>2],i[a+16>>2]==i[e+16>>2])for(r=0;;){if(t=f,f=o,(0|(o=e))!=i[(e=a)+8>>2]&&(!z(i[i[e+4>>2]+12>>2],e)||!z(i[i[o+4>>2]+12>>2],e)))break i;k=i[t+8>>2]-i[e+28>>2]|0,i[f+8>>2]=k;f:{a:switch(i[n+56>>2]-100130|0){case 0:a=1&k;break f;case 1:a=(0|k)!=0;break f;case 2:a=(0|k)>0;break f;case 3:a=k>>>31|0;break f;case 4:break a;default:break f}a=k+1>>>0>2}if(h[f+12|0]=a,h[t+14|0]=1,r&&R0(n,t)&&(i[e+28>>2]=i[e+28>>2]+i[o+28>>2],a=i[e+4>>2],i[a+28>>2]=i[a+28>>2]+i[i[o+4>>2]+28>>2],i[i[t>>2]+24>>2]=0,p2(i[s>>2]),T(t),!$(o)))break i;if(s=f+4|0,r=1,o=i[i[i[f+4>>2]+8>>2]>>2],a=i[i[o>>2]+4>>2],i[a+16>>2]!=i[e+16>>2])break}return h[f+14|0]=1,void(b&&u1(n,f))}b2(n+1740|0,1),o2()}function r1(n){var f=0,a=0,t=0,e=0,b=0,o=0,k=0,r=0,s=0;if((0|(e=i[40+(n|=0)>>2]))!=(0|(k=n+40|0)))for(;;){if(s=i[e>>2],!w[e+21|0]){for(r=i[e+8>>2],n=i[r+12>>2];;){if(i[n+20>>2]=0,o=i[n+12>>2],f=i[n+4>>2],!i[f+20>>2]){if(t=i[n+16>>2],(0|(a=i[n+8>>2]))!=(0|n))i[t+8>>2]=a,t=i[f+12>>2],b=i[t+8>>2],i[i[a+4>>2]+12>>2]=t,i[i[b+4>>2]+12>>2]=n,i[n+8>>2]=b,i[t+8>>2]=a;else{for(f=a=i[t+8>>2];i[f+16>>2]=0,(0|a)!=(0|(f=i[f+8>>2])););f=i[t>>2],a=i[t+4>>2],i[f+4>>2]=a,i[a>>2]=f,T(t),f=i[n+4>>2]}if(t=i[f+16>>2],(0|(a=i[f+8>>2]))!=(0|f))i[t+8>>2]=a,t=i[i[f+4>>2]+12>>2],b=i[t+8>>2],i[i[a+4>>2]+12>>2]=t,i[i[b+4>>2]+12>>2]=f,i[f+8>>2]=b,i[t+8>>2]=a;else{for(f=a=i[t+8>>2];i[f+16>>2]=0,(0|a)!=(0|(f=i[f+8>>2])););f=i[t>>2],a=i[t+4>>2],i[f+4>>2]=a,i[a>>2]=f,T(t),f=i[n+4>>2]}t=i[(f=n>>>0>f>>>0?f:n)>>2],a=i[i[f+4>>2]>>2],i[i[t+4>>2]>>2]=a,i[i[a+4>>2]>>2]=t,T(f)}if(f=(0|n)!=(0|r),n=o,!f)break}n=i[e>>2],o=i[e+4>>2],i[n+4>>2]=o,i[o>>2]=n,T(e)}if((0|k)==(0|(e=s)))break}}function z(n,f){var a=0,t=0,e=0,b=0,o=0,k=0;if((0|(n|=0))!=(0|(f|=0))){if((0|(t=i[f+16>>2]))!=(0|(o=i[n+16>>2]))){for(a=e=i[t+8>>2];i[a+16>>2]=o,(0|e)!=(0|(a=i[a+8>>2])););a=i[t>>2],e=i[t+4>>2],i[a+4>>2]=e,i[e>>2]=a,T(t)}if((0|(k=i[n+20>>2]))!=(0|(e=i[f+20>>2]))){for(a=b=i[e+8>>2];i[a+20>>2]=k,(0|b)!=(0|(a=i[a+12>>2])););a=i[e>>2],b=i[e+4>>2],i[a+4>>2]=b,i[b>>2]=a,T(e)}if(a=i[n+8>>2],b=i[f+8>>2],i[i[b+4>>2]+12>>2]=n,i[i[a+4>>2]+12>>2]=f,i[f+8>>2]=a,i[n+8>>2]=b,(0|t)==(0|o)){if(!(t=H(40)))return 0;for(a=i[n+16>>2],o=i[a+4>>2],i[t+4>>2]=o,i[o>>2]=t,i[t>>2]=a,i[a+4>>2]=t,i[t+12>>2]=0,i[t+8>>2]=f,a=f;i[a+16>>2]=t,(0|(a=i[a+8>>2]))!=(0|f););i[i[n+16>>2]+8>>2]=n}if((0|e)==(0|k)){if(!(t=H(24)))return 0;for(a=i[n+20>>2],e=i[a+4>>2],i[t+4>>2]=e,i[e>>2]=t,i[t>>2]=a,i[a+4>>2]=t,i[t+12>>2]=0,i[t+16>>2]=0,i[t+8>>2]=f,h[t+20|0]=0,h[t+21|0]=w[a+21|0],a=f;i[a+20>>2]=t,(0|(a=i[a+12>>2]))!=(0|f););i[i[n+20>>2]+8>>2]=n}}return 1}function s1(n,f){var a=0,t=0,e=0,b=0,o=0,k=0,r=u(0),s=0,A=0,l=u(0);if(i[n+20>>2]){t=f,a=i[n>>2],n=i[a+8>>2]+1|0,i[a+8>>2]=n;i:{if(!((0|(f=i[a+12>>2]))>=n<<1)){if(i[a+12>>2]=f<<1,e=i[a+4>>2],f=l0(b=i[a>>2],f<<3|4),i[a>>2]=f,!f){i[a>>2]=b,e=2147483647;break i}if(f=l0(i[a+4>>2],8+(i[a+12>>2]<<3)|0),i[a+4>>2]=f,!f){i[a+4>>2]=e,e=2147483647;break i}}if(b=i[a+4>>2],e=n,(f=i[a+16>>2])&&(i[a+16>>2]=i[4+(b+(f<<3)|0)>>2],e=f),o=i[a>>2],i[o+(n<<2)>>2]=e,i[(k=b+(e<<3)|0)>>2]=t,i[k+4>>2]=n,i[a+20>>2]){f:if(n>>>0<2)f=n;else for(r=c[t+28>>2];;){if(a=i[((f=n>>1)<<2)+o>>2],A=i[(s=b+(a<<3)|0)>>2],(l=c[A+28>>2])<r){f=n;break f}if(!(!(c[A+32>>2]<=c[t+32>>2])|r!=l)){f=n;break f}if(i[(n<<2)+o>>2]=a,i[s+4>>2]=n,!((n=f)>>>0>1))break}i[(f<<2)+o>>2]=e,i[k+4>>2]=f}}return e}if(t=(a=i[n+12>>2])+1|0,i[n+12>>2]=t,e=i[n+4>>2],(0|(b=t))<(0|(t=i[n+16>>2])))t=e;else if(i[n+16>>2]=t<<1,t=l0(e,t<<3),i[n+4>>2]=t,!t)return i[n+4>>2]=e,2147483647;return i[(a<<2)+t>>2]=f,-1^a}function f0(n){n|=0;var f=0,a=0,t=0,e=0,b=0,o=0,k=0;b=H(40),o=H(40);i:{if(!(t=H(24))||!b|!o){if(b&&T(b),o&&T(o),!t)break i;return T(t),0}if(!(f=H(64)))return 0;for(a=i[n+68>>2],e=i[(a=a>>>0<(e=n- -64|0)>>>0?a:e)+4>>2],k=i[e>>2],i[f+32>>2]=k,i[i[k+4>>2]>>2]=f,i[f>>2]=a,a=e,e=f+32|0,i[a>>2]=e,i[f+16>>2]=0,i[f+20>>2]=0,i[f+12>>2]=e,i[f+4>>2]=e,i[f+24>>2]=0,i[f+28>>2]=0,i[f+48>>2]=0,i[f+52>>2]=0,i[f+44>>2]=f,i[f+40>>2]=e,i[f+36>>2]=f,i[f+56>>2]=0,i[f+60>>2]=0,i[f+8>>2]=f,a=i[n+4>>2],i[b+4>>2]=a,i[a>>2]=b,i[b+12>>2]=0,i[b+8>>2]=f,a=f;i[a+16>>2]=b,(0|(a=i[a+8>>2]))!=(0|f););for(i[o+4>>2]=b,i[b>>2]=o,i[o>>2]=n,i[n+4>>2]=o,i[o+12>>2]=0,i[o+8>>2]=e,a=e;i[a+16>>2]=o,(0|e)!=(0|(a=i[a+8>>2])););for(a=i[n+44>>2],i[t+4>>2]=a,i[a>>2]=t,i[t>>2]=n+40,i[n+44>>2]=t,i[t+12>>2]=0,i[t+16>>2]=0,i[t+8>>2]=f,h[t+20|0]=0,h[t+21|0]=w[n+61|0],a=f;i[a+20>>2]=t,(0|(a=i[a+12>>2]))!=(0|f););}return 0|f}function M0(n,f){var a=0,t=0,e=0,b=0,o=0,k=0;i[n+8>>2]=i[283],a=i[282],i[n>>2]=i[281],i[n+4>>2]=a,a=i[f+20>>2];i:if(w[a+21|0]){e=f;f:{a:{for(;;){if(w[a+20|0])break i;if(h[a+20|0]=1,i[a+16>>2]=t,e=i[i[e+12>>2]+4>>2],t=i[e+20>>2],w[t+21|0]){if(w[t+20|0])break a;if(h[t+20|0]=1,i[t+16>>2]=a,b=b+2|0,e=i[e+8>>2],a=i[e+20>>2],w[a+21|0])continue;break i}break}b|=1;break f}b|=1}t=a}else e=f;o=i[f+4>>2],a=i[o+20>>2];i:if(!(!w[a+21|0]|w[a+20|0])){f:{a:{for(;;){if(h[a+20|0]=1,i[a+16>>2]=t,f=i[o+12>>2],o=i[f+4>>2],t=i[o+20>>2],w[t+21|0]){if(w[t+20|0])break a;if(h[t+20|0]=1,i[t+16>>2]=a,k=k+2|0,f=i[i[o+8>>2]+4>>2],o=i[f+4>>2],a=i[o+20>>2],!w[a+21|0])break i;if(!w[a+20|0])continue;break i}break}k|=1;break f}k|=1}t=a}a=b+k|0,i[n>>2]=a;i:{if(1&b){if(!(1&k))break i;i[n>>2]=a-1,f=f+8|0}else f=e+4|0;f=i[f>>2]}if(i[n+4>>2]=f,t)for(;h[t+20|0]=0,t=i[t+16>>2];);}function R4(n,f,a){f|=0,a|=0,(0|(a=i[1716+(n|=0)>>2]))==3?M[i[n+88>>2]](5):M[0|a](5,i[n+1896>>2]),(0|(a=i[n+1724>>2]))==5?M[i[n+96>>2]](i[i[f+16>>2]+12>>2]):M[0|a](i[i[f+16>>2]+12>>2],i[n+1896>>2]),(0|(a=i[n+1724>>2]))==5?M[i[n+96>>2]](i[i[i[f+4>>2]+16>>2]+12>>2]):M[0|a](i[i[i[f+4>>2]+16>>2]+12>>2],i[n+1896>>2]),a=i[f+20>>2];i:if(w[a+21|0])for(;;){if(w[a+20|0]||(h[a+20|0]=1,f=i[i[f+12>>2]+4>>2],(0|(a=i[n+1724>>2]))==5?M[i[n+96>>2]](i[i[f+16>>2]+12>>2]):M[0|a](i[i[f+16>>2]+12>>2],i[n+1896>>2]),a=i[f+20>>2],!w[a+21|0]|w[a+20|0]))break i;if(h[a+20|0]=1,f=i[f+8>>2],(0|(a=i[n+1724>>2]))==5?M[i[n+96>>2]](i[i[i[f+4>>2]+16>>2]+12>>2]):M[0|a](i[i[i[f+4>>2]+16>>2]+12>>2],i[n+1896>>2]),a=i[f+20>>2],!w[a+21|0])break}(0|(f=i[n+1728>>2]))==6?M[i[n+100>>2]]():M[0|f](i[n+1896>>2])}function E2(n,f){f|=0;var a=0;if((0|(a=i[(n|=0)>>2]))!=(0|f))for(;;){i:if(f>>>0>a>>>0){f:switch(0|a){case 0:(0|(a=i[n+1732>>2]))==11?M[i[n+12>>2]](100151):M[0|a](100151,i[n+1896>>2]),i[n>>2]&&E2(n,0),i[n+112>>2]=0,a=1,i[n>>2]=1,h[n+108|0]=0,i[n+1896>>2]=0,i[n+8>>2]=0;break i;case 1:break f;default:break i}if((0|(a=i[n+1732>>2]))==11?M[i[n+12>>2]](100152):M[0|a](100152,i[n+1896>>2]),i[n>>2]!=1&&E2(n,1),i[n>>2]=2,i[n+4>>2]=0,a=2,i[n+112>>2]<1)break i;h[n+108|0]=1}else{f:switch(a-1|0){case 1:(0|(a=i[n+1732>>2]))==11?M[i[n+12>>2]](100154):M[0|a](100154,i[n+1896>>2]),i[n>>2]!=2&&E2(n,2),a=1,i[n>>2]=1;break i;case 0:break f;default:break i}(0|(a=i[n+1732>>2]))==11?M[i[n+12>>2]](100153):M[0|a](100153,i[n+1896>>2]),(a=i[n+8>>2])&&_0(a),a=0,i[n+8>>2]=0,i[n>>2]=0,i[n+4>>2]=0}if((0|f)==(0|a))break}}function A1(n,f,a){var t=0,e=0,b=0,o=0,k=0,r=0,s=0,A=0,l=0;i:{f:{a:{n:{t:{b:{e:{c:{u:{o:{if(e=f,f){if(!(t=a))break o;break u}n=(n>>>0)/(a>>>0)|0,v2=0;break i}if(!n)break c;break e}if(!(t-1&t))break b;r=0-(k=(s0(t)+33|0)-s0(e)|0)|0;break n}n=(e>>>0)/0|0,v2=0;break i}if((t=32-s0(e)|0)>>>0<31)break t;break a}if((0|t)==1)break f;a=31&(t=t?31-s0(t-1^t)|0:32),(63&t)>>>0>=32?(e=0,n=f>>>a|0):(e=f>>>a|0,n=((1<<a)-1&f)<<32-a|n>>>a),v2=e;break i}k=t+1|0,r=63-t|0}if(t=f,b=31&(e=63&k),e>>>0>=32?(e=0,b=t>>>b|0):(e=t>>>b|0,b=((1<<b)-1&t)<<32-b|n>>>b),t=31&(r&=63),r>>>0>=32?(f=n<<t,n=0):(f=(1<<t)-1&n>>>32-t|f<<t,n<<=t),k)for(r=(0|(t=a-1|0))!=-1?0:-1;b=(s=o=b<<1|f>>>31)-(A=a&(o=r-((e=e<<1|b>>>31)+(t>>>0<o>>>0)|0)>>31))|0,e=e-(s>>>0<A>>>0)|0,f=f<<1|n>>>31,n=l|n<<1,l=o&=1,k=k-1|0;);v2=f<<1|n>>>31,n=o|n<<1;break i}n=0,f=0}v2=f}return n}function M4(n,f,a){f|=0,a|=0;var t=0,e=0,b=0,o=0,k=0,r=0,s=0;B=e=B-32|0,b=i[28+(n|=0)>>2],i[e+16>>2]=b,t=i[n+20>>2],i[e+28>>2]=a,i[e+24>>2]=f,f=t-b|0,i[e+20>>2]=f,b=f+a|0,s=2,f=e+16|0;i:{f:{(t=0|e1(i[n+60>>2],e+16|0,2,e+12|0))?(i[613]=t,t=-1):t=0;a:{if(!t)for(;;){if((0|(t=i[e+12>>2]))==(0|b))break a;if((0|t)<=-1)break f;if(o=t-((k=(o=i[f+4>>2])>>>0<t>>>0)?o:0)|0,i[(r=(k<<3)+f|0)>>2]=o+i[r>>2],i[(r=(k?12:4)+f|0)>>2]=i[r>>2]-o,b=b-t|0,f=k?f+8|0:f,s=s-k|0,(t=0|e1(i[n+60>>2],0|f,0|s,e+12|0))?(i[613]=t,t=-1):t=0,t)break}if((0|b)!=-1)break f}f=i[n+44>>2],i[n+28>>2]=f,i[n+20>>2]=f,i[n+16>>2]=f+i[n+48>>2],n=a;break i}i[n+28>>2]=0,i[n+16>>2]=0,i[n+20>>2]=0,i[n>>2]=32|i[n>>2],n=0,(0|s)!=2&&(n=a-i[f+4>>2]|0)}return B=e+32|0,0|n}function l1(n){var f=0,a=0,t=0,e=0,b=0,o=0,k=u(0),r=0,s=u(0),A=0,l=0,v=0,p=0,m=0,g=0;if(e=i[n+4>>2],a=i[n>>2],t=i[a+4>>2],m=i[(f=e+(t<<3)|0)>>2],!((0|(b=i[n+8>>2]))<1)&&(A=i[(b<<2)+a>>2],i[a+4>>2]=A,i[4+(l=(A<<3)+e|0)>>2]=1,i[f>>2]=0,i[f+4>>2]=i[n+16>>2],v=b-1|0,i[n+8>>2]=v,i[n+16>>2]=t,(0|b)!=1)){for(g=(A<<3)+e|0,f=1;(0|v)<=(0|(n=f<<1))||(o=i[(i[((t=1|n)<<2)+a>>2]<<3)+e>>2],k=c[o+28>>2],r=i[(i[(n<<2)+a>>2]<<3)+e>>2],s=c[r+28>>2],!(c[o+32>>2]<=c[r+32>>2])|k!=s&&!(k<s)||(n=t)),!((0|n)>=(0|b)||(t=i[g>>2],k=c[t+28>>2],o=i[(n<<2)+a>>2],p=i[(r=(o<<3)+e|0)>>2],k<(s=c[p+28>>2])|(c[t+32>>2]<=c[p+32>>2]?k==s:0)));)i[(f<<2)+a>>2]=o,i[r+4>>2]=f,f=n;i[(f<<2)+a>>2]=A,i[l+4>>2]=f}return m}function I4(n,f,a){f|=0,a|=0,(0|(a=i[1716+(n|=0)>>2]))==3?M[i[n+88>>2]](6):M[0|a](6,i[n+1896>>2]),(0|(a=i[n+1724>>2]))==5?M[i[n+96>>2]](i[i[f+16>>2]+12>>2]):M[0|a](i[i[f+16>>2]+12>>2],i[n+1896>>2]),(0|(a=i[n+1724>>2]))==5?M[i[n+96>>2]](i[i[i[f+4>>2]+16>>2]+12>>2]):M[0|a](i[i[i[f+4>>2]+16>>2]+12>>2],i[n+1896>>2]),a=i[f+20>>2];i:if(w[a+21|0])for(;;){if(w[a+20|0])break i;if(h[a+20|0]=1,f=i[f+8>>2],(0|(a=i[n+1724>>2]))==5?M[i[n+96>>2]](i[i[i[f+4>>2]+16>>2]+12>>2]):M[0|a](i[i[i[f+4>>2]+16>>2]+12>>2],i[n+1896>>2]),a=i[f+20>>2],!w[a+21|0])break}(0|(f=i[n+1728>>2]))==6?M[i[n+100>>2]]():M[0|f](i[n+1896>>2])}function v1(n,f,a){var t=0,e=0;if(a&&(h[(t=n+a|0)-1|0]=f,h[0|n]=f,!(a>>>0<3||(h[t-2|0]=f,h[n+1|0]=f,h[t-3|0]=f,h[n+2|0]=f,a>>>0<7||(h[t-4|0]=f,h[n+3|0]=f,a>>>0<9||(t=(t=n)+(n=0-n&3)|0,e=W(255&f,16843009),i[t>>2]=e,i[(f=(n=a-n&-4)+t|0)-4>>2]=e,n>>>0<9||(i[t+8>>2]=e,i[t+4>>2]=e,i[f-8>>2]=e,i[f-12>>2]=e,n>>>0<25||(i[t+24>>2]=e,i[t+20>>2]=e,i[t+16>>2]=e,i[t+12>>2]=e,i[f-16>>2]=e,i[f-20>>2]=e,i[f-24>>2]=e,i[f-28>>2]=e,(a=n-(f=4&t|24)|0)>>>0<32))))))))for(e=L0(e,0,1,1),n=v2,f=f+t|0;i[f+24>>2]=e,t=n,i[f+28>>2]=t,i[f+16>>2]=e,i[f+20>>2]=t,i[f+8>>2]=e,i[f+12>>2]=t,i[f>>2]=e,i[f+4>>2]=t,f=f+32|0,(a=a-32|0)>>>0>31;);}function d1(n,f,a){i:if(!(f>>>0>20)){f:switch(f-9|0){case 0:return f=i[a>>2],i[a>>2]=f+4,void(i[n>>2]=i[f>>2]);case 1:return f=i[a>>2],i[a>>2]=f+4,f=i[f>>2],i[n>>2]=f,void(i[n+4>>2]=f>>31);case 2:return f=i[a>>2],i[a>>2]=f+4,i[n>>2]=i[f>>2],void(i[n+4>>2]=0);case 3:return f=i[a>>2]+7&-8,i[a>>2]=f+8,a=i[f+4>>2],i[n>>2]=i[f>>2],void(i[n+4>>2]=a);case 4:return f=i[a>>2],i[a>>2]=f+4,f=r2[f>>1],i[n>>2]=f,void(i[n+4>>2]=f>>31);case 5:return f=i[a>>2],i[a>>2]=f+4,i[n>>2]=P2[f>>1],void(i[n+4>>2]=0);case 6:return f=i[a>>2],i[a>>2]=f+4,f=h[0|f],i[n>>2]=f,void(i[n+4>>2]=f>>31);case 7:return f=i[a>>2],i[a>>2]=f+4,i[n>>2]=w[0|f],void(i[n+4>>2]=0);case 8:return f=i[a>>2]+7&-8,i[a>>2]=f+8,void(X[n>>3]=X[f>>3]);case 9:break f;default:break i}M[0](n,a)}}function G(n,f,a){var t=u(0),e=u(0),b=0,o=0,k=u(0),r=u(0),s=0,A=u(0),l=u(0);B=o=B+-64|0,s=(t=c[f+28>>2])>(e=c[n+28>>2]);i:{f:{if(!(!(c[n+32>>2]<=c[f+32>>2])|t!=e)||s){if((k=c[a+28>>2])>t|(c[f+32>>2]<=c[a+32>>2]?t==k:0))break i;if(b=1,s)break f}b=0,t==e&&(b=c[n+32>>2]<=c[f+32>>2])}s=b,b=1,(k=c[a+28>>2])>t||(b=0,t==k&&(b=c[f+32>>2]<=c[a+32>>2])),r=c[n+32>>2],A=c[f+32>>2],l=c[a+32>>2],X[o+40>>3]=k,X[o+24>>3]=t,X[o+48>>3]=l,X[o+32>>3]=A,X[o+16>>3]=r,X[o+8>>3]=e,i[o+4>>2]=b,i[o>>2]=s,R1(1092,o),k=c[a+28>>2],e=c[n+28>>2],t=c[f+28>>2]}return r=u(0),B=o- -64|0,e=u(t-e),t=u(k-t),u(e+t)>u(0)&&(r=e,e=c[f+32>>2],r=u(u(r*u(e-c[a+32>>2]))+u(t*u(e-c[n+32>>2])))),r}function a0(n,f,a){var t=0,e=0,b=0,o=0;t=i[f>>2];i:{if((0|f)!=(0|a))for(;;){if(h[f+15|0]=0,o=i[f+4>>2],b=i[i[o+8>>2]>>2],e=i[b>>2],i[e+16>>2]!=i[t+16>>2]){if(!w[b+15|0])return n=w[f+12|0],a=i[t+20>>2],i[a+8>>2]=t,h[a+21|0]=n,i[t+24>>2]=0,p2(o),T(f),t;if(!(e=d2(i[i[t+8>>2]+4>>2],i[e+4>>2]))||!$(i[b>>2]))break i;i[b>>2]=e,h[b+15|0]=0,i[e+24>>2]=b}if(i[t+8>>2]!=(0|e)&&(!z(i[i[e+4>>2]+12>>2],e)||!z(t,e)))break i;if(e=w[f+12|0],t=i[f>>2],o=i[t+20>>2],i[o+8>>2]=t,h[o+21|0]=e,i[t+24>>2]=0,p2(i[f+4>>2]),T(f),t=i[b>>2],(0|a)==(0|(f=b)))break}return t}b2(n+1740|0,1),o2()}function q2(n,f,a){switch(f-100100|0){case 0:return void(i[n+88>>2]=a||15);case 6:return void(i[n+1716>>2]=a||3);case 4:return h[n+80|0]=(0|a)!=0,void(i[n+92>>2]=a||14);case 10:return h[n+80|0]=(0|a)!=0,void(i[n+1720>>2]=a||4);case 1:return void(i[n+96>>2]=a||13);case 7:return void(i[n+1724>>2]=a||5);case 2:return void(i[n+100>>2]=a||12);case 8:return void(i[n+1728>>2]=a||6);case 3:return void(i[n+12>>2]=a||18);case 9:return void(i[n+1732>>2]=a||11);case 5:return void(i[n+76>>2]=a||17);case 11:return void(i[n+1736>>2]=a||8);case 12:return void(i[n+104>>2]=a||16)}(0|(f=i[n+1732>>2]))==11?M[i[n+12>>2]](100900):M[0|f](100900,i[n+1896>>2])}function Z(n,f,a){var t=0,e=0,b=0;if(!(32&w[0|n]))i:{e=f,t=a;f:{if(!(n=i[(f=n)+16>>2])){if(n=w[f+74|0],h[f+74|0]=n-1|n,8&(n=i[f>>2])?(i[f>>2]=32|n,n=-1):(i[f+4>>2]=0,i[f+8>>2]=0,n=i[f+44>>2],i[f+28>>2]=n,i[f+20>>2]=n,i[f+16>>2]=n+i[f+48>>2],n=0),n)break f;n=i[f+16>>2]}if(n-(b=i[f+20>>2])>>>0<t>>>0){M[i[f+36>>2]](f,e,a);break i}a:if(!(h[f+75|0]<0)){for(n=a;;){if(t=n,!n)break a;if(w[e+(n=t-1|0)|0]==10)break}if(M[i[f+36>>2]](f,e,t)>>>0<t>>>0)break f;e=t+e|0,a=a-t|0,b=i[f+20>>2]}c1(b,e,a),i[f+20>>2]=i[f+20>>2]+a}}}function h1(n,f,a,t){var e=0,b=0;B=e=B-208|0,i[e+204>>2]=a,v1(e+160|0,0,40),i[e+200>>2]=i[e+204>>2],(0|E0(0,f,e+200|0,e+80|0,e+160|0,t))<0||(i[n+76>>2],a=i[n>>2],h[n+74|0]<=0&&(i[n>>2]=-33&a),b=32&a,i[n+48>>2]?E0(n,f,e+200|0,e+80|0,e+160|0,t):(i[n+48>>2]=80,i[n+16>>2]=e+80,i[n+28>>2]=e,i[n+20>>2]=e,a=i[n+44>>2],i[n+44>>2]=e,E0(n,f,e+200|0,e+80|0,e+160|0,t),a&&(M[i[n+36>>2]](n,0,0),i[n+48>>2]=0,i[n+44>>2]=a,i[n+28>>2]=0,i[n+16>>2]=0,i[n+20>>2]=0)),i[n>>2]=i[n>>2]|b),B=e+208|0}function S4(n,f){n|=0;var a=0,t=0,e=0;if((0|(a=i[40+(f|=0)>>2]))!=(0|(t=f+40|0)))for(;;){if(w[a+21|0]){for((0|(f=i[n+1716>>2]))==3?M[i[n+88>>2]](2):M[0|f](2,i[n+1896>>2]),f=i[a+8>>2];(0|(e=i[n+1724>>2]))==5?M[i[n+96>>2]](i[i[f+16>>2]+12>>2]):M[0|e](i[i[f+16>>2]+12>>2],i[n+1896>>2]),(0|(f=i[f+12>>2]))!=i[a+8>>2];);(0|(f=i[n+1728>>2]))==6?M[i[n+100>>2]]():M[0|f](i[n+1896>>2])}if((0|t)==(0|(a=i[a>>2])))break}}function p1(n,f){if(!n)return 0;i:{f:{if(n){if(f>>>0<=127)break f;if(i[i[493]>>2]){if(f>>>0<=2047){h[n+1|0]=63&f|128,h[0|n]=f>>>6|192,n=2;break i}if(!((-8192&f)!=57344&&f>>>0>=55296)){h[n+2|0]=63&f|128,h[0|n]=f>>>12|224,h[n+1|0]=f>>>6&63|128,n=3;break i}if(f-65536>>>0<=1048575){h[n+3|0]=63&f|128,h[0|n]=f>>>18|240,h[n+2|0]=f>>>6&63|128,h[n+1|0]=f>>>12&63|128,n=4;break i}}else if((-128&f)==57216)break f;i[613]=25,n=-1}else n=1;break i}h[0|n]=f,n=1}return n}function I0(){var n=0,f=0,a=0;return(n=H(128))?(i[n+8>>2]=0,i[n+12>>2]=0,f=n+40|0,i[n+44>>2]=f,i[n+48>>2]=0,i[n+52>>2]=0,i[n+40>>2]=f,r2[n+54>>1]=0,r2[n+56>>1]=0,r2[n+58>>1]=0,r2[n+60>>1]=0,i[n+72>>2]=0,i[n+76>>2]=0,f=n+96|0,i[n+68>>2]=f,a=n- -64|0,i[n+64>>2]=a,i[n+80>>2]=0,i[n+84>>2]=0,i[n+88>>2]=0,i[n+92>>2]=0,i[n+104>>2]=0,i[n+108>>2]=0,i[n+100>>2]=a,i[n+96>>2]=f,i[n+112>>2]=0,i[n+116>>2]=0,i[n+120>>2]=0,i[n+124>>2]=0,i[n>>2]=n,i[n+4>>2]=n,0|n):0}function _4(n,f,a){n|=0,f|=0,a|=0;var t=0,e=0,b=u(0),o=u(0);return a=i[a>>2],t=i[i[a+4>>2]+16>>2],f=i[f>>2],(0|(e=i[i[f+4>>2]+16>>2]))==(0|(n=i[n+72>>2]))?(0|n)==(0|t)?(f=i[f+16>>2],b=c[f+28>>2],a=i[a+16>>2],o=c[a+28>>2],!(!(c[f+32>>2]<=c[a+32>>2])|b!=o)||b<o?G(n,f,a)<=u(0)|0:G(n,a,f)>=u(0)|0):G(t,n,i[a+16>>2])<=u(0)|0:(f=i[f+16>>2],(0|n)==(0|t)?G(e,n,f)>=u(0)|0:C1(e,n,f)>=C1(i[i[a+4>>2]+16>>2],n,i[a+16>>2])|0)}function S0(n){var f=0,a=0,t=0,e=0,b=0,o=u(0),k=u(0),r=0;if(!(f=i[n+12>>2]))return l1(i[n>>2]);if(b=i[n+8>>2],t=i[i[(b+(f<<2)|0)-4>>2]>>2],a=i[n>>2],i[a+8>>2]&&(e=i[i[a+4>>2]+(i[i[a>>2]+4>>2]<<3)>>2],o=c[e+28>>2],k=c[t+28>>2],!(!(c[e+32>>2]<=c[t+32>>2])|o!=k)||o<k))return l1(a);for(a=((0|f)<1?f:1)-1|0;;){if((0|f)<2)return i[n+12>>2]=a,t;if(e=f<<2,f=r=f-1|0,i[i[(e+b|0)-8>>2]>>2])break}return i[n+12>>2]=r,t}function x4(n,f,a){f|=0,a|=0;var t=0,e=0,b=0,o=0;b=1;i:if((0|(t=i[64+(n|=0)>>2]))!=(0|(e=n- -64|0))){if(o=0-f|0,!a){for(;;)if(n=w[i[t+20>>2]+21|0],i[t+28>>2]=(0|n)==w[i[i[t+4>>2]+20>>2]+21|0]?0:n?f:o,(0|e)==(0|(t=i[t>>2])))break i}for(;;){if(n=i[t>>2],(0|(a=w[i[t+20>>2]+21|0]))==w[i[i[t+4>>2]+20>>2]+21|0]){if(!$(t)){b=0;break i}}else i[t+28>>2]=a?f:o;if((0|e)==(0|(t=n)))break}}return 0|b}function m1(n,f){var a=0,t=0,e=0;(a=H(16))&&(e=f0(i[n+8>>2]))&&(t=i[e+16>>2],c[t+32>>2]=f,i[t+28>>2]=2112929218,t=i[i[e+4>>2]+16>>2],c[t+32>>2]=f,i[t+28>>2]=-34554430,i[n+72>>2]=t,h[a+15|0]=0,h[a+12|0]=0,i[a+8>>2]=0,i[a>>2]=e,h[a+13|0]=1,h[a+14|0]=0,t=a,a=x0(e=i[n+64>>2],e,a),i[t+4>>2]=a,a)||(b2(n+1740|0,1),o2())}function _0(n){var f=0,a=0,t=0;if((0|(f=i[40+(n|=0)>>2]))!=(0|(a=n+40|0)))for(;t=i[f>>2],T(f),(0|a)!=(0|(f=t)););if((0|(f=i[n>>2]))!=(0|n))for(;t=i[f>>2],T(f),(0|(f=t))!=(0|n););if((0|(f=i[n+64>>2]))!=(0|(a=n- -64|0)))for(;t=i[f>>2],T(f),(0|a)!=(0|(f=t)););T(n)}function w1(n){var f=0,a=u(0),t=u(0);if(!(f=i[n+12>>2]))return n=i[n>>2],i[i[n+4>>2]+(i[i[n>>2]+4>>2]<<3)>>2];f=i[i[(i[n+8>>2]+(f<<2)|0)-4>>2]>>2],n=i[n>>2];i:{if(i[n+8>>2]&&(n=i[i[n+4>>2]+(i[i[n>>2]+4>>2]<<3)>>2],(a=c[n+28>>2])<(t=c[f+28>>2])||a==t&&c[n+32>>2]<=c[f+32>>2]))break i;n=f}return n}function L4(n,f,a,t){n|=0,f|=0,a|=0,t|=0,a=0;i:{if(f=i[520]){if(!((a=i[f>>2])>>>0<100001)){f=H(12);break i}}else f=H(1200008),i[f+4>>2]=12,i[f>>2]=0,i[520]=f;i[f>>2]=a+1,f=8+(W(a,12)+f|0)|0}c[f>>2]=c[n>>2],c[f+4>>2]=c[n+4>>2],c[f+8>>2]=c[n+8>>2],i[t>>2]=f}function z2(n,f,a){var t=0,e=0,b=0;if(f>>>0<1)t=n;else for(;t=A1(n,f,10),b=e=v2,e=L0(t,e,10,0),h[0|(a=a-1|0)]=n-e|48,e=f>>>0>9,n=t,f=b,e;);if(t)for(;n=(t>>>0)/10|0,h[0|(a=a-1|0)]=t-W(n,10)|48,f=t>>>0>9,t=n,f;);return a}function g1(n,f,a,t){var e=0,b=0,o=0;if(b=i[610]+1|0,i[610]=b,i[n>>2]=b,t)for(;;){if(!i[(o=(e<<3)+a|0)>>2])return i[o>>2]=b,i[4+(n=(e<<3)+a|0)>>2]=f,i[n+8>>2]=0,K(0|t),a;if((0|(e=e+1|0))==(0|t))break}return e=n,n=t<<1,f=g1(e,f,l0(a,t<<4|8),n),K(0|n),f}function y1(n,f){var a=0,t=0,e=0;if(C0(+n),a=0|l2(1),t=0|l2(0),e=a,(0|(a=a>>>20&2047))!=2047){if(!a)return a=f,n==0?f=0:(n=y1(18446744073709552e3*n,f),f=i[f>>2]+-64|0),i[a>>2]=f,n;i[f>>2]=a-1022,n1(0,0|t),n1(1,-2146435073&e|1071644672),n=+c4()}return n}function C1(n,f,a){var t=u(0),e=u(0),b=u(0),o=u(0),k=u(0);return t=c[f+28>>2],e=u(t-c[n+28>>2]),t=u(c[a+28>>2]-t),(b=u(e+t))>u(0)?(k=c[f+32>>2],o=c[((f=t>e)?n:a)+32>>2],t=u(u(k-o)+u(u((f?e:t)/b)*u(o-c[(f?a:n)+32>>2])))):t=u(0),t}function x0(n,f,a){for(var t=0;f=i[f+8>>2],(t=i[f>>2])&&!(0|M[i[n+16>>2]](i[n+12>>2],t,a)););return(n=H(12))?(i[n>>2]=a,i[n+4>>2]=i[f+4>>2],i[i[f+4>>2]+8>>2]=n,i[n+8>>2]=f,i[f+4>>2]=n,n):0}function L0(n,f,a,t){var e=0,b=0,o=0,k=0,r=0,s=0;return s=W(e=a>>>16|0,b=n>>>16|0),e=(65535&(b=((r=W(o=65535&a,k=65535&n))>>>16|0)+W(b,o)|0))+W(e,k)|0,v2=(W(f,a)+s|0)+W(n,t)+(b>>>16)+(e>>>16)|0,65535&r|e<<16}function i2(n,f,a,t,e){var b=0;if(B=b=B-256|0,!(73728&e|(0|a)<=(0|t))){if(v1(b,255&f,(t=(a=a-t|0)>>>0<256)?a:256),!t)for(;Z(n,b,256),(a=a-256|0)>>>0>255;);Z(n,b,a)}B=b+256|0}function J(n,f,a){var t=0,e=0;i:if(a)for(;;){if(!(e=i[(t<<3)+f>>2]))break i;if((0|n)==(0|e))return i[4+((t<<3)+f|0)>>2];if((0|(t=t+1|0))==(0|a))break}return 0}function F2(n){var f=0,a=0;return(n=(f=i[412])+(a=n+3&-4)|0)>>>0<=f>>>0&&a||n>>>0>M1()<<16>>>0&&!(0|A4(0|n))?(i[613]=48,-1):(i[412]=n,f)}function E1(n){var f=0,a=0,t=0;if(h[i[n>>2]]-48>>>0<10)for(;f=i[n>>2],t=h[0|f],i[n>>2]=f+1,a=(W(a,10)+t|0)-48|0,h[f+1|0]-48>>>0<10;);return a}function P4(n,f){n|=0;var a=0,t=0;(0|(a=i[4+(f|=0)>>2]))<i[f+8>>2]&&(t=i[f>>2]+(W(i[f+12>>2],a)<<2)|0,c[t>>2]=c[n>>2],c[t+4>>2]=c[n+4>>2],i[f+4>>2]=a+1)}function B4(n,f){n|=0,f|=0;var a=u(0),t=u(0);if((a=c[n+28>>2])<(t=c[f+28>>2]))n=1;else{if(a!=t)return 0;n=c[n+32>>2]<=c[f+32>>2]}return 0|n}function P0(n){var f=0;(f=i[n>>2])&&(T(i[f+4>>2]),T(i[f>>2]),T(f)),(f=i[n+8>>2])&&T(f),(f=i[n+4>>2])&&T(f),T(n)}function T4(n){n|=0;var f=0;B=f=B-16|0,i[f>>2]=n,B=n=B-16|0,i[n+12>>2]=f,h1(i[288],1078,f,0),B=n+16|0,B=f+16|0}function U4(n,f,a){n|=0,f=i[20+(f|=0)>>2],i[f+16>>2]=i[n+84>>2],i[n+84>>2]=f,h[f+20|0]=1}function p2(n){var f=0;f=i[n+4>>2],i[f+8>>2]=i[n+8>>2],i[i[n+8>>2]+4>>2]=f,T(n)}function R1(n,f){var a=0;B=a=B-16|0,i[a+12>>2]=f,h1(i[288],n,f,43),B=a+16|0}function f2(n){var f=0;return(-1>>>(f=31&n)&-2)<<f|(-1<<(n=0-n&31)&-2)>>>n}function b2(n,f){n|=0,f|=0,i[611]||(i[612]=f,i[611]=n),l4()}function F4(n,f){n|=0,f|=0,i[611]||(i[612]=f,i[611]=n)}function j4(n,f,a,t){return v2=0,0}function O4(n,f,a,t,e){}function D4(n,f,a,t){}function H4(n){return 0}function v0(n,f){}function Q4(){return 0|B}function W4(n){B=n|=0}function R2(n){}function B0(){}_=w,a2();var M=x([null,U4,I4,v0,v0,v0,R2,R4,O4,B4,_4,v0,B0,R2,R2,R2,R2,D4,R2,E2,C4,I0,f0,n2,z,b1,b2,w4,x4,E4,S4,y4,r1,_0,P4,R2,B0,L4,T4,R2,H4,M4,j4,g4]);function M1(){return N.byteLength/65536|0}function q4(n){n|=0;var f=0|M1(),a=f+n|0;if(f<a&&a<65536){var t=new ArrayBuffer(W(a,65536));new Int8Array(t).set(h),h=new Int8Array(t),r2=new Int16Array(t),i=new Int32Array(t),w=new Uint8Array(t),P2=new Uint16Array(t),B2=new Uint32Array(t),c=new Float32Array(t),X=new Float64Array(t),N=t,c2.buffer=N,_=w}return f}return{n:B0,o:H,p:T,q:M,r:m4,s:Q4,t:W4,u:F4}}return r4(I)}(i1)},instantiate:function(d,y){return{then:function(I){var x=new g2.Module(d);I({instance:new g2.Instance(x)})}}},RuntimeError:Error};Q2=[],typeof g2!="object"&&$2("no native wasm support detected");var q0=!1;function T1(d,y){d||$2("Assertion failed: "+y)}var G2,J2,Z2,z0=typeof TextDecoder<"u"?new TextDecoder("utf8"):void 0;function K0(d,y,I){for(var x=y+I,_=y;d[_]&&!(_>=x);)++_;if(_-y>16&&d.subarray&&z0)return z0.decode(d.subarray(y,_));for(var U="";y<_;){var O=d[y++];if(128&O){var D=63&d[y++];if((224&O)!=192){var a2=63&d[y++];if((O=(240&O)==224?(15&O)<<12|D<<6|a2:(7&O)<<18|D<<12|a2<<6|63&d[y++])<65536)U+=String.fromCharCode(O);else{var t2=O-65536;U+=String.fromCharCode(55296|t2>>10,56320|1023&t2)}}else U+=String.fromCharCode((31&O)<<6|D)}else U+=String.fromCharCode(O)}return U}function U1(d,y){return d?K0(J2,d,y):""}function F1(d,y){return d%y>0&&(d+=y-d%y),d}function Y0(d){G2=d,C.HEAP8=new Int8Array(d),C.HEAP16=new Int16Array(d),C.HEAP32=Z2=new Int32Array(d),C.HEAPU8=J2=new Uint8Array(d),C.HEAPU16=new Uint16Array(d),C.HEAPU32=new Uint32Array(d),C.HEAPF32=new Float32Array(d),C.HEAPF64=new Float64Array(d)}var h2,N0=C.INITIAL_MEMORY||4194304;(V2=C.wasmMemory?C.wasmMemory:new g2.Memory({initial:N0/65536,maximum:32768}))&&(G2=V2.buffer),N0=G2.byteLength,Y0(G2);var V0=[],G0=[],J0=[];function j1(){if(C.preRun)for(typeof C.preRun=="function"&&(C.preRun=[C.preRun]);C.preRun.length;)H1(C.preRun.shift());w0(V0)}function O1(){w0(G0)}function D1(){if(C.postRun)for(typeof C.postRun=="function"&&(C.postRun=[C.postRun]);C.postRun.length;)W1(C.postRun.shift());w0(J0)}function H1(d){V0.unshift(d)}function Q1(d){G0.unshift(d)}function W1(d){J0.unshift(d)}var I2=0,X2=null;function q1(d){I2++,C.monitorRunDependencies&&C.monitorRunDependencies(I2)}function z1(d){if(I2--,C.monitorRunDependencies&&C.monitorRunDependencies(I2),I2==0&&X2){var y=X2;X2=null,y()}}function $2(d){throw C.onAbort&&C.onAbort(d),H2(d+=""),q0=!0,d="abort("+d+"). Build with -s ASSERTIONS=1 for more info.",new g2.RuntimeError(d)}C.preloadedImages={},C.preloadedAudios={};var e2,Z0="data:application/octet-stream;base64,";function m0(d){return d.startsWith(Z0)}function X0(d){return d.startsWith("file://")}function $0(d){try{if(d==e2&&Q2)return new Uint8Array(Q2);var y=W2(d);if(y)return y;if(o0)return o0(d);throw"both async and sync fetching of the wasm failed"}catch(I){$2(I)}}function K1(){if(!Q2&&(Q0||N2)){if(typeof fetch=="function"&&!X0(e2))return fetch(e2,{credentials:"same-origin"}).then(function(d){if(!d.ok)throw"failed to load wasm binary file at '"+e2+"'";return d.arrayBuffer()}).catch(function(){return $0(e2)});if(e0)return new Promise(function(d,y){e0(e2,function(I){d(new Uint8Array(I))},y)})}return Promise.resolve().then(function(){return $0(e2)})}function Y1(){var d={a:i1};function y(U,O){var D=U.exports;C.asm=D,h2=C.asm.q,Q1(C.asm.n),z1()}function I(U){y(U.instance)}function x(U){return K1().then(function(O){return g2.instantiate(O,d)}).then(U,function(O){H2("failed to asynchronously prepare wasm: "+O),$2(O)})}function _(){return Q2||typeof g2.instantiateStreaming!="function"||m0(e2)||X0(e2)||typeof fetch!="function"?x(I):fetch(e2,{credentials:"same-origin"}).then(function(U){return g2.instantiateStreaming(U,d).then(I,function(O){return H2("wasm streaming compile failed: "+O),H2("falling back to ArrayBuffer instantiation"),x(I)})})}if(q1(),C.instantiateWasm)try{return C.instantiateWasm(d,y)}catch(U){return H2("Module.instantiateWasm callback failed with error: "+U),!1}return _(),{}}function w0(d){for(;d.length>0;){var y=d.shift();if(typeof y!="function"){var I=y.func;typeof I=="number"?y.arg===void 0?h2.get(I)():h2.get(I)(y.arg):I(y.arg===void 0?null:y.arg)}else y(C)}}function N1(){throw"longjmp"}function V1(d,y,I){J2.copyWithin(d,y,y+I)}function G1(d){try{return V2.grow(d-G2.byteLength+65535>>>16),Y0(V2.buffer),1}catch{}}function J1(d){var y=J2.length,I=2147483648;if((d>>>=0)>I)return!1;for(var x=1;x<=4;x*=2){var _=y*(1+.2/x);if(_=Math.min(_,d+100663296),G1(Math.min(I,F1(Math.max(d,_),65536))))return!0}return!1}m0(e2="libtess-asm.wasm")||(e2=x1(e2));var b0={mappings:{},buffers:[null,[],[]],printChar:function(d,y){var I=b0.buffers[d];y===0||y===10?((d===1?L1:H2)(K0(I,0)),I.length=0):I.push(y)},varargs:void 0,get:function(){return b0.varargs+=4,Z2[b0.varargs-4>>2]},getStr:function(d){return U1(d)},get64:function(d,y){return d}};function Z1(d,y,I,x){for(var _=0,U=0;U<I;U++){for(var O=Z2[y+8*U>>2],D=Z2[y+(8*U+4)>>2],a2=0;a2<D;a2++)b0.printChar(d,J2[O+a2]);_+=D}return Z2[x>>2]=_,0}function X1(d){for(var y=[],I=0;I<d.length;I++){var x=d[I];x>255&&(x&=255),y.push(String.fromCharCode(x))}return y.join("")}var $1=typeof atob=="function"?atob:function(d){var y,I,x,_,U,O,D="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",a2="",t2=0;d=d.replace(/[^A-Za-z0-9\+\/\=]/g,"");do y=D.indexOf(d.charAt(t2++))<<2|(_=D.indexOf(d.charAt(t2++)))>>4,I=(15&_)<<4|(U=D.indexOf(d.charAt(t2++)))>>2,x=(3&U)<<6|(O=D.indexOf(d.charAt(t2++))),a2+=String.fromCharCode(y),U!==64&&(a2+=String.fromCharCode(I)),O!==64&&(a2+=String.fromCharCode(x));while(t2<d.length);return a2};function i4(d){if(typeof p0=="boolean"&&p0){var y=Buffer.from(d,"base64");return new Uint8Array(y.buffer,y.byteOffset,y.byteLength)}try{for(var I=$1(d),x=new Uint8Array(I.length),_=0;_<I.length;++_)x[_]=I.charCodeAt(_);return x}catch{throw new Error("Converting base64 string to bytes failed.")}}function W2(d){if(m0(d))return i4(d.slice(Z0.length))}var i1={i:N1,m:V1,h:J1,g:Z1,c:B1,l:t4,e:n4,k:e4,j:o4,f:a4,d:f4,a:V2,b:P1};Y1(),C.___wasm_call_ctors=function(){return(C.___wasm_call_ctors=C.asm.n).apply(null,arguments)},C._malloc=function(){return(C._malloc=C.asm.o).apply(null,arguments)},C._free=function(){return(C._free=C.asm.p).apply(null,arguments)},C._triangulate=function(){return(C._triangulate=C.asm.r).apply(null,arguments)};var k0,S2=C.stackSave=function(){return(S2=C.stackSave=C.asm.s).apply(null,arguments)},_2=C.stackRestore=function(){return(_2=C.stackRestore=C.asm.t).apply(null,arguments)},x2=C._setThrew=function(){return(x2=C._setThrew=C.asm.u).apply(null,arguments)};function f4(d,y,I){var x=S2();try{h2.get(d)(y,I)}catch(_){if(_2(x),_!==_+0&&_!=="longjmp")throw _;x2(1,0)}}function a4(d,y){var I=S2();try{h2.get(d)(y)}catch(x){if(_2(I),x!==x+0&&x!=="longjmp")throw x;x2(1,0)}}function n4(d,y){var I=S2();try{return h2.get(d)(y)}catch(x){if(_2(I),x!==x+0&&x!=="longjmp")throw x;x2(1,0)}}function t4(d){var y=S2();try{return h2.get(d)()}catch(I){if(_2(y),I!==I+0&&I!=="longjmp")throw I;x2(1,0)}}function e4(d,y,I){var x=S2();try{return h2.get(d)(y,I)}catch(_){if(_2(x),_!==_+0&&_!=="longjmp")throw _;x2(1,0)}}function o4(d,y,I,x){var _=S2();try{return h2.get(d)(y,I,x)}catch(U){if(_2(_),U!==U+0&&U!=="longjmp")throw U;x2(1,0)}}function b4(d){this.name="ExitStatus",this.message="Program terminated with exit("+d+")",this.status=d}function g0(d){function y(){k0||(k0=!0,C.calledRun=!0,q0||(O1(),C.onRuntimeInitialized&&C.onRuntimeInitialized(),D1()))}I2>0||(j1(),I2>0||(C.setStatus?(C.setStatus("Running..."),setTimeout(function(){setTimeout(function(){C.setStatus("")},1),y()},1)):y()))}if(X2=function d(){k0||g0(),k0||(X2=d)},C.run=g0,C.preInit)for(typeof C.preInit=="function"&&(C.preInit=[C.preInit]);C.preInit.length>0;)C.preInit.pop()();g0();let y0=null,y2=null,L2=null,u0=null;const s2=u2.Module,k4=2,f1=4e3;let a1=0;const u4=(d,y,I)=>{y0||(y0=s2._triangulate);let x=s2.HEAPF32;const _=s2.HEAP32.BYTES_PER_ELEMENT,U=2,O=x.BYTES_PER_ELEMENT;I>a1&&(a1=I,L2&&(s2._free(L2),L2=0),y2&&(s2._free(y2),y2=0)),L2||(L2=s2._malloc(I*O)),u0||(u0=s2._malloc(f1*_));const D=I*k4;y2||(y2=s2._malloc(D*O)),x=s2.HEAPF32,x.set(d,L2/O),s2.HEAP32.set(y,u0/_);const a2=D/U,t2=y0(L2,u0,Math.min(y.length,f1),U,y2,a2),c0=t2*U;x=s2.HEAPF32;const r0=x.slice(y2/O,y2/O+c0),l2={};return l2.buffer=r0,l2.vertexCount=t2,l2};return u2.triangulate=u4,u2.whenLoaded()}return{load:M2}},(S1=I1())!==void 0&&(H0.exports=S1);const Y4=z4({__proto__:null,default:d0},[d0]);export{Y4 as l};
