package org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartProduction.dispatch.EmergencyVehicle;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

@Getter
@Setter
public class EmergencyVehicleSaveRequest extends SaveRequest<EmergencyVehicle> {
    // 车牌号
    @NotNullOrEmpty
    private String numberPlate;

    // 车辆品牌
    @NotNullOrEmpty
    private String carBrand;

    // 使用年限
    private Integer usePeriod;

    // 车辆负责人
    @NotNullOrEmpty
    private String carUserId;

    // sim卡号
    private String simNum;

    public EmergencyVehicle build() {
        EmergencyVehicle entity = new EmergencyVehicle();
        entity.setCreator(currentUserUUID());
        entity.setCreateTime(createTime());
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    public EmergencyVehicle update(String id) {
        EmergencyVehicle entity = new EmergencyVehicle();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(EmergencyVehicle entity) {
        entity.setNumberPlate(numberPlate);
        entity.setCarBrand(carBrand);
        entity.setUsePeriod(usePeriod);
        entity.setCarUserId(carUserId);
        entity.setSimNum(simNum);
    }
}