/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.sql.optionLog;

import com.google.common.util.concurrent.ListenableFuture;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Component;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.optionLog.OptionLog;
import org.thingsboard.server.dao.DaoUtil;
import org.thingsboard.server.dao.model.sql.OptionLogEntity;
import org.thingsboard.server.dao.optionLog.OptionLogDao;
import org.thingsboard.server.dao.sql.JpaAbstractDao;
import org.thingsboard.server.dao.util.SqlDao;

import java.util.List;
import java.util.UUID;

@Component
@SqlDao
@Slf4j
public class JpaOptionLogDao extends JpaAbstractDao<OptionLogEntity, OptionLog> implements OptionLogDao {

    @Override
    protected void setSearchText(OptionLogEntity entity) {
        super.setSearchText(entity);
    }

    @Override
    public OptionLog save(OptionLog domain) {
        return super.save(domain);
    }

    @Override
    public OptionLog findById(UUID key) {
        return super.findById(key);
    }

//    @Override
//    public ListenableFuture<OptionLog> findByIdAsync(UUID key) {
//        return super.findByIdAsync(key);
//    }

    @Override
    public boolean removeById(UUID id) {
        return super.removeById(id);
    }

    @Override
    public List<OptionLog> findByTenantIdAndOptions(TenantId tenantId, String options, long sTime, long eTime) {
        return DaoUtil.convertDataList(
                optionLogRepository
                        .findByTenantIdAndOptions(
                                UUIDConverter.fromTimeUUID(tenantId.getId()),
                                options,
                                sTime,
                                eTime));
    }

    @Override
    public List<OptionLog> find() {
        return super.find();
    }

    @Autowired
    private OptionLogRepository optionLogRepository;

    @Override
    protected Class<OptionLogEntity> getEntityClass() {
        return OptionLogEntity.class;
    }

    @Override
    protected CrudRepository<OptionLogEntity, String> getCrudRepository() {
        return optionLogRepository;
    }
}
