﻿//==本JS是加载Lodop插件或Web打印服务CLodop/Lodop7的综合示例，可直接使用，建议理解后融入自己程序==

//用双端口加载主JS文件Lodop.js(或CLodopfuncs.js兼容老版本)以防其中某端口被占:
var MainJS = "CLodopfuncs.js",
    URL_WS1 = "ws://localhost:8000/" + MainJS,                //ws用8000/18000
    URL_WS2 = "ws://localhost:18000/" + MainJS,
    URL_HTTP1 = "http://localhost:8000/" + MainJS,              //http用8000/18000
    URL_HTTP2 = "http://localhost:18000/" + MainJS,
    URL_HTTP3 = "https://localhost.lodop.net:8443/" + MainJS;   //https用8000/8443

var CreatedOKLodopObject, CLodopIsLocal, LoadJsState;

//==判断是否需要CLodop(那些不支持插件的浏览器):==
function needCLodop() {
    try {
        var ua = navigator.userAgent;
        if (ua.match(/Windows\sPhone/i) ||
            ua.match(/iPhone|iPod|iPad/i) ||
            ua.match(/Android/i) ||
            ua.match(/Edge\D?\d+/i))
            return true;
        var verTrident = ua.match(/Trident\D?\d+/i);
        var verIE = ua.match(/MSIE\D?\d+/i);
        var verOPR = ua.match(/OPR\D?\d+/i);
        var verFF = ua.match(/Firefox\D?\d+/i);
        var x64 = ua.match(/x64/i);
        if ((!verTrident) && (!verIE) && (x64)) return true;
        else if (verFF) {
            verFF = verFF[0].match(/\d+/);
            if ((verFF[0] >= 41) || (x64)) return true;
        } else if (verOPR) {
            verOPR = verOPR[0].match(/\d+/);
            if (verOPR[0] >= 32) return true;
        } else if ((!verTrident) && (!verIE)) {
            var verChrome = ua.match(/Chrome\D?\d+/i);
            if (verChrome) {
                verChrome = verChrome[0].match(/\d+/);
                if (verChrome[0] >= 41) return true;
            }
        }
        return false;
    } catch (err) {
        return true;
    }
}

//==检查加载成功与否，如没成功则用http(s)再试==
//==低版本CLODOP6.561/Lodop7.043及前)用本方法==
function checkOrTryHttp() {
    if (window.getCLodop) {
        LoadJsState = "complete";
        return true;
    }
    if (LoadJsState == "loadingB" || LoadJsState == "complete") return;
    LoadJsState = "loadingB";
    var head = document.head || document.getElementsByTagName("head")[0] || document.documentElement;
    var JS1 = document.createElement("script")
        , JS2 = document.createElement("script")
        , JS3 = document.createElement("script");
    JS1.src = URL_HTTP1;
    JS2.src = URL_HTTP2;
    JS3.src = URL_HTTP3;
    JS1.onload = JS2.onload = JS3.onload = JS2.onerror = JS3.onerror = function () { LoadJsState = "complete"; }
    JS1.onerror = function (e) {
        if (window.location.protocol !== 'https:')
            head.insertBefore(JS2, head.firstChild); else
            head.insertBefore(JS3, head.firstChild);
    }
    head.insertBefore(JS1, head.firstChild);
}

//==加载Lodop对象的主过程:==
function loadCLodop() {
    if (!needCLodop()) return;
    CLodopIsLocal = !!((URL_WS1 + URL_WS2).match(/\/\/localho|\/\/127.0.0./i));
    LoadJsState = "loadingA";
    if (!window.WebSocket && window.MozWebSocket) window.WebSocket = window.MozWebSocket;
    //ws方式速度快(小于200ms)且可避免CORS错误,但要求Lodop版本足够新:
    try {
        var WSK1 = new WebSocket(URL_WS1);
        WSK1.onopen = function (e) { setTimeout("checkOrTryHttp();", 200); }
        WSK1.onmessage = function (e) { if (!window.getCLodop) eval(e.data); }
        WSK1.onerror = function (e) {
            var WSK2 = new WebSocket(URL_WS2);
            WSK2.onopen = function (e) { setTimeout("checkOrTryHttp();", 200); }
            WSK2.onmessage = function (e) { if (!window.getCLodop) eval(e.data); }
            WSK2.onerror = function (e) { checkOrTryHttp(); }
        }
    } catch (e) {
        console.log(e);
        checkOrTryHttp();
    }
}


