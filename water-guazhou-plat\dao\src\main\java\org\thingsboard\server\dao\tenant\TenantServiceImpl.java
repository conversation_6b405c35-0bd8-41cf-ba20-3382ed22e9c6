/**
 * Copyright © 2016-2019 The Thingsboard Authors
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.tenant;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.util.concurrent.ListenableFuture;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.Device;
import org.thingsboard.server.common.data.Tenant;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.User;
import org.thingsboard.server.common.data.VO.TenantGisVO;
import org.thingsboard.server.common.data.constantsAttribute.ConstantsAttribute;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.EntityId;
import org.thingsboard.server.common.data.id.MenuPoolId;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.id.UserId;
import org.thingsboard.server.common.data.page.TextPageData;
import org.thingsboard.server.common.data.page.TextPageLink;
import org.thingsboard.server.common.data.project.ProjectTreeVO;
import org.thingsboard.server.common.data.security.Authority;
import org.thingsboard.server.dao.app.AppTypeRelationService;
import org.thingsboard.server.dao.app.AppTypeService;
import org.thingsboard.server.dao.constantsAttribute.ConstantsAttributeDao;
import org.thingsboard.server.dao.customer.CustomerService;
import org.thingsboard.server.dao.dashboard.DashboardService;
import org.thingsboard.server.dao.device.DeviceService;
import org.thingsboard.server.dao.entity.AbstractEntityService;
import org.thingsboard.server.dao.entityview.EntityViewService;
import org.thingsboard.server.dao.exception.DataValidationException;
import org.thingsboard.server.dao.menu.MenuTenantService;
import org.thingsboard.server.dao.menu2.TenantMenusService;
import org.thingsboard.server.dao.model.sql.*;
import org.thingsboard.server.dao.project.ProjectService;
import org.thingsboard.server.dao.rule.RuleChainService;
import org.thingsboard.server.dao.service.DataValidator;
import org.thingsboard.server.dao.service.PaginatedRemover;
import org.thingsboard.server.dao.service.Validator;
import org.thingsboard.server.dao.sql.user.UserRepository;
import org.thingsboard.server.dao.sql.user.UserTenantRepository;
import org.thingsboard.server.dao.user.UserDao;
import org.thingsboard.server.dao.user.UserService;
import org.thingsboard.server.dao.widget.WidgetsBundleService;

import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

import static org.thingsboard.server.common.data.CacheConstants.*;
import static org.thingsboard.server.dao.service.Validator.validateId;

@Service
@Slf4j
public class TenantServiceImpl extends AbstractEntityService implements TenantService {

    private static final String DEFAULT_TENANT_REGION = "Global";
    public static final String INCORRECT_TENANT_ID = "Incorrect tenantId ";

    @Autowired
    private TenantDao tenantDao;

    @Autowired
    private UserService userService;

    @Autowired
    private CustomerService customerService;

    @Autowired
    private DeviceService deviceService;

    @Autowired
    private EntityViewService entityViewService;

    @Autowired
    private WidgetsBundleService widgetsBundleService;

    @Autowired
    private DashboardService dashboardService;

    @Autowired
    private RuleChainService ruleChainService;

    @Autowired
    private AppTypeService appTypeService;

    @Autowired
    private AppTypeRelationService appTypeRelationService;

    @Autowired
    private MenuTenantService menuTenantService;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private UserTenantRepository userTenantRepository;

    @Autowired
    private TenantMenusService tenantMenusService;

    @Autowired
    private TenantApplicationService tenantApplicationService;

    @Autowired
    private ConstantsAttributeDao constantsAttributeDao;

    @Override
    public Tenant findTenantById(TenantId tenantId) {
        log.trace("Executing findTenantById [{}]", tenantId);
        Validator.validateId(tenantId, INCORRECT_TENANT_ID + tenantId);
        return tenantDao.findById(tenantId, tenantId.getId());
    }

    @Override
    public ListenableFuture<Tenant> findTenantByIdAsync(TenantId callerId, TenantId tenantId) {
        log.trace("Executing TenantIdAsync [{}]", tenantId);
        validateId(tenantId, INCORRECT_TENANT_ID + tenantId);
        return tenantDao.findByIdAsync(callerId, tenantId.getId());
    }

    @Override
    public ListenableFuture<Tenant> findTenantByIdAsync(TenantId tenantId) {
        return tenantDao.findTenantByIdAsync(tenantId);
    }

    @Override
    @Transactional
    public Tenant saveTenant(Tenant tenant) {
        log.trace("Executing saveTenant [{}]", tenant);
        tenantValidator.validate(tenant, Tenant::getId);
        Tenant save = tenantDao.save(tenant.getId(), tenant);

        if (tenant.getId() == null) {// 仅创建时可以选择应用类型
            // 创建应用菜单
            String appTypeId = save.getAppTypeId();
            List<AppTypeRelation> appTypeRelations = appTypeRelationService.findByAppTypeId(appTypeId);
            List<MenuPoolId> menuPoolIds = appTypeRelations.stream()
                    .map(appTypeRelation -> new MenuPoolId(UUIDConverter.fromString(appTypeRelation.getMenuPoolId())))
                    .collect(Collectors.toList());

            try {
                menuTenantService.saveMenuTenant(menuPoolIds, save.getId());
            } catch (ThingsboardException e) {
                e.printStackTrace();
            }
        }

        return save;
    }

    @Override
    @Caching(evict = {
            @CacheEvict(cacheNames = DEVICE_CACHE, allEntries = true),
            @CacheEvict(cacheNames = DEVICE_CREDENTIALS_CACHE, allEntries = true),
            @CacheEvict(cacheNames = ATTRIBUTE_CACHE, allEntries = true)
    })
    public void deleteTenant(TenantId tenantId) {
        log.trace("Executing deleteTenant [{}]", tenantId);
        Validator.validateId(tenantId, INCORRECT_TENANT_ID + tenantId);
        // 新增判断租户下是否有用户，如果有，不执行删除操作（防止误操作）
        Tenant tenant = tenantDao.findById(tenantId.getId());
        if (tenant == null || userService.findTenantAdmins(tenantId, new TextPageLink(1000)).getData().size() > 0) {
            throw new RuntimeException("删除失败，请确认该企业中无管理员");
        }
        List<User> users = userService.findUserByTenant(tenantId);
        // 删除所有的的普通客户
        for (User user : users) {
            userService.deleteUser(tenantId, user.getId());
        }
        //删除企业下的项目
        projectService.deleteByTenantId(tenantId);
        // 删除其他相关数据
        widgetsBundleService.deleteWidgetsBundlesByTenantId(tenantId);
        dashboardService.deleteDashboardsByTenantId(tenantId);
        deviceService.deleteDevicesByTenantId(tenantId);
        ruleChainService.deleteRuleChainsByTenantId(tenantId);
        tenantDao.removeById(tenantId.getId());
        deleteEntityRelations(tenantId, tenantId);
    }

    @Override
    public TextPageData<Tenant> findTenants(TextPageLink pageLink) {
        log.trace("Executing findTenants pageLink [{}]", pageLink);
        Validator.validatePageLink(pageLink, "Incorrect page link " + pageLink);
        List<Tenant> tenantList = tenantDao.findTenants(new TenantId(EntityId.NULL_UUID), pageLink);
        return new TextPageData<>(tenantList, pageLink);
    }

    @Override
    public List<Tenant> findAll() {
        return tenantDao.find();
    }

    @Override
    public void deleteTenants() {
        log.trace("Executing deleteTenants");
        tenantsRemover.removeEntities(new TenantId(EntityId.NULL_UUID), DEFAULT_TENANT_REGION);
    }

    @Override
    public List<Tenant> findByAppTypeId(String id) {
        return tenantDao.findByAppTypeId(id);
    }

    @Override
    public Object getAllInfo() {
        // 获取所有项目信息
        List<ProjectTreeVO> projectListAll = projectService.findAll();
        // 项目按企业分组
        Map<String, List<ProjectTreeVO>> tenantProjectMap = new HashMap<>();
        projectListAll.forEach(project -> {
            List<ProjectTreeVO> list = null;
            if (tenantProjectMap.containsKey(project.getTenantId())) {
                list = tenantProjectMap.get(project.getTenantId());
            } else {
                list = new ArrayList<>();
            }
            list.add(project);
            tenantProjectMap.put(project.getTenantId(), list);
        });

        // 获取所有主机信息
        List<Device> deviceListAll = deviceService.findAllGateway();
        // 项目按企业分组
        Map<String, List<Device>> tenantDeviceMap = new HashMap<>();
        deviceListAll.forEach(device -> {
            List<Device> list = null;
            if (tenantDeviceMap.containsKey(UUIDConverter.fromTimeUUID(device.getTenantId().getId()))) {
                list = tenantDeviceMap.get(UUIDConverter.fromTimeUUID(device.getTenantId().getId()));
            } else {
                list = new ArrayList<>();
            }
            list.add(device);
            tenantDeviceMap.put(UUIDConverter.fromTimeUUID(device.getTenantId().getId()), list);
        });

        // 获取所有的企业列表
        List<TenantGisVO> tenantList = findAll().parallelStream().map(tenant -> {
            TenantGisVO tenantGisVO = new TenantGisVO();
            BeanUtils.copyProperties(tenant, tenantGisVO);

            // 获取主机信息
            tenantGisVO.setDeviceList(tenantDeviceMap.get(UUIDConverter.fromTimeUUID(tenant.getTenantId().getId())));

            // 获取项目信息
            tenantGisVO.setProjectList(projectService.findProjectList(tenantProjectMap.get(UUIDConverter.fromTimeUUID(tenant.getUuidId())), null, false));

            return tenantGisVO;
        }).collect(Collectors.toList());

        return tenantList;
    }

    @Override
    public List<Tenant> getCurrentTenantList(Authority authority, TenantId tenantId, UserId userId) {
        if (Authority.TENANT_PROMOTE.equals(authority)) {
            List<UserTenant> userTenantList = userTenantRepository.findByUserId(UUIDConverter.fromTimeUUID(userId.getId()));
            if (userTenantList != null) {
                return tenantDao.findByIdIn(userTenantList.stream().map(UserTenant::getTenantId).collect(Collectors.toList()));
            }
        } else if (Authority.TENANT_SUPPORT.equals(authority)) {
            return this.findAll();
        } else {
            return Collections.singletonList(tenantDao.findById(tenantId.getId()));
        }
        return new ArrayList<>();
    }

    @Override
    public void saveUserTenant(User saveUser, Tenant tenant) {
        UserTenant userTenant = new UserTenant();
        userTenant.setUserId(UUIDConverter.fromTimeUUID(saveUser.getUuidId()));
        userTenant.setTenantId(UUIDConverter.fromTimeUUID(tenant.getUuidId()));
        userTenantRepository.save(userTenant);
    }

    @Override
    public JSONObject getOtherTenantList(String userId) {
        JSONObject result = new JSONObject();
        List<UserTenant> userTenantList = userTenantRepository.findAll();
        if (userTenantList == null || userTenantList.isEmpty()) {
            result.put("tenantList", new ArrayList<>());
            result.put("currentUserList", new ArrayList<>());
            return result;
        }
        // 查询tenant列表
        List<Tenant> tenantList = tenantDao.findByIdIn(userTenantList.stream().map(UserTenant::getTenantId).collect(Collectors.toList()));
        Map<String, Tenant> tenantMap = new HashMap<>();
        tenantList.forEach(tenant -> tenantMap.put(UUIDConverter.fromTimeUUID(tenant.getUuidId()), tenant));

        // 查询用户列表
        List<User> all = userService.findAll();
        Map<String, User> userMap = new HashMap<>();
        all.forEach(user -> userMap.put(UUIDConverter.fromTimeUUID(user.getUuidId()), user));

        List<String> currentUserTenantIdList = new ArrayList<>();
        List<Tenant> userAllTenantList = new ArrayList<>();
        for (UserTenant userTenant : userTenantList) {
            if (userTenant.getUserId().equals(userId)) {
                currentUserTenantIdList.add(userTenant.getTenantId());
            }
            Tenant tenant = tenantMap.get(userTenant.getTenantId());
            if (tenant == null) {
                continue;
            }
            User user = userMap.get(userTenant.getUserId());
            if (user != null) {
                tenant.setManagerUserId(userTenant.getUserId());
                tenant.setManagerUserName(user.getFirstName());
            }

            userAllTenantList.add(tenant);
        }

        // 组装数据返回
        result.put("tenantList", userAllTenantList);
        result.put("currentUserList", currentUserTenantIdList);

        return result;
    }

    @Override
    public void setTenantToUser(String toUserId, List<String> tenantIdList) {
        // 删除
        for (String tenantId : tenantIdList) {
            userTenantRepository.deleteByTenantId(tenantId);
        }

        // 添加
        List<UserTenant> addList = new ArrayList<>();
        for (String tenantId : tenantIdList) {
            UserTenant userTenant = new UserTenant();
            userTenant.setUserId(toUserId);
            userTenant.setTenantId(tenantId);

            addList.add(userTenant);
        }

        userTenantRepository.save(addList);
    }

    @Override
    public void createDefaultApplicationAndMenu(TenantId id) {
        try {
            // 创建菜单
            List<ConstantsAttribute> attributes = constantsAttributeDao.getConstantsAttributeByTypeAndKey("defaultMenu", "defaultMenu").get();
            if (attributes == null || attributes.size() < 1) {
                return;
            }
            ConstantsAttribute constantsAttribute = attributes.get(0);
            String value = constantsAttribute.getValue();
            List<TenantMenus> tenantMenus = JSON.parseArray(value).toJavaList(TenantMenus.class);
            tenantMenusService.importMenu(tenantMenus, id);

            // 查询已有的菜单
            List<TenantMenus> menus = tenantMenusService.findByTenantId(id);

            // 创建默认应用
            TenantApplicationEntity tenantApplication = new TenantApplicationEntity();
            tenantApplication.setCreateTime(new Date());
            tenantApplication.setName("支撑系统");
            tenantApplication.setOrderNum(0);
            tenantApplication.setType("1");
            tenantApplication.setMenuIdList(menus.stream().map(TenantMenus::getId).collect(Collectors.toList()));
            tenantApplicationService.saveOrUpdate(tenantApplication);

            //

            // 创建默认项目
            ProjectEntity project = new ProjectEntity();
            project.setType("1");
            project.setName("默认项目");
            project.setTenantId(UUIDConverter.fromTimeUUID(id.getId()));
            project.setCreateTime(System.currentTimeMillis());
            project.setParentId("0");
            projectService.save(project);

        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    private DataValidator<Tenant> tenantValidator =
            new DataValidator<Tenant>() {
                @Override
                protected void validateDataImpl(TenantId tenantId, Tenant tenant) {
                    if (StringUtils.isEmpty(tenant.getTitle())) {
                        throw new DataValidationException("Tenant title should be specified!");
                    }
                    if (!StringUtils.isEmpty(tenant.getEmail())) {
                        validateEmail(tenant.getEmail());
                    }
                }
            };

    private PaginatedRemover<String, Tenant> tenantsRemover =
            new PaginatedRemover<String, Tenant>() {

                @Override
                protected List<Tenant> findEntities(TenantId tenantId, String region, TextPageLink pageLink) {
                    return tenantDao.findTenantsByRegion(tenantId, region, pageLink);
                }

                @Override
                protected void removeEntity(TenantId tenantId, Tenant entity) {
                    deleteTenant(new TenantId(entity.getUuidId()));
                }
            };
}
