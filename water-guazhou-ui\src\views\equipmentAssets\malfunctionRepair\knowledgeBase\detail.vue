<!-- 故障知识库-故障信息 -->
<template>
  <div class="wrapper" :class="{ isDark: useAppStore().isDark }">
    <SLCard class="detail_card" style="padding: 10px">
      <div class="header-wrapper">
        <el-button :text="true" :icon="Back" @click="$router.back()">
        </el-button>
        <span>{{ props.title }} 故障信息</span>
      </div>
      <div class="content-wrapper">
        <Form :key="key" ref="refForm" :config="addOrUpdateConfig"></Form>
      </div>
    </SLCard>
    <CardTable :config="TableConfig" class="card-table"></CardTable>
    <DialogForm ref="refForm1" :config="addOrUpdateConfig1"></DialogForm>
  </div>
</template>

<script lang="ts" setup>
import { Back, Refresh } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import useGlobal from '@/hooks/global/useGlobal';
import { SLConfirm } from '@/utils/Message';
import { useAppStore } from '@/store';
import { ICONS } from '@/common/constans/common';
import {
  getFaultKnowledgeDetailSerch,
  postFaultKnowledgeDetail,
  deleteFaultKnowledgeDetail
} from '@/api/equipment_assets/malfunctionRepair';
import { formatDate } from '@/utils/DateFormatter';

const router = useRouter();

const refForm = ref<IDialogFormIns>();

const refForm1 = ref<IDialogFormIns>();

const key = ref(new Date().toString());

const { $btnPerms } = useGlobal();

const props = reactive<{ id: string; title: string }>({ id: '', title: '' });

// 搜索
const addOrUpdateConfig = reactive<IDialogFormConfig>({
  title: '',
  submit: (params: any) => {
    refreshData(params);
  },

  defaultValue: {},
  group: [
    {
      fields: [
        {
          xl: 5,
          type: 'input',
          label: '故障标题',
          field: 'name'
        },
        {
          xl: 4,
          type: 'btn-group',
          btns: [
            {
              text: '查询',
              icon: ICONS.QUERY,
              perm: true,
              click: () => {
                refForm.value?.Submit();
              }
            },
            {
              type: 'default',
              text: '重置',
              svgIcon: shallowRef(Refresh),
              perm: true,
              click: () => {
                key.value = new Date().toString();
                refreshData();
              }
            },
            {
              type: 'success',
              text: '新增',
              icon: ICONS.ADD,
              perm: true,
              click: () => clickCreatedRole('新建')
            }
          ]
        }
      ]
    }
  ]
});

const addOrUpdateConfig1 = reactive<IDrawerConfig>({
  title: '新增',
  labelWidth: '120px',
  width: '500px',
  submit: (params: any) => {
    params.mainId = props.id;
    let val = '新增成功';
    if (params.id) {
      val = '修改成功';
    }
    postFaultKnowledgeDetail(params).then(() => {
      ElMessage.success(val);
      refreshData();
      refForm1.value?.closeDialog();
    });
  },

  defaultValue: {},
  group: [
    {
      fields: [
        {
          type: 'textarea',
          label: '故障标题',
          field: 'name',
          rules: [{ required: true, message: '请输入故障标题' }]
        },
        {
          type: 'textarea',
          label: '故障具体描述',
          field: 'remark',
          rules: [{ required: true, message: '请输入故障具体描述' }]
        }
      ]
    }
  ]
});

const TableConfig = reactive<ICardTable>({
  defaultExpandAll: true,
  indexVisible: true,
  columns: [
    { label: '故障标题', prop: 'name' },
    { label: '故障具体描述', prop: 'remark' },
    { label: '添加人', prop: 'creatorName' },
    {
      label: '添加时间',
      prop: 'createTime',
      formatter: (row) => formatDate(row.createTime, 'YYYY-MM-DD HH:mm:ss')
    }
  ],
  operationWidth: '320px',
  operations: [
    {
      type: 'primary',
      text: '编辑',
      icon: ICONS.EDIT,
      perm: $btnPerms('RoleManageEdit'),
      click: (row) => clickEdit(row)
    },
    {
      type: 'danger',
      text: '删除',
      perm: $btnPerms('RoleManageDelete'),
      icon: ICONS.DELETE,
      click: (row) => handleDelete(row)
    },
    {
      type: 'primary',
      text: '解决方案',
      icon: ICONS.DETAIL,
      perm: $btnPerms('RoleManageEdit'),
      click: (row) => openDetail(row)
    }
  ],
  dataList: [],
  pagination: {
    hide: true
  }
});

const clickCreatedRole = (title?: any) => {
  addOrUpdateConfig1.title = title || '编辑';
  addOrUpdateConfig1.defaultValue = {};
  refForm1.value?.openDialog();
};

const clickEdit = (row: { [x: string]: any }) => {
  addOrUpdateConfig1.title = '编辑';
  addOrUpdateConfig1.defaultValue = { ...(row || {}) };
  refForm1.value?.openDialog();
};

const handleDelete = (row: { id: string }) => {
  SLConfirm('确定删除指定故障信息?', '删除提示').then(() => {
    deleteFaultKnowledgeDetail([row.id]).then(() => {
      ElMessage.success('删除成功');
      refreshData();
    });
  });
};

const openDetail = (row: any) => {
  router.push({
    name: 'KB_dealWith',
    query: {
      id: row.id,
      title: row.name
    }
  });
};

const refreshData = async (row?: any) => {
  const params = {
    ...(row || {})
  };
  getFaultKnowledgeDetailSerch(props.id, params).then((res) => {
    TableConfig.dataList = res.data.data || [];
  });
};

onMounted(() => {
  props.id = computed(() => router.currentRoute.value.query.id || '') as any;
  props.title = computed(
    () => router.currentRoute.value.query.title || ''
  ) as any;
  refreshData();
});
</script>

<style lang="scss" scoped>
.header-wrapper {
  display: flex;
  width: 100%;
  align-items: center;
}

.content-wrapper {
  margin-top: 20px;
  margin-left: 10px;
}
.detail_card {
  height: 110px;
}
.card-table {
  height: calc(100% - 130px);
  margin-top: 15px;
}
</style>

<style lang="scss">
.el-table__placeholder {
  display: none;
}
</style>
