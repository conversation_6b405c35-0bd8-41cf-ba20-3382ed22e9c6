#
# Copyright © 2019-Now imxushuai
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

server:
  port: 8002
  tomcat:
    basedir: /home/<USER>
spring:
  application:
    name: upload-service
  servlet:
    multipart:
      # 限制单个文件的大小
      max-file-size: 50MB
## Eureka
eureka:
  client:
    service-url:
      #      defaultZone: http://*************:8081/eureka
      defaultZone: http://127.0.0.1:8081/eureka
#  instance:
#    prefer-ip-address:  true  #将自己的ip地址注册到Eureka服务中
#    ip-address: ${IP_ADDRESS:*************}
#    instance-id: ${spring.application.name}:${server.port} #指定实例id

# ===================================================================
# 文件系统minio配置
# ===================================================================
minio:
  endpoint: http://***********:9000
  accessKey: admin
  secretKey: minioadmin
  bucketName: istar
  secure: false
istar:
  upload:
    imageServer: http://***********:9000/
    imageTypes:
      - image/jpeg
      - image/bmp
      - image/png
      - image/svg+xml