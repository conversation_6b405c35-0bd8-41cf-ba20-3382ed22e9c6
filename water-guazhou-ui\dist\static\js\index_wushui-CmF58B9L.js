import{d as X,j as G,dI as H,a6 as I,r as k,c as d,bF as v,s as V,o as Y,bB as A,ay as Z,g as z,n as D,q as f,i as r,p,F as y,bh as L,bo as P,br as $,al as ee,aj as te,C as ae}from"./index-r0dFAfgr.js";import{_ as se}from"./index-C9hz-UZb.js";import{_ as ne}from"./CardTable-rdWOL4_6.js";import{_ as ie}from"./CardSearch-CB_HNR-Q.js";import{l as le,b as oe}from"./echart-D5stWtDc.js";import{a as re,b as ce}from"./statisticalAnalysis-D5JxC4wJ.js";import"./Search-NSrhrIa_.js";const pe={class:"wrapper"},ue={class:"main"},de={class:"left"},fe={class:"right"},me={class:"card-title"},he={class:"title"},ye={class:"card-title"},_e={class:"title"},ge={class:"card-title"},Te={class:"title"},be=X({__name:"index_wushui",setup(ve){const h=G(),q=new H,U=I(),i=k({queryType:"day",chartOption:null,szChartOption:null,detailTitle:"",zxTitle:"",szTitle:""}),_=d(!0),S=d(),w=d(),O=d(),N=d(),B=d(),C=d(),F=d(),R=k({defaultParams:{queryType:"day",date:v().format()},filters:[{type:"input",label:"水源名字",field:"name"},{type:"radio-button",field:"queryType",options:[{label:"日分析",value:"day"},{label:"月分析",value:"month"},{label:"年分析",value:"year"}],label:"分析类型",onChange:e=>{var a;const t=(a=R.filters)==null?void 0:a.find(s=>s.field==="date");t.type=e==="month"?"month":e==="year"?"year":"date",i.queryType=e}},{type:"date",label:"选择时间",field:"date",clearable:!1},{type:"btn-group",btns:[{perm:!0,text:"查询",click:()=>W(),svgIcon:V(ee)},{text:"导出",perm:!0,type:"warning",svgIcon:V(te),click:()=>Q()}]}]}),g=k({loading:!0,dataList:[],highlightCurrentRow:!0,columns:[{prop:"name",label:"水源名称",minWidth:100},{prop:"totalFlow",label:"取水量",unit:"(m³)",minWidth:100},{prop:"energy",label:"用电量",unit:"(kw.h)",minWidth:120},{prop:"totalFlow",label:"本期吨水电耗",unit:"(kw.h/m³)",minWidth:180},{prop:"lastTimeUnitConsumption",label:"上期吨水电耗",unit:"(kw.h/m³)",minWidth:180},{prop:"differenceValue",label:"吨水电耗差值",unit:"(kw.h/m³)",minWidth:180},{prop:"changeRate",label:"变化率",unit:"(%)"}],operations:[],showSummary:!1,operationWidth:"150px",handleRowClick:e=>J(e),pagination:{hide:!0}}),T=k({loading:!0,dataList:[],currentRowKey:"id",highlightCurrentRow:!1,columns:[{prop:"date",label:"时间",unit:"(日)",width:"100px"},{prop:"totalFlow",label:"取水量",unit:"(m³)",width:"120px"},{prop:"energy",label:"用电量",unit:"(kw.h)",width:"120px"},{prop:"unitConsumption",label:"吨位耗电",unit:"(kw.h/m³)",minWidth:150},{prop:"differenceValue",label:"吨水电耗差值",unit:"(kw.h/m³)",minWidth:180},{prop:"changeRate",label:"变化率",unit:"(%)",width:"100px"}],operations:[],showSummary:!1,pagination:{hide:!0},spanMethod:({rowIndex:e,columnIndex:t})=>{let a=1,s=1;return(t===4||t===5)&&(e===0?(a=T.columns.length-1,s=1):(a=0,s=0)),{rowspan:a,colspan:s}}});Y(()=>{W()});const W=()=>{var a;g.loading=!0;const e=((a=O.value)==null?void 0:a.queryParams)||{},t={queryType:e.queryType,start:v(e.date).startOf(i.queryType).valueOf(),end:v(e.date).endOf(i.queryType).valueOf(),name:e.name};re(t).then(s=>{T.loading=!0;const n=s.data.data;g.dataList=n,g.currentRow=n[0],J(n[0])})},J=e=>{var n;const t=((n=O.value)==null?void 0:n.queryParams)||{};i.detailTitle=e.name+"数据详情",i.zxTitle=e.name+"取水量、用电量曲线",i.szTitle=e.name+"吨水电耗曲线";const a=[{date:e.time,totalFlow:e.totalFlow,energy:e.energy,unitConsumption:e.unitConsumption,differenceValue:e.differenceValue,changeRate:e.changeRate},{date:e.lastTime,totalFlow:e.lastTimeTotalFlow,energy:e.lastTimeEnergy,unitConsumption:e.lastTimeUnitConsumption,differenceValue:e.differenceValue,changeRate:e.changeRate}];T.dataList=a;const s={stationId:e.stationId,queryType:t.queryType,start:v(t.date).startOf(i.queryType).valueOf(),end:v(t.date).endOf(i.queryType).valueOf()};_.value=!0,ce(s).then(l=>{const o=l.data.data;M(o)}),g.loading=!1,T.loading=!1},M=e=>{_.value=!0;const t=e.flowList,a=[];for(const l in t)a.push(i.queryType==="day"?parseInt(l)+"时":i.queryType==="month"?parseInt(l)+1+"日":parseInt(l)+1+"月");const s=le();s.yAxis[0].name="电耗(kw/h)",s.xAxis.data=a,j(e,s);const n=oe();n.xAxis.data=a,K(e,n)},j=(e,t)=>{var E;console.log(e.lastTimeFlowList),t.series=[];const a={name:"",smooth:!0,data:[],type:"line",markPoint:{data:[{type:"max",name:"最大值",label:{fontSize:12,color:h.isDark?"#ffffff":"#000000"}},{type:"min",name:"最小值",label:{color:h.isDark?"#ffffff":"#000000"}}]}},s=e==null?void 0:e.lastTimeFlowList,n=JSON.parse(JSON.stringify(a));n.data=s.map(u=>u.value),n.name="取水量"+s[0].ts,t.series.push(n);const l=e==null?void 0:e.flowList,o=JSON.parse(JSON.stringify(a));o.data=l,o.name="取水量"+l[0].ts,t.series.push(o);const b=e==null?void 0:e.energyList,c=JSON.parse(JSON.stringify(a));c.yAxisIndex=1,c.data=b.map(u=>u.value),c.name="耗电量"+b[0].ts,t.series.push(c);const x=e==null?void 0:e.lastTimeEnergyList,m=JSON.parse(JSON.stringify(a));m.yAxisIndex=1,m.data=x.map(u=>u.value),m.name="耗电量"+x[0].ts,t.series.push(m),console.log("这显然图",t.series),(E=S.value)==null||E.clear(),A(()=>{C.value&&U.listenTo(C.value,()=>{var u;i.chartOption=t,(u=S.value)==null||u.resize()})})},K=(e,t)=>{var b;t.series=[];const a={name:"",type:"bar",barMaxWidth:40,data:[],markPoint:{data:[{type:"max",name:"最大值",label:{fontSize:12,color:h.isDark?"#ffffff":"#000000"}},{type:"min",name:"最小值",label:{color:h.isDark?"#ffffff":"#000000"}}]}},s=e==null?void 0:e.lastTimeUnitConsumptionList,n=JSON.parse(JSON.stringify(a));n.data=s.map(c=>c.value),n.name="吨水能耗"+s[0].ts,t.series.push(n);const l=e==null?void 0:e.unitConsumption,o=JSON.parse(JSON.stringify(a));o.data=l.map(c=>c.value),o.name="吨水能耗"+l[0].ts,t.series.push(o),(b=w.value)==null||b.clear(),A(()=>{const x=I({callOnAdd:!0});C.value&&x.listenTo(F.value,()=>{var m;i.szChartOption=t,(m=w.value)==null||m.resize()})}),_.value=!1},Q=()=>{q.addElTable(N.value),q.export()};return(e,t)=>{const a=ie,s=ne,n=se,l=Z("VChart"),o=$;return z(),D("div",pe,[f(a,{ref_key:"cardSearch",ref:O,config:r(R)},null,8,["config"]),p("div",ue,[p("div",de,[f(s,{ref_key:"refCardTable",ref:N,class:"card-table",config:r(g)},null,8,["config"])]),p("div",fe,[f(n,{class:"card-chart",title:" "},{title:y(()=>[p("div",me,[p("span",he,L(r(i).detailTitle),1)])]),default:y(()=>[f(s,{ref_key:"refDetail",ref:B,class:"card-table",config:r(T)},null,8,["config"])]),_:1}),f(n,{title:" ",class:"card-chart"},{title:y(()=>[p("div",ye,[p("span",_e,L(r(i).zxTitle),1)])]),default:y(()=>[P((z(),D("div",{ref_key:"zxDiv",ref:C,class:"chart-box"},[f(l,{ref_key:"refzxChart",ref:S,theme:r(h).isDark?"dark":"light",option:r(i).chartOption},null,8,["theme","option"])])),[[o,r(_)]])]),_:1}),f(n,{title:" ",class:"card-chart"},{title:y(()=>[p("div",ge,[p("span",Te,L(r(i).szTitle),1)])]),default:y(()=>[P((z(),D("div",{ref_key:"szDiv",ref:F,class:"chart-box"},[f(l,{ref_key:"refszChart",ref:w,theme:r(h).isDark?"dark":"light",option:r(i).szChartOption},null,8,["theme","option"])])),[[o,r(_)]])]),_:1})])])])}}}),De=ae(be,[["__scopeId","data-v-6abd0827"]]);export{De as default};
