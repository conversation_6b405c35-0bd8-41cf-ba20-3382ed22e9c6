package org.thingsboard.server.dao.util.imodel.query.purchase;

import org.thingsboard.server.dao.model.sql.purchase.Contract;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ContractPageRequest extends AdvancedPageableQueryEntity<Contract, ContractPageRequest> {
    // 合同编号
    private String code;

    // 合同标题
    private String title;
}
