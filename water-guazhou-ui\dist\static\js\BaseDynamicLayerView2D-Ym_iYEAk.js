import{j as p,i as s,e as r,y as a,a as m}from"./Point-WxyopZva.js";import"./index-r0dFAfgr.js";import{a as h}from"./BitmapContainer-ziwQ7v9F.js";import{f as n,u as d}from"./LayerView-BSt9B8Gh.js";import{v as c}from"./ExportStrategy-BadISnDs.js";import{i as u}from"./RefreshableLayerView-DUeNHzrW.js";import"./WGLContainer-Dyx9110G.js";import"./MapView-DaoQedLH.js";import"./widget-BcWKanF2.js";import"./pe-B8dP0-Ut.js";import"./definitions-826PWLuy.js";import"./FramebufferObject-8j9PRuxE.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./VertexElementDescriptor-BOD-G50G.js";import"./vec4f32-CjrfB-0a.js";import"./color-DAS1c3my.js";import"./enums-B5k73o5q.js";import"./enums-L38xj_2E.js";import"./number-CoJp78Rz.js";import"./ProgramTemplate-tdUBoAol.js";import"./MaterialKey-BYd7cMLJ.js";import"./alignmentUtils-CkNI7z7C.js";import"./utils-DPUVnAXL.js";import"./StyleDefinition-Bnnz5uyC.js";import"./config-MDUrh2eL.js";import"./GeometryUtils-BRRfazic.js";import"./Container-BwXq1a-x.js";import"./earcut-BJup91r2.js";import"./Bitmap-CraE42_6.js";let t=class extends u(n(d)){update(e){this._strategy.update(e).catch(i=>{p(i)||s.getLogger(this.declaredClass).error(i)}),this.notifyChange("updating")}attach(){this._bitmapContainer=new h,this.container.addChild(this._bitmapContainer),this._strategy=new c({container:this._bitmapContainer,fetchSource:this.fetchBitmapData.bind(this),requestUpdate:this.requestUpdate.bind(this)})}detach(){this._strategy.destroy(),this._strategy=null,this.container.removeChild(this._bitmapContainer),this._bitmapContainer.removeAllChildren()}moveStart(){}viewChange(){}moveEnd(){this.requestUpdate()}fetchBitmapData(e,i,o){return this.layer.fetchImageBitmap(e,i,o)}async doRefresh(){this.requestUpdate()}isUpdating(){return this._strategy.updating||this.updateRequested}};r([a()],t.prototype,"_strategy",void 0),r([a()],t.prototype,"updating",void 0),t=r([m("esri.views.2d.layers.BaseDynamicLayerView2D")],t);const M=t;export{M as default};
