package org.thingsboard.server.dao.sql.operatingIncomeInput;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.thingsboard.server.dao.model.sql.OperatingIncomeInput;

import java.util.List;

public interface OperatingIncomeInputRepository extends JpaRepository<OperatingIncomeInput, String> {

    @Query("SELECT o FROM OperatingIncomeInput o " +
            "WHERE o.stationId = ?1 AND o.ts LIKE ?2% AND o.tenantId = ?3 " +
            "ORDER BY o.ts")
    List<OperatingIncomeInput> findList(String stationId, String year, String tenantId);

    List<OperatingIncomeInput> findByStationIdAndTenantId(String stationId, String tenantId);
}
