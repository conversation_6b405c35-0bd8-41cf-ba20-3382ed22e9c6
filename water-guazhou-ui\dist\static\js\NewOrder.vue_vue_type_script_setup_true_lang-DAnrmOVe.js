import{_ as M}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as x}from"./FormMap-BGaXSqQF.js";import"./index-0NlGN6gS.js";import{I as S}from"./utils-D5nxoMq3.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import{d as U,c as F,r as C,u as q,b as u,am as T,g as D,h as W,F as B,n as E,q as N,i as m,an as v}from"./index-r0dFAfgr.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./project-DUuzYgGl.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import{u as G}from"./usePartition-DkcY9fQ2.js";import{u as J}from"./useUser-Blb5V02j.js";import{W as R,g as j,a as z,f as $}from"./config-DqqM5K5L.js";import{P as A}from"./lossControl-DNefZk8I.js";const H={key:0,style:{width:"100%",height:"250px"}},he=U({__name:"NewOrder",props:{needPartition:{type:Boolean},defaultValues:{}},emits:["success","fail"],setup(L,{expose:O,emit:k}){var y,_;const f=F(),{getUserOptions:w}=J(),g=k,a=F(),h=C({coordinate:void 0}),n=L,l=G(),I=n.needPartition?[{type:"select-tree",label:"分区",field:"partitionId",placeholder:"请选择分区",checkStrictly:!0,autoFillOptions(e){l.getTree().then(()=>{e.options=l.Tree.value||[]})},onChange(e){d(e)},rules:[{required:!0,message:"请选择分区"}]}]:[],d=e=>{l.getPartitionInfo(e).then(t=>{var o,r;try{const s=(o=t.data)==null?void 0:o.geom;if(!s)return;const c=JSON.parse(s),i=(r=f.value)==null?void 0:r.getView(),b=l.createPartitionGraphic(i,c);if(!b)return;const P=b.geometry.extent;i==null||i.goTo(P)}catch{}}).catch(()=>{})},p=C({dialogWidth:600,title:"新建工单",labelPosition:"right",group:[{fields:[{type:"input",label:"标题",field:"title",rules:[{required:!0,message:"请填写标题"}],placeholder:"请填写标题"},{type:"select",label:"接收人",field:"stepProcessUserId",rules:[{required:!0,message:"请选择接收人"}],placeholder:"请选择",async autoFillOptions(e){const t=await w(!1,{authority:"CUSTOMER_USER"});e.options=t}},{field:"processLevelLabel",label:"处理级别",type:"select",options:R()},{type:"select",label:"紧急程度",field:"level",options:j(),rules:[{required:!0,message:"请选择紧急程度"}]},{type:"select-tree",label:"事件类型",field:"type",options:z(),rules:[{required:!0,message:"请选择事件类型"}]},...I,{field:"coordinate",label:"坐标",type:"form-map",showInput:!0,onChange:e=>{S({lon:(e==null?void 0:e[0])||"0",lat:(e==null?void 0:e[1])||"0"}).then(t=>{var o,r;(r=(o=a.value)==null?void 0:o.refForm)!=null&&r.dataForm&&(a.value.refForm.dataForm.coordinate=e==null?void 0:e.join(","),a.value.refForm.dataForm.address=t.data.result.formatted_address||"")})}},{field:"address",label:"地址",type:"input"},{type:"textarea",label:"事件描述",field:"remark",placeholder:"请输入事件描述"}]}],defaultValue:{isDirectDispatch:!0,source:"漏损控制",organizerId:(_=(y=q().user)==null?void 0:y.id)==null?void 0:_.id},async submit(e){var t;p.submitting=!0;try{const o={...e,...n.defaultValues||{},processLevel:$(e.processLevelLabel)},r=await A(o);r.data.code===200?(u.success("提交成功"),g("success"),(t=a.value)==null||t.closeDialog()):(u.error(r.data.message),g("fail"))}catch{u.error("提交失败")}p.submitting=!1}});T(()=>{var e,t,o;return(o=(t=(e=a.value)==null?void 0:e.refForm)==null?void 0:t.dataForm)==null?void 0:o.partitionId},e=>{e&&d(e)}),O({openDialog:()=>{var e;return(e=a.value)==null?void 0:e.openDialog()},refDialog:a});const V=()=>{var e;(e=n.defaultValues)!=null&&e.partitionId&&d(n.defaultValues.partitionId)};return(e,t)=>{const o=x,r=M;return D(),W(r,{ref_key:"refDialog",ref:a,config:m(p)},{fieldSlot:B(({config:s,row:c})=>[s.field==="coordinate"?(D(),E("div",H,[N(o,{ref_key:"refMap",ref:f,modelValue:m(h).coordinate,"onUpdate:modelValue":t[0]||(t[0]=i=>m(h).coordinate=i),row:c,"show-input":s.showInput,disabled:s.disabled,readonly:s.readonly,onChange:s.onChange,onLoaded:V},null,8,["modelValue","row","show-input","disabled","readonly","onChange"])])):v("",!0)]),_:1},8,["config"])}}});export{he as _};
