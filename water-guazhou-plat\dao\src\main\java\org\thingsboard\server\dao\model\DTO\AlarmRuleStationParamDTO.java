package org.thingsboard.server.dao.model.DTO;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 报警Version2 报警规则
 */
@Data
public class AlarmRuleStationParamDTO implements Serializable {

    private String stationId;

    private String stationName;

    private String deviceId;

    private String deviceName;

    private String attr;

    private String type;

    private Integer dataNum;

    private BigDecimal maxValue;

    private BigDecimal minValue;

    private BigDecimal upLimitValue;

    private BigDecimal downLimitValue;

}
