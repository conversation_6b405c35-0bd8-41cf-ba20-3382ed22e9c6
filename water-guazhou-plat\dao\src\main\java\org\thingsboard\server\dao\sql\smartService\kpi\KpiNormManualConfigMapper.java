package org.thingsboard.server.dao.sql.smartService.kpi;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.smartService.kpi.KpiNormManualConfig;

import java.util.List;

/**
 * userMybatis
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-08-27
 */
@Mapper
public interface KpiNormManualConfigMapper extends BaseMapper<KpiNormManualConfig> {

    List<KpiNormManualConfig> getList(@Param("enabled") Boolean enabled, @Param("month") String month, @Param("seatsId") String seatsId, @Param("score") Integer score, @Param("page") int page, @Param("size") int size, @Param("tenantId") String tenantId);

    int getListCount(@Param("enabled") Boolean enabled, @Param("month") String month, @Param("seatsId") String seatsId, @Param("score") Integer score, @Param("tenantId") String tenantId);
}
