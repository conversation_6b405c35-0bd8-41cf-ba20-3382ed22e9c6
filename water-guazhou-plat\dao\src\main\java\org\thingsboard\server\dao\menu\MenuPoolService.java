/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.menu;

import org.apache.commons.lang3.StringUtils;
import org.codehaus.jackson.map.ObjectMapper;
import org.thingsboard.server.common.data.id.MenuPoolId;
import org.thingsboard.server.common.data.menu.Menu;
import org.thingsboard.server.common.data.menu.MenuMeta;
import org.thingsboard.server.common.data.menu.MenuPool;
import org.thingsboard.server.common.data.menu.MenuPoolVO;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JacksonUtil;

import java.io.IOException;
import java.util.*;

public interface MenuPoolService {


    /**
     * 查询指定上级菜单的子菜单
     * @param parentId
     * @return
     */
    List<Menu> findMenuByParentId(MenuPoolId parentId);

    /**
     * 查询全部菜单
     * @return
     * @param rootId
     */
    List<Menu> findAll(MenuPoolId rootId);

    /**
     * 按ID查询
     * @param id
     * @return
     */
    Menu findById(MenuPoolId id);

    /**
     * 保存菜单
     * @param menu
     * @param parentId
     * @return
     */
    MenuPool saveMenu(Menu menu, MenuPoolId parentId);

    /**
     * 获取menupool树
     * @return
     */
    List<MenuPoolVO> getSelectableTree();

    List<MenuPool> findAll();



    /**
     * MenuPoolList转换成MenuList
     *
     * @param menuPoolList
     * @return
     */
    default List<Menu> MenuPoolListToMenuList(List<MenuPool> menuPoolList) {
        List<Menu> resultList = new ArrayList<>();
        for (MenuPool menuPool : menuPoolList) {
            resultList.add(MenuPoolToMenu(menuPool));
        }

        return resultList;
    }

    /**
     * 将menupool转换为menu
     *
     * @param menuPool 父菜单
     * @return
     */
    default Menu MenuPoolToMenu(MenuPool menuPool) {
        try {
            Menu menu = new Menu();
            menu.setId(menuPool.getId().getId().toString());
            menu.setParentId(menuPool.getParentId().getId().toString());
            menu.setMeta(buildMeta(menuPool));
            menu.setOrderNum(menuPool.getOrderNum());
            menu.setType(menuPool.getType());
            String additionalInfo = menuPool.getAdditionalInfo();
            Map map = new ObjectMapper().readValue(additionalInfo, Map.class);
            menu.setPath((String) map.get("path"));
            menu.setComponent((String) map.get("component"));
            menu.setName((String) map.get("name"));

            return menu;
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 创建菜单meta
     *
     * @param menuPool
     * @return
     */
    default MenuMeta buildMeta(MenuPool menuPool) {
        MenuMeta meta = new MenuMeta();
        String icon = "form";
        if (StringUtils.isNotBlank(menuPool.getIcon())) {
            icon = menuPool.getIcon();
        }
        meta.setIcon(icon);
        meta.setTitle(menuPool.getDefaultName());
        meta.setRoles(new String[]{"TENANT_ADMIN","TENANT_SYS"});

        return meta;
    }


    /**
     * menu转换为menupool
     *
     * @param menu
     * @return
     */
    default MenuPool MenuToMenuPool(Menu menu) {
        MenuPool menuPool = new MenuPool();
        // 设置扩展信息
        Map<String, String> map = new HashMap<>();
        map.put("path", menu.getPath());
        map.put("name", menu.getName());
        map.put("component", menu.getComponent());
        menuPool.setAdditionalInfo(JacksonUtil.toString(map));
        // 设置基本信息
        if (menu.getId() != null) {
            menuPool.setId(new MenuPoolId(UUID.fromString(menu.getId())));
        }
        if (menu.getParentId() != null && menu.getParentId().trim().length() > 0) {
            menuPool.setId(new MenuPoolId(UUID.fromString(menu.getParentId())));
        } else {
            menuPool.setParentId(new MenuPoolId(ModelConstants.MENU_POOL_ROOT));
        }
        menuPool.setDefaultName(menu.getMeta().getTitle());
        menuPool.setOrderNum(menu.getOrderNum());
        menuPool.setType(menu.getType());
        try {
            String icon = menu.getMeta().getIcon();
            menuPool.setIcon(icon);
        } catch (Exception ignored) {
        }

        return menuPool;
    }

    List<MenuPool> findByParentId(MenuPoolId id);

}
