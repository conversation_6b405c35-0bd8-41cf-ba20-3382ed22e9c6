package org.thingsboard.server.dao.model.sql.purchase;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class DevicePurchaseInquiry {
    // id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    // 采购单子表ID
    private String purchaseDetailId;

    // 供应商ID
    private String supplierId;

    // 联系人
    private String contact;

    // 联系方式
    private String contactPhone;

    // 单价
    private Double price;

    // 询价日期
    private Date inquiryTime;

    // 意向供应商
    private Boolean intentionSupplier;

    // 创建时间
    private Date createTime;

    // 附件
    private String file;

    // 租户ID
    private String tenantId;
    
}
