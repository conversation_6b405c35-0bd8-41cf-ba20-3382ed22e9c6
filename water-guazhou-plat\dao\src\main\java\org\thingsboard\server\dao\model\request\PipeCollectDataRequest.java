package org.thingsboard.server.dao.model.request;

import lombok.Data;

/**
 * 管网采集
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2023-05-31
 */
@Data
public class PipeCollectDataRequest {

    private int page;

    private int size;

    private String name;

    private String mainId;

    private String layerId;

    private Long start;

    private Long end;

    private String creator;

    private String tenantId;
}
