package org.thingsboard.server.dao.smartPipe;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.dao.model.request.PipeCollectDataRequest;
import org.thingsboard.server.dao.model.sql.smartProduction.pipe.PipeCollectData;
import org.thingsboard.server.dao.sql.smartPipe.PipeCollectDataMapper;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class PipeCollectDataServiceImpl implements PipeCollectDataService {

    @Autowired
    private PipeCollectDataMapper pipeCollectDataMapper;

    @Override
    @Transactional
    public List<PipeCollectData> save(List<PipeCollectData> pipeCollectDataList, String tenantId, String userId) {
        Date date = new Date();
        List<String> ids = pipeCollectDataList.stream().map(a -> a.getId()).collect(Collectors.toList());
        if (ids.size() > 0) {
            pipeCollectDataMapper.deleteBatchIds(ids);
        }
        pipeCollectDataMapper.batchInsert(pipeCollectDataList, tenantId, userId, date);
        return pipeCollectDataList;
    }

    @Override
    public List<PipeCollectData> getList(PipeCollectDataRequest pipeCollectDataRequest) {
        List<PipeCollectData> pipeCollectDataList = pipeCollectDataMapper.getList(pipeCollectDataRequest);
        return pipeCollectDataList;
    }

    @Override
    public void delete(List<String> ids) {
        pipeCollectDataMapper.deleteBatchIds(ids);
    }
}
