/**
 * Copyright © 2016-2019 The Thingsboard Authors
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.video;


import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.ny.NyVideo;
import org.thingsboard.server.common.data.ny.NyVideoBO;
import org.thingsboard.server.dao.model.DTO.TreeNodeDTO;
import org.thingsboard.server.dao.model.DTO.VideoSaveDTO;
import org.thingsboard.server.dao.model.sql.VideoEntity;
import org.thingsboard.server.dao.util.CameraUtil;
import org.thingsboard.server.dao.util.TreeUtil;
import org.thingsboard.server.dao.util.imodel.query.video.VideoMonitoringPageRequest;

import java.util.Iterator;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

@Slf4j
@Service
public class VideoServiceImpl implements VideoService {


    @Autowired
    private VideoDao videoDao;

    @Autowired
    private VideoGroupService videoGroupService;

    @Autowired
    private CameraUtil cameraUtil;

    @Override
    public VideoEntity save(VideoEntity videoEntity) {
        videoEntity.setUpdateTime(System.currentTimeMillis());
        return videoDao.save(videoEntity);
    }

    @Override
    public List<VideoEntity> findByProject(String projectId, String name, String isBind, String groupId) {
        name = "%" + name + "%";
        groupId = "%" + groupId + "%";
        return videoDao.findByProject(projectId, name, isBind, groupId);
    }

    @Override
    public boolean delete(String id) {
        return videoDao.delete(id);
    }

    @Override
    public boolean deleteAll(List<String> ids) {
        return videoDao.deleteAll(ids);
    }

    @Override
    public VideoEntity save(NyVideoBO nyVideoBO, TenantId tenantId) {
        VideoEntity videoEntity = new VideoEntity();
        if (StringUtils.isNotBlank(nyVideoBO.getId())) {
            videoEntity = videoDao.findById(nyVideoBO.getId());
        }
        NyVideo nyVideo = nyVideoBO.getNyVideo();

        videoEntity.setProjectId(nyVideoBO.getProjectId());
        videoEntity.setChannelId(nyVideo.getDeviceid());
        videoEntity.setName(nyVideoBO.getName());
        videoEntity.setSerialNumber(nyVideo.getDeviceid());
        videoEntity.setUpdateTime(System.currentTimeMillis());
        videoEntity.setVideoType("NY");
        videoEntity.setTenantId(tenantId.getId().toString());
        videoEntity.setAppSecret(nyVideo.getDevicepwd());
        videoEntity.setAdditionalInfo(nyVideoBO.getAdditionalInfo());

        return videoDao.save(videoEntity);
    }

    @Override
    public List<VideoEntity> findByProjectAndType(String projectId, String type) {
        return videoDao.findByProjectAndType(projectId, type);
    }

    @Override
    public List<VideoEntity> findByType(String type, TenantId tenantId) {
        return videoDao.findByVideoType(type, tenantId);
    }

    @Override
    public List<VideoEntity> findAll(String name, String tenantId) {
        return videoDao.findAllByName(name, tenantId);
    }

    @Override
    @Transactional
    public void batchSave(VideoSaveDTO videoSaveDTO, String tenantId) {

        List<VideoEntity> videoEntityList = videoSaveDTO.getVideoList();

        ExecutorService executorService = Executors.newCachedThreadPool();
        for (VideoEntity videoEntity : videoEntityList) {
            Integer orderNum = videoEntity.getOrderNum();
            if (StringUtils.isNotBlank(videoEntity.getId())) {
                VideoEntity byId = videoDao.findById(videoEntity.getId());
                if (byId != null) {
                    videoEntity = byId;
                } else {
                    videoEntity.setId(null);
                }
            }
            if (orderNum != null) {
                videoEntity.setOrderNum(orderNum);
            }
            videoEntity.setTenantId(tenantId);
            videoEntity.setProjectId(videoSaveDTO.getProjectId());
            videoEntity.setGroupId(videoSaveDTO.getGroupId());
            VideoEntity finalVideoEntity = videoEntity;
            executorService.execute(() -> {
                videoDao.save(finalVideoEntity);
            });
        }
        executorService.shutdown();
        while (!executorService.isTerminated()) {

        }
    }

    @Override
    public List<TreeNodeDTO> getTreeByProjectId(String projectId, String tenantId) {
        // 项目
        List<TreeNodeDTO> treeByProjectId = videoGroupService.getTreeByAllProject(tenantId);
        // 摄像头
        List<VideoEntity> videoEntityList = videoDao.getAllBindList();
        try {
            cameraUtil.setStatus(videoEntityList);
        } catch (Exception e) {
            e.printStackTrace();
        }
        // 构建树节点
        List<TreeNodeDTO> videoList = videoEntityList.stream().map(area ->
                TreeNodeDTO.builder()
                        .id(area.getId())
                        .parentId(area.getGroupId())
                        .type("video")
                        .name(area.getName())
                        .nodeDetail(area)
                        .icon("flat-color-icons:video-call")
                        .build()
        ).collect(Collectors.toList());

        treeByProjectId.addAll(videoList);
        List<TreeNodeDTO> treeNodeDTOS = TreeUtil.listToTree(treeByProjectId, "0");
        for (TreeNodeDTO treeNodeDTO : treeNodeDTOS) {
            this.countNodes(treeNodeDTO, "");
        }

        return treeNodeDTOS;
    }
    public int countNodes(TreeNodeDTO root, String videoType) {
        if (root.getNum() == null) {
            root.setNum(0);
        }
        int count = root.getNum(); // 加上根节点
        Iterator<TreeNodeDTO> iterator = root.getChildren().iterator();
        while (iterator.hasNext()) {
            TreeNodeDTO next = iterator.next();
            int count1 = countNodes(next, videoType);
            if (StringUtils.isNotBlank(videoType) && !next.getType().equals("video") && (next.getChildren().isEmpty())) {
                iterator.remove();
                continue;
            }
            count += count1; // 递归计算每个子节点的数量
        }
        root.setNum(count);
        return count;
    }

    @Override
    public Page<VideoEntity> findByPage(VideoMonitoringPageRequest request) {
        // 处理查询参数，避免空字符串
        if (StringUtils.isBlank(request.getSerialNumber())) {
            request.setSerialNumber(null);
        }

        if (StringUtils.isBlank(request.getName())) {
            request.setName(null);
        }

        if (StringUtils.isBlank(request.getProjectId())) {
            request.setProjectId(null);
        }

        if (StringUtils.isBlank(request.getLongitude())) {
            request.setLongitude(null);
        }

        if (StringUtils.isBlank(request.getLatitude())) {
            request.setLatitude(null);
        }

        if (StringUtils.isBlank(request.getLocation())) {
            request.setLocation(null);
        }

        // 调用 DAO 层的分页查询方法
        return videoDao.findByPage(request.getPage(), request.getSize(), request);
    }
}
