import { ref } from 'vue';
import {
  getProjectRelationByEntityTypeAndEntityId,
  getProjectRoot
} from '@/api/project';
import useGlobal from '@/hooks/global/useGlobal';
import { formatTree } from '@/utils/GlobalHelper';
import { useUserStore } from '@/store';

const useAreaTree = () => {
  const { $formatTree } = useGlobal();
  const getAreaTreeData = async () => {
    const res = await getProjectRoot(false);
    const result = $formatTree(res.data);
    areaTree.value = result;
    return result;
  };
  const getProjectTreeData = async () => {
    const userStore = useUserStore();
    const res = await getProjectRoot();
    const curUserRole = userStore.user?.authority;
    const isUser = curUserRole?.indexOf('CUSTOMER_USER') !== -1;
    let res1;
    if (isUser) {
      res1 = await getProjectRelationByEntityTypeAndEntityId(
        'USER',
        userStore.user?.id?.id
      );
    }
    const userdata: any[] = res1?.data?.map((item) => item.id) || [];
    const data = formatTree(
      res.data || [],
      {
        label: 'name',
        value: 'id',
        id: 'id',
        children: 'children'
      },
      undefined,
      'project',
      (data) => {
        if (isUser) {
          data.disabled = userdata.indexOf(data.value) === -1;
        }
        return data;
      }
    );
    return data;
  };
  const getCurUserProjectTreeData = async () => {
    const userStore = useUserStore();
    const curUserRole = userStore.user?.authority;
    const isUser = curUserRole?.indexOf('CUSTOMER_USER') !== -1;
    if (isUser) {
      const res = await getProjectRelationByEntityTypeAndEntityId(
        'USER',
        userStore.user?.id?.id
      );
      return formatTree(
        res.data || [],
        {
          label: 'name',
          value: 'id',
          id: 'id',
          children: 'children'
        },
        undefined,
        'project'
      );
    }
    return getProjectTreeData();
  };
  const areaTree = ref<NormalOption[]>([]);
  return {
    areaTree,
    getAreaTreeData,
    getProjectTreeData,
    getCurUserProjectTreeData
  };
};
export default useAreaTree;
