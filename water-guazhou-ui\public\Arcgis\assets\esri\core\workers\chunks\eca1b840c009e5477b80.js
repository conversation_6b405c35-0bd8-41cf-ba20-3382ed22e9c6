"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[8008,8062],{52104:(e,a,t)=>{t.d(a,{V:()=>n});var r=t(96187);async function n(e,a){const t=await(0,r.T)(e,a);t.layers=t.layers.filter(s);const n={serviceJSON:t};if((t.currentVersion??0)<10.5)return n;const i=await(0,r.T)(e+"/layers",a);return n.layersJSON={layers:i.layers.filter(s),tables:i.tables},n}function s(e){return!e.type||"Feature Layer"===e.type}},87344:(e,a,t)=>{t.d(a,{T:()=>r});const r={BingMapsLayer:async()=>(await Promise.all([t.e(2710),t.e(1612),t.e(8153)]).then(t.bind(t,2723))).default,BuildingSceneLayer:async()=>(await Promise.all([t.e(2710),t.e(1612),t.e(4729),t.e(9790),t.e(8244),t.e(1223),t.e(1423),t.e(4165),t.e(911),t.e(5546),t.e(9942),t.e(9238),t.e(3975),t.e(5590),t.e(8239),t.e(223)]).then(t.bind(t,30223))).default,CSVLayer:async()=>(await Promise.all([t.e(2710),t.e(1612),t.e(4729),t.e(9790),t.e(8244),t.e(1223),t.e(1423),t.e(4165),t.e(911),t.e(5546),t.e(9942),t.e(9238),t.e(9675)]).then(t.bind(t,45425))).default,DimensionLayer:async()=>(await Promise.all([t.e(5103),t.e(81)]).then(t.bind(t,40081))).default,ElevationLayer:async()=>(await Promise.all([t.e(3055),t.e(9230)]).then(t.bind(t,65665))).default,FeatureLayer:async()=>(await Promise.all([t.e(2710),t.e(1612),t.e(4729),t.e(9790),t.e(8244),t.e(1223),t.e(1423),t.e(4165),t.e(911),t.e(5546),t.e(9942),t.e(9238),t.e(5159)]).then(t.bind(t,19238))).default,GeoJSONLayer:async()=>(await Promise.all([t.e(2710),t.e(1612),t.e(4729),t.e(9790),t.e(8244),t.e(1223),t.e(1423),t.e(4165),t.e(911),t.e(5546),t.e(7202)]).then(t.bind(t,23477))).default,GeoRSSLayer:async()=>(await Promise.all([t.e(2710),t.e(1612),t.e(9790),t.e(9538)]).then(t.bind(t,3723))).default,GroupLayer:async()=>(await Promise.all([t.e(2710),t.e(1612),t.e(7483)]).then(t.bind(t,89348))).default,ImageryLayer:async()=>(await Promise.all([t.e(2710),t.e(1612),t.e(4729),t.e(9790),t.e(8244),t.e(1223),t.e(4165),t.e(4599),t.e(6610),t.e(4242),t.e(665)]).then(t.bind(t,45378))).default,ImageryTileLayer:async()=>(await Promise.all([t.e(2710),t.e(1612),t.e(4729),t.e(9790),t.e(8244),t.e(1223),t.e(3055),t.e(6610),t.e(4242),t.e(8104),t.e(8915)]).then(t.bind(t,92045))).default,IntegratedMeshLayer:async()=>(await Promise.all([t.e(2710),t.e(3975),t.e(1537)]).then(t.bind(t,9310))).default,KMLLayer:async()=>(await Promise.all([t.e(2710),t.e(1612),t.e(4729),t.e(9790),t.e(8244),t.e(1223),t.e(1423),t.e(739)]).then(t.bind(t,42756))).default,LineOfSightLayer:async()=>(await Promise.all([t.e(5103),t.e(690)]).then(t.bind(t,30690))).default,MapImageLayer:async()=>(await Promise.all([t.e(2710),t.e(1612),t.e(4729),t.e(9790),t.e(8244),t.e(1223),t.e(1423),t.e(4165),t.e(911),t.e(4599),t.e(6368),t.e(7374)]).then(t.bind(t,27374))).default,MapNotesLayer:async()=>(await Promise.all([t.e(2710),t.e(1612),t.e(4729),t.e(9790),t.e(8244),t.e(1223),t.e(1423),t.e(4165),t.e(911),t.e(5546),t.e(9942),t.e(9238),t.e(1227)]).then(t.bind(t,62128))).default,MediaLayer:async()=>(await Promise.all([t.e(2710),t.e(1612),t.e(1984)]).then(t.bind(t,57765))).default,OGCFeatureLayer:async()=>(await Promise.all([t.e(2710),t.e(1612),t.e(4729),t.e(9790),t.e(8244),t.e(1223),t.e(1423),t.e(4165),t.e(911),t.e(5546),t.e(2855)]).then(t.bind(t,88068))).default,OpenStreetMapLayer:async()=>(await Promise.all([t.e(2710),t.e(1612),t.e(9971),t.e(6237)]).then(t.bind(t,66237))).default,OrientedImageryLayer:async()=>(await Promise.all([t.e(2710),t.e(1612),t.e(4729),t.e(9790),t.e(8244),t.e(1223),t.e(1423),t.e(4165),t.e(911),t.e(5546),t.e(9942),t.e(9238),t.e(5132)]).then(t.bind(t,76604))).default,PointCloudLayer:async()=>(await Promise.all([t.e(2710),t.e(4729),t.e(3975),t.e(8643),t.e(6772)]).then(t.bind(t,10608))).default,RouteLayer:async()=>(await Promise.all([t.e(2710),t.e(1612),t.e(4729),t.e(9790),t.e(8244),t.e(1223),t.e(1423),t.e(4286)]).then(t.bind(t,40153))).default,SceneLayer:async()=>(await Promise.all([t.e(2710),t.e(1612),t.e(4729),t.e(9790),t.e(8244),t.e(1223),t.e(1423),t.e(4165),t.e(911),t.e(5546),t.e(9942),t.e(9238),t.e(3975),t.e(5590),t.e(8239),t.e(7476)]).then(t.bind(t,57476))).default,StreamLayer:async()=>(await Promise.all([t.e(2710),t.e(1612),t.e(4729),t.e(9790),t.e(8244),t.e(1223),t.e(1423),t.e(4165),t.e(911),t.e(5546),t.e(5004)]).then(t.bind(t,88387))).default,SubtypeGroupLayer:async()=>(await Promise.all([t.e(2710),t.e(1612),t.e(4729),t.e(9790),t.e(8244),t.e(1223),t.e(1423),t.e(4165),t.e(911),t.e(9942),t.e(908)]).then(t.bind(t,30359))).default,TileLayer:async()=>(await Promise.all([t.e(2710),t.e(1612),t.e(4729),t.e(9790),t.e(8244),t.e(1223),t.e(1423),t.e(4165),t.e(911),t.e(4599),t.e(3055),t.e(6368),t.e(8636)]).then(t.bind(t,98636))).default,UnknownLayer:async()=>(await t.e(4166).then(t.bind(t,44166))).default,UnsupportedLayer:async()=>(await t.e(9296).then(t.bind(t,39296))).default,VectorTileLayer:async()=>(await Promise.all([t.e(2710),t.e(1612),t.e(3055),t.e(4325),t.e(1785),t.e(719)]).then(t.bind(t,94756))).default,VoxelLayer:async()=>(await Promise.all([t.e(2710),t.e(4729),t.e(3975),t.e(9327)]).then(t.bind(t,28865))).default,WFSLayer:async()=>(await Promise.all([t.e(2710),t.e(1612),t.e(4729),t.e(9790),t.e(8244),t.e(1223),t.e(1423),t.e(4165),t.e(911),t.e(5546),t.e(3974)]).then(t.bind(t,12653))).default,WMSLayer:async()=>(await Promise.all([t.e(2710),t.e(1612),t.e(4729),t.e(9790),t.e(2462)]).then(t.bind(t,25906))).default,WMTSLayer:async()=>(await Promise.all([t.e(2710),t.e(1612),t.e(9971),t.e(5853)]).then(t.bind(t,55853))).default,WebTileLayer:async()=>(await Promise.all([t.e(2710),t.e(1612),t.e(9971)]).then(t.bind(t,16199))).default}},33516:(e,a,t)=>{t.d(a,{Y:()=>i,h:()=>s});var r=t(17452),n=t(65587);function s(e){return{origin:"portal-item",url:(0,r.mN)(e.itemUrl),portal:e.portal||n.Z.getDefault(),portalItem:e,readResourcePaths:[]}}function i(e){return{origin:"portal-item",messages:[],writtenProperties:[],url:e.itemUrl?(0,r.mN)(e.itemUrl):null,portal:e.portal||n.Z.getDefault(),portalItem:e}}},18062:(e,a,t)=>{t.r(a),t.d(a,{getFirstLayerOrTableId:()=>h,getNumLayersAndTables:()=>g,getSubtypeGroupLayerIds:()=>S,load:()=>p,preprocessFSItemData:()=>b});var r=t(20102),n=t(87085),s=t(66677),i=t(52104),l=t(65587),o=t(15235),c=t(33516),u=t(14661),y=t(40555),d=t(96187);async function p(e,a){const t=e.instance.portalItem;if(t&&t.id)return await t.load(a),function(e){const a=e.instance.portalItem;if(!a?.type||!e.supportedTypes.includes(a.type))throw new r.Z("portal:invalid-layer-item-type","Invalid layer item type '${type}', expected '${expectedType}'",{type:a?.type,expectedType:e.supportedTypes.join(", ")})}(e),async function(e,a){const t=e.instance,n=t.portalItem;if(!n)return;const{url:s,title:l}=n,o=(0,c.h)(n);if("group"===t.type)return t.read({title:l},o),async function(e,a){let t;const{portalItem:n}=e;if(!n)return;const s=n.type,l=a.layerModuleTypeMap,o=(0,u._$)(n,"Oriented Imagery Layer")??!1;switch(s){case"Feature Service":t=o?l.OrientedImageryLayer:l.FeatureLayer;break;case"Stream Service":t=l.StreamLayer;break;case"Scene Service":t=l.SceneLayer;break;case"Feature Collection":t=l.FeatureLayer;break;default:throw new r.Z("portal:unsupported-item-type-as-group",`The item type '${s}' is not supported as a 'IGroupLayer'`)}let[c,y]=await Promise.all([t(),L(a)]),p=()=>c;if("Feature Service"===s){if(y=n.url?await b(y,n.url):{},S(y).length){const e=l.SubtypeGroupLayer,a=await e();p=e=>"SubtypeGroupLayer"===e.layerType?a:c}return f(e,p,y,await async function(e){const{layersJSON:a}=await(0,i.V)(e);if(!a)return null;const t=[...a.layers,...a.tables];return e=>t.find((a=>a.id===e.id))}(n.url))}return g(y)>0?f(e,p,y):async function(e,a){const{portalItem:t}=e;if(!t?.url)return;const r=await(0,d.T)(t.url);r&&f(e,a,{layers:r.layers?.map(m),tables:r.tables?.map(m)})}(e,p)}(t,e);s&&t.read({url:s},o);const p=await L(e,a);return p&&t.read(p,o),t.resourceReferences={portalItem:n,paths:o.readResourcePaths??[]},"subtype-group"!==t.type&&t.read({title:l},o),(0,y.y)(t,o)}(e,a)}function m(e){return{id:e.id,name:e.name}}function f(e,a,t,i){let l=t.layers||[];const c=t.tables||[];if("Feature Collection"===e.portalItem?.type&&(l.forEach((e=>{"Table"===e?.layerDefinition?.type&&c.push(e)})),l=l.filter((e=>"Table"!==e?.layerDefinition?.type))),"coverage"in t){const a=function(e){const{coverage:a}=e;if(!a)return null;const t=new URL(a);if(a.toLowerCase().includes("item.html")){const e=t.searchParams.get("id"),a=t.origin;return n.Z.fromPortalItem({portalItem:new o.default({id:e,url:a})})}if((0,s.B5)(a))return n.Z.fromArcGISServerUrl({url:a});throw new r.Z("portal:oriented-imagery-layer-coverage","the provided coverage url couldn't be loaded as a layer")}(t);a&&e.add(a)}l.reverse().forEach((r=>{const n=w(e,a(r),t,r,i?.(r));e.add(n)})),c.reverse().forEach((r=>{const n=w(e,a(r),t,r,i?.(r));e.tables.add(n)}))}function w(e,a,t,r,n){const s=e.portalItem,i=new a({portalItem:s.clone(),layerId:r.id});if("sourceJSON"in i&&(i.sourceJSON=n),"subtype-group"!==i.type&&(i.sublayerTitleMode="service-name"),"Feature Collection"===s.type){const e={origin:"portal-item",portal:s.portal||l.Z.getDefault()};i.read(r,e);const a=t.showLegend;null!=a&&i.read({showLegend:a},e)}return i}async function L(e,a){if(!1===e.supportsData)return;const t=e.instance,r=t.portalItem;if(!r)return;let n=null;try{n=await r.fetchData("json",a)}catch(e){}if(function(e){return"stream"!==e.type&&"oriented-imagery"!==e.type&&"layerId"in e}(t)){let e=null,a=!0;if(n&&g(n)>0){if(null==t.layerId){const e=S(n);t.layerId="subtype-group"===t.type?e?.[0]:h(n)}e=function(e,a){const{layerId:t}=a,r=e.layers?.find((e=>e.id===t))||e.tables?.find((e=>e.id===t));return r&&function(e,a){return!("feature"===a.type&&"layerType"in e&&"SubtypeGroupLayer"===e.layerType||"subtype-group"===a.type&&!("layerType"in e))}(r,a)?r:null}(n,t),e&&(1===g(n)&&(a=!1),null!=n.showLegend&&(e.showLegend=n.showLegend))}return a&&"service-name"!==t.sublayerTitleMode&&(t.sublayerTitleMode="item-title-and-service-name"),e}return n}async function b(e,a){if(null==e?.layers||null==e?.tables){const t=await(0,d.T)(a);(e=e||{}).layers=e.layers||t?.layers,e.tables=e.tables||t?.tables}return e}function h(e){const a=e.layers;if(a&&a.length)return a[0].id;const t=e.tables;return t&&t.length?t[0].id:null}function g(e){return(e?.layers?.length??0)+(e?.tables?.length??0)}function S(e){const a=[];return e?.layers?.forEach((e=>{"SubtypeGroupLayer"===e.layerType&&a.push(e.id)})),a}},28008:(e,a,t)=>{t.r(a),t.d(a,{fromItem:()=>u,selectLayerClassPath:()=>y});var r=t(20102),n=t(84230),s=t(87344),i=t(15235),l=t(18062),o=t(14661),c=t(96187);async function u(e){!e.portalItem||e.portalItem instanceof i.default||(e={...e,portalItem:new i.default(e.portalItem)});const a=await async function(e){return await e.load(),async function(e){const a=s.T[e.className];return{constructor:await a(),properties:e.properties}}(await y(e))}(e.portalItem);return new(0,a.constructor)({portalItem:e.portalItem,...a.properties})}async function y(e){switch(e.type){case"Map Service":return async function(e){return await async function(e){return(await(0,c.T)(e.url)).tileInfo}(e)?{className:"TileLayer"}:{className:"MapImageLayer"}}(e);case"Feature Service":return async function(e){if((0,o._$)(e,"Oriented Imagery Layer"))return async function(e){await e.load();const a=await e.fetchData();return a.coverage?{className:"GroupLayer"}:{className:"OrientedImageryLayer",properties:a}}(e);const a=await d(e);if("object"==typeof a){const e={};return null!=a.id&&(e.layerId=a.id),{className:a.className||"FeatureLayer",properties:e}}return{className:"GroupLayer"}}(e);case"Feature Collection":return async function(e){await e.load();const a=(0,o._$)(e,"Map Notes"),t=(0,o._$)(e,"Markup");if(a||t)return{className:"MapNotesLayer"};if((0,o._$)(e,"Route Layer"))return{className:"RouteLayer"};const r=await e.fetchData();return 1===(0,l.getNumLayersAndTables)(r)?{className:"FeatureLayer"}:{className:"GroupLayer"}}(e);case"Scene Service":return async function(e){const a=await d(e);if("object"==typeof a){const t={};let r;if(null!=a.id?(t.layerId=a.id,r=`${e.url}/layers/${a.id}`):r=e.url,e.typeKeywords?.length)for(const a of Object.keys(n.fb))if(e.typeKeywords.includes(a))return{className:n.fb[a]};const s=await(0,c.T)(r);return{className:n.fb[s?.layerType]||"SceneLayer",properties:t}}return!1===a&&"Voxel"===(await(0,c.T)(e.url))?.layerType?{className:"VoxelLayer"}:{className:"GroupLayer"}}(e);case"Image Service":return async function(e){await e.load();const a=e.typeKeywords?.map((e=>e.toLowerCase()))??[];if(a.includes("elevation 3d layer"))return{className:"ElevationLayer"};if(a.includes("tiled imagery"))return{className:"ImageryTileLayer"};const t=(await e.fetchData())?.layerType;if("ArcGISTiledImageServiceLayer"===t)return{className:"ImageryTileLayer"};if("ArcGISImageServiceLayer"===t)return{className:"ImageryLayer"};const r=await(0,c.T)(e.url),n=r.cacheType?.toLowerCase(),s=r.capabilities?.toLowerCase().includes("tilesonly");return"map"===n||s?{className:"ImageryTileLayer"}:{className:"ImageryLayer"}}(e);case"Stream Service":case"Feed":return{className:"StreamLayer"};case"Vector Tile Service":return{className:"VectorTileLayer"};case"GeoJson":return{className:"GeoJSONLayer"};case"CSV":return{className:"CSVLayer"};case"KML":return{className:"KMLLayer"};case"WFS":return{className:"WFSLayer"};case"WMTS":return{className:"WMTSLayer"};case"WMS":return{className:"WMSLayer"};default:throw new r.Z("portal:unknown-item-type","Unknown item type '${type}'",{type:e.type})}}async function d(e){const a=e.url;if(!a||a.match(/\/\d+$/))return{};await e.load();const t=await e.fetchData();if("Feature Service"===e.type){const e=p(await(0,l.preprocessFSItemData)(t,a));if("object"==typeof e){const a=(0,l.getSubtypeGroupLayerIds)(t);e.className=null!=e.id&&a.includes(e.id)?"SubtypeGroupLayer":"FeatureLayer"}return e}return(0,l.getNumLayersAndTables)(t)>0?p(t):p(await(0,c.T)(a))}function p(e){return 1===(0,l.getNumLayersAndTables)(e)&&{id:(0,l.getFirstLayerOrTableId)(e)}}},40555:(e,a,t)=>{t.d(a,{y:()=>i});var r=t(66643),n=t(95330),s=t(20941);async function i(e,a,t){const i=e&&e.getAtOrigin&&e.getAtOrigin("renderer",a.origin);if(i&&"unique-value"===i.type&&i.styleOrigin){const l=await(0,r.q6)(i.populateFromStyle());if((0,n.k_)(t),!1===l.ok){const t=l.error;a&&a.messages&&a.messages.push(new s.Z("renderer:style-reference",`Failed to create unique value renderer from style reference: ${t.message}`,{error:t,context:a})),e.clear("renderer",a?.origin)}}}},96187:(e,a,t)=>{t.d(a,{T:()=>n});var r=t(3172);async function n(e,a){const{data:t}=await(0,r.default)(e,{responseType:"json",query:{f:"json",...a?.customParameters,token:a?.apiKey}});return t}}}]);