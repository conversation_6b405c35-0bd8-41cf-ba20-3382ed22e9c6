package org.thingsboard.server.dao.purchase;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.purchase.ContractDetail;
import org.thingsboard.server.dao.sql.deviceType.ContractDetailMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.purchase.ContractDetailPageRequest;
import org.thingsboard.server.dao.util.imodel.query.purchase.ContractDetailSaveRequest;

import java.util.List;

@Service
public class ContractDetailServiceImpl implements ContractDetailService {
    @Autowired
    private ContractDetailMapper mapper;

    @Override
    public IPage<ContractDetail> findAllConditional(ContractDetailPageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    public List<ContractDetail> saveAll(List<ContractDetailSaveRequest> details) {
        return QueryUtil.saveOrUpdateBatchByRequest(details, mapper::saveAll, mapper::saveAll);
    }

    @Override
    public boolean update(ContractDetail entity) {
        return mapper.update(entity);
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }

    @Override
    public boolean deleteAll(List<String> idList) {
        return QueryUtil.deleteBatch(idList, mapper::deleteBatchIds);
    }

    @Override
    public List<ContractDetail> getAllByMainId(String mainId) {
        return null;
    }

    @Override
    public boolean removeAllByMainOnIdNotIn(String mainId, List<String> idList) {
        return mapper.removeAllByMainOnIdNotIn(mainId, idList) > 0;
    }

}
