export const useTagsStore = defineStore('tags', () => {
  const initialState: Store_Tags_state = {
    cachedRouters: [],
    cachedRouterNames: [],
    showTags: localStorage.getItem('showTags') !== 'false',
    reloading: false
  };
  const state = reactive<Store_Tags_state>(initialState);
  const actions = {
    TOGGLE_showTags: (payload?: boolean) => {
      state.showTags = payload ?? true;
    },
    REFRESH_cachedRouterNames: () => {
      state.cachedRouterNames = state.cachedRouters.map((item) => item.name);
    },
    ADD_cachedRouters: (payload: any) => {
      if (!payload) return;
      const index = state.cachedRouters.findIndex(
        (item) => item.name === payload.name
      );
      if (index !== -1) {
        // 如果有旧的则把旧的删除
        state.cachedRouters.splice(index, 1, payload);
      } else {
        state.cachedRouters.push(payload);
        state.cachedRouterNames.push(payload.name);
      }
    },
    REMOVEOTHER_cachedRouters: (payload: any) => {
      if (!payload) return;
      const index = state.cachedRouters.findIndex(
        (item) => item.name === payload.name
      );
      if (index === -1) return;
      state.cachedRouters = state.cachedRouters.filter(
        (item) => item.name === payload.name
      );
      state.cachedRouterNames = state.cachedRouters.map((item) => item.name);
    },
    RELOAD_cachedRouterPage: (payload: boolean) => {
      state.reloading = payload ?? false;
    },
    REMOVE_cachedRouter: (payload: any) => {
      if (!payload) return;
      const index = state.cachedRouters.findIndex(
        (item) => item.name === payload.name
      );
      if (index === -1) return;
      state.cachedRouters.splice(index, 1);
      state.cachedRouterNames = state.cachedRouterNames.filter(
        (item) => item !== payload.name
      );
    },
    REMOVEALL_cachedRouters: () => {
      state.cachedRouters.length = 0;
      state.cachedRouterNames.length = 0;
    },
    REMOVELEFT_cachedRouters: (payload: any) => {
      const index = state.cachedRouters.findIndex(
        (item) => item.name === payload.name
      );
      if (index === -1) return;
      state.cachedRouters.splice(0, index);
      actions.REFRESH_cachedRouterNames();
    },
    REMOVERIGHT_cachedRouters: (payload: any) => {
      const index = state.cachedRouters.findIndex(
        (item) => item.name === payload.name
      );
      if (index === -1) return;
      state.cachedRouters.splice(index + 1);
      actions.REFRESH_cachedRouterNames();
    }
  };
  return { ...toRefs(state), ...actions };
});
