package org.thingsboard.server.dao.smartService.system;

import org.thingsboard.server.dao.model.sql.smartService.system.SystemUrgency;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.List;

/**
 * 知识库公告
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-10-27
 */
public interface SystemUrgencyService {

    SystemUrgency save(SystemUrgency systemUrgency);

    IstarResponse delete(List<String> ids);

    List getList(String isDel, String tenantId);

    IstarResponse deleteHard(List<String> ids);
}
