import{d as Q,M as $,c as o,a8 as c,s as G,x as n,r as f,bQ as J,S as K,ar as X,a9 as W,bT as Z,D as ee,o as ae,g as te,n as le,q as g,i as b,b6 as ie,b7 as se}from"./index-r0dFAfgr.js";import{_ as re}from"./CardTable-rdWOL4_6.js";import{_ as ne}from"./CardSearch-CB_HNR-Q.js";import{I as D}from"./common-CvK_P_ao.js";import{f as y}from"./DateFormatter-Bm9a68Ax.js";import{s as ue}from"./equipmentManage-DuoY00aj.js";import{f as de,a as pe,h as me,i as w,j as oe,k as ce,e as fe,l as ge}from"./equipmentService-DanrK8F-.js";import{g as be}from"./equipmentInspection-Ci5RL-WV.js";import{a as ye}from"./ledgerManagement-CkhtRd8m.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";const Te={class:"wrapper"},Ve=Q({__name:"index",setup(ve){const{$btnPerms:u}=$(),s=o(),v=o(),T=o(),M=o(),k=o(),C=o(),S=o([]),_=o(),H=o({filters:[{label:"任务编号",field:"code",type:"input"},{label:"保养计划",field:"planName",type:"select",options:c(()=>l.planList)},{label:"保养班组",field:"teamName",type:"input"},{label:"保养人员",field:"userName",type:"input"},{type:"select",label:"审核状态",field:"auditStatus",options:[{label:"全部",value:""},{label:"未审核",value:"0"},{label:"拒绝",value:"1"},{label:"通过",value:"2"}],onChange:()=>p()},{type:"daterange",format:"x",label:"开始时间",field:"startEndTime"},{type:"daterange",format:"x",label:"结束时间",field:"endEndTime"}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",icon:D.QUERY,click:()=>p()},{type:"default",perm:!0,text:"重置",svgIcon:G(se),click:()=>{var e;(e=s.value)==null||e.resetForm(),p()}},{perm:u("RoleManageAdd"),text:"新增",type:"success",icon:D.ADD,click:()=>U()},{perm:u("RoleManageAdd"),text:"批量删除",type:"danger",icon:D.DELETE,click:()=>{var e;if(((e=r.selectList)==null?void 0:e.length)===0){n.warning("请选中至少一条数据");return}E()}}]}]}),r=f({defaultExpandAll:!0,indexVisible:!0,selectList:[],handleSelectChange:e=>{r.selectList=e},columns:[{label:"任务编号",prop:"code"},{label:"任务名称",prop:"name"},{label:"任务类型",prop:"type"},{label:"执行班组",prop:"teamName"},{label:"执行人员",prop:"userName"},{label:"预计开始",prop:"startTime",formatter:e=>y(e.startTime,"YYYY-MM-DD HH:mm")},{label:"预计结束",prop:"endTime",formatter:e=>y(e.endTime,"YYYY-MM-DD HH:mm")},{label:"实际开始",prop:"realStartTime",formatter:e=>y(e.realStartTime,"YYYY-MM-DD HH:mm")},{label:"实际结束",prop:"realEndTime",formatter:e=>y(e.realEndTime,"YYYY-MM-DD HH:mm")},{label:"说明/备注",prop:"remark"},{label:"添加人",prop:"creatorName"},{label:"添加时间",prop:"createTime",formatter:e=>y(e.createTime,"YYYY-MM-DD HH:mm")},{label:"任务状态",prop:"status",formatter:e=>j[e.status*1]},{label:"审核状态",prop:"auditStatus",formatter:e=>O[((e==null?void 0:e.auditStatus)||0)*1],tag:!0,tagColor:e=>e.auditStatus==="0"?"#13ce66":"#6885bf"}],operationWidth:"200px",operations:[{hide:e=>e.status==="2"||e.status==="3",type:"primary",isTextBtn:!0,text:"保养",perm:u("RoleManageEdit"),click:e=>{var t;h.title="保养",l.selected=e;const a=l.teamList.find(i=>i.value===e.teamId)||{};l.userList=a.maintainCircuitTeamCList.map(i=>({label:i.userName,value:i.userId})),l.getMaintenanceTaskDetailsValue(e.id),h.defaultValue={...e||{}},(t=M.value)==null||t.openDrawer()}},{hide:e=>!((e.status==="2"||e.status==="3")&&e.auditStatus==="0"),type:"primary",isTextBtn:!0,text:"审核",perm:u("RoleManageEdit"),click:e=>{var a;x.defaultValue={id:e.id},(a=C.value)==null||a.openDrawer()}},{hide:e=>e.status!=="2"&&e.status!=="3",type:"primary",isTextBtn:!0,text:"详情",perm:u("RoleManageEdit"),click:e=>{var t;h.title="详情";const a=l.teamList.find(i=>i.value===e.teamId)||{};l.userList=a.maintainCircuitTeamCList.map(i=>({label:i.userName,value:i.userId})),l.getMaintenanceTaskDetailsValue(e.id),h.defaultValue={...e||{}},(t=M.value)==null||t.openDrawer()}},{hide:e=>e.status!=="0",type:"primary",isTextBtn:!0,text:"编辑",perm:u("RoleManageEdit"),click:e=>z(e)},{hide:e=>e.status!=="0",isTextBtn:!0,type:"danger",text:"删除",perm:u("RoleManageDelete"),click:e=>E(e)}],dataList:[],pagination:{page:1,limit:20,total:0,refreshData:({page:e,size:a})=>{r.pagination.page=e,r.pagination.limit=a,p()}}}),d=f({title:"新增",labelWidth:"130px",submitting:!1,submit:e=>{d.submitting=!0;let a="添加成功";e.id&&(a="修改成功"),e.id?de(e).then(()=>{var t;d.submitting=!1,p(),n.success(a),(t=v.value)==null||t.closeDrawer()}).catch(t=>{d.submitting=!1,n.warning(t)}):(e.maintainPlanCList=e.maintainTaskCList,delete e.maintainTaskCList,e.maintainPlanCList.map(t=>t.mmDeviceId=t.serialId),pe(e).then(()=>{var t;d.submitting=!1,p(),n.success(a),(t=v.value)==null||t.closeDrawer()}).catch(t=>{d.submitting=!1,n.warning(t)}))},defaultValue:{},group:[{fields:[{xl:8,type:"input",label:"任务名称",field:"name",rules:[{required:!0,message:"请输入任务名称"}]},{xl:8,type:"select",label:"执行班组",field:"teamId",rules:[{required:!0,message:"请选择执行班组"}],options:c(()=>l.teamList),onChange:e=>{const a=l.teamList.find(t=>t.value===e)||{};l.userList=a.maintainCircuitTeamCList.map(t=>({label:t.userName,value:t.userId}))}},{xl:8,type:"select",label:"执行人员",field:"userId",rules:[{required:!0,message:"请选择执行人员"}],options:c(()=>l.userList)},{xl:8,type:"date",label:"预计开始",field:"startTime",rules:[{required:!0,message:"请输入预计开始"}],format:"x"},{xl:8,type:"date",label:"预计结束",field:"endTime",rules:[{required:!0,message:"请输入预计结束"}],format:"x"},{xl:8,type:"department-user",label:"审核人员",field:"auditor",rules:[{required:!0,message:"请选择审核人员"}]},{type:"table",field:"maintainTaskCList",config:{indexVisible:!0,height:"350px",titleRight:[{style:{justifyContent:"flex-end"},items:[{type:"btn-group",btns:[{text:"添加设备",perm:!0,click:()=>{var e;(e=T.value)==null||e.openDrawer()}}]}]}],dataList:c(()=>l.selectList),columns:[{label:"标签编码",prop:"deviceLabelCode"},{label:"设备名称",prop:"name"},{label:"规格/型号",prop:"model"},{label:"所属大类",prop:"topType"},{label:"所属类别",prop:"type"},{label:"安装区域",prop:"installAddressName"},{label:"安装位置",prop:"detailInstallAddressName"},{label:"最后保养时间",prop:"lastMaintainanceTime",formatter:e=>y(e.time,"YYYY-MM-DD")}],operations:[{type:"danger",text:"移除",icon:D.DELETE,perm:u("RoleManageDelete"),click:e=>l.selectList=l.selectList.filter(a=>a.id!==e.id)}],pagination:{hide:!0}}}]}]}),h=f({title:"保养",labelWidth:"130px",defaultValue:{},group:[{fields:[{disabled:!0,xl:8,type:"input",label:"任务名称",field:"name",rules:[{required:!0,message:"请输入任务名称"}]},{readonly:!0,xl:8,type:"select",label:"执行班组",field:"teamId",rules:[{required:!0,message:"请选择执行班组"}],options:c(()=>l.teamList),onChange:e=>{const a=l.teamList.find(t=>t.value===e)||{};l.userList=a.maintainCircuitTeamCList.map(t=>({label:t.userName,value:t.userId}))}},{readonly:!0,xl:8,type:"select",label:"执行人员",field:"userId",rules:[{required:!0,message:"请选择执行人员"}],options:c(()=>l.userList)},{readonly:!0,xl:8,type:"date",label:"预计开始",field:"startTime",rules:[{required:!0,message:"请输入预计开始"}],format:"x"},{readonly:!0,xl:8,type:"date",label:"预计结束",field:"endTime",rules:[{required:!0,message:"请输入预计结束"}],format:"x"},{disabled:!0,xl:8,type:"input",label:"审核部门",field:"auditorDepartmentName"},{disabled:!0,xl:8,type:"input",label:"审核人员",field:"auditorName",rules:[{required:!0,message:"请选择审核人员"}]},{type:"table",field:"maintainTaskCList",config:{indexVisible:!0,height:"350px",dataList:c(()=>l.selectList),columns:[{label:"标签编码",prop:"deviceLabelCode"},{label:"设备名称",prop:"name"},{label:"规格/型号",prop:"model"},{label:"所属大类",prop:"topType"},{label:"所属类别",prop:"type"},{label:"安装区域",prop:"installAddressName"},{label:"安装位置",prop:"detailInstallAddressName"},{label:"最后保养时间",prop:"lastMaintainanceTime",formatter:e=>y(e.time,"YYYY-MM-DD")},{label:"保养状态",prop:"status",formatter:e=>e.status==="0"?"未保养":"已保养"}],operations:[{hide:e=>e.status==="1",text:"保养",perm:u("RoleManageDelete"),click:e=>{var a;L.defaultValue={id:e.id,mainId:e.mainId},(a=k.value)==null||a.openDrawer()}},{hide:e=>e.status==="0",text:"详情",perm:u("RoleManageDelete"),click:e=>{var a;Y.defaultValue={...e},(a=_.value)==null||a.openDrawer()}}],pagination:{hide:!0}}}]}]}),L=f({title:"保养",labelWidth:"130px",width:500,submitting:!1,submit:e=>{L.submitting=!0,me(e).then(a=>{var t;a.data.code===200?(p(),n.success("保养成功"),L.submitting=!1,(t=k.value)==null||t.closeDrawer(),w(l.selected.id).then(i=>{l.selectList=i.data.data.maintainTaskCList||[]})):n.warning(a.data.message)}).catch(a=>{L.submitting=!1,n.warning(a)})},defaultValue:{},group:[{fields:[{type:"image",label:"图片",field:"img"},{type:"input",label:"说明",field:"remark",rules:[{required:!0,message:"请输入说明"}]},{type:"select",label:"完成状态",field:"status",options:[{label:"未完成",value:0},{label:"已完成",value:1}],rules:[{required:!0,message:"请输入完成状态"}]}]}]}),Y=f({title:"详情",labelWidth:"130px",width:500,submitting:!1,defaultValue:{},group:[{fields:[{readonly:!0,type:"image",label:"图片",field:"img"},{disabled:!0,type:"input",label:"说明",field:"remark"},{readonly:!0,type:"select",label:"完成状态",field:"status",options:[{label:"未完成",value:"0"},{label:"已完成",value:"1"}]}]}]}),x=f({title:"审核",labelWidth:"130px",width:500,submitting:!1,submit:e=>{x.submitting=!0,oe(e).then(a=>{var t;x.submitting=!1,a.data.code===500?n.warning(a.data.message):(p(),n.success("审核成功"),(t=C.value)==null||t.closeDrawer())}).catch(a=>{x.submitting=!1,n.warning(a)})},defaultValue:{},group:[{fields:[{type:"select",label:"审核状态",field:"auditStatus",options:[{label:"拒绝",value:1},{label:"通过",value:2}],rules:[{required:!0,message:"请输入审核状态"}]},{type:"textarea",label:"审核意见",field:"auditRemark",rules:[{required:!0,message:"请输入审核意见"}]}]}]}),B=f({title:"设备选择",labelWidth:"130px",submit:(e,a)=>{var t;a?(delete e.drive,l.getDevice(e)):(l.selectList=[...l.selectList,...S.value],l.selectList=J(l.selectList,["id"]),(t=T.value)==null||t.closeDrawer())},defaultValue:{},group:[{fields:[{xl:8,type:"input",label:"标签编码",field:"deviceLabelCode"},{xl:8,type:"input",label:"设备名称",field:"name"},{xl:8,type:"input",label:"设备型号",field:"model"},{xl:8,label:"安装区域",field:"areaId",type:"select-tree",checkStrictly:!0,options:c(()=>l.installationArea)},{xl:12,type:"daterange",label:"上次保养时间",field:"lastMaintainanceTime"},{type:"table",field:"drive",config:{indexVisible:!0,height:"350px",selectList:[],handleSelectChange:e=>{S.value=e},dataList:c(()=>l.deviceValue),titleRight:[{style:{justifyContent:"flex-end"},items:[{type:"btn-group",btns:[{text:"查询",type:"success",perm:!0,click:()=>{var e;(e=T.value)==null||e.Submit(!0)}},{text:"重置",type:"primary",perm:!0,click:()=>{var e,a;(e=T.value)==null||e.resetForm(),(a=T.value)==null||a.Submit(!0)}}]}]}],columns:[{label:"标签编码",prop:"deviceLabelCode"},{label:"设备名称",prop:"name"},{label:"规格型号",prop:"model"},{label:"所属大类",prop:"topType"},{label:"所属类别",prop:"type"},{label:"安装区域",prop:"installAddressName"},{label:"安装位置",prop:"detailInstallAddressName"},{label:"最后保养时间",prop:"lastMaintainanceTime",formatter:e=>y(e.time,"YYYY-MM-DD")}],pagination:{hide:!0}}}]}]}),U=()=>{var e;d.title="新增",l.selectList=[],S.value=[],d.defaultValue={},(e=v.value)==null||e.openDrawer()},z=e=>{var t;d.title="编辑";const a=l.teamList.find(i=>i.value===e.teamId)||{};l.userList=a.maintainCircuitTeamCList.map(i=>({label:i.userName,value:i.userId})),l.getUserListValue(e.auditorDepartment),l.selectList=e.maintainTaskCList,l.getMaintenanceTaskDetailsValue(e.id),d.defaultValue={...e||{}},(t=v.value)==null||t.openDrawer()},E=e=>{K("确定删除该保养任务, 是否继续?","删除提示").then(()=>{var t;let a=[];e?a=[e.id]:a=((t=r==null?void 0:r.selectList)==null?void 0:t.map(i=>i.id))??[],ce(a).then(()=>{p(),n.success("删除成功")})})},l=f({planList:[],teamList:[],userList:[],deviceValue:[],selectList:[],WaterSupplyTree:[],installationArea:[],departmentUser:[],taskDetails:[],selected:{},getDevice:e=>{const a={size:99999,page:1,...e};a.lastMaintainanceTime&&a.lastMaintainanceTime.length>1&&(a.lastMaintainanceTimeFrom=a.lastMaintainanceTime[0],a.lastMaintainanceTimeTo=a.lastMaintainanceTime[1]),delete a.lastMaintainanceTime,ye(a).then(t=>{l.deviceValue=t.data.data.data||[]})},getTeam:()=>{be({size:99999,page:1,type:"保养班组",name:""}).then(a=>{const t=a.data.data.data||[];l.teamList=t.map(i=>({label:i.name,value:i.id,maintainCircuitTeamCList:i.maintainCircuitTeamCList}))})},getPlan:()=>{fe({size:99999,page:1,startStartTime:""}).then(a=>{const t=a.data.data.data||[];l.planList=t.map(i=>({label:i.name,value:i.name}))})},getWaterSupplyTreeValue:()=>{X(2).then(a=>{l.WaterSupplyTree=W(a.data.data||[])})},getUserListValue:e=>{Z({pid:e}).then(a=>{const t=a.data.data.data||[];l.departmentUser=t.map(i=>({label:i.firstName,value:ee(i.id.id)}))})},getAreaTreeValue:()=>{ue({page:1,size:99999,shortName:""}).then(a=>{l.installationArea=W(a.data.data.data||[])})},getMaintenanceTaskDetailsValue:e=>{w(e).then(a=>{l.selectList=a.data.data.maintainTaskCList||[]})}}),j=["待接收","已接收","按时完成","超时完成","未完成"],O=["未审核","拒绝","通过"],p=async()=>{var a,t,i,m,V,q,I,N,P,A,R;const e={size:r.pagination.limit,page:r.pagination.page,startStartTime:"",startEndTime:"",endStartTime:"",endEndTime:"",...((a=s.value)==null?void 0:a.queryParams)||{}};(t=s.value)!=null&&t.queryParams&&((i=s.value)!=null&&i.queryParams.startEndTime)&&((m=s.value)==null?void 0:m.queryParams.startEndTime.length)>1&&(e.startStartTime=(V=s.value)==null?void 0:V.queryParams.startEndTime[0],e.startEndTime=(q=s.value)==null?void 0:q.queryParams.startEndTime[1]),(I=s.value)!=null&&I.queryParams&&((N=s.value)!=null&&N.queryParams.endEndTime)&&((P=s.value)==null?void 0:P.queryParams.endEndTime.length)>1&&(e.endStartTime=(A=s.value)==null?void 0:A.queryParams.endEndTime[0],e.endEndTime=(R=s.value)==null?void 0:R.queryParams.endEndTime[1]),ge(e).then(F=>{r.dataList=F.data.data.data||[],r.pagination.total=F.data.data.total||0})};return ae(()=>{p(),l.getTeam(),l.getPlan(),l.getDevice(),l.getWaterSupplyTreeValue(),l.getAreaTreeValue()}),(e,a)=>{const t=ne,i=re,m=ie;return te(),le("div",Te,[g(t,{ref_key:"refSearch",ref:s,config:b(H)},null,8,["config"]),g(i,{config:b(r),class:"card-table"},null,8,["config"]),g(m,{ref_key:"refForm",ref:v,config:b(d)},null,8,["config"]),g(m,{ref_key:"refForm1",ref:T,config:b(B)},null,8,["config"]),g(m,{ref_key:"refmaintainance",ref:M,config:b(h)},null,8,["config"]),g(m,{ref_key:"refdevicesCare",ref:k,config:b(L)},null,8,["config"]),g(m,{ref_key:"refpicture",ref:_,config:b(Y)},null,8,["config"]),g(m,{ref_key:"refreview",ref:C,config:b(x)},null,8,["config"])])}}});export{Ve as default};
