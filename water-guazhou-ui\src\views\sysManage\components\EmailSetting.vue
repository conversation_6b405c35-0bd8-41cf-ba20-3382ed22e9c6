<template>
  <Form
    ref="refForm"
    :config="FormConfig"
  ></Form>
  <Form
    ref="refTestForm"
    :config="TestFormConfig"
  ></Form>
</template>
<script lang="ts" setup>
import { getAdminSettings, saveAdminSettings, sendTestMail } from '@/api/admin'
import { IFormIns } from '@/components/type'
import { SLMessage } from '@/utils/Message'

const refTestForm = ref<IFormIns>()
const refForm = ref<IFormIns>()
let sendId = ''
const sendKey = 'mail'
const FormConfig = reactive<IFormConfig>({
  group: [
    {
      fields: [
        {
          type: 'input',
          field: 'mailFrom',
          label: '邮件来自',
          placeholder: '如：smtp.163.com',
          rules: [
            { required: true, message: '请输入用户邮箱', trigger: 'blur' },
            { type: 'email', message: '请输入正确邮箱地址', trigger: 'blur' }
          ]
        },
        {
          type: 'select',
          label: 'SMTP协议',
          field: 'smtpProtocol',
          options: [
            { label: 'smtp', value: 'smtp' },
            { label: 'smtps', value: 'smtps' }
          ],
          rules: [
            { required: true, message: '请选择SMTP协议', trigger: 'blur' }
          ]
        },
        {
          type: 'input',
          field: 'smtpHost',
          label: 'SMTP网关',
          rules: [
            { required: true, message: '请输入SMTP网关', trigger: 'blur' }
            // { validator: validateSMTP, trigger: 'blur' } // url
          ]
        },
        {
          type: 'input-number',
          label: 'SMTP端口',
          placeholder: '一般为25或465',
          field: 'smtpPort',
          rules: [
            { required: true, message: '请输入SMTP端口', trigger: 'blur' }
          ]
        },
        {
          type: 'input-number',
          label: '超时毫秒数',
          field: 'timeout',
          rules: [
            { required: true, message: '请输入超时毫秒数', trigger: 'blur' }
          ]
        },
        {
          type: 'switch',
          label: '启用TLS',
          field: 'enableTls'
        },
        {
          type: 'input',
          label: '用户账号',
          field: 'username',
          rules: [
            { required: true, message: '请输入用户账号', trigger: 'blur' },
            { type: 'email', message: '账号为邮箱格式', trigger: 'blur' }
          ]
        },
        {
          type: 'password',
          label: '密码',
          field: 'password',
          rules: [
            { required: true, message: '请输入密码', trigger: 'blur' },
            { max: 20, min: 8, message: '密码在8-20位', trigger: 'blur' }
          ]
        }
      ]
    }
  ],
  labelPosition: 'right',
  labelWidth: '100px',
  defaultValue: {
    mailFrom: '',
    smtpProtocol: '',
    smtpHost: '',
    smtpPort: '',
    timeout: '',
    enableTls: true,
    username: '',
    password: ''
  },
  submit: (params: any) => {
    handleSaver(params)
  }
})
const TestFormConfig = reactive<IFormConfig>({
  group: [
    {
      fields: [
        {
          type: 'input',
          label: '测试邮件发送至',
          field: 'mailTo',
          rules: [
            {
              required: true,
              message: '请输入接收测试邮件的邮箱'
            },
            { type: 'email', message: '请输入正确邮箱地址' }
          ]
        },
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              type: 'warning',
              isTextBtn: false,
              text: '发送测试邮件',
              iconifyIcon: 'ep:promotion',
              click: () => {
                refTestForm.value?.Submit()
              }
            },
            {
              perm: true,
              isTextBtn: false,
              text: '保存',
              click: () => {
                refForm.value?.Submit()
              }
            }
          ]
        }
      ]
    }
  ],
  labelPosition: 'right',
  labelWidth: '100px',
  defaultValue: {},
  submit: (params: any) => {
    handlesendTestMail(params)
  }
})

// 发送测试邮件
const handlesendTestMail = (params: any) => {
  const emailBase = refForm.value?.dataForm || {}
  const submitParams = {
    id: {
      id: sendId
    },
    key: sendKey,
    jsonValue: {
      ...params,
      ...emailBase
    }
  }

  sendTestMail(submitParams)
    .then(res => {
      console.log(res)
      SLMessage.success('发送成功')
    })
    .catch(err => {
      console.log(err)
      SLMessage.error(
        '发送失败，请检查输入的账号和密码或接收测试邮件的邮箱是否存在！'
      )
    })
}
const handleSaver = (params: any) => {
  const submitParams = {
    id: {
      id: sendId
    },
    key: sendKey,
    jsonValue: {
      ...params
    }
  }
  saveAdminSettings(submitParams)
    .then(res => {
      console.log(res)
      SLMessage.success('保存成功')
    })
    .catch(err => {
      SLMessage.error('保存失败，用户账号或密码不匹配')
      console.log(err)
    })
}
// 获取邮件配置等
const getEmailInfo = () => {
  getAdminSettings(sendKey).then(res => {
    sendId = res.data.id.id
    let jsonValue = res.data.jsonValue
    jsonValue = JSON.parse(JSON.stringify(jsonValue), (k, v) => (v === 'true' ? true : v === 'false' ? false : v))
    FormConfig.defaultValue = {
      ...jsonValue
    }
    refForm.value?.resetForm()
  })
}
onMounted(() => {
  getEmailInfo()
})
</script>
<style lang="scss" scoped></style>
