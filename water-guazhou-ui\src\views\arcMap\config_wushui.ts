import {
  // WaterSource,
  // WaterSourceDetail,
  // WaterPlant,
  // WaterPlantDetail,
  WaterPlantWS,
  WaterPlantWSDetail,
  // FlowMonitoring,
  // FlowMonitoringDetail,
  // PressureMonitoring,
  // PressureMonitoringDetail,
  WaterQualityMonitoring,
  WaterQualityMonitoringDetail,
  // BigUser,
  // BigUserDetail,
  // Secondary,
  // SecondaryDetail,
  // PipeDetail,
  // PipeLine,
  // Valve,
  // FireHydrant,
  // Sensor,
  // WaterMeter,
  Inspection,
  InspectionDetail,
  // Repair,
  // RepairDetail,
  // MeterReading,
  // MeterReadingDetail,
  // VehicleMonitoring,
  // VehicleMonitoringDetail,
  WorkOrder,
  WorkOrderDetail,
  // Complaint,
  // ComplaintDetail,
  // Install,
  // InstallDetail,
  InspectionMaintenance,
  InspectionMaintenanceDetail,
  // WaterVolume,
  // WaterVolumeDetail,
  // Dma,
  // DmaDetail,
  // EventHeatMap,
  // EventHeatMapDetail,
  // DispatchCommand,
  // VideoSurveillance,
  // VideoSurveillanceDetail,
  // WaterPool,
  // WaterPoolDetail,
  // SmartMeter,
  // SmartMeterDetail,
  AllStations,
  AllStationsDetail
} from './OneMap/index'
/**
 * Symbol的线样式
 */
// const url = window.location.href.split('/')[2].split(':')[0]
export const SymbolLineStyle = [
  { label: '直线', value: 'solid' },
  {
    label: '虚线',
    value: 'dash'
  },
  {
    label: '虚线-点',
    value: 'dash-dot'
  },
  {
    label: '点',
    value: 'dot'
  },
  {
    label: '点-紧凑',
    value: 'short-dot'
  },
  {
    label: '长虚线',
    value: 'long-dash'
  },
  {
    label: '长虚线-点',
    value: 'long-dash-dot'
  },
  {
    label: '长虚线-点-点',
    value: 'long-dash-dot-dot'
  },
  {
    label: '短虚线',
    value: 'short-dash'
  },
  {
    label: '短虚线-点',
    value: 'short-dash-dot'
  },
  {
    label: '短虚线-点-点',
    value: 'short-dash-dot-dot'
  }
]
/**
 * Symbol填充样式
 */
export const SymbolFillStyle = [
  { label: '实心', value: 'solid' },
  { label: '右向斜线', value: 'backward-diagonal' },
  { label: '左向斜线', value: 'forward-diagonal' },
  { label: '网格线', value: 'cross' },
  { label: '斜网格线', value: 'diagonal-cross' },
  { label: '水平线', value: 'horizontal' },
  { label: '垂直线', value: 'vertical' },
  { label: '无填充', value: 'none' }
]
export const SymbolPointStyle = [
  { label: '圆形', value: 'circle' },
  { label: '矩形', value: 'square' },
  { label: '十字', value: 'cross' },
  { label: '交叉', value: 'x' },
  { label: '菱形', value: 'diamond' },
  { label: '三角形', value: 'triangle' }
]
export const getSubmenus = parent => {
  switch (parent) {
    case '实时设备':
      return ['水源', '水厂', '流量点', '压力点', '水质', '大用户', '泵站', '渗漏预警']
    case '设备资产':
      return ['管线', '阀门', '水表', '消防栓', '传感器', '居民区']
    case '人员设备':
      return ['巡检人员', '抢修人员', '抄表员', '车辆监控']
    case '业务流程':
      return ['工单流程', '投诉热线', '巡检养护', '用户报装']
    case '数据场景':
      return ['水量热点图', '事件热点图', 'DMA分区']
    case '应急调度':
      return ['视频监控', '调度指挥']
    default:
      return []
  }
}

export const getMenus = (): IMenuItem[] => {
  return [
    {
      path: 'tygj',
      meta: {
        icon: 'iconfont icon-guanli',
        title: '通用工具'
      },
      children: [
        {
          path: 'tygj_tckz',
          meta: {
            title: '图层控制'
          }
        },
        {
          path: 'tygj_clgj',
          meta: {
            title: '测量工具'
          }
        },
        {
          path: 'tygj_gzkj',
          meta: {
            title: '工作空间'
          }
        },
        // {
        //   path: 'tygj_fdj',
        //   meta: {
        //     title: '放大镜'
        //   }
        // },
        // {
        //   path: 'tygj_fpxs',
        //   meta: {
        //     title: '分屏显示'
        //   }
        // },
        {
          path: 'tygj_sxbz',
          meta: {
            title: '属性标注'
          }
        },
        {
          path: 'tygj_ztt',
          meta: {
            title: '专题图'
          }
        },
        {
          path: 'tygj_ztdy',
          meta: {
            title: '制图打印'
          }
        },
        {
          path: 'tygj_mbdy',
          meta: {
            title: '模板打印'
          }
        },
        {
          path: 'tygj_ywbs',
          meta: {
            title: '疑问标识'
          }
        }
      ]
    },
    {
      path: 'gwjk',
      meta: {
        icon: 'iconfont icon-dixiaguanxian',
        title: '管网监控'
      },
      children: [
        {
          path: 'gwjk_gwcx',
          meta: {
            icon: '',
            title: '管网查询'
          },
          children: [
            {
              path: 'gwjk_gwcx_sql',
              meta: {
                title: 'SQL查询',
                icon: ''
              }
            },
            {
              path: 'gwjk_gwcx_dh',
              meta: {
                title: '点号查询',
                icon: ''
              }
            },
            {
              path: 'gwjk_gwcx_tfh',
              meta: {
                title: '图幅号查询'
              }
            },
            {
              path: 'gwjk_gwcx_sbdz',
              meta: {
                title: '设备地址查询'
              }
            },
            {
              path: 'gwjk_gwcx_cz',
              meta: {
                title: '材质查询'
              }
            },
            {
              path: 'gwjk_gwcx_gj',
              meta: {
                title: '管径查询'
              }
            },
            {
              path: 'gwjk_gwcx_jgsj',
              meta: {
                title: '竣工时间查询'
              }
            }
          ]
        },
        {
          path: 'gwjk_gwtj',
          meta: {
            icon: '',
            title: '管网统计'
          },
          children: [
            {
              path: 'gwjk_gwtj_cd',
              meta: {
                title: '长度统计',
                icon: ''
              }
            },
            {
              path: 'gwjk_gwtj_sl',
              meta: {
                title: '数量统计'
              }
            }
          ]
        },
        {
          path: 'gwjk_gwfx',
          meta: {
            title: '管网分析'
          },
          children: [
            {
              path: 'gwjk_gwfx_bg',
              meta: {
                title: '爆管分析'
              }
            },
            {
              path: 'gwjk_gwfx_ltx',
              meta: {
                title: '连通性分析'
              }
            },
            {
              path: 'gwjk_gwfx_hcq',
              meta: {
                title: '缓冲区分析'
              }
            },
            {
              path: 'gwjk_gwfx_hpm',
              meta: {
                title: '横剖面分析'
              }
            },
            // {
            //   path: 'gwjk_gwfx_zpm',
            //   meta: {
            //     title: '纵剖面分析'
            //   }
            // },
            {
              path: 'gwjk_gwfx_gf',
              meta: {
                title: '关阀分析'
              }
            },
            {
              path: 'gwjk_gwfx_zdlj',
              meta: {
                title: '最短路径分析'
              }
            }
          ]
        }
      ]
    },
    {
      path: 'scjk',
      meta: {
        title: '生产监控',
        icon: 'iconfont icon-liuliang'
      }
    },
    {
      path: 'bzjk',
      meta: {
        title: '泵站监控',
        icon: 'iconfont icon-xunjianguanli'
      }
    },
    {
      path: 'yxjk',
      meta: {
        title: '营销监控',
        icon: 'iconfont icon-liuliang'
      },
      children: [
        {
          path: 'yxjk_yscx',
          meta: {
            title: '用水查询'
          }
        },
        {
          path: 'yxjk_yhcx',
          meta: {
            title: '用户查询'
          }
        },
        {
          path: 'yxjk_yhdw',
          meta: {
            title: '用户定位'
          }
        }
      ]
    },
    {
      path: 'dma',
      meta: {
        title: 'DMA管控',
        icon: 'iconfont icon-weihubaoyang'
      }
    },
    {
      path: 'quanqin',
      meta: {
        title: '全屏',
        icon: 'iconfont icon-quanping'
      }
    }
  ]
}
export const getOneMapMenus = (): IMenuItem => {
  const menu: IMenuItem = {
    path: 'GIS一张图',
    meta: {
      title: 'GIS一张图',
      icon: ''
    },
    children: [
      {
        path: 'ssjk',
        meta: {
          title: '实时监控',
          icon: 'iconfont icon-liuliang'
        },
        children: [
          {
            path: 'ssjk-quanbu',
            component: shallowRef(AllStations),
            detailComponent: shallowRef(AllStationsDetail),
            meta: {
              title: '全部',
              alias: '全部站点'
            }
          },
          // {
          //   path: 'ssjk-sy',
          //   component: shallowRef(WaterSource),
          //   detailComponent: shallowRef(WaterSourceDetail),
          //   meta: {
          //     title: '水源',
          //     alias: '水源列表'
          //   }
          // },
          {
            path: 'ssjk-sc',
            component: shallowRef(WaterPlantWS),
            detailComponent: shallowRef(WaterPlantWSDetail),
            meta: {
              title: '污水处理厂',
              alias: '污水处理厂列表'
            }
          },
          // {
          //   path: 'ssjk-sc',
          //   component: shallowRef(WaterPlant),
          //   detailComponent: shallowRef(WaterPlantDetail),
          //   meta: {
          //     title: '水厂',
          //     alias: '水厂列表'
          //   }
          // },
          // {
          //   path: 'ssjk-znsb',
          //   component: shallowRef(SmartMeter),
          //   detailComponent: shallowRef(SmartMeterDetail),
          //   meta: {
          //     title: '智慧水表',
          //     alias: '智能水表列表'
          //   }
          // },
          // {
          //   path: 'ssjk-lljcd',
          //   component: shallowRef(FlowMonitoring),
          //   detailComponent: shallowRef(FlowMonitoringDetail),
          //   meta: {
          //     title: '流量监测点',
          //     alias: '流量监测点列表'
          //   }
          // },
          // {
          //   path: 'ssjk-yljcd',
          //   component: shallowRef(PressureMonitoring),
          //   detailComponent: shallowRef(PressureMonitoringDetail),
          //   meta: {
          //     title: '压力监测点',
          //     alias: '压力监测点列表'
          //   }
          // },
          {
            path: 'ssjk-szjcd',
            component: shallowRef(WaterQualityMonitoring),
            detailComponent: shallowRef(WaterQualityMonitoringDetail),
            meta: {
              title: '水质监测点',
              alias: '水质监测点'
            }
          }
          // {
          //   path: 'ssjk-dyhjcd',
          //   component: shallowRef(BigUser),
          //   detailComponent: shallowRef(BigUserDetail),
          //   meta: {
          //     title: '大用户监测点',
          //     alias: '大用户监测点列表'
          //   }
          // },
          // {
          //   path: 'ssjk-ecgs',
          //   component: shallowRef(Secondary),
          //   detailComponent: shallowRef(SecondaryDetail),
          //   meta: {
          //     title: '泵站',
          //     alias: '泵站列表'
          //   }
          // },
          // {
          //   path: 'ssjk-gwsc',
          //   component: shallowRef(WaterPool),
          //   detailComponent: shallowRef(WaterPoolDetail),
          //   meta: {
          //     title: '高位水池',
          //     alias: '水池列表'
          //   }
          // }
          // {
          //   path: 'ssjk-slyj',
          //   meta: {
          //     title: '渗漏预警',
          //     alias: '渗漏监测点列表'
          //   }
          // }
        ]
      },
      // {
      //   path: 'sbzc',
      //   meta: {
      //     title: '设备资产',
      //     icon: 'iconfont icon-iconmaintain'
      //   },
      //   children: [
      //     {
      //       path: 'sbzc-gx',
      //       component: shallowRef(PipeLine),
      //       detailComponent: shallowRef(PipeDetail),
      //       meta: {
      //         title: '管网管线',
      //         alias: '管线统计'
      //       }
      //     },
      //     {
      //       path: 'sbzc-fm',
      //       component: shallowRef(Valve),
      //       detailComponent: shallowRef(PipeDetail),
      //       meta: {
      //         title: '阀门',
      //         alias: '阀门统计'
      //       }
      //     },
      //     {
      //       path: 'sbzc-sb',
      //       component: shallowRef(WaterMeter),
      //       detailComponent: shallowRef(PipeDetail),
      //       meta: {
      //         title: '水表',
      //         alias: '水表统计'
      //       }
      //     },
      //     {
      //       path: 'sbzc-xfs',
      //       component: shallowRef(FireHydrant),
      //       detailComponent: shallowRef(PipeDetail),
      //       meta: {
      //         title: '消防栓',
      //         alias: '消防栓统计'
      //       }
      //     },
      //     {
      //       path: 'sbzc-cgq',
      //       component: shallowRef(Sensor),
      //       detailComponent: shallowRef(PipeDetail),
      //       meta: {
      //         title: '传感器',
      //         alias: '传感器统计'
      //       }
      //     }
      //     // {
      //     //   path: 'sbzc-jmq',
      //     //   meta: {
      //     //     title: '居民区',
      //     //     alias: '居民区列表'
      //     //   }
      //     // }
      //   ]
      // },
      {
        path: 'rycl',
        meta: {
          title: '人员车辆',
          icon: 'iconfont icon-yonghuguanli'
        },
        children: [
          {
            path: 'rycl-xjry',
            component: shallowRef(Inspection),
            detailComponent: shallowRef(InspectionDetail),
            meta: {
              title: '巡检人员',
              alias: '巡检人员列表'
            }
          }
          // {
          //   path: 'rycl-qxry',
          //   component: shallowRef(Repair),
          //   detailComponent: shallowRef(RepairDetail),
          //   meta: {
          //     title: '抢修人员',
          //     alias: '抢修人员列表'
          //   }
          // },
          // {
          //   path: 'rycl-cby',
          //   component: shallowRef(MeterReading),
          //   detailComponent: shallowRef(MeterReadingDetail),
          //   meta: {
          //     title: '抄表员',
          //     alias: '抄表员列表'
          //   }
          // },
          // {
          //   path: 'rycl-cljk',
          //   component: shallowRef(VehicleMonitoring),
          //   detailComponent: shallowRef(VehicleMonitoringDetail),
          //   meta: {
          //     title: '车辆监控',
          //     alias: '车辆列表'
          //   }
          // }
        ]
      },
      {
        path: 'ywlc',
        meta: {
          title: '业务流程',
          icon: 'iconfont icon-20shiguchuli'
        },
        children: [
          {
            path: 'ywlc-gdlc',
            component: shallowRef(WorkOrder),
            detailComponent: shallowRef(WorkOrderDetail),
            meta: {
              title: '工单流程',
              alias: '工单列表'
            }
          },
          // {
          //   path: 'ywlc-tsrx',
          //   component: shallowRef(Complaint),
          //   detailComponent: shallowRef(ComplaintDetail),
          //   meta: {
          //     title: '投诉热线',
          //     alias: '热线列表'
          //   }
          // },
          {
            path: 'ywlc-xjyh',
            component: shallowRef(InspectionMaintenance),
            detailComponent: shallowRef(InspectionMaintenanceDetail),
            meta: {
              title: '巡检养护',
              alias: '巡检列表'
            }
          }
          // {
          //   path: 'ywlc-yhbz',
          //   component: shallowRef(Install),
          //   detailComponent: shallowRef(InstallDetail),
          //   meta: {
          //     title: '用户报装',
          //     alias: '报装列表'
          //   }
          // }
        ]
      }
      // {
      //   path: 'sphy',
      //   meta: {
      //     title: '视频会议',
      //     icon: 'iconfont icon-shexiangtou',
      //     url: 'https://' + url + '/meeting'
      //   }
      // }
      // {
      //   path: 'sjcj',
      //   meta: {
      //     title: '数据场景',
      //     icon: 'iconfont icon-baobiaoguanli'
      //   },
      //   children: [
      //     {
      //       path: 'sjcj-slrdt',
      //       component: shallowRef(WaterVolume),
      //       detailComponent: shallowRef(WaterVolumeDetail),
      //       meta: {
      //         title: '水量热点图',
      //         alias: '水量统计'
      //       }
      //     },
      //     {
      //       path: 'sjcj-sjrdt',
      //       component: shallowRef(EventHeatMap),
      //       detailComponent: shallowRef(EventHeatMapDetail),
      //       meta: {
      //         title: '事件热点图',
      //         alias: '事件统计'
      //       }
      //     },
      //     {
      //       path: 'sjcj-dma',
      //       component: shallowRef(Dma),
      //       detailComponent: shallowRef(DmaDetail),
      //       meta: {
      //         title: 'DMA分区',
      //         alias: 'DMA分区列表'
      //       }
      //     }
      //   ]
      // },
      // {
      //   path: 'yjdd',
      //   meta: {
      //     title: '应急调度',
      //     icon: 'iconfont icon-yalijiance'
      //   },
      //   children: [
      //     {
      //       path: 'yjdd-spjk',
      //       component: shallowRef(VideoSurveillance),
      //       detailComponent: shallowRef(VideoSurveillanceDetail),
      //       meta: {
      //         title: '视频监控',
      //         alias: '视频监控列表'
      //       }
      //     },
      //     {
      //       path: 'yjdd-ddzh',
      //       component: shallowRef(DispatchCommand),
      //       meta: {
      //         title: '调度指挥'
      //       }
      //     }
      //   ]
      // }
    ]
  }
  const hiddenMenus = window.SITE_CONFIG.ONE_MAP_CONFIG.hiddenMenus
  if (hiddenMenus?.length) {
    menu.children = menu.children?.filter(item => hiddenMenus.indexOf(item.path) === -1)
  }
  const hiddenSubMenus = window.SITE_CONFIG.ONE_MAP_CONFIG.hiddenSubMenus
  if (hiddenSubMenus?.length) {
    menu.children?.map(item => {
      item.children = item.children?.filter(cItem => hiddenSubMenus.indexOf(cItem.path) === -1)
    })
  }
  return menu
}
/**
 * 获取打印格式列表
 * @returns
 */
export const getPrintFormat = (): NormalOption[] => {
  const formates = ['pdf', 'png32', 'png8', 'jpg', 'gif', 'eps', 'svg', 'svgz']
  return formates.map(item => {
    return {
      label: item,
      value: item,
      id: item
    }
  })
}
export const getPrintTemplate = (): NormalOption[] => {
  const res = [
    {
      layoutOptions: {
        hasAuthorText: true,

        hasCopyrightText: true,

        hasTitleText: true,

        hasLegend: true,

        customTextElements: []
      },

      pageSize: [
        42.0,

        29.7
      ],

      layoutTemplate: 'A3 Landscape',

      activeDataFrameSize: [
        39.999800000001414,

        21.168799999999464
      ]
    },

    {
      layoutOptions: {
        hasAuthorText: true,

        hasCopyrightText: true,

        hasTitleText: true,

        hasLegend: true,

        customTextElements: []
      },

      pageSize: [
        29.7,

        42.0
      ],

      layoutTemplate: 'A3 Portrait',

      activeDataFrameSize: [
        27.675600000000486,

        29.000200000000405
      ]
    },

    {
      layoutOptions: {
        hasAuthorText: true,

        hasCopyrightText: true,

        hasTitleText: true,

        hasLegend: true,

        customTextElements: []
      },

      pageSize: [
        29.7,

        21.0
      ],

      layoutTemplate: 'A4 Landscape',

      activeDataFrameSize: [
        27.756400000000212,

        15.917999999999665
      ]
    },

    {
      layoutOptions: {
        hasAuthorText: true,

        hasCopyrightText: true,

        hasTitleText: true,

        hasLegend: true,

        customTextElements: []
      },

      pageSize: [
        21.0,

        29.7
      ],

      layoutTemplate: 'A4 Portrait',

      activeDataFrameSize: [
        19.023999999999432,

        22.284999999999854
      ]
    },

    {
      layoutOptions: {
        hasAuthorText: true,

        hasCopyrightText: true,

        hasTitleText: true,

        hasLegend: true,

        customTextElements: []
      },

      pageSize: [
        11.0,

        8.5
      ],

      layoutTemplate: 'Letter ANSI A Landscape',

      activeDataFrameSize: [
        10.0,

        6.25
      ]
    },

    {
      layoutOptions: {
        hasAuthorText: true,

        hasCopyrightText: true,

        hasTitleText: true,

        hasLegend: true,

        customTextElements: []
      },

      pageSize: [
        8.5,

        11.0
      ],

      layoutTemplate: 'Letter ANSI A Portrait',

      activeDataFrameSize: [
        7.5,

        8.0
      ]
    },

    {
      layoutOptions: {
        hasAuthorText: true,

        hasCopyrightText: true,

        hasTitleText: true,

        hasLegend: true,

        customTextElements: []
      },

      pageSize: [
        17.0,

        11.0
      ],

      layoutTemplate: 'Tabloid ANSI B Landscape',

      activeDataFrameSize: [
        16.0,

        7.757100000000719
      ]
    },

    {
      layoutOptions: {
        hasAuthorText: true,

        hasCopyrightText: true,

        hasTitleText: true,

        hasLegend: true,

        customTextElements: []
      },

      pageSize: [
        11.0,

        17.0
      ],

      layoutTemplate: 'Tabloid ANSI B Portrait',

      activeDataFrameSize: [
        10.0,

        11.75
      ]
    }
  ]
  return res.map(item => {
    return {
      value: item.layoutTemplate.toLowerCase().replace(' ', '-'),
      label: item.layoutTemplate,
      data: item
    }
  })
  // const templates = [
  //   {
  //     id: 'DefaultA4L',
  //     name: 'A4横向(29.7x21)(cm)',
  //     width: 268.035,
  //     height: 171.583
  //   },
  //   {
  //     id: 'DefaultA4P',
  //     name: 'A4纵向(21×29.7)(cm)',
  //     width: 186.409,
  //     height: 229.585
  //   },
  //   {
  //     id: 'DefaultA3L',
  //     name: 'A3横向(42×29.7)(cm)',
  //     width: 381.453,
  //     height: 244.187
  //   },
  //   {
  //     id: 'DefaultA3P',
  //     name: 'A3纵向(29.7×42)(cm)',
  //     width: 263.652,
  //     height: 324.72
  //   },
  //   {
  //     id: 'DefaultA2L',
  //     name: 'A2横向(59.4×42)(cm)',
  //     width: 539.43,
  //     height: 345.316
  //   },
  //   {
  //     id: 'DefaultA2P',
  //     name: 'A2纵向(42×59.4)(cm)',
  //     width: 372.842,
  //     height: 459.2
  //   },
  //   {
  //     id: 'DefaultA1L',
  //     name: 'A1横向(84.1x59.4)(cm)',
  //     width: 762.911,
  //     height: 488.376
  //   },
  //   {
  //     id: 'DefaultA1P',
  //     name: 'A1纵向(59.4×84.1)(cm)',
  //     width: 527.305,
  //     height: 649.44
  //   },
  //   {
  //     id: 'DefaultA0L',
  //     name: 'A0横向(118.88x84.1)(cm)',
  //     width: 1080.147,
  //     height: 691.455
  //   },
  //   {
  //     id: 'DefaultA0P',
  //     name: 'A0纵向(84.1×118.88)(cm)',
  //     width: 746.571,
  //     height: 919.493
  //   }
  // ]
  // return formatTree(templates, { label: 'name', id: 'id', value: 'id' })
}
export enum EGigLogFunc {
  XINZENGGW = '新增管网',
  GWBIANJI = '管网编辑',
  SHUXINGSHUA = '属性刷',
  SHUXINBIANJI = '属性编辑',
  XIUGAISBSS = '修改设备类型',
  XUNJIANRENWU = '巡检任务'
}
export const GisLogFuncs = [
  EGigLogFunc.XINZENGGW,
  EGigLogFunc.GWBIANJI,
  EGigLogFunc.SHUXINGSHUA,
  EGigLogFunc.SHUXINBIANJI,
  EGigLogFunc.XIUGAISBSS,
  EGigLogFunc.XUNJIANRENWU
]
export const GisLogFuncOptions = GisLogFuncs.map(item => {
  return { label: item, value: item }
})
export enum EGisLogApp {
  BASICGIS = '基础GIS',
  INSPECT = '巡检养护'
}
export const GisLogApps = [EGisLogApp.BASICGIS, EGisLogApp.INSPECT]
export const GisLogAppsOption = GisLogApps.map(item => {
  return { label: item, value: item }
})
export enum EGisLogOperateType {
  DELETE = '删除',
  ADD = '新增',
  UPDATE = '编辑'
}
export const GisLogOperateTypes = [EGisLogOperateType.ADD, EGisLogOperateType.DELETE, EGisLogOperateType.UPDATE]
export const GisLogOperateTypeOptions = GisLogOperateTypes.map(item => {
  return { label: item, value: item }
})
