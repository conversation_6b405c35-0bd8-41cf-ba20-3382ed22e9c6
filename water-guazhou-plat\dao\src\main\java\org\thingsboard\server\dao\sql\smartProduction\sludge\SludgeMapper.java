package org.thingsboard.server.dao.sql.smartProduction.sludge;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.request.SludgeRequest;
import org.thingsboard.server.dao.model.sql.smartProduction.sludge.Sludge;

import java.util.List;

@Mapper
public interface SludgeMapper extends BaseMapper<Sludge> {

    IPage<Sludge> getList(IPage<Sludge> page, @Param("param") SludgeRequest request);

    List<JSONObject> countByYear(@Param("year") String year, @Param("tenantId") String tenantId);
}
