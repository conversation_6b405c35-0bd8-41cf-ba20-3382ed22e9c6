import{d as Z,M as tt,c as L,r as T,am as et,bB as Y,bF as at,o as ot,ay as nt,g as k,n as v,p as F,q as n,F as i,G as c,bh as I,i as l,av as st,aB as A,aJ as E,h as lt,cC as rt,S as it,bU as ft,cE as dt,J as pt,cK as ct,cL as ut,cM as mt,bW as yt,c5 as _t,aq as gt,C as Dt}from"./index-r0dFAfgr.js";import{_ as kt}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{j as It,k as bt,l as Ct}from"./process-DWVjEFpZ.js";import{s as vt,f as wt,c as Tt}from"./applyInstall-D-IustB3.js";import{d as R}from"./printUtils-C-AxhDcd.js";const Ft={class:"top"},St={style:{"margin-top":"20px"}},Nt={style:{"margin-top":"20px"}},xt=["onClick"],ht=Z({__name:"detail",props:{taskInfo:{},taskId:{}},setup(U){const{$messageError:w,$messageSuccess:$,$messageWarning:x}=tt(),b=L(null),C=L(),M=L(),S=U,t=T({activeName:"",formJson:{},optionData:{},formData:{},currentStepId:"",contractList:[],flowDetail:[],fileTypes:[],fileDetail:{},fileArray:[],taskInfo:{},currentContent:null});et(()=>S.taskId,o=>{console.log(o)});const O=T({type:"tabs",tabType:"inset",width:"100%",tabs:[],handleTabClick:o=>{var e,a,d;t.currentContent=(e=t.flowDetail)==null?void 0:e.find(r=>r.id===o.props.name),console.log(t.currentContent),(d=b.value)==null||d.setFormJson(JSON.parse((a=t.currentContent)==null?void 0:a.content)),Y(()=>{var r,p;(p=b.value)==null||p.setFormData(JSON.parse((r=t.currentContent)==null?void 0:r.formData))}),t.fileTypes=[],J()}}),h=T({title:"资料上传",group:[{fields:[{type:"select",label:"文档类型",field:"fileType",options:[],rules:[{required:!0,message:"请选择文档类型"}],onChange:o=>{var r,p,u,g,D,f;t.fileDetail=(r=t.fileTypes)==null?void 0:r.find(m=>m.id===o);const e=(p=h.group[0].fields)==null?void 0:p.find(m=>m.field==="hint");e.text=t.fileDetail.num;const a=(g=(u=C.value)==null?void 0:u.refForm)==null?void 0:g.dataForm;console.log((f=(D=C.value)==null?void 0:D.refForm)==null?void 0:f.dataForm),t.fileArray.find(m=>m.fileType===o)||t.fileArray.push(a)}},{type:"input",label:"文档编号",field:"code"},{type:"hint",label:"上传文件个数",field:"hint",text:"0"},{type:"file",label:"选中文件",field:"file",returnType:"arrStr",rules:[{required:!0,message:"选中文件"}]}]}],submit:o=>{var e;((e=o.file)==null?void 0:e.split(",").length)>=t.fileDetail.num?(t.fileArray=t.fileArray.map(a=>({...t.fileDetail,file:a.file})),console.log(t.fileArray),vt(t.fileArray).then(a=>{var d,r,p,u;((d=a.data)==null?void 0:d.code)===200?((r=C.value)==null||r.closeDialog(),(u=(p=C.value)==null?void 0:p.refForm)==null||u.resetForm(),J(),$("上传成功！")):w("上传失败！")}).catch(a=>{w(a)})):x("请上传指定数量文件")}}),G=T({title:"流程明细",desTroyOnClose:!0,group:[],submit:void 0}),V=T({columns:[{label:"流程",prop:"title",cellStyle:o=>{var e;return{color:o.status==="已完成"?"#409eff":o.id===((e=t.taskInfo)==null?void 0:e.currentStepId)?"red":""}}},{label:"开始时间",prop:"startTime",formatter:o=>B(o.startTime)},{label:"结束时间",prop:"endTime",formatter:o=>B(o.endTime)},{label:"工作人员",prop:"operatorName"},{label:"说明",prop:"status",formatter:o=>o.status==="已完成"?"√":" "}],dataList:[],pagination:{hide:!0}}),H=()=>{var o;(o=M.value)==null||o.openDialog(),console.log(t.flowDetail),V.dataList=t.flowDetail},W=async()=>{var o;if(t.fileTypes.length>0){(o=C.value)==null||o.openDialog();const e=h.group[0].fields.find(a=>a.field==="fileType");e.options=t.fileTypes.map(a=>({label:a.name,value:a.id,data:{num:a.num}}))}else x("无需上传文件")},z=async o=>{const e=o.file.split("/").pop().split(".").pop();R(o.file,`${o.name}.${e}`)},K=()=>{it("确定提交吗?","提示").then(()=>{var e;const o=t.fileTypes.filter(a=>(!a.file||a.num>a.file.split(",").length)&&a.num>0);console.log(o),o.length===0?(e=b.value)==null||e.getFormData().then(a=>{const d={mainId:S.taskInfo.id,id:t.currentContent.id,status:"已完成",formData:JSON.stringify(a)};wt(d).then(r=>{var p,u;((p=r.data)==null?void 0:p.code)===200?($("提交成功"),j()):w((u=r.data)==null?void 0:u.message)}).catch(r=>{w(r)})}).catch(a=>{w(a)}):x("请上传所需资料")})},B=o=>o?at(o).format("YYYY-MM-DD HH:mm:ss"):"",P=async o=>{var a,d;const e=await Ct(o);t.contractList=(a=e.data)==null?void 0:a.data.contractTemplateList,console.log((d=e.data)==null?void 0:d.data)},J=async()=>{var e;const o=await It(S.taskInfo.id,t.currentContent.id);t.fileTypes=(e=o.data)==null?void 0:e.data},Q=async()=>{var e;const o=await Tt(S.taskInfo.id);t.taskInfo=(e=o.data)==null?void 0:e.data},j=async()=>{var d,r,p,u,g,D;await Q();const o=(d=t.taskInfo)==null?void 0:d.currentStepId,a=(p=(await bt((r=t.taskInfo)==null?void 0:r.id)).data)==null?void 0:p.data;t.flowDetail=a,O.tabs=a.map(f=>{var m;return{label:f.title,value:f.id,disabled:f.status!=="已完成"&&((m=t.taskInfo)==null?void 0:m.currentStepId)!==f.id,data:{content:f.content,formData:f.formData,id:f.id}}}),t.activeName=o,t.currentContent=a.find(f=>f.id===o),(g=b.value)==null||g.setFormJson(JSON.parse((u=t.currentContent)==null?void 0:u.content)),await Y(()=>{var f,m;(m=b.value)==null||m.setFormData(JSON.parse((f=t.currentContent)==null?void 0:f.formData))}),await P((D=t.taskInfo)==null?void 0:D.type),await J()};return ot(async()=>{await j()}),(o,e)=>{const a=ft,d=dt,r=pt,p=ct,u=ut,g=mt,D=yt,f=_t,m=nt("v-form-render"),q=kt,X=gt;return k(),v("div",null,[F("div",Ft,[n(D,{gutter:20},{default:i(()=>[n(a,{span:2},{default:i(()=>e[1]||(e[1]=[c(" 申请编号： ")])),_:1}),n(a,{span:4},{default:i(()=>{var s;return[c(I((s=l(t).taskInfo)==null?void 0:s.code),1)]}),_:1}),n(a,{span:2},{default:i(()=>e[2]||(e[2]=[c(" 登记日期： ")])),_:1}),n(a,{span:16},{default:i(()=>{var s;return[c(I(B((s=l(t).taskInfo)==null?void 0:s.createTime)),1)]}),_:1}),n(a,{span:2},{default:i(()=>e[3]||(e[3]=[c(" 工程地址： ")])),_:1}),n(a,{span:4},{default:i(()=>{var s;return[c(I((s=l(t).taskInfo)==null?void 0:s.address),1)]}),_:1}),n(a,{span:2},{default:i(()=>e[4]||(e[4]=[c(" 工程类型： ")])),_:1}),n(a,{span:16},{default:i(()=>{var s;return[c(I((s=l(t).taskInfo)==null?void 0:s.typeName),1)]}),_:1}),n(a,{span:2},{default:i(()=>e[5]||(e[5]=[c(" 当前状态： ")])),_:1}),n(a,{span:6},{default:i(()=>{var s,_,y;return[F("span",{style:st({color:((s=l(t).taskInfo)==null?void 0:s.status)==="已完成"?"red":"green"})},I(((_=l(t).taskInfo)==null?void 0:_.status)=="1"?"进行中":(y=l(t).taskInfo)==null?void 0:y.status),5)]}),_:1}),n(a,{span:16},{default:i(()=>[n(g,{trigger:"click"},{dropdown:i(()=>[n(u,null,{default:i(()=>[(k(!0),v(A,null,E(l(t).contractList,(s,_)=>(k(),lt(p,{key:_,onClick:y=>z(s)},{default:i(()=>[c(I(s.name),1)]),_:2},1032,["onClick"]))),128))]),_:1})]),default:i(()=>[n(r,{type:"primary","split-button":""},{default:i(()=>[e[6]||(e[6]=c(" 下载合同 ")),n(d,{class:"el-icon--right"},{default:i(()=>[n(l(rt))]),_:1})]),_:1})]),_:1})]),_:1}),n(a,{span:16},{default:i(()=>{var s,_,y,N;return[n(r,{type:"primary",disabled:((s=l(t).taskInfo)==null?void 0:s.status)==="已完成"||((_=l(t).currentContent)==null?void 0:_.status)==="进行中",onClick:K},{default:i(()=>e[7]||(e[7]=[c(" 提交 ")])),_:1},8,["disabled"]),n(r,{type:"warning",onClick:H},{default:i(()=>e[8]||(e[8]=[c(" 流程明细 ")])),_:1}),n(r,{type:"primary",disabled:((y=l(t).taskInfo)==null?void 0:y.status)==="已完成"||((N=l(t).currentContent)==null?void 0:N.status)==="进行中",onClick:W},{default:i(()=>e[9]||(e[9]=[c(" 上传资料 ")])),_:1},8,["disabled"])]}),_:1})]),_:1})]),n(f,{modelValue:l(t).activeName,"onUpdate:modelValue":e[0]||(e[0]=s=>l(t).activeName=s),config:l(O)},null,8,["modelValue","config"]),F("div",St,[n(m,{ref_key:"vFormRef",ref:b,"form-json":l(t).formJson,"form-data":l(t).formData,"option-data":l(t).optionData},null,8,["form-json","form-data","option-data"]),F("div",Nt,[(k(!0),v(A,null,E(l(t).fileTypes,(s,_)=>(k(),v("div",{key:_},[(k(!0),v(A,null,E(JSON.parse(s.file),(y,N)=>(k(),v("div",{key:N,style:{color:"#409eff",padding:"10px"}},[F("a",{style:{"border-bottom":"1px solid #409eff"},onClick:Bt=>l(R)(y.url,y.name)},I(y.name),9,xt)]))),128))]))),128))])]),n(q,{ref_key:"refDialog",ref:C,config:l(h)},null,8,["config"]),n(q,{ref_key:"refDetailDialog",ref:M,config:l(G)},{default:i(()=>[n(X,{config:l(V)},null,8,["config"])]),_:1},8,["config"])])}}}),Mt=Dt(ht,[["__scopeId","data-v-caac506d"]]);export{Mt as default};
