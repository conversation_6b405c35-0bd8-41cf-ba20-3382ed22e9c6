import{d as _,c as d,r as s,g as f,n as u,q as c,i,p as g,aB as m,aq as h,C as b}from"./index-r0dFAfgr.js";import{_ as y}from"./Search-NSrhrIa_.js";import{b as x}from"./gisUser-Ba96nctf.js";const S={class:"table-box"},k=_({__name:"WaterSearch",props:{view:{}},setup(v){const o=d(),e=s({dataList:[],height:300,columns:[{label:"水量",prop:"count"},{label:"收费金额",prop:"fee"},{label:"收费时间",prop:"time"}],pagination:{hide:!0,refreshData:({page:t,size:a})=>{e.pagination.page=t,e.pagination.limit=a,r()}}}),l=s({filters:[{type:"input",label:"用户编号",field:"yhbh"},{type:"btn-group",btns:[{perm:!0,text:"查询",type:"default",loading:()=>e.loading===!0,click:()=>r()},{perm:!0,type:"danger",text:"导出",disabled:()=>e.loading===!0,click:()=>{}}]}]}),r=async()=>{var t;e.loading=!0;try{const{yhbh:a}=((t=o.value)==null?void 0:t.queryParams)||{},n=await x(a);e.dataList=n.data}catch(a){console.dir(a)}e.loading=!1};return(t,a)=>{const n=y,p=h;return f(),u(m,null,[c(n,{ref_key:"refSearch",ref:o,style:{padding:"0"},config:i(l)},null,8,["config"]),g("div",S,[c(p,{config:i(e)},null,8,["config"])])],64)}}}),D=b(k,[["__scopeId","data-v-ca5ee38d"]]);export{D as default};
