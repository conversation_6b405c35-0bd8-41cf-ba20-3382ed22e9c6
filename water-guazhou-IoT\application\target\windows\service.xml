<service>
    <id>thingsboard</id>
    <name>ThingsBoard Server Application</name>
    <description>Open-source IoT Platform - Device management, data collection, processing and visualization</description>
    <workingdirectory>%BASE%\conf</workingdirectory>
    <logpath>%BASE%\logs</logpath>
    <logmode>rotate</logmode>
    <env name="LOADER_PATH" value="%BASE%\conf,%BASE%\extensions" />
    <env name="SQL_DATA_FOLDER" value="%BASE%\data\sql" />
    <executable>java</executable>
    <startargument>-Dplatform=windows</startargument>
    <startargument>-Dinstall.data_dir=%BASE%\data</startargument>
    <startargument>-Xloggc:%BASE%\logs\gc.log</startargument>
    <startargument>-XX:+HeapDumpOnOutOfMemoryError</startargument>
    <startargument>-XX:+PrintGCDetails</startargument>
    <startargument>-XX:+PrintGCDateStamps</startargument>
    <startargument>-XX:+PrintHeapAtGC</startargument>
    <startargument>-XX:+PrintTenuringDistribution</startargument>
    <startargument>-XX:+PrintGCApplicationStoppedTime</startargument>
    <startargument>-XX:+UseGCLogFileRotation</startargument>
    <startargument>-XX:NumberOfGCLogFiles=10</startargument>
    <startargument>-XX:GCLogFileSize=10M</startargument>
    <startargument>-XX:-UseBiasedLocking</startargument>
    <startargument>-XX:+UseTLAB</startargument>
    <startargument>-XX:+ResizeTLAB</startargument>
    <startargument>-XX:+PerfDisableSharedMem</startargument>
    <startargument>-XX:+UseCondCardMark</startargument>
    <startargument>-XX:CMSWaitDuration=10000</startargument>
    <startargument>-XX:+UseParNewGC</startargument>
    <startargument>-XX:+UseConcMarkSweepGC</startargument>
    <startargument>-XX:+CMSParallelRemarkEnabled</startargument>
    <startargument>-XX:+CMSParallelInitialMarkEnabled</startargument>
    <startargument>-XX:+CMSEdenChunksRecordAlways</startargument>
    <startargument>-XX:CMSInitiatingOccupancyFraction=75</startargument>
    <startargument>-XX:+UseCMSInitiatingOccupancyOnly</startargument>
    <startargument>-Xms512m</startargument>
    <startargument>-Xmx1024m</startargument>
    <startargument>-jar</startargument>
    <startargument>%BASE%\lib\thingsboard.jar</startargument>

</service>
