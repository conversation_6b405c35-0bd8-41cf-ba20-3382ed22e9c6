package org.thingsboard.server.dao.model.sql.assay;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * 水质-化验报告-报告-采样地点
 */
@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.ASSAY_REPORT_ADDRESS_TABLE)
@TableName(ModelConstants.ASSAY_REPORT_ADDRESS_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class AssayReportAddress {

    @Id
    @TableId
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @TableField(value = ModelConstants.ASSAY_REPORT_ADDRESS_REPORT_ID)
    private String reportId;

    @TableField(value = ModelConstants.ASSAY_REPORT_ADDRESS_ADDRESS_ID)
    private String addressId;

    @TableField(exist = false)
    private String addressName;

    @TableField(value = ModelConstants.CREATE_TIME)
    private Date createTime;

    @TableField(value = ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;


}
