import{S as v}from"./SideDrawer-CBntChyn.js";import{d as k,j as C,r as V,a8 as b,g as d,n as p,h as m,F as D,ax as n,i as a,an as w,p as H,aw as h,av as M,C as W}from"./index-r0dFAfgr.js";const $=k({__name:"DrawerBox",props:{leftDrawer:{type:Boolean},leftDrawerWidth:{},leftDrawerAbsolute:{type:Boolean},leftDrawerBarPosition:{},leftDrawerTitle:{},leftDrawerBarHide:{type:Boolean},leftDrawerModal:{type:Boolean},leftDrawerMinWidth:{},rightDrawer:{type:Boolean},rightDrawerWidth:{},rightDrawerAbsolute:{type:Boolean},rightDrawerBarPosition:{},rightDrawerTitle:{},rightDrawerBarHide:{type:<PERSON>olean},rightDrawerModal:{type:Boolean},rightDrawerMinWidth:{},bottomDrawer:{type:Boolean},bottomDrawerHeight:{},bottomDrawerBarPosition:{},bottomDrawerTitle:{},bottomDrawerBarHide:{type:Boolean},bottomDrawerModal:{type:Boolean},topDrawer:{type:Boolean},topDrawerHeight:{},topDrawerBarPosition:{},topDrawerTitle:{},topDrawerBarHide:{type:Boolean},topDrawerModal:{type:Boolean},theme:{},beforeCollapse:{type:Function}},setup(u,{expose:g}){const B=C(),l=u,o=V({leftDrawerOpen:!1,rightDrawerOpen:!1,bottomDrawerOpen:!1,topDrawerOpen:!1}),y=b(()=>({"--left-width":(l.leftDrawerWidth||350)+"px","--right-width":(l.rightDrawerWidth||350)+"px"})),O=b(()=>{const e=l.leftDrawer&&!l.leftDrawerAbsolute&&o.leftDrawerOpen,r=l.rightDrawer&&!l.rightDrawerAbsolute&&o.rightDrawerOpen;let i=0;const t="layout-drawerbox-content-v-";let f="";return e&&(i++,f=t+"left"),r&&(i++,f=t+"right"),i===2&&(f=t+"both"),f}),s=async(e,r)=>{try{if(l.beforeCollapse&&await l.beforeCollapse(e,r)===!1)return}catch{}switch(e){case"rtl":o.rightDrawerOpen=r===void 0?!o.rightDrawerOpen:r;break;case"ltr":o.leftDrawerOpen=r===void 0?!o.leftDrawerOpen:r;break;case"btt":o.bottomDrawerOpen=r===void 0?!o.bottomDrawerOpen:r;break;case"ttb":o.topDrawerOpen=r===void 0?!o.topDrawerOpen:r;break}};return g({toggleDrawer:s}),(e,r)=>{const i=v;return d(),p("div",{class:h(["layout-drawerbox-container",[e.theme||(a(B).isDark?"dark":"light")]]),style:M(a(y))},[e.leftDrawer&&!e.leftDrawerAbsolute?(d(),m(i,{key:0,modelValue:a(o).leftDrawerOpen,"onUpdate:modelValue":r[0]||(r[0]=t=>a(o).leftDrawerOpen=t),direction:"ltr",theme:e.theme,width:e.leftDrawerWidth,absolute:e.leftDrawerAbsolute,"bar-position":e.leftDrawerBarPosition,title:e.leftDrawerTitle,"hide-bar":e.leftDrawerBarHide,"min-width":e.leftDrawerMinWidth,onCollapse:r[1]||(r[1]=t=>s("ltr",t))},{title:D(()=>[n(e.$slots,"left-title",{},void 0,!0)]),default:D(()=>[n(e.$slots,"left",{},void 0,!0)]),_:3},8,["modelValue","theme","width","absolute","bar-position","title","hide-bar","min-width"])):w("",!0),H("div",{class:h(["layout-drawerbox-content",[a(O)]])},[n(e.$slots,"default",{},void 0,!0),a(o).bottomDrawerOpen&&l.bottomDrawerModal?(d(),p("div",{key:0,class:"sidedrawer-modal",onClick:r[2]||(r[2]=t=>s("btt",!1))})):w("",!0),a(o).topDrawerOpen&&l.topDrawerModal?(d(),p("div",{key:1,class:"sidedrawer-modal",onClick:r[3]||(r[3]=t=>s("ttb",!1))})):w("",!0),e.topDrawer?(d(),m(i,{key:2,modelValue:a(o).topDrawerOpen,"onUpdate:modelValue":r[4]||(r[4]=t=>a(o).topDrawerOpen=t),direction:"ttb",theme:e.theme,width:e.topDrawerHeight,"bar-position":e.topDrawerBarPosition,title:e.topDrawerTitle,"hide-bar":e.topDrawerBarHide,onCollapse:r[5]||(r[5]=t=>s("ttb",t))},{default:D(()=>[n(e.$slots,"top",{},void 0,!0)]),_:3},8,["modelValue","theme","width","bar-position","title","hide-bar"])):w("",!0),e.bottomDrawer?(d(),m(i,{key:3,modelValue:a(o).bottomDrawerOpen,"onUpdate:modelValue":r[6]||(r[6]=t=>a(o).bottomDrawerOpen=t),direction:"btt",theme:e.theme,width:e.bottomDrawerHeight,"bar-position":e.bottomDrawerBarPosition,title:e.bottomDrawerTitle,"hide-bar":e.bottomDrawerBarHide,onCollapse:r[7]||(r[7]=t=>s("btt",t))},{default:D(()=>[n(e.$slots,"bottom",{},void 0,!0)]),_:3},8,["modelValue","theme","width","bar-position","title","hide-bar"])):w("",!0)],2),a(o).leftDrawerOpen&&l.leftDrawerModal?(d(),p("div",{key:1,class:"sidedrawer-modal",onClick:r[8]||(r[8]=t=>s("ltr",!1))})):w("",!0),a(o).rightDrawerOpen&&l.rightDrawerModal?(d(),p("div",{key:2,class:"sidedrawer-modal",onClick:r[9]||(r[9]=t=>s("rtl",!1))})):w("",!0),e.leftDrawer&&e.leftDrawerAbsolute?(d(),m(i,{key:3,modelValue:a(o).leftDrawerOpen,"onUpdate:modelValue":r[10]||(r[10]=t=>a(o).leftDrawerOpen=t),direction:"ltr",theme:e.theme,width:e.leftDrawerWidth,absolute:e.leftDrawerAbsolute,"bar-position":e.leftDrawerBarPosition,title:e.leftDrawerTitle,"hide-bar":e.leftDrawerBarHide,"min-width":e.leftDrawerMinWidth,onCollapse:r[11]||(r[11]=t=>s("ltr",t))},{title:D(()=>[n(e.$slots,"left-title",{},void 0,!0)]),default:D(()=>[n(e.$slots,"left",{},void 0,!0)]),_:3},8,["modelValue","theme","width","absolute","bar-position","title","hide-bar","min-width"])):w("",!0),e.rightDrawer?(d(),m(i,{key:4,modelValue:a(o).rightDrawerOpen,"onUpdate:modelValue":r[12]||(r[12]=t=>a(o).rightDrawerOpen=t),direction:"rtl",theme:e.theme,width:e.rightDrawerWidth,absolute:e.rightDrawerAbsolute,"bar-position":e.rightDrawerBarPosition,title:e.rightDrawerTitle,"hide-bar":e.rightDrawerBarHide,"min-width":e.rightDrawerMinWidth,onCollapse:r[13]||(r[13]=t=>s("rtl",t))},{title:D(()=>[n(e.$slots,"right-title",{},void 0,!0)]),default:D(()=>[n(e.$slots,"right",{},void 0,!0)]),_:3},8,["modelValue","theme","width","absolute","bar-position","title","hide-bar","min-width"])):w("",!0)],6)}}}),T=W($,[["__scopeId","data-v-135a9f9e"]]);export{T as D};
