<!-- 实时位置 -->
<template>
  <RightDrawerMap
    ref="refMap"
    title="实时位置"
    :hide-coords="true"
    :hide-search="true"
    :hide-layer-list="true"
    :pops="state.pops"
    @map-loaded="onMaploaded"
    @pop-toggle="(pop, flag) => (pop.visible = flag)"
  >
    <template #map-bars>
      <div class="arc-bar refresh-bar">
        <el-checkbox
          v-model="inspectInfo.autoRefresh"
          @change="() => toggleRefresh()"
        >
          自动刷新
        </el-checkbox>
        <span class="danger">{{ inspectInfo.refreshLasts }}</span>
        <span class="text">s后刷新</span>
        <el-button
          size="small"
          :icon="Refresh"
          type="primary"
          :loading="inspectInfo.isRefreshing"
          @click="toggleRefresh(true)"
        >
          刷新
        </el-button>
      </div>
      <div class="arc-bar status-bar">
        <el-button
          v-for="(item, i) in state.status"
          :key="i"
          :type="item.type as any"
          @click="
            () => {
              state.curStatus = item.value
              refreshData()
            }
          "
        >
          {{ item.label }}
        </el-button>
      </div>
      <div class="arc-bar rolelist-bar">
        <VerticalBar
          :menus="state.menus"
          @change="
            menu => {
              state.curMenu = menu?.path
              refreshData()
            }
          "
        ></VerticalBar>
      </div>
    </template>
    <Form
      ref="refForm"
      :config="FormConfig"
    ></Form>
    <div class="tree-box overlay-y">
      <FormTree :config="TreeConfig"></FormTree>
    </div>
  </RightDrawerMap>
</template>
<script lang="ts" setup>
import { Refresh, Search as SearchIcon, UserFilled } from '@element-plus/icons-vue'
import { IFormIns } from '@/components/type'
import { bindViewClick, gotoAndHighLight } from '@/utils/MapHelper'
import RightDrawerMap from '../../components/common/RightDrawerMap.vue'
import VerticalBar from '../../components/menus/VerticalBar.vue'
import { getWaterSupplyTree } from '@/api/company_org'
import { formatTree } from '@/utils/GlobalHelper'
import { useUserLocation } from '@/hooks/arcgis'
import { GetUserSettingList } from '@/api/user'

const userlocation = useUserLocation(graphic => highlightCallBack(graphic))
const refForm = ref<IFormIns>()
const refMap = ref<InstanceType<typeof RightDrawerMap>>()
const staticState: {
  view?: __esri.MapView
} = {}
const state = reactive<{
  pops: IArcPopConfig[]
  loading: boolean
  menus: IMenuItem[]
  status: NormalOption[]
  curStatus: string
  curMenu: string
}>({
  pops: [],
  loading: false,
  menus: [],
  status: [
    { label: '全部', value: '', type: 'primary' },
    { label: '在线', value: '2', type: 'success' },
    { label: '离线', value: '1', type: 'warning' },
    { label: '未使用', value: '0', type: 'info' }
  ],
  curStatus: '',
  curMenu: ''
})
const inspectInfo = reactive<{
  autoRefresh: boolean
  refreshLasts: number
  timer?: any
  isRefreshing: boolean
}>({
  autoRefresh: false,
  refreshLasts: 20,
  isRefreshing: false
})
const TreeConfig = reactive<IFormTree>({
  type: 'tree',
  field: 'person',
  props: {
    label: 'userName'
  },
  nodeKey: 'userId',
  options: [],
  nodeClick: (data: any) => {
    const graphic = userlocation.getGraphic(data.userId)
    locateToPerson(graphic)
  }
})
const FormConfig = reactive<IFormConfig>({
  group: [
    {
      fields: [
        {
          type: 'select-tree',
          field: 'pid',
          checkStrictly: true,
          defaultExpandAll: true,
          options: [],
          onChange: () => refreshData(),
          extraFormItem: [
            {
              type: 'input',
              field: 'name',
              prefixIcon: shallowRef(SearchIcon),
              appendBtns: [
                {
                  perm: true,
                  svgIcon: shallowRef(Refresh),
                  click: () => refreshData()
                }
              ]
            }
          ]
        }
      ]
    }
  ],
  labelPosition: 'top',
  defaultValue: {}
})

const refreshData = async () => {
  if (inspectInfo.isRefreshing) return
  inspectInfo.isRefreshing = true
  try {
    const query = refForm.value?.dataForm || {}
    const res = await userlocation._getLatestUserCoords({
      departmentId: query.pid,
      userName: query.name,
      status: state.curStatus,
      userTypeId: state.curMenu === 'qb' ? '' : state.curMenu
    })
    TreeConfig.options = res.data
    state.pops = userlocation.generatePops(staticState.view, res.data, true, id => {
      const g = userlocation.getGraphic(id)
      gotoAndHighLight(staticState.view, g, {
        zoom: 15,
        avoidHighlight: true
      })
    })
  } catch (error) {
    //
  }
  inspectInfo.refreshLasts = 20
  inspectInfo.isRefreshing = false
}
const locateToPerson = (graphic?: __esri.Graphic) => {
  gotoAndHighLight(staticState.view, graphic, {
    zoom: 15,
    avoidHighlight: true
  })
  userlocation.highlight.highlight(staticState.view, graphic, highlightCallBack)
}
const toggleRefresh = (immediate?: boolean) => {
  if (immediate) {
    refreshData()
  }
  inspectInfo.timer && clearInterval(inspectInfo.timer)
  if (!inspectInfo.autoRefresh) {
    inspectInfo.refreshLasts = 20
  } else {
    inspectInfo.timer = setInterval(() => {
      inspectInfo.refreshLasts === 0 ? refreshData() : (inspectInfo.refreshLasts -= 1)
    }, 1000)
  }
}
const initOrganazation = () => {
  getWaterSupplyTree(2).then(res => {
    const select = FormConfig.group[0].fields[0] as ISelectTree
    select.options = formatTree(res.data.data || [])
    refForm.value && (refForm.value.dataForm.pid = select.options?.[0].value)
    refreshData()
  })
}
const initMenu = () => {
  GetUserSettingList({
    page: 1,
    size: 999
  }).then(res => {
    state.menus = res.data?.data?.data.map(item => {
      return {
        path: item.type,
        meta: {
          title: item.name,
          // icon: 'iconfont icon-xunjian'
          svgIcon: shallowRef(UserFilled)
        }
      }
    })
    state.menus.unshift({
      path: 'qb',
      meta: {
        title: '全部',
        svgIcon: shallowRef(UserFilled)
      }
    })
  })
}
const highlightCallBack = (graphic?: __esri.Graphic) => {
  if (graphic?.attributes.userId) refMap.value?.openPop(graphic?.attributes.userId)
}
const onMaploaded = async view => {
  staticState.view = view
  initMenu()
  initOrganazation()
  bindViewClick(staticState.view, async () => {
    refMap.value?.closeAllPop()
  })
}
</script>
<style lang="scss" scoped>
.dark {
  .arc-bar {
    background-color: var(--el-bg-color);
  }
}
.arc-bar {
  position: absolute;
  background-color: #fff;
}
.refresh-bar {
  font-size: 14px;
  left: 50%;
  transform: translateX(-50%);
  top: 15px;
  height: 50px;

  padding: 8px 20px;
  display: flex;
  align-items: center;
  border-radius: 4px;
  .danger {
    color: red;
    margin: 0 4px 0 12px;
    font-size: 16px;
  }
  .text {
    margin-right: 20px;
  }
}
.status-bar {
  bottom: 12px;
  left: 50%;
  transform: translateX(-50%);
  width: 400px;
  height: 70px;
  display: flex;
  justify-content: space-around;
  align-items: center;
  border-radius: 12px;
  padding: 4px 8px;
}
.rolelist-bar {
  top: 15px;
  left: 12px;
  width: 120px;
  height: auto;
  border-radius: 4px;
}
.tree-box {
  height: calc(100% - 82px);
}
</style>
