package org.thingsboard.server.dao.util.imodel.query.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.thingsboard.server.dao.model.sql.base.BaseIotConfiguration;
import org.thingsboard.server.dao.model.sql.base.BaseVideoConfiguration;
import org.thingsboard.server.dao.util.imodel.query.PageableQueryEntity;

/**
 * 平台管理-视频管理对象 base_video_configuration
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@ApiModel(value = "视频管理", description = "平台管理-视频管理实体类")
@Data
public class BaseVideoConfigurationPageRequest extends PageableQueryEntity<BaseVideoConfiguration> {

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 设备名称
     */
    @ApiModelProperty(value = "设备名称")
    private String name;

    /**
     * 设备类型
     */
    @ApiModelProperty(value = "设备类型")
    private String type;

    /**
     * 设备品牌
     */
    @ApiModelProperty(value = "设备品牌")
    private String brand;

    /**
     * 设备型号
     */
    @ApiModelProperty(value = "设备型号")
    private String model;

    /**
     * 设备ip地址
     */
    @ApiModelProperty(value = "设备ip地址")
    private String ipAddress;

    /**
     * 视频流类型
     */
    @ApiModelProperty(value = "视频流类型")
    private String streamType;

    /**
     * 分辨率
     */
    @ApiModelProperty(value = "分辨率")
    private String resolution;

    /**
     * 帧率
     */
    @ApiModelProperty(value = "帧率")
    private String frameRate;

    /**
     * 视频编码格式
     */
    @ApiModelProperty(value = "视频编码格式")
    private String videoCodec;
}
