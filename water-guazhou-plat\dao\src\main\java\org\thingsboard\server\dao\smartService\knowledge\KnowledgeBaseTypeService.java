package org.thingsboard.server.dao.smartService.knowledge;

import org.thingsboard.server.dao.model.sql.smartService.knowledge.KnowledgeBaseType;

import java.util.List;

/**
 * 知识库类型
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-10-27
 */
public interface KnowledgeBaseTypeService {

    List<KnowledgeBaseType> getList(String tenantId);

    KnowledgeBaseType save(KnowledgeBaseType knowledgeBaseType);

    int delete(List<String> ids);

    void getAllIdByPid(String id, List<String> allIds);
}
