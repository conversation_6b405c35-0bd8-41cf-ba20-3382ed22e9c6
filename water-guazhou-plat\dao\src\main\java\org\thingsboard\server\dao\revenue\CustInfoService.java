package org.thingsboard.server.dao.revenue;

import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.model.sql.revenue.CustInfo;

import java.util.List;

public interface CustInfoService {

    CustInfo findOneByCodeAndTenantId(String code, String tenantId);

    List<CustInfo> findByUserCodeList(List<String> userCodeList, TenantId tenantId);

    List<String> findOpenIdListByMeterBookId(String meterBookIds, String tenantId);

    List<String> findOpenIdListByDmaId(String dmaIds, String tenantId);
}
