package org.thingsboard.server.dao.util.imodel.query.smartManagement.plan;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class SMCircuitPlanDeviceSaveRequest {
    public static final String DEVICE_SPECIFIER = "设备";

    public static final String SPECIAL_DEVICE_SPECIFIER = "专项设备";

    private String name;

    private String serialId;

    public CircuitTaskReportSaveRequest build(String type, String code) {
        CircuitTaskReportSaveRequest circuitTaskReportSaveRequest = new CircuitTaskReportSaveRequest();
        circuitTaskReportSaveRequest.setType(type);
        circuitTaskReportSaveRequest.setTask_code(code);
        circuitTaskReportSaveRequest.setName(name);
        circuitTaskReportSaveRequest.setDeviceType(serialId);
        return circuitTaskReportSaveRequest;

    }
}
