package org.thingsboard.server.dao.smartPipe;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.request.PartitionMountRequest;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.PartitionMount;
import org.thingsboard.server.dao.sql.smartPipe.PartitionMountMapper;

import java.util.Date;
import java.util.List;

/**
 *
 */
@Service
public class PartitionMountServiceImpl implements PartitionMountService {

    @Autowired
    private PartitionMountMapper partitionMountMapper;

    @Override
    public PartitionMount save(PartitionMount partitionMount) {
        if (StringUtils.isBlank(partitionMount.getId())) {
            partitionMount.setCreateTime(new Date());
            partitionMountMapper.insert(partitionMount);
        } else {
            partitionMountMapper.updateById(partitionMount);
        }
        return partitionMount;
    }

    @Override
    public String changeDirection(String id) {
        PartitionMount partitionMount = partitionMountMapper.selectById(id);
        if (partitionMount == null) {
            return "该挂载关系不存在";
        }
        switch (partitionMount.getDirection()) {
            case "1":
                partitionMount.setDirection("2");
                break;
            case "2":
                partitionMount.setDirection("1");
                break;
            case "3":
                partitionMount.setDirection("4");
                break;
            case "4":
                partitionMount.setDirection("3");
                break;
        }
        partitionMountMapper.updateById(partitionMount);

        return "";
    }

    @Override
    public PageData<PartitionMount> getList(PartitionMountRequest request) {
        IPage<PartitionMount> page = new Page<>(request.getPage(), request.getSize());
        IPage<PartitionMount> result = partitionMountMapper.getList(page, request);
        return new PageData<>(result.getTotal(), result.getRecords());
    }

    @Override
    public List batchSave(List<PartitionMount> partitionMountList, String tenantId) {
        for (PartitionMount partitionMount : partitionMountList) {
            partitionMount.setTenantId(tenantId);
            // 流向 默认正进负出
            if (!DataConstants.PARTITION_MOUNT_DEVICE_TYPE.PRESSURE.getValue().equals(partitionMount.getType())) {
                partitionMount.setDirection(DataConstants.PARTITION_MOUNT_DIRECTION.IN.getValue());
            }
            partitionMount.setCreateTime(new Date());

            partitionMountMapper.insert(partitionMount);
        }
        return partitionMountList;
    }

    @Override
    public void delete(List<String> ids) {
        partitionMountMapper.deleteBatchIds(ids);
    }
}
