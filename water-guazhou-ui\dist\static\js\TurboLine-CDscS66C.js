import{e as P,n as D}from"./enums-B5k73o5q.js";import{X as mt}from"./definitions-826PWLuy.js";function Tt(x,i){return x.x===i.x&&x.y===i.y}function Nt(x){if(!x)return;const i=x.length;if(i<=1)return;let r=0;for(let g=1;g<i;g++)Tt(x[g],x[r])||++r===g||(x[r]=x[g]);x.length=r+1}function p(x,i){return x.x=i.y,x.y=-i.x,x}function R(x,i){return x.x=-i.y,x.y=i.x,x}function ft(x,i){return x.x=i.x,x.y=i.y,x}function ut(x,i){return x.x=-i.x,x.y=-i.y,x}function vt(x){return Math.sqrt(x.x*x.x+x.y*x.y)}function Et(x,i){return x.x*i.y-x.y*i.x}function wt(x,i){return x.x*i.x+x.y*i.y}function et(x,i,r,g){return x.x=i.x*r+i.y*g,x.y=i.x*g-i.y*r,x}class Ut{constructor(i,r,g){this._writeVertex=i,this._writeTriangle=r,this._canUseThinTessellation=g,this._prevNormal={x:void 0,y:void 0},this._nextNormal={x:void 0,y:void 0},this._textureNormalLeft={x:0,y:1},this._textureNormalRight={x:0,y:-1},this._textureNormal={x:void 0,y:void 0},this._joinNormal={x:void 0,y:void 0},this._inner={x:void 0,y:void 0},this._outer={x:void 0,y:void 0},this._roundStart={x:void 0,y:void 0},this._roundEnd={x:void 0,y:void 0},this._startBreak={x:void 0,y:void 0},this._endBreak={x:void 0,y:void 0},this._innerPrev={x:void 0,y:void 0},this._innerNext={x:void 0,y:void 0},this._bevelStart={x:void 0,y:void 0},this._bevelEnd={x:void 0,y:void 0},this._bevelMiddle={x:void 0,y:void 0}}tessellate(i,r){Nt(i),this._canUseThinTessellation&&r.halfWidth<mt&&!r.offset?this._tessellateThin(i,r):this._tessellate(i,r)}_tessellateThin(i,r){if(i.length<2)return;const g=r.wrapDistance||65535;let E=r.initialDistance||0,X=!1,F=i[0].x,$=i[0].y;const B=i.length;for(let j=1;j<B;++j){X&&(X=!1,E=0);let W=i[j].x,Q=i[j].y,b=W-F,v=Q-$,w=Math.sqrt(b*b+v*v);if(b/=w,v/=w,E+w>g){X=!0;const t=(g-E)/w;w=g-E,W=(1-t)*F+t*W,Q=(1-t)*$+t*Q,--j}const o=this._writeVertex(F,$,0,0,b,v,v,-b,0,-1,E),y=this._writeVertex(F,$,0,0,b,v,-v,b,0,1,E);E+=w;const G=this._writeVertex(W,Q,0,0,b,v,v,-b,0,-1,E),e=this._writeVertex(W,Q,0,0,b,v,-v,b,0,1,E);this._writeTriangle(o,y,G),this._writeTriangle(y,G,e),F=W,$=Q}}_tessellate(i,r){const g=i[0],E=i[i.length-1],X=Tt(g,E),F=X?3:2;if(i.length<F)return;const $=r.pixelCoordRatio,B=r.capType!=null?r.capType:P.BUTT,j=r.joinType!=null?r.joinType:D.MITER,W=r.miterLimit!=null?Math.min(r.miterLimit,4):2,Q=r.roundLimit!=null?Math.min(r.roundLimit,1.05):1.05,b=r.halfWidth!=null?r.halfWidth:2,v=!!r.textured;let w,o,y,G=null;const e=this._prevNormal,t=this._nextNormal;let K=-1,q=-1;const s=this._joinNormal;let a,c;const rt=this._textureNormalLeft,nt=this._textureNormalRight,f=this._textureNormal;let d=-1,h=-1;const at=r.wrapDistance||65535;let l=r.initialDistance||0;const gt=this._writeVertex,Vt=this._writeTriangle,u=(U,st,H,V,L,S)=>{const A=gt(o,y,a,c,H,V,U,st,L,S,l);return d>=0&&h>=0&&A>=0&&Vt(d,h,A),d=h,h=A,A};X&&(w=i[i.length-2],t.x=E.x-w.x,t.y=E.y-w.y,q=vt(t),t.x/=q,t.y/=q);let Y=!1;for(let U=0;U<i.length;++U){if(Y&&(Y=!1,l=0),w&&(e.x=-t.x,e.y=-t.y,K=q,l+K>at&&(Y=!0)),Y){const n=(at-l)/K;K=at-l,w={x:(1-n)*w.x+n*i[U].x,y:(1-n)*w.y+n*i[U].y},--U}else w=i[U];o=w.x,y=w.y;const st=U<=0&&!Y,H=U===i.length-1;if(st||(l+=K),G=H?X?i[1]:null:i[U+1],G?(t.x=G.x-o,t.y=G.y-y,q=vt(t),t.x/=q,t.y/=q):(t.x=void 0,t.y=void 0),!X){if(st){R(s,t),a=s.x,c=s.y,B===P.SQUARE&&(u(-t.y-t.x,t.x-t.y,t.x,t.y,0,-1),u(t.y-t.x,-t.x-t.y,t.x,t.y,0,1)),B===P.ROUND&&(u(-t.y-t.x,t.x-t.y,t.x,t.y,-1,-1),u(t.y-t.x,-t.x-t.y,t.x,t.y,-1,1)),B!==P.ROUND&&B!==P.BUTT||(u(-t.y,t.x,t.x,t.y,0,-1),u(t.y,-t.x,t.x,t.y,0,1));continue}if(H){p(s,e),a=s.x,c=s.y,B!==P.ROUND&&B!==P.BUTT||(u(e.y,-e.x,-e.x,-e.y,0,-1),u(-e.y,e.x,-e.x,-e.y,0,1)),B===P.SQUARE&&(u(e.y-e.x,-e.x-e.y,-e.x,-e.y,0,-1),u(-e.y-e.x,e.x-e.y,-e.x,-e.y,0,1)),B===P.ROUND&&(u(e.y-e.x,-e.x-e.y,-e.x,-e.y,1,-1),u(-e.y-e.x,e.x-e.y,-e.x,-e.y,1,1));continue}}let V,L,S=-Et(e,t);if(Math.abs(S)<.01)wt(e,t)>0?(s.x=e.x,s.y=e.y,S=1,V=Number.MAX_VALUE,L=!0):(R(s,t),S=1,V=1,L=!1);else{s.x=(e.x+t.x)/S,s.y=(e.y+t.y)/S,V=vt(s);const n=(V-1)*b*$;L=V>4||n>K&&n>q}a=s.x,c=s.y;let A=j;switch(j){case D.BEVEL:V<1.05&&(A=D.MITER);break;case D.ROUND:V<Q&&(A=D.MITER);break;case D.MITER:V>W&&(A=D.BEVEL)}switch(A){case D.MITER:if(u(s.x,s.y,-e.x,-e.y,0,-1),u(-s.x,-s.y,-e.x,-e.y,0,1),H)break;if(v){const n=Y?0:l;d=this._writeVertex(o,y,a,c,t.x,t.y,s.x,s.y,0,-1,n),h=this._writeVertex(o,y,a,c,t.x,t.y,-s.x,-s.y,0,1,n)}break;case D.BEVEL:{const n=S<0;let T,m,z,M;if(n){const _=d;d=h,h=_,T=rt,m=nt}else T=nt,m=rt;if(L)z=n?R(this._innerPrev,e):p(this._innerPrev,e),M=n?p(this._innerNext,t):R(this._innerNext,t);else{const _=n?ut(this._inner,s):ft(this._inner,s);z=_,M=_}const k=n?p(this._bevelStart,e):R(this._bevelStart,e);u(z.x,z.y,-e.x,-e.y,T.x,T.y);const yt=u(k.x,k.y,-e.x,-e.y,m.x,m.y);if(H)break;const O=n?R(this._bevelEnd,t):p(this._bevelEnd,t);if(L){const _=this._writeVertex(o,y,a,c,-e.x,-e.y,0,0,0,0,l);d=this._writeVertex(o,y,a,c,t.x,t.y,M.x,M.y,T.x,T.y,l),h=this._writeVertex(o,y,a,c,t.x,t.y,O.x,O.y,m.x,m.y,l),this._writeTriangle(yt,_,h)}else{if(v){const _=this._bevelMiddle;_.x=(k.x+O.x)/2,_.y=(k.y+O.y)/2,et(f,_,-e.x,-e.y),u(_.x,_.y,-e.x,-e.y,f.x,f.y),et(f,_,t.x,t.y),d=this._writeVertex(o,y,a,c,t.x,t.y,_.x,_.y,f.x,f.y,l),h=this._writeVertex(o,y,a,c,t.x,t.y,M.x,M.y,T.x,T.y,l)}else{const _=d;d=h,h=_}u(O.x,O.y,t.x,t.y,m.x,m.y)}if(n){const _=d;d=h,h=_}break}case D.ROUND:{const n=S<0;let T,m;if(n){const N=d;d=h,h=N,T=rt,m=nt}else T=nt,m=rt;const z=n?ut(this._inner,s):ft(this._inner,s);let M,k;L?(M=n?R(this._innerPrev,e):p(this._innerPrev,e),k=n?p(this._innerNext,t):R(this._innerNext,t)):(M=z,k=z);const yt=n?p(this._roundStart,e):R(this._roundStart,e),O=n?R(this._roundEnd,t):p(this._roundEnd,t),_=u(M.x,M.y,-e.x,-e.y,T.x,T.y),ot=u(yt.x,yt.y,-e.x,-e.y,m.x,m.y);if(H)break;const C=this._writeVertex(o,y,a,c,-e.x,-e.y,0,0,0,0,l);L||this._writeTriangle(d,h,C);const I=ut(this._outer,z),J=this._writeVertex(o,y,a,c,t.x,t.y,O.x,O.y,m.x,m.y,l);let Z,tt;const ht=V>2;if(ht){let N;V!==Number.MAX_VALUE?(I.x/=V,I.y/=V,N=wt(e,I),N=(V*(N*N-1)+1)/N):N=-1,Z=n?p(this._startBreak,e):R(this._startBreak,e),Z.x+=e.x*N,Z.y+=e.y*N,tt=n?R(this._endBreak,t):p(this._endBreak,t),tt.x+=t.x*N,tt.y+=t.y*N}et(f,I,-e.x,-e.y);const lt=this._writeVertex(o,y,a,c,-e.x,-e.y,I.x,I.y,f.x,f.y,l);et(f,I,t.x,t.y);const ct=v?this._writeVertex(o,y,a,c,t.x,t.y,I.x,I.y,f.x,f.y,l):lt,dt=C,_t=v?this._writeVertex(o,y,a,c,t.x,t.y,0,0,0,0,l):C;let it=-1,xt=-1;if(ht&&(et(f,Z,-e.x,-e.y),it=this._writeVertex(o,y,a,c,-e.x,-e.y,Z.x,Z.y,f.x,f.y,l),et(f,tt,t.x,t.y),xt=this._writeVertex(o,y,a,c,t.x,t.y,tt.x,tt.y,f.x,f.y,l)),v?ht?(this._writeTriangle(dt,ot,it),this._writeTriangle(dt,it,lt),this._writeTriangle(_t,ct,xt),this._writeTriangle(_t,xt,J)):(this._writeTriangle(dt,ot,lt),this._writeTriangle(_t,ct,J)):ht?(this._writeTriangle(C,ot,it),this._writeTriangle(C,it,xt),this._writeTriangle(C,xt,J)):(this._writeTriangle(C,ot,lt),this._writeTriangle(C,ct,J)),L?(d=this._writeVertex(o,y,a,c,t.x,t.y,k.x,k.y,T.x,T.y,l),h=J):(d=v?this._writeVertex(o,y,a,c,t.x,t.y,k.x,k.y,T.x,T.y,l):_,this._writeTriangle(d,_t,J),h=J),n){const N=d;d=h,h=N}break}}}}}export{Ut as c};
