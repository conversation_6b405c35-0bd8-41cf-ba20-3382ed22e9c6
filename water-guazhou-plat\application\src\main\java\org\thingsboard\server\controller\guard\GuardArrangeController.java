package org.thingsboard.server.controller.guard;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.guard.GuardArrangeService;
import org.thingsboard.server.dao.model.sql.smartProduction.guard.GuardArrange;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.guard.GuardArrangePageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.guard.GuardArrangeQuickArrangeRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.guard.GuardArrangeSaveRequest;
import org.thingsboard.server.utils.imodel.annotations.IStarController2;

@IStarController2
@RequestMapping({"/api/sp/guardArrange"})
public class GuardArrangeController extends BaseController {
    @Autowired
    private GuardArrangeService service;

    public GuardArrangeController() {
    }

    public GuardArrange findById(@PathVariable String id) {
        return this.service.findById(id);
    }

    @GetMapping
    public Map<String, List<GuardArrange>> findAllConditional(GuardArrangePageRequest request) {
        return this.service.findAllConditional(request);
    }

    @PostMapping({"/arrange/detectOverride"})
    public boolean detectArrangeOverride(@RequestBody GuardArrangeQuickArrangeRequest req) {
        return this.service.detectArrangeOverride(req);
    }

    @PostMapping({"/arrange/quick"})
    public boolean quickArrange(@RequestBody GuardArrangeQuickArrangeRequest req) throws ThingsboardException {
        String userId = UUIDConverter.fromTimeUUID(this.getCurrentUser().getUuidId());
        String tenantId = UUIDConverter.fromTimeUUID(this.getTenantId().getId());
        return this.service.quickArrange(req, userId, tenantId);
    }

    @PostMapping({"/arrange/temp"})
    public GuardArrange save(@RequestBody GuardArrangeSaveRequest req) {
        return this.service.save(req);
    }

    @GetMapping({"currentGuard"})
    public GuardArrange getCurrentGuard() throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(this.getTenantId().getId());
        return this.service.getCurrentGuard(tenantId);
    }

    public boolean delete(@PathVariable String id) {
        return this.service.delete(id);
    }
}
