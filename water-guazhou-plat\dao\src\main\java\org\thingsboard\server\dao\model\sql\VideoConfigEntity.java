package org.thingsboard.server.dao.model.sql;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/4/26 10:51
 */
@Data
@TableName("video_config")
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
public class VideoConfigEntity {

    @TableId
    private String id;

    private String host;

    private String hkAppKey;

    private String hkAppSecret;

    private String dhUsername;

    private String dhPassword;

    private String dhClientId;

    private String dhClientSecret;

    private Date createTime;

    private String tenantId;

    private String type;

}
