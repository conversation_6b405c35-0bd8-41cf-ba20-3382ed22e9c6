package org.thingsboard.server.dao.model.sql.smartProduction.guard;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseUsername;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.util.Date;

@Getter
@Setter
@ResponseEntity
@TableName("guard_rearrange_record")
public class GuardRearrangeRecord {
    // id
    private String id;

    // 地点id
    private String placeId;

    // 班次id
    private String classId;

    // 班次名称
    private String className;

    // 值班日期
    private Date dayTime;

    // 开始时间
    private String beginTime;

    // 结束时间
    private String endTime;

    // 之前的班组用户id，多个用逗号隔开
    private String beforeGroupUser;

    // 之前的班组用户名称，多个用顿号隔开
    private String beforeGroupUserName;

    // 之后的班组用户id，多个用逗号隔开
    private String afterGroupUser;

    // 之后的班组用户名称，多个用顿号隔开
    private String afterGroupUserName;

    // 调班原因
    private String remark;

    // 操作人id
    @ParseUsername
    private String creator;

    // 操作时间
    private Date createTime;

    // 租户id
    private String tenantId;

}
