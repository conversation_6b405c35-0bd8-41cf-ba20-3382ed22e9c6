package org.thingsboard.server.dao.model.sql.smartService.wechat;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import org.springframework.util.StringUtils;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.util.Date;

@Getter
@Setter
@ResponseEntity
@TableName("wx_message_record")
public class WxMessageRecord {
    // id
    private String id;

    // 标题
    private String title;

    // 同步停水通知
    private Boolean syncWaterOff;

    // 点击消息卡片跳转的地址
    @Getter(AccessLevel.NONE)
    private String url;

    // 原因
    private String reason;

    // 备注
    private String remark;

    // 页面模板id
    private String templateId;

    // 页面模板名称
    private String templateName;

    // 页面模板JSON
    private String template;

    // 模板变量键值映射信息JSON
    private String variables;

    // 发送人id
    private String sendUserId;

    // 完成状态,0-发送中、1-发送完成
    private Integer status;

    // 创建时间
    private Date createTime;

    // 客户id
    private String tenantId;

    // 客户id
    private String meterBookIds;

    // 客户id
    private String dmaIds;

    public String getUrl(String recordId) {
        if (!StringUtils.hasText(url)) {
            return url;
        }
        if (url.contains("?")) {
            return url + "&recordId=" + recordId;
        }
        return url + "?recordId=" + recordId;
    }

}
