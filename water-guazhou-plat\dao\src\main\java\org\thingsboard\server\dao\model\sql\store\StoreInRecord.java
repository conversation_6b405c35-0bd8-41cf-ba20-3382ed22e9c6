package org.thingsboard.server.dao.model.sql.store;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.sql.department.StoreMapper;
import org.thingsboard.server.dao.sql.supplier.SupplierMapper;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseTenantName;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseUsername;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseViaMapper;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.util.Date;

@Getter
@Setter
@ResponseEntity
public class StoreInRecord {
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    // 入库单编号
    private String code;

    // 入库单标题
    private String title;

    // 批次号
    private String batchCode;

    // 采购单ID
    private String purchaseId;

    // 目标仓库ID
    @ParseViaMapper(StoreMapper.class)
    private String storehouseId;

    // 验收人ID
    @ParseUsername(withDepartment = true)
    private String acceptor;

    // 经办人
    @ParseUsername(withDepartment = true)
    private String manager;

    // 所属合同ID
    private String contractId;

    // 发票编号
    private String invoiceCode;

    // 供应商ID
    @ParseViaMapper(SupplierMapper.class)
    private String supplierId;

    // 入库时间
    private Date inTime;

    // 是否补录
    private Boolean addRecord;

    // 创建人
    @ParseUsername
    private String creator;

    // 创建时间
    private Date createTime;

    // 备注/说明
    private String remark;

    // 租户ID
    @ParseTenantName
    private String tenantId;

}
