package org.thingsboard.server.dao.util.imodel.query.smartService.portal;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartService.portal.SsPortalHomeInfo;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;

@Getter
@Setter
public class SsPortalHomeInfoSaveRequest extends SaveRequest<SsPortalHomeInfo> {
    // 标题
    private String name;

    // 封面
    private String logo;

    // 链接
    private String address;

    // 二维码图片
    private String qr;

    // 客服电话
    private String phone;

    // 公司油邮箱
    private String email;

    // 邮编地址
    private String postcode;


    @Override
    protected SsPortalHomeInfo build() {
        SsPortalHomeInfo entity = new SsPortalHomeInfo();
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    @Override
    protected SsPortalHomeInfo update(String id) {
        SsPortalHomeInfo entity = new SsPortalHomeInfo();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(SsPortalHomeInfo entity) {
        entity.setName(name);
        entity.setLogo(logo);
        entity.setAddress(address);
        entity.setQr(qr);
        entity.setPhone(phone);
        entity.setEmail(email);
        entity.setPostcode(postcode);
    }

}