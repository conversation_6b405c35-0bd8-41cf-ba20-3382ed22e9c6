package org.thingsboard.server.dao.sql.department;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.store.*;
import org.thingsboard.server.dao.util.imodel.query.store.DeviceStorageJournalPageRequest;
import org.thingsboard.server.dao.util.imodel.query.store.RestDeviceInfoPageRequest;
import org.thingsboard.server.dao.util.imodel.query.store.RestDeviceStorageJournalPageRequest;

import java.util.Date;
import java.util.List;

@Mapper
public interface DeviceStorageJournalMapper extends BaseMapper<DeviceStorageJournal> {
    IPage<DeviceStorageJournalResponse> findByPage(DeviceStorageJournalPageRequest request);

    DeviceStorageJournalResponse findById(String id);

    int save(DeviceStorageJournal entity);

    @Override
    default int insert(DeviceStorageJournal entity) {
        return save(entity);
    }

    int saveAll(List<DeviceStorageJournal> list);

    boolean update(DeviceStorageJournal entity);

    int updateAll(List<DeviceStorageJournal> list);

    int checkoutAll(@Param("list") List<String> list, @Param("storeOutItemId") String storeOutItemId, @Param("tenantId") String tenantId);

    boolean checkinViaStoreOutItemId(String storeOutItemId);

    int checkinAllViaStoreOutItemId(List<String> list);

    List<MainRestDeviceStorageJournal> findRestByPage(@Param("page") long page, @Param("size") long size, @Param("req") RestDeviceStorageJournalPageRequest request);

    boolean checkout(@Param("id") String id, @Param("storeOutItemId") String storeOutItemId);

    int restCount(@Param("storeOutItemId") String storeOutItemId, @Param("tenantId") String tenantId);

    boolean checkoutViaStoreOut(@Param("id") String id, @Param("storeOutId") String storeOutId);

    IPage<RestDeviceStorageJournal> findRestWithoutSplitByPage(@Param("req") RestDeviceStorageJournalPageRequest request);

    IPage<RestDeviceStorageInfo> findRestDeviceInfoConditional(RestDeviceInfoPageRequest request);

    long countRestCountByPage(@Param("req") RestDeviceStorageJournalPageRequest request);

    DetailDeviceStorageInfo detail(String id);

    boolean updateScrappedTime(@Param("deviceLabelCode") String deviceLabelCode, @Param("receiveTime") Date receiveTime, @Param("tenantId") String tenantId);

    boolean canOperate(@Param("storeOutId") String storeOutId, @Param("currentUserId") String currentUserId);

}
