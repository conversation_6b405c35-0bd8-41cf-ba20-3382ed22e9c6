<!-- 分区总览 -->
<template>
  <DrawerBox
    ref="refDrawerBox"
    :right-drawer="true"
    :bottom-drawer="true"
    :bottom-drawer-title="TreeData.currentProject?.data?.label"
    bottom-drawer-bar-position="left"
  >
    <template #right>
      <SLTree :tree-data="TreeData"></SLTree>
    </template>
    <template #bottom>
      <DivisionDetail
        :data="TreeData.currentProject"
        :partitions="partition.List.value"
      ></DivisionDetail>
    </template>
    <ArcLayout
      ref="refArcLayout"
      @map-loaded="onMapLoaded"
    >
    </ArcLayout>
  </DrawerBox>
</template>
<script lang="ts" setup>
import DivisionDetail from './components/DivisionDetail.vue'
import DrawerBox from '@/components/DrawerBox/DrawerBox.vue'
import { usePartition } from '@/hooks/arcgis'

const refDrawerBox = ref<InstanceType<typeof DrawerBox>>()
const TreeData = reactive<SLTreeConfig>({
  data: [],
  isFilterTree: true,
  title: '分区总览',
  treeNodeHandleClick(params) {
    refDrawerBox.value?.toggleDrawer('btt', true)
    const par = partition.List.value.find(item => item.id === params.id)
    TreeData.currentProject = {
      data: params,
      partition: par
    }
    par.geom
      && partition.extentToPartition(staticsState.view, JSON.parse(par.geom))
  }
})
const staticsState: {
  view?: __esri.MapView
} = {}
const partition = usePartition()
const refreshTree = async () => {
  await partition.getTree()
  TreeData.data = partition.Tree.value || []
}

const onMapLoaded = async (view: __esri.MapView) => {
  staticsState.view = view
  refreshTree()
  partition.refreshPartitions(staticsState.view)
}
onMounted(async () => {
  refDrawerBox.value?.toggleDrawer('rtl', true)
  refreshTree()
})
</script>
<style lang="scss" scoped></style>
