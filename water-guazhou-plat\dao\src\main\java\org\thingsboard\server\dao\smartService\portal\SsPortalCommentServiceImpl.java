package org.thingsboard.server.dao.smartService.portal;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.smartService.portal.SsPortalComment;
import org.thingsboard.server.dao.sql.smartService.portal.SsPortalCommentMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.smartService.portal.SsPortalCommentPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartService.portal.SsPortalCommentSaveRequest;

@Service
public class SsPortalCommentServiceImpl implements SsPortalCommentService {
    @Autowired
    private SsPortalCommentMapper mapper;

    @Override
    public SsPortalComment findById(String id) {
        return mapper.selectById(id);
    }

    @Override
    public IPage<SsPortalComment> findAllConditional(SsPortalCommentPageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    public SsPortalComment save(SsPortalCommentSaveRequest entity) {
        return QueryUtil.saveOrUpdateOneByRequest(entity, mapper::insert, mapper::updateFully);
    }

    @Override
    public boolean update(SsPortalComment entity) {
        return mapper.update(entity);
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }

    @Override
    public boolean canSave(String tenantId) {
        return mapper.canSave(tenantId);
    }

}
