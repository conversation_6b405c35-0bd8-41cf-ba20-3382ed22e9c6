<!-- 导出CAD -->
<template>
  <RightDrawerMap
    ref="refMap"
    title="导出CAD"
    :detail-max-min="true"
    :hide-detail-close="true"
    @map-loaded="onMaploaded"
  >
    <Form
      ref="refForm"
      :config="FormConfig"
    ></Form>
    <template #detail-header>
      <span>导出CAD任务列表</span>
    </template>
    <template #detail-default>
      <InlineForm
        ref="refSearch"
        :config="SearchConfig"
      ></InlineForm>
      <div class="table-box">
        <FormTable :config="TableConfig"></FormTable>
      </div>
    </template>
  </RightDrawerMap>
</template>
<script lang="ts" setup>
import { Download, Refresh } from '@element-plus/icons-vue'
import SketchViewModel from '@arcgis/core/widgets/Sketch/SketchViewModel.js'
import RightDrawerMap from '../../components/common/RightDrawerMap.vue'
import { getGraphicLayer, getSubLayerIds, setSymbol } from '@/utils/MapHelper'
import { SLConfirm, SLMessage } from '@/utils/Message'
import { queryLayerClassName } from '@/api/mapservice'
import { ExportMapData, ListExportMapTasks } from '@/api/mapservice/tools'
import { downloadUrl } from '@/utils/fileHelper'

const refMap = ref<InstanceType<typeof RightDrawerMap>>()
const refSearch = ref<IInlineFormIns>()
const refForm = ref<IFormIns>()
const state = reactive<{ layerIds: number[]; layerInfos: any[]; timer?: any; refreshCount: number }>({
  layerIds: [],
  layerInfos: [],
  refreshCount: 0
})
const staticState: {
  view?: __esri.MapView
  graphic?: __esri.Graphic
  graphicsLayer?: __esri.GraphicsLayer
  sketch?: __esri.SketchViewModel
  sketchUpdateHandler?: any
  sketchCreateHandler?: any
} = {}
const FormConfig = reactive<IFormConfig>({
  labelPosition: 'top',
  group: [
    {
      fieldset: {
        desc: '绘制工具'
      },
      fields: [
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              title: '绘制多边形',
              disabled: (): boolean => FormConfig.submitting === true,
              iconifyIcon: 'mdi:shape-polygon-plus',
              click: () => initDraw('polygon')
            },
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              title: '绘制矩形',
              disabled: (): boolean => FormConfig.submitting === true,
              iconifyIcon: 'ep:crop',
              click: () => initDraw('rectangle')
            },
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              title: '绘制圆形',
              disabled: (): boolean => FormConfig.submitting === true,
              iconifyIcon: 'mdi:ellipse-outline',
              click: () => initDraw('circle')
            },
            {
              perm: true,
              text: '',
              type: 'default',
              size: 'large',
              title: '清除图形',
              disabled: (): boolean => FormConfig.submitting === true,
              iconifyIcon: 'ep:delete',
              click: () => clearDraw()
            }
          ]
        }
      ]
    },
    {
      id: 'layerid',
      fieldset: {
        desc: '选择图层'
      },
      fields: [
        {
          type: 'tree',
          options: [],
          // checkStrictly: true,
          showCheckbox: true,
          field: 'layerid',
          rules: [{ required: true, message: '请选择图层' }],
          nodeKey: 'value'
          // handleCheckChange: (data, isChecked) => {
          //   if (isChecked) {
          //     refForm.value && (refForm.value.dataForm.layerid = [data.value])
          //   }
          // }
        }
      ]
    },
    {
      fieldset: {
        desc: '任务描述'
      },
      fields: [
        {
          type: 'input',
          field: 'taskname',
          label: '名称',
          rules: [{ required: true, message: '请输入名称' }]
        },
        { type: 'textarea', field: 'description', label: '描述' },
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: '导出',
              styles: {
                width: '50%'
              },
              loading: () => FormConfig.submitting === true,
              iconifyIcon: 'ep:download',
              click: () => refForm.value?.Submit()
            },
            {
              styles: {
                width: '50%'
              },
              perm: true,
              text: '重置',
              type: 'default',
              iconifyIcon: 'ep:refresh',
              click: () => {
                clearDraw()
                refForm.value?.resetForm()
              }
            }
          ]
        }
      ]
    }
  ],
  submit: () => startExport()
})
const TableConfig = reactive<ITable>({
  dataList: [],
  columns: [
    { minWidth: 160, label: '任务名称', prop: 'taskname' },
    { minWidth: 160, label: '任务描述', prop: 'description' },
    // { minWidth: 100, label: '创建人', prop: 'createuser' },
    { minWidth: 160, label: '创建时间', prop: 'createtime' },
    { minWidth: 160, label: '结束时间', prop: 'end_time' },
    {
      width: 100,
      align: 'center',
      label: '导出状态',
      prop: 'exp_status',
      tag: true,
      tagColor: row => (row.exp_status === '导出失败' ? '#990000' : row.exp_status === '导出成功' ? '#009900' : '#409eff')
    }
  ],
  // operationWidth: 180,
  operations: [
    {
      perm: (row: any): boolean => row.exp_status === '导出成功',
      text: '下载',
      iconifyIcon: 'ep:download',
      click: row => handleDownload(row)
    },
    {
      perm: (row: any): boolean => row.exp_status !== '导出成功',
      text: '刷新',
      loading: (row: any): boolean => row.exp_status === '导出中',
      iconifyIcon: 'ep:refresh',
      click: row => {
        clearTimeout(state.timer)
        state.refreshCount = 0
        refreshRow(row.task_id)
      }
    },
    {
      perm: false,
      text: '删除',
      type: 'danger',
      disabled: (row: any): boolean => row.del_status === '不可用',
      iconifyIcon: 'ep:delete',
      click: () => handleDelete()
    }
  ],
  pagination: {
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page
      TableConfig.pagination.limit = size
      refreshData()
    }
  }
})
const handleDownload = row => {
  downloadUrl(window.SITE_CONFIG.GIS_CONFIG.gisApi + '/ExportResult/' + row.task_id + '.dwg', row.taskname)
}
const handleDelete = () => {
  SLConfirm('确定删除吗？', '删除提示')
    .then(() => {
      //
    })
    .catch(() => {
      //
    })
}
const SearchConfig = reactive<IFormConfig>({
  labelPosition: 'right',
  group: [
    {
      fields: [
        {
          itemContainerStyle: { marginBottom: '8px' },
          labelWidth: 60,
          type: 'input',
          clearable: false,
          field: 'taskname',
          label: '任务名'
        },
        {
          itemContainerStyle: { marginBottom: '8px' },
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: '搜索',
              click: () => refreshData()
            }
          ]
        }
      ]
    }
  ]
})
const refreshData = async () => {
  const res = await ListExportMapTasks({
    pageindex: TableConfig.pagination.page || 1,
    pagesize: TableConfig.pagination.limit || 20,
    exporttype: '0',
    taskname: refSearch.value?.dataForm.taskname
  })
  TableConfig.dataList = res.data.result?.rows || []
  TableConfig.pagination.total = res.data.result?.total || 0
}
const refreshRow = async task_id => {
  if (!task_id) return
  const row = TableConfig.dataList.find(item => item.task_id === task_id)
  try {
    const res = await ListExportMapTasks({
      pageindex: 1,
      pagesize: 1,
      exporttype: '0',
      taskid: row.task_id
    })
    if (res.data.result.rows.length) {
      const resRow = res.data.result.rows[0]
      row.exp_status = resRow.exp_status
      row.end_time = resRow.end_time
      row.del_status = resRow.del_status
      if (row.exp_status === '导出中') {
        state.timer = setTimeout(() => {
          refreshRow(task_id)
        }, 1000)
      } else if (row.exp_status === '未开始') {
        if (state.refreshCount < 10) {
          state.timer = setTimeout(() => {
            state.refreshCount++
            refreshRow(task_id)
          }, 1000)
        }
      }
    }
    return row
  } catch (error) {
    row.exp_status = '导出失败'
    return row
  }
}
const clearDraw = () => {
  staticState.graphicsLayer?.removeAll()
  staticState.graphic = undefined
  staticState.sketch?.cancel()
}
const initDraw = (type: any) => {
  clearDraw()
  staticState.sketch?.create(type)
}
const startExport = async () => {
  if (!staticState.graphic) {
    SLMessage.warning('请先绘制图形')
    return
  }
  const dataForm = refForm.value?.dataForm || {}
  if (!dataForm.layerid?.length) {
    SLMessage.warning('请先选择图层')
    return
  }
  FormConfig.submitting = true
  try {
    const res = await ExportMapData({
      exporttype: 0,
      geomjson: JSON.stringify(staticState.graphic.geometry.toJSON()),
      taskname: dataForm.taskname,
      description: dataForm.description,
      outputlayers: dataForm.layerid
        ?.map(item => {
          return state.layerInfos.find(o => o.layerid === item)?.layername
        })
        .filter(item => item)
        .join(',')
    })
    if (res.data.code === 10000) {
      console.log(res.data.message)

      SLMessage.success('导出成功')
      refSearch.value && (refSearch.value.dataForm.taskname = dataForm.taskname)
      await refreshData()
      clearTimeout(state.timer)
      state.refreshCount = 0
      refreshRow(res.data.result)
    } else {
      SLMessage.error(res.data.message)
    }
  } catch (error) {
    SLMessage.error('导出失败')
  }
  FormConfig.submitting = false
}
const initSketch = () => {
  staticState.graphicsLayer = getGraphicLayer(staticState.view, {
    id: 'export-map',
    title: '导出CAD'
  })
  staticState.sketch = new SketchViewModel({
    view: staticState.view,
    layer: staticState.graphicsLayer,
    polygonSymbol: setSymbol('polygon') as any,
    pointSymbol: setSymbol('point') as any,
    polylineSymbol: setSymbol('polyline') as any
  })
  staticState.sketchCreateHandler = staticState.sketch?.on('create', (result: any) => {
    if (result.state === 'complete') {
      staticState.graphic = result.graphic
    }
  })
  staticState.sketchUpdateHandler = staticState.sketch?.on('update', (result: any) => {
    if (result.state === 'complete') {
      staticState.graphic = result.graphics[0]
    }
  })
}

const getLayerInfo = async () => {
  state.layerIds = getSubLayerIds(staticState.view)
  const layerInfo = await queryLayerClassName(state.layerIds)
  state.layerInfos = layerInfo.data?.result?.rows || []
  const field = FormConfig.group.find(item => item.id === 'layerid')?.fields[0] as IFormTree
  const points = state.layerInfos
    .filter(item => item.geometrytype === 'esriGeometryPoint')
    .map(item => {
      return {
        label: item.layername,
        value: item.layerid,
        data: item
      }
    })
  const lines = state.layerInfos
    .filter(item => item.geometrytype === 'esriGeometryPolyline')
    .map(item => {
      return {
        label: item.layername,
        value: item.layerid,
        data: item
      }
    })
  field
    && (field.options = [
      { label: '管点类', value: -1, children: points },
      { label: '管线类', value: -2, children: lines }
    ])
  refForm.value && (refForm.value.dataForm.layerid = state.layerIds || [])
}
const onMaploaded = (view: __esri.MapView) => {
  staticState.view = view
  getLayerInfo()
  initSketch()
  refMap.value?.toggleCustomDetail(true)
}
onMounted(() => {
  refreshData()
})
onBeforeUnmount(() => {
  staticState.sketchCreateHandler?.remove()
  staticState.sketchUpdateHandler?.remove()
  clearDraw()
  staticState.sketch?.destroy()
})
</script>
<style lang="scss" scoped>
.table-box {
  height: calc(100% - 40px);
}
</style>
