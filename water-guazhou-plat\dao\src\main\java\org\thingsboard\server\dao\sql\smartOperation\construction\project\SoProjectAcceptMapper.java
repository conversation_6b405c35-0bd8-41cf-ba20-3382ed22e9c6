package org.thingsboard.server.dao.sql.smartOperation.construction.project;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoProjectAccept;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoProjectAcceptPageRequest;

@Mapper
public interface SoProjectAcceptMapper extends BaseMapper<SoProjectAccept> {
    IPage<SoProjectAccept> findByPage(SoProjectAcceptPageRequest request);

    boolean update(SoProjectAccept entity);

    boolean save(SoProjectAccept entity);

    boolean updateFully(SoProjectAccept entity);

    String getIdByProjectCodeAndTenantId(@Param("projectCode") String projectCode, @Param("tenantId") String tenantId);


}
