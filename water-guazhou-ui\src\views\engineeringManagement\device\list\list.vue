<!-- 工程管理-设备管理-设备清单 -->
<template>
  <div class="wrapper">
    <CardSearch ref="refSearch" :config="cardSearchConfig" />
    <CardTable :config="TableConfig" class="card-table"></CardTable>
  </div>
</template>

<script lang="ts" setup>
import { Refresh } from '@element-plus/icons-vue';
import { ICONS } from '@/common/constans/common';
import { getDeviceItem } from '@/api/engineeringManagement/device';

const refSearch = ref<ICardSearchIns>();

const cardSearchConfig = ref<ISearch>({
  filters: [
    { label: '设备编码', field: 'serialId', type: 'input' },
    { label: '设备名称', field: 'name', type: 'input' }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          type: 'default',
          perm: true,
          text: '重置',
          svgIcon: shallowRef(Refresh),
          click: () => {
            refSearch.value?.resetForm();
            refreshData();
          }
        },
        {
          perm: true,
          text: '查询',
          icon: ICONS.QUERY,
          click: () => refreshData()
        }
      ]
    }
  ]
});

const TableConfig = reactive<ICardTable>({
  defaultExpandAll: true,
  indexVisible: true,
  columns: [
    { label: '设备编码', prop: 'serialId' },
    { label: '设备名称', prop: 'deviceName' },
    { label: '所属大类', prop: 'deviceTopTypeName' },
    { label: '所属类别', prop: 'deviceType' },
    { label: '计量单位', prop: 'unit' },
    { label: '申请数量', prop: 'amount' },
    { label: '项目编号', prop: 'projectCode' },
    { label: '工程编号', prop: 'constructionCode' },
    { label: '合同编号', prop: 'contractCode' },
    { label: '实施编号', prop: 'applyCode' }
  ],
  dataList: [],
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page;
      TableConfig.pagination.limit = size;
      refreshData();
    }
  }
});

const refreshData = async () => {
  const params = {
    size: TableConfig.pagination.limit || 20,
    page: TableConfig.pagination.page || 1,
    ...(refSearch.value?.queryParams || {})
  };
  getDeviceItem(params).then((res) => {
    TableConfig.dataList = res.data.data.data || [];
    TableConfig.pagination.total = res.data.data.total || 0;
  });
};

onMounted(() => {
  refreshData();
});
</script>
