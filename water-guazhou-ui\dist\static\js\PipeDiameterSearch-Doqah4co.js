import{d as I,c as y,r as v,b as d,bB as _,o as D,g as T,n as C,q as b,i as l,_ as E,C as L}from"./index-r0dFAfgr.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./GraphicsLayer-DTrBRwJQ.js";import{e as S,i as h}from"./QueryHelper-ILO3qZqg.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import k from"./PipeDetail-CTBPYFJW.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./project-DUuzYgGl.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./executeForIds-BLdIsxvI.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./Search-NSrhrIa_.js";import"./FormTableColumnFilter-BT7pLXIC.js";import"./fieldconfig-Bk3o1wi7.js";import"./DateFormatter-Bm9a68Ax.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./LayerHelper-Cn-iiqxI.js";import"./pipe-nogVzCHG.js";import"./config-fy91bijz.js";const x=I({__name:"PipeDiameterSearch",props:{view:{},telport:{}},setup(g){const n=g,m=y(),s=y(),e=v({pipeLayerOption:[],curOperate:"",tabs:[],calcType:"="}),u=v({group:[{fieldset:{desc:"图层名称"},fields:[{type:"select",field:"layer",options:[]}]},{fieldset:{desc:"管径"},fields:[{type:"input-number",field:"DIAMETER",prependDefault:"=",prepend:{type:"select",field:"calcType",style:{width:"60px"},options:[{label:"=",value:"="},{label:"<",value:"<"},{label:">",value:">"}],onChange:t=>{e.calcType=t}},append:"mm"},{type:"btn-group",btns:[{perm:!0,loading:()=>e.curOperate==="detailing",text:()=>e.curOperate==="detailing"?"正在查询":"查询",click:()=>w(),styles:{width:"100%"}}]}]}],labelPosition:"top",gutter:12}),O=()=>{var p,a,i,o;if(!n.view)return;const t=(p=n.view)==null?void 0:p.map.findLayerById("pipelayer");e.pipeLayerOption=[],(a=t==null?void 0:t.sublayers)==null||a.map(c=>{var f;(f=e.pipeLayerOption)==null||f.push({label:c.title,value:c.title,id:c.id})});const r=u.group[0].fields[0];r&&(r.options=e.pipeLayerOption),(i=m.value)!=null&&i.dataForm&&(m.value.dataForm.layer=e.pipeLayerOption&&((o=e.pipeLayerOption[0])==null?void 0:o.value))},w=async()=>{var p,a;const t=e.pipeLayerOption.find(i=>{var o;return i.label===((o=m.value)==null?void 0:o.dataForm.layer)});if(!t){d.warning("请选择图层"),e.curOperate="";return}const r=(a=(p=m.value)==null?void 0:p.dataForm)==null?void 0:a.DIAMETER;try{e.curOperate="detailing";const i=await S(window.SITE_CONFIG.GIS_CONFIG.gisService+window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService+"/"+t.id,h({returnGeometry:!1,where:" DIAMETER"+e.calcType+(r||0)+" ",orderByFields:["OBJECTID asc"]}));i!=null&&i.length?(e.tabs=[{label:t.label,name:t.label,id:t.id,data:i}],_(()=>{var o;(o=s.value)==null||o.openDialog()})):(d.info("查询结果为空"),e.curOperate="",e.tabs=[])}catch{d.error("查询失败，请检查查询条件是否正确"),e.curOperate=""}},F=async()=>{var t;n.view&&((t=s.value)==null||t.extentTo(n.view))};return D(()=>{O()}),(t,r)=>{const p=E;return T(),C("div",null,[b(p,{ref_key:"refForm",ref:m,config:l(u)},null,8,["config"]),b(k,{ref_key:"refDetail",ref:s,tabs:l(e).tabs,telport:t.telport,onRefreshed:r[0]||(r[0]=()=>l(e).curOperate="viewingDetail"),onRefreshing:r[1]||(r[1]=()=>l(e).curOperate="detailing"),onClose:r[2]||(r[2]=a=>l(e).curOperate=""),onRowdblclick:F},null,8,["tabs","telport"])])}}}),tt=L(x,[["__scopeId","data-v-b912d0a1"]]);export{tt as default};
