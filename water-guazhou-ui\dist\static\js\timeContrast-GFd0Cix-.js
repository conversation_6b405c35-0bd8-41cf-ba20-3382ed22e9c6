import{_ as z}from"./index-C9hz-UZb.js";import{d as A,M as H,a6 as P,c as f,r as x,am as R,bF as p,s as I,o as j,ay as M,g as k,n as O,q as i,i as s,F as y,cs as N,h as G,an as L,j as W,bB as U,dF as $,dA as J,aq as K,al as Q,aj as X,C as Y}from"./index-r0dFAfgr.js";import{_ as Z}from"./Search-NSrhrIa_.js";import{l as ee}from"./echart-DxEZmJvB.js";import{c as ae}from"./flowMonitoring-DtJlPj0G.js";const te={class:"view"},oe=A({__name:"timeContrast",props:{stationName:{},stationId:{}},setup(S){const{$messageWarning:V}=H(),B=P(),b=f(),q=f(!1),m=S,C=f(),T=f(),w=f(),o=x({chartOption:null,tableDataList:[],activeName:"echarts"});R(()=>m.stationId,()=>{console.log(m.stationId),v()});const E=x({defaultParams:{day:[p().add(-7,"day").format(),p().format()],month:[p().format(),p().format()],queryType:"day"},filters:[{type:"select",label:"统计类型",field:"queryType",width:"140px",options:[{label:"日分时",value:"day"},{label:"月份日",value:"month"}]},{type:"daterange",label:"日期",field:"day",width:"300px",clearable:!1,handleHidden:(e,r,t)=>{t.hidden=e.queryType==="month"}},{type:"monthrange",label:"日期",field:"month",width:"300px",clearable:!1,handleHidden:(e,r,t)=>{t.hidden=e.queryType==="day"}},{type:"btn-group",btns:[{perm:!0,text:"查询",svgIcon:I(Q),click:()=>{m.stationId?v():V("请选择监测点")}},{perm:!0,text:"导出",type:"warning",svgIcon:I(X),hide:()=>o.activeName!=="list",click:()=>{var e;(e=w.value)==null||e.exportTable()}}]}]}),h=x({loading:!1,dataList:[],columns:[],operations:[],showSummary:!1,operationWidth:"150px",pagination:{hide:!0}}),F=()=>{var t;const e=ee();e.series=[],h.columns.filter(a=>a.prop!=="ts").map(a=>{const _=o.tableDataList.map(d=>a.prop.indexOf("Ts")!==-1?p(d[a.prop]).format("HH:00"):d[a.prop]),c={name:a.label,smooth:!0,data:_,type:"line",markPoint:{data:[{type:"max",name:"最大值"},{type:"min",name:"最小值"}]},markLine:{data:[{type:"average",name:"平均值"}]}};e.series.push(c),e.name="流量数据(m³)"}),e.yAxis[0].name="流量(m³)",e.xAxis.data=o.tableDataList.map(a=>a.ts),(t=b.value)==null||t.clear(),U(()=>{B.listenTo(T.value,()=>{var a;o.chartOption=e,(a=b.value)==null||a.resize()})})},v=async()=>{var g,D;q.value=!0;const e=((g=C.value)==null?void 0:g.queryParams)||{},[r,t]=e.queryType==="day"?e.day:e.month,a={start:r?p(r).startOf(e.queryType).valueOf():null,end:t?p(t).endOf(e.queryType).valueOf():null,stationId:m.stationId,queryType:e.queryType},c=(D=(await ae(a)).data)==null?void 0:D.data,d=[{prop:"ts",label:"时间",width:"130px"}],l=[];for(const u in c){d.push({prop:u,label:u,unit:"(m³)"});for(const n in c[u])console.log(n),l[n]?l[n].ts=e.queryType==="day"?n+"时":n+"日":l.push({ts:e.queryType==="day"?n+"时":n+"日"}),l[n][u]=c[u][n].value}h.columns=d,h.dataList=l,o.tableDataList=l,F(),q.value=!1};return j(()=>{m.stationId&&v()}),(e,r)=>{const t=Z,a=$,_=J,c=K,d=M("VChart"),l=z;return k(),O("div",te,[i(t,{ref_key:"cardSearch",ref:C,config:s(E)},null,8,["config"]),i(l,{class:"card-table",title:" "},{right:y(()=>[i(_,{modelValue:s(o).activeName,"onUpdate:modelValue":r[0]||(r[0]=g=>s(o).activeName=g)},{default:y(()=>[i(a,{label:"echarts"},{default:y(()=>[i(s(N),{style:{"margin-right":"1px","font-size":"16px"},icon:"clarity:line-chart-line"})]),_:1}),i(a,{label:"list"},{default:y(()=>[i(s(N),{style:{"margin-right":"1px","font-size":"16px"},icon:"material-symbols:table"})]),_:1})]),_:1},8,["modelValue"])]),default:y(()=>[s(o).activeName==="list"?(k(),G(c,{key:0,ref_key:"refCard",ref:w,config:s(h),class:"table-box"},null,8,["config"])):L("",!0),s(o).activeName==="echarts"?(k(),O("div",{key:1,ref_key:"agriEcoDev",ref:T,class:"card-ehcarts"},[i(d,{ref_key:"refChart",ref:b,theme:s(W)().isDark?"dark":"",class:"card-ehcarts",option:s(o).chartOption},null,8,["theme","option"])],512)):L("",!0)]),_:1})])}}}),ce=Y(oe,[["__scopeId","data-v-f964c7c2"]]);export{ce as default};
