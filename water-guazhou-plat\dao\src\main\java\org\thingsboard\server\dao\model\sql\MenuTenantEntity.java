/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.model.sql;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.common.data.id.MenuPoolId;
import org.thingsboard.server.common.data.id.MenuTenantId;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.menu.MenuTenant;
import org.thingsboard.server.dao.model.BaseSqlEntity;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.MENU_TENANT_NAME)
public class MenuTenantEntity extends BaseSqlEntity<MenuTenant> {

    @Column(name = ModelConstants.MENU_TENANT_TENANT_ID)
    private String tenantId;

    @Column(name = ModelConstants.MENU_TENANT_MENU_POOL_ID)
    private String menuPoolId;

    @Column(name = ModelConstants.MENU_TENANT_IS_EXTENSION_MENU)
    private String isExtensionMenu;


    public MenuTenantEntity() {
    }

    public MenuTenantEntity(MenuTenant menuTenant) {
        if (menuTenant.getId() != null) {
            this.setId(menuTenant.getId().getId());
        }
        if (menuTenant.getMenuPoolId() != null) {
            this.menuPoolId = toString(menuTenant.getMenuPoolId().getId());
        }
        if (menuTenant.getTenantId() != null) {
            this.tenantId = toString(menuTenant.getTenantId().getId());
        }
        if (menuTenant.getIsExtensionMenu() != null) {
            this.isExtensionMenu = menuTenant.getIsExtensionMenu();
        }
    }


    @Override
    public MenuTenant toData() {
        MenuTenant menuTenant = new MenuTenant(new MenuTenantId(getId()));
        menuTenant.setMenuPoolId(new MenuPoolId(toUUID(menuPoolId)));
        menuTenant.setTenantId(new TenantId(toUUID(tenantId)));
        menuTenant.setIsExtensionMenu(isExtensionMenu);

        return menuTenant;
    }
}
