<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.fault.FaultSolutionMapper">

    <select id="getListByMainId" resultType="org.thingsboard.server.dao.model.sql.fault.FaultSolution">

        select a.*, b.first_name as creatorName, c.remark mainName
        from tb_device_fault_solution a
        left join tb_user b on a.creator = b.id
        left join tb_device_fault_info c on a.main_id = c.id
        where a.name like '%' || #{name} || '%'
        and a.main_id = #{mainId}
        and a.tenant_id = #{tenantId}
        order by a.create_time desc
    </select>

</mapper>