<template>
  <div class="wrapper">
    <CardSearch
      ref="refSearch"
      :config="SearchConfig"
    ></CardSearch>
    <CardTable
      class="card-table"
      :config="TableConfig"
    ></CardTable>
  </div>
</template>

<script setup>
import { onMounted, reactive, ref } from 'vue'
import { getOptionLogList } from '@/api/platformManagement/optionLog'
import { SLConfirm, SLMessage } from '@/utils/Message'

const refSearch = ref()

const SearchConfig = reactive({
  labelWidth: '100px',
  filters: [
    { 
      type: 'input', 
      label: '名称', 
      field: 'firstName', 
      placeholder: '请输入名称',
      onChange: () => refreshData() 
    },
    {
      type: 'btn-group',
      btns: [
        { type: 'primary', perm: true, text: '查询', click: () => refreshData() }
      ]
    }
  ]
})

const TableConfig = reactive({
  columns: [
    { label: 'ID', prop: 'id' },
    { label: '用户ID', prop: 'userId' },
    { label: '租户ID', prop: 'tenantId' },
    { label: '名称', prop: 'firstName' },
    { label: '权限', prop: 'authority' },
    { label: '当前执行的操作', prop: 'options' },
    { 
      label: '创建时间', 
      prop: 'createTime',
      render: (row) => {
        return new Date(row.createTime).toLocaleString()
      }
    },
    { label: '扩展信息', prop: 'additionalInfo' },
    { label: '日志类型', prop: 'type' },
    { label: '操作具体信息', prop: 'info' }
  ],
  dataList: [],
  pagination: {
    total: 0,
    page: 1,
    align: 'right',
    limit: 20,
    handlePage: (page) => {
      TableConfig.pagination.page = page
      refreshData()
    },
    handleSize: (size) => {
      TableConfig.pagination.limit = size
      refreshData()
    }
  },
  handleSelectChange: (rows) => {
    TableConfig.selectList = rows || []
  }
})

// 刷新数据
const refreshData = async () => {
  try {
    const res = await getOptionLogList({
      page: TableConfig.pagination.page,
      size: TableConfig.pagination.limit,
      ...(refSearch.value?.queryParams || {})
    })
    const responseData = res.data?.data || res
    TableConfig.dataList = responseData.records || responseData
    TableConfig.pagination.total = responseData.total || responseData.length || 0
  } catch (error) {
    SLMessage.error('数据加载失败')
  }
}

onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.wrapper {
  padding: 20px;
}

.card-table {
  margin-top: 20px;
}
</style>