package org.thingsboard.server.dao.util.imodel.query.store;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.store.StoreOutRecordDetail;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;

@Getter
@Setter
public class StoreOutRecordDetailPageRequest extends AdvancedPageableQueryEntity<StoreOutRecordDetail, StoreOutRecordDetailPageRequest> {
    // 出库单主表ID
    private String mainId;
}
