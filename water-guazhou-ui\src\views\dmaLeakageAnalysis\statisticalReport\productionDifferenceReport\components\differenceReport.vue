<!-- 产销差报表 -->
<template>
  <div class="">
    <Search ref="refSearch" :config="SearchConfig" class="search" />
    <CardTable ref="refTable" class="card-table" :config="TableConfig" />
  </div>
</template>
<script lang="ts" setup>
import { ExportDmaPSReport, GetDmaPSReport } from '@/api/mapservice/dma';
import { formatterMonth, formatterYear } from '@/utils/GlobalHelper';
import { ExportReport } from '@/views/yinshou/baobiao';

defineProps<{ partitions: any[] }>();
const refTable = ref<ICardTableIns>();
const refSearch = ref<ISearchIns>();
const handleHidden = (params, query, config) => {
  config.hidden = params.type !== config.field;
};
// 搜索栏初始化配置
const SearchConfig = reactive<ISearch>({
  defaultParams: {
    type: 'year',
    year: moment().format(formatterYear),
    month: [moment().format(formatterMonth), moment().format(formatterMonth)],
    yearInterval: [
      moment().format(formatterYear),
      moment().format(formatterYear)
    ]
  },
  filters: [
    {
      type: 'radio-button',
      field: 'type',
      options: [
        { label: '按年', value: 'year' },
        { label: '按年月', value: 'month' },
        { label: '按年区间', value: 'yearInterval' }
      ],
      label: '选择方式',
      clearable: false
    },
    {
      handleHidden,
      type: 'year',
      label: '',
      field: 'year',
      clearable: false,
      disabledDate(date) {
        return new Date() < date;
      }
    },
    {
      handleHidden,
      type: 'monthrange',
      label: '',
      field: 'month',
      clearable: false,
      format: formatterMonth,
      disabledDate(date) {
        return new Date() < date;
      }
    },
    {
      handleHidden,
      type: 'yearrange',
      label: '',
      field: 'yearInterval',
      clearable: false,
      disabledDate(date) {
        return new Date() < date;
      }
    },
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          iconifyIcon: 'ep:search',
          disabled: () => !!TableConfig.loading,
          click: () => refreshData()
        },
        {
          perm: true,
          text: '重置',
          type: 'default',
          iconifyIcon: 'ep:refresh',
          click: () => {
            refSearch.value?.resetForm();
          }
        },
        {
          perm: true,
          text: '导出',
          type: 'primary',
          iconifyIcon: 'ep:download',
          disabled: () => !!TableConfig.loading,
          click: () => {
            refreshData(true);
          }
        }
      ]
    }
  ]
});

// 初始化列表配置数据
const TableConfig = reactive<ITable>({
  dataList: [],
  columns: [
    {
      prop: 'name',
      label: moment().format(formatterYear) + '年产销差报表',
      align: 'center',
      subColumns: [
        { prop: 'name', label: '分区名称' },
        {
          prop: 'yearRate',
          label: '年产销差(%)'
        }
      ]
    }
  ],
  pagination: {
    hide: true,
    refreshData({ page, size }) {
      TableConfig.pagination.page = page;
      TableConfig.pagination.limit = size;
      refreshData();
    }
  }
});

const refreshData = async (isExport?: boolean) => {
  try {
    TableConfig.loading = true;
    const query = refSearch.value?.queryParams || {};
    const params = {
      type: query.type,
      date: query.type === 'year' ? query.year : undefined,
      start:
        query.type === 'month'
          ? query.month?.[0]
          : query.type === 'yearInterval'
            ? query.yearInterval?.[0]
            : undefined,
      end:
        query.type === 'month'
          ? query.month?.[1]
          : query.type === 'yearInterval'
            ? query.yearInterval?.[1]
            : undefined
    };
    const res = await GetDmaPSReport(params);
    const data = res.data.data || {};
    const subColumns = data.header || [];
    const list = data.data || [];
    TableConfig.dataList = list.map((item) => {
      const obj: any = {};
      item.data.map((o, i) => {
        obj[i] = o;
      });
      obj.name = item.name;
      return obj;
    });
    TableConfig.columns = [
      {
        prop: 'name',
        label:
          (query.type === 'year'
            ? params.date
            : (params.start ?? '--') + ' ~ ' + (params.end ?? '--')) +
          '产销差报表',
        align: 'center',
        subColumns: [
          { prop: 'name', label: '分区名称' },
          ...subColumns.map((item, i) => {
            return {
              minWidth: 120,
              label: item,
              prop: i.toString()
            };
          })
        ]
      }
    ];
    if (isExport) {
      const res = await ExportDmaPSReport(params);
      ExportReport(res.data, '产销差报表');
    }
  } catch (error) {
    //
  }
  TableConfig.loading = false;
};
onMounted(() => {
  refreshData();
});
</script>
<style lang="scss" scoped>
.card-table {
  height: calc(100vh - 220px);
}
.search {
  margin-bottom: 10px;
}
</style>
