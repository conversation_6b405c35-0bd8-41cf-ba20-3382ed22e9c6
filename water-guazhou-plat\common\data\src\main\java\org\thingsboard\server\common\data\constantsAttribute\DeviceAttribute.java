/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.common.data.constantsAttribute;

import lombok.Data;

@Data
public class DeviceAttribute {

    private String energyId;
    //从站地址
    private String unitId;
    //modbus波特率
    private String baudRate;
    //modbus数据位
    private String dataBits;
    //可选项none,even,odd
    private String parity;
    //停止位
    private String stopBits;
    //控制流
    private String flowCtrl;
    //超时时间
    private String timeout;
    //modbusType
    private String modbusType = "rtu";

    private String pt1;
    private String pt2;
    private String ct1;
    private String ct2;
    private String ctCount;
    private String ptCount;

    private boolean useRtuTcp;

    private String pollPeriod;

    //仪表型号
    private String model;
    //名称
    private String name;
    //计量等级
    private String measurementLevel;
    //生产厂家
    private String mfr;
    //安装地址
    private String installationLocation;
    //安装机柜
    private String installationCabinet;
    //启用谐波检测串口
    private boolean harmonicFlag;
    //测试对象-电力压力
    private String objVoltage;
    //测试对象-数量
    private String objCount;
    //测试对象-额定容量
    private String objVolume;

    private Boolean singleRead;
    private Integer deviceReadRegisterNumberByOnce;

    private String protocol;
    private String delay;
    private String portName;
    private String port;
    private String host = "";
    private String protocolType;
    private String defaultPropCategory;

    //硬件接入类型 485/232 仅作展示
    private String accessType;
}
