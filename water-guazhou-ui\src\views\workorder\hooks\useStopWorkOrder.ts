import { SLConfirm, SLMessage } from '@/utils/Message'

const useStopWorkOrder = (submit:(params)=>any) => {
  const FormConfig_Stop = reactive<IDrawerConfig>({
    width: 500,
    labelPosition: 'top',
    title: '终止',
    group: [
      {
        fields: [
          {
            type: 'textarea',
            label: '终止原因：',
            field: 'processRemark',
            rules: [{ required: true, message: '请输入终止原因' }]
          }
        ]
      }
    ],
    submit: (params: any) => {
      SLConfirm('确定终止？', '提示信息')
        .then(async () => {
          FormConfig_Stop.submitting = true
          try {
            await submit(params)
          } catch (error) {
            SLMessage.error('系统错误')
          }
          FormConfig_Stop.submitting = false
        })
        .catch(() => {
        //
        })
    },
    defaultValue: {
      processAdditionalInfo: undefined
    }
  })
  return {
    FormConfig_Stop
  }
}
export default useStopWorkOrder
