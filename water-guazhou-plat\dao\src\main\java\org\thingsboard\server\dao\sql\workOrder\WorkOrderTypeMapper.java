package org.thingsboard.server.dao.sql.workOrder;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrderType;

import java.util.List;

@Mapper
public interface WorkOrderTypeMapper extends BaseMapper<WorkOrderType> {
    List<WorkOrderType> findList(@Param("status") String status, @Param("tenantId") String tenantId);

    List<WorkOrderType> findByResourceId(@Param("resourceId") String resourceId);
}
