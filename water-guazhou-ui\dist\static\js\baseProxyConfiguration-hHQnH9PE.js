import{_ as D}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as S}from"./CardTable-rdWOL4_6.js";import{_ as P}from"./CardSearch-CB_HNR-Q.js";import{z as u,C as v,c as h,r as d,b as o,S as T,o as q,g as L,n as B,q as g}from"./index-r0dFAfgr.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";function V(i){return u({url:"/api/base/proxy/configuration/list",method:"get",params:i})}function F(i){return u({url:"/api/base/proxy/configuration/getDetail",method:"get",params:{id:i}})}function _(i){return u({url:"/api/base/proxy/configuration/add",method:"post",data:i})}function x(i){return u({url:"/api/base/proxy/configuration/edit",method:"post",data:i})}function z(i){return u({url:"/api/base/proxy/configuration/deleteIds",method:"delete",data:i})}const A={class:"wrapper"},E={__name:"baseProxyConfiguration",setup(i){const f=h(),c=h(),C=d({labelWidth:"100px",filters:[{type:"input",label:"配置名称",field:"configName",placeholder:"请输入配置名称",onChange:()=>n()},{type:"btn-group",btns:[{type:"primary",perm:!0,text:"查询",click:()=>n()},{perm:!0,type:"primary",text:"新增",click:()=>y()},{perm:!0,type:"danger",text:"批量删除",click:()=>b()}]}],defaultParams:{}}),l=d({columns:[{label:"配置文件信息",prop:"proxyConfig"},{label:"版本信息",prop:"version"},{label:"状态",prop:"status"},{label:"处理进程数",prop:"workerProcesses"},{label:"最大并发链接数",prop:"workerConnections"},{label:"连接超时时间",prop:"keepAliveTimeout"},{label:"日志状态",prop:"logStatus"},{label:"缓存状态",prop:"cacheStatus"}],dataList:[],operations:[{perm:!0,type:"primary",isTextBtn:!0,text:"查看详情",click:e=>w(e)},{perm:!0,type:"primary",isTextBtn:!0,text:"编辑",click:e=>y(e)},{perm:!0,type:"danger",isTextBtn:!0,text:"删除",click:e=>b(e)}],pagination:{total:0,page:1,align:"right",limit:20,handlePage:e=>{l.pagination.page=e,n()},handleSize:e=>{l.pagination.limit=e,n()}},handleSelectChange:e=>{l.selectList=e||[]}}),a=d({title:"新增代理配置",group:[{fields:[{type:"input",label:"配置文件信息",field:"proxyConfig",rules:[{required:!0,message:"请输入配置文件信息"}]},{type:"input",label:"版本信息",field:"version",rules:[{required:!0,message:"请输入版本信息"}]},{type:"input",label:"状态",field:"status",rules:[{required:!0,message:"请输入状态"}]},{type:"input",label:"处理进程数",field:"workerProcesses",rules:[{required:!0,message:"请输入处理进程数"}]},{type:"input",label:"最大并发链接数",field:"workerConnections",rules:[{required:!0,message:"请输入最大并发链接数"}]},{type:"input",label:"连接超时时间",field:"keepAliveTimeout",rules:[{required:!0,message:"请输入连接超时时间"}]},{type:"input",label:"日志状态",field:"logStatus",rules:[{required:!0,message:"请输入日志状态"}]},{type:"input",label:"缓存状态",field:"cacheStatus",rules:[{required:!0,message:"请输入缓存状态"}]}]}],labelPosition:"top",defaultValue:{},dialogWidth:600,draggable:!0,showSubmit:!0,showCancel:!0,cancelText:"取消",submitText:"确定",submit:async e=>{var t;try{e.id?(await x(e),o.success("修改成功")):(await _(e),o.success("新增成功")),(t=c.value)==null||t.closeDialog(),n()}catch{o.error("操作失败")}}}),m=()=>{a.group[0].fields.forEach(e=>{e.disabled=!1,e.readonly=!1}),a.showSubmit=!0,a.showCancel=!0,a.cancelText="取消",a.submitText="确定",a.submit=async e=>{var t;try{e.id?(await x(e),o.success("修改成功")):(await _(e),o.success("新增成功")),(t=c.value)==null||t.closeDialog(),n()}catch{o.error("操作失败")}}},w=async e=>{var t,r;try{const s=await F(e.id),p=((t=s.data)==null?void 0:t.data)||s;m(),a.title="代理配置详情",a.defaultValue={...p},a.group[0].fields.forEach(k=>{k.disabled=!0}),a.showSubmit=!1,a.cancelText="关闭",(r=c.value)==null||r.openDialog()}catch{o.error("获取详情失败")}},y=e=>{var t;m(),e?(a.title="编辑代理配置",a.defaultValue={...e}):(a.title="新增代理配置",a.defaultValue={}),(t=c.value)==null||t.openDialog()},b=async e=>{try{const t=e?[e.id]:l.selectList.map(r=>r.id);if(!t.length){o.warning("请选择要删除的数据");return}await T("确定要删除选中的数据吗？"),await z(t),o.success("删除成功"),n()}catch(t){t!=="cancel"&&o.error("删除失败")}},n=async()=>{var e,t;try{const r=await V({page:l.pagination.page,size:l.pagination.limit,...((e=f.value)==null?void 0:e.queryParams)||{}}),s=((t=r.data)==null?void 0:t.data)||r;l.dataList=s.records||s,l.pagination.total=s.total||s.length||0}catch{o.error("数据加载失败")}};return q(()=>{n()}),(e,t)=>{const r=P,s=S,p=D;return L(),B("div",A,[g(r,{ref_key:"refSearch",ref:f,config:C},null,8,["config"]),g(s,{class:"card-table",config:l},null,8,["config"]),g(p,{ref_key:"refDialogForm",ref:c,config:a},null,8,["config"])])}}},G=v(E,[["__scopeId","data-v-30c43ac3"]]);export{G as default};
