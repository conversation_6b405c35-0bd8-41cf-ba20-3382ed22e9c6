import{d as M,M as F,c as n,a8 as d,s as R,r as u,x as h,dQ as Y,bQ as z,o as P,g as W,n as U,q as c,i as m,b6 as w,b7 as Q}from"./index-r0dFAfgr.js";import{_ as A}from"./CardTable-rdWOL4_6.js";import{_ as B}from"./CardSearch-CB_HNR-Q.js";import{I as y}from"./common-CvK_P_ao.js";import{h as J,a as $}from"./ledgerManagement-CkhtRd8m.js";import{i as j,c as _,g as G,d as X}from"./equipmentOutStock-BiNkB8x8.js";import{f as L}from"./DateFormatter-Bm9a68Ax.js";import{e as H,f as C}from"./equipmentAssetsData-B4Olvyjd.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";import"./xlsx-rVJkW9yq.js";const K={class:"wrapper"},ue=M({__name:"index",setup(Z){const{$btnPerms:v}=F(),r=n(),x=n(),b=n(),D=n(),f=n([]),O=n({filters:[{label:"出库单编码",field:"code",type:"input",labelWidth:"90px"},{label:"出库单标题",field:"title",type:"input",labelWidth:"90px"},{label:"仓库名称",field:"storehouseId",type:"select",options:d(()=>l.storeList)},{label:"出库时间",field:"outtime",type:"daterange"},{label:"创建时间",field:"createtime",type:"daterange"},{type:"select",label:"出库状态",field:"isOut",options:H,onChange:()=>s()},{type:"select",label:"出库类型",field:"type",options:C,onChange:()=>s()}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",icon:y.QUERY,click:()=>s()},{type:"default",perm:!0,text:"重置",svgIcon:R(Q),click:()=>{var e;(e=r.value)==null||e.resetForm(),s()}}]}]}),p=u({defaultExpandAll:!0,indexVisible:!0,columns:[{label:"出库单标题",prop:"title"},{label:"出库单编码",prop:"code"},{label:"仓库名称",prop:"storehouseName"},{label:"出库时间",prop:"outTime",formatter:e=>L(e.outTime,"YYYY-MM-DD")||""},{label:"领用部门",prop:"managerDepartmentName"},{label:"领用人",prop:"receiveUserName"},{label:"经办人",prop:"managerName"},{label:"创建人",prop:"creatorName"},{label:"创建时间",prop:"createTime",formatter:e=>L(e.createTime,"YYYY-MM-DD")},{label:"出库类型",prop:"type",formatter:e=>{var t;return(t=C.find(a=>a.value===e.type))==null?void 0:t.label}}],operationWidth:"160px",operations:[{type:"success",text:"出库",perm:v("RoleManageEdit"),icon:y.EXPORT,click:e=>q(e)},{type:"primary",color:"#4195f0",text:"详情",perm:v("RoleManageEdit"),icon:y.DETAIL,click:e=>E(e)}],dataList:[],pagination:{page:1,limit:20,total:0,refreshData:({page:e,size:t})=>{p.pagination.page=e,p.pagination.limit=t,s()}}}),k=u({title:"",labelWidth:"100px",width:"1200px",submit:e=>{const t={storeOutId:e.id,checkouts:l.outStock.map(a=>a.id)};j(t).then(()=>{var a;s(),h.success("出库成功"),(a=x.value)==null||a.closeDrawer()}).catch(a=>{h.warning(a)})},defaultValue:{},group:[{fields:[{xl:8,type:"input",label:"出库单编码",field:"code",disabled:!0},{xl:8,type:"input",label:"出库单标题",field:"title",disabled:!0},{xl:8,type:"select",label:"仓库名称",field:"storehouseName",readonly:!0},{xl:8,type:"input",label:"出库时间",field:"outTime",disabled:!0},{xl:8,type:"input",label:"领用人",field:"receiveUserName",disabled:!0},{xl:8,type:"input",label:"领用部门",field:"managerDepartmentName",disabled:!0},{xl:8,type:"input",label:"经办人",field:"managerName",disabled:!0},{xl:8,type:"input",label:"创建人",field:"creatorName",disabled:!0},{xl:8,type:"date",label:"创建时间",field:"createTime",readonly:!0},{xl:18,type:"textarea",label:"备注",field:"remark",disabled:!0},{type:"divider",text:"出库单设备"},{type:"table",field:"drive",config:{indexVisible:!0,height:"350px",dataList:d(()=>l.selectList),columns:[{label:"设备编码",prop:"serialId"},{label:"设备名称",prop:"name"},{label:"规格/型号",prop:"model"},{label:"所属大类",prop:"topType"},{label:"所属类别",prop:"type"},{label:"出库数量",prop:"num"},{label:"计算单位",prop:"unit"}],operations:[{text:"出库",type:"success",icon:y.SEND,perm:v("RoleManageDelete"),click:e=>{var a;const t=Y(l.outStock,"serialId");if(console.log(t.get(e.serialId)),t.get(e.serialId)>=e.num){h.warning("勾选的数量多于或等于规定出库的数量");return}f.value=[],l.getoutDeviceValue({serialId:e.serialId,storeId:l.outboundOrder.storehouseId||"",shelvesId:e.shelvesId||""}),I.defaultValue={serialId:e.serialId,storeId:l.outboundOrder.storehouseId||"",shelvesId:e.shelvesId||"",num:e.num-(t.get(e.serialId)||0)},(a=b.value)==null||a.openDrawer()}}],pagination:{hide:!0}}},{type:"divider",text:"出库设备列表"},{type:"table",field:"outstock",config:{indexVisible:!0,height:"350px",dataList:d(()=>l.outStock),columns:[{label:"标签编码",prop:"deviceLabelCode"},{label:"设备名称",prop:"name"},{label:"设备型号",prop:"model"},{label:"所属大类",prop:"topType"},{label:"所属类别",prop:"type"},{label:"供应商",prop:"supplierName"},{label:"过期时间",prop:"key8"},{label:"仓库编号",prop:"storehouseName"},{label:"仓库名称",prop:"storehouseName"},{label:"货架名称",prop:"shelvesName"}],pagination:{hide:!0}}}]}]}),I=u({title:"设备选择",labelWidth:"130px",submit:(e,t)=>{var a;if(f.value.length>e.num){h.warning("勾选的数量多于规定出库的数量");return}delete e.device,t?l.getoutDeviceValue({...e}):(l.outStock=[...l.outStock,...f.value],l.outStock=z(l.outStock,"id"),(a=b.value)==null||a.closeDrawer())},defaultValue:{},group:[{fields:[{xl:12,type:"input",label:"设备编码",field:"serialId"},{type:"table",field:"device",config:{indexVisible:!0,height:"350px",dataList:d(()=>l.outDeviceValue),selectList:[],handleSelectChange:e=>{f.value=e},titleRight:[{style:{justifyContent:"flex-end"},items:[{type:"btn-group",btns:[{text:"查询",perm:!0,click:()=>{var e;(e=b.value)==null||e.Submit(!0)}}]}]}],columns:[{label:"标签编码",prop:"deviceLabelCode"},{label:"设备名称",prop:"name"},{label:"设备型号",prop:"model"},{label:"所属大类",prop:"topType"},{label:"所属类别",prop:"type"},{label:"供应商",prop:"supplierName"},{label:"仓库名称",prop:"storehouseName"},{label:"仓库名称",prop:"storehouseName"},{label:"货架名称",prop:"shelvesName"},{label:"状态",prop:"isCheckout",formatter:e=>e.isCheckout?"已出库":"未出库"}],pagination:{hide:!0}}}]}]}),S=u({title:"详情",labelWidth:"100px",defaultValue:{},group:[{fields:[{xl:8,type:"input",label:"出库单编码",field:"code",disabled:!0},{xl:8,type:"input",label:"出库单标题",field:"title",disabled:!0},{xl:8,type:"input",label:"仓库名称",field:"storehouseName",disabled:!0},{xl:8,type:"date",label:"出库时间",field:"outTime",readonly:!0},{xl:8,type:"input",label:"领用人",field:"receiveUserName",disabled:!0},{xl:8,type:"input",label:"领用部门",field:"managerDepartmentName",disabled:!0},{xl:8,type:"select",label:"经办人",field:"managerName",readonly:!0},{xl:8,type:"input",label:"创建人",field:"creatorName",disabled:!0},{xl:8,type:"date",label:"创建时间",field:"createTime",readonly:!0},{xl:18,type:"textarea",label:"备注",field:"remark",disabled:!0},{type:"divider",text:"出库单设备"},{type:"table",field:"drive",config:{indexVisible:!0,height:"350px",dataList:d(()=>l.selectList),columns:[{label:"设备编码",prop:"serialId"},{label:"设备名称",prop:"name"},{label:"规格/型号",prop:"model"},{label:"所属大类",prop:"topType"},{label:"所属类别",prop:"type"},{label:"出库数量",prop:"num"},{label:"单位",prop:"unit"}],pagination:{hide:!0}}},{type:"divider",text:"出库设备列表"},{type:"table",field:"outstock",config:{indexVisible:!0,height:"350px",dataList:d(()=>l.outStock),columns:[{label:"标签编码",prop:"deviceLabelCode"},{label:"设备名称",prop:"name"},{label:"设备型号",prop:"model"},{label:"所属大类",prop:"topType"},{label:"所属类别",prop:"type"},{label:"供应商",prop:"supplierName"},{label:"过期时间",prop:"key8"},{label:"仓库编号",prop:"storehouseName"},{label:"仓库名称",prop:"storehouseName"},{label:"货架名称",prop:"shelvesName"}],pagination:{hide:!0}}}]}]}),E=e=>{var i;const t={...e};for(const o in t)(t[o]===void 0||t[o]===null)&&(t[o]=" ");S.title="设备出库详情",S.defaultValue={...t||{}},(i=D.value)==null||i.openDrawer();const a={page:1,size:99999,mainId:t.id};_(a).then(o=>{l.selectList=o.data.data.data||[]}),l.outStock=[],l.getOutStock({storeOutId:t.id})},q=e=>{var i;const t={...e};for(const o in t)(t[o]===void 0||t[o]===null)&&(t[o]=" ");k.title="设备出库",k.defaultValue={...t},(i=x.value)==null||i.openDrawer(),l.outboundOrder=t;const a={page:1,size:99999,mainId:t.id};_(a).then(o=>{l.selectList=o.data.data.data||[]}),l.outStock=[],l.getOutStock({storeOutId:t.id})},l=u({deviceValue:[],storeList:[],outboundOrder:{},selectList:[],outDeviceValue:[],outStock:[],getDevice:e=>{const t={size:99999,page:1,...e};_(t).then(a=>{l.deviceValue=a.data.data.data||[]})},getoutDeviceValue:e=>{const t={size:99999,page:1,inStoreOnly:!0,...e};J(t).then(a=>{const i=a.data.data.data||[];let o=[];i.forEach(g=>{o=[...o,...g.restDeviceInfos]}),l.outDeviceValue=o})},getOutStock:e=>{const t={size:99999,page:1,scrapped:!0,...e};$(t).then(a=>{l.outStock=a.data.data.data||[]})},getstoreSerchValue:()=>{G({page:1,size:99999}).then(t=>{const a=t.data.data.data||[];l.storeList=a.map(i=>({label:i.name,value:i.id}))})}}),s=async()=>{var t,a,i,o,g,N,T;const e={size:p.pagination.limit,page:p.pagination.page,...((t=r.value)==null?void 0:t.queryParams)||{}};e.createtime&&((a=e.createtime)==null?void 0:a.length)>1&&(e.fromTime=((i=r.value)==null?void 0:i.queryParams).createtime[0]||"",e.toTime=((o=r.value)==null?void 0:o.queryParams).createtime[1]||""),e.outtime&&((g=e.outtime)==null?void 0:g.length)>1&&(e.outTimeFrom=((N=r.value)==null?void 0:N.queryParams).outtime[0]||"",e.outTimeTo=((T=r.value)==null?void 0:T.queryParams).outtime[1]||""),delete e.createtime,delete e.outtime,X(e).then(V=>{p.dataList=V.data.data.data||[],p.pagination.total=V.data.data.total||0})};return P(()=>{s(),l.getDevice(),l.getstoreSerchValue()}),(e,t)=>{const a=B,i=A,o=w;return W(),U("div",K,[c(a,{ref_key:"refSearch",ref:r,config:m(O)},null,8,["config"]),c(i,{config:m(p),class:"card-table"},null,8,["config"]),c(o,{ref_key:"refForm",ref:x,config:m(k)},null,8,["config"]),c(o,{ref_key:"refFormEquipment",ref:b,config:m(I)},null,8,["config"]),c(o,{ref_key:"detailForm",ref:D,config:m(S)},null,8,["config"])])}}});export{ue as default};
