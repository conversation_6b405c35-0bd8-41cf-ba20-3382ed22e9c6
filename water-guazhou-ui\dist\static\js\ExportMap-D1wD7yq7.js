import{d as z,c as _,r as f,s as l,b as g,bS as E,o as N,Q as G,g as P,h as R,F as h,p as w,q as y,i as b,_ as q,aq as O,dG as V,dH as $,aj as C,b7 as F,C as j}from"./index-r0dFAfgr.js";import{_ as J}from"./InlineForm.vue_vue_type_style_index_0_lang-s-ANlzyw.js";import U from"./RightDrawerMap-D5PhmGFO.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import{g as H}from"./LayerHelper-Cn-iiqxI.js";import"./project-DUuzYgGl.js";import{u as Q}from"./arcWidgetButton-0glIxrt7.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import{a as W,b as S}from"./tools-DUuxC1oj.js";import"./ArcView-DpMnCY82.js";import"./ViewHelper-BGCZjxXH.js";import"./fieldconfig-Bk3o1wi7.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./ArcStationWarning-BF9YrSzF.js";/* empty css                         */import"./useWaterPoint-Bv0z6ym6.js";import"./DateFormatter-Bm9a68Ax.js";import"./zhandian-YaGuQZe6.js";import"./useStation-DJgnSZIA.js";import"./DrawerBox-CLde5xC8.js";import"./SideDrawer-CBntChyn.js";import"./PipeDetail-CTBPYFJW.js";import"./Search-NSrhrIa_.js";import"./FormTableColumnFilter-BT7pLXIC.js";import"./QueryHelper-ILO3qZqg.js";import"./config-fy91bijz.js";import"./pipe-nogVzCHG.js";import"./ArcBRTools-BO92yznB.js";import"./PipeLength.vue_vue_type_script_setup_true_lang-BZfUsvDf.js";import"./StatisticsHelper-D-s_6AyQ.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./wfsUtils-DXofo3da.js";import"./usePipelineGroup-Ba1pmWz_.js";import"./GroupLayer-DhhN3FyN.js";import"./lazyLayerLoader-DbM9sT1W.js";import"./WFSLayer-1TtJp3nx.js";import"./clientSideDefaults-VQhQaYxh.js";import"./QueryEngineCapabilities-Dk3NJwmm.js";import"./wfsUtils-Du031XaC.js";import"./geojson-MBFu2-HZ.js";import"./xmlUtils-CtUoQO7q.js";import"./ListWindow-WS05QqV0.js";import"./PopLayout-BP55MvL7.js";import"./PoiSearchV2-D7yNeLuv.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./URLHelper-B9aplt5w.js";import"./useLayerList-DmEwJ-ws.js";import"./useScaleBar-Beed-z91.js";const K={class:"table-box"},X=z({__name:"ExportMap",setup(Y){const m=_(),p=_(),a=f({loading:!1,refreshCount:0}),I=f({labelPosition:"top",group:[{fieldset:{desc:"设置范围"},fields:[{type:"btn-group",btns:[{perm:!0,svgIcon:l(V),styles:{width:"100%"},text:"点击绘制范围",click:()=>A()},{perm:!0,text:"清除",styles:{width:"100%"},svgIcon:l($),type:"default",click:()=>u()}]}]},{fieldset:{desc:"比例尺"},fields:[{type:"input-number",field:"scale",appendBtns:[{perm:!0,iconifyIcon:"ep:refresh",title:"更新比例尺",click:()=>{!p.value||!r.view||(r.view.scale=p.value.dataForm.scale||1128)}}]}]},{fieldset:{desc:"参数配置"},fields:[{type:"input",field:"taskname",label:"文件名称",rules:[{required:!0,message:"请输入文件名"}]},{type:"select",field:"exporttype",label:"文件格式",clearable:!1,options:[{label:"PDF",value:"0"}]},{type:"select",field:"papersize",label:"纸张大小",clearable:!1,options:[{label:"A0",value:"0"},{label:"A1",value:"1"},{label:"A2",value:"2"},{label:"A3",value:"3"},{label:"A4",value:"4"}]},{type:"radio",field:"paperrotation",label:"纸张方向",options:[{label:"纵向",value:"0"},{label:"横向",value:"1"}]},{type:"number",field:"resolution",label:"分辨率",controlPosition:"right",rules:[{required:!0,message:"请输入分辨率"}]},{type:"btn-group",btns:[{perm:!0,text:"导出",styles:{width:"100%"},loading:()=>a.loading,svgIcon:l(C),click:()=>{var t;return(t=p.value)==null?void 0:t.Submit()}},{perm:!0,text:"重置",type:"default",styles:{width:"100%"},svgIcon:l(F),click:()=>{u(),v()}}]}]}],defaultValue:{scale:500,exporttype:"0",papersize:"0",paperrotation:"0",resolution:96},submit:async t=>{var e;if(!r.graphic){g.warning("请先绘制范围");return}a.loading=!0;try{const o=await W({geomjson:JSON.stringify((e=r.graphic)==null?void 0:e.geometry.toJSON()),scale:t.scale,taskname:t.taskname,exporttype:t.exporttype,papersize:t.papersize,paperrotation:t.paperrotation,resolution:t.resolution});o.data.code===1e4?(g.success("任务已生成"),m.value&&(m.value.dataForm.taskname=t.taskname),clearTimeout(a.timer),a.refreshCount=0,await c(),await n(o.data.result)):g.error(o.data.message||"任务生成失败")}catch(o){console.log(o.message),g.error("任务生成失败")}a.loading=!1}}),r={},s=f({dataList:[],columns:[{label:"任务名称",prop:"taskname"},{label:"创建时间",prop:"createtime"},{label:"导出状态",prop:"exp_status",tag:!0,tagColor:t=>t.exp_status==="导出失败"?"#aa0000":t.exp_status==="导出成功"?"#00aa00":"#0000aa"}],operations:[{perm:t=>t.exp_status==="导出成功",text:"下载",svgIcon:l(C),disabled:t=>t.exp_status!=="导出成功",click:t=>L(t)},{perm:t=>t.exp_status!=="导出成功",text:"刷新",loading:t=>t.exp_status==="导出中",svgIcon:l(F),click:t=>{clearTimeout(a.timer),a.refreshCount=0,n(t.task_id)}}],pagination:{refreshData:({page:t,size:e})=>{s.pagination.page=t,s.pagination.limit=e,c()}}}),n=async t=>{if(!t)return;clearTimeout(a.timer);const e=s.dataList.find(o=>o.task_id===t);try{const o=await S({pageindex:1,pagesize:1,exporttype:"0",taskid:e.task_id});if(o.data.result.rows.length){const i=o.data.result.rows[0];e.exp_status=i.exp_status,e.del_status=i.del_status,e.exp_status==="导出中"?a.timer=setTimeout(()=>{n(t)},1e3):e.exp_status==="未开始"&&a.refreshCount<10&&(a.timer=setTimeout(()=>{a.refreshCount++,n(t)},1e3))}return e}catch{return e.exp_status="导出失败",e}},L=t=>{E(window.SITE_CONFIG.GIS_CONFIG.gisApi+"/ExportResult/"+t.task_id+".pdf",t.taskname)},M=f({labelPosition:"right",group:[{fields:[{labelWidth:60,type:"input",label:"任务名",field:"taskname",itemContainerStyle:{marginBottom:"8px"}},{itemContainerStyle:{marginBottom:"8px"},type:"btn-group",btns:[{perm:!0,text:"搜索",click:()=>c()}]}]}]}),c=async()=>{var t,e,o;try{const i=await S({pageindex:s.pagination.page||1,pagesize:s.pagination.limit||20,taskname:(t=m.value)==null?void 0:t.dataForm.taskname,taskid:(e=m.value)==null?void 0:e.dataForm.taskid});s.dataList=((o=i.data.result)==null?void 0:o.rows)||[],s.pagination.total=i.data.result.total||0}catch(i){console.log(i.message)}},u=()=>{var t;(t=r.graphicsLayer)==null||t.removeAll(),r.graphic=void 0},A=()=>{var t,e;u(),(t=r.graphicsLayer)==null||t.removeAll(),(e=r.sketch)==null||e.create("rectangle")},v=()=>{var t;u(),(t=p.value)==null||t.resetForm()},x=_(),{initSketch:B,destroySketch:D}=Q(),k=t=>{t.state==="complete"&&(r.graphic=t.graphics[0])},T=t=>{var e;r.view=t,r.graphicsLayer=H(r.view,{id:"export-map",title:"地图导出"}),r.sketch=B(r.view,r.graphicsLayer,{updateCallBack:k,createCallBack:k}),r.view.watch("stationary",()=>{var o;p.value&&(p.value.dataForm.scale=((o=r.view)==null?void 0:o.scale)||500)}),(e=x.value)==null||e.toggleCustomDetail(!0),v()};return N(()=>{c()}),G(()=>{var t,e,o,i,d;(t=r.sketch)==null||t.cancel(),(e=r.sketch)==null||e.destroy(),(o=r.drawAction)==null||o.destroy(),(i=r.drawer)==null||i.destroy(),(d=r.graphicsLayer)==null||d.removeAll(),D()}),(t,e)=>{const o=q,i=J,d=O;return P(),R(U,{ref_key:"refMap",ref:x,title:"地图导出","detail-max-min":!0,"hide-detail-close":!0,onMapLoaded:T},{"detail-header":h(()=>e[0]||(e[0]=[w("span",null,"地图导出任务列表",-1)])),"detail-default":h(()=>[y(i,{ref_key:"refSearch",ref:m,config:b(M)},null,8,["config"]),w("div",K,[y(d,{config:b(s)},null,8,["config"])])]),default:h(()=>[y(o,{ref_key:"refForm",ref:p,config:b(I)},null,8,["config"])]),_:1},512)}}}),or=j(X,[["__scopeId","data-v-9a2bbae0"]]);export{or as default};
