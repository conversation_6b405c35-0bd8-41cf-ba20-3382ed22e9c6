package org.thingsboard.server.dao.model.sql.shuiwu;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;
import java.util.Date;

/**
 * 出口平均流量
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2021-08-19
 */
@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.SHUIWU_LIULIANG_AVG_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class SwLiuliangAvgEntity {
    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.SHUIWU_LIULIANG_AVG_VALUE_1)
    private Double value1;

    @Column(name = ModelConstants.SHUIWU_LIULIANG_AVG_VALUE_2)
    private Double value2;

    @Column(name = ModelConstants.SHUIWU_LIULIANG_AVG_VALUE_3)
    private Double value3;

    @Column(name = ModelConstants.SHUIWU_LIULIANG_AVG_CREATE_TIME)
    private Date createTime;
}
