<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.base.BasePermissionConfigurationMapper">
    
    <resultMap type="org.thingsboard.server.dao.model.sql.base.BasePermissionConfiguration" id="BasePermissionConfigurationResult">
        <result property="id"    column="id"    />
        <result property="mapConfigId"    column="map_config_id"    />
        <result property="roles"    column="roles"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectBasePermissionConfigurationVo">
        select id, map_config_id, roles, remark from base_permission_configuration
    </sql>

    <select id="selectBasePermissionConfigurationList" parameterType="org.thingsboard.server.dao.model.sql.base.BasePermissionConfiguration" resultMap="BasePermissionConfigurationResult">
        <include refid="selectBasePermissionConfigurationVo"/>
        <where>  
            <if test="mapConfigId != null  and mapConfigId != ''"> and map_config_id = #{mapConfigId}</if>
            <if test="roles != null  and roles != ''"> and roles = #{roles}</if>
        </where>
    </select>
    
    <select id="selectBasePermissionConfigurationById" parameterType="String" resultMap="BasePermissionConfigurationResult">
        <include refid="selectBasePermissionConfigurationVo"/>
        where id = #{id}
    </select>

    <insert id="insertBasePermissionConfiguration" parameterType="org.thingsboard.server.dao.model.sql.base.BasePermissionConfiguration">
        insert into base_permission_configuration
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="mapConfigId != null">map_config_id,</if>
            <if test="roles != null">roles,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="mapConfigId != null">#{mapConfigId},</if>
            <if test="roles != null">#{roles},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateBasePermissionConfiguration" parameterType="org.thingsboard.server.dao.model.sql.base.BasePermissionConfiguration">
        update base_permission_configuration
        <trim prefix="SET" suffixOverrides=",">
            <if test="mapConfigId != null">map_config_id = #{mapConfigId},</if>
            <if test="roles != null">roles = #{roles},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBasePermissionConfigurationById" parameterType="String">
        delete from base_permission_configuration where id = #{id}
    </delete>

    <delete id="deleteBasePermissionConfigurationByIds" parameterType="String">
        delete from base_permission_configuration where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>