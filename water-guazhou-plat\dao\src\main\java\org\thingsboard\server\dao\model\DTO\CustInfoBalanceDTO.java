package org.thingsboard.server.dao.model.DTO;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2023-01-31
 */
@Data
public class CustInfoBalanceDTO {

    private String id;

    private String orgId;

    private String orgName;

    private String meterBookId;

    private String meterBookCode;

    private String meterBookName;

    private String custCode;

    private String custName;

    private BigDecimal balance;

    private BigDecimal amount;
}
