// /**
//  * GeoServer工具函数
//  * 提供基于GeoServer的地理信息服务功能
//  */

// // 定义GeoServer配置接口
// interface GeoServerConfig {
//   geoServerUrl: string;
//   geoServerWorkspace: string;
// }

// // 扩展全局SITE_CONFIG类型，增加GeoServer配置
// declare global {
//   interface Window {
//     SITE_CONFIG: {
//       GIS_CONFIG: IGISCONFIG & GeoServerConfig;
//     };
//     GIS_SERVER_SWITCH?: boolean;
//   }
// }

// /**
//  * 将GeoJSON格式数据转换为ArcGIS格式
//  * @param geojson GeoJSON格式的数据
//  * @returns ArcGIS格式的数据
//  */
// export const convertGeoJSONToArcGIS = (geojson: any) => {
//   // 基本的转换逻辑
//   if (!geojson) return { features: [], spatialReference: { wkid: 4326 } };
  
//   // 处理不同类型的GeoJSON
//   if (geojson.type === 'FeatureCollection') {
//     return {
//       features: geojson.features.map(feature => convertFeatureToArcGIS(feature)).filter(Boolean),
//       spatialReference: { wkid: 4326 } // 默认使用WGS84坐标系
//     };
//   } else if (geojson.type === 'Feature') {
//     const feature = convertFeatureToArcGIS(geojson);
//     return {
//       features: feature ? [feature] : [],
//       spatialReference: { wkid: 4326 }
//     };
//   }
  
//   return { features: [], spatialReference: { wkid: 4326 } };
// };

// /**
//  * 将GeoJSON Feature转换为ArcGIS Feature
//  * @param feature GeoJSON Feature
//  * @returns ArcGIS Feature
//  */
// const convertFeatureToArcGIS = (feature: any) => {
//   if (!feature) return null;
  
//   // 基本结构转换
//   const arcgisFeature = {
//     attributes: feature.properties || {},
//     geometry: convertGeometryToArcGIS(feature.geometry)
//   };
  
//   // 确保OBJECTID存在
//   if (!arcgisFeature.attributes.OBJECTID && feature.id) {
//     arcgisFeature.attributes.OBJECTID = feature.id;
//   }
  
//   return arcgisFeature;
// };

// /**
//  * 将GeoJSON几何对象转换为ArcGIS几何对象
//  * @param geometry GeoJSON几何对象
//  * @returns ArcGIS几何对象
//  */
// const convertGeometryToArcGIS = (geometry: any) => {
//   if (!geometry) return null;
  
//   // 根据几何类型进行转换
//   switch (geometry.type) {
//     case 'Point':
//       return {
//         x: geometry.coordinates[0],
//         y: geometry.coordinates[1],
//         spatialReference: { wkid: 4326 }
//       };
//     case 'LineString':
//       return {
//         paths: [geometry.coordinates],
//         spatialReference: { wkid: 4326 }
//       };
//     case 'Polygon':
//       return {
//         rings: geometry.coordinates,
//         spatialReference: { wkid: 4326 }
//       };
//     // 其他几何类型可以根据需要添加
//     default:
//       return null;
//   }
// };

// /**
//  * 从GeoServer获取图层数据
//  * @param layerName 图层名称
//  * @param queryParams 查询参数
//  * @returns 查询结果
//  */
// export const queryGeoServerLayer = async (layerName: string, queryParams: any = {}) => {
//   try {
//     // 构建WFS请求URL
//     const baseUrl = window.SITE_CONFIG.GIS_CONFIG.geoServerUrl || 'http://localhost:8080/geoserver/wfs';
//     const workspace = window.SITE_CONFIG.GIS_CONFIG.geoServerWorkspace || 'water';
    
//     // 构建基本参数
//     const params = new URLSearchParams({
//       service: 'WFS',
//       version: '1.1.0',
//       request: 'GetFeature',
//       typeName: `${workspace}:${layerName}`,
//       outputFormat: 'application/json',
//       srsName: 'EPSG:4326'
//     });
    
//     // 添加过滤条件
//     if (queryParams.where && queryParams.where !== '1=1') {
//       params.append('CQL_FILTER', queryParams.where);
//     }
    
//     // 如果有几何过滤
//     if (queryParams.geometry) {
//       // 需要将ArcGIS几何对象转换为GeoServer理解的bbox或者WKT格式
//       // 这里简化处理，实际应用中需要更复杂的转换
//       const extent = queryParams.geometry.extent;
//       if (extent) {
//         params.append('bbox', `${extent.xmin},${extent.ymin},${extent.xmax},${extent.ymax},EPSG:4326`);
//       }
//     }
    
//     // 发送请求
//     const response = await fetch(`?${params.toString()}`);
    
//     if (!response.ok) {
//       throw new Error(`GeoServer请求失败: ${response.statusText}`);
//     }
    
//     // 解析GeoJSON响应
//     const geojson = await response.json();
    
//     // 提取OBJECTID数组
//     const objectIds = geojson.features.map(feature => 
//       feature.properties.OBJECTID || feature.id
//     );
    
//     // 转换为ArcGIS格式以便与现有系统集成
//     const arcgisFeatures = convertGeoJSONToArcGIS(geojson);
    
//     return {
//       objectIds,
//       features: arcgisFeatures.features,
//       originalGeoJSON: geojson
//     };
//   } catch (error) {
//     console.error('GeoServer查询失败:', error);
//     throw error;
//   }
// };

// /**
//  * 从GeoServer查询多个图层的ID
//  * @param layerInfos 图层信息数组
//  * @param queryParams 查询参数
//  * @returns 每个图层的查询结果
//  */
// export const queryGeoServerLayerIds = async (layerInfos: any[], queryParams: any = {}) => {
//   try {
//     // 对每个图层进行查询
//     const results = await Promise.allSettled(
//       layerInfos.map(async (item) => {
//         const layerName = item.layername;
//         const result = await queryGeoServerLayer(layerName, queryParams);
//         return {
//           label: `${layerName}(${result.objectIds.length || 0})`,
//           name: layerName,
//           data: result.objectIds || [],
//           originalData: result
//         };
//       })
//     );
    
//     // 处理结果
//     return results.map((result, index) => {
//       if (result.status === 'fulfilled') {
//         return result.value;
//       } else {
//         // 查询失败时返回空数据
//         const layerName = layerInfos[index].layername;
//         console.error(`图层 ${layerName} 查询失败:`, result.reason);
//         return {
//           label: `${layerName}(0)`,
//           name: layerName,
//           data: []
//         };
//       }
//     });
//   } catch (error) {
//     console.error('GeoServer多图层查询失败:', error);
//     throw error;
//   }
// }; 