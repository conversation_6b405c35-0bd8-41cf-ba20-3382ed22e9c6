import{d as u,c as r,bu as _,g as t,n as p,p as n,ax as f,h as i,an as l,i as y,e7 as h,C as v}from"./index-r0dFAfgr.js";import B from"./index-pS52AAh1.js";import g from"./index-DAiX9D4h.js";import"./Timer-DD6yFqCB.js";import"./padStart-BKfyZZDO.js";import"./ExitFullScreen-BAhtug-v.js";import"./Weather-C85tAM-i.js";const C={class:"large-screen overlay-y"},w={class:"layout-item smart-decision__main"},I=u({__name:"index",props:{showBars:{type:Boolean},showHeaders:{type:Boolean},title:{},logo:{}},emits:["to"],setup(b,{emit:c}){const s=c,o=r(),a=r(window.SITE_CONFIG.SMART_DECISION_CONFIG.bars||[]),m=e=>{s("to",e)},d=async()=>{o.value&&await h(o.value)};return _(()=>{s("to",a.value[0])}),(e,k)=>(t(),p("div",C,[n("div",{ref_key:"refContainer",ref:o,class:"smart-decision"},[n("div",w,[f(e.$slots,"default",{},void 0,!0)]),e.showHeaders?(t(),i(g,{key:0,class:"layout-item smart-decision__header",title:e.title,logo:e.logo,onFullscreen:d},null,8,["title","logo"])):l("",!0),e.showBars?(t(),i(B,{key:1,"bar-items":y(a),class:"layout-item smart-decision__footer",onTo:m},null,8,["bar-items"])):l("",!0)],512)]))}}),G=v(I,[["__scopeId","data-v-034c0bca"]]);export{G as default};
