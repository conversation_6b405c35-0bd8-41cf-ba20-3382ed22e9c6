package org.thingsboard.server.dao.util;

import com.getui.push.v2.sdk.ApiHelper;
import com.getui.push.v2.sdk.GtApiConfiguration;
import com.getui.push.v2.sdk.api.PushApi;
import com.getui.push.v2.sdk.common.ApiResult;
import com.getui.push.v2.sdk.dto.req.Audience;
import com.getui.push.v2.sdk.dto.req.Settings;
import com.getui.push.v2.sdk.dto.req.message.PushDTO;
import com.getui.push.v2.sdk.dto.req.message.PushMessage;
import com.getui.push.v2.sdk.dto.req.message.android.GTNotification;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Map;

@Slf4j
@Component
public class AppMsgPushUtil {

    private PushApi pushApi;

    @PostConstruct
    public void getPushApi() {
        try {
            //填写应用配置，参数在“Uni Push”下的“应用配置”页面中获取
            GtApiConfiguration apiConfiguration = new GtApiConfiguration();
            //填写应用配置，参数在“Uni Push”下的“应用配置”页面中获取
            apiConfiguration.setAppId("GiaGXjHQZ6A3vYXOoPFhC4");
            apiConfiguration.setAppKey("XbZ8C0BUjt6V5F1z5yfDw1");
            apiConfiguration.setMasterSecret("xjA3F6DTZn9to0MX1uOR24");
            apiConfiguration.setDomain("https://restapi.getui.com/v2/");
            // 实例化ApiHelper对象，用于创建接口对象
            ApiHelper apiHelper = ApiHelper.build(apiConfiguration);
            // 创建对象，建议复用。目前有PushApi、StatisticApi、UserApi
            this.pushApi = apiHelper.creatApi(PushApi.class);
        } catch (Exception e) {
            log.error("初始化APP推送失败!");
        }
    }

    public ApiResult<Map<String, Map<String, String>>> pushMsg(String body, String title, String cid) {
        //根据cid进行单推
        PushDTO<Audience> pushDTO = new PushDTO<Audience>();
        // 设置推送参数，requestid需要每次变化唯一
        pushDTO.setRequestId(System.currentTimeMillis() + "");
        Settings settings = new Settings();
        pushDTO.setSettings(settings);
        //消息有效期，走厂商消息必须设置该值
        settings.setTtl(3600000);

        //在线走个推通道时推送的消息体
        PushMessage pushMessage = new PushMessage();
        GTNotification gtNotification = new GTNotification();
        gtNotification.setBody(body);
        gtNotification.setTitle(title);
        gtNotification.setClickType("none");
        pushMessage.setNotification(gtNotification);
        pushDTO.setPushMessage(pushMessage);
        //此格式的透传消息由 unipush 做了特殊处理，会自动展示通知栏。开发者也可自定义其它格式，在客户端自己处理。
//        pushMessage.setTransmission(" {title:\"标题\",content:\"内容\",payload:\"自定义数据\"}");
        // 设置接收人信息
        Audience audience = new Audience();
        pushDTO.setAudience(audience);
        audience.addCid(cid);

        // 进行cid单推
        ApiResult<Map<String, Map<String, String>>> apiResult = this.pushApi.pushToSingleByCid(pushDTO);
        return apiResult;
        /*if (apiResult.isSuccess()) {
            // success
            System.out.println(apiResult.getData());
        } else {
            // failed
            System.out.println("code:" + apiResult.getCode() + ", msg: " + apiResult.getMsg());
        }*/
    }

}
