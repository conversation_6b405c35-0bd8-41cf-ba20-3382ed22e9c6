<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartManagement.district.CircuitDistrictPointMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->id,
                           name,
                           area_id,
                           lat,
                           lon,
                           remark,
                           create_time,
                           tenant_id<!--@sql from sm_circuit_district_point -->
    </sql>
    <resultMap id="BaseResultMap"
               type="org.thingsboard.server.dao.model.sql.smartManagement.district.CircuitDistrictPoint">
        <result column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="area_id" property="areaId"/>
        <result column="lat" property="lat"/>
        <result column="lon" property="lon"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sm_circuit_district_point
        <where>
            <if test="areaId != null">
                and area_id = #{areaId}
            </if>
            and tenant_id = #{tenantId}
        </where>
    </select>

    <update id="update">
        update sm_circuit_district_point
        <set>
            name    = #{name},
            area_id = #{areaId},
            lat     = #{lat},
            lon     = #{lon},
            <if test="remark != null">
                remark = #{remark},
            </if>
        </set>
        where id = #{id}
    </update>

    <sql id="Report_Template_Column_List">
        <!--@sql select -->
        '关键点' as type,
        code,
        point.id       as device_type,
        point.name
        <!--@sql from sm_circuit_district_point point, sm_circuit_task task -->
    </sql>
    <resultMap id="CircuitTaskReportSaveRequestResultMap"
               type="org.thingsboard.server.dao.util.imodel.query.smartManagement.plan.CircuitTaskReportSaveRequest">
        <result column="type" property="type"/>
        <result column="code" property="task_code"/>
        <result column="device_type" property="deviceType"/>
        <result column="name" property="name"/>
    </resultMap>
    <select id="selectReportTemplate"
            resultMap="CircuitTaskReportSaveRequestResultMap">
        select
        <include refid="Report_Template_Column_List"/>
        from sm_circuit_district_point point,
             sm_circuit_task task
        where point.area_id = task.district_area_id
          and task.id in
        <foreach collection="idList" item="element" open="(" close=")" separator=",">
            #{element}
        </foreach>
    </select>

    <select id="hasPoint" resultType="boolean">
        select count(1) > 0
        from sm_circuit_district_point
        where area_id = #{districtAreaId}
    </select>
</mapper>