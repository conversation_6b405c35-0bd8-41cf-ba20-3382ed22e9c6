package org.thingsboard.server.dao.maintainCircuit.circuit;

import org.thingsboard.server.dao.model.sql.maintainCircuit.circuit.CircuitTaskC;

import java.util.Map;

/**
 * 班组
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-08-24
 */
public interface CircuitTaskCService {

    void save(CircuitTaskC circuitTaskC);

    Map statistics(String deviceLabelCode);

    boolean checkUser(String mainId, String userId);
}
