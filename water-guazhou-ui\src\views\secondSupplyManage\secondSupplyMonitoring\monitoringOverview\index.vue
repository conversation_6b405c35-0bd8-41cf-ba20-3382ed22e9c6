<template>
  <RightDrawerMap
    ref="refMap"
    :title="'泵站总览'"
    :windows="state.windows"
    :hide-detail-close="true"
    :hide-layer-list="true"
    :right-drawer-width="600"
    @map-loaded="onMapLoaded"
  >
    <div class="content">
      <FieldSet type="underline" title="二供泵房监测"></FieldSet>
      <div class="right-box">
        <FormTable
          ref="refCard"
          class="table-box"
          :config="cardTableConfig"
        ></FormTable>
      </div>
      <FieldSet
        type="underline"
        :title="(state.curRow?.name || '') + '24小时运行曲线'"
      ></FieldSet>
      <div v-loading="loading" class="right-box bottom">
        <VChart
          ref="refChart"
          :option="state.lineOption"
          :theme="appStore.isDark ? 'blackBackground' : 'whiteBackground'"
        />
      </div>
    </div>
    <template #detail-header>
      <span class="title">泵站总览-{{ state.curRow?.name }}</span>
    </template>
    <template #detail-default>
      <div class="bottom-content">
        <div class="bottom-box">
          <!-- <VChart ref="refChart" class="bottom-chart-box" :theme="appStore.isDark ?  'blackBackground' : 'whiteBackground'"
          :option="state.pieOption1" /> -->

          <div class="top">
            今日供水量<span class="value">{{ state.pieOption1 }}</span> m³
          </div>
          <VChart
            ref="refChart2"
            class="bottom-chart-box"
            :theme="appStore.isDark ? 'blackBackground' : 'whiteBackground'"
            :option="state.barOption1"
          />
        </div>
        <div class="bottom-box">
          <div class="top">
            昨日供水量<span class="value">{{ state.pieOption2 }}</span> m³
          </div>
          <VChart
            ref="refChart3"
            class="bottom-chart-box"
            :theme="appStore.isDark ? 'blackBackground' : 'whiteBackground'"
            :option="state.barOption2"
          />
        </div>
        <div ref="refBottom" class="bottom-box">
          <div class="top">
            本月供水量<span class="value">{{ state.pieOption3 }}</span> m³
          </div>
          <VChart
            ref="refChart4"
            class="bottom-chart-box"
            :theme="appStore.isDark ? 'blackBackground' : 'whiteBackground'"
            :option="state.barOption3"
          />
        </div>
      </div>
    </template>
  </RightDrawerMap>
</template>
<script lang="ts" setup>
import Point from '@arcgis/core/geometry/Point.js';
import Graphic from '@arcgis/core/Graphic.js';
import PictureMarkerSymbol from '@arcgis/core/symbols/PictureMarkerSymbol.js';
import { lineOption, pieOption, barOption } from '../../echartsData/echart';
import useStation from '@/hooks/station/useStation';
import { GetStationRealTimeDetail } from '@/api/shuiwureports/zhandian';
import { IECharts } from '@/plugins/echart';
import { colors } from '@/views/dispatchCenter/ShuiWuJiaShiChang/data';
import {
  getWaterSupplyInfo,
  getWaterSupplyDetail
} from '@/api/secondSupplyManage/monitoringOverview';
import RightDrawerMap from '@/views/arcMap/components/common/RightDrawerMap.vue';
import { bindViewClick, gotoAndHighLight } from '@/utils/MapHelper';
import { SLMessage } from '@/utils/Message';
import { useAppStore } from '@/store';
import { getStationImageUrl } from '@/utils/URLHelper';

const appStore = useAppStore();
const refCard = ref<IFormTableIns>();
const refMap = ref<InstanceType<typeof RightDrawerMap>>();
const { getAllStationOption } = useStation();

const refChart = ref<IECharts>();
const loading = ref<boolean>(false);

let tableData = reactive<any[]>([]);
const state = reactive<{
  barOption1: any;
  pieOption1: any;
  barOption2: any;
  pieOption2: any;
  barOption3: any;
  pieOption3: any;
  lineOption: any;
  curRow?: any;
  data: any;
  stationLocation: any[];
  windows: IArcMarkerProps[];
}>({
  pieOption1: null,
  barOption1: null,
  pieOption2: null,
  barOption2: null,
  pieOption3: null,
  barOption3: null,
  lineOption: null,
  curRow: {},
  data: null,
  stationLocation: [],
  windows: []
});
const staticState: {
  view?: __esri.MapView;
} = {};

const handleMarkClick = async (stationId?: string) => {
  const tableRow = cardTableConfig.dataList.find(
    (item) => item.stationId === stationId
  );
  state.curRow = tableRow;
  resetPanel(tableRow);
  let graphic: __esri.Graphic | undefined;
  if (!stationId) {
    graphic = staticState.view?.graphics?.getItemAt(0);
  } else {
    graphic = staticState.view?.graphics.find(
      (item) => item.attributes?.row?.id === stationId
    );
    if (graphic) {
      await gotoAndHighLight(staticState.view, graphic, {
        zoom: 15,
        avoidHighlight: true
      });
    }
  }
  if (!graphic) return;

  const attributes = graphic.attributes?.row || {};
  const res = await GetStationRealTimeDetail(attributes.id);
  const values =
    res.data?.map((item) => {
      item.label = item.propertyName;
      item.value;

      return item;
    }) || [];
  const point = graphic?.geometry as __esri.Point;
  state.windows.length = 0;
  state.windows.push({
    visible: false,
    x: point.x,
    y: point.y,
    offsetY: -30,
    title: attributes.name,
    attributes: {
      values,
      id: attributes.id
    }
  });
  await nextTick();
  refMap.value?.openPop(attributes.id);
};
//
const cardTableConfig = reactive<ITable>({
  loading: true,
  dataList: [],
  columns: [
    { prop: 'name', label: '名称' },
    {
      prop: 'todayWaterSupply',
      label: '今日供水量',
      minWidth: 95,
      unit: '(m³)'
    },
    {
      prop: 'yesterdayWaterSupply',
      label: '昨日供水量',
      minWidth: 95,
      unit: '(m³)'
    },
    {
      prop: 'monthWaterSupply',
      label: '本月供水量',
      minWidth: 95,
      unit: '(m³)'
    }
  ],
  highlightCurrentRow: true,
  currentRowKey: 'stationId',
  handleRowClick: async (row: any) => {
    state.curRow = row;
    const g = staticState.view?.graphics.find(
      (item) => item.attributes?.row?.id === row.stationId
    );
    if (g) {
      await gotoAndHighLight(staticState.view, g, {
        zoom: 15,
        avoidHighlight: true
      });
    } else {
      SLMessage.warning('该站点暂无位置信息');
    }
    handleMarkClick(row.stationId);
  },
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    layout: 'total, sizes, jumper',
    refreshData: ({ page, size }) => {
      cardTableConfig.pagination.page = page;
      cardTableConfig.pagination.limit = size;
      cardTableConfig.dataList = tableData.slice(
        (page - 1) * size,
        page * size
      );
    }
  }
});

// refreshData
// 刷新列表 模拟数据
const refreshData = () => {
  getWaterSupplyInfo().then((res) => {
    tableData = res.data?.data;
    cardTableConfig.dataList = tableData?.slice(0, 20);
    cardTableConfig.pagination.total = tableData.length;
    cardTableConfig.currentRow = tableData[0];
    cardTableConfig.loading = false;
    // resetPanel(tableData[0])
    handleMarkClick(tableData[0]?.stationId);
  });
  addMarks();
};
//  获取监控数据已经加载显示图表
const resetPanel = async (row) => {
  if (!row) return;
  loading.value = true;
  cardTableConfig.currentRow = row;
  const res = await getWaterSupplyDetail(row.stationId || row.id);
  const result = res.data.data;
  refChart.value?.clear();
  nextTick(() => {
    const datax1 = result.todayTotalFlowDataList?.map((item) => {
      return item.ts.substring(8, 20);
    });
    const seriesData1 = result.todayTotalFlowDataList?.map(
      (item) => item.value
    );
    state.barOption1 = barOption('', colors[0], datax1, seriesData1, 70, 70);
    // state.pieOption1 = pieOption('今日供水量', colors[0], row.todayWaterSupply)
    state.pieOption1 = row.todayWaterSupply;
    const datax2 = result.yesterdayTotalFlowDataList?.map((item) => {
      return item.ts.substring(8, 20);
    });
    const seriesData2 = result.yesterdayTotalFlowDataList?.map(
      (item) => item.value
    );
    state.pieOption2 = pieOption(
      '昨日供水量',
      colors[1],
      row.yesterdayWaterSupply
    );
    state.barOption2 = barOption('', colors[1], datax2, seriesData2, 70, 70);
    state.pieOption2 = row.yesterdayWaterSupply;
    const datax3 = result.monthTotalFlowDataList?.map((item) => {
      return item.ts.substring(8, 20);
    });
    const seriesData3 = result.monthTotalFlowDataList?.map(
      (item) => item.value
    );
    // state.pieOption3 = pieOption('本月供水量', colors[2], row.monthWaterSupply)
    state.barOption3 = barOption('', colors[2], datax3, seriesData3, 70, 70);
    state.pieOption3 = row.monthWaterSupply;
    const lineDatax = result.todayTotalFlowDataList.map((item) => item.ts);
    const pressureData = result.pressure?.map((item) => item.value);
    const InstantaneousDta = result.Instantaneous_flow?.map(
      (item) => item.value
    );
    state.lineOption = lineOption(
      lineDatax,
      pressureData,
      InstantaneousDta,
      50,
      50
    );
    refChart.value?.resize();
  });
  loading.value = false;
};

// 添加地图图标
const addMarks = async () => {
  const res = await getAllStationOption('泵站');
  staticState.view?.graphics?.removeAll();
  res.map((data) => {
    const item = data.data;
    const location = item?.location?.split(',');
    if (location?.length !== 2) return;
    const point = new Point({
      longitude: location?.[0],
      latitude: location?.[1],
      spatialReference: staticState.view?.spatialReference
    });
    const markG = new Graphic({
      geometry: point,
      symbol: new PictureMarkerSymbol({
        width: 25,
        height: 30,
        yoffset: 15,
        url: getStationImageUrl('泵站.png')
      }),
      attributes: {
        row: item
      }
    });

    staticState.view?.graphics?.add(markG);
  });
  handleMarkClick();
};
const onMapLoaded = (view: __esri.MapView) => {
  staticState.view = view;
  refMap.value?.toggleCustomDetail(true);
  refreshData();
  bindViewClick(staticState.view, (res) => {
    const result = res.results?.[0];
    if (!result) return;
    if (result.type === 'graphic') {
      const row = result.graphic?.attributes?.row;
      handleMarkClick(row?.id);
    }
  });
};
</script>
<style lang="scss" scoped>
.content {
  height: 100%;
}

.bottom-content {
  height: 100%;
  display: flex;
  justify-content: space-around;

  .bottom-box {
    width: 33.33%;
    height: 100%;
    font-size: 20px;
    font-weight: 600;

    .top {
      .value {
        padding-left: 18px;
        font-size: 30px;
      }
    }
  }
}

.right-box {
  width: 100%;
  height: 50%;

  .table-box {
    height: 100%;
  }
}

.bottom {
  height: calc(42% - 20px);
}

.today-lightning-total,
.today-lightning-perton {
  display: flex;

  .count {
    font-size: 18px;
    color: #ffad51;
    font-weight: 500;
  }

  .unit {
    font-size: 12px;
  }

  .yesterday {
    margin-left: auto;
    font-size: 12px;
    padding-top: 10px;
  }
}

.today-lightning-perton {
  .count {
    color: #ff51ab;
  }
}

.header-slot {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .title {
    word-break: keep-all;
  }
}
</style>
<style lang="scss">
.panel {
  &.map-right {
    right: 0;
  }

  &.map-right {
    top: 0;
    width: 400px;
    height: 100%;

    .content {
      width: 100%;
      height: calc(100% - 15px);
    }
  }

  &.map-bottom {
    left: 0;
  }

  &.map-bottom {
    bottom: 0;
    width: calc(100% - 400px);
    height: 300px;

    .content {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: space-around;
    }
  }
}
</style>
