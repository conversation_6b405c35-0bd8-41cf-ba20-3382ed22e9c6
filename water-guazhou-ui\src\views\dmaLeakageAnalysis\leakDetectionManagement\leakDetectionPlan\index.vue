<template>
  <div class="leak-detection-plan-container">
    <!-- 筛选与操作栏 -->
    <el-form :model="searchParams" :inline="true" class="filter-form">
      <el-form-item label="区域" style="min-width: 160px;">
        <el-select v-model="searchParams.region" placeholder="全部" clearable style="width: 100%;">
          <el-option label="全部" value="" />
          <el-option v-for="item in regionList" :key="item" :label="item" :value="item" />
        </el-select>
      </el-form-item>
      <el-form-item label="分区" style="min-width: 160px;">
        <el-select v-model="searchParams.partition" placeholder="全部" clearable style="width: 100%;">
          <el-option label="全部" value="" />
          <el-option v-for="item in partitionList" :key="item" :label="item" :value="item" />
        </el-select>
      </el-form-item>
      <el-form-item label="负责人">
        <el-input v-model="searchParams.leader" placeholder="全部" clearable />
      </el-form-item>
      <el-form-item label="状态">
        <el-select v-model="searchParams.status" placeholder="全部" clearable style="width: 100%;">
          <el-option label="全部" value="" />
          <el-option label="未开始" value="未开始" />
          <el-option label="进行中" value="进行中" />
          <el-option label="已完成" value="已完成" />
        </el-select>
      </el-form-item>
      <el-form-item label="计划时间">
        <el-date-picker v-model="searchParams.planDate" type="month" placeholder="选择年月" clearable />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">查询</el-button>
        <el-button @click="resetSearch">重置</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="success" @click="openAdd">新增方案</el-button>
        <el-button type="primary" @click="openImport">批量导入</el-button>
        <el-button type="warning" @click="handleExport">导出</el-button>
        <el-button type="danger" :disabled="!multipleSelection.length" @click="handleBatchDelete">批量删除</el-button>
      </el-form-item>
    </el-form>

    <!-- 方案列表 -->
    <el-table
      :data="filteredPlanList"
      border
      style="width: 100%"
      @selection-change="handleSelectionChange"
      :row-key="row => row.id"
      ref="tableRef"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="name" label="方案名称" min-width="140" />
      <el-table-column prop="region" label="区域" min-width="100" />
      <el-table-column prop="partition" label="分区" min-width="100" />
      <el-table-column prop="leader" label="负责人" min-width="100" />
      <el-table-column prop="planDate" label="计划时间" min-width="100" />
      <el-table-column prop="status" label="状态" min-width="100" />
      <el-table-column prop="segments" label="分段数" min-width="80">
        <template #default="scope">{{ scope.row.segments.length }}</template>
      </el-table-column>
      <el-table-column prop="lossRate" label="最新漏损率(%)" min-width="120">
        <template #default="scope">{{ calcPlanLossRate(scope.row) }}</template>
      </el-table-column>
      <el-table-column prop="leakCount" label="发现漏点数" min-width="100">
        <template #default="scope">{{ calcPlanLeakCount(scope.row) }}</template>
      </el-table-column>
      <el-table-column label="操作" width="220">
        <template #default="scope">
          <el-button type="primary" link @click="openEdit(scope.row)">编辑</el-button>
          <el-button type="info" link @click="openDetail(scope.row)">明细</el-button>
          <el-button type="danger" link @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-model:current-page="pagination.page"
      v-model:page-size="pagination.pageSize"
      :total="filteredPlanList.length"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      style="margin-top: 16px; text-align: right;"
    />

    <!-- 编辑/新增弹窗 -->
    <el-dialog v-model="editDialogVisible" :title="editForm.id ? '编辑探漏方案' : '新增探漏方案'" width="800px">
      <el-form :model="editForm" label-width="100px">
        <el-form-item label="方案名称" required>
          <el-input v-model="editForm.name" />
        </el-form-item>
        <el-form-item label="区域" required>
          <el-select v-model="editForm.region" placeholder="请选择区域">
            <el-option v-for="item in regionList" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item label="分区" required>
          <el-select v-model="editForm.partition" placeholder="请选择分区">
            <el-option v-for="item in partitionList" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item label="负责人" required>
          <el-input v-model="editForm.leader" />
        </el-form-item>
        <el-form-item label="计划时间" required>
          <el-date-picker v-model="editForm.planDate" type="month" placeholder="选择年月" />
        </el-form-item>
        <el-form-item label="状态" required>
          <el-select v-model="editForm.status" placeholder="请选择状态">
            <el-option label="未开始" value="未开始" />
            <el-option label="进行中" value="进行中" />
            <el-option label="已完成" value="已完成" />
          </el-select>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="editForm.remark" type="textarea" rows="2" />
        </el-form-item>
        <el-divider>分段明细</el-divider>
        <el-table :data="editForm.segments" border size="small">
          <el-table-column prop="name" label="分段名称" min-width="100">
            <template #default="scope">
              <el-input v-model="scope.row.name" placeholder="如：A1段" />
            </template>
          </el-table-column>
          <el-table-column prop="startPoint" label="起点" min-width="80">
            <template #default="scope">
              <el-input v-model="scope.row.startPoint" />
            </template>
          </el-table-column>
          <el-table-column prop="endPoint" label="终点" min-width="80">
            <template #default="scope">
              <el-input v-model="scope.row.endPoint" />
            </template>
          </el-table-column>
          <el-table-column prop="leader" label="负责人" min-width="80">
            <template #default="scope">
              <el-input v-model="scope.row.leader" />
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" min-width="80">
            <template #default="scope">
              <el-select v-model="scope.row.status" placeholder="请选择">
                <el-option label="未开始" value="未开始" />
                <el-option label="进行中" value="进行中" />
                <el-option label="已完成" value="已完成" />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="lossRate" label="最新漏损率(%)" min-width="100">
            <template #default="scope">
              <el-input-number v-model="scope.row.lossRate" :min="0" :max="100" />
            </template>
          </el-table-column>
          <el-table-column prop="leakCount" label="发现漏点数" min-width="100">
            <template #default="scope">
              <el-input-number v-model="scope.row.leakCount" :min="0" />
            </template>
          </el-table-column>
          <el-table-column prop="remark" label="备注" min-width="100">
            <template #default="scope">
              <el-input v-model="scope.row.remark" />
            </template>
          </el-table-column>
          <el-table-column label="操作" width="80">
            <template #default="scope">
              <el-button type="danger" link @click="removeSegment(scope.$index)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-button type="primary" plain icon="el-icon-plus" @click="addSegment" style="margin-top:8px;">新增分段</el-button>
      </el-form>
      <template #footer>
        <el-button @click="editDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleEditSave">保存</el-button>
      </template>
    </el-dialog>

    <!-- 明细弹窗 -->
    <el-dialog v-model="detailDialogVisible" title="探漏方案明细" width="800px">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="方案名称">{{ detailForm.name }}</el-descriptions-item>
        <el-descriptions-item label="区域">{{ detailForm.region }}</el-descriptions-item>
        <el-descriptions-item label="分区">{{ detailForm.partition }}</el-descriptions-item>
        <el-descriptions-item label="负责人">{{ detailForm.leader }}</el-descriptions-item>
        <el-descriptions-item label="计划时间">{{ detailForm.planDate }}</el-descriptions-item>
        <el-descriptions-item label="状态">{{ detailForm.status }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ detailForm.remark }}</el-descriptions-item>
      </el-descriptions>
      <el-divider>分段明细</el-divider>
      <el-table :data="detailForm.segments" border size="small">
        <el-table-column prop="name" label="分段名称" min-width="100" />
        <el-table-column prop="startPoint" label="起点" min-width="80" />
        <el-table-column prop="endPoint" label="终点" min-width="80" />
        <el-table-column prop="leader" label="负责人" min-width="80" />
        <el-table-column prop="status" label="状态" min-width="80" />
        <el-table-column prop="lossRate" label="最新漏损率(%)" min-width="100" />
        <el-table-column prop="leakCount" label="发现漏点数" min-width="100" />
        <el-table-column prop="remark" label="备注" min-width="100" />
      </el-table>
      <template #footer>
        <el-button @click="detailDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>

    <!-- 导入弹窗 -->
    <el-dialog v-model="importDialogVisible" title="批量导入" width="400px">
      <el-upload
        drag
        action="#"
        :auto-upload="false"
        :show-file-list="true"
        :on-change="handleImportChange"
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
      </el-upload>
      <template #footer>
        <el-button @click="importDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleImport">导入</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'

const regionList = ['东区', '西区', '南区', '北区']
const partitionList = ['A分区', 'B分区', 'C分区', 'D分区']

// 假数据
const planList = ref([
  {
    id: 1,
    name: 'A区主干管分段探漏',
    region: '东区',
    partition: 'A分区',
    leader: '张三',
    planDate: '2023-10',
    status: '进行中',
    remark: '重点关注主干管',
    segments: [
      { id: 1, name: 'A1段', startPoint: 'A-01', endPoint: 'A-10', leader: '李四', status: '已完成', lossRate: 7.2, leakCount: 2, remark: '' },
      { id: 2, name: 'A2段', startPoint: 'A-11', endPoint: 'A-20', leader: '王五', status: '进行中', lossRate: 8.5, leakCount: 1, remark: '' }
    ]
  },
  {
    id: 2,
    name: 'B区分段探漏',
    region: '西区',
    partition: 'B分区',
    leader: '李四',
    planDate: '2023-10',
    status: '未开始',
    remark: '',
    segments: [
      { id: 1, name: 'B1段', startPoint: 'B-01', endPoint: 'B-10', leader: '赵六', status: '未开始', lossRate: 9.1, leakCount: 0, remark: '' }
    ]
  }
])

const searchParams = ref({
  region: '',
  partition: '',
  leader: '',
  status: '',
  planDate: ''
})

const pagination = ref({
  page: 1,
  pageSize: 10,
})

const filteredPlanList = computed(() => {
  let data = planList.value.filter(row => {
    const regionMatch = !searchParams.value.region || row.region === searchParams.value.region
    const partitionMatch = !searchParams.value.partition || row.partition === searchParams.value.partition
    const leaderMatch = !searchParams.value.leader || row.leader.includes(searchParams.value.leader)
    const statusMatch = !searchParams.value.status || row.status === searchParams.value.status
    const planDateMatch = !searchParams.value.planDate || row.planDate === searchParams.value.planDate
    return regionMatch && partitionMatch && leaderMatch && statusMatch && planDateMatch
  })
  // 分页
  const start = (pagination.value.page - 1) * pagination.value.pageSize
  const end = start + pagination.value.pageSize
  return data.slice(start, end)
})

const multipleSelection = ref<any[]>([])
const tableRef = ref()

function handleSelectionChange(val: any[]) {
  multipleSelection.value = val
}

function handleSearch() {
  pagination.value.page = 1
}
function resetSearch() {
  searchParams.value.region = ''
  searchParams.value.partition = ''
  searchParams.value.leader = ''
  searchParams.value.status = ''
  searchParams.value.planDate = ''
  pagination.value.page = 1
}

// 方案主表相关
const editDialogVisible = ref(false)
const editForm = ref<any>({ segments: [] })
function openAdd() {
  editForm.value = { name: '', region: '', partition: '', leader: '', planDate: '', status: '', remark: '', segments: [] }
  editDialogVisible.value = true
}
function openEdit(row: any) {
  editForm.value = JSON.parse(JSON.stringify(row))
  editDialogVisible.value = true
}
function addSegment() {
  if (!editForm.value.segments) editForm.value.segments = []
  editForm.value.segments.push({ id: Date.now(), name: '', startPoint: '', endPoint: '', leader: '', status: '', lossRate: 0, leakCount: 0, remark: '' })
}
function removeSegment(idx: number) {
  editForm.value.segments.splice(idx, 1)
}
function handleEditSave() {
  if (!editForm.value.name || !editForm.value.region || !editForm.value.partition || !editForm.value.leader || !editForm.value.planDate || !editForm.value.status) {
    ElMessage.warning('请填写完整信息')
    return
  }
  if (editForm.value.id) {
    // 编辑
    const idx = planList.value.findIndex(r => r.id === editForm.value.id)
    if (idx !== -1) planList.value[idx] = { ...editForm.value }
    ElMessage.success('编辑成功')
  } else {
    // 新增
    const newId = Math.max(0, ...planList.value.map(r => r.id)) + 1
    planList.value.push({ ...editForm.value, id: newId })
    ElMessage.success('新增成功')
  }
  editDialogVisible.value = false
}

// 明细弹窗
const detailDialogVisible = ref(false)
const detailForm = ref<any>({ segments: [] })
function openDetail(row: any) {
  detailForm.value = JSON.parse(JSON.stringify(row))
  detailDialogVisible.value = true
}

// 删除
function handleDelete(row: any) {
  planList.value = planList.value.filter(r => r.id !== row.id)
  ElMessage.success('删除成功')
}
function handleBatchDelete() {
  if (!multipleSelection.value.length) return
  planList.value = planList.value.filter(r => !multipleSelection.value.includes(r))
  ElMessage.success('批量删除成功')
  multipleSelection.value = []
}

// 导入
const importDialogVisible = ref(false)
function openImport() {
  importDialogVisible.value = true
}
function handleImportChange(file: any) {
  // 这里只做演示
  ElMessage.info('已选择文件：' + file.name)
}
function handleImport() {
  importDialogVisible.value = false
  ElMessage.success('导入成功（模拟）')
}

// 导出
function handleExport() {
  // 这里只做演示
  ElMessage.success('导出成功（模拟）')
}

// 统计字段
function calcPlanLossRate(plan: any) {
  if (!plan.segments.length) return '-'
  // 取所有分段最新漏损率的平均值
  const sum = plan.segments.reduce((acc, seg) => acc + (Number(seg.lossRate) || 0), 0)
  return (sum / plan.segments.length).toFixed(2)
}
function calcPlanLeakCount(plan: any) {
  if (!plan.segments.length) return 0
  return plan.segments.reduce((acc, seg) => acc + (Number(seg.leakCount) || 0), 0)
}
</script>

<style scoped>
.leak-detection-plan-container {
  padding: 20px;
  background: #fff;
  border-radius: 6px;
}
.filter-form {
  margin-bottom: 16px;
}
</style> 