package org.thingsboard.server.dao.smartService.seats;

import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.smartService.seats.SeatsUser;

import java.util.List;

/**
 * 黑名单
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-10-27
 */
public interface SeatsUserService {
    PageData getList(String keywords, int page, int size, String tenantId);

    SeatsUser save(SeatsUser seatsUser);

    int delete(List<String> ids);

    String check(List<String> seatsUserIdList, String tenantId);

}
