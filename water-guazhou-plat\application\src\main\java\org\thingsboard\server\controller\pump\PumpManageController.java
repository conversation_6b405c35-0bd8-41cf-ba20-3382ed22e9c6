package org.thingsboard.server.controller.pump;

import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.sql.smartProduction.pumpHouse.PumpManage;
import org.thingsboard.server.dao.pumpHouse.PumpManageService;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.pumpHouse.PumpManagePageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.pumpHouse.PumpManageSaveRequest;
import org.thingsboard.server.dao.util.imodel.response.ExcelFileInfo;
import org.thingsboard.server.utils.imodel.annotations.IStarController;

@IStarController
@RequestMapping({"/api/sp/pumpManage"})
public class PumpManageController extends BaseController {
    @Autowired
    private PumpManageService service;

    public PumpManageController() {
    }

    @GetMapping
    public IPage<PumpManage> findAllConditional(PumpManagePageRequest request) {
        return this.service.findAllConditional(request);
    }

    @GetMapping({"/excel/template"})
    public ExcelFileInfo excelTemplate() {
        return ExcelFileInfo.of("泵机管理模板").withDate().withoutTitle().nextTitle("设备编码").nextTitle("设备名称").nextTitle("设备简称").nextTitle("泵个数").nextTitle("厂家名称").nextTitle("设备型号").nextTitle("安装人").nextTitle("安装日期（示例：2001-01-01）").nextTitle("性能参数").nextTitle("备注");
    }

    @GetMapping({"/excel/export"})
    public ExcelFileInfo excelExport(PumpManagePageRequest request) {
        return ExcelFileInfo.of("泵机管理", this.findAllConditional((PumpManagePageRequest) request.ignorePage())).withDateTime().nextTitle("pumpRoomCode", "泵房编码").nextTitle("pumpRoomName", "泵房名称").nextTitle("code", "设备编码").nextTitle("name", "设备名称").nextTitle("nickname", "设备简称").nextTitle("pumpNum", "泵个数").nextTitle("companyName", "厂家名称").nextTitle("model", "设备型号").nextTitle("installUserName", "安装人").nextTitle("installDate", "安装日期（示例：2001-01-01）").nextTitle("createTime", "录入日期").nextTitle("performanceParameters", "性能参数").nextTitle("remark", "备注");
    }

    @PostMapping
    public PumpManage save(@RequestBody PumpManageSaveRequest req) {
        return this.service.save(req);
    }

    @PostMapping({"/batch"})
    public List<PumpManage> saveBatch(@RequestBody List<PumpManageSaveRequest> req) {
        return this.service.saveAll(req);
    }

    @PatchMapping({"/{id}"})
    public boolean edit(@RequestBody PumpManageSaveRequest req, @PathVariable String id) {
        return this.service.update((PumpManage) req.unwrap(id));
    }

    @DeleteMapping({"/{id}"})
    public boolean delete(@PathVariable String id) {
        return this.service.delete(id);
    }
}
