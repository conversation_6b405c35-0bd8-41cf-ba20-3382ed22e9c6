package org.thingsboard.server.config;

import org.springframework.amqp.core.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.thingsboard.server.config.amqp.shortmessage.ShortMessageAmqpAdmin;
import org.thingsboard.server.config.amqp.shortmessage.ShortMessageRabbitMQConnectionFactory;
import org.thingsboard.server.config.amqp.shortmessage.ShortMessageRabbitTemplate;

/**
 * rabbit mq 配置
 */
@Configuration
// @EnableConfigurationProperties(RabbitMQConfigurationProperties.class)
public class RabbitMQConfiguration {

    @Autowired
    private RabbitMQConfigurationProperties rabbitMQConfigurationProperties;


    // region shortMessage

    @Bean
    @ConditionalOnProperty("spring.rabbitmq.shortmessage.enabled")
    public ShortMessageRabbitMQConnectionFactory shortMessageConnectionFactory(
            @Value("${spring.rabbitmq.shortmessage.host}") String host,
            @Value("${spring.rabbitmq.shortmessage.port}") int port,
            @Value("${spring.rabbitmq.shortmessage.username}") String username,
            @Value("${spring.rabbitmq.shortmessage.password}") String password,
            @Value("${spring.rabbitmq.shortmessage.virtual-host}") String virtualHost
    ) {
        ShortMessageRabbitMQConnectionFactory connectionFactory = new ShortMessageRabbitMQConnectionFactory();
        connectionFactory.setHost(host);
        connectionFactory.setPort(port);
        connectionFactory.setUsername(username);
        connectionFactory.setPassword(password);
        connectionFactory.setVirtualHost(virtualHost);
        return connectionFactory;
    }

    @Bean(name = "alibaba-short-message-rabbit-template")
    @ConditionalOnProperty("spring.rabbitmq.shortmessage.enabled")
    public ShortMessageRabbitTemplate shortMessageRabbitTemplate(@Autowired ShortMessageRabbitMQConnectionFactory connectionFactory) {
        return new ShortMessageRabbitTemplate(connectionFactory);
    }

    @Bean
    @ConditionalOnProperty("spring.rabbitmq.shortmessage.enabled")
    public ShortMessageAmqpAdmin shortMessageAmqpAdmin(@Autowired ShortMessageRabbitMQConnectionFactory connectionFactory) {
        return new ShortMessageAmqpAdmin(connectionFactory);
    }

    @Bean
    @ConditionalOnProperty("spring.rabbitmq.shortmessage.enabled")
    public String createExchange(ShortMessageAmqpAdmin admin) {
        Exchange delayExchange = new DirectExchange("short-message-exchange", true, false);
        admin.declareExchange(delayExchange);
        return "short-message-delay-exchange";
    }

    @Bean
    @ConditionalOnProperty("spring.rabbitmq.shortmessage.enabled")
    public String createQueue(ShortMessageAmqpAdmin admin) {
        Queue reliableQueue = new Queue("shortmessage.dispatch.reliable", true, false, false);
        Queue vulnerableQueue = new Queue("shortmessage.dispatch.vulnerable", false, false, false);
        admin.declareQueue(reliableQueue);
        admin.declareQueue(vulnerableQueue);
        return "shortmessage.dispatch";
    }

    @Bean
    @ConditionalOnProperty("spring.rabbitmq.shortmessage.enabled")
    public String createBinding(ShortMessageAmqpAdmin admin) {
        Binding reliableQueueBind = new Binding("shortmessage.dispatch.reliable",
                Binding.DestinationType.QUEUE,
                "short-message-exchange",
                "to.reliable", null);
        Binding vulnerableQueueBind = new Binding("shortmessage.dispatch.vulnerable",
                Binding.DestinationType.QUEUE,
                "short-message-exchange",
                "to.vulnerable", null);
        admin.declareBinding(reliableQueueBind);
        admin.declareBinding(vulnerableQueueBind);
        return "short-message-dispatch";
    }
    // endregion

    /**
     * 申明执行规则节点的消息队列
     */
//    @Bean
//     public Queue queueExecuteScript() {
//         return new Queue(rabbitMQConfigurationProperties.getQueueExecuteScript(), true);
//     }

    /**
     * 申明接收执行结果的消息队列
     */
//    @Bean
//     public Queue queueReceiveResult() {
//         return new Queue(rabbitMQConfigurationProperties.getQueueReceiveResult(), true);
//     }

    @Value("${spring.rabbitmq.exchangeSyncUser}")
    private String exchangeSyncUser;
    @Value("${spring.rabbitmq.queueAddUser}")
    private String queueAddUser;
    @Value(value = "${spring.rabbitmq.queueUpdateUser}")
    private String queueUpdateUser;
    @Value(value = "${spring.rabbitmq.queueDeleteUser}")
    private String queueDeleteUser;
    @Value("${spring.rabbitmq.routingKeyAddUser}")
    private String routingKeyAddUser;
    @Value("${spring.rabbitmq.routingKeyUpdateUser}")
    private String routingKeyUpdateUser;
    @Value("${spring.rabbitmq.routingKeyDeleteUser}")
    private String routingKeyDeleteUser;

    @Bean("exchangeSyncUser")
    @ConditionalOnProperty("spring.rabbitmq.shortmessage.enabled")
    public TopicExchange createNewExchange(ShortMessageAmqpAdmin admin) {
        TopicExchange newExchange = new TopicExchange(exchangeSyncUser, true, false);
        admin.declareExchange(newExchange);
        return newExchange;
    }

    @Bean("queueAddUser")
    @ConditionalOnProperty("spring.rabbitmq.shortmessage.enabled")
    public Queue createAddQueue(ShortMessageAmqpAdmin admin) {
        Queue newQueue = new Queue(queueAddUser, true, false, false);
        admin.declareQueue(newQueue);
        return newQueue;
    }

    @Bean("queueUpdateUser")
    @ConditionalOnProperty("spring.rabbitmq.shortmessage.enabled")
    public Queue createpdateQueue(ShortMessageAmqpAdmin admin) {
        Queue newQueue = new Queue(queueUpdateUser, true, false, false);
        admin.declareQueue(newQueue);
        return newQueue;
    }

    @Bean("queueDeleteUser")
    @ConditionalOnProperty("spring.rabbitmq.shortmessage.enabled")
    public Queue createDeleteQueue(ShortMessageAmqpAdmin admin) {
        Queue newQueue = new Queue(queueDeleteUser, true, false, false);
        admin.declareQueue(newQueue);
        return newQueue;
    }

    @Bean
    @ConditionalOnProperty("spring.rabbitmq.shortmessage.enabled")
    public Binding createAddBinding(ShortMessageAmqpAdmin admin) {
        Binding newQueueBind = new Binding(queueAddUser,
                Binding.DestinationType.QUEUE,
                exchangeSyncUser,
                routingKeyAddUser, null);
        admin.declareBinding(newQueueBind);
        return newQueueBind;
    }

    @Bean
    @ConditionalOnProperty("spring.rabbitmq.shortmessage.enabled")
    public Binding createUpdateBinding(ShortMessageAmqpAdmin admin) {
        Binding newQueueBind = new Binding(queueUpdateUser,
                Binding.DestinationType.QUEUE,
                exchangeSyncUser,
                routingKeyUpdateUser, null);
        admin.declareBinding(newQueueBind);
        return newQueueBind;
    }

    @Bean
    @ConditionalOnProperty("spring.rabbitmq.shortmessage.enabled")
    public Binding createDeleteBinding(ShortMessageAmqpAdmin admin) {
        Binding newQueueBind = new Binding(queueDeleteUser,
                Binding.DestinationType.QUEUE,
                exchangeSyncUser,
                routingKeyDeleteUser, null);
        admin.declareBinding(newQueueBind);
        return newQueueBind;
    }

}
