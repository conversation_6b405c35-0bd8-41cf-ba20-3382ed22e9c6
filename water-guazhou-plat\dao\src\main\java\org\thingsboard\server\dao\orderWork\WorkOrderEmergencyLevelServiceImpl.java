package org.thingsboard.server.dao.orderWork;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrderEmergencyLevel;
import org.thingsboard.server.dao.sql.workOrder.WorkOrderEmergencyLevelMapper;

import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class WorkOrderEmergencyLevelServiceImpl implements WorkOrderEmergencyLevelService {

    @Autowired
    private WorkOrderEmergencyLevelMapper workOrderEmergencyLevelMapper;

    @Override
    public List<WorkOrderEmergencyLevel> findList(String status, TenantId tenantId) {
        QueryWrapper<WorkOrderEmergencyLevel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("tenant_id", UUIDConverter.fromTimeUUID(tenantId.getId()));
        if (StringUtils.isNotBlank(status)) {
            queryWrapper.eq("status", status);
        }
        return workOrderEmergencyLevelMapper.selectList(queryWrapper);
    }

    @Override
    public void changeStatus(WorkOrderEmergencyLevel param) {
        WorkOrderEmergencyLevel processLevel = workOrderEmergencyLevelMapper.selectById(param.getId());
        if (processLevel != null) {
            processLevel.setStatus(param.getStatus());
            workOrderEmergencyLevelMapper.updateById(processLevel);
        }
    }

    @Override
    public void save(WorkOrderEmergencyLevel entity) {
        if (StringUtils.isBlank(entity.getId())) {
            entity.setCreateTime(new Date());
            entity.setStatus("1");
            workOrderEmergencyLevelMapper.insert(entity);
        } else {
            workOrderEmergencyLevelMapper.updateById(entity);
        }
    }

    @Override
    public void remove(List<String> ids) {
        workOrderEmergencyLevelMapper.deleteBatchIds(ids);
    }

}
