package org.thingsboard.server.dao.model.sql.smartManagement.plan;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.query.GeneralTaskRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

import java.util.List;

@Getter
@Setter
public class SMCircuitTaskBatchAssignRequest implements GeneralTaskRequest {
    // 任务Id列表
    @NotNullOrEmpty
    public List<String> taskIdList;

    // 接收人员Id
    @NotNullOrEmpty
    private String receiveUserId;

    // 共同完成人Id，多个用逗号隔开
    private String collaborateUserId;


}
