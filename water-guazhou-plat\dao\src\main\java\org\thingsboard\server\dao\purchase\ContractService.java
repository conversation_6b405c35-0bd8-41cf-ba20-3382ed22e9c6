package org.thingsboard.server.dao.purchase;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.purchase.Contract;
import org.thingsboard.server.dao.util.imodel.query.purchase.ContractPageRequest;
import org.thingsboard.server.dao.util.imodel.query.purchase.ContractSaveRequest;

public interface ContractService {
    /**
     * 分页条件查询合同单
     *
     * @param request 分页查询条件
     * @return 分页查询结果
     */
    IPage<Contract> findAllConditional(ContractPageRequest request);

    /**
     * 保存合同单
     *
     * @param entity 实体信息
     * @return 保存好的实体
     */
    Contract save(ContractSaveRequest entity);

    /**
     * 增量修改
     *
     * @param entity 实体信息
     * @return 是否修改成功
     */
    boolean update(Contract entity);

    /**
     * 删除
     *
     * @param id 唯一标识
     * @return 是否删除成功
     */
    boolean delete(String id);

}
