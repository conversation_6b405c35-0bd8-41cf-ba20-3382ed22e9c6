/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.model.sql;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.common.data.constantsAttribute.ConstantsAttribute;
import org.thingsboard.server.common.data.id.ConstantsAttributeId;
import org.thingsboard.server.dao.model.BaseSqlEntity;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.model.SearchTextEntity;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.CONSTANTS_ATTRIBUTE_NAME)
public class ConstantsAttributeEntity extends BaseSqlEntity<ConstantsAttribute> implements SearchTextEntity<ConstantsAttribute> {

    @Column(name = ModelConstants.VIRTUAL_TYPE)
    private String type;
    //公式
    @Column(name = ModelConstants.KEY_COLUMN)
    private String key;
    //group
    @Column(name = ModelConstants.VALUE_COLUMN)
    private String value;


    public ConstantsAttributeEntity() {
    }

    public ConstantsAttributeEntity(ConstantsAttribute constantsAttribute) {
        if (constantsAttribute.getId() != null)
            this.setId(constantsAttribute.getId().getId());
        this.type = constantsAttribute.getType();
        this.key = constantsAttribute.getKey();
        this.value = constantsAttribute.getValue();
    }

    @Override
    public ConstantsAttribute toData() {
        ConstantsAttribute constantsAttribute = new ConstantsAttribute(new ConstantsAttributeId(getId()));
        constantsAttribute.setKey(getKey());
        constantsAttribute.setType(getType());
        constantsAttribute.setValue(getValue());
        return constantsAttribute;

    }

    @Override
    public String getSearchTextSource() {
        return key;
    }

    @Override
    public void setSearchText(String searchText) {
        this.key = searchText;
    }
}
