package org.thingsboard.server.controller.base;

import java.util.List;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.thingsboard.server.dao.base.IBaseDatabaseStructureService;
import org.thingsboard.server.dao.model.sql.base.BaseDatabaseStructure;
import org.thingsboard.server.dao.util.imodel.query.base.BaseDatabaseStructurePageRequest;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;
import org.thingsboard.server.service.aspect.annotation.MonitorPerformance;

/**
 * 公共管理-数据库结构修复Controller
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
@Api(tags = "平台管理-数据库结构修复")
@RestController
@RequestMapping("api/base/database/structure")
public class BaseDatabaseStructureController extends BaseController {

    @Autowired
    private IBaseDatabaseStructureService baseDatabaseStructureService;

    /**
     * 查询公共管理-数据库结构修复列表
     */
    @MonitorPerformance(description = "平台管理-查询数据库结构修复列表")
    @ApiOperation(value = "查询数据库结构修复列表")
    @GetMapping("/list")
    public IstarResponse list(BaseDatabaseStructurePageRequest baseDatabaseStructure) {
        return IstarResponse.ok(baseDatabaseStructureService.selectBaseDatabaseStructureList(baseDatabaseStructure));
    }

    /**
     * 获取公共管理-数据库结构修复详细信息
     */
    @MonitorPerformance(description = "平台管理-查询数据库结构修复详情")
    @ApiOperation(value = "查询数据库结构修复详情")
    @GetMapping(value = "/getDetail")
    public IstarResponse getInfo(String id) {
        return IstarResponse.ok(baseDatabaseStructureService.selectBaseDatabaseStructureById(id));
    }

    /**
     * 新增公共管理-数据库结构修复
     */
    @MonitorPerformance(description = "平台管理-新增数据库结构修复")
    @ApiOperation(value = "新增数据库结构修复")
    @PostMapping("/add")
    public IstarResponse add(@RequestBody BaseDatabaseStructure baseDatabaseStructure) {
        return IstarResponse.ok(baseDatabaseStructureService.insertBaseDatabaseStructure(baseDatabaseStructure));
    }

    /**
     * 修改公共管理-数据库结构修复
     */
    @MonitorPerformance(description = "平台管理-修改数据库结构修复")
    @ApiOperation(value = "修改数据库结构修复")
    @PostMapping("/edit")
    public IstarResponse edit(@RequestBody BaseDatabaseStructure baseDatabaseStructure) {
        return IstarResponse.ok(baseDatabaseStructureService.updateBaseDatabaseStructure(baseDatabaseStructure));
    }

    /**
     * 删除公共管理-数据库结构修复
     */
    @MonitorPerformance(description = "平台管理-删除数据库结构修复")
    @ApiOperation(value = "删除数据库结构修复")
    @DeleteMapping("/deleteIds")
    public IstarResponse remove(@RequestBody List<String> ids) {
        return IstarResponse.ok(baseDatabaseStructureService.deleteBaseDatabaseStructureByIds(ids));
    }
}
