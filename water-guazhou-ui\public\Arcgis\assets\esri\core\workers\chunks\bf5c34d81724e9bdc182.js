"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[3011],{93860:(e,t,r)=>{function n(e,t){const r=e.count;t||(t=new e.TypedArrayConstructor(r));for(let n=0;n<r;n++)t[n]=e.get(n);return t}r.d(t,{m:()=>n}),Object.freeze(Object.defineProperty({__proto__:null,copy:function(e,t,r){const n=e.typedBuffer,o=e.typedBufferStride,s=t.typedBuffer,a=t.typedBufferStride,i=r?r.count:t.count;let _=(r&&r.dstIndex?r.dstIndex:0)*o,u=(r&&r.srcIndex?r.srcIndex:0)*a;for(let e=0;e<i;++e)n[_]=s[u],_+=o,u+=a},makeDense:n},Symbol.toStringTag,{value:"Module"}))},10816:(e,t,r)=>{r.d(t,{f:()=>a,n:()=>s});var n=r(79583);function o(e,t,r){const n=e.typedBuffer,o=e.typedBufferStride,s=t.typedBuffer,a=t.typedBufferStride,i=r?r.count:t.count;let _=(r&&r.dstIndex?r.dstIndex:0)*o,u=(r&&r.srcIndex?r.srcIndex:0)*a;for(let e=0;e<i;++e)n[_]=s[u],n[_+1]=s[u+1],_+=o,u+=a}function s(e,t,r){const s=e.typedBuffer,a=e.typedBufferStride,i=t.typedBuffer,_=t.typedBufferStride,u=r?r.count:t.count;let E=(r&&r.dstIndex?r.dstIndex:0)*a,T=(r&&r.srcIndex?r.srcIndex:0)*_;if((0,n.U)(t.elementType)){const e=(0,n.Op)(t.elementType);if((0,n.B3)(t.elementType))for(let t=0;t<u;++t)s[E]=Math.max(i[T]/e,-1),s[E+1]=Math.max(i[T+1]/e,-1),E+=a,T+=_;else for(let t=0;t<u;++t)s[E]=i[T]/e,s[E+1]=i[T+1]/e,E+=a,T+=_}else o(e,t,r);return e}function a(e,t,r,n){const o=e.typedBuffer,s=e.typedBufferStride,a=n?.count??e.count;let i=(n?.dstIndex??0)*s;for(let e=0;e<a;++e)o[i]=t,o[i+1]=r,i+=s}Object.freeze(Object.defineProperty({__proto__:null,copy:o,fill:a,normalizeIntegerBuffer:s},Symbol.toStringTag,{value:"Module"}))},11077:(e,t,r)=>{r.d(t,{a:()=>i,n:()=>s,s:()=>a,t:()=>o});var n=r(72220);function o(e,t,r){if(e.count!==t.count)return void n.c.error("source and destination buffers need to have the same number of elements");const o=e.count,s=r[0],a=r[1],i=r[2],_=r[3],u=r[4],E=r[5],T=r[6],c=r[7],R=r[8],f=e.typedBuffer,A=e.typedBufferStride,l=t.typedBuffer,d=t.typedBufferStride;for(let e=0;e<o;e++){const t=e*A,r=e*d,n=l[r],o=l[r+1],N=l[r+2],S=l[r+3];f[t]=s*n+_*o+T*N,f[t+1]=a*n+u*o+c*N,f[t+2]=i*n+E*o+R*N,f[t+3]=S}}function s(e,t){const r=Math.min(e.count,t.count),n=e.typedBuffer,o=e.typedBufferStride,s=t.typedBuffer,a=t.typedBufferStride;for(let e=0;e<r;e++){const t=e*o,r=e*a,i=s[r],_=s[r+1],u=s[r+2],E=i*i+_*_+u*u;if(E>0){const e=1/Math.sqrt(E);n[t]=e*i,n[t+1]=e*_,n[t+2]=e*u}}}function a(e,t,r){const n=Math.min(e.count,t.count),o=e.typedBuffer,s=e.typedBufferStride,a=t.typedBuffer,i=t.typedBufferStride;for(let e=0;e<n;e++){const t=e*s,n=e*i;o[t]=r*a[n],o[t+1]=r*a[n+1],o[t+2]=r*a[n+2],o[t+3]=r*a[n+3]}}function i(e,t,r){const n=Math.min(e.count,t.count),o=e.typedBuffer,s=e.typedBufferStride,a=t.typedBuffer,i=t.typedBufferStride;for(let e=0;e<n;e++){const t=e*s,n=e*i;o[t]=a[n]>>r,o[t+1]=a[n+1]>>r,o[t+2]=a[n+2]>>r,o[t+3]=a[n+3]>>r}}Object.freeze(Object.defineProperty({__proto__:null,normalize:s,scale:a,shiftRight:i,transformMat3:o,transformMat4:function(e,t,r){if(e.count!==t.count)return void n.c.error("source and destination buffers need to have the same number of elements");const o=e.count,s=r[0],a=r[1],i=r[2],_=r[3],u=r[4],E=r[5],T=r[6],c=r[7],R=r[8],f=r[9],A=r[10],l=r[11],d=r[12],N=r[13],S=r[14],O=r[15],C=e.typedBuffer,I=e.typedBufferStride,p=t.typedBuffer,L=t.typedBufferStride;for(let e=0;e<o;e++){const t=e*I,r=e*L,n=p[r],o=p[r+1],h=p[r+2],m=p[r+3];C[t]=s*n+u*o+R*h+d*m,C[t+1]=a*n+E*o+f*h+N*m,C[t+2]=i*n+T*o+A*h+S*m,C[t+3]=_*n+c*o+l*h+O*m}}},Symbol.toStringTag,{value:"Module"}))},75488:(e,t,r)=>{function n(e,t,r){const n=e.typedBuffer,o=e.typedBufferStride,s=t.typedBuffer,a=t.typedBufferStride,i=r?r.count:t.count;let _=(r&&r.dstIndex?r.dstIndex:0)*o,u=(r&&r.srcIndex?r.srcIndex:0)*a;for(let e=0;e<i;++e)n[_]=s[u],n[_+1]=s[u+1],n[_+2]=s[u+2],n[_+3]=s[u+3],_+=o,u+=a}function o(e,t,r,n,o,s){const a=e.typedBuffer,i=e.typedBufferStride,_=s?.count??e.count;let u=(s?.dstIndex??0)*i;for(let e=0;e<_;++e)a[u]=t,a[u+1]=r,a[u+2]=n,a[u+3]=o,u+=i}r.d(t,{c:()=>n,f:()=>o}),Object.freeze(Object.defineProperty({__proto__:null,copy:n,fill:o},Symbol.toStringTag,{value:"Module"}))},23670:(e,t,r)=>{r.d(t,{G:()=>o});var n=r(20102);let o=class{constructor(e,t,r=""){this.major=e,this.minor=t,this._context=r}lessThan(e,t){return this.major<e||e===this.major&&this.minor<t}since(e,t){return!this.lessThan(e,t)}validate(e){if(this.major!==e.major){const t=this._context&&this._context+":",r=this._context&&this._context+" ";throw new n.Z(t+"unsupported-version",`Required major ${r}version is '${this.major}', but got '\${version.major}.\${version.minor}'`,{version:e})}}clone(){return new o(this.major,this.minor,this._context)}static parse(e,t=""){const[r,s]=e.split("."),a=/^\s*\d+\s*$/;if(!r||!r.match||!r.match(a))throw new n.Z((t&&t+":")+"invalid-version","Expected major version to be a number, but got '${version}'",{version:e});if(!s||!s.match||!s.match(a))throw new n.Z((t&&t+":")+"invalid-version","Expected minor version to be a number, but got '${version}'",{version:e});const i=parseInt(r,10),_=parseInt(s,10);return new o(i,_,t)}}},79583:(e,t,r)=>{function n(e){switch(e){case"u8":case"i8":return 1;case"u16":case"i16":return 2;case"u32":case"i32":case"f32":return 4;case"f64":return 8}}function o(e){switch(e){case"u8":case"u16":case"u32":return!1;case"i8":case"i16":case"i32":case"f32":case"f64":return!0}}function s(e){switch(e){case"u8":case"u16":case"u32":case"i8":case"i16":case"i32":return!0;case"f32":case"f64":return!1}}function a(e){switch(e){case"u8":return 255;case"u16":return 65535;case"u32":return 4294967295;case"i8":return 127;case"i16":return 32767;case"i32":return 2147483647;case"f32":return 3402823e32;case"f64":return 179769e303}}r.d(t,{B3:()=>o,Op:()=>a,U:()=>s,n1:()=>n})},8323:(e,t,r)=>{r.d(t,{gS:()=>o});var n=r(79583);function o(e,t){return new e(new ArrayBuffer(t*e.ElementCount*(0,n.n1)(e.ElementType)))}Object.freeze(Object.defineProperty({__proto__:null,copy:function(e,t,r){const n=e.typedBuffer,o=e.typedBufferStride,s=t.typedBuffer,a=t.typedBufferStride,i=r?r.count:t.count;let _=(r&&r.dstIndex?r.dstIndex:0)*o,u=(r&&r.srcIndex?r.srcIndex:0)*a;for(let e=0;e<i;++e){for(let e=0;e<9;++e)n[_+e]=s[u+e];_+=o,u+=a}}},Symbol.toStringTag,{value:"Module"})),Object.freeze(Object.defineProperty({__proto__:null,copy:function(e,t,r){const n=e.typedBuffer,o=e.typedBufferStride,s=t.typedBuffer,a=t.typedBufferStride,i=r?r.count:t.count;let _=(r&&r.dstIndex?r.dstIndex:0)*o,u=(r&&r.srcIndex?r.srcIndex:0)*a;for(let e=0;e<i;++e){for(let e=0;e<16;++e)n[_+e]=s[u+e];_+=o,u+=a}}},Symbol.toStringTag,{value:"Module"})),r(93860),r(10816),r(56067),r(75488)},40270:(e,t,r)=>{r.d(t,{C:()=>u});var n=r(3172),o=r(66643),s=r(20102),a=r(70586),i=r(95330),_=r(17452);class u{constructor(e){this._streamDataRequester=e}async loadJSON(e,t){return this._load("json",e,t)}async loadBinary(e,t){return(0,_.HK)(e)?((0,i.k_)(t),(0,_.AH)(e)):this._load("binary",e,t)}async loadImage(e,t){return this._load("image",e,t)}async _load(e,t,r){if((0,a.Wi)(this._streamDataRequester))return(await(0,n.default)(t,{responseType:E[e]})).data;const _=await(0,o.q6)(this._streamDataRequester.request(t,e,r));if(!0===_.ok)return _.value;throw(0,i.r9)(_.error),new s.Z("",`Request for resource failed: ${_.error}`)}}const E={image:"image",binary:"array-buffer",json:"json"}},91911:(e,t,r)=>{r.d(t,{DA:()=>a,jX:()=>i,nh:()=>s});var n=r(1533),o=r(54388);function s(e,t=o.p){return"number"==typeof e?t(e):(0,n.Uc)(e)||(0,n.lq)(e)?new Uint32Array(e):e}function a(e){const t="number"==typeof e?e:e.length;if(t<3)return[];const r=t-2,n=(0,o.$z)(r);if("number"==typeof e){let e=0;for(let t=0;t<r;t+=1)t%2==0?(n[e++]=t,n[e++]=t+1,n[e++]=t+2):(n[e++]=t+1,n[e++]=t,n[e++]=t+2)}else{let t=0;for(let o=0;o<r;o+=1)if(o%2==0){const r=e[o],s=e[o+1],a=e[o+2];n[t++]=r,n[t++]=s,n[t++]=a}else{const r=e[o+1],s=e[o],a=e[o+2];n[t++]=r,n[t++]=s,n[t++]=a}}return n}function i(e){const t="number"==typeof e?e:e.length;if(t<3)return new Uint16Array(0);const r=t-2,n=r<=65536?new Uint16Array(3*r):new Uint32Array(3*r);if("number"==typeof e){let e=0;for(let t=0;t<r;++t)n[e++]=0,n[e++]=t+1,n[e++]=t+2;return n}{const t=e[0];let o=e[1],s=0;for(let a=0;a<r;++a){const r=e[a+2];n[s++]=t,n[s++]=o,n[s++]=r,o=r}return n}}},15317:(e,t,r)=>{r.d(t,{$A:()=>s,Ml:()=>i,NM:()=>o,i$:()=>a});var n=r(47026);class o{constructor(e){this.data=e,this.type="encoded-mesh-texture",this.encoding=n.Ti.KTX2_ENCODING}}function s(e){return"encoded-mesh-texture"===e?.type}async function a(e){return new Promise(((t,r)=>{const n=new Blob([e]),o=new FileReader;o.onload=()=>{const e=o.result;t(JSON.parse(e))},o.onerror=e=>{r(e)},o.readAsText(n)}))}async function i(e,t){return t===n.Ti.KTX2_ENCODING?new o(e):new Promise(((r,n)=>{const o=new Blob([e],{type:t}),s=URL.createObjectURL(o),a=new Image,i=()=>{URL.revokeObjectURL(s),"decode"in a?a.decode().then((()=>r(a)),(()=>r(a))).then(u):(r(a),u())},_=e=>{URL.revokeObjectURL(s),n(e),u()},u=()=>{a.removeEventListener("load",i),a.removeEventListener("error",_)};a.addEventListener("load",i),a.addEventListener("error",_),a.src=s}))}},91695:(e,t,r)=>{r.d(t,{Q:()=>x});var n,o,s=r(92604),a=r(70586),i=r(13598),_=r(35371),u=r(20102),E=r(30175),T=r(95330),c=r(17452),R=r(23670),f=r(52138),A=r(51305),l=r(94961),d=r(56481),N=r(93860);class S{constructor(e){this._data=e,this._offset4=0,this._dataUint32=new Uint32Array(this._data,0,Math.floor(this._data.byteLength/4))}readUint32(){const e=this._offset4;return this._offset4+=1,this._dataUint32[e]}readUint8Array(e){const t=4*this._offset4;return this._offset4+=e/4,new Uint8Array(this._data,t,e)}remainingBytes(){return this._data.byteLength-4*this._offset4}}!function(e){e.SCALAR="SCALAR",e.VEC2="VEC2",e.VEC3="VEC3",e.VEC4="VEC4",e.MAT2="MAT2",e.MAT3="MAT3",e.MAT4="MAT4"}(n||(n={})),function(e){e[e.ARRAY_BUFFER=34962]="ARRAY_BUFFER",e[e.ELEMENT_ARRAY_BUFFER=34963]="ELEMENT_ARRAY_BUFFER"}(o||(o={}));var O=r(74085);const C={baseColorFactor:[1,1,1,1],metallicFactor:1,roughnessFactor:1},I={pbrMetallicRoughness:C,emissiveFactor:[0,0,0],alphaMode:"OPAQUE",alphaCutoff:.5,doubleSided:!1},p={ESRI_externalColorMixMode:"tint"},L=(e={})=>{const t={...C,...e.pbrMetallicRoughness},r=function(e){switch(e.ESRI_externalColorMixMode){case"multiply":case"tint":case"ignore":case"replace":break;default:(0,O.Bg)(e.ESRI_externalColorMixMode),e.ESRI_externalColorMixMode="tint"}return e}({...p,...e.extras});return{...I,...e,pbrMetallicRoughness:t,extras:r}},h={magFilter:_.cw.LINEAR,minFilter:_.cw.LINEAR_MIPMAP_LINEAR,wrapS:_.e8.REPEAT,wrapT:_.e8.REPEAT},m=e=>({...h,...e});var M=r(15317);const D=1179937895;class B{constructor(e,t,r,n){if(this._context=e,this.uri=t,this.json=r,this._glbBuffer=n,this._bufferLoaders=new Map,this._textureLoaders=new Map,this._textureCache=new Map,this._materialCache=new Map,this._nodeParentMap=new Map,this._nodeTransformCache=new Map,this._supportedExtensions=["KHR_texture_basisu"],this._baseUri=function(e){let t,r;return e.replace(/^(.*\/)?([^/]*)$/,((e,n,o)=>(t=n||"",r=o||"",""))),{dirPart:t,filePart:r}}(this.uri).dirPart,this._checkVersionSupported(),this._checkRequiredExtensionsSupported(),null==r.scenes)throw new u.Z("gltf-loader-unsupported-feature","Scenes must be defined.");if(null==r.meshes)throw new u.Z("gltf-loader-unsupported-feature","Meshes must be defined");if(null==r.nodes)throw new u.Z("gltf-loader-unsupported-feature","Nodes must be defined.");this._computeNodeParents()}static async load(e,t,r){if((0,c.HK)(t)){const r=(0,c.sJ)(t);if(r&&"model/gltf-binary"!==r.mediaType)try{const n=JSON.parse(r.isBase64?atob(r.data):r.data);return new B(e,t,n)}catch{}const n=(0,c.AH)(t);if(B._isGLBData(n))return this._fromGLBData(e,t,n)}if(t.endsWith(".gltf")){const n=await e.loadJSON(t,r);return new B(e,t,n)}const n=await e.loadBinary(t,r);if(B._isGLBData(n))return this._fromGLBData(e,t,n);const o=await e.loadJSON(t,r);return new B(e,t,o)}static _isGLBData(e){if(null==e)return!1;const t=new S(e);return t.remainingBytes()>=4&&t.readUint32()===D}static async _fromGLBData(e,t,r){const n=await B._parseGLBData(r);return new B(e,t,n.json,n.binaryData)}static async _parseGLBData(e){const t=new S(e);if(t.remainingBytes()<12)throw new u.Z("gltf-loader-error","GLB binary data is insufficiently large.");const r=t.readUint32(),n=t.readUint32(),o=t.readUint32();if(r!==D)throw new u.Z("gltf-loader-error","Magic first 4 bytes do not fit to expected GLB value.");if(e.byteLength<o)throw new u.Z("gltf-loader-error","GLB binary data is smaller than header specifies.");if(2!==n)throw new u.Z("gltf-loader-unsupported-feature","An unsupported GLB container version was detected. Only version 2 is supported.");let a,i,_=0;for(;t.remainingBytes()>=8;){const e=t.readUint32(),r=t.readUint32();if(0===_){if(1313821514!==r)throw new u.Z("gltf-loader-error","First GLB chunk must be JSON.");if(e<0)throw new u.Z("gltf-loader-error","No JSON data found.");a=await(0,M.i$)(t.readUint8Array(e))}else if(1===_){if(5130562!==r)throw new u.Z("gltf-loader-unsupported-feature","Second GLB chunk expected to be BIN.");i=t.readUint8Array(e)}else s.Z.getLogger("esri.views.3d.glTF").warn("[Unsupported Feature] More than 2 GLB chunks detected. Skipping.");_+=1}if(!a)throw new u.Z("gltf-loader-error","No GLB JSON chunk detected.");return{json:a,binaryData:i}}async getBuffer(e,t){const r=this.json.buffers[e];if(null==r.uri){if(null==this._glbBuffer)throw new u.Z("gltf-loader-error","GLB buffer not present");return this._glbBuffer}const n=await this._getBufferLoader(e,t);if(n.byteLength!==r.byteLength)throw new u.Z("gltf-loader-error","Buffer byte lengths should match.");return n}async _getBufferLoader(e,t){const r=this._bufferLoaders.get(e);if(r)return r;const n=this.json.buffers[e].uri,o=this._context.loadBinary(this._resolveUri(n),t).then((e=>new Uint8Array(e)));return this._bufferLoaders.set(e,o),o}async getAccessor(e,t){if(!this.json.accessors)throw new u.Z("gltf-loader-unsupported-feature","Accessors missing.");const r=this.json.accessors[e];if(null==r?.bufferView)throw new u.Z("gltf-loader-unsupported-feature","Some accessor does not specify a bufferView.");if(r.type in[n.MAT2,n.MAT3,n.MAT4])throw new u.Z("gltf-loader-unsupported-feature",`AttributeType ${r.type} is not supported`);const o=this.json.bufferViews[r.bufferView],s=await this.getBuffer(o.buffer,t),a=P[r.type],i=w[r.componentType],_=a*i,E=o.byteStride||_;return{raw:s.buffer,byteStride:E,byteOffset:s.byteOffset+(o.byteOffset||0)+(r.byteOffset||0),entryCount:r.count,isDenselyPacked:E===_,componentCount:a,componentByteSize:i,componentType:r.componentType,min:r.min,max:r.max,normalized:!!r.normalized}}async getIndexData(e,t){if(null==e.indices)return;const r=await this.getAccessor(e.indices,t);if(r.isDenselyPacked)switch(r.componentType){case _.g.UNSIGNED_BYTE:return new Uint8Array(r.raw,r.byteOffset,r.entryCount);case _.g.UNSIGNED_SHORT:return new Uint16Array(r.raw,r.byteOffset,r.entryCount);case _.g.UNSIGNED_INT:return new Uint32Array(r.raw,r.byteOffset,r.entryCount)}else switch(r.componentType){case _.g.UNSIGNED_BYTE:return(0,N.m)(this._wrapAccessor(d.D_,r));case _.g.UNSIGNED_SHORT:return(0,N.m)(this._wrapAccessor(d.av,r));case _.g.UNSIGNED_INT:return(0,N.m)(this._wrapAccessor(d.Nu,r))}}async getPositionData(e,t){if(null==e.attributes.POSITION)throw new u.Z("gltf-loader-unsupported-feature","No POSITION vertex data found.");const r=await this.getAccessor(e.attributes.POSITION,t);if(r.componentType!==_.g.FLOAT)throw new u.Z("gltf-loader-unsupported-feature","Expected type FLOAT for POSITION vertex attribute, but found "+_.g[r.componentType]);if(3!==r.componentCount)throw new u.Z("gltf-loader-unsupported-feature","POSITION vertex attribute must have 3 components, but found "+r.componentCount.toFixed());return this._wrapAccessor(d.ct,r)}async getNormalData(e,t){if(null==e.attributes.NORMAL)throw new u.Z("gltf-loader-error","No NORMAL vertex data found.");const r=await this.getAccessor(e.attributes.NORMAL,t);if(r.componentType!==_.g.FLOAT)throw new u.Z("gltf-loader-unsupported-feature","Expected type FLOAT for NORMAL vertex attribute, but found "+_.g[r.componentType]);if(3!==r.componentCount)throw new u.Z("gltf-loader-unsupported-feature","NORMAL vertex attribute must have 3 components, but found "+r.componentCount.toFixed());return this._wrapAccessor(d.ct,r)}async getTangentData(e,t){if(null==e.attributes.TANGENT)throw new u.Z("gltf-loader-error","No TANGENT vertex data found.");const r=await this.getAccessor(e.attributes.TANGENT,t);if(r.componentType!==_.g.FLOAT)throw new u.Z("gltf-loader-unsupported-feature","Expected type FLOAT for TANGENT vertex attribute, but found "+_.g[r.componentType]);if(4!==r.componentCount)throw new u.Z("gltf-loader-unsupported-feature","TANGENT vertex attribute must have 4 components, but found "+r.componentCount.toFixed());return new d.ek(r.raw,r.byteOffset,r.byteStride,r.byteOffset+r.byteStride*r.entryCount)}async getTextureCoordinates(e,t){if(null==e.attributes.TEXCOORD_0)throw new u.Z("gltf-loader-error","No TEXCOORD_0 vertex data found.");const r=await this.getAccessor(e.attributes.TEXCOORD_0,t);if(2!==r.componentCount)throw new u.Z("gltf-loader-unsupported-feature","TEXCOORD_0 vertex attribute must have 2 components, but found "+r.componentCount.toFixed());if(r.componentType===_.g.FLOAT)return this._wrapAccessor(d.Eu,r);if(!r.normalized)throw new u.Z("gltf-loader-unsupported-feature","Integer component types are only supported for a normalized accessor for TEXCOORD_0.");return function(e){switch(e.componentType){case _.g.BYTE:return new d.Vs(e.raw,e.byteOffset,e.byteStride,e.byteOffset+e.byteStride*e.entryCount);case _.g.UNSIGNED_BYTE:return new d.xA(e.raw,e.byteOffset,e.byteStride,e.byteOffset+e.byteStride*e.entryCount);case _.g.SHORT:return new d.or(e.raw,e.byteOffset,e.byteStride,e.byteOffset+e.byteStride*e.entryCount);case _.g.UNSIGNED_SHORT:return new d.TS(e.raw,e.byteOffset,e.byteStride,e.byteOffset+e.byteStride*e.entryCount);case _.g.UNSIGNED_INT:return new d.qt(e.raw,e.byteOffset,e.byteStride,e.byteOffset+e.byteStride*e.entryCount);case _.g.FLOAT:return new d.Eu(e.raw,e.byteOffset,e.byteStride,e.byteOffset+e.byteStride*e.entryCount)}}(r)}async getVertexColors(e,t){if(null==e.attributes.COLOR_0)throw new u.Z("gltf-loader-error","No COLOR_0 vertex data found.");const r=await this.getAccessor(e.attributes.COLOR_0,t);if(4!==r.componentCount&&3!==r.componentCount)throw new u.Z("gltf-loader-unsupported-feature","COLOR_0 attribute must have 3 or 4 components, but found "+r.componentCount.toFixed());if(4===r.componentCount){if(r.componentType===_.g.FLOAT)return this._wrapAccessor(d.ek,r);if(r.componentType===_.g.UNSIGNED_BYTE)return this._wrapAccessor(d.mc,r);if(r.componentType===_.g.UNSIGNED_SHORT)return this._wrapAccessor(d.v6,r)}else if(3===r.componentCount){if(r.componentType===_.g.FLOAT)return this._wrapAccessor(d.ct,r);if(r.componentType===_.g.UNSIGNED_BYTE)return this._wrapAccessor(d.ne,r);if(r.componentType===_.g.UNSIGNED_SHORT)return this._wrapAccessor(d.mw,r)}throw new u.Z("gltf-loader-unsupported-feature","Unsupported component type for COLOR_0 attribute: "+_.g[r.componentType])}hasPositions(e){return void 0!==e.attributes.POSITION}hasNormals(e){return void 0!==e.attributes.NORMAL}hasVertexColors(e){return void 0!==e.attributes.COLOR_0}hasTextureCoordinates(e){return void 0!==e.attributes.TEXCOORD_0}hasTangents(e){return void 0!==e.attributes.TANGENT}async getMaterial(e,t,r){let n=e.material?this._materialCache.get(e.material):void 0;if(!n){const o=null!=e.material?L(this.json.materials[e.material]):L(),s=o.pbrMetallicRoughness,a=this.hasVertexColors(e),i=this.getTexture(s.baseColorTexture,t),_=this.getTexture(o.normalTexture,t),u=r?this.getTexture(o.occlusionTexture,t):void 0,E=r?this.getTexture(o.emissiveTexture,t):void 0,T=r?this.getTexture(s.metallicRoughnessTexture,t):void 0,c=null!=e.material?e.material:-1;n={alphaMode:o.alphaMode,alphaCutoff:o.alphaCutoff,color:s.baseColorFactor,doubleSided:!!o.doubleSided,colorTexture:await i,normalTexture:await _,name:o.name,id:c,occlusionTexture:await u,emissiveTexture:await E,emissiveFactor:o.emissiveFactor,metallicFactor:s.metallicFactor,roughnessFactor:s.roughnessFactor,metallicRoughnessTexture:await T,hasVertexColors:a,ESRI_externalColorMixMode:o.extras.ESRI_externalColorMixMode,colorTextureTransform:s?.baseColorTexture?.extensions?.KHR_texture_transform,normalTextureTransform:o.normalTexture?.extensions?.KHR_texture_transform,occlusionTextureTransform:o.occlusionTexture?.extensions?.KHR_texture_transform,emissiveTextureTransform:o.emissiveTexture?.extensions?.KHR_texture_transform,metallicRoughnessTextureTransform:s?.metallicRoughnessTexture?.extensions?.KHR_texture_transform}}return n}async getTexture(e,t){if(!e)return;if(0!==(e.texCoord||0))throw new u.Z("gltf-loader-unsupported-feature","Only TEXCOORD with index 0 is supported.");const r=e.index,n=this.json.textures[r],o=m(null!=n.sampler?this.json.samplers[n.sampler]:{}),s=this._getTextureSourceId(n),a=this.json.images[s],i=await this._loadTextureImageData(r,n,t);return(0,E.s1)(this._textureCache,r,(()=>{const e=e=>33071===e||33648===e||10497===e,t=e=>{throw new u.Z("gltf-loader-error",`Unexpected TextureSampler WrapMode: ${e}`)};return{data:i,wrapS:e(o.wrapS)?o.wrapS:t(o.wrapS),wrapT:e(o.wrapT)?o.wrapT:t(o.wrapT),minFilter:o.minFilter,name:a.name,id:r}}))}getNodeTransform(e){if(void 0===e)return U;let t=this._nodeTransformCache.get(e);if(!t){const r=this.getNodeTransform(this._getNodeParent(e)),n=this.json.nodes[e];n.matrix?t=(0,f.m)((0,i.c)(),r,n.matrix):n.translation||n.rotation||n.scale?(t=(0,i.b)(r),n.translation&&(0,f.w)(t,t,n.translation),n.rotation&&(G[3]=(0,A.g)(G,n.rotation),(0,f.e)(t,t,G[3],G)),n.scale&&(0,f.k)(t,t,n.scale)):t=(0,i.b)(r),this._nodeTransformCache.set(e,t)}return t}_wrapAccessor(e,t){return new e(t.raw,t.byteOffset,t.byteStride,t.byteOffset+t.byteStride*(t.entryCount-1)+t.componentByteSize*t.componentCount)}_resolveUri(e){return(0,c.hF)(e,this._baseUri)}_getNodeParent(e){return this._nodeParentMap.get(e)}_checkVersionSupported(){const e=R.G.parse(this.json.asset.version,"glTF");y.validate(e)}_checkRequiredExtensionsSupported(){const e=this.json;if(e.extensionsRequired&&!e.extensionsRequired.every((e=>this._supportedExtensions.includes(e))))throw new u.Z("gltf-loader-unsupported-feature","gltf loader was not able to load unsupported feature. Required extensions: "+e.extensionsRequired.join(", "))}_computeNodeParents(){this.json.nodes.forEach(((e,t)=>{e.children&&e.children.forEach((e=>{this._nodeParentMap.set(e,t)}))}))}async _loadTextureImageData(e,t,r){const n=this._textureLoaders.get(e);if(n)return n;const o=this._createTextureLoader(t,r);return this._textureLoaders.set(e,o),o}_getTextureSourceId(e){if(void 0!==e.extensions&&null!==e.extensions.KHR_texture_basisu)return e.extensions.KHR_texture_basisu.source;if(null!==e.source)return e.source;throw new u.Z("gltf-loader-unsupported-feature","Source is expected to be defined for a texture. It can also be omitted in favour of an KHR_texture_basisu extension tag.")}async _createTextureLoader(e,t){const r=this._getTextureSourceId(e),n=this.json.images[r];if(n.uri){if(n.uri.endsWith(".ktx2")){const e=await this._context.loadBinary(this._resolveUri(n.uri),t);return new M.NM(new Uint8Array(e))}return this._context.loadImage(this._resolveUri(n.uri),t)}if(null==n.bufferView)throw new u.Z("gltf-loader-unsupported-feature","Image bufferView must be defined.");if(null==n.mimeType)throw new u.Z("gltf-loader-unsupported-feature","Image mimeType must be defined.");const o=this.json.bufferViews[n.bufferView],s=await this.getBuffer(o.buffer,t);if(null!=o.byteStride)throw new u.Z("gltf-loader-unsupported-feature","byteStride not supported for image buffer");const a=s.byteOffset+(o.byteOffset||0);return(0,M.Ml)(new Uint8Array(s.buffer,a,o.byteLength),n.mimeType)}async getLoadedBuffersSize(){if(this._glbBuffer)return this._glbBuffer.byteLength;const e=await(0,T.WW)(Array.from(this._bufferLoaders.values())),t=await(0,T.WW)(Array.from(this._textureLoaders.values()));return e.reduce(((e,t)=>e+(t?.byteLength??0)),0)+t.reduce(((e,t)=>e+(t?(0,M.$A)(t)?t.data.byteLength:t.width*t.height*4:0)),0)}}const U=(0,f.A)((0,i.c)(),Math.PI/2),y=new R.G(2,0,"glTF"),G=(0,l.a)(),P={SCALAR:1,VEC2:2,VEC3:3,VEC4:4},w={[_.g.BYTE]:1,[_.g.UNSIGNED_BYTE]:1,[_.g.SHORT]:2,[_.g.UNSIGNED_SHORT]:2,[_.g.FLOAT]:4,[_.g.UNSIGNED_INT]:4};let g=0;async function x(e,t,r={},n=!0){const o=await B.load(e,t,r),u="gltf_"+g++,E={lods:[],materials:new Map,textures:new Map,meta:F(o)},T=!(!o.json.asset.extras||"symbolResource"!==o.json.asset.extras.ESRI_type),c=new Map;await b(o,(async(e,t,T,R)=>{const f=c.get(T)??0;c.set(T,f+1);const A=void 0!==e.mode?e.mode:_.MX.TRIANGLES,l=A===_.MX.TRIANGLES||A===_.MX.TRIANGLE_STRIP||A===_.MX.TRIANGLE_FAN?A:null;if((0,a.Wi)(l))return void s.Z.getLogger("esri.views.3d.glTF").warn("[Unsupported Feature] Unsupported primitive mode ("+_.MX[A]+"). Skipping primitive.");if(!o.hasPositions(e))return void s.Z.getLogger("esri.views.3d.glTF").warn("Skipping primitive without POSITION vertex attribute.");const d=o.getPositionData(e,r),N=o.getMaterial(e,r,n),S=o.hasNormals(e)?o.getNormalData(e,r):null,O=o.hasTangents(e)?o.getTangentData(e,r):null,C=o.hasTextureCoordinates(e)?o.getTextureCoordinates(e,r):null,I=o.hasVertexColors(e)?o.getVertexColors(e,r):null,p=o.getIndexData(e,r),L={transform:(0,i.b)(t),attributes:{position:await d,normal:S?await S:null,texCoord0:C?await C:null,color:I?await I:null,tangent:O?await O:null},indices:await p,primitiveType:l,material:v(E,await N,u)};let h=null;(0,a.pC)(E.meta)&&(0,a.pC)(E.meta.ESRI_lod)&&"screenSpaceRadius"===E.meta.ESRI_lod.metric&&(h=E.meta.ESRI_lod.thresholds[T]),E.lods[T]=E.lods[T]||{parts:[],name:R,lodThreshold:h},E.lods[T].parts[f]=L}));for(const e of E.lods)e.parts=e.parts.filter((e=>!!e));const R=await o.getLoadedBuffersSize();return{model:E,meta:{isEsriSymbolResource:T,uri:o.uri},customMeta:{},size:R}}function F(e){const t=e.json;let r=null;return t.nodes.forEach((e=>{const t=e.extras;(0,a.pC)(t)&&(t.ESRI_proxyEllipsoid||t.ESRI_lod)&&(r=t)})),r}async function b(e,t){const r=e.json,n=r.scenes[r.scene||0].nodes,o=n.length>1,a=[];for(const e of n){const t=r.nodes[e];a.push(i(e,0)),H(t)&&!o&&t.extensions.MSFT_lod.ids.forEach(((e,t)=>i(e,t+1)))}async function i(n,o){const _=r.nodes[n],u=e.getNodeTransform(n);if(null!=_.weights&&s.Z.getLogger("esri.views.3d.glTF").warn("[Unsupported Feature] Morph targets are not supported."),null!=_.mesh){const e=r.meshes[_.mesh];for(const r of e.primitives)a.push(t(r,u,o,e.name))}for(const e of _.children||[])a.push(i(e,o))}await Promise.all(a)}function H(e){return e.extensions&&e.extensions.MSFT_lod&&Array.isArray(e.extensions.MSFT_lod.ids)}function v(e,t,r){const n=t=>{const n=`${r}_tex_${t&&t.id}${t&&t.name?"_"+t.name:""}`;if(t&&!e.textures.has(n)){const r=function(e,t={}){return{data:e,parameters:{wrap:{s:_.e8.REPEAT,t:_.e8.REPEAT,...t.wrap},noUnpackFlip:!0,mipmap:!1,...t}}}(t.data,{wrap:{s:t.wrapS,t:t.wrapT},mipmap:V.includes(t.minFilter),noUnpackFlip:!0});e.textures.set(n,r)}return n},o=`${r}_mat_${t.id}_${t.name}`;if(!e.materials.has(o)){const r=function(e={}){return{color:[1,1,1],opacity:1,alphaMode:"OPAQUE",alphaCutoff:.5,doubleSided:!1,castShadows:!0,receiveShadows:!0,receiveAmbientOcclustion:!0,textureColor:null,textureNormal:null,textureOcclusion:null,textureEmissive:null,textureMetallicRoughness:null,colorTextureTransform:null,normalTextureTransform:null,occlusionTextureTransform:null,emissiveTextureTransform:null,metallicRoughnessTextureTransform:null,emissiveFactor:[0,0,0],metallicFactor:1,roughnessFactor:1,colorMixMode:"multiply",...e}}({color:[t.color[0],t.color[1],t.color[2]],opacity:t.color[3],alphaMode:t.alphaMode,alphaCutoff:t.alphaCutoff,doubleSided:t.doubleSided,colorMixMode:t.ESRI_externalColorMixMode,textureColor:t.colorTexture?n(t.colorTexture):void 0,textureNormal:t.normalTexture?n(t.normalTexture):void 0,textureOcclusion:t.occlusionTexture?n(t.occlusionTexture):void 0,textureEmissive:t.emissiveTexture?n(t.emissiveTexture):void 0,textureMetallicRoughness:t.metallicRoughnessTexture?n(t.metallicRoughnessTexture):void 0,emissiveFactor:[t.emissiveFactor[0],t.emissiveFactor[1],t.emissiveFactor[2]],colorTextureTransform:t.colorTextureTransform,normalTextureTransform:t.normalTextureTransform,occlusionTextureTransform:t.occlusionTextureTransform,emissiveTextureTransform:t.emissiveTextureTransform,metallicRoughnessTextureTransform:t.metallicRoughnessTextureTransform,metallicFactor:t.metallicFactor,roughnessFactor:t.roughnessFactor});e.materials.set(o,r)}return o}const V=[_.cw.LINEAR_MIPMAP_LINEAR,_.cw.LINEAR_MIPMAP_NEAREST]},47026:(e,t,r)=>{var n,o,s,a,i,_,u,E,T,c,R;r.d(t,{CE:()=>u,Gv:()=>o,JJ:()=>T,Rw:()=>a,Ti:()=>R,V_:()=>_,Vr:()=>n,hU:()=>i}),function(e){e[e.None=0]="None",e[e.Front=1]="Front",e[e.Back=2]="Back",e[e.COUNT=3]="COUNT"}(n||(n={})),function(e){e[e.Less=0]="Less",e[e.Lequal=1]="Lequal",e[e.COUNT=2]="COUNT"}(o||(o={})),function(e){e[e.BACKGROUND=0]="BACKGROUND",e[e.UPDATE=1]="UPDATE"}(s||(s={})),function(e){e[e.NOT_LOADED=0]="NOT_LOADED",e[e.LOADING=1]="LOADING",e[e.LOADED=2]="LOADED"}(a||(a={})),function(e){e[e.IntegratedMeshMaskExcluded=1]="IntegratedMeshMaskExcluded",e[e.OutlineVisualElementMask=2]="OutlineVisualElementMask"}(i||(i={})),function(e){e[e.Highlight=0]="Highlight",e[e.MaskOccludee=1]="MaskOccludee",e[e.COUNT=2]="COUNT"}(_||(_={})),function(e){e[e.STRETCH=1]="STRETCH",e[e.PAD=2]="PAD"}(u||(u={})),function(e){e[e.CHANGED=0]="CHANGED",e[e.UNCHANGED=1]="UNCHANGED"}(E||(E={})),function(e){e[e.Blend=0]="Blend",e[e.Opaque=1]="Opaque",e[e.Mask=2]="Mask",e[e.MaskBlend=3]="MaskBlend",e[e.COUNT=4]="COUNT"}(T||(T={})),function(e){e[e.OFF=0]="OFF",e[e.ON=1]="ON"}(c||(c={})),function(e){e.DDS_ENCODING="image/vnd-ms.dds",e.KTX2_ENCODING="image/ktx2",e.BASIS_ENCODING="image/x.basis"}(R||(R={}))},57758:(e,t,r)=>{r.d(t,{K:()=>n});const n=2.1},35371:(e,t,r)=>{var n,o,s,a,i,_,u,E,T,c,R,f,A,l,d,N,S,O,C,I,p,L,h,m;r.d(t,{Br:()=>N,LR:()=>_,Lm:()=>p,Lu:()=>M,MX:()=>o,No:()=>A,OU:()=>L,Tg:()=>S,VI:()=>l,VY:()=>m,Wf:()=>u,_g:()=>h,cw:()=>R,db:()=>a,e8:()=>f,g:()=>E,l1:()=>O,lP:()=>d,q_:()=>D,qi:()=>I,w0:()=>i,wb:()=>T,xS:()=>c,zi:()=>s}),function(e){e[e.DEPTH_BUFFER_BIT=256]="DEPTH_BUFFER_BIT",e[e.STENCIL_BUFFER_BIT=1024]="STENCIL_BUFFER_BIT",e[e.COLOR_BUFFER_BIT=16384]="COLOR_BUFFER_BIT"}(n||(n={})),function(e){e[e.POINTS=0]="POINTS",e[e.LINES=1]="LINES",e[e.LINE_LOOP=2]="LINE_LOOP",e[e.LINE_STRIP=3]="LINE_STRIP",e[e.TRIANGLES=4]="TRIANGLES",e[e.TRIANGLE_STRIP=5]="TRIANGLE_STRIP",e[e.TRIANGLE_FAN=6]="TRIANGLE_FAN"}(o||(o={})),function(e){e[e.ZERO=0]="ZERO",e[e.ONE=1]="ONE",e[e.SRC_COLOR=768]="SRC_COLOR",e[e.ONE_MINUS_SRC_COLOR=769]="ONE_MINUS_SRC_COLOR",e[e.SRC_ALPHA=770]="SRC_ALPHA",e[e.ONE_MINUS_SRC_ALPHA=771]="ONE_MINUS_SRC_ALPHA",e[e.DST_ALPHA=772]="DST_ALPHA",e[e.ONE_MINUS_DST_ALPHA=773]="ONE_MINUS_DST_ALPHA",e[e.DST_COLOR=774]="DST_COLOR",e[e.ONE_MINUS_DST_COLOR=775]="ONE_MINUS_DST_COLOR",e[e.SRC_ALPHA_SATURATE=776]="SRC_ALPHA_SATURATE",e[e.CONSTANT_COLOR=32769]="CONSTANT_COLOR",e[e.ONE_MINUS_CONSTANT_COLOR=32770]="ONE_MINUS_CONSTANT_COLOR",e[e.CONSTANT_ALPHA=32771]="CONSTANT_ALPHA",e[e.ONE_MINUS_CONSTANT_ALPHA=32772]="ONE_MINUS_CONSTANT_ALPHA"}(s||(s={})),function(e){e[e.ADD=32774]="ADD",e[e.SUBTRACT=32778]="SUBTRACT",e[e.REVERSE_SUBTRACT=32779]="REVERSE_SUBTRACT"}(a||(a={})),function(e){e[e.ARRAY_BUFFER=34962]="ARRAY_BUFFER",e[e.ELEMENT_ARRAY_BUFFER=34963]="ELEMENT_ARRAY_BUFFER",e[e.UNIFORM_BUFFER=35345]="UNIFORM_BUFFER",e[e.PIXEL_PACK_BUFFER=35051]="PIXEL_PACK_BUFFER",e[e.PIXEL_UNPACK_BUFFER=35052]="PIXEL_UNPACK_BUFFER",e[e.COPY_READ_BUFFER=36662]="COPY_READ_BUFFER",e[e.COPY_WRITE_BUFFER=36663]="COPY_WRITE_BUFFER"}(i||(i={})),function(e){e[e.FRONT=1028]="FRONT",e[e.BACK=1029]="BACK",e[e.FRONT_AND_BACK=1032]="FRONT_AND_BACK"}(_||(_={})),function(e){e[e.CW=2304]="CW",e[e.CCW=2305]="CCW"}(u||(u={})),function(e){e[e.BYTE=5120]="BYTE",e[e.UNSIGNED_BYTE=5121]="UNSIGNED_BYTE",e[e.SHORT=5122]="SHORT",e[e.UNSIGNED_SHORT=5123]="UNSIGNED_SHORT",e[e.INT=5124]="INT",e[e.UNSIGNED_INT=5125]="UNSIGNED_INT",e[e.FLOAT=5126]="FLOAT"}(E||(E={})),function(e){e[e.NEVER=512]="NEVER",e[e.LESS=513]="LESS",e[e.EQUAL=514]="EQUAL",e[e.LEQUAL=515]="LEQUAL",e[e.GREATER=516]="GREATER",e[e.NOTEQUAL=517]="NOTEQUAL",e[e.GEQUAL=518]="GEQUAL",e[e.ALWAYS=519]="ALWAYS"}(T||(T={})),function(e){e[e.ZERO=0]="ZERO",e[e.KEEP=7680]="KEEP",e[e.REPLACE=7681]="REPLACE",e[e.INCR=7682]="INCR",e[e.DECR=7683]="DECR",e[e.INVERT=5386]="INVERT",e[e.INCR_WRAP=34055]="INCR_WRAP",e[e.DECR_WRAP=34056]="DECR_WRAP"}(c||(c={})),function(e){e[e.NEAREST=9728]="NEAREST",e[e.LINEAR=9729]="LINEAR",e[e.NEAREST_MIPMAP_NEAREST=9984]="NEAREST_MIPMAP_NEAREST",e[e.LINEAR_MIPMAP_NEAREST=9985]="LINEAR_MIPMAP_NEAREST",e[e.NEAREST_MIPMAP_LINEAR=9986]="NEAREST_MIPMAP_LINEAR",e[e.LINEAR_MIPMAP_LINEAR=9987]="LINEAR_MIPMAP_LINEAR"}(R||(R={})),function(e){e[e.CLAMP_TO_EDGE=33071]="CLAMP_TO_EDGE",e[e.REPEAT=10497]="REPEAT",e[e.MIRRORED_REPEAT=33648]="MIRRORED_REPEAT"}(f||(f={})),function(e){e[e.TEXTURE_2D=3553]="TEXTURE_2D",e[e.TEXTURE_CUBE_MAP=34067]="TEXTURE_CUBE_MAP",e[e.TEXTURE_3D=32879]="TEXTURE_3D",e[e.TEXTURE_CUBE_MAP_POSITIVE_X=34069]="TEXTURE_CUBE_MAP_POSITIVE_X",e[e.TEXTURE_CUBE_MAP_NEGATIVE_X=34070]="TEXTURE_CUBE_MAP_NEGATIVE_X",e[e.TEXTURE_CUBE_MAP_POSITIVE_Y=34071]="TEXTURE_CUBE_MAP_POSITIVE_Y",e[e.TEXTURE_CUBE_MAP_NEGATIVE_Y=34072]="TEXTURE_CUBE_MAP_NEGATIVE_Y",e[e.TEXTURE_CUBE_MAP_POSITIVE_Z=34073]="TEXTURE_CUBE_MAP_POSITIVE_Z",e[e.TEXTURE_CUBE_MAP_NEGATIVE_Z=34074]="TEXTURE_CUBE_MAP_NEGATIVE_Z",e[e.TEXTURE_2D_ARRAY=35866]="TEXTURE_2D_ARRAY"}(A||(A={})),function(e){e[e.DEPTH_COMPONENT=6402]="DEPTH_COMPONENT",e[e.DEPTH_STENCIL=34041]="DEPTH_STENCIL",e[e.ALPHA=6406]="ALPHA",e[e.RGB=6407]="RGB",e[e.RGBA=6408]="RGBA",e[e.LUMINANCE=6409]="LUMINANCE",e[e.LUMINANCE_ALPHA=6410]="LUMINANCE_ALPHA",e[e.RED=6403]="RED",e[e.RG=33319]="RG",e[e.RED_INTEGER=36244]="RED_INTEGER",e[e.RG_INTEGER=33320]="RG_INTEGER",e[e.RGB_INTEGER=36248]="RGB_INTEGER",e[e.RGBA_INTEGER=36249]="RGBA_INTEGER"}(l||(l={})),function(e){e[e.RGBA4=32854]="RGBA4",e[e.R16F=33325]="R16F",e[e.RG16F=33327]="RG16F",e[e.RGB32F=34837]="RGB32F",e[e.RGBA16F=34842]="RGBA16F",e[e.R32F=33326]="R32F",e[e.RG32F=33328]="RG32F",e[e.RGBA32F=34836]="RGBA32F",e[e.R11F_G11F_B10F=35898]="R11F_G11F_B10F",e[e.RGB8=32849]="RGB8",e[e.RGBA8=32856]="RGBA8",e[e.RGB5_A1=32855]="RGB5_A1",e[e.R8=33321]="R8",e[e.RG8=33323]="RG8",e[e.R8I=33329]="R8I",e[e.R8UI=33330]="R8UI",e[e.R16I=33331]="R16I",e[e.R16UI=33332]="R16UI",e[e.R32I=33333]="R32I",e[e.R32UI=33334]="R32UI",e[e.RG8I=33335]="RG8I",e[e.RG8UI=33336]="RG8UI",e[e.RG16I=33337]="RG16I",e[e.RG16UI=33338]="RG16UI",e[e.RG32I=33339]="RG32I",e[e.RG32UI=33340]="RG32UI",e[e.RGB16F=34843]="RGB16F",e[e.RGB9_E5=35901]="RGB9_E5",e[e.SRGB8=35905]="SRGB8",e[e.SRGB8_ALPHA8=35907]="SRGB8_ALPHA8",e[e.RGB565=36194]="RGB565",e[e.RGBA32UI=36208]="RGBA32UI",e[e.RGB32UI=36209]="RGB32UI",e[e.RGBA16UI=36214]="RGBA16UI",e[e.RGB16UI=36215]="RGB16UI",e[e.RGBA8UI=36220]="RGBA8UI",e[e.RGB8UI=36221]="RGB8UI",e[e.RGBA32I=36226]="RGBA32I",e[e.RGB32I=36227]="RGB32I",e[e.RGBA16I=36232]="RGBA16I",e[e.RGB16I=36233]="RGB16I",e[e.RGBA8I=36238]="RGBA8I",e[e.RGB8I=36239]="RGB8I",e[e.R8_SNORM=36756]="R8_SNORM",e[e.RG8_SNORM=36757]="RG8_SNORM",e[e.RGB8_SNORM=36758]="RGB8_SNORM",e[e.RGBA8_SNORM=36759]="RGBA8_SNORM",e[e.RGB10_A2=32857]="RGB10_A2",e[e.RGB10_A2UI=36975]="RGB10_A2UI"}(d||(d={})),function(e){e[e.FLOAT=5126]="FLOAT",e[e.UNSIGNED_BYTE=5121]="UNSIGNED_BYTE",e[e.UNSIGNED_INT_24_8=34042]="UNSIGNED_INT_24_8",e[e.UNSIGNED_SHORT_4_4_4_4=32819]="UNSIGNED_SHORT_4_4_4_4",e[e.UNSIGNED_SHORT_5_5_5_1=32820]="UNSIGNED_SHORT_5_5_5_1",e[e.UNSIGNED_SHORT_5_6_5=33635]="UNSIGNED_SHORT_5_6_5",e[e.BYTE=5120]="BYTE",e[e.UNSIGNED_SHORT=5123]="UNSIGNED_SHORT",e[e.SHORT=5122]="SHORT",e[e.UNSIGNED_INT=5125]="UNSIGNED_INT",e[e.INT=5124]="INT",e[e.HALF_FLOAT=5131]="HALF_FLOAT",e[e.UNSIGNED_INT_2_10_10_10_REV=33640]="UNSIGNED_INT_2_10_10_10_REV",e[e.UNSIGNED_INT_10F_11F_11F_REV=35899]="UNSIGNED_INT_10F_11F_11F_REV",e[e.UNSIGNED_INT_5_9_9_9_REV=35902]="UNSIGNED_INT_5_9_9_9_REV",e[e.FLOAT_32_UNSIGNED_INT_24_8_REV=36269]="FLOAT_32_UNSIGNED_INT_24_8_REV"}(N||(N={})),function(e){e[e.DEPTH_COMPONENT16=33189]="DEPTH_COMPONENT16",e[e.STENCIL_INDEX8=36168]="STENCIL_INDEX8",e[e.DEPTH_STENCIL=34041]="DEPTH_STENCIL",e[e.DEPTH_COMPONENT24=33190]="DEPTH_COMPONENT24",e[e.DEPTH_COMPONENT32F=36012]="DEPTH_COMPONENT32F",e[e.DEPTH24_STENCIL8=35056]="DEPTH24_STENCIL8",e[e.DEPTH32F_STENCIL8=36013]="DEPTH32F_STENCIL8"}(S||(S={})),function(e){e[e.STATIC_DRAW=35044]="STATIC_DRAW",e[e.DYNAMIC_DRAW=35048]="DYNAMIC_DRAW",e[e.STREAM_DRAW=35040]="STREAM_DRAW",e[e.STATIC_READ=35045]="STATIC_READ",e[e.DYNAMIC_READ=35049]="DYNAMIC_READ",e[e.STREAM_READ=35041]="STREAM_READ",e[e.STATIC_COPY=35046]="STATIC_COPY",e[e.DYNAMIC_COPY=35050]="DYNAMIC_COPY",e[e.STREAM_COPY=35042]="STREAM_COPY"}(O||(O={})),function(e){e[e.FRAGMENT_SHADER=35632]="FRAGMENT_SHADER",e[e.VERTEX_SHADER=35633]="VERTEX_SHADER"}(C||(C={})),function(e){e[e.FRAMEBUFFER=36160]="FRAMEBUFFER",e[e.READ_FRAMEBUFFER=36008]="READ_FRAMEBUFFER",e[e.DRAW_FRAMEBUFFER=36009]="DRAW_FRAMEBUFFER"}(I||(I={})),function(e){e[e.TEXTURE=0]="TEXTURE",e[e.RENDER_BUFFER=1]="RENDER_BUFFER",e[e.CUBEMAP=2]="CUBEMAP"}(p||(p={})),function(e){e[e.NONE=0]="NONE",e[e.DEPTH_RENDER_BUFFER=1]="DEPTH_RENDER_BUFFER",e[e.STENCIL_RENDER_BUFFER=2]="STENCIL_RENDER_BUFFER",e[e.DEPTH_STENCIL_RENDER_BUFFER=3]="DEPTH_STENCIL_RENDER_BUFFER",e[e.DEPTH_STENCIL_TEXTURE=4]="DEPTH_STENCIL_TEXTURE"}(L||(L={})),function(e){e[e.Texture=0]="Texture",e[e.BufferObject=1]="BufferObject",e[e.VertexArrayObject=2]="VertexArrayObject",e[e.Shader=3]="Shader",e[e.Program=4]="Program",e[e.FramebufferObject=5]="FramebufferObject",e[e.Renderbuffer=6]="Renderbuffer",e[e.Sync=7]="Sync",e[e.COUNT=8]="COUNT"}(h||(h={})),function(e){e[e.COLOR_ATTACHMENT0=36064]="COLOR_ATTACHMENT0",e[e.COLOR_ATTACHMENT1=36065]="COLOR_ATTACHMENT1",e[e.COLOR_ATTACHMENT2=36066]="COLOR_ATTACHMENT2",e[e.COLOR_ATTACHMENT3=36067]="COLOR_ATTACHMENT3",e[e.COLOR_ATTACHMENT4=36068]="COLOR_ATTACHMENT4",e[e.COLOR_ATTACHMENT5=36069]="COLOR_ATTACHMENT5",e[e.COLOR_ATTACHMENT6=36070]="COLOR_ATTACHMENT6",e[e.COLOR_ATTACHMENT7=36071]="COLOR_ATTACHMENT7",e[e.COLOR_ATTACHMENT8=36072]="COLOR_ATTACHMENT8",e[e.COLOR_ATTACHMENT9=36073]="COLOR_ATTACHMENT9",e[e.COLOR_ATTACHMENT10=36074]="COLOR_ATTACHMENT10",e[e.COLOR_ATTACHMENT11=36075]="COLOR_ATTACHMENT11",e[e.COLOR_ATTACHMENT12=36076]="COLOR_ATTACHMENT12",e[e.COLOR_ATTACHMENT13=36077]="COLOR_ATTACHMENT13",e[e.COLOR_ATTACHMENT14=36078]="COLOR_ATTACHMENT14",e[e.COLOR_ATTACHMENT15=36079]="COLOR_ATTACHMENT15"}(m||(m={}));const M=33306;var D,B,U,y,G,P,w;!function(e){e[e.COMPRESSED_RGB_S3TC_DXT1_EXT=33776]="COMPRESSED_RGB_S3TC_DXT1_EXT",e[e.COMPRESSED_RGBA_S3TC_DXT1_EXT=33777]="COMPRESSED_RGBA_S3TC_DXT1_EXT",e[e.COMPRESSED_RGBA_S3TC_DXT3_EXT=33778]="COMPRESSED_RGBA_S3TC_DXT3_EXT",e[e.COMPRESSED_RGBA_S3TC_DXT5_EXT=33779]="COMPRESSED_RGBA_S3TC_DXT5_EXT",e[e.COMPRESSED_R11_EAC=37488]="COMPRESSED_R11_EAC",e[e.COMPRESSED_SIGNED_R11_EAC=37489]="COMPRESSED_SIGNED_R11_EAC",e[e.COMPRESSED_RG11_EAC=37490]="COMPRESSED_RG11_EAC",e[e.COMPRESSED_SIGNED_RG11_EAC=37491]="COMPRESSED_SIGNED_RG11_EAC",e[e.COMPRESSED_RGB8_ETC2=37492]="COMPRESSED_RGB8_ETC2",e[e.COMPRESSED_SRGB8_ETC2=37493]="COMPRESSED_SRGB8_ETC2",e[e.COMPRESSED_RGB8_PUNCHTHROUGH_ALPHA1_ETC2=37494]="COMPRESSED_RGB8_PUNCHTHROUGH_ALPHA1_ETC2",e[e.COMPRESSED_SRGB8_PUNCHTHROUGH_ALPHA1_ETC2=37495]="COMPRESSED_SRGB8_PUNCHTHROUGH_ALPHA1_ETC2",e[e.COMPRESSED_RGBA8_ETC2_EAC=37496]="COMPRESSED_RGBA8_ETC2_EAC",e[e.COMPRESSED_SRGB8_ALPHA8_ETC2_EAC=37497]="COMPRESSED_SRGB8_ALPHA8_ETC2_EAC"}(D||(D={})),function(e){e[e.FLOAT=5126]="FLOAT",e[e.FLOAT_VEC2=35664]="FLOAT_VEC2",e[e.FLOAT_VEC3=35665]="FLOAT_VEC3",e[e.FLOAT_VEC4=35666]="FLOAT_VEC4",e[e.INT=5124]="INT",e[e.INT_VEC2=35667]="INT_VEC2",e[e.INT_VEC3=35668]="INT_VEC3",e[e.INT_VEC4=35669]="INT_VEC4",e[e.BOOL=35670]="BOOL",e[e.BOOL_VEC2=35671]="BOOL_VEC2",e[e.BOOL_VEC3=35672]="BOOL_VEC3",e[e.BOOL_VEC4=35673]="BOOL_VEC4",e[e.FLOAT_MAT2=35674]="FLOAT_MAT2",e[e.FLOAT_MAT3=35675]="FLOAT_MAT3",e[e.FLOAT_MAT4=35676]="FLOAT_MAT4",e[e.SAMPLER_2D=35678]="SAMPLER_2D",e[e.SAMPLER_CUBE=35680]="SAMPLER_CUBE",e[e.UNSIGNED_INT=5125]="UNSIGNED_INT",e[e.UNSIGNED_INT_VEC2=36294]="UNSIGNED_INT_VEC2",e[e.UNSIGNED_INT_VEC3=36295]="UNSIGNED_INT_VEC3",e[e.UNSIGNED_INT_VEC4=36296]="UNSIGNED_INT_VEC4",e[e.FLOAT_MAT2x3=35685]="FLOAT_MAT2x3",e[e.FLOAT_MAT2x4=35686]="FLOAT_MAT2x4",e[e.FLOAT_MAT3x2=35687]="FLOAT_MAT3x2",e[e.FLOAT_MAT3x4=35688]="FLOAT_MAT3x4",e[e.FLOAT_MAT4x2=35689]="FLOAT_MAT4x2",e[e.FLOAT_MAT4x3=35690]="FLOAT_MAT4x3",e[e.SAMPLER_3D=35679]="SAMPLER_3D",e[e.SAMPLER_2D_SHADOW=35682]="SAMPLER_2D_SHADOW",e[e.SAMPLER_2D_ARRAY=36289]="SAMPLER_2D_ARRAY",e[e.SAMPLER_2D_ARRAY_SHADOW=36292]="SAMPLER_2D_ARRAY_SHADOW",e[e.SAMPLER_CUBE_SHADOW=36293]="SAMPLER_CUBE_SHADOW",e[e.INT_SAMPLER_2D=36298]="INT_SAMPLER_2D",e[e.INT_SAMPLER_3D=36299]="INT_SAMPLER_3D",e[e.INT_SAMPLER_CUBE=36300]="INT_SAMPLER_CUBE",e[e.INT_SAMPLER_2D_ARRAY=36303]="INT_SAMPLER_2D_ARRAY",e[e.UNSIGNED_INT_SAMPLER_2D=36306]="UNSIGNED_INT_SAMPLER_2D",e[e.UNSIGNED_INT_SAMPLER_3D=36307]="UNSIGNED_INT_SAMPLER_3D",e[e.UNSIGNED_INT_SAMPLER_CUBE=36308]="UNSIGNED_INT_SAMPLER_CUBE",e[e.UNSIGNED_INT_SAMPLER_2D_ARRAY=36311]="UNSIGNED_INT_SAMPLER_2D_ARRAY"}(B||(B={})),function(e){e[e.OBJECT_TYPE=37138]="OBJECT_TYPE",e[e.SYNC_CONDITION=37139]="SYNC_CONDITION",e[e.SYNC_STATUS=37140]="SYNC_STATUS",e[e.SYNC_FLAGS=37141]="SYNC_FLAGS"}(U||(U={})),function(e){e[e.UNSIGNALED=37144]="UNSIGNALED",e[e.SIGNALED=37145]="SIGNALED"}(y||(y={})),function(e){e[e.ALREADY_SIGNALED=37146]="ALREADY_SIGNALED",e[e.TIMEOUT_EXPIRED=37147]="TIMEOUT_EXPIRED",e[e.CONDITION_SATISFIED=37148]="CONDITION_SATISFIED",e[e.WAIT_FAILED=37149]="WAIT_FAILED"}(G||(G={})),function(e){e[e.SYNC_GPU_COMMANDS_COMPLETE=37143]="SYNC_GPU_COMMANDS_COMPLETE"}(P||(P={})),function(e){e[e.SYNC_FLUSH_COMMANDS_BIT=1]="SYNC_FLUSH_COMMANDS_BIT"}(w||(w={}))}}]);