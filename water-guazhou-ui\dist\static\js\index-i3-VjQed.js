import{_ as V}from"./index-C9hz-UZb.js";import{d as B,M as R,a6 as E,r as y,c as h,am as j,bF as S,a8 as K,s as Y,bB as G,j as x,bu as W,ay as U,g as $,n as H,q as i,i as s,F as u,cs as N,bo as D,bR as q,p as b,dF as J,dA as Q,aq as X,b7 as Z,aj as ee,C as te}from"./index-r0dFAfgr.js";import{_ as ae}from"./CardSearch-CB_HNR-Q.js";import{g as oe}from"./factoryReport-9H96f-eB.js";import{u as ne}from"./useStation-DJgnSZIA.js";import{f as se}from"./formartColumn-D5r7JJ2G.js";import"./Search-NSrhrIa_.js";import"./zhandian-YaGuQZe6.js";function ie(){return{title:{text:"",textStyle:{color:"#5470C6",fontSize:"14px"},top:10},grid:{left:90,right:90,top:70,bottom:40},legend:{top:20,type:"scroll",width:"500",textStyle:{fontSize:12}},tooltip:{trigger:"axis"},xAxis:{type:"category",boundaryGap:!1,data:[]},yAxis:[{position:"left",type:"value",name:"流量(m³)",axisLine:{show:!0,lineStyle:{types:"solid"}},axisLabel:{show:!0},splitLine:{lineStyle:{type:[5,10],dashOffset:5}}},{position:"right",type:"value",name:"",axisLine:{show:!0,lineStyle:{types:"solid"}},axisLabel:{show:!0},splitLine:{lineStyle:{type:[5,10],dashOffset:5}}}],series:[{name:"供水量09-07",smooth:!0,data:[150,230,224,218,135,147,260,135,147,260,135,147,260,135,147,260,135,147,260,135,147,260,135,260],type:"line",markPoint:{data:[{type:"max",name:"最大值"},{type:"min",name:"最小值"}]},markLine:{data:[{type:"average",name:"平均值"}]}},{name:"供水量09-08",smooth:!0,data:[150,230,224,218,135,147,260,135,147,260,135,147,260,135,147,260,135,147,260,135,147,260,135,260],type:"line",markPoint:{data:[{type:"max",name:"最大值"},{type:"min",name:"最小值"}]},markLine:{data:[{type:"average",name:"平均值"}]}},{name:"耗电量09-07",smooth:!0,data:[150,123,224,218,135,123,260,135,333,444,332,135,147,260,321,147,260,135,147,260,135,147,260,221],type:"line",yAxisIndex:1,markPoint:{data:[{type:"max",name:"最大值"},{type:"min",name:"最小值"}]},markLine:{data:[{type:"average",name:"平均值"}]}},{name:"耗电量09-08",smooth:!0,data:[150,123,224,218,135,123,260,135,333,444,332,135,147,260,321,147,260,135,147,260,135,147,260,221],type:"line",yAxisIndex:1,markPoint:{data:[{type:"max",name:"最大值"},{type:"min",name:"最小值"}]},markLine:{data:[{type:"average",name:"平均值"}]}}]}}const re={class:"wrapper"},le=B({__name:"index",setup(I){const{$messageWarning:v}=R(),{getStationTree:P,getStationTreeByDisabledType:M}=ne(),z=E(),a=y({queryType:"day",chartOption:null,activeName:"echarts",dataList:{},stationTree:[],chartName:""}),d=h(),g=h(),_=h(),k=h();j(()=>a.activeName,()=>{a.activeName==="echarts"&&L()});const C=y({data:[],loading:!1,title:"区域划分",expandOnClickNode:!1,showCheckbox:!0,checkedKeys:[],handleCheck:(e,t)=>{console.log(t.checkedNodes,t.checkedKeys),C.checkedKeys=t.checkedKeys||[],C.checkedNodes=t.checkedNodes||[],w()}}),T=y({defaultParams:{queryType:"day",time:S().format("YYYY-MM-DD")},filters:[{type:"select-tree",label:"监测点:",defaultExpandAll:!0,field:"stationId",clearable:!1,multiple:!0,showCheckbox:!0,width:"200px",options:K(()=>a.stationTree),nodeClick:e=>{a.chartName=e.label}},{type:"radio-button",field:"queryType",options:[{label:"日报",value:"day"},{label:"月报",value:"month"},{label:"年报",value:"year"}],label:"报告类型",onChange:e=>F(e)},{type:"date",label:"日期",field:"time",clearable:!1,format:"YYYY-MM-DD"}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",icon:"iconfont icon-chaxun",click:()=>{var t;const e=((t=d.value)==null?void 0:t.queryParams)||{};e.stationId&&e.stationId.length>0?w():v("选择监测点")}},{type:"default",perm:!0,text:"重置",svgIcon:Y(Z),click:()=>{var e;(e=d.value)==null||e.resetForm()}},{perm:!0,hide:()=>a.activeName!=="list",type:"warning",text:"导出",svgIcon:Y(ee),click:()=>{var e;l.dataList.length>0?(e=k.value)==null||e.exportTable():v("无数据导出")}}]}]}),l=y({loading:!1,dataList:[],columns:[],operations:[],pagination:{hide:!0}}),w=async()=>{var c,m;l.loading=!0;const e=((c=d.value)==null?void 0:c.queryParams)||{},t={stationIds:(m=e.stationId)==null?void 0:m.join(","),queryType:e.queryType,time:S(e.time).format(a.queryType==="month"?"YYYY-MM":a.queryType==="year"?"YYYY":"YYYY-MM-DD")},n=await oe(t);console.log("数据2",n.data.data);const o=n.data.data;if(a.dataList=o,o){const p=se(o==null?void 0:o.tableInfo);l.columns=p,l.dataList=o==null?void 0:o.tableDataList}l.loading=!1,L()},A=()=>{var e;(e=g.value)==null||e.resize()},L=()=>{var e;(e=g.value)==null||e.clear(),G(()=>{var p,f;const t=ie(),n=(p=a.dataList)==null?void 0:p.tableDataList,o=n==null?void 0:n.slice(0,n.length-6);t.xAxis.data=o==null?void 0:o.map(r=>r.ts),t.series=[];const c=(f=a.dataList)==null?void 0:f.tableInfo.filter(r=>!["数据时间","合计"].includes(r.columnName));console.log(n);const m=c.map(r=>({name:r.columnName,smooth:!0,data:n.map(O=>O[r.columnValue]),type:"line",markPoint:{data:[{type:"max",name:"最大值",label:{fontSize:12,color:x().isDark?"#ffffff":"#000000"}},{type:"min",name:"最小值",label:{color:x().isDark?"#ffffff":"#000000"}}]},markLine:{data:[{type:"average",name:"平均值"}]}}));t.series=m,_.value&&z.listenTo(_.value,()=>{a.chartOption=t,A()})})},F=e=>{var n;const t=(n=T.filters)==null?void 0:n.find(o=>o.field==="time");t.type=e==="day"?"date":e};return W(async()=>{var t;const e=await P("水厂");await M(e,[],!1,"Station"),a.stationTree=e,(t=d.value)==null||t.resetForm()}),(e,t)=>{const n=ae,o=J,c=Q,m=U("VChart"),p=X,f=V;return $(),H("div",re,[i(n,{ref_key:"refSearch",ref:d,config:s(T)},null,8,["config"]),i(f,{class:"card",title:s(a).activeName==="list"?"水厂列表":"水厂曲线"},{query:u(()=>[i(c,{modelValue:s(a).activeName,"onUpdate:modelValue":t[0]||(t[0]=r=>s(a).activeName=r)},{default:u(()=>[i(o,{label:"echarts"},{default:u(()=>[i(s(N),{style:{"margin-right":"1px","font-size":"16px"},icon:"clarity:line-chart-line"})]),_:1}),i(o,{label:"list"},{default:u(()=>[i(s(N),{style:{"margin-right":"1px","font-size":"16px"},icon:"material-symbols:table"})]),_:1})]),_:1},8,["modelValue"])]),default:u(()=>[D(b("div",{ref_key:"agriEcoDev",ref:_,class:"chart-box"},[i(m,{ref_key:"refChart",ref:g,theme:s(x)().isDark?"dark":"light",option:s(a).chartOption},null,8,["theme","option"])],512),[[q,s(a).activeName==="echarts"]]),D(b("div",null,[t[1]||(t[1]=b("div",null,null,-1)),i(p,{ref_key:"refCardTable",ref:k,class:"card-table",config:s(l)},null,8,["config"])],512),[[q,s(a).activeName==="list"]])]),_:1},8,["title"])])}}}),ge=te(le,[["__scopeId","data-v-bd8fa79f"]]);export{ge as default};
