package org.thingsboard.server.dao.notify;

import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.request.SystemNotifyTypeListRequest;
import org.thingsboard.server.dao.model.sql.notify.SystemNotifyType;

public interface SystemNotifyTypeService {

    SystemNotifyType save(SystemNotifyType systemNotifyType);

    PageData<SystemNotifyType> findList(SystemNotifyTypeListRequest request);

    void delete(String id);
}
