package org.thingsboard.server.dao.sql.smartOperation.construction;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionSettlement;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionSettlementPageRequest;

@Mapper
public interface SoConstructionSettlementMapper extends BaseMapper<SoConstructionSettlement> {
    IPage<SoConstructionSettlement> findByPage(SoConstructionSettlementPageRequest request);

    int save(SoConstructionSettlement entity);

    @Override
    default int insert(SoConstructionSettlement entity) {
        return save(entity);
    }

    boolean update(SoConstructionSettlement entity);

    boolean updateFully(SoConstructionSettlement entity);

    boolean markAsCompleted(String id);

    String getConstructionCodeById(String id);

    String getIdByConstructionCodeAndTenantId(@Param("constructionCode") String constructionCode, @Param("tenantId") String tenantId);


}
