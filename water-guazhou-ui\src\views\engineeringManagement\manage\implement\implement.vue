<!-- 工程管理-工程管理-实施管理 -->
<template>
  <div class="wrapper">
    <CardSearch ref="refSearch" :config="cardSearchConfig" />
    <CardTable :config="TableConfig" class="card-table"></CardTable>
    <DialogForm ref="refForm" :config="addOrUpdateConfig"></DialogForm>
  </div>
</template>

<script lang="ts" setup>
import { Refresh } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { ICONS } from '@/common/constans/common';
import {
  getProjectType,
  getConstructionApplyList,
  postConstructionApply,
  getConstructionApplyGlobalExport,
  getConstructionApplyExport,
  getConstructionContractType,
  getConstructionContractOptions
} from '@/api/engineeringManagement/manage';
import detail from './components/detail.vue';
import { traverse, GenNonDuplicateID } from '@/utils/GlobalHelper';

const refSearch = ref<ICardSearchIns>();
const refForm = ref<IDialogFormIns>();

const cardSearchConfig = ref<ISearch>({
  filters: [
    { label: '工程编号', field: 'constructionCode', type: 'input' },
    { label: '工程名称', field: 'constructionName', type: 'input' },
    {
      label: '工程类别',
      field: 'constructionTypeId',
      type: 'select',
      options: computed(() => data.projectType) as any
    }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          type: 'default',
          perm: true,
          text: '导出',
          icon: ICONS.DOWNLOAD,
          click: () => {
            getConstructionApplyGlobalExport().then((res) => {
              const url = window.URL.createObjectURL(res.data);
              const link = document.createElement('a');
              link.style.display = 'none';
              link.href = url;
              link.setAttribute('download', `实施管理.xlsx`);
              document.body.appendChild(link);
              link.click();
            });
          }
        },
        {
          type: 'default',
          perm: true,
          text: '重置',
          svgIcon: shallowRef(Refresh),
          click: () => {
            refSearch.value?.resetForm();
            refreshData();
          }
        },
        {
          perm: true,
          text: '查询',
          icon: ICONS.QUERY,
          click: () => refreshData()
        }
      ]
    }
  ]
});

const TableConfig = reactive<ICardTable>({
  defaultExpandAll: false,
  indexVisible: true,
  expandable: true,
  expandComponent: shallowRef(detail),
  extendedReturn: () => {
    refreshData();
  },
  rowKey: 'constructionCode',
  columns: [
    { label: '工程编号', prop: 'constructionCode' },
    { label: '工程名称', prop: 'constructionName' }
  ],
  operationWidth: '260px',
  operations: [
    {
      isTextBtn: false,
      type: 'success',
      text: '新增实施明细',
      perm: true,
      click: (row) => {
        clickAdd(row);
      }
    },
    {
      isTextBtn: false,
      text: '导出实施情况',
      perm: true,
      click: (row) => {
        getConstructionApplyExport(row.constructionCode).then((res) => {
          const url = window.URL.createObjectURL(res.data);
          const link = document.createElement('a');
          link.style.display = 'none';
          link.href = url;
          link.setAttribute('download', `${row.constructionName}实施管理.xlsx`);
          document.body.appendChild(link);
          link.click();
        });
      }
    }
  ],
  dataList: [],
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page;
      TableConfig.pagination.limit = size;
      refreshData();
    }
  }
});

const addOrUpdateConfig = reactive<IDialogFormConfig>({
  title: '添加实施明细',
  appendToBody: true,
  labelWidth: '130px',
  dialogWidth: '1000px',
  submitting: false,
  submit: (params: any) => {
    addOrUpdateConfig.submitting = true;
    let text = '新增';
    if (params.id) text = '修改';
    if (params.time) {
      params.workTimeBegin = params.time[0];
      params.workTimeEnd = params.time[1];
      delete params.time;
    }
    postConstructionApply(params)
      .then((res) => {
        addOrUpdateConfig.submitting = false;
        if (res.data.code === 200) {
          ElMessage.success(text + '成功');
          refForm.value?.closeDialog();
        } else {
          ElMessage.warning(text + '失败');
        }
        refreshData();
      })
      .catch((error) => {
        addOrUpdateConfig.submitting = false;
        ElMessage.warning(error);
      });
  },
  defaultValue: {},
  group: [
    {
      fields: [
        {
          xs: 12,
          type: 'input',
          label: '实施编号',
          field: 'code',
          rules: [{ required: true, message: '请输入实施编号' }],
          disabled: true
        },
        {
          xs: 12,
          type: 'select',
          label: '所属合同',
          field: 'contractCode',
          rules: [{ required: true, message: '请选择所属合同' }],
          options: computed(() => data.contractList) as any
        },
        {
          xs: 12,
          type: 'date',
          label: '工期开始时间',
          field: 'beginTime',
          rules: [{ required: true, message: '请输入工期开始时间' }]
        },
        {
          xs: 12,
          type: 'date',
          label: '工期结束时间',
          field: 'endTime',
          rules: [{ required: true, message: '请输入工期结束时间' }]
        },
        {
          xs: 12,
          type: 'input',
          label: '工程负责人',
          field: 'principal',
          rules: [{ required: true, message: '请输入工程负责人' }]
        },
        {
          xs: 12,
          type: 'input',
          label: '联系电话',
          field: 'phone'
        },
        {
          xs: 12,
          type: 'input',
          label: '施工班组',
          field: 'constructClass'
        },
        {
          type: 'textarea',
          label: '详细说明',
          field: 'remark'
        }
      ]
    }
  ]
});

const clickAdd = (row: { [x: string]: any }) => {
  addOrUpdateConfig.title = '添加工程合同';
  addOrUpdateConfig.defaultValue = {
    code: `${row.constructionCode.replace('S', 'Q')}-${GenNonDuplicateID()}`,
    constructionCode: row.constructionCode,
    constructionName: row.constructionName
  };
  data.getConstructionContract(row.constructionCode);
  refForm.value?.openDialog();
};

const data = reactive({
  // 项目类别
  projectType: [],
  // 合同类型
  ConstructionContractType: [],
  // 合同列表
  contractList: [],
  getOptions: () => {
    getProjectType({ page: 1, size: -1 }).then((res) => {
      data.projectType = traverse(res.data.data.data || [], 'children');
    });

    getConstructionContractType({ page: 1, size: -1 }).then((res) => {
      data.ConstructionContractType = traverse(
        res.data.data.data || [],
        'children'
      );
    });
  },
  getConstructionContract: (val: string) => {
    getConstructionContractOptions(val).then((res) => {
      data.contractList = traverse(res.data.data.data || [], 'children', {
        label: 'name',
        value: 'code'
      });
    });
  }
});

const refreshData = async () => {
  const params: any = {
    size: TableConfig.pagination.limit || 20,
    page: TableConfig.pagination.page || 1,
    ...(refSearch.value?.queryParams || {})
  };
  getConstructionApplyList(params).then((res) => {
    TableConfig.dataList = res.data.data.data || [];
    TableConfig.pagination.total = res.data.data.total || 0;
  });
};

onMounted(() => {
  refreshData();
  data.getOptions();
});
</script>

<style>
.cs {
  margin-top: 10px;
  padding-top: 20px;
}
</style>
