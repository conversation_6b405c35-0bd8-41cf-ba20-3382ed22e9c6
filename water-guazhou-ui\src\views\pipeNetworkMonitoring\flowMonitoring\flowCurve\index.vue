<!--流量曲线-->
<template>
  <div class="wrapper">
    <CardSearch
      ref="refSearch"
      :config="searchConfig"
    >
    </CardSearch>
    <SLCard
      class="card-table"
      title=" "
    >
      <template #title>
        <Tabs
          v-model="state.activeName"
          :config="topTabsConfig"
        ></Tabs>
      </template>
      <SLCard
        class="card"
        :title="state.activePattern === 'list' ? '流量数据列表' : '流量曲线'"
      >
        <template #right>
          <el-radio-group v-model="state.activePattern">
            <el-radio-button label="echarts">
              <Icon
                style="margin-right: 1px;font-size: 16px"
                icon="clarity:line-chart-line"
              />
            </el-radio-button>
            <el-radio-button label="list">
              <Icon
                style="margin-right: 1px;font-size: 16px"
                icon="material-symbols:table"
              />
            </el-radio-button>
          </el-radio-group>
        </template>
        <!-- 列表模式 -->
        <FormTable
          v-if="state.activePattern === 'list'"
          ref="refCard"
          :config="cardTableConfig"
        ></FormTable>
        <!-- 图表模式 -->
        <div
          v-if="state.activePattern === 'echarts'"
          ref="agriEcoDev"
          class="card-ehcarts"
        >
          <VChart
            ref="refChart"
            class="card-ehcarts"
            :theme="useAppStore().isDark ? 'dark' : 'light'"
            :option="state.chartOption"
          ></VChart>
        </div>
      </SLCard>
    </SLCard>
  </div>
</template>

<script lang="ts" setup>
import elementResizeDetectorMaker from 'element-resize-detector'
import { Download, Search as SearchIcon } from '@element-plus/icons-vue'
import dayjs from 'dayjs'
import { Icon } from '@iconify/vue'
import { lineOption } from '../data/echart'
import { IECharts } from '@/plugins/echart'
import useStation from '@/hooks/station/useStation'
import { getFlowData } from '@/api/pipeNetworkMonitoring/flowMonitoring'
import useGlobal from '@/hooks/global/useGlobal'
import { useAppStore } from '@/store'
import { getFormatTreeNodeDeepestChild } from '@/utils/GlobalHelper'

const { $messageWarning } = useGlobal()
const erd = elementResizeDetectorMaker()
const refChart = ref<IECharts>()
const refCard = ref<IFormTableIns>()
const refSearch = ref<ISearchIns>()
const agriEcoDev = ref<any>()

const { getStationTree, getStationTreeByDisabledType } = useStation()
const state = reactive<{
  activeName: string,
  activePattern: string,
  stationId: string,
  chartOption: any,
  stationTree: any,
  chartName: string,
  data: any
}>({
  activeName: 'day',
  activePattern: 'echarts',
  stationId: '',
  chartOption: null,
  data: [],
  stationTree: [],
  chartName: ''
})

// tabs
const topTabsConfig = reactive<ITabs>({
  type: 'tabs',
  tabType: 'simple',
  width: '100%',
  field: 'type',
  tabs: [
    {
      label: '日流量',
      value: 'day'
    },
    {
      label: '月流量',
      value: 'month'
    },
    {
      label: '年流量',
      value: 'year'
    }
    // {
    //   label: '多日流量',
    //   value: 'daterange'
    // }
  ],
  handleTabClick: (tab: any) => {
    state.activeName = tab.props.name
    const queryParams = refSearch.value?.queryParams as any || {}
    if (queryParams.stationId) {
      refreshData()
    } else {
      $messageWarning('请选择监测点')
    }
  }
})
// 获取左边树
// const TreeData = reactive<SLTreeConfig>({
//   data: [],
//   title: '区域划分',
//   defaultExpandAll: true,
//   nodeExpand: async (params: any, node?: any) => {
//     if (params.data?.type === 'Station') {
//       const attrs = await GetStationAttrs({ stationId: params.id })
//       console.log(attrs.data)
//       const newAttrs = attrs.data?.map(attr => {
//         return {
//           label: attr.type,
//           value: '',
//           id: '',
//           disabled: true,
//           children: attr.attrList
//         }
//       })
//       node.data.children = newAttrs as any
//     }
//   },
//   treeNodeHandleClick: async (data: NormalOption) => {
//     // TreeData.loading = true
//     console.log(data)
//     TreeData.currentProject = data
//     await refreshData()
//   }
// })
// TODO 多个监测点
// const TreeData = reactive<SLTreeConfig>({
//   data: [],
//   title: '区域划分',
//   showCheckbox: true,
//   defaultExpandAll: true,
//   checkedKeys: [],
//   handleCheck: (
//     ids: string[],
//     data: {
//         checkedKeys?: string[] | undefined
//         checkedNodes?: Omit<NormalOption, 'children'>[] | undefined
//       }
//   ) => {
//     console.log(data.checkedNodes)
//     console.log(data.checkedKeys)
//     TreeData.checkedKeys = data.checkedKeys || []
//     TreeData.checkedNodes = data.checkedNodes || []
//     refreshData()
//   },
//   nodeExpand: async (params: any, node?: any) => {
//     if (params.data?.type === 'Station' && params.children[0].id === 0) {
//       const attrs = await getStationAttrGroups(params.id, true)
//       node.data.children = attrs as any
//     }
//   }
// })

// 搜索栏初始化配置
const searchConfig = reactive<ISearch>({
  defaultParams: {
    day: dayjs().format('YYYY-MM-DD'),
    month: dayjs().format('YYYY-MM'),
    year: dayjs().format('YYYY'),
    daterange: [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
  },
  filters: [
    {
      type: 'select-tree',
      label: '监测点:',
      defaultExpandAll: true,
      field: 'stationId',
      clearable: false,
      width: '200px',
      options: computed(() => state.stationTree) as any,
      nodeClick: data => {
        state.chartName = data.label
      }
    },
    {
      type: 'select',
      label: '统计类型',
      field: 'va1',
      width: '140px',
      options: [
        {
          label: '净累计',
          value: '净累计'
        },
        {
          label: '正向累计',
          value: '正向累计'
        },
        {
          label: '反向累计',
          value: '反向累计'
        }
      ]
    },
    {
      type: 'date',
      label: '日期',
      field: 'day',
      format: 'YYYY-MM-DD',
      clearable: false,
      hidden: computed(() => state.activeName === 'year' || state.activeName === 'month' || state.activeName === 'daterange') as any,
      width: 300
    },
    {
      type: 'month',
      label: '日期',
      field: 'month',
      format: 'YYYY-MM',
      clearable: false,
      hidden: computed(() => state.activeName === 'year' || state.activeName === 'day' || state.activeName === 'daterange') as any,
      width: 300
    },
    {
      type: 'year',
      label: '日期',
      field: 'year',
      format: 'YYYY',
      clearable: false,
      hidden: computed(() => state.activeName === 'day' || state.activeName === 'month' || state.activeName === 'daterange') as any,
      width: 300
    },
    {
      type: 'daterange',
      label: '日期',
      format: 'YYYY-MM',
      field: 'daterange',
      clearable: false,
      hidden: computed(() => state.activeName === 'year' || state.activeName === 'month' || state.activeName === 'day') as any,
      width: 300
    }

  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          svgIcon: shallowRef(SearchIcon),
          click: () => {
            const queryParams = refSearch.value?.queryParams as any || {}
            if (queryParams.stationId) {
              refreshData()
            } else {
              $messageWarning('请选择监测点')
            }
          }
        },
        {
          perm: true,
          text: '导出',
          type: 'warning',
          svgIcon: shallowRef(Download),
          hide: () => { return state.activePattern !== 'list' },
          click: () => {
            const queryParams = refSearch.value?.queryParams as any || {}
            if (queryParams.stationId) {
              refCard.value?.exportTable()
            } else {
              $messageWarning('请选择监测点')
            }
          }
        }
      ]
    }
  ]
})

// 初始化列表配置数据
const cardTableConfig = reactive<ITable>({
  loading: false,
  dataList: [],
  columns: [],
  operations: [],
  showSummary: false,
  operationWidth: '150px',
  pagination: {
    hide: true
  }
})

// 配置加载图表数据
const refuseChart = () => {
  const dataX = state.data?.tableDataList.map(table => table.ts)
  const chartOption = lineOption(dataX)
  chartOption.series = []
  const serie = {
    name: state.chartName,
    smooth: true,
    data: [],
    type: 'line',
    markPoint: {
      data: [
        { type: 'max', name: '最大值' },
        { type: 'min', name: '最小值' }
      ]
    },
    markLine: {
      data: [{ type: 'average', name: '平均值' }]
    }
  }
  chartOption.yAxis.name = '流量(m³)'
  const newSerie = JSON.parse(JSON.stringify(serie)) as any
  newSerie.data = state.data?.tableDataList.map(table => table.value)
  chartOption.series.push(newSerie)
  // state.data?.tableInfo.map(info => {
  //   if (info.columnValue !== 'ts') {
  //     const newSerie = JSON.parse(JSON.stringify(serie)) as any
  //     newSerie.name = info.columnName
  //     newSerie.data = state.data?.tableDataList.map(table => table[info.columnValue])
  //     chartOption.series.push(newSerie)
  //   }
  // })
  refChart.value?.clear()
  nextTick(() => {
    if (agriEcoDev.value) {
      erd.listenTo(agriEcoDev.value, () => {
        state.chartOption = chartOption
        refChart.value?.resize()
      })
    }
  })
}

// 条件查询数据列表
const refreshData = async () => {
  const queryParams = refSearch.value?.queryParams || {}
  // const attributes = TreeData.checkedKeys as any[]
  const date = queryParams[state.activeName]
  const params: any = {
    stationId: queryParams.stationId,
    queryType: state.activeName,
    date
  }
  const res = await getFlowData(params)
  const data = res.data?.data
  console.log(data)
  cardTableConfig.columns = [
    {
      prop: 'ts',
      label: '时间',
      align: 'center'
    },
    {
      prop: 'value',
      label: '流量',
      unit: '(m³)',
      align: 'center'
    }
  ]
  cardTableConfig.dataList = data
  state.data.tableDataList = data
  refuseChart()
}

onBeforeMount(async () => {
  const type = ['流量监测站,测流压站'].join(',')
  const treeData = await getStationTree(type) as any[]
  state.stationTree = treeData
  await getStationTreeByDisabledType(treeData, ['Project'], false, 'Station')
  const currentStation = getFormatTreeNodeDeepestChild(
    treeData
  ) as any
  searchConfig.defaultParams = {
    ...searchConfig.defaultParams,
    stationId: currentStation.id
  }
  refSearch.value?.resetForm()
  await refreshData()
})
</script>
<style lang="scss" scoped>
.card {
  height: 100%;

  .tab {
    height: 100%;
    width: 100%;
  }

  .table-box {
    height: calc(100vh - 300px);
  }

  .card-ehcarts {
    height: 100%;
    width: 100%;
  }
}
</style>
