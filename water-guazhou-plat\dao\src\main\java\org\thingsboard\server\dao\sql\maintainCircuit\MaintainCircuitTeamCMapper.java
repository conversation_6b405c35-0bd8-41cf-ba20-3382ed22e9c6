package org.thingsboard.server.dao.sql.maintainCircuit;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.thingsboard.server.dao.model.sql.maintainCircuit.MaintainCircuitTeamC;

import java.util.List;

/**
 * userMybatis
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-08-23
 */
@Mapper
public interface MaintainCircuitTeamCMapper extends BaseMapper<MaintainCircuitTeamC> {

    List<MaintainCircuitTeamC> getListByMainId(String id);
}
