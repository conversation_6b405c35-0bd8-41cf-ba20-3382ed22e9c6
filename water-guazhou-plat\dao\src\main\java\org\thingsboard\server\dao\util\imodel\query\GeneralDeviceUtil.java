package org.thingsboard.server.dao.util.imodel.query;

import org.thingsboard.server.common.data.exception.ThingsboardErrorCode;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.dao.util.reflection.ExceptionUtils;

public class GeneralDeviceUtil {

    public static final String LEVEL_TWO_SUFFIX = "000000000";
    public static final String LEVEL_ONE_SUFFIX = "000000000000";

    @SuppressWarnings("ConstantValue")
    public static void checkDeviceSerialId(GeneralDeviceType req, GeneralDeviceTypeService deviceTypeService) throws ThingsboardException {
        // 验证创建规则
        String parentId = req.getParentId();
        long serialId = Long.parseLong(req.getSerialId());
        if (serialId == 0)
            ExceptionUtils.silentThrow("编码已存在");
        // 指定父节点
        // null 时为 0
        // 不为null 时为父节点深度 从1开始
        Integer depth = deviceTypeService.getDepth(parentId);
        if (depth == null)
            ExceptionUtils.silentThrow("无法正确获取父级深度，请检查父级是否存在");
        if (parentId != null) {
            // 父节点不存在时阻止创建
            if (depth == 0)
                throw new ThingsboardException("父节点不存在", ThingsboardErrorCode.GENERAL);

            // 父节点深度大于2时阻止创建 最多只有三层
            if (depth > 2)
                throw new ThingsboardException("父节点深度已达上限", ThingsboardErrorCode.GENERAL);
        }

        //<editor-fold desc="编号不符合规则时阻止创建">
        // 编码规则验证 编码规则为 2 3 3 6
        long temp = serialId;
        if (depth == 0)
            // 当前深度为1 前2位相同
            temp = (temp / 1000_000_000000L) * 1000_000_000000L;
        else if (depth == 1)
            // 当前深度为2 前5位相同
            temp = (temp / 1000_000000L) * 1000_000000L;
        else if (depth == 2)
            // 当前深度为3 前8位相同
            temp = (temp / 1000000L) * 1000000L;
        else
            throw new ThingsboardException("认知中不可能出现的异常", ThingsboardErrorCode.GENERAL);

        if (temp != serialId)
            throw new ThingsboardException("编码不符合规则", ThingsboardErrorCode.GENERAL);

        // 编号上级验证
        if (parentId != null) {
            String rawParentSerialId = deviceTypeService.getSerialId(req.getParentId());
            long parentSerialId = Long.parseLong(rawParentSerialId);
            if (depth == 1)
                // 父级深度为1 与父级前2位相同
                serialId = (serialId / 1000_000_000000L) * 1000_000_000000L;
            else if (depth == 2)
                // 父级深度为2 与父级前5位相同
                serialId = (serialId / 1000_000000L) * 1000_000000L;
            else
                throw new ThingsboardException("认知中不可能出现的异常", ThingsboardErrorCode.GENERAL);

            if (serialId != parentSerialId)
                throw new ThingsboardException("编码头与父节点编码相异", ThingsboardErrorCode.GENERAL);
        }
        //</editor-fold>

        if (deviceTypeService.isSerialIdExists(req.getSerialId(), req.getId(), req.tenantId())) {
            ExceptionUtils.silentThrow("编码重复");
        }
    }

    public static String extractParentSerialId(String serialId) {
        for (int i = 5; i <= 7; i++) {
            if (serialId.charAt(i) != '0') {
                return serialId.substring(0, 5) + LEVEL_TWO_SUFFIX;
            }
        }
        for (int i = 2; i <= 4; i++) {
            if (serialId.charAt(i) != '0') {
                return serialId.substring(0, 2) + LEVEL_ONE_SUFFIX;
            }
        }
        return null;
    }
}
