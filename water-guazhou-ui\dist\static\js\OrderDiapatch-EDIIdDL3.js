import{d as q,c as s,r as i,s as g,bF as f,bH as b,S as u,b as v,o as A,g as B,n as V,q as n,F as j,a9 as W,b6 as z,al as G,b7 as H,aj as $}from"./index-r0dFAfgr.js";import{_ as J}from"./CardTable-rdWOL4_6.js";import{_ as K}from"./CardSearch-CB_HNR-Q.js";import{f as Q}from"./DateFormatter-Bm9a68Ax.js";import X from"./OrderStepTags-CClNfq4j.js";import Z from"./detail-CU6-qhMl.js";import{b as ee,a as te,W as re}from"./config-DqqM5K5L.js";import{D as oe,c as ae,a as le,b as se}from"./index-CpGhZCTT.js";import{u as ie}from"./useUser-Blb5V02j.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";/* empty css                         */import"./detailSteps-BqRp_Y4m.js";/* empty css                */import"./index-CCFuhOrs.js";/* empty css                             */import"./DPlayer-Be2AurQX.js";import"./DPlayer.min-_HMH7IVX.js";import"./OutsideWorkOrder-C6s8joBt.js";const ne={class:"wrapper"},Ye=q({__name:"OrderDiapatch",setup(ce){const{getUserOptions:M}=ie(),_=s(),c=s(),y=s(),D=s(),P=s(),h=s(),k=s(),L=s(),d=i({WorkOrderEmergencyLevelList:[],WorkOrderProcessLevelList:[]});function S(){le("1").then(e=>{d.WorkOrderEmergencyLevelList=W(e.data.data||[],"children",{label:"name",value:"id"})}),se("1").then(e=>{d.WorkOrderProcessLevelList=W(e.data.data||[],"children",{label:"name",value:"name"})})}const O=i({title:"流程明细",cancel:!1,className:"lightColor",group:[]}),F=s(""),E=i({filters:[{type:"daterange",label:"发起时间",field:"date"},{type:"input",label:"标题",field:"title"},{type:"select",label:"来源",field:"source",options:ee()},{type:"select-tree",label:"类型",field:"type",options:te()}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",svgIcon:g(G),click:()=>p()},{perm:!0,text:"重置",type:"default",svgIcon:g(H),click:()=>{var e;(e=h.value)==null||e.resetForm(),p()}},{perm:!0,text:"导出",type:"warning",svgIcon:g($),click:()=>{var e;(e=k.value)==null||e.exportTable()}}]}],defaultParams:{date:[f().subtract(1,"M").format(b),f().format(b)]},handleSearch:()=>p()}),l=i({expandable:!0,expandComponent:g(X),defaultExpandAll:!0,columns:[{label:"工单编号",prop:"serialNo"},{label:"来源",prop:"source"},{label:"类型",prop:"type"},{label:"标题",prop:"title"},{label:"地址",prop:"address"},{label:"创建人",prop:"organizerName"},{label:"发起时间",prop:"createTime",formatter:e=>Q(e.createTime)},{minWidth:120,prop:"level",label:"紧急程度",tag:!0,tagColor:e=>{var t;return((t=d.WorkOrderEmergencyLevelList.find(o=>o.value===e.level))==null?void 0:t.color)||""},formatter:e=>{var t;return(t=d.WorkOrderEmergencyLevelList.find(o=>o.value===e.level))==null?void 0:t.label}}],dataList:[],pagination:{refreshData:({page:e,size:t})=>{l.pagination.page=e,l.pagination.limit=t,p()}},operationWidth:140,operations:[{perm:!0,isTextBtn:!0,text:"详情",click:e=>U(e)},{perm:!0,isTextBtn:!0,text:"分派",click:e=>{var t;l.currentRow=e,m.defaultValue={},(t=c.value)==null||t.openDrawer()}}]}),w=i({title:"转发",width:500,labelPosition:"top",group:[{fields:[{type:"select",label:"转发至：",field:"to",options:[]},{type:"textarea",label:"备注：",field:"remark"}]}],submit:e=>{u("确定转发？","提示信息").then(()=>{var t;console.log(e),(t=_.value)==null||t.closeDrawer()}).catch(()=>{})}}),m=i({title:"分派",width:500,labelPosition:"top",group:[{fields:[{type:"select",label:"分派至：",field:"stepProcessUserId",rules:[{required:!0,message:"请选择用户"}],options:[]},{field:"processLevelLabel",label:"处理级别：",type:"select",rules:[{required:!0,message:"请选择处理级别"}],options:re(),onChange:e=>{var o,a;const t=d.WorkOrderProcessLevelList.find(r=>r.value===e);(a=(o=c==null?void 0:c.value)==null?void 0:o.refForm)!=null&&a.dataForm&&(c.value.refForm.dataForm={...c.value.refForm.dataForm,processLevel:t.dayTime*1440+t.hourTime*60+t.minuteTime})}}]}],submit:e=>{u("确定分派？","提示信息").then(async()=>{var t,o,a;m.submitting=!0;try{const r=await oe(l.currentRow.id,e);((t=r.data)==null?void 0:t.code)===200?(v.success("操作成功"),p()):v.error(((o=r==null?void 0:r.data)==null?void 0:o.err)||"操作失败")}catch{v.error("系统错误")}m.submitting=!1,(a=c.value)==null||a.closeDrawer()}).catch(()=>{})}}),I=i({title:"无效",width:500,labelPosition:"top",group:[{fields:[{type:"textarea",label:"原因：",field:"remark"}]}],submit:e=>{u("确定提交？","提示信息").then(()=>{var t;console.log(e),(t=y.value)==null||t.closeDrawer()}).catch(()=>{})}}),T=i({title:"上报",width:500,labelPosition:"top",group:[{fields:[{type:"select",label:"上报至：",field:"to",options:[]},{type:"textarea",label:"备注：",field:"remark"}]}],submit:e=>{u("确定上报？","提示信息").then(()=>{var t;console.log(e),(t=y.value)==null||t.closeDrawer()}).catch(()=>{})}}),x=i({title:"批示",width:500,group:[{fields:[{type:"textarea",label:"备注",field:"remark"},{type:"select",label:"发送至",field:"to",options:[]}]}],submit:e=>{u("确定发送？","提示信息").then(()=>{var t;console.log(e),(t=D.value)==null||t.closeDrawer()}).catch(()=>{})}}),U=e=>{var t;F.value=e.id||"",O.title=e.serialNo,(t=L.value)==null||t.openDrawer()},p=async()=>{var e,t,o;l.loading=!0;try{const a=((e=h.value)==null?void 0:e.queryParams)||{},[r,N]=((t=a.date)==null?void 0:t.length)===2?[f(a.date[0],b).format("YYYY-MM-DD"),f(a.date[1],b).endOf("D").format("YYYY-MM-DD")]:[f().subtract(1,"M").startOf("D").format("YYYY-MM-DD"),f().endOf("D").format("YYYY-MM-DD")],C={page:l.pagination.page||1,size:l.pagination.limit||20,...a,fromTime:r,toTime:N,status:"PENDING"};delete C.date;const Y=(o=(await ae(C)).data)==null?void 0:o.data;l.dataList=Y.data,l.pagination.total=Y.total}catch{}l.loading=!1},R=async()=>{const e=await M(!1,{authority:"CUSTOMER_USER"}),t=m.group[0].fields[0],o=w.group[0].fields[0],a=x.group[0].fields[1],r=T.group[0].fields[0];r.options=e,a.options=e,o.options=e,t.options=e};return A(()=>{S(),R(),p()}),(e,t)=>{const o=K,a=J,r=z;return B(),V("div",ne,[n(o,{ref_key:"refSearch",ref:h,config:E},null,8,["config"]),n(a,{ref_key:"refTable",ref:k,config:l,class:"card-table"},null,8,["config"]),n(r,{ref_key:"refFormDiapatch",ref:c,config:m},null,8,["config"]),n(r,{ref_key:"refFormTransform",ref:_,config:w},null,8,["config"]),n(r,{ref_key:"refFormReport",ref:y,config:T},null,8,["config"]),n(r,{ref_key:"refFormApply",ref:D,config:x},null,8,["config"]),n(r,{ref_key:"refFormInvalid",ref:P,config:I},null,8,["config"]),n(r,{ref_key:"refdetail",ref:L,config:O},{default:j(()=>[n(Z,{id:F.value},null,8,["id"])]),_:1},8,["config"])])}}});export{Ye as default};
