package org.thingsboard.server.dao.menu2;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.model.sql.TenantMenusRole;
import org.thingsboard.server.dao.sql.menu2.TenantMenusRoleRepository;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class TenantMenusRoleServiceImpl implements TenantMenusRoleService {

    @Autowired
    private TenantMenusRoleRepository tenantMenusRoleRepository;

    @Override
    public List<String> findRoleMenus(String roleId, String tenantApplicationId, TenantId tenantId) {
        return tenantMenusRoleRepository.findByRoleIdAndTenantApplicationIdAndTenantId(roleId, tenantApplicationId, UUIDConverter.fromTimeUUID(tenantId.getId()));
    }

    @Override
    public void saveRoleMenus(String roleId, String tenantApplicationId, List<String> menus, TenantId tenantId) {
        tenantMenusRoleRepository.deleteByRoleIdAndTenantApplicationId(roleId, tenantApplicationId);
        List<TenantMenusRole> addList = new ArrayList<>();
        String tenantIdStr = UUIDConverter.fromTimeUUID(tenantId.getId());
        for (String menu : menus) {
            TenantMenusRole tenantMenusRole = new TenantMenusRole();
            tenantMenusRole.setTenantId(tenantIdStr);
            tenantMenusRole.setRoleId(roleId);
            tenantMenusRole.setMenuId(menu);
            tenantMenusRole.setTenantApplicationId(tenantApplicationId);

            addList.add(tenantMenusRole);
        }

        tenantMenusRoleRepository.save(addList);
    }
}
