package org.thingsboard.server.common.data.VO;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.thingsboard.server.common.data.Device;
import org.thingsboard.server.common.data.Tenant;
import org.thingsboard.server.common.data.project.ProjectTreeVO;

import java.util.List;

/**
 * 平台级GIS 企业及项目主机信息查询
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class TenantGisVO extends Tenant {
    List<ProjectTreeVO> projectList;

    List<Device> deviceList;
}
