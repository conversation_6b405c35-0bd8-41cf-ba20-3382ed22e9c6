package org.thingsboard.server.dao.sql.msgLog;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.ui.velocity.SpringResourceLoader;
import org.thingsboard.server.dao.model.sql.DeviceLogEntity;
import org.thingsboard.server.dao.model.sql.MsgLogEntity;
import org.thingsboard.server.dao.msgLog.DeviceLogDao;
import org.thingsboard.server.dao.msgLog.MsgLogDao;

import javax.transaction.Transactional;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/2/26 15:05
 */
@Service
@Transactional
public class JpaDeviceLogDao implements DeviceLogDao {


    @Autowired
    private DeviceLogRepository deviceLogRepository;


    @Override
    public DeviceLogEntity save(DeviceLogEntity deviceLogEntity) {
        return deviceLogRepository.save(deviceLogEntity);
    }

    @Override
    public List<DeviceLogEntity> findByTenant(String tenantId) {
        return deviceLogRepository.findByTenantIdOrderByUpdateTimeDesc(tenantId);
    }

    @Override
    public List<DeviceLogEntity> findByTenantAndTime(String tenantId, long start, long end) {
        return deviceLogRepository.findByTenantIdAndUpdateTime(tenantId,start,end);
    }

    @Override
    public List<DeviceLogEntity> findByProject(String projectId) {
        Sort orders = new Sort(new Sort.Order(Sort.Direction.DESC, "updateTime"));
        return deviceLogRepository.findByProjectId(projectId, orders);
    }

    @Override
    public List<DeviceLogEntity> findByProject(String projectId, Long start, Long end) {
        Sort orders = new Sort(new Sort.Order(Sort.Direction.DESC, "updateTime"));
        return deviceLogRepository.findByProjectId(projectId, start, end);
    }

    @Override
    public void deleteByDeviceId(String deviceId) {
        deviceLogRepository.deleteByDeviceId(deviceId);
    }
}
