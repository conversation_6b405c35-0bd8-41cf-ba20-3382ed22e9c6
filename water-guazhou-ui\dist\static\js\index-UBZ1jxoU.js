import{_ as q}from"./TreeBox-DDD2iwoR.js";import{_ as L}from"./CardTable-rdWOL4_6.js";import{_ as W}from"./CardSearch-CB_HNR-Q.js";import{_ as B}from"./index-BJ-QPYom.js";import"./index-0NlGN6gS.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import{z as F,d as H,c as P,r as y,l as d,bJ as M,bI as I,bH as v,b as N,o as O,g as R,h as z,F as S,q as b,i as h,C as G}from"./index-r0dFAfgr.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./project-DUuzYgGl.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import{u as J}from"./usePartition-DkcY9fQ2.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./LayerHelper-Cn-iiqxI.js";import"./pipe-nogVzCHG.js";import"./dma-SMxrzG7b.js";import"./hookupDevice-Bcbk7s68.js";const V=g=>F({url:"/api/spp/statisticsReport/partitionInOutWater",method:"get",params:g}),Y=H({__name:"index",setup(g){const x=P(),u=P(),r=y({data:[],title:"选择分区",expandOnClickNode:!1,treeNodeHandleClick:async t=>{r.currentProject!==t&&(r.currentProject=t,n())}}),f=(t,m,o)=>o.hidden=t.type!==o.field,j=y({defaultParams:{type:"month",year:d().format(M),month:d().format(I),day:[d().format(v),d().format(v)]},filters:[{type:"select",field:"type",clearable:!1,options:[{label:"按年",value:"year"},{label:"按月",value:"month"},{label:"按时间段",value:"day"}],label:"选择方式"},{handleHidden:f,type:"year",label:"",field:"year",clearable:!1,disabledDate(t){return new Date<t}},{handleHidden:f,type:"month",label:"",field:"month",clearable:!1,format:I,disabledDate(t){return new Date<t}},{handleHidden:f,type:"daterange",label:"",field:"day",clearable:!1,disabledDate(t){return new Date<t}},{type:"btn-group",btns:[{perm:!0,text:"查询",iconifyIcon:"ep:search",click:()=>n()},{perm:!0,text:"重置",type:"default",iconifyIcon:"ep:refresh",click:()=>{var t;(t=u.value)==null||t.resetForm(),n()}},{perm:!0,text:"导出",type:"primary",iconifyIcon:"ep:download",click:()=>{var t;(t=x.value)==null||t.exportTable()}}]}]}),T=(t=[])=>[{minWidth:160,label:"日期",prop:"date",align:"center"},...t],p=y({dataList:[],columns:T([]),pagination:{hide:!0}}),n=async()=>{var t,m,o,s,c;if(!r.currentProject){N.warning("请先选择分区");return}try{const e=((t=u.value)==null?void 0:t.queryParams)||{},l=(await V({partitionId:(m=r.currentProject)==null?void 0:m.value,type:e.type,date:e.type==="month"?e.month:e.type==="year"?e.year:void 0,start:(o=e.day)==null?void 0:o[0],end:(s=e.day)==null?void 0:s[1]})).data.data||{},k=((c=l.header)==null?void 0:c.map(a=>({label:a,prop:a,minWidth:160,formatter(D,i){return i==null?void 0:i.toFixed(1)}})))||[],_=(l==null?void 0:l.data)||[],w={date:"合计"};k.map(a=>{w[a.prop]=_.reduce((D,i)=>i[a.prop]+D,0)}),_.push(w),p.columns=T(k),p.dataList=_}catch(e){console.log(e)}p.loading=!1},C=J();return O(async()=>{await C.getTree(),r.data=C.Tree.value,r.currentProject=r.data[0],n()}),(t,m)=>{const o=B,s=W,c=L,e=q;return R(),z(e,null,{tree:S(()=>[b(o,{ref:"refTree","tree-data":h(r)},null,8,["tree-data"])]),default:S(()=>[b(s,{ref_key:"refSearch",ref:u,config:h(j)},null,8,["config"]),b(c,{ref_key:"refTable",ref:x,class:"card-table",config:h(p)},null,8,["config"])]),_:1})}}}),_e=G(Y,[["__scopeId","data-v-54ad6b29"]]);export{_e as default};
