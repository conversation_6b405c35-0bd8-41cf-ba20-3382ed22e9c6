package org.thingsboard.server.dao.model.sql.assay;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * 水质化验-化验项内容
 */
@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.ASSAY_REPORT_DATA_ITEM_TABLE)
@TableName(ModelConstants.ASSAY_REPORT_DATA_ITEM_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class AssayReportDataItem {

    @Id
    @TableId
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @TableField(value = ModelConstants.ASSAY_REPORT_DATA_ITEM_PID)
    private String pid;

    @TableField(value = ModelConstants.ASSAY_REPORT_DATA_ITEM_TITLE)
    private String title;

    @TableField(value = ModelConstants.ASSAY_REPORT_DATA_ITEM_TARGET)
    private String target;

    @TableField(value = ModelConstants.ASSAY_REPORT_DATA_ITEM_UNIT)
    private String unit;

    @TableField(value = ModelConstants.ASSAY_REPORT_DATA_ITEM_CHECK_PERSON)
    private String checkPerson;

    @TableField(value = ModelConstants.CREATE_TIME)
    private Date createTime;

    @TableField(value = ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;


}
