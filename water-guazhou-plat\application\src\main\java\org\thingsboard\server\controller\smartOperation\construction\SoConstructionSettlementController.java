package org.thingsboard.server.controller.smartOperation.construction;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionSettlement;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionSettlementPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.SoConstructionSettlementSaveRequest;
import org.thingsboard.server.dao.util.imodel.response.ExcelFileInfo;
import org.thingsboard.server.dao.construction.SoConstructionSettlementService;
import org.thingsboard.server.utils.imodel.annotations.IStarController2;

@IStarController2
@RequestMapping("/api/so/constructionSettlement")
public class SoConstructionSettlementController extends BaseController {
    @Autowired
    private SoConstructionSettlementService service;


    @GetMapping
    public IPage<SoConstructionSettlement> findAllConditional(SoConstructionSettlementPageRequest request) {
        return service.findAllConditional(request);
    }

    @GetMapping("/export/excel")
    public ExcelFileInfo exportExcel(SoConstructionSettlementPageRequest request) {
        return ExcelFileInfo.of("工程结算列表", findAllConditional(request).getRecords())
                .nextTitle("constructionCode", "工程编号")
                .nextTitle("constructionName", "工程名称")
                .nextTitle("constructionTypeName", "工程类别")
                .nextTitle("estimateCost", "预算金额（万元）")
                .nextTitle("cost", "结算金额（万元）")
                .nextTitle("creatorName", "创建人")
                .nextTitle("createTimeName", "创建时间");
    }


    @PostMapping
    public SoConstructionSettlement save(@RequestBody SoConstructionSettlementSaveRequest req) {
        req.preCheckUpdate(() -> !service.isComplete(req.getConstructionCode(), req.tenantId()), "已完成不可更改");
        return service.save(req);
    }

    @PostMapping("/{constructionCode}/complete")
    public boolean complete(@PathVariable String constructionCode) throws ThingsboardException {
        String userId = UUIDConverter.fromTimeUUID(getCurrentUser().getUuidId());
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return service.complete(constructionCode,userId, tenantId);
    }

    // @PatchMapping("/{id}")
    public boolean edit(@RequestBody SoConstructionSettlementSaveRequest req, @PathVariable String id) {
        return service.update(req.unwrap(id));
    }

    // @DeleteMapping("/{id}")
    public boolean delete(@PathVariable String id) {
        return service.delete(id);
    }
}