<!-- 坐标转换 -->
<template>
  <div class="coord-converter">
    <div
      ref="refLeftDiv"
      class="left"
    >
      <SideDrawer
        v-model="state.drawerShow"
        :title="'原始数据'"
        :width="leftWidth"
        :hide-bar="true"
        :direction="'rtl'"
      >
        <FormTable :config="TableConfigLeft" />
      </SideDrawer>
    </div>
    <div class="center">
      <Form :config="FormConfigCenter"></Form>
    </div>
    <div class="right">
      <SideDrawer
        v-model="state.drawerShow"
        :title="'转换结果'"
        :width="leftWidth"
        :hide-bar="true"
        :direction="'ltr'"
      >
        <template #title>
          <span
            class="title"
            style="margin-right: auto"
          >转换结果</span>
          <el-radio-group v-model="state.resultType">
            <el-radio-button label="table">
              表格
            </el-radio-button>
            <el-radio-button label="list">
              文本
            </el-radio-button>
          </el-radio-group>
        </template>
        <FormTable
          v-if="state.resultType === 'table'"
          :config="TableConfigRight"
        ></FormTable>
        <div v-if="state.resultType === 'list'">
          <ul class="list">
            <li
              v-for="(item, i) in TableConfigRight.dataList"
              :key="i"
            >
              {{ item.name }} {{ item.x }},{{ item.y }}
            </li>
          </ul>
        </div>
      </SideDrawer>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ArrowRight, Delete, Plus } from '@element-plus/icons-vue'
import { v4 as uuidv4 } from 'uuid'
import SpatialReference from '@arcgis/core/geometry/SpatialReference.js'
import * as coordinateFormatter from '@arcgis/core/geometry/coordinateFormatter.js'
import SideDrawer from '@/components/DrawerBox/components/SideDrawer.vue'
import { SLConfirm } from '@/utils/Message'

const refLeftDiv = ref<HTMLDivElement>()
const leftWidth = ref<number>(800)

const state = reactive<{
  drawerShow: true
  resultType: 'list' | 'table'
}>({
  drawerShow: true,
  resultType: 'table'
})
const TableConfigLeft = reactive<ITable>({
  indexVisible: true,
  dataList: [
    {
      id: uuidv4(),
      name: '柑子院一块表',
      y: '29°45′37.570678″N',
      x: '106°26′04.257814″E'
    }
  ],
  columns: [
    {
      minWidth: 200,
      label: '名称',
      prop: 'name',
      formItemConfig: { type: 'input' }
    },
    {
      minWidth: 200,
      label: 'x',
      prop: 'x',
      formItemConfig: { type: 'input' }
    },
    {
      minWidth: 200,
      label: 'y',
      prop: 'y',
      formItemConfig: { type: 'input' }
    }
  ],
  operationHeader: {
    type: 'btn-group',
    btns: [
      {
        perm: true,
        text: '新增',
        isTextBtn: true,
        svgIcon: shallowRef(Plus),
        click: () => handleLeftAdd()
      }
    ]
  },
  operations: [
    {
      perm: true,
      text: '删除',
      svgIcon: shallowRef(Delete),
      type: 'danger',
      click: row => handleLeftDelete(row)
    }
  ],
  pagination: { hide: true }
})
const TableConfigRight = reactive<ITable>({
  indexVisible: true,
  dataList: [],
  columns: [
    {
      minWidth: 200,
      label: '名称',
      prop: 'name'
    },
    {
      minWidth: 200,
      label: 'x',
      prop: 'x',
      formItemConfig: { type: 'input' }
    },
    {
      minWidth: 200,
      label: 'y',
      prop: 'y',
      formItemConfig: { type: 'input' }
    }
  ],
  pagination: { hide: true }
})
const FormConfigCenter = reactive<IFormConfig>({
  gutter: 0,
  labelWidth: 'auto',
  group: [
    {
      fields: [
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: '转换坐标',
              svgIcon: shallowRef(ArrowRight),
              styles: { width: '100%' },
              click: () => handleTransCoords()
            }
          ]
        }
        // {
        //   type: 'btn-group',
        //   btns: [
        //     {
        //       perm: true,
        //       text: '上传文档',
        //       svgIcon: shallowRef(Upload),
        //       type: 'success',
        //       styles: { width: '100%' },
        //       click: () => handleUploadFile()
        //     }
        //   ]
        // }
      ]
    }
  ]
})
const handleTransCoords = () => {
  TableConfigRight.dataList = []
  coordinateFormatter.load().then(() => {
    const inputPoints = TableConfigLeft.dataList.map(item => {
      const point = coordinateFormatter.fromLatitudeLongitude(
        [item.y, item.x].join(','),
        new SpatialReference({ wkid: 4326 })
      )
      return { point, ...item }
    })
    TableConfigRight.dataList = inputPoints.map(item => {
      return {
        x: item.point?.longitude,
        y: item.point?.latitude,
        name: item.name
      }
    })
  })
}
const handleLeftAdd = () => {
  TableConfigLeft.dataList.push({ id: uuidv4(), x: '', y: '' })
}
const handleLeftDelete = (row: any) => {
  SLConfirm('确定删除？', '删除提示')
    .then(() => {
      TableConfigLeft.dataList = TableConfigLeft.dataList.filter(
        item => item.id !== row.id
      )
    })
    .catch(() => {
      //
    })
}
const resize = () => {
  leftWidth.value = refLeftDiv.value?.clientWidth ?? 800
}
onMounted(() => {
  resize()
  window.addEventListener('resize', resize)
})
onBeforeUnmount(() => {
  window.removeEventListener('resize', resize)
})
</script>
<style lang="scss" scoped>
.coord-card {
  height: 100%;
  border-radius: 0 !important;
}
.coord-converter {
  width: 100%;
  height: 100%;
  padding: 20px;
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  .center {
    max-width: 190px;
    padding: 20px;
    height: 50%;
  }
  .left,
  .right {
    position: relative;
    border: 1px solid var(--el-border-color);
    height: 100%;
    overflow: hidden;
    width: 100%;
  }
  .list {
    list-style-type: none;
    margin: 0;
    padding: 0;
    li {
      margin: 0;
      padding: 0;
      line-height: 32px;
    }
  }
}
</style>
