<template>
  <div
    v-show="state.visible"
    ref="refContainer"
    class="arc-infowindow"
    :class="[highLight ? 'active' : '', props.theme]"
    :style="{
      backgroundColor: backgroundColor
    }"
  >
    <div class="arc-infowindow-wrapper">
      <div class="title-wrapper" @click="highlight">
        <span class="title" :class="status">
          {{ title }}
        </span>
        <el-icon
          v-if="showMore"
          class="btn btn-more"
          @click.stop="$emit('more')"
        >
          <More />
        </el-icon>
        <el-icon
          v-if="showBack"
          class="btn btn-back"
          @click.stop="$emit('back')"
        >
          <Back />
        </el-icon>
        <el-icon class="btn btn-close" @click.stop="close">
          <Close />
        </el-icon>
      </div>
      <div class="content-wrapper">
        <slot> </slot>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import Point from '@arcgis/core/geometry/Point';
import { Close, More, Back } from '@element-plus/icons-vue';

const refContainer = ref<HTMLDivElement>();
const props = defineProps<{
  offsetx?: number;
  offsety?: number;
  title?: string;
  width?: number;
  height?: number;
  maxHeight?: number;
  maxWidth?: number;
  showClose?: boolean;
  highLight?: boolean;
  visible?: boolean;
  backgroundColor?: string;
  status?: IStatus;
  showMore?: boolean;
  showBack?: boolean;
  theme?: ITheme;
  x?: number;
  y?: number;
}>();
const state = reactive<{
  visible: boolean;
}>({
  visible: !!props.visible
});
const emit = defineEmits([
  'highlight',
  'more',
  'back',
  'toggled',
  'refreshPosition'
]);
const open = () => {
  state.visible = true;
  highlight();
  emit('toggled', true);
};
const toggle = (flag?: boolean) => {
  state.visible = flag ?? !state.visible;
  if (state.visible) {
    highlight();
  }
  emit('toggled', state.visible);
};
const close = () => {
  state.visible = false;
  emit('toggled', false);
};
const highlight = () => {
  refContainer.value?.parentElement?.appendChild(refContainer.value);
  emit('highlight');
};
const setPosition = (
  mapView?: __esri.MapView,
  location?: { x?: number; y?: number }
) => {
  if (!refContainer.value || !mapView) return;
  location = location || position.value;
  if (!location) return;
  const point = new Point({
    x: location?.x,
    y: location?.y,
    spatialReference: mapView?.spatialReference
  });
  const screenPoint = mapView?.toScreen(point);
  refContainer.value.style.left =
    (screenPoint?.x || -10000) + (props.offsetx || 0) + 'px';
  refContainer.value.style.top =
    (screenPoint?.y || -10000) + (props.offsety || -10) + 'px';
};
const position = computed(() => {
  return {
    x: props.x,
    y: props.y
  };
});
watch(
  () => position.value,
  (newVal: any) => {
    if (newVal) emit('refreshPosition');
  },
  {
    immediate: true
  }
);
defineExpose({
  open,
  close,
  toggle,
  setPosition
});
</script>
<style lang="scss" scoped>
.dark {
  .arc-infowindow {
    background-color: #383e53;
  }
  .title-wrapper {
    .title {
      color: var(--el-text-color-darker);
    }
  }
}
.arc-infowindow {
  background-color: #fff;
  // user-select: none;
  transform: translateX(calc(-100% - 20px)) translateY(calc(-100% + 40px));
  position: absolute;
  border-radius: 16px 16px 0 16px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.25);
  padding: 8px;
  // &::before {
  //   content: ' ';
  //   position: absolute;
  //   left: 50%;
  //   bottom: 0;
  //   transform: translateY(50%) translateX(-50%) rotate(45deg);
  //   display: block;
  //   // border: 1px solid #21667b;
  //   background-color: inherit;
  //   width: 10px;
  //   height: 10px;
  //   border-top: none;
  //   border-left: none;
  // }
  &.active {
    .title-wrapper {
      // background-color: #94bcc8;
    }
  }
  &.darkblue {
    color: #fff;
    background-color: #0f3457;
    .title-wrapper {
      .title {
        color: #fff;
      }
    }
  }
}
.arc-infowindow-wrapper {
  min-width: 150px;
  min-height: 28px;
}
.title-wrapper {
  // background-color: #5094a8;
  height: 38px;
  padding: 0 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-width: 150px;
  .title {
    overflow: hidden;
    display: block;
    width: 100%;
    text-overflow: ellipsis;
    white-space: nowrap;
    position: relative;
    font-family: 'PingFang SC';
    font-style: normal;
    font-weight: 600;
    font-size: 16px;
    line-height: 22px;
    color: var(--el-text-color-regular);
    &.normal,
    &.danger,
    &.online,
    &.offline,
    &.warning {
      padding-left: 15px;
      &::before {
        content: ' ';
        position: absolute;
        width: 8px;
        height: 8px;
        left: 0;
        border-radius: 5px 5px;
        top: 5px;
      }
    }
    &.normal {
      &::before {
        background-color: #33a6ee;
      }
    }
    &.danger {
      &::before {
        background-color: #d5584b;
      }
    }
    &.online {
      &::before {
        background-color: #5cb95c;
      }
    }
    &.offline {
      &::before {
        background-color: #686f77;
      }
    }
    &.warning {
      &::before {
        background-color: #e4a90f;
      }
    }
  }
}
.content-wrapper {
  padding: 8px;
  // border: 1px solid #21667b;
}
.btn {
  cursor: pointer;
  margin-left: 8px;
}
</style>
