package org.thingsboard.server.dao.gis;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.smartManagement.UserCoordinate;
import org.thingsboard.server.dao.model.sql.smartManagement.UserCoordinateGroup;
import org.thingsboard.server.dao.sql.smartManagement.UserCoordinateMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.DestUserCoordinatePageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.UserCoordinatePageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.UserCoordinateSaveRequest;

@Service
public class UserCoordinateServiceImpl implements UserCoordinateService {
    @Autowired
    private UserCoordinateMapper mapper;


    @Override
    public IPage<UserCoordinate> findDestUserAllConditional(DestUserCoordinatePageRequest request) {
        return mapper.findDestUserByPageConditional(request);
    }

    @Override
    public IPage<UserCoordinateGroup> findAllConditional(UserCoordinatePageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    public UserCoordinate save(UserCoordinateSaveRequest entity) {
        return QueryUtil.saveOrUpdateOneByRequest(entity, mapper);
    }

    @Override
    public boolean update(UserCoordinate entity) {
        return mapper.update(entity);
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }

    @Override
    public IPage<UserCoordinate> findNewest(UserCoordinatePageRequest request) {
        return mapper.findNewest(request);
    }


}
