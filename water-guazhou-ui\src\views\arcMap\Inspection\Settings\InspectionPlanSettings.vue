<template>
  <div class="inspection-plan-settings">
    <Search
      ref="refSearch"
      :config="SearchConfig"
      style="margin-bottom: 8px; padding: 12px; background-color: #fff; border-radius: 4px;"
    ></Search>
    <div class="table-box">
      <FormTable :config="TableConfig"></FormTable>
    </div>

    <!-- 新建/编辑弹窗 -->
    <InspectionPlanDialog
      v-model="dialogVisible"
      :edit-id="editId"
      :readonly="isViewMode"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, shallowRef } from 'vue'
import { Edit, Delete, View } from '@element-plus/icons-vue'
import Search from '@/components/Form/Search.vue'
import FormTable from '@/components/Form/FormTable.vue'
import InspectionPlanDialog from './components/InspectionPlanDialog.vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { formatDate } from '@/utils/DateFormatter'
import {
  getInspectionPlanList,
  deleteInspectionPlan
} from '@/api/CircuitSettings/inspectionPlan'

// 搜索配置
const SearchConfig = reactive<ISearch>({
  labelWidth: 80,
  filters: [
    {
      type: 'input',
      label: '巡检计划名称',
      field: 'planName'
    },
    {
      type: 'select',
      label: '巡检类型',
      field: 'inspectionType',
      options: [
        { label: '管网巡检', value: 'pipeline' },
        { label: '泵站巡检', value: 'pumpStation' },
        { label: '其他巡检', value: 'other' }
      ]
    },
    {
      type: 'select',
      label: '巡检周期',
      field: 'inspectionCycle',
      options: [
        { label: '每周一、三、五日执行', value: 'weekly_135' },
        { label: '每月5、15、25日执行', value: 'monthly_51525' },
        { label: '每季度第一个月10日执行', value: 'quarterly_10' }
      ]
    },
    {
      type: 'select',
      label: '执行角色',
      field: 'executionRole',
      options: [
        { label: '运维组长、设备专员', value: 'maintenance_equipment' },
        { label: '巡检员、设备专员', value: 'inspector_equipment' },
        { label: '其他', value: 'other' }
      ]
    },
    {
      type: 'select',
      label: '状态',
      field: 'status',
      options: [
        { label: '启用', value: '1' },
        { label: '停用', value: '0' }
      ]
    },
    {
      type: 'daterange',
      label: '创建时间',
      field: 'createTime',
      format: 'YYYY-MM-DD'
    },
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          type: 'primary',
          click: () => refreshData()
        },
        {
          perm: true,
          text: '重置',
          type: 'default',
          click: () => {
            refSearch.value?.resetForm()
            refreshData()
          }
        }
      ]
    }
  ]
})

// 表格配置
const TableConfig = reactive<ITable>({
  title: '巡检计划列表',
  height: 'calc(100vh - 180px)',
  indexVisible: true,
  columns: [
    { minWidth: 150, label: '巡检计划名称', prop: 'planName' },
    {
      minWidth: 120,
      label: '巡检类型',
      prop: 'inspectionType',
      formatter: (row: any) => {
        const typeMap: Record<string, string> = {
          'pipeline': '管网巡检',
          'pumpStation': '泵站巡检',
          'other': '其他巡检'
        }
        return typeMap[row.inspectionType] || row.inspectionType
      }
    },
    {
      minWidth: 180,
      label: '巡检周期',
      prop: 'inspectionCycle',
      formatter: (row: any) => {
        const cycleMap: Record<string, string> = {
          'weekly_135': '每周一、三、五日执行',
          'monthly_51525': '每月5、15、25日执行',
          'quarterly_10': '每季度第一个月10日执行'
        }
        return cycleMap[row.inspectionCycle] || row.inspectionCycle
      }
    },
    {
      minWidth: 150,
      label: '执行角色',
      prop: 'executionRole',
      formatter: (row: any) => {
        const roleMap: Record<string, string> = {
          'maintenance_equipment': '运维组长、设备专员',
          'inspector_equipment': '巡检员、设备专员',
          'other': '其他'
        }
        return roleMap[row.executionRole] || row.executionRole
      }
    },
    {
      minWidth: 150,
      label: '检查表模板',
      prop: 'checklistTemplate',
      formatter: (row: any) => {
        const templateMap: Record<string, string> = {
          'equipment_status': '设备运行状态检查表',
          'pipeline_pressure': '管网压力检查表',
          'pump_station': '泵站运行检查表',
          'water_quality': '水质检测检查表',
          'safety_hazard': '安全隐患检查表'
        }
        return templateMap[row.checklistTemplate] || row.checklistTemplate
      }
    },
    {
      minWidth: 160,
      label: '创建时间',
      prop: 'createTime',
      formatter: (row: any) => formatDate(row.createTime, 'YYYY-MM-DD HH:mm:ss')
    },
    {
      minWidth: 100,
      label: '状态',
      prop: 'status',
      formatter: (row: any) => row.status === '1' ? '启用' : '停用'
    }
  ],
  dataList: [],
  loading: false,
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    refreshData: ({ page, size }: { page: number; size: number }) => {
      TableConfig.pagination.page = page
      TableConfig.pagination.limit = size
      refreshData()
    }
  },
  operations: [
    {
      perm: true,
      text: '查看',
      isTextBtn: true,
      svgIcon: shallowRef(View),
      click: (row: any) => handleView(row)
    },
    {
      perm: true,
      text: '编辑',
      isTextBtn: true,
      svgIcon: shallowRef(Edit),
      click: (row: any) => handleEdit(row)
    },
    {
      perm: true,
      text: '删除',
      isTextBtn: true,
      svgIcon: shallowRef(Delete),
      click: (row: any) => handleDelete(row)
    }
  ],
  titleRight: [
    {
      style: {
        justifyContent: 'flex-end'
      },
      items: [
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: '新增计划',
              type: 'primary',
              click: () => handleCreate()
            }
          ]
        }
      ]
    }
  ]
})

const refSearch = ref<ISearchIns>()

// 弹窗相关
const dialogVisible = ref(false)
const editId = ref('')
const isViewMode = ref(false)

// 刷新数据
const refreshData = async () => {
  try {
    TableConfig.loading = true
    const queryParams = refSearch.value?.queryParams || {}

    // 处理日期范围参数
    let fromTime = ''
    let toTime = ''
    if (queryParams.createTime && Array.isArray(queryParams.createTime)) {
      fromTime = queryParams.createTime[0]
      toTime = queryParams.createTime[1]
    }

    const params = {
      page: TableConfig.pagination.page || 1,
      size: TableConfig.pagination.limit || 20,
      planName: queryParams.planName,
      inspectionType: queryParams.inspectionType,
      inspectionCycle: queryParams.inspectionCycle,
      executionRole: queryParams.executionRole,
      status: queryParams.status,
      fromTime,
      toTime
    }

    const res = await getInspectionPlanList(params)
    if (res?.data) {
      // 处理 IstarResponse 包装的数据格式
      const responseData = res.data.data || res.data
      // 根据实际API返回格式处理数据
      TableConfig.dataList = responseData.data || responseData.records || responseData.content || []
      TableConfig.pagination.total = responseData.total || responseData.totalElements || 0
    }
  } catch (error) {
    console.error(error)
    ElMessage.error('获取数据失败')
  } finally {
    TableConfig.loading = false
  }
}

// 查看详情
const handleView = (row: any) => {
  editId.value = row.id
  isViewMode.value = true
  dialogVisible.value = true
}

// 编辑
const handleEdit = (row: any) => {
  editId.value = row.id
  isViewMode.value = false
  dialogVisible.value = true
}

// 删除
const handleDelete = (row: any) => {
  ElMessageBox.confirm('确定要删除该巡检计划吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await deleteInspectionPlan(row.id)
      ElMessage.success('删除成功')
      refreshData()
    } catch (error) {
      console.error(error)
      ElMessage.error('删除失败')
    }
  }).catch(() => {})
}

// 新建
const handleCreate = () => {
  editId.value = ''
  isViewMode.value = false
  dialogVisible.value = true
}

// 弹窗成功回调
const handleDialogSuccess = () => {
  refreshData()
}

onMounted(() => {
  refreshData()
})
</script>

<style lang="scss" scoped>
.inspection-plan-settings {
  height: 100%;
  padding: 16px;
  background-color: #fff;
  
  .search-area {
    margin-bottom: 16px;
  }
  
  .table-box {
    background-color: #fff;
    border-radius: 4px;
  }
}
</style>
