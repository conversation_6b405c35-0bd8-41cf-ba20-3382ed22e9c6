<template>
  <div class="video_container">
    <div v-if="!video?.url" class="item_bg">待选中视频源</div>
    <DPlayer
      v-else-if="video?.type === 'customHls'"
      style="width: 100%; height: 100%"
      :video-info="{
        live: true,
        hotkey: false,
        preload: 'auto',
        autoplay: true,
        video: {
          url: video?.url ?? '',
          type: 'customHls'
        }
      }"
    ></DPlayer>
    <WSPlayer
      v-else-if="video?.type === 'ws'"
      ref="wsplayer"
      :config="{
        url: video?.url ?? '',
        index: video?.index ?? 0,
        talkurl: video?.talkurl
      }"
    ></WSPlayer>
    <XGPlayer
      v-else
      :video-info="{
        live: true,
        hotkey: false,
        preload: 'auto',
        autoplay: true,
        video: {
          url: video?.url ?? '',
          type: 'flv'
        }
      }"
    ></XGPlayer>
  </div>
</template>

<script lang="ts" setup>
const props = defineProps<{
  video: {
    status: boolean;
    url?: string | undefined;
    talkurl?: string | null | undefined;
    type?: string | undefined;
    id?: string | undefined;
    key: string | null;
    index?: number | undefined | null;
  };
}>();
</script>

<style lang="scss" scoped>
.video_container {
  width: 100%;
  height: 100%;

  .item_bg {
    background-color: #000000;
    height: 100%;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #868686;
  }
}
</style>
