package org.thingsboard.server.dao.sql.project;

import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.thingsboard.server.dao.model.sql.ProjectEntity;

import java.util.List;

public interface ProjectRepository extends CrudRepository<ProjectEntity, String> {

    List<ProjectEntity> findByParentIdOrderByCreateTime(String parentId);

    List<ProjectEntity> findByTenantIdAndParentIdOrderByCreateTime(String tenantId, String parentId);

    int countByParentId(String parentId);

    @Query("SELECT p FROM ProjectEntity p WHERE p.tenantId = ?1 and p.parentId = '0' ORDER BY p.createTime ASC")
    List<ProjectEntity> findRootProject(String tenantId);

    List<ProjectEntity> findByTenantId(String tenantId, Sort orders);

    List<ProjectEntity> findByTenantId(String tenantId);

    @Query("FROM ProjectEntity p ORDER BY p.createTime ASC")
    List<ProjectEntity> findAll();

    int countByTenantId(String tenantId);

    void deleteByTenantId(String tenantId);

    List<ProjectEntity> findByIdIn(List<String> projectIdList);

}
