package org.thingsboard.server.dao.util.imodel.query.cache.info;

import org.thingsboard.server.dao.util.imodel.query.CriteriaBuilderWrapper;
import org.thingsboard.server.dao.util.imodel.query.cache.QueryFieldInfo;

import java.lang.reflect.Field;

public class QueryEqualFieldInfo extends QueryFieldInfo<Object> {

    public QueryEqualFieldInfo( boolean or, Field field) {
        super(or, field);
    }

    @Override
    public void process(Object entity, CriteriaBuilderWrapper wrapper, Object value) {
        wrapper.equalTo(name, value, or);
    }

}
