package org.thingsboard.server.dao.util.imodel.query.smartService.wechat;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartService.wechat.WxNews;
import org.thingsboard.server.dao.util.imodel.query.PageableQueryEntity;

@Getter
@Setter
public class WxNewsPageRequest extends PageableQueryEntity<WxNews> {
    // 标题
    private String title;

    // 分类id
    private String categoryId;


}
