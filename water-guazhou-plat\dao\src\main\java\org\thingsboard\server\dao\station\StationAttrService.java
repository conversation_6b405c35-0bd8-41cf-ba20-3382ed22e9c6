package org.thingsboard.server.dao.station;

import org.thingsboard.server.dao.model.sql.StationAttrEntity;

import java.util.List;

public interface StationAttrService {
    void save(StationAttrEntity stationAttr);

    List<StationAttrEntity> getList(String stationId, String type);

    List<StationAttrEntity> getList(List<String> stationIdList, String type);

    void deleteByStationId(String id);

    void deleteByStationIdAndType(String stationId, String type);

    List<StationAttrEntity> findByStation(String stationId);

    List<String> groupByStationId(String stationId);

    StationAttrEntity findById(String attributeId);

    List<StationAttrEntity> findByIdIn(List<String> ids);

    List<StationAttrEntity> findByDeviceIdAndAttr(String deviceId, List<String> attrList);

    List<StationAttrEntity> findByDeviceId(String deviceId);

    List<StationAttrEntity> findByStationIdInAndAttr(List<String> idList, String attr);
}
