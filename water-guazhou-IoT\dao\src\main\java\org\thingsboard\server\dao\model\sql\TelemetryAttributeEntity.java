/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.model.sql;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.common.data.id.DeviceId;
import org.thingsboard.server.common.data.id.TelemetryAttributeId;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.telemetryAttribute.TelemetryAttribute;
import org.thingsboard.server.dao.DaoUtil;
import org.thingsboard.server.dao.model.BaseSqlEntity;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.model.SearchTextEntity;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.TELEMETRY_ATTRIBUTE_COLIMN_FAMILY_NAME)
public class TelemetryAttributeEntity extends BaseSqlEntity<TelemetryAttribute> implements SearchTextEntity<TelemetryAttribute> {


    @Column(name = ModelConstants.TELEMETRY_ATTRIBUTE_DEVICE_ID)
    private String deviceId;

    @Column(name = ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;
    //属性类别
    @Column(name = ModelConstants.TELEMETRY_ATTRIBUTE_ATTRIBUTE_TYPE)
    private String attributeType;
    //协议名称
    @Column(name = ModelConstants.TELEMETRY_ATTRIBUTE_AGREEMENT_NAME)
    private String agreementName;
    //滤波次数
    @Column(name = ModelConstants.TELEMETRY_ATTRIBUTE_HARMONIC_NUMBER)
    private String harmonicNumber;
    //显示名称
    @Column(name = ModelConstants.TELEMETRY_ATTRIBUTE_SHOW_NAME)
    private String showName;
    //数据类型
    @Column(name = ModelConstants.TELEMETRY_ATTRIBUTE_DATA_TYPE)
    private String dataType;
    //数据偏移量
    @Column(name = ModelConstants.TELEMETRY_ATTRIBUTE_DATA_OFFSET)
    private String dataOffset;
    //采样系数
    @Column(name = ModelConstants.TELEMETRY_ATTRIBUTE_COEFFICIENT)
    private String coefficient;
    //采样最小值
    @Column(name = ModelConstants.TELEMETRY_ATTRIBUTE_MIN_VALUE)
    private String minValue;
    //采样最大值
    @Column(name = ModelConstants.TELEMETRY_ATTRIBUTE_MAX_VALUE)
    private String maxValue;
    //公式
    @Column(name = ModelConstants.TELEMETRY_ATTRIBUTE_FORMULA)
    private String formula;
    //统计数据类型
    @Column(name = ModelConstants.TELEMETRY_ATTRIBUTE_STATISTICS)
    private String statistics;
    //单位
    @Column(name = ModelConstants.TELEMETRY_ATTRIBUTE_UNIT)
    private String unit;
    //单位系数
    @Column(name = ModelConstants.TELEMETRY_ATTRIBUTE_UNIT_COEFFICIENT)
    private String unitCoefficient;
    //字节顺序
    @Column(name = ModelConstants.TELEMETRY_ATTRIBUTE_BYTE_ORDER)
    private String byteOrder;

    public TelemetryAttributeEntity() {
    }

    public TelemetryAttributeEntity(TelemetryAttribute telemetryAttribute) {
        if (telemetryAttribute.getId() != null)
            this.setId(telemetryAttribute.getUuidId());
        if (telemetryAttribute.getDeviceId() != null)
            this.setDeviceId(toString(DaoUtil.getId(telemetryAttribute.getDeviceId())));
        if(telemetryAttribute.getTenantId()!=null)
            this.setTenantId(toString(DaoUtil.getId(telemetryAttribute.getDeviceId())));
        this.attributeType=telemetryAttribute.getAttributeType();
        this.agreementName=telemetryAttribute.getAgreementName();
        this.harmonicNumber=telemetryAttribute.getHarmonicNumber();
        this.showName=telemetryAttribute.getShowName();
        this.dataType=telemetryAttribute.getDataType();
        this.dataOffset=telemetryAttribute.getDataOffset();
        this.coefficient=telemetryAttribute.getCoefficient();
        this.minValue=telemetryAttribute.getMinValue();
        this.maxValue=telemetryAttribute.getMaxValue();
        this.formula=telemetryAttribute.getFormula();
        this.statistics=telemetryAttribute.getStatistics();
        this.unit=telemetryAttribute.getUnit();
        this.unitCoefficient=telemetryAttribute.getUnitCoefficient();
        this.byteOrder=telemetryAttribute.getByteOrder();
    }


    @Override
    public TelemetryAttribute toData() {
        TelemetryAttribute telemetryAttribute=new TelemetryAttribute(new TelemetryAttributeId(getId()));
        telemetryAttribute.setDeviceId(new DeviceId(toUUID(deviceId)));
        telemetryAttribute.setTenantId(new TenantId(toUUID(tenantId)));
        telemetryAttribute.setAgreementName(agreementName);
        telemetryAttribute.setAttributeType(attributeType);
        telemetryAttribute.setByteOrder(byteOrder);
        telemetryAttribute.setHarmonicNumber(harmonicNumber);
        telemetryAttribute.setShowName(showName);
        telemetryAttribute.setDataOffset(dataOffset);
        telemetryAttribute.setCoefficient(coefficient);
        telemetryAttribute.setMinValue(minValue);
        telemetryAttribute.setMaxValue(maxValue);
        telemetryAttribute.setFormula(formula);
        telemetryAttribute.setStatistics(statistics);
        telemetryAttribute.setUnit(unit);
        telemetryAttribute.setUnitCoefficient(unitCoefficient);
        telemetryAttribute.setByteOrder(byteOrder);
        return telemetryAttribute;
    }

    @Override
    public String getSearchTextSource() {
        return showName;
    }

    @Override
    public void setSearchText(String searchText) {
        this.showName=searchText;
    }
}
