<!--

    Copyright © 2016-2019 The Thingsboard Authors

    Licensed under the Apache License, Version 2.0 (the "License");
    you may not use this file except in compliance with the License.
    You may obtain a copy of the License at

        http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.

-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.thingsboard</groupId>
        <version>2.3.0</version>
        <artifactId>msa</artifactId>
    </parent>
    <groupId>org.thingsboard.msa</groupId>
    <artifactId>tb</artifactId>
    <packaging>pom</packaging>

    <name>ThingsBoard Docker Images</name>
    <url>https://thingsboard.io</url>
    <description>ThingsBoard Docker Images</description>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <main.dir>${basedir}/../..</main.dir>
        <pkg.name>thingsboard</pkg.name>
        <tb.docker.name>tb</tb.docker.name>
        <tb-postgres.docker.name>tb-postgres</tb-postgres.docker.name>
        <tb-cassandra.docker.name>tb-cassandra</tb-cassandra.docker.name>
        <pkg.user>thingsboard</pkg.user>
        <pkg.installFolder>/usr/share/${pkg.name}</pkg.installFolder>
        <pkg.upgradeVersion>2.2.0</pkg.upgradeVersion>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.thingsboard</groupId>
            <artifactId>application</artifactId>
            <version>${project.version}</version>
            <classifier>deb</classifier>
            <type>deb</type>
            <scope>provided</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy-tb-deb</id>
                        <phase>package</phase>
                        <goals>
                            <goal>copy</goal>
                        </goals>
                        <configuration>
                            <artifactItems>
                                <artifactItem>
                                    <groupId>org.thingsboard</groupId>
                                    <artifactId>application</artifactId>
                                    <classifier>deb</classifier>
                                    <type>deb</type>
                                    <destFileName>${pkg.name}.deb</destFileName>
                                    <outputDirectory>${project.build.directory}/docker-tb</outputDirectory>
                                </artifactItem>
                            </artifactItems>
                        </configuration>
                    </execution>
                    <execution>
                        <id>copy-tb-postgres-deb</id>
                        <phase>package</phase>
                        <goals>
                            <goal>copy</goal>
                        </goals>
                        <configuration>
                            <artifactItems>
                                <artifactItem>
                                    <groupId>org.thingsboard</groupId>
                                    <artifactId>application</artifactId>
                                    <classifier>deb</classifier>
                                    <type>deb</type>
                                    <destFileName>${pkg.name}.deb</destFileName>
                                    <outputDirectory>${project.build.directory}/docker-postgres</outputDirectory>
                                </artifactItem>
                            </artifactItems>
                        </configuration>
                    </execution>
                    <execution>
                        <id>copy-tb-cassandra-deb</id>
                        <phase>package</phase>
                        <goals>
                            <goal>copy</goal>
                        </goals>
                        <configuration>
                            <artifactItems>
                                <artifactItem>
                                    <groupId>org.thingsboard</groupId>
                                    <artifactId>application</artifactId>
                                    <classifier>deb</classifier>
                                    <type>deb</type>
                                    <destFileName>${pkg.name}.deb</destFileName>
                                    <outputDirectory>${project.build.directory}/docker-cassandra</outputDirectory>
                                </artifactItem>
                            </artifactItems>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy-docker-tb-config</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}/docker-tb</outputDirectory>
                            <resources>
                                <resource>
                                    <directory>docker</directory>
                                    <filtering>true</filtering>
                                </resource>
                                <resource>
                                    <directory>docker-tb</directory>
                                    <filtering>true</filtering>
                                </resource>
                            </resources>
                        </configuration>
                    </execution>
                    <execution>
                        <id>copy-docker-tb-postgres-config</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}/docker-postgres</outputDirectory>
                            <resources>
                                <resource>
                                    <directory>docker</directory>
                                    <filtering>true</filtering>
                                </resource>
                                <resource>
                                    <directory>docker-postgres</directory>
                                    <filtering>true</filtering>
                                </resource>
                            </resources>
                        </configuration>
                    </execution>
                    <execution>
                        <id>copy-docker-tb-cassandra-config</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}/docker-cassandra</outputDirectory>
                            <resources>
                                <resource>
                                    <directory>docker</directory>
                                    <filtering>true</filtering>
                                </resource>
                                <resource>
                                    <directory>docker-cassandra</directory>
                                    <filtering>true</filtering>
                                </resource>
                            </resources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>com.spotify</groupId>
                <artifactId>dockerfile-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>build-docker-tb-image</id>
                        <phase>pre-integration-test</phase>
                        <goals>
                            <goal>build</goal>
                        </goals>
                        <configuration>
                            <skip>${dockerfile.skip}</skip>
                            <repository>${docker.repo}/${tb.docker.name}</repository>
                            <verbose>true</verbose>
                            <googleContainerRegistryEnabled>false</googleContainerRegistryEnabled>
                            <contextDirectory>${project.build.directory}/docker-tb</contextDirectory>
                        </configuration>
                    </execution>
                    <execution>
                        <id>tag-docker-tb-image</id>
                        <phase>pre-integration-test</phase>
                        <goals>
                            <goal>tag</goal>
                        </goals>
                        <configuration>
                            <skip>${dockerfile.skip}</skip>
                            <repository>${docker.repo}/${tb.docker.name}</repository>
                            <tag>${project.version}</tag>
                        </configuration>
                    </execution>
                    <execution>
                        <id>build-docker-tb-postgres-image</id>
                        <phase>pre-integration-test</phase>
                        <goals>
                            <goal>build</goal>
                        </goals>
                        <configuration>
                            <skip>${dockerfile.skip}</skip>
                            <repository>${docker.repo}/${tb-postgres.docker.name}</repository>
                            <verbose>true</verbose>
                            <googleContainerRegistryEnabled>false</googleContainerRegistryEnabled>
                            <contextDirectory>${project.build.directory}/docker-postgres</contextDirectory>
                        </configuration>
                    </execution>
                    <execution>
                        <id>tag-docker-tb-postgres-image</id>
                        <phase>pre-integration-test</phase>
                        <goals>
                            <goal>tag</goal>
                        </goals>
                        <configuration>
                            <skip>${dockerfile.skip}</skip>
                            <repository>${docker.repo}/${tb-postgres.docker.name}</repository>
                            <tag>${project.version}</tag>
                        </configuration>
                    </execution>
                    <execution>
                        <id>build-docker-tb-cassandra-image</id>
                        <phase>pre-integration-test</phase>
                        <goals>
                            <goal>build</goal>
                        </goals>
                        <configuration>
                            <skip>${dockerfile.skip}</skip>
                            <repository>${docker.repo}/${tb-cassandra.docker.name}</repository>
                            <verbose>true</verbose>
                            <googleContainerRegistryEnabled>false</googleContainerRegistryEnabled>
                            <contextDirectory>${project.build.directory}/docker-cassandra</contextDirectory>
                        </configuration>
                    </execution>
                    <execution>
                        <id>tag-docker-tb-cassandra-image</id>
                        <phase>pre-integration-test</phase>
                        <goals>
                            <goal>tag</goal>
                        </goals>
                        <configuration>
                            <skip>${dockerfile.skip}</skip>
                            <repository>${docker.repo}/${tb-cassandra.docker.name}</repository>
                            <tag>${project.version}</tag>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
    <profiles>
        <profile>
            <id>push-docker-image</id>
            <activation>
                <property>
                    <name>push-docker-image</name>
                </property>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>com.spotify</groupId>
                        <artifactId>dockerfile-maven-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>push-latest-docker-tb-image</id>
                                <phase>pre-integration-test</phase>
                                <goals>
                                    <goal>push</goal>
                                </goals>
                                <configuration>
                                    <tag>latest</tag>
                                    <repository>${docker.repo}/${tb.docker.name}</repository>
                                </configuration>
                            </execution>
                            <execution>
                                <id>push-version-docker-tb-image</id>
                                <phase>pre-integration-test</phase>
                                <goals>
                                    <goal>push</goal>
                                </goals>
                                <configuration>
                                    <tag>${project.version}</tag>
                                    <repository>${docker.repo}/${tb.docker.name}</repository>
                                </configuration>
                            </execution>
                            <execution>
                                <id>push-latest-docker-tb-postgres-image</id>
                                <phase>pre-integration-test</phase>
                                <goals>
                                    <goal>push</goal>
                                </goals>
                                <configuration>
                                    <tag>latest</tag>
                                    <repository>${docker.repo}/${tb-postgres.docker.name}</repository>
                                </configuration>
                            </execution>
                            <execution>
                                <id>push-version-docker-tb-postgres-image</id>
                                <phase>pre-integration-test</phase>
                                <goals>
                                    <goal>push</goal>
                                </goals>
                                <configuration>
                                    <tag>${project.version}</tag>
                                    <repository>${docker.repo}/${tb-postgres.docker.name}</repository>
                                </configuration>
                            </execution>
                            <execution>
                                <id>push-latest-docker-tb-cassandra-image</id>
                                <phase>pre-integration-test</phase>
                                <goals>
                                    <goal>push</goal>
                                </goals>
                                <configuration>
                                    <tag>latest</tag>
                                    <repository>${docker.repo}/${tb-cassandra.docker.name}</repository>
                                </configuration>
                            </execution>
                            <execution>
                                <id>push-version-docker-tb-cassandra-image</id>
                                <phase>pre-integration-test</phase>
                                <goals>
                                    <goal>push</goal>
                                </goals>
                                <configuration>
                                    <tag>${project.version}</tag>
                                    <repository>${docker.repo}/${tb-cassandra.docker.name}</repository>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
    <repositories>
        <repository>
            <id>jenkins</id>
            <name>Jenkins Repository</name>
            <url>http://repo.jenkins-ci.org/releases</url>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
    </repositories>
</project>
