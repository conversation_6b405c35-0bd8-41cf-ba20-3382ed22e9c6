"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[5587],{83379:(t,e,r)=>{r.d(e,{Z:()=>y});var o=r(43697),s=r(20102),i=r(609),l=r(95330),n=r(20941),a=r(5600),u=r(52011);const p="loaded",d=t=>{let e=class extends t{constructor(...t){super(...t),this._loadController=null,this.loadError=null,this.loadStatus="not-loaded",this._set("loadWarnings",[]),this.addResolvingPromise(new Promise((t=>{const e=this.load.bind(this);this.load=r=>{const o=new Promise(((t,e)=>{const o=(0,l.$F)(r,e);this.destroyed&&e(new s.Z("load:instance-destroyed",`Instance of '${this.declaredClass||this.constructor.name}' is already destroyed`,{instance:this})),this._promiseProps.when(t,e).finally((()=>{o&&o.remove()}))}));if("not-loaded"===this.loadStatus){this._set("loadStatus","loading");const t=this._loadController=new AbortController;e({signal:t.signal}),(0,l.fu)(t.signal,(()=>{this._promiseProps.abort()}))}return t(),o}}))),this.when((()=>{this._set("loadStatus",p),this._loadController=null}),(t=>{this._set("loadStatus","failed"),this._set("loadError",t),this._loadController=null}))}get loaded(){return this.loadStatus===p}get loadWarnings(){return this._get("loadWarnings")}load(){return null}cancelLoad(){return this.isFulfilled()||(this._set("loadError",new s.Z("load:cancelled","Cancelled")),this._loadController?.abort()),this}};return(0,o._)([(0,a.Cb)({readOnly:!0})],e.prototype,"loaded",null),(0,o._)([(0,a.Cb)({readOnly:!0})],e.prototype,"loadError",void 0),(0,o._)([(0,a.Cb)({clonable:!1})],e.prototype,"loadStatus",void 0),(0,o._)([(0,a.Cb)({type:[n.Z],readOnly:!0})],e.prototype,"loadWarnings",null),e=(0,o._)([(0,u.j)("esri.core.Loadable")],e),e};let h=class extends(d(i.D)){};var c;h=(0,o._)([(0,u.j)("esri.core.Loadable")],h),(c=h||(h={})).LoadableMixin=d,c.isLoadable=function(t){return!(!t||!t.load)};const y=h},609:(t,e,r)=>{r.d(e,{D:()=>h,v:()=>d});var o,s,i=r(43697),l=r(15923),n=r(70586),a=r(95330),u=r(52011);(s=o||(o={}))[s.PENDING=0]="PENDING",s[s.RESOLVED=1]="RESOLVED",s[s.REJECTED=2]="REJECTED";class p{constructor(t){this.instance=t,this._resolver=(0,a.dD)(),this._status=o.PENDING,this._resolvingPromises=[],this._resolver.promise.then((()=>{this._status=o.RESOLVED,this._cleanUp()}),(()=>{this._status=o.REJECTED,this._cleanUp()}))}addResolvingPromise(t){this._resolvingPromises.push(t),this._tryResolve()}isResolved(){return this._status===o.RESOLVED}isRejected(){return this._status===o.REJECTED}isFulfilled(){return this._status!==o.PENDING}abort(){this._resolver.reject((0,a.zE)())}when(t,e){return this._resolver.promise.then(t,e)}_cleanUp(){this._allPromise=this._resolvingPromises=this._allPromise=null}_tryResolve(){if(this.isFulfilled())return;const t=(0,a.dD)(),e=[...this._resolvingPromises,(0,n.j0)(t.promise)],r=this._allPromise=Promise.all(e);r.then((()=>{this.isFulfilled()||this._allPromise!==r||this._resolver.resolve(this.instance)}),(t=>{this.isFulfilled()||this._allPromise!==r||(0,a.D_)(t)||this._resolver.reject(t)})),t.resolve()}}const d=t=>{let e=class extends t{constructor(...t){super(...t),this._promiseProps=new p(this),this.addResolvingPromise(Promise.resolve())}isResolved(){return this._promiseProps.isResolved()}isRejected(){return this._promiseProps.isRejected()}isFulfilled(){return this._promiseProps.isFulfilled()}when(t,e){return new Promise(((t,e)=>{this._promiseProps.when(t,e)})).then(t,e)}catch(t){return this.when(null,t)}addResolvingPromise(t){t&&!this._promiseProps.isFulfilled()&&this._promiseProps.addResolvingPromise("_promiseProps"in t?t.when():t)}};return e=(0,i._)([(0,u.j)("esri.core.Promise")],e),e};let h=class extends(d(l.Z)){};h=(0,i._)([(0,u.j)("esri.core.Promise")],h)},70171:(t,e,r)=>{let o;r.d(e,{Kd:()=>l,Ze:()=>p,qe:()=>a});const s=globalThis.esriConfig?.locale??globalThis.dojoConfig?.locale;function i(){return s??globalThis.navigator?.language??"en"}function l(){return void 0===o&&(o=i()),o}const n=[];function a(t){return n.push(t),{remove(){n.splice(n.indexOf(t),1)}}}const u=[];function p(t){return u.push(t),{remove(){n.splice(u.indexOf(t),1)}}}globalThis.addEventListener?.("languagechange",(function(){const t=i();o!==t&&(o=t,[...u].forEach((e=>{e.call(null,t)})),[...n].forEach((e=>{e.call(null,t)})))}))},65587:(t,e,r)=>{r.d(e,{Z:()=>U});var o=r(43697),s=r(68773),i=r(40330),l=r(3172),n=r(20102),a=r(96674),u=r(83379),p=r(70586),d=r(95330),h=r(5600),c=r(75215),y=(r(67676),r(71715)),m=r(52011),_=r(6570),v=r(70171),b=r(41253),C=r(15923);let f=class extends C.Z{constructor(t){super(t),this.nextQueryParams=null,this.queryParams=null,this.results=null,this.total=null}};(0,o._)([(0,h.Cb)()],f.prototype,"nextQueryParams",void 0),(0,o._)([(0,h.Cb)()],f.prototype,"queryParams",void 0),(0,o._)([(0,h.Cb)()],f.prototype,"results",void 0),(0,o._)([(0,h.Cb)()],f.prototype,"total",void 0),f=(0,o._)([(0,m.j)("esri.portal.PortalQueryResult")],f);const g=f;var S,P=r(86082),O=r(71058);let w;const q={PortalGroup:()=>Promise.resolve().then(r.bind(r,68916)),PortalItem:()=>Promise.all([r.e(5235),r.e(9880)]).then(r.bind(r,15235)),PortalUser:()=>Promise.resolve().then(r.bind(r,86082))};let G=S=class extends((0,a.eC)(u.Z)){constructor(t){super(t),this._esriIdCredentialCreateHandle=null,this.access=null,this.allSSL=!1,this.authMode="auto",this.authorizedCrossOriginDomains=null,this.basemapGalleryGroupQuery=null,this.bingKey=null,this.canListApps=!1,this.canListData=!1,this.canListPreProvisionedItems=!1,this.canProvisionDirectPurchase=!1,this.canSearchPublic=!0,this.canShareBingPublic=!1,this.canSharePublic=!1,this.canSignInArcGIS=!1,this.canSignInIDP=!1,this.colorSetsGroupQuery=null,this.commentsEnabled=!1,this.created=null,this.culture=null,this.customBaseUrl=null,this.defaultBasemap=null,this.defaultDevBasemap=null,this.defaultExtent=null,this.defaultVectorBasemap=null,this.description=null,this.devBasemapGalleryGroupQuery=null,this.eueiEnabled=null,this.featuredGroups=null,this.featuredItemsGroupQuery=null,this.galleryTemplatesGroupQuery=null,this.livingAtlasGroupQuery=null,this.hasCategorySchema=!1,this.helperServices=null,this.homePageFeaturedContent=null,this.homePageFeaturedContentCount=null,this.httpPort=null,this.httpsPort=null,this.id=null,this.ipCntryCode=null,this.isPortal=!1,this.isReadOnly=!1,this.layerTemplatesGroupQuery=null,this.maxTokenExpirationMinutes=null,this.modified=null,this.name=null,this.portalHostname=null,this.portalMode=null,this.portalProperties=null,this.region=null,this.rotatorPanels=null,this.showHomePageDescription=!1,this.sourceJSON=null,this.supportsHostedServices=!1,this.symbolSetsGroupQuery=null,this.templatesGroupQuery=null,this.units=null,this.url=s.Z.portalUrl,this.urlKey=null,this.user=null,this.useStandardizedQuery=!1,this.useVectorBasemaps=!1,this.vectorBasemapGalleryGroupQuery=null}normalizeCtorArgs(t){return"string"==typeof t?{url:t}:t}destroy(){this._esriIdCredentialCreateHandle=(0,p.hw)(this._esriIdCredentialCreateHandle)}readAuthorizedCrossOriginDomains(t){if(t)for(const e of t)s.Z.request.trustedServers.includes(e)||s.Z.request.trustedServers.push(e);return t}readDefaultBasemap(t){return this._readBasemap(t)}readDefaultDevBasemap(t){return this._readBasemap(t)}readDefaultVectorBasemap(t){return this._readBasemap(t)}get extraQuery(){const t=!(this.user&&this.user.orgId)||this.canSearchPublic;return this.id&&!t?` AND orgid:${this.id}`:null}get isOrganization(){return!!this.access}get itemPageUrl(){return this.url?`${this.url}/home/<USER>"/sharing");t=e>0?t.substring(0,e):this.url.replace(/\/+$/,""),t+="/sharing/rest"}return t}get thumbnailUrl(){const t=this.restUrl,e=this.thumbnail;return t&&e?this._normalizeSSL(t+"/portals/self/resources/"+e):null}readUrlKey(t){return t?t.toLowerCase():t}readUser(t){let e=null;return t&&(e=P.default.fromJSON(t),e.portal=this),e}load(t){const e=Promise.all([r.e(5235),r.e(3668)]).then(r.bind(r,93668)).then((({default:e})=>{(0,d.k_)(t),w=e})).then((()=>this.sourceJSON?this.sourceJSON:this.fetchSelf(this.authMode,!1,t))).then((t=>{if(i.id){const t=i.id;this.credential=t.findCredential(this.restUrl),this.credential||this.authMode!==S.AUTH_MODE_AUTO||(this._esriIdCredentialCreateHandle=t.on("credential-create",(()=>{t.findCredential(this.restUrl)&&this.signIn().catch((()=>{}))})))}this.sourceJSON=t,this.read(t)}));return this.addResolvingPromise(e),Promise.resolve(this)}async createElevationLayers(){await this.load();const t=this._getHelperService("defaultElevationLayers"),e=(await Promise.all([r.e(4547),r.e(5235),r.e(3055),r.e(9230),r.e(9393)]).then(r.bind(r,65665))).default;return t?t.map((t=>new e({id:t.id,url:t.url}))):[]}fetchBasemaps(t,e){const r=new b.Z;return r.query=t||(s.Z.apiKey&&(0,O.r)(this.url)?this.devBasemapGalleryGroupQuery:this.useVectorBasemaps?this.vectorBasemapGalleryGroupQuery:this.basemapGalleryGroupQuery),r.disableExtraQuery=!0,this.queryGroups(r,e).then((t=>{if(r.num=100,r.query='type:"Web Map" -type:"Web Application"',t.total){const o=t.results[0];return r.sortField=o.sortField||"name",r.sortOrder=o.sortOrder||"desc",o.queryItems(r,e)}return null})).then((t=>{let e;return e=t&&t.total?t.results.filter((t=>"Web Map"===t.type)).map((t=>new w({portalItem:t}))):[],e}))}fetchCategorySchema(t){return this.hasCategorySchema?this.request(this.restUrl+"/portals/self/categorySchema",t).then((t=>t.categorySchema)):(0,d.Hc)(t)?Promise.reject((0,d.zE)()):Promise.resolve([])}fetchFeaturedGroups(t){const e=this.featuredGroups,r=new b.Z;if(r.num=100,r.sortField="title",e&&e.length){const o=[];for(const t of e)o.push(`(title:"${t.title}" AND owner:${t.owner})`);return r.query=o.join(" OR "),this.queryGroups(r,t).then((t=>t.results))}return(0,d.Hc)(t)?Promise.reject((0,d.zE)()):Promise.resolve([])}fetchRegions(t){const e=this.user?.culture||this.culture||(0,v.Kd)();return this.request(this.restUrl+"/portals/regions",{...t,query:{culture:e}})}fetchSettings(t){const e=this.user?.culture||this.culture||(0,v.Kd)();return this.request(this.restUrl+"/portals/self/settings",{...t,query:{culture:e}})}static getDefault(){return S._default&&!S._default.destroyed||(S._default=new S),S._default}queryGroups(t,e){return this.queryPortal("/community/groups",t,"PortalGroup",e)}queryItems(t,e){return this.queryPortal("/search",t,"PortalItem",e)}queryUsers(t,e){return t.sortField||(t.sortField="username"),this.queryPortal("/community/users",t,"PortalUser",e)}fetchSelf(t=this.authMode,e=!1,r){const o=this.restUrl+"/portals/self",s={authMode:t,query:{culture:(0,v.Kd)().toLowerCase()},...r};return"auto"===s.authMode&&(s.authMode="no-prompt"),e&&(s.query.default=!0),this.request(o,s)}queryPortal(t,e,r,o){const s=(0,c.se)(b.Z,e),i=e=>this.request(this.restUrl+t,{...s.toRequestOptions(this),...o}).then((t=>{const r=s.clone();return r.start=t.nextStart,new g({nextQueryParams:r,queryParams:s,total:t.total,results:S._resultsToTypedArray(e,{portal:this},t,o)})})).then((t=>Promise.all(t.results.map((e=>"function"==typeof e.when?e.when():t))).then((()=>t),(e=>((0,d.r9)(e),t)))));return r&&q[r]?q[r]().then((({default:t})=>((0,d.k_)(o),i(t)))):i()}signIn(){if(this.authMode===S.AUTH_MODE_ANONYMOUS)return Promise.reject(new n.Z("portal:invalid-auth-mode",`Current "authMode"' is "${this.authMode}"`));if("failed"===this.loadStatus)return Promise.reject(this.loadError);const t=t=>Promise.resolve().then((()=>"not-loaded"===this.loadStatus?(t||(this.authMode="immediate"),this.load().then((()=>null))):"loading"===this.loadStatus?this.load().then((()=>this.credential?null:(this.credential=t,this.fetchSelf("immediate")))):this.user&&this.credential===t?null:(this.credential=t,this.fetchSelf("immediate")))).then((t=>{t&&(this.sourceJSON=t,this.read(t))}));return i.id?i.id.getCredential(this.restUrl).then((e=>t(e))):t(this.credential)}normalizeUrl(t){const e=this.credential&&this.credential.token;return this._normalizeSSL(e?t+(t.includes("?")?"&":"?")+"token="+e:t)}requestToTypedArray(t,e,r){return this.request(t,e).then((t=>{const e=S._resultsToTypedArray(r,{portal:this},t);return Promise.all(e.map((e=>"function"==typeof e.when?e.when():t))).then((()=>e),(()=>e))}))}request(t,e={}){const r={f:"json",...e.query},{authMode:o=(this.authMode===S.AUTH_MODE_ANONYMOUS?"anonymous":"auto"),body:s=null,cacheBust:i=!1,method:n="auto",responseType:a="json",signal:u}=e,p={authMode:o,body:s,cacheBust:i,method:n,query:r,responseType:a,timeout:0,signal:u};return(0,l.default)(this._normalizeSSL(t),p).then((t=>t.data))}toJSON(){throw new n.Z("internal:not-yet-implemented","Portal.toJSON is not yet implemented")}static fromJSON(t){if(!t)return null;if(t.declaredClass)throw new Error("JSON object is already hydrated");return new S({sourceJSON:t})}_getHelperService(t){const e=this.helperServices&&this.helperServices[t];if(!e)throw new n.Z("portal:service-not-found",`The \`helperServices\` do not include an entry named "${t}"`);return e}_normalizeSSL(t){return t.replace(/^http:/i,"https:").replace(":7080",":7443")}_readBasemap(t){if(t){const e=w.fromJSON(t);return e.portalItem={portal:this},e}return null}static _resultsToTypedArray(t,e,r,o){let s=[];if(r){const i=(0,p.pC)(o)?o.signal:null;s=r.listings||r.notifications||r.userInvitations||r.tags||r.items||r.groups||r.comments||r.provisions||r.results||r.relatedItems||r,(t||e)&&(s=s.map((r=>{const o=Object.assign(t?t.fromJSON(r):r,e);return"function"==typeof o.load&&o.load(i),o})))}else s=[];return s}};G.AUTH_MODE_ANONYMOUS="anonymous",G.AUTH_MODE_AUTO="auto",G.AUTH_MODE_IMMEDIATE="immediate",(0,o._)([(0,h.Cb)()],G.prototype,"access",void 0),(0,o._)([(0,h.Cb)()],G.prototype,"allSSL",void 0),(0,o._)([(0,h.Cb)()],G.prototype,"authMode",void 0),(0,o._)([(0,h.Cb)()],G.prototype,"authorizedCrossOriginDomains",void 0),(0,o._)([(0,y.r)("authorizedCrossOriginDomains")],G.prototype,"readAuthorizedCrossOriginDomains",null),(0,o._)([(0,h.Cb)()],G.prototype,"basemapGalleryGroupQuery",void 0),(0,o._)([(0,h.Cb)()],G.prototype,"bingKey",void 0),(0,o._)([(0,h.Cb)()],G.prototype,"canListApps",void 0),(0,o._)([(0,h.Cb)()],G.prototype,"canListData",void 0),(0,o._)([(0,h.Cb)()],G.prototype,"canListPreProvisionedItems",void 0),(0,o._)([(0,h.Cb)()],G.prototype,"canProvisionDirectPurchase",void 0),(0,o._)([(0,h.Cb)()],G.prototype,"canSearchPublic",void 0),(0,o._)([(0,h.Cb)()],G.prototype,"canShareBingPublic",void 0),(0,o._)([(0,h.Cb)()],G.prototype,"canSharePublic",void 0),(0,o._)([(0,h.Cb)()],G.prototype,"canSignInArcGIS",void 0),(0,o._)([(0,h.Cb)()],G.prototype,"canSignInIDP",void 0),(0,o._)([(0,h.Cb)()],G.prototype,"colorSetsGroupQuery",void 0),(0,o._)([(0,h.Cb)()],G.prototype,"commentsEnabled",void 0),(0,o._)([(0,h.Cb)({type:Date})],G.prototype,"created",void 0),(0,o._)([(0,h.Cb)()],G.prototype,"credential",void 0),(0,o._)([(0,h.Cb)()],G.prototype,"culture",void 0),(0,o._)([(0,h.Cb)()],G.prototype,"currentVersion",void 0),(0,o._)([(0,h.Cb)()],G.prototype,"customBaseUrl",void 0),(0,o._)([(0,h.Cb)()],G.prototype,"defaultBasemap",void 0),(0,o._)([(0,y.r)("defaultBasemap")],G.prototype,"readDefaultBasemap",null),(0,o._)([(0,h.Cb)()],G.prototype,"defaultDevBasemap",void 0),(0,o._)([(0,y.r)("defaultDevBasemap")],G.prototype,"readDefaultDevBasemap",null),(0,o._)([(0,h.Cb)({type:_.Z})],G.prototype,"defaultExtent",void 0),(0,o._)([(0,h.Cb)()],G.prototype,"defaultVectorBasemap",void 0),(0,o._)([(0,y.r)("defaultVectorBasemap")],G.prototype,"readDefaultVectorBasemap",null),(0,o._)([(0,h.Cb)()],G.prototype,"description",void 0),(0,o._)([(0,h.Cb)()],G.prototype,"devBasemapGalleryGroupQuery",void 0),(0,o._)([(0,h.Cb)()],G.prototype,"eueiEnabled",void 0),(0,o._)([(0,h.Cb)({readOnly:!0})],G.prototype,"extraQuery",null),(0,o._)([(0,h.Cb)()],G.prototype,"featuredGroups",void 0),(0,o._)([(0,h.Cb)()],G.prototype,"featuredItemsGroupQuery",void 0),(0,o._)([(0,h.Cb)()],G.prototype,"galleryTemplatesGroupQuery",void 0),(0,o._)([(0,h.Cb)()],G.prototype,"livingAtlasGroupQuery",void 0),(0,o._)([(0,h.Cb)()],G.prototype,"hasCategorySchema",void 0),(0,o._)([(0,h.Cb)()],G.prototype,"helpBase",void 0),(0,o._)([(0,h.Cb)()],G.prototype,"helperServices",void 0),(0,o._)([(0,h.Cb)()],G.prototype,"helpMap",void 0),(0,o._)([(0,h.Cb)()],G.prototype,"homePageFeaturedContent",void 0),(0,o._)([(0,h.Cb)()],G.prototype,"homePageFeaturedContentCount",void 0),(0,o._)([(0,h.Cb)()],G.prototype,"httpPort",void 0),(0,o._)([(0,h.Cb)()],G.prototype,"httpsPort",void 0),(0,o._)([(0,h.Cb)()],G.prototype,"id",void 0),(0,o._)([(0,h.Cb)()],G.prototype,"ipCntryCode",void 0),(0,o._)([(0,h.Cb)({readOnly:!0})],G.prototype,"isOrganization",null),(0,o._)([(0,h.Cb)()],G.prototype,"isPortal",void 0),(0,o._)([(0,h.Cb)()],G.prototype,"isReadOnly",void 0),(0,o._)([(0,h.Cb)({readOnly:!0})],G.prototype,"itemPageUrl",null),(0,o._)([(0,h.Cb)()],G.prototype,"layerTemplatesGroupQuery",void 0),(0,o._)([(0,h.Cb)()],G.prototype,"maxTokenExpirationMinutes",void 0),(0,o._)([(0,h.Cb)({type:Date})],G.prototype,"modified",void 0),(0,o._)([(0,h.Cb)()],G.prototype,"name",void 0),(0,o._)([(0,h.Cb)()],G.prototype,"portalHostname",void 0),(0,o._)([(0,h.Cb)()],G.prototype,"portalMode",void 0),(0,o._)([(0,h.Cb)()],G.prototype,"portalProperties",void 0),(0,o._)([(0,h.Cb)()],G.prototype,"region",void 0),(0,o._)([(0,h.Cb)({readOnly:!0})],G.prototype,"restUrl",null),(0,o._)([(0,h.Cb)()],G.prototype,"rotatorPanels",void 0),(0,o._)([(0,h.Cb)()],G.prototype,"showHomePageDescription",void 0),(0,o._)([(0,h.Cb)()],G.prototype,"sourceJSON",void 0),(0,o._)([(0,h.Cb)()],G.prototype,"staticImagesUrl",void 0),(0,o._)([(0,h.Cb)({json:{name:"2DStylesGroupQuery"}})],G.prototype,"stylesGroupQuery2d",void 0),(0,o._)([(0,h.Cb)({json:{name:"stylesGroupQuery"}})],G.prototype,"stylesGroupQuery3d",void 0),(0,o._)([(0,h.Cb)()],G.prototype,"supportsHostedServices",void 0),(0,o._)([(0,h.Cb)()],G.prototype,"symbolSetsGroupQuery",void 0),(0,o._)([(0,h.Cb)()],G.prototype,"templatesGroupQuery",void 0),(0,o._)([(0,h.Cb)()],G.prototype,"thumbnail",void 0),(0,o._)([(0,h.Cb)({readOnly:!0})],G.prototype,"thumbnailUrl",null),(0,o._)([(0,h.Cb)()],G.prototype,"units",void 0),(0,o._)([(0,h.Cb)()],G.prototype,"url",void 0),(0,o._)([(0,h.Cb)()],G.prototype,"urlKey",void 0),(0,o._)([(0,y.r)("urlKey")],G.prototype,"readUrlKey",null),(0,o._)([(0,h.Cb)()],G.prototype,"user",void 0),(0,o._)([(0,y.r)("user")],G.prototype,"readUser",null),(0,o._)([(0,h.Cb)()],G.prototype,"useStandardizedQuery",void 0),(0,o._)([(0,h.Cb)()],G.prototype,"useVectorBasemaps",void 0),(0,o._)([(0,h.Cb)()],G.prototype,"vectorBasemapGalleryGroupQuery",void 0),G=S=(0,o._)([(0,m.j)("esri.portal.Portal")],G);const U=G},68916:(t,e,r)=>{r.r(e),r.d(e,{default:()=>h});var o=r(43697),s=r(20102),i=r(96674),l=r(70586),n=r(5600),a=r(75215),u=(r(67676),r(52011)),p=r(41253);let d=class extends i.wq{constructor(t){super(t),this.access=null,this.created=null,this.description=null,this.id=null,this.isInvitationOnly=!1,this.modified=null,this.owner=null,this.portal=null,this.snippet=null,this.sortField=null,this.sortOrder=null,this.tags=null,this.title=null}get thumbnailUrl(){const t=this.url,e=this.thumbnail;return t&&e&&this.portal?this.portal?.normalizeUrl(`${t}/info/${e}?f=json`):null}get url(){const t=this.get("portal.restUrl");return t?t+"/community/groups/"+this.id:null}fetchCategorySchema(t){return(0,l.s3)(this.portal).request(this.url+"/categorySchema",t).then((e=>{const r=e.categorySchema||[];return r.some((t=>"contentCategorySetsGroupQuery.LivingAtlas"===t.source))?this._fetchCategorySchemaSet("LivingAtlas",t):r}))}fetchMembers(t){return(0,l.s3)(this.portal).request(this.url+"/users",t)}getThumbnailUrl(t){let e=this.thumbnailUrl;return e&&t&&(e+=`&w=${t}`),e}toJSON(){throw new s.Z("internal:not-yet-implemented","PortalGroup.toJSON is not yet implemented")}queryItems(t,e){let r=(0,a.se)(p.Z,t);const o=(0,l.s3)(this.portal);return parseFloat(o.currentVersion)>5?(r=r||new p.Z,o.queryPortal(`/content/groups/${this.id}/search`,r,"PortalItem",e)):(r=r?r.clone():new p.Z,r.query="group:"+this.id+(r.query?" "+r.query:""),o.queryItems(r,e))}_fetchCategorySchemaSet(t,e){const r=(0,l.s3)(this.portal);return r.fetchSelf(r.authMode,!0,e).then((t=>{const o=t.contentCategorySetsGroupQuery;if(o){const t=new p.Z;return t.disableExtraQuery=!0,t.num=1,t.query=o,r.queryGroups(t,e)}throw new s.Z("portal-group:fetchCategorySchema","contentCategorySetsGroupQuery value not found")})).then((r=>{if(r.total){const o=r.results[0],s=new p.Z;return s.num=1,s.query=`typekeywords:"${t}"`,o.queryItems(s,e)}throw new s.Z("portal-group:fetchCategorySchema","contentCategorySetsGroupQuery group not found")})).then((t=>t.total?t.results[0].fetchData("json",e).then((t=>{const e=t&&t.categorySchema;return e&&e.length?e:[]})):[]))}};(0,o._)([(0,n.Cb)()],d.prototype,"access",void 0),(0,o._)([(0,n.Cb)({type:Date})],d.prototype,"created",void 0),(0,o._)([(0,n.Cb)()],d.prototype,"description",void 0),(0,o._)([(0,n.Cb)()],d.prototype,"id",void 0),(0,o._)([(0,n.Cb)()],d.prototype,"isInvitationOnly",void 0),(0,o._)([(0,n.Cb)({type:Date})],d.prototype,"modified",void 0),(0,o._)([(0,n.Cb)()],d.prototype,"owner",void 0),(0,o._)([(0,n.Cb)()],d.prototype,"portal",void 0),(0,o._)([(0,n.Cb)()],d.prototype,"snippet",void 0),(0,o._)([(0,n.Cb)()],d.prototype,"sortField",void 0),(0,o._)([(0,n.Cb)()],d.prototype,"sortOrder",void 0),(0,o._)([(0,n.Cb)()],d.prototype,"tags",void 0),(0,o._)([(0,n.Cb)()],d.prototype,"thumbnail",void 0),(0,o._)([(0,n.Cb)({readOnly:!0})],d.prototype,"thumbnailUrl",null),(0,o._)([(0,n.Cb)()],d.prototype,"title",void 0),(0,o._)([(0,n.Cb)({readOnly:!0})],d.prototype,"url",null),d=(0,o._)([(0,u.j)("esri.portal.PortalGroup")],d);const h=d},41253:(t,e,r)=>{r.d(e,{Z:()=>_});var o,s=r(43697),i=r(15923),l=r(35454),n=r(22974),a=r(70586),u=r(5600),p=(r(75215),r(52011)),d=r(6570),h=r(82971),c=r(40488);const y=new l.X({avgRating:"avg-rating",numRatings:"num-ratings",numComments:"num-comments",numViews:"num-views"});let m=o=class extends i.Z{constructor(t){super(t),this.categories=null,this.disableExtraQuery=!1,this.extent=null,this.filter=null,this.num=10,this.query=null,this.sortField=null,this.start=1}get sortOrder(){return this._get("sortOrder")||"asc"}set sortOrder(t){"asc"!==t&&"desc"!==t||this._set("sortOrder",t)}clone(){return new o({categories:this.categories?(0,n.d9)(this.categories):null,disableExtraQuery:this.disableExtraQuery,extent:this.extent?this.extent.clone():null,filter:this.filter,num:this.num,query:this.query,sortField:this.sortField,sortOrder:this.sortOrder,start:this.start})}toRequestOptions(t,e){let r=[];this.categories&&(r=this.categories.map((t=>Array.isArray(t)?JSON.stringify(t):t)));let o="";if(this.extent){const t=(0,c.iV)(this.extent,h.Z.WGS84);(0,a.pC)(t)&&(o=`${t.xmin},${t.ymin},${t.xmax},${t.ymax}`)}let s=this.query;!this.disableExtraQuery&&t.extraQuery&&(s="("+s+")"+t.extraQuery);const i={categories:r,bbox:o,q:s,filter:this.filter,num:this.num,sortField:null,sortOrder:null,start:this.start};return this.sortField&&(i.sortField=this.sortField.split(",").map((t=>y.toJSON(t.trim()))).join(","),i.sortOrder=this.sortOrder),{query:{...e,...i}}}};(0,s._)([(0,u.Cb)()],m.prototype,"categories",void 0),(0,s._)([(0,u.Cb)()],m.prototype,"disableExtraQuery",void 0),(0,s._)([(0,u.Cb)({type:d.Z})],m.prototype,"extent",void 0),(0,s._)([(0,u.Cb)()],m.prototype,"filter",void 0),(0,s._)([(0,u.Cb)()],m.prototype,"num",void 0),(0,s._)([(0,u.Cb)()],m.prototype,"query",void 0),(0,s._)([(0,u.Cb)()],m.prototype,"sortField",void 0),(0,s._)([(0,u.Cb)()],m.prototype,"sortOrder",null),(0,s._)([(0,u.Cb)()],m.prototype,"start",void 0),m=o=(0,s._)([(0,p.j)("esri.portal.PortalQueryParams")],m);const _=m},86082:(t,e,r)=>{r.r(e),r.d(e,{default:()=>c});var o=r(43697),s=r(20102),i=r(96674),l=r(5600),n=(r(75215),r(67676),r(52011));let a=class extends i.wq{constructor(t){super(t),this.created=null,this.id=null,this.portal=null,this.title=null,this.username=null}get url(){const t=this.get("portal.restUrl");return t?`${t}/content/users/${this.username}/${this.id}`:null}toJSON(){throw new s.Z("internal:not-yet-implemented","PortalFolder.toJSON is not yet implemented")}};(0,o._)([(0,l.Cb)({type:Date})],a.prototype,"created",void 0),(0,o._)([(0,l.Cb)()],a.prototype,"id",void 0),(0,o._)([(0,l.Cb)()],a.prototype,"portal",void 0),(0,o._)([(0,l.Cb)()],a.prototype,"title",void 0),(0,o._)([(0,l.Cb)({readOnly:!0})],a.prototype,"url",null),(0,o._)([(0,l.Cb)()],a.prototype,"username",void 0),a=(0,o._)([(0,n.j)("esri.portal.PortalFolder")],a);const u=a;var p,d=r(68916);let h=p=class extends i.wq{constructor(...t){super(...t),this.access=null,this.created=null,this.culture=null,this.description=null,this.email=null,this.fullName=null,this.modified=null,this.orgId=null,this.portal=null,this.preferredView=null,this.privileges=null,this.region=null,this.role=null,this.roleId=null,this.sourceJSON=null,this.units=null,this.username=null,this.userType=null}get thumbnailUrl(){const t=this.url,e=this.thumbnail;return t&&e?this.portal.normalizeUrl(`${t}/info/${e}?f=json`):null}get userContentUrl(){const t=this.get("portal.restUrl");return t?`${t}/content/users/${this.username}`:null}get url(){const t=this.get("portal.restUrl");return t?`${t}/community/users/${this.username}`:null}addItem(t){const e=t&&t.item,r=t&&t.data,o=t&&t.folder,s={method:"post"};e&&(s.query=e.createPostQuery(),null!=r&&("string"==typeof r?s.query.text=r:"object"==typeof r&&(s.query.text=JSON.stringify(r))));let i=this.userContentUrl;return o&&(i+="/"+("string"==typeof o?o:o.id)),this.portal.request(i+"/addItem",s).then((t=>(e.id=t.id,e.portal=this.portal,e.loaded?e.reload():e.load())))}deleteItem(t){let e=this.userContentUrl;return t.ownerFolder&&(e+="/"+t.ownerFolder),this.portal.request(e+`/items/${t.id}/delete`,{method:"post"}).then((()=>{t.id=null,t.portal=null}))}deleteItems(t){const e=this.userContentUrl+"/deleteItems",r=t.map((t=>t.id));if(r.length){const o={method:"post",query:{items:r.join(",")}};return this.portal.request(e,o).then((()=>{t.forEach((t=>{t.id=null,t.portal=null}))}))}return Promise.resolve(void 0)}fetchFolders(){return this.portal.request(this.userContentUrl??"",{query:{num:1}}).then((t=>{let e;return e=t&&t.folders?t.folders.map((t=>{const e=u.fromJSON(t);return e.portal=this.portal,e})):[],e}))}fetchGroups(){return this.portal.request(this.url??"").then((t=>{let e;return e=t&&t.groups?t.groups.map((t=>{const e=d.default.fromJSON(t);return e.portal=this.portal,e})):[],e}))}fetchItems(t){const e=t??{};let o,s=this.userContentUrl??"";return e.folder&&(s+="/"+e.folder.id),Promise.all([r.e(5235),r.e(9880)]).then(r.bind(r,15235)).then((({default:t})=>{o=t;const r={folders:!1,num:e.num||10,start:e.start||1,sortField:e.sortField||"created",sortOrder:e.sortOrder||"asc"};return this.portal.request(s,{query:r})})).then((t=>{let e;return t&&t.items?(e=t.items.map((t=>{const e=o.fromJSON(t);return e.portal=this.portal,e})),Promise.all(e.map((t=>t.load()))).catch((t=>t)).then((()=>({items:e,nextStart:t.nextStart,total:t.total})))):{items:[],nextStart:-1,total:0}}))}fetchTags(){return this.portal.request(this.url+"/tags").then((t=>t.tags))}getThumbnailUrl(t){let e=this.thumbnailUrl;return e&&t&&(e+=`&w=${t}`),e}queryFavorites(t){return this.favGroupId?(this._favGroup||(this._favGroup=new d.default({id:this.favGroupId,portal:this.portal})),this._favGroup.queryItems(t)):Promise.reject(new s.Z("internal:unknown","Unknown internal error",{internalError:"Unknown favGroupId"}))}toJSON(){throw new s.Z("internal:not-yet-implemented","PortalGroup.toJSON is not yet implemented")}static fromJSON(t){if(!t)return null;if(t.declaredClass)throw new Error("JSON object is already hydrated");const e=new p;return e.sourceJSON=t,e.read(t),e}};(0,o._)([(0,l.Cb)()],h.prototype,"access",void 0),(0,o._)([(0,l.Cb)({type:Date})],h.prototype,"created",void 0),(0,o._)([(0,l.Cb)()],h.prototype,"culture",void 0),(0,o._)([(0,l.Cb)()],h.prototype,"description",void 0),(0,o._)([(0,l.Cb)()],h.prototype,"email",void 0),(0,o._)([(0,l.Cb)()],h.prototype,"favGroupId",void 0),(0,o._)([(0,l.Cb)()],h.prototype,"fullName",void 0),(0,o._)([(0,l.Cb)({type:Date})],h.prototype,"modified",void 0),(0,o._)([(0,l.Cb)()],h.prototype,"orgId",void 0),(0,o._)([(0,l.Cb)()],h.prototype,"portal",void 0),(0,o._)([(0,l.Cb)()],h.prototype,"preferredView",void 0),(0,o._)([(0,l.Cb)()],h.prototype,"privileges",void 0),(0,o._)([(0,l.Cb)()],h.prototype,"region",void 0),(0,o._)([(0,l.Cb)()],h.prototype,"role",void 0),(0,o._)([(0,l.Cb)()],h.prototype,"roleId",void 0),(0,o._)([(0,l.Cb)()],h.prototype,"sourceJSON",void 0),(0,o._)([(0,l.Cb)()],h.prototype,"thumbnail",void 0),(0,o._)([(0,l.Cb)({readOnly:!0})],h.prototype,"thumbnailUrl",null),(0,o._)([(0,l.Cb)()],h.prototype,"units",void 0),(0,o._)([(0,l.Cb)({readOnly:!0})],h.prototype,"userContentUrl",null),(0,o._)([(0,l.Cb)({readOnly:!0})],h.prototype,"url",null),(0,o._)([(0,l.Cb)()],h.prototype,"username",void 0),(0,o._)([(0,l.Cb)()],h.prototype,"userType",void 0),h=p=(0,o._)([(0,n.j)("esri.portal.PortalUser")],h);const c=h}}]);