import{_ as B}from"./CardTable-rdWOL4_6.js";import{_ as w}from"./index-C9hz-UZb.js";import{_ as S}from"./InlineForm.vue_vue_type_style_index_0_lang-s-ANlzyw.js";import{_ as x}from"./index-BJ-QPYom.js";import y from"./TreeBox-mfOmxwZJ.js";import{u as D,a as F,b as j,c as L,d as V}from"./useHooks-BeUnknpc.js";import{d as I,c as _,o as P,Q as N,ay as O,g as q,h as A,F as t,q as r,i as o,p as M,j as Q,C as U}from"./index-r0dFAfgr.js";import"./index-BggOjNGp.js";import"./index-cIaXVz1R.js";const $={class:"card-wrapper"},z=I({__name:"realTime",setup(E){const a=_(),n=_(),{chartOption:l,refreshChart:s}=D(n),{TableConfig:f,refreshTable:m}=F(),{FormConfig:p,initFirstLineOfFilters:u,clearTimer:h}=j({refSearch:a,withInterval:!0,refreshCall:e=>{s(e),m(e)}}),{deviceList:d,getDeviceData:C}=L(),{TreeData:c,refreshProject:g}=V(async e=>{c.currentProject=e,await C(e==null?void 0:e.id),u(d.value||[])});return P(async()=>{await g(),s()}),N(()=>{h()}),(e,G)=>{const T=x,b=S,i=w,k=O("VChart"),v=B;return q(),A(y,null,{tree:t(()=>[r(T,{"tree-data":o(c)},null,8,["tree-data"])]),default:t(()=>[r(i,{class:"card-search"},{default:t(()=>[M("div",$,[r(b,{ref_key:"refSearch",ref:a,config:o(p)},null,8,["config"])])]),_:1}),r(i,{class:"card-chart",title:"实时数据"},{default:t(()=>[r(k,{ref_key:"refChart",ref:n,theme:o(Q)().isDark?"blackBackground":"whiteBackground",option:o(l)},null,8,["theme","option"])]),_:1}),r(v,{class:"card-table",config:o(f)},null,8,["config"])]),_:1})}}}),re=U(z,[["__scopeId","data-v-8251825b"]]);export{re as default};
