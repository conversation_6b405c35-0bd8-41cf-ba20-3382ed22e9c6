import{_ as Y}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as Z}from"./InlineForm.vue_vue_type_style_index_0_lang-s-ANlzyw.js";import{d as tt,c as A,r as m,Q as et,g as p,n as c,q as r,F as s,p as e,bo as f,bR as b,i as o,cs as h,aw as ot,j as it,bh as n,G as k,h as I,an as u,aB as x,aJ as lt,bz as at,dt as rt,du as nt,dv as st,bl as pt,aq as mt,bm as dt,by as ct,C as ut}from"./index-r0dFAfgr.js";/* empty css                         */import{_ as ft}from"./index-C9hz-UZb.js";import bt from"./RightDrawerMap-D5PhmGFO.js";import{i as F}from"./config-DqqM5K5L.js";import"./ArcView-DpMnCY82.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./widget-BcWKanF2.js";import"./pe-B8dP0-Ut.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./TileLayer-B5vQ99gG.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./commonProperties-DqNQ4F00.js";import"./project-DUuzYgGl.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./IdentifyResult-4DxLVhTm.js";import"./ViewHelper-BGCZjxXH.js";import"./fieldconfig-Bk3o1wi7.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./LayerHelper-Cn-iiqxI.js";import"./pipe-nogVzCHG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./ArcStationWarning-BF9YrSzF.js";import"./useWaterPoint-Bv0z6ym6.js";import"./DateFormatter-Bm9a68Ax.js";import"./zhandian-YaGuQZe6.js";import"./useStation-DJgnSZIA.js";import"./DrawerBox-CLde5xC8.js";import"./SideDrawer-CBntChyn.js";import"./PipeDetail-CTBPYFJW.js";import"./Search-NSrhrIa_.js";import"./FormTableColumnFilter-BT7pLXIC.js";import"./QueryHelper-ILO3qZqg.js";import"./config-fy91bijz.js";import"./ArcBRTools-BO92yznB.js";import"./PipeLength.vue_vue_type_script_setup_true_lang-BZfUsvDf.js";import"./arcWidgetButton-0glIxrt7.js";import"./StatisticsHelper-D-s_6AyQ.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./wfsUtils-DXofo3da.js";import"./usePipelineGroup-Ba1pmWz_.js";import"./GroupLayer-DhhN3FyN.js";import"./lazyLayerLoader-DbM9sT1W.js";import"./WFSLayer-1TtJp3nx.js";import"./clientSideDefaults-VQhQaYxh.js";import"./QueryEngineCapabilities-Dk3NJwmm.js";import"./wfsUtils-Du031XaC.js";import"./geojson-MBFu2-HZ.js";import"./xmlUtils-CtUoQO7q.js";import"./ListWindow-WS05QqV0.js";import"./PopLayout-BP55MvL7.js";import"./PoiSearchV2-D7yNeLuv.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./URLHelper-B9aplt5w.js";import"./useLayerList-DmEwJ-ws.js";import"./useScaleBar-Beed-z91.js";import"./OutsideWorkOrder-C6s8joBt.js";import"./index-CpGhZCTT.js";const _t={class:"page-wrapper"},gt={class:"detail-header"},vt={class:"detail-main"},yt={class:"content-wrapper"},ht={class:"left overlay-y"},kt={class:"list"},It={class:"tag-blue"},xt={class:"list"},Ct={class:"right overlay-y"},Tt={class:"timeline-item-wrapper"},Nt={class:"timeline-item-title"},Wt={class:"title"},Dt={class:"time"},wt={key:0,class:"text"},St={class:"text-name"},Vt={class:"text-name"},At={key:1,class:"text"},Ft={class:"table-box"},Lt={class:"page-wrapper"},Et={class:"tab-wrapper"},Ut={class:"table-box"},Pt=tt({__name:"PipeCollect",setup(Bt){const _=A(),l=m({curpage:"table",activeName:"rtk",tabConfig:{type:"tabs",tabs:[{label:"RTK任务",value:"rtk"},{label:"外业数据导入",value:"dataImport"}]},detailActionName:"basic",detail:{},timeline:[]}),L={},E=m({labelWidth:40,size:"small",group:[{fields:[{type:"radio-button",field:"status",label:"状态",options:[{label:"全部",value:""},{label:"待分派",value:"PENDING"},{label:"待接单",value:"ASSIGN"},{label:"处理中",value:"RESOLVING"},{label:"待审核",value:"SUBMIT"},{label:"审核通过",value:"APPROVED"}]},{labelWidth:70,type:"input",field:"serialNo",label:"任务编号"},{type:"input",field:"title",label:"标题"},{labelWidth:60,type:"select",field:"creator",label:"创建人",options:[]},{type:"btn-group",btns:[{perm:!0,text:"查询",iconifyIcon:"ep:search",click:()=>g()},{perm:!0,text:"重置",type:"default",iconifyIcon:"ep:refresh",click:()=>U()},{perm:!0,text:"新增",type:"success",iconifyIcon:"ep:plus",click:()=>P()}]}]}]}),U=()=>{},P=()=>{},d=m({dataList:[{code:"aaa",isOverTime:!0,status:"ASSIGN"}],columns:[{minWidth:120,label:"任务编号",prop:"code"},{minWidth:120,label:"任务名称",prop:"title"},{minWidth:120,label:"任务来源",prop:"source"},{minWidth:120,label:"探测类型",prop:"type"},{minWidth:120,label:"任务描述",prop:"remark"},{minWidth:120,label:"创建人",prop:"organizerId"},{minWidth:120,label:"创建时间",prop:"createTime"},{minWidth:120,label:"接收人",prop:"processUserId"},{minWidth:120,label:"是否超期",prop:"isOverTime",tag:!0,tagColor:(i,t)=>t===!0?"#ff0000":"#00ff00",formatter:(i,t)=>t===!0?"是":"否"},{minWidth:120,label:"状态",prop:"status",tag:!0,tagColor:"#409eff",formatter:(i,t)=>F(t)}],operations:[{perm:!0,text:"详情",iconifyIcon:"ep:info-filled",click:i=>T(i)},{perm:!0,text:"转发",type:"warning",iconifyIcon:"ep:promotion",click:i=>C(i)}],pagination:{refreshData:({page:i,size:t})=>{d.pagination.page=i,d.pagination.limit=t,g()}}}),C=i=>{var t;console.log(i),(t=N.value)==null||t.openDialog()},B=m({dataList:[],columns:[{minWidth:120,label:"任务编号",prop:"code"},{minWidth:120,label:"任务名称",prop:"title"},{minWidth:120,label:"任务描述",prop:"remark"},{minWidth:120,label:"创建人",prop:"organizerId"},{minWidth:120,label:"创建时间",prop:"createTime"},{minWidth:120,label:"状态",prop:"status"}],operations:[{perm:!0,text:"详情",iconifyIcon:"ep:info-filled",click:i=>T(i)},{perm:!0,text:"删除",type:"danger",iconifyIcon:"ep:delete",click:i=>C(i)}],pagination:{refreshData:({page:i,size:t})=>{d.pagination.page=i,d.pagination.limit=t,g()}}}),T=i=>{l.curpage="detail",console.log(i)},R=()=>{l.curpage="table"},g=()=>{},v=m({dataList:[],columns:[],pagination:{refreshData:({page:i,size:t})=>{v.pagination.page=i,v.pagination.limit=t,G()}}}),G=()=>{},N=A(),M=m({dialogWidth:450,title:"转发",labelPosition:"left",group:[{fields:[{type:"select",field:"receiver",label:"接收人",options:[]},{type:"textarea",field:"remark",label:"备注"}]}],submit:i=>{console.log(i)}}),O=()=>{var i,t;(i=_.value)==null||i.toggleCustomDetail(),(t=_.value)==null||t.toggleCustomDetailMaxmin("max")};return et(()=>{var i;(i=L.graphicsLayer)==null||i.removeAll()}),(i,t)=>{const W=at,z=rt,$=ft,q=nt,j=st,D=pt,y=mt,J=dt,K=ct,Q=Z,H=Y;return p(),c(x,null,[r(bt,{ref_key:"refMap",ref:_,title:"管网采集","hide-detail-close":!0,"hide-right-drawer":!0,"detail-max-min":!0,onMapLoaded:O},{"detail-header":s(()=>t[2]||(t[2]=[e("span",null,"采集任务管理",-1)])),"detail-default":s(()=>[f(e("div",_t,[e("div",gt,[r(o(h),{icon:"ep:back",onClick:R}),t[3]||(t[3]=e("div",{class:"detail-header-divider"},null,-1)),t[4]||(t[4]=e("span",null,"任务详情",-1))]),e("div",vt,[r(J,{modelValue:o(l).detailActionName,"onUpdate:modelValue":t[0]||(t[0]=a=>o(l).detailActionName=a),type:"border-card",class:ot(o(it)().isDark?"darkblue":"light")},{default:s(()=>[r(D,{label:"基础信息",name:"basic"},{default:s(()=>[e("div",yt,[e("div",ht,[r(W,{class:"card-t-0 darkblue"},{header:s(()=>[r(o(h),{icon:"mdi:book-open-blank-variant"}),t[5]||(t[5]=e("span",null," 关于任务 ",-1))]),default:s(()=>[e("ul",kt,[e("li",null,[t[6]||(t[6]=e("span",null,"编号",-1)),e("span",null,n(o(l).detail.serialNo),1)]),e("li",null,[t[7]||(t[7]=e("span",null,"名称：",-1)),e("span",null,n(o(l).detail.title),1)]),e("li",null,[t[8]||(t[8]=e("span",null,"描述：",-1)),e("span",null,n(o(l).detail.source),1)]),e("li",null,[t[9]||(t[9]=e("span",null,"是否超期：",-1)),e("span",It,n(o(l).detail.overtime?"是":"否"),1)]),e("li",null,[t[10]||(t[10]=e("span",null,"当前状态：",-1)),e("span",null,n(o(F)(o(l).detail.status)),1)])])]),_:1}),r(W,{class:"darkblue"},{header:s(()=>t[11]||(t[11]=[e("span",null,[e("i",{class:"iconfont icon-baogao"}),k(" 采集设备数量")],-1)])),default:s(()=>[e("ul",xt,[e("li",null,[t[12]||(t[12]=e("span",null,"处理人：",-1)),o(l).detail.processUserId?(p(),I(o(h),{key:0,icon:"ep:user"})):u("",!0),e("span",null,n(o(l).detail.processUserId),1)]),e("li",null,[t[13]||(t[13]=e("span",null,"处理级别：",-1)),e("span",null,n(o(l).detail.processLevel&&o(l).detail.processLevel+"级别"),1)]),e("li",null,[t[14]||(t[14]=e("span",null,"预计完成时间：",-1)),e("span",null,n(o(l).detail.estimatedFinishTime),1)]),e("li",null,[t[15]||(t[15]=e("span",null,"完成时间：",-1)),e("span",null,n(o(l).detail.completeTime),1)])])]),_:1})]),e("div",Ct,[r(j,null,{default:s(()=>[(p(!0),c(x,null,lt(o(l).timeline,(a,X)=>(p(),I(q,{key:X,hollow:!0,type:"success","hide-timestamp":!0,placement:"top"},{default:s(()=>[e("div",Tt,[e("div",Nt,[e("span",Wt,n(a.data.typeName),1),e("span",Dt,n(a.data.processTime),1)]),r($,{class:"timeline-item-content"},{default:s(()=>{var w,S,V;return[a.type==="text"?(p(),c(x,{key:0},[a.data.type==="ASSIGN"?(p(),c("p",wt,[t[16]||(t[16]=k(" 任务派发给了 ")),e("span",St,n((w=a.data)==null?void 0:w.nextProcessUserId),1),t[17]||(t[17]=k("， 操作人： ")),e("span",Vt,n((S=a.data)==null?void 0:S.processUserId),1)])):u("",!0),a.data.type==="RESOLVING"?(p(),c("p",At,n((V=a.data)==null?void 0:V.nextProcessUserId)+" 接收了工单 ",1)):u("",!0)],64)):u("",!0),(a==null?void 0:a.type)==="attr-table"?(p(),I(z,{key:1,data:a.data,columns:a.columns},null,8,["data","columns"])):u("",!0)]}),_:2},1024)])]),_:2},1024))),128))]),_:1})])])]),_:1}),r(D,{label:"采集信息",name:"collect"},{default:s(()=>[e("div",Ft,[r(y,{config:o(v)},null,8,["config"])])]),_:1})]),_:1},8,["modelValue","class"])])],512),[[b,o(l).curpage==="detail"]]),f(e("div",Lt,[e("div",Et,[r(K,{modelValue:o(l).activeName,"onUpdate:modelValue":t[1]||(t[1]=a=>o(l).activeName=a),config:o(l).tabConfig},null,8,["modelValue","config"])]),r(Q,{ref:"refForm",config:o(E)},null,8,["config"]),e("div",Ut,[f(r(y,{config:o(d)},null,8,["config"]),[[b,o(l).activeName==="rtk"]]),f(r(y,{config:o(B)},null,8,["config"]),[[b,o(l).activeName==="dataImport"]])])],512),[[b,o(l).curpage==="table"]])]),_:1},512),r(H,{ref_key:"refDialogFormTrans",ref:N,config:o(M)},null,8,["config"])],64)}}}),Ko=ut(Pt,[["__scopeId","data-v-40ffb687"]]);export{Ko as default};
