package org.thingsboard.server.dao.common;

import org.thingsboard.server.dao.model.sql.CommonTodoStatistic;
import org.thingsboard.server.dao.model.sql.SoStatistic;

public interface CommonService {
    /**
     * 获取指定用户二供巡检的待办统计
     *
     * @param userId 用户ID
     * @return 待办统计
     */
    CommonTodoStatistic getCommonTodoStatistic(String userId);

    /**
     * 智慧运营统计
     * @param tenantId 客户id
     * @return 统计信息
     */
    SoStatistic soStatistic(String tenantId);

}
