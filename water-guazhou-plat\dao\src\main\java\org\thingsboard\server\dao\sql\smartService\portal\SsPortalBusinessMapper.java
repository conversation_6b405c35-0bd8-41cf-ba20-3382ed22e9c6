package org.thingsboard.server.dao.sql.smartService.portal;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.thingsboard.server.dao.model.sql.smartService.portal.SsPortalBusiness;
import org.thingsboard.server.dao.util.imodel.query.smartService.portal.SsPortalActiveRequest;
import org.thingsboard.server.dao.util.imodel.query.smartService.portal.SsPortalBusinessPageRequest;

@Mapper
public interface SsPortalBusinessMapper extends BaseMapper<SsPortalBusiness> {
    IPage<SsPortalBusiness> findByPage(SsPortalBusinessPageRequest request);

    @SuppressWarnings("methodNotInXmlInspection")
    boolean update(SsPortalBusiness entity);

    boolean updateFully(SsPortalBusiness entity);

    boolean active(SsPortalActiveRequest request);

}
