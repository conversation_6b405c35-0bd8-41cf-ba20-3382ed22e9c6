package org.thingsboard.server.dao.model.sql.smartService.duty;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 班次管理
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2021-11-14
 */
@TableName("tb_service_duty_team_m")
@Data
public class DutyTeamM {

    @TableId
    private String id;

    private String code;

    private String name;

    private String remark;

    private transient String persons;

    private String creator;

    private transient String creatorName;

    private Date createTime;

    private Date updateTime;

    private String tenantId;

    private transient List<DutyTeamC> dutyTeamCList = new ArrayList<>();

}
