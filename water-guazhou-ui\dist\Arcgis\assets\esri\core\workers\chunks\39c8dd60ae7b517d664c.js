"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[9942,5935],{55343:(e,t,r)=>{r.d(t,{Z:()=>ve});var i,o=r(43697),n=r(96674),s=r(22974),l=r(5600),a=r(90578),d=r(71715),p=r(52011),u=r(30556),c=r(75215);r(67676);let y=i=class extends n.wq{constructor(e){super(e),this.expression=null,this.name=null,this.returnType="boolean",this.title=null}clone(){return new i({name:this.name,title:this.title,expression:this.expression,returnType:this.returnType})}};(0,o._)([(0,l.Cb)({type:String,json:{write:!0}})],y.prototype,"expression",void 0),(0,o._)([(0,l.Cb)({type:String,json:{write:!0}})],y.prototype,"name",void 0),(0,o._)([(0,l.Cb)({type:["boolean","date","number","string"],json:{write:!0}})],y.prototype,"returnType",void 0),(0,o._)([(0,l.Cb)({type:String,json:{write:!0}})],y.prototype,"title",void 0),y=i=(0,o._)([(0,p.j)("esri.form.ExpressionInfo")],y);const h=y;let m=class extends n.wq{constructor(e){super(e),this.description=null,this.label=null,this.type=null,this.visibilityExpression=null}};(0,o._)([(0,l.Cb)({type:String,json:{write:!0}})],m.prototype,"description",void 0),(0,o._)([(0,l.Cb)({type:String,json:{write:!0}})],m.prototype,"label",void 0),(0,o._)([(0,l.Cb)()],m.prototype,"type",void 0),(0,o._)([(0,l.Cb)({type:String,json:{write:!0}})],m.prototype,"visibilityExpression",void 0),m=(0,o._)([(0,p.j)("esri.form.elements.Element")],m);const b=m;var f;let v=f=class extends n.wq{constructor(e){super(e),this.type=null}clone(){return new f({type:this.type})}};(0,o._)([(0,l.Cb)({type:["attachment","audio","document","image","signature","video"],json:{write:!0}})],v.prototype,"type",void 0),v=f=(0,o._)([(0,p.j)("esri.form.elements.inputs.AttachmentInput")],v);const w=v;var g;let _=g=class extends b{constructor(e){super(e),this.attachmentKeyword=null,this.editable=!0,this.input=null,this.type="attachment"}clone(){return new g({attachmentKeyword:this.attachmentKeyword,description:this.description,editable:this.editable,input:this.input,label:this.label,visibilityExpression:this.visibilityExpression})}};(0,o._)([(0,l.Cb)({type:String,json:{write:!0}})],_.prototype,"attachmentKeyword",void 0),(0,o._)([(0,l.Cb)({type:Boolean,json:{write:!0}})],_.prototype,"editable",void 0),(0,o._)([(0,l.Cb)({type:w,json:{read:{source:"inputType"},write:{target:"inputType"}}})],_.prototype,"input",void 0),(0,o._)([(0,l.Cb)({type:["attachment"],json:{read:!1,write:!0}})],_.prototype,"type",void 0),_=g=(0,o._)([(0,p.j)("esri.form.elements.AttachmentElement")],_);const C=_;var j=r(60235),x=r(92604);let I=class extends n.wq{constructor(e){super(e),this.type=null}};(0,o._)([(0,l.Cb)()],I.prototype,"type",void 0),I=(0,o._)([(0,p.j)("esri.form.elements.inputs.Input")],I);const F=I;let S=class extends F{constructor(e){super(e),this.maxLength=null,this.minLength=0}};(0,o._)([(0,l.Cb)({type:Number,json:{write:!0}})],S.prototype,"maxLength",void 0),(0,o._)([(0,l.Cb)({type:Number,json:{write:!0}})],S.prototype,"minLength",void 0),S=(0,o._)([(0,p.j)("esri.form.elements.inputs.TextInput")],S);const E=S;var M;let O=M=class extends E{constructor(e){super(e),this.type="barcode-scanner"}clone(){return new M({maxLength:this.maxLength,minLength:this.minLength})}};(0,o._)([(0,l.Cb)({type:["barcode-scanner"],json:{read:!1,write:!0}})],O.prototype,"type",void 0),O=M=(0,o._)([(0,p.j)("esri.form.elements.inputs.BarcodeScannerInput")],O);const R=O;var T;let V=T=class extends F{constructor(e){super(e),this.noValueOptionLabel=null,this.showNoValueOption=!0,this.type="combo-box"}clone(){return new T({showNoValueOption:this.showNoValueOption,noValueOptionLabel:this.noValueOptionLabel})}};(0,o._)([(0,l.Cb)({type:String,json:{write:!0}})],V.prototype,"noValueOptionLabel",void 0),(0,o._)([(0,l.Cb)({type:Boolean,json:{write:!0}})],V.prototype,"showNoValueOption",void 0),(0,o._)([(0,l.Cb)({type:["combo-box"],json:{read:!1,write:!0}})],V.prototype,"type",void 0),V=T=(0,o._)([(0,p.j)("esri.form.elements.inputs.ComboBoxInput")],V);const Z=V;var L;function q(e){return null!=e?new Date(e):null}function A(e){return e?e.getTime():null}let k=L=class extends F{constructor(e){super(e),this.includeTime=!1,this.max=null,this.min=null,this.type="datetime-picker"}readMax(e,t){return q(t.max)}writeMax(e,t){t.max=A(e)}readMin(e,t){return q(t.min)}writeMin(e,t){t.min=A(e)}clone(){return new L({includeTime:this.includeTime,max:this.max,min:this.min})}};(0,o._)([(0,l.Cb)({type:Boolean,json:{write:!0}})],k.prototype,"includeTime",void 0),(0,o._)([(0,l.Cb)({type:Date,json:{type:Number,write:!0}})],k.prototype,"max",void 0),(0,o._)([(0,d.r)("max")],k.prototype,"readMax",null),(0,o._)([(0,u.c)("max")],k.prototype,"writeMax",null),(0,o._)([(0,l.Cb)({type:Date,json:{type:Number,write:!0}})],k.prototype,"min",void 0),(0,o._)([(0,d.r)("min")],k.prototype,"readMin",null),(0,o._)([(0,u.c)("min")],k.prototype,"writeMin",null),(0,o._)([(0,l.Cb)({type:["datetime-picker"],json:{read:!1,write:!0}})],k.prototype,"type",void 0),k=L=(0,o._)([(0,p.j)("esri.form.elements.inputs.DateTimePickerInput")],k);const U=k;var D;let N=D=class extends F{constructor(e){super(e),this.noValueOptionLabel=null,this.showNoValueOption=!0,this.type="radio-buttons"}clone(){return new D({noValueOptionLabel:this.noValueOptionLabel,showNoValueOption:this.showNoValueOption})}};(0,o._)([(0,l.Cb)({type:String,json:{write:!0}})],N.prototype,"noValueOptionLabel",void 0),(0,o._)([(0,l.Cb)({type:Boolean,json:{write:!0}})],N.prototype,"showNoValueOption",void 0),(0,o._)([(0,l.Cb)({type:["radio-buttons"],json:{read:!1,write:!0}})],N.prototype,"type",void 0),N=D=(0,o._)([(0,p.j)("esri.form.elements.inputs.RadioButtonsInput")],N);const P=N;var B;let G=B=class extends F{constructor(e){super(e),this.offValue=null,this.onValue=null,this.type="switch"}clone(){return new B({offValue:this.offValue,onValue:this.onValue})}};(0,o._)([(0,l.Cb)({type:[String,Number],json:{write:!0}})],G.prototype,"offValue",void 0),(0,o._)([(0,l.Cb)({type:[String,Number],json:{write:!0}})],G.prototype,"onValue",void 0),(0,o._)([(0,l.Cb)({type:["switch"],json:{read:!1,write:!0}})],G.prototype,"type",void 0),G=B=(0,o._)([(0,p.j)("esri.form.elements.inputs.SwitchInput")],G);const H=G;var J;let Q=J=class extends E{constructor(e){super(e),this.type="text-area"}clone(){return new J({maxLength:this.maxLength,minLength:this.minLength})}};(0,o._)([(0,l.Cb)({type:["text-area"],json:{read:!1,write:!0}})],Q.prototype,"type",void 0),Q=J=(0,o._)([(0,p.j)("esri.form.elements.inputs.TextAreaInput")],Q);const W=Q;var z;let $=z=class extends E{constructor(e){super(e),this.type="text-box"}clone(){return new z({maxLength:this.maxLength,minLength:this.minLength})}};(0,o._)([(0,l.Cb)({type:["text-box"],json:{read:!1,write:!0}})],$.prototype,"type",void 0),$=z=(0,o._)([(0,p.j)("esri.form.elements.inputs.TextBoxInput")],$);const K={base:F,key:"type",typeMap:{"barcode-scanner":R,"combo-box":Z,"datetime-picker":U,"radio-buttons":P,switch:H,"text-area":W,"text-box":$}};var X,Y=r(72729);const ee="esri.form.elements.FieldElement",te=x.Z.getLogger(ee);let re=X=class extends b{constructor(e){super(e),this.domain=null,this.editableExpression=null,this.fieldName=null,this.hint=null,this.input=null,this.requiredExpression=null,this.type="field",this.valueExpression=null}get editable(){return(0,j.Mr)(te,"editable",{replacement:"editableExpression",version:"4.26",warnOnce:!0}),this._get("editable")??!0}set editable(e){(0,j.Mr)(te,"editable",{replacement:"editableExpression",version:"4.26",warnOnce:!0}),this._set("editable",e)}clone(){return new X({description:this.description,domain:this.domain,editable:this.editable,editableExpression:this.editableExpression,fieldName:this.fieldName,hint:this.hint,input:this.input,label:this.label,requiredExpression:this.requiredExpression,valueExpression:this.valueExpression,visibilityExpression:this.visibilityExpression})}};(0,o._)([(0,l.Cb)({types:Y.V5,json:{read:{reader:Y.im},write:!0}})],re.prototype,"domain",void 0),(0,o._)([(0,l.Cb)({type:Boolean,json:{write:!0}})],re.prototype,"editable",null),(0,o._)([(0,l.Cb)({type:String,json:{write:!0}})],re.prototype,"editableExpression",void 0),(0,o._)([(0,l.Cb)({type:String,json:{write:!0}})],re.prototype,"fieldName",void 0),(0,o._)([(0,l.Cb)({type:String,json:{write:!0}})],re.prototype,"hint",void 0),(0,o._)([(0,l.Cb)({types:K,json:{read:{source:"inputType"},write:{target:"inputType"}}})],re.prototype,"input",void 0),(0,o._)([(0,l.Cb)({type:String,json:{write:!0}})],re.prototype,"requiredExpression",void 0),(0,o._)([(0,l.Cb)({type:String,json:{read:!1,write:!0}})],re.prototype,"type",void 0),(0,o._)([(0,l.Cb)({type:String,json:{write:!0}})],re.prototype,"valueExpression",void 0),re=X=(0,o._)([(0,p.j)(ee)],re);const ie=re;var oe,ne=r(44729);let se=oe=class extends b{constructor(e){super(e),this.displayCount=null,this.displayType="list",this.editable=!0,this.orderByFields=null,this.relationshipId=null,this.type="relationship"}clone(){return new oe({description:this.description,displayCount:this.displayCount,displayType:this.displayType,editable:this.editable,label:this.label,orderByFields:(0,s.d9)(this.orderByFields),relationshipId:this.relationshipId,visibilityExpression:this.visibilityExpression})}};(0,o._)([(0,l.Cb)({type:Number,json:{write:!0}})],se.prototype,"displayCount",void 0),(0,o._)([(0,l.Cb)({type:["list"],json:{write:!0}})],se.prototype,"displayType",void 0),(0,o._)([(0,l.Cb)({type:Boolean,json:{write:!0}})],se.prototype,"editable",void 0),(0,o._)([(0,l.Cb)({type:[ne.Z],json:{write:!0}})],se.prototype,"orderByFields",void 0),(0,o._)([(0,l.Cb)({type:Number,json:{write:!0}})],se.prototype,"relationshipId",void 0),(0,o._)([(0,l.Cb)({type:["relationship"],json:{read:!1,write:!0}})],se.prototype,"type",void 0),se=oe=(0,o._)([(0,p.j)("esri.form.elements.RelationshipElement")],se);const le=se;function ae(e){return{typesWithGroup:{base:b,key:"type",typeMap:{attachment:C,field:ie,group:e,relationship:le}},typesWithoutGroup:{base:b,key:"type",typeMap:{attachment:C,field:ie,relationship:le}}}}function de(e,t,r=!0){if(!e)return null;const i=r?t.typesWithGroup.typeMap:t.typesWithoutGroup.typeMap;return e.filter((e=>i[e.type])).map((e=>i[e.type].fromJSON(e)))}function pe(e,t,r=!0){if(!e)return null;const i=r?t.typesWithGroup.typeMap:t.typesWithoutGroup.typeMap;return e.filter((e=>i[e.type])).map((e=>e.toJSON()))}function ue(e,t,r=!0){return e?e.map((e=>(0,c.N7)(r?t.typesWithGroup:t.typesWithoutGroup,e))):null}var ce;let ye=ce=class extends b{constructor(e){super(e),this.elements=null,this.initialState="expanded",this.type="group"}castElements(e){return ue(e,he,!1)}readElements(e,t){return de(t.formElements,he,!1)}writeElements(e,t){t.formElements=pe(e,he,!1)}clone(){return new ce({description:this.description,elements:(0,s.d9)(this.elements),initialState:this.initialState,label:this.label,visibilityExpression:this.visibilityExpression})}};(0,o._)([(0,l.Cb)({json:{write:!0}})],ye.prototype,"elements",void 0),(0,o._)([(0,a.p)("elements")],ye.prototype,"castElements",null),(0,o._)([(0,d.r)("elements",["formElements"])],ye.prototype,"readElements",null),(0,o._)([(0,u.c)("elements")],ye.prototype,"writeElements",null),(0,o._)([(0,l.Cb)({type:["collapsed","expanded"],json:{write:!0}})],ye.prototype,"initialState",void 0),(0,o._)([(0,l.Cb)({type:String,json:{read:!1,write:!0}})],ye.prototype,"type",void 0),ye=ce=(0,o._)([(0,p.j)("esri.form.elements.GroupElement")],ye);const he=ae(ye);var me;const be=ae(ye);let fe=me=class extends n.wq{constructor(e){super(e),this.description=null,this.elements=null,this.expressionInfos=null,this.preserveFieldValuesWhenHidden=!1,this.title=null}castElements(e){return ue(e,be)}readElements(e,t){return de(t.formElements,be)}writeElements(e,t){t.formElements=pe(e,be)}clone(){return new me({description:this.description,expressionInfos:(0,s.d9)(this.expressionInfos),elements:(0,s.d9)(this.elements),title:this.title,preserveFieldValuesWhenHidden:this.preserveFieldValuesWhenHidden})}};(0,o._)([(0,l.Cb)({type:String,json:{write:!0}})],fe.prototype,"description",void 0),(0,o._)([(0,l.Cb)({json:{write:!0}})],fe.prototype,"elements",void 0),(0,o._)([(0,a.p)("elements")],fe.prototype,"castElements",null),(0,o._)([(0,d.r)("elements",["formElements"])],fe.prototype,"readElements",null),(0,o._)([(0,u.c)("elements")],fe.prototype,"writeElements",null),(0,o._)([(0,l.Cb)({type:[h],json:{write:!0}})],fe.prototype,"expressionInfos",void 0),(0,o._)([(0,l.Cb)({type:Boolean,json:{default:!1,write:!0}})],fe.prototype,"preserveFieldValuesWhenHidden",void 0),(0,o._)([(0,l.Cb)({type:String,json:{write:!0}})],fe.prototype,"title",void 0),fe=me=(0,o._)([(0,p.j)("esri.form.FormTemplate")],fe);const ve=fe},79235:(e,t,r)=>{r.d(t,{Z:()=>w});var i,o=r(43697),n=r(67676),s=r(35454),l=r(96674),a=r(67900),d=r(20941),p=r(5600),u=(r(75215),r(71715)),c=r(52011),y=r(30556);const h=(0,s.w)()({orthometric:"gravity-related-height",gravity_related_height:"gravity-related-height",ellipsoidal:"ellipsoidal"}),m=h.jsonValues.slice();(0,n.e$)(m,"orthometric");const b=(0,s.w)()({meter:"meters",foot:"feet","us-foot":"us-feet","clarke-foot":"clarke-feet","clarke-yard":"clarke-yards","clarke-link":"clarke-links","sears-yard":"sears-yards","sears-foot":"sears-feet","sears-chain":"sears-chains","benoit-1895-b-chain":"benoit-1895-b-chains","indian-yard":"indian-yards","indian-1937-yard":"indian-1937-yards","gold-coast-foot":"gold-coast-feet","sears-1922-truncated-chain":"sears-1922-truncated-chains","50-kilometers":"50-kilometers","150-kilometers":"150-kilometers"});let f=i=class extends l.wq{constructor(e){super(e),this.heightModel="gravity-related-height",this.heightUnit="meters",this.vertCRS=null}writeHeightModel(e,t,r){return h.write(e,t,r)}readHeightModel(e,t,r){return h.read(e)||(r&&r.messages&&r.messages.push(function(e,t){return new d.Z("height-model:unsupported",`Height model of value '${e}' is not supported`,t)}(e,{context:r})),null)}readHeightUnit(e,t,r){return b.read(e)||(r&&r.messages&&r.messages.push(v(e,{context:r})),null)}readHeightUnitService(e,t,r){return(0,a.$C)(e)||b.read(e)||(r&&r.messages&&r.messages.push(v(e,{context:r})),null)}readVertCRS(e,t){return t.vertCRS||t.ellipsoid||t.geoid}clone(){return new i({heightModel:this.heightModel,heightUnit:this.heightUnit,vertCRS:this.vertCRS})}equals(e){return!!e&&(this===e||this.heightModel===e.heightModel&&this.heightUnit===e.heightUnit&&this.vertCRS===e.vertCRS)}static deriveUnitFromSR(e,t){const r=(0,a.cM)(t);return new i({heightModel:e.heightModel,heightUnit:r,vertCRS:e.vertCRS})}write(e,t){return t={origin:"web-scene",...t},super.write(e,t)}static fromJSON(e){if(!e)return null;const t=new i;return t.read(e,{origin:"web-scene"}),t}};function v(e,t){return new d.Z("height-unit:unsupported",`Height unit of value '${e}' is not supported`,t)}(0,o._)([(0,p.Cb)({type:h.apiValues,constructOnly:!0,json:{origins:{"web-scene":{type:m,default:"ellipsoidal"}}}})],f.prototype,"heightModel",void 0),(0,o._)([(0,y.c)("web-scene","heightModel")],f.prototype,"writeHeightModel",null),(0,o._)([(0,u.r)(["web-scene","service"],"heightModel")],f.prototype,"readHeightModel",null),(0,o._)([(0,p.Cb)({type:b.apiValues,constructOnly:!0,json:{origins:{"web-scene":{type:b.jsonValues,write:b.write}}}})],f.prototype,"heightUnit",void 0),(0,o._)([(0,u.r)("web-scene","heightUnit")],f.prototype,"readHeightUnit",null),(0,o._)([(0,u.r)("service","heightUnit")],f.prototype,"readHeightUnitService",null),(0,o._)([(0,p.Cb)({type:String,constructOnly:!0,json:{origins:{"web-scene":{write:!0}}}})],f.prototype,"vertCRS",void 0),(0,o._)([(0,u.r)("service","vertCRS",["vertCRS","ellipsoid","geoid"])],f.prototype,"readVertCRS",null),f=i=(0,o._)([(0,c.j)("esri.geometry.HeightModelInfo")],f);const w=f},66361:(e,t,r)=>{r.d(t,{dU:()=>l,lQ:()=>p,o1:()=>u});var i=r(43697),o=r(32448),n=r(22974),s=(r(92604),r(75215),r(20102),r(80442),r(52011));const l=new o.Z.EventEmitter,a="esri.layers.mixins.EditBusLayer",d=Symbol(a);function p(e){return null!=e&&"object"==typeof e&&d in e}const u=e=>{var t;let r=class extends e{constructor(...e){super(...e),this[t]=!0,this.when().then((()=>{this.own([l.on("edits",(e=>{const t="layer"in e?e.layer:null,r="layer"in e?e.layer?.url:e.serviceUrl,i="layer"in e?e.layer?.layerId:e.layerId,o=e.event;if(t===this||r!==this.url)return;if(null!=i&&null!=this.layerId&&i===this.layerId)return void this.emit("edits",(0,n.d9)(o));const s=o.editedFeatures?.find((({layerId:e})=>e===this.layerId));if(s){const{adds:e,updates:t,deletes:r}=s.editedFeatures,i={edits:null,addedAttachments:[],deletedAttachments:[],updatedAttachments:[],addedFeatures:e?e.map((({attributes:e})=>({objectId:this.objectIdField&&e[this.objectIdField],globalId:this.globalIdField&&e[this.globalIdField]}))):[],deletedFeatures:r?r.map((({attributes:e})=>({objectId:this.objectIdField&&e[this.objectIdField],globalId:this.globalIdField&&e[this.globalIdField]}))):[],updatedFeatures:t?t.map((({current:{attributes:e}})=>({objectId:this.objectIdField&&e[this.objectIdField],globalId:this.globalIdField&&e[this.globalIdField]}))):[],editedFeatures:(0,n.d9)(o.editedFeatures),exceededTransferLimit:!1};this.emit("edits",i)}}))])}),(()=>{}))}};return t=d,r=(0,i._)([(0,s.j)(a)],r),r}},53713:(e,t,r)=>{r.d(t,{B:()=>V});var i=r(43697),o=r(46791),n=r(22974),s=r(92604),l=r(5600),a=(r(75215),r(71715)),d=r(52011),p=r(30556),u=r(6570),c=r(79235),y=r(82971),h=r(66677),m=r(21506),b=r(2368),f=r(96674),v=(r(67676),r(80216));let w=class extends((0,b.J)(f.wq)){constructor(e){super(e),this.creatorField=null,this.creationDateField=null,this.editorField=null,this.editDateField=null,this.realm=null,this.dateFieldsTimeReference=null}};(0,i._)([(0,l.Cb)()],w.prototype,"creatorField",void 0),(0,i._)([(0,l.Cb)()],w.prototype,"creationDateField",void 0),(0,i._)([(0,l.Cb)()],w.prototype,"editorField",void 0),(0,i._)([(0,l.Cb)()],w.prototype,"editDateField",void 0),(0,i._)([(0,l.Cb)()],w.prototype,"realm",void 0),(0,i._)([(0,l.Cb)({type:v.Z})],w.prototype,"dateFieldsTimeReference",void 0),w=(0,i._)([(0,d.j)("esri.layers.support.EditFieldsInfo")],w);const g=w;let _=class extends((0,b.J)(f.wq)){constructor(e){super(e)}};(0,i._)([(0,l.Cb)({constructOnly:!0,json:{write:!0}})],_.prototype,"name",void 0),(0,i._)([(0,l.Cb)({constructOnly:!0,json:{write:!0}})],_.prototype,"fields",void 0),(0,i._)([(0,l.Cb)({constructOnly:!0,json:{write:!0}})],_.prototype,"isAscending",void 0),(0,i._)([(0,l.Cb)({constructOnly:!0,json:{write:!0}})],_.prototype,"indexType",void 0),(0,i._)([(0,l.Cb)({constructOnly:!0,json:{write:!0}})],_.prototype,"isUnique",void 0),(0,i._)([(0,l.Cb)({constructOnly:!0,json:{write:!0}})],_.prototype,"description",void 0),_=(0,i._)([(0,d.j)("esri.layers.support.FeatureIndex")],_);var C=r(50957),j=r(67900);let x=class extends((0,b.J)(f.wq)){constructor(e){super(e),this.shapeAreaField=null,this.shapeLengthField=null,this.units=null}};(0,i._)([(0,l.Cb)({type:String,json:{read:{source:"shapeAreaFieldName"}}})],x.prototype,"shapeAreaField",void 0),(0,i._)([(0,l.Cb)({type:String,json:{read:{source:"shapeLengthFieldName"}}})],x.prototype,"shapeLengthField",void 0),(0,i._)([(0,l.Cb)({type:String,json:{read:e=>j.gV.read(e)||j.Jo.read(e)}})],x.prototype,"units",void 0),x=(0,i._)([(0,d.j)("esri.layers.support.GeometryFieldsInfo")],x);const I=x;var F=r(56765),S=r(35454);const E=new S.X({esriRelCardinalityOneToOne:"one-to-one",esriRelCardinalityOneToMany:"one-to-many",esriRelCardinalityManyToMany:"many-to-many"}),M=new S.X({esriRelRoleOrigin:"origin",esriRelRoleDestination:"destination"});let O=class extends((0,b.J)(f.wq)){constructor(e){super(e),this.cardinality=null,this.composite=null,this.id=null,this.keyField=null,this.keyFieldInRelationshipTable=null,this.name=null,this.relatedTableId=null,this.relationshipTableId=null,this.role=null}};(0,i._)([(0,l.Cb)({json:{read:E.read,write:E.write}})],O.prototype,"cardinality",void 0),(0,i._)([(0,l.Cb)({json:{read:!0,write:!0}})],O.prototype,"composite",void 0),(0,i._)([(0,l.Cb)({json:{read:!0,write:!0}})],O.prototype,"id",void 0),(0,i._)([(0,l.Cb)({json:{read:!0,write:!0}})],O.prototype,"keyField",void 0),(0,i._)([(0,l.Cb)({json:{read:!0,write:!0}})],O.prototype,"keyFieldInRelationshipTable",void 0),(0,i._)([(0,l.Cb)({json:{read:!0,write:!0}})],O.prototype,"name",void 0),(0,i._)([(0,l.Cb)({json:{read:!0,write:!0}})],O.prototype,"relatedTableId",void 0),(0,i._)([(0,l.Cb)({json:{read:!0,write:!0}})],O.prototype,"relationshipTableId",void 0),(0,i._)([(0,l.Cb)({json:{read:M.read,write:M.write}})],O.prototype,"role",void 0),O=(0,i._)([(0,d.j)("esri.layers.support.Relationship")],O);const R=O;var T=r(72064);const V=e=>{let t=class extends e{constructor(){super(...arguments),this.capabilities=null,this.copyright=null,this.dateFieldsTimeReference=null,this.datesInUnknownTimezone=!1,this.displayField=null,this.definitionExpression=null,this.editFieldsInfo=null,this.editingInfo=null,this.elevationInfo=null,this.floorInfo=null,this.fullExtent=null,this.gdbVersion=null,this.geometryFieldsInfo=null,this.geometryType=null,this.hasM=void 0,this.hasZ=void 0,this.heightModelInfo=null,this.historicMoment=null,this.isTable=!1,this.layerId=void 0,this.minScale=0,this.maxScale=0,this.globalIdField=null,this.objectIdField=null,this.preferredTimeReference=null,this.relationships=null,this.sourceJSON=null,this.returnM=void 0,this.returnZ=void 0,this.serviceDefinitionExpression=null,this.serviceItemId=null,this.spatialReference=y.Z.WGS84,this.subtypeField=null,this.trackIdField=null,this.indexes=new(o.Z.ofType(_)),this.version=void 0}readCapabilitiesFromService(e,t){return(0,T.h)(t,this.url)}get effectiveCapabilities(){const e=this.capabilities;if(!e)return null;const t=(0,n.d9)(e),{operations:r,editing:i}=t;return this.sourceJSON?.isMultiServicesView?(this.userHasUpdateItemPrivileges&&(r.supportsQuery=!0),t):this.userHasUpdateItemPrivileges?(r.supportsAdd=r.supportsDelete=r.supportsEditing=r.supportsQuery=r.supportsUpdate=i.supportsDeleteByOthers=i.supportsGeometryUpdate=i.supportsUpdateByOthers=!0,t):(this.userHasFullEditingPrivileges&&r.supportsEditing&&(r.supportsAdd=r.supportsDelete=r.supportsUpdate=i.supportsGeometryUpdate=!0),t)}readEditingInfo(e,t){const{editingInfo:r}=t;return r?{lastEditDate:null!=r.lastEditDate?new Date(r.lastEditDate):null}:null}readIsTableFromService(e,t){return"Table"===t.type}readMinScale(e,t){return t.effectiveMinScale||e||0}readMaxScale(e,t){return t.effectiveMaxScale||e||0}readGlobalIdFieldFromService(e,t){return(0,C.rk)(t)}readObjectIdFieldFromService(e,t){return(0,C.kZ)(t)}readServiceDefinitionExpression(e,t){return t.definitionQuery||t.definitionExpression}set url(e){const t=(0,h.XG)({layer:this,url:e,nonStandardUrlAllowed:!0,logger:s.Z.getLogger(this.declaredClass)});this._set("url",t.url),null!=t.layerId&&this._set("layerId",t.layerId)}writeUrl(e,t,r,i){(0,h.wH)(this,e,null,t,i)}readVersion(e,t){return(0,C.JY)(t)}};return(0,i._)([(0,l.Cb)({readOnly:!0,json:{read:!1,origins:{service:{read:{source:["advancedQueryCapabilities","allowGeometryUpdates","allowUpdateWithoutMValues","archivingInfo","capabilities","datesInUnknownTimezone","hasAttachments","hasM","hasZ","maxRecordCount","maxRecordCountFactor","ownershipBasedAccessControlForFeatures","standardMaxRecordCount","supportedQueryFormats","supportsAdvancedQueries","supportsApplyEditsWithGlobalIds","supportsAttachmentsByUploadId","supportsAttachmentsResizing","supportsCalculate","supportsCoordinatesQuantization","supportsExceedsLimitStatistics","supportsFieldDescriptionProperty","supportsQuantizationEditMode","supportsRollbackOnFailureParameter","supportsStatistics","supportsTruncate","supportsValidateSql","tileMaxRecordCount","useStandardizedQueries"]}}}}})],t.prototype,"capabilities",void 0),(0,i._)([(0,a.r)("service","capabilities")],t.prototype,"readCapabilitiesFromService",null),(0,i._)([(0,l.Cb)({readOnly:!0})],t.prototype,"effectiveCapabilities",null),(0,i._)([(0,l.Cb)({type:String,json:{origins:{service:{read:{source:"copyrightText"}}}}})],t.prototype,"copyright",void 0),(0,i._)([(0,l.Cb)({type:v.Z})],t.prototype,"dateFieldsTimeReference",void 0),(0,i._)([(0,l.Cb)({type:Boolean})],t.prototype,"datesInUnknownTimezone",void 0),(0,i._)([(0,l.Cb)({type:String,json:{origins:{service:{read:{source:"displayField"}}}}})],t.prototype,"displayField",void 0),(0,i._)([(0,l.Cb)({type:String,json:{origins:{service:{read:!1,write:!1}},name:"layerDefinition.definitionExpression",write:{enabled:!0,allowNull:!0}}})],t.prototype,"definitionExpression",void 0),(0,i._)([(0,l.Cb)({readOnly:!0,type:g})],t.prototype,"editFieldsInfo",void 0),(0,i._)([(0,l.Cb)({readOnly:!0})],t.prototype,"editingInfo",void 0),(0,i._)([(0,a.r)("editingInfo")],t.prototype,"readEditingInfo",null),(0,i._)([(0,l.Cb)((()=>{const e=(0,n.d9)(m.PV),t=e.json.origins;return t["web-map"]={read:!1,write:!1},t["portal-item"]={read:!1,write:!1},e})())],t.prototype,"elevationInfo",void 0),(0,i._)([(0,l.Cb)({type:F.Z,json:{read:{source:"layerDefinition.floorInfo"},write:{target:"layerDefinition.floorInfo"}}})],t.prototype,"floorInfo",void 0),(0,i._)([(0,l.Cb)({type:u.Z,json:{origins:{service:{read:{source:"extent"}}}}})],t.prototype,"fullExtent",void 0),(0,i._)([(0,l.Cb)()],t.prototype,"gdbVersion",void 0),(0,i._)([(0,l.Cb)({readOnly:!0,type:I,json:{read:{source:"geometryProperties"}}})],t.prototype,"geometryFieldsInfo",void 0),(0,i._)([(0,l.Cb)({type:["point","polygon","polyline","multipoint","multipatch","mesh"],json:{origins:{service:{read:C.Fr.read}}}})],t.prototype,"geometryType",void 0),(0,i._)([(0,l.Cb)({type:Boolean,json:{origins:{service:{read:!0}}}})],t.prototype,"hasM",void 0),(0,i._)([(0,l.Cb)({type:Boolean,json:{origins:{service:{read:!0}}}})],t.prototype,"hasZ",void 0),(0,i._)([(0,l.Cb)({readOnly:!0,type:c.Z})],t.prototype,"heightModelInfo",void 0),(0,i._)([(0,l.Cb)({type:Date})],t.prototype,"historicMoment",void 0),(0,i._)([(0,l.Cb)({readOnly:!0})],t.prototype,"isTable",void 0),(0,i._)([(0,a.r)("service","isTable",["type"])],t.prototype,"readIsTableFromService",null),(0,i._)([(0,l.Cb)({type:Number,json:{origins:{service:{read:{source:"id"}},"portal-item":{read:!1,write:{target:"id"}}},read:!1}})],t.prototype,"layerId",void 0),(0,i._)([(0,l.Cb)(m.rO)],t.prototype,"minScale",void 0),(0,i._)([(0,a.r)("service","minScale",["minScale","effectiveMinScale"])],t.prototype,"readMinScale",null),(0,i._)([(0,l.Cb)(m.u1)],t.prototype,"maxScale",void 0),(0,i._)([(0,a.r)("service","maxScale",["maxScale","effectiveMaxScale"])],t.prototype,"readMaxScale",null),(0,i._)([(0,l.Cb)({type:String})],t.prototype,"globalIdField",void 0),(0,i._)([(0,a.r)("service","globalIdField",["globalIdField","fields"])],t.prototype,"readGlobalIdFieldFromService",null),(0,i._)([(0,l.Cb)({type:String})],t.prototype,"objectIdField",void 0),(0,i._)([(0,a.r)("service","objectIdField",["objectIdField","fields"])],t.prototype,"readObjectIdFieldFromService",null),(0,i._)([(0,l.Cb)({type:v.Z})],t.prototype,"preferredTimeReference",void 0),(0,i._)([(0,l.Cb)({type:[R],readOnly:!0})],t.prototype,"relationships",void 0),(0,i._)([(0,l.Cb)()],t.prototype,"sourceJSON",void 0),(0,i._)([(0,l.Cb)({type:Boolean})],t.prototype,"returnM",void 0),(0,i._)([(0,l.Cb)({type:Boolean})],t.prototype,"returnZ",void 0),(0,i._)([(0,l.Cb)({readOnly:!0})],t.prototype,"serviceDefinitionExpression",void 0),(0,i._)([(0,a.r)("service","serviceDefinitionExpression",["definitionQuery","definitionExpression"])],t.prototype,"readServiceDefinitionExpression",null),(0,i._)([(0,l.Cb)({type:String,readOnly:!0,json:{read:!1,origins:{service:{read:!0}}}})],t.prototype,"serviceItemId",void 0),(0,i._)([(0,l.Cb)({type:y.Z,json:{origins:{service:{read:{source:"extent.spatialReference"}}}}})],t.prototype,"spatialReference",void 0),(0,i._)([(0,l.Cb)({type:String,readOnly:!0,json:{origins:{service:{read:!0}}}})],t.prototype,"subtypeField",void 0),(0,i._)([(0,l.Cb)({type:String,json:{read:{source:"timeInfo.trackIdField"}}})],t.prototype,"trackIdField",void 0),(0,i._)([(0,l.Cb)({readOnly:!0,json:{write:!1}})],t.prototype,"serverGens",void 0),(0,i._)([(0,l.Cb)({type:o.Z.ofType(_),readOnly:!0})],t.prototype,"indexes",void 0),(0,i._)([(0,l.Cb)(m.HQ)],t.prototype,"url",null),(0,i._)([(0,p.c)("url")],t.prototype,"writeUrl",null),(0,i._)([(0,l.Cb)({json:{origins:{service:{read:!0}},read:!1}})],t.prototype,"version",void 0),(0,i._)([(0,a.r)("service","version",["currentVersion","capabilities","drawingInfo","hasAttachments","htmlPopupType","relationships","timeInfo","typeIdField","types"])],t.prototype,"readVersion",null),t=(0,i._)([(0,d.j)("esri.layers.mixins.FeatureLayerBase")],t),t}},50957:(e,t,r)=>{r.d(t,{C9:()=>x,Ci:()=>v,FV:()=>f,Fr:()=>c,JD:()=>y,JY:()=>M,Jj:()=>b,KE:()=>C,SU:()=>w,VG:()=>_,Y5:()=>m,gG:()=>I,kZ:()=>E,kp:()=>j,nU:()=>R,rP:()=>F,rk:()=>S,sX:()=>T,tD:()=>g});var i=r(40330),o=r(20102),n=r(35454),s=r(70586),l=r(42033),a=r(84230),d=r(56545),p=r(14165),u=r(75935);const c=new n.X({esriGeometryPoint:"point",esriGeometryMultipoint:"multipoint",esriGeometryPolyline:"polyline",esriGeometryPolygon:"polygon",esriGeometryMultiPatch:"multipatch"});async function y(e,t,r,i){const n=await O(e);if(await h(e,t,i),!n.addAttachment)throw new o.Z(i,"Layer source does not support addAttachment capability");return n.addAttachment(t,r)}function h(e,t,r){const{attributes:i}=t,{objectIdField:n}=e;return e.get("capabilities.data.supportsAttachment")?t?i?n&&i[n]?Promise.resolve():Promise.reject(new o.Z(r,`feature is missing the identifying attribute ${n}`)):Promise.reject(new o.Z(r,"'attributes' are required on a feature to query attachments")):Promise.reject(new o.Z(r,"A feature is required to add/delete/update attachments")):Promise.reject(new o.Z(r,"this layer doesn't support attachments"))}async function m(e,t,r,i,n){const s=await O(e);if(await h(e,t,n),!s.updateAttachment)throw new o.Z(n,"Layer source does not support updateAttachment capability");return s.updateAttachment(t,r,i)}async function b(e,t,i){const o=await r.e(7035).then(r.bind(r,87269)),n=await e.load();return o.applyEdits(n,n.source,t,i)}async function f(e,t,r,i){const n=await O(e);if(await h(e,t,i),!n.deleteAttachments)throw new o.Z(i,"Layer source does not support deleteAttachments capability");return n.deleteAttachments(t,r)}async function v(e,t,r){const i=(await e.load({signal:t?.signal})).source;if(!i.fetchRecomputedExtents)throw new o.Z(r,"Layer source does not support fetchUpdates capability");return i.fetchRecomputedExtents(t)}async function w(e,t,r,i){t=d.Z.from(t),await e.load();const n=e.source,s=e.capabilities;if(!s?.data?.supportsAttachment)throw new o.Z(i,"this layer doesn't support attachments");const{attachmentTypes:l,objectIds:a,globalIds:p,num:u,size:c,start:y,where:h}=t;if(!s?.operations?.supportsQueryAttachments&&(l?.length>0||p?.length>0||c?.length>0||u||y||h))throw new o.Z(i,"when 'capabilities.operations.supportsQueryAttachments' is false, only objectIds is supported",t);if(!(a?.length||p?.length||h))throw new o.Z(i,"'objectIds', 'globalIds', or 'where' are required to perform attachment query",t);if(!n.queryAttachments)throw new o.Z(i,"Layer source does not support queryAttachments capability",t);return n.queryAttachments(t)}async function g(e,t,r,i){const n=await O(e);if(!n.queryObjectIds)throw new o.Z(i,"Layer source does not support queryObjectIds capability");return n.queryObjectIds(p.Z.from(t)??e.createQuery(),r)}async function _(e,t,r,i){const n=await O(e);if(!n.queryFeatureCount)throw new o.Z(i,"Layer source does not support queryFeatureCount capability");return n.queryFeatureCount(p.Z.from(t)??e.createQuery(),r)}async function C(e,t,r,i){const n=await O(e);if(!n.queryExtent)throw new o.Z(i,"Layer source does not support queryExtent capability");return n.queryExtent(p.Z.from(t)??e.createQuery(),r)}async function j(e,t,r,i){const n=await O(e);if(!n.queryRelatedFeatures)throw new o.Z(i,"Layer source does not support queryRelatedFeatures capability");return n.queryRelatedFeatures(u.default.from(t),r)}async function x(e,t,r,i){const n=await O(e);if(!n.queryRelatedFeaturesCount)throw new o.Z(i,"Layer source does not support queryRelatedFeaturesCount capability");return n.queryRelatedFeaturesCount(u.default.from(t),r)}async function I(e){const t=e.source;if(t?.refresh)try{const{dataChanged:r,updates:i}=await t.refresh();if((0,s.pC)(i)&&(e.sourceJSON={...e.sourceJSON,...i},e.read(i,{origin:"service",url:e.parsedUrl})),r)return!0}catch{}if(e.definitionExpression)try{return(await(0,l.E)(e.definitionExpression,e.fieldsIndex)).hasDateFunctions}catch{}return!1}function F(e){const t=new p.Z,r=e.get("capabilities.data"),i=e.get("capabilities.query");t.historicMoment=e.historicMoment,t.gdbVersion=e.gdbVersion,t.returnGeometry=!0,i&&(t.compactGeometryEnabled=i.supportsCompactGeometry,t.defaultSpatialReferenceEnabled=i.supportsDefaultSpatialReference),r&&(r.supportsZ&&null!=e.returnZ&&(t.returnZ=e.returnZ),r.supportsM&&null!=e.returnM&&(t.returnM=e.returnM)),t.outFields=["*"];const{timeOffset:o,timeExtent:n}=e;return t.timeExtent=null!=o&&null!=n?n.offset(-o.value,o.unit):n||null,t.multipatchOption="multipatch"===e.geometryType?"xyFootprint":null,t}function S(e){const{globalIdField:t,fields:r}=e;if(t)return t;if(r)for(const e of r)if("esriFieldTypeGlobalID"===e.type)return e.name}function E(e){const{objectIdField:t,fields:r}=e;if(t)return t;if(r)for(const e of r)if("esriFieldTypeOID"===e.type)return e.name}function M(e){return e.currentVersion?e.currentVersion:e.hasOwnProperty("capabilities")||e.hasOwnProperty("drawingInfo")||e.hasOwnProperty("hasAttachments")||e.hasOwnProperty("htmlPopupType")||e.hasOwnProperty("relationships")||e.hasOwnProperty("timeInfo")||e.hasOwnProperty("typeIdField")||e.hasOwnProperty("types")?10:9.3}async function O(e){return(await e.load()).source}async function R(e,t){const r=e.parsedUrl?.path;if(!r)return;const o=e.editFieldsInfo;(e.userHasUpdateItemPrivileges||e.userHasFullEditingPrivileges&&e.capabilities.operations.supportsEditing||o?.creatorField||o?.editorField)&&await async function(e,t){if(!i.id)return;if(i.id.findCredential(e))return;let r;try{const o=await(0,a.oP)(e,t);o&&(r=await i.id.checkSignInStatus(`${o}/sharing`))}catch(e){}if(r)try{const r=(0,s.pC)(t)?t.signal:null;await i.id.getCredential(e,{signal:r})}catch(e){}}(r,t)}function T(e){return!e.sourceJSON?.isMultiServicesView&&(e.userHasUpdateItemPrivileges||e.editingEnabled)}},60199:(e,t,r)=>{r.d(t,{D:()=>n});var i=r(66677);const o=[];function n(e,t){if((0,i.M8)(e.url??""))return!0;const{wkid:r}=t;for(const t of o){if((e.version??0)>=t[0])return!0;if("function"==typeof t[1]&&(t[1]=t[1]()),t[1].has(r))return!1}return!0}o.push([10.91,()=>{const e=new Set([9709,9716,9741,9761,9766]);for(let t=9712;t<=9713;t++)e.add(t);for(let t=9748;t<=9749;t++)e.add(t);for(let t=20904;t<=20932;t++)e.add(t);for(let t=21004;t<=21032;t++)e.add(t);for(let t=21207;t<=21264;t++)e.add(t);for(let t=21307;t<=21364;t++)e.add(t);for(let t=102759;t<=102760;t++)e.add(t);for(let t=102901;t<=102955;t++)e.add(t);return e}]),o.push([10.9,()=>{const e=new Set([9300,9354,9364,9367,9373,9377,9387,9456,9473,9498,9678,9680,29874,103599,103872,104028]);for(let t=9356;t<=9360;t++)e.add(t);for(let t=9404;t<=9407;t++)e.add(t);for(let t=9476;t<=9482;t++)e.add(t);for(let t=9487;t<=9494;t++)e.add(t);for(let t=9697;t<=9699;t++)e.add(t);return e}]),o.push([10.81,()=>{const e=new Set([9265,9333,103598,103699]);for(let t=9248;t<=9254;t++)e.add(t);for(let t=9271;t<=9273;t++)e.add(t);for(let t=9284;t<=9285;t++)e.add(t);for(let t=21453;t<=21463;t++)e.add(t);return e}]),o.push([10.8,()=>{const e=new Set([8088,8395,8428,8433,8531,8687,8692,8694,8699,8900,9003,9006,9009,9012,9017,9191]);for(let t=8035;t<=8036;t++)e.add(t);for(let t=8455;t<=8456;t++)e.add(t);for(let t=8518;t<=8529;t++)e.add(t);for(let t=8533;t<=8536;t++)e.add(t);for(let t=8538;t<=8540;t++)e.add(t);for(let t=8677;t<=8679;t++)e.add(t);for(let t=8902;t<=8903;t++)e.add(t);for(let t=8907;t<=8910;t++)e.add(t);for(let t=8949;t<=8951;t++)e.add(t);for(let t=8972;t<=8987;t++)e.add(t);for(let t=9039;t<=9040;t++)e.add(t);for(let t=9068;t<=9069;t++)e.add(t);for(let t=9140;t<=9141;t++)e.add(t);for(let t=9148;t<=9150;t++)e.add(t);for(let t=9153;t<=9159;t++)e.add(t);for(let t=9205;t<=9218;t++)e.add(t);for(let t=9221;t<=9222;t++)e.add(t);for(let t=54098;t<=54101;t++)e.add(t);return e}]),o.push([10.71,()=>{const e=new Set([6316]);for(let t=8351;t<=8353;t++)e.add(t);for(let t=9294;t<=9297;t++)e.add(t);for(let t=22619;t<=22621;t++)e.add(t);for(let t=103586;t<=103594;t++)e.add(t);return e}]),o.push([10.7,()=>{const e=new Set([8387,8391,8427,8545,8682,8685,8818,31370,104022,104024,104975]);for(let t=8065;t<=8068;t++)e.add(t);for(let t=8082;t<=8083;t++)e.add(t);for(let t=8379;t<=8385;t++)e.add(t);for(let t=8836;t<=8840;t++)e.add(t);for(let t=8857;t<=8860;t++)e.add(t);for(let t=53035;t<=53037;t++)e.add(t);for(let t=54090;t<=54091;t++)e.add(t);for(let t=102498;t<=102499;t++)e.add(t);return e}]),o.push([10.61,()=>new Set([102497])]),o.push([10.6,()=>{const e=new Set([7803,7805,7887,8086,8232,8237,8240,8246,8249,8252,8255,9019,9391]);for(let t=7755;t<=7787;t++)e.add(t);for(let t=7791;t<=7795;t++)e.add(t);for(let t=7799;t<=7801;t++)e.add(t);for(let t=7825;t<=7831;t++)e.add(t);for(let t=7877;t<=7878;t++)e.add(t);for(let t=7882;t<=7883;t++)e.add(t);for(let t=7991;t<=7992;t++)e.add(t);for(let t=8042;t<=8043;t++)e.add(t);for(let t=8058;t<=8059;t++)e.add(t);for(let t=8311;t<=8348;t++)e.add(t);for(let t=9060;t<=9067;t++)e.add(t);for(let t=102562;t<=102568;t++)e.add(t);for(let t=102799;t<=102900;t++)e.add(t);return e}]),o.push([10.51,()=>{const e=new Set([7683,7881,7886,7899,8888,9e3]);for(let t=8013;t<=8032;t++)e.add(t);for(let t=9053;t<=9057;t++)e.add(t);for(let t=104017;t<=104018;t++)e.add(t);for(let t=104971;t<=104974;t++)e.add(t);return e}]),o.push([10.5,()=>{const e=new Set([6962,7035,7037,7039,7041,7084,7086,7133,7798,102399]);for(let t=4087;t<=4088;t++)e.add(t);for(let t=5896;t<=5899;t++)e.add(t);for(let t=7005;t<=7007;t++)e.add(t);for(let t=7057;t<=7070;t++)e.add(t);for(let t=7073;t<=7082;t++)e.add(t);for(let t=7109;t<=7128;t++)e.add(t);for(let t=7844;t<=7859;t++)e.add(t);return e}])},75935:(e,t,r)=>{r.r(t),r.d(t,{default:()=>h});var i,o=r(43697),n=(r(66577),r(96674)),s=r(22974),l=r(5600),a=r(75215),d=r(52011),p=r(30556),u=r(10158),c=r(82971);let y=i=class extends n.wq{constructor(e){super(e),this.cacheHint=void 0,this.dynamicDataSource=void 0,this.gdbVersion=null,this.geometryPrecision=void 0,this.historicMoment=null,this.maxAllowableOffset=void 0,this.objectIds=null,this.orderByFields=null,this.outFields=null,this.outSpatialReference=null,this.relationshipId=void 0,this.start=void 0,this.num=void 0,this.returnGeometry=!1,this.returnM=void 0,this.returnZ=void 0,this.where=null}_writeHistoricMoment(e,t){t.historicMoment=e&&e.getTime()}writeStart(e,t){t.resultOffset=this.start,t.resultRecordCount=this.num||10,this.start>0&&null==this.where&&(t.definitionExpression="1=1")}clone(){return new i((0,s.d9)({cacheHint:this.cacheHint,dynamicDataSource:this.dynamicDataSource,gdbVersion:this.gdbVersion,geometryPrecision:this.geometryPrecision,historicMoment:this.historicMoment&&new Date(this.historicMoment.getTime()),maxAllowableOffset:this.maxAllowableOffset,objectIds:this.objectIds,orderByFields:this.orderByFields,outFields:this.outFields,outSpatialReference:this.outSpatialReference,relationshipId:this.relationshipId,start:this.start,num:this.num,returnGeometry:this.returnGeometry,where:this.where,returnZ:this.returnZ,returnM:this.returnM}))}};(0,o._)([(0,l.Cb)({type:Boolean,json:{write:!0}})],y.prototype,"cacheHint",void 0),(0,o._)([(0,l.Cb)({type:u.n,json:{write:!0}})],y.prototype,"dynamicDataSource",void 0),(0,o._)([(0,l.Cb)({type:String,json:{write:!0}})],y.prototype,"gdbVersion",void 0),(0,o._)([(0,l.Cb)({type:Number,json:{write:!0}})],y.prototype,"geometryPrecision",void 0),(0,o._)([(0,l.Cb)({type:Date})],y.prototype,"historicMoment",void 0),(0,o._)([(0,p.c)("historicMoment")],y.prototype,"_writeHistoricMoment",null),(0,o._)([(0,l.Cb)({type:Number,json:{write:!0}})],y.prototype,"maxAllowableOffset",void 0),(0,o._)([(0,l.Cb)({type:[Number],json:{write:!0}})],y.prototype,"objectIds",void 0),(0,o._)([(0,l.Cb)({type:[String],json:{write:!0}})],y.prototype,"orderByFields",void 0),(0,o._)([(0,l.Cb)({type:[String],json:{write:!0}})],y.prototype,"outFields",void 0),(0,o._)([(0,l.Cb)({type:c.Z,json:{read:{source:"outSR"},write:{target:"outSR"}}})],y.prototype,"outSpatialReference",void 0),(0,o._)([(0,l.Cb)({json:{write:!0}})],y.prototype,"relationshipId",void 0),(0,o._)([(0,l.Cb)({type:Number,json:{read:{source:"resultOffset"}}})],y.prototype,"start",void 0),(0,o._)([(0,p.c)("start"),(0,p.c)("num")],y.prototype,"writeStart",null),(0,o._)([(0,l.Cb)({type:Number,json:{read:{source:"resultRecordCount"}}})],y.prototype,"num",void 0),(0,o._)([(0,l.Cb)({json:{write:!0}})],y.prototype,"returnGeometry",void 0),(0,o._)([(0,l.Cb)({type:Boolean,json:{write:{overridePolicy:e=>({enabled:e})}}})],y.prototype,"returnM",void 0),(0,o._)([(0,l.Cb)({type:Boolean,json:{write:{overridePolicy:e=>({enabled:e})}}})],y.prototype,"returnZ",void 0),(0,o._)([(0,l.Cb)({type:String,json:{read:{source:"definitionExpression"},write:{target:"definitionExpression"}}})],y.prototype,"where",void 0),y=i=(0,o._)([(0,d.j)("esri.rest.support.RelationshipQuery")],y),y.from=(0,a.se)(y);const h=y}}]);