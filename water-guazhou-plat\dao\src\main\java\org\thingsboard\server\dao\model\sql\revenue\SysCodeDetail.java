package org.thingsboard.server.dao.model.sql.revenue;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;

@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.SYS_CODE_DETAIL_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class SysCodeDetail {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.SYS_CODE_DETAIL_CODE)
    private String code;

    @Column(name = ModelConstants.SYS_CODE_DETAIL_NAME)
    private String name;

    @Column(name = ModelConstants.SYS_CODE_DETAIL_ORDER_NUMBER)
    private Integer orderNumber;

    @Column(name = ModelConstants.SYS_CODE_DETAIL_KEY)
    private String key;

    @Column(name = ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;

}
