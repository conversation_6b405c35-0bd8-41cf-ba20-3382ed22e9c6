package org.thingsboard.server.dao.util.imodel.query.smartProduction.circuit;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartProduction.circuit.CircuitConfig;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;

@Getter
@Setter
public class CircuitConfigPageRequest extends AdvancedPageableQueryEntity<CircuitConfig, CircuitConfigPageRequest> {
    // 配置类型，用于数据隔离。三种类型：水源、水厂、二供泵房。
    private String type;

    // 巡检项目分类，多个用逗号隔开
    private String itemType;

    // 巡检配置名称
    private String name;

    // 巡检模板id
    private String templateId;
}
