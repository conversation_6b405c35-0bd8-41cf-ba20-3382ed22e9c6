package org.thingsboard.server.dao.model.sql.revenue;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;

@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.SYS_CODE_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class SysCode {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.SYS_CODE_CODE)
    private String code;

    @Column(name = ModelConstants.SYS_CODE_NAME)
    private String name;

    @Column(name = ModelConstants.SYS_CODE_ORDER_NUMBER)
    private Integer orderNumber;

    @Column(name = ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;

}
