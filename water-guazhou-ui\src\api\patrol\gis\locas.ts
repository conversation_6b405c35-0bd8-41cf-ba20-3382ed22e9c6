import { request } from '@/plugins/axios';

/**
 * 查询用户历史坐标列表
 * @param params
 * @returns
 */
export const GetUserHostoryLocas = (params: {
  userId?: string;
  page?: number;
  size?: number;
  fromTime?: string | number;
  toTime?: string | number;
}) => {
  return request({
    url: '/api/userCoordinate',
    method: 'get',
    params
  });
};

/**
 * 查询最新坐标列表
 * @param params
 * @returns
 */
export const GetLatestUserCoords = (params: {
  userName?: string;
  departmentId?: string;
  userTypeId?: string;
  status?: string;
  page?: number;
  size?: number;
  fromTime?: string;
  toTime?: string;
}) => {
  return request({
    url: '/api/userCoordinate/newest',
    method: 'get',
    params
  });
};
