<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.workOrder.WorkOrderRoleAuthRelationMapper">

    <insert id="batchInsert">
        INSERT INTO work_order_role_auth_relation(id, role_id, auth_type, dept_id) VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (
            #{element.id},
            #{element.roleId},
            #{element.authType},
            #{element.deptId}
            )
        </foreach>
    </insert>

</mapper>