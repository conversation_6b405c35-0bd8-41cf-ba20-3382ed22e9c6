package org.thingsboard.server.dao.smartPipe;

import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.request.PartitionMountRequest;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.PartitionPipe;

import java.util.List;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2023-05-26
 */
public interface PartitionPipeService {

    PartitionPipe save(PartitionPipe partitionPipe);


    PageData<PartitionPipe> getList(PartitionMountRequest request);


    void delete(List<String> ids);

}
