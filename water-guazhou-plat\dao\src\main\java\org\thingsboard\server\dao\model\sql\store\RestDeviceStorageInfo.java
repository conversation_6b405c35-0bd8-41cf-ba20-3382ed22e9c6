package org.thingsboard.server.dao.model.sql.store;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseTenantName;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

@Getter
@Setter
@ResponseEntity
public class RestDeviceStorageInfo {
    // id
    private String id;

    // 设备标签
    private String deviceLabelCode;

    // 序列号
    private String serialId;

    // 设备类型
    private String typeId;

    // 设备类型
    private String type;

    // 顶级类型
    private String topTypeId;

    // 顶级类型
    private String topType;

    // 设备型号
    private String model;

    // 设备名称
    private String name;

    // 可用数量
    private Integer count;

    // 单位
    private String unit;

    // 租户ID
    @ParseTenantName
    private String tenantId;
}
