<template>
  <div class="img-box">
    <div class="img-top">
      <img
        :src="state.currentImage"
        alt=""
      />
    </div>
    <div>
      <el-carousel
        :interval="4000"
        type="card"
        height="120px"
        class="mg_top_10"
        @change="toggle"
      >
        <el-carousel-item
          v-for="(item, index) in state.imgs"
          :key="index"
        >
          <img
            :src="item.img"
            style="width: 100%; height: 160px"
          />
        </el-carousel-item>
      </el-carousel>
    </div>
  </div>
</template>
<script lang="ts" setup>
import sc0 from '@/assets/images/shuichang/shuichang.png'
import sc1 from '@/assets/images/shuichang/sc1.png'
import sc2 from '@/assets/images/shuichang/sc2.png'
import sc3 from '@/assets/images/shuichang/sc3.png'
import sc4 from '@/assets/images/shuichang/sc4.png'
import sc5 from '@/assets/images/shuichang/sc5.png'
import sc6 from '@/assets/images/shuichang/sc6.png'

const state = reactive<{
  currentImage: string
  imgs: { label: string; img: string }[]
}>({
  currentImage: sc0,
  // 'http://***************:10000/bigData/1c8049e92d5f49109e1a6b909ca13521111.png',
  imgs: [
    {
      label: '水厂',
      img: sc0
    },
    {
      label: '沉淀池',
      img: sc1
      // 'http://***************:10000/bigData/1c8049e92d5f49109e1a6b909ca13521111.png'
    },
    {
      label: '反应池',
      img: sc2
      // img: 'http://***************:10000/bigData/aa8854b70b56464d8bfbce144892bee4222.png'
    },
    {
      label: '加药加氯间',
      img: sc3
      // 'http://***************:10000/bigData/6b9d7545d59d42829fa251813446c9b2333.png'
    },
    {
      label: '空压机鼓风机',
      img: sc4
      // 'http://***************:10000/bigData/b0b2fceb957649729e606a46e174b3bcIMG_20210318_100529.jpg'
    },
    {
      label: '浓缩池',
      img: sc5
      // 'http://***************:10000/bigData/1e18fa90a3ea4e37a49cb637914911f7555.png'
    },
    {
      label: '排污泵房',
      img: sc6
      // 'http://***************:10000/bigData/dd0cadf1f7064c939cea1866060ccadc666.png'
    }
  ]
})
const toggle = (id: number) => {
  state.currentImage = state.imgs[id].img
}
onMounted(() => {
  toggle(0)
})
</script>
<style lang="scss" scoped>
.img-box {
  width: 100%;
  height: 100%;
  .img-top {
    height: 270px;
    margin-bottom: 12px;
    img {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
