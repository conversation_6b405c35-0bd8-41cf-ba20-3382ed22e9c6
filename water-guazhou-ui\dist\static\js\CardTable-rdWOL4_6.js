import{_ as Q}from"./index-C9hz-UZb.js";import{d as V,c as B,r as R,bv as S,h as i,bg as w,F as m,g as n,ax as F,p as N,n as l,bh as P,an as _,aB as c,aJ as h,aw as q,av as z,i as y,q as E,by as I,aq as L,C as U}from"./index-r0dFAfgr.js";const $={id:"title",class:"title-wrapper"},D={key:0,class:"title"},J={key:1,class:"right-wrapper"},A=V({__name:"CardTable",props:{config:{}},setup(G,{expose:v}){const a=B(),s=R({titleQueryParams:{}});return v({refTable:a,getTable:()=>a.value&&a.value.refElTable,exportTable:(e,u)=>{a.value&&a.value.exportTable(e,u)},...S(s)}),(e,u)=>{var f,d;const g=I,k=L,C=Q;return n(),i(C,{class:"table-card",title:e.config.title||!!((f=e.config.titleRight)!=null&&f.length)},w({default:m(()=>[E(k,{ref_key:"refTable",ref:a,config:e.config,"in-card":!0},null,8,["config"])]),_:2},[e.config.title||(d=e.config.titleRight)!=null&&d.length?{name:"title",fn:m(()=>[F(e.$slots,"title",{},()=>{var p;return[N("div",$,[e.config.title?(n(),l("span",D,P(e.config.title),1)):_("",!0),(p=e.config.titleRight)!=null&&p.length?(n(),l("div",J,[(n(!0),l(c,null,h(e.config.titleRight,(r,b)=>(n(),l("div",{key:b,class:q(r.className),style:z(r.style)},[(n(!0),l(c,null,h(r.items,(t,T)=>(n(),l(c,{key:T},[t.field?(n(),i(g,{key:0,modelValue:y(s).titleQueryParams[t.field],"onUpdate:modelValue":o=>y(s).titleQueryParams[t.field]=o,config:t,onChange:o=>e.config.handleQuery&&e.config.handleQuery(o,t)},null,8,["modelValue","onUpdate:modelValue","config","onChange"])):(n(),i(g,{key:1,config:t,onChange:o=>e.config.handleQuery&&e.config.handleQuery(o,t)},null,8,["config","onChange"]))],64))),128))],6))),128))])):_("",!0)])]},!0)]),key:"0"}:void 0]),1032,["title"])}}}),W=U(A,[["__scopeId","data-v-3b01cd93"]]);export{W as _};
