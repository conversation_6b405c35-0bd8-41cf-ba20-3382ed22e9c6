import{e as n,y as r,a as S,v as E,M as W,Q as Z,U as q,w as G,S as A,aF as v,ax as U}from"./Point-WxyopZva.js";import{aR as Q,gl as X,gF as K,B as F,c_ as J,S as Y,g as j,l as ee,gG as te,gH as ie,m as se,gI as L,v as ne,cY as re,cv as oe,cb as ae,z as le,cM as de,f as pe,gJ as ue,gK as he}from"./MapView-DaoQedLH.js";import{R as p,T,b2 as ce,eR as ye,b0 as ge}from"./index-r0dFAfgr.js";import{l as _,U as B,k as b,a as fe}from"./widget-BcWKanF2.js";import{y as me}from"./elevationInfoUtils-5B4aSzEU.js";import{W as ve,$ as we,a as _e}from"./boundedPlane-DeyjpfhM.js";import{i as Se,p as be}from"./queryEngineUtils-zApNHdaJ.js";import{h as Ie}from"./WorkerHandle-3vEm1fum.js";import{M as Oe}from"./dehydratedFeatures-CEuswj7y.js";import"./pe-B8dP0-Ut.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./lineSegment-DQ0q5UHF.js";import"./plane-BhzlJB-C.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./enums-B5k73o5q.js";import"./quat-CM9ioDFt.js";import"./TileLayer-B5vQ99gG.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./commonProperties-DqNQ4F00.js";import"./project-DUuzYgGl.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./VertexSnappingCandidate-CgwNICNk.js";function k(e,t){return we(t.extent,V),_e(V,Q(ke,e.x,e.y,0))}const V=ve(),ke=X();let y=class extends E{get tiles(){const t=this.tilesCoveringView,i=p(this.pointOfInterest)?this.pointOfInterest:this.view.center;return t.sort((a,s)=>k(i,a)-k(i,s)),t}_scaleEnabled(){return K(this.view.scale,this.layer.minScale||0,this.layer.maxScale||0)}get tilesCoveringView(){if(!this.view.ready||!this.view.featuresTilingScheme||!this.view.state||T(this.tileInfo))return[];if(!this._scaleEnabled)return[];const{spans:t,lodInfo:i}=this.view.featuresTilingScheme.getTileCoverage(this.view.state,0),{level:a}=i,s=[];for(const{row:l,colFrom:o,colTo:d}of t)for(let u=o;u<=d;u++){const g=i.normalizeCol(u),f=new F(null,a,l,g);this.tileInfo.updateTileInfo(f),s.push(f)}return s}get tileInfo(){var t;return((t=this.view.featuresTilingScheme)==null?void 0:t.tileInfo)??null}get tileSize(){return p(this.tileInfo)?this.tileInfo.size[0]:256}constructor(t){super(t),this.pointOfInterest=null}initialize(){this.addHandles(_(()=>{var t,i;return(i=(t=this.view)==null?void 0:t.state)==null?void 0:i.viewpoint},()=>this.notifyChange("tilesCoveringView"),B))}};n([r({readOnly:!0})],y.prototype,"tiles",null),n([r({readOnly:!0})],y.prototype,"_scaleEnabled",null),n([r({readOnly:!0})],y.prototype,"tilesCoveringView",null),n([r({readOnly:!0})],y.prototype,"tileInfo",null),n([r({readOnly:!0})],y.prototype,"tileSize",null),n([r({constructOnly:!0})],y.prototype,"view",void 0),n([r({constructOnly:!0})],y.prototype,"layer",void 0),n([r()],y.prototype,"pointOfInterest",void 0),y=n([S("esri.views.interactive.snapping.featureSources.featureServiceSource.FeatureServiceTiles2D")],y);var C;(function(e){e[e.INVISIBLE=0]="INVISIBLE",e[e.VISIBLE_WHEN_EXTENDED=1]="VISIBLE_WHEN_EXTENDED",e[e.VISIBLE_ON_SURFACE=2]="VISIBLE_ON_SURFACE"})(C||(C={}));let m=class extends J{get tiles(){const e=this.tilesCoveringView,t=this._effectivePointOfInterest;if(p(t)){const i=e.map(a=>k(t,a));for(let a=1;a<i.length;a++)if(i[a-1]>i[a])return e.sort((s,l)=>k(t,s)-k(t,l)),e.slice()}return e}get tilesCoveringView(){var e,t;return this._filterTiles((t=(e=this.view.featureTiles)==null?void 0:e.tiles)==null?void 0:t.toArray()).map(He)}get tileInfo(){var e;return((e=this.view.featureTiles)==null?void 0:e.tilingScheme.toTileInfo())??null}get tileSize(){var e;return((e=this.view.featureTiles)==null?void 0:e.tileSize)??256}get _effectivePointOfInterest(){var t;const e=this.pointOfInterest;return p(e)?e:(t=this.view.pointsOfInterest)==null?void 0:t.focus.location}constructor(e){super(e),this.pointOfInterest=null}initialize(){this.handles.add(_(()=>this.view.featureTiles,e=>{this.handles.remove(D),e&&this.handles.add(e.addClient(),D)},b))}_filterTiles(e){return T(e)?[]:e.filter(t=>Math.abs(t.measures.screenRect[3]-t.measures.screenRect[1])>$e&&t.measures.visibility===C.VISIBLE_ON_SURFACE)}};function He({lij:[e,t,i],extent:a}){return new F(`${e}/${t}/${i}`,e,t,i,a)}n([r({readOnly:!0})],m.prototype,"tiles",null),n([r({readOnly:!0})],m.prototype,"tilesCoveringView",null),n([r({readOnly:!0})],m.prototype,"tileInfo",null),n([r({readOnly:!0})],m.prototype,"tileSize",null),n([r({constructOnly:!0})],m.prototype,"view",void 0),n([r()],m.prototype,"pointOfInterest",void 0),n([r()],m.prototype,"_effectivePointOfInterest",null),m=n([S("esri.views.interactive.snapping.featureSources.featureServiceSource.FeatureServiceTiles3D")],m);const $e=50,D="feature-tiles",Ee=[[0,179,255],[117,62,128],[0,104,255],[215,189,166],[32,0,193],[98,162,206],[102,112,129],[52,125,0],[142,118,246],[138,83,0],[92,122,255],[122,55,83],[0,142,255],[81,40,179],[0,200,244],[13,24,127],[0,170,147],[19,58,241],[22,44,35]];let I=class extends E{constructor(e){super(e),this.updating=!1,this.enablePolygons=!0,this.enableLabels=!0,this._polygons=new Map,this._labels=new Map,this._enabled=!0}initialize(){this._symbols=Ee.map(e=>new Y({color:[e[0],e[1],e[2],.6],outline:{color:"black",width:1}})),this.update()}destroy(){this._enabled=!1,this.clear()}get enabled(){return this._enabled}set enabled(e){this._enabled!==e&&(this._enabled=e,this.update())}update(){if(!this._enabled)return void this.clear();const e=l=>{if(p(l.label))return l.label;let o=l.lij.toString();return p(l.loadPriority)&&(o+=` (${l.loadPriority})`),o},t=this.getTiles(),i=new Array,a=new Set((this._labels.size,this._labels.keys()));t.forEach((l,o)=>{const d=l.lij.toString();a.delete(d);const u=l.lij[0],g=l.geometry;if(this.enablePolygons&&!this._polygons.has(d)){const f=new j({geometry:g,symbol:this._symbols[u%this._symbols.length]});this._polygons.set(d,f),i.push(f)}if(this.enableLabels){const f=e(l),z=o/(t.length-1),P=L(0,200,z),M=L(20,6,z)/.75,N=p(l.loadPriority)&&l.loadPriority>=t.length,R=new ee([P,N?0:P,N?0:P]),x=this.view.type==="3d"?()=>new te({verticalOffset:{screenLength:40/.75},callout:{type:"line",color:"white",border:{color:"black"}},symbolLayers:[new ie({text:f,halo:{color:"white",size:1/.75},material:{color:R},size:M})]}):()=>new se({text:f,haloColor:"white",haloSize:1/.75,color:R,size:M}),H=this._labels.get(d);if(H){const O=x();(T(H.symbol)||JSON.stringify(O)!==JSON.stringify(H.symbol))&&(H.symbol=O)}else{const O=new j({geometry:g.extent.center,symbol:x()});this._labels.set(d,O),i.push(O)}}});const s=new Array;a.forEach(l=>{const o=this._polygons.get(l);o!=null&&(s.push(o),this._polygons.delete(l));const d=this._labels.get(l);d!=null&&(s.push(d),this._labels.delete(l))}),this.view.graphics.removeMany(s),this.view.graphics.addMany(i)}clear(){this.view.graphics.removeMany(Array.from(this._polygons.values())),this.view.graphics.removeMany(Array.from(this._labels.values())),this._polygons.clear(),this._labels.clear()}};n([r({constructOnly:!0})],I.prototype,"view",void 0),n([r({readOnly:!0})],I.prototype,"updating",void 0),n([r()],I.prototype,"enabled",null),I=n([S("esri.views.support.TileTreeDebugger")],I);let $=class extends I{constructor(e){super(e),this._handles=new W}initialize(){const e=setInterval(()=>this._fetchDebugInfo(),2e3);this._handles.add(Z(()=>clearInterval(e)))}destroy(){this._handles.destroy()}getTiles(){if(!this._debugInfo)return[];const e=new Map,t=new Map;this._debugInfo.storedTiles.forEach(s=>{e.set(s.data.id,s.featureCount)}),this._debugInfo.pendingTiles.forEach(s=>{e.set(s.data.id,s.featureCount),t.set(s.data.id,s.state)});const i=s=>{const l=t.get(s),o=e.get(s)??"?";return l?`${l}:${o}
${s}`:`store:${o}
${s}`},a=new Map;return this._debugInfo.storedTiles.forEach(s=>{a.set(s.data.id,s.data)}),this._debugInfo.pendingTiles.forEach(s=>{a.set(s.data.id,s.data)}),Array.from(a.values()).map(s=>({lij:[s.level,s.row,s.col],geometry:ne.fromExtent(re(s.extent,this.view.spatialReference)),label:i(s.id)}))}_fetchDebugInfo(){this.handle.getDebugInfo(null).then(e=>{this._debugInfo=e,this.update()})}};n([r({constructOnly:!0})],$.prototype,"handle",void 0),$=n([S("esri.views.interactive.snapping.featureSources.WorkerTileTreeDebugger")],$);let h=class extends J{get updating(){return this.updatingHandles.updating||this._workerHandleUpdating}constructor(e){super(e),this.schedule=null,this.hasZ=!1,this.elevationAlignPointsInFeatures=async t=>{const i=[];for(const{points:a}of t)for(const{z:s}of a)i.push(s);return{elevations:i,drapedObjectIds:new Set,failedObjectIds:new Set}},this.queryForSymbologySnapping=async()=>({candidates:[],sourceCandidateIndices:[]}),this.availability=0,this._workerHandleUpdating=!0,this._editId=0}destroy(){this._workerHandle.destroy()}initialize(){this._workerHandle=new Te(this.schedule,{alignElevation:async(e,{signal:t})=>({result:await this.elevationAlignPointsInFeatures(e.points,t)}),getSymbologyCandidates:async(e,{signal:t})=>({result:await this.queryForSymbologySnapping(e,t)})}),this.handles.add([this._workerHandle.on("notify-updating",({updating:e})=>this._workerHandleUpdating=e),this._workerHandle.on("notify-availability",({availability:e})=>this._set("availability",e))])}async setup(e,t){var s;const i=this._serviceInfoFromLayer(e.layer);if(T(i))return;const a={configuration:this._convertConfiguration(e.configuration),serviceInfo:i,spatialReference:e.spatialReference.toJSON(),hasZ:this.hasZ,elevationInfo:(s=e.layer.elevationInfo)==null?void 0:s.toJSON()};await this.updatingHandles.addPromise(this._workerHandle.invokeMethod("setup",a,t)),this.updatingHandles.addPromise(this._workerHandle.invokeMethod("whenNotUpdating",{},t))}async configure(e,t){const i=this._convertConfiguration(e);await this.updatingHandles.addPromise(this._workerHandle.invokeMethod("configure",i,t)),this.updatingHandles.addPromise(this._workerHandle.invokeMethod("whenNotUpdating",{},t))}async refresh(e){await this.updatingHandles.addPromise(this._workerHandle.invokeMethod("refresh",{},e)),this.updatingHandles.addPromise(this._workerHandle.invokeMethod("whenNotUpdating",{},e))}async fetchCandidates(e,t){const i=e.point,a={distance:e.distance,mode:e.mode,point:Oe(i[0],i[1],i[2],e.coordinateHelper.spatialReference.toJSON()),types:e.types,filter:p(e.filter)?e.filter.toJSON():null};return this._workerHandle.invoke(a,t)}async updateTiles(e,t){const i={tiles:e.tiles,tileInfo:p(e.tileInfo)?e.tileInfo.toJSON():null,tileSize:e.tileSize};await this.updatingHandles.addPromise(this._workerHandle.invokeMethod("updateTiles",i,t)),this.updatingHandles.addPromise(this._workerHandle.invokeMethod("whenNotUpdating",{},t))}async applyEdits(e,t){var o,d,u;const i=this._editId++,a={id:i};await this.updatingHandles.addPromise(this._workerHandle.invokeMethod("beginApplyEdits",a,t));const s=await this.updatingHandles.addPromise(q(e.result,t)),l={id:i,edits:{addedFeatures:((o=s.addedFeatures)==null?void 0:o.map(({objectId:g})=>g).filter(p))??[],deletedFeatures:((d=s.deletedFeatures)==null?void 0:d.map(({objectId:g,globalId:f})=>({objectId:g,globalId:f})))??[],updatedFeatures:((u=s.updatedFeatures)==null?void 0:u.map(({objectId:g})=>g).filter(p))??[]}};await this.updatingHandles.addPromise(this._workerHandle.invokeMethod("endApplyEdits",l,t)),this.updatingHandles.addPromise(this._workerHandle.invokeMethod("whenNotUpdating",{},t))}getDebugInfo(e){return this._workerHandle.invokeMethod("getDebugInfo",{},e)}async notifyElevationSourceChange(){await this._workerHandle.invokeMethod("notifyElevationSourceChange",{})}async notifySymbologyChange(){await this._workerHandle.invokeMethod("notifySymbologyChange",{})}async setSymbologySnappingSupported(e){await this._workerHandle.invokeMethod("setSymbologySnappingSupported",e)}_convertConfiguration(e){return{filter:p(e.filter)?e.filter.toJSON():null,customParameters:e.customParameters,viewType:e.viewType}}_serviceInfoFromLayer(e){var t,i;return e.geometryType==="multipatch"||e.geometryType==="mesh"?null:{url:((t=e.parsedUrl)==null?void 0:t.path)??"",fields:e.fields.map(a=>a.toJSON()),geometryType:oe.toJSON(e.geometryType),capabilities:e.capabilities,objectIdField:e.objectIdField,globalIdField:e.globalIdField,spatialReference:e.spatialReference.toJSON(),timeInfo:(i=e.timeInfo)==null?void 0:i.toJSON()}}};n([r({constructOnly:!0})],h.prototype,"schedule",void 0),n([r({constructOnly:!0})],h.prototype,"hasZ",void 0),n([r({constructOnly:!0})],h.prototype,"elevationAlignPointsInFeatures",void 0),n([r({constructOnly:!0})],h.prototype,"queryForSymbologySnapping",void 0),n([r({readOnly:!0})],h.prototype,"updating",null),n([r({readOnly:!0})],h.prototype,"availability",void 0),n([r()],h.prototype,"_workerHandleUpdating",void 0),h=n([S("esri.views.interactive.snapping.featureSources.featureServiceSource.FeatureServiceSnappingSourceWorkerHandle")],h);class Te extends Ie{constructor(t,i){super("FeatureServiceSnappingSourceWorker","fetchCandidates",{},t,{strategy:"dedicated",client:i})}}let w=class extends E{get tiles(){return[new F("0/0/0",0,0,0,ae(-1e8,-1e8,1e8,1e8))]}get tileInfo(){return new le({origin:new G({x:-1e8,y:1e8,spatialReference:this.layer.spatialReference}),size:[512,512],lods:[new de({level:0,scale:1,resolution:390625})],spatialReference:this.layer.spatialReference})}get tileSize(){return this.tileInfo.size[0]}constructor(e){super(e),this.pointOfInterest=null}};n([r({readOnly:!0})],w.prototype,"tiles",null),n([r({readOnly:!0})],w.prototype,"tileInfo",null),n([r({readOnly:!0})],w.prototype,"tileSize",null),n([r({constructOnly:!0})],w.prototype,"layer",void 0),n([r()],w.prototype,"pointOfInterest",void 0),w=n([S("esri.views.interactive.snapping.featureSources.featureServiceSource.FeatureServiceTilesSimple")],w);let c=class extends pe(E){get _updateTilesParameters(){return{tiles:this._tilesOfInterest.tiles,tileInfo:this._tilesOfInterest.tileInfo,tileSize:this._tilesOfInterest.tileSize}}get updating(){var e;return((e=this._workerHandle)==null?void 0:e.updating)||this.updatingHandles.updating}get configuration(){const{view:e}=this,t=p(e)?e.type:"2d";return{filter:this._layer.createQuery(),customParameters:this._layer.customParameters,viewType:t}}get availability(){var e;return((e=this._workerHandle)==null?void 0:e.availability)??0}get _layer(){return this.layerSource.layer}constructor(e){super(e),this._workerHandle=null,this._debug=null}initialize(){let e;const t=this.view;if(p(t))switch(t.type){case"2d":this._tilesOfInterest=new y({view:t,layer:this._layer}),e=this._workerHandle=new h;break;case"3d":{const{resourceController:i}=t,a=this._layer,s=t.whenLayerView(a);this._tilesOfInterest=new m({view:t}),e=this._workerHandle=new h({schedule:o=>i.immediate.schedule(o),hasZ:this._layer.hasZ&&(this._layer.returnZ??!0),elevationAlignPointsInFeatures:async(o,d)=>{const u=await s;return A(d),u.elevationAlignPointsInFeatures(o,d)},queryForSymbologySnapping:async(o,d)=>{const u=await s;return A(d),u.queryForSymbologySnapping(o,d)}});const l=new he(null);s.then(o=>l.set(o)),this.addHandles([t.elevationProvider.on("elevation-change",({context:o})=>{const{elevationInfo:d}=a;me(o,d)&&v(e.notifyElevationSourceChange())}),_(()=>a.elevationInfo,()=>v(e.notifyElevationSourceChange()),b),_(()=>ce(l.get(),({processor:o})=>o==null?void 0:o.renderer),()=>v(e.notifySymbologyChange()),b),_(()=>ye(l.get(),!1,o=>o.symbologySnappingSupported),o=>v(e.setSymbologySnappingSupported(o)),b),fe(()=>{var o;return(o=ge(l.get()))==null?void 0:o.layer},["edits","apply-edits","graphic-update"],()=>e.notifySymbologyChange())]);break}}else this._tilesOfInterest=new w({layer:this._layer}),e=this._workerHandle=new h;this.handles.add([U(e)]),v(e.setup({layer:this._layer,spatialReference:this.spatialReference,configuration:this.configuration},null)),this.updatingHandles.add(()=>this._updateTilesParameters,()=>v(e.updateTiles(this._updateTilesParameters,null)),b),this.handles.add([_(()=>this.configuration,i=>v(e.configure(i,null)),B)]),p(t)&&this.handles.add(_(()=>ue.FEATURE_SERVICE_SNAPPING_SOURCE_TILE_TREE_SHOW_TILES,i=>{i&&!this._debug?(this._debug=new $({view:t,handle:e}),this.handles.add(U(this._debug),"debug")):!i&&this._debug&&this.handles.remove("debug")},b)),this.handles.add(this.layerSource.layer.on("apply-edits",i=>{v(e.applyEdits(i,null))}))}refresh(){var e;(e=this._workerHandle)==null||e.refresh(null)}async fetchCandidates(e,t){const{coordinateHelper:i,point:a}=e;this._tilesOfInterest.pointOfInterest=i.arrayToPoint(a);const s=this._getGroundElevation;return(await this._workerHandle.fetchCandidates({...e},t)).candidates.map(l=>Se(l,s))}getDebugInfo(e){return this._workerHandle.getDebugInfo(e)}get _getGroundElevation(){return be(this.view)}};n([r({constructOnly:!0})],c.prototype,"spatialReference",void 0),n([r({constructOnly:!0})],c.prototype,"layerSource",void 0),n([r({constructOnly:!0})],c.prototype,"view",void 0),n([r()],c.prototype,"_tilesOfInterest",void 0),n([r({readOnly:!0})],c.prototype,"_updateTilesParameters",null),n([r({readOnly:!0})],c.prototype,"updating",null),n([r({readOnly:!0})],c.prototype,"configuration",null),n([r({readOnly:!0})],c.prototype,"availability",null),n([r()],c.prototype,"_getGroundElevation",null),c=n([S("esri.views.interactive.snapping.featureSources.FeatureServiceSnappingSource")],c);export{c as FeatureServiceSnappingSource};
