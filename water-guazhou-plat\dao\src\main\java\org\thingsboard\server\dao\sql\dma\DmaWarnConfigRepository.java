package org.thingsboard.server.dao.sql.dma;

import org.springframework.data.repository.CrudRepository;
import org.thingsboard.server.dao.model.sql.dma.DmaWarnConfigEntity;
import org.thingsboard.server.dao.util.SqlDao;

@SqlDao
public interface DmaWarnConfigRepository extends CrudRepository<DmaWarnConfigEntity, String> {

    DmaWarnConfigEntity findFirstByPartitionIdAndType(String partitionId, String type);
}
