package org.thingsboard.server.dao.operateLog;

import org.thingsboard.server.common.data.Tenant;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.model.sql.OperateLogEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/5/28 14:19
 */
public interface OperateLogService {

    List<OperateLogEntity> findByTenantId(TenantId tenantId);

    List<OperateLogEntity> findByPhone(String phone);

    List<OperateLogEntity> findByCaptcha(String captcha);

    OperateLogEntity findByCaptchaAndInvalid(String captcha,String invalid);

    OperateLogEntity saveLog(OperateLogEntity operateLogEntity);

    OperateLogEntity findByPhoneAndInvalid(String phone,String invalid);

    OperateLogEntity findByPhoneAndInvalidAndCaptcha(String phone,String invalid,String captcha);

    List<OperateLogEntity> findByTenantIdAndTime(TenantId tenantId,long start ,long end);
}
