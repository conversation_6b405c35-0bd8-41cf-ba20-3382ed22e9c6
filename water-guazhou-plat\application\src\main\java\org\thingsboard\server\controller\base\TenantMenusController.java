package org.thingsboard.server.controller.base;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardErrorCode;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.MenuPoolId;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.menu.Menu;
import org.thingsboard.server.common.data.menu.MenuMeta;
import org.thingsboard.server.common.data.menu.MenuPoolVO;
import org.thingsboard.server.common.data.security.Authority;
import org.thingsboard.server.dao.menu2.TenantMenusRoleService;
import org.thingsboard.server.dao.menu2.TenantMenusService;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.model.sql.TenantMenus;
import org.thingsboard.server.dao.model.sql.TenantMenusRole;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;
import org.thingsboard.server.service.security.model.SecurityUser;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("api/tenantMenus")
public class TenantMenusController extends BaseController {

    @Autowired
    private TenantMenusService tenantMenusService;

    @Autowired
    private TenantMenusRoleService tenantMenusRoleService;

    @DeleteMapping
    public JSONObject remove(@RequestBody List<String> ids) throws ThingsboardException {
        if (ids.size() != 1) {
            return null;
        }
        return tenantMenusService.remove(ids.get(0), getTenantId());
    }

    @GetMapping("{id}")
    public Menu getById(@PathVariable String id) {
        try {
            return tenantMenusService.findById(id);
        } catch (Exception e) {
            return null;
        }
    }

    @RequestMapping(value = "/getRootId", method = RequestMethod.GET)
    public JSONObject getRootId() {
        JSONObject object = new JSONObject();
        object.put("id", ModelConstants.MENU_POOL_ROOT_STR);
        return object;
    }

    @PostMapping
    public TenantMenus saveMenu(@RequestBody Map map) throws ThingsboardException {
        checkNotNull(map);
        String parentId = null;
        if (StringUtils.isNotBlank((String) map.get("parentId"))) {
            parentId = (String) map.get("parentId");
        }

        Menu menu = buildMenu((Map) map.get("data"));
        menu.setTenantId(UUIDConverter.fromTimeUUID(getTenantId().getId()));

        return tenantMenusService.saveMenu(menu, parentId, getTenantId());
    }

    /**
     * 构建menu数据
     *
     * @param data
     * @return
     */
    private Menu buildMenu(Map data) throws ThingsboardException {
        Menu menu = new Menu();
        if (StringUtils.isNotBlank((String) data.get("id"))) {
            menu.setId((String) data.get("id"));
        }
        if (StringUtils.isNotBlank((String) data.get("orderNum"))) {
            menu.setOrderNum(Integer.valueOf((String) data.get("orderNum")));
        }
        if (StringUtils.isNotBlank((String) data.get("path"))) {
            menu.setPath((String) data.get("path"));
        }
        if (StringUtils.isNotBlank((String) data.get("name"))) {
            menu.setName((String) data.get("name"));
        }
        if (StringUtils.isNotBlank((String) data.get("component"))) {
            menu.setComponent((String) data.get("component"));
        }
        if (StringUtils.isNotBlank((String) data.get("url"))) {
            menu.setUrl((String) data.get("url"));
        }
        if (data.get("meta") != null) {
            MenuMeta meta = new MenuMeta();
            if (StringUtils.isBlank((String) ((Map) data.get("meta")).get("title"))) {
                throw new ThingsboardException("title属性为必传属性",
                        ThingsboardErrorCode.BAD_REQUEST_PARAMS);
            }
            meta.setTitle((String) ((Map) data.get("meta")).get("title"));
            meta.setIcon((String) ((Map) data.get("meta")).get("icon"));
            meta.setHidden((Boolean) ((Map) data.get("meta")).get("hidden"));

            menu.setMeta(meta);
        }

        return menu;
    }

    @GetMapping("/getSelectableTree")
    public List<MenuPoolVO> getSelectableTree() throws ThingsboardException {
        return tenantMenusService.getSelectableTree(getTenantId());
    }

    @GetMapping("findMenuByTenantApplication")
    public List<Menu> findMenuByTenantApplication(@RequestParam String tenantApplicationId) throws ThingsboardException {
        SecurityUser user = getCurrentUser();
        try {
            if (user.getAuthority() == Authority.TENANT_ADMIN
                    || user.getAuthority() == Authority.TENANT_SYS
                    || user.getAuthority() == Authority.SYS_ADMIN) {
                return tenantMenusService.findByTenantApplication(null, tenantApplicationId, user.getTenantId());
            } else {
                return tenantMenusService.findByTenantApplication(user.getId(), tenantApplicationId, user.getTenantId());
            }
        } catch (Exception e) {
            e.printStackTrace();
            return new ArrayList<>();
        }

    }

    @GetMapping("findTreeByTenantApplication")
    public List<MenuPoolVO> findTreeByTenantApplication(@RequestParam String tenantApplicationId) throws ThingsboardException {
        SecurityUser user = getCurrentUser();
        try {
            if (user.getAuthority() == Authority.TENANT_ADMIN
                    || user.getAuthority() == Authority.TENANT_SYS
                    || user.getAuthority() == Authority.SYS_ADMIN) {
                return tenantMenusService.findTreeByTenantApplication(null, tenantApplicationId, user.getTenantId());
            } else {
                return tenantMenusService.findTreeByTenantApplication(user.getId(), tenantApplicationId, user.getTenantId());
            }
        } catch (Exception e) {
            e.printStackTrace();
            return new ArrayList<>();
        }

    }

    @GetMapping("roleMenus")
    public IstarResponse roleMenus(@RequestParam String roleId, @RequestParam String tenantApplicationId) throws ThingsboardException {
        return IstarResponse.ok(tenantMenusRoleService.findRoleMenus(roleId, tenantApplicationId, getTenantId()));
    }

    @PostMapping("saveRoleMenus")
    public IstarResponse saveRoleMenus(@RequestBody JSONObject params) throws ThingsboardException {
        List<String> menus = params.getJSONArray("menus").toJavaList(String.class);
        String tenantApplicationId = params.getString("tenantApplicationId");
        String roleId = params.getString("roleId");
        tenantMenusRoleService.saveRoleMenus(roleId, tenantApplicationId, menus, getTenantId());

        return IstarResponse.ok();
    }

    /**
     * 菜单导出
     */
    @GetMapping("export")
    public void export(HttpServletResponse response) throws ThingsboardException {
        TenantId tenantId = getTenantId();

        // 查询菜单树形结构
        List<TenantMenus> tree = tenantMenusService.findTree(tenantId);

        response.setCharacterEncoding("utf-8");
        //设置响应的内容类型
//        response.setContentType("text/plain");
        response.setContentType("application/octet-stream");
        //设置文件的名称和格式
        response.addHeader("Content-Disposition", "attachment;filename="
                + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date())
                + ".txt");
        BufferedOutputStream buff = null;
        ServletOutputStream outStr = null;
        try {
            outStr = response.getOutputStream();
            buff = new BufferedOutputStream(outStr);
            buff.write(JSON.toJSONString(tree).getBytes(StandardCharsets.UTF_8));
            buff.flush();
            buff.close();
        } catch (Exception ignored) {
        } finally {
            try {
                if (buff != null) {
                    buff.close();
                }
                if (outStr != null) {
                    outStr.close();
                }
            } catch (Exception ignored) {
            }
        }

    }

    /**
     * 菜单导出
     */
    @PostMapping("import")
    public void importMenu(@RequestBody MultipartFile file) throws Exception {
        TenantId tenantId = getTenantId();

        String data = new String(file.getBytes());

        // 序列化
        List<TenantMenus> menuList = JSON.parseArray(data).toJavaList(TenantMenus.class);

        // 保存导入的数据
        tenantMenusService.importMenu(menuList, tenantId);

    }

    @GetMapping("menuPoolToTenantMenus")
    public void menuPoolToTenantMenus() throws ThingsboardException {
        TenantId tenantId = getTenantId();
        tenantMenusService.menuPoolToTenantMenus(tenantId);
    }

}
