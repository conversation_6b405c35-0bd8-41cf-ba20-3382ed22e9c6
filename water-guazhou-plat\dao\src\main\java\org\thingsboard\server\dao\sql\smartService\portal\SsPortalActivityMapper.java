package org.thingsboard.server.dao.sql.smartService.portal;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.thingsboard.server.dao.model.sql.smartService.portal.SsPortalActivity;
import org.thingsboard.server.dao.util.imodel.query.smartService.portal.SsPortalActiveRequest;
import org.thingsboard.server.dao.util.imodel.query.smartService.portal.SsPortalActivityPageRequest;

@Mapper
public interface SsPortalActivityMapper extends BaseMapper<SsPortalActivity> {
    IPage<SsPortalActivity> findByPage(SsPortalActivityPageRequest request);

    @SuppressWarnings("methodNotInXmlInspection")
    boolean update(SsPortalActivity entity);

    boolean updateFully(SsPortalActivity entity);

    boolean active(SsPortalActiveRequest req);

}
