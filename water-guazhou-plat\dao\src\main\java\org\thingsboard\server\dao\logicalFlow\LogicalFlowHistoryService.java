package org.thingsboard.server.dao.logicalFlow;

import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.model.sql.LogicalFlowAlarmNodeHistory;
import org.thingsboard.server.dao.model.sql.LogicalFlowHistory;
import org.thingsboard.server.dao.model.sql.LogicalFlowNodeHistory;

import java.util.List;
import java.util.Map;

public interface LogicalFlowHistoryService {
    LogicalFlowHistory save(LogicalFlowHistory history);

    LogicalFlowNodeHistory save(LogicalFlowNodeHistory nodeHistory);

    Map<String, Object> findByLogicalFlowIdList(List<String> LogicalFlowIdList, Integer page, Integer size, Long startTime, Long endTime, String logicalFlowName);

    List<LogicalFlowNodeHistory> findLogicalFlowHistoryDetail(String historyId);

    Map<String, Object> findAlarmNodeHistory(Integer page, Integer size, Long startTime, Long endTime, String projectId);

    LogicalFlowAlarmNodeHistory save(LogicalFlowAlarmNodeHistory logicalFlowAlarmNodeHistory);

    List<LogicalFlowAlarmNodeHistory> findByLogicalFlowNodeId(String nodeId, int page, int size);

    List<LogicalFlowAlarmNodeHistory> findByLogicalFlowNodeIdAndSendFlag(String id, String sendFlag, int page, int size);
}
