package org.thingsboard.server.dao.sql.dma;

import org.springframework.data.repository.CrudRepository;
import org.thingsboard.server.dao.model.sql.dma.DmaDeviceRelationEntity;
import org.thingsboard.server.dao.util.SqlDao;

import java.util.List;

@SqlDao
public interface DmaDeviceRelationRepository extends CrudRepository<DmaDeviceRelationEntity, String> {

    List<DmaDeviceRelationEntity> findAllByPartitionId(String partitionId);

    void deleteAllByPartitionId(String partitionId);

    int countAllByPartitionId(String partitionId);
}
