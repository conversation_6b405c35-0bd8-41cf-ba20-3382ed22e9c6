import{_ as B}from"./index-C9hz-UZb.js";import{d as O,c as i,r as _,o as E,ay as V,g as h,h as m,F as k,p as c,q as D,i as s,j as A,aq as P,C as W}from"./index-r0dFAfgr.js";import{_ as I}from"./Search-NSrhrIa_.js";import"./index-0NlGN6gS.js";import{u as j}from"./useDetector-BRcb7GRN.js";import{r as z}from"./echarts-DP4wVWSW.js";import{a as G}from"./statistics-CeyexT_5.js";const H={class:""},M={class:"charts-box"},N={ref:"agriEcoDev",class:"chart-box"},X=O({__name:"DYHZBFX",setup(Y){const d=i(),n=_({pieChartOption:null,barOption:null,showTable:!1}),L=_({defaultParams:{type:"month",grade:"1",tc:"c"},filters:[{label:"",field:"grade",type:"select",options:[{label:"一级分区",value:"1"},{label:"二级分区",value:"2"}],onChange:()=>{u()}},{label:"",field:"type",type:"radio-button",options:[{label:"月",value:"month"},{label:"季度",value:"quarter"},{label:"年",value:"year"}],onChange:()=>{u()}},{label:"",field:"tc",type:"radio-button",options:[{label:"",iconifyIcon:"ep:pie-chart",value:"c"},{label:"",iconifyIcon:"ep:grid",value:"t"}],onChange:t=>{n.showTable=t==="t"}}]}),p=_({indexVisible:!0,dataList:[],columns:[{label:"区域名称",prop:"name"}],pagination:{hide:!0}}),S=t=>{const o=[],r=t.map(e=>(o.push(e.name),e.total));return{backgroundColor:A().isDark?"transparent":"#F4F7FA",tooltip:{trigger:"axis"},grid:{left:60,right:40,top:40,bottom:30,containLabel:!0},xAxis:{axisLabel:{color:"#409EFF"},boundaryGap:!1,type:"category",data:o,axisTick:{show:!1}},yAxis:{type:"value",axisLabel:{color:"#409EFF"},name:"大用户用水量(m³)",axisTick:{show:!1},axisLine:{show:!1},splitLine:{lineStyle:{color:"#409EFF",type:"dashed"}}},series:[{type:"bar",barWidth:15,data:r,itemStyle:{color:"#409EFF"}}]}},u=()=>{var o;const t=((o=d.value)==null?void 0:o.queryParams)||{};G({...t}).then(r=>{var l,v;const e=r.data.data||[],f=e==null?void 0:e.map(a=>({name:a.name,value:a.total,scale:a.rate}));n.pieChartOption=z(f,"m³"),n.barOption=S(e||[]),p.columns=[{fixed:"left",minWidth:160,label:"区域名称",prop:"name"},...((v=(l=e[0])==null?void 0:l.x)==null?void 0:v.map(a=>({minWidth:120,label:a,prop:a})))||[]],p.dataList=e.map(a=>{var C;const x={name:a.name};return(C=a.x)==null||C.map((w,q)=>{var F;x[w]=(F=a.y)==null?void 0:F[q]}),x})})},T=j(),b=i(),y=i(),g=i();return E(()=>{u(),T.listenToMush(g.value,()=>{var t,o;(t=b.value)==null||t.resize(),(o=y.value)==null||o.resize()})}),(t,o)=>{const r=I,e=V("VChart"),f=P,l=B;return h(),m(l,{title:"大用户占比"},{query:k(()=>[c("div",H,[D(r,{ref_key:"refSearch",ref:d,config:s(L)},null,8,["config"])])]),default:k(()=>[c("div",M,[c("div",N,[D(e,{ref_key:"refChart1",ref:b,option:s(n).pieChartOption},null,8,["option"])],512),c("div",{ref_key:"refDiv",ref:g,class:"chart-box"},[s(n).showTable?(h(),m(f,{key:0,config:s(p)},null,8,["config"])):(h(),m(e,{key:1,ref_key:"refChart2",ref:y,option:s(n).barOption},null,8,["option"]))],512)])]),_:1})}}}),ee=W(X,[["__scopeId","data-v-41a13f6f"]]);export{ee as default};
