package org.thingsboard.server.dao.util.imodel.response;

import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.Getter;
import lombok.Setter;
import org.joda.time.DateTime;
import org.thingsboard.server.dao.util.imodel.response.model.ReturnHelper;

import java.util.*;

@Getter
@Setter
/*
  用于导出Excel文件
 */
public final class ExcelFileInfo implements Responsible {
    private String title;

    private Map<String, String> headMap = new LinkedHashMap<>();

    private List<?> items;
    private boolean withoutTitle;

    private ExcelFileInfo(String title, List<?> items) {
        this.title = title;
        this.items = items;
    }

    public ExcelFileInfo withDateTime() {
        String pattern = DateTime.now().toString("yyyy-MM-dd HH:mm:ss");
        this.title = this.title == null ? pattern : title + pattern;

        return this;
    }

    public ExcelFileInfo withDate() {
        String pattern = DateTime.now().toString("yyyy-MM-dd");
        this.title = this.title == null ? pattern : title + pattern;

        return this;
    }

    public static ExcelFileInfo of(String title) {
        return of(title, Collections.singletonList(Collections.emptyMap()));
    }

    public static ExcelFileInfo of(String title, List<?> items) {
        return new ExcelFileInfo(title, items == null ? Collections.singletonList(Collections.emptyMap()) : items);
    }

    public static ExcelFileInfo of(String title, IPage<?> itemsPage) {
        return new ExcelFileInfo(title, itemsPage.getRecords());
    }

    @Override
    public Object postProcess(ReturnHelper returnHelper, Object arg) {
        items = (List<?>) returnHelper.process(items, arg);
        return this;
    }

    /**
     * @param key  字段名
     * @param name 标题名
     */
    public ExcelFileInfo nextTitle(String key, String name) {
        headMap.put(key, name);
        return this;
    }

    /**
     * @param name 标题名
     */
    public ExcelFileInfo nextTitle(String name) {
        return nextTitle(UUID.randomUUID().toString(), name);
    }

    public ExcelFileInfo withoutTitle() {
        this.withoutTitle = true;
        return this;
    }
}
