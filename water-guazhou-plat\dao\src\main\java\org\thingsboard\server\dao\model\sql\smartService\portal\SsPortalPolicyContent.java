package org.thingsboard.server.dao.model.sql.smartService.portal;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.util.Date;

@Getter
@Setter
@ResponseEntity
@TableName("ss_portal_policy_content")
public class SsPortalPolicyContent {
    // id
    private String id;

    // 名称
    private String title;

    // 法规类型id
    private String typeId;

    // 法规类型名称
    @TableField(exist = false)
    private String typeName;

    // 内容
    private String content;

    // 附件
    private String attachment;

    // 创建时间
    private Date createTime;

    // 客户id
    private String tenantId;

}
