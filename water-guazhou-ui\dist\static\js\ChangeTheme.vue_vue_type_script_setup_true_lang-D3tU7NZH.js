import{d as i,c as r,j as s,g as c,n as p,p as u,q as m,i as _,t as d,E as v}from"./index-r0dFAfgr.js";const f={class:"theme-bar"},V=i({__name:"ChangeTheme",setup(g){const a=r(s().isDark),n=t=>{s().SET_isDark(t)};return(t,e)=>{const o=v;return c(),p("div",f,[e[1]||(e[1]=u("span",null,"夜间模式：",-1)),m(o,{modelValue:_(a),"onUpdate:modelValue":e[0]||(e[0]=l=>d(a)?a.value=l:null),"inline-prompt":"",size:"small","active-text":"是","inactive-text":"否","active-value":!0,"inactive-value":!1,onChange:n},null,8,["modelValue"])])}}});export{V as _};
