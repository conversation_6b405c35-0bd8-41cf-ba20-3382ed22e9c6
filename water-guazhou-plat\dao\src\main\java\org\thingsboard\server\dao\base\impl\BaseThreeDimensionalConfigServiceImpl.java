package org.thingsboard.server.dao.base.impl;

import java.util.List;
import java.util.UUID;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.base.IBaseThreeDimensionalConfigService;
import org.thingsboard.server.dao.model.sql.base.BaseThreeDimensionalConfig;
import org.thingsboard.server.dao.sql.base.BaseThreeDimensionalConfigMapper;
import org.thingsboard.server.dao.util.imodel.query.base.BaseThreeDimensionalConfigPageRequest;

/**
 * 公共管理平台-三维配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-06
 */
@Service
public class BaseThreeDimensionalConfigServiceImpl implements IBaseThreeDimensionalConfigService {
    @Autowired
    private BaseThreeDimensionalConfigMapper baseThreeDimensionalConfigMapper;

    /**
     * 查询公共管理平台-三维配置
     *
     * @param id 公共管理平台-三维配置主键
     * @return 公共管理平台-三维配置
     */
    @Override
    public BaseThreeDimensionalConfig selectBaseThreeDimensionalConfigById(String id) {
        return baseThreeDimensionalConfigMapper.selectBaseThreeDimensionalConfigById(id);
    }

    /**
     * 查询公共管理平台-三维配置列表
     *
     * @param baseThreeDimensionalConfig 公共管理平台-三维配置
     * @return 公共管理平台-三维配置
     */
    @Override
    public IPage<BaseThreeDimensionalConfig> selectBaseThreeDimensionalConfigList(BaseThreeDimensionalConfigPageRequest baseThreeDimensionalConfig) {
        return baseThreeDimensionalConfigMapper.selectBaseThreeDimensionalConfigList(baseThreeDimensionalConfig);
    }

    /**
     * 新增公共管理平台-三维配置
     *
     * @param baseThreeDimensionalConfig 公共管理平台-三维配置
     * @return 结果
     */
    @Override
    public int insertBaseThreeDimensionalConfig(BaseThreeDimensionalConfig baseThreeDimensionalConfig) {
        baseThreeDimensionalConfig.setId(UUID.randomUUID().toString().replace("-", ""));
        return baseThreeDimensionalConfigMapper.insertBaseThreeDimensionalConfig(baseThreeDimensionalConfig);
    }

    /**
     * 修改公共管理平台-三维配置
     *
     * @param baseThreeDimensionalConfig 公共管理平台-三维配置
     * @return 结果
     */
    @Override
    public int updateBaseThreeDimensionalConfig(BaseThreeDimensionalConfig baseThreeDimensionalConfig) {
        return baseThreeDimensionalConfigMapper.updateBaseThreeDimensionalConfig(baseThreeDimensionalConfig);
    }

    /**
     * 批量删除公共管理平台-三维配置
     *
     * @param ids 需要删除的公共管理平台-三维配置主键
     * @return 结果
     */
    @Override
    public int deleteBaseThreeDimensionalConfigByIds(List<String> ids) {
        return baseThreeDimensionalConfigMapper.deleteBaseThreeDimensionalConfigByIds(ids);
    }

    /**
     * 删除公共管理平台-三维配置信息
     *
     * @param id 公共管理平台-三维配置主键
     * @return 结果
     */
    @Override
    public int deleteBaseThreeDimensionalConfigById(String id) {
        return baseThreeDimensionalConfigMapper.deleteBaseThreeDimensionalConfigById(id);
    }
}
