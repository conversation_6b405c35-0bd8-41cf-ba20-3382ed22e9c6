package org.thingsboard.server.dao.model.sql.smartManagement.plan;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrderLevel;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrderStage;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrderStatus;
import org.thingsboard.server.dao.model.sql.workOrder.extentions.WorkOrderUploadUserSupport;
import org.thingsboard.server.dao.util.imodel.response.ResponseMap;
import org.thingsboard.server.dao.util.imodel.response.annotations.Info;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseUsername;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;
import org.thingsboard.server.dao.util.imodel.response.model.JdbcHelper;

import java.util.Date;

import static org.thingsboard.server.dao.model.sql.workOrder.WorkOrderStatus.valueOf;

@Getter
@Setter
@ResponseEntity
public class SMCircuitTaskWorkOrderPitfallResponse implements WorkOrderUploadUserSupport {

    // 任务编号
    private String code;

    // 工单编号
    private String workOrderCode;

    // 工单id
    private String workOrderId;

    // 类型
    private String type;

    // 内容
    private String content;

    // 上报人员
    @ParseUsername
    private String creator;

    // 上报时间
    private Date createTime;

    // 紧急程度
    private String level;

    // 状态
    @Info(name = "statusStage")
    private String status;

    // 关键点id
    private String pointId;

    // 关键点名称
    private String pointName;

    // 有无隐患
    private Boolean isPitfall;

    // 处理级别
    private WorkOrderLevel processLevel;

    // 来源
    private String source;

    // 上报人
    private String uploadUserId;

    // 上报人电话
    private String uploadPhone;

    // 上报人户号
    private String uploadNo;

    // 上报人地址
    private String uploadAddress;

    // 抄送人，多个用逗号隔开
    private String ccUserId;

    // 抄送人，多个用逗号隔开
    private String ccUserName;

    // 现场视频，多个用逗号分隔（最多2）
    private String videoUrl;

    // 现场音频，多个用逗号分隔（最多2）
    private String audioUrl;

    // 现场图片，多个用逗号分隔
    private String imgUrl;

    // 其他附件，多个用逗号分隔（最多2）
    private String otherFileUrl;

    // 地理位置，目前规则为经纬度使用逗号隔开
    private String coordinate;

    // 地理位置名称
    private String coordinateName;

    // 状态阶段 待处理/处理中/已处理
    @SuppressWarnings("unused")
    private String statusStage() {
        WorkOrderStatus status = valueOf(this.status);
        if (status.in(WorkOrderStage.WAITED)) {
            return "待处理";
        } else if (status.in(WorkOrderStage.ACTIVE)) {
            return "处理中";
        } else if (status.in(WorkOrderStage.COMPLETED)) {
            return "已处理";
        }

        return "工单状态异常";
    }

    @Override
    public void customizeMap(ResponseMap map, JdbcHelper jdbc) {
        WorkOrderUploadUserSupport.super.customizeMap(map, jdbc);
    }

}
