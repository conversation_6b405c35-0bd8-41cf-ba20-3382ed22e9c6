export enum ECollectStatus {
  '未派发' = '0',
  '待接收' = '1',
  '处理中' = '2',
  '待审核' = '3',
  '完成' = '4',
  '审核退回' = '5'
  // '已入库' = '6'
}
export const CollectStatusObj: Record<string, string> = {
  0: '未派发',
  1: '待接收',
  2: '处理中',
  3: '待审核',
  4: '完成',
  5: '审核退回'
  // 6: '已入库'
  // 11表示未完成的所有状态合
}
/**
 * 采集审核结果
 */
export enum ECollectAuditStatus {
  PASS = '4',
  REJECT = '5'
}
/**
 * 采集数据的几何类型
 */
export enum ECollectPipeType {
	'线' = '0',
	'点' = '1'
}
/**
 * 采集任务的类型
 */
export enum ECollectTaskType {
	'线' = '线',
	'点' = '点'
}
/**
 * 采集任务的状态
 */
export const CollectStatusOptions: NormalOption[] = Object.entries(ECollectStatus).map(item => {
  return {
    label: item[0],
    value: item[1]
  }
})
export const FormatCollectStatus = (status: string) => {
  return Object.entries(ECollectStatus).find(item => item[1] === status)?.[0]
}
