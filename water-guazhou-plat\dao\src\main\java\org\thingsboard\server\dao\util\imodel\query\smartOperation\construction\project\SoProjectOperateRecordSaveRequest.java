package org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoProjectOperateRecord;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;

@Getter
@Setter
public class SoProjectOperateRecordSaveRequest extends SaveRequest<SoProjectOperateRecord> {
    // 工程编号
    private String code;

    // 详情
    private String detail;

    // 备注
    private String remark;

    @Override
    protected SoProjectOperateRecord build() {
        SoProjectOperateRecord entity = new SoProjectOperateRecord();
        entity.setCreator(currentUserUUID());
        entity.setCreateTime(createTime());
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    @Override
    protected SoProjectOperateRecord update(String id) {
        disallowUpdate();
        SoProjectOperateRecord entity = new SoProjectOperateRecord();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(SoProjectOperateRecord entity) {
        entity.setCode(code);
        entity.setDetail(detail);
        entity.setRemark(remark);
    }

    public static SoProjectOperateRecordSaveRequest of(SaveRequest<?> other, String code,
                                                       String detailPattern, String remark) {
        SoProjectOperateRecordSaveRequest soProjectOperateRecordSaveRequest = new SoProjectOperateRecordSaveRequest();
        other.assimilation(soProjectOperateRecordSaveRequest);
        soProjectOperateRecordSaveRequest.setCode(code);
        soProjectOperateRecordSaveRequest.setDetail(detailPattern);
        soProjectOperateRecordSaveRequest.setRemark(remark);
        return soProjectOperateRecordSaveRequest;
    }

    public static SoProjectOperateRecordSaveRequest of(String tenantId, String currentUserId, String code,
                                                       String detailPattern, String remark) {
        SoProjectOperateRecordSaveRequest soProjectOperateRecordSaveRequest = new SoProjectOperateRecordSaveRequest();
        soProjectOperateRecordSaveRequest.tenantId(tenantId);
        soProjectOperateRecordSaveRequest.currentUserId(currentUserId);
        soProjectOperateRecordSaveRequest.setCode(code);
        soProjectOperateRecordSaveRequest.setDetail(detailPattern);
        soProjectOperateRecordSaveRequest.setRemark(remark);
        return soProjectOperateRecordSaveRequest;
    }
}