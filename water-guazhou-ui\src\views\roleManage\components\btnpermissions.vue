<template>
  <el-dialog
    v-model="visible"
    title="按钮权限赋予"
    width="30%"
    :close-on-click-modal="false"
  >
    <el-tree
      ref="refTree"
      :data="treeData"
      show-checkbox
      node-key="id"
      :default-expand-all="true"
    ></el-tree>
    <slot name="footer">
      <span class="dialog-footer">
        <el-button
          type="primary"
          @click="clickSaveMenuToRole"
        >保存</el-button>
        <el-button @click="clearMenuTree">清空</el-button>
        <el-button @click="closeDialog">取 消</el-button>
      </span>
    </slot>
  </el-dialog>
</template>

<script lang="ts" setup>
// pi/menuButton/getRoleButtonList
import { onMounted, ref, watch } from 'vue'
import { saveRole } from '@/api/menu'
import { getMenuBtnTree, setRoleMenuBtn, getRoleButtonList } from '@/api/permission'
import { IElTree } from '@/common/types/element-plus'
import { SLMessage } from '@/utils/Message'
import { removeSlash } from '@/utils/removeIdSlash'

const props = withDefaults(defineProps<{
  roleId: string,
  curRole: any
}>(), {
  roleId: '',
  curRole: {}
})
defineEmits(['handleclose'])
const visible = ref<boolean>(false)
const treeData = ref<NormalOption[]>([])
const selectedNodes = ref<NormalOption[]>([])
const initTree = async () => {
  const res = await getMenuBtnTree()
  let data = res.data.map(item => {
    item.children = item.children.filter(obj => {
      return obj.children && obj.children.length > 0
    })
    return item
  })
  data = data.filter(item => {
    return item.children && item.children.length > 0
  })
  treeData.value = data
}
const openDialog = () => (visible.value = true)
const closeDialog = () => (visible.value = false)
const refTree = ref<IElTree>()
const getCurRoleBtns = async () => {
  const res = props.roleId ? await getRoleButtonList(removeSlash(props.roleId)) : undefined
  selectedNodes.value = res?.data?.map(item => item.id) || []
  refTree.value?.setCheckedKeys(selectedNodes.value as any, false)
}
const clickSaveMenuToRole = async () => {
  const menuRes = props?.roleId ? await setRoleMenuBtn(removeSlash(props?.roleId), refTree.value?.getCheckedKeys(true)) : undefined

  const aInfo = { ...JSON.parse(props?.curRole?.additionalInfo || '{}') }
  const params = {
    ...(props?.curRole || {}),
    additionalInfo: JSON.stringify(aInfo)
  }

  const roleRes = await saveRole(params)
  if (menuRes?.status === 200 && roleRes.status === 200) {
    SLMessage.success('操作成功')
    visible.value = false
  }
}
// 清空选择的节点
const clearMenuTree = () => {
  refTree.value?.setCheckedKeys([], false)
}
watch(() => visible.value, () => {
  initData()
})
const initData = async () => {
  await initTree()
  await getCurRoleBtns()
}

onMounted(() => {
  initData()
})
defineExpose({
  openDialog,
  closeDialog
})
</script>

<style lang="scss">
.alarm-design {
  .el-tree-node__content {
    &:hover {
      background: #373b4e 100% !important;
    }
  }

  .el-tree-node.is-current {
    >.el-tree-node__content {
      background: #373b4e 100% !important;
    }

    .el-tree-node__content {
      &:hover {
        background: #373b4e 100% !important;
      }
    }

    .el-tree-node__children {
      .el-tree-node__content {
        &:hover {
          background: #373b4e 100% !important;
        }
      }
    }
  }
}

.btnPermForm {
  .el-form-item__label {
    height: 40px !important;
    line-height: 40px !important;
  }

  // display: flex;
  // flex-wrap: wrap;
  // justify-content: space-between;
}
</style>
