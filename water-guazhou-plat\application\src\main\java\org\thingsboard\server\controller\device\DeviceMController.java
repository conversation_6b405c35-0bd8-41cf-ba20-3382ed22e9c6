package org.thingsboard.server.controller.device;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.sql.deviceManage.Device;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;
import org.thingsboard.server.dao.deviceType.DeviceMService;

import java.util.List;

@RestController
@RequestMapping("/api/device/m")
public class DeviceMController extends BaseController {
    @Autowired
    private DeviceMService deviceMService;


    @GetMapping
    public IstarResponse getList(@RequestParam(required = false, defaultValue = "") String typeId,
                                 @RequestParam(required = false, defaultValue = "") String serialId,
                                 @RequestParam(required = false, defaultValue = "") String name,
                                 @RequestParam(required = false, defaultValue = "") String model,
                                 int page, int size) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return IstarResponse.ok(deviceMService.getList(typeId, serialId, name, model, page, size, tenantId));
    }


    @PostMapping
    public IstarResponse save(@RequestBody Device device) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        if (!this.checkSerialId(device, tenantId)) {
            return IstarResponse.error("设备编码重复，请重新输入");
        }
        device.setCreator(UUIDConverter.fromTimeUUID(getCurrentUser().getUuidId()));
        device.setTenantId(tenantId);
        return IstarResponse.ok(deviceMService.save(device));
    }

    private boolean checkSerialId(Device device, String tenantId) {
        return deviceMService.checkSerialId(device, tenantId);
    }

    @DeleteMapping
    public IstarResponse delete(@RequestBody List<String> ids) {
        return deviceMService.delete(ids);
    }

}
