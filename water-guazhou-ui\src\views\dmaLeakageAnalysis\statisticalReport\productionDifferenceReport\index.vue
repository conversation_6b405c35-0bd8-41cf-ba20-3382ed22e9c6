<!-- 产销差报表 -->
<template>
  <div class="wrapper">
    <SLCard
      class="card"
      title=" "
    >
      <template #title>
        <Tabs
          v-model="state.activeName"
          :config="tabsConfig"
        ></Tabs>
      </template>
      <div class="tab">
        <differenceReport
          v-if="state.activeName === '产销差报表'"
          :partitions="partition.List.value"
        ></differenceReport>
        <reportDetail
          v-if="state.activeName === '产销差报表详情'"
          :partitions="partition.List.value"
          :tree="partition.Tree.value"
        ></reportDetail>
        <chartView
          v-if="state.activeName === '图表视图'"
          :partitions="partition.Tree.value"
        ></chartView>
      </div>
    </SLCard>
  </div>
</template>

<script lang="ts" setup>
import differenceReport from './components/differenceReport.vue'
import reportDetail from './components/reportDetail.vue'
import chartView from './components/chartView.vue'
import { usePartition } from '@/hooks/arcgis'

const state = reactive<{
  activeName: string
}>({
  activeName: '产销差报表'
})

// tabs
const tabsConfig = reactive<ITabs>({
  type: 'tabs',
  tabType: 'inset',
  width: '100%',
  tabs: [
    { label: '产销差报表', value: '产销差报表' },
    { label: '产销差报表详情', value: '产销差报表详情' },
    { label: '图表视图', value: '图表视图' }
  ],
  handleTabClick: (tab: any) => {
    //
    state.activeName = tab.props.name
  }
})

const refreshData = async () => {
  //
}
const partition = usePartition()
onMounted(async () => {
  const p1 = partition.getTree()
  const p2 = partition.getList()
  await Promise.all([p1, p2])
  refreshData()
  // TreeData.currentProject = treeData[0]?.children[0]
})
</script>
<style lang="scss" scoped>
.card {
  height: 100%;

  .tab {
    height: 100%;
    width: 100%;
  }
}
</style>
