@mixin swipe() {
  $divider-color: $background-color !default;
  $divider-size: 4px !default;

  .esri-swipe {
    width: 100%;
    height: 100%;
    position: absolute;
    overflow: hidden;
    margin: 0;
    padding: 0;
    background: transparent;
    user-select: none;
  }

  .esri-swipe,
  .esri-ui .esri-swipe {
    pointer-events: none;
  }

  .esri-swipe__container {
    position: absolute;
    margin: 0;
    padding: 0;
    border: 0;
    z-index: 1;
    touch-action: none; // needed for touch actions to work correctly
    pointer-events: auto;
    overflow: hidden; // needed to hide tracers in iOS
  }

  .esri-swipe--horizontal .esri-swipe__container {
    margin-left: -$button-width--half;
    height: 100%;
    cursor: col-resize;
  }

  .esri-swipe--vertical .esri-swipe__container {
    margin-top: -$button-height--half;
    width: 100%;
    cursor: row-resize;
  }

  .esri-swipe--disabled .esri-swipe__container {
    pointer-events: none;
    cursor: default;
  }

  .esri-swipe__divider {
    position: absolute;
    background-color: $divider-color;
  }

  .esri-swipe--horizontal .esri-swipe__divider {
    border-left: 1px solid rgba($interactive-font-color, 0.5);
    border-right: 1px solid rgba($interactive-font-color, 0.5);
    width: $divider-size;
    height: 100%;
    margin-left: -#{$divider-size * 0.5};
    top: 0;
    left: $button-width--half;
  }

  .esri-swipe--vertical .esri-swipe__divider {
    border-top: 1px solid rgba($interactive-font-color, 0.5);
    border-bottom: 1px solid rgba($interactive-font-color, 0.5);
    width: 100%;
    height: $divider-size;
    margin-top: -#{$divider-size * 0.5};
    left: 0;
    top: $button-height--half;
  }

  .esri-swipe__handle {
    width: $button-width;
    height: $button-height;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    border: 1px solid rgba($interactive-font-color, 0.5);
    border-radius: $border-radius;
    background-color: $divider-color;
    &--hidden {
      visibility: hidden;
    }
  }

  .esri-swipe__handle-icon {
    position: relative;
    z-index: 2;
  }

  .esri-swipe--horizontal .esri-swipe__handle {
    top: calc(50% - #{$button-height--half});
  }

  .esri-swipe--vertical .esri-swipe__handle {
    left: calc(50% - #{$button-height--half});
  }
}

@if $include_Swipe == true {
  @include swipe();
}
