package org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralType;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemScope;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

@Getter
@Setter
public class SoGeneralTypeSaveRequest extends SaveRequest<SoGeneralType> {
    // 类型名称
    @NotNullOrEmpty
    private String name;

    // 排序编号
    private Integer orderNum;

    // 类型作用域
    private SoGeneralSystemScope scope;

    @Override
    protected SoGeneralType build() {
        SoGeneralType entity = new SoGeneralType();
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    @Override
    protected SoGeneralType update(String id) {
        SoGeneralType entity = new SoGeneralType();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(SoGeneralType entity) {
        entity.setName(name);
        entity.setScope(scope);
        entity.setOrderNum(orderNum);
    }
}