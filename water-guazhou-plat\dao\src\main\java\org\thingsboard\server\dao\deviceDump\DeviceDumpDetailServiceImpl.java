package org.thingsboard.server.dao.deviceDump;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.deviceDump.DeviceDumpDetail;
import org.thingsboard.server.dao.sql.deviceDump.DeviceDumpDetailMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.deviceDump.DeviceDumpDetailPageRequest;
import org.thingsboard.server.dao.util.imodel.query.deviceDump.DeviceDumpDetailSaveRequest;

import java.util.List;

@Service
public class DeviceDumpDetailServiceImpl implements DeviceDumpDetailService {
    @Autowired
    private DeviceDumpDetailMapper mapper;

    @Override
    public IPage<DeviceDumpDetail> findAllConditional(DeviceDumpDetailPageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    public List<DeviceDumpDetail> saveAll(List<DeviceDumpDetailSaveRequest> entity) {
        return QueryUtil.saveOrUpdateBatchByRequest(entity, mapper::saveAll, mapper::saveAll);
    }

    @Override
    public boolean update(DeviceDumpDetail entity) {
        return mapper.update(entity);
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }

    @Override
    public boolean removeAll(List<String> idList) {
        return QueryUtil.deleteBatch(idList, mapper::deleteBatchIds);
    }

    @Override
    public boolean removeAllByMainId(String id) {
        return mapper.removeAllByMainId(id) > 0;
    }

}
