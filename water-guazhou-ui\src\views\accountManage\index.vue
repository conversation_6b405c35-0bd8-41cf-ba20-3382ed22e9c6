<template>
  <div class="account-manage-warpper">
    <SLCard title="个人中心">
      <el-tabs
        v-model="state.activeName"
        class="demo-tabs"
      >
        <el-tab-pane
          label="账户信息"
          name="account"
          class="overlay-y"
        >
          <AccountInfo v-if="state.activeName === 'account'"></AccountInfo>
        </el-tab-pane>
        <el-tab-pane
          label="个性化配置"
          name="theme"
          class="overlay-y"
        >
          <ThemeInfo v-if="state.activeName === 'theme'"></ThemeInfo>
        </el-tab-pane>
        <el-tab-pane
          label="版本信息"
          name="version"
          class="overlay-y"
        >
          版本信息:{{ state.version }}
        </el-tab-pane>
      </el-tabs>
    </SLCard>
  </div>
</template>

<script lang="ts" setup>
import AccountInfo from './components/AccountInfo.vue'
import ThemeInfo from './components/ThemeInfo.vue'

const state = reactive<{
  version: string | any
  activeName: string
}>({
  version: window.SITE_CONFIG.version,
  activeName: 'account'
})
</script>

<style lang="scss" scoped>
.account-manage-warpper {
  width: 100%;
  height: 100%;
  padding: 15px;
  :deep(.sl-card) {
    height: 100%;
    .sl-card-title {
      border-bottom: 1px solid var(--el-border-color);
      .title {
        margin: 0;
        height: 50px;
        color: #00c6ff;
        line-height: 50px;
      }
    }
    // width: 660px;
  }
  :deep(.el-tabs) {
    height: 100%;
    .el-tabs__header {
      margin-bottom: 0;
    }
    .el-tabs__content {
      height: calc(100% - var(--el-tabs-header-height));
      padding: 15px;
    }
    .el-tab-pane {
      height: 100%;
    }
  }
  .el-form {
    max-width: 600px;
  }
}
.el-form-item {
  h2,
  h5 {
    margin: 0;
    color: #4b4c50;
  }
  h2 {
    line-height: 35px;
  }
  h5 {
    font-size: 16px;
    line-height: 18px;
    font-weight: 300;
  }
}
</style>
