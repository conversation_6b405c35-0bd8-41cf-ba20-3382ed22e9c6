import { request } from '@/plugins/axios';

/**
 * 新增/修改门禁信息
 * @param params
 * @returns
 */
export const PostAccessControl = (params: any) =>
  request({
    url: '/api/accessControl',
    method: 'post',
    data: params
  });
/**
 * 查询门禁分页列表
 * @param params
 * @returns data:{list:[],total:0}
 */
export const GetAccessControlPage = (params: {
  page: number;
  size: number;
  // 项目ID。不传则查询全部项目
  projectId?: string;
}) =>
  request({
    url: '/api/accessControl/list',
    method: 'get',
    params
  });

/**
 * 删除指定的门禁数据
 * @param ids
 * @returns
 */
export const DeleteAccessControl = (ids: string[]) =>
  request({
    url: '/api/accessControl/remove',
    method: 'delete',
    data: ids
  });

/**
 * 批量保存门禁视频关联
 * @param params
 * @returns
 */
export const BatchSaveAccessControlVideo = (params: {
  accessControlId: string;
  videoIds: string[];
}) =>
  request({
    url: '/api/accessControlVideo/batch',
    method: 'post',
    data: params
  });

/**
 * 根据门禁ID查询关联的视频列表
 * @param accessControlId
 * @returns
 */
export const GetAccessControlVideos = (accessControlId: string) =>
  request({
    url: `/api/accessControlVideo/videos/${accessControlId}`,
    method: 'get'
  });

/**
 * 根据视频ID查询关联的门禁列表
 * @param videoId
 * @returns
 */
export const GetVideoAccessControls = (videoId: string) =>
  request({
    url: `/api/accessControlVideo/accessControls/${videoId}`,
    method: 'get'
  });
