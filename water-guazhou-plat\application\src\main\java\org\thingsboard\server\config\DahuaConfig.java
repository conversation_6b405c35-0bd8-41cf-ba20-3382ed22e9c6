package org.thingsboard.server.config;

import com.dahuatech.icc.exception.ClientException;
import com.dahuatech.icc.oauth.http.DefaultClient;
import com.dahuatech.icc.oauth.http.IClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class DahuaConfig {
    @Value("${dahua.host}")
    private String host;

    @Value("${dahua.username}")
    private String username;

    @Value("${dahua.password}")
    private String password;

    @Value("${dahua.clientId}")
    private String clientId;

    @Value("${dahua.clientSecret}")
    private String clientSecret;
    @Bean
    public IClient iClient() throws ClientException {
        return new DefaultClient(host, username, password, clientId, clientSecret);
    }
}
