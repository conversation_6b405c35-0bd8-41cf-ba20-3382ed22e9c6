import{d as u,g as e,n as t,p as a,bh as n,an as c,h as d,aw as r,i as f,cs as m,C as _}from"./index-r0dFAfgr.js";const g={class:"item"},h={key:0,class:"row"},w={class:"title"},y={key:1,class:"row"},v={class:"value blue"},b={class:"unit"},k=u({__name:"SupplyItem",props:{config:{}},setup(p){const o=p;return(s,B)=>{var i,l;return e(),t("div",g,[((i=s.config.rows)==null?void 0:i.indexOf(1))!==-1?(e(),t("div",h,[a("span",w,n(o.config.label),1)])):c("",!0),((l=s.config.rows)==null?void 0:l.indexOf(2))!==-1?(e(),t("div",y,[a("span",v,n(o.config.value),1),a("span",b,n(o.config.unit),1),s.config.status?(e(),d(f(m),{key:0,icon:s.config.status==="up"?"material-symbols:arrow-drop-up":"material-symbols:arrow-drop-down",class:r(s.config.status)},null,8,["icon","class"])):c("",!0),a("span",{class:r(["value blue",s.config.status])},n(o.config.scale),3)])):c("",!0)])}}}),I=_(k,[["__scopeId","data-v-3938fe10"]]);export{I as default};
