<!--分类管理-->
<template>
  <div class="wrapper">
    <CardSearch ref="refSearch" :config="searchConfig" />
    <CardTable ref="refTable" class="card-table" :config="tableConfig" />
    <DialogForm ref="refForm" :config="formConfig" />
  </div>
</template>
<script lang="ts" setup>
import { Search, Delete, Plus, Edit } from '@element-plus/icons-vue';
import { SLConfirm } from '@/utils/Message';
import {
  processTypeList,
  editProcessType,
  delProcessType
} from '@/api/engineeringManagement/process';
import useGlobal from '@/hooks/global/useGlobal';

const { $messageError, $messageSuccess, $messageWarning } = useGlobal();
const refSearch = ref<ISearchIns>();
const refTable = ref<ICardTableIns>();
const refForm = ref<IDialogFormIns>();
const contractTemplates = ref<any>([]);
// 查询条件配置
const searchConfig = reactive<ISearch>({
  filters: [{ type: 'input', label: '类型名称', field: 'name' }],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '新增',
          svgIcon: shallowRef(Plus),
          click: () => {
            formConfig.title = '新增';
            handleAddEdit();
          }
        },
        {
          perm: true,
          text: '删除',
          type: 'danger',
          svgIcon: shallowRef(Delete),
          click: () => {
            console.log(tableConfig.selectList);
            if ((tableConfig.selectList || []).length > 0) {
              const ids = tableConfig.selectList?.map((select) => {
                return select.id;
              });
              handleDelProcessType(ids as string[]);
            } else {
              $messageWarning('请选择删除数据');
            }
          }
        },

        {
          perm: true,
          text: '查询',
          svgIcon: shallowRef(Search),
          click: () => {
            tableConfig.pagination.page = 1;
            refreshData();
          }
        }
      ]
    }
  ]
});

// 列表
const tableConfig = reactive<ITable>({
  loading: true,
  dataList: [],
  selectList: [],
  indexVisible: true,
  rowKey: 'id',
  columns: [
    { prop: 'code', label: '编号', minWidth: 120 },
    { prop: 'name', label: '类型名称', minWidth: 120 },
    {
      prop: 'type',
      label: '流程类型',
      formatter: (row, val) => {
        return row.type === 'app' ? '移动端' : 'PC端';
      }
    },
    { prop: 'orderNum', label: '排序', minWidth: 120 },
    { prop: 'remark', label: '备注', minWidth: 120 },
    {
      prop: 'status',
      label: '生效',
      minWidth: 120,
      iconStyle: {
        color: '#19a39e'
      },
      cellStyle: (row) => ({
        color: row.status ? '#36a624' : '#f56c6c'
      }),
      formatter: (row) => {
        return row.status ? '是' : '否';
      }
    }
  ],
  operations: [
    {
      text: '编辑',
      isTextBtn: false,
      perm: true,
      svgIcon: shallowRef(Edit),
      click: (row) => {
        formConfig.title = '编辑';
        handleAddEdit(row);
      }
    },
    {
      text: '删除',
      isTextBtn: false,
      perm: true,
      type: 'danger',
      svgIcon: shallowRef(Delete),
      click: (row) => handleDelProcessType([row.id])
    }
  ],
  operationFixed: 'right',
  operationWidth: 200,
  pagination: {
    refreshData: ({ page, size }) => {
      tableConfig.pagination.page = page;
      tableConfig.pagination.limit = size;
      // tableConfig.dataList = tableConfig.slice((page - 1) * size, page * size)
      refreshData();
    }
  },
  handleSelectChange: (val) => {
    tableConfig.selectList = val;
  }
});

// 表单配置
const formConfig = reactive<IDialogFormConfig>({
  dialogWidth: 1200,
  labelWidth: '140px',
  title: '新建',
  defaultValue: {
    status: false
  },
  submit: undefined,
  group: [
    {
      fields: [
        {
          type: 'input',
          label: '流程类型名称',
          field: 'name',
          rules: [
            {
              required: true,
              message: '请填写标题'
            }
          ],
          placeholder: '请填写标题'
        },
        {
          type: 'select',
          label: '流程类型',
          field: 'type',
          rules: [
            {
              required: true,
              message: '请选择流程类型'
            }
          ],
          options: [
            { label: '移动端', value: 'app' },
            { label: 'PC端', value: 'pc' }
          ],
          placeholder: ''
        },
        {
          type: 'switch',
          label: '是否启用',
          field: 'status',
          rules: [
            {
              required: true,
              message: '请选择'
            }
          ]
        },
        {
          type: 'number',
          label: '排序',
          field: 'orderNum'
        },
        {
          type: 'textarea',
          label: '备注',
          field: 'remark',
          placeholder: '请填写备注'
        },
        {
          type: 'divider',
          text: '合同模板',
        },
        {
          type: 'table',
          label: '',
          field: 'contractTemplateList',
          config: {
            indexVisible: true,
            height: 200,
            dataList: computed(()=>contractTemplates.value) as any,
            titleRight: [
              {
                items: [
                  {
                    type: 'btn-group',
                    btns: [
                      {
                        type: 'primary',
                        perm: true,
                        text: '',
                        size: 'small',
                        svgIcon: shallowRef(Plus),
                        styles: {
                          padding: '5px'
                        },
                        iconStyles: {
                          marginRight: 0
                        },
                        click: () => {
                          // const configList = formConfig.group[0].fields.find(
                          //   (field) => field.field === 'configList'
                          // ) as any;
                          // console.log(configList.config.dataList);
                          contractTemplates.value.push({
                            name: '',
                            file: '',
                            remark: ''
                          });
                        }
                      }
                    ]
                  }
                ]
              }
            ],
            columns: [
              {
                label: '模板名称',
                prop: 'name',
                formItemConfig: { type: 'input' }
              },
              {
                label: '合同',
                prop: 'file',
                formItemConfig: { type: 'file', returnType: 'comma', limit: 1 }
              },
              {
                label: '备注',
                prop: 'remark',
                formItemConfig: { type: 'input' }
              }
            ],
            pagination: {
              hide: true
            },
            operations: [
              {
                perm: true,
                text: '删除',
                type: 'danger',
                svgIcon: shallowRef(Delete),
                click: (row) => {
                  //
                  console.log(row);
                  SLConfirm('确定删除？', '提示信息').then(() => {
                    const configList = formConfig.group[0].fields?.find(
                      (field) => field.field === 'configList'
                    ) as any;
                    console.log(configList.config?.dataList);
                    const dataList = configList.config?.dataList;
                    configList.config.dataList = dataList.filter((data) => {
                      return data.id !== row.id;
                    });
                  });
                }
              }
            ]
          }
        },
      
      ]
    }
  ]
});
// 附件弹框配置
const handleAddEdit = (row?: any) => {
  // const configList = formConfig.group[0].fields.find(
  //   (field) => field.field === 'configList'
  // ) as any;
  formConfig.defaultValue = {
    ...(row || { status: false })
  };
  // configList.config.dataList =
  //   
  contractTemplates.value = row && row.contractTemplateList ? row.contractTemplateList : [];
  formConfig.submit = (params: any) => {
    SLConfirm('确定提交？', '提示信息').then(() => {
      formConfig.submitting = true;
      params = {
        ...params,
        id: row ? row.id : null,
      };
      editProcessType(params)
        .then(() => {
          refForm.value?.closeDialog();
          formConfig.submitting = false;
          $messageSuccess('保存成功');
          refreshData();
        })
        .catch((error) => {
          $messageError(error);
          formConfig.submitting = false;
        });
    });
  };
  refForm.value?.openDialog();
};
// 删除数据
const handleDelProcessType = (ids: string[]) => {
  SLConfirm('确定删除？', '提示信息').then(() => {
    delProcessType(ids)
      .then(() => {
        $messageSuccess('删除成功');
        refreshData();
      })
      .catch((error) => {
        $messageWarning(error);
      });
  });
};
// 刷新数据
const refreshData = async () => {
  tableConfig.loading = true;
  const queryParams = refSearch.value?.queryParams || {};
  const params = {
    ...queryParams,
    page: tableConfig.pagination.page || 1,
    size: tableConfig.pagination.limit || 20
  };
  processTypeList(params)
    .then(async (res) => {
      const result = res.data?.data;
      tableConfig.dataList = result.data;
      tableConfig.pagination.total = result.total;
    })
    .catch((error) => {
      $messageError(error);
    });
  tableConfig.loading = false;
};

onMounted(() => {
  refreshData();
});
</script>
<style lang="scss" scoped></style>
