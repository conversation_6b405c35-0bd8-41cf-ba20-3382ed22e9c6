<!-- 属性刷 -->
<!-- 批量的把部分设备的指定属性刷成指定的参考设备的属性 -->
<template>
  <RightDrawerMap
    ref="refMap"
    :title="'属性刷'"
    :detail-max-min="true"
    :full-content="true"
    @map-loaded="onMapLoaded"
    @detail-refreshed="form.loading.value = false"
  >
    <Form
      ref="refForm"
      :config="form.FormConfig.value"
    ></Form>
    <template #detail-header>
      <span>属性刷查询结果{{ form.curLayerName.value && ' - ' + form.curLayerName.value }}</span>
    </template>
    <template #detail-default>
      <div class="detail-wrapper">
        <div class="left">
          <div class="title">
            <span> 选择刷新字段 </span>
            <div class="title-btns">
              <!-- <el-button
                type="warning"
                size="small"
                :icon="RefreshLeft"
                @click="handleUndo"
              >
                撤回
              </el-button> -->
              <el-button
                type="success"
                size="small"
                :icon="Refresh"
                :loading="state.submitting"
                :disabled="!TableConfig.selectList?.length"
                @click="handleRefreshAttrs"
              >
                确定
              </el-button>
            </div>
          </div>
          <div class="table-box">
            <FormTable :config="TableConfig"></FormTable>
          </div>
        </div>
        <div class="right">
          <div class="title">
            <span>
              待刷新要素
              {{ TableConfig.currentRow?.id ? ' - 序号' + TableConfig.currentRow?.id : '' }}
            </span>
            <FormTableColumnFilter
              v-if="refDetailTable?.TableConfig_Detail.columns"
              :columns="refDetailTable.TableConfig_Detail.columns"
              :show-tooltip="true"
            ></FormTableColumnFilter>
          </div>
          <div class="table-box">
            <DetailTable
              ref="refDetailTable"
              @row-click="handleRowClick"
              @refresh-data="refreshData"
            ></DetailTable>
          </div>
        </div>
      </div>
    </template>
  </RightDrawerMap>
</template>
<script lang="ts" setup>
import { Refresh } from '@element-plus/icons-vue'
import { IFormIns } from '@/components/type'
import { SLConfirm, SLMessage } from '@/utils/Message'
import RightDrawerMap from '../../components/common/RightDrawerMap.vue'
import DetailTable from '../../components/common/DetailTable.vue'

import { useForm } from './hooks/pipeAttrBrush'
import { applyEdits, generate4548Graphic } from '@/utils/MapHelper'
import { PostGisOperateLog } from '@/api/system/gisSetting'
import { EGigLogFunc, EGisLogApp, EGisLogOperateType } from '../../config'
import { skipedFields } from './config'

const state = reactive<{
  submitting: boolean
}>({
  submitting: false
})
const refForm = ref<IFormIns>()
const form = useForm(
  refForm,
  () => queryCallBack(),
  () => refreshAttrTable()
)
const queryCallBack = () => {
  const identifyResult = form.pick.getIdentifyResult()
  if (!identifyResult) {
    SLMessage.warning('请先选择参考设备')
    return
  }
  refMap.value?.toggleCustomDetail(true)
  setTimeout(() => {
    refreshData()
  }, 0)
  refreshAttrTable()
}
const refreshAttrTable = () => {
  const identifyResult = form.pick.getIdentifyResult()
  if (!identifyResult) {
    TableConfig.dataList = []
  } else {
    const ignoredFields = [
      'OBJECTID',
      'SID',
      'START_SID',
      'END_SID',
      'START_DEPTH',
      'END_DEPTH',
      'X',
      'Y',
      'Z',
      'AUDITEDBY ',
      'GATHERER',
      'CREATEDBY',
      'CREATEDDATE',
      'UPDATEDDATE',
      'COMPLETIONDATE',
      'DESIGNNO',
      'ARCHIVENO',
      'ATTACH_SID',
      'OperatingMax',
      'FACILITYID',
      ...skipedFields
    ]
    TableConfig.dataList = form.state.curLayerFields
      .filter(item => ignoredFields.indexOf(item.name) === -1)
      .map(item => {
        item.value = identifyResult?.feature.attributes[item.alias]
        return item
      })
  }
  refreshData()
}
const refreshData = () => {
  const tabs = form.state.tabs
  const layer = {
    layerid: refForm.value?.dataForm.layerid?.[0],
    layername: tabs[0]?.name,
    oids: tabs[0]?.data
  }
  const queryParams = {
    geometry: form.draw.getGraphic()?.geometry,
    where: refForm.value?.dataForm.sql || '1=1'
  }
  refDetailTable.value?.refreshDetail(staticState.view, layer, {
    queryParams
  })
}

const refMap = ref<InstanceType<typeof RightDrawerMap>>()

const staticState: {
  view?: __esri.MapView
} = {}
const TableConfig = reactive<ITable>({
  dataList: [],
  handleSelectChange: rows => {
    TableConfig.selectList = rows
  },
  // indexVisible: true,
  columns: [
    { label: '字段名', prop: 'alias' },
    { label: '字段值', prop: 'value' }
  ],
  pagination: {
    hide: true
  }
})

/** *************详情table*************** */
const refDetailTable = ref<InstanceType<typeof DetailTable>>()
const handleRowClick = row => {
  refDetailTable.value?.extentTo(staticState.view, row.OBJECTID)
}
const handleRefreshAttrs = () => {
  if (!form.pick.getIdentifyResult()) {
    SLMessage.warning('请重新查询待刷新要素')
    return
  }
  SLConfirm('应用到空间数据？', '提示信息')
    .then(() => {
      const fields = TableConfig.selectList || []
      const features = refDetailTable.value?.staticState.tabFeatures
      const message: string[] = []
      const ids: string[] = features?.map(item => item.attributes.OBJECTID) || []
      const layername = form.curLayerName.value

      if (!features?.length) {
        SLMessage.error('当前没有待刷新要素，操作已终止')
        return
      }
      fields.map(item => {
        message.push(`将字段【${item.alias}】统一刷新至【${item.value}】`)
      })
      const submitFeatures = features?.map(feature => {
        const g = window.SITE_CONFIG.SITENAME === 'qingyang' ? generate4548Graphic(feature) : feature
        fields.map(item => {
          g.attributes[item.name] = item.value === 'Null' ? null : item.value
        })
        return g
      })
      state.submitting = true
      applyEdits(refForm.value?.dataForm.layerid?.[0], {
        updateFeatures: submitFeatures
      })
        .then(() => {
          PostGisOperateLog({
            optionName: EGigLogFunc.SHUXINGSHUA,
            type: EGisLogApp.BASICGIS,
            content: `对OBJECTID为${ids.join('、')}的${layername}应用属性刷，${message.join('，')}`,
            optionType: EGisLogOperateType.UPDATE
          }).catch(() => {
            console.log('生成gis操作日志失败')
          })
          refreshData()
        })
        .finally(() => {
          state.submitting = false
        })
    })
    .catch(() => {
      //
    })
}
const onMapLoaded = view => {
  staticState.view = view
  form.init(staticState.view)
}
</script>
<style lang="scss" scoped>
.darkblue {
  .detail-wrapper {
    .title {
      background-color: rgba(21, 45, 68, 1);
    }
  }
}
.detail-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  .left {
    width: 350px;
  }
  .right {
    width: calc(100% - 350px);
    border-left: 1px solid var(--el-border-color);
  }
  .title {
    background-color: #ddd;
    height: 40px;
    font-size: 14px;
    display: flex;
    align-items: center;
    padding: 0 10px;
    justify-content: space-between;
    margin: 0;
  }
  .table-box {
    padding: 8px;
    height: calc(100% - 40px);
    width: 100%;
  }
}
</style>
