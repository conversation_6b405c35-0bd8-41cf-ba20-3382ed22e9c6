<template>
  <div class="one-map-detail">
    <div v-loading="state.detailLoading" class="row1">
      <FieldSet
        :size="'default'"
        :title="'压力监测'"
        :type="'simple'"
        class="row-title"
      ></FieldSet>
      <div class="pie-charts">
        <div ref="refChartDiv" class="pie-chart">
          <VChart ref="refChart1" :option="state.pieChart1"></VChart>
        </div>
      </div>
    </div>
    <div class="row2">
      <div class="detail-attrgrou-radio">
        <el-radio-group v-model="state.curRadio" @change="refreshRealtimeList">
          <el-radio
            v-for="(item, i) in state.radioGroup"
            :key="i"
            :label="item"
          >
            {{ item }}
          </el-radio>
        </el-radio-group>
      </div>
      <div class="detail-right">
        <div v-loading="state.detailLoading" class="list-items overlay-y">
          <div
            v-for="(item, i) in state.stationRealTimeData"
            :key="i"
            class="list-item"
          >
            <div class="item-label">
              {{ item.propertyName }}
            </div>
            <div class="item-content">
              {{ item.value || '--' }} {{ item.unit }}
            </div>
          </div>
        </div>
        <div v-loading="state.detailLoading" class="chart-box">
          <VChart ref="refChart4" :option="state.lineChartOption"></VChart>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { hourlyLine } from '../../components/components/chart';
import { GetPressureStationDetail } from '@/api/mapservice/onemap';
import { transNumberUnit } from '@/utils/GlobalHelper';
import {
  GetStationAttrGroupNames,
  GetStationRealTimeDetail
} from '@/api/shuiwureports/zhandian';
import { gaugeOption } from '../../echarts';
import { useDetector } from '@/hooks/echarts';

const emit = defineEmits(['refresh', 'mounted']);
const { proxy }: any = getCurrentInstance();
const state = reactive<{
  curRow?: any;
  curRadio: string;
  radioGroup: any[];
  pieChart1: any;
  mapClick?: any;
  lineChartOption: any;
  stationRealTimeData: any[];
  detailLoading: boolean;
}>({
  curRadio: '',
  radioGroup: [],
  pieChart1: gaugeOption(0, { max: 5, title: '压力(Mpa)' }),
  lineChartOption: null,
  stationRealTimeData: [],
  detailLoading: false
});

const refreshDetail = async (row) => {
  emit('refresh', { title: row.name });
  state.detailLoading = true;
  state.curRow = row;
  try {
    const trans = (value) => {
      const val = transNumberUnit(value);
      return { value: +val.value.toFixed(2), unit: val.unit };
    };

    const p1 = GetPressureStationDetail({
      stationId: row.stationId
    })
      .then((res) => {
        const pressure =
          res.data.data.pressure?.map((item) => item.value?.toFixed(2)) || [];
        state.lineChartOption = hourlyLine({
          line1: {
            data: pressure,
            unit: 'MPa',
            name: '压力'
          }
        });
        proxy.$refs['refChart4']?.resize();
        const value1 = trans(res.data.data?.currentPressure || 0);
        state.pieChart1 = gaugeOption(value1.value, {
          max: 5,
          title: '压力(' + (value1.unit || '') + 'MPa)'
        });
      })
      .finally(() => {
        resize();
      });

    const p2 = GetStationAttrGroupNames({ stationId: row.stationId }).then(
      (res) => {
        state.radioGroup = res.data || [];
        state.curRadio = state.radioGroup[0];
        refreshRealtimeList(state.radioGroup[0]);
      }
    );
    Promise.all([p1, p2]).finally(() => {
      state.detailLoading = false;
    });
  } catch (error) {
    state.detailLoading = false;
  }
};
const refreshRealtimeList = async (type) => {
  const real = await GetStationRealTimeDetail(state.curRow?.stationId, type);
  state.stationRealTimeData = real.data || [];
};
defineExpose({
  refreshDetail
});
const resize = () => {
  Array.from({ length: 2 }).map((item, i) => {
    proxy.$refs['refChart' + (i + 1)]?.resize();
  });
};
const resizer = useDetector();
const refChartDiv = ref<HTMLDivElement>();
onMounted(() => {
  emit('mounted');
  resizer.listenToMush(refChartDiv.value, resize);
});
</script>
<style lang="scss" scoped>
// .dark,
// .darkblue {
//   .one-map-detail {
//     .row1,
//     .row2 {
//       background-color: rgb(16, 39, 60);
//     }
//   }
// }
.one-map-detail {
  .row1 {
    background-color: var(--el-bg-color);
    height: 370px;
    align-items: center;
    padding: 8px 8px 0 8px;
    margin-bottom: 20px;
    .row-title {
      margin: 0;
    }
    .pie-charts {
      display: flex;
      height: 346px;
    }
  }
  .row2 {
    background-color: var(--el-bg-color);
    height: 400px;
  }
  .pie-chart {
    width: 33.33%;
    height: 260px;
  }
}

.detail-attrgrou-radio {
  // background-color: rgba(21, 45, 68, 1);
  padding: 0 12px;
  height: 40px;
  align-items: center;
  display: flex;
}
.detail-right {
  display: flex;
  width: 100%;
  height: calc(100% - 40px);
  .list-items {
    min-width: 260px;
    height: 100%;
    width: 260px;
    padding: 8px 12px;
    .list-item {
      width: 100%;
      height: 38px;
      color: var(--el-color-primary);
      font-size: 14px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border: 1px solid var(--el-color-primary);
      margin-bottom: 20px;
      padding: 8px;
      :last-child {
        margin-bottom: 0;
      }
    }
  }
  .chart-box {
    height: 100%;
    width: 100%;
  }
}
</style>
