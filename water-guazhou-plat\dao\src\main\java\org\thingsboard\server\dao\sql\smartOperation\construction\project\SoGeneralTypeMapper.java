package org.thingsboard.server.dao.sql.smartOperation.construction.project;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralType;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoGeneralTypePageRequest;

@Mapper
public interface SoGeneralTypeMapper extends BaseMapper<SoGeneralType> {

    IPage<SoGeneralType> findByPage(SoGeneralTypePageRequest request);

    boolean update(SoGeneralType entity);

    int updateFully(SoGeneralType entity);

}
