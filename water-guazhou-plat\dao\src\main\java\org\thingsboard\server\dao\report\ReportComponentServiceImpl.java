package org.thingsboard.server.dao.report;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.request.ReportRequest;
import org.thingsboard.server.dao.model.sql.report.ReportComponent;
import org.thingsboard.server.dao.model.sql.report.ReportQuery;
import org.thingsboard.server.dao.sql.report.ReportComponentMapper;
import org.thingsboard.server.dao.sql.report.ReportQueryMapper;

import java.util.*;

/**
 *
 */
@Service
public class ReportComponentServiceImpl implements ReportComponentService {

    @Autowired
    private ReportComponentMapper reportComponentMapper;

    @Autowired
    private ReportQueryMapper reportQueryMapper;

    @Override
    @Transactional
    public JSONObject save(JSONObject object) {
        ReportComponent reportComponent = new ReportComponent();
        reportComponent.setId(object.getString("id"));
        reportComponent.setTenantId(object.getString("tenantId"));
        reportComponent.setContent(object.toJSONString());
        Long time = object.getLong("createTime");
        if (time == null) {
            reportComponent.setCreateTime(new Date());
            reportComponentMapper.insert(reportComponent);
            object.put("createTime", System.currentTimeMillis());
        } else {
            Map deleteMap = new HashMap();
            deleteMap.put("id", object.getString("id"));
            reportQueryMapper.deleteByMap(deleteMap);
            reportComponentMapper.updateById(reportComponent);
        }

        if ("3".equals(object.getString("dataType"))) {
            // 添加到query库
            ReportQuery reportQuery = new ReportQuery();
            reportQuery.setId(object.getString("id"));
            reportQuery.setDatabaseId(object.getString("databaseId"));
            reportQuery.setContent(object.getString("value"));
            reportQuery.setCreateTime(new Date());
            reportQuery.setTenantId(object.getString("tenantId"));

            reportQueryMapper.insert(reportQuery);
        }


        return object;
    }


    @Override
    public PageData<JSONObject> getList(ReportRequest request) {
        IPage<ReportComponent> page = new Page<>(request.getPage(), request.getSize());
        IPage<ReportComponent> result = reportComponentMapper.getList(page, request);
        List<JSONObject> resultObject = new ArrayList<>();
        JSONObject jsonObject;
        for (ReportComponent reportComponent : result.getRecords()) {
            try {
                jsonObject = JSONObject.parseObject(reportComponent.getContent());
                jsonObject.put("databaseName", reportComponent.getDatabaseName());
                jsonObject.put("createTime", reportComponent.getCreateTime().getTime());

                resultObject.add(jsonObject);
            } catch (Exception e) {
                resultObject.add(new JSONObject());
                e.printStackTrace();
            }
        }
        return new PageData<>(result.getTotal(), resultObject);
    }

    @Override
    @Transactional
    public void delete(List<String> ids) {
        reportComponentMapper.deleteBatchIds(ids);

        // 删除数据库查询条件
        reportQueryMapper.deleteBatchIds(ids);
    }

}
