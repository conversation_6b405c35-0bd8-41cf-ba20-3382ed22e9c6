<!-- 错误上报 -->
<template>
  <DrawerBox
    ref="refDrawer"
    :left-drawer="true"
    :right-drawer="true"
    :right-drawer-title="'错误属性上报'"
    :right-drawer-absolute="false"
    :left-drawer-absolute="false"
    :left-drawer-bar-hide="false"
    :bottom-drawer="true"
    :left-drawer-width="400"
    :left-drawer-title="'请点击地图选择上报设备'"
    :bottom-drawer-bar-position="'left'"
    :bottom-drawer-title="'上报列表'"
  >
    <ArcLayout
      @map-loaded="onMapLoaded"
      @pipe-loaded="onPipeLoaded"
    ></ArcLayout>
    <template #right>
      <Form
        ref="refForm"
        :config="FormConfig"
      ></Form>
      <FormTable :config="TableConfig"></FormTable>
    </template>
    <template #left-title>
      <span>{{ customConfig.title }}</span>
    </template>
    <template #left>
      <ErrorPopTable
        :config="customConfig"
        @report="handleReport"
      ></ErrorPopTable>
    </template>
    <template #bottom>
      <FormTable :config="TableConfigRecord"></FormTable>
    </template>
  </DrawerBox>
</template>
<script lang="ts" setup>
import Graphic from '@arcgis/core/Graphic'
import {
  excuteIdentify,
  excuteQuery,
  // from4548Graphic,
  getGraphicLayer,
  gotoAndHighLight,
  initIdentifyParams,
  initQueryParams,
  setMapCursor,
  setSymbol
} from '@/utils/MapHelper'
import { convertGeoJSONToArcGIS, excuteIdentifyByGeoserver, queryFeatureByIdGeoserver } from '@/utils/geoserver/geoserverUtils'
import { SLConfirm, SLMessage } from '@/utils/Message'
import { GetAllFieldConfig } from '@/api/mapservice/fieldconfig'
// @ts-ignore
import ErrorPopTable from './components/ErrorPopTable.vue'
import { GetErrorReportList, AddMapErrorUpload } from '@/api/mapservice/errorReport'
import { disabledFields, reportType, tagColors } from './data'
import { formatDate } from '@/utils/DateFormatter'
import { formatterDateTime } from '@/utils/GlobalHelper'
import { skipedFields } from '../PipeManage/config'
import { useGisStore } from '@/store'

const gisStore = useGisStore()
const refDrawer = ref<any>()
const customConfig = ref<{
  hideFooter: boolean
  row?: any
  fid?: string
  layer?: number
  dataList: any[]
  title: string
  img?: string
}>({
  hideFooter: true,
  row: undefined,
  dataList: [],
  title: ''
})
const refForm = ref<IFormIns>()
const state = reactive<{
  fieldConfig: IGISFieldConfig[]
}>({
  fieldConfig: []
})
const staticState: {
  view?: __esri.MapView
  graphicsLayer?: __esri.GraphicsLayer
  identifyResults: any[]
} = {
  identifyResults: []
}
const FormConfig = reactive<IFormConfig>({
  group: [
    {
      id: 'layer',
      fieldset: {
        desc: '选择图层'
      },
      fields: [
        {
          type: 'tree',
          options: [],
          // checkStrictly: true,
          label: '选择图层',
          showCheckbox: true,
          field: 'layerid',
          nodeKey: 'value'
        }
      ]
    },
    {
      fieldset: {
        desc: '点击地图选择设备'
      },
      fields: []
    }
  ],
  labelPosition: 'top',
  gutter: 12,
  defaultValue: {
    length: 1
  }
})

const TableConfig = reactive<ITable>({
  height: 'none',
  columns: [
    { label: '类型', prop: 'layerName' },
    { label: '编号', prop: 'layerId' }
  ],
  pagination: {
    hide: true
  },
  handleRowClick: row => gotoAndOpenPop(row),
  dataList: []
})

const TableConfigRecord = reactive<ITable>({
  indexVisible: true,
  columns: [
    { minWidth: 120, label: '设备类别', prop: 'layer' },
    { minWidth: 120, label: '上报人', prop: 'uploadUserName' },
    {
      minWidth: 120,
      label: '上报时间',
      prop: 'uploadTime',
      formatter(row, value) {
        return formatDate(value, formatterDateTime)
      }
    },
    {
      minWidth: 60,
      label: '上报内容',
      align: 'center',
      prop: 'content',
      formItemConfig: {
        type: 'btn-group',
        btns: [
          {
            perm: true,
            text: '查看',
            styles: {
              width: '100%'
            },
            click: row => handleViewDetail(row),
            isTextBtn: true
          }
        ]
      }
    },
    {
      minWidth: 60,
      label: '状态',
      prop: 'status',
      tag: true,
      align: 'center',
      tagColor: row => tagColors[row.status],
      formatter(row, value) {
        return reportType[value] || '待处理'
      }
    },
    {
      minWidth: 120,
      label: '审批时间',
      prop: 'approvalTime',
      formatter(row, value) {
        return formatDate(value, formatterDateTime)
      }
    },
    { minWidth: 120, label: '备注', prop: 'remark' }
  ],
  pagination: {
    refreshData: ({ page, size }) => {
      TableConfigRecord.pagination.page = page
      TableConfigRecord.pagination.limit = size
      refreshData()
    }
  },
  dataList: []
})
/**
 * 查看错误上报详情
 * @param row 上报记录行数据
 */
const handleViewDetail = async (row: any) => {
  if (!row.fid) {
    SLMessage.warning('缺少设备ID信息')
    return
  }

  try {
    // 解析上报内容
    const content: any = row.uploadContent ? JSON.parse(row.uploadContent) : []
    // 查找图层信息
    let feature: __esri.Graphic | null = null
    // GeoServer模式 - 使用封装好的queryFeatureByIdGeoserver函数
    try {
      // 调用封装好的函数查询要素
      const result = await queryFeatureByIdGeoserver({
        typeName: row.layer,  // 图层名称
        id: row.fid,          // 要素ID
      });

      // 处理查询结果
      if (result.success && result.feature) {
        // 查询成功，使用返回的要素
        const geoJsonFeature = result.feature;
        const geometry = convertGeoJSONToArcGIS(geoJsonFeature.geometry);

        // 创建Graphic对象
        feature = new Graphic({
          geometry: geometry,
          attributes: geoJsonFeature.properties || {}
        });

        // 设置符号
        feature.symbol = setSymbol(geometry.type);
      } else {
          SLMessage.warning('未找到设备信息');
          return;
      }
    } catch (error) {
      console.error('GeoServer查询失败:', error)
      SLMessage.error('查询设备信息失败')
      return
    }

    // 如果找到了要素，显示详情
    if (feature) {
      // 清除之前的图形并添加新的
      staticState.graphicsLayer?.removeAll()
      staticState.graphicsLayer?.add(feature)

      // 高亮显示要素
      await gotoAndHighLight(staticState.view, feature)

      // 获取字段信息
      const field = state.fieldConfig.find(item => item.layername === row.layer)?.fields || []

      // 构建数据列表
      const dataList = content
        .filter((item: any) => item.name !== 'img')
        .map((item: any) => {
          const fieldInfo = field.find(o => o.name.toLocaleLowerCase() === item.name?.toLocaleLowerCase())
          return {
            ...(fieldInfo || {}),
            oldvalue: item.oldvalue,
            newvalue: item.newvalue
          }
        })

      // 更新UI
      TableConfig.dataList = []
      staticState.identifyResults = [{ feature, layerName: row.layer, layerId: row.fid }]

      // 设置自定义配置
      customConfig.value = {
        hideFooter: true,
        row,
        img: content.find((item: any) => item.name === 'img')?.newvalue,
        dataList,
        title: row.layer + '(' + (feature.attributes['ID'] || feature.attributes['SID'] || feature.attributes['OBJECTID'] || row.fid) + ')'
      }

      // 打开左侧抽屉
      refDrawer.value?.toggleDrawer('ltr', true)
    }
  } catch (error) {
    console.error('处理错误上报详情失败:', error)
    SLMessage.error('数据错误')
  }
}

const pickPipe = () => {
  if (!staticState.view) return
  setMapCursor('crosshair')
  staticState.graphicsLayer = getGraphicLayer(staticState.view, {
    id: 'error-report',
    title: '错误属性设备'
  })
  staticState.view?.on('click', async e => {
    await doIdentify(e)
  })
}

const doIdentify = async (e: any) => {
  const layerId = refForm.value?.dataForm.layerid || []
  if (!staticState.view) return
  if (!layerId.length) {
    SLMessage.error('请先选择要上报的图层')
    return
  }

  try {
    // 获取选中的图层名称
    const layerIds = refForm.value?.dataForm.layerid || []
    if (layerIds.length === 0) {
      SLMessage.warning('没有找到有效的图层')
      return;
    }

    // 构建结果数组
    staticState.identifyResults = [];
    const tableData: any[] = [];
    const features: __esri.Graphic[] = [];
    staticState.graphicsLayer?.removeAll();
    // 将所有选中的图层名称用逗号连接
    const layersString = layerIds.join(',');

    try {
      // 使用excuteIdentifyByGeoserver方法执行查询
      const response = await excuteIdentifyByGeoserver(
        staticState.view,
        '/geoserver/guazhou/wms',
        layersString,
        e
      );

      if (response && response.data && response.data.features && response.data.features.length > 0) {
        // 处理每个要素
        for (const geoJsonFeature of response.data.features) {
          // 获取图层名称（从feature的id中提取）
          // 格式通常是: "layername.id"
          const featureId = geoJsonFeature.id || '';
          const layerName = featureId.split('.')[0] || layerIds[0];
          const layerId = featureId.split('.')[1] || layerIds[1];

          // 将GeoJSON转换为ArcGIS Graphic
          const geometry = convertGeoJSONToArcGIS(geoJsonFeature.geometry);

          // 创建Graphic对象
          const graphic = new Graphic({
            geometry: geometry,
            attributes: geoJsonFeature.properties || {}
          });

          // 设置符号
          graphic.symbol = setSymbol(geometry.type);

          // 添加到结果数组
          staticState.identifyResults.push({
            layerName: layerName,
            layerId: layerId, // GeoServer模式下使用图层名称作为ID
            feature: graphic
          });

          // 添加到表格数据
          tableData.push({
            layerName: layerName,
            layerId: layerId,
            ...geoJsonFeature.properties
          });

          // 添加到要素数组
          features.push(graphic);
        }
      } else {
        console.log('没有查询到任何要素');
      }
    } catch (error) {
      console.error('执行多图层查询时出错:', error);
    }

    // 检查是否有查询结果
    if (staticState.identifyResults.length === 0) {
      SLMessage.warning('没有查询到设备');
      return;
    }

    // 限制结果数量
    if (staticState.identifyResults.length > 50) {
      staticState.identifyResults.length = 50;
      features.length = 50;
      tableData.length = 50;
    }

    // 更新UI
    staticState.graphicsLayer?.addMany(features);
    TableConfig.dataList = tableData;

    // 打开属性面板
    // gotoAndOpenPop(staticState.identifyResults[0].feature?.attributes);

  } catch (error) {
    console.error('查询出错:', error);
    staticState.view?.graphics.removeAll();
    SLMessage.error('查询出错');
  }
}
const gotoAndOpenPop = async (row: any) => {
  debugger
  const result = staticState.identifyResults.find(item => item.layerId === row.layerId)
  if (!result) return
  await gotoAndHighLight(staticState.view, result?.feature)

  // 获取要素的属性
  const fields = result.feature.attributes;

  // 构建数据列表
  const dataList: Array<{
    name: string;
    alias: string;
    editable: boolean;
    oldvalue: any;
  }> = [];

  // 遍历属性对象，将每个属性转换为数据项
  for (const key in fields) {
    // 跳过不需要显示的字段
    if (skipedFields.indexOf(key) !== -1) continue;

    // 创建数据项
    dataList.push({
      name: key,         // 字段名
      alias: key,        // 字段别名
      editable: disabledFields.indexOf(key.toUpperCase()) === -1,  // 是否可编辑
      oldvalue: fields[key]  // 字段值
    });
  }

  customConfig.value = {
    hideFooter: false,
    title: result.layerName + '(' + (result.feature.attributes['layerId'] || result.layerId) + ')',
    layer: result.layerName,
    fid: result.layerId,
    dataList: dataList
  }
  refDrawer.value?.toggleDrawer('ltr', true)
}
const handleReport = async (datas: any[], config: Record<string, any>, extrainfo: { img: string; remark: string }) => {
  SLConfirm('确定上报？', '提示信息')
    .then(async () => {
      try {
        const content = datas
          ?.filter(item => item.newvalue !== undefined)
          ?.map(item => {
            return {
              name: item.name,
              alias: item.alia,
              oldvalue: item.oldvalue,
              newvalue: item.newvalue
            }
          }) || []
        if (extrainfo.img) {
          content.push({ name: 'img', alias: '现场图片', oldvalue: extrainfo.img, newvalue: extrainfo.img })
        }
        const res = await AddMapErrorUpload({
          layer: config.layer,
          fid: config.fid,
          remark: extrainfo.remark,
          uploadContent: JSON.stringify(content)
        })
        if (res.data.code === 200) {
          SLMessage.success('上报成功')
          refDrawer.value?.toggleDrawer('ltr', false)
          refreshData()
        } else {
          SLMessage.error('上报失败')
        }
      } catch (error: any) {
        SLMessage.error('上报失败')
      }
    })
    .catch(() => {
      //
    })
}
/**
 * 刷新上报记录表数据
 */
const refreshData = () => {
  GetErrorReportList({
    size: TableConfigRecord.pagination.limit || 20,
    page: TableConfigRecord.pagination.page || 1
  }).then(res => {
    const result = res.data.data
    TableConfigRecord.dataList = result.data || []
    TableConfigRecord.pagination.total = result.total || 0
  })
}
const initFieldConfig = async () => {
  const res = await GetAllFieldConfig()
  state.fieldConfig = res.data.result || []
}
const getLayerInfo = () => {
  if(window.GIS_SERVER_SWITCH){
    const field = FormConfig.group.find(item => item.id === 'layer')?.fields[0] as IFormTree
    // 使用类型断言解决类型错误
    const layerInfo = (staticState.view?.layerViews as any)?.items?.[0]?.layer?.sublayers;

    if (layerInfo && layerInfo.items) {
      let layers = layerInfo.items.map((item: any) => {
        return {
          label: item.name,
          value: item.name,
          // data: item
        }
      });
      field.options = layers;
      refForm.value && (refForm.value.dataForm.layerid = layers.map((item: any) => item.value))
    }
  }else{
    const field = FormConfig.group.find(item => item.id === 'layer')?.fields[0] as IFormTree
    field
      && (field.options = [
        { label: '管点类', value: -1, children: gisStore.gLayerOption_Point },
        { label: '管线类', value: -2, children: gisStore.gLayerOption_Line }
      ])
    refForm.value && (refForm.value.dataForm.layerid = gisStore.gLayerIds || [])
  }
}
const onMapLoaded = async (view: __esri.MapView) => {
  staticState.view = view
  refreshData()
}
const onPipeLoaded = () => {
  pickPipe()
  setTimeout(() => {
    getLayerInfo()
  },1000)
  refDrawer.value?.toggleDrawer('btt', true)
}
onMounted(() => {
  refDrawer.value?.toggleDrawer('rtl', true)
  initFieldConfig()
})
onBeforeUnmount(() => {
  staticState.graphicsLayer?.removeAll()
})
</script>
<style lang="scss" scoped></style>
<style lang="scss">
.gis-bottom-detail-drawer {
  width: 100%;
  height: 300px;
  position: absolute;
  bottom: 0;
  left: 0;
}
</style>
