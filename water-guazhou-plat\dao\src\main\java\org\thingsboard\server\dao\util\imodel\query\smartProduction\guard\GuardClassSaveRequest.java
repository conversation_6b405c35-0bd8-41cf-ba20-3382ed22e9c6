package org.thingsboard.server.dao.util.imodel.query.smartProduction.guard;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartProduction.guard.GuardClass;
import org.thingsboard.server.dao.util.imodel.query.IStarHttpRequest;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

import java.time.LocalTime;

@Getter
@Setter
public class GuardClassSaveRequest extends SaveRequest<GuardClass> {
    // 班次名称
    @NotNullOrEmpty
    private String name;

    // 班次序号
    @NotNullOrEmpty
    private Integer serialNo;

    // 上班时间
    @NotNullOrEmpty
    private String beginTime;

    // 结束时间
    @NotNullOrEmpty
    private String endTime;

    // 地点id
    @NotNullOrEmpty
    private String placeId;

    @Override
    public String valid(IStarHttpRequest request) {
        try {
            LocalTime.parse(beginTime);
            LocalTime.parse(endTime);
        } catch(Exception e) {
            return "时间格式有误";
        }
        return super.valid(request);
    }

    @Override
    protected GuardClass build() {
        GuardClass entity = new GuardClass();
        entity.setCreator(currentUserUUID());
        entity.setCreateTime(createTime());
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    @Override
    protected GuardClass update(String id) {
        GuardClass entity = new GuardClass();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(GuardClass entity) {
        entity.setName(name);
        entity.setSerialNo(serialNo);
        entity.setBeginTime(beginTime);
        entity.setEndTime(endTime);
        entity.setPlaceId(placeId);
        entity.setUpdateTime(createTime());
        entity.setUpdateUserId(currentUserUUID());
    }

}