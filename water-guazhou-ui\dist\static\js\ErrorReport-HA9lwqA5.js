import{D as K}from"./DrawerBox-CLde5xC8.js";import{d as Q,W as X,c as k,r as I,bE as B,b as c,o as Y,Q as Z,g as ee,h as te,F as v,q as b,i as _,p as re,bh as oe,S as ae,_ as ie,aq as ne}from"./index-r0dFAfgr.js";import{_ as le}from"./ArcLayout-CHnHL9Pv.js";import{g as x}from"./MapView-DaoQedLH.js";import{s as E,g as O}from"./FeatureHelper-Da16o0mu.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./Point-WxyopZva.js";import{g as se}from"./LayerHelper-Cn-iiqxI.js";import"./project-DUuzYgGl.js";import{s as pe}from"./ToolHelper-BiiInOzB.js";import{GetAllFieldConfig as me}from"./fieldconfig-Bk3o1wi7.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./geometryEngineBase-BhsKaODW.js";import{q as de,c as W,e as ce}from"./geoserverUtils-wjOSMa7E.js";import{t as ue,r as fe,d as ge,E as ye}from"./ErrorPopTable-Cv-lIqKT.js";import{G as he,A as we}from"./errorReport-D3BFqMSq.js";import{f as V}from"./DateFormatter-Bm9a68Ax.js";import{s as ve}from"./config-fy91bijz.js";import"./SideDrawer-CBntChyn.js";import"./ArcView-DpMnCY82.js";import"./ViewHelper-BGCZjxXH.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./ArcStationWarning-BF9YrSzF.js";/* empty css                         */import"./useWaterPoint-Bv0z6ym6.js";import"./zhandian-YaGuQZe6.js";import"./useStation-DJgnSZIA.js";import"./arcWidgetButton-0glIxrt7.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./pipe-nogVzCHG.js";import"./StatisticsHelper-D-s_6AyQ.js";import"./useLayerList-DmEwJ-ws.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./URLHelper-B9aplt5w.js";import"./ArcPipe.vue_vue_type_script_setup_true_lang-ClsVvV2P.js";import"./WMSLayer-mTaW758E.js";import"./widget-BcWKanF2.js";import"./scaleUtils-DgkF6NQH.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./imageBitmapUtils-Db1drMDc.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./QueryHelper-ILO3qZqg.js";const pr=Q({__name:"ErrorReport",setup(be){const C=X(),y=k(),L=k({hideFooter:!0,row:void 0,dataList:[],title:""}),f=k(),T=I({fieldConfig:[]}),o={identifyResults:[]},D=I({group:[{id:"layer",fieldset:{desc:"选择图层"},fields:[{type:"tree",options:[],label:"选择图层",showCheckbox:!0,field:"layerid",nodeKey:"value"}]},{fieldset:{desc:"点击地图选择设备"},fields:[]}],labelPosition:"top",gutter:12,defaultValue:{length:1}}),F=I({height:"none",columns:[{label:"类型",prop:"layerName"},{label:"编号",prop:"layerId"}],pagination:{hide:!0},handleRowClick:e=>q(e),dataList:[]}),g=I({indexVisible:!0,columns:[{minWidth:120,label:"设备类别",prop:"layer"},{minWidth:120,label:"上报人",prop:"uploadUserName"},{minWidth:120,label:"上报时间",prop:"uploadTime",formatter(e,t){return V(t,B)}},{minWidth:60,label:"上报内容",align:"center",prop:"content",formItemConfig:{type:"btn-group",btns:[{perm:!0,text:"查看",styles:{width:"100%"},click:e=>J(e),isTextBtn:!0}]}},{minWidth:60,label:"状态",prop:"status",tag:!0,align:"center",tagColor:e=>ue[e.status],formatter(e,t){return fe[t]||"待处理"}},{minWidth:120,label:"审批时间",prop:"approvalTime",formatter(e,t){return V(t,B)}},{minWidth:120,label:"备注",prop:"remark"}],pagination:{refreshData:({page:e,size:t})=>{g.pagination.page=e,g.pagination.limit=t,S()}},dataList:[]}),J=async e=>{var t,n,p,l,a;if(!e.fid){c.warning("缺少设备ID信息");return}try{const d=e.uploadContent?JSON.parse(e.uploadContent):[];let r=null;try{const s=await de({typeName:e.layer,id:e.fid});if(s.success&&s.feature){const m=s.feature,i=W(m.geometry);r=new x({geometry:i,attributes:m.properties||{}}),r.symbol=E(i.type)}else{c.warning("未找到设备信息");return}}catch(s){console.error("GeoServer查询失败:",s),c.error("查询设备信息失败");return}if(r){(t=o.graphicsLayer)==null||t.removeAll(),(n=o.graphicsLayer)==null||n.add(r),await O(o.view,r);const s=((p=T.fieldConfig.find(i=>i.layername===e.layer))==null?void 0:p.fields)||[],m=d.filter(i=>i.name!=="img").map(i=>({...s.find(h=>{var w;return h.name.toLocaleLowerCase()===((w=i.name)==null?void 0:w.toLocaleLowerCase())})||{},oldvalue:i.oldvalue,newvalue:i.newvalue}));F.dataList=[],o.identifyResults=[{feature:r,layerName:e.layer,layerId:e.fid}],L.value={hideFooter:!0,row:e,img:(l=d.find(i=>i.name==="img"))==null?void 0:l.newvalue,dataList:m,title:e.layer+"("+(r.attributes.ID||r.attributes.SID||r.attributes.OBJECTID||e.fid)+")"},(a=y.value)==null||a.toggleDrawer("ltr",!0)}}catch(d){console.error("处理错误上报详情失败:",d),c.error("数据错误")}},M=()=>{var e;o.view&&(pe("crosshair"),o.graphicsLayer=se(o.view,{id:"error-report",title:"错误属性设备"}),(e=o.view)==null||e.on("click",async t=>{await P(t)}))},P=async e=>{var n,p,l,a,d;const t=((n=f.value)==null?void 0:n.dataForm.layerid)||[];if(o.view){if(!t.length){c.error("请先选择要上报的图层");return}try{const r=((p=f.value)==null?void 0:p.dataForm.layerid)||[];if(r.length===0){c.warning("没有找到有效的图层");return}o.identifyResults=[];const s=[],m=[];(l=o.graphicsLayer)==null||l.removeAll();const i=r.join(",");try{const u=await ce(o.view,"/geoserver/guazhou/wms",i,e);if(u&&u.data&&u.data.features&&u.data.features.length>0)for(const h of u.data.features){const w=h.id||"",N=w.split(".")[0]||r[0],A=w.split(".")[1]||r[1],G=W(h.geometry),R=new x({geometry:G,attributes:h.properties||{}});R.symbol=E(G.type),o.identifyResults.push({layerName:N,layerId:A,feature:R}),s.push({layerName:N,layerId:A,...h.properties}),m.push(R)}else console.log("没有查询到任何要素")}catch(u){console.error("执行多图层查询时出错:",u)}if(o.identifyResults.length===0){c.warning("没有查询到设备");return}o.identifyResults.length>50&&(o.identifyResults.length=50,m.length=50,s.length=50),(a=o.graphicsLayer)==null||a.addMany(m),F.dataList=s}catch(r){console.error("查询出错:",r),(d=o.view)==null||d.graphics.removeAll(),c.error("查询出错")}}},q=async e=>{var l;debugger;const t=o.identifyResults.find(a=>a.layerId===e.layerId);if(!t)return;await O(o.view,t==null?void 0:t.feature);const n=t.feature.attributes,p=[];for(const a in n)ve.indexOf(a)===-1&&p.push({name:a,alias:a,editable:ge.indexOf(a.toUpperCase())===-1,oldvalue:n[a]});L.value={hideFooter:!1,title:t.layerName+"("+(t.feature.attributes.layerId||t.layerId)+")",layer:t.layerName,fid:t.layerId,dataList:p},(l=y.value)==null||l.toggleDrawer("ltr",!0)},U=async(e,t,n)=>{ae("确定上报？","提示信息").then(async()=>{var p,l;try{const a=((p=e==null?void 0:e.filter(r=>r.newvalue!==void 0))==null?void 0:p.map(r=>({name:r.name,alias:r.alia,oldvalue:r.oldvalue,newvalue:r.newvalue})))||[];n.img&&a.push({name:"img",alias:"现场图片",oldvalue:n.img,newvalue:n.img}),(await we({layer:t.layer,fid:t.fid,remark:n.remark,uploadContent:JSON.stringify(a)})).data.code===200?(c.success("上报成功"),(l=y.value)==null||l.toggleDrawer("ltr",!1),S()):c.error("上报失败")}catch{c.error("上报失败")}}).catch(()=>{})},S=()=>{he({size:g.pagination.limit||20,page:g.pagination.page||1}).then(e=>{const t=e.data.data;g.dataList=t.data||[],g.pagination.total=t.total||0})},$=async()=>{const e=await me();T.fieldConfig=e.data.result||[]},z=()=>{var e,t,n,p,l,a,d;if(window.GIS_SERVER_SWITCH){const r=(e=D.group.find(m=>m.id==="layer"))==null?void 0:e.fields[0],s=(a=(l=(p=(n=(t=o.view)==null?void 0:t.layerViews)==null?void 0:n.items)==null?void 0:p[0])==null?void 0:l.layer)==null?void 0:a.sublayers;if(s&&s.items){let m=s.items.map(i=>({label:i.name,value:i.name}));r.options=m,f.value&&(f.value.dataForm.layerid=m.map(i=>i.value))}}else{const r=(d=D.group.find(s=>s.id==="layer"))==null?void 0:d.fields[0];r&&(r.options=[{label:"管点类",value:-1,children:C.gLayerOption_Point},{label:"管线类",value:-2,children:C.gLayerOption_Line}]),f.value&&(f.value.dataForm.layerid=C.gLayerIds||[])}},H=async e=>{o.view=e,S()},j=()=>{var e;M(),setTimeout(()=>{z()},1e3),(e=y.value)==null||e.toggleDrawer("btt",!0)};return Y(()=>{var e;(e=y.value)==null||e.toggleDrawer("rtl",!0),$()}),Z(()=>{var e;(e=o.graphicsLayer)==null||e.removeAll()}),(e,t)=>{const n=le,p=ie,l=ne,a=K;return ee(),te(a,{ref_key:"refDrawer",ref:y,"left-drawer":!0,"right-drawer":!0,"right-drawer-title":"错误属性上报","right-drawer-absolute":!1,"left-drawer-absolute":!1,"left-drawer-bar-hide":!1,"bottom-drawer":!0,"left-drawer-width":400,"left-drawer-title":"请点击地图选择上报设备","bottom-drawer-bar-position":"left","bottom-drawer-title":"上报列表"},{right:v(()=>[b(p,{ref_key:"refForm",ref:f,config:_(D)},null,8,["config"]),b(l,{config:_(F)},null,8,["config"])]),"left-title":v(()=>[re("span",null,oe(_(L).title),1)]),left:v(()=>[b(ye,{config:_(L),onReport:U},null,8,["config"])]),bottom:v(()=>[b(l,{config:_(g)},null,8,["config"])]),default:v(()=>[b(n,{onMapLoaded:H,onPipeLoaded:j})]),_:1},512)}}});export{pr as default};
