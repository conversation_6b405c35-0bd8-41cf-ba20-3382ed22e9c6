<!-- gis压力监测点 -->
<template>
  <div class="onemap-panel-wrapper">
    <FieldSet
      :title="'监测状态统计'"
      :type="'underline'"
      style="margin-top: 0"
    ></FieldSet>
    <div class="chart-box">
      <VChart :option="state.chartOption"></VChart>
    </div>
    <div class="table-box">
      <FormTable :config="TableConfig"></FormTable>
    </div>
  </div>
</template>
<script lang="ts" setup>
import Point from '@arcgis/core/geometry/Point'
import { PopImage } from '@/views/arcMap/components'
import { ring } from '../../components/components/chart'
import { getStationImageUrl } from '@/utils/URLHelper'
import { useStationsLatestData } from '@/hooks/station/useStation'

const emit = defineEmits(['highlightMark', 'addMarks'])
const props = defineProps<{
  view?: __esri.MapView
  menu: IMenuItem
}>()
const state = reactive<{
  chartOption: any
}>({
  chartOption: ring()
})

const TableConfig = reactive<ITable>({
  indexVisible: true,
  dataList: [],
  pagination: {
    hide: true,
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page
      TableConfig.pagination.limit = size
    },
    layout: 'total,sizes, jumper'
  },
  handleRowClick: row => handleLocate(row),
  columns: [
    {
      width: 200,
      label: '名称',
      prop: 'name',
      sortable: true
    },
    {
      width: 80,
      label: '液位',
      prop: 'level',
      formatter(row, value) {
        return (value ?? '--') + ' ' + (row.level_unit ?? '')
      }
    },
    {
      width: 160,
      label: '更新时间',
      prop: 'time',
      sortable: true
    }
  ]
})
const latest = useStationsLatestData()
const refreshData = async () => {
  TableConfig.loading = true
  try {
    const res = await latest.getLatestData({ type: '水池监测站' })
    TableConfig.dataList = res || []

    const windows: IArcPopConfig[] = []
    res?.map(item => {
      const location = item.location?.split(',')
      if (location?.length === 2) {
        const point = new Point({
          longitude: location[0],
          latitude: location[1],
          spatialReference: props.view?.spatialReference
        })
        windows.push({
          visible: false,
          x: point.x,
          y: point.y,
          offsetY: -40,
          id: item.stationId,
          title: item.name,
          customComponent: shallowRef(PopImage),
          customConfig: {
            info: {
              type: 'attrs',
              imageUrl: item.imgs,
              stationId: item.stationId
            }
          },
          attributes: {
            path: props.menu.path,
            id: item.stationId,
            row: item
          },
          symbolConfig: {
            url: getStationImageUrl('水池监测站.png')
          }
        })
      }
    })
    const online = res.filter(item => item.level_status !== 'offline')?.length ?? 0
    const offline = res.filter(item => item.level_status === 'offline')?.length ?? 0
    const total = res.length

    state.chartOption = ring(
      [
        {
          scale: total === 0 ? '0' : ((online / total) * 100).toFixed(2),
          name: 'online',
          nameAlias: '运行',
          value: online.toString(),
          valueAlias: online.toString()
        },
        {
          scale: total === 0 ? '0' : ((offline / total) * 100).toFixed(2),
          name: 'offline',
          nameAlias: '离线',
          value: offline.toString(),
          valueAlias: offline.toString()
        }
      ],
      '个',
      '',
      0
    )
    emit('addMarks', {
      windows,
      customWinComp: shallowRef(PopImage)
    })
  } catch (error) {
    console.dir(error)
  }
  TableConfig.loading = false
}

const handleLocate = async row => {
  emit('highlightMark', props.menu, row?.stationId)
}
onMounted(() => {
  refreshData()
})
</script>

<style lang="scss" scoped>
.onemap-panel-wrapper {
  min-height: 610px;
  height: 100%;
}
.table-box {
  height: calc(100% - 220px);
  min-height: 200px;
}
.chart-box {
  height: 150px;
}
</style>
