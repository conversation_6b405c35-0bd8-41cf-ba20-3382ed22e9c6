package org.thingsboard.server.dao.shuiwu.assets;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.UserEntity;
import org.thingsboard.server.dao.model.sql.shuiwu.assets.AssetsAccountEntity;
import org.thingsboard.server.dao.model.sql.shuiwu.assets.AssetsHandleEntity;
import org.thingsboard.server.dao.sql.shuiwu.assets.AssetsAccountRepository;
import org.thingsboard.server.dao.sql.shuiwu.assets.AssetsHandleRepository;
import org.thingsboard.server.dao.sql.user.UserRepository;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2021-10-20
 */
@Service
@Slf4j
public class AssetsHandleServiceImpl implements AssetsHandleService {
    @Autowired
    private AssetsHandleRepository assetsHandleRepository;
    @Autowired
    private UserRepository userRepository;
    @Autowired
    private AssetsAccountRepository assetsAccountRepository;

    @Override
    public PageData getPage(JSONObject params) {
        int page = 0;
        int limit = 10;
        String handleNo = "";
        String status = "";
        String reviewerId = "";
        String applicantId = "";
        String handleType = "";
        Long start = 0l;
        Long end = System.currentTimeMillis();

        if (params.getInteger("page") != null) {
            page = params.getInteger("page") - 1;
        }

        if (params.getInteger("limit") != null) {
            limit = params.getInteger("limit");
        }

        if (StringUtils.isNotBlank(params.getString("handleNo"))) {
            handleNo = params.getString("handleNo");
        }
        handleNo = "%" + handleNo + "%";

        if (StringUtils.isNotBlank(params.getString("status"))) {
            status = params.getString("status");
        }
        status = "%" + status + "%";

        if (StringUtils.isNotBlank(params.getString("reviewerId"))) {
            reviewerId = params.getString("reviewerId");
        }
        reviewerId = "%" + reviewerId + "%";

        if (StringUtils.isNotBlank(params.getString("applicantId"))) {
            applicantId = params.getString("applicantId");
        }
        applicantId = "%" + applicantId + "%";

        if (StringUtils.isNotBlank(params.getString("handleType"))) {
            handleType = params.getString("handleType");
        }
        handleType = "%" + handleType + "%";

        if (params.getLong("start") != null) {
            start = params.getLong("start");
        }

        if (params.getLong("end") != null) {
            end = params.getLong("end");
        }

        String tenantId = params.getString("tenantId");
        PageRequest pageRequest = new PageRequest(page, limit, new Sort(Sort.Direction.DESC, "createTime"));

        Page<AssetsHandleEntity> assetsHandleEntities = assetsHandleRepository.findAllByHandleNoLikeAndStatusLikeAndReviewerIdLikeAndApplicantIdLikeAndHandleTypeLikeAndTenantIdAndCreateTimeBetween(handleNo, status, reviewerId, applicantId, handleType, tenantId, start, end, pageRequest);
        PageData<AssetsHandleEntity> pageData = new PageData<>(assetsHandleEntities.getTotalElements(), assetsHandleEntities.getContent());

        for (AssetsHandleEntity assetsHandleEntity : pageData.getData()) {
            // 申请人
            if (StringUtils.isNotBlank(assetsHandleEntity.getApplicantId())) {
                UserEntity userEntity = userRepository.findOne(assetsHandleEntity.getApplicantId());
                if (userEntity != null) {
                    assetsHandleEntity.setApplicantName(userEntity.getFirstName());
                }
            }
            // 审核人
            if (StringUtils.isNotBlank(assetsHandleEntity.getReviewerId())) {
                UserEntity userEntity = userRepository.findOne(assetsHandleEntity.getReviewerId());
                if (userEntity != null) {
                    assetsHandleEntity.setReviewerName(userEntity.getFirstName());
                }
            }

            // 设备列表
            assetsHandleEntity.setAssetsAccountList(new ArrayList<>());
            if (StringUtils.isNotBlank(assetsHandleEntity.getDeviceIds())) {
                String[] deviceIdArr = assetsHandleEntity.getDeviceIds().split(",");
                for (String deviceId : deviceIdArr) {
                    AssetsAccountEntity assetsAccountEntity = assetsAccountRepository.findOne(deviceId);
                    if (assetsAccountEntity != null) {
                        assetsHandleEntity.getAssetsAccountList().add(assetsAccountEntity);
                    }
                }
            }
        }

        return pageData;
    }

    @Override
    public AssetsHandleEntity save(AssetsHandleEntity assetsHandleEntity) {
        if (StringUtils.isBlank(assetsHandleEntity.getId())) {
            assetsHandleEntity.setCreateTime(System.currentTimeMillis());
        }
        // 处置编号 自动生成
        if (StringUtils.isBlank(assetsHandleEntity.getHandleNo())) {
            SimpleDateFormat format = new SimpleDateFormat("yyMMddHHmmssSSS");
            String deviceNo = "CZ" + format.format(new Date());
            assetsHandleEntity.setHandleNo(deviceNo);

        }
        assetsHandleEntity.setUpdateTime(System.currentTimeMillis());
        if (StringUtils.isBlank(assetsHandleEntity.getStatus())) {
            assetsHandleEntity.setStatus("2");
        }
        if (StringUtils.isBlank(assetsHandleEntity.getReviewerId())) {
            assetsHandleEntity.setReviewerId("");
        }
        if (StringUtils.isBlank(assetsHandleEntity.getDeviceIds())) {
            assetsHandleEntity.setDeviceIds("");
        }

        assetsHandleRepository.save(assetsHandleEntity);

        return assetsHandleEntity;
    }

    @Override
    public void delete(List<String> idList) {
        for (String id : idList) {
            assetsHandleRepository.delete(id);
        }
    }

    @Override
    public Map reviewer(String id, String userId, String status) {
        Map result = new HashMap();
        // 根据id查找
        AssetsHandleEntity assetsHandleEntity = assetsHandleRepository.findOne(id);
        if (assetsHandleEntity == null) {
            result.put("code", 500);
            result.put("msg", "该id不存在");

            return result;
        }

        assetsHandleEntity.setReviewerId(userId);
        assetsHandleEntity.setStatus(status);
        assetsHandleEntity.setUpdateTime(System.currentTimeMillis());

        assetsHandleRepository.save(assetsHandleEntity);

        result.put("code", 200);
        result.put("msg", "操作成功");

        return result;
    }
}
