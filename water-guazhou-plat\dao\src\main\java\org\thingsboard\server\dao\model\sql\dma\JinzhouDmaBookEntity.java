package org.thingsboard.server.dao.model.sql.dma;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 锦州DMA小区
 */
@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.JINZHOU_DMA_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class JinzhouDmaBookEntity {

    @Id
    private String id;

    @Column(name = ModelConstants.JINZHOU_DMA_BOOK_ID)
    private String bookId;

    @Column(name = ModelConstants.JINZHOU_DMA_NAME)
    private String name;

    @Column(name = ModelConstants.JINZHOU_DMA_MAP_JSON)
    private String mapJson;

    @Column(name = ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;

}
