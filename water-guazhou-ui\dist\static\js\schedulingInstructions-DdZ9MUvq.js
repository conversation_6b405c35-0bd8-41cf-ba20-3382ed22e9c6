import{m as r}from"./index-r0dFAfgr.js";function d(e){return r({url:"/api/sp/orderRecord",method:"GET",params:e})}function t(e){return r({url:"/api/sp/orderRecord",method:"POST",data:e})}function p(e){return r({url:"/api/sp/orderRecord/send",method:"POST",data:e})}function a(e){return r({url:"/api/sp/orderRecord",method:"delete",data:e})}function n(e){return r({url:"/api/sp/orderRecord/receive",method:"POST",data:e})}function c(e){return r({url:"/api/sp/orderRecord/reject",method:"POST",data:e})}function s(e){return r({url:"/api/sp/orderRecord/reply",method:"POST",data:e})}function u(e){return r({url:"/api/sp/orderRecordType",method:"GET",params:e})}function i(e){return r({url:`/api/sp/orderRecordType/${e}`,method:"delete"})}function m(e){return r({url:"/api/sp/orderRecordType",method:"POST",data:e})}export{d as a,c as b,n as c,i as d,s as e,t as f,u as g,a as h,p as i,m as p};
