import{_ as w}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as I}from"./CardTable-rdWOL4_6.js";import{_ as O}from"./CardSearch-CB_HNR-Q.js";import{z as g,C as A,c as D,r as m,b as n,S as V,o as B,g as P,n as q,q as h}from"./index-r0dFAfgr.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";function k(c){return g({url:"/api/base/vector/configuration/list",method:"get",params:c})}function U(c){return g({url:"/api/base/vector/configuration/getDetail",method:"get",params:{id:c}})}function S(c){return g({url:"/api/base/vector/configuration/add",method:"post",data:c})}function _(c){return g({url:"/api/base/vector/configuration/edit",method:"post",data:c})}function F(c){return g({url:"/api/base/vector/configuration/deleteIds",method:"delete",data:c})}const N={class:"wrapper"},E={__name:"vectorDataConfig",setup(c){const b=D(),u=D(),y=(e,l,r)=>{if(!l){r();return}/^-?\d+(\.\d+)?$/.test(l)?r():r(new Error("只能输入数字"))},C=(e,l,r)=>{if(!l){r();return}/^[1-9]\d*$/.test(l)?r():r(new Error("只能输入正整数"))},L=m({labelWidth:"100px",filters:[{type:"input",label:"配置名称",field:"name",placeholder:"请输入配置名称",onChange:()=>d()},{type:"btn-group",btns:[{type:"primary",perm:!0,text:"查询",click:()=>d()},{perm:!0,type:"primary",text:"新增",click:()=>x()},{perm:!0,type:"danger",text:"批量删除",click:()=>v()}]}],defaultParams:{}}),a=m({columns:[{label:"配置名称",prop:"name"},{label:"数据类型",prop:"type"},{label:"数据来源",prop:"source",showOverflowTooltip:!0},{label:"3D模型URL",prop:"modelUrl",showOverflowTooltip:!0},{label:"刷新频率(秒)",prop:"refreshInterval"},{label:"缩放比例",prop:"scale"},{label:"旋转角度",prop:"rotation"},{label:"细节层次逻辑",prop:"lodStrategy",showOverflowTooltip:!0}],dataList:[],operations:[{perm:!0,type:"primary",isTextBtn:!0,text:"查看详情",click:e=>T(e)},{perm:!0,type:"primary",isTextBtn:!0,text:"编辑",click:e=>x(e)},{perm:!0,type:"danger",isTextBtn:!0,text:"删除",click:e=>v(e)}],pagination:{total:0,page:1,align:"right",limit:20,handlePage:e=>{a.pagination.page=e,d()},handleSize:e=>{a.pagination.limit=e,d()}},handleSelectChange:e=>{a.selectList=e||[]}}),t=m({title:"新增矢量数据配置",group:[{fields:[{type:"input",label:"配置名称",field:"name",rules:[{required:!0,message:"请输入配置名称"}]},{type:"select",label:"数据类型",field:"type",options:[{label:"GeoJSON",value:"GeoJSON"},{label:"Shapefile",value:"Shapefile"},{label:"KML",value:"KML"},{label:"GPX",value:"GPX"},{label:"TopoJSON",value:"TopoJSON"},{label:"WFS",value:"WFS"}],rules:[{required:!0,message:"请选择数据类型"}]},{type:"input",label:"数据来源",field:"source",placeholder:"请输入数据来源URL或路径",rules:[{required:!0,message:"请输入数据来源"}]},{type:"input",label:"3D模型URL",field:"modelUrl",placeholder:"请输入3D模型文件URL"},{type:"input",label:"刷新频率(秒)",field:"refreshInterval",placeholder:"请输入正整数，如：30",rules:[{required:!0,message:"请输入刷新频率"},{validator:C,trigger:"blur"}]},{type:"input",label:"模型缩放比例",field:"scale",placeholder:"请输入数字，如：1.0",rules:[{required:!0,message:"请输入模型缩放比例"},{validator:y,trigger:"blur"}]},{type:"input",label:"初始旋转角度",field:"rotation",placeholder:"请输入角度值，如：0",rules:[{required:!0,message:"请输入初始旋转角度"},{validator:y,trigger:"blur"}]},{type:"select",label:"细节层次逻辑",field:"lodStrategy",options:[{label:"距离LOD",value:"distance"},{label:"屏幕空间LOD",value:"screenSpace"},{label:"几何复杂度LOD",value:"geometryComplexity"},{label:"自适应LOD",value:"adaptive"},{label:"无LOD",value:"none"}],rules:[{required:!0,message:"请选择细节层次逻辑"}]}]}],labelPosition:"top",defaultValue:{},dialogWidth:600,draggable:!0,showSubmit:!0,showCancel:!0,cancelText:"取消",submitText:"确定",submit:async e=>{var l;try{e.id?(await _(e),n.success("修改成功")):(await S(e),n.success("新增成功")),(l=u.value)==null||l.closeDialog(),d()}catch{n.error("操作失败")}}}),f=()=>{t.group[0].fields.forEach(e=>{e.disabled=!1,e.readonly=!1}),t.showSubmit=!0,t.showCancel=!0,t.cancelText="取消",t.submitText="确定",t.submit=async e=>{var l;try{e.id?(await _(e),n.success("修改成功")):(await S(e),n.success("新增成功")),(l=u.value)==null||l.closeDialog(),d()}catch{n.error("操作失败")}},t.footerBtns=void 0},x=e=>{var l;f(),t.title=e?"编辑矢量数据配置":"新增矢量数据配置",t.defaultValue={...e||{}},(l=u.value)==null||l.openDialog()},T=async e=>{var r,o;const l={id:e.id||"1",name:e.name||"测试矢量数据详情",type:e.type||"GeoJSON",source:e.source||"https://example.com/data.geojson",modelUrl:e.modelUrl||"https://example.com/model.gltf",refreshInterval:e.refreshInterval||"30",scale:e.scale||"1.0",rotation:e.rotation||"0",lodStrategy:e.lodStrategy||"distance"};try{console.log("获取详情，行数据:",e);const s=await U(e.id);console.log("详情API响应:",s);let i=null;s.data?s.data.data?i=s.data.data:i=s.data:s&&(i=s),console.log("解析后的详情数据:",i),i||(console.log("使用模拟详情数据"),i=l),f(),t.title="矢量数据配置详情",t.defaultValue={...i},console.log("设置的详情数据:",t.defaultValue),t.group[0].fields.forEach(p=>{p.type==="select"&&(p.readonly=!0),p.disabled=!0}),t.showSubmit=!1,t.showCancel=!0,t.cancel=!0,t.cancelText="关闭",t.submitText=void 0,t.submit=void 0,t.submitting=!1,t.footerBtns=[{text:"关闭",type:"default",click:()=>{var p;(p=u.value)==null||p.closeDialog()}}],console.log("详情模式DialogFormConfig配置:",{showSubmit:t.showSubmit,showCancel:t.showCancel,cancel:t.cancel,cancelText:t.cancelText,submitText:t.submitText,submit:t.submit,footerBtns:t.footerBtns}),(r=u.value)==null||r.openDialog()}catch(s){console.error("获取详情失败:",s),console.log("API调用失败，使用模拟详情数据"),f(),t.title="矢量数据配置详情",t.defaultValue={...l},t.group[0].fields.forEach(i=>{i.type==="select"&&(i.readonly=!0),i.disabled=!0}),t.showSubmit=!1,t.showCancel=!0,t.cancel=!0,t.cancelText="关闭",t.submitText=void 0,t.submit=void 0,t.submitting=!1,t.footerBtns=[{text:"关闭",type:"default",click:()=>{var i;(i=u.value)==null||i.closeDialog()}}],console.log("详情模式DialogFormConfig配置:",{showSubmit:t.showSubmit,showCancel:t.showCancel,cancel:t.cancel,cancelText:t.cancelText,submitText:t.submitText,submit:t.submit,footerBtns:t.footerBtns}),(o=u.value)==null||o.openDialog(),n.error("API调用失败，当前显示模拟数据")}},v=e=>{V("确定删除？","删除提示").then(async()=>{var l;try{const r=e?[e.id]:((l=a.selectList)==null?void 0:l.map(s=>s.id))||[];if(!r.length){n.warning("请选择要删除的数据");return}(await F(r)).data?(n.success("删除成功"),d()):n.error("删除失败")}catch{n.error("删除失败")}}).catch(()=>{})},d=async()=>{var l;const e=[{id:"1",name:"城市道路矢量数据",type:"GeoJSON",source:"https://example.com/roads.geojson",modelUrl:"https://example.com/road-model.gltf",refreshInterval:"60",scale:"1.0",rotation:"0",lodStrategy:"distance"},{id:"2",name:"建筑物矢量数据",type:"Shapefile",source:"https://example.com/buildings.shp",modelUrl:"https://example.com/building-model.gltf",refreshInterval:"120",scale:"1.5",rotation:"90",lodStrategy:"screenSpace"}];try{const r=(l=b.value)==null?void 0:l.queryParams;console.log("请求参数:",{page:a.pagination.page,size:a.pagination.limit,...r||{}});const o=await k({page:a.pagination.page,size:a.pagination.limit,...r||{}});console.log("API响应数据:",o),o.data?o.data.records?(a.dataList=o.data.records||[],a.pagination.total=o.data.total||0):o.data.data&&o.data.data.records?(a.dataList=o.data.data.records||[],a.pagination.total=o.data.data.total||0):Array.isArray(o.data)?(a.dataList=o.data,a.pagination.total=o.data.length):Array.isArray(o.data.data)?(a.dataList=o.data.data,a.pagination.total=o.data.data.length):(console.warn("未知的数据结构:",o.data),a.dataList=[],a.pagination.total=0):Array.isArray(o)?(a.dataList=o,a.pagination.total=o.length):(console.warn("无法解析的响应格式:",o),a.dataList=[],a.pagination.total=0),console.log("解析后的数据:",a.dataList),console.log("总数:",a.pagination.total),a.dataList.length===0&&(console.log("使用模拟数据进行测试"),a.dataList=e,a.pagination.total=e.length)}catch(r){console.error("获取数据失败:",r),console.log("API调用失败，使用模拟数据"),a.dataList=e,a.pagination.total=e.length,n.error("API调用失败，当前显示模拟数据")}};return B(()=>{d()}),(e,l)=>{const r=O,o=I,s=w;return P(),q("div",N,[h(r,{ref_key:"refSearch",ref:b,config:L},null,8,["config"]),h(o,{class:"card-table",config:a},null,8,["config"]),h(s,{ref_key:"refDialogForm",ref:u,config:t},null,8,["config"])])}}},$=A(E,[["__scopeId","data-v-6d9266a0"]]);export{$ as default};
