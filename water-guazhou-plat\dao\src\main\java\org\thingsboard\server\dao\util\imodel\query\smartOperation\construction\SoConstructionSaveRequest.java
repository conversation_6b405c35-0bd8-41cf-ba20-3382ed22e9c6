package org.thingsboard.server.dao.util.imodel.query.smartOperation.construction;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstruction;
import org.thingsboard.server.dao.util.imodel.query.IStarHttpRequest;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
public class SoConstructionSaveRequest extends SaveRequest<SoConstruction> {
    // 编号
    @NotNullOrEmpty
    private String code;

    // 所属项目编号
    @NotNullOrEmpty
    private String projectCode;

    // 工程名称
    @NotNullOrEmpty
    private String name;

    // 工程地址
    private String address;

    // 项目类型id
    private String typeId;

    // 创建时间
    private Date createTime;

    // 甲方代表
    @NotNullOrEmpty
    private String firstpartName;

    // 联系电话
    private String firstpartPhone;

    // 详细地址
    private String detailAddress;

    // 项目概况
    private String remark;

    // 工程预算，万元
    private BigDecimal estimate;

    // 附件信息
    private String attachments;

    @Override
    public String valid(IStarHttpRequest request) {
        /*if (!code.startsWith("S")) {
            return "工程编号需要以S开头";
        }*/

        if (BigDecimal.ZERO.compareTo(estimate) > 0) {
            return "预算不能小于0";
        }

        return null;
    }

    @Override
    protected SoConstruction build() {
        SoConstruction entity = new SoConstruction();
        entity.setCode(code);
        entity.setCreator(currentUserUUID());
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    @Override
    protected SoConstruction update(String id) {
        SoConstruction entity = new SoConstruction();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(SoConstruction entity) {
        entity.setProjectCode(projectCode);
        entity.setName(name);
        entity.setAddress(address);
        entity.setTypeId(typeId);
        entity.setCreateTime(createTime);
        entity.setFirstpartName(firstpartName);
        entity.setFirstpartPhone(firstpartPhone);
        entity.setDetailAddress(detailAddress);
        entity.setRemark(remark);
        entity.setEstimate(estimate);
        entity.setAttachments(attachments);

        entity.setUpdateTime(createTime());
        entity.setUpdateUser(currentUserUUID());
    }

}