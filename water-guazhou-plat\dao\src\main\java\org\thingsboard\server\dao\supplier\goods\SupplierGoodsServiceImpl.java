package org.thingsboard.server.dao.supplier.goods;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.dao.model.sql.SupplierGoodsEntity;
import org.thingsboard.server.dao.model.sql.deviceManage.Device;
import org.thingsboard.server.dao.sql.deviceType.DeviceMapper;
import org.thingsboard.server.dao.sql.supplier.goods.SupplierGoodsMapper;

import java.util.List;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-08-24
 */
@Slf4j
@Service
@Transactional
public class SupplierGoodsServiceImpl implements SupplierGoodsService {
    @Autowired
    private SupplierGoodsMapper supplierGoodsMapper;

    @Autowired
    private DeviceMapper deviceMapper;

    @Override
    public List<SupplierGoodsEntity> getByMainId(String mainId) {
        QueryWrapper<SupplierGoodsEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("main_id", mainId);
        queryWrapper.orderByDesc("create_time");

        List<SupplierGoodsEntity> list = supplierGoodsMapper.selectList(queryWrapper);
        QueryWrapper<Device> deviceQueryWrapper = new QueryWrapper<>();
        for (SupplierGoodsEntity supplierGoodsEntity : list) {
            // 获取设备信息
            deviceQueryWrapper.clear();
            deviceQueryWrapper.eq("serial_id", supplierGoodsEntity.getSerialId());
            List<Device> deviceList = deviceMapper.selectList(deviceQueryWrapper);
            if (deviceList != null && deviceList.size() > 0) {
                supplierGoodsEntity.setName(deviceList.get(0).getName());
                supplierGoodsEntity.setModel(deviceList.get(0).getModel());
                supplierGoodsEntity.setUnit(deviceList.get(0).getUnit());
            }
        }

        return list;

    }
}
