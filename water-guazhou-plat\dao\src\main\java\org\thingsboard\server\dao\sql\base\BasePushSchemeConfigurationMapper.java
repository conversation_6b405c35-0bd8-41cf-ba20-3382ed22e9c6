package org.thingsboard.server.dao.sql.base;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.base.BasePushSchemeConfiguration;
import org.thingsboard.server.dao.util.imodel.query.base.BasePushSchemeConfigurationPageRequest;

import java.util.List;

/**
 * 平台管理-推送方案配置Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-18
 */
@Mapper
public interface BasePushSchemeConfigurationMapper {
    /**
     * 查询平台管理-推送方案配置
     *
     * @param id 平台管理-推送方案配置主键
     * @return 平台管理-推送方案配置
     */
    public BasePushSchemeConfiguration selectBasePushSchemeConfigurationById(String id);

    /**
     * 查询平台管理-推送方案配置列表
     *
     * @param basePushSchemeConfiguration 平台管理-推送方案配置
     * @return 平台管理-推送方案配置集合
     */
    public IPage<BasePushSchemeConfiguration> selectBasePushSchemeConfigurationList(BasePushSchemeConfigurationPageRequest basePushSchemeConfiguration);

    /**
     * 新增平台管理-推送方案配置
     *
     * @param basePushSchemeConfiguration 平台管理-推送方案配置
     * @return 结果
     */
    public int insertBasePushSchemeConfiguration(BasePushSchemeConfiguration basePushSchemeConfiguration);

    /**
     * 修改平台管理-推送方案配置
     *
     * @param basePushSchemeConfiguration 平台管理-推送方案配置
     * @return 结果
     */
    public int updateBasePushSchemeConfiguration(BasePushSchemeConfiguration basePushSchemeConfiguration);

    /**
     * 删除平台管理-推送方案配置
     *
     * @param id 平台管理-推送方案配置主键
     * @return 结果
     */
    public int deleteBasePushSchemeConfigurationById(String id);

    /**
     * 批量删除平台管理-推送方案配置
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBasePushSchemeConfigurationByIds(@Param("array") List<String> ids);
}
