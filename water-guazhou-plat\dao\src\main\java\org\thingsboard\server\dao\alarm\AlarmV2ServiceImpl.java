package org.thingsboard.server.dao.alarm;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.alarm.Alarm;
import org.thingsboard.server.common.data.alarm.AlarmV2;
import org.thingsboard.server.common.data.alarm.AttrAlarmJson;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.device.DeviceService;
import org.thingsboard.server.dao.entity.EntityService;
import org.thingsboard.server.dao.model.sql.StationEntity;
import org.thingsboard.server.dao.msgLog.DeviceLogService;
import org.thingsboard.server.dao.project.ProjectRelationService;
import org.thingsboard.server.dao.project.ProjectService;
import org.thingsboard.server.dao.station.StationService;
import org.thingsboard.server.dao.tenant.TenantDao;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AlarmV2ServiceImpl implements AlarmV2Service {

    @Autowired
    private AlarmDao alarmDao;

    @Autowired
    private AlarmService alarmService;

    @Autowired
    private TenantDao tenantDao;

    @Autowired
    private EntityService entityService;

    protected ExecutorService readResultsProcessingExecutor;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private DeviceLogService deviceLogService;

    @Autowired
    private DeviceService deviceService;

    @Autowired
    private ProjectRelationService projectRelationService;

    @Autowired
    private StationService stationService;

    @Autowired
    private AlarmJsonDao alarmJsonDao;


    @Override
    public PageData<AlarmV2> findList(String stationType, String projectId, Long start, Long end, String status, String level, int page, int size, TenantId tenantId) throws Exception {
        // 查询站点列表
        JSONObject param = new JSONObject();
        param.put("projectId", projectId);
        param.put("type", stationType);
        PageData<StationEntity> stationPageResult = stationService.list(1, 99999, param, tenantId);
        List<StationEntity> stationList = stationPageResult.getData();
        if (stationList == null || stationList.isEmpty()) {
            return new PageData<>();
        }
        if (start == null) {
            start = 0L;
        }
        if (end == null) {
            end = System.currentTimeMillis();
        }

        // 查询报警设置
        List<AttrAlarmJson> attrAlarmJsons = alarmJsonDao.findByTenant(tenantId).get();
        Map<String, AttrAlarmJson> attrAlarmJsonMap = attrAlarmJsons.stream()
                .collect(Collectors.toMap(attrAlarmJson -> UUIDConverter.fromTimeUUID(attrAlarmJson.getUuidId()), attrAlarmJson -> attrAlarmJson));

        // 查询站点列表下的报警列表
        List<Alarm> alarmAllList = new ArrayList<>();
        for (StationEntity station : stationList) {
            List<Alarm> alarmList = alarmService.findAlarmByStation(station.getId(), start, end);
            alarmAllList.addAll(alarmList);
        }

        if (alarmAllList.isEmpty()) {
            return new PageData<>();
        }
        // 处理数据
        alarmAllList = alarmAllList.stream().distinct().filter(alarm -> {
            if (StringUtils.isNotBlank(status)) {
                if (!alarm.getStatus().toString().equals(status)) {
                    return false;
                }
            }
            if (StringUtils.isNotBlank(level)) {
                if (!alarm.getLevel().equals(level)) {
                    return false;
                }
            }
            return true;
        }).collect(Collectors.toList());

        List<AlarmV2> resultList = new ArrayList<>();
        for (Alarm alarm : alarmAllList) {
            AlarmV2 alarmV2 = new AlarmV2();
            JSONObject details = JSONObject.parseObject(alarm.getDetails().toString());
            long count = 0;
            if (details != null) {
                JSONArray records = details.getJSONArray("record");
                if (records != null && records.size() > 0) {
                    count = records.stream().filter(record -> "ALARM".equals(((JSONObject)record).getString("status"))).count();
                }
            }
            alarmV2.setAlarmCount(count);
            alarmV2.setId(UUIDConverter.fromTimeUUID(alarm.getUuidId()));

            alarmV2.setDeviceId(UUIDConverter.fromTimeUUID(alarm.getOriginator().getId()));
            alarmV2.setDeviceName(alarm.getDeviceName());

            alarmV2.setCreateTime(new Date(alarm.getCreatedTime()));
            String type = alarm.getType();
            if ("offline".equals(type)) {
                alarmV2.setName(alarm.getDeviceName() + "_离线");
                alarmV2.setType("设备离线");
            } else if ("scope".equals(type)) {
                alarmV2.setName(alarm.getDeviceName() + "_" + alarm.getAlarmJsonName());
                alarmV2.setType("范围告警");
            } else if ("change".equals(type)) {
                alarmV2.setName(alarm.getDeviceName() + "_" + alarm.getAlarmJsonName());
                alarmV2.setType("变动告警");
            }
            alarmV2.setStatus(alarm.getStatus().toString());
            if (alarm.getEndTs() != 0) {
                alarmV2.setEndTime(new Date(alarm.getEndTs()));
            }
            alarmV2.setLevel(alarm.getSeverity());
            if (StringUtils.isNotBlank(alarm.getAlarmJsonId())) {
                AttrAlarmJson attrAlarmJson = attrAlarmJsonMap.get(alarm.getAlarmJsonId());
                if (attrAlarmJson != null) {
                    alarmV2.setProperty(attrAlarmJson.getAttribute());
                }
            }

            resultList.add(alarmV2);
        }

        return PageData.page(resultList, page, size);
    }

    @Override
    public List<AlarmV2> findList(String stationType, String projectId, Long start, Long end, String status, String level, TenantId tenantId) throws Exception {
        // 查询站点列表
        JSONObject param = new JSONObject();
        param.put("projectId", projectId);
        param.put("type", stationType);
        PageData<StationEntity> stationPageResult = stationService.list(1, 99999, param, tenantId);
        List<StationEntity> stationList = stationPageResult.getData();
        if (stationList == null || stationList.isEmpty()) {
            return new ArrayList<>();
        }
        if (start == null) {
            start = 0L;
        }
        if (end == null) {
            end = System.currentTimeMillis();
        }

        // 查询报警设置
        List<AttrAlarmJson> attrAlarmJsons = alarmJsonDao.findByTenant(tenantId).get();
        Map<String, AttrAlarmJson> attrAlarmJsonMap = attrAlarmJsons.stream()
                .collect(Collectors.toMap(attrAlarmJson -> UUIDConverter.fromTimeUUID(attrAlarmJson.getUuidId()), attrAlarmJson -> attrAlarmJson));

        // 查询站点列表下的报警列表
        List<Alarm> alarmAllList = new ArrayList<>();
        for (StationEntity station : stationList) {
            List<Alarm> alarmList = alarmService.findAlarmByStation(station.getId(), start, end);
            alarmAllList.addAll(alarmList);
        }

        if (alarmAllList.isEmpty()) {
            return new ArrayList<>();
        }
        // 处理数据
        alarmAllList = alarmAllList.stream().distinct().filter(alarm -> {
            if (StringUtils.isNotBlank(status)) {
                if (!alarm.getStatus().toString().equals(status)) {
                    return false;
                }
            }
            if (StringUtils.isNotBlank(level)) {
                if (!alarm.getLevel().equals(level)) {
                    return false;
                }
            }
            return true;
        }).collect(Collectors.toList());

        List<AlarmV2> resultList = new ArrayList<>();
        for (Alarm alarm : alarmAllList) {
            AlarmV2 alarmV2 = new AlarmV2();
            JSONObject details = JSONObject.parseObject(alarm.getDetails().toString());
            long count = 0;
            if (details != null) {
                JSONArray records = details.getJSONArray("record");
                if (records != null && records.size() > 0) {
                    count = records.stream().filter(record -> "ALARM".equals(((JSONObject)record).getString("status"))).count();
                }
            }
            alarmV2.setAlarmCount(count);

            alarmV2.setDeviceId(UUIDConverter.fromTimeUUID(alarm.getOriginator().getId()));
            alarmV2.setDeviceName(alarm.getDeviceName());

            alarmV2.setCreateTime(new Date(alarm.getCreatedTime()));
            String type = alarm.getType();
            if ("offline".equals(type)) {
                alarmV2.setName(alarm.getDeviceName() + "_离线");
                alarmV2.setType("设备离线");
            } else if ("scope".equals(type)) {
                alarmV2.setName(alarm.getDeviceName() + "_" + alarm.getAlarmJsonName());
                alarmV2.setType("范围告警");
            } else if ("change".equals(type)) {
                alarmV2.setName(alarm.getDeviceName() + "_" + alarm.getAlarmJsonName());
                alarmV2.setType("变动告警");
            }
            alarmV2.setStatus(alarm.getStatus().toString());
            if (alarm.getEndTs() != 0) {
                alarmV2.setEndTime(new Date(alarm.getEndTs()));
            }
            alarmV2.setLevel(alarm.getSeverity());
            if (StringUtils.isNotBlank(alarm.getAlarmJsonId())) {
                AttrAlarmJson attrAlarmJson = attrAlarmJsonMap.get(alarm.getAlarmJsonId());
                if (attrAlarmJson != null) {
                    alarmV2.setProperty(attrAlarmJson.getAttribute());
                }
            }

            resultList.add(alarmV2);
        }

        return resultList;
    }

    @Override
    public Object trendByType(String stationType, String projectId, Long start, Long end, String status, String level, TenantId tenantId) throws Exception {
        List<AlarmV2> list = this.findList(stationType, projectId, start, end, status, level, tenantId);
        // 统计类型
        List<String> typeList = list.stream().map(AlarmV2::getType).distinct().collect(Collectors.toList());

        Map<String, List<AlarmV2>> groupMap = new LinkedHashMap<>();
        // 初始化map
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        long temp = start;
        while (temp < end) {
            groupMap.put(dateFormat.format(new Date(temp)), new ArrayList<>());
            temp = temp + (24 * 60 * 60 * 1000);
        }

        // 统计数据
        for (AlarmV2 alarm : list) {
            List<AlarmV2> groupList = new ArrayList<>();
            if (groupMap.containsKey(dateFormat.format(alarm.getCreateTime()))) {
                groupList = groupMap.get(dateFormat.format(alarm.getCreateTime()));
            }
            groupList.add(alarm);

            groupMap.put(alarm.getType(), groupList);
        }

        // 返回曲线数据
        List<JSONObject> resultList = new ArrayList<>();
        for (Map.Entry<String, List<AlarmV2>> entry : groupMap.entrySet()) {
            String key = entry.getKey();
            Map<String, List<AlarmV2>> groupByType = new HashMap<>();
            List<AlarmV2> value = entry.getValue();
            if (value != null) {
                for (AlarmV2 alarm : value) {
                    List<AlarmV2> groupList = new ArrayList<>();
                    if (groupByType.containsKey(alarm.getType())) {
                        groupList = groupByType.get(alarm.getType());
                    }
                    groupList.add(alarm);
                    groupByType.put(alarm.getType(), groupList);
                }
            }

            // 设置数据
            JSONObject data = new JSONObject();
            data.put("ts", key);
            for (String type : typeList) {
                data.put(type, groupByType.get(type) == null ? 0 : groupByType.get(type).size());
            }
            resultList.add(data);
        }

        return resultList;
    }
}
