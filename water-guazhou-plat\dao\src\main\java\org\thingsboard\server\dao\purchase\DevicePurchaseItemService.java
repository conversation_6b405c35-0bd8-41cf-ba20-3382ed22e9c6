package org.thingsboard.server.dao.purchase;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.purchase.DevicePurchaseItem;
import org.thingsboard.server.dao.util.imodel.query.purchase.DevicePurchaseItemPageRequest;
import org.thingsboard.server.dao.util.imodel.query.purchase.DevicePurchaseItemSaveRequest;

import java.util.List;

public interface DevicePurchaseItemService {
    /**
     * 分页条件查询设备采购单条目
     *
     * @param request 分页查询条件
     * @return 分页查询结果
     */
    IPage<DevicePurchaseItem> findAllConditional(DevicePurchaseItemPageRequest request);

    /**
     * 增量修改
     *
     * @param entity 实体信息
     * @return 是否修改成功
     */
    boolean update(DevicePurchaseItem entity);

    /**
     * 删除
     *
     * @param id 唯一标识
     * @return 是否删除成功
     */
    boolean delete(String id);

    /**
     * 批量保存
     *
     * @param entities 实体列表
     * @return 保存好的数据
     */
    List<DevicePurchaseItem> saveAll(List<DevicePurchaseItemSaveRequest> entities);

    /**
     * 批量删除
     *
     * @param idList id列表
     * @return 是否成功
     */
    boolean deleteAll(List<String> idList);

    /**
     * 通过父级id删除所有
     *
     * @param mainId 唯一标识
     * @param idList 实体的id列表，为空列表或null时删除所有
     * @return 是否成功
     */
    boolean deleteByMainIdOnIdNotIn(String mainId, List<String> idList);

    /**
     * 通过父级id删除所有id未在指定列表中的条目，或是删除所有
     *
     * @param mainId 唯一标识
     * @param idList 实体的id列表，为空列表或null时删除所有
     * @return 是否成功
     */
    boolean removeAllByMainOnIdNotIn(String mainId, List<String> idList);

    /**
     * 提交询价，目前逻辑已修正为：有意向供应商时自动完成询价
     *
     * @param id 设备采购单条目id
     * @return 是否成功
     */
    @Deprecated
    boolean submitInquiry(String id);


}
