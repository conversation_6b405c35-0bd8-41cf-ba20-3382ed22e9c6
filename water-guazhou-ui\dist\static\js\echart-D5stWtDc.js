import"./index-r0dFAfgr.js";function p(o,s,t,e,i){return{color:["#318DFF","#FFB800","#FC2B2B"],grid:{left:e||80,right:i||80,top:60,bottom:40},legend:{width:"500",type:"scroll",textStyle:{fontSize:12}},tooltip:{trigger:"axis"},xAxis:{type:"category",data:o},yAxis:[{position:"left",type:"value",name:"供水量(m³)",splitNumber:5,axisLine:{show:!0,lineStyle:{types:"solid"}},axisLabel:{show:!0},splitLine:{lineStyle:{type:[5,10],dashOffset:5}}},{position:"right",type:"value",name:"",axisLine:{show:!0,lineStyle:{types:"solid"}},axisLabel:{show:!0},splitLine:{lineStyle:{type:[5,10],dashOffset:5}}}],series:[{name:"出口压力(Mpa)",smooth:!0,data:s,type:"line"},{name:"瞬时流量(m³/h)",smooth:!0,data:t,type:"line",yAxisIndex:1}]}}const r=(o,s,t,e,i,l)=>({color:["#318DFF","#43B530","#FC2B2B"],xAxis:{type:"category",data:t},grid:{left:i||50,right:l||50,top:50,bottom:50},legend:{width:"500",type:"scroll",textStyle:{fontSize:12}},tooltip:{trigger:"axis"},yAxis:[{position:"left",type:"value",name:"吨水能耗(kw.h/m³)",axisLine:{show:!0,lineStyle:{types:"solid"}},axisLabel:{show:!0,textStyle:{}},splitLine:{lineStyle:{type:[5,10],dashOffset:5}}}],series:[{data:e,type:"bar"}]});export{r as b,p as l};
