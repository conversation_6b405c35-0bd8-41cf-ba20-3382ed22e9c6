package org.thingsboard.server.dao.maintainCircuit.circuit;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.deviceManage.DeviceType;
import org.thingsboard.server.dao.model.sql.maintainCircuit.circuit.CircuitStandard;
import org.thingsboard.server.dao.sql.deviceType.DeviceTypeMapper;
import org.thingsboard.server.dao.sql.maintainCircuit.circuit.CircuitStandardMapper;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-08-24
 */
@Slf4j
@Service
@Transactional
public class CircuitStandardServiceImpl implements CircuitStandardService {

    @Autowired
    private CircuitStandardMapper circuitStandardMapper;

    @Autowired
    private DeviceTypeMapper deviceTypeMapper;

    @Override
    public PageData getList(String deviceTypeId, String keywords, int page, int size, String tenantId) {

        // 获取所有deviceTypeId
        List<String> deviceTypeSerialIds = null;
        if (StringUtils.isNotBlank(deviceTypeId)) {
            deviceTypeSerialIds = new ArrayList<>();
            DeviceType deviceType = deviceTypeMapper.selectById(deviceTypeId);
            if (deviceType != null) {
                deviceTypeSerialIds.add(deviceType.getSerialId());
                this.getAllDeviceTypeId(Collections.singletonList(deviceTypeId), deviceTypeSerialIds);
            } else {
                deviceTypeSerialIds.add("-");
            }
        }

        List<CircuitStandard> circuitStandardList = circuitStandardMapper.getList(deviceTypeSerialIds, keywords, page, size, tenantId);

        int count = circuitStandardMapper.getListCount(deviceTypeSerialIds, keywords, tenantId);
        return new PageData(count, circuitStandardList);
    }

    @Override
    public CircuitStandard save(CircuitStandard circuitStandard) {
        if (StringUtils.isBlank(circuitStandard.getId())) {
            circuitStandard.setCreateTime(new Date());
            circuitStandardMapper.insert(circuitStandard);
        } else {
            circuitStandardMapper.updateById(circuitStandard);
        }
        return circuitStandard;
    }

    @Override
    public IstarResponse delete(List<String> ids) {
        circuitStandardMapper.deleteBatchIds(ids);
        return IstarResponse.ok("删除成功");
    }

    // 设备类型树
    public void getAllDeviceTypeId(List<String> input, List<String> output) {
        QueryWrapper<DeviceType> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("parent_id", input);
        List<DeviceType> list = deviceTypeMapper.selectList(queryWrapper);
        if (list.size() > 0) {
            List<String> ids = list.stream().map(a -> a.getId()).collect(Collectors.toList());
            output.addAll(list.stream().map(a -> a.getSerialId()).collect(Collectors.toList()));
            this.getAllDeviceTypeId(ids, output);
        }
    }
}
