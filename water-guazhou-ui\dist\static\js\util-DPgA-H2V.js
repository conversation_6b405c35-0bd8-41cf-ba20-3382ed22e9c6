import{h}from"./Point-WxyopZva.js";const G=new h({esriGeometryPoint:"point",esriGeometryMultipoint:"multipoint",esriGeometryPolyline:"polyline",esriGeometryPolygon:"polygon",esriGeometryMultiPatch:"multipatch",mesh:"mesh"});function P(l){return G.toJSON(l)}function b(l,y,c){const t=[],e=[];let n=0,o=0;for(const i of l){const r=o;let s=i[0][0],u=i[0][1];t[o++]=s,t[o++]=u;let p=0;for(let m=1;m<i.length;++m){const a=s,f=u;s=i[m][0],u=i[m][1],p+=u*a-s*f,t[o++]=s,t[o++]=u}y(p/2),p>0?(r-n>0&&(c(n,r,t,e),n=r),e.length=0):p<0&&r-n>0?e.push(.5*(r-n)):o=r}o-n>0&&c(n,o,t,e)}function x(l){const{bandCount:y,attributeTable:c,colormap:t,pixelType:e}=l.raster.rasterInfo;return y===1&&(c!=null||t!=null||e==="u8"||e==="s8")}export{P as e,b as n,x as r};
