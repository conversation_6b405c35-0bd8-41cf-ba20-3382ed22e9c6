<!-- 分区进出水报表 -->
<template>
  <TreeBox>
    <template #tree>
      <SLTree
        ref="refTree"
        :tree-data="TreeData"
      ></SLTree>
    </template>
    <CardSearch
      ref="refSearch"
      :config="SearchConfig"
    ></CardSearch>
    <CardTable
      ref="refTable"
      class="card-table"
      :config="TableConfig"
    ></CardTable>
  </TreeBox>
</template>
<script lang="ts" setup>
import { GetDmaPartitionInOutWaterReport } from '@/api/mapservice/dma'
import { ICardTableIns, ISearchIns } from '@/components/type'
import { usePartition } from '@/hooks/arcgis'
import {
  formatterDate,
  formatterMonth,
  formatterYear
} from '@/utils/GlobalHelper'
import { SLMessage } from '@/utils/Message'

const refTable = ref<ICardTableIns>()
const refSearch = ref<ISearchIns>()
const TreeData = reactive<SLTreeConfig>({
  data: [],
  title: '选择分区',
  expandOnClickNode: false,
  treeNodeHandleClick: async (data: NormalOption) => {
    if (TreeData.currentProject !== data) {
      // TreeData.loading = true
      TreeData.currentProject = data
      refreshData()
    }
  }
})

const handleHidden = (params, query, config) => (config.hidden = params.type !== config.field)
// 列表模式搜索配置
const SearchConfig = reactive<ISearch>({
  defaultParams: {
    type: 'month',
    year: moment().format(formatterYear),
    month: moment().format(formatterMonth),
    day: [moment().format(formatterDate), moment().format(formatterDate)]
  },
  filters: [
    {
      type: 'select',
      field: 'type',
      clearable: false,
      options: [
        { label: '按年', value: 'year' },
        { label: '按月', value: 'month' },
        { label: '按时间段', value: 'day' }
      ],
      label: '选择方式'
    },
    {
      handleHidden,
      type: 'year',
      label: '',
      field: 'year',
      clearable: false,
      disabledDate(date) {
        return new Date() < date
      }
    },
    {
      handleHidden,
      type: 'month',
      label: '',
      field: 'month',
      clearable: false,
      format: formatterMonth,
      disabledDate(date) {
        return new Date() < date
      }
    },
    {
      handleHidden,
      type: 'daterange',
      label: '',
      field: 'day',
      clearable: false,
      disabledDate(date) {
        return new Date() < date
      }
    },
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          iconifyIcon: 'ep:search',
          click: () => refreshData()
        },
        {
          perm: true,
          text: '重置',
          type: 'default',
          iconifyIcon: 'ep:refresh',
          click: () => {
            refSearch.value?.resetForm()
            refreshData()
          }
        },
        {
          perm: true,
          text: '导出',
          type: 'primary',
          iconifyIcon: 'ep:download',
          click: () => {
            refTable.value?.exportTable()
          }
        }
      ]
    }
  ]
})
const initTableColumns = (
  subColumns: IFormTableColumn[] = []
): IFormTableColumn[] => {
  return [
    { minWidth: 160, label: '日期', prop: 'date', align: 'center' },
    ...subColumns
  ]
}
// 列表
const TableConfig = reactive<ITable>({
  dataList: [],
  columns: initTableColumns([]),
  pagination: {
    hide: true
  }
})

// 数据获取
const refreshData = async () => {
  if (!TreeData.currentProject) {
    SLMessage.warning('请先选择分区')
    return
  }
  try {
    const query = refSearch.value?.queryParams || {}
    const res = await GetDmaPartitionInOutWaterReport({
      partitionId: TreeData.currentProject?.value,
      type: query.type,
      date:
        query.type === 'month'
          ? query.month
          : query.type === 'year'
            ? query.year
            : undefined,
      start: query.day?.[0],
      end: query.day?.[1]
    })

    const data = res.data.data || {}
    const subColumns: IFormTableColumn[] = data.header?.map(item => {
      return {
        label: item,
        prop: item,
        minWidth: 160,
        formatter(row, value) {
          return value?.toFixed(1)
        }
      }
    }) || []
    const rows = data?.data || []
    const total = {
      date: '合计'
    }
    subColumns.map(item => {
      total[item.prop] = rows.reduce((prev, cur) => cur[item.prop] + prev, 0)
    })
    rows.push(total)
    TableConfig.columns = initTableColumns(subColumns)
    TableConfig.dataList = rows
  } catch (error) {
    console.log(error)
  }
  TableConfig.loading = false
}
const partition = usePartition()
onMounted(async () => {
  await partition.getTree()
  TreeData.data = partition.Tree.value
  TreeData.currentProject = TreeData.data[0]
  refreshData()
})
</script>
<style lang="scss" scoped>
.wrapper-content {
  height: 100%;
}
</style>
