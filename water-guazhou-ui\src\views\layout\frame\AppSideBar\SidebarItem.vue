<template>
  <template v-if="!item.hidden && !item.meta?.hidden">
    <el-sub-menu
      v-if="validChildren.length"
      :index="item.path"
    >
      <template #title>
        <i v-if="item.meta?.icon" :class="item.meta.icon"></i>
        <span>{{ item.meta?.title }}</span>
      </template>
      <sidebar-item
        v-for="child in validChildren"
        :key="child.path"
        :item="child"
      />
    </el-sub-menu>
    <el-menu-item
      v-else-if="item.path && item.path !== '/'"
      :index="item.path"
    >
      <i v-if="item.meta?.icon" :class="item.meta.icon"></i>
      <span>{{ item.meta?.title }}</span>
    </el-menu-item>
  </template>
</template>

<script lang="ts" setup>
import { computed } from 'vue';

const props = defineProps({
  item: {
    type: Object,
    required: true
  }
});

// 只保留有效的 children，且不能是自身，且不能 hidden
const validChildren = computed(() => {
  if (!props.item.children) return [];
  return props.item.children.filter(
    child =>
      child &&
      child.path &&
      child.path !== props.item.path &&
      !child.hidden &&
      !child.meta?.hidden
  );
});

// 添加默认导出
defineOptions({
  name: 'SidebarItem'
});
</script>

<style lang="scss" scoped>
.el-menu-item {
  &.submenu-title-noDropdown {
    padding-left: 10px !important;
    &.is-active {
      background-color: #dde4f4 !important;
    }
  }
}

.el-sub-menu {
  .el-menu-item {
    min-width: 180px;
    &.is-active {
      background-color: #dde4f4 !important;
    }
  }
}

i {
  margin-right: 4px;
  font-size: 18px;
}
</style>
