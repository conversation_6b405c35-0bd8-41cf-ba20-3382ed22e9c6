package org.thingsboard.server.dao.model.sql.shuiwu.assets;

import lombok.Data;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.List;

/**
 * 设备台账
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2021-10-19
 */
@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.ASSETS_ACCOUNT_TABLE)
public class AssetsAccountEntity {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.ASSETS_ACCOUNT_DEVICE_ID)
    private String deviceId;

    @Column(name = ModelConstants.ASSETS_ACCOUNT_DEVICE_NO)
    private String deviceNo;

    @Column(name = ModelConstants.ASSETS_ACCOUNT_DEVICE_NAME)
    private String deviceName;

    @Column(name = ModelConstants.ASSETS_ACCOUNT_UNIT)
    private String unit;

    @Column(name = ModelConstants.ASSETS_ACCOUNT_DEVICE_TYPE)
    private String deviceType;

    @Column(name = ModelConstants.ASSETS_ACCOUNT_SPECIFICATION_MODEL)
    private String specificationModel;

    @Column(name = ModelConstants.ASSETS_ACCOUNT_BRAND)
    private String brand;

    @Column(name = ModelConstants.ASSETS_ACCOUNT_SUPPLIER)
    private String supplier;

    @Column(name = ModelConstants.ASSETS_ACCOUNT_DEVICE_SOURCE)
    private String deviceSource;

    @Column(name = ModelConstants.ASSETS_ACCOUNT_PURCHASE_AMOUNT)
    private BigDecimal purchaseAmount;

    @Column(name = ModelConstants.ASSETS_ACCOUNT_PURCHASE_TIME)
    private Long purchaseTime;

    @Column(name = ModelConstants.ASSETS_ACCOUNT_WARRANTY_TIME)
    private Long warrantyTime;

    @Column(name = ModelConstants.ASSETS_ACCOUNT_ENABLE_TIME)
    private Long enableTime;

    @Column(name = ModelConstants.ASSETS_ACCOUNT_EXPECT_SCRAP_TIME)
    private Long expectScrapTime;

    @Column(name = ModelConstants.ASSETS_ACCOUNT_DEVICE_STATUS)
    private String deviceStatus;

    @Column(name = ModelConstants.ASSETS_ACCOUNT_DEVICE_GRADE)
    private String deviceGrade;

    @Column(name = ModelConstants.ASSETS_ACCOUNT_DIRECTOR)
    private String director;

    @Column(name = ModelConstants.ASSETS_ACCOUNT_PROJECT_ID)
    private String projectId;

    @Column(name = ModelConstants.ASSETS_ACCOUNT_DEVICE_POSITION)
    private String devicePosition;

    @Column(name = ModelConstants.ASSETS_ACCOUNT_LOCATION)
    private String location;

    @Column(name = ModelConstants.ASSETS_ACCOUNT_REMARK)
    private String remark;

    @Column(name = ModelConstants.ASSETS_ACCOUNT_DEPRECIATION_METHOD)
    private String depreciationMethod;

    @Column(name = ModelConstants.ASSETS_ACCOUNT_USE_LIFE)
    private Integer useLife;

    @Column(name = ModelConstants.ASSETS_ACCOUNT_ORIGIN_VALUE)
    private BigDecimal originValue;

    @Column(name = ModelConstants.ASSETS_ACCOUNT_NET_RESIDUAL_RATE)
    private Double netResidualRate;

    @Column(name = ModelConstants.ASSETS_ACCOUNT_NET_VALUE)
    private BigDecimal netValue;

    @Column(name = ModelConstants.ASSETS_ACCOUNT_DEPRECIATION_AMOUNT_MONTH)
    private BigDecimal depreciationAmountMonth;

    @Column(name = ModelConstants.ASSETS_ACCOUNT_TEXT1)
    private String text1;

    @Column(name = ModelConstants.ASSETS_ACCOUNT_TEXT2)
    private String text2;

    @Column(name = ModelConstants.ASSETS_ACCOUNT_TEXT3)
    private String text3;

    @Column(name = ModelConstants.ASSETS_ACCOUNT_TEXT4)
    private String text4;

    @Column(name = ModelConstants.ASSETS_ACCOUNT_TEXT5)
    private String text5;

    @Column(name = ModelConstants.ASSETS_ACCOUNT_TEXT6)
    private String text6;

    @Column(name = ModelConstants.ASSETS_ACCOUNT_TEXT7)
    private String text7;

    @Column(name = ModelConstants.ASSETS_ACCOUNT_TEXT8)
    private String text8;

    @Column(name = ModelConstants.ASSETS_ACCOUNT_NUMBER1)
    private Double number1;

    @Column(name = ModelConstants.ASSETS_ACCOUNT_NUMBER2)
    private Double number2;

    @Column(name = ModelConstants.ASSETS_ACCOUNT_DATE1)
    private Long date1;

    @Column(name = ModelConstants.ASSETS_ACCOUNT_DATE2)
    private Long date2;

    @Column(name = ModelConstants.ASSETS_ACCOUNT_IMGS)
    private String imgs;

    @Column(name = ModelConstants.ASSETS_ACCOUNT_FILE_IDS)
    private String fileIds;

    @Column(name = ModelConstants.ASSETS_ACCOUNT_SUB_DEVICE_IDS)
    private String subDeviceIds;

    @Column(name = ModelConstants.ASSETS_ACCOUNT_PARENT_DEVICE_IDS)
    private String parentDeviceIds;

    @Column(name = ModelConstants.ASSETS_ACCOUNT_SPARE_PART_IDS)
    private String sparePartIds;

    @Column(name = ModelConstants.ASSETS_ACCOUNT_CREATE_TIME)
    private Long createTime;

    @Column(name = ModelConstants.ASSETS_ACCOUNT_UPDATE_TIME)
    private Long updateTime;

    @Column(name = ModelConstants.ASSETS_ACCOUNT_TENANT_ID)
    private String tenantId;

    private transient String projectName;

    private transient List<AssetsFileEntity> assetsFileList;

    private transient List<AssetsAccountEntity> subAssetsAccountList;

    private transient List<AssetsAccountEntity> parentAssetsAccountList;

    // 关联备件
    private transient List<AssetsAccountComponentEntity> componentList;

}
