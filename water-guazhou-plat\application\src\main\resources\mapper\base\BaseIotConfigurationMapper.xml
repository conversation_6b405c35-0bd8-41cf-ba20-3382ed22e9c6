<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.base.BaseIotConfigurationMapper">
    
    <resultMap type="org.thingsboard.server.dao.model.sql.base.BaseIotConfiguration" id="BaseIotConfigurationResult">
        <result property="id"    column="id"    />
        <result property="ipAddress"    column="ip_address"    />
        <result property="listenerProtocol"    column="listener_protocol"    />
        <result property="listenerPort"    column="listener_port"    />
        <result property="nodeName"    column="node_name"    />
        <result property="clusterDiscovery"    column="cluster_discovery"    />
    </resultMap>

    <sql id="selectBaseIotConfigurationVo">
        select id, ip_address, listener_protocol, listener_port, node_name, cluster_discovery from base_iot_configuration
    </sql>

    <select id="selectBaseIotConfigurationList" parameterType="org.thingsboard.server.dao.model.sql.base.BaseIotConfiguration" resultMap="BaseIotConfigurationResult">
        <include refid="selectBaseIotConfigurationVo"/>
        <where>  
            <if test="ipAddress != null  and ipAddress != ''"> and ip_address = #{ipAddress}</if>
            <if test="listenerProtocol != null  and listenerProtocol != ''"> and listener_protocol = #{listenerProtocol}</if>
            <if test="listenerPort != null  and listenerPort != ''"> and listener_port = #{listenerPort}</if>
            <if test="nodeName != null  and nodeName != ''"> and node_name like concat('%', #{nodeName}, '%')</if>
            <if test="clusterDiscovery != null  and clusterDiscovery != ''"> and cluster_discovery = #{clusterDiscovery}</if>
        </where>
    </select>
    
    <select id="selectBaseIotConfigurationById" parameterType="String" resultMap="BaseIotConfigurationResult">
        <include refid="selectBaseIotConfigurationVo"/>
        where id = #{id}
    </select>

    <insert id="insertBaseIotConfiguration" parameterType="org.thingsboard.server.dao.model.sql.base.BaseIotConfiguration">
        insert into base_iot_configuration
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="ipAddress != null">ip_address,</if>
            <if test="listenerProtocol != null">listener_protocol,</if>
            <if test="listenerPort != null">listener_port,</if>
            <if test="nodeName != null">node_name,</if>
            <if test="clusterDiscovery != null">cluster_discovery,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="ipAddress != null">#{ipAddress},</if>
            <if test="listenerProtocol != null">#{listenerProtocol},</if>
            <if test="listenerPort != null">#{listenerPort},</if>
            <if test="nodeName != null">#{nodeName},</if>
            <if test="clusterDiscovery != null">#{clusterDiscovery},</if>
         </trim>
    </insert>

    <update id="updateBaseIotConfiguration" parameterType="org.thingsboard.server.dao.model.sql.base.BaseIotConfiguration">
        update base_iot_configuration
        <trim prefix="SET" suffixOverrides=",">
            <if test="ipAddress != null">ip_address = #{ipAddress},</if>
            <if test="listenerProtocol != null">listener_protocol = #{listenerProtocol},</if>
            <if test="listenerPort != null">listener_port = #{listenerPort},</if>
            <if test="nodeName != null">node_name = #{nodeName},</if>
            <if test="clusterDiscovery != null">cluster_discovery = #{clusterDiscovery},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBaseIotConfigurationById" parameterType="String">
        delete from base_iot_configuration where id = #{id}
    </delete>

    <delete id="deleteBaseIotConfigurationByIds" parameterType="String">
        delete from base_iot_configuration where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>