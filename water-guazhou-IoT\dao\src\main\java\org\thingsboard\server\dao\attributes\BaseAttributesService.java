/**
 * Copyright © 2016-2019 The Thingsboard Authors
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.attributes;

import com.google.common.collect.Lists;
import com.google.common.util.concurrent.Futures;
import com.google.common.util.concurrent.ListenableFuture;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.id.EntityId;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.kv.AttributeKeyKvEntry;
import org.thingsboard.server.common.data.kv.AttributeKvEntry;
import org.thingsboard.server.dao.exception.IncorrectParameterException;
import org.thingsboard.server.dao.service.Validator;

import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.ExecutionException;

import static org.thingsboard.server.common.data.CacheConstants.ATTRIBUTE_CACHE;
import static org.thingsboard.server.common.data.CacheConstants.DEVICE_CACHE;

/**
 * <AUTHOR> Shvayka
 */
@Service
public class BaseAttributesService implements AttributesService {

    @Autowired
    private AttributesDao attributesDao;

    @Override
    //@Cacheable(cacheNames = ATTRIBUTE_CACHE, key = "{#entityId, #scope,#attributeKey}")
    public ListenableFuture<Optional<AttributeKvEntry>> find(TenantId tenantId, EntityId entityId, String scope, String attributeKey) {
        validate(entityId, scope);
        Validator.validateString(attributeKey, "Incorrect attribute key " + attributeKey);
        return attributesDao.find(tenantId, entityId, scope, attributeKey);
    }

    @Override
    @Cacheable(cacheNames = ATTRIBUTE_CACHE, key = "{#entityId, #scope,#attributeKey}")
    public ListenableFuture<AttributeKvEntry> find(EntityId entityId, String scope, String attributeKey) {
        validate(entityId, scope);
        Validator.validateString(attributeKey, "Incorrect attribute key " + attributeKey);
        return attributesDao.find(entityId, scope, attributeKey);
    }

    @Override
    public ListenableFuture<List<AttributeKvEntry>> find(TenantId tenantId, EntityId entityId, String scope, Collection<String> attributeKeys) {
        validate(entityId, scope);
        attributeKeys.forEach(attributeKey -> Validator.validateString(attributeKey, "Incorrect attribute key " + attributeKey));
        List<ListenableFuture<AttributeKvEntry>> futures = Lists.newArrayListWithExpectedSize(attributeKeys.size());
        for (String key : attributeKeys) {
            ListenableFuture<AttributeKvEntry> attributeKeyKvEntryListenableFuture = find(entityId, scope, key);
            try {
                if (attributeKeyKvEntryListenableFuture != null && attributeKeyKvEntryListenableFuture.get() != null) {
                    futures.add(attributeKeyKvEntryListenableFuture);
                }
            } catch (InterruptedException e) {
                e.printStackTrace();
            } catch (ExecutionException e) {
                e.printStackTrace();
            }
        }
        return Futures.allAsList(futures);
    }

//    @Override
//    public ListenableFuture<List<AttributeKvEntry>> find(TenantId tenantId, Collection<EntityId> entityIds, String scope, String attributeKey) {
//        return attributesDao.find(tenantId, entityIds, scope, attributeKey);
//    }

    @Override
    public ListenableFuture<List<AttributeKvEntry>> findAll(TenantId tenantId, EntityId entityId, String scope) {
        validate(entityId, scope);
        return attributesDao.findAll(tenantId, entityId, scope);
    }

    @Override
    @CacheEvict(cacheNames = ATTRIBUTE_CACHE, allEntries = true)
    public ListenableFuture<List<Void>> save(TenantId tenantId, EntityId entityId, String scope, List<AttributeKvEntry> attributes) {
        return save(entityId, scope, attributes);
    }

    @Override
    @CacheEvict(cacheNames = ATTRIBUTE_CACHE, allEntries = true)
    public ListenableFuture<List<Void>> save(EntityId entityId, String scope, List<AttributeKvEntry> attributes) {
        validate(entityId, scope);
        attributes.forEach(attribute -> validate(attribute));
        List<ListenableFuture<Void>> futures = Lists.newArrayListWithExpectedSize(attributes.size());
        for (AttributeKvEntry attribute : attributes) {
            futures.add(save(entityId, scope, attribute));
        }
        return Futures.allAsList(futures);
    }

    @Override
    @CacheEvict(cacheNames = ATTRIBUTE_CACHE, allEntries = true)
    public ListenableFuture<Void> save(EntityId entityId, String scope, AttributeKvEntry attribute) {
        return (attributesDao.save(entityId, scope, attribute));
    }


    @Override
    @CacheEvict(cacheNames = ATTRIBUTE_CACHE,allEntries = true)
    public ListenableFuture<List<Void>> removeAll(TenantId tenantId, EntityId entityId, String scope, List<String> keys) {
        validate(entityId, scope);
        return attributesDao.removeAll(tenantId, entityId, scope, keys);
    }

    @Override
    public List<AttributeKeyKvEntry> findAll(String scope) {
        return attributesDao.findAll(scope);
    }

    private static void validate(EntityId id, String scope) {
        Validator.validateId(id.getId(), "Incorrect id " + id);
        Validator.validateString(scope, "Incorrect scope " + scope);
    }

    private static void validate(AttributeKvEntry kvEntry) {
        if (kvEntry == null) {
            throw new IncorrectParameterException("Key value entry can't be null");
        } else if (kvEntry.getDataType() == null) {
            throw new IncorrectParameterException("Incorrect kvEntry. Data type can't be null");
        } else {
            Validator.validateString(kvEntry.getKey(), "Incorrect kvEntry. Key can't be empty");
            Validator.validatePositiveNumber(kvEntry.getLastUpdateTs(), "Incorrect last update ts. Ts should be positive");
        }
    }

}
