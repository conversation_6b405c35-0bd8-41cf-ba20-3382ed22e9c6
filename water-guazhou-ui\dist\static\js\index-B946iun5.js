import{d as X,r as F,c,u as Z,o as ee,g as C,n as h,q as t,F as o,p as y,G as u,bo as te,h as le,bh as ae,i as oe,an as ne,y as L,x as d,J as re,H as se,I as ie,c2 as ue,K as de,N as pe,O as me,P as ge,bz as ce,bK as fe,L as ve,br as ye,C as be}from"./index-r0dFAfgr.js";import{f as _e}from"./DateFormatter-Bm9a68Ax.js";import{g as Ve,d as E,u as De,s as Ue}from"./assay-DCQODP3D.js";import{u as Ce}from"./fileUpload-CoTDqaci.js";const we={class:"water-quality-test"},ke={class:"card-header"},Ne={class:"header-buttons"},xe={class:"pagination"},Fe={class:"el-upload__tip"},he={key:0,style:{color:"#67C23A"}},Le={class:"dialog-footer"},Ee=X({__name:"index",setup(Se){const r=F({pageNum:1,pageSize:10,samplingLocation:"",reportName:"",testingUnit:"",testDate:null,type:0}),b=c([]),V=c(0),D=c(!1),p=c([]),S=l=>{p.value=l.map(e=>e.id)},n=F({id:"",samplingLocation:"",reportName:"",testingUnit:"",testResults:"",testDate:null,reportFile:"",remark:"",type:0}),z={samplingLocation:[{required:!0,message:"请输入采样地点",trigger:"blur"}],reportName:[{required:!0,message:"请输入报告名称",trigger:"blur"}],testingUnit:[{required:!0,message:"请输入检测单位",trigger:"blur"}],testResults:[{required:!0,message:"请输入检测结果",trigger:"blur"}],testDate:[{required:!0,message:"请选择检测月份",trigger:"change"}]},v=c(!1),U=c(""),_=c();c({"X-Authorization":"Bearer "+Z().token});const f=async()=>{D.value=!0;try{const l=await Ve(r);console.log("API返回数据:",l),l.data&&l.data.data&&l.data.data.data?(b.value=Array.isArray(l.data.data.data)?l.data.data.data:[],V.value=l.data.data.total||0,console.log("表格数据:",b.value)):(console.warn("返回数据格式不正确:",l),b.value=[],V.value=0)}catch(l){console.error("获取数据失败:",l),b.value=[],V.value=0}finally{D.value=!1}},w=()=>{r.pageNum=1,p.value=[],f()},q=()=>{r.samplingLocation="",r.reportName="",r.testingUnit="",r.testDate=null,p.value=[],w()},R=()=>{U.value="新增化验记录",v.value=!0},B=l=>{U.value="编辑化验记录",Object.assign(n,{...l,testDate:l.testDate?Number(l.testDate):null}),v.value=!0},T=l=>{L.confirm("确认删除该记录吗？","提示",{type:"warning"}).then(async()=>{try{await E([l.id]),d.success("删除成功"),f()}catch(e){console.error(e),d.error("删除失败")}})},A=()=>{if(p.value.length===0){d.warning("请选择要删除的记录");return}L.confirm(`确认删除选中的 ${p.value.length} 条记录吗？`,"提示",{type:"warning"}).then(async()=>{try{await E(p.value),d.success("批量删除成功"),p.value=[],f()}catch(l){console.error(l),d.error("批量删除失败")}})},I=l=>{window.open(l.reportFile)},P=l=>!0,Q=async l=>{const{file:e}=l;try{const s=await Ce(e,"file");n.reportFile=s,d.success("文件上传成功")}catch(s){console.error("文件上传失败:",s),d.error("文件上传失败")}},M=async()=>{_.value&&await _.value.validate(async l=>{if(l)try{const e={...n,testDate:n.testDate?String(n.testDate):String(Date.now()),reportFile:typeof n.reportFile=="string"?n.reportFile:"",type:0};if(!e.reportFile&&!n.id){d.warning("请先上传化验报告文件");return}n.id?(await De(e),d.success("修改成功")):(await Ue(e),d.success("新增成功")),v.value=!1,f()}catch(e){console.error("保存失败:",e),d.error("保存失败")}})},$=()=>{_.value&&_.value.resetFields(),Object.assign(n,{id:"",samplingLocation:"",reportName:"",testingUnit:"",testResults:"",testDate:null,reportFile:"",remark:"",type:0});const l=document.querySelector(".upload-demo .el-upload__input");l&&(l.value="")},W=l=>{r.pageSize=l,p.value=[],f()},Y=l=>{r.pageNum=l,p.value=[],f()};return ee(()=>{f()}),(l,e)=>{const s=re,m=se,i=ie,k=ue,N=de,g=pe,j=me,O=ge,K=ce,G=fe,H=ve,J=ye;return C(),h("div",we,[t(K,{class:"box-card"},{header:o(()=>[y("div",ke,[e[16]||(e[16]=y("span",null,"化验记录",-1)),y("div",Ne,[t(s,{type:"primary",onClick:R},{default:o(()=>e[14]||(e[14]=[u("新增记录")])),_:1}),t(s,{type:"danger",disabled:p.value.length===0,onClick:A},{default:o(()=>e[15]||(e[15]=[u("批量删除")])),_:1},8,["disabled"])])])]),default:o(()=>[t(N,{model:r,ref:"queryForm",inline:!0,class:"search-form"},{default:o(()=>[t(i,{label:"采样地点",prop:"samplingLocation"},{default:o(()=>[t(m,{modelValue:r.samplingLocation,"onUpdate:modelValue":e[0]||(e[0]=a=>r.samplingLocation=a),placeholder:"请输入采样地点",clearable:""},null,8,["modelValue"])]),_:1}),t(i,{label:"报告名称",prop:"reportName"},{default:o(()=>[t(m,{modelValue:r.reportName,"onUpdate:modelValue":e[1]||(e[1]=a=>r.reportName=a),placeholder:"请输入报告名称",clearable:""},null,8,["modelValue"])]),_:1}),t(i,{label:"检测单位",prop:"testingUnit"},{default:o(()=>[t(m,{modelValue:r.testingUnit,"onUpdate:modelValue":e[2]||(e[2]=a=>r.testingUnit=a),placeholder:"请输入检测单位",clearable:""},null,8,["modelValue"])]),_:1}),t(i,{label:"检测月份",prop:"testDate"},{default:o(()=>[t(k,{modelValue:r.testDate,"onUpdate:modelValue":e[3]||(e[3]=a=>r.testDate=a),type:"month",placeholder:"选择月份","value-format":"x"},null,8,["modelValue"])]),_:1}),t(i,null,{default:o(()=>[t(s,{type:"primary",onClick:w},{default:o(()=>e[17]||(e[17]=[u("搜索")])),_:1}),t(s,{onClick:q},{default:o(()=>e[18]||(e[18]=[u("重置")])),_:1})]),_:1})]),_:1},8,["model"]),te((C(),le(j,{data:b.value,style:{width:"100%"},onSelectionChange:S},{default:o(()=>[t(g,{type:"selection",width:"55"}),t(g,{prop:"samplingLocation",label:"采样地点","min-width":"120"}),t(g,{prop:"reportName",label:"报告名称","min-width":"120"}),t(g,{prop:"testingUnit",label:"检测单位","min-width":"120"}),t(g,{prop:"testResults",label:"检测结果","min-width":"120"}),t(g,{prop:"testDate",label:"检测月份","min-width":"120"},{default:o(({row:a})=>[u(ae(a.testDate?oe(_e)(Number(a.testDate),"YYYY-MM"):""),1)]),_:1}),t(g,{prop:"reportFile",label:"报告附件","min-width":"120"},{default:o(({row:a})=>[t(s,{type:"text",onClick:x=>I(a)},{default:o(()=>e[19]||(e[19]=[u("下载")])),_:2},1032,["onClick"])]),_:1}),t(g,{label:"操作",width:"150"},{default:o(({row:a})=>[t(s,{type:"text",onClick:x=>B(a)},{default:o(()=>e[20]||(e[20]=[u("编辑")])),_:2},1032,["onClick"]),t(s,{type:"text",onClick:x=>T(a)},{default:o(()=>e[21]||(e[21]=[u("删除")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[J,D.value]]),y("div",xe,[t(O,{"current-page":r.pageNum,"onUpdate:currentPage":e[4]||(e[4]=a=>r.pageNum=a),"page-size":r.pageSize,"onUpdate:pageSize":e[5]||(e[5]=a=>r.pageSize=a),total:V.value,onSizeChange:W,onCurrentChange:Y,layout:"total, sizes, prev, pager, next, jumper"},null,8,["current-page","page-size","total"])])]),_:1}),t(H,{title:U.value,modelValue:v.value,"onUpdate:modelValue":e[13]||(e[13]=a=>v.value=a),width:"500px",onClose:$},{footer:o(()=>[y("span",Le,[t(s,{onClick:e[12]||(e[12]=a=>v.value=!1)},{default:o(()=>e[24]||(e[24]=[u("取消")])),_:1}),t(s,{type:"primary",onClick:M},{default:o(()=>e[25]||(e[25]=[u("确定")])),_:1})])]),default:o(()=>[t(N,{ref_key:"formRef",ref:_,model:n,rules:z,"label-width":"100px"},{default:o(()=>[t(i,{label:"采样地点",prop:"samplingLocation"},{default:o(()=>[t(m,{modelValue:n.samplingLocation,"onUpdate:modelValue":e[6]||(e[6]=a=>n.samplingLocation=a),placeholder:"请输入采样地点"},null,8,["modelValue"])]),_:1}),t(i,{label:"报告名称",prop:"reportName"},{default:o(()=>[t(m,{modelValue:n.reportName,"onUpdate:modelValue":e[7]||(e[7]=a=>n.reportName=a),placeholder:"请输入报告名称"},null,8,["modelValue"])]),_:1}),t(i,{label:"检测单位",prop:"testingUnit"},{default:o(()=>[t(m,{modelValue:n.testingUnit,"onUpdate:modelValue":e[8]||(e[8]=a=>n.testingUnit=a),placeholder:"请输入检测单位"},null,8,["modelValue"])]),_:1}),t(i,{label:"检测结果",prop:"testResults"},{default:o(()=>[t(m,{modelValue:n.testResults,"onUpdate:modelValue":e[9]||(e[9]=a=>n.testResults=a),placeholder:"请输入检测结果"},null,8,["modelValue"])]),_:1}),t(i,{label:"检测月份",prop:"testDate"},{default:o(()=>[t(k,{modelValue:n.testDate,"onUpdate:modelValue":e[10]||(e[10]=a=>n.testDate=a),type:"month",placeholder:"选择月份","value-format":"x"},null,8,["modelValue"])]),_:1}),t(i,{label:"化验报告",prop:"reportFile"},{default:o(()=>[t(G,{class:"upload-demo","http-request":Q,"before-upload":P,limit:1},{tip:o(()=>[y("div",Fe,[e[23]||(e[23]=u(" 请上传化验报告文件 ")),n.reportFile?(C(),h("span",he," (已上传) ")):ne("",!0)])]),default:o(()=>[t(s,{type:"primary"},{default:o(()=>e[22]||(e[22]=[u("点击上传")])),_:1})]),_:1})]),_:1}),t(i,{label:"备注",prop:"remark"},{default:o(()=>[t(m,{modelValue:n.remark,"onUpdate:modelValue":e[11]||(e[11]=a=>n.remark=a),type:"textarea",placeholder:"请输入备注"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["title","modelValue"])])}}}),Te=be(Ee,[["__scopeId","data-v-436a972c"]]);export{Te as default};
