import{d as S,M as w,a6 as B,c as i,r as g,am as V,bF as u,s as q,o as A,ay as E,bo as M,i as l,g as N,n as P,q as y,p as R,j,bB as z,br as L,al as T,C as F}from"./index-r0dFAfgr.js";import{_ as G}from"./Search-NSrhrIa_.js";import{l as W}from"./echart-DxEZmJvB.js";import{g as X}from"./flowMonitoring-DtJlPj0G.js";const $={class:""},H=S({__name:"comparedSame",props:{stationName:{},stationId:{}},setup(k){const{$messageWarning:b}=w(),x=B(),d=i(!1),m=i(),r=k,h=i(),_=i(),v=g({chartOption:null});V(()=>r.stationId,()=>{console.log(r.stationId),p()});const C=g({defaultParams:{date:u().format()},filters:[{type:"month",label:"日期",field:"date",width:"300px"},{type:"btn-group",btns:[{perm:!0,text:"查询",svgIcon:q(T),click:()=>{r.stationId?p():b("请选择监测点")}}]}]}),I=a=>{var o,s,c;const e=W();e.series=[];let t=[];for(const n in a){const O=(o=a[n])==null?void 0:o.map(f=>f.value);t=t.length===0?(s=a[n])==null?void 0:s.map(f=>f.ts):t;const D={name:n,smooth:!0,data:O,type:"line",markPoint:{data:[{type:"max",name:"最大值"},{type:"min",name:"最小值"}]},markLine:{data:[{type:"average",name:"平均值"}]}};e.series.push(D),e.yAxis[0].name="流量(m³)",e.xAxis.data=t}(c=m.value)==null||c.clear(),z(()=>{x.listenTo(_.value,()=>{var n;v.chartOption=e,(n=m.value)==null||n.resize()})})},p=async()=>{var s,c;d.value=!0;const a=((s=h.value)==null?void 0:s.queryParams)||{},e={start:u(a.date).startOf("month").valueOf(),end:u(a.date).endOf("month").valueOf(),stationId:r.stationId,type:"1"},o=(c=(await X(e)).data)==null?void 0:c.data;I(o),d.value=!1};return A(()=>{r.stationId&&p()}),(a,e)=>{const t=G,o=E("VChart"),s=L;return M((N(),P("div",$,[y(t,{ref_key:"cardSearch",ref:h,config:l(C)},null,8,["config"]),R("div",{ref_key:"agriEcoDev",ref:_,class:"card-ehcarts"},[y(o,{ref_key:"refChart",ref:m,class:"card-ehcarts",theme:l(j)().isDark?"dark":"",option:l(v).chartOption},null,8,["theme","option"])],512)])),[[s,l(d)]])}}}),Y=F(H,[["__scopeId","data-v-04c5e2bb"]]);export{Y as default};
