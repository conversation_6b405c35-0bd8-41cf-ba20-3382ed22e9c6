package org.thingsboard.server.dao.smartService.call;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.smartService.call.CallIVRLog;
import org.thingsboard.server.dao.sql.smartService.call.CallIVRLogMapper;

import java.util.List;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-10-27
 */
@Slf4j
@Service
@Transactional
public class CallIVRLogServiceImpl implements CallIVRLogService {
    @Autowired
    private CallIVRLogMapper callIVRLogMapper;

    @Override
    public PageData getList(String phone, Long startTime, Long endTime, int page, int size, String tenantId) {

        List<CallIVRLog> callIVRLogs = callIVRLogMapper.getList(phone, startTime, endTime, page, size);

        int total = callIVRLogMapper.getListCount(phone, startTime, endTime);

        return new PageData(total, callIVRLogs);

    }


    @Override
    public int delete(List<String> ids) {
        return callIVRLogMapper.deleteBatchIds(ids);
    }

}
