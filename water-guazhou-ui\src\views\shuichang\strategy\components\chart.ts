import * as echarts from 'echarts';

export function setBT(params?: any) {
  const colorList = [
    '#47A2FF ',
    '#53C8D1',
    '#59CB74',
    '#FBD444',
    '#7F6AAD',
    '#585247'
  ];
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c}次 ({d}%)',
      textStyle: {
        fontSize: 14
      }
    },
    legend: {
      type: 'scroll',
      orient: 'vertical',
      right: 10,
      top: 'middle',
      itemGap: 12,//纵向间距
      selectedMode: false,
      icon: 'circle',
      itemWidth: 10,//圆点大小
      itemHeight: 10,
      pageIconSize: 14,
      pageTextStyle: {
        color: '#77899c'
      },
      data: params?.name || [],
      textStyle: {
        color: '#77899c',
        fontSize: 14,
        rich: {
          uname: {
            width: 50,
            padding: [0, 0, 0, 0],
            color: '#77899c',
            align: 'left'
          },
          unum: {
            color: '#4a83f7',
            width: 50,
            align: 'right'
          }
        }
      },
      formatter(name) {
        const index = (params?.name || []).indexOf(name);
        return `{uname|${name}} {unum|${params?.data?.[index] || 0}次}`;
      }
    },
    grid: {
      containLabel: true
    },
    color: colorList,
    series: [
      {
        name: '报警',
        type: 'pie',
        radius: ['0%', '70%'],
        center: ['30%', '50%'],
        label: {
          show: false
        },
        labelLine: {
          show: false
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          },
          scale: true
        },
        data: (params?.data || []).map((item, i) => {
          return { name: params?.name?.[i], value: item };
        })
      }
    ]
  };
  return option;
}

export function setBJPX(params?: any) {
  const colorList = ['#f36c6c', '#e6cf4e', '#20d180', '#0093ff'];
  let itemStyle;
  const datas = [
    {
      value: 36,
      name: '系列一'
    },
    {
      value: 54,
      name: '系列二'
    },
    {
      value: 29,
      name: '系列三'
    },
    {
      value: 25,
      name: '系列四'
    },
    {
      value: 55,
      name: '系列五'
    },
    {
      value: 69,
      name: '系列6'
    },
    {
      value: 75,
      name: '系列7'
    },
    {
      value: 85,
      name: '系列8'
    }
  ];
  const maxArr = new Array(datas.length).fill(100);
  const option = {
    legend: {
      show: false
    },
    grid: {
      left: 0,
      right: 0,
      containLabel: true
    },
    xAxis: {
      show: false,
      type: 'value'
    },
    yAxis: [
      {
        type: 'category',
        inverse: true,
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisPointer: {
          label: {
            show: true,
            margin: 30
          }
        },
        data: datas.map((item) => item.name),
        axisLabel: {
          margin: 100,
          fontSize: 14,
          align: 'left',
          color: '#333',
          rich: {
            a1: {
              color: '#fff',
              backgroundColor: colorList[0],
              width: 30,
              height: 30,
              align: 'center',
              borderRadius: 2
            },
            a2: {
              color: '#fff',
              backgroundColor: colorList[1],
              width: 30,
              height: 30,
              align: 'center',
              borderRadius: 2
            },
            a3: {
              color: '#fff',
              backgroundColor: colorList[2],
              width: 30,
              height: 30,
              align: 'center',
              borderRadius: 2
            },
            b: {
              color: '#fff',
              backgroundColor: colorList[3],
              width: 30,
              height: 30,
              align: 'center',
              borderRadius: 2
            }
          },
          formatter(params) {
            let index = datas.map((item) => item.name).indexOf(params);
            index += 1;
            if (index - 1 < 3) {
              return ['{a' + index + '|' + index + '}' + '  ' + params].join(
                '\n'
              );
            }
            return ['{b|' + index + '}' + '  ' + params].join('\n');
          }
        }
      },
      {
        type: 'category',
        inverse: true,
        axisTick: 'none',
        axisLine: 'none',
        show: true,
        data: datas.map((item) => item.value),
        axisLabel: {
          show: true,
          fontSize: 14,
          color: '#333',
          formatter: '{value} 次'
        }
      }
    ],
    series: [
      {
        z: 2,
        name: 'value',
        type: 'bar',
        barWidth: 20,
        zlevel: 1,
        data: datas.map((item, i) => {
          itemStyle = {
            color: i > 3 ? colorList[3] : colorList[i]
          };
          return {
            value: item.value,
            itemStyle
          };
        }),
        label: {
          show: false,
          position: 'right',
          color: '#333333',
          fontSize: 14,
          offset: [10, 0]
        }
      },
      {
        name: '背景',
        type: 'bar',
        barWidth: 20,
        barGap: '-100%',
        itemStyle: {
          normal: {
            color: 'rgba(118, 111, 111, 0.55)'
          }
        },
        data: maxArr
      }
    ]
  };
  return option;
}

export function detailChart(val?: any[], xname?: string, type: string = '0') {
  const xdata: string[] = [];
  const data: any[] = [];
  if (type === '1') {
    val?.forEach((item) => {
      xdata.push(item.dayTimeKey);
      data.push(item.value);
    });
  } else {
    val &&
      val.forEach((item) => {
        item.dayDataList.forEach((key) => {
          xdata.push(`${item.dayTimeKey}: ${key.ts}`);
          data.push(key.value);
        });
      });
  }
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: xdata || []
    },
    yAxis: {
      type: 'value'
    },
    grid: {
      top: '40',
      left: '1%',
      right: '1%',
      bottom: '16%',
      containLabel: true
    },
    legend: {
      itemGap: 50,
      data: [xname || '']
    },
    dataZoom: [
      {
        type: 'inside',
        start: 0,
        end: 100
      },
      {
        start: 0,
        end: 10
      }
    ],
    series: [
      {
        name: xname || '',
        type: 'line',
        symbolSize: 6,
        label: {
          show: true,
          position: 'top',
          textStyle: {
            color: '#fff'
          }
        },
        itemStyle: {
          normal: {
            color: '#28ffb3'
          }
        },
        areaStyle: {
          // 区域填充样式
          normal: {
            color: new echarts.graphic.LinearGradient(
              0,
              0,
              0,
              1,
              [
                {
                  offset: 0,
                  color: 'rgba(0,154,120,0.6)'
                },
                {
                  offset: 1,
                  color: 'rgba(0,0,0, 0)'
                }
              ],
              false
            )
          }
        },
        data: data || []
      }
    ]
  };
  return option;
}

export function lineOption(val?: any[], xname?: string) {
  const xdata: string[] = [];
  const data: any[] = [];
  val &&
    val.forEach((item) => {
      item.dayDataList.forEach((key) => {
        xdata.push(`${item.dayTimeKey}: ${key.ts}`);
        data.push(key.value);
      });
    });
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: xdata || []
    },
    yAxis: {
      type: 'value'
    },
    grid: {
      top: '40',
      left: '1%',
      right: '1%',
      bottom: '16%',
      containLabel: true
    },
    legend: {
      itemGap: 50,
      data: [xname || ''],
      textStyle: {
        color: '#fff'
      }
    },
    dataZoom: [
      {
        type: 'inside',
        start: 0,
        end: 100
      },
      {
        start: 0,
        end: 10
      }
    ],
    series: [
      {
        name: xname || '',
        type: 'line',
        symbolSize: 6,
        label: {
          show: true,
          position: 'top',
          textStyle: {
            color: '#fff'
          }
        },
        itemStyle: {
          normal: {
            color: '#28ffb3'
          }
        },
        areaStyle: {
          // 区域填充样式
          normal: {
            color: new echarts.graphic.LinearGradient(
              0,
              0,
              0,
              1,
              [
                {
                  offset: 0,
                  color: 'rgba(0,154,120,0.6)'
                },
                {
                  offset: 1,
                  color: 'rgba(0,0,0, 0)'
                }
              ],
              false
            )
          }
        },
        data: data || []
      }
    ]
  };
  return option;
}
