<template>
  <div :class="{ header_menu_position: showName }">
    <el-menu
      :default-active="appStore.appid"
      background-color="transparent"
      mode="horizontal"
      :popper-effect="'dark'"
      :ellipsis="!showName"
    >
      <template v-for="val in businessStore.curNavs" :key="val.id">
        <el-menu-item
          class="horizontal-menu-item"
          :index="val.id"
          @click="() => handleMenuItemClick(val)"
        >
          <template #title>
            <div class="menu-title-wrapper">
              <!-- <i v-if="val.img"></i> -->

              <el-image
                v-if="val.img"
                class="image-icon"
                :src="val.img"
                :fit="'fill'"
                alt=""
              />
              <Icon
                v-else
                style="margin-right: 4px; width: 1.5em; height: 1.5em"
                :icon="val.icon || 'material-symbols:dashboard'"
              />

              <span class="menu-title">{{ val.name }}</span>
            </div>
          </template>
        </el-menu-item>
      </template>
    </el-menu>
  </div>
</template>

<script lang="ts" setup>
import { Icon } from '@iconify/vue';
import { getToken, getYinShouLoginParams } from '@/api/login';
// import router from '@/router'
import { useAppStore, useBusinessStore, usePermissionStore, useUserStore } from '@/store';
import { getUrlPramByName } from '@/utils/GlobalHelper';
import {
  GetDeepestRoute,
  // GetDeepestRoutePath,
  refreshAllRoutes
} from '@/utils/RouterHelper';
import router from '@/router';

const businessStore = useBusinessStore();
const appStore = useAppStore();
const permissionStore = usePermissionStore()
const userStore = useUserStore()
const emit = defineEmits(['menu-click']);
const handleMenuItemClick = async (res?: NavResult) => {
  if (!businessStore.curNavs.length || !res) return;
  const isCurrentApp = appStore.appid && res.id === appStore.appid;
  appStore.SET_appname(res.name);
  appStore.SET_appid(res.id);
  // 跳转
  if (res.type === '2') {
    const param: NormalOption | null = getUrlPramByName(
      res.applicationUrl,
      'type'
    );
    let appurl = '';
    appStore.SET_isFrameApp(true);
    if (param) {
      if (param.value === 'yingshou') {
        const ysres = await getYinShouLoginParams();
        appurl =
          res.applicationUrl.split('?')[0] +
          '?username=' +
          ysres.data.username +
          '&password=' +
          ysres.data.password;
      } else if (param.value === 'yingshou2') {
        appurl = res.applicationUrl.split('?')[0] + '?o=' + getToken();
      } else {
        appurl = res.applicationUrl;
      }
      window.open(appurl);
    } else {
      const a = document.createElement('a');
      a.setAttribute('href', res.applicationUrl);
      a.setAttribute('target', '_blank');
      a.setAttribute('id', 'startTelMedicine');
      a.click();
      // window.open(res.applicationUrl)
    }
  } else if (res.type === '3') {
    await refreshAllRoutes();
    appStore.TOGGLE_submenuShow(false);
    if (permissionStore.addRouters.length) {
      const deepRouter = GetDeepestRoute(permissionStore.addRouters[0]);
      if (deepRouter) {
        router.push({
          ...deepRouter
        });
      }
    }
  } else {
    appStore.SET_isFrameApp(false);
    emit('menu-click', isCurrentApp);
    if (!isCurrentApp) {
      await refreshAllRoutes();
      appStore.TOGGLE_submenuShow(false);
    }
    // if (isCurrentApp) {
    //   emit('menu-click')
    //   // const path = router.currentRoute.value.fullPath === '/'
    //   //   ? GetDeepestRoutePath(store.permission.addRouters[0])
    //   //   : router.currentRoute.value.fullPath
    //   // router.push({
    //   //   path: path || '/'
    //   // })
    // } else {
    //   // const deeptestRoute = GetDeepestRoute(store.permission.addRouters[0])
    //   // router.push(deeptestRoute)
    // }
  }
};

// 江西账户专配
const showName = ref(true);
onBeforeMount(() => {
  showName.value = (userStore.name === '<EMAIL>') as boolean;
});
</script>

<style scoped lang="scss">
.el-menu.el-menu--horizontal {
  display: flex;
  // width: 800px;
  // margin-left: auto;
  // flex-direction: row-reverse;
  height: 100%;
  box-sizing: border-box;
  // align-content: center;
  // align-items: center;
  border-bottom: none;
  // // justify-content: flex-end;
  // flex-wrap: nowrap;
  :deep(.el-sub-menu__icon-more) {
    color: #fff;
  }
  & > .el-menu-item {
    border-bottom: none !important;
    box-sizing: border-box;
    -moz-box-sizing: border-box; /* 用于兼容Firefox */
    // height: 100%;
    font-size: 14px;
    .iconify {
      color: #fff;
    }
    * {
      vertical-align: unset;
    }
    &:not(.is-disabled) {
      &:hover,
      &:focus,
      &.is-active {
        // color: #57acea !important;
        // background-color: #001A33 !important;
        border-bottom: 2px solid rgb(2, 195, 249) !important;
        background-image: linear-gradient(
          rgba(2, 191, 249, 0) 0,
          rgba(2, 117, 249, 0.3) 100%
        ) !important;
        .menu-title,
        .iconify {
          // color: var(--el-menu-active-color);
          color: #fff;
        }
      }
    }
  }
}
.menu-title-wrapper {
  display: flex;
  align-items: center;
}
.dark {
  .menu-title-wrapper {
    color: #fff;
  }
}

.menu-title-wrapper {
  color: #8599AD;
  font-size: 14px;
  font-weight: 400;
}
.horizontal-menu-item {
  display: flex;
  align-items: center;
  &.el-menu-item {
    line-height: 52px;
  }
}
.image-icon {
  object-fit: contain;
  width: 1.5em;
  height: 1.5em;
  margin-right: 4px;
  display: flex;
  align-items: center;
  :deep(img) {
    vertical-align: middle;
  }
}

.header_menu {
  // width: auto;
}

.header_menu_position {
  padding-right: 235px;
  display: flex;
  justify-content: flex-end;
}
</style>
<style lang="scss">
.el-menu--popup-container {
  .menu-title-wrapper {
    color: var(--el-text-color-primary);
  }
}
.dark {
  .el-menu--popup-container {
    .menu-title-wrapper {
      color: #fff;
    }
  }
}
</style>
