package org.thingsboard.server.dao.util.imodel.response.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.BeanProperty;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.ContextualSerializer;
import com.fasterxml.jackson.databind.ser.std.StdSerializer;
import org.joda.time.DateTime;

import java.io.IOException;
import java.util.Date;
import java.util.Objects;

public class IStarDateSerializer extends StdSerializer<Date> implements ContextualSerializer {
    private final String name;

    public IStarDateSerializer(Class<Date> clazz, String name) {
        super(clazz);
        this.name = name;
    }

    @Override
    public void serialize(Date date, JsonGenerator gen, SerializerProvider serializerProvider) throws IOException {
        String formattedDate = formatDateTime(date);
        gen.writeString(formattedDate);
    }

    private String formatDateTime(Date date) {
        if (date == null)
            return null;
        return new DateTime(date).toString("yyyy-MM-dd HH:mm:ss");
    }

    @Override
    public JsonSerializer<?> createContextual(SerializerProvider serializerProvider, BeanProperty beanProperty) throws JsonMappingException {
        if (beanProperty != null) {
            if (Objects.equals(beanProperty.getType().getRawClass(), Date.class)) {
                return new IStarDateSerializer(Date.class, beanProperty.getName());
            }
            return serializerProvider.findValueSerializer(beanProperty.getType(), beanProperty);
        }
        return serializerProvider.findNullValueSerializer(beanProperty);
    }
}

