package org.thingsboard.server.dao.model.sql.smartOperation.project;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.Compute;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseUsername;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.util.Date;


@Getter
@Setter
@ResponseEntity
public class SoProjectOperateRecord {
    // id
    private String id;

    // 所属记录域编号
    private String code;

    // 项目名称
    @TableField(exist = false)
    private String name;

    // 项目类型
    @TableField(exist = false)
    private String type;

    // 项目类型名称
    @TableField(exist = false)
    private String typeName;

    // 详情
    @Compute("formatDetail")
    private String detail;

    // 创建人id
    @ParseUsername
    private String creator;

    // 记录时间
    private Date createTime;

    // 备注
    private String remark;

    // 客户id
    private String tenantId;

    @SuppressWarnings("unused")
    private String formatDetail() {
        return String.format(detail, name);
    }

}
