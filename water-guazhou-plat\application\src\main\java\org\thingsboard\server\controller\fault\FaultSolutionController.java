package org.thingsboard.server.controller.fault;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.fault.FaultSolutionService;
import org.thingsboard.server.dao.model.sql.fault.FaultSolution;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.List;

/**
 * 班组
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-11-07
 */
@RestController
@RequestMapping("api/fault/solution")
public class FaultSolutionController extends BaseController {

    @Autowired
    private FaultSolutionService faultSolutionService;

    @GetMapping("{mainId}")
    public IstarResponse getListByMainId(@PathVariable String mainId, @RequestParam(required = false, defaultValue = "") String name) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return IstarResponse.ok(faultSolutionService.getListByMainId(mainId, name, tenantId));
    }

    @PostMapping
    public IstarResponse save(@RequestBody FaultSolution faultSolution) throws ThingsboardException {
        faultSolution.setCreator(UUIDConverter.fromTimeUUID(getCurrentUser().getUuidId()));
        faultSolution.setTenantId(UUIDConverter.fromTimeUUID(getTenantId().getId()));
        return IstarResponse.ok(faultSolutionService.save(faultSolution));
    }

    @DeleteMapping
    public IstarResponse delete(@RequestBody List<String> ids) {
        return faultSolutionService.delete(ids);
    }
}
