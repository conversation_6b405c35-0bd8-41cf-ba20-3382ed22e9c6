package org.thingsboard.server.dao.dataSource;

import org.thingsboard.server.common.data.Device;
import org.thingsboard.server.common.data.id.DeviceId;
import org.thingsboard.server.dao.model.sql.DataSourceEntity;
import org.thingsboard.server.dao.model.sql.RestApiEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/5/26 9:42
 */
public interface JobService {
    void modifyTrigger(DataSourceEntity dataSourceEntity, String originatorId);

    void deleteTrigger(DataSourceEntity dataSourceEntity, String originatorId);

    void modifyOffline(Device device);

    void deleteOffline(Device device);

    void modifyRestApi(RestApiEntity restApiEntity);
}
