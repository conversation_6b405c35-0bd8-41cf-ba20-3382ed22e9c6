package org.thingsboard.server.controller.zutai;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.sql.zutai.AssetsEntity;
import org.thingsboard.server.dao.zutai.AssetsService;

import java.util.List;
import java.util.Map;

/**
 * 组态控件
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2021-06-15
 */
@RestController
@RequestMapping("api/zutai/assets")
public class AssetsController extends BaseController {
    @Autowired
    private AssetsService assetsService;

    /**
     * 获取控件列表
     * @param params
     * @return
     * @throws ThingsboardException
     */
    @GetMapping
    public List getAssetsList(@RequestParam(required = false) Map params) throws ThingsboardException {
        params.put("tenantId", UUIDConverter.fromTimeUUID(getTenantId().getId()));
        return assetsService.getListById(params);
    }

    /**
     * 保存/编辑控件
     * @param assetsEntity
     * @return
     * @throws ThingsboardException
     */
    @PostMapping
    public AssetsEntity save(@RequestBody AssetsEntity assetsEntity) throws ThingsboardException {
        assetsEntity.setTenantId(UUIDConverter.fromTimeUUID(getTenantId().getId()));
        return assetsService.save(assetsEntity);
    }

    /**
     * 删除控件
     * @param id
     */
    @DeleteMapping
    public void delete(@RequestParam String id) {
        assetsService.delete(id);
    }
}
