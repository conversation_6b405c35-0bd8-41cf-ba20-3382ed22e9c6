package org.thingsboard.server.controller.smartOperation.construction.project;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoBiddingCompany;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoBiddingCompanyPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoBiddingCompanySaveRequest;
import org.thingsboard.server.dao.construction.project.SoBiddingCompanyService;
import org.thingsboard.server.utils.imodel.annotations.IStarController2;

@IStarController2
@RequestMapping("/api/so/biddingCompany")
public class SoBiddingCompanyController extends BaseController {
    @Autowired
    private SoBiddingCompanyService service;


    @GetMapping
    public IPage<SoBiddingCompany> findAllConditional(SoBiddingCompanyPageRequest request) {
        return service.findAllConditional(request);
    }

    // @PostMapping
    public SoBiddingCompany save(@RequestBody SoBiddingCompanySaveRequest req) {
        return service.save(req);
    }

    // @PatchMapping("/{id}")
    public boolean edit(@RequestBody SoBiddingCompanySaveRequest req, @PathVariable String id) {
        return service.update(req.unwrap(id));
    }

    // @DeleteMapping("/{id}")
    public boolean delete(@PathVariable String id) {
        return service.delete(id);
    }
}