

package org.thingsboard.server.controller.dispatch;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.dispatch.DispatchMethodService;
import org.thingsboard.server.dao.model.sql.smartProduction.dispatch.DispatchMethod;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch.DispatchMethodPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch.DispatchMethodSaveRequest;
import org.thingsboard.server.utils.imodel.annotations.IStarController;

@IStarController
@RequestMapping({"/api/sp/dispatchMethod"})
public class DispatchMethodController extends BaseController {
    @Autowired
    private DispatchMethodService service;

    public DispatchMethodController() {
    }

    @GetMapping
    public IPage<DispatchMethod> findAllConditional(DispatchMethodPageRequest request) {
        return this.service.findAllConditional(request);
    }

    @PostMapping
    public DispatchMethod save(@RequestBody DispatchMethodSaveRequest req) {
        return this.service.save(req);
    }

    @PostMapping({"/switchEnabled/{id}/{enabled}"})
    public boolean switchEnabled(@PathVariable("id") String id, @PathVariable("enabled") String enabled) {
        return this.service.switchEnabled(id, Boolean.parseBoolean(enabled));
    }

    @PatchMapping({"/{id}"})
    public boolean edit(@RequestBody DispatchMethodSaveRequest req, @PathVariable String id) {
        return this.service.update((DispatchMethod)req.unwrap(id));
    }

    @DeleteMapping({"/{id}"})
    public boolean delete(@PathVariable String id) {
        return this.service.delete(id);
    }
}
