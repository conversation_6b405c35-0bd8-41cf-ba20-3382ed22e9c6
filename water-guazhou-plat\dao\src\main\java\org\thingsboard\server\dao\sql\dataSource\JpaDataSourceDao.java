package org.thingsboard.server.dao.sql.dataSource;

import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.dataSource.DataSourceType;
import org.thingsboard.server.common.data.relation.EntityRelation;
import org.thingsboard.server.dao.dataSource.DataSourceDao;
import org.thingsboard.server.dao.model.sql.DataSourceEntity;
import org.thingsboard.server.dao.project.ProjectRelationServiceImpl;
import org.thingsboard.server.dao.relation.RelationDao;

import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/2/26 15:05
 */
@Service
@Transactional
public class JpaDataSourceDao implements DataSourceDao {

    @Autowired
    private DataSourceRepository dataSourceRepository;

    @Autowired
    private RelationDao relationDao;

    @Autowired
    private ProjectRelationServiceImpl projectRelationService;

    @Override
    public List<DataSourceEntity> findByProjectIdAndType(String projectId, DataSourceType type) {
        return dataSourceRepository.findByProjectIdAndType(projectId, type.name(),1);
    }

    @Override
    @Transactional
    public DataSourceEntity save(DataSourceEntity dataSourceEntity) {
        return dataSourceRepository.save(dataSourceEntity);
    }

    @Override
    public boolean deleteDataSource(String dataSourceId) {
        try {
            dataSourceRepository.delete(dataSourceId);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public DataSourceEntity findById(String dataSourceId) {
        return dataSourceRepository.findOne(dataSourceId);
    }

    @Override
    public List<DataSourceEntity> findAllByOriginatorIdAndType(String originatorId, DataSourceType type) {
        return dataSourceRepository.findAllByOriginatorIdAndType(originatorId,type.name());
    }

    @Override
    public List<DataSourceEntity> findByTypeAndTenantId(String type, String tenantId) {
        List list = dataSourceRepository.findByTypeAndTenantId(type, tenantId);
        List<DataSourceEntity> result = new ArrayList<>();
        list.forEach(obj -> {
            Object[] data = (Object[]) obj;
            DataSourceEntity dataSourceEntity = (DataSourceEntity) data[0];
            dataSourceEntity.setProjectId(String.valueOf(data[1]));
            dataSourceEntity.setNodeId(dataSourceEntity.getId());

            result.add(dataSourceEntity);
        });
        return result;
    }

    @Override
    public List<DataSourceEntity> findByOriginatorId(String originatorId) {
        return dataSourceRepository.findByProjectId(originatorId,1);
    }

    @Override
    public int countByOriginatorIdAndName(String originatorId, String name) {
        return countByOriginatorIdAndName(originatorId, name);
    }

    @Override
    public List<DataSourceEntity> findAllByOriginatorIdInAndProperty(List<String> deviceIdList, String type, String property) {
        return dataSourceRepository.findByOriginatorIdInAndTypeAndProperty(deviceIdList, type, property);
    }

    @Override
    public List<DataSourceEntity> findByIdIn(List<String> dataSourceIds) {
        return dataSourceRepository.findByIdIn(dataSourceIds);
    }


}
