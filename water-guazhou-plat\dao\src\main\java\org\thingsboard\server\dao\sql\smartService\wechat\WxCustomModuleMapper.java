package org.thingsboard.server.dao.sql.smartService.wechat;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.smartService.wechat.WxCustomModule;
import org.thingsboard.server.dao.util.imodel.query.smartService.wechat.WxCustomModulePageRequest;

@Mapper
public interface WxCustomModuleMapper extends BaseMapper<WxCustomModule> {
    IPage<WxCustomModule> findByPage(WxCustomModulePageRequest request);

    boolean updateFully(WxCustomModule entity);

    boolean isMaximumTop(@Param("id") String id, @Param("tenantId") String tenantId, @Param("atTopMaximum") int atTopMaximum);

}
