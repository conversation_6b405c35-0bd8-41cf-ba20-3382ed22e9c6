package org.thingsboard.server.dao.model.sql.install;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 报装流程类型
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-10-14
 */
@TableName("tb_gan_install")
@Data
public class GanInstall {
    @TableId
    private String id;

    private String name;

    private String code;

    private String idCard;

    private String phone;

    private String address;

    private String idCardImg;

    private String housePropertyImg;

    private String businessLicenseImg;

    private String baseLivingImg;

    private String installName;

    private String installAddress;

    private String installIdCard;

    private String installContacts;

    private String installPhone;

    private String payType;

    private String taxpayerNo;

    private String invoiceName;

    private String businessAddress;

    private String bankName;

    private String bankNo;

    private String type;

    private String status;

    private String remark;

    private String auditor;

    private Integer currentStepNo;

    private Date createTime;

    private String userId;


}
