import{G as l,a1 as I,h as d}from"./AnimatedLinesLayer-B2VbV4jv.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./geometryEngineBase-BhsKaODW.js";import{Q as v}from"./index-r0dFAfgr.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./project-DUuzYgGl.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";const b="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAB9UlEQVRYhe3VPWhUQRSG4ZsIWqiFooUBC/8KEWxESGFIKWIliJ1W0cZGLCRWroI/KBai2IhFjO7ON5hiyXLeczdIBK2tIkQhaBFEEUFEkkLj2KwhBE0I7A4IOXCqOcPz3TswUxSrtVptrFqz2SMYCzAl9xPZAwS4LrgXzHoFn1NKXVkDyH1I0JD7WcFkVjy4DwhShAm5D1Xd92XDo9lxwVyEV/V6fWM2uCiKoubeJ5gVvBtx35YVr8J+wVfBp+i+OyteK8vtgmnB9wAHs+JPGo1NESYEP6L70ay4ma0TPBek4D6QFa9UKt3BPQiS4FJWvCiKIsBtQQpmd7PjEc63LpqRGOOa3PgxwU/BhwiDufFDgplg9jaabQ1mp2R2NQseGo29gi8y+zjSaOxcEOqMzE53FK81mz0B3gu+xbI8sHhdMFwdHd3SETy6bxa8FsxGs/6/zQSz3mB2rv14jGsFzwS/ovvJf82llLrkPtRWvFKpdAuqgiT3C8vNC4bbAsfx8Q2CW3+uWMGd5faklLoEj1fklOWO4H4/wM1HZbl+fkHuN1pfflEw1zqCsaU6wnjrNVxyblFPRxgU1ATXFgZ4GNwvB7MjgpkAU51owYzgsMyuCB7MB3jabO4RvBRM1tz7VvJbV3QEZv2CN4IXwWxXp5zV+r/qN5DLzblC4gwsAAAAAElFTkSuQmCC",U="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAADR0lEQVRYhe2WT4hVdRTHzzhJTtPQpoVYGGiuIizKLN20qjGh2iTSQgRrEy7clRt54NJFQRZIycCQvt85+Mjbm3u+575n3gKVkol2hRtFLBCncDLQKWV+LTx3uD7fe/PH92xRP/hx4b5z7ud7/vx+5xH9v3q4xvJ8RRV4uVKpLLvv8CRJRgQ4zUBkgPM8f+Dfgsf7KqJerz/EwNcMxACMBbP1ApxgILLZ4b6Wowxn4G8G0rE8X9Ei6vO+iLgDonokqB5xIVaIECDvi4iWyK9VG41VIjLIwBcMRAEyERkaz7JhBr5xu896IsIjK2r8oz9/kjRdKSKDARgXoHZocnI5EdF4lg2z2bc9ESEiQwVcgOMe9V5P88/jWTYsIoOt3S/N5iMM3HC/XfcM9z0lafo0ERGrvi/AB518g9kbnqmrR82eWjQ8SZIRBk45+JOguoWBGTa7Wk3T57v5BmA7A7cYmApm6xcNF5EhBppzkau+SUTEZtsYuMmql2sTE2va+pptLYQGYMO9wo2BPxmYCapbHPA2m32pqg+2+jLwWgEXsxeWBldtOPyMiAyy6mYGrjFwQ8ye7OQ7VyJguhfwGQZmA/AeEVE1yzZ1azgxG/WOXxqciEiAmh+ZE0F1LQMXGJgVs91d/UrwappuXBLcb69fGYiiWo8xDtTS9AkGzruIrQuBB7N3BBABhAFmYL/k+cPzCmDggKf+gj8PxhgHpNlczWYfFjdci8+rrZEfrdcfrU1MrAnABhcQg+pH8wtQvcjALWk2VzNw1h0/jjEOtLMPqq/Ml3a/R24ycKnTd4iIqKr6bHHHE90uRzHR2s12AV5n4C8GfpM0faZrYMAPDETJsuc6GonqPgZiMPu0eFcWUR6ri4G7gINe0kpnAWbfeffvvOP97WHy/dy/HtU9DFxn4I+g+uJ8cCKioLrDBZxtD6/XH2NgloF4rNFY1/q7n46TpWG0oMhLAta632xN9fG7BQC73OD3To3imTjDwLQALy0UTkQUYxxgYMrnybvtBBx3Ab+0O2rFqlQqy5IkGVkMnIjo0OTkcgYuOeOruwxY9XIpved8CPVynyt9/0o7AcdKBn3dAtTapkjMRgPwVj+3mI12K/F/b/0DkZsqT+CQScYAAAAASUVORK5CYII=",R="data:image/png;base64,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",V="data:image/png;base64,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",w="/static/png/darkblue_1-CKukAQjB.png",L="data:image/png;base64,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",u="/static/png/img-6x6difSs.png",q="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAABO0lEQVRYhe2SPUsDQRCGR7CRCHYSiIVYmsLedCI2+hv8aE0vEsHaP2ChlQRMbgYiyuXmnSXF9elt9J/YrE0uJmeQILim2Ade2J1l2YedIYpEIpEIEQlwymY3QQOcjAUY6DPgA+fl6weyrNrr97dCRrKs+h/dnk3HrM5m+yGTmO2MBRh4CD0DAjxPCSSqj8VeVLcZ8D3VjdH5R+Lc0Wg9FNUrIiI2uxdARutmovpGRMSqDQZ827mK5PkqA55VGxPv3f4o0DGrL5SAAJdPzq2XBUR1T8wOywIyGKyJ6jUR0a8EihZImtbKA8vAkM1a5XoCnBcCk7SdqzDgu87tzi2Q5/lyYnbmvV/69pDqQRfYLNclTWtFm2bcORaRlbkF/pqZAgy8J8BdiDDwOi1g1hRAQobNLkL9eCQSWWw+AZZVJPHoS+wkAAAAAElFTkSuQmCC",W="data:image/png;base64,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",r=g=>{var A;return(A=new URL(Object.assign({"../../../assets/images/gis/angle.png":b,"../../../assets/images/gis/area.png":U,"../../../assets/images/gis/dark.png":R,"../../../assets/images/gis/darkblue.png":V,"../../../assets/images/gis/darkblue_1.png":w,"../../../assets/images/gis/icon.png":L,"../../../assets/images/gis/img.png":u,"../../../assets/images/gis/length.png":q,"../../../assets/images/gis/vect.png":W})[`../../../assets/images/gis/${g}`],import.meta.url))==null?void 0:A.href},f=g=>{let i,A;const m=(t,p,n)=>{const{createTdtLayer:s}=l(),c=s({type:"cva_w"}),o=s({type:"vec_w"}),B=s({type:"vec_w",color:"rgba(255, 255, 255, 0.0)",filter:"grayscale(0%) invert(100%) opacity(100%)",urlTemplate:"http://{subDomain}.tianditu.com/DataServer?T=vec_w&x={col}&y={row}&l={level}&tk="+window.SITE_CONFIG.GIS_CONFIG.gisTdtToken}),C=s({type:"img_w"}),E=s({type:"cia_w"}),e=n??window.SITE_CONFIG.GIS_CONFIG.gisDefaultPoi;return i=new I({view:t,container:document.createElement("div"),source:[{id:"dark_vec_w",title:"暗色",baseLayers:[B,...e?[c]:[]],thumbnailUrl:r("dark.png")},{id:"vec_w",title:"矢量",baseLayers:[o,...e?[c]:[]],thumbnailUrl:r("vect.png")},{id:"img_w",title:"影像",baseLayers:[C,...e?[E]:[]],thumbnailUrl:r("img.png")}]}),A=new d({view:t,content:i,expanded:!1,expandTooltip:"切换底图"}),i==null||i.watch("activeBasemap",Q=>{g==null||g(Q),A==null||A.collapse()}),A&&(t==null||t.ui.add(A,p||"bottom-right")),A},a=()=>{i==null||i.destroy(),A==null||A.destroy()};return v(()=>{a()}),{init:m}};export{f as u};
