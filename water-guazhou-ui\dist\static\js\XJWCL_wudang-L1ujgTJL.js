import{l as f}from"./index-CpGhZCTT.js";import{d as b,r as g,o as v,ay as y,g as a,n as o,aB as d,aJ as u,p as n,q as x,bh as p,i as w,C}from"./index-r0dFAfgr.js";const k={class:"inspection-comp-ratio"},L={class:"chart-wrapper"},S={class:"blocks"},B={class:"text"},N={class:"count"},V=b({__name:"XJWCL_wudang",setup(W){const i=(s,e)=>({series:[{type:"gauge",min:0,max:100,startAngle:200,endAngle:-20,splitNumber:12,itemStyle:{color:"rgb(60, 148, 221)"},radius:"100%",center:["50%","65%"],progress:{show:!0,width:12},pointer:{show:!1},axisLine:{lineStyle:{width:12}},axisTick:{show:!1},axisLabel:{show:!1},splitLine:{show:!1},detail:{offsetCenter:[0,0],valueAnimation:!0,formatter(t){return"{value|"+t.toFixed(0)+`}{unit|%}
{name|`+s+"}"},rich:{name:{fontSize:12,color:"rgb(146, 209, 253)",fontWight:"bolder"},value:{fontSize:20,fontWeight:"bolder",color:"rgb(60, 148, 221)"},unit:{fontSize:12,color:"#999"}}},data:[{value:e}]}]}),r=g({data:[{ratio:35.18,option:i("工单及时率",35.18),items:[{label:"完成工单总数",count:4096},{label:"及时完成数",count:1441}]}]}),m=()=>{f().then(s=>{var t;const e=((t=s.data)==null?void 0:t.data)||{};r.data[0].items=[{label:"完成工单总数",count:e.totalYearly},{label:"及时完成数",count:(e.totalYearly??0)*(e.percentYearly??0)}],r.data[0].option=i("工单及时率",Number(((e.percentYearly??0)*100).toFixed(2)))})};return v(()=>{m()}),(s,e)=>{const t=y("VChart");return a(),o("div",k,[(a(!0),o(d,null,u(w(r).data,(l,_)=>(a(),o("div",{key:_,class:"item"},[n("div",L,[x(t,{option:l.option},null,8,["option"])]),n("div",S,[(a(!0),o(d,null,u(l.items,(c,h)=>(a(),o("div",{key:h,class:"block-item"},[n("div",B,p(c.label),1),n("div",N,p(c.count),1)]))),128))])]))),128))])}}}),A=C(V,[["__scopeId","data-v-282583e9"]]);export{A as default};
