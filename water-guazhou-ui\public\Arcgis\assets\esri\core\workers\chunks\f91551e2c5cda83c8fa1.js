"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[690],{28576:(e,t,r)=>{r.d(t,{B:()=>c});var n=r(81153),i=r(17452),o=r(41123),s=r(7628),a=r(31263),l=r(5600),p=r(66094),u=r(25929);function c(e){const t=e?.origins??[void 0];return(r,o)=>{const c=function(e,t,r){if("resource"===e?.type)return function(e,t,r){const o=(0,s.Oe)(t,r);return{type:String,read:(e,t,r)=>{const n=(0,u.r)(e,t,r);return o.type===String?n:"function"==typeof o.type?new o.type({url:n}):void 0},write:{writer(t,s,l,c){if(!c||!c.resources)return"string"==typeof t?void(s[l]=(0,u.t)(t,c)):void(s[l]=t.write({},c));const g=function(e){return null==e?null:"string"==typeof e?e:e.url}(t),h=(0,u.t)(g,{...c,verifyItemRelativeUrls:c&&c.verifyItemRelativeUrls?{writtenUrls:c.verifyItemRelativeUrls.writtenUrls,rootPath:void 0}:void 0},u.M.NO),b=o.type!==String&&(!(0,n.l)(this)||c&&c.origin&&this.originIdOf(r)>(0,a.M9)(c.origin)),v={object:this,propertyName:r,value:t,targetUrl:h,dest:s,targetPropertyName:l,context:c,params:e};c&&c.portalItem&&h&&!(0,i.YP)(h)?b?function(e){const{context:t,targetUrl:r,params:n,value:o,dest:s,targetPropertyName:a}=e;if(!t.portalItem)return;const l=t.portalItem.resourceFromPath(r),u=f(o,r,t),c=(0,p.B)(u),g=(0,i.Ml)(l.path),h=n?.compress??!1;c===g?(t.resources&&d({...e,resource:l,content:u,compress:h,updates:t.resources.toUpdate}),s[a]=r):y(e)}(v):function({context:e,targetUrl:t,dest:r,targetPropertyName:n}){e.portalItem&&e.resources&&(e.resources.toKeep.push({resource:e.portalItem.resourceFromPath(t),compress:!1}),r[n]=t)}(v):c&&c.portalItem&&(null==h||null!=(0,u.i)(h)||(0,i.jc)(h)||b)?y(v):s[l]=h}}}}(e,t,r);switch(e?.type??"other"){case"other":return{read:!0,write:!0};case"url":{const{read:e,write:t}=u.a;return{read:e,write:t}}}}(e,r,o);for(const e of t){const t=(0,l.CJ)(r,e,o);for(const e in c)t[e]=c[e]}}}function y(e){const{targetUrl:t,params:n,value:s,context:a,dest:l,targetPropertyName:c}=e;if(!a.portalItem)return;const y=(0,u.p)(t),g=y?.filename??(0,o.D)(),h=n?.prefix??y?.prefix,b=f(s,t,a),v=(0,i.v_)(h,g),m=`${v}.${(0,p.B)(b)}`,C=a.portalItem.resourceFromPath(m);(0,i.jc)(t)&&a.resources&&a.resources.pendingOperations.push(async function(e){const t=(await Promise.resolve().then(r.bind(r,3172))).default,{data:n}=await t(e,{responseType:"blob"});return n}(t).then((e=>{C.path=`${v}.${(0,p.B)(e)}`,l[c]=C.itemRelativeUrl})).catch((()=>{})));const I=n?.compress??!1;a.resources&&d({...e,resource:C,content:b,compress:I,updates:a.resources.toAdd}),l[c]=C.itemRelativeUrl}function d({object:e,propertyName:t,updates:r,resource:n,content:i,compress:o}){r.push({resource:n,content:i,compress:o,finish:r=>{!function(e,t,r){"string"==typeof e[t]?e[t]=r.url:e[t].url=r.url}(e,t,r)}})}function f(e,t,r){return"string"==typeof e?{url:t}:new Blob([JSON.stringify(e.toJSON(r))],{type:"application/json"})}},81153:(e,t,r)=>{function n(e){return e&&"getAtOrigin"in e&&"originOf"in e}r.d(t,{l:()=>n})},41123:(e,t,r)=>{r.d(t,{D:()=>i});const n="randomUUID"in crypto;function i(){if(n)return crypto.randomUUID();const e=crypto.getRandomValues(new Uint16Array(8));e[3]=4095&e[3]|16384,e[4]=16383&e[4]|32768;const t=t=>e[t].toString(16).padStart(4,"0");return t(0)+t(1)+"-"+t(2)+"-"+t(3)+"-"+t(4)+"-"+t(5)+t(6)+t(7)}},30690:(e,t,r)=>{r.r(t),r.d(t,{default:()=>L});var n=r(43697),i=r(4757),o=r(70586);function s(e,t){return a(e)===a(t)}function a(e){if((0,o.Wi)(e))return null;const t=null!=e.layer?e.layer.id:"";let r=null;return r=null!=e.objectId?e.objectId:null!=e.layer&&"objectIdField"in e.layer&&null!=e.layer.objectIdField&&null!=e.attributes?e.attributes[e.layer.objectIdField]:e.uid,null==r?null:`o-${t}-${r}`}const l={json:{write:{writer:function(e,t){(0,o.Wi)(e)||null==e.layer?.objectIdField||null==e.attributes||(t.feature={layerId:e.layer.id,objectId:e.attributes[e.layer.objectIdField]})},target:{"feature.layerId":{type:[Number,String]},"feature.objectId":{type:[Number,String]}}},origins:{"web-scene":{read:function(e){if(null!=e.layerId&&null!=e.objectId)return{uid:null,layer:{id:e.layerId,objectIdField:"ObjectId"},attributes:{ObjectId:e.objectId}}}}}}};var p=r(15923),u=r(2368),c=r(96674),y=r(5600),d=(r(75215),r(67676),r(52011)),f=r(28576),g=r(94139),h=r(86787);let b=class extends((0,c.eC)((0,u.J)(p.Z))){constructor(e){super(e),this.position=null,this.elevationInfo=null,this.feature=null}equals(e){return(0,o._W)(this.position,e.position)&&(0,o._W)(this.elevationInfo,e.elevationInfo)&&s(this.feature,e.feature)}};(0,n._)([(0,y.Cb)({type:g.Z}),(0,f.B)()],b.prototype,"position",void 0),(0,n._)([(0,y.Cb)({type:h.Z}),(0,f.B)()],b.prototype,"elevationInfo",void 0),(0,n._)([(0,y.Cb)(l)],b.prototype,"feature",void 0),b=(0,n._)([(0,d.j)("esri.analysis.LineOfSightAnalysisObserver")],b);const v=b;let m=class extends((0,c.eC)(u.j)){constructor(e){super(e),this.position=null,this.elevationInfo=null,this.feature=null}equals(e){return(0,o._W)(this.position,e.position)&&(0,o._W)(this.elevationInfo,e.elevationInfo)&&s(this.feature,e.feature)}};(0,n._)([(0,y.Cb)({type:g.Z}),(0,f.B)()],m.prototype,"position",void 0),(0,n._)([(0,y.Cb)({type:h.Z}),(0,f.B)()],m.prototype,"elevationInfo",void 0),(0,n._)([(0,y.Cb)(l)],m.prototype,"feature",void 0),m=(0,n._)([(0,d.j)("esri.analysis.LineOfSightAnalysisTarget")],m);const C=m;var I=r(46791),w=r(70921),j=r(17445),_=r(44547),x=r(60437);function O(e,t){return function(e,t){return(0,o.Wi)(t)||!t.mode?function(e){return e?R:U}(e).mode:t.mode}(!!(0,o.pC)(e)&&e.hasZ,t)}r(12541);const R={mode:"absolute-height",offset:0},U={mode:"on-the-ground",offset:null},P=I.Z.ofType(C);let S=class extends i.Z{constructor(e){super(e),this.type="line-of-sight",this.observer=null,this.extent=null}initialize(){this.addHandles((0,j.YP)((()=>this._computeExtent()),(e=>{((0,o.Wi)(e)||(0,o.Wi)(e.pending))&&this._set("extent",(0,o.pC)(e)?e.extent:null)}),j.tX))}get targets(){return this._get("targets")||new P}set targets(e){this._set("targets",(0,w.Z)(e,this.targets,P))}get spatialReference(){return(0,o.pC)(this.observer)&&(0,o.pC)(this.observer.position)?this.observer.position.spatialReference:null}get requiredPropertiesForEditing(){return[(0,o.yw)(this.observer,(e=>e.position))]}async waitComputeExtent(){const e=this._computeExtent();return(0,o.pC)(e)?(0,o.Wg)(e.pending):Promise.resolve()}_computeExtent(){const e=this.spatialReference;if((0,o.Wi)(this.observer)||(0,o.Wi)(this.observer.position)||(0,o.Wi)(e))return null;const t=e=>"absolute-height"===O(e.position,e.elevationInfo),r=this.observer.position,n=(0,x.al)(r.x,r.y,r.z,r.x,r.y,r.z);for(const t of this.targets)if((0,o.pC)(t.position)){const r=(0,_.dz)(t.position,e);if((0,o.pC)(r.pending))return{pending:r.pending,extent:null};if((0,o.pC)(r.geometry)){const{x:e,y:t,z:i}=r.geometry;(0,x.pp)(n,[e,t,i])}}const i=(0,x.HH)(n,e);return t(this.observer)&&this.targets.every(t)||(i.zmin=void 0,i.zmax=void 0),{pending:null,extent:i}}clear(){this.observer=null,this.targets.removeAll()}};(0,n._)([(0,y.Cb)({type:["line-of-sight"]})],S.prototype,"type",void 0),(0,n._)([(0,y.Cb)({type:v,json:{read:!0,write:!0}})],S.prototype,"observer",void 0),(0,n._)([(0,y.Cb)({cast:w.R,type:P,nonNullable:!0,json:{read:!0,write:!0}})],S.prototype,"targets",null),(0,n._)([(0,y.Cb)({value:null,readOnly:!0})],S.prototype,"extent",void 0),(0,n._)([(0,y.Cb)({readOnly:!0})],S.prototype,"spatialReference",null),(0,n._)([(0,y.Cb)({readOnly:!0})],S.prototype,"requiredPropertiesForEditing",null),S=(0,n._)([(0,d.j)("esri.analysis.LineOfSightAnalysis")],S);const W=S;var z=r(16453),N=r(87085),Z=r(38009);const B=I.Z.ofType(C);let F=class extends((0,Z.q)((0,z.R)(N.Z))){constructor(e){super(e),this.type="line-of-sight",this.operationalLayerType="LineOfSightLayer",this.analysis=new W,this.opacity=1}initialize(){this.addHandles((0,j.YP)((()=>this.analysis),((e,t)=>{(0,o.pC)(t)&&t.parent===this&&(t.parent=null),(0,o.pC)(e)&&(e.parent=this)}),j.tX))}async load(){return(0,o.pC)(this.analysis)&&this.addResolvingPromise(this.analysis.waitComputeExtent()),this}get observer(){return(0,o.yw)(this.analysis,(e=>e.observer))}set observer(e){(0,o.yw)(this.analysis,(t=>t.observer=e))}get targets(){return(0,o.pC)(this.analysis)?this.analysis.targets:new I.Z}set targets(e){(0,w.Z)(e,this.analysis?.targets)}get fullExtent(){return(0,o.pC)(this.analysis)?this.analysis.extent:null}get spatialReference(){return(0,o.pC)(this.analysis)?(0,o.Wg)(this.analysis.spatialReference):null}releaseAnalysis(e){this.analysis===e&&(this.analysis=new W)}};(0,n._)([(0,y.Cb)({json:{read:!1},readOnly:!0})],F.prototype,"type",void 0),(0,n._)([(0,y.Cb)({type:["LineOfSightLayer"]})],F.prototype,"operationalLayerType",void 0),(0,n._)([(0,y.Cb)({type:v,json:{read:!0,write:{ignoreOrigin:!0}}})],F.prototype,"observer",null),(0,n._)([(0,y.Cb)({type:B,json:{read:!0,write:{ignoreOrigin:!0}}})],F.prototype,"targets",null),(0,n._)([(0,y.Cb)({nonNullable:!0,json:{read:!1,write:!1}})],F.prototype,"analysis",void 0),(0,n._)([(0,y.Cb)({readOnly:!0})],F.prototype,"fullExtent",null),(0,n._)([(0,y.Cb)({readOnly:!0})],F.prototype,"spatialReference",null),(0,n._)([(0,y.Cb)({readOnly:!0,json:{read:!1,write:!1,origins:{service:{read:!1,write:!1},"portal-item":{read:!1,write:!1},"web-document":{read:!1,write:!1}}}})],F.prototype,"opacity",void 0),(0,n._)([(0,y.Cb)({type:["show","hide"]})],F.prototype,"listMode",void 0),F=(0,n._)([(0,d.j)("esri.layers.LineOfSightLayer")],F);const L=F},66094:(e,t,r)=>{r.d(t,{B:()=>i});var n=r(17452);function i(e){return o[function(e){return e instanceof Blob?e.type:function(e){const t=(0,n.Ml)(e);return l[t]||s}(e.url)}(e)]||a}const o={},s="text/plain",a=o[s],l={png:"image/png",jpeg:"image/jpeg",jpg:"image/jpg",bmp:"image/bmp",gif:"image/gif",json:"application/json",txt:"text/plain",xml:"application/xml",svg:"image/svg+xml",zip:"application/zip",pbf:"application/vnd.mapbox-vector-tile",gz:"application/gzip","bin.gz":"application/octet-stream"};for(const e in l)o[l[e]]=e}}]);