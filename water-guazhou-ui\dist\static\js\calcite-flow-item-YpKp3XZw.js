import{p as k,H as C,m as d,g as v,h as a,e as E}from"./widget-BcWKanF2.js";import{u as w}from"./interactive-crkFkZAr.js";import{s as y,a as B,c as L}from"./loadable-DZS8sRBo.js";import{c as S,a as x,s as F,d as I,b as A,u as _}from"./t9n-B2bWcUZc.js";import{d as M,S as i}from"./panel-DHmkOIYq.js";import{d as O}from"./action-CK67UTEO.js";import{d as D,a as $}from"./action-menu-i7jt7xb8.js";import{d as P}from"./icon-vUORPQEt.js";import{d as z}from"./loader-DYvscnHN.js";import{d as R}from"./scrim-Eo5BG2Ie.js";import{d as T}from"./tooltip-Bo4Z0HDM.js";import"./Point-WxyopZva.js";import"./index-r0dFAfgr.js";import"./pe-B8dP0-Ut.js";import"./key-7hamXU9f.js";import"./observers-D10wq1Ib.js";import"./guid-DO7TRjsS.js";import"./openCloseComponent-aiDFLC5b.js";import"./debounce-x6ZvqDEC.js";/*!
 * All material copyright ESRI, All Rights Reserved, unless otherwise specified.
 * See https://github.com/Esri/calcite-components/blob/master/LICENSE.md for details.
 * v1.0.8-next.4
 */const H={backButton:"back-button"},u={backLeft:"chevron-left",backRight:"chevron-right"},o={headerActionsStart:"header-actions-start",headerActionsEnd:"header-actions-end",headerMenuActions:"header-menu-actions",headerContent:"header-content",fab:"fab",footer:"footer",footerActions:"footer-actions"},U="@keyframes in{0%{opacity:0}100%{opacity:1}}@keyframes in-down{0%{opacity:0;transform:translate3D(0, -5px, 0)}100%{opacity:1;transform:translate3D(0, 0, 0)}}@keyframes in-up{0%{opacity:0;transform:translate3D(0, 5px, 0)}100%{opacity:1;transform:translate3D(0, 0, 0)}}@keyframes in-scale{0%{opacity:0;transform:scale3D(0.95, 0.95, 1)}100%{opacity:1;transform:scale3D(1, 1, 1)}}:root{--calcite-animation-timing:calc(150ms * var(--calcite-internal-duration-factor));--calcite-internal-duration-factor:var(--calcite-duration-factor, 1);--calcite-internal-animation-timing-fast:calc(100ms * var(--calcite-internal-duration-factor));--calcite-internal-animation-timing-medium:calc(200ms * var(--calcite-internal-duration-factor));--calcite-internal-animation-timing-slow:calc(300ms * var(--calcite-internal-duration-factor))}.calcite-animate{opacity:0;animation-fill-mode:both;animation-duration:var(--calcite-animation-timing)}.calcite-animate__in{animation-name:in}.calcite-animate__in-down{animation-name:in-down}.calcite-animate__in-up{animation-name:in-up}.calcite-animate__in-scale{animation-name:in-scale}@media (prefers-reduced-motion: reduce){:root{--calcite-internal-duration-factor:0}}:host{box-sizing:border-box;background-color:var(--calcite-ui-foreground-1);color:var(--calcite-ui-text-2);font-size:var(--calcite-font-size--1)}:host *{box-sizing:border-box}:root{--calcite-floating-ui-transition:var(--calcite-animation-timing);--calcite-floating-ui-z-index:600}:host([hidden]){display:none}:host([disabled]){pointer-events:none;cursor:default;-webkit-user-select:none;user-select:none;opacity:var(--calcite-ui-opacity-disabled)}:host{position:relative;display:flex;inline-size:100%;flex:1 1 auto;overflow:hidden}:host([disabled]) ::slotted([calcite-hydrated][disabled]),:host([disabled]) [calcite-hydrated][disabled]{opacity:1}.back-button{border-width:0px;border-style:solid;border-color:var(--calcite-ui-border-3);border-inline-end-width:1px}",p=k(class extends C{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.calciteFlowItemBack=d(this,"calciteFlowItemBack",6),this.calciteFlowItemScroll=d(this,"calciteFlowItemScroll",6),this.calciteFlowItemClose=d(this,"calciteFlowItemClose",6),this.handlePanelScroll=t=>{t.stopPropagation(),this.calciteFlowItemScroll.emit()},this.handlePanelClose=t=>{t.stopPropagation(),this.calciteFlowItemClose.emit()},this.backButtonClick=()=>{this.calciteFlowItemBack.emit()},this.setBackRef=t=>{this.backButtonEl=t},this.setContainerRef=t=>{this.containerEl=t},this.closable=!1,this.closed=!1,this.beforeBack=void 0,this.description=void 0,this.disabled=!1,this.heading=void 0,this.headingLevel=void 0,this.loading=!1,this.menuOpen=!1,this.messageOverrides=void 0,this.messages=void 0,this.showBackButton=!1,this.backButtonEl=void 0,this.defaultMessages=void 0,this.effectiveLocale=""}onMessagesChange(){}connectedCallback(){S(this),x(this)}async componentWillLoad(){await F(this),y(this)}componentDidRender(){w(this)}disconnectedCallback(){I(this),A(this)}componentDidLoad(){B(this)}effectiveLocaleChange(){_(this,this.effectiveLocale)}async setFocus(){await L(this);const{backButtonEl:t,containerEl:e}=this;if(t){t.setFocus();return}e==null||e.setFocus()}async scrollContentTo(t){var e;await((e=this.containerEl)==null?void 0:e.scrollContentTo(t))}renderBackButton(){const{el:t}=this,e=v(t)==="rtl",{showBackButton:s,backButtonClick:c,messages:l}=this,n=l.back,r=e?u.backRight:u.backLeft;return s?a("calcite-action",{"aria-label":n,class:H.backButton,icon:r,key:"flow-back-button",onClick:c,ref:this.setBackRef,scale:"s",slot:"header-actions-start",text:n}):null}render(){const{closable:t,closed:e,description:s,disabled:c,heading:l,headingLevel:n,loading:r,menuOpen:g,messages:m,backButtonEl:f}=this,h=m.back;return a(E,null,a("calcite-panel",{closable:t,closed:e,description:s,disabled:c,heading:l,headingLevel:n,loading:r,menuOpen:g,messageOverrides:m,onCalcitePanelClose:this.handlePanelClose,onCalcitePanelScroll:this.handlePanelScroll,ref:this.setContainerRef},this.renderBackButton(),a("slot",{name:o.headerActionsStart,slot:i.headerActionsStart}),a("slot",{name:o.headerActionsEnd,slot:i.headerActionsEnd}),a("slot",{name:o.headerContent,slot:i.headerContent}),a("slot",{name:o.headerMenuActions,slot:i.headerMenuActions}),a("slot",{name:o.fab,slot:i.fab}),a("slot",{name:o.footerActions,slot:i.footerActions}),a("slot",{name:o.footer,slot:i.footer}),a("slot",null)),f?a("calcite-tooltip",{label:h,overlayPositioning:"fixed",placement:"top",referenceElement:f},h):null)}static get assetsDirs(){return["assets"]}get el(){return this}static get watchers(){return{messageOverrides:["onMessagesChange"],effectiveLocale:["effectiveLocaleChange"]}}static get style(){return U}},[1,"calcite-flow-item",{closable:[1540],closed:[1540],beforeBack:[16],description:[1],disabled:[516],heading:[1],headingLevel:[514,"heading-level"],loading:[516],menuOpen:[516,"menu-open"],messageOverrides:[1040],messages:[1040],showBackButton:[4,"show-back-button"],backButtonEl:[32],defaultMessages:[32],effectiveLocale:[32],setFocus:[64],scrollContentTo:[64]}]);function b(){if(typeof customElements>"u")return;["calcite-flow-item","calcite-action","calcite-action-menu","calcite-icon","calcite-loader","calcite-panel","calcite-popover","calcite-scrim","calcite-tooltip"].forEach(e=>{switch(e){case"calcite-flow-item":customElements.get(e)||customElements.define(e,p);break;case"calcite-action":customElements.get(e)||O();break;case"calcite-action-menu":customElements.get(e)||$();break;case"calcite-icon":customElements.get(e)||P();break;case"calcite-loader":customElements.get(e)||z();break;case"calcite-panel":customElements.get(e)||M();break;case"calcite-popover":customElements.get(e)||D();break;case"calcite-scrim":customElements.get(e)||R();break;case"calcite-tooltip":customElements.get(e)||T();break}})}b();const ce=p,le=b;export{ce as CalciteFlowItem,le as defineCustomElement};
