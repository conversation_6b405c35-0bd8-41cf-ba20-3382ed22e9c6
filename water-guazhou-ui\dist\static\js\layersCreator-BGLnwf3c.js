import{by as T,aH as g,bk as G}from"./MapView-DaoQedLH.js";import"./index-r0dFAfgr.js";import{u as f}from"./Point-WxyopZva.js";import{a as L}from"./lazyLayerLoader-DbM9sT1W.js";import{selectLayerClassPath as M}from"./portalLayers-BBYstHlZ.js";import"./widget-BcWKanF2.js";import"./pe-B8dP0-Ut.js";import"./layersLoader-BA9EpBiq.js";import"./fetchService-B3xiPs3_.js";import"./jsonContext-C-xrKYgv.js";function A(e){return S(e,"notes")}function v(e){return S(e,"markup")}function b(e){return S(e,"route")}function S(e,r){return!(!e.layerType||e.layerType!=="ArcGISFeatureLayer")&&e.featureCollectionType===r}async function w(e,r,y){if(!r)return;const t=[];for(const a of r){const i=h(a,y);a.layerType==="GroupLayer"?t.push(x(i,a,y)):t.push(i)}const n=await f(t);for(const a of n)a.value&&e.add(a.value)}const W={ArcGISDimensionLayer:"DimensionLayer",ArcGISFeatureLayer:"FeatureLayer",ArcGISImageServiceLayer:"ImageryLayer",ArcGISMapServiceLayer:"MapImageLayer",PointCloudLayer:"PointCloudLayer",ArcGISSceneServiceLayer:"SceneLayer",IntegratedMeshLayer:"IntegratedMeshLayer",OGCFeatureLayer:"OGCFeatureLayer",BuildingSceneLayer:"BuildingSceneLayer",ArcGISTiledElevationServiceLayer:"ElevationLayer",ArcGISTiledImageServiceLayer:"ImageryTileLayer",ArcGISTiledMapServiceLayer:"TileLayer",GroupLayer:"GroupLayer",GeoJSON:"GeoJSONLayer",WebTiledLayer:"WebTileLayer",CSV:"CSVLayer",VectorTileLayer:"VectorTileLayer",WFS:"WFSLayer",WMS:"WMSLayer",DefaultTileLayer:"TileLayer",KML:"KMLLayer",RasterDataLayer:"UnsupportedLayer",Voxel:"VoxelLayer",LineOfSightLayer:"LineOfSightLayer"},F={ArcGISTiledElevationServiceLayer:"ElevationLayer",DefaultTileLayer:"ElevationLayer",RasterDataElevationLayer:"UnsupportedLayer"},C={ArcGISTiledMapServiceLayer:"TileLayer",ArcGISTiledImageServiceLayer:"ImageryTileLayer",OpenStreetMap:"OpenStreetMapLayer",WebTiledLayer:"WebTileLayer",VectorTileLayer:"VectorTileLayer",ArcGISImageServiceLayer:"UnsupportedLayer",WMS:"UnsupportedLayer",ArcGISMapServiceLayer:"UnsupportedLayer",ArcGISSceneServiceLayer:"SceneLayer",DefaultTileLayer:"TileLayer"},O={ArcGISAnnotationLayer:"UnsupportedLayer",ArcGISDimensionLayer:"UnsupportedLayer",ArcGISFeatureLayer:"FeatureLayer",ArcGISImageServiceLayer:"ImageryLayer",ArcGISImageServiceVectorLayer:"ImageryLayer",ArcGISMapServiceLayer:"MapImageLayer",ArcGISStreamLayer:"StreamLayer",ArcGISTiledImageServiceLayer:"ImageryTileLayer",ArcGISTiledMapServiceLayer:"TileLayer",BingMapsAerial:"BingMapsLayer",BingMapsRoad:"BingMapsLayer",BingMapsHybrid:"BingMapsLayer",CSV:"CSVLayer",DefaultTileLayer:"TileLayer",GeoRSS:"GeoRSSLayer",GeoJSON:"GeoJSONLayer",GroupLayer:"GroupLayer",KML:"KMLLayer",MediaLayer:"MediaLayer",OGCFeatureLayer:"OGCFeatureLayer",OrientedImageryLayer:"OrientedImageryLayer",SubtypeGroupLayer:"SubtypeGroupLayer",VectorTileLayer:"VectorTileLayer",WFS:"WFSLayer",WMS:"WMSLayer",WebTiledLayer:"WebTileLayer"},V={ArcGISFeatureLayer:"FeatureLayer"},B={ArcGISImageServiceLayer:"ImageryLayer",ArcGISImageServiceVectorLayer:"ImageryLayer",ArcGISMapServiceLayer:"MapImageLayer",ArcGISTiledImageServiceLayer:"ImageryTileLayer",ArcGISTiledMapServiceLayer:"TileLayer",OpenStreetMap:"OpenStreetMapLayer",VectorTileLayer:"VectorTileLayer",WebTiledLayer:"WebTileLayer",BingMapsAerial:"BingMapsLayer",BingMapsRoad:"BingMapsLayer",BingMapsHybrid:"BingMapsLayer",WMS:"WMSLayer",DefaultTileLayer:"TileLayer"};async function h(e,r){return D(await U(e,r),e,r)}async function D(e,r,y){const t=new e;return t.read(r,y.context),t.type==="group"&&d(r)&&await E(t,r,y.context),await T(t,y.context),t}async function U(e,r){var o;const y=r.context,t=k(y);let n=e.layerType||e.type;!n&&r&&r.defaultLayerType&&(n=r.defaultLayerType);const a=t[n];let i=a?L[a]:L.UnknownLayer;if(m(e)){const c=y==null?void 0:y.portal;if(e.itemId){const l=new g({id:e.itemId,portal:c});await l.load();const s=(await M(l)).className||"UnknownLayer";i=L[s]}}else n==="ArcGISFeatureLayer"?A(e)||v(e)?i=L.MapNotesLayer:b(e)?i=L.RouteLayer:d(e)&&(i=L.GroupLayer):e.wmtsInfo&&e.wmtsInfo.url&&e.wmtsInfo.layerIdentifier?i=L.WMTSLayer:n==="WFS"&&((o=e.wfsInfo)==null?void 0:o.version)!=="2.0.0"&&(i=L.UnsupportedLayer);return i()}function d(e){var r,y;return e.layerType!=="ArcGISFeatureLayer"||m(e)?!1:(((y=(r=e.featureCollection)==null?void 0:r.layers)==null?void 0:y.length)??0)>1}function m(e){return e.type==="Feature Collection"}function k(e){let r;if(e.origin==="web-scene")switch(e.layerContainerType){case"basemap":r=C;break;case"ground":r=F;break;default:r=W}else switch(e.layerContainerType){case"basemap":r=B;break;case"tables":r=V;break;default:r=O}return r}async function x(e,r,y){const t=new G,n=w(t,Array.isArray(r.layers)?r.layers:[],y),a=await e;if(await n,a.type==="group")return a.layers.addMany(t),a}async function E(e,r,y){var c;const t=L.FeatureLayer,n=await t(),a=r.featureCollection,i=a==null?void 0:a.showLegend,o=(c=a==null?void 0:a.layers)==null?void 0:c.map((l,s)=>{var I;const p=new n;p.read(l,y);const u={...y,ignoreDefaults:!0};return p.read({id:`${e.id}-sublayer-${s}`,visibility:((I=r.visibleLayers)==null?void 0:I.includes(s))??!0},u),i!=null&&p.read({showLegend:i},u),p});e.layers.addMany(o??[])}export{w as populateOperationalLayers};
