package org.thingsboard.server.dao.fault;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.dao.model.sql.fault.FaultInfo;
import org.thingsboard.server.dao.sql.fault.FaultInfoMapper;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.Date;
import java.util.List;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-08-24
 */
@Slf4j
@Service
@Transactional
public class FaultInfoServiceImpl implements FaultInfoService {

    @Autowired
    private FaultInfoMapper faultInfoMapper;

    @Override
    public List<FaultInfo> getListByMainId(String mainId, String name, String tenantId) {

        List<FaultInfo> faultInfoList = faultInfoMapper.getListByMainId(mainId, name, tenantId);

        return faultInfoList;
    }

    @Override
    public FaultInfo save(FaultInfo faultType) {
        faultType.setUpdateTime(new Date());
        if (StringUtils.isBlank(faultType.getId())) {
            faultType.setCreateTime(new Date());
            faultInfoMapper.insert(faultType);
        } else {
            faultInfoMapper.updateById(faultType);
        }
        return faultType;
    }

    @Override
    public IstarResponse delete(List<String> ids) {
        faultInfoMapper.deleteBatchIds(ids);
        return IstarResponse.ok("删除成功");
    }

}
