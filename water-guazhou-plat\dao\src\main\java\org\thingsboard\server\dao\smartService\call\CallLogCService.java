package org.thingsboard.server.dao.smartService.call;

import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.smartService.call.CallLogC;

/**
 * 知识库公告
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-10-27
 */
public interface CallLogCService {
    PageData getList(String phone, Long startTime, Long endTime, String status, int page, int size);

    CallLogC process(CallLogC callLogC);

    CallLogC getById(String id);
}
