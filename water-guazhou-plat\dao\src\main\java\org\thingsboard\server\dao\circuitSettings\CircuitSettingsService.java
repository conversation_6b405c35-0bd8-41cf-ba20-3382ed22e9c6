package org.thingsboard.server.dao.circuitSettings;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.smartManagement.settings.CircuitSettings;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.settings.CircuitSettingsPageRequest;

// 新增CircuitSettingsService
public interface CircuitSettingsService {
    /**
     * 分页条件查询巡检配置
     *
     * @param request 分页查询条件
     * @return 分页查询结果
     */
    IPage<CircuitSettings> findAllConditional(CircuitSettingsPageRequest request);

    /**
     * 保存巡检配置
     *
     * @param entity 实体信息
     * @return 保存好的实体
     */
    CircuitSettings save(CircuitSettings entity);

    /**
     * 增量修改
     *
     * @param entity 实体信息
     * @return 是否修改成功
     */
    boolean update(CircuitSettings entity);

    /**
     * 删除
     *
     * @param id 唯一标识
     * @return 是否删除成功
     */
    boolean delete(String id);

    /**
     * 查询单个巡检配置
     * @param id 唯一标识
     * @return 巡检配置
     */
    CircuitSettings findById(String id);
}