package org.thingsboard.server.controller.base;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.dao.app.AppTypeRelationService;
import org.thingsboard.server.dao.model.sql.AppTypeRelation;
import org.thingsboard.server.service.aspect.annotation.SysLog;

import java.util.List;

@RestController
@RequestMapping("api/app/type/relation")
public class AppTypeRelationController {

    @Autowired
    private AppTypeRelationService appTypeRelationService;

    @GetMapping("{id}")
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS')")
    public AppTypeRelation findById(@PathVariable String id) {
        return appTypeRelationService.findById(id);
    }

    @PostMapping("{appTypeId}")
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS')")
    @SysLog(detail = DataConstants.OPERATING_TYPE_APP_TYPE_RELATION_ADD)
    public Boolean assignMenuToAppType(@PathVariable String appTypeId, @RequestBody List<String> menuPoolId) {
        try {
            return appTypeRelationService.assignMenuToAppType(appTypeId, menuPoolId);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    @GetMapping("apptypeid/{id}")
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS')")
    public List<AppTypeRelation> findByAppTypeId(@PathVariable String id) {
        return appTypeRelationService.findByAppTypeId(id);
    }

    @GetMapping("menu/{appTypeId}")
    @PreAuthorize("hasAnyAuthority('SYS_ADMIN', 'TENANT_ADMIN', 'TENANT_SUPPORT', 'TENANT_PROMOTE','TENANT_SYS')")
    @SysLog(detail = DataConstants.OPERATING_TYPE_APP_TYPE_RELATION_GET)
    public List<String> findMenuByAppTypeId(@PathVariable String appTypeId) {
        return appTypeRelationService.findMenuByAppTypeId(appTypeId);
    }
}
