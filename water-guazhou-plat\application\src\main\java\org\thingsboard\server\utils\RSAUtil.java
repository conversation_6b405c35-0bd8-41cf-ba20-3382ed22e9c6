package org.thingsboard.server.utils;

// import com.dahuatech.hutool.core.codec.Base64;

import org.apache.commons.codec.binary.Base64;

import javax.crypto.Cipher;
import java.security.KeyFactory;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.X509EncodedKeySpec;

public class RSAUtil {

    /**
     * 基于 原生RSA公钥加密
     * @throws Exception
     */
    public static String testBaseEncrypt() {
        try {
            String publicKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDGXqiyqpWhrEIX76gzRXh92FiEnvLRyCBlXAJPNwnkG+7GM+jq56MXZdvJBmWsjY0KlzCox7MQWWfc74Sbh1QE1kR7g65ei+ks9VOOBFYBphjXZTDUoxP3b1DTRZfrL6CUGEVyy/7tibuwcxc+e+czAqsvsj/l4ZhYm1RV4SNlnwIDAQAB";
            Base64 base64 = new Base64();
            byte[] decoded = base64.decode(publicKey);
            RSAPublicKey pubKey =
                    (RSAPublicKey)
                            KeyFactory.getInstance("RSA").generatePublic(new X509EncodedKeySpec(decoded));
            // RSA加密
            Cipher cipher = Cipher.getInstance("RSA");
            cipher.init(Cipher.ENCRYPT_MODE, pubKey);
            String outStr = new String(base64.encode(cipher.doFinal("dahua2021".getBytes("UTF-8"))));
            return outStr;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

}
