<!--数据对比 -->
<template>
  <div class="wrapper">
    <CardSearch ref="cardSearch" :config="cardSearchConfig" />
    <SLCard
      class="card"
      :title="state.activeName === 'list' ? '水质对比列表' : '水质对比曲线'"
    >
      <template #right>
        <el-radio-group v-model="state.activeName">
          <el-radio-button label="echarts">
            <Icon
              style="margin-right: 1px; font-size: 16px"
              icon="clarity:line-chart-line"
            />
          </el-radio-button>
          <el-radio-button label="list">
            <Icon
              style="margin-right: 1px; font-size: 16px"
              icon="material-symbols:table"
            />
          </el-radio-button>
        </el-radio-group>
      </template>
      <div
        v-show="state.activeName === 'echarts'"
        ref="agriEcoDev"
        class="chart-box"
      >
        <!-- 图表模式 -->
        <VChart
          ref="refChart"
          :theme="useAppStore().isDark ? 'dark' : 'light'"
          :option="state.chartOption"
        ></VChart>
      </div>
      <!-- 列表模式 -->
      <div v-show="state.activeName === 'list'">
        <FormTable
          ref="refCardTable"
          class="card-table"
          :config="cardTableConfig"
        />
      </div>
    </SLCard>
  </div>
</template>
<script lang="ts" setup>
import dayjs from 'dayjs';
import elementResizeDetectorMaker from 'element-resize-detector';
import { Refresh, Search, Download } from '@element-plus/icons-vue';
import { Icon } from '@iconify/vue';
import { lineOption } from '../echartsData/echart';
import { IECharts } from '@/plugins/echart';
import { getDataCompare } from '@/api/headwatersManage/queryStatistics';
import useStation from '@/hooks/station/useStation';
import useGlobal from '@/hooks/global/useGlobal';
import { formatColumn } from '@/utils/formartColumn';
import { useAppStore } from '@/store';
import { GetStationAttrs } from '@/api/shuiwureports/zhandian';

const { $messageWarning } = useGlobal();
const erd = elementResizeDetectorMaker();
const { getStationTree, getStationTreeByDisabledType, getStationAttrGroups } =
  useStation();
const state = reactive<{
  type: 'date' | 'month' | 'year';
  chartOption: any;
  stationTree: any;
  activeName: string;
  data: any;
  checkedKeys: string[];
}>({
  type: 'date',
  chartOption: null,
  stationTree: [],
  activeName: 'echarts',
  data: null,
  checkedKeys: []
});
const queryTypes = [
  { label: '1 m', value: '1m' },
  { label: '5 m', value: '5m' },
  { label: '10 m', value: '10m' },
  { label: '15 m', value: '15m' }
];
const refChart = ref<IECharts>();
const agriEcoDev = ref<any>();
const cardSearch = ref<ICardSearchIns>();
const refCardTable = ref<ICardTableIns>();
let tableData = reactive<any[]>([]);

// 水厂站点树
const TreeData = reactive<SLTreeConfig>({
  data: [],
  title: '区域划分',
  showCheckbox: true,
  defaultExpandAll: false,
  accordion: false,
  checkedKeys: [],
  handleCheck: (
    ids: string[],
    data: {
      checkedKeys?: string[] | undefined;
      checkedNodes?: Omit<NormalOption, 'children'>[] | undefined;
    }
  ) => {
    console.log(data.checkedNodes, data.checkedKeys);
    TreeData.checkedKeys = data.checkedKeys || [];
    TreeData.checkedNodes = data.checkedNodes || [];
    refreshData();
  },
  nodeExpand: async (params: any, node?: any) => {
    if (params.data?.type === 'Station' && params.children[0].id === 0) {
      const attrs = await getStationAttrGroups(params.id, true);
      node.data.children = attrs as any;
    }
  }
});
watch(
  () => state.activeName,
  () => {
    if (state.activeName === 'echarts') {
      refuseChart();
    }
  }
);
// 搜索栏初始化配置
const cardSearchConfig = reactive<ISearch>({
  defaultParams: {
    queryType: '15m',
    type: 'day',
    year: [dayjs().format(), dayjs().format()],
    month: [dayjs().format(), dayjs().format()],
    day: [dayjs().startOf('day').format(), dayjs().format()]
  },
  filters: [
    {
      type: 'select-tree',
      label: '监测点:',
      multiple: true,
      field: 'attributeId',
      clearable: false,
      showCheckbox: true,
      lazy: true,
      options: computed(() => state.stationTree) as any,
      lazyLoad: (node, resolve) => {
        if (node.level === 0) return resolve([]);
        if (node.data.children?.length > 0) {
          return resolve(node.data.children);
        }
        if (node.isLeaf) return resolve([]);
        if (node.data?.isLeaf) return resolve([]);
        GetStationAttrs({ stationId: node.data.id }).then((res) => {
          const newAttrs = res.data?.map((attr) => {
            return {
              label: attr.type,
              value: '',
              id: '',
              children: attr.attrList.map((attr) => {
                return {
                  label: attr.name,
                  value: attr.id,
                  id: attr.id,
                  isLeaf: true
                };
              })
            };
          });
          return resolve(newAttrs);
        });
      }
    },
    {
      type: 'radio-button',
      field: 'type',
      options: [
        { label: '日', value: 'day' },
        { label: '月', value: 'month' },
        { label: '年', value: 'year' }
      ],
      label: '时间频率'
    },
    {
      type: 'datetimerange',
      label: '选择日期',
      field: 'day',
      clearable: false,
      handleHidden: (params: any, query: any, formItem: IFormItem) => {
        formItem.hidden = params.type === 'month' || params.type === 'year';
      }
    },
    {
      type: 'monthrange',
      label: '选择日期',
      field: 'month',
      clearable: false,
      handleHidden: (params: any, query: any, formItem: IFormItem) => {
        formItem.hidden = params.type === 'day' || params.type === 'year';
      }
    },
    {
      type: 'yearrange',
      label: '选择日期',
      field: 'year',
      clearable: false,
      handleHidden: (params: any, query: any, formItem: IFormItem) => {
        formItem.hidden = params.type === 'month' || params.type === 'day';
      }
    },
    {
      type: 'select',
      label: '时间间隔:',
      field: 'queryType',
      clearable: false,
      allowCreate: true,
      options: queryTypes,
      itemContainerStyle: {
        width: '180px'
      },
      handleHidden: (params: any, query: any, formItem: IFormItem) => {
        formItem.hidden = params.type === 'month' || params.type === 'year';
      }
    }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          click: () => {
            const queryParams = (cardSearch.value?.queryParams as any) || {};
            if (queryParams.attributeId && queryParams.attributeId.length > 0) {
              refreshData();
            } else $messageWarning('选择监测点');
          },
          svgIcon: shallowRef(Search)
        },
        {
          type: 'default',
          perm: true,
          text: '重置',
          svgIcon: shallowRef(Refresh),
          click: () => {
            cardSearch.value?.resetForm();
          }
        },

        {
          text: '导出',
          // perm: $btnPerms('user_manage_addUser'),
          perm: true,
          type: 'warning',
          svgIcon: shallowRef(Download),
          hide: () => {
            return state.activeName !== 'list';
          },
          click: () => _exportWaterQuality()
        }
      ]
    }
  ]
});

// 初始化列表配置数据
const cardTableConfig = reactive<ITable>({
  loading: false,
  dataList: [],
  columns: [],
  operations: [],
  pagination: {
    refreshData: ({ page, size }) => {
      cardTableConfig.pagination.page = page;
      cardTableConfig.pagination.limit = size;
      cardTableConfig.dataList = tableData?.slice(
        (page - 1) * size,
        page * size
      );
    }
  }
});

// 导出水量报告
const _exportWaterQuality = () => {
  if (cardTableConfig.dataList.length > 0) {
    refCardTable.value?.exportTable();
  } else {
    $messageWarning('无数据导出');
  }
};
// 刷新列表
const refreshData = () => {
  cardTableConfig.loading = true;
  const queryParams = (cardSearch.value?.queryParams as any) || {};
  console.log(queryParams);
  const [start, end] = queryParams[queryParams.type] || [];
  let startTime: any = 0;
  let endTime: any = 0;
  if (queryParams.type === 'day') {
    startTime = start ? dayjs(start).valueOf() : '';
    endTime = end ? dayjs(end).valueOf() : '';
  } else {
    startTime = start ? dayjs(start).startOf(queryParams.type).valueOf() : '';
    endTime = end ? dayjs(end).endOf(queryParams.type).valueOf() : '';
  }

  const params: any = {
    attributes: queryParams.attributeId.join(','),
    queryType:
      queryParams.type === 'month'
        ? 'day'
        : queryParams.type === 'year'
          ? 'month'
          : queryParams.queryType,
    start: startTime,
    end: endTime
  };

  getDataCompare(params).then((res) => {
    const data = res.data?.data;
    state.data = data;
    tableData = data?.tableDataList as any[];
    const columns = formatColumn(data?.tableInfo) as any;
    cardTableConfig.columns = columns;
    cardTableConfig.dataList = tableData.slice((1 - 1) * 20, 1 * 20);
    cardTableConfig.pagination.total = data?.tableDataList.length;
    cardTableConfig.loading = false;
    refuseChart();
  });
};

// 刷新图表
const resizeChart = () => {
  refChart.value?.resize();
};
// 配置加载图表数据
const refuseChart = () => {
  const chartOption = lineOption() as any;
  chartOption.series = [];
  const serie = {
    name: '',
    smooth: true,
    data: [],
    type: 'line',
    markPoint: {
      data: [
        {
          type: 'max',
          name: '最大值',
          label: {
            fontSize: 12,
            color: useAppStore().isDark ? '#ffffff' : '#000000'
          }
        },
        {
          type: 'min',
          name: '最小值',
          label: {
            color: useAppStore().isDark ? '#ffffff' : '#000000'
          }
        }
      ]
    },
    markLine: {
      data: [{ type: 'average', name: '平均值' }]
    }
  };

  chartOption.xAxis.data = state.data?.tableDataList.map((table) => table.ts);
  state.data?.tableInfo.map((info, index) => {
    if (info.columnValue !== 'ts') {
      const newSerie = JSON.parse(JSON.stringify(serie));
      newSerie.name = info.columnName;
      newSerie.data = state.data?.tableDataList.map(
        (table) => table[info.columnValue]
      );
      const name =
        info.columnName.split('--')[2] +
        (info.unit ? '(' + info.unit + ')' : '');
      if (index === 1) {
        chartOption.yAxis[0].name = name;
      } else if (index > 1) {
        const yAxis = chartOption.yAxis.find((axis) => axis.name === name);
        if (!yAxis) {
          newSerie.yAxisIndex = index - 1;
          chartOption.grid.right = 50 * (index - 1);
          chartOption.yAxis.push({
            position: 'right',
            alignTicks: true,
            type: 'value',
            name,
            offset: 50 * (index - 2),
            axisLine: {
              show: true,
              lineStyle: {
                types: 'solid'
              }
            },
            axisLabel: {
              show: true
            },
            splitLine: {
              lineStyle: {
                type: [5, 10],
                dashOffset: 5
              }
            }
          });
        }
      }
      chartOption.series.push(newSerie);
    }
  });
  refChart.value?.clear();
  nextTick(() => {
    if (agriEcoDev.value) {
      erd.listenTo(agriEcoDev.value, () => {
        state.chartOption = chartOption;
        resizeChart();
      });
    }
  });
};

// onMounted(async () => {
//   const treeData = await getStationTree('水厂') as any[]
//   getStationTreeByDisabledType(treeData, ['Project', 'Station'], true, 'Station')
//   console.log('newTreeData', treeData)
//   nextTick(() => {
//     TreeData.data = treeData as any
//     TreeData.expandNodeId = [treeData[0].id]
//   })
// })
onBeforeMount(async () => {
  const treeData = (await getStationTree('水质监测站')) as any[];
  await getStationTreeByDisabledType(
    treeData,
    ['Project', 'Station'],
    false,
    'Station'
  );
  // await getStationTreeByDisabledType(treeData, ['Project', 'Station'], true, 'Station')
  state.stationTree = treeData;
  console.log(' state.stationTree ', state.stationTree);
});
</script>

<style lang="scss" scoped>
.card {
  height: calc(100% - 70px);
}

.card-table {
  height: calc(100vh - 235px);
  width: 100%;
}

.chart-box {
  width: 100%;
  height: calc(100vh - 250px);
}
</style>
