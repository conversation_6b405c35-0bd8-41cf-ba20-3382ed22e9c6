package org.thingsboard.server.dao.orderWork;

import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrderProcessLevel;

import java.util.List;

public interface WorkOrderProcessLevelService {
    List<WorkOrderProcessLevel> findList(String status, TenantId tenantId);

    void changeStatus(WorkOrderProcessLevel param);

    void save(WorkOrderProcessLevel entity);

    void remove(List<String> ids);
}
