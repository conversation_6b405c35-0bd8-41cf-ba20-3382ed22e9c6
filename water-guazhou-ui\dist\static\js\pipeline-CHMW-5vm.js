import{d as C,r as E,c as v,b,am as F,j as O,bu as N,g as P,n as J,q as k,i as g,t as V,_ as j,W as D,C as G}from"./index-r0dFAfgr.js";import{P as _,a as q,b as Y}from"./pipe-nogVzCHG.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import{c as H}from"./LayerHelper-Cn-iiqxI.js";import"./project-DUuzYgGl.js";import{E as s}from"./StatisticsHelper-D-s_6AyQ.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import{C as U}from"./index-CcDafpIP.js";import{r as L,o as p}from"./chart-wy3NEK2T.js";import{s as W}from"./sumBy-Dpy7mNiE.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./_baseSum-Cz9yialR.js";const $={class:"onemap-panel-wrapper"},z=C({__name:"pipeline",props:{view:{},menu:{}},emits:["highlightMark","addMarks"],setup(M,{emit:S}){const c=S,d=M,u=E({pipeLayerOption:[]}),m=v([{label:"0 km",value:"管网总长度"},{label:"0 条",value:"管网总数量"}]),w=v(),l=E({labelPosition:"top",group:[{id:"diameter",fieldset:{type:"underline",desc:"按管径区间统计管长"},fields:[{type:"vchart",option:L(),handleClick:t=>{var e;const a=(e=t.data.name)==null?void 0:e.split("-");if(a.length<2){b.error("参数错误");return}let r="";a[0]!==""?a[1]!==""?r=" DIAMETER <= "+a[1]+" and DIAMETER > "+a[0]+" ":r=" DIAMETER>"+a[0]+" ":r=" DIAMETER<="+a[1]+" ",c("highlightMark",d.menu,r,t.data.nameAlias)},style:{width:"100%",height:"150px"},itemContainerStyle:{marginBottom:0}}]},{id:"year",fieldset:{type:"underline",desc:"按年份统计管长"},fields:[{type:"vchart",option:p(),style:{width:"100%",height:"200px"},itemContainerStyle:{marginBottom:0}}]},{id:"diameter-bar",fieldset:{type:"underline",desc:"按管径统计管长"},fields:[{type:"vchart",option:p(),style:{width:"100%",height:"150px"},handleClick:t=>{c("highlightMark",d.menu," DIAMETER='"+(t.data.name||"0")+"' ",t.data.nameAlias)},itemContainerStyle:{marginBottom:0}}]},{id:"material-bar",fieldset:{type:"underline",desc:"按管材统计管长"},fields:[{type:"vchart",option:p(),style:{width:"100%",height:"150px"},handleClick:t=>{c("highlightMark",d.menu," MATERIAL='"+t.data.name+"' ",t.name)}}]}]}),y=async t=>{var e,i,o;return((o=(i=(e=(await _({usertoken:D().gToken,layerids:JSON.stringify(u.pipeLayerOption.map(n=>n.id)||[]),group_fields:JSON.stringify(t||["DIAMETER"]),statistic_field:s.ShapeLen,statistic_type:"2",where:"",f:"pjson"})).data)==null?void 0:e.result)==null?void 0:i.rows[0])==null?void 0:o.rows)||[]},T=async()=>{var i,o,n;const t="OBJECTID",e=(((n=(o=(i=(await _({usertoken:D().gToken,layerids:JSON.stringify(u.pipeLayerOption.map(f=>f.id)||[]),group_fields:JSON.stringify(["DIAMETER"]),statistic_field:t,statistic_type:"1",where:"",f:"pjson"})).data)==null?void 0:i.result)==null?void 0:o.rows[0])==null?void 0:n.rows)||[]).reduce((f,h)=>h&&h[t]&&f+h[t]||0,0);m.value[1].label=e+" 条"},R=async()=>{var o;const r=(await q()).data.result.map(n=>({name:n.itemname,nameAlias:"DN"+n.itemname,value:n.sumlength,valueAlias:n.sumlength,scale:n.itempercent+"%"})),e=(o=l.group.find(n=>n.id==="diameter"))==null?void 0:o.fields[0];e&&(e.option=L(r,"km","DN"));const i=W(r,n=>n.value);m.value[0].label=i.toFixed(2)+" km"},I=async()=>{var i;const r=(await Y()).data.result.map(o=>({name:o.itemname,nameAlias:o.itemname+"年",value:o.sumlength,valueAlias:o.sumlength,scale:o.itempercent+"%"})),e=(i=l.group.find(o=>o.id==="year"))==null?void 0:i.fields[0];e&&(e.option=p(r,"km"))},x=t=>{var e;const a=t.data.map(i=>{var o,n;return{name:((o=i.DIAMETER)==null?void 0:o.toString())||"未知",nameAlias:"DN"+(((n=i.DIAMETER)==null?void 0:n.toFixed(0))||"未知"),value:((i[s.ShapeLen]||0)/1e3).toFixed(4),valueAlias:((i[s.ShapeLen]||0)/1e3).toFixed(4),scale:((i[s.ShapeLen]||0)/t.total*100).toFixed(0)+"%"}}),r=(e=l.group.find(i=>i.id==="diameter-bar"))==null?void 0:e.fields[0];r&&(r.option=p(a,"km"))},B=t=>{var e;const a=(e=l.group.find(i=>i.id==="material-bar"))==null?void 0:e.fields[0];if(!a)return;const r=t.filter(i=>i.MATERIAL!==null).map(i=>({name:i.MATERIAL,value:((i[s.ShapeLen]||0)/1e3).toFixed(2)}));a.option=p(r,"km")},A=()=>{y().then(t=>{const a=t.reduce((r,e)=>e&&e[s.ShapeLen]&&r+e[s.ShapeLen]||0,0);x({data:t,total:a})}),I(),R(),y(["MATERIAL"]).then(t=>{B(t)}),T()};return F(()=>O().isDark,()=>A()),N(async()=>{u.pipeLayerOption=await H(d.view),A()}),(t,a)=>{const r=j;return P(),J("div",$,[k(g(U),{modelValue:g(m),"onUpdate:modelValue":a[0]||(a[0]=e=>V(m)?m.value=e:null),span:12},null,8,["modelValue"]),k(r,{ref_key:"refForm",ref:w,config:g(l)},null,8,["config"])])}}}),de=G(z,[["__scopeId","data-v-40294a7c"]]);export{de as default};
