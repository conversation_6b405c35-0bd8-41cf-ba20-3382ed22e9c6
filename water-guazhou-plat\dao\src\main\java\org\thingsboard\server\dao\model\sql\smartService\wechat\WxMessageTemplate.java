package org.thingsboard.server.dao.model.sql.smartService.wechat;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.util.Date;


@Getter
@Setter
@ResponseEntity
@TableName("wx_message_template")
public class WxMessageTemplate {
    // id
    private String id;

    // 模板名称
    private String name;

    // 模板Id
    private String templateId;

    // 页面模板JSON
    private String template;

    // 创建时间
    private Date createTime;

    // 客户id
    private String tenantId;

}
