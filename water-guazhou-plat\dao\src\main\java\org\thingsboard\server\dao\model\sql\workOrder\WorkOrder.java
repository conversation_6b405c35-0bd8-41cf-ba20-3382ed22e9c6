package org.thingsboard.server.dao.model.sql.workOrder;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.workOrder.extentions.WorkOrderUploadUserSupport;
import org.thingsboard.server.dao.util.imodel.response.ResponseMap;
import org.thingsboard.server.dao.util.imodel.response.annotations.Info;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseUsername;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;
import org.thingsboard.server.dao.util.imodel.response.model.JdbcHelper;

import java.util.Date;

@Getter
@Setter
@ResponseEntity
public class WorkOrder implements WorkOrderUploadUserSupport {
    // 工单ID
    private String id;

    // 工单单号唯一。两部分构成：1,日期; 2,递增号码。如：202207290001
    private String serialNo;

    // 标题
    private String title;

    // 来源
    private String source;

    // 发起人
    @ParseUsername
    private String organizerId;

    // 紧急程度
    private String level;

    // 工单类型
    private String type;

    // 地址
    private String address;

    // 描述/备注
    private String remark;

    // 现场视频，多个用逗号分隔（最多2）
    private String videoUrl;

    // 现场音频，多个用逗号分隔（最多2）
    private String audioUrl;

    // 现场图片，多个用逗号分隔
    private String imgUrl;

    // 其他附件，多个用逗号分隔（最多2）
    private String otherFileUrl;

    // 上报人
    @ParseUsername(withDepartment = true, withOrganization = true)
    private String uploadUserId;

    // 上报人电话
    private String uploadPhone;

    // 上报人户号
    private String uploadNo;

    // 上报人地址
    private String uploadAddress;

    // 是否直接派发
    private boolean isDirectDispatch;

    // 处理人(工单的接收人，不可更改)
    @ParseUsername
    private String processUserId;

    // 接收部门Id
    private String receiveDepartmentId;

    // 处理级别对应的分钟数
    private Integer processLevel;

    // 处理级别
    private String processLevelLabel;

    // 预计完成时间
    private Date estimatedFinishTime;

    // 完成时间
    private Date completeTime;

    // 当前状态
    @Info(name = "statusName")
    private WorkOrderStatus status;

    // 当前步骤处理人
    @ParseUsername
    private String stepProcessUserId;

    // 创建时间
    private Date createTime;

    // 最后更新时间
    private Date updateTime;

    // 项目ID
    private String projectId;

    // 项目名称
    @TableField(exist = false)
    private String projectName;

    // 租户ID
    private String tenantId;

    // 抄送人，多个用逗号隔开
    private String ccUserId;

    @TableField(exist = false)
    private String ccUserName;

    // 父工单Id
    private String parentId;

    // 地理位置，目前规则为经纬度使用逗号隔开
    private String coordinate;

    // 地理位置名称
    private String coordinateName;

    public WorkOrderStatus stage() {
        return status;
    }

    @SuppressWarnings("unused")
    private String statusName() {
        return status.getStageName();
    }

    @Override
    public void customizeMap(ResponseMap map, JdbcHelper jdbc) {
        WorkOrderUploadUserSupport.super.customizeMap(map, jdbc);
    }

}
