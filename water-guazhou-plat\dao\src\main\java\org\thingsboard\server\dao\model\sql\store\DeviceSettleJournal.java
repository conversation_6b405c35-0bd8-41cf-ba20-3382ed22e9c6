package org.thingsboard.server.dao.model.sql.store;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.sql.department.ConstructionProjectMapper;
import org.thingsboard.server.dao.sql.department.DepartmentMapper;
import org.thingsboard.server.dao.util.imodel.response.annotations.*;

import java.util.Date;

@Getter
@Setter
@ResponseEntity
public class DeviceSettleJournal {
    // id
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    // 设备标签
    private String deviceLabelCode;

    // 安装人员ID
    @ParseUsername(withDepartment = true)
    private String installUserId;

    // 施工项目ID
    @ParseViaMapper(ConstructionProjectMapper.class)
    private String projectId;

    // 安装时间
    private Date installTime;

    // 安装区域ID
    private String installAddressId;

    // 安装位置描述
    private String address;

    // 备注
    private String remark;

    // 创建时间
    private Date createTime;

    // 租户ID
    @ParseTenantName
    private String tenantId;
}
