import{G as f}from"./index-CpGhZCTT.js";import{d as c,r as u,o as m,ay as h,g,h as y,i as d,l}from"./index-r0dFAfgr.js";const O=c({__name:"GDLXSLTJ",setup(v){const r=u({workorderTypeOption:null}),s=e=>({tooltip:{trigger:"item"},legend:{type:"scroll",orient:"vertical",right:10,top:"center",pageIconColor:"#fff",pageTextStyle:{color:"#fff"},textStyle:{color:"#fff",rich:{name:{align:"left",width:100,fontSize:12},value:{align:"right",width:40,fontSize:12,color:"#00ff00"}}},data:e.map(o=>o.name),formatter(o){if(e&&e.length){for(let t=0;t<e.length;t++)if(o===e[t].name)return"{name| "+o+"}{value| "+e[t].value+"}"}}},series:[{type:"pie",radius:["50%","70%"],center:["30%","50%"],avoidLabelOverlap:!1,label:{show:!1,position:"center"},labelLine:{show:!1},data:e}]}),p=()=>{f({fromTime:l().startOf("y").valueOf(),toTime:l().valueOf(),statisticType:!0}).then(e=>{var t,n,a;const o=((a=(n=(t=e.data)==null?void 0:t.data)==null?void 0:n.types)==null?void 0:a.data)||[];r.workorderTypeOption=s(o.map(i=>({name:i.key,value:i.value})))})};return m(()=>{p()}),(e,o)=>{const t=h("VChart");return g(),y(t,{option:d(r).workorderTypeOption},null,8,["option"])}}});export{O as _};
