/**
 * Copyright &copy; 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package org.thingsboard.server.dao.schedule;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 定时任务Entity
 * <AUTHOR>
 * @version 2020-04-15
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
public class ScheduleJob  {
	
	private static final long serialVersionUID = 1L;
	/**
	 * 任务名称
	 */
	private String name;
	/**
	 * 任务组(默认为资源ID)
	 */
	private String group;
	/**
	 * 定时规则
	 */
	private String cronExpression;
	/**
	 * 启用状态
	 */
	private String status;
	/**
	 * 任务类
	 */
	private String className;
	/**
	 * 描述
	 */
	private String description;
	
}