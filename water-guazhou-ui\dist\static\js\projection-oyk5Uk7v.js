import{i as N,K as Y,L as $,P as j,D as x}from"./Point-WxyopZva.js";import{T as l}from"./index-r0dFAfgr.js";import{bd as R,be as _,bf as g,bg as M,bh as O,ae as W,af as b,bi as h,bj as L}from"./MapView-DaoQedLH.js";import{e as U}from"./mat3f64-BVJGbF0t.js";import{e as k}from"./mat4f64-BCm7QTSd.js";import{c as E}from"./spatialReferenceEllipsoidUtils-j_kxMN-4.js";import{i,T as c}from"./BufferView-BcX1hwIm.js";import{t as w,r as S,o as V}from"./vec33-BEptSvzS.js";const A=N.getLogger("esri.geometry.support.meshUtils.normalProjection");function Q(r,e,t,a,o){return d(a)?(F(s.TO_PCPF,i.fromTypedArray(r),c.fromTypedArray(e),c.fromTypedArray(t),a,i.fromTypedArray(o)),o):(A.error("Cannot convert spatial reference to PCPF"),o)}function X(r,e,t,a,o){return d(a)?(F(s.FROM_PCPF,i.fromTypedArray(r),c.fromTypedArray(e),c.fromTypedArray(t),a,i.fromTypedArray(o)),o):(A.error("Cannot convert to spatial reference from PCPF"),o)}function rr(r,e,t){return R(r,e,0,t,E(e),0,r.length/3),t}function er(r,e,t){return R(r,E(t),0,e,t,0,r.length/3),e}function or(r,e,t){if(l(r))return e;const a=c.fromTypedArray(r),o=c.fromTypedArray(e);return w(o,a,t),e}function tr(r,e,t){if(l(r))return e;h(f,t);const a=i.fromTypedArray(r),o=i.fromTypedArray(e);return S(o,a,f),L(f)||V(o,o),e}function ar(r,e,t){if(l(r))return e;h(f,t);const a=i.fromTypedArray(r,4*Float32Array.BYTES_PER_ELEMENT),o=i.fromTypedArray(e,4*Float32Array.BYTES_PER_ELEMENT);if(S(o,a,f),L(f)||V(o,o),r!==e)for(let n=3;n<r.length;n+=4)e[n]=r[n];return e}function fr(r,e,t,a,o){if(!d(a))return A.error("Cannot convert spatial reference to PCPF"),o;F(s.TO_PCPF,i.fromTypedArray(r,4*Float32Array.BYTES_PER_ELEMENT),c.fromTypedArray(e),c.fromTypedArray(t),a,i.fromTypedArray(o,4*Float32Array.BYTES_PER_ELEMENT));for(let n=3;n<r.length;n+=4)o[n]=r[n];return o}function nr(r,e,t,a,o){if(!d(a))return A.error("Cannot convert to spatial reference from PCPF"),o;F(s.FROM_PCPF,i.fromTypedArray(r,16),c.fromTypedArray(e),c.fromTypedArray(t),a,i.fromTypedArray(o,16));for(let n=3;n<r.length;n+=4)o[n]=r[n];return o}function F(r,e,t,a,o,n){if(!e)return;const C=t.count,u=E(o);if(v(o))for(let y=0;y<C;y++)a.getVec(y,T),e.getVec(y,m),_(u,T,P,u),g(f,P),r===s.FROM_PCPF&&M(f,f),O(m,m,f),n.setVec(y,m);else for(let y=0;y<C;y++){a.getVec(y,T),e.getVec(y,m),_(u,T,P,u),g(f,P);const B=Y(t.get(y,1));let p=Math.cos(B);r===s.TO_PCPF&&(p=1/p),f[0]*=p,f[1]*=p,f[2]*=p,f[3]*=p,f[4]*=p,f[5]*=p,r===s.FROM_PCPF&&M(f,f),O(m,m,f),W(m,m),n.setVec(y,m)}return n}function d(r){return v(r)||z(r)}function v(r){return r.isWGS84||$(r)||j(r)||x(r)}function z(r){return r.isWebMercator}var s;(function(r){r[r.TO_PCPF=0]="TO_PCPF",r[r.FROM_PCPF=1]="FROM_PCPF"})(s||(s={}));const T=b(),m=b(),P=k(),f=U();export{nr as L,rr as M,or as O,er as R,ar as V,X as h,Q as j,fr as k,tr as v};
