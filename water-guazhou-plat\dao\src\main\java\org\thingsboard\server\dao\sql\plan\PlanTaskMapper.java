package org.thingsboard.server.dao.sql.plan;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.plan.PlanTask;
import org.thingsboard.server.dao.model.sql.smartProduction.circuit.GeneralTaskStatus;
import org.thingsboard.server.dao.util.imodel.query.plan.PlanTaskCompleteRequest;
import org.thingsboard.server.dao.util.imodel.query.plan.PlanTaskPageRequest;
import org.thingsboard.server.dao.util.imodel.query.plan.PlanTaskStartRequest;

@Mapper
public interface PlanTaskMapper extends BaseMapper<PlanTask> {
    IPage<PlanTask> findByPage(PlanTaskPageRequest request);

    int save(PlanTask entity);

    @Override
    default int insert(PlanTask entity) {
        return save(entity);
    }

    boolean update(PlanTask entity);

    boolean complete(@Param("req") PlanTaskCompleteRequest req, @Param("status") GeneralTaskStatus status);

    boolean start(@Param("req") PlanTaskStartRequest req, @Param("status") GeneralTaskStatus status);

    int resetChildren(String id);

    boolean reset(String id);

    Boolean getIsRight(String id);

    boolean canOperate(@Param("id") String id, @Param("currentUser") String currentUser);
}
