<template>
  <div
    class="exit-box"
    @mouseover="handleMouseOver"
    @mouseleave="handleMouseLeave"
  >
    <div
      class="exit-fullscreen"
      :class="[collapsed ? 'collapsed' : '']"
      @click="emit('fullscreen')"
    >
      <Icon :icon="isFullScreen ? 'mdi:exit-to-app' : 'mdi:fullscreen'"></Icon>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { Icon } from '@iconify/vue'

const emit = defineEmits(['fullscreen'])

const collapsed = ref<boolean>(true)

const handleMouseOver = () => {
  collapsed.value = false
}
const handleMouseLeave = () => {
  collapsed.value = true
}
const isFullScreen = ref<boolean>(false)
watch(
  () => collapsed.value,
  () => {
    isFullScreen.value = !!document.fullscreenElement
  }
)
</script>
<style lang="scss" scoped>
.exit-box {
  position: absolute;
  width: 100px;
  height: 100%;
  right: 0;
  top: 0;
}
.exit-fullscreen {
  position: absolute;
  width: 50px;
  height: 30px;
  top: 27px;
  right: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  transition: all 0.5s ease-in-out;
  background-image: url('../imgs/exit_bg.png');
  background-repeat: no-repeat;
  cursor: pointer;
  &.collapsed {
    right: -50px;
  }
}
</style>
