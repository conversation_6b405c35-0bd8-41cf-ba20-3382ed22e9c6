<template>
  <el-dialog
    v-model="visible"
    :width="'800px'"
    :title="config.title || ''"
    :lock-scroll="false"
  >
    <div class="dialogPageContainer">
      <CardSearch
        ref="cardSearch"
        class="cardSearch"
        :config="cardSearchConfig"
      />
      <CardTable
        class="card-table"
        :config="cardTableConfig"
      />
      <AddOrUpdateDialog
        v-if="addOrUpdateConfig.visible"
        ref="addOrUpdate"
        :config="addOrUpdateConfig"
        @refreshData="refreshData"
      />
    </div>
  </el-dialog>
</template>

<script lang="ts" setup>
import useGlobal from '@/hooks/global/useGlobal'
import { getTenantAdminsAndSys, deleteUser } from '@/api/user'
import { ICardSearchIns } from '@/components/type'

const props = defineProps<{
  config:DialogConfig
}>()

const { $btnPerms, $message } = useGlobal()
const visible = ref<boolean>(false)
const close = () => {
  visible.value = false
}
const open = () => {
  visible.value = true
  refreshData()
}
const cardSearchConfig = reactive<ISearch>({

  filters: [{ label: '搜索', field: 'name', type: 'input' },
    {
      type: 'btn-group',
      btns: [
        { text: '查询', perm: true, click: () => refreshData() },
        {
          text: '添加',
          perm: true,
          click: () => {
            addOrUpdateConfig.value.title = '添加管理员'
            addOrUpdateConfig.value.visible = true
          }
        }, {
          text: '删除',
          perm: true,
          click: () => handleDelete(null)
        }
      ]
    }]
})

const cardTableConfig = reactive<ITable>({
  loading: false,
  dataList: [],
  selectList: [],
  handleSelectChange: val => {
    cardTableConfig.selectList = val
  },
  columns: [
    { prop: 'name', label: '名称' },
    { prop: 'firstName', label: '昵称' }
  ],
  operations: [
    {
      text: '编辑',
      isTextBtn: true,
      perm: true || $btnPerms('pc_gateway_hostTable_edit'),
      icon: 'iconfont icon-bianji',
      click: row => {
        addOrUpdateConfig.value.title = '编辑管理员'
        addOrUpdateConfig.value.defaultValue = {
          ...row,
          ...JSON.parse(row.additionalInfo)
        }
        addOrUpdateConfig.value.visible = true
      }
    },
    {
      text: '删除',
      isTextBtn: true,
      // perm: $btnPerms('pc_gateway_hostTable_del'),
      perm: true,
      icon: 'iconfont icon-shanchu',
      click: row => handleDelete(row)
    }
  ],
  operationWidth: '160px',
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    layout: 'total, sizes, prev, pager, next, jumper',
    handleSize: val => {
      cardTableConfig.pagination.limit = val
      refreshData()
    },
    handlePage: val => {
      cardTableConfig.pagination.page = val
      refreshData()
    }
  }
})

const addOrUpdateConfig = ref<AOUConfig>({
  visible: false,
  title: '',
  close: () => (addOrUpdateConfig.value.visible = false),
  editUrl: 'api/device',
  addUrl:
        'api/newUser?istarCreatePasswordUrl='
        + encodeURIComponent('https://ems.istarscloud.com/createPassword'),
  defaultValue: {},
  columns: [
    {
      type: 'input',
      label: '用户邮箱',
      key: 'email',
      rules: [{ required: true, validator: checkEmail, trigger: 'blur' }],
      message: '管理员账号即邮箱'
    },
    {
      type: 'password',
      label: '密码:',
      key: 'password',
      rules: [{ required: true, message: '请输入密码' }]
    },
    {
      type: 'input',
      label: '联系手机',
      key: 'phone',
      rules: [{ required: true, validator: checkPhone, trigger: 'blur' }]
    },
    {
      type: 'select',
      label: '管理员权限:',
      key: 'authority',
      options: [{ value: 'TENANT_ADMIN', label: '企业管理员' }],
      rules: [{ required: true, message: '请选择管理员权限', trigger: 'change' }]
    },
    {
      type: 'input',
      label: '昵称',
      key: 'lastName',
      rules: [
        { validator: validateName, trigger: 'blur' },
        { required: true, message: '请输入昵称', trigger: 'blur' },
        { max: 16, message: '昵称输入不可超过16位', trigger: 'blur' }
      ]
    }
  ],
  setSubmitParams(params) {
    params.firstName = params.lastName
    params.name = params.email
    params.tenantId = props.config.currentId
    return params
  }
})

const cardSearch = ref<ICardSearchIns>()

const refreshData = async () => {
  const params: any = {}
  if (cardSearch.value) {
    params.name = cardSearch.value?.queryParams?.name
  }
  const res = await getTenantAdminsAndSys((props.config.currentId as any).id)
  cardTableConfig.dataList = res.data.data
}

const handleDelete = async (row: any) => {
  if (row) {
    await deleteUser(row.id.id)
    $message.success('删除成功')
    refreshData()
  } else {
    await cardTableConfig.selectList?.forEach(async item => {
      await deleteUser(item.id.id)
    })
    $message.success('删除成功')
    refreshData()
  }
}

function checkEmail(rule, value, callback) {
  const mailReg = /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+(.[a-zA-Z0-9_-])+/
  if (!value) {
    return callback(new Error('邮箱不能为空'))
  }
  setTimeout(() => {
    if (mailReg.test(value)) {
      callback()
    } else {
      callback(new Error('请输入正确的邮箱格式'))
    }
  }, 100)
}

function validateName(rules, value, callback) {
  if (value.trim() === '') {
    callback(new Error('名称不可为空，请输入'))
  } else {
    callback()
  }
}

function checkPhone(rule, value, callback) {
  const phoneReg = /^1[34578]\d{9}$$/
  if (!value) {
    return callback(new Error('电话号码不能为空'))
  }
  setTimeout(() => {
    // Number.isInteger是es6验证数字是否为整数的方法,实际输入的数字总是识别成字符串
    // 所以在前面加了一个+实现隐式转换

    if (!Number.isInteger(+value)) {
      callback(new Error('请输入数字值'))
    } else if (phoneReg.test(value)) {
      callback()
    } else {
      callback(new Error('电话号码格式不正确'))
    }
  }, 100)
}

// onMounted(() => {
//   refreshData()
// })

defineExpose({
  visible,
  cardSearch,
  cardSearchConfig,
  cardTableConfig,
  refreshData,
  addOrUpdateConfig,
  open,
  close
})
</script>

<style lang="scss">
.dialogPageContainer {
  .cardSearch {
    margin: 0 !important;
    border-radius: 0;
  }

  .card-table {
    height: 400px;
  }
}
</style>
