import{U as l}from"./pe-B8dP0-Ut.js";import{cw as n,cx as m,cy as p}from"./MapView-DaoQedLH.js";import"./index-r0dFAfgr.js";import"./Point-WxyopZva.js";import"./widget-BcWKanF2.js";function f(e,i,t){const r=e.thumbnail&&e.thumbnail.url;return r?l(r,{responseType:"image"}).then(o=>{const s=u(o.data,t);return t&&t.node?(t.node.appendChild(s),t.node):s}):n(e).then(o=>o?i(o,t):null)}function u(e,i){const t=!/\\.svg$/i.test(e.src)&&i&&i.disableUpsampling,r=Math.max(e.width,e.height);let o=i&&i.maxSize!=null?m(i.maxSize):p.maxSize;t&&(o=Math.min(r,o));const s=typeof(i==null?void 0:i.size)=="number"?i==null?void 0:i.size:null,a=Math.min(o,s!=null?m(s):r);if(a!==r){const h=e.width!==0&&e.height!==0?e.width/e.height:1;h>=1?(e.width=a,e.height=a/h):(e.width=a*h,e.height=a)}return e}export{f as previewWebStyleSymbol};
