/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.service.mail;

import org.junit.Test;
import org.mockito.Mockito;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.Profile;
import org.thingsboard.rule.engine.api.MailService;
import org.thingsboard.server.common.data.User;
import org.thingsboard.server.common.data.utils.DateUtils;
import org.thingsboard.server.common.data.alarm.AlarmReport;
import org.thingsboard.server.common.data.exception.ThingsboardException;

@Profile("test")
@Configuration
public class TestMailService {

    public static String currentActivateToken;
    public static String currentResetPasswordToken;

    @Bean
    @Primary
    public MailService mailService() throws ThingsboardException {
        MailService mailService = Mockito.mock(MailService.class);
        Mockito.doAnswer(new Answer<Void>() {
            public Void answer(InvocationOnMock invocation) {
                Object[] args = invocation.getArguments();
                String activationLink = (String) args[0];
                currentActivateToken = activationLink.split("=")[1];
                return null;
            }
        }).when(mailService).sendActivationEmail(Mockito.anyString(), Mockito.anyString());
        Mockito.doAnswer(new Answer<Void>() {
            public Void answer(InvocationOnMock invocation) {
                Object[] args = invocation.getArguments();
                String passwordResetLink = (String) args[0];
                currentResetPasswordToken = passwordResetLink.split("=")[1];
                return null;
            }
        }).when(mailService).sendResetPasswordEmail(Mockito.anyString(), Mockito.anyString());
        return mailService;
    }



    @Test
    public void name() throws ThingsboardException {
//        MailService mailService = Mockito.mock(MailService.class);
//        AlarmReport alarmReport = new AlarmReport();
//        alarmReport.setTime(DateUtils.date2Str(System.currentTimeMillis(), DateUtils.DATE_FORMATE_DEFAULT_2));
//        alarmReport.setAlarmValue("25");
//        alarmReport.setSetValue("22");
//        alarmReport.setProp("A相电压");
//        alarmReport.setDeviceName("1#变压器");
//        mailService.sendSMS("18408226899",alarmReport);
    }

}
