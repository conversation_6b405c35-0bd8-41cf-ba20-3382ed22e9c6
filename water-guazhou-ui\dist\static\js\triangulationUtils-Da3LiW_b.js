import{r as x}from"./earcut-BJup91r2.js";import{dE as O}from"./MapView-DaoQedLH.js";import{r as _}from"./deduplicate-Clsym5GM.js";import{aR as E}from"./index-r0dFAfgr.js";function L(n,t=!1){return n<=E?t?new Array(n).fill(0):new Array(n):new Float64Array(n)}function R(n){return length<=E?Array.from(n):new Float64Array(n)}function H(n,t,g){return Array.isArray(n)?n.slice(t,t+g):n.subarray(t,t+g)}function Z(n){const t=S(n.rings,n.hasZ,m.CCW_IS_HOLE),g=new Array;let r=0,i=0;for(const o of t.polygons){const c=o.count,s=o.index,y=H(t.position,3*s,3*c),p=o.holeIndices.map(f=>f-s),a=new Uint32Array(x(y,p,3));g.push({position:y,faces:a}),r+=y.length,i+=a.length}const e=N(g,r,i),h=Array.isArray(e.position)?_(e.position,3,{originalIndices:e.faces}):_(e.position.buffer,6,{originalIndices:e.faces});return e.position=new Float64Array(h.buffer),e.faces=h.indices,e}function N(n,t,g){if(n.length===1)return n[0];const r=L(t),i=new Uint32Array(g);let e=0,h=0,o=0;for(const c of n){for(let s=0;s<c.position.length;s++)r[e++]=c.position[s];for(let s=0;s<c.faces.length;s++)i[h++]=c.faces[s]+o;o=e/3}return{position:r,faces:i}}function S(n,t,g){const r=n.length,i=new Array(r),e=new Array(r),h=new Array(r);let o=0,c=0,s=0,y=0;for(let f=0;f<r;++f)y+=n[f].length;const p=L(3*y);let a=0;for(let f=r-1;f>=0;f--){const l=n[f],I=g===m.CCW_IS_HOLE&&W(l);if(I&&r!==1)i[o++]=l;else{let C=l.length;for(let u=0;u<o;++u)C+=i[u].length;const A={index:a,pathLengths:new Array(o+1),count:C,holeIndices:new Array(o)};A.pathLengths[0]=l.length,l.length>0&&(h[s++]={index:a,count:l.length}),a=I?w(l,l.length-1,-1,p,a,l.length,t):w(l,0,1,p,a,l.length,t);for(let u=0;u<o;++u){const d=i[u];A.holeIndices[u]=a,A.pathLengths[u+1]=d.length,d.length>0&&(h[s++]={index:a,count:d.length}),a=w(d,0,1,p,a,d.length,t)}o=0,A.count>0&&(e[c++]=A)}}for(let f=0;f<o;++f){const l=i[f];l.length>0&&(h[s++]={index:a,count:l.length}),a=w(l,0,1,p,a,l.length,t)}return e.length=c,h.length=s,{position:p,polygons:e,outlines:h}}function w(n,t,g,r,i,e,h){i*=3;for(let o=0;o<e;++o){const c=n[t];r[i++]=c[0],r[i++]=c[1],r[i++]=h?c[2]:0,t+=g}return i/3}function W(n){return!O(n,!1,!1)}var m;(function(n){n[n.NONE=0]="NONE",n[n.CCW_IS_HOLE=1]="CCW_IS_HOLE"})(m||(m={}));export{H as a,m as f,Z as i,S as l,L as n,R as t};
