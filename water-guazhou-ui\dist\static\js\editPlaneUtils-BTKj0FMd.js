import{I as $t,$ as Pt,w as Ut,e as s,y as l,a as Y,Q as Et,as as Nt}from"./Point-WxyopZva.js";import{u as At,b as Lt,fQ as bt,v as Dt,dE as kt,ez as S,af as Mt,fR as Zt,f as Yt,g as jt,ag as qt,aQ as F,fS as st,fT as lt,aP as W,fU as pt,e0 as A,fV as ct,fW as Ft,fX as Wt,fY as Ht,fZ as J,f_ as ht,aW as O,ac as ut,ae as Qt,ab as Xt,aT as dt,ea as K}from"./MapView-DaoQedLH.js";import{c as Bt,l as yt,w as gt}from"./widget-BcWKanF2.js";import{a5 as Jt,fp as Kt,fq as ot,R as y,T as m,aW as j,aO as te,b2 as ee,$}from"./index-r0dFAfgr.js";import{t as H,j as M,U as k,a as ft,b as ie,c as tt,m as oe,p as vt,g as Q}from"./automaticLengthMeasurementUtils-DljoUgEz.js";import{h as re}from"./GraphicsLayer-DTrBRwJQ.js";import{g as ae}from"./elevationInfoUtils-5B4aSzEU.js";import{R as g,O as ne,Q as se,S as It,g as Vt,T as mt,U as le,W as pe,X as ce,Y as he}from"./AnimatedLinesLayer-B2VbV4jv.js";import{simplify as ue,distance as de}from"./geometryEngine-OGzB5MRq.js";import{i as wt}from"./automaticAreaMeasurementUtils-DfgMw58X.js";import{W as zt,J as ye}from"./boundedPlane-DeyjpfhM.js";import{t as q,c as x}from"./sphere-NgXH-gLx.js";function U(t,e){const a=new Ut({x:t[0],y:t[1],spatialReference:e});return t.length>2&&(a.z=t[2]),a}function ge(t,e){return new At({points:t,spatialReference:e})}function xt(t,e,a){const o=new Lt({paths:t,spatialReference:e});return a&&bt(o),o}function Z(t,e,a,o=!0){const r=Jt(t);r.forEach(n=>{const c=n[0],p=n[n.length-1];Kt(c,p)&&n.length!==1||n.push(n[0])});let i=new Dt({rings:r,spatialReference:e});return i.rings.forEach(n=>{kt(n,!1,!1)||n.reverse()}),a&&bt(i),o&&i.isSelfIntersecting&&$t(e)&&(i=ue(i)),i}function _t(t,e,a){const o=e.mapToLocalMultiple(t),r=[],i={x:o[0].x,y:o[0].y},n={x:o[1].x,y:o[1].y},c=Math.round(n.x-i.x),p=Math.round(n.y-i.y),u=Math.max(Math.abs(c),Math.abs(p));if(a){const d={x:i.x+u,y:i.y+u},f={x:i.x-u,y:i.y-u};r.push(g(d.x,f.y),g(f.x,f.y),g(f.x,d.y),g(d.x,d.y))}else{const d={x:c>0?i.x+u:i.x-u,y:p>0?i.y+u:i.y-u};r.push(g(i.x,i.y),g(d.x,i.y),g(d.x,d.y),g(i.x,d.y))}return Gt(Z([ot(r.map(d=>e.localToMap(d)))],e.spatialReference,e.doUnnormalization,!0),r,e)}function fe(t,e,a){let o=e.mapToLocalMultiple(t);if(o.length===1){const p=o[0];o=[g(p.x-48,p.y+48),g(p.x+48,p.y-48),g(p.x+48,p.y-48),g(p.x-48,p.y+48)]}const r=[],i={x:o[0].x,y:o[0].y},n={x:o[1].x,y:o[1].y};if(a){const c=Math.round(n.x-i.x),p=Math.round(n.y-i.y);r.push(g(i.x-c,i.y-p),g(n.x,i.y-p),g(n.x,n.y),g(i.x-c,n.y))}else r.push(g(i.x,i.y),g(n.x,i.y),g(n.x,n.y),g(i.x,n.y));return Gt(Z([ot(r.map(c=>e.localToMap(c)))],e.spatialReference,e.doUnnormalization,!0),r,e)}function Gt(t,e,a){const o=X(e[3],e[2],a),r=X(e[1],e[2],a),i=X(e[0],e[1],a),n=X(e[0],e[3],a);return{geometry:t,midpoints:y(o)&&y(r)&&y(i)&&y(n)?{top:o,right:r,bottom:i,left:n}:null}}function X(t,e,a){L[0]=t.x,L[1]=t.y,L[2]=0,D[0]=e.x,D[1]=e.y,D[2]=0,S(L,L,D,.5),B.x=L[0],B.y=D[1],B.z=D[2];const o=a.localToMap(B);return y(o)?U(o,a.spatialReference):null}const B=g(0,0,0),L=Mt(),D=Mt();function Ot(t,e,a,o){const r=e.mapToLocalMultiple(t);let i=null,n=null;if(a)i=r[0],n=r[1];else{const v=r[0],_=r[1],E=Math.round(_.x-v.x),N=Math.round(_.y-v.y),T=Math.max(Math.abs(E),Math.abs(N));i=g(E>0?v.x+T/2:v.x-T/2,N>0?v.y+T/2:v.y-T/2),n=g(Math.abs(E)>Math.abs(N)?i.x-T/2:i.x,Math.abs(E)>Math.abs(N)?i.y:i.y-T/2)}const c=e.localToMap(i),p=e.localToMap(n);if(m(c)||m(p))return null;e.doUnnormalization&&Zt([[c,p]],e.spatialReference);const u=U(c,e.spatialReference),d=U(p,e.spatialReference),f=Pt(e.spatialReference);let w=0;if($t(e.spatialReference))w=f*de(u,d,null);else{const v=i.x-n.x,_=i.y-n.y;w=f*Math.sqrt(v*v+_*_)*1}const R=new ne({center:u,radius:w,radiusUnit:"meters",spatialReference:e.spatialReference});return{geometry:Z(R.rings,R.spatialReference,!1),center:u,edge:d}}function ve(t,e,a){const o=e.mapToLocalMultiple(t),r=o[0],i=o[1],n=Math.round(i.x-r.x),c=Math.round(i.y-r.y),p=g(a?r.x:r.x+n/2,a?r.y:r.y+c/2),u=a?n:n/2,d=a?c:c/2,f=60,w=[],R=2*Math.PI/f;function v(I){const St=Math.cos(I),Ct=Math.sin(I);return g(u*St+p.x,d*Ct+p.y)}for(let I=0;I<f;I++)w.push(v(I*R));w.push(w[0]);const{spatialReference:_,doUnnormalization:E}=e,N=Z([ot(w.map(I=>e.localToMap(I)))],_,E,!1),T=e.localToMap(v(Math.PI/2)),rt=e.localToMap(v(0)),at=e.localToMap(v(-Math.PI/2)),nt=e.localToMap(v(Math.PI));return{geometry:N,midpoints:y(T)&&y(rt)&&y(at)&&y(nt)?{top:U(T,_),right:U(rt,_),bottom:U(at,_),left:U(nt,_)}:null}}function et(t,e){switch(t){case"point":case"multipoint":return"point";case"polyline":return(y(e)&&e.type==="polyline"&&e.paths.length?e.paths[0].length:0)<2?"polylineZeroVertices":"polylineOneVertex";case"polygon":{const a=y(e)&&e.type==="polygon"&&e.rings.length?e.rings[0].length:0;return a<3?"polylineZeroVertices":a<4?"polygonOneVertex":"polygonTwoVertices"}default:return}}let C=class extends H{constructor(t){super(t),this.type="draw-point",this.helpMessage=void 0}};s([l()],C.prototype,"type",void 0),s([l()],C.prototype,"elevation",void 0),s([l()],C.prototype,"viewType",void 0),s([l()],C.prototype,"helpMessage",void 0),C=s([Y("esri.views.interactive.tooltip.DrawPointTooltipInfo")],C);let V=class extends H{constructor(t){super(t),this.type="draw-polyline",this.totalLength=M,this.helpMessage=void 0}};s([l()],V.prototype,"type",void 0),s([l()],V.prototype,"elevation",void 0),s([l()],V.prototype,"totalLength",void 0),s([l()],V.prototype,"viewType",void 0),s([l()],V.prototype,"helpMessage",void 0),V=s([Y("esri.views.interactive.tooltip.DrawPolylineTooltipInfo")],V);let z=class extends H{constructor(t){super(t),this.type="draw-polygon",this.area=k,this.helpMessage=void 0}};s([l()],z.prototype,"type",void 0),s([l()],z.prototype,"elevation",void 0),s([l()],z.prototype,"area",void 0),s([l()],z.prototype,"viewType",void 0),s([l()],z.prototype,"helpMessage",void 0),z=s([Y("esri.views.interactive.tooltip.DrawPolygonTooltipInfo")],z);let P=class extends H{constructor(t){super(t),this.type="draw-rectangle",this.xSize=M,this.ySize=M,this.area=k}};s([l()],P.prototype,"type",void 0),s([l()],P.prototype,"xSize",void 0),s([l()],P.prototype,"ySize",void 0),s([l()],P.prototype,"area",void 0),P=s([Y("esri.views.interactive.tooltip.DrawRectangleTooltipInfo")],P);let G=class extends H{constructor(t){super(t),this.type="draw-circle",this.radius=null,this.xSize=null,this.ySize=null,this.area=k}};s([l()],G.prototype,"type",void 0),s([l()],G.prototype,"radius",void 0),s([l()],G.prototype,"xSize",void 0),s([l()],G.prototype,"ySize",void 0),s([l()],G.prototype,"area",void 0),G=s([Y("esri.views.interactive.tooltip.DrawCircleTooltipInfo")],G);let h=class extends Yt(Bt.EventedMixin(se)){constructor(t){super(t),this._graphic=null,this._createOperationGeometry=null,this.defaultZ=0,this.geometryType=null,this.hasZ=!0,this.labelOptions=new It,this.mode=null,this.snappingManager=null,this.snapToScene=!1,this.tooltip=null,this.tooltipOptions=new Vt}initialize(){this.internalGraphicsLayer=new re({listMode:"hide",internal:!0}),this.view.map.layers.add(this.internalGraphicsLayer),this.drawOperation=this.makeDrawOperation(),this.handles.add([this.drawOperation.on("vertex-add",t=>this.onVertexAdd(t)),this.drawOperation.on("vertex-remove",t=>this.onVertexRemove(t)),this.drawOperation.on("vertex-update",t=>this.onVertexUpdate(t)),this.drawOperation.on("cursor-update",t=>this.onCursorUpdate(t)),this.drawOperation.on("complete",t=>this.onComplete(t)),yt(()=>this.tooltipOptions.enabled,t=>{this.tooltip=t?new oe({view:this.view,info:this._tooltipInfo}):j(this.tooltip)},gt),yt(()=>this._tooltipInfo,t=>{y(this.tooltip)&&(this.tooltip.info=t)},gt)]),this.finishToolCreation()}destroy(){this.drawOperation=j(this.drawOperation),this.tooltip=j(this.tooltip),this._destroyAllVisualisations(),this.view.map.remove(this.internalGraphicsLayer),this.internalGraphicsLayer=j(this.internalGraphicsLayer),this._set("view",null)}get _defaultElevation(){return ft(this.defaultZ,"meters")}get canRedo(){return this.drawOperation.canRedo}get canUndo(){return this.drawOperation.canUndo}set centered(t){this._set("centered",t),this._updateGraphic()}set enabled(t){this.drawOperation.interactive=t,this._set("enabled",t)}set forceUniformSize(t){this._set("forceUniformSize",t),this._updateGraphic()}get graphic(){return this._graphic}set graphicSymbol(t){this._set("graphicSymbol",t),y(this._graphic)&&(this._graphic.symbol=t)}get updating(){var t;return((t=this.drawOperation)==null?void 0:t.updating)??!1}completeCreateOperation(){this.drawOperation.complete()}onInputEvent(t){this.drawOperation.onInputEvent(t)}redo(){this.drawOperation.redo()}reset(){}undo(){this.drawOperation.undo()}_createGraphic(t){this._graphic=new jt({...this.graphicProperties,geometry:t,symbol:this.graphicSymbol}),this.internalGraphicsLayer.add(this._graphic),this.handles.add(this.initializeGraphic(this._graphic)),this.notifyChange("graphic"),this.handles.add(Et(()=>{y(this._graphic)&&(this.internalGraphicsLayer.remove(this._graphic),this._graphic=j(this._graphic))}),it)}_destroyAllVisualisations(){this.handles.remove(b.outline),this.handles.remove(b.regularVertices),this.handles.remove(b.activeVertex),this.handles.remove(it)}_getCreateOperationGeometry(t={operationComplete:!1}){if(this.drawOperation==null||this.drawOperation.numVertices===0)return null;const e=this.drawOperation.stagedVertex,a=this.drawOperation.committedVertices,o=a.slice();y(e)&&o.push(this.drawOperation.coordinateHelper.pointToArray(e));const r=y(e)?this.drawOperation.coordinateHelper.pointToArray(e):a.splice(-1)[0],i={regularVertices:null,activeVertex:null,full:null,outline:null,circle:null,rectangle:null},n=o.length,c=this.view.spatialReference,p=this.view.type==="3d"&&this.view.viewingMode==="global";switch(this.geometryType){case"point":i.regularVertices=a,i.activeVertex=r,i.full=this.drawOperation.coordinateHelper.arrayToPoint(o[0]);break;case"multipoint":i.regularVertices=a,i.activeVertex=r,n>0&&(i.full=ge(o,c));break;case"polyline":i.regularVertices=a,i.activeVertex=r,n>0&&(i.full=xt([o],c,p));break;case"polygon":i.regularVertices=a,i.activeVertex=r,n>0&&(i.full=Z([o],c,p,!0));break;case"circle":if(n>0){const u=mt(this.view,o[0]);if(n===1&&t.operationComplete){const d=o[0],f=u.makeMapPoint(d[0]+Tt*this.view.resolution,d[1]);i.circle=Ot([d,f],u,!0),i.full=y(i.circle)?i.circle.geometry:null}else n===2&&(this.forceUniformSize?(i.circle=Ot(o,u,this.centered),i.full=y(i.circle)?i.circle.geometry:null):(i.rectangle=ve(o,u,this.centered),i.full=i.rectangle.geometry))}break;case"rectangle":if(n>0){const u=mt(this.view,o[0]);if(n===1&&t.operationComplete){const d=o[0],f=u.makeMapPoint(d[0]+Tt*this.view.resolution,d[1]);i.rectangle=_t([d,f],u,!0),i.full=i.rectangle.geometry}else n===2&&(i.rectangle=this.forceUniformSize?_t(o,u,this.centered):fe(o,u,this.centered),i.full=i.rectangle.geometry)}break;default:return null}switch(this.geometryType){case"point":case"multipoint":break;case"polyline":case"polygon":n>1&&(i.outline=xt([o],c,p));break;case"circle":case"rectangle":y(i.full)&&i.full.type==="polygon"&&(i.outline=Z(i.full.rings,c,p))}return i}initializeGraphic(t){return null}onComplete(t){this._updateGraphic();let e=null;if(this.drawOperation.isCompleted){const a=this._getCreateOperationGeometry({operationComplete:!0});y(a)&&(m(this._graphic)?this._createGraphic(a.full):this._graphic.geometry=a.full,e=te(this._graphic).clone())}this._createOperationGeometry=null,this.emit("complete",{graphic:e,...t})}onCursorUpdate(t){this._updateGraphic(),this.emit("cursor-update",t)}onDeactivate(){this.drawOperation.isCompleted||this.drawOperation.cancel()}onVertexAdd(t){this._updateGraphic(),this.emit("vertex-add",t)}onVertexRemove(t){this._updateGraphic(),this.emit("vertex-remove",t)}onVertexUpdate(t){this._updateGraphic(),this.emit("vertex-update",t)}_updateGraphic(){const t=this._getCreateOperationGeometry();this._createOperationGeometry=t,m(t)?this._destroyAllVisualisations():(y(t.outline)?this.handles.add(this.onOutlineChanged(t.outline),b.outline):this.handles.remove(b.outline),y(t.regularVertices)?this.handles.add(this.onRegularVerticesChanged(t.regularVertices),b.regularVertices):this.handles.remove(b.regularVertices),y(t.activeVertex)?this.handles.add(this.onActiveVertexChanged(t.activeVertex),b.activeVertex):this.handles.remove(b.activeVertex),y(t.full)?m(this._graphic)?this._createGraphic(t.full):this._graphic.geometry=t.full:this.handles.remove(it))}get _tooltipInfo(){const{drawOperation:t}=this;if(!t)return null;switch(this.geometryType){case"point":return this._drawPointTooltipInfo;case"polyline":return this._drawPolylineTooltipInfo;case"polygon":return this._drawPolygonTooltipInfo;case"rectangle":return this._drawRectangleTooltipInfo;case"circle":return this._drawCircleTooltipInfo;default:return null}}get _drawPointTooltipInfo(){const t=ee(this.graphic,e=>e.geometry);return m(t)||t.type!=="point"||this.view.type==="2d"&&this.defaultZ===0?null:new C({tooltipOptions:this.tooltipOptions,elevation:this._elevationTooltipDetail,viewType:this.view.type,helpMessage:et("point",t)})}get _drawPolylineTooltipInfo(){const t=this._createOperationGeometry,e=y(t)?t.full:null;if(m(e)||e.type!=="polyline")return null;const a=ie(e,this._elevationMode);return new V({tooltipOptions:this.tooltipOptions,elevation:this._elevationTooltipDetail,totalLength:$(a,M),viewType:this.view.type,helpMessage:et("polyline",e)})}get _drawPolygonTooltipInfo(){const t=this._createOperationGeometry,e=y(t)?t.full:null;if(m(e)||e.type!=="polygon")return null;const a=wt(e,this._elevationMode);return new z({tooltipOptions:this.tooltipOptions,elevation:this._elevationTooltipDetail,area:$(a,k),viewType:this.view.type,helpMessage:et("polygon",e)})}get _drawRectangleTooltipInfo(){return m(this.drawOperation)?null:new P({tooltipOptions:this.tooltipOptions,xSize:$(this._xSize,M),ySize:$(this._ySize,M),area:$(this._fullGeometryArea,k)})}get _drawCircleTooltipInfo(){if(m(this.drawOperation))return null;const t=this.forceUniformSize;return new G({tooltipOptions:this.tooltipOptions,radius:t?$(this._circleRadius,M):null,xSize:t?null:$(this._xSize,M),ySize:t?null:$(this._ySize,M),area:$(this._fullGeometryArea,k)})}get _circleRadius(){const t=this._createOperationGeometry;return y(t)&&y(t.circle)&&y(t.circle.center)&&y(t.circle.edge)?tt(t.circle.center,t.circle.edge,this._elevationMode):null}get _xSize(){const t=this._createOperationGeometry;if(m(t)||m(t.rectangle))return null;const{midpoints:e}=t.rectangle;return y(e)?tt(e.left,e.right,this._elevationMode):null}get _ySize(){const t=this._createOperationGeometry;if(m(t)||m(t.rectangle))return null;const{midpoints:e}=t.rectangle;return y(e)?tt(e.top,e.bottom,this._elevationMode):null}get _fullGeometryArea(){const t=this._createOperationGeometry,e=y(t)?t.full:null;return m(e)||e.type!=="polygon"?null:wt(e,this._elevationMode)}get _elevationTooltipDetail(){return{mode:this.drawOperation.elevationInfo.mode,...this._vertexTooltipElevation}}get _vertexTooltipElevation(){const{tooltipOptions:t,view:e,drawOperation:a}=this;if(m(a))return this._defaultElevation;const o=a.stagedVertex??a.lastVertex;if(m(o)||e.type==="2d")return this._defaultElevation;const r={mode:t.elevation.mode,offset:0},i=(ae(e,o,a.elevationInfo,r)??0)*Nt(o.spatialReference);return ft(i,"meters")}get _elevationMode(){return this.drawOperation.isDraped?"on-the-ground":"absolute-height"}};s([l()],h.prototype,"_createOperationGeometry",void 0),s([l()],h.prototype,"_defaultElevation",null),s([l({value:!0})],h.prototype,"centered",null),s([l({nonNullable:!0})],h.prototype,"defaultZ",void 0),s([l()],h.prototype,"drawOperation",void 0),s([l({value:!0})],h.prototype,"enabled",null),s([l({value:!0})],h.prototype,"forceUniformSize",null),s([l({constructOnly:!0})],h.prototype,"geometryType",void 0),s([l()],h.prototype,"graphic",null),s([l({constructOnly:!0})],h.prototype,"graphicProperties",void 0),s([l()],h.prototype,"graphicSymbol",null),s([l({constructOnly:!0})],h.prototype,"hasZ",void 0),s([l({constructOnly:!0,type:It})],h.prototype,"labelOptions",void 0),s([l({constructOnly:!0})],h.prototype,"mode",void 0),s([l()],h.prototype,"snappingManager",void 0),s([l()],h.prototype,"snapToScene",void 0),s([l()],h.prototype,"tooltip",void 0),s([l({constructOnly:!0,type:Vt})],h.prototype,"tooltipOptions",void 0),s([l({readOnly:!0})],h.prototype,"type",void 0),s([l({readOnly:!0})],h.prototype,"updating",null),s([l({constructOnly:!0,nonNullable:!0})],h.prototype,"view",void 0),s([l()],h.prototype,"_tooltipInfo",null),s([l()],h.prototype,"_drawPointTooltipInfo",null),s([l()],h.prototype,"_drawPolylineTooltipInfo",null),s([l()],h.prototype,"_drawPolygonTooltipInfo",null),s([l()],h.prototype,"_drawRectangleTooltipInfo",null),s([l()],h.prototype,"_drawCircleTooltipInfo",null),s([l()],h.prototype,"_circleRadius",null),s([l()],h.prototype,"_xSize",null),s([l()],h.prototype,"_ySize",null),s([l()],h.prototype,"_fullGeometryArea",null),s([l()],h.prototype,"_elevationTooltipDetail",null),s([l()],h.prototype,"_vertexTooltipElevation",null),s([l()],h.prototype,"_elevationMode",null),h=s([Y("esri.views.draw.DrawGraphicTool")],h);const it="create-operation-graphic",b={outline:"outline-visual",regularVertices:"regular-vertices-visual",activeVertex:"active-vertex-visual"};function Ce(t){switch(t){case"point":case"polyline":case"polygon":case"multipoint":return t;case"circle":case"rectangle":return"segment"}}const Tt=48;function Pe(t,e){return Rt(t,e,!1)}function Ue(t,e){return Rt(t,e,!0)}function Rt(t,e,a){if(t instanceof le){if(t.operation instanceof pe)return me(t.operation,e,a),!0;if(t.operation instanceof ce)return we(t.operation,e,a),!0;if(t.operation instanceof he)return xe(t.operation,e,a),!0}return!1}function me(t,e,a=!1){const o=a?-1:1,r=qt(o*t.dx,o*t.dy,o*t.dz);F(e.origin,e.origin,r)}function we(t,e,a=!1){const o=a?-t.angle:t.angle;st(e.basis1,e.basis1,lt,o),st(e.basis2,e.basis2,lt,o)}function xe(t,e,a=!1){const o=a?1/t.factor1:t.factor1,r=a?1/t.factor2:t.factor2;W(e.basis1,e.basis1,o),W(e.basis2,e.basis2,r),pt(e.origin,e.origin,t.origin,t.axis1,o),pt(e.origin,e.origin,t.origin,t.axis2,r)}function Ee(t,e,a,o){o||(o=zt());const r=A(q.get(),t[1],-t[0]),i=A(q.get(),Number.POSITIVE_INFINITY,Number.POSITIVE_INFINITY),n=A(q.get(),Number.NEGATIVE_INFINITY,Number.NEGATIVE_INFINITY),c=q.get();e.components.forEach(d=>d.vertices.forEach(f=>{const w=f.pos;A(c,ct(t,w),ct(r,w)),Ft(i,i,c),Wt(n,n,c)}));const p=1e-6,u=A(q.get(),n[0]-i[0]<p?a/2:0,n[1]-i[1]<p?a/2:0);return Ht(i,i,u),J(n,n,u),ht(o.basis1,t,(n[0]-i[0])/2),ht(o.basis2,r,(n[1]-i[1])/2),A(o.origin,i[0]*t[0]+i[1]*r[0],i[0]*t[1]+i[1]*r[1]),J(o.origin,o.origin,o.basis1),J(o.origin,o.origin,o.basis2),o}function Ne(t,e,a,o=0,r){r||(r=zt()),e.toRenderCoords(t.origin,a,r.origin);const i=x.get();F(i,t.origin,t.basis1),F(i,i,t.basis2),e.toRenderCoords(i,a,i);const n=x.get();F(n,t.origin,t.basis1),O(n,n,t.basis2),e.toRenderCoords(n,a,n);const c=x.get();O(c,t.origin,t.basis1),O(c,c,t.basis2),e.toRenderCoords(c,a,c);const p=x.get();O(p,t.origin,t.basis1),F(p,p,t.basis2),e.toRenderCoords(p,a,p);const u=S(x.get(),i,n,.5);O(u,u,r.origin);const d=S(x.get(),c,p,.5);O(d,r.origin,d),S(r.basis1,u,d,.5);const f=S(x.get(),p,i,.5);O(f,f,r.origin);const w=S(x.get(),n,c,.5);O(w,r.origin,w),S(r.basis2,f,w,.5);const R=ut(x.get(),r.basis1,r.basis2),v=ut(R,R,r.basis1);return Qt(v,v),W(r.basis2,v,Xt(r.basis2,v)),W(r.basis1,r.basis1,1+o/dt(r.basis1)),W(r.basis2,r.basis2,1+o/dt(r.basis2)),ye(r),r}function Ae(t,e,a,o){const r=x.get();O(r,O(r,t.origin,t.basis1),t.basis2);const i=x.get();K(i,r,t.basis1,2);const n=x.get();K(n,i,t.basis2,2);const c=x.get();K(c,r,t.basis2,2),r[2]=i[2]=n[2]=c[2]=e;const p=o?"on-the-ground":"absolute-height",u=vt(Q(r,i,a,p),Q(c,n,a,p)),d=vt(Q(i,n,a,p),Q(r,c,a,p));return m(d)||m(u)?null:[u,d]}export{Ee as A,Ce as B,Ne as G,h as H,Ae as M,Pe as S,Ue as Y};
