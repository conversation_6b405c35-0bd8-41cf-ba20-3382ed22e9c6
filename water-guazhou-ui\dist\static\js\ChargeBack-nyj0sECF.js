import{d as I,c as p,r as l,s as v,bF as S,S as k,b as g,D as L,u as O,o as B,g as D,n as G,q as c,F as N,G as x,a9 as V,b6 as T,al as H,b7 as w}from"./index-r0dFAfgr.js";import{_ as P}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as K}from"./CardTable-rdWOL4_6.js";import{_ as F}from"./CardSearch-CB_HNR-Q.js";import{C as M,c as q,a as Y}from"./index-CpGhZCTT.js";import U from"./detail-CU6-qhMl.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";/* empty css                         */import"./config-DqqM5K5L.js";import"./DateFormatter-Bm9a68Ax.js";import"./OutsideWorkOrder-C6s8joBt.js";import"./detailSteps-BqRp_Y4m.js";/* empty css                */import"./index-CCFuhOrs.js";/* empty css                             */import"./DPlayer-Be2AurQX.js";import"./DPlayer.min-_HMH7IVX.js";const J={class:"wrapper"},ue=I({__name:"ChargeBack",setup($){const m=p(),d=p(),_=p(),u=l({WorkOrderEmergencyLevelList:[]});function A(){Y("1").then(e=>{u.WorkOrderEmergencyLevelList=V(e.data.data||[],"children",{label:"name",value:"id"})})}const W=l({filters:[{type:"radio-button",label:"类别",field:"status",labelWidth:40,options:[{label:"待审核",value:"CHARGEBACK_REVIEW"},{label:"已审核",value:"CHARGEBACK"}],onChange:e=>{var o;const t=e==="CHARGEBACK_REVIEW"?["详情","审核"]:["详情"],r=e==="CHARGEBACK_REVIEW"?100:80;a.operationWidth=r,(o=a.operations)==null||o.map(s=>{const i=s.text;s.perm=t.indexOf(i)!==-1}),n()}},{type:"input",label:"标题",field:"title",onChange:()=>n()},{type:"btn-group",btns:[{perm:!0,text:"查询",svgIcon:v(H),click:()=>n()},{perm:!0,text:"重置",type:"default",svgIcon:v(w),click:()=>{var e;(e=m.value)==null||e.resetForm(),n()}}]}],defaultParams:{status:"CHARGEBACK_REVIEW"},handleSearch:()=>n()}),a=l({columns:[{minWidth:180,prop:"serialNo",label:"工单编号"},{minWidth:120,prop:"level",label:"紧急程度",tag:!0,tagColor:e=>{var t;return((t=u.WorkOrderEmergencyLevelList.find(r=>r.value===e.level))==null?void 0:t.color)||""},formatter:e=>{var t;return(t=u.WorkOrderEmergencyLevelList.find(r=>r.value===e.level))==null?void 0:t.label}},{minWidth:120,prop:"type",label:"类型"},{minWidth:120,prop:"title",label:"标题"},{minWidth:200,prop:"address",label:"地址"},{minWidth:160,prop:"createTime",label:"工单发起时间",formatter:e=>S(e.createTime).format("YYYY-MM-DD HH:mm:ss")},{minWidth:120,prop:"status",label:"状态",formatter:e=>{switch(e.status){case"PENDING":case"ASSIGN":return"待处理";case"RESOLVING":case"ARRIVING":case"PROCESSING":case"SUBMIT":case"REVIEW":case"CHARGEBACK_REVIEW":case"HANDOVER_REVIEW":case"REASSIGN":case"COLLABORATION":return"处理中";case"APPROVED":case"CHARGEBACK":case"TERMINATED":return"已结束"}}}],dataList:[],pagination:{refreshData:({page:e,size:t})=>{a.pagination.limit=t,a.pagination.page=e,n()}},operations:[{perm:!0,isTextBtn:!0,text:"详情",click:e=>{var t;h.value=e.id||"",E.title=e.serialNo,(t=_.value)==null||t.openDrawer()}},{perm:!0,text:"审核",isTextBtn:!0,click:e=>{var t;a.currentRow=e,(t=d.value)==null||t.openDialog()}}]}),E=l({title:"流程明细",cancel:!1,className:"lightColor",group:[]}),h=p(""),f=l({dialogWidth:500,title:"退单审核",labelPosition:"top",group:[{fields:[{type:"radio",label:"审核结果：",field:"stage",options:[{label:"通过",value:"CHARGEBACK"},{label:"拒绝",value:"REJECTED"}],rules:[{required:!0,message:"请选择结果"}]},{type:"textarea",label:"备注：",field:"processRemark"}]}],submit:e=>{k("确定提交？","提示信息").then(async()=>{var t,r,o,s;f.submitting=!0;try{const i=await M((t=a.currentRow)==null?void 0:t.id,{processAdditionalInfo:JSON.stringify({}),...e});((r=i.data)==null?void 0:r.code)===200?(g.success("操作成功"),(o=d.value)==null||o.closeDialog(),n()):g.error(((s=i.data)==null?void 0:s.err)||"操作失败")}catch{g.error("系统错误")}f.submitting=!1}).catch(()=>{})}}),n=async()=>{var e,t,r,o,s,i,C;a.loading=!0;try{const b=((e=m.value)==null?void 0:e.queryParams)||{},y={page:a.pagination.page||1,size:a.pagination.limit||20,stepProcessUserId:L(((r=(t=O().user)==null?void 0:t.id)==null?void 0:r.id)||""),...b},R=await q(y);a.dataList=((s=(o=R.data)==null?void 0:o.data)==null?void 0:s.data)||[],a.pagination.total=((C=(i=R.data)==null?void 0:i.data)==null?void 0:C.total)||0}catch{}a.loading=!1};return B(()=>{A(),n()}),(e,t)=>{const r=F,o=K,s=P,i=T;return D(),G("div",J,[c(r,{ref_key:"refSearch",ref:m,config:W},null,8,["config"]),c(o,{class:"card-table",config:a},null,8,["config"]),c(s,{ref_key:"refForm",ref:d,config:f},null,8,["config"]),c(i,{ref_key:"refdetail",ref:_,config:E},{default:N(()=>[c(U,{id:h.value},null,8,["id"]),t[0]||(t[0]=x("4 "))]),_:1},8,["config"])])}}});export{ue as default};
