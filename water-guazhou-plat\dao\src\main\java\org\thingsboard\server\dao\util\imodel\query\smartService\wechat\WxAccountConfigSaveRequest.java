package org.thingsboard.server.dao.util.imodel.query.smartService.wechat;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartService.wechat.WxAccountConfig;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;

import java.util.Date;

@Getter
@Setter
public class WxAccountConfigSaveRequest extends SaveRequest<WxAccountConfig> {
    // URL
    private String url;

    // 用户Token，后台自动获取
    private String token;

    // token过期时间
    private Date expireTime;

    // 公众号名称
    private String name;

    // 公众号ID
    private String wxId;

    // 第三方用户唯一凭证
    private String appId;

    // 第三方用户唯一凭证密钥
    private String appSecret;

    // 公众号账号
    private String account;

    @Override
    protected WxAccountConfig build() {
        WxAccountConfig entity = new WxAccountConfig();
        commonSet(entity);
        return entity;
    }

    @Override
    protected WxAccountConfig update(String id) {
        WxAccountConfig entity = new WxAccountConfig();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(WxAccountConfig entity) {
        entity.setUrl(url);
        entity.setToken(token);
        entity.setExpireTime(expireTime);
        entity.setName(name);
        entity.setWxId(wxId);
        entity.setAppId(appId);
        entity.setAppSecret(appSecret);
        entity.setAccount(account);
        entity.setTenantId(tenantId());
    }
}