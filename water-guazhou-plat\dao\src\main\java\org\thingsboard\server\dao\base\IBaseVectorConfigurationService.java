package org.thingsboard.server.dao.base;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.base.BaseVectorConfiguration;
import org.thingsboard.server.dao.util.imodel.query.base.BaseVectorConfigurationPageRequest;

import java.util.List;

/**
 * 公共管理平台-矢量数据配置Service接口
 *
 * <AUTHOR>
 * @date 2025-06-06
 */
public interface IBaseVectorConfigurationService {
    /**
     * 查询公共管理平台-矢量数据配置
     *
     * @param id 公共管理平台-矢量数据配置主键
     * @return 公共管理平台-矢量数据配置
     */
    public BaseVectorConfiguration selectBaseVectorConfigurationById(String id);

    /**
     * 查询公共管理平台-矢量数据配置列表
     *
     * @param baseVectorConfiguration 公共管理平台-矢量数据配置
     * @return 公共管理平台-矢量数据配置集合
     */
    public IPage<BaseVectorConfiguration> selectBaseVectorConfigurationList(BaseVectorConfigurationPageRequest baseVectorConfiguration);

    /**
     * 新增公共管理平台-矢量数据配置
     *
     * @param baseVectorConfiguration 公共管理平台-矢量数据配置
     * @return 结果
     */
    public int insertBaseVectorConfiguration(BaseVectorConfiguration baseVectorConfiguration);

    /**
     * 修改公共管理平台-矢量数据配置
     *
     * @param baseVectorConfiguration 公共管理平台-矢量数据配置
     * @return 结果
     */
    public int updateBaseVectorConfiguration(BaseVectorConfiguration baseVectorConfiguration);

    /**
     * 批量删除公共管理平台-矢量数据配置
     *
     * @param ids 需要删除的公共管理平台-矢量数据配置主键集合
     * @return 结果
     */
    public int deleteBaseVectorConfigurationByIds(List<String> ids);

    /**
     * 删除公共管理平台-矢量数据配置信息
     *
     * @param id 公共管理平台-矢量数据配置主键
     * @return 结果
     */
    public int deleteBaseVectorConfigurationById(String id);

    public List<BaseVectorConfiguration> selectAllBaseVectorConfiguration();
}
