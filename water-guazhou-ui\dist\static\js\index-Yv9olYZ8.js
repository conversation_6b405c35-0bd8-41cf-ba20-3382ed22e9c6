import{d as X,u as Z,M as W,c as E,r as _,a8 as y,s as B,D as U,S as V,y as ee,bF as C,bT as te,bu as ae,g as R,n as ie,q as N,i as I,F as se,h as re,an as le,p as ne,cU as oe,b7 as de,bM as ue}from"./index-r0dFAfgr.js";import{_ as ce}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as pe}from"./CardTable-rdWOL4_6.js";import{_ as me}from"./CardSearch-CB_HNR-Q.js";import{I as A}from"./common-CvK_P_ao.js";import{r as fe,s as ge,v as w,f as be,k as ye,l as he,c as xe,m as Te,n as Ie}from"./waterInspection-DqEu1Oyl.js";import{u as De}from"./useDepartment-BkP08hh6.js";import{u as Ue}from"./useStation-DJgnSZIA.js";import{f as F}from"./DateFormatter-Bm9a68Ax.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";import"./zhandian-YaGuQZe6.js";const ve={class:"wrapper"},Oe=X({__name:"index",setup(ke){const c=Z(),{$messageSuccess:D,$messageError:v}=W(),{getAllStationOption:z}=Ue(),{getDepartmentTree:G}=De(),{$btnPerms:J}=W(),h=E(),L=E([]),p=E(),$=E(),q=E(),S=[{label:"项目分类",prop:"itemType"},{label:"项目名称",prop:"name"},{label:"巡检方法",prop:"method"},{label:"巡检要求",prop:"require"}],l=_({departmentTree:[],templateList:[],stationOptionList:[],searchAuditUserDepartment:{},searchExecutionUserDepartment:{},searchAuditUsers:[],searchExecutionUsers:[],resultEnum:{PENDING:"未审核",RECEIVED:"未审核",VERIFY:"未审核",APPROVED:"合格",REJECTED:"不合格"},code:"",imageUrl:"",counter:"000100"}),x=_({filters:[{label:"任务编号",field:"code",type:"input"},{label:"任务名称",field:"name",type:"input"},{label:"任务类型",field:"taskType",type:"select",options:[{label:"临时任务",value:"临时任务"},{label:"常规任务",value:"常规任务"}]},{label:"巡检部门",field:"executionUserDepartmentId",type:"select-tree",checkStrictly:!0,options:y(()=>l.departmentTree),onChange:async e=>{var a,i,s;const t=(a=x.filters)==null?void 0:a.find(r=>r.field==="executionUserId");if(e){const r=await Y(e);t.options=r}else t.options=[];x.defaultParams={...x.defaultParams,...(i=h.value)==null?void 0:i.queryParams,executionUserDepartmentId:e,executionUserId:""},(s=h.value)==null||s.resetForm()}},{label:"巡检人员",field:"executionUserId",type:"select",placeholder:"请选择巡检人员"},{label:"巡检水源",field:"stationId",type:"select",multiple:!0,options:y(()=>l.stationOptionList)},{label:"预计开始",field:"startTime",type:"date"},{label:"预计完成",field:"endTime",type:"date"},{label:"实际开始",field:"realStartTime",type:"date"},{label:"实际完成",field:"realEndTime",type:"date"},{label:"任务状态",field:"status",type:"select",options:[{label:"待接收",value:"PENDING"},{label:"处理中",value:"RECEIVED"},{label:"已审核",value:"VERIFY"}]},{xs:11,type:"select-tree",label:"审核部门",clearable:!1,checkStrictly:!0,options:y(()=>l.departmentTree),field:"auditUserDepartmentId",rules:[{required:!0,message:"请选择巡检部门"}],onChange:async e=>{var t,a;e?l.searchAuditUsers=await Y(e):l.searchAuditUsers=[],x.defaultParams={...x.defaultParams,...(t=h.value)==null?void 0:t.queryParams,auditUserDepartmentId:e,auditUserId:""},(a=h.value)==null||a.resetForm()}},{xs:11,type:"select",label:"审核人员",field:"auditUserId",options:y(()=>l.searchAuditUsers),rules:[{required:!0,message:"请选择审核人员"}]},{label:"审核结果",field:"status",type:"select",options:[{label:"通过",value:"APPROVED"},{label:"拒绝",value:"REJECTED"}]}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",icon:A.QUERY,click:()=>f()},{type:"default",perm:!0,text:"重置",svgIcon:B(de),click:()=>{var e;x.defaultParams={},(e=h.value)==null||e.resetForm()}},{perm:J("RoleManageAdd"),text:"新增",type:"success",icon:A.ADD,click:()=>{n.title="新增",P()}}]}]}),m=_({loading:!0,defaultExpandAll:!0,indexVisible:!0,selectList:[],columns:[{label:"任务编号",prop:"code",minWidth:140},{label:"创建时间",prop:"createTime",formatter:e=>F(e.createTime,"YYYY-MM-DD"),minWidth:140},{label:"任务名称",prop:"name",minWidth:140},{label:"任务类型",prop:"taskType",minWidth:140},{label:"巡检人员",prop:"executionUserName",minWidth:140},{label:"巡检水源",prop:"stationName",minWidth:140},{label:"预计开始",prop:"startTime",formatter:(e,t)=>F(t,"YYYY-MM-DD"),minWidth:140},{label:"预计完成",prop:"endTime",formatter:(e,t)=>F(t,"YYYY-MM-DD"),minWidth:140},{label:"实际开始",prop:"realStartTime",formatter:(e,t)=>F(t,"YYYY-MM-DD"),minWidth:140},{label:"实际完成",prop:"realEndTime",formatter:(e,t)=>F(t,"YYYY-MM-DD"),minWidth:140},{label:"任务状态",prop:"statusName",minWidth:140},{label:"审核部门",prop:"auditUserDepartmentName",minWidth:140},{label:"审核人员",prop:"auditUserName",minWidth:140},{label:"审核结果",prop:"status",formatter:(e,t)=>l.resultEnum[t],minWidth:140}],operationWidth:"280px",operationFixed:"right",operations:[{type:"primary",isTextBtn:!0,perm:!0,text:"查看",click:e=>{n.title="查看",P(e,!0)}},{type:"primary",isTextBtn:!0,perm:!0,text:"接收",disabled:e=>{var a,i;const t=U(((i=(a=c==null?void 0:c.user)==null?void 0:a.id)==null?void 0:i.id)||"");return!(e.executionUserId===t&&e.status==="PENDING")},click:e=>{V("确定接收任务？","提示信息").then(()=>{fe(e.id).then(()=>{f(),D("接收成功")})}).catch(()=>{})}},{type:"primary",isTextBtn:!0,perm:!0,text:"提交审核",disabled:e=>{var a,i;const t=U(((i=(a=c==null?void 0:c.user)==null?void 0:a.id)==null?void 0:i.id)||"");return!(e.executionUserId===t&&e.status==="RECEIVED")},click:e=>{V("确定提交任务审核？","提示信息").then(()=>{ge(e.id,e.auditUserId).then(()=>{f(),D("提交成功")}).catch(()=>{v("提交失败")})}).catch(()=>{})}},{type:"primary",isTextBtn:!0,perm:!0,text:"审核",disabled:e=>{var a,i;const t=U(((i=(a=c==null?void 0:c.user)==null?void 0:a.id)==null?void 0:i.id)||"");return!(e.auditUserId===t&&e.status==="VERIFY")},click:e=>{ee.confirm("审核是否通过","提示",{confirmButtonText:"通过",cancelButtonText:"驳回",type:"warning"}).then(()=>{w(e.id,!0).then(()=>{f(),D("提交成功")}).catch(()=>{v("提交失败")})}).catch(()=>{w(e.id,!1).then(()=>{f(),D("提交成功")}).catch(()=>{v("提交失败")})})}},{isTextBtn:!0,type:"danger",perm:!0,text:"删除",disabled:e=>{var a,i;const t=U(((i=(a=c==null?void 0:c.user)==null?void 0:a.id)==null?void 0:i.id)||"");return!(e.creator===t&&e.status==="PENDING")},click:e=>Q(e)}],dataList:[],pagination:{page:1,limit:20,total:0,refreshData:({page:e,size:t})=>{m.pagination.page=e,m.pagination.limit=t,f()}}}),j=_({title:"附件信息",labelWidth:"130px",dialogWidth:500,group:[]}),n=_({title:"新增",labelWidth:"130px",defaultValue:{taskType:"临时任务",code:l.code},group:[{fields:[{xs:16,type:"input",label:"任务编号",readonly:!0,clearable:!1,field:"code",rules:[{required:!0,message:"请输入任务名称"}]},{xs:8,type:"btn-group",field:"codeButton",btns:[{text:"获取编号",perm:!0,click:()=>{var t,a,i,s;const e=M();n.defaultValue={...n.defaultValue,...(a=(t=p.value)==null?void 0:t.refForm)==null?void 0:a.dataForm,code:e},(s=(i=p.value)==null?void 0:i.refForm)==null||s.resetForm()}}]},{xs:8,type:"input",label:"任务名称",field:"name",rules:[{required:!0,message:"请输入任务名称"}]},{xs:8,type:"select",label:"任务类型",field:"taskType",readonly:!0,options:[{label:"临时任务",value:"临时任务"},{label:"常规任务",value:"常规任务"}],rules:[{required:!0,message:"请选择任务类型"}]},{xs:8,type:"select-tree",checkStrictly:!0,label:"巡检部门",options:y(()=>l.departmentTree),field:"executionUserDepartmentId",onChange:async e=>{var a,i,s,r;const t=n.group[0].fields.find(o=>o.field==="executionUserId");if(e){const o=await Y(e);t.options=o}else t.options=[];n.defaultValue={...n.defaultValue,...(i=(a=p.value)==null?void 0:a.refForm)==null?void 0:i.dataForm,executionUserId:""},(r=(s=p.value)==null?void 0:s.refForm)==null||r.resetForm()},rules:[{required:!0,message:"请选择巡检部门"}]},{xs:8,type:"select",label:"巡检人员",field:"executionUserId",rules:[{required:!0,message:"请选择人员名称"}]},{xs:8,type:"date",label:"预计时间",field:"startTime",min:C().format("YYYY-MM-DD"),rules:[{required:!0,message:"请输入预计时间"}],onChange:e=>{var a,i,s,r;const t=n.group[0].fields.find(o=>o.field==="endTime");console.log(t),t.min=e,n.defaultValue={...n.defaultValue,...(i=(a=p.value)==null?void 0:a.refForm)==null?void 0:i.dataForm,endTime:""},(r=(s=p.value)==null?void 0:s.refForm)==null||r.resetForm()}},{xs:8,type:"date",label:"预计完成",field:"endTime",rules:[{required:!0,message:"请输入预计完成时间"}]},{xs:8,type:"select-tree",checkStrictly:!0,label:"审核部门",field:"auditUserDepartmentId",options:y(()=>l.departmentTree),onChange:async e=>{var a,i,s,r;const t=n.group[0].fields.find(o=>o.field==="auditUserId");e?t.options=await Y(e):t.options=[],n.defaultValue={...n.defaultValue,...(i=(a=p.value)==null?void 0:a.refForm)==null?void 0:i.dataForm,auditUserId:"",auditUserDepartmentId:e},(r=(s=p.value)==null?void 0:s.refForm)==null||r.resetForm()},rules:[{required:!0,message:"请选择审核部门"}]},{xs:8,type:"select",label:"审核人员",field:"auditUserId",rules:[{required:!0,message:"请选择审核人员"}]},{xs:8,type:"select",label:"巡检水源",field:"stationId",options:y(()=>l.stationOptionList),rules:[{required:!0,message:"请选择巡检水源"}]},{xs:8,type:"select",label:"巡检模板",field:"templateId",clearable:!1,options:y(()=>l.templateList),onChange:e=>H(e),rules:[{required:!0,message:"请选择巡检模板"}]},{type:"table",field:"configList",config:{indexVisible:!0,height:"200px",dataList:[],columns:S,pagination:{hide:!0}}}]}]}),M=()=>{const e=new Date,t=e.getFullYear().toString()+(e.getMonth()+1).toString().padStart(2,"0")+e.getDate().toString().padStart(2,"0");l.counter||(l.counter=100);const a=l.counter.toString().padStart(6,"0");return l.counter++,t+a},P=async(e,t)=>{var o,b,T,g;n.group.map(d=>{d.fields.map(u=>{u.readonly=!!t,u.field==="taskType"||u.field==="code"?u.readonly=!0:u.field==="codeButton"&&(u.btns[0].disabled=!!e)})});const i=(b=(o=(await be({page:1,size:999,type:"水源"})).data)==null?void 0:o.data)==null?void 0:b.data;l.templateList=i==null?void 0:i.map(d=>({id:d.id,label:d.name,value:d.id}));const s=(T=n.group[0].fields)==null?void 0:T.find(d=>d.field==="configList");s.config.dataList=[];const r=JSON.parse(JSON.stringify(S));s.config.columns=r,e?(s.config.columns=r.concat([{label:"巡检结果",prop:"result"},{label:"结果备注",prop:"resultRemark"},{label:"附件",prop:"file",formItemConfig:{type:"btn-group",btns:[{perm:!0,text:"查看",svgIcon:B(ue),plain:!0,click:d=>{var u,k;(u=q.value)==null||u.openDialog(),L.value=(k=d.file)==null?void 0:k.split(","),l.imageUrl=L.value?L.value[0]:""}}]}}]),n.defaultValue={...e,startTime:C(e==null?void 0:e.startTime).format(),endTime:C(e==null?void 0:e.endTime).format(),auditUserId:e.auditUserName,executionUserId:e.executionUserName},await K(e.id)):n.defaultValue={taskType:"临时任务",code:M()},t?n.submit=void 0:n.submit=d=>{V("确定提交？","提示信息").then(()=>{d.type="水源",d.id=e?e.id:null,delete d.configList,console.log(d),ye(d).then(()=>{var u,k,O;(k=(u=p.value)==null?void 0:u.refForm)==null||k.resetForm(),(O=p.value)==null||O.closeDialog(),D("提交成功"),f()}).catch(()=>{v("提交失败")})}).catch(()=>{})},(g=p.value)==null||g.openDialog()},Q=e=>{V("确定删除巡检任务, 是否继续?","删除提示").then(()=>{he(e.id).then(()=>{f(),D("删除成功")}).catch(t=>{v(t.data.message)})})},H=async e=>{var r,o,b;const i=(o=(r=(await xe({page:1,size:9999,type:"水源",templateId:e})).data)==null?void 0:r.data)==null?void 0:o.data,s=(b=n.group[0].fields)==null?void 0:b.find(T=>T.field==="configList");s.config.dataList=i},K=async e=>{var o,b,T;const i=(b=(o=(await Te({page:1,size:999,mainId:e,type:"水源"})).data)==null?void 0:o.data)==null?void 0:b.data,s=i==null?void 0:i.map(g=>({...g,method:g.itemMethod,name:g.itemName,require:g.itemRequire})),r=(T=n.group[0].fields)==null?void 0:T.find(g=>g.field==="configList");r.config.dataList=s},f=async()=>{var i,s,r;m.loading=!0;const e=((i=h.value)==null?void 0:i.queryParams)||{createTime:[]},t={...e,type:"水源",startTime:e.startTime?C(e.startTime).startOf("day").valueOf():null,endTime:e.endTime?C(e.endTime).endOf("day").valueOf():null,page:m.pagination.page||1,size:m.pagination.limit||20,stationId:e.stationId?e.stationId.join(","):""},a=await Ie(t);m.pagination.total=(s=a.data)==null?void 0:s.data.total,m.dataList=(r=a.data)==null?void 0:r.data.data,m.loading=!1},Y=async e=>{var s,r;const a=(r=(s=(await te({pid:e,status:1,page:1,size:999})).data)==null?void 0:s.data)==null?void 0:r.data;return a==null?void 0:a.map(o=>({id:U(o.id.id),label:o.firstName,value:U(o.id.id)}))};return ae(async()=>{l.departmentTree=await G(2),l.stationOptionList=await z("水源地"),await f()}),(e,t)=>{const a=me,i=pe,s=ce,r=oe;return R(),ie("div",ve,[N(a,{ref_key:"refSearch",ref:h,config:I(x)},null,8,["config"]),N(i,{ref_key:"refTable",ref:$,config:I(m),class:"card-table"},null,8,["config"]),N(s,{ref_key:"refDialogForm",ref:p,config:I(n)},null,8,["config"]),N(s,{ref_key:"refImg",ref:q,config:I(j)},{default:se(()=>[I(l).imageUrl?(R(),re(r,{key:0,style:{width:"440px",height:"400px"},src:I(l).imageUrl,"preview-src-list":I(L),"initial-index":1,fit:"cover"},null,8,["src","preview-src-list"])):le("",!0),t[0]||(t[0]=ne("div",{class:"",style:{"text-align":"center"}},"暂无信息",-1))]),_:1},8,["config"])])}}});export{Oe as default};
