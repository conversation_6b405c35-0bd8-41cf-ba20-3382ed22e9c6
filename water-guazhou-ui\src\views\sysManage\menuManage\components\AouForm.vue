<template>
  <div class="aou-form">
    <div class="form-main">
      <el-scrollbar>
        <div class="form-content">
          <Form ref="refForm" :config="FormConfig"></Form>
          <Preview
            :type="refForm?.dataForm.menutype"
            :source="refForm?.dataForm.component"
          ></Preview>
        </div>
      </el-scrollbar>
    </div>
    <div class="form-footer">
      <Button v-for="(item, i) in formBtns" :key="i" :config="item"></Button>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { GetMenu, PostMenu } from '@/api/menu/source';
import { defaultIcon, IFrameComponentPath } from '../data';
import { getUrlPramByName, removeUrlPramByName } from '@/utils/GlobalHelper';
import { SLMessage } from '@/utils/Message';
import Preview from './Preview.vue';

const emit = defineEmits(['success']);
const refForm = ref<IFormIns>();
const props = defineProps<{
  menuParentId?: string;
  menuRootId?: string;
  sources?: NormalOption[];
  menus?: NormalOption[];
  menu?: NormalOption;
}>();
watch(
  () => props.menus,
  () => {
    const field = FormConfig.group[0].fields.find(
      (item) => item.field === 'parentId' && item.type === 'select-tree'
    ) as IFormTreeSelect;
    if (!field) return;
    field.options = [
      {
        id: props.menuRootId,
        label: '根级菜单',
        value: props.menuRootId,
        children: [...(unref(props.menus) || [])]
      }
    ];
  }
);
const formBtns = reactive<IButton[]>([
  { perm: true, type: 'default', text: '重置', click: () => resetForm() },
  {
    perm: true,
    type: 'primary',
    text: '提交',
    click: () => {
      refForm.value?.Submit();
    }
  }
]);
const FormConfig = reactive<IFormConfig>({
  group: [
    {
      fields: [
        // { type: 'input', label: 'id（添加操作不传）:', field: 'id', xs: 12 },

        { type: 'input', label: '标题:', field: 'title', xs: 12 },
        {
          type: 'select-tree',
          label: '父级菜单:',
          field: 'parentId',
          xs: 12,
          checkStrictly: true,
          options: []
        },
        {
          type: 'icon-selector',
          // allowCreate: true,
          label: '图标',
          field: 'icon',
          xs: 12
        },
        {
          type: 'image',
          label: '自定义图标',
          field: 'url',
          size: 'small',
          multiple: false,
          limit: 1,
          xs: 12
        },
        {
          type: 'input-number',
          label: '序号',
          field: 'orderNum',
          xs: 12
        },
        { type: 'input', label: '路由名称（唯一）:', field: 'name' },
        {
          type: 'input',
          label: '路由路径:',
          field: 'path',
          rules: [{ required: true, message: '请输入路由路径' }]
        },
        {
          type: 'radio-button',
          label: '菜单类型',
          field: 'menutype',
          rules: [{ required: true, message: '请选择菜单类型' }],
          options: [
            { label: '菜单', value: 'menu' },
            { label: '页面', value: 'page' },
            { label: 'iframe', value: 'iframe' }
          ]
        },
        {
          handleHidden(params, query, formItem) {
            formItem.hidden = params.menutype !== 'menu';
          },
          type: 'radio-button',
          label: '布局组件：',
          field: 'layout',
          // onChange: (val: string[]) => {
          //   if (!refForm.value) return;
          //   if (val.length > 1) {
          //     // 处理成只保留一个
          //     refForm.value.dataForm.layout = [val[val.length - 1]];
          //   }
          // },
          options: [
            { label: '根级菜单', value: 'Layout' },
            { label: '中间层菜单', value: 'LayoutParentView' },
            { label: '无侧边菜单', value: 'LayoutFull' }
          ]
        },
        {
          handleHidden(params, query, formItem) {
            formItem.hidden = params.menutype !== 'iframe';
          },
          xs: 12,
          type: 'input',
          label: 'iframe路径：',
          field: 'iframe'
        },
        {
          handleHidden(params, query, formItem) {
            formItem.hidden = params.menutype !== 'iframe';
          },
          xs: 12,
          type: 'input',
          label:
            'token字段名（指定路径中携带的token的字段名,不填则表示不需要带token）：',
          field: 'tokenField'
        },
        {
          handleHidden(params, query, formItem) {
            formItem.hidden = params.menutype !== 'page';
          },
          xs: 12,
          type: 'select',
          label: '资源路径（views目录）（不带文件后缀）:',
          field: 'component',
          options: computed(() => props.sources || []) as any
        },
        {
          type: 'checkbox',
          label: '访问权限:',
          field: 'roles',
          options: [
            { label: 'TENANT_ADMIN', value: 'TENANT_ADMIN' },
            { label: 'TENANT_SYS', value: 'TENANT_SYS' },
            { label: 'CUSTOMER_USER', value: 'CUSTOMER_USER' }
          ]
        },
        {
          type: 'radio',
          label: '是否隐藏:',
          field: 'hidden',
          options: [
            { label: '是', value: true },
            { label: '否', value: false }
          ]
        },
        {
          type: 'radio',
          label: '开启缓存:',
          field: 'keepAlive',
          options: [
            { label: '是', value: true },
            { label: '否', value: false }
          ]
        }
        // {
        //   type: 'radio',
        //   label: '当只有一个子级时是否显示父级目录(alwaysShow):',
        //   field: 'alwaysShow',
        //   options: [
        //     { label: '显示', value: true },
        //     { label: '隐藏', value: false }
        //   ]
        // }
      ]
    }
  ],
  labelPosition: 'top',
  defaultValue: {},
  submit: async (params: MenuItem) => {
    debugger
    const res = await PostMenu({
      data: {
        id: params.id,
        type: '109',
        // fixed: 当orderNum值为0时，会被判断为false，所以单独判断一下
        orderNum:
          params.orderNum === 0
            ? '0'
            : params.orderNum
              ? params.orderNum + ''
              : params.orderNum,
        path: params.path,
        name: params.name,
        url: params.url,
        meta: {
          title: params.title,
          icon: params.icon,
          roles: params.roles,
          hidden: params.hidden,
          keepAlive: params.keepAlive || false
          // alwaysShow?: boolean
        },
        // 当类型为菜单时，则组件可以是总局组件或指定的组件路径，布局优先
        // 当类型为页面时，则只能是指定的组件路径
        component:
          params.menutype === 'menu'
            ? params.layout || params.component
            : params.menutype === 'iframe'
              ? IFrameComponentPath +
                '?url=' +
                encodeURIComponent(params.iframe || '') +
                '&tokenField=' +
                (params.tokenField || '')
              : params.component
      },
      parentId: params.parentId
    });
    if (res.data) {
      SLMessage.success(res.data.message || '操作成功');
      emit('success');
    } else {
      SLMessage.error(res.data.message || '操作失败');
    }
  }
});
const getMenuType = (comp?: string) => {
  if (comp === undefined) return;
  const menuItems = ['Layout', 'LayoutParentView', 'LayoutFull'];
  if (comp.startsWith(IFrameComponentPath) && comp.indexOf('?') !== -1) {
    return 'iframe';
  } else if (menuItems.indexOf(comp) !== -1) {
    return 'menu';
  } else {
    return 'page';
  }
};
watch(
  () => props.menu,
  () => {
    resetForm();
  }
);
const resetForm = async () => {
  FormConfig.defaultValue = {
    orderNum: '0',
    parentId: props.menuParentId,
    icon: defaultIcon,
    roles: ['TENANT_ADMIN', 'TENANT_SYS', 'CUSTOMER_USER']
  };
  if (props.menu) {
    const res = await GetMenu(props.menu.value);

    let tokenField: string | undefined;
    let comp = res?.data?.component;
    const menutype = getMenuType(comp);
    // 处理iframe大屏的路径和授权字段
    if (menutype === 'iframe') {
      tokenField = getUrlPramByName(comp, 'tokenField')?.value.toString();
      // 移除tokenField参数
      comp = removeUrlPramByName(comp, 'tokenField');
      // 这里取iframe路径作为组件
      comp = decodeURIComponent(
        getUrlPramByName(comp, 'url')?.value?.toString() || ''
      );
    }

    FormConfig.defaultValue = {
      ...FormConfig.defaultValue,
      ...res.data,
      ...res.data?.meta,
      layout: res?.data?.component,
      iframe: comp,
      tokenField,
      menutype
    };
  }

  refForm.value?.resetForm();
};
</script>
<style lang="scss" scoped>
.aou-form {
  height: 100%;
  width: 100%;
  .form-main {
    height: calc(100% - 40px);
  }
  .form-content {
    padding: 15px;
  }
  .form-footer {
    height: 40px;
    text-align: right;
  }
}
</style>
