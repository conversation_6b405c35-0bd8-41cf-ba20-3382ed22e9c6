import{_ as z}from"./Panel-DyoxrWMd.js";import{d as K,c as B,r as T,b as F,bH as X,g as Y,n as Z,q as v,F as S,i as P,p as ee,aq as te,C as re}from"./index-r0dFAfgr.js";import{_ as ae}from"./Search-NSrhrIa_.js";import{_ as ie}from"./FormTableColumnFilter-BT7pLXIC.js";import{g as oe,v as q,b as N,u as ne}from"./MapView-DaoQedLH.js";import{w as se}from"./Point-WxyopZva.js";import{GetFieldConfig as le}from"./fieldconfig-Bk3o1wi7.js";import{f as pe}from"./DateFormatter-Bm9a68Ax.js";import{s as D,g as me}from"./FeatureHelper-Da16o0mu.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import{g as O,q as ce}from"./LayerHelper-Cn-iiqxI.js";import{d as ue,a as R,i as A}from"./QueryHelper-ILO3qZqg.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./geometryEngineBase-BhsKaODW.js";import{s as de}from"./config-fy91bijz.js";import"./v4-SoommWqA.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./project-DUuzYgGl.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./pipe-nogVzCHG.js";const fe={class:"table-box"},ge=K({__name:"PipeDetail",props:{view:{},queryParams:{},tabs:{},telport:{},maxmin:{type:Boolean},detailUrl:{}},emits:["refreshed","close","refreshing","rowdblclick"],setup(M,{expose:$,emit:j}){const h=j,l=M,i={tabFeatures:[]},_=B(),I=B(),t=T({dataList:[],columns:[{label:"OBJECTID",prop:"OBJECTID"}],handleRowClick:e=>{var r,o,n;t.currentRow=e,l.view?G(l.view,(r=_.value)==null?void 0:r.queryParams.type,e.OBJECTID):h("rowdblclick",e,(n=(o=_.value)==null?void 0:o.queryParams)==null?void 0:n.type)},pagination:{refreshData:({page:e,size:r})=>{t.pagination.page=e,t.pagination.limit=r,C()}}}),b=T({filters:[{type:"tabs",field:"type",tabs:[],onChange:e=>{C(e,!0)}}]}),C=async(e,r)=>{var o,n,s,u,d,k,x;h("refreshing");try{if(t.loading=!0,e=e||((n=(o=_.value)==null?void 0:o.queryParams)==null?void 0:n.type),t.columns=[],t.dataList=[],!e){t.dataList=[],t.loading=!1;return}const a=(s=l.tabs)==null?void 0:s.find(p=>p.name===e);if(GIS_SERVER_SWITCH){const p=a==null?void 0:a.data,L=Object.keys(a.data[0].properties);t.columns=L.map(c=>({prop:c,label:c,minWidth:160}));let m=[...a.data.map(c=>({...c.properties}))];if(t.dataList=m,p!=null&&p.length||(u=i.hilightLayer)==null||u.removeAll(),r&&(p!=null&&p.length)){const c=[];i.hilightLayer=O(l.view,{id:"pipe-detail",title:"详情展示"}),i.hilightLayer&&(i.hilightLayer.removeAll(),p.map(w=>{const f=Q(w.geometry);let y=new oe({geometry:f,attributes:w.properties,symbol:D(f.type)});c.push(y)}),i.hilightLayer.addMany(c),i.tabFeatures=c,t.pagination.hide=!0)}}else{const p=(a==null?void 0:a.layerid)===void 0?await ce(e):a.layerid;if(p<0){F.warning("暂无"+(e||"")+"相关数据"),t.dataList=[],t.pagination.total=0,t.loading=!1,h("refreshed");return}const L=await le(e);i.fieldConfig=(k=(d=L.data)==null?void 0:d.result)==null?void 0:k.rows;const m=a==null?void 0:a.data;t.pagination.total=m.length||0;const c=ue(m,t.pagination.page||1,t.pagination.limit||20),w=[];if(c.length){const f=await R(`${l.detailUrl||window.SITE_CONFIG.GIS_CONFIG.gisService+window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService}/${p}`,A({orderByFields:["OBJECTID asc"],outFields:["*"],objectIds:c||[],returnGeometry:!0,...l.queryParams||{where:a!=null&&a.where?a.where:"1=1"}})),y=[];f.fields.map(g=>{const E={label:g.alias,prop:g.name,minWidth:160};g.type==="date"&&(E.formatter=J=>pe(J[g.name],X)),de.indexOf(g.name)===-1&&y.push(E)}),t.columns=y,f.features.map(g=>{w.push(g.attributes),g.symbol=D(g.geometry.type)})}if(t.dataList=w,m!=null&&m.length||(x=i.hilightLayer)==null||x.removeAll(),r&&(m!=null&&m.length)){const f=await R(`${window.SITE_CONFIG.GIS_CONFIG.gisService+window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService}/${p}`,A({orderByFields:["OBJECTID asc"],outFields:["OBJECTID"],objectIds:m||[],returnGeometry:!0,...l.queryParams||{}}));i.hilightLayer=O(l.view,{id:"pipe-detail",title:"详情展示"}),i.hilightLayer&&(i.hilightLayer.removeAll(),f.features.map(y=>y.symbol=D(y.geometry.type)),i.hilightLayer.addMany(f.features),i.tabFeatures=f.features)}}}catch(a){console.dir(a),F.error("查询失败")}t.loading=!1,h("refreshed")},G=async(e,r,o)=>{var s,u;r=r||((u=(s=_.value)==null?void 0:s.queryParams)==null?void 0:u.type);const n=i.tabFeatures.find(d=>d.attributes.OBJECTID===o);n&&await me(e,n)},H=()=>{var r,o,n;i.hilightLayer=O(l.view,{id:"pipe-detail",title:"详情展示"}),t.dataList=[],t.pagination.total=0,(r=i.hilightLayer)==null||r.removeAll();const e=(o=b.filters)==null?void 0:o.find(s=>s.type==="tabs");e&&(e.tabs=((n=l.tabs)==null?void 0:n.map(s=>({...s,label:s.name,value:s.name})))||[])},U=async()=>{var o,n,s,u;if(!((o=l.tabs)!=null&&o.length)){h("refreshed"),F.info("未查询到结果");return}const e=(n=b.filters)==null?void 0:n.find(d=>d.field==="type");if(!e)return;e.tabs=l.tabs.map(d=>({...d,value:d.name,data:null}));const r=l.tabs[0].name;b.defaultParams={type:r},(s=_.value)==null||s.resetForm(),await C(r,!0),(u=I.value)==null||u.Open()},V=()=>{var e;(e=i.hilightLayer)==null||e.removeAll(),h("close")},W=()=>{var e;(e=I.value)==null||e.Close()},Q=e=>{switch(e.type){case"Point":return new se({x:e.coordinates[0],y:e.coordinates[1],spatialReference:{wkid:3857}});case"MultiPoint":return new ne({points:e.coordinates,spatialReference:{wkid:3857}});case"LineString":return new N({paths:[e.coordinates],spatialReference:{wkid:3857}});case"MultiLineString":return new N({paths:e.coordinates,spatialReference:{wkid:3857}});case"Polygon":return new q({rings:e.coordinates,spatialReference:{wkid:3857}});case"MultiPolygon":return new q({rings:e.coordinates.reduce((r,o)=>r.concat(o),[]),spatialReference:{wkid:3857}});default:return console.error("Unsupported GeoJSON type:",e.type),null}};return $({openDialog:U,closeDialog:W,extentTo:G,clearData:H}),(e,r)=>{const o=ie,n=ae,s=te,u=z;return Y(),Z("div",null,[v(u,{ref_key:"refPanel",ref:I,"custom-class":"gis-detail-panel",telport:e.telport,draggable:!1,"max-min":e.maxmin,"before-close":V,extra:!0},{extra:S(()=>[v(o,{columns:P(t).columns,"show-tooltip":!0},null,8,["columns"])]),header:S(()=>[v(n,{ref_key:"refTab",ref:_,class:"pipe-detail",config:P(b)},null,8,["config"])]),default:S(()=>[ee("div",fe,[v(s,{config:P(t)},null,8,["config"])])]),_:1},8,["telport","max-min"])])}}}),Nt=re(ge,[["__scopeId","data-v-dcaaa073"]]);export{Nt as default};
