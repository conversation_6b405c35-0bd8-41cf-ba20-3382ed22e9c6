package org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartProduction.dispatch.OrderRecordType;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

@Getter
@Setter
public class OrderRecordTypeSaveRequest extends SaveRequest<OrderRecordType> {
    // 指令名称
    @NotNullOrEmpty
    private String name;

    // 部门id，多个用逗号隔开
    private String deptIdList;

    @Override
    protected OrderRecordType build() {
        OrderRecordType entity = new OrderRecordType();
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    @Override
    protected OrderRecordType update(String id) {
        OrderRecordType entity = new OrderRecordType();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(OrderRecordType entity) {
        entity.setName(name);
        entity.setDeptIdList(deptIdList);
    }
}