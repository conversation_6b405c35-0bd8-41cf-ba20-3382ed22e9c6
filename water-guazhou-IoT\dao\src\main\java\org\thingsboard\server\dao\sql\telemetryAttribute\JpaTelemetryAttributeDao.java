/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.sql.telemetryAttribute;

import com.google.common.util.concurrent.ListenableFuture;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Component;
import org.thingsboard.server.common.data.id.EntityId;
import org.thingsboard.server.common.data.telemetryAttribute.TelemetryAttribute;
import org.thingsboard.server.dao.DaoUtil;
import org.thingsboard.server.dao.model.sql.TelemetryAttributeEntity;
import org.thingsboard.server.dao.sql.JpaAbstractSearchTextDao;
import org.thingsboard.server.dao.telemetryAttribute.TelemetryAttributeDao;
import org.thingsboard.server.dao.util.SqlDao;

import java.util.List;
import java.util.UUID;

import static org.thingsboard.server.common.data.UUIDConverter.fromTimeUUID;

@Component
@SqlDao
public class JpaTelemetryAttributeDao extends JpaAbstractSearchTextDao<TelemetryAttributeEntity, TelemetryAttribute> implements TelemetryAttributeDao {


    @Autowired
    private TelemetryAttributeRepository telemetryAttributeRepository;

    @Override
    protected Class getEntityClass() {
        return TelemetryAttributeEntity.class;
    }

    @Override
    protected CrudRepository getCrudRepository() {
        return telemetryAttributeRepository;
    }

    @Override
    public ListenableFuture<List<TelemetryAttribute>> getAttribute(UUID deviceId, String showName) {
        String device = fromTimeUUID(deviceId);
        return service.submit(() -> DaoUtil.convertDataList(telemetryAttributeRepository.findByDeviceIdAndShowName(device, showName)));
    }

    @Override
    public ListenableFuture<List<TelemetryAttribute>> getTelemetryAttributeByDevice(EntityId deviceId) {
        return service.submit(() -> DaoUtil.convertDataList(telemetryAttributeRepository.findAllByDeviceId(fromTimeUUID(deviceId.getId()))));
    }

    @Override
    public ListenableFuture<List<TelemetryAttribute>> getTelemetryAttributeByDeviceNameAndName(String deviceName, String showName) {
        return null;
    }
}
