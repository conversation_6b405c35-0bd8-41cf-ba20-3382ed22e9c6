<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartService.portal.SsPortalHomeInfoMapper">
    <sql id="Base_Column_List">
        <!--@sql select-->
        id,
        name,
        logo,
        address,
        qr,
        phone,
        email,
        postcode,
        tenant_id
        <!--@sql from ss_portal_home_info -->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.smartService.portal.SsPortalHomeInfo">
        <result column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="logo" property="logo"/>
        <result column="address" property="address"/>
        <result column="qr" property="qr"/>
        <result column="phone" property="phone"/>
        <result column="email" property="email"/>
        <result column="postcode" property="postcode"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <update id="updateFully">
        update ss_portal_home_info
        set name     = #{name},
            logo     = #{logo},
            address  = #{address},
            qr       = #{qr},
            phone    = #{phone},
            email    = #{email},
            postcode = #{postcode}
        where id = #{id}
    </update>

    <insert id="save">
        INSERT INTO ss_portal_home_info(id,
                                        name,
                                        logo,
                                        address,
                                        qr,
                                        phone,
                                        email,
                                        postcode,
                                        tenant_id)
        VALUES (#{id},
                #{name},
                #{logo},
                #{address},
                #{qr},
                #{phone},
                #{email},
                #{postcode},
                #{tenantId})
        on conflict (tenant_id) do update
            set name     = #{name},
                logo     = #{logo},
                address  = #{address},
                qr       = #{qr},
                phone    = #{phone},
                email    = #{email},
                postcode = #{postcode}
    </insert>

    <select id="getByTenantId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ss_portal_home_info
        where tenant_id = #{tenantId}
    </select>
</mapper>