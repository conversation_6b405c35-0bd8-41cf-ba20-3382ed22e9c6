package org.thingsboard.server.dao.model.sql.smartProduction.dispatch;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseUsername;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.util.Date;

@Getter
@Setter
@ResponseEntity
@TableName("sp_emergency_plan")
public class EmergencyPlan {
    // id
    @TableId
    private String id;

    // 预案名称
    private String name;

    // 所属站点
    private String stationId;

    // 所属站点名
    @TableField(exist = false)
    private String stationName;

    // 注意事项
    private String remark;

    // 处理步骤。富文本
    private String content;

    // 创建人
    @ParseUsername
    private String creator;

    // 创建时间
    private Date createTime;

    // 租户ID
    private String tenantId;

}
