<template>
  <el-row :gutter="20" style="height: 100%">
    <el-col :span="12" style="height: 100%">
      <CardTable style="height: 100%" :config="CurrentSortTable">
        <template #title>
          <div class="card-table-title">
            <div>当前排序</div>
          </div>
          <el-button
            type="success"
            style="margin-right: 10px"
            @click="handRight"
            >一键右移</el-button
          >
        </template>
      </CardTable>
    </el-col>
    <el-col :span="12" style="height: 100%">
      <CardTable style="height: 100%" :config="NewSortTable">
        <template #title>
          <div class="card-table-title">
            <div>新排序</div>
          </div>
          <el-button type="success" style="margin-right: 10px" @click="handSave"
            >保存配置</el-button
          >
        </template>
      </CardTable>
    </el-col>
  </el-row>
</template>

<script lang="ts" setup>
import { getGroupVideoList, saveCamera } from '@/api/video';
import { CommonRequest, GeneralTable } from '@/utils/GeneralProcessing';
import { ElMessage } from 'element-plus';

const type = [
  { label: '海康', value: '1' },
  { label: '城运平台', value: '3' },
  { label: '天网平台', value: '2' }
];

const props = defineProps({
  projectId: {
    type: String,
    default: () => {
      return '';
    }
  },
  groupId: {
    type: String,
    default: () => {
      return '';
    }
  }
});

const CurrentSortTable = reactive<ICardTable>({
  title: '当前排序',
  defaultExpandAll: true,
  indexVisible: true,
  rowKey: 'id',

  selectList: [],
  columns: [
    { label: '名称', prop: 'name', minWidth: '250px' },
    {
      label: '类型',
      prop: 'videoType',
      formatter: (row) =>
        type.find((item) => item.value === row.videoType)?.label
    }
  ],
  operationWidth: 70,
  operations: [
    {
      text: '右移',
      perm: true,
      click: (val) => {
        NewSortTable.dataList.push({ ...val });
        CurrentSortTable.dataList = CurrentSortTable.dataList.filter((item) => {
          if (item.id === val.id) {
            return false;
          }
          return true;
        });
      }
    }
  ],
  dataList: [],
  pagination: {
    hide: true
  }
});

const NewSortTable = reactive<ICardTable>({
  title: '新排序',
  defaultExpandAll: true,
  indexVisible: true,
  rowKey: 'id',

  selectList: [],
  columns: [
    { label: '名称', prop: 'name', minWidth: '250px' },
    {
      label: '类型',
      prop: 'videoType',
      formatter: (row) =>
        type.find((item) => item.value === row.videoType)?.label
    },
    {
      label: '位置',
      prop: 'number',
      formItemConfig: { type: 'input-number', placeholder: '新位置' }
    }
  ],
  operationWidth: 70,
  operations: [
    {
      text: '移到',
      perm: true,
      click: (val) => {
        // 判断位置是否存在
        if (!val.number) {
          ElMessage.error('请输入位置');
          return;
        }
        // 范围判断
        if (val.number < 1 || val.number > NewSortTable.dataList.length + 1) {
          ElMessage.error('请输入正确的位置');
          return;
        }
        // 删除原始数据
        NewSortTable.dataList = NewSortTable.dataList.filter((item) => {
          if (item.id === val.id) {
            return false;
          }
          return true;
        });
        // 数组插入
        NewSortTable.dataList.splice(val.number - 1, 0, {
          ...val,
          number: null
        });
      }
    },
    {
      text: '左移',
      perm: true,
      click: (val) => {
        CurrentSortTable.dataList.push({ ...val });
        NewSortTable.dataList = NewSortTable.dataList.filter((item) => {
          if (item.id === val.id) {
            return false;
          }
          return true;
        });
      }
    }
  ],
  dataList: [],
  pagination: {
    hide: true
  }
});

// 一键右移
const handRight = () => {
  NewSortTable.dataList = CurrentSortTable.dataList;
  CurrentSortTable.dataList = [];
};

const handSave = () => {
  const params = {
    groupId: props.groupId,
    projectId: props.projectId,
    videoList: NewSortTable.dataList.map((item, index) => {
      return {
        ...item,
        orderNum: index
      };
    })
  };
  CommonRequest(params, saveCamera, {}, '重排成功').then(() => {
    refreshDataleft();
    NewSortTable.dataList = [];
  });
};

const refreshDataleft = async () => {
  const params: any = {
    groupId: props.groupId,
    projectId: props.projectId || '',
    name: ''
  };
  GeneralTable(params, getGroupVideoList, { table: CurrentSortTable });
};

onMounted(() => {
  refreshDataleft();
});
</script>

<style lang="scss" scoped>
.card-table-title {
  width: 100%;
  display: flex;
  justify-content: space-between;
}
</style>
