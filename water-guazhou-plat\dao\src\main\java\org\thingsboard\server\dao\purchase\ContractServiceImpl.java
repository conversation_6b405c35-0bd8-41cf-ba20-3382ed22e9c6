package org.thingsboard.server.dao.purchase;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.dao.model.sql.purchase.Contract;
import org.thingsboard.server.dao.sql.deviceType.ContractMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.purchase.ContractDetailSaveRequest;
import org.thingsboard.server.dao.util.imodel.query.purchase.ContractPageRequest;
import org.thingsboard.server.dao.util.imodel.query.purchase.ContractSaveRequest;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class ContractServiceImpl implements ContractService {
    @Autowired
    private ContractMapper mapper;

    @Autowired
    private ContractDetailService service;


    @Override
    public IPage<Contract> findAllConditional(ContractPageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    @Transactional
    public Contract save(ContractSaveRequest entity) {
        Contract contract = QueryUtil.saveOrUpdateOneByRequest(entity, mapper::insert, mapper::update);
        List<ContractDetailSaveRequest> items = entity.getItems(contract.getId());
        if (items != null) {
            // noinspection Convert2MethodRef 删除所有未在列表中的设备采购单条目
            service.removeAllByMainOnIdNotIn(contract.getId(),
                    items.stream().map(x -> x.getId()).filter(x -> x != null).collect(Collectors.toList()));
            service.saveAll(items);
        }
        // service.deleteAll(entity.getRemove());
        return contract;
    }

    @Override
    public boolean update(Contract entity) {
        return mapper.update(entity);
    }

    @Override
    public boolean delete(String id) {
        service.deleteAll(mapper.getChildrenId(id));
        return mapper.deleteById(id) > 0;
    }
}
