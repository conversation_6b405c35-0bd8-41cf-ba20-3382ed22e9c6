import{d as E,c as v,r as f,a0 as P,o as z,g as p,n as m,q as c,F as y,p as a,aB as w,aJ as b,i as x,cs as G,bh as l,aw as C,j as M,bB as V,cU as F,b6 as O,C as U}from"./index-r0dFAfgr.js";import{_ as $}from"./index-C9hz-UZb.js";import{_ as A}from"./Search-NSrhrIa_.js";import{I as H}from"./common-CvK_P_ao.js";import{u as J}from"./useStation-DJgnSZIA.js";import{b as Q,c as R}from"./headwaterMonitoring-BgK7jThW.js";import Y from"./stationDetailMonitoring-DpwJmR2X.js";import{G as K}from"./Group1-DZYehK7n.js";import"./zhandian-YaGuQZe6.js";import"./CardSearch-CB_HNR-Q.js";import"./index-B69llYYW.js";import"./useAmap-D6DJ1T90.js";import"./index-BI1vGJja.js";import"./URLHelper-B9aplt5w.js";/* empty css                         */import"./echart-D5stWtDc.js";const X={class:"wrapper"},Z={class:"statistics"},tt={class:"item-inner"},et={style:{"padding-right":"8px"}},at={class:"title"},st={class:"title_card"},it={class:"card-box"},ot={class:"monitor-title"},nt={class:"title"},ct=["onClick"],rt={class:"monitor-table"},lt={class:"monitor"},dt={style:{"padding-left":"10px"}},ut=E({__name:"index",setup(pt){const D=v(),{getStationTree:W}=J(),d=v([]),t=f({monitor:{},drawerTitle:"",Statistics:[{className:"text-orange",title:"总供水量",count:0,unit:"m³",icon:"ic:outline-water-drop"},{className:"text-green",title:"昨日供水量",count:0,unit:"m³",icon:"subway:refresh-time"},{className:"text-blue",title:"今日供水量",count:0,unit:"m³",icon:"subway:time-2"},{className:"text-green",title:"上月供水量",count:0,unit:"m³",icon:"ri:calendar-event-line"},{className:"text-blue",title:"本月供水量",count:0,unit:"m³",icon:"ri:calendar-line"},{className:"text-red",title:"报警数",count:0,unit:"个",icon:"ph:bell-simple-bold"}],ShuiChangDatas:[],dataGroup:[],activeName:"全部",tabsList:[],stationTree:[],treeDataType:"Project",stationId:"",icon:K,keywords:""}),k=v(),L=f({title:"",labelWidth:"130px",width:1400,group:[],cancel:!1,onClosed:()=>{t.treeDataType,t.stationId=""}}),S=f({data:[],loading:!0,title:"区域划分",expandOnClickNode:!1,treeNodeHandleClick:async e=>{S.currentProject!==e&&(S.currentProject=e,t.treeDataType=e.data.type,t.treeDataType==="Project"&&(P().SET_selectedProject(e),d.value=[],await I()))}}),B=e=>{var n;(n=k.value)==null||n.openDrawer(),V(()=>{t.drawerTitle=e.title,t.monitor=e,t.stationId=e.id,t.treeDataType="Station"}),console.log(e.id)},N=f({type:"tabs",tabType:"simple",stretch:!1,tabs:[],handleTabClick:e=>{if(t.dataGroup=d.value,e.props.name!=="全部"){const n=d.value.filter(o=>o.id===e.props.name||o.title==="全部");t.dataGroup=n,t.ShuiChangDatas=n}}}),I=async e=>{var u,h;await Q({projectId:(u=P().selectedProject)==null?void 0:u.value}).then(s=>{var i,_,r,g,T,j;t.Statistics[0].count=((i=s.data.data)==null?void 0:i.yesterdayWaterSupply)||0,t.Statistics[1].count=((_=s.data.data)==null?void 0:_.todayWaterSupply)||0,t.Statistics[2].count=((r=s.data.data)==null?void 0:r.lastMonthWaterSupply)||0,t.Statistics[3].count=((g=s.data.data)==null?void 0:g.monthWaterSupply)||0,t.Statistics[4].count=((T=s.data.data)==null?void 0:T.totalWaterSupply)||0,t.Statistics[5].count=((j=s.data.data)==null?void 0:j.alarmNum)||0}).catch(()=>{t.Statistics[0].count=0,t.Statistics[1].count=0,t.Statistics[2].count=0,t.Statistics[3].count=0,t.Statistics[4].count=0,t.Statistics[5].count=0});const o=(h=(await R({projectId:e==null?void 0:e.value})).data)==null?void 0:h.data;console.log(o),N.tabs=[{value:"全部",label:"全部"}],o.map(s=>{N.tabs.push({value:s.stationId,label:s.name}),d.value.push({id:s.stationId,title:s.name,dataList:s.dataList||[]}),console.log("dddd",d.value),t.ShuiChangDatas=d.value,t.dataGroup=d.value})};v(0);const q=f({filters:[{type:"input",label:"",field:"keywords",placeholder:"请输入关键字进行筛选",btn:{icon:H.QUERY,type:"primary",perm:!0,click:()=>{var o;const n=(((o=D.value)==null?void 0:o.queryParams)||{}).keywords||"";t.dataGroup=t.ShuiChangDatas.filter(u=>u.title.includes(n))}}}]});return z(async()=>{const e=await W("水源地");S.data=e,S.currentProject=e[0],await I()}),(e,n)=>{const o=A,u=$,h=F,s=O;return p(),m("div",X,[c(u,{overlay:""},{default:y(()=>[a("div",Z,[(p(!0),m(w,null,b(t.Statistics,(i,_)=>(p(),m("div",{key:_,class:"statistics-item"},[a("div",tt,[a("div",et,[c(x(G),{icon:i.icon,class:"iconClass",style:{"font-size":"16px"}},null,8,["icon"])]),a("span",at,l(i.title),1),a("span",{class:C(["count",i.className])},l(i.count),3),a("span",{class:C(["unit",i.className])},l(i.unit),3)])]))),128))]),a("div",st,[c(o,{ref_key:"refSearch",ref:D,class:"serch",config:q},null,8,["config"])])]),_:1}),a("div",it,[a("div",{class:C(["card-item",{isDark:x(M)().isDark}])},[(p(!0),m(w,null,b(t.dataGroup,(i,_)=>(p(),m("div",{key:_,class:"card-content"},[c(u,{title:" ",class:"inner-card left"},{title:y(()=>[a("div",ot,[a("div",nt,[c(h,{src:t.icon,style:{width:"35px",height:"36px"}},null,8,["src"]),a("span",null,l(i.title),1)]),a("div",{onClick:r=>B(i)},[c(x(G),{icon:"ph:warning-circle-bold",style:{color:"#4f7db8","font-size":"18px"}})],8,ct)])]),default:y(()=>[a("div",rt,[a("div",lt,[(p(!0),m(w,null,b(i.dataList,(r,g)=>(p(),m("div",{key:g,class:"box-1"},[a("div",null,l(r.propertyName)+" "+l(r.unit?"("+r.unit+")":""),1),a("div",null,l(r.value||"无"),1)]))),128))])])]),_:2},1024)]))),128))],2)]),c(s,{ref_key:"refDrawer",ref:k,config:L},{title:y(()=>[c(h,{src:t.icon,style:{width:"35px",height:"36px"}},null,8,["src"]),a("span",dt,l(t.drawerTitle),1)]),default:y(()=>[c(Y,{"station-id":t.stationId,monitor:t.monitor},null,8,["station-id","monitor"])]),_:1},8,["config"])])}}}),Tt=U(ut,[["__scopeId","data-v-3a947e36"]]);export{Tt as default};
