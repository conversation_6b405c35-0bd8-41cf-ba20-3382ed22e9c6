package org.thingsboard.server.dao.plan;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.smartManagement.plan.CircuitTaskReport;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.plan.CircuitTaskReportCompleteRequest;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.plan.CircuitTaskReportPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.plan.CircuitTaskReportSaveRequest;

import java.util.List;

public interface CircuitTaskReportService {
    /**
     * 分页条件查询巡检任务报告
     *
     * @param request 分页查询条件
     * @return 分页查询结果
     */
    IPage<CircuitTaskReport> findAllConditional(CircuitTaskReportPageRequest request);

    /**
     * 保存巡检任务报告
     *
     * @param entity 实体信息
     * @return 保存好的实体
     */
    CircuitTaskReport save(CircuitTaskReportSaveRequest entity);

    /**
     * 增量修改
     *
     * @param entity 实体信息
     * @return 是否修改成功
     */
    boolean update(CircuitTaskReport entity);

    /**
     * 删除
     *
     * @param id 唯一标识
     * @return 是否删除成功
     */
    boolean delete(String id);

    /**
     * 批量保存
     *
     * @param entities 任务报告列表
     * @return 保存好的数据
     */
    List<CircuitTaskReport> saveAll(List<CircuitTaskReportSaveRequest> entities);

    /**
     * 到位操作
     *
     * @param req 详细信息
     * @return 是否成功
     */
    boolean present(CircuitTaskReportCompleteRequest req);

    /**
     * 反馈操作
     *
     * @param req 详细信息
     * @return 是否成功
     */
    boolean fallback(CircuitTaskReportCompleteRequest req);

    /**
     * 查询单个巡检报告
     * @param id 唯一标识
     * @return 巡检报告
     */
    CircuitTaskReport findById(String id);

    /**
     * 通过关键点/关键设备查询单个巡检报告
     *
     * @param taskCode 任务id
     * @param pointId  关键点/关键设备id
     * @param tenantId 客户id
     * @return 巡检报告
     */
    CircuitTaskReport findByPoint(String taskCode, String pointId, String tenantId);

    String arrivalRate();

    String feedbackRate();
}
