import{bn as B,fg as b,c$ as C,fh as M,A as k,fi as w,B as j,fj as F,fk as G,fl as A,fm as L}from"./index-r0dFAfgr.js";function O(r,n){var f=r.length;for(r.sort(n);f--;)r[f]=r[f].value;return r}function U(r,n){if(r!==n){var f=r!==void 0,i=r===null,d=r===r,u=B(r),c=n!==void 0,p=n===null,g=n===n,a=B(n);if(!p&&!a&&!u&&r>n||u&&c&&g&&!p&&!a||i&&c&&g||!f&&g||!d)return 1;if(!i&&!u&&!a&&r<n||a&&f&&d&&!i&&!u||p&&f&&d||!c&&d||!g)return-1}return 0}function $(r,n,f){for(var i=-1,d=r.criteria,u=n.criteria,c=d.length,p=f.length;++i<c;){var g=U(d[i],u[i]);if(g){if(i>=p)return g;var a=f[i];return g*(a=="desc"?-1:1)}}return r.index-n.index}function m(r,n,f){n.length?n=b(n,function(u){return C(u)?function(c){return M(c,u.length===1?u[0]:u)}:u}):n=[k];var i=-1;n=b(n,w(j));var d=F(r,function(u,c,p){var g=b(n,function(a){return a(u)});return{criteria:g,index:++i,value:u}});return O(d,function(u,c){return $(u,c,f)})}var t=G(function(r,n){if(r==null)return[];var f=n.length;return f>1&&A(r,n[0],n[1])?n=[]:f>2&&A(n[0],n[1],n[2])&&(n=[n[0]]),m(r,L(n,1),[])});export{m as b,U as c,t as s};
