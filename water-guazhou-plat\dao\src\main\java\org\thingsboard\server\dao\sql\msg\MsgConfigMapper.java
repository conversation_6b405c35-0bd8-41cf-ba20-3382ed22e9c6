package org.thingsboard.server.dao.sql.msg;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.thingsboard.server.dao.model.sql.MsgConfigEntity;

@Mapper
public interface MsgConfigMapper extends BaseMapper<MsgConfigEntity> {

    @Select("select * from tb_msg_config where sign_name like '%'||#{signName}||'%' and tenant_id = #{tenantId} order by create_time desc")
    Page<MsgConfigEntity> getList(IPage<MsgConfigEntity> iPage, @Param("signName") String signName, @Param("tenantId") String tenantId);

}
