import{eU as f}from"./index-r0dFAfgr.js";function m(r,e){if(f(r)!="object"||!r)return r;var t=r[Symbol.toPrimitive];if(t!==void 0){var n=t.call(r,e||"default");if(f(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(r)}function l(r){var e=m(r,"string");return f(e)=="symbol"?e:e+""}function p(r,e,t){return(e=l(e))in r?Object.defineProperty(r,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):r[e]=t,r}function v(r,e,t,n,u,c,o){try{var a=r[c](o),i=a.value}catch(s){return void t(s)}a.done?e(i):Promise.resolve(i).then(n,u)}function b(r){return function(){var e=this,t=arguments;return new Promise(function(n,u){var c=r.apply(e,t);function o(i){v(c,n,u,o,a,"next",i)}function a(i){v(c,n,u,o,a,"throw",i)}o(void 0)})}}export{b as _,p as a,l as t};
