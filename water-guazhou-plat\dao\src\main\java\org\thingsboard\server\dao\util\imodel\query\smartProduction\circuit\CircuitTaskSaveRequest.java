package org.thingsboard.server.dao.util.imodel.query.smartProduction.circuit;

import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
import org.thingsboard.server.dao.model.sql.smartProduction.circuit.CircuitTask;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

import java.util.Date;

import static org.thingsboard.server.dao.model.sql.smartProduction.circuit.GeneralTaskStatus.PENDING;

@Getter
@Setter
public class CircuitTaskSaveRequest extends SaveRequest<CircuitTask> {
    // 配置类型，用于数据隔离。三种类型：水源、水厂、二供泵房。
    @NotNullOrEmpty
    private String type;

    // 任务编码
    @NotNullOrEmpty
    private String code;

    // 任务名称
    @NotNullOrEmpty
    private String name;

    // 任务类型。常规任务、临时任务。常规任务为巡检计划生成的任务；临时任务为任务页面新增的任务
    // @NotNullOrEmpty
    // private String taskType;

    // 执行巡检人员
    @NotNullOrEmpty(refTable = "tb_user")
    private String executionUserId;

    // 巡检成果审核人员
    @NotNullOrEmpty(refTable = "tb_user")
    private String auditUserId;

    // 预计开始时间
    @NotNullOrEmpty
    private Date startTime;

    // 预计结束时间
    @NotNullOrEmpty
    private Date endTime;
    // 要巡检的站点
    @NotNullOrEmpty
    private String stationId;

    // 巡检模板
    @NotNullOrEmpty
    private String templateId;

    @Override
    protected CircuitTask build() {
        CircuitTask entity = new CircuitTask();
        entity.setCode(code);
        entity.setCreator(currentUserUUID());
        entity.setCreateTime(new Date());
        entity.setTenantId(tenantId());
        entity.setStatus(PENDING.name());
        commonSet(entity);
        return entity;
    }

    @Override
    protected CircuitTask update(String id) {
        CircuitTask entity = new CircuitTask();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(CircuitTask entity) {
        entity.setType(type);
        entity.setName(name);
        entity.setTaskType("临时任务");
        entity.setExecutionUserId(executionUserId);
        entity.setAuditUserId(auditUserId);
        entity.setStartTime(startTime);
        entity.setEndTime(endTime);
        entity.setStationId(stationId);
        entity.setTemplateId(templateId);
    }

    // @Override
    // protected StringSetter<CircuitTaskItemSaveRequest> parentSetter() {
    //     return CircuitTaskItemSaveRequest::setMainId;
    // }
}