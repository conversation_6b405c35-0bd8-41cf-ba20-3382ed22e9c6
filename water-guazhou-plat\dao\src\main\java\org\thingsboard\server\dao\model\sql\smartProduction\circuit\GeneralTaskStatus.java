package org.thingsboard.server.dao.model.sql.smartProduction.circuit;

import org.thingsboard.server.dao.util.imodel.response.NameDisplayableEnum;

public enum GeneralTaskStatus implements NameDisplayableEnum {
    PENDING("发起"),
    RECEIVED("接收"),
    VERIFY("审核"),
    APPROVED("通过"),
    REJECTED("审核驳回");

    private final String displayName;

    GeneralTaskStatus(String displayName) {
        this.displayName = displayName;
    }

    @Override
    public String getDisplayName() {
        return displayName;
    }
}