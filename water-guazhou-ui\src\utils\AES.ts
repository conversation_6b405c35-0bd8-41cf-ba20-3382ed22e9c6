import CryptoJS from 'crypto-js';

export default {
  // 随机生成指定数量的16进制key
  generatekey(num) {
    const library =
      'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let key = '';
    for (let i = 0; i < num; i++) {
      const randomPoz = Math.floor(Math.random() * library.length);
      key += library.substring(randomPoz, randomPoz + 1);
    }
    return key;
  },

  // 加密
  encrypt(word, keyStr) {
    keyStr = keyStr || 'istarcloudaeskey'; // 判断是否存在ksy，不存在就用定义好的key
    const key = CryptoJS.enc.Utf8.parse(keyStr);
    const srcs = CryptoJS.enc.Utf8.parse(word);
    const encrypted = CryptoJS.AES.encrypt(srcs, key, {
      mode: CryptoJS.mode.ECB,
      padding: CryptoJS.pad.Pkcs7
    });
    return encrypted.toString();
  },
  // 解密
  decrypt(word, keyStr) {
    keyStr = keyStr || 'istarcloudaeskey';
    const key = CryptoJS.enc.Utf8.parse(keyStr);
    const decrypt = CryptoJS.AES.decrypt(word, key, {
      mode: CryptoJS.mode.ECB,
      padding: CryptoJS.pad.Pkcs7
    });
    return CryptoJS.enc.Utf8.stringify(decrypt).toString();
  },

  // 加密
  encryptT(word, key) {
    const keys = CryptoJS.enc.Utf8.parse(key);
    const str = CryptoJS.enc.Utf8.parse(word);
    const encrypted = CryptoJS.AES.encrypt(str, keys, {
      mode: CryptoJS.mode.ECB,
      padding: CryptoJS.pad.Pkcs7
    });
    console.log(encrypted);
    return encrypted.ciphertext.toString().toUpperCase();
  },

  // 解密
  decryptT(word, key) {
    const keys = CryptoJS.enc.Utf8.parse(key);
    const decrypt = CryptoJS.AES.decrypt(
      CryptoJS.enc.Base64.stringify(CryptoJS.enc.Hex.parse(word)),
      keys,
      {
        mode: CryptoJS.mode.ECB,
        padding: CryptoJS.pad.Pkcs7
      }
    );
    return decrypt.toString(CryptoJS.enc.Utf8);
  }
};
