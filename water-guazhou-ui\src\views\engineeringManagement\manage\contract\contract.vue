<!-- 工程管理-工程管理-合同管理 -->
<template>
  <div class="wrapper">
    <CardSearch ref="refSearch" :config="cardSearchConfig" />
    <CardTable :config="TableConfig" class="card-table"></CardTable>
    <DialogForm ref="refForm" :config="addOrUpdateConfig"></DialogForm>
  </div>
</template>

<script lang="ts" setup>
import { Refresh } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { ICONS } from '@/common/constans/common';
import {
  getProjectType,
  getConstructionContractList,
  postConstructionContract,
  getConstructionContractGlobalExport,
  postConstructionContractComplete,
  getConstructionContractExport,
  getConstructionContractType
} from '@/api/engineeringManagement/manage';
import detail from './components/detail.vue';
import { StatusType } from '../../data';
import { traverse } from '@/utils/GlobalHelper';

const refSearch = ref<ICardSearchIns>();
const refForm = ref<IDialogFormIns>();

const cardSearchConfig = ref<ISearch>({
  filters: [
    { label: '工程编号', field: 'constructionCode', type: 'input' },
    { label: '工程名称', field: 'constructionName', type: 'input' },
    { label: '业主单位', field: 'firstpartOrganization', type: 'input' },
    { label: '监理单位', field: 'superVisitorOrganization', type: 'input' },
    {
      label: '工程类别',
      field: 'constructionTypeId',
      type: 'select',
      options: computed(() => data.projectType) as any
    }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          type: 'default',
          perm: true,
          text: '导出',
          icon: ICONS.DOWNLOAD,
          click: () => {
            getConstructionContractGlobalExport().then((res) => {
              const url = window.URL.createObjectURL(res.data);
              const link = document.createElement('a');
              link.style.display = 'none';
              link.href = url;
              link.setAttribute('download', `合同管理.xlsx`);
              document.body.appendChild(link);
              link.click();
            });
          }
        },
        {
          type: 'default',
          perm: true,
          text: '重置',
          svgIcon: shallowRef(Refresh),
          click: () => {
            refSearch.value?.resetForm();
            refreshData();
          }
        },
        {
          perm: true,
          text: '查询',
          icon: ICONS.QUERY,
          click: () => refreshData()
        }
      ]
    }
  ]
});

const TableConfig = reactive<ICardTable>({
  defaultExpandAll: false,
  indexVisible: true,
  expandable: true,
  expandComponent: shallowRef(detail),
  extendedReturn: () => {
    refreshData();
  },
  rowKey: 'constructionCode',
  columns: [
    { label: '工程编号', prop: 'constructionCode' },
    { label: '工程名称', prop: 'constructionName' },
    { label: '工程类别', prop: 'constructionTypeName' },
    { label: '业主单位', prop: 'firstpartOrganization' },
    { label: '施工单位', prop: 'constructionOrganization' },
    { label: '设计单位', prop: 'secondpartOrganization' },
    { label: '监理单位', prop: 'supervisorOrganization' },
    { label: '合同总金额(万元)', prop: 'contractTotalCost' },
    {
      label: '工作状态',
      prop: 'status',
      tag: true,
      tagColor: (row): string =>
        StatusType.find((item) => item.value === row.status)?.color || '',
      formatter: (row) =>
        StatusType.find((item) => item.value === row.status)?.label
    }
  ],
  operationWidth: '360px',
  operations: [
    {
      disabled: (val) => val.status === 'COMPLETED',
      isTextBtn: false,
      type: 'primary',
      text: '添加工程合同',
      perm: true,
      click: (row) => {
        clickAdd(row);
      }
    },
    {
      disabled: (val) => val.status === 'COMPLETED',
      isTextBtn: false,
      type: 'success',
      text: '完成',
      perm: true,
      click: (row) => {
        postConstructionContractComplete(row.constructionCode).then((res) => {
          if (res.data.code === 200) {
            ElMessage.success('已完成');
          } else {
            ElMessage.warning('完成失败');
          }
          refreshData();
        });
      }
    },
    {
      isTextBtn: false,
      text: '导出合同情况',
      perm: true,
      click: (row) => {
        getConstructionContractExport(row.constructionCode).then((res) => {
          const url = window.URL.createObjectURL(res.data);
          const link = document.createElement('a');
          link.style.display = 'none';
          link.href = url;
          link.setAttribute('download', `${row.constructionName}合同管理.xlsx`);
          document.body.appendChild(link);
          link.click();
        });
      }
    }
  ],
  dataList: [],
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page;
      TableConfig.pagination.limit = size;
      refreshData();
    }
  }
});

const addOrUpdateConfig = reactive<IDialogFormConfig>({
  title: '添加工程合同',
  appendToBody: true,
  labelWidth: '130px',
  dialogWidth: '1000px',
  submitting: false,
  submit: (params: any) => {
    addOrUpdateConfig.submitting = true;
    let text = '新增';
    if (params.id) text = '修改';
    if (params.time) {
      params.workTimeBegin = params.time[0];
      params.workTimeEnd = params.time[1];
      delete params.time;
    }
    postConstructionContract(params)
      .then((res) => {
        addOrUpdateConfig.submitting = false;
        if (res.data.code === 200) {
          ElMessage.success(text + '成功');
          refForm.value?.closeDialog();
        } else {
          ElMessage.warning(text + '失败');
        }
      })
      .catch((error) => {
        addOrUpdateConfig.submitting = false;
        ElMessage.warning(error);
      });
  },
  defaultValue: {},
  group: [
    {
      fields: [
        {
          xs: 12,
          type: 'input',
          label: '合同编号',
          field: 'code',
          rules: [{ required: true, message: '请输入合同编号' }]
        },
        {
          xs: 12,
          type: 'select',
          label: '合同分类',
          field: 'type',
          options: computed(() => data.ConstructionContractType) as any
        },
        {
          xs: 12,
          type: 'input',
          label: '合同名称',
          field: 'name',
          rules: [{ required: true, message: '请输入合同名称' }]
        },
        {
          xs: 12,
          type: 'input',
          label: '工程名称',
          field: 'constructionName',
          disabled: true
        },
        {
          xs: 12,
          type: 'input',
          label: '工程编号',
          field: 'constructionCode',
          disabled: true
        },
        {
          xs: 12,
          type: 'input',
          label: '甲方单位',
          field: 'firstpartOrganization',
          rules: [{ required: true, message: '请输入甲方单位' }]
        },
        {
          xs: 12,
          type: 'input',
          label: '乙方单位',
          field: 'secondpartOrganization',
          rules: [{ required: true, message: '请输入乙方单位' }]
        },
        {
          xs: 12,
          type: 'input',
          label: '甲方代表',
          field: 'firstpartRepresentative',
          rules: [{ required: true, message: '请输入甲方代表' }]
        },
        {
          xs: 12,
          type: 'input',
          label: '乙方联系人',
          field: 'secondpartRepresentative'
        },
        {
          xs: 12,
          type: 'input',
          label: '甲方代表联系电话',
          field: 'firstpartPhone'
        },
        {
          xs: 12,
          type: 'input',
          label: '乙方联系电话',
          field: 'secondpartPhone'
        },
        {
          xs: 12,
          type: 'number',
          label: '合同金额',
          field: 'cost',
          rules: [{ required: true, message: '请输入合同金额' }]
        },
        {
          xs: 12,
          type: 'daterange',
          label: '合同工期',
          field: 'time'
        },
        {
          xs: 12,
          type: 'date',
          label: '签署时间',
          field: 'signTime'
        },
        {
          type: 'textarea',
          label: '详细说明',
          field: 'remark'
        },
        {
          type: 'file',
          label: '附件',
          field: 'attachments'
        }
      ]
    }
  ]
});

const clickAdd = (row: { [x: string]: any }) => {
  addOrUpdateConfig.title = '添加工程合同';
  addOrUpdateConfig.defaultValue = {
    constructionCode: row.constructionCode,
    constructionName: row.constructionName
  };
  refForm.value?.openDialog();
};

const data = reactive({
  // 项目类别
  projectType: [],
  // 合同类型
  ConstructionContractType: [],
  getOptions: () => {
    getProjectType({ page: 1, size: -1 }).then((res) => {
      data.projectType = traverse(res.data.data.data || [], 'children');
    });

    getConstructionContractType({ page: 1, size: -1 }).then((res) => {
      data.ConstructionContractType = traverse(
        res.data.data.data || [],
        'children'
      );
    });
  }
});

const refreshData = async () => {
  const params: any = {
    size: TableConfig.pagination.limit || 20,
    page: TableConfig.pagination.page || 1,
    ...(refSearch.value?.queryParams || {})
  };
  getConstructionContractList(params).then((res) => {
    TableConfig.dataList = res.data.data.data || [];
    TableConfig.pagination.total = res.data.data.total || 0;
  });
};

onMounted(() => {
  refreshData();
  data.getOptions();
});
</script>

<style>
.cs {
  margin-top: 10px;
  padding-top: 20px;
}
</style>
