package org.thingsboard.server.dao.menu2;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.id.UserId;
import org.thingsboard.server.common.data.menu.Menu;
import org.thingsboard.server.common.data.menu.MenuPool;
import org.thingsboard.server.common.data.menu.MenuPoolVO;
import org.thingsboard.server.dao.menu.MenuPoolService;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.model.sql.ApplicationMenuEntity;
import org.thingsboard.server.dao.model.sql.TenantApplicationEntity;
import org.thingsboard.server.dao.model.sql.TenantMenus;
import org.thingsboard.server.dao.role.RoleService;
import org.thingsboard.server.dao.sql.menu2.TenantMenusRepository;
import org.thingsboard.server.dao.sql.tenant.ApplicationMenuRelationRepository;
import org.thingsboard.server.dao.tenant.TenantApplicationService;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import static org.thingsboard.server.common.data.CacheConstants.TENANT_MENUS_CACHE;

@Slf4j
@Service
public class TenantMenusServiceImpl implements TenantMenusService {

    @Autowired
    private TenantMenusRepository tenantMenusRepository;

    @Autowired
    private TenantApplicationService tenantApplicationService;

    @Autowired
    private MenuPoolService menuPoolService;

    @Autowired
    private TenantMenusRoleService tenantMenusRoleService;

    @Autowired
    private RoleService roleService;

    @Autowired
    private ApplicationMenuRelationRepository menuRelationRepository;

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Caching(evict = {@CacheEvict(cacheNames = TENANT_MENUS_CACHE, key = "{#tenantId}")})
    public TenantMenus saveMenu(Menu menu, String parentId, TenantId tenantId) {
        TenantMenus tenantMenus = MenuToTenantMenus(menu);
        tenantMenus.setParentId(parentId);

        if (tenantMenus.getParentId() == null) {
            tenantMenus.setParentId(ModelConstants.MENU_POOL_ROOT_STR);
        }
        TenantMenus result = tenantMenusRepository.save(tenantMenus);

        //TODO 2025/5/7 添加到应用菜单关系中，cc要求的，个人感觉不太合理
        // 先查询是否存在，存在则不重复添加，不存在才添加
        ApplicationMenuEntity existingEntity = menuRelationRepository.findByMenuId(result.getId());
        if (existingEntity == null) {
            ApplicationMenuEntity applicationMenuEntity = new ApplicationMenuEntity();
            applicationMenuEntity.setMenuId(result.getId());
            if(StringUtils.isNotBlank(result.getParentId())){
                ApplicationMenuEntity byMenuId = menuRelationRepository.findByMenuId(result.getParentId());
                if (ObjectUtils.isNotEmpty(byMenuId)) {
                    applicationMenuEntity.setTenantApplicationId(byMenuId.getTenantApplicationId());
                }
            }
            menuRelationRepository.save(applicationMenuEntity);
        }
        return result;
    }

    @Override
    @Cacheable(cacheNames = TENANT_MENUS_CACHE, key = "{#tenantId}")
    public List<MenuPoolVO> getSelectableTree(TenantId tenantId) {
        // 获取根节点下的一级菜单
        List<TenantMenus> tenantMenus = this.findByParentId(ModelConstants.MENU_POOL_ROOT_STR, tenantId);
//        List<TenantMenus> tenantMenus = tenantMenusRepository.getSelectableTree(ModelConstants.MENU_POOL_ROOT_STR, UUIDConverter.fromTimeUUID(tenantId.getId()));
        // 查询全部的菜单
        List<TenantMenus> tenantMenuList = this.findByTenantId(tenantId);
        // 按父ID分组
        Map<String, List<TenantMenus>> tenantMenuMap = new HashMap<>();
        for (TenantMenus menu : tenantMenuList) {
            List<TenantMenus> list = new ArrayList<>();
            if (tenantMenuMap.containsKey(menu.getParentId())) {
                list = tenantMenuMap.get(menu.getParentId());
            }

            list.add(menu);
            tenantMenuMap.put(menu.getParentId(), list);
        }

        List<MenuPoolVO> collect = tenantMenus.stream()
                .map(TenantMenus::toMenuPoolVO)
                .peek(menu -> menu.setChildren(getChildren(menu, tenantMenuMap, tenantId)))
                .collect(Collectors.toList());
        return collect;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Caching(evict = {@CacheEvict(cacheNames = TENANT_MENUS_CACHE, key = "{#tenantId}")})
    public JSONObject remove(String id, TenantId tenantId) {
        JSONObject result = new JSONObject();
        List<TenantMenus> list = tenantMenusRepository.findByParentIdAndTenantId(id, UUIDConverter.fromTimeUUID(tenantId.getId()));
        if (list == null || list.size() == 0) {
            tenantMenusRepository.delete(id);
            menuRelationRepository.deleteByMenuId(id);
            result.put("code", 200);
            result.put("message", "操作成功");
        } else {
            result.put("code", 400);
            result.put("message", "请先删除该菜单的子菜单!");
        }
        return result;
    }

    @Override
//    @Cacheable(cacheNames = TENANT_MENUS_CACHE, key = "{#tenantApplicationId}")
    public List<Menu> findByTenantApplication(UserId userId, String tenantApplicationId, TenantId tenantId) {
        List<Menu> result = new ArrayList<>();
        try {
            // 获取一级菜单
            List<TenantMenus> parentMenuList = tenantMenusRepository.getSelectableTree(UUIDConverter.fromTimeUUID(tenantId.getId()), ModelConstants.MENU_POOL_ROOT_STR);
            Map<String, TenantMenus> parentMenuMap = new HashMap<>();
            if (parentMenuList != null) {
                parentMenuList.forEach(parentMenu -> parentMenuMap.put(parentMenu.getId(), parentMenu));
            }
            List<TenantMenus> allTenantMenus = tenantMenusRepository.findByTenantIdOrderByOrderNumDesc(UUIDConverter.fromTimeUUID(tenantId.getId()));
            Map<String, TenantMenus> allMap = allTenantMenus.stream().collect(Collectors.toMap(TenantMenus::getId, tenantMenus -> tenantMenus));

            // 获取应用的菜单列表
            List<String> menuIdList = new ArrayList<>();
            if (userId != null) {
                String roleId = roleService.getRoleIdByUserId(userId);
                menuIdList = tenantMenusRoleService.findRoleMenus(roleId, tenantApplicationId, tenantId);
            } else {
                menuIdList = tenantApplicationService.selectedMenuList(tenantApplicationId);
            }

            List<TenantMenus> tenantMenusList = tenantMenusRepository.findByIdInAndTenantId(menuIdList, UUIDConverter.fromTimeUUID(tenantId.getId()));

            Set<TenantMenus> applicationParentMenuList = new HashSet<>();
            Map<String, List<TenantMenus>> childrenMap = new HashMap<>();
            for (TenantMenus tenantMenus : tenantMenusList) {
                if (tenantMenus.getParentId().equals(ModelConstants.MENU_POOL_ROOT_STR)) {
                    applicationParentMenuList.add(tenantMenus);
                } else {
                    String parentId = tenantMenus.getParentId();
                    TenantMenus parent = parentMenuMap.get(parentId);
                    if (parent == null) {
                        parent = allMap.get(tenantMenus.getParentId());
                    }
                    if (!parent.getParentId().equals(ModelConstants.MENU_POOL_ROOT_STR)) {// 不为顶级菜单, 查询其上级菜单
                        TenantMenus top = allMap.get(parent.getParentId());
                        applicationParentMenuList.add(top);
                        List<TenantMenus> menus = childrenMap.get(top.getId());
                        if (menus == null) {
                            menus = new ArrayList<>();
                        }
                        menus.add(parent);
                        childrenMap.put(top.getId(), menus);
                    }
                    applicationParentMenuList.add(parent);
                    List<TenantMenus> menus = childrenMap.get(parentId);
                    if (menus == null) {
                        menus = new ArrayList<>();
                    }
                    menus.add(tenantMenus);

                    childrenMap.put(parentId, new ArrayList<>(new LinkedHashSet<>(menus)));
                }
            }
            List<TenantMenus> list = new ArrayList<>(applicationParentMenuList);
            list.sort((o1, o2) -> {
                Integer orderNum1 = o1.getOrderNum();
                Integer orderNum2 = o2.getOrderNum();
                if (orderNum1 == null) {
                    orderNum1 = 0;
                }
                if (orderNum2 == null) {
                    orderNum2 = 0;
                }

                return orderNum2.compareTo(orderNum1);
            });
            for (TenantMenus parent : list) {
                Menu menu = tenantMenusToMenu(parent);

                if (parent.getParentId().equals(ModelConstants.MENU_POOL_ROOT_STR)) {
                    // 获取该一级菜单下的二级菜单
                    getChildren(childrenMap, parent.getId(), menu);
                    result.add(menu);
                }
            }
            return result;
        } catch (Exception e) {
            throw new RuntimeException("System error!", e);
        }
    }

    private void getChildren(Map<String, List<TenantMenus>> childrenMap, String parent, Menu menu) {
        List<TenantMenus> children = childrenMap.get(parent);
        if (children != null && children.size() > 0) {
            children.sort((o1, o2) -> {
                Integer orderNum1 = o1.getOrderNum();
                Integer orderNum2 = o2.getOrderNum();
                return orderNum2.compareTo(orderNum1);
            });
            List<Menu> childrenMenus = tenantMenusListToMenuList(children);
            childrenMenus = childrenMenus.stream().distinct().collect(Collectors.toList());
            menu.setChildren(childrenMenus);

            for (Menu childMenu : childrenMenus) {
                getChildren(childrenMap, childMenu.getId(), childMenu);
            }
        }

    }

    @Override
    public List<Menu> findCustomerMenuByTenantApplication(UserId userId, String tenantApplicationId, TenantId tenantId) {
        return null;
    }

    @Override
    public Menu findById(String id) throws IOException {
        return tenantMenusToMenu(tenantMenusRepository.findOne(id));
    }

    @Override
    public List<TenantMenus> findTree(TenantId tenantId) {
        List<TenantMenus> tenantMenusList = tenantMenusRepository.findByTenantIdOrderByOrderNumDesc(UUIDConverter.fromTimeUUID(tenantId.getId()));

        // 将父子菜单分组
        Map<String, List<TenantMenus>> tenantMenusMap = new HashMap<>();
        for (TenantMenus tenantMenus : tenantMenusList) {
            String parentId = tenantMenus.getParentId();
            List<TenantMenus> list = new ArrayList<>();
            if (tenantMenusMap.containsKey(parentId)) {
                list = tenantMenusMap.get(parentId);
            }

            list.add(tenantMenus);
            tenantMenusMap.put(parentId, list);
        }

        // 获取顶级菜单
        List<TenantMenus> topMenuList = tenantMenusList.stream()
                .filter(tenantMenus -> ModelConstants.MENU_POOL_ROOT_STR.equals(tenantMenus.getParentId()))
                .map(topMenu -> {
                    getChildren(topMenu, tenantMenusMap);

                    return topMenu;
                })
                .collect(Collectors.toList());

        return topMenuList;
    }

    @Override
    @Transactional
    @Caching(evict = {@CacheEvict(cacheNames = TENANT_MENUS_CACHE, key = "{#tenantId}")})
    public void importMenu(List<TenantMenus> menuList, TenantId tenantId) {
        String newTenantId = UUIDConverter.fromTimeUUID(tenantId.getId());
        // 先删除
        tenantMenusRepository.deleteByTenantId(newTenantId);

        // 导入新的顶级菜单
        importChildren(ModelConstants.MENU_POOL_ROOT_STR, newTenantId, menuList);
    }

    @Override
    public List<TenantMenus> findByTenantId(TenantId id) {
        return tenantMenusRepository.findByTenantIdOrderByOrderNumDesc(UUIDConverter.fromTimeUUID(id.getId()));
    }

    @Override
    @Caching(evict = {@CacheEvict(cacheNames = TENANT_MENUS_CACHE, key = "{#tenantId}")})
    public void menuPoolToTenantMenus(TenantId tenantId) {
        List<MenuPool> all = menuPoolService.findAll();
        String tenantIdStr = UUIDConverter.fromTimeUUID(tenantId.getId());

        List<TenantMenus> list = new ArrayList<>();
        for (MenuPool menuPool : all) {
            TenantMenus tenantMenus = new TenantMenus();

            tenantMenus.setName(menuPool.getDefaultName());
            tenantMenus.setTenantId(tenantIdStr);
            tenantMenus.setAdditionalInfo(menuPool.getAdditionalInfo());
            tenantMenus.setIcon(menuPool.getIcon());
            tenantMenus.setParentId(UUIDConverter.fromTimeUUID(menuPool.getParentId().getId()));
            tenantMenus.setId(UUIDConverter.fromTimeUUID(menuPool.getId().getId()));
            tenantMenus.setOrderNum(menuPool.getOrderNum());
            list.add(tenantMenus);
        }

        tenantMenusRepository.save(list);
    }

    @Override
    public List<TenantMenus> findByParentId(String parentId, TenantId tenantId) {
        return tenantMenusRepository.getSelectableTree(parentId, UUIDConverter.fromTimeUUID(tenantId.getId()));
    }

    @Override
    public List<MenuPoolVO> findTreeByTenantApplication(UserId userId, String tenantApplicationId, TenantId tenantId) {
        try {
            // 获取一级菜单
            List<TenantMenus> parentMenuList = tenantMenusRepository.getSelectableTree(UUIDConverter.fromTimeUUID(tenantId.getId()), ModelConstants.MENU_POOL_ROOT_STR);
            Map<String, TenantMenus> parentMenuMap = new HashMap<>();
            if (parentMenuList != null) {
                parentMenuList.forEach(parentMenu -> parentMenuMap.put(parentMenu.getId(), parentMenu));
            }
            List<TenantMenus> allTenantMenus = tenantMenusRepository.findByTenantIdOrderByOrderNumDesc(UUIDConverter.fromTimeUUID(tenantId.getId()));
            Map<String, TenantMenus> allMap = allTenantMenus.stream().collect(Collectors.toMap(TenantMenus::getId, tenantMenus -> tenantMenus));

            // 获取应用的菜单列表
            List<String> menuIdList = new ArrayList<>();
            if (userId != null) {
                String roleId = roleService.getRoleIdByUserId(userId);
                menuIdList = tenantMenusRoleService.findRoleMenus(roleId, tenantApplicationId, tenantId);
            } else {
                menuIdList = tenantApplicationService.selectedMenuList(tenantApplicationId);
            }

            List<TenantMenus> tenantMenusList = tenantMenusRepository.findByIdInAndTenantId(menuIdList, UUIDConverter.fromTimeUUID(tenantId.getId()));

            Set<TenantMenus> applicationParentMenuList = new HashSet<>();
            Map<String, List<TenantMenus>> childrenMap = new HashMap<>();
            for (TenantMenus tenantMenus : tenantMenusList) {
                if (tenantMenus.getParentId().equals(ModelConstants.MENU_POOL_ROOT_STR)) {
                    applicationParentMenuList.add(tenantMenus);
                } else {
                    String parentId = tenantMenus.getParentId();
                    TenantMenus parent = allMap.get(parentId);
                    if (parent == null) {
                        parent = allMap.get(tenantMenus.getParentId());
                    }
                    if (!parent.getParentId().equals(ModelConstants.MENU_POOL_ROOT_STR)) {// 不为顶级菜单, 查询其上级菜单
                        TenantMenus top = parentMenuMap.get(tenantMenus.getParentId());
                        applicationParentMenuList.add(top);
                        List<TenantMenus> menus = childrenMap.get(top.getId());
                        if (menus == null) {
                            menus = new ArrayList<>();
                        }
                        menus.add(parent);
                        childrenMap.put(top.getId(), menus);
                    }
                    applicationParentMenuList.add(parent);
                    List<TenantMenus> menus = childrenMap.get(parentId);
                    if (menus == null) {
                        menus = new ArrayList<>();
                    }
                    menus.add(tenantMenus);

                    childrenMap.put(parentId, new ArrayList<>(new LinkedHashSet<>(menus)));
                }
            }
            List<TenantMenus> list = new ArrayList<>(applicationParentMenuList);
            list.sort((o1, o2) -> {
                Integer orderNum1 = o1.getOrderNum();
                Integer orderNum2 = o2.getOrderNum();
                if (orderNum1 == null) {
                    orderNum1 = 0;
                }
                if (orderNum2 == null) {
                    orderNum2 = 0;
                }

                return orderNum2.compareTo(orderNum1);
            });

            List<MenuPoolVO> result = list.stream()
                    .map(TenantMenus::toMenuPoolVO)
                    .peek(menu -> menu.setChildren(getChildren(menu, childrenMap, tenantId)))
                    .collect(Collectors.toList());
            return result;
        } catch (Exception e) {
            throw new RuntimeException("System error!", e);
        }
    }

    /**
     * 递归新增导入的菜单与子菜单
     */
    private void importChildren(String parentId, String tenantId, List<TenantMenus> menuList) {
        if (menuList == null) {
            return;
        }
        for (TenantMenus tenantMenus : menuList) {
            tenantMenus.setParentId(parentId);
            tenantMenus.setTenantId(tenantId);
            tenantMenus.setId(null);// 重置菜单

            tenantMenus = tenantMenusRepository.save(tenantMenus);
        }

        // 保存新的子菜单
        for (TenantMenus tenantMenus : menuList) {
            List<TenantMenus> children = tenantMenus.getChildren();
            importChildren(tenantMenus.getId(), tenantId, children);
        }
    }

    private void getChildren(TenantMenus topMenu, Map<String, List<TenantMenus>> tenantMenusMap) {
        List<TenantMenus> children = tenantMenusMap.get(topMenu.getId());
        if (children != null) {
            for (TenantMenus child : children) {
                getChildren(child, tenantMenusMap);
            }
        }
        topMenu.setChildren(children);
    }

    private List<MenuPoolVO> getChildren(MenuPoolVO menu, Map<String, List<TenantMenus>> tenantMenuMap, TenantId tenantId) {
        List<TenantMenus> childrenTree = tenantMenuMap.get(menu.getId());

        if (childrenTree != null) {
            List<MenuPoolVO> menuPoolVOS = TenantMenus.toMenuPoolVOList(childrenTree);
            for (MenuPoolVO menuPoolVO : menuPoolVOS) {
                menuPoolVO.setChildren(getChildren(menuPoolVO, tenantMenuMap, tenantId));
            }

            return menuPoolVOS;
        }

        return null;
    }
}
