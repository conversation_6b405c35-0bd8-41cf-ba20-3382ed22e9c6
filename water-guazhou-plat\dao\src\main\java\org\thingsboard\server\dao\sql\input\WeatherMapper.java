package org.thingsboard.server.dao.sql.input;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.Weather;

@Mapper
public interface WeatherMapper extends BaseMapper<Weather> {

    IPage<Weather> findList(IPage<Weather> pageRequest, @Param("time") String time, @Param("tenantId") String tenantId);
}
