import{d as L,c as U,r as v,s as y,b as f,o as q,g as W,n as R,p as e,q as o,F as i,bh as g,i as N,aj as V,G as j,aq as z,an as M,bo as A,bR as J,cy as H,bz as K,bU as O,bW as Q,cE as X,J as Y,al as Z,b7 as $,db as tt,C as at}from"./index-r0dFAfgr.js";import et from"./PatrolDetail-3FfvS_c4.js";import{_ as ot}from"./Search-NSrhrIa_.js";import{a as st,d as it,e as rt,f as lt}from"./plan-BLf3nu6_.js";import{P as D}from"./config-C9CMv0E7.js";import"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import"./Videor.vue_vue_type_script_setup_true_lang-EsHlP83o.js";import"./InlineForm.vue_vue_type_style_index_0_lang-s-ANlzyw.js";import"./circuitTaskFormRecord-CjbtiPXk.js";import"./FeatureHelper-Da16o0mu.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./widget-BcWKanF2.js";import"./pe-B8dP0-Ut.js";import"./geometryEngine-OGzB5MRq.js";import"./geometryEngineBase-BhsKaODW.js";import"./hydrated-DLkO5ZPr.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./TileLayer-B5vQ99gG.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./commonProperties-DqNQ4F00.js";import"./project-DUuzYgGl.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./IdentifyResult-4DxLVhTm.js";import"./QueryHelper-ILO3qZqg.js";import"./useWaterPoint-Bv0z6ym6.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./useDistrict-B4Fis32p.js";import"./area-Bpl-8n1R.js";import"./LayerHelper-Cn-iiqxI.js";import"./pipe-nogVzCHG.js";import"./config-DqqM5K5L.js";import"./DateFormatter-Bm9a68Ax.js";import"./OutsideWorkOrder-C6s8joBt.js";import"./index-CpGhZCTT.js";import"./useCTI-CrDoUkpT.js";const nt={class:"patrol-statistics-container"},pt={class:"patrol-statistics-content"},ct={key:0,class:"page-wrapper"},dt={class:"statistics-cards"},mt={class:"statistics-card-content"},ft={class:"statistics-card-value"},ut={class:"statistics-card-content"},_t={class:"statistics-card-value"},ht={class:"statistics-card-content"},bt={class:"statistics-card-value"},vt={class:"statistics-card-content"},gt={class:"statistics-card-value"},kt={class:"table-box"},Tt={class:"table-header"},yt={class:"page-wrapper"},wt={class:"detail-header"},Pt={class:"detail-main overlay-y"},Ct=L({__name:"PatrolTaskStatistics",setup(xt){const k=U(),_=v({tabs:[],loading:!1,curPage:"table",orderTypes:[]}),l=v({totalTasks:0,completedTasks:0,presentRate:0,feedbackRate:0}),I=v({scrollBarGradientColor:"#fafafa",filters:[{type:"radio-button",options:[{label:"全部",value:""},{label:"常规",value:"true"},{label:"临时",value:"false"}],field:"isNormalPlan"},{type:"datetimerange",field:"fromTime",label:"开始时间"},{type:"user-select",labelWidth:60,field:"receiveUserId",label:"巡检员"},{type:"user-select",field:"creator",label:"创建人"},{type:"input",label:"快速查找",field:"keyword"}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",svgIcon:y(Z),click:()=>h()},{perm:!0,text:"重置",type:"default",svgIcon:y($),click:()=>{var a;(a=k.value)==null||a.resetForm(),h()}}]}],defaultParams:{}}),r=v({dataList:[],columns:[{minWidth:120,label:"是否常规计划",prop:"isNormalPlan",tag:!0,tagColor:a=>a.isNormalPlan?"#409eff":"#0cbb4a",formatter:a=>a.isNormalPlan?"常规":"临时"},{minWidth:120,label:"任务编号",prop:"code"},{minWidth:120,label:"任务名称",prop:"name"},{minWidth:120,label:"巡检周期",prop:"planCircleName"},{minWidth:160,label:"开始时间",prop:"beginTime"},{minWidth:160,label:"结束时间",prop:"endTime"},{minWidth:120,label:"巡检员",prop:"receiveUserName"},{minWidth:120,label:"到位状况",prop:"presentState"},{minWidth:120,label:"反馈状况",prop:"fallbackState"},{minWidth:100,align:"center",label:"任务状态",prop:"status",tag:!0,tagColor:a=>{var t;return(t=D[a.status])==null?void 0:t.color},formatter:a=>{var t;return((t=D[a.status])==null?void 0:t.text)||a.status}}],operationWidth:100,operations:[{perm:!0,text:"详情",svgIcon:y(tt),type:"info",click:a=>{_.curPage="detail",r.currentRow=a,F()}}],pagination:{refreshData:({page:a,size:t})=>{r.pagination.page=a||1,r.pagination.limit=t||20,h()}},handleSelectChange:a=>{r.selectList=a}}),E=()=>{_.curPage="table"},F=()=>{},h=()=>{var t,n,m;const a=((t=k.value)==null?void 0:t.queryParams)||{};st({page:r.pagination.page||1,size:r.pagination.limit||20,...a,fromTime:a.fromTime&&a.fromTime[0],toTime:a.fromTime&&a.fromTime[1],receiveUserId:(n=a.receiveUserId)==null?void 0:n.join(","),creator:(m=a.creator)==null?void 0:m.join(",")}).then(p=>{var u,b,w,P,C,x;if(((u=p.data)==null?void 0:u.code)===200){r.dataList=((w=(b=p.data)==null?void 0:b.data)==null?void 0:w.data)||[];const S=((C=(P=p.data)==null?void 0:P.data)==null?void 0:C.total)||0;r.pagination.total=S,l.totalTasks=S,it().then(s=>{var c,d;((c=s.data)==null?void 0:c.code)===200&&(l.completedTasks=((d=s.data)==null?void 0:d.data)||0)}).catch(s=>{console.error("获取已完成任务总数失败:",s),f.error("获取已完成任务总数失败")}),rt().then(s=>{var c,d;if(((c=s.data)==null?void 0:c.code)===200){const T=((d=s.data)==null?void 0:d.data)||"0%";l.presentRate=parseFloat(T.replace("%",""))}}).catch(s=>{console.error("获取到位率失败:",s),f.error("获取到位率失败")}),lt().then(s=>{var c,d;if(((c=s.data)==null?void 0:c.code)===200){const T=((d=s.data)==null?void 0:d.data)||"0%";l.feedbackRate=parseFloat(T.replace("%",""))}}).catch(s=>{console.error("获取反馈率失败:",s),f.error("获取反馈率失败")})}else f.error(((x=p.data)==null?void 0:x.message)||"获取任务列表失败")}).catch(p=>{console.error("获取任务列表失败:",p),f.error("系统错误")})},B=()=>{f.info("导出功能开发中...")},G=()=>{};return q(()=>{h()}),(a,t)=>{const n=K,m=O,p=Q,u=X,b=Y;return W(),R("div",nt,[t[8]||(t[8]=e("div",{class:"patrol-statistics-header"},[e("h2",null,"巡检任务统计")],-1)),e("div",pt,[_.curPage==="table"?(W(),R("div",ct,[e("div",dt,[o(p,{gutter:20},{default:i(()=>[o(m,{span:6},{default:i(()=>[o(n,{shadow:"hover",class:"statistics-card"},{default:i(()=>[e("div",mt,[e("div",ft,g(l.totalTasks),1),t[0]||(t[0]=e("div",{class:"statistics-card-label"},"总任务数",-1))])]),_:1})]),_:1}),o(m,{span:6},{default:i(()=>[o(n,{shadow:"hover",class:"statistics-card"},{default:i(()=>[e("div",ut,[e("div",_t,g(l.completedTasks),1),t[1]||(t[1]=e("div",{class:"statistics-card-label"},"已完成任务",-1))])]),_:1})]),_:1}),o(m,{span:6},{default:i(()=>[o(n,{shadow:"hover",class:"statistics-card"},{default:i(()=>[e("div",ht,[e("div",bt,g(l.presentRate.toFixed(2))+"%",1),t[2]||(t[2]=e("div",{class:"statistics-card-label"},"到位率",-1))])]),_:1})]),_:1}),o(m,{span:6},{default:i(()=>[o(n,{shadow:"hover",class:"statistics-card"},{default:i(()=>[e("div",vt,[e("div",gt,g(l.feedbackRate.toFixed(2))+"%",1),t[3]||(t[3]=e("div",{class:"statistics-card-label"},"反馈率",-1))])]),_:1})]),_:1})]),_:1})]),o(ot,{ref_key:"refSearch",ref:k,config:I,style:{margin:"16px 0"}},null,8,["config"]),e("div",kt,[o(n,{shadow:"hover",class:"table-card"},{header:i(()=>[e("div",Tt,[t[5]||(t[5]=e("span",null,"任务列表",-1)),o(b,{type:"primary",size:"small",onClick:B},{default:i(()=>[o(u,null,{default:i(()=>[o(N(V))]),_:1}),t[4]||(t[4]=j(" 导出数据 "))]),_:1})])]),default:i(()=>[o(z,{config:r},null,8,["config"])]),_:1})])])):M("",!0),A(e("div",yt,[e("div",wt,[o(u,{onClick:E},{default:i(()=>[o(N(H))]),_:1}),t[6]||(t[6]=e("div",{class:"detail-header-divider"},null,-1)),t[7]||(t[7]=e("span",null,"巡检任务详情",-1))]),e("div",Pt,[o(et,{row:r.currentRow,onRowClick:G},null,8,["row"])])],512),[[J,_.curPage==="detail"]])])])}}}),oe=at(Ct,[["__scopeId","data-v-17a2f02f"]]);export{oe as default};
