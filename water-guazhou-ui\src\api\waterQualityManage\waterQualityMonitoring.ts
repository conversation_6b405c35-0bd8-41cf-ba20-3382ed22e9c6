import request from '@/plugins/axios'

/**
 *  查询水质曲线
 * @param params
 * @returns
 */
export const getPointMonitor = (params?: {
  stationIds: string,
  time: string,
  queryType: string,
  attr?: string,
  stationType?: string
}) => request({
  method: 'get',
  url: `/istar/api/waterQualityStation/getPointMonitor`,
  params
})
/**
 *  查询水质监测站的实时数据列表
 * @param params
 * @returns
 */
export const getInfoDetail = (params?: {
  projectId: string
}) => request({
  method: 'get',
  url: `//istar/api/waterQualityStation/getInfoDetail`,
  params
})

/**
 *  水质报表-日/月/年报
 * @param params
 * @returns
 */
export const getWaterQualityReport = (params?: {
  stationIds: string,
  time: string,
  queryType: string,
  groupType?: string,
  stationType?: string
}) => request({
  method: 'get',
  url: `/istar/api/waterQualityStation/getReport`,
  params
})

/**
 *  水质日/月/年报统计最大最小值
 * @param params
 * @returns
 */
export const getMaxAndMinReport = (params?: {
  stationId: string,
  time: string,
  queryType: string,
  groupType?: string,
  stationType?: string
}) => request({
  method: 'get',
  url: `/istar/api/waterQualityStation/getMaxAndMinReport`,
  params
})

/**
 *  水质日/月/年报导出
 * @param params
 * @returns
 */
export const exportWaterQualityReport = (params?: {
  stationId: string,
  time: string,
  queryType: string,
  groupType?: string,
  stationType?: string
}) => request({
  method: 'get',
  url: `//istar/api/waterQualityStation/getReport/export`,
  params,
  responseType: 'blob'
})
