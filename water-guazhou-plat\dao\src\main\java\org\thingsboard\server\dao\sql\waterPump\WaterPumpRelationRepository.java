package org.thingsboard.server.dao.sql.waterPump;

import org.springframework.data.jpa.repository.JpaRepository;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.model.sql.WaterPumpRelationEntity;

import java.util.List;

public interface WaterPumpRelationRepository extends JpaRepository<WaterPumpRelationEntity, String> {

    List<WaterPumpRelationEntity> findByWaterPumpId(String waterPumpId);

    List<WaterPumpRelationEntity> findByDeviceId(String deviceId);

    List<WaterPumpRelationEntity> findByTenantId(String tenantId);

    List<WaterPumpRelationEntity> findByTypeAndTenantId(String type, String tenantId);
}
