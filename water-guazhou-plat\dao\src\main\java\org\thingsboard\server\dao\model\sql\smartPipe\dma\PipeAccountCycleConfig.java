package org.thingsboard.server.dao.model.sql.smartPipe.dma;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import java.util.Date;

/**
 * 核算周期配置
 */
@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@TableName(ModelConstants.PIPE_ACCOUNT_CYCLE_CONFIG_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class PipeAccountCycleConfig {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @TableField(ModelConstants.PIPE_ACCOUNT_CYCLE_CONFIG_PARTITION_ID)
    private String partitionId;

    @TableField(exist = false)
    private String partitionName;

    @TableField(ModelConstants.PIPE_ACCOUNT_CYCLE_CONFIG_START_TIME)
    private Date startTime;

    @TableField(ModelConstants.PIPE_ACCOUNT_CYCLE_CONFIG_END_TIME)
    private Date endTime;

    @TableField(ModelConstants.PIPE_ACCOUNT_CYCLE_CONFIG_MONTH)
    private String month;

    @TableField(ModelConstants.PIPE_ACCOUNT_CYCLE_CONFIG_IS_DEFAULT)
    private String isDefault;

    @TableField(ModelConstants.CREATOR)
    private String creator;

    @TableField(ModelConstants.CREATE_TIME)
    private Date createTime;

    @TableField(ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;

    @TableField(exist = false)
    private String autoGenerate;
}
