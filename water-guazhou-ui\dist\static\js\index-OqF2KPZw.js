import{_ as z}from"./TreeBox-DDD2iwoR.js";import{_ as K}from"./index-C9hz-UZb.js";import{d as J,r as _,c as C,l as y,bI as Y,bJ as M,b as U,j as W,o as Z,ay as $,g as T,h as E,F as f,q as m,i as o,cs as B,p as Q,n as X,an as F,dF as ee,dA as te,aq as ae,C as re}from"./index-r0dFAfgr.js";import{_ as oe}from"./CardSearch-CB_HNR-Q.js";import{_ as ie}from"./index-BJ-QPYom.js";import{u as ne}from"./useDetector-BRcb7GRN.js";import"./MapView-DaoQedLH.js";import"./Point-WxyopZva.js";import"./geometryEngineBase-BhsKaODW.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./project-DUuzYgGl.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import{u as le}from"./usePartition-DkcY9fQ2.js";import"./index-0NlGN6gS.js";import{E as pe}from"./index-Bo22WWST.js";import{E as se,g as me}from"./statistics-CeyexT_5.js";import"./Search-NSrhrIa_.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./FeatureHelper-Da16o0mu.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./LayerHelper-Cn-iiqxI.js";import"./pipe-nogVzCHG.js";import"./dma-SMxrzG7b.js";import"./hookupDevice-Bcbk7s68.js";const de=J({__name:"index",setup(ce){const d=_({chartOption:null,activeName:"list"}),k=C(),b=C(),H=C(),D=_([]),h=_({data:[],title:"选择设备",showCheckbox:!0,handleCheck(e,t){h.checkedKeys=t.checkedKeys||[],h.checkedNodes=t.checkedNodes||[],G()}}),A=_({defaultParams:{type:"daterange",queryType:"15m",daterange:[y().subtract(1,"d").format("YYYY-MM-DD"),y().format("YYYY-MM-DD")],month:y().format(Y),year:y().format(M)},filters:[{type:"radio-button",field:"type",options:[{label:"按年",value:"year"},{label:"按月",value:"month"},{label:"按时间段",value:"daterange"}],label:"选择方式",onChange:e=>{var t;(t=b.value)!=null&&t.queryParams&&(b.value.queryParams.queryType=e==="year"?"1nc":e==="month"?"1d":"15min")}},{hidden:!0,handleHidden:(e,t,a)=>a.hidden=e.type!=="year",type:"year",label:"选择年份",field:"year",clearable:!1,format:M,disabledDate(e){return new Date<e}},{handleHidden(e,t,a){a.hidden=e.type!=="year"},type:"select",label:"时间间隔:",field:"queryType",clearable:!1,options:[{label:"1月",value:"1nc"}],itemContainerStyle:{width:"180px"}},{hidden:!0,handleHidden:(e,t,a)=>a.hidden=e.type!=="month",type:"month",label:"选择月份",field:"month",clearable:!1,format:Y,disabledDate(e){return new Date<e}},{handleHidden(e,t,a){a.hidden=e.type!=="month"},type:"select",label:"时间间隔:",field:"queryType",clearable:!1,options:[{label:"1天",value:"1d"}],itemContainerStyle:{width:"180px"}},{hidden:!0,handleHidden:(e,t,a)=>a.hidden=e.type!=="daterange",type:"daterange",label:"选择日期",field:"daterange",clearable:!1,disabledDate(e){return new Date<e}},{handleHidden(e,t,a){a.hidden=e.type!=="daterange"},type:"select",label:"时间间隔:",field:"queryType",clearable:!1,options:[{label:"1分钟",value:"1m"},{label:"5分钟",value:"5m"},{label:"15分钟",value:"15m"},{label:"30分钟",value:"30m"},{label:"1小时",value:"1h"}],itemContainerStyle:{width:"180px"}},{type:"btn-group",btns:[{perm:!0,text:"查询",click:()=>{w()},iconifyIcon:"ep:search"},{type:"default",perm:!0,text:"重置",iconifyIcon:"ep:refresh",click:()=>{var e;(e=b.value)==null||e.resetForm()}},{perm:!0,text:"导出",type:"warning",hide:()=>d.activeName!=="list",click:()=>{w(!0)},iconifyIcon:"ep:download"}]}]}),i=_({loading:!1,dataList:[],columns:[],operations:[],pagination:{page:1,limit:200,pageSize:[100,200,300,500],total:0,refreshData:({page:e,size:t})=>{i.pagination.page=e,i.pagination.limit=t,i.dataList=D==null?void 0:D.slice((e-1)*t,e*t)}}}),S=()=>{var t;return((t=h.checkedNodes)==null?void 0:t.filter(a=>a.data.type==="3"))||[]},N=()=>S().map(a=>{var p;return{minWidth:120,label:a.label,prop:(p=a.data)==null?void 0:p.deviceId,unit:"(m³/h)"}})||[],G=()=>{const e=N();i.columns=[{minWidth:160,label:"时间",prop:"time"},...e,{label:"最小站点",minWidth:120,prop:"minPoint"},{label:"最小值(m³/h)",minWidth:120,prop:"minValue"},{label:"最大站点",minWidth:120,prop:"maxPoint"},{label:"最大值(m³/h)",minWidth:120,prop:"maxValue"},{label:"平均值(m³/h)",minWidth:120,prop:"avgValue"},{label:"合计(m³/h)",minWidth:120,prop:"sumValue"}]},w=async e=>{var p,n,c,r,u,l,g,v;if(!((p=h.checkedNodes)!=null&&p.length)){U.warning("请先选择设备");return}const t=((n=b.value)==null?void 0:n.queryParams)||{},a={page:i.pagination.page,size:i.pagination.limit,deviceIdList:S().map(x=>{var s;return(s=x.data)==null?void 0:s.deviceId}),type:t.type==="daterange"?"time":t.type,interval:t.queryType,start:((c=t.daterange)==null?void 0:c[0])&&y((r=t.daterange)==null?void 0:r[0]).startOf("D").valueOf(),end:((u=t.daterange)==null?void 0:u[1])&&y((l=t.daterange)==null?void 0:l[1]).endOf("D").valueOf(),month:t.month,year:t.year,queryType:"flow"};if(e){const x=await se(a);pe(x.data,"流量分析报表")}else{const s=(g=(await me(a)).data)==null?void 0:g.data;i.dataList=((v=s==null?void 0:s.data)==null?void 0:v.map(P=>{var O;const V={...P};return(O=P.data)==null||O.map(I=>{V[I.id]=I.value}),V}))||[],i.pagination.total=(s==null?void 0:s.total)||0,R(N())}},R=e=>{var c;if(d.activeName!=="echarts")return;const t={grid:{left:50,right:50,top:50,bottom:80},legend:{type:"scroll",textStyle:{color:"#666",fontSize:12}},tooltip:{trigger:"axis"},xAxis:{type:"category",boundaryGap:!1,data:["--"],splitLine:{show:!1}},dataZoom:[{type:"inside",start:0,end:100},{start:0,end:100}],yAxis:[{type:"value",name:"瞬时流量(m³/h)",axisLine:{show:!0,lineStyle:{types:"solid"}},axisLabel:{show:!0,textStyle:{color:"#656b84"}},splitLine:{lineStyle:{color:W().isDark?"#303958":"#ccc",type:[5,10],dashOffset:5}}}],series:[]},a=i.dataList.filter(r=>!!r.time),p=a.map(r=>r.time);t.xAxis.data=p;const n={};a.map(r=>{var u;(u=r.data)==null||u.map(l=>{n[l.id]?n[l.id].push(l.value):n[l.id]=[l.value]})}),t.series=e.map(r=>({name:r.label,smooth:!0,data:n[r.prop]||[],type:"line",markPoint:{data:[{type:"max",name:"最大值"},{type:"min",name:"最小值"}]},markLine:{data:[{type:"average",name:"平均值"}]}})),(c=k.value)==null||c.clear(),d.chartOption=t},j=ne(),L=le(),q=C();return Z(async()=>{await L.getDeviceTree({type:"flow"}),h.data=L.DeviceTree.value,j.listenToMush(q.value,()=>{var e;(e=k.value)==null||e.resize()})}),(e,t)=>{const a=ie,p=oe,n=ee,c=te,r=$("VChart"),u=ae,l=K,g=z;return T(),E(g,null,{tree:f(()=>[m(a,{ref:"refTree","tree-data":o(h)},null,8,["tree-data"])]),default:f(()=>[m(p,{ref_key:"refSearch",ref:b,config:o(A)},null,8,["config"]),m(l,{class:"card-table",title:" "},{right:f(()=>[m(c,{modelValue:o(d).activeName,"onUpdate:modelValue":t[0]||(t[0]=v=>o(d).activeName=v),onChange:t[1]||(t[1]=()=>w())},{default:f(()=>[m(n,{label:"echarts"},{default:f(()=>[m(o(B),{icon:"clarity:line-chart-line"})]),_:1}),m(n,{label:"list"},{default:f(()=>[m(o(B),{icon:"material-symbols:table"})]),_:1})]),_:1},8,["modelValue"])]),default:f(()=>[Q("div",{ref_key:"refDiv",ref:q,class:"content"},[o(d).activeName==="echarts"?(T(),X("div",{key:0,ref_key:"refDiv",ref:q,class:"chart-box"},[m(r,{ref_key:"refChart",ref:k,theme:o(W)().isDark?"dark":"light",option:o(d).chartOption},null,8,["theme","option"])],512)):F("",!0),o(d).activeName==="list"?(T(),E(u,{key:1,ref_key:"refCardTable",ref:H,class:"card-table",config:o(i)},null,8,["config"])):F("",!0)],512)]),_:1})]),_:1})}}}),Wt=re(de,[["__scopeId","data-v-4ba6c7cc"]]);export{Wt as default};
