import{d as T,c as i,r as K,am as E,bB as R,x as y,g,h as v,F as o,p as G,q as t,G as h,bh as I,an as H,n as J,dr as M,H as O,I as j,aK as z,aL as A,K as P,J as Q,L as W,C as X}from"./index-r0dFAfgr.js";/* empty css                         */import{a as Y,u as Z,b as $}from"./circuitSettings-CHqJCF5w.js";const ee={key:0,class:"loading-skeleton"},ae={class:"dialog-footer"},le=T({__name:"CircuitSettingsDialog",props:{modelValue:{type:Boolean,default:!1},editId:{default:""},readonly:{type:Boolean,default:!1}},emits:["update:modelValue","success"],setup(B,{emit:S}){const d=B,b=S,r=i(!1),c=i(!1),m=i(!1),p=i(),n=i(!1),a=K({name:"",code:"",type:"",status:"0"}),D={name:[{required:!0,message:"请输入配置名称",trigger:"blur"}],code:[{required:!0,message:"请输入配置编码",trigger:"blur"}],type:[{required:!0,message:"请选择配置类型",trigger:"change"}],status:[{required:!0,message:"请选择状态",trigger:"change"}]};E(()=>d.modelValue,async e=>{r.value=e,e&&(n.value=!!d.editId,n.value?await U():V())}),E(r,e=>{b("update:modelValue",e)});const q=()=>d.readonly?"查看培训配置":n.value?"编辑培训配置":"新建培训配置",V=()=>{a.name="",a.code="",a.type="",a.status="0",R(()=>{var e;(e=p.value)==null||e.clearValidate()})},U=async()=>{if(d.editId)try{c.value=!0;const e=await Y(d.editId);if(e!=null&&e.data){const l=e.data.data||e.data;a.name=l.name||"",a.code=l.code||"",a.type=l.type||"",a.status=l.status||"0"}}catch(e){console.error(e),y.error("获取数据失败")}finally{c.value=!1}},F=async()=>{if(p.value)try{await p.value.validate(),m.value=!0,n.value?(await Z(d.editId,a),y.success("更新成功")):(await $(a),y.success("创建成功")),b("success"),_()}catch(e){console.error(e),y.error(n.value?"更新失败":"创建失败")}finally{m.value=!1}},_=()=>{r.value=!1,c.value=!1,m.value=!1,V()};return(e,l)=>{const L=M,k=O,f=j,u=z,w=A,N=P,C=Q,x=W;return g(),v(x,{modelValue:r.value,"onUpdate:modelValue":l[4]||(l[4]=s=>r.value=s),title:q(),width:"600px","before-close":_},{footer:o(()=>[G("span",ae,[t(C,{onClick:_},{default:o(()=>[h(I(e.readonly?"关闭":"取消"),1)]),_:1}),e.readonly?H("",!0):(g(),v(C,{key:0,type:"primary",onClick:F,loading:m.value},{default:o(()=>[h(I(n.value?"更新":"创建"),1)]),_:1},8,["loading"]))])]),default:o(()=>[c.value?(g(),J("div",ee,[t(L,{rows:4,animated:""})])):(g(),v(N,{key:1,ref_key:"formRef",ref:p,model:a,rules:D,"label-width":"100px"},{default:o(()=>[t(f,{label:"配置名称",prop:"name"},{default:o(()=>[t(k,{modelValue:a.name,"onUpdate:modelValue":l[0]||(l[0]=s=>a.name=s),placeholder:"请输入配置名称",readonly:e.readonly},null,8,["modelValue","readonly"])]),_:1}),t(f,{label:"配置编码",prop:"code"},{default:o(()=>[t(k,{modelValue:a.code,"onUpdate:modelValue":l[1]||(l[1]=s=>a.code=s),placeholder:"请输入配置编码",readonly:e.readonly},null,8,["modelValue","readonly"])]),_:1}),t(f,{label:"配置类型",prop:"type"},{default:o(()=>[t(w,{modelValue:a.type,"onUpdate:modelValue":l[2]||(l[2]=s=>a.type=s),placeholder:"请选择配置类型",style:{width:"100%"},disabled:e.readonly},{default:o(()=>[t(u,{label:"管网",value:"0"}),t(u,{label:"泵站",value:"1"}),t(u,{label:"其他",value:"2"})]),_:1},8,["modelValue","disabled"])]),_:1}),t(f,{label:"状态",prop:"status"},{default:o(()=>[t(w,{modelValue:a.status,"onUpdate:modelValue":l[3]||(l[3]=s=>a.status=s),placeholder:"请选择状态",style:{width:"100%"},disabled:e.readonly},{default:o(()=>[t(u,{label:"启用",value:"0"}),t(u,{label:"停用",value:"1"})]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1},8,["model"]))]),_:1},8,["modelValue","title"])}}}),de=X(le,[["__scopeId","data-v-64574148"]]);export{de as default};
