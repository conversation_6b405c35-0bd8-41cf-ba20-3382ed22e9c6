import{aG as lt,e as r,y as l,o as A,a6 as Fe,m as D,ai as Ct,w as q,a as F,W as L,b as fe,aK as pe,aL as Ee,aM as ze,h as _t,i as mt,s as V,a4 as H,aN as jt,az as Vt,Z as Mt}from"./Point-WxyopZva.js";import{h6 as At,bH as wt,b$ as ae,dx as Re,d9 as we,aq as Le,w as je,d6 as Ge,c2 as ve,ar as ut,aD as pt,u as $t,cU as he,ec as Jt,cS as Et,c7 as zt,db as Be,dw as qt,v as Lt,g as Gt,c as Ut,dh as Ht,t as Bt,Q as kt,R as Qt,q as Wt,de as Kt,eg as Zt,V as Xt,dk as Yt,fd as ei,cR as ti,dq as ii,bI as ri,e as si}from"./MapView-DaoQedLH.js";import{a5 as $,T as G,R as b,aO as _e}from"./index-r0dFAfgr.js";import{w as S,o as ue,i as Ne,c as ni,l as ai,d as oi,a as li,m as ui,k as pi,L as ci,j as mi,$ as di,f as hi,n as yi}from"./fetchRasterInfo-B6Y1j1SM.js";import{U as O,L as gi}from"./pe-B8dP0-Ut.js";import{l as fi}from"./widget-BcWKanF2.js";import{p as vt,c as ke,v as Ri,h as wi}from"./multidimensionalUtils-BqWBjmd-.js";import{s as vi}from"./pixelRangeUtils-Dr0gmLDH.js";import{f as bt,_ as St,n as bi,C as Si,y as xi,b as Ii,u as xt,T as Fi,S as dt,j as Di}from"./RasterSymbolizer-BF_flzvK.js";import{e as ht}from"./imageBitmapUtils-Db1drMDc.js";import{m as Qe,f as Ti,j as Ni}from"./dataUtils-DovfQoP5.js";import{s as Pi,n as Oi}from"./executeForIds-BLdIsxvI.js";import"./generateRendererUtils-Bt0vqUD2.js";const Ci={StretchFunction:{arguments:{ComputeGamma:{isDataset:!1,isPublic:!1,name:"ComputeGamma",type:"RasterFunctionVariable",value:!1},DRA:{isDataset:!1,isPublic:!1,name:"DRA",type:"RasterFunctionVariable",value:!1},EstimateStatsHistogram:{isDataset:!1,isPublic:!1,name:"EstimateStatsHistogram",type:"RasterFunctionVariable",value:!1},Gamma:{displayName:"Gamma",isDataset:!1,isPublic:!1,name:"Gamma",type:"RasterFunctionVariable"},Histograms:{isDataset:!1,isPublic:!1,name:"Histograms",type:"RasterFunctionVariable"},Max:{isDataset:!1,isPublic:!1,name:"Max",type:"RasterFunctionVariable",value:255},MaxPercent:{isDataset:!1,isPublic:!1,name:"MaxPercent",type:"RasterFunctionVariable",value:.5},Min:{isDataset:!1,isPublic:!1,name:"Min",type:"RasterFunctionVariable",value:0},MinPercent:{isDataset:!1,isPublic:!1,name:"MinPercent",type:"RasterFunctionVariable",value:.25},NumberOfStandardDeviations:{isDataset:!1,isPublic:!1,name:"NumberOfStandardDeviation",type:"RasterFunctionVariable",value:2},Raster:{isDataset:!0,isPublic:!1,name:"Raster",type:"RasterFunctionVariable"},SigmoidStrengthLevel:{isDataset:!1,isPublic:!1,name:"SigmoidStrengthLevel",type:"RasterFunctionVariable",value:2},Statistics:{isDataset:!1,isPublic:!1,name:"Statistics",type:"RasterFunctionVariable"},StretchType:{isDataset:!1,isPublic:!1,name:"StretchType",type:"RasterFunctionVariable",value:0},type:"StretchFunctionArguments",UseGamma:{isDataset:!1,isPublic:!1,name:"UseGamma",type:"RasterFunctionVariable",value:!1}},description:"Enhances an image by adjusting the range of values displayed. This does not alter the underlying pixel values. If a pixel has a value outside of the specified range, it will appear as either the minimum or maximum value.",function:{description:"Enhances an image by adjusting the range of values displayed. This does not alter the underlying pixel values. If a pixel has a value outside of the specified range, it will appear as either the minimum or maximum value.",name:"Stretch",pixelType:"UNKNOWN",type:"StretchFunction"},functionType:0,name:"Stretch",thumbnail:""},RemapFunction:{name:"Remap",description:"Changes pixel values by assigning new values to ranges of pixel values or using an external table.",function:{type:"RemapFunction",pixelType:"UNKNOWN",name:"Remap",description:"Changes pixel values by assigning new values to ranges of pixel values or using an external table."},arguments:{Raster:{name:"Raster",isPublic:!1,isDataset:!0,type:"RasterFunctionVariable"},UseTable:{name:"UseTable",isPublic:!1,isDataset:!1,value:!1,type:"RasterFunctionVariable"},InputRanges:{name:"InputRanges",isPublic:!1,isDataset:!1,type:"RasterFunctionVariable",displayName:"Input Ranges"},OutputValues:{name:"OutputValues",isPublic:!1,isDataset:!1,type:"RasterFunctionVariable",displayName:"Output Values"},NoDataRanges:{name:"NoDataRanges",isPublic:!1,isDataset:!1,type:"RasterFunctionVariable",displayName:"NoData Ranges"},Table:{name:"Table",isPublic:!1,isDataset:!1,type:"RasterFunctionVariable"},InputField:{name:"InputField",isPublic:!1,isDataset:!1,type:"RasterFunctionVariable"},OutputField:{name:"OutputField",isPublic:!1,isDataset:!1,type:"RasterFunctionVariable"},InputMaxField:{name:"InputMaxField",isPublic:!1,isDataset:!1,type:"RasterFunctionVariable"},RemapTableType:{name:"RemapTableType",isPublic:!1,isDataset:!1,value:1,type:"RasterFunctionVariable"},AllowUnmatched:{name:"AllowUnmatched",isPublic:!1,isDataset:!1,value:!0,type:"RasterFunctionVariable"},type:"RemapFunctionArguments"},functionType:0,thumbnail:""},ColormapFunction:{name:"Colormap",description:"Changes pixel values to display the raster data as either a grayscale or a red, green, blue (RGB) image, based on a colormap or a color ramp.",function:{type:"ColormapFunction",pixelType:"UNKNOWN",name:"Colormap",description:"Changes pixel values to display the raster data as either a grayscale or a red, green, blue (RGB) image, based on a colormap or a color ramp."},arguments:{Raster:{name:"Raster",isPublic:!1,isDataset:!0,type:"RasterFunctionVariable"},ColorSchemeType:{name:"ColorSchemeType",isPublic:!1,isDataset:!1,value:1,type:"RasterFunctionVariable"},Colormap:{name:"Colormap",isPublic:!1,isDataset:!1,type:"RasterFunctionVariable"},ColormapName:{name:"ColormapName",isPublic:!1,isDataset:!1,value:"Gray",type:"RasterFunctionVariable"},ColorRamp:{name:"ColorRamp",isPublic:!1,isDataset:!1,type:"RasterFunctionVariable"},type:"ColormapFunctionArguments"},functionType:0,thumbnail:""},ShadedReliefFunction:{name:"Shaded Relief",description:"Creates a multiband, color coded, 3D representation of the surface, with the sun's relative position taken into account for shading the image.",function:{type:"ShadedReliefFunction",pixelType:"UNKNOWN",name:"Shaded Relief",description:"Creates a multiband, color coded, 3D representation of the surface, with the sun's relative position taken into account for shading the image."},arguments:{Raster:{name:"Raster",isPublic:!1,isDataset:!0,type:"RasterFunctionVariable"},ColorSchemeType:{name:"ColorSchemeType",isPublic:!1,isDataset:!1,value:1,type:"RasterFunctionVariable"},ColorRamp:{name:"ColorRamp",isPublic:!1,isDataset:!1,type:"RasterFunctionVariable"},HillshadeType:{name:"HillshadeType",isPublic:!1,isDataset:!1,value:0,type:"RasterFunctionVariable"},Colormap:{name:"Colormap",isPublic:!1,isDataset:!1,type:"RasterFunctionVariable"},Azimuth:{name:"Azimuth",isPublic:!1,isDataset:!1,value:315,type:"RasterFunctionVariable"},Altitude:{name:"Altitude",isPublic:!1,isDataset:!1,value:45,type:"RasterFunctionVariable"},SlopeType:{name:"SlopeType",isPublic:!1,isDataset:!1,value:1,type:"RasterFunctionVariable"},ZFactor:{name:"ZFactor",isPublic:!1,isDataset:!1,value:1,type:"RasterFunctionVariable"},PSPower:{name:"PSPower",isPublic:!1,isDataset:!1,value:.664,type:"RasterFunctionVariable"},PSZFactor:{name:"PSZFactor",isPublic:!1,isDataset:!1,value:.024,type:"RasterFunctionVariable"},RemoveEdgeEffect:{name:"RemoveEdgeEffect",isPublic:!1,isDataset:!1,value:!1,type:"RasterFunctionVariable"},type:"ShadedReliefFunctionArguments"},functionType:0,thumbnail:""},HillshadeFunction:{name:"Hillshade",description:"Creates a 3D representation of the surface, with the sun's relative position taken into account for shading the image",function:{type:"HillshadeFunction",pixelType:"UNKNOWN",name:"Hillshade",description:"Creates a 3D representation of the surface, with the sun's relative position taken into account for shading the image"},arguments:{DEM:{name:"DEM",isPublic:!1,isDataset:!0,type:"RasterFunctionVariable"},HillshadeType:{name:"HillshadeType",isPublic:!1,isDataset:!1,value:0,type:"RasterFunctionVariable"},Azimuth:{name:"Azimuth",isPublic:!1,isDataset:!1,value:315,type:"RasterFunctionVariable"},Altitude:{name:"Altitude",isPublic:!1,isDataset:!1,value:45,type:"RasterFunctionVariable"},SlopeType:{name:"SlopeType",isPublic:!1,isDataset:!1,value:1,type:"RasterFunctionVariable"},ZFactor:{name:"ZFactor",isPublic:!1,isDataset:!1,value:1,type:"RasterFunctionVariable"},PSPower:{name:"PSPower",isPublic:!1,isDataset:!1,value:.664,type:"RasterFunctionVariable"},PSZFactor:{name:"PSZFactor",isPublic:!1,isDataset:!1,value:.024,type:"RasterFunctionVariable"},RemoveEdgeEffect:{name:"RemoveEdgeEffect",isPublic:!1,isDataset:!1,value:!1,type:"RasterFunctionVariable"},type:"HillshadeFunctionArguments"},functionType:0,thumbnail:""},ResampleFunction:{name:"Resample",description:"Changes the cell size of a raster.",function:{type:"ResampleFunction",pixelType:"UNKNOWN",name:"Resample",description:"Changes the cell size of a raster."},arguments:{Raster:{name:"Raster",isPublic:!1,isDataset:!0,type:"RasterFunctionVariable"},ResamplingType:{name:"ResamplingType",isPublic:!1,isDataset:!1,value:0,type:"RasterFunctionVariable"},InputCellSize:{name:"InputCellsize",isPublic:!1,isDataset:!1,value:{x:0,y:0},type:"RasterFunctionVariable"},OutputCellSize:{name:"OutputCellsize",isPublic:!1,isDataset:!1,value:{x:0,y:0},type:"RasterFunctionVariable"},type:"ResampleFunctionArguments"},functionType:0,thumbnail:""}},_i=new Set(["u1","u2","u4","u8","s8","u16","s16"]),ji={simple_scalar:"Simple Scalar",wind_barb:"Wind Barb",single_arrow:"Single Arrow",beaufort_kn:"Beaufort Wind (Knots)",beaufort_m:"Beaufort Wind (MetersPerSecond)",ocean_current_m:"Ocean Current (MetersPerSecond)",ocean_current_kn:"Ocean Current (Knots)"},Vi=new Set(["raster-stretch","unique-value","class-breaks","raster-shaded-relief","vector-field","raster-colormap"]);function It(s){return Vi.has(s.type)}function We(s,i){var e;if(!s||!i)return $(s||i);const o=$(s);if(o.rasterFunctionDefinition&&i.rasterFunctionDefinition){const t=i.rasterFunctionDefinition;(t.thumbnail||t.thumbnailEx)&&(t.thumbnail=t.thumbnailEx=void 0),Ft(o.rasterFunctionDefinition.arguments,i)}else((e=i.functionName)==null?void 0:e.toLowerCase())!=="none"&&(Dt(o.functionArguments).Raster=i);return o}function Ft(s,i){for(const o in s)o.toLowerCase()==="raster"&&(s[o].type==="RasterFunctionVariable"?(s[o]=i.rasterFunctionDefinition,s[o].type="RasterFunctionTemplate"):s[o].type==="RasterFunctionTemplate"&&Ft(s[o].arguments,i))}function te(s){const i=$(Ci[s.functionName+"Function"]),o=s.functionArguments;for(const e in o)e.toLowerCase()==="raster"?(i.arguments[e]=te(o[e]),i.arguments[e].type="RasterFunctionTemplate"):e.toLowerCase()==="colormap"?(i.arguments[e].value=Gi(o[e]),i.arguments.ColorSchemeType.value=0):i.arguments[e].value=o[e];return i}function Mi(s,i){switch(i=i||{},s.type){case"raster-stretch":return Ji(s,i);case"class-breaks":return Ei(s,i);case"unique-value":return zi(s,i);case"raster-colormap":return qi(s,i);case"vector-field":return Ai(s,i);case"raster-shaded-relief":return $i(s,i);case"flow":throw new Error("Unsupported rendering rule.")}}function Dt(s){const i=s==null?void 0:s.Raster;return i&&i.declaredClass==="esri.layers.support.RasterFunction"?Dt(i.functionArguments):s}const De={none:0,standardDeviation:3,histogramEqualization:4,minMax:5,percentClip:6,sigmoid:9};function Ai(s,i){const o=new S;o.functionName="VectorFieldRenderer";const{dataType:e,bandProperties:t}=i,n=e==="vector-uv";let a,u;t&&t.length===2&&(a=t.map(y=>y.BandName.toLowerCase()).indexOf("magnitude"),u=t.map(y=>y.BandName.toLowerCase()).indexOf("direction")),a!==-1&&a!==null||(a=0,u=1);const p=s.rotationType==="arithmetic"?1:2,m=s.flowRepresentation==="flow-from"?0:1,c=s.visualVariables?s.visualVariables.find(y=>y.field==="Magnitude"):new At,d={magnitudeBandID:a,directionBandID:u,isUVComponents:n,referenceSystem:p,massFlowAngleRepresentation:m,symbolTileSize:50,symbolTileSizeUnits:100,calculationMethod:"Vector Average",symbologyName:ji[s.style.toLowerCase().replace("-","_")],minimumMagnitude:c.minDataValue,maximumMagnitude:c.maxDataValue,minimumSymbolSize:c.minSize,maximumSymbolSize:c.maxSize};return o.functionArguments=d,i.convertToRFT?new S({rasterFunctionDefinition:te(o)}):o}function $i(s,i){const o=i.convertToRFT;if(i.dataType!=="elevation"&&(i.dataType!=="generic"||i.bandCount!==1||i.pixelType!=="s16"&&i.pixelType!=="s32"&&i.pixelType!=="f32"&&i.pixelType!=="f64"))return new S;const e=new S;e.functionName="Hillshade";const t=s.hillshadeType==="traditional"?0:1,n=s.scalingType==="none"?1:3,a={HillshadeType:t,SlopeType:n,ZFactor:s.zFactor};return t===0&&(a.Azimuth=s.azimuth,a.Altitude=s.altitude),n===3&&(a.PSPower=s.pixelSizePower,a.PSZFactor=s.pixelSizeFactor),e.functionArguments=a,e.variableName="Raster",s.colorRamp&&(e.functionName="ShadedRelief",o?a.ColorRamp=bt(s.colorRamp):a.Colormap=St(s.colorRamp)),o?new S({rasterFunctionDefinition:te(e)}):e}function Ji(s,i){var u;const o=i.convertToRFT,e=new S;e.functionName="Stretch";const t=De[bi.toJSON(s.stretchType)],n="u8",a={StretchType:t,Statistics:Li(s.statistics??[]),DRA:s.dynamicRangeAdjustment,UseGamma:s.useGamma,Gamma:s.gamma,ComputeGamma:s.computeGamma};if(s.outputMin!=null&&(a.Min=s.outputMin),s.outputMax!=null&&(a.Max=s.outputMax),t===De.standardDeviation?(a.NumberOfStandardDeviations=s.numberOfStandardDeviations,e.outputPixelType=n):t===De.percentClip?(a.MinPercent=s.minPercent,a.MaxPercent=s.maxPercent,e.outputPixelType=n):t===De.minMax?e.outputPixelType=n:t===De.sigmoid&&(a.SigmoidStrengthLevel=s.sigmoidStrengthLevel),e.functionArguments=a,e.variableName="Raster",s.colorRamp){const p=s.colorRamp,m=new S;if(o)m.functionArguments={ColorRamp:bt(p)};else{const c=Si(p);if(c)m.functionArguments={colorRamp:c};else if(!i.convertColorRampToColormap||p.type!=="algorithmic"&&p.type!=="multipart"){const d=s.colorRamp.toJSON();d.type==="algorithmic"?d.algorithm=d.algorithm||"esriCIELabAlgorithm":d.type==="multipart"&&((u=d.colorRamps)!=null&&u.length)&&d.colorRamps.forEach(y=>y.algorithm=y.algorithm||"esriCIELabAlgorithm"),m.functionArguments={colorRamp:d}}else m.functionArguments={Colormap:St(p)}}return m.variableName="Raster",m.functionName="Colormap",m.functionArguments.Raster=e,o?new S({rasterFunctionDefinition:te(m)}):m}return o?new S({rasterFunctionDefinition:te(e)}):e}function Ei(s,i){const o=[],e=[],t=[],n=[],{pixelType:u,rasterAttributeTable:p}=i,m=G(p)?null:p.features,c=Tt(p);if(c&&m&&Array.isArray(m)&&s.classBreakInfos){s.classBreakInfos.forEach((h,g)=>{var _;const v=(_=h.symbol)==null?void 0:_.color;let x;v!=null&&v.a&&h.minValue!=null&&h.maxValue!=null&&m.forEach(J=>{h.minValue!=null&&h.maxValue!=null&&(x=J.attributes[s.field],(x>=h.minValue&&x<h.maxValue||g===s.classBreakInfos.length-1&&x>=h.minValue)&&n.push([J.attributes[c],v.r,v.g,v.b]))})});const f=u?qe(n,u):n,R=new S;return R.functionName="Colormap",R.functionArguments={},R.functionArguments.Colormap=f,R.variableName="Raster",i.convertToRFT?new S({rasterFunctionDefinition:te(R)}):R}s.classBreakInfos.forEach((f,R)=>{if(f.minValue==null||f.maxValue==null)return;const h=f.symbol&&f.symbol.color;h!=null&&h.a?(R===0?o.push(f.minValue,f.maxValue+1e-6):o.push(f.minValue+1e-6,f.maxValue+1e-6),e.push(R),n.push([R,h.r,h.g,h.b])):t.push(f.minValue,f.maxValue)});const d=u?qe(n,u):n,y=new S;y.functionName="Remap",y.functionArguments={InputRanges:o,OutputValues:e,NoDataRanges:t},y.variableName="Raster";const w=new S;return w.functionName="Colormap",w.functionArguments={Colormap:d,Raster:y},i.convertToRFT?new S({rasterFunctionDefinition:te(w)}):w}function qe(s,i){const o=_i.has(i)?vi(i):null;return o&&s.push([Math.floor(o[0]-1),0,0,0],[Math.ceil(o[1]+1),0,0,0]),s}function Tt(s){if(G(s))return;const{fields:i}=s,o=i&&i.find(e=>e&&e.name&&e.name.toLowerCase()==="value");return o&&o.name}function zi(s,i){var d,y,w;const o=[],{pixelType:e,rasterAttributeTable:t}=i,n=G(t)?null:t.features,a=Tt(t),u=(y=(d=s.defaultSymbol)==null?void 0:d.color)==null?void 0:y.toRgb(),p=s.uniqueValueInfos;if(p)if(n){if(a){const f=new Map;p.forEach(h=>{var x;const g=h.value,v=(x=h.symbol)==null?void 0:x.color;g!=null&&v&&v.a&&f.set(String(g),v.toRgb())});const R=s.field;n.forEach(({attributes:h})=>{const g=String(h[R]),v=h[a],x=f.get(g);x?o.push([v,...x]):u&&o.push([v,...u])})}}else for(let f=0;f<p.length;f++){const R=p[f],h=(w=R.symbol)==null?void 0:w.color,g=+R.value;if(h!=null&&h.a){if(isNaN(g))return null;o.push([g,h.r,h.g,h.b])}}const m=e&&o.length>0?qe(o,e):o,c=new S;return c.functionName="Colormap",c.functionArguments={},c.functionArguments.Colormap=m,c.variableName="Raster",i.convertToRFT?new S({rasterFunctionDefinition:te(c)}):c}function qi(s,i){const o=s.extractColormap();if(!o||o.length===0)return null;const{pixelType:e}=i,t=e?qe(o,e):o,n=new S;return n.functionName="Colormap",n.functionArguments={},n.functionArguments.Colormap=t,i.convertToRFT?new S({rasterFunctionDefinition:te(n)}):n}function Li(s){const i=[];return s==null||s.forEach(o=>{const e=o;if(Array.isArray(e))i.push(e);else{if(e.min==null||e.max==null)return;const t=[e.min,e.max,e.avg||0,e.stddev||0];i.push(t)}}),i}function Gi(s){const i=[],o=[];return s.forEach(e=>{i.push(e[0]),o.push(xi([...e.slice(1),255]))}),{type:"RasterColormap",values:i,colors:o}}var Ze;const Pe=lt()({MT_FIRST:"first",MT_LAST:"last",MT_MIN:"min",MT_MAX:"max",MT_MEAN:"mean",MT_BLEND:"blend",MT_SUM:"sum"}),Xe=lt()({esriMosaicNone:"none",esriMosaicCenter:"center",esriMosaicNadir:"nadir",esriMosaicViewpoint:"viewpoint",esriMosaicAttribute:"attribute",esriMosaicLockRaster:"lock-raster",esriMosaicNorthwest:"northwest",esriMosaicSeamline:"seamline"});function Ui(s){let i;switch(s?s.toLowerCase().replace("esrimosaic",""):""){case"byattribute":case"attribute":i="esriMosaicAttribute";break;case"lockraster":i="esriMosaicLockRaster";break;case"center":i="esriMosaicCenter";break;case"northwest":i="esriMosaicNorthwest";break;case"nadir":i="esriMosaicNadir";break;case"viewpoint":i="esriMosaicViewpoint";break;case"seamline":i="esriMosaicSeamline";break;default:i="esriMosaicNone"}return Xe.fromJSON(i)}let M=Ze=class extends L{constructor(s){super(s),this.ascending=!0,this.itemRenderingRule=null,this.lockRasterIds=null,this.method=null,this.multidimensionalDefinition=null,this.objectIds=null,this.operation=null,this.sortField=null,this.sortValue=null,this.viewpoint=null,this.where=null}readAscending(s,i){return i.ascending!=null?i.ascending:i.sortAscending==null||i.sortAscending}readMethod(s,i){return Ui(i.mosaicMethod||i.defaultMosaicMethod)}writeMultidimensionalDefinition(s,i,o){s!=null&&(s=s.filter(({variableName:e,dimensionName:t})=>e&&e!=="*"||t)).length&&(i[o]=s.map(e=>e.toJSON()))}readOperation(s,i){const o=i.mosaicOperation,e=i.mosaicOperator&&i.mosaicOperator.toLowerCase(),t=o||(e?Pe.toJSON(e):null);return Pe.fromJSON(t)||"first"}castSortValue(s){return s==null||typeof s=="string"||typeof s=="number"?s:`${s}`}clone(){return new Ze({ascending:this.ascending,itemRenderingRule:$(this.itemRenderingRule),lockRasterIds:$(this.lockRasterIds),method:this.method,multidimensionalDefinition:$(this.multidimensionalDefinition),objectIds:$(this.objectIds),operation:this.operation,sortField:this.sortField,sortValue:this.sortValue,viewpoint:$(this.viewpoint),where:this.where})}};r([l({type:Boolean,json:{write:!0}})],M.prototype,"ascending",void 0),r([A("ascending",["ascending","sortAscending"])],M.prototype,"readAscending",null),r([l({type:S,json:{write:!0}})],M.prototype,"itemRenderingRule",void 0),r([l({type:[Fe],json:{write:{overridePolicy(){return{enabled:this.method==="lock-raster"}}}}})],M.prototype,"lockRasterIds",void 0),r([l({type:String,json:{type:Xe.jsonValues,write:{target:"mosaicMethod",writer:Xe.write}}})],M.prototype,"method",void 0),r([A("method",["mosaicMethod","defaultMosaicMethod"])],M.prototype,"readMethod",null),r([l({type:[vt],json:{write:!0}})],M.prototype,"multidimensionalDefinition",void 0),r([D("multidimensionalDefinition")],M.prototype,"writeMultidimensionalDefinition",null),r([l({type:[Fe],json:{name:"fids",write:!0}})],M.prototype,"objectIds",void 0),r([l({json:{type:Pe.jsonValues,read:{reader:Pe.read},write:{target:"mosaicOperation",writer:Pe.write}}})],M.prototype,"operation",void 0),r([A("operation",["mosaicOperation","mosaicOperator"])],M.prototype,"readOperation",null),r([l({type:String,json:{write:{overridePolicy(){return{enabled:this.method==="attribute"}}}}})],M.prototype,"sortField",void 0),r([l({type:[String,Number],json:{write:{allowNull:!0,overridePolicy(){return{enabled:this.method==="attribute",allowNull:!0}}}}})],M.prototype,"sortValue",void 0),r([Ct("sortValue")],M.prototype,"castSortValue",null),r([l({type:q,json:{write:!0}})],M.prototype,"viewpoint",void 0),r([l({type:String,json:{write:!0}})],M.prototype,"where",void 0),M=Ze=r([F("esri.layers.support.MosaicRule")],M);const U=M;let N=class extends L{constructor(){super(...arguments),this.layer=null,this.compression=void 0,this.pixelType=void 0,this.lercVersion=2}get adjustAspectRatio(){return this.layer.adjustAspectRatio}writeAdjustAspectRatio(i,o,e){this.layer.version<10.3||(o[e]=i)}get bandIds(){return this.layer.bandIds}get compressionQuality(){return this.layer.compressionQuality}writeCompressionQuality(i,o,e){this.format&&this.format.toLowerCase().includes("jpg")&&i!=null&&(o[e]=i)}get compressionTolerance(){return this.layer.compressionTolerance}writeCompressionTolerance(i,o,e){this.format==="lerc"&&i!=null&&(o[e]=i)}get format(){var i;return((i=this.layer.renderer)==null?void 0:i.type)==="vector-field"?"lerc":this.layer.format}get interpolation(){return this.layer.interpolation}get noData(){return this.layer.noData}get noDataInterpretation(){return this.layer.noDataInterpretation}writeLercVersion(i,o,e){this.format==="lerc"&&this.layer.version>=10.5&&(o[e]=i)}get version(){const i=this.layer;return i.commitProperty("bandIds"),i.commitProperty("format"),i.commitProperty("compressionQuality"),i.commitProperty("compressionTolerance"),i.commitProperty("interpolation"),i.commitProperty("noData"),i.commitProperty("noDataInterpretation"),i.commitProperty("mosaicRule"),i.commitProperty("renderingRule"),i.commitProperty("adjustAspectRatio"),i.commitProperty("pixelFilter"),i.commitProperty("definitionExpression"),i.commitProperty("multidimensionalSubset"),(this._get("version")||0)+1}set version(i){this._set("version",i)}get mosaicRule(){const i=this.layer;let o=i.mosaicRule;const e=i.definitionExpression;return o?e&&e!==o.where&&(o=o.clone(),o.where=e):e&&(o=new U({where:e})),o}get renderingRule(){var a,u;const i=this.layer;let o=i.renderingRule;const e=i.pixelFilter,t=!i.format||i.format.includes("jpg")||i.format.includes("png");o=this._addResampleRasterFunction(o);const n=(a=i.multidimensionalSubset)==null?void 0:a.areaOfInterest;return n&&(o=this._addClipFunction(o,n)),t&&!e&&((u=i.renderer)==null?void 0:u.type)!=="vector-field"&&(o=this.combineRendererWithRenderingRule(o)),o}combineRendererWithRenderingRule(i){var n;const o=this.layer,{rasterInfo:e,renderer:t}=o;return i=i||o.renderingRule,!t||!It(t)?i:We(Mi(t,{rasterAttributeTable:e.attributeTable,pixelType:e.pixelType,dataType:e.dataType,bandProperties:(n=e.keyProperties)==null?void 0:n.BandProperties,convertColorRampToColormap:o.version<10.6,convertToRFT:!!(i!=null&&i.rasterFunctionDefinition),bandCount:e.bandCount}),i)}_addResampleRasterFunction(i){var n;if(((n=this.layer.renderer)==null?void 0:n.type)!=="vector-field"||(i==null?void 0:i.functionName)==="Resample")return i;const o=this.layer.serviceDataType==="esriImageServiceDataTypeVector-UV"?7:10,e=this.layer.serviceRasterInfo.pixelSize;let t=new S({functionName:"Resample",functionArguments:{ResamplingType:o,InputCellSize:e}});return t=i!=null&&i.rasterFunctionDefinition?new S({rasterFunctionDefinition:te(t)}):t,We(t,i)}_addClipFunction(i,o){const e=new S({functionName:"Clip",functionArguments:{ClippingGeometry:o.toJSON(),ClippingType:1}});return We(e,i)}};r([l()],N.prototype,"layer",void 0),r([l({json:{write:!0}})],N.prototype,"adjustAspectRatio",null),r([D("adjustAspectRatio")],N.prototype,"writeAdjustAspectRatio",null),r([l({json:{write:!0}})],N.prototype,"bandIds",null),r([l({json:{write:!0}})],N.prototype,"compression",void 0),r([l({json:{write:!0}})],N.prototype,"compressionQuality",null),r([D("compressionQuality")],N.prototype,"writeCompressionQuality",null),r([l({json:{write:!0}})],N.prototype,"compressionTolerance",null),r([D("compressionTolerance")],N.prototype,"writeCompressionTolerance",null),r([l({json:{write:!0}})],N.prototype,"format",null),r([l({type:String,json:{read:{reader:ue.read},write:{writer:ue.write}}})],N.prototype,"interpolation",null),r([l({json:{write:!0}})],N.prototype,"noData",null),r([l({type:String,json:{read:{reader:Ne.read},write:{writer:Ne.write}}})],N.prototype,"noDataInterpretation",null),r([l({json:{write:!0}})],N.prototype,"pixelType",void 0),r([l({json:{write:!0}})],N.prototype,"lercVersion",void 0),r([D("lercVersion")],N.prototype,"writeLercVersion",null),r([l({type:Number})],N.prototype,"version",null),r([l({json:{write:!0}})],N.prototype,"mosaicRule",null),r([l({json:{write:!0}})],N.prototype,"renderingRule",null),N=r([F("esri.layers.mixins.ExportImageServiceParameters")],N);let be=class extends L{constructor(i){super(i),this.north=null,this.up=null,this.spatialReference=null}};r([l({type:Number,json:{write:!0}})],be.prototype,"north",void 0),r([l({type:Number,json:{write:!0}})],be.prototype,"up",void 0),r([l({type:fe,json:{write:!0}})],be.prototype,"spatialReference",void 0),be=r([F("esri.rest.support.ImageAngleResult")],be);const Hi=be;let oe=class extends L{constructor(){super(...arguments),this.value=null,this.displayValue=null,this.uncertainty=null}};r([l({type:Number,json:{read:!0,write:!0}})],oe.prototype,"value",void 0),r([l({type:String,json:{read:!0,write:!0}})],oe.prototype,"displayValue",void 0),r([l({type:Number,json:{read:!0,write:!0}})],oe.prototype,"uncertainty",void 0),oe=r([F("esri.rest.support.ImageMeasureResultValue")],oe);let Ie=class extends oe{constructor(){super(...arguments),this.unit=null}};r([l({type:String,json:{read:pe.read,write:pe.write}})],Ie.prototype,"unit",void 0),Ie=r([F("esri.rest.support.ImageMeasureResultLengthValue")],Ie);let Ve=class extends oe{constructor(){super(...arguments),this.unit=null}};r([l({type:String,json:{read:Ee.read,write:Ee.write}})],Ve.prototype,"unit",void 0),Ve=r([F("esri.rest.support.ImageMeasureResultAreaValue")],Ve);let Oe=class extends oe{constructor(){super(...arguments),this.unit=null}};r([l({type:String,json:{read:ze.read,write:ze.write}})],Oe.prototype,"unit",void 0),Oe=r([F("esri.rest.support.ImageMeasureResultAngleValue")],Oe);let le=class extends L{constructor(){super(...arguments),this.name=null,this.sensorName=null}};r([l({type:String,json:{read:!0,write:!0}})],le.prototype,"name",void 0),r([l({type:String,json:{read:!0,write:!0}})],le.prototype,"sensorName",void 0),le=r([F("esri.rest.support.BaseImageMeasureResult")],le);let Te=class extends le{constructor(){super(...arguments),this.area=null,this.perimeter=null}};r([l({type:Ve,json:{read:!0,write:!0}})],Te.prototype,"area",void 0),r([l({type:Ie,json:{read:!0,write:!0}})],Te.prototype,"perimeter",void 0),Te=r([F("esri.rest.support.ImageAreaResult")],Te);const Bi=Te;let Se=class extends le{constructor(){super(...arguments),this.distance=null,this.azimuthAngle=null,this.elevationAngle=null}};r([l({type:Ie,json:{read:!0,write:!0}})],Se.prototype,"distance",void 0),r([l({type:Oe,json:{read:!0,write:!0}})],Se.prototype,"azimuthAngle",void 0),r([l({type:Oe,json:{read:!0,write:!0}})],Se.prototype,"elevationAngle",void 0),Se=r([F("esri.rest.support.ImageDistanceResult")],Se);const ki=Se;let Me=class extends le{constructor(){super(...arguments),this.height=null}};r([l({type:Ie,json:{read:!0,write:!0}})],Me.prototype,"height",void 0),Me=r([F("esri.rest.support.ImageHeightResult")],Me);const Qi=Me;let K=class extends L{constructor(){super(...arguments),this.catalogItemVisibilities=null,this.catalogItems=null,this.location=null,this.name=null,this.objectId=null,this.processedValues=null,this.properties=null,this.value=null}};r([l({json:{write:!0}})],K.prototype,"catalogItemVisibilities",void 0),r([l({type:wt,json:{write:!0}})],K.prototype,"catalogItems",void 0),r([l({type:q,json:{write:!0}})],K.prototype,"location",void 0),r([l({json:{write:!0}})],K.prototype,"name",void 0),r([l({json:{write:!0}})],K.prototype,"objectId",void 0),r([l({json:{write:!0}})],K.prototype,"processedValues",void 0),r([l({json:{write:!0}})],K.prototype,"properties",void 0),r([l({json:{write:!0}})],K.prototype,"value",void 0),K=r([F("esri.rest.support.ImageIdentifyResult")],K);const Wi=K;let Ae=class extends L{constructor(){super(...arguments),this.geometries=null}};r([l({json:{write:!0}})],Ae.prototype,"geometries",void 0),Ae=r([F("esri.rest.support.ImagePixelLocationResult")],Ae);const Ki=Ae;let $e=class extends le{constructor(){super(...arguments),this.point=null}};r([l({type:q,json:{name:"point.value",read:!0,write:!0}})],$e.prototype,"point",void 0),$e=r([F("esri.rest.support.ImagePointResult")],$e);const Zi=$e;let re=class extends L{constructor(){super(...arguments),this.attributes=null,this.location=null,this.locationId=null,this.rasterId=null,this.resolution=null,this.pixelValue=null}};r([l({json:{write:!0}})],re.prototype,"attributes",void 0),r([l({type:q,json:{write:!0}})],re.prototype,"location",void 0),r([l({json:{write:!0}})],re.prototype,"locationId",void 0),r([l({json:{write:!0}})],re.prototype,"rasterId",void 0),r([l({json:{write:!0}})],re.prototype,"resolution",void 0),r([l({json:{write:!0}})],re.prototype,"pixelValue",void 0),re=r([F("esri.rest.support.ImageSample")],re);const Xi=re;let Je=class extends L{constructor(){super(...arguments),this.samples=null}};r([l({type:[Xi],json:{write:!0}})],Je.prototype,"samples",void 0),Je=r([F("esri.rest.support.ImageSampleResult")],Je);const Yi=Je;function Nt(s){const i=s==null?void 0:s.time;if(i&&(i.start!=null||i.end!=null)){const o=[];i.start!=null&&o.push(i.start),i.end==null||o.includes(i.end)||o.push(i.end),s.time=o.join(",")}}async function Pt(s,i,o){const e=ae(s),t=i.geometry?[i.geometry]:[],n=await Le(t),a=i.toJSON();Nt(a);const u=n&&n[0];b(u)&&(a.geometry=u.toJSON());const p=Re({...e.query,f:"json",...a});return we(p,o)}async function er(s,i,o){var p;const e=i.toJSON();b(e.angleName)&&(e.angleName=e.angleName.join(",")),b(i.point)&&((p=i.point.spatialReference)!=null&&p.imageCoordinateSystem)&&(e.point.spatialReference=He(i.point.spatialReference)),b(i.spatialReference)&&i.spatialReference.imageCoordinateSystem&&(e.spatialReference=Ot(i.spatialReference));const t=ae(s),n=Re({...t.query,f:"json",...e}),a=we(n,o),{data:u}=await O(`${t.path}/computeAngles`,a);return u.spatialReference=u.spatialReference?u.spatialReference.geodataXform!=null?new fe({wkid:0,imageCoordinateSystem:u.spatialReference}):fe.fromJSON(u.spatialReference):null,u.north==="NaN"&&(u.north=null),u.up==="NaN"&&(u.up=null),new Hi(u)}async function tr(s,i,o){var m;const e=i.toJSON(),{geometries:t}=i;if(t)for(let c=0;c<t.length;c++)(m=t[c].spatialReference)!=null&&m.imageCoordinateSystem&&(e.geometries.geometries[c].spatialReference=He(t[c].spatialReference));const n=ae(s),a=Re({...n.query,f:"json",...e}),u=we(a,o),{data:p}=await O(`${n.path}/computePixelLocation`,u);return Ki.fromJSON(p)}async function ir(s,i,o){const e=await Pt(s,i,o),t=ae(s),{data:n}=await O(`${t.path}/computeStatisticsHistograms`,e),{statistics:a}=n;return a!=null&&a.length&&a.forEach(u=>{u.avg=u.mean,u.stddev=u.standardDeviation}),{statistics:a,histograms:n.histograms}}async function rr(s,i,o){const e=await Pt(s,i,o),t=ae(s),{data:n}=await O(`${t.path}/computeHistograms`,e);return{histograms:n.histograms}}async function sr(s,i,o){var c,d,y;const e=i.toJSON();Nt(e),(c=e.outFields)!=null&&c.length&&(e.outFields=e.outFields.join(","));const t=(d=await Le(i.geometry))==null?void 0:d[0];b(t)&&(e.geometry=t.toJSON());const n=ae(s),a=Re({...n.query,f:"json",...e}),u=we(a,o),{data:p}=await O(`${n.path}/getSamples`,u),m=(y=p==null?void 0:p.samples)==null?void 0:y.map(w=>{const f=w.value==="NaN"||w.value===""?null:w.value.split(" ").map(R=>Number(R));return{...w,pixelValue:f}});return Yi.fromJSON({samples:m})}async function yt(s,i,o){const e=ae(s),t=i.geometry?[i.geometry]:[];return Le(t).then(n=>{const a=i.toJSON(),u=n&&n[0];b(u)&&(a.geometry=JSON.stringify(u.toJSON()));const p=Re({...e.query,f:"json",...a}),m=we(p,o);return O(e.path+"/identify",m)}).then(n=>Wi.fromJSON(n.data))}async function nr(s,i,o){const e=await Ue(s,i,[i.fromGeometry,i.toGeometry],o);return Qi.fromJSON(e)}async function ar(s,i,o){const e=await Ue(s,i,[i.geometry],o);return Bi.fromJSON(e)}async function or(s,i,o){const e=await Ue(s,i,[i.geometry],o);return Zi.fromJSON(e)}async function lr(s,i,o){const e=await Ue(s,i,[i.fromGeometry,i.toGeometry],o);return ki.fromJSON(e)}async function Ue(s,i,o,e){const t=ae(s),n=await Le(o),a=i.toJSON();b(n[0])&&(a.fromGeometry=JSON.stringify(gt(n[0]))),b(n[1])&&(a.toGeometry=JSON.stringify(gt(n[1])));const u=Re({...t.query,f:"json",...a}),p=we(u,e),{data:m}=await O(t.path+"/measure",p);return m}function gt(s){var o;const i=s.toJSON();return(o=s.spatialReference)!=null&&o.imageCoordinateSystem&&(i.spatialReference=He(s.spatialReference)),i}function He(s){const{imageCoordinateSystem:i}=s;if(i){const{id:o,referenceServiceName:e}=i;return o!=null?e?{icsid:o,icsns:e}:{icsid:o}:{ics:i}}return s.toJSON()}function Ot(s,i){const o=He(s),{icsid:e,icsns:t,wkid:n}=o;return e!=null?t==null||i!=null&&i.toLowerCase().includes("/"+t.toLowerCase()+"/")?`0:${e}`:JSON.stringify(o):n?n.toString():JSON.stringify(o)}async function ur(s,i,o){var _,J;const e=ae(s),t=Re({...e==null?void 0:e.query,f:"json"}),n=we(t,o),a=`${e==null?void 0:e.path}/${i}/info`,u=O(`${a}`,n),p=O(`${a}/keyProperties`,n),m=await Promise.allSettled([u,p]),c=m[0].status==="fulfilled"?m[0].value.data:null,d=m[1].status==="fulfilled"?m[1].value.data:null;let y=null;(_=c.statistics)!=null&&_.length&&(y=c.statistics.map(T=>({min:T[0],max:T[1],avg:T[2],stddev:T[3]})));const w=je.fromJSON(c.extent),f=Math.ceil(w.width/c.pixelSizeX-.1),R=Math.ceil(w.height/c.pixelSizeY-.1),h=w.spatialReference,g=new q({x:c.pixelSizeX,y:c.pixelSizeY,spatialReference:h}),v=(J=c.histograms)!=null&&J.length?c.histograms:null,x=new Ii({origin:c.origin,blockWidth:c.blockWidth,blockHeight:c.blockHeight,firstPyramidLevel:c.firstPyramidLevel,maximumPyramidLevel:c.maxPyramidLevel});return new xt({width:f,height:R,bandCount:c.bandCount,extent:w,spatialReference:h,pixelSize:g,pixelType:c.pixelType.toLowerCase(),statistics:y,histograms:v,keyProperties:d,storageInfo:x})}var Ye;let ye=Ye=class extends L{constructor(s){super(s),this.angleNames=null,this.point=null,this.spatialReference=null,this.rasterId=null}clone(){return new Ye($({angleNames:this.angleNames,point:this.point,spatialReference:this.spatialReference,rasterId:this.rasterId}))}};r([l({type:[String],json:{name:"angleName",write:!0}})],ye.prototype,"angleNames",void 0),r([l({type:q,json:{write:!0}})],ye.prototype,"point",void 0),r([l({type:fe,json:{write:!0}})],ye.prototype,"spatialReference",void 0),r([l({type:Fe,json:{write:!0}})],ye.prototype,"rasterId",void 0),ye=Ye=r([F("esri.rest.support.ImageAngleParameters")],ye);const pr=ye,Q=new _t({esriMensurationPoint:"point",esriMensurationCentroid:"centroid",esriMensurationDistanceAndAngle:"distance-and-angle",esriMensurationAreaAndPerimeter:"area-and-perimeter",esriMensurationHeightFromBaseAndTop:"base-and-top",esriMensurationHeightFromBaseAndTopShadow:"base-and-top-shadow",esriMensurationHeightFromTopAndTopShadow:"top-and-top-shadow",esriMensurationPoint3D:"point-3D",esriMensurationCentroid3D:"centroid-3D",esriMensurationDistanceAndAngle3D:"distance-and-angle-3D",esriMensurationAreaAndPerimeter3D:"area-and-perimeter-3D"});let k=class extends L{constructor(){super(...arguments),this.type=null,this.measureOperation=null,this.mosaicRule=null,this.renderingRule=null,this.pixelSize=null,this.raster=void 0}};r([l()],k.prototype,"type",void 0),r([l({type:Q.apiValues,json:{read:Q.read,write:Q.write}})],k.prototype,"measureOperation",void 0),r([l({type:U,json:{write:!0}})],k.prototype,"mosaicRule",void 0),r([l({type:S,json:{write:!0}})],k.prototype,"renderingRule",void 0),r([l({type:q,json:{write:!0}})],k.prototype,"pixelSize",void 0),r([l({json:{write:!0}})],k.prototype,"raster",void 0),k=r([F("esri.rest.support.BaseImageMeasureParameters")],k);var et;let se=et=class extends k{constructor(){super(...arguments),this.type="area-perimeter",this.geometry=null,this.is3D=!1,this.linearUnit="meters",this.areaUnit="square-meters"}writeGeometry(s,i,o){s!=null&&(i.geometryType=ve(s),i[o]=s.toJSON())}get measureOperation(){return this.is3D?"area-and-perimeter-3D":"area-and-perimeter"}clone(){return new et($({geometry:this.geometry,is3D:this.is3D,linearUnit:this.linearUnit,areaUnit:this.areaUnit,mosaicRule:this.mosaicRule,renderingRule:this.renderingRule,pixelSize:this.pixelSize,raster:this.raster}))}};r([l({types:Ge,json:{name:"fromGeometry",read:!0,write:!0}})],se.prototype,"geometry",void 0),r([D("geometry")],se.prototype,"writeGeometry",null),r([l({type:Q.apiValues,json:{write:Q.write}})],se.prototype,"measureOperation",null),r([l({json:{read:!0}})],se.prototype,"is3D",void 0),r([l({type:String,json:{read:pe.read,write:pe.write}})],se.prototype,"linearUnit",void 0),r([l({type:String,json:{read:Ee.read,write:Ee.write}})],se.prototype,"areaUnit",void 0),se=et=r([F("esri.rest.support.ImageAreaParameters")],se);const cr=se;var tt;let Y=tt=class extends k{constructor(){super(...arguments),this.type="distance-angle",this.fromGeometry=null,this.toGeometry=null,this.is3D=!1,this.linearUnit="meters",this.angularUnit="degrees"}writeFromGeometry(s,i,o){s!=null&&(i.geometryType=ve(s),i[o]=s.toJSON())}get measureOperation(){return this.is3D?"distance-and-angle-3D":"distance-and-angle"}clone(){return new tt($({fromGeometry:this.fromGeometry,toGeometry:this.toGeometry,is3D:this.is3D,linearUnit:this.linearUnit,angularUnit:this.angularUnit,mosaicRule:this.mosaicRule,renderingRule:this.renderingRule,pixelSize:this.pixelSize,raster:this.raster}))}};r([l({type:q,json:{read:!0,write:!0}})],Y.prototype,"fromGeometry",void 0),r([D("fromGeometry")],Y.prototype,"writeFromGeometry",null),r([l({type:q,json:{read:!0,write:!0}})],Y.prototype,"toGeometry",void 0),r([l({type:Q.apiValues,json:{write:Q.write}})],Y.prototype,"measureOperation",null),r([l({json:{read:!0}})],Y.prototype,"is3D",void 0),r([l({type:String,json:{read:pe.read,write:pe.write}})],Y.prototype,"linearUnit",void 0),r([l({type:String,json:{read:ze.read,write:ze.write}})],Y.prototype,"angularUnit",void 0),Y=tt=r([F("esri.rest.support.ImageDistanceParameters")],Y);const mr=Y;var it;let ne=it=class extends k{constructor(){super(...arguments),this.type="height",this.fromGeometry=null,this.toGeometry=null,this.operationType="base-and-top",this.linearUnit="meters"}writeFromGeometry(s,i,o){s!=null&&(i.geometryType=ve(s),i[o]=s.toJSON())}get measureOperation(){return this.operationType}clone(){return new it($({fromGeometry:this.fromGeometry,toGeometry:this.toGeometry,operationType:this.operationType,linearUnit:this.linearUnit,mosaicRule:this.mosaicRule,renderingRule:this.renderingRule,pixelSize:this.pixelSize,raster:this.raster}))}};r([l({type:q,json:{read:!0}})],ne.prototype,"fromGeometry",void 0),r([D("fromGeometry")],ne.prototype,"writeFromGeometry",null),r([l({type:q,json:{read:!0,write:!0}})],ne.prototype,"toGeometry",void 0),r([l({type:Q.apiValues,json:{write:Q.write}})],ne.prototype,"measureOperation",null),r([l({json:{read:!0}})],ne.prototype,"operationType",void 0),r([l({type:String,json:{read:pe.read,write:pe.write}})],ne.prototype,"linearUnit",void 0),ne=it=r([F("esri.rest.support.ImageHeightParameters")],ne);const dr=ne;var rt;let ee=rt=class extends L{constructor(){super(...arguments),this.geometry=null,this.mosaicRule=null,this.renderingRule=null,this.pixelSize=null,this.raster=void 0,this.timeExtent=null}writeGeometry(s,i,o){s!=null&&(i.geometryType=ve(s),i[o]=s.toJSON())}clone(){return new rt($({geometry:this.geometry,mosaicRule:this.mosaicRule,renderingRule:this.renderingRule,pixelSize:this.pixelSize,raster:this.raster,timeExtent:this.timeExtent}))}};r([l({types:Ge,json:{read:ut}})],ee.prototype,"geometry",void 0),r([D("geometry")],ee.prototype,"writeGeometry",null),r([l({type:U,json:{write:!0}})],ee.prototype,"mosaicRule",void 0),r([l({type:S,json:{write:!0}})],ee.prototype,"renderingRule",void 0),r([l({type:q,json:{write:!0}})],ee.prototype,"pixelSize",void 0),r([l({json:{write:!0}})],ee.prototype,"raster",void 0),r([l({type:pt,json:{read:{source:"time"},write:{target:"time"}}})],ee.prototype,"timeExtent",void 0),ee=rt=r([F("esri.rest.support.ImageHistogramParameters")],ee);const ft=ee;var st;let P=st=class extends L{constructor(){super(...arguments),this.geometry=null,this.renderingRules=null,this.pixelSize=null,this.returnGeometry=!0,this.returnCatalogItems=!0,this.returnPixelValues=!0,this.maxItemCount=null,this.timeExtent=null,this.raster=void 0,this.viewId=void 0,this.processAsMultidimensional=!1}writeGeometry(s,i,o){s!=null&&(i.geometryType=ve(s),i[o]=JSON.stringify(s.toJSON()))}set mosaicRule(s){let i=s;i&&i.mosaicMethod&&(i=U.fromJSON({...i.toJSON(),mosaicMethod:i.mosaicMethod,mosaicOperation:i.mosaicOperation})),this._set("mosaicRule",i)}writeMosaicRule(s,i,o){s!=null&&(i[o]=JSON.stringify(s.toJSON()))}set renderingRule(s){let i=s;i&&i.rasterFunction&&(i=S.fromJSON({...i.toJSON(),rasterFunction:i.rasterFunction,rasterFunctionArguments:i.rasterFunctionArguments})),this._set("renderingRule",i)}writeRenderingRule(s,i,o){s!=null&&(i[o]=JSON.stringify(s.toJSON())),s.rasterFunctionDefinition&&(i[o]=JSON.stringify(s.rasterFunctionDefinition))}writeRenderingRules(s,i,o){s!=null&&(i[o]=JSON.stringify(s.map(e=>e.rasterFunctionDefinition||e.toJSON())))}writePixelSize(s,i,o){s!=null&&(i[o]=JSON.stringify(s))}writeTimeExtent(s,i,o){if(s!=null){const e=b(s.start)?s.start.getTime():null,t=b(s.end)?s.end.getTime():null;i[o]=e!=null?t!=null?`${e},${t}`:`${e}`:null}}clone(){return new st($({geometry:this.geometry,mosaicRule:this.mosaicRule,renderingRule:this.renderingRule,pixelSize:this.pixelSize,returnGeometry:this.returnGeometry,returnCatalogItems:this.returnCatalogItems,returnPixelValues:this.returnPixelValues,maxItemCount:this.maxItemCount,processAsMultidimensional:this.processAsMultidimensional,raster:this.raster,viewId:this.viewId,timeExtent:this.timeExtent}))}};r([l({json:{write:!0}})],P.prototype,"geometry",void 0),r([D("geometry")],P.prototype,"writeGeometry",null),r([l({type:U,json:{write:!0}})],P.prototype,"mosaicRule",null),r([D("mosaicRule")],P.prototype,"writeMosaicRule",null),r([l({type:S,json:{write:!0}})],P.prototype,"renderingRule",null),r([D("renderingRule")],P.prototype,"writeRenderingRule",null),r([l({type:[S],json:{write:!0}})],P.prototype,"renderingRules",void 0),r([D("renderingRules")],P.prototype,"writeRenderingRules",null),r([l({type:q,json:{write:!0}})],P.prototype,"pixelSize",void 0),r([D("pixelSize")],P.prototype,"writePixelSize",null),r([l({type:Boolean,json:{write:!0}})],P.prototype,"returnGeometry",void 0),r([l({type:Boolean,json:{write:!0}})],P.prototype,"returnCatalogItems",void 0),r([l({type:Boolean,json:{write:!0}})],P.prototype,"returnPixelValues",void 0),r([l({type:Number,json:{write:!0}})],P.prototype,"maxItemCount",void 0),r([l({type:pt,json:{write:{target:"time"}}})],P.prototype,"timeExtent",void 0),r([D("timeExtent")],P.prototype,"writeTimeExtent",null),r([l({json:{write:!0}})],P.prototype,"raster",void 0),r([l({json:{write:!0}})],P.prototype,"viewId",void 0),r([l({type:Boolean,json:{write:!0}})],P.prototype,"processAsMultidimensional",void 0),P=st=r([F("esri.rest.support.ImageIdentifyParameters")],P);const Rt=P;var nt;let xe=nt=class extends L{constructor(){super(...arguments),this.geometries=null,this.rasterId=null}writeGeometry(s,i,o){i.geometries={geometryType:"esriGeometryPoint",geometries:s.map(e=>e.toJSON())}}clone(){var s;return new nt({geometries:((s=this.geometries)==null?void 0:s.map(i=>i.clone()))??[],rasterId:this.rasterId})}};r([l({type:[q],json:{write:!0}})],xe.prototype,"geometries",void 0),r([D("geometries")],xe.prototype,"writeGeometry",null),r([l({type:Fe,json:{write:!0}})],xe.prototype,"rasterId",void 0),xe=nt=r([F("esri.rest.support.ImagePixelLocationParameters")],xe);const hr=xe;var at;let ge=at=class extends k{constructor(){super(...arguments),this.type="point",this.geometry=null,this.is3D=!1}writeGeometry(s,i,o){s!=null&&(i.geometryType=ve(s),i[o]=s.toJSON())}get measureOperation(){const{is3D:s,geometry:i}=this;return i.type==="point"?s?"point-3D":"point":s?"centroid-3D":"centroid"}clone(){return new at($({geometry:this.geometry,is3D:this.is3D,mosaicRule:this.mosaicRule,renderingRule:this.renderingRule,pixelSize:this.pixelSize,raster:this.raster}))}};r([l({types:Ge,json:{name:"fromGeometry",read:ut}})],ge.prototype,"geometry",void 0),r([D("geometry")],ge.prototype,"writeGeometry",null),r([l({type:Q.apiValues,json:{read:Q.read,write:Q.write}})],ge.prototype,"measureOperation",null),r([l({json:{read:!0}})],ge.prototype,"is3D",void 0),ge=at=r([F("esri.rest.support.ImagePointParameters")],ge);const yr=ge;var ot;let E=ot=class extends L{constructor(){super(...arguments),this.geometry=null,this.interpolation="nearest",this.mosaicRule=null,this.outFields=null,this.pixelSize=null,this.returnFirstValueOnly=!0,this.sampleDistance=null,this.sampleCount=null,this.sliceId=null,this.timeExtent=null}writeGeometry(s,i,o){s!=null&&(i.geometryType=ve(s),i[o]=s.toJSON())}set locations(s){if(s!=null&&s.length){const i=new $t({spatialReference:s[0].spatialReference});i.points=s.map(o=>[o.x,o.y]),this._set("locations",s),this.geometry=i}}clone(){return new ot($({geometry:this.geometry,locations:this.locations,interpolation:this.interpolation,mosaicRule:this.mosaicRule,outFields:this.outFields,raster:this.raster,returnFirstValueOnly:this.returnFirstValueOnly,sampleDistance:this.sampleDistance,sampleCount:this.sampleCount,sliceId:this.sliceId,pixelSize:this.pixelSize,timeExtent:this.timeExtent}))}};r([l({types:Ge,json:{read:ut}})],E.prototype,"geometry",void 0),r([D("geometry")],E.prototype,"writeGeometry",null),r([l()],E.prototype,"locations",null),r([l({type:String,json:{type:ue.jsonValues,read:ue.read,write:ue.write}})],E.prototype,"interpolation",void 0),r([l({type:U,json:{write:!0}})],E.prototype,"mosaicRule",void 0),r([l({type:[String],json:{write:!0}})],E.prototype,"outFields",void 0),r([l({type:q,json:{write:!0}})],E.prototype,"pixelSize",void 0),r([l({type:String,json:{write:!0}})],E.prototype,"raster",void 0),r([l({type:Boolean,json:{write:!0}})],E.prototype,"returnFirstValueOnly",void 0),r([l({type:Number,json:{write:!0}})],E.prototype,"sampleDistance",void 0),r([l({type:Number,json:{write:!0}})],E.prototype,"sampleCount",void 0),r([l({type:Number,json:{write:!0}})],E.prototype,"sliceId",void 0),r([l({type:pt,json:{read:{source:"time"},write:{target:"time"}}})],E.prototype,"timeExtent",void 0),E=ot=r([F("esri.rest.support.ImageSampleParameters")],E);const gr=E,Ke=lt()({U1:"u1",U2:"u2",U4:"u4",U8:"u8",S8:"s8",U16:"u16",S16:"s16",U32:"u32",S32:"s32",F32:"f32",F64:"f64",C64:"c64",C128:"c128",UNKNOWN:"unknown"}),fr=new Set(["png","png8","png24","png32","jpg","bmp","gif","jpgpng","lerc","tiff"]),Rr=jt(Vt,{min:0,max:255});function wr(s){var o;if(!s)return null;const i=(o=JSON.stringify(s).match(/"rasterFunction":"(.*?")/gi))==null?void 0:o.map(e=>e.replace('"rasterFunction":"',"").replace('"',""));return i?i.join("/"):null}const vr=s=>{let i=class extends s{constructor(){super(...arguments),this._functionRasterInfos={},this._rasterJobHandler={instance:null,refCount:0,connectionPromise:null},this._cachedRendererJson=null,this._serviceSupportsMosaicRule=null,this._rasterAttributeTableFieldPrefix="Raster.",this.adjustAspectRatio=null,this.bandIds=void 0,this.capabilities=null,this.compressionQuality=void 0,this.compressionTolerance=.01,this.copyright=null,this.defaultMosaicRule=null,this.definitionExpression=null,this.exportImageServiceParameters=null,this.rasterInfo=null,this.fields=null,this.fullExtent=null,this.hasMultidimensions=!1,this.imageMaxHeight=4100,this.imageMaxWidth=4100,this.interpolation=void 0,this.minScale=0,this.maxScale=0,this.multidimensionalInfo=null,this.multidimensionalSubset=null,this.noData=null,this.noDataInterpretation=void 0,this.objectIdField=null,this.geometryType="polygon",this.typeIdField=null,this.types=[],this.pixelFilter=null,this.raster=void 0,this.sourceType=null,this.viewId=void 0,this.symbolizer=null,this.rasterFunctionInfos=null,this.serviceDataType=null,this.spatialReference=null,this.pixelType=null,this.serviceRasterInfo=null,this.sourceJSON=null,this.url=null,this.version=void 0}initialize(){this._set("exportImageServiceParameters",new N({layer:this}))}readServiceSupportsMosaicRule(e,t){return this._isMosaicRuleSupported(t)}get _rasterFunctionNamesIndex(){const e=new Map;return!this.rasterFunctionInfos||b(this.rasterFunctionInfos)&&this.rasterFunctionInfos.length<1||b(this.rasterFunctionInfos)&&this.rasterFunctionInfos.forEach(t=>{e.set(t.name.toLowerCase().replace(/ /gi,"_"),t.name)}),e}readBandIds(e,t){if(Array.isArray(e)&&e.length>0&&e.every(n=>typeof n=="number"))return e}readCapabilities(e,t){return this._readCapabilities(t)}writeCompressionQuality(e,t,n){e!=null&&this.format!=="lerc"&&(t[n]=e)}writeCompressionTolerance(e,t,n){this.format==="lerc"&&e!=null&&(t[n]=e)}readDefaultMosaicRule(e,t){return this._serviceSupportsMosaicRule?U.fromJSON(t):null}get fieldsIndex(){return this.fields?new zt(this.fields):null}set format(e){e&&fr.has(e.toLowerCase())&&this._set("format",e.toLowerCase())}readFormat(e,t){return t.serviceDataType==="esriImageServiceDataTypeVector-UV"||t.serviceDataType==="esriImageServiceDataTypeVector-MagDir"||this.pixelFilter!=null?"lerc":"jpgpng"}readMinScale(e,t){return t.minLOD!=null&&t.maxLOD!=null?e:0}readMaxScale(e,t){return t.minLOD!=null&&t.maxLOD!=null?e:0}set mosaicRule(e){let t=e;t&&t.mosaicMethod&&(t=U.fromJSON({...t.toJSON(),mosaicMethod:t.mosaicMethod,mosaicOperation:t.mosaicOperation})),this._set("mosaicRule",t)}readMosaicRule(e,t){const n=e||t.mosaicRule;return n?U.fromJSON(n):this._isMosaicRuleSupported(t)?U.fromJSON(t):null}writeMosaicRule(e,t,n){let a=this.mosaicRule;const u=this.definitionExpression;a?u&&u!==a.where&&(a=a.clone(),a.where=u):u&&(a=new U({where:u})),this._isValidCustomizedMosaicRule(a)&&(t[n]=a.toJSON())}writeNoData(e,t,n){e!=null&&typeof e=="number"&&(t[n]=Rr(e))}readObjectIdField(e,t){if(!e){const n=t.fields.filter(a=>a.type==="esriFieldTypeOID"||a.type==="oid");e=n&&n[0]&&n[0].name}return e}get parsedUrl(){return gi(this.url)}readSourceType(e,t){return this._isMosaicDataset(t)?"mosaic-dataset":"raster-dataset"}set renderer(e){this.loaded&&(e=this._configRenderer(e)),this._set("renderer",e)}readRenderer(e,t,n){var p,m;const a=(m=(p=t==null?void 0:t.layerDefinition)==null?void 0:p.drawingInfo)==null?void 0:m.renderer,u=li(a,n);return u==null?null:(u.type==="vector-field"&&t.symbolTileSize&&!a.symbolTileSize&&(u.symbolTileSize=t.symbolTileSize),It(u)||mt.getLogger(this.declaredClass).warn("ArcGISImageService","Imagery layer doesn't support given renderer type."),u)}writeRenderer(e,t,n){t.layerDefinition=t.layerDefinition||{},t.layerDefinition.drawingInfo=t.layerDefinition.drawingInfo||{},t.layerDefinition.drawingInfo.renderer=e.toJSON(),e.type==="vector-field"&&(t.symbolTileSize=e.symbolTileSize)}get rasterFields(){var m;const e=this._rasterAttributeTableFieldPrefix||"Raster.",t=new he({name:"Raster.ItemPixelValue",alias:"Item Pixel Value",domain:null,editable:!1,length:50,type:"string"}),n=new he({name:"Raster.ServicePixelValue",alias:"Service Pixel Value",domain:null,editable:!1,length:50,type:"string"}),a=new he({name:"Raster.ServicePixelValue.Raw",alias:"Raw Service Pixel Value",domain:null,editable:!1,length:50,type:"string"});let u=this.fields?$(this.fields):[];u.push(n),(m=this.capabilities)!=null&&m.operations.supportsQuery&&this.fields&&this.fields.length>0&&u.push(t),this.version>=10.4&&b(this.rasterFunctionInfos)&&this.rasterFunctionInfos.some(c=>c.name.toLowerCase()==="none")&&u.push(a),b(this.rasterFunctionInfos)&&this.rasterFunctionInfos.filter(c=>c.name.toLowerCase()!=="none").forEach(c=>{u.push(new he({name:"Raster.ServicePixelValue."+c.name,alias:c.name,domain:null,editable:!1,length:50,type:"string"}))}),this._isVectorDataSet()&&(u.push(new he({name:"Raster.Magnitude",alias:"Magnitude",domain:null,editable:!1,type:"double"})),u.push(new he({name:"Raster.Direction",alias:"Direction",domain:null,editable:!1,type:"double"})));const{attributeTable:p}=this.rasterInfo??{};if(b(p)){const c=p.fields.filter(d=>d.type!=="esriFieldTypeOID"&&d.name.toLowerCase()!=="value").map(d=>{const y=$(d);return y.name=e+d.name,y});u=u.concat(c)}return u}set renderingRule(e){let t=e;t&&t.rasterFunction&&(t=S.fromJSON({...t.toJSON(),rasterFunction:t.rasterFunction,rasterFunctionArguments:t.rasterFunctionArguments})),this._set("renderingRule",t)}readRenderingRule(e,t){const n=t.rasterFunctionInfos;return t.renderingRule||n&&n.length&&n[0].name!=="None"?this._isRFTJson(t.renderingRule)?S.fromJSON({rasterFunctionDefinition:t.renderingRule}):S.fromJSON(t.renderingRule||{rasterFunctionInfos:t.rasterFunctionInfos}):null}writeRenderingRule(e,t,n){const a=e.toJSON();a.rasterFunctionDefinition?t[n]=a.rasterFunctionDefinition:t[n]=a}readSpatialReference(e,t){const n=e||t.extent.spatialReference;return n?fe.fromJSON(n):null}readPixelType(e){return Ke.fromJSON(e)||e}writePixelType(e,t,n){(G(this.serviceRasterInfo)||this.pixelType!==this.serviceRasterInfo.pixelType)&&(t[n]=Ke.toJSON(e))}readVersion(e,t){let n=t.currentVersion;return n||(n=t.hasOwnProperty("fields")||t.hasOwnProperty("timeInfo")?10:9.3),n}applyFilter(e){let t=e;return this.pixelFilter&&(t=this._clonePixelData(e),this.pixelFilter(t)),t}async applyRenderer(e,t){let n=e;const{renderer:a,symbolizer:u,pixelFilter:p,bandIds:m}=this;if(!this._isPicture()&&a&&u&&!p){const c=JSON.stringify(this._cachedRendererJson)!==JSON.stringify(a.toJSON()),d=this._rasterJobHandler.instance;if(d){c&&(u.bind(),await d.updateSymbolizer(u,t),this._cachedRendererJson=a.toJSON());const y=await d.symbolize({bandIds:m,...e},t);n={extent:e.extent,pixelBlock:y}}else n={extent:e.extent,pixelBlock:u.symbolize({bandIds:m,...e})}}return n}destroy(){this._shutdownJobHandler()}increaseRasterJobHandlerUsage(){this._rasterJobHandler.refCount++}decreaseRasterJobHandlerUsage(){this._rasterJobHandler.refCount--,this._rasterJobHandler.refCount<=0&&this._shutdownJobHandler()}async computeAngles(e,t){if(!(await this._fetchCapabilities(t==null?void 0:t.signal)).operations.supportsComputeAngles)throw new V("imagery-layer:compute-angles","this operation is not supported on the input image service");return e=H(pr,e).clone(),er(this.url,e,this._getRequestOptions(t))}async computePixelSpaceLocations(e,t){if(!(await this._fetchCapabilities(t==null?void 0:t.signal)).operations.supportsComputePixelLocation)throw new V("imagery-layer:compute-pixel-space-locations","this operation is not supported on the input image service");return e=H(hr,e).clone(),tr(this.url,e,this._getRequestOptions(t))}async computeHistograms(e,t){if(!(await this._fetchCapabilities(t==null?void 0:t.signal)).operations.supportsComputeHistograms)throw new V("imagery-layer:compute-histograms","this operation is not supported on the input image service");return e=H(ft,e).clone(),this._applyMosaicAndRenderingRules(e),rr(this.url,e,this._getRequestOptions(t))}async computeStatisticsHistograms(e,t){if(!(await this._fetchCapabilities(t==null?void 0:t.signal)).operations.supportsComputeStatisticsHistograms)throw new V("imagery-layer:compute-statistics-histograms","this operation is not supported on the input image service");return e=H(ft,e).clone(),this._applyMosaicAndRenderingRules(e),ir(this.url,e,this._getRequestOptions(t))}async measureHeight(e,t){const n=await this._fetchCapabilities(t==null?void 0:t.signal);if(!(e.operationType==="base-and-top"?n.mensuration.supportsHeightFromBaseAndTop:e.operationType==="base-and-top-shadow"?n.mensuration.supportsHeightFromBaseAndTopShadow:n.mensuration.supportsHeightFromTopAndTopShadow))throw new V("imagery-layer:measure-height","this operation is not supported on the input image service");return e=H(dr,e).clone(),this._applyMosaicAndRenderingRules(e),nr(this.url,e,this._getRequestOptions(t))}async measureAreaAndPerimeter(e,t){const n=await this._fetchCapabilities(t==null?void 0:t.signal);if(!(n.mensuration.supportsAreaAndPerimeter&&(!e.is3D||n.mensuration.supports3D)))throw new V("imagery-layer:measure-area-and-perimeter","this operation is not supported on the input image service");return e=H(cr,e).clone(),this._applyMosaicAndRenderingRules(e),ar(this.url,e,this._getRequestOptions(t))}async measureDistanceAndAngle(e,t){const n=await this._fetchCapabilities(t==null?void 0:t.signal);if(!(n.mensuration.supportsDistanceAndAngle&&(!e.is3D||n.mensuration.supports3D)))throw new V("imagery-layer:measure-distance-and-angle","this operation is not supported on the input image service");return e=H(mr,e).clone(),this._applyMosaicAndRenderingRules(e),lr(this.url,e,this._getRequestOptions(t))}async measurePointOrCentroid(e,t){const n=await this._fetchCapabilities(t==null?void 0:t.signal);if(!(n.mensuration.supportsPointOrCentroid&&(!e.is3D||n.mensuration.supports3D)))throw new V("imagery-layer:measure-point-or-centroid","this operation is not supported on the input image service");return e=H(yr,e).clone(),this._applyMosaicAndRenderingRules(e),or(this.url,e,this._getRequestOptions(t))}getField(e){const{fieldsIndex:t}=this;return b(t)?t.get(e):void 0}getFieldDomain(e,t){const n=this.getField(e);return n?n.domain:null}async fetchImage(e,t,n,a={}){if(e==null||t==null||n==null)throw new V("imagery-layer:fetch-image","Insufficient parameters for requesting an image. A valid extent, width and height values are required.");if(this.renderer||this.symbolizer){const d=await this.generateRasterInfo(this.renderingRule,{signal:a.signal});d&&(this.rasterInfo=d)}const u=this.getExportImageServiceParameters(e,t,n,a.timeExtent);if(u==null){if(a.requestAsImageElement&&this._canRequestImageElement(this.format)){const g=document.createElement("canvas");return g.width=t,g.height=n,a.returnImageBitmap?{imageBitmap:await ht(g,`${o(this.parsedUrl)}/exportImage`)}:{imageOrCanvasElement:g}}const{bandIds:d,rasterInfo:y}=this,w=((d==null?void 0:d.length)||y.bandCount)??0,f=t*n,R=y.pixelType,h=[];for(let g=0;g<w;g++)h.push(Qe.createEmptyBand(R,f));return{pixelData:{pixelBlock:new Qe({width:t,height:n,pixels:h,mask:new Uint8Array(f),pixelType:R}),extent:e}}}const p=!!a.requestAsImageElement&&!this.pixelFilter,m=p&&!!a.returnImageBitmap,c={imageServiceParameters:u,imageProps:{extent:e,width:t,height:n,format:this.format},requestAsImageElement:p,returnImageBitmap:m,signal:a.signal};return this._requestArrayBuffer(c)}fetchKeyProperties(e){return O(o(this.parsedUrl)+"/keyProperties",{query:this._getQueryParams({renderingRule:this.version>=10.3?e==null?void 0:e.renderingRule:null})}).then(t=>t.data)}fetchRasterAttributeTable(e){return this.version<10.1?Promise.reject(new V("#fetchRasterAttributeTable()","Failed to get rasterAttributeTable")):O(o(this.parsedUrl)+"/rasterAttributeTable",{query:this._getQueryParams({renderingRule:this.version>=10.3?e==null?void 0:e.renderingRule:null})}).then(t=>wt.fromJSON(t.data))}getCatalogItemRasterInfo(e,t){const n={...t,query:this._getQueryParams()};return ur(o(this.parsedUrl),e,n)}async getCatalogItemICSInfo(e,t){var de,ie,ct;const{data:n}=await O(o(this.parsedUrl)+"/"+e+"/info/ics",{query:this._getQueryParams(),...t}),a=n&&n.ics;if(!a)return;let u=null;try{u=(await O(o(this.parsedUrl)+"/"+e+"/info",{query:this._getQueryParams(),...t})).data.extent}catch{}if(!u||!u.spatialReference)return{ics:a,icsToPixelTransform:null,icsExtent:null,northDirection:null};const p=this.version>=10.7?O(o(this.parsedUrl)+"/"+e+"/info/icstopixel",{query:this._getQueryParams(),...t}).then(I=>I.data).catch(()=>({})):{},m=u.spatialReference,c={geometries:JSON.stringify({geometryType:"esriGeometryEnvelope",geometries:[u]}),inSR:m.wkid||JSON.stringify(m),outSR:"0:"+e},d=O(o(this.parsedUrl)+"/project",{query:this._getQueryParams(c),...t}).then(I=>I.data).catch(()=>({})),y=5,w=(u.xmin+u.xmax)/2,f=(u.ymax-u.ymin)/(y+1),R=u.ymin+f,h=[];for(let I=0;I<y;I++)h.push({x:w,y:R+f*I});const g={geometries:JSON.stringify({geometryType:"esriGeometryPoint",geometries:h}),inSR:m.wkid||JSON.stringify(m),outSR:"0:"+e},v=O(o(this.parsedUrl)+"/project",{query:this._getQueryParams(g),...t}).then(I=>I.data).catch(()=>({})),x=await Promise.all([p,d,v]);let _=x[0].ipxf;if(_==null){const I=(de=a.geodataXform)==null?void 0:de.xf_0;((ie=I==null?void 0:I.name)==null?void 0:ie.toLowerCase())==="topup"&&((ct=I==null?void 0:I.coefficients)==null?void 0:ct.length)===6&&(_={affine:{name:"ics [sensor: Frame] to pixel (column, row) transformation",coefficients:I.coefficients,cellsizeRatio:0,type:"GeometricXform"}})}const J=je.fromJSON(x[1]&&x[1].geometries&&x[1].geometries[0]);J&&(J.spatialReference=new fe({wkid:0,imageCoordinateSystem:a}));const T=x[2].geometries?x[2].geometries.filter(I=>I!=null&&I.x!=null&&I.y!=null&&I.x!=="NaN"&&I.y!=="NaN"):[],Z=T.length;if(Z<3)return{ics:a,icsToPixelTransform:_,icsExtent:J,northDirection:null};let j=0,C=0,z=0,X=0;for(let I=0;I<Z;I++)j+=T[I].x,C+=T[I].y,z+=T[I].x*T[I].x,X+=T[I].x*T[I].y;const W=(Z*X-j*C)/(Z*z-j*j);let ce=0;const Ce=T[y-1].x>T[0].x,me=T[y-1].y>T[0].y;return W===1/0?ce=me?90:270:W===0?ce=Ce?0:180:W>0?ce=Ce?180*Math.atan(W)/Math.PI:180*Math.atan(W)/Math.PI+180:W<0&&(ce=me?180+180*Math.atan(W)/Math.PI:360+180*Math.atan(W)/Math.PI),{ics:a,icsToPixelTransform:_,icsExtent:J,northDirection:ce}}async generateRasterInfo(e,t){var u;if(e=H(S,e),this.serviceRasterInfo&&(!e||((u=e.functionName)==null?void 0:u.toLowerCase())==="none"||this._isVectorFieldResampleFunction(e)))return this.serviceRasterInfo;const n=wr(e);if(!n)return null;if(this._functionRasterInfos[n])return this._functionRasterInfos[n];const a=this._generateRasterInfo(e,t);this._functionRasterInfos[n]=a;try{return await a}catch{return this._functionRasterInfos[n]=null,null}}getExportImageServiceParameters(e,t,n,a){var h;e=e.clone().shiftCentralMeridian();const u=Ot(e.spatialReference,o(this.parsedUrl));this.pixelType!==this.serviceRasterInfo.pixelType&&(this.exportImageServiceParameters.pixelType=this.pixelType);const p=this.exportImageServiceParameters.toJSON(),{bandIds:m,noData:c}=p;let{renderingRule:d}=p;const y=(h=this.renderingRule)==null?void 0:h.rasterFunctionDefinition,w=!this.renderer||this.renderer.type==="raster-stretch";if(m!=null&&m.length&&this._hasRenderingRule(this.renderingRule)&&!y&&w){const g={rasterFunction:"ExtractBand",rasterFunctionArguments:{BandIds:m}};if(d.rasterFunction==="Stretch")g.rasterFunctionArguments.Raster=d.rasterFunctionArguments.Raster,d.rasterFunctionArguments.Raster=g;else if(d.rasterFunction==="Colormap"){const v=d.rasterFunctionArguments.Raster;(v==null?void 0:v.rasterFunction)==="Stretch"?(g.rasterFunctionArguments.Raster=v.rasterFunctionArguments.Raster,v.rasterFunctionArguments.Raster=g):(g.rasterFunctionArguments.Raster=v,d.rasterFunctionArguments.Raster=g)}else g.rasterFunctionArguments.Raster=d,d=g;p.bandIds=void 0}else p.bandIds=m==null?void 0:m.join(",");c instanceof Array&&c.length>0&&(p.noData=c.join(","));const f=this._processMultidimensionalIntersection(null,a,this.exportImageServiceParameters.mosaicRule);if(f.isOutSide)return null;p.mosaicRule=b(f.mosaicRule)?JSON.stringify(f.mosaicRule):null,a=f.timeExtent,p.renderingRule=this._getRenderingRuleString(S.fromJSON(d));const R={};if(b(a)){const{start:g,end:v}=a.toJSON();g&&v&&g===v?R.time=""+g:g==null&&v==null||(R.time=`${g??"null"},${v??"null"}`)}return{bbox:e.xmin+","+e.ymin+","+e.xmax+","+e.ymax,bboxSR:u,imageSR:u,size:t+","+n,...p,...R}}async getSamples(e,t){var a;if(!((a=await this._fetchCapabilities(t==null?void 0:t.signal))!=null&&a.operations.supportsGetSamples))throw new V("imagery-layer:get-samples","getSamples operation is not supported on the input image service");e=H(gr,e).clone();const{raster:n}=this;return n&&e.raster==null&&(e.raster=n),sr(this.url,e,this._getRequestOptions(t))}async identify(e,t){if(!(await this._fetchCapabilities(t==null?void 0:t.signal)).operations.supportsIdentify)throw new V("imagery-layer:identify","identify operation is not supported on the input image service");e=H(Rt,e).clone();const n=this._processMultidimensionalIntersection(e.geometry,e.timeExtent,e.mosaicRule||this.mosaicRule);if(n.isOutSide)throw new V("imagery-layer:identify","the request cannot be fulfilled when falling outside of the multidimensional subset");e.timeExtent=_e(n.timeExtent),e.mosaicRule=_e(n.mosaicRule);const{raster:a,renderingRule:u}=this;return u&&e.renderingRule==null&&(e.renderingRule=u),a&&e.raster==null&&(e.raster=a),yt(this.url,e,this._getRequestOptions(t))}createQuery(){const e=new Be;return e.outFields=["*"],e.returnGeometry=!0,e.where=this.definitionExpression||"1=1",e}async queryRasters(e,t){return{query:e,requestOptions:t}=await this._prepareForQuery(e,t),qt(this.url,e,t)}async queryObjectIds(e,t){return{query:e,requestOptions:t}=await this._prepareForQuery(e,t),Pi(this.url,e,t)}async queryRasterCount(e,t){return{query:e,requestOptions:t}=await this._prepareForQuery(e,t),Oi(this.url,e,t)}async queryVisibleRasters(e,t){var _,J,T,Z;if(!e)throw new V("imagery-layer: query-visible-rasters","missing query parameter");await this.load();const{pixelSize:n,returnDomainValues:a,returnTopmostRaster:u,showNoDataRecords:p}=t||{pixelSize:null,returnDomainValues:!1,returnTopmostRaster:!1,showNoDataRecords:!1};let m=!1,c=null,d=null;const y="raster.servicepixelvalue",w=this._rasterFunctionNamesIndex;if(b(e.outFields)&&(m=e.outFields.some(j=>!j.toLowerCase().includes(y)),this.version>=10.4)){const j=e.outFields.filter(z=>z.toLowerCase().includes(y)&&z.length>y.length).map(z=>{const X=z.slice(y.length+1);return[this._updateRenderingRulesFunctionName(X,w),X]});c=j.map(z=>new S({functionName:z[0]})),d=j.map(z=>z[1]);const{renderingRule:C}=this;c.length===0?C!=null&&C.functionName?(c.push(C),d.push(C.functionName)):c=null:C!=null&&C.functionName&&!c.some(z=>z.functionName===C.functionName)&&(c.push(C),d.push(C.functionName))}const f=G(e.outSpatialReference)||e.outSpatialReference.equals(this.spatialReference),{multidimensionalSubset:R}=this;let h=e.timeExtent||this.timeExtent;if(R){const{isOutside:j,intersection:C}=ke(R,{geometry:_e(e.geometry),timeExtent:_e(e.timeExtent),multidimensionalDefinition:(_=this.exportImageServiceParameters.mosaicRule)==null?void 0:_.multidimensionalDefinition});if(j)throw new V("imagery-layer:query-visible-rasters","the request cannot be fulfilled when falling outside of the multidimensional subset");C&&b(C.timeExtent)&&(h=C.timeExtent)}const g=this._combineMosaicRuleWithTimeExtent(this.exportImageServiceParameters.mosaicRule,h),v=this._getQueryParams({geometry:e.geometry,timeExtent:h,mosaicRule:g,renderingRule:this.version<10.4?this.renderingRule:null,renderingRules:c,pixelSize:n,returnCatalogItems:m,returnGeometry:f,raster:this.raster,maxItemCount:u?1:null});delete v.f;const x=new Rt(v);try{await this.generateRasterInfo(this.renderingRule);const j=await yt(this.url,x,{signal:t==null?void 0:t.signal,query:{...this.customParameters}}),C=e.outFields,z=j.value!=null&&j.value.toLowerCase().includes("nodata");if(!(m&&!f&&((J=j==null?void 0:j.catalogItems)!=null&&J.features.length)&&(p||!z)))return this._processVisibleRastersResponse(j,{returnDomainValues:a,templateRRFunctionNames:d,showNoDataRecords:p,templateFields:C});const X=this.objectIdField||"ObjectId",W=((T=j.catalogItems)==null?void 0:T.features)??[],ce=W.map(de=>{var ie;return(ie=de.attributes)==null?void 0:ie[X]}),Ce=new Be({objectIds:ce,returnGeometry:!0,outSpatialReference:e.outSpatialReference,outFields:[X]}),me=await this.queryRasters(Ce);return(Z=me==null?void 0:me.features)!=null&&Z.length&&me.features.forEach(de=>{W.forEach(ie=>{ie.attributes[X]===de.attributes[X]&&(ie.geometry=new Lt(de.geometry),b(e.outSpatialReference)&&(ie.geometry.spatialReference=e.outSpatialReference))})}),this._processVisibleRastersResponse(j,{returnDomainValues:a,templateRRFunctionNames:d,showNoDataRecords:p,templateFields:C})}catch{throw new V("imagery-layer:query-visible-rasters","encountered error when querying visible rasters")}}async fetchVariableStatisticsHistograms(e,t){const n=O(o(this.parsedUrl)+"/statistics",{query:this._getQueryParams({variable:e}),signal:t}).then(p=>{var m;return(m=p.data)==null?void 0:m.statistics}),a=O(o(this.parsedUrl)+"/histograms",{query:this._getQueryParams({variable:e}),signal:t}).then(p=>{var m;return(m=p.data)==null?void 0:m.histograms}),u=await Promise.all([n,a]);return u[0]&&u[0].forEach(p=>{p.avg=p.mean,p.stddev=p.standardDeviation}),{statistics:u[0]||null,histograms:u[1]||null}}async createFlowMesh(e,t){const n=this._rasterJobHandler.instance;return n?n.createFlowMesh(e,t):Ti(e.meshType,e.simulationSettings,e.flowData,b(t.signal)?t.signal:new AbortController().signal)}getMultidimensionalSubsetVariables(e){const t=e??this.serviceRasterInfo.multidimensionalInfo;return Ri(this.multidimensionalSubset,t)}async _fetchService(e){await this._fetchServiceInfo(e),this.rasterInfo||(this.rasterInfo=this.serviceRasterInfo);const t=this.sourceJSON,n=b(this.serviceRasterInfo)?Promise.resolve(this.serviceRasterInfo):ui(o(this.parsedUrl),t,{signal:e,query:this._getQueryParams()}).then(p=>(this._set("serviceRasterInfo",p),this._set("multidimensionalInfo",p.multidimensionalInfo),p)),a=this._hasRenderingRule(this.renderingRule)?this.generateRasterInfo(this.renderingRule,{signal:e}):null,u=this._getRasterFunctionInfos();return Promise.all([n,a,u]).then(p=>{p[1]?this._set("rasterInfo",p[1]):this._set("rasterInfo",p[0]),p[2]&&this._set("rasterFunctionInfos",p[2]),this.renderer&&!this._isSupportedRenderer(this.renderer)&&(this._set("renderer",null),mt.getLogger(this.declaredClass).warn("ArcGISImageService","Switching to the default renderer. Renderer applied is not valid for this Imagery Layer")),this._set("renderer",this._configRenderer(this.renderer)),this.addHandles([fi(()=>this.renderingRule,c=>{(this.renderer||this.symbolizer||this.popupEnabled&&this.popupTemplate)&&this.generateRasterInfo(c).then(d=>{d&&(this.rasterInfo=d)})})]);const{serviceRasterInfo:m}=this;b(m.multidimensionalInfo)&&this._updateMultidimensionalDefinition(m)})}_combineMosaicRuleWithTimeExtent(e,t){var R;const n=this.timeInfo,{multidimensionalInfo:a}=this.serviceRasterInfo;if(G(e)||G(a)||G(t)||G(n==null?void 0:n.startField))return e;const{startField:u}=n,p=a.variables.some(h=>h.dimensions.some(g=>g.name===u))?u:"StdTime";if(e=e.clone(),this.sourceType==="mosaic-dataset")return e.multidimensionalDefinition=(R=e.multidimensionalDefinition)==null?void 0:R.filter(h=>h.dimensionName!==p),this._cleanupMultidimensionalDefinition(e);e.multidimensionalDefinition=e.multidimensionalDefinition||[];const m=e.multidimensionalDefinition.filter(h=>h.dimensionName===p),c=b(t.start)?t.start.getTime():null,d=b(t.end)?t.end.getTime():null,y=c==null||d==null||c===d,w=y?[c||d]:[[c,d]],f=this.version>=10.8;if(m.length)m.forEach(h=>{h.dimensionName===p&&(f?(h.dimensionName=null,h.isSlice=!1,h.values=[]):(h.isSlice=y,h.values=w))});else if(!f){const h=e.multidimensionalDefinition.filter(g=>g.variableName!=null&&g.dimensionName==null);h.length?h.forEach(g=>{g.dimensionName=p,g.isSlice=y,g.values=w}):e.multidimensionalDefinition.push(new vt({variableName:"",dimensionName:p,isSlice:y,values:w}))}return this._cleanupMultidimensionalDefinition(e)}_cleanupMultidimensionalDefinition(e){return G(e)?null:(e.multidimensionalDefinition&&(e.multidimensionalDefinition=e.multidimensionalDefinition.filter(t=>!(!t.variableName&&!t.dimensionName)),e.multidimensionalDefinition.length===0&&(e.multidimensionalDefinition=null)),this.sourceType!=="mosaic-dataset"&&e.multidimensionalDefinition==null?null:e)}async _prepareForQuery(e,t){if(!(await this._fetchCapabilities(t==null?void 0:t.signal)).operations.supportsQuery)throw new V("imagery-layer:query-rasters","query operation is not supported on the input image service");return e=b(e)?H(Be,e):this.createQuery(),t=this._getRequestOptions(t),this.raster&&(t.query={...t.query,raster:this.raster}),{query:e,requestOptions:t}}async _initJobHandler(){if(this._rasterJobHandler.connectionPromise!=null)return this._rasterJobHandler.connectionPromise;const e=new yi;this._rasterJobHandler.connectionPromise=e.initialize().then(()=>{this._rasterJobHandler.instance=e},()=>{}),await this._rasterJobHandler.connectionPromise}_shutdownJobHandler(){this._rasterJobHandler.instance&&this._rasterJobHandler.instance.destroy(),this._rasterJobHandler.instance=null,this._rasterJobHandler.connectionPromise=null,this._rasterJobHandler.refCount=0,this._cachedRendererJson=null}_isSupportedRenderer(e){const{rasterInfo:t,renderingRule:n}=this;return e.type==="unique-value"&&this._hasRenderingRule(n)&&(t==null?void 0:t.bandCount)===1&&["u8","s8"].includes(t.pixelType)||t!=null&&e!=null&&pi(t).includes(e.type)}async _fetchCapabilities(e){return this.capabilities||await this._fetchServiceInfo(e),this.capabilities}async _fetchServiceInfo(e){var n;let t=this.sourceJSON;if(!t){const{data:a,ssl:u}=await O(o(this.parsedUrl),{query:this._getQueryParams(),signal:e});t=a,this.sourceJSON=t,u&&(this.url=this.url.replace(/^http:/i,"https:"))}if(((n=t.capabilities)==null?void 0:n.toLowerCase().split(",").map(a=>a.trim()).indexOf("tilesonly"))>-1)throw new V("imagery-layer:fetch-service-info","use ImageryTileLayer to open tiles-only image services");this.read(t,{origin:"service",url:this.parsedUrl})}_isMosaicDataset(e){var t;return e.serviceSourceType?e.serviceSourceType==="esriImageServiceSourceTypeMosaicDataset":((t=e.fields)==null?void 0:t.length)>0}_isMosaicRuleSupported(e){var a;if(!e)return!1;const t=this._isMosaicDataset(e),n=e.currentVersion>=10.71&&e.hasMultidimensions&&!(((a=e.fields)==null?void 0:a.length)>1);return t||n}_isVectorFieldResampleFunction(e){if(G(e))return!1;const{functionName:t,functionArguments:n}=e,a=(t==null?void 0:t.toLowerCase())==="resample",u=(n==null?void 0:n.ResampleType)||(n==null?void 0:n.resampleType);return a&&(u===7||u===10)}_isPicture(){return!this.format||this.format.includes("jpg")||this.format.includes("png")}_configRenderer(e){var a,u;const t=this._isPicture(),{rasterInfo:n}=this;if(!t&&!this.pixelFilter||this._isVectorDataSet()){if(!this.bandIds&&n.bandCount>=3){const m=ci(n);!m||n.bandCount===3&&m[0]===0&&m[1]===1&&m[2]===2||(this.bandIds=m)}e||(e=mi(n,{bandIds:this.bandIds,variableName:this.renderingRule?null:(u=(a=this.mosaicRule)==null?void 0:a.multidimensionalDefinition)==null?void 0:u[0].variableName}));const p=di(e.toJSON());this.symbolizer?(this.symbolizer.rendererJSON=p,this.symbolizer.rasterInfo=n):this.symbolizer=new Fi({rendererJSON:p,rasterInfo:n}),this.symbolizer.bind().success||(this.symbolizer=null)}return e}_clonePixelData(e){return e==null?e:{extent:e.extent&&e.extent.clone(),pixelBlock:b(e.pixelBlock)?e.pixelBlock.clone():null}}_getQueryParams(e){e&&b(e.renderingRule)&&typeof e.renderingRule!="string"&&(e.renderingRule=this._getRenderingRuleString(e.renderingRule));const{raster:t,viewId:n}=this;return{raster:t,viewId:n,f:"json",...e,...this.customParameters}}_getRequestOptions(e){return{...e,query:{...e==null?void 0:e.query,...this.customParameters}}}_decodePixelBlock(e,t,n){return this._rasterJobHandler.instance?this._rasterJobHandler.instance.decode({data:e,options:t}):dt(e,t,n)}async _getRasterFunctionInfos(e){var n;const t=this.sourceJSON.rasterFunctionInfos;return this.loaded?t:t&&this.version>=10.3?t.length===1&&t[0].name.toLowerCase()==="none"?t:(n=(await O(o(this.parsedUrl)+"/rasterFunctionInfos",{query:this._getQueryParams(),signal:e})).data)==null?void 0:n.rasterFunctionInfos:null}_canRequestImageElement(e){return!this.pixelFilter&&(!e||e.includes("png"))}async _requestArrayBuffer(e){const{imageProps:t,requestAsImageElement:n,returnImageBitmap:a,signal:u}=e;if(n&&this._canRequestImageElement(t.format)){const R=`${o(this.parsedUrl)}/exportImage`,{data:h}=await O(R,{responseType:a?"blob":"image",query:this._getQueryParams({f:"image",...this.refreshParameters,...e.imageServiceParameters}),signal:u});return h instanceof Blob?{imageBitmap:await ht(h,R),params:t}:{imageOrCanvasElement:h,params:t}}const p=this._initJobHandler(),m=O(o(this.parsedUrl)+"/exportImage",{responseType:"array-buffer",query:this._getQueryParams({f:"image",...e.imageServiceParameters}),signal:u}),c=(await Promise.all([m,p]))[0].data,d=t.format||"jpgpng";let y=d;if(y!=="bsq"&&y!=="bip"&&(y=Di(c)),!y)throw new V("imagery-layer:fetch-image","unsupported format signature "+String.fromCharCode.apply(null,new Uint8Array(c)));const w={signal:u};return{pixelData:{pixelBlock:await(d==="gif"||d==="bmp"||d.includes("png")&&(y==="png"||y==="jpg")?dt(c,{useCanvas:!0,...t},w):this._decodePixelBlock(c,{width:t.width,height:t.height,planes:null,pixelType:null,noDataValue:null,format:d},w)),extent:t.extent},params:t}}_generateRasterInfo(e,t){const n={...t,query:this._getQueryParams()};return hi(o(this.parsedUrl),e,n)}_isValidCustomizedMosaicRule(e){var t;return e&&JSON.stringify(e.toJSON())!==JSON.stringify((t=this.defaultMosaicRule)==null?void 0:t.toJSON())}_updateMultidimensionalDefinition(e){var n;if(this._isValidCustomizedMosaicRule(this.mosaicRule))return;let t=wi(e,{multidimensionalSubset:this.multidimensionalSubset});if(b(t)&&t.length>0){this.mosaicRule=this.mosaicRule||new U;const a=this.mosaicRule.multidimensionalDefinition;!this.sourceJSON.defaultVariableName&&this.renderingRule&&((n=this.renderingRule.functionName)==null?void 0:n.toLowerCase())!=="none"&&t.forEach(u=>u.variableName=""),t=t.filter(({variableName:u,dimensionName:p})=>u&&u!=="*"||p),!(a!=null&&a.length)&&t.length&&(this.mosaicRule.multidimensionalDefinition=t)}}_processVisibleRastersResponse(e,t){t=t||{};const n=e.value,{templateRRFunctionNames:a,showNoDataRecords:u,returnDomainValues:p,templateFields:m}=t,c=e.processedValues;let d=e.catalogItems&&e.catalogItems.features,y=e.properties&&e.properties.Values&&e.properties.Values.map(x=>x.replace(/ /gi,", "))||[];const w=this.objectIdField||"ObjectId",f=typeof n=="string"&&n.toLowerCase().includes("nodata"),R=[];if(n&&!d&&!f){const x={};x[w]=0,y=[n],d=[new Gt(this.fullExtent,null,x)]}if(!d)return[];let h,g,v;this._updateResponseFieldNames(d,m),f&&!u&&(d=[]);for(let x=0;x<d.length;x++){if(h=d[x],n!=null){if(g=y[x],v=this.renderingRule&&c&&c.length>0&&a&&a.length>0&&a.includes(this.renderingRule.functionName)?c[a.indexOf(this.renderingRule.functionName)]:n,g.toLowerCase()==="nodata"&&!u)continue;const _="Raster.ItemPixelValue",J="Raster.ServicePixelValue";h.attributes[_]=g,h.attributes[J]=v,this._updateFeatureWithMagDirValues(h,g);const T=this.fields&&this.fields.length>0;let Z=this.renderingRule&&b(this.serviceRasterInfo.attributeTable)?T?g:n:v;this.renderingRule||(Z=T?g:n),this._updateFeatureWithRasterAttributeTableValues(h,Z)}if(h.sourceLayer=h.layer=this,p&&this._updateFeatureWithDomainValues(h),a&&c&&a.length===c.length)for(let _=0;_<a.length;_++){const J="Raster.ServicePixelValue."+a[_];h.attributes[J]=c[_]}R.push(d[x])}return R}_processMultidimensionalIntersection(e,t,n){const{multidimensionalSubset:a}=this;if(!a)return{isOutSide:!1,timeExtent:t,mosaicRule:n=this._combineMosaicRuleWithTimeExtent(n,t)};if(a){const{isOutside:u,intersection:p}=ke(a,{geometry:e,timeExtent:t});if(u)return{isOutSide:!0,timeExtent:null,mosaicRule:null};p&&b(p.timeExtent)&&(t=p.timeExtent)}if(n=this._combineMosaicRuleWithTimeExtent(n,t),b(n)&&n.multidimensionalDefinition){const{isOutside:u}=ke(a,{multidimensionalDefinition:n.multidimensionalDefinition});if(u)return{isOutSide:!0,timeExtent:null,mosaicRule:null}}return{isOutSide:!1,timeExtent:t,mosaicRule:n}}_updateFeatureWithRasterAttributeTableValues(e,t){const n=this.rasterInfo.attributeTable||this.serviceRasterInfo.attributeTable;if(G(n))return;const{features:a,fields:u}=n,p=u.map(d=>d.name).filter(d=>d.toLowerCase()==="value"),m=p&&p[0];if(!m)return;const c=a.filter(d=>d.attributes[m]===(t!=null?parseInt(t,10):null));c&&c[0]&&u.forEach(d=>{const y=this._rasterAttributeTableFieldPrefix+d.name;e.attributes[y]=c[0].attributes[d.name]})}_updateFeatureWithMagDirValues(e,t){if(!this._isVectorDataSet())return;const n=t.split(/,\s*/).map(c=>parseFloat(c)),a=n.map(c=>[c]),u=n.map(c=>({minValue:c,maxValue:c,noDataValue:null})),p=new Qe({height:1,width:1,pixelType:"f32",pixels:a,statistics:u});this.pixelFilter!=null&&this.pixelFilter({pixelBlock:p,extent:new je(0,0,0,0,this.spatialReference)});const m=this.serviceDataType==="esriImageServiceDataTypeVector-MagDir"?[p.pixels[0][0],p.pixels[1][0]]:Ni([p.pixels[0][0],p.pixels[1][0]]);e.attributes["Raster.Magnitude"]=m[0],e.attributes["Raster.Direction"]=m[1]}_updateFeatureWithDomainValues(e){const t=this.fields&&this.fields.filter(n=>n.domain&&n.domain.type==="coded-value");t!=null&&t.forEach(n=>{const a=e.attributes[n.name];if(a!=null){const u=n.domain.codedValues.find(p=>p.code===a);u&&(e.attributes[n.name]=u.name)}})}_updateResponseFieldNames(e,t){if(!t||t.length<1)return;const n=this.fieldsIndex;G(n)||e.forEach(a=>{var u;if(a&&a.attributes)for(const p of t){const m=(u=n.get(p))==null?void 0:u.name;m&&m!==p&&(a.attributes[p]=a.attributes[m],delete a.attributes[m])}})}_getRenderingRuleString(e){if(e){let t=e.toJSON();return t=t.rasterFunctionDefinition??t,(t.thumbnail||t.thumbnailEx)&&(t.thumbnail=t.thumbnailEx=null),JSON.stringify(t)}return null}_hasRenderingRule(e){return e!=null&&e.functionName!=null&&e.functionName.toLowerCase()!=="none"}_updateRenderingRulesFunctionName(e,t){if(!e||e.length<1)return;if(e==="Raw")return e.replace("Raw","None");const n=e.toLowerCase().replace(/ /gi,"_");return t.has(n)?t.get(n):e}_isRFTJson(e){return e&&e.name&&e.arguments&&e.function&&e.hasOwnProperty("functionType")}_isVectorDataSet(){return this.serviceDataType==="esriImageServiceDataTypeVector-UV"||this.serviceDataType==="esriImageServiceDataTypeVector-MagDir"}_applyMosaicAndRenderingRules(e){const{raster:t,mosaicRule:n,renderingRule:a}=this;a&&e.renderingRule==null&&(e.renderingRule=a),n&&e.mosaicRule==null&&(e.mosaicRule=n),t&&e.raster==null&&(e.raster=t)}_readCapabilities(e){const t=e.capabilities?e.capabilities.toLowerCase().split(",").map(R=>R.trim()):["image","catalog"],{currentVersion:n,advancedQueryCapabilities:a,maxRecordCount:u}=e,p=t.includes("image"),m=e.serviceDataType==="esriImageServiceDataTypeElevation",c=!!(e.spatialReference||e.extent&&e.extent.spatialReference),d=t.includes("edit"),y=t.includes("mensuration")&&c,w=e.mensurationCapabilities==null?[]:e.mensurationCapabilities.toLowerCase().split(",").map(R=>R.trim()),f=y&&w.includes("basic");return{data:{supportsAttachment:!1},operations:{supportsComputeHistograms:p,supportsExportImage:p,supportsIdentify:p,supportsMeasure:y,supportsDownload:t.includes("download"),supportsQuery:t.includes("catalog")&&e.fields&&e.fields.length>0,supportsGetSamples:n>=10.2&&p,supportsProject:n>=10.3&&p,supportsComputeStatisticsHistograms:n>=10.4&&p,supportsQueryBoundary:n>=10.6&&p,supportsCalculateVolume:n>=10.7&&m,supportsComputePixelLocation:n>=10.7&&t.includes("catalog"),supportsComputeAngles:n>=10.91,supportsAdd:d,supportsDelete:d,supportsEditing:d,supportsUpdate:d,supportsCalculate:!1,supportsTruncate:!1,supportsValidateSql:!1,supportsChangeTracking:!1,supportsQueryAttachments:!1,supportsResizeAttachments:!1,supportsSync:!1,supportsExceedsLimitStatistics:!1,supportsQueryAnalytics:!1,supportsQueryTopFeatures:!1},query:{maxRecordCount:u,maxRecordCountFactor:void 0,supportsStatistics:!!(a!=null&&a.supportsStatistics),supportsOrderBy:!!(a!=null&&a.supportsOrderBy),supportsDistinct:!!(a!=null&&a.supportsDistinct),supportsPagination:!!(a!=null&&a.supportsPagination),supportsStandardizedQueriesOnly:!!(a!=null&&a.useStandardizedQueries),supportsPercentileStatistics:!!(a!=null&&a.supportsPercentileStatistics),supportsCentroid:!!(a!=null&&a.supportsReturningGeometryCentroid),supportsDistance:!!(a!=null&&a.supportsQueryWithDistance),supportsExtent:!!(a!=null&&a.supportsReturningQueryExtent),supportsGeometryProperties:!!(a!=null&&a.supportsReturningGeometryProperties),supportsHavingClause:!!(a!=null&&a.supportsHavingClause),supportsQuantization:!1,supportsQuantizationEditMode:!1,supportsQueryGeometry:!1,supportsResultType:!1,supportsMaxRecordCountFactor:!1,supportsSqlExpression:!1,supportsTopFeaturesQuery:!1,supportsQueryByOthers:!1,supportsHistoricMoment:!1,supportsFormatPBF:!1,supportsDisjointSpatialRelationship:!1,supportsCacheHint:!1,supportsSpatialAggregationStatistics:!1,supportedSpatialAggregationStatistics:{envelope:!1,centroid:!1,convexHull:!1},supportsDefaultSpatialReference:!!(a!=null&&a.supportsDefaultSR),supportsFullTextSearch:!1,supportsCompactGeometry:!1,standardMaxRecordCount:void 0,tileMaxRecordCount:void 0},mensuration:{supportsDistanceAndAngle:f,supportsAreaAndPerimeter:f,supportsPointOrCentroid:f,supportsHeightFromBaseAndTop:y&&w.includes("base-top height"),supportsHeightFromBaseAndTopShadow:y&&w.includes("base-top shadow height"),supportsHeightFromTopAndTopShadow:y&&w.includes("top-top shadow height"),supports3D:y&&w.includes("3d")}}}};function o(e){return(e==null?void 0:e.path)??""}return r([l()],i.prototype,"_functionRasterInfos",void 0),r([l()],i.prototype,"_rasterJobHandler",void 0),r([l()],i.prototype,"_cachedRendererJson",void 0),r([l({readOnly:!0})],i.prototype,"_serviceSupportsMosaicRule",void 0),r([A("_serviceSupportsMosaicRule",["currentVersion","fields"])],i.prototype,"readServiceSupportsMosaicRule",null),r([l()],i.prototype,"_rasterAttributeTableFieldPrefix",void 0),r([l({readOnly:!0})],i.prototype,"_rasterFunctionNamesIndex",null),r([l()],i.prototype,"adjustAspectRatio",void 0),r([l({type:[Fe],json:{write:!0}})],i.prototype,"bandIds",void 0),r([A("bandIds")],i.prototype,"readBandIds",null),r([l({readOnly:!0,json:{read:!1}})],i.prototype,"capabilities",void 0),r([A("service","capabilities",["capabilities","currentVersion","serviceDataType"])],i.prototype,"readCapabilities",null),r([l({type:Number})],i.prototype,"compressionQuality",void 0),r([D("compressionQuality")],i.prototype,"writeCompressionQuality",null),r([l({type:Number})],i.prototype,"compressionTolerance",void 0),r([D("compressionTolerance")],i.prototype,"writeCompressionTolerance",null),r([l({json:{read:{source:"copyrightText"}}})],i.prototype,"copyright",void 0),r([l({readOnly:!0,dependsOn:["_serviceSupportsMosaicRule"]})],i.prototype,"defaultMosaicRule",void 0),r([A("defaultMosaicRule",["defaultMosaicMethod"])],i.prototype,"readDefaultMosaicRule",null),r([l({type:String,json:{name:"layerDefinition.definitionExpression",write:{enabled:!0,allowNull:!0}}})],i.prototype,"definitionExpression",void 0),r([l({readOnly:!0,constructOnly:!0})],i.prototype,"exportImageServiceParameters",void 0),r([l()],i.prototype,"rasterInfo",void 0),r([l({readOnly:!0,type:[he]})],i.prototype,"fields",void 0),r([l({readOnly:!0})],i.prototype,"fieldsIndex",null),r([l({type:["png","png8","png24","png32","jpg","bmp","gif","jpgpng","lerc","tiff"],json:{write:!0}})],i.prototype,"format",null),r([A("service","format",["serviceDataType"])],i.prototype,"readFormat",null),r([l({type:je})],i.prototype,"fullExtent",void 0),r([l({readOnly:!0})],i.prototype,"hasMultidimensions",void 0),r([l({json:{read:{source:"maxImageHeight"}}})],i.prototype,"imageMaxHeight",void 0),r([l({json:{read:{source:"maxImageWidth"}}})],i.prototype,"imageMaxWidth",void 0),r([l({type:String,json:{type:ue.jsonValues,read:ue.read,write:ue.write}})],i.prototype,"interpolation",void 0),r([l()],i.prototype,"minScale",void 0),r([A("service","minScale")],i.prototype,"readMinScale",null),r([l()],i.prototype,"maxScale",void 0),r([A("service","maxScale")],i.prototype,"readMaxScale",null),r([l({type:U})],i.prototype,"mosaicRule",null),r([A("mosaicRule",["mosaicRule","defaultMosaicMethod"])],i.prototype,"readMosaicRule",null),r([D("mosaicRule")],i.prototype,"writeMosaicRule",null),r([l()],i.prototype,"multidimensionalInfo",void 0),r([l({type:ni,json:{write:!0}})],i.prototype,"multidimensionalSubset",void 0),r([l({json:{type:Fe}})],i.prototype,"noData",void 0),r([D("noData")],i.prototype,"writeNoData",null),r([l({type:String,json:{type:Ne.jsonValues,read:Ne.read,write:Ne.write}})],i.prototype,"noDataInterpretation",void 0),r([l({type:String,readOnly:!0,json:{read:{source:["fields"]}}})],i.prototype,"objectIdField",void 0),r([A("objectIdField")],i.prototype,"readObjectIdField",null),r([l({})],i.prototype,"geometryType",void 0),r([l({})],i.prototype,"typeIdField",void 0),r([l({})],i.prototype,"types",void 0),r([l({readOnly:!0})],i.prototype,"parsedUrl",null),r([l({type:Function})],i.prototype,"pixelFilter",void 0),r([l()],i.prototype,"raster",void 0),r([l({readOnly:!0})],i.prototype,"sourceType",void 0),r([A("sourceType",["serviceSourceType","fields"])],i.prototype,"readSourceType",null),r([l()],i.prototype,"viewId",void 0),r([l({types:ai,json:{name:"layerDefinition.drawingInfo.renderer",origins:{"web-scene":{types:oi,name:"layerDefinition.drawingInfo.renderer",write:{overridePolicy:e=>({enabled:e&&e.type!=="vector-field"&&e.type!=="flow"})}}}}})],i.prototype,"renderer",null),r([A("renderer")],i.prototype,"readRenderer",null),r([D("renderer")],i.prototype,"writeRenderer",null),r([l()],i.prototype,"symbolizer",void 0),r([l(Jt)],i.prototype,"opacity",void 0),r([l({readOnly:!0})],i.prototype,"rasterFields",null),r([l({constructOnly:!0})],i.prototype,"rasterFunctionInfos",void 0),r([l({type:S})],i.prototype,"renderingRule",null),r([A("renderingRule",["renderingRule","rasterFunctionInfos"])],i.prototype,"readRenderingRule",null),r([D("renderingRule")],i.prototype,"writeRenderingRule",null),r([l()],i.prototype,"serviceDataType",void 0),r([l({readOnly:!0,type:fe})],i.prototype,"spatialReference",void 0),r([A("spatialReference",["spatialReference","extent"])],i.prototype,"readSpatialReference",null),r([l({json:{type:Ke.jsonValues}})],i.prototype,"pixelType",void 0),r([A("pixelType")],i.prototype,"readPixelType",null),r([D("pixelType")],i.prototype,"writePixelType",null),r([l({constructOnly:!0,type:xt})],i.prototype,"serviceRasterInfo",void 0),r([l()],i.prototype,"sourceJSON",void 0),r([l(Et)],i.prototype,"url",void 0),r([l({readOnly:!0})],i.prototype,"version",void 0),r([A("version",["currentVersion","fields","timeInfo"])],i.prototype,"readVersion",null),i=r([F("esri.layers.mixins.ArcGISImageService")],i),i};let B=class extends Ut(Ht(Bt(kt(Qt(vr(Wt(Kt(Zt(Xt(si)))))))))){constructor(...s){super(...s),this.legendEnabled=!0,this.isReference=null,this.operationalLayerType="ArcGISImageServiceLayer",this.popupEnabled=!0,this.popupTemplate=null,this.type="imagery"}normalizeCtorArgs(s,i){return typeof s=="string"?{url:s,...i}:s}load(s){const i=b(s)?s.signal:null;return this.addResolvingPromise(this.loadFromPortal({supportedTypes:["Image Service"]},s).catch(Mt).then(()=>this._fetchService(i))),Promise.resolve(this)}writeOperationalLayerType(s,i,o){var t;const e=((t=this.renderer)==null?void 0:t.type)==="vector-field";i[o]=e?"ArcGISImageServiceVectorLayer":"ArcGISImageServiceLayer"}get defaultPopupTemplate(){return this.createPopupTemplate()}createPopupTemplate(s){const i=this.rasterFields,o=this.title,e=new Set;let t=!1,n=!1;this.capabilities&&(t=this.capabilities.operations.supportsQuery&&this.fields&&this.fields.length>0,n=this.serviceDataType==="esriImageServiceDataTypeVector-UV"||this.serviceDataType==="esriImageServiceDataTypeVector-MagDir");const a=new Set;t&&a.add("raster.itempixelvalue");for(const u of i){const p=u.name.toLowerCase();a.has(p)||p.includes("raster.servicepixelvalue.")||e.add(u.name)}return n&&e.add("raster.magnitude").add("raster.direction"),Yt({fields:i,title:o},{...s,visibleFieldNames:e})}queryFeatures(s,i){return this.queryRasters(s,i).then(o=>{if(o!=null&&o.features)for(const e of o.features)e.layer=e.sourceLayer=this;return o})}queryFeatureCount(s,i){return this.queryRasterCount(s,i)}redraw(){this.emit("redraw")}serviceSupportsSpatialReference(s){return ei(this,s)}};r([l(ti)],B.prototype,"legendEnabled",void 0),r([l({type:["show","hide"]})],B.prototype,"listMode",void 0),r([l({type:Boolean,json:{read:!1,write:{enabled:!0,overridePolicy:()=>({enabled:!1})}}})],B.prototype,"isReference",void 0),r([l({type:["ArcGISImageServiceLayer"],json:{origins:{"web-map":{type:["ArcGISImageServiceLayer","ArcGISImageServiceVectorLayer"],read:!1,write:{target:"layerType",ignoreOrigin:!0}}}}})],B.prototype,"operationalLayerType",void 0),r([D("web-map","operationalLayerType")],B.prototype,"writeOperationalLayerType",null),r([l(ii)],B.prototype,"popupEnabled",void 0),r([l({type:ri,json:{read:{source:"popupInfo"},write:{target:"popupInfo"}}})],B.prototype,"popupTemplate",void 0),r([l({readOnly:!0})],B.prototype,"defaultPopupTemplate",null),r([l({readOnly:!0,json:{read:!1}})],B.prototype,"type",void 0),B=r([F("esri.layers.ImageryLayer")],B);const Qr=B;export{Qr as default};
