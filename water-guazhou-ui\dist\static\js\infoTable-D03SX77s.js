import{C as r,g as l,n as c,q as t,F as h,N as d,O as g,P as p}from"./index-r0dFAfgr.js";const u={name:"InfoTable",props:["dialogInfo"],data(){return{currentPage:1,showList:[],recordList:[]}},computed:{total(){return this.recordList.length},visible(){return this.dialogInfo.visible}},created(){this.dialogInfo.isStatistics?(this.showList=this.dialogInfo.row.recordList.slice(0,[9]),this.recordList=this.dialogInfo.row.recordList):(this.showList=this.dialogInfo.row.info.slice(0,[9]),this.recordList=this.dialogInfo.row.info)},methods:{handleCurrentChange(e){this.currentPage=e,this.showList=this.recordList.slice(e*10-10,[e*10])}}},f={class:"info-table-dialog"};function _(e,m,b,L,s,a){const o=d,i=g,n=p;return l(),c("div",f,[t(i,{height:"470",data:s.showList},{default:h(()=>[t(o,{prop:"time",label:"触发时间",width:"180"}),t(o,{prop:"status",label:"触发状态"}),t(o,{prop:"infoValue",label:"告警详细信息"})]),_:1},8,["data"]),t(n,{"page-size":10,"current-page":s.currentPage,total:a.total,layout:"total, prev, pager, next",onCurrentChange:a.handleCurrentChange},null,8,["current-page","total","onCurrentChange"])])}const C=r(u,[["render",_]]);export{C as default};
