<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartService.portal.SsPortalPolicyMapper">
    <sql id="Base_Column_List">
        <!--@sql select-->
        id,
        name,
        icon,
        effect_icon,
        create_time,
        tenant_id
        <!--@sql from ss_portal_policy -->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.smartService.portal.SsPortalPolicy">
        <result column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="icon" property="icon"/>
        <result column="effect_icon" property="effectIcon"/>
        <result column="create_time" property="createTime"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>
    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ss_portal_policy
        <where>
            <if test="name != null and name != ''">
                and name like '%'|| #{name} ||'%'
            </if>
            <if test="fromTime != null">
                and create_time >= #{fromTime}
            </if>
            <if test="toTime != null">
                and create_time &lt;= #{toTime}
            </if>
            and tenant_id = #{tenantId}
        </where>
        order by create_time desc
    </select>

    <update id="updateFully">
        update ss_portal_policy
        set name        = #{name},
            icon        = #{icon},
            effect_icon = #{effectIcon}
        where id = #{id}
    </update>

    <select id="canBeDelete" resultType="boolean">
        select count(1) = 0
        from ss_portal_policy_content
        where type_id = #{id}
    </select>
</mapper>