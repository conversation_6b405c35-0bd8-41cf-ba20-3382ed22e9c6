declare global {
  type ITheme = '' | 'darkblue' | 'dark' | undefined;
  export type IAdditionalFiltersIns = InstanceType<
    (typeof import('@/components/additionalFilters/index.vue'))['default']
  >;
  export type IAddOrUpdateDialogIns = InstanceType<
    (typeof import('@/components/addOrUpdateDialog/index.vue'))['default']
  >;
  export type IArcAreaMeasureIns = InstanceType<
    (typeof import('@/components/arcMap/widgets/ArcAreaMeasure.vue'))['default']
  >;
  export type IArcBasemapGallaryIns = InstanceType<
    (typeof import('@/components/arcMap/widgets/ArcBasemapGallary.vue'))['default']
  >;
  export type IArcCoordinateIns = InstanceType<
    (typeof import('@/components/arcMap/widgets/ArcCoordinate.vue'))['default']
  >;
  export type IArcDrawIns = InstanceType<
    (typeof import('@/components/arcMap/widgets/ArcDraw.vue'))['default']
  >;
  export type IArcHomeIns = InstanceType<
    (typeof import('@/components/arcMap/widgets/ArcHome.vue'))['default']
  >;
  export type IArcLayerListIns = InstanceType<
    (typeof import('@/components/arcMap/widgets/ArcLayerList.vue'))['default']
  >;
  export type IArcLayoutIns = InstanceType<
    (typeof import('@/components/arcMap/widgets/ArcLayout.vue'))['default']
  >;
  export type IArcLegendIns = InstanceType<
    (typeof import('@/components/arcMap/widgets/ArcLegend.vue'))['default']
  >;
  export type IArcLengthMeasureIns = InstanceType<
    (typeof import('@/components/arcMap/widgets/ArcLengthMeasure.vue'))['default']
  >;
  export type IArcMapIns = InstanceType<
    (typeof import('@/components/arcMap/arcMap.vue'))['default']
  >;
  export type IArcOverviewIns = InstanceType<
    (typeof import('@/components/arcMap/widgets/ArcOverview.vue'))['default']
  >;
  export type IArcPipeIns = InstanceType<
    (typeof import('@/components/arcMap/widgets/ArcPipe.vue'))['default']
  >;
  export type IArcPipeBarIns = InstanceType<
    (typeof import('@/components/arcMap/widgets/ArcPipeBar.vue'))['default']
  >;
  export type IArcPipePickIns = InstanceType<
    (typeof import('@/components/arcMap/widgets/ArcPipePick.vue'))['default']
  >;
  export type IArcPoiIns = InstanceType<
    (typeof import('@/components/arcMap/widgets/ArcPoi.vue'))['default']
  >;
  export type IArcPopIns = InstanceType<
    (typeof import('@/components/arcMap/widgets/ArcPop.vue'))['default']
  >;
  export type IArcPopsIns = InstanceType<
    (typeof import('@/components/arcMap/widgets/ArcPops.vue'))['default']
  >;
  export type IArcPrintIns = InstanceType<
    (typeof import('@/components/arcMap/widgets/ArcPrint.vue'))['default']
  >;
  export type IArcScaleIns = InstanceType<
    (typeof import('@/components/arcMap/widgets/ArcScale.vue'))['default']
  >;
  export type IArcSqlGeneratorIns = InstanceType<
    (typeof import('@/components/arcMap/widgets/ArcSqlGenerator.vue'))['default']
  >;
  export type IArcSqlTableIns = InstanceType<
    (typeof import('@/components/arcMap/widgets/ArcSqlTable.vue'))['default']
  >;
  export type IArcStationWarningIns = InstanceType<
    (typeof import('@/components/arcMap/widgets/ArcStationWarning.vue'))['default']
  >;
  export type IArcViewIns = InstanceType<
    (typeof import('@/components/arcMap/ArcView.vue'))['default']
  >;
  export type IArcWidgetButtonIns = InstanceType<
    (typeof import('@/components/arcMap/arcWidgetButton.vue'))['default']
  >;
  export type IArcZoomIns = InstanceType<
    (typeof import('@/components/arcMap/widgets/ArcZoom.vue'))['default']
  >;
  export type IArcZoomToIns = InstanceType<
    (typeof import('@/components/arcMap/widgets/ArcZoomTo.vue'))['default']
  >;
  export type IAreaMeasureIns = InstanceType<
    (typeof import('@/components/arcMap/AreaMeasure.vue'))['default']
  >;
  export type IAttrTableIns = InstanceType<
    (typeof import('@/components/Form/AttrTable.vue'))['default']
  >;
  export type IAttrTableCellContentIns = InstanceType<
    (typeof import('@/components/Form/AttrTableCellContent.vue'))['default']
  >;
  export type IAvatarUploaderIns = InstanceType<
    (typeof import('@/components/Form/AvatarUploader.vue'))['default']
  >;
  export type IBreadcrumbIns = InstanceType<
    (typeof import('@/components/Breadcrumb/index.vue'))['default']
  >;
  export type IButtonIns = InstanceType<
    (typeof import('@/components/Form/Button.vue'))['default']
  >;
  export type ICardIns = InstanceType<
    (typeof import('@/components/SLCard/Card.vue'))['default']
  >;
  export type ICardFineReportIns = InstanceType<
    (typeof import('@/components/cardFineReport/index.vue'))['default']
  >;
  export type ICardSearchIns = InstanceType<
    (typeof import('@/components/Form/CardSearch.vue'))['default']
  >;
  export type ICardTableIns = InstanceType<
    (typeof import('@/components/Form/CardTable.vue'))['default']
  >;
  export type IChooseUserByRoleIns = InstanceType<
    (typeof import('@/components/chooseUserByRole/index.vue'))['default']
  >;
  export type IColorPickerIns = InstanceType<
    (typeof import('@/components/Form/ColorPicker.vue'))['default']
  >;
  export type IcopyIns = InstanceType<
    (typeof import('@/components/Form/Search copy.vue'))['default']
  >;
  export type IDataPointFilterIns = InstanceType<
    (typeof import('@/components/additionalFilters/components/dataPointFilter.vue'))['default']
  >;
  export type IDescriptionsIns = InstanceType<
    (typeof import('@/components/Descriptions/index.vue'))['default']
  >;
  export type IDialogFormIns = InstanceType<
    (typeof import('@/components/Form/DialogForm.vue'))['default']
  >;
  export type IDPlayerIns = InstanceType<
    (typeof import('@/components/videoPlayer/DPlayer.vue'))['default']
  >;
  export type IDraggableFormItemIns = InstanceType<
    (typeof import('@/components/Form/DraggableFormItem.vue'))['default']
  >;
  export type IDrawerBoxIns = InstanceType<
    (typeof import('@/components/DrawerBox/DrawerBox.vue'))['default']
  >;
  export type IFieldSetIns = InstanceType<
    (typeof import('@/components/Form/FieldSet.vue'))['default']
  >;
  export type IFieldSetV1Ins = InstanceType<
    (typeof import('@/components/Form/FieldSetV1.vue'))['default']
  >;
  export type IFolderBtnIns = InstanceType<
    (typeof import('@/components/DrawerBox/components/FolderBtn.vue'))['default']
  >;
  export type IFormIns = InstanceType<
    (typeof import('@/components/Form/Form.vue'))['default']
  >;
  export type IFormItemIns = InstanceType<
    (typeof import('@/components/Form/FormItem.vue'))['default']
  >;
  export type IFormMapIns = InstanceType<
    (typeof import('@/components/arcMap/FormMap.vue'))['default']
  >;
  export type IFormTableIns = InstanceType<
    (typeof import('@/components/Form/FormTable.vue'))['default']
  >;
  export type IFormTableColumnIns = InstanceType<
    (typeof import('@/components/Form/FormTableColumn.vue'))['default']
  >;
  export type IFormTableColumnFilterIns = InstanceType<
    (typeof import('@/components/Form/FormTableColumnFilter.vue'))['default']
  >;
  export type IFormTreeIns = InstanceType<
    (typeof import('@/components/Form/FormTree.vue'))['default']
  >;
  export type IFormWangeditorIns = InstanceType<
    (typeof import('@/components/Form/FormWangeditor.vue'))['default']
  >;
  export type IFullScreenIns = InstanceType<
    (typeof import('@/components/FullScreen/index.vue'))['default']
  >;
  export type IHamburgerIns = InstanceType<
    (typeof import('@/components/Hamburger/index.vue'))['default']
  >;
  export type IIconSelectorIns = InstanceType<
    (typeof import('@/components/Form/IconSelector.vue'))['default']
  >;
  export type IImgViewerIns = InstanceType<
    (typeof import('@/components/Form/ImgViewer.vue'))['default']
  >;
  export type IImportBtnIns = InstanceType<
    (typeof import('@/components/importBtn/index.vue'))['default']
  >;
  export type IImportButtonIns = InstanceType<
    (typeof import('@/components/Form/ImportButton.vue'))['default']
  >;
  export type IImportButtonSuccessIns = InstanceType<
    (typeof import('@/components/Form/importButtonSuccess.vue'))['default']
  >;
  export type IImportJsonButtonIns = InstanceType<
    (typeof import('@/components/Form/ImportJsonButton.vue'))['default']
  >;
  export type IInlineFormIns = InstanceType<
    (typeof import('@/components/Form/InlineForm.vue'))['default']
  >;
  export type IInputIns = InstanceType<
    (typeof import('@/components/Form/Input.vue'))['default']
  >;
  export type IIntervalFilterIns = InstanceType<
    (typeof import('@/components/additionalFilters/components/intervalFilter.vue'))['default']
  >;
  export type ILinearTextIns = InstanceType<
    (typeof import('@/components/LinearText/index.vue'))['default']
  >;
  export type IListIns = InstanceType<
    (typeof import('@/components/Form/List.vue'))['default']
  >;
  export type ILivePlayerIns = InstanceType<
    (typeof import('@/components/videoPlayer/LivePlayer.vue'))['default']
  >;
  export type IMapIns = InstanceType<
    (typeof import('@/components/arcMap/Map.vue'))['default']
  >;
  export type IMenuItemIns = InstanceType<
    (typeof import('@/components/MenuItem/MenuItem.vue'))['default']
  >;
  export type INestedDraggableFormGroupIns = InstanceType<
    (typeof import('@/components/Form/NestedDraggableFormGroup.vue'))['default']
  >;
  export type IOrillusion3DIns = InstanceType<
    (typeof import('@/components/Orillusion3D/index.vue'))['default']
  >;
  export type IPaginationIns = InstanceType<
    (typeof import('@/components/Form/Pagination.vue'))['default']
  >;
  export type IPanelIns = InstanceType<
    (typeof import('@/components/Panel/Panel.vue'))['default']
  >;
  export type IPipeLengthIns = InstanceType<
    (typeof import('@/components/arcMap/PipeLength.vue'))['default']
  >;
  export type IPoiSearchIns = InstanceType<
    (typeof import('@/components/arcMap/PoiSearch.vue'))['default']
  >;
  export type IPoiSearchV2Ins = InstanceType<
    (typeof import('@/components/arcMap/PoiSearchV2.vue'))['default']
  >;
  export type IPopLayoutIns = InstanceType<
    (typeof import('@/components/arcMap/PopLayout.vue'))['default']
  >;
  export type IPopWindowIns = InstanceType<
    (typeof import('@/components/arcMap/popWindow.vue'))['default']
  >;
  export type IRangeSelecterIns = InstanceType<
    (typeof import('@/components/Form/RangeSelecter.vue'))['default']
  >;
  export type ISearchIns = InstanceType<
    (typeof import('@/components/Form/Search.vue'))['default']
  >;
  export type ISearchMoreIns = InstanceType<
    (typeof import('@/components/Form/SearchMore.vue'))['default']
  >;
  export type ISideDrawerIns = InstanceType<
    (typeof import('@/components/DrawerBox/components/SideDrawer.vue'))['default']
  >;
  export type ISimpleLinesIns = InstanceType<
    (typeof import('@/components/Echarts/SimpleLines.vue'))['default']
  >;
  export type ISimplePieIns = InstanceType<
    (typeof import('@/components/Echarts/SimplePie.vue'))['default']
  >;
  export type ISLAmapIns = InstanceType<
    (typeof import('@/components/SLAmap/index.vue'))['default']
  >;
  export type ISLButtonIns = InstanceType<
    (typeof import('@/components/SLButton/index.vue'))['default']
  >;
  export type ISLCardIns = InstanceType<
    (typeof import('@/components/SLCard/index.vue'))['default']
  >;
  export type ISLCardSearchIns = InstanceType<
    (typeof import('@/components/SLCardSearch/index.vue'))['default']
  >;
  export type ISLCardTableIns = InstanceType<
    (typeof import('@/components/SLCardTable/index.vue'))['default']
  >;
  export type ISLCodeMirrorIns = InstanceType<
    (typeof import('@/components/SLCodemirror/SLCodeMirror.vue'))['default']
  >;
  export type ISLDescriptionHeaderIns = InstanceType<
    (typeof import('@/components/SLDescriptionHeader/index.vue'))['default']
  >;
  export type ISLDialogIns = InstanceType<
    (typeof import('@/components/SLDialog/index.vue'))['default']
  >;
  export type ISLDialogFormIns = InstanceType<
    (typeof import('@/components/SLDialogForm/index.vue'))['default']
  >;
  export type ISLDrawerIns = InstanceType<
    (typeof import('@/components/SLDrawer/index.vue'))['default']
  >;
  export type ISLFileUploaderIns = InstanceType<
    (typeof import('@/components/SLFileUploader/index.vue'))['default']
  >;
  export type ISLFlexGroupIns = InstanceType<
    (typeof import('@/components/SLFlexGroup/index.vue'))['default']
  >;
  export type ISLFormIns = InstanceType<
    (typeof import('@/components/SLForm/index.vue'))['default']
  >;
  export type ISLFormItemIns = InstanceType<
    (typeof import('@/components/SLFormItem/index.vue'))['default']
  >;
  export type ISLMoreFilterIns = InstanceType<
    (typeof import('@/components/SLMoreFilter/index.vue'))['default']
  >;
  export type ISLPaginationIns = InstanceType<
    (typeof import('@/components/SLPagination/index.vue'))['default']
  >;
  export type ISLStepsIns = InstanceType<
    (typeof import('@/components/SLSteps/index.vue'))['default']
  >;
  export type ISLTableIns = InstanceType<
    (typeof import('@/components/SLTable/index.vue'))['default']
  >;
  export type ISLTextItemIns = InstanceType<
    (typeof import('@/components/SLTextItem/index.vue'))['default']
  >;
  export type ISLTreeIns = InstanceType<
    (typeof import('@/components/SLTree/index.vue'))['default']
  >;
  export type ISLUploaderIns = InstanceType<
    (typeof import('@/components/SLUploader/index.vue'))['default']
  >;
  export type ISvgIconIns = InstanceType<
    (typeof import('@/components/SvgIcon/SvgIcon.vue'))['default']
  >;
  export type ITableColumnIns = InstanceType<
    (typeof import('@/components/SLTable/TableColumn.vue'))['default']
  >;
  export type ITabsIns = InstanceType<
    (typeof import('@/components/Form/Tabs.vue'))['default']
  >;
  export type ITagIns = InstanceType<
    (typeof import('@/components/Form/Tag.vue'))['default']
  >;
  export type ITagGroupIns = InstanceType<
    (typeof import('@/components/Form/TagGroup.vue'))['default']
  >;
  export type ITimeFilterIns = InstanceType<
    (typeof import('@/components/additionalFilters/components/timeFilter.vue'))['default']
  >;
  export type ITiniImageUploaderIns = InstanceType<
    (typeof import('@/components/Form/TiniImageUploader.vue'))['default']
  >;
  export type ITreeBoxIns = InstanceType<
    (typeof import('@/components/TreeBox/TreeBox.vue'))['default']
  >;
  export type ITreeListIns = InstanceType<
    (typeof import('@/components/treeList/index.vue'))['default']
  >;
  export type IDepartmentUserIns = InstanceType<
    (typeof import('@/components/Form/DepartmentUser.vue'))['default']
  >;
  export type IUserSelectorIns = InstanceType<
    (typeof import('@/components/Form/UserSelector.vue'))['default']
  >;
  export type IVideojsPlayerIns = InstanceType<
    (typeof import('@/components/videoPlayer/VideojsPlayer.vue'))['default']
  >;
  export type IVideoPlayerIns = InstanceType<
    (typeof import('@/components/videoPlayer/index.vue'))['default']
  >;
  export type IVideorIns = InstanceType<
    (typeof import('@/components/Form/Videor.vue'))['default']
  >;
  export type IVoicerIns = InstanceType<
    (typeof import('@/components/Form/Voicer.vue'))['default']
  >;
  export type IVueScrollIns = InstanceType<
    (typeof import('@/components/VueScroll/index.vue'))['default']
  >;
  export type IWhitePackageIns = InstanceType<
    (typeof import('@/components/whitePackage/index.vue'))['default']
  >;
}

export {};
