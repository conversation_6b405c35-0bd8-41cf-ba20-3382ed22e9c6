<template>
  <div class="wrapper">
    <div class="left">
      <div class="circle">
        <!-- <div class="percent">
          {{ curItem.percent }}
          <span class="unit">%</span>
          <img
            src="../../imgs/up.png"
            alt=""
            class="up"
          />
        </div> -->
        <div class="count flex-baseline">
          <span class="value">{{ curItem?.today ?? '--' }}</span>
          <span class="unit">{{ curItem?.unit }}</span>
        </div>
        <div class="text">
          <span> 今日取水量 </span>
        </div>
      </div>
      <div class="foot-info">
        昨日 <span class="count"> {{ curItem?.yesterday ?? '--' }}</span>
        {{ curItem?.yesterdayUnit }}
      </div>
    </div>
    <div class="right" style="display: flex">
      <div v-if="state.blockItems.length" ref="refWrapper" class="items overlay-y onlyone">
        <div class="item-block" :class="{ isActive: state.curItemName === '梓莲达' }">
          <div class="line horizontal">
            <div class="line-frag inclined"></div>
            <div class="line-frag curve"></div>
            <div class="line-frag horizontal"></div>
          </div>
          <div class="shin-block"></div>
          <div class="text-box" @click="() => handleClick('梓莲达')">梓莲达</div>
        </div>
      </div>
      <div
        v-if="state.blockItems.length"
        ref="refWrapper"
        class="items overlay-y"
        :class="{
          onlyone: state.blockItems.length === 1,
          onlytwo: state.blockItems.length === 2
        }"
      >
        <div
          v-for="(item, i) in state.blockItems"
          :key="i"
          class="item-block"
          :class="{ isActive: state.curItemName === item.name }"
        >
          <!-- toDo： 判断线形 -->
          <div
            class="line"
            :class="[
              state.blockItems.length === 1 || i === Math.floor(state.blockItems.length / 2)
                ? 'horizontal'
                : i < Math.floor(state.blockItems.length / 2)
                ? 'circle-down'
                : 'circle-up'
            ]"
          >
            <div class="line-frag inclined"></div>
            <div class="line-frag curve"></div>
            <div class="line-frag horizontal"></div>
          </div>
          <div class="shin-block"></div>
          <div class="text-box" @click="() => handleClick(item.name)">
            {{ item.name }}
          </div>
        </div>
      </div>
      <div v-else class="empty">暂无原水信息</div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { getWaterSupplyInfo, getWaterSupplyTotal } from '@/api/headwatersManage/headwaterMonitoring';
import { toCommaNumber, transNumberUnit } from '@/utils/GlobalHelper';

type BlockItem = {
  name: string;
  percent: string;
  today: string;
  upordown: string;
  yesterday: string;
  unit: string;
  yesterdayUnit: string;
};
const state = reactive<{
  curItemIndex: number;
  blockItems: BlockItem[];
  curItemName: string;
}>({
  curItemIndex: 0,
  blockItems: [
    // {
    //   unit: 'm³',
    //   yesterdayUnit: 'm³',
    //   name: '1#水源井',
    //   percent: '14',
    //   upordown: 'up',
    //   today: toCommaNumber(2245),
    //   yesterday: toCommaNumber(1925)
    // },
    // {
    //   unit: 'm³',
    //   yesterdayUnit: 'm³',
    //   name: '2#水源井',
    //   percent: '9',
    //   upordown: 'up',
    //   today: toCommaNumber(2045),
    //   yesterday: toCommaNumber(1825)
    // },
    // {
    //   unit: 'm³',
    //   yesterdayUnit: 'm³',
    //   name: '3#水源井',
    //   percent: '11',
    //   upordown: 'up',
    //   today: toCommaNumber(2645),
    //   yesterday: toCommaNumber(2225)
    // }
  ],
  curItemName: '梓莲达'
});

const emit = defineEmits(['changeFactory']);

const curItem = ref<any>({});
const handleClick = async (name: string) => {
  // state.curItemIndex = i
  state.curItemName = name;
  curItem.value = state.blockItems?.find((item) => item.name === state.curItemName);
  if (name === '梓莲达') {
    const res = await getWaterSupplyTotal();
    const data = res.data.data;
    const todayWaterSupply = transNumberUnit(data.todayWaterSupply ?? 0);
    const yesterdayWaterSupply = transNumberUnit(data.yesterdayWaterSupply ?? 0);
    curItem.value = {
      unit: todayWaterSupply.unit + 'm³',
      yesterdayUnit: yesterdayWaterSupply.unit + 'm³',
      name: data.name,
      percent: undefined,
      upordown: 'up',
      today: toCommaNumber(todayWaterSupply.value?.toFixed(1)),
      yesterday: toCommaNumber(yesterdayWaterSupply.value?.toFixed(1))
    };
  }
  emit('changeFactory', curItem.value);
};
const refreshData = async () => {
  try {
    const res = await getWaterSupplyInfo();
    const newData = res.data?.data; // .filter(item => item.name !== '梓莲达')
    state.blockItems =
      newData.map((item) => {
        const todayWaterSupply = transNumberUnit(item.todayWaterSupply ?? 0);
        const yesterdayWaterSupply = transNumberUnit(item.yesterdayWaterSupply ?? 0);
        return {
          unit: todayWaterSupply.unit + 'm³',
          yesterdayUnit: yesterdayWaterSupply.unit + 'm³',
          name: item.name,
          percent: undefined,
          upordown: 'up',
          today: toCommaNumber(todayWaterSupply.value?.toFixed(1)),
          yesterday: toCommaNumber(yesterdayWaterSupply.value?.toFixed(1))
        };
      }) || [];
    await nextTick();
    handleClick('梓莲达');
  } catch (error) {
    //
  }
};
const timer = -1;
const refWrapper = ref<HTMLDivElement>();
// const autoRun = () => {
//   if (!refWrapper.value) return
//   timer = setInterval(() => {
//     state.curItemIndex = state.blockItems.length === 0
//       ? 0
//       : (state.curItemIndex + 1) % state.blockItems.length
//     const top: any = (refWrapper.value?.children[state.curItemIndex] as any)
//       ?.offsetTop
//     refWrapper.value?.scrollTo({
//       top
//     })
//   }, 5000)
//   refWrapper.value.onmouseenter = () => {
//     clearInterval(timer)
//   }
//   refWrapper.value.onmouseleave = () => {
//     autoRun()
//   }
// }
onMounted(() => {
  refreshData();
});
</script>
<style lang="scss" scoped>
.empty {
  display: grid;
  place-items: center;
  color: #77c0fa;
  height: 100%;
  font-size: 14px;
  width: 100%;
}
.wrapper {
  margin: 0;
  padding: 0;
  display: flex;
  flex-wrap: nowrap;
  flex-direction: row;
  align-items: center;
  justify-content: space-around;
  padding: 20px;
  .left {
    width: 40%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    .percent {
      display: flex;
      align-items: baseline;
      font-size: 12px;
    }
    .circle {
      width: 134px;
      height: 134px;
      background: url('../../imgs/dash_circle.png') 0 0 /100% 100% no-repeat;
      text-align: center;
      display: flex;
      justify-content: space-around;
      align-items: center;
      flex-direction: column;
      padding: 30px;
      .up {
        margin-left: 4px;
      }
    }
    .info {
      text-align: center;
    }
  }
  .right {
    width: 60%;
    height: 100%;
    // background: url('../../imgs/water_list.png') 0 0 /100% 100% no-repeat;
    ::-webkit-scrollbar {
      display: none;
    }
    .items {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      position: relative;
      width: 100%;
      height: 100%;
      &.onlyone {
        justify-content: center;
        padding-bottom: 20px;
      }
      &.onlytwo {
        justify-content: space-around;
      }
      .item-block {
        width: 100%;
        height: 40px;
        min-height: 40px;
        margin-bottom: 15px;
        display: flex;
        user-select: none;
        cursor: pointer;
        &:last-child {
          margin-bottom: 0;
        }
        &.isActive {
          .shin-block {
            background-color: #2bf9ed;
          }
          .text-box {
            background-color: rgba(43, 249, 237, 0.3);
          }
        }
        .line {
          width: 40px;
          height: 100%;
          // &.horizontal {
          //   margin: auto 0;
          //   height: 2px;
          //   border: none;
          //   position: relative;
          //   background: linear-gradient(
          //     to right,
          //     rgba(147, 236, 253, 0),
          //     rgba(147, 236, 253, 1)
          //   );
          //   &:after {
          //     content: ' ';
          //     background-color: rgba(147, 236, 253, 1);
          //     width: 4px;
          //     height: 4px;
          //     border-radius: 2px;
          //     position: absolute;
          //     right: -2px;
          //     top: -1px;
          //   }
          // }
          // &.circle-down {
          //   display: flex;

          //   .line-frag {
          //     // transform: translateX(-50%);
          //   }
          //   .inclined {
          //     width: 40px;
          //     height: 2px;
          //     background: linear-gradient(
          //       to right,
          //       rgba(147, 236, 253, 0),
          //       rgba(147, 236, 253, 0.5)
          //     );
          //     transform: rotate(-45deg) translateY(37px) translateX(-37px);
          //   }
          //   .curve {
          //     width: 34px;
          //     height: 40px;
          //     background: radial-gradient(
          //         circle,
          //         transparent 18px,
          //         rgba(147, 236, 253, 0.5) 18px,

          //         rgba(147, 236, 253, 0.5) 20px,
          //         transparent 20px
          //       )
          //       no-repeat;
          //     background-position: 20px 26px;
          //     transform: translateX(-25px);
          //   }
          // }
          &.circle-up {
          }
        }
        .shin-block {
          width: 6px;
          height: 100%;
          background-color: #77c0fa;
          margin-right: 10px;
        }
        .text-box {
          width: 134px;
          height: 100%;
          background: rgba(43, 166, 249, 0.3);
          display: grid;
          place-items: center;
        }
      }
    }
  }
  .count {
    color: #65ffbe;
    font-size: 24px;
    font-family: font-lcd;
    &.flex-baseline {
      display: flex;
      align-items: baseline;
    }
  }
  .text {
    font-size: 14px;
    color: #fff;
  }
  .unit {
    font-size: 12px;
    margin-left: 2px;
    word-break: keep-all;
  }
  .foot-info {
    color: #77c3ff;
    font-size: 14px;
    .count {
      font-size: 20px;
    }
  }
}
</style>
