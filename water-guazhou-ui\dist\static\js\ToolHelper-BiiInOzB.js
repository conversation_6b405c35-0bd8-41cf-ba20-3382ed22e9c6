import{l as a,p,d as m}from"./AnimatedLinesLayer-B2VbV4jv.js";import{g as l,b as w}from"./MapView-DaoQedLH.js";import{b as i,w as f}from"./Point-WxyopZva.js";import{s as y}from"./FeatureHelper-Da16o0mu.js";const R=e=>new a({view:e}),G=(e,t,o)=>{const n=new p({view:e,layer:t,polygonSymbol:y("polygon"),polylineSymbol:y("polyline"),pointSymbol:y("point")}),s=n==null?void 0:n.on("create",r=>{r.state==="complete"&&o&&o(r.graphic)}),u=n==null?void 0:n.on("update",r=>{r.state==="complete"&&o&&o(r.graphics[0])});return{sketch:n,createHandler:s,updateHandler:u}},P=(e,t)=>{const o=document.getElementById("viewDiv");o&&(o.style.cursor=e)},_=(e,t)=>e&&(e==null?void 0:e.match(/(\d(\.\d+)?)+/g))||t,d=e=>{try{return m.defs("EPSG:4548","+proj=tmerc +lat_0=0 +lon_0=117 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"),m(new m.Proj("EPSG:3857"),new m.Proj("EPSG:4548"),e)}catch(t){console.log(t);return}},j=e=>{var t,o;if(e.geometry.spatialReference.wkid===4548)return e;if(((t=e==null?void 0:e.geometry)==null?void 0:t.type)==="polyline"){const s=e.geometry.paths[0].map(d);return new l({geometry:new w({paths:[s],spatialReference:new i({wkid:4548})}),attributes:e.attributes,symbol:e.symbol})}if(((o=e==null?void 0:e.geometry)==null?void 0:o.type)==="point"){const n=e.geometry,s=d([n.x,n.y]);return new l({geometry:new f({x:s[0],y:s[1],spatialReference:new i({wkid:4548})}),attributes:{...e.attributes||{},X:s[0],Y:s[1]},symbol:e.symbol})}return e};export{_ as R,G as a,j as g,R as i,P as s};
