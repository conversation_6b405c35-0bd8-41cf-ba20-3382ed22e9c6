package org.thingsboard.server.dao.util.imodel.query.smartManagement.maintain;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartManagement.maintaince.SMMaintainTask;
import org.thingsboard.server.dao.util.imodel.query.GeneralTaskPageableQueryEntity;

@Getter
@Setter
public class SMMaintainTaskPageRequest extends GeneralTaskPageableQueryEntity<SMMaintainTask, SMMaintainTaskPageRequest> {
    // 是否已分配
    private Boolean isAssigned;

    // 是否已完成
    private Boolean isComplete;

    // 创建人Id
    private String creator;

    // 养护人员Id
    private String maintainUser;

}
