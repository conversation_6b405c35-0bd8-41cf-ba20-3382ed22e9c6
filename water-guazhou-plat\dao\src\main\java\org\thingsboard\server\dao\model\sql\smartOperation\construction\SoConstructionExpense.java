package org.thingsboard.server.dao.model.sql.smartOperation.construction;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseUsername;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.math.BigDecimal;
import java.util.Date;


@Getter
@Setter
@ResponseEntity
public class SoConstructionExpense implements SoConstructionRelatedEntity {
    // id
    private String id;

    // 编号
    private String code;

    // 所属工程编号
    private String constructionCode;

    // 所属合同编号
    private String contractCode;

    // 所属合同名称
    @TableField(exist = false)
    private String contractName;

    // 所属合同类型
    @TableField(exist = false)
    private String contractType;

    // 所属合同类型
    @TableField(exist = false)
    private String contractTypeName;

    // 所属合同金额，万元
    @TableField(exist = false)
    private BigDecimal contractCost;

    // 费用类型
    private String type;

    // 金额，万元
    private BigDecimal cost;

    // 支付方式
    private String paymentType;

    // 报批时间
    private Date approvalTime;

    // 提交财务处理时间
    private Date submitFinanceTime;

    // 一审审核金额，万元
    private BigDecimal firstVerifyCost;

    // 一审结算单位
    private String firstVerifyOrganization;

    // 二审审核金额，万元
    private BigDecimal secondVerifyCost;

    // 二审结算单位
    private String secondVerifyOrganization;

    // 代收款信息
    private String payeeInfo;

    // 收款单位
    private String payeeOrganization;

    // 说明
    private String remark;

    // 附件信息
    private String attachments;

    // 创建人
    @ParseUsername
    private String creator;

    // 创建时间
    private Date createTime;

    // 最后更新用户
    @ParseUsername
    private String updateUser;

    // 最后更新时间
    private Date updateTime;

    // 客户id
    private String tenantId;

}
