package org.thingsboard.server.controller.base;

import java.util.List;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.thingsboard.server.dao.base.IBaseUnderlyingConfigurationService;
import org.thingsboard.server.dao.model.sql.base.BaseUnderlyingConfiguration;
import org.thingsboard.server.dao.util.imodel.query.base.BaseUnderlyingConfigurationPageRequest;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;
import org.thingsboard.server.service.aspect.annotation.MonitorPerformance;

/**
 * 平台管理-基础配置Controller
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
@Api(tags = "平台管理-基础配置")
@RestController
@RequestMapping("api/base/underlying/configuration")
public class BaseUnderlyingConfigurationController extends BaseController {

    @Autowired
    private IBaseUnderlyingConfigurationService baseUnderlyingConfigurationService;

    /**
     * 查询平台管理-基础配置列表
     */
    @MonitorPerformance(description = "平台管理-查询基础配置列表")
    @ApiOperation(value = "查询基础配置列表")
    @GetMapping("/list")
    public IstarResponse list(BaseUnderlyingConfigurationPageRequest baseUnderlyingConfiguration) {
        return IstarResponse.ok(baseUnderlyingConfigurationService.selectBaseUnderlyingConfigurationList(baseUnderlyingConfiguration));
    }

    /**
     * 获取平台管理-基础配置详细信息
     */
    @MonitorPerformance(description = "平台管理-查询基础配置详情")
    @ApiOperation(value = "查询基础配置详情")
    @GetMapping(value = "/getDetail")
    public IstarResponse getInfo(String id) {
        return IstarResponse.ok(baseUnderlyingConfigurationService.selectBaseUnderlyingConfigurationById(id));
    }

    /**
     * 新增平台管理-基础配置
     */
    @MonitorPerformance(description = "平台管理-新增基础配置")
    @ApiOperation(value = "新增基础配置")
    @PostMapping("/add")
    public IstarResponse add(@RequestBody BaseUnderlyingConfiguration baseUnderlyingConfiguration) {
        return IstarResponse.ok(baseUnderlyingConfigurationService.insertBaseUnderlyingConfiguration(baseUnderlyingConfiguration));
    }

    /**
     * 修改平台管理-基础配置
     */
    @MonitorPerformance(description = "平台管理-修改基础配置")
    @ApiOperation(value = "修改基础配置")
    @PostMapping("/edit")
    public IstarResponse edit(@RequestBody BaseUnderlyingConfiguration baseUnderlyingConfiguration) {
        return IstarResponse.ok(baseUnderlyingConfigurationService.updateBaseUnderlyingConfiguration(baseUnderlyingConfiguration));
    }

    /**
     * 删除平台管理-基础配置
     */
    @MonitorPerformance(description = "平台管理-删除基础配置")
    @ApiOperation(value = "删除基础配置")
    @DeleteMapping("/deleteIds")
    public IstarResponse remove(@RequestBody List<String> ids) {
        return IstarResponse.ok(baseUnderlyingConfigurationService.deleteBaseUnderlyingConfigurationByIds(ids));
    }
}
