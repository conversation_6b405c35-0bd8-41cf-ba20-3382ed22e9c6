<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.smartService.call.CallWorkOrderMapper">
    <select id="getList" resultType="org.thingsboard.server.dao.model.sql.smartService.call.CallWorkOrder">
        select coalesce(b.serial_no, a.serial_no) as serial_no ,a.* , COALESCE(b.status, 'COMPLETE') as status, coalesce(b.remark, a.remark), coalesce(b.address, a.address), b.receive_department_id, b.level, b.process_level,
        a.source as sourceName, d.name as areaName, a.type as workOrderType, a.topic as topicName, g.first_name as creator<PERSON><PERSON>, j.first_name as seatsName, k.name as departmentName,
        o.name as receive_department_name,
        l.phone as fromPhone, l.call_time, cust_info.code as custCode, meter_book.name as meterBookName,
        case when (extract(epoch from b.create_time) +  b.process_level * 60 &lt; extract(epoch from now())) then '1'
            when (extract(epoch from b.create_time) +  b.process_level * 60 &gt;= extract(epoch from now())) then '0'
        end as chaoShi
        from tb_service_work_order a
        left join work_order b on a.work_order_id = b.id
        left join tb_service_system_dict c on a.source = c.id
        left join tb_service_system_dict d on a.area = d.id
        left join tb_service_system_work_order_type e on a.type = e.id
        left join tb_service_system_work_order_type f on a.topic = f.id
        left join tb_user g on a.creator = g.id
        left join tb_service_seats_user h on a.seats_id = h.user_id
        left join tb_user j on h.user_id = j.id
        left join tb_department k on g.department_id = k.id
        left join tb_service_call_log l on a.call_id = l.id
        left join tb_user m on b.process_user_id = m.id
        left join tb_department n on m.department_id = n.id
        left join tb_department o on b.receive_department_id = o.id
        left join revenue.tb_cust_info cust_info on a.phone = cust_info.phone
        left join revenue.tb_meter_book meter_book on cust_info.meter_book_id = meter_book.id
        left join (select work_order_id, count(*) as num from tb_service_work_order_remind group by work_order_id) remind on remind.work_order_id = a.work_order_id

        where 1=1
        <if test="phone != null and phone != ''">
         and a.phone like '%' || #{phone} || '%'
        </if>
        <if test="isDispatch != null and isDispatch != ''">
          and a.is_dispatch like '%' || #{isDispatch} || '%'
        </if>
        <if test="seatsId != null and seatsId != ''">
         and h.id like '%' || #{seatsId} || '%'
        </if>
        <if test="type != null and type != ''">
         and a.type like '%' || #{type} || '%'
        </if>
        <if test="topic != null and topic != ''">
          and a.topic like '%' || #{topic} || '%'
        </if>
        <if test="keywords != ''">
            and (a.name like '%' || #{keywords} || '%' or a.phone like '%' || #{keywords} || '%' or b.remark like '%' || #{keywords} || '%')
        </if>
        <if test="departmentId != null and departmentId != ''">
          and b.receive_department_id = #{departmentId}
        </if>
        <if test="status != ''">
            <choose>
                <when test="status == 'APPROVED'">
                    and (b.status is null or b.status = 'APPROVED')
                </when>
                <otherwise>
                    and (b.status is not null and b.status = #{status})
                </otherwise>

            </choose>
        </if>
        <if test="chaoShi != '' and chaoShi != null">
            <choose>
                <when test="chaoShi == '1'.toString()">
                    and extract(epoch from b.create_time) +  b.process_level * 60 &lt; extract(epoch from now())
                </when>
                <otherwise>
                    and extract(epoch from b.create_time) +  b.process_level * 60 &gt;= extract(epoch from now())
                </otherwise>
            </choose>
        </if>
        <if test="cuiBan != ''">
            <choose>
                <when test="cuiBan == '1'.toString()">
                    and remind.num > 0
                </when>
                <otherwise>
                    and (remind.num is null or remind.num =0)
                </otherwise>
            </choose>
        </if>
        <if test="zhongZhi != null and zhongZhi != ''">
            <choose>
                <when test="zhongZhi == '1'.toString()">
                    and b.status = 'TERMINATED'
                </when>
                <otherwise>
                    and b.status != 'TERMINATED'
                </otherwise>
            </choose>
        </if>
        <if test="manYi != ''">
            and l.evaluate = #{manYi}
        </if>
        <if test="zenRenFang != ''">

        </if>
        <if test="jinChang != ''">

        </if>
        <if test="chongFa != ''">

        </if>
        <if test="heGe != ''">

        </if>
        <if test="wuPan != ''">

        </if>
        <if test="serialNo != ''">
            and (b.serial_no like '%' || #{serialNo} || '%' or a.serial_no like '%' || #{serialNo} || '%' )
        </if>
        <if test="startTime != null">
            and a.create_time &gt;= to_timestamp(#{startTime} / 1000)
        </if>
        <if test="endTime != null">
            and a.create_time &lt;= to_timestamp(#{endTime} / 1000)
        </if>
        order by a.create_time desc
        offset (#{page} - 1) * #{size}  limit #{size}
    </select>

    <select id="getListCount" resultType="int">
        select count(*)
        from tb_service_work_order a
        left join work_order b on a.work_order_id = b.id
        left join tb_service_system_dict c on a.source = c.id
        left join tb_service_system_dict d on a.area = d.id
        left join tb_service_system_work_order_type e on a.type = e.id
        left join tb_service_system_work_order_type f on a.topic = f.id
        left join tb_user g on a.creator = g.id
        left join tb_service_seats_user h on a.seats_id = h.user_id
        left join tb_user j on h.user_id = j.id
        left join tb_department k on g.department_id = k.id
        left join tb_service_call_log l on a.call_id = l.id
        left join tb_user m on b.process_user_id = m.id
        left join tb_department n on m.department_id = n.id
        left join tb_department o on b.receive_department_id = o.id
        left join revenue.tb_cust_info cust_info on a.phone = cust_info.phone
        left join revenue.tb_meter_book meter_book on cust_info.meter_book_id = meter_book.id
        left join (select work_order_id, count(*) as num from tb_service_work_order_remind group by work_order_id) remind on remind.work_order_id = a.work_order_id

        where 1=1
        <if test="phone != null and phone != ''">
            and a.phone like '%' || #{phone} || '%'
        </if>
        <if test="isDispatch != null and isDispatch != ''">
            and a.is_dispatch like '%' || #{isDispatch} || '%'
        </if>
        <if test="seatsId != null and seatsId != ''">
            and h.id like '%' || #{seatsId} || '%'
        </if>
        <if test="type != null and type != ''">
            and a.type like '%' || #{type} || '%'
        </if>
        <if test="topic != null and topic != ''">
            and a.topic like '%' || #{topic} || '%'
        </if>
        <if test="keywords != ''">
            and (a.name like '%' || #{keywords} || '%' or a.phone like '%' || #{keywords} || '%' or b.remark like '%' || #{keywords} || '%')
        </if>
        <if test="departmentId != null and departmentId != ''">
            and b.receive_department_id = #{departmentId}
        </if>
        <if test="status != ''">
            <choose>
                <when test="status == 'APPROVED'">
                    and (b.status is null or b.status = 'APPROVED')
                </when>
                <otherwise>
                    and (b.status is not null and b.status = #{status})
                </otherwise>

            </choose>
        </if>
        <if test="chaoShi != '' and chaoShi != null">
            <choose>
                <when test="chaoShi == '1'.toString()">
                    and extract(epoch from b.create_time) +  b.process_level * 60 &lt; extract(epoch from now())
                </when>
                <otherwise>
                    and extract(epoch from b.create_time) +  b.process_level * 60 &gt;= extract(epoch from now())
                </otherwise>
            </choose>
        </if>
        <if test="cuiBan != ''">
            <choose>
                <when test="cuiBan == '1'.toString()">
                    and remind.num > 0
                </when>
                <otherwise>
                    and (remind.num is null or remind.num =0)
                </otherwise>
            </choose>
        </if>
        <if test="zhongZhi != null and zhongZhi != ''">
            <choose>
                <when test="zhongZhi == '1'.toString()">
                    and b.status = 'TERMINATED'
                </when>
                <otherwise>
                    and b.status != 'TERMINATED'
                </otherwise>
            </choose>
        </if>
        <if test="manYi != ''">
            and l.evaluate = #{manYi}
        </if>
        <if test="zenRenFang != ''">

        </if>
        <if test="jinChang != ''">

        </if>
        <if test="chongFa != ''">

        </if>
        <if test="heGe != ''">

        </if>
        <if test="wuPan != ''">

        </if>
        <if test="serialNo != ''">
            and (b.serial_no like '%' || #{serialNo} || '%' or a.serial_no like '%' || #{serialNo} || '%' )
        </if>
        <if test="startTime != null">
            and a.create_time &gt;= to_timestamp(#{startTime} / 1000)
        </if>
        <if test="endTime != null">
            and a.create_time &lt;= to_timestamp(#{endTime} / 1000)
        </if>
    </select>

    <select id="getMonthCallLogStatistics" resultType="java.util.Map">
        select to_char(create_time, 'YYYY-MM') as month,count(*)
        from tb_service_work_order a
        where create_time between #{startTime} and #{endTime} and seats_id like '%' || #{seatsId} || '%' and a.tenant_id = #{tenantId}
        group by to_char(create_time, 'YYYY-MM')
        order by to_char(create_time, 'YYYY-MM')
    </select>
    <select id="getYearCallLogStatistics" resultType="java.util.Map">
        select a.user_id as "seatsId", c.first_name as "seatsName", coalesce(b.total, 0) as "subCount",
               (select count(*) from tb_service_work_order where create_time between #{startTime} and #{endTime}) as "allCount"
        from
            tb_service_seats_user a
                left join tb_user c on a.user_id = c.id
                left join (select seats_id, count(*) as total from tb_service_work_order
                               where create_time between #{startTime} and #{endTime}
                                group by seats_id)
                            b on a.user_id = b.seats_id
        where a.tenant_id = #{tenantId}
        order by "subCount" desc
    </select>
    <select id="getSeatsCall" resultType="org.thingsboard.server.dao.model.DTO.SeatsCallDTO">
        select g.user_id as seats_id, b.first_name as seats_name, c.direction, c.call_time, c.listen_time, c.end_time from
        tb_service_seats_user g
        left join tb_user b on g.user_id = b.id
        left join tb_service_call_log c on g.user_id = c.user_id
        where c.id is not null
        and g.user_id like '%' || #{seatsId} || '%' and g.tenant_id = #{tenantId}
        <if test="startTime != null">
            and c.call_time &gt;= to_timestamp(#{startTime} / 1000)
        </if>
        <if test="endTime != null">
            and c.call_time &lt;= to_timestamp(#{endTime} / 1000)
        </if>
        order by c.call_time desc;
    </select>
    <select id="getListByTime" resultType="org.thingsboard.server.dao.model.sql.smartService.call.CallWorkOrder">
        select a.*, a.source as sourceName, d.name as areaName, a.type as typeName, a.topic as topicName
        from tb_service_work_order a
                 left join work_order b on a.work_order_id = b.id
                 left join tb_service_system_dict d on a.area = d.id
        where a.tenant_id = #{tenantId}
        <if test="startTime != null">
            and a.create_time &gt;= to_timestamp(#{startTime} / 1000)
        </if>
        <if test="endTime != null">
            and a.create_time &lt;= to_timestamp(#{endTime} / 1000)
        </if>
    </select>
    <select id="getDetail" resultType="org.thingsboard.server.dao.model.sql.smartService.call.CallWorkOrder">
        select a.*, b.serial_no, COALESCE(b.status, 'COMPLETE') as status, b.remark, b.address, b.receive_department_id, b.level, b.process_level,
               a.source as sourceName, d.name as areaName, a.type as typeName,
               a.topic as topicName, g.first_name as creatorName, j.first_name as seatsName, k.name as departmentName,
               o.name as receive_department_name,
            l.phone as fromPhone, l.call_time
        from tb_service_work_order a
                 left join work_order b on a.work_order_id = b.id
                 left join tb_service_system_dict c on a.source = c.id
                 left join tb_service_system_dict d on a.area = d.id
                 left join tb_service_system_work_order_type e on a.type = e.id
                 left join tb_service_system_work_order_type f on a.topic = f.id
                 left join tb_user g on a.creator = g.id
                 left join tb_service_seats_user h on a.seats_id = h.id
                 left join tb_user j on h.user_id = j.id
                 left join tb_department k on g.department_id = k.id
                 left join tb_service_call_log l on a.call_id = l.id
                 left join tb_user m on b.process_user_id = m.id
                 left join tb_department n on m.department_id = n.id
                 left join tb_department o on b.receive_department_id = o.id
        where b.id = #{workOrderId} offset 0 limit 1
    </select>

    <select id="getListByTimeCount" resultType="java.lang.Integer">
        select count(*)
        from tb_service_work_order a
        where 1 = 1
        <if test="startTime != null">
            and a.create_time &gt;= to_timestamp(#{startTime} / 1000)
        </if>
        <if test="endTime != null">
            and a.create_time &lt;= to_timestamp(#{endTime} / 1000)
        </if>
    </select>

    <select id="getKpiList" resultType="com.alibaba.fastjson.JSONObject">
        select  b.first_name as "userName", count(d.id) "completeOrder", count(e.id) "allOrder", count(f.id) "goodEvaluate", count(g.id) "allEvaluate"
        from postgres.public.tb_service_seats_user a
        left join tb_user b on a.user_id = b.id
        left join tb_service_work_order c on b.id = c.seats_id
        left join tb_work_order d on c.work_order_id = d.id and d.status = '4'
        left join tb_work_order e on c.work_order_id = d.id
        left join tb_service_call_log f on a.user_no = f.extension and f.evaluate = '1'
        left join tb_service_call_log g on a.user_no = f.extension

        where a.tenant_id = #{tenantId}
        <if test="start != null">
            and c.create_time &gt;= #{start}
            and f.call_time &gt;= #{start}
            and g.call_time &gt;= #{start}
        </if>
        <if test="end != null">
            and c.create_time &lt;= #{end}
            and f.call_time &lt;= #{end}
            and g.call_time &lt;= #{end}
        </if>
        group by b.id, b.first_name
    </select>
    <select id="getListByTimeByWorkOrderType" resultType="org.thingsboard.server.dao.model.sql.smartService.call.CallWorkOrder">
        select a.*, a.source as sourceName, d.name as areaName, a.type as typeName, a.topic as topicName
        from tb_service_work_order a
        left join tb_service_system_dict d on a.area = d.id
        where a.tenant_id = #{tenantId}
        <if test="startTime != null">
            and a.create_time &gt;= to_timestamp(#{startTime} / 1000)
        </if>
        <if test="endTime != null">
            and a.create_time &lt;= to_timestamp(#{endTime} / 1000)
        </if>
    </select>
</mapper>