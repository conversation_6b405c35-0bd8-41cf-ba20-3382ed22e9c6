package org.thingsboard.server.dao.model.sql;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 水质报表填报
 */
@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.TB_WATER_REPORT_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class WaterReport {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.TB_WATER_REPORT_TYPE)
    private String type;

    @Column(name = ModelConstants.TB_WATER_REPORT_STATION_ID)
    private String stationId;

    @Column(name = ModelConstants.TB_WATER_REPORT_COD)
    private BigDecimal cod;

    @Column(name = ModelConstants.TB_WATER_REPORT_BOD)
    private BigDecimal bod;

    @Column(name = ModelConstants.TB_WATER_REPORT_NH3)
    private BigDecimal nh3;

    @Column(name = ModelConstants.TB_WATER_REPORT_PH)
    private BigDecimal ph;

    @Column(name = ModelConstants.TB_WATER_REPORT_TP)
    private BigDecimal tp;

    @Column(name = ModelConstants.TB_WATER_REPORT_TN)
    private BigDecimal tn;

    @Column(name = ModelConstants.TB_WATER_REPORT_TIME)
    private Date time;

    @Column(name = ModelConstants.TB_WATER_REPORT_TENANT_ID)
    private String tenantId;

    @Column(name = ModelConstants.TB_WATER_REPORT_TIME_TYPE)
    private String timeType;

    @Transient
    private String timeStr;

    @Transient
    private String stationName;


}
