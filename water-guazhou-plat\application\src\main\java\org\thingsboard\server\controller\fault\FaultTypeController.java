package org.thingsboard.server.controller.fault;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.fault.FaultTypeService;
import org.thingsboard.server.dao.model.sql.fault.FaultType;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.List;

/**
 * 班组
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-11-07
 */
@RestController
@RequestMapping("api/fault/type")
public class FaultTypeController extends BaseController {

    @Autowired
    private FaultTypeService faultTypeService;

    @GetMapping
    public IstarResponse getList(@RequestParam(required = false, defaultValue = "") String deviceTypeId, @RequestParam(required = false, defaultValue = "") String name,
                                 int page, int size) throws ThingsboardException {

        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return IstarResponse.ok(faultTypeService.getList(deviceTypeId, name, page, size, tenantId));
    }

    @PostMapping
    public IstarResponse save(@RequestBody FaultType faultType) throws ThingsboardException {
        faultType.setCreator(UUIDConverter.fromTimeUUID(getCurrentUser().getUuidId()));
        faultType.setTenantId(UUIDConverter.fromTimeUUID(getTenantId().getId()));
        return IstarResponse.ok(faultTypeService.save(faultType));
    }

    @DeleteMapping
    public IstarResponse delete(@RequestBody List<String> ids) {
        return faultTypeService.delete(ids);
    }
}
