package org.thingsboard.server.dao.store;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.store.DeviceUsageJournal;
import org.thingsboard.server.dao.util.imodel.query.store.DeviceUsageJournalPageRequest;
import org.thingsboard.server.dao.util.imodel.query.store.DeviceUsageJournalSaveRequest;

public interface DeviceUsageJournalService {
    /**
     * 分页条件查询设备使用信息
     *
     * @param request 分页查询条件
     * @return 分页查询结果
     */
    IPage<DeviceUsageJournal> findAllConditional(DeviceUsageJournalPageRequest request);

    /**
     * 保存设备使用信息
     *
     * @param entity 实体信息
     * @return 保存好的实体
     */
    DeviceUsageJournal save(DeviceUsageJournalSaveRequest entity);

    /**
     * 增量修改
     *
     * @param entity 实体信息
     * @return 是否修改成功
     */
    boolean update(DeviceUsageJournal entity);

    /**
     * 删除
     *
     * @param id 唯一标识
     * @return 是否删除成功
     */
    boolean delete(String id);

}
