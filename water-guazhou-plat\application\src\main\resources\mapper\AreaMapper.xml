<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.area.AreaMapper">
    <resultMap id="MapResultMap" type="java.util.Map">
        <!--suppress s -->
        <result column="id" property="id"/>
        <!--suppress s -->
        <result column="path" property="path"/>
    </resultMap>
    <select id="getTreePathMap" resultMap="MapResultMap">
        with recursive path(id, path) as (select id, (name)::text
                                          from tb_area
                                          where parent_id is null
                                          union all
                                          select org.id, path.path || '-' || org.name
                                          from path,
                                               tb_area org
                                          where path.id = org.parent_id)
        select *
        from path;
    </select>
</mapper>