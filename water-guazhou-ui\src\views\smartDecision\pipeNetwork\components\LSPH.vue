<template>
  <div class="chart">
    <VChart :option="state.option"></VChart>
  </div>
</template>
<script lang="ts" setup>
import { graphic } from 'echarts'
import { GetPartitionRefLeakSort } from '@/api/mapservice/dma'

const generateSupplyOption = (xData: number[] = [], yData: any[] = []) => {
  // this.Top5ListName=[
  //      {0: "三亚市税务局",
  //       1: "三亚市市场监督管理局",
  //       2: "三亚市公安局",
  //       3: "三亚市邮政管理局",
  //       4: "三亚市社会保险服务中心窗口"}]

  // this.Top5ListValue=[{0: 189354, 1: 56933, 2: 13267, 3: 10979, 4: 9054}]

  const max = Math.max(...xData) || 100
  // this.list=[{itemStyle:
  // color: "rgba(248, 75, 110, 1)"
  // name: "三亚市税务局"
  // num: "189354"
  // : "57.03%"
  // value: 189354}]
  const list = yData.map((item, index) => {
    const obj = {
      name: item,
      value: xData[index],
      num: xData[index]
    }
    return obj
  })
  // this.list1=[
  // label:{
  // normal:{
  // color: colors[index],
  // fontSize: 14
  // position: "right"
  // show: true
  // offset:[16,0]
  // name: "三亚市税务局"
  // formatter(){return(item.num+'单位'+''+item.rate)}
  // rate: "57.03%"
  // value: 189354}}
  const list1 = yData.map((item, index) => {
    const obj = {
      name: item,
      value: max,
      label: {
        show: true,
        position: 'right',
        fontSize: 14,
        color: '#fff',
        offset: [16, 0],
        formatter() {
          return xData[index]
        }
      }
    }
    return obj
  })
  // this.list2=[{label: "189354"
  // name: "三亚市税务局"
  // rate: "57.03%"
  // value: 189354}]
  const list2 = yData.map((item, index) => {
    const obj = {
      name: item,
      value: xData[index],
      label: xData[index]
    }
    return obj
  })
  const options = {
    tooltip: {
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      top: 20,
      left: 20,
      right: 60,
      bottom: 20,
      containLabel: true
    },
    xAxis: {
      type: 'value',
      splitLine: {
        show: false
      },
      axisLine: {
        show: false
      },
      axisLabel: {
        show: false
      },
      axisTick: {
        show: false
      },
      position: 'top'
    },
    yAxis: {
      type: 'category',
      data: yData,
      inverse: true, // 倒叙
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        textStyle: {
          color: 'rgba(255,255,255,0.85)',
          fontSize: 14,
          fontFamily: 'TencentSans'
        }
      }
    },
    dataZoom: [
      {
        type: 'slider',
        show: false, // 隐藏或显示（true）组件
        // backgroundColor: 'rgba(0,0,0,0)', // 组件的背景颜色。
        // fillerColor: 'rgba(255,255,255,0.3)', // 选中范围的填充颜色。
        // borderColor: 'rgb(0,0,0,0.25)', // 边框颜色
        // showDetail: false, // 是否显示detail，即拖拽时候显示详细数值信息
        startValue: 0, // 数据窗口范围的起始数值
        endValue: 5, // 数据窗口范围的结束数值（一页显示多少条数据）
        yAxisIndex: [0] // 控制哪个轴，如果是 number 表示控制一个轴，如果是 Array 表示控制多个轴。此处控制第二根轴
        // filterMode: 'empty',
        // width: 18, // 滚动条高度
        // right: 10, // 距离右边
        // handleSize: 0, // 控制手柄的尺寸
        // zoomLoxk: true // 是否锁定选择区域（或叫做数据窗口）的大小
        // top: 10,
        // height: '90%'
      },
      {
        // 没有下面这块的话，只能拖动滚动条，鼠标滚轮在区域内不能控制外部滚动条
        type: 'inside',
        yAxisIndex: [0], // 控制哪个轴，如果是 number 表示控制一个轴，如果是 Array 表示控制多个轴。此处控制第二根轴
        zoomOnMouseWheel: false, // 滚轮是否触发缩放
        moveOnMouseMove: true, // 鼠标移动能否触发平移
        moveOnMouseWheel: true // 鼠标滚轮能否触发平移
      }
    ],
    series: [
      {
        type: 'bar',
        barGap: '-100%',
        barWidth: 14,
        z: 1,
        itemStyle: {
          color: new graphic.LinearGradient(
            0,
            0,
            1,
            0,
            [
              {
                offset: 0,
                color: 'rgba(0,255,255,1)'
              },
              {
                offset: 1,
                color: 'rgba(255,0,0,1)'
              }
            ],
            false
          )
        },
        data: list
      },
      {
        type: 'bar',
        barWidth: 14,
        z: 0,
        itemStyle: {
          color: 'rgba(26, 49, 99, 1)'
        },
        tooltip: {
          show: false
        },
        data: list1
      },
      {
        type: 'pictorialBar',
        symbolRepeat: 'fixed',
        symbolMargin: 6,
        symbol: 'rect',
        z: 2,
        symbolClip: true,
        symbolSize: [1, 14],
        symbolPosition: 'start',
        itemStyle: {
          color: 'rgba(0,0,0,1)'
        },
        data: list2
      }
    ]
  }
  return options
}
const state = reactive<{ option: any }>({
  option: generateSupplyOption()
})
const refreshData = () => {
  GetPartitionRefLeakSort()
    .then(res => {
      const { x, y } = res.data.data || {}
      state.option = generateSupplyOption(y || [], x || [])
    })
    .catch(() => {
      state.option = generateSupplyOption()
    })
}
onMounted(() => {
  refreshData()
})
</script>
<style lang="scss" scoped>
.chart {
  width: 100%;
  height: 100%;
}
</style>
