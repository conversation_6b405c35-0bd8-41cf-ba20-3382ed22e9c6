import { request } from '@/plugins/axios';

/**
 * 查询指定项目下的站点报警列表
 * @param params
 * @returns
 */
export const GetStationAlarmByProjectId = (params: { projectId: string }) => {
  return request({
    url: '/api/alarm/stationAlarmByProjectId',
    method: 'get',
    params
  });
};

/**
 * 查询查询泵站数量统计
 * @param params
 * @returns
 */
export const GetBumbStationCount = () => {
  return request({
    url: '/istar/api/pumpingStation/data/pumpingStationCount',
    method: 'get'
  });
};

/**
 * 查询指定项目下的供水量以及能耗情况
 * @param params
 * @returns
 */
export const GetMonthAndYearWaterSupplyEnerge = (params: {
  projectId: string;
}) => {
  return request({
    url: '/istar/api/pumpingStation/data/energyInAndWaterSupply',
    method: 'get',
    params
  });
};
/**
 * 查询运行日志
 * @param params
 * @returns
 */
export const GetMotionDailyLog = (params: {
  stationId: string;
  start: number;
  end: number;
}) => {
  return request({
    url: '/istar/api/report/flowRate',
    method: 'get',
    params
  });
};
/**
 * 查询指定类型的站点实时数据列表
 * @param params
 * @returns
 */
export const GetStationDynamicRealtimeData = (params?: {
  stationType?: string;
  projectId?: string;
}) => {
  return request({
    url: '/istar/api/station/data/detailList/view',
    method: 'get',
    params
  });
};

/**
 * 查询近三天出水流量
 * @param params
 * @returns
 */
export const GetTreeDayStationFlow = (params: { stationId: string }) => {
  return request({
    url: '/istar/api/station/data/waterPlant/stationFlowRate',
    method: 'get',
    params
  });
};
/**
 * 查询近三天出水压力
 * @param params
 * @returns
 */
export const GetTreeDayStaionPressure = (params: { stationId: string }) => {
  return request({
    url: '/istar/api/station/data/waterPlant/stationPressure',
    method: 'get',
    params
  });
};
