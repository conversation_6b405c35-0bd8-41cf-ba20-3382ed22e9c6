<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.deviceAuth.DeviceUserAuthMapper">
    
    <select id="findByPage" resultType="org.thingsboard.server.dao.model.sql.deviceAuth.DeviceUserAuth">
        SELECT 
            a.*
        FROM tb_device_user_auth a
        <where>
            <if test="tenantId != null and tenantId != ''">
                AND a.tenant_id = #{tenantId}
            </if>
            <if test="deviceId != null and deviceId != ''">
                AND a.device_id = #{deviceId}
            </if>
            <if test="deviceName != null and deviceName != ''">
                AND a.device_name LIKE CONCAT('%', #{deviceName}, '%')
            </if>
            <if test="deviceSerial != null and deviceSerial != ''">
                AND a.device_serial LIKE CONCAT('%', #{deviceSerial}, '%')
            </if>
            <if test="userId != null and userId != ''">
                AND a.user_id = #{userId}
            </if>
            <if test="userName != null and userName != ''">
                AND a.user_name LIKE CONCAT('%', #{userName}, '%')
            </if>
            <if test="authType != null">
                AND a.auth_type = #{authType}
            </if>
        </where>
        ORDER BY a.create_time DESC
    </select>
    
    <select id="findByDeviceId" resultType="org.thingsboard.server.dao.model.sql.deviceAuth.DeviceUserAuth">
        SELECT 
            a.*
        FROM tb_device_user_auth a
        WHERE a.device_id = #{deviceId}
        AND a.tenant_id = #{tenantId}
        ORDER BY a.create_time DESC
    </select>
    
    <select id="findByUserId" resultType="org.thingsboard.server.dao.model.sql.deviceAuth.DeviceUserAuth">
        SELECT 
            a.*
        FROM tb_device_user_auth a
        WHERE a.user_id = #{userId}
        AND a.tenant_id = #{tenantId}
        ORDER BY a.create_time DESC
    </select>
    
    <insert id="batchInsert">
        INSERT INTO tb_device_user_auth (
            id, device_id, device_name, device_serial, user_id, 
            user_name, auth_type, creator, create_time, tenant_id
        ) VALUES 
        <foreach collection="list" item="item" separator=",">
            (
                #{item.id}, #{item.deviceId}, #{item.deviceName}, #{item.deviceSerial}, #{item.userId},
                #{item.userName}, #{item.authType}, #{item.creator}, #{item.createTime}, #{item.tenantId}
            )
        </foreach>
    </insert>
    
    <delete id="deleteByDeviceId">
        DELETE FROM tb_device_user_auth
        WHERE device_id = #{deviceId}
        AND tenant_id = #{tenantId}
    </delete>
</mapper>
