import { request } from '@/plugins/axios'

// 获取三维数据配置列表
export function getThreeDimensionalConfigList(params: any) {
  return request({
    url: '/api/base/three/configuration/list',
    method: 'get',
    params
  })
}

// 获取三维数据配置详情
export function getThreeDimensionalConfigDetail(id: string) {
  return request({
    url: '/api/base/three/configuration/getDetail',
    method: 'get',
    params: { id }
  })
}

// 新增三维数据配置
export function addThreeDimensionalConfig(data: any) {
  return request({
    url: '/api/base/three/configuration/add',
    method: 'post',
    data
  })
}

// 修改三维数据配置
export function editThreeDimensionalConfig(data: any) {
  return request({
    url: '/api/base/three/configuration/edit',
    method: 'post',
    data
  })
}

// 删除三维数据配置
export function deleteThreeDimensionalConfig(ids: string[]) {
  return request({
    url: '/api/base/three/configuration/deleteIds',
    method: 'delete',
    data: ids
  })
}

// 获取所有方案管理数据
export function getAllBaseSchemeManagement() {
  return request({
    url: '/api/base/scheme/management/getAllBaseSchemeManagement',
    method: 'get'
  })
}

// 获取所有瓦片数据配置
export function getAllBaseTileConfiguration() {
  return request({
    url: '/api/base/tile/configuration/getAllBaseTileConfiguration',
    method: 'get'
  })
}

// 获取所有矢量数据配置
export function getAllBaseVectorConfiguration() {
  return request({
    url: '/api/base/vector/configuration/getAllBaseVectorConfiguration',
    method: 'get'
  })
} 