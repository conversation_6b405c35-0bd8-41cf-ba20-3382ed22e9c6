package org.thingsboard.server.controller.fault;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.fault.FaultPlanMService;
import org.thingsboard.server.dao.model.sql.fault.FaultPlanM;
import org.thingsboard.server.dao.util.imodel.StringUtils;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.Date;
import java.util.List;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-09-08
 */
@RestController
@RequestMapping("api/fault/plan/m")
public class FaultPlanMController extends BaseController {

    @Autowired
    private FaultPlanMService faultPlanMService;

    @GetMapping
    public IstarResponse getList(@RequestParam(required = false, defaultValue = "") String planName,
                                 @RequestParam(required = false, defaultValue = "") String teamName,
                                 @RequestParam(required = false, defaultValue = "") String userName,
                                 Long startStartTime,
                                 Long startEndTime,
                                 Long endStartTime,
                                 Long endEndTime,
                                 int page, int size) throws ThingsboardException {

        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return IstarResponse.ok(faultPlanMService.getList(planName, teamName, userName, startStartTime == null ? null : new Date(startStartTime), startEndTime == null ? null : new Date(startEndTime), endStartTime == null ? null : new Date(endStartTime), endEndTime == null ? null : new Date(endEndTime), page, size, tenantId));
    }

    @GetMapping("detail/{mainId}")
    public IstarResponse getDetail(@PathVariable String mainId) {
        return IstarResponse.ok(faultPlanMService.getDetail(mainId));
    }

    @PostMapping
    public IstarResponse save(@RequestBody FaultPlanM faultPlanM) throws ThingsboardException {
        // 审核过不许修改
        if (!StringUtils.isNullOrEmpty(faultPlanM.getId()) && !"0".equals(faultPlanM.getStatus())) {
            return IstarResponse.error("改计划已审核，不允许修改！");
        }
        faultPlanM.setCreator(UUIDConverter.fromTimeUUID(getCurrentUser().getUuidId()));
        faultPlanM.setTenantId(UUIDConverter.fromTimeUUID(getTenantId().getId()));
        return IstarResponse.ok(faultPlanMService.save(faultPlanM));
    }

    @PostMapping("reviewer")
    public IstarResponse reviewer(@RequestBody FaultPlanM faultPlanM) throws ThingsboardException {
        faultPlanM.setReviewer(UUIDConverter.fromTimeUUID(getCurrentUser().getUuidId()));
        faultPlanMService.reviewer(faultPlanM);

        return IstarResponse.ok("审核成功");
    }

    @DeleteMapping
    public IstarResponse delete(@RequestBody List<String> ids) {
        return faultPlanMService.delete(ids);
    }
}
