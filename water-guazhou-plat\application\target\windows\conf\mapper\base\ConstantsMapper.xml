<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.base.ConstantsMapper">
    
    <resultMap type="org.thingsboard.server.dao.model.sql.base.ConstantsAttributeList" id="ConstantsAttributeResult">
        <result property="id"    column="id"    />
        <result property="type"    column="type"    />
        <result property="key"    column="key"    />
        <result property="value"    column="value"    />
    </resultMap>

    <sql id="selectConstantsAttributeVo">
        select id, type, key, value from constants_attribute
    </sql>

    <select id="selectConstantsList" parameterType="org.thingsboard.server.dao.model.sql.base.ConstantsAttributeList" resultMap="ConstantsAttributeResult">
        <include refid="selectConstantsAttributeVo"/>
        <where>  
            <if test="type != null  and type != ''"> and type like concat('%', #{type}, '%')</if>
            <if test="key != null  and key != ''"> and key = #{key}</if>
            <if test="value != null  and value != ''"> and value = #{value}</if>
        </where>
    </select>
</mapper>