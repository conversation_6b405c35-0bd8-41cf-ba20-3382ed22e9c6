package org.thingsboard.server.controller.smartOperation.construction.project;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralType;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoGeneralTypePageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoGeneralTypeSaveRequest;
import org.thingsboard.server.dao.construction.project.SoGeneralTypeService;
import org.thingsboard.server.utils.imodel.annotations.IStarController2;

@IStarController2
@RequestMapping("/api/so/generalType")
public class SoGeneralTypeController extends BaseController {
    @Autowired
    private SoGeneralTypeService service;


    // @GetMapping
    public IPage<SoGeneralType> findAllConditional(SoGeneralTypePageRequest request) {
        return service.findAllConditional(request);
    }

    @PostMapping
    public SoGeneralType save(@RequestBody SoGeneralTypeSaveRequest req) {
        return service.save(req);
    }

    @PatchMapping("/{id}")
    public boolean edit(@RequestBody SoGeneralTypeSaveRequest req, @PathVariable String id) {
        return service.update(req.unwrap(id));
    }

    @DeleteMapping("/{id}")
    public boolean delete(@PathVariable String id) {
        return service.delete(id);
    }
}