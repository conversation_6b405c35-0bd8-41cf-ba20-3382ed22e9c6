import{_ as x}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as W}from"./CardTable-rdWOL4_6.js";import{_ as C}from"./CardSearch-CB_HNR-Q.js";import{z as h,d as N,c as _,r as p,a8 as k,ep as q,x as u,o as D,g as S,n as V,q as d,i as m,ey as w,a9 as M}from"./index-r0dFAfgr.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";const I=n=>h({url:"/api/systemNotifyType/list",method:"get",params:n}),L=n=>h({url:"/api/systemNotifyType",method:"post",data:n}),z={class:"wrapper"},A=N({__name:"messageType",setup(n){const f=_(),l=_(),g=[{label:"有效",value:"1",color:"#42eb45"},{label:"无效",value:"0",color:"#f02929"}],y=[{label:"短信",value:"1"},{label:"邮件",value:"2"}],c=[{label:"通知",value:"0"},{label:"告警",value:"1"},{label:"待办",value:"2"}],v=p({filters:[{label:"消息类型",field:"type",type:"select",options:c}],operations:[{type:"btn-group",btns:[{perm:!0,text:"新增",iconifyIcon:"ep:circle-plus",type:"success",click:()=>{var t;i.title="新增消息类型",i.defaultValue={},(t=l.value)==null||t.openDialog()}},{perm:!0,text:"查询",iconifyIcon:"ep:search",click:()=>r()}]}]}),o=p({dataList:[],operationWidth:220,indexVisible:!0,columns:[{minWidth:120,label:"唯一标识",prop:"code"},{minWidth:120,label:"业务模块",prop:"model"},{minWidth:120,label:"消息类型",prop:"type",formatter:t=>{var e;return(e=c.find(a=>a.value===t.type))==null?void 0:e.label}},{minWidth:120,label:"跳转菜单",prop:"menuName"},{minWidth:120,label:"跳转路由",prop:"route"},{minWidth:120,label:"发送类型",prop:"sendType",formatter:t=>{var e;return(e=y.find(a=>a.value===t.sendType))==null?void 0:e.label}},{minWidth:120,label:"状态",prop:"status",tag:!0,tagColor:t=>{var e;return((e=g.find(a=>a.value===t.status))==null?void 0:e.color)||""},formatter:t=>{var e;return(e=g.find(a=>a.value===t.status))==null?void 0:e.label}}],operations:[{perm:!0,text:"编辑",iconifyIcon:"ep:edit-pen",click:t=>{var e;i.title="编辑消息类型",i.defaultValue={...t},(e=l.value)==null||e.openDialog()}}],pagination:{page:1,limit:20,total:0,refreshData:({page:t,size:e})=>{o.pagination.page=t,o.pagination.limit=e,r()}}}),i=p({defaultValue:{route:"",menuName:""},title:"消息类型",dialogWidth:"500px",group:[{fields:[{type:"input",label:"名称",field:"model",rules:[{required:!0,message:"请输入名称"}]},{type:"select",label:"消息类型",field:"type",rules:[{required:!0,message:"请选择消息类型"}],options:c},{type:"select-tree",label:"跳转菜单",field:"path",rules:[{required:!0,message:"请选择跳转菜单"}],options:k(()=>b.data),onChange:t=>{q(t).then(e=>{var a,s;(s=(a=l.value)==null?void 0:a.refForm)!=null&&s.dataForm&&(l.value.refForm.dataForm.route=e.data.component,l.value.refForm.dataForm.menuName=e.data.meta.title)})}},{type:"select",label:"发送类型",field:"sendType",rules:[{required:!0,message:"请选择发送类型"}],options:y},{type:"select",label:"状态",field:"status",options:[{label:"有效",value:"1"},{label:"无效",value:"0"}]}]}],submit:t=>{let e="新增";t.id&&(e="编辑"),L(t).then(a=>{var s;a.data.code===200?(u.success(e+"成功"),(s=l.value)==null||s.closeDialog(),r()):u.warning(a.data.message)}).catch(a=>{u.warning(a)})}}),b=p({data:[]}),T=async()=>{const t=await w();b.data=M(t.data||[],"children",{label:"label",value:"id"})},r=()=>{var e;const t={size:o.pagination.limit,page:o.pagination.page,...((e=f.value)==null?void 0:e.queryParams)||{}};I(t).then(a=>{o.dataList=a.data.data.data||[],o.pagination.total=a.data.data.total||0})};return D(()=>{r(),T()}),(t,e)=>{const a=C,s=W,F=x;return S(),V("div",z,[d(a,{ref_key:"refSearch",ref:f,config:m(v)},null,8,["config"]),d(s,{class:"card-table",config:m(o)},null,8,["config"]),d(F,{ref_key:"refForm",ref:l,config:m(i)},null,8,["config"])])}}});export{A as default};
