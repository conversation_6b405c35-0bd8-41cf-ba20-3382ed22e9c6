import{_ as m}from"./index-C9hz-UZb.js";import{d as u,c as i,o as f,u as d,g as t,n as S,i as e,h as n,F as a,q as _,an as o,C as l}from"./index-r0dFAfgr.js";import{_ as O}from"./ShortMessage.vue_vue_type_script_setup_true_lang-D8NfAq4A.js";import{_ as I}from"./EmailSetting.vue_vue_type_script_setup_true_lang-BnCi-hJ2.js";import C from"./EnterpriseSetting-OTIZhyFl.js";/* empty css                                                                         */import N from"./AppSetting-CMSbxe53.js";import"./formValidate-U0WTqY4Y.js";import"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import"./DateFormatter-Bm9a68Ax.js";const w={class:"wrapper overlay-y"},E=u({__name:"sysSetting",setup(g){const r=i(!0),c=i(window.SITE_CONFIG.LOGIN_CONFIG.SHOWFOOTER&&window.SITE_CONFIG.LOGIN_CONFIG.SHOWQRCODE),p=i(window.SITE_CONFIG.SHORTMESSAGE??!0);return f(()=>{r.value=d().roles[0]==="SYS_ADMIN"}),(y,G)=>{const s=m;return t(),S("div",w,[!e(r)&&e(p)?(t(),n(s,{key:0,title:"短信配置"},{default:a(()=>[_(O)]),_:1})):o("",!0),e(r)?(t(),n(s,{key:1,title:"邮箱配置"},{default:a(()=>[_(I)]),_:1})):o("",!0),e(r)?o("",!0):(t(),n(s,{key:2,title:"企业配置"},{default:a(()=>[_(C)]),_:1})),e(c)?(t(),n(s,{key:3,title:"APP配置"},{default:a(()=>[_(N)]),_:1})):o("",!0),o("",!0)])}}}),L=l(E,[["__scopeId","data-v-f1cfd746"]]);export{L as default};
