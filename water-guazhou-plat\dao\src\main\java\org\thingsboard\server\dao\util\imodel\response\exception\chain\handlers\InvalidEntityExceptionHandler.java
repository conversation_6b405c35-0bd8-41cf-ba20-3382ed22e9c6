package org.thingsboard.server.dao.util.imodel.response.exception.chain.handlers;


import org.thingsboard.server.dao.util.imodel.response.exception.InvalidEntityException;
import org.thingsboard.server.dao.util.imodel.response.model.ReturnHelper;
import org.thingsboard.server.dao.util.imodel.response.exception.chain.ExceptionHandler;
import org.thingsboard.server.dao.util.imodel.response.exception.chain.ExceptionResolvingChain;
import org.thingsboard.server.dao.util.imodel.response.model.IModel;

public class InvalidEntityExceptionHandler implements ExceptionHandler<InvalidEntityException> {
    @Override
    public boolean canHandle(Throwable e) {
        return e instanceof InvalidEntityException;
    }

    @Override
    public void handle(ExceptionResolvingChain chain, ReturnHelper wrap, InvalidEntityException e) throws Throwable {
        IModel model = wrap.getModel();
        model.validationFailureDelegate().invoke(e);
    }
}
