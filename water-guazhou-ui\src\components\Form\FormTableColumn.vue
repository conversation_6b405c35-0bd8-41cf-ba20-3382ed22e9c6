<template>
  <el-table-column
    v-if="!props.config.hidden"
    :key="config.prop"
    :prop="config.prop"
    :label="
      (config.preUnit || '') +
      config.label +
      ((config.unit && ' ' + config.unit) || '')
    "
    :width="config.width"
    :align="config.align || 'left'"
    :min-width="config.minWidth"
    :fixed="config.fixed"
    :show-overflow-tooltip="showOverflowTooltip"
    :sortable="config.sortable || false"
  >
    <template v-if="config.headerFormItemConfig" #header>
      <template v-if="config.headerFormItemConfig">
        <FormItem
          v-if="config.headerFormItemConfig?.field"
          v-model="value"
          :row="row"
          :config="config.headerFormItemConfig"
          @change="
            () => {
              config.headerFormItemConfig?.onChange &&
                config.headerFormItemConfig.onChange(value, row);
              $emit('change', value);
            }
          "
        ></FormItem>
        <FormItem
          v-else
          :row="row"
          :config="config.headerFormItemConfig"
        ></FormItem>
      </template>
    </template>
    <template #default="scope">
      <template v-if="config.subColumns?.length">
        <template v-for="(subColumn, i) in config.subColumns" :key="i">
          <form-table-column :config="subColumn"></form-table-column>
        </template>
      </template>
      <template v-else>
        <template v-if="config.prop">
          <!-- 用来处理每一行的配置都不相同的情况，比如第一行要输入框，但是第二行要下拉框，或者是下拉框的内容需要根据每行的数据进行变动的情况 -->
          <FormItem
            v-if="scope.row[config.prop + 'FormItemConfig']"
            v-model="scope.row[config.prop]"
            :config="scope.row[config.prop + 'FormItemConfig']"
            :row="scope.row"
            @change="
              (val) => {
                scope.row[config.prop + 'FormItemConfig']?.onChange?.(
                  val,
                  scope.row,
                  scope.row[config.prop + 'FormItemConfig']
                );
              }
            "
          ></FormItem>
          <template v-else-if="config.formItemConfig">
            <el-form-item
              v-if="config.formItemConfig?.rules && scope.$index >= 0"
              :prop="
                config.tableDataName + '.' + scope.$index + '.' + config.prop
              "
              :rules="config.formItemConfig.rules"
              class="el_form_item"
            >
              <FormItem
                v-model="scope.row[config.prop]"
                :config="config.formItemConfig"
                :row="scope.row"
                @change="
                  (val) =>
                    config.formItemConfig?.onChange?.(
                      val,
                      scope.row,
                      config.formItemConfig
                    )
                "
                @blur="
                  (val, row) =>
                    config.formItemConfig?.onBlur?.(
                      val,
                      scope.row,
                      config.formItemConfig
                    )
                "
              ></FormItem>
            </el-form-item>
            <FormItem
              v-else
              v-model="scope.row[config.prop]"
              :config="config.formItemConfig"
              :row="scope.row"
              @change="
                (val) =>
                  config.formItemConfig?.onChange?.(
                    val,
                    scope.row,
                    config.formItemConfig
                  )
              "
              @blur="
                (val, row) =>
                  config.formItemConfig?.onBlur?.(
                    val,
                    scope.row,
                    config.formItemConfig
                  )
              "
            ></FormItem>
          </template>

          <template v-else-if="config.image">
            <el-image
              v-if="config.image && scope.row[config.prop]"
              style="width: 100px; height: 100px; margin-top: 5px"
              :style="config.cellStyle"
              :preview-teleported="true"
              :src="resolveElImageData(scope.row[config.prop], false)"
              :hide-on-click-modal="true"
              :preview-src-list="
                resolveElImageData(
                  scope.row[config.prop],
                  typeof config.isPreview === 'undefined'
                    ? true
                    : config.isPreview
                )
              "
              :z-index="9999"
            ></el-image>
          </template>
          <Tag
            v-else-if="config.tag && !!(scope.row[config.prop] !== undefined)"
            :row="scope.row"
            :color="config.tagColor"
            :value="scope.row[config.prop]"
            :closeable="config.tagCloseable"
            :disable-transitions="config.tagDisableTransitions"
            :hit="config.taghit"
            :size="config.tagSize"
            :effect="config.tagEffect"
            :tag-type="config.tagType"
            class="tag-flexable"
          >
            <!-- 表格项 图标 -->
            <Icon v-if="config.iconifyIcon" :icon="config.iconifyIcon"></Icon>
            <el-icon
              v-else-if="config.icon || config.svgIcon || config.symbolIcon"
              :style="
                typeof config.iconStyle === 'function'
                  ? config.iconStyle(scope.row, scope.row[config.prop], config)
                  : config.iconStyle
              "
            >
              <i v-if="config.icon" :class="config.icon"></i>
              <svg
                v-else-if="config.symbolIcon"
                class="symbolIcon"
                aria-hidden="true"
              >
                <use :xlink:href="config.symbolIcon"></use>
              </svg>
              <component :is="config.svgIcon" v-else></component>
            </el-icon>
            <span>
              {{
                config.formatter?.(
                  scope.row,
                  scope.row[config.prop],
                  config.prop
                ) ?? scope.row[config.prop]
              }}
            </span>
          </Tag>
          <Voicer
            v-else-if="config.isVoice"
            :download="config.download"
            :show-url="config.showUrl"
            :url="scope.row[config.prop]"
            :paused="scope.row.paused"
            @playVoice="
              (val) => {
                config.playVoice?.(val, scope.row);
              }
            "
          ></Voicer>
          <div v-else-if="config.isHtml" v-html="scope.row[config.prop]"></div>
          <template v-else>
            <!-- 表格项 图标 -->

            <span
              class="table-cell"
              :style="
                typeof config.cellStyle === 'function'
                  ? config.cellStyle(
                      scope.row,
                      scope.row[config.prop],
                      config.prop
                    )
                  : config.cellStyle
              "
              :class="[
                config.isUrl ? 'is-url' : '',
                config.className,
                config.icon ||
                config.svgIcon ||
                config.symbolIcon ||
                config.iconifyIcon
                  ? 'has-icon'
                  : ''
              ]"
              @click="config.handleClick?.(scope.row, config)"
            >
              <Icon
                v-if="config.iconifyIcon"
                :icon="config.iconifyIcon"
                style="margin-right: 4px"
                :style="
                  typeof config.iconStyle === 'function'
                    ? config.iconStyle(
                        scope.row,
                        scope.row[config.prop],
                        config
                      )
                    : config.iconStyle
                "
              ></Icon>
              <el-icon
                v-else-if="config.icon || config.svgIcon || config.symbolIcon"
                :style="
                  typeof config.iconStyle === 'function'
                    ? config.iconStyle(
                        scope.row,
                        scope.row[config.prop],
                        config
                      )
                    : config.iconStyle
                "
              >
                <i v-if="config.icon" :class="config.icon"></i>
                <svg
                  v-else-if="config.symbolIcon"
                  class="symbolIcon"
                  aria-hidden="true"
                >
                  <use :xlink:href="config.symbolIcon"></use>
                </svg>
                <component :is="config.svgIcon" v-else></component>
              </el-icon>
              <span>
                {{
                  config.formatter?.(
                    scope.row,
                    scope.row[config.prop],
                    config.prop
                  ) ?? scope.row[config.prop]
                }}
              </span>
            </span>
          </template>
        </template>
      </template>
    </template>
  </el-table-column>
</template>
<script lang="ts" setup>
import { Icon } from '@iconify/vue';
import { resolveElImageData } from '@/utils/GlobalHelper';

const emit = defineEmits(['update:modelValue', 'change']);
const props = defineProps<{
  config: IFormTableColumn;
  /** 表头绑定的数据对象 */
  row?: Record<string, any>;
  modelValue?: any;
}>();
const value = computed({
  get: () => props.modelValue,
  set: (nv) => {
    emit('update:modelValue', nv);
  }
});
const showOverflowTooltip = computed(() => {
  if (props.config.formItemConfig) {
    // 是表单时不显示提示信息
    return false;
  } else if (props.config.showOverflowTooltip === undefined) {
    return true;
  } else {
    return props.config.showOverflowTooltip;
  }
});
</script>

<style lang="scss" scoped>
.tag-flexable {
  :deep(.el-tag__content) {
    display: flex;
    align-items: center;
  }
}

.table-cell {
  &.has-icon {
    display: flex;
    align-items: center;
  }

  &.is-url {
    &:hover {
      cursor: pointer;
      text-decoration: underline;
      color: rgb(0, 72, 180);
    }
  }
}

.el-icon + span {
  margin-left: 4px;
}
</style>

<style lang="scss">
.el_form_item {
  margin: 0 0 !important;

  .el-form-item__content {
    margin-left: 0 !important;
  }
}
</style>
