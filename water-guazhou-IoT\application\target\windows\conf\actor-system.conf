#
# Copyright © 2016-2019 The Thingsboard Authors
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#


akka {
  # JVM shutdown, System.exit(-1), in case of a fatal error,
  # such as OutOfMemoryError
  jvm-exit-on-fatal-error = off
  loglevel = "INFO"
  loggers = ["akka.event.slf4j.Slf4jLogger"]
}

# This dispatcher is used for app
app-dispatcher {
  type = Dispatcher
  executor = "fork-join-executor"
  fork-join-executor {
      # Min number of threads to cap factor-based parallelism number to
      parallelism-min = 1
      # Max number of threads to cap factor-based parallelism number to
      parallelism-max = 1
      
      # The parallelism factor is used to determine thread pool size using the
      # following formula: ceil(available processors * factor). Resulting size
      # is then bounded by the parallelism-min and parallelism-max values.
      parallelism-factor = 1.0
  }
  # How long time the dispatcher will wait for new actors until it shuts down
  shutdown-timeout = 1s
  
  # Throughput defines the number of messages that are processed in a batch
  # before the thread is returned to the pool. Set to 1 for as fair as possible.
  throughput = 5
}

# This dispatcher is used for rpc actors
rpc-dispatcher {
  type = Dispatcher
  executor = "fork-join-executor"
  fork-join-executor {
      # Min number of threads to cap factor-based parallelism number to
      parallelism-min = 2
      # Max number of threads to cap factor-based parallelism number to
      parallelism-max = 8

      # The parallelism factor is used to determine thread pool size using the
      # following formula: ceil(available processors * factor). Resulting size
      # is then bounded by the parallelism-min and parallelism-max values.
      parallelism-factor = 0.5
  }
  # How long time the dispatcher will wait for new actors until it shuts down
  shutdown-timeout = 1s

  # Throughput defines the number of messages that are processed in a batch
  # before the thread is returned to the pool. Set to 1 for as fair as possible.
  throughput = 5
}

# This dispatcher is used for auth
core-dispatcher {
  type = Dispatcher
  executor = "fork-join-executor"
  fork-join-executor {
      # Min number of threads to cap factor-based parallelism number to
      parallelism-min = 2
      # Max number of threads to cap factor-based parallelism number to
      parallelism-max = 12

      # The parallelism factor is used to determine thread pool size using the
      # following formula: ceil(available processors * factor). Resulting size
      # is then bounded by the parallelism-min and parallelism-max values.
      parallelism-factor = 0.25
  }
  # How long time the dispatcher will wait for new actors until it shuts down
  shutdown-timeout = 1s

  # Throughput defines the number of messages that are processed in a batch
  # before the thread is returned to the pool. Set to 1 for as fair as possible.
  throughput = 5
}

# This dispatcher is used for system rule chains and rule node actors
system-rule-dispatcher {
  type = Dispatcher
  executor = "fork-join-executor"
  fork-join-executor {
    # Min number of threads to cap factor-based parallelism number to
    parallelism-min = 2
    # Max number of threads to cap factor-based parallelism number to
    parallelism-max = 12

    # The parallelism factor is used to determine thread pool size using the
    # following formula: ceil(available processors * factor). Resulting size
    # is then bounded by the parallelism-min and parallelism-max values.
    parallelism-factor = 0.25
  }
  # How long time the dispatcher will wait for new actors until it shuts down
  shutdown-timeout = 1s

  # Throughput defines the number of messages that are processed in a batch
  # before the thread is returned to the pool. Set to 1 for as fair as possible.
  throughput = 5
}

# This dispatcher is used for tenant rule chains and rule node actors
rule-dispatcher {
  type = Dispatcher
  executor = "fork-join-executor"
  fork-join-executor {
      # Min number of threads to cap factor-based parallelism number to
      parallelism-min = 2
      # Max number of threads to cap factor-based parallelism number to
      parallelism-max = 12

      # The parallelism factor is used to determine thread pool size using the
      # following formula: ceil(available processors * factor). Resulting size
      # is then bounded by the parallelism-min and parallelism-max values.
      parallelism-factor = 0.25
  }
  # How long time the dispatcher will wait for new actors until it shuts down
  shutdown-timeout = 1s

  # Throughput defines the number of messages that are processed in a batch
  # before the thread is returned to the pool. Set to 1 for as fair as possible.
  throughput = 5
}
