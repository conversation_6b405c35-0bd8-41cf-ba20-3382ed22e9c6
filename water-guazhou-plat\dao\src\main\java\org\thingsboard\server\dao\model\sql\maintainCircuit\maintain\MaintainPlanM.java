package org.thingsboard.server.dao.model.sql.maintainCircuit.maintain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 保养计划主表
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-09-08
 */
@TableName("tb_device_maintain_plan_m")
@Data
public class MaintainPlanM {
    @TableId
    private String id;

    private String name;

    private String teamId;

    private transient String teamName;

    private String userId;

    private transient String userName;

    private Date startTime;

    private Date endTime;

    private Integer executionDays;

    private Integer intervalDays;

    private Integer executionNum;

    private String status;

    private String reviewer;

    private transient String reviewerName;

    private transient String reviewerDepartment;

    private String remark;

    private String creator;

    private transient String creatorName;

    private transient List<MaintainPlanC> maintainPlanCList;

    private Date createTime;

    private String tenantId;


}
