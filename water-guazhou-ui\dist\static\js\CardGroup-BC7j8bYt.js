import{d as p,g as e,n as t,aB as d,aJ as i,p as s,bh as o,aw as u,i as _,j as m,C as h}from"./index-r0dFAfgr.js";const y={class:"header"},g=["src"],k={class:"title"},v={class:"content"},f={class:"count"},w={class:"unit"},C={key:1,class:"empty"},B=p({__name:"CardGroup",props:{row:{},movelValue:{}},setup(V){return(r,n)=>{var c;return(c=r.row.supply)!=null&&c.length?(e(),t("div",{key:0,class:u(["card-wrapper",_(m)().isDark?"darkblue":""])},[(e(!0),t(d,null,i(r.row.supply,(a,l)=>(e(),t("div",{key:l,class:"card watersupply"},[s("div",y,[s("img",{class:"image",src:a.img,alt:""},null,8,g),s("span",k,o(a.title),1)]),s("div",v,[s("span",f,o(a.count),1),s("span",w,o(a.unit),1)])]))),128))],2)):(e(),t("div",C,n[0]||(n[0]=[s("span",{class:"empty-text"},"暂无数据",-1)])))}}}),x=h(B,[["__scopeId","data-v-2619c6a7"]]);export{x as default};
