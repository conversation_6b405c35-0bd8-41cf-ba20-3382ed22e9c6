package org.thingsboard.server.dao.sql.dataSource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.dataSource.RestApiDao;
import org.thingsboard.server.dao.model.sql.RestApiEntity;

import javax.transaction.Transactional;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/2/26 15:05
 */
@Service
@Transactional
public class JpaRestApiDao implements RestApiDao {

    @Autowired
    private RestApiRepository restApiRepository;

    @Override
    public RestApiEntity save(RestApiEntity restApiEntity) {
        return restApiRepository.save(restApiEntity);
    }

    @Override
    public List<RestApiEntity> findByTenantId(TenantId tenantId) {
        return restApiRepository.findByTenantIdOrderByUpdateTime(UUIDConverter.fromTimeUUID(tenantId.getId()));
    }

    @Override
    public List<RestApiEntity> findByProjectId(String projectId) {
        return restApiRepository.findByProjectIdOrderByUpdateTime(projectId);
    }

    @Override
    public RestApiEntity findById(String id) {
        return restApiRepository.findOne(id);
    }

    @Override
    public boolean deleteById(String id) {
        restApiRepository.delete(id);
        return true;
    }


    @Override
    public List<RestApiEntity> findAll() {
        return restApiRepository.findAll();
    }

    @Override
    public int countRestApiByName(String tenantId, String name) {
        return restApiRepository.countByTenantIdAndName(tenantId,name);
    }
}
