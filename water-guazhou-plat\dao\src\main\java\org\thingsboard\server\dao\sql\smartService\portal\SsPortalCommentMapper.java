package org.thingsboard.server.dao.sql.smartService.portal;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.thingsboard.server.dao.model.sql.smartService.portal.SsPortalComment;
import org.thingsboard.server.dao.util.imodel.query.smartService.portal.SsPortalCommentPageRequest;

@Mapper
public interface SsPortalCommentMapper extends BaseMapper<SsPortalComment> {
    IPage<SsPortalComment> findByPage(SsPortalCommentPageRequest request);

    @SuppressWarnings("methodNotInXmlInspection")
    boolean update(SsPortalComment entity);

    boolean updateFully(SsPortalComment entity);

    boolean canSave(String tenantId);

}
