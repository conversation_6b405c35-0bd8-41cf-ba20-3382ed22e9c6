package org.thingsboard.server.dao.shuiwu;

import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.shuiwu.PipeEntity;
import org.thingsboard.server.dao.model.sql.shuiwu.PipePointEntity;

import java.util.List;

public interface PipeService {
    PipeEntity findById(String id);

    PageData<PipeEntity> findList(int page, int size, String keyword, TenantId tenantId);

    void save(PipeEntity entity);

    void deleteById(String id);

    PipePointEntity findPipePointById(String pipePointId);

    PageData<PipePointEntity> findPipePointList(int page, int size, String keyword, TenantId tenantId);

    void savePipePoint(PipePointEntity entity);

    void removePipePoints(List<String> ids);

    void deleteById(List<String> ids);

    List<PipeEntity> findAll(TenantId tenantId);
}
