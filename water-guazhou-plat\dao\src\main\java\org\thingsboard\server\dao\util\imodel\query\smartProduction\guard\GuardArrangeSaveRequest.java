package org.thingsboard.server.dao.util.imodel.query.smartProduction.guard;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartProduction.guard.GuardArrange;
import org.thingsboard.server.dao.util.TimeUtils;
import org.thingsboard.server.dao.util.imodel.query.ComplexSaveRequest;
import org.thingsboard.server.dao.util.imodel.query.IStarHttpRequest;
import org.thingsboard.server.dao.util.imodel.query.StringSetter;

import java.time.LocalTime;
import java.util.Date;

@Getter
@Setter
public class GuardArrangeSaveRequest extends ComplexSaveRequest<GuardArrange, GuardArrangePartnerSaveRequest> {
    // 地点id
    private String placeId;

    // 值班日期
    private Date dayTime;

    // 开始时间
    private String beginTime;

    // 结束时间
    private String endTime;

    // 班组部门id
    private String departmentId;

    // 值班长id
    private String head;

    @Override
    public String valid(IStarHttpRequest request) {
        try {
            LocalTime.parse(beginTime);
            LocalTime.parse(endTime);
        } catch(Exception e) {
            return "时间格式有误";
        }
        return super.valid(request);
    }


    @Override
    protected GuardArrange build() {
        GuardArrange entity = new GuardArrange();
        entity.setCreator(currentUserUUID());
        entity.setCreateTime(createTime());
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    @Override
    protected GuardArrange update(String id) {
        GuardArrange entity = new GuardArrange();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(GuardArrange entity) {
        entity.setPlaceId(placeId);
        entity.setClassName(GuardArrange.TEMP_GUARD);
        entity.setDayTime(TimeUtils.floorDay(dayTime));
        entity.setBeginTime(beginTime);
        entity.setEndTime(endTime);
        entity.setDepartmentId(departmentId);
        entity.setHead(head);
        entity.setUpdateTime(createTime());
        entity.setUpdateUserId(currentUserUUID());
    }

    @Override
    protected boolean allowUpdate() {
        return false;
    }

    @Override
    protected StringSetter<GuardArrangePartnerSaveRequest> parentSetter() {
        return GuardArrangePartnerSaveRequest::setArrangeId;
    }

}