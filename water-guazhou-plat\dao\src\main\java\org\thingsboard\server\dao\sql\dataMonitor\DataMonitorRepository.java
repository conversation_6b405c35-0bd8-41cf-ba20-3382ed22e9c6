/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.sql.dataMonitor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.thingsboard.server.common.data.dataMonitor.DataMonitor;
import org.thingsboard.server.dao.model.sql.DashboardEntity;
import org.thingsboard.server.dao.model.sql.DataMonitorEntity;
import org.thingsboard.server.dao.model.sql.EnergyEntity;
import org.thingsboard.server.dao.model.sql.InputKvEntity;
import org.thingsboard.server.dao.util.SqlDao;

import java.util.List;

/**
 * Created by Valerii Sosliuk on 5/21/2017.
 */
@SqlDao
public interface DataMonitorRepository extends CrudRepository<DataMonitorEntity, String> {


    List<DataMonitorEntity> findByTenantId(@Param("tenant_id") String tenantId);


    List<DataMonitorEntity> findByDeviceId(@Param("device_id") String deviceId);


    @Query("SELECT a FROM DataMonitorEntity a WHERE a.tenantId = :tenantId AND a.time >= :start and a.time <=:endTime")
    List<DataMonitorEntity> findByTenantIdAndtime(@Param("tenantId") String tenantId, @Param("start") long start, @Param("endTime") long endTime);


    @Query("SELECT a FROM DataMonitorEntity a WHERE a.deviceId = :deviceId AND a.time >= :start and a.time <=:endTime")
    List<DataMonitorEntity> findByDeviceIdAndtime(@Param("deviceId") String deviceId, @Param("start") long start, @Param("endTime") long endTime);

}
