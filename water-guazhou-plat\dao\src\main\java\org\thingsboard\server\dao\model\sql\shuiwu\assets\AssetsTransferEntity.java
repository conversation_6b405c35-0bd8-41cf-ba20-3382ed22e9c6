package org.thingsboard.server.dao.model.sql.shuiwu.assets;

import lombok.Data;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;
import java.util.List;

/**
 * 设备台账
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2021-10-19
 */
@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.ASSETS_TRANSFER_TABLE)
public class AssetsTransferEntity {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.ASSETS_TRANSFER_TRANSFER_NO)
    private String transferNo;

    @Column(name = ModelConstants.ASSETS_TRANSFER_PROJECT_ID)
    private String projectId;

    @Column(name = ModelConstants.ASSETS_TRANSFER_POSITION)
    private String position;

    @Column(name = ModelConstants.ASSETS_TRANSFER_DIRECTOR)
    private String director;

    @Column(name = ModelConstants.ASSETS_TRANSFER_REMARK)
    private String remark;

    @Column(name = ModelConstants.ASSETS_TRANSFER_DEVICE_IDS)
    private String deviceIds;

    @Column(name = ModelConstants.ASSETS_TRANSFER_APPLICANT_ID)
    private String applicantId;

    @Column(name = ModelConstants.ASSETS_TRANSFER_REVIEWER_ID)
    private String reviewerId;

    @Column(name = ModelConstants.ASSETS_TRANSFER_STATUS)
    private String status;

    @Column(name = ModelConstants.ASSETS_TRANSFER_CREATE_TIME)
    private Long createTime;

    @Column(name = ModelConstants.ASSETS_TRANSFER_UPDATE_TIME)
    private Long updateTime;

    @Column(name = ModelConstants.ASSETS_TRANSFER_TENANT_ID)
    private String tenantId;

    private transient String reviewerName;

    private transient String applicantName;

    private transient List<AssetsAccountEntity> assetsAccountList;

    private transient String projectName;
}
