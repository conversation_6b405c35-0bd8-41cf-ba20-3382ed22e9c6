package org.thingsboard.server.dao.sql.report;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.report.ReportDatabase;

@Mapper
public interface ReportDatabaseMapper extends BaseMapper<ReportDatabase> {
    IPage<ReportDatabase> getList(IPage<ReportDatabase> page, @Param("name") String name, @Param("tenantId") String tenantId);
}
