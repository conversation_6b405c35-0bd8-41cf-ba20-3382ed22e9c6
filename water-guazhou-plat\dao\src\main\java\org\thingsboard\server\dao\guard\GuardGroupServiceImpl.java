package org.thingsboard.server.dao.guard;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.dao.model.sql.smartProduction.guard.GuardGroup;
import org.thingsboard.server.dao.sql.smartProduction.guard.GuardGroupMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.guard.GuardGroupPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.guard.GuardGroupPartnerSaveRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.guard.GuardGroupSaveRequest;

import java.util.List;

@Service
public class GuardGroupServiceImpl implements GuardGroupService {
    @Autowired
    private GuardGroupMapper mapper;

    @Autowired
    private GuardGroupPartnerService guardGroupPartnerService;


    @Override
    public GuardGroup findById(String id) {
        return mapper.selectById(id);
    }

    @Override
    public IPage<GuardGroup> findAllConditional(GuardGroupPageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    @Transactional
    public GuardGroup save(GuardGroupSaveRequest entity) {
        GuardGroup group = QueryUtil.saveOrUpdateOneByRequest(entity, mapper::insert, mapper::updateFully);

        List<GuardGroupPartnerSaveRequest> partners = entity.getItems(group.getId());
        guardGroupPartnerService.replaceAll(group.getId(), partners);

        return group;
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }

}
