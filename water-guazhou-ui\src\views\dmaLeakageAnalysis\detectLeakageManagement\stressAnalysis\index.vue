<template>
  <div class="app-container">
    <!-- 筛选区域 -->
    <el-card class="filter-container">
      <el-form :inline="true" :model="filterForm">
        <el-form-item label="分区">
          <el-select v-model="filterForm.partitionId" placeholder="请选择分区">
            <el-option
              v-for="item in partitionOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
      </el-form-item>
      <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="resetFilter">重置</el-button>
      </el-form-item>
    </el-form>
    </el-card>

    <!-- 主要内容区域 -->
    <el-row :gutter="20" class="main-content">
      <!-- 左侧压力分布图 -->
      <el-col :span="16">
        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <span>区域压力分布</span>
              <div class="header-right">
                <el-radio-group v-model="pressureViewType" size="small">
                  <el-radio-button label="realtime">实时</el-radio-button>
                  <el-radio-button label="daily">日均</el-radio-button>
                  <el-radio-button label="weekly">周均</el-radio-button>
                </el-radio-group>
              </div>
            </div>
          </template>
          
          <!-- 气泡图 -->
          <div class="view-toggle-container">
            <el-button-group size="small">
              <el-button :type="viewMode === 'map' ? 'primary' : ''" @click="viewMode = 'map'">地图气泡图</el-button>
              <el-button :type="viewMode === 'bar' ? 'primary' : ''" @click="viewMode = 'bar'">监测点柱状图</el-button>
            </el-button-group>
          </div>
          
          <!-- 地图气泡图 -->
          <div v-show="viewMode === 'map'" ref="pressureBubbleMap" class="map-container"></div>
          
          <!-- 柱状图 -->
          <div v-show="viewMode === 'bar'" ref="pressureBarChart" class="chart-container"></div>
          
          <div class="pressure-legend">
            <div class="legend-title">压力值 (MPa)</div>
            <div class="legend-items">
              <div class="legend-item">
                <div class="color-box high-pressure-bg"></div>
                <span>高压 (>0.5)</span>
              </div>
              <div class="legend-item">
                <div class="color-box normal-pressure-bg"></div>
                <span>正常 (0.2-0.5)</span>
              </div>
              <div class="legend-item">
                <div class="color-box low-pressure-bg"></div>
                <span>欠压 (<0.2)</span>
              </div>
            </div>
          </div>
        </el-card>
        
        <!-- 异常区域列表 -->
        <el-card class="box-card anomaly-list">
          <template #header>
            <div class="card-header">
              <span>压力异常区域</span>
            </div>
          </template>
          <el-table :data="anomalyAreas" style="width: 100%">
            <el-table-column prop="areaName" label="区域名称" />
            <el-table-column prop="pressureValue" label="压力值(MPa)">
              <template #default="scope">
                <span :class="getPressureClass(scope.row.pressureValue)">{{ scope.row.pressureValue }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="anomalyType" label="异常类型">
              <template #default="scope">
                <el-tag :type="scope.row.anomalyType === '高压' ? 'danger' : 'warning'">{{ scope.row.anomalyType }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="duration" label="持续时间" />
            <el-table-column label="操作" width="120">
        <template #default="scope">
                <el-button size="small" @click="handleViewAreaDetail(scope.row)">查看详情</el-button>
        </template>
      </el-table-column>
    </el-table>
        </el-card>
      </el-col>
      
      <!-- 右侧统计信息 -->
      <el-col :span="8">
        <!-- 压力区间分布 -->
        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <span>压力区间分布</span>
            </div>
          </template>
          <div ref="pressureDistributionChart" class="chart-container"></div>
        </el-card>
        
        <!-- 压力变化趋势 -->
        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <span>压力变化趋势</span>
            </div>
          </template>
          <div ref="pressureTrendChart" class="chart-container"></div>
        </el-card>
        
        <!-- 压力异常统计 -->
        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <span>压力异常统计</span>
            </div>
          </template>
          <div class="stats-container">
            <div class="stats-item high-pressure">
              <div class="stats-title">高压点位</div>
              <div class="stats-value">{{ highPressureStats.count }}</div>
              <div class="stats-detail">最高压力: {{ highPressureStats.max }} MPa</div>
            </div>
            <div class="stats-item normal-pressure">
              <div class="stats-title">正常点位</div>
              <div class="stats-value">{{ normalPressureStats.count }}</div>
              <div class="stats-detail">平均压力: {{ normalPressureStats.avg }} MPa</div>
            </div>
            <div class="stats-item low-pressure">
              <div class="stats-title">欠压点位</div>
              <div class="stats-value">{{ lowPressureStats.count }}</div>
              <div class="stats-detail">最低压力: {{ lowPressureStats.min }} MPa</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, reactive } from 'vue'
import * as echarts from 'echarts'
import { ElMessage } from 'element-plus'

// 引用图表容器
const pressureBubbleMap = ref()
const pressureBarChart = ref()
const pressureDistributionChart = ref()
const pressureTrendChart = ref()

// 视图模式：map-气泡图，bar-柱状图
const viewMode = ref('map')

// 筛选表单
const filterForm = reactive({
  partitionId: ''
})

// 压力视图类型
const pressureViewType = ref('realtime')

// 分区选项
const partitionOptions = [
  { label: '城区A分区', value: 'A' },
  { label: '城区B分区', value: 'B' },
  { label: '城区C分区', value: 'C' },
  { label: '郊区D分区', value: 'D' }
]

// 监测点数据
const monitoringPoints = reactive([
  { id: 1, name: '监测点A1', x: 10, y: 20, pressure: 0.58, status: '高压' },
  { id: 2, name: '监测点A2', x: 30, y: 40, pressure: 0.32, status: '正常' },
  { id: 3, name: '监测点B1', x: 50, y: 60, pressure: 0.15, status: '欠压' },
  { id: 4, name: '监测点B2', x: 70, y: 80, pressure: 0.42, status: '正常' },
  { id: 5, name: '监测点C1', x: 90, y: 100, pressure: 0.62, status: '高压' },
  { id: 6, name: '监测点C2', x: 110, y: 120, pressure: 0.28, status: '正常' },
  { id: 7, name: '监测点D1', x: 130, y: 140, pressure: 0.18, status: '欠压' },
  { id: 8, name: '监测点D2', x: 150, y: 160, pressure: 0.35, status: '正常' }
])

// 压力异常区域数据
const anomalyAreas = ref([
  { areaName: '城区A分区北部', pressureValue: 0.58, anomalyType: '高压', duration: '2小时15分钟' },
  { areaName: '城区B分区东部', pressureValue: 0.15, anomalyType: '欠压', duration: '1小时30分钟' },
  { areaName: '郊区D分区南部', pressureValue: 0.62, anomalyType: '高压', duration: '3小时40分钟' },
  { areaName: '城区C分区西部', pressureValue: 0.18, anomalyType: '欠压', duration: '45分钟' }
])

// 压力统计数据
const highPressureStats = reactive({ count: 12, max: 0.62 })
const normalPressureStats = reactive({ count: 68, avg: 0.35 })
const lowPressureStats = reactive({ count: 8, min: 0.15 })

// 根据压力值获取样式类
const getPressureClass = (value) => {
  if (value > 0.5) return 'high-pressure-text'
  if (value < 0.2) return 'low-pressure-text'
  return 'normal-pressure-text'
}

// 根据压力值获取颜色
const getPressureColor = (value) => {
  if (value > 0.5) return '#ff4d4f'  // 高压红色
  if (value < 0.2) return '#1890ff'  // 欠压蓝色
  return '#52c41a'  // 正常绿色
}

// 搜索处理
const handleSearch = () => {
  ElMessage.success('正在加载数据...')
  // 这里添加实际的数据请求逻辑
  initCharts()
}

// 重置筛选条件
const resetFilter = () => {
  filterForm.partitionId = ''
}

// 查看区域详情
const handleViewAreaDetail = (row) => {
  ElMessage.info(`查看 "${row.areaName}" 的详细信息`)
}

// 初始化气泡图
const initBubbleMap = () => {
  const chart = echarts.init(pressureBubbleMap.value)
  
  // 模拟地图背景
  const mapData = []
  for (let i = 0; i < 200; i += 10) {
    for (let j = 0; j < 200; j += 10) {
      mapData.push([i, j])
    }
  }
  
  // 监测点数据
  const bubbleData = monitoringPoints.map(point => {
    return {
      name: point.name,
      value: [point.x, point.y, point.pressure],
      itemStyle: {
        color: getPressureColor(point.pressure)
      }
    }
  })
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: (params) => {
        return `${params.name}<br/>压力值: ${params.value[2]} MPa`
      }
    },
    xAxis: {
      show: false,
      min: 0,
      max: 200
    },
    yAxis: {
      show: false,
      min: 0,
      max: 200
    },
    series: [
      {
        // 模拟地图背景
        type: 'scatter',
        data: mapData,
        symbolSize: 1,
        itemStyle: {
          color: '#eee'
        },
        zlevel: 1
      },
      {
        name: '压力监测点',
        type: 'scatter',
        data: bubbleData,
        symbolSize: (data) => {
          // 根据压力值动态调整气泡大小
          return Math.max(20, data[2] * 100)
        },
        label: {
          show: true,
          formatter: (param) => {
            return param.name
          },
          position: 'top',
          fontSize: 12
        },
        emphasis: {
          label: {
            show: true
          }
        },
        zlevel: 2
      }
    ]
  }
  
  chart.setOption(option)
  
  // 监听窗口大小变化，调整图表大小
  window.addEventListener('resize', () => {
    chart.resize()
  })
}

// 初始化柱状图
const initBarChart = () => {
  const chart = echarts.init(pressureBarChart.value)
  
  // 准备数据
  const categories = monitoringPoints.map(point => point.name)
  const data = monitoringPoints.map(point => point.pressure)
  const colors = monitoringPoints.map(point => getPressureColor(point.pressure))
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: '{b}: {c} MPa'
    },
    xAxis: {
      type: 'category',
      data: categories,
      axisLabel: {
        interval: 0,
        rotate: 30
      }
    },
    yAxis: {
      type: 'value',
      name: '压力值(MPa)',
      min: 0,
      max: 0.7,
      splitLine: {
        show: true,
        lineStyle: {
          type: 'dashed'
        }
      }
    },
    series: [
      {
        data: data.map((value, index) => {
          return {
            value: value,
            itemStyle: {
              color: colors[index]
            }
          }
        }),
        type: 'bar',
        barWidth: '40%',
        label: {
          show: true,
          position: 'top',
          formatter: '{c} MPa'
        }
      },
      {
        // 高压警戒线
        name: '高压警戒线',
        type: 'line',
        markLine: {
          silent: true,
          lineStyle: {
            color: '#ff4d4f',
            type: 'dashed'
          },
          data: [
            {
              yAxis: 0.5,
              label: {
                formatter: '高压警戒线: 0.5 MPa',
                position: 'end'
              }
            }
          ]
        }
      },
      {
        // 低压警戒线
        name: '低压警戒线',
        type: 'line',
        markLine: {
          silent: true,
          lineStyle: {
            color: '#1890ff',
            type: 'dashed'
          },
          data: [
            {
              yAxis: 0.2,
              label: {
                formatter: '低压警戒线: 0.2 MPa',
                position: 'end'
              }
            }
          ]
        }
      }
    ]
  }
  
  chart.setOption(option)
  
  // 监听窗口大小变化，调整图表大小
  window.addEventListener('resize', () => {
    chart.resize()
  })
}

// 初始化压力区间分布图
const initPressureDistributionChart = () => {
  const chart = echarts.init(pressureDistributionChart.value)
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      data: ['高压', '正常', '欠压']
    },
    series: [
      {
        name: '压力区间分布',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { value: highPressureStats.count, name: '高压', itemStyle: { color: '#ff4d4f' } },
          { value: normalPressureStats.count, name: '正常', itemStyle: { color: '#52c41a' } },
          { value: lowPressureStats.count, name: '欠压', itemStyle: { color: '#1890ff' } }
        ]
      }
    ]
  }
  
  chart.setOption(option)
  
  // 监听窗口大小变化，调整图表大小
  window.addEventListener('resize', () => {
    chart.resize()
  })
}

// 初始化压力变化趋势图
const initPressureTrendChart = () => {
  const chart = echarts.init(pressureTrendChart.value)
  
  // 生成过去24小时的时间点
  const hours = []
  for (let i = 0; i < 24; i++) {
    const hour = 24 - i
    hours.unshift(hour + ':00')
  }
  
  // 生成模拟的压力数据
  const generatePressureData = (base, variance) => {
    return Array.from({ length: 24 }, () => {
      return (base + (Math.random() - 0.5) * variance).toFixed(2)
    })
  }
  
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['平均压力', '最高压力', '最低压力']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: hours,
      axisLabel: {
        interval: 3
      }
    },
    yAxis: {
      type: 'value',
      name: '压力值(MPa)',
      min: 0,
      max: 0.7
    },
    series: [
      {
        name: '平均压力',
        type: 'line',
        data: generatePressureData(0.35, 0.1),
        itemStyle: {
          color: '#52c41a'
        }
      },
      {
        name: '最高压力',
        type: 'line',
        data: generatePressureData(0.55, 0.15),
        itemStyle: {
          color: '#ff4d4f'
        }
      },
      {
        name: '最低压力',
        type: 'line',
        data: generatePressureData(0.18, 0.08),
        itemStyle: {
          color: '#1890ff'
        }
      }
    ]
  }
  
  chart.setOption(option)
  
  // 监听窗口大小变化，调整图表大小
  window.addEventListener('resize', () => {
    chart.resize()
  })
}

// 初始化所有图表
const initCharts = () => {
  initBubbleMap()
  initBarChart()
  initPressureDistributionChart()
  initPressureTrendChart()
}

// 页面加载完成后初始化图表
onMounted(() => {
  initCharts()
})
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.filter-container {
  margin-bottom: 20px;
}

.main-content {
  margin-bottom: 20px;
}

.box-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-right {
  display: flex;
  align-items: center;
}

.map-container,
.chart-container {
  height: 300px;
  width: 100%;
}

.view-toggle-container {
  margin-bottom: 15px;
  text-align: right;
}

.pressure-legend {
  margin-top: 10px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.legend-title {
  font-weight: bold;
  margin-bottom: 5px;
}

.legend-items {
  display: flex;
  justify-content: space-around;
}

.legend-item {
  display: flex;
  align-items: center;
}

.color-box {
  width: 20px;
  height: 20px;
  margin-right: 5px;
  border-radius: 3px;
}

.high-pressure-bg {
  background-color: #ff4d4f;
}

.normal-pressure-bg {
  background-color: #52c41a;
}

.low-pressure-bg {
  background-color: #1890ff;
}

.anomaly-list {
  margin-top: 20px;
}

.stats-container {
  display: flex;
  justify-content: space-between;
}

.stats-item {
  flex: 1;
  text-align: center;
  padding: 15px 10px;
  border-radius: 4px;
}

.stats-title {
  font-size: 14px;
  margin-bottom: 5px;
}

.stats-value {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 5px;
}

.stats-detail {
  font-size: 12px;
}

.high-pressure {
  background-color: rgba(255, 77, 79, 0.1);
}

.high-pressure-text {
  color: #ff4d4f;
}

.normal-pressure {
  background-color: rgba(82, 196, 26, 0.1);
}

.normal-pressure-text {
  color: #52c41a;
}

.low-pressure {
  background-color: rgba(24, 144, 255, 0.1);
}

.low-pressure-text {
  color: #1890ff;
}
</style> 