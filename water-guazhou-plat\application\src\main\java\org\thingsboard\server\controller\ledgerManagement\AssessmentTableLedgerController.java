package org.thingsboard.server.controller.ledgerManagement;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.thingsboard.server.common.data.assessmenttable.AssessmentTableDto;
import org.thingsboard.server.common.data.assessmenttable.AssessmentTableItemDto;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.assessmenttable.AssessmentTableService;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * 考核表管理Controller
 */
@Api(value = "考核表管理接口", tags = {"考核表管理"})
@RestController
@RequestMapping("/api/ledger/assessment-table")
@Slf4j
public class AssessmentTableLedgerController extends BaseController {

    @Autowired
    private AssessmentTableService assessmentTableService;

    @ApiOperation(value = "保存考核表", notes = "创建或更新考核表")
    @PreAuthorize("hasAnyAuthority('TENANT_ADMIN')")
    @PostMapping
    public ResponseEntity<AssessmentTableDto> saveAssessmentTable(
            @ApiParam(value = "考核表数据", required = true)
            @RequestBody AssessmentTableDto assessmentTableDto) throws ThingsboardException {
        try {
            TenantId tenantId = getCurrentUser().getTenantId();
            assessmentTableDto.setTenantId(tenantId);
            
            // 保存考核表
            AssessmentTableDto savedDto = checkNotNull(assessmentTableService.saveAssessmentTable(assessmentTableDto));
            return new ResponseEntity<>(savedDto, HttpStatus.OK);
        } catch (Exception e) {
            log.error("保存考核表失败", e);
            throw handleException(e);
        }
    }

    @ApiOperation(value = "根据ID获取考核表", notes = "根据ID获取考核表详情")
    @PreAuthorize("hasAnyAuthority('TENANT_ADMIN')")
    @GetMapping("/{id}")
    public ResponseEntity<AssessmentTableDto> getAssessmentTableById(
            @ApiParam(value = "考核表ID", required = true)
            @PathVariable("id") String strId) throws ThingsboardException {
        try {
            TenantId tenantId = getCurrentUser().getTenantId();
            UUID id = toUUID(strId);
            
            // 查询考核表
            AssessmentTableDto dto = checkNotNull(assessmentTableService.getAssessmentTableById(tenantId, id));
            return new ResponseEntity<>(dto, HttpStatus.OK);
        } catch (Exception e) {
            log.error("根据ID获取考核表失败", e);
            throw handleException(e);
        }
    }

    @ApiOperation(value = "分页查询考核表列表", notes = "分页查询考核表列表")
    @PreAuthorize("hasAnyAuthority('TENANT_ADMIN')")
    @GetMapping
    public ResponseEntity<PageData<AssessmentTableDto>> getAssessmentTables(
            @ApiParam(value = "页码", required = true)
            @RequestParam int page,
            @ApiParam(value = "每页记录数", required = true)
            @RequestParam int pageSize,
            @ApiParam(value = "搜索文本")
            @RequestParam(required = false) String searchText,
            @ApiParam(value = "考核类型")
            @RequestParam(required = false) String assessmentType,
            @ApiParam(value = "考核状态")
            @RequestParam(required = false) String status,
            @ApiParam(value = "考核周期")
            @RequestParam(required = false) String period) throws ThingsboardException {
        try {
            TenantId tenantId = getCurrentUser().getTenantId();
            
            // 查询考核表列表
            PageData<AssessmentTableDto> pageData = assessmentTableService.getAssessmentTables(
                    tenantId, page, pageSize, searchText, assessmentType, status, period);
            return new ResponseEntity<>(pageData, HttpStatus.OK);
        } catch (Exception e) {
            log.error("分页查询考核表列表失败", e);
            throw handleException(e);
        }
    }

    @ApiOperation(value = "删除考核表", notes = "根据ID删除考核表")
    @PreAuthorize("hasAnyAuthority('TENANT_ADMIN')")
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteAssessmentTable(
            @ApiParam(value = "考核表ID", required = true)
            @PathVariable("id") String strId) throws ThingsboardException {
        try {
            TenantId tenantId = getCurrentUser().getTenantId();
            UUID id = toUUID(strId);
            
            // 删除考核表
            assessmentTableService.deleteAssessmentTable(tenantId, id);
            return new ResponseEntity<>(HttpStatus.OK);
        } catch (Exception e) {
            log.error("删除考核表失败", e);
            throw handleException(e);
        }
    }

    @ApiOperation(value = "批量删除考核表", notes = "批量删除考核表")
    @PreAuthorize("hasAnyAuthority('TENANT_ADMIN')")
    @PostMapping("/batch-delete")
    public ResponseEntity<Void> batchDeleteAssessmentTables(
            @ApiParam(value = "考核表ID列表", required = true)
            @RequestBody List<String> strIds) throws ThingsboardException {
        try {
            TenantId tenantId = getCurrentUser().getTenantId();
            
            // 转换ID列表
            List<UUID> ids = new ArrayList<>();
            for (String strId : strIds) {
                ids.add(toUUID(strId));
            }
            
            // 批量删除考核表
            assessmentTableService.batchDeleteAssessmentTables(tenantId, ids);
            return new ResponseEntity<>(HttpStatus.OK);
        } catch (Exception e) {
            log.error("批量删除考核表失败", e);
            throw handleException(e);
        }
    }

    @ApiOperation(value = "获取考核表明细项列表", notes = "根据考核表ID获取明细项列表")
    @PreAuthorize("hasAnyAuthority('TENANT_ADMIN')")
    @GetMapping("/{id}/items")
    public ResponseEntity<List<AssessmentTableItemDto>> getAssessmentTableItems(
            @ApiParam(value = "考核表ID", required = true)
            @PathVariable("id") String strId) throws ThingsboardException {
        try {
            TenantId tenantId = getCurrentUser().getTenantId();
            UUID id = toUUID(strId);
            
            // 查询明细项列表
            List<AssessmentTableItemDto> items = assessmentTableService.getAssessmentTableItems(tenantId, id);
            return new ResponseEntity<>(items, HttpStatus.OK);
        } catch (Exception e) {
            log.error("获取考核表明细项列表失败", e);
            throw handleException(e);
        }
    }

    @ApiOperation(value = "导入考核表", notes = "从Excel文件导入考核表")
    @PreAuthorize("hasAnyAuthority('TENANT_ADMIN')")
    @PostMapping("/import")
    public ResponseEntity<Void> importAssessmentTables(
            @ApiParam(value = "Excel文件", required = true)
            @RequestParam("file") MultipartFile file) throws ThingsboardException {
        try {
            TenantId tenantId = getCurrentUser().getTenantId();
            // 使用系统已有的ExcelUtil工具类实现导入功能
            // 这里需要根据实际业务需求实现，可以调用ExcelUtil中的相关方法
            return new ResponseEntity<>(HttpStatus.OK);
        } catch (Exception e) {
            log.error("导入考核表失败", e);
            throw handleException(e);
        }
    }

    @ApiOperation(value = "导出考核表", notes = "导出考核表为Excel文件")
    @PreAuthorize("hasAnyAuthority('TENANT_ADMIN')")
    @GetMapping("/export")
    public void exportAssessmentTables(
            @ApiParam(value = "考核类型")
            @RequestParam(required = false) String assessmentType,
            @ApiParam(value = "考核状态")
            @RequestParam(required = false) String status,
            @ApiParam(value = "考核周期")
            @RequestParam(required = false) String period,
            HttpServletResponse response) throws ThingsboardException {
        try {
            TenantId tenantId = getCurrentUser().getTenantId();
            
            // 查询要导出的数据
            PageData<AssessmentTableDto> pageData = assessmentTableService.getAssessmentTables(
                    tenantId, 0, Integer.MAX_VALUE, null, assessmentType, status, period);
            
            if (pageData != null && pageData.getData() != null && !pageData.getData().isEmpty()) {
                // 使用系统已有的ExcelUtil工具类实现导出功能
                // 这里可以调用ExcelUtil.exportExcelX或其他适合的方法
                // ExcelUtil.exportExcelX("考核表数据", headMap, jsonArray, null, 17, false, response);
            }
        } catch (Exception e) {
            log.error("导出考核表失败", e);
            throw handleException(e);
        }
    }

    @ApiOperation(value = "下载导入模板", notes = "下载考核表导入模板")
    @PreAuthorize("hasAnyAuthority('TENANT_ADMIN')")
    @GetMapping("/template")
    public void downloadTemplate(HttpServletResponse response) throws ThingsboardException {
        try {
            // 使用系统已有的ExcelUtil工具类实现模板下载功能
            // 这里可以调用ExcelUtil中的相关方法创建和下载模板
        } catch (Exception e) {
            log.error("下载导入模板失败", e);
            throw handleException(e);
        }
    }
} 