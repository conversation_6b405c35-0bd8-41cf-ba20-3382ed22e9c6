package org.thingsboard.server.dao.model.request;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class StationCircuitPlanListRequest {

    private int page;

    private int size;

    // 维保部门
    private String optionDep;

    // 维保人员
    private String optionUserId;

    // 审核部门
    private String auditDep;

    // 审核人员
    private String auditUserId;

    // 开始时间范围
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date beginStartTime;
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date endStartTime;

    // 结束时间范围
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date beginEndTime;
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date endEndTime;

    // 计划名称
    private String keyword;

    private String stationType;

    private String tenantId;

}
