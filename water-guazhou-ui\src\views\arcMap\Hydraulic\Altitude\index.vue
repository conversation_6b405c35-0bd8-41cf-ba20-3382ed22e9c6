<!-- 节点高程 -->
<template>
  <RightDrawerMap :title="'节点高程'">
    <SliderBar></SliderBar>
    <el-divider></el-divider>
    <HydraulicPanel
      :header="['节点高程分级(m)', '图层控制', '定位']"
      :legends="[
        { label: '>1000', value: 14, checked: true },
        { label: '800~1000m', value: 32, checked: true },
        { label: '600~800m', value: 431, checked: true },
        { label: '400~600m', value: 4211, checked: true },
        { label: '0~400m', value: 41, checked: true }
      ]"
      :unit="'个'"
    ></HydraulicPanel>
  </RightDrawerMap>
</template>
<script lang="ts" setup>
import SliderBar from '../components/SliderBar.vue'
import RightDrawerMap from '../../components/common/RightDrawerMap.vue'
import HydraulicPanel from '../components/HydraulicPanel.vue'
</script>
<style lang="scss" scoped></style>
