<template>
  <div class="table-box">
    <FormTable :config="TableConfig_Detail"></FormTable>
  </div>
</template>
<script lang="ts" setup>
import { GetFieldConfig } from '@/api/mapservice/fieldconfig';
import { formatDate } from '@/utils/DateFormatter';
import { formatterDate } from '@/utils/GlobalHelper';
import {
  excuteQuery,
  getCurrentPageOIDs,
  getGraphicLayer,
  initQueryParams,
  querySourceLayerIndex,
  setSymbol,
  gotoAndHighLight
} from '@/utils/MapHelper';
import { SLMessage } from '@/utils/Message';

const emit = defineEmits([
  'refreshed',
  'close',
  'refreshing',
  'rowclick',
  'mounted'
]);
const staticState: {
  view?: __esri.MapView;
  fieldConfig?: any;
  tabFeatures: any[];
  tab?: string;
  queryParams?: {
    geometry?: __esri.Geometry;
    where?: string;
  };
  allRes?: __esri.FeatureSet;
} = {
  tabFeatures: []
};
const refTab = ref<ISearchIns>();
const TableConfig_Detail = reactive<ITable>({
  dataList: [],
  columns: [
    {
      label: 'OBJECTID',
      prop: 'OBJECTID'
    }
  ],
  height: 'none',
  handleRowClick: (row) => {
    TableConfig_Detail.currentRow = row;
    staticState.view &&
      extentTo(staticState.view, refTab.value?.queryParams.type, row.OBJECTID);
  },
  pagination: {
    refreshData: ({ page, size }) => {
      TableConfig_Detail.pagination.page = page;
      TableConfig_Detail.pagination.limit = size;
      refreshDetail(
        staticState.view,
        staticState.tab,
        staticState.queryParams,
        true
      );
    }
  }
});
const clearData = () => {
  TableConfig_Detail.dataList = [];
  TableConfig_Detail.loading = false;
};
const refreshDetail = async (
  view?: __esri.MapView,
  tab?: string,
  queryParams?: {
    geometry?: __esri.Geometry;
    where?: string;
  },
  useOldeParams?: boolean
) => {
  if (!useOldeParams) {
    staticState.view = view;
    staticState.tab = tab;
    staticState.queryParams = queryParams;
  }
  try {
    TableConfig_Detail.loading = true;
    if (!staticState.tab) {
      clearData();
      return;
    }
    const layerindex = await querySourceLayerIndex(staticState.tab);
    if (layerindex === undefined || layerindex === -1) {
      clearData();
      return;
    }
    if (!useOldeParams) {
      // 查询满足条件的全部oid
      staticState.allRes = await excuteQuery(
        `${window.SITE_CONFIG.GIS_CONFIG.gisService + window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService}/${layerindex}`,
        initQueryParams({
          orderByFields: ['OBJECTID asc'],
          outFields: ['OBJECTID'],
          returnGeometry: true,
          ...(staticState.queryParams || {})
        })
      );
    }

    const alloids =
      staticState.allRes?.features.map((item) => item.attributes.OBJECTID) ||
      [];
    const fields = await GetFieldConfig(staticState.tab);
    staticState.fieldConfig = fields.data?.result?.rows;
    TableConfig_Detail.pagination.total = alloids.length || 0;
    // 计算当前页的oid
    const oids = getCurrentPageOIDs(
      alloids,
      TableConfig_Detail.pagination.page || 1,
      TableConfig_Detail.pagination.limit || 20
    );
    const data: any[] = [];
    if (oids.length) {
      const res = await excuteQuery(
        `${window.SITE_CONFIG.GIS_CONFIG.gisService + window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService}/${layerindex}`,
        initQueryParams({
          orderByFields: ['OBJECTID asc'],
          outFields: ['*'],
          //   staticState.fieldConfig
          //     ?.filter(item => item.visible)
          //     .map(item => item.name) || [],
          objectIds: oids || [],
          returnGeometry: true,
          ...(staticState.queryParams || { where: '1=1' })
        })
      );

      res.features.map((item) => {
        data.push(item.attributes);
        item.symbol = setSymbol(item.geometry.type);
      });

      const columns: IFormTableColumn[] = [];
      res.fields.map((item) => {
        const conf: IFormTableColumn = {
          label: item.alias,
          prop: item.name,
          minWidth: 160
        };
        if (item.type === 'date') {
          conf.formatter = (row) => {
            return formatDate(row[item.name], formatterDate);
          };
        }
        columns.push(conf);
      });
      TableConfig_Detail.columns = columns;
    }

    TableConfig_Detail.dataList = data;
    if (alloids?.length) {
      const layer = getGraphicLayer(staticState.view, {
        id: 'pipe-detail',
        title: '详情展示'
      });
      if (layer) {
        layer.removeAll();
        staticState.allRes?.features.map(
          (item) => (item.symbol = setSymbol(item.geometry.type))
        );
        layer.addMany(staticState.allRes?.features || []);
        staticState.tabFeatures = staticState.allRes?.features || [];
      }
    }
  } catch (error) {
    console.dir(error);
    SLMessage.error('查询失败');
  }
  TableConfig_Detail.loading = false;
  emit('refreshed');
};
const extentTo = async (view: __esri.MapView, tab?: string, oid?: string) => {
  tab = tab || refTab.value?.queryParams?.type;
  const feature = staticState.tabFeatures.find(
    (item) => item.attributes.OBJECTID === oid
  );
  if (feature) {
    await gotoAndHighLight(view, feature);
  }
};
onMounted(() => {
  emit('mounted');
});
defineExpose({
  refreshDetail
});
</script>
<style lang="scss" scoped>
.table-box {
  height: calc(100% - 80px);
}
</style>
<style lang="scss"></style>
