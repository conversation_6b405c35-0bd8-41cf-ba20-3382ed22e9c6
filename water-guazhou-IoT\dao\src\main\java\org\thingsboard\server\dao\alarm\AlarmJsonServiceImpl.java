/**
 * Copyright © 2016-2019 The Thingsboard Authors
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.alarm;

import com.google.common.util.concurrent.ListenableFuture;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.alarm.AlarmJsonId;
import org.thingsboard.server.common.data.alarm.AttrAlarmJson;
import org.thingsboard.server.common.data.id.DeviceId;
import org.thingsboard.server.common.data.id.TenantId;

import java.util.List;

import static org.thingsboard.server.common.data.CacheConstants.ALARM_JSON_CACHE;
import static org.thingsboard.server.common.data.CacheConstants.ATTRIBUTE_CACHE;

@Service
public class AlarmJsonServiceImpl implements AlarmJsonService {

    @Autowired
    private AlarmJsonDao alarmJsonDao;

    @Override
    @Caching(evict = {@CacheEvict(cacheNames = ALARM_JSON_CACHE, key = "{#attrAlarmJson.deviceId}"),
            @CacheEvict(cacheNames = ALARM_JSON_CACHE, key = "{#attrAlarmJson.deviceId, #attrAlarmJson.attribute}"),
            @CacheEvict(cacheNames = ALARM_JSON_CACHE, key = "{#attrAlarmJson.tenantId}")})
    public AttrAlarmJson createOrUpdateAlarmJson(AttrAlarmJson attrAlarmJson) {
        return alarmJsonDao.save(attrAlarmJson);
    }

    @Override
//    @Cacheable(cacheNames = ALARM_JSON_CACHE, key = "{#alarmJsonId}")
    public ListenableFuture<AttrAlarmJson> findAlarmByIdAsync(AlarmJsonId alarmJsonId) {
        return alarmJsonDao.findAlarmByIdAsync(alarmJsonId);
    }

    @Override
    public ListenableFuture<List<AttrAlarmJson>> findByParams(TenantId tenantId, DeviceId deviceId, String attribute) {
        return alarmJsonDao.findByParams(tenantId, deviceId, attribute);
    }

    @Override
    @Caching(evict = {
//            @CacheEvict(cacheNames = ALARM_JSON_CACHE, key = "{#alarmJson.id}"),
            @CacheEvict(cacheNames = ALARM_JSON_CACHE, key = "{#alarmJson.tenantId}"),
            @CacheEvict(cacheNames = ALARM_JSON_CACHE, key = "{#alarmJson.deviceId,#alarmJson.attribute}"),
            @CacheEvict(cacheNames = ALARM_JSON_CACHE, key = "{#alarmJson.deviceId}")
    })
    public void deleteAlarmJsonById(AttrAlarmJson alarmJson) {
        alarmJsonDao.removeById(alarmJson.getUuidId());
    }

    @Override
    @Cacheable(cacheNames = ALARM_JSON_CACHE, key = "{#deviceId, #attribute}")
    public ListenableFuture<List<AttrAlarmJson>> findByDeviceAndAttribute(DeviceId deviceId, String attribute) {
        return alarmJsonDao.findByDeviceAndAttribute(deviceId, attribute);
    }

    @Override
    @Cacheable(cacheNames = ALARM_JSON_CACHE, key = "{#tenantId}")
    public ListenableFuture<List<AttrAlarmJson>> findByTenant(TenantId tenantId) {
        return alarmJsonDao.findByTenant(tenantId);
    }

    @Override
    @Cacheable(cacheNames = ALARM_JSON_CACHE, key = "{#deviceId}")
    public ListenableFuture<List<AttrAlarmJson>> findByDevice(DeviceId deviceId) {
        return alarmJsonDao.findByDevice(deviceId);
    }

    @Override
    public ListenableFuture<List<AttrAlarmJson>> findByDeviceAndPropAndLevel(DeviceId deviceId, String prop, String level) {
        return alarmJsonDao.findByDeviceAndPropAndLevel(deviceId, prop, level);
    }


}
