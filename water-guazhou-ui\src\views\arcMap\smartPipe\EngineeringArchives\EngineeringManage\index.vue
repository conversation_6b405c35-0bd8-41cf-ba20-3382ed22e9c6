<template>
  <RightDrawerMap
    ref="refRightDrawerMap"
    :title="'工程录入'"
    :right-drawer-width="800"
    :panel-custom-class="'engineering-panel'"
    :detail-title="'添加工程'"
    @detail-closed="refArcDraw?.clear()"
    @map-loaded="onMaploaded"
  >
    <Search
      ref="refSearch"
      :config="SearchConfig"
    ></Search>
    <FormTable
      class="table-box"
      :config="TableConfig"
    ></FormTable>
    <template #detail-default>
      <Form
        ref="refForm"
        :config="FormConfig"
      ></Form>
    </template>
    <template #map-bars>
      <ArcDraw
        ref="refArcDraw"
        :layerid="'drawing-pipes'"
        :layername="'绘制管网'"
        :multiple="true"
      ></ArcDraw>
    </template>
  </RightDrawerMap>
</template>
<script lang="ts" setup>
import Graphic from '@arcgis/core/Graphic'
import { DeleteGisProject, GetGisProjectList, PostGisProject } from '@/api/mapservice/engineeringDocuments'
import { SLConfirm, SLMessage } from '@/utils/Message'
import RightDrawerMap from '@/views/arcMap/components/common/RightDrawerMap.vue'
import { formatDate } from '@/utils/DateFormatter'
import { formatterDate } from '@/utils/GlobalHelper'
import { calcLength, getGraphicLayer } from '@/utils/MapHelper'

const refForm = ref<IFormIns>()
const refSearch = ref<ISearchIns>()
const refArcDraw = ref<IArcDrawIns>()
const refRightDrawerMap = ref<InstanceType<typeof RightDrawerMap>>()
const isDisabled = ref<boolean>(false)
const SearchConfig = reactive<ISearch>({
  size: 'small',
  filters: [
    { type: 'input', label: '工程名称', field: 'name' }
    // { type: 'input', label: '工程编号', field: 'code' },
    // { type: 'daterange', label: '竣工日期', field: 'date', width: 200 },
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        { perm: true, text: '查询', iconifyIcon: 'ep:search', click: () => refreshData() },
        {
          perm: true,
          text: '添加',
          iconifyIcon: 'ep:plus',
          click: () => handleAou()
        }
      ]
    }
  ],
  defaultParams: {}
})
const TableConfig = reactive<ITable>({
  columns: [
    // { label: '工程编号', prop: 'code' },
    { label: '工程名称', prop: 'name' },
    {
      label: '竣工日期',
      prop: 'completeDate',
      formatter(row, value) {
        return formatDate(value, formatterDate)
      }
    },
    { label: '长度', prop: 'length' },
    { label: '管网进度', prop: 'status' }
  ],
  dataList: [],
  operationWidth: 200,
  operations: [
    { perm: true, text: '编辑', iconifyIcon: 'ep:edit-pen', circle: true, click: row => handleAou(row) },
    {
      perm: true,
      text: '定位',
      iconifyIcon: 'ep:location',
      circle: true,
      type: 'success',
      click: row => handleLocate(row)
    },
    {
      perm: true,
      text: '删除',
      iconifyIcon: 'ep:delete',
      circle: true,
      type: 'danger',
      click: row => handleDelete(row)
    }
  ],
  pagination: {
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page || 1
      TableConfig.pagination.limit = size || 20
      refreshData()
    }
  }
})
const handleAou = (row?: any) => {
  isDisabled.value = false
  staticState.graphicsLayer?.removeAll()
  refArcDraw.value?.clear()
  FormConfig.defaultValue = {
    ...(row
      ? {
        ...row,
        buildDate: row.buildDate ? moment(row.buildDate).format(formatterDate) : undefined,
        completeDate: row.completeDate ? moment(row.completeDate).format(formatterDate) : undefined
      }
      : { status: '规划' })
  }
  refRightDrawerMap.value?.toggleCustomDetail(true)
  refForm.value?.resetForm()
  if (!row) return
  const geo = row?.geo
  if (!geo) {
    SLMessage.error('暂无位置信息')
    return
  }

  const gJson = JSON.parse(geo)
  const graphics = gJson.map(item => Graphic.fromJSON(item))
  refArcDraw.value?.setGraphics(graphics)
  staticState.view?.goTo(graphics)
}
const handleLocate = (row: any) => {
  const geo = row.geo
  isDisabled.value = true

  FormConfig.defaultValue = {
    ...(row
      ? {
        ...row,
        buildDate: row.buildDate ? moment(row.buildDate).format(formatterDate) : undefined,
        completeDate: row.completeDate ? moment(row.completeDate).format(formatterDate) : undefined
      }
      : { status: '规划' })
  }
  refForm.value?.resetForm()
  if (!geo) {
    SLMessage.error('暂无位置信息')
    return
  }
  const gJson = JSON.parse(geo)
  const graphics = gJson.map(item => Graphic.fromJSON(item))
  staticState.graphicsLayer?.removeAll()
  staticState.graphicsLayer?.addMany(graphics)
  staticState.view?.goTo(graphics)
  refArcDraw.value?.clear()
  refRightDrawerMap.value?.toggleCustomDetail(true)
}
const handleDelete = (row: any) => {
  const ids = row ? [row.id] : []
  if (!ids.length) {
    SLMessage.error('请选择要删除的数据')
    return
  }
  SLConfirm('确定删除？', '提示').then(async () => {
    try {
      const res = await DeleteGisProject([row.id])
      if (res.data.code === 200) {
        SLMessage.success('删除成功')
        refreshData()
        TableConfig.currentRow = undefined
      } else {
        SLMessage.error('删除失败')
        console.log(res.data.message)
      }
    } catch (error) {
      SLMessage.error('删除失败')
    }
  })
}
const refreshData = () => {
  const queryParams = refSearch.value?.queryParams || {}
  GetGisProjectList({
    page: TableConfig.pagination.page || 1,
    size: TableConfig.pagination.limit || 20,
    ...queryParams
    // beginTime: queryParams?.date?.[0],
    // endTime: queryParams?.date?.[1]
  })
    .then(res => {
      TableConfig.dataList = res.data.data.data || []
      TableConfig.pagination.total = res.data.data.total || 0
    })
    .catch(e => {
      console.log(e)
    })
}
const FormConfig = reactive<IFormConfig>({
  gutter: 0,
  size: 'small',
  group: [
    {
      fields: [
        // { type: 'input', label: '工程编号', field: 'code', rules: [{ required: true, message: '请输入工程编号' }] },
        {
          disabled: computed(() => isDisabled.value) as any,
          type: 'input',
          label: '工程名称',
          field: 'name',
          rules: [{ required: true, message: '请输入工程名称' }]
        },
        {
          type: 'btn-group',
          label: '建设管网',
          btns: [
            {
              perm: true,
              text: '绘制',
              type: 'success',

              disabled: computed(() => isDisabled.value) as any,
              click: () => {
                refArcDraw.value?.initDraw('polyline')
              }
            },
            {
              perm: true,
              text: '清除',
              disabled: computed(() => isDisabled.value) as any,
              type: 'danger',
              click: () => {
                SLConfirm('确定清除绘制吗?', '提示')
                  .then(() => {
                    refArcDraw.value?.clear()
                  })
                  .catch(() => {
                    //
                  })
              }
            }
          ]
        },
        {
          disabled: computed(() => isDisabled.value) as any,
          type: 'input',
          label: '建设单位',
          field: 'createDept'
        },
        {
          disabled: computed(() => isDisabled.value) as any,
          type: 'input',
          label: '设计单位',
          field: 'designDept'
        },
        {
          disabled: computed(() => isDisabled.value) as any,
          type: 'input',
          label: '施工单位',
          field: 'buildDept'
        },
        {
          readonly: computed(() => isDisabled.value) as any,
          type: 'date',
          label: '施工日期',
          field: 'buildDate'
        },
        {
          readonly: computed(() => isDisabled.value) as any,
          type: 'date',
          label: '竣工日期',
          field: 'completeDate'
        },
        {
          disabled: computed(() => isDisabled.value) as any,
          type: 'input',
          label: '主要材料',
          field: 'mainMaterial'
        },
        {
          readonly: computed(() => isDisabled.value) as any,
          type: 'input-number',
          label: '主要口径',
          field: 'caliber'
        },
        {
          readonly: computed(() => isDisabled.value) as any,
          type: 'radio',
          label: '管网进度',
          field: 'status',
          options: [
            { label: '规划', value: '规划' },
            { label: '建设', value: '建设' },
            { label: '完工', value: '完工' }
          ]
        },
        {
          readonly: computed(() => isDisabled.value) as any,
          type: 'file',
          label: '附件',
          field: 'file'
        },
        {
          readonly: computed(() => isDisabled.value) as any,
          type: 'btn-group',
          style: {
            justifyContent: 'right'
          },
          btns: [
            {
              perm: true,
              text: () => (isDisabled.value ? '关闭' : '取消'),
              size: 'default',
              type: 'default',
              disabled: (): boolean => FormConfig.submitting === true,
              click: () => {
                refRightDrawerMap.value?.toggleCustomDetail(false)
              }
            },
            {
              perm: () => !isDisabled.value,
              text: '确定',
              size: 'default',
              type: 'primary',
              loading: (): boolean => FormConfig.submitting === true,
              click: () => {
                refForm.value?.Submit()
              }
            }
          ]
        }
      ]
    }
  ],
  labelPosition: 'right',
  labelWidth: '100px',
  defaultValue: {
    progress: '1'
  },
  submit: (params: any) => {
    console.log(params)
    const lines = refArcDraw.value?.getGraphics().map(item => item.toJSON())
    console.log(lines)
    // 此处编写提交逻辑
    FormConfig.submitting = true
    const lengths = refArcDraw.value
      ?.getGraphics()
      .map(item => calcLength((item.geometry as __esri.Polyline).paths[0], 'meters', staticState.view?.spatialReference))
    PostGisProject({
      ...params,
      length: lengths?.reduce((prev, cur) => {
        return prev + cur
      }, 0),
      geo: lines ? JSON.stringify(lines) : undefined
    })
      .then(res => {
        if (res.data.code === 200) {
          SLMessage.success('添加成功')
          refreshData()
          refRightDrawerMap.value?.toggleCustomDetail(false)
        } else {
          SLMessage.error('添加失败')
          console.log(res.data.message)
        }
      })
      .catch(e => {
        SLMessage.error('添加失败')
        console.log(e)
      })
      .finally(() => {
        FormConfig.submitting = false
      })
  }
})
const staticState: { view: __esri.MapView | undefined; graphicsLayer: __esri.GraphicsLayer | undefined } = {
  view: undefined,
  graphicsLayer: undefined
}
const onMaploaded = (view: __esri.MapView) => {
  staticState.view = view
  staticState.graphicsLayer = getGraphicLayer(view, { id: 'engi-pipes', title: '建设管网' })
}
onMounted(() => {
  refreshData()
})
</script>
<style lang="scss" scoped>
.table-box {
  height: calc(100% - 40px);
}
</style>
<style lang="scss">
.engineering-panel {
  left: 0;
  top: 0;
  width: 400px;
  height: 600px;
  max-height: 100%;
  position: absolute;
}
.form-box {
  overflow: hidden;
}
</style>
