import{s as fe,am as Zt,an as Qt,e as V,y as z,w as er,a as et,W as tt,b as tr,h as Lt,i as ct}from"./Point-WxyopZva.js";import{a3 as rr,R as J,eZ as Be,a5 as ee,T as Ne,aO as Ce}from"./index-r0dFAfgr.js";import{m as re,k as Ie,n as ht,u as Ve,o as or,p as ft,q as Ee}from"./dataUtils-DovfQoP5.js";import{a as ir}from"./pe-B8dP0-Ut.js";import{u as rt,s as nr}from"./pixelRangeUtils-Dr0gmLDH.js";import{w as dt,hu as Ge,l as pt,hi as mt,hg as gt,hh as ar}from"./MapView-DaoQedLH.js";var $e,yt,Ct,we={},sr={get exports(){return we},set exports(e){we=e}};yt=sr,$e=function(){function e(){this.pos=0,this.bufferLength=0,this.eof=!1,this.buffer=null}return e.prototype={ensureBuffer:function(t){var r=this.buffer,s=r?r.byteLength:0;if(t<s)return r;for(var a=512;a<t;)a<<=1;for(var n=new Uint8Array(a),f=0;f<s;++f)n[f]=r[f];return this.buffer=n},getByte:function(){for(var t=this.pos;this.bufferLength<=t;){if(this.eof)return null;this.readBlock()}return this.buffer[this.pos++]},getBytes:function(t){var r=this.pos;if(t){this.ensureBuffer(r+t);for(var s=r+t;!this.eof&&this.bufferLength<s;)this.readBlock();var a=this.bufferLength;s>a&&(s=a)}else{for(;!this.eof;)this.readBlock();s=this.bufferLength}return this.pos=s,this.buffer.subarray(r,s)},lookChar:function(){for(var t=this.pos;this.bufferLength<=t;){if(this.eof)return null;this.readBlock()}return String.fromCharCode(this.buffer[this.pos])},getChar:function(){for(var t=this.pos;this.bufferLength<=t;){if(this.eof)return null;this.readBlock()}return String.fromCharCode(this.buffer[this.pos++])},makeSubStream:function(t,r,s){for(var a=t+r;this.bufferLength<=a&&!this.eof;)this.readBlock();return new Stream(this.buffer,t,r,s)},skip:function(t){t||(t=1),this.pos+=t},reset:function(){this.pos=0}},e}(),(Ct=function(){if(!self||!self.Uint32Array)return null;var e=new Uint32Array([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),t=new Uint32Array([3,4,5,6,7,8,9,10,65547,65549,65551,65553,131091,131095,131099,131103,196643,196651,196659,196667,262211,262227,262243,262259,327811,327843,327875,327907,258,258,258]),r=new Uint32Array([1,2,3,4,65541,65543,131081,131085,196625,196633,262177,262193,327745,327777,393345,393409,459009,459137,524801,525057,590849,591361,657409,658433,724993,727041,794625,798721,868353,876545]),s=[new Uint32Array([459008,524368,524304,524568,459024,524400,524336,590016,459016,524384,524320,589984,524288,524416,524352,590048,459012,524376,524312,589968,459028,524408,524344,590032,459020,524392,524328,59e4,524296,524424,524360,590064,459010,524372,524308,524572,459026,524404,524340,590024,459018,524388,524324,589992,524292,524420,524356,590056,459014,524380,524316,589976,459030,524412,524348,590040,459022,524396,524332,590008,524300,524428,524364,590072,459009,524370,524306,524570,459025,524402,524338,590020,459017,524386,524322,589988,524290,524418,524354,590052,459013,524378,524314,589972,459029,524410,524346,590036,459021,524394,524330,590004,524298,524426,524362,590068,459011,524374,524310,524574,459027,524406,524342,590028,459019,524390,524326,589996,524294,524422,524358,590060,459015,524382,524318,589980,459031,524414,524350,590044,459023,524398,524334,590012,524302,524430,524366,590076,459008,524369,524305,524569,459024,524401,524337,590018,459016,524385,524321,589986,524289,524417,524353,590050,459012,524377,524313,589970,459028,524409,524345,590034,459020,524393,524329,590002,524297,524425,524361,590066,459010,524373,524309,524573,459026,524405,524341,590026,459018,524389,524325,589994,524293,524421,524357,590058,459014,524381,524317,589978,459030,524413,524349,590042,459022,524397,524333,590010,524301,524429,524365,590074,459009,524371,524307,524571,459025,524403,524339,590022,459017,524387,524323,589990,524291,524419,524355,590054,459013,524379,524315,589974,459029,524411,524347,590038,459021,524395,524331,590006,524299,524427,524363,590070,459011,524375,524311,524575,459027,524407,524343,590030,459019,524391,524327,589998,524295,524423,524359,590062,459015,524383,524319,589982,459031,524415,524351,590046,459023,524399,524335,590014,524303,524431,524367,590078,459008,524368,524304,524568,459024,524400,524336,590017,459016,524384,524320,589985,524288,524416,524352,590049,459012,524376,524312,589969,459028,524408,524344,590033,459020,524392,524328,590001,524296,524424,524360,590065,459010,524372,524308,524572,459026,524404,524340,590025,459018,524388,524324,589993,524292,524420,524356,590057,459014,524380,524316,589977,459030,524412,524348,590041,459022,524396,524332,590009,524300,524428,524364,590073,459009,524370,524306,524570,459025,524402,524338,590021,459017,524386,524322,589989,524290,524418,524354,590053,459013,524378,524314,589973,459029,524410,524346,590037,459021,524394,524330,590005,524298,524426,524362,590069,459011,524374,524310,524574,459027,524406,524342,590029,459019,524390,524326,589997,524294,524422,524358,590061,459015,524382,524318,589981,459031,524414,524350,590045,459023,524398,524334,590013,524302,524430,524366,590077,459008,524369,524305,524569,459024,524401,524337,590019,459016,524385,524321,589987,524289,524417,524353,590051,459012,524377,524313,589971,459028,524409,524345,590035,459020,524393,524329,590003,524297,524425,524361,590067,459010,524373,524309,524573,459026,524405,524341,590027,459018,524389,524325,589995,524293,524421,524357,590059,459014,524381,524317,589979,459030,524413,524349,590043,459022,524397,524333,590011,524301,524429,524365,590075,459009,524371,524307,524571,459025,524403,524339,590023,459017,524387,524323,589991,524291,524419,524355,590055,459013,524379,524315,589975,459029,524411,524347,590039,459021,524395,524331,590007,524299,524427,524363,590071,459011,524375,524311,524575,459027,524407,524343,590031,459019,524391,524327,589999,524295,524423,524359,590063,459015,524383,524319,589983,459031,524415,524351,590047,459023,524399,524335,590015,524303,524431,524367,590079]),9],a=[new Uint32Array([327680,327696,327688,327704,327684,327700,327692,327708,327682,327698,327690,327706,327686,327702,327694,0,327681,327697,327689,327705,327685,327701,327693,327709,327683,327699,327691,327707,327687,327703,327695,0]),5];function n(o){throw new Error(o)}function f(o){var u=0,l=o[u++],i=o[u++];l!=-1&&i!=-1||n("Invalid header in flate stream"),(15&l)!=8&&n("Unknown compression method in flate stream"),((l<<8)+i)%31!=0&&n("Bad FCHECK in flate stream"),32&i&&n("FDICT bit set in flate stream"),this.bytes=o,this.bytesPos=u,this.codeSize=0,this.codeBuf=0,$e.call(this)}return f.prototype=Object.create($e.prototype),f.prototype.getBits=function(o){for(var u,l=this.codeSize,i=this.codeBuf,g=this.bytes,d=this.bytesPos;l<o;)(u=g[d++])===void 0&&n("Bad encoding in flate stream"),i|=u<<l,l+=8;return u=i&(1<<o)-1,this.codeBuf=i>>o,this.codeSize=l-=o,this.bytesPos=d,u},f.prototype.getCode=function(o){for(var u=o[0],l=o[1],i=this.codeSize,g=this.codeBuf,d=this.bytes,p=this.bytesPos;i<l;){var T;(T=d[p++])===void 0&&n("Bad encoding in flate stream"),g|=T<<i,i+=8}var y=u[g&(1<<l)-1],b=y>>16,c=65535&y;return(i==0||i<b||b==0)&&n("Bad encoding in flate stream"),this.codeBuf=g>>b,this.codeSize=i-b,this.bytesPos=p,c},f.prototype.generateHuffmanTable=function(o){for(var u=o.length,l=0,i=0;i<u;++i)o[i]>l&&(l=o[i]);for(var g=1<<l,d=new Uint32Array(g),p=1,T=0,y=2;p<=l;++p,T<<=1,y<<=1)for(var b=0;b<u;++b)if(o[b]==p){var c=0,h=T;for(i=0;i<p;++i)c=c<<1|1&h,h>>=1;for(i=c;i<g;i+=y)d[i]=p<<16|b;++T}return[d,l]},f.prototype.readBlock=function(){function o(O,W,_,U,N){for(var q=O.getBits(_)+U;q-- >0;)W[y++]=N}var u=this.getBits(3);if(1&u&&(this.eof=!0),(u>>=1)!=0){var l,i;if(u==1)l=s,i=a;else if(u==2){for(var g=this.getBits(5)+257,d=this.getBits(5)+1,p=this.getBits(4)+4,T=Array(e.length),y=0;y<p;)T[e[y++]]=this.getBits(3);for(var b=this.generateHuffmanTable(T),c=0,h=(y=0,g+d),m=new Array(h);y<h;){var v=this.getCode(b);v==16?o(this,m,2,3,c):v==17?o(this,m,3,3,c=0):v==18?o(this,m,7,11,c=0):m[y++]=c=v}l=this.generateHuffmanTable(m.slice(0,g)),i=this.generateHuffmanTable(m.slice(g,h))}else n("Unknown block type in flate stream");for(var w=(B=this.buffer)?B.length:0,S=this.bufferLength;;){var C=this.getCode(l);if(C<256)S+1>=w&&(w=(B=this.ensureBuffer(S+1)).length),B[S++]=C;else{if(C==256)return void(this.bufferLength=S);var x=(C=t[C-=257])>>16;x>0&&(x=this.getBits(x)),c=(65535&C)+x,C=this.getCode(i),(x=(C=r[C])>>16)>0&&(x=this.getBits(x));var I=(65535&C)+x;S+c>=w&&(w=(B=this.ensureBuffer(S+c)).length);for(var E=0;E<c;++E,++S)B[S]=B[S-I]}}}else{var P,L=this.bytes,D=this.bytesPos;(P=L[D++])===void 0&&n("Bad block header in flate stream");var A=P;(P=L[D++])===void 0&&n("Bad block header in flate stream"),A|=P<<8,(P=L[D++])===void 0&&n("Bad block header in flate stream");var k=P;(P=L[D++])===void 0&&n("Bad block header in flate stream"),(k|=P<<8)!=(65535&~A)&&n("Bad uncompressed block length in flate stream"),this.codeBuf=0,this.codeSize=0;var R=this.bufferLength,B=this.ensureBuffer(R+A),F=R+A;this.bufferLength=F;for(var M=R;M<F;++M){if((P=L[D++])===void 0){this.eof=!0;break}B[M]=P}this.bytesPos=D}},f}())!==void 0&&(yt.exports=Ct);let lr=class Ot{constructor(t){this._canvas=null,this._ctx=null,t&&(this._canvas=t.canvas,this._ctx=t.ctx||t.canvas&&t.canvas.getContext("2d"))}decode(t,r,s){if(!t||t.byteLength<10)throw new fe("imagecanvasdecoder: decode","required a valid encoded data as input.");let{width:a=0,height:n=0,format:f}=r;const{applyJpegMask:o}=r;if(o&&(!a||!n))throw new fe("imagecanvasdecoder: decode","image width and height are needed to apply jpeg mask directly to canvas");return new Promise((u,l)=>{let i=null;f==="jpg"&&o&&(i=Ot._getMask(t,{width:a,height:n}));const g=new Blob([new Uint8Array(t)],{type:"image/"+f=="jpg"?"jpeg":f}),d=URL.createObjectURL(g),p=new Image;let T;p.src=d,p.onload=()=>{if(URL.revokeObjectURL(d),Zt(s))return void l(Qt());a=p.width,n=p.height,this._canvas&&this._ctx?(this._canvas.width===a&&this._canvas.height===n||(this._canvas.width=a,this._canvas.height=n),this._ctx.clearRect(0,0,a,n)):(this._canvas=document.createElement("canvas"),this._canvas.width=a,this._canvas.height=n,this._ctx=this._canvas.getContext("2d")),this._ctx.drawImage(p,0,0);const y=this._ctx.getImageData(0,0,a,n);let b;if(T=y.data,r.renderOnCanvas){if(i)for(b=0;b<i.length;b++)i[b]?T[4*b+3]=255:T[4*b+3]=0;return this._ctx.putImageData(y,0,0),void u(null)}const c=a*n,h=new Uint8Array(c),m=new Uint8Array(c),v=new Uint8Array(c);if(i)for(b=0;b<c;b++)h[b]=T[4*b],m[b]=T[4*b+1],v[b]=T[4*b+2];else for(i=new Uint8Array(c),b=0;b<c;b++)h[b]=T[4*b],m[b]=T[4*b+1],v[b]=T[4*b+2],i[b]=T[4*b+3];u({width:a,height:n,pixels:[h,m,v],mask:i,pixelType:"u8"})},p.onerror=()=>{URL.revokeObjectURL(d),l("cannot load image")}})}static _getMask(t,r){let s=null;try{const a=new Uint8Array(t),n=Math.ceil(a.length/2);let f=0;const o=a.length-2;for(f=n;f<o&&(a[f]!==255||a[f+1]!==217);f++);if(f+=2,f<a.length-1){const u=new we(a.subarray(f)).getBytes();s=new Uint8Array(r.width*r.height);let l=0;for(let i=0;i<u.length;i++)for(let g=7;g>=0;g--)s[l++]=u[i]>>g&1}}catch{}return s}};var bt,wt,vt,Ue={},ur={get exports(){return Ue},set exports(e){Ue=e}};bt=ur,wt=function(){var e=function(){function t(r){this.message="JPEG error: "+r}return t.prototype=new Error,t.prototype.name="JpegError",t.constructor=t,t}();return function(){if(!self||!self.Uint8ClampedArray)return null;var t=new Uint8Array([0,1,8,16,9,2,3,10,17,24,32,25,18,11,4,5,12,19,26,33,40,48,41,34,27,20,13,6,7,14,21,28,35,42,49,56,57,50,43,36,29,22,15,23,30,37,44,51,58,59,52,45,38,31,39,46,53,60,61,54,47,55,62,63]),r=4017,s=799,a=3406,n=2276,f=1567,o=3784,u=5793,l=2896;function i(){this.decodeTransform=null,this.colorTransform=-1}function g(c,h){for(var m,v,w=0,S=[],C=16;C>0&&!c[C-1];)C--;S.push({children:[],index:0});var x,I=S[0];for(m=0;m<C;m++){for(v=0;v<c[m];v++){for((I=S.pop()).children[I.index]=h[w];I.index>0;)I=S.pop();for(I.index++,S.push(I);S.length<=m;)S.push(x={children:[],index:0}),I.children[I.index]=x.children,I=x;w++}m+1<C&&(S.push(x={children:[],index:0}),I.children[I.index]=x.children,I=x)}return S[0].children}function d(c,h,m){return 64*((c.blocksPerLine+1)*h+m)}function p(c,h,m,v,w,S,C,x,I){var E=m.mcusPerLine,P=m.progressive,L=h,D=0,A=0;function k(){if(A>0)return A--,D>>A&1;if((D=c[h++])===255){var G=c[h++];if(G)throw new e("unexpected marker "+(D<<8|G).toString(16))}return A=7,D>>>7}function R(G){for(var j=G;;){if(typeof(j=j[k()])=="number")return j;if(typeof j!="object")throw new e("invalid huffman sequence")}}function B(G){for(var j=0;G>0;)j=j<<1|k(),G--;return j}function F(G){if(G===1)return k()===1?1:-1;var j=B(G);return j>=1<<G-1?j:j+(-1<<G)+1}function M(G,j){var Y=R(G.huffmanTableDC),X=Y===0?0:F(Y);G.blockData[j]=G.pred+=X;for(var te=1;te<64;){var se=R(G.huffmanTableAC),Z=15&se,ie=se>>4;if(Z!==0){var Xt=t[te+=ie];G.blockData[j+Xt]=F(Z),te++}else{if(ie<15)break;te+=16}}}function O(G,j){var Y=R(G.huffmanTableDC),X=Y===0?0:F(Y)<<I;G.blockData[j]=G.pred+=X}function W(G,j){G.blockData[j]|=k()<<I}var _=0;function U(G,j){if(_>0)_--;else for(var Y=S,X=C;Y<=X;){var te=R(G.huffmanTableAC),se=15&te,Z=te>>4;if(se!==0){var ie=t[Y+=Z];G.blockData[j+ie]=F(se)*(1<<I),Y++}else{if(Z<15){_=B(Z)+(1<<Z)-1;break}Y+=16}}}var N,q=0;function oe(G,j){for(var Y,X,te=S,se=C,Z=0;te<=se;){var ie=t[te];switch(q){case 0:if(Z=(X=R(G.huffmanTableAC))>>4,(Y=15&X)==0)Z<15?(_=B(Z)+(1<<Z),q=4):(Z=16,q=1);else{if(Y!==1)throw new e("invalid ACn encoding");N=F(Y),q=Z?2:3}continue;case 1:case 2:G.blockData[j+ie]?G.blockData[j+ie]+=k()<<I:--Z==0&&(q=q===2?3:0);break;case 3:G.blockData[j+ie]?G.blockData[j+ie]+=k()<<I:(G.blockData[j+ie]=N<<I,q=0);break;case 4:G.blockData[j+ie]&&(G.blockData[j+ie]+=k()<<I)}te++}q===4&&--_==0&&(q=0)}function ve(G,j,Y,X,te){var se=Y%E;j(G,d(G,(Y/E|0)*G.v+X,se*G.h+te))}function Te(G,j,Y){j(G,d(G,Y/G.blocksPerLine|0,Y%G.blocksPerLine))}var le,ne,ue,de,K,Ae,pe=v.length;Ae=P?S===0?x===0?O:W:x===0?U:oe:M;var ae,me,De,Re,ce=0;for(me=pe===1?v[0].blocksPerLine*v[0].blocksPerColumn:E*m.mcusPerColumn;ce<me;){var Le=w?Math.min(me-ce,w):me;for(ne=0;ne<pe;ne++)v[ne].pred=0;if(_=0,pe===1)for(le=v[0],K=0;K<Le;K++)Te(le,Ae,ce),ce++;else for(K=0;K<Le;K++){for(ne=0;ne<pe;ne++)for(De=(le=v[ne]).h,Re=le.v,ue=0;ue<Re;ue++)for(de=0;de<De;de++)ve(le,Ae,ce,ue,de);ce++}A=0,(ae=b(c,h))&&ae.invalid&&(console.log("decodeScan - unexpected MCU data, next marker is: "+ae.invalid),h=ae.offset);var ge=ae&&ae.marker;if(!ge||ge<=65280)throw new e("marker was not found");if(!(ge>=65488&&ge<=65495))break;h+=2}return(ae=b(c,h))&&ae.invalid&&(console.log("decodeScan - unexpected Scan data, next marker is: "+ae.invalid),h=ae.offset),h-L}function T(c,h,m){var v,w,S,C,x,I,E,P,L,D,A,k,R,B,F,M,O,W=c.quantizationTable,_=c.blockData;if(!W)throw new e("missing required Quantization Table.");for(var U=0;U<64;U+=8)L=_[h+U],D=_[h+U+1],A=_[h+U+2],k=_[h+U+3],R=_[h+U+4],B=_[h+U+5],F=_[h+U+6],M=_[h+U+7],L*=W[U],D|A|k|R|B|F|M?(D*=W[U+1],A*=W[U+2],k*=W[U+3],R*=W[U+4],B*=W[U+5],F*=W[U+6],M*=W[U+7],w=(v=(v=u*L+128>>8)+(w=u*R+128>>8)+1>>1)-w,O=(S=A)*o+(C=F)*f+128>>8,S=S*f-C*o+128>>8,E=(x=(x=l*(D-M)+128>>8)+(E=B<<4)+1>>1)-E,I=(P=(P=l*(D+M)+128>>8)+(I=k<<4)+1>>1)-I,C=(v=v+(C=O)+1>>1)-C,S=(w=w+S+1>>1)-S,O=x*n+P*a+2048>>12,x=x*a-P*n+2048>>12,P=O,O=I*s+E*r+2048>>12,I=I*r-E*s+2048>>12,E=O,m[U]=v+P,m[U+7]=v-P,m[U+1]=w+E,m[U+6]=w-E,m[U+2]=S+I,m[U+5]=S-I,m[U+3]=C+x,m[U+4]=C-x):(O=u*L+512>>10,m[U]=O,m[U+1]=O,m[U+2]=O,m[U+3]=O,m[U+4]=O,m[U+5]=O,m[U+6]=O,m[U+7]=O);for(var N=0;N<8;++N)L=m[N],(D=m[N+8])|(A=m[N+16])|(k=m[N+24])|(R=m[N+32])|(B=m[N+40])|(F=m[N+48])|(M=m[N+56])?(w=(v=4112+((v=u*L+2048>>12)+(w=u*R+2048>>12)+1>>1))-w,O=(S=A)*o+(C=F)*f+2048>>12,S=S*f-C*o+2048>>12,C=O,E=(x=(x=l*(D-M)+2048>>12)+(E=B)+1>>1)-E,I=(P=(P=l*(D+M)+2048>>12)+(I=k)+1>>1)-I,O=x*n+P*a+2048>>12,x=x*a-P*n+2048>>12,P=O,O=I*s+E*r+2048>>12,I=I*r-E*s+2048>>12,L=(L=(v=v+C+1>>1)+P)<16?0:L>=4080?255:L>>4,D=(D=(w=w+S+1>>1)+(E=O))<16?0:D>=4080?255:D>>4,A=(A=(S=w-S)+I)<16?0:A>=4080?255:A>>4,k=(k=(C=v-C)+x)<16?0:k>=4080?255:k>>4,R=(R=C-x)<16?0:R>=4080?255:R>>4,B=(B=S-I)<16?0:B>=4080?255:B>>4,F=(F=w-E)<16?0:F>=4080?255:F>>4,M=(M=v-P)<16?0:M>=4080?255:M>>4,_[h+N]=L,_[h+N+8]=D,_[h+N+16]=A,_[h+N+24]=k,_[h+N+32]=R,_[h+N+40]=B,_[h+N+48]=F,_[h+N+56]=M):(O=(O=u*L+8192>>14)<-2040?0:O>=2024?255:O+2056>>4,_[h+N]=O,_[h+N+8]=O,_[h+N+16]=O,_[h+N+24]=O,_[h+N+32]=O,_[h+N+40]=O,_[h+N+48]=O,_[h+N+56]=O)}function y(c,h){for(var m=h.blocksPerLine,v=h.blocksPerColumn,w=new Int16Array(64),S=0;S<v;S++)for(var C=0;C<m;C++)T(h,d(h,S,C),w);return h.blockData}function b(c,h,m){function v(I){return c[I]<<8|c[I+1]}var w=c.length-1,S=m<h?m:h;if(h>=w)return null;var C=v(h);if(C>=65472&&C<=65534)return{invalid:null,marker:C,offset:h};for(var x=v(S);!(x>=65472&&x<=65534);){if(++S>=w)return null;x=v(S)}return{invalid:C.toString(16),marker:x,offset:S}}return i.prototype={parse:function(c){function h(){var G=c[C]<<8|c[C+1];return C+=2,G}function m(){var G=h(),j=C+G-2,Y=b(c,j,C);Y&&Y.invalid&&(console.log("readDataBlock - incorrect length, next marker is: "+Y.invalid),j=Y.offset);var X=c.subarray(C,j);return C+=X.length,X}function v(G){for(var j=Math.ceil(G.samplesPerLine/8/G.maxH),Y=Math.ceil(G.scanLines/8/G.maxV),X=0;X<G.components.length;X++){K=G.components[X];var te=Math.ceil(Math.ceil(G.samplesPerLine/8)*K.h/G.maxH),se=Math.ceil(Math.ceil(G.scanLines/8)*K.v/G.maxV),Z=j*K.h,ie=Y*K.v*64*(Z+1);K.blockData=new Int16Array(ie),K.blocksPerLine=te,K.blocksPerColumn=se}G.mcusPerLine=j,G.mcusPerColumn=Y}var w,S,C=0,x=null,I=null,E=[],P=[],L=[],D=h();if(D!==65496)throw new e("SOI not found");for(D=h();D!==65497;){var A,k,R;switch(D){case 65504:case 65505:case 65506:case 65507:case 65508:case 65509:case 65510:case 65511:case 65512:case 65513:case 65514:case 65515:case 65516:case 65517:case 65518:case 65519:case 65534:var B=m();D===65504&&B[0]===74&&B[1]===70&&B[2]===73&&B[3]===70&&B[4]===0&&(x={version:{major:B[5],minor:B[6]},densityUnits:B[7],xDensity:B[8]<<8|B[9],yDensity:B[10]<<8|B[11],thumbWidth:B[12],thumbHeight:B[13],thumbData:B.subarray(14,14+3*B[12]*B[13])}),D===65518&&B[0]===65&&B[1]===100&&B[2]===111&&B[3]===98&&B[4]===101&&(I={version:B[5]<<8|B[6],flags0:B[7]<<8|B[8],flags1:B[9]<<8|B[10],transformCode:B[11]});break;case 65499:for(var F=h()+C-2;C<F;){var M=c[C++],O=new Uint16Array(64);if(M>>4){if(M>>4!=1)throw new e("DQT - invalid table spec");for(k=0;k<64;k++)O[t[k]]=h()}else for(k=0;k<64;k++)O[t[k]]=c[C++];E[15&M]=O}break;case 65472:case 65473:case 65474:if(w)throw new e("Only single frame JPEGs supported");h(),(w={}).extended=D===65473,w.progressive=D===65474,w.precision=c[C++],w.scanLines=h(),w.samplesPerLine=h(),w.components=[],w.componentIds={};var W,_=c[C++],U=0,N=0;for(A=0;A<_;A++){W=c[C];var q=c[C+1]>>4,oe=15&c[C+1];U<q&&(U=q),N<oe&&(N=oe);var ve=c[C+2];R=w.components.push({h:q,v:oe,quantizationId:ve,quantizationTable:null}),w.componentIds[W]=R-1,C+=3}w.maxH=U,w.maxV=N,v(w);break;case 65476:var Te=h();for(A=2;A<Te;){var le=c[C++],ne=new Uint8Array(16),ue=0;for(k=0;k<16;k++,C++)ue+=ne[k]=c[C];var de=new Uint8Array(ue);for(k=0;k<ue;k++,C++)de[k]=c[C];A+=17+ue,(le>>4?P:L)[15&le]=g(ne,de)}break;case 65501:h(),S=h();break;case 65498:h();var K,Ae=c[C++],pe=[];for(A=0;A<Ae;A++){var ae=w.componentIds[c[C++]];K=w.components[ae];var me=c[C++];K.huffmanTableDC=L[me>>4],K.huffmanTableAC=P[15&me],pe.push(K)}var De=c[C++],Re=c[C++],ce=c[C++],Le=p(c,C,w,pe,S,De,Re,ce>>4,15&ce);C+=Le;break;case 65535:c[C]!==255&&C--;break;default:if(c[C-3]===255&&c[C-2]>=192&&c[C-2]<=254){C-=3;break}throw new e("unknown marker "+D.toString(16))}D=h()}for(this.width=w.samplesPerLine,this.height=w.scanLines,this.jfif=x,this.eof=C,this.adobe=I,this.components=[],A=0;A<w.components.length;A++){var ge=E[(K=w.components[A]).quantizationId];ge&&(K.quantizationTable=ge),this.components.push({output:y(w,K),scaleX:K.h/w.maxH,scaleY:K.v/w.maxV,blocksPerLine:K.blocksPerLine,blocksPerColumn:K.blocksPerColumn})}this.numComponents=this.components.length},_getLinearizedBlockData:function(c,h){var m,v,w,S,C,x,I,E,P,L,D,A=this.width/c,k=this.height/h,R=0,B=this.components.length,F=c*h*B,M=new Uint8ClampedArray(F),O=new Uint32Array(c),W=4294967288;for(I=0;I<B;I++){for(v=(m=this.components[I]).scaleX*A,w=m.scaleY*k,R=I,D=m.output,S=m.blocksPerLine+1<<3,C=0;C<c;C++)E=0|C*v,O[C]=(E&W)<<3|7&E;for(x=0;x<h;x++)for(L=S*((E=0|x*w)&W)|(7&E)<<3,C=0;C<c;C++)M[R]=D[L+O[C]],R+=B}var _=this.decodeTransform;if(_)for(I=0;I<F;)for(E=0,P=0;E<B;E++,I++,P+=2)M[I]=(M[I]*_[P]>>8)+_[P+1];return M},_isColorConversionNeeded:function(){return this.adobe?!!this.adobe.transformCode:this.numComponents===3?this.colorTransform!==0:this.colorTransform===1},_convertYccToRgb:function(c){for(var h,m,v,w=0,S=c.length;w<S;w+=3)h=c[w],m=c[w+1],v=c[w+2],c[w]=h-179.456+1.402*v,c[w+1]=h+135.459-.344*m-.714*v,c[w+2]=h-226.816+1.772*m;return c},_convertYcckToRgb:function(c){for(var h,m,v,w,S=0,C=0,x=c.length;C<x;C+=4)h=c[C],m=c[C+1],v=c[C+2],w=c[C+3],c[S++]=m*(-660635669420364e-19*m+.000437130475926232*v-54080610064599e-18*h+.00048449797120281*w-.154362151871126)-122.67195406894+v*(-.000957964378445773*v+.000817076911346625*h-.00477271405408747*w+1.53380253221734)+h*(.000961250184130688*h-.00266257332283933*w+.48357088451265)+w*(-.000336197177618394*w+.484791561490776),c[S++]=107.268039397724+m*(219927104525741e-19*m-.000640992018297945*v+.000659397001245577*h+.000426105652938837*w-.176491792462875)+v*(-.000778269941513683*v+.00130872261408275*h+.000770482631801132*w-.151051492775562)+h*(.00126935368114843*h-.00265090189010898*w+.25802910206845)+w*(-.000318913117588328*w-.213742400323665),c[S++]=m*(-.000570115196973677*m-263409051004589e-19*v+.0020741088115012*h-.00288260236853442*w+.814272968359295)-20.810012546947+v*(-153496057440975e-19*v-.000132689043961446*h+.000560833691242812*w-.195152027534049)+h*(.00174418132927582*h-.00255243321439347*w+.116935020465145)+w*(-.000343531996510555*w+.24165260232407);return c},_convertYcckToCmyk:function(c){for(var h,m,v,w=0,S=c.length;w<S;w+=4)h=c[w],m=c[w+1],v=c[w+2],c[w]=434.456-h-1.402*v,c[w+1]=119.541-h+.344*m+.714*v,c[w+2]=481.816-h-1.772*m;return c},_convertCmykToRgb:function(c){for(var h,m,v,w,S=0,C=1/255,x=0,I=c.length;x<I;x+=4)h=c[x]*C,m=c[x+1]*C,v=c[x+2]*C,w=c[x+3]*C,c[S++]=255+h*(-4.387332384609988*h+54.48615194189176*m+18.82290502165302*v+212.25662451639585*w-285.2331026137004)+m*(1.7149763477362134*m-5.6096736904047315*v-17.873870861415444*w-5.497006427196366)+v*(-2.5217340131683033*v-21.248923337353073*w+17.5119270841813)-w*(21.86122147463605*w+189.48180835922747),c[S++]=255+h*(8.841041422036149*h+60.118027045597366*m+6.871425592049007*v+31.159100130055922*w-79.2970844816548)+m*(-15.310361306967817*m+17.575251261109482*v+131.35250912493976*w-190.9453302588951)+v*(4.444339102852739*v+9.8632861493405*w-24.86741582555878)-w*(20.737325471181034*w+187.80453709719578),c[S++]=255+h*(.8842522430003296*h+8.078677503112928*m+30.89978309703729*v-.23883238689178934*w-14.183576799673286)+m*(10.49593273432072*m+63.02378494754052*v+50.606957656360734*w-112.23884253719248)+v*(.03296041114873217*v+115.60384449646641*w-193.58209356861505)-w*(22.33816807309886*w+180.12613974708367);return c},getData:function(c,h,m){if(this.numComponents>4)throw new e("Unsupported color mode");var v=this._getLinearizedBlockData(c,h);if(this.numComponents===1&&m){for(var w=v.length,S=new Uint8ClampedArray(3*w),C=0,x=0;x<w;x++){var I=v[x];S[C++]=I,S[C++]=I,S[C++]=I}return S}if(this.numComponents===3&&this._isColorConversionNeeded())return this._convertYccToRgb(v);if(this.numComponents===4){if(this._isColorConversionNeeded())return m?this._convertYcckToRgb(v):this._convertYcckToCmyk(v);if(m)return this._convertCmykToRgb(v)}return v}},i}()},(vt=wt())!==void 0&&(bt.exports=vt);let cr=class{static decode(t,r=!1){const s=new Uint8Array(t),a=new Ue;a.parse(s);const{width:n,height:f,numComponents:o,eof:u}=a,l=a.getData(n,f,!0),i=n*f;let g,d=null,p=0,T=0,y=0;if(!r&&u<s.length-1)try{const b=new we(s.subarray(u)).getBytes();d=new Uint8Array(i);let c=0;for(p=0;p<b.length;p++)for(y=7;y>=0;y--)d[c++]=b[p]>>y&1}catch{}if(o===1&&l.length===n*f){const b=new Uint8Array(l.buffer);g=[b,b,b]}else{for(g=[],p=0;p<3;p++)g.push(new Uint8Array(i));for(y=0,T=0;T<i;T++)for(p=0;p<3;p++)g[p][T]=l[y++]}return{width:n,height:f,pixels:g,mask:d}}};const Bt=[{pixelType:"S8",size:1,ctor:Int8Array,range:[-128,127]},{pixelType:"U8",size:1,ctor:Uint8Array,range:[0,255]},{pixelType:"S16",size:2,ctor:Int16Array,range:[-32768,32767]},{pixelType:"U16",size:2,ctor:Uint16Array,range:[0,65536]},{pixelType:"S32",size:4,ctor:Int32Array,range:[-2147483648,2147483647]},{pixelType:"U32",size:4,ctor:Uint32Array,range:[0,4294967296]},{pixelType:"F32",size:4,ctor:Float32Array,range:[-34027999387901484e22,34027999387901484e22]},{pixelType:"F64",size:8,ctor:Float64Array,range:[-17976931348623157e292,17976931348623157e292]}];let je=null;function Mt(){return je||(je=rr(()=>import("./lerc-wasm-BLAYzsLD.js"),[]).then(e=>e.l).then(({default:e})=>e({locateFile:t=>ir(`esri/layers/support/rasterFormats/${t}`)})).then(e=>{fr(e)}),je)}const _e={getBlobInfo:null,decode:null};function hr(e){return 16+(e>>3<<3)}function he(e,t,r){r.set(e.slice(t,t+r.length))}function fr(e){const{_malloc:t,_free:r,_lerc_getBlobInfo:s,_lerc_getDataRanges:a,_lerc_decode_4D:n,asm:f}=e;let o;const u=Object.values(f).find(i=>i&&"buffer"in i&&i.buffer===e.HEAPU8.buffer),l=i=>{const g=i.map(y=>hr(y)),d=g.reduce((y,b)=>y+b),p=t(d);o=new Uint8Array(u.buffer);let T=g[0];g[0]=p;for(let y=1;y<g.length;y++){const b=g[y];g[y]=g[y-1]+T,T=b}return g};_e.getBlobInfo=i=>{const p=new Uint8Array(48),T=new Uint8Array(8*3),[y,b,c]=l([i.length,p.length,T.length]);o.set(i,y),o.set(p,b),o.set(T,c);let h=s(y,i.length,b,c,12,3);if(h)throw r(y),new Error(`lerc-getBlobInfo: error code is ${h}`);o=new Uint8Array(u.buffer),he(o,b,p),he(o,c,T);const m=new Uint32Array(p.buffer),v=new Float64Array(T.buffer),[w,S,,C,x,I,E,P,L,D,A]=m,k={version:w,depthCount:D,width:C,height:x,validPixelCount:E,bandCount:I,blobSize:P,maskCount:L,dataType:S,minValue:v[0],maxValue:v[1],maxZerror:v[2],statistics:[],bandCountWithNoData:A};if(A)return k;if(D===1&&I===1)return r(y),k.statistics.push({minValue:v[0],maxValue:v[1]}),k;const R=D*I*8,B=new Uint8Array(R),F=new Uint8Array(R);let M=y,O=0,W=0,_=!1;if(o.byteLength<y+2*R?(r(y),_=!0,[M,O,W]=l([i.length,R,R]),o.set(i,M)):[O,W]=l([R,R]),o.set(B,O),o.set(F,W),h=a(M,i.length,D,I,O,W),h)throw r(M),_||r(O),new Error(`lerc-getDataRanges: error code is ${h}`);o=new Uint8Array(u.buffer),he(o,O,B),he(o,W,F);const U=new Float64Array(B.buffer),N=new Float64Array(F.buffer),q=k.statistics;for(let oe=0;oe<I;oe++)if(D>1){const ve=U.slice(oe*D,(oe+1)*D),Te=N.slice(oe*D,(oe+1)*D),le=Math.min.apply(null,ve),ne=Math.max.apply(null,Te);q.push({minValue:le,maxValue:ne,depthStats:{minValues:ve,maxValues:Te}})}else q.push({minValue:U[oe],maxValue:N[oe]});return r(M),_||r(O),k},_e.decode=(i,g)=>{const{maskCount:d,depthCount:p,bandCount:T,width:y,height:b,dataType:c,bandCountWithNoData:h}=g,m=Bt[c],v=y*b,w=new Uint8Array(v*T),S=v*p*T*m.size,C=new Uint8Array(S),x=new Uint8Array(T),I=new Uint8Array(8*T),[E,P,L,D,A]=l([i.length,w.length,C.length,x.length,I.length]);o.set(i,E),o.set(w,P),o.set(C,L),o.set(x,D),o.set(I,A);const k=n(E,i.length,d,P,p,y,b,T,c,L,D,A);if(k)throw r(E),new Error(`lerc-decode: error code is ${k}`);o=new Uint8Array(u.buffer),he(o,L,C),he(o,P,w);let R=null;if(h){he(o,D,x),he(o,A,I),R=[];const B=new Float64Array(I.buffer);for(let F=0;F<x.length;F++)R.push(x[F]?B[F]:null)}return r(E),{data:C,maskData:w,noDataValues:R}}}function dr(e,t,r,s,a){if(r<2)return e;const n=new s(t*r);for(let f=0,o=0;f<t;f++)for(let u=0,l=f;u<r;u++,l+=t)n[l]=e[o++];return n}function Gt(e,t={}){const r=t.inputOffset??0,s=e instanceof Uint8Array?e.subarray(r):new Uint8Array(e,r),a=_e.getBlobInfo(s),{data:n,maskData:f,noDataValues:o}=_e.decode(s,a),{width:u,height:l,bandCount:i,depthCount:g,dataType:d,maskCount:p,statistics:T}=a,y=Bt[d],b=new y.ctor(n.buffer),c=[],h=[],m=u*l,v=m*g;for(let E=0;E<i;E++){const P=b.subarray(E*v,(E+1)*v);if(t.returnInterleaved)c.push(P);else{const L=dr(P,m,g,y.ctor);c.push(L)}h.push(f.subarray(E*v,(E+1)*v))}const w=p===0?null:p===1?h[0]:new Uint8Array(m);if(p>1){w.set(h[0]);for(let E=1;E<h.length;E++){const P=h[E];for(let L=0;L<m;L++)w[L]=w[L]&P[L]}}const{noDataValue:S}=t,C=S!=null&&y.range[0]<=S&&y.range[1]>=S;if(p>0&&C)for(let E=0;E<i;E++){const P=c[E],L=h[E]||w;for(let D=0;D<m;D++)L[D]===0&&(P[D]=S)}const x=p===i&&i>1?h:null,{pixelType:I}=y;return{width:u,height:l,bandCount:i,pixelType:I,depthCount:g,statistics:T,pixels:c,mask:w,bandMasks:x,noDataValues:o}}function pr(e,t,r,s=!0){if(t%4!=0||r%4!=0){const a=new ArrayBuffer(4*Math.ceil(r/4)),n=new Uint8Array(a),f=new Uint8Array(e,t,r);if(s)for(let o=0;o<n.length;o+=4)n[o]=f[o+3],n[o+1]=f[o+2],n[o+2]=f[o+1],n[o+3]=f[o];else n.set(f);return new Uint32Array(n.buffer)}if(s){const a=new Uint8Array(e,t,r),n=new Uint8Array(a.length);for(let f=0;f<n.length;f+=4)n[f]=a[f+3],n[f+1]=a[f+2],n[f+2]=a[f+1],n[f+3]=a[f];return new Uint32Array(n.buffer)}return new Uint32Array(e,t,r/4)}function Tt(){const e=[];for(let t=0;t<=257;t++)e[t]=[t];return e}function St(e,t){for(let r=0;r<t.length;r++)e.push(t[r])}const Se=new Set;function ot(e,t,r,s=!0){const a=pr(e,t,r,s);let n=9,f=Tt(),o=32,u=f.length,l=[],i=1,g=a[0],d=0;const p=a.length,T=8*(4*p-r),y=[];for(;g!=null;){if(o>=n)o-=n,d=g>>>32-n,g<<=n;else{d=g>>>32-o,g=a[i++];const c=n-o;o=32-c,d=(d<<c)+(g>>>o),g<<=c}if(d===257)break;if(d===256){n=9,f=Tt(),u=f.length,l=[];continue}const b=f[d];if(b==null){if(d>f.length)throw new Error("data integrity issue: code does not exist on code page");l.push(l[0]),f[u++]=l.slice(),St(y,l)}else St(y,b),l.push(b[0]),l.length>1&&(f[u++]=l.slice()),l=b.slice();if(Se.has(u)&&n++,o===0&&(g=a[i++],o=32),i>p||i===p&&o<=T)break}return new Uint8Array(y)}Se.add(511),Se.add(1023),Se.add(2047),Se.add(4095),Se.add(8191);const kt=(e,t)=>{const r=t.width*t.height,s=t.pixelType;return Math.floor(e.byteLength/(r*Ut(s)))},Ut=e=>{let t=1;switch(e){case Uint8Array:case Int8Array:t=1;break;case Uint16Array:case Int16Array:t=2;break;case Uint32Array:case Int32Array:case Float32Array:t=4;break;case Float64Array:t=8}return t},mr=(e,t)=>{if(8*e.byteLength<t)return null;const r=new Uint8Array(e,0,Math.ceil(t/8)),s=new Uint8Array(t);let a=0,n=0,f=0,o=0;for(f=0;f<r.length-1;f++)for(n=r[f],o=7;o>=0;o--)s[a++]=n>>o&1;for(o=7;a<t-1;)n=r[r.length-1],s[a++]=n>>o&1,o--;return s};let gr=class{static decode(t,r){const s=r.pixelType,a=[],n=r.width*r.height,f=kt(t,r),{bandIds:o,format:u}=r,l=o&&o.length||kt(t,r),i=t.byteLength-t.byteLength%(n*Ut(s)),g=new s(t,0,n*f);let d,p,T,y,b=null;if(u==="bip")for(d=0;d<l;d++){for(T=new s(n),y=o?o[d]:d,p=0;p<n;p++)T[p]=g[p*f+y];a.push(T)}else if(u==="bsq")for(d=0;d<l;d++)y=o?o[d]:d,a.push(g.subarray(y*n,(y+1)*n));return i<t.byteLength-1&&(b=mr(t.slice(i),n)),{pixels:a,mask:b}}};function yr(e,t){let r=0,s="",a=0,n=0;const f=e.length;for(;r<f;)n=e[r++],a=n>>4,a<8?a=1:a===15?(a=4,n=(7&n)<<18|(63&e[r++])<<12|(63&e[r++])<<6|63&e[r++]):a===14?(a=3,n=(15&n)<<12|(63&e[r++])<<6|63&e[r++]):(a=2,n=(31&n)<<6|63&e[r++]),(n!==0||t)&&(s+=String.fromCharCode(n));return s}const it=(()=>{const e=[];return e[254]="NEWSUBFILETYPE",e[255]="SUBFILETYPE",e[256]="IMAGEWIDTH",e[257]="IMAGELENGTH",e[258]="BITSPERSAMPLE",e[259]="COMPRESSION",e[262]="PHOTOMETRICINTERPRETATION",e[263]="THRESHHOLDING",e[264]="CELLWIDTH",e[265]="CELLLENGTH",e[266]="FILLORDER",e[269]="DOCUMENTNAME",e[270]="IMAGEDESCRIPTION",e[271]="MAKE",e[272]="MODEL",e[273]="STRIPOFFSETS",e[274]="ORIENTATION",e[277]="SAMPLESPERPIXEL",e[278]="ROWSPERSTRIP",e[279]="STRIPBYTECOUNTS",e[280]="MINSAMPLEVALUE",e[281]="MAXSAMPLEVALUE",e[282]="XRESOLUTION",e[283]="YRESOLUTION",e[284]="PLANARCONFIGURATION",e[285]="PAGENAME",e[286]="XPOSITION",e[287]="YPOSITION",e[288]="FREEOFFSETS",e[289]="FREEBYTECOUNTS",e[290]="GRAYRESPONSEUNIT",e[291]="GRAYRESPONSECURVE",e[292]="T4OPTIONS",e[293]="T6OPTIONS",e[296]="RESOLUTIONUNIT",e[297]="PAGENUMBER",e[300]="COLORRESPONSEUNIT",e[301]="TRANSFERFUNCTION",e[305]="SOFTWARE",e[306]="DATETIME",e[315]="ARTIST",e[316]="HOSTCOMPUTER",e[317]="PREDICTOR",e[318]="WHITEPOINT",e[319]="PRIMARYCHROMATICITIES",e[320]="COLORMAP",e[321]="HALFTONEHINTS",e[322]="TILEWIDTH",e[323]="TILELENGTH",e[324]="TILEOFFSETS",e[325]="TILEBYTECOUNTS",e[326]="BADFAXLINES",e[327]="CLEANFAXDATA",e[328]="CONSECUTIVEBADFAXLINES",e[330]="SUBIFD",e[332]="INKSET",e[333]="INKNAMES",e[334]="NUMBEROFINKS",e[336]="DOTRANGE",e[337]="TARGETPRINTER",e[338]="EXTRASAMPLES",e[339]="SAMPLEFORMAT",e[340]="SMINSAMPLEVALUE",e[341]="SMAXSAMPLEVALUE",e[342]="TRANSFERRANGE",e[347]="JPEGTABLES",e[512]="JPEGPROC",e[513]="JPEGIFOFFSET",e[514]="JPEGIFBYTECOUNT",e[515]="JPEGRESTARTINTERVAL",e[517]="JPEGLOSSLESSPREDICTORS",e[518]="JPEGPOINTTRANSFORM",e[519]="JPEGQTABLES",e[520]="JPEGDCTABLES",e[521]="JPEGACTABLES",e[529]="YCBCRCOEFFICIENTS",e[530]="YCBCRSUBSAMPLING",e[531]="YCBCRPOSITIONING",e[532]="REFERENCEBLACKWHITE",e[700]="XMP",e[33550]="GEOPIXELSCALE",e[33922]="GEOTIEPOINTS",e[33432]="COPYRIGHT",e[42112]="GDAL_METADATA",e[42113]="GDAL_NODATA",e[50844]="RPCCOEFFICIENT",e[34264]="GEOTRANSMATRIX",e[34735]="GEOKEYDIRECTORY",e[34736]="GEODOUBLEPARAMS",e[34737]="GEOASCIIPARAMS",e[34665]="EXIFIFD",e[34853]="GPSIFD",e[40965]="INTEROPERABILITYIFD",e})(),Cr=(()=>{const e=it.slice();return e[36864]="ExifVersion",e[40960]="FlashpixVersion",e[40961]="ColorSpace",e[42240]="Gamma",e[37121]="ComponentsConfiguration",e[37122]="CompressedBitsPerPixel",e[40962]="PixelXDimension",e[40963]="PixelYDimension",e[37500]="MakerNote",e[37510]="UserComment",e[40964]="RelatedSoundFile",e[36867]="DateTimeOriginal",e[36868]="DateTimeDigitized",e[36880]="OffsetTime",e[36881]="OffsetTimeOriginal",e[36882]="OffsetTimeDigitized",e[37520]="SubSecTime",e[37521]="SubSecTimeOriginal",e[37522]="SubSecTimeDigitized",e[37888]="Temperature",e[37889]="Humidity",e[37890]="Pressure",e[37891]="WaterDepth",e[37892]="Acceleration",e[37893]="CameraElevationAngle",e[42016]="ImageUniqueID",e[42032]="CameraOwnerName",e[42033]="BodySerialNumber",e[42034]="LensSpecification",e[42035]="LensMake",e[42036]="LensModel",e[42037]="LensSerialNumber",e[33434]="ExposureTime",e[33437]="FNumber",e[34850]="ExposureProgram",e[34852]="SpectralSensitivity",e[34855]="PhotographicSensitivity",e[34856]="OECF",e[34864]="SensitivityType",e[34865]="StandardOutputSensitivity",e[34866]="RecommendedExposureIndex",e[34867]="ISOSpeed",e[34868]="ISOSpeedLatitudeyyy",e[34869]="ISOSpeedLatitudezzz",e[37377]="ShutterSpeedValue",e[37378]="ApertureValue",e[37379]="BrightnessValue",e[37380]="ExposureBiasValue",e[37381]="MaxApertureValue",e[37382]="SubjectDistance",e[37383]="MeteringMode",e[37384]="LightSource",e[37385]="Flash",e[37386]="FocalLength",e[37396]="SubjectArea",e[41483]="FlashEnergy",e[41484]="SpatialFrequencyResponse",e[41486]="FocalPlaneXResolution",e[41487]="FocalPlaneYResolution",e[41488]="FocalPlaneResolutionUnit",e[41492]="SubjectLocation",e[41493]="ExposureIndex",e[41495]="SensingMethod",e[41728]="FileSource",e[41729]="SceneType",e[41730]="CFAPattern",e[41985]="CustomRendered",e[41986]="ExposureMode",e[41987]="WhiteBalance",e[41988]="DigitalZoomRatio",e[41989]="FocalLengthIn35mmFilm",e[41990]="SceneCaptureType",e[41991]="GainControl",e[41992]="Contrast",e[41993]="Saturation",e[41994]="Sharpness",e[41995]="DeviceSettingDescription",e[41996]="SubjectDistanceRange",e})(),br=["GPSVersionID","GPSLatitudeRef","GPSLatitude","GPSLongitudeRef","GPSLongitude","GPSAltitudeRef","GPSAltitude","GPSTimeStamp","GPSSatellites","GPSStatus","GPSMeasureMode","GPSDOP","GPSSpeedRef","GPSSpeed","GPSTrackRef","GPSTrack","GPSImgDirectionRef","GPSImgDirection","GPSMapDatum","GPSDestLatitudeRef","GPSDestLatitude","GPSDestLongitudeRef","GPSDestLongitude","GPSDestBearingRef","GPSDestBearing","GPSDestDistanceRef","GPSDestDistance","GPSProcessingMethod","GPSAreaInformation","GPSDateStamp","GPSDifferential","GPSHPositioningError"],wr=(()=>{const e=[];return e[1024]="GTModelTypeGeoKey",e[1025]="GTRasterTypeGeoKey",e[1026]="GTCitationGeoKey",e[2048]="GeographicTypeGeoKey",e[2049]="GeogCitationGeoKey",e[2050]="GeogGeodeticDatumGeoKey",e[2051]="GeogPrimeMeridianGeoKey",e[2052]="GeogLinearUnitsGeoKey",e[2053]="GeogLinearUnitSizeGeoKey",e[2054]="GeogAngularUnitsGeoKey",e[2055]="GeogAngularUnitSizeGeoKey",e[2056]="GeogEllipsoidGeoKey",e[2057]="GeogSemiMajorAxisGeoKey",e[2058]="GeogSemiMinorAxisGeoKey",e[2059]="GeogInvFlatteningGeoKey",e[2061]="GeogPrimeMeridianLongGeoKey",e[2060]="GeogAzimuthUnitsGeoKey",e[3072]="ProjectedCSTypeGeoKey",e[3073]="PCSCitationGeoKey",e[3074]="ProjectionGeoKey",e[3075]="ProjCoordTransGeoKey",e[3076]="ProjLinearUnitsGeoKey",e[3077]="ProjLinearUnitSizeGeoKey",e[3078]="ProjStdParallel1GeoKey",e[3079]="ProjStdParallel2GeoKey",e[3080]="ProjNatOriginLongGeoKey",e[3081]="ProjNatOriginLatGeoKey",e[3082]="ProjFalseEastingGeoKey",e[3083]="ProjFalseNorthingGeoKey",e[3084]="ProjFalseOriginLongGeoKey",e[3085]="ProjFalseOriginLatGeoKey",e[3086]="ProjFalseOriginEastingGeoKey",e[3087]="ProjFalseOriginNorthingGeoKey",e[3088]="ProjCenterLongGeoKey",e[3090]="ProjCenterEastingGeoKey",e[3091]="ProjCenterNorthingGeoKey",e[3092]="ProjScaleAtNatOriginGeoKey",e[3093]="ProjScaleAtCenterGeoKey",e[3094]="ProjAzimuthAngleGeoKey",e[3095]="ProjStraightVertPoleLongGeoKey",e[4096]="VerticalCSTypeGeoKey",e[4097]="VerticalCitationGeoKey",e[4098]="VerticalDatumGeoKey",e[4099]="VerticalUnitsGeoKey",e})(),vr=(e,t)=>{let r=(t||it)[e];return r===void 0&&(r="unknown"+String(e)),r},Ke=new Map;Ke.set("EXIFIFD",Cr),Ke.set("GPSIFD",br);const ke={TIFF_TAGS:it,ifdTags:Ke,GEO_KEYS:wr,getTagName:vr},nt=(()=>{const e=new ArrayBuffer(4),t=new Uint8Array(e);return new Uint32Array(e)[0]=1,t[0]===1})(),It=[0,1,1,2,4,8,1,1,2,4,8,4,8,-1,-1,-1,8,8,8],xe=4294967296,Tr=new Set([1,5,6,7,8,34712,34887]);function at(e,t){let r="unknown";return e===3?r=t===64?"f64":"f32":e===1?t===1?r="u1":t===2?r="u2":t===4?r="u4":t<=8?r="u8":t<=16?r="u16":t<=32&&(r="u32"):e===2&&(t<=8?r="s8":t<=16?r="s16":t<=32&&(r="s32")),r}function Fe(e){let t=null;switch(e?e.toLowerCase():"f32"){case"u1":case"u2":case"u4":case"u8":t=Uint8Array;break;case"u16":t=Uint16Array;break;case"u32":t=Uint32Array;break;case"s8":t=Int8Array;break;case"s16":t=Int16Array;break;case"s32":t=Int32Array;break;case"f64":t=Float64Array;break;default:t=Float32Array}return t}function Sr(e,t){return{x:t[0]*e.x+t[1]*e.y+t[2],y:t[3]*e.x+t[4]*e.y+t[5]}}function _t(e,t){var r;return(r=e.get(t))==null?void 0:r.values}function be(e,t){var r;return(r=e.get(t))==null?void 0:r.values}function xt(e,t){var r,s;return(s=(r=e.get(t))==null?void 0:r.values)==null?void 0:s[0]}function $(e,t){var r,s;return(s=(r=e.get(t))==null?void 0:r.values)==null?void 0:s[0]}function ze(e,t,r,s=0,a=ke.TIFF_TAGS,n=4){const f=n===8,o=f?ut(new DataView(e,r,8),0,t):new DataView(e,r,2).getUint16(0,t),u=4+2*n,l=f?8:2,i=l+o*u;if(r+i>e.byteLength)return{success:!1,ifd:null,nextIFD:null,requiredBufferSize:i};const g=r+i+4<=e.byteLength?Me(new DataView(e,r+i,n===8?8:4),0,t,n===8):null,d=r+l,p=new Map;let T,y,b,c,h,m=0,v=0;for(let w=0;w<o;w++){y=new DataView(e,d+u*w,u),b=y.getUint16(0,t),h=y.getUint16(2,t),c=ke.getTagName(b,a);const S=[];n===2?(m=y.getUint16(4,t),v=y.getUint16(6,t)):n===4?(m=y.getUint32(4,t),v=y.getUint32(8,t)):n===8&&(m=Me(y,4,t,!0),v=Me(y,12,t,!0),S.push(y.getUint32(12,t)),S.push(y.getUint32(16,t))),T={id:b,type:h,valueCount:m,valueOffset:v,valueOffsets:S,values:null},Pr(e,t,T,s,!1,n),p.set(c,T)}return{success:!0,ifd:p,nextIFD:g,requiredBufferSize:i}}const kr=(e,t)=>Gt(e,{inputOffset:t}).pixels[0];function He(e,t){if(t!==1&&t!==2&&t!==4)return e;const r=new Uint8Array(e),s=8/t,a=new Uint8Array(e.byteLength*s);let n=0;const f=2**t-1;for(let o=0;o<r.length;o++){const u=r[o];for(let l=0;l<s;l++)a[n++]=u<<t*l>>>8-t&f}return a.buffer}function Je(e,t,r){const s=new Ue;s.parse(e),s.colorTransform=r===6?-1:0;const a=s.getData(s.width,s.height,t!==1);return new Uint8Array(a.buffer)}function qe(e){const t=new we(e).getBytes(),r=new ArrayBuffer(t.length),s=new Uint8Array(r);return s.set(t),s}async function Xe(e,t,r,s,a){const n=nt===t,f=$(r,"BITSPERSAMPLE"),o=$(r,"SAMPLESPERPIXEL"),u=$(r,"PHOTOMETRICINTERPRETATION"),l=at($(r,"SAMPLEFORMAT")??1,f),i=$(r,"COMPRESSION")??1,g=Fe(l);let d,p,T;if(i===34887)return await Mt(),kr(e,s);if(i===1)d=e.slice(s,s+a),p=new Uint8Array(d);else if(i===8||i===32946)p=new Uint8Array(e,s,a),p=qe(p),d=p.buffer;else if(i===6)p=new Uint8Array(e,s,a),p=Je(p,o,u),d=p.buffer;else if(i===7){const y=r.get("JPEGTABLES").values,b=y.length-2;p=new Uint8Array(b+a-2);for(let h=0;h<b;h++)p[h]=y[h];const c=new Uint8Array(e,s+2,a-2);for(let h=0;h<c.length;h++)p[b+h]=c[h];p=Je(p,o,u),d=p.buffer}else{if(i!==5)throw new Error("tiff-decode: unsupport compression "+i);p=ot(e,s,a,t),d=p.buffer}if(d=He(d,f),l==="u8"||l==="s8"||n)T=new g(d);else{d=new ArrayBuffer(p.length);const y=new Uint8Array(d);switch(l){case"u16":case"s16":for(let b=0;b<p.length;b+=2)y[b]=p[b+1],y[b+1]=p[b];break;case"u32":case"s32":case"f32":for(let b=0;b<p.length;b+=4)y[b]=p[b+3],y[b+1]=p[b+2],y[b+2]=p[b+1],y[b+3]=p[b]}T=new g(d)}return T}async function Ir(e,t,r){const s=be(r,"TILEOFFSETS");if(s===void 0)return null;const a=be(r,"TILEBYTECOUNTS"),{width:n,height:f,pixelType:o,tileWidth:u,tileHeight:l}=st([r]),i=lt(r,t),g=$(r,"SAMPLESPERPIXEL")||t.planes,d=n*f,p=$(r,"BITSPERSAMPLE"),T=($(r,"COMPRESSION")??1)===34887,y=Fe(o),b=[];for(let k=0;k<g;k++)b.push(new y(d));let c,h,m,v,w,S,C,x,I,E,P,L,D;const A=Math.ceil(n/u);if(p%8==0){if(T&&i&&g>1){const k=Math.round(s.length/g);for(c=0;c<k;c++){S=Math.floor(c/A)*l,C=c%A*u,x=S*n+C;for(let R=0;R<g;R++){const B=c*g+R;if(a[B]!==0)for(m=await Xe(e,t.littleEndian,r,s[B],a[B]),E=0,I=x,L=Math.min(u,n-C),P=Math.min(l,f-S),D=b[R],v=0;v<P;v++)for(I=x+v*n,E=v*u,w=0;w<L;w++,I++,E++)D[I]=m[E]}}}else for(c=0;c<s.length;c++)if(a[c]!==0)for(S=Math.floor(c/A)*l,C=c%A*u,x=S*n+C,m=await Xe(e,t.littleEndian,r,s[c],a[c]),E=0,I=x,L=Math.min(u,n-C),P=Math.min(l,f-S),h=0;h<g;h++)if(D=b[h],i||T)for(v=0;v<P;v++)for(I=x+v*n,E=u*l*h+v*u,w=0;w<L;w++,I++,E++)D[I]=m[E];else for(v=0;v<P;v++)for(I=x+v*n,E=v*u*g+h,w=0;w<L;w++,I++,E+=g)D[I]=m[E]}return{width:n,height:f,pixelType:o,pixels:b}}const xr=(e,t,r)=>{const s=nt===t.littleEndian,a=be(r,"STRIPOFFSETS");if(a===void 0)return null;const{width:n,height:f,pixelType:o}=st([r]),u=$(r,"SAMPLESPERPIXEL")||t.planes,l=$(r,"PHOTOMETRICINTERPRETATION"),i=n*f,g=$(r,"BITSPERSAMPLE"),d=Fe(o),p=new d(i*u),T=be(r,"STRIPBYTECOUNTS"),y=$(r,"ROWSPERSTRIP"),b=$(r,"COMPRESSION")??1;let c,h,m,v,w,S,C,x,I,E=y;if(g%8==0)for(c=0;c<a.length;c++){if(w=c*(y*n)*u,E=(c+1)*y>f?f-c*y:y,o==="u8"||o==="s8"||s)b===8||b===32946?(C=new Uint8Array(e,a[c],T[c]),C=qe(C),S=C.buffer):b===6?(C=new Uint8Array(e,a[c],T[c]),C=Je(C,u,l),S=C.buffer):b===5?(C=ot(e,a[c],T[c],t.littleEndian),S=C.buffer):(T[c]!==E*n*u*g/8&&console.log("strip byte counts is different than expected"),S=e.slice(a[c],a[c]+T[c])),S=He(S,g),v=new d(S);else{switch(b===6||b===8||b===32946?(C=new Uint8Array(e,a[c],T[c]),x=qe(C),S=x.buffer):(T[c]!==E*n*u*g/8&&console.log("strip byte counts is different than expected"),S=new ArrayBuffer(T[c]),C=new Uint8Array(e,a[c],T[c]),x=new Uint8Array(S)),o){case"u16":case"s16":for(m=0;m<C.length;m+=2)x[m]=C[m+1],x[m+1]=C[m];break;case"u32":case"s32":case"f32":for(m=0;m<C.length;m+=4)x[m]=C[m+3],x[m+1]=C[m+2],x[m+2]=C[m+1],x[m+3]=C[m]}S=He(S,g),v=new d(S)}p.set(v,w)}const P=[];if(u===1)P.push(p);else for(c=0;c<u;c++){for(I=new d(i),h=0;h<i;h++)I[h]=p[h*u+c];P.push(I)}return{width:n,height:f,pixelType:o,pixels:P}},Ar=(e,t,r)=>{if(!(e&&e.length>0&&t&&r))return null;let s,a,n;const f=e[0].length,o=e.length,u=new Uint8Array(f);for(let l=0;l<o;l++)if(s=e[l],a=t[l],n=r[l],l===0)for(let i=0;i<f;i++)u[i]=s[i]<a||s[i]>n?0:1;else for(let i=0;i<f;i++)u[i]&&(u[i]=s[i]<a||s[i]>n?0:1);return u},Er=e=>{if(!e)return null;const t=e.match(/<Item(.*?)Item>/gi);if(!t||t.length===0)return null;const r=new Map;let s,a,n,f,o;for(let c=0;c<t.length;c++)s=t[c],a=s.slice(6,s.indexOf(">")),f=s.indexOf("sample="),f>-1&&(o=s.slice(f+8,s.indexOf('"',f+8))),f=s.indexOf("name="),f>-1&&(a=s.slice(f+6,s.indexOf('"',f+6))),a&&(n=s.slice(s.indexOf(">")+1,s.indexOf("</Item>")).trim(),o!=null?r.has(a)?r.get(a)[o]=n:r.set(a,[n]):r.set(a,n)),o=null;const u=r.get("STATISTICS_MINIMUM"),l=r.get("STATISTICS_MAXIMUM"),i=r.get("STATISTICS_MEAN"),g=r.get("STATISTICS_STDDEV");let d=null;if(u&&l){d=[];for(let c=0;c<u.length;c++)d.push({min:parseFloat(u[c]),max:parseFloat(l[c]),avg:i&&parseFloat(i[c]),stddev:g&&parseFloat(g[c])})}const p=r.get("BandName"),T=r.get("WavelengthMin"),y=r.get("WavelengthMax");let b=null;if(p){b=[];for(let c=0;c<p.length;c++)b.push({BandName:p[c],WavelengthMin:T&&parseFloat(T[c]),WavelengthMax:y&&parseFloat(y[c])})}return{statistics:d,bandProperties:b,dataType:r.get("DataType"),rawMetadata:r}};function Pr(e,t,r,s=0,a=!1,n=4){if(r.values)return!0;const f=r.type,o=r.valueCount;let u=r.valueOffset,l=[];const i=It[f],g=8*i,d=o*i,p=o*It[f]*8;let T,y;const b=n===8?64:32,c=r.valueOffsets;if(p>b&&d>(a?e.byteLength:e?e.byteLength-u+s:0))return r.offlineOffsetSize=[u,d],r.values=null,!1;if(p<=b){if(!t)if(b<=32)u>>>=32-p;else{const h=c!=null&&c.length?c[0]:u>>>0,m=c!=null&&c.length?c[1]:Math.round((u-h)/xe);p<=32?(u=h>>>32-p,c[0]=u):(u=h*2**(32-p)+(m>>>32-p),c[0]=h,c[1]=m>>>32-p)}if(o===1&&g===b)l=[u];else if(b===64){const h=c!=null&&c.length?c[0]:u>>>0,m=c!=null&&c.length?c[1]:Math.round((u-h)/xe);let v=h,w=32;for(y=1;y<=o;y++){const S=32-g*y%32;if(w<g){const C=v<<S>>>32-w,x=m<<32-w>>>32-w;v=m,l.push(C+x*2**(g-w)),w-=32-(g-w)}else l.push(v<<S>>>32-g),w-=g;w===0&&(w=32,v=m)}}else for(y=1;y<=o;y++){const h=32-g*y;l.push(u<<h>>>32-g)}}else{u-=s,a&&(u=0);for(let h=u;h<u+d;h+=i){switch(f){case 1:case 2:case 7:T=new DataView(e,h,1).getUint8(0);break;case 3:T=new DataView(e,h,2).getUint16(0,t);break;case 4:case 13:T=new DataView(e,h,4).getUint32(0,t);break;case 5:T=new DataView(e,h,4).getUint32(0,t)/new DataView(e,h+4,4).getUint32(0,t);break;case 6:T=new DataView(e,h,1).getInt8(0);break;case 8:T=new DataView(e,h,2).getInt16(0,t);break;case 9:T=new DataView(e,h,4).getInt32(0,t);break;case 10:T=new DataView(e,h,4).getInt32(0,t)/new DataView(e,h+4,4).getInt32(0,t);break;case 11:T=new DataView(e,h,4).getFloat32(0,t);break;case 12:T=new DataView(e,h,8).getFloat64(0,t);break;case 16:case 18:T=ut(new DataView(e,h,8),0,t);break;case 17:T=Rr(new DataView(e,h,8),0,t);break;default:T=null}l.push(T)}}if(f===2){let h="";const m=l;for(l=[],y=0;y<m.length;y++)m[y]===0&&h!==""?(l.push(h),h=""):h+=String.fromCharCode(m[y]);h===""&&l.length!==0||l.push(h)}return r.values=l,!0}function st(e){const t=e[0],r=$(t,"TILEWIDTH"),s=$(t,"TILELENGTH"),a=$(t,"IMAGEWIDTH"),n=$(t,"IMAGELENGTH"),f=$(t,"BITSPERSAMPLE"),o=$(t,"SAMPLESPERPIXEL"),u=$(t,"SAMPLEFORMAT")??1,l=at(u,f),i=lt(t),g=_t(t,"GDAL_NODATA");let d=null;g!=null&&g.length&&(d=g.map(M=>parseFloat(M)),d.some(M=>isNaN(M))&&(d=null));const p=$(t,"COMPRESSION")??1;let T;switch(p){case 1:T="NONE";break;case 2:case 3:case 4:case 32771:T="CCITT";break;case 5:T="LZW";break;case 6:case 7:T="JPEG";break;case 32773:T="PACKBITS";break;case 8:case 32946:T="DEFLATE";break;case 34712:T="JPEG2000";break;case 34887:T="LERC";break;default:T=String(p)}let y=!0,b="";Tr.has(p)||(y=!1,b+="unsupported tag compression "+p),u>3&&(y=!1,b+="unsupported tag sampleFormat "+u),f!==1&&f!==2&&f!==4&&f%8!=0&&(y=!1,b+="unsupported tag bitsPerSample "+f);const c=xt(t,"GEOASCIIPARAMS");let h;if(c){const M=c.split("|").find(W=>W.includes("ESRI PE String = ")),O=M?M.replace("ESRI PE String = ",""):"";h=O.startsWith("COMPD_CS")||O.startsWith("PROJCS")||O.startsWith("GEOGCS")?{wkid:null,wkt:O}:null}const m=be(t,"GEOTIEPOINTS"),v=be(t,"GEOPIXELSCALE"),w=be(t,"GEOTRANSMATRIX"),S=t.has("GEOKEYDIRECTORY")?t.get("GEOKEYDIRECTORY").data:null;let C,x,I=!1,E=!1;if(S){I=$(S,"GTRasterTypeGeoKey")===2;const M=$(S,"GTModelTypeGeoKey");if(M===2){const O=$(S,"GeographicTypeGeoKey");O>=1024&&O<=32766&&(h={wkid:O}),h||O!==32767||(E=!0,h={wkid:4326})}else if(M===1){const O=$(S,"ProjectedCSTypeGeoKey");O>=1024&&O<=32766&&(h={wkid:O})}}if(v&&m&&m.length>=6?(C=[v[0],0,m[3]-m[0]*v[0],0,-Math.abs(v[1]),m[4]-m[1]*v[1]],I&&(C[2]-=.5*C[0]+.5*C[1],C[5]-=.5*C[3]+.5*C[4])):w&&w.length===16&&(C=I?[w[0],w[1],w[3]-.5*w[0],w[4],w[5],w[7]-.5*w[5]]:[w[0],w[1],w[3],w[4],w[5],w[7]]),C){const M=[{x:0,y:n},{x:0,y:0},{x:a,y:n},{x:a,y:0}];let O,W=Number.POSITIVE_INFINITY,_=Number.POSITIVE_INFINITY,U=Number.NEGATIVE_INFINITY,N=Number.NEGATIVE_INFINITY;for(let q=0;q<M.length;q++)O=Sr(M[q],C),W=O.x>W?W:O.x,U=O.x<U?U:O.x,_=O.y>_?_:O.y,N=O.y<N?N:O.y;x={xmin:W,xmax:U,ymin:_,ymax:N,spatialReference:h}}else x={xmin:-.5,ymin:.5-n,xmax:a-.5,ymax:.5,spatialReference:h};E&&(x.xmax-x.xmin>400||Math.max(Math.abs(x.xmin),Math.abs(x.xmax))>361)&&(h=null,x.spatialReference=null);const P=Nt(e);let L,D,A,k,R;if(P.length>0){A=Math.round(Math.log(a/$(P[0],"IMAGEWIDTH"))/Math.LN2);const M=P[P.length-1];k=Math.round(Math.log(a/$(M,"IMAGEWIDTH"))/Math.LN2),L=$(M,"TILEWIDTH"),D=$(M,"TILELENGTH")}L=k!=null&&k>0?L||r:null,D=k!=null&&k>0?D||s:null,r&&(R=[{maxCol:Math.ceil(a/r)-1,maxRow:Math.ceil(n/s)-1,minRow:0,minCol:0}],P.forEach(M=>{R.push({maxCol:Math.ceil($(M,"IMAGEWIDTH")/$(M,"TILEWIDTH"))-1,maxRow:Math.ceil($(M,"IMAGELENGTH")/$(M,"TILELENGTH"))-1,minRow:0,minCol:0})}));const B=xt(e[0],"GDAL_METADATA"),F=Er(B);return b+=" "+Vt({width:a,height:n,tileWidth:r,tileHeight:s,planes:o,ifds:e}),{width:a,height:n,tileWidth:r,tileHeight:s,planes:o,isBSQ:i,pixelType:l,compression:T,noData:d,hasMaskBand:Ft(e).length===P.length+1,isSupported:y,message:b,extent:x,isPseudoGeographic:E,affine:v?null:C,firstPyramidLevel:A,maximumPyramidLevel:k,pyramidBlockWidth:L,pyramidBlockHeight:D,tileBoundary:R,metadata:F}}function lt(e,t){const r=_t(e,"PLANARCONFIGURATION");return r?r[0]===2:!!t&&t.isBSQ}function Nt(e){return e.filter(t=>$(t,"NEWSUBFILETYPE")===1)}function Ft(e){return e.filter(t=>{const r=(4&($(t,"NEWSUBFILETYPE")??0))==4,s=$(t,"PHOTOMETRICINTERPRETATION")===4;return r&&s})}function Dr(e){const{littleEndian:t,isBigTiff:r,firstIFDPos:s}=Lr(e);let a=s;const n=[];do{const f=Or(e,t,a,0,ke.TIFF_TAGS,r?8:4);if(!f.success)break;n.push(f.ifd),a=f.nextIFD}while(a>0);return{...st(n),littleEndian:t,isBigTiff:r,ifds:n,pyramidIFDs:Nt(n),maskIFDs:Ft(n)}}function ut(e,t,r){const s=e.getUint32(t,r),a=e.getUint32(t+4,r);return r?a*xe+s:s*xe+a}function Rr(e,t,r){let s=r?e.getInt32(t,r):e.getUint32(t,r),a=r?e.getUint32(t+4,r):e.getInt32(t+4,r);const n=(r?s:a)>=0?1:-1;return r?s*=n:a*=n,n*(r?a*xe+s:s*xe+a)}function Me(e,t,r,s){return s?ut(e,t,r):e.getUint32(t,r)}function Lr(e){const t=new DataView(e,0,16),r=t.getUint16(0,!1);let s=null;if(r===18761)s=!0;else{if(r!==19789)throw new Error("unexpected endianess byte");s=!1}const a=t.getUint16(2,s);if(a!==42&&a!==43)throw new Error("unexpected tiff identifier");let n=4;const f=a===43;if(f){const o=t.getUint16(n,s);if(n+=2,o!==8)throw new Error("unsupported bigtiff version");if(t.getUint16(n,s)!==0)throw new Error("unsupported bigtiff version");n+=2}return{littleEndian:s,isBigTiff:f,firstIFDPos:Me(t,n,s,f)}}function Or(e,t,r,s=0,a=ke.TIFF_TAGS,n=4){const f=ze(e,t,r,s,a,n);let o;const u=f.ifd;if(u){if(ke.ifdTags.forEach((l,i)=>{u.has(i)&&(o=u.get(i),o.data=ze(e,t,o.valueOffset-s,s,l).ifd)}),u.has("GEOKEYDIRECTORY")){o=u.get("GEOKEYDIRECTORY");const l=o.values;if(l&&l.length>4){const i=l[0]+"."+l[1]+"."+l[2];o.data=ze(e,t,o.valueOffset+6-s,s,ke.GEO_KEYS,2).ifd,o.data&&o.data.set("GEOTIFFVersion",{id:0,type:2,valueCount:1,valueOffset:null,values:[i]})}}if(u.has("XMP")){o=u.get("XMP");const l=o.values;typeof l[0]=="number"&&o.type===7&&(o.values=[yr(new Uint8Array(l))])}}return f}function Vt(e){const{width:t,height:r,tileHeight:s,tileWidth:a}=e,n=e.planes,f=a?a*s:t*r,o=$(e.ifds[0],"BITSPERSAMPLE");let u="";return f*n>2**30/(o>8?o/8:1)&&(u=a?"tiled tiff exceeding 1 gigabits per tile is not supported":"scanline tiff exceeding 1 gigabits is not supported"),u}async function Br(e,t){const{headerInfo:r,ifd:s,offsets:a,sizes:n}=t,f=[];for(let I=0;I<a.length;I++){const E=await Xe(e,r.littleEndian,s,a[I],n[I]||e.byteLength);f.push(E)}const o=lt(s,r),u=$(s,"BITSPERSAMPLE"),l=at($(s,"SAMPLEFORMAT")??1,u),i=$(s,"SAMPLESPERPIXEL")||r.planes,g=Fe(l),d=$(s,"TILEWIDTH"),p=$(s,"TILELENGTH"),T=$(s,"COMPRESSION")??1,y=d*p;let b;const c=[];let h=f[0];const m=T===34887;for(let I=0;I<i;I++){if(b=new g(y),o&&m){if(h=f[I],h.length)for(let E=0;E<y;E++)b[E]=h[I][E+I]}else if(h.length)if(o||m&&!o)b=h.slice(y*I,y*(I+1));else for(let E=0;E<y;E++)b[E]=h[E*i+I];c.push(b)}const v=r.noData?r.noData[0]:t.noDataValue,w=r.metadata?r.metadata.statistics:null,S=w?w.map(I=>I.min):null,C=w?w.map(I=>I.max):null,x={pixelType:l,width:d,height:p,pixels:c,noDataValue:v};return v!=null?rt(x,v):S&&C&&t.applyMinMaxConstraint&&(x.mask=Ar(c,S,C)),x}async function Mr(e,t={}){const r=t.pyramidLevel||0,s=t.headerInfo||Dr(e),{ifds:a,noData:n}=s;if(a.length===0)throw new Error("no valid image file directory");const f=Vt(s);if(f)throw f;let o=null;const u=r===-1?a[a.length-1]:a[r],l=n??t.noDataValue;return o=s.tileWidth?await Ir(e,s,u):await xr(e,s,u),o&&(l!=null&&rt(o,l),o)}var Gr=function(e){var t,r,s,a,n,f;function o(u){var l,i,g,d,p,T,y,b,c,h,m,v,w;for(this.data=u,this.pos=8,this.palette=[],this.imgData=[],this.transparency={},this.animation=null,this.text={},p=null;;){switch(l=this.readUInt32(),b=(function(){var S,C;for(C=[],S=0;S<4;++S)C.push(String.fromCharCode(this.data[this.pos++]));return C}).call(this).join(""),b){case"IHDR":this.width=this.readUInt32(),this.height=this.readUInt32(),this.bits=this.data[this.pos++],this.colorType=this.data[this.pos++],this.compressionMethod=this.data[this.pos++],this.filterMethod=this.data[this.pos++],this.interlaceMethod=this.data[this.pos++];break;case"acTL":this.animation={numFrames:this.readUInt32(),numPlays:this.readUInt32()||1/0,frames:[]};break;case"PLTE":this.palette=this.read(l);break;case"fcTL":p&&this.animation.frames.push(p),this.pos+=4,p={width:this.readUInt32(),height:this.readUInt32(),xOffset:this.readUInt32(),yOffset:this.readUInt32()},d=this.readUInt16(),g=this.readUInt16()||100,p.delay=1e3*d/g,p.disposeOp=this.data[this.pos++],p.blendOp=this.data[this.pos++],p.data=[];break;case"IDAT":case"fdAT":for(b==="fdAT"&&(this.pos+=4,l-=4),u=(p!=null?p.data:void 0)||this.imgData,m=0;0<=l?m<l:m>l;0<=l?++m:--m)u.push(this.data[this.pos++]);break;case"tRNS":switch(this.transparency={},this.colorType){case 3:if(this.transparency.indexed=this.read(l),(c=255-this.transparency.indexed.length)>0)for(v=0;0<=c?v<c:v>c;0<=c?++v:--v)this.transparency.indexed.push(255);break;case 0:this.transparency.grayscale=this.read(l)[0];break;case 2:this.transparency.rgb=this.read(l)}break;case"tEXt":T=(h=this.read(l)).indexOf(0),y=String.fromCharCode.apply(String,h.slice(0,T)),this.text[y]=String.fromCharCode.apply(String,h.slice(T+1));break;case"IEND":return p&&this.animation.frames.push(p),this.colors=(function(){switch(this.colorType){case 0:case 3:case 4:return 1;case 2:case 6:return 3}}).call(this),this.hasAlphaChannel=(w=this.colorType)===4||w===6,i=this.colors+(this.hasAlphaChannel?1:0),this.pixelBitlength=this.bits*i,this.colorSpace=(function(){switch(this.colors){case 1:return"DeviceGray";case 3:return"DeviceRGB"}}).call(this),void(this.imgData=new Uint8Array(this.imgData));default:this.pos+=l}if(this.pos+=4,this.pos>this.data.length)throw new Error("Incomplete or corrupt PNG file")}}return o.load=function(u,l,i){var g;return typeof l=="function"&&(i=l),(g=new XMLHttpRequest).open("GET",u,!0),g.responseType="arraybuffer",g.onload=function(){var d;return d=new o(new Uint8Array(g.response||g.mozResponseArrayBuffer)),typeof(l!=null?l.getContext:void 0)=="function"&&d.render(l),typeof i=="function"?i(d):void 0},g.send(null)},r=1,s=2,t=0,o.prototype.read=function(u){var l,i;for(i=[],l=0;0<=u?l<u:l>u;0<=u?++l:--l)i.push(this.data[this.pos++]);return i},o.prototype.readUInt32=function(){return this.data[this.pos++]<<24|this.data[this.pos++]<<16|this.data[this.pos++]<<8|this.data[this.pos++]},o.prototype.readUInt16=function(){return this.data[this.pos++]<<8|this.data[this.pos++]},o.prototype.decodePixels=function(u){var l,i,g,d,p,T,y,b,c,h,m,v,w,S,C,x,I,E,P,L,D,A,k;if(u==null&&(u=this.imgData),u.length===0)return new Uint8Array(0);for(u=(u=new we(u)).getBytes(),x=(v=this.pixelBitlength/8)*this.width,w=new Uint8Array(x*this.height),T=u.length,C=0,S=0,i=0;S<T;){switch(u[S++]){case 0:for(d=P=0;P<x;d=P+=1)w[i++]=u[S++];break;case 1:for(d=L=0;L<x;d=L+=1)l=u[S++],p=d<v?0:w[i-v],w[i++]=(l+p)%256;break;case 2:for(d=D=0;D<x;d=D+=1)l=u[S++],g=(d-d%v)/v,I=C&&w[(C-1)*x+g*v+d%v],w[i++]=(I+l)%256;break;case 3:for(d=A=0;A<x;d=A+=1)l=u[S++],g=(d-d%v)/v,p=d<v?0:w[i-v],I=C&&w[(C-1)*x+g*v+d%v],w[i++]=(l+Math.floor((p+I)/2))%256;break;case 4:for(d=k=0;k<x;d=k+=1)l=u[S++],g=(d-d%v)/v,p=d<v?0:w[i-v],C===0?I=E=0:(I=w[(C-1)*x+g*v+d%v],E=g&&w[(C-1)*x+(g-1)*v+d%v]),y=p+I-E,b=Math.abs(y-p),h=Math.abs(y-I),m=Math.abs(y-E),c=b<=h&&b<=m?p:h<=m?I:E,w[i++]=(l+c)%256;break;default:throw new Error("Invalid filter algorithm: "+u[S-1])}C++}return w},o.prototype.decodePalette=function(){var u,l,i,g,d,p,T,y,b;for(i=this.palette,p=this.transparency.indexed||[],d=new Uint8Array((p.length||0)+i.length),g=0,i.length,u=0,l=T=0,y=i.length;T<y;l=T+=3)d[g++]=i[l],d[g++]=i[l+1],d[g++]=i[l+2],d[g++]=(b=p[u++])!=null?b:255;return d},o.prototype.copyToImageData=function(u,l){var i,g,d,p,T,y,b,c,h,m,v;if(g=this.colors,h=null,i=this.hasAlphaChannel,this.palette.length&&(h=(v=this._decodedPalette)!=null?v:this._decodedPalette=this.decodePalette(),g=4,i=!0),c=(d=u.data||u).length,T=h||l,p=y=0,g===1)for(;p<c;)b=h?4*l[p/4]:y,m=T[b++],d[p++]=m,d[p++]=m,d[p++]=m,d[p++]=i?T[b++]:this.transparency.grayscale&&this.transparency.grayscale===m?0:255,y=b;else for(;p<c;)b=h?4*l[p/4]:y,d[p++]=T[b++],d[p++]=T[b++],d[p++]=T[b++],d[p++]=i?T[b++]:this.transparency.rgb&&this.transparency.rgb[1]===T[b-3]&&this.transparency.rgb[3]===T[b-2]&&this.transparency.rgb[5]===T[b-1]?0:255,y=b},o.prototype.decode=function(){var u;return u=new Uint8Array(this.width*this.height*4),this.copyToImageData(u,this.decodePixels()),u},n=e.document&&e.document.createElement("canvas"),f=n&&n.getContext("2d"),a=function(u){var l;return f.width=u.width,f.height=u.height,f.clearRect(0,0,u.width,u.height),f.putImageData(u,0,0),(l=new Image).src=n.toDataURL(),l},o.prototype.decodeFrames=function(u){var l,i,g,d,p,T,y,b;if(this.animation){for(b=[],i=p=0,T=(y=this.animation.frames).length;p<T;i=++p)l=y[i],g=u.createImageData(l.width,l.height),d=this.decodePixels(new Uint8Array(l.data)),this.copyToImageData(g,d),l.imageData=g,b.push(l.image=a(g));return b}},o.prototype.renderFrame=function(u,l){var i,g,d;return i=(g=this.animation.frames)[l],d=g[l-1],l===0&&u.clearRect(0,0,this.width,this.height),(d!=null?d.disposeOp:void 0)===r?u.clearRect(d.xOffset,d.yOffset,d.width,d.height):(d!=null?d.disposeOp:void 0)===s&&u.putImageData(d.imageData,d.xOffset,d.yOffset),i.blendOp===t&&u.clearRect(i.xOffset,i.yOffset,i.width,i.height),u.drawImage(i.image,i.xOffset,i.yOffset)},o.prototype.animate=function(u){var l,i,g,d,p,T,y=this;return i=0,T=this.animation,d=T.numFrames,g=T.frames,p=T.numPlays,(l=function(){var b,c;if(b=i++%d,c=g[b],y.renderFrame(u,b),d>1&&i/d<p)return y.animation._timeout=setTimeout(l,c.delay)})()},o.prototype.stopAnimation=function(){var u;return clearTimeout((u=this.animation)!=null?u._timeout:void 0)},o.prototype.render=function(u){var l,i;return u._png&&u._png.stopAnimation(),u._png=this,u.width=this.width,u.height=this.height,l=u.getContext("2d"),this.animation?(this.decodeFrames(l),this.animate(l)):(i=l.createImageData(this.width,this.height),this.copyToImageData(i,this.decodePixels()),l.putImageData(i,0,0))},o}(self);const Ur=new Set(["jpg","png","bmp","gif"]);async function _r(e,t){if(!nt)throw new fe("rasterCoded:decode","lerc decoder is not supported on big endian platform");await Mt();const{offset:r}=t,{width:s,height:a,pixelType:n,statistics:f,depthCount:o,noDataValues:u,bandMasks:l,pixels:i,mask:g}=Gt(e,{inputOffset:r,returnInterleaved:t.returnInterleaved});return new re({width:s,height:a,pixelType:n.toLowerCase(),pixels:i,mask:g,statistics:f,bandMasks:l,depthCount:o,noDataValues:u})}async function Nr(e,t){const r=await Mr(e,{...t,noDataValue:null});Be(r);const s=new re({width:r.width,height:r.height,pixels:r.pixels,pixelType:r.pixelType.toLowerCase(),mask:r.mask,statistics:null});return s.updateStatistics(),s}async function Fr(e,t){const r=await Br(e,t.customOptions),s=new re({width:r.width,height:r.height,pixels:r.pixels,pixelType:r.pixelType.toLowerCase(),mask:r.mask,statistics:null});return s.updateStatistics(),s}function $t(e,t){const r=t.pixelType||"u8",s=re.getPixelArrayConstructor(r),a=r==="u8"?e:new s(e.buffer),n=[],f=t.planes||1;if(f===1)n.push(a);else for(let u=0;u<f;u++){const l=(t.width||1)*(t.height||e.length),i=new s(l);for(let g=0;g<l;g++)i[g]=a[g*f+u];n.push(i)}const o=new re({width:t.width||1,height:t.height||e.length,pixels:n,pixelType:r,statistics:null});return o.updateStatistics(),o}function Vr(e,t){return $t(new we(new Uint8Array(e)).getBytes(),t)}function $r(e,t){return $t(ot(e,t.offset,t.eof,!t.isInputBigEndian),t)}function jr(e,t,r){const{pixelTypeCtor:s}=Hr(t.pixelType),a=(0,gr.decode)(e,{width:t.width,height:t.height,pixelType:s,format:r}),n=new re({width:t.width,height:t.height,pixels:a.pixels,pixelType:t.pixelType,mask:a.mask,statistics:null});return n.updateStatistics(),n}function zr(e,t){const r=cr.decode(e,t.hasNoZlibMask??void 0),s=new re({width:r.width,height:r.height,pixels:r.pixels,pixelType:"U8",mask:r.mask,statistics:null});return s.updateStatistics(),s}function Wr(e,t){const r=new Uint8Array(e),s=new Gr(r),{width:a,height:n}=t,f=a*n,o=s.decode();let u,l=0,i=0;const g=new Uint8Array(f);for(l=0;l<f;l++)g[l]=o[4*l+3];const d=new re({width:a,height:n,pixels:[],pixelType:"U8",mask:g,statistics:[]});for(l=0;l<3;l++){for(u=new Uint8Array(f),i=0;i<f;i++)u[i]=o[4*i+l];d.addData({pixels:u})}return d.updateStatistics(),d}async function Yr(e,t,r,s){const a=new lr,n={applyJpegMask:!1,format:t,...r},f=await a.decode(e,n,s),o=new re(f);return o.updateStatistics(),o}function jt(e){if(e==null)throw new fe("rasterCodec:decode","parameter encodeddata is required.");const t=new Uint8Array(e,0,10);let r="";return t[0]===255&&t[1]===216?r="jpg":t[0]===137&&t[1]===80&&t[2]===78&&t[3]===71?r="png":t[0]===67&&t[1]===110&&t[2]===116&&t[3]===90&&t[4]===73&&t[5]===109&&t[6]===97&&t[7]===103&&t[8]===101&&t[9]===32?r="lerc":t[0]===76&&t[1]===101&&t[2]===114&&t[3]===99&&t[4]===50&&t[5]===32?r="lerc2":t[0]===73&&t[1]===73&&t[2]===42&&t[3]===0||t[0]===77&&t[1]===77&&t[2]===0&&t[3]===42||t[0]===73&&t[1]===73&&t[2]===43&&t[3]===0||t[0]===77&&t[1]===77&&t[2]===0&&t[3]===43?r="tiff":t[0]===71&&t[1]===73&&t[2]===70?r="gif":t[0]===66&&t[1]===77?r="bmp":String.fromCharCode.apply(null,t).toLowerCase().includes("error")&&(r="error"),r}function Kr(e){let t=null;switch(e){case"lerc":case"lerc2":t=_r;break;case"jpg":t=zr;break;case"png":t=Wr;break;case"bsq":case"bip":t=(r,s)=>jr(r,s,e);break;case"tiff":t=Nr;break;case"deflate":t=Vr;break;case"lzw":t=$r;break;case"error":t=()=>{throw new fe("rasterCodec:decode","input data contains error")};break;default:t=()=>{throw new fe("rasterCodec:decode","unsupported raster format")}}return t}function Hr(e){let t=null,r=null;switch(e?e.toLowerCase():"f32"){case"u1":case"u2":case"u4":case"u8":r=255,t=Uint8Array;break;case"u16":r=r||65535,t=Uint16Array;break;case"u32":r=r||2**32-1,t=Uint32Array;break;case"s8":r=r||-128,t=Int8Array;break;case"s16":r=r||-32768,t=Int16Array;break;case"s32":r=r||0-2**31,t=Int32Array;break;default:t=Float32Array}return{pixelTypeCtor:t,noDataValue:r}}function Jr(e,t=1){if(!e)return;const{pixels:r,width:s,height:a,mask:n}=e;if(!r||r.length===0)return;const f=r.length,o=s-1,u=a-1,l=[];let i,g,d,p,T,y,b=null;const c=re.getPixelArrayConstructor(e.pixelType);if(t===0){for(i=0;i<f;i++){for(T=r[i],y=new c(o*u),g=0;g<u;g++)for(p=g*s,d=0;d<o;d++)y[g*o+d]=T[p+d];l.push(y)}if(J(n))for(b=new Uint8Array(o*u),g=0;g<u;g++)for(p=g*s,d=0;d<o;d++)b[g*o+d]=n[p+d]}else{for(i=0;i<f;i++){for(T=r[i],y=new c(o*u),g=0;g<u;g++)for(p=g*s,d=0;d<o;d++)y[g*o+d]=(T[p+d]+T[p+d+1]+T[p+s+d]+T[p+s+d+1])/4;l.push(y)}if(n)for(b=new Uint8Array(o*u),g=0;g<u;g++)for(p=g*s,d=0;d<o;d++)b[g*o+d]=Math.min.apply(null,[n[p+d],n[p+d+1],n[p+s+d],n[p+s+d+1]])}e.width=o,e.height=u,e.mask=b,e.pixels=l}function xo(e){let t=jt(e);return t==="lerc2"?t="lerc":t==="error"&&(t=""),t}async function Ao(e,t={},r){if(e==null)throw new fe("rasterCodec:decode","missing encodeddata parameter.");let s=t.format&&t.format.toLowerCase();if(!(s!=="bsq"&&s!=="bip"||t.width!=null&&t.height!=null))throw new fe("rasterCodec:decode","requires width and height in options parameter.");if(s==="tiff"&&t.customOptions)return Fr(e,t);if((!s||s!=="bsq"&&s!=="bip"&&s!=="deflate"&&s!=="lzw")&&(s=jt(e)),t.useCanvas&&Ur.has(s))return Yr(e,s,t,r);const a=Kr(s);t.isPoint&&((t={...t}).width!=null&&t.width++,t.height!=null&&t.height++);const n=await a(e,t);return n&&(s!=="jpg"&&t.noDataValue!=null&&n.depthCount===1&&rt(n,t.noDataValue,{customFloatTolerance:t.tolerance}),t.isPoint&&Jr(n),n)}var Ze;let Q=Ze=class extends tt{constructor(){super(...arguments),this.blockWidth=void 0,this.blockHeight=void 0,this.compression=null,this.origin=null,this.firstPyramidLevel=null,this.maximumPyramidLevel=null,this.pyramidScalingFactor=2,this.pyramidBlockWidth=null,this.pyramidBlockHeight=null,this.isVirtualTileInfo=!1,this.tileInfo=null,this.transposeInfo=null,this.blockBoundary=null}clone(){return new Ze({blockWidth:this.blockWidth,blockHeight:this.blockHeight,compression:this.compression,origin:ee(this.origin),firstPyramidLevel:this.firstPyramidLevel,maximumPyramidLevel:this.maximumPyramidLevel,pyramidResolutions:ee(this.pyramidResolutions),pyramidScalingFactor:this.pyramidScalingFactor,pyramidBlockWidth:this.pyramidBlockWidth,pyramidBlockHeight:this.pyramidBlockHeight,isVirtualTileInfo:this.isVirtualTileInfo,tileInfo:ee(this.tileInfo),transposeInfo:ee(this.transposeInfo),blockBoundary:ee(this.blockBoundary)})}};V([z({type:Number,json:{write:!0}})],Q.prototype,"blockWidth",void 0),V([z({type:Number,json:{write:!0}})],Q.prototype,"blockHeight",void 0),V([z({type:String,json:{write:!0}})],Q.prototype,"compression",void 0),V([z({type:er,json:{write:!0}})],Q.prototype,"origin",void 0),V([z({type:Number,json:{write:!0}})],Q.prototype,"firstPyramidLevel",void 0),V([z({type:Number,json:{write:!0}})],Q.prototype,"maximumPyramidLevel",void 0),V([z({json:{write:!0}})],Q.prototype,"pyramidResolutions",void 0),V([z({type:Number,json:{write:!0}})],Q.prototype,"pyramidScalingFactor",void 0),V([z({type:Number,json:{write:!0}})],Q.prototype,"pyramidBlockWidth",void 0),V([z({type:Number,json:{write:!0}})],Q.prototype,"pyramidBlockHeight",void 0),V([z({type:Boolean,json:{write:!0}})],Q.prototype,"isVirtualTileInfo",void 0),V([z({json:{write:!0}})],Q.prototype,"tileInfo",void 0),V([z()],Q.prototype,"transposeInfo",void 0),V([z()],Q.prototype,"blockBoundary",void 0),Q=Ze=V([et("esri.layers.support.RasterStorageInfo")],Q);const qr=Q;var Qe;let H=Qe=class extends tt{constructor(e){super(e),this.attributeTable=null,this.bandCount=null,this.colormap=null,this.extent=null,this.format=void 0,this.height=null,this.width=null,this.histograms=null,this.keyProperties={},this.multidimensionalInfo=null,this.noDataValue=null,this.pixelSize=null,this.pixelType=null,this.isPseudoSpatialReference=!1,this.spatialReference=null,this.statistics=null,this.storageInfo=null,this.transform=null}get dataType(){var t,r;const e=((r=(t=this.keyProperties)==null?void 0:t.DataType)==null?void 0:r.toLowerCase())??"generic";return e==="stdtime"?"standard-time":e}get nativeExtent(){return this._get("nativeExtent")||this.extent}set nativeExtent(e){e&&this._set("nativeExtent",e)}get nativePixelSize(){if(Ne(this.transform)||!this.transform.affectsPixelSize)return this.pixelSize;const e=this.nativeExtent;return{x:e.width/this.width,y:e.height/this.height}}get hasMultidimensionalTranspose(){var e;return!!((e=this.storageInfo)!=null&&e.transposeInfo)}clone(){return new Qe({attributeTable:ee(this.attributeTable),bandCount:this.bandCount,colormap:ee(this.colormap),extent:ee(this.extent),nativePixelSize:ee(this.nativePixelSize),format:this.format,height:this.height,width:this.width,histograms:ee(this.histograms),keyProperties:ee(this.keyProperties),multidimensionalInfo:ee(this.multidimensionalInfo),noDataValue:this.noDataValue,pixelSize:ee(this.pixelSize),pixelType:this.pixelType,isPseudoSpatialReference:this.isPseudoSpatialReference,spatialReference:ee(this.spatialReference),statistics:ee(this.statistics),storageInfo:ee(this.storageInfo),transform:ee(this.transform)})}};V([z({json:{write:!0}})],H.prototype,"attributeTable",void 0),V([z({json:{write:!0}})],H.prototype,"bandCount",void 0),V([z({json:{write:!0}})],H.prototype,"colormap",void 0),V([z({type:String,readOnly:!0})],H.prototype,"dataType",null),V([z({type:dt,json:{write:!0}})],H.prototype,"extent",void 0),V([z({type:dt,json:{write:!0}})],H.prototype,"nativeExtent",null),V([z({json:{write:!0}})],H.prototype,"nativePixelSize",null),V([z({json:{write:!0}})],H.prototype,"format",void 0),V([z({json:{write:!0}})],H.prototype,"height",void 0),V([z({json:{write:!0}})],H.prototype,"width",void 0),V([z({json:{write:!0}})],H.prototype,"hasMultidimensionalTranspose",null),V([z({json:{write:!0}})],H.prototype,"histograms",void 0),V([z({json:{write:!0}})],H.prototype,"keyProperties",void 0),V([z({json:{write:!0}})],H.prototype,"multidimensionalInfo",void 0),V([z({json:{write:!0}})],H.prototype,"noDataValue",void 0),V([z({json:{write:!0}})],H.prototype,"pixelSize",void 0),V([z({json:{write:!0}})],H.prototype,"pixelType",void 0),V([z()],H.prototype,"isPseudoSpatialReference",void 0),V([z({type:tr,json:{write:!0}})],H.prototype,"spatialReference",void 0),V([z({json:{write:!0}})],H.prototype,"statistics",void 0),V([z({type:qr,json:{write:!0}})],H.prototype,"storageInfo",void 0),V([z({json:{write:!0}})],H.prototype,"transform",void 0),H=Qe=V([et("esri.layers.support.RasterInfo")],H);const Xr=H;function zt(e){let{altitude:t,azimuth:r}=e;const{hillshadeType:s,pixelSizePower:a=1,pixelSizeFactor:n=1,scalingType:f,isGCS:o,resolution:u}=e,l=s==="multi-directional"?2*e.zFactor:e.zFactor,{x:i,y:g}=u;let d=l/(8*i),p=l/(8*g);if(o&&l>.001&&(d/=111e3,p/=111e3),f==="adjusted")if(o){const L=111e3*i,D=111e3*g;d=(l+L**a*n)/(8*L),p=(l+D**a*n)/(8*D)}else d=(l+i**a*n)/(8*i),p=(l+g**a*n)/(8*g);let T=(90-t)*Math.PI/180,y=Math.cos(T),b=(360-r+90)*Math.PI/180,c=Math.sin(T)*Math.cos(b),h=Math.sin(T)*Math.sin(b);const m=[315,270,225,360,180,0],v=[60,60,60,60,60,90],w=new Float32Array([3,5,3,2,1,4]),S=w.reduce((L,D)=>L+D),C=w.map(L=>L/S),x=s==="multi-directional"?m.length:1,I=new Float32Array(6),E=new Float32Array(6),P=new Float32Array(6);if(s==="multi-directional")for(let L=0;L<x;L++)t=v[L],r=m[L],T=(90-t)*Math.PI/180,y=Math.cos(T),b=(360-r+90)*Math.PI/180,c=Math.sin(T)*Math.cos(b),h=Math.sin(T)*Math.sin(b),I[L]=y,E[L]=c,P[L]=h;else I.fill(y),E.fill(c),P.fill(h);return{resolution:u,factor:[d,p],sinZcosA:c,sinZsinA:h,cosZ:y,sinZcosAs:E,sinZsinAs:P,cosZs:I,weights:C,hillshadeType:["traditional","multi-directional"].indexOf(s)}}function Zr(e,t){if(!Ie(e))return e;const{width:r,height:s,mask:a}=e,n=new Uint8Array(r*s);let f=1;if(J(a)){for(let A=0;A<a.length;A++)if(a[A]){f=a[A];break}n.set(a)}const{factor:o,sinZcosA:u,sinZsinA:l,cosZ:i,sinZcosAs:g,sinZsinAs:d,cosZs:p,weights:T}=zt(t),[y,b]=o,{hillshadeType:c}=t,h=e.pixels[0],m=new Uint8Array(r*s);let v,w,S,C,x,I,E,P;const L=1,D=J(a);for(let A=L;A<s-L;A++){const k=A*r;for(let R=L;R<r-L;R++){if(a&&!a[k+R]){m[k+R]=0;continue}let B=8;if(D&&(B=(a[k-r+R-1]+a[k-r+R]+a[k-r+R+1]+a[k+R-1]+a[k+R+1]+a[k+r+R-1]+a[k+r+R]+a[k+r+R+1])/f,B<7)){m[k+R]=0,n[k+R]=0;continue}a&&B===7?(v=a[k-r+R-1]?h[k-r+R-1]:h[k+R],w=a[k-r+R]?h[k-r+R]:h[k+R],S=a[k-r+R+1]?h[k-r+R+1]:h[k+R],C=a[k+R-1]?h[k+R-1]:h[k+R],x=a[k+R+1]?h[k+R+1]:h[k+R],I=a[k+r+R-1]?h[k+r+R-1]:h[k+R],E=a[k+r+R]?h[k+r+R]:h[k+R],P=a[k+r+R+1]?h[k+r+R+1]:h[k+R]):(v=h[k-r+R-1],w=h[k-r+R],S=h[k-r+R+1],C=h[k+R-1],x=h[k+R+1],I=h[k+r+R-1],E=h[k+r+R],P=h[k+r+R+1]);const F=(S+x+x+P-(v+C+C+I))*y,M=(I+E+E+P-(v+w+w+S))*b,O=Math.sqrt(1+F*F+M*M);let W=0;if(c==="traditional"){let _=255*(i+l*M-u*F)/O;_<0&&(_=0),W=_}else{const _=d.length;for(let U=0;U<_;U++){let N=255*(p[U]+d[U]*M-g[U]*F)/O;N<0&&(N=0),W+=N*T[U]}}m[k+R]=255&W}}for(let A=0;A<s;A++)m[A*r]=m[A*r+1],m[(A+1)*r-1]=m[(A+1)*r-2];for(let A=1;A<r-1;A++)m[A]=m[A+r],m[A+(s-1)*r]=m[A+(s-2)*r];return new re({width:r,height:s,pixels:[m],mask:a?n:null,pixelType:"u8",validPixelCount:e.validPixelCount,statistics:[{minValue:0,maxValue:255}]})}function Qr(e,t,r,s){if(!Ie(e)||!Ie(t))return;const{min:a,max:n}=s,f=e.pixels[0],{pixels:o,mask:u}=t,l=o[0],i=255.00001/(n-a),g=new Uint8ClampedArray(l.length),d=new Uint8ClampedArray(l.length),p=new Uint8ClampedArray(l.length),T=r.length-1;for(let y=0;y<l.length;y++){if(u&&u[y]===0)continue;const b=Math.floor((l[y]-a)*i),[c,h]=r[b<0?0:b>T?T:b],m=f[y],v=m*h,w=v*(1-Math.abs(c%2-1)),S=m-v;switch(Math.floor(c)){case 0:g[y]=v+S,d[y]=w+S,p[y]=S;break;case 1:g[y]=w+S,d[y]=v+S,p[y]=S;break;case 2:g[y]=S,d[y]=v+S,p[y]=w+S;break;case 3:g[y]=S,d[y]=w+S,p[y]=v+S;break;case 4:g[y]=w+S,d[y]=S,p[y]=v+S;break;case 5:case 6:g[y]=v+S,d[y]=S,p[y]=w+S}}e.pixels=[g,d,p],e.updateStatistics()}function Eo(e,t){if(!Ie(e))return e;const r=t.zFactor,s=t.pixelSizePower??1,a=t.pixelSizeFactor??1,n=t.slopeType,f=t.isGCS,{width:o,height:u,mask:l}=e,i=e.pixels[0],g=new Uint8Array(o*u);let d=1;if(J(l)){for(let D=0;D<l.length;D++)if(l[D]){d=l[D];break}g.set(l)}const p=new Float32Array(o*u),{x:T,y}=t.resolution;let b=r/(8*T),c=r/(8*y);f&&Math.abs(r-1)<1e-4&&(b/=111e3,c/=111e3),n==="adjusted"&&(b=(r+T**s*a)/(8*T),c=(r+y**s*a)/(8*y));const h=1;let m,v,w,S,C,x,I,E;const P=J(l);for(let D=h;D<u-h;D++){const A=D*o;for(let k=h;k<o-h;k++){if(l&&!l[A+k]){p[A+k]=0;continue}let R=0;if(P&&(R=(l[A-o+k-1]+l[A-o+k]+l[A-o+k+1]+l[A+k-1]+l[A+k+1]+l[A+o+k-1]+l[A+o+k]+l[A+o+k+1])/d,R<7)){p[A+k]=0,g[A+k]=0;continue}l&&R===7?(m=l[A-o+k-1]?i[A-o+k-1]:i[A+k],v=l[A-o+k]?i[A-o+k]:i[A+k],w=l[A-o+k+1]?i[A-o+k+1]:i[A+k],S=l[A+k-1]?i[A+k-1]:i[A+k],C=l[A+k+1]?i[A+k+1]:i[A+k],x=l[A+o+k-1]?i[A+o+k-1]:i[A+k],I=l[A+o+k]?i[A+o+k]:i[A+k],E=l[A+o+k+1]?i[A+o+k+1]:i[A+k]):(m=i[A-o+k-1],v=i[A-o+k],w=i[A-o+k+1],S=i[A+k-1],C=i[A+k+1],x=i[A+o+k-1],I=i[A+o+k],E=i[A+o+k+1]);const B=(w+C+C+E-(m+S+S+x))*b,F=(x+I+I+E-(m+v+v+w))*c,M=Math.sqrt(B*B+F*F);p[A+k]=n==="percent-rise"?100*M:57.2957795*Math.atan(M)}}for(let D=0;D<u;D++)p[D*o]=p[D*o+1],p[(D+1)*o-1]=p[(D+1)*o-2];for(let D=1;D<o-1;D++)p[D]=p[D+o],p[D+(u-1)*o]=p[D+(u-2)*o];const L=new re({width:o,height:u,pixels:[p],mask:l?g:null,pixelType:"f32",validPixelCount:e.validPixelCount});return L.updateStatistics(),L}function Po(e,t={}){if(!Ie(e))return e;const{width:r,height:s,mask:a}=e,n=e.pixels[0],f=new Uint8Array(r*s);J(a)&&f.set(a);const o=new Float32Array(r*s),{resolution:u}=t,l=u?1/u.x:1,i=u?1/u.y:1,g=1;let d,p,T,y,b,c,h,m;const v=J(a);for(let w=g;w<s-g;w++){const S=w*r;for(let C=g;C<r-g;C++){if(a&&!a[S+C]){o[S+C]=0;continue}let x=0;if(v&&(x=a[S-r+C-1]+a[S-r+C]+a[S-r+C+1]+a[S+C-1]+a[S+C+1]+a[S+r+C-1]+a[S+r+C]+a[S+r+C+1],x<7)){o[S+C]=0,f[S+C]=0;continue}a&&x===7?(d=a[S-r+C-1]?n[S-r+C-1]:n[S+C],p=a[S-r+C]?n[S-r+C]:n[S+C],T=a[S-r+C+1]?n[S-r+C+1]:n[S+C],y=a[S+C-1]?n[S+C-1]:n[S+C],b=a[S+C+1]?n[S+C+1]:n[S+C],c=a[S+r+C-1]?n[S+r+C-1]:n[S+C],h=a[S+r+C]?n[S+r+C]:n[S+C],m=a[S+r+C+1]?n[S+r+C+1]:n[S+C]):(d=n[S-r+C-1],p=n[S-r+C],T=n[S-r+C+1],y=n[S+C-1],b=n[S+C+1],c=n[S+r+C-1],h=n[S+r+C],m=n[S+r+C+1]);const I=(T+b+b+m-(d+y+y+c))*l,E=(c+h+h+m-(d+p+p+T))*i;let P=-1;I===0&&E===0||(P=90-57.29578*Math.atan2(E,-I),P<0&&(P+=360),P===360?P=0:P>360&&(P%=360)),o[S+C]=P}}for(let w=0;w<s;w++)o[w*r]=o[w*r+1],o[(w+1)*r-1]=o[(w+1)*r-2];for(let w=1;w<r-1;w++)o[w]=o[w+r],o[w+(s-1)*r]=o[w+(s-2)*r];return new re({width:r,height:s,pixels:[o],mask:a?f:null,pixelType:"f32",validPixelCount:e.validPixelCount,statistics:[{minValue:0,maxValue:360}]})}const Do=["random","ndvi","ndvi2","ndvi3","elevation","gray","hillshade"],Wt=[{id:"aspect_predefined",type:"multipart",colorRamps:[{fromColor:[190,190,190],toColor:[255,45,8]},{fromColor:[255,45,8],toColor:[255,181,61]},{fromColor:[255,181,61],toColor:[255,254,52]},{fromColor:[255,254,52],toColor:[0,251,50]},{fromColor:[0,251,50],toColor:[255,254,52]},{fromColor:[0,253,255],toColor:[0,181,255]},{fromColor:[0,181,255],toColor:[26,35,253]},{fromColor:[26,35,253],toColor:[255,57,251]},{fromColor:[255,57,251],toColor:[255,45,8]}]},{id:"blackToWhite_predefined",fromColor:[0,0,0],toColor:[255,255,255]},{id:"blueBright_predefined",fromColor:[204,204,255],toColor:[0,0,224]},{id:"blueLightToDark_predefined",fromColor:[211,229,232],toColor:[46,100,140]},{id:"blueGreenBright_predefined",fromColor:[203,245,234],toColor:[48,207,146]},{id:"blueGreenLightToDark_predefined",fromColor:[216,242,237],toColor:[21,79,74]},{id:"brownLightToDark_predefined",fromColor:[240,236,170],toColor:[102,72,48]},{id:"brownToBlueGreenDivergingBright_predefined",type:"multipart",colorRamps:[{fromColor:[156,85,31],toColor:[255,255,191]},{fromColor:[255,255,191],toColor:[33,130,145]}]},{id:"brownToBlueGreenDivergingDark_predefined",type:"multipart",colorRamps:[{fromColor:[110,70,45],toColor:[204,204,102]},{fromColor:[204,204,102],toColor:[48,100,102]}]},{id:"coefficientBias_predefined",fromColor:[214,214,255],toColor:[0,57,148]},{id:"coldToHotDiverging_predefined",type:"multipart",colorRamps:[{fromColor:[69,117,181],toColor:[255,255,191]},{fromColor:[255,255,191],toColor:[214,47,39]}]},{id:"conditionNumber_predefined",type:"multipart",colorRamps:[{fromColor:[0,97,0],toColor:[255,255,0]},{fromColor:[255,255,0],toColor:[255,34,0]}]},{id:"cyanToPurple_predefined",type:"multipart",colorRamps:[{fromColor:[0,245,245],toColor:[0,0,245]},{fromColor:[0,0,245],toColor:[245,0,245]}]},{id:"cyanLightToBlueDark_predefined",type:"multipart",colorRamps:[{fromColor:[182,237,240],toColor:[31,131,224]},{fromColor:[31,131,224],toColor:[9,9,145]}]},{id:"distance_predefined",fromColor:[255,200,0],toColor:[0,0,255]},{id:"elevation1_predefined",type:"multipart",colorRamps:[{fromColor:[175,240,233],toColor:[255,255,179]},{fromColor:[255,255,179],toColor:[0,128,64]},{fromColor:[0,128,64],toColor:[252,186,3]},{fromColor:[252,186,3],toColor:[128,0,0]},{fromColor:[120,0,0],toColor:[105,48,13]},{fromColor:[105,48,13],toColor:[171,171,171]},{fromColor:[171,171,171],toColor:[255,252,255]}]},{id:"elevation2_predefined",type:"multipart",colorRamps:[{fromColor:[118,219,211],toColor:[255,255,199]},{fromColor:[255,255,199],toColor:[255,255,128]},{fromColor:[255,255,128],toColor:[217,194,121]},{fromColor:[217,194,121],toColor:[135,96,38]},{fromColor:[135,96,38],toColor:[150,150,181]},{fromColor:[150,150,181],toColor:[181,150,181]},{fromColor:[181,150,181],toColor:[255,252,255]}]},{id:"errors_predefined",fromColor:[255,235,214],toColor:[196,10,10]},{id:"grayLightToDark_predefined",fromColor:[219,219,219],toColor:[69,69,69]},{id:"greenBright_predefined",fromColor:[204,255,204],toColor:[14,204,14]},{id:"greenLightToDark_predefined",fromColor:[220,245,233],toColor:[34,102,51]},{id:"greenToBlue_predefined",type:"multipart",colorRamps:[{fromColor:[32,204,16],toColor:[0,242,242]},{fromColor:[0,242,242],toColor:[2,33,227]}]},{id:"orangeBright_predefined",fromColor:[255,235,204],toColor:[240,118,5]},{id:"orangeLightToDark_predefined",fromColor:[250,233,212],toColor:[171,65,36]},{id:"partialSpectrum_predefined",type:"multipart",colorRamps:[{fromColor:[242,241,162],toColor:[255,255,0]},{fromColor:[255,255,0],toColor:[255,0,0]},{fromColor:[252,3,69],toColor:[176,7,237]},{fromColor:[176,7,237],toColor:[2,29,173]}]},{id:"partialSpectrum1Diverging_predefined",type:"multipart",colorRamps:[{fromColor:[135,38,38],toColor:[240,149,12]},{fromColor:[240,149,12],toColor:[255,255,191]},{fromColor:[255,255,191],toColor:[74,80,181]},{fromColor:[74,80,181],toColor:[39,32,122]}]},{id:"partialSpectrum2Diverging_predefined",type:"multipart",colorRamps:[{fromColor:[115,77,42],toColor:[201,137,52]},{fromColor:[201,137,52],toColor:[255,255,191]},{fromColor:[255,255,191],toColor:[91,63,176]},{fromColor:[91,63,176],toColor:[81,13,97]}]},{id:"pinkToYellowGreenDivergingBright_predefined",type:"multipart",colorRamps:[{fromColor:[158,30,113],toColor:[255,255,191]},{fromColor:[255,255,191],toColor:[99,110,45]}]},{id:"pinkToYellowGreenDivergingDark_predefined",type:"multipart",colorRamps:[{fromColor:[97,47,73],toColor:[204,204,102]},{fromColor:[204,204,102],toColor:[22,59,15]}]},{id:"precipitation_predefined",type:"multipart",colorRamps:[{fromColor:[194,82,60],toColor:[237,161,19]},{fromColor:[237,161,19],toColor:[255,255,0]},{fromColor:[255,255,0],toColor:[0,219,0]},{fromColor:[0,219,0],toColor:[32,153,143]},{fromColor:[32,153,143],toColor:[11,44,122]}]},{id:"prediction_predefined",type:"multipart",colorRamps:[{fromColor:[40,146,199],toColor:[250,250,100]},{fromColor:[250,250,100],toColor:[232,16,20]}]},{id:"purpleBright_predefined",fromColor:[255,204,255],toColor:[199,0,199]},{id:"purpleToGreenDivergingBright_predefined",type:"multipart",colorRamps:[{fromColor:[77,32,150],toColor:[255,255,191]},{fromColor:[255,255,191],toColor:[20,122,11]}]},{id:"purpleToGreenDivergingDark_predefined",type:"multipart",colorRamps:[{fromColor:[67,14,89],toColor:[204,204,102]},{fromColor:[204,204,102],toColor:[24,79,15]}]},{id:"purpleBlueBright_predefined",fromColor:[223,184,230],toColor:[112,12,242]},{id:"purpleBlueLightToDark_predefined",fromColor:[229,213,242],toColor:[93,44,112]},{id:"purpleRedBright_predefined",fromColor:[255,204,225],toColor:[199,0,99]},{id:"purpleRedLightToDark_predefined",fromColor:[250,215,246],toColor:[143,17,57]},{id:"redBright_predefined",fromColor:[255,204,204],toColor:[219,0,0]},{id:"redLightToDark_predefined",fromColor:[255,224,224],toColor:[143,10,10]},{id:"redToBlueDivergingBright_predefined",type:"multipart",colorRamps:[{fromColor:[196,69,57],toColor:[255,255,191]},{fromColor:[255,255,191],toColor:[48,95,207]}]},{id:"redToBlueDivergingDark_predefined",type:"multipart",colorRamps:[{fromColor:[107,13,13],toColor:[204,204,102]},{fromColor:[204,204,102],toColor:[13,53,97]}]},{id:"redToGreen_predefined",type:"multipart",colorRamps:[{fromColor:[245,0,0],toColor:[245,245,0]},{fromColor:[245,245,0],toColor:[0,245,0]}]},{id:"redToGreenDivergingBright_predefined",type:"multipart",colorRamps:[{fromColor:[186,20,20],toColor:[255,255,191]},{fromColor:[255,255,191],toColor:[54,145,33]}]},{id:"redToGreenDivergingDark_predefined",type:"multipart",colorRamps:[{fromColor:[97,21,13],toColor:[204,204,102]},{fromColor:[204,204,102],toColor:[16,69,16]}]},{id:"slope_predefined",type:"multipart",colorRamps:[{fromColor:[56,168,0],toColor:[255,255,0]},{fromColor:[255,255,0],toColor:[255,0,0]}]},{id:"spectrumFullBright_predefined",type:"multipart",colorRamps:[{fromColor:[255,0,0],toColor:[255,255,0]},{fromColor:[255,255,0],toColor:[0,255,255]},{fromColor:[0,255,255],toColor:[0,0,255]}]},{id:"spectrumFullDark_predefined",type:"multipart",colorRamps:[{fromColor:[153,0,0],toColor:[153,153,0]},{fromColor:[153,153,0],toColor:[0,153,153]},{fromColor:[0,153,153],toColor:[0,0,153]}]},{id:"spectrumFullLight_predefined",type:"multipart",colorRamps:[{fromColor:[255,153,153],toColor:[255,255,153]},{fromColor:[255,255,153],toColor:[153,255,255]},{fromColor:[153,255,255],toColor:[153,153,255]}]},{id:"surface_predefined",type:"multipart",colorRamps:[{fromColor:[112,153,89],toColor:[242,238,162]},{fromColor:[242,238,162],toColor:[242,206,133]},{fromColor:[242,206,133],toColor:[194,140,124]},{fromColor:[194,140,124],toColor:[255,242,255]}]},{id:"temperature_predefined",type:"multipart",colorRamps:[{fromColor:[255,252,255],toColor:[255,0,255]},{fromColor:[255,0,255],toColor:[0,0,255]},{fromColor:[0,0,255],toColor:[0,255,255]},{fromColor:[0,255,255],toColor:[0,255,0]},{fromColor:[0,255,0],toColor:[255,255,0]},{fromColor:[255,255,0],toColor:[255,128,0]},{fromColor:[255,128,0],toColor:[128,0,0]}]},{id:"whiteToBlack_predefined",fromColor:[255,255,255],toColor:[0,0,0]},{id:"yellowToDarkRed_predefined",type:"multipart",colorRamps:[{fromColor:[255,255,128],toColor:[242,167,46]},{fromColor:[242,167,46],toColor:[107,0,0]}]},{id:"yellowToGreenToDarkBlue_predefined",type:"multipart",colorRamps:[{fromColor:[255,255,128],toColor:[56,224,9]},{fromColor:[56,224,9],toColor:[26,147,171]},{fromColor:[26,147,171],toColor:[12,16,120]}]},{id:"yellowToRed_predefined",fromColor:[245,245,0],toColor:[255,0,0]},{id:"yellowGreenBright_predefined",fromColor:[236,252,204],toColor:[157,204,16]},{id:"yellowGreenLightToDark_predefined",fromColor:[215,240,175],toColor:[96,107,45]}],Yt={aspect_predefined:"Aspect",blackToWhite_predefined:"Black to White",blueBright_predefined:"Blue Bright",blueLightToDark_predefined:"Blue Light to Dark",blueGreenBright_predefined:"Blue-Green Bright",blueGreenLightToDark_predefined:"Blue-Green Light to Dark",brownLightToDark_predefined:"Brown Light to Dark",brownToBlueGreenDivergingBright_predefined:"Brown to Blue Green Diverging, Bright",brownToBlueGreenDivergingDark_predefined:"Brown to Blue Green Diverging, Dark",coefficientBias_predefined:"Coefficient Bias",coldToHotDiverging_predefined:"Cold to Hot Diverging",conditionNumber_predefined:"Condition Number",cyanToPurple_predefined:"Cyan to Purple",cyanLightToBlueDark_predefined:"Cyan-Light to Blue-Dark",distance_predefined:"Distance",elevation1_predefined:"Elevation #1",elevation2_predefined:"Elevation #2",errors_predefined:"Errors",grayLightToDark_predefined:"Gray Light to Dark",greenBright_predefined:"Green Bright",greenLightToDark_predefined:"Green Light to Dark",greenToBlue_predefined:"Green to Blue",orangeBright_predefined:"Orange Bright",orangeLightToDark_predefined:"Orange Light to Dark",partialSpectrum_predefined:"Partial Spectrum",partialSpectrum1Diverging_predefined:"Partial Spectrum 1 Diverging",partialSpectrum2Diverging_predefined:"Partial Spectrum 2 Diverging",pinkToYellowGreenDivergingBright_predefined:"Pink to YellowGreen Diverging, Bright",pinkToYellowGreenDivergingDark_predefined:"Pink to YellowGreen Diverging, Dark",precipitation_predefined:"Precipitation",prediction_predefined:"Prediction",purpleBright_predefined:"Purple Bright",purpleToGreenDivergingBright_predefined:"Purple to Green Diverging, Bright",purpleToGreenDivergingDark_predefined:"Purple to Green Diverging, Dark",purpleBlueBright_predefined:"Purple-Blue Bright",purpleBlueLightToDark_predefined:"Purple-Blue Light to Dark",purpleRedBright_predefined:"Purple-Red Bright",purpleRedLightToDark_predefined:"Purple-Red Light to Dark",redBright_predefined:"Red Bright",redLightToDark_predefined:"Red Light to Dark",redToBlueDivergingBright_predefined:"Red to Blue Diverging, Bright",redToBlueDivergingDark_predefined:"Red to Blue Diverging, Dark",redToGreen_predefined:"Red to Green",redToGreenDivergingBright_predefined:"Red to Green Diverging, Bright",redToGreenDivergingDark_predefined:"Red to Green Diverging, Dark",slope_predefined:"Slope",spectrumFullBright_predefined:"Spectrum-Full Bright",spectrumFullDark_predefined:"Spectrum-Full Dark",spectrumFullLight_predefined:"Spectrum-Full Light",surface_predefined:"Surface",temperature_predefined:"Temperature",whiteToBlack_predefined:"White to Black",yellowToDarkRed_predefined:"Yellow to Dark Red",yellowToGreenToDarkBlue_predefined:"Yellow to Green to Dark Blue",yellowToRed_predefined:"Yellow to Red",yellowGreenBright_predefined:"Yellow-Green Bright",yellowGreenLightToDark_predefined:"Yellow-Green Light to Dark"},eo=new Lt({Aspect:"aspect","Black to White":"black-to-white","Blue Bright":"blue-bright","Blue Light to Dark":"blue-light-to-dark","Blue-Green Bright":"blue-green-bright","Blue-Green Light to Dark":"blue-green-light-to-dark","Brown Light to Dark":"brown-light-to-dark","Brown to Blue Green Diverging, Bright":"brown-to-blue-green-diverging-right","Brown to Blue Green Diverging, Dark":"brown-to-blue-green-diverging-dark","Coefficient Bias":"coefficient-bias","Cold to Hot Diverging":"cold-to-hot-diverging","Condition Number":"condition-number","Cyan to Purple":"cyan-to-purple","Cyan-Light to Blue-Dark":"cyan-light-to-blue-dark",Distance:"distance","Elevation #1":"elevation1","Elevation #2":"elevation2",Errors:"errors","Gray Light to Dark":"gray-light-to-dark","Green Bright":"green-bright","Green Light to Dark":"green-light-to-dark","Green to Blue":"green-to-blue","Orange Bright":"orange-bright","Orange Light to Dark":"orange-light-to-dark","Partial Spectrum":"partial-spectrum","Partial Spectrum 1 Diverging":"partial-spectrum-1-diverging","Partial Spectrum 2 Diverging":"partial-spectrum-2-diverging","Pink to YellowGreen Diverging, Bright":"pink-to-yellow-green-diverging-bright","Pink to YellowGreen Diverging, Dark":"pink-to-yellow-green-diverging-dark",Precipitation:"precipitation",Prediction:"prediction","urple Bright":"purple-bright","Purple to Green Diverging, Bright":"purple-to-green-diverging-bright","Purple to Green Diverging, Dark":"purple-to-green-diverging-dark","Purple-Blue Bright":"purple-blue-bright","Purple-Blue Light to Dark":"purple-blue-light-to-dark","Purple-Red Bright":"purple-red-bright","Purple-Red Light to Dark":"purple-red-light-to-dark","Red Bright":"red-bright","Red Light to Dark":"red-light-to-dark","Red to Blue Diverging, Bright":"red-to-blue-diverging-bright","Red to Blue Diverging, Dark":"red-to-blue-diverging-dark","Red to Green":"red-to-green","Red to Green Diverging, Bright":"red-to-green-diverging-bright","Red to Green Diverging, Dark":"red-to-green-diverging-dark",Slope:"slope","Spectrum-Full Bright":"spectrum-full-bright","Spectrum-Full Dark":"spectrum-full-dark","Spectrum-Full Light":"spectrum-full-light",Surface:"surface",Temperature:"temperature","White to Black":"white-to-black","Yellow to Dark Red":"yellow-to-dark-red","Yellow to Green to Dark Blue":"yellow-to-green-to-dark-blue","Yellow to Red":"yellow-to-red","Yellow-Green Bright":"yellow-green-bright","Yellow-Green Light to Dark":"yellow-green-light-to-dark"});function Oe(e,t){if(!e||!t||e.length!==t.length)return!1;for(let r=0;r<e.length;r++)if(e[r]>t[r]+2||e[r]<t[r]-2)return!1;return!0}function to(e,t){if(!e)return;const r=Wt;let s=null;return e.type==="algorithmic"?r.some(a=>{if(Oe(e.fromColor.toRgb(),a.fromColor)&&Oe(e.toColor.toRgb(),a.toColor))return s=a.id,!0}):e.type==="multipart"&&r.some(a=>{const n=e.colorRamps,f=a.colorRamps;if(n&&f&&n.length===f.length&&!f.some((o,u)=>{if(!Oe(n[u].fromColor.toRgb(),new pt(o.fromColor).toRgb())||!Oe(n[u].toColor.toRgb(),new pt(o.toColor).toRgb()))return!0})){if(s)return!0;s=a.id}}),s}function ro(e){const t=to(e);return t?Yt[t]:null}function Ro(e){var a;const t=eo.toJSON(e),r=(a=Object.entries(Yt).find(n=>n[1]===t))==null?void 0:a[0],s=Wt.find(n=>n.id===r);return s?s.colorRamps?{type:"multipart",colorRamps:s.colorRamps.map(n=>({type:"algorithmic",algorithm:"esriCIELabAlgorithm",fromColor:n.fromColor,toColor:n.toColor}))}:{type:"algorithmic",algorithm:"esriCIELabAlgorithm",fromColor:s.fromColor,toColor:s.toColor}:null}function Kt(e){const t=(e=e||{}).numColors||256,r=e.distanceOffset||0,s=e.isCustomInterval!=null?e.isCustomInterval:e.distanceInterval!==null&&e.distanceInterval!==1/(t-1),a=e.distanceInterval||1/(t-1);return{...e,numColors:t,distanceOffset:r,interpolateAlpha:!!e.interpolateAlpha,distanceInterval:a,isCustomInterval:s,weights:e.weights}}function oo(e,t,r){const{numColors:s,distanceOffset:a,distanceInterval:n,isCustomInterval:f}=r,o=e.s===0,u=t.s===0;let l=e.h,i=t.h;o&&!u?l=i:u&&!o&&(t={...t,h:l},i=l);let g,d=Math.abs(i-l);const p=360;d<p/2?g=(i-l)*n:(d=p-d,g=l>i?d*n:-d*n);const T=(t.s-e.s)*n,y=(t.v-e.v)*n;let{s:b,v:c}=e,h=l;if(a){const v=a/n;h=(h+v*g+p)%p,b+=v*T,c+=v*y}const m=[];for(let v=0;v<s-1;v++)m.push({h,s:b,v:c}),h=(h+g+p)%p,b+=T,c+=y;return m.push(f?{h,s:b,v:c}:t),m}function io(e,t,r){const{numColors:s,distanceOffset:a,distanceInterval:n,isCustomInterval:f}=r;let{l:o,a:u,b:l}=e;const i=(t.l-o)*n,g=(t.a-u)*n,d=(t.b-l)*n,p=[];if(a){const T=a/n;o+=T*i,u+=T*g,l+=T*d}for(let T=0;T<s-1;T++)p.push({l:o,a:u,b:l}),o+=i,u+=g,l+=d;return p.push(f?{l:o,a:u,b:l}:t),p}function no(e,t,r){const{numColors:s,distanceOffset:a,distanceInterval:n,isCustomInterval:f}=r,o=e.h,u=t.h,l=2*Math.PI;let i;if(o<=u){const h=u-o,m=u-o-l;i=Math.abs(m)<Math.abs(h)?m:h}else{const h=u+l-o,m=u-o;i=Math.abs(m)<Math.abs(h)?m:h}const g=i*n,d=(t.l-e.l)*n,p=(t.c-e.c)*n;let{l:T,c:y,h:b}=e;if(a){const h=a/n;T+=h*d,y+=h*p,b=(b+h*g+l)%l}const c=[];for(let h=0;h<s-1;h++)c.push({l:T,c:y,h:b}),T+=d,y+=p,b=(b+g+l)%l;return c.push(f?{l:T,c:y,h:b}:t),c}function Ht(e,t){let{fromColor:r,toColor:s}=e;r.length===3&&(r=r.concat([255])),s.length===3&&(s=s.concat([255]));const a=e.algorithm||"esriCIELabAlgorithm",n=Kt(t),{numColors:f,distanceOffset:o,isCustomInterval:u,interpolateAlpha:l}=n;if(f===1&&o===0)return[r];if(f===2&&o===0&&!u)return[r,s];const i={r:r[0],g:r[1],b:r[2]},g={r:s[0],g:s[1],b:s[2]},d=a==="esriCIELabAlgorithm"?io(mt(i),mt(g),n):a==="esriHSVAlgorithm"?oo(Ge(i),Ge(g),n):no(gt(i),gt(g),n),p=[],T=r[3]??255,y=((s[3]??255)-T)/(f-1);for(let b=0;b<f;b++){const{r:c,g:h,b:m}=ar(d[b]),v=l?Math.round(T+y*b):255;p.push([c,h,m,v])}return p}function ao(e,t){const{numColors:r,interpolateAlpha:s}=Kt(t);let a=t==null?void 0:t.weights;const{colorRamps:n}=e;if(a){const d=a.reduce((p,T)=>p+T);a=a.map(p=>p/d)}else{a=[];for(let d=0;d<n.length;d++)a[d]=1/n.length}const f=[];let o=0,u=0;const l=1/(r-1);let i=!1;for(let d=0;d<n.length;d++){let p=i?0:o*l-u,T=d===n.length-1?r-1-o:(a[d]-p)/l;if(i=Math.ceil(T)===T,T=Math.ceil(T),T===0)continue;p/=a[d];const y=Ht(n[d],{numColors:T,interpolateAlpha:s,distanceOffset:p,distanceInterval:l/a[d]});o+=y.length,f.push(...y),u+=a[d]}const g=[...n[n.length-1].toColor];return g.length===3&&g.push(255),f.push(g),f}function so(e,t){const r="toJSON"in e?e.toJSON():e;return r.type==="multipart"?ao(r,t):Ht(r,t)}function At(e,t){const r=so(e,t),s=t==null?void 0:t.interpolateAlpha;return r.forEach((a,n)=>{a.unshift(n),s||a.pop()}),r}function Lo(e){const t=ro(e);if(e){if(e.type==="algorithmic")return{...Et(e),Name:t};if(e.colorRamps){const r=e.colorRamps.map(Et);return{type:"MultiPartColorRamp",NumColorRamps:r.length,ArrayOfColorRamp:r,Name:t}}}}function Et(e){var t;if(e)return{Algorithm:((t=e.toJSON())==null?void 0:t.Algorithm)||"esriHSVAlgorithm",type:"AlgorithmicColorRamp",FromColor:Pt(e.fromColor),ToColor:Pt(e.toColor)}}function Pt(e){const t=Ge(e);return{type:"HsvColor",Hue:t.h,Saturation:t.s,Value:t.v,AlphaValue:255}}function Oo(e){const t=e.reverse().map(r=>{const s=r.toString(16);return s.length<2?"0"+s:s});return 4294967295&Number.parseInt(t.join(""),16)}const Bo=new Lt({none:"none",standardDeviation:"standard-deviation",histogramEqualization:"histogram-equalization",minMax:"min-max",percentClip:"percent-clip",sigmoid:"sigmoid"}),lo={0:"none",3:"standardDeviation",4:"histogramEqualization",5:"minMax",6:"percentClip",9:"sigmoid"},We=1;function Jt(e,t=256){t=Math.min(t,256);const{size:r,counts:s}=e,a=new Uint8Array(r),n=s.reduce((i,g)=>i+g/t,0);let f=0,o=0,u=0,l=n;for(let i=0;i<r;i++)if(u+=s[i],!(i<r-1&&u+s[i+1]<l)){for(;f<t-1&&l<u;)f++,l+=n;for(let g=o;g<=i;g++)a[g]=f;o=i+1}for(let i=o;i<r;i++)a[i]=t-1;return a}function Dt(e){const{minCutOff:t,maxCutOff:r,gamma:s,pixelType:a,rounding:n}=e,f=e.outMin||0,o=e.outMax||255;if(!["u8","u16","s8","s16"].includes(a))return null;const u=t.length;let l,i,g=0;a==="s8"?g=-127:a==="s16"&&(g=-32767);let d=256;["u16","s16"].includes(a)&&(d=65536);const p=[],T=[],y=o-f;for(l=0;l<u;l++)T[l]=r[l]-t[l],p[l]=T[l]===0?0:y/T[l];let b;const c=[];let h,m,v;if(s&&s.length>=u){const w=qt(u,s);for(l=0;l<u;l++){for(v=[],i=0;i<d;i++)if(T[l]!==0)if(h=i+g,b=(h-t[l])/T[l],m=1,s[l]>1&&(m-=(1/y)**(b*w[l])),h<r[l]&&h>t[l]){const S=m*y*b**(1/s[l])+f;v[i]=n==="floor"?Math.floor(S):n==="round"?Math.round(S):S}else h>=r[l]?v[i]=o:v[i]=f;else v[i]=f;c[l]=v}}else for(l=0;l<u;l++){for(v=[],i=0;i<d;i++)if(h=i+g,h<=t[l])v[i]=f;else if(h>=r[l])v[i]=o;else{const w=(h-t[l])*p[l]+f;v[i]=n==="floor"?Math.floor(w):n==="round"?Math.round(w):w}c[l]=v}if(e.contrastOffset!=null){const w=uo(e.contrastOffset,e.brightnessOffset);for(l=0;l<u;l++)for(v=c[l],i=0;i<d;i++)v[i]=w[v[i]]}return{lut:c,offset:g}}function uo(e,t){const r=Math.min(Math.max(e,-100),100),s=Math.min(Math.max(t??0,-100),100),a=255,n=128;let f=0,o=0;const u=new Uint8Array(256);for(f=0;f<256;f++)r>0&&r<100?o=(200*f-100*a+2*a*s)/(2*(100-r))+n:r<=0&&r>-100?o=(200*f-100*a+2*a*s)*(100+r)/2e4+n:r===100?(o=200*f-100*a+(a+1)*(100-r)+2*a*s,o=o>0?a:0):r===-100&&(o=n),u[f]=o>a?a:o<0?0:o;return u}function co(e,t,r){const s=[];for(let a=0;a<t.length;a++){let n=0,f=0,o=0;"min"in t[a]?{min:n,max:f,avg:o}=t[a]:[n,f,o]=t[a];let u=o??0;e!=="u8"&&(u=255*(u-n)/(f-n)),s.push(ho(u))}return s}function ho(e){if(e<=0||e>=255)return We;let t=0;e!==150&&(t=e<=150?45*Math.cos(.01047*e):17*Math.sin(.021*e));const r=255,s=e+t,a=Math.log(e/r),n=Math.log(s/r);if(n===0)return We;const f=a/n;return isNaN(f)?We:Math.min(9.9,Math.max(.01,f))}function fo(e){var x;if(Ne(e)||!((x=e.pixels)!=null&&x.length))return null;const{pixels:t,mask:r,pixelType:s}=e,a=e.width*e.height,n=t.length;let f,o,u,l,i;const g=[],d=[];let p,T,y,b,c,h,m,v,w,S;const C=256;for(l=0;l<n;l++){if(p=new Uint32Array(C),y=t[l],s==="u8")if(f=-.5,o=255.5,r)for(i=0;i<a;i++)r[i]&&p[y[i]]++;else for(i=0;i<a;i++)p[y[i]]++;else{let I=!1;e.statistics||(e.updateStatistics(),I=!0);const E=e.statistics;if(f=E[l].minValue,o=E[l].maxValue,u=(o-f)/C,u===0){!E||e.validPixelCount||I||e.updateStatistics();const P=(e.validPixelCount||e.width*e.height)/C;for(let L=0;L<C;L++)p[L]=Math.round(P*(L+1))-Math.round(P*L)}else{for(T=new Uint32Array(C+1),i=0;i<a;i++)r&&!r[i]||T[Math.floor((y[i]-f)/u)]++;for(i=0;i<C-1;i++)p[i]=T[i];p[C-1]=T[C-1]+T[C]}}for(g.push({min:f,max:o,size:C,counts:p}),b=0,c=0,v=0,i=0;i<C;i++)b+=p[i],c+=i*p[i];for(w=c/b,i=0;i<C;i++)v+=p[i]*(i-w)**2;S=Math.sqrt(v/(b-1)),u=(o-f)/C,h=(w+.5)*u+f,m=S*u,d.push({min:f,max:o,avg:h,stddev:m})}return{statistics:d,histograms:g}}function Mo(e){const t=[];for(let r=0;r<e.length;r++){const{min:s,max:a,size:n,counts:f}=e[r];let o=0,u=0;for(let T=0;T<n;T++)o+=f[T],u+=T*f[T];const l=u/o;let i=0;for(let T=0;T<n;T++)i+=f[T]*(T-l)**2;const g=(a-s)/n,d=(l+.5)*g+s,p=Math.sqrt(i/(o-1))*g;t.push({min:s,max:a,avg:d,stddev:p})}return t}function Pe(e,t){var E;const{pixelBlock:r,bandIds:s,returnHistogramLut:a,rasterInfo:n}=t;let f=null,o=null,u=e.stretchType;if(typeof u=="number"&&(u=lo[u]),e.dra)if(u==="minMax"&&J(r)&&r.statistics)f=r.statistics.map(P=>[P.minValue,P.maxValue,0,0]);else{const P=fo(r);f=J(P)?P.statistics:null,o=J(P)?P.histograms:null}else f=((E=e.statistics)==null?void 0:E.length)>0?e.statistics:Ce(n.statistics),o=e.histograms||Ce(n.histograms);u!=="percentClip"&&u!=="histogramEqualization"||o!=null&&o.length||(u="minMax");const l=(f==null?void 0:f.length)||(o==null?void 0:o.length)||n.bandCount,i=[],g=[];let d,p,T,y,b,c,h,m,v;f&&!Array.isArray(f[0])&&(f=f.map(P=>[P.min,P.max,P.avg,P.stddev]));const[w,S]=nr(n.pixelType);if(!(f!=null&&f.length)){for(f=[],m=0;m<l;m++)f.push([w,S,1,1]);u==="standardDeviation"&&(u="minMax")}switch(u){case"none":for(m=0;m<l;m++)i[m]=w,g[m]=S;break;case"minMax":for(m=0;m<l;m++)i[m]=f[m][0],g[m]=f[m][1];break;case"standardDeviation":for(m=0;m<l;m++)i[m]=f[m][2]-e.numberOfStandardDeviations*f[m][3],g[m]=f[m][2]+e.numberOfStandardDeviations*f[m][3],i[m]<f[m][0]&&(i[m]=f[m][0]),g[m]>f[m][1]&&(g[m]=f[m][1]);break;case"histogramEqualization":for(Be(o),m=0;m<l;m++)i[m]=o[m].min,g[m]=o[m].max;break;case"percentClip":for(Be(o),m=0;m<o.length;m++){for(d=o[m],b=new Uint32Array(d.size),y=[...d.counts],y.length>=20&&(y[0]=y[1]=y[2]=y[y.length-1]=y[y.length-2]=0),T=0,p=(d.max-d.min)/d.size,h=d.min===-.5&&p===1?.5:0,v=0;v<d.size;v++)T+=y[v],b[v]=T;for(c=(e.minPercent||0)*T/100,i[m]=d.min+h,v=0;v<d.size;v++)if(b[v]>c){i[m]=d.min+p*(v+h);break}for(c=(1-(e.maxPercent||0)/100)*T,g[m]=d.max+h,v=d.size-2;v>=0;v--)if(b[v]<c){g[m]=d.min+p*(v+2-h);break}if(g[m]<i[m]){const P=i[m];i[m]=g[m],g[m]=P}}break;default:for(m=0;m<l;m++)i[m]=f[m][0],g[m]=f[m][1]}let C,x,I;return u==="histogramEqualization"?(Be(o),x=o[0].size||256,C=0,a&&(I=o.map(P=>Jt(P)))):(x=e.max||255,C=e.min||0),po({minCutOff:i,maxCutOff:g,outMax:x,outMin:C,histogramLut:I},s)}function po(e,t){if(t==null||t.length===0)return e;const r=Math.max.apply(null,t),{minCutOff:s,maxCutOff:a,outMin:n,outMax:f,histogramLut:o}=e;return s.length===t.length||s.length<=r?e:{minCutOff:t.map(u=>s[u]),maxCutOff:t.map(u=>a[u]),histogramLut:o?t.map(u=>o[u]):null,outMin:n,outMax:f}}function qt(e,t){const r=new Float32Array(e);for(let s=0;s<e;s++)t[s]>1?t[s]>2?r[s]=6.5+(t[s]-2)**2.5:r[s]=6.5+100*(2-t[s])**4:r[s]=1;return r}function Ye(e,t){var P;if(Ne(e)||!((P=e.pixels)!=null&&P.length))return e;const{mask:r,width:s,height:a,pixels:n}=e,{minCutOff:f,maxCutOff:o,gamma:u}=t,l=t.outMin||0,i=t.outMax||255,g=s*a,d=t.outputPixelType||"u8",p=e.pixels.map(()=>re.createEmptyBand(d,g)),T=p.length;let y,b,c,h,m;const v=i-l,w=[],S=[];for(y=0;y<T;y++)S[y]=o[y]-f[y],w[y]=S[y]===0?0:v/S[y];const C=d.startsWith("u")||d.startsWith("s"),x=u&&u.length>=T,I=!!t.isRenderer;if(x){const L=qt(T,u);for(b=0;b<g;b++)if(r==null||r[b])for(y=0;y<T;y++)if(S[y]!==0)if(c=n[y][b],m=(c-f[y])/S[y],h=1,u[y]>1&&(h-=(1/v)**(m*L[y])),c<o[y]&&c>f[y]){const D=h*v*m**(1/u[y])+l;p[y][b]=I?Math.floor(D):C?Math.round(D):D}else c>=o[y]?p[y][b]=i:p[y][b]=l;else p[y][b]=l}else for(b=0;b<g;b++)if(r==null||r[b])for(y=0;y<T;y++)if(c=n[y][b],c<o[y]&&c>f[y]){const L=(c-f[y])*w[y]+l;p[y][b]=I?Math.floor(L):C?Math.round(L):L}else c>=o[y]?p[y][b]=i:p[y][b]=l;const E=new re({width:s,height:a,mask:r,pixels:p,pixelType:d});return E.updateStatistics(),E}function mo(e,t){const{attributeTable:r,bandCount:s}=e;return!(Ne(r)||s>1||t&&r.fields.find(a=>a.name.toLowerCase()===t.toLowerCase())==null)}function go(e){const{bandCount:t,dataType:r,pixelType:s}=e;return r==="elevation"||r==="generic"&&t===1&&(s==="s16"||s==="f32"||s==="f64")}function yo(e){const{bandCount:t,colormap:r}=e;return J(r)&&r.length>0&&t===1}let ye=class extends tt{constructor(e){super(e)}bind(){const{rendererJSON:e}=this;if(!e)return{success:!1};let t;switch(this.lookup={rendererJSON:{}},e.type){case"uniqueValue":t=this._updateUVRenderer(e);break;case"rasterColormap":t=this._updateColormapRenderer(e);break;case"rasterStretch":t=this._updateStretchRenderer(e);break;case"classBreaks":t=this._updateClassBreaksRenderer(e);break;case"rasterShadedRelief":t=this._updateShadedReliefRenderer(e);break;case"vectorField":t=this._updateVectorFieldRenderer();break;case"flowRenderer":t=this._updateFlowRenderer()}return t}symbolize(e){let t=e&&e.pixelBlock;if(!Rt(t))return t;if(e.simpleStretchParams&&this.rendererJSON.type==="rasterStretch")return this.simpleStretch(t,e.simpleStretchParams);try{let r;switch(t.pixels.length>3&&(t=ht(t,[0,1,2])),this.rendererJSON.type){case"uniqueValue":case"rasterColormap":r=this._symbolizeColormap(t);break;case"classBreaks":r=this._symbolizeClassBreaks(t);break;case"rasterStretch":r=this._symbolizeStretch(t,e.bandIds);break;case"rasterShadedRelief":{const s=e.extent,a=s.spatialReference.isGeographic,n={x:(s.xmax-s.xmin)/t.width,y:(s.ymax-s.ymin)/t.height};r=this._symbolizeShadedRelief(t,{isGCS:a,resolution:n});break}}return r}catch(r){return ct.getLogger(this.declaredClass).error("symbolize",r.message),t}}simpleStretch(e,t){if(!Rt(e))return e;try{return e.pixels.length>3&&(e=ht(e,[0,1,2])),Ye(e,{...t,isRenderer:!0})}catch(r){return ct.getLogger(this.declaredClass).error("symbolize",r.message),e}}generateWebGLParameters(e){if(["uniqueValue","rasterColormap","classBreaks"].includes(this.rendererJSON.type)){const{indexedColormap:f,offset:o}=this.lookup.colormapLut||{};return{colormap:f,colormapOffset:o,type:"lut"}}const{pixelBlock:t,isGCS:r,resolution:s,bandIds:a}=e,{rendererJSON:n}=this;return n.type==="rasterStretch"?this._generateStretchWebGLParams(t,n,a):n.type==="rasterShadedRelief"?this._generateShadedReliefWebGLParams(n,r,s??void 0):n.type==="vectorField"?this._generateVectorFieldWebGLParams(n):null}_isLUTChanged(e){if(!this.lookup||!this.lookup.rendererJSON)return!0;if("colorRamp"in this.rendererJSON){const t=this.rendererJSON.colorRamp;return e?JSON.stringify(t)!==JSON.stringify(this.lookup.rendererJSON.colorRamp):(this.rendererJSON,this.lookup.rendererJSON,JSON.stringify(this.rendererJSON)!==JSON.stringify(this.lookup.rendererJSON))}return JSON.stringify(this.rendererJSON)!==JSON.stringify(this.lookup.rendererJSON)}_symbolizeColormap(e){return this._isLUTChanged()&&!this.bind().success?e:Ve(e,this.lookup.colormapLut)}_symbolizeClassBreaks(e){const{canUseIndexedLUT:t}=this._analyzeClassBreaks(this.rendererJSON);return this._isLUTChanged()&&!this.bind().success?e:t?Ve(e,this.lookup.colormapLut):or(e,this.lookup.remapLut??[])}_symbolizeStretch(e,t){var g,d,p;const{rasterInfo:r}=this,{pixelType:s,bandCount:a}=r,n=this.rendererJSON,f=["u8","u16","s8","s16"].includes(s);let o,u;const{dra:l}=n,{gamma:i}=this.lookup;if(n.stretchType==="histogramEqualization"){const T=l?null:(g=this.lookup)==null?void 0:g.histogramLut,y=Pe(n,{rasterInfo:r,pixelBlock:e,bandIds:t,returnHistogramLut:!T}),b=Ye(e,{...y,gamma:i,isRenderer:!0});u=ft(b,{lut:l?y.histogramLut:T,offset:0})}else if(f){if(l){const T=Pe(n,{rasterInfo:r,pixelBlock:e,bandIds:t});o=Dt({pixelType:s,...T,gamma:i,rounding:"floor"})}else if(this._isLUTChanged()){if(!this.bind().success)return e;o=this.lookup?this.lookup.stretchLut:null}else o=this.lookup?this.lookup.stretchLut:null;if(!o)return e;a>1&&(t==null?void 0:t.length)===((d=Ce(e))==null?void 0:d.pixels.length)&&(o==null?void 0:o.lut.length)===a&&(o={lut:t.map(T=>o.lut[T]),offset:o.offset}),u=ft(e,o)}else{const T=Pe(n,{rasterInfo:r,pixelBlock:e,bandIds:t});u=Ye(e,{...T,gamma:i,isRenderer:!0})}if(n.colorRamp){if(this._isLUTChanged(!0)&&!this.bind().success)return e;u=Ve(u,(p=this.lookup)==null?void 0:p.colormapLut)}return u}_symbolizeShadedRelief(e,t){var o;const r=this.rendererJSON,s={...r,...t},a=Zr(e,s);if(!r.colorRamp)return a;let n;if(this._isLUTChanged(!0)){if(!this.bind().success)return a;n=this.lookup?this.lookup.hsvMap:null}else n=this.lookup?this.lookup.hsvMap:null;if(!n)return a;const f=((o=Ce(this.rasterInfo.statistics))==null?void 0:o[0])??{min:0,max:8e3};return Qr(a,e,n,f),a}_isVectorFieldData(){const{bandCount:e,dataType:t}=this.rasterInfo;return e===2&&(t==="vector-magdir"||t==="vector-uv")}_updateVectorFieldRenderer(){return this._isVectorFieldData()?{success:!0}:{success:!1,error:`Unsupported data type "${this.rasterInfo.dataType}"; VectorFieldRenderer only supports "vector-magdir" and "vector-uv".`}}_updateFlowRenderer(){return this._isVectorFieldData()?{success:!0}:{success:!1,error:`Unsupported data type "${this.rasterInfo.dataType}"; FlowRenderer only supports "vector-magdir" and "vector-uv".`}}_updateUVRenderer(e){var l;const{bandCount:t,attributeTable:r,pixelType:s}=this.rasterInfo,a=e.field1;if(!a)return{success:!1,error:"Unsupported renderer; missing UniqueValueRenderer.field."};const n=e.defaultSymbol,f=t===1&&["u8","s8"].includes(s);if(!mo(this.rasterInfo,a)&&!f)return{success:!1,error:"Unsupported data; UniqueValueRenderer is only supported on single band data with a valid raster attribute table."};const o=[];if(J(r)){const i=r.fields.find(g=>g.name.toLowerCase()==="value");if(!i)return{success:!1,error:"Unsupported data; the data's raster attribute table does not have a value field."};r.features.forEach(g=>{var T,y;const d=(T=e.uniqueValueInfos)==null?void 0:T.find(b=>String(b.value)===String(g.attributes[a])),p=(y=d==null?void 0:d.symbol)==null?void 0:y.color;p?o.push([g.attributes[i.name]].concat(p)):n&&o.push([g.attributes[i.name]].concat(n.color))})}else{if(a.toLowerCase()!=="value")return{success:!1,error:'Unsupported renderer; UniqueValueRenderer.field must be "Value" when raster attribute table is not availalbe.'};(l=e.uniqueValueInfos)==null||l.forEach(i=>{var d;const g=(d=i==null?void 0:i.symbol)==null?void 0:d.color;g?o.push([parseInt(""+i.value,10)].concat(g)):n&&o.push([parseInt(""+i.value,10)].concat(n==null?void 0:n.color))})}if(o.length===0)return{success:!1,error:"Invalid UniqueValueRenderer. Cannot find matching records in the raster attribute table."};const u=Ee({colormap:o});return this.lookup={rendererJSON:e,colormapLut:u},this.canRenderInWebGL=!0,{success:!0}}_updateColormapRenderer(e){if(!yo(this.rasterInfo))return{success:!1,error:"Unsupported data; the data source does not have a colormap."};const t=e.colormapInfos.map(s=>[s.value].concat(s.color)).sort((s,a)=>s[0]-a[0]);if(!t||t.length===0)return{success:!1,error:"Unsupported renderer; ColormapRenderer must have meaningful colormapInfos."};const r=Ee({colormap:t});return this.lookup={rendererJSON:e,colormapLut:r},this.canRenderInWebGL=!0,{success:!0}}_updateShadedReliefRenderer(e){if(!go(this.rasterInfo))return{success:!1,error:`Unsupported data type "${this.rasterInfo.dataType}"; ShadedReliefRenderer only supports "elevation", or single band float/s16 data.`};if(e.colorRamp){const t=At(e.colorRamp,{interpolateAlpha:!0}),r=Ee({colormap:t}),s=[],a=r.indexedColormap;for(let n=0;n<a.length;n+=4){const f=Ge({r:a[n],g:a[n+1],b:a[n+2]});s.push([f.h/60,f.s/100,255*f.v/100])}this.lookup={rendererJSON:e,colormapLut:r,hsvMap:s}}else this.lookup=null;return this.canRenderInWebGL=!0,{success:!0}}_analyzeClassBreaks(e){const{attributeTable:t,pixelType:r}=this.rasterInfo,s=J(t)?t.fields.find(f=>f.name.toLowerCase()==="value"):null,a=J(t)?t.fields.find(f=>f.name.toLowerCase()===e.field.toLowerCase()):null,n=s!=null&&a!==null;return{canUseIndexedLUT:["u8","u16","s8","s16"].includes(r)||n,tableValueField:s,tableBreakField:a}}_updateClassBreaksRenderer(e){const{attributeTable:t}=this.rasterInfo,{canUseIndexedLUT:r,tableValueField:s,tableBreakField:a}=this._analyzeClassBreaks(e),n=e.classBreakInfos;if(!(n!=null&&n.length))return{success:!1,error:"Unsupported renderer; missing or invalid ClassBreaksRenderer.classBreakInfos."};const f=n.sort((g,d)=>g.classMaxValue-d.classMaxValue),o=f[f.length-1];let u=e.minValue;if(!r){const g=[];for(let d=0;d<f.length;d++)g.push({value:f[d].classMinValue??u,mappedColor:f[d].symbol.color}),u=f[d].classMaxValue;return g.push({value:o.classMaxValue,mappedColor:o.symbol.color}),this.lookup={rendererJSON:e,remapLut:g},this.canRenderInWebGL=!1,{success:!0}}const l=[];if(J(t)&&s!=null&&a!==null&&s!==a){const g=s.name,d=a.name,p=f[f.length-1],{classMaxValue:T}=p;u=e.minValue;for(const y of t.features){const b=y.attributes[g],c=y.attributes[d],h=c===T?p:c<u?null:f.find(({classMaxValue:m})=>m>c);h&&l.push([b].concat(h.symbol.color))}}else{u=Math.floor(e.minValue);for(let g=0;g<f.length;g++){const d=f[g];for(let p=u;p<d.classMaxValue;p++)l.push([p].concat(d.symbol.color));u=Math.ceil(d.classMaxValue)}o.classMaxValue===u&&l.push([o.classMaxValue].concat(o.symbol.color))}const i=Ee({colormap:l,fillUnspecified:!1});return this.lookup={rendererJSON:e,colormapLut:i},this.canRenderInWebGL=!0,{success:!0}}_isHistogramRequired(e){return e==="percentClip"||e==="histogramEqualization"}_isValidRasterStatistics(e){return J(e)&&e.length>0&&e[0].min!=null&&e[0].max!=null}_updateStretchRenderer(e){var i,g;let{stretchType:t,dra:r}=e;if(!(t==="none"||(i=e.statistics)!=null&&i.length||this._isValidRasterStatistics(this.rasterInfo.statistics)||r))return{success:!1,error:"Unsupported renderer; StretchRenderer.statistics is required when dynamic range adjustment is not used."};const s=Ce(e.histograms||this.rasterInfo.histograms);!this._isHistogramRequired(e.stretchType)||s!=null&&s.length||r||(t="minMax");const{computeGamma:a,useGamma:n,colorRamp:f}=e;let{gamma:o}=e;if(n&&a&&!(o!=null&&o.length)){const d=(g=e.statistics)!=null&&g.length?e.statistics:Ce(this.rasterInfo.statistics);o=co(this.rasterInfo.pixelType,d)}const u=this.rasterInfo.pixelType,l=!r&&["u8","u16","s8","s16"].includes(u);if(t==="histogramEqualization"){const d=s.map(p=>Jt(p));this.lookup={rendererJSON:e,histogramLut:d}}else if(l){const d=Pe(e,{rasterInfo:this.rasterInfo}),p=Dt({pixelType:u,...d,gamma:n?o:null,rounding:"floor"});this.lookup={rendererJSON:e,stretchLut:p}}if(f){const d=At(f,{interpolateAlpha:!0});this.lookup||(this.lookup={rendererJSON:e}),this.lookup.colormapLut=Ee({colormap:d}),this.lookup.rendererJSON=e}return this.lookup.gamma=n&&(o!=null&&o.length)?o:null,this.canRenderInWebGL=!0,{success:!0}}_generateStretchWebGLParams(e,t,r){let s=null,a=null;const n=this.lookup&&this.lookup.colormapLut;t.colorRamp&&n&&(s=n.indexedColormap,a=n.offset),t.stretchType==="histogramEqualization"&&(t={...t,stretchType:"minMax"});const{gamma:f}=this.lookup,o=!!(t.useGamma&&f&&f.some(m=>m!==1)),{minCutOff:u,maxCutOff:l,outMin:i,outMax:g}=Pe(t,{rasterInfo:this.rasterInfo,pixelBlock:e,bandIds:r});let d=0;J(e)&&(d=e.getPlaneCount(),d===2&&((e=e.clone()).statistics=[e.statistics[0]],e.pixels=[e.pixels[0]]));const{bandCount:p}=this.rasterInfo,T=Math.min(3,(r==null?void 0:r.length)||d||p,p),y=new Float32Array(T),b=s||o?1:255;let c;for(c=0;c<u.length;c++)y[c]=l[c]===u[c]?0:(g-i)/(l[c]-u[c])/b;const h=new Float32Array(T);if(o&&f)for(c=0;c<T;c++)f[c]>1?f[c]>2?h[c]=6.5+(f[c]-2)**2.5:h[c]=6.5+100*(2-f[c])**4:h[c]=1;return{bandCount:T,outMin:i/b,outMax:g/b,minCutOff:u,maxCutOff:l,factor:y,useGamma:o,gamma:o?f:[1,1,1],gammaCorrection:o?h:[1,1,1],colormap:s,colormapOffset:a,stretchType:t.stretchType,type:"stretch"}}_generateShadedReliefWebGLParams(e,t=!1,r={x:0,y:0}){var l;let s=null,a=null;const n=this.lookup&&this.lookup.colormapLut;e.colorRamp&&n&&(s=n.indexedColormap,a=n.offset);const f={...e,isGCS:t,resolution:r},o=zt(f),u=(l=Ce(this.rasterInfo.statistics))==null?void 0:l[0];return{...o,minValue:(u==null?void 0:u.min)??0,maxValue:(u==null?void 0:u.max)??8e3,hillshadeType:e.hillshadeType==="traditional"?0:1,type:"hillshade",colormap:s,colormapOffset:a}}_generateVectorFieldWebGLParams(e){var c,h,m;const{style:t,inputUnit:r,outputUnit:s,visualVariables:a,symbolTileSize:n,flowRepresentation:f}=e;let o;const u=((c=this.rasterInfo.statistics)==null?void 0:c[0].min)??0,l=((h=this.rasterInfo.statistics)==null?void 0:h[0].max)??50,i=(a==null?void 0:a.find(v=>v.type==="sizeInfo"))??{type:"sizeInfo",field:"Magnitude",maxDataValue:l,maxSize:.8*n,minDataValue:u,minSize:.2*n},g=i.minDataValue??u,d=i.maxDataValue??l,p=J(i.maxSize)&&J(i.minSize)?[i.minSize/n,i.maxSize/n]:[.2,.8];if(t==="wind_speed"){const v=(p[0]+p[1])/2;p[0]=p[1]=v}const T=J(g)&&J(d)?[g,d]:null;if(t==="classified_arrow")if(J(g)&&J(d)&&J(i)){o=[];const v=(i.maxDataValue-i.minDataValue)/5;for(let w=0;w<6;w++)o.push(i.minDataValue+v*w)}else o=[0,1e-6,3.5,7,10.5,14];const y=f==="flow_to"==(t==="ocean_current_kn"||t==="ocean_current_m")?0:Math.PI,b=a==null?void 0:a.find(v=>v.type==="rotationInfo");return{breakValues:o,dataRange:T,inputUnit:r,outputUnit:s,symbolTileSize:n,symbolPercentRange:p,style:t||"single_arrow",rotation:y,rotationType:(m=this.rasterInfo.storageInfo)!=null&&m.tileInfo&&this.rasterInfo.dataType==="vector-uv"?"geographic":(b==null?void 0:b.rotationType)||e.rotationType,type:"vectorField"}}};function Rt(e){return Ie(e)&&e.validPixelCount!==0}V([z({json:{write:!0}})],ye.prototype,"rendererJSON",void 0),V([z({type:Xr,json:{write:!0}})],ye.prototype,"rasterInfo",void 0),V([z({json:{write:!0}})],ye.prototype,"lookup",void 0),V([z()],ye.prototype,"canRenderInWebGL",void 0),ye=V([et("esri.renderers.support.RasterSymbolizer")],ye);const Go=ye;export{Or as B,ro as C,lt as D,Pr as L,qt as M,Nt as N,st as R,Ao as S,Go as T,Ft as U,At as _,lo as a,qr as b,nt as c,Wt as d,ke as e,Lo as f,Mo as g,eo as h,Do as i,xo as j,so as k,Eo as l,Ro as m,Bo as n,Po as o,fo as p,Dt as q,yr as r,Ye as s,Xr as u,Lr as v,Pe as x,Oo as y};
