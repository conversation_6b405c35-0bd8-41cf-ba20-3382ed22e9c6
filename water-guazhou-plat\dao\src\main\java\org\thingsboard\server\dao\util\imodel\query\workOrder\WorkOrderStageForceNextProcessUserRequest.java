package org.thingsboard.server.dao.util.imodel.query.workOrder;


import org.thingsboard.server.dao.util.imodel.StringUtils;
import org.thingsboard.server.dao.util.imodel.query.IStarHttpRequest;

public class WorkOrderStageForceNextProcessUserRequest extends WorkOrderStageRequest {

    @Override
    public String valid(IStarHttpRequest request) {
        if (StringUtils.isNullOrBlank(getNextProcessUserId())) {
            return "下一步处理人不能为空";
        }

        return super.valid(request);
    }
}
