package org.thingsboard.server.dao.model.sql.statistic;

import java.util.Date;
import java.util.Iterator;

public abstract class TimeIterator<T> implements Iterator<T> {
    private final Date to;
    protected Date now;
    private Date createTime = new Date();

    protected TimeIterator(Date from, Date to) {
        this.now = clampDate(from);
        this.to = clampDate(to);
    }

    @Override
    public boolean hasNext() {
        return now.getTime() < to.getTime();
    }

    /**
     * 防止时间溢出
     *
     * @param date 最大为当前时间
     */
    protected void setNow(Date date) {
        this.now = clampDate(date);
    }

    protected Date clampDate(Date origin) {
        if (origin.getTime() > createTime.getTime())
            origin = createTime;
        return origin;
    }
}
