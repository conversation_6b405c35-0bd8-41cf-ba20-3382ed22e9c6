package org.thingsboard.server.dao.model.sql.smartService.call;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@TableName("tb_msg_visit_back")
public class MsgVisitBack {

    @TableId
    private String id;
    
    private String workOrderId;

    private String phone;

    private Date sendTime;

    private Date visitTime;

    private String content;

    private Date createTime;

    private String sendStatus;

    private String sendUser;

    private String remark;
    
}
