<!-- 采集总览 -->
<template>
  <DrawerBox
    ref="refDrawer"
    :right-drawer="true"
    :right-drawer-width="350"
  >
    <ArcLayout @map-loaded="onMaploaded">
      <CollectPipes :code="curCollect?.code"></CollectPipes>
    </ArcLayout>
    <template #right>
      <el-tabs
        v-model="status.status.value"
        class="collect-tabs"
        :stretch="true"
        @tab-click="() => refreshData()"
      >
        <el-tab-pane
          label="待办"
          name="dealing"
        >
        </el-tab-pane>
        <el-tab-pane
          label="已办"
          name="complete"
        >
        </el-tab-pane>
        <el-tab-pane
          label="总览"
          name="total"
        >
        </el-tab-pane>
      </el-tabs>
      <div class="collection-list">
        <Search
          ref="refSearch"
          style="margin-bottom: 12px"
          :config="SearchConfig"
        ></Search>
        <div class="collections">
          <CollectScrollList
            ref="refCollect"
            :status="'complete'"
            @click="handleItemClick"
            @scroll-end="handleScrollEnd"
          ></CollectScrollList>
        </div>
      </div>
    </template>
  </DrawerBox>
</template>
<script lang="ts" setup>
import { Search as SearchIcon } from '@element-plus/icons-vue'
import CollectScrollList from './components/CollectScrollList.vue'
import { usePipeCollectStatus } from '../hooks/usePipeCollect'
import CollectPipes from './components/CollectPipes.vue'

const refSearch = ref<ISearchIns>()
const refDrawer = ref<IDrawerBoxIns>()
const refCollect = ref<InstanceType<typeof CollectScrollList>>()
const status = usePipeCollectStatus('dealing')
const curCollect = ref<Record<string, any>>()
const staticState: {
  view: __esri.MapView | undefined
} = {
  view: undefined
}
const SearchConfig = reactive<ISearch>({
  filters: [
    {
      type: 'input',
      style: {
        width: '320px'
      },
      prefixIcon: shallowRef(SearchIcon),
      field: 'title',
      appendBtns: [{ perm: true, iconifyIcon: 'ep:filter', click: () => refreshData() }]
    }
  ],
  defaultParams: {}
})
const handleItemClick = async (item: any) => {
  // 显示采集的设备定位
  console.log(item)
  curCollect.value = item
}
const handleScrollEnd = () => {
  refCollect.value
  console.log('scrollend')
  refreshData(true)
}
const onMaploaded = (view: __esri.MapView) => {
  staticState.view = view
}
const refreshData = (append?: boolean) => {
  refCollect.value?.refreshData({ status: status.status.value, title: refSearch.value?.queryParams.title }, append)
}
onMounted(() => {
  refreshData
  refDrawer.value?.toggleDrawer('rtl', true)
})
</script>
<style lang="scss" scoped>
.collect-tabs {
  width: 100%;
  :deep(.el-tabs__header) {
    margin-bottom: 0;
  }
}
.collection-list {
  height: calc(100% - 40px);
  padding: 12px;
  :deep(.search-box) {
    padding: 0;
  }
}
.collections {
  height: calc(100% - 48px);
  padding: 0;
}
</style>
