package org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.device;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartOperation.device.SoDeviceType;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;

@Getter
@Setter
public class SoDeviceTypePageRequest extends AdvancedPageableQueryEntity<SoDeviceType, SoDeviceTypePageRequest> {
    // 类别编码
    private String serialId;

    // 名称
    private String name;

    // 父级id
    private String parentId;

}
