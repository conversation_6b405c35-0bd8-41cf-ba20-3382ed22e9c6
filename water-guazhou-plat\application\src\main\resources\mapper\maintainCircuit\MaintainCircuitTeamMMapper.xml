<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.maintainCircuit.MaintainCircuitTeamMMapper">
    <select id="getList" resultType="org.thingsboard.server.dao.model.sql.maintainCircuit.MaintainCircuitTeamM">

        select a.*, b.first_name as creatorName
        from tb_maintain_circuit_team_m a
                 left join tb_user b on a.creator = b.id
        where a.name like '%' || #{name} || '%'  and a.type like '%' || #{type} || '%'
          and a.tenant_id = #{tenantId}
        order by a.create_time desc
        offset (#{page} - 1) * #{size} limit #{size}
    </select>

    <select id="getListCount" resultType="int">

        select count(*)
        from tb_maintain_circuit_team_m a
                 left join tb_user b on a.creator = b.id
        where a.name like '%' || #{name} || '%' and a.type like '%' || #{type} || '%'
          and a.tenant_id = #{tenantId}
    </select>

</mapper>