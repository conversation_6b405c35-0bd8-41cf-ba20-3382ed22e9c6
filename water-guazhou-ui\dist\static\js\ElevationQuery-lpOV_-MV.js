import{cY as U,A as W,fr as J,_ as D,bM as I,d$ as K,B as Q,fs as X,u as A,b as ee,ft as L}from"./MapView-DaoQedLH.js";import{i as te,w as $,$ as g,T as ie,s as d,U as q,Z as O,u as se}from"./Point-WxyopZva.js";import{T as v,$ as ne,R as x,eZ as Z}from"./index-r0dFAfgr.js";import"./widget-BcWKanF2.js";import"./pe-B8dP0-Ut.js";const M=te.getLogger("esri.layers.support.ElevationSampler");class B{queryElevation(e){return oe(e.clone(),this)}on(){return ue}projectIfRequired(e,t){return Y(e,t)}}class ae extends B{get spatialReference(){return this.extent.spatialReference}constructor(e,t,i){super(),this.tile=e,this.noDataValue=i;const s=e.tile.extent;this.extent=U(s,t.spatialReference),this.extent.zmin=e.zmin,this.extent.zmax=e.zmax,this._aaExtent=s;const a=g(t.spatialReference),n=t.lodAt(e.tile.level).resolution*a;this.demResolution={min:n,max:n}}contains(e){const t=this.projectIfRequired(e,this.spatialReference);return!v(t)&&this.containsAt(t.x,t.y)}containsAt(e,t){return J(this._aaExtent,e,t)}elevationAt(e,t){if(!this.containsAt(e,t)){const i=this.extent,s=`${i.xmin}, ${i.ymin}, ${i.xmax}, ${i.ymax}`;return M.warn("#elevationAt()",`Point used to sample elevation (${e}, ${t}) is outside of the sampler extent (${s})`),this.noDataValue}return ne(this.tile.sample(e,t),this.noDataValue)}}class G extends B{get spatialReference(){return this.extent.spatialReference}constructor(e,t,i){let s;super(),typeof t=="number"?(this.noDataValue=t,s=null):(s=t,this.noDataValue=i),this.samplers=s?e.map(n=>new ae(n,s,this.noDataValue)):e;const a=this.samplers[0];if(a){this.extent=a.extent.clone();const{min:n,max:l}=a.demResolution;this.demResolution={min:n,max:l};for(let o=1;o<this.samplers.length;o++){const r=this.samplers[o];this.extent.union(r.extent),this.demResolution.min=Math.min(this.demResolution.min,r.demResolution.min),this.demResolution.max=Math.max(this.demResolution.max,r.demResolution.max)}}else this.extent=U(W(),s.spatialReference),this.demResolution={min:0,max:0}}elevationAt(e,t){for(const i of this.samplers)if(i.containsAt(e,t))return i.elevationAt(e,t);return M.warn("#elevationAt()",`Point used to sample elevation (${e}, ${t}) is outside of the sampler`),this.noDataValue}}function oe(c,e){const t=Y(c,e.spatialReference);if(!t)return null;switch(c.type){case"point":le(c,t,e);break;case"polyline":re(c,t,e);break;case"multipoint":ce(c,t,e)}return c}function Y(c,e){if(v(c))return null;const t=c.spatialReference;if(t.equals(e))return c;const i=ie(c,e);return i||M.error(`Cannot project geometry spatial reference (wkid:${t.wkid}) to elevation sampler spatial reference (wkid:${e.wkid})`),i}function le(c,e,t){c.z=t.elevationAt(e.x,e.y)}function re(c,e,t){y.spatialReference=e.spatialReference;const i=c.hasM&&!c.hasZ;for(let s=0;s<c.paths.length;s++){const a=c.paths[s],n=e.paths[s];for(let l=0;l<a.length;l++){const o=a[l],r=n[l];y.x=r[0],y.y=r[1],i&&(o[3]=o[2]),o[2]=t.elevationAt(y.x,y.y)}}c.hasZ=!0}function ce(c,e,t){y.spatialReference=e.spatialReference;const i=c.hasM&&!c.hasZ;for(let s=0;s<c.points.length;s++){const a=c.points[s],n=e.points[s];y.x=n[0],y.y=n[1],i&&(a[3]=a[2]),a[2]=t.elevationAt(y.x,y.y)}c.hasZ=!0}const y=new $,ue={remove(){}};class he{constructor(e,t){this.data=e,this.safeWidth=.99999999*(e.width-1),this.dx=(e.width-1)/(t[2]-t[0]),this.dy=(e.width-1)/(t[3]-t[1]),this.x0=t[0],this.y1=t[3]}}class j{constructor(e,t=null){if(this.tile=e,x(t)&&x(e)){const i=e.extent;this._samplerData=new he(t,i)}}get zmin(){return x(this._samplerData)?this._samplerData.data.minValue:0}get zmax(){return x(this._samplerData)?this._samplerData.data.maxValue:0}sample(e,t){if(v(this._samplerData))return;const{safeWidth:i,data:s,dx:a,dy:n,y1:l,x0:o}=this._samplerData,{width:r,values:u,noDataValue:m}=s,p=P(n*(l-t),0,i),h=P(a*(e-o),0,i),C=Math.floor(p),F=Math.floor(h),_=C*r+F,z=_+r,R=u[_],E=u[z],b=u[_+1],S=u[z+1];if(R!==m&&E!==m&&b!==m&&S!==m){const V=h-F,k=R+(b-R)*V;return k+(E+(S-E)*V-k)*(p-C)}}}function P(c,e,t){return c<e?e:c>t?t:c}class ve{async queryAll(e,t,i){if(!(e=i&&i.ignoreInvisibleLayers?e.filter(r=>r.visible):e.slice()).length)throw new d("elevation-query:invalid-layer","Elevation queries require at least one elevation layer to fetch tiles from");const s=T.fromGeometry(t);let a=!1;i&&i.returnSampleInfo||(a=!0);const n={...w,...i,returnSampleInfo:!0},l=await this.query(e[e.length-1],s,n),o=await this._queryAllContinue(e,l,n);return o.geometry=o.geometry.export(),a&&delete o.sampleInfo,o}async query(e,t,i){if(!e)throw new d("elevation-query:invalid-layer","Elevation queries require an elevation layer to fetch tiles from");if(!t||!(t instanceof T)&&t.type!=="point"&&t.type!=="multipoint"&&t.type!=="polyline")throw new d("elevation-query:invalid-geometry","Only point, polyline and multipoint geometries can be used to query elevation");const s={...w,...i},a=new pe(e,t.spatialReference,s),n=s.signal;return await e.load({signal:n}),await this._createGeometryDescriptor(a,t,n),await this._selectTiles(a,n),await this._populateElevationTiles(a,n),this._sampleGeometryWithElevation(a),this._createQueryResult(a,n)}async createSampler(e,t,i){if(!e)throw new d("elevation-query:invalid-layer","Elevation queries require an elevation layer to fetch tiles from");if(!t||t.type!=="extent")throw new d("elevation-query:invalid-extent","Invalid or undefined extent");const s={...w,...i};return this._createSampler(e,t,s)}async createSamplerAll(e,t,i){if(!(e=i&&i.ignoreInvisibleLayers?e.filter(n=>n.visible):e.slice()).length)throw new d("elevation-query:invalid-layer","Elevation queries require at least one elevation layer to fetch tiles from");if(!t||t.type!=="extent")throw new d("elevation-query:invalid-extent","Invalid or undefined extent");const s={...w,...i,returnSampleInfo:!0},a=await this._createSampler(e[e.length-1],t,s);return this._createSamplerAllContinue(e,t,a,s)}async _createSampler(e,t,i,s){const a=i.signal;await e.load({signal:a});const n=t.spatialReference,l=e.tileInfo.spatialReference;n.equals(l)||(await D([{source:n,dest:l}],{signal:a}),t=I(t,l));const o=new me(e,t,i,s);return await this._selectTiles(o,a),await this._populateElevationTiles(o,a),new G(o.elevationTiles,o.layer.tileInfo,o.options.noDataValue)}async _createSamplerAllContinue(e,t,i,s){if(e.pop(),!e.length)return i;const a=i.samplers.map(r=>K(r.extent)),n=await this._createSampler(e[e.length-1],t,s,a);if(n.samplers.length===0)return i;const l=i.samplers.concat(n.samplers),o=new G(l,s.noDataValue);return this._createSamplerAllContinue(e,t,o,s)}async _queryAllContinue(e,t,i){const s=e.pop(),a=t.geometry.coordinates,n=t.sampleInfo;Z(n);const l=[],o=[];for(let p=0;p<a.length;p++){const h=n[p];h.demResolution>=0?h.source||(h.source=s):e.length&&(l.push(a[p]),o.push(p))}if(!e.length||l.length===0)return t;const r=t.geometry.clone(l),u=await this.query(e[e.length-1],r,i),m=u.sampleInfo;if(!m)throw new Error("no sampleInfo");return o.forEach((p,h)=>{a[p].z=u.geometry.coordinates[h].z,n[p].demResolution=m[h].demResolution}),this._queryAllContinue(e,t,i)}async _createQueryResult(e,t){const i=await e.geometry.project(e.outSpatialReference,t);Z(i);const s={geometry:i.export(),noDataValue:e.options.noDataValue};return e.options.returnSampleInfo&&(s.sampleInfo=this._extractSampleInfo(e)),e.geometry.coordinates.forEach(a=>{a.tile=null,a.elevationTile=null}),s}async _createGeometryDescriptor(e,t,i){let s;const a=e.layer.tileInfo.spatialReference;if(t instanceof T?s=await t.project(a,i):(await D([{source:t.spatialReference,dest:a}],{signal:i}),s=I(t,a)),!s)throw new d("elevation-query:spatial-reference-mismatch",`Cannot query elevation in '${t.spatialReference.wkid}' on an elevation service in '${a.wkid}'`);e.geometry=T.fromGeometry(s)}async _selectTiles(e,t){const i=e.options.demResolution;if(e.type==="geometry"&&this._preselectOutsideLayerExtent(e),typeof i=="number")this._selectTilesClosestResolution(e);else if(i==="finest-contiguous")await this._selectTilesFinestContiguous(e,t);else{if(i!=="auto")throw new d("elevation-query:invalid-dem-resolution",`Invalid dem resolution value '${i}', expected a number, "finest-contiguous" or "auto"`);await this._selectTilesAuto(e,t)}}_preselectOutsideLayerExtent(e){if(v(e.layer.fullExtent))return;const t=new j(null);t.sample=()=>e.options.noDataValue,e.outsideExtentTile=t;const i=e.layer.fullExtent;e.geometry.coordinates.forEach(s=>{const a=s.x,n=s.y;(a<i.xmin||a>i.xmax||n<i.ymin||n>i.ymax)&&(s.elevationTile=t)})}_selectTilesClosestResolution(e){const t=e.layer.tileInfo,i=this._findNearestDemResolutionLODIndex(t,e.options.demResolution);e.selectTilesAtLOD(i)}_findNearestDemResolutionLODIndex(e,t){const i=t/g(e.spatialReference);let s=e.lods[0],a=0;for(let n=1;n<e.lods.length;n++){const l=e.lods[n];Math.abs(l.resolution-i)<Math.abs(s.resolution-i)&&(s=l,a=n)}return a}async _selectTilesFinestContiguous(e,t){const i=N(e.layer.tileInfo,e.options.minDemResolution);await this._selectTilesFinestContiguousAt(e,i,t)}async _selectTilesFinestContiguousAt(e,t,i){const s=e.layer;if(e.selectTilesAtLOD(t),t<0)return;const a=s.tilemapCache,n=e.getTilesToFetch();try{if(a)await q(Promise.all(n.map(l=>a.fetchAvailability(l.level,l.row,l.col,{signal:i}))),i);else if(await this._populateElevationTiles(e,i),!e.allElevationTilesFetched())throw e.clearElevationTiles(),new d("elevation-query:has-unavailable-tiles")}catch(l){O(l),await this._selectTilesFinestContiguousAt(e,t-1,i)}}async _populateElevationTiles(e,t){const i=e.getTilesToFetch(),s={},a=e.options.cache,n=e.options.noDataValue,l=i.map(async o=>{if(o.id==null)return;const r=`${e.layer.uid}:${o.id}:${n}`,u=x(a)?a.get(r):null,m=x(u)?u:await e.layer.fetchTile(o.level,o.row,o.col,{noDataValue:n,signal:t});x(a)&&a.put(r,m),s[o.id]=new j(o,m)});await q(se(l),t),e.populateElevationTiles(s)}async _selectTilesAuto(e,t){this._selectTilesAutoFinest(e),this._reduceTilesForMaximumRequests(e);const i=e.layer.tilemapCache;if(!i)return this._selectTilesAutoPrefetchUpsample(e,t);const s=e.getTilesToFetch(),a={},n=s.map(async l=>{const o=new Q(null,0,0,0,W()),r=await X(i.fetchAvailabilityUpsample(l.level,l.row,l.col,o,{signal:t}));r.ok!==!1?l.id!=null&&(a[l.id]=o):O(r.error)});await q(Promise.all(n),t),e.remapTiles(a)}_reduceTilesForMaximumRequests(e){const t=e.layer.tileInfo;let i=0;const s={},a=o=>{o.id!=null&&(o.id in s?s[o.id]++:(s[o.id]=1,i++))},n=o=>{if(o.id==null)return;const r=s[o.id];r===1?(delete s[o.id],i--):s[o.id]=r-1};e.forEachTileToFetch(a,n);let l=!0;for(;l&&(l=!1,e.forEachTileToFetch(o=>{i<=e.options.maximumAutoTileRequests||(n(o),t.upsampleTile(o)&&(l=!0),a(o))},n),l););}_selectTilesAutoFinest(e){const t=N(e.layer.tileInfo,e.options.minDemResolution);e.selectTilesAtLOD(t,e.options.maximumAutoTileRequests)}async _selectTilesAutoPrefetchUpsample(e,t){const i=e.layer.tileInfo;await this._populateElevationTiles(e,t);let s=!1;e.forEachTileToFetch((a,n)=>{i.upsampleTile(a)?s=!0:n()}),s&&await this._selectTilesAutoPrefetchUpsample(e,t)}_sampleGeometryWithElevation(e){e.geometry.coordinates.forEach(t=>{const i=t.elevationTile;let s=e.options.noDataValue;if(i){const a=i.sample(t.x,t.y);x(a)?s=a:t.elevationTile=null}t.z=s})}_extractSampleInfo(e){const t=e.layer.tileInfo,i=g(t.spatialReference);return e.geometry.coordinates.map(s=>{let a=-1;return s.elevationTile&&s.elevationTile!==e.outsideExtentTile&&(a=t.lodAt(s.elevationTile.tile.level).resolution*i),{demResolution:a}})}}class T{export(){return this._exporter(this.coordinates,this.spatialReference)}clone(e){const t=new T;return t.geometry=this.geometry,t.spatialReference=this.spatialReference,t.coordinates=e||this.coordinates.map(i=>i.clone()),t._exporter=this._exporter,t}async project(e,t){if(this.spatialReference.equals(e))return this.clone();await D([{source:this.spatialReference,dest:e}],{signal:t});const i=new A({spatialReference:this.spatialReference,points:this.coordinates.map(l=>[l.x,l.y])}),s=I(i,e);if(!s)return null;const a=this.coordinates.map((l,o)=>{const r=l.clone(),u=s.points[o];return r.x=u[0],r.y=u[1],r}),n=this.clone(a);return n.spatialReference=e,n}static fromGeometry(e){const t=new T;if(t.geometry=e,t.spatialReference=e.spatialReference,e instanceof T)t.coordinates=e.coordinates.map(i=>i.clone()),t._exporter=(i,s)=>{const a=e.clone(i);return a.spatialReference=s,a};else switch(e.type){case"point":{const i=e,{hasZ:s,hasM:a}=i;t.coordinates=s&&a?[new f(i.x,i.y,i.z,i.m)]:s?[new f(i.x,i.y,i.z)]:a?[new f(i.x,i.y,null,i.m)]:[new f(i.x,i.y)],t._exporter=(n,l)=>e.hasM?new $(n[0].x,n[0].y,n[0].z,n[0].m,l):new $(n[0].x,n[0].y,n[0].z,l);break}case"multipoint":{const i=e,{hasZ:s,hasM:a}=i;t.coordinates=s&&a?i.points.map(n=>new f(n[0],n[1],n[2],n[3])):s?i.points.map(n=>new f(n[0],n[1],n[2])):a?i.points.map(n=>new f(n[0],n[1],null,n[2])):i.points.map(n=>new f(n[0],n[1])),t._exporter=(n,l)=>e.hasM?new A({points:n.map(o=>[o.x,o.y,o.z,o.m]),hasZ:!0,hasM:!0,spatiaReference:l}):new A(n.map(o=>[o.x,o.y,o.z]),l);break}case"polyline":{const i=e,s=[],a=[],{hasZ:n,hasM:l}=e;let o=0;for(const r of i.paths)if(a.push([o,o+r.length]),o+=r.length,n&&l)for(const u of r)s.push(new f(u[0],u[1],u[2],u[3]));else if(n)for(const u of r)s.push(new f(u[0],u[1],u[2]));else if(l)for(const u of r)s.push(new f(u[0],u[1],null,u[2]));else for(const u of r)s.push(new f(u[0],u[1]));t.coordinates=s,t._exporter=(r,u)=>{const m=e.hasM?r.map(h=>[h.x,h.y,h.z,h.m]):r.map(h=>[h.x,h.y,h.z]),p=a.map(h=>m.slice(h[0],h[1]));return new ee({paths:p,hasM:e.hasM,hasZ:!0,spatialReference:u})};break}}return t}}class f{constructor(e,t,i=null,s=null,a=null,n=null){this.x=e,this.y=t,this.z=i,this.m=s,this.tile=a,this.elevationTile=n}clone(){return new f(this.x,this.y,this.z,this.m)}}class H{constructor(e,t){this.layer=e,this.options=t}}class pe extends H{constructor(e,t,i){super(e,i),this.outSpatialReference=t,this.type="geometry"}selectTilesAtLOD(e){if(e<0)this.geometry.coordinates.forEach(t=>{t.tile=null});else{const t=this.layer.tileInfo,i=t.lods[e].level;this.geometry.coordinates.forEach(s=>{s.tile=t.tileAt(i,s.x,s.y)})}}allElevationTilesFetched(){return!this.geometry.coordinates.some(e=>!e.elevationTile)}clearElevationTiles(){for(const e of this.geometry.coordinates)e.elevationTile!==this.outsideExtentTile&&(e.elevationTile=null)}populateElevationTiles(e){var t;for(const i of this.geometry.coordinates)!i.elevationTile&&((t=i.tile)!=null&&t.id)&&(i.elevationTile=e[i.tile.id])}remapTiles(e){var t;for(const i of this.geometry.coordinates){const s=(t=i.tile)==null?void 0:t.id;i.tile=s?e[s]:null}}getTilesToFetch(){var i;const e={},t=[];for(const s of this.geometry.coordinates){const a=s.tile;if(!a)continue;const n=(i=s.tile)==null?void 0:i.id;s.elevationTile||!n||e[n]||(e[n]=a,t.push(a))}return t}forEachTileToFetch(e){for(const t of this.geometry.coordinates)t.tile&&!t.elevationTile&&e(t.tile,()=>{t.tile=null})}}class me extends H{constructor(e,t,i,s){super(e,i),this.type="extent",this.elevationTiles=[],this._candidateTiles=[],this._fetchedCandidates=new Set,this.extent=t.intersection(e.fullExtent),this.maskExtents=s}selectTilesAtLOD(e,t){const i=this._maximumLodForRequests(t),s=Math.min(i,e);s<0?this._candidateTiles.length=0:this._selectCandidateTilesCoveringExtentAt(s)}_maximumLodForRequests(e){const t=this.layer.tileInfo;if(!e)return t.lods.length-1;const i=this.extent;if(v(i))return-1;for(let s=t.lods.length-1;s>=0;s--){const a=t.lods[s],n=a.resolution*t.size[0],l=a.resolution*t.size[1];if(Math.ceil(i.width/n)*Math.ceil(i.height/l)<=e)return s}return-1}allElevationTilesFetched(){return this._candidateTiles.length===this.elevationTiles.length}clearElevationTiles(){this.elevationTiles.length=0,this._fetchedCandidates.clear()}populateElevationTiles(e){for(const t of this._candidateTiles){const i=t.id&&e[t.id];i&&(this._fetchedCandidates.add(t),this.elevationTiles.push(i))}}remapTiles(e){this._candidateTiles=this._uniqueNonOverlappingTiles(this._candidateTiles.map(t=>e[t.id]))}getTilesToFetch(){return this._candidateTiles}forEachTileToFetch(e,t){const i=this._candidateTiles;this._candidateTiles=[],i.forEach(s=>{if(this._fetchedCandidates.has(s))return void(t&&t(s));let a=!1;e(s,()=>a=!0),a?t&&t(s):this._candidateTiles.push(s)}),this._candidateTiles=this._uniqueNonOverlappingTiles(this._candidateTiles,t)}_uniqueNonOverlappingTiles(e,t){const i={},s=[];for(const n of e){const l=n.id;l&&!i[l]?(i[l]=n,s.push(n)):t&&t(n)}const a=s.sort((n,l)=>n.level-l.level);return a.filter((n,l)=>{for(let o=0;o<l;o++){const r=a[o].extent;if(r&&n.extent&&L(r,n.extent))return t&&t(n),!1}return!0})}_selectCandidateTilesCoveringExtentAt(e){this._candidateTiles.length=0;const t=this.extent;if(v(t))return;const i=this.layer.tileInfo,s=i.lods[e],a=i.tileAt(s.level,t.xmin,t.ymin),n=a.extent;if(v(n))return;const l=s.resolution*i.size[0],o=s.resolution*i.size[1],r=Math.ceil((t.xmax-n[0])/l),u=Math.ceil((t.ymax-n[1])/o);for(let m=0;m<u;m++)for(let p=0;p<r;p++){const h=new Q(null,a.level,a.row-m,a.col+p);i.updateTileInfo(h),this._tileIsMasked(h)||this._candidateTiles.push(h)}}_tileIsMasked(e){return!!this.maskExtents&&this.maskExtents.some(t=>e.extent&&L(t,e.extent))}}function N(c,e=0){let t=c.lods.length-1;if(e>0){const i=e/g(c.spatialReference),s=c.lods.findIndex(a=>a.resolution<i);s===0?t=0:s>0&&(t=s-1)}return t}const w={maximumAutoTileRequests:20,noDataValue:0,returnSampleInfo:!1,demResolution:"auto",minDemResolution:0};export{ve as ElevationQuery,T as GeometryDescriptor,N as getFinestLodIndex};
