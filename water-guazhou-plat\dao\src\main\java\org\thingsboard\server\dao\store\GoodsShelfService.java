package org.thingsboard.server.dao.store;

import org.thingsboard.server.dao.model.sql.store.GoodsShelf;
import org.thingsboard.server.dao.model.sql.store.Store;
import org.thingsboard.server.dao.util.imodel.query.store.GoodsShelfSaveRequest;
import org.thingsboard.server.dao.util.imodel.query.store.StorePageRequest;
import org.thingsboard.server.dao.util.imodel.response.tree.PagedTreeResponse;

public interface GoodsShelfService {
    /**
     * 分页条件查询货架信息
     *
     * @param request 分页查询条件
     * @return 分页查询结果
     */
    PagedTreeResponse<Store> findTreeConditional(StorePageRequest request);

    /**
     * 保存货架信息
     *
     * @param entity 实体信息
     * @return 保存好的实体
     */
    GoodsShelf save(GoodsShelfSaveRequest entity);

    /**
     * 增量修改
     *
     * @param entity 实体信息
     * @return 是否修改成功
     */
    boolean update(GoodsShelf entity);

    /**
     * 删除
     *
     * @param id 唯一标识
     * @return 是否删除成功
     */
    boolean delete(String id);

    /**
     * 编号是否已存在
     *
     * @param code     编号
     * @param tenantId 客户id
     * @param id       自身id（更新时不为null）
     * @return 是否已存在
     */
    boolean isCodeExists(String code, String id, String tenantId);

    /**
     * 是否允许被删除
     * 1. 货架上有设备时不允许删除
     * @param id 唯一标识
     * @return 是否允许删除
     */
    boolean canBeDelete(String id);

}
