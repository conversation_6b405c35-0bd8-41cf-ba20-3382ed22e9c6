package org.thingsboard.server.dao.util.imodel.query.store;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.store.StoreInRecord;
import org.thingsboard.server.dao.util.imodel.query.ComplexSaveRequest;
import org.thingsboard.server.dao.util.imodel.query.IStarHttpRequest;
import org.thingsboard.server.dao.util.imodel.query.StringSetter;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

import java.util.Date;

@Getter
@Setter
public class StoreInRecordSaveRequest extends ComplexSaveRequest<StoreInRecord, StoreInRecordDetailSaveRequest> {
    // id
    private String id;

    // 入库单标题
    @NotNullOrEmpty
    private String title;

    // 批次号
    private String batchCode;

    // 采购单ID
    private String purchaseId;

    // 目标仓库ID
    @NotNullOrEmpty
    private String storehouseId;

    // 验收人ID
    private String acceptor;

    // 经办人
    @NotNullOrEmpty
    private String manager;

    // 所属合同ID
    private String contractId;

    // 发票编号
    private String invoiceCode;

    // 供应商ID
    @NotNullOrEmpty
    private String supplierId;

    // 入库时间
    @NotNullOrEmpty
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date inTime;

    // 是否补录
    private Boolean addRecord;

    // 备注/说明
    private String remark;

    @Override
    public String valid(IStarHttpRequest request) {
        return checkItemExistence("入库单至少需要入库一个设备");
    }

    public StoreInRecord build() {
        StoreInRecord entity = new StoreInRecord();
        entity.setCreateTime(new Date());
        entity.setCreator(currentUserUUID());
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    public StoreInRecord update(String id) {
        StoreInRecord entity = new StoreInRecord();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(StoreInRecord entity) {
        entity.setTitle(title);
        entity.setBatchCode(batchCode);
        entity.setPurchaseId(purchaseId);
        entity.setStorehouseId(storehouseId);
        entity.setAcceptor(acceptor);
        entity.setManager(manager);
        entity.setContractId(contractId);
        entity.setInvoiceCode(invoiceCode);
        entity.setSupplierId(supplierId);
        entity.setInTime(inTime);
        entity.setAddRecord(addRecord);
        entity.setRemark(remark);
    }

    @Override
    protected StringSetter<StoreInRecordDetailSaveRequest> parentSetter() {
        return StoreInRecordDetailSaveRequest::setMainId;
    }
}