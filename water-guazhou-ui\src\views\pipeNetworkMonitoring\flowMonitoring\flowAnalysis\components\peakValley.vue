<!-- 谷峰分析 -->
<template>
  <div
    v-loading="loading"
    class="view"
  >
    <Search
      ref="cardSearch"
      :config="cardSearchConfig"
    >
    </Search>
    <SLCard
      class="card-table"
      title=" "
    >
      <template #right>
        <el-radio-group
          v-model="state.activeName"
        >
          <el-radio-button label="echarts">
            <Icon
              style="margin-right: 1px;font-size: 16px"
              icon="clarity:line-chart-line"
            />
          </el-radio-button>
          <el-radio-button label="list">
            <Icon
              style="margin-right: 1px;font-size: 16px"
              icon="material-symbols:table"
            />
          </el-radio-button>
        </el-radio-group>
      </template>
      <!-- 列表模式 -->
      <div
        v-if="state.activeName === 'list'"
        style="height: 100%;"
      >
        <FormTable
          ref="refCard"
          :config="cardTableConfig"
        ></FormTable>
      </div>
      <!-- 图表模式 -->
      <div
        v-if="state.activeName === 'echarts'"
        ref="agriEcoDev"
        class="card-ehcarts"
      >
        <VChart
          ref="refChart"
          :theme="useAppStore().isDark?'dark':''"
          class="card-ehcarts"
          :option="state.chartOption"
        ></VChart>
      </div>
    </SLCard>
  </div>
</template>
<script lang="ts" setup>
import elementResizeDetectorMaker from 'element-resize-detector'
import { Search as SearchIcon, Download } from '@element-plus/icons-vue'
import dayjs from 'dayjs'
import { Icon } from '@iconify/vue'
import { useAppStore } from '@/store'
import { lineOption } from '../../data/echart'
import { IECharts } from '@/plugins/echart'
import { getPeak } from '@/api/pipeNetworkMonitoring/flowMonitoring'
import useGlobal from '@/hooks/global/useGlobal'

const { $messageWarning } = useGlobal()
const erd = elementResizeDetectorMaker()
const loading = ref<boolean>(false)
const refCard = ref<ICardTableIns>()
const refChart = ref<IECharts>()
const props = defineProps<{
  stationName?: string,
  stationId?: string
}>()
const cardSearch = ref<ISearchIns>()
const agriEcoDev = ref<any>()

const state = reactive<{
  chartOption: any,
  tableDataList: any,
  activeName: string
}>({
  chartOption: null,
  tableDataList: [],
  activeName: 'echarts'
})

watch(
  () => props.stationId,
  () => {
    console.log(props.stationId)
    refreshData()
  }
)
// 搜索栏初始化配置
const cardSearchConfig = reactive<ISearch>({
  defaultParams: {
    date: [dayjs().add(-7, 'day').format(), dayjs().format()]
  },
  filters: [
    { type: 'daterange', label: '日期', field: 'date' },
    {
      type: 'btn-group',
      btns: [
        { perm: true,
          text: '查询',
          svgIcon: shallowRef(SearchIcon),
          click: () => {
            if (props.stationId) {
              refreshData()
            } else {
              $messageWarning('请选择监测点')
            }
          }
        },
        {
          perm: true,
          text: '导出',
          type: 'warning',
          svgIcon: shallowRef(Download),
          hide: () => { return state.activeName !== 'list' },
          click: () => {
            if (props.stationId) {
              refCard.value?.exportTable()
            } else {
              $messageWarning('请选择监测点')
            }
          }
        }
      ]
    }
  ]
})

// 初始化列表配置数据
const cardTableConfig = reactive<ITable>({
  loading: false,
  dataList: [],
  columns: [
    { prop: 'ts', label: '时间', width: '130px' },
    { prop: 'max', label: '最大流量', unit: '(m³)' },
    { prop: 'maxTs', label: '最大流量读取时间', formatter: (row: any, value: any) => { return value ? dayjs(value).format('HH:00') : '-' } },
    { prop: 'min', label: '最小流量', unit: '(m³)' },
    { prop: 'minTs', label: '最小流量读取时间', formatter: (row: any, value: any) => { return value ? dayjs(value).format('HH:00') : '-' } },
    // { prop: 'inletTotalFlow', label: '分界流量下', unit: '(m³)' },
    { prop: 'avg', label: '平均流量', unit: '(m³)' }
  ],
  operations: [],
  showSummary: false,
  operationWidth: '150px',
  pagination: {
    hide: true
  }
})

// 配置加载图表数据
const refuseChart = () => {
  refChart.value?.clear()
  const chartOption = lineOption()
  chartOption.series = []
  const nc = cardTableConfig.columns.filter(column => column.prop !== 'ts')
  nc.map(n => {
    const newData = state.tableDataList?.map(chart => {
      if (n.prop.indexOf('Ts') !== -1) {
        return dayjs(chart[n.prop]).format('HH:00')
      }
      return chart[n.prop]
    })
    const serie = {
      name: n.label,
      smooth: true,
      data: newData,
      type: 'line',
      markPoint: {
        data: [
          { type: 'max', name: '最大值' },
          { type: 'min', name: '最小值' }
        ]
      },
      markLine: {
        data: [{ type: 'average', name: '平均值' }]
      }
    }
    chartOption.series.push(serie)
    chartOption.name = '流量数据(m³)'
  })
  console.log('state.tableDataList', state.tableDataList)
  chartOption.yAxis[0].name = '时间'
  chartOption.yAxis[1].name = '流量(m³)'
  chartOption.xAxis.data = state.tableDataList?.map(table => table.ts)

  nextTick(() => {
    if (agriEcoDev.value) {
      erd.listenTo(agriEcoDev.value, () => {
        state.chartOption = chartOption
        refChart.value?.resize()
      })
    }
  })
}
const refreshData = async () => {
  loading.value = true
  const query = cardSearch.value?.queryParams || {}
  const [start, end] = query.date
  const params = {
    start: start ? dayjs(start).startOf('day').valueOf() : null,
    end: end ? dayjs(end).endOf('day').valueOf() : null,
    stationId: props.stationId as string
  }
  const res = await getPeak(params)
  const data = res.data?.data
  cardTableConfig.dataList = data
  state.tableDataList = data
  refuseChart()
  loading.value = false
}

onMounted(() => {
  if (props.stationId) {
    refreshData()
  }
})
</script>
<style lang="scss" scoped>
.view{
  height: 100%;
}
.table-box {
    height: calc(100vh - 300px);
  }

.card-ehcarts {
  height: 100%;
  width: 100%;
}

.card-table{
  height: calc(100% - 43px);
  margin-top: 10px;
}
</style>
