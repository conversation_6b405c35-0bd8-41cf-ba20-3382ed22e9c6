package org.thingsboard.server.dao.store;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.store.StoreOutRecordDetail;
import org.thingsboard.server.dao.model.sql.store.StoreOutRecordDetailResponse;
import org.thingsboard.server.dao.util.imodel.query.store.StoreOutRecordDetailPageRequest;
import org.thingsboard.server.dao.util.imodel.query.store.StoreOutRecordDetailSaveRequest;

import java.util.List;

public interface StoreOutRecordDetailService {
    /**
     * 分页条件查询出库单条目
     *
     * @param request 分页查询条件
     * @return 分页查询结果
     */
    IPage<StoreOutRecordDetailResponse> findAllConditional(StoreOutRecordDetailPageRequest request);

    /**
     * 保存所有出库单条目
     *
     * @param items 实体信息
     * @return 保存好的实体
     */
    List<StoreOutRecordDetail> saveAll(List<StoreOutRecordDetailSaveRequest> items);

    /**
     * 增量修改
     *
     * @param entity 实体信息
     * @return 是否修改成功
     */
    boolean update(StoreOutRecordDetail entity);

    /**
     * 删除
     *
     * @param id 唯一标识
     * @return 是否删除成功
     */
    boolean delete(String id);

    /**
     * 批量删除
     *
     * @param idList id列表
     * @return 是否删除成功
     */
    boolean deleteAll(List<String> idList);

    /**
     * 通过出库单id批量删除
     *
     * @param id     出库单id
     * @param idList 实体的id列表，为空列表或null时删除所有
     * @return 是否成功
     */
    boolean removeAllByMainOnIdNotIn(String id, List<String> idList);

    /**
     * 找出出库记录中没有在excludeList中的条目
     *
     * @param mainId      出库记录id
     * @param excludeList 排除在外的条目id集合
     * @return 没有在excludeList中的条目id集合
     */
    List<String> differentIdListTo(String mainId, List<String> excludeList);

    /**
     * 检入（取消出库）所有不在itemIdList中的出库单条目下的设备
     *
     * @param id         出库单id
     * @param itemIdList 需要保留检出信息的条目id集合
     * @return 是否检入有数据
     */
    boolean checkInStorages(String id, List<String> itemIdList);

}
