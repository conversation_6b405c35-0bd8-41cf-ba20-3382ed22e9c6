import request from '@/plugins/axios';
import { formatTree } from '@/utils/GlobalHelper';

// 新增项目
export function addProject(params) {
  return request({
    url: '/api/project',
    method: 'post',
    data: params
  });
}

// 修改项目
export function editProject(params) {
  return request({
    url: '/api/project/edit',
    method: 'post',
    data: params
  });
}

// 删除项目 不带-
export function delProject(id) {
  return request({
    url: `/api/project/${id}`,
    method: 'delete'
  });
}

// 查询当前账号所有根项目
export function getProjectRoot(devices?) {
  let params = false;
  if (devices) {
    params = true;
  }
  return request({
    url: `/api/project/root?devices=${params}`, // 默认false
    method: 'get'
  });
}

// 查询指定项目信息
export function getAppointProject(id) {
  return request({
    url: `/api/project/${id}`,
    method: 'get'
  });
}

// 根据资源类型和资源ID查询关系
export function getProjectRelationByEntityTypeAndEntityId(
  entityType,
  entityId
) {
  return request({
    url: `/api/project/relation/project/${entityType}/${entityId}`,
    method: 'get'
  });
}

// 获取账户下的企业
export function getCurrentTenantList() {
  return request({
    url: '/api/tenant/getCurrentTenantList',
    method: 'get'
  });
}

// 获取账户下的企业
export function delTenant(tenantId) {
  return request({
    url: `/api/tenant/${tenantId}`,
    method: 'delete'
  });
}

// 获取所有企业和，自己拥有的企业 （市场推广
export function getAllTenantList(userId) {
  return request({
    url: `/api/tenant/otherTenantList/${userId}`,
    method: 'get'
  });
}

// 赋予企业给指定用户
export function setTenantToUser(data) {
  return request({
    url: `/api/tenant/setTenantToUser`,
    method: 'post',
    data
  });
}
