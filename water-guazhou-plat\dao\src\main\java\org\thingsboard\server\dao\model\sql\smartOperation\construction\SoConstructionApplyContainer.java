package org.thingsboard.server.dao.model.sql.smartOperation.construction;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartOperation.SoGeneralTaskStatus;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.util.List;

@Getter
@Setter
@ResponseEntity
public class SoConstructionApplyContainer {
    // 所属工程编号
    private String constructionCode;

    // 所属工程名称
    private String constructionName;

    // 所属工程名称
    private SoGeneralTaskStatus status;

    private List<SoConstructionApply> items;

}
