package org.thingsboard.server.dao.model.sql.shuiwu;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;
import java.util.Date;

@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.SHUIWU_VALVE_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class ValveEntity {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.SHUIWU_VALVE_NAME)
    private String name;

    @Column(name = ModelConstants.SHUIWU_VALVE_CODE)
    private String code;

    @Column(name = ModelConstants.SHUIWU_VALVE_ADDRESS)
    private String address;

    @Column(name = ModelConstants.SHUIWU_VALVE_LOCATION)
    private String location;

    @Column(name = ModelConstants.CREATOR)
    private String creator;

    @Column(name = ModelConstants.CREATE_TIME)
    private Date createTime;

    @Column(name = ModelConstants.TENANT_ID_PROPERTY)
    private String tenantId;

}
