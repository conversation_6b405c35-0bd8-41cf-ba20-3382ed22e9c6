package org.thingsboard.server.dao.shuiwu;

import com.alibaba.fastjson.JSON;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.shuiwu.CriterionEntity;
import org.thingsboard.server.dao.shuiwu.DTO.CriterionDetailDTO;
import org.thingsboard.server.dao.sql.shuiwu.CriterionRepository;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Slf4j
@Service
public class CriterionServiceImpl implements CriterionService {

    @Autowired
    private CriterionRepository criterionRepository;

    @Override
    public CriterionEntity findById(String id) {
        CriterionEntity entity = criterionRepository.findOne(id);
        String detail = entity.getDetail();
        List<CriterionDetailDTO> list = JSON.parseArray(detail, CriterionDetailDTO.class);
        entity.setDataList(list);
        return entity;
    }

    @Override
    public PageData<CriterionEntity> findList(int page, int size, String name, String deviceType, TenantId tenantId) {
        // 分页参数
        PageRequest pageable = new PageRequest(page - 1, size, new Sort(Sort.Direction.DESC, "createTime"));

        List<String> deviceTypeList = new ArrayList<>();
        if (StringUtils.isNotBlank(deviceType)) {
            deviceTypeList.addAll(Arrays.asList(deviceType.split(",")));
        }

        Page<CriterionEntity> pageResult = null;
        if (deviceTypeList.size() > 1) {
            pageResult = criterionRepository.findList(name, deviceTypeList, UUIDConverter.fromTimeUUID(tenantId.getId()), pageable);
        } else {
            pageResult = criterionRepository.findList(name, deviceType, UUIDConverter.fromTimeUUID(tenantId.getId()), pageable);
        }
        if (pageResult.getTotalElements() == 0) {// 查全部
            pageResult = criterionRepository.findList(name, "", UUIDConverter.fromTimeUUID(tenantId.getId()), pageable);
        }

        return new PageData<>(pageResult.getTotalElements(), pageResult.getContent());
    }

    @Override
    public void save(CriterionEntity entity) {
        List<CriterionDetailDTO> dataList = entity.getDataList();
        String detail = JSON.toJSONString(dataList);
        entity.setDetail(detail);

        criterionRepository.save(entity);
    }

    @Override
    public void deleteById(String id) {
        CriterionEntity criterion = criterionRepository.findOne(id);
        if (criterion == null) {
            return;
        }
        criterion.setIsDel("1");
        criterionRepository.save(criterion);
    }

    @Override
    public List<CriterionEntity> findAll(String tenantId) {
        return criterionRepository.findByTenantIdAndIsDel(tenantId, "0");
    }

    @Override
    public void deleteById(List<String> ids) {
        for (String id : ids) {
            this.deleteById(id);
        }
    }
}
