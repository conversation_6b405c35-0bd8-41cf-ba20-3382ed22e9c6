<template>
  <div
    class="layout-drawerbox-container"
    :class="[theme || (appStore.isDark ? 'dark' : 'light')]"
    :style="styleVars"
  >
    <SideDrawer
      v-if="leftDrawer && !leftDrawerAbsolute"
      v-model="state.leftDrawerOpen"
      :direction="'ltr'"
      :theme="theme"
      :width="leftDrawerWidth"
      :absolute="leftDrawerAbsolute"
      :bar-position="leftDrawerBarPosition"
      :title="leftDrawerTitle"
      :hide-bar="leftDrawerBarHide"
      :min-width="leftDrawerMinWidth"
      @collapse="(val) => toggleDrawer('ltr', val)"
    >
      <template #title>
        <slot name="left-title"></slot>
      </template>
      <template #default>
        <slot name="left"> </slot>
      </template>
    </SideDrawer>
    <div class="layout-drawerbox-content" :class="[computedContentWidthClass]">
      <slot></slot>
      <div
        v-if="state.bottomDrawerOpen && props.bottomDrawerModal"
        class="sidedrawer-modal"
        @click="toggleDrawer('btt', false)"
      ></div>
      <div
        v-if="state.topDrawerOpen && props.topDrawerModal"
        class="sidedrawer-modal"
        @click="toggleDrawer('ttb', false)"
      ></div>

      <SideDrawer
        v-if="topDrawer"
        v-model="state.topDrawerOpen"
        direction="ttb"
        :theme="theme"
        :width="topDrawerHeight"
        :bar-position="topDrawerBarPosition"
        :title="topDrawerTitle"
        :hide-bar="topDrawerBarHide"
        @collapse="(val) => toggleDrawer('ttb', val)"
      >
        <slot name="top"></slot>
      </SideDrawer>
      <SideDrawer
        v-if="bottomDrawer"
        v-model="state.bottomDrawerOpen"
        direction="btt"
        :theme="theme"
        :width="bottomDrawerHeight"
        :bar-position="bottomDrawerBarPosition"
        :title="bottomDrawerTitle"
        :hide-bar="bottomDrawerBarHide"
        @collapse="(val) => toggleDrawer('btt', val)"
      >
        <slot name="bottom"></slot>
      </SideDrawer>
    </div>
    <div
      v-if="state.leftDrawerOpen && props.leftDrawerModal"
      class="sidedrawer-modal"
      @click="toggleDrawer('ltr', false)"
    ></div>
    <div
      v-if="state.rightDrawerOpen && props.rightDrawerModal"
      class="sidedrawer-modal"
      @click="toggleDrawer('rtl', false)"
    ></div>
    <SideDrawer
      v-if="leftDrawer && leftDrawerAbsolute"
      v-model="state.leftDrawerOpen"
      :direction="'ltr'"
      :theme="theme"
      :width="leftDrawerWidth"
      :absolute="leftDrawerAbsolute"
      :bar-position="leftDrawerBarPosition"
      :title="leftDrawerTitle"
      :hide-bar="leftDrawerBarHide"
      :min-width="leftDrawerMinWidth"
      @collapse="(val) => toggleDrawer('ltr', val)"
    >
      <template #title>
        <slot name="left-title"></slot>
      </template>
      <template #default>
        <slot name="left"> </slot>
      </template>
    </SideDrawer>
    <SideDrawer
      v-if="rightDrawer"
      v-model="state.rightDrawerOpen"
      :direction="'rtl'"
      :theme="theme"
      :width="rightDrawerWidth"
      :absolute="rightDrawerAbsolute"
      :bar-position="rightDrawerBarPosition"
      :title="rightDrawerTitle"
      :hide-bar="rightDrawerBarHide"
      :min-width="rightDrawerMinWidth"
      @collapse="(val) => toggleDrawer('rtl', val)"
    >
      <template #title>
        <slot name="right-title"></slot>
      </template>
      <template #default>
        <slot name="right"></slot>
      </template>
    </SideDrawer>
  </div>
</template>

<script lang="ts" setup>
import { useAppStore } from '@/store';
const appStore = useAppStore();
const props = defineProps<{
  /** 显示左侧抽屉 */
  leftDrawer?: boolean;
  leftDrawerWidth?: number;
  leftDrawerAbsolute?: boolean;
  /** 左侧抽屉的按钮位置 */
  leftDrawerBarPosition?: 'top' | 'center' | 'bottom';
  leftDrawerTitle?: string;
  leftDrawerBarHide?: boolean;
  leftDrawerModal?: boolean;
  leftDrawerMinWidth?: number;
  /** 显示右侧抽屉 */
  rightDrawer?: boolean;
  rightDrawerWidth?: number;
  rightDrawerAbsolute?: boolean;
  /** 右侧抽屉的按钮位置 */
  rightDrawerBarPosition?: 'top' | 'center' | 'bottom';
  rightDrawerTitle?: string;
  rightDrawerBarHide?: boolean;
  rightDrawerModal?: boolean;
  rightDrawerMinWidth?: number;
  /** 显示下侧抽屉 */
  bottomDrawer?: boolean;
  bottomDrawerHeight?: number;
  /** 下侧抽屉的按钮位置 */
  bottomDrawerBarPosition?: 'left' | 'center' | 'right';
  bottomDrawerTitle?: string;
  bottomDrawerBarHide?: boolean;
  bottomDrawerModal?: boolean;
  /** 显示上侧抽屉 */
  topDrawer?: boolean;
  topDrawerHeight?: number;
  /** 上侧抽屉的按钮位置 */
  topDrawerBarPosition?: 'left' | 'center' | 'right';
  topDrawerTitle?: string;
  topDrawerBarHide?: boolean;
  topDrawerModal?: boolean;
  /**
   * 主题设置
   */
  theme?: 'darkblue' | 'dark' | 'light';
  /** 当返回false时则取消操作 */
  beforeCollapse?: (
    direction: 'rtl' | 'ltr' | 'btt' | 'ttb',
    open?: boolean
  ) => any;
}>();
const state = reactive<{
  leftDrawerOpen: boolean;
  rightDrawerOpen: boolean;
  bottomDrawerOpen: boolean;
  topDrawerOpen: boolean;
}>({
  leftDrawerOpen: false,
  rightDrawerOpen: false,
  bottomDrawerOpen: false,
  topDrawerOpen: false
});
const styleVars = computed(() => {
  return {
    '--left-width': (props.leftDrawerWidth || 350) + 'px',
    '--right-width': (props.rightDrawerWidth || 350) + 'px'
  };
});
const computedContentWidthClass = computed(() => {
  const leftOpened =
    props.leftDrawer && !props.leftDrawerAbsolute && state.leftDrawerOpen;
  const rightOpened =
    props.rightDrawer && !props.rightDrawerAbsolute && state.rightDrawerOpen;
  let count = 0;
  const base = 'layout-drawerbox-content-v-';
  let className = '';
  if (leftOpened) {
    count++;
    className = base + 'left';
  }
  if (rightOpened) {
    count++;
    className = base + 'right';
  }
  if (count === 2) {
    className = base + 'both';
  }
  return className;
});
const toggleDrawer = async (
  drawer: 'rtl' | 'ltr' | 'btt' | 'ttb',
  open?: boolean
) => {
  try {
    if (props.beforeCollapse) {
      const flag = await props.beforeCollapse(drawer, open);
      if (flag === false) return;
    }
  } catch (error) {
    //
  }
  switch (drawer) {
    case 'rtl':
      state.rightDrawerOpen =
        open === undefined ? !state.rightDrawerOpen : open;
      break;
    case 'ltr':
      state.leftDrawerOpen = open === undefined ? !state.leftDrawerOpen : open;
      break;
    case 'btt':
      state.bottomDrawerOpen =
        open === undefined ? !state.bottomDrawerOpen : open;
      break;
    case 'ttb':
      state.topDrawerOpen = open === undefined ? !state.topDrawerOpen : open;
      break;
    default:
      break;
  }
};
defineExpose({
  toggleDrawer
});
</script>

<style lang="scss" scoped>
.layout-drawerbox-container {
  height: 100%;
  width: 100%;
  display: flex;
  z-index: 1;
}
.layout-drawerbox-content {
  width: 100%;
  height: 100%;
  transition: width ease 0.5s;
  padding: 0;
  overflow: hidden;
  // background-color: black;
  z-index: 0;
  position: relative;
  &.layout-drawerbox-content-v-left {
    width: calc(100% - var(--left-width, 350));
  }
  &.layout-drawerbox-content-v-right {
    width: calc(100% - var(--right-width, 350));
  }
  &.layout-drawerbox-content-v-both {
    width: calc(100% - var(--left-width, 350) - var(--right-width, 350));
  }
}
.sidedrawer-modal {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
}
</style>
