package org.thingsboard.server.dao.gis;

import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.request.GisProjectListRequest;
import org.thingsboard.server.dao.model.sql.gis.GisProject;

import java.util.Date;
import java.util.List;

public interface GisProjectService {
    void save(GisProject entity);

    PageData<GisProject> findList(GisProjectListRequest request, TenantId tenantId);

    void remove(List<String> ids);
}
