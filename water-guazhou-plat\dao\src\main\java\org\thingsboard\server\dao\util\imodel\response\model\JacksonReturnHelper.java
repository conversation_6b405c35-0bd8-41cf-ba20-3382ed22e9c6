package org.thingsboard.server.dao.util.imodel.response.model;

import com.fasterxml.jackson.core.JsonEncoding;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ObjectWriter;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.databind.type.TypeFactory;
import org.apache.ibatis.session.SqlSession;
import org.springframework.core.ResolvableType;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.util.TypeUtils;
import org.thingsboard.server.common.data.exception.ThingsboardErrorCode;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.dao.util.imodel.Environment;
import org.thingsboard.server.dao.util.imodel.response.serializer.IStarDateSerializer;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.lang.reflect.TypeVariable;
import java.lang.reflect.UndeclaredThrowableException;
import java.util.Date;

public class JacksonReturnHelper extends ReturnHelper {
    private final ObjectMapper objectMapper;

    public JacksonReturnHelper(JdbcTemplate jdbcTemplate, SqlSession sqlSession, Environment environment, IModel model, int version) {
        super(jdbcTemplate, sqlSession, environment, model, version);

        objectMapper = new IStarObjectMapper();
        SimpleModule module = new SimpleModule("IStarSerializationModule");
        module.addSerializer(Date.class, new IStarDateSerializer(Date.class, null));
        objectMapper.registerModule(module);
    }

    @Override
    public Object process(Object o, Object arg) {
        return writeInternal(o, o.getClass());
    }

    protected String writeInternal(Object object, Type type) {
        try (
                ByteArrayOutputStream content = new ByteArrayOutputStream();
        ) {
            JsonGenerator generator = this.objectMapper.getFactory().createGenerator(content, JsonEncoding.valueOf("UTF-8"));
            JavaType javaType = null;
            if (type != null && object != null && TypeUtils.isAssignable(type, object.getClass())) {
                javaType = getJavaType(type, null);
            }
            ObjectWriter objectWriter;
            objectWriter = this.objectMapper.writer();
            if (javaType != null && javaType.isContainerType()) {
                objectWriter = objectWriter.forType(javaType);
            }
            objectWriter.writeValue(generator, object);
            return content.toString();
        } catch (IOException e) {
            throw new UndeclaredThrowableException(new ThingsboardException(e, ThingsboardErrorCode.GENERAL));
        }
    }

    /**
     * Return the Jackson {@link JavaType} for the specified type and context class.
     * <p>The default implementation returns {@code typeFactory.constructType(type, contextClass)},
     * but this can be overridden in subclasses, to allow for custom generic collection handling.
     * For instance:
     * <pre class="code">
     * protected JavaType getJavaType(Type type) {
     *   if (type instanceof Class && List.class.isAssignableFrom((Class)type)) {
     *     return TypeFactory.collectionType(ArrayList.class, MyBean.class);
     *   } else {
     *     return super.getJavaType(type);
     *   }
     * }
     * </pre>
     *
     * @param type         the generic type to return the Jackson JavaType for
     * @param contextClass a context class for the target type, for example a class
     *                     in which the target type appears in a method signature (can be {@code null})
     * @return the Jackson JavaType
     */
    protected JavaType getJavaType(Type type, Class<?> contextClass) {
        TypeFactory typeFactory = this.objectMapper.getTypeFactory();
        if (contextClass != null) {
            ResolvableType resolvedType = ResolvableType.forType(type);
            if (type instanceof TypeVariable) {
                ResolvableType resolvedTypeVariable = resolveVariable(
                        (TypeVariable<?>) type, ResolvableType.forClass(contextClass));
                if (resolvedTypeVariable != ResolvableType.NONE) {
                    return typeFactory.constructType(resolvedTypeVariable.resolve());
                }
            } else if (type instanceof ParameterizedType && resolvedType.hasUnresolvableGenerics()) {
                ParameterizedType parameterizedType = (ParameterizedType) type;
                Class<?>[] generics = new Class<?>[parameterizedType.getActualTypeArguments().length];
                Type[] typeArguments = parameterizedType.getActualTypeArguments();
                for (int i = 0; i < typeArguments.length; i++) {
                    Type typeArgument = typeArguments[i];
                    if (typeArgument instanceof TypeVariable) {
                        ResolvableType resolvedTypeArgument = resolveVariable(
                                (TypeVariable<?>) typeArgument, ResolvableType.forClass(contextClass));
                        if (resolvedTypeArgument != ResolvableType.NONE) {
                            generics[i] = resolvedTypeArgument.resolve();
                        } else {
                            generics[i] = ResolvableType.forType(typeArgument).resolve();
                        }
                    } else {
                        generics[i] = ResolvableType.forType(typeArgument).resolve();
                    }
                }
                return typeFactory.constructType(ResolvableType.
                        forClassWithGenerics(resolvedType.getRawClass(), generics).getType());
            }
        }
        return typeFactory.constructType(type);
    }

    private ResolvableType resolveVariable(TypeVariable<?> typeVariable, ResolvableType contextType) {
        ResolvableType resolvedType;
        if (contextType.hasGenerics()) {
            resolvedType = ResolvableType.forType(typeVariable, contextType);
            if (resolvedType.resolve() != null) {
                return resolvedType;
            }
        }

        ResolvableType superType = contextType.getSuperType();
        if (superType != ResolvableType.NONE) {
            resolvedType = resolveVariable(typeVariable, superType);
            if (resolvedType.resolve() != null) {
                return resolvedType;
            }
        }
        for (ResolvableType ifc : contextType.getInterfaces()) {
            resolvedType = resolveVariable(typeVariable, ifc);
            if (resolvedType.resolve() != null) {
                return resolvedType;
            }
        }
        return ResolvableType.NONE;
    }
}
