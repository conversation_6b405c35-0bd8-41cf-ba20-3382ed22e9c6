package org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.device;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartOperation.device.SoDeviceType;
import org.thingsboard.server.dao.util.imodel.query.GeneralDeviceType;
import org.thingsboard.server.dao.util.imodel.query.IStarHttpRequest;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

@Getter
@Setter
public class SoDeviceTypeSaveRequest extends SaveRequest<SoDeviceType> implements GeneralDeviceType {
    // 类别编码
    @NotNullOrEmpty
    private String serialId;

    // 名称
    @NotNullOrEmpty
    private String name;

    // 父级id
    private String parentId;

    // 排序编号
    private Integer sortNum;

    // 备注
    private String remark;

    @Override
    public String valid(IStarHttpRequest request) {
        if (serialId.length() != 14) {
            return "非法的设备类型序列号";
        }
        return super.valid(request);
    }
    @Override
    protected SoDeviceType build() {
        SoDeviceType entity = new SoDeviceType();
        entity.setCreateTime(createTime());
        entity.setCreator(currentUserUUID());
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    @Override
    protected SoDeviceType update(String id) {
        SoDeviceType entity = new SoDeviceType();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(SoDeviceType entity) {
        entity.setSerialId(serialId);
        entity.setName(name);
        entity.setParentId(parentId);
        entity.setOrderNum(sortNum);
        entity.setRemark(remark);
    }
}