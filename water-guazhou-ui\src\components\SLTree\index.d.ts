interface ITreeSelectFilter {
  key: string;
  label: string;
  options?: NormalOption[];
  style?: string;
  multiple?: boolean;
  search?: boolean;
  handleChange?: (params: any) => any;
  /**
   * @deprecated 设置默认值在SLTreeConfig.queryParams中设置对应属性
   */
  defaultVal?: any;
}

interface ITreeSwitch {
  options: NormalOption[];
  curVal?: any;
  handle?: (val: any) => any;
}
interface IBtnPerm {
  editBtn?: any;
  delBtn?: any;
  addBtn?: any;
  noChild?: any;
}
interface SLTreeConfig {
  /** 标题 */
  title?: string;
  titleRight?: IFormItem[];
  queryParams?: Record<string, any>;
  /** 树数据 */
  data: NormalOption[]; // 这个可以通过继承来更改
  /**
   * 图标
   */
  icon?: string;
  svgIcon?: any;
  iconifyIcon?: string | ((row?: any, ...args: any[]) => string);
  iconStyle?: any;
  /** 开启懒加载  */
  isLazy?: boolean;
  loadFun?: any;
  /** 显示复选框 */
  showCheckbox?: boolean;
  checkedKeys?: string[];
  checkedNodes?: NormalOption[];
  /** 当复选框被点击的时候触发 */
  handleCheckChange?: (
    data?: any,
    isChecked?: boolean,
    hasChildChecked?: boolean
  ) => any;
  /** 点击节点复选框之后触发 */
  handleCheck?: (
    ids: string[],
    data: {
      checkedKeys?: string[];
      checkedNodes?: Omit<NormalOption, 'children'>[];
      halfCheckedKeys?: string[];
      halfCheckedNodes?: Omit<NormalOption, 'children'>[];
    }
  ) => any;
  /** 是否开户搜索 */
  isFilterTree?: boolean;
  filterSize?: 'small' | 'default' | 'large';
  filterIcon?: any;
  filterClassName?: string;
  filterPlaceHolder?: string;
  /** 标签，可多个，用逗号隔开 */
  tags?: {
    /** 绑定字段 */
    field: string;
    /** 标签主题 */
    effect?: 'plain' | 'dark' | 'light';
    /** 标签类型 */
    type?: 'success' | 'info' | 'warning' | 'danger';
    /** 背景色 */
    color?: string | ((...args: any[]) => string);
    hit?: boolean;
    round?: boolean;
  }[];
  /** 加载状态 */
  loading?: boolean;
  /** 开启选择选项 */
  selectFilter?: ITreeSelectFilter;
  extraFilters?: IFormItem[];
  /** 开启开关选项 */
  switch?: ITreeSwitch;
  /**
   * @deprecated
   * 在SLTree组件中不适用，请直接在具体的按钮中配置
   */
  operationText?: string[];
  /** 短按钮宽度 */
  nodeBtnWidth?: number;
  /** 当前选中项，需要手动赋值，可在点击回调事件中处理 */
  currentProject?: any;
  /** 显示全部功能 */
  showAll?: boolean; // 全部项目
  /** 是否在点击节点的时候展开或者收缩节点， 默认值为 true，如果为 false，则只有点箭头图标的时候才会展开或者收缩节点 */
  expandOnClickNode?: boolean;
  /** 点击节点的回调 */
  treeNodeHandleClick?: (params: any, node?: any, e?: any) => void;
  /** 点击展开是回调 */
  nodeExpand?: (params: any, node?: any, e?: any) => void;
  /** 默认展开的id数组 */
  expandNodeId?: Array;
  /** 默认展开全部 */
  defaultExpandAll?: boolean;
  /** 树的字段匹配 */
  defaultProps?: any;
  /**
   * 新增子项按钮，配置此项会同步开启树中的按钮
   */
  add?: IButton;
  /** 编辑按钮，配置此项会同步开启树中的按钮 */
  edit?: IButton;
  /** 删除按钮，配置此项会同步开启树中的按钮 */
  delete?: IButton;
  /** 长条新增按钮 */
  addRoot?: IButton;
  /**
   * @deprecated
   * 在SLTree组件中不适用
   */
  allowCreate?: boolean; // 是否显示功能按钮
  /**
   * @deprecated
   * 在SLTree组件中不适用
   */
  allowNew?: boolean; // 是否可以新增根项
  /**
   * @deprecated
   * 在SLTree组件中不适用
   */
  allowAdd?: boolean;
  /**
   * @deprecated
   * 在SLTree组件中不适用
   */
  allowEdit?: boolean;
  /**
   * @deprecated
   * 在SLTree组件中不适用
   */
  allowDelete?: boolean;
  /**
   * @deprecated
   * 在SLTree组件中不适用,请直接在具体的按钮中使用权限
   */
  btnPerms?: IBtnPerm; // 按钮授权
  /**
   * @deprecated
   * 在SLTree组件中不适用，请直接在具体的按键中绑定事件
   */
  clickAddOrEdit?: any; // 点击增加或者编辑
  /**
   * @deprecated
   * 在SLTree组件中不适用，请参考delete按钮
   */
  projectDelete?: any;
  /**
   * @deprecated
   * 在SLTree组件中不适用
   */
  activeAll?: boolean;
  /**
   * @deprecated
   * 在SLTree组件中不适用
   */
  showAllProject?: () => void;
  accordion?: boolean; // 是否每次只打开一个同级树节点展开 默认是 true
  /**
   * 元素的更多操作项
   */
  nodeOperations?: IButton[];
  /**
   * 自动填充data的方法，在onMounted中调用
   */
  autoFillOptions?: () => void;
}
