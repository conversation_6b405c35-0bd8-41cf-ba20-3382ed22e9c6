package org.thingsboard.server.dao.operatingIncomeInput;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.model.VO.LineChartDataVO;
import org.thingsboard.server.dao.model.sql.OperatingIncomeInput;
import org.thingsboard.server.dao.sql.operatingIncomeInput.OperatingIncomeInputRepository;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

@Slf4j
@Service
public class OperatingIncomeInputServiceImpl implements OperatingIncomeInputService {

    @Autowired
    private OperatingIncomeInputRepository operatingIncomeInputRepository;

    @Override
    public List<OperatingIncomeInput> findList(String stationId, String year, TenantId tenantId) {
        // 查询数据
        List<OperatingIncomeInput> dataList = operatingIncomeInputRepository.findList(stationId, year, UUIDConverter.fromTimeUUID(tenantId.getId()));
        if (dataList == null || dataList.isEmpty()) {
            // 创建数据并保存
            dataList = new ArrayList<>();
            for (int i = 1; i <= 12; i++) {
                OperatingIncomeInput data = new OperatingIncomeInput();
                data.setStationId(stationId);
                data.setTs(year + "-" + String.format("%02d", i));
                data.setTenantId(UUIDConverter.fromTimeUUID(tenantId.getId()));
                dataList.add(data);
            }
            // 保存
            operatingIncomeInputRepository.save(dataList);
        }

        return dataList;
    }

    @Override
    public void update(OperatingIncomeInput entity) {
        // 查询数据
        OperatingIncomeInput data = operatingIncomeInputRepository.findOne(entity.getId());
        if (data == null) {
            return;
        }
        data.setUpdateTime(entity.getUpdateTime());
        data.setUpdateUser(entity.getUpdateUser());
        data.setWaterSales(entity.getWaterSales());
        data.setMoney(entity.getMoney());
        operatingIncomeInputRepository.save(data);
    }

    @Override
    public JSONObject getDetail(String stationId, TenantId tenantId) {
        // 查询本年数据
        Calendar instance = Calendar.getInstance();
        List<OperatingIncomeInput> list = this.findList(stationId, instance.get(Calendar.YEAR) + "", tenantId);
        // 统计年水费
        BigDecimal yearTotalMoney = new BigDecimal("0");
        for (OperatingIncomeInput input : list) {
            if (input.getMoney() != null) {
                yearTotalMoney = yearTotalMoney.add(input.getMoney());
            }
        }
        // 查询全部数据
        List<OperatingIncomeInput> allData = operatingIncomeInputRepository.findByStationIdAndTenantId(stationId, UUIDConverter.fromTimeUUID(tenantId.getId()));
        // 统计全部水费
        BigDecimal totalMoney = new BigDecimal("0");
        for (OperatingIncomeInput input : allData) {
            if (input.getMoney() != null) {
                totalMoney = totalMoney.add(input.getMoney());
            }
        }


        JSONObject result = new JSONObject();
        OperatingIncomeInput month = list.get(instance.get(Calendar.MONTH));

        result.put("monthWaterSales", month.getWaterSales());
        result.put("monthMoney", month.getMoney());
        result.put("yearTotalMoney", yearTotalMoney);
        result.put("totalMoney", totalMoney);

        return result;
    }

    @Override
    public JSONObject getTrend(String stationId, TenantId tenantId) {
        // 查询本年数据
        Calendar instance = Calendar.getInstance();
        List<OperatingIncomeInput> yearDataList = this.findList(stationId, instance.get(Calendar.YEAR) + "", tenantId);
        // 查询去年
        List<OperatingIncomeInput> lastYearDataList = this.findList(stationId, (instance.get(Calendar.YEAR) - 1) + "", tenantId);

        // 售水量同比趋势
        List<JSONObject> waterSalesReport = new ArrayList<>();
        for (int i = 0; i < 12; i++) {
            JSONObject data = new JSONObject();
            OperatingIncomeInput nowYearData = yearDataList.get(i);
            OperatingIncomeInput lastYearData = lastYearDataList.get(i);

            data.put("yearData", nowYearData.getWaterSales());
            data.put("lastYearData", lastYearData.getWaterSales());
            data.put("ts", nowYearData.getTs());
            waterSalesReport.add(data);
        }

        // 水费同比趋势
        List<JSONObject> moneyReport = new ArrayList<>();
        for (int i = 0; i < 12; i++) {
            JSONObject data = new JSONObject();
            OperatingIncomeInput nowYearData = yearDataList.get(i);
            OperatingIncomeInput lastYearData = lastYearDataList.get(i);

            data.put("yearData", nowYearData.getMoney());
            data.put("lastYearData", lastYearData.getMoney());
            data.put("ts", nowYearData.getTs());
            moneyReport.add(data);
        }

        // 水费月报
        List<LineChartDataVO> monthMoneyReport = new ArrayList<>();
        for (OperatingIncomeInput input : yearDataList) {
            LineChartDataVO data = new LineChartDataVO();
            data.setTs(input.getTs());
            data.setValue(input.getMoney());
            monthMoneyReport.add(data);
        }


        JSONObject result = new JSONObject();
        result.put("monthMoneyReport", monthMoneyReport);
        result.put("waterSalesReport", waterSalesReport);
        result.put("moneyReport", moneyReport);

        return result;
    }
}
