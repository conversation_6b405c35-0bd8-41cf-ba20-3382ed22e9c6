import"./index-r0dFAfgr.js";const n={1:"正进负出",2:"负进正出",3:"入口表",4:"出口表"},o={1:"集抄系统",2:"非集抄系统",3:"集非混合"},t=[{label:"集成系统",value:"1"},{label:"非集抄系统",value:"2"},{label:"集非混合",value:"3"}],u=[5,15,30,60],v=[{label:"5",value:5},{label:"15",value:15},{label:"30",value:30},{label:"60",value:60}],b=[{label:"商居混合",value:"1"},{label:"居民区",value:"2"},{label:"商业区",value:"3"}],i=[{label:"户改小区",value:"1"},{label:"商业小区",value:"2"},{label:"安置房",value:"3"}],p=[{label:"计量分区",value:"1"},{label:"DMA分区",value:"2"}];var a=(e=>(e.<PERSON>="1",e.<PERSON><PERSON>hong="2",e.PingGuZhong="3",e.JianXiuLou="4",e.YingYunZhong="5",e))(a||{});const c=[{label:"规则中",value:"1"},{label:"建制中",value:"2"},{label:"评估中",value:"3"},{label:"检修漏",value:"4"},{label:"营运中",value:"5"}],r=[{label:"是",value:!0},{label:"否",value:!1}],y=Array.from({length:24}).map((e,l)=>({label:l+"点",value:l})),O={1:"流量计",2:"压力计",3:"大用户"};export{i as A,t as C,n as D,a as E,r as I,y as T,b as U,O as a,p as b,c,v as d,o as e,u as f};
