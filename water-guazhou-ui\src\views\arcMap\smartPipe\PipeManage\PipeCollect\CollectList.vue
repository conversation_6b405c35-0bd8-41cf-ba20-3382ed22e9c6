<!--
  采集任务列表
  1. 任务列表展示（分在办任务和已办任务）
  2. 任务信息预览
  3. 任务详细查看（包含办理过程）
  4. 任务采集数据导出
  5. 删除任务（仅授权过的用户可删）
  6. 新建任务
  7. 任务派发
 -->
<template>
  <ArcLayout
    ref="refArcLayout"
    :panel-default-visible="true"
    :panel-max-min="true"
    :hide-panel-close="true"
    :panel-dragable="false"
    :panel-default-maxmin="'max'"
    :panel-maxmin-changed="onPanelMaxminChanged"
    :panel-title="'采集任务列表'"
  >
    <template #detail-default>
      <div class="collect-pages">
        <div
          v-show="curPage === 'table'"
          class="page-table"
        >
          <CardSearch
            ref="refSearch"
            :config="SearchConfig"
          ></CardSearch>
          <CardTable
            :config="TableConfig"
            class="card-table"
            :class="maxmin"
          ></CardTable>
          <DialogForm
            ref="refDialog"
            :config="DialogFormConfig"
          ></DialogForm>
        </div>
        <div
          v-if="curPage === 'detail'"
          class="page-detail"
        >
          <div class="detail-header">
            <span>{{ TableConfig.currentRow?.name }}</span>
            <span v-if="TableConfig.currentRow?.code">{{ '(' + TableConfig.currentRow?.code + ')' }}</span>
            <Icon
              style="margin-left: auto; cursor: pointer"
              :icon="'ep:close'"
              @click="toTable"
            ></Icon>
          </div>
          <CollectDetail
            v-if="TableConfig.currentRow"
            :row="TableConfig.currentRow"
            class="detail-table"
            @audit="handleAudit"
            @row-click="refArcLayout?.refPanel?.toggleMaxMin('normal')"
          ></CollectDetail>
        </div>
      </div>

      <DialogForm
        ref="refDispatch"
        :config="DialogFormConfig_dispatch"
      ></DialogForm>
    </template>
    <!-- <SLDrawer
      ref="refDrawer"
      :config="DrawerConfig"
    >
      <template #title>
        <span>{{ TableConfig.currentRow?.name }}</span>
        <span v-if="TableConfig.currentRow?.code">{{ '(' + TableConfig.currentRow?.code + ')' }}</span>
      </template>
      <CollectDetail :row="TableConfig.currentRow"></CollectDetail>
    </SLDrawer> -->
  </ArcLayout>
</template>
<script lang="ts" setup>
import { Icon } from '@iconify/vue'
import CollectDetail from './components/CollectDetail.vue'
import {
  DeletePipeCollect,
  DispatchPipeCollect,
  GetPipeCollectList,
  PostPipeCollect
} from '@/api/mapservice/pipeCollection'
import { CollectStatusOptions, ECollectAuditStatus, ECollectStatus, FormatCollectStatus } from './config'
import { formatDate } from '@/utils/DateFormatter'
import { formatterDateTime } from '@/utils/GlobalHelper'
import { useUserStore } from '@/store'
import { removeSlash } from '@/utils/removeIdSlash'
import { SLConfirm, SLMessage } from '@/utils/Message'

const userStore = useUserStore()
const refArcLayout = ref<IArcLayoutIns>()
const refSearch = ref<ICardSearchIns>()
const SearchConfig = reactive<ISearch>({
  filters: [
    {
      type: 'radio-button',
      label: '状态',
      field: 'status',
      options: [{ label: '全部', value: 'all' }, ...CollectStatusOptions]
    },
    { type: 'input', label: '任务名称', field: 'name' },
    { type: 'input', label: '任务编号', field: 'code' }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        { perm: true, text: '搜索', click: () => refreshData() },
        { perm: true, text: '重置', click: () => resetSearch() },
        { perm: true, text: '新建任务', type: 'success', click: () => createPro() }
      ]
    }
  ],
  defaultParams: {
    status: 'all'
  }
})
const TableConfig = reactive<ITable>({
  columns: [
    { prop: 'name', label: '任务名称' },
    { prop: 'code', label: '任务编号' },
    { prop: 'type', label: '采集类型' },
    { prop: 'creatorName', label: '创建人' },
    {
      prop: 'createTime',
      label: '创建时间',
      formatter(row, value) {
        return formatDate(value, formatterDateTime)
      }
    },
    { prop: 'processUserName', label: '接收人' },
    {
      prop: 'status',
      label: '状态',
      formatter(row, value) {
        return FormatCollectStatus(value) ?? ''
      }
    },
    { prop: 'remark', label: '任务备描述' }
  ],
  dataList: [],
  operationWidth: 180,
  operations: [
    { perm: true, text: '详情', click: row => toDetail(row) },
    {
      perm: row => !row.processUser || !row.status,
      text: '派发',
      click: row => handleDispatch(row)
    },
    {
      // 已接收或审核退回的可以转发给别人做
      perm: row => row.processUser === removeSlash(userStore.user?.id?.id)
        && [ECollectStatus.待接收, ECollectStatus.处理中, ECollectStatus.审核退回].includes(row.status),
      text: '转发',
      click: row => handleDispatch(row)
    },
    {
      perm: row => row.creator === removeSlash(userStore.user?.id?.id),
      text: '删除',
      type: 'danger',
      click: row => handleDelete(row)
    }
  ],
  pagination: {
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page || 1
      TableConfig.pagination.limit = size || 20
      refreshData()
    }
  }
})
const resetSearch = () => {
  refSearch.value?.resetForm()
}
const handleAudit = (status: ECollectAuditStatus) => {
  TableConfig.currentRow.status = status
  curPage.value = 'table'
  refArcLayout.value?.refPanel?.toggleMaxMin('normal')
  refreshData()
}
const handleDelete = async (row?: any) => {
  const ids = row ? [row.id] : TableConfig.selectList?.map(item => item.id) || []
  if (!ids.length) {
    SLMessage.warning('请选择要删除的数据')
    return
  }
  SLConfirm('确定删除？', '提示信息')
    .then(async () => {
      try {
        await DeletePipeCollect(ids)
        SLMessage.success('删除成功')
        refreshData()
        refreshData()
      } catch (error) {
        SLMessage.error('删除失败')
        console.log(error)
      }
    })
    .catch(() => {
      //
    })
}
const refreshData = async () => {
  try {
    const params: any = {
      ...(refSearch.value?.queryParams || {}),
      page: TableConfig.pagination.page ?? 1,
      size: TableConfig.pagination.limit ?? 20
    }
    const res = await GetPipeCollectList({ ...params, status: params.status === 'all' ? undefined : params.status })
    TableConfig.dataList = res.data.data?.data || []
    TableConfig.pagination.total = res.data.data?.total || 0
  } catch (error) {
    console.log(error)
  }
}

const refDialog = ref<IDialogFormIns>()
const DialogFormConfig = reactive<IDialogFormConfig>({
  title: '创建项目',
  dialogWidth: 550,
  group: [
    {
      fields: [
        { label: '任务名称', field: 'name', type: 'input', rules: [{ required: true, message: '请输入任务名称' }] },
        // {
        //   label: '接收人',
        //   field: 'processUser',
        //   type: 'user-select',
        //   departField: 'processUserDepartmentId',
        //   onChange: (val, config, group, row, conf, nodes) => {
        //     row.processUserDepartmentId = nodes[0]?.departId
        //   }
        // },
        {
          label: '采集类型',
          field: 'type',
          type: 'radio',
          options: [
            { label: '点', value: '点' },
            { label: '线', value: '线' }
          ]
        },
        // { label: '审核人', field: 'reviewUser', type: 'user-select',departField: '' },
        // { label: '任务地址', field: 'address', type: 'input' },
        { label: '备注', field: 'remark', type: 'textarea' }
      ]
    }
  ],
  labelPosition: 'right',
  labelWidth: '100px',
  defaultValue: {
    type: '点'
  },
  submit: (params: any) => {
    SLConfirm('确定提交？', '提示信息')
      .then(async () => {
        DialogFormConfig.submitting = true
        try {
          const res = await PostPipeCollect({
            ...params,
            processUser: params.processUser?.join(',')
          })
          // console.log(params)
          if (res.data.code === 200) {
            SLMessage.success('提交成功')
            refDialog.value?.closeDialog()
            refreshData()
          } else {
            SLMessage.error('提交失败')
          }
        } catch (error) {
          console.log(error)
          SLMessage.error('提交失败')
        }
        DialogFormConfig.submitting = false
      })
      .catch(() => {
        //
      })
  }
})
const refDispatch = ref<IDialogFormIns>()
const DialogFormConfig_dispatch = reactive<IDialogFormConfig>({
  dialogWidth: 550,
  title: '采集任务指派',
  group: [
    {
      fields: [
        {
          type: 'user-select',
          label: '指派到',
          field: 'processUser',
          rules: [{ required: true, message: '请选择处理人', trigger: 'change' }],
          departField: 'processUserDepartmentId'
        },
        {
          type: 'user-select',
          label: '审核人',
          rules: [{ trigger: 'change', required: true, message: '请选择审核人' }],
          field: 'reviewUser',
          departField: 'processUserDepartmentId'
        }
      ]
    }
  ],
  labelPosition: 'right',
  labelWidth: '100px',
  defaultValue: {},
  submit: (params: any) => {
    SLConfirm('确定派发此任务？', '提示信息')
      .then(async () => {
        DialogFormConfig_dispatch.submitting = true
        try {
          const res = await DispatchPipeCollect({
            ...params,
            id: TableConfig.currentRow.id,
            processUser: params.processUser?.join(','),
            reviewUser: params.reviewUser?.join(',')
          })
          if (res.data.code === 200) {
            SLMessage.success('派发成功')
            refreshData()
            refDispatch.value?.closeDialog()
          } else {
            SLMessage.error('派发失败')
          }
        } catch (error) {
          console.log(error)
          SLMessage.error('派发失败')
        }
        DialogFormConfig_dispatch.submitting = false
      })
      .catch(() => {
        //
      })
  }
})
const handleDispatch = (row: any) => {
  TableConfig.currentRow = row
  refDispatch.value?.openDialog()
}
const createPro = () => {
  refDialog.value?.openDialog()
}
// const refDrawer = ref<ISLDrawerIns>()

// const DrawerConfig = reactive<IDrawerConfig>({
//   title: '',
//   group: [],
//   appendToBody: false
// })
const curPage = ref<'table' | 'detail'>('table')
const toDetail = (row: any) => {
  TableConfig.currentRow = row
  curPage.value = 'detail'
  // refDrawer.value?.openDrawer()
}
const toTable = () => {
  curPage.value = 'table'
}
const maxmin = ref<string>('normal')
const onPanelMaxminChanged = (type: string) => {
  maxmin.value = type
}
onMounted(() => {
  refreshData()
  refArcLayout.value?.refPanel?.toggleMaxMin('max')
})
</script>
<style lang="scss" scoped>
.collect-pages {
  height: 100%;
  .page-table {
    height: 100%;
  }
  .page-detail {
    height: 100%;
    .detail-header {
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: flex-start;
    }
    .detail-table {
      height: calc(100% - 40px);
    }
  }
}
</style>
