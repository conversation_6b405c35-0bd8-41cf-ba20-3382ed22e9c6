package org.thingsboard.server.controller.base;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.datastax.driver.mapping.annotations.Param;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.VO.BaseResult;
import org.thingsboard.server.common.data.exception.ThingsboardErrorCode;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.utils.DateUtils;
import org.thingsboard.server.dao.dashChart.DashChartVO;
import org.thingsboard.server.dao.model.sql.DashChartEntity;
import org.thingsboard.server.dao.util.RestUtil;
import org.thingsboard.server.dao.util.mapping.JacksonUtil;
import org.thingsboard.server.service.aspect.annotation.SysLog;
import org.thingsboard.server.service.rpc.DeviceRpcService;
import org.thingsboard.server.service.utils.Base64Util;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2020/4/26 10:54
 */
@RestController
@RequestMapping("/api/dashChart")
public class DashChartController extends BaseController {

    @Autowired
    private DeviceRpcService deviceRpcService;


    @GetMapping(value = "/findByTenant")
    public List<DashChartEntity> findDashChartByTenant() throws Exception {
        return dashChartService.findByTenant(UUIDConverter.fromTimeUUID(getTenantId().getId()));
    }


    @GetMapping(value = "/findByOriginatorId")
    public List<DashChartEntity> findDashChartByOriginatorId(@Param("originatorId") String originatorId,
                                                             @RequestParam(required = false, defaultValue = "") String name) throws Exception {
        return dashChartService.findByOriginatorIdAndName(originatorId, name);
    }

    @DeleteMapping(value = "/delete")
    @SysLog(options = DataConstants.OPERATING_TYPE.OPERATING_TYPE_DELETE_DASH_CHART)
    public boolean delete(@Param("id") String id, @Param("token") String token) throws Exception {
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", token);
        String object = RestUtil.DeleteMothod(DataConstants.DASHBOARD_IP + DataConstants.DASHBOARD_LIST + "/" + id, headers);
        return dashChartService.deleteByDashboardId(id);
    }


    @PostMapping(value = "/save")
    @SysLog(options = DataConstants.OPERATING_TYPE.OPERATING_TYPE_OPTIONS_SCADA_SAVE)
    public DashChartEntity save(@RequestBody DashChartVO dashChartVO) {
        if (dashChartVO.getDashChart().getId() != null) {
            return dashChartService.save(dashChartVO.getDashChart());
        }
        DashChartEntity dashChartEntity = dashChartVO.getDashChart();
        //创建面板
        JSONObject user = JSONObject.parseObject(new String(Base64Util.decodeData(dashChartVO.getToken().split("\\.")[1])));
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", dashChartVO.getToken());
        headers.put("Content-Type", "application/json");
        String dashJson = convertChartDpi(dashChartEntity.getChartType());
        String object = RestUtil.PostMothod(DataConstants.DASHBOARD_IP + DataConstants.DASHBOARD_CREATE, headers, dashJson);
        JSONObject jsonObject = JSONObject.parseObject(object);
        String dashboardId = jsonObject.getString("_id");
        //创建组态
        JSONObject sendBody = new JSONObject();
        sendBody.put("name", dashChartEntity.getName());
        sendBody.put("details", dashChartEntity.getDetail());
        sendBody.put("project", dashChartEntity.getProtect() != 0);
        sendBody.put("projectPwd", dashChartEntity.getProtect());
        sendBody.put("createTime", DateUtils.date2Str(System.currentTimeMillis(), DateUtils.DATE_FORMATE_MINUTE));
        sendBody.put("userId", user.getString("_id"));
        sendBody.put("dashboardId", dashboardId);
        String dashBoard = RestUtil.PostMothod(DataConstants.DASHBOARD_IP + DataConstants.DASHBOARD__CREAT_LIST, headers, sendBody.toJSONString());
        JSONObject jsonObject1 = JSONObject.parseObject(dashBoard);
        String dashboardResult = jsonObject1.getString("_id");
        dashChartEntity.setDashBoardId(dashboardResult);
        dashChartEntity.setDashBoardJsonId(dashboardId);
        dashChartEntity.setCreateTime(System.currentTimeMillis());
        dashChartEntity.setDashJson(dashJson);
        DashChartEntity save = dashChartService.save(dashChartEntity);
//        if (save.getChartType().equalsIgnoreCase("local")) {
//            deviceRpcService.sendDataToMqttDevice(save.getOriginatorId(), Collections.singleton(save), DataConstants.DATA_TYPE_DASH_CHART);
//        }
        return dashChartService.save(dashChartEntity);
    }


    @GetMapping(value = "/findByTenantId")
    public List<DashChartEntity> findDashChartByTenant(@Param("token") String token) throws Exception {
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", token);
        String object = RestUtil.GetMothod(DataConstants.DASHBOARD_IP + DataConstants.DASHBOARD_LIST, headers);
        JSONObject jsonObject = JSONObject.parseObject(object);
        List<DashChartEntity> chartEntities = new ArrayList<>();
        JSONArray jsonArray = (JSONArray) jsonObject.get("data");
        jsonArray.forEach(jsonElement -> {
            JSONObject json = (JSONObject) jsonElement;
            try {
                chartEntities.add(DashChartEntity.builder()
                        .name(json.getString("name"))
                        .detail(json.getString("detail"))
                        .protect(json.getBoolean("protect") ? 1 : 0)
                        .createTime(System.currentTimeMillis())
                        .dashBoardId(json.getString("_id"))
                        .protectPwd(json.getString("protectPwd"))
                        .chartType("cloud")
                        .originatorId(UUIDConverter.fromTimeUUID(getTenantId().getId()))
                        .tenantId(UUIDConverter.fromTimeUUID(getTenantId().getId()))
                        .updateTime(System.currentTimeMillis()).build());
            } catch (ThingsboardException e) {
                e.printStackTrace();
            }
        });
        //更新到数据库
        chartEntities.forEach(chartEntity -> dashChartService.save(chartEntity));
        return chartEntities;
    }

    /**
     * 导出组态
     *
     * @param dashChartVO
     * @return
     */
    @SneakyThrows
    @PostMapping(value = "/downloadDashChart")
    @SysLog(options = DataConstants.OPERATING_TYPE.OPERATING_TYPE_DOWNLOAD_DASH_CHART)
    public Object downLoadDashChart(@RequestBody DashChartVO dashChartVO) {
        String token = dashChartVO.getToken();
        DashChartEntity dashChartEntity = dashChartVO.getDashChart();
        dashChartEntity.setName(dashChartEntity.getName() + System.currentTimeMillis());
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", token);
        Map<String, String> params = new HashMap<>();
        params.put("_id", dashChartVO.getDashChart().getDashBoardJsonId());
        String object = RestUtil.GetMothod(DataConstants.DASHBOARD_IP + DataConstants.DASHBOARD_GET_JSON, headers, params);
        JSONObject jsonObject = JSONObject.parseObject(object);
        String json = jsonObject.getString(DataConstants.REQUEST_PARAM_DATA);
        JSONArray jsonArray = JSONArray.parseArray(json);
        JSONObject object1 = (JSONObject) jsonArray.get(0);
        object1.remove("_id");

//        jsonArray.forEach(o->{
//            JSONObject jsonObject1 = JSONObject.parseObject(String.valueOf(o));
//            jsonObject1.remove("-id");
//        });
        dashChartVO.setDashChartJson(object1);
        return dashChartVO;
    }

    /**
     * 导入组态
     *
     * @param dashChartVO
     * @return
     */
    @SneakyThrows
    @PostMapping(value = "/uploadDashChart")
    @SysLog(options = DataConstants.OPERATING_TYPE.OPERATING_TYPE_UPLOAD_DASH_CHART)
    public DashChartEntity uploadDashChart(@RequestBody DashChartVO dashChartVO) {
        try {
//            DashChartEntity dashChartEntity =new ObjectMapper().readValue(dashChartVO.getDashChartJson(), new TypeReference<DashChartEntity>() {
//            });
            DashChartEntity dashChartEntity = dashChartVO.getDashChart();
            String token = dashChartVO.getToken();
            //创建新的组态
            JSONObject user = JSONObject.parseObject(new String(Base64Util.decodeData(dashChartVO.getToken().split("\\.")[1])));
            Map<String, String> headers = new HashMap<>();
            headers.put("Authorization", dashChartVO.getToken());
            headers.put("Content-Type", "application/json");
            String object = RestUtil.PostMothod(DataConstants.DASHBOARD_IP + DataConstants.DASHBOARD_CREATE, headers, JacksonUtil.toString(dashChartVO.getDashChartJson()));
            JSONObject jsonObject = JSONObject.parseObject(object);
            String dashboardId = jsonObject.getString("_id");
            //创建组态
            JSONObject sendBody = new JSONObject();
            sendBody.put("name", dashChartEntity.getName());
            sendBody.put("details", dashChartEntity.getDetail());
            sendBody.put("project", dashChartEntity.getProtect() != 0);
            sendBody.put("projectPwd", dashChartEntity.getProtect());
            sendBody.put("createTime", DateUtils.date2Str(System.currentTimeMillis(), DateUtils.DATE_FORMATE_MINUTE));
            sendBody.put("userId", user.getString("_id"));
            sendBody.put("dashboardId", dashboardId);
            String dashBoard = RestUtil.PostMothod(DataConstants.DASHBOARD_IP + DataConstants.DASHBOARD__CREAT_LIST, headers, sendBody.toJSONString());
            JSONObject jsonObject1 = JSONObject.parseObject(dashBoard);
            String dashboardResult = jsonObject1.getString("_id");
            dashChartEntity.setDashBoardId(dashboardResult);
            dashChartEntity.setDashBoardJsonId(dashboardId);
            dashChartEntity.setDashJson(JacksonUtil.toString(dashChartVO.getDashChartJson()));
            dashChartEntity.setCreateTime(System.currentTimeMillis());
            return dashChartService.save(dashChartEntity);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ThingsboardException("导入失败！", ThingsboardErrorCode.GENERAL);
        }
    }


    /**
     * 组态系统更新后调用-更新组态
     *
     * @param dashChartVO
     * @return
     */
    @SneakyThrows
    @PostMapping(value = "/updateDashChart")
    @SysLog(options = DataConstants.OPERATING_TYPE.OPERATING_TYPE_UPLOAD_DASH_CHART)
    public BaseResult updateDashChart(@RequestBody DashChartVO dashChartVO) {
        try {
            //创建新的组态
            DashChartEntity data = dashChartService.findByDashJsonId(dashChartVO.getDashChartJsonId());
            if (data != null) {
                data.setDashJson(JacksonUtil.toString(dashChartVO.getDashChartJson()));
                data.setUpdateTime(System.currentTimeMillis());
                dashChartService.save(data);
                return BaseResult.builder().isResult(true).msg("更新组态成功").build();
            } else {
                return BaseResult.builder().isResult(false).msg("更新组态失败").build();
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new ThingsboardException("导入失败！", ThingsboardErrorCode.GENERAL);
        }
    }


    private String convertChartDpi(String type) {
        switch (type) {
            case "local": {
                return DataConstants.DASHBOARD_SAVE_DATA.replace("720", "1200").replace("1280", "1920");
            }
            case "app": {
                return DataConstants.DASHBOARD_SAVE_DATA.replace("720", "1334").replace("1280", "750");
            }
            default:
                return DataConstants.DASHBOARD_SAVE_DATA;
        }
    }


}
