# 系统使用说明

## 1、配置项

### 1.1 开启横向菜单

- 在public/config.js中修改*horizontalMenu*选项值为*true*

### 1.2 开启依据项目类型过滤应用

- 在public/config.js中修改*useprojectapp*选项值为*true*

### 1.3 开启着陆导航页

- 在public/config.js中修改*useportal*选项值为*true*

### 1.4 开启着陆页的大卡片

- 在public/config.js中修改*showBigPortalCard*选项值为*true*

## 2、部署

### 2.1 接口修改

- 在public/config.js中修改对应接口路径

### 2.2 部署到非根目录或根目录

- 2.2.1只是修改了config.js中的配置则 **不需要** 重新打包

- 2.2.2部署目录与上一次打包不同时 **需要** 重新打包

- 2.2.3 **修改部署目录**
  两个点：

  1. 修改vue.config.js中的publicPath为需要部署到的子目录，比如要部署到sw目录下，则修改为/sw/, **需要以斜杠(/)结尾**

  2. 同时要修改public文件夹中的 **index.html** 中的config.js配置的引用路径，比如修改为/sw/config.js

## 3. 升级bug修复

- ~~cesium样式引入报错~~ 不引用样式文件似乎也没有影响，不确定，再看看
- ~~Module not found: Error: Package path ./Source/Widgets/widgets.css is not exported from package~~ **已经解决**

解决办法：目前只能是在node_modules中找到cecium的package.json中修改exports属性：

```json
"exports": {
  "./package.json": "./package.json",
  ".": {
      "require": "./index.cjs",
      "import": "./Source/Cesium.js"
  },
  "./Source/Widgets/widgets.css": "./Source/Widgets/widgets.css" // <--- 添加这行
},
```

- 报错'cross-env' 不是内部或外部命令，也不是可运行的程序

  1. npm install -g cross-env

- 打包内存溢出/

  1. 先安装内存提升依赖包: npm install -g increase-memory-limit
  2. 执行yarn mf
  3. 重新打包yarn build
     **注: 每次执行yarn（即安装node_module）后都需要执行再一下2**

- 【其它】

  - 导航栏去除项目选择，具体项目具体页面中选择，存在问题：
    - 1. 大屏中的项目无法确定，需要通过当前用户来确定
    - 2. 驾驶舱跳转到水厂总览具体实现

- 应用配置

  - gis <http://10.0.31.253:8001/jumping/?f=sw>

## 6 新天

- <https://zhsw.sunaas.com/#/SYSdashboard>
- guanjiqiang/guan1013
