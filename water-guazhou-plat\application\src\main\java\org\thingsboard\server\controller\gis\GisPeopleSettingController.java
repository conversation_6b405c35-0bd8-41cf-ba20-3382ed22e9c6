package org.thingsboard.server.controller.gis;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.gis.GisPeopleSettingService;
import org.thingsboard.server.dao.model.sql.gis.GisPeopleSettingEntity;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

@RestController
@RequestMapping("api/gis/setting/people")
public class GisPeopleSettingController extends BaseController {

    @Autowired
    private GisPeopleSettingService gisPeopleSettingService;

    @GetMapping("{id}")
    public IstarResponse get(@PathVariable String id) {
        return IstarResponse.ok(gisPeopleSettingService.get(id));
    }

    @GetMapping("list")
    public IstarResponse list(@RequestParam Integer page, @RequestParam Integer size,
                              @RequestParam(required = false, defaultValue = "") String name) {
        try {
            return IstarResponse.ok(gisPeopleSettingService.findList(page, size, name, getTenantId()));
        } catch (ThingsboardException e) {
            return IstarResponse.error(e.getMessage());
        }
    }

    @PostMapping("save")
    public IstarResponse saveOrUpdate(@RequestBody GisPeopleSettingEntity entity) {
        try {
            if (entity.getId() == null) {
                entity.setTenantId(UUIDConverter.fromTimeUUID(getTenantId().getId()));
            }

            gisPeopleSettingService.save(entity);
            return IstarResponse.ok();
        } catch (ThingsboardException e) {
            return IstarResponse.error("保存失败!");
        }
    }

}
