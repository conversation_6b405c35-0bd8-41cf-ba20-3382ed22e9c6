import{d as n,n as r,q as a,p as c,F as e,ay as p,g as o,cq as _,h as i,cn as l,C as m}from"./index-r0dFAfgr.js";import d from"./Navbar-BL3qNhVr.js";import"./alarmScrollView-DjIanuT8.js";import"./index-nTd2CWfi.js";import"./ChangeTheme.vue_vue_type_script_setup_true_lang-D3tU7NZH.js";const u={class:"app-wrapper"},f={id:"app-main",class:"app-main"},v=n({__name:"LayoutFull",setup(h){return(C,w)=>{const t=p("router-view");return o(),r("div",u,[a(d,{class:"app-navbar"}),c("section",f,[a(t,null,{default:e(({Component:s})=>[a(_,{name:"fade"},{default:e(()=>[(o(),i(l(s)))]),_:2},1024)]),_:1})])])}}}),N=m(v,[["__scopeId","data-v-a7731c03"]]);export{N as default};
