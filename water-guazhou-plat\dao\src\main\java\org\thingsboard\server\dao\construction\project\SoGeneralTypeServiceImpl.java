package org.thingsboard.server.dao.construction.project;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralType;
import org.thingsboard.server.dao.sql.smartOperation.construction.project.SoGeneralTypeMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoGeneralTypePageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoGeneralTypeSaveRequest;

@Service
public class SoGeneralTypeServiceImpl implements SoGeneralTypeService {
    @Autowired
    private SoGeneralTypeMapper mapper;

    @Override
    public IPage<SoGeneralType> findAllConditional(SoGeneralTypePageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    public SoGeneralType save(SoGeneralTypeSaveRequest entity) {
        return QueryUtil.saveOrUpdateOneByRequest(entity, mapper::insert, mapper::updateFully);
    }

    @Override
    public boolean update(SoGeneralType entity) {
        return mapper.update(entity);
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }
}
