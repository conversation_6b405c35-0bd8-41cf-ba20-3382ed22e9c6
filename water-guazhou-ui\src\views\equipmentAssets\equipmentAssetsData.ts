import XLSX from 'xlsx'

// 是否超期
export const overdue = [
  { label: '已超期', value: true },
  { label: '未超期', value: false }
]

// 任务类型
export const taskType = [
  { label: '临时任务', value: '1' },
  { label: '计划任务', value: '2' }
]

// 盘点类型
export const inventoryType = [
  { label: '仓内盘点', value: '1' },
  { label: '仓外盘点', value: '2' }
]

// 任务状态
export const taskStatus = [
  { label: '待执行', value: 'PENDING' },
  { label: '执行中', value: 'RECEIVED' },
  { label: '已完成', value: 'APPROVED' }
]

// 企业规模
export const companySize = [
  { label: '大型', value: '3' },
  { label: '中型', value: '2' },
  { label: '小型', value: '1' },
  { label: '微型', value: '0' }
]

// 企业状态
export const status = [
  { label: '已冻结', value: '3' },
  { label: '停止合作', value: '2' },
  { label: '正在合作', value: '1' },
  { label: '意向供应商', value: '0' }
]

// 企业星级
export const importance = [
  { label: '五星', value: '5' },
  { label: '四星', value: '4' },
  { label: '三星', value: '3' },
  { label: '二星', value: '2' },
  { label: '一星', value: '1' }
]

// 出库类型
export const exportType = [
  { label: '领用', value: '0' },
  { label: '安装', value: '1' },
  { label: '售出', value: '2' }
]

// 出库状态
export const exportStatus = [
  { label: '未出库', value: false },
  { label: '已出库', value: true }
]

// 巡检进度
export const inspectionProgress = [
  { label: '待询价', value: 0, color: '#79bbff' },
  { label: '询价中', value: 1, color: '#95d475' },
  { label: '已完成', value: 2, color: '#eebe77' }
]

// 合同上传对应
export const contractCorrespondence = {
  设备编号: 'serialId',
  货品名称: 'name',
  规格型号: 'model',
  单位: 'unit',
  数量: 'num',
  单价: 'price',
  税率: 'taxRate',
  备注: 'remark'
}

// 供应商上传对应
export const supplierUpload = {
  设备编码: 'serialId',
  设备名称: 'name',
  规格型号: 'model',
  计算单位: 'unit',
  采购数量: 'num',
  '单价(元)': 'price',
  '税率(%)': 'taxRate'
}

// 电话验证
export const phoneVerification = [
  { required: true, message: '请输入联系方式', trigger: 'blur' },
  { min: 11, max: 11, message: '请输入11位手机号码', trigger: 'blur' },
  {
    pattern: /^(13[0-9]|14[579]|15[0-3,5-9]|16[6]|17[0135678]|18[0-9]|19[89])\d{8}$/,
    // pattern: /^1[3456789]\d{9}$/,
    message: '请输入正确的手机号码'
  }
]

/**
 * 异步读取Excel文件的sheet表为json数据
 * 不支持合并单元格
 * @param {File对象} file
 */
export function readExcelToJson(file) {
  return new Promise((resolve, reject) => {
    console.log(reject)

    const reader = new FileReader()

    reader.onload = (e: any) => {
      const data = new Uint8Array(e.target.result)
      const workbook = XLSX.read(data, { type: 'array' })
      //  console.log("workbook: ", workbook);

      // 将Excel 第一个sheet内容转为json格式
      const worksheet = workbook.Sheets[workbook.SheetNames[0]]
      const json = XLSX.utils.sheet_to_json(worksheet)
      //   console.log("jsonExcel:", jsonExcel);
      resolve(json)
    }

    reader.readAsArrayBuffer(file.raw)
  })
}
