/* eslint-disable @typescript-eslint/explicit-module-boundary-types */

import axios from 'axios';
import { getToken } from '@/api/login';
import { getCurTenant } from '@/api/tenant';
// import { ElMessage } from 'element-plus'
// Full config:  https://github.com/axios/axios#request-config
// axios.defaults.baseURL = process.env.baseURL || process.env.apiUrl || '';
// axios.defaults.headers.common['Authorization'] = AUTH_TOKEN;
// axios.defaults.headers.post['Content-Type'] = 'application/x-www-form-urlencoded';

// axios.defaults.headers['Content-Type'] = 'application/x-www-form-urlencoded'

const config = {
  baseURL: 'http://localhost:8081',
  // baseURL: localStorage.getItem('videoURL') ??'https://www.cqdxzhny.com',
  // baseURL:'http://***********:8089',
  timeout: 1000 * 180,
  method: 'post'
};
export const videoAxios = axios.create(config);
videoAxios.interceptors.request.use(
  (config) => {
    config.headers['Content-Type'] = 'application/json';
    return config;
  },
  (error) => {
    console.log(error); // for debug
    // Do something with request error
    return Promise.reject(error);
  }
);
const _axios = axios.create(config);

_axios.interceptors.request.use(
  (config) => {
    // 在请求发送之前做某事
    if (getToken()) {
      // 让每个请求携带token-- ['X-Token']为自定义key 请根据实际情况自行修改
      config.headers['X-Authorization'] = `bearer ${getToken()}`;
    }
    if (getCurTenant()) {
      config.headers.currentTenant = getCurTenant();
    }
    return config;
  },
  (error) => {
    console.log(error); // for debug
    // Do something with request error
    return Promise.reject(error);
  }
);

// Add a response interceptor
_axios.interceptors.response.use(
  (response) => {
    // Do something with response data
    /**
     * 下面的注释为通过在response里，自定义code来标示请求状态
     * 当code返回如下情况则说明权限有问题，登出并返回到登录页
     * 如想通过xmlhttprequest来状态码标识 逻辑可写在下面error中
     * 以下代码均为样例，请结合自生需求加以修改，若不需要，则可删除
     * code为非200是抛错 可结合自己业务进行修改
     */
    const res = response.status;
    if (res !== 200 && res !== 201 && res !== 204) {
      return Promise.reject(new Error(response.data?.message || '错误'));
    }
    return response;
  },
  (error) => {
    const message =
      error.response?.data?.message || error.response?.message || error.message;
    return Promise.reject(new Error(message));
  }
);

export default _axios;
