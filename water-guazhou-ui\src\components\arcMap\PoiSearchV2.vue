<template>
  <div id="tool-search-poi" class="esri-widget">
    <el-select
      v-model="state.queryType"
      placeholder="Select"
      class="poi-selector"
    >
      <el-option label="poi" value="1" />
      <!-- <el-option
        label="视野搜索"
        value="2"
      />
      <el-option
        label="周边搜索"
        value="3"
      />
      <el-option
        label="普通建议词搜索"
        value="4"
      />
      <el-option
        label="公交规划建议词搜索"
        value="5"
      />
      <el-option
        label="公交站搜索"
        value="6"
      /> -->
      <el-option label="地名" value="7" />
      <!-- <el-option
        label="拉框搜索"
        value="10"
      /> -->
    </el-select>
    <!-- <el-select
      v-model="state.value"
      filterable
      remote
      reserve-keyword
      placeholder="请输入地名"
      remote-show-suffix
      :remote-method="remoteMethod"
      :loading="state.loading"
      :class="'poi-search'"
      @change="handleChange"
    >
      <el-option
        v-for="item in state.options"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      />
    </el-select> -->
    <el-autocomplete
      ref="refAutoCom"
      v-model="state.value"
      :fetch-suggestions="remoteMethod"
      popper-class="my-autocomplete"
      placeholder="请输入"
      @select="handleChange"
    >
      <!-- <template #append>
        <Icon
          style="cursor: pointer"
          :icon="state.loading ? 'ep:loading' : 'ep:add-location'"
          @click="handleChange"
        ></Icon>
      </template> -->
      <template #default="{ item }">
        <div class="poi-item">
          <div class="poi-text">
            {{ item.value }}
          </div>
          <div v-if="state.returnType === 1" class="poi-address">
            地址：{{ item.data.address }}
          </div>
          <div v-else class="poi-address">城市：{{ item.data.name }}</div>
        </div>
      </template>
    </el-autocomplete>
  </div>
</template>
<script lang="ts" setup>
// import { Icon } from '@iconify/vue'
import { queryTdt, InverseGeocoding } from '@/api/mapservice/utils';

const refAutoCom = ref();
const emit = defineEmits(['change']);
const state = reactive<{
  value: string;
  loading: boolean;
  options: NormalOption[];
  queryType: string;
  returnType?: number;
  curItem: any;
  specifyAdminCode?: any;
}>({
  value: '',
  loading: false,
  options: [],
  queryType: '7',
  curItem: undefined
});
let timeout: any;
const remoteMethod = (
  query: string,
  cb: (data: Record<string, any>[]) => void
) => {
  clearTimeout(timeout);
  timeout = setTimeout(() => {
    if (query) {
      state.loading = true;
      queryTdt({
        keyWord: query,
        queryType: state.queryType,
        specifyAdminCode: state.specifyAdminCode
      })
        .then((res) => {
          // state.specifyAdminCode = undefined
          const data = res.data || {};
          state.returnType = data.resultType;
          if (data.resultType === 1) {
            state.options =
              data.pois?.map((item) => {
                return {
                  value: item.name,
                  data: item
                };
              }) || [];
          } else if (data.resultType === 2) {
            state.options = data?.statistics?.priorityCitys?.map((item) => {
              return {
                value: data.keyWord,
                data: item
              };
            });
          }

          cb(state.options);
        })
        .finally(() => {
          state.loading = false;
        });
    } else {
      state.options = [];
      cb(state.options);
    }
  }, 300);
};
const handleChange = (item?: any) => {
  if (item) state.curItem = item;
  if (state.loading || !state.curItem) return;
  if (state.returnType === 2) {
    // state.specifyAdminCode = item.data.adminCode
    state.value = item.data.name;
    refAutoCom.value?.focus();
  } else {
    const location = state.curItem.data.lonlat?.split(',');
    emit('change', location);
  }
};
onMounted(() => {
  InverseGeocoding({
    lon: window.SITE_CONFIG.GIS_CONFIG.gisDefaultCenter?.[0]?.toString(),
    lat: window.SITE_CONFIG.GIS_CONFIG.gisDefaultCenter?.[1]?.toString()
  })
    .then((res) => {
      state.specifyAdminCode = res.data?.result?.addressComponent?.city_code;
    })
    .catch((error) => {
      console.log(error);
    });
});
</script>
<style lang="scss" scoped>
#tool-search-poi {
  display: flex;
  align-items: center;
  border-radius: var(--el-border-radius-base);
  :deep(.el-input__wrapper) {
    border-radius: 0;
    &:hover,
    &.is-focus {
      box-shadow: 0 0 0 1px var(--el-input-border-color, var(--el-border-color))
        inset !important;
    }
  }
}
.poi-item {
  padding: 12px 0 8px 0;
  .poi-text {
    font-size: 12px;
    line-height: 20px;
    font-weight: bold;
  }
  .poi-address {
    font-size: 12px;
    line-height: 20px;
  }
}
.custom-toolbar {
  line-height: 32px;
  text-align: center;
  display: flex;

  .tool-icon {
    margin: auto;
  }
}
.poi-selector {
  width: 80px;
  :deep(.el-input__wrapper) {
    border-radius: 4px 0 0 4px;
  }
}
.poi-search {
  :deep(.el-input__wrapper) {
    border-radius: 0;
  }
}
.poi-button {
  margin: 0;
  border-radius: 0 4px 4px 0;
}
</style>
