package org.thingsboard.server.dao.smartService.portal;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.smartService.portal.SsPortalActivity;
import org.thingsboard.server.dao.sql.smartService.portal.SsPortalActivityMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.smartService.portal.SsPortalActiveRequest;
import org.thingsboard.server.dao.util.imodel.query.smartService.portal.SsPortalActivityPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartService.portal.SsPortalActivitySaveRequest;

@Service
public class SsPortalActivityServiceImpl implements SsPortalActivityService {
    @Autowired
    private SsPortalActivityMapper mapper;

    @Override
    public SsPortalActivity findById(String id) {
        return mapper.selectById(id);
    }

    @Override
    public IPage<SsPortalActivity> findAllConditional(SsPortalActivityPageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    public SsPortalActivity save(SsPortalActivitySaveRequest entity) {
        return QueryUtil.saveOrUpdateOneByRequest(entity, mapper::insert, mapper::updateFully);
    }

    @Override
    public boolean update(SsPortalActivity entity) {
        return mapper.update(entity);
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }

    @Override
    public boolean active(SsPortalActiveRequest req) {
        return mapper.active(req);
    }

}
