export const handleZoomToOverBig = (view: __esri.MapView, baseLayers: __esri.Layer[]) => {
  // const init = (view: __esri.MapView) => {
  //   oldBaseMap = view.map.basemap.baseLayers.toArray()
  // }
  // const toggle = (view: __esri.MapView) => {
  //   view.map.basemap.baseLayers =
  // }
  if (!view) return
  let oldScale = 0
  view.watch('scale', v => {
    // 监听缩放，不能缩放了就去除底图，因为底图是切片，会限制缩放级别
    if (v < 71) {
      if (view.map.basemap.baseLayers.length) {
        console.log('移除底图，开启无限缩放')
        // oldBaseLayers = view.map.basemap.baseLayers.clone()
        // 判断是放大的话就不清除底图了
        if (v > oldScale) {
          oldScale = v
          return
        }
        view.map.basemap.baseLayers.removeAll()
      }
    } else {
      if (view.map.basemap.baseLayers.length) {
        oldScale = v
        return
      }
      console.log('添加底图，限制缩放级别')
      view.map.basemap.baseLayers.addMany(baseLayers)
    }
    oldScale = v

    // if (v > 20 || v < 0) {
    //   if (view.map.basemap.baseLayers.length) {
    //     view.map.basemap.baseLayers.removeAll()
    //   }
    // } else {
    //   if (view.map.basemap.baseLayers.length) return
    //   view.map.basemap.baseLayers.addMany(baseLayers)
    // }
  })
}
