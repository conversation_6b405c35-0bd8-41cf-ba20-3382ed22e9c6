"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[2462],{38171:(e,t,r)=>{r.d(t,{Z:()=>y});var s=r(43697),n=r(66577),i=r(51773),l=r(9790),o=r(2368),a=r(96674),u=r(70586),h=r(99001),c=r(5600),d=(r(75215),r(67676),r(52011)),p=r(33955);function m(e){if(!(0,u.pC)(e))return null;const t={};for(const r in e){const s=e[r];s&&(t[r]=s.toJSON())}return 0!==Object.keys(t).length?t:null}let f=class extends((0,o.J)(a.wq)){constructor(...e){super(...e),this.isAggregate=!1,this.layer=null,this.popupTemplate=null,this.sourceLayer=null,Object.defineProperty(this,"uid",{value:(0,h.D)(),configurable:!0})}normalizeCtorArgs(e,t,r,s){return e&&!e.declaredClass?e:{geometry:e,symbol:t,attributes:r,popupTemplate:s}}set aggregateGeometries(e){const t=this._get("aggregateGeometries");JSON.stringify(t)!==JSON.stringify(e)&&this._set("aggregateGeometries",e)}set attributes(e){const t=this._get("attributes");t!==e&&(this._set("attributes",e),this._notifyLayer("attributes",t,e))}set geometry(e){const t=this._get("geometry");t!==e&&(this._set("geometry",e),this._notifyLayer("geometry",t,e))}set symbol(e){const t=this._get("symbol");t!==e&&(this._set("symbol",e),this._notifyLayer("symbol",t,e))}set visible(e){const t=this._get("visible");t!==e&&(this._set("visible",e),this._notifyLayer("visible",t,e))}getEffectivePopupTemplate(e=!1){if(this.popupTemplate)return this.popupTemplate;for(const t of[this.sourceLayer,this.layer])if(t){if("popupTemplate"in t&&t.popupTemplate)return t.popupTemplate;if(e&&"defaultPopupTemplate"in t&&(0,u.pC)(t.defaultPopupTemplate))return t.defaultPopupTemplate}return null}getAttribute(e){return this.attributes?.[e]}setAttribute(e,t){if(this.attributes){const r=this.getAttribute(e);this.attributes[e]=t,this._notifyLayer("attributes",r,t,e)}else this.attributes={[e]:t},this._notifyLayer("attributes",void 0,t,e)}getObjectId(){return this.sourceLayer&&"objectIdField"in this.sourceLayer&&this.sourceLayer.objectIdField?this.getAttribute(this.sourceLayer.objectIdField):null}toJSON(){return{aggregateGeometries:m(this.aggregateGeometries),geometry:(0,u.pC)(this.geometry)?this.geometry.toJSON():null,symbol:(0,u.pC)(this.symbol)?this.symbol.toJSON():null,attributes:{...this.attributes},popupTemplate:this.popupTemplate&&this.popupTemplate.toJSON()}}notifyGeometryChanged(){this._notifyLayer("geometry",this.geometry,this.geometry)}notifyMeshTransformChanged(){(0,u.pC)(this.geometry)&&"mesh"===this.geometry.type&&this._notifyLayer("transform",this.geometry.transform,this.geometry.transform)}_notifyLayer(e,t,r,s){if(!this.layer||!("graphicChanged"in this.layer))return;const n={graphic:this,property:e,oldValue:t,newValue:r};"attributes"===e&&(n.attributeName=s),this.layer.graphicChanged(n)}};(0,s._)([(0,c.Cb)({value:null,json:{read:function(e){if(!e)return null;const t={};for(const r in e){const s=(0,p.im)(e[r]);s&&(t[r]=s)}return 0!==Object.keys(t).length?t:null}}})],f.prototype,"aggregateGeometries",null),(0,s._)([(0,c.Cb)({value:null})],f.prototype,"attributes",null),(0,s._)([(0,c.Cb)({value:null,types:n.qM,json:{read:p.im}})],f.prototype,"geometry",null),(0,s._)([(0,c.Cb)({type:Boolean})],f.prototype,"isAggregate",void 0),(0,s._)([(0,c.Cb)({clonable:"reference"})],f.prototype,"layer",void 0),(0,s._)([(0,c.Cb)({type:i.Z})],f.prototype,"popupTemplate",void 0),(0,s._)([(0,c.Cb)({clonable:"reference"})],f.prototype,"sourceLayer",void 0),(0,s._)([(0,c.Cb)({value:null,types:l.LB})],f.prototype,"symbol",null),(0,s._)([(0,c.Cb)({type:Boolean,value:!0})],f.prototype,"visible",null),f=(0,s._)([(0,d.j)("esri.Graphic")],f),(f||(f={})).generateUID=h.D;const y=f},92835:(e,t,r)=>{r.d(t,{Z:()=>f});var s,n=r(43697),i=r(96674),l=r(70586),o=r(35463),a=r(5600),u=(r(75215),r(67676),r(71715)),h=r(52011),c=r(30556);let d=s=class extends i.wq{static get allTime(){return p}static get empty(){return m}constructor(e){super(e),this.end=null,this.start=null}readEnd(e,t){return null!=t.end?new Date(t.end):null}writeEnd(e,t){t.end=e?e.getTime():null}get isAllTime(){return this.equals(s.allTime)}get isEmpty(){return this.equals(s.empty)}readStart(e,t){return null!=t.start?new Date(t.start):null}writeStart(e,t){t.start=e?e.getTime():null}clone(){return new s({end:this.end,start:this.start})}equals(e){if(!e)return!1;const t=(0,l.pC)(this.start)?this.start.getTime():this.start,r=(0,l.pC)(this.end)?this.end.getTime():this.end,s=(0,l.pC)(e.start)?e.start.getTime():e.start,n=(0,l.pC)(e.end)?e.end.getTime():e.end;return t===s&&r===n}expandTo(e){if(this.isEmpty||this.isAllTime)return this.clone();const t=(0,l.yw)(this.start,(t=>(0,o.JE)(t,e))),r=(0,l.yw)(this.end,(t=>{const r=(0,o.JE)(t,e);return t.getTime()===r.getTime()?r:(0,o.Nm)(r,1,e)}));return new s({start:t,end:r})}intersection(e){if(!e)return this.clone();if(this.isEmpty||e.isEmpty)return s.empty;if(this.isAllTime)return e.clone();if(e.isAllTime)return this.clone();const t=(0,l.R2)(this.start,-1/0,(e=>e.getTime())),r=(0,l.R2)(this.end,1/0,(e=>e.getTime())),n=(0,l.R2)(e.start,-1/0,(e=>e.getTime())),i=(0,l.R2)(e.end,1/0,(e=>e.getTime()));let o,a;if(n>=t&&n<=r?o=n:t>=n&&t<=i&&(o=t),r>=n&&r<=i?a=r:i>=t&&i<=r&&(a=i),null!=o&&null!=a&&!isNaN(o)&&!isNaN(a)){const e=new s;return e.start=o===-1/0?null:new Date(o),e.end=a===1/0?null:new Date(a),e}return s.empty}offset(e,t){if(this.isEmpty||this.isAllTime)return this.clone();const r=new s,{start:n,end:i}=this;return(0,l.pC)(n)&&(r.start=(0,o.Nm)(n,e,t)),(0,l.pC)(i)&&(r.end=(0,o.Nm)(i,e,t)),r}union(e){if(!e||e.isEmpty)return this.clone();if(this.isEmpty)return e.clone();if(this.isAllTime||e.isAllTime)return p.clone();const t=(0,l.pC)(this.start)&&(0,l.pC)(e.start)?new Date(Math.min(this.start.getTime(),e.start.getTime())):null,r=(0,l.pC)(this.end)&&(0,l.pC)(e.end)?new Date(Math.max(this.end.getTime(),e.end.getTime())):null;return new s({start:t,end:r})}};(0,n._)([(0,a.Cb)({type:Date,json:{write:{allowNull:!0}}})],d.prototype,"end",void 0),(0,n._)([(0,u.r)("end")],d.prototype,"readEnd",null),(0,n._)([(0,c.c)("end")],d.prototype,"writeEnd",null),(0,n._)([(0,a.Cb)({readOnly:!0,json:{read:!1}})],d.prototype,"isAllTime",null),(0,n._)([(0,a.Cb)({readOnly:!0,json:{read:!1}})],d.prototype,"isEmpty",null),(0,n._)([(0,a.Cb)({type:Date,json:{write:{allowNull:!0}}})],d.prototype,"start",void 0),(0,n._)([(0,u.r)("start")],d.prototype,"readStart",null),(0,n._)([(0,c.c)("start")],d.prototype,"writeStart",null),d=s=(0,n._)([(0,h.j)("esri.TimeExtent")],d);const p=new d,m=new d({start:void 0,end:void 0}),f=d},84552:(e,t,r)=>{r.d(t,{Z:()=>d});var s=r(43697),n=r(2368),i=r(96674),l=r(35463),o=r(5600),a=(r(75215),r(67676),r(36030)),u=r(52011),h=r(78981);let c=class extends((0,n.J)(i.wq)){constructor(e){super(e),this.unit="milliseconds",this.value=0}toMilliseconds(){return(0,l.rJ)(this.value,this.unit,"milliseconds")}};(0,s._)([(0,a.J)(h.v,{nonNullable:!0})],c.prototype,"unit",void 0),(0,s._)([(0,o.Cb)({type:Number,json:{write:!0},nonNullable:!0})],c.prototype,"value",void 0),c=(0,s._)([(0,u.j)("esri.TimeInterval")],c);const d=c},5732:(e,t,r)=>{r.d(t,{c:()=>s});var s="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{}},46791:(e,t,r)=>{r.d(t,{Z:()=>A});var s,n=r(43697),i=r(3894),l=r(32448),o=r(22974),a=r(70586),u=r(71143);!function(e){e[e.ADD=1]="ADD",e[e.REMOVE=2]="REMOVE",e[e.MOVE=4]="MOVE"}(s||(s={}));var h,c=r(1654),d=r(5600),p=r(75215),m=r(52421),f=r(52011),y=r(58971),g=r(10661);const b=new u.Z(class{constructor(){this.target=null,this.cancellable=!1,this.defaultPrevented=!1,this.item=void 0,this.type=void 0}preventDefault(){this.cancellable&&(this.defaultPrevented=!0)}reset(e){this.defaultPrevented=!1,this.item=e}},void 0,(e=>{e.item=null,e.target=null,e.defaultPrevented=!1,e.cancellable=!1})),_=()=>{};function v(e){return e?e instanceof N?e.toArray():e.length?Array.prototype.slice.apply(e):[]:[]}function w(e){if(e&&e.length)return e[0]}function C(e,t,r,s){const n=Math.min(e.length-r,t.length-s);let i=0;for(;i<n&&e[r+i]===t[s+i];)i++;return i}function x(e,t,r,s){t&&t.forEach(((t,n,i)=>{e.push(t),x(e,r.call(s,t,n,i),r,s)}))}const E=new Set,I=new Set,S=new Set,O=new Map;let T=0,N=h=class extends l.Z.EventedAccessor{static isCollection(e){return null!=e&&e instanceof h}constructor(e){super(e),this._chgListeners=[],this._notifications=null,this._timer=null,this._observable=new g.s,this.length=0,this._items=[],Object.defineProperty(this,"uid",{value:T++})}normalizeCtorArgs(e){return e?Array.isArray(e)||e instanceof h?{items:e}:e:{}}destroy(){this.removeAll()}*[Symbol.iterator](){yield*this.items}get items(){return(0,y.it)(this._observable),this._items}set items(e){this._emitBeforeChanges(s.ADD)||(this._splice(0,this.length,v(e)),this._emitAfterChanges(s.ADD))}hasEventListener(e){return"change"===e?this._chgListeners.length>0:this._emitter.hasEventListener(e)}on(e,t){if("change"===e){const e=this._chgListeners,r={removed:!1,callback:t};return e.push(r),this._notifications&&this._notifications.push({listeners:e.slice(),items:this._items.slice(),changes:[]}),{remove(){this.remove=_,r.removed=!0,e.splice(e.indexOf(r),1)}}}return this._emitter.on(e,t)}once(e,t){const r=this.on(e,t);return{remove(){r.remove()}}}add(e,t){if((0,y.it)(this._observable),this._emitBeforeChanges(s.ADD))return this;const r=this.getNextIndex(t??null);return this._splice(r,0,[e]),this._emitAfterChanges(s.ADD),this}addMany(e,t=this._items.length){if((0,y.it)(this._observable),!e||!e.length)return this;if(this._emitBeforeChanges(s.ADD))return this;const r=this.getNextIndex(t);return this._splice(r,0,v(e)),this._emitAfterChanges(s.ADD),this}at(e){if((0,y.it)(this._observable),(e=Math.trunc(e)||0)<0&&(e+=this.length),!(e<0||e>=this.length))return this._items[e]}removeAll(){if((0,y.it)(this._observable),!this.length||this._emitBeforeChanges(s.REMOVE))return[];const e=this._splice(0,this.length)||[];return this._emitAfterChanges(s.REMOVE),e}clone(){return(0,y.it)(this._observable),this._createNewInstance({items:this._items.map(o.d9)})}concat(...e){(0,y.it)(this._observable);const t=e.map(v);return this._createNewInstance({items:this._items.concat(...t)})}drain(e,t){if((0,y.it)(this._observable),!this.length||this._emitBeforeChanges(s.REMOVE))return;const r=(0,a.j0)(this._splice(0,this.length)),n=r.length;for(let s=0;s<n;s++)e.call(t,r[s],s,r);this._emitAfterChanges(s.REMOVE)}every(e,t){return(0,y.it)(this._observable),this._items.every(e,t)}filter(e,t){let r;return(0,y.it)(this._observable),r=2===arguments.length?this._items.filter(e,t):this._items.filter(e),this._createNewInstance({items:r})}find(e,t){return(0,y.it)(this._observable),this._items.find(e,t)}findIndex(e,t){return(0,y.it)(this._observable),this._items.findIndex(e,t)}flatten(e,t){(0,y.it)(this._observable);const r=[];return x(r,this,e,t),new h(r)}forEach(e,t){return(0,y.it)(this._observable),this._items.forEach(e,t)}getItemAt(e){return(0,y.it)(this._observable),this._items[e]}getNextIndex(e){(0,y.it)(this._observable);const t=this.length;return(e=e??t)<0?e=0:e>t&&(e=t),e}includes(e,t=0){return(0,y.it)(this._observable),this._items.includes(e,t)}indexOf(e,t=0){return(0,y.it)(this._observable),this._items.indexOf(e,t)}join(e=","){return(0,y.it)(this._observable),this._items.join(e)}lastIndexOf(e,t=this.length-1){return(0,y.it)(this._observable),this._items.lastIndexOf(e,t)}map(e,t){(0,y.it)(this._observable);const r=this._items.map(e,t);return new h({items:r})}reorder(e,t=this.length-1){(0,y.it)(this._observable);const r=this.indexOf(e);if(-1!==r){if(t<0?t=0:t>=this.length&&(t=this.length-1),r!==t){if(this._emitBeforeChanges(s.MOVE))return e;this._splice(r,1),this._splice(t,0,[e]),this._emitAfterChanges(s.MOVE)}return e}}pop(){if((0,y.it)(this._observable),!this.length||this._emitBeforeChanges(s.REMOVE))return;const e=w(this._splice(this.length-1,1));return this._emitAfterChanges(s.REMOVE),e}push(...e){return(0,y.it)(this._observable),this._emitBeforeChanges(s.ADD)||(this._splice(this.length,0,e),this._emitAfterChanges(s.ADD)),this.length}reduce(e,t){(0,y.it)(this._observable);const r=this._items;return 2===arguments.length?r.reduce(e,t):r.reduce(e)}reduceRight(e,t){(0,y.it)(this._observable);const r=this._items;return 2===arguments.length?r.reduceRight(e,t):r.reduceRight(e)}remove(e){return(0,y.it)(this._observable),this.removeAt(this.indexOf(e))}removeAt(e){if((0,y.it)(this._observable),e<0||e>=this.length||this._emitBeforeChanges(s.REMOVE))return;const t=w(this._splice(e,1));return this._emitAfterChanges(s.REMOVE),t}removeMany(e){if((0,y.it)(this._observable),!e||!e.length||this._emitBeforeChanges(s.REMOVE))return[];const t=e instanceof h?e.toArray():e,r=this._items,n=[],i=t.length;for(let e=0;e<i;e++){const s=t[e],i=r.indexOf(s);if(i>-1){const s=1+C(t,r,e+1,i+1),l=this._splice(i,s);l&&l.length>0&&n.push.apply(n,l),e+=s-1}}return this._emitAfterChanges(s.REMOVE),n}reverse(){if((0,y.it)(this._observable),this._emitBeforeChanges(s.MOVE))return this;const e=this._splice(0,this.length);return e&&(e.reverse(),this._splice(0,0,e)),this._emitAfterChanges(s.MOVE),this}shift(){if((0,y.it)(this._observable),!this.length||this._emitBeforeChanges(s.REMOVE))return;const e=w(this._splice(0,1));return this._emitAfterChanges(s.REMOVE),e}slice(e=0,t=this.length){return(0,y.it)(this._observable),this._createNewInstance({items:this._items.slice(e,t)})}some(e,t){return(0,y.it)(this._observable),this._items.some(e,t)}sort(e){if((0,y.it)(this._observable),!this.length||this._emitBeforeChanges(s.MOVE))return this;const t=(0,a.j0)(this._splice(0,this.length));return arguments.length?t.sort(e):t.sort(),this._splice(0,0,t),this._emitAfterChanges(s.MOVE),this}splice(e,t,...r){(0,y.it)(this._observable);const n=(t?s.REMOVE:0)|(r.length?s.ADD:0);if(this._emitBeforeChanges(n))return[];const i=this._splice(e,t,r)||[];return this._emitAfterChanges(n),i}toArray(){return(0,y.it)(this._observable),this._items.slice()}toJSON(){return(0,y.it)(this._observable),this.toArray()}toLocaleString(){return(0,y.it)(this._observable),this._items.toLocaleString()}toString(){return(0,y.it)(this._observable),this._items.toString()}unshift(...e){return(0,y.it)(this._observable),!e.length||this._emitBeforeChanges(s.ADD)||(this._splice(0,0,e),this._emitAfterChanges(s.ADD)),this.length}_createNewInstance(e){return new this.constructor(e)}_splice(e,t,r){const s=this._items,n=this.itemType;let i,l;if(!this._notifications&&this.hasEventListener("change")&&(this._notifications=[{listeners:this._chgListeners.slice(),items:this._items.slice(),changes:[]}],this._timer&&this._timer.remove(),this._timer=(0,c.Os)((()=>this._dispatchChange()))),t){if(l=s.splice(e,t),this.hasEventListener("before-remove")){const t=b.acquire();t.target=this,t.cancellable=!0;for(let r=0,n=l.length;r<n;r++)i=l[r],t.reset(i),this.emit("before-remove",t),t.defaultPrevented&&(l.splice(r,1),s.splice(e,0,i),e+=1,r-=1,n-=1);b.release(t)}if(this.length=this._items.length,this.hasEventListener("after-remove")){const e=b.acquire();e.target=this,e.cancellable=!1;const t=l.length;for(let r=0;r<t;r++)e.reset(l[r]),this.emit("after-remove",e);b.release(e)}}if(r&&r.length){if(n){const e=[];for(const t of r){const r=n.ensureType(t);null==r&&null!=t||e.push(r)}r=e}const t=this.hasEventListener("before-add"),i=this.hasEventListener("after-add"),l=e===this.length;if(t||i){const n=b.acquire();n.target=this,n.cancellable=!0;const o=b.acquire();o.target=this,o.cancellable=!1;for(const a of r)t?(n.reset(a),this.emit("before-add",n),n.defaultPrevented||(l?s.push(a):s.splice(e++,0,a),this._set("length",s.length),i&&(o.reset(a),this.emit("after-add",o)))):(l?s.push(a):s.splice(e++,0,a),this._set("length",s.length),o.reset(a),this.emit("after-add",o));b.release(o),b.release(n)}else{if(l)for(const e of r)s.push(e);else s.splice(e,0,...r);this._set("length",s.length)}}return(r&&r.length||l&&l.length)&&this._notifyChangeEvent(r,l),l}_emitBeforeChanges(e){let t=!1;if(this.hasEventListener("before-changes")){const r=b.acquire();r.target=this,r.cancellable=!0,r.type=e,this.emit("before-changes",r),t=r.defaultPrevented,b.release(r)}return t}_emitAfterChanges(e){if(this.hasEventListener("after-changes")){const t=b.acquire();t.target=this,t.cancellable=!1,t.type=e,this.emit("after-changes",t),b.release(t)}this._observable.notify()}_notifyChangeEvent(e,t){this.hasEventListener("change")&&this._notifications&&this._notifications[this._notifications.length-1].changes.push({added:e,removed:t})}_dispatchChange(){if(this._timer&&(this._timer.remove(),this._timer=null),!this._notifications)return;const e=this._notifications;this._notifications=null;for(const t of e){const e=t.changes;E.clear(),I.clear(),S.clear();for(const{added:t,removed:r}of e){if(t)if(0===S.size&&0===I.size)for(const e of t)E.add(e);else for(const e of t)I.has(e)?(S.add(e),I.delete(e)):S.has(e)||E.add(e);if(r)if(0===S.size&&0===E.size)for(const e of r)I.add(e);else for(const e of r)E.has(e)?E.delete(e):(S.delete(e),I.add(e))}const r=i.Z.acquire();E.forEach((e=>{r.push(e)}));const s=i.Z.acquire();I.forEach((e=>{s.push(e)}));const n=this._items,l=t.items,o=i.Z.acquire();if(S.forEach((e=>{l.indexOf(e)!==n.indexOf(e)&&o.push(e)})),t.listeners&&(r.length||s.length||o.length)){const e={target:this,added:r,removed:s,moved:o},n=t.listeners.length;for(let r=0;r<n;r++){const s=t.listeners[r];s.removed||s.callback.call(this,e)}}i.Z.release(r),i.Z.release(s),i.Z.release(o)}E.clear(),I.clear(),S.clear()}};N.ofType=e=>{if(!e)return h;if(O.has(e))return O.get(e);let t=null;if("function"==typeof e)t=e.prototype.declaredClass;else if(e.base)t=e.base.prototype.declaredClass;else for(const r in e.typeMap){const s=e.typeMap[r].prototype.declaredClass;t?t+=` | ${s}`:t=s}let r=class extends h{};return(0,n._)([(0,m.c)({Type:e,ensureType:"function"==typeof e?(0,p.se)(e):(0,p.N7)(e)})],r.prototype,"itemType",void 0),r=(0,n._)([(0,f.j)(`esri.core.Collection<${t}>`)],r),O.set(e,r),r},(0,n._)([(0,d.Cb)()],N.prototype,"length",void 0),(0,n._)([(0,d.Cb)()],N.prototype,"items",null),N=h=(0,n._)([(0,f.j)("esri.core.Collection")],N);const A=N},57435:(e,t,r)=>{r.d(t,{Z:()=>h});var s=r(43697),n=r(46791),i=r(70586),l=(r(80442),r(20102),r(92604),r(26258),r(87538)),o=r(5600),a=(r(75215),r(67676),r(52011));let u=class extends n.Z{constructor(e){super(e),this.getCollections=null}initialize(){this.own((0,l.EH)((()=>this._refresh())))}destroy(){this.getCollections=null}_refresh(){const e=(0,i.pC)(this.getCollections)?this.getCollections():null;if((0,i.Wi)(e))return void this.removeAll();let t=0;for(const r of e)(0,i.pC)(r)&&(t=this._processCollection(t,r));this.splice(t,this.length)}_createNewInstance(e){return new n.Z(e)}_processCollection(e,t){if(!t)return e;const r=this.itemFilterFunction?this.itemFilterFunction:e=>!!e;for(const s of t)if(s){if(r(s)){const t=this.indexOf(s,e);t>=0?t!==e&&this.reorder(s,e):this.add(s,e),++e}if(this.getChildrenFunction){const t=this.getChildrenFunction(s);if(Array.isArray(t))for(const r of t)e=this._processCollection(e,r);else e=this._processCollection(e,t)}}return e}};(0,s._)([(0,o.Cb)()],u.prototype,"getCollections",void 0),(0,s._)([(0,o.Cb)()],u.prototype,"getChildrenFunction",void 0),(0,s._)([(0,o.Cb)()],u.prototype,"itemFilterFunction",void 0),u=(0,s._)([(0,a.j)("esri.core.CollectionFlattener")],u);const h=u},3920:(e,t,r)=>{r.d(t,{p:()=>u,r:()=>h});var s=r(43697),n=r(15923),i=r(61247),l=r(5600),o=r(52011),a=r(72762);const u=e=>{let t=class extends e{destroy(){this.destroyed||(this._get("handles")?.destroy(),this._get("updatingHandles")?.destroy())}get handles(){return this._get("handles")||new i.Z}get updatingHandles(){return this._get("updatingHandles")||new a.t}};return(0,s._)([(0,l.Cb)({readOnly:!0})],t.prototype,"handles",null),(0,s._)([(0,l.Cb)({readOnly:!0})],t.prototype,"updatingHandles",null),t=(0,s._)([(0,o.j)("esri.core.HandleOwner")],t),t};let h=class extends(u(n.Z)){};h=(0,s._)([(0,o.j)("esri.core.HandleOwner")],h)},52421:(e,t,r)=>{function s(e){return(t,r)=>{t[r]=e}}r.d(t,{c:()=>s})},17445:(e,t,r)=>{r.d(t,{N1:()=>d,YP:()=>a,Z_:()=>f,gx:()=>u,nn:()=>y,on:()=>c,tX:()=>g});var s=r(91460),n=r(50758),i=r(70586),l=r(95330),o=r(26258);function a(e,t,r={}){return h(e,t,r,p)}function u(e,t,r={}){return h(e,t,r,m)}function h(e,t,r={},s){let n=null;const l=r.once?(e,r)=>{s(e)&&((0,i.hw)(n),t(e,r))}:(e,r)=>{s(e)&&t(e,r)};if(n=(0,o.aQ)(e,l,r.sync,r.equals),r.initial){const t=e();l(t,t)}return n}function c(e,t,r,l={}){let o=null,u=null,h=null;function c(){o&&u&&(u.remove(),l.onListenerRemove?.(o),o=null,u=null)}function d(e){l.once&&l.once&&(0,i.hw)(h),r(e)}const p=a(e,((e,r)=>{c(),(0,s.vT)(e)&&(o=e,u=(0,s.on)(e,t,d),l.onListenerAdd?.(e))}),{sync:l.sync,initial:!0});return h=(0,n.kB)((()=>{p.remove(),c()})),h}function d(e,t){return function(e,t,r){if((0,l.Hc)(r))return Promise.reject((0,l.zE)());const s=e();if(t?.(s))return Promise.resolve(s);let o=null;function a(){o=(0,i.hw)(o)}return new Promise(((s,i)=>{o=(0,n.AL)([(0,l.fu)(r,(()=>{a(),i((0,l.zE)())})),h(e,(e=>{a(),s(e)}),{sync:!1,once:!0},t??p)])}))}(e,m,t)}function p(e){return!0}function m(e){return!!e}r(87538);const f={sync:!0},y={initial:!0},g={sync:!0,initial:!0}},72762:(e,t,r)=>{r.d(t,{t:()=>c});var s=r(43697),n=r(15923),i=r(61247),l=r(70586),o=r(17445),a=r(1654),u=r(5600),h=r(52011);let c=class extends n.Z{constructor(){super(...arguments),this.updating=!1,this._handleId=0,this._handles=new i.Z,this._scheduleHandleId=0,this._pendingPromises=new Set}destroy(){this.removeAll(),this._handles.destroy()}add(e,t,r={}){return this._installWatch(e,t,r,o.YP)}addWhen(e,t,r={}){return this._installWatch(e,t,r,o.gx)}addOnCollectionChange(e,t,{initial:r=!1,final:s=!1}={}){const n=++this._handleId;return this._handles.add([(0,o.on)(e,"after-changes",this._createSyncUpdatingCallback(),o.Z_),(0,o.on)(e,"change",t,{onListenerAdd:r?e=>t({added:e.toArray(),removed:[]}):void 0,onListenerRemove:s?e=>t({added:[],removed:e.toArray()}):void 0})],n),{remove:()=>this._handles.remove(n)}}addPromise(e){if((0,l.Wi)(e))return e;const t=++this._handleId;this._handles.add({remove:()=>{this._pendingPromises.delete(e)&&(0!==this._pendingPromises.size||this._handles.has(d)||this._set("updating",!1))}},t),this._pendingPromises.add(e),this._set("updating",!0);const r=()=>this._handles.remove(t);return e.then(r,r),e}removeAll(){this._pendingPromises.clear(),this._handles.removeAll(),this._set("updating",!1)}_installWatch(e,t,r={},s){const n=++this._handleId;r.sync||this._installSyncUpdatingWatch(e,n);const i=s(e,t,r);return this._handles.add(i,n),{remove:()=>this._handles.remove(n)}}_installSyncUpdatingWatch(e,t){const r=this._createSyncUpdatingCallback(),s=(0,o.YP)(e,r,{sync:!0,equals:()=>!1});return this._handles.add(s,t),s}_createSyncUpdatingCallback(){return()=>{this._handles.remove(d),++this._scheduleHandleId;const e=this._scheduleHandleId;this._get("updating")||this._set("updating",!0),this._handles.add((0,a.Os)((()=>{e===this._scheduleHandleId&&(this._set("updating",this._pendingPromises.size>0),this._handles.remove(d))})),d)}}};(0,s._)([(0,u.Cb)({readOnly:!0})],c.prototype,"updating",void 0),c=(0,s._)([(0,h.j)("esri.core.support.WatchUpdatingTracking")],c);const d=-42},35463:(e,t,r)=>{r.d(t,{JE:()=>l,Nm:()=>i,rJ:()=>o}),r(80442);const s={milliseconds:1,seconds:1e3,minutes:6e4,hours:36e5,days:864e5,weeks:6048e5,months:26784e5,years:31536e6,decades:31536e7,centuries:31536e8},n={milliseconds:{getter:"getMilliseconds",setter:"setMilliseconds",multiplier:1},seconds:{getter:"getSeconds",setter:"setSeconds",multiplier:1},minutes:{getter:"getMinutes",setter:"setMinutes",multiplier:1},hours:{getter:"getHours",setter:"setHours",multiplier:1},days:{getter:"getDate",setter:"setDate",multiplier:1},weeks:{getter:"getDate",setter:"setDate",multiplier:7},months:{getter:"getMonth",setter:"setMonth",multiplier:1},years:{getter:"getFullYear",setter:"setFullYear",multiplier:1},decades:{getter:"getFullYear",setter:"setFullYear",multiplier:10},centuries:{getter:"getFullYear",setter:"setFullYear",multiplier:100}};function i(e,t,r){const s=new Date(e.getTime());if(t&&r){const e=n[r],{getter:i,setter:l,multiplier:o}=e;if("months"===r){const e=function(e,t){const r=new Date(e,t+1,1);return r.setDate(0),r.getDate()}(s.getFullYear(),s.getMonth()+t);s.getDate()>e&&s.setDate(e)}s[l](s[i]()+t*o)}return s}function l(e,t){switch(t){case"milliseconds":return new Date(e.getTime());case"seconds":return new Date(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours(),e.getMinutes(),e.getSeconds());case"minutes":return new Date(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours(),e.getMinutes());case"hours":return new Date(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours());case"days":return new Date(e.getFullYear(),e.getMonth(),e.getDate());case"weeks":return new Date(e.getFullYear(),e.getMonth(),e.getDate()-e.getDay());case"months":return new Date(e.getFullYear(),e.getMonth(),1);case"years":return new Date(e.getFullYear(),0,1);case"decades":return new Date(e.getFullYear()-e.getFullYear()%10,0,1);case"centuries":return new Date(e.getFullYear()-e.getFullYear()%100,0,1);default:return new Date}}function o(e,t,r){return 0===e?0:e*s[t]/s[r]}},91040:(e,t,r)=>{r.d(t,{yZ:()=>n});var s=r(67900);function n(e,t){const r=t||e.extent,n=e.width,i=(0,s.c9)(r&&r.spatialReference);return r&&n?r.width/n*i*s.hd*96:0}},25906:(e,t,r)=>{r.r(t),r.d(t,{default:()=>xe});var s=r(43697),n=r(68773),i=r(38171),l=r(51773),o=r(3172),a=r(46791),u=r(57435),h=r(35454),c=r(22974),d=r(70586),p=r(16453),m=r(95330),f=r(17445),y=r(17452),g=r(5600),b=r(75215),_=r(71715),v=r(52011),w=r(30556),C=r(76169),x=r(6570),E=r(82971),I=r(91040),S=r(8744),O=r(87085),T=r(71612),N=r(38009),A=r(16859),F=r(34760),M=r(72965),L=r(28294),D=r(52162),R=r(66677),j=r(21506),P=r(15923),U=r(3920);r(67676);const Z={visible:"visibleSublayers"};let q=class extends((0,U.p)(P.Z)){constructor(e){super(e),this.scale=0}set layer(e){this._get("layer")!==e&&(this._set("layer",e),this.handles.remove("layer"),e&&this.handles.add([e.sublayers.on("change",(()=>this.notifyChange("visibleSublayers"))),e.on("wms-sublayer-update",(e=>this.notifyChange(Z[e.propertyName])))],"layer"))}get layers(){return this.visibleSublayers.filter((({name:e})=>e)).map((({name:e})=>e)).join()}get version(){this.commitProperty("layers");const e=this.layer;return e&&e.commitProperty("imageTransparency"),(this._get("version")||0)+1}get visibleSublayers(){const{layer:e,scale:t}=this,r=e?.sublayers,s=[],n=e=>{const{minScale:r,maxScale:i,sublayers:l,visible:o}=e;o&&(0===t||(0===r||t<=r)&&(0===i||t>=i))&&(l?l.forEach(n):s.push(e))};return r?.forEach(n),s}toJSON(){const{layer:e,layers:t}=this,{imageFormat:r,imageTransparency:s,version:n}=e;return{format:r,request:"GetMap",service:"WMS",styles:"",transparent:s?"TRUE":"FALSE",version:n,layers:t}}};(0,s._)([(0,g.Cb)()],q.prototype,"layer",null),(0,s._)([(0,g.Cb)({readOnly:!0})],q.prototype,"layers",null),(0,s._)([(0,g.Cb)({type:Number})],q.prototype,"scale",void 0),(0,s._)([(0,g.Cb)({readOnly:!0})],q.prototype,"version",null),(0,s._)([(0,g.Cb)({readOnly:!0})],q.prototype,"visibleSublayers",null),q=(0,s._)([(0,v.j)("esri.layers.support.ExportWMSImageParameters")],q);var V,B=r(90082),W=r(10699),k=r(90578);let H=0,J=V=class extends((0,W.IG)(p.w)){constructor(e){super(e),this.dimensions=null,this.fullExtents=null,this.legendUrl=null,this.legendEnabled=!0,this.layer=null,this.maxScale=0,this.minScale=0,this.parent=null,this.popupEnabled=!1,this.queryable=!1,this.sublayers=null,this.spatialReferences=null,this.addHandles([(0,f.on)((()=>this.sublayers),"after-add",(({item:e})=>{e.parent=this,e.layer=this.layer}),f.Z_),(0,f.on)((()=>this.sublayers),"after-remove",(({item:e})=>{e.layer=e.parent=null}),f.Z_),(0,f.YP)((()=>this.sublayers),((e,t)=>{if(t)for(const e of t)e.layer=e.parent=null;if(e)for(const t of e)t.parent=this,t.layer=this.layer}),f.Z_)])}get description(){return this._get("description")}set description(e){this._set("description",e)}get fullExtent(){return this._get("fullExtent")}set fullExtent(e){this._set("fullExtent",e)}readExtent(e,t){return(e=t.extent)?x.Z.fromJSON(e):null}get id(){return this._get("id")??H++}set id(e){this._set("id",e)}readLegendUrl(e,t){return t?.legendUrl??t?.legendURL??null}get effectiveScaleRange(){const{minScale:e,maxScale:t}=this;return{minScale:e,maxScale:t}}get name(){return this._get("name")}set name(e){this._set("name",e)}castSublayers(e){return(0,b.se)(a.Z.ofType(V),e)}get title(){return this._get("title")}set title(e){this._set("title",e)}get visible(){return this._get("visible")}set visible(e){this._setAndNotifyLayer("visible",e)}clone(){const e=new V;return this.hasOwnProperty("description")&&(e.description=this.description),this.hasOwnProperty("fullExtent")&&(e.fullExtent=this.fullExtent.clone()),this.hasOwnProperty("fullExtents")&&(e.fullExtents=this.fullExtents?.map((e=>e.clone()))??null),this.hasOwnProperty("legendUrl")&&(e.legendUrl=this.legendUrl),this.hasOwnProperty("legendEnabled")&&(e.legendEnabled=this.legendEnabled),this.hasOwnProperty("layer")&&(e.layer=this.layer),this.hasOwnProperty("name")&&(e.name=this.name),this.hasOwnProperty("parent")&&(e.parent=this.parent),this.hasOwnProperty("queryable")&&(e.queryable=this.queryable),this.hasOwnProperty("sublayers")&&(e.sublayers=this.sublayers&&this.sublayers.map((e=>e.clone()))),this.hasOwnProperty("spatialReferences")&&(e.spatialReferences=this.spatialReferences?.map((e=>e))),this.hasOwnProperty("visible")&&(e.visible=this.visible),this.hasOwnProperty("title")&&(e.title=this.title),e}_setAndNotifyLayer(e,t){const r=this.layer;this._get(e)!==t&&(this._set(e,t),r&&r.emit("wms-sublayer-update",{propertyName:e,id:this.id}))}};(0,s._)([(0,g.Cb)()],J.prototype,"description",null),(0,s._)([(0,g.Cb)({readOnly:!0})],J.prototype,"dimensions",void 0),(0,s._)([(0,g.Cb)({value:null})],J.prototype,"fullExtent",null),(0,s._)([(0,_.r)("fullExtent",["extent"])],J.prototype,"readExtent",null),(0,s._)([(0,g.Cb)()],J.prototype,"fullExtents",void 0),(0,s._)([(0,g.Cb)({type:Number,json:{write:{enabled:!1,overridePolicy:()=>({ignoreOrigin:!0,enabled:!0})}}})],J.prototype,"id",null),(0,s._)([(0,g.Cb)({type:String,json:{origins:{"web-document":{read:{source:["legendUrl","legendURL"]},write:{target:"legendUrl",ignoreOrigin:!0}}},read:{source:"legendURL"},write:{ignoreOrigin:!0}}})],J.prototype,"legendUrl",void 0),(0,s._)([(0,_.r)(["web-document"],"legendUrl")],J.prototype,"readLegendUrl",null),(0,s._)([(0,g.Cb)({value:!0,type:Boolean,json:{read:{source:"showLegend"},write:{target:"showLegend"},origins:{"web-map":{read:!1,write:!1},"web-scene":{read:!1,write:!1}}}})],J.prototype,"legendEnabled",void 0),(0,s._)([(0,g.Cb)()],J.prototype,"layer",void 0),(0,s._)([(0,g.Cb)()],J.prototype,"maxScale",void 0),(0,s._)([(0,g.Cb)()],J.prototype,"minScale",void 0),(0,s._)([(0,g.Cb)({readOnly:!0})],J.prototype,"effectiveScaleRange",null),(0,s._)([(0,g.Cb)({type:String,value:null,json:{read:{source:"name"},write:{ignoreOrigin:!0}}})],J.prototype,"name",null),(0,s._)([(0,g.Cb)()],J.prototype,"parent",void 0),(0,s._)([(0,g.Cb)({type:Boolean,json:{read:{source:"showPopup"},write:{ignoreOrigin:!0,target:"showPopup"}}})],J.prototype,"popupEnabled",void 0),(0,s._)([(0,g.Cb)({type:Boolean,json:{write:{ignoreOrigin:!0}}})],J.prototype,"queryable",void 0),(0,s._)([(0,g.Cb)()],J.prototype,"sublayers",void 0),(0,s._)([(0,k.p)("sublayers")],J.prototype,"castSublayers",null),(0,s._)([(0,g.Cb)({type:[Number],json:{read:{source:"spatialReferences"}}})],J.prototype,"spatialReferences",void 0),(0,s._)([(0,g.Cb)({type:String,value:null,json:{write:{ignoreOrigin:!0}}})],J.prototype,"title",null),(0,s._)([(0,g.Cb)({type:Boolean,value:!0,json:{read:{source:"defaultVisibility"}}})],J.prototype,"visible",null),J=V=(0,s._)([(0,v.j)("esri.layers.support.WMSSublayer")],J);const Y=J;var G=r(20102);const $={84:4326,83:4269,27:4267};function z(e){if(!e)return null;const t={idCounter:-1};"string"==typeof e&&(e=(new DOMParser).parseFromString(e,"text/xml"));const r=e.documentElement;if("ServiceExceptionReport"===r.nodeName){const e=Array.prototype.slice.call(r.childNodes).map((e=>e.textContent)).join("\r\n");throw new G.Z("wmslayer:wms-capabilities-xml-is-not-valid","The server returned errors when the WMS capabilities were requested.",e)}const s=ee("Capability",r),n=ee("Service",r),i=s&&ee("Request",s);if(!s||!n||!i)return null;const l=ee("Layer",s);if(!l)return null;const o="WMS_Capabilities"===r.nodeName||"WMT_MS_Capabilities"===r.nodeName?r.getAttribute("version"):"1.3.0",a=re("Title",n,"")||re("Name",n,""),u=re("AccessConstraints",n,""),h=/^none$/i.test(u)?"":u,c=re("Abstract",n,""),p=parseInt(re("MaxWidth",n,"5000"),10),m=parseInt(re("MaxHeight",n,"5000"),10),f=ie(i,"GetMap"),y=ne(i,"GetMap"),g=oe(l,o,t);if(!g)return null;let b,_=0;const v=Array.prototype.slice.call(s.childNodes),w=g.sublayers??[],C=e=>{null!=e&&w.push(e)};v.forEach((e=>{"Layer"===e.nodeName&&(0===_?b=e:1===_?(g.name&&(g.name="",C(oe(b,o,t))),C(oe(e,o,t))):C(oe(e,o,t)),_++)}));let E=g.sublayers,I=g.extent;const S=g.fullExtents??[];if(E||(E=[]),0===E.length&&E.push(g),!I){const e=new x.Z(E[0].extent);g.extent=e.toJSON(),I=g.extent}const O=g.spatialReferences.length>0?g.spatialReferences:X(g),T=ne(i,"GetFeatureInfo"),N=T?ie(i,"GetFeatureInfo"):null,A=Q(E),F=g.minScale||0,M=g.maxScale||0,L=g.dimensions??[],D=A.reduce(((e,t)=>e.concat(t.dimensions??[])),[]),R=L.concat(D).filter(ue);let j=null;if(R.length){const e=R.map((e=>{const{extent:t}=e;return function(e){return Array.isArray(e)&&e.length>0&&e[0]instanceof Date}(t)?t.map((e=>e.getTime())):t?.map((e=>[e.min.getTime(),e.max.getTime()]))})).flat(2).filter(d.pC);j={startTimeField:null,endTimeField:null,trackIdField:void 0,timeExtent:[Math.min(...e),Math.max(...e)]}}return{copyright:h,description:c,dimensions:L,extent:I,fullExtents:S,featureInfoFormats:N,featureInfoUrl:T,mapUrl:y,maxWidth:p,maxHeight:m,maxScale:M,minScale:F,layers:A,spatialReferences:O,supportedImageFormatTypes:f,timeInfo:j,title:a,version:o}}function X(e){if(e.spatialReferences.length>0)return e.spatialReferences;if(e.sublayers)for(const t of e.sublayers){const e=X(t);if(e.length>0)return e}return[]}function Q(e){let t=[];for(const r of e)t.push(r),r.sublayers?.length&&(t=t.concat(Q(r.sublayers)),delete r.sublayers);return t}function K(e,t,r){return t.getAttribute(e)??r}function ee(e,t){for(let r=0;r<t.childNodes.length;r++){const s=t.childNodes[r];if(ae(s)&&s.nodeName===e)return s}return null}function te(e,t){if(null==t)return[];const r=[];for(let s=0;s<t.childNodes.length;s++){const n=t.childNodes[s];ae(n)&&n.nodeName===e&&r.push(n)}return r}function re(e,t,r){return ee(e,t)?.textContent??r}function se(e,t,r){if(!e)return null;const s=parseFloat(e.getAttribute("minx")),n=parseFloat(e.getAttribute("miny")),i=parseFloat(e.getAttribute("maxx")),l=parseFloat(e.getAttribute("maxy"));let o,a,u,h;r?(o=isNaN(n)?-Number.MAX_VALUE:n,a=isNaN(s)?-Number.MAX_VALUE:s,u=isNaN(l)?Number.MAX_VALUE:l,h=isNaN(i)?Number.MAX_VALUE:i):(o=isNaN(s)?-Number.MAX_VALUE:s,a=isNaN(n)?-Number.MAX_VALUE:n,u=isNaN(i)?Number.MAX_VALUE:i,h=isNaN(l)?Number.MAX_VALUE:l);const c=new E.Z({wkid:t});return new x.Z({xmin:o,ymin:a,xmax:u,ymax:h,spatialReference:c})}function ne(e,t){const r=ee(t,e);if(r){const e=ee("DCPType",r);if(e){const t=ee("HTTP",e);if(t){const e=ee("Get",t);if(e){let t=function(e,t,r,s){const n=ee("OnlineResource",r);return n?K("xlink:href",n,null):null}(0,0,e);if(t)return t.indexOf("&")===t.length-1&&(t=t.substring(0,t.length-1)),function(e,t){const r=[],s=(0,y.mN)(e);for(const e in s.query)s.query.hasOwnProperty(e)&&(t.includes(e.toLowerCase())||r.push(e+"="+s.query[e]));return s.path+(r.length?"?"+r.join("&"):"")}(t,["service","request"])}}}}return null}function ie(e,t){const r=te("Operation",e);if(!r.length)return te("Format",ee(t,e)).map((({textContent:e})=>e)).filter(d.pC);const s=[];for(const e of r)if(e.getAttribute("name")===t){const t=te("Format",e);for(const{textContent:e}of t)null!=e&&s.push(e)}return s}function le(e,t,r){const s=ee(t,e);if(!s)return r;const{textContent:n}=s;if(null==n||""===n)return r;const i=Number(n);return isNaN(i)?r:i}function oe(e,t,r){if(!e)return null;const s={id:r.idCounter++,fullExtents:[],parentLayerId:null,queryable:"1"===e.getAttribute("queryable"),spatialReferences:[],sublayers:null},n=ee("LatLonBoundingBox",e),i=ee("EX_GeographicBoundingBox",e);let l=null;n&&(l=se(n,4326)),i&&(l=new x.Z(0,0,0,0,new E.Z({wkid:4326})),l.xmin=parseFloat(re("westBoundLongitude",i,"0")),l.ymin=parseFloat(re("southBoundLatitude",i,"0")),l.xmax=parseFloat(re("eastBoundLongitude",i,"0")),l.ymax=parseFloat(re("northBoundLatitude",i,"0"))),n||i||(l=new x.Z(-180,-90,180,90,new E.Z({wkid:4326}))),s.minScale=le(e,"MaxScaleDenominator",0),s.maxScale=le(e,"MinScaleDenominator",0);const o=["1.0.0","1.1.0","1.1.1"].includes(t)?"SRS":"CRS";return Array.prototype.slice.call(e.childNodes).forEach((e=>{if("Name"===e.nodeName)s.name=e.textContent||"";else if("Title"===e.nodeName)s.title=e.textContent||"";else if("Abstract"===e.nodeName)s.description=e.textContent||"";else if("BoundingBox"===e.nodeName){const r=e.getAttribute(o);if(r&&0===r.indexOf("EPSG:")){const s=parseInt(r.substring(5),10);0===s||isNaN(s)||l||(l="1.3.0"===t?se(e,s,(0,D.A)(s)):se(e,s))}const n=r&&r.indexOf(":");if(n&&n>-1){let i=parseInt(r.substring(n+1,r.length),10);0===i||isNaN(i)||(i=$[i]?$[i]:i);const l="1.3.0"===t?se(e,i,(0,D.A)(i)):se(e,i);l&&s.fullExtents&&s.fullExtents.push(l)}}else if(e.nodeName===o)(e.textContent?.split(" ")??[]).forEach((e=>{const t=e.includes(":")?parseInt(e.split(":")[1],10):parseInt(e,10);if(0!==t&&!isNaN(t)){const e=$[t]?$[t]:t;s.spatialReferences.includes(e)||s.spatialReferences.push(e)}}));else if("Style"!==e.nodeName||s.legendURL){if("Layer"===e.nodeName){const n=oe(e,t,r);n&&(n.parentLayerId=s.id,s.sublayers||(s.sublayers=[]),s.sublayers.push(n))}}else{const t=ee("LegendURL",e);if(t){const e=ee("OnlineResource",t);e&&(s.legendURL=e.getAttribute("xlink:href"))}}})),s.extent=l?.toJSON(),s.dimensions=te("Dimension",e).filter((e=>e.getAttribute("name")&&e.getAttribute("units")&&e.textContent)).map((e=>{const t=e.getAttribute("name"),r=e.getAttribute("units"),s=e.textContent,n=e.getAttribute("unitSymbol")??void 0,i=e.getAttribute("default")??void 0,l="0"!==K("default",e,"0"),o="0"!==K("nearestValue",e,"0"),a="0"!==K("current",e,"0");return ue({name:t,units:r})?{name:"time",units:"ISO8601",extent:de(s),default:de(i),multipleValues:l,nearestValue:o,current:a}:function(e){return/^elevation$/i.test(e.name)&&/^(epsg|crs):\d+$/i.test(e.units)}({name:t,units:r})?{name:"elevation",units:r,extent:he(s),unitSymbol:n,default:he(i),multipleValues:l,nearestValue:o}:{name:t,units:r,extent:ce(s),unitSymbol:n,default:ce(i),multipleValues:l,nearestValue:o}})),s}function ae(e){return e.nodeType===Node.ELEMENT_NODE}function ue(e){return/^time$/i.test(e.name)&&/^iso8601$/i.test(e.units)}function he(e){if(!e)return;const t=e.includes("/"),r=e.split(",");return t?r.map((e=>{const t=e.split("/");return t.length<2?null:{min:parseFloat(t[0]),max:parseFloat(t[1]),resolution:t.length>=3&&"0"!==t[2]?parseFloat(t[2]):void 0}})).filter(d.pC):r.map((e=>parseFloat(e)))}function ce(e){if(!e)return;const t=e.includes("/"),r=e.split(",");return t?r.map((e=>{const t=e.split("/");return t.length<2?null:{min:t[0],max:t[1],resolution:t.length>=3&&"0"!==t[2]?t[2]:void 0}})).filter(d.pC):r}function de(e){if(!e)return;const t=e.includes("/"),r=e.split(",");return t?r.map((e=>{const t=e.split("/");return t.length<2?null:{min:new Date(t[0]),max:new Date(t[1]),resolution:t.length>=3&&"0"!==t[2]?pe(t[2]):void 0}})).filter(d.pC):r.map((e=>new Date(e)))}function pe(e){const t=e.match(/(?:p(\d+y|\d+(?:.|,)\d+y)?(\d+m|\d+(?:.|,)\d+m)?(\d+d|\d+(?:.|,)\d+d)?)?(?:t(\d+h|\d+(?:.|,)\d+h)?(\d+m|\d+(?:.|,)\d+m)?(\d+s|\d+(?:.|,)\d+s)?)?/i);return t?{years:me(t[1]),months:me(t[2]),days:me(t[3]),hours:me(t[4]),minutes:me(t[5]),seconds:me(t[6])}:null}function me(e){if(!e)return 0;const t=e.match(/(?:\d+(?:.|,)\d+|\d+)/);if(!t)return 0;const r=t[0].replace(",",".");return Number(r)}function fe(e){return e.toISOString().replace(/\.[0-9]{3}/,"")}const ye=new Set([102100,3857,102113,900913]),ge=new Set([3395,54004]),be=new h.X({bmp:"image/bmp",gif:"image/gif",jpg:"image/jpeg",png:"image/png",svg:"image/svg+xml"},{ignoreUnknown:!1});function _e(e){return"text/html"===e}function ve(e){return"text/plain"===e}let we=class extends((0,T.h)((0,L.n)((0,F.Q)((0,M.M)((0,N.q)((0,A.I)((0,p.R)(O.Z)))))))){constructor(...e){super(...e),this.allSublayers=new u.Z({getCollections:()=>[this.sublayers],getChildrenFunction:e=>e.sublayers}),this.customParameters=null,this.customLayerParameters=null,this.copyright=null,this.description=null,this.dimensions=null,this.fullExtent=null,this.fullExtents=null,this.featureInfoFormats=null,this.featureInfoUrl=null,this.fetchFeatureInfoFunction=null,this.imageFormat=null,this.imageMaxHeight=2048,this.imageMaxWidth=2048,this.imageTransparency=!0,this.legendEnabled=!0,this.mapUrl=null,this.isReference=null,this.operationalLayerType="WMS",this.spatialReference=null,this.spatialReferences=null,this.sublayers=null,this.type="wms",this.url=null,this.version=null,this.addHandles([(0,f.on)((()=>this.sublayers),"after-add",(({item:e})=>{e.parent=e.layer=this}),f.Z_),(0,f.on)((()=>this.sublayers),"after-remove",(({item:e})=>{e.layer=e.parent=null}),f.Z_),(0,f.YP)((()=>this.sublayers),((e,t)=>{if(t)for(const e of t)e.layer=e.parent=null;if(e)for(const t of e)t.parent=t.layer=this}),f.Z_)])}normalizeCtorArgs(e,t){return"string"==typeof e?{url:e,...t}:e}load(e){const t=(0,d.pC)(e)?e.signal:null;return this.addResolvingPromise(this.loadFromPortal({supportedTypes:["WMS"]},e).catch(m.r9).then((()=>this._fetchService(t)))),Promise.resolve(this)}readFullExtentFromItemOrMap(e,t){const r=t.extent;return r?new x.Z({xmin:r[0][0],ymin:r[0][1],xmax:r[1][0],ymax:r[1][1]}):null}writeFullExtent(e,t){t.extent=[[e.xmin,e.ymin],[e.xmax,e.ymax]]}get featureInfoFormat(){return(0,d.Wi)(this.featureInfoFormats)?null:this.featureInfoFormats.find(_e)??this.featureInfoFormats.find(ve)??null}set featureInfoFormat(e){(0,d.pC)(e)?(_e(e)||ve(e))&&this._override("featureInfoFormat",e):(this.revert("featureInfoFormat","service"),this._clearOverride("featureInfoFormat"))}readImageFormat(e,t){const r=t.supportedImageFormatTypes;return r&&r.includes("image/png")?"image/png":r&&r[0]}readSpatialReferenceFromItemOrDocument(e,t){return new E.Z(t.spatialReferences[0])}writeSpatialReferences(e,t){const r=this.spatialReference?.wkid;e&&r?(t.spatialReferences=e.filter((e=>e!==r)),t.spatialReferences.unshift(r)):t.spatialReferences=e}readSublayersFromItemOrMap(e,t,r){return Ce(t.layers,r,t.visibleLayers)}readSublayers(e,t,r){return Ce(t.layers,r)}writeSublayers(e,t,r,s){t.layers=[];const n=new Map,i=e.flatten((({sublayers:e})=>e??[]));for(const e of i)if("number"==typeof e.parent?.id){const t=n.get(e.parent.id);null!=t?t.push(e.id):n.set(e.parent.id,[e.id])}for(const e of i){const r={sublayer:e,...s},i=e.write({parentLayerId:"number"==typeof e.parent?.id?e.parent.id:-1},r);if(n.has(e.id)&&(i.sublayerIds=n.get(e.id)),!e.sublayers&&e.name){const s=e.write({},r);delete s.id,t.layers.push(s)}}t.visibleLayers=i.filter((({visible:e,sublayers:t})=>e&&!t)).map((({name:e})=>e)).toArray()}createExportImageParameters(e,t,r,s){const n=s?.pixelRatio??1,i=(0,I.yZ)({extent:e,width:t})*n,l=new q({layer:this,scale:i}),{xmin:o,ymin:a,xmax:u,ymax:h,spatialReference:c}=e,p=function(e,t){let r=e.wkid;return(0,d.Wi)(t)?r:(null!=r&&t.includes(r)||!e.latestWkid||(r=e.latestWkid),null!=r&&ye.has(r)?t.find((e=>ye.has(e)))||t.find((e=>ge.has(e)))||102100:r)}(c,this.spatialReferences),m="1.3.0"===this.version&&(0,D.A)(p)?`${a},${o},${h},${u}`:`${o},${a},${u},${h}`,f=l.toJSON();return{bbox:m,["1.3.0"===this.version?"crs":"srs"]:null==p||isNaN(p)?void 0:"EPSG:"+p,...f}}async fetchImage(e,t,r,s){const n=this.mapUrl,i=this.createExportImageParameters(e,t,r,s);if(!i.layers){const e=document.createElement("canvas");return e.width=t,e.height=r,e}const l=s?.timeExtent?.start,a=s?.timeExtent?.end,u=(0,d.pC)(l)&&(0,d.pC)(a)?l.getTime()===a.getTime()?fe(l):`${fe(l)}/${fe(a)}`:void 0,h={responseType:"image",query:this._mixCustomParameters({width:t,height:r,...i,time:u,...this.refreshParameters}),signal:s?.signal};return(0,o.default)(n??"",h).then((e=>e.data))}async fetchImageBitmap(e,t,r,s){const n=this.mapUrl??"",i=this.createExportImageParameters(e,t,r,s);if(!i.layers){const e=document.createElement("canvas");return e.width=t,e.height=r,e}const l=s?.timeExtent?.start,a=s?.timeExtent?.end,u=(0,d.pC)(l)&&(0,d.pC)(a)?l.getTime()===a.getTime()?fe(l):`${fe(l)}/${fe(a)}`:void 0,h={responseType:"blob",query:this._mixCustomParameters({width:t,height:r,...i,time:u,...this.refreshParameters}),signal:s?.signal},{data:c}=await(0,o.default)(n,h);return(0,B.g)(c,n)}fetchFeatureInfo(e,t,r,s,n){const i=(0,I.yZ)({extent:e,width:t}),l=function(e){const t=e.filter((e=>e.popupEnabled&&e.name&&e.queryable));return t.length?t.map((({name:e})=>e)).join():null}(new q({layer:this,scale:i}).visibleSublayers);if((0,d.Wi)(this.featureInfoUrl)||(0,d.Wi)(l))return Promise.resolve([]);if((0,d.Wi)(this.fetchFeatureInfoFunction)&&(0,d.Wi)(this.featureInfoFormat))return Promise.resolve([]);const o="1.3.0"===this.version?{I:s,J:n}:{x:s,y:n},a={query_layers:l,request:"GetFeatureInfo",info_format:this.featureInfoFormat,feature_count:25,width:t,height:r,...o},u={...this.createExportImageParameters(e,t,r),...a},h=this._mixCustomParameters(u);return(0,d.pC)(this.fetchFeatureInfoFunction)?this.fetchFeatureInfoFunction(h):this._defaultFetchFeatureInfoFunction((0,y.fl)(this.featureInfoUrl,h))}findSublayerById(e){return this.allSublayers.find((t=>t.id===e))}findSublayerByName(e){return this.allSublayers.find((t=>t.name===e))}serviceSupportsSpatialReference(e){return(0,R.G)(this.url)||null!=this.spatialReferences&&this.spatialReferences.some((t=>{const r=900913===t?E.Z.WebMercator:new E.Z({wkid:t});return(0,S.fS)(r,e)}))}_defaultFetchFeatureInfoFunction(e){const t=document.createElement("iframe");t.src=(0,y.qg)(e),t.style.border="none",t.style.margin="0",t.style.width="100%",t.setAttribute("sandbox","");const r=new l.Z({title:this.title,content:t}),s=new i.Z({sourceLayer:this,popupTemplate:r});return Promise.resolve([s])}async _fetchService(e){if(!this.resourceInfo){const{path:t,query:r}=this.parsedUrl??{};r?.service&&(r.SERVICE=r.service,delete r.service),r?.request&&(r.REQUEST=r.request,delete r.request);const{data:s}=await(0,o.default)(t??"",{query:{SERVICE:"WMS",REQUEST:"GetCapabilities",...r,...this.customParameters},responseType:"xml",signal:e});this.resourceInfo=z(s)}if(this.parsedUrl){const e=new y.R9(this.parsedUrl.path),{httpsDomains:t}=n.Z.request;"https"!==e.scheme||e.port&&"443"!==e.port||!e.host||t.includes(e.host)||t.push(e.host)}this.read(this.resourceInfo,{origin:"service"})}_mixCustomParameters(e){if(!this.customLayerParameters&&!this.customParameters)return e;const t={...this.customParameters,...this.customLayerParameters};for(const r in t)e[r.toLowerCase()]=t[r];return e}};function Ce(e,t,r){e=e??[];const s=new Map;e.every((e=>null==e.id))&&(e=(0,c.d9)(e)).forEach(((e,t)=>e.id=t));for(const n of e){const e=new Y;e.read(n,t),r&&!r.includes(e.name)&&(e.visible=!1),s.set(e.id,e)}const n=[];for(const t of e){const e=null!=t.id?s.get(t.id):null;if(e)if(null!=t.parentLayerId&&t.parentLayerId>=0){const r=s.get(t.parentLayerId);if(!r)continue;r.sublayers||(r.sublayers=new a.Z),r.sublayers.push(e)}else n.push(e)}return n}(0,s._)([(0,g.Cb)({readOnly:!0})],we.prototype,"allSublayers",void 0),(0,s._)([(0,g.Cb)({json:{type:Object,write:!0}})],we.prototype,"customParameters",void 0),(0,s._)([(0,g.Cb)({json:{type:Object,write:!0}})],we.prototype,"customLayerParameters",void 0),(0,s._)([(0,g.Cb)({type:String,json:{write:!0}})],we.prototype,"copyright",void 0),(0,s._)([(0,g.Cb)()],we.prototype,"description",void 0),(0,s._)([(0,g.Cb)({readOnly:!0})],we.prototype,"dimensions",void 0),(0,s._)([(0,g.Cb)({json:{type:[[Number]],read:{source:"extent"},write:{target:"extent"},origins:{"web-document":{write:{ignoreOrigin:!0}},"portal-item":{write:{ignoreOrigin:!0}}}}})],we.prototype,"fullExtent",void 0),(0,s._)([(0,_.r)(["web-document","portal-item"],"fullExtent",["extent"])],we.prototype,"readFullExtentFromItemOrMap",null),(0,s._)([(0,w.c)(["web-document","portal-item"],"fullExtent",{extent:{type:[[Number]]}})],we.prototype,"writeFullExtent",null),(0,s._)([(0,g.Cb)()],we.prototype,"fullExtents",void 0),(0,s._)([(0,g.Cb)({type:String,json:{write:{ignoreOrigin:!0}}})],we.prototype,"featureInfoFormat",null),(0,s._)([(0,g.Cb)({type:[String],readOnly:!0})],we.prototype,"featureInfoFormats",void 0),(0,s._)([(0,g.Cb)({type:String,json:{write:{ignoreOrigin:!0}}})],we.prototype,"featureInfoUrl",void 0),(0,s._)([(0,g.Cb)()],we.prototype,"fetchFeatureInfoFunction",void 0),(0,s._)([(0,g.Cb)({type:String,json:{origins:{"web-document":{default:"image/png",type:be.jsonValues,read:{reader:be.read,source:"format"},write:{writer:be.write,target:"format"}}}}})],we.prototype,"imageFormat",void 0),(0,s._)([(0,_.r)("imageFormat",["supportedImageFormatTypes"])],we.prototype,"readImageFormat",null),(0,s._)([(0,g.Cb)({type:Number,json:{read:{source:"maxHeight"},write:{target:"maxHeight"}}})],we.prototype,"imageMaxHeight",void 0),(0,s._)([(0,g.Cb)({type:Number,json:{read:{source:"maxWidth"},write:{target:"maxWidth"}}})],we.prototype,"imageMaxWidth",void 0),(0,s._)([(0,g.Cb)()],we.prototype,"imageTransparency",void 0),(0,s._)([(0,g.Cb)(j.rn)],we.prototype,"legendEnabled",void 0),(0,s._)([(0,g.Cb)({type:["show","hide","hide-children"]})],we.prototype,"listMode",void 0),(0,s._)([(0,g.Cb)({type:String,json:{write:{ignoreOrigin:!0}}})],we.prototype,"mapUrl",void 0),(0,s._)([(0,g.Cb)({type:Boolean,json:{read:!1,write:{enabled:!0,overridePolicy:()=>({enabled:!1})}}})],we.prototype,"isReference",void 0),(0,s._)([(0,g.Cb)({type:["WMS"]})],we.prototype,"operationalLayerType",void 0),(0,s._)([(0,g.Cb)()],we.prototype,"resourceInfo",void 0),(0,s._)([(0,g.Cb)({type:E.Z,json:{origins:{service:{read:{source:"extent.spatialReference"}}},write:!1}})],we.prototype,"spatialReference",void 0),(0,s._)([(0,_.r)(["web-document","portal-item"],"spatialReference",["spatialReferences"])],we.prototype,"readSpatialReferenceFromItemOrDocument",null),(0,s._)([(0,g.Cb)({type:[b.z8],json:{read:!1,origins:{service:{read:!0},"web-document":{read:!1,write:{ignoreOrigin:!0}},"portal-item":{read:!1,write:{ignoreOrigin:!0}}}}})],we.prototype,"spatialReferences",void 0),(0,s._)([(0,w.c)(["web-document","portal-item"],"spatialReferences")],we.prototype,"writeSpatialReferences",null),(0,s._)([(0,g.Cb)({type:a.Z.ofType(Y),json:{write:{target:"layers",overridePolicy(e,t,r){if(function(e,t){return e.some((e=>{for(const r in e)if((0,C.d)(e,r,null,t))return!0;return!1}))}(this.allSublayers,r))return{ignoreOrigin:!0}}}}})],we.prototype,"sublayers",void 0),(0,s._)([(0,_.r)(["web-document","portal-item"],"sublayers",["layers","visibleLayers"])],we.prototype,"readSublayersFromItemOrMap",null),(0,s._)([(0,_.r)("service","sublayers",["layers"])],we.prototype,"readSublayers",null),(0,s._)([(0,w.c)("sublayers",{layers:{type:[Y]},visibleLayers:{type:[String]}})],we.prototype,"writeSublayers",null),(0,s._)([(0,g.Cb)({json:{read:!1},readOnly:!0,value:"wms"})],we.prototype,"type",void 0),(0,s._)([(0,g.Cb)(j.HQ)],we.prototype,"url",void 0),(0,s._)([(0,g.Cb)({type:String,json:{write:{ignoreOrigin:!0}}})],we.prototype,"version",void 0),we=(0,s._)([(0,v.j)("esri.layers.WMSLayer")],we);const xe=we},34760:(e,t,r)=>{r.d(t,{Q:()=>g});var s=r(43697),n=r(92604),i=r(95330),l=r(5600),o=(r(75215),r(67676),r(52011)),a=r(46791),u=(r(80442),r(20102),r(26258),r(87538));const h=new a.Z,c=new WeakMap;function d(e){return null!=e&&"object"==typeof e&&"refreshInterval"in e&&"refresh"in e}function p(e,t){return Number.isFinite(e)&&Number.isFinite(t)?t<=0?e:p(t,e%t):0}let m=0,f=0;function y(){const e=Date.now();for(const t of h)t.refreshInterval&&e-(c.get(t)??0)+5>=6e4*t.refreshInterval&&(c.set(t,e),t.refresh(e))}(0,u.EH)((()=>{const e=Date.now();let t=0;for(const r of h)t=p(Math.round(6e4*r.refreshInterval),t),r.refreshInterval?c.get(r)||c.set(r,e):c.delete(r);if(t!==f){if(f=t,clearInterval(m),0===f)return void(m=0);m=setInterval(y,f)}}));const g=e=>{let t=class extends e{constructor(...e){super(...e),this.refreshInterval=0,this.refreshTimestamp=0,this._debounceHasDataChanged=(0,i.Ds)((()=>this.hasDataChanged())),this.when().then((()=>{!function(e){d(e)&&h.push(e)}(this)}),(()=>{}))}destroy(){d(this)&&h.includes(this)&&h.remove(this)}get refreshParameters(){return{_ts:this.refreshTimestamp||null}}refresh(e=Date.now()){(0,i.R8)(this._debounceHasDataChanged()).then((t=>{t&&this._set("refreshTimestamp",e),this.emit("refresh",{dataChanged:t})}),(e=>{n.Z.getLogger(this.declaredClass).error(e),this.emit("refresh",{dataChanged:!1,error:e})}))}async hasDataChanged(){return!0}};return(0,s._)([(0,l.Cb)({type:Number,cast:e=>e>=.1?e:e<=0?0:.1,json:{write:!0}})],t.prototype,"refreshInterval",void 0),(0,s._)([(0,l.Cb)({readOnly:!0})],t.prototype,"refreshTimestamp",void 0),(0,s._)([(0,l.Cb)()],t.prototype,"refreshParameters",null),t=(0,s._)([(0,o.j)("esri.layers.mixins.RefreshableLayer")],t),t}},28294:(e,t,r)=>{r.d(t,{n:()=>d});var s=r(43697),n=r(92835),i=r(84552),l=r(5600),o=(r(75215),r(67676),r(71715)),a=r(52011),u=r(35671),h=r(76259),c=r(78981);const d=e=>{let t=class extends e{constructor(){super(...arguments),this.timeExtent=null,this.timeOffset=null,this.useViewTime=!0}readOffset(e,t){const r=t.timeInfo.exportOptions;if(!r)return null;const s=r.timeOffset,n=c.v.fromJSON(r.timeOffsetUnits);return s&&n?new i.Z({value:s,unit:n}):null}set timeInfo(e){(0,u.UF)(e,this.fieldsIndex),this._set("timeInfo",e)}};return(0,s._)([(0,l.Cb)({type:n.Z,json:{write:!1}})],t.prototype,"timeExtent",void 0),(0,s._)([(0,l.Cb)({type:i.Z})],t.prototype,"timeOffset",void 0),(0,s._)([(0,o.r)("service","timeOffset",["timeInfo.exportOptions"])],t.prototype,"readOffset",null),(0,s._)([(0,l.Cb)({value:null,type:h.Z,json:{write:!0,origins:{"web-document":{read:!1,write:!1},"portal-item":{read:!1,write:!1}}}})],t.prototype,"timeInfo",null),(0,s._)([(0,l.Cb)({type:Boolean,json:{read:{source:"timeAnimation"},write:{target:"timeAnimation"},origins:{"web-scene":{read:!1,write:!1}}}})],t.prototype,"useViewTime",void 0),t=(0,s._)([(0,a.j)("esri.layers.mixins.TemporalLayer")],t),t}},52162:(e,t,r)=>{r.d(t,{A:()=>n});const s=[[3819,3819],[3821,3824],[3889,3889],[3906,3906],[4001,4025],[4027,4036],[4039,4047],[4052,4055],[4074,4075],[4080,4081],[4120,4176],[4178,4185],[4188,4216],[4218,4289],[4291,4304],[4306,4319],[4322,4326],[4463,4463],[4470,4470],[4475,4475],[4483,4483],[4490,4490],[4555,4558],[4600,4646],[4657,4765],[4801,4811],[4813,4821],[4823,4824],[4901,4904],[5013,5013],[5132,5132],[5228,5229],[5233,5233],[5246,5246],[5252,5252],[5264,5264],[5324,5340],[5354,5354],[5360,5360],[5365,5365],[5370,5373],[5381,5381],[5393,5393],[5451,5451],[5464,5464],[5467,5467],[5489,5489],[5524,5524],[5527,5527],[5546,5546],[2044,2045],[2081,2083],[2085,2086],[2093,2093],[2096,2098],[2105,2132],[2169,2170],[2176,2180],[2193,2193],[2200,2200],[2206,2212],[2319,2319],[2320,2462],[2523,2549],[2551,2735],[2738,2758],[2935,2941],[2953,2953],[3006,3030],[3034,3035],[3038,3051],[3058,3059],[3068,3068],[3114,3118],[3126,3138],[3150,3151],[3300,3301],[3328,3335],[3346,3346],[3350,3352],[3366,3366],[3389,3390],[3416,3417],[3833,3841],[3844,3850],[3854,3854],[3873,3885],[3907,3910],[4026,4026],[4037,4038],[4417,4417],[4434,4434],[4491,4554],[4839,4839],[5048,5048],[5105,5130],[5253,5259],[5269,5275],[5343,5349],[5479,5482],[5518,5519],[5520,5520],[20004,20032],[20064,20092],[21413,21423],[21473,21483],[21896,21899],[22171,22177],[22181,22187],[22191,22197],[25884,25884],[27205,27232],[27391,27398],[27492,27492],[28402,28432],[28462,28492],[30161,30179],[30800,30800],[31251,31259],[31275,31279],[31281,31290],[31466,31700]];function n(e){return null!=e&&s.some((([t,r])=>e>=t&&e<=r))}},76259:(e,t,r)=>{r.d(t,{Z:()=>y});var s=r(43697),n=r(92835),i=r(84552),l=r(2368),o=r(96674),a=r(70586),u=r(5600),h=(r(75215),r(67676),r(71715)),c=r(52011),d=r(30556),p=r(80216);function m(e,t){return i.Z.fromJSON({value:e,unit:t})}let f=class extends((0,l.J)(o.wq)){constructor(e){super(e),this.cumulative=!1,this.endField=null,this.fullTimeExtent=null,this.hasLiveData=!1,this.interval=null,this.startField=null,this.timeReference=null,this.trackIdField=null,this.useTime=!0}readFullTimeExtent(e,t){if(!t.timeExtent||!Array.isArray(t.timeExtent)||2!==t.timeExtent.length)return null;const r=new Date(t.timeExtent[0]),s=new Date(t.timeExtent[1]);return new n.Z({start:r,end:s})}writeFullTimeExtent(e,t){e&&(0,a.pC)(e.start)&&(0,a.pC)(e.end)?t.timeExtent=[e.start.getTime(),e.end.getTime()]:t.timeExtent=null}readInterval(e,t){return t.timeInterval&&t.timeIntervalUnits?m(t.timeInterval,t.timeIntervalUnits):t.defaultTimeInterval&&t.defaultTimeIntervalUnits?m(t.defaultTimeInterval,t.defaultTimeIntervalUnits):null}writeInterval(e,t){t.timeInterval=e?.toJSON().value??null,t.timeIntervalUnits=e?.toJSON().unit??null}};(0,s._)([(0,u.Cb)({type:Boolean,json:{name:"exportOptions.timeDataCumulative",write:!0}})],f.prototype,"cumulative",void 0),(0,s._)([(0,u.Cb)({type:String,json:{name:"endTimeField",write:{enabled:!0,allowNull:!0}}})],f.prototype,"endField",void 0),(0,s._)([(0,u.Cb)({type:n.Z,json:{write:{enabled:!0,allowNull:!0}}})],f.prototype,"fullTimeExtent",void 0),(0,s._)([(0,h.r)("fullTimeExtent",["timeExtent"])],f.prototype,"readFullTimeExtent",null),(0,s._)([(0,d.c)("fullTimeExtent")],f.prototype,"writeFullTimeExtent",null),(0,s._)([(0,u.Cb)({type:Boolean,json:{write:!0}})],f.prototype,"hasLiveData",void 0),(0,s._)([(0,u.Cb)({type:i.Z,json:{write:{enabled:!0,allowNull:!0}}})],f.prototype,"interval",void 0),(0,s._)([(0,h.r)("interval",["timeInterval","timeIntervalUnits","defaultTimeInterval","defaultTimeIntervalUnits"])],f.prototype,"readInterval",null),(0,s._)([(0,d.c)("interval")],f.prototype,"writeInterval",null),(0,s._)([(0,u.Cb)({type:String,json:{name:"startTimeField",write:{enabled:!0,allowNull:!0}}})],f.prototype,"startField",void 0),(0,s._)([(0,u.Cb)({type:p.Z,json:{write:{enabled:!0,allowNull:!0}}})],f.prototype,"timeReference",void 0),(0,s._)([(0,u.Cb)({type:String,json:{write:{enabled:!0,allowNull:!0}}})],f.prototype,"trackIdField",void 0),(0,s._)([(0,u.Cb)({type:Boolean,json:{name:"exportOptions.useTime",write:!0}})],f.prototype,"useTime",void 0),f=(0,s._)([(0,c.j)("esri.layers.support.TimeInfo")],f);const y=f},90082:(e,t,r)=>{r.d(t,{g:()=>n});var s=r(20102);async function n(e,t){try{return await createImageBitmap(e)}catch(e){throw new s.Z("request:server",`Unable to load ${t}`,{url:t,error:e})}}},78981:(e,t,r)=>{r.d(t,{v:()=>s});const s=(0,r(35454).w)()({esriTimeUnitsMilliseconds:"milliseconds",esriTimeUnitsSeconds:"seconds",esriTimeUnitsMinutes:"minutes",esriTimeUnitsHours:"hours",esriTimeUnitsDays:"days",esriTimeUnitsWeeks:"weeks",esriTimeUnitsMonths:"months",esriTimeUnitsYears:"years",esriTimeUnitsDecades:"decades",esriTimeUnitsCenturies:"centuries",esriTimeUnitsUnknown:void 0})}}]);