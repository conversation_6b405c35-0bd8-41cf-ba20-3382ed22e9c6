package org.thingsboard.server.dao.sql.smartProduction.guard;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.thingsboard.server.dao.model.sql.smartProduction.guard.GuardGroup;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.guard.GuardGroupPageRequest;

@Mapper
public interface GuardGroupMapper extends BaseMapper<GuardGroup> {
    IPage<GuardGroup> findByPage(GuardGroupPageRequest request);

    boolean updateFully(GuardGroup entity);

}
