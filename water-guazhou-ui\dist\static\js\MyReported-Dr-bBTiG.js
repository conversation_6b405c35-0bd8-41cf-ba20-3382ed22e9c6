import{d as B,c as d,r as m,s as u,l as p,bH as g,D as T,u as C,o as N,g as R,n as U,q as f,i as q,F as P,a9 as V,b as L,b6 as $,al as j,b7 as A,aj as G}from"./index-r0dFAfgr.js";import{_ as H}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as J}from"./CardTable-rdWOL4_6.js";import{_ as K}from"./CardSearch-CB_HNR-Q.js";import{f as Q}from"./DateFormatter-Bm9a68Ax.js";import X from"./OrderStepTags-CClNfq4j.js";import Y from"./detail-CU6-qhMl.js";import{h as Z,i as ee,b as te,a as ae,S as re}from"./config-DqqM5K5L.js";import{c as oe,a as se,T as ne}from"./index-CpGhZCTT.js";import{u as le}from"./useStopWorkOrder-8kgjTMvz.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";/* empty css                         */import"./detailSteps-BqRp_Y4m.js";/* empty css                */import"./index-CCFuhOrs.js";/* empty css                             */import"./DPlayer-Be2AurQX.js";import"./DPlayer.min-_HMH7IVX.js";import"./OutsideWorkOrder-C6s8joBt.js";const ie={class:"wrapper"},We=B({__name:"MyReported",setup(ce){const _=d(),b=d(),h=d(),y=d(),O=m({title:"流程明细",group:[],cancel:!1,modalClass:"lightColor"}),k=d(""),v=m({WorkOrderEmergencyLevelList:[]});function W(){se("1").then(e=>{v.WorkOrderEmergencyLevelList=V(e.data.data||[],"children",{label:"name",value:"id"})})}const z=m({filters:[{type:"radio-button",label:"类别",labelWidth:40,field:"organizerId",options:[{label:"我上报的",value:"organizerId"},{label:"抄送我的",value:"ccUserId"}],onChange:e=>{var o;const t=e==="organizerId"?["详情","终止"]:["详情"],r=e==="organizerId"?100:80;a.operationWidth=r,(o=a.operations)==null||o.map(n=>{const l=n.text;n.perm=t.indexOf(l)!==-1}),s()}},{type:"input",label:"标题",field:"title",onChange:()=>s()},{sm:24,xl:24,type:"radio-button",label:"工单状态",field:"status",options:Z(!0),formatter:ee},{type:"daterange",label:"发起时间",field:"date"},{type:"select",label:"来源",field:"source",options:te()},{type:"select-tree",label:"类型",field:"type",options:ae()}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",svgIcon:u(j),click:()=>s()},{perm:!0,text:"重置",type:"default",svgIcon:u(A),click:()=>{var e;(e=b.value)==null||e.resetForm(),s()}},{perm:!0,text:"导出",type:"warning",svgIcon:u(G),click:()=>{var e;(e=h.value)==null||e.exportTable()}}]}],defaultParams:{organizerId:"organizerId",date:[p().subtract(1,"M").format(g),p().format(g)]},handleSearch:()=>s()}),a=m({expandable:!0,expandComponent:u(X),defaultExpandAll:!0,columns:[{label:"工单编号",prop:"serialNo"},{label:"来源",prop:"source"},{label:"类型",prop:"type"},{label:"标题",prop:"title"},{label:"状态",prop:"statusName"},{label:"发起时间",prop:"createTime",formatter:e=>Q(e.createTime)},{label:"紧急程度",prop:"level",tag:!0,tagColor:e=>{var t;return((t=v.WorkOrderEmergencyLevelList.find(r=>r.value===e.level))==null?void 0:t.color)||""},formatter:e=>{var t;return(t=v.WorkOrderEmergencyLevelList.find(r=>r.value===e.level))==null?void 0:t.label}}],dataList:[],pagination:{refreshData:({page:e,size:t})=>{a.pagination.page=e,a.pagination.limit=t,s()}},operations:[{perm:!0,isTextBtn:!0,text:"详情",click:e=>F(e)},{perm:!0,isTextBtn:!0,text:"终止",disabled:e=>re.includes(e.status),click:e=>{var t;a.currentRow=e,(t=_.value)==null||t.openDialog()}}]}),{FormConfig_Stop:w}=le(async e=>{var r,o;const t=await ne((r=a.currentRow)==null?void 0:r.id,e);t.data.code===200?(L.success("操作成功"),s(),(o=_.value)==null||o.closeDialog()):L.error(t.data.err||"操作失败")}),F=e=>{var t;k.value=e.id||"",O.title=e.serialNo,(t=y.value)==null||t.openDrawer()},s=async()=>{var e,t,r,o,n,l,S,x,I;a.loading=!0;try{const i=((e=b.value)==null?void 0:e.queryParams)||{},[E,M]=((t=i.date)==null?void 0:t.length)===2?[p(i.date[0],g).valueOf(),p(i.date[1],g).endOf("D").valueOf()]:[p().subtract(1,"M").startOf("D").valueOf(),p().endOf("D").valueOf()],c={page:a.pagination.page||1,size:a.pagination.limit||20,...i,fromTime:E,toTime:M};delete c.date,i.organizerId==="organizerId"?(c.organizerId=T(((o=(r=C().user)==null?void 0:r.id)==null?void 0:o.id)||""),c.ccUserId=""):(c.organizerId="",c.ccUserId=T(((l=(n=C().user)==null?void 0:n.id)==null?void 0:l.id)||""));const D=await oe(c);a.dataList=((x=(S=D.data)==null?void 0:S.data)==null?void 0:x.data)||[],a.pagination.total=((I=D.data)==null?void 0:I.data.total)||0}catch{}a.loading=!1};return N(()=>{W(),s()}),(e,t)=>{const r=K,o=J,n=H,l=$;return R(),U("div",ie,[f(r,{ref_key:"refSearch",ref:b,config:z},null,8,["config"]),f(o,{ref_key:"refTable",ref:h,config:a,class:"card-table"},null,8,["config"]),f(n,{ref_key:"refForm_Stop",ref:_,config:q(w)},null,8,["config"]),f(l,{ref_key:"refdetail",ref:y,config:O},{default:P(()=>[f(Y,{id:k.value},null,8,["id"])]),_:1},8,["config"])])}}});export{We as default};
