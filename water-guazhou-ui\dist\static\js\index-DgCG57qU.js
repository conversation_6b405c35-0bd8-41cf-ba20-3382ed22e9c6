import{d as q,c as l,s as y,r as x,o as E,g as p,n as h,q as i,i as n,p as V,F as f,aB as S,aJ as T,h as B,bU as F,bW as I,b6 as U,b7 as L,ak as N,C as A}from"./index-r0dFAfgr.js";import{_ as G}from"./CardSearch-CB_HNR-Q.js";import{I as H}from"./common-CvK_P_ao.js";import P from"./itemCard-Bmo7oqxQ.js";import R from"./detail-Bh7SNC3b.js";import{a as W,b as O,d as j,e as z}from"./index-CjYkj_FN.js";import{G as J,a as M,U as Q}from"./GeneralProcessing-CQ8i9ijT.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";/* empty css                             */const Y={class:"wrapper"},$={style:{height:"calc(100% - 100px)"}},K=q({__name:"index",setup(X){const o=l(),m=l(),d=l(),_=l(),g=l([]),v=l({filters:[{label:"专家姓名",field:"name",type:"input"},{type:"btn-group",btns:[{perm:!0,text:"查询",icon:H.QUERY,click:()=>s()},{type:"default",perm:!0,text:"重置",svgIcon:y(L),click:()=>{var e;(e=d.value)==null||e.resetForm(),s()}},{type:"success",perm:!0,text:"新增",svgIcon:y(N),click:()=>{var e;c.defaultValue={gender:"男"},(e=o.value)==null||e.openDrawer()}}]}]}),c=x({title:"新增",labelWidth:"100px",submit:e=>{J(e,O,W,c).then(()=>{var t;(t=o.value)==null||t.closeDrawer(),s()})},defaultValue:{},group:[{fields:[{type:"divider",text:"基本信息"},{xl:8,type:"input",label:"姓名",field:"name",rules:[{required:!0,message:"请输入姓名"}]},{xl:8,type:"switch",label:"性别",field:"gender",activeText:"男",inActiveText:"女",activeValue:"男",inActiveValue:"女",inActiveColor:"#66b1ff"},{xl:8,type:"input-number",label:"联系电话",field:"phone",rules:[{required:!0,message:"请输入联系电话"}]},{xl:8,type:"input",label:"联系邮箱",field:"email",rules:[{required:!0,message:"请输入联系邮箱"}]},{xl:8,type:"input",label:"最高学历",field:"educationLevel",rules:[{required:!0,message:"请输入最高学历"}]},{type:"divider",text:"行业领域"},{xl:8,type:"input",label:"职称",field:"professionalTitle",rules:[{required:!0,message:"请输入职称"}]},{xl:8,type:"date",label:"职称评定时间",field:"professionalTitleTime"},{xl:8,type:"input",label:"行业领域",field:"industrySector",rules:[{required:!0,message:"请输入行业领域"}]},{xl:8,type:"date",label:"行业从事时间",field:"industryTime"},{xl:8,type:"input",label:"工作单位",field:"deptName",rules:[{required:!0,message:"请输入工作单位"}]},{xl:8,type:"input-number",label:"单位电话",field:"deptPhone",rules:[{required:!0,message:"请输入单位电话"}]},{type:"divider",text:"工作经历"},{type:"wangeditor",label:"工作经历",field:"jobHistory"},{type:"divider",text:"所获荣誉"},{type:"wangeditor",label:"所获荣誉",field:"honorHistory"},{type:"divider",text:"学术成就"},{type:"wangeditor",label:"学术成就",field:"academicHistory"}]}]}),k=x({title:"详情",labelWidth:"100px",submit:e=>{},defaultValue:{},group:[{fields:[]}]}),u=(e,t)=>{var a,r;switch(e){case 0:c.defaultValue={...t},(a=o.value)==null||a.openDrawer();break;case 1:_.value={...t},(r=m.value)==null||r.openDrawer();break;case 2:Q(t.id,j,"确定删除该专家信息").then(()=>{s()});break}},s=async e=>{var a;const t={page:1,size:9999,...((a=d.value)==null?void 0:a.queryParams)||{}};M(t,z).then(r=>{g.value=r.data})};return E(async()=>{s()}),(e,t)=>{const a=G,r=F,w=I,b=U;return p(),h("div",Y,[i(a,{ref_key:"refSearch",ref:d,config:n(v)},null,8,["config"]),V("div",$,[i(w,{gutter:20},{default:f(()=>[(p(!0),h(S,null,T(n(g),(C,D)=>(p(),B(r,{key:D,class:"mag_bot_20",span:6},{default:f(()=>[i(P,{config:C,onOpenDetail:u,onEdit:u,onDel:u},null,8,["config"])]),_:2},1024))),128))]),_:1})]),i(b,{ref_key:"refForm",ref:o,config:n(c)},null,8,["config"]),i(b,{ref_key:"refDetail",ref:m,config:n(k)},{default:f(()=>[i(R,{config:n(_)},null,8,["config"])]),_:1},8,["config"])])}}}),ce=A(K,[["__scopeId","data-v-579b0208"]]);export{ce as default};
