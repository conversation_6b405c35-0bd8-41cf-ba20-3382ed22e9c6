import{_ as S}from"./DPlayer-Be2AurQX.js";import{C as z,M as V,ag as y,g as u,n as h,p as n,q as N,F as x,aw as a,an as v,bh as m,aB as I,aJ as F,h as T,bb as j}from"./index-r0dFAfgr.js";import{e as P,f as D,d as O,c as g,M as f,a as p,b as R}from"./index-D9ERhRP6.js";import{g as B,a as q}from"./data-CfQCw447.js";import"./DPlayer.min-_HMH7IVX.js";const{$message:c,$messageError:b,$messageSuccess:E}=V(),M={name:"RealTimeVideo",components:{DPlayer:S},data(){return{videoSources:[],treeData:[],showVideo:[],dataSourceData:[],currentProject:{},currentSize:4,currentSelect:0,videoInfoMap:{},panelOpen:!0,fullScreen:!1,hiddenText:"隐藏",defaultProps:{label:"name"},command:"",currentData:{}}},created(){for(let t=0;t<this.currentSize;t++)this.showVideo.push({id:t,projectId:"",msg:"无信号"});y().then(t=>{if(t.data){this.treeData=t.data;const e=this.treeData.filter(s=>!s.disabled),r=this.$route.query.projectId,i=r?B(e,r):q(e[0]);this.$nextTick(()=>{this.$refs.dcTree?(this.$refs.dcTree.setChecked(i.id),this.$refs.dcTree.setCurrentKey(i.id),this.getProjectData(i)):this.getProjectData(e[0])}),this.currentProject=i,this.currentProId=i.id}else c("暂无项目 不可操作，请创建项目")}).catch(t=>{console.log(t),c("暂无项目 不可操作，请创建项目")})},methods:{async getProjectData(t){if(this.currentData=t,console.log(t),t.serialNumber)this.showVideo[this.currentSelect].videoUrl?(this.closeVideo(this.currentSelect,"获取中"),this.getVideo(t,this.currentSelect)):(this.showVideo[this.currentSelect].msg="视频获取中...",this.getVideo(t,this.currentSelect));else if(t.videoType==="3")if(console.log("点击了项目",t),t.url){const e=await P({rtspUrl:t.url});console.log(e),t.videoUrl={rtspuri:t.url,live:!0},this.videoInfoMap[t.serialNumber]=t,this.showVideo[this.currentSelect]=t,this.selcetVideoBox(this.currentSelect+1)}else this.showVideo[this.currentSelect].msg="视频获取中...";else this.currentProject=t,D(this.currentProject.id).then(e=>{if(e.data.length===0){c("该项目下无视频");return}const r=e.data.map(i=>{const s=this.videoSources.find(l=>l.deviceid===i.serialNumber);return i.status=s&&s.status,i.online=s&&s.online,i});t.children?t.children=t.children.filter(i=>i.nodeType==="Project").concat(r):this.$set(t,"children",r)})},getVideo(t,e){if(this.videoInfoMap[t.serialNumber]){this.showVideo[e]=this.videoInfoMap[t.serialNumber],this.selcetVideoBox(e+1);return}O(t.id).then(r=>{const i=r.data;i?(t.videoUrl={m3u8uri:i,live:!0},this.videoInfoMap[t.serialNumber]=t,this.showVideo[e]=t,this.selcetVideoBox(this.currentSelect+1)):c("获取播放地址失败")}).catch(r=>{c.error(r.message)})},changeShowSize(t){this.currentSize=t,this.showVideo=[],this.currentSelect=0;for(let e=0;e<this.currentSize;e++)this.showVideo.push({id:e,projectId:"",msg:"无信号"})},selcetVideoBox(t){this.currentSelect=t},closeVideo(t,e){this.showVideo[t]={id:t,projectId:"",msg:e==="获取中"?"视频获取中...":"无信号"},this.hiddenText=e+" 当前："+t},async changeControl(t){if(this.command=t.action?this.command:t,!this.showVideo[this.currentSelect].id){c("请选择播放成功的窗口，进行控制");return}const e={id:this.showVideo[this.currentSelect].id,...this.command,action:t.action||0};g(e).then(r=>{r.data.code!==200?changeControl({...e,action:1}).then(i=>{i.data.code!==200?E(i.data.message):b(i.data.message)}):b(r.data.message)})},async chengeControl(t){if(!this.showVideo[this.currentSelect].serialNumber){c("请选择播放成功的窗口，进行控制");return}const e={memberkey:f,deviceid:this.showVideo[this.currentSelect].serialNumber,operator:t,speed:10},r=await p({param:JSON.stringify(e)}),i={parmdata:e,sign:r.data.sign};R(i).then(s=>{s.data.code==="0"&&s.data.msg==="设备不在线"&&c("设备不在线 不可控制")})},async changeZoom(t){if(console.log(t),!this.showVideo[this.currentSelect].serialNumber){c("请选择播放成功的窗口，进行控制");return}const e={memberkey:f,deviceid:this.showVideo[this.currentSelect].serialNumber,operator:t,speed:t===0?0:10},r=await p({param:JSON.stringify(e)}),i={parmdata:e,sign:r.data.sign};g(i).then(s=>{console.log(s)})},async changeAperture(t){if(console.log(t),!this.showVideo[this.currentSelect].serialNumber){c("请选择播放成功的窗口，进行控制");return}const e={memberkey:f,deviceid:this.showVideo[this.currentSelect].serialNumber,operator:t,speed:t===0?0:10},r=await p({param:JSON.stringify(e)}),i={parmdata:e,sign:r.data.sign};g(i).then(s=>{console.log(s)})},panelOpenChange(){this.panelOpen=!this.panelOpen},changeFullScreen(){const t=document.getElementById("full-box");document.fullScreen||document.mozFullScreen||document.webkitIsFullScreen?(document.exitFullscreen&&document.exitFullscreen(),this.fullScreen=!1):(t.requestFullscreen&&t.requestFullscreen()||t.mozRequestFullScreen&&t.mozRequestFullScreen()||t.webkitRequestFullscreen&&t.webkitRequestFullscreen()||t.msRequestFullscreen&&t.msRequestFullscreen(),this.fullScreen=!0)}}},U={class:"left-video-list"},_={class:"list-box tree-list-box"},J={class:"custom-tree-node"},L={class:"c-t-label"},Z={class:"c-t-name"},A={class:"control-box"},G={class:"direction-box"},H={class:"zoom-aperture"},K={class:"zoom-box"},W={class:"aperture-box"},Y={class:"hidden-text"},Q={class:"right-video-show"},X={class:"top-btn-control"},$={id:"full-box",class:"video-container"},ee=["onClick"],te=["onClick"],ne={key:1,class:"video-msg"};function se(t,e,r,i,s,l){const C=j,w=S;return u(),h("div",{class:a(["real-time-video-container",{"real-time-video-full-screen":s.fullScreen}])},[n("div",U,[e[24]||(e[24]=n("p",{class:"list-title"}," 设备列表 ",-1)),n("div",_,[N(C,{ref:"dcTree",class:"data-source-s-tree",data:s.treeData,"node-key":"id","highlight-current":"","default-expand-all":"","default-checked-keys":[1],props:s.defaultProps,"expand-on-click-node":!1,onNodeClick:l.getProjectData},{default:x(({node:o,data:d})=>[n("div",J,[n("p",L,[d.serialNumber||d.videoType==="3"?(u(),h("i",{key:0,class:a(["iconfont icon-shexiangtou",{"shexiangtou-active":d.online}])},null,2)):v("",!0),n("span",Z,m(o.label),1)])])]),_:1},8,["data","props","onNodeClick"])]),s.currentData.videoType!=="3"?(u(),h("div",{key:0,class:a(["video-control",{"hidden-panel":!s.panelOpen}])},[e[23]||(e[23]=n("p",{class:"v-c-title"}," 云台控制 ",-1)),n("div",A,[n("div",G,[n("p",{class:"c-d-btn d-btn-center",onClick:e[0]||(e[0]=o=>l.changeControl({action:"1"}))},e[16]||(e[16]=[n("i",{class:"iconfont icon-zantingtingzhi"},null,-1)])),n("p",{class:"c-d-btn",onClick:e[1]||(e[1]=o=>l.changeControl({command:"UP"}))},e[17]||(e[17]=[n("i",{class:"iconfont icon-arrowright cd-btn-l"},null,-1)])),n("p",{class:"c-d-btn",onClick:e[2]||(e[2]=o=>l.changeControl({command:"RIGHT"}))},e[18]||(e[18]=[n("i",{class:"iconfont icon-arrowright cd-btn-t"},null,-1)])),n("p",{class:"c-d-btn",onClick:e[3]||(e[3]=o=>l.changeControl({command:"LEFT"}))},e[19]||(e[19]=[n("i",{class:"iconfont icon-arrowright cd-btn-b"},null,-1)])),n("p",{class:"c-d-btn",onClick:e[4]||(e[4]=o=>l.changeControl({command:"DOWN"}))},e[20]||(e[20]=[n("i",{class:"iconfont icon-arrowright cd-btn-r"},null,-1)]))]),n("div",H,[n("div",K,[e[21]||(e[21]=n("p",{class:"z-a-title"}," 变焦： ",-1)),n("i",{class:"el-icon-minus c-btn c-btn-lh",style:{"font-size":"20px","font-weight":"800"},onClick:e[5]||(e[5]=o=>l.changeControl({command:"ZOOM_IN"}))},"—"),n("i",{class:"iconfont icon-zantingtingzhi c-btn",onClick:e[6]||(e[6]=o=>l.changeControl({action:"1"}))}),n("i",{class:"iconfont icon-jia c-btn c-btn-lh",onClick:e[7]||(e[7]=o=>l.changeControl({command:"ZOOM_OUT"}))})]),n("div",W,[e[22]||(e[22]=n("p",{class:"z-a-title"}," 光圈： ",-1)),n("i",{class:"el-icon-minus c-btn c-btn-lh",style:{"font-size":"20px","font-weight":"800"},onClick:e[8]||(e[8]=o=>l.changeControl({command:"IRIS_REDUCE"}))},"—"),n("i",{class:"iconfont icon-zantingtingzhi c-btn",onClick:e[9]||(e[9]=o=>l.changeControl({action:"1"}))}),n("i",{class:"iconfont icon-jia c-btn c-btn-lh",onClick:e[10]||(e[10]=o=>l.changeControl({command:"IRIS_ENLARGE"}))})])]),n("p",{class:"panel-control",onClick:e[11]||(e[11]=o=>l.panelOpenChange())},[n("i",{class:a(["iconfont",{"icon-arrowleft":s.panelOpen,"icon-arrowright":!s.panelOpen}])},null,2)]),n("span",Y,m(s.hiddenText),1)])],2)):v("",!0)]),n("div",Q,[n("div",X,[n("div",null,[e[25]||(e[25]=n("span",{class:"t-b-label"},"分屏：",-1)),n("i",{class:a(["iconfont icon-yiping size-btn",{"current-size":s.currentSize===1}]),onClick:e[12]||(e[12]=o=>l.changeShowSize(1))},null,2),n("i",{class:a(["iconfont icon-siping size-btn",{"current-size":s.currentSize===4}]),onClick:e[13]||(e[13]=o=>l.changeShowSize(4))},null,2),n("i",{class:a(["iconfont icon-iconjiuping size-btn",{"current-size":s.currentSize===9}]),onClick:e[14]||(e[14]=o=>l.changeShowSize(9))},null,2)]),n("p",{class:"full-screen",onClick:e[15]||(e[15]=(...o)=>l.changeFullScreen&&l.changeFullScreen(...o))},e[26]||(e[26]=[n("i",{class:"iconfont icon-quanping"},null,-1)]))]),n("div",$,[(u(!0),h(I,null,F(s.showVideo,(o,d)=>(u(),h("div",{key:d,class:a(["video-box",{"item-1-size":s.currentSize===1,"item-4-size":s.currentSize===4,"item-9-size":s.currentSize===9,"current-select":s.currentSelect===d}]),onClick:k=>l.selcetVideoBox(d)},[n("i",{class:a(["el-icon-close",{"show-close":o.videoUrl}]),onClick:k=>l.closeVideo(d,"关闭")},null,10,te),o.videoUrl?(u(),T(w,{key:0,"video-info":o},null,8,["video-info"])):(u(),h("p",ne,m(o.msg),1))],10,ee))),128))])])],2)}const ae=z(M,[["render",se],["__scopeId","data-v-0ac7efa0"]]);export{ae as default};
