package org.thingsboard.server.dao.alarm;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.Device;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.id.DeviceId;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.device.DeviceService;
import org.thingsboard.server.dao.model.sql.AlarmCountEntity;
import org.thingsboard.server.dao.model.sql.ProjectEntity;
import org.thingsboard.server.dao.project.ProjectRelationService;
import org.thingsboard.server.dao.project.ProjectService;
import org.thingsboard.server.dao.sql.alarm.AlarmCountRepository;
import org.thingsboard.server.dao.tenant.TenantService;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AlarmCountServiceImpl implements AlarmCountService {

    @Autowired
    private AlarmCountRepository alarmCountRepository;

    @Autowired
    private DeviceService deviceService;

    @Autowired
    private ProjectRelationService projectRelationService;

    @Autowired
    private ProjectService projectService;

    @Override
    public void save(AlarmCountEntity alarmCountEntity) {
        alarmCountRepository.save(alarmCountEntity);
    }

    @Override
    public Object powerQuantityAlarmCount(JSONObject params, TenantId tenantId) {
        List<JSONObject> result = new ArrayList<>();

        List<AlarmCountEntity> alarmCountList = alarmCountRepository.findByTimeLessThanEqualAndTimeGreaterThanEqual(
                params.getLongValue("endTime"), params.getLongValue("startTime"));

        if (params.containsKey("gatewayId") && StringUtils.isNotBlank(params.getString("gatewayId"))) {
            List<Device> deviceList = deviceService.findDeviceByGateWayId(
                    new DeviceId(UUIDConverter.fromString(params.getString("gatewayId")))
            );
            List<String> deviceIdList = deviceList.stream()
                    .map(device -> UUIDConverter.fromTimeUUID(device.getUuidId()))
                    .collect(Collectors.toList());

            alarmCountList = alarmCountList.stream()
                    .filter(alarm -> deviceIdList.contains(alarm.getDeviceId()))
                    .collect(Collectors.toList());
        }

        // 先按设备分组
        Map<String, List<AlarmCountEntity>> deviceAlarmCountMap = new HashMap<>();
        for (AlarmCountEntity alarmCountEntity : alarmCountList) {
            String deviceId = alarmCountEntity.getDeviceId();
            List<AlarmCountEntity> list = new ArrayList<>();
            if (deviceAlarmCountMap.containsKey(deviceId)) {
                list = deviceAlarmCountMap.get(deviceId);
            }
            list.add(alarmCountEntity);

            deviceAlarmCountMap.put(deviceId, list);
        }

        for (Map.Entry<String, List<AlarmCountEntity>> stringListEntry : deviceAlarmCountMap.entrySet()) {
            String deviceId = stringListEntry.getKey();
            List<AlarmCountEntity> list = stringListEntry.getValue();
            //电压越上限
            int count1 = 0;
            //电压越下限
            int count2 = 0;
            //电压波动
            int count3 = 0;
            //三相电压不平衡
            int count4 = 0;
            //谐波电流
            int count5 = 0;
            //谐波电压
            int count6 = 0;
            //电压合格
            BigDecimal count7 = new BigDecimal("0");
            //电压不合格
            BigDecimal count8 = new BigDecimal("0");

            int total = 0;
            JSONObject countObj = new JSONObject();
            countObj.put("deviceId", deviceId);
            for (AlarmCountEntity alarmCountEntity : list) {
                String group = alarmCountEntity.getGroup();
                switch (group) {
                    case "电压越上限":
                        count1++;
                        break;
                    case "电压越下限":
                        count2++;
                        break;
                    case "电压波动":
                        count3++;
                        break;
                    case "三相电压不平衡":
                        count4++;
                        break;
                    case "谐波电流":
                        count5++;
                        break;
                    case "谐波电压":
                        count6++;
                        break;
                    case "电压不合格":
                        count8 = count8.add(BigDecimal.ONE);
                        continue;
                    case "电压合格":
                        count7 = count7.add(BigDecimal.ONE);
                        continue;
                }
                total++;
            }
            countObj.put("电压越上限", count1);
            countObj.put("电压越下限", count2);
            countObj.put("电压波动", count3);
            countObj.put("三相电压不平衡", count4);
            countObj.put("谐波电流", count5);
            countObj.put("谐波电压", count6);
            try {
                countObj.put("电压合格率", (count7
                        .divide((count7.add(count8)), 2, BigDecimal.ROUND_HALF_DOWN)
                        .multiply(new BigDecimal("100"))
                        .toString()
                ) + "%");
            } catch (Exception e) {
                e.printStackTrace();
                countObj.put("电压合格率", 0);
            }
            countObj.put("total", total);

            result.add(countObj);
        }

        List<ProjectEntity> projectList = projectService.findByTenantId(tenantId);
        Map<String, ProjectEntity> projectMap = new HashMap<>();
        projectList.forEach(project -> projectMap.put(project.getId(), project));

        List<Device> deviceList = deviceService.findByTenantId(tenantId);
        Map<String, Device> deviceMap = new HashMap<>();
        deviceList.forEach(device -> deviceMap.put(UUIDConverter.fromTimeUUID(device.getUuidId()), device));

        for (JSONObject jsonObject : result) {
            Device device = deviceMap.get(jsonObject.getString("deviceId"));
            if (device != null) {
                jsonObject.put("deviceName", device.getName());
                // 查询设备所属项目
                List<ProjectEntity> projectEntityList = projectRelationService.findProjectRelationByEntityTypeAndEntityId(
                        DataConstants.ProjectRelationEntityType.DEVICE.name(), jsonObject.getString("deviceId"));
                if (projectEntityList != null && projectEntityList.size() > 0) {
                    ProjectEntity projectEntity = projectEntityList.get(0);
                    jsonObject.put("projectName", projectMap.get(projectEntity.getId()).getName());
                    jsonObject.put("projectId", projectMap.get(projectEntity.getId()).getId());
                }
            }
        }

        return result;
    }
}
