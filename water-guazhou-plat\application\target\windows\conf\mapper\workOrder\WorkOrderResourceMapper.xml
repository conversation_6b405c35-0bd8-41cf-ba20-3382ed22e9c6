<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.workOrder.WorkOrderResourceMapper">
    <select id="findList" resultType="org.thingsboard.server.dao.model.sql.workOrder.WorkOrderResource">
        select *
        from work_order_resource
        <where>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>
            <if test="tenantId != null and status != ''">
                and tenant_id = #{tenantId}
            </if>
        </where>
        order by create_time desc
    </select>
</mapper>