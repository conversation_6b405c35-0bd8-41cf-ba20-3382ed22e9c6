package org.thingsboard.server.dao.sql.smartProduction.circuit;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.smartProduction.circuit.CircuitTaskItem;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.circuit.CircuitTaskItemCompleteRequest;
import org.thingsboard.server.dao.util.imodel.query.smartProduction.circuit.CircuitTaskItemPageRequest;

import java.util.List;

@Mapper
public interface CircuitTaskItemMapper extends BaseMapper<CircuitTaskItem> {
    IPage<CircuitTaskItem> findByPage(CircuitTaskItemPageRequest request);

    boolean update(CircuitTaskItem entity);

    int saveAll(List<CircuitTaskItem> list);

    int updateAll(List<CircuitTaskItem> list);

    boolean complete(CircuitTaskItemCompleteRequest req);

    CircuitTaskItem buildBySetting(@Param("setting") String setting, @Param("mainId") String mainId);

    boolean isComplete(String id);

    boolean markParentComplete(String id);

}
