package org.thingsboard.server.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardErrorCode;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.deviceType.DeviceTypeService;
import org.thingsboard.server.dao.model.sql.deviceManage.DeviceType;
import org.thingsboard.server.dao.util.imodel.query.GeneralDeviceUtil;
import org.thingsboard.server.dao.util.imodel.query.device.DeviceTypePageRequest;
import org.thingsboard.server.dao.util.imodel.query.device.DeviceTypeSaveRequest;
import org.thingsboard.server.utils.imodel.annotations.IStarController;

import java.util.List;

@IStarController
@RequestMapping("/api/deviceType")
public class DeviceTypeController extends BaseController {
    @Autowired
    private DeviceTypeService deviceTypeService;


    //<editor-fold desc="CRUD">
    @GetMapping
    public IPage<DeviceType> findAllConditional(DeviceTypePageRequest request) {
        return deviceTypeService.findAllConditional(request);
    }

    @GetMapping("/tree")
    public List<DeviceType> findTree(String type) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
//        return deviceTypeService.findAllStructure(type, tenantId);
        return deviceTypeService.findAllStructure(tenantId);
    }

    @PostMapping
    public DeviceType save(@RequestBody DeviceTypeSaveRequest req) throws ThingsboardException {
        GeneralDeviceUtil.checkDeviceSerialId(req, deviceTypeService);
        return deviceTypeService.save(req);
    }

    @PatchMapping("/{id}")
    public boolean edit(@RequestBody DeviceTypeSaveRequest req, @PathVariable String id) throws ThingsboardException {
        if (req.getParentId() != null || req.getSerialId() != null) {
            if (req.getParentId() == null)
                req.setParentId(deviceTypeService.getParentId(id));

            GeneralDeviceUtil.checkDeviceSerialId(req, deviceTypeService);
        }
        return deviceTypeService.update(req.unwrap(id));
    }

    @DeleteMapping("/{id}")
    public boolean delete(@PathVariable String id) throws ThingsboardException {
        // 验证删除规则
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        if (!deviceTypeService.canBeDelete(id, tenantId))
            throw new ThingsboardException("其下有设备类型或设备属性或设备，无法执行删除操作", ThingsboardErrorCode.GENERAL);

        return deviceTypeService.delete(id);
    }
    //</editor-fold>


}
