package org.thingsboard.server.dao.sql.dma;

import org.springframework.data.jpa.repository.JpaRepository;
import org.thingsboard.server.dao.model.sql.dma.JinzhouDmaBookEntity;

import java.util.List;

public interface JinzhouDmaBookRepository extends JpaRepository<JinzhouDmaBookEntity, String> {

    List<JinzhouDmaBookEntity> findByTenantId(String tenantId);

    List<JinzhouDmaBookEntity> findByIdIn(List<String> ids);
}
