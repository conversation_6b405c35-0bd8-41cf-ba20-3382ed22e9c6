package org.thingsboard.server.dao.gis;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.model.sql.Tb3DData;
import org.thingsboard.server.dao.sql.gis.Tb3DDataRepository;

@Slf4j
@Service
public class Tb3DDataServiceImpl implements Tb3DDataService {

    @Autowired
    private Tb3DDataRepository tb3DDataRepository;

    @Override
    public Tb3DData findOne(String code, TenantId tenantId) {
        return tb3DDataRepository.findByCodeAndTenantId(code, UUIDConverter.fromTimeUUID(tenantId.getId()));
    }

    @Override
    public void save(Tb3DData entity) {
        tb3DDataRepository.save(entity);
    }
}
