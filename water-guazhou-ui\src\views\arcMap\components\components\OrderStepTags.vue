<template>
  <div class="step-wrapper">
    <div
      v-for="(step, index) in OrderStatus"
      :key="index"
      class="step-item"
    >
      <Button
        :config="step"
        :type="step.type"
      />
      <el-icon
        v-if="index !== OrderStatus.length - 1"
        style="width: 40px; height: 100%; font-size: 24px"
      >
        <svg
          viewBox="0 0 1024 1024"
          xmlns="http://www.w3.org/2000/svg"
          data-v-78e17ca8=""
        >
          <path
            fill="currentColor"
            d="M754.752 480H160a32 32 0 1 0 0 64h594.752L521.344 777.344a32 32 0 0 0 45.312 45.312l288-288a32 32 0 0 0 0-45.312l-288-288a32 32 0 1 0-45.312 45.312L754.752 480z"
          ></path>
        </svg>
      </el-icon>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { WorkOrderStep, complaint } from '@/views/workorder/config'

const props = defineProps<{
  config: {
    status: string
    statusName: string
    colum: boolean
  }
}>()

const OrderStatus = computed<IButton[]>(() => {
  let statusOptions = props.config.colum ? WorkOrderStep() : complaint()

  if (props.config.status === 'REJECTED') {
    statusOptions = statusOptions.filter(item => item.value !== 'APPROVED')
  }
  if (props.config.status === 'APPROVED') {
    statusOptions = statusOptions.filter(item => item.value !== 'REJECTED')
  }
  const curIndex = statusOptions.findIndex(
    item => item.value === props.config.status
  )
  if (curIndex === -1) {
    return [{ perm: true, text: props.config.statusName, type: 'info' }]
  }
  return statusOptions.map((item, i) => {
    return {
      perm: true,
      text: item.label,
      type: curIndex > i ? 'primary' : curIndex === i ? 'success' : 'info'
    }
  })
})
</script>
<style lang="scss" scoped>
.step-wrapper {
  display: flex;
  justify-content: flex-start;
  align-items: cecenter;
  padding-left: 20px;
  .step-item {
    display: flex;
    align-items: cecenter;
  }
}
</style>
