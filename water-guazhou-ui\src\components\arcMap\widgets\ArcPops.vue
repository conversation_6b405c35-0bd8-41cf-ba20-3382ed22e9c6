<template>
  <div class="pop-container">
    <ArcPop
      v-for="pop in pops"
      :key="pop.id"
      :ref="'refPop' + pop.id"
      :latitude="pop.latitude"
      :longitude="pop.longitude"
      :x="pop.x"
      :y="pop.y"
      :mark-url="pop.symbolConfig?.url"
      :mark-width="pop.symbolConfig?.width"
      :mark-height="pop.symbolConfig?.height"
    >
      <slot :config="pop"></slot>
    </ArcPop>
  </div>
</template>
<script lang="ts" setup>
const props = defineProps<{
  pops?: IArcPopConfig[]
}>()
const view: __esri.MapView | undefined = inject('view')
const { proxy }: any = getCurrentInstance()
const setPopPosition = (popConfig: IArcPopConfig) => {
  const pop = proxy.$refs['refPop' + popConfig.id]
  if (!pop?.[0]) return
  pop[0]?.setPosition(view)
}
const togglePopById = (id: string, flag?: boolean) => {
  const pop = proxy.$refs['refPop' + id]
  if (!pop?.[0]) return
  pop[0]?.toggle(flag)
  nextTick().then(() => {
    pop[0]?.setPosition(view)
  })
}
const togglePop = (popConfig: IArcPopConfig, flag?: boolean) => {
  const id = popConfig.id
  if (id === undefined) return
  togglePopById(id, flag)
}
const closeAllPop = () => {
  props.pops?.map(item => {
    const id = item?.id
    if (!id) return
    const pop = proxy.$refs['refPop' + id]
    pop?.length && pop[0]?.toggle(false)
  })
}
defineExpose({
  togglePop,
  togglePopById,
  closeAllPop,
  setPopPosition
})
onMounted(() => {
  view?.watch('extent', () => {
    props.pops?.map(item => {
      const pop = proxy.$refs['refPop' + item.id]
      pop[0]?.setPosition(view)
    })
  })
})
</script>
<style lang="scss" scoped></style>
