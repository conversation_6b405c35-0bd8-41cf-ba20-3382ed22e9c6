package org.thingsboard.server.dao.model.sql.dma;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;
import java.util.Date;

/**
 * DMA分析
 */
@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.DMA_PRESSURE_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class DmaPressureEntity {
    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.DMA_PRESSURE_PARTITION_ID)
    private String partitionId;

    @Column(name = ModelConstants.DMA_PRESSURE_DEVICE_ID)
    private String deviceId;

    @Column(name = ModelConstants.DMA_PRESSURE_PRESSURE)
    private Double pressure;

    @Column(name = ModelConstants.DMA_PRESSURE_CREATE_TIME)
    private Date createTime;

    @Column(name = ModelConstants.DMA_PRESSURE_TENANT_ID)
    private String tenantId;

}
