import{m,c,d as f,r as v,o as d,a0 as g,l as p,ay as h,g as A,n as _,q as y,i as w,C as O}from"./index-r0dFAfgr.js";const T=a=>m({url:"/api/alarmV2/alarmCenter/alarmRank",method:"get",params:a}),k=a=>m({url:"/api/alarm/trend",method:"get",params:a}),C=a=>m({url:"/api/alarm/countAlarm/overview",method:"get",params:a}),x=a=>m({url:"/api/alarm/countAlarmByProject",method:"get",params:a}),B=()=>{const a=c([]),l=async o=>{const n=await T(o);a.value=n.data},s=c([]),u=async o=>{const n=await k(o);s.value=n.data},i=c({alarmTotal:0,alarmDeviceTotal:0,unconfirmTotal:0,unremoveTotal:0}),e=async o=>{const n=await C(o);i.value=n.data},t=c({提示:0,次要:0,重要:0,紧急:0});return{AlarmRank:a,getAlarmRank:l,AlarmTrend:s,getAlarmTrend:u,AlarmOverview:i,getAlarmOverview:e,AlarmPriority:t,getAlarmPriority:async o=>{const n=await x(o);t.value=n.data}}},P=B,R={class:"chart-wrapper"},S=f({__name:"BJTJ",setup(a){const l=v({materialOption:null,diameterOption:null}),s=e=>({tooltip:{trigger:"item"},legend:{type:"scroll",orient:"vertical",right:0,top:"center",pageIconColor:"#fff",pageTextStyle:{color:"#fff"},textStyle:{color:"#fff",rich:{name:{align:"left",width:170,fontSize:12},value:{align:"right",width:40,fontSize:12,color:"#00ff00"}}},data:e.map(t=>t.name),formatter(t){if(e&&e.length){for(let r=0;r<e.length;r++)if(t===e[r].name)return"{name| "+t+"}{value| "+e[r].value+"}"}}},series:[{type:"pie",radius:["40%","60%"],center:["20%","50%"],avoidLabelOverlap:!1,label:{show:!1,position:"center"},labelLine:{show:!1},data:e}]}),{getAlarmRank:u,AlarmRank:i}=P();return d(()=>{var e;u({projectId:(e=g().selectedProject)==null?void 0:e.value,start:p().startOf("year").valueOf(),end:p().valueOf()}).then(()=>{l.diameterOption=s(i.value.map(t=>({name:t.deviecName,value:t.alarm})))})}),(e,t)=>{const r=h("VChart");return A(),_("div",R,[y(r,{option:w(l).diameterOption},null,8,["option"])])}}}),G=O(S,[["__scopeId","data-v-6372651e"]]);export{G as default};
