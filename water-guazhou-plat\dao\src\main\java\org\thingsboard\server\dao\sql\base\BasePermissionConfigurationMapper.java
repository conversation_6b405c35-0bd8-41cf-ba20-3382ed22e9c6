package org.thingsboard.server.dao.sql.base;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.base.BasePermissionConfiguration;
import org.thingsboard.server.dao.util.imodel.query.base.BasePermissionConfigurationPageRequest;

import java.util.List;


/**
 * 平台管理-权限配置Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@Mapper
public interface BasePermissionConfigurationMapper {
    /**
     * 查询平台管理-权限配置
     *
     * @param id 平台管理-权限配置主键
     * @return 平台管理-权限配置
     */
    public BasePermissionConfiguration selectBasePermissionConfigurationById(String id);

    /**
     * 查询平台管理-权限配置列表
     *
     * @param basePermissionConfiguration 平台管理-权限配置
     * @return 平台管理-权限配置集合
     */
    public IPage<BasePermissionConfiguration> selectBasePermissionConfigurationList(BasePermissionConfigurationPageRequest basePermissionConfiguration);

    /**
     * 新增平台管理-权限配置
     *
     * @param basePermissionConfiguration 平台管理-权限配置
     * @return 结果
     */
    public int insertBasePermissionConfiguration(BasePermissionConfiguration basePermissionConfiguration);

    /**
     * 修改平台管理-权限配置
     *
     * @param basePermissionConfiguration 平台管理-权限配置
     * @return 结果
     */
    public int updateBasePermissionConfiguration(BasePermissionConfiguration basePermissionConfiguration);

    /**
     * 删除平台管理-权限配置
     *
     * @param id 平台管理-权限配置主键
     * @return 结果
     */
    public int deleteBasePermissionConfigurationById(String id);

    /**
     * 批量删除平台管理-权限配置
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBasePermissionConfigurationByIds(@Param("array") List<String> ids);
}
