package org.thingsboard.server.dao.util.imodel.query.purchase;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.purchase.Contract;
import org.thingsboard.server.dao.util.imodel.query.ComplexSaveRequest;
import org.thingsboard.server.dao.util.imodel.query.StringSetter;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

import java.util.Date;

@Getter
@Setter
public class ContractSaveRequest extends ComplexSaveRequest<Contract, ContractDetailSaveRequest> {

    // 所属采购单ID
    private String purchaseId;

    // 合同编号
    @NotNullOrEmpty
    private String code;

    // 合同标题
    @NotNullOrEmpty
    private String title;

    // 合同文件
    private String file;

    protected Contract build() {
        Contract entity = new Contract();
        entity.setCreator(currentUserUUID());
        entity.setCreateTime(new Date());
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    protected Contract update(String id) {
        Contract entity = new Contract();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(Contract entity) {
        entity.setPurchaseId(purchaseId);
        entity.setCode(code);
        entity.setTitle(title);
        entity.setFile(file);
    }

    @Override
    protected StringSetter<ContractDetailSaveRequest> parentSetter() {
        return ContractDetailSaveRequest::setMainId;
    }
}