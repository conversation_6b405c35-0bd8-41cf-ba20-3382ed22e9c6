/**
 * Copyright © 2016-2019 The Thingsboard Authors
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.server.dao.alarm;

import com.google.common.util.concurrent.ListenableFuture;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.User;
import org.thingsboard.server.common.data.VO.AlarmLinkedUser;
import org.thingsboard.server.common.data.alarm.AlarmJsonId;
import org.thingsboard.server.common.data.alarm.AttrAlarmJson;
import org.thingsboard.server.common.data.id.DeviceId;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.id.UserId;
import org.thingsboard.server.common.data.relation.EntityRelation;
import org.thingsboard.server.common.data.relation.RelationTypeGroup;
import org.thingsboard.server.dao.extraUser.ExtraUserService;
import org.thingsboard.server.dao.model.sql.ExtraUser;
import org.thingsboard.server.dao.project.ProjectRelationService;
import org.thingsboard.server.dao.relation.RelationService;
import org.thingsboard.server.dao.user.UserService;

import java.util.*;
import java.util.concurrent.ScheduledThreadPoolExecutor;

import static org.thingsboard.server.common.data.CacheConstants.ALARM_JSON_CACHE;

@Service
public class AlarmJsonServiceImpl implements AlarmJsonService {

    @Autowired
    private AlarmJsonDao alarmJsonDao;

    @Autowired
    private ProjectRelationService projectRelationService;

    @Autowired
    private RelationService relationService;

    @Autowired
    private UserService userService;

    @Autowired
    private ExtraUserService extraUserService;

    @Override
    @Caching(evict = {@CacheEvict(cacheNames = ALARM_JSON_CACHE, key = "{#attrAlarmJson.deviceId}"),
            @CacheEvict(cacheNames = ALARM_JSON_CACHE, key = "{#attrAlarmJson.deviceId, #attrAlarmJson.attribute}"),
            @CacheEvict(cacheNames = ALARM_JSON_CACHE, key = "{#attrAlarmJson.tenantId}")})
    public AttrAlarmJson createOrUpdateAlarmJson(AttrAlarmJson attrAlarmJson) {
        AttrAlarmJson save = alarmJsonDao.save(attrAlarmJson);
        // 保存项目与报警设置的关系
        projectRelationService.mountEntityToProject(DataConstants.ProjectRelationEntityType.ALARM_SETTING.name(),
                attrAlarmJson.getProjectId(), Collections.singletonList(UUIDConverter.fromTimeUUID(save.getUuidId())));
        return save;
    }

    @Override
//    @Cacheable(cacheNames = ALARM_JSON_CACHE, key = "{#alarmJsonId}")
    public ListenableFuture<AttrAlarmJson> findAlarmByIdAsync(AlarmJsonId alarmJsonId) {
        return alarmJsonDao.findAlarmByIdAsync(alarmJsonId);
    }

    @Override
    public ListenableFuture<List<AttrAlarmJson>> findByParams(TenantId tenantId, DeviceId deviceId, String attribute) {
        return alarmJsonDao.findByParams(tenantId, deviceId, attribute);
    }

    @Override
    @Caching(evict = {
//            @CacheEvict(cacheNames = ALARM_JSON_CACHE, key = "{#alarmJson.id}"),
            @CacheEvict(cacheNames = ALARM_JSON_CACHE, key = "{#alarmJson.tenantId}"),
            @CacheEvict(cacheNames = ALARM_JSON_CACHE, key = "{#alarmJson.deviceId,#alarmJson.attribute}"),
            @CacheEvict(cacheNames = ALARM_JSON_CACHE, key = "{#alarmJson.deviceId}")
    })
    public void deleteAlarmJsonById(AttrAlarmJson alarmJson) {
        alarmJsonDao.removeById(alarmJson.getUuidId());
    }

    @Override
    @Cacheable(cacheNames = ALARM_JSON_CACHE, key = "{#deviceId, #attribute}")
    public ListenableFuture<List<AttrAlarmJson>> findByDeviceAndAttribute(DeviceId deviceId, String attribute) {
        return alarmJsonDao.findByDeviceAndAttribute(deviceId, attribute);
    }

    @Override
//    @Cacheable(cacheNames = ALARM_JSON_CACHE, key = "{#tenantId}")
    public ListenableFuture<List<AttrAlarmJson>> findByTenant(TenantId tenantId) {
        return alarmJsonDao.findByTenant(tenantId);
    }

    @Override
    @Cacheable(cacheNames = ALARM_JSON_CACHE, key = "{#deviceId}")
    public List<AttrAlarmJson> findByDevice(DeviceId deviceId) {
        return alarmJsonDao.findByDevice(deviceId);
    }

    @Override
    public ListenableFuture<List<AttrAlarmJson>> findByDeviceAndPropAndLevel(DeviceId deviceId, String prop, String level) {
        return alarmJsonDao.findByDeviceAndPropAndLevel(deviceId, prop, level);
    }

    @Override
    public List<AttrAlarmJson> findByProjectId(String projectId) {
        return alarmJsonDao.findByProjectId(projectId);
    }

    @Override
    public AttrAlarmJson findAlarmById(AlarmJsonId alarmJsonId) {
        return alarmJsonDao.findById(alarmJsonId.getId());
    }

    @Override
    public boolean linkedUser(List<AlarmLinkedUser> alarmLinkedUsers, String alarmJsonId, TenantId tenantId, RelationTypeGroup relationTypeGroup) {
        relationService.deleteEntityRelationsAsync(tenantId, new AlarmJsonId(UUIDConverter.fromString(alarmJsonId)), relationTypeGroup).addListener(() -> {
            alarmLinkedUsers.forEach(alarmLinkedUser -> {
                try {
                    EntityRelation entityRelation = new EntityRelation();
                    entityRelation.setFrom(new AlarmJsonId(UUIDConverter.fromString(alarmJsonId)));
                    if (alarmLinkedUser.getUserId() != null) {
                        entityRelation.setTo(alarmLinkedUser.getUserId());
                        entityRelation.setTypeGroup(RelationTypeGroup.USER);
                    } else {
                        entityRelation.setTo(new UserId(UUIDConverter.fromString(alarmLinkedUser.getExtraUserId())));
                        entityRelation.setTypeGroup(RelationTypeGroup.EXTAR_USER);
                    }
                    if (alarmLinkedUser.isSendEmail()) {
                        entityRelation.setType(DataConstants.ALARM_SEND_EMAIL);
                        relationService.saveRelation(tenantId, entityRelation);
                    }
                    if (alarmLinkedUser.isSendSms()) {
                        entityRelation.setType(DataConstants.ALARM_SEND_SMS);
                        relationService.saveRelation(tenantId, entityRelation);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            });
        }, new ScheduledThreadPoolExecutor(1));
        return true;
    }

    @Override
    public List<AlarmLinkedUser> getAllLinkedUser(TenantId tenantId, String alarmJsonId) {
        Map<UserId, AlarmLinkedUser> alarmLinkedUsers = new HashMap<>();
        List<User> users = userService.findUserByTenant(tenantId);
        users.forEach(user -> {
            alarmLinkedUsers.put(user.getId(), AlarmLinkedUser.builder()
                    .email(user.getEmail())
                    .phone(user.getPhone())
                    .userId(user.getId())
                    .userName(user.getFirstName() + "(" + user.getName() + ")")
                    .tenantId(user.getTenantId())
                    .build());
        });

        return getAlarmLinkedUsers(alarmJsonId, alarmLinkedUsers);
    }

    @Override
    public List<AlarmLinkedUser> getAlarmLinkedUser(String alarmJsonId) {
        Map<UserId, AlarmLinkedUser> alarmLinkedUsers = new HashMap<>();
        return getAlarmLinkedUsers(alarmJsonId, alarmLinkedUsers);
    }

    @Override
    public List<AlarmLinkedUser> getAllLinkedExtraUser(TenantId tenantId, String alarmJsonId) {
        Map<String, AlarmLinkedUser> alarmLinkedUsers = new HashMap<>();
        List<ExtraUser> extraUsers = extraUserService.findByTenant(UUIDConverter.fromTimeUUID(tenantId.getId()));
        extraUsers.forEach(user -> {
            alarmLinkedUsers.put(user.getId(), AlarmLinkedUser.builder()
                    .email(user.getEmail())
                    .phone(user.getPhone())
                    .extraUserId(user.getId())
                    .userName(user.getName())
                    .tenantId(tenantId)
                    .build());
        });
        return getAlarmLinkedExtraUsers(alarmJsonId, alarmLinkedUsers);
    }


    private List<AlarmLinkedUser> getAlarmLinkedExtraUsers(String alarmJsonId, Map<String, AlarmLinkedUser> alarmLinkedUsers) {
        List<AlarmLinkedUser> result = new ArrayList<>();
        List<ExtraUser> emailUsers = alarmJsonDao.getAlarmLinkedExtraUserBySendMethod(alarmJsonId, DataConstants.ALARM_SEND_EMAIL);
        List<ExtraUser> smsUsers = alarmJsonDao.getAlarmLinkedExtraUserBySendMethod(alarmJsonId, DataConstants.ALARM_SEND_SMS);
        emailUsers.forEach(user -> {
            if (alarmLinkedUsers.containsKey(user.getId())) {
                alarmLinkedUsers.get(user.getId()).setSendEmail(true);
            } else {
                alarmLinkedUsers.put(user.getId(), AlarmLinkedUser.builder()
                        .email(user.getEmail())
                        .phone(user.getPhone())
                        .extraUserId(user.getId())
                        .userName(user.getName())
                        .sendEmail(true)
                        .tenantId(new TenantId(UUIDConverter.fromString(user.getTenantId())))
                        .build());
            }
        });
        smsUsers.forEach(user -> {
            if (alarmLinkedUsers.containsKey(user.getId())) {
                alarmLinkedUsers.get(user.getId()).setSendSms(true);
            } else {
                alarmLinkedUsers.put(user.getId(), AlarmLinkedUser.builder()
                        .email(user.getEmail())
                        .phone(user.getPhone())
                        .extraUserId(user.getId())
                        .userName(user.getName())
                        .sendSms(true)
                        .tenantId(new TenantId(UUIDConverter.fromString(user.getTenantId())))
                        .build());
            }
        });
        alarmLinkedUsers.values().forEach(alarmLinkedUser -> result.add(alarmLinkedUser));
        return result;
    }


    private List<AlarmLinkedUser> getAlarmLinkedUsers(String alarmJsonId, Map<UserId, AlarmLinkedUser> alarmLinkedUsers) {
        List<AlarmLinkedUser> result = new ArrayList<>();
        List<User> emailUsers = alarmJsonDao.getAlarmLinkedUserBySendMethod(alarmJsonId, DataConstants.ALARM_SEND_EMAIL);
        List<User> smsUsers = alarmJsonDao.getAlarmLinkedUserBySendMethod(alarmJsonId, DataConstants.ALARM_SEND_SMS);
        emailUsers.forEach(user -> {
            if (alarmLinkedUsers.containsKey(user.getId())) {
                alarmLinkedUsers.get(user.getId()).setSendEmail(true);
            } else {
                alarmLinkedUsers.put(user.getId(), AlarmLinkedUser.builder()
                        .email(user.getEmail())
                        .phone(user.getPhone())
                        .userId(user.getId())
                        .userName(user.getFirstName() + "(" + user.getName() + ")")
                        .sendEmail(true)
                        .tenantId(user.getTenantId())
                        .build());
            }
        });
        smsUsers.forEach(user -> {
            if (alarmLinkedUsers.containsKey(user.getId())) {
                alarmLinkedUsers.get(user.getId()).setSendSms(true);
            } else {
                alarmLinkedUsers.put(user.getId(), AlarmLinkedUser.builder()
                        .email(user.getEmail())
                        .phone(user.getPhone())
                        .userId(user.getId())
                        .userName(user.getFirstName() + "(" + user.getName() + ")")
                        .sendSms(true)
                        .build());
            }
        });
        alarmLinkedUsers.values().forEach(alarmLinkedUser -> result.add(alarmLinkedUser));
        return result;
    }


}
