package org.thingsboard.server.dao.sql.smartOperation.construction.project;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.smartOperation.device.SoDeviceItem;
import org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemScope;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoDeviceItemPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartOperation.construction.project.SoDeviceItemSaveRequest;

import java.util.List;

@Mapper
public interface SoDeviceItemMapper extends BaseMapper<SoDeviceItem> {
    IPage<SoDeviceItem> findByPage(SoDeviceItemPageRequest request);

    boolean update(SoDeviceItem entity);

    boolean updateFully(SoDeviceItem entity);

    boolean updateAllFully(List<SoDeviceItem> entity);

    int saveAll(List<SoDeviceItem> entities);

    int remove(@Param("id") String id);

    boolean canBeDelete(String id);

    boolean save(SoDeviceItem item);

    List<SoDeviceItemSaveRequest> selectBatchMaxRestCount(@Param("list") List<SoDeviceItemSaveRequest> request, @Param("scope") SoGeneralSystemScope scope);

}
