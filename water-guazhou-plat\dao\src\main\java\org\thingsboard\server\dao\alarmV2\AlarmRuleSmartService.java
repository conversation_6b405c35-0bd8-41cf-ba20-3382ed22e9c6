package org.thingsboard.server.dao.alarmV2;

import org.thingsboard.server.dao.model.DTO.AlarmRuleSmartSaveDTO;
import org.thingsboard.server.dao.model.request.AlarmRuleSmartRequest;
import org.thingsboard.server.dao.model.sql.alarmV2.AlarmRuleSmart;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.List;

public interface AlarmRuleSmartService {

    IstarResponse save(AlarmRuleSmartSaveDTO entity, String tenantId);

    IstarResponse findList(AlarmRuleSmartRequest request, String tenantId);

    List<AlarmRuleSmart> findByStationAttrList(List<String> stationIdList, List<String> attrList);

    IstarResponse delete(List<String> idList);

    IstarResponse findListV2(AlarmRuleSmartRequest request, String tenantId);
}
