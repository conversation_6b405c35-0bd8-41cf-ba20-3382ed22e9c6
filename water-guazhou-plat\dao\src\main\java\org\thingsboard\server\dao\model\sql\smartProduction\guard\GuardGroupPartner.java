package org.thingsboard.server.dao.model.sql.smartProduction.guard;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseUsername;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

@Getter
@Setter
@ResponseEntity
@TableName("guard_group_partner")
public class GuardGroupPartner {
    // id
    private String id;

    // 班组id
    private String groupId;

    // 用户id
    @ParseUsername(withDepartment = true)
    private String userId;

    // 岗位
    private String roleName;


}
