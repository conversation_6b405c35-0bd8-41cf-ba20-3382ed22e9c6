package org.thingsboard.server.controller.base;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.thingsboard.server.dao.base.IBasePermissionConfigurationService;
import org.thingsboard.server.dao.model.sql.base.BasePermissionConfiguration;
import org.thingsboard.server.dao.util.imodel.query.base.BasePermissionConfigurationPageRequest;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;
import org.thingsboard.server.service.aspect.annotation.MonitorPerformance;

import java.util.List;

/**
 * 平台管理-权限配置Controller
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@Api(tags = "平台管理-权限配置")
@RestController
@RequestMapping("api/base/permission/configuration")
public class BasePermissionConfigurationController extends BaseController {

    @Autowired
    private IBasePermissionConfigurationService basePermissionConfigurationService;

    /**
     * 查询平台管理-权限配置列表
     */
    @MonitorPerformance(description = "平台管理-查询权限配置列表接口")
    @ApiOperation(value = "查询权限配置列表")
    @GetMapping("/list")
    public IstarResponse list(BasePermissionConfigurationPageRequest basePermissionConfiguration) {
        return IstarResponse.ok(basePermissionConfigurationService.selectBasePermissionConfigurationList(basePermissionConfiguration));
    }

    /**
     * 获取平台管理-权限配置详细信息
     */
    @MonitorPerformance(description = "平台管理-查询权限配置详情接口")
    @ApiOperation(value = "获取权限配置详情")
    @GetMapping(value = "/getDetail")
    public IstarResponse getInfo(String id) {
        return IstarResponse.ok(basePermissionConfigurationService.selectBasePermissionConfigurationById(id));
    }

    /**
     * 新增平台管理-权限配置
     */
    @MonitorPerformance(description = "平台管理-新增权限配置接口")
    @ApiOperation(value = "新增权限配置")
    @PostMapping("/add")
    public IstarResponse add(@RequestBody BasePermissionConfiguration basePermissionConfiguration) {
        return IstarResponse.ok(basePermissionConfigurationService.insertBasePermissionConfiguration(basePermissionConfiguration));
    }

    /**
     * 修改平台管理-权限配置
     */
    @MonitorPerformance(description = "平台管理-修改权限配置接口")
    @ApiOperation(value = "修改权限配置")
    @PostMapping("/edit")
    public IstarResponse edit(@RequestBody BasePermissionConfiguration basePermissionConfiguration) {
        return IstarResponse.ok(basePermissionConfigurationService.updateBasePermissionConfiguration(basePermissionConfiguration));
    }

    /**
     * 删除平台管理-权限配置
     */
    @MonitorPerformance(description = "平台管理-删除权限配置接口")
    @ApiOperation(value = "删除权限配置")
    @DeleteMapping("/deleteIds")
    public IstarResponse remove(@RequestBody List<String> ids) {
        return IstarResponse.ok(basePermissionConfigurationService.deleteBasePermissionConfigurationByIds(ids));
    }
}
