<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.maintainCircuit.circuit.CircuitTaskPointMapper">

    <select id="getList" resultType="org.thingsboard.server.dao.model.sql.maintainCircuit.circuit.CircuitTaskPoint">

        select a.*
        from tb_device_circuit_task_point a
            where a.main_id = #{mainId}
            order by a.create_time desc

    </select>
</mapper>