<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartOperation.construction.project.SoProjectArchiveMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->
        archive.id,
        project_code,
        project.name       as project_name,
        project.start_time as project_start_time,
        archive.archive_time,
        archive.remark,
        archive.attachments,
        archive.creator,
        archive.create_time,
        archive.update_user,
        archive.update_time,
        archive.tenant_id
        <!--@sql from so_project_archive archive, so_project project  -->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.smartOperation.project.SoProjectArchive">
        <result column="id" property="id"/>
        <result column="project_code" property="projectCode"/>
        <result column="project_name" property="projectName"/>
        <result column="project_start_time" property="projectStartTime"/>
        <result column="archive_time" property="archiveTime"/>
        <result column="remark" property="remark"/>
        <result column="attachments" property="attachments"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <select id="findByPage" resultMap="BaseResultMap">
        <bind name="processingStatus"
              value="@org.thingsboard.server.dao.model.sql.smartOperation.SoGeneralTaskStatus@PROCESSING"/>
        select
        <include refid="Base_Column_List"/>
        from so_project project
                 left join so_project_archive archive
                           on project.code = archive.project_code and project.tenant_id = archive.tenant_id
        <where>
            <if test="projectCode != null and projectCode != ''">
                and project_code = #{projectCode}
            </if>
            <if test="projectName != null and projectName != ''">
                and project.name like '%' || #{projectName} || '%'
            </if>
            and (select count(1) = 0
                 from so_construction_task_info
                 where construction_code in
                       (select code from so_construction where project_code = project.code and tenant_id = #{tenantId})
                   and status = #{processingStatus}
                   and tenant_id = #{tenantId})
            and project.tenant_id = #{tenantId}
        </where>
        order by project.create_time desc
    </select>


    <update id="update">
        update so_project_archive
        <set>
            <if test="archiveTime != null">
                archive_time = #{archiveTime},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="attachments != null">
                attachments = #{attachments},
            </if>
        </set>
        where id = #{id}
    </update>

    <update id="updateFully">
        update so_project_archive
        set archive_time = #{archiveTime},
            remark       = #{remark},
            attachments  = #{attachments}
        where id = #{id}
    </update>

    <insert id="save">
        INSERT INTO so_project_archive(id,
                                       project_code,
                                       archive_time,
                                       remark,
                                       attachments,
                                       creator,
                                       create_time,
                                       update_user,
                                       update_time,
                                       tenant_id)
        VALUES (#{id},
                #{projectCode},
                #{archiveTime},
                #{remark},
                #{attachments},
                #{creator},
                #{createTime},
                #{updateUser},
                #{updateTime},
                #{tenantId})
    </insert>

    <select id="canArchive" resultType="boolean">
        <bind name="processingStatus"
              value="@org.thingsboard.server.dao.model.sql.smartOperation.SoGeneralTaskStatus@PROCESSING"/>
        select count(1) = 0
        from so_construction_task_info
        where construction_code in
              (select code from so_construction where project_code = #{projectCode} and tenant_id = #{tenantId})
          and status = #{processingStatus}
          and tenant_id = #{tenantId}
    </select>

    <resultMap id="SoProjectFilesResultMap"
               type="org.thingsboard.server.dao.model.sql.smartOperation.project.SoProjectFiles">
        <result column="project" property="project"/>
        <result column="bidding" property="bidding"/>
        <result column="construction" property="construction"/>
        <result column="construction_design" property="constructionDesign"/>
        <result column="construction_design_amend" property="constructionDesignAmend"/>
        <result column="construction_estimate" property="constructionEstimate"/>
        <result column="construction_contract" property="constructionContract"/>
        <result column="construction_contract_amend" property="constructionContractAmend"/>
        <result column="construction_visa" property="constructionVisa"/>
        <result column="construction_expense" property="constructionExpense"/>
        <!--        <result column="construction_apply" property="constructionApply"/>-->
        <result column="construction_accept" property="constructionAccept"/>
        <result column="construction_settlement" property="constructionSettlement"/>
        <result column="construction_archive" property="constructionArchive"/>
        <result column="project_accept" property="projectAccept"/>
        <result column="project_settlement" property="projectSettlement"/>
    </resultMap>
    <select id="findFilesByConstructionCode"
            resultMap="SoProjectFilesResultMap">
        <!--@formatter:off-->
        select
        (select attachments from so_project where tenant_id = #{tenantId} and code = #{projectCode}) project,

        (select attachments from so_bidding where tenant_id = #{tenantId} and project_code = #{projectCode}) bidding,

        (select string_agg(attachments, ',') from so_construction where tenant_id = #{tenantId} and project_code = #{projectCode}) construction,

        (select string_agg(attachments, ',') from so_construction_design where tenant_id = #{tenantId} and construction_code in
            (select project_code from so_construction where tenant_id = #{tenantId} and project_code = #{projectCode})) construction_design,

        (select string_agg(attachments, ',') from so_construction_design_amend where tenant_id = #{tenantId} and design_code in
            (select code from so_construction_design where tenant_id = #{tenantId} and construction_code in (
                (select project_code from so_construction where tenant_id = #{tenantId} and project_code = #{projectCode})))) construction_design_amend,

        (select string_agg(attachments, ',') from so_construction_estimate where tenant_id = #{tenantId} and construction_code in
            (select project_code from so_construction where tenant_id = #{tenantId} and project_code = #{projectCode})) construction_estimate,

        (select string_agg(attachments, ',') from so_construction_contract where tenant_id = #{tenantId} and construction_code in
            (select project_code from so_construction where tenant_id = #{tenantId} and project_code = #{projectCode})) construction_contract,

        (select string_agg(attachments, ',') from so_construction_contract_amend where tenant_id = #{tenantId} and contract_code in
            (select code from so_construction_contract where tenant_id = #{tenantId} and construction_code in (
                (select project_code from so_construction where tenant_id = #{tenantId} and project_code = #{projectCode})))) construction_contract_amend,

        (select string_agg(attachments, ',') from so_construction_visa where tenant_id = #{tenantId} and construction_code in
            (select project_code from so_construction where tenant_id = #{tenantId} and project_code = #{projectCode})) construction_visa,

        (select string_agg(attachments, ',') from so_construction_expense where tenant_id = #{tenantId} and construction_code in
            (select project_code from so_construction where tenant_id = #{tenantId} and project_code = #{projectCode})) construction_expense,

<!--        (select string_agg(attachments, ',') from so_construction_apply where tenant_id = #{tenantId} and construction_code in-->
<!--            (select project_code from so_construction where tenant_id = #{tenantId} and project_code = #{projectCode})) construction_apply,-->

        (select string_agg(attachments, ',') from so_construction_accept where tenant_id = #{tenantId} and construction_code in
            (select project_code from so_construction where tenant_id = #{tenantId} and project_code = #{projectCode})) construction_accept,

        (select string_agg(attachments, ',') from so_construction_settlement where tenant_id = #{tenantId} and construction_code in
            (select project_code from so_construction where tenant_id = #{tenantId} and project_code = #{projectCode})) construction_settlement,

        (select string_agg(attachments, ',') from so_construction_archive where tenant_id = #{tenantId} and construction_code in
            (select project_code from so_construction where tenant_id = #{tenantId} and project_code = #{projectCode})) construction_archive,

        (select string_agg(attachments, ',') from so_project_accept where tenant_id = #{tenantId} and project_code = #{projectCode}) project_accept,

        (select string_agg(attachments, ',') from so_project_settlement where tenant_id = #{tenantId} and project_code = #{projectCode}) project_settlement
        <!--@formatter:on-->
    </select>

    <select id="getIdByProjectCodeAndTenantId" resultType="java.lang.String">
        select id
        from so_project_archive
        where project_code = #{projectCode}
          and tenant_id = #{tenantId}
    </select>
</mapper>