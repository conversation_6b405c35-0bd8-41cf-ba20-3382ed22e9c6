package org.thingsboard.server.dao.auditRecord;

import org.thingsboard.server.dao.auditRecord.BO.AuditRecordBO;
import org.thingsboard.server.dao.model.sql.AuditRecordEntity;

import java.util.Map;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2021-01-19
 */
public interface AuditRecordService {
    AuditRecordEntity save(AuditRecordEntity auditRecordEntity);

    AuditRecordEntity findById(String id);

    Map<String, Object> getList(AuditRecordBO recordBO);

    void deleteById(String id);

}
