<template>
  <div
    ref="refContainer"
    class="iframe-container"
    @dblclick="fullscreen.toggle"
    @mouseover="handleMouseOver"
    @mouseleave="handleMouseLeave"
  >
    <iframe
      frameborder="0"
      scrolling="auto"
      :src="frameUrl"
      width="100%"
      height="100%"
      allowfullscreen="true"
    ></iframe>
    <ExitFullScreen
      v-if="!props.disableFullScreen"
      style="margin-top: 40px"
      :collapsed="collapsed"
      @fullscreen="handleFullScreen"
    ></ExitFullScreen>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { useRouter } from 'vue-router';
import { useUserStore } from '@/store';
import { removeUrlPramByName } from '@/utils/GlobalHelper';
import { useFullscreen } from '@vueuse/core';
import ExitFullScreen from '@/views/largeScreen/components/ExitFullScreen.vue';

const props = defineProps<{
  url?: string;
  tokenField?: string;
  disableFullScreen?: boolean;
}>();
const router = useRouter();
const collapsed = ref<boolean>(true);
const refContainer = ref<HTMLElement>();
const frameUrl = computed(() => {
  let iframePath =
    props.url || router.currentRoute.value.query?.url?.toString() || '';
  const tokenField =
    props.tokenField || router.currentRoute.value.query?.tokenField;
  if (tokenField) {
    const sparater = iframePath.indexOf('?') !== -1 ? '&' : '?';
    iframePath += sparater + tokenField + '=' + useUserStore().token;
  }
  return removeUrlPramByName(iframePath, 'tokenField');
});
const handleMouseOver = () => {
  collapsed.value = false;
};
const handleMouseLeave = () => {
  collapsed.value = true;
};
const isFullScreen = ref<boolean>(false);
const handleFullScreen = (flag?: boolean) => {
  if (props.disableFullScreen) return;
  if (flag !== undefined) {
    isFullScreen.value = true;
  } else {
    isFullScreen.value = !!document.fullscreenElement;
  }
  fullscreen.toggle();
};
const fullscreen = useFullscreen(refContainer);
</script>

<style lang="scss" scoped>
.iframe-container {
  width: 100%;
  height: 100%;
}
</style>
