package org.thingsboard.server.dao.district;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.smartManagement.district.CircuitDistrictPoint;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.district.CircuitDistrictPointPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.district.CircuitDistrictPointSaveRequest;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.plan.CircuitTaskReportSaveRequest;

import java.util.List;

public interface CircuitDistrictPointService {
    /**
     * 分页条件查询关键点
     *
     * @param request 分页查询条件
     * @return 分页查询结果
     */
    IPage<CircuitDistrictPoint> findAllConditional(CircuitDistrictPointPageRequest request);

    /**
     * 保存关键点
     *
     * @param entity 实体信息
     * @return 保存好的实体
     */
    CircuitDistrictPoint save(CircuitDistrictPointSaveRequest entity);

    /**
     * 增量修改
     *
     * @param entity 实体信息
     * @return 是否修改成功
     */
    boolean update(CircuitDistrictPoint entity);

    /**
     * 删除
     *
     * @param id 唯一标识
     * @return 是否删除成功
     */
    boolean delete(String id);

    /**
     * 获取关键点的报告保存模板
     *
     * @param idList 巡检任务id列表
     * @return 关键点的报告保存模板
     */
    List<CircuitTaskReportSaveRequest> selectReportTemplate(List<String> idList);

    /**
     * 通过id查询指定的关键点
     *
     * @param id 关键点id
     * @return 关键点信息
     */
    CircuitDistrictPoint findById(String id);

    /**
     * 区域内是否有关键点
     * @param districtAreaId 区域id
     * @return 是否有关键点
     */
    boolean hasPoint(String districtAreaId);


}
