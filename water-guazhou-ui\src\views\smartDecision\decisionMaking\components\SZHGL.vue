<template>
  <div
    class="bottom-box"
    style=""
  >
    <span class="green total"> 99.76% </span>
    <span class="green plant"> 99.72% </span>
    <span class="green pipe"> 99.81% </span>
  </div>
</template>
<script lang="ts" setup></script>
<style lang="scss" scoped>
.bottom-box {
  overflow: hidden;
  height: 100%;
  display: flex;
  background: url('../imgs/decisionMaking_bottom_left.png') 50% / 75% 90%
    no-repeat;

  .green {
    font-size: 22px;
    color: #4be322;
  }
  .total {
    position: absolute;
    top: 22%;
    right: 90px;
  }
  .plant {
    position: absolute;
    bottom: 30px;
    left: 70px;
  }
  .pipe {
    position: absolute;
    bottom: 30px;
    right: 70px;
  }
}
</style>
