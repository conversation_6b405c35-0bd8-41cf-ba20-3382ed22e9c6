package org.thingsboard.server.controller.smartPipe;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.PipeUseWaterReport;
import org.thingsboard.server.dao.smartPipe.PipeUseWaterReportService;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

/**
 * 智慧管网-水量平衡
 */
@RestController
@RequestMapping("api/spp/useWaterReport")
public class PipeUseWaterReportController extends BaseController {

    @Autowired
    private PipeUseWaterReportService pipeUseWaterReportService;

    @PostMapping
    public IstarResponse save(@RequestBody PipeUseWaterReport pipeUseWaterReport) throws ThingsboardException {
        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        pipeUseWaterReport.setCreator(UUIDConverter.fromTimeUUID(getCurrentUser().getUuidId()));
        pipeUseWaterReport.setTenantId(tenantId);

        return pipeUseWaterReportService.save(pipeUseWaterReport);
    }

    @GetMapping("waterBalance")
    public IstarResponse getWaterBalance(String partitionId, String ym) {
        if (StringUtils.isBlank(partitionId)) {
            return IstarResponse.error("请选择分区");
        }
        if (StringUtils.isBlank(ym)) {
            return IstarResponse.error("请选择日期");
        }

        return IstarResponse.ok(pipeUseWaterReportService.getWaterBalance(partitionId, ym));
    }

    @GetMapping("yearDetail")
    public IstarResponse getYearDetail(String partitionId, String year) throws ThingsboardException {
        if (StringUtils.isBlank(partitionId)) {
            return IstarResponse.error("请选择分区");
        }
        if (StringUtils.isBlank(year)) {
            return IstarResponse.error("请选择时间");
        }

        String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
        return IstarResponse.ok(pipeUseWaterReportService.getYearDetail(partitionId, year));
    }

}
