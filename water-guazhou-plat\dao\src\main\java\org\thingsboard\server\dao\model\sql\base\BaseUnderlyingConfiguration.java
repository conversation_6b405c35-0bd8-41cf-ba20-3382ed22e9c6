package org.thingsboard.server.dao.model.sql.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 平台管理-基础配置对象 base_underlying_configuration
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
@ApiModel(value = "基础配置", description = "平台管理-基础配置实体类")
@Data
public class BaseUnderlyingConfiguration {
    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 主机地址
     */
    @ApiModelProperty(value = "主机地址")
    private String hostAddress;

    /**
     * 端口号
     */
    @ApiModelProperty(value = "端口号")
    private String hostPort;

    /**
     * 数据库名称
     */
    @ApiModelProperty(value = "数据库名称")
    private String name;

    /**
     * 用户名
     */
    @ApiModelProperty(value = "用户名")
    private String username;

    /**
     * 密码
     */
    @ApiModelProperty(value = "密码")
    private String password;

    /**
     * url扩展参数
     */
    @ApiModelProperty(value = "url扩展参数")
    private String urlEx;

    /**
     * 连接池配置
     */
    @ApiModelProperty(value = "连接池配置")
    private String poolConfig;

    /**
     * 安全配置
     */
    @ApiModelProperty(value = "安全配置")
    private String safeConfig;

    /**
     * 超时与重试
     */
    @ApiModelProperty(value = "超时与重试")
    private String connTimeout;

    /**
     * 唯一站点id
     */
    @ApiModelProperty(value = "唯一站点id")
    private String siteId;

    /**
     * IP地址
     */
    @ApiModelProperty(value = "IP地址")
    private String ipAddress;

    /**
     * 数据库版本
     */
    @ApiModelProperty(value = "数据库版本")
    private String dbVersion;
}
