.tree-right-detail-box{
    background: #131624;
}
.el-card {
  background: #222536 !important;
}
.el-input__inner{
    background: #222536 !important;
    border: 1px solid #3C3F54 !important;
}
.el-form-item__label{
    color: #9097C0 !important;
}
/* .app-custom .app-wrapper .main-container .app-main .el-input__inner{
    columns: #ffffff !important;
} */
.el-pagination__total{
    color: #ffffff !important;
}
/* .app-custom .app-wrapper .main-container .app-main .el-input__inner{
    color: #ffffff !important;
} */
.el-pagination__jump{
    color: #9292BA !important;
}
.app-body-dark .el-pagination .btn-next, .app-body-dark .el-pagination .btn-prev{
    color: #888888;
}
.el-checkbox__inner{
    background-color: #222536 !important;
}
.app-custom .app-wrapper .main-container .app-main .el-checkbox .el-checkbox__inner{
    border-color: #8D93A1;
}
.app-body-dark .el-table .el-table__header-wrapper .el-table__body tr .cell, .app-body-dark .el-table .el-table__body-wrapper .el-table__body tr .cell {
    color: #FFFFFF;
}
:deep(.tree-list-container){
    background-color:#272A3E;
}
.app-body-dark .el-pagination .el-pager{
    color: #9292BA;
}
.tree-list-container{
    background-color: #272A3E !important;
}
.tree-list-container .tree-list-box .active-tree-node .c-t-label .c-t-name{
    color: #ffffff !important;
}
.headerBoxBorder {
    border-bottom: 1px solid #33374D;
}
.tree-list-container .tree-list-title{
    color: #ffffff !important;
}
.app-body-dark .el-table .el-table__header-wrapper .cell, .app-body-dark .el-table .el-table__fixed-header-wrapper .cell {
    color: #9097C0;
}
.app-body-dark .el-tabs--card .el-tabs__header{
    background: #272A3E;
    color: #B0B9C6;
}
.app-body-dark .el-tabs--card .el-tabs__header .el-tabs__nav-wrap .el-tabs__nav .el-tabs__item.is-active {
    background-color: #272A3E;
    border-bottom: 1px solid #F69080;
    color: #F69080;
}
.app-body-dark .el-table .el-table__header-wrapper .el-table__body tr:hover td, .app-body-dark .el-table .el-table__body-wrapper .el-table__body tr:hover td {
    background-color: #2f3c50;
}
.app-body-dark .el-table .el-table__header-wrapper .el-table__body tr:hover .cell, .app-body-dark .el-table .el-table__body-wrapper .el-table__body tr:hover .cell {
    color: #ffffff;
}
.app-body-dark .el-button--default{
    color: #C1D8FA;
}
.control-fold-btn{
    background: #272A3E !important;
}
/* 这是弹框颜色 */
.app-body-dark .el-select-dropdown, .app-body-dark .el-select-dropdown.is-multiple {
    background-color: #272A3E;
    border: none;
}
.el-select__popper{
    border: solid 1px #282829 !important;
}
.app-body-dark .el-select-dropdown .el-select-dropdown__item.selected, .app-body-dark .el-select-dropdown.is-multiple .el-select-dropdown__item.selected {
    color: rgb(255, 255, 255);
    background-color: #323d4e !important;
}
.app-body-dark .el-select-dropdown .el-select-dropdown__item.hover, .app-body-dark .el-select-dropdown.is-multiple .el-select-dropdown__item.hover {
    background-color: #323d4e;
}
.app-body-dark .el-select-dropdown .el-select-dropdown__item, .app-body-dark .el-select-dropdown .el-select-dropdown__item.selected, .app-body-dark .el-select-dropdown.is-multiple .el-select-dropdown__item, .app-body-dark .el-select-dropdown.is-multiple .el-select-dropdown__item.selected {
    color: #ffffff;
}
.app-body-dark .el-select-dropdown .el-select-dropdown__item:hover, .app-body-dark .el-select-dropdown .el-select-dropdown__item.selected:hover, .app-body-dark .el-select-dropdown.is-multiple .el-select-dropdown__item:hover, .app-body-dark .el-select-dropdown.is-multiple .el-select-dropdown__item.selected:hover {
    background-color: #323d4e;
}
.el-dialog__header{
    background: #2B2E41;
}
.el-dialog__title{
    color: #ffffff;
}
.el-dialog__body{
    background: #222536;
}
.el-dialog__footer{
    background: #222536;
}
.addOrUpdateDialog .avatar-uploader .el-upload:hover {
    border-color: #3d4050 !important;
    background: #313542;
}
.el-textarea__inner{
    background-color: #222536;
}
/* datatime */
/* .el-picker-panel{
    background: #222536;
}
.el-picker__popper{
    border: 1px solid #313542;
}
.app-body-dark .el-picker-panel {
    border: 1px solid #1d1b1b;
    border-color: #dcdfe6;
    color: #ffffff !important;
} */
.location-label{
    color: red;
}
.el-table {

    width: 100%;
    
    display: flex;
    
    flex-direction: column;
    
    .el-table__body-wrapper {
    
    flex: 1;
    
        }
    }
