<template>
  <ul class="legend-list">
    <li>
      <span
        v-for="(item, i) in props.header"
        :key="i"
        class="label header"
      >
        {{ item }}
      </span>
    </li>
    <li
      v-for="(item, i) in list"
      :key="i"
    >
      <span class="label">
        {{ item.label }}
      </span>
      <el-checkbox v-model="item.checked"></el-checkbox>
      <Icon
        :icon="'ep:location'"
        color="#409eff"
        style="cursor: pointer"
        @click="locate(item)"
      ></Icon>
    </li>
  </ul>
</template>
<script lang="ts" setup>
import { Icon } from '@iconify/vue'

type IListItemProperty = { label: string; checked: boolean }
const emit = defineEmits(['click'])
const props = defineProps<{ data: IListItemProperty[]; header: string[] }>()
watch(
  () => props.data,
  () => refreshData()
)
const list = ref<{ label: string; checked: boolean }[]>([])
const refreshData = () => {
  list.value = props.data || []
}
const locate = (item: any) => {
  emit('click', item)
}
onMounted(() => {
  refreshData()
})
</script>
<style lang="scss" scoped>
.legend-list {
  list-style: none;
  margin: 0;
  padding: 0;
  font-size: 14px;
  li {
    display: grid;
    padding: 4px 8px;
    place-items: center;
    grid-template-columns: 1fr 1fr 1fr;
  }
  .header {
    font-weight: bold;
    text-wrap: nowrap;
    font-size: 14px;
  }
}
</style>
