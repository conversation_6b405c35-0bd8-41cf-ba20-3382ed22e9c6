package org.thingsboard.server.controller.base;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.dao.model.sql.OperatingIncomeInput;
import org.thingsboard.server.dao.operatingIncomeInput.OperatingIncomeInputService;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.Date;

@RestController
@RequestMapping("api/operatingIncomeInput")
public class OperatingIncomeInputController extends BaseController {

    @Autowired
    private OperatingIncomeInputService operatingIncomeInputService;

    @GetMapping("list")
    public IstarResponse list(@RequestParam String year, @RequestParam String stationId) throws ThingsboardException {
        return IstarResponse.ok(operatingIncomeInputService.findList(stationId, year, getTenantId()));
    }

    @PostMapping("update")
    public IstarResponse save(@RequestBody OperatingIncomeInput entity) throws ThingsboardException {
        if (StringUtils.isBlank(entity.getId())) {
            return IstarResponse.error("提交的数据错误!");
        }
        entity.setUpdateUser(getCurrentUser().getFirstName());
        entity.setUpdateTime(new Date());
        operatingIncomeInputService.update(entity);
        return IstarResponse.ok();
    }

    @GetMapping("detail")
    public IstarResponse detail(@RequestParam String stationId) throws ThingsboardException {
        return IstarResponse.ok(operatingIncomeInputService.getDetail(stationId, getTenantId()));
    }

    @GetMapping("trend")
    public IstarResponse trend(@RequestParam String stationId) throws ThingsboardException {
        return IstarResponse.ok(operatingIncomeInputService.getTrend(stationId, getTenantId()));
    }



}
