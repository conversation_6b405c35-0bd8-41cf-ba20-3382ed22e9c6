package org.thingsboard.server.dao.model.sql;

import lombok.Data;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;

@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.APP_TYPE_RELATION_TABLE)
public class AppTypeRelation {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.APP_TYPE_RELATION_CREATE_TIME)
    private Long createTime;

    @Column(name = ModelConstants.APP_TYPE_RELATION_MENU_POOL_ID)
    private String menuPoolId;

    @Column(name = ModelConstants.APP_TYPE_RELATION_APP_TYPE_ID)
    private String appTypeId;

}
