import{C as h,D as u,u as v,b as f,g as k,n as I,q as r,F as t,G as c,H as b,I as C,J as x,K as V,L as w}from"./index-r0dFAfgr.js";import{a as E}from"./index-Bj5d3Vsu.js";const R={name:"ConfirmDialog",props:["dialogInfo"],emits:["refresh"],data(){return{form:{removeRemark:""},rules:{removeRemark:[{required:!0,message:"请输入告警确认备注",trigger:"blur"},{validator:(e,o,s)=>{o.trim()===""?(console.log(o),s(new Error("输入不可为空，请输入"))):s()},trigger:"blur"}]}}},computed:{visible(){return this.dialogInfo.visible}},methods:{save(){this.$refs.form.validate(a=>{if(a){const e=this.dialogInfo.row,o={alarmId:[],remark:this.form.removeRemark.trim()},s=u(e.id.id);if(o.alarmId.push(s),this.$route.path==="/eventAlarm/realTimeAlarm"||this.$route.path==="/moverealTimeAlarm"){const l=u(v().id)+"."+new Date().valueOf();o.secret=btoa(l)}else{const l=this.$route.fullPath.split("=secret");o.secret=l[l.length-1]}console.log(o,this.$route.fullPath,"-----单独"),E(o).then(()=>{f.success("确认成功"),this.dialogInfo.close(),this.$emit("refresh")})}else return f.warning("请填写备注"),!1})}}},D={class:"confirm-dialog"};function T(a,e,o,s,l,m){const p=b,i=C,d=x,g=V,_=w;return k(),I("div",D,[r(_,{modelValue:m.visible,"onUpdate:modelValue":e[1]||(e[1]=n=>m.visible=n),title:"确认告警",width:"35%","append-to-body":"",class:"alarm-design","close-on-click-modal":!1,onClose:o.dialogInfo.close},{default:t(()=>[r(g,{ref:"form",model:l.form,"label-width":"90px",rules:l.rules},{default:t(()=>[r(i,{label:"确认备注",prop:"removeRemark"},{default:t(()=>[r(p,{modelValue:l.form.removeRemark,"onUpdate:modelValue":e[0]||(e[0]=n=>l.form.removeRemark=n),placeholder:"请输入告警确认备注"},null,8,["modelValue"])]),_:1}),r(i,null,{default:t(()=>[r(d,{type:"primary",onClick:m.save},{default:t(()=>e[2]||(e[2]=[c(" 保 存 ")])),_:1},8,["onClick"]),r(d,{onClick:o.dialogInfo.close},{default:t(()=>e[3]||(e[3]=[c(" 取 消 ")])),_:1},8,["onClick"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue","onClose"])])}const B=h(R,[["render",T]]);export{B as default};
