package org.thingsboard.server.dao.util.imodel.query.base;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.thingsboard.server.dao.model.sql.base.BaseSchemeManagement;
import org.thingsboard.server.dao.util.imodel.query.PageableQueryEntity;

/**
 * 平台管理-方案管理对象 base_scheme_management
 *
 * <AUTHOR>
 * @date 2025-05-22
 */
@ApiModel(value = "方案管理", description = "方案管理实体类")
@Data
public class BaseSchemeManagementPageRequest extends PageableQueryEntity<BaseSchemeManagement> {

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 方案名称
     */
    @ApiModelProperty(value = "方案名称")
    private String name;

    /**
     * 底图配置id
     */
    @ApiModelProperty(value = "底图配置id")
    private String mapConfId;

    /**
     * 管网配置id
     */
    @ApiModelProperty(value = "管网配置id")
    private String pipeConfId;

    /**
     * 状态（0-禁用，1-启用）
     */
    @ApiModelProperty(value = "状态")
    private String status;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

}
