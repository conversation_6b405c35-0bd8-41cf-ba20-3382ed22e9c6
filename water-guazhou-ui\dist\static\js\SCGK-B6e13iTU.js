import{d as u,r as p,g as e,n as r,p as a,aB as c,aJ as d,h as f,aw as _,i as s,q as o,cU as g,C as v}from"./index-r0dFAfgr.js";import w from"./ScrollList-C0VFzhoB.js";import x from"./TargetItem-MDYkIAGN.js";import{e as y}from"./decisionMaking_center_left-CLFn6qN_.js";import"./index-BlG8PIOK.js";const B={class:"scgk-info"},N=u({__name:"SCGK",setup(b){const t=p({data:[{name:"供水总量(万m³)",value:"1.95",scale:"-"},{name:"总电耗(KW•H)",value:"0",scale:"-"}],info:[{label:"年度目标供水量",value:"3,000.00",unit:"万m3",text:"完成率",scale:"19.2%",className:"annual-water-target-supply",rows:[1,2,3]},{label:"全年日均供水量",value:"0.04",unit:"万m3",text:"同比",status:"up",scale:"3.4%",className:"average-water-supply",rows:[1,2,3]},{label:"最高日水量",value:"1.25",unit:"万m3",text:"同比",status:"down",scale:"3.4%",className:"dayly-max-water-supply",rows:[1,2,3]}]});return(k,l)=>{const m=g;return e(),r(c,null,[a("div",B,[(e(!0),r(c,null,d(s(t).info,(n,i)=>(e(),f(x,{key:i,config:n,class:_(n.className)},null,8,["config","class"]))),128)),o(m,{fit:"contain",class:"ellipse-image",src:s(y),alt:""},null,8,["src"]),l[0]||(l[0]=a("div",{class:"annual-water-supply"},[a("div",{class:"count"}," 1.95 "),a("div",{class:"text"}," 年度累计供水量 (万m3) ")],-1))]),o(w,{data:s(t).data},null,8,["data"])],64)}}}),G=v(N,[["__scopeId","data-v-4b18b5ca"]]);export{G as default};
