package org.thingsboard.server.utils.imodel.validate;

import org.springframework.stereotype.Component;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;
import org.thingsboard.server.dao.util.reflection.BeanWrapper;
import org.thingsboard.server.dao.util.reflection.FieldWrapper;
import org.thingsboard.server.utils.imodel.aop.ValidatePerformer;

import java.util.Collection;

import static org.thingsboard.server.dao.util.imodel.StringUtils.isNullOrEmpty;

@Component
public class NotNullOrEmptyValidator implements ValidatePerformer {

    @Override
    public boolean match(FieldWrapper field) {
        return field.isAnnotationPresent(NotNullOrEmpty.class);
    }

    @Override
    public String resolveString(BeanWrapper bean, FieldWrapper field, String value, boolean hasParent) {
        NotNullOrEmpty notNullOrEmptyAnno = field.getAnnotation(NotNullOrEmpty.class);

        String condition = notNullOrEmptyAnno.condition();
        if (callConditionalGetter(bean, condition))
            return null;

        // 去除两端空格
        String valid;
        if (value != null && notNullOrEmptyAnno.trim()) {
            value = value.trim();
            valid = field.conditionalSetValue(value, true);
            if (valid != null) {
                return valid;
            }
        }

        // 含父级则忽略
        if (notNullOrEmptyAnno.parentIgnore()) {
            if (hasParent) {
                return null;
            }
        }

        return isNullOrEmpty(value) ? getRequireTips(field) : null;
    }

    @Override
    public String resolveDestType(BeanWrapper bean, FieldWrapper field, Class<?> type, Object value, boolean hasParent) {
        NotNullOrEmpty notNullOrEmptyAnno = field.getAnnotation(NotNullOrEmpty.class);
        String condition = notNullOrEmptyAnno.condition();
        if (callConditionalGetter(bean, condition))
            return null;

        // 含父级则忽略
        if (notNullOrEmptyAnno.parentIgnore()) {
            if (hasParent) {
                return null;
            }
        }

        if (value != null && value.getClass().isArray()) {
            Object[] arr = (Object[]) value;
            if (arr.length == 0) {
                return getRequireTips(field);
            }

            for (Object o : arr) {
                if (o == null || (o instanceof String && isNullOrEmpty((String) o))) {
                    return getRequireTips(field);
                }
            }

            return null;
        } else if (value instanceof Collection) {
            Collection<?> collection = ((Collection<?>) value);
            if (collection.isEmpty()) {
                return getRequireTips(field);
            }
            for (Object o : collection) {
                if (o == null) {
                    return getRequireTips(field);
                } else if (o instanceof String && isNullOrEmpty((String) o)) {
                    return getRequireTips(field);
                }
            }
        }

        return value == null ? getRequireTips(field) : null;
    }

    private String getRequireTips(FieldWrapper field) {
        return "必要参数未传入：" + field.getName();
    }

    @Override
    public int getOrder() {
        return 100;
    }
}
