package org.thingsboard.server.dao.util.imodel.query.smartManagement.maintain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartManagement.maintaince.SMMaintainTask;
import org.thingsboard.server.dao.util.imodel.query.ComplexSaveRequest;
import org.thingsboard.server.dao.util.imodel.query.GeneralTaskRequest;
import org.thingsboard.server.dao.util.imodel.query.StringSetter;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

import java.util.Date;

@Getter
@Setter
public class SMMaintainTaskSaveRequest extends ComplexSaveRequest<SMMaintainTask, SMMaintainTaskItemSaveRequest> implements GeneralTaskRequest { // 任务名称
    // 任务名称
    @NotNullOrEmpty
    private String name;

    // 设备Id
    @NotNullOrEmpty
    private String device;

    // 设备名称
    @NotNullOrEmpty
    private String deviceName;

    // 养护人员Id
    private String maintainUser;

    // 开始时间
    @NotNullOrEmpty
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date beginTime;

    // 结束时间
    @NotNullOrEmpty
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    // 备注
    private String remark;

    @Override
    protected SMMaintainTask build() {
        SMMaintainTask entity = new SMMaintainTask();
        // entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    @Override
    protected SMMaintainTask update(String id) {
        disallowUpdate();
        SMMaintainTask entity = new SMMaintainTask();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(SMMaintainTask entity) {
        entity.setName(name);
        entity.setDevice(device);
        entity.setDeviceName(deviceName);
        entity.setMaintainUser(maintainUser);
        entity.setBeginTime(beginTime);
        entity.setEndTime(endTime);
        entity.setStatus(getCreateStatus());
        entity.setRemark(remark);
        entity.setTenantId(tenantId());
        entity.setCreateTime(createTime());
        entity.setCreator(currentUserUUID());
    }

    @Override
    protected StringSetter<SMMaintainTaskItemSaveRequest> parentSetter() {
        return SMMaintainTaskItemSaveRequest::setTaskId;
    }
}