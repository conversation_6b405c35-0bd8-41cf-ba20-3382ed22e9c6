package org.thingsboard.server.dao.maintainCircuit;

import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.maintainCircuit.circuit.StationCircuitScheme;

import java.util.List;

public interface StationCircuitSchemeService {

    void save(StationCircuitScheme entity);

    /**
     * 分页查询站点养护方案
     */
    PageData<StationCircuitScheme> findList(int page, int size, String name, String stationType, TenantId tenantId);

    void remove(List<String> ids);

    StationCircuitScheme findById(String id);
}
