import {
  ElForm,
  ElDialog,
  ElTree,
  ElTable,
  ElScrollbar,
  ElMenu,
  FormItemRule,
  ElInput
} from 'element-plus'
// import { FormItemRule } from 'element-plus/lib/components/form/src/form.vue'

export type IElForm = InstanceType<typeof ElForm>
export type IElDialog = InstanceType<typeof ElDialog>
export type IElTree = InstanceType<typeof ElTree>
export type IElTable = InstanceType<typeof ElTable>

export type IFormRule = Record<string, FormItemRule[]>
export type IFormItemRule = FormItemRule | FormItemRule[]
export type IElScrollbar = InstanceType<typeof ElScrollbar>

export type IELMenu = InstanceType<typeof ElMenu>
export type IElInput = InstanceType<typeof ElInput>
