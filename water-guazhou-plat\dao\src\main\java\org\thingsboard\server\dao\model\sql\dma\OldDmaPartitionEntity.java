package org.thingsboard.server.dao.model.sql.dma;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * DMA分区设置
 */
@Data
@TableName("dma_partition")
public class OldDmaPartitionEntity {
    private String id;

    private String pid;

    private String name;

    private String type;

    private Integer orderNum;

    private String copyMeterType;

    private Boolean isMachineMeter;

    private Integer collectRate;

    private String range;

    private String borderColor;

    private String rangeColor;

    private String userType;

    private String copyMeterUser;

    private String director;

    private Integer bigUserNum;

    private Integer revenueUserNum;

    private Double supplyWaterArea;

    private Double mainLineLength;

    private Double maintainCost;

    private String remark;

    private Date createTime;

    private Date updateTime;

    private String tenantId;

}
