import { request } from '@/plugins/axios'

// 获取管网配置列表
export function getPipeNetworkConfigList(params: any) {
  return request({
    url: '/api/base/pipe/configuration/list',
    method: 'get',
    params
  })
}

// 获取管网配置详情
export function getPipeNetworkConfigDetail(id: string) {
  return request({
    url: '/api/base/pipe/configuration/getDetail',
    method: 'get',
    params: { id }
  })
}

// 新增管网配置
export function addPipeNetworkConfig(data: any) {
  return request({
    url: '/api/base/pipe/configuration/add',
    method: 'post',
    data
  })
}

// 修改管网配置
export function editPipeNetworkConfig(data: any) {
  return request({
    url: '/api/base/pipe/configuration/edit',
    method: 'post',
    data
  })
}

// 删除管网配置
export function deletePipeNetworkConfig(ids: string[]) {
  return request({
    url: '/api/base/pipe/configuration/deleteIds',
    method: 'delete',
    data: ids
  })
} 