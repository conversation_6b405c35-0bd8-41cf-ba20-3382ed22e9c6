package org.thingsboard.server.dao.sql.dma;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.dma.DmaJinzhouAnalysisEntity;

import java.util.List;

/**
 * userMybatis
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2023-11-07
 */
@Mapper
public interface DmaJinzhouAnalysisMapper extends BaseMapper<DmaJinzhouAnalysisEntity> {

    List<DmaJinzhouAnalysisEntity> getByCodesAndTime(@Param("code") String code, @Param("start") String startStr, @Param("end") String endStr);
}
