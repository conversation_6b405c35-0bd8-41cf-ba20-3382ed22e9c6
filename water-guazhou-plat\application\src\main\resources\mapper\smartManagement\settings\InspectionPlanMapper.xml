<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartManagement.settings.InspectionPlanMapper">
    
    <sql id="Base_Column_List">
        <!--@sql select -->
        id,
        plan_name,
        inspection_type,
        inspection_cycle,
        execution_role,
        checklist_template,
        status,
        remark,
        create_time,
        tenant_id
        <!--@sql from sm_inspection_plan_setting -->
    </sql>

    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.smartManagement.settings.InspectionPlanSetting">
        <id column="id" property="id"/>
        <result column="plan_name" property="planName"/>
        <result column="inspection_type" property="inspectionType"/>
        <result column="inspection_cycle" property="inspectionCycle"/>
        <result column="execution_role" property="executionRole"/>
        <result column="checklist_template" property="checklistTemplate"/>
        <result column="status" property="status"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <select id="findByPage" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM sm_inspection_plan_setting
        WHERE 1=1
        <if test="tenantId != null and tenantId != ''">
            AND tenant_id = #{tenantId}
        </if>
        <if test="planName != null and planName != ''">
            AND plan_name LIKE '%' || #{planName} || '%'
        </if>
        <if test="inspectionType != null and inspectionType != ''">
            AND inspection_type = #{inspectionType}
        </if>
        <if test="inspectionCycle != null and inspectionCycle != ''">
            AND inspection_cycle = #{inspectionCycle}
        </if>
        <if test="executionRole != null and executionRole != ''">
            AND execution_role = #{executionRole}
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        <if test="fromTime != null and fromTime != ''">
            AND create_time >= #{fromTime}
        </if>
        <if test="toTime != null and toTime != ''">
            AND create_time &lt;= #{toTime}
        </if>
        ORDER BY create_time DESC
    </select>

    <update id="update" parameterType="org.thingsboard.server.dao.model.sql.smartManagement.settings.InspectionPlanSetting">
        UPDATE sm_inspection_plan_setting
        <set>
            <if test="planName != null and planName != ''">
                plan_name = #{planName},
            </if>
            <if test="inspectionType != null and inspectionType != ''">
                inspection_type = #{inspectionType},
            </if>
            <if test="inspectionCycle != null and inspectionCycle != ''">
                inspection_cycle = #{inspectionCycle},
            </if>
            <if test="executionRole != null and executionRole != ''">
                execution_role = #{executionRole},
            </if>
            <if test="checklistTemplate != null and checklistTemplate != ''">
                checklist_template = #{checklistTemplate},
            </if>
            <if test="status != null and status != ''">
                status = #{status},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
        </set>
        WHERE id = #{id}
    </update>

    <select id="findById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM sm_inspection_plan_setting
        WHERE id = #{id}
    </select>
</mapper>
