<template>
  <div ref="refContainer" class="el-menu-horizontal-warp">
    <el-scrollbar
      ref="elMenuHorizontalScrollRef"
      :min-size="8"
      @wheel.prevent="onElMenuHorizontalScroll"
    >
      <el-menu
        ref="refMenu"
        :default-active="appStore.appid"
        background-color="transparent"
        mode="horizontal"
        :ellipsis="false"
      >
        <template v-for="val in businessStore.curNavs" :key="val.id">
          <el-menu-item
            class="horizontal-menu-item"
            :index="val.id"
            @click="() => handleMenuItemClick(val)"
          >
            <template #title>
              <!-- <i v-if="val.img"></i> -->
              <el-image
                class="image-icon"
                :src="val.img"
                :fit="'fill'"
                alt=""
              />
              <span class="menu-title">{{ val.name }}</span>
            </template>
          </el-menu-item>
        </template>
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script lang="ts" setup>
import { getCurrentInstance, onMounted, nextTick, watch, ref } from 'vue';
import { getToken, getYinShouLoginParams } from '@/api/login';
import { IELMenu } from '@/common/types/element-plus';
import router from '@/router';
import { useAppStore, useBusinessStore, usePermissionStore } from '@/store';
import { getUrlPramByName } from '@/utils/GlobalHelper';
import {
  GetDeepestRoute,
  GetDeepestRoutePath,
  refreshAllRoutes
  // Set_navApps
} from '@/utils/RouterHelper';

const permissionStore = usePermissionStore();
const businessStore = useBusinessStore();
const appStore = useAppStore();
const { proxy }: any = getCurrentInstance();
const refMenu = ref<IELMenu>();
const handleMenuItemClick = async (res?: NavResult) => {
  if (!businessStore.curNavs.length || !res) return;

  const isCurrentApp = appStore.appid && res.id === appStore.appid;
  appStore.SET_appname(res.name);
  appStore.SET_appid(res.id);
  // 跳转
  if (res.type === '2') {
    const param: NormalOption | null = getUrlPramByName(
      res.applicationUrl,
      'type'
    );
    let appurl = '';
    appStore.SET_isFrameApp(true);
    if (param) {
      if (param.value === 'yingshou') {
        const ysres = await getYinShouLoginParams();
        appurl =
          res.applicationUrl.split('?')[0] +
          '?username=' +
          ysres.data.username +
          '&password=' +
          ysres.data.password;
      } else if (param.value === 'yingshou2') {
        appurl = res.applicationUrl.split('?')[0] + '?o=' + getToken();
      } else {
        appurl = res.applicationUrl;
      }
      window.open(appurl);
    } else {
      window.open(res.applicationUrl);
    }
  } else {
    appStore.SET_isFrameApp(false);
    await refreshAllRoutes();
    if (isCurrentApp) {
      const path =
        router.currentRoute.value.fullPath === '/'
          ? GetDeepestRoutePath(permissionStore.addRouters[0])
          : router.currentRoute.value.fullPath;
      router.push({
        path: path || '/'
      });
    } else {
      const deeptestRoute = GetDeepestRoute(permissionStore.addRouters[0]);
      router.push(deeptestRoute);
    }
  }
};

// const filterMenus = async () => {
//   const curNavs = businessStore.curNavs

//   const currentNavAppName = appStore.appname
//   const currentNav = curNavs.find(item => item.name === currentNavAppName)

//   handleMenuItemClick(currentNav || businessStore.curNavs[0])
// }
// watch(
//   () => businessStore.navSelectedRange?.data?.type,
//   () => {
//     filterMenus()
//     resetWidth()
//   }
// )
watch(
  () => businessStore.curNavs,
  async () => {
    await nextTick();
    resetWidth();
    initElMenuOffsetLeft();
  }
);
// 设置横向滚动条可以鼠标滚轮滚动
const onElMenuHorizontalScroll = (e: any) => {
  const eventDelta = e.wheelDelta || -e.deltaY * 40;
  proxy.$refs.elMenuHorizontalScrollRef.wrap$.scrollLeft -= eventDelta / 4;
};
// 初始化数据，页面刷新时，滚动条滚动到对应位置
const initElMenuOffsetLeft = async () => {
  await nextTick();
  const els: any = document.querySelector(
    '.el-menu.el-menu--horizontal li.is-active'
  );
  if (!els) return false;
  proxy.$refs.elMenuHorizontalScrollRef.wrap$.scrollLeft = els.offsetLeft;
};
const refContainer = ref<HTMLDivElement>();
const resetWidth = () => {
  if (!refContainer.value || !refMenu.value) return;
  // const containerwidth = refContainer.value?.clientWidth
  // const
  const childrens = document.getElementsByClassName('horizontal-menu-item');
  let width = 0;
  for (let i = 0; i < childrens.length; i++) {
    width += childrens[i].clientWidth;
  }
  refMenu.value.$el.style.width = width + 'px';
};

// watch(
//   () => router.currentRoute.value,
//   () => {
//     resetWidth()
//   }
// )
// 页面加载时
onMounted(async () => {
  // if (!businessStore.useprojectapp) {
  //   await Set_navApps()
  // }
  await nextTick();
  resetWidth();
  // filterMenus()
});
</script>

<style scoped lang="scss">
.el-menu-horizontal-warp {
  flex: 1;
  overflow: hidden;
  overflow-x: auto;
  overflow-x: overlay;
  width: 100%;
  height: 58px;
  user-select: none;
  :deep(.el-scrollbar__bar.is-vertical) {
    display: none;
  }
  :deep(a) {
    width: 100%;
  }

  .el-menu.el-menu--horizontal {
    display: flex;
    // width: 800px;
    margin-left: auto;
    // flex-direction: row-reverse;
    height: 58px;
    box-sizing: border-box;
    align-content: center;
    align-items: center;
    border-bottom: none;
    justify-content: flex-end;
    flex-wrap: nowrap;
    & > .el-menu-item {
      border-bottom: none !important;
      height: 58px;
      font-size: 16px;
      * {
        vertical-align: unset;
      }
      &:not(.is-disabled) {
        &:hover,
        &:focus,
        &.is-active {
          // color: #57acea !important;
          background-color: transparent !important;
          border-bottom: 2px solid rgb(2, 195, 249) !important;
          background-image: linear-gradient(
            rgba(2, 191, 249, 0) 0,
            rgba(2, 117, 249, 0.3) 100%
          ) !important;
        }
      }
    }
  }
  .horizontal-menu-item {
    display: flex;
    align-items: center;
  }
  .image-icon {
    object-fit: contain;
    width: 1.2em;
    height: 1.2em;
    margin-right: 8px;
    display: flex;
    align-items: center;
    :deep(img) {
      vertical-align: middle;
    }
  }
  .menu-title {
    color: #fff;
  }
}
</style>
