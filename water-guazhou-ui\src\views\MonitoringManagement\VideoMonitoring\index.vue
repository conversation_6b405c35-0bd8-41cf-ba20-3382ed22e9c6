<!-- 视频监控管理-视频监控 -->
<template>
  <div class="app-container">
    <CardSearch
      ref="refSearch"
      :config="cardSearchConfig"
    />

    <!-- 操作按钮区 -->
    <div class="action-bar">
      <el-button type="primary" @click="handleAdd">新增</el-button>
    </div>
    <CardTable
      :config="TableConfig"
      class="card-table"
    />

    <!-- 查看视频对话框 -->
    <el-dialog
      title="查看视频监控"
      v-model="viewDialogVisible"
      width="700px"
      destroy-on-close
    >
      <div v-if="currentVideo" class="video-detail">
        <div class="detail-item">
          <span class="label">设备名称：</span>
          <span class="value">{{ currentVideo.name }}</span>
        </div>
        <div class="detail-item">
          <span class="label">设备编号：</span>
          <span class="value">{{ currentVideo.serialNumber }}</span>
        </div>
        <div class="detail-item">
          <span class="label">视频类型：</span>
          <span class="value">{{ getVideoTypeName(currentVideo.videoType) }}</span>
        </div>
        <div class="detail-item">
          <span class="label">经纬度：</span>
          <span class="value">{{ getCoordinateText(currentVideo) }}</span>
        </div>
        <div class="detail-item">
          <span class="label">地址：</span>
          <span class="value">{{ currentVideo.location || '-' }}</span>
        </div>
        <div class="detail-item">
          <span class="label">更新时间：</span>
          <span class="value">{{ formatUpdateTime(currentVideo.updateTime) }}</span>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="viewDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 编辑视频对话框 -->
    <el-dialog
      title="编辑视频监控"
      v-model="editDialogVisible"
      width="700px"
      destroy-on-close
    >
      <el-form v-if="currentVideo" :model="currentVideo" label-width="100px">
        <el-form-item label="设备名称">
          <el-input v-model="currentVideo.name" placeholder="请输入设备名称"></el-input>
        </el-form-item>
        <el-form-item label="设备编号">
          <el-input v-model="currentVideo.serialNumber" placeholder="请输入设备编号"></el-input>
        </el-form-item>
        <el-form-item label="视频类型">
          <el-select v-model="currentVideo.videoType" placeholder="请选择视频类型" style="width: 100%">
            <el-option label="海康" value="1"></el-option>
            <el-option label="大华" value="2"></el-option>
            <el-option label="其他" value="3"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="经度">
          <el-input v-model="currentVideo.longitude" placeholder="请输入经度"></el-input>
        </el-form-item>
        <el-form-item label="纬度">
          <el-input v-model="currentVideo.latitude" placeholder="请输入纬度"></el-input>
        </el-form-item>
        <el-form-item label="地址">
          <el-input v-model="currentVideo.location" placeholder="请输入地址"></el-input>
        </el-form-item>
        <el-form-item label="地图选点">
          <div id="edit-map-container" ref="editMapRef" style="width: 100%; height: 300px; border: 1px solid #ddd;"></div>
          <div class="map-tip">点击地图可获取经纬度信息</div>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSaveEdit">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 新增视频对话框 -->
    <el-dialog
      title="新增视频监控"
      v-model="addDialogVisible"
      width="700px"
      destroy-on-close
    >
      <el-form v-if="currentVideo" :model="currentVideo" label-width="100px">
        <el-form-item label="设备名称" prop="name">
          <el-input v-model="currentVideo.name" placeholder="请输入设备名称"></el-input>
        </el-form-item>
        <el-form-item label="设备编号" prop="serialNumber">
          <el-input v-model="currentVideo.serialNumber" placeholder="请输入设备编号"></el-input>
        </el-form-item>
        <el-form-item label="视频类型" prop="videoType">
          <el-select v-model="currentVideo.videoType" placeholder="请选择视频类型" style="width: 100%">
            <el-option label="海康" value="1"></el-option>
            <el-option label="大华" value="2"></el-option>
            <el-option label="其他" value="3"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="经度" prop="longitude">
          <el-input v-model="currentVideo.longitude" placeholder="请输入经度"></el-input>
        </el-form-item>
        <el-form-item label="纬度" prop="latitude">
          <el-input v-model="currentVideo.latitude" placeholder="请输入纬度"></el-input>
        </el-form-item>
        <el-form-item label="地址" prop="location">
          <el-input v-model="currentVideo.location" placeholder="请输入地址"></el-input>
        </el-form-item>
        <el-form-item label="地图选点">
          <div id="map-container" ref="mapRef" style="width: 100%; height: 300px; border: 1px solid #ddd;"></div>
          <div class="map-tip">点击地图可获取经纬度信息</div>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="addDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSaveAdd">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { Refresh, View, Edit, Delete } from '@element-plus/icons-vue'
import { ICONS } from '@/common/constans/common'
import { getVideoMonitoringList, deleteVideo, saveVideo } from '@/api/video'
import { ElMessageBox, ElMessage } from 'element-plus'
import useAmap from '@/hooks/amap/useAmap'
import { getMapLocationImageUrl } from '@/utils/URLHelper'

const refSearch = ref<ICardSearchIns>()

// 对话框状态
const viewDialogVisible = ref(false)
const editDialogVisible = ref(false)
const addDialogVisible = ref(false)
const currentVideo = ref<any>(null)

// 地图相关
const mapRef = ref<HTMLDivElement | null>(null)
const editMapRef = ref<HTMLDivElement | null>(null)
let map: any = null
let editMap: any = null
let marker: any = null
let editMarker: any = null

// 初始化地图
const initMapForAdd = async () => {
  if (!mapRef.value) return

  const { initAMap, setMarker } = useAmap()

  // 初始化地图
  map = await initAMap('map-container', {
    center: [116.397428, 39.90923], // 默认中心点
    events: {
      click: (e: any) => {
        // 获取点击位置的经纬度
        const position = e.lnglat
        currentVideo.value.longitude = position.lng
        currentVideo.value.latitude = position.lat

        // 更新标记位置
        if (marker) {
          marker.setPosition([position.lng, position.lat])
        } else {
          marker = setMarker([position.lng, position.lat], {
            icon: getMapLocationImageUrl()
          })
        }
      }
    }
  })
}

// 初始化编辑地图
const initMapForEdit = async () => {
  if (!editMapRef.value) return

  const { initAMap, setMarker } = useAmap()

  // 初始化地图
  editMap = await initAMap('edit-map-container', {
    center: currentVideo.value.longitude && currentVideo.value.latitude
      ? [currentVideo.value.longitude, currentVideo.value.latitude]
      : [116.397428, 39.90923],
    events: {
      click: (e: any) => {
        // 获取点击位置的经纬度
        const position = e.lnglat
        currentVideo.value.longitude = position.lng
        currentVideo.value.latitude = position.lat

        // 更新标记位置
        if (editMarker) {
          editMarker.setPosition([position.lng, position.lat])
        } else {
          editMarker = setMarker([position.lng, position.lat], {
            icon: getMapLocationImageUrl()
          })
        }
      }
    }
  })

  // 如果有经纬度，添加标记
  if (currentVideo.value.longitude && currentVideo.value.latitude) {
    const { setMarker } = useAmap()
    editMarker = setMarker(
      [currentVideo.value.longitude, currentVideo.value.latitude],
      { icon: getMapLocationImageUrl() }
    )
  }
}

// 搜索配置
const cardSearchConfig = ref<ISearch>({
  filters: [
    { label: '设备名称', field: 'name', type: 'input' },
    { label: '设备编号', field: 'serialNumber', type: 'input' },
    { label: '地址', field: 'location', type: 'input' }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          icon: ICONS.QUERY,
          click: () => refreshData()
        },
        {
          type: 'default',
          perm: true,
          text: '重置',
          svgIcon: shallowRef(Refresh),
          click: () => {
            refSearch.value?.resetForm()
            refreshData()
          }
        }
      ]
    }
  ]
})

// 表格配置
const TableConfig = reactive<ICardTable>({
  title: '视频监控',
  defaultExpandAll: true,
  indexVisible: true,
  rowKey: 'id',
  columns: [
    { label: '设备名称', prop: 'name' },
    { label: '设备编号', prop: 'serialNumber' },
    { label: '视频类型', prop: 'videoType', formatter: (row: any) => {
      const videoTypes = {
        '1': '海康',
        '2': '大华',
        '3': '其他'
      }
      return videoTypes[row.videoType] || row.videoType
    }},
    { label: '更新时间', prop: 'updateTime', formatter: (row: any) => {
      if (!row.updateTime) return '-'
      const date = new Date(row.updateTime)
      return date.toLocaleString()
    }},
    { label: '经纬度', prop: 'coordinate', formatter: (row: any) => {
      if (row.longitude && row.latitude) {
        return `${row.longitude}, ${row.latitude}`
      } else if (row.longitude) {
        return `${row.longitude}, -`
      } else if (row.latitude) {
        return `-, ${row.latitude}`
      }
      return '-'
    }},
    { label: '地址', prop: 'location', formatter: (row: any) => {
      return row.location || '-'
    }}
  ],
  dataList: [],
  loading: false,
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page
      TableConfig.pagination.limit = size
      refreshData()
    }
  },
  operations: [
    {
      perm: true,
      text: '查看',
      isTextBtn: true,
      svgIcon: shallowRef(View),
      click: (row) => handleView(row)
    },
    {
      perm: true,
      text: '编辑',
      isTextBtn: true,
      svgIcon: shallowRef(Edit),
      click: (row) => handleEdit(row)
    },
    {
      perm: true,
      text: '删除',
      isTextBtn: true,
      svgIcon: shallowRef(Delete),
      click: (row) => handleDelete(row)
    }
  ]
})

// 新增视频
const handleAdd = () => {
  // 初始化一个空的视频对象
  currentVideo.value = {
    name: '',
    serialNumber: '',
    videoType: '1', // 默认为自建
    longitude: '',
    latitude: '',
    location: '',
    url: '',
    // 其他必要字段可以根据需要添加
  }
  addDialogVisible.value = true

  // 在对话框打开后初始化地图
  setTimeout(() => {
    initMapForAdd()
  }, 300)
}

// 保存新增
const handleSaveAdd = async () => {
  if (!currentVideo.value) return

  // 验证必填字段
  if (!currentVideo.value.name) {
    ElMessage.warning('请输入设备名称')
    return
  }
  if (!currentVideo.value.serialNumber) {
    ElMessage.warning('请输入设备编号')
    return
  }

  try {
    // 准备保存的数据
    const saveData = {
      name: currentVideo.value.name,
      serialNumber: currentVideo.value.serialNumber,
      videoType: currentVideo.value.videoType,
      longitude: currentVideo.value.longitude,
      latitude: currentVideo.value.latitude,
      location: currentVideo.value.location,
      // 其他必要字段
      url: currentVideo.value.url || '',
      // 使用默认值或从配置中获取
      projectId: '', // 可以根据需要设置默认项目ID
      tenantId: '', // 可以根据需要设置默认租户ID
      groupId: '' // 可以根据需要设置默认分组ID
    }

    // 调用保存API
    console.log('新增视频数据:', saveData)
    await saveVideo(saveData)

    ElMessage.success('新增成功')
    addDialogVisible.value = false

    // 刷新数据列表
    refreshData()
  } catch (error) {
    console.error('新增失败:', error)
    ElMessage.error('新增失败，请重试')
  }
}

// 查看视频
const handleView = (row: any) => {
  // 设置当前视频并打开查看对话框
  currentVideo.value = row
  viewDialogVisible.value = true
}

// 编辑视频
const handleEdit = (row: any) => {
  // 设置当前视频并打开编辑对话框
  currentVideo.value = JSON.parse(JSON.stringify(row)) // 深拷贝，避免直接修改原数据
  editDialogVisible.value = true
  console.log('编辑视频详情:', row)

  // 在对话框打开后初始化地图
  setTimeout(() => {
    initMapForEdit()
  }, 300)
}

// 保存编辑
const handleSaveEdit = async () => {
  if (!currentVideo.value) return

  try {
    // 准备保存的数据
    const saveData = {
      id: currentVideo.value.id,
      name: currentVideo.value.name,
      serialNumber: currentVideo.value.serialNumber,
      videoType: currentVideo.value.videoType,
      longitude: currentVideo.value.longitude,
      latitude: currentVideo.value.latitude,
      location: currentVideo.value.location,
      // 其他必要字段
      url: currentVideo.value.url || '',
      projectId: currentVideo.value.projectId || '',
      tenantId: currentVideo.value.tenantId || '',
      groupId: currentVideo.value.groupId || ''
    }

    // 调用保存API
    console.log('保存视频数据:', saveData)
    await saveVideo(saveData)

    ElMessage.success('保存成功')
    editDialogVisible.value = false

    // 刷新数据列表
    refreshData()
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败，请重试')
  }
}

// 获取视频类型名称
const getVideoTypeName = (type: string) => {
  const videoTypes = {
    '1': '海康',
    '2': '大华',
    '3': '其他'
  }
  return videoTypes[type] || type
}

// 获取经纬度文本
const getCoordinateText = (row: any) => {
  if (row.longitude && row.latitude) {
    return `${row.longitude}, ${row.latitude}`
  } else if (row.longitude) {
    return `${row.longitude}, -`
  } else if (row.latitude) {
    return `-, ${row.latitude}`
  }
  return '-'
}

// 格式化更新时间
const formatUpdateTime = (timestamp: number) => {
  if (!timestamp) return '-'
  const date = new Date(timestamp)
  return date.toLocaleString()
}

// 删除视频
const handleDelete = (row: any) => {
  ElMessageBox.confirm('确认删除该视频监控设备吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await deleteVideo(row.id)
      ElMessage.success('删除成功')
      refreshData()
    } catch (error) {
      ElMessage.error('删除失败')
    }
  }).catch(() => {})
}

// 刷新数据
const refreshData = async () => {
  TableConfig.loading = true
  try {
    const params = {
      page: TableConfig.pagination.page || 1,
      size: TableConfig.pagination.limit || 20,
      name: refSearch.value?.queryParams?.name || '',
      serialNumber: refSearch.value?.queryParams?.serialNumber || '',
      location: refSearch.value?.queryParams?.location || ''
    }

    const res = await getVideoMonitoringList(params)
    console.log('视频监控数据 - 完整响应:', res)
    console.log('视频监控数据 - data:', res.data)

    // 直接将数据赋值给 TableConfig.dataList
    if (res.data && Array.isArray(res.data)) {
      TableConfig.dataList = res.data
      TableConfig.pagination.total = res.data.length
      console.log('数据已赋值 - 新的TableConfig:', TableConfig)
    } else if (res.data && Array.isArray(res.data.content)) {
      // 兼容分页格式
      TableConfig.dataList = res.data.content
      TableConfig.pagination.total = res.data.totalElements || 0
      console.log('数据已赋值(分页格式) - 新的TableConfig:', TableConfig)
    } else {
      console.error('数据结构不符合预期:', res.data)
      TableConfig.dataList = []
      TableConfig.pagination.total = 0
    }
  } catch (error) {
    ElMessage.error('获取数据失败')
  }
  TableConfig.loading = false
}

// 初始化
onMounted(async () => {
  await refreshData()
})
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  background-color: #f5f7fa;
  height: 100%;
}

.card-table {
  height: calc(100vh - 200px);
  margin-top: 10px;
}

/* 操作按钮区样式 */
.action-bar {
  display: flex;
  justify-content: flex-end;
  margin: 10px 0;
}

/* 地图提示样式 */
.map-tip {
  margin-top: 5px;
  color: #909399;
  font-size: 12px;
  text-align: center;
}

/* 对话框样式 */
.video-detail {
  padding: 20px;

  .detail-item {
    margin-bottom: 15px;
    display: flex;

    .label {
      width: 100px;
      color: #606266;
      font-weight: 500;
    }

    .value {
      flex: 1;
      color: #333;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  width: 100%;
}
</style>
