package org.thingsboard.server.dao.smartService.call;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.smartService.call.CallLog;
import org.thingsboard.server.dao.sql.smartService.call.CallLogMapper;

import java.util.*;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-10-27
 */
@Slf4j
@Service
@Transactional
public class CallLogServiceImpl implements CallLogService {
    @Autowired
    private CallLogMapper callLogMapper;


    @Override
    public PageData getList(String phone, Long startTime, Long endTime, String source, String area, String type, String topic, String seatsId, String status, String direction, int page, int size, String tenantId) {
        List<CallLog> knowledgeNotices = callLogMapper.getList(phone, startTime, endTime, source, area, type, topic, seatsId, status, direction, page, size, tenantId);

        int total = callLogMapper.getListCount(phone, startTime, endTime, source, area, type, topic, seatsId, status, direction, tenantId);

        return new PageData(total, knowledgeNotices);
    }

    @Override
    public List getQueueMonitor(String day) {
        List<CallLog> callLogList = callLogMapper.getQueueMonitor(day);
        List<Map<String, Integer>> result = new ArrayList<>();
        Map<String, Integer> stasticsMap;
        for (int i = 0; i < 24; i++) {
            stasticsMap = new HashMap();
            stasticsMap.put("hour", i);
            stasticsMap.put("all", 0);
            stasticsMap.put("on", 0);
            stasticsMap.put("off", 0);
            stasticsMap.put("busy", 0);
            result.add(stasticsMap);
        }
        for (CallLog callLog : callLogList) {
            stasticsMap = result.get(callLog.getCallTime().getHours());
            stasticsMap.put("all", stasticsMap.get("all") + 1);
            switch (callLog.getStatus()) {
                case "1": stasticsMap.put("on", stasticsMap.get("on") + 1); break;
                case "2": stasticsMap.put("busy", stasticsMap.get("busy") + 1); break;
            }
            // 未接通数 总数 - 接通数 - 遇忙数
            stasticsMap.put("off", stasticsMap.get("all") - stasticsMap.get("on") - stasticsMap.get("busy"));
        }

        return result;
    }
}
