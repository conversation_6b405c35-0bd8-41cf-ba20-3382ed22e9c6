package org.thingsboard.server.dao.smartPipe;

import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.DTO.PipeWorkOrderDTO;
import org.thingsboard.server.dao.model.request.PipeWorkOrderRequest;
import org.thingsboard.server.dao.util.imodel.query.workOrder.PipeWorkOrderSaveRequest;
import org.thingsboard.server.dao.util.imodel.query.workOrder.WorkOrderSaveRequest;

import java.util.List;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2023-05-26
 */
public interface PipeWorkOrderService {

    WorkOrderSaveRequest save(PipeWorkOrderSaveRequest pipeWorkOrderSaveRequest);

    PageData<PipeWorkOrderDTO> getList(PipeWorkOrderRequest request);

    void delete(List<String> ids);

}
