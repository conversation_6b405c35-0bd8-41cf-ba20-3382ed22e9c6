package org.thingsboard.server.dao.smartService.portal;

import org.thingsboard.server.dao.model.sql.smartService.portal.SsPortalHomeInfo;
import org.thingsboard.server.dao.util.imodel.query.smartService.portal.SsPortalHomeInfoSaveRequest;

public interface SsPortalHomeInfoService {
    SsPortalHomeInfo get(String tenantId);

    SsPortalHomeInfo save(SsPortalHomeInfoSaveRequest entity);

    boolean update(SsPortalHomeInfo entity);

    boolean delete(String id);

}
