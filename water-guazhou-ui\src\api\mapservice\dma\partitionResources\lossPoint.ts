import { request } from '@/plugins/axios'

/**
 * 查询漏点列表
 * @param params
 * @returns
 */
export const GetDmaPartitionLossPoint = (
  params: IQueryPagerParams & {
    findDate?: string
    partitionId?: string
  }
) => {
  return request({
    url: '/api/spp/dma/partition/lossPoint/list',
    method: 'get',
    params
  })
}
/**
 * 添加漏点
 * @param params
 * @returns
 */
export const AddDmaPartitionLossPoint = (params: {
  id?: string
  partitionId?: string
  findDate?: string
  num?: string
}) => {
  return request({
    url: '/api/spp/dma/partition/lossPoint',
    method: 'post',
    data: params
  })
}
/**
 * 删除漏点
 * @param ids
 * @returns
 */
export const DeleteDmaPartitionLossPoint = (ids: string[]) => {
  return request({
    url: '/api/spp/dma/partition/lossPoint',
    method: 'delete',
    data: ids
  })
}
