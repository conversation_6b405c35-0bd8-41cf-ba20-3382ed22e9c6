const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["static/js/geomasync-wZnXylSS.js","static/js/pe-B8dP0-Ut.js","static/js/index-r0dFAfgr.js","static/css/index-ByiGXvnH.css","static/js/Point-WxyopZva.js","static/js/MapView-DaoQedLH.js","static/js/widget-BcWKanF2.js","static/js/geometryEngineAsync-Cl4J6jqd.js","static/js/executionError-BOo4jP8A.js","static/js/portalUtils-PE38F3vi.js","static/js/arcadeTimeUtils-CyWQANWo.js","static/js/number-Q7BpbuNy.js","static/js/geometryEngine-OGzB5MRq.js","static/js/geometryEngineBase-BhsKaODW.js","static/js/hydrated-DLkO5ZPr.js","static/js/arcadeAsyncRuntime-DZV1z6mf.js","static/js/featureSetUtils-CBB3vR3p.js","static/js/WhereClause-CNjGNHY9.js","static/js/SpatialFilter-LhUD8pWs.js","static/js/executeForIds-BLdIsxvI.js","static/js/featuresetbase-B-e-df0j.js","static/js/featuresetgeom-DuF4cn4O.js","static/js/featuresetstats-O0zjoCUA.js","static/js/featuresetstring-DacevaUZ.js"])))=>i.map(i=>d[i]);
import{cU as ta,w as he,v as ae,b as ie,u as we,dS as at,dE as jr,ar as de,ce as na,iI as sr,aI as Wr,iJ as ur}from"./MapView-DaoQedLH.js";import{T as ra,R as or,aO as lr,eZ as Kr,a3 as Ne,a4 as Yr,a5 as ia}from"./index-r0dFAfgr.js";import{c as q,u as aa,T as cr}from"./arcadeTimeUtils-CyWQANWo.js";import{t as h,e as c,s as ue,c as sn,p as un,l as on,u as sa}from"./executionError-BOo4jP8A.js";import{a3 as Oe,w as H,a2 as T,$ as ua,b as kt}from"./Point-WxyopZva.js";import{s as oa,p as Xr}from"./number-Q7BpbuNy.js";import{b as Qr}from"./widget-BcWKanF2.js";import{h as la,W as ca,U as ha}from"./pe-B8dP0-Ut.js";let se=class{constructor(e=[]){this._elements=e}length(){return this._elements.length}get(e){return this._elements[e]}toArray(){const e=[];for(let n=0;n<this.length();n++)e.push(this.get(n));return e}},je=class{constructor(){}};function ot(t,e,n){if(t instanceof je&&!(t instanceof Le)){const r=new Le;return r.fn=t,r.parameterEvaluator=n,r.context=e,r}return t}let We=class extends je{constructor(e){super(),this.fn=e}createFunction(e){return(...n)=>this.fn(e,{preparsed:!0,arguments:n})}call(e,n){return this.fn(e,n)}marshalledCall(e,n,r,a){return a(e,n,(u,s,i)=>{i=i.map(l=>l instanceof je&&!(l instanceof Le)?ot(l,e,a):l);const o=this.call(r,{args:i});return Oe(o)?o.then(l=>ot(l,r,a)):o})}},Le=class extends je{constructor(){super(...arguments),this.fn=null,this.context=null}createFunction(e){return this.fn.createFunction(this.context)}call(e,n){return this.fn.marshalledCall(e,n,this.context,this.parameterEvaluator)}marshalledCall(e,n,r){return this.fn.marshalledCall(e,n,this.context,this.parameterEvaluator)}},Ye=class ei extends se{constructor(e,n,r,a,u,s){super(e),this._lazyPt=[],this._hasZ=!1,this._hasM=!1,this._spRef=n,this._hasZ=r,this._hasM=a,this._cacheId=u,this._partId=s}get(e){if(this._lazyPt[e]===void 0){const n=this._elements[e];if(n===void 0)return;const r=this._hasZ,a=this._hasM;let u=null;u=r&&!a?new H(n[0],n[1],n[2],void 0,this._spRef):a&&!r?new H(n[0],n[1],void 0,n[2],this._spRef):r&&a?new H(n[0],n[1],n[2],n[3],this._spRef):new H(n[0],n[1],this._spRef),u.cache._arcadeCacheId=this._cacheId.toString()+"-"+this._partId.toString()+"-"+e.toString(),this._lazyPt[e]=u}return this._lazyPt[e]}equalityTest(e){return e===this||e!==null&&e instanceof ei&&e.getUniqueHash()===this.getUniqueHash()}getUniqueHash(){return this._cacheId.toString()+"-"+this._partId.toString()}},En=class ti extends se{constructor(e,n,r,a,u){super(e),this._lazyPath=[],this._hasZ=!1,this._hasM=!1,this._hasZ=r,this._hasM=a,this._spRef=n,this._cacheId=u}get(e){if(this._lazyPath[e]===void 0){const n=this._elements[e];if(n===void 0)return;this._lazyPath[e]=new Ye(n,this._spRef,this._hasZ,this._hasM,this._cacheId,e)}return this._lazyPath[e]}equalityTest(e){return e===this||e!==null&&e instanceof ti&&e.getUniqueHash()===this.getUniqueHash()}getUniqueHash(){return this._cacheId.toString()}};var hr,fr;function ko(t){return ta.fromJSON(t.toJSON())}function fa(t){return t.toJSON?t.toJSON():t}function Bo(t){return typeof t=="string"||t instanceof String}function To(t){return typeof t=="number"}function dr(t){return t instanceof Date}function mr(t){return t instanceof q}function _o(t,e){return t===e||!(!dr(t)&&!mr(t)||!dr(e)&&!mr(e))&&t.getTime()===e.getTime()}function Mo(t){if(t==null)return null;if(typeof t=="number")return t;switch(t.toLowerCase()){case"meters":case"meter":return 109404;case"miles":case"mile":return 109439;case"kilometers":case"kilometer":case"km":return 109414}return null}function $o(t){if(t==null)return null;switch(t.type){case"polygon":case"multipoint":case"polyline":return t.extent;case"point":return new he({xmin:t.x,ymin:t.y,xmax:t.x,ymax:t.y,spatialReference:t.spatialReference});case"extent":return t}return null}function No(t){if(t==null)return null;if(typeof t=="number"||typeof t=="number")return t;switch(t.toLowerCase()){case"meters":case"meter":return 9001;case"miles":case"mile":return 9093;case"kilometers":case"kilometer":case"km":return 9036}return null}(function(t){t[t.Standardised=0]="Standardised",t[t.StandardisedNoInterval=1]="StandardisedNoInterval",t[t.SqlServer=2]="SqlServer",t[t.Oracle=3]="Oracle",t[t.Postgres=4]="Postgres",t[t.PGDB=5]="PGDB",t[t.FILEGDB=6]="FILEGDB",t[t.NotEvaluated=7]="NotEvaluated"})(hr||(hr={})),function(t){t[t.InFeatureSet=0]="InFeatureSet",t[t.NotInFeatureSet=1]="NotInFeatureSet",t[t.Unknown=2]="Unknown"}(fr||(fr={}));const Lo=1e3,Ro={point:"point",polygon:"polygon",polyline:"polyline",multipoint:"multipoint",extent:"extent",esriGeometryPoint:"point",esriGeometryPolygon:"polygon",esriGeometryPolyline:"polyline",esriGeometryMultipoint:"multipoint",esriGeometryEnvelope:"extent",envelope:"extent"},pr={point:"esriGeometryPoint",polygon:"esriGeometryPolygon",polyline:"esriGeometryPolyline",multipoint:"esriGeometryMultipoint",extent:"esriGeometryEnvelope",esriGeometryPoint:"esriGeometryPoint",esriGeometryPolygon:"esriGeometryPolygon",esriGeometryPolyline:"esriGeometryPolyline",esriGeometryMultipoint:"esriGeometryMultipoint",esriGeometryEnvelope:"esriGeometryEnvelope",envelope:"esriGeometryEnvelope"},Dr={"small-integer":"esriFieldTypeSmallInteger",integer:"esriFieldTypeInteger",long:"esriFieldTypeLong",single:"esriFieldTypeSingle",double:"esriFieldTypeDouble",string:"esriFieldTypeString",date:"esriFieldTypeDate",oid:"esriFieldTypeOID",geometry:"esriFieldTypeGeometry",blob:"esriFieldTypeBlob",raster:"esriFieldTypeRaster",guid:"esriFieldTypeGUID","global-id":"esriFieldTypeGlobalID",xml:"eesriFieldTypeXML",esriFieldTypeSmallInteger:"esriFieldTypeSmallInteger",esriFieldTypeInteger:"esriFieldTypeInteger",esriFieldTypeLong:"esriFieldTypeLong",esriFieldTypeSingle:"esriFieldTypeSingle",esriFieldTypeDouble:"esriFieldTypeDouble",esriFieldTypeString:"esriFieldTypeString",esriFieldTypeDate:"esriFieldTypeDate",esriFieldTypeOID:"esriFieldTypeOID",esriFieldTypeGeometry:"esriFieldTypeGeometry",esriFieldTypeBlob:"esriFieldTypeBlob",esriFieldTypeRaster:"esriFieldTypeRaster",esriFieldTypeGUID:"esriFieldTypeGUID",esriFieldTypeGlobalID:"esriFieldTypeGlobalID",esriFieldTypeXML:"eesriFieldTypeXML"};function Po(t){return t===void 0?"":t=(t=(t=t.replace(/\/featureserver\/[0-9]*/i,"/FeatureServer")).replace(/\/mapserver\/[0-9]*/i,"/MapServer")).split("?")[0]}function Oo(t,e){e||(e={}),typeof e=="function"&&(e={cmp:e});const n=typeof e.cycles=="boolean"&&e.cycles,r=e.cmp&&(a=e.cmp,function(s){return function(i,o){const l={key:i,value:s[i]},m={key:o,value:s[o]};return a(l,m)}});var a;const u=[];return function s(i){if(i&&i.toJSON&&typeof i.toJSON=="function"&&(i=i.toJSON()),i===void 0)return;if(typeof i=="number")return isFinite(i)?""+i:"null";if(typeof i!="object")return JSON.stringify(i);let o,l;if(Array.isArray(i)){for(l="[",o=0;o<i.length;o++)o&&(l+=","),l+=s(i[o])||"null";return l+"]"}if(i===null)return"null";if(u.includes(i)){if(n)return JSON.stringify("__cycle__");throw new TypeError("Converting circular structure to JSON")}const m=u.push(i)-1,f=Object.keys(i).sort(r&&r(i));for(l="",o=0;o<f.length;o++){const p=f[o],g=s(i[p]);g&&(l&&(l+=","),l+=JSON.stringify(p)+":"+g)}return u.splice(m,1),"{"+l+"}"}(t)}let Zt=class{constructor(e){this.source=e}},ni=class{constructor(e){this.value=e}},ri=class{constructor(e){this.value=e}};const lt=ri,me=ni,C={type:"VOID"},Ae={type:"BREAK"},pt={type:"CONTINUE"};function Ge(t,e,n){return e===""||e==null||e===n||e===n?t:t=t.split(e).join(n)}function K(t){return t instanceof je}function qn(t){return t instanceof Zt}function ze(t){return!!b(t)||!!G(t)||!!j(t)||!!U(t)||t===null||t===C||typeof t=="number"}function M(t,e){return t===void 0?e:t}function Jn(t){return t==null?"":k(t)||N(t)?"Array":j(t)?"Date":b(t)?"String":U(t)?"Boolean":G(t)?"Number":(t==null?void 0:t.declaredClass)==="esri.arcade.Attachment"?"Attachment":(t==null?void 0:t.declaredClass)==="esri.arcade.Portal"?"Portal":(t==null?void 0:t.declaredClass)==="esri.arcade.Dictionary"?"Dictionary":t instanceof Zt?"Module":Q(t)?"Feature":t instanceof H?"Point":t instanceof ae?"Polygon":t instanceof ie?"Polyline":t instanceof we?"Multipoint":t instanceof he?"Extent":K(t)?"Function":jt(t)?"FeatureSet":Wt(t)?"FeatureSetCollection":t===C?"":typeof t=="number"&&isNaN(t)?"Number":"Unrecognised Type"}function b(t){return typeof t=="string"||t instanceof String}function U(t){return typeof t=="boolean"}function G(t){return typeof t=="number"}function _e(t){return typeof t=="number"&&isFinite(t)&&Math.floor(t)===t}function k(t){return t instanceof Array}function Q(t){return(t==null?void 0:t.arcadeDeclaredClass)==="esri.arcade.Feature"}function jt(t){return(t==null?void 0:t.declaredRootClass)==="esri.arcade.featureset.support.FeatureSet"}function Wt(t){return(t==null?void 0:t.declaredRootClass)==="esri.arcade.featureSetCollection"}function N(t){return t instanceof se}function j(t){return t instanceof q}function bn(t){return t!=null&&typeof t=="object"}function Ot(t){return t instanceof Date}function D(t,e,n,r,a){if(t.length<e||t.length>n)throw new h(r,c.WrongNumberOfParameters,a)}function Sn(t){return t<0?-Math.round(-t):Math.round(t)}function it(){let t=Date.now();return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,e=>{const n=(t+16*Math.random())%16|0;return t=Math.floor(t/16),(e==="x"?n:3&n|8).toString(16)})}function Vn(t,e){return isNaN(t)||e==null||e===""?t.toString():(e=Ge(e,"‰",""),e=Ge(e,"¤",""),oa(t,{pattern:e}))}function Kt(t,e){return e==null||e===""?t.toISOString(!0):t.toFormat(Hn(e),{locale:Qr(),numberingSystem:"latn"})}function Hn(t){t=t.replace(/LTS|LT|LL?L?L?|l{1,4}/g,"[$&]");let e="";const n=/(\[[^\[]*\])|(\\)?([Hh]mm(ss)?|Mo|MM?M?M?|Do|DDDo|DD?D?D?|ddd?d?|do?|w[o|w]?|W[o|W]?|Qo?|N{1,5}|YYYYYY|YYYYY|YYYY|YY|y{2,4}|yo?|gg(ggg?)?|GG(GGG?)?|e|E|a|A|hh?|HH?|kk?|mm?|ss?|S{1,9}|x|X|zz?|ZZ?|.)/g;for(const r of t.match(n)||[])switch(r){case"D":e+="d";break;case"DD":e+="dd";break;case"DDD":e+="o";break;case"d":e+="c";break;case"ddd":e+="ccc";break;case"dddd":e+="cccc";break;case"M":e+="L";break;case"MM":e+="LL";break;case"MMM":e+="LLL";break;case"MMMM":e+="LLLL";break;case"YY":e+="yy";break;case"Y":case"YYYY":e+="yyyy";break;case"Q":e+="q";break;case"Z":e+="ZZ";break;case"ZZ":e+="ZZZ";break;case"S":e+="'S'";break;case"SS":e+="'SS'";break;case"SSS":e+="u";break;case"A":case"a":e+="a";break;case"m":case"mm":case"h":case"hh":case"H":case"HH":case"s":case"ss":case"X":case"x":e+=r;break;default:r.length>=2&&r.slice(0,1)==="["&&r.slice(-1)==="]"?e+=`'${r.slice(1,-1)}'`:e+=`'${r}'`}return e}function W(t,e,n){switch(n){case">":return t>e;case"<":return t<e;case">=":return t>=e;case"<=":return t<=e}return!1}function Zn(t,e,n){if(t===null){if(e===null||e===C)return W(null,null,n);if(G(e))return W(0,e,n);if(b(e)||U(e))return W(0,d(e),n);if(j(e))return W(0,e.toNumber(),n)}if(t===C){if(e===null||e===C)return W(null,null,n);if(G(e))return W(0,e,n);if(b(e)||U(e))return W(0,d(e),n);if(j(e))return W(0,e.toNumber(),n)}else if(G(t)){if(G(e))return W(t,e,n);if(U(e))return W(t,d(e),n);if(e===null||e===C)return W(t,0,n);if(b(e))return W(t,d(e),n);if(j(e))return W(t,e.toNumber(),n)}else if(b(t)){if(b(e))return W(A(t),A(e),n);if(j(e))return W(d(t),e.toNumber(),n);if(G(e))return W(d(t),e,n);if(e===null||e===C)return W(d(t),0,n);if(U(e))return W(d(t),d(e),n)}else if(j(t)){if(j(e))return W(t.toNumber(),e.toNumber(),n);if(e===null||e===C)return W(t.toNumber(),0,n);if(G(e))return W(t.toNumber(),e,n);if(U(e)||b(e))return W(t.toNumber(),d(e),n)}else if(U(t)){if(U(e))return W(t,e,n);if(G(e))return W(d(t),d(e),n);if(j(e))return W(d(t),e.toNumber(),n);if(e===null||e===C)return W(d(t),0,n);if(b(e))return W(d(t),d(e),n)}return!!xe(t,e)&&(n==="<="||n===">=")}function xe(t,e){if(t===e||t===null&&e===C||e===null&&t===C)return!0;if(j(t)&&j(e))return t.equals(e);if(t instanceof En||t instanceof Ye)return t.equalityTest(e);if(t instanceof H&&e instanceof H){const n=t.cache._arcadeCacheId,r=e.cache._arcadeCacheId;if(n!=null)return n===r}return!!(bn(t)&&bn(e)&&(t._arcadeCacheId===e._arcadeCacheId&&t._arcadeCacheId!==void 0&&t._arcadeCacheId!==null||t._underlyingGraphic===e._underlyingGraphic&&t._underlyingGraphic!==void 0&&t._underlyingGraphic!==null))}function A(t,e){if(b(t))return t;if(t===null)return"";if(G(t))return Vn(t,e);if(U(t))return t.toString();if(j(t))return Kt(t,e);if(t instanceof T)return JSON.stringify(t.toJSON());if(k(t)){const n=[];for(let r=0;r<t.length;r++)n[r]=Ut(t[r]);return"["+n.join(",")+"]"}if(t instanceof se){const n=[];for(let r=0;r<t.length();r++)n[r]=Ut(t.get(r));return"["+n.join(",")+"]"}return t!==null&&typeof t=="object"&&t.castToText!==void 0?t.castToText():K(t)?"object, Function":t===C?"":qn(t)?"object, Module":""}function Je(t){const e=[];if(!k(t))return null;if(t instanceof se){for(let n=0;n<t.length();n++)e[n]=d(t.get(n));return e}for(let n=0;n<t.length;n++)e[n]=d(t[n]);return e}function pe(t,e,n=!1){if(b(t))return t;if(t===null)return"";if(G(t))return Vn(t,e);if(U(t))return t.toString();if(j(t))return Kt(t,e);if(t instanceof T)return t instanceof he?'{"xmin":'+t.xmin.toString()+',"ymin":'+t.ymin.toString()+","+(t.hasZ?'"zmin":'+t.zmin.toString()+",":"")+(t.hasM?'"mmin":'+t.mmin.toString()+",":"")+'"xmax":'+t.xmax.toString()+',"ymax":'+t.ymax.toString()+","+(t.hasZ?'"zmax":'+t.zmax.toString()+",":"")+(t.hasM?'"mmax":'+t.mmax.toString()+",":"")+'"spatialReference":'+vn(t.spatialReference)+"}":vn(t.toJSON(),(r,a)=>r.key===a.key?0:r.key==="spatialReference"?1:a.key==="spatialReference"||r.key<a.key?-1:r.key>a.key?1:0);if(k(t)){const r=[];for(let a=0;a<t.length;a++)r[a]=Ut(t[a],n);return"["+r.join(",")+"]"}if(t instanceof se){const r=[];for(let a=0;a<t.length();a++)r[a]=Ut(t.get(a),n);return"["+r.join(",")+"]"}return t!==null&&typeof t=="object"&&t.castToText!==void 0?t.castToText(n):K(t)?"object, Function":t===C?"":qn(t)?"object, Module":""}function Ut(t,e=!1){if(t===null)return"null";if(U(t)||G(t)||b(t))return JSON.stringify(t);if(t instanceof T||t instanceof se||t instanceof Array)return pe(t,null,e);if(j(t))return JSON.stringify(e?t.getTime():Kt(t,""));if(t!==null&&typeof t=="object"){if(t.castToText!==void 0)return t.castToText(e)}else if(t===C)return"null";return"null"}function d(t,e){return G(t)?t:t===null||t===""?0:j(t)?NaN:U(t)?t?1:0:k(t)||t===""||t===void 0?NaN:e!==void 0&&b(t)?(e=Ge(e,"‰",""),e=Ge(e,"¤",""),Xr(t,{pattern:e})):t===C?0:Number(t)}function Y(t,e){if(j(t))return t;if(b(t)){const n=da(t,e);if(n)return q.dateTimeToArcadeDate(n)}return null}function da(t,e){const n=/ (\d\d)/,r=aa(e);let a=at.fromISO(t,{zone:r});return a.isValid||n.test(t)&&(t=t.replace(n,"T$1"),a=at.fromISO(t,{zone:e}),a.isValid)?a:null}function ct(t){return U(t)?t:b(t)?(t=t.toLowerCase())==="true":!!G(t)&&t!==0&&!isNaN(t)}function ee(t,e){return ra(t)?null:(t.spatialReference!==null&&t.spatialReference!==void 0||(t.spatialReference=e),t)}function Ve(t){if(t===null)return null;if(t instanceof H)return t.x==="NaN"||t.x===null||isNaN(t.x)?null:t;if(t instanceof ae){if(t.rings.length===0)return null;for(const e of t.rings)if(e.length>0)return t;return null}if(t instanceof ie){if(t.paths.length===0)return null;for(const e of t.paths)if(e.length>0)return t;return null}return t instanceof we?t.points.length===0?null:t:t instanceof he?t.xmin==="NaN"||t.xmin===null||isNaN(t.xmin)?null:t:null}function ii(t,e){if(!t||!t.domain)return e;let n=null;if(t.field.type==="string"||t.field.type==="esriFieldTypeString")e=A(e);else{if(e==null)return null;if(e==="")return e;e=d(e)}for(let r=0;r<t.domain.codedValues.length;r++){const a=t.domain.codedValues[r];a.code===e&&(n=a)}return n===null?e:n.name}function ai(t,e){if(!t||!t.domain)return e;let n=null;e=A(e);for(let r=0;r<t.domain.codedValues.length;r++){const a=t.domain.codedValues[r];a.name===e&&(n=a)}return n===null?e:n.code}function Yt(t,e,n=null,r=null){if(!e||!e.fields)return null;let a,u,s=null;for(let i=0;i<e.fields.length;i++){const o=e.fields[i];o.name.toLowerCase()===t.toString().toLowerCase()&&(s=o)}if(s===null)throw new h(null,c.FieldNotFound,null,{key:t});return r===null&&n&&e.typeIdField&&(r=n.hasField(e.typeIdField)?n.field(e.typeIdField):null),r!=null&&e.types.some(i=>i.id===r&&(a=i.domains&&i.domains[s.name],a&&a.type==="inherited"&&(a=gr(s.name,e),u=!0),!0)),u||a||(a=gr(t,e)),{field:s,domain:a}}function gr(t,e){let n;return e.fields.some(r=>(r.name.toLowerCase()===t.toLowerCase()&&(n=r.domain),!!n)),n}function vn(t,e){e||(e={}),typeof e=="function"&&(e={cmp:e});const n=typeof e.cycles=="boolean"&&e.cycles,r=e.cmp&&(a=e.cmp,function(s){return function(i,o){const l={key:i,value:s[i]},m={key:o,value:s[o]};return a(l,m)}});var a;const u=[];return function s(i){if(i&&i.toJSON&&typeof i.toJSON=="function"&&(i=i.toJSON()),i===void 0)return;if(typeof i=="number")return isFinite(i)?""+i:"null";if(typeof i!="object")return JSON.stringify(i);let o,l;if(Array.isArray(i)){for(l="[",o=0;o<i.length;o++)o&&(l+=","),l+=s(i[o])||"null";return l+"]"}if(i===null)return"null";if(u.includes(i)){if(n)return JSON.stringify("__cycle__");throw new TypeError("Converting circular structure to JSON")}const m=u.push(i)-1,f=Object.keys(i).sort(r&&r(i));for(l="",o=0;o<f.length;o++){const p=f[o],g=s(i[p]);g&&(l&&(l+=","),l+=JSON.stringify(p)+":"+g)}return u.splice(m,1),"{"+l+"}"}(t)}function O(t){if(t===null)return null;const e=[];for(const n of t)n&&n.arcadeDeclaredClass&&n.arcadeDeclaredClass==="esri.arcade.Feature"?e.push(n.geometry()):e.push(n);return e}function ht(t,e){if(!(e instanceof H))throw new h(null,c.InvalidParameter,null);t.push(e.hasZ?e.hasM?[e.x,e.y,e.z,e.m]:[e.x,e.y,e.z]:[e.x,e.y])}function In(t,e){if(k(t)||N(t)){let n=!1,r=!1,a=[],u=e;if(k(t)){for(const s of t)ht(a,s);a.length>0&&(u=t[0].spatialReference,n=t[0].hasZ,r=t[0].hasM)}else if(t instanceof Ye)a=t._elements,a.length>0&&(n=t._hasZ,r=t._hasM,u=t.get(0).spatialReference);else{if(!N(t))throw new h(null,c.InvalidParameter,null);for(const s of t.toArray())ht(a,s);a.length>0&&(u=t.get(0).spatialReference,n=t.get(0).hasZ===!0,r=t.get(0).hasM===!0)}return a.length===0?null:(jr(a,r,n)||(a=a.slice(0).reverse()),new ae({rings:[a],spatialReference:u,hasZ:n,hasM:r}))}return t}function Ct(t,e){if(k(t)||N(t)){let n=!1,r=!1,a=[],u=e;if(k(t)){for(const s of t)ht(a,s);a.length>0&&(u=t[0].spatialReference,n=t[0].hasZ===!0,r=t[0].hasM===!0)}else if(t instanceof Ye)a=t._elements,a.length>0&&(n=t._hasZ,r=t._hasM,u=t.get(0).spatialReference);else if(N(t)){for(const s of t.toArray())ht(a,s);a.length>0&&(u=t.get(0).spatialReference,n=t.get(0).hasZ===!0,r=t.get(0).hasM===!0)}return a.length===0?null:new ie({paths:[a],spatialReference:u,hasZ:n,hasM:r})}return t}function Lt(t,e){if(k(t)||N(t)){let n=!1,r=!1,a=[],u=e;if(k(t)){for(const s of t)ht(a,s);a.length>0&&(u=t[0].spatialReference,n=t[0].hasZ===!0,r=t[0].hasM===!0)}else if(t instanceof Ye)a=t._elements,a.length>0&&(n=t._hasZ,r=t._hasM,u=t.get(0).spatialReference);else if(N(t)){for(const s of t.toArray())ht(a,s);a.length>0&&(u=t.get(0).spatialReference,n=t.get(0).hasZ===!0,r=t.get(0).hasM===!0)}return a.length===0?null:new we({points:a,spatialReference:u,hasZ:n,hasM:r})}return t}function ma(t,e=!1){const n=[];if(t===null)return n;if(k(t)===!0){for(let r=0;r<t.length;r++){const a=A(t[r]);a===""&&e!==!0||n.push(a)}return n}if(t instanceof se){for(let r=0;r<t.length();r++){const a=A(t.get(r));a===""&&e!==!0||n.push(a)}return n}if(ze(t)){const r=A(t);return r===""&&e!==!0||n.push(r),n}return[]}let ln=0;function pa(t){return ln++,ln%100==0?(ln=0,new Promise(e=>{setTimeout(()=>{e(t)},0)})):t}function jn(t,e,n){switch(n){case"&":return t&e;case"|":return t|e;case"^":return t^e;case"<<":return t<<e;case">>":return t>>e;case">>>":return t>>>e}}function qe(t,e=null){return t==null?null:U(t)||G(t)||b(t)?t:t instanceof T?(e==null?void 0:e.keepGeometryType)===!0?t:t.toJSON():t instanceof se?t.toArray().map(n=>qe(n,e)):t instanceof Array?t.map(n=>qe(n,e)):Ot(t)?t:j(t)?t.toJSDate():t!==null&&typeof t=="object"&&t.castAsJson!==void 0?t.castAsJson(e):null}async function Da(t,e,n,r,a){const u=await Wn(t,e,n);a[r]=u}async function Wn(t,e=null,n=null){if(t instanceof se&&(t=t.toArray()),t==null)return null;if(ze(t)||t instanceof T||Ot(t)||j(t))return qe(t,n);if(t instanceof Array){const r=[],a=[];for(const u of t)u===null||ze(u)||u instanceof T||Ot(u)||j(u)?a.push(qe(u,n)):(a.push(null),r.push(Da(u,e,n,a.length-1,a)));return r.length>0&&await Promise.all(r),a}return t!==null&&typeof t=="object"&&t.castAsJsonAsync!==void 0?t.castAsJsonAsync(e,n):null}function si(t,e,n){const r=t.fullSchema();return r===null||!r.fields?null:Yt(e,r,t,n)}function Rt(t){const e=t.fullSchema();return e===null?null:e.fields&&e.typeIdField?{subtypeField:e.typeIdField,subtypes:e.types?e.types.map(n=>({name:n.name,code:n.id})):[]}:null}function ui(t,e,n,r){const a=t.fullSchema();if(a===null||!a.fields)return null;const u=Yt(e,a,t,r);if(n===void 0)try{n=t.field(e)}catch{return null}return ii(u,n)}function oi(t,e,n,r){const a=t.fullSchema();if(a===null||!a.fields)return null;if(n===void 0){try{n=t.field(e)}catch{return null}return n}return ai(Yt(e,a,t,r),n)}function P(t){var e,n;return(e=t==null?void 0:t.timeReference)!=null&&e.timeZone?(n=t==null?void 0:t.timeReference)==null?void 0:n.timeZone:"system"}function li(t){const e=t.fullSchema();if(e===null||!e.fields)return null;const n=[];for(const r of e.fields)n.push(fa(r));return{objectIdField:e.objectIdField,globalIdField:e.globalIdField,geometryType:pr[e.geometryType]===void 0?"":pr[e.geometryType],fields:n,datesInUnknownTimezone:e.datesInUnknownTimezone===!0,preferredTimeReference:e.preferredTimeReference||null,editFieldsInfo:e.editFieldsInfo||null,timeInfo:e.timeInfo||null,dateFieldsTimeReference:e.dateFieldsTimeReference||null}}const ci=Object.freeze(Object.defineProperty({__proto__:null,ImplicitResult:lt,ImplicitResultE:ri,ReturnResult:me,ReturnResultE:ni,absRound:Sn,autoCastArrayOfPointsToMultiPoint:Lt,autoCastArrayOfPointsToPolygon:In,autoCastArrayOfPointsToPolyline:Ct,autoCastFeatureToGeometry:O,binaryOperator:jn,breakResult:Ae,castAsJson:qe,castAsJsonAsync:Wn,continueResult:pt,defaultTimeZone:P,defaultUndefined:M,equalityTest:xe,featureDomainCodeLookup:oi,featureDomainValueLookup:ui,featureFullDomain:si,featureSchema:li,featureSubtypes:Rt,fixNullGeometry:Ve,fixSpatialReference:ee,formatDate:Kt,formatNumber:Vn,generateUUID:it,getDomain:Yt,getDomainCode:ai,getDomainValue:ii,getType:Jn,greaterThanLessThan:Zn,isArray:k,isBoolean:U,isDate:j,isFeature:Q,isFeatureSet:jt,isFeatureSetCollection:Wt,isFunctionParameter:K,isImmutableArray:N,isInteger:_e,isJsDate:Ot,isModule:qn,isNumber:G,isObject:bn,isSimpleType:ze,isString:b,multiReplace:Ge,pcCheck:D,stableStringify:vn,standardiseDateFormat:Hn,tick:pa,toBoolean:ct,toDate:Y,toNumber:d,toNumberArray:Je,toString:A,toStringArray:ma,toStringExplicit:pe,voidOperation:C},Symbol.toStringTag,{value:"Module"}));function Gt(t,e,n=!1){if(t==null)return null;if(G(t))return d(t);if(U(t))return ct(t);if(b(t))return A(t);if(j(t))return Y(t,e);if(k(t)){const a=[];for(const u of t)a.push(Gt(u,e,n));return a}const r=new _;r.immutable=!1;for(const a of Object.keys(t)){const u=t[a];u!==void 0&&r.setField(a,Gt(u,e,n))}return r.immutable=n,r}let _=class kn{constructor(e){this.declaredClass="esri.arcade.Dictionary",this.attributes=null,this.plain=!1,this.immutable=!0,this.attributes=e instanceof kn?e.attributes:e??{}}field(e){const n=e.toLowerCase(),r=this.attributes[e];if(r!==void 0)return r;for(const a in this.attributes)if(a.toLowerCase()===n)return this.attributes[a];throw new h(null,c.FieldNotFound,null,{key:e})}setField(e,n){if(this.immutable)throw new h(null,c.Immutable,null);if(K(n))throw new h(null,c.NoFunctionInDictionary,null);const r=e.toLowerCase();if(n instanceof Date&&(n=q.dateJSToArcadeDate(n)),this.attributes[e]===void 0){for(const a in this.attributes)if(a.toLowerCase()===r)return void(this.attributes[a]=n);this.attributes[e]=n}else this.attributes[e]=n}hasField(e){const n=e.toLowerCase();if(this.attributes[e]!==void 0)return!0;for(const r in this.attributes)if(r.toLowerCase()===n)return!0;return!1}keys(){let e=[];for(const n in this.attributes)e.push(n);return e=e.sort(),e}castToText(e=!1){let n="";for(const r in this.attributes){n!==""&&(n+=",");const a=this.attributes[r];a==null?n+=JSON.stringify(r)+":null":U(a)||G(a)||b(a)?n+=JSON.stringify(r)+":"+JSON.stringify(a):a instanceof T?n+=JSON.stringify(r)+":"+pe(a):a instanceof se||a instanceof Array?n+=JSON.stringify(r)+":"+pe(a,null,e):a instanceof q?n+=e?JSON.stringify(r)+":"+JSON.stringify(a.getTime()):JSON.stringify(r)+":"+a.stringify():a!==null&&typeof a=="object"&&a.castToText!==void 0&&(n+=JSON.stringify(r)+":"+a.castToText(e))}return"{"+n+"}"}static convertObjectToArcadeDictionary(e,n,r=!0){const a=new kn;a.immutable=!1;for(const u in e){const s=e[u];s!==void 0&&a.setField(u.toString(),Gt(s,n))}return a.immutable=r,a}static convertJsonToArcade(e,n,r=!1){return Gt(e,n,r)}castAsJson(e=null){const n={};for(let r in this.attributes){const a=this.attributes[r];a!==void 0&&(e!=null&&e.keyTranslate&&(r=e.keyTranslate(r)),n[r]=qe(a,e))}return n}async castDictionaryValueAsJsonAsync(e,n,r,a=null,u){const s=await Wn(r,a,u);return e[n]=s,s}async castAsJsonAsync(e=null,n=null){const r={},a=[];for(let u in this.attributes){const s=this.attributes[u];n!=null&&n.keyTranslate&&(u=n.keyTranslate(u)),s!==void 0&&(ze(s)||s instanceof T||s instanceof q?r[u]=qe(s,n):a.push(this.castDictionaryValueAsJsonAsync(r,u,s,e,n)))}return a.length>0&&await Promise.all(a),r}},X=class Se{constructor(){this.arcadeDeclaredClass="esri.arcade.Feature",this._optimizedGeomDefinition=null,this._geometry=null,this.attributes=null,this._layer=null,this._datesfixed=!0,this.dateTimeReferenceFieldIndex=null,this.contextTimeReference=null,this.immutable=!0,this._datefields=null,this.immutable=!0}static createFromGraphic(e,n){const r=new Se;return r.contextTimeReference=n??null,r._geometry=or(e.geometry)?e.geometry:null,e.attributes===void 0||e.attributes===null?r.attributes={}:r.attributes=e.attributes,e._sourceLayer?(r._layer=e._sourceLayer,r._datesfixed=!1):e._layer?(r._layer=e._layer,r._datesfixed=!1):e.layer&&"fields"in e.layer?(r._layer=e.layer,r._datesfixed=!1):e.sourceLayer&&"fields"in e.sourceLayer&&(r._layer=e.sourceLayer,r._datesfixed=!1),r._layer&&r._datesfixed===!1&&(r._layer.dateTimeReferenceFieldIndex!==void 0?r.dateTimeReferenceFieldIndex=r._layer.dateTimeReferenceFieldIndex:r.dateTimeReferenceFieldIndex=cr.createFromLayer(r._layer)),r}static createFromArcadeFeature(e){const n=new Se;return n._datesfixed=e._datesfixed,n.attributes=e.attributes,n._geometry=e._geometry,n._optimizedGeomDefinition=e._optimizedGeomDefinition,e._layer&&(n._layer=e._layer),n.dateTimeReferenceFieldIndex=e.dateTimeReferenceFieldIndex,n.contextTimeReference=e.contextTimeReference,n}static createFromOptimisedFeature(e,n,r){const a=new Se;return a._geometry=e.geometry?{geometry:e.geometry}:null,a._optimizedGeomDefinition=r,a.attributes=e.attributes||{},a._layer=n,a._datesfixed=!1,a}static createFromArcadeDictionary(e){const n=new Se;return n.attributes=e.field("attributes"),n.attributes!==null&&n.attributes instanceof _?(n.attributes=n.attributes.attributes,n.attributes===null&&(n.attributes={})):n.attributes={},n._geometry=e.field("geometry"),n._geometry!==null&&(n._geometry instanceof _?n._geometry=Se.parseGeometryFromDictionary(n._geometry):n._geometry instanceof T||(n._geometry=null)),n}static createFromGraphicLikeObject(e,n,r=null,a){const u=new Se;return u.contextTimeReference=a??null,n===null&&(n={}),u.attributes=n,u._geometry=or(e)?e:null,u._layer=r,u._layer&&(u._datesfixed=!1,u._layer.dateTimeReferenceFieldIndex!==void 0?u.dateTimeReferenceFieldIndex=u._layer.dateTimeReferenceFieldIndex:u.dateTimeReferenceFieldIndex=cr.createFromLayer(u._layer)),u}repurposeFromGraphicLikeObject(e,n,r=null){n===null&&(n={}),this.attributes=n,this._geometry=e||null,this._layer=r,this._layer?this._datesfixed=!1:this._datesfixed=!0}get layerPreferredTimeZone(){var e;return((e=this.dateTimeReferenceFieldIndex)==null?void 0:e.layerPreferredTimeZone)??""}fieldSourceTimeZone(e){var n;return((n=this.dateTimeReferenceFieldIndex)==null?void 0:n.fieldTimeZone(e))??""}castToText(e=!1){let n="";this._datesfixed===!1&&this._fixDates();for(const r in this.attributes){n!==""&&(n+=",");const a=this.attributes[r];a==null?n+=JSON.stringify(r)+":null":U(a)||G(a)||b(a)?n+=JSON.stringify(r)+":"+JSON.stringify(a):a instanceof T?n+=JSON.stringify(r)+":"+pe(a):a instanceof se||a instanceof Array?n+=JSON.stringify(r)+":"+pe(a,null,e):a instanceof q?n+=e?JSON.stringify(r)+":"+JSON.stringify(a.getTime()):JSON.stringify(r)+":"+a.stringify():a!==null&&typeof a=="object"&&a.castToText!==void 0&&(n+=JSON.stringify(r)+":"+a.castToText(e))}return'{"geometry":'+(this.geometry()===null?"null":pe(this.geometry()))+',"attributes":{'+n+"}}"}_fixDates(){if(this._datefields!==null)return this._datefields.length>0&&this._fixDateFields(this._datefields),void(this._datesfixed=!0);const e=[],n=this._layer.fields;for(let r=0;r<n.length;r++){const a=n[r],u=a.type;u!=="date"&&u!=="esriFieldTypeDate"||e.push(a.name)}this._datefields=e,e.length>0&&this._fixDateFields(e),this._datesfixed=!0}isUnknownDateTimeField(e){var n;return((n=this.dateTimeReferenceFieldIndex)==null?void 0:n.fieldTimeZone(e))==="unknown"}_fixDateFields(e){var r;this.attributes={...this.attributes};const n=((r=this.contextTimeReference)==null?void 0:r.timeZone)??"system";for(let a=0;a<e.length;a++){let u=this.attributes[e[a]];if(u!==null)if(u===void 0){for(const s in this.attributes)if(s.toLowerCase()===e[a].toLowerCase()){if(u=this.attributes[s],u!==null){const i=this.isUnknownDateTimeField(s);j(u)?this.attributes[s]=u:u instanceof Date?this.attributes[s]=i?q.unknownDateJSToArcadeDate(u):q.dateJSAndZoneToArcadeDate(u,n):this.attributes[s]=i?q.unknownEpochToArcadeDate(u):q.epochToArcadeDate(u,n)}break}}else{const s=this.isUnknownDateTimeField(e[a]);j(u)?this.attributes[e[a]]=u:u instanceof Date?this.attributes[e[a]]=s?q.unknownDateJSToArcadeDate(u):q.dateJSAndZoneToArcadeDate(u,n):this.attributes[e[a]]=s?q.unknownEpochToArcadeDate(u):q.epochToArcadeDate(u,n)}}}geometry(){return this._geometry===null||this._geometry instanceof T||(this._optimizedGeomDefinition?(this._geometry=lr(de(na(this._geometry,this._optimizedGeomDefinition.geometryType,this._optimizedGeomDefinition.hasZ,this._optimizedGeomDefinition.hasM))),this._geometry.spatialReference=this._optimizedGeomDefinition.spatialReference):this._geometry=lr(de(this._geometry))),this._geometry}field(e){this._datesfixed===!1&&this._fixDates();const n=this.attributes[e];if(n!==void 0)return n;const r=e.toLowerCase();for(const a in this.attributes)if(a.toLowerCase()===r)return this.attributes[a];if(this._hasFieldDefinition(r))return null;throw new h(null,c.FieldNotFound,null,{key:e})}_hasFieldDefinition(e){if(this._layer===null)return!1;for(let n=0;n<this._layer.fields.length;n++)if(this._layer.fields[n].name.toLowerCase()===e)return!0;return!1}setField(e,n){if(this.immutable)throw new h(null,c.Immutable,null);if(n instanceof Date&&(n=this.isUnknownDateTimeField(e)?q.unknownDateJSToArcadeDate(n):q.dateJSToArcadeDate(n)),ze(n)===!1)throw new h(null,c.TypeNotAllowedInFeature,null);const r=e.toLowerCase();if(this.attributes[e]===void 0){for(const a in this.attributes)if(a.toLowerCase()===r)return void(this.attributes[a]=n);this.attributes[e]=n}else this.attributes[e]=n}hasField(e){const n=e.toLowerCase();if(this.attributes[e]!==void 0)return!0;for(const r in this.attributes)if(r.toLowerCase()===n)return!0;return!!this._hasFieldDefinition(n)}keys(){let e=[];const n={};for(const r in this.attributes)e.push(r),n[r.toLowerCase()]=1;if(this._layer!==null)for(let r=0;r<this._layer.fields.length;r++){const a=this._layer.fields[r];n[a.name.toLowerCase()]!==1&&e.push(a.name)}return e=e.sort(),e}static parseGeometryFromDictionary(e){const n=Se._convertDictionaryToJson(e,!0);return n.hasm!==void 0&&(n.hasM=n.hasm,delete n.hasm),n.hasz!==void 0&&(n.hasZ=n.hasz,delete n.hasz),n.spatialreference!==void 0&&(n.spatialReference=n.spatialreference,delete n.spatialreference),n.rings!==void 0&&(n.rings=this._fixPathArrays(n.rings,n.hasZ===!0,n.hasZ===!0)),n.paths!==void 0&&(n.paths=this._fixPathArrays(n.paths,n.hasZ===!0,n.hasM===!0)),n.points!==void 0&&(n.points=this._fixPointArrays(n.points,n.hasZ===!0,n.hasM===!0)),de(n)}static _fixPathArrays(e,n,r){const a=[];if(e instanceof Array)for(let u=0;u<e.length;u++)a.push(this._fixPointArrays(e[u],n,r));else if(e instanceof se)for(let u=0;u<e.length();u++)a.push(this._fixPointArrays(e.get(u),n,r));return a}static _fixPointArrays(e,n,r){const a=[];if(e instanceof Array)for(let u=0;u<e.length;u++){const s=e[u];s instanceof H?n&&r?a.push([s.x,s.y,s.z,s.m]):n?a.push([s.x,s.y,s.z]):r?a.push([s.x,s.y,s.m]):a.push([s.x,s.y]):s instanceof se?a.push(s.toArray()):a.push(s)}else if(e instanceof se)for(let u=0;u<e.length();u++){const s=e.get(u);s instanceof H?n&&r?a.push([s.x,s.y,s.z,s.m]):n?a.push([s.x,s.y,s.z]):r?a.push([s.x,s.y,s.m]):a.push([s.x,s.y]):s instanceof se?a.push(s.toArray()):a.push(s)}return a}static _convertDictionaryToJson(e,n=!1){const r={};for(const a in e.attributes){let u=e.attributes[a];u instanceof _&&(u=Se._convertDictionaryToJson(u)),n?r[a.toLowerCase()]=u:r[a]=u}return r}static parseAttributesFromDictionary(e){const n={};for(const r in e.attributes){const a=e.attributes[r];if(!ze(a))throw new h(null,c.InvalidParameter,null);n[r]=a}return n}static fromJson(e,n){let r=null;e.geometry!==null&&e.geometry!==void 0&&(r=de(e.geometry));const a={};if(e.attributes!==null&&e.attributes!==void 0)for(const u in e.attributes){const s=e.attributes[u];if(s===null)a[u]=s;else{if(!(b(s)||G(s)||U(s)||j(s)))throw new h(null,c.InvalidParameter,null);a[u]=s}}return Se.createFromGraphicLikeObject(r,a,null,n??null)}fullSchema(){return this._layer}gdbVersion(){if(this._layer===null)return"";const e=this._layer.gdbVersion;return e===void 0?"":e===""&&this._layer.capabilities&&this._layer.capabilities.isVersioned?"SDE.DEFAULT":e}castAsJson(e){var r;const n={attributes:{},geometry:(e==null?void 0:e.keepGeometryType)===!0?this.geometry():((r=this.geometry())==null?void 0:r.toJSON())??null};for(const a in this.attributes){const u=this.attributes[a];u!==void 0&&(n.attributes[a]=qe(u,e))}return n}async castAsJsonAsync(e=null,n){return this.castAsJson(n)}};const yr={all:{min:2,max:2},none:{min:2,max:2},any:{min:2,max:2},reduce:{min:2,max:3},map:{min:2,max:2},filter:{min:2,max:2},fromcodepoint:{min:1,max:-1},fromcharcode:{min:1,max:-1},tocodepoint:{min:1,max:2},tocharcode:{min:1,max:2},concatenate:{min:0,max:-1},expects:{min:1,max:-1},getfeatureset:{min:1,max:2},week:{min:1,max:2},fromjson:{min:1,max:1},length3d:{min:1,max:2},tohex:{min:1,max:1},hash:{min:1,max:1},timezone:{min:1,max:1},timezoneoffset:{min:1,max:1},changetimezone:{min:2,max:2},isoweek:{min:1,max:1},isoweekday:{min:1,max:1},hasvalue:{min:2,max:2},isomonth:{min:1,max:1},isoyear:{min:1,max:1},resize:{min:2,max:3},slice:{min:0,max:-1},splice:{min:0,max:-1},push:{min:2,max:2},pop:{min:1,max:1},includes:{min:2,max:2},array:{min:1,max:2},front:{min:1,max:1},back:{min:1,max:1},insert:{min:3,max:3},erase:{min:2,max:2},split:{min:2,max:4},guid:{min:0,max:1},standardizeguid:{min:2,max:2},today:{min:0,max:0},angle:{min:2,max:3},bearing:{min:2,max:3},urlencode:{min:1,max:1},now:{min:0,max:0},timestamp:{min:0,max:0},day:{min:1,max:1},month:{min:1,max:1},year:{min:1,max:1},hour:{min:1,max:1},second:{min:1,max:1},millisecond:{min:1,max:1},minute:{min:1,max:1},weekday:{min:1,max:1},toutc:{min:1,max:1},tolocal:{min:1,max:1},date:{min:0,max:8},datediff:{min:2,max:4},dateadd:{min:2,max:3},trim:{min:1,max:1},text:{min:1,max:2},left:{min:2,max:2},right:{min:2,max:2},mid:{min:2,max:3},upper:{min:1,max:1},proper:{min:1,max:2},lower:{min:1,max:1},find:{min:2,max:3},iif:{min:3,max:3},decode:{min:2,max:-1},when:{min:2,max:-1},defaultvalue:{min:2,max:2},isempty:{min:1,max:1},domaincode:{min:2,max:4},domainname:{min:2,max:4},polygon:{min:1,max:1},point:{min:1,max:1},polyline:{min:1,max:1},extent:{min:1,max:1},multipoint:{min:1,max:1},ringisclockwise:{min:1,max:1},geometry:{min:1,max:1},count:{min:0,max:-1},number:{min:1,max:2},acos:{min:1,max:1},asin:{min:1,max:1},atan:{min:1,max:1},atan2:{min:2,max:2},ceil:{min:1,max:2},floor:{min:1,max:2},round:{min:1,max:2},cos:{min:1,max:1},exp:{min:1,max:1},log:{min:1,max:1},min:{min:0,max:-1},constrain:{min:3,max:3},console:{min:0,max:-1},max:{min:0,max:-1},pow:{min:2,max:2},random:{min:0,max:0},sqrt:{min:1,max:1},sin:{min:1,max:1},tan:{min:1,max:1},abs:{min:1,max:1},isnan:{min:1,max:1},stdev:{min:0,max:-1},average:{min:0,max:-1},mean:{min:0,max:-1},sum:{min:0,max:-1},variance:{min:0,max:-1},distinct:{min:0,max:-1},first:{min:1,max:1},top:{min:2,max:2},boolean:{min:1,max:1},dictionary:{min:0,max:-1},typeof:{min:1,max:1},reverse:{min:1,max:1},replace:{min:3,max:4},sort:{min:1,max:2},feature:{min:1,max:-1},haskey:{min:2,max:2},indexof:{min:2,max:2},disjoint:{min:2,max:2},intersects:{min:2,max:2},touches:{min:2,max:2},crosses:{min:2,max:2},within:{min:2,max:2},contains:{min:2,max:2},overlaps:{min:2,max:2},equals:{min:2,max:2},relate:{min:3,max:3},intersection:{min:2,max:2},union:{min:1,max:2},difference:{min:2,max:2},symmetricdifference:{min:2,max:2},clip:{min:2,max:2},cut:{min:2,max:2},area:{min:1,max:2},areageodetic:{min:1,max:2},length:{min:1,max:2},lengthgeodetic:{min:1,max:2},distancegeodetic:{min:2,max:3},distance:{min:2,max:3},densify:{min:2,max:3},densifygeodetic:{min:2,max:3},generalize:{min:2,max:4},buffer:{min:2,max:3},buffergeodetic:{min:2,max:3},offset:{min:2,max:6},rotate:{min:2,max:3},issimple:{min:1,max:1},simplify:{min:1,max:1},convexhull:{min:1,max:1},centroid:{min:1,max:1},isselfintersecting:{min:1,max:1},multiparttosinglepart:{min:1,max:1},setgeometry:{min:2,max:2},portal:{min:1,max:1},getuser:{min:0,max:2},subtypes:{min:1,max:1},subtypecode:{min:1,max:1},subtypename:{min:1,max:1},domain:{min:2,max:3},convertdirection:{min:3,max:3},sqltimestamp:{min:1,max:3},schema:{min:1,max:1}},Xt={functionDefinitions:new Map,constantDefinitions:new Map},Qt={functionDefinitions:new Map,constantDefinitions:new Map};for(const t of["pi","infinity"])Qt.constantDefinitions.set(t,{type:"constant"}),Xt.constantDefinitions.set(t,{type:"constant"});Qt.constantDefinitions.set("textformatting",{type:"namespace",key:"textformatting",members:[{key:"backwardslash",type:"constant"},{key:"doublequote",type:"constant"},{key:"forwardslash",type:"constant"},{key:"tab",type:"constant"},{key:"singlequote",type:"constant"},{key:"newline",type:"constant"}]}),Xt.constantDefinitions.set("textformatting",{type:"namespace",key:"textformatting",members:[{key:"backwardslash",type:"constant"},{key:"tab",type:"constant"},{key:"singlequote",type:"constant"},{key:"doublequote",type:"constant"},{key:"forwardslash",type:"constant"},{key:"newline",type:"constant"}]});for(const t in yr){const e=yr[t];Qt.functionDefinitions.set(t,{overloads:[{type:"function",parametersInfo:{min:e.min,max:e.max}}]}),Xt.functionDefinitions.set(t,{overloads:[{type:"function",parametersInfo:{min:e.min,max:e.max}}]})}const ga=["featureset","featuresetbyid","featuresetbyname","featuresetbyassociation","featuresetbyrelationshipname","featuresetbyurl","getfeatureset","getuser","attachments","featuresetbyportalitem"],ya=["disjoint","intersects","touches","crosses","within","contains","overlaps","equals","relate","intersection","union","difference","symmetricdifference","clip","cut","area","areageodetic","length","length3d","lengthgeodetic","distance","distancegeodetic","densify","densifygeodetic","generalize","buffer","buffergeodetic","offset","rotate","issimple","convexhull","simplify","multiparttosinglepart"];function wr(t){return typeof t=="string"||t instanceof String}function Bn(t,e){var r;const n=e==="sync"?Xt:Qt;n.functionDefinitions.has(t.name.toLowerCase())?(r=n.functionDefinitions.get(t.name.toLowerCase()))==null||r.overloads.push({type:"function",parametersInfo:{min:t.min,max:t.max}}):n.functionDefinitions.set(t.name.toLowerCase(),{overloads:[{type:"function",parametersInfo:{min:t.min,max:t.max}}]})}function ke(t,e){if(t)for(const n of t)V(n,e)}function V(t,e){if(t&&e(t)!==!1)switch(t.type){case"ImportDeclaration":ke(t.specifiers,e),V(t.source,e);break;case"ExportNamedDeclaration":V(t.declaration,e);break;case"ArrayExpression":ke(t.elements,e);break;case"AssignmentExpression":case"BinaryExpression":case"LogicalExpression":V(t.left,e),V(t.right,e);break;case"BlockStatement":case"Program":ke(t.body,e);break;case"BreakStatement":case"ContinueStatement":case"EmptyStatement":case"Identifier":case"Literal":break;case"CallExpression":V(t.callee,e),ke(t.arguments,e);break;case"ExpressionStatement":V(t.expression,e);break;case"ForInStatement":V(t.left,e),V(t.right,e),V(t.body,e);break;case"ForStatement":V(t.init,e),V(t.test,e),V(t.update,e),V(t.body,e);break;case"WhileStatement":V(t.test,e),V(t.body,e);break;case"FunctionDeclaration":V(t.id,e),ke(t.params,e),V(t.body,e);break;case"IfStatement":V(t.test,e),V(t.consequent,e),V(t.alternate,e);break;case"MemberExpression":V(t.object,e),V(t.property,e);break;case"ObjectExpression":ke(t.properties,e);break;case"Property":V(t.key,e),V(t.value,e);break;case"ReturnStatement":case"UnaryExpression":case"UpdateExpression":V(t.argument,e);break;case"VariableDeclaration":ke(t.declarations,e);break;case"VariableDeclarator":V(t.id,e),V(t.init,e);break;case"TemplateLiteral":ke(t.expressions,e),ke(t.quasis,e)}}function hi(t,e){let n=!1;const r=e.toLowerCase();return V(t,a=>!n&&(a.type==="Identifier"&&a.name&&a.name.toLowerCase()===r&&(n=!0),!0)),n}function fi(t){const e=[];return V(t,n=>(n.type==="ImportDeclaration"&&n.source&&n.source.value&&e.push({libname:n.specifiers[0].local.name.toLowerCase(),source:n.source.value}),!0)),e}function wa(t,e){let n=!1;const r=e.toLowerCase();return V(t,a=>!n&&(a.type!=="CallExpression"||a.callee.type!=="Identifier"||!a.callee.name||a.callee.name.toLowerCase()!==r||(n=!0,!1))),n}function xa(t){const e=[];return V(t,n=>{var r;return n.type!=="MemberExpression"||n.object.type!=="Identifier"||(n.computed===!1&&n.object&&n.object.name&&n.property&&n.property.type==="Identifier"&&n.property.name?e.push(n.object.name.toLowerCase()+"."+n.property.name.toLowerCase()):n.object&&n.object.name&&n.property&&n.property.type==="Literal"&&typeof n.property.value=="string"&&e.push(n.object.name.toLowerCase()+"."+((r=n.property.value)==null?void 0:r.toString().toLowerCase())),!1)}),e}function Fa(t){const e=[];return V(t,n=>{var r;if(n.type==="CallExpression"){if(n.callee.type==="Identifier"&&n.callee.name.toLowerCase()==="expects"){let a="";for(let u=0;u<(n.arguments||[]).length;u++)u===0?n.arguments[u].type==="Identifier"&&(a=n.arguments[u].name.toLowerCase()):a&&n.arguments[u].type==="Literal"&&wr(n.arguments[u].value)&&e.push(a+"."+n.arguments[u].value.toLowerCase());return!1}if(n.callee.type==="Identifier"&&["domainname","domaincode","domain","haskey"].includes(n.callee.name.toLowerCase())&&n.arguments.length>=2){let a="";return n.arguments[0].type==="Identifier"&&(a=n.arguments[0].name.toLowerCase()),a&&n.arguments[1].type==="Literal"&&wr(n.arguments[1].value)&&e.push(a+"."+n.arguments[1].value.toLowerCase()),!1}}return n.type!=="MemberExpression"||n.object.type!=="Identifier"||(n.computed===!1&&n.object&&n.object.name&&n.property&&n.property.type==="Identifier"&&n.property.name?e.push(n.object.name.toLowerCase()+"."+n.property.name.toLowerCase()):n.object&&n.object.name&&n.property&&n.property.type==="Literal"&&typeof n.property.value=="string"&&e.push(n.object.name.toLowerCase()+"."+((r=n.property.value)==null?void 0:r.toString().toLowerCase())),!1)}),e}function Tn(t){const e=[];return V(t,n=>(n.type==="CallExpression"&&n.callee.type==="Identifier"&&e.push(n.callee.name.toLowerCase()),!0)),e}function Bt(t,e=[]){let n=null;if(t.usesFeatureSet===void 0){n===null&&(n=Tn(t)),t.usesFeatureSet=!1;for(let r=0;r<n.length;r++)ga.includes(n[r])&&(t.usesFeatureSet=!0,t.isAsync=!0);if(t.usesFeatureSet===!1&&e&&e.length>0){for(const r of e)if(hi(t,r)){t.usesFeatureSet=!0,t.isAsync=!0;break}}}if(t.usesModules===void 0&&(t.usesModules=!1,fi(t).length>0&&(t.usesModules=!0)),t.usesGeometry===void 0){t.usesGeometry=!1,n===null&&(n=Tn(t));for(let r=0;r<n.length;r++)ya.includes(n[r])&&(t.usesGeometry=!0)}}function Ca(t){function e(i,o,l){if(i instanceof se)return i.toArray();if(k(i))return i;throw new h(o,c.InvalidParameter,l)}function n(i,o){const l=i.length,m=Math.floor(l/2);return l===0?[]:l===1?[i[0]]:r(n(i.slice(0,m),o),n(i.slice(m,l),o),o)}function r(i,o,l){const m=[];for(;i.length>0||o.length>0;)if(i.length>0&&o.length>0){let f=l(i[0],o[0]);isNaN(f)&&(f=0),f<=0?(m.push(i[0]),i=i.slice(1)):(m.push(o[0]),o=o.slice(1))}else i.length>0?(m.push(i[0]),i=i.slice(1)):o.length>0&&(m.push(o[0]),o=o.slice(1));return m}async function a(i,o){const l=i.length,m=Math.floor(l/2);if(l===0)return[];if(l===1)return[i[0]];const f=[await a(i.slice(0,m),o),await a(i.slice(m,l),o)];return u(f[0],f[1],o,[])}async function u(i,o,l,m){const f=m;if(!(i.length>0||o.length>0))return m;if(i.length>0&&o.length>0){let p=await l(i[0],o[0]);return isNaN(p)&&(p=1),p<=0?(f.push(i[0]),i=i.slice(1)):(f.push(o[0]),o=o.slice(1)),u(i,o,l,m)}return i.length>0?(f.push(i[0]),u(i=i.slice(1),o,l,m)):o.length>0?(f.push(o[0]),u(i,o=o.slice(1),l,m)):void 0}function s(i,o,l,m){D(l,1,2,i,o);let f=l[0];if(N(f)&&(f=f.toArray()),k(f)===!1)throw new h(i,c.InvalidParameter,o);if(l.length>1){if(K(l[1])===!1)throw new h(i,c.InvalidParameter,o);let I=f;const J=l[1].createFunction(i);return m?a(I,J):(I=n(I,(fe,_t)=>J(fe,_t)),I)}let p=f;if(p.length===0)return[];const g={};for(let I=0;I<p.length;I++){const J=Jn(p[I]);J!==""&&(g[J]=!0)}if(g.Array===!0||g.Dictionary===!0||g.Feature===!0||g.Point===!0||g.Polygon===!0||g.Polyline===!0||g.Multipoint===!0||g.Extent===!0||g.Function===!0)return p.slice(0);let w=0,B="";for(const I in g)w++,B=I;return p=w>1||B==="String"?n(p,(I,J)=>{if(I==null||I===C)return J==null||J===C?0:1;if(J==null||J===C)return-1;const fe=A(I),_t=A(J);return fe<_t?-1:fe===_t?0:1}):B==="Number"?n(p,(I,J)=>I-J):B==="Boolean"?n(p,(I,J)=>I===J?0:J?-1:1):B==="Date"?n(p,(I,J)=>J-I):p.slice(0),p}t.functions.array=function(i,o){return t.standardFunction(i,o,(l,m,f)=>{D(f,1,2,i,o);const p=d(f[0]);if(isNaN(p)||_e(p)===!1)throw new h(i,c.InvalidParameter,o);const g=M(f[1],null),w=new Array(p);return w.fill(g),w})},t.functions.front=function(i,o){return t.standardFunction(i,o,(l,m,f)=>{if(D(f,1,1,i,o),N(f[0])){if(f[0].length()<=0)throw new h(i,c.OutOfBounds,o);return f[0].get(0)}if(k(f[0])){if(f[0].length<=0)throw new h(i,c.OutOfBounds,o);return f[0][0]}throw new h(i,c.InvalidParameter,o)})},t.functions.back=function(i,o){return t.standardFunction(i,o,(l,m,f)=>{if(D(f,1,1,i,o),N(f[0])){if(f[0].length()<=0)throw new h(i,c.OutOfBounds,o);return f[0].get(f[0].length()-1)}if(k(f[0])){if(f[0].length<=0)throw new h(i,c.OutOfBounds,o);return f[0][f[0].length-1]}throw new h(i,c.InvalidParameter,o)})},t.functions.push=function(i,o){return t.standardFunction(i,o,(l,m,f)=>{if(D(f,1,2,i,o),k(f[0]))return f[0][f[0].length]=f[1],f[0].length;throw new h(i,c.InvalidParameter,o)})},t.functions.pop=function(i,o){return t.standardFunction(i,o,(l,m,f)=>{if(D(f,1,1,i,o),k(f[0])){if(f[0].length<=0)throw new h(i,c.OutOfBounds,o);const p=f[0][f[0].length-1];return f[0].length=f[0].length-1,p}throw new h(i,c.InvalidParameter,o)})},t.functions.erase=function(i,o){return t.standardFunction(i,o,(l,m,f)=>{if(D(f,2,2,i,o),k(f[0])){let p=d(f[1]);if(isNaN(p)||_e(p)===!1)throw new h(i,c.InvalidParameter,o);const g=f[0];if(g.length<=0)throw new h(i,c.OutOfBounds,o);if(p<0&&(p=g.length+p),p<0)throw new h(i,c.OutOfBounds,o);if(p>=g.length)throw new h(i,c.OutOfBounds,o);return g.splice(p,1),C}throw new h(i,c.InvalidParameter,o)})},t.functions.insert=function(i,o){return t.standardFunction(i,o,(l,m,f)=>{if(D(f,3,3,i,o),k(f[0])){const p=d(f[1]);if(isNaN(p)||_e(p)===!1)throw new h(i,c.InvalidParameter,o);const g=f[2],w=f[0];if(p>w.length)throw new h(i,c.OutOfBounds,o);if(p<0&&p<-1*w.length)throw new h(i,c.OutOfBounds,o);return p===w.length?(w[p]=g,C):(w.splice(p,0,g),C)}throw new h(i,c.InvalidParameter,o)})},t.functions.resize=function(i,o){return t.standardFunction(i,o,(l,m,f)=>{if(D(f,2,3,i,o),k(f[0])){const p=d(f[1]);if(isNaN(p)||_e(p)===!1)throw new h(i,c.InvalidParameter,o);if(p<0)throw new h(i,c.InvalidParameter,o);const g=M(f[2],null),w=f[0];if(w.length>=p)return w.length=p,C;const B=w.length;w.length=p;for(let I=B;I<w.length;I++)w[I]=g;return C}throw new h(i,c.InvalidParameter,o)})},t.functions.includes=function(i,o){return t.standardFunction(i,o,(l,m,f)=>{if(D(f,2,2,i,o),k(f[0])){const p=f[1];return f[0].findIndex(g=>xe(g,p))>-1}if(N(f[0])){const p=f[1];return f[0].toArray().findIndex(g=>xe(g,p))>-1}throw new h(i,c.InvalidParameter,o)})},t.functions.slice=function(i,o){return t.standardFunction(i,o,(l,m,f)=>{if(D(f,1,3,i,o),k(f[0])){const p=d(M(f[1],0)),g=d(M(f[2],f[0].length));if(isNaN(p)||_e(p)===!1)throw new h(i,c.InvalidParameter,o);if(isNaN(g)||_e(g)===!1)throw new h(i,c.InvalidParameter,o);return f[0].slice(p,g)}if(N(f[0])){const p=f[0],g=d(M(f[1],0)),w=d(M(f[2],p.length()));if(isNaN(g)||_e(g)===!1)throw new h(i,c.InvalidParameter,o);if(isNaN(w)||_e(w)===!1)throw new h(i,c.InvalidParameter,o);return p.toArray().slice(g,w)}throw new h(i,c.InvalidParameter,o)})},t.functions.splice=function(i,o){return t.standardFunction(i,o,(l,m,f)=>{const p=[];for(let g=0;g<f.length;g++)k(f[g])?p.push(...f[g]):N(f[g])?p.push(...f[g].toArray()):p.push(f[g]);return p})},t.functions.top=function(i,o){return t.standardFunction(i,o,(l,m,f)=>{if(D(f,2,2,i,o),k(f[0]))return d(f[1])>=f[0].length?f[0].slice(0):f[0].slice(0,d(f[1]));if(N(f[0]))return d(f[1])>=f[0].length()?f[0].slice(0):f[0].slice(0,d(f[1]));throw new h(i,c.InvalidParameter,o)})},t.functions.first=function(i,o){return t.standardFunction(i,o,(l,m,f)=>(D(f,1,1,i,o),k(f[0])?f[0].length===0?null:f[0][0]:N(f[0])?f[0].length()===0?null:f[0].get(0):null))},t.mode==="sync"&&(t.functions.sort=function(i,o){return t.standardFunction(i,o,(l,m,f)=>s(i,o,f,!1))},t.functions.any=function(i,o){return t.standardFunction(i,o,(l,m,f)=>{D(f,2,2,i,o);const p=f[1].createFunction(i),g=e(f[0],i,o);for(const w of g){const B=p(w);if(U(B)&&B===!0)return!0}return!1})},t.functions.all=function(i,o){return t.standardFunction(i,o,(l,m,f)=>{D(f,2,2,i,o);const p=f[1].createFunction(i),g=e(f[0],i,o);for(const w of g)if(p(w)!==!0)return!1;return!0})},t.functions.none=function(i,o){return t.standardFunction(i,o,(l,m,f)=>{D(f,2,2,i,o);const p=f[1].createFunction(i),g=e(f[0],i,o);for(const w of g)if(p(w)===!0)return!1;return!0})},t.functions.reduce=function(i,o){return t.standardFunction(i,o,(l,m,f)=>{D(f,2,3,i,o);const p=f[1].createFunction(i),g=e(f[0],i,o);return f.length===2?g.length===0?null:g.reduce((w,B)=>{const I=p(w,B);return w=I!==void 0&&I!==C?I:null}):g.reduce((w,B)=>{const I=p(w,B);return w=I!==void 0&&I!==C?I:null},f[2])})},t.functions.map=function(i,o){return t.standardFunction(i,o,(l,m,f)=>{D(f,2,2,i,o);const p=f[1].createFunction(i),g=e(f[0],i,o),w=[];for(const B of g){const I=p(B);I!==void 0&&I!==C?w.push(I):w.push(null)}return w})},t.functions.filter=function(i,o){return t.standardFunction(i,o,(l,m,f)=>{D(f,2,2,i,o);const p=f[1].createFunction(i),g=e(f[0],i,o),w=[];for(const B of g)p(B)===!0&&w.push(B);return w})}),t.mode==="async"&&(t.functions.sort=function(i,o){return t.standardFunctionAsync(i,o,(l,m,f)=>s(i,o,f,!0))},t.functions.any=function(i,o){return t.standardFunctionAsync(i,o,async(l,m,f)=>{D(f,2,2,i,o);const p=f[1].createFunction(i),g=e(f[0],i,o);for(const w of g){const B=await p(w);let I=null;if(I=Oe(I)?await B:B,U(I)&&I===!0)return!0}return!1})},t.functions.all=function(i,o){return t.standardFunctionAsync(i,o,async(l,m,f)=>{D(f,2,2,i,o);const p=f[1].createFunction(i),g=e(f[0],i,o);for(const w of g){const B=await p(w);let I=null;if(I=Oe(I)?await B:B,I!==!0)return!1}return!0})},t.functions.none=function(i,o){return t.standardFunctionAsync(i,o,async(l,m,f)=>{D(f,2,2,i,o);const p=f[1].createFunction(i),g=e(f[0],i,o);for(const w of g){const B=await p(w);let I=null;if(I=Oe(I)?await B:B,I===!0)return!1}return!0})},t.functions.filter=function(i,o){return t.standardFunctionAsync(i,o,async(l,m,f)=>{D(f,2,2,i,o);const p=f[1].createFunction(i),g=e(f[0],i,o),w=[];for(const B of g){const I=await p(B);let J=null;J=Oe(J)?await I:I,J===!0&&w.push(B)}return w})},t.functions.reduce=function(i,o){return t.standardFunctionAsync(i,o,(l,m,f)=>{D(f,2,3,i,o);const p=f[1].createFunction(i),g=e(f[0],i,o);let w=null;if(f.length>2){const B=M(f[2],null);w=g.reduce(async(I,J)=>{let fe=await I;return fe!==void 0&&fe!==C||(fe=null),p(fe,J)},Promise.resolve(B))}else{if(g.length===0)return null;w=g.reduce(async(B,I,J)=>{if(J<=1)return p(B,I);let fe=await B;return fe!==void 0&&fe!==C||(fe=null),p(fe,I)})}return w.then(B=>B!==void 0&&B!==C?B:null)})},t.functions.map=function(i,o){return t.standardFunctionAsync(i,o,async(l,m,f)=>{D(f,2,2,i,o);const p=f[1].createFunction(i),g=e(f[0],i,o),w=[];for(const B of g){const I=await p(B);let J=null;J=Oe(J)?await I:I,J!==void 0&&J!==C?w.push(J):w.push(null)}return w})})}const _n=Object.freeze(Object.defineProperty({__proto__:null,registerFunctions:Ca},Symbol.toStringTag,{value:"Module"}));function Aa(t,e,n){return t+(Ea(n)?Sa:ba)[e]}function Ea(t){return t%4==0&&(t%100!=0||t%400==0)}const ba=[0,31,59,90,120,151,181,212,243,273,304,334],Sa=[0,31,60,91,121,152,182,213,244,274,305,335];function Xe(t){return t===null?t:t.isValid===!1?null:t}function xr(t,e){return t===""||t.toLowerCase().trim()==="default"?P(e):t}function di(t,e){t.today=function(n,r){return e(n,r,(a,u,s)=>{D(s,0,0,n,r);const i=new Date;return i.setHours(0,0,0,0),q.dateJSAndZoneToArcadeDate(i,P(n))})},t.changetimezone=function(n,r){return e(n,r,(a,u,s)=>{D(s,2,2,n,r);const i=Y(s[0],P(n));if(i===null)return null;const o=q.arcadeDateAndZoneToArcadeDate(i,xr(A(s[1]),n));return o.isValid===!1?null:o})},t.timezone=function(n,r){return e(n,r,(a,u,s)=>{D(s,1,2,n,r);const i=Y(s[0],P(n));if(i===null)return null;const o=i.timeZone;return o==="system"?q.systemTimeZoneCanonicalName:o})},t.timezoneoffset=function(n,r){return e(n,r,(a,u,s)=>{D(s,1,1,n,r);const i=Y(s[0],P(n));return i===null?null:i.timeZoneOffset})},t.now=function(n,r){return e(n,r,(a,u,s)=>{D(s,0,0,n,r);const i=q.nowToArcadeDate(P(n));return i.isValid===!1?null:i})},t.timestamp=function(n,r){return e(n,r,(a,u,s)=>{D(s,0,0,n,r);const i=q.nowUTCToArcadeDate();return i.isValid===!1?null:i})},t.toutc=function(n,r){return e(n,r,(a,u,s)=>{D(s,1,1,n,r);const i=Y(s[0],P(n));return i===null?null:i.toUTC()})},t.tolocal=function(n,r){return e(n,r,(a,u,s)=>{D(s,1,1,n,r);const i=Y(s[0],P(n));return i===null?null:i.toLocal()})},t.day=function(n,r){return e(n,r,(a,u,s)=>{D(s,1,1,n,r);const i=Y(s[0],P(n));return i===null?NaN:i.day})},t.month=function(n,r){return e(n,r,(a,u,s)=>{D(s,1,1,n,r);const i=Y(s[0],P(n));return i===null?NaN:i.monthJS})},t.year=function(n,r){return e(n,r,(a,u,s)=>{D(s,1,1,n,r);const i=Y(s[0],P(n));return i===null?NaN:i.year})},t.hour=function(n,r){return e(n,r,(a,u,s)=>{D(s,1,1,n,r);const i=Y(s[0],P(n));return i===null?NaN:i.hour})},t.second=function(n,r){return e(n,r,(a,u,s)=>{D(s,1,1,n,r);const i=Y(s[0],P(n));return i===null?NaN:i.second})},t.millisecond=function(n,r){return e(n,r,(a,u,s)=>{D(s,1,1,n,r);const i=Y(s[0],P(n));return i===null?NaN:i.millisecond})},t.minute=function(n,r){return e(n,r,(a,u,s)=>{D(s,1,1,n,r);const i=Y(s[0],P(n));return i===null?NaN:i.minute})},t.week=function(n,r){return e(n,r,(a,u,s)=>{D(s,1,2,n,r);const i=Y(s[0],P(n));if(i===null)return NaN;const o=d(M(s[1],0));if(o<0||o>6)throw new h(n,c.InvalidParameter,r);const l=i.day,m=i.monthJS,f=i.year,p=i.dayOfWeekJS,g=Aa(l,m,f)-1,w=Math.floor(g/7);return p-o+(p-o<0?7:0)<g-7*w?w+1:w})},t.weekday=function(n,r){return e(n,r,(a,u,s)=>{D(s,1,1,n,r);const i=Y(s[0],P(n));return i===null?NaN:i.dayOfWeekJS})},t.isoweekday=function(n,r){return e(n,r,(a,u,s)=>{D(s,1,1,n,r);const i=Y(s[0],P(n));return i===null?NaN:i.dayOfWeekISO})},t.isomonth=function(n,r){return e(n,r,(a,u,s)=>{D(s,1,1,n,r);const i=Y(s[0],P(n));return i===null?NaN:i.monthISO})},t.isoweek=function(n,r){return e(n,r,(a,u,s)=>{D(s,1,1,n,r);const i=Y(s[0],P(n));return i===null?NaN:i.weekISO})},t.isoyear=function(n,r){return e(n,r,(a,u,s)=>{D(s,1,1,n,r);const i=Y(s[0],P(n));return i===null?NaN:i.yearISO})},t.date=function(n,r){return e(n,r,(a,u,s)=>{if(D(s,0,8,n,r),s.length===3)return Xe(q.fromParts(d(s[0]),d(s[1])+1,d(s[2]),0,0,0,0,P(n)));if(s.length===4)return Xe(q.fromParts(d(s[0]),d(s[1])+1,d(s[2]),d(s[3]),0,0,0,P(n)));if(s.length===5)return Xe(q.fromParts(d(s[0]),d(s[1])+1,d(s[2]),d(s[3]),d(s[4]),0,0,P(n)));if(s.length===6)return Xe(q.fromParts(d(s[0]),d(s[1])+1,d(s[2]),d(s[3]),d(s[4]),d(s[5]),0,P(n)));if(s.length===7)return Xe(q.fromParts(d(s[0]),d(s[1])+1,d(s[2]),d(s[3]),d(s[4]),d(s[5]),d(s[6]),P(n)));if(s.length===8)return Xe(q.fromParts(d(s[0]),d(s[1])+1,d(s[2]),d(s[3]),d(s[4]),d(s[5]),d(s[6]),xr(A(s[7]),n)));if(s.length===2){let i,o=A(s[1]);return o===""?null:(o=Hn(o),i=o==="X"?at.fromSeconds(d(s[0])):o==="x"?at.fromMillis(d(s[0])):at.fromFormat(A(s[0]),o,{locale:Qr(),numberingSystem:"latn"}),i.isValid?q.dateTimeToArcadeDate(i):null)}if(s.length===1){if(b(s[0])){if(s[0].replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")==="")return null;if(/^[0-9][0-9][0-9][0-9]$/.test(s[0])===!0)return Y(s[0]+"-01-01",P(n))}const i=d(s[0]);if(isNaN(i)===!1){const o=at.fromMillis(i);return o.isValid?q.dateTimeAndZoneToArcadeDate(o,P(n)):null}return Y(s[0],P(n))}return s.length===0?q.nowToArcadeDate(P(n)):null})},t.datediff=function(n,r){return e(n,r,(a,u,s)=>{D(s,2,4,n,r);let i=Y(s[0],P(n)),o=Y(s[1],P(n));if(i===null||o===null)return NaN;let l=M(s[3],"");switch(l!==""&&l!==null?(l=A(l),i=q.arcadeDateAndZoneToArcadeDate(i,l),o=q.arcadeDateAndZoneToArcadeDate(o,l)):i.timeZone!==o.timeZone&&(i.isUnknownTimeZone?i=q.arcadeDateAndZoneToArcadeDate(i,o.timeZone):o=(o.isUnknownTimeZone,q.arcadeDateAndZoneToArcadeDate(o,i.timeZone))),A(s[2]).toLowerCase()){case"days":case"day":case"d":return i.diff(o,"days");case"months":case"month":return i.diff(o,"months");case"minutes":case"minute":case"m":return s[2]==="M"?i.diff(o,"months"):i.diff(o,"minutes");case"seconds":case"second":case"s":return i.diff(o,"seconds");case"milliseconds":case"millisecond":case"ms":default:return i.diff(o);case"hours":case"hour":case"h":return i.diff(o,"hours");case"years":case"year":case"y":return i.diff(o,"years")}})},t.dateadd=function(n,r){return e(n,r,(a,u,s)=>{D(s,2,3,n,r);const i=Y(s[0],P(n));if(i===null)return null;let o=d(s[1]);if(isNaN(o))return i;let l="milliseconds";switch(A(s[2]).toLowerCase()){case"days":case"day":case"d":l="days",o=Sn(o);break;case"months":case"month":l="months",o=Sn(o);break;case"minutes":case"minute":case"m":l=s[2]==="M"?"months":"minutes";break;case"seconds":case"second":case"s":l="seconds";break;case"milliseconds":case"millisecond":case"ms":l="milliseconds";break;case"hours":case"hour":case"h":l="hours";break;case"years":case"year":case"y":l="years"}return i.plus({[l]:o})})}}function Dt(t,e,n){return Math.sqrt((t[0]-e[0])**2+(t[1]-e[1])**2+(t[2]!==void 0&&e[2]!==void 0?(t[2]*n-e[2]*n)**2:0))}const yt=[];for(const t of[[9002,56146130,6131,6132,8050,8051,8228],[9003,5702,6358,6359,6360,8052,8053],[9095,5754]]){const e=t[0];for(let n=1;n<t.length;n++)yt[t[n]]=e}const At=[];function va(t){return t.vcsWkid&&yt[t.vcsWkid]!==void 0?At[yt[t.vcsWkid]]:t.latestVcsWkid&&yt[t.latestVcsWkid]!==void 0?At[yt[t.latestVcsWkid]]:1}function Fr(t,e,n){const r={x:0,y:0};e&&(r.z=0),n&&(r.m=0);let a=0,u=t[0];for(let s=0;s<t.length;s++){const i=t[s];if(Ba(i,u)===!1){const o=mi(u,i,e),l=Ia(u,i,e,n);l.x*=o,l.y*=o,r.x+=l.x,r.y+=l.y,e&&(l.z*=o,r.z+=l.z),n&&(l.m*=o,r.m+=l.m),a+=o,u=i}}return a>0?(r.x/=a,r.y/=a,e&&(r.z/=a),n&&(r.m/=a)):(r.x=t[0][0],r.y=t[0][1],e&&(r.z=t[0][2]),n&&e?r.m=t[0][3]:n&&(r.m=t[0][2])),r}function Ia(t,e,n,r){const a={x:(t[0]+e[0])/2,y:(t[1]+e[1])/2};return n&&(a.z=(t[2]+e[2])/2),n&&r?a.m=(t[3]+e[3])/2:r&&(a.m=(t[2]+e[2])/2),a}function ka(t,e){if(t.length<=1)return 0;let n=0;for(let r=1;r<t.length;r++)n+=mi(t[r-1],t[r],e);return n}function mi(t,e,n){const r=e[0]-t[0],a=e[1]-t[1];if(n){const u=e[2]-e[2];return Math.sqrt(r*r+a*a+u*u)}return Math.sqrt(r*r+a*a)}function Ba(t,e){if(t.length!==e.length)return!1;for(let n=0;n<t.length;n++)if(t[n]!==e[n])return!1;return!0}function Ta(t){const e={x:0,y:0,spatialReference:t.spatialReference.toJSON()},n={x:0,y:0,spatialReference:t.spatialReference.toJSON()};let r=0,a=0;for(let u=0;u<t.paths.length;u++){if(t.paths[u].length===0)continue;const s=ka(t.paths[u],t.hasZ===!0);if(s===0){const i=Fr(t.paths[u],t.hasZ===!0,t.hasM===!0);e.x+=i.x,e.y+=i.y,t.hasZ===!0&&(e.z+=i.z),t.hasM===!0&&(e.m+=i.m),++r}else{const i=Fr(t.paths[u],t.hasZ===!0,t.hasM===!0);n.x+=i.x*s,n.y+=i.y*s,t.hasZ===!0&&(n.z+=i.z*s),t.hasM===!0&&(n.m+=i.m*s),a+=s}}return a>0?(n.x/=a,n.y/=a,t.hasZ===!0&&(n.z/=a),t.hasM===!0&&(n.m/=a),new H(n)):r>0?(e.x/=r,e.y/=r,t.hasZ===!0&&(n.z/=r),t.hasM===!0&&(e.m/=r),new H(e)):null}function _a(t){if(t.points.length===0)return null;let e=0,n=0,r=0,a=0;for(let s=0;s<t.points.length;s++){const i=t.getPoint(s);i.hasZ===!0&&(r+=i.z),i.hasM===!0&&(a+=i.m),e+=i.x,n+=i.y,a+=i.m}const u={x:e/t.points.length,y:n/t.points.length,spatialReference:null};return u.spatialReference=t.spatialReference.toJSON(),t.hasZ===!0&&(u.z=r/t.points.length),t.hasM===!0&&(u.m=a/t.points.length),new H(u)}function Ma(t,e){return t.x*e.x+t.y*e.y}function $a(t,e){return t.x*e.y-e.x*t.y}function en(t,e,n=0){for(;t<n;)t+=e;const r=n+e;for(;t>=r;)t-=e;return t}function pi(t,e){return Math.atan2(e.y-t.y,e.x-t.x)}function Na(t,e){return en(pi(t,e),2*Math.PI)*(180/Math.PI)}function La(t,e){return en(Math.PI/2-pi(t,e),2*Math.PI)*(180/Math.PI)}function Di(t,e,n){const r={x:t.x-e.x,y:t.y-e.y},a={x:n.x-e.x,y:n.y-e.y};return Math.atan2($a(r,a),Ma(r,a))}function Ra(t,e,n){return en(Di(t,e,n),2*Math.PI)*(180/Math.PI)}function Pa(t,e,n){return en(-1*Di(t,e,n),2*Math.PI)*(180/Math.PI)}At[9002]=.3048,At[9003]=.3048006096012192,At[9095]=.3048007491;const ne=[0,0];function Cr(t){for(let e=0;e<t.length;e++){const n=t[e];for(let a=0;a<n.length-1;a++){const u=n[a],s=n[a+1];for(let i=e+1;i<t.length;i++)for(let o=0;o<t[i].length-1;o++){const l=t[i][o],m=t[i][o+1];if(sr(u,s,l,m,ne)&&!(ne[0]===u[0]&&ne[1]===u[1]||ne[0]===l[0]&&ne[1]===l[1]||ne[0]===s[0]&&ne[1]===s[1]||ne[0]===m[0]&&ne[1]===m[1]))return!0}}const r=n.length;if(!(r<3))for(let a=0;a<=r-2;a++){const u=n[a],s=n[a+1];for(let i=a+2;i<=r-2;i++){const o=n[i],l=n[i+1];if(sr(u,s,o,l,ne)&&!(ne[0]===u[0]&&ne[1]===u[1]||ne[0]===o[0]&&ne[1]===o[1]||ne[0]===s[0]&&ne[1]===s[1]||ne[0]===l[0]&&ne[1]===l[1]))return!0}}}return!1}function Mt(t){return t&&t.arcadeDeclaredClass==="esri.arcade.Feature"}function gi(t,e){t.ringisclockwise=function(n,r){return e(n,r,(a,u,s)=>{D(s,1,1,n,r);let i=[],o=!1,l=!1;if(s[0]===null)return!1;if(k(s[0])){for(const m of s[0]){if(!(m instanceof H))throw new h(n,c.InvalidParameter,r);i.push(m.hasZ?m.hasM?[m.x,m.y,m.z,m.m]:[m.x,m.y,m.z]:[m.x,m.y])}i.length>0&&(o=s[0][0].hasZ,l=s[0][0].hasM)}else if(s[0]instanceof Ye)i=s[0]._elements,i.length>0&&(o=s[0]._hasZ,l=s[0]._hasM);else{if(!N(s[0]))throw new h(n,c.InvalidParameter,r);for(const m of s[0].toArray()){if(!(m instanceof H))throw new h(n,c.InvalidParameter,r);i.push(m.hasZ?m.hasM?[m.x,m.y,m.z,m.m]:[m.x,m.y,m.z]:[m.x,m.y])}i.length>0&&(o=s[0].get(0).hasZ,l=s[0].get(0).hasM)}return!(i.length<3)&&jr(i,l,o)})},t.polygon=function(n,r){return e(n,r,(a,u,s)=>{D(s,1,1,n,r);let i=null;if(s[0]instanceof _){if(i=ee(X.parseGeometryFromDictionary(s[0]),n.spatialReference),!(i instanceof ae))throw new h(n,c.InvalidParameter,r)}else i=s[0]instanceof ae?de(s[0].toJSON()):ee(new ae(JSON.parse(s[0])),n.spatialReference);if(i!==null&&i.spatialReference.equals(n.spatialReference)===!1)throw new h(n,c.WrongSpatialReference,r);return Ve(i)})},t.polyline=function(n,r){return e(n,r,(a,u,s)=>{D(s,1,1,n,r);let i=null;if(s[0]instanceof _){if(i=ee(X.parseGeometryFromDictionary(s[0]),n.spatialReference),!(i instanceof ie))throw new h(n,c.InvalidParameter,r)}else i=s[0]instanceof ie?de(s[0].toJSON()):ee(new ie(JSON.parse(s[0])),n.spatialReference);if(i!==null&&i.spatialReference.equals(n.spatialReference)===!1)throw new h(n,c.WrongSpatialReference,r);return Ve(i)})},t.point=function(n,r){return e(n,r,(a,u,s)=>{D(s,1,1,n,r);let i=null;if(s[0]instanceof _){if(i=ee(X.parseGeometryFromDictionary(s[0]),n.spatialReference),!(i instanceof H))throw new h(n,c.InvalidParameter,r)}else i=s[0]instanceof H?de(s[0].toJSON()):ee(new H(JSON.parse(s[0])),n.spatialReference);if(i!==null&&i.spatialReference.equals(n.spatialReference)===!1)throw new h(n,c.WrongSpatialReference,r);return Ve(i)})},t.multipoint=function(n,r){return e(n,r,(a,u,s)=>{D(s,1,1,n,r);let i=null;if(s[0]instanceof _){if(i=ee(X.parseGeometryFromDictionary(s[0]),n.spatialReference),!(i instanceof we))throw new h(n,c.InvalidParameter,r)}else i=s[0]instanceof we?de(s[0].toJSON()):ee(new we(JSON.parse(s[0])),n.spatialReference);if(i!==null&&i.spatialReference.equals(n.spatialReference)===!1)throw new h(n,c.WrongSpatialReference,r);return Ve(i)})},t.extent=function(n,r){return e(n,r,(a,u,s)=>{var o;s=O(s),D(s,1,1,n,r);let i=null;if(s[0]instanceof _)i=ee(X.parseGeometryFromDictionary(s[0]),n.spatialReference);else if(s[0]instanceof H){const l={xmin:s[0].x,ymin:s[0].y,xmax:s[0].x,ymax:s[0].y,spatialReference:s[0].spatialReference.toJSON()},m=s[0];m.hasZ?(l.zmin=m.z,l.zmax=m.z):m.hasM&&(l.mmin=m.m,l.mmax=m.m),i=de(l)}else i=s[0]instanceof ae||s[0]instanceof ie||s[0]instanceof we?de((o=s[0].extent)==null?void 0:o.toJSON()):s[0]instanceof he?de(s[0].toJSON()):ee(new he(JSON.parse(s[0])),n.spatialReference);if(i!==null&&i.spatialReference.equals(n.spatialReference)===!1)throw new h(n,c.WrongSpatialReference,r);return Ve(i)})},t.geometry=function(n,r){return e(n,r,(a,u,s)=>{D(s,1,1,n,r);let i=null;if(s[0]===null)return null;if(i=Mt(s[0])?ee(s[0].geometry(),n.spatialReference):s[0]instanceof _?ee(X.parseGeometryFromDictionary(s[0]),n.spatialReference):ee(de(JSON.parse(s[0])),n.spatialReference),i!==null&&i.spatialReference.equals(n.spatialReference)===!1)throw new h(n,c.WrongSpatialReference,r);return Ve(i)})},t.setgeometry=function(n,r){return e(n,r,(a,u,s)=>{if(D(s,2,2,n,r),!Mt(s[0]))throw new h(n,c.InvalidParameter,r);if(s[0].immutable===!0)throw new h(n,c.Immutable,r);if(!(s[1]instanceof T||s[1]===null))throw new h(n,c.InvalidParameter,r);return s[0]._geometry=s[1],C})},t.feature=function(n,r){return e(n,r,(a,u,s)=>{if(s.length===0)throw new h(n,c.WrongNumberOfParameters,r);let i=null;if(s.length===1)if(b(s[0]))i=X.fromJson(JSON.parse(s[0]),n.timeReference);else if(Mt(s[0]))i=X.createFromArcadeFeature(s[0]);else if(s[0]instanceof T)i=X.createFromGraphicLikeObject(s[0],null,null,n.timeReference);else{if(!(s[0]instanceof _))throw new h(n,c.InvalidParameter,r);{let o=s[0].hasField("geometry")?s[0].field("geometry"):null,l=s[0].hasField("attributes")?s[0].field("attributes"):null;o!==null&&o instanceof _&&(o=X.parseGeometryFromDictionary(o)),l!==null&&(l=X.parseAttributesFromDictionary(l)),i=X.createFromGraphicLikeObject(o,l,null,n.timeReference)}}else if(s.length===2){let o=null,l=null;if(s[0]!==null)if(s[0]instanceof T)o=s[0];else{if(!(o instanceof _))throw new h(n,c.InvalidParameter,r);o=X.parseGeometryFromDictionary(s[0])}if(s[1]!==null){if(!(s[1]instanceof _))throw new h(n,c.InvalidParameter,r);l=X.parseAttributesFromDictionary(s[1])}i=X.createFromGraphicLikeObject(o,l,null,n.timeReference)}else{let o=null;const l={};if(s[0]!==null)if(s[0]instanceof T)o=s[0];else{if(!(o instanceof _))throw new h(n,c.InvalidParameter,r);o=X.parseGeometryFromDictionary(s[0])}for(let m=1;m<s.length;m+=2){const f=A(s[m]),p=s[m+1];if(!(p==null||b(p)||isNaN(p)||j(p)||G(p)||U(p)))throw new h(n,c.InvalidParameter,r);if(K(p)||ze(p)===!1)throw new h(n,c.InvalidParameter,r);l[f]=p===C?null:p}i=X.createFromGraphicLikeObject(o,l,null,n.timeReference)}return i._geometry=ee(i.geometry(),n.spatialReference),i.immutable=!1,i})},t.dictionary=function(n,r){return e(n,r,(a,u,s)=>{if(s.length===0){const l=new _;return l.immutable=!1,l}if(s.length===1&&b(s[0]))try{const l=JSON.parse(s[0]),m=_.convertObjectToArcadeDictionary(l,P(n),!1);return m.immutable=!1,m}catch{throw new h(n,c.InvalidParameter,r)}if(s.length%2!=0)throw new h(n,c.WrongNumberOfParameters,r);const i={};for(let l=0;l<s.length;l+=2){const m=A(s[l]),f=s[l+1];if(!(f==null||b(f)||isNaN(f)||j(f)||G(f)||U(f)||k(f)||N(f)))throw new h(n,c.InvalidParameter,r);if(K(f))throw new h(n,c.InvalidParameter,r);i[m]=f===C?null:f}const o=new _(i);return o.immutable=!1,o})},t.haskey=function(n,r){return e(n,r,(a,u,s)=>{D(s,2,2,n,r);const i=A(s[1]);if(Mt(s[0])||s[0]instanceof _)return s[0].hasField(i);if(s[0]instanceof T){const o=vt(s[0],i,null,null,2);return!o||o.keystate!=="notfound"}throw new h(n,c.InvalidParameter,r)})},t.hasvalue=function(n,r){return e(n,r,(a,u,s)=>{if(D(s,2,2,n,r),s[0]===null||s[1]===null)return!1;const i=A(s[1]);return Q(s[0])||s[0]instanceof _?!!s[0].hasField(i)&&s[0].field(i)!==null:s[0]instanceof T?vt(s[0],i,null,null,0)!==null:!1})},t.indexof=function(n,r){return e(n,r,(a,u,s)=>{D(s,2,2,n,r);const i=s[1];if(k(s[0])){for(let o=0;o<s[0].length;o++)if(xe(i,s[0][o]))return o;return-1}if(N(s[0])){const o=s[0].length();for(let l=0;l<o;l++)if(xe(i,s[0].get(l)))return l;return-1}throw new h(n,c.InvalidParameter,r)})},t.angle=function(n,r){return e(n,r,(a,u,s)=>{if(s=O(s),D(s,2,3,n,r),!(s[0]instanceof H))throw new h(n,c.InvalidParameter,r);if(!(s[1]instanceof H))throw new h(n,c.InvalidParameter,r);if(s.length>2&&!(s[2]instanceof H))throw new h(n,c.InvalidParameter,r);return s.length===2?Na(s[0],s[1]):Ra(s[0],s[1],s[2])})},t.bearing=function(n,r){return e(n,r,(a,u,s)=>{if(s=O(s),D(s,2,3,n,r),!(s[0]instanceof H))throw new h(n,c.InvalidParameter,r);if(!(s[1]instanceof H))throw new h(n,c.InvalidParameter,r);if(s.length>2&&!(s[2]instanceof H))throw new h(n,c.InvalidParameter,r);return s.length===2?La(s[0],s[1]):Pa(s[0],s[1],s[2])})},t.isselfintersecting=function(n,r){return e(n,r,(a,u,s)=>{s=O(s),D(s,1,1,n,r);let i=s[0];if(i instanceof ae)return i.isSelfIntersecting;if(i instanceof ie)return i=i.paths,Cr(i);if(i instanceof we){const o=i.points;for(let l=0;l<o.length;l++)for(let m=0;m<o.length;m++)if(m!==l){let f=!0;for(let p=0;p<o[l].length;p++)if(o[l][p]!==o[m][p]){f=!1;break}if(f===!0)return!0}}return!(!k(i)&&!N(i))&&(i=Ct(i,n.spatialReference),i!==null&&(i=i.paths),Cr(i))})}}let Be=0;function vt(t,e,n,r,a=1){let u;switch(e=e.toLowerCase()){case"hasz":{const s=t.hasZ;return s!==void 0&&s}case"hasm":{const s=t.hasM;return s!==void 0&&s}case"spatialreference":{let s=t.spatialReference._arcadeCacheId;if(s===void 0){let o=!0;Object.freeze&&Object.isFrozen(t.spatialReference)&&(o=!1),o&&(Be++,t.spatialReference._arcadeCacheId=Be,s=Be)}const i=new _({wkt:t.spatialReference.wkt,wkid:t.spatialReference.wkid});return s!==void 0&&(i._arcadeCacheId="SPREF"+s.toString()),i}}switch(t.type){case"extent":switch(e){case"xmin":case"xmax":case"ymin":case"ymax":case"zmin":case"zmax":case"mmin":case"mmax":{const s=t[e];return s!==void 0?s:null}case"type":return"Extent"}break;case"polygon":switch(e){case"rings":return u=t.cache._arcadeCacheId,u===void 0&&(Be++,u=Be,t.cache._arcadeCacheId=u),new En(t.rings,t.spatialReference,t.hasZ===!0,t.hasM===!0,u);case"type":return"Polygon"}break;case"point":switch(e){case"x":case"y":case"z":case"m":return t[e]!==void 0?t[e]:null;case"type":return"Point"}break;case"polyline":switch(e){case"paths":return u=t.cache._arcadeCacheId,u===void 0&&(Be++,u=Be,t.cache._arcadeCacheId=u),new En(t.paths,t.spatialReference,t.hasZ===!0,t.hasM===!0,u);case"type":return"Polyline"}break;case"multipoint":switch(e){case"points":return u=t.cache._arcadeCacheId,u===void 0&&(Be++,u=Be,t.cache._arcadeCacheId=u),new Ye(t.points,t.spatialReference,t.hasZ===!0,t.hasM===!0,u,1);case"type":return"Multipoint"}}if(a===1)throw new h(n,c.InvalidIdentifier,r);return a===2?{keystate:"notfound"}:null}function $t(t){if(t==null)return null;if(typeof t=="number")return t;let e=t.toLowerCase();switch(e=e.replace(/\s/g,""),e=e.replace(/-/g,""),e){case"meters":case"meter":case"m":case"squaremeters":case"squaremeter":return 109404;case"miles":case"mile":case"squaremile":case"squaremiles":return 109439;case"kilometers":case"kilometer":case"squarekilometers":case"squarekilometer":case"km":return 109414;case"acres":case"acre":case"ac":return 109402;case"hectares":case"hectare":case"ha":return 109401;case"yard":case"yd":case"yards":case"squareyards":case"squareyard":return 109442;case"feet":case"ft":case"foot":case"squarefeet":case"squarefoot":return 109405;case"nmi":case"nauticalmile":case"nauticalmiles":case"squarenauticalmile":case"squarenauticalmiles":return 109409}return null}function qo(t){if(t==null)return null;switch(t.type){case"polygon":case"multipoint":case"polyline":return t.extent;case"point":return new he({xmin:t.x,ymin:t.y,xmax:t.x,ymax:t.y,spatialReference:t.spatialReference});case"extent":return t}return null}function re(t){if(t==null)return null;if(typeof t=="number")return t;let e=t.toLowerCase();switch(e=e.replace(/\s/g,""),e=e.replace(/-/g,""),e){case"meters":case"meter":case"m":case"squaremeters":case"squaremeter":return 9001;case"miles":case"mile":case"squaremile":case"squaremiles":return 9093;case"kilometers":case"kilometer":case"squarekilometers":case"squarekilometer":case"km":return 9036;case"yard":case"yd":case"yards":case"squareyards":case"squareyard":return 9096;case"feet":case"ft":case"foot":case"squarefeet":case"squarefoot":return 9002;case"nmi":case"nauticalmile":case"nauticalmiles":case"squarenauticalmile":case"squarenauticalmiles":return 9030}return null}function Fe(t){if(t==null)return null;const e=t.clone();return t.cache._geVersion!==void 0&&(e.cache._geVersion=t.cache._geVersion),e}let L=null;function Ar(t){return la.indexOf("4.")===0?ae.fromExtent(t):new ae({spatialReference:t.spatialReference,rings:[[[t.xmin,t.ymin],[t.xmin,t.ymax],[t.xmax,t.ymax],[t.xmax,t.ymin],[t.xmin,t.ymin]]]})}function Oa(t){L=t}function Er(t,e){if(t.type!=="polygon"&&t.type!=="polyline"&&t.type!=="extent")return 0;let n=1;(t.spatialReference.vcsWkid||t.spatialReference.latestVcsWkid)&&(n=va(t.spatialReference)/ua(t.spatialReference));let r=0;if(t.type==="polyline")for(const u of t.paths)for(let s=1;s<u.length;s++)r+=Dt(u[s],u[s-1],n);else if(t.type==="polygon")for(const u of t.rings){for(let s=1;s<u.length;s++)r+=Dt(u[s],u[s-1],n);(u[0][0]!==u[u.length-1][0]||u[0][1]!==u[u.length-1][1]||u[0][2]!==void 0&&u[0][2]!==u[u.length-1][2])&&(r+=Dt(u[0],u[u.length-1],n))}else t.type==="extent"&&(r+=2*Dt([t.xmin,t.ymin,0],[t.xmax,t.ymin,0],n),r+=2*Dt([t.xmin,t.ymin,0],[t.xmin,t.ymax,0],n),r*=2,r+=4*Math.abs(M(t.zmax,0)*n-M(t.zmin,0)*n));const a=new ie({hasZ:!1,hasM:!1,spatialReference:t.spatialReference,paths:[[0,0],[0,r]]});return L.planarLength(a,e)}function Kn(t,e){function n(r,a,u){if(D(u,2,2,r,a),!(u[0]instanceof T&&u[1]instanceof T)){if(!(u[0]instanceof T&&u[1]===null)){if(!(u[1]instanceof T&&u[0]===null)){if(u[0]!==null||u[1]!==null)throw new h(r,c.InvalidParameter,a)}}}}t.disjoint=function(r,a){return e(r,a,(u,s,i)=>(i=O(i),n(r,a,i),i[0]===null||i[1]===null||L.disjoint(i[0],i[1])))},t.intersects=function(r,a){return e(r,a,(u,s,i)=>(i=O(i),n(r,a,i),i[0]!==null&&i[1]!==null&&L.intersects(i[0],i[1])))},t.touches=function(r,a){return e(r,a,(u,s,i)=>(i=O(i),n(r,a,i),i[0]!==null&&i[1]!==null&&L.touches(i[0],i[1])))},t.crosses=function(r,a){return e(r,a,(u,s,i)=>(i=O(i),n(r,a,i),i[0]!==null&&i[1]!==null&&L.crosses(i[0],i[1])))},t.within=function(r,a){return e(r,a,(u,s,i)=>(i=O(i),n(r,a,i),i[0]!==null&&i[1]!==null&&L.within(i[0],i[1])))},t.contains=function(r,a){return e(r,a,(u,s,i)=>(i=O(i),n(r,a,i),i[0]!==null&&i[1]!==null&&L.contains(i[0],i[1])))},t.overlaps=function(r,a){return e(r,a,(u,s,i)=>(i=O(i),n(r,a,i),i[0]!==null&&i[1]!==null&&L.overlaps(i[0],i[1])))},t.equals=function(r,a){return e(r,a,(u,s,i)=>(D(i,2,2,r,a),i[0]===i[1]||(i[0]instanceof T&&i[1]instanceof T?L.equals(i[0],i[1]):!(!j(i[0])||!j(i[1]))&&i[0].equals(i[1]))))},t.relate=function(r,a){return e(r,a,(u,s,i)=>{if(i=O(i),D(i,3,3,r,a),i[0]instanceof T&&i[1]instanceof T)return L.relate(i[0],i[1],A(i[2]));if(i[0]instanceof T&&i[1]===null||i[1]instanceof T&&i[0]===null||i[0]===null&&i[1]===null)return!1;throw new h(r,c.InvalidParameter,a)})},t.intersection=function(r,a){return e(r,a,(u,s,i)=>(i=O(i),n(r,a,i),i[0]===null||i[1]===null?null:L.intersect(i[0],i[1])))},t.union=function(r,a){return e(r,a,(u,s,i)=>{const o=[];if((i=O(i)).length===0)throw new h(r,c.WrongNumberOfParameters,a);if(i.length===1)if(k(i[0])){const l=O(i[0]);for(let m=0;m<l.length;m++)if(l[m]!==null){if(!(l[m]instanceof T))throw new h(r,c.InvalidParameter,a);o.push(l[m])}}else{if(!N(i[0])){if(i[0]instanceof T)return ee(Fe(i[0]),r.spatialReference);if(i[0]===null)return null;throw new h(r,c.InvalidParameter,a)}{const l=O(i[0].toArray());for(let m=0;m<l.length;m++)if(l[m]!==null){if(!(l[m]instanceof T))throw new h(r,c.InvalidParameter,a);o.push(l[m])}}}else for(let l=0;l<i.length;l++)if(i[l]!==null){if(!(i[l]instanceof T))throw new h(r,c.InvalidParameter,a);o.push(i[l])}return o.length===0?null:L.union(o)})},t.difference=function(r,a){return e(r,a,(u,s,i)=>(i=O(i),n(r,a,i),i[0]!==null&&i[1]===null?Fe(i[0]):i[0]===null?null:L.difference(i[0],i[1])))},t.symmetricdifference=function(r,a){return e(r,a,(u,s,i)=>(i=O(i),n(r,a,i),i[0]===null&&i[1]===null?null:i[0]===null?Fe(i[1]):i[1]===null?Fe(i[0]):L.symmetricDifference(i[0],i[1])))},t.clip=function(r,a){return e(r,a,(u,s,i)=>{if(i=O(i),D(i,2,2,r,a),!(i[1]instanceof he)&&i[1]!==null)throw new h(r,c.InvalidParameter,a);if(i[0]===null)return null;if(!(i[0]instanceof T))throw new h(r,c.InvalidParameter,a);return i[1]===null?null:L.clip(i[0],i[1])})},t.cut=function(r,a){return e(r,a,(u,s,i)=>{if(i=O(i),D(i,2,2,r,a),!(i[1]instanceof ie)&&i[1]!==null)throw new h(r,c.InvalidParameter,a);if(i[0]===null)return[];if(!(i[0]instanceof T))throw new h(r,c.InvalidParameter,a);return i[1]===null?[Fe(i[0])]:L.cut(i[0],i[1])})},t.area=function(r,a){return e(r,a,(u,s,i)=>{if(D(i,1,2,r,a),(i=O(i))[0]===null)return 0;if(k(i[0])||N(i[0])){const o=In(i[0],r.spatialReference);return o===null?0:L.planarArea(o,$t(M(i[1],-1)))}if(!(i[0]instanceof T))throw new h(r,c.InvalidParameter,a);return L.planarArea(i[0],$t(M(i[1],-1)))})},t.areageodetic=function(r,a){return e(r,a,(u,s,i)=>{if(D(i,1,2,r,a),(i=O(i))[0]===null)return 0;if(k(i[0])||N(i[0])){const o=In(i[0],r.spatialReference);return o===null?0:L.geodesicArea(o,$t(M(i[1],-1)))}if(!(i[0]instanceof T))throw new h(r,c.InvalidParameter,a);return L.geodesicArea(i[0],$t(M(i[1],-1)))})},t.length=function(r,a){return e(r,a,(u,s,i)=>{if(D(i,1,2,r,a),(i=O(i))[0]===null)return 0;if(k(i[0])||N(i[0])){const o=Ct(i[0],r.spatialReference);return o===null?0:L.planarLength(o,re(M(i[1],-1)))}if(!(i[0]instanceof T))throw new h(r,c.InvalidParameter,a);return L.planarLength(i[0],re(M(i[1],-1)))})},t.length3d=function(r,a){return e(r,a,(u,s,i)=>{if(D(i,1,2,r,a),(i=O(i))[0]===null)return 0;if(k(i[0])||N(i[0])){const o=Ct(i[0],r.spatialReference);return o===null?0:o.hasZ===!0?Er(o,re(M(i[1],-1))):L.planarLength(o,re(M(i[1],-1)))}if(!(i[0]instanceof T))throw new h(r,c.InvalidParameter,a);return i[0].hasZ===!0?Er(i[0],re(M(i[1],-1))):L.planarLength(i[0],re(M(i[1],-1)))})},t.lengthgeodetic=function(r,a){return e(r,a,(u,s,i)=>{if(D(i,1,2,r,a),(i=O(i))[0]===null)return 0;if(k(i[0])||N(i[0])){const o=Ct(i[0],r.spatialReference);return o===null?0:L.geodesicLength(o,re(M(i[1],-1)))}if(!(i[0]instanceof T))throw new h(r,c.InvalidParameter,a);return L.geodesicLength(i[0],re(M(i[1],-1)))})},t.distance=function(r,a){return e(r,a,(u,s,i)=>{i=O(i),D(i,2,3,r,a);let o=i[0];(k(i[0])||N(i[0]))&&(o=Lt(i[0],r.spatialReference));let l=i[1];if((k(i[1])||N(i[1]))&&(l=Lt(i[1],r.spatialReference)),!(o instanceof T))throw new h(r,c.InvalidParameter,a);if(!(l instanceof T))throw new h(r,c.InvalidParameter,a);return L.distance(o,l,re(M(i[2],-1)))})},t.distancegeodetic=function(r,a){return e(r,a,(u,s,i)=>{i=O(i),D(i,2,3,r,a);const o=i[0],l=i[1];if(!(o instanceof H))throw new h(r,c.InvalidParameter,a);if(!(l instanceof H))throw new h(r,c.InvalidParameter,a);const m=new ie({paths:[],spatialReference:o.spatialReference});return m.addPath([o,l]),L.geodesicLength(m,re(M(i[2],-1)))})},t.densify=function(r,a){return e(r,a,(u,s,i)=>{if(i=O(i),D(i,2,3,r,a),i[0]===null)return null;if(!(i[0]instanceof T))throw new h(r,c.InvalidParameter,a);const o=d(i[1]);if(isNaN(o))throw new h(r,c.InvalidParameter,a);if(o<=0)throw new h(r,c.InvalidParameter,a);return i[0]instanceof ae||i[0]instanceof ie?L.densify(i[0],o,re(M(i[2],-1))):i[0]instanceof he?L.densify(Ar(i[0]),o,re(M(i[2],-1))):i[0]})},t.densifygeodetic=function(r,a){return e(r,a,(u,s,i)=>{if(i=O(i),D(i,2,3,r,a),i[0]===null)return null;if(!(i[0]instanceof T))throw new h(r,c.InvalidParameter,a);const o=d(i[1]);if(isNaN(o))throw new h(r,c.InvalidParameter,a);if(o<=0)throw new h(r,c.InvalidParameter,a);return i[0]instanceof ae||i[0]instanceof ie?L.geodesicDensify(i[0],o,re(M(i[2],-1))):i[0]instanceof he?L.geodesicDensify(Ar(i[0]),o,re(M(i[2],-1))):i[0]})},t.generalize=function(r,a){return e(r,a,(u,s,i)=>{if(i=O(i),D(i,2,4,r,a),i[0]===null)return null;if(!(i[0]instanceof T))throw new h(r,c.InvalidParameter,a);const o=d(i[1]);if(isNaN(o))throw new h(r,c.InvalidParameter,a);return L.generalize(i[0],o,ct(M(i[2],!0)),re(M(i[3],-1)))})},t.buffer=function(r,a){return e(r,a,(u,s,i)=>{if(i=O(i),D(i,2,3,r,a),i[0]===null)return null;if(!(i[0]instanceof T))throw new h(r,c.InvalidParameter,a);const o=d(i[1]);if(isNaN(o))throw new h(r,c.InvalidParameter,a);return o===0?Fe(i[0]):L.buffer(i[0],o,re(M(i[2],-1)))})},t.buffergeodetic=function(r,a){return e(r,a,(u,s,i)=>{if(i=O(i),D(i,2,3,r,a),i[0]===null)return null;if(!(i[0]instanceof T))throw new h(r,c.InvalidParameter,a);const o=d(i[1]);if(isNaN(o))throw new h(r,c.InvalidParameter,a);return o===0?Fe(i[0]):L.geodesicBuffer(i[0],o,re(M(i[2],-1)))})},t.offset=function(r,a){return e(r,a,(u,s,i)=>{if(i=O(i),D(i,2,6,r,a),i[0]===null)return null;if(!(i[0]instanceof ae||i[0]instanceof ie))throw new h(r,c.InvalidParameter,a);const o=d(i[1]);if(isNaN(o))throw new h(r,c.InvalidParameter,a);const l=d(M(i[4],10));if(isNaN(l))throw new h(r,c.InvalidParameter,a);const m=d(M(i[5],0));if(isNaN(m))throw new h(r,c.InvalidParameter,a);return L.offset(i[0],o,re(M(i[2],-1)),A(M(i[3],"round")).toLowerCase(),l,m)})},t.rotate=function(r,a){return e(r,a,(u,s,i)=>{i=O(i),D(i,2,3,r,a);let o=i[0];if(o===null)return null;if(!(o instanceof T))throw new h(r,c.InvalidParameter,a);o instanceof he&&(o=ae.fromExtent(o));const l=d(i[1]);if(isNaN(l))throw new h(r,c.InvalidParameter,a);const m=M(i[2],null);if(m===null)return L.rotate(o,l);if(m instanceof H)return L.rotate(o,l,m);throw new h(r,c.InvalidParameter,a)})},t.centroid=function(r,a){return e(r,a,(u,s,i)=>{if(i=O(i),D(i,1,1,r,a),i[0]===null)return null;let o=i[0];if((k(i[0])||N(i[0]))&&(o=Lt(i[0],r.spatialReference)),o===null)return null;if(!(o instanceof T))throw new h(r,c.InvalidParameter,a);return o instanceof H?ee(Fe(i[0]),r.spatialReference):o instanceof ae?o.centroid:o instanceof ie?Ta(o):o instanceof we?_a(o):o instanceof he?o.center:null})},t.multiparttosinglepart=function(r,a){return e(r,a,(u,s,i)=>{i=O(i),D(i,1,1,r,a);const o=[];if(i[0]===null)return null;if(!(i[0]instanceof T))throw new h(r,c.InvalidParameter,a);if(i[0]instanceof H)return[ee(Fe(i[0]),r.spatialReference)];if(i[0]instanceof he)return[ee(Fe(i[0]),r.spatialReference)];const l=L.simplify(i[0]);if(l instanceof ae){const m=[],f=[];for(let p=0;p<l.rings.length;p++)if(l.isClockwise(l.rings[p])){const g=de({rings:[l.rings[p]],hasZ:l.hasZ===!0,hasM:l.hasM===!0,spatialReference:l.spatialReference.toJSON()});m.push(g)}else f.push({ring:l.rings[p],pt:l.getPoint(p,0)});for(let p=0;p<f.length;p++)for(let g=0;g<m.length;g++)if(m[g].contains(f[p].pt)){m[g].addRing(f[p].ring);break}return m}if(l instanceof ie){const m=[];for(let f=0;f<l.paths.length;f++){const p=de({paths:[l.paths[f]],hasZ:l.hasZ===!0,hasM:l.hasM===!0,spatialReference:l.spatialReference.toJSON()});m.push(p)}return m}if(i[0]instanceof we){const m=ee(Fe(i[0]),r.spatialReference);for(let f=0;f<m.points.length;f++)o.push(m.getPoint(f));return o}return null})},t.issimple=function(r,a){return e(r,a,(u,s,i)=>{if(i=O(i),D(i,1,1,r,a),i[0]===null)return!0;if(!(i[0]instanceof T))throw new h(r,c.InvalidParameter,a);return L.isSimple(i[0])})},t.simplify=function(r,a){return e(r,a,(u,s,i)=>{if(i=O(i),D(i,1,1,r,a),i[0]===null)return null;if(!(i[0]instanceof T))throw new h(r,c.InvalidParameter,a);return L.simplify(i[0])})},t.convexhull=function(r,a){return e(r,a,(u,s,i)=>{if(i=O(i),D(i,1,1,r,a),i[0]===null)return null;if(!(i[0]instanceof T))throw new h(r,c.InvalidParameter,a);return L.convexHull(i[0])})}}const Ua=Object.freeze(Object.defineProperty({__proto__:null,registerFunctions:Kn,setGeometryEngine:Oa},Symbol.toStringTag,{value:"Module"}));function cn(t,e,n){return n===void 0||+n==0?Math[t](e):(e=+e,n=+n,isNaN(e)||typeof n!="number"||n%1!=0?NaN:(e=e.toString().split("e"),+((e=(e=Math[t](+(e[0]+"e"+(e[1]?+e[1]-n:-n)))).toString().split("e"))[0]+"e"+(e[1]?+e[1]+n:n))))}function yi(t,e){function n(r,a,u){const s=d(r);return isNaN(s)?s:isNaN(a)||isNaN(u)||a>u?NaN:s<a?a:s>u?u:s}t.number=function(r,a){return e(r,a,(u,s,i)=>{D(i,1,2,r,a);const o=i[0];if(G(o))return o;if(o===null)return 0;if(j(o))return o.toNumber();if(U(o))return Number(o);if(k(o))return NaN;if(o===""||o===void 0)return Number(o);if(b(o)){if(i[1]!==void 0){let l=Ge(i[1],"‰","");return l=Ge(l,"¤",""),Xr(o,{pattern:l})}return Number(o.trim())}return Number(o)})},t.abs=function(r,a){return e(r,a,(u,s,i)=>(D(i,1,1,r,a),Math.abs(d(i[0]))))},t.acos=function(r,a){return e(r,a,(u,s,i)=>(D(i,1,1,r,a),Math.acos(d(i[0]))))},t.asin=function(r,a){return e(r,a,(u,s,i)=>(D(i,1,1,r,a),Math.asin(d(i[0]))))},t.atan=function(r,a){return e(r,a,(u,s,i)=>(D(i,1,1,r,a),Math.atan(d(i[0]))))},t.atan2=function(r,a){return e(r,a,(u,s,i)=>(D(i,2,2,r,a),Math.atan2(d(i[0]),d(i[1]))))},t.ceil=function(r,a){return e(r,a,(u,s,i)=>{if(D(i,1,2,r,a),i.length===2){let o=d(i[1]);return isNaN(o)&&(o=0),cn("ceil",d(i[0]),-1*o)}return Math.ceil(d(i[0]))})},t.round=function(r,a){return e(r,a,(u,s,i)=>{if(D(i,1,2,r,a),i.length===2){let o=d(i[1]);return isNaN(o)&&(o=0),cn("round",d(i[0]),-1*o)}return Math.round(d(i[0]))})},t.floor=function(r,a){return e(r,a,(u,s,i)=>{if(D(i,1,2,r,a),i.length===2){let o=d(i[1]);return isNaN(o)&&(o=0),cn("floor",d(i[0]),-1*o)}return Math.floor(d(i[0]))})},t.cos=function(r,a){return e(r,a,(u,s,i)=>(D(i,1,1,r,a),Math.cos(d(i[0]))))},t.isnan=function(r,a){return e(r,a,(u,s,i)=>(D(i,1,1,r,a),typeof i[0]=="number"&&isNaN(i[0])))},t.exp=function(r,a){return e(r,a,(u,s,i)=>(D(i,1,1,r,a),Math.exp(d(i[0]))))},t.log=function(r,a){return e(r,a,(u,s,i)=>(D(i,1,1,r,a),Math.log(d(i[0]))))},t.pow=function(r,a){return e(r,a,(u,s,i)=>(D(i,2,2,r,a),d(i[0])**d(i[1])))},t.random=function(r,a){return e(r,a,(u,s,i)=>(D(i,0,0,r,a),Math.random()))},t.sin=function(r,a){return e(r,a,(u,s,i)=>(D(i,1,1,r,a),Math.sin(d(i[0]))))},t.sqrt=function(r,a){return e(r,a,(u,s,i)=>(D(i,1,1,r,a),Math.sqrt(d(i[0]))))},t.tan=function(r,a){return e(r,a,(u,s,i)=>(D(i,1,1,r,a),Math.tan(d(i[0]))))},t.defaultvalue=function(r,a){return e(r,a,(u,s,i)=>(D(i,2,2,r,a),i[0]===null||i[0]===""||i[0]===void 0?i[1]:i[0]))},t.isempty=function(r,a){return e(r,a,(u,s,i)=>(D(i,1,1,r,a),i[0]===null||i[0]===""||i[0]===void 0))},t.boolean=function(r,a){return e(r,a,(u,s,i)=>{D(i,1,1,r,a);const o=i[0];return ct(o)})},t.constrain=function(r,a){return e(r,a,(u,s,i)=>{D(i,3,3,r,a);const o=d(i[1]),l=d(i[2]);if(k(i[0])){const m=[];for(const f of i[0])m.push(n(f,o,l));return m}if(N(i[0])){const m=[];for(let f=0;f<i[0].length();f++)m.push(n(i[0].get(f),o,l));return m}return n(i[0],o,l)})}}function wi(t){let e=0;for(let n=0;n<t.length;n++)e+=t[n];return e/t.length}function br(t){const e=wi(t);let n=0;for(let r=0;r<t.length;r++)n+=(e-t[r])**2;return n/t.length}function Ga(t){let e=0;for(let n=0;n<t.length;n++)e+=t[n];return e}function za(t,e){const n=[],r={},a=[];for(let u=0;u<t.length;u++){if(t[u]!==void 0&&t[u]!==null&&t[u]!==C){const s=t[u];if(G(s)||b(s))r[s]===void 0&&(n.push(s),r[s]=1);else{let i=!1;for(let o=0;o<a.length;o++)xe(a[o],s)===!0&&(i=!0);i===!1&&(a.push(s),n.push(s))}}if(n.length>=e&&e!==-1)return n}return n}function hn(t,e,n=1e3){switch(t.toLowerCase()){case"distinct":return za(e,n);case"avg":case"mean":return wi(Je(e));case"min":return Math.min.apply(Math,Je(e));case"sum":return Ga(Je(e));case"max":return Math.max.apply(Math,Je(e));case"stdev":case"stddev":return Math.sqrt(br(Je(e)));case"var":case"variance":return br(Je(e));case"count":return e.length}return 0}function Re(t,e,n,r){if(r.length===1){if(k(r[0]))return hn(t,r[0],-1);if(N(r[0]))return hn(t,r[0].toArray(),-1)}return hn(t,r,-1)}function xi(t,e){t.stdev=function(n,r){return e(n,r,(a,u,s)=>Re("stdev",a,u,s))},t.variance=function(n,r){return e(n,r,(a,u,s)=>Re("variance",a,u,s))},t.average=function(n,r){return e(n,r,(a,u,s)=>Re("mean",a,u,s))},t.mean=function(n,r){return e(n,r,(a,u,s)=>Re("mean",a,u,s))},t.sum=function(n,r){return e(n,r,(a,u,s)=>Re("sum",a,u,s))},t.min=function(n,r){return e(n,r,(a,u,s)=>Re("min",a,u,s))},t.max=function(n,r){return e(n,r,(a,u,s)=>Re("max",a,u,s))},t.distinct=function(n,r){return e(n,r,(a,u,s)=>Re("distinct",a,u,s))},t.count=function(n,r){return e(n,r,(a,u,s)=>{if(D(s,1,1,n,r),k(s[0])||b(s[0]))return s[0].length;if(N(s[0]))return s[0].length();throw new h(n,c.InvalidParameter,r)})}}let Mn=class extends _{constructor(e){super(),this.declaredClass="esri.arcade.Portal",this.immutable=!1,this.setField("url",e),this.immutable=!0}},qa=class extends _{constructor(e,n,r,a,u,s){super(),this.attachmentUrl=u,this.declaredClass="esri.arcade.Attachment",this.immutable=!1,this.setField("id",e),this.setField("name",n),this.setField("contenttype",r),this.setField("size",a),this.setField("exifinfo",s),this.immutable=!0}};const Yn=t=>(e,n,r)=>(r=r||14,+t(e,n).toFixed(r)),Ja=(t,e)=>t+e,Va=(t,e)=>t*e,Ha=(t,e)=>t/e,Sr=(t,e,n)=>Yn(Ja)(t,e,n),ut=(t,e,n)=>Yn(Va)(t,e,n),zt=(t,e,n)=>Yn(Ha)(t,e,n),qt=360,Za=400,ja=2*Math.PI,Ce=3600,vr=3240,Et=60,Ue=60,Ir=180*Ce/Math.PI,wt=qt*Et*Ue,fn=90*Ce,Qe=180*Ce,Wa=270*Ce,Fi="ᵍ",xt="°";function gt(t){if(b(t)===!1)throw new h(null,c.InvalidParameter,null);return t}function $n(t,e){const n=10**e;return Math.round(t*n)/n}function Ka(t,e){return t%e}function et(t){const e=parseFloat(t.toString().replace(Math.trunc(t).toString(),"0"))*Math.sign(t);return t<0?{fraction:e,integer:Math.ceil(t)}:{fraction:e,integer:Math.floor(t)}}var Z,y,z,Nn;function st(t,e){switch(t){case Z.north:return e==="SHORT"?"N":"North";case Z.east:return e==="SHORT"?"E":"East";case Z.south:return e==="SHORT"?"S":"South";case Z.west:return e==="SHORT"?"W":"West"}}function dn(t,e,n){for(;t.length<n;)t=e+t;return t}function mn(t,e){return t-Math.floor(t/e)*e}function pn(t){switch(t){case y.truncated_degrees:case y.decimal_degrees:return qt;case y.radians:return ja;case y.gradians:return Za;case y.seconds:return wt;case y.fractional_degree_minutes:return Et;case y.fractional_minute_seconds:return Ue;default:throw new h(null,c.LogicError,null,{reason:"unsupported evaluations"})}}function kr(t){switch(t.toUpperCase().trim()){case"NORTH":case"NORTHAZIMUTH":case"NORTH AZIMUTH":return z.north_azimuth;case"POLAR":return z.polar;case"QUADRANT":return z.quadrant;case"SOUTH":case"SOUTHAZIMUTH":case"SOUTH AZIMUTH":return z.south_azimuth}throw new h(null,c.LogicError,null,{reason:"unsupported directionType"})}function Br(t){switch(t.toUpperCase().trim()){case"D":case"DD":case"DECIMALDEGREE":case"DECIMAL DEGREE":case"DEGREE":case"DECIMALDEGREES":case"DECIMAL DEGREES":case"DEGREES":return y.decimal_degrees;case"DMS":case"DEGREESMINUTESSECONDS":case"DEGREES MINUTES SECONDS":return y.degrees_minutes_seconds;case"R":case"RAD":case"RADS":case"RADIAN":case"RADIANS":return y.radians;case"G":case"GON":case"GONS":case"GRAD":case"GRADS":case"GRADIAN":case"GRADIANS":return y.gradians}throw new h(null,c.LogicError,null,{reason:"unsupported units"})}(function(t){t[t.north=0]="north",t[t.east=1]="east",t[t.south=2]="south",t[t.west=3]="west"})(Z||(Z={})),function(t){t[t.decimal_degrees=1]="decimal_degrees",t[t.seconds=2]="seconds",t[t.degrees_minutes_seconds=3]="degrees_minutes_seconds",t[t.radians=4]="radians",t[t.gradians=5]="gradians",t[t.truncated_degrees=6]="truncated_degrees",t[t.fractional_degree_minutes=7]="fractional_degree_minutes",t[t.fractional_minute_seconds=8]="fractional_minute_seconds"}(y||(y={})),function(t){t[t.north_azimuth=1]="north_azimuth",t[t.polar=2]="polar",t[t.quadrant=3]="quadrant",t[t.south_azimuth=4]="south_azimuth"}(z||(z={})),function(t){t[t.meridian=0]="meridian",t[t.direction=1]="direction"}(Nn||(Nn={}));let ft=class Pt{constructor(e,n,r){this.m_degrees=e,this.m_minutes=n,this.m_seconds=r}getField(e){switch(e){case y.decimal_degrees:case y.truncated_degrees:return this.m_degrees;case y.fractional_degree_minutes:return this.m_minutes;case y.seconds:case y.fractional_minute_seconds:return this.m_seconds;default:throw new h(null,c.LogicError,null,{reason:"unexpected evaluation"})}}static secondsToDMS(e){const n=et(e).fraction;let r=et(e).integer;const a=Math.floor(r/Ce);r-=a*Ce;const u=Math.floor(r/Ue);return r-=u*Ue,new Pt(a,u,r+n)}static numberToDms(e){const n=et(e).fraction,r=et(e).integer,a=ut(et(100*n).fraction,100),u=et(100*n).integer;return new Pt(r,u,a)}format(e,n){let r=$n(this.m_seconds,n),a=this.m_minutes,u=this.m_degrees;if(e===y.seconds||e===y.fractional_minute_seconds)Ue<=r&&(r-=Ue,++a),Et<=a&&(a=0,++u),qt<=u&&(u=0);else if(e===y.fractional_degree_minutes)r=0,a=30<=this.m_seconds?this.m_minutes+1:this.m_minutes,u=this.m_degrees,Et<=a&&(a=0,++u),qt<=u&&(u=0);else if(e===y.decimal_degrees||e===y.truncated_degrees){const s=zt(this.m_seconds,Ce),i=zt(this.m_minutes,Et);u=Math.round(this.m_degrees+i+s),a=0,r=0}return new Pt(u,a,r)}static dmsToSeconds(e,n,r){return e*Ce+n*Ue+r}},Ya=class{constructor(e,n,r){this.meridian=e,this.angle=n,this.direction=r}fetchAzimuth(e){return e===Nn.meridian?this.meridian:this.direction}},Pe=class Me{constructor(e){this._angle=e}static createFromAngleAndDirection(e,n){return new Me(new ve(Me._convertDirectionFormat(e.extractAngularUnits(y.seconds),n,z.north_azimuth)))}getAngle(e){const n=this._angle.extractAngularUnits(y.seconds);switch(e){case z.north_azimuth:case z.south_azimuth:case z.polar:return new ve(Me._convertDirectionFormat(n,z.north_azimuth,e));case z.quadrant:{const r=Me.secondsNorthAzimuthToQuadrant(n);return new ve(r.angle)}}}getMeridian(e){const n=this._angle.extractAngularUnits(y.seconds);switch(e){case z.north_azimuth:return Z.north;case z.south_azimuth:return Z.south;case z.polar:return Z.east;case z.quadrant:return Me.secondsNorthAzimuthToQuadrant(n).meridian}}getDirection(e){const n=this._angle.extractAngularUnits(y.seconds);switch(e){case z.north_azimuth:return Z.east;case z.south_azimuth:return Z.west;case z.polar:return Z.north;case z.quadrant:return Me.secondsNorthAzimuthToQuadrant(n).direction}}static secondsNorthAzimuthToQuadrant(e){const n=e<=fn||e>=Wa?Z.north:Z.south,r=n===Z.north?Math.min(wt-e,e):Math.abs(e-Qe),a=e>Qe?Z.west:Z.east;return new Ya(n,r,a)}static createFromAngleMeridianAndDirection(e,n,r){return new Me(new ve(Me.secondsQuadrantToNorthAzimuth(e.extractAngularUnits(y.seconds),n,r)))}static secondsQuadrantToNorthAzimuth(e,n,r){return n===Z.north?r===Z.east?e:wt-e:r===Z.east?Qe-e:Qe+e}static _convertDirectionFormat(e,n,r){let a=0;switch(n){case z.north_azimuth:a=e;break;case z.polar:a=fn-e;break;case z.quadrant:throw new h(null,c.LogicError,null,{reason:"unexpected evaluation"});case z.south_azimuth:a=e+Qe}let u=0;switch(r){case z.north_azimuth:u=a;break;case z.polar:u=fn-a;break;case z.quadrant:throw new h(null,c.LogicError,null,{reason:"unexpected evaluation"});case z.south_azimuth:u=a-Qe}return u=Ka(u,wt),u<0?wt+u:u}};function Tr(t,e,n){let r=null;switch(e){case y.decimal_degrees:r=ut(t,Ce);break;case y.seconds:r=t;break;case y.gradians:r=ut(t,vr);break;case y.radians:r=ut(t,Ir);break;default:throw new h(null,c.LogicError,null,{reason:"unexpected evaluation"})}switch(n){case y.decimal_degrees:return zt(r,Ce);case y.seconds:return r;case y.gradians:return zt(r,vr);case y.radians:return r/Ir;default:throw new h(null,c.LogicError,null,{reason:"unexpected evaluation"})}}let ve=class Ln{constructor(e){this._seconds=e}static createFromAngleAndUnits(e,n){return new Ln(Tr(e,n,y.seconds))}extractAngularUnits(e){return Tr(this._seconds,y.seconds,It(e))}static createFromDegreesMinutesSeconds(e,n,r){return new Ln(Sr(Sr(ut(e,Ce),ut(n,Ue)),r))}};function It(t){switch(Kr(t),t){case y.decimal_degrees:case y.truncated_degrees:case y.degrees_minutes_seconds:return y.decimal_degrees;case y.gradians:return y.gradians;case y.fractional_degree_minutes:return y.fractional_degree_minutes;case y.radians:return y.radians;case y.seconds:case y.fractional_minute_seconds:return y.seconds}}let Xa=class Ci{constructor(e,n,r,a){this.view=e,this.angle=n,this.merdian=r,this.direction=a,this._dms=null,this._formattedDms=null}static createFromStringAndBearing(e,n,r){return new Ci(e,n.getAngle(r),n.getMeridian(r),n.getDirection(r))}fetchAngle(){return this.angle}fetchMeridian(){return this.merdian}fetchDirection(){return this.direction}fetchView(){return this.view}fetchDms(){return this._dms===null&&this._calculateDms(),this._dms}fetchFormattedDms(){return this._formattedDms===null&&this._calculateDms(),this._formattedDms}_calculateDms(){let e=null,n=y.truncated_degrees,r=0;for(let a=0;a<this.view.length;a++){const u=this.view[a];switch(u){case"m":e=Rn(this.view,a,u),n=n===y.truncated_degrees?y.fractional_degree_minutes:n,a=e.newpos;continue;case"s":e=Rn(this.view,a,u),n=y.fractional_minute_seconds,r=r<e.rounding?e.rounding:r,a=e.newpos;continue;default:continue}}this._dms=ft.secondsToDMS(this.angle.extractAngularUnits(y.seconds)),this._formattedDms=ft.secondsToDMS(this.angle.extractAngularUnits(y.seconds)).format(n,r)}};function Qa(t,e,n,r,a){let u=null;switch(e){case y.decimal_degrees:case y.radians:case y.gradians:return u=mn($n(t.extractAngularUnits(e),r),pn(e)),dn(u.toFixed(r),"0",n+r+(r>0?1:0));case y.truncated_degrees:case y.fractional_degree_minutes:return u=mn(a.fetchFormattedDms().getField(e),pn(e)),dn(u.toFixed(r),"0",n+r+(r>0?1:0));case y.fractional_minute_seconds:return u=mn($n(a.fetchDms().getField(e),r),pn(e)),dn(u.toFixed(r),"0",n+r+(r>0?1:0));default:throw new h(null,c.LogicError,null,{reason:"unexpected evaluation"})}}function es(t,e,n){if(n===z.quadrant)throw new h(null,c.LogicError,null,{reason:"conversion error"});if(e===y.degrees_minutes_seconds){const r=ft.numberToDms(t);return Pe.createFromAngleAndDirection(ve.createFromDegreesMinutesSeconds(r.m_degrees,r.m_minutes,r.m_seconds),n)}return Pe.createFromAngleAndDirection(ve.createFromAngleAndUnits(t,It(e)),n)}function ts(t){switch(d(t)){case 1:return{first:Z.north,second:Z.east};case 2:return{first:Z.south,second:Z.east};case 3:return{first:Z.south,second:Z.west};case 4:return{first:Z.north,second:Z.west}}return null}function _r(t){switch(t.toUpperCase().trim()){case"N":case"NORTH":return Z.north;case"E":case"EAST":return Z.east;case"S":case"SOUTH":return Z.south;case"W":case"WEST":return Z.west}return null}function tt(t){const e=parseFloat(t);if(G(e)){if(isNaN(e))throw new h(null,c.LogicError,null,{reason:"invalid conversion"});return e}throw new h(null,c.LogicError,null,{reason:"invalid conversion"})}function Dn(t,e,n){const r=n===z.quadrant;let a=null,u=null,s=0,i=0,o=0;if(r){if(t.length<2)throw new h(null,c.LogicError,null,{reason:"conversion error"});o=1;const l=ts(A(t[t.length-1]));if(l?(a=l.first,u=l.second):(s=1,a=_r(A(t[0])),u=_r(A(t[t.length-1]))),a===null||u===null)throw new h(null,c.LogicError,null,{reason:"invalid conversion"})}switch(e){case y.decimal_degrees:case y.radians:case y.gradians:if(t.length===0)throw new h(null,c.LogicError,null,{reason:"invalid conversion"});return r?Pe.createFromAngleMeridianAndDirection(ve.createFromAngleAndUnits(tt(t[s]),It(e)),a,u):Pe.createFromAngleAndDirection(ve.createFromAngleAndUnits(tt(t[s]),It(e)),n);case y.degrees_minutes_seconds:if(i=t.length-o-s,i===3){const l=ve.createFromDegreesMinutesSeconds(tt(t[s]),tt(t[s+1]),tt(t[s+2]));return r?Pe.createFromAngleMeridianAndDirection(l,a,u):Pe.createFromAngleAndDirection(l,n)}if(i===1){const l=tt(t[s]),m=ft.numberToDms(l),f=ve.createFromDegreesMinutesSeconds(m.m_degrees,m.m_minutes,m.m_seconds);return r?Pe.createFromAngleMeridianAndDirection(f,a,u):Pe.createFromAngleAndDirection(f,n)}}throw new h(null,c.LogicError,null,{reason:"invalid conversion"})}function ns(t){const e=[" ","-","/","'",'"',"\\","^",xt,Fi,"	","\r",`
`,"*"];let n="";for(let r=0;r<t.length;r++){const a=t.charAt(r);e.includes(a)?n+="RRSPLITRRSPLITRR":n+=a}return n.split("RRSPLITRRSPLITRR").filter(r=>r!=="")}function rs(t,e,n){if(G(t))return es(d(t),e,n);if(b(t))return Dn(ns(t),e,n);if(k(t))return Dn(t,e,n);if(N(t))return Dn(t.toArray(),e,n);throw new h(null,c.LogicError,null,{reason:"conversion error"})}function is(t,e,n){const r=It(n);if(r&&n!==y.degrees_minutes_seconds)return t.getAngle(e).extractAngularUnits(r);throw new h(null,c.LogicError,null,{reason:"conversion error"})}function as(t,e,n){const r=t.getAngle(e);if(e===z.quadrant&&n===y.degrees_minutes_seconds){const a=ft.secondsToDMS(r.extractAngularUnits(y.seconds));return[st(t.getMeridian(e),"SHORT"),a.m_degrees,a.m_minutes,a.m_seconds,st(t.getDirection(e),"SHORT")]}if(n===y.degrees_minutes_seconds){const a=ft.secondsToDMS(r.extractAngularUnits(y.seconds));return[a.m_degrees,a.m_minutes,a.m_seconds]}return e===z.quadrant?[st(t.getMeridian(e),"SHORT"),r.extractAngularUnits(n),st(t.getDirection(e),"SHORT")]:[r.extractAngularUnits(n)]}function ss(t,e){let n="";switch(t){case y.decimal_degrees:n=e===z.quadrant?"DD.DD"+xt:"DDD.DD"+xt;break;case y.degrees_minutes_seconds:n=e===z.quadrant?"dd"+xt+` mm' ss"`:"ddd"+xt+` mm' ss.ss"`;break;case y.radians:n="R.RR";break;case y.gradians:n="GGG.GG"+Fi;break;default:throw new h(null,c.LogicError,null,{reason:"conversion error"})}return e===z.quadrant&&(n="p "+n+" b"),n}function Rn(t,e,n){const r={padding:0,rounding:0,newpos:e};let a=!1;for(;e<t.length;){const u=t[e];if(u===n)a?r.rounding++:r.padding++,e++;else{if(u!==".")break;a=!0,e++}}return r.newpos=e-1,r}function us(t,e,n){const r={escaped:"",newpos:e};for(e++;e<t.length;){const a=t[e];if(e++,a==="]")break;r.escaped+=a}return r.newpos=e-1,r}function os(t,e,n){let r="",a=null,u=null;const s=Xa.createFromStringAndBearing(e,t,n),i={D:y.decimal_degrees,d:y.truncated_degrees,m:y.fractional_degree_minutes,s:y.fractional_minute_seconds,R:y.radians,G:y.gradians};for(let o=0;o<e.length;o++){const l=e[o];switch(l){case"[":a=us(e,o),r+=a.escaped,o=a.newpos;continue;case"D":case"d":case"m":case"s":case"R":case"G":a=Rn(e,o,l),u=t.getAngle(n),r+=Qa(u,i[l],a.padding,a.rounding,s),o=a.newpos;continue;case"P":case"p":r+=st(s.fetchMeridian(),l==="p"?"SHORT":"LONG");continue;case"B":case"b":r+=st(s.fetchDirection(),l==="b"?"SHORT":"LONG");continue;default:r+=l}}return r}function ls(t,e,n){if(!(e instanceof _))throw new h(null,c.InvalidParameter,null);if(e.hasField("directionType")===!1)throw new h(null,c.LogicError,null,{reason:"missing directionType"});if(e.hasField("angleType")===!1)throw new h(null,c.LogicError,null,{reason:"missing angleType"});const r=kr(gt(e.field("directiontype"))),a=rs(t,Br(gt(e.field("angletype"))),r);if(!(n instanceof _))throw new h(null,c.InvalidParameter,null);if(n.hasField("directionType")===!1)throw new h(null,c.LogicError,null,{reason:"missing directionType"});if(n.hasField("outputType")===!1)throw new h(null,c.LogicError,null,{reason:"missing angleType"});const u=kr(gt(n.field("directiontype"))),s=n.hasField("angleType")?Br(gt(n.field("angletype"))):null,i=gt(n.field("outputType")).toUpperCase().trim();if(!u||!i)throw new h(null,c.LogicError,null,{reason:"conversion error"});if(!(s||i==="TEXT"&&n.hasField("format")))throw new h(null,c.LogicError,null,{reason:"invalid unit"});switch(i){case"VALUE":return u===z.quadrant||s===y.degrees_minutes_seconds?as(a,u,s):is(a,u,s);case"TEXT":{let o="";return n.hasField("format")&&(o=A(n.field("format"))),o!==null&&o!==""||(o=ss(s,u)),os(a,o,u)}default:throw new h(null,c.InvalidParameter,null)}}const nt=2654435761,rt=2246822519,Nt=3266489917,Mr=668265263,$r=374761393;function Nr(t){const e=[];for(let n=0,r=t.length;n<r;n++){let a=t.charCodeAt(n);a<128?e.push(a):a<2048?e.push(192|a>>6,128|63&a):a<55296||a>=57344?e.push(224|a>>12,128|a>>6&63,128|63&a):(n++,a=65536+((1023&a)<<10|1023&t.charCodeAt(n)),e.push(240|a>>18,128|a>>12&63,128|a>>6&63,128|63&a))}return new Uint8Array(e)}let cs=class{constructor(e){this._seed=e,this._totallen=0,this._bufs=[],this.init()}init(){return this._bufs=[],this._totallen=0,this}updateFloatArray(e){const n=[];for(const r of e)isNaN(r)?n.push("NaN"):r===1/0?n.push("Infinity"):r===-1/0?n.push("-Infinity"):r===0?n.push("0"):n.push(r.toString(16));this.update(Nr(n.join("")))}updateIntArray(e){const n=Int32Array.from(e);this.update(new Uint8Array(n.buffer))}updateUint8Array(e){this.update(Uint8Array.from(e))}updateWithString(e){return this.update(Nr(e))}update(e){return this._bufs.push(e),this._totallen+=e.length,this}digest(){const e=new Uint8Array(this._totallen);let n=0;for(const r of this._bufs)e.set(r,n),n+=r.length;return this.init(),this._xxHash32(e,this._seed)}_xxHash32(e,n=0){const r=e;let a=n+$r&4294967295,u=0;if(r.length>=16){const i=[n+nt+rt&4294967295,n+rt&4294967295,n+0&4294967295,n-nt&4294967295],o=e,l=o.length-16;let m=0;for(u=0;(4294967280&u)<=l;u+=4){const f=u,p=o[f+0]+(o[f+1]<<8),g=o[f+2]+(o[f+3]<<8),w=p*rt+(g*rt<<16);let B=i[m]+w&4294967295;B=B<<13|B>>>19;const I=65535&B,J=B>>>16;i[m]=I*nt+(J*nt<<16)&4294967295,m=m+1&3}a=(i[0]<<1|i[0]>>>31)+(i[1]<<7|i[1]>>>25)+(i[2]<<12|i[2]>>>20)+(i[3]<<18|i[3]>>>14)&4294967295}a=a+e.length&4294967295;const s=e.length-4;for(;u<=s;u+=4){const i=u,o=r[i+0]+(r[i+1]<<8),l=r[i+2]+(r[i+3]<<8);a=a+(o*Nt+(l*Nt<<16))&4294967295,a=a<<17|a>>>15,a=(65535&a)*Mr+((a>>>16)*Mr<<16)&4294967295}for(;u<r.length;++u)a+=r[u]*$r,a=a<<11|a>>>21,a=(65535&a)*nt+((a>>>16)*nt<<16)&4294967295;return a^=a>>>15,a=((65535&a)*rt&4294967295)+((a>>>16)*rt<<16),a^=a>>>13,a=((65535&a)*Nt&4294967295)+((a>>>16)*Nt<<16),a^=a>>>16,a<0?a+4294967296:a}};function hs(t){return t.loadStatus==="loaded"&&t.user&&t.user.sourceJSON?t.user.sourceJSON:null}function fs(t,e){return!!t&&ca(t,(e==null?void 0:e.restUrl)||"")}function Lr(t,e){if(!t||!e)return t===e;if(t.x===e.x&&t.y===e.y){if(t.hasZ){if(t.z!==e.z)return!1}else if(e.hasZ)return!1;if(t.hasM){if(t.m!==e.m)return!1}else if(e.hasM)return!1;return!0}return!1}function ge(t,e,n){if(t!==null)if(k(t)){if(e.updateUint8Array([61]),n.map.has(t)){const r=n.map.get(t);e.updateIntArray([61237541^r])}else{n.map.set(t,n.currentLength++);for(const r of t)ge(r,e,n);n.map.delete(t),n.currentLength--}e.updateUint8Array([199])}else if(N(t)){if(e.updateUint8Array([61]),n.map.has(t)){const r=n.map.get(t);e.updateIntArray([61237541^r])}else{n.map.set(t,n.currentLength++);for(const r of t.toArray())ge(r,e,n);n.map.delete(t),n.currentLength--}e.updateUint8Array([199])}else{if(j(t))return e.updateIntArray([t.toNumber()]),void e.updateUint8Array([241]);if(b(t))return e.updateIntArray([t.length]),e.updateWithString(t),void e.updateUint8Array([41]);if(U(t))e.updateUint8Array([t===!0?1:0,113]);else{if(G(t))return e.updateFloatArray([t]),void e.updateUint8Array([173]);if(t instanceof qa)throw new h(n.context,c.UnsupportedHashType,n.node);if(t instanceof Mn)throw new h(n.context,c.UnsupportedHashType,n.node);if(!(t instanceof _)){if(Q(t))throw new h(n.context,c.UnsupportedHashType,n.node);if(t instanceof H)return e.updateIntArray([3833836621]),e.updateIntArray([0]),e.updateFloatArray([t.x]),e.updateIntArray([1]),e.updateFloatArray([t.y]),t.hasZ&&(e.updateIntArray([2]),e.updateFloatArray([t.z])),t.hasM&&(e.updateIntArray([3]),e.updateFloatArray([t.m])),e.updateIntArray([3765347959]),void ge(t.spatialReference.wkid,e,n);if(t instanceof ae){e.updateIntArray([1266616829]);for(let r=0;r<t.rings.length;r++){const a=t.rings[r],u=[];let s=null,i=null;for(let o=0;o<a.length;o++){const l=t.getPoint(r,o);if(o===0)s=l;else if(Lr(i,l))continue;i=l,o===a.length-1&&Lr(s,l)||u.push(l)}e.updateIntArray([1397116793,u.length]);for(let o=0;o<u.length;o++){const l=u[o];e.updateIntArray([3962308117,o]),ge(l,e,n),e.updateIntArray([2716288009])}e.updateIntArray([2278822459])}return e.updateIntArray([3878477243]),void ge(t.spatialReference.wkid,e,n)}if(t instanceof ie){e.updateIntArray([4106883559]);for(let r=0;r<t.paths.length;r++){const a=t.paths[r];e.updateIntArray([1397116793,a.length]);for(let u=0;u<a.length;u++)e.updateIntArray([3962308117,u]),ge(t.getPoint(r,u),e,n),e.updateIntArray([2716288009]);e.updateIntArray([2278822459])}return e.updateIntArray([2568784753]),void ge(t.spatialReference.wkid,e,n)}if(t instanceof we){e.updateIntArray([588535921,t.points.length]);for(let r=0;r<t.points.length;r++){const a=t.getPoint(r);e.updateIntArray([r]),ge(a,e,n)}return e.updateIntArray([1700171621]),void ge(t.spatialReference.wkid,e,n)}if(t instanceof he)return e.updateIntArray([3483648373]),e.updateIntArray([0]),e.updateFloatArray([t.xmax]),e.updateIntArray([1]),e.updateFloatArray([t.xmin]),e.updateIntArray([2]),e.updateFloatArray([t.ymax]),e.updateIntArray([3]),e.updateFloatArray([t.ymin]),t.hasZ&&(e.updateIntArray([4]),e.updateFloatArray([t.zmax]),e.updateIntArray([5]),e.updateFloatArray([t.zmin])),t.hasM&&(e.updateIntArray([6]),e.updateFloatArray([t.mmax]),e.updateIntArray([7]),e.updateFloatArray([t.mmin])),e.updateIntArray([3622027469]),void ge(t.spatialReference.wkid,e,n);if(t instanceof kt)return e.updateIntArray([14]),t.wkid!==void 0&&t.wkid!==null&&e.updateIntArray([t.wkid]),void(t.wkt&&e.updateWithString(t.wkt));throw K(t)?new h(n.context,c.UnsupportedHashType,n.node):jt(t)?new h(n.context,c.UnsupportedHashType,n.node):Wt(t)?new h(n.context,c.UnsupportedHashType,n.node):t===C?new h(n.context,c.UnsupportedHashType,n.node):new h(n.context,c.UnsupportedHashType,n.node)}if(e.updateUint8Array([223]),n.map.has(t)){const r=n.map.get(t);e.updateIntArray([61237541^r])}else{n.map.set(t,n.currentLength++);for(const r of t.keys())e.updateIntArray([r.length]),e.updateWithString(r),e.updateUint8Array([251]),ge(t.field(r),e,n),e.updateUint8Array([239]);n.map.delete(t),n.currentLength--}e.updateUint8Array([73])}}else e.updateUint8Array([0,139])}function Ai(t,e){t.portal=function(n,r){return e(n,r,(a,u,s)=>(D(s,1,1,n,r),new Mn(A(s[0]))))},t.typeof=function(n,r){return e(n,r,(a,u,s)=>{D(s,1,1,n,r);const i=Jn(s[0]);if(i==="Unrecognised Type")throw new h(n,c.UnrecognisedType,r);return i})},t.trim=function(n,r){return e(n,r,(a,u,s)=>(D(s,1,1,n,r),A(s[0]).trim()))},t.tohex=function(n,r){return e(n,r,(a,u,s)=>{D(s,1,1,n,r);const i=d(s[0]);return isNaN(i)?i:i.toString(16)})},t.upper=function(n,r){return e(n,r,(a,u,s)=>(D(s,1,1,n,r),A(s[0]).toUpperCase()))},t.proper=function(n,r){return e(n,r,(a,u,s)=>{D(s,1,2,n,r);let i=1;s.length===2&&A(s[1]).toLowerCase()==="firstword"&&(i=2);const o=/\s/,l=A(s[0]);let m="",f=!0;for(let p=0;p<l.length;p++){let g=l[p];o.test(g)?i===1&&(f=!0):g.toUpperCase()!==g.toLowerCase()&&(f?(g=g.toUpperCase(),f=!1):g=g.toLowerCase()),m+=g}return m})},t.lower=function(n,r){return e(n,r,(a,u,s)=>(D(s,1,1,n,r),A(s[0]).toLowerCase()))},t.guid=function(n,r){return e(n,r,(a,u,s)=>{if(D(s,0,1,n,r),s.length>0)switch(A(s[0]).toLowerCase()){case"digits":return it().replace("-","").replace("-","").replace("-","").replace("-","");case"digits-hyphen":return it();case"digits-hyphen-braces":return"{"+it()+"}";case"digits-hyphen-parentheses":return"("+it()+")"}return"{"+it()+"}"})},t.standardizeguid=function(n,r){return e(n,r,(a,u,s)=>{D(s,2,2,n,r);let i=A(s[0]);if(i===""||i===null)return"";const o=/^(\{|\()?(?<partA>[0-9a-z]{8})(\-?)(?<partB>[0-9a-z]{4})(\-?)(?<partC>[0-9a-z]{4})(\-?)(?<partD>[0-9a-z]{4})(\-?)(?<partE>[0-9a-z]{12})(\}|\))?$/gim.exec(i);if(!o)return"";const l=o.groups;switch(i=l.partA+"-"+l.partB+"-"+l.partC+"-"+l.partD+"-"+l.partE,A(s[1]).toLowerCase()){case"digits":return i.replace("-","").replace("-","").replace("-","").replace("-","");case"digits-hyphen":return i;case"digits-hyphen-braces":return"{"+i+"}";case"digits-hyphen-parentheses":return"("+i+")"}return"{"+i+"}"})},t.console=function(n,r){return e(n,r,(a,u,s)=>(s.length===0||(s.length===1?n.console(A(s[0])):n.console(A(s))),C))},t.mid=function(n,r){return e(n,r,(a,u,s)=>{D(s,2,3,n,r);let i=d(s[1]);if(isNaN(i))return"";if(i<0&&(i=0),s.length===2)return A(s[0]).substr(i);let o=d(s[2]);return isNaN(o)?"":(o<0&&(o=0),A(s[0]).substr(i,o))})},t.find=function(n,r){return e(n,r,(a,u,s)=>{D(s,2,3,n,r);let i=0;if(s.length>2){if(i=d(M(s[2],0)),isNaN(i))return-1;i<0&&(i=0)}return A(s[1]).indexOf(A(s[0]),i)})},t.left=function(n,r){return e(n,r,(a,u,s)=>{D(s,2,2,n,r);let i=d(s[1]);return isNaN(i)?"":(i<0&&(i=0),A(s[0]).substr(0,i))})},t.right=function(n,r){return e(n,r,(a,u,s)=>{D(s,2,2,n,r);let i=d(s[1]);return isNaN(i)?"":(i<0&&(i=0),A(s[0]).substr(-1*i,i))})},t.split=function(n,r){return e(n,r,(a,u,s)=>{let i;D(s,2,4,n,r);let o=d(M(s[2],-1));const l=ct(M(s[3],!1));if(o===-1||o===null||l===!0?i=A(s[0]).split(A(s[1])):(isNaN(o)&&(o=-1),o<-1&&(o=-1),i=A(s[0]).split(A(s[1]),o)),l===!1)return i;const m=[];for(let f=0;f<i.length&&!(o!==-1&&m.length>=o);f++)i[f]!==""&&i[f]!==void 0&&m.push(i[f]);return m})},t.text=function(n,r){return e(n,r,(a,u,s)=>(D(s,1,2,n,r),pe(s[0],s[1])))},t.concatenate=function(n,r){return e(n,r,(a,u,s)=>{const i=[];if(s.length<1)return"";if(k(s[0])){const o=M(s[2],"");for(let l=0;l<s[0].length;l++)i[l]=pe(s[0][l],o);return s.length>1?i.join(s[1]):i.join("")}if(N(s[0])){const o=M(s[2],"");for(let l=0;l<s[0].length();l++)i[l]=pe(s[0].get(l),o);return s.length>1?i.join(s[1]):i.join("")}for(let o=0;o<s.length;o++)i[o]=pe(s[o]);return i.join("")})},t.reverse=function(n,r){return e(n,r,(a,u,s)=>{if(D(s,1,1,n,r),k(s[0])){const i=s[0].slice(0);return i.reverse(),i}if(N(s[0])){const i=s[0].toArray().slice(0);return i.reverse(),i}throw new h(n,c.InvalidParameter,r)})},t.replace=function(n,r){return e(n,r,(a,u,s)=>{D(s,3,4,n,r);const i=A(s[0]),o=A(s[1]),l=A(s[2]);return s.length!==4||ct(s[3])?Ge(i,o,l):i.replace(o,l)})},t.schema=function(n,r){return e(n,r,(a,u,s)=>{if(Q(s[0])){const i=li(s[0]);return i?_.convertObjectToArcadeDictionary(i,P(n)):null}throw new h(n,c.InvalidParameter,r)})},t.subtypes=function(n,r){return e(n,r,(a,u,s)=>{if(D(s,1,1,n,r),Q(s[0])){const i=Rt(s[0]);return i?_.convertObjectToArcadeDictionary(i,P(n)):null}throw new h(n,c.InvalidParameter,r)})},t.subtypecode=function(n,r){return e(n,r,(a,u,s)=>{if(D(s,1,1,n,r),Q(s[0])){const i=Rt(s[0]);if(!i)return null;if(i.subtypeField&&s[0].hasField(i.subtypeField)){const o=s[0].field(i.subtypeField);for(const l of i.subtypes)if(l.code===o)return l.code;return null}return null}throw new h(n,c.InvalidParameter,r)})},t.subtypename=function(n,r){return e(n,r,(a,u,s)=>{if(D(s,1,1,n,r),Q(s[0])){const i=Rt(s[0]);if(!i)return"";if(i.subtypeField&&s[0].hasField(i.subtypeField)){const o=s[0].field(i.subtypeField);for(const l of i.subtypes)if(l.code===o)return l.name;return""}return""}throw new h(n,c.InvalidParameter,r)})},t.gdbversion=function(n,r){return e(n,r,(a,u,s)=>{if(D(s,1,1,n,r),Q(s[0]))return s[0].gdbVersion();throw new h(n,c.InvalidParameter,r)})},t.domain=function(n,r){return e(n,r,(a,u,s)=>{if(D(s,2,3,n,r),Q(s[0])){const i=si(s[0],A(s[1]),s[2]===void 0?void 0:d(s[2]));return i&&i.domain?i.domain.type==="coded-value"||i.domain.type==="codedValue"?_.convertObjectToArcadeDictionary({type:"codedValue",name:i.domain.name,dataType:Dr[i.field.type],codedValues:i.domain.codedValues.map(o=>({name:o.name,code:o.code}))},P(n)):_.convertObjectToArcadeDictionary({type:"range",name:i.domain.name,dataType:Dr[i.field.type],min:i.domain.min,max:i.domain.max},P(n)):null}throw new h(n,c.InvalidParameter,r)})},t.domainname=function(n,r){return e(n,r,(a,u,s)=>{if(D(s,2,4,n,r),Q(s[0]))return ui(s[0],A(s[1]),s[2],s[3]===void 0?void 0:d(s[3]));throw new h(n,c.InvalidParameter,r)})},t.domaincode=function(n,r){return e(n,r,(a,u,s)=>{if(D(s,2,4,n,r),Q(s[0]))return oi(s[0],A(s[1]),s[2],s[3]===void 0?void 0:d(s[3]));throw new h(n,c.InvalidParameter,r)})},t.urlencode=function(n,r){return e(n,r,(a,u,s)=>{if(D(s,1,1,n,r),s[0]===null)return"";if(s[0]instanceof _){let i="";for(const o of s[0].keys()){const l=s[0].field(o);i!==""&&(i+="&"),i+=l===null?encodeURIComponent(o)+"=":encodeURIComponent(o)+"="+encodeURIComponent(l)}return i}return encodeURIComponent(A(s[0]))})},t.hash=function(n,r){return e(n,r,(a,u,s)=>{D(s,1,1,n,r);const i=new cs(0);return ge(s[0],i,{context:n,node:r,map:new Map,currentLength:0}),i.digest()})},t.convertdirection=function(n,r){return e(n,r,(a,u,s)=>(D(s,3,3,n,r),ls(s[0],s[1],s[2])))},t.fromjson=function(n,r){return e(n,r,(a,u,s)=>{if(D(s,1,1,n,r),b(s[0])===!1)throw new h(n,c.InvalidParameter,r);return _.convertJsonToArcade(JSON.parse(A(s[0])),P(n))})},t.expects=function(n,r){return e(n,r,(a,u,s)=>{if(s.length<1)throw new h(n,c.WrongNumberOfParameters,r);return C})},t.tocharcode=function(n,r){return e(n,r,(a,u,s)=>{D(s,1,2,n,r);const i=d(M(s[1],0)),o=A(s[0]);if(o.length===0&&s.length===1)return null;if(o.length<=i||i<0)throw new h(n,c.OutOfBounds,r);return o.charCodeAt(i)})},t.tocodepoint=function(n,r){return e(n,r,(a,u,s)=>{D(s,1,2,n,r);const i=d(M(s[1],0)),o=A(s[0]);if(o.length===0&&s.length===1)return null;if(o.length<=i||i<0)throw new h(n,c.OutOfBounds,r);return o.codePointAt(i)})},t.fromcharcode=function(n,r){return e(n,r,(a,u,s)=>{if(s.length<1)throw new h(n,c.WrongNumberOfParameters,r);const i=s.map(o=>Math.trunc(d(o))).filter(o=>o>=0&&o<=65535);return i.length===0?null:String.fromCharCode.apply(null,i)})},t.fromcodepoint=function(n,r){return e(n,r,(a,u,s)=>{if(s.length<1)throw new h(n,c.WrongNumberOfParameters,r);let i;try{i=s.map(o=>Math.trunc(d(o))).filter(o=>o<=1114111&&o>>>0===o)}catch{return null}return i.length===0?null:String.fromCodePoint.apply(null,i)})},t.getuser=function(n,r){return e(n,r,(a,u,s)=>{D(s,0,2,n,r);let i=M(s[1],"");if(i=i===!0||i===!1?"":A(i),i!==null&&i!=="")return null;if(s.length===0||s[0]instanceof Mn){let o=null;if(n.services&&n.services.portal&&(o=n.services.portal),s.length>0&&!fs(s[0].field("url"),o)||!o)return null;if(i===""){const l=hs(o);if(l){const m=JSON.parse(JSON.stringify(l));for(const f of["lastLogin","created","modified"])m[f]!==void 0&&m[f]!==null&&(m[f]=new Date(m[f]));return _.convertObjectToArcadeDictionary(m,P(n))}}return null}throw new h(n,c.InvalidParameter,r)})}}let tn=class{constructor(e,n){this._moduleSingletons=e,this._syntaxModules=n}loadLibrary(e){if(this._syntaxModules==null)return null;const n=this._syntaxModules[e.toLowerCase()];return n?{syntax:n.script,uri:n.uri}:null}},ds=class extends je{constructor(e,n){super(),this.paramCount=n,this.fn=e}createFunction(e){return(...n)=>{if(n.length!==this.paramCount)throw new h(e,c.WrongNumberOfParameters,null);return this.fn(...n)}}call(e,n){return this.fn(...n.arguments)}marshalledCall(e,n,r,a){return a(e,n,(u,s,i)=>{i=i.map(l=>!K(l)||l instanceof Le?l:ot(l,e,a));const o=this.call(r,{arguments:i});return Oe(o)?o.then(l=>ot(l,r,a)):o})}};function ye(t,e,n){try{return n(t,null,e.arguments)}catch(r){throw r}}function S(t,e){try{switch(e.type){case"EmptyStatement":return"lc.voidOperation";case"VariableDeclarator":return vs(t,e);case"VariableDeclaration":return Ss(t,e);case"BlockStatement":case"Program":return Pn(t,e);case"FunctionDeclaration":return bs(t,e);case"ImportDeclaration":return As(t,e);case"ExportNamedDeclaration":return Es(t,e);case"ReturnStatement":return Cs(t,e);case"IfStatement":return Ei(t,e);case"ExpressionStatement":return Fs(t,e);case"AssignmentExpression":return xs(t,e);case"UpdateExpression":return ys(t,e);case"BreakStatement":return"break";case"ContinueStatement":return"continue";case"TemplateLiteral":return Ts(t,e);case"TemplateElement":return JSON.stringify(e.value?e.value.cooked:"");case"ForStatement":return gs(t,e);case"ForInStatement":return Ds(t,e);case"WhileStatement":return ws(t,e);case"Identifier":return $s(t,e);case"MemberExpression":return Is(t,e);case"Literal":return e.value===null||e.value===void 0?"null":JSON.stringify(e.value);case"CallExpression":return Ns(t,e);case"UnaryExpression":return ks(t,e);case"BinaryExpression":return _s(t,e);case"LogicalExpression":return Ms(t,e);case"ArrayExpression":return Bs(t,e);case"ObjectExpression":return ms(t,e);case"Property":return ps(t,e);case"Array":throw new ue(t,c.NeverReach,e);default:throw new ue(t,c.Unrecognised,e)}}catch(n){throw n}}function ms(t,e){let n="lang.dictionary([";for(let r=0;r<e.properties.length;r++){const a=e.properties[r];De(a.key.name),r>0&&(n+=","),n+="lang.strCheck("+(a.key.type==="Identifier"?"'"+a.key.name+"'":S(t,a.key))+",'ObjectExpression'),lang.aCheck("+S(t,a.value)+", 'ObjectExpression')"}return n+="])",n}function ps(t,e){throw new ue(t,c.NeverReach,e)}function Ds(t,e){const n=Ie(t),r=Ie(t),a=Ie(t);let u="var "+n+" = "+S(t,e.right)+`;
`;e.left.type==="VariableDeclaration"&&(u+=S(t,e.left));let s=e.left.type==="VariableDeclaration"?e.left.declarations[0].id.name:e.left.name;s=s.toLowerCase(),De(s);let i="";t.localScope!==null&&(t.localScope[s]!==void 0?i="lscope['"+s+"']":t.localScope._SymbolsMap[s]!==void 0&&(i="lscope['"+t.localScope._SymbolsMap[s]+"']"));let o="";if(i===""){if(t.globalScope[s]!==void 0)i="gscope['"+s+"']";else if(t.globalScope._SymbolsMap[s]!==void 0)i="gscope['"+t.globalScope._SymbolsMap[s]+"']";else if(t.localScope!==null)if(t.undeclaredGlobalsInFunctions.has(s))i="gscope['"+t.undeclaredGlobalsInFunctions.get(s).manglename+"']",o=t.undeclaredGlobalsInFunctions.get(s).manglename;else{const l={manglename:be(t),node:e.left};t.undeclaredGlobalsInFunctions.set(s,l),i="gscope['"+l.manglename+"']",o=l.manglename}}return o&&(u+="lang.chkAssig('"+o+`',runtimeCtx); 
`),u+="if ("+n+`===null) {  lastStatement = lc.voidOperation; }
 `,u+="else if (lc.isArray("+n+") || lc.isString("+n+")) {",u+="var "+r+"="+n+`.length; 
`,u+="for(var "+a+"=0; "+a+"<"+r+"; "+a+`++) {
`,u+=i+"="+a+`;
`,u+=S(t,e.body),u+=`
}
`,u+=` lastStatement = lc.voidOperation; 
`,u+=` 
}
`,u+="else if (lc.isImmutableArray("+n+")) {",u+="var "+r+"="+n+`.length(); 
`,u+="for(var "+a+"=0; "+a+"<"+r+"; "+a+`++) {
`,u+=i+"="+a+`;
`,u+=S(t,e.body),u+=`
}
`,u+=` lastStatement = lc.voidOperation; 
`,u+=` 
}
`,u+="else if (( "+n+" instanceof lang.Dictionary) || ( "+n+" instanceof lang.Feature)) {",u+="var "+r+"="+n+`.keys(); 
`,u+="for(var "+a+"=0; "+a+"<"+r+".length; "+a+`++) {
`,u+=i+"="+r+"["+a+`];
`,u+=S(t,e.body),u+=`
}
`,u+=` lastStatement = lc.voidOperation; 
`,u+=` 
}
`,t.isAsync&&(u+="else if (lc.isFeatureSet("+n+")) {",u+="var "+r+"="+n+`.iterator(runtimeCtx.abortSignal); 
`,u+="for(var "+a+"=lang. graphicToFeature( yield "+r+".next(),"+n+", runtimeCtx); "+a+"!=null; "+a+"=lang. graphicToFeature( yield "+r+".next(),"+n+`, runtimeCtx)) {
`,u+=i+"="+a+`;
`,u+=S(t,e.body),u+=`
}
`,u+=` lastStatement = lc.voidOperation; 
`,u+=` 
}
`),u+=`else { lastStatement = lc.voidOperation; } 
`,u}function gs(t,e){let n=`lastStatement = lc.voidOperation; 
`;e.init!==null&&(n+=S(t,e.init)+"; ");const r=Ie(t),a=Ie(t);return n+="var "+r+" = true; ",n+=`
 do { `,e.update!==null&&(n+=" if ("+r+`===false) {
 `+S(t,e.update)+`  
}
 `+r+`=false; 
`),e.test!==null&&(n+="var "+a+" = "+S(t,e.test)+"; ",n+="if ("+a+"===false) { break; } else if ("+a+"!==true) { lang.error('"+c.BooleanConditionRequired+`');   }
`),n+=S(t,e.body),e.update!==null&&(n+=`
 `+S(t,e.update)),n+=`
`+r+` = true; 
} while(true);  lastStatement = lc.voidOperation; `,n}function ys(t,e){let n=null,r="";if(e.argument.type==="MemberExpression")return n=S(t,e.argument.object),e.argument.computed===!0?r=S(t,e.argument.property):(r="'"+e.argument.property.name+"'",De(e.argument.property.name)),"lang.memberupdate("+n+","+r+",'"+e.operator+"',"+e.prefix+")";if(n=e.argument.name.toLowerCase(),De(n),t.localScope!==null){if(t.localScope[n]!==void 0)return"lang.update(lscope, '"+n+"','"+e.operator+"',"+e.prefix+")";if(t.localScope._SymbolsMap[n]!==void 0)return"lang.update(lscope, '"+t.localScope._SymbolsMap[n]+"','"+e.operator+"',"+e.prefix+")"}if(t.globalScope[n]!==void 0)return"lang.update(gscope, '"+n+"','"+e.operator+"',"+e.prefix+")";if(t.globalScope._SymbolsMap[n]!==void 0)return"lang.update(gscope, '"+t.globalScope._SymbolsMap[n]+"','"+e.operator+"',"+e.prefix+")";if(t.localScope!==null){if(t.undeclaredGlobalsInFunctions.has(n))return"lang.update(gscope,lang.chkAssig( '"+t.undeclaredGlobalsInFunctions.get(n).manglename+"',runtimeCtx),'"+e.operator+"',"+e.prefix+")";const a={manglename:be(t),node:e.argument};return t.undeclaredGlobalsInFunctions.set(n,a),"lang.update(gscope, lang.chkAssig('"+a.manglename+"',runtimeCtx),'"+e.operator+"',"+e.prefix+")"}throw new h(t,c.InvalidIdentifier,e)}function ws(t,e){let n=`lastStatement = lc.voidOperation; 
`;const r=Ie(t);return n+=`
  var ${r} = true;
    do {
      ${r} = ${S(t,e.test)};
      if (${r}==false) {
        break;
      }
      if (${r}!==true) {
        lang.error('${c.BooleanConditionRequired}');
      }
      ${S(t,e.body)}
    }
    while (${r} !== false);
    lastStatement = lc.voidOperation;
  `,n}function xs(t,e){const n=S(t,e.right);let r=null,a="";if(e.left.type==="MemberExpression")return r=S(t,e.left.object),e.left.computed===!0?a=S(t,e.left.property):(a="'"+e.left.property.name+"'",De(e.left.property.name)),"lang.assignmember("+r+","+a+",'"+e.operator+"',"+n+")";if(r=e.left.name.toLowerCase(),De(r),t.localScope!==null){if(t.localScope[r]!==void 0)return"lscope['"+r+"']=lang.assign("+n+",'"+e.operator+"', lscope['"+r+"'])";if(t.localScope._SymbolsMap[r]!==void 0)return"lscope['"+t.localScope._SymbolsMap[r]+"']=lang.assign("+n+",'"+e.operator+"', lscope['"+t.localScope._SymbolsMap[r]+"'])"}if(t.globalScope[r]!==void 0)return"gscope['"+r+"']=lang.assign("+n+",'"+e.operator+"', gscope['"+r+"'])";if(t.globalScope._SymbolsMap[r]!==void 0)return"gscope['"+t.globalScope._SymbolsMap[r]+"']=lang.assign("+n+",'"+e.operator+"', gscope['"+t.globalScope._SymbolsMap[r]+"'])";if(t.localScope!==null){if(t.undeclaredGlobalsInFunctions.has(r))return"gscope[lang.chkAssig('"+t.undeclaredGlobalsInFunctions.get(r).manglename+"',runtimeCtx)]=lang.assign("+n+",'"+e.operator+"', gscope['"+t.undeclaredGlobalsInFunctions.get(r).manglename+"'])";const u={manglename:be(t),node:e.argument};return t.undeclaredGlobalsInFunctions.set(r,u),"gscope[lang.chkAssig('"+u.manglename+"',runtimeCtx)]=lang.assign("+n+",'"+e.operator+"', gscope['"+u.manglename+"'])"}throw new h(t,c.InvalidIdentifier,e)}function Fs(t,e){return e.expression.type==="AssignmentExpression"?"lastStatement = lc.voidOperation; "+S(t,e.expression)+`; 
 `:(e.expression.type,"lastStatement = "+S(t,e.expression)+"; ")}function Rr(t,e){return e.type==="BlockStatement"?S(t,e):e.type==="ReturnStatement"||e.type==="BreakStatement"||e.type==="ContinueStatement"?S(t,e)+"; ":e.type==="UpdateExpression"?"lastStatement = "+S(t,e)+"; ":e.type==="ExpressionStatement"?S(t,e):e.type==="ObjectExpression"?"lastStatement = "+S(t,e)+"; ":S(t,e)+"; "}function Ei(t,e){if(e.test.type==="AssignmentExpression"||e.test.type==="UpdateExpression")throw new ue(t,c.BooleanConditionRequired,e);return`if (lang.mustBoolean(${S(t,e.test)}, runtimeCtx) === true) {
    ${Rr(t,e.consequent)}
  } `+(e.alternate!==null?e.alternate.type==="IfStatement"?" else "+Ei(t,e.alternate):` else {
      ${Rr(t,e.alternate)}
    }
`:` else {
      lastStatement = lc.voidOperation;
    }
`)}function Pn(t,e){let n="";for(let r=0;r<e.body.length;r++)e.body[r].type!=="EmptyStatement"&&(e.body[r].type==="ReturnStatement"||e.body[r].type==="BreakStatement"||e.body[r].type==="ContinueStatement"?n+=S(t,e.body[r])+`; 
`:e.body[r].type==="UpdateExpression"||e.body[r].type==="ObjectExpression"?n+="lastStatement = "+S(t,e.body[r])+`; 
`:n+=S(t,e.body[r])+` 
`);return n}function Cs(t,e){return e.argument===null?"return lc.voidOperation":"return "+S(t,e.argument)}function As(t,e){var i;const n=e.specifiers[0].local.name.toLowerCase();De(n);const r=(i=t.libraryResolver)==null?void 0:i.loadLibrary(n),a=be(t);t.moduleFactory[r.uri]===void 0&&(t.moduleFactory[r.uri]=Gs(r.syntax,{interceptor:t.interceptor,services:t.services,moduleFactory:t.moduleFactory,lrucache:t.lrucache,timeReference:t.timeReference??null,libraryResolver:t.libraryResolver,customfunctions:t.customfunctions,vars:{}},t.isAsync)),t.moduleFactoryMap[a]=r.uri;let u="";if(u=t.isAsync?"(yield lang.loadModule('"+a+"', runtimeCtx) ); ":"lang.loadModule('"+a+"', runtimeCtx); ",t.globalScope[n]!==void 0)return"gscope['"+n+"']="+u;if(t.globalScope._SymbolsMap[n]!==void 0)return"gscope['"+t.globalScope._SymbolsMap[n]+"']="+u;let s="";return t.undeclaredGlobalsInFunctions.has(n)?(s=t.undeclaredGlobalsInFunctions.get(n).manglename,t.undeclaredGlobalsInFunctions.delete(n)):s=be(t),t.globalScope._SymbolsMap[n]=s,t.mangleMap[n]=s,"gscope[lang.setAssig('"+s+"', runtimeCtx)]="+u}function Es(t,e){const n=S(t,e.declaration);if(e.declaration.type==="FunctionDeclaration")t.exports[e.declaration.id.name.toLowerCase()]="function";else if(e.declaration.type==="VariableDeclaration")for(const r of e.declaration.declarations)t.exports[r.id.name.toLowerCase()]="variable";return n}function De(t){if(t==="iif")throw new sn;if(t==="decode")throw new sn;if(t==="when")throw new sn}function bs(t,e){const n=e.id.name.toLowerCase();De(n);let r="",a=!1;t.globalScope[n]!==void 0?r=n:t.globalScope._SymbolsMap[n]!==void 0?r=t.globalScope._SymbolsMap[n]:t.undeclaredGlobalsInFunctions.has(n)?(r=t.undeclaredGlobalsInFunctions.get(n).manglename,t.globalScope._SymbolsMap[n]=r,t.mangleMap[n]=r,t.undeclaredGlobalsInFunctions.delete(n),a=!0):(r=be(t),t.globalScope._SymbolsMap[n]=r,t.mangleMap[n]=r);const u={isAsync:t.isAsync,console:t.console,exports:t.exports,undeclaredGlobalsInFunctions:t.undeclaredGlobalsInFunctions,customfunctions:t.customfunctions,moduleFactory:t.moduleFactory,moduleFactoryMap:t.moduleFactoryMap,libraryResolver:t.libraryResolver,lrucache:t.lrucache,interceptor:t.interceptor,services:t.services,symbols:t.symbols,mangleMap:t.mangleMap,localScope:{_SymbolsMap:{}},depthCounter:t.depthCounter,globalScope:t.globalScope};let s=`new lang.UserDefinedCompiledFunction( lang.functionDepthchecker(function() { var lastStatement = lc.voidOperation; 
   var lscope = runtimeCtx.localStack[runtimeCtx.localStack.length-1];
`;for(let i=0;i<e.params.length;i++){const o=e.params[i].name.toLowerCase();De(o);const l=be(t);u.localScope._SymbolsMap[o]=l,u.mangleMap[o]=l,s+="lscope['"+l+"']=arguments["+i.toString()+`];
`}return t.isAsync===!0?(s+=`return lang.__awaiter(this, void 0, void 0, function* () {
`,s+=Pn(u,e.body)+`
 return lastStatement; `,s+="});  }",s+=", runtimeCtx),"+e.params.length+")",s+=`
 lastStatement = lc.voidOperation; 
`):(s+=Pn(u,e.body)+`
 return lastStatement; }, runtimeCtx),`+e.params.length+")",s+=`
 lastStatement = lc.voidOperation; 
`),a?"gscope[lang.setAssig('"+r+"', runtimeCtx)]="+s:"gscope['"+r+"']="+s}function Ss(t,e){const n=[];for(let r=0;r<e.declarations.length;r++)n.push(S(t,e.declarations[r]));return n.join(`
`)+` 
 lastStatement=  lc.voidOperation; 
`}function vs(t,e){let n=e.init===null?null:S(t,e.init);n===C&&(n=null);const r=e.id.name.toLowerCase();if(De(r),t.localScope!==null){if(t.localScope[r]!==void 0)return"lscope['"+r+"']="+n+"; ";if(t.localScope._SymbolsMap[r]!==void 0)return"lscope['"+t.localScope._SymbolsMap[r]+"']="+n+"; ";{const u=be(t);return t.localScope._SymbolsMap[r]=u,t.mangleMap[r]=u,"lscope['"+u+"']="+n+"; "}}if(t.globalScope[r]!==void 0)return"gscope['"+r+"']="+n+"; ";if(t.globalScope._SymbolsMap[r]!==void 0)return"gscope['"+t.globalScope._SymbolsMap[r]+"']="+n+"; ";if(t.undeclaredGlobalsInFunctions.has(r)){const u=t.undeclaredGlobalsInFunctions.get(r).manglename;return t.globalScope._SymbolsMap[r]=u,t.mangleMap[r]=u,t.undeclaredGlobalsInFunctions.delete(r),"gscope[lang.setAssig('"+u+"', runtimeCtx)]="+n+"; "}const a=be(t);return t.globalScope._SymbolsMap[r]=a,t.mangleMap[r]=a,"gscope['"+a+"']="+n+"; "}function Is(t,e){try{let n;return e.computed===!0?n=S(t,e.property):(n="'"+e.property.name+"'",De(e.property.name)),"lang.member("+S(t,e.object)+","+n+")"}catch(n){throw n}}function ks(t,e){try{return"lang.unary("+S(t,e.argument)+",'"+e.operator+"')"}catch(n){throw n}}function Bs(t,e){try{const n=[];for(let r=0;r<e.elements.length;r++)e.elements[r].type==="Literal"?n.push(S(t,e.elements[r])):n.push("lang.aCheck("+S(t,e.elements[r])+",'ArrayExpression')");return"["+n.join(",")+"]"}catch(n){throw n}}function Ts(t,e){try{const n=[];let r=0;for(const a of e.quasis)n.push(a.value?JSON.stringify(a.value.cooked):JSON.stringify("")),a.tail===!1&&(n.push(e.expressions[r]?"lang.castString(lang.aCheck("+S(t,e.expressions[r])+", 'TemplateLiteral'))":""),r++);return"(["+n.join(",")+"]).join('')"}catch(n){throw n}}function _s(t,e){try{return"lang.binary("+S(t,e.left)+","+S(t,e.right)+",'"+e.operator+"')"}catch(n){throw n}}function Ms(t,e){try{if(e.left.type==="AssignmentExpression"||e.left.type==="UpdateExpression")throw new ue(t,c.LogicalExpressionOnlyBoolean,e);if(e.right.type==="AssignmentExpression"||e.right.type==="UpdateExpression")throw new ue(t,c.LogicalExpressionOnlyBoolean,e);if(e.operator==="&&"||e.operator==="||")return"(lang.logicalCheck("+S(t,e.left)+") "+e.operator+" lang.logicalCheck("+S(t,e.right)+"))";throw new ue(null,c.LogicExpressionOrAnd,null)}catch(n){throw n}}function $s(t,e){try{const n=e.name.toLowerCase();if(De(n),t.localScope!==null){if(t.localScope[n]!==void 0)return"lscope['"+n+"']";if(t.localScope._SymbolsMap[n]!==void 0)return"lscope['"+t.localScope._SymbolsMap[n]+"']"}if(t.globalScope[n]!==void 0)return"gscope['"+n+"']";if(t.globalScope._SymbolsMap[n]!==void 0)return"gscope['"+t.globalScope._SymbolsMap[n]+"']";if(t.localScope!==null){if(t.undeclaredGlobalsInFunctions.has(n))return"gscope[lang.chkAssig('"+t.undeclaredGlobalsInFunctions.get(n).manglename+"',runtimeCtx)]";const r={manglename:be(t),node:e.argument};return t.undeclaredGlobalsInFunctions.set(n,r),"gscope[lang.chkAssig('"+r.manglename+"',runtimeCtx)]"}throw new ue(t,c.InvalidIdentifier,e)}catch(n){throw n}}function Ns(t,e){try{if(e.callee.type==="MemberExpression"){let a;e.callee.computed===!0?a=S(t,e.callee.property):(a="'"+e.callee.property.name+"'",De(e.callee.property.name));let u="[";for(let s=0;s<e.arguments.length;s++)s>0&&(u+=", "),u+=S(t,e.arguments[s]);return u+="]",t.isAsync?"(yield lang.callModuleFunction("+S(t,e.callee.object)+","+u+","+a+",runtimeCtx))":"lang.callModuleFunction("+S(t,e.callee.object)+","+u+","+a+",runtimeCtx)"}if(e.callee.type!=="Identifier")throw new ue(t,c.FuncionNotFound,e);const n=e.callee.name.toLowerCase();if(n==="iif")return Ls(t,e);if(n==="when")return Rs(t,e);if(n==="decode")return Ps(t,e);let r="";if(t.localScope!==null&&(t.localScope[n]!==void 0?r="lscope['"+n+"']":t.localScope._SymbolsMap[n]!==void 0&&(r="lscope['"+t.localScope._SymbolsMap[n]+"']")),r===""){if(t.globalScope[n]!==void 0)r="gscope['"+n+"']";else if(t.globalScope._SymbolsMap[n]!==void 0)r="gscope['"+t.globalScope._SymbolsMap[n]+"']";else if(t.localScope!==null)if(t.undeclaredGlobalsInFunctions.has(n))r="gscope[lang.chkAssig('"+t.undeclaredGlobalsInFunctions.get(n).manglename+"',runtimeCtx)]";else{const a={manglename:be(t),node:e.argument};t.undeclaredGlobalsInFunctions.set(n,a),r="gscope[lang.chkAssig('"+a.manglename+"',runtimeCtx)]"}}if(r!==""){let a="[";for(let u=0;u<e.arguments.length;u++)u>0&&(a+=", "),a+=S(t,e.arguments[u]);return a+="]",t.isAsync?"(yield lang.callfunc("+r+","+a+",runtimeCtx) )":"lang.callfunc("+r+","+a+",runtimeCtx)"}throw new ue(t,c.FuncionNotFound,e)}catch(n){throw n}}function Ls(t,e){try{if(e.arguments.length!==3)throw new ue(t,c.WrongNumberOfParameters,e);const n=Ie(t);return`${t.isAsync?`(yield (function() { 
 return lang.__awaiter(this, void 0, void 0, function* () {`:"function() {"}
        var ${n} = ${S(t,e.arguments[0])};
       
        if (${n} === true) {
          return  ${S(t,e.arguments[1])};
        }
        else if (${n} === false) {
          return ${S(t,e.arguments[2])};
        }
        else {
          lang.error('ExecutionErrorCodes.BooleanConditionRequired');
        }
      ${t.isAsync?"})}()))":"}()"}`}catch(n){throw n}}function Rs(t,e){try{if(e.arguments.length<3)throw new ue(t,c.WrongNumberOfParameters,e);if(e.arguments.length%2==0)throw new ue(t,c.WrongNumberOfParameters,e);const n=Ie(t);let r="var ";for(let a=0;a<e.arguments.length-1;a+=2)r+=`${n} = lang.mustBoolean(${S(t,e.arguments[a])}, runtimeCtx);
      if (${n} === true ) {
        return ${S(t,e.arguments[a+1])} 
      }
`;return`${t.isAsync?`(yield (function() { 
 return lang.__awaiter(this, void 0, void 0, function* () {`:"function() {"}
        ${r}
        return ${S(t,e.arguments[e.arguments.length-1])}
        ${t.isAsync?"})}()))":"}()"}`}catch(n){throw n}}function Ps(t,e){try{if(e.arguments.length<2)throw new ue(t,c.WrongNumberOfParameters,e);if(e.arguments.length===2)return`(${S(t,e.arguments[1])})`;if((e.arguments.length-1)%2==0)throw new ue(t,c.WrongNumberOfParameters,e);const n=Ie(t),r=Ie(t);let a="var ";for(let u=1;u<e.arguments.length-1;u+=2)a+=`${r} = ${S(t,e.arguments[u])};
      if (lang.binary(${r}, ${n}, "==") === true ) {
        return ${S(t,e.arguments[u+1])} 
      }
`;return`${t.isAsync?`(yield (function() { 
 return lang.__awaiter(this, void 0, void 0, function* () {`:"function() {"}
        var ${n} = ${S(t,e.arguments[0])};
        ${a}
        return ${S(t,e.arguments[e.arguments.length-1])}
        ${t.isAsync?"})}()))":"}()"}`}catch(n){throw n}}const te={};function be(t){return t.symbols.symbolCounter++,"_T"+t.symbols.symbolCounter.toString()}function Ie(t){return t.symbols.symbolCounter++,"_Tvar"+t.symbols.symbolCounter.toString()}di(te,ye),Ai(te,ye),yi(te,ye),gi(te,ye),xi(te,ye),te.iif=function(t,e){try{return ye(t,e,(n,r,a)=>{throw new h(t,c.Unrecognised,e)})}catch(n){throw n}},te.decode=function(t,e){try{return ye(t,e,(n,r,a)=>{throw new h(t,c.Unrecognised,e)})}catch(n){throw n}},te.when=function(t,e){try{return ye(t,e,(n,r,a)=>{throw new h(t,c.Unrecognised,e)})}catch(n){throw n}};const dt={};for(const t in te)dt[t]=new We(te[t]);Kn(te,ye);for(const t in te)te[t]=new We(te[t]);const Xn=function(){};Xn.prototype=te;const Qn=function(){};function bi(t,e,n){const r={};t||(t={}),n||(n={}),r._SymbolsMap={},r.textformatting=1,r.infinity=1,r.pi=1;for(const a in e)r[a]=1;for(const a in n)r[a]=1;for(const a in t)r[a]=1;return r}function Si(t,e,n,r){const a=n?new Qn:new Xn;t||(t={}),e||(e={});const u=new _({newline:`
`,tab:"	",singlequote:"'",doublequote:'"',forwardslash:"/",backwardslash:"\\"});u.immutable=!1,a._SymbolsMap={textformatting:1,infinity:1,pi:1},a.textformatting=u,a.infinity=Number.POSITIVE_INFINITY,a.pi=Math.PI;for(const s in e)a[s]=e[s],a._SymbolsMap[s]=1;for(const s in t)a._SymbolsMap[s]=1,t[s]&&t[s].declaredClass==="esri.Graphic"?a[s]=X.createFromGraphic(t[s],r??null):a[s]=t[s];return a}Qn.prototype=dt;function Ke(t,e){const n={mode:e,compiled:!0,functions:{},signatures:[],standardFunction:ye,standardFunctionAsync:ye,evaluateIdentifier:Os};for(let r=0;r<t.length;r++)t[r].registerFunctions(n);if(e==="sync"){for(const r in n.functions)te[r]=new We(n.functions[r]),Xn.prototype[r]=te[r];for(let r=0;r<n.signatures.length;r++)Bn(n.signatures[r],"sync")}else{for(const r in n.functions)dt[r]=new We(n.functions[r]),Qn.prototype[r]=dt[r];for(let r=0;r<n.signatures.length;r++)Bn(n.signatures[r],"async")}}function Os(t,e){const n=e.name;if(n==="_SymbolsMap")throw new h(t,c.InvalidIdentifier,null);if(t.localStack.length>0){if(n.substr(0,2).toLowerCase()!=="_t"&&t.localStack[t.localStack.length-1][n]!==void 0)return t.localStack[t.localStack.length-1][n];const a=t.mangleMap[n];if(a!==void 0&&t.localStack[t.localStack.length-1][a]!==void 0)return t.localStack[t.localStack.length-1][a]}if(n.substr(0,2).toLowerCase()!=="_t"&&t.globalScope[n]!==void 0||t.globalScope._SymbolsMap[n]===1)return t.globalScope[n];const r=t.mangleMap[n];return r!==void 0?t.globalScope[r]:void 0}Ke([_n],"sync"),Ke([_n],"async");let gn=0;const vi={error(t){throw new h(null,t,null)},__awaiter:(t,e,n,r)=>new Promise((a,u)=>{function s(l){try{o(r.next(l))}catch(m){u(m)}}function i(l){try{o(r.throw(l))}catch(m){u(m)}}function o(l){l.done?a(l.value):l.value&&l.value.then?l.value.then(s,i):(gn++,gn%100==0?setTimeout(()=>{gn=0,s(l.value)},0):s(l.value))}o((r=r.apply(t,e||[])).next())}),functionDepthchecker:(t,e)=>function(){if(e.depthCounter.depth++,e.localStack.push([]),e.depthCounter.depth>64)throw new h(null,c.MaximumCallDepth,null);const n=t.apply(this,arguments);return Oe(n)?n.then(r=>(e.depthCounter.depth--,e.localStack.length=e.localStack.length-1,r)):(e.depthCounter.depth--,e.localStack.length=e.localStack.length-1,n)},chkAssig(t,e){if(e.gdefs[t]===void 0)throw new h(e,c.InvalidIdentifier,null);return t},mustBoolean(t,e){if(t===!0||t===!1)return t;throw new h(e,c.BooleanConditionRequired,null)},setAssig:(t,e)=>(e.gdefs[t]=1,t),castString:t=>A(t),aCheck(t,e){if(K(t))throw e==="ArrayExpression"?new h(null,c.NoFunctionInArray,null):e==="ObjectExpression"?new h(null,c.NoFunctionInDictionary,null):new h(null,c.NoFunctionInTemplateLiteral,null);return t===C?null:t},Dictionary:_,Feature:X,UserDefinedCompiledFunction:ds,dictionary(t){const e={},n=new Map;for(let a=0;a<t.length;a+=2){if(K(t[a+1]))throw new h(null,c.NoFunctionInDictionary,null);if(b(t[a])===!1)throw new h(null,c.KeyMustBeString,null);let u=t[a].toString();const s=u.toLowerCase();n.has(s)?u=n.get(s):n.set(s,u),t[a+1]===C?e[u]=null:e[u]=t[a+1]}const r=new _(e);return r.immutable=!1,r},strCheck(t){if(b(t)===!1)throw new h(null,c.KeyMustBeString,null);return t},unary(t,e){if(U(t)){if(e==="!")return!t;if(e==="-")return-1*d(t);if(e==="+")return 1*d(t);if(e==="~")return~d(t);throw new h(null,c.UnsupportedUnaryOperator,null)}if(e==="-")return-1*d(t);if(e==="+")return 1*d(t);if(e==="~")return~d(t);throw new h(null,c.UnsupportedUnaryOperator,null)},logicalCheck(t){if(U(t)===!1)throw new h(null,c.LogicExpressionOrAnd,null);return t},logical(t,e,n){if(U(t)&&U(e))switch(n){case"||":return t||e;case"&&":return t&&e;default:throw new h(null,c.LogicExpressionOrAnd,null)}throw new h(null,c.LogicExpressionOrAnd,null)},binary(t,e,n){switch(n){case"|":case"<<":case">>":case">>>":case"^":case"&":return jn(d(t),d(e),n);case"==":case"=":return xe(t,e);case"!=":return!xe(t,e);case"<":case">":case"<=":case">=":return Zn(t,e,n);case"+":return b(t)||b(e)?A(t)+A(e):d(t)+d(e);case"-":return d(t)-d(e);case"*":return d(t)*d(e);case"/":return d(t)/d(e);case"%":return d(t)%d(e);default:throw new h(null,c.UnsupportedOperator,null)}},assign(t,e,n){switch(e){case"=":return t===C?null:t;case"/=":return d(n)/d(t);case"*=":return d(n)*d(t);case"-=":return d(n)-d(t);case"+=":return b(n)||b(t)?A(n)+A(t):d(n)+d(t);case"%=":return d(n)%d(t);default:throw new h(null,c.UnsupportedOperator,null)}},update(t,e,n,r){const a=d(t[e]);return t[e]=n==="++"?a+1:a-1,r===!1?a:n==="++"?a+1:a-1},graphicToFeature:(t,e,n)=>t===null?null:X.createFromGraphicLikeObject(t.geometry,t.attributes,e,n.timeReference),memberupdate(t,e,n,r){let a;if(k(t)){if(!G(e))throw new h(null,c.ArrayAccessorMustBeNumber,null);if(e<0&&(e=t.length+e),e<0||e>=t.length)throw new h(null,c.OutOfBounds,null);a=d(t[e]),t[e]=n==="++"?a+1:a-1}else if(t instanceof _){if(b(e)===!1)throw new h(null,c.KeyAccessorMustBeString,null);if(t.hasField(e)!==!0)throw new h(null,c.FieldNotFound,null,{key:e});a=d(t.field(e)),t.setField(e,n==="++"?a+1:a-1)}else if(Q(t)){if(b(e)===!1)throw new h(null,c.KeyAccessorMustBeString,null);if(t.hasField(e)!==!0)throw new h(null,c.FieldNotFound,null);a=d(t.field(e)),t.setField(e,n==="++"?a+1:a-1)}else{if(N(t))throw new h(null,c.Immutable,null);if(!(t instanceof Ft))throw new h(null,c.InvalidIdentifier,null);if(b(e)===!1)throw new h(null,c.ModuleAccessorMustBeString,null);if(t.hasGlobal(e)!==!0)throw new h(null,c.ModuleExportNotFound,null);a=d(t.global(e)),t.setGlobal(e,n==="++"?a+1:a-1)}return r===!1?a:n==="++"?a+1:a-1},assignmember(t,e,n,r){if(k(t)){if(!G(e))throw new h(null,c.ArrayAccessorMustBeNumber,null);if(e<0&&(e=t.length+e),e<0||e>t.length)throw new h(null,c.OutOfBounds,null);if(e===t.length){if(n!=="=")throw new h(null,c.OutOfBounds,null);t[e]=this.assign(r,n,t[e])}else t[e]=this.assign(r,n,t[e])}else if(t instanceof _){if(b(e)===!1)throw new h(null,c.KeyAccessorMustBeString,null);if(t.hasField(e)===!0)t.setField(e,this.assign(r,n,t.field(e)));else{if(n!=="=")throw new h(null,c.FieldNotFound,null);t.setField(e,this.assign(r,n,null))}}else if(Q(t)){if(b(e)===!1)throw new h(null,c.KeyAccessorMustBeString,null);if(t.hasField(e)===!0)t.setField(e,this.assign(r,n,t.field(e)));else{if(n!=="=")throw new h(null,c.FieldNotFound,null);t.setField(e,this.assign(r,n,null))}}else{if(N(t))throw new h(null,c.Immutable,null);if(!(t instanceof Ft))throw new h(null,c.InvalidIdentifier,null);if(b(e)===!1)throw new h(null,c.ModuleAccessorMustBeString,null);if(!t.hasGlobal(e))throw new h(null,c.ModuleExportNotFound,null);t.setGlobal(e,this.assign(r,n,t.global(e)))}},member(t,e){if(t===null)throw new h(null,c.MemberOfNull,null);if(t instanceof _||Q(t)){if(b(e))return t.field(e);throw new h(null,c.InvalidMemberAccessKey,null)}if(t instanceof T){if(b(e))return vt(t,e,null,null);throw new h(null,c.InvalidMemberAccessKey,null)}if(k(t)){if(G(e)&&isFinite(e)&&Math.floor(e)===e){if(e<0&&(e=t.length+e),e>=t.length||e<0)throw new h(null,c.OutOfBounds,null);return t[e]}throw new h(null,c.InvalidMemberAccessKey,null)}if(b(t)){if(G(e)&&isFinite(e)&&Math.floor(e)===e){if(e<0&&(e=t.length+e),e>=t.length||e<0)throw new h(null,c.OutOfBounds,null);return t[e]}throw new h(null,c.InvalidMemberAccessKey,null)}if(N(t)){if(G(e)&&isFinite(e)&&Math.floor(e)===e){if(e<0&&(e=t.length()+e),e>=t.length()||e<0)throw new h(null,c.OutOfBounds,null);return t.get(e)}throw new h(null,c.InvalidMemberAccessKey,null)}if(t instanceof Ft){if(b(e))return t.global(e);throw new h(null,c.InvalidMemberAccessKey,null)}throw new h(null,c.InvalidMemberAccessKey,null)},callfunc:(t,e,n)=>t.call(n,{arguments:e,preparsed:!0}),loadModule(t,e){const n=e.moduleFactoryMap[t];if(e.moduleSingletons[n])return e.moduleSingletons[n];const r=e.moduleFactory[n]({vars:{},moduleSingletons:e.moduleSingletons,depthCounter:e.depthCounter,console:e.console,abortSignal:e.abortSignal,isAsync:e.isAsync,services:e.services,lrucache:e.lrucache,timeReference:e.timeReference?e.timeReference:null,interceptor:e.interceptor},e.spatialReference);return e.moduleSingletons[n]=r,r},callModuleFunction(t,e,n,r){if(!(t instanceof Ft))throw new h(null,c.FuncionNotFound,null);const a=t.global(n);if(K(a)===!1)throw new h(null,c.CallNonFunction,null);return a.call(r,{preparsed:!0,arguments:e})}};function Jt(t){console.log(t)}function Ii(t,e,n=!1){e===null&&(e={vars:{},customfunctions:{}});let r=null;t.usesModules&&(r=new tn(null,t.loadedModules));const a={isAsync:n,globalScope:bi(e.vars,n?dt:te,e.customfunctions),moduleFactory:{},moduleFactoryMap:{},undeclaredGlobalsInFunctions:new Map,customfunctions:e.customfunctions,libraryResolver:r,localScope:null,mangleMap:{},depthCounter:{depth:1},exports:{},console:Jt,lrucache:e.lrucache,timeReference:e.timeReference??null,interceptor:e.interceptor,services:e.services,symbols:{symbolCounter:0}};let u=S(a,t);u===""&&(u="lc.voidOperation; "),a.undeclaredGlobalsInFunctions.size>0&&a.undeclaredGlobalsInFunctions.forEach(p=>{throw new ue(e,c.InvalidIdentifier,p.node)});let s="";s=n?`var runtimeCtx=this.prepare(context, true);
 var lc = this.lc;  var lang = this.lang; var gscope=runtimeCtx.globalScope; 
return lang.__awaiter(this, void 0, void 0, function* () {

 function mainBody() {
 var lastStatement=lc.voidOperation;
 return lang.__awaiter(this, void 0, void 0, function* () {
`+u+`
 return lastStatement; }); } 
 return this.postProcess(yield mainBody()); }); `:`var runtimeCtx=this.prepare(context, false);
 var lc = this.lc;  var lang = this.lang; var gscope=runtimeCtx.globalScope; 
 function mainBody() {
 var lastStatement=lc.voidOperation;
 `+u+`
 return lastStatement; } 
 return this.postProcess(mainBody()); `;const i=a.moduleFactory,o=a.moduleFactoryMap,l=a.exports,m={};for(const p in l)m[p]=a.mangleMap[p]!==void 0?a.mangleMap[p]:p;const f={lc:ci,lang:vi,mangles:a.mangleMap,postProcess(p){if(p instanceof me&&(p=p.value),p instanceof lt&&(p=p.value),p===C&&(p=null),p===Ae)throw new h(null,c.IllegalResult,null);if(p===pt)throw new h(null,c.IllegalResult,null);if(K(p))throw new h(null,c.IllegalResult,null);return p},prepare(p,g){let w=p.spatialReference;w==null&&(w=kt.WebMercator);const B=Si(p.vars,p.customfunctions,g,p.timeReference);return{localStack:[],isAsync:g,moduleFactory:i,moduleFactoryMap:o,mangleMap:this.mangles,moduleSingletons:{},exports:l,gdefs:{},exportmangle:m,spatialReference:w,globalScope:B,abortSignal:p.abortSignal===void 0||p.abortSignal===null?{aborted:!1}:p.abortSignal,localScope:null,services:p.services,console:p.console?p.console:Jt,lrucache:p.lrucache,timeReference:p.timeReference??null,interceptor:p.interceptor,symbols:{symbolCounter:0},depthCounter:{depth:1}}}};return new Function("context","spatialReference",s).bind(f)}async function Us(){return Ke([await Ne(()=>import("./geomasync-wZnXylSS.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11]))],"async"),!0}class Ft extends Zt{constructor(e){super(null),this.moduleContext=e}hasGlobal(e){return this.moduleContext.exports[e]===void 0&&(e=e.toLowerCase()),this.moduleContext.exports[e]!==void 0}setGlobal(e,n){const r=this.moduleContext.globalScope,a=e.toLowerCase();if(K(n))throw new h(null,c.AssignModuleFunction,null);r[this.moduleContext.exportmangle[a]]=n}global(e){const n=this.moduleContext.globalScope;e=e.toLowerCase();const r=n[this.moduleContext.exportmangle[e]];if(r===void 0)throw new h(null,c.InvalidIdentifier,null);if(K(r)&&!(r instanceof Le)){const a=new Le;return a.fn=r,a.parameterEvaluator=ye,a.context=this.moduleContext,n[this.moduleContext.exportmangle[e]]=a,a}return r}}function Gs(t,e,n=!1){const r={isAsync:n,moduleFactory:e.moduleFactory,moduleFactoryMap:{},libraryResolver:new tn(null,t.loadedModules),globalScope:bi(e.vars,n?dt:te,e.customfunctions),customfunctions:e.customfunctions,localScope:null,mangleMap:{},undeclaredGlobalsInFunctions:new Map,depthCounter:{depth:1},exports:{},console:Jt,lrucache:e.lrucache,timeReference:e.timeReference??null,interceptor:e.interceptor,services:e.services,symbols:{symbolCounter:0}};let a=S(r,t);a===""&&(a="lc.voidOperation; ");let u="";u=n?`var runtimeCtx=this.prepare(context, true);
 var lc = this.lc;  var lang = this.lang; var gscope=runtimeCtx.globalScope; 
return lang.__awaiter(this, void 0, void 0, function* () {

 function mainBody() {
 var lastStatement=lc.voidOperation;
 return lang.__awaiter(this, void 0, void 0, function* () {
`+a+`
 return lastStatement; }); } 
 yield mainBody(); 
 return this.prepareModule(runtimeCtx); }); `:`var runtimeCtx=this.prepare(context, false);
 var lc = this.lc;  var lang = this.lang; var gscope=runtimeCtx.globalScope; 
 function mainBody() {
 var lastStatement=lc.voidOperation;
 `+a+`
 return lastStatement; } 
 mainBody(); 
 return this.prepareModule(runtimeCtx); `;const s=r.moduleFactory,i=r.moduleFactoryMap,o=r.exports,l={};for(const f in o)l[f]=r.mangleMap[f]!==void 0?r.mangleMap[f]:f;const m={lc:ci,lang:vi,mangles:r.mangleMap,prepareModule:f=>new Ft(f),prepare(f,p){let g=f.spatialReference;g==null&&(g=new kt({wkid:102100}));const w=Si(f.vars,f.customfunctions,p,f.timeReference);return{localStack:[],isAsync:p,exports:o,exportmangle:l,gdefs:{},moduleFactory:s,moduleFactoryMap:i,moduleSingletons:f.moduleSingletons,mangleMap:this.mangles,spatialReference:g,globalScope:w,abortSignal:f.abortSignal===void 0||f.abortSignal===null?{aborted:!1}:f.abortSignal,localScope:null,services:f.services,console:f.console?f.console:Jt,lrucache:f.lrucache,timeReference:f.timeReference??null,interceptor:f.interceptor,symbols:{symbolCounter:0},depthCounter:f.depthCounter}}};return new Function("context","spatialReference",u).bind(m)}var $,F;(function(t){t.Break="break",t.Continue="continue",t.Else="else",t.False="false",t.For="for",t.From="from",t.Function="function",t.If="if",t.Import="import",t.Export="export",t.In="in",t.Null="null",t.Return="return",t.True="true",t.Var="var",t.While="while"})($||($={})),function(t){t.AssignmentExpression="AssignmentExpression",t.ArrayExpression="ArrayExpression",t.BlockComment="BlockComment",t.BlockStatement="BlockStatement",t.BinaryExpression="BinaryExpression",t.BreakStatement="BreakStatement",t.CallExpression="CallExpression",t.ContinueStatement="ContinueStatement",t.EmptyStatement="EmptyStatement",t.ExpressionStatement="ExpressionStatement",t.ExportNamedDeclaration="ExportNamedDeclaration",t.ExportSpecifier="ExportSpecifier",t.ForStatement="ForStatement",t.ForInStatement="ForInStatement",t.FunctionDeclaration="FunctionDeclaration",t.Identifier="Identifier",t.IfStatement="IfStatement",t.ImportDeclaration="ImportDeclaration",t.ImportDefaultSpecifier="ImportDefaultSpecifier",t.LineComment="LineComment",t.Literal="Literal",t.LogicalExpression="LogicalExpression",t.MemberExpression="MemberExpression",t.ObjectExpression="ObjectExpression",t.Program="Program",t.Property="Property",t.ReturnStatement="ReturnStatement",t.TemplateElement="TemplateElement",t.TemplateLiteral="TemplateLiteral",t.UnaryExpression="UnaryExpression",t.UpdateExpression="UpdateExpression",t.VariableDeclaration="VariableDeclaration",t.VariableDeclarator="VariableDeclarator",t.WhileStatement="WhileStatement"}(F||(F={}));const On=["++","--"],ki=["-","+","!","~"],Bi=["=","/=","*=","%=","+=","-="],Ti=["||","&&"],zs=["|","&",">>","<<",">>>","^","==","!=","<","<=",">",">=","+","-","*","/","%"],qs={"||":1,"&&":2,"|":3,"^":4,"&":5,"==":6,"!=":6,"<":7,">":7,"<=":7,">=":7,"<<":8,">>":8,">>>":8,"+":9,"-":9,"*":10,"/":10,"%":10};var E;(function(t){t[t.Unknown=0]="Unknown",t[t.BooleanLiteral=1]="BooleanLiteral",t[t.EOF=2]="EOF",t[t.Identifier=3]="Identifier",t[t.Keyword=4]="Keyword",t[t.NullLiteral=5]="NullLiteral",t[t.NumericLiteral=6]="NumericLiteral",t[t.Punctuator=7]="Punctuator",t[t.StringLiteral=8]="StringLiteral",t[t.Template=10]="Template"})(E||(E={}));const Js=["Unknown","Boolean","<end>","Identifier","Keyword","Null","Numeric","Punctuator","String","RegularExpression","Template"];var x;(function(t){t.InvalidModuleUri="InvalidModuleUri",t.ForInOfLoopInitializer="ForInOfLoopInitializer",t.IdentiferExpected="IdentiferExpected",t.InvalidEscapedReservedWord="InvalidEscapedReservedWord",t.InvalidExpression="InvalidExpression",t.InvalidFunctionIdentifier="InvalidFunctionIdentifier",t.InvalidHexEscapeSequence="InvalidHexEscapeSequence",t.InvalidLeftHandSideInAssignment="InvalidLeftHandSideInAssignment",t.InvalidLeftHandSideInForIn="InvalidLeftHandSideInForIn",t.InvalidTemplateHead="InvalidTemplateHead",t.InvalidVariableAssignment="InvalidVariableAssignment",t.KeyMustBeString="KeyMustBeString",t.NoFunctionInsideBlock="NoFunctionInsideBlock",t.NoFunctionInsideFunction="NoFunctionInsideFunction",t.ModuleExportRootOnly="ModuleExportRootOnly",t.ModuleImportRootOnly="ModuleImportRootOnly",t.PunctuatorExpected="PunctuatorExpected",t.TemplateOctalLiteral="TemplateOctalLiteral",t.UnexpectedBoolean="UnexpectedBoolean",t.UnexpectedEndOfScript="UnexpectedEndOfScript",t.UnexpectedIdentifier="UnexpectedIdentifier",t.UnexpectedKeyword="UnexpectedKeyword",t.UnexpectedNull="UnexpectedNull",t.UnexpectedNumber="UnexpectedNumber",t.UnexpectedPunctuator="UnexpectedPunctuator",t.UnexpectedString="UnexpectedString",t.UnexpectedTemplate="UnexpectedTemplate",t.UnexpectedToken="UnexpectedToken"})(x||(x={}));const Vs={[x.InvalidModuleUri]:"Module uri must be a text literal.",[x.ForInOfLoopInitializer]:"for-in loop variable declaration may not have an initializer.",[x.IdentiferExpected]:"'${value}' is an invalid identifier.",[x.InvalidEscapedReservedWord]:"Keyword cannot contain escaped characters.",[x.InvalidExpression]:"Invalid expression.",[x.InvalidFunctionIdentifier]:"'${value}' is an invalid function identifier.",[x.InvalidHexEscapeSequence]:"Invalid hexadecimal escape sequence.",[x.InvalidLeftHandSideInAssignment]:"Invalid left-hand side in assignment.",[x.InvalidLeftHandSideInForIn]:"Invalid left-hand side in for-in.",[x.InvalidTemplateHead]:"Invalid template structure.",[x.InvalidVariableAssignment]:"Invalid variable assignment.",[x.KeyMustBeString]:"Object property keys must be a word starting with a letter.",[x.NoFunctionInsideBlock]:"Functions cannot be declared inside of code blocks.",[x.NoFunctionInsideFunction]:"Functions cannot be declared inside another function.",[x.ModuleExportRootOnly]:"Module exports cannot be declared inside of code blocks.",[x.ModuleImportRootOnly]:"Module import cannot be declared inside of code blocks.",[x.PunctuatorExpected]:"'${value}' expected.",[x.TemplateOctalLiteral]:"Octal literals are not allowed in template literals.",[x.UnexpectedBoolean]:"Unexpected boolean literal.",[x.UnexpectedEndOfScript]:"Unexpected end of Arcade expression.",[x.UnexpectedIdentifier]:"Unexpected identifier.",[x.UnexpectedKeyword]:"Unexpected keyword.",[x.UnexpectedNull]:"Unexpected null literal.",[x.UnexpectedNumber]:"Unexpected number.",[x.UnexpectedPunctuator]:"Unexpected ponctuator.",[x.UnexpectedString]:"Unexpected text literal.",[x.UnexpectedTemplate]:"Unexpected quasi '${value}'.",[x.UnexpectedToken]:"Unexpected token '${value}'."};let Ze=class _i extends Error{constructor({code:e,index:n,line:r,column:a,len:u=0,description:s,data:i}){var o;super(`${s??e}`),this.declaredRootClass="esri.arcade.lib.parsingerror",this.name="ParsingError",this.code=e,this.index=n,this.line=r,this.column=a,this.len=u,this.data=i,this.description=s,this.range={start:{line:r,column:a-1},end:{line:r,column:a+u}},(o=Error.captureStackTrace)==null||o.call(Error,this,_i)}};function Hs(t){return(t==null?void 0:t.type)===F.Program}function Pr(t){return(t==null?void 0:t.type)===F.BlockStatement}function Zs(t){return(t==null?void 0:t.type)===F.BlockComment}function js(t){return(t==null?void 0:t.type)===F.EmptyStatement}function Ws(t){return(t==null?void 0:t.type)===F.VariableDeclarator}function yn(t,e){return!!e&&e.loc.end.line===t.loc.start.line&&e.loc.end.column<=t.loc.start.column}function Or(t,e){return t.range[0]>=e.range[0]&&t.range[1]<=e.range[1]}let Ks=class{constructor(){this.comments=[],this._nodeStack=[],this._newComments=[]}insertInnerComments(e){if(!Pr(e)||e.body.length!==0)return;const n=[];for(let r=this._newComments.length-1;r>=0;--r){const a=this._newComments[r];e.range[1]>=a.range[0]&&(n.unshift(a),this._newComments.splice(r,1))}n.length&&(e.innerComments=n)}attachTrailingComments(e){if(!e)return;const n=this._nodeStack[this._nodeStack.length-1];if(Pr(e)&&Or(n,e))for(let a=this._newComments.length-1;a>=0;--a){const u=this._newComments[a];Or(u,e)&&(n.trailingComments=[...n.trailingComments??[],u],this._newComments.splice(a,1))}let r=[];if(this._newComments.length>0)for(let a=this._newComments.length-1;a>=0;--a){const u=this._newComments[a];yn(u,n)?(n.trailingComments=[...n.trailingComments??[],u],this._newComments.splice(a,1)):yn(u,e)&&(r.unshift(u),this._newComments.splice(a,1))}n!=null&&n.trailingComments&&yn(n.trailingComments[0],e)&&(r=[...r,...n.trailingComments],delete n.trailingComments),r.length>0&&(e.trailingComments=r)}attachLeadingComments(e){var u,s;if(!e)return;let n;for(;this._nodeStack.length>0;){const i=this._nodeStack[this._nodeStack.length-1];if(!(e.range[0]<=i.range[0]))break;n=i,this._nodeStack.pop()}const r=[],a=[];if(n){for(let i=(((u=n.leadingComments)==null?void 0:u.length)??0)-1;i>=0;--i){const o=n.leadingComments[i];e.range[0]>=o.range[1]?(r.unshift(o),n.leadingComments.splice(i,1)):Ws(e)&&!Zs(o)&&(a.unshift(o),n.leadingComments.splice(i,1))}return((s=n.leadingComments)==null?void 0:s.length)===0&&delete n.leadingComments,r.length&&(e.leadingComments=r),void(a.length&&(e.trailingComments=[...a,...e.trailingComments??[]]))}for(let i=this._newComments.length-1;i>=0;--i){const o=this._newComments[i];e.range[0]>=o.range[0]&&(r.unshift(o),this._newComments.splice(i,1))}r.length&&(e.leadingComments=r)}attachComments(e){if(Hs(e)&&e.body.length>0){const n=this._nodeStack[this._nodeStack.length-1];return n?(n.trailingComments=[...n.trailingComments??[],...this._newComments],this._newComments.length=0,void this._nodeStack.pop()):(e.trailingComments=[...this._newComments],void(this._newComments.length=0))}this.attachTrailingComments(e),this.attachLeadingComments(e),this.insertInnerComments(e),this._nodeStack.push(e)}collectComment(e){this.comments.push(e),this._newComments.push(e)}};function Un(t,e){const n=Vs[t];return e?n.replace(/\${(.*?)}/g,(r,a)=>{var u;return((u=e[a])==null?void 0:u.toString())??""}):n}let Ys=class{constructor(e=!1){this.tolerant=e,this.errors=[]}recordError(e){this.errors.push(e)}tolerate(e){if(!this.tolerant)throw e;this.recordError(e)}throwError(e){throw e.description=e.description??Un(e.code,e.data),new Ze(e)}tolerateError(e){e.description=e.description??Un(e.code,e.data);const n=new Ze(e);if(!this.tolerant)throw n;this.recordError(n)}};function Ur(t,e){if(!t)throw new Error("ASSERT: "+e)}const Gr={NonAsciiIdentifierStart:/[\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0560-\u0588\u05D0-\u05EA\u05EF-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u0860-\u086A\u08A0-\u08B4\u08B6-\u08BD\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u09FC\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C60\u0C61\u0C80\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D54-\u0D56\u0D5F-\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81\u0E82\u0E84\u0E86-\u0E8A\u0E8C-\u0EA3\u0EA5\u0EA7-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u170C\u170E-\u1711\u1720-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u1820-\u1878\u1880-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u1A00-\u1A16\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4B\u1B83-\u1BA0\u1BAE\u1BAF\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1C80-\u1C88\u1C90-\u1CBA\u1CBD-\u1CBF\u1CE9-\u1CEC\u1CEE-\u1CF3\u1CF5\u1CF6\u1CFA\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2118-\u211D\u2124\u2126\u2128\u212A-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u3005-\u3007\u3021-\u3029\u3031-\u3035\u3038-\u303C\u3041-\u3096\u309B-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312F\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FEF\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6EF\uA717-\uA71F\uA722-\uA788\uA78B-\uA7BF\uA7C2-\uA7C6\uA7F7-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB\uA8FD\uA8FE\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uA9E0-\uA9E4\uA9E6-\uA9EF\uA9FA-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB67\uAB70-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDD40-\uDD74\uDE80-\uDE9C\uDEA0-\uDED0\uDF00-\uDF1F\uDF2D-\uDF4A\uDF50-\uDF75\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF\uDFD1-\uDFD5]|\uD801[\uDC00-\uDC9D\uDCB0-\uDCD3\uDCD8-\uDCFB\uDD00-\uDD27\uDD30-\uDD63\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00\uDE10-\uDE13\uDE15-\uDE17\uDE19-\uDE35\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE4\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2\uDD00-\uDD23\uDF00-\uDF1C\uDF27\uDF30-\uDF45\uDFE0-\uDFF6]|\uD804[\uDC03-\uDC37\uDC83-\uDCAF\uDCD0-\uDCE8\uDD03-\uDD26\uDD44\uDD50-\uDD72\uDD76\uDD83-\uDDB2\uDDC1-\uDDC4\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE2B\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEDE\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3D\uDF50\uDF5D-\uDF61]|\uD805[\uDC00-\uDC34\uDC47-\uDC4A\uDC5F\uDC80-\uDCAF\uDCC4\uDCC5\uDCC7\uDD80-\uDDAE\uDDD8-\uDDDB\uDE00-\uDE2F\uDE44\uDE80-\uDEAA\uDEB8\uDF00-\uDF1A]|\uD806[\uDC00-\uDC2B\uDCA0-\uDCDF\uDCFF\uDDA0-\uDDA7\uDDAA-\uDDD0\uDDE1\uDDE3\uDE00\uDE0B-\uDE32\uDE3A\uDE50\uDE5C-\uDE89\uDE9D\uDEC0-\uDEF8]|\uD807[\uDC00-\uDC08\uDC0A-\uDC2E\uDC40\uDC72-\uDC8F\uDD00-\uDD06\uDD08\uDD09\uDD0B-\uDD30\uDD46\uDD60-\uDD65\uDD67\uDD68\uDD6A-\uDD89\uDD98\uDEE0-\uDEF2]|\uD808[\uDC00-\uDF99]|\uD809[\uDC00-\uDC6E\uDC80-\uDD43]|[\uD80C\uD81C-\uD820\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872\uD874-\uD879][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2E]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDED0-\uDEED\uDF00-\uDF2F\uDF40-\uDF43\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDE40-\uDE7F\uDF00-\uDF4A\uDF50\uDF93-\uDF9F\uDFE0\uDFE1\uDFE3]|\uD821[\uDC00-\uDFF7]|\uD822[\uDC00-\uDEF2]|\uD82C[\uDC00-\uDD1E\uDD50-\uDD52\uDD64-\uDD67\uDD70-\uDEFB]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB]|\uD838[\uDD00-\uDD2C\uDD37-\uDD3D\uDD4E\uDEC0-\uDEEB]|\uD83A[\uDC00-\uDCC4\uDD00-\uDD43\uDD4B]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDED6\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF34\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1\uDEB0-\uDFFF]|\uD87A[\uDC00-\uDFE0]|\uD87E[\uDC00-\uDE1D]/,NonAsciiIdentifierPart:/[\xAA\xB5\xB7\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0300-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u0483-\u0487\u048A-\u052F\u0531-\u0556\u0559\u0560-\u0588\u0591-\u05BD\u05BF\u05C1\u05C2\u05C4\u05C5\u05C7\u05D0-\u05EA\u05EF-\u05F2\u0610-\u061A\u0620-\u0669\u066E-\u06D3\u06D5-\u06DC\u06DF-\u06E8\u06EA-\u06FC\u06FF\u0710-\u074A\u074D-\u07B1\u07C0-\u07F5\u07FA\u07FD\u0800-\u082D\u0840-\u085B\u0860-\u086A\u08A0-\u08B4\u08B6-\u08BD\u08D3-\u08E1\u08E3-\u0963\u0966-\u096F\u0971-\u0983\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BC-\u09C4\u09C7\u09C8\u09CB-\u09CE\u09D7\u09DC\u09DD\u09DF-\u09E3\u09E6-\u09F1\u09FC\u09FE\u0A01-\u0A03\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A3C\u0A3E-\u0A42\u0A47\u0A48\u0A4B-\u0A4D\u0A51\u0A59-\u0A5C\u0A5E\u0A66-\u0A75\u0A81-\u0A83\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABC-\u0AC5\u0AC7-\u0AC9\u0ACB-\u0ACD\u0AD0\u0AE0-\u0AE3\u0AE6-\u0AEF\u0AF9-\u0AFF\u0B01-\u0B03\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3C-\u0B44\u0B47\u0B48\u0B4B-\u0B4D\u0B56\u0B57\u0B5C\u0B5D\u0B5F-\u0B63\u0B66-\u0B6F\u0B71\u0B82\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BBE-\u0BC2\u0BC6-\u0BC8\u0BCA-\u0BCD\u0BD0\u0BD7\u0BE6-\u0BEF\u0C00-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D-\u0C44\u0C46-\u0C48\u0C4A-\u0C4D\u0C55\u0C56\u0C58-\u0C5A\u0C60-\u0C63\u0C66-\u0C6F\u0C80-\u0C83\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBC-\u0CC4\u0CC6-\u0CC8\u0CCA-\u0CCD\u0CD5\u0CD6\u0CDE\u0CE0-\u0CE3\u0CE6-\u0CEF\u0CF1\u0CF2\u0D00-\u0D03\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D44\u0D46-\u0D48\u0D4A-\u0D4E\u0D54-\u0D57\u0D5F-\u0D63\u0D66-\u0D6F\u0D7A-\u0D7F\u0D82\u0D83\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0DCA\u0DCF-\u0DD4\u0DD6\u0DD8-\u0DDF\u0DE6-\u0DEF\u0DF2\u0DF3\u0E01-\u0E3A\u0E40-\u0E4E\u0E50-\u0E59\u0E81\u0E82\u0E84\u0E86-\u0E8A\u0E8C-\u0EA3\u0EA5\u0EA7-\u0EBD\u0EC0-\u0EC4\u0EC6\u0EC8-\u0ECD\u0ED0-\u0ED9\u0EDC-\u0EDF\u0F00\u0F18\u0F19\u0F20-\u0F29\u0F35\u0F37\u0F39\u0F3E-\u0F47\u0F49-\u0F6C\u0F71-\u0F84\u0F86-\u0F97\u0F99-\u0FBC\u0FC6\u1000-\u1049\u1050-\u109D\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u135D-\u135F\u1369-\u1371\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u170C\u170E-\u1714\u1720-\u1734\u1740-\u1753\u1760-\u176C\u176E-\u1770\u1772\u1773\u1780-\u17D3\u17D7\u17DC\u17DD\u17E0-\u17E9\u180B-\u180D\u1810-\u1819\u1820-\u1878\u1880-\u18AA\u18B0-\u18F5\u1900-\u191E\u1920-\u192B\u1930-\u193B\u1946-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u19D0-\u19DA\u1A00-\u1A1B\u1A20-\u1A5E\u1A60-\u1A7C\u1A7F-\u1A89\u1A90-\u1A99\u1AA7\u1AB0-\u1ABD\u1B00-\u1B4B\u1B50-\u1B59\u1B6B-\u1B73\u1B80-\u1BF3\u1C00-\u1C37\u1C40-\u1C49\u1C4D-\u1C7D\u1C80-\u1C88\u1C90-\u1CBA\u1CBD-\u1CBF\u1CD0-\u1CD2\u1CD4-\u1CFA\u1D00-\u1DF9\u1DFB-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u200C\u200D\u203F\u2040\u2054\u2071\u207F\u2090-\u209C\u20D0-\u20DC\u20E1\u20E5-\u20F0\u2102\u2107\u210A-\u2113\u2115\u2118-\u211D\u2124\u2126\u2128\u212A-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D7F-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2DE0-\u2DFF\u3005-\u3007\u3021-\u302F\u3031-\u3035\u3038-\u303C\u3041-\u3096\u3099-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312F\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FEF\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA62B\uA640-\uA66F\uA674-\uA67D\uA67F-\uA6F1\uA717-\uA71F\uA722-\uA788\uA78B-\uA7BF\uA7C2-\uA7C6\uA7F7-\uA827\uA840-\uA873\uA880-\uA8C5\uA8D0-\uA8D9\uA8E0-\uA8F7\uA8FB\uA8FD-\uA92D\uA930-\uA953\uA960-\uA97C\uA980-\uA9C0\uA9CF-\uA9D9\uA9E0-\uA9FE\uAA00-\uAA36\uAA40-\uAA4D\uAA50-\uAA59\uAA60-\uAA76\uAA7A-\uAAC2\uAADB-\uAADD\uAAE0-\uAAEF\uAAF2-\uAAF6\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB67\uAB70-\uABEA\uABEC\uABED\uABF0-\uABF9\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE00-\uFE0F\uFE20-\uFE2F\uFE33\uFE34\uFE4D-\uFE4F\uFE70-\uFE74\uFE76-\uFEFC\uFF10-\uFF19\uFF21-\uFF3A\uFF3F\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDD40-\uDD74\uDDFD\uDE80-\uDE9C\uDEA0-\uDED0\uDEE0\uDF00-\uDF1F\uDF2D-\uDF4A\uDF50-\uDF7A\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF\uDFD1-\uDFD5]|\uD801[\uDC00-\uDC9D\uDCA0-\uDCA9\uDCB0-\uDCD3\uDCD8-\uDCFB\uDD00-\uDD27\uDD30-\uDD63\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00-\uDE03\uDE05\uDE06\uDE0C-\uDE13\uDE15-\uDE17\uDE19-\uDE35\uDE38-\uDE3A\uDE3F\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE6\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2\uDD00-\uDD27\uDD30-\uDD39\uDF00-\uDF1C\uDF27\uDF30-\uDF50\uDFE0-\uDFF6]|\uD804[\uDC00-\uDC46\uDC66-\uDC6F\uDC7F-\uDCBA\uDCD0-\uDCE8\uDCF0-\uDCF9\uDD00-\uDD34\uDD36-\uDD3F\uDD44-\uDD46\uDD50-\uDD73\uDD76\uDD80-\uDDC4\uDDC9-\uDDCC\uDDD0-\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE37\uDE3E\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEEA\uDEF0-\uDEF9\uDF00-\uDF03\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3B-\uDF44\uDF47\uDF48\uDF4B-\uDF4D\uDF50\uDF57\uDF5D-\uDF63\uDF66-\uDF6C\uDF70-\uDF74]|\uD805[\uDC00-\uDC4A\uDC50-\uDC59\uDC5E\uDC5F\uDC80-\uDCC5\uDCC7\uDCD0-\uDCD9\uDD80-\uDDB5\uDDB8-\uDDC0\uDDD8-\uDDDD\uDE00-\uDE40\uDE44\uDE50-\uDE59\uDE80-\uDEB8\uDEC0-\uDEC9\uDF00-\uDF1A\uDF1D-\uDF2B\uDF30-\uDF39]|\uD806[\uDC00-\uDC3A\uDCA0-\uDCE9\uDCFF\uDDA0-\uDDA7\uDDAA-\uDDD7\uDDDA-\uDDE1\uDDE3\uDDE4\uDE00-\uDE3E\uDE47\uDE50-\uDE99\uDE9D\uDEC0-\uDEF8]|\uD807[\uDC00-\uDC08\uDC0A-\uDC36\uDC38-\uDC40\uDC50-\uDC59\uDC72-\uDC8F\uDC92-\uDCA7\uDCA9-\uDCB6\uDD00-\uDD06\uDD08\uDD09\uDD0B-\uDD36\uDD3A\uDD3C\uDD3D\uDD3F-\uDD47\uDD50-\uDD59\uDD60-\uDD65\uDD67\uDD68\uDD6A-\uDD8E\uDD90\uDD91\uDD93-\uDD98\uDDA0-\uDDA9\uDEE0-\uDEF6]|\uD808[\uDC00-\uDF99]|\uD809[\uDC00-\uDC6E\uDC80-\uDD43]|[\uD80C\uD81C-\uD820\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872\uD874-\uD879][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2E]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDE60-\uDE69\uDED0-\uDEED\uDEF0-\uDEF4\uDF00-\uDF36\uDF40-\uDF43\uDF50-\uDF59\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDE40-\uDE7F\uDF00-\uDF4A\uDF4F-\uDF87\uDF8F-\uDF9F\uDFE0\uDFE1\uDFE3]|\uD821[\uDC00-\uDFF7]|\uD822[\uDC00-\uDEF2]|\uD82C[\uDC00-\uDD1E\uDD50-\uDD52\uDD64-\uDD67\uDD70-\uDEFB]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99\uDC9D\uDC9E]|\uD834[\uDD65-\uDD69\uDD6D-\uDD72\uDD7B-\uDD82\uDD85-\uDD8B\uDDAA-\uDDAD\uDE42-\uDE44]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB\uDFCE-\uDFFF]|\uD836[\uDE00-\uDE36\uDE3B-\uDE6C\uDE75\uDE84\uDE9B-\uDE9F\uDEA1-\uDEAF]|\uD838[\uDC00-\uDC06\uDC08-\uDC18\uDC1B-\uDC21\uDC23\uDC24\uDC26-\uDC2A\uDD00-\uDD2C\uDD30-\uDD3D\uDD40-\uDD49\uDD4E\uDEC0-\uDEF9]|\uD83A[\uDC00-\uDCC4\uDCD0-\uDCD6\uDD00-\uDD4B\uDD50-\uDD59]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDED6\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF34\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1\uDEB0-\uDFFF]|\uD87A[\uDC00-\uDFE0]|\uD87E[\uDC00-\uDE1D]|\uDB40[\uDD00-\uDDEF]/},R={fromCodePoint:t=>t<65536?String.fromCharCode(t):String.fromCharCode(55296+(t-65536>>10))+String.fromCharCode(56320+(t-65536&1023)),isWhiteSpace:t=>t===32||t===9||t===11||t===12||t===160||t>=5760&&[5760,8192,8193,8194,8195,8196,8197,8198,8199,8200,8201,8202,8239,8287,12288,65279].includes(t),isLineTerminator:t=>t===10||t===13||t===8232||t===8233,isIdentifierStart:t=>t===36||t===95||t>=65&&t<=90||t>=97&&t<=122||t===92||t>=128&&Gr.NonAsciiIdentifierStart.test(R.fromCodePoint(t)),isIdentifierPart:t=>t===36||t===95||t>=65&&t<=90||t>=97&&t<=122||t>=48&&t<=57||t===92||t>=128&&Gr.NonAsciiIdentifierPart.test(R.fromCodePoint(t)),isDecimalDigit:t=>t>=48&&t<=57,isHexDigit:t=>t>=48&&t<=57||t>=65&&t<=70||t>=97&&t<=102,isOctalDigit:t=>t>=48&&t<=55};function zr(t){return"0123456789abcdef".indexOf(t.toLowerCase())}function wn(t){return"01234567".indexOf(t)}const He=[[],[],[]];On.forEach(t=>He[t.length-1].push(t)),ki.forEach(t=>He[t.length-1].push(t)),Ti.forEach(t=>He[t.length-1].push(t)),Bi.forEach(t=>He[t.length-1].push(t)),zs.forEach(t=>He[t.length-1].push(t));let Xs=class{constructor(e,n){this.source=e,this.errorHandler=n,this._length=e.length,this.index=0,this.lineNumber=1,this.lineStart=0,this.curlyStack=[]}saveState(){return{index:this.index,lineNumber:this.lineNumber,lineStart:this.lineStart,curlyStack:this.curlyStack.slice()}}restoreState(e){this.index=e.index,this.lineNumber=e.lineNumber,this.lineStart=e.lineStart,this.curlyStack=e.curlyStack}eof(){return this.index>=this._length}throwUnexpectedToken(e=x.UnexpectedToken){this.errorHandler.throwError({code:e,index:this.index,line:this.lineNumber,column:this.index-this.lineStart+1})}tolerateUnexpectedToken(e=x.UnexpectedToken){this.errorHandler.tolerateError({code:e,index:this.index,line:this.lineNumber,column:this.index-this.lineStart+1})}skipSingleLineComment(e){const n=[],r=this.index-e,a={start:{line:this.lineNumber,column:this.index-this.lineStart-e},end:{line:0,column:0}};for(;!this.eof();){const u=this.source.charCodeAt(this.index);if(++this.index,R.isLineTerminator(u)){if(a){a.end={line:this.lineNumber,column:this.index-this.lineStart-1};const s={multiLine:!1,start:r+e,end:this.index-1,range:[r,this.index-1],loc:a};n.push(s)}return u===13&&this.source.charCodeAt(this.index)===10&&++this.index,++this.lineNumber,this.lineStart=this.index,n}}if(a){a.end={line:this.lineNumber,column:this.index-this.lineStart};const u={multiLine:!1,start:r+e,end:this.index,range:[r,this.index],loc:a};n.push(u)}return n}skipMultiLineComment(){const e=[],n=this.index-2,r={start:{line:this.lineNumber,column:this.index-this.lineStart-2},end:{line:0,column:0}};for(;!this.eof();){const a=this.source.charCodeAt(this.index);if(R.isLineTerminator(a))a===13&&this.source.charCodeAt(this.index+1)===10&&++this.index,++this.lineNumber,++this.index,this.lineStart=this.index;else if(a===42){if(this.source.charCodeAt(this.index+1)===47){if(this.index+=2,r){r.end={line:this.lineNumber,column:this.index-this.lineStart};const u={multiLine:!0,start:n+2,end:this.index-2,range:[n,this.index],loc:r};e.push(u)}return e}++this.index}else++this.index}if(r){r.end={line:this.lineNumber,column:this.index-this.lineStart};const a={multiLine:!0,start:n+2,end:this.index,range:[n,this.index],loc:r};e.push(a)}return this.tolerateUnexpectedToken(),e}scanComments(){let e=[];for(;!this.eof();){let n=this.source.charCodeAt(this.index);if(R.isWhiteSpace(n))++this.index;else if(R.isLineTerminator(n))++this.index,n===13&&this.source.charCodeAt(this.index)===10&&++this.index,++this.lineNumber,this.lineStart=this.index;else{if(n!==47)break;if(n=this.source.charCodeAt(this.index+1),n===47){this.index+=2;const r=this.skipSingleLineComment(2);e=[...e,...r]}else{if(n!==42)break;{this.index+=2;const r=this.skipMultiLineComment();e=[...e,...r]}}}}return e}isKeyword(e){switch((e=e.toLowerCase()).length){case 2:return e===$.If||e===$.In;case 3:return e===$.Var||e===$.For;case 4:return e===$.Else;case 5:return e===$.Break||e===$.While;case 6:return e===$.Return||e===$.Import||e===$.Export;case 8:return e===$.Function||e===$.Continue;default:return!1}}codePointAt(e){let n=this.source.charCodeAt(e);if(n>=55296&&n<=56319){const r=this.source.charCodeAt(e+1);r>=56320&&r<=57343&&(n=1024*(n-55296)+r-56320+65536)}return n}scanHexEscape(e){const n=e==="u"?4:2;let r=0;for(let a=0;a<n;++a){if(this.eof()||!R.isHexDigit(this.source.charCodeAt(this.index)))return null;r=16*r+zr(this.source[this.index++])}return String.fromCharCode(r)}scanUnicodeCodePointEscape(){let e=this.source[this.index],n=0;for(e==="}"&&this.throwUnexpectedToken();!this.eof()&&(e=this.source[this.index++],R.isHexDigit(e.charCodeAt(0)));)n=16*n+zr(e);return(n>1114111||e!=="}")&&this.throwUnexpectedToken(),R.fromCodePoint(n)}getIdentifier(){const e=this.index++;for(;!this.eof();){const n=this.source.charCodeAt(this.index);if(n===92)return this.index=e,this.getComplexIdentifier();if(n>=55296&&n<57343)return this.index=e,this.getComplexIdentifier();if(!R.isIdentifierPart(n))break;++this.index}return this.source.slice(e,this.index)}getComplexIdentifier(){let e,n=this.codePointAt(this.index),r=R.fromCodePoint(n);for(this.index+=r.length,n===92&&(this.source.charCodeAt(this.index)!==117&&this.throwUnexpectedToken(),++this.index,this.source[this.index]==="{"?(++this.index,e=this.scanUnicodeCodePointEscape()):(e=this.scanHexEscape("u"),e!==null&&e!=="\\"&&R.isIdentifierStart(e.charCodeAt(0))||this.throwUnexpectedToken()),r=e);!this.eof()&&(n=this.codePointAt(this.index),R.isIdentifierPart(n));)e=R.fromCodePoint(n),r+=e,this.index+=e.length,n===92&&(r=r.substring(0,r.length-1),this.source.charCodeAt(this.index)!==117&&this.throwUnexpectedToken(),++this.index,this.source[this.index]==="{"?(++this.index,e=this.scanUnicodeCodePointEscape()):(e=this.scanHexEscape("u"),e!==null&&e!=="\\"&&R.isIdentifierPart(e.charCodeAt(0))||this.throwUnexpectedToken()),r+=e);return r}octalToDecimal(e){let n=e!=="0",r=wn(e);return!this.eof()&&R.isOctalDigit(this.source.charCodeAt(this.index))&&(n=!0,r=8*r+wn(this.source[this.index++]),"0123".includes(e)&&!this.eof()&&R.isOctalDigit(this.source.charCodeAt(this.index))&&(r=8*r+wn(this.source[this.index++]))),{code:r,octal:n}}scanIdentifier(){let e;const n=this.index,r=this.source.charCodeAt(n)===92?this.getComplexIdentifier():this.getIdentifier();if(e=r.length===1?E.Identifier:this.isKeyword(r)?E.Keyword:r.toLowerCase()===$.Null?E.NullLiteral:r.toLowerCase()===$.True||r.toLowerCase()===$.False?E.BooleanLiteral:E.Identifier,e!==E.Identifier&&n+r.length!==this.index){const a=this.index;this.index=n,this.tolerateUnexpectedToken(x.InvalidEscapedReservedWord),this.index=a}return{type:e,value:r,lineNumber:this.lineNumber,lineStart:this.lineStart,start:n,end:this.index}}scanPunctuator(){const e=this.index;let n=this.source[this.index];switch(n){case"(":case"{":n==="{"&&this.curlyStack.push("{"),++this.index;break;case".":case")":case";":case",":case"[":case"]":case":":case"?":case"~":++this.index;break;case"}":++this.index,this.curlyStack.pop();break;default:for(let r=He.length;r>0;r--)if(n=this.source.substring(this.index,this.index+r),He[r-1].includes(n)){this.index+=r;break}}return this.index===e&&this.throwUnexpectedToken(),{type:E.Punctuator,value:n,lineNumber:this.lineNumber,lineStart:this.lineStart,start:e,end:this.index}}scanHexLiteral(e){let n="";for(;!this.eof()&&R.isHexDigit(this.source.charCodeAt(this.index));)n+=this.source[this.index++];return n.length===0&&this.throwUnexpectedToken(),R.isIdentifierStart(this.source.charCodeAt(this.index))&&this.throwUnexpectedToken(),{type:E.NumericLiteral,value:parseInt("0x"+n,16),lineNumber:this.lineNumber,lineStart:this.lineStart,start:e,end:this.index}}scanBinaryLiteral(e){let n="";for(;!this.eof();){const r=this.source[this.index];if(r!=="0"&&r!=="1")break;n+=this.source[this.index++]}if(n.length===0&&this.throwUnexpectedToken(),!this.eof()){const r=this.source.charCodeAt(this.index);(R.isIdentifierStart(r)||R.isDecimalDigit(r))&&this.throwUnexpectedToken()}return{type:E.NumericLiteral,value:parseInt(n,2),lineNumber:this.lineNumber,lineStart:this.lineStart,start:e,end:this.index}}scanOctalLiteral(e,n){let r="",a=!1;for(R.isOctalDigit(e.charCodeAt(0))?(a=!0,r="0"+this.source[this.index++]):++this.index;!this.eof()&&R.isOctalDigit(this.source.charCodeAt(this.index));)r+=this.source[this.index++];return a||r.length!==0||this.throwUnexpectedToken(),(R.isIdentifierStart(this.source.charCodeAt(this.index))||R.isDecimalDigit(this.source.charCodeAt(this.index)))&&this.throwUnexpectedToken(),{type:E.NumericLiteral,value:parseInt(r,8),lineNumber:this.lineNumber,lineStart:this.lineStart,start:n,end:this.index}}scanNumericLiteral(){const e=this.index;let n=this.source[e];Ur(R.isDecimalDigit(n.charCodeAt(0))||n===".","Numeric literal must start with a decimal digit or a decimal point");let r="";if(n!=="."){if(r=this.source[this.index++],n=this.source[this.index],r==="0"){if(n==="x"||n==="X")return++this.index,this.scanHexLiteral(e);if(n==="b"||n==="B")return++this.index,this.scanBinaryLiteral(e);if(n==="o"||n==="O")return this.scanOctalLiteral(n,e)}for(;R.isDecimalDigit(this.source.charCodeAt(this.index));)r+=this.source[this.index++];n=this.source[this.index]}if(n==="."){for(r+=this.source[this.index++];R.isDecimalDigit(this.source.charCodeAt(this.index));)r+=this.source[this.index++];n=this.source[this.index]}if(n==="e"||n==="E")if(r+=this.source[this.index++],n=this.source[this.index],n!=="+"&&n!=="-"||(r+=this.source[this.index++]),R.isDecimalDigit(this.source.charCodeAt(this.index)))for(;R.isDecimalDigit(this.source.charCodeAt(this.index));)r+=this.source[this.index++];else this.throwUnexpectedToken();return R.isIdentifierStart(this.source.charCodeAt(this.index))&&this.throwUnexpectedToken(),{type:E.NumericLiteral,value:parseFloat(r),lineNumber:this.lineNumber,lineStart:this.lineStart,start:e,end:this.index}}scanStringLiteral(){const e=this.index;let n=this.source[e];Ur(n==="'"||n==='"',"String literal must starts with a quote"),++this.index;let r=!1,a="";for(;!this.eof();){let u=this.source[this.index++];if(u===n){n="";break}if(u==="\\")if(u=this.source[this.index++],u&&R.isLineTerminator(u.charCodeAt(0)))++this.lineNumber,u==="\r"&&this.source[this.index]===`
`&&++this.index,this.lineStart=this.index;else switch(u){case"u":if(this.source[this.index]==="{")++this.index,a+=this.scanUnicodeCodePointEscape();else{const s=this.scanHexEscape(u);s===null&&this.throwUnexpectedToken(),a+=s}break;case"x":{const s=this.scanHexEscape(u);s===null&&this.throwUnexpectedToken(x.InvalidHexEscapeSequence),a+=s;break}case"n":a+=`
`;break;case"r":a+="\r";break;case"t":a+="	";break;case"b":a+="\b";break;case"f":a+="\f";break;case"v":a+="\v";break;case"8":case"9":a+=u,this.tolerateUnexpectedToken();break;default:if(u&&R.isOctalDigit(u.charCodeAt(0))){const s=this.octalToDecimal(u);r=s.octal||r,a+=String.fromCharCode(s.code)}else a+=u}else{if(R.isLineTerminator(u.charCodeAt(0)))break;a+=u}}return n!==""&&(this.index=e,this.throwUnexpectedToken()),{type:E.StringLiteral,value:a,lineNumber:this.lineNumber,lineStart:this.lineStart,start:e,end:this.index}}scanTemplate(){let e="",n=!1;const r=this.index,a=this.source[r]==="`";let u=!1,s=2;for(++this.index;!this.eof();){let i=this.source[this.index++];if(i==="`"){s=1,u=!0,n=!0;break}if(i!=="$")if(i!=="\\")R.isLineTerminator(i.charCodeAt(0))?(++this.lineNumber,i==="\r"&&this.source[this.index]===`
`&&++this.index,this.lineStart=this.index,e+=`
`):e+=i;else if(i=this.source[this.index++],R.isLineTerminator(i.charCodeAt(0)))++this.lineNumber,i==="\r"&&this.source[this.index]===`
`&&++this.index,this.lineStart=this.index;else switch(i){case"n":e+=`
`;break;case"r":e+="\r";break;case"t":e+="	";break;case"u":if(this.source[this.index]==="{")++this.index,e+=this.scanUnicodeCodePointEscape();else{const o=this.index,l=this.scanHexEscape(i);l!==null?e+=l:(this.index=o,e+=i)}break;case"x":{const o=this.scanHexEscape(i);o===null&&this.throwUnexpectedToken(x.InvalidHexEscapeSequence),e+=o;break}case"b":e+="\b";break;case"f":e+="\f";break;case"v":e+="\v";break;default:i==="0"?(R.isDecimalDigit(this.source.charCodeAt(this.index))&&this.throwUnexpectedToken(x.TemplateOctalLiteral),e+="\0"):R.isOctalDigit(i.charCodeAt(0))?this.throwUnexpectedToken(x.TemplateOctalLiteral):e+=i}else{if(this.source[this.index]==="{"){this.curlyStack.push("${"),++this.index,n=!0;break}e+=i}}return n||this.throwUnexpectedToken(),a||this.curlyStack.pop(),{type:E.Template,value:this.source.slice(r+1,this.index-s),cooked:e,head:a,tail:u,lineNumber:this.lineNumber,lineStart:this.lineStart,start:r,end:this.index}}lex(){if(this.eof())return{type:E.EOF,value:"",lineNumber:this.lineNumber,lineStart:this.lineStart,start:this.index,end:this.index};const e=this.source.charCodeAt(this.index);return R.isIdentifierStart(e)?this.scanIdentifier():e===40||e===41||e===59?this.scanPunctuator():e===39||e===34?this.scanStringLiteral():e===46?R.isDecimalDigit(this.source.charCodeAt(this.index+1))?this.scanNumericLiteral():this.scanPunctuator():R.isDecimalDigit(e)?this.scanNumericLiteral():e===96||e===125&&this.curlyStack[this.curlyStack.length-1]==="${"?this.scanTemplate():e>=55296&&e<57343&&R.isIdentifierStart(this.codePointAt(this.index))?this.scanIdentifier():this.scanPunctuator()}};var oe,bt;function qr(t,e=0){let n=t.start-t.lineStart,r=t.lineNumber;return n<0&&(n+=e,r--),{index:t.start,line:r,column:n}}function Jr(t){return[{index:t.range[0],...t.loc.start},{index:t.range[1],...t.loc.end}]}function Vr(t){return qs[t]??0}(function(t){t[t.None=0]="None",t[t.Function=1]="Function",t[t.IfClause=2]="IfClause",t[t.ForLoop=4]="ForLoop",t[t.WhileLoop=8]="WhileLoop"})(oe||(oe={})),function(t){t[t.AsObject=0]="AsObject",t[t.Automatic=1]="Automatic"}(bt||(bt={}));class Qs{constructor(e,n={},r){this.delegate=r,this.hasLineTerminator=!1,this.options={tokens:typeof n.tokens=="boolean"&&n.tokens,comments:typeof n.comments=="boolean"&&n.comments,tolerant:typeof n.tolerant=="boolean"&&n.tolerant},this.options.comments&&(this.commentHandler=new Ks),this.errorHandler=new Ys(this.options.tolerant),this.scanner=new Xs(e,this.errorHandler),this.context={isAssignmentTarget:!1,blockContext:oe.None,curlyParsingType:bt.AsObject},this.rawToken={type:E.EOF,value:"",lineNumber:this.scanner.lineNumber,lineStart:0,start:0,end:0},this.tokens=[],this.startMarker={index:0,line:this.scanner.lineNumber,column:0},this.endMarker={index:0,line:this.scanner.lineNumber,column:0},this.readNextRawToken(),this.endMarker={index:this.scanner.index,line:this.scanner.lineNumber,column:this.scanner.index-this.scanner.lineStart}}throwIfInvalidType(e,n,{validTypes:r,invalidTypes:a}){r!=null&&r.some(u=>e.type===u)||a!=null&&a.some(u=>e.type===u)&&this.throwError(x.InvalidExpression,n)}throwError(e,n,r=this.endMarker){const{index:a,line:u,column:s}=n,i=r.index-a-1;this.errorHandler.throwError({code:e,index:a,line:u,column:s+1,len:i})}tolerateError(e,n){throw new Error("######################################### !!!")}unexpectedTokenError(e={}){const{rawToken:n}=e;let r,{code:a,data:u}=e;if(n){if(!a)switch(n.type){case E.EOF:a=x.UnexpectedEndOfScript;break;case E.Identifier:a=x.UnexpectedIdentifier;break;case E.NumericLiteral:a=x.UnexpectedNumber;break;case E.StringLiteral:a=x.UnexpectedString;break;case E.Template:a=x.UnexpectedTemplate}r=n.value.toString()}else r="ILLEGAL";a=a??x.UnexpectedToken,u||(u={value:r});const s=Un(a,u);if(n){const l=n.start,m=n.lineNumber,f=n.start-n.lineStart+1;return new Ze({code:a,index:l,line:m,column:f,len:n.end-n.start-1,data:u,description:s})}const{index:i,line:o}=this.endMarker;return new Ze({code:a,index:i,line:o,column:this.endMarker.column+1,data:u,description:s})}throwUnexpectedToken(e={}){throw e.rawToken=e.rawToken??this.rawToken,this.unexpectedTokenError(e)}collectComments(e){const{commentHandler:n}=this;n&&e.length&&e.forEach(r=>{const a={type:r.multiLine?F.BlockComment:F.LineComment,value:this.getSourceValue(r),range:r.range,loc:r.loc};n.collectComment(a)})}peekAhead(e){const n=()=>(this.scanner.scanComments(),this.scanner.lex()),r=this.scanner.saveState(),a=e.call(this,n);return this.scanner.restoreState(r),a}getSourceValue(e){return this.scanner.source.slice(e.start,e.end)}convertToToken(e){return{type:Js[e.type],value:this.getSourceValue(e),range:[e.start,e.end],loc:{start:{line:this.startMarker.line,column:this.startMarker.column},end:{line:this.scanner.lineNumber,column:this.scanner.index-this.scanner.lineStart}}}}readNextRawToken(){this.endMarker.index=this.scanner.index,this.endMarker.line=this.scanner.lineNumber,this.endMarker.column=this.scanner.index-this.scanner.lineStart;const e=this.rawToken;this.collectComments(this.scanner.scanComments()),this.scanner.index!==this.startMarker.index&&(this.startMarker.index=this.scanner.index,this.startMarker.line=this.scanner.lineNumber,this.startMarker.column=this.scanner.index-this.scanner.lineStart),this.rawToken=this.scanner.lex(),this.hasLineTerminator=e.lineNumber!==this.rawToken.lineNumber,this.options.tokens&&this.rawToken.type!==E.EOF&&this.tokens.push(this.convertToToken(this.rawToken))}captureStartMarker(){return{index:this.startMarker.index,line:this.startMarker.line,column:this.startMarker.column}}getItemLocation(e){return{range:[e.index,this.endMarker.index],loc:{start:{line:e.line,column:e.column},end:{line:this.endMarker.line,column:this.endMarker.column}}}}finalize(e){var n,r;return(this.delegate||this.commentHandler)&&((n=this.commentHandler)==null||n.attachComments(e),(r=this.delegate)==null||r.call(this,e)),e}expectPunctuator(e){const n=this.rawToken;this.matchPunctuator(e)?this.readNextRawToken():this.throwUnexpectedToken({rawToken:n,code:x.PunctuatorExpected,data:{value:e}})}expectKeyword(e){this.rawToken.type!==E.Keyword||this.rawToken.value.toLowerCase()!==e?this.throwUnexpectedToken({rawToken:this.rawToken}):this.readNextRawToken()}expectContextualKeyword(e){this.rawToken.type!==E.Identifier||this.rawToken.value.toLowerCase()!==e?this.throwUnexpectedToken({rawToken:this.rawToken}):this.readNextRawToken()}matchKeyword(e){return this.rawToken.type===E.Keyword&&this.rawToken.value.toLowerCase()===e}matchContextualKeyword(e){return this.rawToken.type===E.Identifier&&this.rawToken.value===e}matchPunctuator(e){return this.rawToken.type===E.Punctuator&&this.rawToken.value===e}getMatchingPunctuator(e){if(typeof e=="string"&&(e=e.split("")),this.rawToken.type===E.Punctuator&&(e==null?void 0:e.length))return e.find(this.matchPunctuator,this)}isolateCoverGrammar(e){const n=this.context.isAssignmentTarget;this.context.isAssignmentTarget=!0;const r=e.call(this);return this.context.isAssignmentTarget=n,r}inheritCoverGrammar(e){const n=this.context.isAssignmentTarget;this.context.isAssignmentTarget=!0;const r=e.call(this);return this.context.isAssignmentTarget=this.context.isAssignmentTarget&&n,r}withBlockContext(e,n){const r=this.context.blockContext;this.context.blockContext=this.context.blockContext|e;const a=this.context.curlyParsingType;this.context.curlyParsingType=bt.Automatic;const u=n.call(this);return this.context.blockContext=r,this.context.curlyParsingType=a,u}consumeSemicolon(){if(this.matchPunctuator(";"))this.readNextRawToken();else if(!this.hasLineTerminator)return this.rawToken.type===E.EOF||this.matchPunctuator("}")?(this.endMarker.index=this.startMarker.index,this.endMarker.line=this.startMarker.line,void(this.endMarker.column=this.startMarker.column)):void this.throwUnexpectedToken({rawToken:this.rawToken})}parsePrimaryExpression(){const e=this.captureStartMarker(),n=this.rawToken;switch(n.type){case E.Identifier:return this.readNextRawToken(),this.finalize({type:F.Identifier,name:n.value,...this.getItemLocation(e)});case E.NumericLiteral:case E.StringLiteral:return this.context.isAssignmentTarget=!1,this.readNextRawToken(),this.finalize({type:F.Literal,value:n.value,raw:this.getSourceValue(n),isString:typeof n.value=="string",...this.getItemLocation(e)});case E.BooleanLiteral:return this.context.isAssignmentTarget=!1,this.readNextRawToken(),this.finalize({type:F.Literal,value:n.value.toLowerCase()===$.True,raw:this.getSourceValue(n),isString:!1,...this.getItemLocation(e)});case E.NullLiteral:return this.context.isAssignmentTarget=!1,this.readNextRawToken(),this.finalize({type:F.Literal,value:null,raw:this.getSourceValue(n),isString:!1,...this.getItemLocation(e)});case E.Template:return this.parseTemplateLiteral();case E.Punctuator:switch(n.value){case"(":return this.inheritCoverGrammar(this.parseGroupExpression);case"[":return this.inheritCoverGrammar(this.parseArrayInitializer);case"{":return this.inheritCoverGrammar(this.parseObjectExpression);default:return this.throwUnexpectedToken({rawToken:this.rawToken})}case E.Keyword:return this.context.isAssignmentTarget=!1,this.throwUnexpectedToken({rawToken:this.rawToken});default:return this.throwUnexpectedToken({rawToken:this.rawToken})}}parseArrayInitializer(){const e=this.captureStartMarker();this.expectPunctuator("[");const n=[];for(;!this.matchPunctuator("]");){const r=this.captureStartMarker();this.matchPunctuator(",")?(this.readNextRawToken(),this.throwError(x.InvalidExpression,r)):(n.push(this.inheritCoverGrammar(this.parseAssignmentExpression)),this.matchPunctuator("]")||this.expectPunctuator(","))}return this.expectPunctuator("]"),this.finalize({type:F.ArrayExpression,elements:n,...this.getItemLocation(e)})}parseObjectPropertyKey(){const e=this.captureStartMarker(),n=this.rawToken;switch(n.type){case E.StringLiteral:return this.readNextRawToken(),this.finalize({type:F.Literal,value:n.value,raw:this.getSourceValue(n),isString:!0,...this.getItemLocation(e)});case E.Identifier:case E.BooleanLiteral:case E.NullLiteral:case E.Keyword:return this.readNextRawToken(),this.finalize({type:F.Identifier,name:n.value,...this.getItemLocation(e)});default:this.throwError(x.KeyMustBeString,e)}}parseObjectProperty(){const e=this.rawToken,n=this.captureStartMarker(),r=this.parseObjectPropertyKey();let a=!1,u=null;return this.matchPunctuator(":")?(this.readNextRawToken(),u=this.inheritCoverGrammar(this.parseAssignmentExpression)):e.type===E.Identifier?(a=!0,u=this.finalize({type:F.Identifier,name:e.value,...this.getItemLocation(n)})):this.throwUnexpectedToken({rawToken:this.rawToken}),this.finalize({type:F.Property,kind:"init",key:r,value:u,shorthand:a,...this.getItemLocation(n)})}parseObjectExpression(){const e=this.captureStartMarker();this.expectPunctuator("{");const n=[];for(;!this.matchPunctuator("}");)n.push(this.parseObjectProperty()),this.matchPunctuator("}")||this.expectPunctuator(",");return this.expectPunctuator("}"),this.finalize({type:F.ObjectExpression,properties:n,...this.getItemLocation(e)})}parseTemplateElement(e=!1){const n=this.rawToken;n.type!==E.Template&&this.throwUnexpectedToken({rawToken:n}),e&&!n.head&&this.throwUnexpectedToken({code:x.InvalidTemplateHead,rawToken:n});const r=this.captureStartMarker();this.readNextRawToken();const{value:a,cooked:u,tail:s}=n,i=this.finalize({type:F.TemplateElement,value:{raw:a,cooked:u},tail:s,...this.getItemLocation(r)});return i.loc.start.column++,i.loc.end.column=i.loc.end.column-(s?1:2),i}parseTemplateLiteral(){const e=this.captureStartMarker(),n=[],r=[];let a=this.parseTemplateElement(!0);for(r.push(a);!a.tail;)n.push(this.parseExpression()),a=this.parseTemplateElement(),r.push(a);return this.finalize({type:F.TemplateLiteral,quasis:r,expressions:n,...this.getItemLocation(e)})}parseGroupExpression(){this.expectPunctuator("(");const e=this.inheritCoverGrammar(this.parseAssignmentExpression);return this.expectPunctuator(")"),e}parseArguments(){this.expectPunctuator("(");const e=[];if(!this.matchPunctuator(")"))for(;;){const n=this.isolateCoverGrammar(this.parseAssignmentExpression);if(e.push(n),this.matchPunctuator(")")||(this.expectPunctuator(","),this.matchPunctuator(")")))break}return this.expectPunctuator(")"),e}parseMemberName(){const e=this.rawToken,n=this.captureStartMarker();return this.readNextRawToken(),e.type!==E.NullLiteral&&e.type!==E.Identifier&&e.type!==E.Keyword&&e.type!==E.BooleanLiteral&&this.throwUnexpectedToken({rawToken:e}),this.finalize({type:F.Identifier,name:e.value,...this.getItemLocation(n)})}parseLeftHandSideExpression(){const e=this.captureStartMarker();let n=this.inheritCoverGrammar(this.parsePrimaryExpression);const r=this.captureStartMarker();let a;for(;a=this.getMatchingPunctuator("([.");)switch(a){case"(":{this.context.isAssignmentTarget=!1,n.type!==F.Identifier&&n.type!==F.MemberExpression&&this.throwError(x.IdentiferExpected,e,r);const u=this.parseArguments();n=this.finalize({type:F.CallExpression,callee:n,arguments:u,...this.getItemLocation(e)});continue}case"[":{this.context.isAssignmentTarget=!0,this.expectPunctuator("[");const u=this.isolateCoverGrammar(this.parseExpression);this.expectPunctuator("]"),n=this.finalize({type:F.MemberExpression,computed:!0,object:n,property:u,...this.getItemLocation(e)});continue}case".":{this.context.isAssignmentTarget=!0,this.expectPunctuator(".");const u=this.parseMemberName();n=this.finalize({type:F.MemberExpression,computed:!1,object:n,property:u,...this.getItemLocation(e)});continue}}return n}parseUpdateExpression(){const e=this.captureStartMarker();let n=this.getMatchingPunctuator(On);if(n){this.readNextRawToken();const s=this.captureStartMarker(),i=this.inheritCoverGrammar(this.parseUnaryExpression);return i.type!==F.Identifier&&i.type!==F.MemberExpression&&i.type!==F.CallExpression&&this.throwError(x.InvalidExpression,s),this.context.isAssignmentTarget||this.tolerateError(x.InvalidLeftHandSideInAssignment,e),this.context.isAssignmentTarget=!1,this.finalize({type:F.UpdateExpression,operator:n,argument:i,prefix:!0,...this.getItemLocation(e)})}const r=this.captureStartMarker(),a=this.inheritCoverGrammar(this.parseLeftHandSideExpression),u=this.captureStartMarker();return this.hasLineTerminator?a:(n=this.getMatchingPunctuator(On),n?(a.type!==F.Identifier&&a.type!==F.MemberExpression&&this.throwError(x.InvalidExpression,r,u),this.context.isAssignmentTarget||this.tolerateError(x.InvalidLeftHandSideInAssignment,e),this.readNextRawToken(),this.context.isAssignmentTarget=!1,this.finalize({type:F.UpdateExpression,operator:n,argument:a,prefix:!1,...this.getItemLocation(e)})):a)}parseUnaryExpression(){const e=this.getMatchingPunctuator(ki);if(e){const n=this.captureStartMarker();this.readNextRawToken();const r=this.inheritCoverGrammar(this.parseUnaryExpression);return this.context.isAssignmentTarget=!1,this.finalize({type:F.UnaryExpression,operator:e,argument:r,prefix:!0,...this.getItemLocation(n)})}return this.parseUpdateExpression()}parseBinaryExpression(){const e=this.rawToken;let n=this.inheritCoverGrammar(this.parseUnaryExpression);if(this.rawToken.type!==E.Punctuator)return n;const r=this.rawToken.value;let a=Vr(r);if(a===0)return n;this.readNextRawToken(),this.context.isAssignmentTarget=!1;const u=[e,this.rawToken];let s=n,i=this.inheritCoverGrammar(this.parseUnaryExpression);const o=[s,r,i],l=[a];for(;this.rawToken.type===E.Punctuator&&(a=Vr(this.rawToken.value))>0;){for(;o.length>2&&a<=l[l.length-1];){i=o.pop();const p=o.pop();l.pop(),s=o.pop(),u.pop();const g=u[u.length-1],w=qr(g,g.lineStart);o.push(this.finalize(this.createBinaryOrLogicalExpression(w,p,s,i)))}o.push(this.rawToken.value),l.push(a),u.push(this.rawToken),this.readNextRawToken(),o.push(this.inheritCoverGrammar(this.parseUnaryExpression))}let m=o.length-1;n=o[m];let f=u.pop();for(;m>1;){const p=u.pop();if(!p)break;const g=f==null?void 0:f.lineStart,w=qr(p,g),B=o[m-1];n=this.finalize(this.createBinaryOrLogicalExpression(w,B,o[m-2],n)),m-=2,f=p}return n}createBinaryOrLogicalExpression(e,n,r,a){const u=Ti.includes(n)?F.LogicalExpression:F.BinaryExpression;return u===F.BinaryExpression||(r.type!==F.AssignmentExpression&&r.type!==F.UpdateExpression||this.throwError(x.InvalidExpression,...Jr(r)),a.type!==F.AssignmentExpression&&a.type!==F.UpdateExpression||this.throwError(x.InvalidExpression,...Jr(r))),{type:u,operator:n,left:r,right:a,...this.getItemLocation(e)}}parseAssignmentExpression(){const e=this.captureStartMarker(),n=this.inheritCoverGrammar(this.parseBinaryExpression),r=this.captureStartMarker(),a=this.getMatchingPunctuator(Bi);if(!a)return n;n.type!==F.Identifier&&n.type!==F.MemberExpression&&this.throwError(x.InvalidExpression,e,r),this.context.isAssignmentTarget||this.tolerateError(x.InvalidLeftHandSideInAssignment,e),this.matchPunctuator("=")||(this.context.isAssignmentTarget=!1),this.readNextRawToken();const u=this.isolateCoverGrammar(this.parseAssignmentExpression);return this.finalize({type:F.AssignmentExpression,left:n,operator:a,right:u,...this.getItemLocation(e)})}parseExpression(){return this.isolateCoverGrammar(this.parseAssignmentExpression)}parseStatements(e){const n=[];for(;this.rawToken.type!==E.EOF&&!this.matchPunctuator(e);){const r=this.parseStatementListItem();js(r)||n.push(r)}return n}parseStatementListItem(){return this.context.isAssignmentTarget=!0,this.matchKeyword($.Function)?this.parseFunctionDeclaration():this.matchKeyword($.Export)?this.parseExportDeclaration():this.matchKeyword($.Import)?this.parseImportDeclaration():this.parseStatement()}parseBlock(){const e=this.captureStartMarker();this.expectPunctuator("{");const n=this.parseStatements("}");return this.expectPunctuator("}"),this.finalize({type:F.BlockStatement,body:n,...this.getItemLocation(e)})}parseObjectStatement(){const e=this.captureStartMarker(),n=this.parseObjectExpression();return this.finalize({type:F.ExpressionStatement,expression:n,...this.getItemLocation(e)})}parseBlockOrObjectStatement(){return this.context.curlyParsingType===bt.AsObject?this.parseObjectStatement():this.peekAhead(e=>{let n=e();return(n.type===E.Identifier||n.type===E.StringLiteral)&&(n=e(),n.type===E.Punctuator&&n.value===":")})?this.parseObjectStatement():this.parseBlock()}parseIdentifier(){const e=this.rawToken;if(e.type!==E.Identifier)return null;const n=this.captureStartMarker();return this.readNextRawToken(),this.finalize({type:F.Identifier,name:e.value,...this.getItemLocation(n)})}parseVariableDeclarator(){const e=this.captureStartMarker(),n=this.parseIdentifier();n||this.throwUnexpectedToken({code:x.IdentiferExpected});let r=null;if(this.matchPunctuator("=")){this.readNextRawToken();const a=this.rawToken;try{r=this.isolateCoverGrammar(this.parseAssignmentExpression)}catch{this.throwUnexpectedToken({rawToken:a,code:x.InvalidVariableAssignment})}}return this.finalize({type:F.VariableDeclarator,id:n,init:r,...this.getItemLocation(e)})}parseVariableDeclarationList(){const e=[this.parseVariableDeclarator()];for(;this.matchPunctuator(",");)this.readNextRawToken(),e.push(this.parseVariableDeclarator());return e}parseVariableDeclaration(){const e=this.captureStartMarker();this.expectKeyword($.Var);const n=this.parseVariableDeclarationList();return this.consumeSemicolon(),this.finalize({type:F.VariableDeclaration,declarations:n,kind:"var",...this.getItemLocation(e)})}parseEmptyStatement(){const e=this.captureStartMarker();return this.expectPunctuator(";"),this.finalize({type:F.EmptyStatement,...this.getItemLocation(e)})}parseExpressionStatement(){const e=this.captureStartMarker(),n=this.parseExpression();return this.consumeSemicolon(),this.finalize({type:F.ExpressionStatement,expression:n,...this.getItemLocation(e)})}parseIfClause(){return this.withBlockContext(oe.IfClause,this.parseStatement)}parseIfStatement(){const e=this.captureStartMarker();this.expectKeyword($.If),this.expectPunctuator("(");const n=this.captureStartMarker(),r=this.parseExpression(),a=this.captureStartMarker();this.expectPunctuator(")"),r.type!==F.AssignmentExpression&&r.type!==F.UpdateExpression||this.throwError(x.InvalidExpression,n,a);const u=this.parseIfClause();let s=null;return this.matchKeyword($.Else)&&(this.readNextRawToken(),s=this.parseIfClause()),this.finalize({type:F.IfStatement,test:r,consequent:u,alternate:s,...this.getItemLocation(e)})}parseWhileStatement(){const e=this.captureStartMarker();this.expectKeyword($.While),this.expectPunctuator("(");const n=this.captureStartMarker(),r=this.parseExpression(),a=this.captureStartMarker();this.expectPunctuator(")"),r.type!==F.AssignmentExpression&&r.type!==F.UpdateExpression||this.throwError(x.InvalidExpression,n,a);const u=this.withBlockContext(oe.WhileLoop,this.parseStatement);return this.finalize({type:F.WhileStatement,test:r,body:u,...this.getItemLocation(e)})}parseForStatement(){let e=null,n=null,r=null,a=null,u=null;const s=this.captureStartMarker();if(this.expectKeyword($.For),this.expectPunctuator("("),this.matchPunctuator(";"))this.readNextRawToken();else if(this.matchKeyword($.Var)){const o=this.captureStartMarker();this.readNextRawToken();const l=this.parseVariableDeclarationList();l.length===1&&this.matchKeyword($.In)?(l[0].init&&this.throwError(x.ForInOfLoopInitializer,o),a=this.finalize({type:F.VariableDeclaration,declarations:l,kind:"var",...this.getItemLocation(o)}),this.readNextRawToken(),u=this.parseExpression()):(this.matchKeyword($.In)&&this.throwError(x.InvalidLeftHandSideInForIn,o),e=this.finalize({type:F.VariableDeclaration,declarations:l,kind:"var",...this.getItemLocation(o)}),this.expectPunctuator(";"))}else{const o=this.context.isAssignmentTarget,l=this.captureStartMarker();e=this.inheritCoverGrammar(this.parseAssignmentExpression),this.matchKeyword($.In)?(this.context.isAssignmentTarget||this.tolerateError(x.InvalidLeftHandSideInForIn,l),e.type!==F.Identifier&&this.throwError(x.InvalidLeftHandSideInForIn,l),this.readNextRawToken(),a=e,u=this.parseExpression(),e=null):(this.context.isAssignmentTarget=o,this.expectPunctuator(";"))}a||(this.matchPunctuator(";")||(n=this.isolateCoverGrammar(this.parseExpression)),this.expectPunctuator(";"),this.matchPunctuator(")")||(r=this.isolateCoverGrammar(this.parseExpression))),this.expectPunctuator(")");const i=this.withBlockContext(oe.ForLoop,()=>this.isolateCoverGrammar(this.parseStatement));return a&&u?this.finalize({type:F.ForInStatement,left:a,right:u,body:i,...this.getItemLocation(s)}):this.finalize({type:F.ForStatement,init:e,test:n,update:r,body:i,...this.getItemLocation(s)})}parseContinueStatement(){const e=this.captureStartMarker();return this.expectKeyword($.Continue),this.consumeSemicolon(),this.finalize({type:F.ContinueStatement,...this.getItemLocation(e)})}parseBreakStatement(){const e=this.captureStartMarker();return this.expectKeyword($.Break),this.consumeSemicolon(),this.finalize({type:F.BreakStatement,...this.getItemLocation(e)})}parseReturnStatement(){const e=this.captureStartMarker();this.expectKeyword($.Return);const n=!this.matchPunctuator(";")&&!this.matchPunctuator("}")&&!this.hasLineTerminator&&this.rawToken.type!==E.EOF||this.rawToken.type===E.StringLiteral||this.rawToken.type===E.Template?this.parseExpression():null;return this.consumeSemicolon(),this.finalize({type:F.ReturnStatement,argument:n,...this.getItemLocation(e)})}parseStatement(){switch(this.rawToken.type){case E.BooleanLiteral:case E.NullLiteral:case E.NumericLiteral:case E.StringLiteral:case E.Template:case E.Identifier:return this.parseExpressionStatement();case E.Punctuator:return this.rawToken.value==="{"?this.parseBlockOrObjectStatement():this.rawToken.value==="("?this.parseExpressionStatement():this.rawToken.value===";"?this.parseEmptyStatement():this.parseExpressionStatement();case E.Keyword:switch(this.rawToken.value.toLowerCase()){case $.Break:return this.parseBreakStatement();case $.Continue:return this.parseContinueStatement();case $.For:return this.parseForStatement();case $.Function:return this.parseFunctionDeclaration();case $.If:return this.parseIfStatement();case $.Return:return this.parseReturnStatement();case $.Var:return this.parseVariableDeclaration();case $.While:return this.parseWhileStatement();default:return this.parseExpressionStatement()}default:return this.throwUnexpectedToken({rawToken:this.rawToken})}}parseFormalParameters(){const e=[];if(this.expectPunctuator("("),!this.matchPunctuator(")"))for(;this.rawToken.type!==E.EOF;){const n=this.parseIdentifier();if(n||this.throwUnexpectedToken({rawToken:this.rawToken,code:x.IdentiferExpected}),e.push(n),this.matchPunctuator(")")||(this.expectPunctuator(","),this.matchPunctuator(")")))break}return this.expectPunctuator(")"),e}parseFunctionDeclaration(){(this.context.blockContext&oe.Function)===oe.Function&&this.throwUnexpectedToken({code:x.NoFunctionInsideFunction}),(this.context.blockContext&oe.WhileLoop)!==oe.WhileLoop&&(this.context.blockContext&oe.IfClause)!==oe.IfClause||this.throwUnexpectedToken({code:x.NoFunctionInsideBlock});const e=this.captureStartMarker();this.expectKeyword($.Function);const n=this.parseIdentifier();n||this.throwUnexpectedToken({code:x.InvalidFunctionIdentifier});const r=this.parseFormalParameters(),a=this.context.blockContext;this.context.blockContext=this.context.blockContext|oe.Function;const u=this.parseBlock();return this.context.blockContext=a,this.finalize({type:F.FunctionDeclaration,id:n,params:r,body:u,...this.getItemLocation(e)})}parseScript(){const e=this.captureStartMarker(),n=this.parseStatements(),r=this.finalize({type:F.Program,body:n,...this.getItemLocation(e)});return this.options.tokens&&(r.tokens=this.tokens),this.options.tolerant&&(r.errors=this.errorHandler.errors),r}parseExportDeclaration(){this.context.blockContext!==oe.None&&this.throwUnexpectedToken({code:x.ModuleExportRootOnly});let e=null;const n=this.captureStartMarker();return this.expectKeyword($.Export),this.matchKeyword($.Var)?e=this.parseVariableDeclaration():this.matchKeyword("function")?e=this.parseFunctionDeclaration():this.throwUnexpectedToken({code:x.InvalidExpression}),this.finalize({type:F.ExportNamedDeclaration,declaration:e,specifiers:[],source:null,...this.getItemLocation(n)})}parseModuleSpecifier(){const e=this.captureStartMarker(),n=this.rawToken;if(n.type===E.StringLiteral)return this.readNextRawToken(),this.finalize({type:F.Literal,value:n.value,raw:this.getSourceValue(n),isString:!0,...this.getItemLocation(e)});this.throwError(x.InvalidModuleUri,e)}parseDefaultSpecifier(){const e=this.captureStartMarker(),n=this.parseIdentifier();return n||this.throwUnexpectedToken({code:x.IdentiferExpected}),this.finalize({type:F.ImportDefaultSpecifier,local:n,...this.getItemLocation(e)})}parseImportDeclaration(){this.context.blockContext!==oe.None&&this.throwUnexpectedToken({code:x.ModuleImportRootOnly});const e=this.captureStartMarker();this.expectKeyword($.Import);const n=this.parseDefaultSpecifier();this.expectContextualKeyword($.From);const r=this.parseModuleSpecifier();return this.finalize({type:F.ImportDeclaration,specifiers:[n],source:r,...this.getItemLocation(e)})}}function eu(t,e,n){return new Qs(t,e,n).parseScript()}function Vt(t,e=[]){const n=eu(t);if(n.body===null||n.body===void 0)throw new Ze({index:0,line:0,column:0,data:null,description:"",code:x.InvalidExpression});if(n.body.length===0)throw new Ze({index:0,line:0,column:0,data:null,description:"",code:x.InvalidExpression});if(n.body.length===0)throw new Ze({index:0,line:0,column:0,data:null,description:"",code:x.InvalidExpression});return n.loadedModules={},Bt(n,e),n}class Ht{constructor(e){const n=this;n._keys=[],n._values=[],n.length=0,e&&e.forEach(r=>{n.set(r[0],r[1])})}entries(){return[].slice.call(this.keys().map((e,n)=>[e,this._values[n]]))}keys(){return[].slice.call(this._keys)}values(){return[].slice.call(this._values)}has(e){return this._keys.includes(e)}get(e){const n=this._keys.indexOf(e);return n>-1?this._values[n]:null}deepGet(e){if(!e||!e.length)return null;const n=(r,a)=>r==null?null:a.length?n(r instanceof Ht?r.get(a[0]):r[a[0]],a.slice(1)):r;return n(this.get(e[0]),e.slice(1))}set(e,n){const r=this,a=this._keys.indexOf(e);return a>-1?r._values[a]=n:(r._keys.push(e),r._values.push(n),r.length=r._values.length),this}sortedSet(e,n,r,a){const u=this,s=this._keys.length,i=r||0,o=a!==void 0?a:s-1;if(s===0)return u._keys.push(e),u._values.push(n),u;if(e===this._keys[i])return this._values.splice(i,0,n),this;if(e===this._keys[o])return this._values.splice(o,0,n),this;if(e>this._keys[o])return this._keys.splice(o+1,0,e),this._values.splice(o+1,0,n),this;if(e<this._keys[i])return this._values.splice(i,0,n),this._keys.splice(i,0,e),this;if(i>=o)return this;const l=i+Math.floor((o-i)/2);return e<this._keys[l]?this.sortedSet(e,n,i,l-1):e>this._keys[l]?this.sortedSet(e,n,l+1,o):this}size(){return this.length}clear(){const e=this;return e._keys.length=e.length=e._values.length=0,this}delete(e){const n=this,r=n._keys.indexOf(e);return r>-1&&(n._keys.splice(r,1),n._values.splice(r,1),n.length=n._keys.length,!0)}forEach(e){this._keys.forEach((n,r)=>{e(this._values[r],n,r)})}map(e){return this.keys().map((n,r)=>e(this._values[r],n,r))}filter(e){const n=this;return n._keys.forEach((r,a)=>{e(n._values[a],r,a)===!1&&n.delete(r)}),this}clone(){return new Ht(this.entries())}}class Hr{constructor(e=20){this._maxEntries=e,this._values=new Ht}delete(e){this._values.has(e)&&this._values.delete(e)}get(e){let n=null;return this._values.has(e)&&(n=this._values.get(e),this._values.delete(e),this._values.set(e,n)),n}put(e,n){if(this._values.size()>=this._maxEntries){const r=this._values.keys()[0];this._values.delete(r)}this._values.set(e,n)}}class tu{constructor(e=20){this._maxEntries=e,this._cache=new Hr(this._maxEntries)}clear(){this._cache=new Hr(this._maxEntries)}addToCache(e,n){this._cache.put(e,n)}removeFromCache(e){this._cache.delete(e)}getFromCache(e){return this._cache.get(e)}}class ce{constructor(e){this.portalUri=e}normalizeModuleUri(e){const n=/^[a-z0-9A-Z]+(@[0-9]+\.[0-9]+\.[0-9]+)?([\?|\/].*)?$/gi,r=/(?<portalurl>.+)\/home\/item\.html\?id\=(?<itemid>.+)$/gi,a=/(?<portalurl>.+)\/sharing\/rest\/content\/users\/[a-zA-Z0-9]+\/items\/(?<itemid>.+)$/gi,u=/(?<portalurl>.+)\/sharing\/rest\/content\/items\/(?<itemid>.+)$/gi,s=/(?<itemid>.*)@(?<versionstring>[0-9]+\.[0-9]+\.[0-9]+)([\?|\/].*)?$/gi;if(e.startsWith("portal+")){let i=e.substring(7),o="",l=i,m=!1;for(const g of[r,u,a]){const w=g.exec(i);if(w!==null){const B=w.groups;l=B.itemid,o=B.portalurl,m=!0;break}}if(m===!1){if(!n.test(i))throw new un(on.UnsupportedUriProtocol,{uri:e});l=i,o=this.portalUri}l.includes("/")&&(l=l.split("/")[0]),l.includes("?")&&(l=l.split("?")[0]);let f="current";const p=s.exec(l);if(p!==null){const g=p.groups;l=g.itemid,f=g.versionstring}return i=new Wr({url:o}).restUrl+"/content/items/"+l+"/resources/"+f+".arc",{url:i,scheme:"portal",uri:"PO:"+i}}if(e.startsWith("mock")){if(e==="mock")return{url:"",scheme:"mock",data:`
      export var hello = 1;
      export function helloWorld() {
          return "Hello World " + hello;
      }
  `,uri:"mock"};const i=e.replace("mock:","");if(ce.mocks[i]!==void 0)return{url:"",scheme:"mock",data:ce.mocks[i],uri:e}}throw new un(on.UnrecognisedUri,{uri:e})}async fetchModule(e){const n=ce.cachedModules.getFromCache(e.uri);if(n)return n;const r=this.fetchSource(e);ce.cachedModules.addToCache(e.uri,r);let a=null;try{a=await r}catch(u){throw ce.cachedModules.removeFromCache(e.uri),u}return a}async fetchSource(e){if(e.scheme==="portal"){const n=await ha(e.url,{responseType:"text",query:{}});if(n.data)return Vt(n.data,[])}if(e.scheme==="mock")return Vt(e.data??"",[]);throw new un(on.UnsupportedUriProtocol)}static create(e){return new ce(e)}static getDefault(){return this._default??(ce._default=ce._moduleResolverFactory())}static set moduleResolverClass(e){this._moduleResolverFactory=e,this._default=null}}ce.mocks={},ce.cachedModules=new tu(30),ce._default=null,ce._moduleResolverFactory=()=>{const t=Wr.getDefault();return new ce(t.url)};let nu=class extends je{constructor(e,n){super(),this.definition=null,this.context=null,this.definition=e,this.context=n}createFunction(e){return(...n)=>{const r={spatialReference:this.context.spatialReference,console:this.context.console,timeReference:this.context.timeReference?this.context.timeReference:null,lrucache:this.context.lrucache,exports:this.context.exports,libraryResolver:this.context.libraryResolver,interceptor:this.context.interceptor,localScope:{},depthCounter:{depth:e.depthCounter.depth+1},globalScope:this.context.globalScope};if(r.depthCounter.depth>64)throw new h(e,c.MaximumCallDepth,null);return xn(this.definition,r,n,null)}}call(e,n){return $e(e,n,(r,a,u)=>{const s={spatialReference:e.spatialReference,globalScope:e.globalScope,depthCounter:{depth:e.depthCounter.depth+1},libraryResolver:e.libraryResolver,exports:e.exports,timeReference:e.timeReference??null,console:e.console,lrucache:e.lrucache,interceptor:e.interceptor,localScope:{}};if(s.depthCounter.depth>64)throw new h(e,c.MaximumCallDepth,n);return xn(this.definition,s,u,n)})}marshalledCall(e,n,r,a){return a(e,n,(u,s,i)=>{const o={spatialReference:e.spatialReference,globalScope:r.globalScope,depthCounter:{depth:e.depthCounter.depth+1},libraryResolver:e.libraryResolver,exports:e.exports,console:e.console,timeReference:e.timeReference??null,lrucache:e.lrucache,interceptor:e.interceptor,localScope:{}};return i=i.map(l=>!K(l)||l instanceof Le?l:ot(l,e,a)),ot(xn(this.definition,o,i,n),r,a)})}},mt=class extends Zt{constructor(e){super(e)}global(e){const n=this.executingContext.globalScope[e.toLowerCase()];if(n.valueset||(n.value=v(this.executingContext,n.node),n.valueset=!0),K(n.value)&&!(n.value instanceof Le)){const r=new Le;r.fn=n.value,r.parameterEvaluator=$e,r.context=this.executingContext,n.value=r}return n.value}setGlobal(e,n){if(K(n))throw new h(null,c.AssignModuleFunction,null);this.executingContext.globalScope[e.toLowerCase()]={value:n,valueset:!0,node:null}}hasGlobal(e){return this.executingContext.exports[e]===void 0&&(e=e.toLowerCase()),this.executingContext.exports[e]!==void 0}loadModule(e){let n=e.spatialReference;n==null&&(n=new kt({wkid:102100})),this.moduleScope=Li({},e.customfunctions,e.timeReference),this.executingContext={spatialReference:n,globalScope:this.moduleScope,localScope:null,libraryResolver:new tn(e.libraryResolver._moduleSingletons,this.source.syntax.loadedModules),exports:{},console:e.console?e.console:Ri,timeReference:e.timeReference??null,lrucache:e.lrucache,interceptor:e.interceptor,depthCounter:{depth:1}},v(this.executingContext,this.source.syntax)}};function ru(t,e){const n=[];for(let r=0;r<e.arguments.length;r++)n.push(v(t,e.arguments[r]));return n}function $e(t,e,n){try{return e.preparsed===!0?n(t,null,e.arguments):n(t,e,ru(t,e))}catch(r){throw r}}function v(t,e){try{switch(e==null?void 0:e.type){case"EmptyStatement":return C;case"VariableDeclarator":return xu(t,e);case"VariableDeclaration":return wu(t,e);case"ImportDeclaration":return gu(t,e);case"ExportNamedDeclaration":return yu(t,e);case"BlockStatement":case"Program":return mu(t,e);case"FunctionDeclaration":return Du(t,e);case"ReturnStatement":return pu(t,e);case"IfStatement":return du(t,e);case"ExpressionStatement":return fu(t,e);case"AssignmentExpression":return hu(t,e);case"UpdateExpression":return cu(t,e);case"BreakStatement":return Ae;case"ContinueStatement":return pt;case"TemplateElement":return Su(t,e);case"TemplateLiteral":return Iu(t,e);case"ForStatement":return uu(t,e);case"ForInStatement":return su(t,e);case"WhileStatement":return ou(t,e);case"Identifier":return Mi(t,e);case"MemberExpression":return Fu(t,e);case"Literal":return e.value;case"CallExpression":return ku(t,e);case"UnaryExpression":return Cu(t,e);case"BinaryExpression":return Eu(t,e);case"LogicalExpression":return bu(t,e);case"ArrayExpression":return Au(t,e);case"ObjectExpression":return iu(t,e);case"Property":return au(t,e);default:throw new h(t,c.Unrecognised,e)}}catch(n){throw sa(t,e,n)}}function iu(t,e){const n={},r=new Map;for(let u=0;u<e.properties.length;u++){const s=v(t,e.properties[u]);if(K(s.value))throw new h(t,c.NoFunctionInDictionary,e);if(b(s.key)===!1)throw new h(t,c.KeyMustBeString,e);let i=s.key.toString();const o=i.toLowerCase();r.has(o)?i=r.get(o):r.set(o,i),s.value===C?n[i]=null:n[i]=s.value}const a=new _(n);return a.immutable=!1,a}function au(t,e){return{key:e.key.type==="Identifier"?e.key.name:v(t,e.key),value:v(t,e.value)}}function su(t,e){const n=v(t,e.right);e.left.type==="VariableDeclaration"&&v(t,e.left);let r=null,a="";if(e.left.type==="VariableDeclaration"){const u=e.left.declarations[0].id;u.type==="Identifier"&&(a=u.name)}else e.left.type==="Identifier"&&(a=e.left.name);if(!a)throw new h(t,c.InvalidIdentifier,e);if(a=a.toLowerCase(),t.localScope!=null&&t.localScope[a]!==void 0&&(r=t.localScope[a]),r===null&&t.globalScope[a]!==void 0&&(r=t.globalScope[a]),r===null)throw new h(t,c.InvalidIdentifier,e);if(k(n)||b(n)){const u=n.length;for(let s=0;s<u;s++){r.value=s;const i=v(t,e.body);if(i===Ae)break;if(i instanceof me)return i}return C}if(N(n)){for(let u=0;u<n.length();u++){r.value=u;const s=v(t,e.body);if(s===Ae)break;if(s instanceof me)return s}return C}if(!(n instanceof _||Q(n)))return C;{const u=n.keys();for(let s=0;s<u.length;s++){r.value=u[s];const i=v(t,e.body);if(i===Ae)break;if(i instanceof me)return i}}}function uu(t,e){e.init!==null&&v(t,e.init);const n={testResult:!0,lastAction:C};do lu(t,e,n);while(n.testResult===!0);return n.lastAction instanceof me?n.lastAction:C}function ou(t,e){const n={testResult:!0,lastAction:C};if(n.testResult=v(t,e.test),n.testResult===!1)return C;if(n.testResult!==!0)throw new h(t,c.BooleanConditionRequired,e);for(;n.testResult===!0&&(n.lastAction=v(t,e.body),n.lastAction!==Ae)&&!(n.lastAction instanceof me);)if(n.testResult=v(t,e.test),n.testResult!==!0&&n.testResult!==!1)throw new h(t,c.BooleanConditionRequired,e);return n.lastAction instanceof me?n.lastAction:C}function lu(t,e,n){if(e.test!==null){if(n.testResult=v(t,e.test),n.testResult===!1)return;if(n.testResult!==!0)throw new h(t,c.BooleanConditionRequired,e)}n.lastAction=v(t,e.body),n.lastAction!==Ae?n.lastAction instanceof me?n.testResult=!1:e.update!==null&&v(t,e.update):n.testResult=!1}function cu(t,e){let n,r=null,a="";if(e.argument.type==="MemberExpression"){if(r=v(t,e.argument.object),e.argument.computed===!0?a=v(t,e.argument.property):e.argument.property.type==="Identifier"&&(a=e.argument.property.name),k(r)){if(!G(a))throw new h(t,c.ArrayAccessorMustBeNumber,e);if(a<0&&(a=r.length+a),a<0||a>=r.length)throw new h(t,c.OutOfBounds,e);n=d(r[a]),r[a]=e.operator==="++"?n+1:n-1}else if(r instanceof _){if(b(a)===!1)throw new h(t,c.KeyAccessorMustBeString,e);if(r.hasField(a)!==!0)throw new h(t,c.FieldNotFound,e);n=d(r.field(a)),r.setField(a,e.operator==="++"?n+1:n-1)}else if(Q(r)){if(b(a)===!1)throw new h(t,c.KeyAccessorMustBeString,e);if(r.hasField(a)!==!0)throw new h(t,c.FieldNotFound,e);n=d(r.field(a)),r.setField(a,e.operator==="++"?n+1:n-1)}else{if(N(r))throw new h(t,c.Immutable,e);if(!(r instanceof mt))throw new h(t,c.InvalidParameter,e);if(b(a)===!1)throw new h(t,c.ModuleAccessorMustBeString,e);if(r.hasGlobal(a)!==!0)throw new h(t,c.ModuleExportNotFound,e);n=d(r.global(a)),r.setGlobal(a,e.operator==="++"?n+1:n-1)}return e.prefix===!1?n:e.operator==="++"?n+1:n-1}if(r=e.argument.type==="Identifier"?e.argument.name.toLowerCase():"",!r)throw new h(t,c.InvalidIdentifier,e);if(t.localScope!=null&&t.localScope[r]!==void 0)return n=d(t.localScope[r].value),t.localScope[r]={value:e.operator==="++"?n+1:n-1,valueset:!0,node:e},e.prefix===!1?n:e.operator==="++"?n+1:n-1;if(t.globalScope[r]!==void 0)return n=d(t.globalScope[r].value),t.globalScope[r]={value:e.operator==="++"?n+1:n-1,valueset:!0,node:e},e.prefix===!1?n:e.operator==="++"?n+1:n-1;throw new h(t,c.InvalidIdentifier,e)}function Te(t,e,n,r,a){switch(e){case"=":return t===C?null:t;case"/=":return d(n)/d(t);case"*=":return d(n)*d(t);case"-=":return d(n)-d(t);case"+=":return b(n)||b(t)?A(n)+A(t):d(n)+d(t);case"%=":return d(n)%d(t);default:throw new h(a,c.UnsupportedOperator,r)}}function hu(t,e){let n=null,r="";if(e.left.type==="MemberExpression"){if(n=v(t,e.left.object),e.left.computed===!0)r=v(t,e.left.property);else{if(e.left.property.type!=="Identifier")throw new h(t,c.InvalidIdentifier,e);r=e.left.property.name}const u=v(t,e.right);if(k(n)){if(!G(r))throw new h(t,c.ArrayAccessorMustBeNumber,e);if(r<0&&(r=n.length+r),r<0||r>n.length)throw new h(t,c.OutOfBounds,e);if(r===n.length){if(e.operator!=="=")throw new h(t,c.OutOfBounds,e);n[r]=Te(u,e.operator,n[r],e,t)}else n[r]=Te(u,e.operator,n[r],e,t)}else if(n instanceof _){if(b(r)===!1)throw new h(t,c.KeyAccessorMustBeString,e);if(n.hasField(r)===!0)n.setField(r,Te(u,e.operator,n.field(r),e,t));else{if(e.operator!=="=")throw new h(t,c.FieldNotFound,e,{key:r});n.setField(r,Te(u,e.operator,null,e,t))}}else if(Q(n)){if(b(r)===!1)throw new h(t,c.KeyAccessorMustBeString,e);if(n.hasField(r)===!0)n.setField(r,Te(u,e.operator,n.field(r),e,t));else{if(e.operator!=="=")throw new h(t,c.FieldNotFound,e,{key:r});n.setField(r,Te(u,e.operator,null,e,t))}}else{if(N(n))throw new h(t,c.Immutable,e);if(!(n instanceof mt))throw new h(t,c.InvalidIdentifier,e);if(b(r)===!1)throw new h(t,c.ModuleAccessorMustBeString,e);if(n.hasGlobal(r)!==!0)throw new h(t,c.ModuleExportNotFound,e);n.setGlobal(r,Te(u,e.operator,n.global(r),e,t))}return C}n=e.left.name.toLowerCase();const a=v(t,e.right);if(t.localScope!=null&&t.localScope[n]!==void 0)return t.localScope[n]={value:Te(a,e.operator,t.localScope[n].value,e,t),valueset:!0,node:e.right},C;if(t.globalScope[n]!==void 0)return t.globalScope[n]={value:Te(a,e.operator,t.globalScope[n].value,e,t),valueset:!0,node:e.right},C;throw new h(t,c.InvalidIdentifier,e)}function fu(t,e){if(e.expression.type==="AssignmentExpression"||e.expression.type==="UpdateExpression")return v(t,e.expression);if(e.expression.type==="CallExpression"){const n=v(t,e.expression);return n===C?C:new lt(n)}{const n=v(t,e.expression);return n===C?C:new lt(n)}}function du(t,e){const n=v(t,e.test);if(n===!0)return v(t,e.consequent);if(n===!1)return e.alternate!==null?v(t,e.alternate):C;throw new h(t,c.BooleanConditionRequired,e)}function mu(t,e){let n=C;for(let r=0;r<e.body.length;r++)if(n=v(t,e.body[r]),n instanceof me||n===Ae||n===pt)return n;return n}function pu(t,e){if(e.argument===null)return new me(C);const n=v(t,e.argument);return new me(n)}function Du(t,e){const n=e.id.name.toLowerCase();return t.globalScope[n]={valueset:!0,node:null,value:new nu(e,t)},C}function gu(t,e){var u,s;const n=e.specifiers[0].local.name.toLowerCase(),r=t.libraryResolver.loadLibrary(n);let a=null;return(u=t.libraryResolver._moduleSingletons)!=null&&u.has(r.uri)?a=t.libraryResolver._moduleSingletons.get(r.uri):(a=new mt(r),a.loadModule(t),(s=t.libraryResolver._moduleSingletons)==null||s.set(r.uri,a)),t.globalScope[n]={value:a,valueset:!0,node:e},C}function yu(t,e){if(v(t,e.declaration),e.declaration.type==="FunctionDeclaration")t.exports[e.declaration.id.name.toLowerCase()]="function";else if(e.declaration.type==="VariableDeclaration")for(const n of e.declaration.declarations)t.exports[n.id.name.toLowerCase()]="variable";return C}function wu(t,e){for(let n=0;n<e.declarations.length;n++)v(t,e.declarations[n]);return C}function xu(t,e){let n=e.init===null?null:v(t,e.init);if(n===C&&(n=null),e.id.type!=="Identifier")throw new h(t,c.InvalidIdentifier,e);const r=e.id.name.toLowerCase();return t.localScope!=null?t.localScope[r]={value:n,valueset:!0,node:e.init}:t.globalScope[r]={value:n,valueset:!0,node:e.init},C}function Fu(t,e){try{const n=v(t,e.object);if(n===null)throw new h(t,c.MemberOfNull,e);if(e.computed===!1){if(e.property.type==="Identifier"){if(n instanceof _||Q(n))return n.field(e.property.name);if(n instanceof T)return vt(n,e.property.name,e,t);if(n instanceof mt){if(!n.hasGlobal(e.property.name))throw new h(t,c.InvalidIdentifier,e);return n.global(e.property.name)}}throw new h(t,c.InvalidMemberAccessKey,e)}{let r=v(t,e.property);if(n instanceof _||Q(n)){if(b(r))return n.field(r);throw new h(t,c.InvalidMemberAccessKey,e)}if(n instanceof mt){if(b(r))return n.global(r);throw new h(t,c.InvalidMemberAccessKey,e)}if(n instanceof T){if(b(r))return vt(n,r,e,t);throw new h(t,c.InvalidMemberAccessKey,e)}if(k(n)){if(G(r)&&isFinite(r)&&Math.floor(r)===r){if(r<0&&(r=n.length+r),r>=n.length||r<0)throw new h(t,c.OutOfBounds,e);return n[r]}throw new h(t,c.InvalidMemberAccessKey,e)}if(b(n)){if(G(r)&&isFinite(r)&&Math.floor(r)===r){if(r<0&&(r=n.length+r),r>=n.length||r<0)throw new h(t,c.OutOfBounds,e);return n[r]}throw new h(t,c.InvalidMemberAccessKey,e)}if(N(n)){if(G(r)&&isFinite(r)&&Math.floor(r)===r){if(r<0&&(r=n.length()+r),r>=n.length()||r<0)throw new h(t,c.OutOfBounds,e);return n.get(r)}throw new h(t,c.InvalidMemberAccessKey,e)}throw new h(t,c.InvalidMemberAccessKey,e)}}catch(n){throw n}}function Cu(t,e){try{const n=v(t,e.argument);if(U(n)){if(e.operator==="!")return!n;if(e.operator==="-")return-1*d(n);if(e.operator==="+")return 1*d(n);if(e.operator==="~")return~d(n);throw new h(t,c.UnsupportedUnaryOperator,e)}if(e.operator==="~")return~d(n);if(e.operator==="-")return-1*d(n);if(e.operator==="+")return 1*d(n);throw new h(t,c.UnsupportedUnaryOperator,e)}catch(n){throw n}}function Au(t,e){try{const n=[];for(let r=0;r<e.elements.length;r++){const a=v(t,e.elements[r]);if(K(a))throw new h(t,c.NoFunctionInArray,e);a===C?n.push(null):n.push(a)}return n}catch(n){throw n}}function Eu(t,e){try{const n=[v(t,e.left),v(t,e.right)],r=n[0],a=n[1];switch(e.operator){case"|":case"<<":case">>":case">>>":case"^":case"&":return jn(d(r),d(a),e.operator);case"==":return xe(r,a);case"!=":return!xe(r,a);case"<":case">":case"<=":case">=":return Zn(r,a,e.operator);case"+":return b(r)||b(a)?A(r)+A(a):d(r)+d(a);case"-":return d(r)-d(a);case"*":return d(r)*d(a);case"/":return d(r)/d(a);case"%":return d(r)%d(a);default:throw new h(t,c.UnsupportedOperator,e)}}catch(n){throw n}}function bu(t,e){try{const n=v(t,e.left);if(U(n))switch(e.operator){case"||":if(n===!0)return n;{const r=v(t,e.right);if(U(r))return r;throw new h(t,c.LogicExpressionOrAnd,e)}case"&&":if(n===!1)return n;{const r=v(t,e.right);if(U(r))return r;throw new h(t,c.LogicExpressionOrAnd,e)}default:throw new h(t,c.LogicExpressionOrAnd,e)}throw new h(t,c.LogicalExpressionOnlyBoolean,e)}catch(n){throw n}}function Su(t,e){return e.value?e.value.cooked:""}function vu(t,e,n){if(K(t))throw new h(e,c.NoFunctionInTemplateLiteral,n);return t}function Iu(t,e){let n="",r=0;for(const a of e.quasis)n+=a.value?a.value.cooked:"",a.tail===!1&&(n+=e.expressions[r]?A(vu(v(t,e.expressions[r]),t,e)):"",r++);return n}function Mi(t,e){let n;try{const r=e.name.toLowerCase();if(t.localScope!=null&&t.localScope[r]!==void 0)return n=t.localScope[r],n.valueset===!0||(n.value=v(t,n.node),n.valueset=!0),n.value;if(t.globalScope[r]!==void 0)return n=t.globalScope[r],n.valueset===!0||(n.value=v(t,n.node),n.valueset=!0),n.value;throw new h(t,c.InvalidIdentifier,e)}catch(r){throw r}}function ku(t,e){try{if(e.callee.type==="MemberExpression"){const n=v(t,e.callee.object);if(!(n instanceof mt))throw new h(t,c.FuncionNotFound,e);const r=e.callee.computed===!1?e.callee.property.name:v(t,e.callee.property);if(!n.hasGlobal(r))throw new h(t,c.FuncionNotFound,e);const a=n.global(r);if(!K(a))throw new h(t,c.CallNonFunction,e);return a.call(t,e)}if(e.callee.type!=="Identifier")throw new h(t,c.FuncionNotFound,e);if(t.localScope!=null&&t.localScope[e.callee.name.toLowerCase()]!==void 0){const n=t.localScope[e.callee.name.toLowerCase()];if(K(n.value))return n.value.call(t,e);throw new h(t,c.CallNonFunction,e)}if(t.globalScope[e.callee.name.toLowerCase()]!==void 0){const n=t.globalScope[e.callee.name.toLowerCase()];if(K(n.value))return n.value.call(t,e);throw new h(t,c.CallNonFunction,e)}throw new h(t,c.FuncionNotFound,e)}catch(n){throw n}}const le={};function $i(t,e,n,r){try{const a=v(t,e.arguments[n]);if(xe(a,r))return v(t,e.arguments[n+1]);{const u=e.arguments.length-n;return u===1?v(t,e.arguments[n]):u===2?null:u===3?v(t,e.arguments[n+2]):$i(t,e,n+2,r)}}catch(a){throw a}}function Ni(t,e,n,r){try{if(r===!0)return v(t,e.arguments[n+1]);if(e.arguments.length-n===3)return v(t,e.arguments[n+2]);{const a=v(t,e.arguments[n+2]);if(U(a)===!1)throw new h(t,c.BooleanConditionRequired,e.arguments[n+2]);return Ni(t,e,n+2,a)}}catch(a){throw a}}function xn(t,e,n,r){try{const a=t.body;if(n.length!==t.params.length)throw new h(e,c.WrongNumberOfParameters,r);if(e.localScope!=null)for(let s=0;s<n.length;s++)e.localScope[t.params[s].name.toLowerCase()]={value:n[s],valueset:!0,node:null};const u=v(e,a);if(u instanceof me)return u.value;if(u===Ae)throw new h(e,c.UnexpectedToken,r);if(u===pt)throw new h(e,c.UnexpectedToken,r);return u instanceof lt?u.value:u}catch(a){throw a}}di(le,$e),Ai(le,$e),yi(le,$e),gi(le,$e),xi(le,$e),Kn(le,$e),le.iif=function(t,e){try{D(e.arguments===null?[]:e.arguments,3,3,t,e);const n=v(t,e.arguments[0]);if(U(n)===!1)throw new h(t,c.BooleanConditionRequired,e);return v(t,n===!0?e.arguments[1]:e.arguments[2])}catch(n){throw n}},le.decode=function(t,e){try{if(e.arguments.length<2)throw new h(t,c.WrongNumberOfParameters,e);if(e.arguments.length===2)return v(t,e.arguments[1]);{if((e.arguments.length-1)%2==0)throw new h(t,c.WrongNumberOfParameters,e);const n=v(t,e.arguments[0]);return $i(t,e,1,n)}}catch(n){throw n}},le.when=function(t,e){try{if(e.arguments.length<3)throw new h(t,c.WrongNumberOfParameters,e);if(e.arguments.length%2==0)throw new h(t,c.WrongNumberOfParameters,e);const n=v(t,e.arguments[0]);if(U(n)===!1)throw new h(t,c.BooleanConditionRequired,e.arguments[0]);return Ni(t,e,0,n)}catch(n){throw n}};for(const t in le)le[t]={value:new We(le[t]),valueset:!0,node:null};const St=function(){};function Li(t,e,n){const r=new St;t||(t={}),e||(e={});const a=new _({newline:`
`,tab:"	",singlequote:"'",doublequote:'"',forwardslash:"/",backwardslash:"\\"});a.immutable=!1,r.textformatting={value:a,valueset:!0,node:null};for(const u in e)r[u]={value:new We(e[u]),native:!0,valueset:!0,node:null};for(const u in t)t[u]&&t[u].declaredClass==="esri.Graphic"?r[u]={value:X.createFromGraphic(t[u],n),valueset:!0,node:null}:r[u]={value:t[u],valueset:!0,node:null};return r}St.prototype=le,St.prototype.infinity={value:Number.POSITIVE_INFINITY,valueset:!0,node:null},St.prototype.pi={value:Math.PI,valueset:!0,node:null};function Ri(t){console.log(t)}function Pi(t){const e={mode:"sync",compiled:!1,functions:{},signatures:[],standardFunction:$e,evaluateIdentifier:Mi};for(let n=0;n<t.length;n++)t[n].registerFunctions(e);for(const n in e.functions)le[n]={value:new We(e.functions[n]),valueset:!0,node:null},St.prototype[n]=le[n];for(let n=0;n<e.signatures.length;n++)Bn(e.signatures[n],"sync")}function Gn(t,e){let n=e.spatialReference;n==null&&(n=new kt({wkid:102100}));let r=null;t.usesModules&&(r=new tn(new Map,t.loadedModules));const a={spatialReference:n,globalScope:Li(e.vars,e.customfunctions,e.timeReference),localScope:null,exports:{},libraryResolver:r,console:e.console?e.console:Ri,timeReference:e.timeReference??null,lrucache:e.lrucache,interceptor:e.interceptor,depthCounter:{depth:1}};let u=v(a,t);if(u instanceof me&&(u=u.value),u instanceof lt&&(u=u.value),u===C&&(u=null),u===Ae)throw new h(a,c.IllegalResult,null);if(u===pt)throw new h(a,c.IllegalResult,null);if(K(u))throw new h(a,c.IllegalResult,null);return u}Pi([_n]);const Bu=["feature","angle","bearing","centroid","envelopeintersects","extent","geometry","isselfintersecting","ringisclockwise"];function Tu(){return!0}const _u=Tu();let Oi=!1,Ui=!1,Ee=null,zn=[];function Gi(t,e){if(e.useAsync===!0||t.isAsync===!0)return Mu(t,e);if(Yr("esri-csp-restrictions"))return function(n){return Gn(t,n)};try{return Ii(t,e)}catch(n){if(n.declaredRootClass==="esri.arcade.arcadeuncompilableerror")return function(r){return Gn(t,r)};throw n}}function Mu(t,e){if(Ee===null)throw new h(null,c.AsyncNotEnabled,null);if(Yr("esri-csp-restrictions")||_u===!1)return function(n){return Ee.executeScript(t,n)};try{return Ii(t,e,!0)}catch(n){if(n.declaredRootClass==="esri.arcade.arcadeuncompilableerror")return function(r){return Ee.executeScript(t,r)};throw n}}function $u(t){Pi(t),Ke(t,"sync"),Ee===null?zn.push(t):(Ke(t,"async"),Ee.extend(t))}function er(t,e=[]){return Vt(t,e)}function Nu(t,e,n=[]){return tr(Vt(t,n),e)}function tr(t,e){if(e.useAsync===!0||t.isAsync===!0){if(Ee===null)throw new h(null,c.AsyncNotEnabled,null);return Ee.executeScript(t,e)}return Gn(t,e)}function nn(t,e){return hi(t,e)}function Lu(t,e){return wa(t,e)}function Ru(t,e=!1){return e===void 0&&(e=!1),xa(t)}function zi(t){return Fa(t)}function rn(t,e=[]){return t.usesGeometry===void 0&&Bt(t,e),t.usesGeometry===!0}let Fn=null;function nr(){return Fn||(Fn=qi(),Fn)}async function qi(){const[t,e]=await Promise.all([Ne(()=>import("./geometryEngine-OGzB5MRq.js"),__vite__mapDeps([12,13,14,5,2,3,4,6,1])),Ne(()=>Promise.resolve().then(()=>Ua),void 0)]);return Ui=!0,e.setGeometryEngine(t),!0}let Cn=null;function rr(){return Cn!==null||(Cn=Ji()),Cn}async function Ji(){await Us(),Ee=await Ne(()=>import("./arcadeAsyncRuntime-DZV1z6mf.js"),__vite__mapDeps([15,8,0,1,2,3,4,5,6,7,9,10,11]));for(const t of zn)Ee.extend(t),Ke(t,"async");return zn=null,!0}function Vi(){return Oi}function Hi(){return!!Ee}function Zi(){return Ui}let An=null;function ir(){return An||(An=ji(),An)}async function ji(){await rr();const[t,e,n,r,a]=await Promise.all([Ne(()=>import("./featureSetUtils-CBB3vR3p.js").then(u=>u.i),__vite__mapDeps([16,1,2,3,4,17,8,5,6,18,10,7,19])),Ne(()=>import("./featuresetbase-B-e-df0j.js"),__vite__mapDeps([20,10,8,5,2,3,4,6,1,16,17,18,7,19,9,11])),Ne(()=>import("./featuresetgeom-DuF4cn4O.js"),__vite__mapDeps([21,8,18,17,2,3,4,5,6,1,10,7,11])),Ne(()=>import("./featuresetstats-O0zjoCUA.js"),__vite__mapDeps([22,8,17,2,3,4,5,6,1,10,11])),Ne(()=>import("./featuresetstring-DacevaUZ.js"),__vite__mapDeps([23,8,5,2,3,4,6,1,10,11]))]);return Qi=t,Ee.extend([e,n,r,a]),Ke([e,n,r,a],"async"),Oi=!0,!0}function Wi(t,e=[]){return t.usesFeatureSet===void 0&&Bt(t,e),t.usesFeatureSet===!0}function Pu(t,e=[]){return t.isAsync===void 0&&Bt(t,e),t.isAsync===!0}function Ou(t,e){if(e){for(const n of e)if(nn(t,n))return!0;return!1}return!1}async function Ki(t,e,n=[],r=!1,a=null){return ar(new Set,t,e,n,r,a)}async function ar(t,e,n,r=[],a=!1,u=null){const s=typeof e=="string"?er(e):e,i=[];return s&&(Zi()===!1&&(rn(s)||a)&&i.push(nr()),Hi()===!1&&(s.isAsync===!0||n)&&i.push(rr()),Vi()===!1&&(Wi(s)||Ou(s,r))&&i.push(ir())),i.length&&await Promise.all(i),await Yi(t,s,u,n,a),!0}function Uu(t,e=[]){return t.usesModules===void 0&&Bt(t,e),t.usesModules===!0}async function Yi(t,e,n=null,r=!1,a=!1){const u=fi(e);n===null&&u.length>0&&(n=ce.getDefault()),e.loadedModules={};for(const s of u){Kr(n);const i=n.normalizeModuleUri(s.source);if(t.has(i.uri))throw new h(null,c.CircularModules,null);t.add(i.uri);const o=await n.fetchModule(i);await ar(t,o,r,[],a,n),t.delete(i.uri),o.isAsync&&(e.isAsync=!0),o.usesFeatureSet&&(e.usesFeatureSet=!0),o.usesGeometry&&(e.usesGeometry=!0),e.loadedModules[s.libname]={uri:i.uri,script:o}}}function Xi(t){if(rn(t))return!0;const e=Tn(t);let n=!1;for(let r=0;r<e.length;r++)if(Bu.includes(e[r])){n=!0;break}return n}let Qi=null;function an(){return Qi}const Gu=Object.freeze(Object.defineProperty({__proto__:null,_loadScriptDependenciesImpl:ar,compileScript:Gi,enableAsyncSupport:rr,enableAsyncSupportImpl:Ji,enableFeatureSetSupport:ir,enableFeatureSetSupportImpl:ji,enableGeometrySupport:nr,enableGeometrySupportImpl:qi,executeScript:tr,extend:$u,extractExpectedFieldLiterals:zi,extractFieldLiterals:Ru,featureSetUtils:an,isAsyncEnabled:Hi,isFeatureSetSupportEnabled:Vi,isGeometryEnabled:Zi,loadDependentModules:Yi,loadScriptDependencies:Ki,parseAndExecuteScript:Nu,parseScript:er,referencesFunction:Lu,referencesMember:nn,scriptIsAsync:Pu,scriptTouchesGeometry:Xi,scriptUsesFeatureSet:Wi,scriptUsesGeometryEngine:rn,scriptUsesModules:Uu},Symbol.toStringTag,{value:"Module"})),Zr=/^\$(feature|aggregatedFeatures)\./i,zu={vars:{$feature:"any",$view:"any"},spatialReference:null};function qu(t){return t.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&")}function Ju(t){return t==null?null:k(t)||N(t)?"array":j(t)?"date":b(t)?"text":U(t)?"boolean":G(t)?"number":t instanceof _?"dictionary":Q(t)?"feature":t instanceof H?"point":t instanceof ae?"polygon":t instanceof ie?"polyline":t instanceof we?"multipoint":t instanceof he?"extent":jt(t)?"featureSet":Wt(t)?"featureSetCollection":null}function Tt(t){if(!t)return null;try{return er(t)}catch{}return null}function Vu(t,e){const n=typeof t=="string"?Tt(t):t;if(!n)return null;try{return e=e||ia(zu),Gi(n,e)}catch{}return null}function Hu(t,e){return{vars:{$feature:t==null?new X:X.createFromGraphic(t,null),$view:e&&e.view},spatialReference:e&&e.sr}}function Zu(t,e,n){return X.createFromGraphicLikeObject(e,t,n,null)}function ju(t,e){t.vars!=null&&(t.vars.$feature=e)}function Wu(t,e){let n;try{n=tr(t,e)}catch{n=null}return n}function Ku(t,e){let n;try{n=t?t(e):null}catch{n=null}return n}function Yu(t,e){try{return t?t(e):Promise.resolve(null)}catch{return Promise.resolve(null)}}function Xu(t,e){if(!t)return[];const n=typeof t=="string"?Tt(t):t;if(!n)return[];const r=zi(n);let a=new Array;r.forEach(s=>{Zr.test(s)&&(s=s.replace(Zr,""),a.push(s))});const u=a.filter(s=>s.includes("*"));return a=a.filter(s=>!u.includes(s)),e&&u.forEach(s=>{const i=new RegExp(`^${s.split(/\*+/).map(qu).join(".*")}$`,"i");e.forEach(o=>i.test(o)?a.push(o):null)}),[...new Set(a.sort())]}function Qu(t){return nn(t,"$view")}function eo(t,e){return!!t&&nn(t,e)}function to(t){if(!(!t||t.spatialReference==null&&(t.scale==null||t.viewingMode==null)))return{view:t.viewingMode&&t.scale!=null?new _({viewingMode:t.viewingMode,scale:t.scale}):null,sr:t.spatialReference}}function no({url:t,spatialReference:e,lrucache:n,interceptor:r}){const a=an();return a?a.createFeatureSetCollectionFromService(t,e,n,r):null}function ro({layer:t,spatialReference:e,outFields:n,returnGeometry:r,lrucache:a,interceptor:u}){if(t===null)return null;const s=an();return s?s.constructFeatureSet(t,e,n,r??!0,a,u):null}function io(t){if((t==null?void 0:t.map)===null)return null;const e=an();return e?e.createFeatureSetCollectionFromMap(t.map,t.spatialReference,t.lrucache,t.interceptor):null}function ao(t,e){return _.convertJsonToArcade(t,e)}function so(t,e,n=[]){return Ki(t,e,n)}function uo(){return nr()}function oo(){return ir()}function lo(t){return t.type==="simple"||t.type==="class-breaks"||t.type==="unique-value"||t.type==="dot-density"||t.type==="dictionary"||t.type==="pie-chart"}function co(t){return t.declaredClass==="esri.layers.support.LabelClass"}function ho(t){return t.declaredClass==="esri.PopupTemplate"}function ea(t,e){var r;if(!t)return!1;if(typeof t=="string")return e(t);const n=t;if(lo(n)){if(n.type==="dot-density"){const s=(r=n.attributes)==null?void 0:r.some(i=>e(i.valueExpression));if(s)return s}const a=n.visualVariables,u=!!a&&a.some(s=>{let i=e(s.valueExpression);return s.type==="size"&&(ur(s.minSize)&&(i=i||e(s.minSize.valueExpression)),ur(s.maxSize)&&(i=i||e(s.maxSize.valueExpression))),i});return!(!("valueExpression"in n)||!e(n.valueExpression))||u}if(co(n)){const a=n.labelExpressionInfo&&n.labelExpressionInfo.expression;return!(!a||!e(a))||!1}return!!ho(n)&&(!!n.expressionInfos&&n.expressionInfos.some(a=>e(a.expression))||Array.isArray(n.content)&&n.content.some(a=>{var u;return a.type==="expression"&&e((u=a.expressionInfo)==null?void 0:u.expression)}))}function fo(t){const e=Tt(t);return!!e&&Xi(e)}function mo(t){return ea(t,fo)}function po(t){const e=Tt(t);return!!e&&rn(e)}function Do(t){return ea(t,po)}const tl=Object.freeze(Object.defineProperty({__proto__:null,Dictionary:_,arcade:Gu,arcadeFeature:X,convertFeatureLayerToFeatureSet:ro,convertJsonToArcade:ao,convertMapToFeatureSetCollection:io,convertServiceUrlToWorkspace:no,createExecContext:Hu,createFeature:Zu,createFunction:Vu,createSyntaxTree:Tt,dependsOnView:Qu,enableFeatureSetOperations:oo,enableGeometryOperations:uo,evalSyntaxTree:Wu,executeAsyncFunction:Yu,executeFunction:Ku,extractFieldNames:Xu,getArcadeType:Ju,getViewInfo:to,hasGeometryFunctions:mo,hasGeometryOperations:Do,hasVariable:eo,loadScriptDependencies:so,updateExecContext:ju},Symbol.toStringTag,{value:"Module"}));export{se as $,me as A,D as B,K as C,Ae as D,re as E,pt as F,jt as G,Ct as H,Lt as I,k as J,ct as K,U as L,Ta as M,yi as N,_a as O,Mn as P,P as Q,C as R,In as S,di as T,j as U,N as V,va as W,Dt as X,G as Y,Ai as Z,Y as _,gi as a,ma as a0,Ge as a1,ko as a2,hn as a3,qo as a4,si as a5,Yt as a6,Rt as a7,ui as a8,ii as a9,oi as aa,ai as ab,pe as ac,li as ad,Dr as ae,hr as af,dr as ag,mr as ah,To as ai,Bo as aj,_o as ak,Ro as al,fr as am,Mo as an,No as ao,fa as ap,pr as aq,$o as ar,pa as as,Lo as at,Oo as au,Po as av,qa as aw,tl as ax,_ as b,jn as c,ee as d,We as e,xi as f,X as g,je as h,Le as i,lt as j,ot as k,d as l,_n as m,Zn as n,Bn as o,Zt as p,vt as q,A as r,tn as s,xe as t,O as u,b as v,Fe as w,$t as x,M as y,Q as z};
