import{E as w,j as a,i as y,e as l,y as c,a as I}from"./Point-WxyopZva.js";import{l as V}from"./widget-BcWKanF2.js";import"./index-r0dFAfgr.js";import{i as v,bB as H,bC as T,bD as q,bs as g}from"./MapView-DaoQedLH.js";import"./pe-B8dP0-Ut.js";import"./Rasterizer-CuAuGNQK.js";import"./Container-BwXq1a-x.js";import"./BufferPool-BAwXXd5w.js";import"./enums-L38xj_2E.js";import"./WGLContainer-Dyx9110G.js";import"./vec4f32-CjrfB-0a.js";import"./color-DAS1c3my.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./ProgramTemplate-tdUBoAol.js";import"./MaterialKey-BYd7cMLJ.js";import"./utils-DPUVnAXL.js";import"./FramebufferObject-8j9PRuxE.js";import"./number-CoJp78Rz.js";import"./StyleDefinition-Bnnz5uyC.js";import"./enums-BRzLM11V.js";import"./MagnifierPrograms-txlcEObf.js";import"./OrderIndependentTransparency-C5Ap76ew.js";import"./floatRGBA-PQQNbO39.js";import"./webgl-debug-BJuvLAW9.js";import{a as b}from"./GraphicsView2D-DDTEO9AX.js";import"./AttributeStoreView-B0-phoCE.js";import"./earcut-BJup91r2.js";import"./vec3f32-nZdmKIgz.js";import{t as U,o as f,n as d}from"./imageUtils-BPtoKHto.js";import{f as Q,u as S}from"./LayerView-BSt9B8Gh.js";import{n as C}from"./HighlightGraphicContainer-B4wkFrY6.js";import{i as k}from"./RefreshableLayerView-DUeNHzrW.js";import{P as F,S as P}from"./MapServiceLayerViewHelper-Cc5aApGi.js";import{a as E}from"./drapedUtils-DJwxIB1g.js";import"./_commonjsHelpers-DCkdB7M8.js";import"./cimAnalyzer-CMgqZsaO.js";import"./fontUtils-BuXIMW9g.js";import"./BidiEngine-CsUYIMdL.js";import"./GeometryUtils-B7ExOJII.js";import"./enums-B5k73o5q.js";import"./alignmentUtils-CkNI7z7C.js";import"./definitions-826PWLuy.js";import"./Rect-CUzevAry.js";import"./callExpressionWithFeature-DgtD4TSq.js";import"./quantizationUtils-DtI9CsYu.js";import"./rasterizingUtils-BGZonnNf.js";import"./VertexElementDescriptor-BOD-G50G.js";import"./config-MDUrh2eL.js";import"./GeometryUtils-BRRfazic.js";import"./imageutils-KgbVacIV.js";import"./Matcher-v9ErZwmD.js";import"./visualVariablesUtils-0WgcmuMn.js";import"./visualVariablesUtils-7_6yXvXo.js";import"./tileUtils-B7X19rIS.js";import"./libtess-lH4Jrtkh.js";import"./TurboLine-CDscS66C.js";import"./ExpandedCIM-C1laM-_7.js";import"./CircularArray-CFz2ft5h.js";import"./ComputedAttributeStorage-CF7WDnl8.js";import"./arcadeTimeUtils-CyWQANWo.js";import"./executionError-BOo4jP8A.js";import"./centroid-UTistape.js";import"./basicInterfaces-Dc_Mm1a-.js";import"./normalizeUtilsSync-NMksarRY.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./schemaUtils-DLXXqxNF.js";import"./util-DPgA-H2V.js";import"./TiledDisplayObject-C5kAiJtw.js";import"./BitmapTileContainer-CnUUv4uK.js";import"./Bitmap-CraE42_6.js";import"./TileContainer-CC8_A7ZF.js";import"./BaseGraphicContainer-Cqw9Xlck.js";import"./FeatureContainer-B5oUlI2-.js";import"./scaleUtils-DgkF6NQH.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./identify-4SBo5EZk.js";import"./sublayerUtils-bmirCD0I.js";import"./IdentifyResult-4DxLVhTm.js";import"./popupUtils-BjdidZV3.js";const G=[0,0];let h=class extends k(U(Q(S))){constructor(){super(...arguments),this._fetchQueue=null,this._highlightGraphics=new v,this._highlightView=null,this._popupHighlightHelper=null,this._tileStrategy=null,this.layer=null}get resampling(){return!("resampling"in this.layer)||this.layer.resampling!==!1}update(t){var i;this._fetchQueue.pause(),this._fetchQueue.state=t.state,this._tileStrategy.update(t),this._fetchQueue.resume(),(i=this._highlightView)==null||i.processUpdate(t)}attach(){const t="tileServers"in this.layer?this.layer.tileServers:null;if(this._tileInfoView=new H(this.layer.tileInfo,this.layer.fullExtent),this._fetchQueue=new T({tileInfoView:this._tileInfoView,concurrency:t&&10*t.length||10,process:(i,e)=>this.fetchTile(i,e)}),this._tileStrategy=new q({cachePolicy:"keep",resampling:this.resampling,acquireTile:i=>this.acquireTile(i),releaseTile:i=>this.releaseTile(i),tileInfoView:this._tileInfoView}),F(this,this.layer)){const i=this._highlightView=new b({view:this.view,graphics:this._highlightGraphics,requestUpdateCallback:()=>this.requestUpdate(),container:new C(this.view.featuresTilingScheme),defaultPointSymbolEnabled:!1});this.container.addChild(this._highlightView.container),this._popupHighlightHelper=new P({createFetchPopupFeaturesQueryGeometry:(e,r)=>E(e,r,this.view),highlightGraphics:this._highlightGraphics,highlightGraphicUpdated:(e,r)=>{i.graphicUpdateHandler({graphic:e,property:r})},layerView:this,updatingHandles:this.updatingHandles})}this.requestUpdate(),this.addAttachHandles(V(()=>this.resampling,()=>{this.doRefresh()})),super.attach()}detach(){var t;super.detach(),this._tileStrategy.destroy(),this._fetchQueue.clear(),this.container.removeAllChildren(),(t=this._popupHighlightHelper)==null||t.destroy(),this._fetchQueue=this._tileStrategy=this._tileInfoView=this._popupHighlightHelper=null}async fetchPopupFeatures(t,i){return this._popupHighlightHelper?this._popupHighlightHelper.fetchPopupFeatures(t,i):[]}highlight(t){return this._popupHighlightHelper?this._popupHighlightHelper.highlight(t):{remove(){}}}moveStart(){this.requestUpdate()}viewChange(){this.requestUpdate()}moveEnd(){this.requestUpdate()}supportsSpatialReference(t){var i;return w((i=this.layer.tileInfo)==null?void 0:i.spatialReference,t)}async doRefresh(){!this.attached||this.updateRequested||this.suspended||(this._fetchQueue.reset(),this._tileStrategy.tiles.forEach(t=>this._enqueueTileFetch(t)))}isUpdating(){var t;return((t=this._fetchQueue)==null?void 0:t.updating)??!1}acquireTile(t){const i=this._bitmapView.createTile(t),e=i.bitmap;return[e.x,e.y]=this._tileInfoView.getTileCoords(G,i.key),e.resolution=this._tileInfoView.getTileResolution(i.key),[e.width,e.height]=this._tileInfoView.tileInfo.size,this._enqueueTileFetch(i),this._bitmapView.addChild(i),this.requestUpdate(),i}releaseTile(t){this._fetchQueue.abort(t.key.id),this._bitmapView.removeChild(t),t.once("detach",()=>t.destroy()),this.requestUpdate()}async fetchTile(t,i={}){const e="tilemapCache"in this.layer?this.layer.tilemapCache:null,{signal:r,resamplingLevel:m=0}=i;if(!e)try{return await this._fetchImage(t,r)}catch(s){if(!a(s)&&!this.resampling)return f(this._tileInfoView.tileInfo.size);if(m<3){const n=this._tileInfoView.getTileParentId(t.id);if(n){const u=new g(n),_=await this.fetchTile(u,{...i,resamplingLevel:m+1});return d(this._tileInfoView,_,u,t)}}throw s}const p=new g(0,0,0,0);let o;try{if(await e.fetchAvailabilityUpsample(t.level,t.row,t.col,p,{signal:r}),p.level!==t.level&&!this.resampling)return f(this._tileInfoView.tileInfo.size);o=await this._fetchImage(p,r)}catch(s){if(a(s))throw s;o=await this._fetchImage(t,r)}return this.resampling?d(this._tileInfoView,o,p,t):o}async _enqueueTileFetch(t){if(!this._fetchQueue.has(t.key.id)){try{const i=await this._fetchQueue.push(t.key);t.bitmap.source=i,t.bitmap.width=this._tileInfoView.tileInfo.size[0],t.bitmap.height=this._tileInfoView.tileInfo.size[1],t.once("attach",()=>this.requestUpdate())}catch(i){a(i)||y.getLogger(this.declaredClass).error(i)}this.requestUpdate()}}async _fetchImage(t,i){return this.layer.fetchImageBitmapTile(t.level,t.row,t.col,{signal:i})}};l([c()],h.prototype,"_fetchQueue",void 0),l([c()],h.prototype,"resampling",null),h=l([I("esri.views.2d.layers.TileLayerView2D")],h);const li=h;export{li as default};
