// 变化曲线图
export function lineOption() {
  const option = {
    title: {
      text: '',
      textStyle: {
        color: '#5470C6',
        fontSize: '14px'
      },
      top: 10
    },
    grid: {
      left: 90,
      right: 90,
      top: 70,
      bottom: 40
    },
    legend: {
      // right: 150,
      top: 20,
      type: 'scroll',
      width: '500',
      textStyle: {
        fontSize: 12
      }
    },
    tooltip: { trigger: 'axis' },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: []
    },
    yAxis: [{
      position: 'left',
      type: 'value',
      name: '流量(m³)',
      axisLine: {
        show: true,
        lineStyle: {
          types: 'solid'
        }
      },
      axisLabel: {
        show: true

      },
      splitLine: {
        lineStyle: {
          type: [5, 10],
          dashOffset: 5
        }
      }
    },
    {
      position: 'right',
      type: 'value',
      name: '',
      axisLine: {
        show: true,
        lineStyle: {
          types: 'solid'
        }
      },
      axisLabel: {
        show: true
      },
      splitLine: {
        lineStyle: {
          type: [5, 10],
          dashOffset: 5
        }
      }
    }],
    series: [
      {
        name: '供水量09-07',
        smooth: true,
        data: [150, 230, 224, 218, 135, 147, 260, 135, 147, 260, 135, 147, 260, 135, 147, 260, 135, 147, 260, 135, 147, 260, 135, 260],
        type: 'line',
        markPoint: {
          data: [
            { type: 'max', name: '最大值' },
            { type: 'min', name: '最小值' }
          ]
        },
        markLine: {
          data: [{ type: 'average', name: '平均值' }]
        }
      },
      {
        name: '供水量09-08',
        smooth: true,
        data: [150, 230, 224, 218, 135, 147, 260, 135, 147, 260, 135, 147, 260, 135, 147, 260, 135, 147, 260, 135, 147, 260, 135, 260],
        type: 'line',
        markPoint: {
          data: [
            { type: 'max', name: '最大值' },
            { type: 'min', name: '最小值' }
          ]
        },
        markLine: {
          data: [{ type: 'average', name: '平均值' }]
        }
      },
      {
        name: '耗电量09-07',
        smooth: true,
        data: [150, 123, 224, 218, 135, 123, 260, 135, 333, 444, 332, 135, 147, 260, 321, 147, 260, 135, 147, 260, 135, 147, 260, 221],
        type: 'line',
        yAxisIndex: 1,
        markPoint: {
          data: [
            { type: 'max', name: '最大值' },
            { type: 'min', name: '最小值' }
          ]
        },
        markLine: {
          data: [{ type: 'average', name: '平均值' }]
        }
      },
      {
        name: '耗电量09-08',
        smooth: true,
        data: [150, 123, 224, 218, 135, 123, 260, 135, 333, 444, 332, 135, 147, 260, 321, 147, 260, 135, 147, 260, 135, 147, 260, 221],
        type: 'line',
        yAxisIndex: 1,
        markPoint: {
          data: [
            { type: 'max', name: '最大值' },
            { type: 'min', name: '最小值' }
          ]
        },
        markLine: {
          data: [{ type: 'average', name: '平均值' }]
        }
      }
    ]
  }
  return option
}
