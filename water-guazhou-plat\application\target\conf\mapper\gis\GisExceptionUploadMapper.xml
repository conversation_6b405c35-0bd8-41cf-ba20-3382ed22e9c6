<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.gis.GisExceptionUploadMapper">
    <select id="findList" resultType="org.thingsboard.server.dao.model.sql.gis.GisExceptionUpload">
        SELECT
            a.*
        FROM
            tb_gis_exception_upload a
        <where>
            <if test="param.tenantId != null and param.tenantId != ''">
                AND a.tenant_id = #{param.tenantId}
            </if>
            <if test="param.status != null and param.status != ''">
                AND a.status = #{param.status}
            </if>
            <if test="param.beginTime != null">
                AND a.upload_time <![CDATA[ >= ]]> #{param.beginTime}
            </if>
            <if test="param.endTime != null">
                AND a.upload_time <![CDATA[ <= ]]> #{param.endTime}
            </if>
        </where>
        ORDER BY a.upload_time DESC
    </select>

</mapper>