import"./index-0NlGN6gS.js";import{c as i,a1 as D,S as P,b as s}from"./index-r0dFAfgr.js";import{c as f,a as y,s as g}from"./FeatureHelper-Da16o0mu.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import"./MapView-DaoQedLH.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./Point-WxyopZva.js";import{g as G}from"./LayerHelper-Cn-iiqxI.js";import"./project-DUuzYgGl.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./geometryEngineBase-BhsKaODW.js";import{G as A,a as M,D as R,P as k,b as v}from"./dma-SMxrzG7b.js";import{G as x,a as H}from"./hookupDevice-Bcbk7s68.js";const to=()=>{const d=i([]),l=i([]),u=i([]),p=async()=>{try{const r=await A();return d.value=D(r.data||[],void 0,[],void 0,o=>{var t,c;return o.path&&(((t=o.data)==null?void 0:t.type)==="1"?(o.iconifyIcon="ep:folder",o.iconStyle={color:"orange"}):((c=o.data)==null?void 0:c.type)==="2"?(o.iconifyIcon="ep:collection-tag",o.iconStyle={color:"#318DFF"}):(o.iconifyIcon="ep:collection-tag",o.iconStyle={color:"#318DFF"})),o}),r}catch{console.log("获取分区树失败")}},m=async r=>{try{const o=await M(r);return l.value=o.data||[],o}catch{console.log("获取分区列表失败")}},b=async r=>{const o=await P("提示信息","确定删除？");if(console.log(o),o==="confirm")try{await R(r),s.success("删除成功"),await p()}catch(t){console.log(t),s.error("删除失败")}},S=async r=>{if(await P("提示信息","确定提交？")==="confirm")try{(await k(r)).data.code===200?(s.success("提交成功"),p()):s.error("提交失败")}catch{s.error("提交失败")}},T=()=>({label:"label",children:"children"});let e;const I=r=>e==null?void 0:e.graphics.find(o=>{var t;return((t=o.attributes)==null?void 0:t.id)===r}),F=async r=>{await m(),e=G(r,{id:"partition-dma-layer",title:"分区"});const o=[];l.value.map(t=>{if(!t.geom)return;const c=JSON.parse(t.geom),n=f({geometry:y("polygon",c,r==null?void 0:r.spatialReference),symbol:g("polygon",{color:t.rangeColor,outlineColor:t.borderColor}),attributes:t});n&&o.push(n)}),e==null||e.removeAll(),e==null||e.addMany(o)};let a;const C=(r,o)=>{if(!r||!(o!=null&&o.length))return;a=G(r,{id:"partition-cur-layer",title:"当前分区"}),a==null||a.removeAll();const t=f({geometry:y("polygon",o,r==null?void 0:r.spatialReference),symbol:g("polygon")});t&&(a==null||a.add(t),r==null||r.goTo(t))},h=i([]);return{getList:m,getTree:p,List:l,Tree:d,TreeProps:T,Devices:h,getDevices:async r=>{var t,c,n;const o=await x(r);return h.value=(c=(t=o.data)==null?void 0:t.data)==null?void 0:c.data,(n=o.data)==null?void 0:n.data},DeviceTree:u,getDeviceTree:async r=>{try{const o=await H(r);return u.value=D(o.data.data||[],void 0,[],void 0,t=>(t.path&&(t.data.type==="1"?(t.iconifyIcon="ep:folder",t.iconStyle={color:"#e6a23c"}):t.data.type==="2"?(t.iconifyIcon="ep:collection-tag",t.iconStyle={color:"#318DFF"}):(t.iconifyIcon="ep:location-information",t.iconStyle={color:"#38ceff"})),t)),o}catch{console.log("获取分区树失败")}},Delete:b,Post:S,refreshPartitions:F,getPartitionGraphic:I,extentToPartition:C,getPartitionInfo:async r=>await v(r),createPartitionGraphic:(r,o)=>!o||!r?void 0:f({geometry:y("polygon",o,r.spatialReference),symbol:g("polygon")})}};export{to as u};
