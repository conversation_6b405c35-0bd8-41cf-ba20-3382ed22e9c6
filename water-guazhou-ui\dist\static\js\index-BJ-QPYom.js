import{d as oe,r as se,a8 as ie,c as ne,am as Y,bB as ce,bv as de,o as ue,g as r,n as i,p as b,ax as pe,bh as E,aB as k,aJ as w,h as c,by as L,an as d,aw as f,F as I,av as T,q as B,aH as z,i as N,cs as Q,cn as W,G as fe,cY as ye,bw as q,ak as De,bM as ke,bq as he,aK as me,aL as ge,H as _e,cE as Ce,bb as ve,bV as xe,C as Pe}from"./index-r0dFAfgr.js";const be={class:"tree-list-container"},we={key:0,class:"tree-list-title"},Ie={key:0,class:"tree-list-title-right-wrapper"},Fe={key:1,class:"switch-box"},Ve=["onClick"],Ne={key:3,class:"operatebtn-box"},qe={class:"c-t-label"},Ee=["title"],Le={key:0},Te={key:1,class:"hover-button"},ze=oe({__name:"index",props:{treeData:{}},setup(X,{expose:Z}){const e=X,p=se({filterText:"",customer_disable:!1,queryParams:{...e.treeData.queryParams||{}},defaultProps:e.treeData.defaultProps||{children:"children",label:"name",isLeaf:"isLeaf"}}),ee=ie(()=>{var C,v,m,x,P,g;let l=0;const o=e.treeData.title?"title-":"";((C=e.treeData.add)!=null&&C.perm||(v=e.treeData.edit)!=null&&v.perm||(m=e.treeData.delete)!=null&&m.perm)&&l++,(x=e.treeData.addRoot)!=null&&x.perm&&l++,e.treeData.isFilterTree&&l++,(P=e.treeData.switch)!=null&&P.options.length&&l++,e.treeData.selectFilter&&l++,(g=e.treeData.extraFilters)==null||g.map(()=>{l++});const D="operate"+l;return"tree-height-"+o+D}),h=ne(),te=(l,o)=>{var v,m,x,P,g;const D=(v=l==null?void 0:l.trim())==null?void 0:v.toLocaleLowerCase();if(!D)return!0;const C=o.label?(x=(m=o==null?void 0:o.label)==null?void 0:m.trim())==null?void 0:x.toLocaleLowerCase():(g=(P=o==null?void 0:o.name)==null?void 0:P.trim())==null?void 0:g.toLocaleLowerCase();return C?C.indexOf(D)!==-1:!1};Y(()=>p.filterText,l=>{h.value&&h.value.filter(l)});const ae=l=>{var o,D;(D=h.value)==null||D.setCurrentKey(l||((o=e.treeData.currentProject)==null?void 0:o.id),!0)},le=l=>{var o;(o=h.value)==null||o.setCheckedKeys(l||[])};Y(()=>e.treeData.currentProject,l=>{ce(()=>{h.value&&h.value.setCurrentKey(l==null?void 0:l.id)})},{immediate:!0});const re=(l,o,D)=>{l.disabled||e.treeData.treeNodeHandleClick&&e.treeData.treeNodeHandleClick(l,o,D)};return Z({...de(p),refTree:h,setCurrentByKey:ae,setCheckedKeys:le}),ue(()=>{var l,o;(o=(l=e.treeData).autoFillOptions)==null||o.call(l)}),(l,o)=>{var g,S,$,K,j,O,R,U;const D=me,C=ge,v=_e,m=Ce,x=ve,P=xe;return r(),i("div",be,[b("div",{class:f({headerBoxBorder:e.treeData.title})},[e.treeData.title?(r(),i("div",we,[pe(l.$slots,"header",{},()=>{var s;return[b("span",null,E(e.treeData.title),1),(s=e.treeData.titleRight)!=null&&s.length?(r(),i("div",Ie,[(r(!0),i(k,null,w(e.treeData.titleRight,(u,a)=>(r(),i(k,{key:a},[u.field?(r(),c(L,{key:0,modelValue:p.queryParams[u.field],"onUpdate:modelValue":F=>p.queryParams[u.field]=F,config:u,onChange:u.onChange},null,8,["modelValue","onUpdate:modelValue","config","onChange"])):(r(),c(L,{key:1,config:u},null,8,["config"]))],64))),128))])):d("",!0)]},!0)])):d("",!0)],2),(r(!0),i(k,null,w(e.treeData.extraFilters,(s,u)=>(r(),i("div",{key:u,class:"tree-form-item"},[s.field?(r(),c(L,{key:0,modelValue:p.queryParams[s.field],"onUpdate:modelValue":a=>p.queryParams[s.field]=a,config:s,row:p.queryParams,onChange:s.onChange},null,8,["modelValue","onUpdate:modelValue","config","row","onChange"])):(r(),c(L,{key:1,config:s,row:p.queryParams},null,8,["config","row"]))]))),128)),e.treeData.selectFilter?(r(),c(C,{key:0,modelValue:p.queryParams[e.treeData.selectFilter.key],"onUpdate:modelValue":o[0]||(o[0]=s=>p.queryParams[e.treeData.selectFilter.key]=s),"collapse-tags":"",size:"default",class:"tree-filter-box",multiple:e.treeData.selectFilter.multiple,placeholder:"请选择"+e.treeData.selectFilter.label,style:T([e.treeData.selectFilter.style,{width:"100%"}]),filterable:e.treeData.selectFilter.search,onChange:(g=e.treeData.selectFilter)==null?void 0:g.handleChange},{default:I(()=>[(r(!0),i(k,null,w(e.treeData.selectFilter.options,s=>(r(),c(D,{key:s.value,value:s.value,label:s.label,style:{"box-sizing":"border-box"}},null,8,["value","label"]))),128))]),_:1},8,["modelValue","multiple","placeholder","style","filterable","onChange"])):d("",!0),e.treeData.switch?(r(),i("div",Fe,[(r(!0),i(k,null,w(e.treeData.switch.options,s=>{var u,a;return r(),i("span",{key:s.value,class:f({active:((a=(u=e.treeData)==null?void 0:u.switch)==null?void 0:a.curVal)===s.value}),onClick:F=>{var V;return((V=e.treeData.switch)==null?void 0:V.handle)&&e.treeData.switch.handle(s)}},E(s.label),11,Ve)}),128))])):d("",!0),e.treeData.isFilterTree?(r(),c(v,{key:2,modelValue:p.filterText,"onUpdate:modelValue":o[1]||(o[1]=s=>p.filterText=s),placeholder:e.treeData.filterPlaceHolder||"搜索关键字",class:f(["tree-filter-box",e.treeData.filterClassName]),clearable:!0,"prefix-icon":e.treeData.filterIcon},null,8,["modelValue","placeholder","class","prefix-icon"])):d("",!0),(S=e.treeData.addRoot)!=null&&S.perm?(r(),i("div",Ne,[B(z,{class:"btn-lang edit-primary-blue",config:e.treeData.addRoot},null,8,["config"])])):d("",!0),($=e.treeData.add)!=null&&$.perm||(K=e.treeData.delete)!=null&&K.perm||(j=e.treeData.edit)!=null&&j.perm?(r(),i("div",{key:4,class:f(["operatebtn-box",{"active-operation":e.treeData.currentProject}])},[(O=e.treeData.add)!=null&&O.perm?(r(),c(z,{key:0,class:"node-o-btn query-yellow",config:e.treeData.add,type:"warning"},null,8,["config"])):d("",!0),(R=e.treeData.edit)!=null&&R.perm?(r(),c(z,{key:1,class:"node-o-btn add-child-blue",config:e.treeData.edit},null,8,["config"])):d("",!0),(U=e.treeData.delete)!=null&&U.perm?(r(),c(z,{key:2,class:"node-o-btn delete-red",config:e.treeData.delete,type:"danger"},null,8,["config"])):d("",!0)],2)):d("",!0),b("div",{class:f(["tree-list-box",{[ee.value]:!0}])},[B(P,null,{default:I(()=>{var s;return[B(x,{ref_key:"refTree",ref:h,accordion:e.treeData.accordion!==!1,"highlight-current":"","node-key":"id","default-expand-all":e.treeData.defaultExpandAll!==!1,"expand-on-click-node":e.treeData.expandOnClickNode!==!1,"current-node-key":(s=e.treeData.currentProject)==null?void 0:s.id,loading:e.treeData.loading,data:e.treeData.data,props:e.treeData.defaultProps||p.defaultProps,"filter-node-method":te,"default-checked-keys":e.treeData.checkedKeys,"default-expanded-keys":e.treeData.expandNodeId,"show-checkbox":e.treeData.showCheckbox,lazy:!!e.treeData.isLazy,load:e.treeData.loadFun,onCheck:e.treeData.handleCheck,onCheckChange:e.treeData.handleCheckChange,onNodeExpand:e.treeData.nodeExpand,onNodeClick:re},{default:I(({node:u,data:a})=>{var F,V,H,M,A,G,J;return[b("div",{class:f(["custom-tree-node",{"active-tree-node":e.treeData.currentProject&&e.treeData.currentProject.id===a.id,"disabled-node":a.disabled}])},[b("p",qe,[a.iconifyIcon||l.treeData.iconifyIcon?(r(),c(N(Q),{key:0,style:T([{"margin-right":"8px"},a.iconStyle||l.treeData.iconStyle]),icon:a.iconifyIcon||l.treeData.iconifyIcon},null,8,["style","icon"])):d("",!0),a.icon||a.svgIcon||l.treeData.icon||l.treeData.svgIcon?(r(),c(m,{key:1,style:T([{"margin-right":"8px"},a.iconStyle||l.treeData.iconStyle])},{default:I(()=>[a.icon||l.treeData.icon?(r(),i("i",{key:0,class:f(a.icon||l.treeData.icon)},null,2)):(r(),c(W(a.svgIcon||l.treeData.svgIcon),{key:1}))]),_:2},1032,["style"])):d("",!0),b("span",{class:"c-t-name",title:a.label||a[p.defaultProps.label]},E(a.label||a[p.defaultProps.label]),9,Ee)]),e.treeData.tags?(r(),i("div",Le,[(r(!0),i(k,null,w(e.treeData.tags,(t,y)=>(r(),c(ye,{key:y,round:t.round,color:typeof t.color=="string"?t.color:t.color&&t.color(t.field&&a.data[t.field])},{default:I(()=>[fe(E(a.data[t.field]),1)]),_:2},1032,["round","color"]))),128))])):d("",!0),(F=e.treeData.add)!=null&&F.perm||(V=e.treeData.delete)!=null&&V.perm||(H=e.treeData.edit)!=null&&H.perm||(M=e.treeData.nodeOperations)!=null&&M.length?(r(),i("div",Te,[(r(!0),i(k,null,w(e.treeData.nodeOperations,(t,y)=>(r(),i(k,{key:y},[t.iconifyIcon?(r(),i(k,{key:0},[(typeof t.perm=="function"?t.perm(a):t.perm)?(r(),c(N(Q),{key:0,class:f(["svgicon",[t.type||"primary",typeof t.disabled=="function"?t.disabled(a)?"disabled":"":t.disabled?"disabled":""]]),icon:typeof t.iconifyIcon=="function"?t.iconifyIcon(a):t.iconifyIcon,color:typeof t.color=="function"?t.color(a):t.color,title:typeof t.text=="function"?t.text(a):t.text,onClick:q(()=>{var n;return!(typeof(t==null?void 0:t.disabled)=="function"?t.disabled(a):t!=null&&t.disabled)&&((n=t.click)==null?void 0:n.call(t,a,l.$parent,u))},["stop"])},null,8,["class","icon","color","title","onClick"])):d("",!0)],64)):t.icon||t.svgIcon?(r(),i(k,{key:1},[(typeof t.perm=="function"?t.perm(a):t.perm)?(r(),c(m,{key:0,class:f(["svgicon",[t.type||"primary",typeof t.disabled=="function"?t.disabled(a)?"disabled":"":t.disabled?"disabled":""]]),title:typeof t.text=="function"?t.text(a):t.text,onClick:q(()=>{var n;return!(typeof(t==null?void 0:t.disabled)=="function"?t.disabled(a):t!=null&&t.disabled)&&((n=t.click)==null?void 0:n.call(t,a,l.$parent,u))},["stop"])},{default:I(()=>[t.icon?(r(),i("i",{key:0,class:f(typeof t.icon=="function"?t.icon(a):t.icon),style:T({color:typeof t.color=="function"?t.color(a):t.color})},null,6)):(r(),c(W(typeof t.svgIcon=="function"?t.svgIcon(a):t.svgIcon),{key:1}))]),_:2},1032,["class","title","onClick"])):d("",!0)],64)):d("",!0)],64))),128)),(A=e.treeData.add)!=null&&A.perm?(r(),c(N(De),{key:0,class:f(["svgicon success",[typeof e.treeData.add.disabled=="function"?e.treeData.add.disabled(a)?"disabled":"":e.treeData.add.disabled?"disabled":""]]),size:12,onClick:q(()=>{var t,y,n,_;return!(typeof((t=e.treeData.add)==null?void 0:t.disabled)=="function"?e.treeData.add.disabled(a):(y=e.treeData.add)!=null&&y.disabled)&&((_=(n=e.treeData.add)==null?void 0:n.click)==null?void 0:_.call(n,a,l.$parent,u))},["stop"])},null,8,["class","onClick"])):d("",!0),(G=e.treeData.edit)!=null&&G.perm?(r(),c(N(ke),{key:1,class:f(["svgicon primary",[typeof e.treeData.edit.disabled=="function"?e.treeData.edit.disabled(a)?"disabled":"":e.treeData.edit.disabled?"disabled":""]]),size:12,onClick:q(()=>{var t,y,n,_;return!(typeof((t=e.treeData.edit)==null?void 0:t.disabled)=="function"?e.treeData.edit.disabled(a):(y=e.treeData.edit)!=null&&y.disabled)&&((_=(n=e.treeData.edit)==null?void 0:n.click)==null?void 0:_.call(n,a,l.$parent,u))},["stop"])},null,8,["class","onClick"])):d("",!0),(J=e.treeData.delete)!=null&&J.perm?(r(),c(N(he),{key:2,class:f(["svgicon danger",[typeof e.treeData.delete.disabled=="function"?e.treeData.delete.disabled(a)?"disabled":"":e.treeData.delete.disabled?"disabled":""]]),size:12,onClick:q(()=>{var t,y,n,_;return!(typeof((t=e.treeData.delete)==null?void 0:t.disabled)=="function"?e.treeData.delete.disabled(a):(y=e.treeData.delete)!=null&&y.disabled)&&((_=(n=e.treeData.delete)==null?void 0:n.click)==null?void 0:_.call(n,a,l.$parent,u))},["stop"])},null,8,["class","onClick"])):d("",!0)])):d("",!0)],2)]}),_:1},8,["accordion","default-expand-all","expand-on-click-node","current-node-key","loading","data","props","default-checked-keys","default-expanded-keys","show-checkbox","lazy","load","onCheck","onCheckChange","onNodeExpand"])]}),_:1})],2)])}}}),Se=Pe(ze,[["__scopeId","data-v-74608861"]]);export{Se as _};
