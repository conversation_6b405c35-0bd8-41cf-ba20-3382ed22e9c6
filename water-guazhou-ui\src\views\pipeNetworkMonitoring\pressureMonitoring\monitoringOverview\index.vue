<template>
  <RightDrawerMap
    ref="refMap"
    :title="'压力监测总览'"
    :windows="state.windows"
    :hide-detail-close="true"
    :hide-layer-list="true"
    :right-drawer-width="550"
    :enable-cluster="true"
    :cluster-radius="100"
    :cluster-min-size="24"
    :cluster-max-size="60"
    :cluster-graphics="clusterDataRef"
    @map-loaded="onMapLoaded"
  >
    <div class="content">
      <!--      <FieldSet-->
      <!--        type="underline"-->
      <!--        title="压力监测设备"-->
      <!--      ></FieldSet>-->
      <!--      <div-->
      <!--        ref="echartsDiv"-->
      <!--        v-loading="loading"-->
      <!--        class="right-box top"-->
      <!--      >-->
      <!--        <VChart-->
      <!--          ref="refChart"-->
      <!--          class="top"-->
      <!--          :option="state.pieOption"-->
      <!--        />-->
      <Form ref="refForm" :config="FormConfig"> </Form>
      <!--      </div>-->
      <FieldSet type="underline" title="压力数据监测"></FieldSet>
      <div class="right-box">
        <FormTable
          ref="refCard"
          class="table-box"
          :config="cardTableConfig"
        ></FormTable>
      </div>
      <FieldSet
        type="underline"
        :title="(state.curRow?.name || '') + '近三天运行曲线'"
      ></FieldSet>
      <div ref="echartsDiv" v-loading="loading" class="right-box bottom">
        <Tabs v-model="state.activeName" :config="TabsConfig"></Tabs>
        <VChart
          ref="refChart"
          :theme="useAppStore().isDark ? 'dark' : 'light'"
          :option="state.lineOption"
        />
      </div>
    </div>
    <template #detail-header> </template>
    <template #detail-default> </template>
  </RightDrawerMap>
</template>
<script lang="ts" setup>
import { reactive, ref, nextTick, onMounted } from 'vue';
import Point from '@arcgis/core/geometry/Point.js';
import Graphic from '@arcgis/core/Graphic.js';
import PictureMarkerSymbol from '@arcgis/core/symbols/PictureMarkerSymbol.js';
import TextSymbol from '@arcgis/core/symbols/TextSymbol.js';
import useStation from '@/hooks/station/useStation';
import { GetStationRealTimeDetail } from '@/api/shuiwureports/zhandian';
import { IECharts } from '@/plugins/echart';
import { useAppStore, useBusinessStore } from '@/store';
import RightDrawerMap from '@/views/arcMap/components/common/RightDrawerMap.vue';
import { bindViewClick, gotoAndHighLight } from '@/utils/MapHelper';
import { stationView } from '@/api/pipeNetworkMonitoring/flowMonitoring';
import { getThreeDaysData } from '@/api/headwatersManage/headwaterMonitoring';
import { lineOption, pieOption } from '../echartsData/echart';
import { ring } from '../../flowMonitoring/monitoringOverview/data';
import { GetPressureStationList } from '@/api/mapservice/onemap';
import { getStationImageUrl } from '@/utils/URLHelper';

// 使用@ts-ignore忽略TypeScript错误
// @ts-ignore
import elementResizeDetectorMaker from 'element-resize-detector';
// @ts-ignore
const erd = elementResizeDetectorMaker();
const refCard = ref<IFormTableIns>();
// const refAmap = ref<ISLAmapIns>()
const { getAllStationOption } = useStation();

const refMap = ref<InstanceType<typeof RightDrawerMap>>();
const refChart = ref<IECharts>();
const loading = ref<boolean>(true);

const echartsDiv = ref<any>();
const tableData = reactive<any[]>([]);

const statusOptions = [
  { name: 'offline', label: '离线' },
  { name: 'alarm', label: '报警' },
  { name: 'online', label: '正常' }
];

// 定义简化的点位数据接口
interface ISimpleMarker {
  id: string;
  name: string;
  longitude: number;
  latitude: number;
  attributes: any;
  symbolType: 'marker' | 'text';
  symbolConfig: any;
}

// 使用简化的数据结构
const clusterDataRef = ref<ISimpleMarker[]>([]);

const state = reactive<{
  lineOption: any;
  pieOption: any;
  curRow?: any;
  activeName: any;
  stationStatus: any;
  data: any;
  stationLocation: any[];
  tableData: any[];
  windows: IArcMarkerProps[];
}>({
  lineOption: null,
  pieOption: null,
  stationStatus: [],
  curRow: {},
  activeName: null,
  data: null,
  stationLocation: [],
  tableData: [],
  windows: []
});
const TabsConfig = reactive<ITabs>({
  type: 'tabs',
  tabType: 'simple',
  tabs: [],
  handleTabClick: (tab) => {
    const tabData =
      (TabsConfig.tabs.find((tb) => tb.value === tab.props.name) as any) || {};
    resetPanel(state.curRow, tabData.data);
  }
});

const staticState: {
  view?: __esri.MapView;
} = {};

const handleMarkClick = async (stationId?: string) => {
  const tableRow = cardTableConfig.dataList.find(
    (item) => item.stationId === stationId
  );
  const station = state.tableData.find((item) => item.stationId === stationId);
  TabsConfig.tabs = station?.dataList?.map((d, i) => {
    return {
      label: d.propertyName,
      value: d.property + i,
      data: d
    };
  });
  state.activeName = TabsConfig.tabs[0]?.value;
  state.curRow = tableRow;
  resetPanel(tableRow, TabsConfig.tabs[0]?.data);
  
  // 从简化数据中查找对应的数据
  let markerData: ISimpleMarker | undefined;
  if (!stationId) {
    markerData = clusterDataRef.value?.[0];
  } else {
    markerData = clusterDataRef.value.find(
      (item) => item.attributes?.stationId === stationId && item.symbolType === 'marker'
    );
    // 如果找到数据且地图视图存在，进行定位
    if (markerData && staticState.view) {
      // 创建临时的Point对象用于定位
      const point = new Point({
        longitude: markerData.longitude,
        latitude: markerData.latitude,
        spatialReference: staticState.view.spatialReference
      });
      await staticState.view.goTo({
        target: point,
        zoom: 15
      });
    }
  }
  if (!markerData) return;

  const attributes = markerData.attributes;
  const res = await GetStationRealTimeDetail(attributes.id);
  const values =
    res.data?.map((item) => {
      item.label = item.propertyName;
      item.value;

      return item;
    }) || [];
  
  state.windows.length = 0;
  state.windows.push({
    visible: false,
    x: markerData.longitude,
    y: markerData.latitude,
    offsetY: -30,
    title: attributes.name,
    attributes: {
      values,
      id: attributes.id
    }
  });
  await nextTick();
  refMap.value?.openPop(attributes.id);
};



const FormConfig = reactive<IFormConfig>({
  group: [
    {
      id: 'chart',
      fieldset: {
        desc: '压力监测设备',
        type: 'underline',
        style: {
          marginTop: 0
        }
      },
      fields: [
        {
          type: 'vchart',
          option: ring(),
          style: {
            height: '150px'
          }
        }
      ]
    }
  ],
  labelPosition: 'top',
  gutter: 12,
  defaultValue: {
    type: 'all'
  }
});

const cardTableConfig = reactive<ITable>({
  loading: true,
  dataList: [],
  columns: [],
  highlightCurrentRow: true,
  currentRowKey: 'stationId',
  handleRowClick: async (row: any) => {
    // 从简化数据中查找对应的数据
    const markerData = clusterDataRef.value.find(
      (item) => item.attributes?.stationId === row.stationId && item.symbolType === 'marker'
    );
    // 如果找到数据且地图视图存在，进行定位
    if (markerData && staticState.view) {
      const point = new Point({
        longitude: markerData.longitude,
        latitude: markerData.latitude,
        spatialReference: staticState.view.spatialReference
      });
      await staticState.view.goTo({
        target: point,
        zoom: 15
      });
    }

    const station = state.tableData.find(
      (item) => item.stationId === row.stationId
    );
    TabsConfig.tabs = station?.dataList?.map((d, i) => {
      return {
        label: d.propertyName,
        value: d.property + i,
        data: d
      };
    });
    state.activeName = TabsConfig.tabs[0]?.value;
    resetPanel(TabsConfig.tabs[0]?.data);
    cardTableConfig.currentRow = row;
    state.curRow = row;
    handleMarkClick(row.stationId);
  },
  pagination: { hide: true }
});

// 添加地图图标 - 修改为创建简化数据对象
const addMarks = async (realTimeData: any) => {
  console.log('=== addMarks 开始执行 ===');
  const res = await getAllStationOption('压力监测站,测流压站');

  state.stationStatus = res;
  console.log('压力监测站', res);
  
  // 清空现有聚合数据
  clusterDataRef.value = [];
  console.log('清空聚合数据，当前长度:', clusterDataRef.value.length);
  
  // 创建简化数据数组
  const simpleDataArray: ISimpleMarker[] = [];
  
  res.map((data, index) => {
    const item = data.data;
    const location = item?.location?.split(',');
    const status = state.stationStatus?.find(
      (station) => station.id === data.id
    );
    
    // TODO 切换异常图片
    const url =
      status.status === 'online'
        ? getStationImageUrl('压力监测站.png')
        : getStationImageUrl('压力监测站.png');
        
    // 创建简化的标记点数据
    const simpleMarker: ISimpleMarker = {
      id: `marker_${index}`,
      name: item.name || '未命名站点',
      longitude: parseFloat(location?.[0] || '0'),
      latitude: parseFloat(location?.[1] || '0'),
      attributes: {
        ObjectID: index + 1,
        name: item.name || '未命名站点',
        // 只保留可序列化的基本属性，避免包含复杂对象
        id: item.id,
        stationId: item.id,
        stationType: item.type,
        location: item.location,
        status: status?.status || 'unknown'
      },
      symbolType: 'marker',
      symbolConfig: {
        width: 25,
        height: 30,
        yoffset: 15,
        url
      }
    };
    
    simpleDataArray.push(simpleMarker);
    
    // 可选：添加文本标签（如果需要的话）
    // const pressureData = realTimeData?.find(
    //   (pressure) => pressure.stationId === item.id
    // );
    // 
    // const simpleText: ISimpleMarker = {
    //   id: `text_${index}`,
    //   name: pressureData ? pressureData.name : '-',
    //   longitude: parseFloat(location?.[0] || '0'),
    //   latitude: parseFloat(location?.[1] || '0'),
    //   attributes: {
    //     ObjectID: index + 2,
    //     name: pressureData ? pressureData.name : '-',
    //     type: 'label'
    //   },
    //   symbolType: 'text',
    //   symbolConfig: {
    //     yoffset: -15,
    //     color: '#00ff33',
    //     text: pressureData ? pressureData.name : '-'
    //   }
    // };
    // 
    // simpleDataArray.push(simpleText);
  });
  
  // 直接赋值简化数据
  console.log('=== 准备设置 clusterData ===');
  console.log('simpleDataArray length:', simpleDataArray.length);
  console.log('simpleDataArray:', simpleDataArray);
  
  clusterDataRef.value = simpleDataArray;
  
  console.log('=== clusterData 已设置 ===');
  console.log('clusterDataRef.value length:', clusterDataRef.value.length);
  
  state.stationLocation = res;
  state.curRow = res[0]?.data;
  cardTableConfig.currentRow = state.curRow;
  handleMarkClick(state.curRow?.id);
};

// 刷新列表 模拟数据
const refreshData = async () => {
  stationView({
    stationType: '压力监测站,测流压站',
    projectId: useBusinessStore().selectedProject?.value
  }).then((res) => {
    state.tableData = res.data;
    if (tableData) {
      const cs = state.tableData[0]?.dataList?.map((row) => {
        return {
          prop: row.property,
          label: row.propertyName,
          unit: row.unit,
          minWidth: 120
        } as IFormTableColumn;
      });
      cardTableConfig.loading = false;
      const newData = state.tableData?.map((row) => {
        const property = row.dataList?.find(
          (data) => data.property === 'pressure'
        );
        const newData = {
          name: row.name,
          stationId: row.stationId,
          time: property ? property.time : '-',
          unit: property ? property.unit : '-'
        };
        row?.dataList?.map((d) => {
          newData[d.property + ''] = d.value;
        });
        return newData;
      });
      console.log('newData--', newData);
      addMarks(newData);
      cardTableConfig.columns = [
        {
          prop: 'name',
          label: '名称',
          minWidth: 200
        }
      ].concat(cs);
      cardTableConfig.dataList = newData;
      // cardTableConfig.currentRow = newData[0]
      state.activeName = state.tableData[0]?.dataList[0];
      cardTableConfig.currentRow = newData[0];
      const station = state.tableData.find(
        (item) => item.stationId === newData[0]?.stationId
      );
      TabsConfig.tabs = station?.dataList?.map((d, i) => {
        return {
          label: d.propertyName,
          value: d.property + i,
          data: d
        };
      });
      state.activeName = TabsConfig.tabs[0]?.value;
      resetPanel(TabsConfig.tabs[0]?.data);
      handleMarkClick(state.tableData[0]?.stationId);
    } else {
      cardTableConfig.loading = false;
      loading.value = false;
    }
  });
  await alarmNum();
};

// 告警饼图统计
const alarmNum = async () => {
  const res = await GetPressureStationList({ status: '' });
  const field = FormConfig.group[0].fields[0] as IFormVChart;
  const total: number = res.data?.data?.length || 0;
  const statusDatas: any[] = [];
  const data = res.data?.data;
  data?.map((item) => {
    let statusData = statusDatas.find((o) => o.status === item.status);
    const { label } = statusOptions.find((o) => o.name === item.status) || {};
    if (!statusData) {
      statusData = {
        name: label,
        status: item.status,
        nameAlias: label,
        value: 1,
        // valueAlias: '1',
        scale: '0%'
      };
      statusDatas.push(statusData);
    } else {
      statusData.value++;
    }
  });
  statusDatas.map((item) => {
    item.scale = total === 0 ? '0%' : (Number(item.value) / total) * 100 + '%';
    return item;
  });
  field && (field.option = ring(statusDatas, '个'));
};

//  获取监控数据已经加载显示图表
const resetPanel = async (row, data?: any) => {
  console.log('resetPanel', row, data);
  if (row || data) {
    cardTableConfig.currentRow = row;
  await refuseChart(data || row);
  nextTick(async () => {
    state.pieOption = pieOption();
  });
  loading.value = false;
  }
};

// 加载图表
const refuseChart = async (data: any) => {
  console.log('refuseChart', data);
  const threeDaysData = await getThreeDaysData({
    deviceId: data.deviceId,
    attr: data.property
  });
  const tData = threeDaysData.data?.data;
  const options = lineOption(
    200,
    tData.todayDataList.map((item) => item.ts),
    40,
    40
  );
  const dataMap = [
    { name: '前天', key: 'beforeYesterdayDataList' },
    { name: '昨天', key: 'yesterdayDataList' },
    { name: '今天', key: 'todayDataList' }
  ];
  options.yAxis[0].name = data.propertyName.concat(
    data.unit ? '(' + data.unit + ')' : ''
  );
  const series = dataMap.map((item) => {
    const data = tData[item.key].map((item) => item.value);
    return {
      name: item.name,
      smooth: true,
      data,
      type: 'line',
      markPoint: {
        data: [
          { type: 'max', name: '最大值' },
          { type: 'min', name: '最小值' }
        ]
      },
      markLine: {
        data: [{ type: 'average', name: '平均值' }]
      }
    };
  });
  options.series = series;
  refChart.value?.clear();
  await nextTick(() => {
    if (echartsDiv.value) {
      erd.listenTo(echartsDiv.value, () => {
        state.lineOption = options;
        state.pieOption = pieOption();
        refChart.value?.resize();
      });
    }
  });
};



// 显示地图弹出层
// const addInfoWindow = async (row: any) => {
//   const res = await GetStationRealTimeDetail(row.stationId || row.id)
//   const values = res.data?.map(item => {
//     // item.value = (item.value || '') + ' ' + (item.unit || '')
//     item.label = item.propertyName
//     return item
//   }) || []
//   const location = row.location?.split(',')
//   location?.length === 2
//     && refAmap.value?.setListInfoWindow({
//       point: location,
//       values,
//       title: row.name
//     })
// }

// watch(() => store.app.isDark, () => {
//   handleMarkClick(cardTableConfig.currentRow, state.curRow)
// })

onMounted(async () => {
  // refPanelBottom.value?.Open()
  // refPanelRight.value?.Open()
  // refreshData()
  // refMap.value?.toggleCluster(true)
})

const onMapLoaded = async (view: __esri.MapView) => {
  staticState.view = view;
  refMap.value?.toggleCustomDetail(false);
  await refreshData();
  
  // 修改bindViewClick逻辑，处理聚合图层的点击事件
  bindViewClick(staticState.view, (res) => {
    const result = res.results?.[0];
    if (!result) return;
    
    // 处理graphic点击事件（保留原有逻辑）
    if (result.type === 'graphic') {
      const attributes = result.graphic?.attributes;
      // 使用简化属性而不是row
      if (attributes?.stationId || attributes?.id) {
        handleMarkClick(attributes.stationId || attributes.id);
      }
    }
    // 处理聚合图层的feature点击事件
    else if ((result as any).type === 'feature' && (result as any).layer && (result as any).layer.title === '点聚合图层') {
      const feature = (result as any).feature;
      const attributes = feature?.attributes;
      // 使用简化属性而不是row
      if (attributes?.stationId || attributes?.id) {
        handleMarkClick(attributes.stationId || attributes.id);
      }
    }
  });
};
</script>
<style lang="scss" scoped>
.right-box {
  width: 100%;
  height: 250px;

  .table-box {
    height: 90%;
  }
}

.top {
  height: calc(20vh - 20px);
}

.bottom {
  height: calc(33vh - 20px);
}

.today-lightning-total,
.today-lightning-perton {
  display: flex;

  .count {
    font-size: 18px;
    color: #ffad51;
    font-weight: 500;
  }

  .unit {
    font-size: 12px;
  }

  .yesterday {
    margin-left: auto;
    font-size: 12px;
    padding-top: 10px;
  }
}

.today-lightning-perton {
  .count {
    color: #ff51ab;
  }
}

.header-slot {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .title {
    word-break: keep-all;
  }
}
</style>
<style lang="scss">
.panel {
  &.map-right {
    right: 0;
  }

  &.map-right {
    top: 0;
    width: 400px;
    height: 100%;

    .content {
      width: 100%;
      height: calc(100% - 15px);
    }
  }
}
</style>
