import { request } from '@/plugins/axios';

/**
 * 查询产销差统计
 * @param params
 * @returns
 */
export const GetPartionProSaleDeltaStatistic = (params: { type: string }) => {
  return request({
    url: '/api/spp/dma/partition/nrwCount',
    method: 'get',
    params
  });
};
/**
 * 查询总供水量统计
 * @param params
 * @returns
 */
export const GetPartitionWaterSupplyStatistic = (params: { type: string }) => {
  return request({
    url: '/api/spp/dma/partition/totalSupplyCount',
    method: 'get',
    params
  });
};
/**
 * 查询不同时段的供水量和售水量及产销差
 * @returns
 */
export const GetPartitionWaterOverview = () => {
  return request({
    url: '/api/spp/dma/partition/waterOverview',
    method: 'get'
  });
};

/**
 * 参考漏失排行
 * @returns
 */
export const GetPartitionRefLeakSort = (params?: { name?: string }) => {
  return request({
    url: 'api/spp/dma/partition/referenceLeakSort',
    method: 'get',
    params: params
  });
};
/**
 * 年度产销差
 * @param params
 * @returns
 */
export const nrwYearByName = (params: any) => {
  return request({
    url: '/api/spp/dma/partition/nrwYearByName',
    method: 'get',
    params
  });
};
/**
 * 供水量分析
 * @param params
 * @returns
 */
export const GetPartitionSupplyWaterCount = (params: any) => {
  return request({
    url: '/api/spp/dma/partition/supplyCount',
    method: 'get',
    params
  });
};
/**
 * 售水量分析
 * @param params
 * @returns
 */
export const GetPratitionSaleProWaterCount = (params: any) => {
  return request({
    url: `/api/spp/dma/partition/saleCount`,
    method: 'get',
    params
  });
};
/**
 * 大用户占比
 * @param params
 */
export const GetPartitionBigUserWaterPercent = (params: any) => {
  return request({
    url: `/api/spp/dma/partition/bigUserRate`,
    method: 'get',
    params
  });
};
/**
 * 查询分区监控
 * @param params
 * @returns
 */
export const GetPartitionMonitoring = (params: { partitionId: string }) => {
  return request({
    url: `/api/spp/dma/partition/monitor`,
    method: 'get',
    params
  });
};
/**
 * 查询分区上月流量曲线
 * @param params
 * @returns
 */
export const GetPartitionLastMonthFlow = (params: { partitionId: string }) => {
  return request({
    url: `/api/spp/dma/partition/monthDetailFlow`,
    method: 'get',
    params
  });
};
/**
 * 查询指定设备列表的流量或压力曲线
 * @param params
 * @returns
 */
export const GetPartitionDeviceFlowOrPressure = (
  params: IQueryPartitionDeviceFlowOrPressureProperties
) => {
  return request({
    url: '/api/spp/partitionTotalFlow/flowAnalysis',
    method: 'post',
    data: params
  });
};
export type IQueryPartitionDeviceFlowOrPressureProperties =
  Partial<IQueryPagerParams> & {
    deviceIdList?: string[];
    type?: string;
    interval?: string;
    start?: number;
    end?: number;
    month?: string;
    year?: string;
    queryType?: 'flow' | 'totalFlow' | 'pressure';
  };
/**
 * 查询指定设备列表的流量或压力曲线
 * @param params
 * @returns
 */
export const ExportPartitionDeviceFlowOrPressure = (
  params: IQueryPartitionDeviceFlowOrPressureProperties
) => {
  return request({
    url: '/api/spp/partitionTotalFlow/flowAnalysisExport',
    method: 'post',
    data: params,
    responseType: 'blob'
  });
};

/**
 * 大屏-智慧生产-出厂流量
 * @param params
 * @returns
 */
export const GetFactoryFlowList = (params: {
  start: string;
  end: string;
  queryType: 'day' | string;
}) => {
  return request({
    url: '/istar/api/customer/report/waterPlantSupplyReport',
    method: 'get',
    params
  });
};

/**
 * 大屏-智慧生产-出厂压力
 * @param params
 * @returns
 */
export const GetFactoryPressureList = (params: {
  start: string;
  end: string;
  queryType: 'day' | string;
}) => {
  return request({
    url: '/istar/api/customer/report/waterPlantSupplyPressureReport',
    method: 'get',
    params
  });
};
