<template>
  <div class="one-map-detail">
    <div
      style="
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: space-around;
      "
    >
      <video controls>
        <source
          src=""
          type="video/webm"
        />
        <source
          src=""
          type="video/mp4"
        />
        Sorry, your browser doesn't support embedded videos.
      </video>
      <video controls>
        <source
          src=""
          type="video/webm"
        />
        <source
          src=""
          type="video/mp4"
        />
        Sorry, your browser doesn't support embedded videos.
      </video>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { IECharts } from '@/plugins/echart'

const emit = defineEmits(['refresh', 'mounted'])
const refChart2 = ref<IECharts>()

const refreshDetail = async row => {
  console.log(row)
}
defineExpose({
  refreshDetail
})

onMounted(() => {
  emit('mounted')
  setTimeout(() => {
    window.addEventListener('resize', resizeChart)
    refChart2.value?.resize()
  }, 3000)
})

function resizeChart() {
  refChart2.value?.resize()
}
</script>
<style lang="scss" scoped>
.one-map-detail {
  width: 100%;
  height: 100%;
}
</style>
