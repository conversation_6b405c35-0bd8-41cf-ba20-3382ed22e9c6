<template>
  <Search
    ref="refSearch"
    style="padding: 0"
    :config="SearchConfig"
  ></Search>
  <div class="table-box">
    <FormTable :config="TableConfig"></FormTable>
  </div>
</template>
<script lang="ts" setup>
import { ISearchIns } from '@/components/type'
import { getLastPayDetailByYhbh } from '@/api/mapservice/gisUser'

defineProps<{
  view?: __esri.MapView
}>()
const refSearch = ref<ISearchIns>()
const TableConfig = reactive<ITable>({
  dataList: [],
  height: 300,
  columns: [
    { label: '水量', prop: 'count' },
    { label: '收费金额', prop: 'fee' },
    { label: '收费时间', prop: 'time' }
  ],
  pagination: {
    hide: true,
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page
      TableConfig.pagination.limit = size
      refreshData()
    }
  }
})
const SearchConfig = reactive<ISearch>({
  filters: [
    { type: 'input', label: '用户编号', field: 'yhbh' },
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          type: 'default',
          loading: () => TableConfig.loading === true,
          click: () => refreshData()
        },
        {
          perm: true,
          type: 'danger',
          text: '导出',
          disabled: () => TableConfig.loading === true,
          click: () => {
            //
          }
        }
      ]
    }
  ]
})
const refreshData = async () => {
  TableConfig.loading = true
  try {
    const { yhbh } = refSearch.value?.queryParams || {}
    const res = await getLastPayDetailByYhbh(yhbh)
    TableConfig.dataList = res.data
  } catch (error) {
    console.dir(error)
  }
  TableConfig.loading = false
}
</script>
<style lang="scss" scoped>
.table-box {
  height: calc(100% - 50px);
}
</style>
