package org.thingsboard.server.dao.model.sql.smartOperation.project;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseUsername;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.util.Date;


@Getter
@Setter
@ResponseEntity
public class SoProjectAccept {
    // id
    private String id;

    // 所属项目编号
    private String projectCode;

    // 所属项目名称
    @TableField(exist = false)
    private String projectName;

    // 所属项目启动时间
    @TableField(exist = false)
    private Date projectStartTime;

    // 总验时间
    private Date acceptTime;

    // 总验说明
    private String remark;

    // 附件信息
    private String attachments;

    // 创建人
    @ParseUsername
    private String creator;

    // 创建时间
    private Date createTime;

    // 最后更新用户
    @ParseUsername
    private String updateUser;

    // 最后更新时间
    private Date updateTime;

    // 客户id
    private String tenantId;

}

