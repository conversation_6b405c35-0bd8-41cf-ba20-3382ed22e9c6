"use strict";(self.webpackChunkRemoteClient=self.webpackChunkRemoteClient||[]).push([[9880],{99880:(s,e,t)=>{t.d(e,{V:()=>h});var n=t(68773),a=(t(3172),t(20102)),o=t(92604),i=t(17452);const r=o.Z.getLogger("esri.assets");function h(s){if(!n.Z.assetsPath)throw r.errorOnce("The API assets location needs to be set using config.assetsPath. More information: https://arcg.is/1OzLe50"),new a.Z("assets:path-not-set","config.assetsPath is not set");return(0,i.v_)(n.Z.assetsPath,s)}}}]);