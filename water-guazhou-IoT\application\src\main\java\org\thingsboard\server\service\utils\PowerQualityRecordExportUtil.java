package org.thingsboard.server.service.utils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import freemarker.template.Configuration;
import freemarker.template.Template;
import org.apache.catalina.util.URLEncoder;
import org.apache.commons.io.IOUtils;
import org.thingsboard.server.dao.model.sql.PowerQualityRecordEntity;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

/**
 * 电能质量报告导出
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2021-02-20
 */
public class PowerQualityRecordExportUtil {

    public static void createDoc(Map<String, String> params, PowerQualityRecordEntity powerQualityRecordEntity, HttpServletResponse response) throws IOException {

        Configuration configuration = new Configuration(Configuration.VERSION_2_3_23);
        configuration.setDefaultEncoding("utf-8");

        // 要填入模本的数据文件
        // 获取参数
        String title = params.get("title");
        String start = params.get("startTime");
        String end = params.get("endTime");
        String voltage = params.get("voltage");
        String St = params.get("St");
        String Si = params.get("Si");
        String Sk1 = params.get("Sk1");
        String Sk2 = params.get("Sk2");
        String Sc = params.get("Sc");
        String username = params.get("username");// 用户名称
        String station = params.get("station");// 变电站名称
        String position = params.get("position");// 安装位置
        String identifier = params.get("identifier");// 终端编号
        String threeVoltageCurve = params.get("threeVoltageCurve");// 三相电压曲线
        String threeCurrentCurve = params.get("threeCurrentCurve");// 三相电流曲线
        String threePowerCurve = params.get("threePowerCurve");// 三相有功曲线
        String threeNonPowerCurve = params.get("threeNonPowerCurve");// 三相无功曲线
        String threePowerFactorCurve = params.get("threePowerFactorCurve");// 三相功率因数
        String threeVoltageHarmonicCurve = params.get("threeVoltageHarmonicCurve");// 三相电压谐波
        String threeCurrentHarmonicCurve = params.get("threeCurrentHarmonicCurve");// 三相电流谐波
        String threeVoltageWave = params.get("threeVoltageWave");// 三相电压波形
        String threeCurrentWave = params.get("threeCurrentWave");// 三相电流波形
        String overHarmonicCurrent = params.get("overHarmonicCurrent");// 超谐波电流曲线

        Map dataMap = new HashMap();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        dataMap.put("title", title);
        dataMap.put("date", new SimpleDateFormat("yyyy年MM月dd日").format(new Date()));
        dataMap.put("start", format.format(new Date(Long.parseLong(start))));
        dataMap.put("end", format.format(new Date(Long.parseLong(end))));

        dataMap.put("username", username);
        dataMap.put("substation", station);
        dataMap.put("position", position);
        dataMap.put("identifier", identifier);
        dataMap.put("voltage", voltage);
        dataMap.put("St", St);
        dataMap.put("Si", Si);
        dataMap.put("Sk1", Sk1);
        dataMap.put("Sk2", Sk2);
        dataMap.put("Sc", Sc);

        JSONObject jsonObject = JSONObject.parseObject(powerQualityRecordEntity.getPowerQualityDetail());
        // testData02处理
        JSONArray testData02 = jsonObject.getObject("testData02", JSONArray.class);
        JSONObject o = (JSONObject) testData02.get(6);
        o.put("remarks", 2);
        testData02.set(6, o);
        jsonObject.put("testData02", testData02);

        // testData03~08处理
        for (int i = 3; i < 9; i++) {
            String key = "testData0" + i;
            JSONArray testData = jsonObject.getObject(key, JSONArray.class);
            for (int j = 0; j < testData.size(); j++) {
                JSONObject object = (JSONObject) testData.get(j);
                if (object.get("remarks") == null || object.get("remarks").equals("")) {
                    object.put("remarks", 2);
                    testData.set(j, object);
                }
            }
            jsonObject.put(key, testData);
        }
        setScale(jsonObject);

        dataMap.put("powerQualityDetail", jsonObject);

        // 测试总结1
        JSONArray testData01 = jsonObject.getJSONArray("testData01");
        Double maxVoltageA = ((JSONObject) testData01.get(0)).getDouble("maxValue") == null ? 0 : ((JSONObject) testData01.get(0)).getDouble("maxValue");
        Double maxVoltageB = ((JSONObject) testData01.get(1)).getDouble("maxValue") == null ? 0 : ((JSONObject) testData01.get(1)).getDouble("maxValue");
        Double maxVoltageC = ((JSONObject) testData01.get(2)).getDouble("maxValue") == null ? 0 : ((JSONObject) testData01.get(2)).getDouble("maxValue");
        Double maxVoltage = BigDecimal.valueOf(maxVoltageA > (maxVoltageB = maxVoltageB > maxVoltageC ? maxVoltageB : maxVoltageC) ? maxVoltageA : maxVoltageB).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
        Double minVoltageA = ((JSONObject) testData01.get(0)).getDouble("minValue") == null ? 0 : ((JSONObject) testData01.get(0)).getDouble("minValue");
        Double minVoltageB = ((JSONObject) testData01.get(1)).getDouble("minValue") == null ? 0 : ((JSONObject) testData01.get(1)).getDouble("minValue");
        Double minVoltageC = ((JSONObject) testData01.get(2)).getDouble("minValue") == null ? 0 : ((JSONObject) testData01.get(2)).getDouble("minValue");
        Double minVoltage = BigDecimal.valueOf(minVoltageA < (minVoltageB = minVoltageB < minVoltageC ? minVoltageB : minVoltageC) ? minVoltageA : minVoltageB).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
        Double avgVoltageA = ((JSONObject) testData01.get(0)).getDouble("avgValue") == null ? 0 : ((JSONObject) testData01.get(0)).getDouble("avgValue");
        Double avgVoltageB = ((JSONObject) testData01.get(1)).getDouble("avgValue") == null ? 0 : ((JSONObject) testData01.get(1)).getDouble("avgValue");
        Double avgVoltageC = ((JSONObject) testData01.get(2)).getDouble("avgValue") == null ? 0 : ((JSONObject) testData01.get(2)).getDouble("avgValue");
        Double avgVoltage = BigDecimal.valueOf((avgVoltageA + avgVoltageB + avgVoltageC) / 3).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();

        dataMap.put("maxVoltageValue", maxVoltage);
        dataMap.put("minVoltageValue", minVoltage);
        dataMap.put("avgVoltageValue", avgVoltage);

        // 测试总结2
        Double maxCurrentA = ((JSONObject) testData01.get(3)).getDouble("maxValue") == null ? 0 : ((JSONObject) testData01.get(3)).getDouble("maxValue");
        Double maxCurrentB = ((JSONObject) testData01.get(4)).getDouble("maxValue") == null ? 0 : ((JSONObject) testData01.get(4)).getDouble("maxValue");
        Double maxCurrentC = ((JSONObject) testData01.get(5)).getDouble("maxValue") == null ? 0 : ((JSONObject) testData01.get(5)).getDouble("maxValue");
        Double maxCurrent = BigDecimal.valueOf(maxCurrentA > (maxCurrentB = maxCurrentB > maxCurrentC ? maxCurrentB : maxCurrentC) ? maxCurrentA : maxCurrentB).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
        Double minCurrentA = ((JSONObject) testData01.get(3)).getDouble("minValue") == null ? 0 : ((JSONObject) testData01.get(3)).getDouble("minValue");
        Double minCurrentB = ((JSONObject) testData01.get(4)).getDouble("minValue") == null ? 0 : ((JSONObject) testData01.get(4)).getDouble("minValue");
        Double minCurrentC = ((JSONObject) testData01.get(5)).getDouble("minValue") == null ? 0 : ((JSONObject) testData01.get(5)).getDouble("minValue");
        Double minCurrent = BigDecimal.valueOf(minCurrentA < (minCurrentB = minCurrentB < minCurrentC ? minCurrentB : minCurrentC) ? minCurrentA : minCurrentB).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
        Double avgCurrentA = ((JSONObject) testData01.get(3)).getDouble("avgValue") == null ? 0 : ((JSONObject) testData01.get(3)).getDouble("avgValue");
        Double avgCurrentB = ((JSONObject) testData01.get(4)).getDouble("avgValue") == null ? 0 : ((JSONObject) testData01.get(4)).getDouble("avgValue");
        Double avgCurrentC = ((JSONObject) testData01.get(5)).getDouble("avgValue") == null ? 0 : ((JSONObject) testData01.get(5)).getDouble("avgValue");
        Double avgCurrent = BigDecimal.valueOf((avgCurrentA + avgCurrentB + avgCurrentC) / 3).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();

        dataMap.put("maxCurrentValue", maxCurrent);
        dataMap.put("minCurrentValue", minCurrent);
        dataMap.put("avgCurrentValue", avgCurrent);

        // 测试总结3
        Double maxPowerA = ((JSONObject) testData01.get(6)).getDouble("maxValue") == null ? 0 : ((JSONObject) testData01.get(6)).getDouble("maxValue");
        Double maxPowerB = ((JSONObject) testData01.get(7)).getDouble("maxValue") == null ? 0 : ((JSONObject) testData01.get(7)).getDouble("maxValue");
        Double maxPowerC = ((JSONObject) testData01.get(8)).getDouble("maxValue") == null ? 0 : ((JSONObject) testData01.get(8)).getDouble("maxValue");
        Double maxPower = BigDecimal.valueOf(maxPowerA + maxPowerB + maxPowerC).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
        Double minPowerA = ((JSONObject) testData01.get(6)).getDouble("minValue") == null ? 0 : ((JSONObject) testData01.get(6)).getDouble("minValue");
        Double minPowerB = ((JSONObject) testData01.get(7)).getDouble("minValue") == null ? 0 : ((JSONObject) testData01.get(7)).getDouble("minValue");
        Double minPowerC = ((JSONObject) testData01.get(5)).getDouble("minValue") == null ? 0 : ((JSONObject) testData01.get(8)).getDouble("minValue");
        Double minPower = BigDecimal.valueOf(minPowerA + minPowerB + minPowerC).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
        Double avgPowerA = ((JSONObject) testData01.get(6)).getDouble("avgValue") == null ? 0 : ((JSONObject) testData01.get(6)).getDouble("avgValue");
        Double avgPowerB = ((JSONObject) testData01.get(7)).getDouble("avgValue") == null ? 0 : ((JSONObject) testData01.get(7)).getDouble("avgValue");
        Double avgPowerC = ((JSONObject) testData01.get(8)).getDouble("avgValue") == null ? 0 : ((JSONObject) testData01.get(8)).getDouble("avgValue");
        Double avgPower = BigDecimal.valueOf(avgPowerA + avgPowerB + avgPowerC).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
        dataMap.put("maxPowerValue", maxPower);
        dataMap.put("minPowerValue", minPower);
        dataMap.put("avgPowerValue", avgPower);

        // 测试总结4
        Double maxNonPowerA = ((JSONObject) testData01.get(9)).getDouble("maxValue") == null ? 0 : ((JSONObject) testData01.get(9)).getDouble("maxValue");
        Double maxNonPowerB = ((JSONObject) testData01.get(10)).getDouble("maxValue") == null ? 0 : ((JSONObject) testData01.get(10)).getDouble("maxValue");
        Double maxNonPowerC = ((JSONObject) testData01.get(11)).getDouble("maxValue") == null ? 0 : ((JSONObject) testData01.get(11)).getDouble("maxValue");
        Double maxNonPower = BigDecimal.valueOf(maxNonPowerA + maxNonPowerB + maxNonPowerC).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
        Double minNonPowerA = ((JSONObject) testData01.get(9)).getDouble("minValue") == null ? 0 : ((JSONObject) testData01.get(9)).getDouble("minValue");
        Double minNonPowerB = ((JSONObject) testData01.get(10)).getDouble("minValue") == null ? 0 : ((JSONObject) testData01.get(10)).getDouble("minValue");
        Double minNonPowerC = ((JSONObject) testData01.get(11)).getDouble("minValue") == null ? 0 : ((JSONObject) testData01.get(11)).getDouble("minValue");
        Double minNonPower = BigDecimal.valueOf(minNonPowerA + minNonPowerB + minNonPowerC).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
        Double avgNonPowerA = ((JSONObject) testData01.get(9)).getDouble("avgValue") == null ? 0 : ((JSONObject) testData01.get(9)).getDouble("avgValue");
        Double avgNonPowerB = ((JSONObject) testData01.get(10)).getDouble("avgValue") == null ? 0 : ((JSONObject) testData01.get(10)).getDouble("avgValue");
        Double avgNonPowerC = ((JSONObject) testData01.get(11)).getDouble("avgValue") == null ? 0 : ((JSONObject) testData01.get(11)).getDouble("avgValue");
        Double avgNonPower = BigDecimal.valueOf(avgNonPowerA + avgNonPowerB + avgNonPowerC).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
        dataMap.put("maxNonPowerValue", maxNonPower);
        dataMap.put("minNonPowerValue", minNonPower);
        dataMap.put("avgNonPowerValue", avgNonPower);


        // 测试总结5 测试期间功率因数平均值为
        Double avgPowerFactor = avgPower / Math.sqrt(Math.pow(avgPower, 2) + Math.pow(avgNonPower, 2));
        dataMap.put("avgPowerFactorValue", new BigDecimal(avgPowerFactor).setScale(2, BigDecimal.ROUND_HALF_UP));

        // 测试总结10 谐波电流
        Integer isQualified03 = jsonObject.getObject("isQualified03", Integer.class);
        Integer isQualified04 = jsonObject.getObject("isQualified04", Integer.class);
        Integer isQualified05 = jsonObject.getObject("isQualified05", Integer.class);
        if (isQualified03 != 1 || isQualified04 != 1 || isQualified05 != 1) {
            dataMap.put("harmonicIsQualified", 0);
        } else {
            dataMap.put("harmonicIsQualified", 1);
        }

        // 测试总结11 谐波电压
        JSONArray testData06 = jsonObject.getJSONArray("testData06");
        Double totalA = Double.valueOf(String.valueOf(((Map) testData06.get(50)).get("probMaxValue")));
        dataMap.put("voltageTotalAHarmonic", totalA);

        Integer isQualified06 = jsonObject.getObject("isQualified06", Integer.class);
        if (isQualified06 != 1) {
            dataMap.put("totalHarmonicAIsQualified", 0);
        } else {
            dataMap.put("totalHarmonicAIsQualified", 1);
        }

        JSONArray testData07 = jsonObject.getJSONArray("testData07");
        Double totalB = Double.valueOf(String.valueOf(((Map) testData07.get(50)).get("probMaxValue")));
        dataMap.put("voltageTotalBHarmonic", totalB);
        Integer isQualified07 = jsonObject.getObject("isQualified07", Integer.class);
        if (isQualified07 != 1) {
            dataMap.put("totalHarmonicBIsQualified", 0);
        } else {
            dataMap.put("totalHarmonicBIsQualified", 1);
        }

        JSONArray testData08 = jsonObject.getJSONArray("testData08");
        Double totalC = Double.valueOf(String.valueOf(((Map) testData08.get(50)).get("probMaxValue")));
        dataMap.put("voltageTotalCHarmonic", totalC);
        Integer isQualified08 = jsonObject.getObject("isQualified08", Integer.class);
        if (isQualified08 != 1) {
            dataMap.put("totalHarmonicCIsQualified", 0);
        } else {
            dataMap.put("totalHarmonicCIsQualified", 1);
        }


        // 图片处理
        dataMap.put("threeVoltageCurve", threeVoltageCurve.substring(threeVoltageCurve.indexOf("base64") + 7));
        dataMap.put("threeCurrentCurve", threeCurrentCurve.substring(threeCurrentCurve.indexOf("base64") + 7));
        dataMap.put("threePowerCurve", threePowerCurve.substring(threePowerCurve.indexOf("base64") + 7));
        dataMap.put("threeNonPowerCurve", threeNonPowerCurve.substring(threeNonPowerCurve.indexOf("base64") + 7));
        dataMap.put("threePowerFactorCurve", threePowerFactorCurve.substring(threePowerFactorCurve.indexOf("base64") + 7));
        dataMap.put("threeVoltageHarmonicCurve", threeVoltageHarmonicCurve.substring(threeVoltageHarmonicCurve.indexOf("base64") + 7));
        dataMap.put("threeCurrentHarmonicCurve", threeCurrentHarmonicCurve.substring(threeCurrentHarmonicCurve.indexOf("base64") + 7));
        dataMap.put("threeVoltageWave", threeVoltageWave.substring(threeVoltageWave.indexOf("base64") + 7));
        dataMap.put("threeCurrentWave", threeCurrentWave.substring(threeCurrentWave.indexOf("base64") + 7));
        dataMap.put("overHarmonicCurrent", overHarmonicCurrent.substring(overHarmonicCurrent.indexOf("base64") + 7));

        // 设置模本装置方法和路径,FreeMarker支持多种模板装载方法。可以重servlet，classpath，数据库装载，
        // 这里我们的模板是放在com.ftl包下面
        configuration.setClassForTemplateLoading(PowerQualityRecordExportUtil.class, "/templates");
        Template template = null;
        // 输出文档路径及名称
        File outFile = File.createTempFile(System.currentTimeMillis() + "", ".doc");
        Writer out = null;

        try {
            // test.ftl为要装载的模板
            template = configuration.getTemplate("PowerReportTemplate.ftl");
            template.setEncoding("utf-8");
            out = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(outFile), "utf-8"));
            template.process(dataMap, out);
            response.setContentType("application/octet-stream; filename=" + URLEncoder.DEFAULT.encode(title + ".docx", "utf-8"));
            response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.DEFAULT.encode(title + ".doc", "utf-8"));
            FileInputStream fis = new FileInputStream(outFile);
            IOUtils.copy(fis, response.getOutputStream());
            response.flushBuffer();
            out.close();
            outFile.delete();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 截取两位小数
     *
     * @param jsonObject
     */
    private static void setScale(JSONObject jsonObject) {
        Iterator<String> iterator = jsonObject.keySet().iterator();
        while (iterator.hasNext()) {
            String key = iterator.next();
            if (jsonObject.get(key) == null) {
                jsonObject.put(key, 0);
                continue;
            }
            if (jsonObject.get(key).getClass() == BigDecimal.class) {
                jsonObject.put(key, jsonObject.getBigDecimal(key).setScale(2, BigDecimal.ROUND_HALF_UP));
            }
            if (jsonObject.get(key) == JSONObject.class) {
                setScale(jsonObject.getJSONObject(key));
            }
            if (jsonObject.get(key).getClass() == JSONArray.class) {
                JSONArray jsonArray = jsonObject.getJSONArray(key);
                for (int i = 0; i < jsonArray.size(); i++) {
                    JSONObject jsonObject1 = jsonArray.getJSONObject(i);
                    setScale(jsonObject1);
                    jsonArray.set(i, jsonObject1);
                }
            }
        }
    }
}
