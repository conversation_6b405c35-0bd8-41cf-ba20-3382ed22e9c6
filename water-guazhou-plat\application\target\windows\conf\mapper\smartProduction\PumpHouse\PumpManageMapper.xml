<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartProduction.pumpHouse.PumpManageMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->id,
                           pump_room_id,
                           (select code from sp_pump_house_storage where id = pump_room_id) pump_room_code,
                           (select name from sp_pump_house_storage where id = pump_room_id) pump_room_name,
                           code,
                           name,
                           nickname,
                           pump_num,
                           company_name,
                           model,
                           install_user_name,
                           install_date,
                           performance_parameters,
                           creator,
                           create_time,
                           remark,
                           tenant_id<!--@sql from sp_pump_manage -->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.smartProduction.pumpHouse.PumpManage">
        <result column="id" property="id"/>
        <result column="pump_room_id" property="pumpRoomId"/>
        <result column="pump_room_code" property="pumpRoomCode"/>
        <result column="pump_room_name" property="pumpRoomName"/>
        <result column="code" property="code"/>
        <result column="name" property="name"/>
        <result column="nickname" property="nickname"/>
        <result column="pump_num" property="pumpNum"/>
        <result column="company_name" property="companyName"/>
        <result column="model" property="model"/>
        <result column="install_user_name" property="installUserName"/>
        <result column="install_date" property="installDate"/>
        <result column="performance_parameters" property="performanceParameters"/>
        <result column="remark" property="remark"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sp_pump_manage
        <where>
            <if test="pumpRoomId != null and pumpRoomId != ''">
                and pump_room_id = #{pumpRoomId}
            </if>
            <if test="pumpRoomName != null and pumpRoomName != ''">
                and (select name like '%' || #{pumpRoomName} || '%' from sp_pump_house_storage where id = pump_room_id)
            </if>
            <if test="pumpRoomCode != null and pumpRoomCode != ''">
                and (select code like '%' || #{pumpRoomCode} || '%' from sp_pump_house_storage where id = pump_room_id)
            </if>
            <if test="code != null and code != ''">
                and code like '%' || #{code} || '%'
            </if>
            <if test="name != null and name != ''">
                and name like '%' || #{name} || '%'
            </if>
            <if test="nickname != null and nickname != ''">
                and nickname like '%' || #{nickname} || '%'
            </if>
            <if test="pumpNum != null and pumpNum != ''">
                and pump_num =  #{pumpNum}
            </if>
            <if test="companyName != null and companyName != ''">
                and company_name like '%' || #{companyName} || '%'
            </if>
            <if test="model != null and model != ''">
                and model like '%' || #{model} || '%'
            </if>
            <if test="installUserName != null and installUserName != ''">
                and install_user_name like '%' || #{installUserName} || '%'
            </if>
            <if test="installDateFrom != null">
                and install_date >= #{installDateFrom}
            </if>
            <if test="installDateTo != null">
                and install_date &lt;= #{installDateTo}
            </if>
            <if test="fromTime != null">
                and create_time >= #{fromTime}
            </if>
            <if test="toTime != null">
                and create_time &lt;= #{toTime}
            </if>
            and tenant_id = #{tenantId}
        </where>
        order by create_time desc
    </select>

    <update id="update">
        update sp_pump_manage
        <set>
            <if test="pumpRoomId != null">
                pump_room_id = #{pumpRoomId},
            </if>
            <if test="code != null">
                code = #{code},
            </if>
            <if test="name != null">
                name = #{name},
            </if>
            <if test="nickname != null">
                nickname = #{nickname},
            </if>
            <if test="pumpNum != null">
                pump_num = #{pumpNum},
            </if>
            <if test="companyName != null">
                company_name = #{companyName},
            </if>
            <if test="model != null">
                model = #{model},
            </if>
            <if test="installUserName != null">
                install_user_name = #{installUserName},
            </if>
            <if test="installDate != null">
                install_date = #{installDate},
            </if>
            <if test="performanceParameters != null">
                performance_parameters = #{performanceParameters},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
        </set>
        where id = #{id}
    </update>

    <insert id="saveAll">
        INSERT INTO sp_pump_manage(id,
                                   pump_room_id,
                                   code,
                                   name,
                                   nickname,
                                   pump_num,
                                   company_name,
                                   model,
                                   install_user_name,
                                   install_date,
                                   performance_parameters,
        remark,
                                   creator,
                                   create_time,
                                   tenant_id)VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (#{element.id},
             #{element.pumpRoomId},
             #{element.code},
             #{element.name},
             #{element.nickname},
             #{element.pumpNum},
             #{element.companyName},
             #{element.model},
             #{element.installUserName},
             #{element.installDate},
             #{element.performanceParameters},
             #{element.remark},
             #{element.creator},
             #{element.createTime},
             #{element.tenantId})
        </foreach>
    </insert>

    <update id="updateAll">
        update sp_pump_manage
        <set>
            pump_room_id           = valueTable.pumpRoomId,
            code                   = valueTable.code,
            name                   = valueTable.name,
            nickname               = valueTable.nickname,
            pump_num               = valueTable.pumpNum,
            company_name           = valueTable.companyName,
            model                  = valueTable.model,
            install_user_name      = valueTable.installUserName,
            install_date           = valueTable.installDate,
            performance_parameters = valueTable.performanceParameters,
            remark                 = valueTable.remark
        </set>
        FROM (
        VALUES
        <foreach collection="list" item="element" separator=",">
            (#{element.id},
             #{element.pumpRoomId},
             #{element.code},
             #{element.name},
             #{element.nickname},
             #{element.pumpNum},
             #{element.companyName},
             #{element.model},
             #{element.installUserName},
             #{element.installDate},
             #{element.performanceParameters},
             #{element.remark})
        </foreach>
        ) as valueTable(id, pumpRoomId, code, name, nickname, pumpNum, companyName, model, installUserName, installDate,
                        performanceParameters, remark)
        where id = valueTable.id
    </update>
</mapper>