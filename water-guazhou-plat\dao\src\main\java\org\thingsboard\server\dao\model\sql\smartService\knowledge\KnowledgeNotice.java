package org.thingsboard.server.dao.model.sql.smartService.knowledge;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 班组主表
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-10-26
 */
@TableName("tb_service_knowledge_notice")
@Data
public class KnowledgeNotice {
    
    @TableId
    private String id;

    private String type;

    private Date startTime;

    private Date endTime;

    private Date recoveryTime;

    private String title;

    private String area;

    private String file;

    private String content;

    private Date createTime;

    private Date updateTime;

    private String creator;

    private transient String creatorName;

    private String tenantId;
}
