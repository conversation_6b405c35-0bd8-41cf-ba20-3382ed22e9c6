package org.thingsboard.server.dao.dashChart;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.DashChartEntity;
import org.thingsboard.server.dao.sql.dashChart.JpaDashChartDao;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/3 14:05
 */
@Service
public class DashChartServiceImpl implements DashChartService {


    @Autowired
    private JpaDashChartDao dashChartDao;

    @Override
    public DashChartEntity save(DashChartEntity dashChartEntity) {
        dashChartEntity.setUpdateTime(System.currentTimeMillis());
        return dashChartDao.save(dashChartEntity);
    }

    @Override
    public List<DashChartEntity> findByTenant(String tenantId) {
        return dashChartDao.findByTenant(tenantId);
    }

    @Override
    public List<DashChartEntity> findByOriginatorId(String originatorId) {
        return dashChartDao.findByOriginatorId(originatorId);
    }

    @Override
    public boolean delete(String id) {
        return dashChartDao.delete(id);
    }

    @Override
    public boolean deleteByDashboardId(String dashboardId) {
        return dashChartDao.deleteByDashboardId(dashboardId);
    }

    @Override
    public DashChartEntity findByDashJsonId(String jsonId) {
        return dashChartDao.findByJsonId(jsonId);
    }

    @Override
    public List<DashChartEntity> findByOriginatorIdAndType(String originatorId, String type) {
        return dashChartDao.findByOriginatorIdAndType(originatorId, type);
    }

    @Override
    public List<DashChartEntity> findByOriginatorIdAndName(String originatorId, String name) {
        return dashChartDao.findByOriginatorIdAndNameLike(originatorId, "%" + name + "%");
    }
}
