import{_ as D}from"./CardTable-rdWOL4_6.js";import{_ as g}from"./CardSearch-CB_HNR-Q.js";import{G as O}from"./gisSetting-CQEP-Q3N.js";import{f as _}from"./DateFormatter-Bm9a68Ax.js";import{G as h,b as x}from"./config-DncLSA-r.js";import{d as v,c as f,r as m,bF as t,o as w,g as T,n as k,q as u,i as y}from"./index-r0dFAfgr.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";import"./index-DeAQQ1ej.js";const C={class:"wrapper"},A=v({__name:"OperateLogs",setup(G){const c=f(),i=f(),b=m({filters:[{type:"datetimerange",field:"date",label:"时间范围",shortCusts:[{text:"今天",value:()=>[t().startOf("D").toDate(),t().endOf("D").toDate()]},{text:"昨天",value:()=>[t().startOf("D").subtract(1,"day").toDate(),t().endOf("D").subtract(1,"day").toDate()]},{text:"最近三天",value:()=>[t().startOf("D").subtract(2,"d").toDate(),t().endOf("D").toDate()]},{text:"本周",value:()=>[t().startOf("week").toDate(),t().endOf("week").toDate()]},{text:"上周",value:()=>[t().startOf("w").subtract(1,"w").toDate(),t().endOf("w").subtract(1,"w").toDate()]},{text:"本月",value:()=>[t().startOf("month").toDate(),t().endOf("month").toDate()]},{text:"上月",value:()=>[t().startOf("month").subtract(1,"month").toDate(),t().endOf("month").subtract(1,"month").toDate()]},{text:"最近三月",value:()=>[t().startOf("day").add(1,"day").subtract(3,"month").toDate(),t().endOf("day").toDate()]},{text:"最近半年",value:()=>[t().startOf("day").add(1,"day").subtract(6,"month").toDate(),t().endOf("day").toDate()]},{text:"最近一年",value:()=>[t().startOf("day").add(1,"d").subtract(1,"year").toDate(),t().endOf("day").toDate()]}]},{type:"select",options:h,field:"type",label:"系统模块"},{type:"input",label:"关键字",field:"keyword"},{type:"select",label:"操作类型",field:"optionType",options:x}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",iconifyIcon:"ep:search",click:()=>p()},{perm:!0,text:"重置",type:"default",iconifyIcon:"ep:refresh",click:()=>{var e;return(e=i.value)==null?void 0:e.resetForm()}},{perm:!0,text:"导出",type:"success",iconifyIcon:"ep:download",click:()=>{var e;(e=c.value)==null||e.exportTable()}}]}]}),a=m({dataList:[],columns:[{minWidth:120,label:"功能模块",prop:"optionName"},{minWidth:220,label:"日志消息",prop:"content"},{minWidth:120,label:"操作用户",prop:"optionUserName"},{minWidth:120,label:"操作类型",prop:"optionType"},{minWidth:180,label:"操作时间",prop:"optionTime",formatter(e,o){return _(o)}}],pagination:{refreshData({page:e,size:o}){a.pagination.page=e||1,a.pagination.limit=o||20,p()}}}),p=async()=>{var e,o,n,r;a.loading=!0;try{const s=((e=i.value)==null?void 0:e.queryParams)||{},l={...s,page:a.pagination.page||1,size:a.pagination.limit||20,beginOptionTime:(o=s.date)==null?void 0:o[0],endOptionTime:(n=s.date)==null?void 0:n[1]};delete l.date;const d=((r=(await O(l)).data)==null?void 0:r.data)||{};a.dataList=d.data||[],a.pagination.total=d.total||0}catch{}a.loading=!1};return w(()=>{p()}),(e,o)=>{const n=g,r=D;return T(),k("div",C,[u(n,{ref_key:"refSearch",ref:i,config:y(b)},null,8,["config"]),u(r,{ref_key:"refTable",ref:c,class:"card-table",config:y(a)},null,8,["config"])])}}});export{A as default};
