import{s as k}from"./FeatureHelper-Da16o0mu.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import{d as x,ad as P,o as C,bA as _,g as S,n as E,c as v,a8 as y}from"./index-r0dFAfgr.js";import"./MapView-DaoQedLH.js";import"./commonProperties-DqNQ4F00.js";import{i as R,e as G}from"./IdentifyHelper-RJWmLn49.js";import{g as O}from"./LayerHelper-Cn-iiqxI.js";import"./Point-WxyopZva.js";import"./project-DUuzYgGl.js";import{s as b}from"./ToolHelper-BiiInOzB.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import"./index-0NlGN6gS.js";import"./geometryEngineBase-BhsKaODW.js";const K=x({__name:"ArcPipePick",props:{layerIds:{},autoStart:{type:Boolean},addToView:{type:Boolean}},emits:["picked","error"],setup(t,{expose:u,emit:d}){const r=d,n=t,e={view:P("view"),identifyResults:[]},o=()=>{var a;e.view&&(b("crosshair"),e.mapClick=(a=e.view)==null?void 0:a.on("click",async s=>{await w(s)}))},l=()=>{var a;(a=e.mapClick)==null||a.remove()},i=async(a,s)=>{var p,f;if(!e.view)return;e.mapExtent=e.view.extent,e.mapPoint=a;const c=n.layerIds||[];if(!c.length){console.log("请先选择要查询的图层");return}const h=R({layerIds:c||[],geometry:a,mapExtent:e.view.extent}),g=await G(window.SITE_CONFIG.GIS_CONFIG.gisService+window.SITE_CONFIG.GIS_CONFIG.gisPipeDynamicService,h);e.identifyResults=g.results||[],e.identifyResults.length>50&&(e.identifyResults.length=50);const I=e.identifyResults.map(m=>(m.feature.symbol=k(m.feature.geometry.type),m.feature));n.addToView&&(e.graphicsLayer=O(e.view,{id:"search-pick",title:"查询结果"}),(p=e.graphicsLayer)==null||p.removeAll(),(f=e.graphicsLayer)==null||f.addMany(I)),r("picked",{mapPoint:e.mapPoint,mapExtent:e.mapExtent,layerIds:n.layerIds||[],results:e.identifyResults})},w=async a=>{if(e.view)try{await i(a.mapPoint)}catch(s){r("error",s,{mapPoint:e.mapPoint,mapExtent:e.mapExtent,layerIds:n.layerIds||[]})}};return C(()=>{n.autoStart&&o()}),_(()=>{var a,s,c;(a=e.mapClick)==null||a.remove(),e.mapClick=void 0,(s=e.graphicsLayer)==null||s.removeAll(),e.graphicsLayer&&((c=e.view)==null||c.map.remove(e.graphicsLayer))}),u({startPick:o,stopPick:l}),(a,s)=>(S(),E("div"))}}),A=t=>{const u=v((t==null?void 0:t.minWidth)===void 0?48:t.minWidth),d=y(()=>!(r.value==="closed"||r.value==="opened")),r=v("");return{dialogWidth:y(()=>{var o,l,i;if(r.value==="")return((o=t==null?void 0:t.widthOption)==null?void 0:o.default)||u.value;if(r.value==="opened")return((l=t==null?void 0:t.widthOption)==null?void 0:l.opend)||460;if(r.value==="more")return((i=t==null?void 0:t.widthOption)==null?void 0:i.more)||1460;if(r.value==="closed")return u.value}),drawerLevel:r,drawerMinWidth:u,drawerAbsolute:d,beforeCollapse:(o,l,i)=>{if(l===!1)return r.value==="opened"?(r.value="closed",!0):(r.value==="more"&&(r.value="opened"),r.value==="");if(r.value===""||r.value==="closed")return r.value=i??"opened",!0;if(r.value==="opened")return r.value=i??"more",!1;if(r.value==="more")return r.value=i??"opened",!1}}},Q=A;export{K as _,Q as u};
