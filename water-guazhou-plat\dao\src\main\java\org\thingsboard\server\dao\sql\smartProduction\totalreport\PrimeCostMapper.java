package org.thingsboard.server.dao.sql.smartProduction.totalreport;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.thingsboard.server.dimain.smartproduct.totalreport.primeCost.*;

import java.util.List;

@Mapper
public interface PrimeCostMapper extends BaseMapper<PrimeCost> {
    int saveAll(List<PrimeCost> costs);

    List<PrimeCostStatisticResult> statistic(PrimeCostStatisticRequest request);

    boolean adjustYMPrice(PrimeCost primeCost);

    List<PrimeCostStatisticResultOfWaterPrice> getWaterPriceInfoList(PrimeCostStatisticRequest request);

    List<PrimeCostStatisticResultOfMedical> getMedicalPriceInfoList(PrimeCostStatisticRequest request);

    List<PrimeCostStatisticResultOfPower> getPowerPriceInfoList(PrimeCostStatisticRequest request);

}