package org.thingsboard.server.dao.response.department;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class DepartmentResponseEntity {
    // "ID"
    private String id;

    // "父级ID"
    private String parentId;

    // "部门名称"
    private String name;

    // "部门类型"
    private String type;

    // "排序，升序"
    private int orderNum;

    // "创建时间"
    private String createTime;

    // "租户ID"
    private String tenantId;
}
