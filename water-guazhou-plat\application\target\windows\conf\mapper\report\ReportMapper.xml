<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.report.ReportMapper">
    <update id="changeStatus">
        update tb_report set status = #{status}
        where id in
        <foreach collection="ids" open="(" separator="," close=")" item="item">
            #{item}
        </foreach>
    </update>

    <delete id="delete">
        delete from tb_report
        where id in
        <foreach collection="ids" open="(" separator="," close=")" item="item">
            #{item}
        </foreach>
    </delete>

    <select id="getList" resultType="org.thingsboard.server.dao.model.sql.report.Report">
        select * from tb_report a

        where a.tenant_id = #{param.tenantId}
         <if test="param.typeId != null and param.typeId != ''">
             and a.type_id = #{param.typeId}
         </if>
         <if test="param.name != null and param.name != ''">
             and a.name like '%'||#{param.name}||'%'
         </if>
         <if test="param.status != null and param.status != ''">
             and a.status = #{param.status}
         </if>
         order by a.create_time desc
    </select>
</mapper>