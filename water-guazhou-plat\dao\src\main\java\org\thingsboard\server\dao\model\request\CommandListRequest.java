package org.thingsboard.server.dao.model.request;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
public class CommandListRequest extends IstarPageRequest {

    private String optionUserId;

    private String deviceId;

    private String stationId;

    private String attr;

    @DateTimeFormat(pattern = "yyyy-MM-ss HH:mm:ss")
    private Date beginTime;

    @DateTimeFormat(pattern = "yyyy-MM-ss HH:mm:ss")
    private Date endTime;

    private String tenantId;

}
