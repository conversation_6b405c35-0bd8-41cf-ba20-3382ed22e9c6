import{_ as T}from"./TreeBox-DDD2iwoR.js";import{d as L,M as D,c as u,a9 as f,a8 as _,s as N,r as i,o as G,g as V,h as E,F as g,q as s,i as p,b6 as F,b7 as P}from"./index-r0dFAfgr.js";import{_ as O}from"./CardTable-rdWOL4_6.js";import{_ as j}from"./CardSearch-CB_HNR-Q.js";import{_ as q}from"./index-BJ-QPYom.js";import{I as h}from"./common-CvK_P_ao.js";import{h as w}from"./ledgerManagement-CkhtRd8m.js";import{c as z}from"./equipmentManage-DuoY00aj.js";import{g as B,b as M}from"./equipmentOutStock-BiNkB8x8.js";import"./index-C9hz-UZb.js";import"./Search-NSrhrIa_.js";const ee=L({__name:"index",setup(R){const{$btnPerms:b}=D(),m=u(),c=u(),y=u({filters:[{label:"设备编码",field:"serialId",type:"input"},{label:"设备名称",field:"name",type:"input"},{label:"设备型号",field:"model",type:"input"},{label:"仓库名称",field:"storeId",type:"select-tree",checkStrictly:!0,autoFillOptions:e=>{B({page:1,size:99999}).then(t=>{e.options=f(t.data.data.data||[])})},onChange:e=>l.getGoodsShelfValue(e)},{label:"货架",field:"shelvesId",type:"select-tree",checkStrictly:!0,options:_(()=>l.GoodsShelf)}],operations:[{type:"btn-group",btns:[{perm:!0,text:"查询",icon:h.QUERY,click:()=>n()},{type:"default",perm:!0,text:"重置",svgIcon:N(P),click:()=>{var e;(e=c.value)==null||e.resetForm(),n()}}]}]}),a=i({defaultExpandAll:!0,indexVisible:!0,columns:[{label:"设备编码",prop:"serialId"},{label:"设备名称",prop:"name"},{label:"设备型号",prop:"model"},{label:"所属大类",prop:"topType"},{label:"所属类别",prop:"type"},{label:"仓库",prop:"storehouseName"},{label:"货架",prop:"shelvesName"},{label:"当前在库量",prop:"count"}],operationWidth:"160px",operations:[{type:"primary",text:"查看标签",icon:h.DETAIL,perm:b("RoleManageEdit"),click:e=>v(e)}],dataList:[],pagination:{page:1,limit:20,total:0,refreshData:({page:e,size:t})=>{a.pagination.page=e,a.pagination.limit=t,n()}}}),d=i({title:"详情",labelWidth:"100px",defaultValue:{},group:[{fields:[{type:"table",field:"drive",config:{height:"600px",indexVisible:!0,dataList:_(()=>l.deviceList),columns:[{label:"标签编码",prop:"deviceLabelCode"},{label:"入库单编号",prop:"storeInCode"},{label:"供应商",prop:"supplierName"},{label:"仓库编码",prop:"storehouseCode"},{label:"仓库名称",prop:"storehouseName"},{label:"货架编码",prop:"shelvesCode"},{label:"货架名称",prop:"shelvesName"}],pagination:{hide:!0}}}]}]}),r=i({title:" ",data:[],currentProject:{},expandOnClickNode:!1,isFilterTree:!0,treeNodeHandleClick:e=>{r.currentProject=e,n()}}),v=e=>{var t;d.title="详情",l.deviceList=e.restDeviceInfos,d.defaultValue={...e||{}},(t=m.value)==null||t.openDrawer()},l=i({deviceList:[],GoodsShelf:[],getGoodsShelfValue:e=>{M({page:1,size:99999,id:e}).then(o=>{l.GoodsShelf=f(o.data.data.data||[])})}}),n=async()=>{var t;const e={size:a.pagination.limit,page:a.pagination.page,inStoreOnly:!0,deviceTypeId:r.currentProject.id,...((t=c.value)==null?void 0:t.queryParams)||{}};w(e).then(o=>{a.dataList=o.data.data.data||[],a.pagination.total=o.data.data.total||0})};function S(){z().then(e=>{r.data=f(e.data.data||[]),r.currentProject=e.data.data[0],n()})}return G(async()=>{S()}),(e,t)=>{const o=q,x=j,C=O,k=F,I=T;return V(),E(I,null,{tree:g(()=>[s(o,{"tree-data":p(r)},null,8,["tree-data"])]),default:g(()=>[s(x,{ref_key:"refSearch",ref:c,config:p(y)},null,8,["config"]),s(C,{config:p(a),class:"card-table"},null,8,["config"]),s(k,{ref_key:"refForm",ref:m,config:p(d)},null,8,["config"])]),_:1})}}});export{ee as default};
