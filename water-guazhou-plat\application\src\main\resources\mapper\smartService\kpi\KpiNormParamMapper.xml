<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.smartService.kpi.KpiNormParamMapper">
    <select id="getList" resultType="org.thingsboard.server.dao.model.sql.smartService.kpi.KpiNormParam">
        select a.*
        from tb_service_kpi_norm_param a
        where a.source like '%'|| #{source}||'%'
        and a.type like '%'|| #{type}||'%'
        and a.code like '%'|| #{code}||'%'
        and a.name like '%'|| #{name}||'%'
        <if test="source == '1'">
            and a.tenant_id = #{tenantId}
        </if>
        <if test="enabled != null">
            and a.enabled = #{enabled}
        </if>
        order by a.order_num, a.create_time desc
        offset (#{page} - 1) * #{size}  limit #{size}
    </select>

    <select id="getListCount" resultType="int">
        select count(*)
        from tb_service_kpi_norm_param a
        where a.source like '%'|| #{source}||'%'
        and a.type like '%'|| #{type}||'%'
        and a.code like '%'|| #{code}||'%'
        and a.name like '%'|| #{name}||'%'
        <if test="source == '1'">
            and a.tenant_id = #{tenantId}
        </if>
        <if test="enabled != null">
            and a.enabled = #{enabled}
        </if>
    </select>
</mapper>