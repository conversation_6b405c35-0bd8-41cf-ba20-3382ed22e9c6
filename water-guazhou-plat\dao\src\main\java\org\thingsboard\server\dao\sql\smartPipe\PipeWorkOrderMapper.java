package org.thingsboard.server.dao.sql.smartPipe;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.DTO.PipeWorkOrderDTO;
import org.thingsboard.server.dao.model.request.PipeWorkOrderRequest;
import org.thingsboard.server.dao.model.sql.smartPipe.dma.PipeWorkOrder;

/**
 *
 * @<NAME_EMAIL>
 * @since v1.0.0 2023-05-26
 */
@Mapper
public interface PipeWorkOrderMapper extends BaseMapper<PipeWorkOrder> {

    IPage<PipeWorkOrderDTO> getList(IPage<PipeWorkOrderDTO> page, @Param("req") PipeWorkOrderRequest request);

}
