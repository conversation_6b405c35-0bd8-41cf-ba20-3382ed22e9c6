package org.thingsboard.server.dao.sql.zutai;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.thingsboard.server.dao.model.sql.zutai.DashboardListEntity;

import java.util.List;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2021-06-15
 */
public interface DashboardListRepository extends CrudRepository<DashboardListEntity, String> {

    @Query(value = "select * from zutai_dashboard_list  where id like ?1 and name like ?2 and project_id like ?3 and tenant_id like ?4 order by create_time desc offset ?5 limit ?6", nativeQuery = true)
    List<DashboardListEntity> findList(String id, String name, String projectId, String tenantId, Integer skip, Integer size);

    @Query(value = "select count(*) from zutai_dashboard_list  where id like ?1 and name like ?2 and project_id like ?3 and tenant_id like ?4", nativeQuery = true)
    int findListCount(String id, String name, String projectId, String tenantId);
}
