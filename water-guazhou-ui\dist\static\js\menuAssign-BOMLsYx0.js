import{C as h,M as f,b8 as m,b9 as g,ba as k,h as _,F as a,an as C,L as b,g as v,p as i,q as o,G as l,bb as I,J as M}from"./index-r0dFAfgr.js";import{c as y}from"./index-CaaU9niG.js";const{$messageSuccess:x}=f(),T={name:"MenuAssign",props:["config"],data(){return{treeData:[],defaultProps:{children:"children",label:"label"},appTypeId:""}},computed:{visible(){return this.config.visible}},created(){this.getList()},methods:{getList(){this.appTypeId=this.config.currentId,y().then(t=>{m(t.data,"id"),this.treeData=t.data,this.getCheckItemMenu()})},getCheckItemMenu(){g(this.appTypeId).then(t=>{const e=t.data;e.length>0&&this.$refs.tree.setCheckedKeys(e)})},saveMenuAssign(){const e={data:this.$refs.tree.getCheckedKeys(),appTypeId:this.config.currentId};k(e).then(()=>{this.messageInfo()}),this.config.close()},resetChecked(){this.$refs.tree.setCheckedKeys([])},messageInfo(){x("操作成功")}}},V={class:"roles-tree-conent"},A={class:"tree-info"},B={class:"tree-footer"};function D(t,e,c,N,d,s){const r=I,n=M,u=b;return s.visible?(v(),_(u,{key:0,modelValue:s.visible,"onUpdate:modelValue":e[0]||(e[0]=p=>s.visible=p),title:"应用授权管理",width:"600px","close-on-click-modal":!1,onClose:c.config.close},{default:a(()=>[i("div",V,[i("div",A,[o(r,{ref:"tree",class:"roles-tree",data:d.treeData,"show-checkbox":"","default-expand-all":"","node-key":"id","highlight-current":"",props:d.defaultProps},null,8,["data","props"])]),i("div",B,[o(n,{type:"primary",size:"medium",onClick:s.saveMenuAssign},{default:a(()=>e[1]||(e[1]=[l(" 保 存 ")])),_:1},8,["onClick"]),o(n,{plain:"",size:"medium",onClick:s.resetChecked},{default:a(()=>e[2]||(e[2]=[l(" 清 空 ")])),_:1},8,["onClick"]),o(n,{size:"medium",onClick:c.config.close},{default:a(()=>e[3]||(e[3]=[l(" 取 消 ")])),_:1},8,["onClick"])])])]),_:1},8,["modelValue","onClose"])):C("",!0)}const E=h(T,[["render",D],["__scopeId","data-v-48a792a7"]]);export{E as default};
