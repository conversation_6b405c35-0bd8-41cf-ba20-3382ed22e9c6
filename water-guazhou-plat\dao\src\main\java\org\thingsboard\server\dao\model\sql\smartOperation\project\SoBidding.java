package org.thingsboard.server.dao.model.sql.smartOperation.project;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseUsername;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.math.BigDecimal;
import java.util.Date;


@Getter
@Setter
@ResponseEntity
public class SoBidding {
    // id
    private String id;

    // 所属项目编号
    private String projectCode;

    // 所属项目名称
    @TableField(exist = false)
    private String projectName;

    // 所属项目类型Id
    @TableField(exist = false)
    private String projectTypeId;

    // 所属项目类型名称
    @TableField(exist = false)
    private String projectTypeName;

    // 所属项目概算
    @TableField(exist = false)
    private BigDecimal projectEstimate;

    // 所属项目启动时间
    @TableField(exist = false)
    private Date projectStartTime;

    // 所属项目预计结束时间
    @TableField(exist = false)
    private Date projectExpectEndTime;

    // 所属项目负责人
    @TableField(exist = false)
    private String projectPrincipal;

    // 代理招标公司
    private String proxyBiddingCompany;

    // 中标公司id
    private String preferCompanyId;

    // 附件
    private String attachments;

    // 创建人
    @ParseUsername
    private String creator;

    // 创建时间
    private Date createTime;

    // 最后更新用户
    @ParseUsername
    private String updateUser;

    // 最后更新时间
    private Date updateTime;

    // 客户id
    private String tenantId;

}
