package org.thingsboard.server.dao.model.sql;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.TypeDef;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.util.mapping.JsonStringType;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Entity
@TypeDef(name = "json", typeClass = JsonStringType.class)
@Table(name = ModelConstants.TB_FLOW_RATE_REPORT_TABLE)
@NoArgsConstructor
@AllArgsConstructor
public class FlowRateReport {

    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = ModelConstants.TB_FLOW_RATE_REPORT_STATION_ID)
    private String stationId;

    @Column(name = ModelConstants.TB_FLOW_RATE_REPORT_TIME)
    private Date time;

    @Column(name = ModelConstants.TB_FLOW_RATE_REPORT_FLOW_RATE_AVG)
    private BigDecimal flowRateAvg;

    @Column(name = ModelConstants.TB_FLOW_RATE_REPORT_FLOW_RATE_MIN)
    private BigDecimal flowRateMin;

    @Column(name = ModelConstants.TB_FLOW_RATE_REPORT_FLOW_RATE_MIN_TS)
    private Date flowRateMinTs;

    @Column(name = ModelConstants.TB_FLOW_RATE_REPORT_FLOW_RATE_MAX)
    private BigDecimal flowRateMax;

    @Column(name = ModelConstants.TB_FLOW_RATE_REPORT_FLOW_RATE_MAX_TS)
    private Date flowRateMaxTs;

    @Column(name = ModelConstants.TB_FLOW_RATE_REPORT_LAST_TOTAL_FLOW)
    private BigDecimal lastTotalFlow;

    @Column(name = ModelConstants.TB_FLOW_RATE_REPORT_LAST_REVERSE_TOTAL_FLOW)
    private BigDecimal lastReverseTotalFlow;

    @Column(name = ModelConstants.TB_FLOW_RATE_REPORT_LAST_TOTAL_FLOW_TS)
    private Date lastTotalFlowTs;

    @Column(name = ModelConstants.TB_FLOW_RATE_REPORT_LAST_REVERSE_TOTAL_FLOW_TS)
    private Date lastReverseTotalFlowTs;

    @Column(name = ModelConstants.TB_FLOW_RATE_REPORT_TOTAL_FLOW)
    private BigDecimal totalFlow;

    @Column(name = ModelConstants.TB_FLOW_RATE_REPORT_ENERGY_IN)
    private BigDecimal energyIn;

    @Column(name = ModelConstants.TB_FLOW_RATE_REPORT_LAST_ENERGY_IN)
    private BigDecimal lastEnergyIn;

    @Column(name = ModelConstants.TB_FLOW_RATE_REPORT_LAST_ENERGY_IN_TS)
    private Date lastEnergyInTs;

    @Column(name = ModelConstants.TB_FLOW_RATE_REPORT_TOTAL_RUN_TIME)
    private BigDecimal totalRunTime;

    @Column(name = ModelConstants.TB_FLOW_RATE_REPORT_TOTAL_STOP_TIME)
    private BigDecimal totalStopTime;

    @Transient
    private BigDecimal closeTime;

    @Transient
    private String stationName;

}
