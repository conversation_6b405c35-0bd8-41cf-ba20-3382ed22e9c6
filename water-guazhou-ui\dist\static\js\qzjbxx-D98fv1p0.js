import{d as r,r as s,a8 as c,bF as n,g as d,h as p,F as f,q as m,i as u,bz as x,C as b}from"./index-r0dFAfgr.js";import{d as _}from"./index-CCFuhOrs.js";/* empty css                             */import"./DPlayer-Be2AurQX.js";import"./DPlayer.min-_HMH7IVX.js";const y=r({__name:"qzjbxx",props:{config:{}},setup(t){const a=t,o=s({defaultValue:c(()=>a.config),border:!0,direction:"horizontal",column:2,title:"签证基本信息",fields:[{type:"text",label:"签证编号:",field:"code"},{type:"text",label:"工程名称:",field:"constructionName"},{type:"text",label:"施工地点:",field:"address"},{type:"text",label:"施工单位:",field:"constructOrganization"},{type:"text",label:"施工日期:",field:"constructTimeName"},{type:"text",label:"建设单位:",field:"buildOrganization"},{type:"text",label:"监理单位:",field:"supervisorOrganization"},{type:"text",label:"审计单位:",field:"auditOrganization"},{type:"text",label:"添加人:",field:"creatorName"},{type:"text",label:"添加时间:",field:"createTime",formatter:e=>n(e).format("YYYY-MM-DD HH:mm:ss")}]});return(e,g)=>{const i=_,l=x;return d(),p(l,{class:"card"},{default:f(()=>[m(i,{config:u(o)},null,8,["config"])]),_:1})}}}),Y=b(y,[["__scopeId","data-v-ce767568"]]);export{Y as default};
