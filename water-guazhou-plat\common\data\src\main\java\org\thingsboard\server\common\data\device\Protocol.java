package org.thingsboard.server.common.data.device;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 设备协议相关
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class Protocol {
    private String id;

    private String template_id;

    private String number;

    private String property_category;

    private String name;

    private String stat_type;

    private String data_type;

    private String property_type;

    private String unit;

    private String register_type;

    private Integer function_code;

    private Long register_address;

    private Integer byte_count;

    private Integer bit_position;

    private String register_sign_flag;

    private BigDecimal sample_deviation;

    private String order;

    private String byte_order;

    private Long data_offset;

    private Long sampling_max;

    private Long sampling_min;

    private BigDecimal sample_coef;

    private BigDecimal unit_coef;

    private BigDecimal range;

    private String formulaProperty;

    private Long createTime;

}
