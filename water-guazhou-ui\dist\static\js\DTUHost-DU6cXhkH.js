import{d as Y,M as Z,c as S,r as g,dJ as ee,o as te,bo as ae,g as oe,h as le,F as H,q as b,i as y,S as v,dK as ie,dL as re,b as d,s as W,D as L,br as ne,bG as se,ah as de,al as ce,ak as ue,C as pe}from"./index-r0dFAfgr.js";import{_ as fe}from"./DialogForm.vue_vue_type_style_index_0_lang-CwVZykAN.js";import{_ as me}from"./index-C9hz-UZb.js";import{_ as ge}from"./InlineForm.vue_vue_type_style_index_0_lang-s-ANlzyw.js";import{_ as be}from"./CardTable-rdWOL4_6.js";import{_ as _e}from"./CardSearch-CB_HNR-Q.js";import{_ as ye}from"./index-BJ-QPYom.js";import{f as he,n as ve,o as De,p as Te,q as Ce,c as Se,e as Ie,d as xe}from"./index-BggOjNGp.js";import{g as ke}from"./ledger2-CVFjtR6o.js";import Pe from"./TreeBox-mfOmxwZJ.js";import"./Search-NSrhrIa_.js";const je=Y({__name:"DTUHost",setup(Fe){const{btnPerms:h}=Z(),D=S(),P=S(),I=S(),q=S(),m=g({totalLoading:!1,submitProjectType:"add"}),M=g({filters:[{label:"搜索",field:"name",type:"input"},{type:"btn-group",btns:[{perm:!0,text:"查询",click:()=>F()},{perm:h("DTUHostAdd"),text:"添加网关",click:()=>$()}]}],defaultParams:{}}),{getProjectTreeData:R}=ee(),w=()=>{const l=()=>{var t;m.submitProjectType="addRoot",a.title="新建项目",a.defaultValue={location:[116.4,39.91]},(t=D.value)==null||t.openDialog()},s=t=>{var e,o;t||(t=r.currentProject),m.submitProjectType="edit",a.defaultValue={...(t==null?void 0:t.data)||{},...((e=t==null?void 0:t.data)==null?void 0:e.additionalInfo)&&JSON.parse(t.data.additionalInfo)||{}},(o=D.value)==null||o.openDialog()},u=t=>{var e,o;m.submitProjectType="add",a.defaultValue={parentId:(t==null?void 0:t.id)||((e=r.currentProject)==null?void 0:e.id)},(o=D.value)==null||o.openDialog()},c=t=>{v("确定删除?","删除提示").then(async()=>{var e;try{const o=(t==null?void 0:t.id)||((e=r.currentProject)==null?void 0:e.id);await se(o),d.success("删除成功"),i()}catch{d.error("删除失败")}}).catch(()=>{})},i=async()=>{m.totalLoading=!0;try{const t=await R();r.data=t,r.currentProject=de(r.data||[]),F(),m.totalLoading=!1}catch{m.totalLoading=!1}},r=g({title:"区域划分",data:[],loading:!1,isFilterTree:!0,addRoot:{perm:!0,text:"新建项目",click:()=>l()},add:{perm:!0,text:"子项",click:t=>u(t)},edit:{perm:!0,text:"编辑",click:t=>s(t)},delete:{perm:!0,text:"删除",click:t=>c(t)},treeNodeHandleClick:t=>{r.currentProject=t,U.dataList=[],F()}}),a=g({dialogWidth:500,title:"新建项目",group:[{fields:[{type:"input",label:"项目名称:",field:"name",rules:[{required:!0,message:"请填写项目名称"}]},{type:"select",label:"项目类型",field:"type",options:[{label:"水司",value:"1"},{label:"水厂",value:"2"}]},{type:"textarea",label:"项目简介:",field:"introduction",aInfo:!0},{type:"avatar",aInfo:!0,label:"图片:",field:"imageUrl"},{type:"input",label:"项目地址:",aInfo:!0,field:"address",rules:[{required:!0,message:"请填写项目地址"}]},{type:"amap",label:"项目定位:",aInfo:!0,field:"location",rules:[{required:!0,message:"请输入项目定位"}]}]}],defaultValue:{},submit:t=>{v("确定提交？","确认信息").then(async()=>{var e;try{t.id?await ie(t):await re(t),d.success("提交成功"),i(),(e=D.value)==null||e.closeDialog()}catch{d.error("提交失败")}}).catch(()=>{})},submitting:!1});return{refreshTree:i,DialogFormConfig_Project:a,TreeData:r}},J=()=>{const l=()=>{var e;i.dataList=[];const a=(e=q.value)==null?void 0:e.queryParams,t={page:i.pagination.page,size:i.pagination.limit,name:a==null?void 0:a.name};Se(T.currentProject.id,"DTU",t).then(o=>{i.dataList=o.data.data.map(n=>({...n,...JSON.parse(n.additionalInfo||"")||{}})),i.pagination.total=o.data.total,!o.data||!o.data.data||!o.data.data[0]?i.dataList=[]:O(o.data.data[0])})},s=a=>{Ie(L(a.id.id),T.currentProject.id).then(t=>{l(),d.success(t.data.result)})},u=a=>{v("确定删除该网关?","删除提示").then(()=>{xe(L(a.id.id)).then(t=>{l(),d.success(t.data.result)})}).catch(()=>{})},c=a=>{var t;if(!a)r.defaultValue={location:[116.4,39.91],gateway:!0,type:"DTU",projectId:T.currentProject.id},r.title="添加网关";else{if(r.title="编辑网关",a.info)for(const e in a.info)a[`info__${e}`]=a.info[e];r.defaultValue={...a,...JSON.parse(a.additionalInfo),gateway:!0}}(t=P.value)==null||t.openDialog()},i=g({columns:[{label:"网关名称",prop:"name",width:200},{label:"网关地址",prop:"address"},{label:"网关密钥",prop:"credentialsId"},{label:"掉线判断",prop:"dropJudgement",width:100},{label:"网关定位",prop:"showLocation",width:200}],pagination:{page:1,limit:20,total:0,handleSize:a=>{i.pagination.limit=a,l()},handlePage:a=>{i.pagination.page=a,l()}},dataList:[],operationFixed:"right",handleRowClick:a=>{i.currentRow=a,O(a)},operations:[{isTextBtn:!0,text:"编辑",perm:h("DTUHostEdit"),icon:"iconfont icon-bianji",click:a=>c(a)},{isTextBtn:!0,text:"复制",perm:h("DTUHostCopy"),icon:"iconfont icon-icon_fuzhi",click:a=>s(a)},{isTextBtn:!0,text:"删除",perm:h("DTUHostDelete"),type:"danger",icon:"iconfont icon-shanchu",click:a=>u(a)}],operationWidth:"230px",indexVisible:!0}),r=g({dialogWidth:500,labelPosition:"top",title:"添加网关",group:[{fields:[{type:"input",label:"网关名称:",field:"name",rules:[{required:!0,message:"请输入网关名称"}]},{type:"input",label:"传输协议:",readonly:!0,field:"type",rules:[{required:!0,message:"请填写传输协议"}]},{type:"textarea",label:"网关简介:",aInfo:!0,field:"introduction"},{type:"input",aInfo:!0,label:"硬件ID:",field:"hardwareId",rules:[{required:!0,message:"请输入硬件ID"}]},{type:"input",label:"网关地址:",aInfo:!0,field:"address",rules:[{required:!0,message:"请填写网关地址"}]},{type:"amap",label:"网关定位:",aInfo:!0,field:"location",rules:[{required:!0,message:"请输入网关定位"}]},{type:"image",aInfo:!0,label:"网关图片:",field:"imageUrl"},{type:"select",aInfo:!0,label:"掉线判断:",field:"dropJudgement",options:[{label:"5m",value:"5m"},{label:"10m",value:"10m"},{label:"15m",value:"15m"},{label:"30m",value:"30m"},{label:"60m",value:"60m"}],rules:[{required:!0,message:"请选择掉线判断"}]}]}],defaultValue:{type:"DTU"},submit:a=>{v("确定提交？","提示信息").then(async()=>{var t;try{const e=await ve(a);console.log(e),d.success("提交成功"),l(),(t=P.value)==null||t.closeDialog()}catch{d.error("提交失败")}})}});return{TableConfig_Host:i,DialogFormConfig_Host:r,refreshHost:l,addOrEditHost:c}},j=S(),z=()=>{const l=g({columns:[{label:"采集器名称",prop:"name",minWidth:140},{label:"接入类型",prop:"accessType"},{label:"设备协议",prop:"type"},{label:"从站地址",prop:"unitId"},{label:"Modbus类型",prop:"modbusType",minWidth:120},{label:"RTU串口",prop:"portName"},{label:"TCP地址",prop:"host"},{label:"TCP端口",prop:"port"},{label:"rtuOverTcp",prop:"useRtuTcp",minWidth:140},{label:"波特率",prop:"baudRate"},{label:"Modbus 数据位",prop:"dataBits",minWidth:140},{label:"Modbus 校验位",prop:"parity",minWidth:140},{label:"Modbus 停止位",prop:"stopBits",minWidth:140}],operations:[{isTextBtn:!0,text:"复制ID",perm:!0,icon:"iconfont icon-icon_fuzhi",click:e=>r(e)},{isTextBtn:!0,text:"编辑",perm:h("DTUSensorEdit"),icon:"iconfont icon-bianji",click:e=>{var f,_;s.title="编辑采集器";const o=(f=e.location)==null?void 0:f.split(","),n=(o==null?void 0:o.length)===2?o:window.SITE_CONFIG.GIS_CONFIG.gisDefaultCenter,p={...e,...JSON.parse(e.additionalInfo||{}),location:n,gateway:!0};for(const x in e.info)p[`info__${x}`]=e.info[x];s.defaultValue=p,(_=I.value)==null||_.openDialog()}},{isTextBtn:!0,text:"协议",perm:!0,icon:"iconfont icon-icon_fuzhi",click:e=>{console.log(e)}},{isTextBtn:!0,text:"删除",type:"danger",perm:h("DTUSensorDelete"),icon:"iconfont icon-bianji",click:e=>a(e)}],operationWidth:"320px",operationFixed:"right",pagination:{page:1,limit:20,total:0,layout:"total, sizes, prev, pager, next, jumper",handleSize:e=>{l.pagination.limit=e,c()},handlePage:e=>{l.pagination.page=e,c()}},dataList:[],indexVisible:!0}),s=g({dialogWidth:500,labelPosition:"top",title:"添加采集器",group:[{fields:[{type:"input",label:"采集器名称:",field:"name",rules:[{required:!0,message:"请输入采集器名称"}]},{type:"select",label:"协议模板:",field:"templateId",options:[],rules:[{required:!0,message:"请选择"}]},{type:"select",allowCreate:!0,field:"deviceTypeName",label:"采集器类型",rules:[{required:!0,message:"请输入采集器类型"}]},{type:"input",label:"从站地址:",field:"info__unitId",rules:[{required:!0,message:"请输入从站地址"}]},{type:"radio",label:"接入类型:",field:"info__accessType",options:[{label:"232",value:"232"},{label:"485",value:"485"}],rules:[{required:!0,message:"请输入硬件ID"}]},{type:"input",label:"读取超时时间 单位：(ms):",field:"info__timeout",rules:[{required:!0,message:"请填写网关地址"}]},{type:"amap",label:"采集器定位:",field:"location",resultType:"str",rules:[{required:!0,message:"请输入采集器定位"}]},{type:"select",label:"波特率",field:"info__baudRate",options:[{label:"1200",value:1200},{label:"2400",value:2400},{label:"4800",value:4800},{label:"9600",value:9600},{label:"14400",value:1400},{label:"19200",value:19200},{label:"38400",value:38400},{label:"57600",value:57600},{label:"115200",value:115200},{label:"128000",value:128e3}],rules:[{required:!0,message:"请选择波特率"}]},{type:"select",label:"Modbus数据位",field:"info__dataBits",options:[{label:"7",value:7},{label:"8",value:8}],rules:[{required:!0,message:"请选择Modbus数据位"}]},{type:"select",label:"Modbus校验",field:"info__parity",options:[{value:"none",label:"none"},{value:"even",label:"even"},{value:"odd",label:"odd"}],rules:[{required:!0,message:"请选择Modbus校验"}]},{type:"select",label:"Modbus停止位",field:"info__stopBits",options:[{value:"1",label:"1"},{value:"2",label:"2"}],rules:[{required:!0,message:"请输入Modbus停止位"}]},{type:"select",aInfo:!0,label:"掉线判断:",field:"dropJudgement",options:[{label:"5m",value:"5m"},{label:"10m",value:"10m"},{label:"15m",value:"15m"},{label:"30m",value:"30m"},{label:"60m",value:"60m"}],rules:[{required:!0,message:"请选择掉线判断"}]}]}],defaultValue:{type:"DTU"},submit:e=>{v("确定提交？","提示信息").then(async()=>{var o,n;try{e.info={};for(const f in e)if(f.indexOf("__")!==-1){const _=f.split("__")[1];e.info[_]=e[f],delete e[f]}e.info=JSON.stringify(e.info),e.projectId=T.currentProject.id;const p=await De(e);p.data?(d.success("提交成功"),c(),(o=I.value)==null||o.closeDialog()):d.error(((n=p.data)==null?void 0:n.message)||"提交失败")}catch(p){d.error(p.message||"系统错误")}}).catch(()=>{})}}),u=g({size:"small",labelPosition:"top",defaultValue:{sensorType:"sonsor"},group:[{fields:[{itemContainerStyle:{width:"100%"},type:"tabs",label:"",field:"sensorType",tabs:[{label:"采集器",value:"sonsor"}]}]},{fields:[{type:"input",label:"",field:"sensorKeyWords"},{type:"btn-group",btns:[{perm:!0,svgIcon:W(ce),text:"查询",click:()=>c()}]},{type:"btn-group",btns:[{perm:!0,svgIcon:W(ue),text:"添加采集器",click:()=>t()}]}]}]}),c=e=>{var n,p,f;e=e||C.currentRow||C.dataList[0],l.dataList=[];const o={key:"info,prop",page:l.pagination.page,size:l.pagination.limit,name:(p=(n=j.value)==null?void 0:n.dataForm)==null?void 0:p.sensorKeyWords};console.log((f=j.value)==null?void 0:f.dataForm),Te(L(e.id.id),o).then(_=>{var N;const x=((N=_.data)==null?void 0:N.data)||[];l.dataList=x.map(k=>{const V=JSON.parse(k.info),X=JSON.parse(k.additionalInfo);return{...k,info:V,...V,...X,name:k.name}}),l.pagination.total=_.data.total||0})},i=()=>{ke().then(e=>{if(e.data){const o=s.group[0].fields.find(n=>n.field==="deviceTypeName");o&&(o.options=e.data.map(n=>({label:n,value:n})))}})},r=e=>{console.log("----------",e);const o=document.createElement("textarea");o.value=e.id.id,document.body.appendChild(o),o.select(),document.execCommand("copy"),document.body.removeChild(o),d.info("已复制设备ID")},a=e=>{v("确定删除该采集器?","删除提示").then(()=>{Ce(e.id.id).then(()=>{d.success("操作成功"),c()})}).catch(()=>{})},t=e=>{var o;{if(!C.currentRow)return d.info("请选择网关");s.defaultValue={type:"DTU",location:[116.4,39.91],gateWayId:C.currentRow.id},s.title="添加采集器"}(o=I.value)==null||o.openDialog()};return{FormConfig_SensorHeader:u,DialogFormConfig_Sendor:s,TableConfig_Sensor:l,refreshSensor:c,initDeviceType:i,copySensorId:r}},{TreeData:T,refreshTree:E,DialogFormConfig_Project:G}=w(),{TableConfig_Host:C,DialogFormConfig_Host:A,refreshHost:F,addOrEditHost:$}=J(),{FormConfig_SensorHeader:K,DialogFormConfig_Sendor:B,TableConfig_Sensor:U,initDeviceType:Q,refreshSensor:O}=z();return te(()=>{m.totalLoading=!0;try{E()}catch{m.totalLoading=!1}Q(),he("DTU").then(l=>{const s=B.group[0].fields.find(u=>u.field==="templateId");s&&(s.options=l.data.map(u=>({label:u.name,value:u.id})))})}),(l,s)=>{const u=ye,c=_e,i=be,r=ge,a=me,t=fe,e=ne;return ae((oe(),le(Pe,null,{tree:H(()=>[b(u,{"tree-data":y(T)},null,8,["tree-data"])]),default:H(()=>[b(c,{ref_key:"refCardSearch",ref:q,class:"card-search",config:M},null,8,["config"]),b(i,{config:y(C),class:"card-table"},null,8,["config"]),b(a,{class:"card"},{default:H(()=>[b(r,{ref_key:"refFormConfig_SensorHeader",ref:j,class:"search-sensor",config:y(K)},null,8,["config"]),b(i,{config:y(U),class:"card-table-bottom"},null,8,["config"])]),_:1}),b(t,{ref_key:"refDialogForm_Project",ref:D,config:y(G)},null,8,["config"]),b(t,{ref_key:"refDialogForm_Host",ref:P,config:y(A)},null,8,["config"]),b(t,{ref_key:"refDialogForm_Sensor",ref:I,config:y(B)},null,8,["config"])]),_:1})),[[e,m.totalLoading]])}}}),Je=pe(je,[["__scopeId","data-v-f1158b7b"]]);export{Je as default};
