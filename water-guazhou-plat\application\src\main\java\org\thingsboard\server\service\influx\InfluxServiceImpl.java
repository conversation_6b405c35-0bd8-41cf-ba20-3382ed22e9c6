package org.thingsboard.server.service.influx;

import com.influxdb.client.InfluxDBClient;
import com.influxdb.client.WriteApi;
import com.influxdb.client.domain.WritePrecision;
import com.influxdb.client.flux.FluxClient;
import com.influxdb.client.write.Point;
import com.influxdb.query.FluxTable;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.time.InfluxSearchTs;
import org.thingsboard.server.common.data.tsdb.DataPoint;
import org.thingsboard.server.common.data.tsdb.DataPointReload;
import org.thingsboard.server.common.data.utils.DateUtils;
import org.thingsboard.server.common.data.utils.TimeDiff;
import org.thingsboard.server.dao.influx.Flux;
import org.thingsboard.server.dao.influx.InfluxService;
import org.thingsboard.server.dao.influx.functions.restriction.Restrictions;

import java.time.Instant;
import java.util.*;

import static org.thingsboard.server.common.data.utils.DateUtils.*;


/**
 * <AUTHOR>
 * @date 2020/8/2 16:03
 */
@Slf4j
@Service
public class InfluxServiceImpl implements InfluxService {


    /**
     * 保存设备数据点
     *
     * @param dataPoints 设备数据点列表
     */
    @Override
    public void saveDeviceToInflux(List<DataPoint> dataPoints) {
        try {
            InfluxDBClient influxDBClient = InfluxFactory.createInFluxClient();
            List<Point> points = new ArrayList<>();
            dataPoints.forEach(dataPoint -> {
                points.add(Point.measurement(dataPoint.getMetric())
                        .addTag(DataConstants.ATTRIBUTE_PROP, dataPoint.getProp())
                        .addField(DataConstants.VALUE, Double.parseDouble(dataPoint.getValue()))
                        .time(Instant.ofEpochMilli(dataPoint.getTimestamp()), WritePrecision.MS));
            });
            try (WriteApi writeApi = influxDBClient.getWriteApi()) {
                writeApi.writePoints(points);
            }
            influxDBClient.close();
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }

    @Override
    public void transferToInflux(List<DataPointReload> dataPoints) {
        InfluxDBClient influxDBClient = InfluxFactory.createInFluxClient();
        List<Point> points = new ArrayList<>();
        dataPoints.forEach(dataPoint -> {
            points.add(Point.measurement(dataPoint.getMetric())
                    .addTag(DataConstants.ATTRIBUTE_PROP, dataPoint.getTags().get(DataConstants.ATTRIBUTE_PROP))
                    .addField(DataConstants.VALUE, Double.parseDouble(dataPoint.getValue()))
                    .time(Instant.ofEpochMilli(dataPoint.getTimestamp()), WritePrecision.MS));
        });
        try (WriteApi writeApi = influxDBClient.getWriteApi()) {
            writeApi.writePoints(points);
        }
        influxDBClient.close();
    }


    @Override
    public List<FluxTable> findDeviceDataFromInflux2(List<String> formulas, long start, long end) {
        FluxClient fluxClient = InfluxFactory.createFluxClient();
        Restrictions[] restrictionsArray = new Restrictions[formulas.size()];
        for (int i = 0; i < formulas.size(); i++) {
            String[] array = formulas.get(i).split("\\.");
            restrictionsArray[i] = (Restrictions.and(Restrictions.measurement().equal(array[0]), Restrictions.tag(DataConstants.ATTRIBUTE_PROP).equal(array[1])));
        }

        Restrictions restrictions = Restrictions.or(restrictionsArray);
        Flux flux = Flux
                .from(DataConstants.INFLUX_DEVICE_DATA)
                .range(Instant.ofEpochMilli(start), Instant.ofEpochMilli(end))
                .filter(restrictions);
        return fluxClient.query(flux.toString());
    }


    /**
     * 获取指定时间段的第一条数据
     *
     * @param formulas 公式列表
     * @param start    开始时间
     * @param end      结束时间
     * @return fluxTable
     */
    @Override
    public List<FluxTable> findFirstData(List<String> formulas, long start, long end) {
        Restrictions[] restrictionsArray = new Restrictions[formulas.size()];
        for (int i = 0; i < formulas.size(); i++) {
            String[] array = formulas.get(i).split("\\.");
            restrictionsArray[i] = (Restrictions.and(Restrictions.measurement().equal(array[0]), Restrictions.tag(DataConstants.ATTRIBUTE_PROP).equal(array[1])));
        }
        Restrictions restrictions = Restrictions.or(restrictionsArray);
        Flux flux = Flux
                .from(DataConstants.INFLUX_DEVICE_DATA)
                .range(Instant.ofEpochMilli(start), Instant.ofEpochMilli(end))
                .filter(restrictions)
                .first();
        return InfluxFactory.createFluxClient().query(flux.toString());
    }


    /**
     * 按照时间顺序获取数据一段时间内的数据起点和终点
     *
     * @param formulas 设备公式列表
     * @param start    开始时间
     * @param end      结束时间
     * @return 查询到的数据
     */
    @Override
    public List<FluxTable> findData(List<String> formulas, long start, long end, String type) {
        if (type.equalsIgnoreCase(MONTH) || type.equalsIgnoreCase(YEAR) || type.equalsIgnoreCase(DAY)) {
            List<InfluxSearchTs> influxSearchTs = DateUtils.getInfluxSearchTs(start, end, type);
            Restrictions[] restrictionsArray = new Restrictions[formulas.size()];
            for (int i = 0; i < formulas.size(); i++) {
                String[] array = formulas.get(i).split("\\.");
                restrictionsArray[i] = (Restrictions.and(Restrictions.measurement().equal(array[0]), Restrictions.tag(DataConstants.ATTRIBUTE_PROP).equal(array[1])));
            }

            Map<String, FluxTable> fluxTableMap = new HashMap<>();
            formulas.forEach(f -> {
                fluxTableMap.put(f, new FluxTable());
            });

            Restrictions restrictions = Restrictions.or(restrictionsArray);
            List<FluxTable> fluxTables = new ArrayList<>();
            for (int i = 0; i < influxSearchTs.size(); i++) {
                InfluxSearchTs influxSearchTs1 = influxSearchTs.get(i);
                Flux flux = Flux
                        .from(DataConstants.INFLUX_DEVICE_DATA)
                        .range(Instant.ofEpochMilli(influxSearchTs1.getStart()), Instant.ofEpochMilli(influxSearchTs1.getEnd()))
                        .filter(restrictions)
                        .first();
                fluxTables.addAll(InfluxFactory.createFluxClient().query(flux.toString()));
                if (i == influxSearchTs.size() - 1) {
                    Flux flux1 = Flux
                            .from(DataConstants.INFLUX_DEVICE_DATA)
                            .range(Instant.ofEpochMilli(influxSearchTs1.getStart()), Instant.ofEpochMilli(influxSearchTs1.getEnd()))
                            .filter(restrictions)
                            .last();
                    fluxTables.addAll(InfluxFactory.createFluxClient().query(flux1.toString()));
                }
            }
            //simple synchronous query
            fluxTables.forEach(fluxTable -> {
                String formula = fluxTable.getRecords().get(0).getMeasurement() + "." + fluxTable.getRecords().get(0).getValueByKey(DataConstants.ATTRIBUTE_PROP);
                fluxTableMap.get(formula).getRecords().addAll(fluxTable.getRecords());
            });
            List<FluxTable> result = new ArrayList<>();
            fluxTableMap.forEach((key, value) -> {
                result.add(value);
            });
            return result;
        } else {
            return findDeviceDataFromInflux2(formulas, start, end);
        }
    }

    /**
     * 按照时间顺序和换表时间获取数据一段时间内的数据起点和终点
     *
     * @param formulas    设备公式列表
     * @param start       开始时间
     * @param end         结束时间
     * @param changeMeter 换表时间
     * @return 查询到的数据
     */
    public List<FluxTable> findDataByChangeMeter(List<String> formulas, long start, long end, String type, List<String> changeMeter) {
        if (type.equalsIgnoreCase(MONTH) || type.equalsIgnoreCase(YEAR) || type.equalsIgnoreCase(DAY)) {
            List<InfluxSearchTs> influxSearchTs = DateUtils.getInfluxSearchTs(start, end, type);
            if (changeMeter != null) {
                LinkedHashMap<Long, Long> changemeterMap = new LinkedHashMap<>();
                changeMeter.forEach(s -> {
                    changemeterMap.putAll(TimeDiff.changemeter(s));
                });

//                changemeterMap.forEach(entry->{
//
//                });
            }
            Restrictions[] restrictionsArray = new Restrictions[formulas.size()];
            for (int i = 0; i < formulas.size(); i++) {
                String[] array = formulas.get(i).split("\\.");
                restrictionsArray[i] = (Restrictions.and(Restrictions.measurement().equal(array[0]), Restrictions.tag(DataConstants.ATTRIBUTE_PROP).equal(array[1])));
            }

            Map<String, FluxTable> fluxTableMap = new HashMap<>();
            formulas.forEach(f -> {
                fluxTableMap.put(f, new FluxTable());
            });

            Restrictions restrictions = Restrictions.or(restrictionsArray);
            List<FluxTable> fluxTables = new ArrayList<>();
            for (int i = 0; i < influxSearchTs.size(); i++) {
                InfluxSearchTs influxSearchTs1 = influxSearchTs.get(i);
                Flux flux = Flux
                        .from(DataConstants.INFLUX_DEVICE_DATA)
                        .range(Instant.ofEpochMilli(influxSearchTs1.getStart()), Instant.ofEpochMilli(influxSearchTs1.getEnd()))
                        .filter(restrictions)
                        .first();
                fluxTables.addAll(InfluxFactory.createFluxClient().query(flux.toString()));
                if (i == influxSearchTs.size() - 1) {
                    Flux flux1 = Flux
                            .from(DataConstants.INFLUX_DEVICE_DATA)
                            .range(Instant.ofEpochMilli(influxSearchTs1.getStart()), Instant.ofEpochMilli(influxSearchTs1.getEnd()))
                            .filter(restrictions)
                            .last();
                    fluxTables.addAll(InfluxFactory.createFluxClient().query(flux1.toString()));
                }
            }
            //simple synchronous query
            fluxTables.forEach(fluxTable -> {
                String formula = fluxTable.getRecords().get(0).getMeasurement() + "." + fluxTable.getRecords().get(0).getValueByKey(DataConstants.ATTRIBUTE_PROP);
                fluxTableMap.get(formula).getRecords().addAll(fluxTable.getRecords());
            });
            List<FluxTable> result = new ArrayList<>();
            fluxTableMap.forEach((key, value) -> {
                result.add(value);
            });
            return result;
        } else {
            return findDeviceDataFromInflux2(formulas, start, end);
        }
    }



 /*   public static void main(String[] args) {
        FluxClient fluxClient = FluxClientFactory.create("http://47.105.80.231:8086?readTimeout=10000&connectTimeout=10000&logLevel=BASIC");
        List<String> formulas = new ArrayList<>();
        formulas.add("1ea8514fad6c240ac8e31170e0453db.ENERGY_IN");
        long start = Long.parseLong("1593532800000");
        long end = Long.parseLong("1596211200000");
        List<InfluxSearchTs> influxSearchTs = DateUtils.getInfluxSearchTs(start, end, DAY);
        Restrictions[] restrictionsArray = new Restrictions[formulas.size()];
        for (int i = 0; i < formulas.size(); i++) {
            String[] array = formulas.get(i).split("\\.");
            restrictionsArray[i] = (Restrictions.and(Restrictions.measurement().equal(array[0]), Restrictions.tag(DataConstants.ATTRIBUTE_PROP).equal(array[1])));
        }
        Map<String, FluxTable> fluxTableMap = new HashMap<>();
        formulas.forEach(f -> {
            fluxTableMap.put(f, new FluxTable());
        });

        Restrictions restrictions = Restrictions.or(restrictionsArray);
        List<FluxTable> fluxTables = new ArrayList<>();
        for (int i = 0; i < influxSearchTs.size(); i++) {
            InfluxSearchTs influxSearchTs1 = influxSearchTs.get(i);
            Flux flux = Flux
                    .from(DataConstants.INFLUX_DEVICE_DATA)
                    .range(Instant.ofEpochMilli(influxSearchTs1.getStart()), Instant.ofEpochMilli(influxSearchTs1.getEnd()))
                    .filter(restrictions)
                    .first();
            fluxTables.addAll(fluxClient.query(flux.toString()));
            if (i == influxSearchTs.size() - 1) {
                Flux flux1 = Flux
                        .from(DataConstants.INFLUX_DEVICE_DATA)
                        .range(Instant.ofEpochMilli(influxSearchTs1.getStart()), Instant.ofEpochMilli(influxSearchTs1.getEnd()))
                        .filter(restrictions)
                        .last();
                fluxTables.addAll(fluxClient.query(flux1.toString()));
            }
        }
        //simple synchronous query
        fluxTables.forEach(fluxTable -> {
            String formula = fluxTable.getRecords().get(0).getMeasurement() + "." + fluxTable.getRecords().get(0).getValueByKey(DataConstants.ATTRIBUTE_PROP);
            fluxTableMap.get(formula).getRecords().addAll(fluxTable.getRecords());
        });

        List<FluxTable> result = new ArrayList<>();
        fluxTableMap.forEach((key, value) -> {
            result.add(value);
        });


        for (FluxTable fluxTable : result) {
            List<FluxRecord> records = fluxTable.getRecords();
            for (FluxRecord fluxRecord : records) {
                System.out.println(fluxRecord.getTime().atOffset(ZoneOffset.of("+8")).format(DateTimeFormatter.ofPattern(DateUtils.DATE_FORMATE_DEFAULT)) + ": " + fluxRecord.getValueByKey("_value"));
            }
        }

    }

  */

}
