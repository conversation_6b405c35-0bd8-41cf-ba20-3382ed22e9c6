package org.thingsboard.server.dao.smartProduction.safeProduction;

import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.request.SafeProductionRequest;
import org.thingsboard.server.dao.model.sql.smartProduction.safeProduction.ProductionHiddenDander;
import org.thingsboard.server.dao.util.imodel.query.workOrder.PipeWorkOrderSaveRequest;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.util.List;

public interface ProductionHiddenDangerService {

    ProductionHiddenDander save(ProductionHiddenDander productionHiddenDander);

    PageData<ProductionHiddenDander> findList(SafeProductionRequest request);

    void delete(List<String> ids);

    IstarResponse toWorkOrder(PipeWorkOrderSaveRequest pipeWorkOrderSaveRequest, String userId);

}
