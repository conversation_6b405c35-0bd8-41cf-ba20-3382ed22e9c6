import{_ as h}from"./index-BlG8PIOK.js";import{c as v}from"./useStation-DJgnSZIA.js";import{d as w,r as D,o as C,g as c,n as d,dy as b,q as g,F as y,p as s,aB as z,aJ as L,bh as e,i as p,aw as M,l as k,bE as x,C as B}from"./index-r0dFAfgr.js";import"./zhandian-YaGuQZe6.js";const N={class:"item_view"},S={class:"td"},V={class:"td"},H={class:"td"},J={class:"td"},j={class:"td"},E=w({__name:"YLJC_jinzhou",props:{size:{}},setup(r){const l=r,n=D({data:[]}),i={step:.2,limitMoveNum:6},_=v(),m=()=>{_.getLatestData({type:"测流压站,流量监测站,压力监测站"}).then(o=>{n.data=(o==null?void 0:o.map(a=>(a.time=k(a.time,x).format("MM/DD HH:mm"),a)))||[]})};return C(()=>{m()}),(o,a)=>{const f=h;return c(),d("div",{class:M(["card",l.size])},[a[0]||(a[0]=b('<div class="table-header" data-v-662eaaf4><span class="th" data-v-662eaaf4>监测点名称</span><span class="th" data-v-662eaaf4>压力</span><span class="th" data-v-662eaaf4>累计流量</span><span class="th" data-v-662eaaf4>瞬时流量</span><span class="th" data-v-662eaaf4>读取时间</span></div>',1)),g(f,{data:p(n).data,"class-option":i,class:"warp"},{default:y(()=>[s("ul",N,[(c(!0),d(z,null,L(p(n).data,(t,u)=>(c(),d("li",{key:u,class:"table-body-row"},[s("span",S,e(t.name),1),s("span",V,e(t.pressure??"--")+" MPa",1),s("span",H,e(t.total_flow??"--")+" m³",1),s("span",J,e(t.Instantaneous_flow??"--")+" m³/h",1),s("span",j,e(t.time),1)]))),128))])]),_:1},8,["data"])],2)}}}),P=B(E,[["__scopeId","data-v-662eaaf4"]]);export{P as default};
