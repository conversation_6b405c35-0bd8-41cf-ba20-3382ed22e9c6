package org.thingsboard.server.dao.fileupload;

import io.minio.*;
import io.minio.errors.MinioException;
import io.minio.http.Method;
import io.minio.messages.Item;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.thingsboard.server.dao.fileupload.config.MinioConfig;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.*;

/**
 * Minio 文件存储
 */
@Service
@Primary
@Slf4j
public class MinioSysFileServiceImpl implements ISysFileService {

    @Autowired
    private MinioConfig minioConfig;

    @Autowired
    private MinioClient minioClient;

    @Value("${minio.url}")
    private String endpoint;

    @Value("${minio.accessKey}")
    private String accessKey;

    @Value("${minio.secretKey}")
    private String secretKey;

    @Value("${minio.bucketName}")
    private String bucketName;





    /**
     * 使用 FileInputStream 上传
     *
     * @param fileName
     * @param fileInputStream
     * @param bucketName
     * @return
     */
    public String uploadFile(String fileName, FileInputStream fileInputStream, String bucketName) {
        checkBucketExists(bucketName);

        try {
            minioClient.putObject(
                    PutObjectArgs.builder()
                            .bucket(bucketName)
                            .object(fileName)
                            .stream(fileInputStream, fileInputStream.available(), -1)
                            .build()
            );

            return getFileUrl(fileName);
        } catch (MinioException | IOException | NoSuchAlgorithmException | InvalidKeyException e) {
            log.error("文件上传失败:{}", e.getMessage());
        }

        return null;
    }

    /**
     * 使用 FileInputStream 上传，使用默认 bucket
     *
     * @param fileName
     * @param fileInputStream
     * @return
     */
    public String uploadFile(String fileName, FileInputStream fileInputStream) {
        return uploadFile(fileName, fileInputStream, bucketName);
    }

    /**
     * 上传 MultiFile
     *
     * @param file
     * @return
     */
    @Override
    public String uploadFile(MultipartFile file) {
        try (InputStream inputStream = file.getInputStream();) {
            String fileName = UUID.randomUUID().toString();
            minioClient.putObject(
                    PutObjectArgs.builder()
                            .bucket(bucketName)
                            .object(fileName)
                            .stream(inputStream, inputStream.available(), -1)
                            .contentType(file.getContentType())
                            .build()
            );
            return getFileUrl(fileName);
        } catch (MinioException | IOException | NoSuchAlgorithmException | InvalidKeyException e) {
            log.error("文件上传失败:{}", e.getMessage());
        }

        return null;
    }


    /**
     * 上传本地文件
     *
     * @param file
     * @return
     */
    public String uploadFile(File file, String bucketName) {
        return uploadFile(file.getAbsolutePath(), file, bucketName);
    }

    /**
     * 上传本地文件，使用默认 bucket
     *
     * @param file
     * @return
     */
    public String uploadFile(File file) {
        return uploadFile(file, bucketName);
    }

    /**
     * 上传本地文件并指定路径
     *
     * @param file
     * @return
     */
    public String uploadFile(String fileName, File file, String bucketName) {
        checkBucketExists(bucketName);

        try (InputStream inputStream = new FileInputStream(file)) {
            minioClient.putObject(
                    PutObjectArgs.builder()
                            .bucket(bucketName)
                            .object(fileName)
                            .stream(inputStream, inputStream.available(), -1)
                            .contentType(Files.probeContentType(file.toPath()))
                            .build()
            );

            return getFileUrl(fileName, bucketName);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }



    /**
     * 查询桶中的所有对象
     *
     * @param bucketName 存储桶名称
     * @throws Exception
     */
    public List<Item> listObjects(String bucketName) throws Exception {
        Iterable<io.minio.Result<Item>> results = minioClient.listObjects(ListObjectsArgs.builder().bucket(bucketName).prefix("audio_data/example_voices2/").recursive(false).build());
        Iterator<Result<Item>> iterator = results.iterator();
        List<Item> items = new ArrayList<>();
        while (iterator.hasNext()) {
            Item item = iterator.next().get();
            log.info("name:{}", item.objectName());
            items.add(item);
        }
        return items;
    }

    /**
     * 获取文件的读取流
     *
     * @param fileName
     * @return
     */
    public InputStream getFileInputStream(String fileName, String bucketName) {
        try {
            return minioClient.getObject(
                    GetObjectArgs.builder()
                            .bucket(bucketName)
                            .object(fileName)
                            .build());
        } catch (Exception e) {
            log.error("读取文件流失败:{}", e.getMessage());
        }
        return null;
    }

    /**
     * 获取文件的读取流，使用默认 bucket
     *
     * @param fileName
     * @return
     */
    public InputStream getFileInputStream(String fileName) {
        return getFileInputStream(fileName, bucketName);
    }

    /**
     * 删除文件
     *
     * @param fileName
     */
    @Override
    public void deleteFile(String fileName) {
        try {
            if (exists(fileName, bucketName)) {
                minioClient.removeObject(
                        RemoveObjectArgs.builder().bucket(bucketName).object(fileName).build());
                log.info("文件{}删除成功", fileName);
            } else {
                log.warn("文件{}不存在", fileName);
            }
        } catch (Exception e) {
            log.error("文件{}删除失败:{}", fileName, e.getMessage());
            throw new RuntimeException(e);
        }
    }



    /**
     * 查询文件是否存在
     *
     * @param fileName
     * @return
     */
    public boolean exists(String fileName, String bucketName) {
        try {
            minioClient.statObject(StatObjectArgs.builder().bucket(bucketName).object(fileName).build());
            return true;
        } catch (Exception e) {
            log.info("文件{}不存在:{}", fileName, e.getMessage());
        }
        return false;
    }

    /**
     * 根据文件路径得到预览文件外链
     *
     * @param path       如： /works/example/xxx.png
     * @param bucketName 如： meta-human
     * @return
     */
    public String getPreviewUrl(String path, String bucketName) {
        try {
            return minioClient.getPresignedObjectUrl(
                    GetPresignedObjectUrlArgs.builder()
                            .method(Method.GET)
                            .bucket(bucketName)
                            .object(path)
                            .build());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 根据文件路径得到预览文件外链，使用默认 bucket
     *
     * @param path
     * @return
     */
    public String getPreviewUrl(String path) {
        return getPreviewUrl(path, bucketName);
    }

    /**
     * 根据文件路径得到文件的下载外链
     *
     * @param fileName
     * @return
     */
    public String getDownloadUrl(String fileName, String bucketName) {
        Map<String, String> reqParams = new HashMap<String, String>();
        reqParams.put("response-content-type", "application/octet-stream");

        try {
            return minioClient.getPresignedObjectUrl(
                    GetPresignedObjectUrlArgs.builder()
                            .method(Method.GET)
                            .bucket(bucketName)
                            .object(fileName)
                            .extraQueryParams(reqParams)
                            .build());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 根据文件路径得到文件的下载外链，使用默认 bucket
     *
     * @param fileName
     * @return
     */
    public String getDownloadUrl(String fileName) {
        return getDownloadUrl(fileName, bucketName);
    }

    /**
     * 获取直接访问的文件路径
     *
     * @param fileName
     * @return
     */
    public String getFileUrl(String fileName) {
        return minioConfig.getUrl() + "/" + minioConfig.getBucketName() + "/" + fileName;
    }

    /**
     * 获取直接访问的文件路径
     *
     * @param fileName
     * @return
     */
    public String getFileUrl(String fileName, String bucketName) {
        return endpoint + "/" + bucketName + "/" + fileName;
    }

    /**
     * 访问路径转换为文件名称
     *
     * @param path
     * @return
     */
    private String pathToFileName(String path, String bucketName) {
        return path.substring(path.indexOf(bucketName) + bucketName.length() + 1);
    }

    /**
     * 检查 bucket 是否存在，不存在则创建并自定义策略
     *
     * @param bucketName
     */
    private void checkBucketExists(String bucketName) {
        try {
            boolean found =
                    minioClient.bucketExists(BucketExistsArgs.builder().bucket(bucketName).build());

            if (!found) {
                log.info("存储桶不存在");
                minioClient.makeBucket(
                        MakeBucketArgs.builder()
                                .bucket(bucketName)
                                .region("zh_CN")
                                .build());

                String policyJson = "{\n" +
                        "    \"Version\": \"2012-10-17\",\n" +
                        "    \"Statement\": [\n" +
                        "        {\n" +
                        "            \"Effect\": \"Allow\",\n" +
                        "            \"Principal\": {\n" +
                        "                \"AWS\": [\n" +
                        "                    \"*\"\n" +
                        "                ]\n" +
                        "            },\n" +
                        "            \"Action\": [\n" +
                        "                \"s3:GetBucketLocation\"\n" +
                        "            ],\n" +
                        "            \"Resource\": [\n" +
                        "                \"arn:aws:s3:::" + bucketName + "\"\n" +
                        "            ]\n" +
                        "        },\n" +
                        "        {\n" +
                        "            \"Effect\": \"Allow\",\n" +
                        "            \"Principal\": {\n" +
                        "                \"AWS\": [\n" +
                        "                    \"*\"\n" +
                        "                ]\n" +
                        "            },\n" +
                        "            \"Action\": [\n" +
                        "                \"s3:GetObject\"\n" +
                        "            ],\n" +
                        "            \"Resource\": [\n" +
                        "                \"arn:aws:s3:::" + bucketName + "\"\n" +
                        "            ]\n" +
                        "        }\n" +
                        "    ]\n" +
                        "}";
                minioClient.setBucketPolicy(
                        SetBucketPolicyArgs.builder().bucket(bucketName).config(policyJson).build());
            }

        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

}
