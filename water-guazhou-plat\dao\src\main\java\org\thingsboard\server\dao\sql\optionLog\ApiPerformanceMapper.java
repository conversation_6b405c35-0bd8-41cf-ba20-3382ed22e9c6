package org.thingsboard.server.dao.sql.optionLog;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.base.ApiPerformance;
import org.thingsboard.server.dao.util.imodel.query.base.ApiPerformancePageRequest;

/**
 * 公共平台-服务监控Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-24
 */
@Mapper
public interface ApiPerformanceMapper {
    /**
     * 查询公共平台-服务监控
     *
     * @param id 公共平台-服务监控主键
     * @return 公共平台-服务监控
     */
    public ApiPerformance selectApiPerformanceById(String id);

    /**
     * 查询公共平台-服务监控列表
     *
     * @param apiPerformance 公共平台-服务监控
     * @return 公共平台-服务监控集合
     */
    public IPage<ApiPerformance> selectApiPerformanceList(ApiPerformancePageRequest apiPerformance);

    /**
     * 新增公共平台-服务监控
     *
     * @param apiPerformance 公共平台-服务监控
     * @return 结果
     */
    public int insertApiPerformance(ApiPerformance apiPerformance);

    /**
     * 修改公共平台-服务监控
     *
     * @param apiPerformance 公共平台-服务监控
     * @return 结果
     */
    public int updateApiPerformance(ApiPerformance apiPerformance);

    /**
     * 删除公共平台-服务监控
     *
     * @param id 公共平台-服务监控主键
     * @return 结果
     */
    public int deleteApiPerformanceById(String id);

    /**
     * 批量删除公共平台-服务监控
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteApiPerformanceByIds(@Param("array") List<String> ids);
}
