package org.thingsboard.server.dao.sql.workOrder;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.thingsboard.server.dao.model.sql.WorkOrderEntity;

import java.util.Date;
import java.util.List;

@Repository
@Deprecated
public interface WorkOrderRepository extends JpaRepository<WorkOrderEntity, String> {

    @Query("SELECT wo FROM WorkOrderEntity wo " +
            "WHERE wo.tenantId = ?1 AND wo.code LIKE %?2% AND wo.status IN ?3")
    Page<WorkOrderEntity> findList(String tenantId, String code, List<String> status, Pageable pageable);

    List<WorkOrderEntity> findByTenantIdAndType(String tenantId, String type);

    List<WorkOrderEntity> findByTenantIdOrderByCreateTimeDesc(String tenantId);

    List<WorkOrderEntity> findByTenantIdAndCreateTimeBetweenOrderByCreateTime(String tenantId, Date startTime, Date endTime);

}
