 <!--水厂管理-水厂报表 -->
<template>
  <div class="wrapper">
    <CardSearch
      ref="refSearch"
      :config="cardSearchConfig"
    />
    <SLCard
      class="card"
      :title="state.activeName==='list'?'水厂列表':'水厂曲线'"
    >
      <template #query>
        <el-radio-group
          v-model="state.activeName"
        >
          <el-radio-button label="echarts">
            <Icon
              style="margin-right: 1px;font-size: 16px"
              icon="clarity:line-chart-line"
            />
          </el-radio-button>
          <el-radio-button label="list">
            <Icon
              style="margin-right: 1px;font-size: 16px"
              icon="material-symbols:table"
            />
          </el-radio-button>
        </el-radio-group>
      </template>
      <div
        v-show="state.activeName === 'echarts'"
        ref="agriEcoDev"
        class="chart-box"
      >
        <!-- 图表模式 -->
        <VChart
          ref="refChart"
          :theme="useAppStore().isDark?'dark':'light'"
          :option="state.chartOption"
        ></VChart>
      </div>
      <!-- 列表模式 -->
      <div v-show="state.activeName === 'list'">
        <div>
        </div>
        <FormTable
          ref="refCardTable"
          class="card-table"
          :config="cardTableConfig"
        />
      </div>
    </SLCard>
  </div>
</template>
<script lang="ts" setup>
import dayjs from 'dayjs'
import elementResizeDetectorMaker from 'element-resize-detector'
import { Download, Refresh } from '@element-plus/icons-vue'
import { Icon } from '@iconify/vue'
import { lineOption } from '../echartsData/echart'
import { getWaterPlantFlowReport } from '@/api/waterFactoryManage/factoryReport'
import { IECharts } from '@/plugins/echart'
import useStation from '@/hooks/station/useStation'
import { formatColumn } from '@/utils/formartColumn'
import useGlobal from '@/hooks/global/useGlobal'
import { useAppStore } from '@/store'

const { $messageWarning } = useGlobal()
const { getStationTree, getStationTreeByDisabledType } = useStation()
const erd = elementResizeDetectorMaker()
const state = reactive<{
  queryType: 'day' | 'month' | 'year',
  chartOption: any,
  activeName: string,
  dataList: any,
  stationTree: any
  chartName:string
}>({
  queryType: 'day',
  chartOption: null,
  activeName: 'echarts',
  dataList: {},
  stationTree: [],
  chartName: ''
})
//
// const loading = ref<boolean>(false)
const refSearch = ref<ICardSearchIns>()
const refChart = ref<IECharts>()
const agriEcoDev = ref<any>()
const refCardTable = ref<ICardTableIns>()

watch(() => state.activeName, () => {
  if (state.activeName === 'echarts') {
    refuseChart()
  }
})

// 水厂站点树
const TreeData = reactive<SLTreeConfig>({
  data: [],
  loading: false,
  title: '区域划分',
  expandOnClickNode: false,
  showCheckbox: true,
  checkedKeys: [],
  handleCheck: (
    ids: string[],
    data: {
      checkedKeys?: string[] | undefined
      checkedNodes?: Omit<NormalOption, 'children'>[] | undefined
    }
  ) => {
    console.log(data.checkedNodes, data.checkedKeys)
    TreeData.checkedKeys = data.checkedKeys || []
    TreeData.checkedNodes = data.checkedNodes || []
    refreshData()
  }
})

// 获取左边树
// const TreeData = reactive<SLTreeConfig>({
//   data: store.business.projectList,
//   title: '水厂列表',
//   currentProject: store.business.selectedProject,
//   checkedKeys: [],
//   checkedNodes: [],
//   isFilterTree: true,
//   showCheckbox: true,
//   treeNodeHandleClick: data => {
//     TreeData.currentProject = data
//     store.business.SET_selectedProject(data)
//     // initStation()
//   },
//   handleCheck: (
//     ids: string[],
//     data: {
//       checkedKeys?: string[] | undefined
//       checkedNodes?: Omit<NormalOption, 'children'>[] | undefined
//     }
//   ) => {
//     console.log(data.checkedNodes, data.checkedKeys)

//     TreeData.checkedKeys = data.checkedKeys || []
//     TreeData.checkedNodes = data.checkedNodes || []
//     refreshData()
//   }
// })

// 搜索栏初始化配置
const cardSearchConfig = reactive<ISearch>({
  defaultParams: {
    queryType: 'day',
    time: dayjs().format('YYYY-MM-DD')
  },
  filters: [
    {
      type: 'select-tree',
      label: '监测点:',
      defaultExpandAll: true,
      field: 'stationId',
      clearable: false,
      multiple: true,
      showCheckbox: true,
      width: '200px',
      options: computed(() => state.stationTree) as any,
      nodeClick: data => {
        state.chartName = data.label
      }
    },
    {
      type: 'radio-button',
      field: 'queryType',
      options: [
        { label: '日报', value: 'day' },
        { label: '月报', value: 'month' },
        { label: '年报', value: 'year' }
      ],
      label: '报告类型',
      onChange: (val: any) => dateTypeChange(val)
    },
    { type: 'date', label: '日期', field: 'time', clearable: false, format: 'YYYY-MM-DD' }

  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          icon: 'iconfont icon-chaxun',
          click: () => {
            const queryParams = refSearch.value?.queryParams as any || {}
            if (queryParams.stationId && queryParams.stationId.length > 0) {
              refreshData()
            } else $messageWarning('选择监测点')
          }
        },
        {
          type: 'default',
          perm: true,
          text: '重置',
          svgIcon: shallowRef(Refresh),
          click: () => {
            refSearch.value?.resetForm()
          }
        },
        {
          perm: true,
          hide: () => { return state.activeName !== 'list' },
          type: 'warning',
          text: '导出',
          svgIcon: shallowRef(Download),
          click: () => {
            if (cardTableConfig.dataList.length > 0) {
              refCardTable.value?.exportTable()
            } else {
              $messageWarning('无数据导出')
            }
          }
        }
      ]
    }
  ]
})

// 初始化列表配置数据
const cardTableConfig = reactive<ITable>({
  loading: false,
  dataList: [],
  columns: [],
  operations: [],
  pagination: {
    hide: true
  }
})

// 刷新列表 模拟数据
const refreshData = async () => {
  cardTableConfig.loading = true
  const queryParams = refSearch.value?.queryParams as any || {}
  const params: any = {
    stationIds: queryParams.stationId?.join(','),
    queryType: queryParams.queryType,
    time: dayjs(queryParams.time).format(state.queryType === 'month' ? 'YYYY-MM' : state.queryType === 'year' ? 'YYYY' : 'YYYY-MM-DD')
  }
  const waterPlantFlowReport = await getWaterPlantFlowReport(params)
  console.log('数据2', waterPlantFlowReport.data.data)
  const data = waterPlantFlowReport.data.data
  state.dataList = data
  if (data) {
    const columns = formatColumn(data?.tableInfo)
    cardTableConfig.columns = columns
    cardTableConfig.dataList = data?.tableDataList
  }
  cardTableConfig.loading = false
  refuseChart()
}

const resizeChart = () => {
  refChart.value?.resize()
}

const refuseChart = () => {
  refChart.value?.clear()
  nextTick(() => {
    const chartOption = lineOption()
    const tableDataList = state.dataList?.tableDataList
    const newData = tableDataList?.slice(0, tableDataList.length - 6)
    chartOption.xAxis.data = newData?.map(item => item.ts)
    chartOption.series = []
    const legends = state.dataList?.tableInfo.filter(item => !['数据时间', '合计'].includes(item.columnName))
    console.log(tableDataList)
    const series = legends.map(legend => {
      return {
        name: legend.columnName,
        smooth: true,
        data: tableDataList.map(item => item[legend.columnValue]),
        type: 'line',
        markPoint: {
          data: [
            {
              type: 'max',
              name: '最大值',
              label: {
                fontSize: 12,
                color: useAppStore().isDark ? '#ffffff' : '#000000'
              } },
            {
              type: 'min',
              name: '最小值',
              label: {
                color: useAppStore().isDark ? '#ffffff' : '#000000'
              } }
          ]
        },
        markLine: {
          data: [{ type: 'average', name: '平均值' }]
        }
      }
    })
    chartOption.series = series
    if (agriEcoDev.value) {
      erd.listenTo(agriEcoDev.value, () => {
        state.chartOption = chartOption
        resizeChart()
      })
    }
  })
}

const dateTypeChange = (val: any) => {
  const filter = cardSearchConfig.filters?.find(filter => filter.field === 'time') as IFormDate
  filter.type = val === 'day' ? 'date' : val
}

// onMounted(async () => {
//   // refreshData()
//   const treeData = await getStationTree('水厂') as any[]
//   // getStationTreeByDisabledType(treeData, ['Project'])
//   await getStationTreeByDisabledType(treeData, [], false, 'Station')
//   TreeData.data = treeData as any
// })
onBeforeMount(async () => {
  const treeData = await getStationTree('水厂') as any[]
  await getStationTreeByDisabledType(treeData, [], false, 'Station')
  state.stationTree = treeData
  // cardSearchConfig.defaultParams = {
  //   ...cardSearchConfig.defaultParams,
  //   stationId: [treeData[0].id]
  // }
  refSearch.value?.resetForm()
})
</script>

<style lang="scss" scoped>
.card {
  height: calc(100% - 80px);
}

.card-table {
  height: calc(100vh - 254px);
  width: 100%;
}

.chart-box {
  width: 100%;
  height: calc(100vh - 254px);
}
</style>
