import{g as q,h as E}from"./FeatureHelper-Da16o0mu.js";import"./AnimatedLinesLayer-B2VbV4jv.js";import"./pe-B8dP0-Ut.js";import{d as J,cN as j,c as R,r as V,o as K,g as n,n as m,q as y,F as w,p as f,i as l,bh as Q,aB as h,aJ as c,h as b,cn as k,an as _,b6 as X,bB as z,dc as Y,C as Z}from"./index-r0dFAfgr.js";import"./MapView-DaoQedLH.js";import"./commonProperties-DqNQ4F00.js";import"./IdentifyResult-4DxLVhTm.js";import"./Point-WxyopZva.js";import{g as N}from"./LayerHelper-Cn-iiqxI.js";import"./project-DUuzYgGl.js";import{u as I}from"./useHighLight-DPevRAc5.js";import"./GraphicsLayer-DTrBRwJQ.js";import"./TileLayer-B5vQ99gG.js";/* empty css                                                                      */import{a as tt}from"./URLHelper-B9aplt5w.js";import"./index-0NlGN6gS.js";import"./geometryEngineBase-BhsKaODW.js";import et from"./RightDrawerMap-D5PhmGFO.js";import rt from"./VerticalBar-D5SkKIju.js";import it from"./HorizontalBar-DzSaExsy.js";import{e as ot}from"./config-DncLSA-r.js";import"./geometryEngine-OGzB5MRq.js";import"./hydrated-DLkO5ZPr.js";import"./widget-BcWKanF2.js";import"./dehydratedFeatures-CEuswj7y.js";import"./enums-B5k73o5q.js";import"./plane-BhzlJB-C.js";import"./sphere-NgXH-gLx.js";import"./mat3f64-BVJGbF0t.js";import"./mat4f64-BCm7QTSd.js";import"./quatf64-QCogZAoR.js";import"./elevationInfoUtils-5B4aSzEU.js";import"./quat-CM9ioDFt.js";import"./scaleUtils-DgkF6NQH.js";import"./ExportImageParameters-BiedgHNY.js";import"./floorFilterUtils-DZ5C6FQv.js";import"./sublayerUtils-bmirCD0I.js";import"./imageBitmapUtils-Db1drMDc.js";import"./WMSLayer-mTaW758E.js";import"./crsUtils-DAndLU68.js";import"./ExportWMSImageParameters-CGwvCiFd.js";import"./BaseTileLayer-DM38cky_.js";import"./QueryEngineResult-D2Huf9Bb.js";import"./quantizationUtils-DtI9CsYu.js";import"./WhereClause-CNjGNHY9.js";import"./executionError-BOo4jP8A.js";import"./utils-DcsZ6Otn.js";import"./generateRendererUtils-Bt0vqUD2.js";import"./projectionSupport-BDUl30tr.js";import"./json-Wa8cmqdu.js";import"./utils-dKbgHYZY.js";import"./LayerView-BSt9B8Gh.js";import"./Container-BwXq1a-x.js";import"./definitions-826PWLuy.js";import"./enums-BDQrMlcz.js";import"./Texture-BYqObwfn.js";import"./Util-sSNWzwlq.js";import"./pixelRangeUtils-Dr0gmLDH.js";import"./number-Q7BpbuNy.js";import"./coordinateFormatter-C2XOyrWt.js";import"./earcut-BJup91r2.js";import"./normalizeUtilsSync-NMksarRY.js";import"./TurboLine-CDscS66C.js";import"./enums-L38xj_2E.js";import"./util-DPgA-H2V.js";import"./RefreshableLayerView-DUeNHzrW.js";import"./vec2-Fy2J07i2.js";import"./pipe-nogVzCHG.js";import"./ToolHelper-BiiInOzB.js";import"./ArcGISCachedService-CQM8IwuM.js";import"./TilemapCache-BPMaYmR0.js";import"./Version-Q4YOKegY.js";import"./QueryTask-B4og_2RG.js";import"./executeForIds-BLdIsxvI.js";import"./ArcView-DpMnCY82.js";import"./ViewHelper-BGCZjxXH.js";import"./fieldconfig-Bk3o1wi7.js";import"./Panel-DyoxrWMd.js";import"./v4-SoommWqA.js";import"./ArcStationWarning-BF9YrSzF.js";/* empty css                         */import"./useWaterPoint-Bv0z6ym6.js";import"./DateFormatter-Bm9a68Ax.js";import"./zhandian-YaGuQZe6.js";import"./useStation-DJgnSZIA.js";import"./DrawerBox-CLde5xC8.js";import"./SideDrawer-CBntChyn.js";import"./PipeDetail-CTBPYFJW.js";import"./Search-NSrhrIa_.js";import"./FormTableColumnFilter-BT7pLXIC.js";import"./QueryHelper-ILO3qZqg.js";import"./config-fy91bijz.js";import"./ArcBRTools-BO92yznB.js";import"./PipeLength.vue_vue_type_script_setup_true_lang-BZfUsvDf.js";import"./arcWidgetButton-0glIxrt7.js";import"./StatisticsHelper-D-s_6AyQ.js";import"./useWidgets-BRE-VQU9.js";import"./useBasemapGallary-Bk4zNUJL.js";import"./wfsUtils-DXofo3da.js";import"./usePipelineGroup-Ba1pmWz_.js";import"./GroupLayer-DhhN3FyN.js";import"./lazyLayerLoader-DbM9sT1W.js";import"./WFSLayer-1TtJp3nx.js";import"./clientSideDefaults-VQhQaYxh.js";import"./QueryEngineCapabilities-Dk3NJwmm.js";import"./wfsUtils-Du031XaC.js";import"./geojson-MBFu2-HZ.js";import"./xmlUtils-CtUoQO7q.js";import"./ListWindow-WS05QqV0.js";import"./PopLayout-BP55MvL7.js";import"./PoiSearchV2-D7yNeLuv.js";/* empty css                        */import"./utils-D5nxoMq3.js";import"./useLayerList-DmEwJ-ws.js";import"./useScaleBar-Beed-z91.js";/* empty css                         */import"./index-DeAQQ1ej.js";const at={class:"custom-menubar"},pt={class:"custom-submenubar"},st={class:"one-map-bg"},nt={class:"one-map-page"},lt={class:"one-map-detail"},mt=J({__name:"OneMap copy",setup(ht){const{proxy:M}=j(),d=R(),D=R(),v=I(),i=V({title:"",curPath:"",detailTitle:"",pageConfig:[],menus:[],pops:[],detailType:"",rightWidth:460}),p={layers:[]},g=V({width:460,group:[],title:"",escapeClose:!1,modal:!1,showClose:!1,withHeader:!1,modalClass:"one-map-drawer",cancel:!1}),A=t=>{var o,r,e,s,a;((o=i.hMenu)==null?void 0:o.path)!==t.path&&t.path!==((r=i.hMenu)==null?void 0:r.path)&&((e=p.view)==null||e.map.removeMany(p.layers),v.destroy(),p.layers.length=0,i.pops.length=0,(s=d.value)==null||s.toggleCustomDetail(!1),i.hMenu=t,(a=t.children)!=null&&a.length&&C(t.children[0],!0))},C=(t,o)=>{i.curPath=t.path,i.title=t.meta.title;let r=p.layers.find(e=>e.id===t.path);r||(r=N(p.view,{id:t.path,title:t.meta.title}),r&&p.layers.push(r),W()),r&&(r.visible=o),g.width=460},T=async t=>{var r,e,s;p.highlightGraphic=t;const o=(t==null?void 0:t.attributes.id)||"";i.detailType=((r=t==null?void 0:t.attributes)==null?void 0:r.path)||"",await z(),i.detailType&&((e=d.value)==null||e.toggleCustomDetail(!0),(s=d.value)==null||s.openPop(o),S())},G=async(t,o)=>{var s,a;g.width=1460,((s=d.value)==null?void 0:s.isCustomOpened())||((a=d.value)==null||a.toggleCustomDetail(!0),await Y(600));const e=M.$refs["refDetail"+i.detailType];e==null||e[0].refreshDetail(p.view,t,{where:o})},S=async()=>{var o,r;g.width=1460,await z();const t=M.$refs["refDetail"+i.detailType];t!=null&&t.length&&t[0].refreshDetail((r=(o=p.highlightGraphic)==null?void 0:o.attributes)==null?void 0:r.row)},W=()=>{v.bindHoverHighLight(p.view,p.layers,T)},$=async(t,o,r)=>{if(t.path.startsWith("sbzc"))i.detailTitle=(t.meta.title||"")+(r?"("+r+")":""),i.detailType=t.path||"",G(t.meta.title,o);else{const e=p.layers.find(a=>a.id===t.path),s=e==null?void 0:e.graphics.find(a=>{var u;return((u=a.attributes)==null?void 0:u.id)===o});if(!e||!s||!p.view)return;await q(p.view,s,{avoidHighlight:!0}),v.highlight(p.view,s,T)}},F=t=>{const o=i.curPath;if(!o)return;i.pops=i.pops.filter(a=>{var u;return((u=a.attributes.customConfig)==null?void 0:u.path)!==o});const r=(t==null?void 0:t.windows)||[];i.pops.push(...r);const e=N(p.view,{id:o});if(!e)return;e.removeAll();const s=r.filter(a=>a.symbolConfig).map(a=>{var B,H,x,L,P,O;return E(a.x,a.y,{picUrl:((B=a.symbolConfig)==null?void 0:B.url)||tt(),spatialReference:(H=p.view)==null?void 0:H.spatialReference,attributes:a.attributes,picSize:[((x=a.symbolConfig)==null?void 0:x.width)||20,((L=a.symbolConfig)==null?void 0:L.height)||25],xOffset:((P=a.symbolConfig)==null?void 0:P.xoffset)||0,yOffset:((O=a.symbolConfig)==null?void 0:O.yoffset)||13})});e.addMany(s)},U=t=>{var r;p.view=t,i.menus=((r=ot())==null?void 0:r.children)||[],i.hMenu=i.menus[0];const o=i.hMenu.children||[];C(o[0],!0)};return K(()=>{var t;(t=D.value)==null||t.openDrawer()}),(t,o)=>(n(),m(h,null,[y(et,{ref_key:"refMap",ref:d,title:l(i).title,"hide-coords":!0,"hide-right-drawer":!0,"right-drawer-width":l(i).rightWidth,"right-drawer-absolute":!0,pops:l(i).pops,onMapLoaded:U,onPopToggle:o[1]||(o[1]=(r,e)=>r.visible=e)},{"map-bars":w(()=>[f("div",at,[y(rt,{"collapse-on-click":!0,menus:l(i).menus,onChange:A},null,8,["menus"])]),f("div",pt,[y(it,{menu:l(i).hMenu,onClick:C},null,8,["menu"])])]),"detail-header":w(()=>[f("span",null,Q(l(i).detailTitle),1)]),"detail-default":w(()=>[(n(!0),m(h,null,c(l(i).menus,r=>(n(),m(h,{key:r.path},[(n(!0),m(h,null,c(r.children,e=>(n(),m(h,{key:e.path},[l(i).detailType===e.path&&e.detailComponent?(n(),b(k(e.detailComponent),{key:0,ref_for:!0,ref:"refDetail"+e.path,onRefresh:o[0]||(o[0]=s=>l(i).detailTitle=s.title)},null,544)):_("",!0)],64))),128))],64))),128))]),_:1},8,["title","right-drawer-width","pops"]),y(X,{ref_key:"refDrawer",ref:D,config:l(g)},{default:w(()=>[f("div",st,[f("div",nt,[(n(!0),m(h,null,c(l(i).menus,r=>(n(),m(h,{key:r.path},[(n(!0),m(h,null,c(r.children,e=>(n(),m(h,{key:e.path},[e.path===l(i).curPath&&e.component?(n(),b(k(e.component),{key:0,menu:e,view:p.view,onHighlightMark:$,onAddMarks:F},null,40,["menu","view"])):_("",!0)],64))),128))],64))),128))]),f("div",lt,[(n(!0),m(h,null,c(l(i).menus,r=>(n(),m(h,{key:r.path},[(n(!0),m(h,null,c(r.children,e=>(n(),m(h,{key:e.path},[l(i).detailType===e.path&&e.detailComponent?(n(),b(k(e.detailComponent),{key:0,ref_for:!0,ref:"refDetail"+e.path,onRefresh:o[2]||(o[2]=s=>l(i).detailTitle=s.title)},null,544)):_("",!0)],64))),128))],64))),128))])])]),_:1},8,["config"])],64))}}),br=Z(mt,[["__scopeId","data-v-a4238b5f"]]);export{br as default};
